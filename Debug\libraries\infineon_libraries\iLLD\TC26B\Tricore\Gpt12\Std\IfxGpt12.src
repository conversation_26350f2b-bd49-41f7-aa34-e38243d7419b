	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc5412a --dep-file=IfxGpt12.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/infineon_libraries/iLLD/TC26B/Tricore/Gpt12/Std/IfxGpt12.src ../libraries/infineon_libraries/iLLD/TC26B/Tricore/Gpt12/Std/IfxGpt12.c"
	.compiler_name		"ctc"
	;source	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Gpt12/Std/IfxGpt12.c'

	
$TC16X
	
	.sdecl	'.text.IfxGpt12.IfxGpt12_T2_getFrequency',code,cluster('IfxGpt12_T2_getFrequency')
	.sect	'.text.IfxGpt12.IfxGpt12_T2_getFrequency'
	.align	2
	
	.global	IfxGpt12_T2_getFrequency
; Function IfxGpt12_T2_getFrequency
.L129:
IfxGpt12_T2_getFrequency:	.type	func
	mov.aa	a15,a4
.L339:
	jz.a	a15,.L2
.L2:
	call	IfxScuCcu_getSpbFrequency
.L442:
	j	.L3
.L3:
	ld.bu	d15,[a15]21
	extr.u	d15,d15,#3,#2
.L443:
	mov	d0,#0
	jeq	d15,d0,.L4
.L617:
	mov	d0,#1
	jeq	d15,d0,.L5
.L618:
	mov	d0,#3
	jeq	d15,d0,.L6
	j	.L7
.L5:
	movh	d15,#16512
.L444:
	div.f	d0,d2,d15
.L445:
	j	.L8
.L4:
	movh	d15,#16640
.L446:
	div.f	d0,d2,d15
.L447:
	j	.L9
.L6:
	movh	d15,#16768
.L448:
	div.f	d0,d2,d15
.L449:
	j	.L10
.L7:
	movh	d15,#16896
.L450:
	div.f	d0,d2,d15
.L451:
	j	.L11
.L11:
.L10:
.L9:
.L8:
	ld.bu	d15,[a15]16
	extr.u	d15,d15,#3,#3
.L619:
	j	.L12
.L12:
	ld.bu	d1,[a15]16
	and	d1,d1,#7
.L453:
	jeq	d15,#0,.L13
.L620:
	jeq	d15,#3,.L14
.L621:
	jne	d15,#2,.L15
.L14:
.L13:
	mov	d15,#1
.L622:
	sha	d15,d15,d1
	itof	d15,d15
.L623:
	div.f	d2,d0,d15
.L452:
	j	.L16
.L15:
	movh	d15,#16384
.L624:
	div.f	d2,d0,d15
.L16:
	j	.L17
.L17:
	ret
.L329:
	
__IfxGpt12_T2_getFrequency_function_end:
	.size	IfxGpt12_T2_getFrequency,__IfxGpt12_T2_getFrequency_function_end-IfxGpt12_T2_getFrequency
.L180:
	; End of function
	
	.sdecl	'.text.IfxGpt12.IfxGpt12_T3_getFrequency',code,cluster('IfxGpt12_T3_getFrequency')
	.sect	'.text.IfxGpt12.IfxGpt12_T3_getFrequency'
	.align	2
	
	.global	IfxGpt12_T3_getFrequency
; Function IfxGpt12_T3_getFrequency
.L131:
IfxGpt12_T3_getFrequency:	.type	func
	mov.aa	a15,a4
.L353:
	jz.a	a15,.L18
.L18:
	call	IfxScuCcu_getSpbFrequency
.L454:
	j	.L19
.L19:
	ld.bu	d15,[a15]21
	extr.u	d15,d15,#3,#2
.L455:
	mov	d0,#0
	jeq	d15,d0,.L20
.L629:
	mov	d0,#1
	jeq	d15,d0,.L21
.L630:
	mov	d0,#3
	jeq	d15,d0,.L22
	j	.L23
.L21:
	movh	d15,#16512
.L456:
	div.f	d0,d2,d15
.L457:
	j	.L24
.L20:
	movh	d15,#16640
.L458:
	div.f	d0,d2,d15
.L459:
	j	.L25
.L22:
	movh	d15,#16768
.L460:
	div.f	d0,d2,d15
.L461:
	j	.L26
.L23:
	movh	d15,#16896
.L462:
	div.f	d0,d2,d15
.L463:
	j	.L27
.L27:
.L26:
.L25:
.L24:
	ld.bu	d15,[a15]20
	extr.u	d15,d15,#3,#3
.L465:
	ld.bu	d1,[a15]20
	and	d1,d1,#7
.L467:
	jeq	d15,#0,.L28
.L631:
	jeq	d15,#3,.L29
.L632:
	jne	d15,#2,.L30
.L29:
.L28:
	mov	d15,#1
.L466:
	sha	d15,d15,d1
	itof	d15,d15
.L633:
	div.f	d2,d0,d15
.L464:
	j	.L31
.L30:
	movh	d15,#16384
.L468:
	div.f	d2,d0,d15
.L31:
	j	.L32
.L32:
	ret
.L347:
	
__IfxGpt12_T3_getFrequency_function_end:
	.size	IfxGpt12_T3_getFrequency,__IfxGpt12_T3_getFrequency_function_end-IfxGpt12_T3_getFrequency
.L185:
	; End of function
	
	.sdecl	'.text.IfxGpt12.IfxGpt12_T4_getFrequency',code,cluster('IfxGpt12_T4_getFrequency')
	.sect	'.text.IfxGpt12.IfxGpt12_T4_getFrequency'
	.align	2
	
	.global	IfxGpt12_T4_getFrequency
; Function IfxGpt12_T4_getFrequency
.L133:
IfxGpt12_T4_getFrequency:	.type	func
	mov.aa	a15,a4
.L361:
	jz.a	a15,.L33
.L33:
	call	IfxScuCcu_getSpbFrequency
.L469:
	j	.L34
.L34:
	ld.bu	d15,[a15]21
	extr.u	d15,d15,#3,#2
.L470:
	mov	d0,#0
	jeq	d15,d0,.L35
.L638:
	mov	d0,#1
	jeq	d15,d0,.L36
.L639:
	mov	d0,#3
	jeq	d15,d0,.L37
	j	.L38
.L36:
	movh	d15,#16512
.L471:
	div.f	d0,d2,d15
.L472:
	j	.L39
.L35:
	movh	d15,#16640
.L473:
	div.f	d0,d2,d15
.L474:
	j	.L40
.L37:
	movh	d15,#16768
.L475:
	div.f	d0,d2,d15
.L476:
	j	.L41
.L38:
	movh	d15,#16896
.L477:
	div.f	d0,d2,d15
.L478:
	j	.L42
.L42:
.L41:
.L40:
.L39:
	ld.bu	d15,[a15]24
	extr.u	d15,d15,#3,#3
.L480:
	ld.bu	d1,[a15]24
	and	d1,d1,#7
.L482:
	jeq	d15,#0,.L43
.L640:
	jeq	d15,#3,.L44
.L641:
	jne	d15,#2,.L45
.L44:
.L43:
	mov	d15,#1
.L481:
	sha	d15,d15,d1
	itof	d15,d15
.L642:
	div.f	d2,d0,d15
.L479:
	j	.L46
.L45:
	movh	d15,#16384
.L483:
	div.f	d2,d0,d15
.L46:
	j	.L47
.L47:
	ret
.L355:
	
__IfxGpt12_T4_getFrequency_function_end:
	.size	IfxGpt12_T4_getFrequency,__IfxGpt12_T4_getFrequency_function_end-IfxGpt12_T4_getFrequency
.L190:
	; End of function
	
	.sdecl	'.text.IfxGpt12.IfxGpt12_T5_getFrequency',code,cluster('IfxGpt12_T5_getFrequency')
	.sect	'.text.IfxGpt12.IfxGpt12_T5_getFrequency'
	.align	2
	
	.global	IfxGpt12_T5_getFrequency
; Function IfxGpt12_T5_getFrequency
.L135:
IfxGpt12_T5_getFrequency:	.type	func
	mov.aa	a15,a4
.L370:
	jz.a	a15,.L48
.L48:
	call	IfxScuCcu_getSpbFrequency
.L484:
	j	.L49
.L49:
	ld.bu	d15,[a15]33
	extr.u	d15,d15,#3,#2
.L485:
	mov	d0,#0
	jeq	d15,d0,.L50
.L647:
	mov	d0,#1
	jeq	d15,d0,.L51
.L648:
	mov	d0,#3
	jeq	d15,d0,.L52
	j	.L53
.L51:
	movh	d15,#16384
.L486:
	div.f	d0,d2,d15
.L487:
	j	.L54
.L50:
	movh	d15,#16512
.L488:
	div.f	d0,d2,d15
.L489:
	j	.L55
.L52:
	movh	d15,#16640
.L490:
	div.f	d0,d2,d15
.L491:
	j	.L56
.L53:
	movh	d15,#16768
.L492:
	div.f	d0,d2,d15
.L493:
	j	.L57
.L57:
.L56:
.L55:
.L54:
	ld.bu	d15,[a15]28
	extr.u	d15,d15,#3,#3
.L495:
	ld.bu	d1,[a15]28
	and	d1,d1,#7
.L497:
	jeq	d15,#0,.L58
.L649:
	jeq	d15,#3,.L59
.L650:
	jne	d15,#2,.L60
.L59:
.L58:
	mov	d15,#1
.L496:
	sha	d15,d15,d1
	itof	d15,d15
.L651:
	div.f	d2,d0,d15
.L494:
	j	.L61
.L60:
	movh	d15,#16384
.L498:
	div.f	d2,d0,d15
.L61:
	j	.L62
.L62:
	ret
.L363:
	
__IfxGpt12_T5_getFrequency_function_end:
	.size	IfxGpt12_T5_getFrequency,__IfxGpt12_T5_getFrequency_function_end-IfxGpt12_T5_getFrequency
.L195:
	; End of function
	
	.sdecl	'.text.IfxGpt12.IfxGpt12_T6_getFrequency',code,cluster('IfxGpt12_T6_getFrequency')
	.sect	'.text.IfxGpt12.IfxGpt12_T6_getFrequency'
	.align	2
	
	.global	IfxGpt12_T6_getFrequency
; Function IfxGpt12_T6_getFrequency
.L137:
IfxGpt12_T6_getFrequency:	.type	func
	mov.aa	a15,a4
.L378:
	jz.a	a15,.L63
.L63:
	call	IfxScuCcu_getSpbFrequency
.L499:
	j	.L64
.L64:
	ld.bu	d15,[a15]33
	extr.u	d15,d15,#3,#2
.L500:
	mov	d0,#0
	jeq	d15,d0,.L65
.L656:
	mov	d0,#1
	jeq	d15,d0,.L66
.L657:
	mov	d0,#3
	jeq	d15,d0,.L67
	j	.L68
.L66:
	movh	d15,#16384
.L501:
	div.f	d0,d2,d15
.L502:
	j	.L69
.L65:
	movh	d15,#16512
.L503:
	div.f	d0,d2,d15
.L504:
	j	.L70
.L67:
	movh	d15,#16640
.L505:
	div.f	d0,d2,d15
.L506:
	j	.L71
.L68:
	movh	d15,#16768
.L507:
	div.f	d0,d2,d15
.L508:
	j	.L72
.L72:
.L71:
.L70:
.L69:
	ld.bu	d15,[a15]32
	extr.u	d15,d15,#3,#3
.L510:
	ld.bu	d1,[a15]32
	and	d1,d1,#7
.L512:
	jeq	d15,#0,.L73
.L658:
	jeq	d15,#3,.L74
.L659:
	jne	d15,#2,.L75
.L74:
.L73:
	mov	d15,#1
.L511:
	sha	d15,d15,d1
	itof	d15,d15
.L660:
	div.f	d2,d0,d15
.L509:
	j	.L76
.L75:
	movh	d15,#16384
.L513:
	div.f	d2,d0,d15
.L76:
	j	.L77
.L77:
	ret
.L372:
	
__IfxGpt12_T6_getFrequency_function_end:
	.size	IfxGpt12_T6_getFrequency,__IfxGpt12_T6_getFrequency_function_end-IfxGpt12_T6_getFrequency
.L200:
	; End of function
	
	.sdecl	'.text.IfxGpt12.IfxGpt12_disableModule',code,cluster('IfxGpt12_disableModule')
	.sect	'.text.IfxGpt12.IfxGpt12_disableModule'
	.align	2
	
	.global	IfxGpt12_disableModule
; Function IfxGpt12_disableModule
.L139:
IfxGpt12_disableModule:	.type	func
	mov.aa	a15,a4
.L515:
	call	IfxScuWdt_getCpuWatchdogPassword
.L514:
	mov	d8,d2
.L517:
	mov	d4,d8
.L516:
	call	IfxScuWdt_clearCpuEndinit
.L518:
	ld.bu	d15,[a15]
.L567:
	or	d15,#1
	st.b	[a15],d15
.L568:
	mov	d4,d8
.L519:
	call	IfxScuWdt_setCpuEndinit
.L520:
	ret
.L216:
	
__IfxGpt12_disableModule_function_end:
	.size	IfxGpt12_disableModule,__IfxGpt12_disableModule_function_end-IfxGpt12_disableModule
.L160:
	; End of function
	
	.sdecl	'.text.IfxGpt12.IfxGpt12_enableModule',code,cluster('IfxGpt12_enableModule')
	.sect	'.text.IfxGpt12.IfxGpt12_enableModule'
	.align	2
	
	.global	IfxGpt12_enableModule
; Function IfxGpt12_enableModule
.L141:
IfxGpt12_enableModule:	.type	func
	mov.aa	a15,a4
.L522:
	call	IfxScuWdt_getCpuWatchdogPassword
.L521:
	mov	d15,d2
.L524:
	mov	d4,d15
.L523:
	call	IfxScuWdt_clearCpuEndinit
.L525:
	ld.bu	d0,[a15]
.L573:
	insert	d0,d0,#0,#0,#1
	st.b	[a15],d0
.L574:
	mov	d4,d15
.L526:
	call	IfxScuWdt_setCpuEndinit
.L527:
	ret
.L221:
	
__IfxGpt12_enableModule_function_end:
	.size	IfxGpt12_enableModule,__IfxGpt12_enableModule_function_end-IfxGpt12_enableModule
.L165:
	; End of function
	
	.sdecl	'.text.IfxGpt12.IfxGpt12_initTxEudInPin',code,cluster('IfxGpt12_initTxEudInPin')
	.sect	'.text.IfxGpt12.IfxGpt12_initTxEudInPin'
	.align	2
	
	.global	IfxGpt12_initTxEudInPin
; Function IfxGpt12_initTxEudInPin
.L143:
IfxGpt12_initTxEudInPin:	.type	func
	mov.aa	a12,a4
.L530:
	ld.a	a4,[a12]8
.L529:
	ld.bu	d15,[a12]12
.L230:
	extr.u	d5,d4,#0,#8
	mov	d4,d15
.L528:
	call	IfxPort_setPinMode
.L231:
	ld.bu	d15,[a12]4
.L579:
	mov	d0,#2
	jeq	d15,d0,.L78
.L580:
	mov	d0,#3
	jeq	d15,d0,.L79
.L581:
	mov	d0,#4
	jeq	d15,d0,.L80
.L582:
	mov	d0,#5
	jeq	d15,d0,.L81
.L583:
	mov	d0,#6
	jeq	d15,d0,.L82
	j	.L83
.L78:
	ld.a	a15,[a12]
.L584:
	ld.bu	d0,[a12]16
.L240:
	ld.bu	d15,[a15]4
.L585:
	insert	d15,d15,d0,#1,#1
	st.b	[a15]4,d15
.L241:
	j	.L84
.L79:
	ld.a	a15,[a12]
.L586:
	ld.bu	d0,[a12]16
.L248:
	ld.bu	d15,[a15]4
.L587:
	insert	d15,d15,d0,#4,#2
	st.b	[a15]4,d15
.L249:
	j	.L85
.L80:
	ld.a	a15,[a12]
.L588:
	ld.bu	d0,[a12]16
.L256:
	ld.bu	d15,[a15]5
.L589:
	insert	d15,d15,d0,#0,#2
	st.b	[a15]5,d15
.L257:
	j	.L86
.L81:
	ld.a	a15,[a12]
.L590:
	ld.bu	d0,[a12]16
.L264:
	ld.bu	d15,[a15]5
.L591:
	insert	d15,d15,d0,#3,#1
	st.b	[a15]5,d15
.L265:
	j	.L87
.L82:
	ld.a	a15,[a12]
.L592:
	ld.bu	d0,[a12]16
.L272:
	ld.bu	d15,[a15]5
.L593:
	insert	d15,d15,d0,#5,#1
	st.b	[a15]5,d15
.L273:
	j	.L88
.L83:
	j	.L89
.L89:
.L88:
.L87:
.L86:
.L85:
.L84:
	ret
.L224:
	
__IfxGpt12_initTxEudInPin_function_end:
	.size	IfxGpt12_initTxEudInPin,__IfxGpt12_initTxEudInPin_function_end-IfxGpt12_initTxEudInPin
.L170:
	; End of function
	
	.sdecl	'.text.IfxGpt12.IfxGpt12_initTxEudInPinWithPadLevel',code,cluster('IfxGpt12_initTxEudInPinWithPadLevel')
	.sect	'.text.IfxGpt12.IfxGpt12_initTxEudInPinWithPadLevel'
	.align	2
	
	.global	IfxGpt12_initTxEudInPinWithPadLevel
; Function IfxGpt12_initTxEudInPinWithPadLevel
.L145:
IfxGpt12_initTxEudInPinWithPadLevel:	.type	func
	mov.aa	a12,a4
.L534:
	mov	d8,d5
.L535:
	ld.a	a4,[a12]8
.L533:
	ld.bu	d15,[a12]12
.L388:
	extr.u	d5,d4,#0,#8
.L532:
	mov	d4,d15
.L531:
	call	IfxPort_setPinMode
.L389:
	ld.a	a4,[a12]8
.L677:
	ld.bu	d4,[a12]12
.L678:
	mov	d5,d8
.L536:
	call	IfxPort_setPinPadDriver
.L537:
	ld.bu	d15,[a12]4
.L679:
	mov	d0,#2
	jeq	d15,d0,.L90
.L680:
	mov	d0,#3
	jeq	d15,d0,.L91
.L681:
	mov	d0,#4
	jeq	d15,d0,.L92
.L682:
	mov	d0,#5
	jeq	d15,d0,.L93
.L683:
	mov	d0,#6
	jeq	d15,d0,.L94
	j	.L95
.L90:
	ld.a	a15,[a12]
.L684:
	ld.bu	d0,[a12]16
.L393:
	ld.bu	d15,[a15]4
.L685:
	insert	d15,d15,d0,#1,#1
	st.b	[a15]4,d15
.L394:
	j	.L96
.L91:
	ld.a	a15,[a12]
.L686:
	ld.bu	d0,[a12]16
.L397:
	ld.bu	d15,[a15]4
.L687:
	insert	d15,d15,d0,#4,#2
	st.b	[a15]4,d15
.L398:
	j	.L97
.L92:
	ld.a	a15,[a12]
.L688:
	ld.bu	d0,[a12]16
.L401:
	ld.bu	d15,[a15]5
.L689:
	insert	d15,d15,d0,#0,#2
	st.b	[a15]5,d15
.L402:
	j	.L98
.L93:
	ld.a	a15,[a12]
.L690:
	ld.bu	d0,[a12]16
.L405:
	ld.bu	d15,[a15]5
.L691:
	insert	d15,d15,d0,#3,#1
	st.b	[a15]5,d15
.L406:
	j	.L99
.L94:
	ld.a	a15,[a12]
.L692:
	ld.bu	d0,[a12]16
.L409:
	ld.bu	d15,[a15]5
.L693:
	insert	d15,d15,d0,#5,#1
	st.b	[a15]5,d15
.L410:
	j	.L100
.L95:
	j	.L101
.L101:
.L100:
.L99:
.L98:
.L97:
.L96:
	ret
.L383:
	
__IfxGpt12_initTxEudInPinWithPadLevel_function_end:
	.size	IfxGpt12_initTxEudInPinWithPadLevel,__IfxGpt12_initTxEudInPinWithPadLevel_function_end-IfxGpt12_initTxEudInPinWithPadLevel
.L210:
	; End of function
	
	.sdecl	'.text.IfxGpt12.IfxGpt12_initTxInPin',code,cluster('IfxGpt12_initTxInPin')
	.sect	'.text.IfxGpt12.IfxGpt12_initTxInPin'
	.align	2
	
	.global	IfxGpt12_initTxInPin
; Function IfxGpt12_initTxInPin
.L147:
IfxGpt12_initTxInPin:	.type	func
	mov.aa	a12,a4
.L540:
	ld.a	a4,[a12]8
.L539:
	ld.bu	d15,[a12]12
.L283:
	extr.u	d5,d4,#0,#8
	mov	d4,d15
.L538:
	call	IfxPort_setPinMode
.L284:
	ld.bu	d15,[a12]4
.L598:
	mov	d0,#2
	jeq	d15,d0,.L102
.L599:
	mov	d0,#3
	jeq	d15,d0,.L103
.L600:
	mov	d0,#4
	jeq	d15,d0,.L104
.L601:
	mov	d0,#5
	jeq	d15,d0,.L105
.L602:
	mov	d0,#6
	jeq	d15,d0,.L106
	j	.L107
.L102:
	ld.a	a15,[a12]
.L603:
	ld.bu	d0,[a12]16
.L289:
	ld.bu	d15,[a15]4
.L604:
	insert	d15,d15,d0,#0,#1
	st.b	[a15]4,d15
.L290:
	j	.L108
.L103:
	ld.a	a15,[a12]
.L605:
	ld.bu	d0,[a12]16
.L297:
	ld.bu	d15,[a15]4
.L606:
	insert	d15,d15,d0,#2,#2
	st.b	[a15]4,d15
.L298:
	j	.L109
.L104:
	ld.a	a15,[a12]
.L607:
	ld.bu	d0,[a12]16
.L305:
	ld.bu	d15,[a15]4
.L608:
	insert	d15,d15,d0,#6,#2
	st.b	[a15]4,d15
.L306:
	j	.L110
.L105:
	ld.a	a15,[a12]
.L609:
	ld.bu	d0,[a12]16
.L313:
	ld.bu	d15,[a15]5
.L610:
	insert	d15,d15,d0,#2,#1
	st.b	[a15]5,d15
.L314:
	j	.L111
.L106:
	ld.a	a15,[a12]
.L611:
	ld.bu	d0,[a12]16
.L321:
	ld.bu	d15,[a15]5
.L612:
	insert	d15,d15,d0,#4,#1
	st.b	[a15]5,d15
.L322:
	j	.L112
.L107:
	j	.L113
.L113:
.L112:
.L111:
.L110:
.L109:
.L108:
	ret
.L279:
	
__IfxGpt12_initTxInPin_function_end:
	.size	IfxGpt12_initTxInPin,__IfxGpt12_initTxInPin_function_end-IfxGpt12_initTxInPin
.L175:
	; End of function
	
	.sdecl	'.text.IfxGpt12.IfxGpt12_initTxInPinWithPadLevel',code,cluster('IfxGpt12_initTxInPinWithPadLevel')
	.sect	'.text.IfxGpt12.IfxGpt12_initTxInPinWithPadLevel'
	.align	2
	
	.global	IfxGpt12_initTxInPinWithPadLevel
; Function IfxGpt12_initTxInPinWithPadLevel
.L149:
IfxGpt12_initTxInPinWithPadLevel:	.type	func
	mov.aa	a12,a4
.L544:
	mov	d8,d5
.L545:
	ld.a	a4,[a12]8
.L543:
	ld.bu	d15,[a12]12
.L417:
	extr.u	d5,d4,#0,#8
.L542:
	mov	d4,d15
.L541:
	call	IfxPort_setPinMode
.L418:
	ld.a	a4,[a12]8
.L698:
	ld.bu	d4,[a12]12
.L699:
	mov	d5,d8
.L546:
	call	IfxPort_setPinPadDriver
.L547:
	ld.bu	d15,[a12]4
.L700:
	mov	d0,#2
	jeq	d15,d0,.L114
.L701:
	mov	d0,#3
	jeq	d15,d0,.L115
.L702:
	mov	d0,#4
	jeq	d15,d0,.L116
.L703:
	mov	d0,#5
	jeq	d15,d0,.L117
.L704:
	mov	d0,#6
	jeq	d15,d0,.L118
	j	.L119
.L114:
	ld.a	a15,[a12]
.L705:
	ld.bu	d0,[a12]16
.L422:
	ld.bu	d15,[a15]4
.L706:
	insert	d15,d15,d0,#0,#1
	st.b	[a15]4,d15
.L423:
	j	.L120
.L115:
	ld.a	a15,[a12]
.L707:
	ld.bu	d0,[a12]16
.L426:
	ld.bu	d15,[a15]4
.L708:
	insert	d15,d15,d0,#2,#2
	st.b	[a15]4,d15
.L427:
	j	.L121
.L116:
	ld.a	a15,[a12]
.L709:
	ld.bu	d0,[a12]16
.L430:
	ld.bu	d15,[a15]4
.L710:
	insert	d15,d15,d0,#6,#2
	st.b	[a15]4,d15
.L431:
	j	.L122
.L117:
	ld.a	a15,[a12]
.L711:
	ld.bu	d0,[a12]16
.L434:
	ld.bu	d15,[a15]5
.L712:
	insert	d15,d15,d0,#2,#1
	st.b	[a15]5,d15
.L435:
	j	.L123
.L118:
	ld.a	a15,[a12]
.L713:
	ld.bu	d0,[a12]16
.L438:
	ld.bu	d15,[a15]5
.L714:
	insert	d15,d15,d0,#4,#1
	st.b	[a15]5,d15
.L439:
	j	.L124
.L119:
	j	.L125
.L125:
.L124:
.L123:
.L122:
.L121:
.L120:
	ret
.L413:
	
__IfxGpt12_initTxInPinWithPadLevel_function_end:
	.size	IfxGpt12_initTxInPinWithPadLevel,__IfxGpt12_initTxInPinWithPadLevel_function_end-IfxGpt12_initTxInPinWithPadLevel
.L215:
	; End of function
	
	.sdecl	'.text.IfxGpt12.IfxGpt12_resetModule',code,cluster('IfxGpt12_resetModule')
	.sect	'.text.IfxGpt12.IfxGpt12_resetModule'
	.align	2
	
	.global	IfxGpt12_resetModule
; Function IfxGpt12_resetModule
.L151:
IfxGpt12_resetModule:	.type	func
	mov.aa	a15,a4
.L549:
	call	IfxScuWdt_getCpuWatchdogPassword
.L548:
	mov	d8,d2
.L551:
	mov	d4,d8
.L550:
	call	IfxScuWdt_clearCpuEndinit
.L552:
	ld.bu	d15,[a15]244
.L665:
	or	d15,#1
	st.b	[a15]244,d15
.L666:
	ld.bu	d15,[a15]240
.L667:
	or	d15,#1
	st.b	[a15]240,d15
.L668:
	mov	d4,d8
.L553:
	call	IfxScuWdt_setCpuEndinit
.L554:
	j	.L126
.L127:
.L126:
	ld.bu	d15,[a15]244
.L669:
	jz.t	d15:1,.L127
.L670:
	mov	d4,d8
.L555:
	call	IfxScuWdt_clearCpuEndinit
.L556:
	ld.bu	d15,[a15]236
.L671:
	or	d15,#1
	st.b	[a15]236,d15
.L672:
	mov	d4,d8
.L557:
	call	IfxScuWdt_setCpuEndinit
.L558:
	ret
.L380:
	
__IfxGpt12_resetModule_function_end:
	.size	IfxGpt12_resetModule,__IfxGpt12_resetModule_function_end-IfxGpt12_resetModule
.L205:
	; End of function
	
	.calls	'IfxGpt12_T2_getFrequency','IfxScuCcu_getSpbFrequency'
	.calls	'IfxGpt12_T3_getFrequency','IfxScuCcu_getSpbFrequency'
	.calls	'IfxGpt12_T4_getFrequency','IfxScuCcu_getSpbFrequency'
	.calls	'IfxGpt12_T5_getFrequency','IfxScuCcu_getSpbFrequency'
	.calls	'IfxGpt12_T6_getFrequency','IfxScuCcu_getSpbFrequency'
	.calls	'IfxGpt12_disableModule','IfxScuWdt_getCpuWatchdogPassword'
	.calls	'IfxGpt12_disableModule','IfxScuWdt_clearCpuEndinit'
	.calls	'IfxGpt12_disableModule','IfxScuWdt_setCpuEndinit'
	.calls	'IfxGpt12_enableModule','IfxScuWdt_getCpuWatchdogPassword'
	.calls	'IfxGpt12_enableModule','IfxScuWdt_clearCpuEndinit'
	.calls	'IfxGpt12_enableModule','IfxScuWdt_setCpuEndinit'
	.calls	'IfxGpt12_initTxEudInPin','IfxPort_setPinMode'
	.calls	'IfxGpt12_initTxEudInPinWithPadLevel','IfxPort_setPinMode'
	.calls	'IfxGpt12_initTxEudInPinWithPadLevel','IfxPort_setPinPadDriver'
	.calls	'IfxGpt12_initTxInPin','IfxPort_setPinMode'
	.calls	'IfxGpt12_initTxInPinWithPadLevel','IfxPort_setPinMode'
	.calls	'IfxGpt12_initTxInPinWithPadLevel','IfxPort_setPinPadDriver'
	.calls	'IfxGpt12_resetModule','IfxScuWdt_getCpuWatchdogPassword'
	.calls	'IfxGpt12_resetModule','IfxScuWdt_clearCpuEndinit'
	.calls	'IfxGpt12_resetModule','IfxScuWdt_setCpuEndinit'
	.calls	'IfxGpt12_T2_getFrequency','',0
	.calls	'IfxGpt12_T3_getFrequency','',0
	.calls	'IfxGpt12_T4_getFrequency','',0
	.calls	'IfxGpt12_T5_getFrequency','',0
	.calls	'IfxGpt12_T6_getFrequency','',0
	.calls	'IfxGpt12_disableModule','',0
	.calls	'IfxGpt12_enableModule','',0
	.calls	'IfxGpt12_initTxEudInPin','',0
	.calls	'IfxGpt12_initTxEudInPinWithPadLevel','',0
	.calls	'IfxGpt12_initTxInPin','',0
	.calls	'IfxGpt12_initTxInPinWithPadLevel','',0
	.extern	IfxScuWdt_clearCpuEndinit
	.extern	IfxScuWdt_setCpuEndinit
	.extern	IfxScuWdt_getCpuWatchdogPassword
	.extern	IfxPort_setPinMode
	.extern	IfxPort_setPinPadDriver
	.extern	IfxScuCcu_getSpbFrequency
	.calls	'IfxGpt12_resetModule','',0
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L153:
	.word	88891
	.half	3
	.word	.L154
	.byte	4
.L152:
	.byte	1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Gpt12/Std/IfxGpt12.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L155
	.byte	2,1,1,3
	.word	234
	.byte	4
	.byte	'Ifx__jump_and_link',0,3,1,61,17,1,1,5
	.byte	'fun',0,1,61,43
	.word	237
	.byte	6,0,7
	.byte	'__fract',0,4,128,1
.L328:
	.byte	7
	.byte	'float',0,4,4,8
	.byte	'Ifx__float_to_fract',0,3,1,152,2,18
	.word	282
	.byte	1,1,5
	.byte	'a',0,1,152,2,44
	.word	294
	.byte	6,0,4
	.byte	'Ifx__stopPerfCounters',0,3,1,172,2,17,1,1,6,0,7
	.byte	'unsigned long long int',0,8,7,9
	.byte	'void',0,3
	.word	406
	.byte	8
	.byte	'__ld64',0,3,2,135,1,19
	.word	380
	.byte	1,1,5
	.byte	'addr',0,2,135,1,32
	.word	412
	.byte	6,0,4
	.byte	'__st64',0,3,2,143,1,17,1,1,5
	.byte	'addr',0,2,143,1,30
	.word	412
	.byte	5
	.byte	'value',0,2,143,1,43
	.word	380
	.byte	6,0,7
	.byte	'unsigned int',0,4,7,7
	.byte	'int',0,4,5,7
	.byte	'unsigned char',0,1,8,10
	.byte	'_Ifx_SRC_SRCR_Bits',0,4,45,16,4,11
	.byte	'SRPN',0,1
	.word	521
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	521
	.byte	2,6,2,35,1,11
	.byte	'SRE',0,1
	.word	521
	.byte	1,5,2,35,1,11
	.byte	'TOS',0,1
	.word	521
	.byte	2,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	521
	.byte	3,0,2,35,1,11
	.byte	'ECC',0,1
	.word	521
	.byte	6,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	521
	.byte	2,0,2,35,2,11
	.byte	'SRR',0,1
	.word	521
	.byte	1,7,2,35,3,11
	.byte	'CLRR',0,1
	.word	521
	.byte	1,6,2,35,3,11
	.byte	'SETR',0,1
	.word	521
	.byte	1,5,2,35,3,11
	.byte	'IOV',0,1
	.word	521
	.byte	1,4,2,35,3,11
	.byte	'IOVCLR',0,1
	.word	521
	.byte	1,3,2,35,3,11
	.byte	'SWS',0,1
	.word	521
	.byte	1,2,2,35,3,11
	.byte	'SWSCLR',0,1
	.word	521
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	521
	.byte	1,0,2,35,3,0,12,4,70,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	538
	.byte	4,2,35,0,0,14
	.word	828
	.byte	3
	.word	867
	.byte	4
	.byte	'IfxSrc_clearRequest',0,3,3,250,1,17,1,1,5
	.byte	'src',0,3,250,1,60
	.word	872
	.byte	6,0,7
	.byte	'unsigned int',0,4,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON0_Bits',0,6,241,8,16,4,11
	.byte	'ENDINIT',0,4
	.word	920
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	920
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	920
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	920
	.byte	16,0,2,35,0,0,12,6,247,14,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	936
	.byte	4,2,35,0,0
.L219:
	.byte	7
	.byte	'unsigned short int',0,2,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON1_Bits',0,6,250,8,16,4,11
	.byte	'reserved_0',0,1
	.word	521
	.byte	2,6,2,35,0,11
	.byte	'IR0',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	521
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	521
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	521
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	521
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	521
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	521
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1072
	.byte	16,0,2,35,2,0,12,6,255,14,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1094
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_WDTCPU_SR_Bits',0,6,137,9,16,4,11
	.byte	'AE',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	521
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	521
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	521
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	521
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	521
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	521
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	1072
	.byte	16,0,2,35,2,0,12,6,135,15,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1338
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_WDTCPU',0,6,175,15,25,12,13
	.byte	'CON0',0
	.word	1032
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	1298
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	1529
	.byte	4,2,35,8,0,14
	.word	1569
	.byte	3
	.word	1632
	.byte	4
	.byte	'IfxScuWdt_clearCpuEndinitInline',0,3,5,181,3,17,1,1,5
	.byte	'watchdog',0,5,181,3,65
	.word	1637
	.byte	5
	.byte	'password',0,5,181,3,82
	.word	1072
	.byte	6,0,4
	.byte	'IfxScuWdt_setCpuEndinitInline',0,3,5,140,4,17,1,1,5
	.byte	'watchdog',0,5,140,4,63
	.word	1637
	.byte	5
	.byte	'password',0,5,140,4,80
	.word	1072
	.byte	6,0,8
	.byte	'IfxScuWdt_getCpuWatchdogPasswordInline',0,3,5,227,3,19
	.word	1072
	.byte	1,1,5
	.byte	'watchdog',0,5,227,3,74
	.word	1637
	.byte	6,0,15,8,156,1,9,1,16
	.byte	'IfxCpu_ResourceCpu_0',0,0,16
	.byte	'IfxCpu_ResourceCpu_1',0,1,16
	.byte	'IfxCpu_ResourceCpu_none',0,2,0,8
	.byte	'IfxCpu_getCoreIndex',0,3,7,141,6,31
	.word	1867
	.byte	1,1,6,0,8
	.byte	'IfxCpu_areInterruptsEnabled',0,3,7,139,5,20
	.word	521
	.byte	1,1,6,0,7
	.byte	'unsigned long int',0,4,7,8
	.byte	'IfxCpu_getPerformanceCounter',0,3,7,161,6,19
	.word	2022
	.byte	1,1,5
	.byte	'address',0,7,161,6,55
	.word	1072
	.byte	6,0,8
	.byte	'IfxCpu_getPerformanceCounterStickyOverflow',0,3,7,190,6,20
	.word	521
	.byte	1,1,5
	.byte	'address',0,7,190,6,70
	.word	1072
	.byte	6,0,4
	.byte	'IfxCpu_updatePerformanceCounter',0,3,7,172,8,17,1,1,5
	.byte	'address',0,7,172,8,56
	.word	2022
	.byte	5
	.byte	'count',0,7,172,8,72
	.word	2022
	.byte	17,6,0,0,10
	.byte	'_Ifx_P_OUT_Bits',0,10,143,3,16,4,11
	.byte	'P0',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	521
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	521
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	521
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	521
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	521
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	521
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	521
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	521
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	521
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	521
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	521
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	521
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1072
	.byte	16,0,2,35,2,0,12,10,181,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2253
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMR_Bits',0,10,169,2,16,4,11
	.byte	'PS0',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	521
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	521
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	521
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	521
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	521
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	521
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	521
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	521
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	521
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	521
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	521
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	521
	.byte	1,0,2,35,1,11
	.byte	'PCL0',0,1
	.word	521
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	521
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	521
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	521
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	521
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	521
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	521
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	521
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	521
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	521
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	521
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	521
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	521
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	521
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	521
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	521
	.byte	1,0,2,35,3,0,12,10,133,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2569
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ID_Bits',0,10,110,16,4,11
	.byte	'MODREV',0,1
	.word	521
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	521
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	1072
	.byte	16,0,2,35,2,0,12,10,148,4,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3140
	.byte	4,2,35,0,0,18,4
	.word	521
	.byte	19,3,0,10
	.byte	'_Ifx_P_IOCR0_Bits',0,10,140,1,16,4,11
	.byte	'reserved_0',0,1
	.word	521
	.byte	3,5,2,35,0,11
	.byte	'PC0',0,1
	.word	521
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	521
	.byte	3,5,2,35,1,11
	.byte	'PC1',0,1
	.word	521
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	521
	.byte	3,5,2,35,2,11
	.byte	'PC2',0,1
	.word	521
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	521
	.byte	3,5,2,35,3,11
	.byte	'PC3',0,1
	.word	521
	.byte	5,0,2,35,3,0,12,10,164,4,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3268
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR4_Bits',0,10,166,1,16,4,11
	.byte	'reserved_0',0,1
	.word	521
	.byte	3,5,2,35,0,11
	.byte	'PC4',0,1
	.word	521
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	521
	.byte	3,5,2,35,1,11
	.byte	'PC5',0,1
	.word	521
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	521
	.byte	3,5,2,35,2,11
	.byte	'PC6',0,1
	.word	521
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	521
	.byte	3,5,2,35,3,11
	.byte	'PC7',0,1
	.word	521
	.byte	5,0,2,35,3,0,12,10,180,4,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3483
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR8_Bits',0,10,179,1,16,4,11
	.byte	'reserved_0',0,1
	.word	521
	.byte	3,5,2,35,0,11
	.byte	'PC8',0,1
	.word	521
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	521
	.byte	3,5,2,35,1,11
	.byte	'PC9',0,1
	.word	521
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	521
	.byte	3,5,2,35,2,11
	.byte	'PC10',0,1
	.word	521
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	521
	.byte	3,5,2,35,3,11
	.byte	'PC11',0,1
	.word	521
	.byte	5,0,2,35,3,0,12,10,188,4,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3698
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR12_Bits',0,10,153,1,16,4,11
	.byte	'reserved_0',0,1
	.word	521
	.byte	3,5,2,35,0,11
	.byte	'PC12',0,1
	.word	521
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	521
	.byte	3,5,2,35,1,11
	.byte	'PC13',0,1
	.word	521
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	521
	.byte	3,5,2,35,2,11
	.byte	'PC14',0,1
	.word	521
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	521
	.byte	3,5,2,35,3,11
	.byte	'PC15',0,1
	.word	521
	.byte	5,0,2,35,3,0,12,10,172,4,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3915
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IN_Bits',0,10,118,16,4,11
	.byte	'P0',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	521
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	521
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	521
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	521
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	521
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	521
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	521
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	521
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	521
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	521
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	521
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	521
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1072
	.byte	16,0,2,35,2,0,12,10,156,4,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4135
	.byte	4,2,35,0,0,18,24
	.word	521
	.byte	19,23,0,10
	.byte	'_Ifx_P_PDR0_Bits',0,10,205,3,16,4,11
	.byte	'PD0',0,1
	.word	521
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	521
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	521
	.byte	1,0,2,35,0,11
	.byte	'PD2',0,1
	.word	521
	.byte	3,5,2,35,1,11
	.byte	'PL2',0,1
	.word	521
	.byte	1,4,2,35,1,11
	.byte	'PD3',0,1
	.word	521
	.byte	3,1,2,35,1,11
	.byte	'PL3',0,1
	.word	521
	.byte	1,0,2,35,1,11
	.byte	'PD4',0,1
	.word	521
	.byte	3,5,2,35,2,11
	.byte	'PL4',0,1
	.word	521
	.byte	1,4,2,35,2,11
	.byte	'PD5',0,1
	.word	521
	.byte	3,1,2,35,2,11
	.byte	'PL5',0,1
	.word	521
	.byte	1,0,2,35,2,11
	.byte	'PD6',0,1
	.word	521
	.byte	3,5,2,35,3,11
	.byte	'PL6',0,1
	.word	521
	.byte	1,4,2,35,3,11
	.byte	'PD7',0,1
	.word	521
	.byte	3,1,2,35,3,11
	.byte	'PL7',0,1
	.word	521
	.byte	1,0,2,35,3,0,12,10,205,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4458
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PDR1_Bits',0,10,226,3,16,4,11
	.byte	'PD8',0,1
	.word	521
	.byte	3,5,2,35,0,11
	.byte	'PL8',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'PD9',0,1
	.word	521
	.byte	3,1,2,35,0,11
	.byte	'PL9',0,1
	.word	521
	.byte	1,0,2,35,0,11
	.byte	'PD10',0,1
	.word	521
	.byte	3,5,2,35,1,11
	.byte	'PL10',0,1
	.word	521
	.byte	1,4,2,35,1,11
	.byte	'PD11',0,1
	.word	521
	.byte	3,1,2,35,1,11
	.byte	'PL11',0,1
	.word	521
	.byte	1,0,2,35,1,11
	.byte	'PD12',0,1
	.word	521
	.byte	3,5,2,35,2,11
	.byte	'PL12',0,1
	.word	521
	.byte	1,4,2,35,2,11
	.byte	'PD13',0,1
	.word	521
	.byte	3,1,2,35,2,11
	.byte	'PL13',0,1
	.word	521
	.byte	1,0,2,35,2,11
	.byte	'PD14',0,1
	.word	521
	.byte	3,5,2,35,3,11
	.byte	'PL14',0,1
	.word	521
	.byte	1,4,2,35,3,11
	.byte	'PD15',0,1
	.word	521
	.byte	3,1,2,35,3,11
	.byte	'PL15',0,1
	.word	521
	.byte	1,0,2,35,3,0,12,10,213,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4762
	.byte	4,2,35,0,0,18,8
	.word	521
	.byte	19,7,0,10
	.byte	'_Ifx_P_ESR_Bits',0,10,88,16,4,11
	.byte	'EN0',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	521
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	521
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	521
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	521
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	521
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	521
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	521
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	521
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	521
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	521
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	521
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	521
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1072
	.byte	16,0,2,35,2,0,12,10,140,4,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5087
	.byte	4,2,35,0,0,18,12
	.word	521
	.byte	19,11,0,10
	.byte	'_Ifx_P_PDISC_Bits',0,10,183,3,16,4,11
	.byte	'PDIS0',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'PDIS2',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'PDIS3',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'PDIS4',0,1
	.word	521
	.byte	1,3,2,35,0,11
	.byte	'PDIS5',0,1
	.word	521
	.byte	1,2,2,35,0,11
	.byte	'PDIS6',0,1
	.word	521
	.byte	1,1,2,35,0,11
	.byte	'PDIS7',0,1
	.word	521
	.byte	1,0,2,35,0,11
	.byte	'PDIS8',0,1
	.word	521
	.byte	1,7,2,35,1,11
	.byte	'PDIS9',0,1
	.word	521
	.byte	1,6,2,35,1,11
	.byte	'PDIS10',0,1
	.word	521
	.byte	1,5,2,35,1,11
	.byte	'PDIS11',0,1
	.word	521
	.byte	1,4,2,35,1,11
	.byte	'PDIS12',0,1
	.word	521
	.byte	1,3,2,35,1,11
	.byte	'PDIS13',0,1
	.word	521
	.byte	1,2,2,35,1,11
	.byte	'PDIS14',0,1
	.word	521
	.byte	1,1,2,35,1,11
	.byte	'PDIS15',0,1
	.word	521
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1072
	.byte	16,0,2,35,2,0,12,10,197,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5427
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PCSR_Bits',0,10,165,3,16,4,11
	.byte	'SEL0',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'SEL1',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'SEL2',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'SEL3',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'SEL4',0,1
	.word	521
	.byte	1,3,2,35,0,11
	.byte	'SEL5',0,1
	.word	521
	.byte	1,2,2,35,0,11
	.byte	'SEL6',0,1
	.word	521
	.byte	1,1,2,35,0,11
	.byte	'SEL7',0,1
	.word	521
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	521
	.byte	2,6,2,35,1,11
	.byte	'SEL10',0,1
	.word	521
	.byte	1,5,2,35,1,11
	.byte	'SEL11',0,1
	.word	521
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	498
	.byte	19,1,2,35,0,11
	.byte	'LCK',0,1
	.word	521
	.byte	1,0,2,35,3,0,12,10,189,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5793
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR0_Bits',0,10,206,2,16,4,11
	.byte	'PS0',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	498
	.byte	28,0,2,35,0,0,12,10,149,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6079
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR4_Bits',0,10,227,2,16,4,11
	.byte	'reserved_0',0,1
	.word	521
	.byte	4,4,2,35,0,11
	.byte	'PS4',0,1
	.word	521
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	521
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	521
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	521
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	498
	.byte	24,0,2,35,0,0,12,10,165,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6226
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR8_Bits',0,10,238,2,16,4,11
	.byte	'reserved_0',0,1
	.word	521
	.byte	8,0,2,35,0,11
	.byte	'PS8',0,1
	.word	521
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	521
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	521
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	521
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	498
	.byte	20,0,2,35,0,0,12,10,173,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6395
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR12_Bits',0,10,216,2,16,4,11
	.byte	'reserved_0',0,2
	.word	1072
	.byte	12,4,2,35,0,11
	.byte	'PS12',0,1
	.word	521
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	521
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	521
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	521
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1072
	.byte	16,0,2,35,2,0,12,10,157,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6567
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR0_Bits',0,10,232,1,16,4,11
	.byte	'reserved_0',0,2
	.word	1072
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	521
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	521
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	521
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	521
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	1072
	.byte	12,0,2,35,2,0,12,10,229,4,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6742
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR4_Bits',0,10,253,1,16,4,11
	.byte	'reserved_0',0,4
	.word	498
	.byte	20,12,2,35,0,11
	.byte	'PCL4',0,1
	.word	521
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	521
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	521
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	521
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	521
	.byte	8,0,2,35,3,0,12,10,245,4,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6916
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR8_Bits',0,10,136,2,16,4,11
	.byte	'reserved_0',0,4
	.word	498
	.byte	24,8,2,35,0,11
	.byte	'PCL8',0,1
	.word	521
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	521
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	521
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	521
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	521
	.byte	4,0,2,35,3,0,12,10,253,4,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7090
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR12_Bits',0,10,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	498
	.byte	28,4,2,35,0,11
	.byte	'PCL12',0,1
	.word	521
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	521
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	521
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	521
	.byte	1,0,2,35,3,0,12,10,237,4,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7266
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR_Bits',0,10,249,2,16,4,11
	.byte	'PS0',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	521
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	521
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	521
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	521
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	521
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	521
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	521
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	521
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	521
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	521
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	521
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	521
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1072
	.byte	16,0,2,35,2,0,12,10,141,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7422
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR_Bits',0,10,147,2,16,4,11
	.byte	'reserved_0',0,2
	.word	1072
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	521
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	521
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	521
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	521
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	521
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	521
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	521
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	521
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	521
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	521
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	521
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	521
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	521
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	521
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	521
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	521
	.byte	1,0,2,35,3,0,12,10,221,4,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7755
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR0_Bits',0,10,192,1,16,4,11
	.byte	'reserved_0',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	498
	.byte	30,0,2,35,0,0,12,10,196,4,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8103
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_Bits',0,10,200,1,16,4,11
	.byte	'reserved_0',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	498
	.byte	30,0,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_P21_Bits',0,10,208,1,16,4,11
	.byte	'RDIS_CTRL',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'RX_DIS',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'TERM',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'LRXTERM',0,1
	.word	521
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	498
	.byte	24,0,2,35,0,0,12,10,204,4,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8227
	.byte	4,2,35,0,13
	.byte	'B_P21',0
	.word	8311
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR2_Bits',0,10,218,1,16,4,11
	.byte	'reserved_0',0,1
	.word	521
	.byte	8,0,2,35,0,11
	.byte	'LVDSR',0,1
	.word	521
	.byte	1,7,2,35,1,11
	.byte	'LVDSRL',0,1
	.word	521
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	521
	.byte	2,4,2,35,1,11
	.byte	'TDIS_CTRL',0,1
	.word	521
	.byte	1,3,2,35,1,11
	.byte	'TX_DIS',0,1
	.word	521
	.byte	1,2,2,35,1,11
	.byte	'TX_PD',0,1
	.word	521
	.byte	1,1,2,35,1,11
	.byte	'TX_PWDPD',0,1
	.word	521
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1072
	.byte	16,0,2,35,2,0,12,10,213,4,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8491
	.byte	4,2,35,0,0,18,76
	.word	521
	.byte	19,75,0,10
	.byte	'_Ifx_P_ACCEN1_Bits',0,10,82,16,4,11
	.byte	'reserved_0',0,4
	.word	498
	.byte	32,0,2,35,0,0,12,10,132,4,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8744
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ACCEN0_Bits',0,10,45,16,4,11
	.byte	'EN0',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	521
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	521
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	521
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	521
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	521
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	521
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	521
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	521
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	521
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	521
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	521
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	521
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	521
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	521
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	521
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	521
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	521
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	521
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	521
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	521
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	521
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	521
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	521
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	521
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	521
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	521
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	521
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	521
	.byte	1,0,2,35,3,0,12,10,252,3,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8831
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P',0,10,229,5,25,128,2,13
	.byte	'OUT',0
	.word	2529
	.byte	4,2,35,0,13
	.byte	'OMR',0
	.word	3100
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	3219
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	3259
	.byte	4,2,35,12,13
	.byte	'IOCR0',0
	.word	3443
	.byte	4,2,35,16,13
	.byte	'IOCR4',0
	.word	3658
	.byte	4,2,35,20,13
	.byte	'IOCR8',0
	.word	3875
	.byte	4,2,35,24,13
	.byte	'IOCR12',0
	.word	4095
	.byte	4,2,35,28,13
	.byte	'reserved_20',0
	.word	3259
	.byte	4,2,35,32,13
	.byte	'IN',0
	.word	4409
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	4449
	.byte	24,2,35,40,13
	.byte	'PDR0',0
	.word	4722
	.byte	4,2,35,64,13
	.byte	'PDR1',0
	.word	5038
	.byte	4,2,35,68,13
	.byte	'reserved_48',0
	.word	5078
	.byte	8,2,35,72,13
	.byte	'ESR',0
	.word	5378
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	5418
	.byte	12,2,35,84,13
	.byte	'PDISC',0
	.word	5753
	.byte	4,2,35,96,13
	.byte	'PCSR',0
	.word	6039
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	5078
	.byte	8,2,35,104,13
	.byte	'OMSR0',0
	.word	6186
	.byte	4,2,35,112,13
	.byte	'OMSR4',0
	.word	6355
	.byte	4,2,35,116,13
	.byte	'OMSR8',0
	.word	6527
	.byte	4,2,35,120,13
	.byte	'OMSR12',0
	.word	6702
	.byte	4,2,35,124,13
	.byte	'OMCR0',0
	.word	6876
	.byte	4,3,35,128,1,13
	.byte	'OMCR4',0
	.word	7050
	.byte	4,3,35,132,1,13
	.byte	'OMCR8',0
	.word	7226
	.byte	4,3,35,136,1,13
	.byte	'OMCR12',0
	.word	7382
	.byte	4,3,35,140,1,13
	.byte	'OMSR',0
	.word	7715
	.byte	4,3,35,144,1,13
	.byte	'OMCR',0
	.word	8063
	.byte	4,3,35,148,1,13
	.byte	'reserved_98',0
	.word	5078
	.byte	8,3,35,152,1,13
	.byte	'LPCR0',0
	.word	8187
	.byte	4,3,35,160,1,13
	.byte	'LPCR1',0
	.word	8436
	.byte	4,3,35,164,1,13
	.byte	'LPCR2',0
	.word	8695
	.byte	4,3,35,168,1,13
	.byte	'reserved_A4',0
	.word	8735
	.byte	76,3,35,172,1,13
	.byte	'ACCEN1',0
	.word	8791
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	9358
	.byte	4,3,35,252,1,0,14
	.word	9398
	.byte	3
	.word	10001
.L227:
	.byte	15,9,83,9,1,16
	.byte	'IfxPort_InputMode_undefined',0,127,16
	.byte	'IfxPort_InputMode_noPullDevice',0,0,16
	.byte	'IfxPort_InputMode_pullDown',0,8,16
	.byte	'IfxPort_InputMode_pullUp',0,16,0
.L229:
	.byte	4
	.byte	'IfxPort_setPinModeInput',0,3,9,196,4,17,1,1
.L232:
	.byte	5
	.byte	'port',0,9,196,4,48
	.word	10006
.L234:
	.byte	5
	.byte	'pinIndex',0,9,196,4,60
	.word	521
.L236:
	.byte	5
	.byte	'mode',0,9,196,4,88
	.word	10011
.L238:
	.byte	6,0,15,9,134,1,9,1,16
	.byte	'IfxPort_OutputMode_pushPull',0,128,1,16
	.byte	'IfxPort_OutputMode_openDrain',0,192,1,0,15,9,120,9,1,16
	.byte	'IfxPort_OutputIdx_general',0,128,1,16
	.byte	'IfxPort_OutputIdx_alt1',0,136,1,16
	.byte	'IfxPort_OutputIdx_alt2',0,144,1,16
	.byte	'IfxPort_OutputIdx_alt3',0,152,1,16
	.byte	'IfxPort_OutputIdx_alt4',0,160,1,16
	.byte	'IfxPort_OutputIdx_alt5',0,168,1,16
	.byte	'IfxPort_OutputIdx_alt6',0,176,1,16
	.byte	'IfxPort_OutputIdx_alt7',0,184,1,0,4
	.byte	'IfxPort_setPinModeOutput',0,3,9,202,4,17,1,1,5
	.byte	'port',0,9,202,4,49
	.word	10006
	.byte	5
	.byte	'pinIndex',0,9,202,4,61
	.word	521
	.byte	5
	.byte	'mode',0,9,202,4,90
	.word	10216
	.byte	5
	.byte	'index',0,9,202,4,114
	.word	10286
	.byte	6,0,15,9,172,1,9,4,16
	.byte	'IfxPort_State_notChanged',0,0,16
	.byte	'IfxPort_State_high',0,1,16
	.byte	'IfxPort_State_low',0,128,128,4,16
	.byte	'IfxPort_State_toggled',0,129,128,4,0,4
	.byte	'IfxPort_setPinState',0,3,9,208,4,17,1,1,5
	.byte	'port',0,9,208,4,44
	.word	10006
	.byte	5
	.byte	'pinIndex',0,9,208,4,56
	.word	521
	.byte	5
	.byte	'action',0,9,208,4,80
	.word	10599
	.byte	6,0,10
	.byte	'_Ifx_GPT12_CLC_Bits',0,12,95,16,4,11
	.byte	'DISR',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'DISS',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'EDIS',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	498
	.byte	28,0,2,35,0,0,12,12,184,2,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10780
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GPT12_PISEL_Bits',0,12,145,1,16,4,11
	.byte	'IST2IN',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'IST2EUD',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'IST3IN',0,1
	.word	521
	.byte	2,4,2,35,0,11
	.byte	'IST3EUD',0,1
	.word	521
	.byte	2,2,2,35,0,11
	.byte	'IST4IN',0,1
	.word	521
	.byte	2,0,2,35,0,11
	.byte	'IST4EUD',0,1
	.word	521
	.byte	2,6,2,35,1,11
	.byte	'IST5IN',0,1
	.word	521
	.byte	1,5,2,35,1,11
	.byte	'IST5EUD',0,1
	.word	521
	.byte	1,4,2,35,1,11
	.byte	'IST6IN',0,1
	.word	521
	.byte	1,3,2,35,1,11
	.byte	'IST6EUD',0,1
	.word	521
	.byte	1,2,2,35,1,11
	.byte	'ISCAPIN',0,1
	.word	521
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1072
	.byte	16,0,2,35,2,0,12,12,232,2,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10938
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GPT12_ID_Bits',0,12,105,16,4,11
	.byte	'MODREV',0,1
	.word	521
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	521
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	1072
	.byte	16,0,2,35,2,0,12,12,192,2,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11234
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GPT12_T2CON_Bits',0,12,169,1,16,4,11
	.byte	'T2I',0,1
	.word	521
	.byte	3,5,2,35,0,11
	.byte	'T2M',0,1
	.word	521
	.byte	3,2,2,35,0,11
	.byte	'T2R',0,1
	.word	521
	.byte	1,1,2,35,0,11
	.byte	'T2UD',0,1
	.word	521
	.byte	1,0,2,35,0,11
	.byte	'T2UDE',0,1
	.word	521
	.byte	1,7,2,35,1,11
	.byte	'T2RC',0,1
	.word	521
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	521
	.byte	2,4,2,35,1,11
	.byte	'T2IRDIS',0,1
	.word	521
	.byte	1,3,2,35,1,11
	.byte	'T2EDGE',0,1
	.word	521
	.byte	1,2,2,35,1,11
	.byte	'T2CHDIR',0,1
	.word	521
	.byte	1,1,2,35,1,11
	.byte	'T2RDIR',0,1
	.word	521
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1072
	.byte	16,0,2,35,2,0,12,12,248,2,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11357
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GPT12_T3CON_Bits',0,12,193,1,16,4,11
	.byte	'T3I',0,1
	.word	521
	.byte	3,5,2,35,0,11
	.byte	'T3M',0,1
	.word	521
	.byte	3,2,2,35,0,11
	.byte	'T3R',0,1
	.word	521
	.byte	1,1,2,35,0,11
	.byte	'T3UD',0,1
	.word	521
	.byte	1,0,2,35,0,11
	.byte	'T3UDE',0,1
	.word	521
	.byte	1,7,2,35,1,11
	.byte	'T3OE',0,1
	.word	521
	.byte	1,6,2,35,1,11
	.byte	'T3OTL',0,1
	.word	521
	.byte	1,5,2,35,1,11
	.byte	'BPS1',0,1
	.word	521
	.byte	2,3,2,35,1,11
	.byte	'T3EDGE',0,1
	.word	521
	.byte	1,2,2,35,1,11
	.byte	'T3CHDIR',0,1
	.word	521
	.byte	1,1,2,35,1,11
	.byte	'T3RDIR',0,1
	.word	521
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1072
	.byte	16,0,2,35,2,0,12,12,136,3,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11640
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GPT12_T4CON_Bits',0,12,217,1,16,4,11
	.byte	'T4I',0,1
	.word	521
	.byte	3,5,2,35,0,11
	.byte	'T4M',0,1
	.word	521
	.byte	3,2,2,35,0,11
	.byte	'T4R',0,1
	.word	521
	.byte	1,1,2,35,0,11
	.byte	'T4UD',0,1
	.word	521
	.byte	1,0,2,35,0,11
	.byte	'T4UDE',0,1
	.word	521
	.byte	1,7,2,35,1,11
	.byte	'T4RC',0,1
	.word	521
	.byte	1,6,2,35,1,11
	.byte	'CLRT2EN',0,1
	.word	521
	.byte	1,5,2,35,1,11
	.byte	'CLRT3EN',0,1
	.word	521
	.byte	1,4,2,35,1,11
	.byte	'T4IRDIS',0,1
	.word	521
	.byte	1,3,2,35,1,11
	.byte	'T4EDGE',0,1
	.word	521
	.byte	1,2,2,35,1,11
	.byte	'T4CHDIR',0,1
	.word	521
	.byte	1,1,2,35,1,11
	.byte	'T4RDIR',0,1
	.word	521
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1072
	.byte	16,0,2,35,2,0,12,12,152,3,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11914
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GPT12_T5CON_Bits',0,12,242,1,16,4,11
	.byte	'T5I',0,1
	.word	521
	.byte	3,5,2,35,0,11
	.byte	'T5M',0,1
	.word	521
	.byte	3,2,2,35,0,11
	.byte	'T5R',0,1
	.word	521
	.byte	1,1,2,35,0,11
	.byte	'T5UD',0,1
	.word	521
	.byte	1,0,2,35,0,11
	.byte	'T5UDE',0,1
	.word	521
	.byte	1,7,2,35,1,11
	.byte	'T5RC',0,1
	.word	521
	.byte	1,6,2,35,1,11
	.byte	'CT3',0,1
	.word	521
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	521
	.byte	1,4,2,35,1,11
	.byte	'CI',0,1
	.word	521
	.byte	2,2,2,35,1,11
	.byte	'T5CLR',0,1
	.word	521
	.byte	1,1,2,35,1,11
	.byte	'T5SC',0,1
	.word	521
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1072
	.byte	16,0,2,35,2,0,12,12,168,3,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12212
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GPT12_T6CON_Bits',0,12,138,2,16,4,11
	.byte	'T6I',0,1
	.word	521
	.byte	3,5,2,35,0,11
	.byte	'T6M',0,1
	.word	521
	.byte	3,2,2,35,0,11
	.byte	'T6R',0,1
	.word	521
	.byte	1,1,2,35,0,11
	.byte	'T6UD',0,1
	.word	521
	.byte	1,0,2,35,0,11
	.byte	'T6UDE',0,1
	.word	521
	.byte	1,7,2,35,1,11
	.byte	'T6OE',0,1
	.word	521
	.byte	1,6,2,35,1,11
	.byte	'T6OTL',0,1
	.word	521
	.byte	1,5,2,35,1,11
	.byte	'BPS2',0,1
	.word	521
	.byte	2,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	521
	.byte	1,2,2,35,1,11
	.byte	'T6CLR',0,1
	.word	521
	.byte	1,1,2,35,1,11
	.byte	'T6SR',0,1
	.word	521
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1072
	.byte	16,0,2,35,2,0,12,12,184,3,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12483
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GPT12_CAPREL_Bits',0,12,88,16,4,11
	.byte	'CAPREL',0,2
	.word	1072
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	1072
	.byte	16,0,2,35,2,0,12,12,176,2,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12758
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GPT12_T2_Bits',0,12,162,1,16,4,11
	.byte	'T2',0,2
	.word	1072
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	1072
	.byte	16,0,2,35,2,0,12,12,240,2,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12868
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GPT12_T3_Bits',0,12,186,1,16,4,11
	.byte	'T3',0,2
	.word	1072
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	1072
	.byte	16,0,2,35,2,0,12,12,128,3,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12971
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GPT12_T4_Bits',0,12,210,1,16,4,11
	.byte	'T4',0,2
	.word	1072
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	1072
	.byte	16,0,2,35,2,0,12,12,144,3,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13074
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GPT12_T5_Bits',0,12,235,1,16,4,11
	.byte	'T5',0,2
	.word	1072
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	1072
	.byte	16,0,2,35,2,0,12,12,160,3,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13177
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GPT12_T6_Bits',0,12,131,2,16,4,11
	.byte	'T6',0,2
	.word	1072
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	1072
	.byte	16,0,2,35,2,0,12,12,176,3,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13280
	.byte	4,2,35,0,0,18,160,1
	.word	521
	.byte	19,159,1,0,10
	.byte	'_Ifx_GPT12_OCS_Bits',0,12,135,1,16,4,11
	.byte	'reserved_0',0,4
	.word	498
	.byte	24,8,2,35,0,11
	.byte	'SUS',0,1
	.word	521
	.byte	4,4,2,35,3,11
	.byte	'SUS_P',0,1
	.word	521
	.byte	1,3,2,35,3,11
	.byte	'SUSSTA',0,1
	.word	521
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	521
	.byte	2,0,2,35,3,0,12,12,224,2,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13394
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GPT12_KRSTCLR_Bits',0,12,128,1,16,4,11
	.byte	'CLR',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	498
	.byte	31,0,2,35,0,0,12,12,216,2,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13556
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GPT12_KRST1_Bits',0,12,121,16,4,11
	.byte	'RST',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	498
	.byte	31,0,2,35,0,0,12,12,208,2,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13664
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GPT12_KRST0_Bits',0,12,113,16,4,11
	.byte	'RST',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'RSTSTAT',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	498
	.byte	30,0,2,35,0,0,12,12,200,2,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13769
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GPT12_ACCEN1_Bits',0,12,82,16,4,11
	.byte	'reserved_0',0,4
	.word	498
	.byte	32,0,2,35,0,0,12,12,168,2,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13893
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GPT12_ACCEN0_Bits',0,12,45,16,4,11
	.byte	'EN0',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	521
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	521
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	521
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	521
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	521
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	521
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	521
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	521
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	521
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	521
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	521
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	521
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	521
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	521
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	521
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	521
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	521
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	521
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	521
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	521
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	521
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	521
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	521
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	521
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	521
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	521
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	521
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	521
	.byte	1,0,2,35,3,0,12,12,160,2,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13984
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GPT12',0,12,200,3,25,128,2,13
	.byte	'CLC',0
	.word	10898
	.byte	4,2,35,0,13
	.byte	'PISEL',0
	.word	11194
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	11317
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	3259
	.byte	4,2,35,12,13
	.byte	'T2CON',0
	.word	11600
	.byte	4,2,35,16,13
	.byte	'T3CON',0
	.word	11874
	.byte	4,2,35,20,13
	.byte	'T4CON',0
	.word	12172
	.byte	4,2,35,24,13
	.byte	'T5CON',0
	.word	12443
	.byte	4,2,35,28,13
	.byte	'T6CON',0
	.word	12718
	.byte	4,2,35,32,13
	.byte	'reserved_24',0
	.word	5418
	.byte	12,2,35,36,13
	.byte	'CAPREL',0
	.word	12828
	.byte	4,2,35,48,13
	.byte	'T2',0
	.word	12931
	.byte	4,2,35,52,13
	.byte	'T3',0
	.word	13034
	.byte	4,2,35,56,13
	.byte	'T4',0
	.word	13137
	.byte	4,2,35,60,13
	.byte	'T5',0
	.word	13240
	.byte	4,2,35,64,13
	.byte	'T6',0
	.word	13343
	.byte	4,2,35,68,13
	.byte	'reserved_48',0
	.word	13383
	.byte	160,1,2,35,72,13
	.byte	'OCS',0
	.word	13516
	.byte	4,3,35,232,1,13
	.byte	'KRSTCLR',0
	.word	13624
	.byte	4,3,35,236,1,13
	.byte	'KRST1',0
	.word	13729
	.byte	4,3,35,240,1,13
	.byte	'KRST0',0
	.word	13853
	.byte	4,3,35,244,1,13
	.byte	'ACCEN1',0
	.word	13944
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	14515
	.byte	4,3,35,252,1,0,14
	.word	14555
.L217:
	.byte	3
	.word	14926
	.byte	15,11,134,1,9,1,16
	.byte	'IfxGpt12_EudInput_A',0,0,16
	.byte	'IfxGpt12_EudInput_B',0,1,16
	.byte	'IfxGpt12_EudInput_C',0,2,16
	.byte	'IfxGpt12_EudInput_D',0,3,0
.L239:
	.byte	4
	.byte	'IfxGpt12_T2_setEudInput',0,3,11,218,8,17,1,1
.L242:
	.byte	5
	.byte	'gpt12',0,11,218,8,52
	.word	14931
.L244:
	.byte	5
	.byte	'input',0,11,218,8,77
	.word	14936
.L246:
	.byte	6,0,15,11,178,1,9,1,16
	.byte	'IfxGpt12_Input_A',0,0,16
	.byte	'IfxGpt12_Input_B',0,1,16
	.byte	'IfxGpt12_Input_C',0,2,16
	.byte	'IfxGpt12_Input_D',0,3,0
.L288:
	.byte	4
	.byte	'IfxGpt12_T2_setInput',0,3,11,231,8,17,1,1
.L291:
	.byte	5
	.byte	'gpt12',0,11,231,8,49
	.word	14931
.L293:
	.byte	5
	.byte	'input',0,11,231,8,71
	.word	15095
.L295:
	.byte	6,0
.L247:
	.byte	4
	.byte	'IfxGpt12_T3_setEudInput',0,3,11,196,9,17,1,1
.L250:
	.byte	5
	.byte	'gpt12',0,11,196,9,52
	.word	14931
.L252:
	.byte	5
	.byte	'input',0,11,196,9,77
	.word	14936
.L254:
	.byte	6,0
.L296:
	.byte	4
	.byte	'IfxGpt12_T3_setInput',0,3,11,208,9,17,1,1
.L299:
	.byte	5
	.byte	'gpt12',0,11,208,9,49
	.word	14931
.L301:
	.byte	5
	.byte	'input',0,11,208,9,71
	.word	15095
.L303:
	.byte	6,0
.L255:
	.byte	4
	.byte	'IfxGpt12_T4_setEudInput',0,3,11,166,10,17,1,1
.L258:
	.byte	5
	.byte	'gpt12',0,11,166,10,52
	.word	14931
.L260:
	.byte	5
	.byte	'input',0,11,166,10,77
	.word	14936
.L262:
	.byte	6,0
.L304:
	.byte	4
	.byte	'IfxGpt12_T4_setInput',0,3,11,178,10,17,1,1
.L307:
	.byte	5
	.byte	'gpt12',0,11,178,10,49
	.word	14931
.L309:
	.byte	5
	.byte	'input',0,11,178,10,71
	.word	15095
.L311:
	.byte	6,0
.L263:
	.byte	4
	.byte	'IfxGpt12_T5_setEudInput',0,3,11,159,11,17,1,1
.L266:
	.byte	5
	.byte	'gpt12',0,11,159,11,52
	.word	14931
.L268:
	.byte	5
	.byte	'input',0,11,159,11,77
	.word	14936
.L270:
	.byte	6,0
.L312:
	.byte	4
	.byte	'IfxGpt12_T5_setInput',0,3,11,166,11,17,1,1
.L315:
	.byte	5
	.byte	'gpt12',0,11,166,11,49
	.word	14931
.L317:
	.byte	5
	.byte	'input',0,11,166,11,71
	.word	15095
.L319:
	.byte	6,0
.L271:
	.byte	4
	.byte	'IfxGpt12_T6_setEudInput',0,3,11,248,11,17,1,1
.L274:
	.byte	5
	.byte	'gpt12',0,11,248,11,52
	.word	14931
.L276:
	.byte	5
	.byte	'input',0,11,248,11,77
	.word	14936
.L278:
	.byte	6,0
.L320:
	.byte	4
	.byte	'IfxGpt12_T6_setInput',0,3,11,255,11,17,1,1
.L323:
	.byte	5
	.byte	'gpt12',0,11,255,11,49
	.word	14931
.L325:
	.byte	5
	.byte	'input',0,11,255,11,71
	.word	15095
.L327:
	.byte	6,0,15,11,79,9,1,16
	.byte	'IfxGpt12_CaptureInput_A',0,0,16
	.byte	'IfxGpt12_CaptureInput_B',0,1,16
	.byte	'IfxGpt12_CaptureInput_C',0,2,16
	.byte	'IfxGpt12_CaptureInput_D',0,3,0,4
	.byte	'IfxGpt12_setCaptureInput',0,3,11,213,12,17,1,1,5
	.byte	'gpt12',0,11,213,12,53
	.word	14931
	.byte	5
	.byte	'input',0,11,213,12,82
	.word	15739
	.byte	6,0
.L332:
	.byte	15,11,189,1,9,1,16
	.byte	'IfxGpt12_Mode_timer',0,0,16
	.byte	'IfxGpt12_Mode_counter',0,1,16
	.byte	'IfxGpt12_Mode_lowGatedTimer',0,2,16
	.byte	'IfxGpt12_Mode_highGatedTimer',0,3,16
	.byte	'IfxGpt12_Mode_reload',0,4,16
	.byte	'IfxGpt12_Mode_capture',0,5,16
	.byte	'IfxGpt12_Mode_incrementalInterfaceRotationDetection',0,6,16
	.byte	'IfxGpt12_Mode_incrementalInterfaceEdgeDetection',0,7,0
.L343:
	.byte	8
	.byte	'IfxGpt12_T2_getMode',0,3,11,175,8,26
	.word	15914
	.byte	1,1
.L344:
	.byte	5
	.byte	'gpt12',0,11,175,8,57
	.word	14931
.L346:
	.byte	6,0
.L338:
	.byte	8
	.byte	'IfxGpt12_getModuleFrequency',0,3,11,172,12,20
	.word	294
	.byte	1,1
.L340:
	.byte	5
	.byte	'gpt12',0,11,172,12,59
	.word	14931
.L342:
	.byte	6,0,20
	.word	242
	.byte	21
	.word	268
	.byte	6,0,20
	.word	303
	.byte	21
	.word	335
	.byte	6,0,20
	.word	348
	.byte	6,0,20
	.word	417
	.byte	21
	.word	436
	.byte	6,0,20
	.word	452
	.byte	21
	.word	467
	.byte	21
	.word	481
	.byte	6,0,20
	.word	877
	.byte	21
	.word	905
	.byte	6,0,20
	.word	1642
	.byte	21
	.word	1682
	.byte	21
	.word	1700
	.byte	6,0,20
	.word	1720
	.byte	21
	.word	1758
	.byte	21
	.word	1776
	.byte	6,0,22
	.byte	'IfxScuWdt_clearCpuEndinit',0,5,217,1,17,1,1,1,1,5
	.byte	'password',0,5,217,1,50
	.word	1072
	.byte	0,22
	.byte	'IfxScuWdt_setCpuEndinit',0,5,239,1,17,1,1,1,1,5
	.byte	'password',0,5,239,1,48
	.word	1072
	.byte	0,20
	.word	1796
	.byte	21
	.word	1847
	.byte	6,0,23
	.byte	'IfxScuWdt_getCpuWatchdogPassword',0,5,129,3,19
	.word	1072
	.byte	1,1,1,1,20
	.word	1946
	.byte	6,0,20
	.word	1980
	.byte	6,0,20
	.word	2043
	.byte	21
	.word	2084
	.byte	6,0,20
	.word	2103
	.byte	21
	.word	2158
	.byte	6,0,20
	.word	2177
	.byte	21
	.word	2217
	.byte	21
	.word	2234
	.byte	17,6,0,0,20
	.word	10136
	.byte	21
	.word	10168
	.byte	21
	.word	10182
	.byte	21
	.word	10200
	.byte	6,0,20
	.word	10503
	.byte	21
	.word	10536
	.byte	21
	.word	10550
	.byte	21
	.word	10568
	.byte	21
	.word	10582
	.byte	6,0,20
	.word	10702
	.byte	21
	.word	10730
	.byte	21
	.word	10744
	.byte	21
	.word	10762
	.byte	6,0,15,9,95,9,1,16
	.byte	'IfxPort_Mode_inputNoPullDevice',0,0,16
	.byte	'IfxPort_Mode_inputPullDown',0,8,16
	.byte	'IfxPort_Mode_inputPullUp',0,16,16
	.byte	'IfxPort_Mode_outputPushPullGeneral',0,128,1,16
	.byte	'IfxPort_Mode_outputPushPullAlt1',0,136,1,16
	.byte	'IfxPort_Mode_outputPushPullAlt2',0,144,1,16
	.byte	'IfxPort_Mode_outputPushPullAlt3',0,152,1,16
	.byte	'IfxPort_Mode_outputPushPullAlt4',0,160,1,16
	.byte	'IfxPort_Mode_outputPushPullAlt5',0,168,1,16
	.byte	'IfxPort_Mode_outputPushPullAlt6',0,176,1,16
	.byte	'IfxPort_Mode_outputPushPullAlt7',0,184,1,16
	.byte	'IfxPort_Mode_outputOpenDrainGeneral',0,192,1,16
	.byte	'IfxPort_Mode_outputOpenDrainAlt1',0,200,1,16
	.byte	'IfxPort_Mode_outputOpenDrainAlt2',0,208,1,16
	.byte	'IfxPort_Mode_outputOpenDrainAlt3',0,216,1,16
	.byte	'IfxPort_Mode_outputOpenDrainAlt4',0,224,1,16
	.byte	'IfxPort_Mode_outputOpenDrainAlt5',0,232,1,16
	.byte	'IfxPort_Mode_outputOpenDrainAlt6',0,240,1,16
	.byte	'IfxPort_Mode_outputOpenDrainAlt7',0,248,1,0,22
	.byte	'IfxPort_setPinMode',0,9,247,2,17,1,1,1,1,5
	.byte	'port',0,9,247,2,43
	.word	10006
	.byte	5
	.byte	'pinIndex',0,9,247,2,55
	.word	521
	.byte	5
	.byte	'mode',0,9,247,2,78
	.word	16683
	.byte	0
.L386:
	.byte	15,9,144,1,9,1,16
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed1',0,0,16
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed2',0,1,16
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed3',0,2,16
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed4',0,3,16
	.byte	'IfxPort_PadDriver_lvdsSpeed1',0,4,16
	.byte	'IfxPort_PadDriver_lvdsSpeed2',0,5,16
	.byte	'IfxPort_PadDriver_lvdsSpeed3',0,6,16
	.byte	'IfxPort_PadDriver_lvdsSpeed4',0,7,16
	.byte	'IfxPort_PadDriver_ttlSpeed1',0,8,16
	.byte	'IfxPort_PadDriver_ttlSpeed2',0,9,16
	.byte	'IfxPort_PadDriver_ttlSpeed3',0,10,16
	.byte	'IfxPort_PadDriver_ttlSpeed4',0,11,0,22
	.byte	'IfxPort_setPinPadDriver',0,9,134,3,17,1,1,1,1,5
	.byte	'port',0,9,134,3,48
	.word	10006
	.byte	5
	.byte	'pinIndex',0,9,134,3,60
	.word	521
	.byte	5
	.byte	'padDriver',0,9,134,3,88
	.word	17427
	.byte	0,23
	.byte	'IfxScuCcu_getSpbFrequency',0,13,179,7,20
	.word	294
	.byte	1,1,1,1,20
	.word	15031
	.byte	21
	.word	15063
	.byte	21
	.word	15078
	.byte	6,0,20
	.word	15178
	.byte	21
	.word	15207
	.byte	21
	.word	15222
	.byte	6,0,20
	.word	15239
	.byte	21
	.word	15271
	.byte	21
	.word	15286
	.byte	6,0,20
	.word	15303
	.byte	21
	.word	15332
	.byte	21
	.word	15347
	.byte	6,0,20
	.word	15364
	.byte	21
	.word	15396
	.byte	21
	.word	15411
	.byte	6,0,20
	.word	15428
	.byte	21
	.word	15457
	.byte	21
	.word	15472
	.byte	6,0,20
	.word	15489
	.byte	21
	.word	15521
	.byte	21
	.word	15536
	.byte	6,0,20
	.word	15553
	.byte	21
	.word	15582
	.byte	21
	.word	15597
	.byte	6,0,20
	.word	15614
	.byte	21
	.word	15646
	.byte	21
	.word	15661
	.byte	6,0,20
	.word	15678
	.byte	21
	.word	15707
	.byte	21
	.word	15722
	.byte	6,0,20
	.word	15849
	.byte	21
	.word	15882
	.byte	21
	.word	15897
	.byte	6,0,24,9,190,1,9,8,13
	.byte	'port',0
	.word	10006
	.byte	4,2,35,0,13
	.byte	'pinIndex',0
	.word	521
	.byte	1,2,35,4,0,15,15,130,1,9,1,16
	.byte	'Ifx_RxSel_a',0,0,16
	.byte	'Ifx_RxSel_b',0,1,16
	.byte	'Ifx_RxSel_c',0,2,16
	.byte	'Ifx_RxSel_d',0,3,16
	.byte	'Ifx_RxSel_e',0,4,16
	.byte	'Ifx_RxSel_f',0,5,16
	.byte	'Ifx_RxSel_g',0,6,16
	.byte	'Ifx_RxSel_h',0,7,0,24,14,67,15,20,13
	.byte	'module',0
	.word	14931
	.byte	4,2,35,0,13
	.byte	'timer',0
	.word	521
	.byte	1,2,35,4,13
	.byte	'pin',0
	.word	18153
	.byte	8,2,35,8,13
	.byte	'select',0
	.word	18192
	.byte	1,2,35,16,0,25
	.word	18311
.L225:
	.byte	3
	.word	18377
	.byte	24,14,76,15,20,13
	.byte	'module',0
	.word	14931
	.byte	4,2,35,0,13
	.byte	'timer',0
	.word	521
	.byte	1,2,35,4,13
	.byte	'pin',0
	.word	18153
	.byte	8,2,35,8,13
	.byte	'select',0
	.word	18192
	.byte	1,2,35,16,0,25
	.word	18387
.L280:
	.byte	3
	.word	18453
	.byte	20
	.word	16179
	.byte	21
	.word	16211
	.byte	6,0,20
	.word	16228
	.byte	21
	.word	16268
	.byte	6,0
.L334:
	.byte	15,11,254,1,9,1,16
	.byte	'IfxGpt12_TimerInputPrescaler_1',0,0,16
	.byte	'IfxGpt12_TimerInputPrescaler_2',0,1,16
	.byte	'IfxGpt12_TimerInputPrescaler_4',0,2,16
	.byte	'IfxGpt12_TimerInputPrescaler_8',0,3,16
	.byte	'IfxGpt12_TimerInputPrescaler_16',0,4,16
	.byte	'IfxGpt12_TimerInputPrescaler_32',0,5,16
	.byte	'IfxGpt12_TimerInputPrescaler_64',0,6,16
	.byte	'IfxGpt12_TimerInputPrescaler_128',0,7,0
.L336:
	.byte	15,11,145,1,9,1,16
	.byte	'IfxGpt12_Gpt1BlockPrescaler_8',0,0,16
	.byte	'IfxGpt12_Gpt1BlockPrescaler_4',0,1,16
	.byte	'IfxGpt12_Gpt1BlockPrescaler_32',0,2,16
	.byte	'IfxGpt12_Gpt1BlockPrescaler_16',0,3,0
.L368:
	.byte	15,11,156,1,9,1,16
	.byte	'IfxGpt12_Gpt2BlockPrescaler_4',0,0,16
	.byte	'IfxGpt12_Gpt2BlockPrescaler_2',0,1,16
	.byte	'IfxGpt12_Gpt2BlockPrescaler_16',0,2,16
	.byte	'IfxGpt12_Gpt2BlockPrescaler_8',0,3,0,7
	.byte	'short int',0,2,5,26
	.byte	'__wchar_t',0,16,1,1
	.word	19036
	.byte	26
	.byte	'__size_t',0,16,1,1
	.word	498
	.byte	26
	.byte	'__ptrdiff_t',0,16,1,1
	.word	514
	.byte	27,1,3
	.word	19104
	.byte	26
	.byte	'__codeptr',0,16,1,1
	.word	19106
	.byte	15,17,69,9,1,16
	.byte	'IfxSrc_Tos_cpu0',0,0,16
	.byte	'IfxSrc_Tos_cpu1',0,1,16
	.byte	'IfxSrc_Tos_dma',0,3,0,26
	.byte	'IfxSrc_Tos',0,17,74,3
	.word	19129
	.byte	26
	.byte	'boolean',0,18,101,29
	.word	521
	.byte	26
	.byte	'uint8',0,18,105,29
	.word	521
	.byte	26
	.byte	'uint16',0,18,109,29
	.word	1072
	.byte	26
	.byte	'uint32',0,18,113,29
	.word	2022
	.byte	26
	.byte	'uint64',0,18,118,29
	.word	380
	.byte	26
	.byte	'sint16',0,18,126,29
	.word	19036
	.byte	7
	.byte	'long int',0,4,5,26
	.byte	'sint32',0,18,131,1,29
	.word	19297
	.byte	7
	.byte	'long long int',0,8,5,26
	.byte	'sint64',0,18,138,1,29
	.word	19325
	.byte	26
	.byte	'float32',0,18,167,1,29
	.word	294
	.byte	26
	.byte	'pvoid',0,15,57,28
	.word	412
	.byte	26
	.byte	'Ifx_TickTime',0,15,79,28
	.word	19325
	.byte	26
	.byte	'Ifx_Priority',0,15,103,16
	.word	1072
	.byte	26
	.byte	'Ifx_RxSel',0,15,140,1,3
	.word	18192
	.byte	26
	.byte	'Ifx_SRC_SRCR_Bits',0,4,62,3
	.word	538
	.byte	26
	.byte	'Ifx_SRC_SRCR',0,4,75,3
	.word	828
	.byte	10
	.byte	'_Ifx_SRC_AGBT',0,4,86,25,4,13
	.byte	'SR',0
	.word	828
	.byte	4,2,35,0,0,14
	.word	19497
	.byte	26
	.byte	'Ifx_SRC_AGBT',0,4,89,3
	.word	19529
	.byte	10
	.byte	'_Ifx_SRC_ASCLIN',0,4,92,25,12,13
	.byte	'TX',0
	.word	828
	.byte	4,2,35,0,13
	.byte	'RX',0
	.word	828
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	828
	.byte	4,2,35,8,0,14
	.word	19555
	.byte	26
	.byte	'Ifx_SRC_ASCLIN',0,4,97,3
	.word	19614
	.byte	10
	.byte	'_Ifx_SRC_BCUSPB',0,4,100,25,4,13
	.byte	'SBSRC',0
	.word	828
	.byte	4,2,35,0,0,14
	.word	19642
	.byte	26
	.byte	'Ifx_SRC_BCUSPB',0,4,103,3
	.word	19679
	.byte	18,64
	.word	828
	.byte	19,15,0,10
	.byte	'_Ifx_SRC_CAN',0,4,106,25,64,13
	.byte	'INT',0
	.word	19707
	.byte	64,2,35,0,0,14
	.word	19716
	.byte	26
	.byte	'Ifx_SRC_CAN',0,4,109,3
	.word	19748
	.byte	10
	.byte	'_Ifx_SRC_CCU6',0,4,112,25,16,13
	.byte	'SR0',0
	.word	828
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	828
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	828
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	828
	.byte	4,2,35,12,0,14
	.word	19773
	.byte	26
	.byte	'Ifx_SRC_CCU6',0,4,118,3
	.word	19845
	.byte	18,8
	.word	828
	.byte	19,1,0,10
	.byte	'_Ifx_SRC_CERBERUS',0,4,121,25,8,13
	.byte	'SR',0
	.word	19871
	.byte	8,2,35,0,0,14
	.word	19880
	.byte	26
	.byte	'Ifx_SRC_CERBERUS',0,4,124,3
	.word	19916
	.byte	10
	.byte	'_Ifx_SRC_CIF',0,4,127,25,16,13
	.byte	'MI',0
	.word	828
	.byte	4,2,35,0,13
	.byte	'MIEP',0
	.word	828
	.byte	4,2,35,4,13
	.byte	'ISP',0
	.word	828
	.byte	4,2,35,8,13
	.byte	'MJPEG',0
	.word	828
	.byte	4,2,35,12,0,14
	.word	19946
	.byte	26
	.byte	'Ifx_SRC_CIF',0,4,133,1,3
	.word	20019
	.byte	10
	.byte	'_Ifx_SRC_CPU',0,4,136,1,25,4,13
	.byte	'SBSRC',0
	.word	828
	.byte	4,2,35,0,0,14
	.word	20045
	.byte	26
	.byte	'Ifx_SRC_CPU',0,4,139,1,3
	.word	20080
	.byte	18,192,1
	.word	828
	.byte	19,47,0,10
	.byte	'_Ifx_SRC_DMA',0,4,142,1,25,208,1,13
	.byte	'ERR',0
	.word	828
	.byte	4,2,35,0,13
	.byte	'reserved_4',0
	.word	5418
	.byte	12,2,35,4,13
	.byte	'CH',0
	.word	20106
	.byte	192,1,2,35,16,0,14
	.word	20116
	.byte	26
	.byte	'Ifx_SRC_DMA',0,4,147,1,3
	.word	20183
	.byte	10
	.byte	'_Ifx_SRC_DSADC',0,4,150,1,25,8,13
	.byte	'SRM',0
	.word	828
	.byte	4,2,35,0,13
	.byte	'SRA',0
	.word	828
	.byte	4,2,35,4,0,14
	.word	20209
	.byte	26
	.byte	'Ifx_SRC_DSADC',0,4,154,1,3
	.word	20257
	.byte	10
	.byte	'_Ifx_SRC_EMEM',0,4,157,1,25,4,13
	.byte	'SR',0
	.word	828
	.byte	4,2,35,0,0,14
	.word	20285
	.byte	26
	.byte	'Ifx_SRC_EMEM',0,4,160,1,3
	.word	20318
	.byte	18,40
	.word	521
	.byte	19,39,0,10
	.byte	'_Ifx_SRC_ERAY',0,4,163,1,25,80,13
	.byte	'INT',0
	.word	19871
	.byte	8,2,35,0,13
	.byte	'TINT',0
	.word	19871
	.byte	8,2,35,8,13
	.byte	'NDAT',0
	.word	19871
	.byte	8,2,35,16,13
	.byte	'MBSC',0
	.word	19871
	.byte	8,2,35,24,13
	.byte	'OBUSY',0
	.word	828
	.byte	4,2,35,32,13
	.byte	'IBUSY',0
	.word	828
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	20345
	.byte	40,2,35,40,0,14
	.word	20354
	.byte	26
	.byte	'Ifx_SRC_ERAY',0,4,172,1,3
	.word	20481
	.byte	10
	.byte	'_Ifx_SRC_ETH',0,4,175,1,25,4,13
	.byte	'SR',0
	.word	828
	.byte	4,2,35,0,0,14
	.word	20508
	.byte	26
	.byte	'Ifx_SRC_ETH',0,4,178,1,3
	.word	20540
	.byte	10
	.byte	'_Ifx_SRC_FCE',0,4,181,1,25,4,13
	.byte	'SR',0
	.word	828
	.byte	4,2,35,0,0,14
	.word	20566
	.byte	26
	.byte	'Ifx_SRC_FCE',0,4,184,1,3
	.word	20598
	.byte	10
	.byte	'_Ifx_SRC_FFT',0,4,187,1,25,12,13
	.byte	'DONE',0
	.word	828
	.byte	4,2,35,0,13
	.byte	'ERR',0
	.word	828
	.byte	4,2,35,4,13
	.byte	'RFS',0
	.word	828
	.byte	4,2,35,8,0,14
	.word	20624
	.byte	26
	.byte	'Ifx_SRC_FFT',0,4,192,1,3
	.word	20684
	.byte	18,16
	.word	521
	.byte	19,15,0,10
	.byte	'_Ifx_SRC_GPSR',0,4,195,1,25,32,13
	.byte	'SR0',0
	.word	828
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	828
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	828
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	828
	.byte	4,2,35,12,13
	.byte	'reserved_10',0
	.word	20710
	.byte	16,2,35,16,0,14
	.word	20719
	.byte	26
	.byte	'Ifx_SRC_GPSR',0,4,202,1,3
	.word	20813
	.byte	10
	.byte	'_Ifx_SRC_GPT12',0,4,205,1,25,48,13
	.byte	'CIRQ',0
	.word	828
	.byte	4,2,35,0,13
	.byte	'T2',0
	.word	828
	.byte	4,2,35,4,13
	.byte	'T3',0
	.word	828
	.byte	4,2,35,8,13
	.byte	'T4',0
	.word	828
	.byte	4,2,35,12,13
	.byte	'T5',0
	.word	828
	.byte	4,2,35,16,13
	.byte	'T6',0
	.word	828
	.byte	4,2,35,20,13
	.byte	'reserved_18',0
	.word	4449
	.byte	24,2,35,24,0,14
	.word	20840
	.byte	26
	.byte	'Ifx_SRC_GPT12',0,4,214,1,3
	.word	20957
	.byte	18,12
	.word	828
	.byte	19,2,0,18,32
	.word	828
	.byte	19,7,0,18,32
	.word	20994
	.byte	19,0,0,18,88
	.word	521
	.byte	19,87,0,18,108
	.word	828
	.byte	19,26,0,18,96
	.word	521
	.byte	19,95,0,18,96
	.word	20994
	.byte	19,2,0,18,160,3
	.word	521
	.byte	19,159,3,0,18,64
	.word	20994
	.byte	19,1,0,18,192,3
	.word	521
	.byte	19,191,3,0,18,16
	.word	828
	.byte	19,3,0,18,64
	.word	21079
	.byte	19,3,0,18,192,2
	.word	521
	.byte	19,191,2,0,18,52
	.word	521
	.byte	19,51,0,10
	.byte	'_Ifx_SRC_GTM',0,4,217,1,25,204,18,13
	.byte	'AEIIRQ',0
	.word	828
	.byte	4,2,35,0,13
	.byte	'ARUIRQ',0
	.word	20985
	.byte	12,2,35,4,13
	.byte	'reserved_10',0
	.word	3259
	.byte	4,2,35,16,13
	.byte	'BRCIRQ',0
	.word	828
	.byte	4,2,35,20,13
	.byte	'CMPIRQ',0
	.word	828
	.byte	4,2,35,24,13
	.byte	'SPEIRQ',0
	.word	19871
	.byte	8,2,35,28,13
	.byte	'reserved_24',0
	.word	5078
	.byte	8,2,35,36,13
	.byte	'PSM',0
	.word	21003
	.byte	32,2,35,44,13
	.byte	'reserved_4C',0
	.word	21012
	.byte	88,2,35,76,13
	.byte	'DPLL',0
	.word	21021
	.byte	108,3,35,164,1,13
	.byte	'reserved_110',0
	.word	21030
	.byte	96,3,35,144,2,13
	.byte	'ERR',0
	.word	828
	.byte	4,3,35,240,2,13
	.byte	'reserved_174',0
	.word	5418
	.byte	12,3,35,244,2,13
	.byte	'TIM',0
	.word	21039
	.byte	96,3,35,128,3,13
	.byte	'reserved_1E0',0
	.word	21048
	.byte	160,3,3,35,224,3,13
	.byte	'MCS',0
	.word	21039
	.byte	96,3,35,128,7,13
	.byte	'reserved_3E0',0
	.word	21048
	.byte	160,3,3,35,224,7,13
	.byte	'TOM',0
	.word	21059
	.byte	64,3,35,128,11,13
	.byte	'reserved_5C0',0
	.word	21068
	.byte	192,3,3,35,192,11,13
	.byte	'ATOM',0
	.word	21088
	.byte	64,3,35,128,15,13
	.byte	'reserved_7C0',0
	.word	21097
	.byte	192,2,3,35,192,15,13
	.byte	'MCSW0',0
	.word	20985
	.byte	12,3,35,128,18,13
	.byte	'reserved_90C',0
	.word	21108
	.byte	52,3,35,140,18,13
	.byte	'MCSW1',0
	.word	20985
	.byte	12,3,35,192,18,0,14
	.word	21117
	.byte	26
	.byte	'Ifx_SRC_GTM',0,4,243,1,3
	.word	21577
	.byte	10
	.byte	'_Ifx_SRC_HSCT',0,4,246,1,25,4,13
	.byte	'SR',0
	.word	828
	.byte	4,2,35,0,0,14
	.word	21603
	.byte	26
	.byte	'Ifx_SRC_HSCT',0,4,249,1,3
	.word	21636
	.byte	10
	.byte	'_Ifx_SRC_HSSL',0,4,252,1,25,16,13
	.byte	'COK',0
	.word	828
	.byte	4,2,35,0,13
	.byte	'RDI',0
	.word	828
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	828
	.byte	4,2,35,8,13
	.byte	'TRG',0
	.word	828
	.byte	4,2,35,12,0,14
	.word	21663
	.byte	26
	.byte	'Ifx_SRC_HSSL',0,4,130,2,3
	.word	21736
	.byte	18,56
	.word	521
	.byte	19,55,0,10
	.byte	'_Ifx_SRC_I2C',0,4,133,2,25,80,13
	.byte	'BREQ',0
	.word	828
	.byte	4,2,35,0,13
	.byte	'LBREQ',0
	.word	828
	.byte	4,2,35,4,13
	.byte	'SREQ',0
	.word	828
	.byte	4,2,35,8,13
	.byte	'LSREQ',0
	.word	828
	.byte	4,2,35,12,13
	.byte	'ERR',0
	.word	828
	.byte	4,2,35,16,13
	.byte	'P',0
	.word	828
	.byte	4,2,35,20,13
	.byte	'reserved_18',0
	.word	21763
	.byte	56,2,35,24,0,14
	.word	21772
	.byte	26
	.byte	'Ifx_SRC_I2C',0,4,142,2,3
	.word	21895
	.byte	10
	.byte	'_Ifx_SRC_LMU',0,4,145,2,25,4,13
	.byte	'SR',0
	.word	828
	.byte	4,2,35,0,0,14
	.word	21921
	.byte	26
	.byte	'Ifx_SRC_LMU',0,4,148,2,3
	.word	21953
	.byte	10
	.byte	'_Ifx_SRC_MSC',0,4,151,2,25,20,13
	.byte	'SR0',0
	.word	828
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	828
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	828
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	828
	.byte	4,2,35,12,13
	.byte	'SR4',0
	.word	828
	.byte	4,2,35,16,0,14
	.word	21979
	.byte	26
	.byte	'Ifx_SRC_MSC',0,4,158,2,3
	.word	22064
	.byte	10
	.byte	'_Ifx_SRC_PMU',0,4,161,2,25,4,13
	.byte	'SR',0
	.word	828
	.byte	4,2,35,0,0,14
	.word	22090
	.byte	26
	.byte	'Ifx_SRC_PMU',0,4,164,2,3
	.word	22122
	.byte	10
	.byte	'_Ifx_SRC_PSI5',0,4,167,2,25,32,13
	.byte	'SR',0
	.word	20994
	.byte	32,2,35,0,0,14
	.word	22148
	.byte	26
	.byte	'Ifx_SRC_PSI5',0,4,170,2,3
	.word	22181
	.byte	10
	.byte	'_Ifx_SRC_PSI5S',0,4,173,2,25,32,13
	.byte	'SR',0
	.word	20994
	.byte	32,2,35,0,0,14
	.word	22208
	.byte	26
	.byte	'Ifx_SRC_PSI5S',0,4,176,2,3
	.word	22242
	.byte	10
	.byte	'_Ifx_SRC_QSPI',0,4,179,2,25,24,13
	.byte	'TX',0
	.word	828
	.byte	4,2,35,0,13
	.byte	'RX',0
	.word	828
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	828
	.byte	4,2,35,8,13
	.byte	'PT',0
	.word	828
	.byte	4,2,35,12,13
	.byte	'HC',0
	.word	828
	.byte	4,2,35,16,13
	.byte	'U',0
	.word	828
	.byte	4,2,35,20,0,14
	.word	22270
	.byte	26
	.byte	'Ifx_SRC_QSPI',0,4,187,2,3
	.word	22363
	.byte	10
	.byte	'_Ifx_SRC_SCR',0,4,190,2,25,4,13
	.byte	'SR',0
	.word	828
	.byte	4,2,35,0,0,14
	.word	22390
	.byte	26
	.byte	'Ifx_SRC_SCR',0,4,193,2,3
	.word	22422
	.byte	10
	.byte	'_Ifx_SRC_SCU',0,4,196,2,25,20,13
	.byte	'DTS',0
	.word	828
	.byte	4,2,35,0,13
	.byte	'ERU',0
	.word	21079
	.byte	16,2,35,4,0,14
	.word	22448
	.byte	26
	.byte	'Ifx_SRC_SCU',0,4,200,2,3
	.word	22494
	.byte	18,24
	.word	828
	.byte	19,5,0,10
	.byte	'_Ifx_SRC_SENT',0,4,203,2,25,24,13
	.byte	'SR',0
	.word	22520
	.byte	24,2,35,0,0,14
	.word	22529
	.byte	26
	.byte	'Ifx_SRC_SENT',0,4,206,2,3
	.word	22562
	.byte	10
	.byte	'_Ifx_SRC_SMU',0,4,209,2,25,12,13
	.byte	'SR',0
	.word	20985
	.byte	12,2,35,0,0,14
	.word	22589
	.byte	26
	.byte	'Ifx_SRC_SMU',0,4,212,2,3
	.word	22621
	.byte	10
	.byte	'_Ifx_SRC_STM',0,4,215,2,25,8,13
	.byte	'SR0',0
	.word	828
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	828
	.byte	4,2,35,4,0,14
	.word	22647
	.byte	26
	.byte	'Ifx_SRC_STM',0,4,219,2,3
	.word	22693
	.byte	10
	.byte	'_Ifx_SRC_VADCCG',0,4,222,2,25,16,13
	.byte	'SR0',0
	.word	828
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	828
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	828
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	828
	.byte	4,2,35,12,0,14
	.word	22719
	.byte	26
	.byte	'Ifx_SRC_VADCCG',0,4,228,2,3
	.word	22794
	.byte	10
	.byte	'_Ifx_SRC_VADCG',0,4,231,2,25,16,13
	.byte	'SR0',0
	.word	828
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	828
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	828
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	828
	.byte	4,2,35,12,0,14
	.word	22823
	.byte	26
	.byte	'Ifx_SRC_VADCG',0,4,237,2,3
	.word	22897
	.byte	10
	.byte	'_Ifx_SRC_XBAR',0,4,240,2,25,4,13
	.byte	'SRC',0
	.word	828
	.byte	4,2,35,0,0,14
	.word	22925
	.byte	26
	.byte	'Ifx_SRC_XBAR',0,4,243,2,3
	.word	22959
	.byte	18,4
	.word	19497
	.byte	19,0,0,14
	.word	22986
	.byte	10
	.byte	'_Ifx_SRC_GAGBT',0,4,128,3,25,4,13
	.byte	'AGBT',0
	.word	22995
	.byte	4,2,35,0,0,14
	.word	23000
	.byte	26
	.byte	'Ifx_SRC_GAGBT',0,4,131,3,3
	.word	23036
	.byte	18,48
	.word	19555
	.byte	19,3,0,14
	.word	23064
	.byte	10
	.byte	'_Ifx_SRC_GASCLIN',0,4,134,3,25,48,13
	.byte	'ASCLIN',0
	.word	23073
	.byte	48,2,35,0,0,14
	.word	23078
	.byte	26
	.byte	'Ifx_SRC_GASCLIN',0,4,137,3,3
	.word	23118
	.byte	14
	.word	19642
	.byte	10
	.byte	'_Ifx_SRC_GBCU',0,4,140,3,25,4,13
	.byte	'SPB',0
	.word	23148
	.byte	4,2,35,0,0,14
	.word	23153
	.byte	26
	.byte	'Ifx_SRC_GBCU',0,4,143,3,3
	.word	23187
	.byte	18,64
	.word	19716
	.byte	19,0,0,14
	.word	23214
	.byte	10
	.byte	'_Ifx_SRC_GCAN',0,4,146,3,25,64,13
	.byte	'CAN',0
	.word	23223
	.byte	64,2,35,0,0,14
	.word	23228
	.byte	26
	.byte	'Ifx_SRC_GCAN',0,4,149,3,3
	.word	23262
	.byte	18,32
	.word	19773
	.byte	19,1,0,14
	.word	23289
	.byte	10
	.byte	'_Ifx_SRC_GCCU6',0,4,152,3,25,32,13
	.byte	'CCU6',0
	.word	23298
	.byte	32,2,35,0,0,14
	.word	23303
	.byte	26
	.byte	'Ifx_SRC_GCCU6',0,4,155,3,3
	.word	23339
	.byte	14
	.word	19880
	.byte	10
	.byte	'_Ifx_SRC_GCERBERUS',0,4,158,3,25,8,13
	.byte	'CERBERUS',0
	.word	23367
	.byte	8,2,35,0,0,14
	.word	23372
	.byte	26
	.byte	'Ifx_SRC_GCERBERUS',0,4,161,3,3
	.word	23416
	.byte	18,16
	.word	19946
	.byte	19,0,0,14
	.word	23448
	.byte	10
	.byte	'_Ifx_SRC_GCIF',0,4,164,3,25,16,13
	.byte	'CIF',0
	.word	23457
	.byte	16,2,35,0,0,14
	.word	23462
	.byte	26
	.byte	'Ifx_SRC_GCIF',0,4,167,3,3
	.word	23496
	.byte	18,8
	.word	20045
	.byte	19,1,0,14
	.word	23523
	.byte	10
	.byte	'_Ifx_SRC_GCPU',0,4,170,3,25,8,13
	.byte	'CPU',0
	.word	23532
	.byte	8,2,35,0,0,14
	.word	23537
	.byte	26
	.byte	'Ifx_SRC_GCPU',0,4,173,3,3
	.word	23571
	.byte	18,208,1
	.word	20116
	.byte	19,0,0,14
	.word	23598
	.byte	10
	.byte	'_Ifx_SRC_GDMA',0,4,176,3,25,208,1,13
	.byte	'DMA',0
	.word	23608
	.byte	208,1,2,35,0,0,14
	.word	23613
	.byte	26
	.byte	'Ifx_SRC_GDMA',0,4,179,3,3
	.word	23649
	.byte	14
	.word	20209
	.byte	14
	.word	20209
	.byte	14
	.word	20209
	.byte	10
	.byte	'_Ifx_SRC_GDSADC',0,4,182,3,25,32,13
	.byte	'DSADC0',0
	.word	23676
	.byte	8,2,35,0,13
	.byte	'reserved_8',0
	.word	5078
	.byte	8,2,35,8,13
	.byte	'DSADC2',0
	.word	23681
	.byte	8,2,35,16,13
	.byte	'DSADC3',0
	.word	23686
	.byte	8,2,35,24,0,14
	.word	23691
	.byte	26
	.byte	'Ifx_SRC_GDSADC',0,4,188,3,3
	.word	23782
	.byte	18,4
	.word	20285
	.byte	19,0,0,14
	.word	23811
	.byte	10
	.byte	'_Ifx_SRC_GEMEM',0,4,191,3,25,4,13
	.byte	'EMEM',0
	.word	23820
	.byte	4,2,35,0,0,14
	.word	23825
	.byte	26
	.byte	'Ifx_SRC_GEMEM',0,4,194,3,3
	.word	23861
	.byte	18,80
	.word	20354
	.byte	19,0,0,14
	.word	23889
	.byte	10
	.byte	'_Ifx_SRC_GERAY',0,4,197,3,25,80,13
	.byte	'ERAY',0
	.word	23898
	.byte	80,2,35,0,0,14
	.word	23903
	.byte	26
	.byte	'Ifx_SRC_GERAY',0,4,200,3,3
	.word	23939
	.byte	18,4
	.word	20508
	.byte	19,0,0,14
	.word	23967
	.byte	10
	.byte	'_Ifx_SRC_GETH',0,4,203,3,25,4,13
	.byte	'ETH',0
	.word	23976
	.byte	4,2,35,0,0,14
	.word	23981
	.byte	26
	.byte	'Ifx_SRC_GETH',0,4,206,3,3
	.word	24015
	.byte	18,4
	.word	20566
	.byte	19,0,0,14
	.word	24042
	.byte	10
	.byte	'_Ifx_SRC_GFCE',0,4,209,3,25,4,13
	.byte	'FCE',0
	.word	24051
	.byte	4,2,35,0,0,14
	.word	24056
	.byte	26
	.byte	'Ifx_SRC_GFCE',0,4,212,3,3
	.word	24090
	.byte	18,12
	.word	20624
	.byte	19,0,0,14
	.word	24117
	.byte	10
	.byte	'_Ifx_SRC_GFFT',0,4,215,3,25,12,13
	.byte	'FFT',0
	.word	24126
	.byte	12,2,35,0,0,14
	.word	24131
	.byte	26
	.byte	'Ifx_SRC_GFFT',0,4,218,3,3
	.word	24165
	.byte	18,64
	.word	20719
	.byte	19,1,0,14
	.word	24192
	.byte	10
	.byte	'_Ifx_SRC_GGPSR',0,4,221,3,25,64,13
	.byte	'GPSR',0
	.word	24201
	.byte	64,2,35,0,0,14
	.word	24206
	.byte	26
	.byte	'Ifx_SRC_GGPSR',0,4,224,3,3
	.word	24242
	.byte	18,48
	.word	20840
	.byte	19,0,0,14
	.word	24270
	.byte	10
	.byte	'_Ifx_SRC_GGPT12',0,4,227,3,25,48,13
	.byte	'GPT12',0
	.word	24279
	.byte	48,2,35,0,0,14
	.word	24284
	.byte	26
	.byte	'Ifx_SRC_GGPT12',0,4,230,3,3
	.word	24322
	.byte	18,204,18
	.word	21117
	.byte	19,0,0,14
	.word	24351
	.byte	10
	.byte	'_Ifx_SRC_GGTM',0,4,233,3,25,204,18,13
	.byte	'GTM',0
	.word	24361
	.byte	204,18,2,35,0,0,14
	.word	24366
	.byte	26
	.byte	'Ifx_SRC_GGTM',0,4,236,3,3
	.word	24402
	.byte	18,4
	.word	21603
	.byte	19,0,0,14
	.word	24429
	.byte	10
	.byte	'_Ifx_SRC_GHSCT',0,4,239,3,25,4,13
	.byte	'HSCT',0
	.word	24438
	.byte	4,2,35,0,0,14
	.word	24443
	.byte	26
	.byte	'Ifx_SRC_GHSCT',0,4,242,3,3
	.word	24479
	.byte	18,64
	.word	21663
	.byte	19,3,0,14
	.word	24507
	.byte	10
	.byte	'_Ifx_SRC_GHSSL',0,4,245,3,25,68,13
	.byte	'HSSL',0
	.word	24516
	.byte	64,2,35,0,13
	.byte	'EXI',0
	.word	828
	.byte	4,2,35,64,0,14
	.word	24521
	.byte	26
	.byte	'Ifx_SRC_GHSSL',0,4,249,3,3
	.word	24570
	.byte	18,80
	.word	21772
	.byte	19,0,0,14
	.word	24598
	.byte	10
	.byte	'_Ifx_SRC_GI2C',0,4,252,3,25,80,13
	.byte	'I2C',0
	.word	24607
	.byte	80,2,35,0,0,14
	.word	24612
	.byte	26
	.byte	'Ifx_SRC_GI2C',0,4,255,3,3
	.word	24646
	.byte	18,4
	.word	21921
	.byte	19,0,0,14
	.word	24673
	.byte	10
	.byte	'_Ifx_SRC_GLMU',0,4,130,4,25,4,13
	.byte	'LMU',0
	.word	24682
	.byte	4,2,35,0,0,14
	.word	24687
	.byte	26
	.byte	'Ifx_SRC_GLMU',0,4,133,4,3
	.word	24721
	.byte	18,40
	.word	21979
	.byte	19,1,0,14
	.word	24748
	.byte	10
	.byte	'_Ifx_SRC_GMSC',0,4,136,4,25,40,13
	.byte	'MSC',0
	.word	24757
	.byte	40,2,35,0,0,14
	.word	24762
	.byte	26
	.byte	'Ifx_SRC_GMSC',0,4,139,4,3
	.word	24796
	.byte	18,8
	.word	22090
	.byte	19,1,0,14
	.word	24823
	.byte	10
	.byte	'_Ifx_SRC_GPMU',0,4,142,4,25,8,13
	.byte	'PMU',0
	.word	24832
	.byte	8,2,35,0,0,14
	.word	24837
	.byte	26
	.byte	'Ifx_SRC_GPMU',0,4,145,4,3
	.word	24871
	.byte	18,32
	.word	22148
	.byte	19,0,0,14
	.word	24898
	.byte	10
	.byte	'_Ifx_SRC_GPSI5',0,4,148,4,25,32,13
	.byte	'PSI5',0
	.word	24907
	.byte	32,2,35,0,0,14
	.word	24912
	.byte	26
	.byte	'Ifx_SRC_GPSI5',0,4,151,4,3
	.word	24948
	.byte	18,32
	.word	22208
	.byte	19,0,0,14
	.word	24976
	.byte	10
	.byte	'_Ifx_SRC_GPSI5S',0,4,154,4,25,32,13
	.byte	'PSI5S',0
	.word	24985
	.byte	32,2,35,0,0,14
	.word	24990
	.byte	26
	.byte	'Ifx_SRC_GPSI5S',0,4,157,4,3
	.word	25028
	.byte	18,96
	.word	22270
	.byte	19,3,0,14
	.word	25057
	.byte	10
	.byte	'_Ifx_SRC_GQSPI',0,4,160,4,25,96,13
	.byte	'QSPI',0
	.word	25066
	.byte	96,2,35,0,0,14
	.word	25071
	.byte	26
	.byte	'Ifx_SRC_GQSPI',0,4,163,4,3
	.word	25107
	.byte	18,4
	.word	22390
	.byte	19,0,0,14
	.word	25135
	.byte	10
	.byte	'_Ifx_SRC_GSCR',0,4,166,4,25,4,13
	.byte	'SCR',0
	.word	25144
	.byte	4,2,35,0,0,14
	.word	25149
	.byte	26
	.byte	'Ifx_SRC_GSCR',0,4,169,4,3
	.word	25183
	.byte	14
	.word	22448
	.byte	10
	.byte	'_Ifx_SRC_GSCU',0,4,172,4,25,20,13
	.byte	'SCU',0
	.word	25210
	.byte	20,2,35,0,0,14
	.word	25215
	.byte	26
	.byte	'Ifx_SRC_GSCU',0,4,175,4,3
	.word	25249
	.byte	18,24
	.word	22529
	.byte	19,0,0,14
	.word	25276
	.byte	10
	.byte	'_Ifx_SRC_GSENT',0,4,178,4,25,24,13
	.byte	'SENT',0
	.word	25285
	.byte	24,2,35,0,0,14
	.word	25290
	.byte	26
	.byte	'Ifx_SRC_GSENT',0,4,181,4,3
	.word	25326
	.byte	18,12
	.word	22589
	.byte	19,0,0,14
	.word	25354
	.byte	10
	.byte	'_Ifx_SRC_GSMU',0,4,184,4,25,12,13
	.byte	'SMU',0
	.word	25363
	.byte	12,2,35,0,0,14
	.word	25368
	.byte	26
	.byte	'Ifx_SRC_GSMU',0,4,187,4,3
	.word	25402
	.byte	18,16
	.word	22647
	.byte	19,1,0,14
	.word	25429
	.byte	10
	.byte	'_Ifx_SRC_GSTM',0,4,190,4,25,16,13
	.byte	'STM',0
	.word	25438
	.byte	16,2,35,0,0,14
	.word	25443
	.byte	26
	.byte	'Ifx_SRC_GSTM',0,4,193,4,3
	.word	25477
	.byte	18,64
	.word	22823
	.byte	19,3,0,14
	.word	25504
	.byte	18,224,1
	.word	521
	.byte	19,223,1,0,18,32
	.word	22719
	.byte	19,1,0,14
	.word	25529
	.byte	10
	.byte	'_Ifx_SRC_GVADC',0,4,196,4,25,192,2,13
	.byte	'G',0
	.word	25513
	.byte	64,2,35,0,13
	.byte	'reserved_40',0
	.word	25518
	.byte	224,1,2,35,64,13
	.byte	'CG',0
	.word	25538
	.byte	32,3,35,160,2,0,14
	.word	25543
	.byte	26
	.byte	'Ifx_SRC_GVADC',0,4,201,4,3
	.word	25612
	.byte	14
	.word	22925
	.byte	10
	.byte	'_Ifx_SRC_GXBAR',0,4,204,4,25,4,13
	.byte	'XBAR',0
	.word	25640
	.byte	4,2,35,0,0,14
	.word	25645
	.byte	26
	.byte	'Ifx_SRC_GXBAR',0,4,207,4,3
	.word	25681
	.byte	15,19,236,10,9,1,16
	.byte	'IfxScu_CCUCON0_CLKSEL_fBack',0,0,16
	.byte	'IfxScu_CCUCON0_CLKSEL_fPll',0,1,0,26
	.byte	'IfxScu_CCUCON0_CLKSEL',0,19,240,10,3
	.word	25709
	.byte	15,19,250,10,9,1,16
	.byte	'IfxScu_WDTCON1_IR_divBy16384',0,0,16
	.byte	'IfxScu_WDTCON1_IR_divBy256',0,1,16
	.byte	'IfxScu_WDTCON1_IR_divBy64',0,2,0,26
	.byte	'IfxScu_WDTCON1_IR',0,19,255,10,3
	.word	25806
	.byte	10
	.byte	'_Ifx_SCU_ACCEN0_Bits',0,6,45,16,4,11
	.byte	'EN0',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	521
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	521
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	521
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	521
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	521
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	521
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	521
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	521
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	521
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	521
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	521
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	521
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	521
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	521
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	521
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	521
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	521
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	521
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	521
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	521
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	521
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	521
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	521
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	521
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	521
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	521
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	521
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_ACCEN0_Bits',0,6,79,3
	.word	25928
	.byte	10
	.byte	'_Ifx_SCU_ACCEN1_Bits',0,6,82,16,4,11
	.byte	'reserved_0',0,4
	.word	498
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_SCU_ACCEN1_Bits',0,6,85,3
	.word	26485
	.byte	10
	.byte	'_Ifx_SCU_ARSTDIS_Bits',0,6,88,16,4,11
	.byte	'STM0DIS',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'STM1DIS',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'STM2DIS',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	498
	.byte	29,0,2,35,0,0,26
	.byte	'Ifx_SCU_ARSTDIS_Bits',0,6,94,3
	.word	26562
	.byte	10
	.byte	'_Ifx_SCU_CCUCON0_Bits',0,6,97,16,4,11
	.byte	'BAUD1DIV',0,1
	.word	521
	.byte	4,4,2,35,0,11
	.byte	'BAUD2DIV',0,1
	.word	521
	.byte	4,0,2,35,0,11
	.byte	'SRIDIV',0,1
	.word	521
	.byte	4,4,2,35,1,11
	.byte	'LPDIV',0,1
	.word	521
	.byte	4,0,2,35,1,11
	.byte	'SPBDIV',0,1
	.word	521
	.byte	4,4,2,35,2,11
	.byte	'FSI2DIV',0,1
	.word	521
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	521
	.byte	2,0,2,35,2,11
	.byte	'FSIDIV',0,1
	.word	521
	.byte	2,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	521
	.byte	2,4,2,35,3,11
	.byte	'CLKSEL',0,1
	.word	521
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	521
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_CCUCON0_Bits',0,6,111,3
	.word	26698
	.byte	10
	.byte	'_Ifx_SCU_CCUCON1_Bits',0,6,114,16,4,11
	.byte	'CANDIV',0,1
	.word	521
	.byte	4,4,2,35,0,11
	.byte	'ERAYDIV',0,1
	.word	521
	.byte	4,0,2,35,0,11
	.byte	'STMDIV',0,1
	.word	521
	.byte	4,4,2,35,1,11
	.byte	'GTMDIV',0,1
	.word	521
	.byte	4,0,2,35,1,11
	.byte	'ETHDIV',0,1
	.word	521
	.byte	4,4,2,35,2,11
	.byte	'ASCLINFDIV',0,1
	.word	521
	.byte	4,0,2,35,2,11
	.byte	'ASCLINSDIV',0,1
	.word	521
	.byte	4,4,2,35,3,11
	.byte	'INSEL',0,1
	.word	521
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	521
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_CCUCON1_Bits',0,6,126,3
	.word	26978
	.byte	10
	.byte	'_Ifx_SCU_CCUCON2_Bits',0,6,129,1,16,4,11
	.byte	'BBBDIV',0,1
	.word	521
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	498
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	521
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_CCUCON2_Bits',0,6,135,1,3
	.word	27216
	.byte	10
	.byte	'_Ifx_SCU_CCUCON3_Bits',0,6,138,1,16,4,11
	.byte	'PLLDIV',0,1
	.word	521
	.byte	6,2,2,35,0,11
	.byte	'PLLSEL',0,1
	.word	521
	.byte	2,0,2,35,0,11
	.byte	'PLLERAYDIV',0,1
	.word	521
	.byte	6,2,2,35,1,11
	.byte	'PLLERAYSEL',0,1
	.word	521
	.byte	2,0,2,35,1,11
	.byte	'SRIDIV',0,1
	.word	521
	.byte	6,2,2,35,2,11
	.byte	'SRISEL',0,1
	.word	521
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	521
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	521
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	521
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_CCUCON3_Bits',0,6,150,1,3
	.word	27344
	.byte	10
	.byte	'_Ifx_SCU_CCUCON4_Bits',0,6,153,1,16,4,11
	.byte	'SPBDIV',0,1
	.word	521
	.byte	6,2,2,35,0,11
	.byte	'SPBSEL',0,1
	.word	521
	.byte	2,0,2,35,0,11
	.byte	'GTMDIV',0,1
	.word	521
	.byte	6,2,2,35,1,11
	.byte	'GTMSEL',0,1
	.word	521
	.byte	2,0,2,35,1,11
	.byte	'STMDIV',0,1
	.word	521
	.byte	6,2,2,35,2,11
	.byte	'STMSEL',0,1
	.word	521
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	521
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	521
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	521
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_CCUCON4_Bits',0,6,165,1,3
	.word	27587
	.byte	10
	.byte	'_Ifx_SCU_CCUCON5_Bits',0,6,168,1,16,4,11
	.byte	'MAXDIV',0,1
	.word	521
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	498
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	521
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_CCUCON5_Bits',0,6,174,1,3
	.word	27822
	.byte	10
	.byte	'_Ifx_SCU_CCUCON6_Bits',0,6,177,1,16,4,11
	.byte	'CPU0DIV',0,1
	.word	521
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	498
	.byte	26,0,2,35,0,0,26
	.byte	'Ifx_SCU_CCUCON6_Bits',0,6,181,1,3
	.word	27950
	.byte	10
	.byte	'_Ifx_SCU_CCUCON7_Bits',0,6,184,1,16,4,11
	.byte	'CPU1DIV',0,1
	.word	521
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	498
	.byte	26,0,2,35,0,0,26
	.byte	'Ifx_SCU_CCUCON7_Bits',0,6,188,1,3
	.word	28050
	.byte	10
	.byte	'_Ifx_SCU_CHIPID_Bits',0,6,191,1,16,4,11
	.byte	'CHREV',0,1
	.word	521
	.byte	6,2,2,35,0,11
	.byte	'CHTEC',0,1
	.word	521
	.byte	2,0,2,35,0,11
	.byte	'CHID',0,1
	.word	521
	.byte	8,0,2,35,1,11
	.byte	'EEA',0,1
	.word	521
	.byte	1,7,2,35,2,11
	.byte	'UCODE',0,1
	.word	521
	.byte	7,0,2,35,2,11
	.byte	'FSIZE',0,1
	.word	521
	.byte	4,4,2,35,3,11
	.byte	'SP',0,1
	.word	521
	.byte	2,2,2,35,3,11
	.byte	'SEC',0,1
	.word	521
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_CHIPID_Bits',0,6,202,1,3
	.word	28150
	.byte	10
	.byte	'_Ifx_SCU_DTSCON_Bits',0,6,205,1,16,4,11
	.byte	'PWD',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'START',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	521
	.byte	2,4,2,35,0,11
	.byte	'CAL',0,4
	.word	498
	.byte	20,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	521
	.byte	7,1,2,35,3,11
	.byte	'SLCK',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_DTSCON_Bits',0,6,213,1,3
	.word	28358
	.byte	10
	.byte	'_Ifx_SCU_DTSLIM_Bits',0,6,216,1,16,4,11
	.byte	'LOWER',0,2
	.word	1072
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	521
	.byte	5,1,2,35,1,11
	.byte	'LLU',0,1
	.word	521
	.byte	1,0,2,35,1,11
	.byte	'UPPER',0,2
	.word	1072
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	521
	.byte	4,2,2,35,3,11
	.byte	'SLCK',0,1
	.word	521
	.byte	1,1,2,35,3,11
	.byte	'UOF',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_DTSLIM_Bits',0,6,225,1,3
	.word	28523
	.byte	10
	.byte	'_Ifx_SCU_DTSSTAT_Bits',0,6,228,1,16,4,11
	.byte	'RESULT',0,2
	.word	1072
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	521
	.byte	4,2,2,35,1,11
	.byte	'RDY',0,1
	.word	521
	.byte	1,1,2,35,1,11
	.byte	'BUSY',0,1
	.word	521
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1072
	.byte	16,0,2,35,2,0,26
	.byte	'Ifx_SCU_DTSSTAT_Bits',0,6,235,1,3
	.word	28706
	.byte	10
	.byte	'_Ifx_SCU_EICR_Bits',0,6,238,1,16,4,11
	.byte	'reserved_0',0,1
	.word	521
	.byte	4,4,2,35,0,11
	.byte	'EXIS0',0,1
	.word	521
	.byte	3,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	521
	.byte	1,0,2,35,0,11
	.byte	'FEN0',0,1
	.word	521
	.byte	1,7,2,35,1,11
	.byte	'REN0',0,1
	.word	521
	.byte	1,6,2,35,1,11
	.byte	'LDEN0',0,1
	.word	521
	.byte	1,5,2,35,1,11
	.byte	'EIEN0',0,1
	.word	521
	.byte	1,4,2,35,1,11
	.byte	'INP0',0,1
	.word	521
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	498
	.byte	5,12,2,35,0,11
	.byte	'EXIS1',0,1
	.word	521
	.byte	3,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	521
	.byte	1,0,2,35,2,11
	.byte	'FEN1',0,1
	.word	521
	.byte	1,7,2,35,3,11
	.byte	'REN1',0,1
	.word	521
	.byte	1,6,2,35,3,11
	.byte	'LDEN1',0,1
	.word	521
	.byte	1,5,2,35,3,11
	.byte	'EIEN1',0,1
	.word	521
	.byte	1,4,2,35,3,11
	.byte	'INP1',0,1
	.word	521
	.byte	3,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_EICR_Bits',0,6,129,2,3
	.word	28860
	.byte	10
	.byte	'_Ifx_SCU_EIFR_Bits',0,6,132,2,16,4,11
	.byte	'INTF0',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'INTF1',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'INTF2',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'INTF3',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'INTF4',0,1
	.word	521
	.byte	1,3,2,35,0,11
	.byte	'INTF5',0,1
	.word	521
	.byte	1,2,2,35,0,11
	.byte	'INTF6',0,1
	.word	521
	.byte	1,1,2,35,0,11
	.byte	'INTF7',0,1
	.word	521
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	498
	.byte	24,0,2,35,0,0,26
	.byte	'Ifx_SCU_EIFR_Bits',0,6,143,2,3
	.word	29224
	.byte	10
	.byte	'_Ifx_SCU_EMSR_Bits',0,6,146,2,16,4,11
	.byte	'POL',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'MODE',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'ENON',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'PSEL',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	1072
	.byte	12,0,2,35,0,11
	.byte	'EMSF',0,1
	.word	521
	.byte	1,7,2,35,2,11
	.byte	'SEMSF',0,1
	.word	521
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	521
	.byte	6,0,2,35,2,11
	.byte	'EMSFM',0,1
	.word	521
	.byte	2,6,2,35,3,11
	.byte	'SEMSFM',0,1
	.word	521
	.byte	2,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	521
	.byte	4,0,2,35,3,0,26
	.byte	'Ifx_SCU_EMSR_Bits',0,6,159,2,3
	.word	29435
	.byte	10
	.byte	'_Ifx_SCU_ESRCFG_Bits',0,6,162,2,16,4,11
	.byte	'reserved_0',0,1
	.word	521
	.byte	7,1,2,35,0,11
	.byte	'EDCON',0,2
	.word	1072
	.byte	2,7,2,35,0,11
	.byte	'reserved_9',0,4
	.word	498
	.byte	23,0,2,35,0,0,26
	.byte	'Ifx_SCU_ESRCFG_Bits',0,6,167,2,3
	.word	29687
	.byte	10
	.byte	'_Ifx_SCU_ESROCFG_Bits',0,6,170,2,16,4,11
	.byte	'ARI',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'ARC',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	498
	.byte	30,0,2,35,0,0,26
	.byte	'Ifx_SCU_ESROCFG_Bits',0,6,175,2,3
	.word	29805
	.byte	10
	.byte	'_Ifx_SCU_EVR13CON_Bits',0,6,178,2,16,4,11
	.byte	'reserved_0',0,4
	.word	498
	.byte	28,4,2,35,0,11
	.byte	'EVR13OFF',0,1
	.word	521
	.byte	1,3,2,35,3,11
	.byte	'BPEVR13OFF',0,1
	.word	521
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	521
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_EVR13CON_Bits',0,6,185,2,3
	.word	29916
	.byte	10
	.byte	'_Ifx_SCU_EVR33CON_Bits',0,6,188,2,16,4,11
	.byte	'reserved_0',0,4
	.word	498
	.byte	28,4,2,35,0,11
	.byte	'EVR33OFF',0,1
	.word	521
	.byte	1,3,2,35,3,11
	.byte	'BPEVR33OFF',0,1
	.word	521
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	521
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_EVR33CON_Bits',0,6,195,2,3
	.word	30079
	.byte	10
	.byte	'_Ifx_SCU_EVRADCSTAT_Bits',0,6,198,2,16,4,11
	.byte	'ADC13V',0,1
	.word	521
	.byte	8,0,2,35,0,11
	.byte	'ADC33V',0,1
	.word	521
	.byte	8,0,2,35,1,11
	.byte	'ADCSWDV',0,1
	.word	521
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	521
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_EVRADCSTAT_Bits',0,6,205,2,3
	.word	30242
	.byte	10
	.byte	'_Ifx_SCU_EVRDVSTAT_Bits',0,6,208,2,16,4,11
	.byte	'DVS13TRIM',0,1
	.word	521
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	521
	.byte	8,0,2,35,1,11
	.byte	'DVS33TRIM',0,1
	.word	521
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	521
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_EVRDVSTAT_Bits',0,6,215,2,3
	.word	30400
	.byte	10
	.byte	'_Ifx_SCU_EVRMONCTRL_Bits',0,6,218,2,16,4,11
	.byte	'EVR13OVMOD',0,1
	.word	521
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	521
	.byte	2,4,2,35,0,11
	.byte	'EVR13UVMOD',0,1
	.word	521
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	521
	.byte	2,0,2,35,0,11
	.byte	'EVR33OVMOD',0,1
	.word	521
	.byte	2,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	521
	.byte	2,4,2,35,1,11
	.byte	'EVR33UVMOD',0,1
	.word	521
	.byte	2,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	521
	.byte	2,0,2,35,1,11
	.byte	'SWDOVMOD',0,1
	.word	521
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	521
	.byte	2,4,2,35,2,11
	.byte	'SWDUVMOD',0,1
	.word	521
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,2
	.word	1072
	.byte	10,0,2,35,2,0,26
	.byte	'Ifx_SCU_EVRMONCTRL_Bits',0,6,232,2,3
	.word	30565
	.byte	10
	.byte	'_Ifx_SCU_EVROSCCTRL_Bits',0,6,235,2,16,4,11
	.byte	'OSCTRIM',0,2
	.word	1072
	.byte	10,6,2,35,0,11
	.byte	'OSCPTAT',0,1
	.word	521
	.byte	6,0,2,35,1,11
	.byte	'OSCANASEL',0,1
	.word	521
	.byte	4,4,2,35,2,11
	.byte	'HPBGTRIM',0,2
	.word	1072
	.byte	7,5,2,35,2,11
	.byte	'HPBGCLKEN',0,1
	.word	521
	.byte	1,4,2,35,3,11
	.byte	'OSC3V3',0,1
	.word	521
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	521
	.byte	2,1,2,35,3,11
	.byte	'LCK',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_EVROSCCTRL_Bits',0,6,245,2,3
	.word	30894
	.byte	10
	.byte	'_Ifx_SCU_EVROVMON_Bits',0,6,248,2,16,4,11
	.byte	'EVR13OVVAL',0,1
	.word	521
	.byte	8,0,2,35,0,11
	.byte	'EVR33OVVAL',0,1
	.word	521
	.byte	8,0,2,35,1,11
	.byte	'SWDOVVAL',0,1
	.word	521
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	521
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_EVROVMON_Bits',0,6,255,2,3
	.word	31115
	.byte	10
	.byte	'_Ifx_SCU_EVRRSTCON_Bits',0,6,130,3,16,4,11
	.byte	'RST13TRIM',0,1
	.word	521
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	498
	.byte	16,8,2,35,0,11
	.byte	'RST13OFF',0,1
	.word	521
	.byte	1,7,2,35,3,11
	.byte	'BPRST13OFF',0,1
	.word	521
	.byte	1,6,2,35,3,11
	.byte	'RST33OFF',0,1
	.word	521
	.byte	1,5,2,35,3,11
	.byte	'BPRST33OFF',0,1
	.word	521
	.byte	1,4,2,35,3,11
	.byte	'RSTSWDOFF',0,1
	.word	521
	.byte	1,3,2,35,3,11
	.byte	'BPRSTSWDOFF',0,1
	.word	521
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	521
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_EVRRSTCON_Bits',0,6,142,3,3
	.word	31278
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF1_Bits',0,6,145,3,16,4,11
	.byte	'SD5P',0,1
	.word	521
	.byte	8,0,2,35,0,11
	.byte	'SD5I',0,1
	.word	521
	.byte	8,0,2,35,1,11
	.byte	'SD5D',0,1
	.word	521
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	521
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_EVRSDCOEFF1_Bits',0,6,152,3,3
	.word	31550
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF2_Bits',0,6,155,3,16,4,11
	.byte	'SD33P',0,1
	.word	521
	.byte	8,0,2,35,0,11
	.byte	'SD33I',0,1
	.word	521
	.byte	8,0,2,35,1,11
	.byte	'SD33D',0,1
	.word	521
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	521
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_EVRSDCOEFF2_Bits',0,6,162,3,3
	.word	31703
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF3_Bits',0,6,165,3,16,4,11
	.byte	'CT5REG0',0,1
	.word	521
	.byte	8,0,2,35,0,11
	.byte	'CT5REG1',0,1
	.word	521
	.byte	8,0,2,35,1,11
	.byte	'CT5REG2',0,1
	.word	521
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	521
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_EVRSDCOEFF3_Bits',0,6,172,3,3
	.word	31859
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF4_Bits',0,6,175,3,16,4,11
	.byte	'CT5REG3',0,1
	.word	521
	.byte	8,0,2,35,0,11
	.byte	'CT5REG4',0,1
	.word	521
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1072
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_EVRSDCOEFF4_Bits',0,6,181,3,3
	.word	32021
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF5_Bits',0,6,184,3,16,4,11
	.byte	'CT33REG0',0,1
	.word	521
	.byte	8,0,2,35,0,11
	.byte	'CT33REG1',0,1
	.word	521
	.byte	8,0,2,35,1,11
	.byte	'CT33REG2',0,1
	.word	521
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	521
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_EVRSDCOEFF5_Bits',0,6,191,3,3
	.word	32164
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF6_Bits',0,6,194,3,16,4,11
	.byte	'CT33REG3',0,1
	.word	521
	.byte	8,0,2,35,0,11
	.byte	'CT33REG4',0,1
	.word	521
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1072
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_EVRSDCOEFF6_Bits',0,6,200,3,3
	.word	32329
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL1_Bits',0,6,203,3,16,4,11
	.byte	'SDFREQSPRD',0,2
	.word	1072
	.byte	16,0,2,35,0,11
	.byte	'SDFREQ',0,1
	.word	521
	.byte	8,0,2,35,2,11
	.byte	'SDSTEP',0,1
	.word	521
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	521
	.byte	2,2,2,35,3,11
	.byte	'SDSAMPLE',0,1
	.word	521
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_EVRSDCTRL1_Bits',0,6,211,3,3
	.word	32474
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL2_Bits',0,6,214,3,16,4,11
	.byte	'DRVP',0,1
	.word	521
	.byte	8,0,2,35,0,11
	.byte	'SDMINMAXDC',0,1
	.word	521
	.byte	8,0,2,35,1,11
	.byte	'DRVN',0,1
	.word	521
	.byte	8,0,2,35,2,11
	.byte	'SDLUT',0,1
	.word	521
	.byte	6,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	521
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_EVRSDCTRL2_Bits',0,6,222,3,3
	.word	32655
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL3_Bits',0,6,225,3,16,4,11
	.byte	'SDPWMPRE',0,1
	.word	521
	.byte	8,0,2,35,0,11
	.byte	'SDPID',0,1
	.word	521
	.byte	8,0,2,35,1,11
	.byte	'SDVOKLVL',0,1
	.word	521
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	521
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_EVRSDCTRL3_Bits',0,6,232,3,3
	.word	32829
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL4_Bits',0,6,235,3,16,4,11
	.byte	'reserved_0',0,1
	.word	521
	.byte	8,0,2,35,0,11
	.byte	'SYNCDIV',0,1
	.word	521
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	498
	.byte	20,1,2,35,0,11
	.byte	'LCK',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_EVRSDCTRL4_Bits',0,6,241,3,3
	.word	32989
	.byte	10
	.byte	'_Ifx_SCU_EVRSTAT_Bits',0,6,244,3,16,4,11
	.byte	'EVR13',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'OV13',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'EVR33',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'OV33',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'OVSWD',0,1
	.word	521
	.byte	1,3,2,35,0,11
	.byte	'UV13',0,1
	.word	521
	.byte	1,2,2,35,0,11
	.byte	'UV33',0,1
	.word	521
	.byte	1,1,2,35,0,11
	.byte	'UVSWD',0,1
	.word	521
	.byte	1,0,2,35,0,11
	.byte	'EXTPASS13',0,1
	.word	521
	.byte	1,7,2,35,1,11
	.byte	'EXTPASS33',0,1
	.word	521
	.byte	1,6,2,35,1,11
	.byte	'BGPROK',0,1
	.word	521
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	498
	.byte	21,0,2,35,0,0,26
	.byte	'Ifx_SCU_EVRSTAT_Bits',0,6,130,4,3
	.word	33133
	.byte	10
	.byte	'_Ifx_SCU_EVRTRIM_Bits',0,6,133,4,16,4,11
	.byte	'EVR13TRIM',0,1
	.word	521
	.byte	8,0,2,35,0,11
	.byte	'SDVOUTSEL',0,1
	.word	521
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1072
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_EVRTRIM_Bits',0,6,139,4,3
	.word	33407
	.byte	10
	.byte	'_Ifx_SCU_EVRUVMON_Bits',0,6,142,4,16,4,11
	.byte	'EVR13UVVAL',0,1
	.word	521
	.byte	8,0,2,35,0,11
	.byte	'EVR33UVVAL',0,1
	.word	521
	.byte	8,0,2,35,1,11
	.byte	'SWDUVVAL',0,1
	.word	521
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	521
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_EVRUVMON_Bits',0,6,149,4,3
	.word	33546
	.byte	10
	.byte	'_Ifx_SCU_EXTCON_Bits',0,6,152,4,16,4,11
	.byte	'EN0',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'SEL0',0,1
	.word	521
	.byte	4,2,2,35,0,11
	.byte	'reserved_6',0,2
	.word	1072
	.byte	10,0,2,35,0,11
	.byte	'EN1',0,1
	.word	521
	.byte	1,7,2,35,2,11
	.byte	'NSEL',0,1
	.word	521
	.byte	1,6,2,35,2,11
	.byte	'SEL1',0,1
	.word	521
	.byte	4,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	521
	.byte	2,0,2,35,2,11
	.byte	'DIV1',0,1
	.word	521
	.byte	8,0,2,35,3,0,26
	.byte	'Ifx_SCU_EXTCON_Bits',0,6,163,4,3
	.word	33709
	.byte	10
	.byte	'_Ifx_SCU_FDR_Bits',0,6,166,4,16,4,11
	.byte	'STEP',0,2
	.word	1072
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	521
	.byte	4,2,2,35,1,11
	.byte	'DM',0,1
	.word	521
	.byte	2,0,2,35,1,11
	.byte	'RESULT',0,2
	.word	1072
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	521
	.byte	5,1,2,35,3,11
	.byte	'DISCLK',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_FDR_Bits',0,6,174,4,3
	.word	33927
	.byte	10
	.byte	'_Ifx_SCU_FMR_Bits',0,6,177,4,16,4,11
	.byte	'FS0',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'FS1',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'FS2',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'FS3',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'FS4',0,1
	.word	521
	.byte	1,3,2,35,0,11
	.byte	'FS5',0,1
	.word	521
	.byte	1,2,2,35,0,11
	.byte	'FS6',0,1
	.word	521
	.byte	1,1,2,35,0,11
	.byte	'FS7',0,1
	.word	521
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	521
	.byte	8,0,2,35,1,11
	.byte	'FC0',0,1
	.word	521
	.byte	1,7,2,35,2,11
	.byte	'FC1',0,1
	.word	521
	.byte	1,6,2,35,2,11
	.byte	'FC2',0,1
	.word	521
	.byte	1,5,2,35,2,11
	.byte	'FC3',0,1
	.word	521
	.byte	1,4,2,35,2,11
	.byte	'FC4',0,1
	.word	521
	.byte	1,3,2,35,2,11
	.byte	'FC5',0,1
	.word	521
	.byte	1,2,2,35,2,11
	.byte	'FC6',0,1
	.word	521
	.byte	1,1,2,35,2,11
	.byte	'FC7',0,1
	.word	521
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	521
	.byte	8,0,2,35,3,0,26
	.byte	'Ifx_SCU_FMR_Bits',0,6,197,4,3
	.word	34090
	.byte	10
	.byte	'_Ifx_SCU_ID_Bits',0,6,200,4,16,4,11
	.byte	'MODREV',0,1
	.word	521
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	521
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	1072
	.byte	16,0,2,35,2,0,26
	.byte	'Ifx_SCU_ID_Bits',0,6,205,4,3
	.word	34426
	.byte	10
	.byte	'_Ifx_SCU_IGCR_Bits',0,6,208,4,16,4,11
	.byte	'IPEN00',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'IPEN01',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'IPEN02',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'IPEN03',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'IPEN04',0,1
	.word	521
	.byte	1,3,2,35,0,11
	.byte	'IPEN05',0,1
	.word	521
	.byte	1,2,2,35,0,11
	.byte	'IPEN06',0,1
	.word	521
	.byte	1,1,2,35,0,11
	.byte	'IPEN07',0,1
	.word	521
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	521
	.byte	5,3,2,35,1,11
	.byte	'GEEN0',0,1
	.word	521
	.byte	1,2,2,35,1,11
	.byte	'IGP0',0,1
	.word	521
	.byte	2,0,2,35,1,11
	.byte	'IPEN10',0,1
	.word	521
	.byte	1,7,2,35,2,11
	.byte	'IPEN11',0,1
	.word	521
	.byte	1,6,2,35,2,11
	.byte	'IPEN12',0,1
	.word	521
	.byte	1,5,2,35,2,11
	.byte	'IPEN13',0,1
	.word	521
	.byte	1,4,2,35,2,11
	.byte	'IPEN14',0,1
	.word	521
	.byte	1,3,2,35,2,11
	.byte	'IPEN15',0,1
	.word	521
	.byte	1,2,2,35,2,11
	.byte	'IPEN16',0,1
	.word	521
	.byte	1,1,2,35,2,11
	.byte	'IPEN17',0,1
	.word	521
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	521
	.byte	5,3,2,35,3,11
	.byte	'GEEN1',0,1
	.word	521
	.byte	1,2,2,35,3,11
	.byte	'IGP1',0,1
	.word	521
	.byte	2,0,2,35,3,0,26
	.byte	'Ifx_SCU_IGCR_Bits',0,6,232,4,3
	.word	34533
	.byte	10
	.byte	'_Ifx_SCU_IN_Bits',0,6,235,4,16,4,11
	.byte	'P0',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	498
	.byte	30,0,2,35,0,0,26
	.byte	'Ifx_SCU_IN_Bits',0,6,240,4,3
	.word	34985
	.byte	10
	.byte	'_Ifx_SCU_IOCR_Bits',0,6,243,4,16,4,11
	.byte	'reserved_0',0,1
	.word	521
	.byte	4,4,2,35,0,11
	.byte	'PC0',0,1
	.word	521
	.byte	4,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	521
	.byte	4,4,2,35,1,11
	.byte	'PC1',0,1
	.word	521
	.byte	4,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1072
	.byte	16,0,2,35,2,0,26
	.byte	'Ifx_SCU_IOCR_Bits',0,6,250,4,3
	.word	35084
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL0_Bits',0,6,253,4,16,4,11
	.byte	'LBISTREQ',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'LBISTREQP',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'PATTERNS',0,2
	.word	1072
	.byte	14,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	1072
	.byte	16,0,2,35,2,0,26
	.byte	'Ifx_SCU_LBISTCTRL0_Bits',0,6,131,5,3
	.word	35234
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL1_Bits',0,6,134,5,16,4,11
	.byte	'SEED',0,4
	.word	498
	.byte	23,9,2,35,0,11
	.byte	'reserved_23',0,1
	.word	521
	.byte	1,0,2,35,2,11
	.byte	'SPLITSH',0,1
	.word	521
	.byte	3,5,2,35,3,11
	.byte	'BODY',0,1
	.word	521
	.byte	1,4,2,35,3,11
	.byte	'LBISTFREQU',0,1
	.word	521
	.byte	4,0,2,35,3,0,26
	.byte	'Ifx_SCU_LBISTCTRL1_Bits',0,6,141,5,3
	.word	35383
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL2_Bits',0,6,144,5,16,4,11
	.byte	'SIGNATURE',0,4
	.word	498
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	521
	.byte	7,1,2,35,3,11
	.byte	'LBISTDONE',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_LBISTCTRL2_Bits',0,6,149,5,3
	.word	35544
	.byte	10
	.byte	'_Ifx_SCU_LCLCON_Bits',0,6,152,5,16,4,11
	.byte	'reserved_0',0,2
	.word	1072
	.byte	16,0,2,35,0,11
	.byte	'LS',0,1
	.word	521
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,2
	.word	1072
	.byte	14,1,2,35,2,11
	.byte	'LSEN',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_LCLCON_Bits',0,6,158,5,3
	.word	35674
	.byte	10
	.byte	'_Ifx_SCU_LCLTEST_Bits',0,6,161,5,16,4,11
	.byte	'LCLT0',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'LCLT1',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	498
	.byte	30,0,2,35,0,0,26
	.byte	'Ifx_SCU_LCLTEST_Bits',0,6,166,5,3
	.word	35806
	.byte	10
	.byte	'_Ifx_SCU_MANID_Bits',0,6,169,5,16,4,11
	.byte	'DEPT',0,1
	.word	521
	.byte	5,3,2,35,0,11
	.byte	'MANUF',0,2
	.word	1072
	.byte	11,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	1072
	.byte	16,0,2,35,2,0,26
	.byte	'Ifx_SCU_MANID_Bits',0,6,174,5,3
	.word	35921
	.byte	10
	.byte	'_Ifx_SCU_OMR_Bits',0,6,177,5,16,4,11
	.byte	'PS0',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	1072
	.byte	14,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	521
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	521
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	1072
	.byte	14,0,2,35,2,0,26
	.byte	'Ifx_SCU_OMR_Bits',0,6,185,5,3
	.word	36032
	.byte	10
	.byte	'_Ifx_SCU_OSCCON_Bits',0,6,188,5,16,4,11
	.byte	'reserved_0',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'PLLLV',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'OSCRES',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'GAINSEL',0,1
	.word	521
	.byte	2,3,2,35,0,11
	.byte	'MODE',0,1
	.word	521
	.byte	2,1,2,35,0,11
	.byte	'SHBY',0,1
	.word	521
	.byte	1,0,2,35,0,11
	.byte	'PLLHV',0,1
	.word	521
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	521
	.byte	1,6,2,35,1,11
	.byte	'X1D',0,1
	.word	521
	.byte	1,5,2,35,1,11
	.byte	'X1DEN',0,1
	.word	521
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	521
	.byte	4,0,2,35,1,11
	.byte	'OSCVAL',0,1
	.word	521
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	521
	.byte	2,1,2,35,2,11
	.byte	'APREN',0,1
	.word	521
	.byte	1,0,2,35,2,11
	.byte	'CAP0EN',0,1
	.word	521
	.byte	1,7,2,35,3,11
	.byte	'CAP1EN',0,1
	.word	521
	.byte	1,6,2,35,3,11
	.byte	'CAP2EN',0,1
	.word	521
	.byte	1,5,2,35,3,11
	.byte	'CAP3EN',0,1
	.word	521
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	521
	.byte	4,0,2,35,3,0,26
	.byte	'Ifx_SCU_OSCCON_Bits',0,6,209,5,3
	.word	36190
	.byte	10
	.byte	'_Ifx_SCU_OUT_Bits',0,6,212,5,16,4,11
	.byte	'P0',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	498
	.byte	30,0,2,35,0,0,26
	.byte	'Ifx_SCU_OUT_Bits',0,6,217,5,3
	.word	36602
	.byte	10
	.byte	'_Ifx_SCU_OVCCON_Bits',0,6,220,5,16,4,11
	.byte	'CSEL0',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'CSEL1',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'CSEL2',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,2
	.word	1072
	.byte	13,0,2,35,0,11
	.byte	'OVSTRT',0,1
	.word	521
	.byte	1,7,2,35,2,11
	.byte	'OVSTP',0,1
	.word	521
	.byte	1,6,2,35,2,11
	.byte	'DCINVAL',0,1
	.word	521
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	521
	.byte	5,0,2,35,2,11
	.byte	'OVCONF',0,1
	.word	521
	.byte	1,7,2,35,3,11
	.byte	'POVCONF',0,1
	.word	521
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	521
	.byte	6,0,2,35,3,0,26
	.byte	'Ifx_SCU_OVCCON_Bits',0,6,233,5,3
	.word	36703
	.byte	10
	.byte	'_Ifx_SCU_OVCENABLE_Bits',0,6,236,5,16,4,11
	.byte	'OVEN0',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'OVEN1',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'OVEN2',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	498
	.byte	29,0,2,35,0,0,26
	.byte	'Ifx_SCU_OVCENABLE_Bits',0,6,242,5,3
	.word	36970
	.byte	10
	.byte	'_Ifx_SCU_PDISC_Bits',0,6,245,5,16,4,11
	.byte	'PDIS0',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	498
	.byte	30,0,2,35,0,0,26
	.byte	'Ifx_SCU_PDISC_Bits',0,6,250,5,3
	.word	37106
	.byte	10
	.byte	'_Ifx_SCU_PDR_Bits',0,6,253,5,16,4,11
	.byte	'PD0',0,1
	.word	521
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	521
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	521
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	498
	.byte	24,0,2,35,0,0,26
	.byte	'Ifx_SCU_PDR_Bits',0,6,132,6,3
	.word	37217
	.byte	10
	.byte	'_Ifx_SCU_PDRR_Bits',0,6,135,6,16,4,11
	.byte	'PDR0',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'PDR1',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'PDR2',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'PDR3',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'PDR4',0,1
	.word	521
	.byte	1,3,2,35,0,11
	.byte	'PDR5',0,1
	.word	521
	.byte	1,2,2,35,0,11
	.byte	'PDR6',0,1
	.word	521
	.byte	1,1,2,35,0,11
	.byte	'PDR7',0,1
	.word	521
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	498
	.byte	24,0,2,35,0,0,26
	.byte	'Ifx_SCU_PDRR_Bits',0,6,146,6,3
	.word	37350
	.byte	10
	.byte	'_Ifx_SCU_PLLCON0_Bits',0,6,149,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'MODEN',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	521
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	521
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	521
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	1072
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	521
	.byte	7,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	521
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	521
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	521
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	521
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	521
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	521
	.byte	4,0,2,35,3,0,26
	.byte	'Ifx_SCU_PLLCON0_Bits',0,6,166,6,3
	.word	37553
	.byte	10
	.byte	'_Ifx_SCU_PLLCON1_Bits',0,6,169,6,16,4,11
	.byte	'K2DIV',0,1
	.word	521
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	521
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	521
	.byte	7,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	521
	.byte	1,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	521
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	1072
	.byte	9,0,2,35,2,0,26
	.byte	'Ifx_SCU_PLLCON1_Bits',0,6,177,6,3
	.word	37909
	.byte	10
	.byte	'_Ifx_SCU_PLLCON2_Bits',0,6,180,6,16,4,11
	.byte	'MODCFG',0,2
	.word	1072
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	1072
	.byte	16,0,2,35,2,0,26
	.byte	'Ifx_SCU_PLLCON2_Bits',0,6,184,6,3
	.word	38087
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON0_Bits',0,6,187,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	521
	.byte	2,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	521
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	521
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	521
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	1072
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	521
	.byte	5,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	521
	.byte	2,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	521
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	521
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	521
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	521
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	521
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	521
	.byte	4,0,2,35,3,0,26
	.byte	'Ifx_SCU_PLLERAYCON0_Bits',0,6,204,6,3
	.word	38187
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON1_Bits',0,6,207,6,16,4,11
	.byte	'K2DIV',0,1
	.word	521
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	521
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	521
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	521
	.byte	4,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	521
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	1072
	.byte	9,0,2,35,2,0,26
	.byte	'Ifx_SCU_PLLERAYCON1_Bits',0,6,215,6,3
	.word	38557
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYSTAT_Bits',0,6,218,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'PWDSTAT',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	521
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	521
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	498
	.byte	26,0,2,35,0,0,26
	.byte	'Ifx_SCU_PLLERAYSTAT_Bits',0,6,227,6,3
	.word	38743
	.byte	10
	.byte	'_Ifx_SCU_PLLSTAT_Bits',0,6,230,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	521
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	521
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	521
	.byte	1,1,2,35,0,11
	.byte	'MODRUN',0,1
	.word	521
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	498
	.byte	24,0,2,35,0,0,26
	.byte	'Ifx_SCU_PLLSTAT_Bits',0,6,241,6,3
	.word	38941
	.byte	10
	.byte	'_Ifx_SCU_PMCSR_Bits',0,6,244,6,16,4,11
	.byte	'REQSLP',0,1
	.word	521
	.byte	2,6,2,35,0,11
	.byte	'SMUSLP',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	521
	.byte	5,0,2,35,0,11
	.byte	'PMST',0,1
	.word	521
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	498
	.byte	21,0,2,35,0,0,26
	.byte	'Ifx_SCU_PMCSR_Bits',0,6,251,6,3
	.word	39174
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR0_Bits',0,6,254,6,16,4,11
	.byte	'reserved_0',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'ESR1WKEN',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'PINAWKEN',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'PINBWKEN',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'ESR0DFEN',0,1
	.word	521
	.byte	1,3,2,35,0,11
	.byte	'ESR0EDCON',0,1
	.word	521
	.byte	2,1,2,35,0,11
	.byte	'ESR1DFEN',0,1
	.word	521
	.byte	1,0,2,35,0,11
	.byte	'ESR1EDCON',0,1
	.word	521
	.byte	2,6,2,35,1,11
	.byte	'PINADFEN',0,1
	.word	521
	.byte	1,5,2,35,1,11
	.byte	'PINAEDCON',0,1
	.word	521
	.byte	2,3,2,35,1,11
	.byte	'PINBDFEN',0,1
	.word	521
	.byte	1,2,2,35,1,11
	.byte	'PINBEDCON',0,1
	.word	521
	.byte	2,0,2,35,1,11
	.byte	'SCREN',0,1
	.word	521
	.byte	1,7,2,35,2,11
	.byte	'STBYRAMSEL',0,1
	.word	521
	.byte	2,5,2,35,2,11
	.byte	'SCRCLKSEL',0,1
	.word	521
	.byte	1,4,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	521
	.byte	1,3,2,35,2,11
	.byte	'TRISTEN',0,1
	.word	521
	.byte	1,2,2,35,2,11
	.byte	'TRISTREQ',0,1
	.word	521
	.byte	1,1,2,35,2,11
	.byte	'PORSTDF',0,1
	.word	521
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	521
	.byte	1,7,2,35,3,11
	.byte	'DCDCSYNC',0,1
	.word	521
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	521
	.byte	3,3,2,35,3,11
	.byte	'ESR0TRIST',0,1
	.word	521
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	521
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_PMSWCR0_Bits',0,6,153,7,3
	.word	39326
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR1_Bits',0,6,156,7,16,4,11
	.byte	'SCRSTEN',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'SCRSTREQ',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	521
	.byte	6,0,2,35,0,11
	.byte	'CPUIDLSEL',0,1
	.word	521
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	521
	.byte	1,4,2,35,1,11
	.byte	'IRADIS',0,1
	.word	521
	.byte	1,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	521
	.byte	3,0,2,35,1,11
	.byte	'SCRCFG',0,1
	.word	521
	.byte	8,0,2,35,2,11
	.byte	'CPUSEL',0,1
	.word	521
	.byte	3,5,2,35,3,11
	.byte	'STBYEVEN',0,1
	.word	521
	.byte	1,4,2,35,3,11
	.byte	'STBYEV',0,1
	.word	521
	.byte	3,1,2,35,3,11
	.byte	'LCK',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_PMSWCR1_Bits',0,6,170,7,3
	.word	39893
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR2_Bits',0,6,173,7,16,4,11
	.byte	'SCRINT',0,1
	.word	521
	.byte	8,0,2,35,0,11
	.byte	'BUSY',0,1
	.word	521
	.byte	1,7,2,35,1,11
	.byte	'SCRECC',0,1
	.word	521
	.byte	1,6,2,35,1,11
	.byte	'SCRWDT',0,1
	.word	521
	.byte	1,5,2,35,1,11
	.byte	'SCRRST',0,1
	.word	521
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	521
	.byte	4,0,2,35,1,11
	.byte	'TCINT',0,1
	.word	521
	.byte	8,0,2,35,2,11
	.byte	'TCINTREQ',0,1
	.word	521
	.byte	1,7,2,35,3,11
	.byte	'SMURST',0,1
	.word	521
	.byte	1,6,2,35,3,11
	.byte	'RST',0,1
	.word	521
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	521
	.byte	4,1,2,35,3,11
	.byte	'LCK',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_PMSWCR2_Bits',0,6,187,7,3
	.word	40187
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTAT_Bits',0,6,190,7,16,4,11
	.byte	'reserved_0',0,1
	.word	521
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKP',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUN',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'PINAWKP',0,1
	.word	521
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUN',0,1
	.word	521
	.byte	1,2,2,35,0,11
	.byte	'PINBWKP',0,1
	.word	521
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUN',0,1
	.word	521
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	521
	.byte	1,7,2,35,1,11
	.byte	'PORSTDF',0,1
	.word	521
	.byte	1,6,2,35,1,11
	.byte	'HWCFGEVR',0,1
	.word	521
	.byte	3,3,2,35,1,11
	.byte	'STBYRAM',0,1
	.word	521
	.byte	2,1,2,35,1,11
	.byte	'TRIST',0,1
	.word	521
	.byte	1,0,2,35,1,11
	.byte	'SCRST',0,1
	.word	521
	.byte	1,7,2,35,2,11
	.byte	'SCRWKP',0,1
	.word	521
	.byte	1,6,2,35,2,11
	.byte	'SCR',0,1
	.word	521
	.byte	1,5,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	521
	.byte	1,4,2,35,2,11
	.byte	'ESR1WKEN',0,1
	.word	521
	.byte	1,3,2,35,2,11
	.byte	'PINAWKEN',0,1
	.word	521
	.byte	1,2,2,35,2,11
	.byte	'PINBWKEN',0,1
	.word	521
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	1072
	.byte	4,5,2,35,2,11
	.byte	'ESR0TRIST',0,1
	.word	521
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	521
	.byte	4,0,2,35,3,0,26
	.byte	'Ifx_SCU_PMSWSTAT_Bits',0,6,214,7,3
	.word	40465
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTATCLR_Bits',0,6,217,7,16,4,11
	.byte	'reserved_0',0,1
	.word	521
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKPCLR',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUNCLR',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'PINAWKPCLR',0,1
	.word	521
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUNCLR',0,1
	.word	521
	.byte	1,2,2,35,0,11
	.byte	'PINBWKPCLR',0,1
	.word	521
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUNCLR',0,1
	.word	521
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	521
	.byte	8,0,2,35,1,11
	.byte	'SCRSTCLR',0,1
	.word	521
	.byte	1,7,2,35,2,11
	.byte	'SCRWKPCLR',0,1
	.word	521
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	1072
	.byte	14,0,2,35,2,0,26
	.byte	'Ifx_SCU_PMSWSTATCLR_Bits',0,6,230,7,3
	.word	40961
	.byte	10
	.byte	'_Ifx_SCU_RSTCON2_Bits',0,6,233,7,16,4,11
	.byte	'reserved_0',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'CLRC',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	1072
	.byte	10,4,2,35,0,11
	.byte	'CSS0',0,1
	.word	521
	.byte	1,3,2,35,1,11
	.byte	'CSS1',0,1
	.word	521
	.byte	1,2,2,35,1,11
	.byte	'CSS2',0,1
	.word	521
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	521
	.byte	1,0,2,35,1,11
	.byte	'USRINFO',0,2
	.word	1072
	.byte	16,0,2,35,2,0,26
	.byte	'Ifx_SCU_RSTCON2_Bits',0,6,243,7,3
	.word	41274
	.byte	10
	.byte	'_Ifx_SCU_RSTCON_Bits',0,6,246,7,16,4,11
	.byte	'ESR0',0,1
	.word	521
	.byte	2,6,2,35,0,11
	.byte	'ESR1',0,1
	.word	521
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	521
	.byte	2,2,2,35,0,11
	.byte	'SMU',0,1
	.word	521
	.byte	2,0,2,35,0,11
	.byte	'SW',0,1
	.word	521
	.byte	2,6,2,35,1,11
	.byte	'STM0',0,1
	.word	521
	.byte	2,4,2,35,1,11
	.byte	'STM1',0,1
	.word	521
	.byte	2,2,2,35,1,11
	.byte	'STM2',0,1
	.word	521
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1072
	.byte	16,0,2,35,2,0,26
	.byte	'Ifx_SCU_RSTCON_Bits',0,6,129,8,3
	.word	41483
	.byte	10
	.byte	'_Ifx_SCU_RSTSTAT_Bits',0,6,132,8,16,4,11
	.byte	'ESR0',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'ESR1',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'SMU',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'SW',0,1
	.word	521
	.byte	1,3,2,35,0,11
	.byte	'STM0',0,1
	.word	521
	.byte	1,2,2,35,0,11
	.byte	'STM1',0,1
	.word	521
	.byte	1,1,2,35,0,11
	.byte	'STM2',0,1
	.word	521
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	521
	.byte	8,0,2,35,1,11
	.byte	'PORST',0,1
	.word	521
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	521
	.byte	1,6,2,35,2,11
	.byte	'CB0',0,1
	.word	521
	.byte	1,5,2,35,2,11
	.byte	'CB1',0,1
	.word	521
	.byte	1,4,2,35,2,11
	.byte	'CB3',0,1
	.word	521
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	521
	.byte	2,1,2,35,2,11
	.byte	'EVR13',0,1
	.word	521
	.byte	1,0,2,35,2,11
	.byte	'EVR33',0,1
	.word	521
	.byte	1,7,2,35,3,11
	.byte	'SWD',0,1
	.word	521
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	521
	.byte	2,4,2,35,3,11
	.byte	'STBYR',0,1
	.word	521
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	521
	.byte	3,0,2,35,3,0,26
	.byte	'Ifx_SCU_RSTSTAT_Bits',0,6,155,8,3
	.word	41694
	.byte	10
	.byte	'_Ifx_SCU_SAFECON_Bits',0,6,158,8,16,4,11
	.byte	'HBT',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	498
	.byte	31,0,2,35,0,0,26
	.byte	'Ifx_SCU_SAFECON_Bits',0,6,162,8,3
	.word	42126
	.byte	10
	.byte	'_Ifx_SCU_STSTAT_Bits',0,6,165,8,16,4,11
	.byte	'HWCFG',0,1
	.word	521
	.byte	8,0,2,35,0,11
	.byte	'FTM',0,1
	.word	521
	.byte	7,1,2,35,1,11
	.byte	'MODE',0,1
	.word	521
	.byte	1,0,2,35,1,11
	.byte	'FCBAE',0,1
	.word	521
	.byte	1,7,2,35,2,11
	.byte	'LUDIS',0,1
	.word	521
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	521
	.byte	1,5,2,35,2,11
	.byte	'TRSTL',0,1
	.word	521
	.byte	1,4,2,35,2,11
	.byte	'SPDEN',0,1
	.word	521
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	521
	.byte	3,0,2,35,2,11
	.byte	'RAMINT',0,1
	.word	521
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	521
	.byte	7,0,2,35,3,0,26
	.byte	'Ifx_SCU_STSTAT_Bits',0,6,178,8,3
	.word	42222
	.byte	10
	.byte	'_Ifx_SCU_SWRSTCON_Bits',0,6,181,8,16,4,11
	.byte	'reserved_0',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'SWRSTREQ',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	498
	.byte	30,0,2,35,0,0,26
	.byte	'Ifx_SCU_SWRSTCON_Bits',0,6,186,8,3
	.word	42482
	.byte	10
	.byte	'_Ifx_SCU_SYSCON_Bits',0,6,189,8,16,4,11
	.byte	'CCTRIG0',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'RAMINTM',0,1
	.word	521
	.byte	2,4,2,35,0,11
	.byte	'SETLUDIS',0,1
	.word	521
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	521
	.byte	3,0,2,35,0,11
	.byte	'DATM',0,1
	.word	521
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,4
	.word	498
	.byte	23,0,2,35,0,0,26
	.byte	'Ifx_SCU_SYSCON_Bits',0,6,198,8,3
	.word	42607
	.byte	10
	.byte	'_Ifx_SCU_TRAPCLR_Bits',0,6,201,8,16,4,11
	.byte	'ESR0T',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	498
	.byte	28,0,2,35,0,0,26
	.byte	'Ifx_SCU_TRAPCLR_Bits',0,6,208,8,3
	.word	42804
	.byte	10
	.byte	'_Ifx_SCU_TRAPDIS_Bits',0,6,211,8,16,4,11
	.byte	'ESR0T',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	498
	.byte	28,0,2,35,0,0,26
	.byte	'Ifx_SCU_TRAPDIS_Bits',0,6,218,8,3
	.word	42957
	.byte	10
	.byte	'_Ifx_SCU_TRAPSET_Bits',0,6,221,8,16,4,11
	.byte	'ESR0T',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	498
	.byte	28,0,2,35,0,0,26
	.byte	'Ifx_SCU_TRAPSET_Bits',0,6,228,8,3
	.word	43110
	.byte	10
	.byte	'_Ifx_SCU_TRAPSTAT_Bits',0,6,231,8,16,4,11
	.byte	'ESR0T',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	498
	.byte	28,0,2,35,0,0,26
	.byte	'Ifx_SCU_TRAPSTAT_Bits',0,6,238,8,3
	.word	43263
	.byte	26
	.byte	'Ifx_SCU_WDTCPU_CON0_Bits',0,6,247,8,3
	.word	936
	.byte	26
	.byte	'Ifx_SCU_WDTCPU_CON1_Bits',0,6,134,9,3
	.word	1094
	.byte	26
	.byte	'Ifx_SCU_WDTCPU_SR_Bits',0,6,150,9,3
	.word	1338
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON0_Bits',0,6,153,9,16,4,11
	.byte	'ENDINIT',0,4
	.word	920
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	920
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	920
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	920
	.byte	16,0,2,35,0,0,26
	.byte	'Ifx_SCU_WDTS_CON0_Bits',0,6,159,9,3
	.word	43518
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON1_Bits',0,6,162,9,16,4,11
	.byte	'CLRIRF',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'IR0',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	521
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	521
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	521
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	521
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	521
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	521
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1072
	.byte	16,0,2,35,2,0,26
	.byte	'Ifx_SCU_WDTS_CON1_Bits',0,6,175,9,3
	.word	43644
	.byte	10
	.byte	'_Ifx_SCU_WDTS_SR_Bits',0,6,178,9,16,4,11
	.byte	'AE',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	521
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	521
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	521
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	521
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	521
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	521
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	1072
	.byte	16,0,2,35,2,0,26
	.byte	'Ifx_SCU_WDTS_SR_Bits',0,6,191,9,3
	.word	43896
	.byte	12,6,199,9,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25928
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_ACCEN0',0,6,204,9,3
	.word	44115
	.byte	12,6,207,9,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26485
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_ACCEN1',0,6,212,9,3
	.word	44179
	.byte	12,6,215,9,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26562
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_ARSTDIS',0,6,220,9,3
	.word	44243
	.byte	12,6,223,9,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26698
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_CCUCON0',0,6,228,9,3
	.word	44308
	.byte	12,6,231,9,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26978
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_CCUCON1',0,6,236,9,3
	.word	44373
	.byte	12,6,239,9,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27216
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_CCUCON2',0,6,244,9,3
	.word	44438
	.byte	12,6,247,9,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27344
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_CCUCON3',0,6,252,9,3
	.word	44503
	.byte	12,6,255,9,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27587
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_CCUCON4',0,6,132,10,3
	.word	44568
	.byte	12,6,135,10,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27822
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_CCUCON5',0,6,140,10,3
	.word	44633
	.byte	12,6,143,10,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27950
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_CCUCON6',0,6,148,10,3
	.word	44698
	.byte	12,6,151,10,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28050
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_CCUCON7',0,6,156,10,3
	.word	44763
	.byte	12,6,159,10,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28150
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_CHIPID',0,6,164,10,3
	.word	44828
	.byte	12,6,167,10,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28358
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_DTSCON',0,6,172,10,3
	.word	44892
	.byte	12,6,175,10,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28523
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_DTSLIM',0,6,180,10,3
	.word	44956
	.byte	12,6,183,10,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28706
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_DTSSTAT',0,6,188,10,3
	.word	45020
	.byte	12,6,191,10,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28860
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_EICR',0,6,196,10,3
	.word	45085
	.byte	12,6,199,10,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29224
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_EIFR',0,6,204,10,3
	.word	45147
	.byte	12,6,207,10,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29435
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_EMSR',0,6,212,10,3
	.word	45209
	.byte	12,6,215,10,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29687
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_ESRCFG',0,6,220,10,3
	.word	45271
	.byte	12,6,223,10,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29805
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_ESROCFG',0,6,228,10,3
	.word	45335
	.byte	12,6,231,10,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29916
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_EVR13CON',0,6,236,10,3
	.word	45400
	.byte	12,6,239,10,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30079
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_EVR33CON',0,6,244,10,3
	.word	45466
	.byte	12,6,247,10,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30242
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_EVRADCSTAT',0,6,252,10,3
	.word	45532
	.byte	12,6,255,10,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30400
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_EVRDVSTAT',0,6,132,11,3
	.word	45600
	.byte	12,6,135,11,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30565
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_EVRMONCTRL',0,6,140,11,3
	.word	45667
	.byte	12,6,143,11,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30894
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_EVROSCCTRL',0,6,148,11,3
	.word	45735
	.byte	12,6,151,11,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31115
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_EVROVMON',0,6,156,11,3
	.word	45803
	.byte	12,6,159,11,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31278
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_EVRRSTCON',0,6,164,11,3
	.word	45869
	.byte	12,6,167,11,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31550
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_EVRSDCOEFF1',0,6,172,11,3
	.word	45936
	.byte	12,6,175,11,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31703
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_EVRSDCOEFF2',0,6,180,11,3
	.word	46005
	.byte	12,6,183,11,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31859
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_EVRSDCOEFF3',0,6,188,11,3
	.word	46074
	.byte	12,6,191,11,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32021
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_EVRSDCOEFF4',0,6,196,11,3
	.word	46143
	.byte	12,6,199,11,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32164
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_EVRSDCOEFF5',0,6,204,11,3
	.word	46212
	.byte	12,6,207,11,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32329
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_EVRSDCOEFF6',0,6,212,11,3
	.word	46281
	.byte	12,6,215,11,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32474
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_EVRSDCTRL1',0,6,220,11,3
	.word	46350
	.byte	12,6,223,11,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32655
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_EVRSDCTRL2',0,6,228,11,3
	.word	46418
	.byte	12,6,231,11,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32829
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_EVRSDCTRL3',0,6,236,11,3
	.word	46486
	.byte	12,6,239,11,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32989
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_EVRSDCTRL4',0,6,244,11,3
	.word	46554
	.byte	12,6,247,11,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33133
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_EVRSTAT',0,6,252,11,3
	.word	46622
	.byte	12,6,255,11,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33407
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_EVRTRIM',0,6,132,12,3
	.word	46687
	.byte	12,6,135,12,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33546
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_EVRUVMON',0,6,140,12,3
	.word	46752
	.byte	12,6,143,12,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33709
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_EXTCON',0,6,148,12,3
	.word	46818
	.byte	12,6,151,12,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33927
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_FDR',0,6,156,12,3
	.word	46882
	.byte	12,6,159,12,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34090
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_FMR',0,6,164,12,3
	.word	46943
	.byte	12,6,167,12,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34426
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_ID',0,6,172,12,3
	.word	47004
	.byte	12,6,175,12,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34533
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_IGCR',0,6,180,12,3
	.word	47064
	.byte	12,6,183,12,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34985
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_IN',0,6,188,12,3
	.word	47126
	.byte	12,6,191,12,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35084
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_IOCR',0,6,196,12,3
	.word	47186
	.byte	12,6,199,12,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35234
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_LBISTCTRL0',0,6,204,12,3
	.word	47248
	.byte	12,6,207,12,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35383
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_LBISTCTRL1',0,6,212,12,3
	.word	47316
	.byte	12,6,215,12,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35544
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_LBISTCTRL2',0,6,220,12,3
	.word	47384
	.byte	12,6,223,12,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35674
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_LCLCON',0,6,228,12,3
	.word	47452
	.byte	12,6,231,12,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35806
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_LCLTEST',0,6,236,12,3
	.word	47516
	.byte	12,6,239,12,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35921
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_MANID',0,6,244,12,3
	.word	47581
	.byte	12,6,247,12,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36032
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_OMR',0,6,252,12,3
	.word	47644
	.byte	12,6,255,12,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36190
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_OSCCON',0,6,132,13,3
	.word	47705
	.byte	12,6,135,13,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36602
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_OUT',0,6,140,13,3
	.word	47769
	.byte	12,6,143,13,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36703
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_OVCCON',0,6,148,13,3
	.word	47830
	.byte	12,6,151,13,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36970
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_OVCENABLE',0,6,156,13,3
	.word	47894
	.byte	12,6,159,13,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37106
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_PDISC',0,6,164,13,3
	.word	47961
	.byte	12,6,167,13,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37217
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_PDR',0,6,172,13,3
	.word	48024
	.byte	12,6,175,13,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37350
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_PDRR',0,6,180,13,3
	.word	48085
	.byte	12,6,183,13,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37553
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_PLLCON0',0,6,188,13,3
	.word	48147
	.byte	12,6,191,13,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37909
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_PLLCON1',0,6,196,13,3
	.word	48212
	.byte	12,6,199,13,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38087
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_PLLCON2',0,6,204,13,3
	.word	48277
	.byte	12,6,207,13,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38187
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_PLLERAYCON0',0,6,212,13,3
	.word	48342
	.byte	12,6,215,13,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38557
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_PLLERAYCON1',0,6,220,13,3
	.word	48411
	.byte	12,6,223,13,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38743
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_PLLERAYSTAT',0,6,228,13,3
	.word	48480
	.byte	12,6,231,13,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38941
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_PLLSTAT',0,6,236,13,3
	.word	48549
	.byte	12,6,239,13,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39174
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_PMCSR',0,6,244,13,3
	.word	48614
	.byte	12,6,247,13,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39326
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_PMSWCR0',0,6,252,13,3
	.word	48677
	.byte	12,6,255,13,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39893
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_PMSWCR1',0,6,132,14,3
	.word	48742
	.byte	12,6,135,14,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40187
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_PMSWCR2',0,6,140,14,3
	.word	48807
	.byte	12,6,143,14,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40465
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_PMSWSTAT',0,6,148,14,3
	.word	48872
	.byte	12,6,151,14,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40961
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_PMSWSTATCLR',0,6,156,14,3
	.word	48938
	.byte	12,6,159,14,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41483
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_RSTCON',0,6,164,14,3
	.word	49007
	.byte	12,6,167,14,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41274
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_RSTCON2',0,6,172,14,3
	.word	49071
	.byte	12,6,175,14,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41694
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_RSTSTAT',0,6,180,14,3
	.word	49136
	.byte	12,6,183,14,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42126
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_SAFECON',0,6,188,14,3
	.word	49201
	.byte	12,6,191,14,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42222
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_STSTAT',0,6,196,14,3
	.word	49266
	.byte	12,6,199,14,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42482
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_SWRSTCON',0,6,204,14,3
	.word	49330
	.byte	12,6,207,14,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42607
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_SYSCON',0,6,212,14,3
	.word	49396
	.byte	12,6,215,14,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42804
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_TRAPCLR',0,6,220,14,3
	.word	49460
	.byte	12,6,223,14,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42957
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_TRAPDIS',0,6,228,14,3
	.word	49525
	.byte	12,6,231,14,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43110
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_TRAPSET',0,6,236,14,3
	.word	49590
	.byte	12,6,239,14,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43263
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_TRAPSTAT',0,6,244,14,3
	.word	49655
	.byte	26
	.byte	'Ifx_SCU_WDTCPU_CON0',0,6,252,14,3
	.word	1032
	.byte	26
	.byte	'Ifx_SCU_WDTCPU_CON1',0,6,132,15,3
	.word	1298
	.byte	26
	.byte	'Ifx_SCU_WDTCPU_SR',0,6,140,15,3
	.word	1529
	.byte	12,6,143,15,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43518
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_WDTS_CON0',0,6,148,15,3
	.word	49806
	.byte	12,6,151,15,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43644
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_WDTS_CON1',0,6,156,15,3
	.word	49873
	.byte	12,6,159,15,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43896
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_WDTS_SR',0,6,164,15,3
	.word	49940
	.byte	14
	.word	1569
	.byte	26
	.byte	'Ifx_SCU_WDTCPU',0,6,180,15,3
	.word	50005
	.byte	10
	.byte	'_Ifx_SCU_WDTS',0,6,183,15,25,12,13
	.byte	'CON0',0
	.word	49806
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	49873
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	49940
	.byte	4,2,35,8,0,14
	.word	50034
	.byte	26
	.byte	'Ifx_SCU_WDTS',0,6,188,15,3
	.word	50095
	.byte	18,8
	.word	45271
	.byte	19,1,0,18,20
	.word	521
	.byte	19,19,0,18,8
	.word	48614
	.byte	19,1,0,14
	.word	50034
	.byte	18,24
	.word	1569
	.byte	19,1,0,14
	.word	50154
	.byte	18,28
	.word	521
	.byte	19,27,0,18,16
	.word	45085
	.byte	19,3,0,18,16
	.word	47064
	.byte	19,3,0,18,180,3
	.word	521
	.byte	19,179,3,0,10
	.byte	'_Ifx_SCU',0,6,201,15,25,128,8,13
	.byte	'reserved_0',0
	.word	5078
	.byte	8,2,35,0,13
	.byte	'ID',0
	.word	47004
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	3259
	.byte	4,2,35,12,13
	.byte	'OSCCON',0
	.word	47705
	.byte	4,2,35,16,13
	.byte	'PLLSTAT',0
	.word	48549
	.byte	4,2,35,20,13
	.byte	'PLLCON0',0
	.word	48147
	.byte	4,2,35,24,13
	.byte	'PLLCON1',0
	.word	48212
	.byte	4,2,35,28,13
	.byte	'PLLCON2',0
	.word	48277
	.byte	4,2,35,32,13
	.byte	'PLLERAYSTAT',0
	.word	48480
	.byte	4,2,35,36,13
	.byte	'PLLERAYCON0',0
	.word	48342
	.byte	4,2,35,40,13
	.byte	'PLLERAYCON1',0
	.word	48411
	.byte	4,2,35,44,13
	.byte	'CCUCON0',0
	.word	44308
	.byte	4,2,35,48,13
	.byte	'CCUCON1',0
	.word	44373
	.byte	4,2,35,52,13
	.byte	'FDR',0
	.word	46882
	.byte	4,2,35,56,13
	.byte	'EXTCON',0
	.word	46818
	.byte	4,2,35,60,13
	.byte	'CCUCON2',0
	.word	44438
	.byte	4,2,35,64,13
	.byte	'CCUCON3',0
	.word	44503
	.byte	4,2,35,68,13
	.byte	'CCUCON4',0
	.word	44568
	.byte	4,2,35,72,13
	.byte	'CCUCON5',0
	.word	44633
	.byte	4,2,35,76,13
	.byte	'RSTSTAT',0
	.word	49136
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	3259
	.byte	4,2,35,84,13
	.byte	'RSTCON',0
	.word	49007
	.byte	4,2,35,88,13
	.byte	'ARSTDIS',0
	.word	44243
	.byte	4,2,35,92,13
	.byte	'SWRSTCON',0
	.word	49330
	.byte	4,2,35,96,13
	.byte	'RSTCON2',0
	.word	49071
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	3259
	.byte	4,2,35,104,13
	.byte	'EVRRSTCON',0
	.word	45869
	.byte	4,2,35,108,13
	.byte	'ESRCFG',0
	.word	50122
	.byte	8,2,35,112,13
	.byte	'ESROCFG',0
	.word	45335
	.byte	4,2,35,120,13
	.byte	'SYSCON',0
	.word	49396
	.byte	4,2,35,124,13
	.byte	'CCUCON6',0
	.word	44698
	.byte	4,3,35,128,1,13
	.byte	'CCUCON7',0
	.word	44763
	.byte	4,3,35,132,1,13
	.byte	'reserved_88',0
	.word	50131
	.byte	20,3,35,136,1,13
	.byte	'PDR',0
	.word	48024
	.byte	4,3,35,156,1,13
	.byte	'IOCR',0
	.word	47186
	.byte	4,3,35,160,1,13
	.byte	'OUT',0
	.word	47769
	.byte	4,3,35,164,1,13
	.byte	'OMR',0
	.word	47644
	.byte	4,3,35,168,1,13
	.byte	'IN',0
	.word	47126
	.byte	4,3,35,172,1,13
	.byte	'EVRSTAT',0
	.word	46622
	.byte	4,3,35,176,1,13
	.byte	'EVRDVSTAT',0
	.word	45600
	.byte	4,3,35,180,1,13
	.byte	'EVR13CON',0
	.word	45400
	.byte	4,3,35,184,1,13
	.byte	'EVR33CON',0
	.word	45466
	.byte	4,3,35,188,1,13
	.byte	'STSTAT',0
	.word	49266
	.byte	4,3,35,192,1,13
	.byte	'reserved_C4',0
	.word	3259
	.byte	4,3,35,196,1,13
	.byte	'PMSWCR0',0
	.word	48677
	.byte	4,3,35,200,1,13
	.byte	'PMSWSTAT',0
	.word	48872
	.byte	4,3,35,204,1,13
	.byte	'PMSWSTATCLR',0
	.word	48938
	.byte	4,3,35,208,1,13
	.byte	'PMCSR',0
	.word	50140
	.byte	8,3,35,212,1,13
	.byte	'reserved_DC',0
	.word	3259
	.byte	4,3,35,220,1,13
	.byte	'DTSSTAT',0
	.word	45020
	.byte	4,3,35,224,1,13
	.byte	'DTSCON',0
	.word	44892
	.byte	4,3,35,228,1,13
	.byte	'PMSWCR1',0
	.word	48742
	.byte	4,3,35,232,1,13
	.byte	'PMSWCR2',0
	.word	48807
	.byte	4,3,35,236,1,13
	.byte	'WDTS',0
	.word	50149
	.byte	12,3,35,240,1,13
	.byte	'EMSR',0
	.word	45209
	.byte	4,3,35,252,1,13
	.byte	'WDTCPU',0
	.word	50163
	.byte	24,3,35,128,2,13
	.byte	'reserved_118',0
	.word	5418
	.byte	12,3,35,152,2,13
	.byte	'TRAPSTAT',0
	.word	49655
	.byte	4,3,35,164,2,13
	.byte	'TRAPSET',0
	.word	49590
	.byte	4,3,35,168,2,13
	.byte	'TRAPCLR',0
	.word	49460
	.byte	4,3,35,172,2,13
	.byte	'TRAPDIS',0
	.word	49525
	.byte	4,3,35,176,2,13
	.byte	'reserved_134',0
	.word	3259
	.byte	4,3,35,180,2,13
	.byte	'LCLCON1',0
	.word	47452
	.byte	4,3,35,184,2,13
	.byte	'LCLTEST',0
	.word	47516
	.byte	4,3,35,188,2,13
	.byte	'CHIPID',0
	.word	44828
	.byte	4,3,35,192,2,13
	.byte	'MANID',0
	.word	47581
	.byte	4,3,35,196,2,13
	.byte	'reserved_148',0
	.word	5078
	.byte	8,3,35,200,2,13
	.byte	'SAFECON',0
	.word	49201
	.byte	4,3,35,208,2,13
	.byte	'reserved_154',0
	.word	20710
	.byte	16,3,35,212,2,13
	.byte	'LBISTCTRL0',0
	.word	47248
	.byte	4,3,35,228,2,13
	.byte	'LBISTCTRL1',0
	.word	47316
	.byte	4,3,35,232,2,13
	.byte	'LBISTCTRL2',0
	.word	47384
	.byte	4,3,35,236,2,13
	.byte	'reserved_170',0
	.word	50168
	.byte	28,3,35,240,2,13
	.byte	'PDISC',0
	.word	47961
	.byte	4,3,35,140,3,13
	.byte	'reserved_190',0
	.word	5078
	.byte	8,3,35,144,3,13
	.byte	'EVRTRIM',0
	.word	46687
	.byte	4,3,35,152,3,13
	.byte	'EVRADCSTAT',0
	.word	45532
	.byte	4,3,35,156,3,13
	.byte	'EVRUVMON',0
	.word	46752
	.byte	4,3,35,160,3,13
	.byte	'EVROVMON',0
	.word	45803
	.byte	4,3,35,164,3,13
	.byte	'EVRMONCTRL',0
	.word	45667
	.byte	4,3,35,168,3,13
	.byte	'reserved_1AC',0
	.word	3259
	.byte	4,3,35,172,3,13
	.byte	'EVRSDCTRL1',0
	.word	46350
	.byte	4,3,35,176,3,13
	.byte	'EVRSDCTRL2',0
	.word	46418
	.byte	4,3,35,180,3,13
	.byte	'EVRSDCTRL3',0
	.word	46486
	.byte	4,3,35,184,3,13
	.byte	'EVRSDCTRL4',0
	.word	46554
	.byte	4,3,35,188,3,13
	.byte	'EVRSDCOEFF1',0
	.word	45936
	.byte	4,3,35,192,3,13
	.byte	'EVRSDCOEFF2',0
	.word	46005
	.byte	4,3,35,196,3,13
	.byte	'EVRSDCOEFF3',0
	.word	46074
	.byte	4,3,35,200,3,13
	.byte	'EVRSDCOEFF4',0
	.word	46143
	.byte	4,3,35,204,3,13
	.byte	'EVRSDCOEFF5',0
	.word	46212
	.byte	4,3,35,208,3,13
	.byte	'EVRSDCOEFF6',0
	.word	46281
	.byte	4,3,35,212,3,13
	.byte	'EVROSCCTRL',0
	.word	45735
	.byte	4,3,35,216,3,13
	.byte	'reserved_1DC',0
	.word	3259
	.byte	4,3,35,220,3,13
	.byte	'OVCENABLE',0
	.word	47894
	.byte	4,3,35,224,3,13
	.byte	'OVCCON',0
	.word	47830
	.byte	4,3,35,228,3,13
	.byte	'reserved_1E8',0
	.word	20345
	.byte	40,3,35,232,3,13
	.byte	'EICR',0
	.word	50177
	.byte	16,3,35,144,4,13
	.byte	'EIFR',0
	.word	45147
	.byte	4,3,35,160,4,13
	.byte	'FMR',0
	.word	46943
	.byte	4,3,35,164,4,13
	.byte	'PDRR',0
	.word	48085
	.byte	4,3,35,168,4,13
	.byte	'IGCR',0
	.word	50186
	.byte	16,3,35,172,4,13
	.byte	'reserved_23C',0
	.word	3259
	.byte	4,3,35,188,4,13
	.byte	'DTSLIM',0
	.word	44956
	.byte	4,3,35,192,4,13
	.byte	'reserved_244',0
	.word	50195
	.byte	180,3,3,35,196,4,13
	.byte	'ACCEN1',0
	.word	44179
	.byte	4,3,35,248,7,13
	.byte	'ACCEN0',0
	.word	44115
	.byte	4,3,35,252,7,0,14
	.word	50206
	.byte	26
	.byte	'Ifx_SCU',0,6,181,16,3
	.word	52196
	.byte	10
	.byte	'_Ifx_CPU_A_Bits',0,20,45,16,4,11
	.byte	'ADDR',0,4
	.word	920
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_CPU_A_Bits',0,20,48,3
	.word	52218
	.byte	10
	.byte	'_Ifx_CPU_BIV_Bits',0,20,51,16,4,11
	.byte	'VSS',0,4
	.word	920
	.byte	1,31,2,35,0,11
	.byte	'BIV',0,4
	.word	920
	.byte	31,0,2,35,0,0,26
	.byte	'Ifx_CPU_BIV_Bits',0,20,55,3
	.word	52279
	.byte	10
	.byte	'_Ifx_CPU_BTV_Bits',0,20,58,16,4,11
	.byte	'reserved_0',0,4
	.word	920
	.byte	1,31,2,35,0,11
	.byte	'BTV',0,4
	.word	920
	.byte	31,0,2,35,0,0,26
	.byte	'Ifx_CPU_BTV_Bits',0,20,62,3
	.word	52358
	.byte	10
	.byte	'_Ifx_CPU_CCNT_Bits',0,20,65,16,4,11
	.byte	'CountValue',0,4
	.word	920
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	920
	.byte	1,0,2,35,0,0,26
	.byte	'Ifx_CPU_CCNT_Bits',0,20,69,3
	.word	52444
	.byte	10
	.byte	'_Ifx_CPU_CCTRL_Bits',0,20,72,16,4,11
	.byte	'CM',0,4
	.word	920
	.byte	1,31,2,35,0,11
	.byte	'CE',0,4
	.word	920
	.byte	1,30,2,35,0,11
	.byte	'M1',0,4
	.word	920
	.byte	3,27,2,35,0,11
	.byte	'M2',0,4
	.word	920
	.byte	3,24,2,35,0,11
	.byte	'M3',0,4
	.word	920
	.byte	3,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	920
	.byte	21,0,2,35,0,0,26
	.byte	'Ifx_CPU_CCTRL_Bits',0,20,80,3
	.word	52533
	.byte	10
	.byte	'_Ifx_CPU_COMPAT_Bits',0,20,83,16,4,11
	.byte	'reserved_0',0,4
	.word	920
	.byte	3,29,2,35,0,11
	.byte	'RM',0,4
	.word	920
	.byte	1,28,2,35,0,11
	.byte	'SP',0,4
	.word	920
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	920
	.byte	27,0,2,35,0,0,26
	.byte	'Ifx_CPU_COMPAT_Bits',0,20,89,3
	.word	52679
	.byte	10
	.byte	'_Ifx_CPU_CORE_ID_Bits',0,20,92,16,4,11
	.byte	'CORE_ID',0,4
	.word	920
	.byte	3,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	920
	.byte	29,0,2,35,0,0,26
	.byte	'Ifx_CPU_CORE_ID_Bits',0,20,96,3
	.word	52806
	.byte	10
	.byte	'_Ifx_CPU_CPR_L_Bits',0,20,99,16,4,11
	.byte	'reserved_0',0,4
	.word	920
	.byte	3,29,2,35,0,11
	.byte	'LOWBND',0,4
	.word	920
	.byte	29,0,2,35,0,0,26
	.byte	'Ifx_CPU_CPR_L_Bits',0,20,103,3
	.word	52904
	.byte	10
	.byte	'_Ifx_CPU_CPR_U_Bits',0,20,106,16,4,11
	.byte	'reserved_0',0,4
	.word	920
	.byte	3,29,2,35,0,11
	.byte	'UPPBND',0,4
	.word	920
	.byte	29,0,2,35,0,0,26
	.byte	'Ifx_CPU_CPR_U_Bits',0,20,110,3
	.word	52997
	.byte	10
	.byte	'_Ifx_CPU_CPU_ID_Bits',0,20,113,16,4,11
	.byte	'MODREV',0,4
	.word	920
	.byte	8,24,2,35,0,11
	.byte	'MOD_32B',0,4
	.word	920
	.byte	8,16,2,35,0,11
	.byte	'MOD',0,4
	.word	920
	.byte	16,0,2,35,0,0,26
	.byte	'Ifx_CPU_CPU_ID_Bits',0,20,118,3
	.word	53090
	.byte	10
	.byte	'_Ifx_CPU_CPXE_Bits',0,20,121,16,4,11
	.byte	'XE',0,4
	.word	920
	.byte	8,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	920
	.byte	24,0,2,35,0,0,26
	.byte	'Ifx_CPU_CPXE_Bits',0,20,125,3
	.word	53197
	.byte	10
	.byte	'_Ifx_CPU_CREVT_Bits',0,20,128,1,16,4,11
	.byte	'EVTA',0,4
	.word	920
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	920
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	920
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	920
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	920
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	920
	.byte	24,0,2,35,0,0,26
	.byte	'Ifx_CPU_CREVT_Bits',0,20,136,1,3
	.word	53284
	.byte	10
	.byte	'_Ifx_CPU_CUS_ID_Bits',0,20,139,1,16,4,11
	.byte	'CID',0,4
	.word	920
	.byte	3,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	920
	.byte	29,0,2,35,0,0,26
	.byte	'Ifx_CPU_CUS_ID_Bits',0,20,143,1,3
	.word	53438
	.byte	10
	.byte	'_Ifx_CPU_D_Bits',0,20,146,1,16,4,11
	.byte	'DATA',0,4
	.word	920
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_CPU_D_Bits',0,20,149,1,3
	.word	53532
	.byte	10
	.byte	'_Ifx_CPU_DATR_Bits',0,20,152,1,16,4,11
	.byte	'reserved_0',0,4
	.word	920
	.byte	3,29,2,35,0,11
	.byte	'SBE',0,4
	.word	920
	.byte	1,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	920
	.byte	5,23,2,35,0,11
	.byte	'CWE',0,4
	.word	920
	.byte	1,22,2,35,0,11
	.byte	'CFE',0,4
	.word	920
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	920
	.byte	3,18,2,35,0,11
	.byte	'SOE',0,4
	.word	920
	.byte	1,17,2,35,0,11
	.byte	'SME',0,4
	.word	920
	.byte	1,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	920
	.byte	16,0,2,35,0,0,26
	.byte	'Ifx_CPU_DATR_Bits',0,20,163,1,3
	.word	53595
	.byte	10
	.byte	'_Ifx_CPU_DBGSR_Bits',0,20,166,1,16,4,11
	.byte	'DE',0,4
	.word	920
	.byte	1,31,2,35,0,11
	.byte	'HALT',0,4
	.word	920
	.byte	2,29,2,35,0,11
	.byte	'SIH',0,4
	.word	920
	.byte	1,28,2,35,0,11
	.byte	'SUSP',0,4
	.word	920
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	920
	.byte	1,26,2,35,0,11
	.byte	'PREVSUSP',0,4
	.word	920
	.byte	1,25,2,35,0,11
	.byte	'PEVT',0,4
	.word	920
	.byte	1,24,2,35,0,11
	.byte	'EVTSRC',0,4
	.word	920
	.byte	5,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	920
	.byte	19,0,2,35,0,0,26
	.byte	'Ifx_CPU_DBGSR_Bits',0,20,177,1,3
	.word	53813
	.byte	10
	.byte	'_Ifx_CPU_DBGTCR_Bits',0,20,180,1,16,4,11
	.byte	'DTA',0,4
	.word	920
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	920
	.byte	31,0,2,35,0,0,26
	.byte	'Ifx_CPU_DBGTCR_Bits',0,20,184,1,3
	.word	54028
	.byte	10
	.byte	'_Ifx_CPU_DCON0_Bits',0,20,187,1,16,4,11
	.byte	'reserved_0',0,4
	.word	920
	.byte	1,31,2,35,0,11
	.byte	'DCBYP',0,4
	.word	920
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	920
	.byte	30,0,2,35,0,0,26
	.byte	'Ifx_CPU_DCON0_Bits',0,20,192,1,3
	.word	54122
	.byte	10
	.byte	'_Ifx_CPU_DCON2_Bits',0,20,195,1,16,4,11
	.byte	'DCACHE_SZE',0,4
	.word	920
	.byte	16,16,2,35,0,11
	.byte	'DSCRATCH_SZE',0,4
	.word	920
	.byte	16,0,2,35,0,0,26
	.byte	'Ifx_CPU_DCON2_Bits',0,20,199,1,3
	.word	54238
	.byte	10
	.byte	'_Ifx_CPU_DCX_Bits',0,20,202,1,16,4,11
	.byte	'reserved_0',0,4
	.word	920
	.byte	6,26,2,35,0,11
	.byte	'DCXValue',0,4
	.word	920
	.byte	26,0,2,35,0,0,26
	.byte	'Ifx_CPU_DCX_Bits',0,20,206,1,3
	.word	54339
	.byte	10
	.byte	'_Ifx_CPU_DEADD_Bits',0,20,209,1,16,4,11
	.byte	'ERROR_ADDRESS',0,4
	.word	920
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_CPU_DEADD_Bits',0,20,212,1,3
	.word	54432
	.byte	10
	.byte	'_Ifx_CPU_DIEAR_Bits',0,20,215,1,16,4,11
	.byte	'TA',0,4
	.word	920
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_CPU_DIEAR_Bits',0,20,218,1,3
	.word	54512
	.byte	10
	.byte	'_Ifx_CPU_DIETR_Bits',0,20,221,1,16,4,11
	.byte	'IED',0,4
	.word	920
	.byte	1,31,2,35,0,11
	.byte	'IE_T',0,4
	.word	920
	.byte	1,30,2,35,0,11
	.byte	'IE_C',0,4
	.word	920
	.byte	1,29,2,35,0,11
	.byte	'IE_S',0,4
	.word	920
	.byte	1,28,2,35,0,11
	.byte	'IE_BI',0,4
	.word	920
	.byte	1,27,2,35,0,11
	.byte	'E_INFO',0,4
	.word	920
	.byte	6,21,2,35,0,11
	.byte	'IE_DUAL',0,4
	.word	920
	.byte	1,20,2,35,0,11
	.byte	'IE_SP',0,4
	.word	920
	.byte	1,19,2,35,0,11
	.byte	'IE_BS',0,4
	.word	920
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	920
	.byte	18,0,2,35,0,0,26
	.byte	'Ifx_CPU_DIETR_Bits',0,20,233,1,3
	.word	54581
	.byte	10
	.byte	'_Ifx_CPU_DMS_Bits',0,20,236,1,16,4,11
	.byte	'reserved_0',0,4
	.word	920
	.byte	1,31,2,35,0,11
	.byte	'DMSValue',0,4
	.word	920
	.byte	31,0,2,35,0,0,26
	.byte	'Ifx_CPU_DMS_Bits',0,20,240,1,3
	.word	54810
	.byte	10
	.byte	'_Ifx_CPU_DPR_L_Bits',0,20,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	920
	.byte	3,29,2,35,0,11
	.byte	'LOWBND',0,4
	.word	920
	.byte	29,0,2,35,0,0,26
	.byte	'Ifx_CPU_DPR_L_Bits',0,20,247,1,3
	.word	54903
	.byte	10
	.byte	'_Ifx_CPU_DPR_U_Bits',0,20,250,1,16,4,11
	.byte	'reserved_0',0,4
	.word	920
	.byte	3,29,2,35,0,11
	.byte	'UPPBND',0,4
	.word	920
	.byte	29,0,2,35,0,0,26
	.byte	'Ifx_CPU_DPR_U_Bits',0,20,254,1,3
	.word	54998
	.byte	10
	.byte	'_Ifx_CPU_DPRE_Bits',0,20,129,2,16,4,11
	.byte	'RE',0,4
	.word	920
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	920
	.byte	16,0,2,35,0,0,26
	.byte	'Ifx_CPU_DPRE_Bits',0,20,133,2,3
	.word	55093
	.byte	10
	.byte	'_Ifx_CPU_DPWE_Bits',0,20,136,2,16,4,11
	.byte	'WE',0,4
	.word	920
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	920
	.byte	16,0,2,35,0,0,26
	.byte	'Ifx_CPU_DPWE_Bits',0,20,140,2,3
	.word	55183
	.byte	10
	.byte	'_Ifx_CPU_DSTR_Bits',0,20,143,2,16,4,11
	.byte	'SRE',0,4
	.word	920
	.byte	1,31,2,35,0,11
	.byte	'GAE',0,4
	.word	920
	.byte	1,30,2,35,0,11
	.byte	'LBE',0,4
	.word	920
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	920
	.byte	3,26,2,35,0,11
	.byte	'CRE',0,4
	.word	920
	.byte	1,25,2,35,0,11
	.byte	'reserved_7',0,4
	.word	920
	.byte	7,18,2,35,0,11
	.byte	'DTME',0,4
	.word	920
	.byte	1,17,2,35,0,11
	.byte	'LOE',0,4
	.word	920
	.byte	1,16,2,35,0,11
	.byte	'SDE',0,4
	.word	920
	.byte	1,15,2,35,0,11
	.byte	'SCE',0,4
	.word	920
	.byte	1,14,2,35,0,11
	.byte	'CAC',0,4
	.word	920
	.byte	1,13,2,35,0,11
	.byte	'MPE',0,4
	.word	920
	.byte	1,12,2,35,0,11
	.byte	'CLE',0,4
	.word	920
	.byte	1,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	920
	.byte	3,8,2,35,0,11
	.byte	'ALN',0,4
	.word	920
	.byte	1,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	920
	.byte	7,0,2,35,0,0,26
	.byte	'Ifx_CPU_DSTR_Bits',0,20,161,2,3
	.word	55273
	.byte	10
	.byte	'_Ifx_CPU_EXEVT_Bits',0,20,164,2,16,4,11
	.byte	'EVTA',0,4
	.word	920
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	920
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	920
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	920
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	920
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	920
	.byte	24,0,2,35,0,0,26
	.byte	'Ifx_CPU_EXEVT_Bits',0,20,172,2,3
	.word	55597
	.byte	10
	.byte	'_Ifx_CPU_FCX_Bits',0,20,175,2,16,4,11
	.byte	'FCXO',0,4
	.word	920
	.byte	16,16,2,35,0,11
	.byte	'FCXS',0,4
	.word	920
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	920
	.byte	12,0,2,35,0,0,26
	.byte	'Ifx_CPU_FCX_Bits',0,20,180,2,3
	.word	55751
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_CON_Bits',0,20,183,2,16,4,11
	.byte	'TST',0,4
	.word	920
	.byte	1,31,2,35,0,11
	.byte	'TCL',0,4
	.word	920
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	920
	.byte	6,24,2,35,0,11
	.byte	'RM',0,4
	.word	920
	.byte	2,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	920
	.byte	8,14,2,35,0,11
	.byte	'FXE',0,4
	.word	920
	.byte	1,13,2,35,0,11
	.byte	'FUE',0,4
	.word	920
	.byte	1,12,2,35,0,11
	.byte	'FZE',0,4
	.word	920
	.byte	1,11,2,35,0,11
	.byte	'FVE',0,4
	.word	920
	.byte	1,10,2,35,0,11
	.byte	'FIE',0,4
	.word	920
	.byte	1,9,2,35,0,11
	.byte	'reserved_23',0,4
	.word	920
	.byte	3,6,2,35,0,11
	.byte	'FX',0,4
	.word	920
	.byte	1,5,2,35,0,11
	.byte	'FU',0,4
	.word	920
	.byte	1,4,2,35,0,11
	.byte	'FZ',0,4
	.word	920
	.byte	1,3,2,35,0,11
	.byte	'FV',0,4
	.word	920
	.byte	1,2,2,35,0,11
	.byte	'FI',0,4
	.word	920
	.byte	1,1,2,35,0,11
	.byte	'reserved_31',0,4
	.word	920
	.byte	1,0,2,35,0,0,26
	.byte	'Ifx_CPU_FPU_TRAP_CON_Bits',0,20,202,2,3
	.word	55857
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_OPC_Bits',0,20,205,2,16,4,11
	.byte	'OPC',0,4
	.word	920
	.byte	8,24,2,35,0,11
	.byte	'FMT',0,4
	.word	920
	.byte	1,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	920
	.byte	7,16,2,35,0,11
	.byte	'DREG',0,4
	.word	920
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	920
	.byte	12,0,2,35,0,0,26
	.byte	'Ifx_CPU_FPU_TRAP_OPC_Bits',0,20,212,2,3
	.word	56206
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_PC_Bits',0,20,215,2,16,4,11
	.byte	'PC',0,4
	.word	920
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_CPU_FPU_TRAP_PC_Bits',0,20,218,2,3
	.word	56366
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC1_Bits',0,20,221,2,16,4,11
	.byte	'SRC1',0,4
	.word	920
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_CPU_FPU_TRAP_SRC1_Bits',0,20,224,2,3
	.word	56447
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC2_Bits',0,20,227,2,16,4,11
	.byte	'SRC2',0,4
	.word	920
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_CPU_FPU_TRAP_SRC2_Bits',0,20,230,2,3
	.word	56534
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC3_Bits',0,20,233,2,16,4,11
	.byte	'SRC3',0,4
	.word	920
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_CPU_FPU_TRAP_SRC3_Bits',0,20,236,2,3
	.word	56621
	.byte	10
	.byte	'_Ifx_CPU_ICNT_Bits',0,20,239,2,16,4,11
	.byte	'CountValue',0,4
	.word	920
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	920
	.byte	1,0,2,35,0,0,26
	.byte	'Ifx_CPU_ICNT_Bits',0,20,243,2,3
	.word	56708
	.byte	10
	.byte	'_Ifx_CPU_ICR_Bits',0,20,246,2,16,4,11
	.byte	'CCPN',0,4
	.word	920
	.byte	10,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	920
	.byte	5,17,2,35,0,11
	.byte	'IE',0,4
	.word	920
	.byte	1,16,2,35,0,11
	.byte	'PIPN',0,4
	.word	920
	.byte	10,6,2,35,0,11
	.byte	'reserved_26',0,4
	.word	920
	.byte	6,0,2,35,0,0,26
	.byte	'Ifx_CPU_ICR_Bits',0,20,253,2,3
	.word	56799
	.byte	10
	.byte	'_Ifx_CPU_ISP_Bits',0,20,128,3,16,4,11
	.byte	'ISP',0,4
	.word	920
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_CPU_ISP_Bits',0,20,131,3,3
	.word	56942
	.byte	10
	.byte	'_Ifx_CPU_LCX_Bits',0,20,134,3,16,4,11
	.byte	'LCXO',0,4
	.word	920
	.byte	16,16,2,35,0,11
	.byte	'LCXS',0,4
	.word	920
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	920
	.byte	12,0,2,35,0,0,26
	.byte	'Ifx_CPU_LCX_Bits',0,20,139,3,3
	.word	57008
	.byte	10
	.byte	'_Ifx_CPU_M1CNT_Bits',0,20,142,3,16,4,11
	.byte	'CountValue',0,4
	.word	920
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	920
	.byte	1,0,2,35,0,0,26
	.byte	'Ifx_CPU_M1CNT_Bits',0,20,146,3,3
	.word	57114
	.byte	10
	.byte	'_Ifx_CPU_M2CNT_Bits',0,20,149,3,16,4,11
	.byte	'CountValue',0,4
	.word	920
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	920
	.byte	1,0,2,35,0,0,26
	.byte	'Ifx_CPU_M2CNT_Bits',0,20,153,3,3
	.word	57207
	.byte	10
	.byte	'_Ifx_CPU_M3CNT_Bits',0,20,156,3,16,4,11
	.byte	'CountValue',0,4
	.word	920
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	920
	.byte	1,0,2,35,0,0,26
	.byte	'Ifx_CPU_M3CNT_Bits',0,20,160,3,3
	.word	57300
	.byte	10
	.byte	'_Ifx_CPU_PC_Bits',0,20,163,3,16,4,11
	.byte	'reserved_0',0,4
	.word	920
	.byte	1,31,2,35,0,11
	.byte	'PC',0,4
	.word	920
	.byte	31,0,2,35,0,0,26
	.byte	'Ifx_CPU_PC_Bits',0,20,167,3,3
	.word	57393
	.byte	10
	.byte	'_Ifx_CPU_PCON0_Bits',0,20,170,3,16,4,11
	.byte	'reserved_0',0,4
	.word	920
	.byte	1,31,2,35,0,11
	.byte	'PCBYP',0,4
	.word	920
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	920
	.byte	30,0,2,35,0,0,26
	.byte	'Ifx_CPU_PCON0_Bits',0,20,175,3,3
	.word	57478
	.byte	10
	.byte	'_Ifx_CPU_PCON1_Bits',0,20,178,3,16,4,11
	.byte	'PCINV',0,4
	.word	920
	.byte	1,31,2,35,0,11
	.byte	'PBINV',0,4
	.word	920
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	920
	.byte	30,0,2,35,0,0,26
	.byte	'Ifx_CPU_PCON1_Bits',0,20,183,3,3
	.word	57594
	.byte	10
	.byte	'_Ifx_CPU_PCON2_Bits',0,20,186,3,16,4,11
	.byte	'PCACHE_SZE',0,4
	.word	920
	.byte	16,16,2,35,0,11
	.byte	'PSCRATCH_SZE',0,4
	.word	920
	.byte	16,0,2,35,0,0,26
	.byte	'Ifx_CPU_PCON2_Bits',0,20,190,3,3
	.word	57705
	.byte	10
	.byte	'_Ifx_CPU_PCXI_Bits',0,20,193,3,16,4,11
	.byte	'PCXO',0,4
	.word	920
	.byte	16,16,2,35,0,11
	.byte	'PCXS',0,4
	.word	920
	.byte	4,12,2,35,0,11
	.byte	'UL',0,4
	.word	920
	.byte	1,11,2,35,0,11
	.byte	'PIE',0,4
	.word	920
	.byte	1,10,2,35,0,11
	.byte	'PCPN',0,4
	.word	920
	.byte	10,0,2,35,0,0,26
	.byte	'Ifx_CPU_PCXI_Bits',0,20,200,3,3
	.word	57806
	.byte	10
	.byte	'_Ifx_CPU_PIEAR_Bits',0,20,203,3,16,4,11
	.byte	'TA',0,4
	.word	920
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_CPU_PIEAR_Bits',0,20,206,3,3
	.word	57936
	.byte	10
	.byte	'_Ifx_CPU_PIETR_Bits',0,20,209,3,16,4,11
	.byte	'IED',0,4
	.word	920
	.byte	1,31,2,35,0,11
	.byte	'IE_T',0,4
	.word	920
	.byte	1,30,2,35,0,11
	.byte	'IE_C',0,4
	.word	920
	.byte	1,29,2,35,0,11
	.byte	'IE_S',0,4
	.word	920
	.byte	1,28,2,35,0,11
	.byte	'IE_BI',0,4
	.word	920
	.byte	1,27,2,35,0,11
	.byte	'E_INFO',0,4
	.word	920
	.byte	6,21,2,35,0,11
	.byte	'IE_DUAL',0,4
	.word	920
	.byte	1,20,2,35,0,11
	.byte	'IE_SP',0,4
	.word	920
	.byte	1,19,2,35,0,11
	.byte	'IE_BS',0,4
	.word	920
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	920
	.byte	18,0,2,35,0,0,26
	.byte	'Ifx_CPU_PIETR_Bits',0,20,221,3,3
	.word	58005
	.byte	10
	.byte	'_Ifx_CPU_PMA0_Bits',0,20,224,3,16,4,11
	.byte	'reserved_0',0,4
	.word	920
	.byte	13,19,2,35,0,11
	.byte	'DAC',0,4
	.word	920
	.byte	3,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	920
	.byte	16,0,2,35,0,0,26
	.byte	'Ifx_CPU_PMA0_Bits',0,20,229,3,3
	.word	58234
	.byte	10
	.byte	'_Ifx_CPU_PMA1_Bits',0,20,232,3,16,4,11
	.byte	'reserved_0',0,4
	.word	920
	.byte	14,18,2,35,0,11
	.byte	'CAC',0,4
	.word	920
	.byte	2,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	920
	.byte	16,0,2,35,0,0,26
	.byte	'Ifx_CPU_PMA1_Bits',0,20,237,3,3
	.word	58347
	.byte	10
	.byte	'_Ifx_CPU_PMA2_Bits',0,20,240,3,16,4,11
	.byte	'PSI',0,4
	.word	920
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	920
	.byte	16,0,2,35,0,0,26
	.byte	'Ifx_CPU_PMA2_Bits',0,20,244,3,3
	.word	58460
	.byte	10
	.byte	'_Ifx_CPU_PSTR_Bits',0,20,247,3,16,4,11
	.byte	'FRE',0,4
	.word	920
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	920
	.byte	1,30,2,35,0,11
	.byte	'FBE',0,4
	.word	920
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	920
	.byte	9,20,2,35,0,11
	.byte	'FPE',0,4
	.word	920
	.byte	1,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	920
	.byte	1,18,2,35,0,11
	.byte	'FME',0,4
	.word	920
	.byte	1,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	920
	.byte	17,0,2,35,0,0,26
	.byte	'Ifx_CPU_PSTR_Bits',0,20,129,4,3
	.word	58551
	.byte	10
	.byte	'_Ifx_CPU_PSW_Bits',0,20,132,4,16,4,11
	.byte	'CDC',0,4
	.word	920
	.byte	7,25,2,35,0,11
	.byte	'CDE',0,4
	.word	920
	.byte	1,24,2,35,0,11
	.byte	'GW',0,4
	.word	920
	.byte	1,23,2,35,0,11
	.byte	'IS',0,4
	.word	920
	.byte	1,22,2,35,0,11
	.byte	'IO',0,4
	.word	920
	.byte	2,20,2,35,0,11
	.byte	'PRS',0,4
	.word	920
	.byte	2,18,2,35,0,11
	.byte	'S',0,4
	.word	920
	.byte	1,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	920
	.byte	12,5,2,35,0,11
	.byte	'SAV',0,4
	.word	920
	.byte	1,4,2,35,0,11
	.byte	'AV',0,4
	.word	920
	.byte	1,3,2,35,0,11
	.byte	'SV',0,4
	.word	920
	.byte	1,2,2,35,0,11
	.byte	'V',0,4
	.word	920
	.byte	1,1,2,35,0,11
	.byte	'C',0,4
	.word	920
	.byte	1,0,2,35,0,0,26
	.byte	'Ifx_CPU_PSW_Bits',0,20,147,4,3
	.word	58754
	.byte	10
	.byte	'_Ifx_CPU_SEGEN_Bits',0,20,150,4,16,4,11
	.byte	'ADFLIP',0,4
	.word	920
	.byte	8,24,2,35,0,11
	.byte	'ADTYPE',0,4
	.word	920
	.byte	2,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	920
	.byte	21,1,2,35,0,11
	.byte	'AE',0,4
	.word	920
	.byte	1,0,2,35,0,0,26
	.byte	'Ifx_CPU_SEGEN_Bits',0,20,156,4,3
	.word	58997
	.byte	10
	.byte	'_Ifx_CPU_SMACON_Bits',0,20,159,4,16,4,11
	.byte	'PC',0,4
	.word	920
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	920
	.byte	1,30,2,35,0,11
	.byte	'PT',0,4
	.word	920
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	920
	.byte	5,24,2,35,0,11
	.byte	'DC',0,4
	.word	920
	.byte	1,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	920
	.byte	1,22,2,35,0,11
	.byte	'DT',0,4
	.word	920
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	920
	.byte	13,8,2,35,0,11
	.byte	'IODT',0,4
	.word	920
	.byte	1,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	920
	.byte	7,0,2,35,0,0,26
	.byte	'Ifx_CPU_SMACON_Bits',0,20,171,4,3
	.word	59125
	.byte	10
	.byte	'_Ifx_CPU_SPROT_ACCENA_Bits',0,20,174,4,16,4,11
	.byte	'EN',0,4
	.word	498
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_CPU_SPROT_ACCENA_Bits',0,20,177,4,3
	.word	59366
	.byte	10
	.byte	'_Ifx_CPU_SPROT_ACCENB_Bits',0,20,180,4,16,4,11
	.byte	'reserved_0',0,4
	.word	498
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_CPU_SPROT_ACCENB_Bits',0,20,183,4,3
	.word	59449
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,20,186,4,16,4,11
	.byte	'EN',0,4
	.word	498
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,20,189,4,3
	.word	59540
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,20,192,4,16,4,11
	.byte	'reserved_0',0,4
	.word	498
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,20,195,4,3
	.word	59631
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_LA_Bits',0,20,198,4,16,4,11
	.byte	'reserved_0',0,1
	.word	521
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	498
	.byte	27,0,2,35,0,0,26
	.byte	'Ifx_CPU_SPROT_RGN_LA_Bits',0,20,202,4,3
	.word	59730
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_UA_Bits',0,20,205,4,16,4,11
	.byte	'reserved_0',0,1
	.word	521
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	498
	.byte	27,0,2,35,0,0,26
	.byte	'Ifx_CPU_SPROT_RGN_UA_Bits',0,20,209,4,3
	.word	59837
	.byte	10
	.byte	'_Ifx_CPU_SWEVT_Bits',0,20,212,4,16,4,11
	.byte	'EVTA',0,4
	.word	920
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	920
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	920
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	920
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	920
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	920
	.byte	24,0,2,35,0,0,26
	.byte	'Ifx_CPU_SWEVT_Bits',0,20,220,4,3
	.word	59944
	.byte	10
	.byte	'_Ifx_CPU_SYSCON_Bits',0,20,223,4,16,4,11
	.byte	'FCDSF',0,4
	.word	920
	.byte	1,31,2,35,0,11
	.byte	'PROTEN',0,4
	.word	920
	.byte	1,30,2,35,0,11
	.byte	'TPROTEN',0,4
	.word	920
	.byte	1,29,2,35,0,11
	.byte	'IS',0,4
	.word	920
	.byte	1,28,2,35,0,11
	.byte	'IT',0,4
	.word	920
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	920
	.byte	27,0,2,35,0,0,26
	.byte	'Ifx_CPU_SYSCON_Bits',0,20,231,4,3
	.word	60098
	.byte	10
	.byte	'_Ifx_CPU_TASK_ASI_Bits',0,20,234,4,16,4,11
	.byte	'ASI',0,4
	.word	920
	.byte	5,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	920
	.byte	27,0,2,35,0,0,26
	.byte	'Ifx_CPU_TASK_ASI_Bits',0,20,238,4,3
	.word	60259
	.byte	10
	.byte	'_Ifx_CPU_TPS_CON_Bits',0,20,241,4,16,4,11
	.byte	'TEXP0',0,4
	.word	920
	.byte	1,31,2,35,0,11
	.byte	'TEXP1',0,4
	.word	920
	.byte	1,30,2,35,0,11
	.byte	'TEXP2',0,4
	.word	920
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	920
	.byte	13,16,2,35,0,11
	.byte	'TTRAP',0,4
	.word	920
	.byte	1,15,2,35,0,11
	.byte	'reserved_17',0,4
	.word	920
	.byte	15,0,2,35,0,0,26
	.byte	'Ifx_CPU_TPS_CON_Bits',0,20,249,4,3
	.word	60357
	.byte	10
	.byte	'_Ifx_CPU_TPS_TIMER_Bits',0,20,252,4,16,4,11
	.byte	'Timer',0,4
	.word	920
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_CPU_TPS_TIMER_Bits',0,20,255,4,3
	.word	60529
	.byte	10
	.byte	'_Ifx_CPU_TR_ADR_Bits',0,20,130,5,16,4,11
	.byte	'ADDR',0,4
	.word	920
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_CPU_TR_ADR_Bits',0,20,133,5,3
	.word	60609
	.byte	10
	.byte	'_Ifx_CPU_TR_EVT_Bits',0,20,136,5,16,4,11
	.byte	'EVTA',0,4
	.word	920
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	920
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	920
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	920
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	920
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	920
	.byte	4,20,2,35,0,11
	.byte	'TYP',0,4
	.word	920
	.byte	1,19,2,35,0,11
	.byte	'RNG',0,4
	.word	920
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	920
	.byte	1,17,2,35,0,11
	.byte	'ASI_EN',0,4
	.word	920
	.byte	1,16,2,35,0,11
	.byte	'ASI',0,4
	.word	920
	.byte	5,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	920
	.byte	6,5,2,35,0,11
	.byte	'AST',0,4
	.word	920
	.byte	1,4,2,35,0,11
	.byte	'ALD',0,4
	.word	920
	.byte	1,3,2,35,0,11
	.byte	'reserved_29',0,4
	.word	920
	.byte	3,0,2,35,0,0,26
	.byte	'Ifx_CPU_TR_EVT_Bits',0,20,153,5,3
	.word	60682
	.byte	10
	.byte	'_Ifx_CPU_TRIG_ACC_Bits',0,20,156,5,16,4,11
	.byte	'T0',0,4
	.word	920
	.byte	1,31,2,35,0,11
	.byte	'T1',0,4
	.word	920
	.byte	1,30,2,35,0,11
	.byte	'T2',0,4
	.word	920
	.byte	1,29,2,35,0,11
	.byte	'T3',0,4
	.word	920
	.byte	1,28,2,35,0,11
	.byte	'T4',0,4
	.word	920
	.byte	1,27,2,35,0,11
	.byte	'T5',0,4
	.word	920
	.byte	1,26,2,35,0,11
	.byte	'T6',0,4
	.word	920
	.byte	1,25,2,35,0,11
	.byte	'T7',0,4
	.word	920
	.byte	1,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	920
	.byte	24,0,2,35,0,0,26
	.byte	'Ifx_CPU_TRIG_ACC_Bits',0,20,167,5,3
	.word	61000
	.byte	12,20,175,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52218
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_A',0,20,180,5,3
	.word	61195
	.byte	12,20,183,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52279
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_BIV',0,20,188,5,3
	.word	61254
	.byte	12,20,191,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52358
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_BTV',0,20,196,5,3
	.word	61315
	.byte	12,20,199,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52444
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_CCNT',0,20,204,5,3
	.word	61376
	.byte	12,20,207,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52533
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_CCTRL',0,20,212,5,3
	.word	61438
	.byte	12,20,215,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52679
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_COMPAT',0,20,220,5,3
	.word	61501
	.byte	12,20,223,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52806
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_CORE_ID',0,20,228,5,3
	.word	61565
	.byte	12,20,231,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52904
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_CPR_L',0,20,236,5,3
	.word	61630
	.byte	12,20,239,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52997
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_CPR_U',0,20,244,5,3
	.word	61693
	.byte	12,20,247,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53090
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_CPU_ID',0,20,252,5,3
	.word	61756
	.byte	12,20,255,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53197
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_CPXE',0,20,132,6,3
	.word	61820
	.byte	12,20,135,6,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53284
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_CREVT',0,20,140,6,3
	.word	61882
	.byte	12,20,143,6,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53438
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_CUS_ID',0,20,148,6,3
	.word	61945
	.byte	12,20,151,6,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53532
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_D',0,20,156,6,3
	.word	62009
	.byte	12,20,159,6,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53595
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_DATR',0,20,164,6,3
	.word	62068
	.byte	12,20,167,6,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53813
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_DBGSR',0,20,172,6,3
	.word	62130
	.byte	12,20,175,6,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54028
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_DBGTCR',0,20,180,6,3
	.word	62193
	.byte	12,20,183,6,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54122
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_DCON0',0,20,188,6,3
	.word	62257
	.byte	12,20,191,6,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54238
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_DCON2',0,20,196,6,3
	.word	62320
	.byte	12,20,199,6,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54339
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_DCX',0,20,204,6,3
	.word	62383
	.byte	12,20,207,6,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54432
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_DEADD',0,20,212,6,3
	.word	62444
	.byte	12,20,215,6,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54512
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_DIEAR',0,20,220,6,3
	.word	62507
	.byte	12,20,223,6,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54581
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_DIETR',0,20,228,6,3
	.word	62570
	.byte	12,20,231,6,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54810
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_DMS',0,20,236,6,3
	.word	62633
	.byte	12,20,239,6,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54903
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_DPR_L',0,20,244,6,3
	.word	62694
	.byte	12,20,247,6,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54998
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_DPR_U',0,20,252,6,3
	.word	62757
	.byte	12,20,255,6,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55093
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_DPRE',0,20,132,7,3
	.word	62820
	.byte	12,20,135,7,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55183
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_DPWE',0,20,140,7,3
	.word	62882
	.byte	12,20,143,7,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55273
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_DSTR',0,20,148,7,3
	.word	62944
	.byte	12,20,151,7,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55597
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_EXEVT',0,20,156,7,3
	.word	63006
	.byte	12,20,159,7,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55751
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_FCX',0,20,164,7,3
	.word	63069
	.byte	12,20,167,7,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55857
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_FPU_TRAP_CON',0,20,172,7,3
	.word	63130
	.byte	12,20,175,7,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56206
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_FPU_TRAP_OPC',0,20,180,7,3
	.word	63200
	.byte	12,20,183,7,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56366
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_FPU_TRAP_PC',0,20,188,7,3
	.word	63270
	.byte	12,20,191,7,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56447
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_FPU_TRAP_SRC1',0,20,196,7,3
	.word	63339
	.byte	12,20,199,7,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56534
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_FPU_TRAP_SRC2',0,20,204,7,3
	.word	63410
	.byte	12,20,207,7,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56621
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_FPU_TRAP_SRC3',0,20,212,7,3
	.word	63481
	.byte	12,20,215,7,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56708
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_ICNT',0,20,220,7,3
	.word	63552
	.byte	12,20,223,7,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56799
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_ICR',0,20,228,7,3
	.word	63614
	.byte	12,20,231,7,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56942
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_ISP',0,20,236,7,3
	.word	63675
	.byte	12,20,239,7,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57008
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_LCX',0,20,244,7,3
	.word	63736
	.byte	12,20,247,7,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57114
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_M1CNT',0,20,252,7,3
	.word	63797
	.byte	12,20,255,7,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57207
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_M2CNT',0,20,132,8,3
	.word	63860
	.byte	12,20,135,8,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57300
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_M3CNT',0,20,140,8,3
	.word	63923
	.byte	12,20,143,8,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57393
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_PC',0,20,148,8,3
	.word	63986
	.byte	12,20,151,8,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57478
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_PCON0',0,20,156,8,3
	.word	64046
	.byte	12,20,159,8,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57594
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_PCON1',0,20,164,8,3
	.word	64109
	.byte	12,20,167,8,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57705
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_PCON2',0,20,172,8,3
	.word	64172
	.byte	12,20,175,8,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57806
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_PCXI',0,20,180,8,3
	.word	64235
	.byte	12,20,183,8,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57936
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_PIEAR',0,20,188,8,3
	.word	64297
	.byte	12,20,191,8,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58005
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_PIETR',0,20,196,8,3
	.word	64360
	.byte	12,20,199,8,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58234
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_PMA0',0,20,204,8,3
	.word	64423
	.byte	12,20,207,8,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58347
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_PMA1',0,20,212,8,3
	.word	64485
	.byte	12,20,215,8,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58460
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_PMA2',0,20,220,8,3
	.word	64547
	.byte	12,20,223,8,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58551
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_PSTR',0,20,228,8,3
	.word	64609
	.byte	12,20,231,8,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58754
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_PSW',0,20,236,8,3
	.word	64671
	.byte	12,20,239,8,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58997
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_SEGEN',0,20,244,8,3
	.word	64732
	.byte	12,20,247,8,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59125
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_SMACON',0,20,252,8,3
	.word	64795
	.byte	12,20,255,8,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59366
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_SPROT_ACCENA',0,20,132,9,3
	.word	64859
	.byte	12,20,135,9,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59449
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_SPROT_ACCENB',0,20,140,9,3
	.word	64929
	.byte	12,20,143,9,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59540
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA',0,20,148,9,3
	.word	64999
	.byte	12,20,151,9,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59631
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB',0,20,156,9,3
	.word	65073
	.byte	12,20,159,9,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59730
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_SPROT_RGN_LA',0,20,164,9,3
	.word	65147
	.byte	12,20,167,9,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59837
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_SPROT_RGN_UA',0,20,172,9,3
	.word	65217
	.byte	12,20,175,9,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59944
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_SWEVT',0,20,180,9,3
	.word	65287
	.byte	12,20,183,9,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60098
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_SYSCON',0,20,188,9,3
	.word	65350
	.byte	12,20,191,9,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60259
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_TASK_ASI',0,20,196,9,3
	.word	65414
	.byte	12,20,199,9,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60357
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_TPS_CON',0,20,204,9,3
	.word	65480
	.byte	12,20,207,9,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60529
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_TPS_TIMER',0,20,212,9,3
	.word	65545
	.byte	12,20,215,9,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60609
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_TR_ADR',0,20,220,9,3
	.word	65612
	.byte	12,20,223,9,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60682
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_TR_EVT',0,20,228,9,3
	.word	65676
	.byte	12,20,231,9,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61000
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_TRIG_ACC',0,20,236,9,3
	.word	65740
	.byte	10
	.byte	'_Ifx_CPU_CPR',0,20,247,9,25,8,13
	.byte	'L',0
	.word	61630
	.byte	4,2,35,0,13
	.byte	'U',0
	.word	61693
	.byte	4,2,35,4,0,14
	.word	65806
	.byte	26
	.byte	'Ifx_CPU_CPR',0,20,251,9,3
	.word	65848
	.byte	10
	.byte	'_Ifx_CPU_DPR',0,20,254,9,25,8,13
	.byte	'L',0
	.word	62694
	.byte	4,2,35,0,13
	.byte	'U',0
	.word	62757
	.byte	4,2,35,4,0,14
	.word	65874
	.byte	26
	.byte	'Ifx_CPU_DPR',0,20,130,10,3
	.word	65916
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN',0,20,133,10,25,16,13
	.byte	'LA',0
	.word	65147
	.byte	4,2,35,0,13
	.byte	'UA',0
	.word	65217
	.byte	4,2,35,4,13
	.byte	'ACCENA',0
	.word	64999
	.byte	4,2,35,8,13
	.byte	'ACCENB',0
	.word	65073
	.byte	4,2,35,12,0,14
	.word	65942
	.byte	26
	.byte	'Ifx_CPU_SPROT_RGN',0,20,139,10,3
	.word	66024
	.byte	18,12
	.word	65545
	.byte	19,2,0,10
	.byte	'_Ifx_CPU_TPS',0,20,142,10,25,16,13
	.byte	'CON',0
	.word	65480
	.byte	4,2,35,0,13
	.byte	'TIMER',0
	.word	66056
	.byte	12,2,35,4,0,14
	.word	66065
	.byte	26
	.byte	'Ifx_CPU_TPS',0,20,146,10,3
	.word	66113
	.byte	10
	.byte	'_Ifx_CPU_TR',0,20,149,10,25,8,13
	.byte	'EVT',0
	.word	65676
	.byte	4,2,35,0,13
	.byte	'ADR',0
	.word	65612
	.byte	4,2,35,4,0,14
	.word	66139
	.byte	26
	.byte	'Ifx_CPU_TR',0,20,153,10,3
	.word	66184
	.byte	18,176,32
	.word	521
	.byte	19,175,32,0,18,208,223,1
	.word	521
	.byte	19,207,223,1,0,18,248,1
	.word	521
	.byte	19,247,1,0,18,244,29
	.word	521
	.byte	19,243,29,0,18,188,3
	.word	521
	.byte	19,187,3,0,18,232,3
	.word	521
	.byte	19,231,3,0,18,252,23
	.word	521
	.byte	19,251,23,0,18,228,63
	.word	521
	.byte	19,227,63,0,18,128,1
	.word	65874
	.byte	19,15,0,14
	.word	66299
	.byte	18,128,31
	.word	521
	.byte	19,255,30,0,18,64
	.word	65806
	.byte	19,7,0,14
	.word	66325
	.byte	18,192,31
	.word	521
	.byte	19,191,31,0,18,16
	.word	61820
	.byte	19,3,0,18,16
	.word	62820
	.byte	19,3,0,18,16
	.word	62882
	.byte	19,3,0,18,208,7
	.word	521
	.byte	19,207,7,0,14
	.word	66065
	.byte	18,240,23
	.word	521
	.byte	19,239,23,0,18,64
	.word	66139
	.byte	19,7,0,14
	.word	66404
	.byte	18,192,23
	.word	521
	.byte	19,191,23,0,18,232,1
	.word	521
	.byte	19,231,1,0,18,180,1
	.word	521
	.byte	19,179,1,0,18,172,1
	.word	521
	.byte	19,171,1,0,18,64
	.word	62009
	.byte	19,15,0,18,64
	.word	521
	.byte	19,63,0,18,64
	.word	61195
	.byte	19,15,0,10
	.byte	'_Ifx_CPU',0,20,166,10,25,128,128,4,13
	.byte	'reserved_0',0
	.word	66209
	.byte	176,32,2,35,0,13
	.byte	'SEGEN',0
	.word	64732
	.byte	4,3,35,176,32,13
	.byte	'reserved_1034',0
	.word	66220
	.byte	208,223,1,3,35,180,32,13
	.byte	'TASK_ASI',0
	.word	65414
	.byte	4,4,35,132,128,2,13
	.byte	'reserved_8008',0
	.word	66233
	.byte	248,1,4,35,136,128,2,13
	.byte	'PMA0',0
	.word	64423
	.byte	4,4,35,128,130,2,13
	.byte	'PMA1',0
	.word	64485
	.byte	4,4,35,132,130,2,13
	.byte	'PMA2',0
	.word	64547
	.byte	4,4,35,136,130,2,13
	.byte	'reserved_810C',0
	.word	66244
	.byte	244,29,4,35,140,130,2,13
	.byte	'DCON2',0
	.word	62320
	.byte	4,4,35,128,160,2,13
	.byte	'reserved_9004',0
	.word	5078
	.byte	8,4,35,132,160,2,13
	.byte	'SMACON',0
	.word	64795
	.byte	4,4,35,140,160,2,13
	.byte	'DSTR',0
	.word	62944
	.byte	4,4,35,144,160,2,13
	.byte	'reserved_9014',0
	.word	3259
	.byte	4,4,35,148,160,2,13
	.byte	'DATR',0
	.word	62068
	.byte	4,4,35,152,160,2,13
	.byte	'DEADD',0
	.word	62444
	.byte	4,4,35,156,160,2,13
	.byte	'DIEAR',0
	.word	62507
	.byte	4,4,35,160,160,2,13
	.byte	'DIETR',0
	.word	62570
	.byte	4,4,35,164,160,2,13
	.byte	'reserved_9028',0
	.word	4449
	.byte	24,4,35,168,160,2,13
	.byte	'DCON0',0
	.word	62257
	.byte	4,4,35,192,160,2,13
	.byte	'reserved_9044',0
	.word	66255
	.byte	188,3,4,35,196,160,2,13
	.byte	'PSTR',0
	.word	64609
	.byte	4,4,35,128,164,2,13
	.byte	'PCON1',0
	.word	64109
	.byte	4,4,35,132,164,2,13
	.byte	'PCON2',0
	.word	64172
	.byte	4,4,35,136,164,2,13
	.byte	'PCON0',0
	.word	64046
	.byte	4,4,35,140,164,2,13
	.byte	'PIEAR',0
	.word	64297
	.byte	4,4,35,144,164,2,13
	.byte	'PIETR',0
	.word	64360
	.byte	4,4,35,148,164,2,13
	.byte	'reserved_9218',0
	.word	66266
	.byte	232,3,4,35,152,164,2,13
	.byte	'COMPAT',0
	.word	61501
	.byte	4,4,35,128,168,2,13
	.byte	'reserved_9404',0
	.word	66277
	.byte	252,23,4,35,132,168,2,13
	.byte	'FPU_TRAP_CON',0
	.word	63130
	.byte	4,4,35,128,192,2,13
	.byte	'FPU_TRAP_PC',0
	.word	63270
	.byte	4,4,35,132,192,2,13
	.byte	'FPU_TRAP_OPC',0
	.word	63200
	.byte	4,4,35,136,192,2,13
	.byte	'reserved_A00C',0
	.word	3259
	.byte	4,4,35,140,192,2,13
	.byte	'FPU_TRAP_SRC1',0
	.word	63339
	.byte	4,4,35,144,192,2,13
	.byte	'FPU_TRAP_SRC2',0
	.word	63410
	.byte	4,4,35,148,192,2,13
	.byte	'FPU_TRAP_SRC3',0
	.word	63481
	.byte	4,4,35,152,192,2,13
	.byte	'reserved_A01C',0
	.word	66288
	.byte	228,63,4,35,156,192,2,13
	.byte	'DPR',0
	.word	66309
	.byte	128,1,4,35,128,128,3,13
	.byte	'reserved_C080',0
	.word	66314
	.byte	128,31,4,35,128,129,3,13
	.byte	'CPR',0
	.word	66334
	.byte	64,4,35,128,160,3,13
	.byte	'reserved_D040',0
	.word	66339
	.byte	192,31,4,35,192,160,3,13
	.byte	'CPXE',0
	.word	66350
	.byte	16,4,35,128,192,3,13
	.byte	'DPRE',0
	.word	66359
	.byte	16,4,35,144,192,3,13
	.byte	'DPWE',0
	.word	66368
	.byte	16,4,35,160,192,3,13
	.byte	'reserved_E030',0
	.word	66377
	.byte	208,7,4,35,176,192,3,13
	.byte	'TPS',0
	.word	66388
	.byte	16,4,35,128,200,3,13
	.byte	'reserved_E410',0
	.word	66393
	.byte	240,23,4,35,144,200,3,13
	.byte	'TR',0
	.word	66413
	.byte	64,4,35,128,224,3,13
	.byte	'reserved_F040',0
	.word	66418
	.byte	192,23,4,35,192,224,3,13
	.byte	'CCTRL',0
	.word	61438
	.byte	4,4,35,128,248,3,13
	.byte	'CCNT',0
	.word	61376
	.byte	4,4,35,132,248,3,13
	.byte	'ICNT',0
	.word	63552
	.byte	4,4,35,136,248,3,13
	.byte	'M1CNT',0
	.word	63797
	.byte	4,4,35,140,248,3,13
	.byte	'M2CNT',0
	.word	63860
	.byte	4,4,35,144,248,3,13
	.byte	'M3CNT',0
	.word	63923
	.byte	4,4,35,148,248,3,13
	.byte	'reserved_FC18',0
	.word	66429
	.byte	232,1,4,35,152,248,3,13
	.byte	'DBGSR',0
	.word	62130
	.byte	4,4,35,128,250,3,13
	.byte	'reserved_FD04',0
	.word	3259
	.byte	4,4,35,132,250,3,13
	.byte	'EXEVT',0
	.word	63006
	.byte	4,4,35,136,250,3,13
	.byte	'CREVT',0
	.word	61882
	.byte	4,4,35,140,250,3,13
	.byte	'SWEVT',0
	.word	65287
	.byte	4,4,35,144,250,3,13
	.byte	'reserved_FD14',0
	.word	50168
	.byte	28,4,35,148,250,3,13
	.byte	'TRIG_ACC',0
	.word	65740
	.byte	4,4,35,176,250,3,13
	.byte	'reserved_FD34',0
	.word	5418
	.byte	12,4,35,180,250,3,13
	.byte	'DMS',0
	.word	62633
	.byte	4,4,35,192,250,3,13
	.byte	'DCX',0
	.word	62383
	.byte	4,4,35,196,250,3,13
	.byte	'DBGTCR',0
	.word	62193
	.byte	4,4,35,200,250,3,13
	.byte	'reserved_FD4C',0
	.word	66440
	.byte	180,1,4,35,204,250,3,13
	.byte	'PCXI',0
	.word	64235
	.byte	4,4,35,128,252,3,13
	.byte	'PSW',0
	.word	64671
	.byte	4,4,35,132,252,3,13
	.byte	'PC',0
	.word	63986
	.byte	4,4,35,136,252,3,13
	.byte	'reserved_FE0C',0
	.word	5078
	.byte	8,4,35,140,252,3,13
	.byte	'SYSCON',0
	.word	65350
	.byte	4,4,35,148,252,3,13
	.byte	'CPU_ID',0
	.word	61756
	.byte	4,4,35,152,252,3,13
	.byte	'CORE_ID',0
	.word	61565
	.byte	4,4,35,156,252,3,13
	.byte	'BIV',0
	.word	61254
	.byte	4,4,35,160,252,3,13
	.byte	'BTV',0
	.word	61315
	.byte	4,4,35,164,252,3,13
	.byte	'ISP',0
	.word	63675
	.byte	4,4,35,168,252,3,13
	.byte	'ICR',0
	.word	63614
	.byte	4,4,35,172,252,3,13
	.byte	'reserved_FE30',0
	.word	5078
	.byte	8,4,35,176,252,3,13
	.byte	'FCX',0
	.word	63069
	.byte	4,4,35,184,252,3,13
	.byte	'LCX',0
	.word	63736
	.byte	4,4,35,188,252,3,13
	.byte	'reserved_FE40',0
	.word	20710
	.byte	16,4,35,192,252,3,13
	.byte	'CUS_ID',0
	.word	61945
	.byte	4,4,35,208,252,3,13
	.byte	'reserved_FE54',0
	.word	66451
	.byte	172,1,4,35,212,252,3,13
	.byte	'D',0
	.word	66462
	.byte	64,4,35,128,254,3,13
	.byte	'reserved_FF40',0
	.word	66471
	.byte	64,4,35,192,254,3,13
	.byte	'A',0
	.word	66480
	.byte	64,4,35,128,255,3,13
	.byte	'reserved_FFC0',0
	.word	66471
	.byte	64,4,35,192,255,3,0,14
	.word	66489
	.byte	26
	.byte	'Ifx_CPU',0,20,130,11,3
	.word	68280
	.byte	15,8,127,9,1,16
	.byte	'IfxCpu_Id_0',0,0,16
	.byte	'IfxCpu_Id_1',0,1,16
	.byte	'IfxCpu_Id_none',0,2,0,26
	.byte	'IfxCpu_Id',0,8,132,1,3
	.word	68302
	.byte	26
	.byte	'IfxCpu_ResourceCpu',0,8,161,1,3
	.word	1867
	.byte	10
	.byte	'_Ifx_STM_ACCEN0_Bits',0,21,45,16,4,11
	.byte	'EN0',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	521
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	521
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	521
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	521
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	521
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	521
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	521
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	521
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	521
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	521
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	521
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	521
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	521
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	521
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	521
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	521
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	521
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	521
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	521
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	521
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	521
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	521
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	521
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	521
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	521
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	521
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	521
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_STM_ACCEN0_Bits',0,21,79,3
	.word	68400
	.byte	10
	.byte	'_Ifx_STM_ACCEN1_Bits',0,21,82,16,4,11
	.byte	'reserved_0',0,4
	.word	498
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_STM_ACCEN1_Bits',0,21,85,3
	.word	68957
	.byte	10
	.byte	'_Ifx_STM_CAP_Bits',0,21,88,16,4,11
	.byte	'STMCAP63_32',0,4
	.word	498
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_STM_CAP_Bits',0,21,91,3
	.word	69034
	.byte	10
	.byte	'_Ifx_STM_CAPSV_Bits',0,21,94,16,4,11
	.byte	'STMCAP63_32',0,4
	.word	498
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_STM_CAPSV_Bits',0,21,97,3
	.word	69106
	.byte	10
	.byte	'_Ifx_STM_CLC_Bits',0,21,100,16,4,11
	.byte	'DISR',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'DISS',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'EDIS',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	498
	.byte	28,0,2,35,0,0,26
	.byte	'Ifx_STM_CLC_Bits',0,21,107,3
	.word	69182
	.byte	10
	.byte	'_Ifx_STM_CMCON_Bits',0,21,110,16,4,11
	.byte	'MSIZE0',0,1
	.word	521
	.byte	5,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	521
	.byte	3,0,2,35,0,11
	.byte	'MSTART0',0,1
	.word	521
	.byte	5,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	521
	.byte	3,0,2,35,1,11
	.byte	'MSIZE1',0,1
	.word	521
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	521
	.byte	3,0,2,35,2,11
	.byte	'MSTART1',0,1
	.word	521
	.byte	5,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	521
	.byte	3,0,2,35,3,0,26
	.byte	'Ifx_STM_CMCON_Bits',0,21,120,3
	.word	69323
	.byte	10
	.byte	'_Ifx_STM_CMP_Bits',0,21,123,16,4,11
	.byte	'CMPVAL',0,4
	.word	498
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_STM_CMP_Bits',0,21,126,3
	.word	69541
	.byte	10
	.byte	'_Ifx_STM_ICR_Bits',0,21,129,1,16,4,11
	.byte	'CMP0EN',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'CMP0IR',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'CMP0OS',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'CMP1EN',0,1
	.word	521
	.byte	1,3,2,35,0,11
	.byte	'CMP1IR',0,1
	.word	521
	.byte	1,2,2,35,0,11
	.byte	'CMP1OS',0,1
	.word	521
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,4
	.word	498
	.byte	25,0,2,35,0,0,26
	.byte	'Ifx_STM_ICR_Bits',0,21,139,1,3
	.word	69608
	.byte	10
	.byte	'_Ifx_STM_ID_Bits',0,21,142,1,16,4,11
	.byte	'MODREV',0,1
	.word	521
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	521
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	1072
	.byte	16,0,2,35,2,0,26
	.byte	'Ifx_STM_ID_Bits',0,21,147,1,3
	.word	69811
	.byte	10
	.byte	'_Ifx_STM_ISCR_Bits',0,21,150,1,16,4,11
	.byte	'CMP0IRR',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'CMP0IRS',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'CMP1IRR',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'CMP1IRS',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	498
	.byte	28,0,2,35,0,0,26
	.byte	'Ifx_STM_ISCR_Bits',0,21,157,1,3
	.word	69918
	.byte	10
	.byte	'_Ifx_STM_KRST0_Bits',0,21,160,1,16,4,11
	.byte	'RST',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'RSTSTAT',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	498
	.byte	30,0,2,35,0,0,26
	.byte	'Ifx_STM_KRST0_Bits',0,21,165,1,3
	.word	70069
	.byte	10
	.byte	'_Ifx_STM_KRST1_Bits',0,21,168,1,16,4,11
	.byte	'RST',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	498
	.byte	31,0,2,35,0,0,26
	.byte	'Ifx_STM_KRST1_Bits',0,21,172,1,3
	.word	70180
	.byte	10
	.byte	'_Ifx_STM_KRSTCLR_Bits',0,21,175,1,16,4,11
	.byte	'CLR',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	498
	.byte	31,0,2,35,0,0,26
	.byte	'Ifx_STM_KRSTCLR_Bits',0,21,179,1,3
	.word	70272
	.byte	10
	.byte	'_Ifx_STM_OCS_Bits',0,21,182,1,16,4,11
	.byte	'reserved_0',0,4
	.word	498
	.byte	24,8,2,35,0,11
	.byte	'SUS',0,1
	.word	521
	.byte	4,4,2,35,3,11
	.byte	'SUS_P',0,1
	.word	521
	.byte	1,3,2,35,3,11
	.byte	'SUSSTA',0,1
	.word	521
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	521
	.byte	2,0,2,35,3,0,26
	.byte	'Ifx_STM_OCS_Bits',0,21,189,1,3
	.word	70368
	.byte	10
	.byte	'_Ifx_STM_TIM0_Bits',0,21,192,1,16,4,11
	.byte	'STM31_0',0,4
	.word	498
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_STM_TIM0_Bits',0,21,195,1,3
	.word	70514
	.byte	10
	.byte	'_Ifx_STM_TIM0SV_Bits',0,21,198,1,16,4,11
	.byte	'STM31_0',0,4
	.word	498
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_STM_TIM0SV_Bits',0,21,201,1,3
	.word	70586
	.byte	10
	.byte	'_Ifx_STM_TIM1_Bits',0,21,204,1,16,4,11
	.byte	'STM35_4',0,4
	.word	498
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_STM_TIM1_Bits',0,21,207,1,3
	.word	70662
	.byte	10
	.byte	'_Ifx_STM_TIM2_Bits',0,21,210,1,16,4,11
	.byte	'STM39_8',0,4
	.word	498
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_STM_TIM2_Bits',0,21,213,1,3
	.word	70734
	.byte	10
	.byte	'_Ifx_STM_TIM3_Bits',0,21,216,1,16,4,11
	.byte	'STM43_12',0,4
	.word	498
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_STM_TIM3_Bits',0,21,219,1,3
	.word	70806
	.byte	10
	.byte	'_Ifx_STM_TIM4_Bits',0,21,222,1,16,4,11
	.byte	'STM47_16',0,4
	.word	498
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_STM_TIM4_Bits',0,21,225,1,3
	.word	70879
	.byte	10
	.byte	'_Ifx_STM_TIM5_Bits',0,21,228,1,16,4,11
	.byte	'STM51_20',0,4
	.word	498
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_STM_TIM5_Bits',0,21,231,1,3
	.word	70952
	.byte	10
	.byte	'_Ifx_STM_TIM6_Bits',0,21,234,1,16,4,11
	.byte	'STM63_32',0,4
	.word	498
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_STM_TIM6_Bits',0,21,237,1,3
	.word	71025
	.byte	12,21,245,1,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68400
	.byte	4,2,35,0,0,26
	.byte	'Ifx_STM_ACCEN0',0,21,250,1,3
	.word	71098
	.byte	12,21,253,1,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68957
	.byte	4,2,35,0,0,26
	.byte	'Ifx_STM_ACCEN1',0,21,130,2,3
	.word	71162
	.byte	12,21,133,2,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69034
	.byte	4,2,35,0,0,26
	.byte	'Ifx_STM_CAP',0,21,138,2,3
	.word	71226
	.byte	12,21,141,2,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69106
	.byte	4,2,35,0,0,26
	.byte	'Ifx_STM_CAPSV',0,21,146,2,3
	.word	71287
	.byte	12,21,149,2,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69182
	.byte	4,2,35,0,0,26
	.byte	'Ifx_STM_CLC',0,21,154,2,3
	.word	71350
	.byte	12,21,157,2,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69323
	.byte	4,2,35,0,0,26
	.byte	'Ifx_STM_CMCON',0,21,162,2,3
	.word	71411
	.byte	12,21,165,2,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69541
	.byte	4,2,35,0,0,26
	.byte	'Ifx_STM_CMP',0,21,170,2,3
	.word	71474
	.byte	12,21,173,2,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69608
	.byte	4,2,35,0,0,26
	.byte	'Ifx_STM_ICR',0,21,178,2,3
	.word	71535
	.byte	12,21,181,2,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69811
	.byte	4,2,35,0,0,26
	.byte	'Ifx_STM_ID',0,21,186,2,3
	.word	71596
	.byte	12,21,189,2,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69918
	.byte	4,2,35,0,0,26
	.byte	'Ifx_STM_ISCR',0,21,194,2,3
	.word	71656
	.byte	12,21,197,2,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70069
	.byte	4,2,35,0,0,26
	.byte	'Ifx_STM_KRST0',0,21,202,2,3
	.word	71718
	.byte	12,21,205,2,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70180
	.byte	4,2,35,0,0,26
	.byte	'Ifx_STM_KRST1',0,21,210,2,3
	.word	71781
	.byte	12,21,213,2,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70272
	.byte	4,2,35,0,0,26
	.byte	'Ifx_STM_KRSTCLR',0,21,218,2,3
	.word	71844
	.byte	12,21,221,2,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70368
	.byte	4,2,35,0,0,26
	.byte	'Ifx_STM_OCS',0,21,226,2,3
	.word	71909
	.byte	12,21,229,2,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70514
	.byte	4,2,35,0,0,26
	.byte	'Ifx_STM_TIM0',0,21,234,2,3
	.word	71970
	.byte	12,21,237,2,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70586
	.byte	4,2,35,0,0,26
	.byte	'Ifx_STM_TIM0SV',0,21,242,2,3
	.word	72032
	.byte	12,21,245,2,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70662
	.byte	4,2,35,0,0,26
	.byte	'Ifx_STM_TIM1',0,21,250,2,3
	.word	72096
	.byte	12,21,253,2,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70734
	.byte	4,2,35,0,0,26
	.byte	'Ifx_STM_TIM2',0,21,130,3,3
	.word	72158
	.byte	12,21,133,3,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70806
	.byte	4,2,35,0,0,26
	.byte	'Ifx_STM_TIM3',0,21,138,3,3
	.word	72220
	.byte	12,21,141,3,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70879
	.byte	4,2,35,0,0,26
	.byte	'Ifx_STM_TIM4',0,21,146,3,3
	.word	72282
	.byte	12,21,149,3,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70952
	.byte	4,2,35,0,0,26
	.byte	'Ifx_STM_TIM5',0,21,154,3,3
	.word	72344
	.byte	12,21,157,3,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71025
	.byte	4,2,35,0,0,26
	.byte	'Ifx_STM_TIM6',0,21,162,3,3
	.word	72406
	.byte	15,7,144,1,9,1,16
	.byte	'IfxCpu_CounterMode_normal',0,0,16
	.byte	'IfxCpu_CounterMode_task',0,1,0,26
	.byte	'IfxCpu_CounterMode',0,7,148,1,3
	.word	72468
	.byte	24,7,160,1,9,6,13
	.byte	'counter',0
	.word	2022
	.byte	4,2,35,0,13
	.byte	'overlfow',0
	.word	521
	.byte	1,2,35,4,0,26
	.byte	'IfxCpu_Counter',0,7,164,1,3
	.word	72557
	.byte	24,7,172,1,9,32,13
	.byte	'instruction',0
	.word	72557
	.byte	6,2,35,0,13
	.byte	'clock',0
	.word	72557
	.byte	6,2,35,6,13
	.byte	'counter1',0
	.word	72557
	.byte	6,2,35,12,13
	.byte	'counter2',0
	.word	72557
	.byte	6,2,35,18,13
	.byte	'counter3',0
	.word	72557
	.byte	6,2,35,24,0,26
	.byte	'IfxCpu_Perf',0,7,179,1,3
	.word	72623
	.byte	10
	.byte	'_Ifx_FLASH_ACCEN0_Bits',0,22,45,16,4,11
	.byte	'EN0',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	521
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	521
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	521
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	521
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	521
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	521
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	521
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	521
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	521
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	521
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	521
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	521
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	521
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	521
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	521
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	521
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	521
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	521
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	521
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	521
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	521
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	521
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	521
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	521
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	521
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	521
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	521
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_FLASH_ACCEN0_Bits',0,22,79,3
	.word	72741
	.byte	10
	.byte	'_Ifx_FLASH_ACCEN1_Bits',0,22,82,16,4,11
	.byte	'reserved_0',0,4
	.word	498
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_FLASH_ACCEN1_Bits',0,22,85,3
	.word	73302
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_CFG_Bits',0,22,88,16,4,11
	.byte	'SEL',0,1
	.word	521
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	521
	.byte	2,0,2,35,0,11
	.byte	'CLR',0,1
	.word	521
	.byte	1,7,2,35,1,11
	.byte	'DIS',0,1
	.word	521
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	498
	.byte	22,0,2,35,0,0,26
	.byte	'Ifx_FLASH_CBAB_CFG_Bits',0,22,95,3
	.word	73383
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_STAT_Bits',0,22,98,16,4,11
	.byte	'VLD0',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'VLD1',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'VLD2',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'VLD3',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'VLD4',0,1
	.word	521
	.byte	1,3,2,35,0,11
	.byte	'VLD5',0,1
	.word	521
	.byte	1,2,2,35,0,11
	.byte	'VLD6',0,1
	.word	521
	.byte	1,1,2,35,0,11
	.byte	'VLD7',0,1
	.word	521
	.byte	1,0,2,35,0,11
	.byte	'VLD8',0,1
	.word	521
	.byte	1,7,2,35,1,11
	.byte	'VLD9',0,1
	.word	521
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	498
	.byte	22,0,2,35,0,0,26
	.byte	'Ifx_FLASH_CBAB_STAT_Bits',0,22,111,3
	.word	73536
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_TOP_Bits',0,22,114,16,4,11
	.byte	'reserved_0',0,1
	.word	521
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	498
	.byte	19,8,2,35,0,11
	.byte	'ERR',0,1
	.word	521
	.byte	6,2,2,35,3,11
	.byte	'VLD',0,1
	.word	521
	.byte	1,1,2,35,3,11
	.byte	'CLR',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_FLASH_CBAB_TOP_Bits',0,22,121,3
	.word	73784
	.byte	10
	.byte	'_Ifx_FLASH_COMM0_Bits',0,22,124,16,4,11
	.byte	'STATUS',0,1
	.word	521
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	498
	.byte	24,0,2,35,0,0,26
	.byte	'Ifx_FLASH_COMM0_Bits',0,22,128,1,3
	.word	73930
	.byte	10
	.byte	'_Ifx_FLASH_COMM1_Bits',0,22,131,1,16,4,11
	.byte	'STATUS',0,1
	.word	521
	.byte	8,0,2,35,0,11
	.byte	'DATA',0,1
	.word	521
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1072
	.byte	16,0,2,35,2,0,26
	.byte	'Ifx_FLASH_COMM1_Bits',0,22,136,1,3
	.word	74028
	.byte	10
	.byte	'_Ifx_FLASH_COMM2_Bits',0,22,139,1,16,4,11
	.byte	'STATUS',0,1
	.word	521
	.byte	8,0,2,35,0,11
	.byte	'DATA',0,1
	.word	521
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1072
	.byte	16,0,2,35,2,0,26
	.byte	'Ifx_FLASH_COMM2_Bits',0,22,144,1,3
	.word	74144
	.byte	10
	.byte	'_Ifx_FLASH_ECCRD_Bits',0,22,147,1,16,4,11
	.byte	'RCODE',0,4
	.word	498
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	1072
	.byte	8,2,2,35,2,11
	.byte	'EDCERRINJ',0,1
	.word	521
	.byte	1,1,2,35,3,11
	.byte	'ECCORDIS',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_FLASH_ECCRD_Bits',0,22,153,1,3
	.word	74260
	.byte	10
	.byte	'_Ifx_FLASH_ECCRP_Bits',0,22,156,1,16,4,11
	.byte	'RCODE',0,4
	.word	498
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	1072
	.byte	8,2,2,35,2,11
	.byte	'EDCERRINJ',0,1
	.word	521
	.byte	1,1,2,35,3,11
	.byte	'ECCORDIS',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_FLASH_ECCRP_Bits',0,22,162,1,3
	.word	74400
	.byte	10
	.byte	'_Ifx_FLASH_ECCW_Bits',0,22,165,1,16,4,11
	.byte	'WCODE',0,4
	.word	498
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	1072
	.byte	8,2,2,35,2,11
	.byte	'DECENCDIS',0,1
	.word	521
	.byte	1,1,2,35,3,11
	.byte	'PECENCDIS',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_FLASH_ECCW_Bits',0,22,171,1,3
	.word	74540
	.byte	10
	.byte	'_Ifx_FLASH_FCON_Bits',0,22,174,1,16,4,11
	.byte	'WSPFLASH',0,1
	.word	521
	.byte	4,4,2,35,0,11
	.byte	'WSECPF',0,1
	.word	521
	.byte	2,2,2,35,0,11
	.byte	'WSDFLASH',0,2
	.word	1072
	.byte	6,4,2,35,0,11
	.byte	'WSECDF',0,1
	.word	521
	.byte	3,1,2,35,1,11
	.byte	'IDLE',0,1
	.word	521
	.byte	1,0,2,35,1,11
	.byte	'ESLDIS',0,1
	.word	521
	.byte	1,7,2,35,2,11
	.byte	'SLEEP',0,1
	.word	521
	.byte	1,6,2,35,2,11
	.byte	'NSAFECC',0,1
	.word	521
	.byte	1,5,2,35,2,11
	.byte	'STALL',0,1
	.word	521
	.byte	1,4,2,35,2,11
	.byte	'RES21',0,1
	.word	521
	.byte	2,2,2,35,2,11
	.byte	'RES23',0,1
	.word	521
	.byte	2,0,2,35,2,11
	.byte	'VOPERM',0,1
	.word	521
	.byte	1,7,2,35,3,11
	.byte	'SQERM',0,1
	.word	521
	.byte	1,6,2,35,3,11
	.byte	'PROERM',0,1
	.word	521
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	521
	.byte	3,2,2,35,3,11
	.byte	'PR5V',0,1
	.word	521
	.byte	1,1,2,35,3,11
	.byte	'EOBM',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_FLASH_FCON_Bits',0,22,193,1,3
	.word	74679
	.byte	10
	.byte	'_Ifx_FLASH_FPRO_Bits',0,22,196,1,16,4,11
	.byte	'PROINP',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'PRODISP',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'PROIND',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'PRODISD',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'PROINHSMCOTP',0,1
	.word	521
	.byte	1,3,2,35,0,11
	.byte	'RES5',0,1
	.word	521
	.byte	1,2,2,35,0,11
	.byte	'PROINOTP',0,1
	.word	521
	.byte	1,1,2,35,0,11
	.byte	'RES7',0,1
	.word	521
	.byte	1,0,2,35,0,11
	.byte	'PROINDBG',0,1
	.word	521
	.byte	1,7,2,35,1,11
	.byte	'PRODISDBG',0,1
	.word	521
	.byte	1,6,2,35,1,11
	.byte	'PROINHSM',0,1
	.word	521
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	521
	.byte	5,0,2,35,1,11
	.byte	'DCFP',0,1
	.word	521
	.byte	1,7,2,35,2,11
	.byte	'DDFP',0,1
	.word	521
	.byte	1,6,2,35,2,11
	.byte	'DDFPX',0,1
	.word	521
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	521
	.byte	1,4,2,35,2,11
	.byte	'DDFD',0,1
	.word	521
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	521
	.byte	1,2,2,35,2,11
	.byte	'ENPE',0,1
	.word	521
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	521
	.byte	8,0,2,35,3,0,26
	.byte	'Ifx_FLASH_FPRO_Bits',0,22,218,1,3
	.word	75041
	.byte	10
	.byte	'_Ifx_FLASH_FSR_Bits',0,22,221,1,16,4,11
	.byte	'FABUSY',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'D0BUSY',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'RES1',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'P0BUSY',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'P1BUSY',0,1
	.word	521
	.byte	1,3,2,35,0,11
	.byte	'RES5',0,1
	.word	521
	.byte	1,2,2,35,0,11
	.byte	'RES6',0,1
	.word	521
	.byte	1,1,2,35,0,11
	.byte	'PROG',0,1
	.word	521
	.byte	1,0,2,35,0,11
	.byte	'ERASE',0,1
	.word	521
	.byte	1,7,2,35,1,11
	.byte	'PFPAGE',0,1
	.word	521
	.byte	1,6,2,35,1,11
	.byte	'DFPAGE',0,1
	.word	521
	.byte	1,5,2,35,1,11
	.byte	'OPER',0,1
	.word	521
	.byte	1,4,2,35,1,11
	.byte	'SQER',0,1
	.word	521
	.byte	1,3,2,35,1,11
	.byte	'PROER',0,1
	.word	521
	.byte	1,2,2,35,1,11
	.byte	'PFSBER',0,1
	.word	521
	.byte	1,1,2,35,1,11
	.byte	'PFDBER',0,1
	.word	521
	.byte	1,0,2,35,1,11
	.byte	'PFMBER',0,1
	.word	521
	.byte	1,7,2,35,2,11
	.byte	'RES17',0,1
	.word	521
	.byte	1,6,2,35,2,11
	.byte	'DFSBER',0,1
	.word	521
	.byte	1,5,2,35,2,11
	.byte	'DFDBER',0,1
	.word	521
	.byte	1,4,2,35,2,11
	.byte	'DFTBER',0,1
	.word	521
	.byte	1,3,2,35,2,11
	.byte	'DFMBER',0,1
	.word	521
	.byte	1,2,2,35,2,11
	.byte	'SRIADDERR',0,1
	.word	521
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	1072
	.byte	2,7,2,35,2,11
	.byte	'PVER',0,1
	.word	521
	.byte	1,6,2,35,3,11
	.byte	'EVER',0,1
	.word	521
	.byte	1,5,2,35,3,11
	.byte	'SPND',0,1
	.word	521
	.byte	1,4,2,35,3,11
	.byte	'SLM',0,1
	.word	521
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	521
	.byte	1,2,2,35,3,11
	.byte	'ORIER',0,1
	.word	521
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_FLASH_FSR_Bits',0,22,254,1,3
	.word	75482
	.byte	10
	.byte	'_Ifx_FLASH_ID_Bits',0,22,129,2,16,4,11
	.byte	'MODREV',0,1
	.word	521
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	521
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	1072
	.byte	16,0,2,35,2,0,26
	.byte	'Ifx_FLASH_ID_Bits',0,22,134,2,3
	.word	76088
	.byte	10
	.byte	'_Ifx_FLASH_MARD_Bits',0,22,137,2,16,4,11
	.byte	'HMARGIN',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'SELD0',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'SPND',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'SPNDERR',0,1
	.word	521
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,2
	.word	1072
	.byte	10,1,2,35,0,11
	.byte	'TRAPDIS',0,1
	.word	521
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1072
	.byte	16,0,2,35,2,0,26
	.byte	'Ifx_FLASH_MARD_Bits',0,22,147,2,3
	.word	76199
	.byte	10
	.byte	'_Ifx_FLASH_MARP_Bits',0,22,150,2,16,4,11
	.byte	'SELP0',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'SELP1',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'RES2',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'RES3',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	1072
	.byte	11,1,2,35,0,11
	.byte	'TRAPDIS',0,1
	.word	521
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1072
	.byte	16,0,2,35,2,0,26
	.byte	'Ifx_FLASH_MARP_Bits',0,22,159,2,3
	.word	76413
	.byte	10
	.byte	'_Ifx_FLASH_PROCOND_Bits',0,22,162,2,16,4,11
	.byte	'L',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'NSAFECC',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'RAMIN',0,1
	.word	521
	.byte	2,4,2,35,0,11
	.byte	'RAMINSEL',0,1
	.word	521
	.byte	4,0,2,35,0,11
	.byte	'OSCCFG',0,1
	.word	521
	.byte	1,7,2,35,1,11
	.byte	'MODE',0,1
	.word	521
	.byte	2,5,2,35,1,11
	.byte	'APREN',0,1
	.word	521
	.byte	1,4,2,35,1,11
	.byte	'CAP0EN',0,1
	.word	521
	.byte	1,3,2,35,1,11
	.byte	'CAP1EN',0,1
	.word	521
	.byte	1,2,2,35,1,11
	.byte	'CAP2EN',0,1
	.word	521
	.byte	1,1,2,35,1,11
	.byte	'CAP3EN',0,1
	.word	521
	.byte	1,0,2,35,1,11
	.byte	'ESR0CNT',0,2
	.word	1072
	.byte	12,4,2,35,2,11
	.byte	'RES29',0,1
	.word	521
	.byte	2,2,2,35,3,11
	.byte	'RES30',0,1
	.word	521
	.byte	1,1,2,35,3,11
	.byte	'RPRO',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_FLASH_PROCOND_Bits',0,22,179,2,3
	.word	76600
	.byte	10
	.byte	'_Ifx_FLASH_PROCONDBG_Bits',0,22,182,2,16,4,11
	.byte	'OCDSDIS',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'DBGIFLCK',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'EDM',0,1
	.word	521
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	498
	.byte	28,0,2,35,0,0,26
	.byte	'Ifx_FLASH_PROCONDBG_Bits',0,22,188,2,3
	.word	76924
	.byte	10
	.byte	'_Ifx_FLASH_PROCONHSM_Bits',0,22,191,2,16,4,11
	.byte	'HSMDBGDIS',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'DBGIFLCK',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'TSTIFLCK',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'HSMTSTDIS',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'RES15',0,2
	.word	1072
	.byte	12,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	1072
	.byte	16,0,2,35,2,0,26
	.byte	'Ifx_FLASH_PROCONHSM_Bits',0,22,199,2,3
	.word	77067
	.byte	10
	.byte	'_Ifx_FLASH_PROCONHSMCOTP_Bits',0,22,202,2,16,4,11
	.byte	'HSMBOOTEN',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'SSWWAIT',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'HSMDX',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'HSM6X',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'HSM16X',0,1
	.word	521
	.byte	1,3,2,35,0,11
	.byte	'HSM17X',0,1
	.word	521
	.byte	1,2,2,35,0,11
	.byte	'S6ROM',0,1
	.word	521
	.byte	1,1,2,35,0,11
	.byte	'HSMENPINS',0,2
	.word	1072
	.byte	2,7,2,35,0,11
	.byte	'HSMENRES',0,1
	.word	521
	.byte	2,5,2,35,1,11
	.byte	'DESTDBG',0,1
	.word	521
	.byte	2,3,2,35,1,11
	.byte	'BLKFLAN',0,1
	.word	521
	.byte	1,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	521
	.byte	2,0,2,35,1,11
	.byte	'S16ROM',0,1
	.word	521
	.byte	1,7,2,35,2,11
	.byte	'S17ROM',0,1
	.word	521
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	1072
	.byte	14,0,2,35,2,0,26
	.byte	'Ifx_FLASH_PROCONHSMCOTP_Bits',0,22,219,2,3
	.word	77256
	.byte	10
	.byte	'_Ifx_FLASH_PROCONOTP_Bits',0,22,222,2,16,4,11
	.byte	'S0ROM',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'S1ROM',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'S2ROM',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'S3ROM',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'S4ROM',0,1
	.word	521
	.byte	1,3,2,35,0,11
	.byte	'S5ROM',0,1
	.word	521
	.byte	1,2,2,35,0,11
	.byte	'S6ROM',0,1
	.word	521
	.byte	1,1,2,35,0,11
	.byte	'S7ROM',0,1
	.word	521
	.byte	1,0,2,35,0,11
	.byte	'S8ROM',0,1
	.word	521
	.byte	1,7,2,35,1,11
	.byte	'S9ROM',0,1
	.word	521
	.byte	1,6,2,35,1,11
	.byte	'S10ROM',0,1
	.word	521
	.byte	1,5,2,35,1,11
	.byte	'S11ROM',0,1
	.word	521
	.byte	1,4,2,35,1,11
	.byte	'S12ROM',0,1
	.word	521
	.byte	1,3,2,35,1,11
	.byte	'S13ROM',0,1
	.word	521
	.byte	1,2,2,35,1,11
	.byte	'S14ROM',0,1
	.word	521
	.byte	1,1,2,35,1,11
	.byte	'S15ROM',0,1
	.word	521
	.byte	1,0,2,35,1,11
	.byte	'S16ROM',0,1
	.word	521
	.byte	1,7,2,35,2,11
	.byte	'S17ROM',0,1
	.word	521
	.byte	1,6,2,35,2,11
	.byte	'S18ROM',0,1
	.word	521
	.byte	1,5,2,35,2,11
	.byte	'S19ROM',0,1
	.word	521
	.byte	1,4,2,35,2,11
	.byte	'S20ROM',0,1
	.word	521
	.byte	1,3,2,35,2,11
	.byte	'S21ROM',0,1
	.word	521
	.byte	1,2,2,35,2,11
	.byte	'S22ROM',0,1
	.word	521
	.byte	1,1,2,35,2,11
	.byte	'S23ROM',0,1
	.word	521
	.byte	1,0,2,35,2,11
	.byte	'S24ROM',0,1
	.word	521
	.byte	1,7,2,35,3,11
	.byte	'S25ROM',0,1
	.word	521
	.byte	1,6,2,35,3,11
	.byte	'S26ROM',0,1
	.word	521
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	521
	.byte	2,3,2,35,3,11
	.byte	'BML',0,1
	.word	521
	.byte	2,1,2,35,3,11
	.byte	'TP',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_FLASH_PROCONOTP_Bits',0,22,254,2,3
	.word	77619
	.byte	10
	.byte	'_Ifx_FLASH_PROCONP_Bits',0,22,129,3,16,4,11
	.byte	'S0L',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'S1L',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'S2L',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'S3L',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'S4L',0,1
	.word	521
	.byte	1,3,2,35,0,11
	.byte	'S5L',0,1
	.word	521
	.byte	1,2,2,35,0,11
	.byte	'S6L',0,1
	.word	521
	.byte	1,1,2,35,0,11
	.byte	'S7L',0,1
	.word	521
	.byte	1,0,2,35,0,11
	.byte	'S8L',0,1
	.word	521
	.byte	1,7,2,35,1,11
	.byte	'S9L',0,1
	.word	521
	.byte	1,6,2,35,1,11
	.byte	'S10L',0,1
	.word	521
	.byte	1,5,2,35,1,11
	.byte	'S11L',0,1
	.word	521
	.byte	1,4,2,35,1,11
	.byte	'S12L',0,1
	.word	521
	.byte	1,3,2,35,1,11
	.byte	'S13L',0,1
	.word	521
	.byte	1,2,2,35,1,11
	.byte	'S14L',0,1
	.word	521
	.byte	1,1,2,35,1,11
	.byte	'S15L',0,1
	.word	521
	.byte	1,0,2,35,1,11
	.byte	'S16L',0,1
	.word	521
	.byte	1,7,2,35,2,11
	.byte	'S17L',0,1
	.word	521
	.byte	1,6,2,35,2,11
	.byte	'S18L',0,1
	.word	521
	.byte	1,5,2,35,2,11
	.byte	'S19L',0,1
	.word	521
	.byte	1,4,2,35,2,11
	.byte	'S20L',0,1
	.word	521
	.byte	1,3,2,35,2,11
	.byte	'S21L',0,1
	.word	521
	.byte	1,2,2,35,2,11
	.byte	'S22L',0,1
	.word	521
	.byte	1,1,2,35,2,11
	.byte	'S23L',0,1
	.word	521
	.byte	1,0,2,35,2,11
	.byte	'S24L',0,1
	.word	521
	.byte	1,7,2,35,3,11
	.byte	'S25L',0,1
	.word	521
	.byte	1,6,2,35,3,11
	.byte	'S26L',0,1
	.word	521
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	521
	.byte	4,1,2,35,3,11
	.byte	'RPRO',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_FLASH_PROCONP_Bits',0,22,160,3,3
	.word	78214
	.byte	10
	.byte	'_Ifx_FLASH_PROCONWOP_Bits',0,22,163,3,16,4,11
	.byte	'S0WOP',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'S1WOP',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'S2WOP',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'S3WOP',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'S4WOP',0,1
	.word	521
	.byte	1,3,2,35,0,11
	.byte	'S5WOP',0,1
	.word	521
	.byte	1,2,2,35,0,11
	.byte	'S6WOP',0,1
	.word	521
	.byte	1,1,2,35,0,11
	.byte	'S7WOP',0,1
	.word	521
	.byte	1,0,2,35,0,11
	.byte	'S8WOP',0,1
	.word	521
	.byte	1,7,2,35,1,11
	.byte	'S9WOP',0,1
	.word	521
	.byte	1,6,2,35,1,11
	.byte	'S10WOP',0,1
	.word	521
	.byte	1,5,2,35,1,11
	.byte	'S11WOP',0,1
	.word	521
	.byte	1,4,2,35,1,11
	.byte	'S12WOP',0,1
	.word	521
	.byte	1,3,2,35,1,11
	.byte	'S13WOP',0,1
	.word	521
	.byte	1,2,2,35,1,11
	.byte	'S14WOP',0,1
	.word	521
	.byte	1,1,2,35,1,11
	.byte	'S15WOP',0,1
	.word	521
	.byte	1,0,2,35,1,11
	.byte	'S16WOP',0,1
	.word	521
	.byte	1,7,2,35,2,11
	.byte	'S17WOP',0,1
	.word	521
	.byte	1,6,2,35,2,11
	.byte	'S18WOP',0,1
	.word	521
	.byte	1,5,2,35,2,11
	.byte	'S19WOP',0,1
	.word	521
	.byte	1,4,2,35,2,11
	.byte	'S20WOP',0,1
	.word	521
	.byte	1,3,2,35,2,11
	.byte	'S21WOP',0,1
	.word	521
	.byte	1,2,2,35,2,11
	.byte	'S22WOP',0,1
	.word	521
	.byte	1,1,2,35,2,11
	.byte	'S23WOP',0,1
	.word	521
	.byte	1,0,2,35,2,11
	.byte	'S24WOP',0,1
	.word	521
	.byte	1,7,2,35,3,11
	.byte	'S25WOP',0,1
	.word	521
	.byte	1,6,2,35,3,11
	.byte	'S26WOP',0,1
	.word	521
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	521
	.byte	4,1,2,35,3,11
	.byte	'DATM',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_FLASH_PROCONWOP_Bits',0,22,194,3,3
	.word	78738
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG0_Bits',0,22,197,3,16,4,11
	.byte	'TAG',0,1
	.word	521
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	498
	.byte	26,0,2,35,0,0,26
	.byte	'Ifx_FLASH_RDB_CFG0_Bits',0,22,201,3,3
	.word	79320
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG1_Bits',0,22,204,3,16,4,11
	.byte	'TAG',0,1
	.word	521
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	498
	.byte	26,0,2,35,0,0,26
	.byte	'Ifx_FLASH_RDB_CFG1_Bits',0,22,208,3,3
	.word	79422
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG2_Bits',0,22,211,3,16,4,11
	.byte	'TAG',0,1
	.word	521
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	498
	.byte	26,0,2,35,0,0,26
	.byte	'Ifx_FLASH_RDB_CFG2_Bits',0,22,215,3,3
	.word	79524
	.byte	10
	.byte	'_Ifx_FLASH_RRAD_Bits',0,22,218,3,16,4,11
	.byte	'reserved_0',0,1
	.word	521
	.byte	3,5,2,35,0,11
	.byte	'ADD',0,4
	.word	498
	.byte	29,0,2,35,0,0,26
	.byte	'Ifx_FLASH_RRAD_Bits',0,22,222,3,3
	.word	79626
	.byte	10
	.byte	'_Ifx_FLASH_RRCT_Bits',0,22,225,3,16,4,11
	.byte	'STRT',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'STP',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'BUSY',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'DONE',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'ERR',0,1
	.word	521
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	521
	.byte	3,0,2,35,0,11
	.byte	'EOBM',0,1
	.word	521
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	521
	.byte	7,0,2,35,1,11
	.byte	'CNT',0,2
	.word	1072
	.byte	16,0,2,35,2,0,26
	.byte	'Ifx_FLASH_RRCT_Bits',0,22,236,3,3
	.word	79720
	.byte	10
	.byte	'_Ifx_FLASH_RRD0_Bits',0,22,239,3,16,4,11
	.byte	'DATA',0,4
	.word	498
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_FLASH_RRD0_Bits',0,22,242,3,3
	.word	79930
	.byte	10
	.byte	'_Ifx_FLASH_RRD1_Bits',0,22,245,3,16,4,11
	.byte	'DATA',0,4
	.word	498
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_FLASH_RRD1_Bits',0,22,248,3,3
	.word	80003
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_CFG_Bits',0,22,251,3,16,4,11
	.byte	'SEL',0,1
	.word	521
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	521
	.byte	2,0,2,35,0,11
	.byte	'CLR',0,1
	.word	521
	.byte	1,7,2,35,1,11
	.byte	'DIS',0,1
	.word	521
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	498
	.byte	22,0,2,35,0,0,26
	.byte	'Ifx_FLASH_UBAB_CFG_Bits',0,22,130,4,3
	.word	80076
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_STAT_Bits',0,22,133,4,16,4,11
	.byte	'VLD0',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	498
	.byte	31,0,2,35,0,0,26
	.byte	'Ifx_FLASH_UBAB_STAT_Bits',0,22,137,4,3
	.word	80231
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_TOP_Bits',0,22,140,4,16,4,11
	.byte	'reserved_0',0,1
	.word	521
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	498
	.byte	19,8,2,35,0,11
	.byte	'ERR',0,1
	.word	521
	.byte	6,2,2,35,3,11
	.byte	'VLD',0,1
	.word	521
	.byte	1,1,2,35,3,11
	.byte	'CLR',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_FLASH_UBAB_TOP_Bits',0,22,147,4,3
	.word	80336
	.byte	12,22,155,4,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72741
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_ACCEN0',0,22,160,4,3
	.word	80484
	.byte	12,22,163,4,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	73302
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_ACCEN1',0,22,168,4,3
	.word	80550
	.byte	12,22,171,4,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	73383
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_CBAB_CFG',0,22,176,4,3
	.word	80616
	.byte	12,22,179,4,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	73536
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_CBAB_STAT',0,22,184,4,3
	.word	80684
	.byte	12,22,187,4,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	73784
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_CBAB_TOP',0,22,192,4,3
	.word	80753
	.byte	12,22,195,4,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	73930
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_COMM0',0,22,200,4,3
	.word	80821
	.byte	12,22,203,4,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	74028
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_COMM1',0,22,208,4,3
	.word	80886
	.byte	12,22,211,4,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	74144
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_COMM2',0,22,216,4,3
	.word	80951
	.byte	12,22,219,4,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	74260
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_ECCRD',0,22,224,4,3
	.word	81016
	.byte	12,22,227,4,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	74400
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_ECCRP',0,22,232,4,3
	.word	81081
	.byte	12,22,235,4,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	74540
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_ECCW',0,22,240,4,3
	.word	81146
	.byte	12,22,243,4,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	74679
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_FCON',0,22,248,4,3
	.word	81210
	.byte	12,22,251,4,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	75041
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_FPRO',0,22,128,5,3
	.word	81274
	.byte	12,22,131,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	75482
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_FSR',0,22,136,5,3
	.word	81338
	.byte	12,22,139,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	76088
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_ID',0,22,144,5,3
	.word	81401
	.byte	12,22,147,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	76199
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_MARD',0,22,152,5,3
	.word	81463
	.byte	12,22,155,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	76413
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_MARP',0,22,160,5,3
	.word	81527
	.byte	12,22,163,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	76600
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_PROCOND',0,22,168,5,3
	.word	81591
	.byte	12,22,171,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	76924
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_PROCONDBG',0,22,176,5,3
	.word	81658
	.byte	12,22,179,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	77067
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_PROCONHSM',0,22,184,5,3
	.word	81727
	.byte	12,22,187,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	77256
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_PROCONHSMCOTP',0,22,192,5,3
	.word	81796
	.byte	12,22,195,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	77619
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_PROCONOTP',0,22,200,5,3
	.word	81869
	.byte	12,22,203,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	78214
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_PROCONP',0,22,208,5,3
	.word	81938
	.byte	12,22,211,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	78738
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_PROCONWOP',0,22,216,5,3
	.word	82005
	.byte	12,22,219,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	79320
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_RDB_CFG0',0,22,224,5,3
	.word	82074
	.byte	12,22,227,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	79422
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_RDB_CFG1',0,22,232,5,3
	.word	82142
	.byte	12,22,235,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	79524
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_RDB_CFG2',0,22,240,5,3
	.word	82210
	.byte	12,22,243,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	79626
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_RRAD',0,22,248,5,3
	.word	82278
	.byte	12,22,251,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	79720
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_RRCT',0,22,128,6,3
	.word	82342
	.byte	12,22,131,6,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	79930
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_RRD0',0,22,136,6,3
	.word	82406
	.byte	12,22,139,6,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	80003
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_RRD1',0,22,144,6,3
	.word	82470
	.byte	12,22,147,6,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	80076
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_UBAB_CFG',0,22,152,6,3
	.word	82534
	.byte	12,22,155,6,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	80231
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_UBAB_STAT',0,22,160,6,3
	.word	82602
	.byte	12,22,163,6,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	80336
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_UBAB_TOP',0,22,168,6,3
	.word	82671
	.byte	10
	.byte	'_Ifx_FLASH_CBAB',0,22,179,6,25,12,13
	.byte	'CFG',0
	.word	80616
	.byte	4,2,35,0,13
	.byte	'STAT',0
	.word	80684
	.byte	4,2,35,4,13
	.byte	'TOP',0
	.word	80753
	.byte	4,2,35,8,0,14
	.word	82739
	.byte	26
	.byte	'Ifx_FLASH_CBAB',0,22,184,6,3
	.word	82802
	.byte	10
	.byte	'_Ifx_FLASH_RDB',0,22,187,6,25,12,13
	.byte	'CFG0',0
	.word	82074
	.byte	4,2,35,0,13
	.byte	'CFG1',0
	.word	82142
	.byte	4,2,35,4,13
	.byte	'CFG2',0
	.word	82210
	.byte	4,2,35,8,0,14
	.word	82831
	.byte	26
	.byte	'Ifx_FLASH_RDB',0,22,192,6,3
	.word	82895
	.byte	10
	.byte	'_Ifx_FLASH_UBAB',0,22,195,6,25,12,13
	.byte	'CFG',0
	.word	82534
	.byte	4,2,35,0,13
	.byte	'STAT',0
	.word	82602
	.byte	4,2,35,4,13
	.byte	'TOP',0
	.word	82671
	.byte	4,2,35,8,0,14
	.word	82923
	.byte	26
	.byte	'Ifx_FLASH_UBAB',0,22,200,6,3
	.word	82986
	.byte	26
	.byte	'Ifx_P_ACCEN0_Bits',0,10,79,3
	.word	8831
	.byte	26
	.byte	'Ifx_P_ACCEN1_Bits',0,10,85,3
	.word	8744
	.byte	26
	.byte	'Ifx_P_ESR_Bits',0,10,107,3
	.word	5087
	.byte	26
	.byte	'Ifx_P_ID_Bits',0,10,115,3
	.word	3140
	.byte	26
	.byte	'Ifx_P_IN_Bits',0,10,137,1,3
	.word	4135
	.byte	26
	.byte	'Ifx_P_IOCR0_Bits',0,10,150,1,3
	.word	3268
	.byte	26
	.byte	'Ifx_P_IOCR12_Bits',0,10,163,1,3
	.word	3915
	.byte	26
	.byte	'Ifx_P_IOCR4_Bits',0,10,176,1,3
	.word	3483
	.byte	26
	.byte	'Ifx_P_IOCR8_Bits',0,10,189,1,3
	.word	3698
	.byte	26
	.byte	'Ifx_P_LPCR0_Bits',0,10,197,1,3
	.word	8103
	.byte	26
	.byte	'Ifx_P_LPCR1_Bits',0,10,205,1,3
	.word	8227
	.byte	26
	.byte	'Ifx_P_LPCR1_P21_Bits',0,10,215,1,3
	.word	8311
	.byte	26
	.byte	'Ifx_P_LPCR2_Bits',0,10,229,1,3
	.word	8491
	.byte	26
	.byte	'Ifx_P_OMCR0_Bits',0,10,240,1,3
	.word	6742
	.byte	26
	.byte	'Ifx_P_OMCR12_Bits',0,10,250,1,3
	.word	7266
	.byte	26
	.byte	'Ifx_P_OMCR4_Bits',0,10,133,2,3
	.word	6916
	.byte	26
	.byte	'Ifx_P_OMCR8_Bits',0,10,144,2,3
	.word	7090
	.byte	26
	.byte	'Ifx_P_OMCR_Bits',0,10,166,2,3
	.word	7755
	.byte	26
	.byte	'Ifx_P_OMR_Bits',0,10,203,2,3
	.word	2569
	.byte	26
	.byte	'Ifx_P_OMSR0_Bits',0,10,213,2,3
	.word	6079
	.byte	26
	.byte	'Ifx_P_OMSR12_Bits',0,10,224,2,3
	.word	6567
	.byte	26
	.byte	'Ifx_P_OMSR4_Bits',0,10,235,2,3
	.word	6226
	.byte	26
	.byte	'Ifx_P_OMSR8_Bits',0,10,246,2,3
	.word	6395
	.byte	26
	.byte	'Ifx_P_OMSR_Bits',0,10,140,3,3
	.word	7422
	.byte	26
	.byte	'Ifx_P_OUT_Bits',0,10,162,3,3
	.word	2253
	.byte	26
	.byte	'Ifx_P_PCSR_Bits',0,10,180,3,3
	.word	5793
	.byte	26
	.byte	'Ifx_P_PDISC_Bits',0,10,202,3,3
	.word	5427
	.byte	26
	.byte	'Ifx_P_PDR0_Bits',0,10,223,3,3
	.word	4458
	.byte	26
	.byte	'Ifx_P_PDR1_Bits',0,10,244,3,3
	.word	4762
	.byte	26
	.byte	'Ifx_P_ACCEN0',0,10,129,4,3
	.word	9358
	.byte	26
	.byte	'Ifx_P_ACCEN1',0,10,137,4,3
	.word	8791
	.byte	26
	.byte	'Ifx_P_ESR',0,10,145,4,3
	.word	5378
	.byte	26
	.byte	'Ifx_P_ID',0,10,153,4,3
	.word	3219
	.byte	26
	.byte	'Ifx_P_IN',0,10,161,4,3
	.word	4409
	.byte	26
	.byte	'Ifx_P_IOCR0',0,10,169,4,3
	.word	3443
	.byte	26
	.byte	'Ifx_P_IOCR12',0,10,177,4,3
	.word	4095
	.byte	26
	.byte	'Ifx_P_IOCR4',0,10,185,4,3
	.word	3658
	.byte	26
	.byte	'Ifx_P_IOCR8',0,10,193,4,3
	.word	3875
	.byte	26
	.byte	'Ifx_P_LPCR0',0,10,201,4,3
	.word	8187
	.byte	26
	.byte	'Ifx_P_LPCR1',0,10,210,4,3
	.word	8436
	.byte	26
	.byte	'Ifx_P_LPCR2',0,10,218,4,3
	.word	8695
	.byte	26
	.byte	'Ifx_P_OMCR',0,10,226,4,3
	.word	8063
	.byte	26
	.byte	'Ifx_P_OMCR0',0,10,234,4,3
	.word	6876
	.byte	26
	.byte	'Ifx_P_OMCR12',0,10,242,4,3
	.word	7382
	.byte	26
	.byte	'Ifx_P_OMCR4',0,10,250,4,3
	.word	7050
	.byte	26
	.byte	'Ifx_P_OMCR8',0,10,130,5,3
	.word	7226
	.byte	26
	.byte	'Ifx_P_OMR',0,10,138,5,3
	.word	3100
	.byte	26
	.byte	'Ifx_P_OMSR',0,10,146,5,3
	.word	7715
	.byte	26
	.byte	'Ifx_P_OMSR0',0,10,154,5,3
	.word	6186
	.byte	26
	.byte	'Ifx_P_OMSR12',0,10,162,5,3
	.word	6702
	.byte	26
	.byte	'Ifx_P_OMSR4',0,10,170,5,3
	.word	6355
	.byte	26
	.byte	'Ifx_P_OMSR8',0,10,178,5,3
	.word	6527
	.byte	26
	.byte	'Ifx_P_OUT',0,10,186,5,3
	.word	2529
	.byte	26
	.byte	'Ifx_P_PCSR',0,10,194,5,3
	.word	6039
	.byte	26
	.byte	'Ifx_P_PDISC',0,10,202,5,3
	.word	5753
	.byte	26
	.byte	'Ifx_P_PDR0',0,10,210,5,3
	.word	4722
	.byte	26
	.byte	'Ifx_P_PDR1',0,10,218,5,3
	.word	5038
	.byte	14
	.word	9398
	.byte	26
	.byte	'Ifx_P',0,10,139,6,3
	.word	84333
	.byte	26
	.byte	'IfxPort_InputMode',0,9,89,3
	.word	10011
	.byte	26
	.byte	'IfxPort_Mode',0,9,116,3
	.word	16683
	.byte	26
	.byte	'IfxPort_OutputIdx',0,9,130,1,3
	.word	10286
	.byte	26
	.byte	'IfxPort_OutputMode',0,9,138,1,3
	.word	10216
	.byte	26
	.byte	'IfxPort_PadDriver',0,9,158,1,3
	.word	17427
	.byte	26
	.byte	'IfxPort_State',0,9,178,1,3
	.word	10599
	.byte	26
	.byte	'IfxPort_Pin',0,9,194,1,3
	.word	18153
	.byte	26
	.byte	'IfxScuCcu_PllStepsFunctionHook',0,13,148,1,16
	.word	237
	.byte	24,13,212,5,9,8,13
	.byte	'value',0
	.word	2022
	.byte	4,2,35,0,13
	.byte	'mask',0
	.word	2022
	.byte	4,2,35,4,0,26
	.byte	'IfxScuCcu_CcuconRegConfig',0,13,216,5,3
	.word	84566
	.byte	24,13,221,5,9,8,13
	.byte	'pDivider',0
	.word	521
	.byte	1,2,35,0,13
	.byte	'nDivider',0
	.word	521
	.byte	1,2,35,1,13
	.byte	'k2Initial',0
	.word	521
	.byte	1,2,35,2,13
	.byte	'waitTime',0
	.word	294
	.byte	4,2,35,4,0,26
	.byte	'IfxScuCcu_InitialStepConfig',0,13,227,5,3
	.word	84637
	.byte	24,13,231,5,9,12,13
	.byte	'k2Step',0
	.word	521
	.byte	1,2,35,0,13
	.byte	'waitTime',0
	.word	294
	.byte	4,2,35,2,13
	.byte	'hookFunction',0
	.word	84526
	.byte	4,2,35,8,0,26
	.byte	'IfxScuCcu_PllStepsConfig',0,13,236,5,3
	.word	84754
	.byte	3
	.word	234
	.byte	24,13,244,5,9,48,13
	.byte	'ccucon0',0
	.word	84566
	.byte	8,2,35,0,13
	.byte	'ccucon1',0
	.word	84566
	.byte	8,2,35,8,13
	.byte	'ccucon2',0
	.word	84566
	.byte	8,2,35,16,13
	.byte	'ccucon5',0
	.word	84566
	.byte	8,2,35,24,13
	.byte	'ccucon6',0
	.word	84566
	.byte	8,2,35,32,13
	.byte	'ccucon7',0
	.word	84566
	.byte	8,2,35,40,0,26
	.byte	'IfxScuCcu_ClockDistributionConfig',0,13,252,5,3
	.word	84856
	.byte	24,13,128,6,9,8,13
	.byte	'value',0
	.word	2022
	.byte	4,2,35,0,13
	.byte	'mask',0
	.word	2022
	.byte	4,2,35,4,0,26
	.byte	'IfxScuCcu_FlashWaitstateConfig',0,13,132,6,3
	.word	85008
	.byte	3
	.word	84754
	.byte	24,13,137,6,9,16,13
	.byte	'numOfPllDividerSteps',0
	.word	521
	.byte	1,2,35,0,13
	.byte	'pllDividerStep',0
	.word	85084
	.byte	4,2,35,4,13
	.byte	'pllInitialStep',0
	.word	84637
	.byte	8,2,35,8,0,26
	.byte	'IfxScuCcu_SysPllConfig',0,13,142,6,3
	.word	85089
	.byte	26
	.byte	'Ifx_GPT12_ACCEN0_Bits',0,12,79,3
	.word	13984
	.byte	26
	.byte	'Ifx_GPT12_ACCEN1_Bits',0,12,85,3
	.word	13893
	.byte	26
	.byte	'Ifx_GPT12_CAPREL_Bits',0,12,92,3
	.word	12758
	.byte	26
	.byte	'Ifx_GPT12_CLC_Bits',0,12,102,3
	.word	10780
	.byte	26
	.byte	'Ifx_GPT12_ID_Bits',0,12,110,3
	.word	11234
	.byte	26
	.byte	'Ifx_GPT12_KRST0_Bits',0,12,118,3
	.word	13769
	.byte	26
	.byte	'Ifx_GPT12_KRST1_Bits',0,12,125,3
	.word	13664
	.byte	26
	.byte	'Ifx_GPT12_KRSTCLR_Bits',0,12,132,1,3
	.word	13556
	.byte	26
	.byte	'Ifx_GPT12_OCS_Bits',0,12,142,1,3
	.word	13394
	.byte	26
	.byte	'Ifx_GPT12_PISEL_Bits',0,12,159,1,3
	.word	10938
	.byte	26
	.byte	'Ifx_GPT12_T2_Bits',0,12,166,1,3
	.word	12868
	.byte	26
	.byte	'Ifx_GPT12_T2CON_Bits',0,12,183,1,3
	.word	11357
	.byte	26
	.byte	'Ifx_GPT12_T3_Bits',0,12,190,1,3
	.word	12971
	.byte	26
	.byte	'Ifx_GPT12_T3CON_Bits',0,12,207,1,3
	.word	11640
	.byte	26
	.byte	'Ifx_GPT12_T4_Bits',0,12,214,1,3
	.word	13074
	.byte	26
	.byte	'Ifx_GPT12_T4CON_Bits',0,12,232,1,3
	.word	11914
	.byte	26
	.byte	'Ifx_GPT12_T5_Bits',0,12,239,1,3
	.word	13177
	.byte	26
	.byte	'Ifx_GPT12_T5CON_Bits',0,12,128,2,3
	.word	12212
	.byte	26
	.byte	'Ifx_GPT12_T6_Bits',0,12,135,2,3
	.word	13280
	.byte	26
	.byte	'Ifx_GPT12_T6CON_Bits',0,12,152,2,3
	.word	12483
	.byte	26
	.byte	'Ifx_GPT12_ACCEN0',0,12,165,2,3
	.word	14515
	.byte	26
	.byte	'Ifx_GPT12_ACCEN1',0,12,173,2,3
	.word	13944
	.byte	26
	.byte	'Ifx_GPT12_CAPREL',0,12,181,2,3
	.word	12828
	.byte	26
	.byte	'Ifx_GPT12_CLC',0,12,189,2,3
	.word	10898
	.byte	26
	.byte	'Ifx_GPT12_ID',0,12,197,2,3
	.word	11317
	.byte	26
	.byte	'Ifx_GPT12_KRST0',0,12,205,2,3
	.word	13853
	.byte	26
	.byte	'Ifx_GPT12_KRST1',0,12,213,2,3
	.word	13729
	.byte	26
	.byte	'Ifx_GPT12_KRSTCLR',0,12,221,2,3
	.word	13624
	.byte	26
	.byte	'Ifx_GPT12_OCS',0,12,229,2,3
	.word	13516
	.byte	26
	.byte	'Ifx_GPT12_PISEL',0,12,237,2,3
	.word	11194
	.byte	26
	.byte	'Ifx_GPT12_T2',0,12,245,2,3
	.word	12931
	.byte	26
	.byte	'Ifx_GPT12_T2CON',0,12,253,2,3
	.word	11600
	.byte	26
	.byte	'Ifx_GPT12_T3',0,12,133,3,3
	.word	13034
	.byte	26
	.byte	'Ifx_GPT12_T3CON',0,12,141,3,3
	.word	11874
	.byte	26
	.byte	'Ifx_GPT12_T4',0,12,149,3,3
	.word	13137
	.byte	26
	.byte	'Ifx_GPT12_T4CON',0,12,157,3,3
	.word	12172
	.byte	26
	.byte	'Ifx_GPT12_T5',0,12,165,3,3
	.word	13240
	.byte	26
	.byte	'Ifx_GPT12_T5CON',0,12,173,3,3
	.word	12443
	.byte	26
	.byte	'Ifx_GPT12_T6',0,12,181,3,3
	.word	13343
	.byte	26
	.byte	'Ifx_GPT12_T6CON',0,12,189,3,3
	.word	12718
	.byte	14
	.word	14555
	.byte	26
	.byte	'Ifx_GPT12',0,12,225,3,3
	.word	86265
	.byte	25
	.word	18311
	.byte	26
	.byte	'IfxGpt12_TxEud_In',0,14,73,3
	.word	86289
	.byte	25
	.word	18387
	.byte	26
	.byte	'IfxGpt12_TxIn_In',0,14,82,3
	.word	86320
	.byte	24,14,85,15,20,13
	.byte	'module',0
	.word	14931
	.byte	4,2,35,0,13
	.byte	'timer',0
	.word	521
	.byte	1,2,35,4,13
	.byte	'pin',0
	.word	18153
	.byte	8,2,35,8,13
	.byte	'select',0
	.word	10286
	.byte	1,2,35,16,0,25
	.word	86350
	.byte	26
	.byte	'IfxGpt12_TxOut_Out',0,14,91,3
	.word	86416
	.byte	26
	.byte	'IfxGpt12_CaptureInput',0,11,85,3
	.word	15739
	.byte	15,11,89,9,1,16
	.byte	'IfxGpt12_CaptureInputMode_none',0,0,16
	.byte	'IfxGpt12_CaptureInputMode_risingEdgeTxIN',0,1,16
	.byte	'IfxGpt12_CaptureInputMode_fallingEdgeTxIN',0,2,16
	.byte	'IfxGpt12_CaptureInputMode_bothEdgesTxIN',0,3,0,26
	.byte	'IfxGpt12_CaptureInputMode',0,11,95,3
	.word	86478
	.byte	15,11,100,9,1,16
	.byte	'IfxGpt12_CaptureTrigger_capin',0,0,16
	.byte	'IfxGpt12_CaptureTrigger_t3inOrT3EUD',0,1,0,26
	.byte	'IfxGpt12_CaptureTrigger',0,11,104,3
	.word	86680
	.byte	15,11,109,9,1,16
	.byte	'IfxGpt12_CaptureTriggerMode_disabled',0,0,16
	.byte	'IfxGpt12_CaptureTriggerMode_risingEdge',0,1,16
	.byte	'IfxGpt12_CaptureTriggerMode_fallingEdge',0,2,16
	.byte	'IfxGpt12_CaptureTriggerMode_randomEdge',0,3,0,26
	.byte	'IfxGpt12_CaptureTriggerMode',0,11,115,3
	.word	86788
	.byte	15,11,120,9,1,16
	.byte	'IfxGpt12_CounterInputMode_counterDisabled',0,0,16
	.byte	'IfxGpt12_CounterInputMode_risingEdgeTxIN',0,1,16
	.byte	'IfxGpt12_CounterInputMode_fallingEdgeTxIN',0,2,16
	.byte	'IfxGpt12_CounterInputMode_bothEdgesTxIN',0,3,16
	.byte	'IfxGpt12_CounterInputMode_risingEdgeTxOTL',0,5,16
	.byte	'IfxGpt12_CounterInputMode_fallingEdgeTxOTL',0,6,16
	.byte	'IfxGpt12_CounterInputMode_bothEdgesTxOTL',0,7,0,26
	.byte	'IfxGpt12_CounterInputMode',0,11,129,1,3
	.word	86993
	.byte	26
	.byte	'IfxGpt12_EudInput',0,11,140,1,3
	.word	14936
	.byte	26
	.byte	'IfxGpt12_Gpt1BlockPrescaler',0,11,151,1,3
	.word	18763
	.byte	26
	.byte	'IfxGpt12_Gpt2BlockPrescaler',0,11,162,1,3
	.word	18900
	.byte	15,11,167,1,9,1,16
	.byte	'IfxGpt12_IncrementalInterfaceInputMode_stopCounterTx',0,0,16
	.byte	'IfxGpt12_IncrementalInterfaceInputMode_bothEdgesTxIN',0,1,16
	.byte	'IfxGpt12_IncrementalInterfaceInputMode_bothEdgesTxEUD',0,2,16
	.byte	'IfxGpt12_IncrementalInterfaceInputMode_bothEdgesTxINOrTxEUD',0,3,0,26
	.byte	'IfxGpt12_IncrementalInterfaceInputMode',0,11,173,1,3
	.word	87440
	.byte	26
	.byte	'IfxGpt12_Input',0,11,184,1,3
	.word	15095
	.byte	26
	.byte	'IfxGpt12_Mode',0,11,199,1,3
	.word	15914
	.byte	15,11,204,1,9,1,16
	.byte	'IfxGpt12_ReloadInputMode_counterDisabled',0,0,16
	.byte	'IfxGpt12_ReloadInputMode_risingEdgeTxIN',0,1,16
	.byte	'IfxGpt12_ReloadInputMode_fallingEdgeTxIN',0,2,16
	.byte	'IfxGpt12_ReloadInputMode_bothEdgesTxIN',0,3,16
	.byte	'IfxGpt12_ReloadInputMode_risingEdgeTxOTL',0,5,16
	.byte	'IfxGpt12_ReloadInputMode_fallingEdgeTxOTL',0,6,16
	.byte	'IfxGpt12_ReloadInputMode_bothEdgesTxOTL',0,7,0,26
	.byte	'IfxGpt12_ReloadInputMode',0,11,213,1,3
	.word	87770
	.byte	15,11,218,1,9,1,16
	.byte	'IfxGpt12_SleepMode_enable',0,0,16
	.byte	'IfxGpt12_SleepMode_disable',0,1,0,26
	.byte	'IfxGpt12_SleepMode',0,11,222,1,3
	.word	88109
	.byte	15,11,226,1,9,1,16
	.byte	'IfxGpt12_SuspendMode_none',0,0,16
	.byte	'IfxGpt12_SuspendMode_hard',0,1,16
	.byte	'IfxGpt12_SuspendMode_soft',0,2,0,26
	.byte	'IfxGpt12_SuspendMode',0,11,231,1,3
	.word	88201
	.byte	15,11,236,1,9,1,16
	.byte	'IfxGpt12_TimerDirection_up',0,0,16
	.byte	'IfxGpt12_TimerDirection_down',0,1,0,26
	.byte	'IfxGpt12_TimerDirection',0,11,240,1,3
	.word	88322
	.byte	15,11,245,1,9,1,16
	.byte	'IfxGpt12_TimerDirectionSource_internal',0,0,16
	.byte	'IfxGpt12_TimerDirectionSource_external',0,1,0,26
	.byte	'IfxGpt12_TimerDirectionSource',0,11,249,1,3
	.word	88422
	.byte	26
	.byte	'IfxGpt12_TimerInputPrescaler',0,11,136,2,3
	.word	18487
	.byte	15,11,141,2,9,1,16
	.byte	'IfxGpt12_TimerReloadMode_disable',0,0,16
	.byte	'IfxGpt12_TimerReloadMode_enable',0,1,0,26
	.byte	'IfxGpt12_TimerReloadMode',0,11,145,2,3
	.word	88588
	.byte	15,11,150,2,9,1,16
	.byte	'IfxGpt12_TimerRemoteControl_off',0,0,16
	.byte	'IfxGpt12_TimerRemoteControl_on',0,1,0,26
	.byte	'IfxGpt12_TimerRemoteControl',0,11,154,2,3
	.word	88698
	.byte	15,11,159,2,9,1,16
	.byte	'IfxGpt12_TimerRun_stop',0,0,16
	.byte	'IfxGpt12_TimerRun_start',0,1,0,26
	.byte	'IfxGpt12_TimerRun',0,11,163,2,3
	.word	88809
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L154:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,21,0,54,15,39,12,0,0,3,15,0,73,19,0,0,4,46,1,3,8,32,13
	.byte	58,15,59,15,57,15,54,15,39,12,0,0,5,5,0,3,8,58,15,59,15,57,15,73,19,0,0,6,11,0,0,0,7,36,0,3,8,11,15,62
	.byte	15,0,0,8,46,1,3,8,32,13,58,15,59,15,57,15,73,19,54,15,39,12,0,0,9,59,0,3,8,0,0,10,19,1,3,8,58,15,59,15
	.byte	57,15,11,15,0,0,11,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,12,23,1,58,15,59,15,57,15,11,15,0,0,13,13
	.byte	0,3,8,73,19,11,15,56,9,0,0,14,53,0,73,19,0,0,15,4,1,58,15,59,15,57,15,11,15,0,0,16,40,0,3,8,28,13,0,0
	.byte	17,11,1,0,0,18,1,1,11,15,73,19,0,0,19,33,0,47,15,0,0,20,46,1,49,19,0,0,21,5,0,49,19,0,0,22,46,1,3,8,58
	.byte	15,59,15,57,15,54,15,39,12,63,12,60,12,0,0,23,46,0,3,8,58,15,59,15,57,15,73,19,54,15,39,12,63,12,60,12
	.byte	0,0,24,19,1,58,15,59,15,57,15,11,15,0,0,25,38,0,73,19,0,0,26,22,0,3,8,58,15,59,15,57,15,73,19,0,0,27,21
	.byte	0,54,15,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L155:
	.word	.L560-.L559
.L559:
	.half	3
	.word	.L562-.L561
.L561:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg',0
	.byte	0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_Intrinsics.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Src\\Std\\IfxSrc.h',0
	.byte	0,0,0
	.byte	'IfxSrc_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Scu\\Std\\IfxScuWdt.h',0
	.byte	0,0,0
	.byte	'IfxScu_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxCpu_cfg.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Port\\Std\\IfxPort.h',0
	.byte	0,0,0
	.byte	'IfxPort_regdef.h',0,1,0,0
	.byte	'..\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Gpt12\\Std\\IfxGpt12.h',0,0,0,0
	.byte	'IfxGpt12_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Scu\\Std\\IfxScuCcu.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_PinMap\\IfxGpt12_PinMap.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Ifx_Types.h',0
	.byte	0,0,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Gpt12/Std/IfxGpt12.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxSrc_cfg.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Platform_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxScu_cfg.h',0
	.byte	0,0,0
	.byte	'IfxCpu_regdef.h',0,1,0,0
	.byte	'IfxStm_regdef.h',0,1,0,0
	.byte	'IfxFlash_regdef.h',0,1,0,0,0
.L562:
.L560:
	.sdecl	'.debug_info',debug,cluster('IfxGpt12_disableModule')
	.sect	'.debug_info'
.L156:
	.word	330
	.half	3
	.word	.L157
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Gpt12/Std/IfxGpt12.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L159,.L158
	.byte	2
	.word	.L152
	.byte	3
	.byte	'IfxGpt12_disableModule',0,1,132,2,6,1,1,1
	.word	.L139,.L216,.L138
	.byte	4
	.byte	'gpt12',0,1,132,2,40
	.word	.L217,.L218
	.byte	5
	.word	.L139,.L216
	.byte	6
	.byte	'psw',0,1,134,2,12
	.word	.L219,.L220
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxGpt12_disableModule')
	.sect	'.debug_abbrev'
.L157:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxGpt12_disableModule')
	.sect	'.debug_line'
.L158:
	.word	.L564-.L563
.L563:
	.half	3
	.word	.L566-.L565
.L565:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Gpt12/Std/IfxGpt12.c',0,0,0,0,0
.L566:
	.byte	5,6,7,0,5,2
	.word	.L139
	.byte	3,131,2,1,5,50,9
	.half	.L515-.L139
	.byte	3,2,1,5,16,9
	.half	.L514-.L515
	.byte	1,5,31,9
	.half	.L517-.L514
	.byte	3,1,1,5,17,9
	.half	.L518-.L517
	.byte	3,1,1,5,23,9
	.half	.L567-.L518
	.byte	1,5,29,9
	.half	.L568-.L567
	.byte	3,1,1,5,1,9
	.half	.L520-.L568
	.byte	3,1,1,7,9
	.half	.L160-.L520
	.byte	0,1,1
.L564:
	.sdecl	'.debug_ranges',debug,cluster('IfxGpt12_disableModule')
	.sect	'.debug_ranges'
.L159:
	.word	-1,.L139,0,.L160-.L139,0,0
	.sdecl	'.debug_info',debug,cluster('IfxGpt12_enableModule')
	.sect	'.debug_info'
.L161:
	.word	329
	.half	3
	.word	.L162
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Gpt12/Std/IfxGpt12.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L164,.L163
	.byte	2
	.word	.L152
	.byte	3
	.byte	'IfxGpt12_enableModule',0,1,141,2,6,1,1,1
	.word	.L141,.L221,.L140
	.byte	4
	.byte	'gpt12',0,1,141,2,39
	.word	.L217,.L222
	.byte	5
	.word	.L141,.L221
	.byte	6
	.byte	'psw',0,1,143,2,12
	.word	.L219,.L223
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxGpt12_enableModule')
	.sect	'.debug_abbrev'
.L162:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxGpt12_enableModule')
	.sect	'.debug_line'
.L163:
	.word	.L570-.L569
.L569:
	.half	3
	.word	.L572-.L571
.L571:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Gpt12/Std/IfxGpt12.c',0,0,0,0,0
.L572:
	.byte	5,6,7,0,5,2
	.word	.L141
	.byte	3,140,2,1,5,50,9
	.half	.L522-.L141
	.byte	3,2,1,5,16,9
	.half	.L521-.L522
	.byte	1,5,31,9
	.half	.L524-.L521
	.byte	3,1,1,5,17,9
	.half	.L525-.L524
	.byte	3,1,1,5,23,9
	.half	.L573-.L525
	.byte	1,5,29,9
	.half	.L574-.L573
	.byte	3,1,1,5,1,9
	.half	.L527-.L574
	.byte	3,1,1,7,9
	.half	.L165-.L527
	.byte	0,1,1
.L570:
	.sdecl	'.debug_ranges',debug,cluster('IfxGpt12_enableModule')
	.sect	'.debug_ranges'
.L164:
	.word	-1,.L141,0,.L165-.L141,0,0
	.sdecl	'.debug_info',debug,cluster('IfxGpt12_initTxEudInPin')
	.sect	'.debug_info'
.L166:
	.word	618
	.half	3
	.word	.L167
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Gpt12/Std/IfxGpt12.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L169,.L168
	.byte	2
	.word	.L152
	.byte	3
	.byte	'IfxGpt12_initTxEudInPin',0,1,150,2,6,1,1,1
	.word	.L143,.L224,.L142
	.byte	4
	.byte	'txEudIn',0,1,150,2,55
	.word	.L225,.L226
	.byte	4
	.byte	'inputMode',0,1,150,2,82
	.word	.L227,.L228
	.byte	5
	.word	.L143,.L224
	.byte	6
	.word	.L229,.L230,.L231
	.byte	7
	.word	.L232,.L233
	.byte	7
	.word	.L234,.L235
	.byte	7
	.word	.L236,.L237
	.byte	8
	.word	.L238,.L230,.L231
	.byte	0,6
	.word	.L239,.L240,.L241
	.byte	7
	.word	.L242,.L243
	.byte	7
	.word	.L244,.L245
	.byte	8
	.word	.L246,.L240,.L241
	.byte	0,6
	.word	.L247,.L248,.L249
	.byte	7
	.word	.L250,.L251
	.byte	7
	.word	.L252,.L253
	.byte	8
	.word	.L254,.L248,.L249
	.byte	0,6
	.word	.L255,.L256,.L257
	.byte	7
	.word	.L258,.L259
	.byte	7
	.word	.L260,.L261
	.byte	8
	.word	.L262,.L256,.L257
	.byte	0,6
	.word	.L263,.L264,.L265
	.byte	7
	.word	.L266,.L267
	.byte	7
	.word	.L268,.L269
	.byte	8
	.word	.L270,.L264,.L265
	.byte	0,6
	.word	.L271,.L272,.L273
	.byte	7
	.word	.L274,.L275
	.byte	7
	.word	.L276,.L277
	.byte	8
	.word	.L278,.L272,.L273
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxGpt12_initTxEudInPin')
	.sect	'.debug_abbrev'
.L167:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,29,1,49
	.byte	16,17,1,18,1,0,0,7,5,0,49,16,2,6,0,0,8,11,0,49,16,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxGpt12_initTxEudInPin')
	.sect	'.debug_line'
.L168:
	.word	.L576-.L575
.L575:
	.half	3
	.word	.L578-.L577
.L577:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Gpt12/Std/IfxGpt12.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Port\\Std\\IfxPort.h',0
	.byte	0,0,0
	.byte	'..\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Gpt12\\Std\\IfxGpt12.h',0,0,0,0,0
.L578:
	.byte	5,6,7,0,5,2
	.word	.L143
	.byte	3,149,2,1,5,41,9
	.half	.L530-.L143
	.byte	3,2,1,5,60,9
	.half	.L529-.L530
	.byte	1,4,2,5,40,9
	.half	.L230-.L529
	.byte	3,174,2,1,4,1,5,20,9
	.half	.L231-.L230
	.byte	3,212,125,1,5,10,9
	.half	.L579-.L231
	.byte	3,2,1,9
	.half	.L580-.L579
	.byte	3,3,1,9
	.half	.L581-.L580
	.byte	3,3,1,9
	.half	.L582-.L581
	.byte	3,3,1,9
	.half	.L583-.L582
	.byte	3,3,1,5,40,9
	.half	.L78-.L583
	.byte	3,117,1,5,76,9
	.half	.L584-.L78
	.byte	1,4,3,5,19,9
	.half	.L240-.L584
	.byte	3,192,6,1,5,28,9
	.half	.L585-.L240
	.byte	1,4,1,5,9,9
	.half	.L241-.L585
	.byte	3,193,121,1,5,40,9
	.half	.L79-.L241
	.byte	3,2,1,5,76,9
	.half	.L586-.L79
	.byte	1,4,3,5,19,9
	.half	.L248-.L586
	.byte	3,166,7,1,5,28,9
	.half	.L587-.L248
	.byte	1,4,1,5,9,9
	.half	.L249-.L587
	.byte	3,219,120,1,5,40,9
	.half	.L80-.L249
	.byte	3,2,1,5,76,9
	.half	.L588-.L80
	.byte	1,4,3,5,19,9
	.half	.L256-.L588
	.byte	3,133,8,1,5,28,9
	.half	.L589-.L256
	.byte	1,4,1,5,9,9
	.half	.L257-.L589
	.byte	3,252,119,1,5,40,9
	.half	.L81-.L257
	.byte	3,2,1,5,76,9
	.half	.L590-.L81
	.byte	1,4,3,5,19,9
	.half	.L264-.L590
	.byte	3,252,8,1,5,28,9
	.half	.L591-.L264
	.byte	1,4,1,5,9,9
	.half	.L265-.L591
	.byte	3,133,119,1,5,40,9
	.half	.L82-.L265
	.byte	3,2,1,5,76,9
	.half	.L592-.L82
	.byte	1,4,3,5,19,9
	.half	.L272-.L592
	.byte	3,210,9,1,5,28,9
	.half	.L593-.L272
	.byte	1,4,1,5,9,9
	.half	.L273-.L593
	.byte	3,175,118,1,9
	.half	.L83-.L273
	.byte	3,2,1,5,1,9
	.half	.L84-.L83
	.byte	3,2,1,7,9
	.half	.L170-.L84
	.byte	0,1,1
.L576:
	.sdecl	'.debug_ranges',debug,cluster('IfxGpt12_initTxEudInPin')
	.sect	'.debug_ranges'
.L169:
	.word	-1,.L143,0,.L170-.L143,0,0
	.sdecl	'.debug_info',debug,cluster('IfxGpt12_initTxInPin')
	.sect	'.debug_info'
.L171:
	.word	612
	.half	3
	.word	.L172
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Gpt12/Std/IfxGpt12.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L174,.L173
	.byte	2
	.word	.L152
	.byte	3
	.byte	'IfxGpt12_initTxInPin',0,1,205,2,6,1,1,1
	.word	.L147,.L279,.L146
	.byte	4
	.byte	'txIn',0,1,205,2,51
	.word	.L280,.L281
	.byte	4
	.byte	'inputMode',0,1,205,2,75
	.word	.L227,.L282
	.byte	5
	.word	.L147,.L279
	.byte	6
	.word	.L229,.L283,.L284
	.byte	7
	.word	.L232,.L285
	.byte	7
	.word	.L234,.L286
	.byte	7
	.word	.L236,.L287
	.byte	8
	.word	.L238,.L283,.L284
	.byte	0,6
	.word	.L288,.L289,.L290
	.byte	7
	.word	.L291,.L292
	.byte	7
	.word	.L293,.L294
	.byte	8
	.word	.L295,.L289,.L290
	.byte	0,6
	.word	.L296,.L297,.L298
	.byte	7
	.word	.L299,.L300
	.byte	7
	.word	.L301,.L302
	.byte	8
	.word	.L303,.L297,.L298
	.byte	0,6
	.word	.L304,.L305,.L306
	.byte	7
	.word	.L307,.L308
	.byte	7
	.word	.L309,.L310
	.byte	8
	.word	.L311,.L305,.L306
	.byte	0,6
	.word	.L312,.L313,.L314
	.byte	7
	.word	.L315,.L316
	.byte	7
	.word	.L317,.L318
	.byte	8
	.word	.L319,.L313,.L314
	.byte	0,6
	.word	.L320,.L321,.L322
	.byte	7
	.word	.L323,.L324
	.byte	7
	.word	.L325,.L326
	.byte	8
	.word	.L327,.L321,.L322
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxGpt12_initTxInPin')
	.sect	'.debug_abbrev'
.L172:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,29,1,49
	.byte	16,17,1,18,1,0,0,7,5,0,49,16,2,6,0,0,8,11,0,49,16,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxGpt12_initTxInPin')
	.sect	'.debug_line'
.L173:
	.word	.L595-.L594
.L594:
	.half	3
	.word	.L597-.L596
.L596:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Gpt12/Std/IfxGpt12.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Port\\Std\\IfxPort.h',0
	.byte	0,0,0
	.byte	'..\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Gpt12\\Std\\IfxGpt12.h',0,0,0,0,0
.L597:
	.byte	5,6,7,0,5,2
	.word	.L147
	.byte	3,204,2,1,5,38,9
	.half	.L540-.L147
	.byte	3,2,1,5,54,9
	.half	.L539-.L540
	.byte	1,4,2,5,40,9
	.half	.L283-.L539
	.byte	3,247,1,1,4,1,5,17,9
	.half	.L284-.L283
	.byte	3,139,126,1,5,10,9
	.half	.L598-.L284
	.byte	3,2,1,9
	.half	.L599-.L598
	.byte	3,3,1,9
	.half	.L600-.L599
	.byte	3,3,1,9
	.half	.L601-.L600
	.byte	3,3,1,9
	.half	.L602-.L601
	.byte	3,3,1,5,34,9
	.half	.L102-.L602
	.byte	3,117,1,5,64,9
	.half	.L603-.L102
	.byte	1,4,3,5,19,9
	.half	.L289-.L603
	.byte	3,150,6,1,5,27,9
	.half	.L604-.L289
	.byte	1,4,1,5,9,9
	.half	.L290-.L604
	.byte	3,235,121,1,5,34,9
	.half	.L103-.L290
	.byte	3,2,1,5,64,9
	.half	.L605-.L103
	.byte	1,4,3,5,19,9
	.half	.L297-.L605
	.byte	3,251,6,1,5,27,9
	.half	.L606-.L297
	.byte	1,4,1,5,9,9
	.half	.L298-.L606
	.byte	3,134,121,1,5,34,9
	.half	.L104-.L298
	.byte	3,2,1,5,64,9
	.half	.L607-.L104
	.byte	1,4,3,5,19,9
	.half	.L305-.L607
	.byte	3,218,7,1,5,27,9
	.half	.L608-.L305
	.byte	1,4,1,5,9,9
	.half	.L306-.L608
	.byte	3,167,120,1,5,34,9
	.half	.L105-.L306
	.byte	3,2,1,5,64,9
	.half	.L609-.L105
	.byte	1,4,3,5,19,9
	.half	.L313-.L609
	.byte	3,204,8,1,5,27,9
	.half	.L610-.L313
	.byte	1,4,1,5,9,9
	.half	.L314-.L610
	.byte	3,181,119,1,5,34,9
	.half	.L106-.L314
	.byte	3,2,1,5,64,9
	.half	.L611-.L106
	.byte	1,4,3,5,19,9
	.half	.L321-.L611
	.byte	3,162,9,1,5,27,9
	.half	.L612-.L321
	.byte	1,4,1,5,9,9
	.half	.L322-.L612
	.byte	3,223,118,1,9
	.half	.L107-.L322
	.byte	3,2,1,5,1,9
	.half	.L108-.L107
	.byte	3,2,1,7,9
	.half	.L175-.L108
	.byte	0,1,1
.L595:
	.sdecl	'.debug_ranges',debug,cluster('IfxGpt12_initTxInPin')
	.sect	'.debug_ranges'
.L174:
	.word	-1,.L147,0,.L175-.L147,0,0
	.sdecl	'.debug_info',debug,cluster('IfxGpt12_T2_getFrequency')
	.sect	'.debug_info'
.L176:
	.word	462
	.half	3
	.word	.L177
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Gpt12/Std/IfxGpt12.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L179,.L178
	.byte	2
	.word	.L152
	.byte	3
	.byte	'IfxGpt12_T2_getFrequency',0,1,55,9
	.word	.L328
	.byte	1,1,1
	.word	.L129,.L329,.L128
	.byte	4
	.byte	'gpt12',0,1,55,45
	.word	.L217,.L330
	.byte	5
	.word	.L129,.L329
	.byte	6
	.byte	'freq',0,1,57,34
	.word	.L328,.L331
	.byte	6
	.byte	'mode',0,1,59,34
	.word	.L332,.L333
	.byte	6
	.byte	'prescaler',0,1,60,34
	.word	.L334,.L335
	.byte	6
	.byte	'bps1',0,1,62,34
	.word	.L336,.L337
	.byte	7
	.word	.L338,.L339,.L3
	.byte	8
	.word	.L340,.L341
	.byte	9
	.word	.L342,.L339,.L3
	.byte	0,7
	.word	.L343,.L8,.L12
	.byte	8
	.word	.L344,.L345
	.byte	9
	.word	.L346,.L8,.L12
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxGpt12_T2_getFrequency')
	.sect	'.debug_abbrev'
.L177:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,5,0,49,16,2,6,0,0,9,11,0,49,16,17
	.byte	1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxGpt12_T2_getFrequency')
	.sect	'.debug_line'
.L178:
	.word	.L614-.L613
.L613:
	.half	3
	.word	.L616-.L615
.L615:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Gpt12/Std/IfxGpt12.c',0,0,0,0
	.byte	'..\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Gpt12\\Std\\IfxGpt12.h',0,0,0,0,0
.L616:
	.byte	5,9,7,0,5,2
	.word	.L129
	.byte	3,54,1,4,2,5,5,9
	.half	.L339-.L129
	.byte	3,247,11,1,5,37,7,9
	.half	.L2-.L339
	.byte	3,1,1,5,5,9
	.half	.L442-.L2
	.byte	1,4,1,5,84,9
	.half	.L3-.L442
	.byte	3,143,116,1,5,10,9
	.half	.L443-.L3
	.byte	3,7,1,9
	.half	.L617-.L443
	.byte	3,125,1,9
	.half	.L618-.L617
	.byte	3,6,1,5,23,9
	.half	.L5-.L618
	.byte	3,123,1,5,21,9
	.half	.L444-.L5
	.byte	1,5,9,9
	.half	.L445-.L444
	.byte	3,1,1,5,23,9
	.half	.L4-.L445
	.byte	3,2,1,5,21,9
	.half	.L446-.L4
	.byte	1,5,9,9
	.half	.L447-.L446
	.byte	3,1,1,5,23,9
	.half	.L6-.L447
	.byte	3,2,1,5,21,9
	.half	.L448-.L6
	.byte	1,5,9,9
	.half	.L449-.L448
	.byte	3,1,1,5,23,9
	.half	.L7-.L449
	.byte	3,2,1,5,21,9
	.half	.L450-.L7
	.byte	1,5,9,9
	.half	.L451-.L450
	.byte	3,1,1,4,2,5,41,9
	.half	.L8-.L451
	.byte	3,228,7,1,5,5,9
	.half	.L619-.L8
	.byte	1,4,1,5,61,9
	.half	.L12-.L619
	.byte	3,160,120,1,5,9,9
	.half	.L453-.L12
	.byte	3,2,1,5,48,7,9
	.half	.L620-.L453
	.byte	1,5,90,7,9
	.half	.L621-.L620
	.byte	1,5,24,7,9
	.half	.L13-.L621
	.byte	3,2,1,5,26,9
	.half	.L622-.L13
	.byte	1,5,21,9
	.half	.L623-.L622
	.byte	1,5,39,9
	.half	.L452-.L623
	.byte	1,5,23,9
	.half	.L15-.L452
	.byte	3,4,1,5,21,9
	.half	.L624-.L15
	.byte	1,5,5,9
	.half	.L16-.L624
	.byte	3,3,1,5,1,9
	.half	.L17-.L16
	.byte	3,1,1,7,9
	.half	.L180-.L17
	.byte	0,1,1
.L614:
	.sdecl	'.debug_ranges',debug,cluster('IfxGpt12_T2_getFrequency')
	.sect	'.debug_ranges'
.L179:
	.word	-1,.L129,0,.L180-.L129,0,0
	.sdecl	'.debug_info',debug,cluster('IfxGpt12_T3_getFrequency')
	.sect	'.debug_info'
.L181:
	.word	426
	.half	3
	.word	.L182
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Gpt12/Std/IfxGpt12.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L184,.L183
	.byte	2
	.word	.L152
	.byte	3
	.byte	'IfxGpt12_T3_getFrequency',0,1,96,9
	.word	.L328
	.byte	1,1,1
	.word	.L131,.L347,.L130
	.byte	4
	.byte	'gpt12',0,1,96,45
	.word	.L217,.L348
	.byte	5
	.word	.L131,.L347
	.byte	6
	.byte	'freq',0,1,98,34
	.word	.L328,.L349
	.byte	6
	.byte	'mode',0,1,100,34
	.word	.L332,.L350
	.byte	6
	.byte	'prescaler',0,1,101,34
	.word	.L334,.L351
	.byte	6
	.byte	'bps1',0,1,103,34
	.word	.L336,.L352
	.byte	7
	.word	.L338,.L353,.L19
	.byte	8
	.word	.L340,.L354
	.byte	9
	.word	.L342,.L353,.L19
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxGpt12_T3_getFrequency')
	.sect	'.debug_abbrev'
.L182:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,5,0,49,16,2,6,0,0,9,11,0,49,16,17
	.byte	1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxGpt12_T3_getFrequency')
	.sect	'.debug_line'
.L183:
	.word	.L626-.L625
.L625:
	.half	3
	.word	.L628-.L627
.L627:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Gpt12/Std/IfxGpt12.c',0,0,0,0
	.byte	'..\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Gpt12\\Std\\IfxGpt12.h',0,0,0,0,0
.L628:
	.byte	5,9,7,0,5,2
	.word	.L131
	.byte	3,223,0,1,4,2,5,5,9
	.half	.L353-.L131
	.byte	3,206,11,1,5,37,7,9
	.half	.L18-.L353
	.byte	3,1,1,5,5,9
	.half	.L454-.L18
	.byte	1,4,1,5,84,9
	.half	.L19-.L454
	.byte	3,184,116,1,5,10,9
	.half	.L455-.L19
	.byte	3,7,1,9
	.half	.L629-.L455
	.byte	3,125,1,9
	.half	.L630-.L629
	.byte	3,6,1,5,23,9
	.half	.L21-.L630
	.byte	3,123,1,5,21,9
	.half	.L456-.L21
	.byte	1,5,9,9
	.half	.L457-.L456
	.byte	3,1,1,5,23,9
	.half	.L20-.L457
	.byte	3,2,1,5,21,9
	.half	.L458-.L20
	.byte	1,5,9,9
	.half	.L459-.L458
	.byte	3,1,1,5,23,9
	.half	.L22-.L459
	.byte	3,2,1,5,21,9
	.half	.L460-.L22
	.byte	1,5,9,9
	.half	.L461-.L460
	.byte	3,1,1,5,23,9
	.half	.L23-.L461
	.byte	3,2,1,5,21,9
	.half	.L462-.L23
	.byte	1,5,9,9
	.half	.L463-.L462
	.byte	3,1,1,5,46,9
	.half	.L24-.L463
	.byte	3,3,1,5,61,9
	.half	.L465-.L24
	.byte	3,1,1,5,9,9
	.half	.L467-.L465
	.byte	3,2,1,5,48,7,9
	.half	.L631-.L467
	.byte	1,5,90,7,9
	.half	.L632-.L631
	.byte	1,5,24,7,9
	.half	.L28-.L632
	.byte	3,2,1,5,26,9
	.half	.L466-.L28
	.byte	1,5,21,9
	.half	.L633-.L466
	.byte	1,5,39,9
	.half	.L464-.L633
	.byte	1,5,23,9
	.half	.L30-.L464
	.byte	3,4,1,5,21,9
	.half	.L468-.L30
	.byte	1,5,5,9
	.half	.L31-.L468
	.byte	3,3,1,5,1,9
	.half	.L32-.L31
	.byte	3,1,1,7,9
	.half	.L185-.L32
	.byte	0,1,1
.L626:
	.sdecl	'.debug_ranges',debug,cluster('IfxGpt12_T3_getFrequency')
	.sect	'.debug_ranges'
.L184:
	.word	-1,.L131,0,.L185-.L131,0,0
	.sdecl	'.debug_info',debug,cluster('IfxGpt12_T4_getFrequency')
	.sect	'.debug_info'
.L186:
	.word	432
	.half	3
	.word	.L187
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Gpt12/Std/IfxGpt12.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L189,.L188
	.byte	2
	.word	.L152
	.byte	3
	.byte	'IfxGpt12_T4_getFrequency',0,1,137,1,9
	.word	.L328
	.byte	1,1,1
	.word	.L133,.L355,.L132
	.byte	4
	.byte	'gpt12',0,1,137,1,45
	.word	.L217,.L356
	.byte	5
	.word	.L133,.L355
	.byte	6
	.byte	'freq',0,1,139,1,34
	.word	.L328,.L357
	.byte	6
	.byte	'mode',0,1,141,1,34
	.word	.L332,.L358
	.byte	6
	.byte	'prescaler',0,1,142,1,34
	.word	.L334,.L359
	.byte	6
	.byte	'bps1',0,1,144,1,34
	.word	.L336,.L360
	.byte	7
	.word	.L338,.L361,.L34
	.byte	8
	.word	.L340,.L362
	.byte	9
	.word	.L342,.L361,.L34
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxGpt12_T4_getFrequency')
	.sect	'.debug_abbrev'
.L187:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,5,0,49,16,2,6,0,0,9,11,0,49,16,17
	.byte	1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxGpt12_T4_getFrequency')
	.sect	'.debug_line'
.L188:
	.word	.L635-.L634
.L634:
	.half	3
	.word	.L637-.L636
.L636:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Gpt12/Std/IfxGpt12.c',0,0,0,0
	.byte	'..\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Gpt12\\Std\\IfxGpt12.h',0,0,0,0,0
.L637:
	.byte	5,9,7,0,5,2
	.word	.L133
	.byte	3,136,1,1,4,2,5,5,9
	.half	.L361-.L133
	.byte	3,165,11,1,5,37,7,9
	.half	.L33-.L361
	.byte	3,1,1,5,5,9
	.half	.L469-.L33
	.byte	1,4,1,5,84,9
	.half	.L34-.L469
	.byte	3,225,116,1,5,10,9
	.half	.L470-.L34
	.byte	3,7,1,9
	.half	.L638-.L470
	.byte	3,125,1,9
	.half	.L639-.L638
	.byte	3,6,1,5,23,9
	.half	.L36-.L639
	.byte	3,123,1,5,21,9
	.half	.L471-.L36
	.byte	1,5,9,9
	.half	.L472-.L471
	.byte	3,1,1,5,23,9
	.half	.L35-.L472
	.byte	3,2,1,5,21,9
	.half	.L473-.L35
	.byte	1,5,9,9
	.half	.L474-.L473
	.byte	3,1,1,5,23,9
	.half	.L37-.L474
	.byte	3,2,1,5,21,9
	.half	.L475-.L37
	.byte	1,5,9,9
	.half	.L476-.L475
	.byte	3,1,1,5,23,9
	.half	.L38-.L476
	.byte	3,2,1,5,21,9
	.half	.L477-.L38
	.byte	1,5,9,9
	.half	.L478-.L477
	.byte	3,1,1,5,46,9
	.half	.L39-.L478
	.byte	3,3,1,5,61,9
	.half	.L480-.L39
	.byte	3,1,1,5,9,9
	.half	.L482-.L480
	.byte	3,2,1,5,48,7,9
	.half	.L640-.L482
	.byte	1,5,90,7,9
	.half	.L641-.L640
	.byte	1,5,24,7,9
	.half	.L43-.L641
	.byte	3,2,1,5,26,9
	.half	.L481-.L43
	.byte	1,5,21,9
	.half	.L642-.L481
	.byte	1,5,39,9
	.half	.L479-.L642
	.byte	1,5,23,9
	.half	.L45-.L479
	.byte	3,4,1,5,21,9
	.half	.L483-.L45
	.byte	1,5,5,9
	.half	.L46-.L483
	.byte	3,3,1,5,1,9
	.half	.L47-.L46
	.byte	3,1,1,7,9
	.half	.L190-.L47
	.byte	0,1,1
.L635:
	.sdecl	'.debug_ranges',debug,cluster('IfxGpt12_T4_getFrequency')
	.sect	'.debug_ranges'
.L189:
	.word	-1,.L133,0,.L190-.L133,0,0
	.sdecl	'.debug_info',debug,cluster('IfxGpt12_T5_getFrequency')
	.sect	'.debug_info'
.L191:
	.word	432
	.half	3
	.word	.L192
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Gpt12/Std/IfxGpt12.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L194,.L193
	.byte	2
	.word	.L152
	.byte	3
	.byte	'IfxGpt12_T5_getFrequency',0,1,178,1,9
	.word	.L328
	.byte	1,1,1
	.word	.L135,.L363,.L134
	.byte	4
	.byte	'gpt12',0,1,178,1,45
	.word	.L217,.L364
	.byte	5
	.word	.L135,.L363
	.byte	6
	.byte	'freq',0,1,180,1,34
	.word	.L328,.L365
	.byte	6
	.byte	'mode',0,1,182,1,34
	.word	.L332,.L366
	.byte	6
	.byte	'prescaler',0,1,183,1,34
	.word	.L334,.L367
	.byte	6
	.byte	'bps2',0,1,185,1,34
	.word	.L368,.L369
	.byte	7
	.word	.L338,.L370,.L49
	.byte	8
	.word	.L340,.L371
	.byte	9
	.word	.L342,.L370,.L49
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxGpt12_T5_getFrequency')
	.sect	'.debug_abbrev'
.L192:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,5,0,49,16,2,6,0,0,9,11,0,49,16,17
	.byte	1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxGpt12_T5_getFrequency')
	.sect	'.debug_line'
.L193:
	.word	.L644-.L643
.L643:
	.half	3
	.word	.L646-.L645
.L645:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Gpt12/Std/IfxGpt12.c',0,0,0,0
	.byte	'..\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Gpt12\\Std\\IfxGpt12.h',0,0,0,0,0
.L646:
	.byte	5,9,7,0,5,2
	.word	.L135
	.byte	3,177,1,1,4,2,5,5,9
	.half	.L370-.L135
	.byte	3,252,10,1,5,37,7,9
	.half	.L48-.L370
	.byte	3,1,1,5,5,9
	.half	.L484-.L48
	.byte	1,4,1,5,84,9
	.half	.L49-.L484
	.byte	3,138,117,1,5,10,9
	.half	.L485-.L49
	.byte	3,7,1,9
	.half	.L647-.L485
	.byte	3,125,1,9
	.half	.L648-.L647
	.byte	3,6,1,5,23,9
	.half	.L51-.L648
	.byte	3,123,1,5,21,9
	.half	.L486-.L51
	.byte	1,5,9,9
	.half	.L487-.L486
	.byte	3,1,1,5,23,9
	.half	.L50-.L487
	.byte	3,2,1,5,21,9
	.half	.L488-.L50
	.byte	1,5,9,9
	.half	.L489-.L488
	.byte	3,1,1,5,23,9
	.half	.L52-.L489
	.byte	3,2,1,5,21,9
	.half	.L490-.L52
	.byte	1,5,9,9
	.half	.L491-.L490
	.byte	3,1,1,5,23,9
	.half	.L53-.L491
	.byte	3,2,1,5,21,9
	.half	.L492-.L53
	.byte	1,5,9,9
	.half	.L493-.L492
	.byte	3,1,1,5,46,9
	.half	.L54-.L493
	.byte	3,3,1,5,61,9
	.half	.L495-.L54
	.byte	3,1,1,5,9,9
	.half	.L497-.L495
	.byte	3,2,1,5,48,7,9
	.half	.L649-.L497
	.byte	1,5,90,7,9
	.half	.L650-.L649
	.byte	1,5,24,7,9
	.half	.L58-.L650
	.byte	3,2,1,5,26,9
	.half	.L496-.L58
	.byte	1,5,21,9
	.half	.L651-.L496
	.byte	1,5,39,9
	.half	.L494-.L651
	.byte	1,5,23,9
	.half	.L60-.L494
	.byte	3,4,1,5,21,9
	.half	.L498-.L60
	.byte	1,5,5,9
	.half	.L61-.L498
	.byte	3,3,1,5,1,9
	.half	.L62-.L61
	.byte	3,1,1,7,9
	.half	.L195-.L62
	.byte	0,1,1
.L644:
	.sdecl	'.debug_ranges',debug,cluster('IfxGpt12_T5_getFrequency')
	.sect	'.debug_ranges'
.L194:
	.word	-1,.L135,0,.L195-.L135,0,0
	.sdecl	'.debug_info',debug,cluster('IfxGpt12_T6_getFrequency')
	.sect	'.debug_info'
.L196:
	.word	432
	.half	3
	.word	.L197
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Gpt12/Std/IfxGpt12.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L199,.L198
	.byte	2
	.word	.L152
	.byte	3
	.byte	'IfxGpt12_T6_getFrequency',0,1,219,1,9
	.word	.L328
	.byte	1,1,1
	.word	.L137,.L372,.L136
	.byte	4
	.byte	'gpt12',0,1,219,1,45
	.word	.L217,.L373
	.byte	5
	.word	.L137,.L372
	.byte	6
	.byte	'freq',0,1,221,1,34
	.word	.L328,.L374
	.byte	6
	.byte	'mode',0,1,223,1,34
	.word	.L332,.L375
	.byte	6
	.byte	'prescaler',0,1,224,1,34
	.word	.L334,.L376
	.byte	6
	.byte	'bps2',0,1,226,1,34
	.word	.L368,.L377
	.byte	7
	.word	.L338,.L378,.L64
	.byte	8
	.word	.L340,.L379
	.byte	9
	.word	.L342,.L378,.L64
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxGpt12_T6_getFrequency')
	.sect	'.debug_abbrev'
.L197:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,5,0,49,16,2,6,0,0,9,11,0,49,16,17
	.byte	1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxGpt12_T6_getFrequency')
	.sect	'.debug_line'
.L198:
	.word	.L653-.L652
.L652:
	.half	3
	.word	.L655-.L654
.L654:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Gpt12/Std/IfxGpt12.c',0,0,0,0
	.byte	'..\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Gpt12\\Std\\IfxGpt12.h',0,0,0,0,0
.L655:
	.byte	5,9,7,0,5,2
	.word	.L137
	.byte	3,218,1,1,4,2,5,5,9
	.half	.L378-.L137
	.byte	3,211,10,1,5,37,7,9
	.half	.L63-.L378
	.byte	3,1,1,5,5,9
	.half	.L499-.L63
	.byte	1,4,1,5,84,9
	.half	.L64-.L499
	.byte	3,179,117,1,5,10,9
	.half	.L500-.L64
	.byte	3,7,1,9
	.half	.L656-.L500
	.byte	3,125,1,9
	.half	.L657-.L656
	.byte	3,6,1,5,23,9
	.half	.L66-.L657
	.byte	3,123,1,5,21,9
	.half	.L501-.L66
	.byte	1,5,9,9
	.half	.L502-.L501
	.byte	3,1,1,5,23,9
	.half	.L65-.L502
	.byte	3,2,1,5,21,9
	.half	.L503-.L65
	.byte	1,5,9,9
	.half	.L504-.L503
	.byte	3,1,1,5,23,9
	.half	.L67-.L504
	.byte	3,2,1,5,21,9
	.half	.L505-.L67
	.byte	1,5,9,9
	.half	.L506-.L505
	.byte	3,1,1,5,23,9
	.half	.L68-.L506
	.byte	3,2,1,5,21,9
	.half	.L507-.L68
	.byte	1,5,9,9
	.half	.L508-.L507
	.byte	3,1,1,5,46,9
	.half	.L69-.L508
	.byte	3,3,1,5,61,9
	.half	.L510-.L69
	.byte	3,1,1,5,9,9
	.half	.L512-.L510
	.byte	3,2,1,5,48,7,9
	.half	.L658-.L512
	.byte	1,5,90,7,9
	.half	.L659-.L658
	.byte	1,5,24,7,9
	.half	.L73-.L659
	.byte	3,2,1,5,26,9
	.half	.L511-.L73
	.byte	1,5,21,9
	.half	.L660-.L511
	.byte	1,5,39,9
	.half	.L509-.L660
	.byte	1,5,23,9
	.half	.L75-.L509
	.byte	3,4,1,5,21,9
	.half	.L513-.L75
	.byte	1,5,5,9
	.half	.L76-.L513
	.byte	3,3,1,5,1,9
	.half	.L77-.L76
	.byte	3,1,1,7,9
	.half	.L200-.L77
	.byte	0,1,1
.L653:
	.sdecl	'.debug_ranges',debug,cluster('IfxGpt12_T6_getFrequency')
	.sect	'.debug_ranges'
.L199:
	.word	-1,.L137,0,.L200-.L137,0,0
	.sdecl	'.debug_info',debug,cluster('IfxGpt12_resetModule')
	.sect	'.debug_info'
.L201:
	.word	331
	.half	3
	.word	.L202
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Gpt12/Std/IfxGpt12.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L204,.L203
	.byte	2
	.word	.L152
	.byte	3
	.byte	'IfxGpt12_resetModule',0,1,132,3,6,1,1,1
	.word	.L151,.L380,.L150
	.byte	4
	.byte	'gpt12',0,1,132,3,38
	.word	.L217,.L381
	.byte	5
	.word	.L151,.L380
	.byte	6
	.byte	'passwd',0,1,134,3,12
	.word	.L219,.L382
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxGpt12_resetModule')
	.sect	'.debug_abbrev'
.L202:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxGpt12_resetModule')
	.sect	'.debug_line'
.L203:
	.word	.L662-.L661
.L661:
	.half	3
	.word	.L664-.L663
.L663:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Gpt12/Std/IfxGpt12.c',0,0,0,0,0
.L664:
	.byte	5,6,7,0,5,2
	.word	.L151
	.byte	3,131,3,1,5,53,9
	.half	.L549-.L151
	.byte	3,2,1,5,19,9
	.half	.L548-.L549
	.byte	1,5,31,9
	.half	.L551-.L548
	.byte	3,2,1,5,19,9
	.half	.L552-.L551
	.byte	3,1,1,5,24,9
	.half	.L665-.L552
	.byte	1,5,19,9
	.half	.L666-.L665
	.byte	3,1,1,5,24,9
	.half	.L667-.L666
	.byte	1,5,29,9
	.half	.L668-.L667
	.byte	3,1,1,5,39,9
	.half	.L554-.L668
	.byte	3,2,1,5,31,9
	.half	.L126-.L554
	.byte	1,5,39,9
	.half	.L669-.L126
	.byte	1,5,31,7,9
	.half	.L670-.L669
	.byte	3,4,1,5,21,9
	.half	.L556-.L670
	.byte	3,1,1,5,26,9
	.half	.L671-.L556
	.byte	1,5,29,9
	.half	.L672-.L671
	.byte	3,1,1,5,1,9
	.half	.L558-.L672
	.byte	3,1,1,7,9
	.half	.L205-.L558
	.byte	0,1,1
.L662:
	.sdecl	'.debug_ranges',debug,cluster('IfxGpt12_resetModule')
	.sect	'.debug_ranges'
.L204:
	.word	-1,.L151,0,.L205-.L151,0,0
	.sdecl	'.debug_info',debug,cluster('IfxGpt12_initTxEudInPinWithPadLevel')
	.sect	'.debug_info'
.L206:
	.word	653
	.half	3
	.word	.L207
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Gpt12/Std/IfxGpt12.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L209,.L208
	.byte	2
	.word	.L152
	.byte	3
	.byte	'IfxGpt12_initTxEudInPinWithPadLevel',0,1,177,2,6,1,1,1
	.word	.L145,.L383,.L144
	.byte	4
	.byte	'txEudIn',0,1,177,2,67
	.word	.L225,.L384
	.byte	4
	.byte	'inputMode',0,1,177,2,94
	.word	.L227,.L385
	.byte	4
	.byte	'padDriver',0,1,177,2,123
	.word	.L386,.L387
	.byte	5
	.word	.L145,.L383
	.byte	6
	.word	.L229,.L388,.L389
	.byte	7
	.word	.L232,.L390
	.byte	7
	.word	.L234,.L391
	.byte	7
	.word	.L236,.L392
	.byte	8
	.word	.L238,.L388,.L389
	.byte	0,6
	.word	.L239,.L393,.L394
	.byte	7
	.word	.L242,.L395
	.byte	7
	.word	.L244,.L396
	.byte	8
	.word	.L246,.L393,.L394
	.byte	0,6
	.word	.L247,.L397,.L398
	.byte	7
	.word	.L250,.L399
	.byte	7
	.word	.L252,.L400
	.byte	8
	.word	.L254,.L397,.L398
	.byte	0,6
	.word	.L255,.L401,.L402
	.byte	7
	.word	.L258,.L403
	.byte	7
	.word	.L260,.L404
	.byte	8
	.word	.L262,.L401,.L402
	.byte	0,6
	.word	.L263,.L405,.L406
	.byte	7
	.word	.L266,.L407
	.byte	7
	.word	.L268,.L408
	.byte	8
	.word	.L270,.L405,.L406
	.byte	0,6
	.word	.L271,.L409,.L410
	.byte	7
	.word	.L274,.L411
	.byte	7
	.word	.L276,.L412
	.byte	8
	.word	.L278,.L409,.L410
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxGpt12_initTxEudInPinWithPadLevel')
	.sect	'.debug_abbrev'
.L207:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,29,1,49
	.byte	16,17,1,18,1,0,0,7,5,0,49,16,2,6,0,0,8,11,0,49,16,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxGpt12_initTxEudInPinWithPadLevel')
	.sect	'.debug_line'
.L208:
	.word	.L674-.L673
.L673:
	.half	3
	.word	.L676-.L675
.L675:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Gpt12/Std/IfxGpt12.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Port\\Std\\IfxPort.h',0
	.byte	0,0,0
	.byte	'..\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Gpt12\\Std\\IfxGpt12.h',0,0,0,0,0
.L676:
	.byte	5,6,7,0,5,2
	.word	.L145
	.byte	3,176,2,1,5,41,9
	.half	.L535-.L145
	.byte	3,2,1,5,60,9
	.half	.L533-.L535
	.byte	1,4,2,5,40,9
	.half	.L388-.L533
	.byte	3,147,2,1,4,1,5,41,9
	.half	.L389-.L388
	.byte	3,238,125,1,5,60,9
	.half	.L677-.L389
	.byte	1,5,71,9
	.half	.L678-.L677
	.byte	1,5,20,9
	.half	.L537-.L678
	.byte	3,2,1,5,10,9
	.half	.L679-.L537
	.byte	3,2,1,9
	.half	.L680-.L679
	.byte	3,3,1,9
	.half	.L681-.L680
	.byte	3,3,1,9
	.half	.L682-.L681
	.byte	3,3,1,9
	.half	.L683-.L682
	.byte	3,3,1,5,40,9
	.half	.L90-.L683
	.byte	3,117,1,5,76,9
	.half	.L684-.L90
	.byte	1,4,3,5,19,9
	.half	.L393-.L684
	.byte	3,164,6,1,5,28,9
	.half	.L685-.L393
	.byte	1,4,1,5,9,9
	.half	.L394-.L685
	.byte	3,221,121,1,5,40,9
	.half	.L91-.L394
	.byte	3,2,1,5,76,9
	.half	.L686-.L91
	.byte	1,4,3,5,19,9
	.half	.L397-.L686
	.byte	3,138,7,1,5,28,9
	.half	.L687-.L397
	.byte	1,4,1,5,9,9
	.half	.L398-.L687
	.byte	3,247,120,1,5,40,9
	.half	.L92-.L398
	.byte	3,2,1,5,76,9
	.half	.L688-.L92
	.byte	1,4,3,5,19,9
	.half	.L401-.L688
	.byte	3,233,7,1,5,28,9
	.half	.L689-.L401
	.byte	1,4,1,5,9,9
	.half	.L402-.L689
	.byte	3,152,120,1,5,40,9
	.half	.L93-.L402
	.byte	3,2,1,5,76,9
	.half	.L690-.L93
	.byte	1,4,3,5,19,9
	.half	.L405-.L690
	.byte	3,224,8,1,5,28,9
	.half	.L691-.L405
	.byte	1,4,1,5,9,9
	.half	.L406-.L691
	.byte	3,161,119,1,5,40,9
	.half	.L94-.L406
	.byte	3,2,1,5,76,9
	.half	.L692-.L94
	.byte	1,4,3,5,19,9
	.half	.L409-.L692
	.byte	3,182,9,1,5,28,9
	.half	.L693-.L409
	.byte	1,4,1,5,9,9
	.half	.L410-.L693
	.byte	3,203,118,1,9
	.half	.L95-.L410
	.byte	3,2,1,5,1,9
	.half	.L96-.L95
	.byte	3,2,1,7,9
	.half	.L210-.L96
	.byte	0,1,1
.L674:
	.sdecl	'.debug_ranges',debug,cluster('IfxGpt12_initTxEudInPinWithPadLevel')
	.sect	'.debug_ranges'
.L209:
	.word	-1,.L145,0,.L210-.L145,0,0
	.sdecl	'.debug_info',debug,cluster('IfxGpt12_initTxInPinWithPadLevel')
	.sect	'.debug_info'
.L211:
	.word	647
	.half	3
	.word	.L212
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Gpt12/Std/IfxGpt12.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L214,.L213
	.byte	2
	.word	.L152
	.byte	3
	.byte	'IfxGpt12_initTxInPinWithPadLevel',0,1,232,2,6,1,1,1
	.word	.L149,.L413,.L148
	.byte	4
	.byte	'txIn',0,1,232,2,63
	.word	.L280,.L414
	.byte	4
	.byte	'inputMode',0,1,232,2,87
	.word	.L227,.L415
	.byte	4
	.byte	'padDriver',0,1,232,2,116
	.word	.L386,.L416
	.byte	5
	.word	.L149,.L413
	.byte	6
	.word	.L229,.L417,.L418
	.byte	7
	.word	.L232,.L419
	.byte	7
	.word	.L234,.L420
	.byte	7
	.word	.L236,.L421
	.byte	8
	.word	.L238,.L417,.L418
	.byte	0,6
	.word	.L288,.L422,.L423
	.byte	7
	.word	.L291,.L424
	.byte	7
	.word	.L293,.L425
	.byte	8
	.word	.L295,.L422,.L423
	.byte	0,6
	.word	.L296,.L426,.L427
	.byte	7
	.word	.L299,.L428
	.byte	7
	.word	.L301,.L429
	.byte	8
	.word	.L303,.L426,.L427
	.byte	0,6
	.word	.L304,.L430,.L431
	.byte	7
	.word	.L307,.L432
	.byte	7
	.word	.L309,.L433
	.byte	8
	.word	.L311,.L430,.L431
	.byte	0,6
	.word	.L312,.L434,.L435
	.byte	7
	.word	.L315,.L436
	.byte	7
	.word	.L317,.L437
	.byte	8
	.word	.L319,.L434,.L435
	.byte	0,6
	.word	.L320,.L438,.L439
	.byte	7
	.word	.L323,.L440
	.byte	7
	.word	.L325,.L441
	.byte	8
	.word	.L327,.L438,.L439
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxGpt12_initTxInPinWithPadLevel')
	.sect	'.debug_abbrev'
.L212:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,29,1,49
	.byte	16,17,1,18,1,0,0,7,5,0,49,16,2,6,0,0,8,11,0,49,16,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxGpt12_initTxInPinWithPadLevel')
	.sect	'.debug_line'
.L213:
	.word	.L695-.L694
.L694:
	.half	3
	.word	.L697-.L696
.L696:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Gpt12/Std/IfxGpt12.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Port\\Std\\IfxPort.h',0
	.byte	0,0,0
	.byte	'..\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Gpt12\\Std\\IfxGpt12.h',0,0,0,0,0
.L697:
	.byte	5,6,7,0,5,2
	.word	.L149
	.byte	3,231,2,1,5,38,9
	.half	.L545-.L149
	.byte	3,2,1,5,54,9
	.half	.L543-.L545
	.byte	1,4,2,5,40,9
	.half	.L417-.L543
	.byte	3,220,1,1,4,1,5,38,9
	.half	.L418-.L417
	.byte	3,165,126,1,5,54,9
	.half	.L698-.L418
	.byte	1,5,65,9
	.half	.L699-.L698
	.byte	1,5,17,9
	.half	.L547-.L699
	.byte	3,2,1,5,10,9
	.half	.L700-.L547
	.byte	3,2,1,9
	.half	.L701-.L700
	.byte	3,3,1,9
	.half	.L702-.L701
	.byte	3,3,1,9
	.half	.L703-.L702
	.byte	3,3,1,9
	.half	.L704-.L703
	.byte	3,3,1,5,34,9
	.half	.L114-.L704
	.byte	3,117,1,5,64,9
	.half	.L705-.L114
	.byte	1,4,3,5,19,9
	.half	.L422-.L705
	.byte	3,250,5,1,5,27,9
	.half	.L706-.L422
	.byte	1,4,1,5,9,9
	.half	.L423-.L706
	.byte	3,135,122,1,5,34,9
	.half	.L115-.L423
	.byte	3,2,1,5,64,9
	.half	.L707-.L115
	.byte	1,4,3,5,19,9
	.half	.L426-.L707
	.byte	3,223,6,1,5,27,9
	.half	.L708-.L426
	.byte	1,4,1,5,9,9
	.half	.L427-.L708
	.byte	3,162,121,1,5,34,9
	.half	.L116-.L427
	.byte	3,2,1,5,64,9
	.half	.L709-.L116
	.byte	1,4,3,5,19,9
	.half	.L430-.L709
	.byte	3,190,7,1,5,27,9
	.half	.L710-.L430
	.byte	1,4,1,5,9,9
	.half	.L431-.L710
	.byte	3,195,120,1,5,34,9
	.half	.L117-.L431
	.byte	3,2,1,5,64,9
	.half	.L711-.L117
	.byte	1,4,3,5,19,9
	.half	.L434-.L711
	.byte	3,176,8,1,5,27,9
	.half	.L712-.L434
	.byte	1,4,1,5,9,9
	.half	.L435-.L712
	.byte	3,209,119,1,5,34,9
	.half	.L118-.L435
	.byte	3,2,1,5,64,9
	.half	.L713-.L118
	.byte	1,4,3,5,19,9
	.half	.L438-.L713
	.byte	3,134,9,1,5,27,9
	.half	.L714-.L438
	.byte	1,4,1,5,9,9
	.half	.L439-.L714
	.byte	3,251,118,1,9
	.half	.L119-.L439
	.byte	3,2,1,5,1,9
	.half	.L120-.L119
	.byte	3,2,1,7,9
	.half	.L215-.L120
	.byte	0,1,1
.L695:
	.sdecl	'.debug_ranges',debug,cluster('IfxGpt12_initTxInPinWithPadLevel')
	.sect	'.debug_ranges'
.L214:
	.word	-1,.L149,0,.L215-.L149,0,0
	.sdecl	'.debug_loc',debug,cluster('IfxGpt12_T2_getFrequency')
	.sect	'.debug_loc'
.L128:
	.word	-1,.L129,0,.L329-.L129
	.half	2
	.byte	138,0
	.word	0,0
.L337:
	.word	-1,.L129,.L443-.L129,.L444-.L129
	.half	1
	.byte	95
	.word	.L4-.L129,.L446-.L129
	.half	1
	.byte	95
	.word	.L6-.L129,.L448-.L129
	.half	1
	.byte	95
	.word	.L7-.L129,.L450-.L129
	.half	1
	.byte	95
	.word	0,0
.L331:
	.word	-1,.L129,.L445-.L129,.L4-.L129
	.half	5
	.byte	144,32,157,32,0
	.word	.L447-.L129,.L6-.L129
	.half	5
	.byte	144,32,157,32,0
	.word	.L449-.L129,.L7-.L129
	.half	5
	.byte	144,32,157,32,0
	.word	.L451-.L129,.L452-.L129
	.half	5
	.byte	144,32,157,32,0
	.word	.L452-.L129,.L15-.L129
	.half	1
	.byte	82
	.word	.L15-.L129,.L16-.L129
	.half	5
	.byte	144,32,157,32,0
	.word	.L16-.L129,.L329-.L129
	.half	1
	.byte	82
	.word	0,0
.L330:
	.word	-1,.L129,0,.L442-.L129
	.half	1
	.byte	100
	.word	.L339-.L129,.L329-.L129
	.half	1
	.byte	111
	.word	0,0
.L345:
	.word	0,0
.L341:
	.word	0,0
.L333:
	.word	0,0
.L335:
	.word	-1,.L129,.L453-.L129,.L329-.L129
	.half	1
	.byte	81
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxGpt12_T3_getFrequency')
	.sect	'.debug_loc'
.L130:
	.word	-1,.L131,0,.L347-.L131
	.half	2
	.byte	138,0
	.word	0,0
.L352:
	.word	-1,.L131,.L455-.L131,.L456-.L131
	.half	1
	.byte	95
	.word	.L20-.L131,.L458-.L131
	.half	1
	.byte	95
	.word	.L22-.L131,.L460-.L131
	.half	1
	.byte	95
	.word	.L23-.L131,.L462-.L131
	.half	1
	.byte	95
	.word	0,0
.L349:
	.word	-1,.L131,.L457-.L131,.L20-.L131
	.half	5
	.byte	144,32,157,32,0
	.word	.L459-.L131,.L22-.L131
	.half	5
	.byte	144,32,157,32,0
	.word	.L461-.L131,.L23-.L131
	.half	5
	.byte	144,32,157,32,0
	.word	.L463-.L131,.L464-.L131
	.half	5
	.byte	144,32,157,32,0
	.word	.L464-.L131,.L30-.L131
	.half	1
	.byte	82
	.word	.L30-.L131,.L31-.L131
	.half	5
	.byte	144,32,157,32,0
	.word	.L31-.L131,.L347-.L131
	.half	1
	.byte	82
	.word	0,0
.L348:
	.word	-1,.L131,0,.L454-.L131
	.half	1
	.byte	100
	.word	.L353-.L131,.L347-.L131
	.half	1
	.byte	111
	.word	0,0
.L354:
	.word	0,0
.L350:
	.word	-1,.L131,.L465-.L131,.L466-.L131
	.half	1
	.byte	95
	.word	.L30-.L131,.L468-.L131
	.half	1
	.byte	95
	.word	0,0
.L351:
	.word	-1,.L131,.L467-.L131,.L347-.L131
	.half	1
	.byte	81
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxGpt12_T4_getFrequency')
	.sect	'.debug_loc'
.L132:
	.word	-1,.L133,0,.L355-.L133
	.half	2
	.byte	138,0
	.word	0,0
.L360:
	.word	-1,.L133,.L470-.L133,.L471-.L133
	.half	1
	.byte	95
	.word	.L35-.L133,.L473-.L133
	.half	1
	.byte	95
	.word	.L37-.L133,.L475-.L133
	.half	1
	.byte	95
	.word	.L38-.L133,.L477-.L133
	.half	1
	.byte	95
	.word	0,0
.L357:
	.word	-1,.L133,.L472-.L133,.L35-.L133
	.half	5
	.byte	144,32,157,32,0
	.word	.L474-.L133,.L37-.L133
	.half	5
	.byte	144,32,157,32,0
	.word	.L476-.L133,.L38-.L133
	.half	5
	.byte	144,32,157,32,0
	.word	.L478-.L133,.L479-.L133
	.half	5
	.byte	144,32,157,32,0
	.word	.L479-.L133,.L45-.L133
	.half	1
	.byte	82
	.word	.L45-.L133,.L46-.L133
	.half	5
	.byte	144,32,157,32,0
	.word	.L46-.L133,.L355-.L133
	.half	1
	.byte	82
	.word	0,0
.L356:
	.word	-1,.L133,0,.L469-.L133
	.half	1
	.byte	100
	.word	.L361-.L133,.L355-.L133
	.half	1
	.byte	111
	.word	0,0
.L362:
	.word	0,0
.L358:
	.word	-1,.L133,.L480-.L133,.L481-.L133
	.half	1
	.byte	95
	.word	.L45-.L133,.L483-.L133
	.half	1
	.byte	95
	.word	0,0
.L359:
	.word	-1,.L133,.L482-.L133,.L355-.L133
	.half	1
	.byte	81
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxGpt12_T5_getFrequency')
	.sect	'.debug_loc'
.L134:
	.word	-1,.L135,0,.L363-.L135
	.half	2
	.byte	138,0
	.word	0,0
.L369:
	.word	-1,.L135,.L485-.L135,.L486-.L135
	.half	1
	.byte	95
	.word	.L50-.L135,.L488-.L135
	.half	1
	.byte	95
	.word	.L52-.L135,.L490-.L135
	.half	1
	.byte	95
	.word	.L53-.L135,.L492-.L135
	.half	1
	.byte	95
	.word	0,0
.L365:
	.word	-1,.L135,.L487-.L135,.L50-.L135
	.half	5
	.byte	144,32,157,32,0
	.word	.L489-.L135,.L52-.L135
	.half	5
	.byte	144,32,157,32,0
	.word	.L491-.L135,.L53-.L135
	.half	5
	.byte	144,32,157,32,0
	.word	.L493-.L135,.L494-.L135
	.half	5
	.byte	144,32,157,32,0
	.word	.L494-.L135,.L60-.L135
	.half	1
	.byte	82
	.word	.L60-.L135,.L61-.L135
	.half	5
	.byte	144,32,157,32,0
	.word	.L61-.L135,.L363-.L135
	.half	1
	.byte	82
	.word	0,0
.L364:
	.word	-1,.L135,0,.L484-.L135
	.half	1
	.byte	100
	.word	.L370-.L135,.L363-.L135
	.half	1
	.byte	111
	.word	0,0
.L371:
	.word	0,0
.L366:
	.word	-1,.L135,.L495-.L135,.L496-.L135
	.half	1
	.byte	95
	.word	.L60-.L135,.L498-.L135
	.half	1
	.byte	95
	.word	0,0
.L367:
	.word	-1,.L135,.L497-.L135,.L363-.L135
	.half	1
	.byte	81
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxGpt12_T6_getFrequency')
	.sect	'.debug_loc'
.L136:
	.word	-1,.L137,0,.L372-.L137
	.half	2
	.byte	138,0
	.word	0,0
.L377:
	.word	-1,.L137,.L500-.L137,.L501-.L137
	.half	1
	.byte	95
	.word	.L65-.L137,.L503-.L137
	.half	1
	.byte	95
	.word	.L67-.L137,.L505-.L137
	.half	1
	.byte	95
	.word	.L68-.L137,.L507-.L137
	.half	1
	.byte	95
	.word	0,0
.L374:
	.word	-1,.L137,.L502-.L137,.L65-.L137
	.half	5
	.byte	144,32,157,32,0
	.word	.L504-.L137,.L67-.L137
	.half	5
	.byte	144,32,157,32,0
	.word	.L506-.L137,.L68-.L137
	.half	5
	.byte	144,32,157,32,0
	.word	.L508-.L137,.L509-.L137
	.half	5
	.byte	144,32,157,32,0
	.word	.L509-.L137,.L75-.L137
	.half	1
	.byte	82
	.word	.L75-.L137,.L76-.L137
	.half	5
	.byte	144,32,157,32,0
	.word	.L76-.L137,.L372-.L137
	.half	1
	.byte	82
	.word	0,0
.L373:
	.word	-1,.L137,0,.L499-.L137
	.half	1
	.byte	100
	.word	.L378-.L137,.L372-.L137
	.half	1
	.byte	111
	.word	0,0
.L379:
	.word	0,0
.L375:
	.word	-1,.L137,.L510-.L137,.L511-.L137
	.half	1
	.byte	95
	.word	.L75-.L137,.L513-.L137
	.half	1
	.byte	95
	.word	0,0
.L376:
	.word	-1,.L137,.L512-.L137,.L372-.L137
	.half	1
	.byte	81
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxGpt12_disableModule')
	.sect	'.debug_loc'
.L138:
	.word	-1,.L139,0,.L216-.L139
	.half	2
	.byte	138,0
	.word	0,0
.L218:
	.word	-1,.L139,0,.L514-.L139
	.half	1
	.byte	100
	.word	.L515-.L139,.L216-.L139
	.half	1
	.byte	111
	.word	0,0
.L220:
	.word	-1,.L139,.L514-.L139,.L516-.L139
	.half	1
	.byte	82
	.word	.L517-.L139,.L216-.L139
	.half	1
	.byte	88
	.word	.L516-.L139,.L518-.L139
	.half	1
	.byte	84
	.word	.L519-.L139,.L520-.L139
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxGpt12_enableModule')
	.sect	'.debug_loc'
.L140:
	.word	-1,.L141,0,.L221-.L141
	.half	2
	.byte	138,0
	.word	0,0
.L222:
	.word	-1,.L141,0,.L521-.L141
	.half	1
	.byte	100
	.word	.L522-.L141,.L221-.L141
	.half	1
	.byte	111
	.word	0,0
.L223:
	.word	-1,.L141,.L521-.L141,.L523-.L141
	.half	1
	.byte	82
	.word	.L524-.L141,.L221-.L141
	.half	1
	.byte	95
	.word	.L523-.L141,.L525-.L141
	.half	1
	.byte	84
	.word	.L526-.L141,.L527-.L141
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxGpt12_initTxEudInPin')
	.sect	'.debug_loc'
.L142:
	.word	-1,.L143,0,.L224-.L143
	.half	2
	.byte	138,0
	.word	0,0
.L243:
	.word	0,0
.L251:
	.word	0,0
.L259:
	.word	0,0
.L267:
	.word	0,0
.L275:
	.word	0,0
.L245:
	.word	0,0
.L253:
	.word	0,0
.L261:
	.word	0,0
.L269:
	.word	0,0
.L277:
	.word	0,0
.L228:
	.word	-1,.L143,0,.L528-.L143
	.half	1
	.byte	84
	.word	0,0
.L237:
	.word	0,0
.L235:
	.word	0,0
.L233:
	.word	0,0
.L226:
	.word	-1,.L143,0,.L529-.L143
	.half	1
	.byte	100
	.word	.L530-.L143,.L224-.L143
	.half	1
	.byte	108
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxGpt12_initTxEudInPinWithPadLevel')
	.sect	'.debug_loc'
.L144:
	.word	-1,.L145,0,.L383-.L145
	.half	2
	.byte	138,0
	.word	0,0
.L395:
	.word	0,0
.L399:
	.word	0,0
.L403:
	.word	0,0
.L407:
	.word	0,0
.L411:
	.word	0,0
.L396:
	.word	0,0
.L400:
	.word	0,0
.L404:
	.word	0,0
.L408:
	.word	0,0
.L412:
	.word	0,0
.L385:
	.word	-1,.L145,0,.L531-.L145
	.half	1
	.byte	84
	.word	0,0
.L392:
	.word	0,0
.L387:
	.word	-1,.L145,0,.L532-.L145
	.half	1
	.byte	85
	.word	.L535-.L145,.L383-.L145
	.half	1
	.byte	88
	.word	.L536-.L145,.L537-.L145
	.half	1
	.byte	85
	.word	0,0
.L391:
	.word	0,0
.L390:
	.word	0,0
.L384:
	.word	-1,.L145,0,.L533-.L145
	.half	1
	.byte	100
	.word	.L534-.L145,.L383-.L145
	.half	1
	.byte	108
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxGpt12_initTxInPin')
	.sect	'.debug_loc'
.L146:
	.word	-1,.L147,0,.L279-.L147
	.half	2
	.byte	138,0
	.word	0,0
.L292:
	.word	0,0
.L300:
	.word	0,0
.L308:
	.word	0,0
.L316:
	.word	0,0
.L324:
	.word	0,0
.L294:
	.word	0,0
.L302:
	.word	0,0
.L310:
	.word	0,0
.L318:
	.word	0,0
.L326:
	.word	0,0
.L282:
	.word	-1,.L147,0,.L538-.L147
	.half	1
	.byte	84
	.word	0,0
.L287:
	.word	0,0
.L286:
	.word	0,0
.L285:
	.word	0,0
.L281:
	.word	-1,.L147,0,.L539-.L147
	.half	1
	.byte	100
	.word	.L540-.L147,.L279-.L147
	.half	1
	.byte	108
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxGpt12_initTxInPinWithPadLevel')
	.sect	'.debug_loc'
.L148:
	.word	-1,.L149,0,.L413-.L149
	.half	2
	.byte	138,0
	.word	0,0
.L424:
	.word	0,0
.L428:
	.word	0,0
.L432:
	.word	0,0
.L436:
	.word	0,0
.L440:
	.word	0,0
.L425:
	.word	0,0
.L429:
	.word	0,0
.L433:
	.word	0,0
.L437:
	.word	0,0
.L441:
	.word	0,0
.L415:
	.word	-1,.L149,0,.L541-.L149
	.half	1
	.byte	84
	.word	0,0
.L421:
	.word	0,0
.L416:
	.word	-1,.L149,0,.L542-.L149
	.half	1
	.byte	85
	.word	.L545-.L149,.L413-.L149
	.half	1
	.byte	88
	.word	.L546-.L149,.L547-.L149
	.half	1
	.byte	85
	.word	0,0
.L420:
	.word	0,0
.L419:
	.word	0,0
.L414:
	.word	-1,.L149,0,.L543-.L149
	.half	1
	.byte	100
	.word	.L544-.L149,.L413-.L149
	.half	1
	.byte	108
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxGpt12_resetModule')
	.sect	'.debug_loc'
.L150:
	.word	-1,.L151,0,.L380-.L151
	.half	2
	.byte	138,0
	.word	0,0
.L381:
	.word	-1,.L151,0,.L548-.L151
	.half	1
	.byte	100
	.word	.L549-.L151,.L380-.L151
	.half	1
	.byte	111
	.word	0,0
.L382:
	.word	-1,.L151,.L548-.L151,.L550-.L151
	.half	1
	.byte	82
	.word	.L551-.L151,.L380-.L151
	.half	1
	.byte	88
	.word	.L550-.L151,.L552-.L151
	.half	1
	.byte	84
	.word	.L553-.L151,.L554-.L151
	.half	1
	.byte	84
	.word	.L555-.L151,.L556-.L151
	.half	1
	.byte	84
	.word	.L557-.L151,.L558-.L151
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L715:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('IfxGpt12_T2_getFrequency')
	.sect	'.debug_frame'
	.word	12
	.word	.L715,.L129,.L329-.L129
	.sdecl	'.debug_frame',debug,cluster('IfxGpt12_T3_getFrequency')
	.sect	'.debug_frame'
	.word	12
	.word	.L715,.L131,.L347-.L131
	.sdecl	'.debug_frame',debug,cluster('IfxGpt12_T4_getFrequency')
	.sect	'.debug_frame'
	.word	12
	.word	.L715,.L133,.L355-.L133
	.sdecl	'.debug_frame',debug,cluster('IfxGpt12_T5_getFrequency')
	.sect	'.debug_frame'
	.word	12
	.word	.L715,.L135,.L363-.L135
	.sdecl	'.debug_frame',debug,cluster('IfxGpt12_T6_getFrequency')
	.sect	'.debug_frame'
	.word	12
	.word	.L715,.L137,.L372-.L137
	.sdecl	'.debug_frame',debug,cluster('IfxGpt12_disableModule')
	.sect	'.debug_frame'
	.word	12
	.word	.L715,.L139,.L216-.L139
	.sdecl	'.debug_frame',debug,cluster('IfxGpt12_enableModule')
	.sect	'.debug_frame'
	.word	12
	.word	.L715,.L141,.L221-.L141
	.sdecl	'.debug_frame',debug,cluster('IfxGpt12_initTxEudInPin')
	.sect	'.debug_frame'
	.word	12
	.word	.L715,.L143,.L224-.L143
	.sdecl	'.debug_frame',debug,cluster('IfxGpt12_initTxEudInPinWithPadLevel')
	.sect	'.debug_frame'
	.word	12
	.word	.L715,.L145,.L383-.L145
	.sdecl	'.debug_frame',debug,cluster('IfxGpt12_initTxInPin')
	.sect	'.debug_frame'
	.word	12
	.word	.L715,.L147,.L279-.L147
	.sdecl	'.debug_frame',debug,cluster('IfxGpt12_initTxInPinWithPadLevel')
	.sect	'.debug_frame'
	.word	12
	.word	.L715,.L149,.L413-.L149
	.sdecl	'.debug_frame',debug,cluster('IfxGpt12_resetModule')
	.sect	'.debug_frame'
	.word	12
	.word	.L715,.L151,.L380-.L151
	; Module end
