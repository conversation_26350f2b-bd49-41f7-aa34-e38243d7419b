/**
 * \file Ifx_LutSincosF32.c
 * \brief Sin/Cos lookup functions with fixed-point angle data type
 *
 *
 * \version disabled
 * \copyright Copyright (c) 2013 Infineon Technologies AG. All rights reserved.
 *
 *
 *                                 IMPORTANT NOTICE
 *
 *
 * Use of this file is subject to the terms of use agreed between (i) you or 
 * the company in which ordinary course of business you are acting and (ii) 
 * Infineon Technologies AG or its licensees. If and as long as no such 
 * terms of use are agreed, use of this file is subject to following:


 * Boost Software License - Version 1.0 - August 17th, 2003

 * Permission is hereby granted, free of charge, to any person or 
 * organization obtaining a copy of the software and accompanying 
 * documentation covered by this license (the "Software") to use, reproduce,
 * display, distribute, execute, and transmit the Software, and to prepare
 * derivative works of the Software, and to permit third-parties to whom the 
 * Software is furnished to do so, all subject to the following:

 * The copyright notices in the Software and this entire statement, including
 * the above license grant, this restriction and the following disclaimer, must
 * be included in all copies of the Software, in whole or in part, and all
 * derivative works of the Software, unless such copies or derivative works are
 * solely in the form of machine-executable object code generated by a source
 * language processor.

 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR 
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE, TITLE AND NON-INFRINGEMENT. IN NO EVENT
 * SHALL THE COPYRIGHT HOLDERS OR ANYONE DISTRIBUTING THE SOFTWARE BE LIABLE 
 * FOR ANY DAMAGES OR OTHER LIABILITY, WHETHER IN CONTRACT, TORT OR OTHERWISE,
 * ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
 * DEALINGS IN THE SOFTWARE.

 *
 */
#include "Ifx_LutSincosF32.h"

#include <math.h>

void Ifx_LutSincosF32_init(void)
{
#if IFX_LUT_TABLE_CONST == 0
    {   /* generate sin and cos table */
        sint32 k;

        for (k = 0; k <= (IFX_LUT_ANGLE_PI / 2); k++)
        {
            Ifx_g_LutSincosF32_table[k] = sinf((IFX_PI * 2 * k) / IFX_LUT_ANGLE_RESOLUTION);
        }
    }
#endif
}


float32 Ifx_LutSincosF32_sin(Ifx_Lut_FxpAngle fxpAngle)
{
    float32 result;
    fxpAngle = fxpAngle & (IFX_LUT_ANGLE_RESOLUTION - 1);

    if (fxpAngle < (IFX_LUT_ANGLE_PI / 2))
    {
        result = Ifx_g_LutSincosF32_table[fxpAngle];
    }
    else if (fxpAngle < IFX_LUT_ANGLE_PI)
    {
        fxpAngle = IFX_LUT_ANGLE_PI - fxpAngle;
        result   = Ifx_g_LutSincosF32_table[fxpAngle];
    }
    else if (fxpAngle < (IFX_LUT_ANGLE_PI / 2 * 3))
    {
        fxpAngle = fxpAngle - IFX_LUT_ANGLE_PI;
        result   = -Ifx_g_LutSincosF32_table[fxpAngle];
    }
    else
    {
        fxpAngle = IFX_LUT_ANGLE_RESOLUTION - fxpAngle;
        result   = -Ifx_g_LutSincosF32_table[fxpAngle];
    }

    return result;
}
