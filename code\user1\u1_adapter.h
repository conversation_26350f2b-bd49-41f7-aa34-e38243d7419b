/*
 * u1_adapter.h - 硬件适配层接口
 * 作者: BMW_智能车组_Baiyuyu_Xiaohuihui_Wzz
 * 创建时间: 2025年07月06日
 * 描述: 精简的硬件适配层，专注于UART通信和硬件抽象
 */

#ifndef U1_ADAPTER_H
#define U1_ADAPTER_H

#include "u1_config.h"
#include "zf_driver_uart.h"
#include "zf_driver_delay.h"
#include "zf_common_debug.h"

/* UART中断处理函数声明 - 在isr.c中调用 */
void u1_adapter_uart_rx_handler(void);

/* 初始化硬件适配层 */
boolean u1_adapter_init(void);

/* 检查适配层是否就绪 */
boolean u1_adapter_is_ready(void);

/* 发送数据 */
boolean u1_adapter_send_data(const uint8* data, uint32 len);

/* 接收数据（非阻塞） */
uint32 u1_adapter_receive_data(uint8* buffer, uint32 max_len, uint32 timeout_ms);

/* 检查接收缓冲区中的数据量 */
uint32 u1_adapter_get_rx_count(void);

/* 清空接收缓冲区 */
void u1_adapter_clear_rx_buffer(void);

/* 检查是否有接收溢出 */
boolean u1_adapter_check_rx_overflow(void);

/* 获取适配层状态 */
typedef enum {
    U1_ADAPTER_OK = 0,
    U1_ADAPTER_ERROR_INIT_FAILED,
    U1_ADAPTER_ERROR_SEND_FAILED,
    U1_ADAPTER_ERROR_RX_OVERFLOW,
    U1_ADAPTER_ERROR_INVALID_PARAM
} u1_adapter_status_t;

u1_adapter_status_t u1_adapter_get_status(void);

/* 调试输出接口 */
void u1_adapter_debug_output(const char* msg);

/* 音频采集接口 */
#if U1_FEATURE_AUDIO
boolean u1_adapter_audio_init(void);
boolean u1_adapter_audio_start(void);
boolean u1_adapter_audio_stop(void);
boolean u1_adapter_button_pressed(void);
uint32 u1_adapter_audio_read(int16* buffer, uint32 max_samples);
boolean u1_adapter_audio_available(void);
#endif

#endif /* U1_ADAPTER_H */