	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc35192a --dep-file=zf_device_icm20602.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2\\\\xf_asr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/zf_device/zf_device_icm20602.src ../libraries/zf_device/zf_device_icm20602.c"
	.compiler_name		"ctc"
	;source	'../libraries/zf_device/zf_device_icm20602.c'

	
$TC16X
	
	.sdecl	'.text.zf_device_icm20602.icm20602_write_register',code,cluster('icm20602_write_register')
	.sect	'.text.zf_device_icm20602.icm20602_write_register'
	.align	2
	
; Function icm20602_write_register
.L54:
icm20602_write_register:	.type	func
	mov	e8,d5,d4
.L340:
	mov	d15,#0
	jeq	d15,#0,.L2
	mov	d4,#653
.L150:
	call	get_port
.L149:
	add.a	a2,#4
	mov	d15,#8192
	st.w	[a2],d15
	j	.L3
.L2:
	mov	d4,#653
.L152:
	call	get_port
.L151:
	add.a	a2,#4
	movh	d15,#8192
	st.w	[a2],d15
.L3:
	mov	d4,#0
.L153:
	mov	d5,d8
.L154:
	mov	d6,d9
.L155:
	call	spi_write_8bit_register
.L156:
	mov	d15,#1
	jeq	d15,#0,.L4
	mov	d4,#653
	call	get_port
	add.a	a2,#4
	mov	d15,#8192
	st.w	[a2],d15
	j	.L5
.L4:
	mov	d4,#653
	call	get_port
	add.a	a2,#4
	movh	d15,#8192
	st.w	[a2],d15
.L5:
	ret
.L131:
	
__icm20602_write_register_function_end:
	.size	icm20602_write_register,__icm20602_write_register_function_end-icm20602_write_register
.L90:
	; End of function
	
	.sdecl	'.text.zf_device_icm20602.icm20602_read_register',code,cluster('icm20602_read_register')
	.sect	'.text.zf_device_icm20602.icm20602_read_register'
	.align	2
	
; Function icm20602_read_register
.L56:
icm20602_read_register:	.type	func
	mov	d8,d4
.L158:
	mov	d15,#0
	jeq	d15,#0,.L6
	mov	d4,#653
.L157:
	call	get_port
	add.a	a2,#4
	mov	d15,#8192
	st.w	[a2],d15
	j	.L7
.L6:
	mov	d4,#653
.L160:
	call	get_port
	add.a	a2,#4
	movh	d15,#8192
	st.w	[a2],d15
.L7:
	mov	d4,#0
.L345:
	or	d5,d8,#128
	call	spi_read_8bit_register
.L161:
	mov	d8,d2
.L159:
	mov	d15,#1
	jeq	d15,#0,.L8
	mov	d4,#653
	call	get_port
.L162:
	add.a	a2,#4
	mov	d15,#8192
	st.w	[a2],d15
	j	.L9
.L8:
	mov	d4,#653
	call	get_port
.L163:
	add.a	a2,#4
	movh	d15,#8192
	st.w	[a2],d15
.L9:
	mov	d2,d8
.L164:
	j	.L10
.L10:
	ret
.L134:
	
__icm20602_read_register_function_end:
	.size	icm20602_read_register,__icm20602_read_register_function_end-icm20602_read_register
.L95:
	; End of function
	
	.sdecl	'.text.zf_device_icm20602.icm20602_read_registers',code,cluster('icm20602_read_registers')
	.sect	'.text.zf_device_icm20602.icm20602_read_registers'
	.align	2
	
; Function icm20602_read_registers
.L58:
icm20602_read_registers:	.type	func
	mov	d8,d4
.L167:
	mov.aa	a15,a4
.L168:
	mov	d9,d5
.L169:
	mov	d15,#0
	jeq	d15,#0,.L11
	mov	d4,#653
.L166:
	call	get_port
.L165:
	add.a	a2,#4
	mov	d15,#8192
	st.w	[a2],d15
	j	.L12
.L11:
	mov	d4,#653
.L171:
	call	get_port
.L170:
	add.a	a2,#4
	movh	d15,#8192
	st.w	[a2],d15
.L12:
	mov	d4,#0
.L350:
	or	d5,d8,#128
.L351:
	mov.aa	a4,a15
.L172:
	mov	d6,d9
.L174:
	call	spi_read_8bit_registers
.L173:
	mov	d15,#1
	jeq	d15,#0,.L13
	mov	d4,#653
	call	get_port
	add.a	a2,#4
	mov	d15,#8192
	st.w	[a2],d15
	j	.L14
.L13:
	mov	d4,#653
	call	get_port
	add.a	a2,#4
	movh	d15,#8192
	st.w	[a2],d15
.L14:
	ret
.L137:
	
__icm20602_read_registers_function_end:
	.size	icm20602_read_registers,__icm20602_read_registers_function_end-icm20602_read_registers
.L100:
	; End of function
	
	.sdecl	'.text.zf_device_icm20602.icm20602_self_check',code,cluster('icm20602_self_check')
	.sect	'.text.zf_device_icm20602.icm20602_self_check'
	.align	2
	
; Function icm20602_self_check
.L60:
icm20602_self_check:	.type	func
	mov	d8,#0
.L175:
	mov	d10,#0
.L177:
	mov	d9,#0
.L178:
	j	.L15
.L16:
	mov	d0,d9
	add	d9,#1
.L179:
	extr.u	d9,d9,#0,#16
.L180:
	mov	d15,#255
.L356:
	jge.u	d15,d0,.L17
.L357:
	mov	d10,#1
.L358:
	j	.L18
.L17:
	mov	d4,#117
	call	icm20602_read_register
.L176:
	mov	d8,d2
.L181:
	mov	d4,#10
	call	system_delay_ms
.L15:
	mov	d15,#18
.L359:
	jne	d15,d8,.L16
.L18:
	mov	d2,d10
.L182:
	j	.L19
.L19:
	ret
.L143:
	
__icm20602_self_check_function_end:
	.size	icm20602_self_check,__icm20602_self_check_function_end-icm20602_self_check
.L105:
	; End of function
	
	.sdecl	'.text.zf_device_icm20602.icm20602_get_acc',code,cluster('icm20602_get_acc')
	.sect	'.text.zf_device_icm20602.icm20602_get_acc'
	.align	2
	
	.global	icm20602_get_acc
; Function icm20602_get_acc
.L62:
icm20602_get_acc:	.type	func
	sub.a	a10,#8
.L183:
	mov	d4,#59
.L201:
	lea	a4,[a10]0
.L202:
	mov	d5,#6
	call	icm20602_read_registers
.L203:
	movh.a	a15,#@his(icm20602_acc_x)
	lea	a15,[a15]@los(icm20602_acc_x)
.L204:
	ld.bu	d15,[a10]
.L205:
	sha	d0,d15,#8
.L206:
	ld.bu	d15,[a10]1
.L207:
	or	d0,d15
.L208:
	st.h	[a15],d0
.L209:
	movh.a	a15,#@his(icm20602_acc_y)
	lea	a15,[a15]@los(icm20602_acc_y)
.L210:
	ld.bu	d15,[a10]2
.L211:
	sha	d0,d15,#8
.L212:
	ld.bu	d15,[a10]3
.L213:
	or	d0,d15
.L214:
	st.h	[a15],d0
.L215:
	movh.a	a15,#@his(icm20602_acc_z)
	lea	a15,[a15]@los(icm20602_acc_z)
.L216:
	ld.bu	d15,[a10]4
.L217:
	sha	d0,d15,#8
.L218:
	ld.bu	d15,[a10]5
.L219:
	or	d0,d15
.L220:
	st.h	[a15],d0
.L221:
	ret
.L120:
	
__icm20602_get_acc_function_end:
	.size	icm20602_get_acc,__icm20602_get_acc_function_end-icm20602_get_acc
.L75:
	; End of function
	
	.sdecl	'.text.zf_device_icm20602.icm20602_get_gyro',code,cluster('icm20602_get_gyro')
	.sect	'.text.zf_device_icm20602.icm20602_get_gyro'
	.align	2
	
	.global	icm20602_get_gyro
; Function icm20602_get_gyro
.L64:
icm20602_get_gyro:	.type	func
	sub.a	a10,#8
.L184:
	mov	d4,#67
.L226:
	lea	a4,[a10]0
.L227:
	mov	d5,#6
	call	icm20602_read_registers
.L228:
	movh.a	a15,#@his(icm20602_gyro_x)
	lea	a15,[a15]@los(icm20602_gyro_x)
.L229:
	ld.bu	d15,[a10]
.L230:
	sha	d0,d15,#8
.L231:
	ld.bu	d15,[a10]1
.L232:
	or	d0,d15
.L233:
	st.h	[a15],d0
.L234:
	movh.a	a15,#@his(icm20602_gyro_y)
	lea	a15,[a15]@los(icm20602_gyro_y)
.L235:
	ld.bu	d15,[a10]2
.L236:
	sha	d0,d15,#8
.L237:
	ld.bu	d15,[a10]3
.L238:
	or	d0,d15
.L239:
	st.h	[a15],d0
.L240:
	movh.a	a15,#@his(icm20602_gyro_z)
	lea	a15,[a15]@los(icm20602_gyro_z)
.L241:
	ld.bu	d15,[a10]4
.L242:
	sha	d0,d15,#8
.L243:
	ld.bu	d15,[a10]5
.L244:
	or	d0,d15
.L245:
	st.h	[a15],d0
.L246:
	ret
.L123:
	
__icm20602_get_gyro_function_end:
	.size	icm20602_get_gyro,__icm20602_get_gyro_function_end-icm20602_get_gyro
.L80:
	; End of function
	
	.sdecl	'.text.zf_device_icm20602.icm20602_init',code,cluster('icm20602_init')
	.sect	'.text.zf_device_icm20602.icm20602_init'
	.align	2
	
	.global	icm20602_init
; Function icm20602_init
.L66:
icm20602_init:	.type	func
	sub.a	a10,#16
.L185:
	mov	d9,#0
.L186:
	mov	d8,#0
.L187:
	mov	d4,#10
	call	system_delay_ms
.L251:
	mov	d0,#7
	st.h	[a10],d0
.L252:
	mov	d15,#12
	st.h	[a10]4,d15
.L253:
	mov	d15,#403
	st.h	[a10]8,d15
.L254:
	mov	d4,#0
.L255:
	mov	d5,#0
.L256:
	mov.u	d6,#38528
	addih	d6,d6,#152
.L257:
	mov	d7,#0
	call	spi_init
.L258:
	mov	d4,#653
.L259:
	mov	d5,#1
.L260:
	mov	d6,#1
.L261:
	mov	d7,#3
	call	gpio_init
.L20:
	call	icm20602_self_check
.L262:
	jeq	d2,#0,.L21
.L263:
	mov	d4,#0
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	movh.a	a5,#@his(.2.str)
	lea	a5,[a5]@los(.2.str)
	mov	d5,#237
	call	debug_log_handler
.L264:
	mov	d9,#1
.L265:
	j	.L22
.L21:
	mov	d4,#107
.L266:
	mov	d5,#128
	call	icm20602_write_register
.L267:
	mov	d4,#2
	call	system_delay_ms
.L23:
	mov	d4,#107
	call	icm20602_read_register
.L189:
	mov	d0,d8
	add	d8,#1
.L188:
	extr.u	d8,d8,#0,#16
.L191:
	mov	d15,#255
.L268:
	jge.u	d15,d0,.L24
.L269:
	mov	d4,#0
	movh.a	a4,#@his(.3.str)
	lea	a4,[a4]@los(.3.str)
	movh.a	a5,#@his(.2.str)
	lea	a5,[a5]@los(.2.str)
	mov	d5,#253
	call	debug_log_handler
.L190:
	mov	d9,#1
.L270:
	j	.L25
.L24:
	mov	d15,#65
.L271:
	jne	d15,d2,.L23
.L25:
	jne	d9,#1,.L26
.L272:
	j	.L27
.L26:
	mov	d4,#107
.L273:
	mov	d5,#1
	call	icm20602_write_register
.L274:
	mov	d4,#108
.L275:
	mov	d5,#0
	call	icm20602_write_register
.L276:
	mov	d4,#26
.L277:
	mov	d5,#1
	call	icm20602_write_register
.L278:
	mov	d4,#25
.L279:
	mov	d5,#7
	call	icm20602_write_register
.L280:
	mov	d0,#2
.L281:
	mov	d15,#0
	jeq	d15,d0,.L28
.L282:
	mov	d15,#1
	jeq	d15,d0,.L29
.L283:
	mov	d15,#2
	jeq	d15,d0,.L30
.L284:
	mov	d15,#3
	jeq	d15,d0,.L31
	j	.L32
.L28:
	mov	d4,#28
.L285:
	mov	d5,#0
	call	icm20602_write_register
.L286:
	movh.a	a15,#@his(icm20602_transition_factor)
	lea	a15,[a15]@los(icm20602_transition_factor)
.L287:
	movh	d15,#18048
.L288:
	st.w	[a15],d15
.L289:
	j	.L33
.L29:
	mov	d4,#28
.L290:
	mov	d5,#8
	call	icm20602_write_register
.L291:
	movh.a	a15,#@his(icm20602_transition_factor)
	lea	a15,[a15]@los(icm20602_transition_factor)
.L292:
	movh	d15,#17920
.L293:
	st.w	[a15],d15
.L294:
	j	.L34
.L30:
	mov	d4,#28
.L295:
	mov	d5,#16
	call	icm20602_write_register
.L296:
	movh.a	a15,#@his(icm20602_transition_factor)
	lea	a15,[a15]@los(icm20602_transition_factor)
.L297:
	movh	d15,#17792
.L298:
	st.w	[a15],d15
.L299:
	j	.L35
.L31:
	mov	d4,#28
.L300:
	mov	d5,#24
	call	icm20602_write_register
.L301:
	movh.a	a15,#@his(icm20602_transition_factor)
	lea	a15,[a15]@los(icm20602_transition_factor)
.L302:
	movh	d15,#17664
.L303:
	st.w	[a15],d15
.L304:
	j	.L36
.L32:
	mov	d4,#0
	movh.a	a4,#@his(.4.str)
	lea	a4,[a4]@los(.4.str)
	movh.a	a5,#@his(.2.str)
	lea	a5,[a5]@los(.2.str)
	mov	d5,#297
	call	debug_log_handler
.L305:
	mov	d9,#1
.L306:
	j	.L37
.L37:
.L36:
.L35:
.L34:
.L33:
	jne	d9,#1,.L38
.L307:
	j	.L39
.L38:
	mov	d0,#3
.L308:
	mov	d15,#0
	jeq	d15,d0,.L40
.L309:
	mov	d15,#1
	jeq	d15,d0,.L41
.L310:
	mov	d15,#2
	jeq	d15,d0,.L42
.L311:
	mov	d15,#3
	jeq	d15,d0,.L43
	j	.L44
.L40:
	mov	d4,#27
.L312:
	mov	d5,#0
	call	icm20602_write_register
.L313:
	movh.a	a15,#@his(icm20602_transition_factor)
	lea	a15,[a15]@los(icm20602_transition_factor)
.L314:
	movh	d15,#17155
.L315:
	st.w	[a15]4,d15
.L316:
	j	.L45
.L41:
	mov	d4,#27
.L317:
	mov	d5,#8
	call	icm20602_write_register
.L318:
	movh.a	a15,#@his(icm20602_transition_factor)
	lea	a15,[a15]@los(icm20602_transition_factor)
.L319:
	movh	d15,#17027
.L320:
	st.w	[a15]4,d15
.L321:
	j	.L46
.L42:
	mov	d4,#27
.L322:
	mov	d5,#16
	call	icm20602_write_register
.L323:
	movh.a	a15,#@his(icm20602_transition_factor)
	lea	a15,[a15]@los(icm20602_transition_factor)
.L324:
	mov	d15,#13107
	addih	d15,d15,#16899
.L325:
	st.w	[a15]4,d15
.L326:
	j	.L47
.L43:
	mov	d4,#27
.L327:
	mov	d5,#24
	call	icm20602_write_register
.L328:
	movh.a	a15,#@his(icm20602_transition_factor)
	lea	a15,[a15]@los(icm20602_transition_factor)
.L329:
	mov	d15,#13107
	addih	d15,d15,#16771
.L330:
	st.w	[a15]4,d15
.L331:
	j	.L48
.L44:
	mov	d4,#0
	movh.a	a4,#@his(.5.str)
	lea	a4,[a4]@los(.5.str)
	movh.a	a5,#@his(.2.str)
	lea	a5,[a5]@los(.2.str)
	mov	d5,#335
	call	debug_log_handler
.L332:
	mov	d9,#1
.L333:
	j	.L49
.L49:
.L48:
.L47:
.L46:
.L45:
	jne	d9,#1,.L50
.L334:
	j	.L51
.L50:
	mov	d4,#29
.L335:
	mov	d5,#3
	call	icm20602_write_register
.L51:
.L39:
.L27:
.L22:
	mov	d2,d9
.L192:
	j	.L52
.L52:
	ret
.L126:
	
__icm20602_init_function_end:
	.size	icm20602_init,__icm20602_init_function_end-icm20602_init
.L85:
	; End of function
	
	.sdecl	'.data.zf_device_icm20602.icm20602_gyro_x',data,cluster('icm20602_gyro_x')
	.sect	'.data.zf_device_icm20602.icm20602_gyro_x'
	.global	icm20602_gyro_x
	.align	2
icm20602_gyro_x:	.type	object
	.size	icm20602_gyro_x,2
	.space	2
	.sdecl	'.data.zf_device_icm20602.icm20602_gyro_y',data,cluster('icm20602_gyro_y')
	.sect	'.data.zf_device_icm20602.icm20602_gyro_y'
	.global	icm20602_gyro_y
	.align	2
icm20602_gyro_y:	.type	object
	.size	icm20602_gyro_y,2
	.space	2
	.sdecl	'.data.zf_device_icm20602.icm20602_gyro_z',data,cluster('icm20602_gyro_z')
	.sect	'.data.zf_device_icm20602.icm20602_gyro_z'
	.global	icm20602_gyro_z
	.align	2
icm20602_gyro_z:	.type	object
	.size	icm20602_gyro_z,2
	.space	2
	.sdecl	'.data.zf_device_icm20602.icm20602_acc_x',data,cluster('icm20602_acc_x')
	.sect	'.data.zf_device_icm20602.icm20602_acc_x'
	.global	icm20602_acc_x
	.align	2
icm20602_acc_x:	.type	object
	.size	icm20602_acc_x,2
	.space	2
	.sdecl	'.data.zf_device_icm20602.icm20602_acc_y',data,cluster('icm20602_acc_y')
	.sect	'.data.zf_device_icm20602.icm20602_acc_y'
	.global	icm20602_acc_y
	.align	2
icm20602_acc_y:	.type	object
	.size	icm20602_acc_y,2
	.space	2
	.sdecl	'.data.zf_device_icm20602.icm20602_acc_z',data,cluster('icm20602_acc_z')
	.sect	'.data.zf_device_icm20602.icm20602_acc_z'
	.global	icm20602_acc_z
	.align	2
icm20602_acc_z:	.type	object
	.size	icm20602_acc_z,2
	.space	2
	.sdecl	'.data.zf_device_icm20602.icm20602_transition_factor',data,cluster('icm20602_transition_factor')
	.sect	'.data.zf_device_icm20602.icm20602_transition_factor'
	.global	icm20602_transition_factor
	.align	2
icm20602_transition_factor:	.type	object
	.size	icm20602_transition_factor,8
	.word	1166016512,1099117363
	.sdecl	'.rodata.zf_device_icm20602..1.str',data,rom
	.sect	'.rodata.zf_device_icm20602..1.str'
.1.str:	.type	object
	.size	.1.str,27
	.byte	105,99,109,50,48,54,48,50
	.byte	32,115,101,108,102,32,99,104
	.byte	101,99,107,32,101,114,114,111
	.byte	114,46
	.space	1
	.sdecl	'.rodata.zf_device_icm20602..2.str',data,rom
	.sect	'.rodata.zf_device_icm20602..2.str'
.2.str:	.type	object
	.size	.2.str,44
	.byte	46,46,47,108,105,98,114,97
	.byte	114,105,101,115,47,122,102,95
	.byte	100,101,118,105,99,101,47,122
	.byte	102,95,100,101,118,105,99,101
	.byte	95,105,99,109,50,48,54,48
	.byte	50,46,99
	.space	1
	.sdecl	'.rodata.zf_device_icm20602..3.str',data,rom
	.sect	'.rodata.zf_device_icm20602..3.str'
.3.str:	.type	object
	.size	.3.str,22
	.byte	105,99,109,50,48,54,48,50
	.byte	32,114,101,115,101,116,32,101
	.byte	114,114,111,114
	.byte	46
	.space	1
	.sdecl	'.rodata.zf_device_icm20602..4.str',data,rom
	.sect	'.rodata.zf_device_icm20602..4.str'
.4.str:	.type	object
	.size	.4.str,39
	.byte	73,67,77,50,48,54,48,50
	.byte	95,65,67,67,95,83,65,77
	.byte	80,76,69,95,68,69,70,65
	.byte	85,76,84,32,115,101,116,32
	.byte	101,114,114,111
	.byte	114,46
	.space	1
	.sdecl	'.rodata.zf_device_icm20602..5.str',data,rom
	.sect	'.rodata.zf_device_icm20602..5.str'
.5.str:	.type	object
	.size	.5.str,40
	.byte	73,67,77,50,48,54,48,50
	.byte	95,71,89,82,79,95,83,65
	.byte	77,80,76,69,95,68,69,70
	.byte	65,85,76,84,32,115,101,116
	.byte	32,101,114,114
	.byte	111,114,46
	.space	1
	.calls	'icm20602_write_register','get_port'
	.calls	'icm20602_write_register','spi_write_8bit_register'
	.calls	'icm20602_read_register','get_port'
	.calls	'icm20602_read_register','spi_read_8bit_register'
	.calls	'icm20602_read_registers','get_port'
	.calls	'icm20602_read_registers','spi_read_8bit_registers'
	.calls	'icm20602_self_check','icm20602_read_register'
	.calls	'icm20602_self_check','system_delay_ms'
	.calls	'icm20602_get_acc','icm20602_read_registers'
	.calls	'icm20602_get_gyro','icm20602_read_registers'
	.calls	'icm20602_init','system_delay_ms'
	.calls	'icm20602_init','spi_init'
	.calls	'icm20602_init','gpio_init'
	.calls	'icm20602_init','icm20602_self_check'
	.calls	'icm20602_init','debug_log_handler'
	.calls	'icm20602_init','icm20602_write_register'
	.calls	'icm20602_init','icm20602_read_register'
	.calls	'icm20602_write_register','',0
	.calls	'icm20602_read_register','',0
	.calls	'icm20602_read_registers','',0
	.calls	'icm20602_self_check','',0
	.calls	'icm20602_get_acc','',8
	.calls	'icm20602_get_gyro','',8
	.extern	debug_log_handler
	.extern	system_delay_ms
	.extern	spi_write_8bit_register
	.extern	spi_read_8bit_register
	.extern	spi_read_8bit_registers
	.extern	spi_init
	.extern	get_port
	.extern	gpio_init
	.calls	'icm20602_init','',16
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L68:
	.word	40898
	.half	3
	.word	.L69
	.byte	4
.L67:
	.byte	1
	.byte	'../libraries/zf_device/zf_device_icm20602.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L70
	.byte	2,1,1,3
	.word	206
	.byte	4
	.byte	'Ifx__jump_and_link',0,3,1,61,17,1,1,5
	.byte	'fun',0,1,61,43
	.word	209
	.byte	6,0,7
	.byte	'__fract',0,4,128,1,7
	.byte	'float',0,4,4,8
	.byte	'Ifx__float_to_fract',0,3,1,152,2,18
	.word	254
	.byte	1,1,5
	.byte	'a',0,1,152,2,44
	.word	266
	.byte	6,0,7
	.byte	'unsigned long long int',0,8,7,9
	.byte	'void',0,3
	.word	346
	.byte	8
	.byte	'__ld64',0,3,2,135,1,19
	.word	320
	.byte	1,1,5
	.byte	'addr',0,2,135,1,32
	.word	352
	.byte	6,0,4
	.byte	'__st64',0,3,2,143,1,17,1,1,5
	.byte	'addr',0,2,143,1,30
	.word	352
	.byte	5
	.byte	'value',0,2,143,1,43
	.word	320
	.byte	6,0,7
	.byte	'unsigned int',0,4,7,7
	.byte	'int',0,4,5
.L125:
	.byte	7
	.byte	'unsigned char',0,1,8
.L129:
	.byte	7
	.byte	'unsigned short int',0,2,7,10
	.byte	'_Ifx_P_OUT_Bits',0,4,143,3,16,4,11
	.byte	'P0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	461
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	461
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	461
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	461
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	461
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	461
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	461
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	478
	.byte	16,0,2,35,2,0,12,4,181,5,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	500
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMR_Bits',0,4,169,2,16,4,11
	.byte	'PS0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	461
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	461
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	461
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	461
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	461
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	461
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	461
	.byte	1,0,2,35,1,11
	.byte	'PCL0',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	461
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	461
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	461
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	461
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	461
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	461
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	461
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	461
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	461
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	461
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	461
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	461
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	461
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	461
	.byte	1,0,2,35,3,0,12,4,133,5,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	816
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ID_Bits',0,4,110,16,4,11
	.byte	'MODREV',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	478
	.byte	16,0,2,35,2,0,12,4,148,4,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1387
	.byte	4,2,35,0,0,14,4
	.word	461
	.byte	15,3,0,10
	.byte	'_Ifx_P_IOCR0_Bits',0,4,140,1,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	3,5,2,35,0,11
	.byte	'PC0',0,1
	.word	461
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	461
	.byte	3,5,2,35,1,11
	.byte	'PC1',0,1
	.word	461
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	461
	.byte	3,5,2,35,2,11
	.byte	'PC2',0,1
	.word	461
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	3,5,2,35,3,11
	.byte	'PC3',0,1
	.word	461
	.byte	5,0,2,35,3,0,12,4,164,4,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1515
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR4_Bits',0,4,166,1,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	3,5,2,35,0,11
	.byte	'PC4',0,1
	.word	461
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	461
	.byte	3,5,2,35,1,11
	.byte	'PC5',0,1
	.word	461
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	461
	.byte	3,5,2,35,2,11
	.byte	'PC6',0,1
	.word	461
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	3,5,2,35,3,11
	.byte	'PC7',0,1
	.word	461
	.byte	5,0,2,35,3,0,12,4,180,4,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1730
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR8_Bits',0,4,179,1,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	3,5,2,35,0,11
	.byte	'PC8',0,1
	.word	461
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	461
	.byte	3,5,2,35,1,11
	.byte	'PC9',0,1
	.word	461
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	461
	.byte	3,5,2,35,2,11
	.byte	'PC10',0,1
	.word	461
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	3,5,2,35,3,11
	.byte	'PC11',0,1
	.word	461
	.byte	5,0,2,35,3,0,12,4,188,4,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1945
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR12_Bits',0,4,153,1,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	3,5,2,35,0,11
	.byte	'PC12',0,1
	.word	461
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	461
	.byte	3,5,2,35,1,11
	.byte	'PC13',0,1
	.word	461
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	461
	.byte	3,5,2,35,2,11
	.byte	'PC14',0,1
	.word	461
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	3,5,2,35,3,11
	.byte	'PC15',0,1
	.word	461
	.byte	5,0,2,35,3,0,12,4,172,4,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2162
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IN_Bits',0,4,118,16,4,11
	.byte	'P0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	461
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	461
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	461
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	461
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	461
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	461
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	461
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	478
	.byte	16,0,2,35,2,0,12,4,156,4,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2382
	.byte	4,2,35,0,0,14,24
	.word	461
	.byte	15,23,0,10
	.byte	'_Ifx_P_PDR0_Bits',0,4,205,3,16,4,11
	.byte	'PD0',0,1
	.word	461
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	461
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'PD2',0,1
	.word	461
	.byte	3,5,2,35,1,11
	.byte	'PL2',0,1
	.word	461
	.byte	1,4,2,35,1,11
	.byte	'PD3',0,1
	.word	461
	.byte	3,1,2,35,1,11
	.byte	'PL3',0,1
	.word	461
	.byte	1,0,2,35,1,11
	.byte	'PD4',0,1
	.word	461
	.byte	3,5,2,35,2,11
	.byte	'PL4',0,1
	.word	461
	.byte	1,4,2,35,2,11
	.byte	'PD5',0,1
	.word	461
	.byte	3,1,2,35,2,11
	.byte	'PL5',0,1
	.word	461
	.byte	1,0,2,35,2,11
	.byte	'PD6',0,1
	.word	461
	.byte	3,5,2,35,3,11
	.byte	'PL6',0,1
	.word	461
	.byte	1,4,2,35,3,11
	.byte	'PD7',0,1
	.word	461
	.byte	3,1,2,35,3,11
	.byte	'PL7',0,1
	.word	461
	.byte	1,0,2,35,3,0,12,4,205,5,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2705
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PDR1_Bits',0,4,226,3,16,4,11
	.byte	'PD8',0,1
	.word	461
	.byte	3,5,2,35,0,11
	.byte	'PL8',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'PD9',0,1
	.word	461
	.byte	3,1,2,35,0,11
	.byte	'PL9',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'PD10',0,1
	.word	461
	.byte	3,5,2,35,1,11
	.byte	'PL10',0,1
	.word	461
	.byte	1,4,2,35,1,11
	.byte	'PD11',0,1
	.word	461
	.byte	3,1,2,35,1,11
	.byte	'PL11',0,1
	.word	461
	.byte	1,0,2,35,1,11
	.byte	'PD12',0,1
	.word	461
	.byte	3,5,2,35,2,11
	.byte	'PL12',0,1
	.word	461
	.byte	1,4,2,35,2,11
	.byte	'PD13',0,1
	.word	461
	.byte	3,1,2,35,2,11
	.byte	'PL13',0,1
	.word	461
	.byte	1,0,2,35,2,11
	.byte	'PD14',0,1
	.word	461
	.byte	3,5,2,35,3,11
	.byte	'PL14',0,1
	.word	461
	.byte	1,4,2,35,3,11
	.byte	'PD15',0,1
	.word	461
	.byte	3,1,2,35,3,11
	.byte	'PL15',0,1
	.word	461
	.byte	1,0,2,35,3,0,12,4,213,5,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3009
	.byte	4,2,35,0,0,14,8
	.word	461
	.byte	15,7,0,10
	.byte	'_Ifx_P_ESR_Bits',0,4,88,16,4,11
	.byte	'EN0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	461
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	461
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	461
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	461
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	461
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	461
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	461
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	478
	.byte	16,0,2,35,2,0,12,4,140,4,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3334
	.byte	4,2,35,0,0,14,12
	.word	461
	.byte	15,11,0,10
	.byte	'_Ifx_P_PDISC_Bits',0,4,183,3,16,4,11
	.byte	'PDIS0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'PDIS2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'PDIS3',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'PDIS4',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'PDIS5',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'PDIS6',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'PDIS7',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'PDIS8',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'PDIS9',0,1
	.word	461
	.byte	1,6,2,35,1,11
	.byte	'PDIS10',0,1
	.word	461
	.byte	1,5,2,35,1,11
	.byte	'PDIS11',0,1
	.word	461
	.byte	1,4,2,35,1,11
	.byte	'PDIS12',0,1
	.word	461
	.byte	1,3,2,35,1,11
	.byte	'PDIS13',0,1
	.word	461
	.byte	1,2,2,35,1,11
	.byte	'PDIS14',0,1
	.word	461
	.byte	1,1,2,35,1,11
	.byte	'PDIS15',0,1
	.word	461
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	478
	.byte	16,0,2,35,2,0,12,4,197,5,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3674
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PCSR_Bits',0,4,165,3,16,4,11
	.byte	'SEL0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'SEL1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'SEL2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'SEL3',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'SEL4',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'SEL5',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'SEL6',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'SEL7',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	461
	.byte	2,6,2,35,1,11
	.byte	'SEL10',0,1
	.word	461
	.byte	1,5,2,35,1,11
	.byte	'SEL11',0,1
	.word	461
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	438
	.byte	19,1,2,35,0,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,12,4,189,5,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4040
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR0_Bits',0,4,206,2,16,4,11
	.byte	'PS0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	438
	.byte	28,0,2,35,0,0,12,4,149,5,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4326
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR4_Bits',0,4,227,2,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	4,4,2,35,0,11
	.byte	'PS4',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	438
	.byte	24,0,2,35,0,0,12,4,165,5,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4473
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR8_Bits',0,4,238,2,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'PS8',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	461
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	461
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	461
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	438
	.byte	20,0,2,35,0,0,12,4,173,5,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4642
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR12_Bits',0,4,216,2,16,4,11
	.byte	'reserved_0',0,2
	.word	478
	.byte	12,4,2,35,0,11
	.byte	'PS12',0,1
	.word	461
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	461
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	461
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	461
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	478
	.byte	16,0,2,35,2,0,12,4,157,5,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4814
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR0_Bits',0,4,232,1,16,4,11
	.byte	'reserved_0',0,2
	.word	478
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	461
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	461
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	461
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	478
	.byte	12,0,2,35,2,0,12,4,229,4,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4989
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR4_Bits',0,4,253,1,16,4,11
	.byte	'reserved_0',0,4
	.word	438
	.byte	20,12,2,35,0,11
	.byte	'PCL4',0,1
	.word	461
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	461
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	461
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	461
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	8,0,2,35,3,0,12,4,245,4,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5163
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR8_Bits',0,4,136,2,16,4,11
	.byte	'reserved_0',0,4
	.word	438
	.byte	24,8,2,35,0,11
	.byte	'PCL8',0,1
	.word	461
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	461
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	461
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	461
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	461
	.byte	4,0,2,35,3,0,12,4,253,4,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5337
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR12_Bits',0,4,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	438
	.byte	28,4,2,35,0,11
	.byte	'PCL12',0,1
	.word	461
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	461
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	461
	.byte	1,0,2,35,3,0,12,4,237,4,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5513
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR_Bits',0,4,249,2,16,4,11
	.byte	'PS0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	461
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	461
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	461
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	461
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	461
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	461
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	461
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	478
	.byte	16,0,2,35,2,0,12,4,141,5,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5669
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR_Bits',0,4,147,2,16,4,11
	.byte	'reserved_0',0,2
	.word	478
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	461
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	461
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	461
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	461
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	461
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	461
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	461
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	461
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	461
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	461
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	461
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	461
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	461
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	461
	.byte	1,0,2,35,3,0,12,4,221,4,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6002
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR0_Bits',0,4,192,1,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	438
	.byte	30,0,2,35,0,0,12,4,196,4,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6350
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_Bits',0,4,200,1,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	438
	.byte	30,0,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_P21_Bits',0,4,208,1,16,4,11
	.byte	'RDIS_CTRL',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'RX_DIS',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'TERM',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'LRXTERM',0,1
	.word	461
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	438
	.byte	24,0,2,35,0,0,12,4,204,4,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6474
	.byte	4,2,35,0,13
	.byte	'B_P21',0
	.word	6558
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR2_Bits',0,4,218,1,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'LVDSR',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'LVDSRL',0,1
	.word	461
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	461
	.byte	2,4,2,35,1,11
	.byte	'TDIS_CTRL',0,1
	.word	461
	.byte	1,3,2,35,1,11
	.byte	'TX_DIS',0,1
	.word	461
	.byte	1,2,2,35,1,11
	.byte	'TX_PD',0,1
	.word	461
	.byte	1,1,2,35,1,11
	.byte	'TX_PWDPD',0,1
	.word	461
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	478
	.byte	16,0,2,35,2,0,12,4,213,4,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6738
	.byte	4,2,35,0,0,14,76
	.word	461
	.byte	15,75,0,10
	.byte	'_Ifx_P_ACCEN1_Bits',0,4,82,16,4,11
	.byte	'reserved_0',0,4
	.word	438
	.byte	32,0,2,35,0,0,12,4,132,4,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6991
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ACCEN0_Bits',0,4,45,16,4,11
	.byte	'EN0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	461
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	461
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	461
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	461
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	461
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	461
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	461
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	461
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	461
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	461
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	461
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	461
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	461
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	461
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	461
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	461
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	461
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	461
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	461
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	461
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	461
	.byte	1,0,2,35,3,0,12,4,252,3,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7078
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P',0,4,229,5,25,128,2,13
	.byte	'OUT',0
	.word	776
	.byte	4,2,35,0,13
	.byte	'OMR',0
	.word	1347
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	1466
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	1506
	.byte	4,2,35,12,13
	.byte	'IOCR0',0
	.word	1690
	.byte	4,2,35,16,13
	.byte	'IOCR4',0
	.word	1905
	.byte	4,2,35,20,13
	.byte	'IOCR8',0
	.word	2122
	.byte	4,2,35,24,13
	.byte	'IOCR12',0
	.word	2342
	.byte	4,2,35,28,13
	.byte	'reserved_20',0
	.word	1506
	.byte	4,2,35,32,13
	.byte	'IN',0
	.word	2656
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	2696
	.byte	24,2,35,40,13
	.byte	'PDR0',0
	.word	2969
	.byte	4,2,35,64,13
	.byte	'PDR1',0
	.word	3285
	.byte	4,2,35,68,13
	.byte	'reserved_48',0
	.word	3325
	.byte	8,2,35,72,13
	.byte	'ESR',0
	.word	3625
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	3665
	.byte	12,2,35,84,13
	.byte	'PDISC',0
	.word	4000
	.byte	4,2,35,96,13
	.byte	'PCSR',0
	.word	4286
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	3325
	.byte	8,2,35,104,13
	.byte	'OMSR0',0
	.word	4433
	.byte	4,2,35,112,13
	.byte	'OMSR4',0
	.word	4602
	.byte	4,2,35,116,13
	.byte	'OMSR8',0
	.word	4774
	.byte	4,2,35,120,13
	.byte	'OMSR12',0
	.word	4949
	.byte	4,2,35,124,13
	.byte	'OMCR0',0
	.word	5123
	.byte	4,3,35,128,1,13
	.byte	'OMCR4',0
	.word	5297
	.byte	4,3,35,132,1,13
	.byte	'OMCR8',0
	.word	5473
	.byte	4,3,35,136,1,13
	.byte	'OMCR12',0
	.word	5629
	.byte	4,3,35,140,1,13
	.byte	'OMSR',0
	.word	5962
	.byte	4,3,35,144,1,13
	.byte	'OMCR',0
	.word	6310
	.byte	4,3,35,148,1,13
	.byte	'reserved_98',0
	.word	3325
	.byte	8,3,35,152,1,13
	.byte	'LPCR0',0
	.word	6434
	.byte	4,3,35,160,1,13
	.byte	'LPCR1',0
	.word	6683
	.byte	4,3,35,164,1,13
	.byte	'LPCR2',0
	.word	6942
	.byte	4,3,35,168,1,13
	.byte	'reserved_A4',0
	.word	6982
	.byte	76,3,35,172,1,13
	.byte	'ACCEN1',0
	.word	7038
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	7605
	.byte	4,3,35,252,1,0,16
	.word	7645
	.byte	3
	.word	8248
	.byte	17,3,172,1,9,4,18
	.byte	'IfxPort_State_notChanged',0,0,18
	.byte	'IfxPort_State_high',0,1,18
	.byte	'IfxPort_State_low',0,128,128,4,18
	.byte	'IfxPort_State_toggled',0,129,128,4,0,4
	.byte	'IfxPort_setPinState',0,3,3,208,4,17,1,1,5
	.byte	'port',0,3,208,4,44
	.word	8253
	.byte	5
	.byte	'pinIndex',0,3,208,4,56
	.word	461
	.byte	5
	.byte	'action',0,3,208,4,80
	.word	8258
	.byte	6,0,7
	.byte	'char',0,1,6,3
	.word	8439
	.byte	19
	.byte	'debug_log_handler',0,5,113,9,1,1,1,1,5
	.byte	'pass',0,5,113,47
	.word	461
	.byte	5
	.byte	'str',0,5,113,59
	.word	8447
	.byte	5
	.byte	'file',0,5,113,70
	.word	8447
	.byte	5
	.byte	'line',0,5,113,80
	.word	454
	.byte	0
.L141:
	.byte	7
	.byte	'unsigned long int',0,4,7,19
	.byte	'system_delay_ms',0,6,46,9,1,1,1,1,5
	.byte	'time',0,6,46,45
	.word	8530
	.byte	0,17,7,42,9,1,18
	.byte	'SPI_0',0,0,18
	.byte	'SPI_1',0,1,18
	.byte	'SPI_2',0,2,18
	.byte	'SPI_3',0,3,0,20
	.word	461
	.byte	20
	.word	461
	.byte	19
	.byte	'spi_write_8bit_register',0,7,149,1,13,1,1,1,1,5
	.byte	'spi_n',0,7,149,1,61
	.word	8589
	.byte	5
	.byte	'register_name',0,7,149,1,80
	.word	8627
	.byte	5
	.byte	'data',0,7,149,1,107
	.word	8632
	.byte	0,20
	.word	461
	.byte	21
	.byte	'spi_read_8bit_register',0,7,161,1,13
	.word	461
	.byte	1,1,1,1,5
	.byte	'spi_n',0,7,161,1,61
	.word	8589
	.byte	5
	.byte	'register_name',0,7,161,1,80
	.word	8723
	.byte	0,20
	.word	461
.L139:
	.byte	3
	.word	461
	.byte	19
	.byte	'spi_read_8bit_registers',0,7,162,1,13,1,1,1,1,5
	.byte	'spi_n',0,7,162,1,61
	.word	8589
	.byte	5
	.byte	'register_name',0,7,162,1,80
	.word	8803
	.byte	5
	.byte	'data',0,7,162,1,102
	.word	8808
	.byte	5
	.byte	'len',0,7,162,1,115
	.word	8530
	.byte	0,17,7,50,9,1,18
	.byte	'SPI_MODE0',0,0,18
	.byte	'SPI_MODE1',0,1,18
	.byte	'SPI_MODE2',0,2,18
	.byte	'SPI_MODE3',0,3,0,17,7,58,9,2,18
	.byte	'SPI0_SCLK_P20_11',0,0,18
	.byte	'SPI0_SCLK_P20_13',0,1,18
	.byte	'SPI1_SCLK_P10_2',0,230,0,18
	.byte	'SPI1_SCLK_P11_6',0,231,0,18
	.byte	'SPI2_SCLK_P13_0',0,204,1,18
	.byte	'SPI2_SCLK_P13_1',0,205,1,18
	.byte	'SPI2_SCLK_P15_3',0,206,1,18
	.byte	'SPI2_SCLK_P15_6',0,207,1,18
	.byte	'SPI2_SCLK_P15_8',0,208,1,18
	.byte	'SPI3_SCLK_P02_7',0,178,2,18
	.byte	'SPI3_SCLK_P22_0',0,179,2,18
	.byte	'SPI3_SCLK_P22_1',0,180,2,18
	.byte	'SPI3_SCLK_P22_3',0,181,2,18
	.byte	'SPI3_SCLK_P33_11',0,182,2,0,17,7,69,9,2,18
	.byte	'SPI0_MOSI_P20_12',0,6,18
	.byte	'SPI0_MOSI_P20_14',0,7,18
	.byte	'SPI1_MOSI_P10_1',0,236,0,18
	.byte	'SPI1_MOSI_P10_3',0,237,0,18
	.byte	'SPI1_MOSI_P11_9',0,238,0,18
	.byte	'SPI2_MOSI_P13_3',0,210,1,18
	.byte	'SPI2_MOSI_P15_5',0,211,1,18
	.byte	'SPI2_MOSI_P15_6',0,212,1,18
	.byte	'SPI3_MOSI_P02_6',0,184,2,18
	.byte	'SPI3_MOSI_P10_6',0,185,2,18
	.byte	'SPI3_MOSI_P22_0',0,186,2,18
	.byte	'SPI3_MOSI_P22_3',0,187,2,18
	.byte	'SPI3_MOSI_P33_12',0,188,2,0,17,7,80,9,2,18
	.byte	'SPI0_MISO_P20_12',0,12,18
	.byte	'SPI1_MISO_P10_1',0,242,0,18
	.byte	'SPI1_MISO_P11_3',0,243,0,18
	.byte	'SPI2_MISO_P15_2',0,216,1,18
	.byte	'SPI2_MISO_P15_4',0,217,1,18
	.byte	'SPI2_MISO_P15_7',0,218,1,18
	.byte	'SPI2_MISO_P21_2',0,219,1,18
	.byte	'SPI2_MISO_P21_3',0,220,1,18
	.byte	'SPI3_MISO_P02_5',0,190,2,18
	.byte	'SPI3_MISO_P22_1',0,191,2,18
	.byte	'SPI3_MISO_P21_2',0,192,2,18
	.byte	'SPI3_MISO_P21_3',0,193,2,18
	.byte	'SPI3_MISO_P33_13',0,194,2,0,17,7,91,9,2,18
	.byte	'SPI0_CS0_P20_8',0,18,18
	.byte	'SPI0_CS1_P20_9',0,24,18
	.byte	'SPI0_CS2_P20_13',0,30,18
	.byte	'SPI0_CS3_P11_10',0,36,18
	.byte	'SPI0_CS4_P11_11',0,42,18
	.byte	'SPI0_CS5_P11_2',0,48,18
	.byte	'SPI0_CS6_P20_10',0,54,18
	.byte	'SPI0_CS7_P33_5',0,60,18
	.byte	'SPI0_CS8_P20_6',0,194,0,18
	.byte	'SPI0_CS9_P20_3',0,200,0,18
	.byte	'SPI0_CS13_P15_0',0,224,0,18
	.byte	'SPI1_CS0_P20_8',0,248,0,18
	.byte	'SPI1_CS1_P20_9',0,254,0,18
	.byte	'SPI1_CS2_P20_13',0,132,1,18
	.byte	'SPI1_CS3_P11_10',0,138,1,18
	.byte	'SPI1_CS4_P11_11',0,144,1,18
	.byte	'SPI1_CS5_P11_2',0,150,1,18
	.byte	'SPI1_CS6_P33_10',0,156,1,18
	.byte	'SPI1_CS7_P33_5',0,162,1,18
	.byte	'SPI1_CS8_P10_4',0,168,1,18
	.byte	'SPI1_CS9_P10_5',0,174,1,18
	.byte	'SPI2_CS0_P15_2',0,222,1,18
	.byte	'SPI2_CS1_P14_2',0,228,1,18
	.byte	'SPI2_CS2_P14_6',0,234,1,18
	.byte	'SPI2_CS3_P14_3',0,240,1,18
	.byte	'SPI2_CS5_P15_1',0,252,1,18
	.byte	'SPI2_CS6_P33_13',0,130,2,18
	.byte	'SPI2_CS7_P20_10',0,136,2,18
	.byte	'SPI2_CS8_P20_6',0,142,2,18
	.byte	'SPI2_CS9_P20_3',0,148,2,18
	.byte	'SPI3_CS0_P02_4',0,196,2,18
	.byte	'SPI3_CS1_P02_0',0,202,2,18
	.byte	'SPI3_CS1_P33_9',0,203,2,18
	.byte	'SPI3_CS2_P02_1',0,208,2,18
	.byte	'SPI3_CS2_P33_8',0,209,2,18
	.byte	'SPI3_CS3_P02_2',0,214,2,18
	.byte	'SPI3_CS4_P02_3',0,220,2,18
	.byte	'SPI3_CS5_P02_8',0,226,2,18
	.byte	'SPI3_CS6_P00_8',0,232,2,18
	.byte	'SPI3_CS7_P00_9',0,238,2,18
	.byte	'SPI3_CS7_P33_7',0,239,2,18
	.byte	'SPI3_CS8_P10_5',0,244,2,18
	.byte	'SPI3_CS11_P33_10',0,134,3,18
	.byte	'SPI3_CS12_P22_2',0,140,3,18
	.byte	'SPI3_CS13_P23_1',0,146,3,18
	.byte	'SPI_CS_NULL',0,147,3,0,19
	.byte	'spi_init',0,7,170,1,13,1,1,1,1,5
	.byte	'spi_n',0,7,170,1,61
	.word	8589
	.byte	5
	.byte	'mode',0,7,170,1,82
	.word	8912
	.byte	5
	.byte	'baud',0,7,170,1,95
	.word	8530
	.byte	5
	.byte	'sck_pin',0,7,170,1,118
	.word	8966
	.byte	5
	.byte	'mosi_pin',0,7,170,1,145,1
	.word	9239
	.byte	5
	.byte	'miso_pin',0,7,170,1,173,1
	.word	9493
	.byte	5
	.byte	'cs_pin',0,7,170,1,199,1
	.word	9747
	.byte	0,22
	.word	214
	.byte	23
	.word	240
	.byte	6,0,22
	.word	275
	.byte	23
	.word	307
	.byte	6,0,22
	.word	357
	.byte	23
	.word	376
	.byte	6,0,22
	.word	392
	.byte	23
	.word	407
	.byte	23
	.word	421
	.byte	6,0,22
	.word	8361
	.byte	23
	.word	8389
	.byte	23
	.word	8403
	.byte	23
	.word	8421
	.byte	6,0,17,8,42,9,2,18
	.byte	'P00_0',0,0,18
	.byte	'P00_1',0,1,18
	.byte	'P00_2',0,2,18
	.byte	'P00_3',0,3,18
	.byte	'P00_4',0,4,18
	.byte	'P00_5',0,5,18
	.byte	'P00_6',0,6,18
	.byte	'P00_7',0,7,18
	.byte	'P00_8',0,8,18
	.byte	'P00_9',0,9,18
	.byte	'P00_10',0,10,18
	.byte	'P00_11',0,11,18
	.byte	'P00_12',0,12,18
	.byte	'P00_13',0,13,18
	.byte	'P00_14',0,14,18
	.byte	'P00_15',0,15,18
	.byte	'P02_0',0,192,0,18
	.byte	'P02_1',0,193,0,18
	.byte	'P02_2',0,194,0,18
	.byte	'P02_3',0,195,0,18
	.byte	'P02_4',0,196,0,18
	.byte	'P02_5',0,197,0,18
	.byte	'P02_6',0,198,0,18
	.byte	'P02_7',0,199,0,18
	.byte	'P02_8',0,200,0,18
	.byte	'P02_9',0,201,0,18
	.byte	'P02_10',0,202,0,18
	.byte	'P02_11',0,203,0,18
	.byte	'P02_12',0,204,0,18
	.byte	'P02_13',0,205,0,18
	.byte	'P02_14',0,206,0,18
	.byte	'P02_15',0,207,0,18
	.byte	'P10_0',0,192,2,18
	.byte	'P10_1',0,193,2,18
	.byte	'P10_2',0,194,2,18
	.byte	'P10_3',0,195,2,18
	.byte	'P10_4',0,196,2,18
	.byte	'P10_5',0,197,2,18
	.byte	'P10_6',0,198,2,18
	.byte	'P10_7',0,199,2,18
	.byte	'P10_8',0,200,2,18
	.byte	'P10_9',0,201,2,18
	.byte	'P10_10',0,202,2,18
	.byte	'P10_11',0,203,2,18
	.byte	'P10_12',0,204,2,18
	.byte	'P10_13',0,205,2,18
	.byte	'P10_14',0,206,2,18
	.byte	'P10_15',0,207,2,18
	.byte	'P11_0',0,224,2,18
	.byte	'P11_1',0,225,2,18
	.byte	'P11_2',0,226,2,18
	.byte	'P11_3',0,227,2,18
	.byte	'P11_4',0,228,2,18
	.byte	'P11_5',0,229,2,18
	.byte	'P11_6',0,230,2,18
	.byte	'P11_7',0,231,2,18
	.byte	'P11_8',0,232,2,18
	.byte	'P11_9',0,233,2,18
	.byte	'P11_10',0,234,2,18
	.byte	'P11_11',0,235,2,18
	.byte	'P11_12',0,236,2,18
	.byte	'P11_13',0,237,2,18
	.byte	'P11_14',0,238,2,18
	.byte	'P11_15',0,239,2,18
	.byte	'P13_0',0,160,3,18
	.byte	'P13_1',0,161,3,18
	.byte	'P13_2',0,162,3,18
	.byte	'P13_3',0,163,3,18
	.byte	'P13_4',0,164,3,18
	.byte	'P13_5',0,165,3,18
	.byte	'P13_6',0,166,3,18
	.byte	'P13_7',0,167,3,18
	.byte	'P13_8',0,168,3,18
	.byte	'P13_9',0,169,3,18
	.byte	'P13_10',0,170,3,18
	.byte	'P13_11',0,171,3,18
	.byte	'P13_12',0,172,3,18
	.byte	'P13_13',0,173,3,18
	.byte	'P13_14',0,174,3,18
	.byte	'P13_15',0,175,3,18
	.byte	'P14_0',0,192,3,18
	.byte	'P14_1',0,193,3,18
	.byte	'P14_2',0,194,3,18
	.byte	'P14_3',0,195,3,18
	.byte	'P14_4',0,196,3,18
	.byte	'P14_5',0,197,3,18
	.byte	'P14_6',0,198,3,18
	.byte	'P14_7',0,199,3,18
	.byte	'P14_8',0,200,3,18
	.byte	'P14_9',0,201,3,18
	.byte	'P14_10',0,202,3,18
	.byte	'P14_11',0,203,3,18
	.byte	'P14_12',0,204,3,18
	.byte	'P14_13',0,205,3,18
	.byte	'P14_14',0,206,3,18
	.byte	'P14_15',0,207,3,18
	.byte	'P15_0',0,224,3,18
	.byte	'P15_1',0,225,3,18
	.byte	'P15_2',0,226,3,18
	.byte	'P15_3',0,227,3,18
	.byte	'P15_4',0,228,3,18
	.byte	'P15_5',0,229,3,18
	.byte	'P15_6',0,230,3,18
	.byte	'P15_7',0,231,3,18
	.byte	'P15_8',0,232,3,18
	.byte	'P15_9',0,233,3,18
	.byte	'P15_10',0,234,3,18
	.byte	'P15_11',0,235,3,18
	.byte	'P15_12',0,236,3,18
	.byte	'P15_13',0,237,3,18
	.byte	'P15_14',0,238,3,18
	.byte	'P15_15',0,239,3,18
	.byte	'P20_0',0,128,5,18
	.byte	'P20_1',0,129,5,18
	.byte	'P20_2',0,130,5,18
	.byte	'P20_3',0,131,5,18
	.byte	'P20_4',0,132,5,18
	.byte	'P20_5',0,133,5,18
	.byte	'P20_6',0,134,5,18
	.byte	'P20_7',0,135,5,18
	.byte	'P20_8',0,136,5,18
	.byte	'P20_9',0,137,5,18
	.byte	'P20_10',0,138,5,18
	.byte	'P20_11',0,139,5,18
	.byte	'P20_12',0,140,5,18
	.byte	'P20_13',0,141,5,18
	.byte	'P20_14',0,142,5,18
	.byte	'P20_15',0,143,5,18
	.byte	'P21_0',0,160,5,18
	.byte	'P21_1',0,161,5,18
	.byte	'P21_2',0,162,5,18
	.byte	'P21_3',0,163,5,18
	.byte	'P21_4',0,164,5,18
	.byte	'P21_5',0,165,5,18
	.byte	'P21_6',0,166,5,18
	.byte	'P21_7',0,167,5,18
	.byte	'P21_8',0,168,5,18
	.byte	'P21_9',0,169,5,18
	.byte	'P21_10',0,170,5,18
	.byte	'P21_11',0,171,5,18
	.byte	'P21_12',0,172,5,18
	.byte	'P21_13',0,173,5,18
	.byte	'P21_14',0,174,5,18
	.byte	'P21_15',0,175,5,18
	.byte	'P22_0',0,192,5,18
	.byte	'P22_1',0,193,5,18
	.byte	'P22_2',0,194,5,18
	.byte	'P22_3',0,195,5,18
	.byte	'P22_4',0,196,5,18
	.byte	'P22_5',0,197,5,18
	.byte	'P22_6',0,198,5,18
	.byte	'P22_7',0,199,5,18
	.byte	'P22_8',0,200,5,18
	.byte	'P22_9',0,201,5,18
	.byte	'P22_10',0,202,5,18
	.byte	'P22_11',0,203,5,18
	.byte	'P22_12',0,204,5,18
	.byte	'P22_13',0,205,5,18
	.byte	'P22_14',0,206,5,18
	.byte	'P22_15',0,207,5,18
	.byte	'P23_0',0,224,5,18
	.byte	'P23_1',0,225,5,18
	.byte	'P23_2',0,226,5,18
	.byte	'P23_3',0,227,5,18
	.byte	'P23_4',0,228,5,18
	.byte	'P23_5',0,229,5,18
	.byte	'P23_6',0,230,5,18
	.byte	'P23_7',0,231,5,18
	.byte	'P23_8',0,232,5,18
	.byte	'P23_9',0,233,5,18
	.byte	'P23_10',0,234,5,18
	.byte	'P23_11',0,235,5,18
	.byte	'P23_12',0,236,5,18
	.byte	'P23_13',0,237,5,18
	.byte	'P23_14',0,238,5,18
	.byte	'P23_15',0,239,5,18
	.byte	'P32_0',0,128,8,18
	.byte	'P32_1',0,129,8,18
	.byte	'P32_2',0,130,8,18
	.byte	'P32_3',0,131,8,18
	.byte	'P32_4',0,132,8,18
	.byte	'P32_5',0,133,8,18
	.byte	'P32_6',0,134,8,18
	.byte	'P32_7',0,135,8,18
	.byte	'P32_8',0,136,8,18
	.byte	'P32_9',0,137,8,18
	.byte	'P32_10',0,138,8,18
	.byte	'P32_11',0,139,8,18
	.byte	'P32_12',0,140,8,18
	.byte	'P32_13',0,141,8,18
	.byte	'P32_14',0,142,8,18
	.byte	'P32_15',0,143,8,18
	.byte	'P33_0',0,160,8,18
	.byte	'P33_1',0,161,8,18
	.byte	'P33_2',0,162,8,18
	.byte	'P33_3',0,163,8,18
	.byte	'P33_4',0,164,8,18
	.byte	'P33_5',0,165,8,18
	.byte	'P33_6',0,166,8,18
	.byte	'P33_7',0,167,8,18
	.byte	'P33_8',0,168,8,18
	.byte	'P33_9',0,169,8,18
	.byte	'P33_10',0,170,8,18
	.byte	'P33_11',0,171,8,18
	.byte	'P33_12',0,172,8,18
	.byte	'P33_13',0,173,8,18
	.byte	'P33_14',0,174,8,18
	.byte	'P33_15',0,175,8,0,21
	.byte	'get_port',0,8,114,13
	.word	8253
	.byte	1,1,1,1,5
	.byte	'pin',0,8,114,56
	.word	10794
	.byte	0,17,8,91,9,1,18
	.byte	'GPI',0,0,18
	.byte	'GPO',0,1,0,17,8,103,9,1,18
	.byte	'GPI_FLOATING_IN',0,0,18
	.byte	'GPI_PULL_UP',0,1,18
	.byte	'GPI_PULL_DOWN',0,2,18
	.byte	'GPO_PUSH_PULL',0,3,18
	.byte	'GPO_OPEN_DTAIN',0,4,0,19
	.byte	'gpio_init',0,8,143,1,7,1,1,1,1,5
	.byte	'pin',0,8,143,1,40
	.word	10794
	.byte	5
	.byte	'dir',0,8,143,1,59
	.word	12768
	.byte	5
	.byte	'dat',0,8,143,1,70
	.word	461
	.byte	5
	.byte	'pinconf',0,8,143,1,90
	.word	12786
	.byte	0
.L121:
	.byte	14,6
	.word	461
	.byte	15,5,0
.L147:
	.byte	7
	.byte	'short int',0,2,5,24
	.byte	'__wchar_t',0,9,1,1
	.word	12958
	.byte	24
	.byte	'__size_t',0,9,1,1
	.word	438
	.byte	24
	.byte	'__ptrdiff_t',0,9,1,1
	.word	454
	.byte	25,1,3
	.word	13026
	.byte	24
	.byte	'__codeptr',0,9,1,1
	.word	13028
	.byte	24
	.byte	'__intptr_t',0,9,1,1
	.word	454
	.byte	24
	.byte	'__uintptr_t',0,9,1,1
	.word	438
	.byte	24
	.byte	'_iob_flag_t',0,10,82,25
	.word	478
	.byte	24
	.byte	'boolean',0,11,101,29
	.word	461
	.byte	24
	.byte	'uint8',0,11,105,29
	.word	461
	.byte	24
	.byte	'uint16',0,11,109,29
	.word	478
	.byte	24
	.byte	'uint32',0,11,113,29
	.word	8530
	.byte	24
	.byte	'uint64',0,11,118,29
	.word	320
	.byte	24
	.byte	'sint16',0,11,126,29
	.word	12958
	.byte	7
	.byte	'long int',0,4,5,24
	.byte	'sint32',0,11,131,1,29
	.word	13200
	.byte	7
	.byte	'long long int',0,8,5,24
	.byte	'sint64',0,11,138,1,29
	.word	13228
	.byte	24
	.byte	'float32',0,11,167,1,29
	.word	266
	.byte	24
	.byte	'pvoid',0,12,57,28
	.word	352
	.byte	24
	.byte	'Ifx_TickTime',0,12,79,28
	.word	13228
	.byte	7
	.byte	'char',0,1,6,24
	.byte	'int8',0,13,54,29
	.word	13313
	.byte	24
	.byte	'int16',0,13,55,29
	.word	12958
	.byte	24
	.byte	'int32',0,13,56,29
	.word	454
	.byte	24
	.byte	'int64',0,13,57,29
	.word	13228
	.byte	24
	.byte	'spi_index_enum',0,7,48,2
	.word	8589
	.byte	24
	.byte	'spi_mode_enum',0,7,56,2
	.word	8912
	.byte	24
	.byte	'spi_sck_pin_enum',0,7,67,2
	.word	8966
	.byte	24
	.byte	'spi_mosi_pin_enum',0,7,78,2
	.word	9239
	.byte	24
	.byte	'spi_miso_pin_enum',0,7,89,2
	.word	9493
	.byte	24
	.byte	'spi_cs_pin_enum',0,7,140,1,2
	.word	9747
	.byte	24
	.byte	'Ifx_P_ACCEN0_Bits',0,4,79,3
	.word	7078
	.byte	24
	.byte	'Ifx_P_ACCEN1_Bits',0,4,85,3
	.word	6991
	.byte	24
	.byte	'Ifx_P_ESR_Bits',0,4,107,3
	.word	3334
	.byte	24
	.byte	'Ifx_P_ID_Bits',0,4,115,3
	.word	1387
	.byte	24
	.byte	'Ifx_P_IN_Bits',0,4,137,1,3
	.word	2382
	.byte	24
	.byte	'Ifx_P_IOCR0_Bits',0,4,150,1,3
	.word	1515
	.byte	24
	.byte	'Ifx_P_IOCR12_Bits',0,4,163,1,3
	.word	2162
	.byte	24
	.byte	'Ifx_P_IOCR4_Bits',0,4,176,1,3
	.word	1730
	.byte	24
	.byte	'Ifx_P_IOCR8_Bits',0,4,189,1,3
	.word	1945
	.byte	24
	.byte	'Ifx_P_LPCR0_Bits',0,4,197,1,3
	.word	6350
	.byte	24
	.byte	'Ifx_P_LPCR1_Bits',0,4,205,1,3
	.word	6474
	.byte	24
	.byte	'Ifx_P_LPCR1_P21_Bits',0,4,215,1,3
	.word	6558
	.byte	24
	.byte	'Ifx_P_LPCR2_Bits',0,4,229,1,3
	.word	6738
	.byte	24
	.byte	'Ifx_P_OMCR0_Bits',0,4,240,1,3
	.word	4989
	.byte	24
	.byte	'Ifx_P_OMCR12_Bits',0,4,250,1,3
	.word	5513
	.byte	24
	.byte	'Ifx_P_OMCR4_Bits',0,4,133,2,3
	.word	5163
	.byte	24
	.byte	'Ifx_P_OMCR8_Bits',0,4,144,2,3
	.word	5337
	.byte	24
	.byte	'Ifx_P_OMCR_Bits',0,4,166,2,3
	.word	6002
	.byte	24
	.byte	'Ifx_P_OMR_Bits',0,4,203,2,3
	.word	816
	.byte	24
	.byte	'Ifx_P_OMSR0_Bits',0,4,213,2,3
	.word	4326
	.byte	24
	.byte	'Ifx_P_OMSR12_Bits',0,4,224,2,3
	.word	4814
	.byte	24
	.byte	'Ifx_P_OMSR4_Bits',0,4,235,2,3
	.word	4473
	.byte	24
	.byte	'Ifx_P_OMSR8_Bits',0,4,246,2,3
	.word	4642
	.byte	24
	.byte	'Ifx_P_OMSR_Bits',0,4,140,3,3
	.word	5669
	.byte	24
	.byte	'Ifx_P_OUT_Bits',0,4,162,3,3
	.word	500
	.byte	24
	.byte	'Ifx_P_PCSR_Bits',0,4,180,3,3
	.word	4040
	.byte	24
	.byte	'Ifx_P_PDISC_Bits',0,4,202,3,3
	.word	3674
	.byte	24
	.byte	'Ifx_P_PDR0_Bits',0,4,223,3,3
	.word	2705
	.byte	24
	.byte	'Ifx_P_PDR1_Bits',0,4,244,3,3
	.word	3009
	.byte	24
	.byte	'Ifx_P_ACCEN0',0,4,129,4,3
	.word	7605
	.byte	24
	.byte	'Ifx_P_ACCEN1',0,4,137,4,3
	.word	7038
	.byte	24
	.byte	'Ifx_P_ESR',0,4,145,4,3
	.word	3625
	.byte	24
	.byte	'Ifx_P_ID',0,4,153,4,3
	.word	1466
	.byte	24
	.byte	'Ifx_P_IN',0,4,161,4,3
	.word	2656
	.byte	24
	.byte	'Ifx_P_IOCR0',0,4,169,4,3
	.word	1690
	.byte	24
	.byte	'Ifx_P_IOCR12',0,4,177,4,3
	.word	2342
	.byte	24
	.byte	'Ifx_P_IOCR4',0,4,185,4,3
	.word	1905
	.byte	24
	.byte	'Ifx_P_IOCR8',0,4,193,4,3
	.word	2122
	.byte	24
	.byte	'Ifx_P_LPCR0',0,4,201,4,3
	.word	6434
	.byte	24
	.byte	'Ifx_P_LPCR1',0,4,210,4,3
	.word	6683
	.byte	24
	.byte	'Ifx_P_LPCR2',0,4,218,4,3
	.word	6942
	.byte	24
	.byte	'Ifx_P_OMCR',0,4,226,4,3
	.word	6310
	.byte	24
	.byte	'Ifx_P_OMCR0',0,4,234,4,3
	.word	5123
	.byte	24
	.byte	'Ifx_P_OMCR12',0,4,242,4,3
	.word	5629
	.byte	24
	.byte	'Ifx_P_OMCR4',0,4,250,4,3
	.word	5297
	.byte	24
	.byte	'Ifx_P_OMCR8',0,4,130,5,3
	.word	5473
	.byte	24
	.byte	'Ifx_P_OMR',0,4,138,5,3
	.word	1347
	.byte	24
	.byte	'Ifx_P_OMSR',0,4,146,5,3
	.word	5962
	.byte	24
	.byte	'Ifx_P_OMSR0',0,4,154,5,3
	.word	4433
	.byte	24
	.byte	'Ifx_P_OMSR12',0,4,162,5,3
	.word	4949
	.byte	24
	.byte	'Ifx_P_OMSR4',0,4,170,5,3
	.word	4602
	.byte	24
	.byte	'Ifx_P_OMSR8',0,4,178,5,3
	.word	4774
	.byte	24
	.byte	'Ifx_P_OUT',0,4,186,5,3
	.word	776
	.byte	24
	.byte	'Ifx_P_PCSR',0,4,194,5,3
	.word	4286
	.byte	24
	.byte	'Ifx_P_PDISC',0,4,202,5,3
	.word	4000
	.byte	24
	.byte	'Ifx_P_PDR0',0,4,210,5,3
	.word	2969
	.byte	24
	.byte	'Ifx_P_PDR1',0,4,218,5,3
	.word	3285
	.byte	16
	.word	7645
	.byte	24
	.byte	'Ifx_P',0,4,139,6,3
	.word	14841
	.byte	17,14,250,10,9,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy16384',0,0,18
	.byte	'IfxScu_WDTCON1_IR_divBy256',0,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy64',0,2,0,24
	.byte	'IfxScu_WDTCON1_IR',0,14,255,10,3
	.word	14861
	.byte	10
	.byte	'_Ifx_SCU_ACCEN0_Bits',0,15,45,16,4,11
	.byte	'EN0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	461
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	461
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	461
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	461
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	461
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	461
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	461
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	461
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	461
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	461
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	461
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	461
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	461
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	461
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	461
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	461
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	461
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	461
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	461
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	461
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	461
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_ACCEN0_Bits',0,15,79,3
	.word	14983
	.byte	10
	.byte	'_Ifx_SCU_ACCEN1_Bits',0,15,82,16,4,11
	.byte	'reserved_0',0,4
	.word	438
	.byte	32,0,2,35,0,0,24
	.byte	'Ifx_SCU_ACCEN1_Bits',0,15,85,3
	.word	15540
	.byte	10
	.byte	'_Ifx_SCU_ARSTDIS_Bits',0,15,88,16,4,11
	.byte	'STM0DIS',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'STM1DIS',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'STM2DIS',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	438
	.byte	29,0,2,35,0,0,24
	.byte	'Ifx_SCU_ARSTDIS_Bits',0,15,94,3
	.word	15617
	.byte	10
	.byte	'_Ifx_SCU_CCUCON0_Bits',0,15,97,16,4,11
	.byte	'BAUD1DIV',0,1
	.word	461
	.byte	4,4,2,35,0,11
	.byte	'BAUD2DIV',0,1
	.word	461
	.byte	4,0,2,35,0,11
	.byte	'SRIDIV',0,1
	.word	461
	.byte	4,4,2,35,1,11
	.byte	'LPDIV',0,1
	.word	461
	.byte	4,0,2,35,1,11
	.byte	'SPBDIV',0,1
	.word	461
	.byte	4,4,2,35,2,11
	.byte	'FSI2DIV',0,1
	.word	461
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	461
	.byte	2,0,2,35,2,11
	.byte	'FSIDIV',0,1
	.word	461
	.byte	2,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	461
	.byte	2,4,2,35,3,11
	.byte	'CLKSEL',0,1
	.word	461
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_CCUCON0_Bits',0,15,111,3
	.word	15753
	.byte	10
	.byte	'_Ifx_SCU_CCUCON1_Bits',0,15,114,16,4,11
	.byte	'CANDIV',0,1
	.word	461
	.byte	4,4,2,35,0,11
	.byte	'ERAYDIV',0,1
	.word	461
	.byte	4,0,2,35,0,11
	.byte	'STMDIV',0,1
	.word	461
	.byte	4,4,2,35,1,11
	.byte	'GTMDIV',0,1
	.word	461
	.byte	4,0,2,35,1,11
	.byte	'ETHDIV',0,1
	.word	461
	.byte	4,4,2,35,2,11
	.byte	'ASCLINFDIV',0,1
	.word	461
	.byte	4,0,2,35,2,11
	.byte	'ASCLINSDIV',0,1
	.word	461
	.byte	4,4,2,35,3,11
	.byte	'INSEL',0,1
	.word	461
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_CCUCON1_Bits',0,15,126,3
	.word	16033
	.byte	10
	.byte	'_Ifx_SCU_CCUCON2_Bits',0,15,129,1,16,4,11
	.byte	'BBBDIV',0,1
	.word	461
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	438
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_CCUCON2_Bits',0,15,135,1,3
	.word	16271
	.byte	10
	.byte	'_Ifx_SCU_CCUCON3_Bits',0,15,138,1,16,4,11
	.byte	'PLLDIV',0,1
	.word	461
	.byte	6,2,2,35,0,11
	.byte	'PLLSEL',0,1
	.word	461
	.byte	2,0,2,35,0,11
	.byte	'PLLERAYDIV',0,1
	.word	461
	.byte	6,2,2,35,1,11
	.byte	'PLLERAYSEL',0,1
	.word	461
	.byte	2,0,2,35,1,11
	.byte	'SRIDIV',0,1
	.word	461
	.byte	6,2,2,35,2,11
	.byte	'SRISEL',0,1
	.word	461
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	461
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_CCUCON3_Bits',0,15,150,1,3
	.word	16399
	.byte	10
	.byte	'_Ifx_SCU_CCUCON4_Bits',0,15,153,1,16,4,11
	.byte	'SPBDIV',0,1
	.word	461
	.byte	6,2,2,35,0,11
	.byte	'SPBSEL',0,1
	.word	461
	.byte	2,0,2,35,0,11
	.byte	'GTMDIV',0,1
	.word	461
	.byte	6,2,2,35,1,11
	.byte	'GTMSEL',0,1
	.word	461
	.byte	2,0,2,35,1,11
	.byte	'STMDIV',0,1
	.word	461
	.byte	6,2,2,35,2,11
	.byte	'STMSEL',0,1
	.word	461
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	461
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_CCUCON4_Bits',0,15,165,1,3
	.word	16642
	.byte	10
	.byte	'_Ifx_SCU_CCUCON5_Bits',0,15,168,1,16,4,11
	.byte	'MAXDIV',0,1
	.word	461
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	438
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_CCUCON5_Bits',0,15,174,1,3
	.word	16877
	.byte	10
	.byte	'_Ifx_SCU_CCUCON6_Bits',0,15,177,1,16,4,11
	.byte	'CPU0DIV',0,1
	.word	461
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	438
	.byte	26,0,2,35,0,0,24
	.byte	'Ifx_SCU_CCUCON6_Bits',0,15,181,1,3
	.word	17005
	.byte	10
	.byte	'_Ifx_SCU_CCUCON7_Bits',0,15,184,1,16,4,11
	.byte	'CPU1DIV',0,1
	.word	461
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	438
	.byte	26,0,2,35,0,0,24
	.byte	'Ifx_SCU_CCUCON7_Bits',0,15,188,1,3
	.word	17105
	.byte	10
	.byte	'_Ifx_SCU_CHIPID_Bits',0,15,191,1,16,4,11
	.byte	'CHREV',0,1
	.word	461
	.byte	6,2,2,35,0,11
	.byte	'CHTEC',0,1
	.word	461
	.byte	2,0,2,35,0,11
	.byte	'CHID',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'EEA',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'UCODE',0,1
	.word	461
	.byte	7,0,2,35,2,11
	.byte	'FSIZE',0,1
	.word	461
	.byte	4,4,2,35,3,11
	.byte	'SP',0,1
	.word	461
	.byte	2,2,2,35,3,11
	.byte	'SEC',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	461
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_CHIPID_Bits',0,15,202,1,3
	.word	17205
	.byte	10
	.byte	'_Ifx_SCU_DTSCON_Bits',0,15,205,1,16,4,11
	.byte	'PWD',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'START',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	461
	.byte	2,4,2,35,0,11
	.byte	'CAL',0,4
	.word	438
	.byte	20,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	7,1,2,35,3,11
	.byte	'SLCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_DTSCON_Bits',0,15,213,1,3
	.word	17413
	.byte	10
	.byte	'_Ifx_SCU_DTSLIM_Bits',0,15,216,1,16,4,11
	.byte	'LOWER',0,2
	.word	478
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	461
	.byte	5,1,2,35,1,11
	.byte	'LLU',0,1
	.word	461
	.byte	1,0,2,35,1,11
	.byte	'UPPER',0,2
	.word	478
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	461
	.byte	4,2,2,35,3,11
	.byte	'SLCK',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'UOF',0,1
	.word	461
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_DTSLIM_Bits',0,15,225,1,3
	.word	17578
	.byte	10
	.byte	'_Ifx_SCU_DTSSTAT_Bits',0,15,228,1,16,4,11
	.byte	'RESULT',0,2
	.word	478
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	461
	.byte	4,2,2,35,1,11
	.byte	'RDY',0,1
	.word	461
	.byte	1,1,2,35,1,11
	.byte	'BUSY',0,1
	.word	461
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	478
	.byte	16,0,2,35,2,0,24
	.byte	'Ifx_SCU_DTSSTAT_Bits',0,15,235,1,3
	.word	17761
	.byte	10
	.byte	'_Ifx_SCU_EICR_Bits',0,15,238,1,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	4,4,2,35,0,11
	.byte	'EXIS0',0,1
	.word	461
	.byte	3,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'FEN0',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'REN0',0,1
	.word	461
	.byte	1,6,2,35,1,11
	.byte	'LDEN0',0,1
	.word	461
	.byte	1,5,2,35,1,11
	.byte	'EIEN0',0,1
	.word	461
	.byte	1,4,2,35,1,11
	.byte	'INP0',0,1
	.word	461
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	438
	.byte	5,12,2,35,0,11
	.byte	'EXIS1',0,1
	.word	461
	.byte	3,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	461
	.byte	1,0,2,35,2,11
	.byte	'FEN1',0,1
	.word	461
	.byte	1,7,2,35,3,11
	.byte	'REN1',0,1
	.word	461
	.byte	1,6,2,35,3,11
	.byte	'LDEN1',0,1
	.word	461
	.byte	1,5,2,35,3,11
	.byte	'EIEN1',0,1
	.word	461
	.byte	1,4,2,35,3,11
	.byte	'INP1',0,1
	.word	461
	.byte	3,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	461
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_EICR_Bits',0,15,129,2,3
	.word	17915
	.byte	10
	.byte	'_Ifx_SCU_EIFR_Bits',0,15,132,2,16,4,11
	.byte	'INTF0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'INTF1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'INTF2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'INTF3',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'INTF4',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'INTF5',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'INTF6',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'INTF7',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	438
	.byte	24,0,2,35,0,0,24
	.byte	'Ifx_SCU_EIFR_Bits',0,15,143,2,3
	.word	18279
	.byte	10
	.byte	'_Ifx_SCU_EMSR_Bits',0,15,146,2,16,4,11
	.byte	'POL',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'MODE',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'ENON',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'PSEL',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	478
	.byte	12,0,2,35,0,11
	.byte	'EMSF',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'SEMSF',0,1
	.word	461
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	461
	.byte	6,0,2,35,2,11
	.byte	'EMSFM',0,1
	.word	461
	.byte	2,6,2,35,3,11
	.byte	'SEMSFM',0,1
	.word	461
	.byte	2,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	461
	.byte	4,0,2,35,3,0,24
	.byte	'Ifx_SCU_EMSR_Bits',0,15,159,2,3
	.word	18490
	.byte	10
	.byte	'_Ifx_SCU_ESRCFG_Bits',0,15,162,2,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	7,1,2,35,0,11
	.byte	'EDCON',0,2
	.word	478
	.byte	2,7,2,35,0,11
	.byte	'reserved_9',0,4
	.word	438
	.byte	23,0,2,35,0,0,24
	.byte	'Ifx_SCU_ESRCFG_Bits',0,15,167,2,3
	.word	18742
	.byte	10
	.byte	'_Ifx_SCU_ESROCFG_Bits',0,15,170,2,16,4,11
	.byte	'ARI',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'ARC',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	438
	.byte	30,0,2,35,0,0,24
	.byte	'Ifx_SCU_ESROCFG_Bits',0,15,175,2,3
	.word	18860
	.byte	10
	.byte	'_Ifx_SCU_EVR13CON_Bits',0,15,178,2,16,4,11
	.byte	'reserved_0',0,4
	.word	438
	.byte	28,4,2,35,0,11
	.byte	'EVR13OFF',0,1
	.word	461
	.byte	1,3,2,35,3,11
	.byte	'BPEVR13OFF',0,1
	.word	461
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_EVR13CON_Bits',0,15,185,2,3
	.word	18971
	.byte	10
	.byte	'_Ifx_SCU_EVR33CON_Bits',0,15,188,2,16,4,11
	.byte	'reserved_0',0,4
	.word	438
	.byte	28,4,2,35,0,11
	.byte	'EVR33OFF',0,1
	.word	461
	.byte	1,3,2,35,3,11
	.byte	'BPEVR33OFF',0,1
	.word	461
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_EVR33CON_Bits',0,15,195,2,3
	.word	19134
	.byte	10
	.byte	'_Ifx_SCU_EVRADCSTAT_Bits',0,15,198,2,16,4,11
	.byte	'ADC13V',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'ADC33V',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'ADCSWDV',0,1
	.word	461
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	461
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_EVRADCSTAT_Bits',0,15,205,2,3
	.word	19297
	.byte	10
	.byte	'_Ifx_SCU_EVRDVSTAT_Bits',0,15,208,2,16,4,11
	.byte	'DVS13TRIM',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'DVS33TRIM',0,1
	.word	461
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	461
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_EVRDVSTAT_Bits',0,15,215,2,3
	.word	19455
	.byte	10
	.byte	'_Ifx_SCU_EVRMONCTRL_Bits',0,15,218,2,16,4,11
	.byte	'EVR13OVMOD',0,1
	.word	461
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	461
	.byte	2,4,2,35,0,11
	.byte	'EVR13UVMOD',0,1
	.word	461
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	461
	.byte	2,0,2,35,0,11
	.byte	'EVR33OVMOD',0,1
	.word	461
	.byte	2,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	461
	.byte	2,4,2,35,1,11
	.byte	'EVR33UVMOD',0,1
	.word	461
	.byte	2,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	461
	.byte	2,0,2,35,1,11
	.byte	'SWDOVMOD',0,1
	.word	461
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	461
	.byte	2,4,2,35,2,11
	.byte	'SWDUVMOD',0,1
	.word	461
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,2
	.word	478
	.byte	10,0,2,35,2,0,24
	.byte	'Ifx_SCU_EVRMONCTRL_Bits',0,15,232,2,3
	.word	19620
	.byte	10
	.byte	'_Ifx_SCU_EVROSCCTRL_Bits',0,15,235,2,16,4,11
	.byte	'OSCTRIM',0,2
	.word	478
	.byte	10,6,2,35,0,11
	.byte	'OSCPTAT',0,1
	.word	461
	.byte	6,0,2,35,1,11
	.byte	'OSCANASEL',0,1
	.word	461
	.byte	4,4,2,35,2,11
	.byte	'HPBGTRIM',0,2
	.word	478
	.byte	7,5,2,35,2,11
	.byte	'HPBGCLKEN',0,1
	.word	461
	.byte	1,4,2,35,3,11
	.byte	'OSC3V3',0,1
	.word	461
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	461
	.byte	2,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_EVROSCCTRL_Bits',0,15,245,2,3
	.word	19949
	.byte	10
	.byte	'_Ifx_SCU_EVROVMON_Bits',0,15,248,2,16,4,11
	.byte	'EVR13OVVAL',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'EVR33OVVAL',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'SWDOVVAL',0,1
	.word	461
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_EVROVMON_Bits',0,15,255,2,3
	.word	20170
	.byte	10
	.byte	'_Ifx_SCU_EVRRSTCON_Bits',0,15,130,3,16,4,11
	.byte	'RST13TRIM',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	438
	.byte	16,8,2,35,0,11
	.byte	'RST13OFF',0,1
	.word	461
	.byte	1,7,2,35,3,11
	.byte	'BPRST13OFF',0,1
	.word	461
	.byte	1,6,2,35,3,11
	.byte	'RST33OFF',0,1
	.word	461
	.byte	1,5,2,35,3,11
	.byte	'BPRST33OFF',0,1
	.word	461
	.byte	1,4,2,35,3,11
	.byte	'RSTSWDOFF',0,1
	.word	461
	.byte	1,3,2,35,3,11
	.byte	'BPRSTSWDOFF',0,1
	.word	461
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_EVRRSTCON_Bits',0,15,142,3,3
	.word	20333
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF1_Bits',0,15,145,3,16,4,11
	.byte	'SD5P',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'SD5I',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'SD5D',0,1
	.word	461
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_EVRSDCOEFF1_Bits',0,15,152,3,3
	.word	20605
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF2_Bits',0,15,155,3,16,4,11
	.byte	'SD33P',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'SD33I',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'SD33D',0,1
	.word	461
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_EVRSDCOEFF2_Bits',0,15,162,3,3
	.word	20758
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF3_Bits',0,15,165,3,16,4,11
	.byte	'CT5REG0',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'CT5REG1',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'CT5REG2',0,1
	.word	461
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_EVRSDCOEFF3_Bits',0,15,172,3,3
	.word	20914
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF4_Bits',0,15,175,3,16,4,11
	.byte	'CT5REG3',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'CT5REG4',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	478
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_EVRSDCOEFF4_Bits',0,15,181,3,3
	.word	21076
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF5_Bits',0,15,184,3,16,4,11
	.byte	'CT33REG0',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'CT33REG1',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'CT33REG2',0,1
	.word	461
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_EVRSDCOEFF5_Bits',0,15,191,3,3
	.word	21219
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF6_Bits',0,15,194,3,16,4,11
	.byte	'CT33REG3',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'CT33REG4',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	478
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_EVRSDCOEFF6_Bits',0,15,200,3,3
	.word	21384
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL1_Bits',0,15,203,3,16,4,11
	.byte	'SDFREQSPRD',0,2
	.word	478
	.byte	16,0,2,35,0,11
	.byte	'SDFREQ',0,1
	.word	461
	.byte	8,0,2,35,2,11
	.byte	'SDSTEP',0,1
	.word	461
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	461
	.byte	2,2,2,35,3,11
	.byte	'SDSAMPLE',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_EVRSDCTRL1_Bits',0,15,211,3,3
	.word	21529
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL2_Bits',0,15,214,3,16,4,11
	.byte	'DRVP',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'SDMINMAXDC',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'DRVN',0,1
	.word	461
	.byte	8,0,2,35,2,11
	.byte	'SDLUT',0,1
	.word	461
	.byte	6,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_EVRSDCTRL2_Bits',0,15,222,3,3
	.word	21710
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL3_Bits',0,15,225,3,16,4,11
	.byte	'SDPWMPRE',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'SDPID',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'SDVOKLVL',0,1
	.word	461
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_EVRSDCTRL3_Bits',0,15,232,3,3
	.word	21884
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL4_Bits',0,15,235,3,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'SYNCDIV',0,1
	.word	461
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	438
	.byte	20,1,2,35,0,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_EVRSDCTRL4_Bits',0,15,241,3,3
	.word	22044
	.byte	10
	.byte	'_Ifx_SCU_EVRSTAT_Bits',0,15,244,3,16,4,11
	.byte	'EVR13',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'OV13',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'EVR33',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'OV33',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'OVSWD',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'UV13',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'UV33',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'UVSWD',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'EXTPASS13',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'EXTPASS33',0,1
	.word	461
	.byte	1,6,2,35,1,11
	.byte	'BGPROK',0,1
	.word	461
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	438
	.byte	21,0,2,35,0,0,24
	.byte	'Ifx_SCU_EVRSTAT_Bits',0,15,130,4,3
	.word	22188
	.byte	10
	.byte	'_Ifx_SCU_EVRTRIM_Bits',0,15,133,4,16,4,11
	.byte	'EVR13TRIM',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'SDVOUTSEL',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	478
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_EVRTRIM_Bits',0,15,139,4,3
	.word	22462
	.byte	10
	.byte	'_Ifx_SCU_EVRUVMON_Bits',0,15,142,4,16,4,11
	.byte	'EVR13UVVAL',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'EVR33UVVAL',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'SWDUVVAL',0,1
	.word	461
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_EVRUVMON_Bits',0,15,149,4,3
	.word	22601
	.byte	10
	.byte	'_Ifx_SCU_EXTCON_Bits',0,15,152,4,16,4,11
	.byte	'EN0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'SEL0',0,1
	.word	461
	.byte	4,2,2,35,0,11
	.byte	'reserved_6',0,2
	.word	478
	.byte	10,0,2,35,0,11
	.byte	'EN1',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'NSEL',0,1
	.word	461
	.byte	1,6,2,35,2,11
	.byte	'SEL1',0,1
	.word	461
	.byte	4,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	461
	.byte	2,0,2,35,2,11
	.byte	'DIV1',0,1
	.word	461
	.byte	8,0,2,35,3,0,24
	.byte	'Ifx_SCU_EXTCON_Bits',0,15,163,4,3
	.word	22764
	.byte	10
	.byte	'_Ifx_SCU_FDR_Bits',0,15,166,4,16,4,11
	.byte	'STEP',0,2
	.word	478
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	461
	.byte	4,2,2,35,1,11
	.byte	'DM',0,1
	.word	461
	.byte	2,0,2,35,1,11
	.byte	'RESULT',0,2
	.word	478
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	461
	.byte	5,1,2,35,3,11
	.byte	'DISCLK',0,1
	.word	461
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_FDR_Bits',0,15,174,4,3
	.word	22982
	.byte	10
	.byte	'_Ifx_SCU_FMR_Bits',0,15,177,4,16,4,11
	.byte	'FS0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'FS1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'FS2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'FS3',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'FS4',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'FS5',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'FS6',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'FS7',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'FC0',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'FC1',0,1
	.word	461
	.byte	1,6,2,35,2,11
	.byte	'FC2',0,1
	.word	461
	.byte	1,5,2,35,2,11
	.byte	'FC3',0,1
	.word	461
	.byte	1,4,2,35,2,11
	.byte	'FC4',0,1
	.word	461
	.byte	1,3,2,35,2,11
	.byte	'FC5',0,1
	.word	461
	.byte	1,2,2,35,2,11
	.byte	'FC6',0,1
	.word	461
	.byte	1,1,2,35,2,11
	.byte	'FC7',0,1
	.word	461
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	8,0,2,35,3,0,24
	.byte	'Ifx_SCU_FMR_Bits',0,15,197,4,3
	.word	23145
	.byte	10
	.byte	'_Ifx_SCU_ID_Bits',0,15,200,4,16,4,11
	.byte	'MODREV',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	478
	.byte	16,0,2,35,2,0,24
	.byte	'Ifx_SCU_ID_Bits',0,15,205,4,3
	.word	23481
	.byte	10
	.byte	'_Ifx_SCU_IGCR_Bits',0,15,208,4,16,4,11
	.byte	'IPEN00',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'IPEN01',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'IPEN02',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'IPEN03',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'IPEN04',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'IPEN05',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'IPEN06',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'IPEN07',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	461
	.byte	5,3,2,35,1,11
	.byte	'GEEN0',0,1
	.word	461
	.byte	1,2,2,35,1,11
	.byte	'IGP0',0,1
	.word	461
	.byte	2,0,2,35,1,11
	.byte	'IPEN10',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'IPEN11',0,1
	.word	461
	.byte	1,6,2,35,2,11
	.byte	'IPEN12',0,1
	.word	461
	.byte	1,5,2,35,2,11
	.byte	'IPEN13',0,1
	.word	461
	.byte	1,4,2,35,2,11
	.byte	'IPEN14',0,1
	.word	461
	.byte	1,3,2,35,2,11
	.byte	'IPEN15',0,1
	.word	461
	.byte	1,2,2,35,2,11
	.byte	'IPEN16',0,1
	.word	461
	.byte	1,1,2,35,2,11
	.byte	'IPEN17',0,1
	.word	461
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	5,3,2,35,3,11
	.byte	'GEEN1',0,1
	.word	461
	.byte	1,2,2,35,3,11
	.byte	'IGP1',0,1
	.word	461
	.byte	2,0,2,35,3,0,24
	.byte	'Ifx_SCU_IGCR_Bits',0,15,232,4,3
	.word	23588
	.byte	10
	.byte	'_Ifx_SCU_IN_Bits',0,15,235,4,16,4,11
	.byte	'P0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	438
	.byte	30,0,2,35,0,0,24
	.byte	'Ifx_SCU_IN_Bits',0,15,240,4,3
	.word	24040
	.byte	10
	.byte	'_Ifx_SCU_IOCR_Bits',0,15,243,4,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	4,4,2,35,0,11
	.byte	'PC0',0,1
	.word	461
	.byte	4,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	461
	.byte	4,4,2,35,1,11
	.byte	'PC1',0,1
	.word	461
	.byte	4,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	478
	.byte	16,0,2,35,2,0,24
	.byte	'Ifx_SCU_IOCR_Bits',0,15,250,4,3
	.word	24139
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL0_Bits',0,15,253,4,16,4,11
	.byte	'LBISTREQ',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'LBISTREQP',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'PATTERNS',0,2
	.word	478
	.byte	14,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	478
	.byte	16,0,2,35,2,0,24
	.byte	'Ifx_SCU_LBISTCTRL0_Bits',0,15,131,5,3
	.word	24289
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL1_Bits',0,15,134,5,16,4,11
	.byte	'SEED',0,4
	.word	438
	.byte	23,9,2,35,0,11
	.byte	'reserved_23',0,1
	.word	461
	.byte	1,0,2,35,2,11
	.byte	'SPLITSH',0,1
	.word	461
	.byte	3,5,2,35,3,11
	.byte	'BODY',0,1
	.word	461
	.byte	1,4,2,35,3,11
	.byte	'LBISTFREQU',0,1
	.word	461
	.byte	4,0,2,35,3,0,24
	.byte	'Ifx_SCU_LBISTCTRL1_Bits',0,15,141,5,3
	.word	24438
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL2_Bits',0,15,144,5,16,4,11
	.byte	'SIGNATURE',0,4
	.word	438
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	7,1,2,35,3,11
	.byte	'LBISTDONE',0,1
	.word	461
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_LBISTCTRL2_Bits',0,15,149,5,3
	.word	24599
	.byte	10
	.byte	'_Ifx_SCU_LCLCON_Bits',0,15,152,5,16,4,11
	.byte	'reserved_0',0,2
	.word	478
	.byte	16,0,2,35,0,11
	.byte	'LS',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,2
	.word	478
	.byte	14,1,2,35,2,11
	.byte	'LSEN',0,1
	.word	461
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_LCLCON_Bits',0,15,158,5,3
	.word	24729
	.byte	10
	.byte	'_Ifx_SCU_LCLTEST_Bits',0,15,161,5,16,4,11
	.byte	'LCLT0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'LCLT1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	438
	.byte	30,0,2,35,0,0,24
	.byte	'Ifx_SCU_LCLTEST_Bits',0,15,166,5,3
	.word	24861
	.byte	10
	.byte	'_Ifx_SCU_MANID_Bits',0,15,169,5,16,4,11
	.byte	'DEPT',0,1
	.word	461
	.byte	5,3,2,35,0,11
	.byte	'MANUF',0,2
	.word	478
	.byte	11,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	478
	.byte	16,0,2,35,2,0,24
	.byte	'Ifx_SCU_MANID_Bits',0,15,174,5,3
	.word	24976
	.byte	10
	.byte	'_Ifx_SCU_OMR_Bits',0,15,177,5,16,4,11
	.byte	'PS0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	478
	.byte	14,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	461
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	478
	.byte	14,0,2,35,2,0,24
	.byte	'Ifx_SCU_OMR_Bits',0,15,185,5,3
	.word	25087
	.byte	10
	.byte	'_Ifx_SCU_OSCCON_Bits',0,15,188,5,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'PLLLV',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'OSCRES',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'GAINSEL',0,1
	.word	461
	.byte	2,3,2,35,0,11
	.byte	'MODE',0,1
	.word	461
	.byte	2,1,2,35,0,11
	.byte	'SHBY',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'PLLHV',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	461
	.byte	1,6,2,35,1,11
	.byte	'X1D',0,1
	.word	461
	.byte	1,5,2,35,1,11
	.byte	'X1DEN',0,1
	.word	461
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	461
	.byte	4,0,2,35,1,11
	.byte	'OSCVAL',0,1
	.word	461
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	461
	.byte	2,1,2,35,2,11
	.byte	'APREN',0,1
	.word	461
	.byte	1,0,2,35,2,11
	.byte	'CAP0EN',0,1
	.word	461
	.byte	1,7,2,35,3,11
	.byte	'CAP1EN',0,1
	.word	461
	.byte	1,6,2,35,3,11
	.byte	'CAP2EN',0,1
	.word	461
	.byte	1,5,2,35,3,11
	.byte	'CAP3EN',0,1
	.word	461
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	461
	.byte	4,0,2,35,3,0,24
	.byte	'Ifx_SCU_OSCCON_Bits',0,15,209,5,3
	.word	25245
	.byte	10
	.byte	'_Ifx_SCU_OUT_Bits',0,15,212,5,16,4,11
	.byte	'P0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	438
	.byte	30,0,2,35,0,0,24
	.byte	'Ifx_SCU_OUT_Bits',0,15,217,5,3
	.word	25657
	.byte	10
	.byte	'_Ifx_SCU_OVCCON_Bits',0,15,220,5,16,4,11
	.byte	'CSEL0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'CSEL1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'CSEL2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,2
	.word	478
	.byte	13,0,2,35,0,11
	.byte	'OVSTRT',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'OVSTP',0,1
	.word	461
	.byte	1,6,2,35,2,11
	.byte	'DCINVAL',0,1
	.word	461
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	461
	.byte	5,0,2,35,2,11
	.byte	'OVCONF',0,1
	.word	461
	.byte	1,7,2,35,3,11
	.byte	'POVCONF',0,1
	.word	461
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	461
	.byte	6,0,2,35,3,0,24
	.byte	'Ifx_SCU_OVCCON_Bits',0,15,233,5,3
	.word	25758
	.byte	10
	.byte	'_Ifx_SCU_OVCENABLE_Bits',0,15,236,5,16,4,11
	.byte	'OVEN0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'OVEN1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'OVEN2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	438
	.byte	29,0,2,35,0,0,24
	.byte	'Ifx_SCU_OVCENABLE_Bits',0,15,242,5,3
	.word	26025
	.byte	10
	.byte	'_Ifx_SCU_PDISC_Bits',0,15,245,5,16,4,11
	.byte	'PDIS0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	438
	.byte	30,0,2,35,0,0,24
	.byte	'Ifx_SCU_PDISC_Bits',0,15,250,5,3
	.word	26161
	.byte	10
	.byte	'_Ifx_SCU_PDR_Bits',0,15,253,5,16,4,11
	.byte	'PD0',0,1
	.word	461
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	461
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	438
	.byte	24,0,2,35,0,0,24
	.byte	'Ifx_SCU_PDR_Bits',0,15,132,6,3
	.word	26272
	.byte	10
	.byte	'_Ifx_SCU_PDRR_Bits',0,15,135,6,16,4,11
	.byte	'PDR0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'PDR1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'PDR2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'PDR3',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'PDR4',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'PDR5',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'PDR6',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'PDR7',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	438
	.byte	24,0,2,35,0,0,24
	.byte	'Ifx_SCU_PDRR_Bits',0,15,146,6,3
	.word	26405
	.byte	10
	.byte	'_Ifx_SCU_PLLCON0_Bits',0,15,149,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'MODEN',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	478
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	461
	.byte	7,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	461
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	461
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	461
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	461
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	461
	.byte	4,0,2,35,3,0,24
	.byte	'Ifx_SCU_PLLCON0_Bits',0,15,166,6,3
	.word	26608
	.byte	10
	.byte	'_Ifx_SCU_PLLCON1_Bits',0,15,169,6,16,4,11
	.byte	'K2DIV',0,1
	.word	461
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	461
	.byte	7,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	461
	.byte	1,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	461
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	478
	.byte	9,0,2,35,2,0,24
	.byte	'Ifx_SCU_PLLCON1_Bits',0,15,177,6,3
	.word	26964
	.byte	10
	.byte	'_Ifx_SCU_PLLCON2_Bits',0,15,180,6,16,4,11
	.byte	'MODCFG',0,2
	.word	478
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	478
	.byte	16,0,2,35,2,0,24
	.byte	'Ifx_SCU_PLLCON2_Bits',0,15,184,6,3
	.word	27142
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON0_Bits',0,15,187,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	461
	.byte	2,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	478
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	461
	.byte	5,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	461
	.byte	2,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	461
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	461
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	461
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	461
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	461
	.byte	4,0,2,35,3,0,24
	.byte	'Ifx_SCU_PLLERAYCON0_Bits',0,15,204,6,3
	.word	27242
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON1_Bits',0,15,207,6,16,4,11
	.byte	'K2DIV',0,1
	.word	461
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	461
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	461
	.byte	4,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	461
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	478
	.byte	9,0,2,35,2,0,24
	.byte	'Ifx_SCU_PLLERAYCON1_Bits',0,15,215,6,3
	.word	27612
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYSTAT_Bits',0,15,218,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'PWDSTAT',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	438
	.byte	26,0,2,35,0,0,24
	.byte	'Ifx_SCU_PLLERAYSTAT_Bits',0,15,227,6,3
	.word	27798
	.byte	10
	.byte	'_Ifx_SCU_PLLSTAT_Bits',0,15,230,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'MODRUN',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	438
	.byte	24,0,2,35,0,0,24
	.byte	'Ifx_SCU_PLLSTAT_Bits',0,15,241,6,3
	.word	27996
	.byte	10
	.byte	'_Ifx_SCU_PMCSR_Bits',0,15,244,6,16,4,11
	.byte	'REQSLP',0,1
	.word	461
	.byte	2,6,2,35,0,11
	.byte	'SMUSLP',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	461
	.byte	5,0,2,35,0,11
	.byte	'PMST',0,1
	.word	461
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	438
	.byte	21,0,2,35,0,0,24
	.byte	'Ifx_SCU_PMCSR_Bits',0,15,251,6,3
	.word	28229
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR0_Bits',0,15,254,6,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'ESR1WKEN',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'PINAWKEN',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'PINBWKEN',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'ESR0DFEN',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'ESR0EDCON',0,1
	.word	461
	.byte	2,1,2,35,0,11
	.byte	'ESR1DFEN',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'ESR1EDCON',0,1
	.word	461
	.byte	2,6,2,35,1,11
	.byte	'PINADFEN',0,1
	.word	461
	.byte	1,5,2,35,1,11
	.byte	'PINAEDCON',0,1
	.word	461
	.byte	2,3,2,35,1,11
	.byte	'PINBDFEN',0,1
	.word	461
	.byte	1,2,2,35,1,11
	.byte	'PINBEDCON',0,1
	.word	461
	.byte	2,0,2,35,1,11
	.byte	'SCREN',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'STBYRAMSEL',0,1
	.word	461
	.byte	2,5,2,35,2,11
	.byte	'SCRCLKSEL',0,1
	.word	461
	.byte	1,4,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	461
	.byte	1,3,2,35,2,11
	.byte	'TRISTEN',0,1
	.word	461
	.byte	1,2,2,35,2,11
	.byte	'TRISTREQ',0,1
	.word	461
	.byte	1,1,2,35,2,11
	.byte	'PORSTDF',0,1
	.word	461
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	1,7,2,35,3,11
	.byte	'DCDCSYNC',0,1
	.word	461
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	461
	.byte	3,3,2,35,3,11
	.byte	'ESR0TRIST',0,1
	.word	461
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_PMSWCR0_Bits',0,15,153,7,3
	.word	28381
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR1_Bits',0,15,156,7,16,4,11
	.byte	'SCRSTEN',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'SCRSTREQ',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	461
	.byte	6,0,2,35,0,11
	.byte	'CPUIDLSEL',0,1
	.word	461
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	461
	.byte	1,4,2,35,1,11
	.byte	'IRADIS',0,1
	.word	461
	.byte	1,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	461
	.byte	3,0,2,35,1,11
	.byte	'SCRCFG',0,1
	.word	461
	.byte	8,0,2,35,2,11
	.byte	'CPUSEL',0,1
	.word	461
	.byte	3,5,2,35,3,11
	.byte	'STBYEVEN',0,1
	.word	461
	.byte	1,4,2,35,3,11
	.byte	'STBYEV',0,1
	.word	461
	.byte	3,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_PMSWCR1_Bits',0,15,170,7,3
	.word	28948
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR2_Bits',0,15,173,7,16,4,11
	.byte	'SCRINT',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'BUSY',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'SCRECC',0,1
	.word	461
	.byte	1,6,2,35,1,11
	.byte	'SCRWDT',0,1
	.word	461
	.byte	1,5,2,35,1,11
	.byte	'SCRRST',0,1
	.word	461
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	461
	.byte	4,0,2,35,1,11
	.byte	'TCINT',0,1
	.word	461
	.byte	8,0,2,35,2,11
	.byte	'TCINTREQ',0,1
	.word	461
	.byte	1,7,2,35,3,11
	.byte	'SMURST',0,1
	.word	461
	.byte	1,6,2,35,3,11
	.byte	'RST',0,1
	.word	461
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	461
	.byte	4,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_PMSWCR2_Bits',0,15,187,7,3
	.word	29242
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTAT_Bits',0,15,190,7,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKP',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUN',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'PINAWKP',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUN',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'PINBWKP',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUN',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'PORSTDF',0,1
	.word	461
	.byte	1,6,2,35,1,11
	.byte	'HWCFGEVR',0,1
	.word	461
	.byte	3,3,2,35,1,11
	.byte	'STBYRAM',0,1
	.word	461
	.byte	2,1,2,35,1,11
	.byte	'TRIST',0,1
	.word	461
	.byte	1,0,2,35,1,11
	.byte	'SCRST',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'SCRWKP',0,1
	.word	461
	.byte	1,6,2,35,2,11
	.byte	'SCR',0,1
	.word	461
	.byte	1,5,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	461
	.byte	1,4,2,35,2,11
	.byte	'ESR1WKEN',0,1
	.word	461
	.byte	1,3,2,35,2,11
	.byte	'PINAWKEN',0,1
	.word	461
	.byte	1,2,2,35,2,11
	.byte	'PINBWKEN',0,1
	.word	461
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	478
	.byte	4,5,2,35,2,11
	.byte	'ESR0TRIST',0,1
	.word	461
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	461
	.byte	4,0,2,35,3,0,24
	.byte	'Ifx_SCU_PMSWSTAT_Bits',0,15,214,7,3
	.word	29520
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTATCLR_Bits',0,15,217,7,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKPCLR',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUNCLR',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'PINAWKPCLR',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUNCLR',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'PINBWKPCLR',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUNCLR',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'SCRSTCLR',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'SCRWKPCLR',0,1
	.word	461
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	478
	.byte	14,0,2,35,2,0,24
	.byte	'Ifx_SCU_PMSWSTATCLR_Bits',0,15,230,7,3
	.word	30016
	.byte	10
	.byte	'_Ifx_SCU_RSTCON2_Bits',0,15,233,7,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'CLRC',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	478
	.byte	10,4,2,35,0,11
	.byte	'CSS0',0,1
	.word	461
	.byte	1,3,2,35,1,11
	.byte	'CSS1',0,1
	.word	461
	.byte	1,2,2,35,1,11
	.byte	'CSS2',0,1
	.word	461
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	461
	.byte	1,0,2,35,1,11
	.byte	'USRINFO',0,2
	.word	478
	.byte	16,0,2,35,2,0,24
	.byte	'Ifx_SCU_RSTCON2_Bits',0,15,243,7,3
	.word	30329
	.byte	10
	.byte	'_Ifx_SCU_RSTCON_Bits',0,15,246,7,16,4,11
	.byte	'ESR0',0,1
	.word	461
	.byte	2,6,2,35,0,11
	.byte	'ESR1',0,1
	.word	461
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	461
	.byte	2,2,2,35,0,11
	.byte	'SMU',0,1
	.word	461
	.byte	2,0,2,35,0,11
	.byte	'SW',0,1
	.word	461
	.byte	2,6,2,35,1,11
	.byte	'STM0',0,1
	.word	461
	.byte	2,4,2,35,1,11
	.byte	'STM1',0,1
	.word	461
	.byte	2,2,2,35,1,11
	.byte	'STM2',0,1
	.word	461
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	478
	.byte	16,0,2,35,2,0,24
	.byte	'Ifx_SCU_RSTCON_Bits',0,15,129,8,3
	.word	30538
	.byte	10
	.byte	'_Ifx_SCU_RSTSTAT_Bits',0,15,132,8,16,4,11
	.byte	'ESR0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'ESR1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'SMU',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'SW',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'STM0',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'STM1',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'STM2',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'PORST',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	461
	.byte	1,6,2,35,2,11
	.byte	'CB0',0,1
	.word	461
	.byte	1,5,2,35,2,11
	.byte	'CB1',0,1
	.word	461
	.byte	1,4,2,35,2,11
	.byte	'CB3',0,1
	.word	461
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	461
	.byte	2,1,2,35,2,11
	.byte	'EVR13',0,1
	.word	461
	.byte	1,0,2,35,2,11
	.byte	'EVR33',0,1
	.word	461
	.byte	1,7,2,35,3,11
	.byte	'SWD',0,1
	.word	461
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	461
	.byte	2,4,2,35,3,11
	.byte	'STBYR',0,1
	.word	461
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	461
	.byte	3,0,2,35,3,0,24
	.byte	'Ifx_SCU_RSTSTAT_Bits',0,15,155,8,3
	.word	30749
	.byte	10
	.byte	'_Ifx_SCU_SAFECON_Bits',0,15,158,8,16,4,11
	.byte	'HBT',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	438
	.byte	31,0,2,35,0,0,24
	.byte	'Ifx_SCU_SAFECON_Bits',0,15,162,8,3
	.word	31181
	.byte	10
	.byte	'_Ifx_SCU_STSTAT_Bits',0,15,165,8,16,4,11
	.byte	'HWCFG',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'FTM',0,1
	.word	461
	.byte	7,1,2,35,1,11
	.byte	'MODE',0,1
	.word	461
	.byte	1,0,2,35,1,11
	.byte	'FCBAE',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'LUDIS',0,1
	.word	461
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	461
	.byte	1,5,2,35,2,11
	.byte	'TRSTL',0,1
	.word	461
	.byte	1,4,2,35,2,11
	.byte	'SPDEN',0,1
	.word	461
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	461
	.byte	3,0,2,35,2,11
	.byte	'RAMINT',0,1
	.word	461
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	461
	.byte	7,0,2,35,3,0,24
	.byte	'Ifx_SCU_STSTAT_Bits',0,15,178,8,3
	.word	31277
	.byte	10
	.byte	'_Ifx_SCU_SWRSTCON_Bits',0,15,181,8,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'SWRSTREQ',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	438
	.byte	30,0,2,35,0,0,24
	.byte	'Ifx_SCU_SWRSTCON_Bits',0,15,186,8,3
	.word	31537
	.byte	10
	.byte	'_Ifx_SCU_SYSCON_Bits',0,15,189,8,16,4,11
	.byte	'CCTRIG0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'RAMINTM',0,1
	.word	461
	.byte	2,4,2,35,0,11
	.byte	'SETLUDIS',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	461
	.byte	3,0,2,35,0,11
	.byte	'DATM',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,4
	.word	438
	.byte	23,0,2,35,0,0,24
	.byte	'Ifx_SCU_SYSCON_Bits',0,15,198,8,3
	.word	31662
	.byte	10
	.byte	'_Ifx_SCU_TRAPCLR_Bits',0,15,201,8,16,4,11
	.byte	'ESR0T',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	438
	.byte	28,0,2,35,0,0,24
	.byte	'Ifx_SCU_TRAPCLR_Bits',0,15,208,8,3
	.word	31859
	.byte	10
	.byte	'_Ifx_SCU_TRAPDIS_Bits',0,15,211,8,16,4,11
	.byte	'ESR0T',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	438
	.byte	28,0,2,35,0,0,24
	.byte	'Ifx_SCU_TRAPDIS_Bits',0,15,218,8,3
	.word	32012
	.byte	10
	.byte	'_Ifx_SCU_TRAPSET_Bits',0,15,221,8,16,4,11
	.byte	'ESR0T',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	438
	.byte	28,0,2,35,0,0,24
	.byte	'Ifx_SCU_TRAPSET_Bits',0,15,228,8,3
	.word	32165
	.byte	10
	.byte	'_Ifx_SCU_TRAPSTAT_Bits',0,15,231,8,16,4,11
	.byte	'ESR0T',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	438
	.byte	28,0,2,35,0,0,24
	.byte	'Ifx_SCU_TRAPSTAT_Bits',0,15,238,8,3
	.word	32318
	.byte	7
	.byte	'unsigned int',0,4,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON0_Bits',0,15,241,8,16,4,11
	.byte	'ENDINIT',0,4
	.word	32473
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	32473
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	32473
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	32473
	.byte	16,0,2,35,0,0,24
	.byte	'Ifx_SCU_WDTCPU_CON0_Bits',0,15,247,8,3
	.word	32489
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU_CON1_Bits',0,15,250,8,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	2,6,2,35,0,11
	.byte	'IR0',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	461
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	478
	.byte	16,0,2,35,2,0,24
	.byte	'Ifx_SCU_WDTCPU_CON1_Bits',0,15,134,9,3
	.word	32619
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU_SR_Bits',0,15,137,9,16,4,11
	.byte	'AE',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	461
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	478
	.byte	16,0,2,35,2,0,24
	.byte	'Ifx_SCU_WDTCPU_SR_Bits',0,15,150,9,3
	.word	32857
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON0_Bits',0,15,153,9,16,4,11
	.byte	'ENDINIT',0,4
	.word	32473
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	32473
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	32473
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	32473
	.byte	16,0,2,35,0,0,24
	.byte	'Ifx_SCU_WDTS_CON0_Bits',0,15,159,9,3
	.word	33080
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON1_Bits',0,15,162,9,16,4,11
	.byte	'CLRIRF',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'IR0',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	461
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	478
	.byte	16,0,2,35,2,0,24
	.byte	'Ifx_SCU_WDTS_CON1_Bits',0,15,175,9,3
	.word	33206
	.byte	10
	.byte	'_Ifx_SCU_WDTS_SR_Bits',0,15,178,9,16,4,11
	.byte	'AE',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	461
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	478
	.byte	16,0,2,35,2,0,24
	.byte	'Ifx_SCU_WDTS_SR_Bits',0,15,191,9,3
	.word	33458
	.byte	12,15,199,9,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14983
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_ACCEN0',0,15,204,9,3
	.word	33677
	.byte	12,15,207,9,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15540
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_ACCEN1',0,15,212,9,3
	.word	33741
	.byte	12,15,215,9,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15617
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_ARSTDIS',0,15,220,9,3
	.word	33805
	.byte	12,15,223,9,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15753
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_CCUCON0',0,15,228,9,3
	.word	33870
	.byte	12,15,231,9,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16033
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_CCUCON1',0,15,236,9,3
	.word	33935
	.byte	12,15,239,9,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16271
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_CCUCON2',0,15,244,9,3
	.word	34000
	.byte	12,15,247,9,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16399
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_CCUCON3',0,15,252,9,3
	.word	34065
	.byte	12,15,255,9,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16642
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_CCUCON4',0,15,132,10,3
	.word	34130
	.byte	12,15,135,10,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16877
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_CCUCON5',0,15,140,10,3
	.word	34195
	.byte	12,15,143,10,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17005
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_CCUCON6',0,15,148,10,3
	.word	34260
	.byte	12,15,151,10,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17105
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_CCUCON7',0,15,156,10,3
	.word	34325
	.byte	12,15,159,10,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17205
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_CHIPID',0,15,164,10,3
	.word	34390
	.byte	12,15,167,10,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17413
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_DTSCON',0,15,172,10,3
	.word	34454
	.byte	12,15,175,10,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17578
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_DTSLIM',0,15,180,10,3
	.word	34518
	.byte	12,15,183,10,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17761
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_DTSSTAT',0,15,188,10,3
	.word	34582
	.byte	12,15,191,10,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17915
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_EICR',0,15,196,10,3
	.word	34647
	.byte	12,15,199,10,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18279
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_EIFR',0,15,204,10,3
	.word	34709
	.byte	12,15,207,10,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18490
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_EMSR',0,15,212,10,3
	.word	34771
	.byte	12,15,215,10,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18742
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_ESRCFG',0,15,220,10,3
	.word	34833
	.byte	12,15,223,10,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18860
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_ESROCFG',0,15,228,10,3
	.word	34897
	.byte	12,15,231,10,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18971
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_EVR13CON',0,15,236,10,3
	.word	34962
	.byte	12,15,239,10,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19134
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_EVR33CON',0,15,244,10,3
	.word	35028
	.byte	12,15,247,10,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19297
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_EVRADCSTAT',0,15,252,10,3
	.word	35094
	.byte	12,15,255,10,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19455
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_EVRDVSTAT',0,15,132,11,3
	.word	35162
	.byte	12,15,135,11,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19620
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_EVRMONCTRL',0,15,140,11,3
	.word	35229
	.byte	12,15,143,11,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19949
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_EVROSCCTRL',0,15,148,11,3
	.word	35297
	.byte	12,15,151,11,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20170
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_EVROVMON',0,15,156,11,3
	.word	35365
	.byte	12,15,159,11,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20333
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_EVRRSTCON',0,15,164,11,3
	.word	35431
	.byte	12,15,167,11,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20605
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_EVRSDCOEFF1',0,15,172,11,3
	.word	35498
	.byte	12,15,175,11,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20758
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_EVRSDCOEFF2',0,15,180,11,3
	.word	35567
	.byte	12,15,183,11,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20914
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_EVRSDCOEFF3',0,15,188,11,3
	.word	35636
	.byte	12,15,191,11,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21076
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_EVRSDCOEFF4',0,15,196,11,3
	.word	35705
	.byte	12,15,199,11,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21219
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_EVRSDCOEFF5',0,15,204,11,3
	.word	35774
	.byte	12,15,207,11,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21384
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_EVRSDCOEFF6',0,15,212,11,3
	.word	35843
	.byte	12,15,215,11,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21529
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_EVRSDCTRL1',0,15,220,11,3
	.word	35912
	.byte	12,15,223,11,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21710
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_EVRSDCTRL2',0,15,228,11,3
	.word	35980
	.byte	12,15,231,11,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21884
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_EVRSDCTRL3',0,15,236,11,3
	.word	36048
	.byte	12,15,239,11,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22044
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_EVRSDCTRL4',0,15,244,11,3
	.word	36116
	.byte	12,15,247,11,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22188
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_EVRSTAT',0,15,252,11,3
	.word	36184
	.byte	12,15,255,11,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22462
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_EVRTRIM',0,15,132,12,3
	.word	36249
	.byte	12,15,135,12,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22601
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_EVRUVMON',0,15,140,12,3
	.word	36314
	.byte	12,15,143,12,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22764
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_EXTCON',0,15,148,12,3
	.word	36380
	.byte	12,15,151,12,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22982
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_FDR',0,15,156,12,3
	.word	36444
	.byte	12,15,159,12,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23145
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_FMR',0,15,164,12,3
	.word	36505
	.byte	12,15,167,12,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23481
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_ID',0,15,172,12,3
	.word	36566
	.byte	12,15,175,12,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23588
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_IGCR',0,15,180,12,3
	.word	36626
	.byte	12,15,183,12,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24040
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_IN',0,15,188,12,3
	.word	36688
	.byte	12,15,191,12,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24139
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_IOCR',0,15,196,12,3
	.word	36748
	.byte	12,15,199,12,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24289
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_LBISTCTRL0',0,15,204,12,3
	.word	36810
	.byte	12,15,207,12,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24438
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_LBISTCTRL1',0,15,212,12,3
	.word	36878
	.byte	12,15,215,12,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24599
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_LBISTCTRL2',0,15,220,12,3
	.word	36946
	.byte	12,15,223,12,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24729
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_LCLCON',0,15,228,12,3
	.word	37014
	.byte	12,15,231,12,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24861
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_LCLTEST',0,15,236,12,3
	.word	37078
	.byte	12,15,239,12,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24976
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_MANID',0,15,244,12,3
	.word	37143
	.byte	12,15,247,12,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25087
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_OMR',0,15,252,12,3
	.word	37206
	.byte	12,15,255,12,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25245
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_OSCCON',0,15,132,13,3
	.word	37267
	.byte	12,15,135,13,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25657
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_OUT',0,15,140,13,3
	.word	37331
	.byte	12,15,143,13,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25758
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_OVCCON',0,15,148,13,3
	.word	37392
	.byte	12,15,151,13,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26025
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_OVCENABLE',0,15,156,13,3
	.word	37456
	.byte	12,15,159,13,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26161
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_PDISC',0,15,164,13,3
	.word	37523
	.byte	12,15,167,13,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26272
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_PDR',0,15,172,13,3
	.word	37586
	.byte	12,15,175,13,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26405
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_PDRR',0,15,180,13,3
	.word	37647
	.byte	12,15,183,13,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26608
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_PLLCON0',0,15,188,13,3
	.word	37709
	.byte	12,15,191,13,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26964
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_PLLCON1',0,15,196,13,3
	.word	37774
	.byte	12,15,199,13,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27142
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_PLLCON2',0,15,204,13,3
	.word	37839
	.byte	12,15,207,13,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27242
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_PLLERAYCON0',0,15,212,13,3
	.word	37904
	.byte	12,15,215,13,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27612
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_PLLERAYCON1',0,15,220,13,3
	.word	37973
	.byte	12,15,223,13,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27798
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_PLLERAYSTAT',0,15,228,13,3
	.word	38042
	.byte	12,15,231,13,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27996
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_PLLSTAT',0,15,236,13,3
	.word	38111
	.byte	12,15,239,13,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28229
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_PMCSR',0,15,244,13,3
	.word	38176
	.byte	12,15,247,13,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28381
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_PMSWCR0',0,15,252,13,3
	.word	38239
	.byte	12,15,255,13,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28948
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_PMSWCR1',0,15,132,14,3
	.word	38304
	.byte	12,15,135,14,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29242
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_PMSWCR2',0,15,140,14,3
	.word	38369
	.byte	12,15,143,14,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29520
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_PMSWSTAT',0,15,148,14,3
	.word	38434
	.byte	12,15,151,14,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30016
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_PMSWSTATCLR',0,15,156,14,3
	.word	38500
	.byte	12,15,159,14,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30538
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_RSTCON',0,15,164,14,3
	.word	38569
	.byte	12,15,167,14,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30329
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_RSTCON2',0,15,172,14,3
	.word	38633
	.byte	12,15,175,14,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30749
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_RSTSTAT',0,15,180,14,3
	.word	38698
	.byte	12,15,183,14,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31181
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_SAFECON',0,15,188,14,3
	.word	38763
	.byte	12,15,191,14,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31277
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_STSTAT',0,15,196,14,3
	.word	38828
	.byte	12,15,199,14,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31537
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_SWRSTCON',0,15,204,14,3
	.word	38892
	.byte	12,15,207,14,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31662
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_SYSCON',0,15,212,14,3
	.word	38958
	.byte	12,15,215,14,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31859
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_TRAPCLR',0,15,220,14,3
	.word	39022
	.byte	12,15,223,14,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32012
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_TRAPDIS',0,15,228,14,3
	.word	39087
	.byte	12,15,231,14,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32165
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_TRAPSET',0,15,236,14,3
	.word	39152
	.byte	12,15,239,14,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32318
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_TRAPSTAT',0,15,244,14,3
	.word	39217
	.byte	12,15,247,14,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32489
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_WDTCPU_CON0',0,15,252,14,3
	.word	39283
	.byte	12,15,255,14,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32619
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_WDTCPU_CON1',0,15,132,15,3
	.word	39352
	.byte	12,15,135,15,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32857
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_WDTCPU_SR',0,15,140,15,3
	.word	39421
	.byte	12,15,143,15,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33080
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_WDTS_CON0',0,15,148,15,3
	.word	39488
	.byte	12,15,151,15,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33206
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_WDTS_CON1',0,15,156,15,3
	.word	39555
	.byte	12,15,159,15,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33458
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_WDTS_SR',0,15,164,15,3
	.word	39622
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU',0,15,175,15,25,12,13
	.byte	'CON0',0
	.word	39283
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	39352
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	39421
	.byte	4,2,35,8,0,16
	.word	39687
	.byte	24
	.byte	'Ifx_SCU_WDTCPU',0,15,180,15,3
	.word	39750
	.byte	10
	.byte	'_Ifx_SCU_WDTS',0,15,183,15,25,12,13
	.byte	'CON0',0
	.word	39488
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	39555
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	39622
	.byte	4,2,35,8,0,16
	.word	39779
	.byte	24
	.byte	'Ifx_SCU_WDTS',0,15,188,15,3
	.word	39840
	.byte	17,3,83,9,1,18
	.byte	'IfxPort_InputMode_undefined',0,127,18
	.byte	'IfxPort_InputMode_noPullDevice',0,0,18
	.byte	'IfxPort_InputMode_pullDown',0,8,18
	.byte	'IfxPort_InputMode_pullUp',0,16,0,24
	.byte	'IfxPort_InputMode',0,3,89,3
	.word	39867
	.byte	17,3,120,9,1,18
	.byte	'IfxPort_OutputIdx_general',0,128,1,18
	.byte	'IfxPort_OutputIdx_alt1',0,136,1,18
	.byte	'IfxPort_OutputIdx_alt2',0,144,1,18
	.byte	'IfxPort_OutputIdx_alt3',0,152,1,18
	.byte	'IfxPort_OutputIdx_alt4',0,160,1,18
	.byte	'IfxPort_OutputIdx_alt5',0,168,1,18
	.byte	'IfxPort_OutputIdx_alt6',0,176,1,18
	.byte	'IfxPort_OutputIdx_alt7',0,184,1,0,24
	.byte	'IfxPort_OutputIdx',0,3,130,1,3
	.word	40018
	.byte	17,3,134,1,9,1,18
	.byte	'IfxPort_OutputMode_pushPull',0,128,1,18
	.byte	'IfxPort_OutputMode_openDrain',0,192,1,0,24
	.byte	'IfxPort_OutputMode',0,3,138,1,3
	.word	40262
	.byte	17,3,144,1,9,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed1',0,0,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed2',0,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed3',0,2,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed4',0,3,18
	.byte	'IfxPort_PadDriver_lvdsSpeed1',0,4,18
	.byte	'IfxPort_PadDriver_lvdsSpeed2',0,5,18
	.byte	'IfxPort_PadDriver_lvdsSpeed3',0,6,18
	.byte	'IfxPort_PadDriver_lvdsSpeed4',0,7,18
	.byte	'IfxPort_PadDriver_ttlSpeed1',0,8,18
	.byte	'IfxPort_PadDriver_ttlSpeed2',0,9,18
	.byte	'IfxPort_PadDriver_ttlSpeed3',0,10,18
	.byte	'IfxPort_PadDriver_ttlSpeed4',0,11,0,24
	.byte	'IfxPort_PadDriver',0,3,158,1,3
	.word	40360
	.byte	24
	.byte	'IfxPort_State',0,3,178,1,3
	.word	8258
	.byte	24
	.byte	'gpio_pin_enum',0,8,89,2
	.word	10794
	.byte	24
	.byte	'gpio_dir_enum',0,8,95,2
	.word	12768
	.byte	24
	.byte	'gpio_mode_enum',0,8,111,2
	.word	12786
.L148:
	.byte	14,8
	.word	266
	.byte	15,1,0,0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L69:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,21,0,54,15,39,12,0,0,3,15,0,73,19,0,0,4,46,1,3,8,32,13
	.byte	58,15,59,15,57,15,54,15,39,12,0,0,5,5,0,3,8,58,15,59,15,57,15,73,19,0,0,6,11,0,0,0,7,36,0,3,8,11,15,62
	.byte	15,0,0,8,46,1,3,8,32,13,58,15,59,15,57,15,73,19,54,15,39,12,0,0,9,59,0,3,8,0,0,10,19,1,3,8,58,15,59,15
	.byte	57,15,11,15,0,0,11,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,12,23,1,58,15,59,15,57,15,11,15,0,0,13,13
	.byte	0,3,8,73,19,11,15,56,9,0,0,14,1,1,11,15,73,19,0,0,15,33,0,47,15,0,0,16,53,0,73,19,0,0,17,4,1,58,15,59
	.byte	15,57,15,11,15,0,0,18,40,0,3,8,28,13,0,0,19,46,1,3,8,58,15,59,15,57,15,54,15,39,12,63,12,60,12,0,0,20
	.byte	38,0,73,19,0,0,21,46,1,3,8,58,15,59,15,57,15,73,19,54,15,39,12,63,12,60,12,0,0,22,46,1,49,19,0,0,23,5
	.byte	0,49,19,0,0,24,22,0,3,8,58,15,59,15,57,15,73,19,0,0,25,21,0,54,15,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L70:
	.word	.L194-.L193
.L193:
	.half	3
	.word	.L196-.L195
.L195:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver',0
	.byte	'F:\\ADS\\AURIX-Studio-1.10.2\\tools\\Compilers\\Tasking_1.1r8\\ctc\\include\\',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std',0
	.byte	0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_Intrinsics.h',0
	.byte	0,0,0
	.byte	'IFXPORT.h',0,1,0,0
	.byte	'IfxPort_regdef.h',0,2,0,0
	.byte	'zf_common_debug.h',0,3,0,0
	.byte	'zf_driver_delay.h',0,4,0,0
	.byte	'zf_driver_spi.h',0,4,0,0
	.byte	'zf_driver_gpio.h',0,4,0,0
	.byte	'../libraries/zf_device/zf_device_icm20602.c',0,0,0,0
	.byte	'stdio.h',0,5,0,0
	.byte	'Platform_Types.h',0,6,0,0
	.byte	'ifx_types.h',0,6,0,0
	.byte	'zf_common_typedef.h',0,3,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxScu_cfg.h',0
	.byte	0,0,0
	.byte	'IfxScu_regdef.h',0,2,0,0,0
.L196:
.L194:
	.sdecl	'.debug_info',debug,cluster('icm20602_get_acc')
	.sect	'.debug_info'
.L71:
	.word	277
	.half	3
	.word	.L72
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_icm20602.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L74,.L73
	.byte	2
	.word	.L67
	.byte	3
	.byte	'icm20602_get_acc',0,1,182,1,6,1,1,1
	.word	.L62,.L120,.L61
	.byte	4
	.word	.L62,.L120
	.byte	5
	.byte	'dat',0,1,184,1,11
	.word	.L121,.L122
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('icm20602_get_acc')
	.sect	'.debug_abbrev'
.L72:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('icm20602_get_acc')
	.sect	'.debug_line'
.L73:
	.word	.L198-.L197
.L197:
	.half	3
	.word	.L200-.L199
.L199:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_icm20602.c',0,0,0,0,0
.L200:
	.byte	5,6,7,0,5,2
	.word	.L62
	.byte	3,181,1,1,5,29,9
	.half	.L183-.L62
	.byte	3,4,1,5,52,9
	.half	.L201-.L183
	.byte	1,5,57,9
	.half	.L202-.L201
	.byte	1,5,5,9
	.half	.L203-.L202
	.byte	3,1,1,5,42,9
	.half	.L204-.L203
	.byte	1,5,46,9
	.half	.L205-.L204
	.byte	1,5,56,9
	.half	.L206-.L205
	.byte	1,5,51,9
	.half	.L207-.L206
	.byte	1,5,20,9
	.half	.L208-.L207
	.byte	1,5,5,9
	.half	.L209-.L208
	.byte	3,1,1,5,42,9
	.half	.L210-.L209
	.byte	1,5,46,9
	.half	.L211-.L210
	.byte	1,5,56,9
	.half	.L212-.L211
	.byte	1,5,51,9
	.half	.L213-.L212
	.byte	1,5,20,9
	.half	.L214-.L213
	.byte	1,5,5,9
	.half	.L215-.L214
	.byte	3,1,1,5,42,9
	.half	.L216-.L215
	.byte	1,5,46,9
	.half	.L217-.L216
	.byte	1,5,56,9
	.half	.L218-.L217
	.byte	1,5,51,9
	.half	.L219-.L218
	.byte	1,5,20,9
	.half	.L220-.L219
	.byte	1,5,1,9
	.half	.L221-.L220
	.byte	3,1,1,7,9
	.half	.L75-.L221
	.byte	0,1,1
.L198:
	.sdecl	'.debug_ranges',debug,cluster('icm20602_get_acc')
	.sect	'.debug_ranges'
.L74:
	.word	-1,.L62,0,.L75-.L62,0,0
	.sdecl	'.debug_info',debug,cluster('icm20602_get_gyro')
	.sect	'.debug_info'
.L76:
	.word	278
	.half	3
	.word	.L77
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_icm20602.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L79,.L78
	.byte	2
	.word	.L67
	.byte	3
	.byte	'icm20602_get_gyro',0,1,199,1,6,1,1,1
	.word	.L64,.L123,.L63
	.byte	4
	.word	.L64,.L123
	.byte	5
	.byte	'dat',0,1,201,1,11
	.word	.L121,.L124
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('icm20602_get_gyro')
	.sect	'.debug_abbrev'
.L77:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('icm20602_get_gyro')
	.sect	'.debug_line'
.L78:
	.word	.L223-.L222
.L222:
	.half	3
	.word	.L225-.L224
.L224:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_icm20602.c',0,0,0,0,0
.L225:
	.byte	5,6,7,0,5,2
	.word	.L64
	.byte	3,198,1,1,5,29,9
	.half	.L184-.L64
	.byte	3,4,1,5,51,9
	.half	.L226-.L184
	.byte	1,5,56,9
	.half	.L227-.L226
	.byte	1,5,5,9
	.half	.L228-.L227
	.byte	3,1,1,5,43,9
	.half	.L229-.L228
	.byte	1,5,47,9
	.half	.L230-.L229
	.byte	1,5,57,9
	.half	.L231-.L230
	.byte	1,5,52,9
	.half	.L232-.L231
	.byte	1,5,21,9
	.half	.L233-.L232
	.byte	1,5,5,9
	.half	.L234-.L233
	.byte	3,1,1,5,43,9
	.half	.L235-.L234
	.byte	1,5,47,9
	.half	.L236-.L235
	.byte	1,5,57,9
	.half	.L237-.L236
	.byte	1,5,52,9
	.half	.L238-.L237
	.byte	1,5,21,9
	.half	.L239-.L238
	.byte	1,5,5,9
	.half	.L240-.L239
	.byte	3,1,1,5,43,9
	.half	.L241-.L240
	.byte	1,5,47,9
	.half	.L242-.L241
	.byte	1,5,57,9
	.half	.L243-.L242
	.byte	1,5,52,9
	.half	.L244-.L243
	.byte	1,5,21,9
	.half	.L245-.L244
	.byte	1,5,1,9
	.half	.L246-.L245
	.byte	3,1,1,7,9
	.half	.L80-.L246
	.byte	0,1,1
.L223:
	.sdecl	'.debug_ranges',debug,cluster('icm20602_get_gyro')
	.sect	'.debug_ranges'
.L79:
	.word	-1,.L64,0,.L80-.L64,0,0
	.sdecl	'.debug_info',debug,cluster('icm20602_init')
	.sect	'.debug_info'
.L81:
	.word	331
	.half	3
	.word	.L82
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_icm20602.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L84,.L83
	.byte	2
	.word	.L67
	.byte	3
	.byte	'icm20602_init',0,1,216,1,7
	.word	.L125
	.byte	1,1,1
	.word	.L66,.L126,.L65
	.byte	4
	.word	.L66,.L126
	.byte	5
	.byte	'val',0,1,218,1,11
	.word	.L125,.L127
	.byte	5
	.byte	'return_state',0,1,218,1,22
	.word	.L125,.L128
	.byte	5
	.byte	'timeout_count',0,1,219,1,12
	.word	.L129,.L130
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('icm20602_init')
	.sect	'.debug_abbrev'
.L82:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('icm20602_init')
	.sect	'.debug_line'
.L83:
	.word	.L248-.L247
.L247:
	.half	3
	.word	.L250-.L249
.L249:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_icm20602.c',0,0,0,0,0
.L250:
	.byte	5,7,7,0,5,2
	.word	.L66
	.byte	3,215,1,1,5,35,9
	.half	.L185-.L66
	.byte	3,2,1,5,26,9
	.half	.L186-.L185
	.byte	3,1,1,5,21,9
	.half	.L187-.L186
	.byte	3,2,1,5,77,9
	.half	.L251-.L187
	.byte	3,5,1,5,95,9
	.half	.L252-.L251
	.byte	1,5,113,9
	.half	.L253-.L252
	.byte	1,5,14,9
	.half	.L254-.L253
	.byte	1,5,28,9
	.half	.L255-.L254
	.byte	1,5,39,9
	.half	.L256-.L255
	.byte	1,5,59,9
	.half	.L257-.L256
	.byte	1,5,15,9
	.half	.L258-.L257
	.byte	3,1,1,5,32,9
	.half	.L259-.L258
	.byte	1,5,37,9
	.half	.L260-.L259
	.byte	1,5,48,9
	.half	.L261-.L260
	.byte	1,5,31,9
	.half	.L20-.L261
	.byte	3,5,1,5,9,9
	.half	.L262-.L20
	.byte	1,5,13,7,9
	.half	.L263-.L262
	.byte	3,5,1,5,26,9
	.half	.L264-.L263
	.byte	3,1,1,5,13,9
	.half	.L265-.L264
	.byte	3,1,1,5,33,9
	.half	.L21-.L265
	.byte	3,3,1,5,54,9
	.half	.L266-.L21
	.byte	1,5,25,9
	.half	.L267-.L266
	.byte	3,1,1,5,42,9
	.half	.L23-.L267
	.byte	3,4,1,5,55,9
	.half	.L189-.L23
	.byte	3,1,1,5,16,9
	.half	.L191-.L189
	.byte	1,5,13,9
	.half	.L268-.L191
	.byte	1,5,17,7,9
	.half	.L269-.L268
	.byte	3,5,1,5,30,9
	.half	.L190-.L269
	.byte	3,1,1,5,17,9
	.half	.L270-.L190
	.byte	3,1,1,5,16,9
	.half	.L24-.L270
	.byte	3,2,1,5,28,9
	.half	.L271-.L24
	.byte	1,5,9,7,9
	.half	.L25-.L271
	.byte	3,1,1,5,13,7,9
	.half	.L272-.L25
	.byte	3,2,1,5,33,9
	.half	.L26-.L272
	.byte	3,3,1,5,58,9
	.half	.L273-.L26
	.byte	1,5,33,9
	.half	.L274-.L273
	.byte	3,1,1,5,58,9
	.half	.L275-.L274
	.byte	1,5,33,9
	.half	.L276-.L275
	.byte	3,1,1,5,58,9
	.half	.L277-.L276
	.byte	1,5,33,9
	.half	.L278-.L277
	.byte	3,1,1,5,58,9
	.half	.L279-.L278
	.byte	1,5,16,9
	.half	.L280-.L279
	.byte	3,7,1,5,18,9
	.half	.L281-.L280
	.byte	3,2,1,9
	.half	.L282-.L281
	.byte	3,5,1,9
	.half	.L283-.L282
	.byte	3,5,1,9
	.half	.L284-.L283
	.byte	3,5,1,5,41,9
	.half	.L28-.L284
	.byte	3,115,1,5,64,9
	.half	.L285-.L28
	.byte	1,5,17,9
	.half	.L286-.L285
	.byte	3,1,1,5,49,9
	.half	.L287-.L286
	.byte	1,5,47,9
	.half	.L288-.L287
	.byte	1,5,14,9
	.half	.L289-.L288
	.byte	3,1,1,5,41,9
	.half	.L29-.L289
	.byte	3,3,1,5,64,9
	.half	.L290-.L29
	.byte	1,5,17,9
	.half	.L291-.L290
	.byte	3,1,1,5,49,9
	.half	.L292-.L291
	.byte	1,5,47,9
	.half	.L293-.L292
	.byte	1,5,14,9
	.half	.L294-.L293
	.byte	3,1,1,5,41,9
	.half	.L30-.L294
	.byte	3,3,1,5,64,9
	.half	.L295-.L30
	.byte	1,5,17,9
	.half	.L296-.L295
	.byte	3,1,1,5,49,9
	.half	.L297-.L296
	.byte	1,5,47,9
	.half	.L298-.L297
	.byte	1,5,14,9
	.half	.L299-.L298
	.byte	3,1,1,5,41,9
	.half	.L31-.L299
	.byte	3,3,1,5,64,9
	.half	.L300-.L31
	.byte	1,5,17,9
	.half	.L301-.L300
	.byte	3,1,1,5,49,9
	.half	.L302-.L301
	.byte	1,5,47,9
	.half	.L303-.L302
	.byte	1,5,14,9
	.half	.L304-.L303
	.byte	3,1,1,5,17,9
	.half	.L32-.L304
	.byte	3,3,1,5,30,9
	.half	.L305-.L32
	.byte	3,1,1,5,14,9
	.half	.L306-.L305
	.byte	3,1,1,5,9,9
	.half	.L33-.L306
	.byte	3,2,1,5,13,7,9
	.half	.L307-.L33
	.byte	3,2,1,5,16,9
	.half	.L38-.L307
	.byte	3,8,1,5,18,9
	.half	.L308-.L38
	.byte	3,2,1,9
	.half	.L309-.L308
	.byte	3,5,1,9
	.half	.L310-.L309
	.byte	3,5,1,9
	.half	.L311-.L310
	.byte	3,5,1,5,41,9
	.half	.L40-.L311
	.byte	3,115,1,5,63,9
	.half	.L312-.L40
	.byte	1,5,17,9
	.half	.L313-.L312
	.byte	3,1,1,5,49,9
	.half	.L314-.L313
	.byte	1,5,47,9
	.half	.L315-.L314
	.byte	1,5,14,9
	.half	.L316-.L315
	.byte	3,1,1,5,41,9
	.half	.L41-.L316
	.byte	3,3,1,5,63,9
	.half	.L317-.L41
	.byte	1,5,17,9
	.half	.L318-.L317
	.byte	3,1,1,5,49,9
	.half	.L319-.L318
	.byte	1,5,47,9
	.half	.L320-.L319
	.byte	1,5,14,9
	.half	.L321-.L320
	.byte	3,1,1,5,41,9
	.half	.L42-.L321
	.byte	3,3,1,5,63,9
	.half	.L322-.L42
	.byte	1,5,17,9
	.half	.L323-.L322
	.byte	3,1,1,5,49,9
	.half	.L324-.L323
	.byte	1,5,47,9
	.half	.L325-.L324
	.byte	1,5,14,9
	.half	.L326-.L325
	.byte	3,1,1,5,41,9
	.half	.L43-.L326
	.byte	3,3,1,5,63,9
	.half	.L327-.L43
	.byte	1,5,17,9
	.half	.L328-.L327
	.byte	3,1,1,5,49,9
	.half	.L329-.L328
	.byte	1,5,47,9
	.half	.L330-.L329
	.byte	1,5,14,9
	.half	.L331-.L330
	.byte	3,1,1,5,17,9
	.half	.L44-.L331
	.byte	3,3,1,5,30,9
	.half	.L332-.L44
	.byte	3,1,1,5,14,9
	.half	.L333-.L332
	.byte	3,1,1,5,9,9
	.half	.L45-.L333
	.byte	3,2,1,5,13,7,9
	.half	.L334-.L45
	.byte	3,2,1,5,33,9
	.half	.L50-.L334
	.byte	3,3,1,5,58,9
	.half	.L335-.L50
	.byte	1,5,5,9
	.half	.L22-.L335
	.byte	3,2,1,5,1,9
	.half	.L52-.L22
	.byte	3,1,1,7,9
	.half	.L85-.L52
	.byte	0,1,1
.L248:
	.sdecl	'.debug_ranges',debug,cluster('icm20602_init')
	.sect	'.debug_ranges'
.L84:
	.word	-1,.L66,0,.L85-.L66,0,0
	.sdecl	'.debug_info',debug,cluster('icm20602_write_register')
	.sect	'.debug_info'
.L86:
	.word	297
	.half	3
	.word	.L87
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_icm20602.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L89,.L88
	.byte	2
	.word	.L67
	.byte	3
	.byte	'icm20602_write_register',0,1,110,13,1,1
	.word	.L54,.L131,.L53
	.byte	4
	.byte	'reg',0,1,110,43
	.word	.L125,.L132
	.byte	4
	.byte	'data',0,1,110,54
	.word	.L125,.L133
	.byte	5
	.word	.L54,.L131
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('icm20602_write_register')
	.sect	'.debug_abbrev'
.L87:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('icm20602_write_register')
	.sect	'.debug_line'
.L88:
	.word	.L337-.L336
.L336:
	.half	3
	.word	.L339-.L338
.L338:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_icm20602.c',0,0,0,0,0
.L339:
	.byte	5,13,7,0,5,2
	.word	.L54
	.byte	3,237,0,1,5,5,9
	.half	.L340-.L54
	.byte	3,2,1,5,29,9
	.half	.L3-.L340
	.byte	3,1,1,5,65,9
	.half	.L153-.L3
	.byte	1,5,5,9
	.half	.L156-.L153
	.byte	3,1,1,5,1,9
	.half	.L5-.L156
	.byte	3,1,1,7,9
	.half	.L90-.L5
	.byte	0,1,1
.L337:
	.sdecl	'.debug_ranges',debug,cluster('icm20602_write_register')
	.sect	'.debug_ranges'
.L89:
	.word	-1,.L54,0,.L90-.L54,0,0
	.sdecl	'.debug_info',debug,cluster('icm20602_read_register')
	.sect	'.debug_info'
.L91:
	.word	301
	.half	3
	.word	.L92
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_icm20602.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L94,.L93
	.byte	2
	.word	.L67
	.byte	3
	.byte	'icm20602_read_register',0,1,124,14
	.word	.L125
	.byte	1,1
	.word	.L56,.L134,.L55
	.byte	4
	.byte	'reg',0,1,124,43
	.word	.L125,.L135
	.byte	5
	.word	.L56,.L134
	.byte	6
	.byte	'data',0,1,126,11
	.word	.L125,.L136
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('icm20602_read_register')
	.sect	'.debug_abbrev'
.L92:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('icm20602_read_register')
	.sect	'.debug_line'
.L93:
	.word	.L342-.L341
.L341:
	.half	3
	.word	.L344-.L343
.L343:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_icm20602.c',0,0,0,0,0
.L344:
	.byte	5,14,7,0,5,2
	.word	.L56
	.byte	3,251,0,1,5,5,9
	.half	.L158-.L56
	.byte	3,3,1,5,35,9
	.half	.L7-.L158
	.byte	3,1,1,5,53,9
	.half	.L345-.L7
	.byte	1,5,10,9
	.half	.L161-.L345
	.byte	1,5,5,9
	.half	.L159-.L161
	.byte	3,1,1,9
	.half	.L9-.L159
	.byte	3,1,1,5,1,9
	.half	.L10-.L9
	.byte	3,1,1,7,9
	.half	.L95-.L10
	.byte	0,1,1
.L342:
	.sdecl	'.debug_ranges',debug,cluster('icm20602_read_register')
	.sect	'.debug_ranges'
.L94:
	.word	-1,.L56,0,.L95-.L56,0,0
	.sdecl	'.debug_info',debug,cluster('icm20602_read_registers')
	.sect	'.debug_info'
.L96:
	.word	317
	.half	3
	.word	.L97
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_icm20602.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L99,.L98
	.byte	2
	.word	.L67
	.byte	3
	.byte	'icm20602_read_registers',0,1,142,1,13,1,1
	.word	.L58,.L137,.L57
	.byte	4
	.byte	'reg',0,1,142,1,43
	.word	.L125,.L138
	.byte	4
	.byte	'data',0,1,142,1,55
	.word	.L139,.L140
	.byte	4
	.byte	'len',0,1,142,1,68
	.word	.L141,.L142
	.byte	5
	.word	.L58,.L137
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('icm20602_read_registers')
	.sect	'.debug_abbrev'
.L97:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('icm20602_read_registers')
	.sect	'.debug_line'
.L98:
	.word	.L347-.L346
.L346:
	.half	3
	.word	.L349-.L348
.L348:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_icm20602.c',0,0,0,0,0
.L349:
	.byte	5,13,7,0,5,2
	.word	.L58
	.byte	3,141,1,1,5,5,9
	.half	.L169-.L58
	.byte	3,2,1,5,29,9
	.half	.L12-.L169
	.byte	3,1,1,5,47,9
	.half	.L350-.L12
	.byte	1,5,71,9
	.half	.L351-.L350
	.byte	1,5,5,9
	.half	.L173-.L351
	.byte	3,1,1,5,1,9
	.half	.L14-.L173
	.byte	3,1,1,7,9
	.half	.L100-.L14
	.byte	0,1,1
.L347:
	.sdecl	'.debug_ranges',debug,cluster('icm20602_read_registers')
	.sect	'.debug_ranges'
.L99:
	.word	-1,.L58,0,.L100-.L58,0,0
	.sdecl	'.debug_info',debug,cluster('icm20602_self_check')
	.sect	'.debug_info'
.L101:
	.word	336
	.half	3
	.word	.L102
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_icm20602.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L104,.L103
	.byte	2
	.word	.L67
	.byte	3
	.byte	'icm20602_self_check',0,1,157,1,14
	.word	.L125
	.byte	1,1
	.word	.L60,.L143,.L59
	.byte	4
	.word	.L60,.L143
	.byte	5
	.byte	'dat',0,1,159,1,11
	.word	.L125,.L144
	.byte	5
	.byte	'return_state',0,1,159,1,20
	.word	.L125,.L145
	.byte	5
	.byte	'timeout_count',0,1,160,1,12
	.word	.L129,.L146
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('icm20602_self_check')
	.sect	'.debug_abbrev'
.L102:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('icm20602_self_check')
	.sect	'.debug_line'
.L103:
	.word	.L353-.L352
.L352:
	.half	3
	.word	.L355-.L354
.L354:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_icm20602.c',0,0,0,0,0
.L355:
	.byte	5,15,7,0,5,2
	.word	.L60
	.byte	3,158,1,1,5,33,9
	.half	.L175-.L60
	.byte	1,5,26,9
	.half	.L177-.L175
	.byte	3,1,1,5,22,9
	.half	.L178-.L177
	.byte	3,2,1,5,51,9
	.half	.L16-.L178
	.byte	3,2,1,5,12,9
	.half	.L180-.L16
	.byte	1,5,9,9
	.half	.L356-.L180
	.byte	1,5,26,7,9
	.half	.L357-.L356
	.byte	3,2,1,5,13,9
	.half	.L358-.L357
	.byte	3,1,1,5,38,9
	.half	.L17-.L358
	.byte	3,2,1,5,13,9
	.half	.L176-.L17
	.byte	1,5,25,9
	.half	.L181-.L176
	.byte	3,1,1,5,11,9
	.half	.L15-.L181
	.byte	3,120,1,5,22,9
	.half	.L359-.L15
	.byte	1,5,5,7,9
	.half	.L18-.L359
	.byte	3,10,1,5,1,9
	.half	.L19-.L18
	.byte	3,1,1,7,9
	.half	.L105-.L19
	.byte	0,1,1
.L353:
	.sdecl	'.debug_ranges',debug,cluster('icm20602_self_check')
	.sect	'.debug_ranges'
.L104:
	.word	-1,.L60,0,.L105-.L60,0,0
	.sdecl	'.debug_info',debug,cluster('icm20602_gyro_x')
	.sect	'.debug_info'
.L106:
	.word	235
	.half	3
	.word	.L107
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_icm20602.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L67
	.byte	3
	.byte	'icm20602_gyro_x',0,9,65,7
	.word	.L147
	.byte	1,5,3
	.word	icm20602_gyro_x
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('icm20602_gyro_x')
	.sect	'.debug_abbrev'
.L107:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('icm20602_gyro_y')
	.sect	'.debug_info'
.L108:
	.word	235
	.half	3
	.word	.L109
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_icm20602.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L67
	.byte	3
	.byte	'icm20602_gyro_y',0,9,65,28
	.word	.L147
	.byte	1,5,3
	.word	icm20602_gyro_y
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('icm20602_gyro_y')
	.sect	'.debug_abbrev'
.L109:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('icm20602_gyro_z')
	.sect	'.debug_info'
.L110:
	.word	235
	.half	3
	.word	.L111
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_icm20602.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L67
	.byte	3
	.byte	'icm20602_gyro_z',0,9,65,49
	.word	.L147
	.byte	1,5,3
	.word	icm20602_gyro_z
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('icm20602_gyro_z')
	.sect	'.debug_abbrev'
.L111:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('icm20602_acc_x')
	.sect	'.debug_info'
.L112:
	.word	234
	.half	3
	.word	.L113
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_icm20602.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L67
	.byte	3
	.byte	'icm20602_acc_x',0,9,66,7
	.word	.L147
	.byte	1,5,3
	.word	icm20602_acc_x
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('icm20602_acc_x')
	.sect	'.debug_abbrev'
.L113:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('icm20602_acc_y')
	.sect	'.debug_info'
.L114:
	.word	234
	.half	3
	.word	.L115
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_icm20602.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L67
	.byte	3
	.byte	'icm20602_acc_y',0,9,66,27
	.word	.L147
	.byte	1,5,3
	.word	icm20602_acc_y
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('icm20602_acc_y')
	.sect	'.debug_abbrev'
.L115:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('icm20602_acc_z')
	.sect	'.debug_info'
.L116:
	.word	234
	.half	3
	.word	.L117
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_icm20602.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L67
	.byte	3
	.byte	'icm20602_acc_z',0,9,66,47
	.word	.L147
	.byte	1,5,3
	.word	icm20602_acc_z
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('icm20602_acc_z')
	.sect	'.debug_abbrev'
.L117:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('icm20602_transition_factor')
	.sect	'.debug_info'
.L118:
	.word	246
	.half	3
	.word	.L119
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_icm20602.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L67
	.byte	3
	.byte	'icm20602_transition_factor',0,9,67,7
	.word	.L148
	.byte	1,5,3
	.word	icm20602_transition_factor
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('icm20602_transition_factor')
	.sect	'.debug_abbrev'
.L119:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_loc',debug,cluster('icm20602_get_acc')
	.sect	'.debug_loc'
.L122:
	.word	-1,.L62,0,.L120-.L62
	.half	2
	.byte	145,120
	.word	0,0
.L61:
	.word	-1,.L62,0,.L183-.L62
	.half	2
	.byte	138,0
	.word	.L183-.L62,.L120-.L62
	.half	2
	.byte	138,8
	.word	.L120-.L62,.L120-.L62
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('icm20602_get_gyro')
	.sect	'.debug_loc'
.L124:
	.word	-1,.L64,0,.L123-.L64
	.half	2
	.byte	145,120
	.word	0,0
.L63:
	.word	-1,.L64,0,.L184-.L64
	.half	2
	.byte	138,0
	.word	.L184-.L64,.L123-.L64
	.half	2
	.byte	138,8
	.word	.L123-.L64,.L123-.L64
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('icm20602_init')
	.sect	'.debug_loc'
.L65:
	.word	-1,.L66,0,.L185-.L66
	.half	2
	.byte	138,0
	.word	.L185-.L66,.L126-.L66
	.half	2
	.byte	138,16
	.word	.L126-.L66,.L126-.L66
	.half	2
	.byte	138,0
	.word	0,0
.L128:
	.word	-1,.L66,.L186-.L66,.L126-.L66
	.half	1
	.byte	89
	.word	.L192-.L66,.L126-.L66
	.half	1
	.byte	82
	.word	0,0
.L130:
	.word	-1,.L66,.L187-.L66,.L188-.L66
	.half	1
	.byte	88
	.word	.L191-.L66,.L126-.L66
	.half	1
	.byte	88
	.word	0,0
.L127:
	.word	-1,.L66,.L189-.L66,.L190-.L66
	.half	1
	.byte	82
	.word	.L24-.L66,.L25-.L66
	.half	1
	.byte	82
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('icm20602_read_register')
	.sect	'.debug_loc'
.L136:
	.word	-1,.L56,.L161-.L56,.L162-.L56
	.half	1
	.byte	82
	.word	.L159-.L56,.L134-.L56
	.half	1
	.byte	88
	.word	.L8-.L56,.L163-.L56
	.half	1
	.byte	82
	.word	.L164-.L56,.L134-.L56
	.half	1
	.byte	82
	.word	0,0
.L55:
	.word	-1,.L56,0,.L134-.L56
	.half	2
	.byte	138,0
	.word	0,0
.L135:
	.word	-1,.L56,0,.L157-.L56
	.half	1
	.byte	84
	.word	.L158-.L56,.L159-.L56
	.half	1
	.byte	88
	.word	.L6-.L56,.L160-.L56
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('icm20602_read_registers')
	.sect	'.debug_loc'
.L140:
	.word	-1,.L58,0,.L165-.L58
	.half	1
	.byte	100
	.word	.L168-.L58,.L137-.L58
	.half	1
	.byte	111
	.word	.L11-.L58,.L170-.L58
	.half	1
	.byte	100
	.word	.L172-.L58,.L173-.L58
	.half	1
	.byte	100
	.word	0,0
.L57:
	.word	-1,.L58,0,.L137-.L58
	.half	2
	.byte	138,0
	.word	0,0
.L142:
	.word	-1,.L58,0,.L165-.L58
	.half	1
	.byte	85
	.word	.L169-.L58,.L137-.L58
	.half	1
	.byte	89
	.word	.L11-.L58,.L170-.L58
	.half	1
	.byte	85
	.word	.L174-.L58,.L173-.L58
	.half	1
	.byte	86
	.word	0,0
.L138:
	.word	-1,.L58,0,.L166-.L58
	.half	1
	.byte	84
	.word	.L167-.L58,.L137-.L58
	.half	1
	.byte	88
	.word	.L11-.L58,.L171-.L58
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('icm20602_self_check')
	.sect	'.debug_loc'
.L144:
	.word	-1,.L60,.L175-.L60,.L176-.L60
	.half	1
	.byte	88
	.word	.L176-.L60,.L15-.L60
	.half	1
	.byte	82
	.word	.L181-.L60,.L143-.L60
	.half	1
	.byte	88
	.word	0,0
.L59:
	.word	-1,.L60,0,.L143-.L60
	.half	2
	.byte	138,0
	.word	0,0
.L145:
	.word	-1,.L60,.L177-.L60,.L143-.L60
	.half	1
	.byte	90
	.word	.L182-.L60,.L143-.L60
	.half	1
	.byte	82
	.word	0,0
.L146:
	.word	-1,.L60,.L178-.L60,.L179-.L60
	.half	1
	.byte	89
	.word	.L180-.L60,.L143-.L60
	.half	1
	.byte	89
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('icm20602_write_register')
	.sect	'.debug_loc'
.L133:
	.word	-1,.L54,0,.L149-.L54
	.half	1
	.byte	85
	.word	.L2-.L54,.L151-.L54
	.half	1
	.byte	85
	.word	.L154-.L54,.L155-.L54
	.half	1
	.byte	89
	.word	.L155-.L54,.L156-.L54
	.half	1
	.byte	86
	.word	0,0
.L53:
	.word	-1,.L54,0,.L131-.L54
	.half	2
	.byte	138,0
	.word	0,0
.L132:
	.word	-1,.L54,0,.L150-.L54
	.half	1
	.byte	84
	.word	.L2-.L54,.L152-.L54
	.half	1
	.byte	84
	.word	.L153-.L54,.L154-.L54
	.half	1
	.byte	88
	.word	.L154-.L54,.L156-.L54
	.half	1
	.byte	85
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L360:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('icm20602_write_register')
	.sect	'.debug_frame'
	.word	12
	.word	.L360,.L54,.L131-.L54
	.sdecl	'.debug_frame',debug,cluster('icm20602_read_register')
	.sect	'.debug_frame'
	.word	12
	.word	.L360,.L56,.L134-.L56
	.sdecl	'.debug_frame',debug,cluster('icm20602_read_registers')
	.sect	'.debug_frame'
	.word	12
	.word	.L360,.L58,.L137-.L58
	.sdecl	'.debug_frame',debug,cluster('icm20602_self_check')
	.sect	'.debug_frame'
	.word	12
	.word	.L360,.L60,.L143-.L60
	.sdecl	'.debug_frame',debug,cluster('icm20602_get_acc')
	.sect	'.debug_frame'
	.word	36
	.word	.L360,.L62,.L120-.L62
	.byte	4
	.word	(.L183-.L62)/2
	.byte	19,8,22,26,3,19,138,8,4
	.word	(.L120-.L183)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('icm20602_get_gyro')
	.sect	'.debug_frame'
	.word	36
	.word	.L360,.L64,.L123-.L64
	.byte	4
	.word	(.L184-.L64)/2
	.byte	19,8,22,26,3,19,138,8,4
	.word	(.L123-.L184)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('icm20602_init')
	.sect	'.debug_frame'
	.word	36
	.word	.L360,.L66,.L126-.L66
	.byte	4
	.word	(.L185-.L66)/2
	.byte	19,16,22,26,3,19,138,16,4
	.word	(.L126-.L185)/2
	.byte	19,0,8,26,0,0
	; Module end
