/**
 * \file IfxFce_bf.h
 * \brief
 * \copyright Copyright (c) 2015 Infineon Technologies AG. All rights reserved.
 *
 * Version: TC26XB_UM_V1.2.R0
 * Specification: tc26xB_um_v1.2_MCSFR.xml (Revision: UM_V1.2)
 * MAY BE CHANGED BY USER [yes/no]: No
 *
 *                                 IMPORTANT NOTICE
 *
 * Infineon Technologies AG (Infineon) is supplying this file for use
 * exclusively with Infineon's microcontroller products. This file can be freely
 * distributed within development tools that are supporting such microcontroller
 * products.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS".  NO WARRANTIES, WHETHER EXPRESS, IMPLIED
 * OR STATUTORY, INCLUDING, BUT NOT LIMITED TO, IMPLIED WARRANTIES OF
 * MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE APPLY TO THIS SOFTWARE.
 * INFINEON SHALL NOT, IN ANY CIRCUMSTANCES, BE LIABLE FOR SPECIAL, INCIDENTAL,
 * OR CONSEQUENTIAL DAMAGES, FOR ANY REASON WHATSOEVER.
 *
 * \defgroup IfxLld_Fce_BitfieldsMask Bitfields mask and offset
 * \ingroup IfxLld_Fce
 * 
 */
#ifndef IFXFCE_BF_H
#define IFXFCE_BF_H 1
/******************************************************************************/
/******************************************************************************/
/** \addtogroup IfxLld_Fce_BitfieldsMask
 * \{  */

/** \brief  Length for Ifx_FCE_ACCEN0_Bits.EN0 */
#define IFX_FCE_ACCEN0_EN0_LEN (1u)

/** \brief  Mask for Ifx_FCE_ACCEN0_Bits.EN0 */
#define IFX_FCE_ACCEN0_EN0_MSK (0x1u)

/** \brief  Offset for Ifx_FCE_ACCEN0_Bits.EN0 */
#define IFX_FCE_ACCEN0_EN0_OFF (0u)

/** \brief  Length for Ifx_FCE_ACCEN0_Bits.EN10 */
#define IFX_FCE_ACCEN0_EN10_LEN (1u)

/** \brief  Mask for Ifx_FCE_ACCEN0_Bits.EN10 */
#define IFX_FCE_ACCEN0_EN10_MSK (0x1u)

/** \brief  Offset for Ifx_FCE_ACCEN0_Bits.EN10 */
#define IFX_FCE_ACCEN0_EN10_OFF (10u)

/** \brief  Length for Ifx_FCE_ACCEN0_Bits.EN11 */
#define IFX_FCE_ACCEN0_EN11_LEN (1u)

/** \brief  Mask for Ifx_FCE_ACCEN0_Bits.EN11 */
#define IFX_FCE_ACCEN0_EN11_MSK (0x1u)

/** \brief  Offset for Ifx_FCE_ACCEN0_Bits.EN11 */
#define IFX_FCE_ACCEN0_EN11_OFF (11u)

/** \brief  Length for Ifx_FCE_ACCEN0_Bits.EN12 */
#define IFX_FCE_ACCEN0_EN12_LEN (1u)

/** \brief  Mask for Ifx_FCE_ACCEN0_Bits.EN12 */
#define IFX_FCE_ACCEN0_EN12_MSK (0x1u)

/** \brief  Offset for Ifx_FCE_ACCEN0_Bits.EN12 */
#define IFX_FCE_ACCEN0_EN12_OFF (12u)

/** \brief  Length for Ifx_FCE_ACCEN0_Bits.EN13 */
#define IFX_FCE_ACCEN0_EN13_LEN (1u)

/** \brief  Mask for Ifx_FCE_ACCEN0_Bits.EN13 */
#define IFX_FCE_ACCEN0_EN13_MSK (0x1u)

/** \brief  Offset for Ifx_FCE_ACCEN0_Bits.EN13 */
#define IFX_FCE_ACCEN0_EN13_OFF (13u)

/** \brief  Length for Ifx_FCE_ACCEN0_Bits.EN14 */
#define IFX_FCE_ACCEN0_EN14_LEN (1u)

/** \brief  Mask for Ifx_FCE_ACCEN0_Bits.EN14 */
#define IFX_FCE_ACCEN0_EN14_MSK (0x1u)

/** \brief  Offset for Ifx_FCE_ACCEN0_Bits.EN14 */
#define IFX_FCE_ACCEN0_EN14_OFF (14u)

/** \brief  Length for Ifx_FCE_ACCEN0_Bits.EN15 */
#define IFX_FCE_ACCEN0_EN15_LEN (1u)

/** \brief  Mask for Ifx_FCE_ACCEN0_Bits.EN15 */
#define IFX_FCE_ACCEN0_EN15_MSK (0x1u)

/** \brief  Offset for Ifx_FCE_ACCEN0_Bits.EN15 */
#define IFX_FCE_ACCEN0_EN15_OFF (15u)

/** \brief  Length for Ifx_FCE_ACCEN0_Bits.EN16 */
#define IFX_FCE_ACCEN0_EN16_LEN (1u)

/** \brief  Mask for Ifx_FCE_ACCEN0_Bits.EN16 */
#define IFX_FCE_ACCEN0_EN16_MSK (0x1u)

/** \brief  Offset for Ifx_FCE_ACCEN0_Bits.EN16 */
#define IFX_FCE_ACCEN0_EN16_OFF (16u)

/** \brief  Length for Ifx_FCE_ACCEN0_Bits.EN17 */
#define IFX_FCE_ACCEN0_EN17_LEN (1u)

/** \brief  Mask for Ifx_FCE_ACCEN0_Bits.EN17 */
#define IFX_FCE_ACCEN0_EN17_MSK (0x1u)

/** \brief  Offset for Ifx_FCE_ACCEN0_Bits.EN17 */
#define IFX_FCE_ACCEN0_EN17_OFF (17u)

/** \brief  Length for Ifx_FCE_ACCEN0_Bits.EN18 */
#define IFX_FCE_ACCEN0_EN18_LEN (1u)

/** \brief  Mask for Ifx_FCE_ACCEN0_Bits.EN18 */
#define IFX_FCE_ACCEN0_EN18_MSK (0x1u)

/** \brief  Offset for Ifx_FCE_ACCEN0_Bits.EN18 */
#define IFX_FCE_ACCEN0_EN18_OFF (18u)

/** \brief  Length for Ifx_FCE_ACCEN0_Bits.EN19 */
#define IFX_FCE_ACCEN0_EN19_LEN (1u)

/** \brief  Mask for Ifx_FCE_ACCEN0_Bits.EN19 */
#define IFX_FCE_ACCEN0_EN19_MSK (0x1u)

/** \brief  Offset for Ifx_FCE_ACCEN0_Bits.EN19 */
#define IFX_FCE_ACCEN0_EN19_OFF (19u)

/** \brief  Length for Ifx_FCE_ACCEN0_Bits.EN1 */
#define IFX_FCE_ACCEN0_EN1_LEN (1u)

/** \brief  Mask for Ifx_FCE_ACCEN0_Bits.EN1 */
#define IFX_FCE_ACCEN0_EN1_MSK (0x1u)

/** \brief  Offset for Ifx_FCE_ACCEN0_Bits.EN1 */
#define IFX_FCE_ACCEN0_EN1_OFF (1u)

/** \brief  Length for Ifx_FCE_ACCEN0_Bits.EN20 */
#define IFX_FCE_ACCEN0_EN20_LEN (1u)

/** \brief  Mask for Ifx_FCE_ACCEN0_Bits.EN20 */
#define IFX_FCE_ACCEN0_EN20_MSK (0x1u)

/** \brief  Offset for Ifx_FCE_ACCEN0_Bits.EN20 */
#define IFX_FCE_ACCEN0_EN20_OFF (20u)

/** \brief  Length for Ifx_FCE_ACCEN0_Bits.EN21 */
#define IFX_FCE_ACCEN0_EN21_LEN (1u)

/** \brief  Mask for Ifx_FCE_ACCEN0_Bits.EN21 */
#define IFX_FCE_ACCEN0_EN21_MSK (0x1u)

/** \brief  Offset for Ifx_FCE_ACCEN0_Bits.EN21 */
#define IFX_FCE_ACCEN0_EN21_OFF (21u)

/** \brief  Length for Ifx_FCE_ACCEN0_Bits.EN22 */
#define IFX_FCE_ACCEN0_EN22_LEN (1u)

/** \brief  Mask for Ifx_FCE_ACCEN0_Bits.EN22 */
#define IFX_FCE_ACCEN0_EN22_MSK (0x1u)

/** \brief  Offset for Ifx_FCE_ACCEN0_Bits.EN22 */
#define IFX_FCE_ACCEN0_EN22_OFF (22u)

/** \brief  Length for Ifx_FCE_ACCEN0_Bits.EN23 */
#define IFX_FCE_ACCEN0_EN23_LEN (1u)

/** \brief  Mask for Ifx_FCE_ACCEN0_Bits.EN23 */
#define IFX_FCE_ACCEN0_EN23_MSK (0x1u)

/** \brief  Offset for Ifx_FCE_ACCEN0_Bits.EN23 */
#define IFX_FCE_ACCEN0_EN23_OFF (23u)

/** \brief  Length for Ifx_FCE_ACCEN0_Bits.EN24 */
#define IFX_FCE_ACCEN0_EN24_LEN (1u)

/** \brief  Mask for Ifx_FCE_ACCEN0_Bits.EN24 */
#define IFX_FCE_ACCEN0_EN24_MSK (0x1u)

/** \brief  Offset for Ifx_FCE_ACCEN0_Bits.EN24 */
#define IFX_FCE_ACCEN0_EN24_OFF (24u)

/** \brief  Length for Ifx_FCE_ACCEN0_Bits.EN25 */
#define IFX_FCE_ACCEN0_EN25_LEN (1u)

/** \brief  Mask for Ifx_FCE_ACCEN0_Bits.EN25 */
#define IFX_FCE_ACCEN0_EN25_MSK (0x1u)

/** \brief  Offset for Ifx_FCE_ACCEN0_Bits.EN25 */
#define IFX_FCE_ACCEN0_EN25_OFF (25u)

/** \brief  Length for Ifx_FCE_ACCEN0_Bits.EN26 */
#define IFX_FCE_ACCEN0_EN26_LEN (1u)

/** \brief  Mask for Ifx_FCE_ACCEN0_Bits.EN26 */
#define IFX_FCE_ACCEN0_EN26_MSK (0x1u)

/** \brief  Offset for Ifx_FCE_ACCEN0_Bits.EN26 */
#define IFX_FCE_ACCEN0_EN26_OFF (26u)

/** \brief  Length for Ifx_FCE_ACCEN0_Bits.EN27 */
#define IFX_FCE_ACCEN0_EN27_LEN (1u)

/** \brief  Mask for Ifx_FCE_ACCEN0_Bits.EN27 */
#define IFX_FCE_ACCEN0_EN27_MSK (0x1u)

/** \brief  Offset for Ifx_FCE_ACCEN0_Bits.EN27 */
#define IFX_FCE_ACCEN0_EN27_OFF (27u)

/** \brief  Length for Ifx_FCE_ACCEN0_Bits.EN28 */
#define IFX_FCE_ACCEN0_EN28_LEN (1u)

/** \brief  Mask for Ifx_FCE_ACCEN0_Bits.EN28 */
#define IFX_FCE_ACCEN0_EN28_MSK (0x1u)

/** \brief  Offset for Ifx_FCE_ACCEN0_Bits.EN28 */
#define IFX_FCE_ACCEN0_EN28_OFF (28u)

/** \brief  Length for Ifx_FCE_ACCEN0_Bits.EN29 */
#define IFX_FCE_ACCEN0_EN29_LEN (1u)

/** \brief  Mask for Ifx_FCE_ACCEN0_Bits.EN29 */
#define IFX_FCE_ACCEN0_EN29_MSK (0x1u)

/** \brief  Offset for Ifx_FCE_ACCEN0_Bits.EN29 */
#define IFX_FCE_ACCEN0_EN29_OFF (29u)

/** \brief  Length for Ifx_FCE_ACCEN0_Bits.EN2 */
#define IFX_FCE_ACCEN0_EN2_LEN (1u)

/** \brief  Mask for Ifx_FCE_ACCEN0_Bits.EN2 */
#define IFX_FCE_ACCEN0_EN2_MSK (0x1u)

/** \brief  Offset for Ifx_FCE_ACCEN0_Bits.EN2 */
#define IFX_FCE_ACCEN0_EN2_OFF (2u)

/** \brief  Length for Ifx_FCE_ACCEN0_Bits.EN30 */
#define IFX_FCE_ACCEN0_EN30_LEN (1u)

/** \brief  Mask for Ifx_FCE_ACCEN0_Bits.EN30 */
#define IFX_FCE_ACCEN0_EN30_MSK (0x1u)

/** \brief  Offset for Ifx_FCE_ACCEN0_Bits.EN30 */
#define IFX_FCE_ACCEN0_EN30_OFF (30u)

/** \brief  Length for Ifx_FCE_ACCEN0_Bits.EN31 */
#define IFX_FCE_ACCEN0_EN31_LEN (1u)

/** \brief  Mask for Ifx_FCE_ACCEN0_Bits.EN31 */
#define IFX_FCE_ACCEN0_EN31_MSK (0x1u)

/** \brief  Offset for Ifx_FCE_ACCEN0_Bits.EN31 */
#define IFX_FCE_ACCEN0_EN31_OFF (31u)

/** \brief  Length for Ifx_FCE_ACCEN0_Bits.EN3 */
#define IFX_FCE_ACCEN0_EN3_LEN (1u)

/** \brief  Mask for Ifx_FCE_ACCEN0_Bits.EN3 */
#define IFX_FCE_ACCEN0_EN3_MSK (0x1u)

/** \brief  Offset for Ifx_FCE_ACCEN0_Bits.EN3 */
#define IFX_FCE_ACCEN0_EN3_OFF (3u)

/** \brief  Length for Ifx_FCE_ACCEN0_Bits.EN4 */
#define IFX_FCE_ACCEN0_EN4_LEN (1u)

/** \brief  Mask for Ifx_FCE_ACCEN0_Bits.EN4 */
#define IFX_FCE_ACCEN0_EN4_MSK (0x1u)

/** \brief  Offset for Ifx_FCE_ACCEN0_Bits.EN4 */
#define IFX_FCE_ACCEN0_EN4_OFF (4u)

/** \brief  Length for Ifx_FCE_ACCEN0_Bits.EN5 */
#define IFX_FCE_ACCEN0_EN5_LEN (1u)

/** \brief  Mask for Ifx_FCE_ACCEN0_Bits.EN5 */
#define IFX_FCE_ACCEN0_EN5_MSK (0x1u)

/** \brief  Offset for Ifx_FCE_ACCEN0_Bits.EN5 */
#define IFX_FCE_ACCEN0_EN5_OFF (5u)

/** \brief  Length for Ifx_FCE_ACCEN0_Bits.EN6 */
#define IFX_FCE_ACCEN0_EN6_LEN (1u)

/** \brief  Mask for Ifx_FCE_ACCEN0_Bits.EN6 */
#define IFX_FCE_ACCEN0_EN6_MSK (0x1u)

/** \brief  Offset for Ifx_FCE_ACCEN0_Bits.EN6 */
#define IFX_FCE_ACCEN0_EN6_OFF (6u)

/** \brief  Length for Ifx_FCE_ACCEN0_Bits.EN7 */
#define IFX_FCE_ACCEN0_EN7_LEN (1u)

/** \brief  Mask for Ifx_FCE_ACCEN0_Bits.EN7 */
#define IFX_FCE_ACCEN0_EN7_MSK (0x1u)

/** \brief  Offset for Ifx_FCE_ACCEN0_Bits.EN7 */
#define IFX_FCE_ACCEN0_EN7_OFF (7u)

/** \brief  Length for Ifx_FCE_ACCEN0_Bits.EN8 */
#define IFX_FCE_ACCEN0_EN8_LEN (1u)

/** \brief  Mask for Ifx_FCE_ACCEN0_Bits.EN8 */
#define IFX_FCE_ACCEN0_EN8_MSK (0x1u)

/** \brief  Offset for Ifx_FCE_ACCEN0_Bits.EN8 */
#define IFX_FCE_ACCEN0_EN8_OFF (8u)

/** \brief  Length for Ifx_FCE_ACCEN0_Bits.EN9 */
#define IFX_FCE_ACCEN0_EN9_LEN (1u)

/** \brief  Mask for Ifx_FCE_ACCEN0_Bits.EN9 */
#define IFX_FCE_ACCEN0_EN9_MSK (0x1u)

/** \brief  Offset for Ifx_FCE_ACCEN0_Bits.EN9 */
#define IFX_FCE_ACCEN0_EN9_OFF (9u)

/** \brief  Length for Ifx_FCE_CFG_Bits.ALR */
#define IFX_FCE_CFG_ALR_LEN (1u)

/** \brief  Mask for Ifx_FCE_CFG_Bits.ALR */
#define IFX_FCE_CFG_ALR_MSK (0x1u)

/** \brief  Offset for Ifx_FCE_CFG_Bits.ALR */
#define IFX_FCE_CFG_ALR_OFF (5u)

/** \brief  Length for Ifx_FCE_CFG_Bits.BEI */
#define IFX_FCE_CFG_BEI_LEN (1u)

/** \brief  Mask for Ifx_FCE_CFG_Bits.BEI */
#define IFX_FCE_CFG_BEI_MSK (0x1u)

/** \brief  Offset for Ifx_FCE_CFG_Bits.BEI */
#define IFX_FCE_CFG_BEI_OFF (3u)

/** \brief  Length for Ifx_FCE_CFG_Bits.CCE */
#define IFX_FCE_CFG_CCE_LEN (1u)

/** \brief  Mask for Ifx_FCE_CFG_Bits.CCE */
#define IFX_FCE_CFG_CCE_MSK (0x1u)

/** \brief  Offset for Ifx_FCE_CFG_Bits.CCE */
#define IFX_FCE_CFG_CCE_OFF (4u)

/** \brief  Length for Ifx_FCE_CFG_Bits.CEI */
#define IFX_FCE_CFG_CEI_LEN (1u)

/** \brief  Mask for Ifx_FCE_CFG_Bits.CEI */
#define IFX_FCE_CFG_CEI_MSK (0x1u)

/** \brief  Offset for Ifx_FCE_CFG_Bits.CEI */
#define IFX_FCE_CFG_CEI_OFF (1u)

/** \brief  Length for Ifx_FCE_CFG_Bits.CMI */
#define IFX_FCE_CFG_CMI_LEN (1u)

/** \brief  Mask for Ifx_FCE_CFG_Bits.CMI */
#define IFX_FCE_CFG_CMI_MSK (0x1u)

/** \brief  Offset for Ifx_FCE_CFG_Bits.CMI */
#define IFX_FCE_CFG_CMI_OFF (0u)

/** \brief  Length for Ifx_FCE_CFG_Bits.LEI */
#define IFX_FCE_CFG_LEI_LEN (1u)

/** \brief  Mask for Ifx_FCE_CFG_Bits.LEI */
#define IFX_FCE_CFG_LEI_MSK (0x1u)

/** \brief  Offset for Ifx_FCE_CFG_Bits.LEI */
#define IFX_FCE_CFG_LEI_OFF (2u)

/** \brief  Length for Ifx_FCE_CFG_Bits.REFIN */
#define IFX_FCE_CFG_REFIN_LEN (1u)

/** \brief  Mask for Ifx_FCE_CFG_Bits.REFIN */
#define IFX_FCE_CFG_REFIN_MSK (0x1u)

/** \brief  Offset for Ifx_FCE_CFG_Bits.REFIN */
#define IFX_FCE_CFG_REFIN_OFF (8u)

/** \brief  Length for Ifx_FCE_CFG_Bits.REFOUT */
#define IFX_FCE_CFG_REFOUT_LEN (1u)

/** \brief  Mask for Ifx_FCE_CFG_Bits.REFOUT */
#define IFX_FCE_CFG_REFOUT_MSK (0x1u)

/** \brief  Offset for Ifx_FCE_CFG_Bits.REFOUT */
#define IFX_FCE_CFG_REFOUT_OFF (9u)

/** \brief  Length for Ifx_FCE_CFG_Bits.XSEL */
#define IFX_FCE_CFG_XSEL_LEN (1u)

/** \brief  Mask for Ifx_FCE_CFG_Bits.XSEL */
#define IFX_FCE_CFG_XSEL_MSK (0x1u)

/** \brief  Offset for Ifx_FCE_CFG_Bits.XSEL */
#define IFX_FCE_CFG_XSEL_OFF (10u)

/** \brief  Length for Ifx_FCE_CLC_Bits.DISR */
#define IFX_FCE_CLC_DISR_LEN (1u)

/** \brief  Mask for Ifx_FCE_CLC_Bits.DISR */
#define IFX_FCE_CLC_DISR_MSK (0x1u)

/** \brief  Offset for Ifx_FCE_CLC_Bits.DISR */
#define IFX_FCE_CLC_DISR_OFF (0u)

/** \brief  Length for Ifx_FCE_CLC_Bits.DISS */
#define IFX_FCE_CLC_DISS_LEN (1u)

/** \brief  Mask for Ifx_FCE_CLC_Bits.DISS */
#define IFX_FCE_CLC_DISS_MSK (0x1u)

/** \brief  Offset for Ifx_FCE_CLC_Bits.DISS */
#define IFX_FCE_CLC_DISS_OFF (1u)

/** \brief  Length for Ifx_FCE_CTR_Bits.FCM */
#define IFX_FCE_CTR_FCM_LEN (1u)

/** \brief  Mask for Ifx_FCE_CTR_Bits.FCM */
#define IFX_FCE_CTR_FCM_MSK (0x1u)

/** \brief  Offset for Ifx_FCE_CTR_Bits.FCM */
#define IFX_FCE_CTR_FCM_OFF (0u)

/** \brief  Length for Ifx_FCE_CTR_Bits.FRM_CFG */
#define IFX_FCE_CTR_FRM_CFG_LEN (1u)

/** \brief  Mask for Ifx_FCE_CTR_Bits.FRM_CFG */
#define IFX_FCE_CTR_FRM_CFG_MSK (0x1u)

/** \brief  Offset for Ifx_FCE_CTR_Bits.FRM_CFG */
#define IFX_FCE_CTR_FRM_CFG_OFF (1u)

/** \brief  Length for Ifx_FCE_CTR_Bits.FRM_CHECK */
#define IFX_FCE_CTR_FRM_CHECK_LEN (1u)

/** \brief  Mask for Ifx_FCE_CTR_Bits.FRM_CHECK */
#define IFX_FCE_CTR_FRM_CHECK_MSK (0x1u)

/** \brief  Offset for Ifx_FCE_CTR_Bits.FRM_CHECK */
#define IFX_FCE_CTR_FRM_CHECK_OFF (2u)

/** \brief  Length for Ifx_FCE_ID_Bits.MODNUMBER */
#define IFX_FCE_ID_MODNUMBER_LEN (16u)

/** \brief  Mask for Ifx_FCE_ID_Bits.MODNUMBER */
#define IFX_FCE_ID_MODNUMBER_MSK (0xffffu)

/** \brief  Offset for Ifx_FCE_ID_Bits.MODNUMBER */
#define IFX_FCE_ID_MODNUMBER_OFF (16u)

/** \brief  Length for Ifx_FCE_ID_Bits.MODREV */
#define IFX_FCE_ID_MODREV_LEN (8u)

/** \brief  Mask for Ifx_FCE_ID_Bits.MODREV */
#define IFX_FCE_ID_MODREV_MSK (0xffu)

/** \brief  Offset for Ifx_FCE_ID_Bits.MODREV */
#define IFX_FCE_ID_MODREV_OFF (0u)

/** \brief  Length for Ifx_FCE_ID_Bits.MODTYPE */
#define IFX_FCE_ID_MODTYPE_LEN (8u)

/** \brief  Mask for Ifx_FCE_ID_Bits.MODTYPE */
#define IFX_FCE_ID_MODTYPE_MSK (0xffu)

/** \brief  Offset for Ifx_FCE_ID_Bits.MODTYPE */
#define IFX_FCE_ID_MODTYPE_OFF (8u)

/** \brief  Length for Ifx_FCE_IN0_CHECK_Bits.CHECK */
#define IFX_FCE_IN0_CHECK_CHECK_LEN (32u)

/** \brief  Mask for Ifx_FCE_IN0_CHECK_Bits.CHECK */
#define IFX_FCE_IN0_CHECK_CHECK_MSK (0xffffffffu)

/** \brief  Offset for Ifx_FCE_IN0_CHECK_Bits.CHECK */
#define IFX_FCE_IN0_CHECK_CHECK_OFF (0u)

/** \brief  Length for Ifx_FCE_IN0_CRC_Bits.CRC */
#define IFX_FCE_IN0_CRC_CRC_LEN (32u)

/** \brief  Mask for Ifx_FCE_IN0_CRC_Bits.CRC */
#define IFX_FCE_IN0_CRC_CRC_MSK (0xffffffffu)

/** \brief  Offset for Ifx_FCE_IN0_CRC_Bits.CRC */
#define IFX_FCE_IN0_CRC_CRC_OFF (0u)

/** \brief  Length for Ifx_FCE_IN0_IR_Bits.IR */
#define IFX_FCE_IN0_IR_IR_LEN (32u)

/** \brief  Mask for Ifx_FCE_IN0_IR_Bits.IR */
#define IFX_FCE_IN0_IR_IR_MSK (0xffffffffu)

/** \brief  Offset for Ifx_FCE_IN0_IR_Bits.IR */
#define IFX_FCE_IN0_IR_IR_OFF (0u)

/** \brief  Length for Ifx_FCE_IN0_RES_Bits.RES */
#define IFX_FCE_IN0_RES_RES_LEN (32u)

/** \brief  Mask for Ifx_FCE_IN0_RES_Bits.RES */
#define IFX_FCE_IN0_RES_RES_MSK (0xffffffffu)

/** \brief  Offset for Ifx_FCE_IN0_RES_Bits.RES */
#define IFX_FCE_IN0_RES_RES_OFF (0u)

/** \brief  Length for Ifx_FCE_IN1_CHECK_Bits.CHECK */
#define IFX_FCE_IN1_CHECK_CHECK_LEN (32u)

/** \brief  Mask for Ifx_FCE_IN1_CHECK_Bits.CHECK */
#define IFX_FCE_IN1_CHECK_CHECK_MSK (0xffffffffu)

/** \brief  Offset for Ifx_FCE_IN1_CHECK_Bits.CHECK */
#define IFX_FCE_IN1_CHECK_CHECK_OFF (0u)

/** \brief  Length for Ifx_FCE_IN1_CRC_Bits.CRC */
#define IFX_FCE_IN1_CRC_CRC_LEN (32u)

/** \brief  Mask for Ifx_FCE_IN1_CRC_Bits.CRC */
#define IFX_FCE_IN1_CRC_CRC_MSK (0xffffffffu)

/** \brief  Offset for Ifx_FCE_IN1_CRC_Bits.CRC */
#define IFX_FCE_IN1_CRC_CRC_OFF (0u)

/** \brief  Length for Ifx_FCE_IN1_IR_Bits.IR */
#define IFX_FCE_IN1_IR_IR_LEN (32u)

/** \brief  Mask for Ifx_FCE_IN1_IR_Bits.IR */
#define IFX_FCE_IN1_IR_IR_MSK (0xffffffffu)

/** \brief  Offset for Ifx_FCE_IN1_IR_Bits.IR */
#define IFX_FCE_IN1_IR_IR_OFF (0u)

/** \brief  Length for Ifx_FCE_IN1_RES_Bits.RES */
#define IFX_FCE_IN1_RES_RES_LEN (32u)

/** \brief  Mask for Ifx_FCE_IN1_RES_Bits.RES */
#define IFX_FCE_IN1_RES_RES_MSK (0xffffffffu)

/** \brief  Offset for Ifx_FCE_IN1_RES_Bits.RES */
#define IFX_FCE_IN1_RES_RES_OFF (0u)

/** \brief  Length for Ifx_FCE_IN2_CHECK_Bits.CHECK */
#define IFX_FCE_IN2_CHECK_CHECK_LEN (16u)

/** \brief  Mask for Ifx_FCE_IN2_CHECK_Bits.CHECK */
#define IFX_FCE_IN2_CHECK_CHECK_MSK (0xffffu)

/** \brief  Offset for Ifx_FCE_IN2_CHECK_Bits.CHECK */
#define IFX_FCE_IN2_CHECK_CHECK_OFF (0u)

/** \brief  Length for Ifx_FCE_IN2_CRC_Bits.CRC */
#define IFX_FCE_IN2_CRC_CRC_LEN (16u)

/** \brief  Mask for Ifx_FCE_IN2_CRC_Bits.CRC */
#define IFX_FCE_IN2_CRC_CRC_MSK (0xffffu)

/** \brief  Offset for Ifx_FCE_IN2_CRC_Bits.CRC */
#define IFX_FCE_IN2_CRC_CRC_OFF (0u)

/** \brief  Length for Ifx_FCE_IN2_IR_Bits.IR */
#define IFX_FCE_IN2_IR_IR_LEN (16u)

/** \brief  Mask for Ifx_FCE_IN2_IR_Bits.IR */
#define IFX_FCE_IN2_IR_IR_MSK (0xffffu)

/** \brief  Offset for Ifx_FCE_IN2_IR_Bits.IR */
#define IFX_FCE_IN2_IR_IR_OFF (0u)

/** \brief  Length for Ifx_FCE_IN2_RES_Bits.RES */
#define IFX_FCE_IN2_RES_RES_LEN (16u)

/** \brief  Mask for Ifx_FCE_IN2_RES_Bits.RES */
#define IFX_FCE_IN2_RES_RES_MSK (0xffffu)

/** \brief  Offset for Ifx_FCE_IN2_RES_Bits.RES */
#define IFX_FCE_IN2_RES_RES_OFF (0u)

/** \brief  Length for Ifx_FCE_IN3_CHECK_Bits.CHECK */
#define IFX_FCE_IN3_CHECK_CHECK_LEN (8u)

/** \brief  Mask for Ifx_FCE_IN3_CHECK_Bits.CHECK */
#define IFX_FCE_IN3_CHECK_CHECK_MSK (0xffu)

/** \brief  Offset for Ifx_FCE_IN3_CHECK_Bits.CHECK */
#define IFX_FCE_IN3_CHECK_CHECK_OFF (0u)

/** \brief  Length for Ifx_FCE_IN3_CRC_Bits.CRC */
#define IFX_FCE_IN3_CRC_CRC_LEN (8u)

/** \brief  Mask for Ifx_FCE_IN3_CRC_Bits.CRC */
#define IFX_FCE_IN3_CRC_CRC_MSK (0xffu)

/** \brief  Offset for Ifx_FCE_IN3_CRC_Bits.CRC */
#define IFX_FCE_IN3_CRC_CRC_OFF (0u)

/** \brief  Length for Ifx_FCE_IN3_IR_Bits.IR */
#define IFX_FCE_IN3_IR_IR_LEN (8u)

/** \brief  Mask for Ifx_FCE_IN3_IR_Bits.IR */
#define IFX_FCE_IN3_IR_IR_MSK (0xffu)

/** \brief  Offset for Ifx_FCE_IN3_IR_Bits.IR */
#define IFX_FCE_IN3_IR_IR_OFF (0u)

/** \brief  Length for Ifx_FCE_IN3_RES_Bits.RES */
#define IFX_FCE_IN3_RES_RES_LEN (8u)

/** \brief  Mask for Ifx_FCE_IN3_RES_Bits.RES */
#define IFX_FCE_IN3_RES_RES_MSK (0xffu)

/** \brief  Offset for Ifx_FCE_IN3_RES_Bits.RES */
#define IFX_FCE_IN3_RES_RES_OFF (0u)

/** \brief  Length for Ifx_FCE_KRST0_Bits.RST */
#define IFX_FCE_KRST0_RST_LEN (1u)

/** \brief  Mask for Ifx_FCE_KRST0_Bits.RST */
#define IFX_FCE_KRST0_RST_MSK (0x1u)

/** \brief  Offset for Ifx_FCE_KRST0_Bits.RST */
#define IFX_FCE_KRST0_RST_OFF (0u)

/** \brief  Length for Ifx_FCE_KRST0_Bits.RSTSTAT */
#define IFX_FCE_KRST0_RSTSTAT_LEN (1u)

/** \brief  Mask for Ifx_FCE_KRST0_Bits.RSTSTAT */
#define IFX_FCE_KRST0_RSTSTAT_MSK (0x1u)

/** \brief  Offset for Ifx_FCE_KRST0_Bits.RSTSTAT */
#define IFX_FCE_KRST0_RSTSTAT_OFF (1u)

/** \brief  Length for Ifx_FCE_KRST1_Bits.RST */
#define IFX_FCE_KRST1_RST_LEN (1u)

/** \brief  Mask for Ifx_FCE_KRST1_Bits.RST */
#define IFX_FCE_KRST1_RST_MSK (0x1u)

/** \brief  Offset for Ifx_FCE_KRST1_Bits.RST */
#define IFX_FCE_KRST1_RST_OFF (0u)

/** \brief  Length for Ifx_FCE_KRSTCLR_Bits.CLR */
#define IFX_FCE_KRSTCLR_CLR_LEN (1u)

/** \brief  Mask for Ifx_FCE_KRSTCLR_Bits.CLR */
#define IFX_FCE_KRSTCLR_CLR_MSK (0x1u)

/** \brief  Offset for Ifx_FCE_KRSTCLR_Bits.CLR */
#define IFX_FCE_KRSTCLR_CLR_OFF (0u)

/** \brief  Length for Ifx_FCE_LENGTH_Bits.LENGTH */
#define IFX_FCE_LENGTH_LENGTH_LEN (16u)

/** \brief  Mask for Ifx_FCE_LENGTH_Bits.LENGTH */
#define IFX_FCE_LENGTH_LENGTH_MSK (0xffffu)

/** \brief  Offset for Ifx_FCE_LENGTH_Bits.LENGTH */
#define IFX_FCE_LENGTH_LENGTH_OFF (0u)

/** \brief  Length for Ifx_FCE_STS_Bits.BEF */
#define IFX_FCE_STS_BEF_LEN (1u)

/** \brief  Mask for Ifx_FCE_STS_Bits.BEF */
#define IFX_FCE_STS_BEF_MSK (0x1u)

/** \brief  Offset for Ifx_FCE_STS_Bits.BEF */
#define IFX_FCE_STS_BEF_OFF (3u)

/** \brief  Length for Ifx_FCE_STS_Bits.CEF */
#define IFX_FCE_STS_CEF_LEN (1u)

/** \brief  Mask for Ifx_FCE_STS_Bits.CEF */
#define IFX_FCE_STS_CEF_MSK (0x1u)

/** \brief  Offset for Ifx_FCE_STS_Bits.CEF */
#define IFX_FCE_STS_CEF_OFF (1u)

/** \brief  Length for Ifx_FCE_STS_Bits.CMF */
#define IFX_FCE_STS_CMF_LEN (1u)

/** \brief  Mask for Ifx_FCE_STS_Bits.CMF */
#define IFX_FCE_STS_CMF_MSK (0x1u)

/** \brief  Offset for Ifx_FCE_STS_Bits.CMF */
#define IFX_FCE_STS_CMF_OFF (0u)

/** \brief  Length for Ifx_FCE_STS_Bits.LEF */
#define IFX_FCE_STS_LEF_LEN (1u)

/** \brief  Mask for Ifx_FCE_STS_Bits.LEF */
#define IFX_FCE_STS_LEF_MSK (0x1u)

/** \brief  Offset for Ifx_FCE_STS_Bits.LEF */
#define IFX_FCE_STS_LEF_OFF (2u)
/** \}  */
/******************************************************************************/
/******************************************************************************/
#endif /* IFXFCE_BF_H */
