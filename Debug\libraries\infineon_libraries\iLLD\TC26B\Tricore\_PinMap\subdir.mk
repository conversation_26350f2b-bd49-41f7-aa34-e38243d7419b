################################################################################
# Automatically-generated file. Do not edit!
################################################################################

# Add inputs and outputs from these tool invocations to the build variables 
C_SRCS += \
../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c \
../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c \
../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCif_PinMap.c \
../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxDsadc_PinMap.c \
../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxEray_PinMap.c \
../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxEth_PinMap.c \
../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxGpt12_PinMap.c \
../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxGtm_PinMap.c \
../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxI2c_PinMap.c \
../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMsc_PinMap.c \
../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMultican_PinMap.c \
../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c \
../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPsi5_PinMap.c \
../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPsi5s_PinMap.c \
../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c \
../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxScu_PinMap.c \
../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxSent_PinMap.c \
../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxSmu_PinMap.c \
../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c 

COMPILED_SRCS += \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.src \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.src \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCif_PinMap.src \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxDsadc_PinMap.src \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxEray_PinMap.src \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxEth_PinMap.src \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxGpt12_PinMap.src \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxGtm_PinMap.src \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxI2c_PinMap.src \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMsc_PinMap.src \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMultican_PinMap.src \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.src \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPsi5_PinMap.src \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPsi5s_PinMap.src \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.src \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxScu_PinMap.src \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxSent_PinMap.src \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxSmu_PinMap.src \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.src 

C_DEPS += \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.d \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.d \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCif_PinMap.d \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxDsadc_PinMap.d \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxEray_PinMap.d \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxEth_PinMap.d \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxGpt12_PinMap.d \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxGtm_PinMap.d \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxI2c_PinMap.d \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMsc_PinMap.d \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMultican_PinMap.d \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.d \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPsi5_PinMap.d \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPsi5s_PinMap.d \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.d \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxScu_PinMap.d \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxSent_PinMap.d \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxSmu_PinMap.d \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.d 

OBJS += \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.o \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.o \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCif_PinMap.o \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxDsadc_PinMap.o \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxEray_PinMap.o \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxEth_PinMap.o \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxGpt12_PinMap.o \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxGtm_PinMap.o \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxI2c_PinMap.o \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMsc_PinMap.o \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMultican_PinMap.o \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.o \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPsi5_PinMap.o \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPsi5s_PinMap.o \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.o \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxScu_PinMap.o \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxSent_PinMap.o \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxSmu_PinMap.o \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.o 


# Each subdirectory must supply rules for building sources it contributes
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.src: ../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/subdir.mk
	cctc -cs --dep-file="$(*F).d" --misrac-version=2004 -D__CPU__=tc26xb "-fC:/Users/<USER>/Desktop/ORRN_Code_4G - 鍓湰/ORRN_Code/Mark_1/Debug/TASKING_C_C___Compiler-Include_paths__-I_.opt" --iso=99 --c++14 --language=+volatile --exceptions --anachronisms --fp-model=2 -O0 --tradeoff=4 --compact-max-size=200 -g -Wc-w544 -Wc-w557 -Ctc26xb -Y0 -N0 -Z0 -o "$@" "$<"
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.o: libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.src libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/subdir.mk
	astc -Og -Os --no-warnings= --error-limit=42 -o  "$@" "$<"
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.src: ../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/subdir.mk
	cctc -cs --dep-file="$(*F).d" --misrac-version=2004 -D__CPU__=tc26xb "-fC:/Users/<USER>/Desktop/ORRN_Code_4G - 鍓湰/ORRN_Code/Mark_1/Debug/TASKING_C_C___Compiler-Include_paths__-I_.opt" --iso=99 --c++14 --language=+volatile --exceptions --anachronisms --fp-model=2 -O0 --tradeoff=4 --compact-max-size=200 -g -Wc-w544 -Wc-w557 -Ctc26xb -Y0 -N0 -Z0 -o "$@" "$<"
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.o: libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.src libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/subdir.mk
	astc -Og -Os --no-warnings= --error-limit=42 -o  "$@" "$<"
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCif_PinMap.src: ../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCif_PinMap.c libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/subdir.mk
	cctc -cs --dep-file="$(*F).d" --misrac-version=2004 -D__CPU__=tc26xb "-fC:/Users/<USER>/Desktop/ORRN_Code_4G - 鍓湰/ORRN_Code/Mark_1/Debug/TASKING_C_C___Compiler-Include_paths__-I_.opt" --iso=99 --c++14 --language=+volatile --exceptions --anachronisms --fp-model=2 -O0 --tradeoff=4 --compact-max-size=200 -g -Wc-w544 -Wc-w557 -Ctc26xb -Y0 -N0 -Z0 -o "$@" "$<"
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCif_PinMap.o: libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCif_PinMap.src libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/subdir.mk
	astc -Og -Os --no-warnings= --error-limit=42 -o  "$@" "$<"
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxDsadc_PinMap.src: ../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxDsadc_PinMap.c libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/subdir.mk
	cctc -cs --dep-file="$(*F).d" --misrac-version=2004 -D__CPU__=tc26xb "-fC:/Users/<USER>/Desktop/ORRN_Code_4G - 鍓湰/ORRN_Code/Mark_1/Debug/TASKING_C_C___Compiler-Include_paths__-I_.opt" --iso=99 --c++14 --language=+volatile --exceptions --anachronisms --fp-model=2 -O0 --tradeoff=4 --compact-max-size=200 -g -Wc-w544 -Wc-w557 -Ctc26xb -Y0 -N0 -Z0 -o "$@" "$<"
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxDsadc_PinMap.o: libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxDsadc_PinMap.src libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/subdir.mk
	astc -Og -Os --no-warnings= --error-limit=42 -o  "$@" "$<"
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxEray_PinMap.src: ../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxEray_PinMap.c libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/subdir.mk
	cctc -cs --dep-file="$(*F).d" --misrac-version=2004 -D__CPU__=tc26xb "-fC:/Users/<USER>/Desktop/ORRN_Code_4G - 鍓湰/ORRN_Code/Mark_1/Debug/TASKING_C_C___Compiler-Include_paths__-I_.opt" --iso=99 --c++14 --language=+volatile --exceptions --anachronisms --fp-model=2 -O0 --tradeoff=4 --compact-max-size=200 -g -Wc-w544 -Wc-w557 -Ctc26xb -Y0 -N0 -Z0 -o "$@" "$<"
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxEray_PinMap.o: libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxEray_PinMap.src libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/subdir.mk
	astc -Og -Os --no-warnings= --error-limit=42 -o  "$@" "$<"
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxEth_PinMap.src: ../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxEth_PinMap.c libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/subdir.mk
	cctc -cs --dep-file="$(*F).d" --misrac-version=2004 -D__CPU__=tc26xb "-fC:/Users/<USER>/Desktop/ORRN_Code_4G - 鍓湰/ORRN_Code/Mark_1/Debug/TASKING_C_C___Compiler-Include_paths__-I_.opt" --iso=99 --c++14 --language=+volatile --exceptions --anachronisms --fp-model=2 -O0 --tradeoff=4 --compact-max-size=200 -g -Wc-w544 -Wc-w557 -Ctc26xb -Y0 -N0 -Z0 -o "$@" "$<"
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxEth_PinMap.o: libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxEth_PinMap.src libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/subdir.mk
	astc -Og -Os --no-warnings= --error-limit=42 -o  "$@" "$<"
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxGpt12_PinMap.src: ../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxGpt12_PinMap.c libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/subdir.mk
	cctc -cs --dep-file="$(*F).d" --misrac-version=2004 -D__CPU__=tc26xb "-fC:/Users/<USER>/Desktop/ORRN_Code_4G - 鍓湰/ORRN_Code/Mark_1/Debug/TASKING_C_C___Compiler-Include_paths__-I_.opt" --iso=99 --c++14 --language=+volatile --exceptions --anachronisms --fp-model=2 -O0 --tradeoff=4 --compact-max-size=200 -g -Wc-w544 -Wc-w557 -Ctc26xb -Y0 -N0 -Z0 -o "$@" "$<"
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxGpt12_PinMap.o: libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxGpt12_PinMap.src libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/subdir.mk
	astc -Og -Os --no-warnings= --error-limit=42 -o  "$@" "$<"
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxGtm_PinMap.src: ../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxGtm_PinMap.c libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/subdir.mk
	cctc -cs --dep-file="$(*F).d" --misrac-version=2004 -D__CPU__=tc26xb "-fC:/Users/<USER>/Desktop/ORRN_Code_4G - 鍓湰/ORRN_Code/Mark_1/Debug/TASKING_C_C___Compiler-Include_paths__-I_.opt" --iso=99 --c++14 --language=+volatile --exceptions --anachronisms --fp-model=2 -O0 --tradeoff=4 --compact-max-size=200 -g -Wc-w544 -Wc-w557 -Ctc26xb -Y0 -N0 -Z0 -o "$@" "$<"
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxGtm_PinMap.o: libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxGtm_PinMap.src libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/subdir.mk
	astc -Og -Os --no-warnings= --error-limit=42 -o  "$@" "$<"
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxI2c_PinMap.src: ../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxI2c_PinMap.c libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/subdir.mk
	cctc -cs --dep-file="$(*F).d" --misrac-version=2004 -D__CPU__=tc26xb "-fC:/Users/<USER>/Desktop/ORRN_Code_4G - 鍓湰/ORRN_Code/Mark_1/Debug/TASKING_C_C___Compiler-Include_paths__-I_.opt" --iso=99 --c++14 --language=+volatile --exceptions --anachronisms --fp-model=2 -O0 --tradeoff=4 --compact-max-size=200 -g -Wc-w544 -Wc-w557 -Ctc26xb -Y0 -N0 -Z0 -o "$@" "$<"
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxI2c_PinMap.o: libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxI2c_PinMap.src libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/subdir.mk
	astc -Og -Os --no-warnings= --error-limit=42 -o  "$@" "$<"
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMsc_PinMap.src: ../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMsc_PinMap.c libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/subdir.mk
	cctc -cs --dep-file="$(*F).d" --misrac-version=2004 -D__CPU__=tc26xb "-fC:/Users/<USER>/Desktop/ORRN_Code_4G - 鍓湰/ORRN_Code/Mark_1/Debug/TASKING_C_C___Compiler-Include_paths__-I_.opt" --iso=99 --c++14 --language=+volatile --exceptions --anachronisms --fp-model=2 -O0 --tradeoff=4 --compact-max-size=200 -g -Wc-w544 -Wc-w557 -Ctc26xb -Y0 -N0 -Z0 -o "$@" "$<"
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMsc_PinMap.o: libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMsc_PinMap.src libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/subdir.mk
	astc -Og -Os --no-warnings= --error-limit=42 -o  "$@" "$<"
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMultican_PinMap.src: ../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMultican_PinMap.c libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/subdir.mk
	cctc -cs --dep-file="$(*F).d" --misrac-version=2004 -D__CPU__=tc26xb "-fC:/Users/<USER>/Desktop/ORRN_Code_4G - 鍓湰/ORRN_Code/Mark_1/Debug/TASKING_C_C___Compiler-Include_paths__-I_.opt" --iso=99 --c++14 --language=+volatile --exceptions --anachronisms --fp-model=2 -O0 --tradeoff=4 --compact-max-size=200 -g -Wc-w544 -Wc-w557 -Ctc26xb -Y0 -N0 -Z0 -o "$@" "$<"
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMultican_PinMap.o: libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMultican_PinMap.src libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/subdir.mk
	astc -Og -Os --no-warnings= --error-limit=42 -o  "$@" "$<"
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.src: ../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/subdir.mk
	cctc -cs --dep-file="$(*F).d" --misrac-version=2004 -D__CPU__=tc26xb "-fC:/Users/<USER>/Desktop/ORRN_Code_4G - 鍓湰/ORRN_Code/Mark_1/Debug/TASKING_C_C___Compiler-Include_paths__-I_.opt" --iso=99 --c++14 --language=+volatile --exceptions --anachronisms --fp-model=2 -O0 --tradeoff=4 --compact-max-size=200 -g -Wc-w544 -Wc-w557 -Ctc26xb -Y0 -N0 -Z0 -o "$@" "$<"
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.o: libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.src libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/subdir.mk
	astc -Og -Os --no-warnings= --error-limit=42 -o  "$@" "$<"
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPsi5_PinMap.src: ../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPsi5_PinMap.c libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/subdir.mk
	cctc -cs --dep-file="$(*F).d" --misrac-version=2004 -D__CPU__=tc26xb "-fC:/Users/<USER>/Desktop/ORRN_Code_4G - 鍓湰/ORRN_Code/Mark_1/Debug/TASKING_C_C___Compiler-Include_paths__-I_.opt" --iso=99 --c++14 --language=+volatile --exceptions --anachronisms --fp-model=2 -O0 --tradeoff=4 --compact-max-size=200 -g -Wc-w544 -Wc-w557 -Ctc26xb -Y0 -N0 -Z0 -o "$@" "$<"
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPsi5_PinMap.o: libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPsi5_PinMap.src libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/subdir.mk
	astc -Og -Os --no-warnings= --error-limit=42 -o  "$@" "$<"
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPsi5s_PinMap.src: ../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPsi5s_PinMap.c libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/subdir.mk
	cctc -cs --dep-file="$(*F).d" --misrac-version=2004 -D__CPU__=tc26xb "-fC:/Users/<USER>/Desktop/ORRN_Code_4G - 鍓湰/ORRN_Code/Mark_1/Debug/TASKING_C_C___Compiler-Include_paths__-I_.opt" --iso=99 --c++14 --language=+volatile --exceptions --anachronisms --fp-model=2 -O0 --tradeoff=4 --compact-max-size=200 -g -Wc-w544 -Wc-w557 -Ctc26xb -Y0 -N0 -Z0 -o "$@" "$<"
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPsi5s_PinMap.o: libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPsi5s_PinMap.src libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/subdir.mk
	astc -Og -Os --no-warnings= --error-limit=42 -o  "$@" "$<"
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.src: ../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/subdir.mk
	cctc -cs --dep-file="$(*F).d" --misrac-version=2004 -D__CPU__=tc26xb "-fC:/Users/<USER>/Desktop/ORRN_Code_4G - 鍓湰/ORRN_Code/Mark_1/Debug/TASKING_C_C___Compiler-Include_paths__-I_.opt" --iso=99 --c++14 --language=+volatile --exceptions --anachronisms --fp-model=2 -O0 --tradeoff=4 --compact-max-size=200 -g -Wc-w544 -Wc-w557 -Ctc26xb -Y0 -N0 -Z0 -o "$@" "$<"
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.o: libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.src libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/subdir.mk
	astc -Og -Os --no-warnings= --error-limit=42 -o  "$@" "$<"
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxScu_PinMap.src: ../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxScu_PinMap.c libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/subdir.mk
	cctc -cs --dep-file="$(*F).d" --misrac-version=2004 -D__CPU__=tc26xb "-fC:/Users/<USER>/Desktop/ORRN_Code_4G - 鍓湰/ORRN_Code/Mark_1/Debug/TASKING_C_C___Compiler-Include_paths__-I_.opt" --iso=99 --c++14 --language=+volatile --exceptions --anachronisms --fp-model=2 -O0 --tradeoff=4 --compact-max-size=200 -g -Wc-w544 -Wc-w557 -Ctc26xb -Y0 -N0 -Z0 -o "$@" "$<"
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxScu_PinMap.o: libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxScu_PinMap.src libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/subdir.mk
	astc -Og -Os --no-warnings= --error-limit=42 -o  "$@" "$<"
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxSent_PinMap.src: ../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxSent_PinMap.c libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/subdir.mk
	cctc -cs --dep-file="$(*F).d" --misrac-version=2004 -D__CPU__=tc26xb "-fC:/Users/<USER>/Desktop/ORRN_Code_4G - 鍓湰/ORRN_Code/Mark_1/Debug/TASKING_C_C___Compiler-Include_paths__-I_.opt" --iso=99 --c++14 --language=+volatile --exceptions --anachronisms --fp-model=2 -O0 --tradeoff=4 --compact-max-size=200 -g -Wc-w544 -Wc-w557 -Ctc26xb -Y0 -N0 -Z0 -o "$@" "$<"
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxSent_PinMap.o: libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxSent_PinMap.src libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/subdir.mk
	astc -Og -Os --no-warnings= --error-limit=42 -o  "$@" "$<"
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxSmu_PinMap.src: ../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxSmu_PinMap.c libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/subdir.mk
	cctc -cs --dep-file="$(*F).d" --misrac-version=2004 -D__CPU__=tc26xb "-fC:/Users/<USER>/Desktop/ORRN_Code_4G - 鍓湰/ORRN_Code/Mark_1/Debug/TASKING_C_C___Compiler-Include_paths__-I_.opt" --iso=99 --c++14 --language=+volatile --exceptions --anachronisms --fp-model=2 -O0 --tradeoff=4 --compact-max-size=200 -g -Wc-w544 -Wc-w557 -Ctc26xb -Y0 -N0 -Z0 -o "$@" "$<"
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxSmu_PinMap.o: libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxSmu_PinMap.src libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/subdir.mk
	astc -Og -Os --no-warnings= --error-limit=42 -o  "$@" "$<"
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.src: ../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/subdir.mk
	cctc -cs --dep-file="$(*F).d" --misrac-version=2004 -D__CPU__=tc26xb "-fC:/Users/<USER>/Desktop/ORRN_Code_4G - 鍓湰/ORRN_Code/Mark_1/Debug/TASKING_C_C___Compiler-Include_paths__-I_.opt" --iso=99 --c++14 --language=+volatile --exceptions --anachronisms --fp-model=2 -O0 --tradeoff=4 --compact-max-size=200 -g -Wc-w544 -Wc-w557 -Ctc26xb -Y0 -N0 -Z0 -o "$@" "$<"
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.o: libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.src libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/subdir.mk
	astc -Og -Os --no-warnings= --error-limit=42 -o  "$@" "$<"

clean: clean-libraries-2f-infineon_libraries-2f-iLLD-2f-TC26B-2f-Tricore-2f-_PinMap

clean-libraries-2f-infineon_libraries-2f-iLLD-2f-TC26B-2f-Tricore-2f-_PinMap:
	-$(RM) libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.d libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.o libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.src libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.d libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.o libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.src libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCif_PinMap.d libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCif_PinMap.o libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCif_PinMap.src libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxDsadc_PinMap.d libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxDsadc_PinMap.o libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxDsadc_PinMap.src libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxEray_PinMap.d libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxEray_PinMap.o libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxEray_PinMap.src libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxEth_PinMap.d libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxEth_PinMap.o libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxEth_PinMap.src libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxGpt12_PinMap.d libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxGpt12_PinMap.o libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxGpt12_PinMap.src libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxGtm_PinMap.d libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxGtm_PinMap.o libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxGtm_PinMap.src libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxI2c_PinMap.d libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxI2c_PinMap.o libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxI2c_PinMap.src libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMsc_PinMap.d libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMsc_PinMap.o libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMsc_PinMap.src libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMultican_PinMap.d libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMultican_PinMap.o libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMultican_PinMap.src libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.d libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.o libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.src libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPsi5_PinMap.d libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPsi5_PinMap.o libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPsi5_PinMap.src libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPsi5s_PinMap.d libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPsi5s_PinMap.o libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPsi5s_PinMap.src libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.d libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.o libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.src libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxScu_PinMap.d libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxScu_PinMap.o libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxScu_PinMap.src libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxSent_PinMap.d libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxSent_PinMap.o libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxSent_PinMap.src libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxSmu_PinMap.d libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxSmu_PinMap.o libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxSmu_PinMap.src libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.d libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.o libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.src

.PHONY: clean-libraries-2f-infineon_libraries-2f-iLLD-2f-TC26B-2f-Tricore-2f-_PinMap

