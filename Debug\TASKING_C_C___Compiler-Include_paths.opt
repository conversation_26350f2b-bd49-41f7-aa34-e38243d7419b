-I"F:\\YY_TC624_YY\\Mark_1" -I"F:\\YY_TC624_YY\\Mark_1\\code" -I"F:\\YY_TC624_YY\\Mark_1\\libraries" -I"F:\\YY_TC624_YY\\Mark_1\\libraries\\infineon_libraries" -I"F:\\YY_TC624_YY\\Mark_1\\libraries\\infineon_libraries\\Configurations" -I"F:\\YY_TC624_YY\\Mark_1\\libraries\\infineon_libraries\\Infra" -I"F:\\YY_TC624_YY\\Mark_1\\libraries\\infineon_libraries\\Infra\\Platform" -I"F:\\YY_TC624_YY\\Mark_1\\libraries\\infineon_libraries\\Infra\\Platform\\Tricore" -I"F:\\YY_TC624_YY\\Mark_1\\libraries\\infineon_libraries\\Infra\\Platform\\Tricore\\Compilers" -I"F:\\YY_TC624_YY\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr" -I"F:\\YY_TC624_YY\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B" -I"F:\\YY_TC624_YY\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg" -I"F:\\YY_TC624_YY\\Mark_1\\libraries\\infineon_libraries\\Service" -I"F:\\YY_TC624_YY\\Mark_1\\libraries\\infineon_libraries\\Service\\CpuGeneric" -I"F:\\YY_TC624_YY\\Mark_1\\libraries\\infineon_libraries\\Service\\CpuGeneric\\If" -I"F:\\YY_TC624_YY\\Mark_1\\libraries\\infineon_libraries\\Service\\CpuGeneric\\If\\Ccu6If" -I"F:\\YY_TC624_YY\\Mark_1\\libraries\\infineon_libraries\\Service\\CpuGeneric\\StdIf" -I"F:\\YY_TC624_YY\\Mark_1\\libraries\\infineon_libraries\\Service\\CpuGeneric\\SysSe" -I"F:\\YY_TC624_YY\\Mark_1\\libraries\\infineon_libraries\\Service\\CpuGeneric\\SysSe\\Bsp" -I"F:\\YY_TC624_YY\\Mark_1\\libraries\\infineon_libraries\\Service\\CpuGeneric\\SysSe\\Math" -I"F:\\YY_TC624_YY\\Mark_1\\libraries\\infineon_libraries\\Service\\CpuGeneric\\_Utilities" -I"F:\\YY_TC624_YY\\Mark_1\\libraries\\infineon_libraries\\iLLD" -I"F:\\YY_TC624_YY\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B" -I"F:\\YY_TC624_YY\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore" -I"F:\\YY_TC624_YY\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Asclin" -I"F:\\YY_TC624_YY\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Asclin\\Asc" -I"F:\\YY_TC624_YY\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Asclin\\Std" -I"F:\\YY_TC624_YY\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Ccu6" -I"F:\\YY_TC624_YY\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Ccu6\\Std" -I"F:\\YY_TC624_YY\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Ccu6\\Timer" -I"F:\\YY_TC624_YY\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Cpu" -I"F:\\YY_TC624_YY\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Cpu\\CStart" -I"F:\\YY_TC624_YY\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Cpu\\Irq" -I"F:\\YY_TC624_YY\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Cpu\\Std" -I"F:\\YY_TC624_YY\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Dma" -I"F:\\YY_TC624_YY\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Dma\\Dma" -I"F:\\YY_TC624_YY\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Dma\\Std" -I"F:\\YY_TC624_YY\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Flash" -I"F:\\YY_TC624_YY\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Flash\\Std" -I"F:\\YY_TC624_YY\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Gpt12" -I"F:\\YY_TC624_YY\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Gpt12\\IncrEnc" -I"F:\\YY_TC624_YY\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Gpt12\\Std" -I"F:\\YY_TC624_YY\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Gtm" -I"F:\\YY_TC624_YY\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Gtm\\Atom" -I"F:\\YY_TC624_YY\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Gtm\\Atom\\Pwm" -I"F:\\YY_TC624_YY\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Gtm\\Std" -I"F:\\YY_TC624_YY\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Mtu" -I"F:\\YY_TC624_YY\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Mtu\\Std" -I"F:\\YY_TC624_YY\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Port" -I"F:\\YY_TC624_YY\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Port\\Std" -I"F:\\YY_TC624_YY\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Qspi" -I"F:\\YY_TC624_YY\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Qspi\\SpiMaster" -I"F:\\YY_TC624_YY\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Qspi\\Std" -I"F:\\YY_TC624_YY\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Scu" -I"F:\\YY_TC624_YY\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Scu\\Std" -I"F:\\YY_TC624_YY\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Src" -I"F:\\YY_TC624_YY\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Src\\Std" -I"F:\\YY_TC624_YY\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Stm" -I"F:\\YY_TC624_YY\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Stm\\Std" -I"F:\\YY_TC624_YY\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Vadc" -I"F:\\YY_TC624_YY\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Vadc\\Adc" -I"F:\\YY_TC624_YY\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Vadc\\Std" -I"F:\\YY_TC624_YY\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\_Impl" -I"F:\\YY_TC624_YY\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\_Lib" -I"F:\\YY_TC624_YY\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\_Lib\\DataHandling" -I"F:\\YY_TC624_YY\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\_PinMap" -I"F:\\YY_TC624_YY\\Mark_1\\libraries\\zf_common" -I"F:\\YY_TC624_YY\\Mark_1\\libraries\\zf_components" -I"F:\\YY_TC624_YY\\Mark_1\\libraries\\zf_device" -I"F:\\YY_TC624_YY\\Mark_1\\libraries\\zf_driver" -I"F:\\YY_TC624_YY\\Mark_1\\user" 