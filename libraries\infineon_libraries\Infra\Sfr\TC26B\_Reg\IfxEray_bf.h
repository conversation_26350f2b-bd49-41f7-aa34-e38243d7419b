/**
 * \file IfxEray_bf.h
 * \brief
 * \copyright Copyright (c) 2015 Infineon Technologies AG. All rights reserved.
 *
 * Version: TC26XB_UM_V1.2.R0
 * Specification: tc26xB_um_v1.2_MCSFR.xml (Revision: UM_V1.2)
 * MAY BE CHANGED BY USER [yes/no]: No
 *
 *                                 IMPORTANT NOTICE
 *
 * Infineon Technologies AG (Infineon) is supplying this file for use
 * exclusively with Infineon's microcontroller products. This file can be freely
 * distributed within development tools that are supporting such microcontroller
 * products.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS".  NO WARRANTIES, WHETHER EXPRESS, IMPLIED
 * OR STATUTORY, INCLUDING, BUT NOT LIMITED TO, IMPLIED WARRANTIES OF
 * MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE APPLY TO THIS SOFTWARE.
 * INFINEON SHALL NOT, IN ANY CIRCUMSTANCES, BE LIABLE FOR SPECIAL, INCIDENTAL,
 * OR CONSEQUENTIAL DAMAGES, FOR ANY REASON WHATSOEVER.
 *
 * \defgroup IfxLld_Eray_BitfieldsMask Bitfields mask and offset
 * \ingroup IfxLld_Eray
 * 
 */
#ifndef IFXERAY_BF_H
#define IFXERAY_BF_H 1
/******************************************************************************/
/******************************************************************************/
/** \addtogroup IfxLld_Eray_BitfieldsMask
 * \{  */

/** \brief  Length for Ifx_ERAY_ACCEN0_Bits.EN0 */
#define IFX_ERAY_ACCEN0_EN0_LEN (1u)

/** \brief  Mask for Ifx_ERAY_ACCEN0_Bits.EN0 */
#define IFX_ERAY_ACCEN0_EN0_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_ACCEN0_Bits.EN0 */
#define IFX_ERAY_ACCEN0_EN0_OFF (0u)

/** \brief  Length for Ifx_ERAY_ACCEN0_Bits.EN10 */
#define IFX_ERAY_ACCEN0_EN10_LEN (1u)

/** \brief  Mask for Ifx_ERAY_ACCEN0_Bits.EN10 */
#define IFX_ERAY_ACCEN0_EN10_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_ACCEN0_Bits.EN10 */
#define IFX_ERAY_ACCEN0_EN10_OFF (10u)

/** \brief  Length for Ifx_ERAY_ACCEN0_Bits.EN11 */
#define IFX_ERAY_ACCEN0_EN11_LEN (1u)

/** \brief  Mask for Ifx_ERAY_ACCEN0_Bits.EN11 */
#define IFX_ERAY_ACCEN0_EN11_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_ACCEN0_Bits.EN11 */
#define IFX_ERAY_ACCEN0_EN11_OFF (11u)

/** \brief  Length for Ifx_ERAY_ACCEN0_Bits.EN12 */
#define IFX_ERAY_ACCEN0_EN12_LEN (1u)

/** \brief  Mask for Ifx_ERAY_ACCEN0_Bits.EN12 */
#define IFX_ERAY_ACCEN0_EN12_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_ACCEN0_Bits.EN12 */
#define IFX_ERAY_ACCEN0_EN12_OFF (12u)

/** \brief  Length for Ifx_ERAY_ACCEN0_Bits.EN13 */
#define IFX_ERAY_ACCEN0_EN13_LEN (1u)

/** \brief  Mask for Ifx_ERAY_ACCEN0_Bits.EN13 */
#define IFX_ERAY_ACCEN0_EN13_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_ACCEN0_Bits.EN13 */
#define IFX_ERAY_ACCEN0_EN13_OFF (13u)

/** \brief  Length for Ifx_ERAY_ACCEN0_Bits.EN14 */
#define IFX_ERAY_ACCEN0_EN14_LEN (1u)

/** \brief  Mask for Ifx_ERAY_ACCEN0_Bits.EN14 */
#define IFX_ERAY_ACCEN0_EN14_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_ACCEN0_Bits.EN14 */
#define IFX_ERAY_ACCEN0_EN14_OFF (14u)

/** \brief  Length for Ifx_ERAY_ACCEN0_Bits.EN15 */
#define IFX_ERAY_ACCEN0_EN15_LEN (1u)

/** \brief  Mask for Ifx_ERAY_ACCEN0_Bits.EN15 */
#define IFX_ERAY_ACCEN0_EN15_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_ACCEN0_Bits.EN15 */
#define IFX_ERAY_ACCEN0_EN15_OFF (15u)

/** \brief  Length for Ifx_ERAY_ACCEN0_Bits.EN16 */
#define IFX_ERAY_ACCEN0_EN16_LEN (1u)

/** \brief  Mask for Ifx_ERAY_ACCEN0_Bits.EN16 */
#define IFX_ERAY_ACCEN0_EN16_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_ACCEN0_Bits.EN16 */
#define IFX_ERAY_ACCEN0_EN16_OFF (16u)

/** \brief  Length for Ifx_ERAY_ACCEN0_Bits.EN17 */
#define IFX_ERAY_ACCEN0_EN17_LEN (1u)

/** \brief  Mask for Ifx_ERAY_ACCEN0_Bits.EN17 */
#define IFX_ERAY_ACCEN0_EN17_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_ACCEN0_Bits.EN17 */
#define IFX_ERAY_ACCEN0_EN17_OFF (17u)

/** \brief  Length for Ifx_ERAY_ACCEN0_Bits.EN18 */
#define IFX_ERAY_ACCEN0_EN18_LEN (1u)

/** \brief  Mask for Ifx_ERAY_ACCEN0_Bits.EN18 */
#define IFX_ERAY_ACCEN0_EN18_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_ACCEN0_Bits.EN18 */
#define IFX_ERAY_ACCEN0_EN18_OFF (18u)

/** \brief  Length for Ifx_ERAY_ACCEN0_Bits.EN19 */
#define IFX_ERAY_ACCEN0_EN19_LEN (1u)

/** \brief  Mask for Ifx_ERAY_ACCEN0_Bits.EN19 */
#define IFX_ERAY_ACCEN0_EN19_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_ACCEN0_Bits.EN19 */
#define IFX_ERAY_ACCEN0_EN19_OFF (19u)

/** \brief  Length for Ifx_ERAY_ACCEN0_Bits.EN1 */
#define IFX_ERAY_ACCEN0_EN1_LEN (1u)

/** \brief  Mask for Ifx_ERAY_ACCEN0_Bits.EN1 */
#define IFX_ERAY_ACCEN0_EN1_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_ACCEN0_Bits.EN1 */
#define IFX_ERAY_ACCEN0_EN1_OFF (1u)

/** \brief  Length for Ifx_ERAY_ACCEN0_Bits.EN20 */
#define IFX_ERAY_ACCEN0_EN20_LEN (1u)

/** \brief  Mask for Ifx_ERAY_ACCEN0_Bits.EN20 */
#define IFX_ERAY_ACCEN0_EN20_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_ACCEN0_Bits.EN20 */
#define IFX_ERAY_ACCEN0_EN20_OFF (20u)

/** \brief  Length for Ifx_ERAY_ACCEN0_Bits.EN21 */
#define IFX_ERAY_ACCEN0_EN21_LEN (1u)

/** \brief  Mask for Ifx_ERAY_ACCEN0_Bits.EN21 */
#define IFX_ERAY_ACCEN0_EN21_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_ACCEN0_Bits.EN21 */
#define IFX_ERAY_ACCEN0_EN21_OFF (21u)

/** \brief  Length for Ifx_ERAY_ACCEN0_Bits.EN22 */
#define IFX_ERAY_ACCEN0_EN22_LEN (1u)

/** \brief  Mask for Ifx_ERAY_ACCEN0_Bits.EN22 */
#define IFX_ERAY_ACCEN0_EN22_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_ACCEN0_Bits.EN22 */
#define IFX_ERAY_ACCEN0_EN22_OFF (22u)

/** \brief  Length for Ifx_ERAY_ACCEN0_Bits.EN23 */
#define IFX_ERAY_ACCEN0_EN23_LEN (1u)

/** \brief  Mask for Ifx_ERAY_ACCEN0_Bits.EN23 */
#define IFX_ERAY_ACCEN0_EN23_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_ACCEN0_Bits.EN23 */
#define IFX_ERAY_ACCEN0_EN23_OFF (23u)

/** \brief  Length for Ifx_ERAY_ACCEN0_Bits.EN24 */
#define IFX_ERAY_ACCEN0_EN24_LEN (1u)

/** \brief  Mask for Ifx_ERAY_ACCEN0_Bits.EN24 */
#define IFX_ERAY_ACCEN0_EN24_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_ACCEN0_Bits.EN24 */
#define IFX_ERAY_ACCEN0_EN24_OFF (24u)

/** \brief  Length for Ifx_ERAY_ACCEN0_Bits.EN25 */
#define IFX_ERAY_ACCEN0_EN25_LEN (1u)

/** \brief  Mask for Ifx_ERAY_ACCEN0_Bits.EN25 */
#define IFX_ERAY_ACCEN0_EN25_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_ACCEN0_Bits.EN25 */
#define IFX_ERAY_ACCEN0_EN25_OFF (25u)

/** \brief  Length for Ifx_ERAY_ACCEN0_Bits.EN26 */
#define IFX_ERAY_ACCEN0_EN26_LEN (1u)

/** \brief  Mask for Ifx_ERAY_ACCEN0_Bits.EN26 */
#define IFX_ERAY_ACCEN0_EN26_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_ACCEN0_Bits.EN26 */
#define IFX_ERAY_ACCEN0_EN26_OFF (26u)

/** \brief  Length for Ifx_ERAY_ACCEN0_Bits.EN27 */
#define IFX_ERAY_ACCEN0_EN27_LEN (1u)

/** \brief  Mask for Ifx_ERAY_ACCEN0_Bits.EN27 */
#define IFX_ERAY_ACCEN0_EN27_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_ACCEN0_Bits.EN27 */
#define IFX_ERAY_ACCEN0_EN27_OFF (27u)

/** \brief  Length for Ifx_ERAY_ACCEN0_Bits.EN28 */
#define IFX_ERAY_ACCEN0_EN28_LEN (1u)

/** \brief  Mask for Ifx_ERAY_ACCEN0_Bits.EN28 */
#define IFX_ERAY_ACCEN0_EN28_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_ACCEN0_Bits.EN28 */
#define IFX_ERAY_ACCEN0_EN28_OFF (28u)

/** \brief  Length for Ifx_ERAY_ACCEN0_Bits.EN29 */
#define IFX_ERAY_ACCEN0_EN29_LEN (1u)

/** \brief  Mask for Ifx_ERAY_ACCEN0_Bits.EN29 */
#define IFX_ERAY_ACCEN0_EN29_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_ACCEN0_Bits.EN29 */
#define IFX_ERAY_ACCEN0_EN29_OFF (29u)

/** \brief  Length for Ifx_ERAY_ACCEN0_Bits.EN2 */
#define IFX_ERAY_ACCEN0_EN2_LEN (1u)

/** \brief  Mask for Ifx_ERAY_ACCEN0_Bits.EN2 */
#define IFX_ERAY_ACCEN0_EN2_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_ACCEN0_Bits.EN2 */
#define IFX_ERAY_ACCEN0_EN2_OFF (2u)

/** \brief  Length for Ifx_ERAY_ACCEN0_Bits.EN30 */
#define IFX_ERAY_ACCEN0_EN30_LEN (1u)

/** \brief  Mask for Ifx_ERAY_ACCEN0_Bits.EN30 */
#define IFX_ERAY_ACCEN0_EN30_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_ACCEN0_Bits.EN30 */
#define IFX_ERAY_ACCEN0_EN30_OFF (30u)

/** \brief  Length for Ifx_ERAY_ACCEN0_Bits.EN31 */
#define IFX_ERAY_ACCEN0_EN31_LEN (1u)

/** \brief  Mask for Ifx_ERAY_ACCEN0_Bits.EN31 */
#define IFX_ERAY_ACCEN0_EN31_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_ACCEN0_Bits.EN31 */
#define IFX_ERAY_ACCEN0_EN31_OFF (31u)

/** \brief  Length for Ifx_ERAY_ACCEN0_Bits.EN3 */
#define IFX_ERAY_ACCEN0_EN3_LEN (1u)

/** \brief  Mask for Ifx_ERAY_ACCEN0_Bits.EN3 */
#define IFX_ERAY_ACCEN0_EN3_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_ACCEN0_Bits.EN3 */
#define IFX_ERAY_ACCEN0_EN3_OFF (3u)

/** \brief  Length for Ifx_ERAY_ACCEN0_Bits.EN4 */
#define IFX_ERAY_ACCEN0_EN4_LEN (1u)

/** \brief  Mask for Ifx_ERAY_ACCEN0_Bits.EN4 */
#define IFX_ERAY_ACCEN0_EN4_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_ACCEN0_Bits.EN4 */
#define IFX_ERAY_ACCEN0_EN4_OFF (4u)

/** \brief  Length for Ifx_ERAY_ACCEN0_Bits.EN5 */
#define IFX_ERAY_ACCEN0_EN5_LEN (1u)

/** \brief  Mask for Ifx_ERAY_ACCEN0_Bits.EN5 */
#define IFX_ERAY_ACCEN0_EN5_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_ACCEN0_Bits.EN5 */
#define IFX_ERAY_ACCEN0_EN5_OFF (5u)

/** \brief  Length for Ifx_ERAY_ACCEN0_Bits.EN6 */
#define IFX_ERAY_ACCEN0_EN6_LEN (1u)

/** \brief  Mask for Ifx_ERAY_ACCEN0_Bits.EN6 */
#define IFX_ERAY_ACCEN0_EN6_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_ACCEN0_Bits.EN6 */
#define IFX_ERAY_ACCEN0_EN6_OFF (6u)

/** \brief  Length for Ifx_ERAY_ACCEN0_Bits.EN7 */
#define IFX_ERAY_ACCEN0_EN7_LEN (1u)

/** \brief  Mask for Ifx_ERAY_ACCEN0_Bits.EN7 */
#define IFX_ERAY_ACCEN0_EN7_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_ACCEN0_Bits.EN7 */
#define IFX_ERAY_ACCEN0_EN7_OFF (7u)

/** \brief  Length for Ifx_ERAY_ACCEN0_Bits.EN8 */
#define IFX_ERAY_ACCEN0_EN8_LEN (1u)

/** \brief  Mask for Ifx_ERAY_ACCEN0_Bits.EN8 */
#define IFX_ERAY_ACCEN0_EN8_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_ACCEN0_Bits.EN8 */
#define IFX_ERAY_ACCEN0_EN8_OFF (8u)

/** \brief  Length for Ifx_ERAY_ACCEN0_Bits.EN9 */
#define IFX_ERAY_ACCEN0_EN9_LEN (1u)

/** \brief  Mask for Ifx_ERAY_ACCEN0_Bits.EN9 */
#define IFX_ERAY_ACCEN0_EN9_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_ACCEN0_Bits.EN9 */
#define IFX_ERAY_ACCEN0_EN9_OFF (9u)

/** \brief  Length for Ifx_ERAY_ACS_Bits.CEDA */
#define IFX_ERAY_ACS_CEDA_LEN (1u)

/** \brief  Mask for Ifx_ERAY_ACS_Bits.CEDA */
#define IFX_ERAY_ACS_CEDA_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_ACS_Bits.CEDA */
#define IFX_ERAY_ACS_CEDA_OFF (2u)

/** \brief  Length for Ifx_ERAY_ACS_Bits.CEDB */
#define IFX_ERAY_ACS_CEDB_LEN (1u)

/** \brief  Mask for Ifx_ERAY_ACS_Bits.CEDB */
#define IFX_ERAY_ACS_CEDB_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_ACS_Bits.CEDB */
#define IFX_ERAY_ACS_CEDB_OFF (10u)

/** \brief  Length for Ifx_ERAY_ACS_Bits.CIA */
#define IFX_ERAY_ACS_CIA_LEN (1u)

/** \brief  Mask for Ifx_ERAY_ACS_Bits.CIA */
#define IFX_ERAY_ACS_CIA_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_ACS_Bits.CIA */
#define IFX_ERAY_ACS_CIA_OFF (3u)

/** \brief  Length for Ifx_ERAY_ACS_Bits.CIB */
#define IFX_ERAY_ACS_CIB_LEN (1u)

/** \brief  Mask for Ifx_ERAY_ACS_Bits.CIB */
#define IFX_ERAY_ACS_CIB_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_ACS_Bits.CIB */
#define IFX_ERAY_ACS_CIB_OFF (11u)

/** \brief  Length for Ifx_ERAY_ACS_Bits.SBVA */
#define IFX_ERAY_ACS_SBVA_LEN (1u)

/** \brief  Mask for Ifx_ERAY_ACS_Bits.SBVA */
#define IFX_ERAY_ACS_SBVA_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_ACS_Bits.SBVA */
#define IFX_ERAY_ACS_SBVA_OFF (4u)

/** \brief  Length for Ifx_ERAY_ACS_Bits.SBVB */
#define IFX_ERAY_ACS_SBVB_LEN (1u)

/** \brief  Mask for Ifx_ERAY_ACS_Bits.SBVB */
#define IFX_ERAY_ACS_SBVB_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_ACS_Bits.SBVB */
#define IFX_ERAY_ACS_SBVB_OFF (12u)

/** \brief  Length for Ifx_ERAY_ACS_Bits.SEDA */
#define IFX_ERAY_ACS_SEDA_LEN (1u)

/** \brief  Mask for Ifx_ERAY_ACS_Bits.SEDA */
#define IFX_ERAY_ACS_SEDA_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_ACS_Bits.SEDA */
#define IFX_ERAY_ACS_SEDA_OFF (1u)

/** \brief  Length for Ifx_ERAY_ACS_Bits.SEDB */
#define IFX_ERAY_ACS_SEDB_LEN (1u)

/** \brief  Mask for Ifx_ERAY_ACS_Bits.SEDB */
#define IFX_ERAY_ACS_SEDB_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_ACS_Bits.SEDB */
#define IFX_ERAY_ACS_SEDB_OFF (9u)

/** \brief  Length for Ifx_ERAY_ACS_Bits.VFRA */
#define IFX_ERAY_ACS_VFRA_LEN (1u)

/** \brief  Mask for Ifx_ERAY_ACS_Bits.VFRA */
#define IFX_ERAY_ACS_VFRA_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_ACS_Bits.VFRA */
#define IFX_ERAY_ACS_VFRA_OFF (0u)

/** \brief  Length for Ifx_ERAY_ACS_Bits.VFRB */
#define IFX_ERAY_ACS_VFRB_LEN (1u)

/** \brief  Mask for Ifx_ERAY_ACS_Bits.VFRB */
#define IFX_ERAY_ACS_VFRB_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_ACS_Bits.VFRB */
#define IFX_ERAY_ACS_VFRB_OFF (8u)

/** \brief  Length for Ifx_ERAY_CCEV_Bits.CCFC */
#define IFX_ERAY_CCEV_CCFC_LEN (4u)

/** \brief  Mask for Ifx_ERAY_CCEV_Bits.CCFC */
#define IFX_ERAY_CCEV_CCFC_MSK (0xfu)

/** \brief  Offset for Ifx_ERAY_CCEV_Bits.CCFC */
#define IFX_ERAY_CCEV_CCFC_OFF (0u)

/** \brief  Length for Ifx_ERAY_CCEV_Bits.ERRM */
#define IFX_ERAY_CCEV_ERRM_LEN (2u)

/** \brief  Mask for Ifx_ERAY_CCEV_Bits.ERRM */
#define IFX_ERAY_CCEV_ERRM_MSK (0x3u)

/** \brief  Offset for Ifx_ERAY_CCEV_Bits.ERRM */
#define IFX_ERAY_CCEV_ERRM_OFF (6u)

/** \brief  Length for Ifx_ERAY_CCEV_Bits.PTAC */
#define IFX_ERAY_CCEV_PTAC_LEN (5u)

/** \brief  Mask for Ifx_ERAY_CCEV_Bits.PTAC */
#define IFX_ERAY_CCEV_PTAC_MSK (0x1fu)

/** \brief  Offset for Ifx_ERAY_CCEV_Bits.PTAC */
#define IFX_ERAY_CCEV_PTAC_OFF (8u)

/** \brief  Length for Ifx_ERAY_CCSV_Bits.CSAI */
#define IFX_ERAY_CCSV_CSAI_LEN (1u)

/** \brief  Mask for Ifx_ERAY_CCSV_Bits.CSAI */
#define IFX_ERAY_CCSV_CSAI_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_CCSV_Bits.CSAI */
#define IFX_ERAY_CCSV_CSAI_OFF (13u)

/** \brief  Length for Ifx_ERAY_CCSV_Bits.CSI */
#define IFX_ERAY_CCSV_CSI_LEN (1u)

/** \brief  Mask for Ifx_ERAY_CCSV_Bits.CSI */
#define IFX_ERAY_CCSV_CSI_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_CCSV_Bits.CSI */
#define IFX_ERAY_CCSV_CSI_OFF (14u)

/** \brief  Length for Ifx_ERAY_CCSV_Bits.CSNI */
#define IFX_ERAY_CCSV_CSNI_LEN (1u)

/** \brief  Mask for Ifx_ERAY_CCSV_Bits.CSNI */
#define IFX_ERAY_CCSV_CSNI_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_CCSV_Bits.CSNI */
#define IFX_ERAY_CCSV_CSNI_OFF (12u)

/** \brief  Length for Ifx_ERAY_CCSV_Bits.FSI */
#define IFX_ERAY_CCSV_FSI_LEN (1u)

/** \brief  Mask for Ifx_ERAY_CCSV_Bits.FSI */
#define IFX_ERAY_CCSV_FSI_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_CCSV_Bits.FSI */
#define IFX_ERAY_CCSV_FSI_OFF (6u)

/** \brief  Length for Ifx_ERAY_CCSV_Bits.HRQ */
#define IFX_ERAY_CCSV_HRQ_LEN (1u)

/** \brief  Mask for Ifx_ERAY_CCSV_Bits.HRQ */
#define IFX_ERAY_CCSV_HRQ_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_CCSV_Bits.HRQ */
#define IFX_ERAY_CCSV_HRQ_OFF (7u)

/** \brief  Length for Ifx_ERAY_CCSV_Bits.POCS */
#define IFX_ERAY_CCSV_POCS_LEN (6u)

/** \brief  Mask for Ifx_ERAY_CCSV_Bits.POCS */
#define IFX_ERAY_CCSV_POCS_MSK (0x3fu)

/** \brief  Offset for Ifx_ERAY_CCSV_Bits.POCS */
#define IFX_ERAY_CCSV_POCS_OFF (0u)

/** \brief  Length for Ifx_ERAY_CCSV_Bits.PSL */
#define IFX_ERAY_CCSV_PSL_LEN (6u)

/** \brief  Mask for Ifx_ERAY_CCSV_Bits.PSL */
#define IFX_ERAY_CCSV_PSL_MSK (0x3fu)

/** \brief  Offset for Ifx_ERAY_CCSV_Bits.PSL */
#define IFX_ERAY_CCSV_PSL_OFF (24u)

/** \brief  Length for Ifx_ERAY_CCSV_Bits.RCA */
#define IFX_ERAY_CCSV_RCA_LEN (5u)

/** \brief  Mask for Ifx_ERAY_CCSV_Bits.RCA */
#define IFX_ERAY_CCSV_RCA_MSK (0x1fu)

/** \brief  Offset for Ifx_ERAY_CCSV_Bits.RCA */
#define IFX_ERAY_CCSV_RCA_OFF (19u)

/** \brief  Length for Ifx_ERAY_CCSV_Bits.SLM */
#define IFX_ERAY_CCSV_SLM_LEN (2u)

/** \brief  Mask for Ifx_ERAY_CCSV_Bits.SLM */
#define IFX_ERAY_CCSV_SLM_MSK (0x3u)

/** \brief  Offset for Ifx_ERAY_CCSV_Bits.SLM */
#define IFX_ERAY_CCSV_SLM_OFF (8u)

/** \brief  Length for Ifx_ERAY_CCSV_Bits.WSV */
#define IFX_ERAY_CCSV_WSV_LEN (3u)

/** \brief  Mask for Ifx_ERAY_CCSV_Bits.WSV */
#define IFX_ERAY_CCSV_WSV_MSK (0x7u)

/** \brief  Offset for Ifx_ERAY_CCSV_Bits.WSV */
#define IFX_ERAY_CCSV_WSV_OFF (16u)

/** \brief  Length for Ifx_ERAY_CLC_Bits.DISR */
#define IFX_ERAY_CLC_DISR_LEN (1u)

/** \brief  Mask for Ifx_ERAY_CLC_Bits.DISR */
#define IFX_ERAY_CLC_DISR_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_CLC_Bits.DISR */
#define IFX_ERAY_CLC_DISR_OFF (0u)

/** \brief  Length for Ifx_ERAY_CLC_Bits.DISS */
#define IFX_ERAY_CLC_DISS_LEN (1u)

/** \brief  Mask for Ifx_ERAY_CLC_Bits.DISS */
#define IFX_ERAY_CLC_DISS_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_CLC_Bits.DISS */
#define IFX_ERAY_CLC_DISS_OFF (1u)

/** \brief  Length for Ifx_ERAY_CLC_Bits.EDIS */
#define IFX_ERAY_CLC_EDIS_LEN (1u)

/** \brief  Mask for Ifx_ERAY_CLC_Bits.EDIS */
#define IFX_ERAY_CLC_EDIS_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_CLC_Bits.EDIS */
#define IFX_ERAY_CLC_EDIS_OFF (3u)

/** \brief  Length for Ifx_ERAY_CLC_Bits.RMC */
#define IFX_ERAY_CLC_RMC_LEN (3u)

/** \brief  Mask for Ifx_ERAY_CLC_Bits.RMC */
#define IFX_ERAY_CLC_RMC_MSK (0x7u)

/** \brief  Offset for Ifx_ERAY_CLC_Bits.RMC */
#define IFX_ERAY_CLC_RMC_OFF (8u)

/** \brief  Length for Ifx_ERAY_CREL_Bits.DAY */
#define IFX_ERAY_CREL_DAY_LEN (8u)

/** \brief  Mask for Ifx_ERAY_CREL_Bits.DAY */
#define IFX_ERAY_CREL_DAY_MSK (0xffu)

/** \brief  Offset for Ifx_ERAY_CREL_Bits.DAY */
#define IFX_ERAY_CREL_DAY_OFF (0u)

/** \brief  Length for Ifx_ERAY_CREL_Bits.MON */
#define IFX_ERAY_CREL_MON_LEN (8u)

/** \brief  Mask for Ifx_ERAY_CREL_Bits.MON */
#define IFX_ERAY_CREL_MON_MSK (0xffu)

/** \brief  Offset for Ifx_ERAY_CREL_Bits.MON */
#define IFX_ERAY_CREL_MON_OFF (8u)

/** \brief  Length for Ifx_ERAY_CREL_Bits.REL */
#define IFX_ERAY_CREL_REL_LEN (4u)

/** \brief  Mask for Ifx_ERAY_CREL_Bits.REL */
#define IFX_ERAY_CREL_REL_MSK (0xfu)

/** \brief  Offset for Ifx_ERAY_CREL_Bits.REL */
#define IFX_ERAY_CREL_REL_OFF (28u)

/** \brief  Length for Ifx_ERAY_CREL_Bits.STEP */
#define IFX_ERAY_CREL_STEP_LEN (4u)

/** \brief  Mask for Ifx_ERAY_CREL_Bits.STEP */
#define IFX_ERAY_CREL_STEP_MSK (0xfu)

/** \brief  Offset for Ifx_ERAY_CREL_Bits.STEP */
#define IFX_ERAY_CREL_STEP_OFF (24u)

/** \brief  Length for Ifx_ERAY_CREL_Bits.SUBSTEP */
#define IFX_ERAY_CREL_SUBSTEP_LEN (4u)

/** \brief  Mask for Ifx_ERAY_CREL_Bits.SUBSTEP */
#define IFX_ERAY_CREL_SUBSTEP_MSK (0xfu)

/** \brief  Offset for Ifx_ERAY_CREL_Bits.SUBSTEP */
#define IFX_ERAY_CREL_SUBSTEP_OFF (20u)

/** \brief  Length for Ifx_ERAY_CREL_Bits.YEAR */
#define IFX_ERAY_CREL_YEAR_LEN (4u)

/** \brief  Mask for Ifx_ERAY_CREL_Bits.YEAR */
#define IFX_ERAY_CREL_YEAR_MSK (0xfu)

/** \brief  Offset for Ifx_ERAY_CREL_Bits.YEAR */
#define IFX_ERAY_CREL_YEAR_OFF (16u)

/** \brief  Length for Ifx_ERAY_CUST1_Bits.IBF1PAG */
#define IFX_ERAY_CUST1_IBF1PAG_LEN (1u)

/** \brief  Mask for Ifx_ERAY_CUST1_Bits.IBF1PAG */
#define IFX_ERAY_CUST1_IBF1PAG_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_CUST1_Bits.IBF1PAG */
#define IFX_ERAY_CUST1_IBF1PAG_OFF (4u)

/** \brief  Length for Ifx_ERAY_CUST1_Bits.IBF2PAG */
#define IFX_ERAY_CUST1_IBF2PAG_LEN (1u)

/** \brief  Mask for Ifx_ERAY_CUST1_Bits.IBF2PAG */
#define IFX_ERAY_CUST1_IBF2PAG_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_CUST1_Bits.IBF2PAG */
#define IFX_ERAY_CUST1_IBF2PAG_OFF (7u)

/** \brief  Length for Ifx_ERAY_CUST1_Bits.IBFS */
#define IFX_ERAY_CUST1_IBFS_LEN (1u)

/** \brief  Mask for Ifx_ERAY_CUST1_Bits.IBFS */
#define IFX_ERAY_CUST1_IBFS_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_CUST1_Bits.IBFS */
#define IFX_ERAY_CUST1_IBFS_OFF (3u)

/** \brief  Length for Ifx_ERAY_CUST1_Bits.IEN */
#define IFX_ERAY_CUST1_IEN_LEN (1u)

/** \brief  Mask for Ifx_ERAY_CUST1_Bits.IEN */
#define IFX_ERAY_CUST1_IEN_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_CUST1_Bits.IEN */
#define IFX_ERAY_CUST1_IEN_OFF (2u)

/** \brief  Length for Ifx_ERAY_CUST1_Bits.INT0 */
#define IFX_ERAY_CUST1_INT0_LEN (1u)

/** \brief  Mask for Ifx_ERAY_CUST1_Bits.INT0 */
#define IFX_ERAY_CUST1_INT0_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_CUST1_Bits.INT0 */
#define IFX_ERAY_CUST1_INT0_OFF (0u)

/** \brief  Length for Ifx_ERAY_CUST1_Bits.OEN */
#define IFX_ERAY_CUST1_OEN_LEN (1u)

/** \brief  Mask for Ifx_ERAY_CUST1_Bits.OEN */
#define IFX_ERAY_CUST1_OEN_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_CUST1_Bits.OEN */
#define IFX_ERAY_CUST1_OEN_OFF (1u)

/** \brief  Length for Ifx_ERAY_CUST1_Bits.RISA */
#define IFX_ERAY_CUST1_RISA_LEN (2u)

/** \brief  Mask for Ifx_ERAY_CUST1_Bits.RISA */
#define IFX_ERAY_CUST1_RISA_MSK (0x3u)

/** \brief  Offset for Ifx_ERAY_CUST1_Bits.RISA */
#define IFX_ERAY_CUST1_RISA_OFF (10u)

/** \brief  Length for Ifx_ERAY_CUST1_Bits.RISB */
#define IFX_ERAY_CUST1_RISB_LEN (2u)

/** \brief  Mask for Ifx_ERAY_CUST1_Bits.RISB */
#define IFX_ERAY_CUST1_RISB_MSK (0x3u)

/** \brief  Offset for Ifx_ERAY_CUST1_Bits.RISB */
#define IFX_ERAY_CUST1_RISB_OFF (12u)

/** \brief  Length for Ifx_ERAY_CUST1_Bits.STPWTS */
#define IFX_ERAY_CUST1_STPWTS_LEN (2u)

/** \brief  Mask for Ifx_ERAY_CUST1_Bits.STPWTS */
#define IFX_ERAY_CUST1_STPWTS_MSK (0x3u)

/** \brief  Offset for Ifx_ERAY_CUST1_Bits.STPWTS */
#define IFX_ERAY_CUST1_STPWTS_OFF (14u)

/** \brief  Length for Ifx_ERAY_CUST3_Bits.TO */
#define IFX_ERAY_CUST3_TO_LEN (32u)

/** \brief  Mask for Ifx_ERAY_CUST3_Bits.TO */
#define IFX_ERAY_CUST3_TO_MSK (0xffffffffu)

/** \brief  Offset for Ifx_ERAY_CUST3_Bits.TO */
#define IFX_ERAY_CUST3_TO_OFF (0u)

/** \brief  Length for Ifx_ERAY_EIER_Bits.CCFE */
#define IFX_ERAY_EIER_CCFE_LEN (1u)

/** \brief  Mask for Ifx_ERAY_EIER_Bits.CCFE */
#define IFX_ERAY_EIER_CCFE_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_EIER_Bits.CCFE */
#define IFX_ERAY_EIER_CCFE_OFF (4u)

/** \brief  Length for Ifx_ERAY_EIER_Bits.CCLE */
#define IFX_ERAY_EIER_CCLE_LEN (1u)

/** \brief  Mask for Ifx_ERAY_EIER_Bits.CCLE */
#define IFX_ERAY_EIER_CCLE_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_EIER_Bits.CCLE */
#define IFX_ERAY_EIER_CCLE_OFF (5u)

/** \brief  Length for Ifx_ERAY_EIER_Bits.CNAE */
#define IFX_ERAY_EIER_CNAE_LEN (1u)

/** \brief  Mask for Ifx_ERAY_EIER_Bits.CNAE */
#define IFX_ERAY_EIER_CNAE_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_EIER_Bits.CNAE */
#define IFX_ERAY_EIER_CNAE_OFF (1u)

/** \brief  Length for Ifx_ERAY_EIER_Bits.EDAE */
#define IFX_ERAY_EIER_EDAE_LEN (1u)

/** \brief  Mask for Ifx_ERAY_EIER_Bits.EDAE */
#define IFX_ERAY_EIER_EDAE_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_EIER_Bits.EDAE */
#define IFX_ERAY_EIER_EDAE_OFF (16u)

/** \brief  Length for Ifx_ERAY_EIER_Bits.EDBE */
#define IFX_ERAY_EIER_EDBE_LEN (1u)

/** \brief  Mask for Ifx_ERAY_EIER_Bits.EDBE */
#define IFX_ERAY_EIER_EDBE_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_EIER_Bits.EDBE */
#define IFX_ERAY_EIER_EDBE_OFF (24u)

/** \brief  Length for Ifx_ERAY_EIER_Bits.EERRE */
#define IFX_ERAY_EIER_EERRE_LEN (1u)

/** \brief  Mask for Ifx_ERAY_EIER_Bits.EERRE */
#define IFX_ERAY_EIER_EERRE_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_EIER_Bits.EERRE */
#define IFX_ERAY_EIER_EERRE_OFF (6u)

/** \brief  Length for Ifx_ERAY_EIER_Bits.EFAE */
#define IFX_ERAY_EIER_EFAE_LEN (1u)

/** \brief  Mask for Ifx_ERAY_EIER_Bits.EFAE */
#define IFX_ERAY_EIER_EFAE_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_EIER_Bits.EFAE */
#define IFX_ERAY_EIER_EFAE_OFF (8u)

/** \brief  Length for Ifx_ERAY_EIER_Bits.IIBAE */
#define IFX_ERAY_EIER_IIBAE_LEN (1u)

/** \brief  Mask for Ifx_ERAY_EIER_Bits.IIBAE */
#define IFX_ERAY_EIER_IIBAE_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_EIER_Bits.IIBAE */
#define IFX_ERAY_EIER_IIBAE_OFF (9u)

/** \brief  Length for Ifx_ERAY_EIER_Bits.IOBAE */
#define IFX_ERAY_EIER_IOBAE_LEN (1u)

/** \brief  Mask for Ifx_ERAY_EIER_Bits.IOBAE */
#define IFX_ERAY_EIER_IOBAE_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_EIER_Bits.IOBAE */
#define IFX_ERAY_EIER_IOBAE_OFF (10u)

/** \brief  Length for Ifx_ERAY_EIER_Bits.LTVAE */
#define IFX_ERAY_EIER_LTVAE_LEN (1u)

/** \brief  Mask for Ifx_ERAY_EIER_Bits.LTVAE */
#define IFX_ERAY_EIER_LTVAE_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_EIER_Bits.LTVAE */
#define IFX_ERAY_EIER_LTVAE_OFF (17u)

/** \brief  Length for Ifx_ERAY_EIER_Bits.LTVBE */
#define IFX_ERAY_EIER_LTVBE_LEN (1u)

/** \brief  Mask for Ifx_ERAY_EIER_Bits.LTVBE */
#define IFX_ERAY_EIER_LTVBE_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_EIER_Bits.LTVBE */
#define IFX_ERAY_EIER_LTVBE_OFF (25u)

/** \brief  Length for Ifx_ERAY_EIER_Bits.MHFE */
#define IFX_ERAY_EIER_MHFE_LEN (1u)

/** \brief  Mask for Ifx_ERAY_EIER_Bits.MHFE */
#define IFX_ERAY_EIER_MHFE_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_EIER_Bits.MHFE */
#define IFX_ERAY_EIER_MHFE_OFF (11u)

/** \brief  Length for Ifx_ERAY_EIER_Bits.PEMCE */
#define IFX_ERAY_EIER_PEMCE_LEN (1u)

/** \brief  Mask for Ifx_ERAY_EIER_Bits.PEMCE */
#define IFX_ERAY_EIER_PEMCE_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_EIER_Bits.PEMCE */
#define IFX_ERAY_EIER_PEMCE_OFF (0u)

/** \brief  Length for Ifx_ERAY_EIER_Bits.RFOE */
#define IFX_ERAY_EIER_RFOE_LEN (1u)

/** \brief  Mask for Ifx_ERAY_EIER_Bits.RFOE */
#define IFX_ERAY_EIER_RFOE_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_EIER_Bits.RFOE */
#define IFX_ERAY_EIER_RFOE_OFF (7u)

/** \brief  Length for Ifx_ERAY_EIER_Bits.SFBME */
#define IFX_ERAY_EIER_SFBME_LEN (1u)

/** \brief  Mask for Ifx_ERAY_EIER_Bits.SFBME */
#define IFX_ERAY_EIER_SFBME_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_EIER_Bits.SFBME */
#define IFX_ERAY_EIER_SFBME_OFF (2u)

/** \brief  Length for Ifx_ERAY_EIER_Bits.SFOE */
#define IFX_ERAY_EIER_SFOE_LEN (1u)

/** \brief  Mask for Ifx_ERAY_EIER_Bits.SFOE */
#define IFX_ERAY_EIER_SFOE_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_EIER_Bits.SFOE */
#define IFX_ERAY_EIER_SFOE_OFF (3u)

/** \brief  Length for Ifx_ERAY_EIER_Bits.TABAE */
#define IFX_ERAY_EIER_TABAE_LEN (1u)

/** \brief  Mask for Ifx_ERAY_EIER_Bits.TABAE */
#define IFX_ERAY_EIER_TABAE_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_EIER_Bits.TABAE */
#define IFX_ERAY_EIER_TABAE_OFF (18u)

/** \brief  Length for Ifx_ERAY_EIER_Bits.TABBE */
#define IFX_ERAY_EIER_TABBE_LEN (1u)

/** \brief  Mask for Ifx_ERAY_EIER_Bits.TABBE */
#define IFX_ERAY_EIER_TABBE_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_EIER_Bits.TABBE */
#define IFX_ERAY_EIER_TABBE_OFF (26u)

/** \brief  Length for Ifx_ERAY_EIES_Bits.CCFE */
#define IFX_ERAY_EIES_CCFE_LEN (1u)

/** \brief  Mask for Ifx_ERAY_EIES_Bits.CCFE */
#define IFX_ERAY_EIES_CCFE_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_EIES_Bits.CCFE */
#define IFX_ERAY_EIES_CCFE_OFF (4u)

/** \brief  Length for Ifx_ERAY_EIES_Bits.CCLE */
#define IFX_ERAY_EIES_CCLE_LEN (1u)

/** \brief  Mask for Ifx_ERAY_EIES_Bits.CCLE */
#define IFX_ERAY_EIES_CCLE_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_EIES_Bits.CCLE */
#define IFX_ERAY_EIES_CCLE_OFF (5u)

/** \brief  Length for Ifx_ERAY_EIES_Bits.CNAE */
#define IFX_ERAY_EIES_CNAE_LEN (1u)

/** \brief  Mask for Ifx_ERAY_EIES_Bits.CNAE */
#define IFX_ERAY_EIES_CNAE_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_EIES_Bits.CNAE */
#define IFX_ERAY_EIES_CNAE_OFF (1u)

/** \brief  Length for Ifx_ERAY_EIES_Bits.EDAE */
#define IFX_ERAY_EIES_EDAE_LEN (1u)

/** \brief  Mask for Ifx_ERAY_EIES_Bits.EDAE */
#define IFX_ERAY_EIES_EDAE_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_EIES_Bits.EDAE */
#define IFX_ERAY_EIES_EDAE_OFF (16u)

/** \brief  Length for Ifx_ERAY_EIES_Bits.EDBE */
#define IFX_ERAY_EIES_EDBE_LEN (1u)

/** \brief  Mask for Ifx_ERAY_EIES_Bits.EDBE */
#define IFX_ERAY_EIES_EDBE_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_EIES_Bits.EDBE */
#define IFX_ERAY_EIES_EDBE_OFF (24u)

/** \brief  Length for Ifx_ERAY_EIES_Bits.EERRE */
#define IFX_ERAY_EIES_EERRE_LEN (1u)

/** \brief  Mask for Ifx_ERAY_EIES_Bits.EERRE */
#define IFX_ERAY_EIES_EERRE_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_EIES_Bits.EERRE */
#define IFX_ERAY_EIES_EERRE_OFF (6u)

/** \brief  Length for Ifx_ERAY_EIES_Bits.EFAE */
#define IFX_ERAY_EIES_EFAE_LEN (1u)

/** \brief  Mask for Ifx_ERAY_EIES_Bits.EFAE */
#define IFX_ERAY_EIES_EFAE_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_EIES_Bits.EFAE */
#define IFX_ERAY_EIES_EFAE_OFF (8u)

/** \brief  Length for Ifx_ERAY_EIES_Bits.IIBAE */
#define IFX_ERAY_EIES_IIBAE_LEN (1u)

/** \brief  Mask for Ifx_ERAY_EIES_Bits.IIBAE */
#define IFX_ERAY_EIES_IIBAE_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_EIES_Bits.IIBAE */
#define IFX_ERAY_EIES_IIBAE_OFF (9u)

/** \brief  Length for Ifx_ERAY_EIES_Bits.IOBAE */
#define IFX_ERAY_EIES_IOBAE_LEN (1u)

/** \brief  Mask for Ifx_ERAY_EIES_Bits.IOBAE */
#define IFX_ERAY_EIES_IOBAE_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_EIES_Bits.IOBAE */
#define IFX_ERAY_EIES_IOBAE_OFF (10u)

/** \brief  Length for Ifx_ERAY_EIES_Bits.LTVAE */
#define IFX_ERAY_EIES_LTVAE_LEN (1u)

/** \brief  Mask for Ifx_ERAY_EIES_Bits.LTVAE */
#define IFX_ERAY_EIES_LTVAE_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_EIES_Bits.LTVAE */
#define IFX_ERAY_EIES_LTVAE_OFF (17u)

/** \brief  Length for Ifx_ERAY_EIES_Bits.LTVBE */
#define IFX_ERAY_EIES_LTVBE_LEN (1u)

/** \brief  Mask for Ifx_ERAY_EIES_Bits.LTVBE */
#define IFX_ERAY_EIES_LTVBE_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_EIES_Bits.LTVBE */
#define IFX_ERAY_EIES_LTVBE_OFF (25u)

/** \brief  Length for Ifx_ERAY_EIES_Bits.MHFE */
#define IFX_ERAY_EIES_MHFE_LEN (1u)

/** \brief  Mask for Ifx_ERAY_EIES_Bits.MHFE */
#define IFX_ERAY_EIES_MHFE_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_EIES_Bits.MHFE */
#define IFX_ERAY_EIES_MHFE_OFF (11u)

/** \brief  Length for Ifx_ERAY_EIES_Bits.PEMCE */
#define IFX_ERAY_EIES_PEMCE_LEN (1u)

/** \brief  Mask for Ifx_ERAY_EIES_Bits.PEMCE */
#define IFX_ERAY_EIES_PEMCE_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_EIES_Bits.PEMCE */
#define IFX_ERAY_EIES_PEMCE_OFF (0u)

/** \brief  Length for Ifx_ERAY_EIES_Bits.RFOE */
#define IFX_ERAY_EIES_RFOE_LEN (1u)

/** \brief  Mask for Ifx_ERAY_EIES_Bits.RFOE */
#define IFX_ERAY_EIES_RFOE_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_EIES_Bits.RFOE */
#define IFX_ERAY_EIES_RFOE_OFF (7u)

/** \brief  Length for Ifx_ERAY_EIES_Bits.SFBME */
#define IFX_ERAY_EIES_SFBME_LEN (1u)

/** \brief  Mask for Ifx_ERAY_EIES_Bits.SFBME */
#define IFX_ERAY_EIES_SFBME_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_EIES_Bits.SFBME */
#define IFX_ERAY_EIES_SFBME_OFF (2u)

/** \brief  Length for Ifx_ERAY_EIES_Bits.SFOE */
#define IFX_ERAY_EIES_SFOE_LEN (1u)

/** \brief  Mask for Ifx_ERAY_EIES_Bits.SFOE */
#define IFX_ERAY_EIES_SFOE_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_EIES_Bits.SFOE */
#define IFX_ERAY_EIES_SFOE_OFF (3u)

/** \brief  Length for Ifx_ERAY_EIES_Bits.TABAE */
#define IFX_ERAY_EIES_TABAE_LEN (1u)

/** \brief  Mask for Ifx_ERAY_EIES_Bits.TABAE */
#define IFX_ERAY_EIES_TABAE_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_EIES_Bits.TABAE */
#define IFX_ERAY_EIES_TABAE_OFF (18u)

/** \brief  Length for Ifx_ERAY_EIES_Bits.TABBE */
#define IFX_ERAY_EIES_TABBE_LEN (1u)

/** \brief  Mask for Ifx_ERAY_EIES_Bits.TABBE */
#define IFX_ERAY_EIES_TABBE_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_EIES_Bits.TABBE */
#define IFX_ERAY_EIES_TABBE_OFF (26u)

/** \brief  Length for Ifx_ERAY_EILS_Bits.CCFL */
#define IFX_ERAY_EILS_CCFL_LEN (1u)

/** \brief  Mask for Ifx_ERAY_EILS_Bits.CCFL */
#define IFX_ERAY_EILS_CCFL_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_EILS_Bits.CCFL */
#define IFX_ERAY_EILS_CCFL_OFF (4u)

/** \brief  Length for Ifx_ERAY_EILS_Bits.CCLL */
#define IFX_ERAY_EILS_CCLL_LEN (1u)

/** \brief  Mask for Ifx_ERAY_EILS_Bits.CCLL */
#define IFX_ERAY_EILS_CCLL_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_EILS_Bits.CCLL */
#define IFX_ERAY_EILS_CCLL_OFF (5u)

/** \brief  Length for Ifx_ERAY_EILS_Bits.CNAL */
#define IFX_ERAY_EILS_CNAL_LEN (1u)

/** \brief  Mask for Ifx_ERAY_EILS_Bits.CNAL */
#define IFX_ERAY_EILS_CNAL_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_EILS_Bits.CNAL */
#define IFX_ERAY_EILS_CNAL_OFF (1u)

/** \brief  Length for Ifx_ERAY_EILS_Bits.EDAL */
#define IFX_ERAY_EILS_EDAL_LEN (1u)

/** \brief  Mask for Ifx_ERAY_EILS_Bits.EDAL */
#define IFX_ERAY_EILS_EDAL_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_EILS_Bits.EDAL */
#define IFX_ERAY_EILS_EDAL_OFF (16u)

/** \brief  Length for Ifx_ERAY_EILS_Bits.EDBL */
#define IFX_ERAY_EILS_EDBL_LEN (1u)

/** \brief  Mask for Ifx_ERAY_EILS_Bits.EDBL */
#define IFX_ERAY_EILS_EDBL_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_EILS_Bits.EDBL */
#define IFX_ERAY_EILS_EDBL_OFF (24u)

/** \brief  Length for Ifx_ERAY_EILS_Bits.EERRL */
#define IFX_ERAY_EILS_EERRL_LEN (1u)

/** \brief  Mask for Ifx_ERAY_EILS_Bits.EERRL */
#define IFX_ERAY_EILS_EERRL_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_EILS_Bits.EERRL */
#define IFX_ERAY_EILS_EERRL_OFF (6u)

/** \brief  Length for Ifx_ERAY_EILS_Bits.EFAL */
#define IFX_ERAY_EILS_EFAL_LEN (1u)

/** \brief  Mask for Ifx_ERAY_EILS_Bits.EFAL */
#define IFX_ERAY_EILS_EFAL_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_EILS_Bits.EFAL */
#define IFX_ERAY_EILS_EFAL_OFF (8u)

/** \brief  Length for Ifx_ERAY_EILS_Bits.IIBAL */
#define IFX_ERAY_EILS_IIBAL_LEN (1u)

/** \brief  Mask for Ifx_ERAY_EILS_Bits.IIBAL */
#define IFX_ERAY_EILS_IIBAL_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_EILS_Bits.IIBAL */
#define IFX_ERAY_EILS_IIBAL_OFF (9u)

/** \brief  Length for Ifx_ERAY_EILS_Bits.IOBAL */
#define IFX_ERAY_EILS_IOBAL_LEN (1u)

/** \brief  Mask for Ifx_ERAY_EILS_Bits.IOBAL */
#define IFX_ERAY_EILS_IOBAL_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_EILS_Bits.IOBAL */
#define IFX_ERAY_EILS_IOBAL_OFF (10u)

/** \brief  Length for Ifx_ERAY_EILS_Bits.LTVAL */
#define IFX_ERAY_EILS_LTVAL_LEN (1u)

/** \brief  Mask for Ifx_ERAY_EILS_Bits.LTVAL */
#define IFX_ERAY_EILS_LTVAL_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_EILS_Bits.LTVAL */
#define IFX_ERAY_EILS_LTVAL_OFF (17u)

/** \brief  Length for Ifx_ERAY_EILS_Bits.LTVBL */
#define IFX_ERAY_EILS_LTVBL_LEN (1u)

/** \brief  Mask for Ifx_ERAY_EILS_Bits.LTVBL */
#define IFX_ERAY_EILS_LTVBL_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_EILS_Bits.LTVBL */
#define IFX_ERAY_EILS_LTVBL_OFF (25u)

/** \brief  Length for Ifx_ERAY_EILS_Bits.MHFL */
#define IFX_ERAY_EILS_MHFL_LEN (1u)

/** \brief  Mask for Ifx_ERAY_EILS_Bits.MHFL */
#define IFX_ERAY_EILS_MHFL_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_EILS_Bits.MHFL */
#define IFX_ERAY_EILS_MHFL_OFF (11u)

/** \brief  Length for Ifx_ERAY_EILS_Bits.PEMCL */
#define IFX_ERAY_EILS_PEMCL_LEN (1u)

/** \brief  Mask for Ifx_ERAY_EILS_Bits.PEMCL */
#define IFX_ERAY_EILS_PEMCL_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_EILS_Bits.PEMCL */
#define IFX_ERAY_EILS_PEMCL_OFF (0u)

/** \brief  Length for Ifx_ERAY_EILS_Bits.RFOL */
#define IFX_ERAY_EILS_RFOL_LEN (1u)

/** \brief  Mask for Ifx_ERAY_EILS_Bits.RFOL */
#define IFX_ERAY_EILS_RFOL_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_EILS_Bits.RFOL */
#define IFX_ERAY_EILS_RFOL_OFF (7u)

/** \brief  Length for Ifx_ERAY_EILS_Bits.SFBML */
#define IFX_ERAY_EILS_SFBML_LEN (1u)

/** \brief  Mask for Ifx_ERAY_EILS_Bits.SFBML */
#define IFX_ERAY_EILS_SFBML_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_EILS_Bits.SFBML */
#define IFX_ERAY_EILS_SFBML_OFF (2u)

/** \brief  Length for Ifx_ERAY_EILS_Bits.SFOL */
#define IFX_ERAY_EILS_SFOL_LEN (1u)

/** \brief  Mask for Ifx_ERAY_EILS_Bits.SFOL */
#define IFX_ERAY_EILS_SFOL_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_EILS_Bits.SFOL */
#define IFX_ERAY_EILS_SFOL_OFF (3u)

/** \brief  Length for Ifx_ERAY_EILS_Bits.TABAL */
#define IFX_ERAY_EILS_TABAL_LEN (1u)

/** \brief  Mask for Ifx_ERAY_EILS_Bits.TABAL */
#define IFX_ERAY_EILS_TABAL_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_EILS_Bits.TABAL */
#define IFX_ERAY_EILS_TABAL_OFF (18u)

/** \brief  Length for Ifx_ERAY_EILS_Bits.TABBL */
#define IFX_ERAY_EILS_TABBL_LEN (1u)

/** \brief  Mask for Ifx_ERAY_EILS_Bits.TABBL */
#define IFX_ERAY_EILS_TABBL_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_EILS_Bits.TABBL */
#define IFX_ERAY_EILS_TABBL_OFF (26u)

/** \brief  Length for Ifx_ERAY_EIR_Bits.CCF */
#define IFX_ERAY_EIR_CCF_LEN (1u)

/** \brief  Mask for Ifx_ERAY_EIR_Bits.CCF */
#define IFX_ERAY_EIR_CCF_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_EIR_Bits.CCF */
#define IFX_ERAY_EIR_CCF_OFF (4u)

/** \brief  Length for Ifx_ERAY_EIR_Bits.CCL */
#define IFX_ERAY_EIR_CCL_LEN (1u)

/** \brief  Mask for Ifx_ERAY_EIR_Bits.CCL */
#define IFX_ERAY_EIR_CCL_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_EIR_Bits.CCL */
#define IFX_ERAY_EIR_CCL_OFF (5u)

/** \brief  Length for Ifx_ERAY_EIR_Bits.CNA */
#define IFX_ERAY_EIR_CNA_LEN (1u)

/** \brief  Mask for Ifx_ERAY_EIR_Bits.CNA */
#define IFX_ERAY_EIR_CNA_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_EIR_Bits.CNA */
#define IFX_ERAY_EIR_CNA_OFF (1u)

/** \brief  Length for Ifx_ERAY_EIR_Bits.EDA */
#define IFX_ERAY_EIR_EDA_LEN (1u)

/** \brief  Mask for Ifx_ERAY_EIR_Bits.EDA */
#define IFX_ERAY_EIR_EDA_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_EIR_Bits.EDA */
#define IFX_ERAY_EIR_EDA_OFF (16u)

/** \brief  Length for Ifx_ERAY_EIR_Bits.EDB */
#define IFX_ERAY_EIR_EDB_LEN (1u)

/** \brief  Mask for Ifx_ERAY_EIR_Bits.EDB */
#define IFX_ERAY_EIR_EDB_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_EIR_Bits.EDB */
#define IFX_ERAY_EIR_EDB_OFF (24u)

/** \brief  Length for Ifx_ERAY_EIR_Bits.EERR */
#define IFX_ERAY_EIR_EERR_LEN (1u)

/** \brief  Mask for Ifx_ERAY_EIR_Bits.EERR */
#define IFX_ERAY_EIR_EERR_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_EIR_Bits.EERR */
#define IFX_ERAY_EIR_EERR_OFF (6u)

/** \brief  Length for Ifx_ERAY_EIR_Bits.EFA */
#define IFX_ERAY_EIR_EFA_LEN (1u)

/** \brief  Mask for Ifx_ERAY_EIR_Bits.EFA */
#define IFX_ERAY_EIR_EFA_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_EIR_Bits.EFA */
#define IFX_ERAY_EIR_EFA_OFF (8u)

/** \brief  Length for Ifx_ERAY_EIR_Bits.IIBA */
#define IFX_ERAY_EIR_IIBA_LEN (1u)

/** \brief  Mask for Ifx_ERAY_EIR_Bits.IIBA */
#define IFX_ERAY_EIR_IIBA_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_EIR_Bits.IIBA */
#define IFX_ERAY_EIR_IIBA_OFF (9u)

/** \brief  Length for Ifx_ERAY_EIR_Bits.IOBA */
#define IFX_ERAY_EIR_IOBA_LEN (1u)

/** \brief  Mask for Ifx_ERAY_EIR_Bits.IOBA */
#define IFX_ERAY_EIR_IOBA_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_EIR_Bits.IOBA */
#define IFX_ERAY_EIR_IOBA_OFF (10u)

/** \brief  Length for Ifx_ERAY_EIR_Bits.LTVA */
#define IFX_ERAY_EIR_LTVA_LEN (1u)

/** \brief  Mask for Ifx_ERAY_EIR_Bits.LTVA */
#define IFX_ERAY_EIR_LTVA_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_EIR_Bits.LTVA */
#define IFX_ERAY_EIR_LTVA_OFF (17u)

/** \brief  Length for Ifx_ERAY_EIR_Bits.LTVB */
#define IFX_ERAY_EIR_LTVB_LEN (1u)

/** \brief  Mask for Ifx_ERAY_EIR_Bits.LTVB */
#define IFX_ERAY_EIR_LTVB_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_EIR_Bits.LTVB */
#define IFX_ERAY_EIR_LTVB_OFF (25u)

/** \brief  Length for Ifx_ERAY_EIR_Bits.MHF */
#define IFX_ERAY_EIR_MHF_LEN (1u)

/** \brief  Mask for Ifx_ERAY_EIR_Bits.MHF */
#define IFX_ERAY_EIR_MHF_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_EIR_Bits.MHF */
#define IFX_ERAY_EIR_MHF_OFF (11u)

/** \brief  Length for Ifx_ERAY_EIR_Bits.PEMC */
#define IFX_ERAY_EIR_PEMC_LEN (1u)

/** \brief  Mask for Ifx_ERAY_EIR_Bits.PEMC */
#define IFX_ERAY_EIR_PEMC_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_EIR_Bits.PEMC */
#define IFX_ERAY_EIR_PEMC_OFF (0u)

/** \brief  Length for Ifx_ERAY_EIR_Bits.RFO */
#define IFX_ERAY_EIR_RFO_LEN (1u)

/** \brief  Mask for Ifx_ERAY_EIR_Bits.RFO */
#define IFX_ERAY_EIR_RFO_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_EIR_Bits.RFO */
#define IFX_ERAY_EIR_RFO_OFF (7u)

/** \brief  Length for Ifx_ERAY_EIR_Bits.SFBM */
#define IFX_ERAY_EIR_SFBM_LEN (1u)

/** \brief  Mask for Ifx_ERAY_EIR_Bits.SFBM */
#define IFX_ERAY_EIR_SFBM_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_EIR_Bits.SFBM */
#define IFX_ERAY_EIR_SFBM_OFF (2u)

/** \brief  Length for Ifx_ERAY_EIR_Bits.SFO */
#define IFX_ERAY_EIR_SFO_LEN (1u)

/** \brief  Mask for Ifx_ERAY_EIR_Bits.SFO */
#define IFX_ERAY_EIR_SFO_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_EIR_Bits.SFO */
#define IFX_ERAY_EIR_SFO_OFF (3u)

/** \brief  Length for Ifx_ERAY_EIR_Bits.TABA */
#define IFX_ERAY_EIR_TABA_LEN (1u)

/** \brief  Mask for Ifx_ERAY_EIR_Bits.TABA */
#define IFX_ERAY_EIR_TABA_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_EIR_Bits.TABA */
#define IFX_ERAY_EIR_TABA_OFF (18u)

/** \brief  Length for Ifx_ERAY_EIR_Bits.TABB */
#define IFX_ERAY_EIR_TABB_LEN (1u)

/** \brief  Mask for Ifx_ERAY_EIR_Bits.TABB */
#define IFX_ERAY_EIR_TABB_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_EIR_Bits.TABB */
#define IFX_ERAY_EIR_TABB_OFF (26u)

/** \brief  Length for Ifx_ERAY_ENDN_Bits.ETV */
#define IFX_ERAY_ENDN_ETV_LEN (32u)

/** \brief  Mask for Ifx_ERAY_ENDN_Bits.ETV */
#define IFX_ERAY_ENDN_ETV_MSK (0xffffffffu)

/** \brief  Offset for Ifx_ERAY_ENDN_Bits.ETV */
#define IFX_ERAY_ENDN_ETV_OFF (0u)

/** \brief  Length for Ifx_ERAY_ESID_Bits.EID */
#define IFX_ERAY_ESID_EID_LEN (10u)

/** \brief  Mask for Ifx_ERAY_ESID_Bits.EID */
#define IFX_ERAY_ESID_EID_MSK (0x3ffu)

/** \brief  Offset for Ifx_ERAY_ESID_Bits.EID */
#define IFX_ERAY_ESID_EID_OFF (0u)

/** \brief  Length for Ifx_ERAY_ESID_Bits.RXEA */
#define IFX_ERAY_ESID_RXEA_LEN (1u)

/** \brief  Mask for Ifx_ERAY_ESID_Bits.RXEA */
#define IFX_ERAY_ESID_RXEA_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_ESID_Bits.RXEA */
#define IFX_ERAY_ESID_RXEA_OFF (14u)

/** \brief  Length for Ifx_ERAY_ESID_Bits.RXEB */
#define IFX_ERAY_ESID_RXEB_LEN (1u)

/** \brief  Mask for Ifx_ERAY_ESID_Bits.RXEB */
#define IFX_ERAY_ESID_RXEB_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_ESID_Bits.RXEB */
#define IFX_ERAY_ESID_RXEB_OFF (15u)

/** \brief  Length for Ifx_ERAY_FCL_Bits.CL */
#define IFX_ERAY_FCL_CL_LEN (8u)

/** \brief  Mask for Ifx_ERAY_FCL_Bits.CL */
#define IFX_ERAY_FCL_CL_MSK (0xffu)

/** \brief  Offset for Ifx_ERAY_FCL_Bits.CL */
#define IFX_ERAY_FCL_CL_OFF (0u)

/** \brief  Length for Ifx_ERAY_FRF_Bits.CH */
#define IFX_ERAY_FRF_CH_LEN (2u)

/** \brief  Mask for Ifx_ERAY_FRF_Bits.CH */
#define IFX_ERAY_FRF_CH_MSK (0x3u)

/** \brief  Offset for Ifx_ERAY_FRF_Bits.CH */
#define IFX_ERAY_FRF_CH_OFF (0u)

/** \brief  Length for Ifx_ERAY_FRF_Bits.CYF */
#define IFX_ERAY_FRF_CYF_LEN (7u)

/** \brief  Mask for Ifx_ERAY_FRF_Bits.CYF */
#define IFX_ERAY_FRF_CYF_MSK (0x7fu)

/** \brief  Offset for Ifx_ERAY_FRF_Bits.CYF */
#define IFX_ERAY_FRF_CYF_OFF (16u)

/** \brief  Length for Ifx_ERAY_FRF_Bits.FID */
#define IFX_ERAY_FRF_FID_LEN (11u)

/** \brief  Mask for Ifx_ERAY_FRF_Bits.FID */
#define IFX_ERAY_FRF_FID_MSK (0x7ffu)

/** \brief  Offset for Ifx_ERAY_FRF_Bits.FID */
#define IFX_ERAY_FRF_FID_OFF (2u)

/** \brief  Length for Ifx_ERAY_FRF_Bits.RNF */
#define IFX_ERAY_FRF_RNF_LEN (1u)

/** \brief  Mask for Ifx_ERAY_FRF_Bits.RNF */
#define IFX_ERAY_FRF_RNF_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_FRF_Bits.RNF */
#define IFX_ERAY_FRF_RNF_OFF (24u)

/** \brief  Length for Ifx_ERAY_FRF_Bits.RSS */
#define IFX_ERAY_FRF_RSS_LEN (1u)

/** \brief  Mask for Ifx_ERAY_FRF_Bits.RSS */
#define IFX_ERAY_FRF_RSS_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_FRF_Bits.RSS */
#define IFX_ERAY_FRF_RSS_OFF (23u)

/** \brief  Length for Ifx_ERAY_FRFM_Bits.MFID */
#define IFX_ERAY_FRFM_MFID_LEN (11u)

/** \brief  Mask for Ifx_ERAY_FRFM_Bits.MFID */
#define IFX_ERAY_FRFM_MFID_MSK (0x7ffu)

/** \brief  Offset for Ifx_ERAY_FRFM_Bits.MFID */
#define IFX_ERAY_FRFM_MFID_OFF (2u)

/** \brief  Length for Ifx_ERAY_FSR_Bits.RFCL */
#define IFX_ERAY_FSR_RFCL_LEN (1u)

/** \brief  Mask for Ifx_ERAY_FSR_Bits.RFCL */
#define IFX_ERAY_FSR_RFCL_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_FSR_Bits.RFCL */
#define IFX_ERAY_FSR_RFCL_OFF (1u)

/** \brief  Length for Ifx_ERAY_FSR_Bits.RFFL */
#define IFX_ERAY_FSR_RFFL_LEN (8u)

/** \brief  Mask for Ifx_ERAY_FSR_Bits.RFFL */
#define IFX_ERAY_FSR_RFFL_MSK (0xffu)

/** \brief  Offset for Ifx_ERAY_FSR_Bits.RFFL */
#define IFX_ERAY_FSR_RFFL_OFF (8u)

/** \brief  Length for Ifx_ERAY_FSR_Bits.RFNE */
#define IFX_ERAY_FSR_RFNE_LEN (1u)

/** \brief  Mask for Ifx_ERAY_FSR_Bits.RFNE */
#define IFX_ERAY_FSR_RFNE_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_FSR_Bits.RFNE */
#define IFX_ERAY_FSR_RFNE_OFF (0u)

/** \brief  Length for Ifx_ERAY_FSR_Bits.RFO */
#define IFX_ERAY_FSR_RFO_LEN (1u)

/** \brief  Mask for Ifx_ERAY_FSR_Bits.RFO */
#define IFX_ERAY_FSR_RFO_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_FSR_Bits.RFO */
#define IFX_ERAY_FSR_RFO_OFF (2u)

/** \brief  Length for Ifx_ERAY_GTUC01_Bits.UT */
#define IFX_ERAY_GTUC01_UT_LEN (20u)

/** \brief  Mask for Ifx_ERAY_GTUC01_Bits.UT */
#define IFX_ERAY_GTUC01_UT_MSK (0xfffffu)

/** \brief  Offset for Ifx_ERAY_GTUC01_Bits.UT */
#define IFX_ERAY_GTUC01_UT_OFF (0u)

/** \brief  Length for Ifx_ERAY_GTUC02_Bits.MPC */
#define IFX_ERAY_GTUC02_MPC_LEN (14u)

/** \brief  Mask for Ifx_ERAY_GTUC02_Bits.MPC */
#define IFX_ERAY_GTUC02_MPC_MSK (0x3fffu)

/** \brief  Offset for Ifx_ERAY_GTUC02_Bits.MPC */
#define IFX_ERAY_GTUC02_MPC_OFF (0u)

/** \brief  Length for Ifx_ERAY_GTUC02_Bits.SNM */
#define IFX_ERAY_GTUC02_SNM_LEN (4u)

/** \brief  Mask for Ifx_ERAY_GTUC02_Bits.SNM */
#define IFX_ERAY_GTUC02_SNM_MSK (0xfu)

/** \brief  Offset for Ifx_ERAY_GTUC02_Bits.SNM */
#define IFX_ERAY_GTUC02_SNM_OFF (16u)

/** \brief  Length for Ifx_ERAY_GTUC03_Bits.MIOA */
#define IFX_ERAY_GTUC03_MIOA_LEN (7u)

/** \brief  Mask for Ifx_ERAY_GTUC03_Bits.MIOA */
#define IFX_ERAY_GTUC03_MIOA_MSK (0x7fu)

/** \brief  Offset for Ifx_ERAY_GTUC03_Bits.MIOA */
#define IFX_ERAY_GTUC03_MIOA_OFF (16u)

/** \brief  Length for Ifx_ERAY_GTUC03_Bits.MIOB */
#define IFX_ERAY_GTUC03_MIOB_LEN (7u)

/** \brief  Mask for Ifx_ERAY_GTUC03_Bits.MIOB */
#define IFX_ERAY_GTUC03_MIOB_MSK (0x7fu)

/** \brief  Offset for Ifx_ERAY_GTUC03_Bits.MIOB */
#define IFX_ERAY_GTUC03_MIOB_OFF (24u)

/** \brief  Length for Ifx_ERAY_GTUC03_Bits.UIOA */
#define IFX_ERAY_GTUC03_UIOA_LEN (8u)

/** \brief  Mask for Ifx_ERAY_GTUC03_Bits.UIOA */
#define IFX_ERAY_GTUC03_UIOA_MSK (0xffu)

/** \brief  Offset for Ifx_ERAY_GTUC03_Bits.UIOA */
#define IFX_ERAY_GTUC03_UIOA_OFF (0u)

/** \brief  Length for Ifx_ERAY_GTUC03_Bits.UIOB */
#define IFX_ERAY_GTUC03_UIOB_LEN (8u)

/** \brief  Mask for Ifx_ERAY_GTUC03_Bits.UIOB */
#define IFX_ERAY_GTUC03_UIOB_MSK (0xffu)

/** \brief  Offset for Ifx_ERAY_GTUC03_Bits.UIOB */
#define IFX_ERAY_GTUC03_UIOB_OFF (8u)

/** \brief  Length for Ifx_ERAY_GTUC04_Bits.NIT */
#define IFX_ERAY_GTUC04_NIT_LEN (14u)

/** \brief  Mask for Ifx_ERAY_GTUC04_Bits.NIT */
#define IFX_ERAY_GTUC04_NIT_MSK (0x3fffu)

/** \brief  Offset for Ifx_ERAY_GTUC04_Bits.NIT */
#define IFX_ERAY_GTUC04_NIT_OFF (0u)

/** \brief  Length for Ifx_ERAY_GTUC04_Bits.OCS */
#define IFX_ERAY_GTUC04_OCS_LEN (14u)

/** \brief  Mask for Ifx_ERAY_GTUC04_Bits.OCS */
#define IFX_ERAY_GTUC04_OCS_MSK (0x3fffu)

/** \brief  Offset for Ifx_ERAY_GTUC04_Bits.OCS */
#define IFX_ERAY_GTUC04_OCS_OFF (16u)

/** \brief  Length for Ifx_ERAY_GTUC05_Bits.CDD */
#define IFX_ERAY_GTUC05_CDD_LEN (5u)

/** \brief  Mask for Ifx_ERAY_GTUC05_Bits.CDD */
#define IFX_ERAY_GTUC05_CDD_MSK (0x1fu)

/** \brief  Offset for Ifx_ERAY_GTUC05_Bits.CDD */
#define IFX_ERAY_GTUC05_CDD_OFF (16u)

/** \brief  Length for Ifx_ERAY_GTUC05_Bits.DCA */
#define IFX_ERAY_GTUC05_DCA_LEN (8u)

/** \brief  Mask for Ifx_ERAY_GTUC05_Bits.DCA */
#define IFX_ERAY_GTUC05_DCA_MSK (0xffu)

/** \brief  Offset for Ifx_ERAY_GTUC05_Bits.DCA */
#define IFX_ERAY_GTUC05_DCA_OFF (0u)

/** \brief  Length for Ifx_ERAY_GTUC05_Bits.DCB */
#define IFX_ERAY_GTUC05_DCB_LEN (8u)

/** \brief  Mask for Ifx_ERAY_GTUC05_Bits.DCB */
#define IFX_ERAY_GTUC05_DCB_MSK (0xffu)

/** \brief  Offset for Ifx_ERAY_GTUC05_Bits.DCB */
#define IFX_ERAY_GTUC05_DCB_OFF (8u)

/** \brief  Length for Ifx_ERAY_GTUC05_Bits.DEC */
#define IFX_ERAY_GTUC05_DEC_LEN (8u)

/** \brief  Mask for Ifx_ERAY_GTUC05_Bits.DEC */
#define IFX_ERAY_GTUC05_DEC_MSK (0xffu)

/** \brief  Offset for Ifx_ERAY_GTUC05_Bits.DEC */
#define IFX_ERAY_GTUC05_DEC_OFF (24u)

/** \brief  Length for Ifx_ERAY_GTUC06_Bits.ASR */
#define IFX_ERAY_GTUC06_ASR_LEN (11u)

/** \brief  Mask for Ifx_ERAY_GTUC06_Bits.ASR */
#define IFX_ERAY_GTUC06_ASR_MSK (0x7ffu)

/** \brief  Offset for Ifx_ERAY_GTUC06_Bits.ASR */
#define IFX_ERAY_GTUC06_ASR_OFF (0u)

/** \brief  Length for Ifx_ERAY_GTUC06_Bits.MOD */
#define IFX_ERAY_GTUC06_MOD_LEN (11u)

/** \brief  Mask for Ifx_ERAY_GTUC06_Bits.MOD */
#define IFX_ERAY_GTUC06_MOD_MSK (0x7ffu)

/** \brief  Offset for Ifx_ERAY_GTUC06_Bits.MOD */
#define IFX_ERAY_GTUC06_MOD_OFF (16u)

/** \brief  Length for Ifx_ERAY_GTUC07_Bits.NSS */
#define IFX_ERAY_GTUC07_NSS_LEN (10u)

/** \brief  Mask for Ifx_ERAY_GTUC07_Bits.NSS */
#define IFX_ERAY_GTUC07_NSS_MSK (0x3ffu)

/** \brief  Offset for Ifx_ERAY_GTUC07_Bits.NSS */
#define IFX_ERAY_GTUC07_NSS_OFF (16u)

/** \brief  Length for Ifx_ERAY_GTUC07_Bits.SSL */
#define IFX_ERAY_GTUC07_SSL_LEN (10u)

/** \brief  Mask for Ifx_ERAY_GTUC07_Bits.SSL */
#define IFX_ERAY_GTUC07_SSL_MSK (0x3ffu)

/** \brief  Offset for Ifx_ERAY_GTUC07_Bits.SSL */
#define IFX_ERAY_GTUC07_SSL_OFF (0u)

/** \brief  Length for Ifx_ERAY_GTUC08_Bits.MSL */
#define IFX_ERAY_GTUC08_MSL_LEN (6u)

/** \brief  Mask for Ifx_ERAY_GTUC08_Bits.MSL */
#define IFX_ERAY_GTUC08_MSL_MSK (0x3fu)

/** \brief  Offset for Ifx_ERAY_GTUC08_Bits.MSL */
#define IFX_ERAY_GTUC08_MSL_OFF (0u)

/** \brief  Length for Ifx_ERAY_GTUC08_Bits.NMS */
#define IFX_ERAY_GTUC08_NMS_LEN (13u)

/** \brief  Mask for Ifx_ERAY_GTUC08_Bits.NMS */
#define IFX_ERAY_GTUC08_NMS_MSK (0x1fffu)

/** \brief  Offset for Ifx_ERAY_GTUC08_Bits.NMS */
#define IFX_ERAY_GTUC08_NMS_OFF (16u)

/** \brief  Length for Ifx_ERAY_GTUC09_Bits.APO */
#define IFX_ERAY_GTUC09_APO_LEN (6u)

/** \brief  Mask for Ifx_ERAY_GTUC09_Bits.APO */
#define IFX_ERAY_GTUC09_APO_MSK (0x3fu)

/** \brief  Offset for Ifx_ERAY_GTUC09_Bits.APO */
#define IFX_ERAY_GTUC09_APO_OFF (0u)

/** \brief  Length for Ifx_ERAY_GTUC09_Bits.DSI */
#define IFX_ERAY_GTUC09_DSI_LEN (2u)

/** \brief  Mask for Ifx_ERAY_GTUC09_Bits.DSI */
#define IFX_ERAY_GTUC09_DSI_MSK (0x3u)

/** \brief  Offset for Ifx_ERAY_GTUC09_Bits.DSI */
#define IFX_ERAY_GTUC09_DSI_OFF (16u)

/** \brief  Length for Ifx_ERAY_GTUC09_Bits.MAPO */
#define IFX_ERAY_GTUC09_MAPO_LEN (5u)

/** \brief  Mask for Ifx_ERAY_GTUC09_Bits.MAPO */
#define IFX_ERAY_GTUC09_MAPO_MSK (0x1fu)

/** \brief  Offset for Ifx_ERAY_GTUC09_Bits.MAPO */
#define IFX_ERAY_GTUC09_MAPO_OFF (8u)

/** \brief  Length for Ifx_ERAY_GTUC10_Bits.MOC */
#define IFX_ERAY_GTUC10_MOC_LEN (14u)

/** \brief  Mask for Ifx_ERAY_GTUC10_Bits.MOC */
#define IFX_ERAY_GTUC10_MOC_MSK (0x3fffu)

/** \brief  Offset for Ifx_ERAY_GTUC10_Bits.MOC */
#define IFX_ERAY_GTUC10_MOC_OFF (0u)

/** \brief  Length for Ifx_ERAY_GTUC10_Bits.MRC */
#define IFX_ERAY_GTUC10_MRC_LEN (11u)

/** \brief  Mask for Ifx_ERAY_GTUC10_Bits.MRC */
#define IFX_ERAY_GTUC10_MRC_MSK (0x7ffu)

/** \brief  Offset for Ifx_ERAY_GTUC10_Bits.MRC */
#define IFX_ERAY_GTUC10_MRC_OFF (16u)

/** \brief  Length for Ifx_ERAY_GTUC11_Bits.EOC */
#define IFX_ERAY_GTUC11_EOC_LEN (3u)

/** \brief  Mask for Ifx_ERAY_GTUC11_Bits.EOC */
#define IFX_ERAY_GTUC11_EOC_MSK (0x7u)

/** \brief  Offset for Ifx_ERAY_GTUC11_Bits.EOC */
#define IFX_ERAY_GTUC11_EOC_OFF (16u)

/** \brief  Length for Ifx_ERAY_GTUC11_Bits.EOCC */
#define IFX_ERAY_GTUC11_EOCC_LEN (2u)

/** \brief  Mask for Ifx_ERAY_GTUC11_Bits.EOCC */
#define IFX_ERAY_GTUC11_EOCC_MSK (0x3u)

/** \brief  Offset for Ifx_ERAY_GTUC11_Bits.EOCC */
#define IFX_ERAY_GTUC11_EOCC_OFF (0u)

/** \brief  Length for Ifx_ERAY_GTUC11_Bits.ERC */
#define IFX_ERAY_GTUC11_ERC_LEN (3u)

/** \brief  Mask for Ifx_ERAY_GTUC11_Bits.ERC */
#define IFX_ERAY_GTUC11_ERC_MSK (0x7u)

/** \brief  Offset for Ifx_ERAY_GTUC11_Bits.ERC */
#define IFX_ERAY_GTUC11_ERC_OFF (24u)

/** \brief  Length for Ifx_ERAY_GTUC11_Bits.ERCC */
#define IFX_ERAY_GTUC11_ERCC_LEN (2u)

/** \brief  Mask for Ifx_ERAY_GTUC11_Bits.ERCC */
#define IFX_ERAY_GTUC11_ERCC_MSK (0x3u)

/** \brief  Offset for Ifx_ERAY_GTUC11_Bits.ERCC */
#define IFX_ERAY_GTUC11_ERCC_OFF (8u)

/** \brief  Length for Ifx_ERAY_IBCM_Bits.LDSH */
#define IFX_ERAY_IBCM_LDSH_LEN (1u)

/** \brief  Mask for Ifx_ERAY_IBCM_Bits.LDSH */
#define IFX_ERAY_IBCM_LDSH_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_IBCM_Bits.LDSH */
#define IFX_ERAY_IBCM_LDSH_OFF (1u)

/** \brief  Length for Ifx_ERAY_IBCM_Bits.LDSS */
#define IFX_ERAY_IBCM_LDSS_LEN (1u)

/** \brief  Mask for Ifx_ERAY_IBCM_Bits.LDSS */
#define IFX_ERAY_IBCM_LDSS_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_IBCM_Bits.LDSS */
#define IFX_ERAY_IBCM_LDSS_OFF (17u)

/** \brief  Length for Ifx_ERAY_IBCM_Bits.LHSH */
#define IFX_ERAY_IBCM_LHSH_LEN (1u)

/** \brief  Mask for Ifx_ERAY_IBCM_Bits.LHSH */
#define IFX_ERAY_IBCM_LHSH_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_IBCM_Bits.LHSH */
#define IFX_ERAY_IBCM_LHSH_OFF (0u)

/** \brief  Length for Ifx_ERAY_IBCM_Bits.LHSS */
#define IFX_ERAY_IBCM_LHSS_LEN (1u)

/** \brief  Mask for Ifx_ERAY_IBCM_Bits.LHSS */
#define IFX_ERAY_IBCM_LHSS_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_IBCM_Bits.LHSS */
#define IFX_ERAY_IBCM_LHSS_OFF (16u)

/** \brief  Length for Ifx_ERAY_IBCM_Bits.STXRH */
#define IFX_ERAY_IBCM_STXRH_LEN (1u)

/** \brief  Mask for Ifx_ERAY_IBCM_Bits.STXRH */
#define IFX_ERAY_IBCM_STXRH_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_IBCM_Bits.STXRH */
#define IFX_ERAY_IBCM_STXRH_OFF (2u)

/** \brief  Length for Ifx_ERAY_IBCM_Bits.STXRS */
#define IFX_ERAY_IBCM_STXRS_LEN (1u)

/** \brief  Mask for Ifx_ERAY_IBCM_Bits.STXRS */
#define IFX_ERAY_IBCM_STXRS_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_IBCM_Bits.STXRS */
#define IFX_ERAY_IBCM_STXRS_OFF (18u)

/** \brief  Length for Ifx_ERAY_IBCR_Bits.IBRH */
#define IFX_ERAY_IBCR_IBRH_LEN (7u)

/** \brief  Mask for Ifx_ERAY_IBCR_Bits.IBRH */
#define IFX_ERAY_IBCR_IBRH_MSK (0x7fu)

/** \brief  Offset for Ifx_ERAY_IBCR_Bits.IBRH */
#define IFX_ERAY_IBCR_IBRH_OFF (0u)

/** \brief  Length for Ifx_ERAY_IBCR_Bits.IBRS */
#define IFX_ERAY_IBCR_IBRS_LEN (7u)

/** \brief  Mask for Ifx_ERAY_IBCR_Bits.IBRS */
#define IFX_ERAY_IBCR_IBRS_MSK (0x7fu)

/** \brief  Offset for Ifx_ERAY_IBCR_Bits.IBRS */
#define IFX_ERAY_IBCR_IBRS_OFF (16u)

/** \brief  Length for Ifx_ERAY_IBCR_Bits.IBSYH */
#define IFX_ERAY_IBCR_IBSYH_LEN (1u)

/** \brief  Mask for Ifx_ERAY_IBCR_Bits.IBSYH */
#define IFX_ERAY_IBCR_IBSYH_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_IBCR_Bits.IBSYH */
#define IFX_ERAY_IBCR_IBSYH_OFF (15u)

/** \brief  Length for Ifx_ERAY_IBCR_Bits.IBSYS */
#define IFX_ERAY_IBCR_IBSYS_LEN (1u)

/** \brief  Mask for Ifx_ERAY_IBCR_Bits.IBSYS */
#define IFX_ERAY_IBCR_IBSYS_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_IBCR_Bits.IBSYS */
#define IFX_ERAY_IBCR_IBSYS_OFF (31u)

/** \brief  Length for Ifx_ERAY_ID_Bits.MODNUMBER */
#define IFX_ERAY_ID_MODNUMBER_LEN (16u)

/** \brief  Mask for Ifx_ERAY_ID_Bits.MODNUMBER */
#define IFX_ERAY_ID_MODNUMBER_MSK (0xffffu)

/** \brief  Offset for Ifx_ERAY_ID_Bits.MODNUMBER */
#define IFX_ERAY_ID_MODNUMBER_OFF (16u)

/** \brief  Length for Ifx_ERAY_ID_Bits.MODREV */
#define IFX_ERAY_ID_MODREV_LEN (8u)

/** \brief  Mask for Ifx_ERAY_ID_Bits.MODREV */
#define IFX_ERAY_ID_MODREV_MSK (0xffu)

/** \brief  Offset for Ifx_ERAY_ID_Bits.MODREV */
#define IFX_ERAY_ID_MODREV_OFF (0u)

/** \brief  Length for Ifx_ERAY_ID_Bits.MODTYPE */
#define IFX_ERAY_ID_MODTYPE_LEN (8u)

/** \brief  Mask for Ifx_ERAY_ID_Bits.MODTYPE */
#define IFX_ERAY_ID_MODTYPE_MSK (0xffu)

/** \brief  Offset for Ifx_ERAY_ID_Bits.MODTYPE */
#define IFX_ERAY_ID_MODTYPE_OFF (8u)

/** \brief  Length for Ifx_ERAY_ILE_Bits.EINT0 */
#define IFX_ERAY_ILE_EINT0_LEN (1u)

/** \brief  Mask for Ifx_ERAY_ILE_Bits.EINT0 */
#define IFX_ERAY_ILE_EINT0_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_ILE_Bits.EINT0 */
#define IFX_ERAY_ILE_EINT0_OFF (0u)

/** \brief  Length for Ifx_ERAY_ILE_Bits.EINT1 */
#define IFX_ERAY_ILE_EINT1_LEN (1u)

/** \brief  Mask for Ifx_ERAY_ILE_Bits.EINT1 */
#define IFX_ERAY_ILE_EINT1_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_ILE_Bits.EINT1 */
#define IFX_ERAY_ILE_EINT1_OFF (1u)

/** \brief  Length for Ifx_ERAY_KRST0_Bits.RST */
#define IFX_ERAY_KRST0_RST_LEN (1u)

/** \brief  Mask for Ifx_ERAY_KRST0_Bits.RST */
#define IFX_ERAY_KRST0_RST_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_KRST0_Bits.RST */
#define IFX_ERAY_KRST0_RST_OFF (0u)

/** \brief  Length for Ifx_ERAY_KRST0_Bits.RSTSTAT */
#define IFX_ERAY_KRST0_RSTSTAT_LEN (1u)

/** \brief  Mask for Ifx_ERAY_KRST0_Bits.RSTSTAT */
#define IFX_ERAY_KRST0_RSTSTAT_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_KRST0_Bits.RSTSTAT */
#define IFX_ERAY_KRST0_RSTSTAT_OFF (1u)

/** \brief  Length for Ifx_ERAY_KRST1_Bits.RST */
#define IFX_ERAY_KRST1_RST_LEN (1u)

/** \brief  Mask for Ifx_ERAY_KRST1_Bits.RST */
#define IFX_ERAY_KRST1_RST_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_KRST1_Bits.RST */
#define IFX_ERAY_KRST1_RST_OFF (0u)

/** \brief  Length for Ifx_ERAY_KRSTCLR_Bits.CLR */
#define IFX_ERAY_KRSTCLR_CLR_LEN (1u)

/** \brief  Mask for Ifx_ERAY_KRSTCLR_Bits.CLR */
#define IFX_ERAY_KRSTCLR_CLR_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_KRSTCLR_Bits.CLR */
#define IFX_ERAY_KRSTCLR_CLR_OFF (0u)

/** \brief  Length for Ifx_ERAY_LCK_Bits.CLK */
#define IFX_ERAY_LCK_CLK_LEN (8u)

/** \brief  Mask for Ifx_ERAY_LCK_Bits.CLK */
#define IFX_ERAY_LCK_CLK_MSK (0xffu)

/** \brief  Offset for Ifx_ERAY_LCK_Bits.CLK */
#define IFX_ERAY_LCK_CLK_OFF (0u)

/** \brief  Length for Ifx_ERAY_LCK_Bits.TMK */
#define IFX_ERAY_LCK_TMK_LEN (8u)

/** \brief  Mask for Ifx_ERAY_LCK_Bits.TMK */
#define IFX_ERAY_LCK_TMK_MSK (0xffu)

/** \brief  Offset for Ifx_ERAY_LCK_Bits.TMK */
#define IFX_ERAY_LCK_TMK_OFF (8u)

/** \brief  Length for Ifx_ERAY_LDTS_Bits.LDTA */
#define IFX_ERAY_LDTS_LDTA_LEN (11u)

/** \brief  Mask for Ifx_ERAY_LDTS_Bits.LDTA */
#define IFX_ERAY_LDTS_LDTA_MSK (0x7ffu)

/** \brief  Offset for Ifx_ERAY_LDTS_Bits.LDTA */
#define IFX_ERAY_LDTS_LDTA_OFF (0u)

/** \brief  Length for Ifx_ERAY_LDTS_Bits.LDTB */
#define IFX_ERAY_LDTS_LDTB_LEN (11u)

/** \brief  Mask for Ifx_ERAY_LDTS_Bits.LDTB */
#define IFX_ERAY_LDTS_LDTB_MSK (0x7ffu)

/** \brief  Offset for Ifx_ERAY_LDTS_Bits.LDTB */
#define IFX_ERAY_LDTS_LDTB_OFF (16u)

/** \brief  Length for Ifx_ERAY_MBS_Bits.CCS */
#define IFX_ERAY_MBS_CCS_LEN (6u)

/** \brief  Mask for Ifx_ERAY_MBS_Bits.CCS */
#define IFX_ERAY_MBS_CCS_MSK (0x3fu)

/** \brief  Offset for Ifx_ERAY_MBS_Bits.CCS */
#define IFX_ERAY_MBS_CCS_OFF (16u)

/** \brief  Length for Ifx_ERAY_MBS_Bits.CEOA */
#define IFX_ERAY_MBS_CEOA_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBS_Bits.CEOA */
#define IFX_ERAY_MBS_CEOA_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBS_Bits.CEOA */
#define IFX_ERAY_MBS_CEOA_OFF (4u)

/** \brief  Length for Ifx_ERAY_MBS_Bits.CEOB */
#define IFX_ERAY_MBS_CEOB_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBS_Bits.CEOB */
#define IFX_ERAY_MBS_CEOB_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBS_Bits.CEOB */
#define IFX_ERAY_MBS_CEOB_OFF (5u)

/** \brief  Length for Ifx_ERAY_MBS_Bits.ESA */
#define IFX_ERAY_MBS_ESA_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBS_Bits.ESA */
#define IFX_ERAY_MBS_ESA_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBS_Bits.ESA */
#define IFX_ERAY_MBS_ESA_OFF (10u)

/** \brief  Length for Ifx_ERAY_MBS_Bits.ESB */
#define IFX_ERAY_MBS_ESB_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBS_Bits.ESB */
#define IFX_ERAY_MBS_ESB_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBS_Bits.ESB */
#define IFX_ERAY_MBS_ESB_OFF (11u)

/** \brief  Length for Ifx_ERAY_MBS_Bits.FTA */
#define IFX_ERAY_MBS_FTA_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBS_Bits.FTA */
#define IFX_ERAY_MBS_FTA_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBS_Bits.FTA */
#define IFX_ERAY_MBS_FTA_OFF (14u)

/** \brief  Length for Ifx_ERAY_MBS_Bits.FTB */
#define IFX_ERAY_MBS_FTB_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBS_Bits.FTB */
#define IFX_ERAY_MBS_FTB_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBS_Bits.FTB */
#define IFX_ERAY_MBS_FTB_OFF (15u)

/** \brief  Length for Ifx_ERAY_MBS_Bits.MLST */
#define IFX_ERAY_MBS_MLST_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBS_Bits.MLST */
#define IFX_ERAY_MBS_MLST_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBS_Bits.MLST */
#define IFX_ERAY_MBS_MLST_OFF (12u)

/** \brief  Length for Ifx_ERAY_MBS_Bits.NFIS */
#define IFX_ERAY_MBS_NFIS_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBS_Bits.NFIS */
#define IFX_ERAY_MBS_NFIS_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBS_Bits.NFIS */
#define IFX_ERAY_MBS_NFIS_OFF (27u)

/** \brief  Length for Ifx_ERAY_MBS_Bits.PPIS */
#define IFX_ERAY_MBS_PPIS_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBS_Bits.PPIS */
#define IFX_ERAY_MBS_PPIS_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBS_Bits.PPIS */
#define IFX_ERAY_MBS_PPIS_OFF (28u)

/** \brief  Length for Ifx_ERAY_MBS_Bits.RCIS */
#define IFX_ERAY_MBS_RCIS_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBS_Bits.RCIS */
#define IFX_ERAY_MBS_RCIS_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBS_Bits.RCIS */
#define IFX_ERAY_MBS_RCIS_OFF (24u)

/** \brief  Length for Ifx_ERAY_MBS_Bits.RESS */
#define IFX_ERAY_MBS_RESS_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBS_Bits.RESS */
#define IFX_ERAY_MBS_RESS_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBS_Bits.RESS */
#define IFX_ERAY_MBS_RESS_OFF (29u)

/** \brief  Length for Ifx_ERAY_MBS_Bits.SEOA */
#define IFX_ERAY_MBS_SEOA_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBS_Bits.SEOA */
#define IFX_ERAY_MBS_SEOA_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBS_Bits.SEOA */
#define IFX_ERAY_MBS_SEOA_OFF (2u)

/** \brief  Length for Ifx_ERAY_MBS_Bits.SEOB */
#define IFX_ERAY_MBS_SEOB_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBS_Bits.SEOB */
#define IFX_ERAY_MBS_SEOB_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBS_Bits.SEOB */
#define IFX_ERAY_MBS_SEOB_OFF (3u)

/** \brief  Length for Ifx_ERAY_MBS_Bits.SFIS */
#define IFX_ERAY_MBS_SFIS_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBS_Bits.SFIS */
#define IFX_ERAY_MBS_SFIS_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBS_Bits.SFIS */
#define IFX_ERAY_MBS_SFIS_OFF (25u)

/** \brief  Length for Ifx_ERAY_MBS_Bits.SVOA */
#define IFX_ERAY_MBS_SVOA_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBS_Bits.SVOA */
#define IFX_ERAY_MBS_SVOA_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBS_Bits.SVOA */
#define IFX_ERAY_MBS_SVOA_OFF (6u)

/** \brief  Length for Ifx_ERAY_MBS_Bits.SVOB */
#define IFX_ERAY_MBS_SVOB_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBS_Bits.SVOB */
#define IFX_ERAY_MBS_SVOB_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBS_Bits.SVOB */
#define IFX_ERAY_MBS_SVOB_OFF (7u)

/** \brief  Length for Ifx_ERAY_MBS_Bits.SYNS */
#define IFX_ERAY_MBS_SYNS_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBS_Bits.SYNS */
#define IFX_ERAY_MBS_SYNS_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBS_Bits.SYNS */
#define IFX_ERAY_MBS_SYNS_OFF (26u)

/** \brief  Length for Ifx_ERAY_MBS_Bits.TCIA */
#define IFX_ERAY_MBS_TCIA_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBS_Bits.TCIA */
#define IFX_ERAY_MBS_TCIA_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBS_Bits.TCIA */
#define IFX_ERAY_MBS_TCIA_OFF (8u)

/** \brief  Length for Ifx_ERAY_MBS_Bits.TCIB */
#define IFX_ERAY_MBS_TCIB_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBS_Bits.TCIB */
#define IFX_ERAY_MBS_TCIB_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBS_Bits.TCIB */
#define IFX_ERAY_MBS_TCIB_OFF (9u)

/** \brief  Length for Ifx_ERAY_MBS_Bits.VFRA */
#define IFX_ERAY_MBS_VFRA_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBS_Bits.VFRA */
#define IFX_ERAY_MBS_VFRA_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBS_Bits.VFRA */
#define IFX_ERAY_MBS_VFRA_OFF (0u)

/** \brief  Length for Ifx_ERAY_MBS_Bits.VFRB */
#define IFX_ERAY_MBS_VFRB_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBS_Bits.VFRB */
#define IFX_ERAY_MBS_VFRB_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBS_Bits.VFRB */
#define IFX_ERAY_MBS_VFRB_OFF (1u)

/** \brief  Length for Ifx_ERAY_MBSC1_Bits.MBC0 */
#define IFX_ERAY_MBSC1_MBC0_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC1_Bits.MBC0 */
#define IFX_ERAY_MBSC1_MBC0_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC1_Bits.MBC0 */
#define IFX_ERAY_MBSC1_MBC0_OFF (0u)

/** \brief  Length for Ifx_ERAY_MBSC1_Bits.MBC10 */
#define IFX_ERAY_MBSC1_MBC10_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC1_Bits.MBC10 */
#define IFX_ERAY_MBSC1_MBC10_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC1_Bits.MBC10 */
#define IFX_ERAY_MBSC1_MBC10_OFF (10u)

/** \brief  Length for Ifx_ERAY_MBSC1_Bits.MBC11 */
#define IFX_ERAY_MBSC1_MBC11_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC1_Bits.MBC11 */
#define IFX_ERAY_MBSC1_MBC11_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC1_Bits.MBC11 */
#define IFX_ERAY_MBSC1_MBC11_OFF (11u)

/** \brief  Length for Ifx_ERAY_MBSC1_Bits.MBC12 */
#define IFX_ERAY_MBSC1_MBC12_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC1_Bits.MBC12 */
#define IFX_ERAY_MBSC1_MBC12_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC1_Bits.MBC12 */
#define IFX_ERAY_MBSC1_MBC12_OFF (12u)

/** \brief  Length for Ifx_ERAY_MBSC1_Bits.MBC13 */
#define IFX_ERAY_MBSC1_MBC13_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC1_Bits.MBC13 */
#define IFX_ERAY_MBSC1_MBC13_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC1_Bits.MBC13 */
#define IFX_ERAY_MBSC1_MBC13_OFF (13u)

/** \brief  Length for Ifx_ERAY_MBSC1_Bits.MBC14 */
#define IFX_ERAY_MBSC1_MBC14_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC1_Bits.MBC14 */
#define IFX_ERAY_MBSC1_MBC14_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC1_Bits.MBC14 */
#define IFX_ERAY_MBSC1_MBC14_OFF (14u)

/** \brief  Length for Ifx_ERAY_MBSC1_Bits.MBC15 */
#define IFX_ERAY_MBSC1_MBC15_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC1_Bits.MBC15 */
#define IFX_ERAY_MBSC1_MBC15_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC1_Bits.MBC15 */
#define IFX_ERAY_MBSC1_MBC15_OFF (15u)

/** \brief  Length for Ifx_ERAY_MBSC1_Bits.MBC16 */
#define IFX_ERAY_MBSC1_MBC16_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC1_Bits.MBC16 */
#define IFX_ERAY_MBSC1_MBC16_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC1_Bits.MBC16 */
#define IFX_ERAY_MBSC1_MBC16_OFF (16u)

/** \brief  Length for Ifx_ERAY_MBSC1_Bits.MBC17 */
#define IFX_ERAY_MBSC1_MBC17_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC1_Bits.MBC17 */
#define IFX_ERAY_MBSC1_MBC17_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC1_Bits.MBC17 */
#define IFX_ERAY_MBSC1_MBC17_OFF (17u)

/** \brief  Length for Ifx_ERAY_MBSC1_Bits.MBC18 */
#define IFX_ERAY_MBSC1_MBC18_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC1_Bits.MBC18 */
#define IFX_ERAY_MBSC1_MBC18_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC1_Bits.MBC18 */
#define IFX_ERAY_MBSC1_MBC18_OFF (18u)

/** \brief  Length for Ifx_ERAY_MBSC1_Bits.MBC19 */
#define IFX_ERAY_MBSC1_MBC19_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC1_Bits.MBC19 */
#define IFX_ERAY_MBSC1_MBC19_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC1_Bits.MBC19 */
#define IFX_ERAY_MBSC1_MBC19_OFF (19u)

/** \brief  Length for Ifx_ERAY_MBSC1_Bits.MBC1 */
#define IFX_ERAY_MBSC1_MBC1_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC1_Bits.MBC1 */
#define IFX_ERAY_MBSC1_MBC1_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC1_Bits.MBC1 */
#define IFX_ERAY_MBSC1_MBC1_OFF (1u)

/** \brief  Length for Ifx_ERAY_MBSC1_Bits.MBC20 */
#define IFX_ERAY_MBSC1_MBC20_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC1_Bits.MBC20 */
#define IFX_ERAY_MBSC1_MBC20_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC1_Bits.MBC20 */
#define IFX_ERAY_MBSC1_MBC20_OFF (20u)

/** \brief  Length for Ifx_ERAY_MBSC1_Bits.MBC21 */
#define IFX_ERAY_MBSC1_MBC21_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC1_Bits.MBC21 */
#define IFX_ERAY_MBSC1_MBC21_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC1_Bits.MBC21 */
#define IFX_ERAY_MBSC1_MBC21_OFF (21u)

/** \brief  Length for Ifx_ERAY_MBSC1_Bits.MBC22 */
#define IFX_ERAY_MBSC1_MBC22_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC1_Bits.MBC22 */
#define IFX_ERAY_MBSC1_MBC22_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC1_Bits.MBC22 */
#define IFX_ERAY_MBSC1_MBC22_OFF (22u)

/** \brief  Length for Ifx_ERAY_MBSC1_Bits.MBC23 */
#define IFX_ERAY_MBSC1_MBC23_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC1_Bits.MBC23 */
#define IFX_ERAY_MBSC1_MBC23_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC1_Bits.MBC23 */
#define IFX_ERAY_MBSC1_MBC23_OFF (23u)

/** \brief  Length for Ifx_ERAY_MBSC1_Bits.MBC24 */
#define IFX_ERAY_MBSC1_MBC24_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC1_Bits.MBC24 */
#define IFX_ERAY_MBSC1_MBC24_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC1_Bits.MBC24 */
#define IFX_ERAY_MBSC1_MBC24_OFF (24u)

/** \brief  Length for Ifx_ERAY_MBSC1_Bits.MBC25 */
#define IFX_ERAY_MBSC1_MBC25_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC1_Bits.MBC25 */
#define IFX_ERAY_MBSC1_MBC25_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC1_Bits.MBC25 */
#define IFX_ERAY_MBSC1_MBC25_OFF (25u)

/** \brief  Length for Ifx_ERAY_MBSC1_Bits.MBC26 */
#define IFX_ERAY_MBSC1_MBC26_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC1_Bits.MBC26 */
#define IFX_ERAY_MBSC1_MBC26_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC1_Bits.MBC26 */
#define IFX_ERAY_MBSC1_MBC26_OFF (26u)

/** \brief  Length for Ifx_ERAY_MBSC1_Bits.MBC27 */
#define IFX_ERAY_MBSC1_MBC27_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC1_Bits.MBC27 */
#define IFX_ERAY_MBSC1_MBC27_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC1_Bits.MBC27 */
#define IFX_ERAY_MBSC1_MBC27_OFF (27u)

/** \brief  Length for Ifx_ERAY_MBSC1_Bits.MBC28 */
#define IFX_ERAY_MBSC1_MBC28_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC1_Bits.MBC28 */
#define IFX_ERAY_MBSC1_MBC28_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC1_Bits.MBC28 */
#define IFX_ERAY_MBSC1_MBC28_OFF (28u)

/** \brief  Length for Ifx_ERAY_MBSC1_Bits.MBC29 */
#define IFX_ERAY_MBSC1_MBC29_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC1_Bits.MBC29 */
#define IFX_ERAY_MBSC1_MBC29_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC1_Bits.MBC29 */
#define IFX_ERAY_MBSC1_MBC29_OFF (29u)

/** \brief  Length for Ifx_ERAY_MBSC1_Bits.MBC2 */
#define IFX_ERAY_MBSC1_MBC2_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC1_Bits.MBC2 */
#define IFX_ERAY_MBSC1_MBC2_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC1_Bits.MBC2 */
#define IFX_ERAY_MBSC1_MBC2_OFF (2u)

/** \brief  Length for Ifx_ERAY_MBSC1_Bits.MBC30 */
#define IFX_ERAY_MBSC1_MBC30_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC1_Bits.MBC30 */
#define IFX_ERAY_MBSC1_MBC30_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC1_Bits.MBC30 */
#define IFX_ERAY_MBSC1_MBC30_OFF (30u)

/** \brief  Length for Ifx_ERAY_MBSC1_Bits.MBC31 */
#define IFX_ERAY_MBSC1_MBC31_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC1_Bits.MBC31 */
#define IFX_ERAY_MBSC1_MBC31_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC1_Bits.MBC31 */
#define IFX_ERAY_MBSC1_MBC31_OFF (31u)

/** \brief  Length for Ifx_ERAY_MBSC1_Bits.MBC3 */
#define IFX_ERAY_MBSC1_MBC3_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC1_Bits.MBC3 */
#define IFX_ERAY_MBSC1_MBC3_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC1_Bits.MBC3 */
#define IFX_ERAY_MBSC1_MBC3_OFF (3u)

/** \brief  Length for Ifx_ERAY_MBSC1_Bits.MBC4 */
#define IFX_ERAY_MBSC1_MBC4_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC1_Bits.MBC4 */
#define IFX_ERAY_MBSC1_MBC4_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC1_Bits.MBC4 */
#define IFX_ERAY_MBSC1_MBC4_OFF (4u)

/** \brief  Length for Ifx_ERAY_MBSC1_Bits.MBC5 */
#define IFX_ERAY_MBSC1_MBC5_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC1_Bits.MBC5 */
#define IFX_ERAY_MBSC1_MBC5_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC1_Bits.MBC5 */
#define IFX_ERAY_MBSC1_MBC5_OFF (5u)

/** \brief  Length for Ifx_ERAY_MBSC1_Bits.MBC6 */
#define IFX_ERAY_MBSC1_MBC6_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC1_Bits.MBC6 */
#define IFX_ERAY_MBSC1_MBC6_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC1_Bits.MBC6 */
#define IFX_ERAY_MBSC1_MBC6_OFF (6u)

/** \brief  Length for Ifx_ERAY_MBSC1_Bits.MBC7 */
#define IFX_ERAY_MBSC1_MBC7_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC1_Bits.MBC7 */
#define IFX_ERAY_MBSC1_MBC7_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC1_Bits.MBC7 */
#define IFX_ERAY_MBSC1_MBC7_OFF (7u)

/** \brief  Length for Ifx_ERAY_MBSC1_Bits.MBC8 */
#define IFX_ERAY_MBSC1_MBC8_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC1_Bits.MBC8 */
#define IFX_ERAY_MBSC1_MBC8_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC1_Bits.MBC8 */
#define IFX_ERAY_MBSC1_MBC8_OFF (8u)

/** \brief  Length for Ifx_ERAY_MBSC1_Bits.MBC9 */
#define IFX_ERAY_MBSC1_MBC9_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC1_Bits.MBC9 */
#define IFX_ERAY_MBSC1_MBC9_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC1_Bits.MBC9 */
#define IFX_ERAY_MBSC1_MBC9_OFF (9u)

/** \brief  Length for Ifx_ERAY_MBSC2_Bits.MBC32 */
#define IFX_ERAY_MBSC2_MBC32_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC2_Bits.MBC32 */
#define IFX_ERAY_MBSC2_MBC32_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC2_Bits.MBC32 */
#define IFX_ERAY_MBSC2_MBC32_OFF (0u)

/** \brief  Length for Ifx_ERAY_MBSC2_Bits.MBC33 */
#define IFX_ERAY_MBSC2_MBC33_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC2_Bits.MBC33 */
#define IFX_ERAY_MBSC2_MBC33_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC2_Bits.MBC33 */
#define IFX_ERAY_MBSC2_MBC33_OFF (1u)

/** \brief  Length for Ifx_ERAY_MBSC2_Bits.MBC34 */
#define IFX_ERAY_MBSC2_MBC34_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC2_Bits.MBC34 */
#define IFX_ERAY_MBSC2_MBC34_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC2_Bits.MBC34 */
#define IFX_ERAY_MBSC2_MBC34_OFF (2u)

/** \brief  Length for Ifx_ERAY_MBSC2_Bits.MBC35 */
#define IFX_ERAY_MBSC2_MBC35_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC2_Bits.MBC35 */
#define IFX_ERAY_MBSC2_MBC35_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC2_Bits.MBC35 */
#define IFX_ERAY_MBSC2_MBC35_OFF (3u)

/** \brief  Length for Ifx_ERAY_MBSC2_Bits.MBC36 */
#define IFX_ERAY_MBSC2_MBC36_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC2_Bits.MBC36 */
#define IFX_ERAY_MBSC2_MBC36_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC2_Bits.MBC36 */
#define IFX_ERAY_MBSC2_MBC36_OFF (4u)

/** \brief  Length for Ifx_ERAY_MBSC2_Bits.MBC37 */
#define IFX_ERAY_MBSC2_MBC37_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC2_Bits.MBC37 */
#define IFX_ERAY_MBSC2_MBC37_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC2_Bits.MBC37 */
#define IFX_ERAY_MBSC2_MBC37_OFF (5u)

/** \brief  Length for Ifx_ERAY_MBSC2_Bits.MBC38 */
#define IFX_ERAY_MBSC2_MBC38_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC2_Bits.MBC38 */
#define IFX_ERAY_MBSC2_MBC38_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC2_Bits.MBC38 */
#define IFX_ERAY_MBSC2_MBC38_OFF (6u)

/** \brief  Length for Ifx_ERAY_MBSC2_Bits.MBC39 */
#define IFX_ERAY_MBSC2_MBC39_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC2_Bits.MBC39 */
#define IFX_ERAY_MBSC2_MBC39_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC2_Bits.MBC39 */
#define IFX_ERAY_MBSC2_MBC39_OFF (7u)

/** \brief  Length for Ifx_ERAY_MBSC2_Bits.MBC40 */
#define IFX_ERAY_MBSC2_MBC40_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC2_Bits.MBC40 */
#define IFX_ERAY_MBSC2_MBC40_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC2_Bits.MBC40 */
#define IFX_ERAY_MBSC2_MBC40_OFF (8u)

/** \brief  Length for Ifx_ERAY_MBSC2_Bits.MBC41 */
#define IFX_ERAY_MBSC2_MBC41_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC2_Bits.MBC41 */
#define IFX_ERAY_MBSC2_MBC41_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC2_Bits.MBC41 */
#define IFX_ERAY_MBSC2_MBC41_OFF (9u)

/** \brief  Length for Ifx_ERAY_MBSC2_Bits.MBC42 */
#define IFX_ERAY_MBSC2_MBC42_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC2_Bits.MBC42 */
#define IFX_ERAY_MBSC2_MBC42_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC2_Bits.MBC42 */
#define IFX_ERAY_MBSC2_MBC42_OFF (10u)

/** \brief  Length for Ifx_ERAY_MBSC2_Bits.MBC43 */
#define IFX_ERAY_MBSC2_MBC43_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC2_Bits.MBC43 */
#define IFX_ERAY_MBSC2_MBC43_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC2_Bits.MBC43 */
#define IFX_ERAY_MBSC2_MBC43_OFF (11u)

/** \brief  Length for Ifx_ERAY_MBSC2_Bits.MBC44 */
#define IFX_ERAY_MBSC2_MBC44_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC2_Bits.MBC44 */
#define IFX_ERAY_MBSC2_MBC44_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC2_Bits.MBC44 */
#define IFX_ERAY_MBSC2_MBC44_OFF (12u)

/** \brief  Length for Ifx_ERAY_MBSC2_Bits.MBC45 */
#define IFX_ERAY_MBSC2_MBC45_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC2_Bits.MBC45 */
#define IFX_ERAY_MBSC2_MBC45_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC2_Bits.MBC45 */
#define IFX_ERAY_MBSC2_MBC45_OFF (13u)

/** \brief  Length for Ifx_ERAY_MBSC2_Bits.MBC46 */
#define IFX_ERAY_MBSC2_MBC46_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC2_Bits.MBC46 */
#define IFX_ERAY_MBSC2_MBC46_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC2_Bits.MBC46 */
#define IFX_ERAY_MBSC2_MBC46_OFF (14u)

/** \brief  Length for Ifx_ERAY_MBSC2_Bits.MBC47 */
#define IFX_ERAY_MBSC2_MBC47_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC2_Bits.MBC47 */
#define IFX_ERAY_MBSC2_MBC47_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC2_Bits.MBC47 */
#define IFX_ERAY_MBSC2_MBC47_OFF (15u)

/** \brief  Length for Ifx_ERAY_MBSC2_Bits.MBC48 */
#define IFX_ERAY_MBSC2_MBC48_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC2_Bits.MBC48 */
#define IFX_ERAY_MBSC2_MBC48_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC2_Bits.MBC48 */
#define IFX_ERAY_MBSC2_MBC48_OFF (16u)

/** \brief  Length for Ifx_ERAY_MBSC2_Bits.MBC49 */
#define IFX_ERAY_MBSC2_MBC49_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC2_Bits.MBC49 */
#define IFX_ERAY_MBSC2_MBC49_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC2_Bits.MBC49 */
#define IFX_ERAY_MBSC2_MBC49_OFF (17u)

/** \brief  Length for Ifx_ERAY_MBSC2_Bits.MBC50 */
#define IFX_ERAY_MBSC2_MBC50_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC2_Bits.MBC50 */
#define IFX_ERAY_MBSC2_MBC50_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC2_Bits.MBC50 */
#define IFX_ERAY_MBSC2_MBC50_OFF (18u)

/** \brief  Length for Ifx_ERAY_MBSC2_Bits.MBC51 */
#define IFX_ERAY_MBSC2_MBC51_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC2_Bits.MBC51 */
#define IFX_ERAY_MBSC2_MBC51_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC2_Bits.MBC51 */
#define IFX_ERAY_MBSC2_MBC51_OFF (19u)

/** \brief  Length for Ifx_ERAY_MBSC2_Bits.MBC52 */
#define IFX_ERAY_MBSC2_MBC52_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC2_Bits.MBC52 */
#define IFX_ERAY_MBSC2_MBC52_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC2_Bits.MBC52 */
#define IFX_ERAY_MBSC2_MBC52_OFF (20u)

/** \brief  Length for Ifx_ERAY_MBSC2_Bits.MBC53 */
#define IFX_ERAY_MBSC2_MBC53_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC2_Bits.MBC53 */
#define IFX_ERAY_MBSC2_MBC53_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC2_Bits.MBC53 */
#define IFX_ERAY_MBSC2_MBC53_OFF (21u)

/** \brief  Length for Ifx_ERAY_MBSC2_Bits.MBC54 */
#define IFX_ERAY_MBSC2_MBC54_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC2_Bits.MBC54 */
#define IFX_ERAY_MBSC2_MBC54_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC2_Bits.MBC54 */
#define IFX_ERAY_MBSC2_MBC54_OFF (22u)

/** \brief  Length for Ifx_ERAY_MBSC2_Bits.MBC55 */
#define IFX_ERAY_MBSC2_MBC55_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC2_Bits.MBC55 */
#define IFX_ERAY_MBSC2_MBC55_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC2_Bits.MBC55 */
#define IFX_ERAY_MBSC2_MBC55_OFF (23u)

/** \brief  Length for Ifx_ERAY_MBSC2_Bits.MBC56 */
#define IFX_ERAY_MBSC2_MBC56_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC2_Bits.MBC56 */
#define IFX_ERAY_MBSC2_MBC56_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC2_Bits.MBC56 */
#define IFX_ERAY_MBSC2_MBC56_OFF (24u)

/** \brief  Length for Ifx_ERAY_MBSC2_Bits.MBC57 */
#define IFX_ERAY_MBSC2_MBC57_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC2_Bits.MBC57 */
#define IFX_ERAY_MBSC2_MBC57_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC2_Bits.MBC57 */
#define IFX_ERAY_MBSC2_MBC57_OFF (25u)

/** \brief  Length for Ifx_ERAY_MBSC2_Bits.MBC58 */
#define IFX_ERAY_MBSC2_MBC58_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC2_Bits.MBC58 */
#define IFX_ERAY_MBSC2_MBC58_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC2_Bits.MBC58 */
#define IFX_ERAY_MBSC2_MBC58_OFF (26u)

/** \brief  Length for Ifx_ERAY_MBSC2_Bits.MBC59 */
#define IFX_ERAY_MBSC2_MBC59_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC2_Bits.MBC59 */
#define IFX_ERAY_MBSC2_MBC59_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC2_Bits.MBC59 */
#define IFX_ERAY_MBSC2_MBC59_OFF (27u)

/** \brief  Length for Ifx_ERAY_MBSC2_Bits.MBC60 */
#define IFX_ERAY_MBSC2_MBC60_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC2_Bits.MBC60 */
#define IFX_ERAY_MBSC2_MBC60_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC2_Bits.MBC60 */
#define IFX_ERAY_MBSC2_MBC60_OFF (28u)

/** \brief  Length for Ifx_ERAY_MBSC2_Bits.MBC61 */
#define IFX_ERAY_MBSC2_MBC61_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC2_Bits.MBC61 */
#define IFX_ERAY_MBSC2_MBC61_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC2_Bits.MBC61 */
#define IFX_ERAY_MBSC2_MBC61_OFF (29u)

/** \brief  Length for Ifx_ERAY_MBSC2_Bits.MBC62 */
#define IFX_ERAY_MBSC2_MBC62_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC2_Bits.MBC62 */
#define IFX_ERAY_MBSC2_MBC62_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC2_Bits.MBC62 */
#define IFX_ERAY_MBSC2_MBC62_OFF (30u)

/** \brief  Length for Ifx_ERAY_MBSC2_Bits.MBC63 */
#define IFX_ERAY_MBSC2_MBC63_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC2_Bits.MBC63 */
#define IFX_ERAY_MBSC2_MBC63_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC2_Bits.MBC63 */
#define IFX_ERAY_MBSC2_MBC63_OFF (31u)

/** \brief  Length for Ifx_ERAY_MBSC3_Bits.MBC64 */
#define IFX_ERAY_MBSC3_MBC64_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC3_Bits.MBC64 */
#define IFX_ERAY_MBSC3_MBC64_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC3_Bits.MBC64 */
#define IFX_ERAY_MBSC3_MBC64_OFF (0u)

/** \brief  Length for Ifx_ERAY_MBSC3_Bits.MBC65 */
#define IFX_ERAY_MBSC3_MBC65_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC3_Bits.MBC65 */
#define IFX_ERAY_MBSC3_MBC65_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC3_Bits.MBC65 */
#define IFX_ERAY_MBSC3_MBC65_OFF (1u)

/** \brief  Length for Ifx_ERAY_MBSC3_Bits.MBC66 */
#define IFX_ERAY_MBSC3_MBC66_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC3_Bits.MBC66 */
#define IFX_ERAY_MBSC3_MBC66_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC3_Bits.MBC66 */
#define IFX_ERAY_MBSC3_MBC66_OFF (2u)

/** \brief  Length for Ifx_ERAY_MBSC3_Bits.MBC67 */
#define IFX_ERAY_MBSC3_MBC67_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC3_Bits.MBC67 */
#define IFX_ERAY_MBSC3_MBC67_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC3_Bits.MBC67 */
#define IFX_ERAY_MBSC3_MBC67_OFF (3u)

/** \brief  Length for Ifx_ERAY_MBSC3_Bits.MBC68 */
#define IFX_ERAY_MBSC3_MBC68_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC3_Bits.MBC68 */
#define IFX_ERAY_MBSC3_MBC68_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC3_Bits.MBC68 */
#define IFX_ERAY_MBSC3_MBC68_OFF (4u)

/** \brief  Length for Ifx_ERAY_MBSC3_Bits.MBC69 */
#define IFX_ERAY_MBSC3_MBC69_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC3_Bits.MBC69 */
#define IFX_ERAY_MBSC3_MBC69_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC3_Bits.MBC69 */
#define IFX_ERAY_MBSC3_MBC69_OFF (5u)

/** \brief  Length for Ifx_ERAY_MBSC3_Bits.MBC70 */
#define IFX_ERAY_MBSC3_MBC70_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC3_Bits.MBC70 */
#define IFX_ERAY_MBSC3_MBC70_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC3_Bits.MBC70 */
#define IFX_ERAY_MBSC3_MBC70_OFF (6u)

/** \brief  Length for Ifx_ERAY_MBSC3_Bits.MBC71 */
#define IFX_ERAY_MBSC3_MBC71_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC3_Bits.MBC71 */
#define IFX_ERAY_MBSC3_MBC71_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC3_Bits.MBC71 */
#define IFX_ERAY_MBSC3_MBC71_OFF (7u)

/** \brief  Length for Ifx_ERAY_MBSC3_Bits.MBC72 */
#define IFX_ERAY_MBSC3_MBC72_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC3_Bits.MBC72 */
#define IFX_ERAY_MBSC3_MBC72_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC3_Bits.MBC72 */
#define IFX_ERAY_MBSC3_MBC72_OFF (8u)

/** \brief  Length for Ifx_ERAY_MBSC3_Bits.MBC73 */
#define IFX_ERAY_MBSC3_MBC73_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC3_Bits.MBC73 */
#define IFX_ERAY_MBSC3_MBC73_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC3_Bits.MBC73 */
#define IFX_ERAY_MBSC3_MBC73_OFF (9u)

/** \brief  Length for Ifx_ERAY_MBSC3_Bits.MBC74 */
#define IFX_ERAY_MBSC3_MBC74_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC3_Bits.MBC74 */
#define IFX_ERAY_MBSC3_MBC74_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC3_Bits.MBC74 */
#define IFX_ERAY_MBSC3_MBC74_OFF (10u)

/** \brief  Length for Ifx_ERAY_MBSC3_Bits.MBC75 */
#define IFX_ERAY_MBSC3_MBC75_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC3_Bits.MBC75 */
#define IFX_ERAY_MBSC3_MBC75_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC3_Bits.MBC75 */
#define IFX_ERAY_MBSC3_MBC75_OFF (11u)

/** \brief  Length for Ifx_ERAY_MBSC3_Bits.MBC76 */
#define IFX_ERAY_MBSC3_MBC76_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC3_Bits.MBC76 */
#define IFX_ERAY_MBSC3_MBC76_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC3_Bits.MBC76 */
#define IFX_ERAY_MBSC3_MBC76_OFF (12u)

/** \brief  Length for Ifx_ERAY_MBSC3_Bits.MBC77 */
#define IFX_ERAY_MBSC3_MBC77_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC3_Bits.MBC77 */
#define IFX_ERAY_MBSC3_MBC77_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC3_Bits.MBC77 */
#define IFX_ERAY_MBSC3_MBC77_OFF (13u)

/** \brief  Length for Ifx_ERAY_MBSC3_Bits.MBC78 */
#define IFX_ERAY_MBSC3_MBC78_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC3_Bits.MBC78 */
#define IFX_ERAY_MBSC3_MBC78_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC3_Bits.MBC78 */
#define IFX_ERAY_MBSC3_MBC78_OFF (14u)

/** \brief  Length for Ifx_ERAY_MBSC3_Bits.MBC79 */
#define IFX_ERAY_MBSC3_MBC79_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC3_Bits.MBC79 */
#define IFX_ERAY_MBSC3_MBC79_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC3_Bits.MBC79 */
#define IFX_ERAY_MBSC3_MBC79_OFF (15u)

/** \brief  Length for Ifx_ERAY_MBSC3_Bits.MBC80 */
#define IFX_ERAY_MBSC3_MBC80_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC3_Bits.MBC80 */
#define IFX_ERAY_MBSC3_MBC80_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC3_Bits.MBC80 */
#define IFX_ERAY_MBSC3_MBC80_OFF (16u)

/** \brief  Length for Ifx_ERAY_MBSC3_Bits.MBC81 */
#define IFX_ERAY_MBSC3_MBC81_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC3_Bits.MBC81 */
#define IFX_ERAY_MBSC3_MBC81_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC3_Bits.MBC81 */
#define IFX_ERAY_MBSC3_MBC81_OFF (17u)

/** \brief  Length for Ifx_ERAY_MBSC3_Bits.MBC82 */
#define IFX_ERAY_MBSC3_MBC82_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC3_Bits.MBC82 */
#define IFX_ERAY_MBSC3_MBC82_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC3_Bits.MBC82 */
#define IFX_ERAY_MBSC3_MBC82_OFF (18u)

/** \brief  Length for Ifx_ERAY_MBSC3_Bits.MBC83 */
#define IFX_ERAY_MBSC3_MBC83_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC3_Bits.MBC83 */
#define IFX_ERAY_MBSC3_MBC83_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC3_Bits.MBC83 */
#define IFX_ERAY_MBSC3_MBC83_OFF (19u)

/** \brief  Length for Ifx_ERAY_MBSC3_Bits.MBC84 */
#define IFX_ERAY_MBSC3_MBC84_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC3_Bits.MBC84 */
#define IFX_ERAY_MBSC3_MBC84_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC3_Bits.MBC84 */
#define IFX_ERAY_MBSC3_MBC84_OFF (20u)

/** \brief  Length for Ifx_ERAY_MBSC3_Bits.MBC85 */
#define IFX_ERAY_MBSC3_MBC85_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC3_Bits.MBC85 */
#define IFX_ERAY_MBSC3_MBC85_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC3_Bits.MBC85 */
#define IFX_ERAY_MBSC3_MBC85_OFF (21u)

/** \brief  Length for Ifx_ERAY_MBSC3_Bits.MBC86 */
#define IFX_ERAY_MBSC3_MBC86_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC3_Bits.MBC86 */
#define IFX_ERAY_MBSC3_MBC86_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC3_Bits.MBC86 */
#define IFX_ERAY_MBSC3_MBC86_OFF (22u)

/** \brief  Length for Ifx_ERAY_MBSC3_Bits.MBC87 */
#define IFX_ERAY_MBSC3_MBC87_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC3_Bits.MBC87 */
#define IFX_ERAY_MBSC3_MBC87_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC3_Bits.MBC87 */
#define IFX_ERAY_MBSC3_MBC87_OFF (23u)

/** \brief  Length for Ifx_ERAY_MBSC3_Bits.MBC88 */
#define IFX_ERAY_MBSC3_MBC88_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC3_Bits.MBC88 */
#define IFX_ERAY_MBSC3_MBC88_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC3_Bits.MBC88 */
#define IFX_ERAY_MBSC3_MBC88_OFF (24u)

/** \brief  Length for Ifx_ERAY_MBSC3_Bits.MBC89 */
#define IFX_ERAY_MBSC3_MBC89_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC3_Bits.MBC89 */
#define IFX_ERAY_MBSC3_MBC89_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC3_Bits.MBC89 */
#define IFX_ERAY_MBSC3_MBC89_OFF (25u)

/** \brief  Length for Ifx_ERAY_MBSC3_Bits.MBC90 */
#define IFX_ERAY_MBSC3_MBC90_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC3_Bits.MBC90 */
#define IFX_ERAY_MBSC3_MBC90_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC3_Bits.MBC90 */
#define IFX_ERAY_MBSC3_MBC90_OFF (26u)

/** \brief  Length for Ifx_ERAY_MBSC3_Bits.MBC91 */
#define IFX_ERAY_MBSC3_MBC91_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC3_Bits.MBC91 */
#define IFX_ERAY_MBSC3_MBC91_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC3_Bits.MBC91 */
#define IFX_ERAY_MBSC3_MBC91_OFF (27u)

/** \brief  Length for Ifx_ERAY_MBSC3_Bits.MBC92 */
#define IFX_ERAY_MBSC3_MBC92_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC3_Bits.MBC92 */
#define IFX_ERAY_MBSC3_MBC92_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC3_Bits.MBC92 */
#define IFX_ERAY_MBSC3_MBC92_OFF (28u)

/** \brief  Length for Ifx_ERAY_MBSC3_Bits.MBC93 */
#define IFX_ERAY_MBSC3_MBC93_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC3_Bits.MBC93 */
#define IFX_ERAY_MBSC3_MBC93_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC3_Bits.MBC93 */
#define IFX_ERAY_MBSC3_MBC93_OFF (29u)

/** \brief  Length for Ifx_ERAY_MBSC3_Bits.MBC94 */
#define IFX_ERAY_MBSC3_MBC94_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC3_Bits.MBC94 */
#define IFX_ERAY_MBSC3_MBC94_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC3_Bits.MBC94 */
#define IFX_ERAY_MBSC3_MBC94_OFF (30u)

/** \brief  Length for Ifx_ERAY_MBSC3_Bits.MBC95 */
#define IFX_ERAY_MBSC3_MBC95_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC3_Bits.MBC95 */
#define IFX_ERAY_MBSC3_MBC95_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC3_Bits.MBC95 */
#define IFX_ERAY_MBSC3_MBC95_OFF (31u)

/** \brief  Length for Ifx_ERAY_MBSC4_Bits.MBC100 */
#define IFX_ERAY_MBSC4_MBC100_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC4_Bits.MBC100 */
#define IFX_ERAY_MBSC4_MBC100_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC4_Bits.MBC100 */
#define IFX_ERAY_MBSC4_MBC100_OFF (4u)

/** \brief  Length for Ifx_ERAY_MBSC4_Bits.MBC101 */
#define IFX_ERAY_MBSC4_MBC101_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC4_Bits.MBC101 */
#define IFX_ERAY_MBSC4_MBC101_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC4_Bits.MBC101 */
#define IFX_ERAY_MBSC4_MBC101_OFF (5u)

/** \brief  Length for Ifx_ERAY_MBSC4_Bits.MBC102 */
#define IFX_ERAY_MBSC4_MBC102_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC4_Bits.MBC102 */
#define IFX_ERAY_MBSC4_MBC102_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC4_Bits.MBC102 */
#define IFX_ERAY_MBSC4_MBC102_OFF (6u)

/** \brief  Length for Ifx_ERAY_MBSC4_Bits.MBC103 */
#define IFX_ERAY_MBSC4_MBC103_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC4_Bits.MBC103 */
#define IFX_ERAY_MBSC4_MBC103_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC4_Bits.MBC103 */
#define IFX_ERAY_MBSC4_MBC103_OFF (7u)

/** \brief  Length for Ifx_ERAY_MBSC4_Bits.MBC104 */
#define IFX_ERAY_MBSC4_MBC104_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC4_Bits.MBC104 */
#define IFX_ERAY_MBSC4_MBC104_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC4_Bits.MBC104 */
#define IFX_ERAY_MBSC4_MBC104_OFF (8u)

/** \brief  Length for Ifx_ERAY_MBSC4_Bits.MBC105 */
#define IFX_ERAY_MBSC4_MBC105_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC4_Bits.MBC105 */
#define IFX_ERAY_MBSC4_MBC105_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC4_Bits.MBC105 */
#define IFX_ERAY_MBSC4_MBC105_OFF (9u)

/** \brief  Length for Ifx_ERAY_MBSC4_Bits.MBC106 */
#define IFX_ERAY_MBSC4_MBC106_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC4_Bits.MBC106 */
#define IFX_ERAY_MBSC4_MBC106_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC4_Bits.MBC106 */
#define IFX_ERAY_MBSC4_MBC106_OFF (10u)

/** \brief  Length for Ifx_ERAY_MBSC4_Bits.MBC107 */
#define IFX_ERAY_MBSC4_MBC107_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC4_Bits.MBC107 */
#define IFX_ERAY_MBSC4_MBC107_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC4_Bits.MBC107 */
#define IFX_ERAY_MBSC4_MBC107_OFF (11u)

/** \brief  Length for Ifx_ERAY_MBSC4_Bits.MBC108 */
#define IFX_ERAY_MBSC4_MBC108_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC4_Bits.MBC108 */
#define IFX_ERAY_MBSC4_MBC108_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC4_Bits.MBC108 */
#define IFX_ERAY_MBSC4_MBC108_OFF (12u)

/** \brief  Length for Ifx_ERAY_MBSC4_Bits.MBC109 */
#define IFX_ERAY_MBSC4_MBC109_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC4_Bits.MBC109 */
#define IFX_ERAY_MBSC4_MBC109_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC4_Bits.MBC109 */
#define IFX_ERAY_MBSC4_MBC109_OFF (13u)

/** \brief  Length for Ifx_ERAY_MBSC4_Bits.MBC110 */
#define IFX_ERAY_MBSC4_MBC110_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC4_Bits.MBC110 */
#define IFX_ERAY_MBSC4_MBC110_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC4_Bits.MBC110 */
#define IFX_ERAY_MBSC4_MBC110_OFF (14u)

/** \brief  Length for Ifx_ERAY_MBSC4_Bits.MBC111 */
#define IFX_ERAY_MBSC4_MBC111_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC4_Bits.MBC111 */
#define IFX_ERAY_MBSC4_MBC111_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC4_Bits.MBC111 */
#define IFX_ERAY_MBSC4_MBC111_OFF (15u)

/** \brief  Length for Ifx_ERAY_MBSC4_Bits.MBC112 */
#define IFX_ERAY_MBSC4_MBC112_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC4_Bits.MBC112 */
#define IFX_ERAY_MBSC4_MBC112_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC4_Bits.MBC112 */
#define IFX_ERAY_MBSC4_MBC112_OFF (16u)

/** \brief  Length for Ifx_ERAY_MBSC4_Bits.MBC113 */
#define IFX_ERAY_MBSC4_MBC113_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC4_Bits.MBC113 */
#define IFX_ERAY_MBSC4_MBC113_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC4_Bits.MBC113 */
#define IFX_ERAY_MBSC4_MBC113_OFF (17u)

/** \brief  Length for Ifx_ERAY_MBSC4_Bits.MBC114 */
#define IFX_ERAY_MBSC4_MBC114_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC4_Bits.MBC114 */
#define IFX_ERAY_MBSC4_MBC114_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC4_Bits.MBC114 */
#define IFX_ERAY_MBSC4_MBC114_OFF (18u)

/** \brief  Length for Ifx_ERAY_MBSC4_Bits.MBC115 */
#define IFX_ERAY_MBSC4_MBC115_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC4_Bits.MBC115 */
#define IFX_ERAY_MBSC4_MBC115_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC4_Bits.MBC115 */
#define IFX_ERAY_MBSC4_MBC115_OFF (19u)

/** \brief  Length for Ifx_ERAY_MBSC4_Bits.MBC116 */
#define IFX_ERAY_MBSC4_MBC116_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC4_Bits.MBC116 */
#define IFX_ERAY_MBSC4_MBC116_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC4_Bits.MBC116 */
#define IFX_ERAY_MBSC4_MBC116_OFF (20u)

/** \brief  Length for Ifx_ERAY_MBSC4_Bits.MBC117 */
#define IFX_ERAY_MBSC4_MBC117_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC4_Bits.MBC117 */
#define IFX_ERAY_MBSC4_MBC117_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC4_Bits.MBC117 */
#define IFX_ERAY_MBSC4_MBC117_OFF (21u)

/** \brief  Length for Ifx_ERAY_MBSC4_Bits.MBC118 */
#define IFX_ERAY_MBSC4_MBC118_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC4_Bits.MBC118 */
#define IFX_ERAY_MBSC4_MBC118_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC4_Bits.MBC118 */
#define IFX_ERAY_MBSC4_MBC118_OFF (22u)

/** \brief  Length for Ifx_ERAY_MBSC4_Bits.MBC119 */
#define IFX_ERAY_MBSC4_MBC119_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC4_Bits.MBC119 */
#define IFX_ERAY_MBSC4_MBC119_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC4_Bits.MBC119 */
#define IFX_ERAY_MBSC4_MBC119_OFF (23u)

/** \brief  Length for Ifx_ERAY_MBSC4_Bits.MBC120 */
#define IFX_ERAY_MBSC4_MBC120_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC4_Bits.MBC120 */
#define IFX_ERAY_MBSC4_MBC120_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC4_Bits.MBC120 */
#define IFX_ERAY_MBSC4_MBC120_OFF (24u)

/** \brief  Length for Ifx_ERAY_MBSC4_Bits.MBC121 */
#define IFX_ERAY_MBSC4_MBC121_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC4_Bits.MBC121 */
#define IFX_ERAY_MBSC4_MBC121_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC4_Bits.MBC121 */
#define IFX_ERAY_MBSC4_MBC121_OFF (25u)

/** \brief  Length for Ifx_ERAY_MBSC4_Bits.MBC122 */
#define IFX_ERAY_MBSC4_MBC122_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC4_Bits.MBC122 */
#define IFX_ERAY_MBSC4_MBC122_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC4_Bits.MBC122 */
#define IFX_ERAY_MBSC4_MBC122_OFF (26u)

/** \brief  Length for Ifx_ERAY_MBSC4_Bits.MBC123 */
#define IFX_ERAY_MBSC4_MBC123_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC4_Bits.MBC123 */
#define IFX_ERAY_MBSC4_MBC123_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC4_Bits.MBC123 */
#define IFX_ERAY_MBSC4_MBC123_OFF (27u)

/** \brief  Length for Ifx_ERAY_MBSC4_Bits.MBC124 */
#define IFX_ERAY_MBSC4_MBC124_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC4_Bits.MBC124 */
#define IFX_ERAY_MBSC4_MBC124_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC4_Bits.MBC124 */
#define IFX_ERAY_MBSC4_MBC124_OFF (28u)

/** \brief  Length for Ifx_ERAY_MBSC4_Bits.MBC125 */
#define IFX_ERAY_MBSC4_MBC125_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC4_Bits.MBC125 */
#define IFX_ERAY_MBSC4_MBC125_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC4_Bits.MBC125 */
#define IFX_ERAY_MBSC4_MBC125_OFF (29u)

/** \brief  Length for Ifx_ERAY_MBSC4_Bits.MBC126 */
#define IFX_ERAY_MBSC4_MBC126_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC4_Bits.MBC126 */
#define IFX_ERAY_MBSC4_MBC126_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC4_Bits.MBC126 */
#define IFX_ERAY_MBSC4_MBC126_OFF (30u)

/** \brief  Length for Ifx_ERAY_MBSC4_Bits.MBC127 */
#define IFX_ERAY_MBSC4_MBC127_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC4_Bits.MBC127 */
#define IFX_ERAY_MBSC4_MBC127_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC4_Bits.MBC127 */
#define IFX_ERAY_MBSC4_MBC127_OFF (31u)

/** \brief  Length for Ifx_ERAY_MBSC4_Bits.MBC96 */
#define IFX_ERAY_MBSC4_MBC96_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC4_Bits.MBC96 */
#define IFX_ERAY_MBSC4_MBC96_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC4_Bits.MBC96 */
#define IFX_ERAY_MBSC4_MBC96_OFF (0u)

/** \brief  Length for Ifx_ERAY_MBSC4_Bits.MBC97 */
#define IFX_ERAY_MBSC4_MBC97_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC4_Bits.MBC97 */
#define IFX_ERAY_MBSC4_MBC97_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC4_Bits.MBC97 */
#define IFX_ERAY_MBSC4_MBC97_OFF (1u)

/** \brief  Length for Ifx_ERAY_MBSC4_Bits.MBC98 */
#define IFX_ERAY_MBSC4_MBC98_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC4_Bits.MBC98 */
#define IFX_ERAY_MBSC4_MBC98_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC4_Bits.MBC98 */
#define IFX_ERAY_MBSC4_MBC98_OFF (2u)

/** \brief  Length for Ifx_ERAY_MBSC4_Bits.MBC99 */
#define IFX_ERAY_MBSC4_MBC99_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MBSC4_Bits.MBC99 */
#define IFX_ERAY_MBSC4_MBC99_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MBSC4_Bits.MBC99 */
#define IFX_ERAY_MBSC4_MBC99_OFF (3u)

/** \brief  Length for Ifx_ERAY_MHDC_Bits.SFDL */
#define IFX_ERAY_MHDC_SFDL_LEN (7u)

/** \brief  Mask for Ifx_ERAY_MHDC_Bits.SFDL */
#define IFX_ERAY_MHDC_SFDL_MSK (0x7fu)

/** \brief  Offset for Ifx_ERAY_MHDC_Bits.SFDL */
#define IFX_ERAY_MHDC_SFDL_OFF (0u)

/** \brief  Length for Ifx_ERAY_MHDC_Bits.SLT */
#define IFX_ERAY_MHDC_SLT_LEN (13u)

/** \brief  Mask for Ifx_ERAY_MHDC_Bits.SLT */
#define IFX_ERAY_MHDC_SLT_MSK (0x1fffu)

/** \brief  Offset for Ifx_ERAY_MHDC_Bits.SLT */
#define IFX_ERAY_MHDC_SLT_OFF (16u)

/** \brief  Length for Ifx_ERAY_MHDF_Bits.FNFA */
#define IFX_ERAY_MHDF_FNFA_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MHDF_Bits.FNFA */
#define IFX_ERAY_MHDF_FNFA_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MHDF_Bits.FNFA */
#define IFX_ERAY_MHDF_FNFA_OFF (2u)

/** \brief  Length for Ifx_ERAY_MHDF_Bits.FNFB */
#define IFX_ERAY_MHDF_FNFB_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MHDF_Bits.FNFB */
#define IFX_ERAY_MHDF_FNFB_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MHDF_Bits.FNFB */
#define IFX_ERAY_MHDF_FNFB_OFF (3u)

/** \brief  Length for Ifx_ERAY_MHDF_Bits.SNUA */
#define IFX_ERAY_MHDF_SNUA_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MHDF_Bits.SNUA */
#define IFX_ERAY_MHDF_SNUA_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MHDF_Bits.SNUA */
#define IFX_ERAY_MHDF_SNUA_OFF (0u)

/** \brief  Length for Ifx_ERAY_MHDF_Bits.SNUB */
#define IFX_ERAY_MHDF_SNUB_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MHDF_Bits.SNUB */
#define IFX_ERAY_MHDF_SNUB_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MHDF_Bits.SNUB */
#define IFX_ERAY_MHDF_SNUB_OFF (1u)

/** \brief  Length for Ifx_ERAY_MHDF_Bits.TBFA */
#define IFX_ERAY_MHDF_TBFA_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MHDF_Bits.TBFA */
#define IFX_ERAY_MHDF_TBFA_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MHDF_Bits.TBFA */
#define IFX_ERAY_MHDF_TBFA_OFF (4u)

/** \brief  Length for Ifx_ERAY_MHDF_Bits.TBFB */
#define IFX_ERAY_MHDF_TBFB_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MHDF_Bits.TBFB */
#define IFX_ERAY_MHDF_TBFB_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MHDF_Bits.TBFB */
#define IFX_ERAY_MHDF_TBFB_OFF (5u)

/** \brief  Length for Ifx_ERAY_MHDF_Bits.TNSA */
#define IFX_ERAY_MHDF_TNSA_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MHDF_Bits.TNSA */
#define IFX_ERAY_MHDF_TNSA_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MHDF_Bits.TNSA */
#define IFX_ERAY_MHDF_TNSA_OFF (6u)

/** \brief  Length for Ifx_ERAY_MHDF_Bits.TNSB */
#define IFX_ERAY_MHDF_TNSB_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MHDF_Bits.TNSB */
#define IFX_ERAY_MHDF_TNSB_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MHDF_Bits.TNSB */
#define IFX_ERAY_MHDF_TNSB_OFF (7u)

/** \brief  Length for Ifx_ERAY_MHDF_Bits.WAHP */
#define IFX_ERAY_MHDF_WAHP_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MHDF_Bits.WAHP */
#define IFX_ERAY_MHDF_WAHP_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MHDF_Bits.WAHP */
#define IFX_ERAY_MHDF_WAHP_OFF (8u)

/** \brief  Length for Ifx_ERAY_MHDS_Bits.CRAM */
#define IFX_ERAY_MHDS_CRAM_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MHDS_Bits.CRAM */
#define IFX_ERAY_MHDS_CRAM_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MHDS_Bits.CRAM */
#define IFX_ERAY_MHDS_CRAM_OFF (7u)

/** \brief  Length for Ifx_ERAY_MHDS_Bits.EIBF */
#define IFX_ERAY_MHDS_EIBF_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MHDS_Bits.EIBF */
#define IFX_ERAY_MHDS_EIBF_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MHDS_Bits.EIBF */
#define IFX_ERAY_MHDS_EIBF_OFF (0u)

/** \brief  Length for Ifx_ERAY_MHDS_Bits.EMR */
#define IFX_ERAY_MHDS_EMR_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MHDS_Bits.EMR */
#define IFX_ERAY_MHDS_EMR_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MHDS_Bits.EMR */
#define IFX_ERAY_MHDS_EMR_OFF (2u)

/** \brief  Length for Ifx_ERAY_MHDS_Bits.EOBF */
#define IFX_ERAY_MHDS_EOBF_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MHDS_Bits.EOBF */
#define IFX_ERAY_MHDS_EOBF_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MHDS_Bits.EOBF */
#define IFX_ERAY_MHDS_EOBF_OFF (1u)

/** \brief  Length for Ifx_ERAY_MHDS_Bits.ETBF1 */
#define IFX_ERAY_MHDS_ETBF1_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MHDS_Bits.ETBF1 */
#define IFX_ERAY_MHDS_ETBF1_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MHDS_Bits.ETBF1 */
#define IFX_ERAY_MHDS_ETBF1_OFF (3u)

/** \brief  Length for Ifx_ERAY_MHDS_Bits.ETBF2 */
#define IFX_ERAY_MHDS_ETBF2_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MHDS_Bits.ETBF2 */
#define IFX_ERAY_MHDS_ETBF2_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MHDS_Bits.ETBF2 */
#define IFX_ERAY_MHDS_ETBF2_OFF (4u)

/** \brief  Length for Ifx_ERAY_MHDS_Bits.FMB */
#define IFX_ERAY_MHDS_FMB_LEN (7u)

/** \brief  Mask for Ifx_ERAY_MHDS_Bits.FMB */
#define IFX_ERAY_MHDS_FMB_MSK (0x7fu)

/** \brief  Offset for Ifx_ERAY_MHDS_Bits.FMB */
#define IFX_ERAY_MHDS_FMB_OFF (8u)

/** \brief  Length for Ifx_ERAY_MHDS_Bits.FMBD */
#define IFX_ERAY_MHDS_FMBD_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MHDS_Bits.FMBD */
#define IFX_ERAY_MHDS_FMBD_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MHDS_Bits.FMBD */
#define IFX_ERAY_MHDS_FMBD_OFF (5u)

/** \brief  Length for Ifx_ERAY_MHDS_Bits.MBT */
#define IFX_ERAY_MHDS_MBT_LEN (7u)

/** \brief  Mask for Ifx_ERAY_MHDS_Bits.MBT */
#define IFX_ERAY_MHDS_MBT_MSK (0x7fu)

/** \brief  Offset for Ifx_ERAY_MHDS_Bits.MBT */
#define IFX_ERAY_MHDS_MBT_OFF (16u)

/** \brief  Length for Ifx_ERAY_MHDS_Bits.MBU */
#define IFX_ERAY_MHDS_MBU_LEN (7u)

/** \brief  Mask for Ifx_ERAY_MHDS_Bits.MBU */
#define IFX_ERAY_MHDS_MBU_MSK (0x7fu)

/** \brief  Offset for Ifx_ERAY_MHDS_Bits.MBU */
#define IFX_ERAY_MHDS_MBU_OFF (24u)

/** \brief  Length for Ifx_ERAY_MHDS_Bits.MFMB */
#define IFX_ERAY_MHDS_MFMB_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MHDS_Bits.MFMB */
#define IFX_ERAY_MHDS_MFMB_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MHDS_Bits.MFMB */
#define IFX_ERAY_MHDS_MFMB_OFF (6u)

/** \brief  Length for Ifx_ERAY_MRC_Bits.FDB */
#define IFX_ERAY_MRC_FDB_LEN (8u)

/** \brief  Mask for Ifx_ERAY_MRC_Bits.FDB */
#define IFX_ERAY_MRC_FDB_MSK (0xffu)

/** \brief  Offset for Ifx_ERAY_MRC_Bits.FDB */
#define IFX_ERAY_MRC_FDB_OFF (0u)

/** \brief  Length for Ifx_ERAY_MRC_Bits.FFB */
#define IFX_ERAY_MRC_FFB_LEN (8u)

/** \brief  Mask for Ifx_ERAY_MRC_Bits.FFB */
#define IFX_ERAY_MRC_FFB_MSK (0xffu)

/** \brief  Offset for Ifx_ERAY_MRC_Bits.FFB */
#define IFX_ERAY_MRC_FFB_OFF (8u)

/** \brief  Length for Ifx_ERAY_MRC_Bits.LCB */
#define IFX_ERAY_MRC_LCB_LEN (8u)

/** \brief  Mask for Ifx_ERAY_MRC_Bits.LCB */
#define IFX_ERAY_MRC_LCB_MSK (0xffu)

/** \brief  Offset for Ifx_ERAY_MRC_Bits.LCB */
#define IFX_ERAY_MRC_LCB_OFF (16u)

/** \brief  Length for Ifx_ERAY_MRC_Bits.SEC */
#define IFX_ERAY_MRC_SEC_LEN (2u)

/** \brief  Mask for Ifx_ERAY_MRC_Bits.SEC */
#define IFX_ERAY_MRC_SEC_MSK (0x3u)

/** \brief  Offset for Ifx_ERAY_MRC_Bits.SEC */
#define IFX_ERAY_MRC_SEC_OFF (24u)

/** \brief  Length for Ifx_ERAY_MRC_Bits.SPLM */
#define IFX_ERAY_MRC_SPLM_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MRC_Bits.SPLM */
#define IFX_ERAY_MRC_SPLM_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MRC_Bits.SPLM */
#define IFX_ERAY_MRC_SPLM_OFF (26u)

/** \brief  Length for Ifx_ERAY_MSIC1_Bits.MSIP0 */
#define IFX_ERAY_MSIC1_MSIP0_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC1_Bits.MSIP0 */
#define IFX_ERAY_MSIC1_MSIP0_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC1_Bits.MSIP0 */
#define IFX_ERAY_MSIC1_MSIP0_OFF (0u)

/** \brief  Length for Ifx_ERAY_MSIC1_Bits.MSIP10 */
#define IFX_ERAY_MSIC1_MSIP10_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC1_Bits.MSIP10 */
#define IFX_ERAY_MSIC1_MSIP10_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC1_Bits.MSIP10 */
#define IFX_ERAY_MSIC1_MSIP10_OFF (10u)

/** \brief  Length for Ifx_ERAY_MSIC1_Bits.MSIP11 */
#define IFX_ERAY_MSIC1_MSIP11_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC1_Bits.MSIP11 */
#define IFX_ERAY_MSIC1_MSIP11_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC1_Bits.MSIP11 */
#define IFX_ERAY_MSIC1_MSIP11_OFF (11u)

/** \brief  Length for Ifx_ERAY_MSIC1_Bits.MSIP12 */
#define IFX_ERAY_MSIC1_MSIP12_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC1_Bits.MSIP12 */
#define IFX_ERAY_MSIC1_MSIP12_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC1_Bits.MSIP12 */
#define IFX_ERAY_MSIC1_MSIP12_OFF (12u)

/** \brief  Length for Ifx_ERAY_MSIC1_Bits.MSIP13 */
#define IFX_ERAY_MSIC1_MSIP13_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC1_Bits.MSIP13 */
#define IFX_ERAY_MSIC1_MSIP13_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC1_Bits.MSIP13 */
#define IFX_ERAY_MSIC1_MSIP13_OFF (13u)

/** \brief  Length for Ifx_ERAY_MSIC1_Bits.MSIP14 */
#define IFX_ERAY_MSIC1_MSIP14_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC1_Bits.MSIP14 */
#define IFX_ERAY_MSIC1_MSIP14_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC1_Bits.MSIP14 */
#define IFX_ERAY_MSIC1_MSIP14_OFF (14u)

/** \brief  Length for Ifx_ERAY_MSIC1_Bits.MSIP15 */
#define IFX_ERAY_MSIC1_MSIP15_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC1_Bits.MSIP15 */
#define IFX_ERAY_MSIC1_MSIP15_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC1_Bits.MSIP15 */
#define IFX_ERAY_MSIC1_MSIP15_OFF (15u)

/** \brief  Length for Ifx_ERAY_MSIC1_Bits.MSIP16 */
#define IFX_ERAY_MSIC1_MSIP16_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC1_Bits.MSIP16 */
#define IFX_ERAY_MSIC1_MSIP16_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC1_Bits.MSIP16 */
#define IFX_ERAY_MSIC1_MSIP16_OFF (16u)

/** \brief  Length for Ifx_ERAY_MSIC1_Bits.MSIP17 */
#define IFX_ERAY_MSIC1_MSIP17_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC1_Bits.MSIP17 */
#define IFX_ERAY_MSIC1_MSIP17_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC1_Bits.MSIP17 */
#define IFX_ERAY_MSIC1_MSIP17_OFF (17u)

/** \brief  Length for Ifx_ERAY_MSIC1_Bits.MSIP18 */
#define IFX_ERAY_MSIC1_MSIP18_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC1_Bits.MSIP18 */
#define IFX_ERAY_MSIC1_MSIP18_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC1_Bits.MSIP18 */
#define IFX_ERAY_MSIC1_MSIP18_OFF (18u)

/** \brief  Length for Ifx_ERAY_MSIC1_Bits.MSIP19 */
#define IFX_ERAY_MSIC1_MSIP19_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC1_Bits.MSIP19 */
#define IFX_ERAY_MSIC1_MSIP19_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC1_Bits.MSIP19 */
#define IFX_ERAY_MSIC1_MSIP19_OFF (19u)

/** \brief  Length for Ifx_ERAY_MSIC1_Bits.MSIP1 */
#define IFX_ERAY_MSIC1_MSIP1_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC1_Bits.MSIP1 */
#define IFX_ERAY_MSIC1_MSIP1_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC1_Bits.MSIP1 */
#define IFX_ERAY_MSIC1_MSIP1_OFF (1u)

/** \brief  Length for Ifx_ERAY_MSIC1_Bits.MSIP20 */
#define IFX_ERAY_MSIC1_MSIP20_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC1_Bits.MSIP20 */
#define IFX_ERAY_MSIC1_MSIP20_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC1_Bits.MSIP20 */
#define IFX_ERAY_MSIC1_MSIP20_OFF (20u)

/** \brief  Length for Ifx_ERAY_MSIC1_Bits.MSIP21 */
#define IFX_ERAY_MSIC1_MSIP21_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC1_Bits.MSIP21 */
#define IFX_ERAY_MSIC1_MSIP21_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC1_Bits.MSIP21 */
#define IFX_ERAY_MSIC1_MSIP21_OFF (21u)

/** \brief  Length for Ifx_ERAY_MSIC1_Bits.MSIP22 */
#define IFX_ERAY_MSIC1_MSIP22_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC1_Bits.MSIP22 */
#define IFX_ERAY_MSIC1_MSIP22_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC1_Bits.MSIP22 */
#define IFX_ERAY_MSIC1_MSIP22_OFF (22u)

/** \brief  Length for Ifx_ERAY_MSIC1_Bits.MSIP23 */
#define IFX_ERAY_MSIC1_MSIP23_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC1_Bits.MSIP23 */
#define IFX_ERAY_MSIC1_MSIP23_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC1_Bits.MSIP23 */
#define IFX_ERAY_MSIC1_MSIP23_OFF (23u)

/** \brief  Length for Ifx_ERAY_MSIC1_Bits.MSIP24 */
#define IFX_ERAY_MSIC1_MSIP24_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC1_Bits.MSIP24 */
#define IFX_ERAY_MSIC1_MSIP24_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC1_Bits.MSIP24 */
#define IFX_ERAY_MSIC1_MSIP24_OFF (24u)

/** \brief  Length for Ifx_ERAY_MSIC1_Bits.MSIP25 */
#define IFX_ERAY_MSIC1_MSIP25_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC1_Bits.MSIP25 */
#define IFX_ERAY_MSIC1_MSIP25_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC1_Bits.MSIP25 */
#define IFX_ERAY_MSIC1_MSIP25_OFF (25u)

/** \brief  Length for Ifx_ERAY_MSIC1_Bits.MSIP26 */
#define IFX_ERAY_MSIC1_MSIP26_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC1_Bits.MSIP26 */
#define IFX_ERAY_MSIC1_MSIP26_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC1_Bits.MSIP26 */
#define IFX_ERAY_MSIC1_MSIP26_OFF (26u)

/** \brief  Length for Ifx_ERAY_MSIC1_Bits.MSIP27 */
#define IFX_ERAY_MSIC1_MSIP27_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC1_Bits.MSIP27 */
#define IFX_ERAY_MSIC1_MSIP27_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC1_Bits.MSIP27 */
#define IFX_ERAY_MSIC1_MSIP27_OFF (27u)

/** \brief  Length for Ifx_ERAY_MSIC1_Bits.MSIP28 */
#define IFX_ERAY_MSIC1_MSIP28_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC1_Bits.MSIP28 */
#define IFX_ERAY_MSIC1_MSIP28_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC1_Bits.MSIP28 */
#define IFX_ERAY_MSIC1_MSIP28_OFF (28u)

/** \brief  Length for Ifx_ERAY_MSIC1_Bits.MSIP29 */
#define IFX_ERAY_MSIC1_MSIP29_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC1_Bits.MSIP29 */
#define IFX_ERAY_MSIC1_MSIP29_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC1_Bits.MSIP29 */
#define IFX_ERAY_MSIC1_MSIP29_OFF (29u)

/** \brief  Length for Ifx_ERAY_MSIC1_Bits.MSIP2 */
#define IFX_ERAY_MSIC1_MSIP2_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC1_Bits.MSIP2 */
#define IFX_ERAY_MSIC1_MSIP2_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC1_Bits.MSIP2 */
#define IFX_ERAY_MSIC1_MSIP2_OFF (2u)

/** \brief  Length for Ifx_ERAY_MSIC1_Bits.MSIP30 */
#define IFX_ERAY_MSIC1_MSIP30_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC1_Bits.MSIP30 */
#define IFX_ERAY_MSIC1_MSIP30_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC1_Bits.MSIP30 */
#define IFX_ERAY_MSIC1_MSIP30_OFF (30u)

/** \brief  Length for Ifx_ERAY_MSIC1_Bits.MSIP31 */
#define IFX_ERAY_MSIC1_MSIP31_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC1_Bits.MSIP31 */
#define IFX_ERAY_MSIC1_MSIP31_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC1_Bits.MSIP31 */
#define IFX_ERAY_MSIC1_MSIP31_OFF (31u)

/** \brief  Length for Ifx_ERAY_MSIC1_Bits.MSIP3 */
#define IFX_ERAY_MSIC1_MSIP3_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC1_Bits.MSIP3 */
#define IFX_ERAY_MSIC1_MSIP3_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC1_Bits.MSIP3 */
#define IFX_ERAY_MSIC1_MSIP3_OFF (3u)

/** \brief  Length for Ifx_ERAY_MSIC1_Bits.MSIP4 */
#define IFX_ERAY_MSIC1_MSIP4_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC1_Bits.MSIP4 */
#define IFX_ERAY_MSIC1_MSIP4_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC1_Bits.MSIP4 */
#define IFX_ERAY_MSIC1_MSIP4_OFF (4u)

/** \brief  Length for Ifx_ERAY_MSIC1_Bits.MSIP5 */
#define IFX_ERAY_MSIC1_MSIP5_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC1_Bits.MSIP5 */
#define IFX_ERAY_MSIC1_MSIP5_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC1_Bits.MSIP5 */
#define IFX_ERAY_MSIC1_MSIP5_OFF (5u)

/** \brief  Length for Ifx_ERAY_MSIC1_Bits.MSIP6 */
#define IFX_ERAY_MSIC1_MSIP6_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC1_Bits.MSIP6 */
#define IFX_ERAY_MSIC1_MSIP6_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC1_Bits.MSIP6 */
#define IFX_ERAY_MSIC1_MSIP6_OFF (6u)

/** \brief  Length for Ifx_ERAY_MSIC1_Bits.MSIP7 */
#define IFX_ERAY_MSIC1_MSIP7_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC1_Bits.MSIP7 */
#define IFX_ERAY_MSIC1_MSIP7_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC1_Bits.MSIP7 */
#define IFX_ERAY_MSIC1_MSIP7_OFF (7u)

/** \brief  Length for Ifx_ERAY_MSIC1_Bits.MSIP8 */
#define IFX_ERAY_MSIC1_MSIP8_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC1_Bits.MSIP8 */
#define IFX_ERAY_MSIC1_MSIP8_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC1_Bits.MSIP8 */
#define IFX_ERAY_MSIC1_MSIP8_OFF (8u)

/** \brief  Length for Ifx_ERAY_MSIC1_Bits.MSIP9 */
#define IFX_ERAY_MSIC1_MSIP9_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC1_Bits.MSIP9 */
#define IFX_ERAY_MSIC1_MSIP9_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC1_Bits.MSIP9 */
#define IFX_ERAY_MSIC1_MSIP9_OFF (9u)

/** \brief  Length for Ifx_ERAY_MSIC2_Bits.MSIP32 */
#define IFX_ERAY_MSIC2_MSIP32_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC2_Bits.MSIP32 */
#define IFX_ERAY_MSIC2_MSIP32_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC2_Bits.MSIP32 */
#define IFX_ERAY_MSIC2_MSIP32_OFF (0u)

/** \brief  Length for Ifx_ERAY_MSIC2_Bits.MSIP33 */
#define IFX_ERAY_MSIC2_MSIP33_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC2_Bits.MSIP33 */
#define IFX_ERAY_MSIC2_MSIP33_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC2_Bits.MSIP33 */
#define IFX_ERAY_MSIC2_MSIP33_OFF (1u)

/** \brief  Length for Ifx_ERAY_MSIC2_Bits.MSIP34 */
#define IFX_ERAY_MSIC2_MSIP34_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC2_Bits.MSIP34 */
#define IFX_ERAY_MSIC2_MSIP34_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC2_Bits.MSIP34 */
#define IFX_ERAY_MSIC2_MSIP34_OFF (2u)

/** \brief  Length for Ifx_ERAY_MSIC2_Bits.MSIP35 */
#define IFX_ERAY_MSIC2_MSIP35_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC2_Bits.MSIP35 */
#define IFX_ERAY_MSIC2_MSIP35_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC2_Bits.MSIP35 */
#define IFX_ERAY_MSIC2_MSIP35_OFF (3u)

/** \brief  Length for Ifx_ERAY_MSIC2_Bits.MSIP36 */
#define IFX_ERAY_MSIC2_MSIP36_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC2_Bits.MSIP36 */
#define IFX_ERAY_MSIC2_MSIP36_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC2_Bits.MSIP36 */
#define IFX_ERAY_MSIC2_MSIP36_OFF (4u)

/** \brief  Length for Ifx_ERAY_MSIC2_Bits.MSIP37 */
#define IFX_ERAY_MSIC2_MSIP37_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC2_Bits.MSIP37 */
#define IFX_ERAY_MSIC2_MSIP37_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC2_Bits.MSIP37 */
#define IFX_ERAY_MSIC2_MSIP37_OFF (5u)

/** \brief  Length for Ifx_ERAY_MSIC2_Bits.MSIP38 */
#define IFX_ERAY_MSIC2_MSIP38_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC2_Bits.MSIP38 */
#define IFX_ERAY_MSIC2_MSIP38_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC2_Bits.MSIP38 */
#define IFX_ERAY_MSIC2_MSIP38_OFF (6u)

/** \brief  Length for Ifx_ERAY_MSIC2_Bits.MSIP39 */
#define IFX_ERAY_MSIC2_MSIP39_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC2_Bits.MSIP39 */
#define IFX_ERAY_MSIC2_MSIP39_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC2_Bits.MSIP39 */
#define IFX_ERAY_MSIC2_MSIP39_OFF (7u)

/** \brief  Length for Ifx_ERAY_MSIC2_Bits.MSIP40 */
#define IFX_ERAY_MSIC2_MSIP40_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC2_Bits.MSIP40 */
#define IFX_ERAY_MSIC2_MSIP40_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC2_Bits.MSIP40 */
#define IFX_ERAY_MSIC2_MSIP40_OFF (8u)

/** \brief  Length for Ifx_ERAY_MSIC2_Bits.MSIP41 */
#define IFX_ERAY_MSIC2_MSIP41_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC2_Bits.MSIP41 */
#define IFX_ERAY_MSIC2_MSIP41_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC2_Bits.MSIP41 */
#define IFX_ERAY_MSIC2_MSIP41_OFF (9u)

/** \brief  Length for Ifx_ERAY_MSIC2_Bits.MSIP42 */
#define IFX_ERAY_MSIC2_MSIP42_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC2_Bits.MSIP42 */
#define IFX_ERAY_MSIC2_MSIP42_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC2_Bits.MSIP42 */
#define IFX_ERAY_MSIC2_MSIP42_OFF (10u)

/** \brief  Length for Ifx_ERAY_MSIC2_Bits.MSIP43 */
#define IFX_ERAY_MSIC2_MSIP43_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC2_Bits.MSIP43 */
#define IFX_ERAY_MSIC2_MSIP43_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC2_Bits.MSIP43 */
#define IFX_ERAY_MSIC2_MSIP43_OFF (11u)

/** \brief  Length for Ifx_ERAY_MSIC2_Bits.MSIP44 */
#define IFX_ERAY_MSIC2_MSIP44_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC2_Bits.MSIP44 */
#define IFX_ERAY_MSIC2_MSIP44_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC2_Bits.MSIP44 */
#define IFX_ERAY_MSIC2_MSIP44_OFF (12u)

/** \brief  Length for Ifx_ERAY_MSIC2_Bits.MSIP45 */
#define IFX_ERAY_MSIC2_MSIP45_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC2_Bits.MSIP45 */
#define IFX_ERAY_MSIC2_MSIP45_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC2_Bits.MSIP45 */
#define IFX_ERAY_MSIC2_MSIP45_OFF (13u)

/** \brief  Length for Ifx_ERAY_MSIC2_Bits.MSIP46 */
#define IFX_ERAY_MSIC2_MSIP46_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC2_Bits.MSIP46 */
#define IFX_ERAY_MSIC2_MSIP46_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC2_Bits.MSIP46 */
#define IFX_ERAY_MSIC2_MSIP46_OFF (14u)

/** \brief  Length for Ifx_ERAY_MSIC2_Bits.MSIP47 */
#define IFX_ERAY_MSIC2_MSIP47_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC2_Bits.MSIP47 */
#define IFX_ERAY_MSIC2_MSIP47_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC2_Bits.MSIP47 */
#define IFX_ERAY_MSIC2_MSIP47_OFF (15u)

/** \brief  Length for Ifx_ERAY_MSIC2_Bits.MSIP48 */
#define IFX_ERAY_MSIC2_MSIP48_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC2_Bits.MSIP48 */
#define IFX_ERAY_MSIC2_MSIP48_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC2_Bits.MSIP48 */
#define IFX_ERAY_MSIC2_MSIP48_OFF (16u)

/** \brief  Length for Ifx_ERAY_MSIC2_Bits.MSIP49 */
#define IFX_ERAY_MSIC2_MSIP49_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC2_Bits.MSIP49 */
#define IFX_ERAY_MSIC2_MSIP49_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC2_Bits.MSIP49 */
#define IFX_ERAY_MSIC2_MSIP49_OFF (17u)

/** \brief  Length for Ifx_ERAY_MSIC2_Bits.MSIP50 */
#define IFX_ERAY_MSIC2_MSIP50_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC2_Bits.MSIP50 */
#define IFX_ERAY_MSIC2_MSIP50_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC2_Bits.MSIP50 */
#define IFX_ERAY_MSIC2_MSIP50_OFF (18u)

/** \brief  Length for Ifx_ERAY_MSIC2_Bits.MSIP51 */
#define IFX_ERAY_MSIC2_MSIP51_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC2_Bits.MSIP51 */
#define IFX_ERAY_MSIC2_MSIP51_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC2_Bits.MSIP51 */
#define IFX_ERAY_MSIC2_MSIP51_OFF (19u)

/** \brief  Length for Ifx_ERAY_MSIC2_Bits.MSIP52 */
#define IFX_ERAY_MSIC2_MSIP52_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC2_Bits.MSIP52 */
#define IFX_ERAY_MSIC2_MSIP52_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC2_Bits.MSIP52 */
#define IFX_ERAY_MSIC2_MSIP52_OFF (20u)

/** \brief  Length for Ifx_ERAY_MSIC2_Bits.MSIP53 */
#define IFX_ERAY_MSIC2_MSIP53_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC2_Bits.MSIP53 */
#define IFX_ERAY_MSIC2_MSIP53_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC2_Bits.MSIP53 */
#define IFX_ERAY_MSIC2_MSIP53_OFF (21u)

/** \brief  Length for Ifx_ERAY_MSIC2_Bits.MSIP54 */
#define IFX_ERAY_MSIC2_MSIP54_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC2_Bits.MSIP54 */
#define IFX_ERAY_MSIC2_MSIP54_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC2_Bits.MSIP54 */
#define IFX_ERAY_MSIC2_MSIP54_OFF (22u)

/** \brief  Length for Ifx_ERAY_MSIC2_Bits.MSIP55 */
#define IFX_ERAY_MSIC2_MSIP55_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC2_Bits.MSIP55 */
#define IFX_ERAY_MSIC2_MSIP55_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC2_Bits.MSIP55 */
#define IFX_ERAY_MSIC2_MSIP55_OFF (23u)

/** \brief  Length for Ifx_ERAY_MSIC2_Bits.MSIP56 */
#define IFX_ERAY_MSIC2_MSIP56_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC2_Bits.MSIP56 */
#define IFX_ERAY_MSIC2_MSIP56_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC2_Bits.MSIP56 */
#define IFX_ERAY_MSIC2_MSIP56_OFF (24u)

/** \brief  Length for Ifx_ERAY_MSIC2_Bits.MSIP57 */
#define IFX_ERAY_MSIC2_MSIP57_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC2_Bits.MSIP57 */
#define IFX_ERAY_MSIC2_MSIP57_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC2_Bits.MSIP57 */
#define IFX_ERAY_MSIC2_MSIP57_OFF (25u)

/** \brief  Length for Ifx_ERAY_MSIC2_Bits.MSIP58 */
#define IFX_ERAY_MSIC2_MSIP58_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC2_Bits.MSIP58 */
#define IFX_ERAY_MSIC2_MSIP58_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC2_Bits.MSIP58 */
#define IFX_ERAY_MSIC2_MSIP58_OFF (26u)

/** \brief  Length for Ifx_ERAY_MSIC2_Bits.MSIP59 */
#define IFX_ERAY_MSIC2_MSIP59_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC2_Bits.MSIP59 */
#define IFX_ERAY_MSIC2_MSIP59_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC2_Bits.MSIP59 */
#define IFX_ERAY_MSIC2_MSIP59_OFF (27u)

/** \brief  Length for Ifx_ERAY_MSIC2_Bits.MSIP60 */
#define IFX_ERAY_MSIC2_MSIP60_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC2_Bits.MSIP60 */
#define IFX_ERAY_MSIC2_MSIP60_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC2_Bits.MSIP60 */
#define IFX_ERAY_MSIC2_MSIP60_OFF (28u)

/** \brief  Length for Ifx_ERAY_MSIC2_Bits.MSIP61 */
#define IFX_ERAY_MSIC2_MSIP61_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC2_Bits.MSIP61 */
#define IFX_ERAY_MSIC2_MSIP61_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC2_Bits.MSIP61 */
#define IFX_ERAY_MSIC2_MSIP61_OFF (29u)

/** \brief  Length for Ifx_ERAY_MSIC2_Bits.MSIP62 */
#define IFX_ERAY_MSIC2_MSIP62_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC2_Bits.MSIP62 */
#define IFX_ERAY_MSIC2_MSIP62_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC2_Bits.MSIP62 */
#define IFX_ERAY_MSIC2_MSIP62_OFF (30u)

/** \brief  Length for Ifx_ERAY_MSIC2_Bits.MSIP63 */
#define IFX_ERAY_MSIC2_MSIP63_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC2_Bits.MSIP63 */
#define IFX_ERAY_MSIC2_MSIP63_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC2_Bits.MSIP63 */
#define IFX_ERAY_MSIC2_MSIP63_OFF (31u)

/** \brief  Length for Ifx_ERAY_MSIC3_Bits.MSIP64 */
#define IFX_ERAY_MSIC3_MSIP64_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC3_Bits.MSIP64 */
#define IFX_ERAY_MSIC3_MSIP64_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC3_Bits.MSIP64 */
#define IFX_ERAY_MSIC3_MSIP64_OFF (0u)

/** \brief  Length for Ifx_ERAY_MSIC3_Bits.MSIP65 */
#define IFX_ERAY_MSIC3_MSIP65_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC3_Bits.MSIP65 */
#define IFX_ERAY_MSIC3_MSIP65_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC3_Bits.MSIP65 */
#define IFX_ERAY_MSIC3_MSIP65_OFF (1u)

/** \brief  Length for Ifx_ERAY_MSIC3_Bits.MSIP66 */
#define IFX_ERAY_MSIC3_MSIP66_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC3_Bits.MSIP66 */
#define IFX_ERAY_MSIC3_MSIP66_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC3_Bits.MSIP66 */
#define IFX_ERAY_MSIC3_MSIP66_OFF (2u)

/** \brief  Length for Ifx_ERAY_MSIC3_Bits.MSIP67 */
#define IFX_ERAY_MSIC3_MSIP67_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC3_Bits.MSIP67 */
#define IFX_ERAY_MSIC3_MSIP67_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC3_Bits.MSIP67 */
#define IFX_ERAY_MSIC3_MSIP67_OFF (3u)

/** \brief  Length for Ifx_ERAY_MSIC3_Bits.MSIP68 */
#define IFX_ERAY_MSIC3_MSIP68_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC3_Bits.MSIP68 */
#define IFX_ERAY_MSIC3_MSIP68_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC3_Bits.MSIP68 */
#define IFX_ERAY_MSIC3_MSIP68_OFF (4u)

/** \brief  Length for Ifx_ERAY_MSIC3_Bits.MSIP69 */
#define IFX_ERAY_MSIC3_MSIP69_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC3_Bits.MSIP69 */
#define IFX_ERAY_MSIC3_MSIP69_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC3_Bits.MSIP69 */
#define IFX_ERAY_MSIC3_MSIP69_OFF (5u)

/** \brief  Length for Ifx_ERAY_MSIC3_Bits.MSIP70 */
#define IFX_ERAY_MSIC3_MSIP70_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC3_Bits.MSIP70 */
#define IFX_ERAY_MSIC3_MSIP70_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC3_Bits.MSIP70 */
#define IFX_ERAY_MSIC3_MSIP70_OFF (6u)

/** \brief  Length for Ifx_ERAY_MSIC3_Bits.MSIP71 */
#define IFX_ERAY_MSIC3_MSIP71_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC3_Bits.MSIP71 */
#define IFX_ERAY_MSIC3_MSIP71_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC3_Bits.MSIP71 */
#define IFX_ERAY_MSIC3_MSIP71_OFF (7u)

/** \brief  Length for Ifx_ERAY_MSIC3_Bits.MSIP72 */
#define IFX_ERAY_MSIC3_MSIP72_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC3_Bits.MSIP72 */
#define IFX_ERAY_MSIC3_MSIP72_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC3_Bits.MSIP72 */
#define IFX_ERAY_MSIC3_MSIP72_OFF (8u)

/** \brief  Length for Ifx_ERAY_MSIC3_Bits.MSIP73 */
#define IFX_ERAY_MSIC3_MSIP73_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC3_Bits.MSIP73 */
#define IFX_ERAY_MSIC3_MSIP73_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC3_Bits.MSIP73 */
#define IFX_ERAY_MSIC3_MSIP73_OFF (9u)

/** \brief  Length for Ifx_ERAY_MSIC3_Bits.MSIP74 */
#define IFX_ERAY_MSIC3_MSIP74_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC3_Bits.MSIP74 */
#define IFX_ERAY_MSIC3_MSIP74_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC3_Bits.MSIP74 */
#define IFX_ERAY_MSIC3_MSIP74_OFF (10u)

/** \brief  Length for Ifx_ERAY_MSIC3_Bits.MSIP75 */
#define IFX_ERAY_MSIC3_MSIP75_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC3_Bits.MSIP75 */
#define IFX_ERAY_MSIC3_MSIP75_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC3_Bits.MSIP75 */
#define IFX_ERAY_MSIC3_MSIP75_OFF (11u)

/** \brief  Length for Ifx_ERAY_MSIC3_Bits.MSIP76 */
#define IFX_ERAY_MSIC3_MSIP76_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC3_Bits.MSIP76 */
#define IFX_ERAY_MSIC3_MSIP76_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC3_Bits.MSIP76 */
#define IFX_ERAY_MSIC3_MSIP76_OFF (12u)

/** \brief  Length for Ifx_ERAY_MSIC3_Bits.MSIP77 */
#define IFX_ERAY_MSIC3_MSIP77_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC3_Bits.MSIP77 */
#define IFX_ERAY_MSIC3_MSIP77_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC3_Bits.MSIP77 */
#define IFX_ERAY_MSIC3_MSIP77_OFF (13u)

/** \brief  Length for Ifx_ERAY_MSIC3_Bits.MSIP78 */
#define IFX_ERAY_MSIC3_MSIP78_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC3_Bits.MSIP78 */
#define IFX_ERAY_MSIC3_MSIP78_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC3_Bits.MSIP78 */
#define IFX_ERAY_MSIC3_MSIP78_OFF (14u)

/** \brief  Length for Ifx_ERAY_MSIC3_Bits.MSIP79 */
#define IFX_ERAY_MSIC3_MSIP79_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC3_Bits.MSIP79 */
#define IFX_ERAY_MSIC3_MSIP79_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC3_Bits.MSIP79 */
#define IFX_ERAY_MSIC3_MSIP79_OFF (15u)

/** \brief  Length for Ifx_ERAY_MSIC3_Bits.MSIP80 */
#define IFX_ERAY_MSIC3_MSIP80_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC3_Bits.MSIP80 */
#define IFX_ERAY_MSIC3_MSIP80_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC3_Bits.MSIP80 */
#define IFX_ERAY_MSIC3_MSIP80_OFF (16u)

/** \brief  Length for Ifx_ERAY_MSIC3_Bits.MSIP81 */
#define IFX_ERAY_MSIC3_MSIP81_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC3_Bits.MSIP81 */
#define IFX_ERAY_MSIC3_MSIP81_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC3_Bits.MSIP81 */
#define IFX_ERAY_MSIC3_MSIP81_OFF (17u)

/** \brief  Length for Ifx_ERAY_MSIC3_Bits.MSIP82 */
#define IFX_ERAY_MSIC3_MSIP82_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC3_Bits.MSIP82 */
#define IFX_ERAY_MSIC3_MSIP82_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC3_Bits.MSIP82 */
#define IFX_ERAY_MSIC3_MSIP82_OFF (18u)

/** \brief  Length for Ifx_ERAY_MSIC3_Bits.MSIP83 */
#define IFX_ERAY_MSIC3_MSIP83_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC3_Bits.MSIP83 */
#define IFX_ERAY_MSIC3_MSIP83_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC3_Bits.MSIP83 */
#define IFX_ERAY_MSIC3_MSIP83_OFF (19u)

/** \brief  Length for Ifx_ERAY_MSIC3_Bits.MSIP84 */
#define IFX_ERAY_MSIC3_MSIP84_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC3_Bits.MSIP84 */
#define IFX_ERAY_MSIC3_MSIP84_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC3_Bits.MSIP84 */
#define IFX_ERAY_MSIC3_MSIP84_OFF (20u)

/** \brief  Length for Ifx_ERAY_MSIC3_Bits.MSIP85 */
#define IFX_ERAY_MSIC3_MSIP85_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC3_Bits.MSIP85 */
#define IFX_ERAY_MSIC3_MSIP85_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC3_Bits.MSIP85 */
#define IFX_ERAY_MSIC3_MSIP85_OFF (21u)

/** \brief  Length for Ifx_ERAY_MSIC3_Bits.MSIP86 */
#define IFX_ERAY_MSIC3_MSIP86_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC3_Bits.MSIP86 */
#define IFX_ERAY_MSIC3_MSIP86_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC3_Bits.MSIP86 */
#define IFX_ERAY_MSIC3_MSIP86_OFF (22u)

/** \brief  Length for Ifx_ERAY_MSIC3_Bits.MSIP87 */
#define IFX_ERAY_MSIC3_MSIP87_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC3_Bits.MSIP87 */
#define IFX_ERAY_MSIC3_MSIP87_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC3_Bits.MSIP87 */
#define IFX_ERAY_MSIC3_MSIP87_OFF (23u)

/** \brief  Length for Ifx_ERAY_MSIC3_Bits.MSIP88 */
#define IFX_ERAY_MSIC3_MSIP88_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC3_Bits.MSIP88 */
#define IFX_ERAY_MSIC3_MSIP88_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC3_Bits.MSIP88 */
#define IFX_ERAY_MSIC3_MSIP88_OFF (24u)

/** \brief  Length for Ifx_ERAY_MSIC3_Bits.MSIP89 */
#define IFX_ERAY_MSIC3_MSIP89_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC3_Bits.MSIP89 */
#define IFX_ERAY_MSIC3_MSIP89_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC3_Bits.MSIP89 */
#define IFX_ERAY_MSIC3_MSIP89_OFF (25u)

/** \brief  Length for Ifx_ERAY_MSIC3_Bits.MSIP90 */
#define IFX_ERAY_MSIC3_MSIP90_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC3_Bits.MSIP90 */
#define IFX_ERAY_MSIC3_MSIP90_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC3_Bits.MSIP90 */
#define IFX_ERAY_MSIC3_MSIP90_OFF (26u)

/** \brief  Length for Ifx_ERAY_MSIC3_Bits.MSIP91 */
#define IFX_ERAY_MSIC3_MSIP91_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC3_Bits.MSIP91 */
#define IFX_ERAY_MSIC3_MSIP91_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC3_Bits.MSIP91 */
#define IFX_ERAY_MSIC3_MSIP91_OFF (27u)

/** \brief  Length for Ifx_ERAY_MSIC3_Bits.MSIP92 */
#define IFX_ERAY_MSIC3_MSIP92_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC3_Bits.MSIP92 */
#define IFX_ERAY_MSIC3_MSIP92_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC3_Bits.MSIP92 */
#define IFX_ERAY_MSIC3_MSIP92_OFF (28u)

/** \brief  Length for Ifx_ERAY_MSIC3_Bits.MSIP93 */
#define IFX_ERAY_MSIC3_MSIP93_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC3_Bits.MSIP93 */
#define IFX_ERAY_MSIC3_MSIP93_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC3_Bits.MSIP93 */
#define IFX_ERAY_MSIC3_MSIP93_OFF (29u)

/** \brief  Length for Ifx_ERAY_MSIC3_Bits.MSIP94 */
#define IFX_ERAY_MSIC3_MSIP94_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC3_Bits.MSIP94 */
#define IFX_ERAY_MSIC3_MSIP94_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC3_Bits.MSIP94 */
#define IFX_ERAY_MSIC3_MSIP94_OFF (30u)

/** \brief  Length for Ifx_ERAY_MSIC3_Bits.MSIP95 */
#define IFX_ERAY_MSIC3_MSIP95_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC3_Bits.MSIP95 */
#define IFX_ERAY_MSIC3_MSIP95_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC3_Bits.MSIP95 */
#define IFX_ERAY_MSIC3_MSIP95_OFF (31u)

/** \brief  Length for Ifx_ERAY_MSIC4_Bits.MSIP100 */
#define IFX_ERAY_MSIC4_MSIP100_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC4_Bits.MSIP100 */
#define IFX_ERAY_MSIC4_MSIP100_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC4_Bits.MSIP100 */
#define IFX_ERAY_MSIC4_MSIP100_OFF (4u)

/** \brief  Length for Ifx_ERAY_MSIC4_Bits.MSIP101 */
#define IFX_ERAY_MSIC4_MSIP101_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC4_Bits.MSIP101 */
#define IFX_ERAY_MSIC4_MSIP101_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC4_Bits.MSIP101 */
#define IFX_ERAY_MSIC4_MSIP101_OFF (5u)

/** \brief  Length for Ifx_ERAY_MSIC4_Bits.MSIP102 */
#define IFX_ERAY_MSIC4_MSIP102_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC4_Bits.MSIP102 */
#define IFX_ERAY_MSIC4_MSIP102_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC4_Bits.MSIP102 */
#define IFX_ERAY_MSIC4_MSIP102_OFF (6u)

/** \brief  Length for Ifx_ERAY_MSIC4_Bits.MSIP103 */
#define IFX_ERAY_MSIC4_MSIP103_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC4_Bits.MSIP103 */
#define IFX_ERAY_MSIC4_MSIP103_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC4_Bits.MSIP103 */
#define IFX_ERAY_MSIC4_MSIP103_OFF (7u)

/** \brief  Length for Ifx_ERAY_MSIC4_Bits.MSIP104 */
#define IFX_ERAY_MSIC4_MSIP104_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC4_Bits.MSIP104 */
#define IFX_ERAY_MSIC4_MSIP104_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC4_Bits.MSIP104 */
#define IFX_ERAY_MSIC4_MSIP104_OFF (8u)

/** \brief  Length for Ifx_ERAY_MSIC4_Bits.MSIP105 */
#define IFX_ERAY_MSIC4_MSIP105_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC4_Bits.MSIP105 */
#define IFX_ERAY_MSIC4_MSIP105_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC4_Bits.MSIP105 */
#define IFX_ERAY_MSIC4_MSIP105_OFF (9u)

/** \brief  Length for Ifx_ERAY_MSIC4_Bits.MSIP106 */
#define IFX_ERAY_MSIC4_MSIP106_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC4_Bits.MSIP106 */
#define IFX_ERAY_MSIC4_MSIP106_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC4_Bits.MSIP106 */
#define IFX_ERAY_MSIC4_MSIP106_OFF (10u)

/** \brief  Length for Ifx_ERAY_MSIC4_Bits.MSIP107 */
#define IFX_ERAY_MSIC4_MSIP107_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC4_Bits.MSIP107 */
#define IFX_ERAY_MSIC4_MSIP107_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC4_Bits.MSIP107 */
#define IFX_ERAY_MSIC4_MSIP107_OFF (11u)

/** \brief  Length for Ifx_ERAY_MSIC4_Bits.MSIP108 */
#define IFX_ERAY_MSIC4_MSIP108_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC4_Bits.MSIP108 */
#define IFX_ERAY_MSIC4_MSIP108_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC4_Bits.MSIP108 */
#define IFX_ERAY_MSIC4_MSIP108_OFF (12u)

/** \brief  Length for Ifx_ERAY_MSIC4_Bits.MSIP109 */
#define IFX_ERAY_MSIC4_MSIP109_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC4_Bits.MSIP109 */
#define IFX_ERAY_MSIC4_MSIP109_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC4_Bits.MSIP109 */
#define IFX_ERAY_MSIC4_MSIP109_OFF (13u)

/** \brief  Length for Ifx_ERAY_MSIC4_Bits.MSIP110 */
#define IFX_ERAY_MSIC4_MSIP110_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC4_Bits.MSIP110 */
#define IFX_ERAY_MSIC4_MSIP110_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC4_Bits.MSIP110 */
#define IFX_ERAY_MSIC4_MSIP110_OFF (14u)

/** \brief  Length for Ifx_ERAY_MSIC4_Bits.MSIP111 */
#define IFX_ERAY_MSIC4_MSIP111_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC4_Bits.MSIP111 */
#define IFX_ERAY_MSIC4_MSIP111_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC4_Bits.MSIP111 */
#define IFX_ERAY_MSIC4_MSIP111_OFF (15u)

/** \brief  Length for Ifx_ERAY_MSIC4_Bits.MSIP112 */
#define IFX_ERAY_MSIC4_MSIP112_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC4_Bits.MSIP112 */
#define IFX_ERAY_MSIC4_MSIP112_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC4_Bits.MSIP112 */
#define IFX_ERAY_MSIC4_MSIP112_OFF (16u)

/** \brief  Length for Ifx_ERAY_MSIC4_Bits.MSIP113 */
#define IFX_ERAY_MSIC4_MSIP113_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC4_Bits.MSIP113 */
#define IFX_ERAY_MSIC4_MSIP113_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC4_Bits.MSIP113 */
#define IFX_ERAY_MSIC4_MSIP113_OFF (17u)

/** \brief  Length for Ifx_ERAY_MSIC4_Bits.MSIP114 */
#define IFX_ERAY_MSIC4_MSIP114_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC4_Bits.MSIP114 */
#define IFX_ERAY_MSIC4_MSIP114_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC4_Bits.MSIP114 */
#define IFX_ERAY_MSIC4_MSIP114_OFF (18u)

/** \brief  Length for Ifx_ERAY_MSIC4_Bits.MSIP115 */
#define IFX_ERAY_MSIC4_MSIP115_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC4_Bits.MSIP115 */
#define IFX_ERAY_MSIC4_MSIP115_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC4_Bits.MSIP115 */
#define IFX_ERAY_MSIC4_MSIP115_OFF (19u)

/** \brief  Length for Ifx_ERAY_MSIC4_Bits.MSIP116 */
#define IFX_ERAY_MSIC4_MSIP116_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC4_Bits.MSIP116 */
#define IFX_ERAY_MSIC4_MSIP116_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC4_Bits.MSIP116 */
#define IFX_ERAY_MSIC4_MSIP116_OFF (20u)

/** \brief  Length for Ifx_ERAY_MSIC4_Bits.MSIP117 */
#define IFX_ERAY_MSIC4_MSIP117_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC4_Bits.MSIP117 */
#define IFX_ERAY_MSIC4_MSIP117_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC4_Bits.MSIP117 */
#define IFX_ERAY_MSIC4_MSIP117_OFF (21u)

/** \brief  Length for Ifx_ERAY_MSIC4_Bits.MSIP118 */
#define IFX_ERAY_MSIC4_MSIP118_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC4_Bits.MSIP118 */
#define IFX_ERAY_MSIC4_MSIP118_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC4_Bits.MSIP118 */
#define IFX_ERAY_MSIC4_MSIP118_OFF (22u)

/** \brief  Length for Ifx_ERAY_MSIC4_Bits.MSIP119 */
#define IFX_ERAY_MSIC4_MSIP119_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC4_Bits.MSIP119 */
#define IFX_ERAY_MSIC4_MSIP119_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC4_Bits.MSIP119 */
#define IFX_ERAY_MSIC4_MSIP119_OFF (23u)

/** \brief  Length for Ifx_ERAY_MSIC4_Bits.MSIP120 */
#define IFX_ERAY_MSIC4_MSIP120_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC4_Bits.MSIP120 */
#define IFX_ERAY_MSIC4_MSIP120_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC4_Bits.MSIP120 */
#define IFX_ERAY_MSIC4_MSIP120_OFF (24u)

/** \brief  Length for Ifx_ERAY_MSIC4_Bits.MSIP121 */
#define IFX_ERAY_MSIC4_MSIP121_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC4_Bits.MSIP121 */
#define IFX_ERAY_MSIC4_MSIP121_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC4_Bits.MSIP121 */
#define IFX_ERAY_MSIC4_MSIP121_OFF (25u)

/** \brief  Length for Ifx_ERAY_MSIC4_Bits.MSIP122 */
#define IFX_ERAY_MSIC4_MSIP122_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC4_Bits.MSIP122 */
#define IFX_ERAY_MSIC4_MSIP122_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC4_Bits.MSIP122 */
#define IFX_ERAY_MSIC4_MSIP122_OFF (26u)

/** \brief  Length for Ifx_ERAY_MSIC4_Bits.MSIP123 */
#define IFX_ERAY_MSIC4_MSIP123_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC4_Bits.MSIP123 */
#define IFX_ERAY_MSIC4_MSIP123_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC4_Bits.MSIP123 */
#define IFX_ERAY_MSIC4_MSIP123_OFF (27u)

/** \brief  Length for Ifx_ERAY_MSIC4_Bits.MSIP124 */
#define IFX_ERAY_MSIC4_MSIP124_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC4_Bits.MSIP124 */
#define IFX_ERAY_MSIC4_MSIP124_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC4_Bits.MSIP124 */
#define IFX_ERAY_MSIC4_MSIP124_OFF (28u)

/** \brief  Length for Ifx_ERAY_MSIC4_Bits.MSIP125 */
#define IFX_ERAY_MSIC4_MSIP125_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC4_Bits.MSIP125 */
#define IFX_ERAY_MSIC4_MSIP125_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC4_Bits.MSIP125 */
#define IFX_ERAY_MSIC4_MSIP125_OFF (29u)

/** \brief  Length for Ifx_ERAY_MSIC4_Bits.MSIP126 */
#define IFX_ERAY_MSIC4_MSIP126_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC4_Bits.MSIP126 */
#define IFX_ERAY_MSIC4_MSIP126_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC4_Bits.MSIP126 */
#define IFX_ERAY_MSIC4_MSIP126_OFF (30u)

/** \brief  Length for Ifx_ERAY_MSIC4_Bits.MSIP127 */
#define IFX_ERAY_MSIC4_MSIP127_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC4_Bits.MSIP127 */
#define IFX_ERAY_MSIC4_MSIP127_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC4_Bits.MSIP127 */
#define IFX_ERAY_MSIC4_MSIP127_OFF (31u)

/** \brief  Length for Ifx_ERAY_MSIC4_Bits.MSIP96 */
#define IFX_ERAY_MSIC4_MSIP96_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC4_Bits.MSIP96 */
#define IFX_ERAY_MSIC4_MSIP96_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC4_Bits.MSIP96 */
#define IFX_ERAY_MSIC4_MSIP96_OFF (0u)

/** \brief  Length for Ifx_ERAY_MSIC4_Bits.MSIP97 */
#define IFX_ERAY_MSIC4_MSIP97_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC4_Bits.MSIP97 */
#define IFX_ERAY_MSIC4_MSIP97_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC4_Bits.MSIP97 */
#define IFX_ERAY_MSIC4_MSIP97_OFF (1u)

/** \brief  Length for Ifx_ERAY_MSIC4_Bits.MSIP98 */
#define IFX_ERAY_MSIC4_MSIP98_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC4_Bits.MSIP98 */
#define IFX_ERAY_MSIC4_MSIP98_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC4_Bits.MSIP98 */
#define IFX_ERAY_MSIC4_MSIP98_OFF (2u)

/** \brief  Length for Ifx_ERAY_MSIC4_Bits.MSIP99 */
#define IFX_ERAY_MSIC4_MSIP99_LEN (1u)

/** \brief  Mask for Ifx_ERAY_MSIC4_Bits.MSIP99 */
#define IFX_ERAY_MSIC4_MSIP99_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_MSIC4_Bits.MSIP99 */
#define IFX_ERAY_MSIC4_MSIP99_OFF (3u)

/** \brief  Length for Ifx_ERAY_MTCCV_Bits.CCV */
#define IFX_ERAY_MTCCV_CCV_LEN (6u)

/** \brief  Mask for Ifx_ERAY_MTCCV_Bits.CCV */
#define IFX_ERAY_MTCCV_CCV_MSK (0x3fu)

/** \brief  Offset for Ifx_ERAY_MTCCV_Bits.CCV */
#define IFX_ERAY_MTCCV_CCV_OFF (16u)

/** \brief  Length for Ifx_ERAY_MTCCV_Bits.MTV */
#define IFX_ERAY_MTCCV_MTV_LEN (14u)

/** \brief  Mask for Ifx_ERAY_MTCCV_Bits.MTV */
#define IFX_ERAY_MTCCV_MTV_MSK (0x3fffu)

/** \brief  Offset for Ifx_ERAY_MTCCV_Bits.MTV */
#define IFX_ERAY_MTCCV_MTV_OFF (0u)

/** \brief  Length for Ifx_ERAY_NDAT1_Bits.ND0 */
#define IFX_ERAY_NDAT1_ND0_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT1_Bits.ND0 */
#define IFX_ERAY_NDAT1_ND0_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT1_Bits.ND0 */
#define IFX_ERAY_NDAT1_ND0_OFF (0u)

/** \brief  Length for Ifx_ERAY_NDAT1_Bits.ND10 */
#define IFX_ERAY_NDAT1_ND10_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT1_Bits.ND10 */
#define IFX_ERAY_NDAT1_ND10_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT1_Bits.ND10 */
#define IFX_ERAY_NDAT1_ND10_OFF (10u)

/** \brief  Length for Ifx_ERAY_NDAT1_Bits.ND11 */
#define IFX_ERAY_NDAT1_ND11_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT1_Bits.ND11 */
#define IFX_ERAY_NDAT1_ND11_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT1_Bits.ND11 */
#define IFX_ERAY_NDAT1_ND11_OFF (11u)

/** \brief  Length for Ifx_ERAY_NDAT1_Bits.ND12 */
#define IFX_ERAY_NDAT1_ND12_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT1_Bits.ND12 */
#define IFX_ERAY_NDAT1_ND12_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT1_Bits.ND12 */
#define IFX_ERAY_NDAT1_ND12_OFF (12u)

/** \brief  Length for Ifx_ERAY_NDAT1_Bits.ND13 */
#define IFX_ERAY_NDAT1_ND13_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT1_Bits.ND13 */
#define IFX_ERAY_NDAT1_ND13_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT1_Bits.ND13 */
#define IFX_ERAY_NDAT1_ND13_OFF (13u)

/** \brief  Length for Ifx_ERAY_NDAT1_Bits.ND14 */
#define IFX_ERAY_NDAT1_ND14_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT1_Bits.ND14 */
#define IFX_ERAY_NDAT1_ND14_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT1_Bits.ND14 */
#define IFX_ERAY_NDAT1_ND14_OFF (14u)

/** \brief  Length for Ifx_ERAY_NDAT1_Bits.ND15 */
#define IFX_ERAY_NDAT1_ND15_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT1_Bits.ND15 */
#define IFX_ERAY_NDAT1_ND15_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT1_Bits.ND15 */
#define IFX_ERAY_NDAT1_ND15_OFF (15u)

/** \brief  Length for Ifx_ERAY_NDAT1_Bits.ND16 */
#define IFX_ERAY_NDAT1_ND16_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT1_Bits.ND16 */
#define IFX_ERAY_NDAT1_ND16_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT1_Bits.ND16 */
#define IFX_ERAY_NDAT1_ND16_OFF (16u)

/** \brief  Length for Ifx_ERAY_NDAT1_Bits.ND17 */
#define IFX_ERAY_NDAT1_ND17_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT1_Bits.ND17 */
#define IFX_ERAY_NDAT1_ND17_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT1_Bits.ND17 */
#define IFX_ERAY_NDAT1_ND17_OFF (17u)

/** \brief  Length for Ifx_ERAY_NDAT1_Bits.ND18 */
#define IFX_ERAY_NDAT1_ND18_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT1_Bits.ND18 */
#define IFX_ERAY_NDAT1_ND18_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT1_Bits.ND18 */
#define IFX_ERAY_NDAT1_ND18_OFF (18u)

/** \brief  Length for Ifx_ERAY_NDAT1_Bits.ND19 */
#define IFX_ERAY_NDAT1_ND19_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT1_Bits.ND19 */
#define IFX_ERAY_NDAT1_ND19_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT1_Bits.ND19 */
#define IFX_ERAY_NDAT1_ND19_OFF (19u)

/** \brief  Length for Ifx_ERAY_NDAT1_Bits.ND1 */
#define IFX_ERAY_NDAT1_ND1_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT1_Bits.ND1 */
#define IFX_ERAY_NDAT1_ND1_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT1_Bits.ND1 */
#define IFX_ERAY_NDAT1_ND1_OFF (1u)

/** \brief  Length for Ifx_ERAY_NDAT1_Bits.ND20 */
#define IFX_ERAY_NDAT1_ND20_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT1_Bits.ND20 */
#define IFX_ERAY_NDAT1_ND20_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT1_Bits.ND20 */
#define IFX_ERAY_NDAT1_ND20_OFF (20u)

/** \brief  Length for Ifx_ERAY_NDAT1_Bits.ND21 */
#define IFX_ERAY_NDAT1_ND21_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT1_Bits.ND21 */
#define IFX_ERAY_NDAT1_ND21_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT1_Bits.ND21 */
#define IFX_ERAY_NDAT1_ND21_OFF (21u)

/** \brief  Length for Ifx_ERAY_NDAT1_Bits.ND22 */
#define IFX_ERAY_NDAT1_ND22_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT1_Bits.ND22 */
#define IFX_ERAY_NDAT1_ND22_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT1_Bits.ND22 */
#define IFX_ERAY_NDAT1_ND22_OFF (22u)

/** \brief  Length for Ifx_ERAY_NDAT1_Bits.ND23 */
#define IFX_ERAY_NDAT1_ND23_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT1_Bits.ND23 */
#define IFX_ERAY_NDAT1_ND23_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT1_Bits.ND23 */
#define IFX_ERAY_NDAT1_ND23_OFF (23u)

/** \brief  Length for Ifx_ERAY_NDAT1_Bits.ND24 */
#define IFX_ERAY_NDAT1_ND24_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT1_Bits.ND24 */
#define IFX_ERAY_NDAT1_ND24_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT1_Bits.ND24 */
#define IFX_ERAY_NDAT1_ND24_OFF (24u)

/** \brief  Length for Ifx_ERAY_NDAT1_Bits.ND25 */
#define IFX_ERAY_NDAT1_ND25_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT1_Bits.ND25 */
#define IFX_ERAY_NDAT1_ND25_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT1_Bits.ND25 */
#define IFX_ERAY_NDAT1_ND25_OFF (25u)

/** \brief  Length for Ifx_ERAY_NDAT1_Bits.ND26 */
#define IFX_ERAY_NDAT1_ND26_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT1_Bits.ND26 */
#define IFX_ERAY_NDAT1_ND26_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT1_Bits.ND26 */
#define IFX_ERAY_NDAT1_ND26_OFF (26u)

/** \brief  Length for Ifx_ERAY_NDAT1_Bits.ND27 */
#define IFX_ERAY_NDAT1_ND27_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT1_Bits.ND27 */
#define IFX_ERAY_NDAT1_ND27_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT1_Bits.ND27 */
#define IFX_ERAY_NDAT1_ND27_OFF (27u)

/** \brief  Length for Ifx_ERAY_NDAT1_Bits.ND28 */
#define IFX_ERAY_NDAT1_ND28_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT1_Bits.ND28 */
#define IFX_ERAY_NDAT1_ND28_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT1_Bits.ND28 */
#define IFX_ERAY_NDAT1_ND28_OFF (28u)

/** \brief  Length for Ifx_ERAY_NDAT1_Bits.ND29 */
#define IFX_ERAY_NDAT1_ND29_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT1_Bits.ND29 */
#define IFX_ERAY_NDAT1_ND29_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT1_Bits.ND29 */
#define IFX_ERAY_NDAT1_ND29_OFF (29u)

/** \brief  Length for Ifx_ERAY_NDAT1_Bits.ND2 */
#define IFX_ERAY_NDAT1_ND2_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT1_Bits.ND2 */
#define IFX_ERAY_NDAT1_ND2_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT1_Bits.ND2 */
#define IFX_ERAY_NDAT1_ND2_OFF (2u)

/** \brief  Length for Ifx_ERAY_NDAT1_Bits.ND30 */
#define IFX_ERAY_NDAT1_ND30_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT1_Bits.ND30 */
#define IFX_ERAY_NDAT1_ND30_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT1_Bits.ND30 */
#define IFX_ERAY_NDAT1_ND30_OFF (30u)

/** \brief  Length for Ifx_ERAY_NDAT1_Bits.ND31 */
#define IFX_ERAY_NDAT1_ND31_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT1_Bits.ND31 */
#define IFX_ERAY_NDAT1_ND31_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT1_Bits.ND31 */
#define IFX_ERAY_NDAT1_ND31_OFF (31u)

/** \brief  Length for Ifx_ERAY_NDAT1_Bits.ND3 */
#define IFX_ERAY_NDAT1_ND3_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT1_Bits.ND3 */
#define IFX_ERAY_NDAT1_ND3_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT1_Bits.ND3 */
#define IFX_ERAY_NDAT1_ND3_OFF (3u)

/** \brief  Length for Ifx_ERAY_NDAT1_Bits.ND4 */
#define IFX_ERAY_NDAT1_ND4_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT1_Bits.ND4 */
#define IFX_ERAY_NDAT1_ND4_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT1_Bits.ND4 */
#define IFX_ERAY_NDAT1_ND4_OFF (4u)

/** \brief  Length for Ifx_ERAY_NDAT1_Bits.ND5 */
#define IFX_ERAY_NDAT1_ND5_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT1_Bits.ND5 */
#define IFX_ERAY_NDAT1_ND5_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT1_Bits.ND5 */
#define IFX_ERAY_NDAT1_ND5_OFF (5u)

/** \brief  Length for Ifx_ERAY_NDAT1_Bits.ND6 */
#define IFX_ERAY_NDAT1_ND6_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT1_Bits.ND6 */
#define IFX_ERAY_NDAT1_ND6_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT1_Bits.ND6 */
#define IFX_ERAY_NDAT1_ND6_OFF (6u)

/** \brief  Length for Ifx_ERAY_NDAT1_Bits.ND7 */
#define IFX_ERAY_NDAT1_ND7_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT1_Bits.ND7 */
#define IFX_ERAY_NDAT1_ND7_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT1_Bits.ND7 */
#define IFX_ERAY_NDAT1_ND7_OFF (7u)

/** \brief  Length for Ifx_ERAY_NDAT1_Bits.ND8 */
#define IFX_ERAY_NDAT1_ND8_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT1_Bits.ND8 */
#define IFX_ERAY_NDAT1_ND8_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT1_Bits.ND8 */
#define IFX_ERAY_NDAT1_ND8_OFF (8u)

/** \brief  Length for Ifx_ERAY_NDAT1_Bits.ND9 */
#define IFX_ERAY_NDAT1_ND9_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT1_Bits.ND9 */
#define IFX_ERAY_NDAT1_ND9_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT1_Bits.ND9 */
#define IFX_ERAY_NDAT1_ND9_OFF (9u)

/** \brief  Length for Ifx_ERAY_NDAT2_Bits.ND32 */
#define IFX_ERAY_NDAT2_ND32_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT2_Bits.ND32 */
#define IFX_ERAY_NDAT2_ND32_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT2_Bits.ND32 */
#define IFX_ERAY_NDAT2_ND32_OFF (0u)

/** \brief  Length for Ifx_ERAY_NDAT2_Bits.ND33 */
#define IFX_ERAY_NDAT2_ND33_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT2_Bits.ND33 */
#define IFX_ERAY_NDAT2_ND33_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT2_Bits.ND33 */
#define IFX_ERAY_NDAT2_ND33_OFF (1u)

/** \brief  Length for Ifx_ERAY_NDAT2_Bits.ND34 */
#define IFX_ERAY_NDAT2_ND34_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT2_Bits.ND34 */
#define IFX_ERAY_NDAT2_ND34_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT2_Bits.ND34 */
#define IFX_ERAY_NDAT2_ND34_OFF (2u)

/** \brief  Length for Ifx_ERAY_NDAT2_Bits.ND35 */
#define IFX_ERAY_NDAT2_ND35_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT2_Bits.ND35 */
#define IFX_ERAY_NDAT2_ND35_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT2_Bits.ND35 */
#define IFX_ERAY_NDAT2_ND35_OFF (3u)

/** \brief  Length for Ifx_ERAY_NDAT2_Bits.ND36 */
#define IFX_ERAY_NDAT2_ND36_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT2_Bits.ND36 */
#define IFX_ERAY_NDAT2_ND36_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT2_Bits.ND36 */
#define IFX_ERAY_NDAT2_ND36_OFF (4u)

/** \brief  Length for Ifx_ERAY_NDAT2_Bits.ND37 */
#define IFX_ERAY_NDAT2_ND37_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT2_Bits.ND37 */
#define IFX_ERAY_NDAT2_ND37_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT2_Bits.ND37 */
#define IFX_ERAY_NDAT2_ND37_OFF (5u)

/** \brief  Length for Ifx_ERAY_NDAT2_Bits.ND38 */
#define IFX_ERAY_NDAT2_ND38_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT2_Bits.ND38 */
#define IFX_ERAY_NDAT2_ND38_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT2_Bits.ND38 */
#define IFX_ERAY_NDAT2_ND38_OFF (6u)

/** \brief  Length for Ifx_ERAY_NDAT2_Bits.ND39 */
#define IFX_ERAY_NDAT2_ND39_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT2_Bits.ND39 */
#define IFX_ERAY_NDAT2_ND39_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT2_Bits.ND39 */
#define IFX_ERAY_NDAT2_ND39_OFF (7u)

/** \brief  Length for Ifx_ERAY_NDAT2_Bits.ND40 */
#define IFX_ERAY_NDAT2_ND40_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT2_Bits.ND40 */
#define IFX_ERAY_NDAT2_ND40_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT2_Bits.ND40 */
#define IFX_ERAY_NDAT2_ND40_OFF (8u)

/** \brief  Length for Ifx_ERAY_NDAT2_Bits.ND41 */
#define IFX_ERAY_NDAT2_ND41_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT2_Bits.ND41 */
#define IFX_ERAY_NDAT2_ND41_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT2_Bits.ND41 */
#define IFX_ERAY_NDAT2_ND41_OFF (9u)

/** \brief  Length for Ifx_ERAY_NDAT2_Bits.ND42 */
#define IFX_ERAY_NDAT2_ND42_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT2_Bits.ND42 */
#define IFX_ERAY_NDAT2_ND42_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT2_Bits.ND42 */
#define IFX_ERAY_NDAT2_ND42_OFF (10u)

/** \brief  Length for Ifx_ERAY_NDAT2_Bits.ND43 */
#define IFX_ERAY_NDAT2_ND43_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT2_Bits.ND43 */
#define IFX_ERAY_NDAT2_ND43_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT2_Bits.ND43 */
#define IFX_ERAY_NDAT2_ND43_OFF (11u)

/** \brief  Length for Ifx_ERAY_NDAT2_Bits.ND44 */
#define IFX_ERAY_NDAT2_ND44_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT2_Bits.ND44 */
#define IFX_ERAY_NDAT2_ND44_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT2_Bits.ND44 */
#define IFX_ERAY_NDAT2_ND44_OFF (12u)

/** \brief  Length for Ifx_ERAY_NDAT2_Bits.ND45 */
#define IFX_ERAY_NDAT2_ND45_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT2_Bits.ND45 */
#define IFX_ERAY_NDAT2_ND45_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT2_Bits.ND45 */
#define IFX_ERAY_NDAT2_ND45_OFF (13u)

/** \brief  Length for Ifx_ERAY_NDAT2_Bits.ND46 */
#define IFX_ERAY_NDAT2_ND46_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT2_Bits.ND46 */
#define IFX_ERAY_NDAT2_ND46_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT2_Bits.ND46 */
#define IFX_ERAY_NDAT2_ND46_OFF (14u)

/** \brief  Length for Ifx_ERAY_NDAT2_Bits.ND47 */
#define IFX_ERAY_NDAT2_ND47_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT2_Bits.ND47 */
#define IFX_ERAY_NDAT2_ND47_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT2_Bits.ND47 */
#define IFX_ERAY_NDAT2_ND47_OFF (15u)

/** \brief  Length for Ifx_ERAY_NDAT2_Bits.ND48 */
#define IFX_ERAY_NDAT2_ND48_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT2_Bits.ND48 */
#define IFX_ERAY_NDAT2_ND48_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT2_Bits.ND48 */
#define IFX_ERAY_NDAT2_ND48_OFF (16u)

/** \brief  Length for Ifx_ERAY_NDAT2_Bits.ND49 */
#define IFX_ERAY_NDAT2_ND49_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT2_Bits.ND49 */
#define IFX_ERAY_NDAT2_ND49_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT2_Bits.ND49 */
#define IFX_ERAY_NDAT2_ND49_OFF (17u)

/** \brief  Length for Ifx_ERAY_NDAT2_Bits.ND50 */
#define IFX_ERAY_NDAT2_ND50_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT2_Bits.ND50 */
#define IFX_ERAY_NDAT2_ND50_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT2_Bits.ND50 */
#define IFX_ERAY_NDAT2_ND50_OFF (18u)

/** \brief  Length for Ifx_ERAY_NDAT2_Bits.ND51 */
#define IFX_ERAY_NDAT2_ND51_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT2_Bits.ND51 */
#define IFX_ERAY_NDAT2_ND51_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT2_Bits.ND51 */
#define IFX_ERAY_NDAT2_ND51_OFF (19u)

/** \brief  Length for Ifx_ERAY_NDAT2_Bits.ND52 */
#define IFX_ERAY_NDAT2_ND52_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT2_Bits.ND52 */
#define IFX_ERAY_NDAT2_ND52_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT2_Bits.ND52 */
#define IFX_ERAY_NDAT2_ND52_OFF (20u)

/** \brief  Length for Ifx_ERAY_NDAT2_Bits.ND53 */
#define IFX_ERAY_NDAT2_ND53_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT2_Bits.ND53 */
#define IFX_ERAY_NDAT2_ND53_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT2_Bits.ND53 */
#define IFX_ERAY_NDAT2_ND53_OFF (21u)

/** \brief  Length for Ifx_ERAY_NDAT2_Bits.ND54 */
#define IFX_ERAY_NDAT2_ND54_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT2_Bits.ND54 */
#define IFX_ERAY_NDAT2_ND54_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT2_Bits.ND54 */
#define IFX_ERAY_NDAT2_ND54_OFF (22u)

/** \brief  Length for Ifx_ERAY_NDAT2_Bits.ND55 */
#define IFX_ERAY_NDAT2_ND55_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT2_Bits.ND55 */
#define IFX_ERAY_NDAT2_ND55_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT2_Bits.ND55 */
#define IFX_ERAY_NDAT2_ND55_OFF (23u)

/** \brief  Length for Ifx_ERAY_NDAT2_Bits.ND56 */
#define IFX_ERAY_NDAT2_ND56_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT2_Bits.ND56 */
#define IFX_ERAY_NDAT2_ND56_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT2_Bits.ND56 */
#define IFX_ERAY_NDAT2_ND56_OFF (24u)

/** \brief  Length for Ifx_ERAY_NDAT2_Bits.ND57 */
#define IFX_ERAY_NDAT2_ND57_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT2_Bits.ND57 */
#define IFX_ERAY_NDAT2_ND57_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT2_Bits.ND57 */
#define IFX_ERAY_NDAT2_ND57_OFF (25u)

/** \brief  Length for Ifx_ERAY_NDAT2_Bits.ND58 */
#define IFX_ERAY_NDAT2_ND58_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT2_Bits.ND58 */
#define IFX_ERAY_NDAT2_ND58_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT2_Bits.ND58 */
#define IFX_ERAY_NDAT2_ND58_OFF (26u)

/** \brief  Length for Ifx_ERAY_NDAT2_Bits.ND59 */
#define IFX_ERAY_NDAT2_ND59_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT2_Bits.ND59 */
#define IFX_ERAY_NDAT2_ND59_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT2_Bits.ND59 */
#define IFX_ERAY_NDAT2_ND59_OFF (27u)

/** \brief  Length for Ifx_ERAY_NDAT2_Bits.ND60 */
#define IFX_ERAY_NDAT2_ND60_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT2_Bits.ND60 */
#define IFX_ERAY_NDAT2_ND60_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT2_Bits.ND60 */
#define IFX_ERAY_NDAT2_ND60_OFF (28u)

/** \brief  Length for Ifx_ERAY_NDAT2_Bits.ND61 */
#define IFX_ERAY_NDAT2_ND61_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT2_Bits.ND61 */
#define IFX_ERAY_NDAT2_ND61_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT2_Bits.ND61 */
#define IFX_ERAY_NDAT2_ND61_OFF (29u)

/** \brief  Length for Ifx_ERAY_NDAT2_Bits.ND62 */
#define IFX_ERAY_NDAT2_ND62_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT2_Bits.ND62 */
#define IFX_ERAY_NDAT2_ND62_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT2_Bits.ND62 */
#define IFX_ERAY_NDAT2_ND62_OFF (30u)

/** \brief  Length for Ifx_ERAY_NDAT2_Bits.ND63 */
#define IFX_ERAY_NDAT2_ND63_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT2_Bits.ND63 */
#define IFX_ERAY_NDAT2_ND63_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT2_Bits.ND63 */
#define IFX_ERAY_NDAT2_ND63_OFF (31u)

/** \brief  Length for Ifx_ERAY_NDAT3_Bits.ND64 */
#define IFX_ERAY_NDAT3_ND64_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT3_Bits.ND64 */
#define IFX_ERAY_NDAT3_ND64_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT3_Bits.ND64 */
#define IFX_ERAY_NDAT3_ND64_OFF (0u)

/** \brief  Length for Ifx_ERAY_NDAT3_Bits.ND65 */
#define IFX_ERAY_NDAT3_ND65_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT3_Bits.ND65 */
#define IFX_ERAY_NDAT3_ND65_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT3_Bits.ND65 */
#define IFX_ERAY_NDAT3_ND65_OFF (1u)

/** \brief  Length for Ifx_ERAY_NDAT3_Bits.ND66 */
#define IFX_ERAY_NDAT3_ND66_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT3_Bits.ND66 */
#define IFX_ERAY_NDAT3_ND66_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT3_Bits.ND66 */
#define IFX_ERAY_NDAT3_ND66_OFF (2u)

/** \brief  Length for Ifx_ERAY_NDAT3_Bits.ND67 */
#define IFX_ERAY_NDAT3_ND67_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT3_Bits.ND67 */
#define IFX_ERAY_NDAT3_ND67_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT3_Bits.ND67 */
#define IFX_ERAY_NDAT3_ND67_OFF (3u)

/** \brief  Length for Ifx_ERAY_NDAT3_Bits.ND68 */
#define IFX_ERAY_NDAT3_ND68_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT3_Bits.ND68 */
#define IFX_ERAY_NDAT3_ND68_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT3_Bits.ND68 */
#define IFX_ERAY_NDAT3_ND68_OFF (4u)

/** \brief  Length for Ifx_ERAY_NDAT3_Bits.ND69 */
#define IFX_ERAY_NDAT3_ND69_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT3_Bits.ND69 */
#define IFX_ERAY_NDAT3_ND69_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT3_Bits.ND69 */
#define IFX_ERAY_NDAT3_ND69_OFF (5u)

/** \brief  Length for Ifx_ERAY_NDAT3_Bits.ND70 */
#define IFX_ERAY_NDAT3_ND70_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT3_Bits.ND70 */
#define IFX_ERAY_NDAT3_ND70_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT3_Bits.ND70 */
#define IFX_ERAY_NDAT3_ND70_OFF (6u)

/** \brief  Length for Ifx_ERAY_NDAT3_Bits.ND71 */
#define IFX_ERAY_NDAT3_ND71_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT3_Bits.ND71 */
#define IFX_ERAY_NDAT3_ND71_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT3_Bits.ND71 */
#define IFX_ERAY_NDAT3_ND71_OFF (7u)

/** \brief  Length for Ifx_ERAY_NDAT3_Bits.ND72 */
#define IFX_ERAY_NDAT3_ND72_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT3_Bits.ND72 */
#define IFX_ERAY_NDAT3_ND72_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT3_Bits.ND72 */
#define IFX_ERAY_NDAT3_ND72_OFF (8u)

/** \brief  Length for Ifx_ERAY_NDAT3_Bits.ND73 */
#define IFX_ERAY_NDAT3_ND73_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT3_Bits.ND73 */
#define IFX_ERAY_NDAT3_ND73_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT3_Bits.ND73 */
#define IFX_ERAY_NDAT3_ND73_OFF (9u)

/** \brief  Length for Ifx_ERAY_NDAT3_Bits.ND74 */
#define IFX_ERAY_NDAT3_ND74_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT3_Bits.ND74 */
#define IFX_ERAY_NDAT3_ND74_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT3_Bits.ND74 */
#define IFX_ERAY_NDAT3_ND74_OFF (10u)

/** \brief  Length for Ifx_ERAY_NDAT3_Bits.ND75 */
#define IFX_ERAY_NDAT3_ND75_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT3_Bits.ND75 */
#define IFX_ERAY_NDAT3_ND75_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT3_Bits.ND75 */
#define IFX_ERAY_NDAT3_ND75_OFF (11u)

/** \brief  Length for Ifx_ERAY_NDAT3_Bits.ND76 */
#define IFX_ERAY_NDAT3_ND76_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT3_Bits.ND76 */
#define IFX_ERAY_NDAT3_ND76_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT3_Bits.ND76 */
#define IFX_ERAY_NDAT3_ND76_OFF (12u)

/** \brief  Length for Ifx_ERAY_NDAT3_Bits.ND77 */
#define IFX_ERAY_NDAT3_ND77_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT3_Bits.ND77 */
#define IFX_ERAY_NDAT3_ND77_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT3_Bits.ND77 */
#define IFX_ERAY_NDAT3_ND77_OFF (13u)

/** \brief  Length for Ifx_ERAY_NDAT3_Bits.ND78 */
#define IFX_ERAY_NDAT3_ND78_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT3_Bits.ND78 */
#define IFX_ERAY_NDAT3_ND78_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT3_Bits.ND78 */
#define IFX_ERAY_NDAT3_ND78_OFF (14u)

/** \brief  Length for Ifx_ERAY_NDAT3_Bits.ND79 */
#define IFX_ERAY_NDAT3_ND79_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT3_Bits.ND79 */
#define IFX_ERAY_NDAT3_ND79_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT3_Bits.ND79 */
#define IFX_ERAY_NDAT3_ND79_OFF (15u)

/** \brief  Length for Ifx_ERAY_NDAT3_Bits.ND80 */
#define IFX_ERAY_NDAT3_ND80_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT3_Bits.ND80 */
#define IFX_ERAY_NDAT3_ND80_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT3_Bits.ND80 */
#define IFX_ERAY_NDAT3_ND80_OFF (16u)

/** \brief  Length for Ifx_ERAY_NDAT3_Bits.ND81 */
#define IFX_ERAY_NDAT3_ND81_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT3_Bits.ND81 */
#define IFX_ERAY_NDAT3_ND81_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT3_Bits.ND81 */
#define IFX_ERAY_NDAT3_ND81_OFF (17u)

/** \brief  Length for Ifx_ERAY_NDAT3_Bits.ND82 */
#define IFX_ERAY_NDAT3_ND82_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT3_Bits.ND82 */
#define IFX_ERAY_NDAT3_ND82_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT3_Bits.ND82 */
#define IFX_ERAY_NDAT3_ND82_OFF (18u)

/** \brief  Length for Ifx_ERAY_NDAT3_Bits.ND83 */
#define IFX_ERAY_NDAT3_ND83_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT3_Bits.ND83 */
#define IFX_ERAY_NDAT3_ND83_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT3_Bits.ND83 */
#define IFX_ERAY_NDAT3_ND83_OFF (19u)

/** \brief  Length for Ifx_ERAY_NDAT3_Bits.ND84 */
#define IFX_ERAY_NDAT3_ND84_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT3_Bits.ND84 */
#define IFX_ERAY_NDAT3_ND84_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT3_Bits.ND84 */
#define IFX_ERAY_NDAT3_ND84_OFF (20u)

/** \brief  Length for Ifx_ERAY_NDAT3_Bits.ND85 */
#define IFX_ERAY_NDAT3_ND85_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT3_Bits.ND85 */
#define IFX_ERAY_NDAT3_ND85_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT3_Bits.ND85 */
#define IFX_ERAY_NDAT3_ND85_OFF (21u)

/** \brief  Length for Ifx_ERAY_NDAT3_Bits.ND86 */
#define IFX_ERAY_NDAT3_ND86_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT3_Bits.ND86 */
#define IFX_ERAY_NDAT3_ND86_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT3_Bits.ND86 */
#define IFX_ERAY_NDAT3_ND86_OFF (22u)

/** \brief  Length for Ifx_ERAY_NDAT3_Bits.ND87 */
#define IFX_ERAY_NDAT3_ND87_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT3_Bits.ND87 */
#define IFX_ERAY_NDAT3_ND87_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT3_Bits.ND87 */
#define IFX_ERAY_NDAT3_ND87_OFF (23u)

/** \brief  Length for Ifx_ERAY_NDAT3_Bits.ND88 */
#define IFX_ERAY_NDAT3_ND88_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT3_Bits.ND88 */
#define IFX_ERAY_NDAT3_ND88_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT3_Bits.ND88 */
#define IFX_ERAY_NDAT3_ND88_OFF (24u)

/** \brief  Length for Ifx_ERAY_NDAT3_Bits.ND89 */
#define IFX_ERAY_NDAT3_ND89_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT3_Bits.ND89 */
#define IFX_ERAY_NDAT3_ND89_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT3_Bits.ND89 */
#define IFX_ERAY_NDAT3_ND89_OFF (25u)

/** \brief  Length for Ifx_ERAY_NDAT3_Bits.ND90 */
#define IFX_ERAY_NDAT3_ND90_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT3_Bits.ND90 */
#define IFX_ERAY_NDAT3_ND90_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT3_Bits.ND90 */
#define IFX_ERAY_NDAT3_ND90_OFF (26u)

/** \brief  Length for Ifx_ERAY_NDAT3_Bits.ND91 */
#define IFX_ERAY_NDAT3_ND91_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT3_Bits.ND91 */
#define IFX_ERAY_NDAT3_ND91_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT3_Bits.ND91 */
#define IFX_ERAY_NDAT3_ND91_OFF (27u)

/** \brief  Length for Ifx_ERAY_NDAT3_Bits.ND92 */
#define IFX_ERAY_NDAT3_ND92_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT3_Bits.ND92 */
#define IFX_ERAY_NDAT3_ND92_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT3_Bits.ND92 */
#define IFX_ERAY_NDAT3_ND92_OFF (28u)

/** \brief  Length for Ifx_ERAY_NDAT3_Bits.ND93 */
#define IFX_ERAY_NDAT3_ND93_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT3_Bits.ND93 */
#define IFX_ERAY_NDAT3_ND93_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT3_Bits.ND93 */
#define IFX_ERAY_NDAT3_ND93_OFF (29u)

/** \brief  Length for Ifx_ERAY_NDAT3_Bits.ND94 */
#define IFX_ERAY_NDAT3_ND94_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT3_Bits.ND94 */
#define IFX_ERAY_NDAT3_ND94_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT3_Bits.ND94 */
#define IFX_ERAY_NDAT3_ND94_OFF (30u)

/** \brief  Length for Ifx_ERAY_NDAT3_Bits.ND95 */
#define IFX_ERAY_NDAT3_ND95_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT3_Bits.ND95 */
#define IFX_ERAY_NDAT3_ND95_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT3_Bits.ND95 */
#define IFX_ERAY_NDAT3_ND95_OFF (31u)

/** \brief  Length for Ifx_ERAY_NDAT4_Bits.ND100 */
#define IFX_ERAY_NDAT4_ND100_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT4_Bits.ND100 */
#define IFX_ERAY_NDAT4_ND100_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT4_Bits.ND100 */
#define IFX_ERAY_NDAT4_ND100_OFF (4u)

/** \brief  Length for Ifx_ERAY_NDAT4_Bits.ND101 */
#define IFX_ERAY_NDAT4_ND101_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT4_Bits.ND101 */
#define IFX_ERAY_NDAT4_ND101_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT4_Bits.ND101 */
#define IFX_ERAY_NDAT4_ND101_OFF (5u)

/** \brief  Length for Ifx_ERAY_NDAT4_Bits.ND102 */
#define IFX_ERAY_NDAT4_ND102_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT4_Bits.ND102 */
#define IFX_ERAY_NDAT4_ND102_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT4_Bits.ND102 */
#define IFX_ERAY_NDAT4_ND102_OFF (6u)

/** \brief  Length for Ifx_ERAY_NDAT4_Bits.ND103 */
#define IFX_ERAY_NDAT4_ND103_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT4_Bits.ND103 */
#define IFX_ERAY_NDAT4_ND103_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT4_Bits.ND103 */
#define IFX_ERAY_NDAT4_ND103_OFF (7u)

/** \brief  Length for Ifx_ERAY_NDAT4_Bits.ND104 */
#define IFX_ERAY_NDAT4_ND104_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT4_Bits.ND104 */
#define IFX_ERAY_NDAT4_ND104_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT4_Bits.ND104 */
#define IFX_ERAY_NDAT4_ND104_OFF (8u)

/** \brief  Length for Ifx_ERAY_NDAT4_Bits.ND105 */
#define IFX_ERAY_NDAT4_ND105_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT4_Bits.ND105 */
#define IFX_ERAY_NDAT4_ND105_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT4_Bits.ND105 */
#define IFX_ERAY_NDAT4_ND105_OFF (9u)

/** \brief  Length for Ifx_ERAY_NDAT4_Bits.ND106 */
#define IFX_ERAY_NDAT4_ND106_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT4_Bits.ND106 */
#define IFX_ERAY_NDAT4_ND106_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT4_Bits.ND106 */
#define IFX_ERAY_NDAT4_ND106_OFF (10u)

/** \brief  Length for Ifx_ERAY_NDAT4_Bits.ND107 */
#define IFX_ERAY_NDAT4_ND107_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT4_Bits.ND107 */
#define IFX_ERAY_NDAT4_ND107_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT4_Bits.ND107 */
#define IFX_ERAY_NDAT4_ND107_OFF (11u)

/** \brief  Length for Ifx_ERAY_NDAT4_Bits.ND108 */
#define IFX_ERAY_NDAT4_ND108_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT4_Bits.ND108 */
#define IFX_ERAY_NDAT4_ND108_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT4_Bits.ND108 */
#define IFX_ERAY_NDAT4_ND108_OFF (12u)

/** \brief  Length for Ifx_ERAY_NDAT4_Bits.ND109 */
#define IFX_ERAY_NDAT4_ND109_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT4_Bits.ND109 */
#define IFX_ERAY_NDAT4_ND109_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT4_Bits.ND109 */
#define IFX_ERAY_NDAT4_ND109_OFF (13u)

/** \brief  Length for Ifx_ERAY_NDAT4_Bits.ND110 */
#define IFX_ERAY_NDAT4_ND110_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT4_Bits.ND110 */
#define IFX_ERAY_NDAT4_ND110_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT4_Bits.ND110 */
#define IFX_ERAY_NDAT4_ND110_OFF (14u)

/** \brief  Length for Ifx_ERAY_NDAT4_Bits.ND111 */
#define IFX_ERAY_NDAT4_ND111_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT4_Bits.ND111 */
#define IFX_ERAY_NDAT4_ND111_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT4_Bits.ND111 */
#define IFX_ERAY_NDAT4_ND111_OFF (15u)

/** \brief  Length for Ifx_ERAY_NDAT4_Bits.ND112 */
#define IFX_ERAY_NDAT4_ND112_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT4_Bits.ND112 */
#define IFX_ERAY_NDAT4_ND112_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT4_Bits.ND112 */
#define IFX_ERAY_NDAT4_ND112_OFF (16u)

/** \brief  Length for Ifx_ERAY_NDAT4_Bits.ND113 */
#define IFX_ERAY_NDAT4_ND113_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT4_Bits.ND113 */
#define IFX_ERAY_NDAT4_ND113_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT4_Bits.ND113 */
#define IFX_ERAY_NDAT4_ND113_OFF (17u)

/** \brief  Length for Ifx_ERAY_NDAT4_Bits.ND114 */
#define IFX_ERAY_NDAT4_ND114_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT4_Bits.ND114 */
#define IFX_ERAY_NDAT4_ND114_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT4_Bits.ND114 */
#define IFX_ERAY_NDAT4_ND114_OFF (18u)

/** \brief  Length for Ifx_ERAY_NDAT4_Bits.ND115 */
#define IFX_ERAY_NDAT4_ND115_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT4_Bits.ND115 */
#define IFX_ERAY_NDAT4_ND115_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT4_Bits.ND115 */
#define IFX_ERAY_NDAT4_ND115_OFF (19u)

/** \brief  Length for Ifx_ERAY_NDAT4_Bits.ND116 */
#define IFX_ERAY_NDAT4_ND116_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT4_Bits.ND116 */
#define IFX_ERAY_NDAT4_ND116_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT4_Bits.ND116 */
#define IFX_ERAY_NDAT4_ND116_OFF (20u)

/** \brief  Length for Ifx_ERAY_NDAT4_Bits.ND117 */
#define IFX_ERAY_NDAT4_ND117_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT4_Bits.ND117 */
#define IFX_ERAY_NDAT4_ND117_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT4_Bits.ND117 */
#define IFX_ERAY_NDAT4_ND117_OFF (21u)

/** \brief  Length for Ifx_ERAY_NDAT4_Bits.ND118 */
#define IFX_ERAY_NDAT4_ND118_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT4_Bits.ND118 */
#define IFX_ERAY_NDAT4_ND118_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT4_Bits.ND118 */
#define IFX_ERAY_NDAT4_ND118_OFF (22u)

/** \brief  Length for Ifx_ERAY_NDAT4_Bits.ND119 */
#define IFX_ERAY_NDAT4_ND119_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT4_Bits.ND119 */
#define IFX_ERAY_NDAT4_ND119_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT4_Bits.ND119 */
#define IFX_ERAY_NDAT4_ND119_OFF (23u)

/** \brief  Length for Ifx_ERAY_NDAT4_Bits.ND120 */
#define IFX_ERAY_NDAT4_ND120_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT4_Bits.ND120 */
#define IFX_ERAY_NDAT4_ND120_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT4_Bits.ND120 */
#define IFX_ERAY_NDAT4_ND120_OFF (24u)

/** \brief  Length for Ifx_ERAY_NDAT4_Bits.ND121 */
#define IFX_ERAY_NDAT4_ND121_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT4_Bits.ND121 */
#define IFX_ERAY_NDAT4_ND121_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT4_Bits.ND121 */
#define IFX_ERAY_NDAT4_ND121_OFF (25u)

/** \brief  Length for Ifx_ERAY_NDAT4_Bits.ND122 */
#define IFX_ERAY_NDAT4_ND122_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT4_Bits.ND122 */
#define IFX_ERAY_NDAT4_ND122_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT4_Bits.ND122 */
#define IFX_ERAY_NDAT4_ND122_OFF (26u)

/** \brief  Length for Ifx_ERAY_NDAT4_Bits.ND123 */
#define IFX_ERAY_NDAT4_ND123_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT4_Bits.ND123 */
#define IFX_ERAY_NDAT4_ND123_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT4_Bits.ND123 */
#define IFX_ERAY_NDAT4_ND123_OFF (27u)

/** \brief  Length for Ifx_ERAY_NDAT4_Bits.ND124 */
#define IFX_ERAY_NDAT4_ND124_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT4_Bits.ND124 */
#define IFX_ERAY_NDAT4_ND124_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT4_Bits.ND124 */
#define IFX_ERAY_NDAT4_ND124_OFF (28u)

/** \brief  Length for Ifx_ERAY_NDAT4_Bits.ND125 */
#define IFX_ERAY_NDAT4_ND125_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT4_Bits.ND125 */
#define IFX_ERAY_NDAT4_ND125_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT4_Bits.ND125 */
#define IFX_ERAY_NDAT4_ND125_OFF (29u)

/** \brief  Length for Ifx_ERAY_NDAT4_Bits.ND126 */
#define IFX_ERAY_NDAT4_ND126_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT4_Bits.ND126 */
#define IFX_ERAY_NDAT4_ND126_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT4_Bits.ND126 */
#define IFX_ERAY_NDAT4_ND126_OFF (30u)

/** \brief  Length for Ifx_ERAY_NDAT4_Bits.ND127 */
#define IFX_ERAY_NDAT4_ND127_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT4_Bits.ND127 */
#define IFX_ERAY_NDAT4_ND127_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT4_Bits.ND127 */
#define IFX_ERAY_NDAT4_ND127_OFF (31u)

/** \brief  Length for Ifx_ERAY_NDAT4_Bits.ND96 */
#define IFX_ERAY_NDAT4_ND96_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT4_Bits.ND96 */
#define IFX_ERAY_NDAT4_ND96_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT4_Bits.ND96 */
#define IFX_ERAY_NDAT4_ND96_OFF (0u)

/** \brief  Length for Ifx_ERAY_NDAT4_Bits.ND97 */
#define IFX_ERAY_NDAT4_ND97_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT4_Bits.ND97 */
#define IFX_ERAY_NDAT4_ND97_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT4_Bits.ND97 */
#define IFX_ERAY_NDAT4_ND97_OFF (1u)

/** \brief  Length for Ifx_ERAY_NDAT4_Bits.ND98 */
#define IFX_ERAY_NDAT4_ND98_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT4_Bits.ND98 */
#define IFX_ERAY_NDAT4_ND98_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT4_Bits.ND98 */
#define IFX_ERAY_NDAT4_ND98_OFF (2u)

/** \brief  Length for Ifx_ERAY_NDAT4_Bits.ND99 */
#define IFX_ERAY_NDAT4_ND99_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDAT4_Bits.ND99 */
#define IFX_ERAY_NDAT4_ND99_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDAT4_Bits.ND99 */
#define IFX_ERAY_NDAT4_ND99_OFF (3u)

/** \brief  Length for Ifx_ERAY_NDIC1_Bits.NDIP0 */
#define IFX_ERAY_NDIC1_NDIP0_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC1_Bits.NDIP0 */
#define IFX_ERAY_NDIC1_NDIP0_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC1_Bits.NDIP0 */
#define IFX_ERAY_NDIC1_NDIP0_OFF (0u)

/** \brief  Length for Ifx_ERAY_NDIC1_Bits.NDIP10 */
#define IFX_ERAY_NDIC1_NDIP10_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC1_Bits.NDIP10 */
#define IFX_ERAY_NDIC1_NDIP10_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC1_Bits.NDIP10 */
#define IFX_ERAY_NDIC1_NDIP10_OFF (10u)

/** \brief  Length for Ifx_ERAY_NDIC1_Bits.NDIP11 */
#define IFX_ERAY_NDIC1_NDIP11_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC1_Bits.NDIP11 */
#define IFX_ERAY_NDIC1_NDIP11_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC1_Bits.NDIP11 */
#define IFX_ERAY_NDIC1_NDIP11_OFF (11u)

/** \brief  Length for Ifx_ERAY_NDIC1_Bits.NDIP12 */
#define IFX_ERAY_NDIC1_NDIP12_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC1_Bits.NDIP12 */
#define IFX_ERAY_NDIC1_NDIP12_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC1_Bits.NDIP12 */
#define IFX_ERAY_NDIC1_NDIP12_OFF (12u)

/** \brief  Length for Ifx_ERAY_NDIC1_Bits.NDIP13 */
#define IFX_ERAY_NDIC1_NDIP13_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC1_Bits.NDIP13 */
#define IFX_ERAY_NDIC1_NDIP13_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC1_Bits.NDIP13 */
#define IFX_ERAY_NDIC1_NDIP13_OFF (13u)

/** \brief  Length for Ifx_ERAY_NDIC1_Bits.NDIP14 */
#define IFX_ERAY_NDIC1_NDIP14_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC1_Bits.NDIP14 */
#define IFX_ERAY_NDIC1_NDIP14_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC1_Bits.NDIP14 */
#define IFX_ERAY_NDIC1_NDIP14_OFF (14u)

/** \brief  Length for Ifx_ERAY_NDIC1_Bits.NDIP15 */
#define IFX_ERAY_NDIC1_NDIP15_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC1_Bits.NDIP15 */
#define IFX_ERAY_NDIC1_NDIP15_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC1_Bits.NDIP15 */
#define IFX_ERAY_NDIC1_NDIP15_OFF (15u)

/** \brief  Length for Ifx_ERAY_NDIC1_Bits.NDIP16 */
#define IFX_ERAY_NDIC1_NDIP16_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC1_Bits.NDIP16 */
#define IFX_ERAY_NDIC1_NDIP16_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC1_Bits.NDIP16 */
#define IFX_ERAY_NDIC1_NDIP16_OFF (16u)

/** \brief  Length for Ifx_ERAY_NDIC1_Bits.NDIP17 */
#define IFX_ERAY_NDIC1_NDIP17_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC1_Bits.NDIP17 */
#define IFX_ERAY_NDIC1_NDIP17_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC1_Bits.NDIP17 */
#define IFX_ERAY_NDIC1_NDIP17_OFF (17u)

/** \brief  Length for Ifx_ERAY_NDIC1_Bits.NDIP18 */
#define IFX_ERAY_NDIC1_NDIP18_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC1_Bits.NDIP18 */
#define IFX_ERAY_NDIC1_NDIP18_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC1_Bits.NDIP18 */
#define IFX_ERAY_NDIC1_NDIP18_OFF (18u)

/** \brief  Length for Ifx_ERAY_NDIC1_Bits.NDIP19 */
#define IFX_ERAY_NDIC1_NDIP19_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC1_Bits.NDIP19 */
#define IFX_ERAY_NDIC1_NDIP19_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC1_Bits.NDIP19 */
#define IFX_ERAY_NDIC1_NDIP19_OFF (19u)

/** \brief  Length for Ifx_ERAY_NDIC1_Bits.NDIP1 */
#define IFX_ERAY_NDIC1_NDIP1_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC1_Bits.NDIP1 */
#define IFX_ERAY_NDIC1_NDIP1_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC1_Bits.NDIP1 */
#define IFX_ERAY_NDIC1_NDIP1_OFF (1u)

/** \brief  Length for Ifx_ERAY_NDIC1_Bits.NDIP20 */
#define IFX_ERAY_NDIC1_NDIP20_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC1_Bits.NDIP20 */
#define IFX_ERAY_NDIC1_NDIP20_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC1_Bits.NDIP20 */
#define IFX_ERAY_NDIC1_NDIP20_OFF (20u)

/** \brief  Length for Ifx_ERAY_NDIC1_Bits.NDIP21 */
#define IFX_ERAY_NDIC1_NDIP21_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC1_Bits.NDIP21 */
#define IFX_ERAY_NDIC1_NDIP21_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC1_Bits.NDIP21 */
#define IFX_ERAY_NDIC1_NDIP21_OFF (21u)

/** \brief  Length for Ifx_ERAY_NDIC1_Bits.NDIP22 */
#define IFX_ERAY_NDIC1_NDIP22_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC1_Bits.NDIP22 */
#define IFX_ERAY_NDIC1_NDIP22_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC1_Bits.NDIP22 */
#define IFX_ERAY_NDIC1_NDIP22_OFF (22u)

/** \brief  Length for Ifx_ERAY_NDIC1_Bits.NDIP23 */
#define IFX_ERAY_NDIC1_NDIP23_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC1_Bits.NDIP23 */
#define IFX_ERAY_NDIC1_NDIP23_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC1_Bits.NDIP23 */
#define IFX_ERAY_NDIC1_NDIP23_OFF (23u)

/** \brief  Length for Ifx_ERAY_NDIC1_Bits.NDIP24 */
#define IFX_ERAY_NDIC1_NDIP24_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC1_Bits.NDIP24 */
#define IFX_ERAY_NDIC1_NDIP24_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC1_Bits.NDIP24 */
#define IFX_ERAY_NDIC1_NDIP24_OFF (24u)

/** \brief  Length for Ifx_ERAY_NDIC1_Bits.NDIP25 */
#define IFX_ERAY_NDIC1_NDIP25_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC1_Bits.NDIP25 */
#define IFX_ERAY_NDIC1_NDIP25_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC1_Bits.NDIP25 */
#define IFX_ERAY_NDIC1_NDIP25_OFF (25u)

/** \brief  Length for Ifx_ERAY_NDIC1_Bits.NDIP26 */
#define IFX_ERAY_NDIC1_NDIP26_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC1_Bits.NDIP26 */
#define IFX_ERAY_NDIC1_NDIP26_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC1_Bits.NDIP26 */
#define IFX_ERAY_NDIC1_NDIP26_OFF (26u)

/** \brief  Length for Ifx_ERAY_NDIC1_Bits.NDIP27 */
#define IFX_ERAY_NDIC1_NDIP27_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC1_Bits.NDIP27 */
#define IFX_ERAY_NDIC1_NDIP27_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC1_Bits.NDIP27 */
#define IFX_ERAY_NDIC1_NDIP27_OFF (27u)

/** \brief  Length for Ifx_ERAY_NDIC1_Bits.NDIP28 */
#define IFX_ERAY_NDIC1_NDIP28_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC1_Bits.NDIP28 */
#define IFX_ERAY_NDIC1_NDIP28_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC1_Bits.NDIP28 */
#define IFX_ERAY_NDIC1_NDIP28_OFF (28u)

/** \brief  Length for Ifx_ERAY_NDIC1_Bits.NDIP29 */
#define IFX_ERAY_NDIC1_NDIP29_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC1_Bits.NDIP29 */
#define IFX_ERAY_NDIC1_NDIP29_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC1_Bits.NDIP29 */
#define IFX_ERAY_NDIC1_NDIP29_OFF (29u)

/** \brief  Length for Ifx_ERAY_NDIC1_Bits.NDIP2 */
#define IFX_ERAY_NDIC1_NDIP2_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC1_Bits.NDIP2 */
#define IFX_ERAY_NDIC1_NDIP2_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC1_Bits.NDIP2 */
#define IFX_ERAY_NDIC1_NDIP2_OFF (2u)

/** \brief  Length for Ifx_ERAY_NDIC1_Bits.NDIP30 */
#define IFX_ERAY_NDIC1_NDIP30_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC1_Bits.NDIP30 */
#define IFX_ERAY_NDIC1_NDIP30_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC1_Bits.NDIP30 */
#define IFX_ERAY_NDIC1_NDIP30_OFF (30u)

/** \brief  Length for Ifx_ERAY_NDIC1_Bits.NDIP31 */
#define IFX_ERAY_NDIC1_NDIP31_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC1_Bits.NDIP31 */
#define IFX_ERAY_NDIC1_NDIP31_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC1_Bits.NDIP31 */
#define IFX_ERAY_NDIC1_NDIP31_OFF (31u)

/** \brief  Length for Ifx_ERAY_NDIC1_Bits.NDIP3 */
#define IFX_ERAY_NDIC1_NDIP3_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC1_Bits.NDIP3 */
#define IFX_ERAY_NDIC1_NDIP3_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC1_Bits.NDIP3 */
#define IFX_ERAY_NDIC1_NDIP3_OFF (3u)

/** \brief  Length for Ifx_ERAY_NDIC1_Bits.NDIP4 */
#define IFX_ERAY_NDIC1_NDIP4_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC1_Bits.NDIP4 */
#define IFX_ERAY_NDIC1_NDIP4_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC1_Bits.NDIP4 */
#define IFX_ERAY_NDIC1_NDIP4_OFF (4u)

/** \brief  Length for Ifx_ERAY_NDIC1_Bits.NDIP5 */
#define IFX_ERAY_NDIC1_NDIP5_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC1_Bits.NDIP5 */
#define IFX_ERAY_NDIC1_NDIP5_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC1_Bits.NDIP5 */
#define IFX_ERAY_NDIC1_NDIP5_OFF (5u)

/** \brief  Length for Ifx_ERAY_NDIC1_Bits.NDIP6 */
#define IFX_ERAY_NDIC1_NDIP6_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC1_Bits.NDIP6 */
#define IFX_ERAY_NDIC1_NDIP6_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC1_Bits.NDIP6 */
#define IFX_ERAY_NDIC1_NDIP6_OFF (6u)

/** \brief  Length for Ifx_ERAY_NDIC1_Bits.NDIP7 */
#define IFX_ERAY_NDIC1_NDIP7_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC1_Bits.NDIP7 */
#define IFX_ERAY_NDIC1_NDIP7_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC1_Bits.NDIP7 */
#define IFX_ERAY_NDIC1_NDIP7_OFF (7u)

/** \brief  Length for Ifx_ERAY_NDIC1_Bits.NDIP8 */
#define IFX_ERAY_NDIC1_NDIP8_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC1_Bits.NDIP8 */
#define IFX_ERAY_NDIC1_NDIP8_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC1_Bits.NDIP8 */
#define IFX_ERAY_NDIC1_NDIP8_OFF (8u)

/** \brief  Length for Ifx_ERAY_NDIC1_Bits.NDIP9 */
#define IFX_ERAY_NDIC1_NDIP9_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC1_Bits.NDIP9 */
#define IFX_ERAY_NDIC1_NDIP9_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC1_Bits.NDIP9 */
#define IFX_ERAY_NDIC1_NDIP9_OFF (9u)

/** \brief  Length for Ifx_ERAY_NDIC2_Bits.NDIP32 */
#define IFX_ERAY_NDIC2_NDIP32_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC2_Bits.NDIP32 */
#define IFX_ERAY_NDIC2_NDIP32_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC2_Bits.NDIP32 */
#define IFX_ERAY_NDIC2_NDIP32_OFF (0u)

/** \brief  Length for Ifx_ERAY_NDIC2_Bits.NDIP33 */
#define IFX_ERAY_NDIC2_NDIP33_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC2_Bits.NDIP33 */
#define IFX_ERAY_NDIC2_NDIP33_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC2_Bits.NDIP33 */
#define IFX_ERAY_NDIC2_NDIP33_OFF (1u)

/** \brief  Length for Ifx_ERAY_NDIC2_Bits.NDIP34 */
#define IFX_ERAY_NDIC2_NDIP34_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC2_Bits.NDIP34 */
#define IFX_ERAY_NDIC2_NDIP34_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC2_Bits.NDIP34 */
#define IFX_ERAY_NDIC2_NDIP34_OFF (2u)

/** \brief  Length for Ifx_ERAY_NDIC2_Bits.NDIP35 */
#define IFX_ERAY_NDIC2_NDIP35_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC2_Bits.NDIP35 */
#define IFX_ERAY_NDIC2_NDIP35_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC2_Bits.NDIP35 */
#define IFX_ERAY_NDIC2_NDIP35_OFF (3u)

/** \brief  Length for Ifx_ERAY_NDIC2_Bits.NDIP36 */
#define IFX_ERAY_NDIC2_NDIP36_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC2_Bits.NDIP36 */
#define IFX_ERAY_NDIC2_NDIP36_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC2_Bits.NDIP36 */
#define IFX_ERAY_NDIC2_NDIP36_OFF (4u)

/** \brief  Length for Ifx_ERAY_NDIC2_Bits.NDIP37 */
#define IFX_ERAY_NDIC2_NDIP37_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC2_Bits.NDIP37 */
#define IFX_ERAY_NDIC2_NDIP37_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC2_Bits.NDIP37 */
#define IFX_ERAY_NDIC2_NDIP37_OFF (5u)

/** \brief  Length for Ifx_ERAY_NDIC2_Bits.NDIP38 */
#define IFX_ERAY_NDIC2_NDIP38_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC2_Bits.NDIP38 */
#define IFX_ERAY_NDIC2_NDIP38_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC2_Bits.NDIP38 */
#define IFX_ERAY_NDIC2_NDIP38_OFF (6u)

/** \brief  Length for Ifx_ERAY_NDIC2_Bits.NDIP39 */
#define IFX_ERAY_NDIC2_NDIP39_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC2_Bits.NDIP39 */
#define IFX_ERAY_NDIC2_NDIP39_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC2_Bits.NDIP39 */
#define IFX_ERAY_NDIC2_NDIP39_OFF (7u)

/** \brief  Length for Ifx_ERAY_NDIC2_Bits.NDIP40 */
#define IFX_ERAY_NDIC2_NDIP40_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC2_Bits.NDIP40 */
#define IFX_ERAY_NDIC2_NDIP40_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC2_Bits.NDIP40 */
#define IFX_ERAY_NDIC2_NDIP40_OFF (8u)

/** \brief  Length for Ifx_ERAY_NDIC2_Bits.NDIP41 */
#define IFX_ERAY_NDIC2_NDIP41_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC2_Bits.NDIP41 */
#define IFX_ERAY_NDIC2_NDIP41_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC2_Bits.NDIP41 */
#define IFX_ERAY_NDIC2_NDIP41_OFF (9u)

/** \brief  Length for Ifx_ERAY_NDIC2_Bits.NDIP42 */
#define IFX_ERAY_NDIC2_NDIP42_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC2_Bits.NDIP42 */
#define IFX_ERAY_NDIC2_NDIP42_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC2_Bits.NDIP42 */
#define IFX_ERAY_NDIC2_NDIP42_OFF (10u)

/** \brief  Length for Ifx_ERAY_NDIC2_Bits.NDIP43 */
#define IFX_ERAY_NDIC2_NDIP43_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC2_Bits.NDIP43 */
#define IFX_ERAY_NDIC2_NDIP43_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC2_Bits.NDIP43 */
#define IFX_ERAY_NDIC2_NDIP43_OFF (11u)

/** \brief  Length for Ifx_ERAY_NDIC2_Bits.NDIP44 */
#define IFX_ERAY_NDIC2_NDIP44_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC2_Bits.NDIP44 */
#define IFX_ERAY_NDIC2_NDIP44_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC2_Bits.NDIP44 */
#define IFX_ERAY_NDIC2_NDIP44_OFF (12u)

/** \brief  Length for Ifx_ERAY_NDIC2_Bits.NDIP45 */
#define IFX_ERAY_NDIC2_NDIP45_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC2_Bits.NDIP45 */
#define IFX_ERAY_NDIC2_NDIP45_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC2_Bits.NDIP45 */
#define IFX_ERAY_NDIC2_NDIP45_OFF (13u)

/** \brief  Length for Ifx_ERAY_NDIC2_Bits.NDIP46 */
#define IFX_ERAY_NDIC2_NDIP46_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC2_Bits.NDIP46 */
#define IFX_ERAY_NDIC2_NDIP46_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC2_Bits.NDIP46 */
#define IFX_ERAY_NDIC2_NDIP46_OFF (14u)

/** \brief  Length for Ifx_ERAY_NDIC2_Bits.NDIP47 */
#define IFX_ERAY_NDIC2_NDIP47_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC2_Bits.NDIP47 */
#define IFX_ERAY_NDIC2_NDIP47_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC2_Bits.NDIP47 */
#define IFX_ERAY_NDIC2_NDIP47_OFF (15u)

/** \brief  Length for Ifx_ERAY_NDIC2_Bits.NDIP48 */
#define IFX_ERAY_NDIC2_NDIP48_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC2_Bits.NDIP48 */
#define IFX_ERAY_NDIC2_NDIP48_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC2_Bits.NDIP48 */
#define IFX_ERAY_NDIC2_NDIP48_OFF (16u)

/** \brief  Length for Ifx_ERAY_NDIC2_Bits.NDIP49 */
#define IFX_ERAY_NDIC2_NDIP49_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC2_Bits.NDIP49 */
#define IFX_ERAY_NDIC2_NDIP49_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC2_Bits.NDIP49 */
#define IFX_ERAY_NDIC2_NDIP49_OFF (17u)

/** \brief  Length for Ifx_ERAY_NDIC2_Bits.NDIP50 */
#define IFX_ERAY_NDIC2_NDIP50_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC2_Bits.NDIP50 */
#define IFX_ERAY_NDIC2_NDIP50_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC2_Bits.NDIP50 */
#define IFX_ERAY_NDIC2_NDIP50_OFF (18u)

/** \brief  Length for Ifx_ERAY_NDIC2_Bits.NDIP51 */
#define IFX_ERAY_NDIC2_NDIP51_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC2_Bits.NDIP51 */
#define IFX_ERAY_NDIC2_NDIP51_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC2_Bits.NDIP51 */
#define IFX_ERAY_NDIC2_NDIP51_OFF (19u)

/** \brief  Length for Ifx_ERAY_NDIC2_Bits.NDIP52 */
#define IFX_ERAY_NDIC2_NDIP52_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC2_Bits.NDIP52 */
#define IFX_ERAY_NDIC2_NDIP52_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC2_Bits.NDIP52 */
#define IFX_ERAY_NDIC2_NDIP52_OFF (20u)

/** \brief  Length for Ifx_ERAY_NDIC2_Bits.NDIP53 */
#define IFX_ERAY_NDIC2_NDIP53_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC2_Bits.NDIP53 */
#define IFX_ERAY_NDIC2_NDIP53_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC2_Bits.NDIP53 */
#define IFX_ERAY_NDIC2_NDIP53_OFF (21u)

/** \brief  Length for Ifx_ERAY_NDIC2_Bits.NDIP54 */
#define IFX_ERAY_NDIC2_NDIP54_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC2_Bits.NDIP54 */
#define IFX_ERAY_NDIC2_NDIP54_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC2_Bits.NDIP54 */
#define IFX_ERAY_NDIC2_NDIP54_OFF (22u)

/** \brief  Length for Ifx_ERAY_NDIC2_Bits.NDIP55 */
#define IFX_ERAY_NDIC2_NDIP55_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC2_Bits.NDIP55 */
#define IFX_ERAY_NDIC2_NDIP55_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC2_Bits.NDIP55 */
#define IFX_ERAY_NDIC2_NDIP55_OFF (23u)

/** \brief  Length for Ifx_ERAY_NDIC2_Bits.NDIP56 */
#define IFX_ERAY_NDIC2_NDIP56_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC2_Bits.NDIP56 */
#define IFX_ERAY_NDIC2_NDIP56_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC2_Bits.NDIP56 */
#define IFX_ERAY_NDIC2_NDIP56_OFF (24u)

/** \brief  Length for Ifx_ERAY_NDIC2_Bits.NDIP57 */
#define IFX_ERAY_NDIC2_NDIP57_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC2_Bits.NDIP57 */
#define IFX_ERAY_NDIC2_NDIP57_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC2_Bits.NDIP57 */
#define IFX_ERAY_NDIC2_NDIP57_OFF (25u)

/** \brief  Length for Ifx_ERAY_NDIC2_Bits.NDIP58 */
#define IFX_ERAY_NDIC2_NDIP58_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC2_Bits.NDIP58 */
#define IFX_ERAY_NDIC2_NDIP58_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC2_Bits.NDIP58 */
#define IFX_ERAY_NDIC2_NDIP58_OFF (26u)

/** \brief  Length for Ifx_ERAY_NDIC2_Bits.NDIP59 */
#define IFX_ERAY_NDIC2_NDIP59_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC2_Bits.NDIP59 */
#define IFX_ERAY_NDIC2_NDIP59_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC2_Bits.NDIP59 */
#define IFX_ERAY_NDIC2_NDIP59_OFF (27u)

/** \brief  Length for Ifx_ERAY_NDIC2_Bits.NDIP60 */
#define IFX_ERAY_NDIC2_NDIP60_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC2_Bits.NDIP60 */
#define IFX_ERAY_NDIC2_NDIP60_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC2_Bits.NDIP60 */
#define IFX_ERAY_NDIC2_NDIP60_OFF (28u)

/** \brief  Length for Ifx_ERAY_NDIC2_Bits.NDIP61 */
#define IFX_ERAY_NDIC2_NDIP61_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC2_Bits.NDIP61 */
#define IFX_ERAY_NDIC2_NDIP61_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC2_Bits.NDIP61 */
#define IFX_ERAY_NDIC2_NDIP61_OFF (29u)

/** \brief  Length for Ifx_ERAY_NDIC2_Bits.NDIP62 */
#define IFX_ERAY_NDIC2_NDIP62_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC2_Bits.NDIP62 */
#define IFX_ERAY_NDIC2_NDIP62_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC2_Bits.NDIP62 */
#define IFX_ERAY_NDIC2_NDIP62_OFF (30u)

/** \brief  Length for Ifx_ERAY_NDIC2_Bits.NDIP63 */
#define IFX_ERAY_NDIC2_NDIP63_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC2_Bits.NDIP63 */
#define IFX_ERAY_NDIC2_NDIP63_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC2_Bits.NDIP63 */
#define IFX_ERAY_NDIC2_NDIP63_OFF (31u)

/** \brief  Length for Ifx_ERAY_NDIC3_Bits.NDIP64 */
#define IFX_ERAY_NDIC3_NDIP64_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC3_Bits.NDIP64 */
#define IFX_ERAY_NDIC3_NDIP64_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC3_Bits.NDIP64 */
#define IFX_ERAY_NDIC3_NDIP64_OFF (0u)

/** \brief  Length for Ifx_ERAY_NDIC3_Bits.NDIP65 */
#define IFX_ERAY_NDIC3_NDIP65_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC3_Bits.NDIP65 */
#define IFX_ERAY_NDIC3_NDIP65_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC3_Bits.NDIP65 */
#define IFX_ERAY_NDIC3_NDIP65_OFF (1u)

/** \brief  Length for Ifx_ERAY_NDIC3_Bits.NDIP66 */
#define IFX_ERAY_NDIC3_NDIP66_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC3_Bits.NDIP66 */
#define IFX_ERAY_NDIC3_NDIP66_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC3_Bits.NDIP66 */
#define IFX_ERAY_NDIC3_NDIP66_OFF (2u)

/** \brief  Length for Ifx_ERAY_NDIC3_Bits.NDIP67 */
#define IFX_ERAY_NDIC3_NDIP67_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC3_Bits.NDIP67 */
#define IFX_ERAY_NDIC3_NDIP67_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC3_Bits.NDIP67 */
#define IFX_ERAY_NDIC3_NDIP67_OFF (3u)

/** \brief  Length for Ifx_ERAY_NDIC3_Bits.NDIP68 */
#define IFX_ERAY_NDIC3_NDIP68_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC3_Bits.NDIP68 */
#define IFX_ERAY_NDIC3_NDIP68_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC3_Bits.NDIP68 */
#define IFX_ERAY_NDIC3_NDIP68_OFF (4u)

/** \brief  Length for Ifx_ERAY_NDIC3_Bits.NDIP69 */
#define IFX_ERAY_NDIC3_NDIP69_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC3_Bits.NDIP69 */
#define IFX_ERAY_NDIC3_NDIP69_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC3_Bits.NDIP69 */
#define IFX_ERAY_NDIC3_NDIP69_OFF (5u)

/** \brief  Length for Ifx_ERAY_NDIC3_Bits.NDIP70 */
#define IFX_ERAY_NDIC3_NDIP70_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC3_Bits.NDIP70 */
#define IFX_ERAY_NDIC3_NDIP70_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC3_Bits.NDIP70 */
#define IFX_ERAY_NDIC3_NDIP70_OFF (6u)

/** \brief  Length for Ifx_ERAY_NDIC3_Bits.NDIP71 */
#define IFX_ERAY_NDIC3_NDIP71_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC3_Bits.NDIP71 */
#define IFX_ERAY_NDIC3_NDIP71_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC3_Bits.NDIP71 */
#define IFX_ERAY_NDIC3_NDIP71_OFF (7u)

/** \brief  Length for Ifx_ERAY_NDIC3_Bits.NDIP72 */
#define IFX_ERAY_NDIC3_NDIP72_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC3_Bits.NDIP72 */
#define IFX_ERAY_NDIC3_NDIP72_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC3_Bits.NDIP72 */
#define IFX_ERAY_NDIC3_NDIP72_OFF (8u)

/** \brief  Length for Ifx_ERAY_NDIC3_Bits.NDIP73 */
#define IFX_ERAY_NDIC3_NDIP73_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC3_Bits.NDIP73 */
#define IFX_ERAY_NDIC3_NDIP73_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC3_Bits.NDIP73 */
#define IFX_ERAY_NDIC3_NDIP73_OFF (9u)

/** \brief  Length for Ifx_ERAY_NDIC3_Bits.NDIP74 */
#define IFX_ERAY_NDIC3_NDIP74_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC3_Bits.NDIP74 */
#define IFX_ERAY_NDIC3_NDIP74_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC3_Bits.NDIP74 */
#define IFX_ERAY_NDIC3_NDIP74_OFF (10u)

/** \brief  Length for Ifx_ERAY_NDIC3_Bits.NDIP75 */
#define IFX_ERAY_NDIC3_NDIP75_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC3_Bits.NDIP75 */
#define IFX_ERAY_NDIC3_NDIP75_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC3_Bits.NDIP75 */
#define IFX_ERAY_NDIC3_NDIP75_OFF (11u)

/** \brief  Length for Ifx_ERAY_NDIC3_Bits.NDIP76 */
#define IFX_ERAY_NDIC3_NDIP76_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC3_Bits.NDIP76 */
#define IFX_ERAY_NDIC3_NDIP76_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC3_Bits.NDIP76 */
#define IFX_ERAY_NDIC3_NDIP76_OFF (12u)

/** \brief  Length for Ifx_ERAY_NDIC3_Bits.NDIP77 */
#define IFX_ERAY_NDIC3_NDIP77_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC3_Bits.NDIP77 */
#define IFX_ERAY_NDIC3_NDIP77_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC3_Bits.NDIP77 */
#define IFX_ERAY_NDIC3_NDIP77_OFF (13u)

/** \brief  Length for Ifx_ERAY_NDIC3_Bits.NDIP78 */
#define IFX_ERAY_NDIC3_NDIP78_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC3_Bits.NDIP78 */
#define IFX_ERAY_NDIC3_NDIP78_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC3_Bits.NDIP78 */
#define IFX_ERAY_NDIC3_NDIP78_OFF (14u)

/** \brief  Length for Ifx_ERAY_NDIC3_Bits.NDIP79 */
#define IFX_ERAY_NDIC3_NDIP79_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC3_Bits.NDIP79 */
#define IFX_ERAY_NDIC3_NDIP79_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC3_Bits.NDIP79 */
#define IFX_ERAY_NDIC3_NDIP79_OFF (15u)

/** \brief  Length for Ifx_ERAY_NDIC3_Bits.NDIP80 */
#define IFX_ERAY_NDIC3_NDIP80_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC3_Bits.NDIP80 */
#define IFX_ERAY_NDIC3_NDIP80_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC3_Bits.NDIP80 */
#define IFX_ERAY_NDIC3_NDIP80_OFF (16u)

/** \brief  Length for Ifx_ERAY_NDIC3_Bits.NDIP81 */
#define IFX_ERAY_NDIC3_NDIP81_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC3_Bits.NDIP81 */
#define IFX_ERAY_NDIC3_NDIP81_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC3_Bits.NDIP81 */
#define IFX_ERAY_NDIC3_NDIP81_OFF (17u)

/** \brief  Length for Ifx_ERAY_NDIC3_Bits.NDIP82 */
#define IFX_ERAY_NDIC3_NDIP82_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC3_Bits.NDIP82 */
#define IFX_ERAY_NDIC3_NDIP82_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC3_Bits.NDIP82 */
#define IFX_ERAY_NDIC3_NDIP82_OFF (18u)

/** \brief  Length for Ifx_ERAY_NDIC3_Bits.NDIP83 */
#define IFX_ERAY_NDIC3_NDIP83_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC3_Bits.NDIP83 */
#define IFX_ERAY_NDIC3_NDIP83_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC3_Bits.NDIP83 */
#define IFX_ERAY_NDIC3_NDIP83_OFF (19u)

/** \brief  Length for Ifx_ERAY_NDIC3_Bits.NDIP84 */
#define IFX_ERAY_NDIC3_NDIP84_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC3_Bits.NDIP84 */
#define IFX_ERAY_NDIC3_NDIP84_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC3_Bits.NDIP84 */
#define IFX_ERAY_NDIC3_NDIP84_OFF (20u)

/** \brief  Length for Ifx_ERAY_NDIC3_Bits.NDIP85 */
#define IFX_ERAY_NDIC3_NDIP85_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC3_Bits.NDIP85 */
#define IFX_ERAY_NDIC3_NDIP85_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC3_Bits.NDIP85 */
#define IFX_ERAY_NDIC3_NDIP85_OFF (21u)

/** \brief  Length for Ifx_ERAY_NDIC3_Bits.NDIP86 */
#define IFX_ERAY_NDIC3_NDIP86_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC3_Bits.NDIP86 */
#define IFX_ERAY_NDIC3_NDIP86_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC3_Bits.NDIP86 */
#define IFX_ERAY_NDIC3_NDIP86_OFF (22u)

/** \brief  Length for Ifx_ERAY_NDIC3_Bits.NDIP87 */
#define IFX_ERAY_NDIC3_NDIP87_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC3_Bits.NDIP87 */
#define IFX_ERAY_NDIC3_NDIP87_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC3_Bits.NDIP87 */
#define IFX_ERAY_NDIC3_NDIP87_OFF (23u)

/** \brief  Length for Ifx_ERAY_NDIC3_Bits.NDIP88 */
#define IFX_ERAY_NDIC3_NDIP88_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC3_Bits.NDIP88 */
#define IFX_ERAY_NDIC3_NDIP88_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC3_Bits.NDIP88 */
#define IFX_ERAY_NDIC3_NDIP88_OFF (24u)

/** \brief  Length for Ifx_ERAY_NDIC3_Bits.NDIP89 */
#define IFX_ERAY_NDIC3_NDIP89_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC3_Bits.NDIP89 */
#define IFX_ERAY_NDIC3_NDIP89_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC3_Bits.NDIP89 */
#define IFX_ERAY_NDIC3_NDIP89_OFF (25u)

/** \brief  Length for Ifx_ERAY_NDIC3_Bits.NDIP90 */
#define IFX_ERAY_NDIC3_NDIP90_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC3_Bits.NDIP90 */
#define IFX_ERAY_NDIC3_NDIP90_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC3_Bits.NDIP90 */
#define IFX_ERAY_NDIC3_NDIP90_OFF (26u)

/** \brief  Length for Ifx_ERAY_NDIC3_Bits.NDIP91 */
#define IFX_ERAY_NDIC3_NDIP91_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC3_Bits.NDIP91 */
#define IFX_ERAY_NDIC3_NDIP91_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC3_Bits.NDIP91 */
#define IFX_ERAY_NDIC3_NDIP91_OFF (27u)

/** \brief  Length for Ifx_ERAY_NDIC3_Bits.NDIP92 */
#define IFX_ERAY_NDIC3_NDIP92_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC3_Bits.NDIP92 */
#define IFX_ERAY_NDIC3_NDIP92_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC3_Bits.NDIP92 */
#define IFX_ERAY_NDIC3_NDIP92_OFF (28u)

/** \brief  Length for Ifx_ERAY_NDIC3_Bits.NDIP93 */
#define IFX_ERAY_NDIC3_NDIP93_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC3_Bits.NDIP93 */
#define IFX_ERAY_NDIC3_NDIP93_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC3_Bits.NDIP93 */
#define IFX_ERAY_NDIC3_NDIP93_OFF (29u)

/** \brief  Length for Ifx_ERAY_NDIC3_Bits.NDIP94 */
#define IFX_ERAY_NDIC3_NDIP94_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC3_Bits.NDIP94 */
#define IFX_ERAY_NDIC3_NDIP94_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC3_Bits.NDIP94 */
#define IFX_ERAY_NDIC3_NDIP94_OFF (30u)

/** \brief  Length for Ifx_ERAY_NDIC3_Bits.NDIP95 */
#define IFX_ERAY_NDIC3_NDIP95_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC3_Bits.NDIP95 */
#define IFX_ERAY_NDIC3_NDIP95_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC3_Bits.NDIP95 */
#define IFX_ERAY_NDIC3_NDIP95_OFF (31u)

/** \brief  Length for Ifx_ERAY_NDIC4_Bits.NDIP100 */
#define IFX_ERAY_NDIC4_NDIP100_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC4_Bits.NDIP100 */
#define IFX_ERAY_NDIC4_NDIP100_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC4_Bits.NDIP100 */
#define IFX_ERAY_NDIC4_NDIP100_OFF (4u)

/** \brief  Length for Ifx_ERAY_NDIC4_Bits.NDIP101 */
#define IFX_ERAY_NDIC4_NDIP101_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC4_Bits.NDIP101 */
#define IFX_ERAY_NDIC4_NDIP101_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC4_Bits.NDIP101 */
#define IFX_ERAY_NDIC4_NDIP101_OFF (5u)

/** \brief  Length for Ifx_ERAY_NDIC4_Bits.NDIP102 */
#define IFX_ERAY_NDIC4_NDIP102_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC4_Bits.NDIP102 */
#define IFX_ERAY_NDIC4_NDIP102_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC4_Bits.NDIP102 */
#define IFX_ERAY_NDIC4_NDIP102_OFF (6u)

/** \brief  Length for Ifx_ERAY_NDIC4_Bits.NDIP103 */
#define IFX_ERAY_NDIC4_NDIP103_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC4_Bits.NDIP103 */
#define IFX_ERAY_NDIC4_NDIP103_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC4_Bits.NDIP103 */
#define IFX_ERAY_NDIC4_NDIP103_OFF (7u)

/** \brief  Length for Ifx_ERAY_NDIC4_Bits.NDIP104 */
#define IFX_ERAY_NDIC4_NDIP104_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC4_Bits.NDIP104 */
#define IFX_ERAY_NDIC4_NDIP104_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC4_Bits.NDIP104 */
#define IFX_ERAY_NDIC4_NDIP104_OFF (8u)

/** \brief  Length for Ifx_ERAY_NDIC4_Bits.NDIP105 */
#define IFX_ERAY_NDIC4_NDIP105_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC4_Bits.NDIP105 */
#define IFX_ERAY_NDIC4_NDIP105_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC4_Bits.NDIP105 */
#define IFX_ERAY_NDIC4_NDIP105_OFF (9u)

/** \brief  Length for Ifx_ERAY_NDIC4_Bits.NDIP106 */
#define IFX_ERAY_NDIC4_NDIP106_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC4_Bits.NDIP106 */
#define IFX_ERAY_NDIC4_NDIP106_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC4_Bits.NDIP106 */
#define IFX_ERAY_NDIC4_NDIP106_OFF (10u)

/** \brief  Length for Ifx_ERAY_NDIC4_Bits.NDIP107 */
#define IFX_ERAY_NDIC4_NDIP107_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC4_Bits.NDIP107 */
#define IFX_ERAY_NDIC4_NDIP107_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC4_Bits.NDIP107 */
#define IFX_ERAY_NDIC4_NDIP107_OFF (11u)

/** \brief  Length for Ifx_ERAY_NDIC4_Bits.NDIP108 */
#define IFX_ERAY_NDIC4_NDIP108_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC4_Bits.NDIP108 */
#define IFX_ERAY_NDIC4_NDIP108_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC4_Bits.NDIP108 */
#define IFX_ERAY_NDIC4_NDIP108_OFF (12u)

/** \brief  Length for Ifx_ERAY_NDIC4_Bits.NDIP109 */
#define IFX_ERAY_NDIC4_NDIP109_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC4_Bits.NDIP109 */
#define IFX_ERAY_NDIC4_NDIP109_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC4_Bits.NDIP109 */
#define IFX_ERAY_NDIC4_NDIP109_OFF (13u)

/** \brief  Length for Ifx_ERAY_NDIC4_Bits.NDIP110 */
#define IFX_ERAY_NDIC4_NDIP110_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC4_Bits.NDIP110 */
#define IFX_ERAY_NDIC4_NDIP110_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC4_Bits.NDIP110 */
#define IFX_ERAY_NDIC4_NDIP110_OFF (14u)

/** \brief  Length for Ifx_ERAY_NDIC4_Bits.NDIP111 */
#define IFX_ERAY_NDIC4_NDIP111_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC4_Bits.NDIP111 */
#define IFX_ERAY_NDIC4_NDIP111_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC4_Bits.NDIP111 */
#define IFX_ERAY_NDIC4_NDIP111_OFF (15u)

/** \brief  Length for Ifx_ERAY_NDIC4_Bits.NDIP112 */
#define IFX_ERAY_NDIC4_NDIP112_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC4_Bits.NDIP112 */
#define IFX_ERAY_NDIC4_NDIP112_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC4_Bits.NDIP112 */
#define IFX_ERAY_NDIC4_NDIP112_OFF (16u)

/** \brief  Length for Ifx_ERAY_NDIC4_Bits.NDIP113 */
#define IFX_ERAY_NDIC4_NDIP113_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC4_Bits.NDIP113 */
#define IFX_ERAY_NDIC4_NDIP113_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC4_Bits.NDIP113 */
#define IFX_ERAY_NDIC4_NDIP113_OFF (17u)

/** \brief  Length for Ifx_ERAY_NDIC4_Bits.NDIP114 */
#define IFX_ERAY_NDIC4_NDIP114_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC4_Bits.NDIP114 */
#define IFX_ERAY_NDIC4_NDIP114_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC4_Bits.NDIP114 */
#define IFX_ERAY_NDIC4_NDIP114_OFF (18u)

/** \brief  Length for Ifx_ERAY_NDIC4_Bits.NDIP115 */
#define IFX_ERAY_NDIC4_NDIP115_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC4_Bits.NDIP115 */
#define IFX_ERAY_NDIC4_NDIP115_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC4_Bits.NDIP115 */
#define IFX_ERAY_NDIC4_NDIP115_OFF (19u)

/** \brief  Length for Ifx_ERAY_NDIC4_Bits.NDIP116 */
#define IFX_ERAY_NDIC4_NDIP116_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC4_Bits.NDIP116 */
#define IFX_ERAY_NDIC4_NDIP116_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC4_Bits.NDIP116 */
#define IFX_ERAY_NDIC4_NDIP116_OFF (20u)

/** \brief  Length for Ifx_ERAY_NDIC4_Bits.NDIP117 */
#define IFX_ERAY_NDIC4_NDIP117_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC4_Bits.NDIP117 */
#define IFX_ERAY_NDIC4_NDIP117_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC4_Bits.NDIP117 */
#define IFX_ERAY_NDIC4_NDIP117_OFF (21u)

/** \brief  Length for Ifx_ERAY_NDIC4_Bits.NDIP118 */
#define IFX_ERAY_NDIC4_NDIP118_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC4_Bits.NDIP118 */
#define IFX_ERAY_NDIC4_NDIP118_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC4_Bits.NDIP118 */
#define IFX_ERAY_NDIC4_NDIP118_OFF (22u)

/** \brief  Length for Ifx_ERAY_NDIC4_Bits.NDIP119 */
#define IFX_ERAY_NDIC4_NDIP119_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC4_Bits.NDIP119 */
#define IFX_ERAY_NDIC4_NDIP119_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC4_Bits.NDIP119 */
#define IFX_ERAY_NDIC4_NDIP119_OFF (23u)

/** \brief  Length for Ifx_ERAY_NDIC4_Bits.NDIP120 */
#define IFX_ERAY_NDIC4_NDIP120_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC4_Bits.NDIP120 */
#define IFX_ERAY_NDIC4_NDIP120_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC4_Bits.NDIP120 */
#define IFX_ERAY_NDIC4_NDIP120_OFF (24u)

/** \brief  Length for Ifx_ERAY_NDIC4_Bits.NDIP121 */
#define IFX_ERAY_NDIC4_NDIP121_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC4_Bits.NDIP121 */
#define IFX_ERAY_NDIC4_NDIP121_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC4_Bits.NDIP121 */
#define IFX_ERAY_NDIC4_NDIP121_OFF (25u)

/** \brief  Length for Ifx_ERAY_NDIC4_Bits.NDIP122 */
#define IFX_ERAY_NDIC4_NDIP122_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC4_Bits.NDIP122 */
#define IFX_ERAY_NDIC4_NDIP122_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC4_Bits.NDIP122 */
#define IFX_ERAY_NDIC4_NDIP122_OFF (26u)

/** \brief  Length for Ifx_ERAY_NDIC4_Bits.NDIP123 */
#define IFX_ERAY_NDIC4_NDIP123_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC4_Bits.NDIP123 */
#define IFX_ERAY_NDIC4_NDIP123_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC4_Bits.NDIP123 */
#define IFX_ERAY_NDIC4_NDIP123_OFF (27u)

/** \brief  Length for Ifx_ERAY_NDIC4_Bits.NDIP124 */
#define IFX_ERAY_NDIC4_NDIP124_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC4_Bits.NDIP124 */
#define IFX_ERAY_NDIC4_NDIP124_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC4_Bits.NDIP124 */
#define IFX_ERAY_NDIC4_NDIP124_OFF (28u)

/** \brief  Length for Ifx_ERAY_NDIC4_Bits.NDIP125 */
#define IFX_ERAY_NDIC4_NDIP125_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC4_Bits.NDIP125 */
#define IFX_ERAY_NDIC4_NDIP125_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC4_Bits.NDIP125 */
#define IFX_ERAY_NDIC4_NDIP125_OFF (29u)

/** \brief  Length for Ifx_ERAY_NDIC4_Bits.NDIP126 */
#define IFX_ERAY_NDIC4_NDIP126_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC4_Bits.NDIP126 */
#define IFX_ERAY_NDIC4_NDIP126_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC4_Bits.NDIP126 */
#define IFX_ERAY_NDIC4_NDIP126_OFF (30u)

/** \brief  Length for Ifx_ERAY_NDIC4_Bits.NDIP127 */
#define IFX_ERAY_NDIC4_NDIP127_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC4_Bits.NDIP127 */
#define IFX_ERAY_NDIC4_NDIP127_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC4_Bits.NDIP127 */
#define IFX_ERAY_NDIC4_NDIP127_OFF (31u)

/** \brief  Length for Ifx_ERAY_NDIC4_Bits.NDIP96 */
#define IFX_ERAY_NDIC4_NDIP96_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC4_Bits.NDIP96 */
#define IFX_ERAY_NDIC4_NDIP96_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC4_Bits.NDIP96 */
#define IFX_ERAY_NDIC4_NDIP96_OFF (0u)

/** \brief  Length for Ifx_ERAY_NDIC4_Bits.NDIP97 */
#define IFX_ERAY_NDIC4_NDIP97_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC4_Bits.NDIP97 */
#define IFX_ERAY_NDIC4_NDIP97_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC4_Bits.NDIP97 */
#define IFX_ERAY_NDIC4_NDIP97_OFF (1u)

/** \brief  Length for Ifx_ERAY_NDIC4_Bits.NDIP98 */
#define IFX_ERAY_NDIC4_NDIP98_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC4_Bits.NDIP98 */
#define IFX_ERAY_NDIC4_NDIP98_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC4_Bits.NDIP98 */
#define IFX_ERAY_NDIC4_NDIP98_OFF (2u)

/** \brief  Length for Ifx_ERAY_NDIC4_Bits.NDIP99 */
#define IFX_ERAY_NDIC4_NDIP99_LEN (1u)

/** \brief  Mask for Ifx_ERAY_NDIC4_Bits.NDIP99 */
#define IFX_ERAY_NDIC4_NDIP99_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_NDIC4_Bits.NDIP99 */
#define IFX_ERAY_NDIC4_NDIP99_OFF (3u)

/** \brief  Length for Ifx_ERAY_NEMC_Bits.NML */
#define IFX_ERAY_NEMC_NML_LEN (4u)

/** \brief  Mask for Ifx_ERAY_NEMC_Bits.NML */
#define IFX_ERAY_NEMC_NML_MSK (0xfu)

/** \brief  Offset for Ifx_ERAY_NEMC_Bits.NML */
#define IFX_ERAY_NEMC_NML_OFF (0u)

/** \brief  Length for Ifx_ERAY_NMV_Bits.NM */
#define IFX_ERAY_NMV_NM_LEN (32u)

/** \brief  Mask for Ifx_ERAY_NMV_Bits.NM */
#define IFX_ERAY_NMV_NM_MSK (0xffffffffu)

/** \brief  Offset for Ifx_ERAY_NMV_Bits.NM */
#define IFX_ERAY_NMV_NM_OFF (0u)

/** \brief  Length for Ifx_ERAY_OBCM_Bits.RDSH */
#define IFX_ERAY_OBCM_RDSH_LEN (1u)

/** \brief  Mask for Ifx_ERAY_OBCM_Bits.RDSH */
#define IFX_ERAY_OBCM_RDSH_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_OBCM_Bits.RDSH */
#define IFX_ERAY_OBCM_RDSH_OFF (17u)

/** \brief  Length for Ifx_ERAY_OBCM_Bits.RDSS */
#define IFX_ERAY_OBCM_RDSS_LEN (1u)

/** \brief  Mask for Ifx_ERAY_OBCM_Bits.RDSS */
#define IFX_ERAY_OBCM_RDSS_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_OBCM_Bits.RDSS */
#define IFX_ERAY_OBCM_RDSS_OFF (1u)

/** \brief  Length for Ifx_ERAY_OBCM_Bits.RHSH */
#define IFX_ERAY_OBCM_RHSH_LEN (1u)

/** \brief  Mask for Ifx_ERAY_OBCM_Bits.RHSH */
#define IFX_ERAY_OBCM_RHSH_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_OBCM_Bits.RHSH */
#define IFX_ERAY_OBCM_RHSH_OFF (16u)

/** \brief  Length for Ifx_ERAY_OBCM_Bits.RHSS */
#define IFX_ERAY_OBCM_RHSS_LEN (1u)

/** \brief  Mask for Ifx_ERAY_OBCM_Bits.RHSS */
#define IFX_ERAY_OBCM_RHSS_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_OBCM_Bits.RHSS */
#define IFX_ERAY_OBCM_RHSS_OFF (0u)

/** \brief  Length for Ifx_ERAY_OBCR_Bits.OBRH */
#define IFX_ERAY_OBCR_OBRH_LEN (7u)

/** \brief  Mask for Ifx_ERAY_OBCR_Bits.OBRH */
#define IFX_ERAY_OBCR_OBRH_MSK (0x7fu)

/** \brief  Offset for Ifx_ERAY_OBCR_Bits.OBRH */
#define IFX_ERAY_OBCR_OBRH_OFF (16u)

/** \brief  Length for Ifx_ERAY_OBCR_Bits.OBRS */
#define IFX_ERAY_OBCR_OBRS_LEN (7u)

/** \brief  Mask for Ifx_ERAY_OBCR_Bits.OBRS */
#define IFX_ERAY_OBCR_OBRS_MSK (0x7fu)

/** \brief  Offset for Ifx_ERAY_OBCR_Bits.OBRS */
#define IFX_ERAY_OBCR_OBRS_OFF (0u)

/** \brief  Length for Ifx_ERAY_OBCR_Bits.OBSYS */
#define IFX_ERAY_OBCR_OBSYS_LEN (1u)

/** \brief  Mask for Ifx_ERAY_OBCR_Bits.OBSYS */
#define IFX_ERAY_OBCR_OBSYS_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_OBCR_Bits.OBSYS */
#define IFX_ERAY_OBCR_OBSYS_OFF (15u)

/** \brief  Length for Ifx_ERAY_OBCR_Bits.REQ */
#define IFX_ERAY_OBCR_REQ_LEN (1u)

/** \brief  Mask for Ifx_ERAY_OBCR_Bits.REQ */
#define IFX_ERAY_OBCR_REQ_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_OBCR_Bits.REQ */
#define IFX_ERAY_OBCR_REQ_OFF (9u)

/** \brief  Length for Ifx_ERAY_OBCR_Bits.VIEW */
#define IFX_ERAY_OBCR_VIEW_LEN (1u)

/** \brief  Mask for Ifx_ERAY_OBCR_Bits.VIEW */
#define IFX_ERAY_OBCR_VIEW_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_OBCR_Bits.VIEW */
#define IFX_ERAY_OBCR_VIEW_OFF (8u)

/** \brief  Length for Ifx_ERAY_OCS_Bits.SUS */
#define IFX_ERAY_OCS_SUS_LEN (4u)

/** \brief  Mask for Ifx_ERAY_OCS_Bits.SUS */
#define IFX_ERAY_OCS_SUS_MSK (0xfu)

/** \brief  Offset for Ifx_ERAY_OCS_Bits.SUS */
#define IFX_ERAY_OCS_SUS_OFF (24u)

/** \brief  Length for Ifx_ERAY_OCS_Bits.SUS_P */
#define IFX_ERAY_OCS_SUS_P_LEN (1u)

/** \brief  Mask for Ifx_ERAY_OCS_Bits.SUS_P */
#define IFX_ERAY_OCS_SUS_P_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_OCS_Bits.SUS_P */
#define IFX_ERAY_OCS_SUS_P_OFF (28u)

/** \brief  Length for Ifx_ERAY_OCS_Bits.SUSSTA */
#define IFX_ERAY_OCS_SUSSTA_LEN (1u)

/** \brief  Mask for Ifx_ERAY_OCS_Bits.SUSSTA */
#define IFX_ERAY_OCS_SUSSTA_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_OCS_Bits.SUSSTA */
#define IFX_ERAY_OCS_SUSSTA_OFF (29u)

/** \brief  Length for Ifx_ERAY_OCV_Bits.OCV */
#define IFX_ERAY_OCV_OCV_LEN (19u)

/** \brief  Mask for Ifx_ERAY_OCV_Bits.OCV */
#define IFX_ERAY_OCV_OCV_MSK (0x7ffffu)

/** \brief  Offset for Ifx_ERAY_OCV_Bits.OCV */
#define IFX_ERAY_OCV_OCV_OFF (0u)

/** \brief  Length for Ifx_ERAY_OSID_Bits.OID */
#define IFX_ERAY_OSID_OID_LEN (10u)

/** \brief  Mask for Ifx_ERAY_OSID_Bits.OID */
#define IFX_ERAY_OSID_OID_MSK (0x3ffu)

/** \brief  Offset for Ifx_ERAY_OSID_Bits.OID */
#define IFX_ERAY_OSID_OID_OFF (0u)

/** \brief  Length for Ifx_ERAY_OSID_Bits.RXOA */
#define IFX_ERAY_OSID_RXOA_LEN (1u)

/** \brief  Mask for Ifx_ERAY_OSID_Bits.RXOA */
#define IFX_ERAY_OSID_RXOA_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_OSID_Bits.RXOA */
#define IFX_ERAY_OSID_RXOA_OFF (14u)

/** \brief  Length for Ifx_ERAY_OSID_Bits.RXOB */
#define IFX_ERAY_OSID_RXOB_LEN (1u)

/** \brief  Mask for Ifx_ERAY_OSID_Bits.RXOB */
#define IFX_ERAY_OSID_RXOB_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_OSID_Bits.RXOB */
#define IFX_ERAY_OSID_RXOB_OFF (15u)

/** \brief  Length for Ifx_ERAY_OTSS_Bits.OTGB0 */
#define IFX_ERAY_OTSS_OTGB0_LEN (2u)

/** \brief  Mask for Ifx_ERAY_OTSS_Bits.OTGB0 */
#define IFX_ERAY_OTSS_OTGB0_MSK (0x3u)

/** \brief  Offset for Ifx_ERAY_OTSS_Bits.OTGB0 */
#define IFX_ERAY_OTSS_OTGB0_OFF (0u)

/** \brief  Length for Ifx_ERAY_OTSS_Bits.OTGB1 */
#define IFX_ERAY_OTSS_OTGB1_LEN (2u)

/** \brief  Mask for Ifx_ERAY_OTSS_Bits.OTGB1 */
#define IFX_ERAY_OTSS_OTGB1_MSK (0x3u)

/** \brief  Offset for Ifx_ERAY_OTSS_Bits.OTGB1 */
#define IFX_ERAY_OTSS_OTGB1_OFF (8u)

/** \brief  Length for Ifx_ERAY_OTSS_Bits.OTGB2 */
#define IFX_ERAY_OTSS_OTGB2_LEN (1u)

/** \brief  Mask for Ifx_ERAY_OTSS_Bits.OTGB2 */
#define IFX_ERAY_OTSS_OTGB2_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_OTSS_Bits.OTGB2 */
#define IFX_ERAY_OTSS_OTGB2_OFF (16u)

/** \brief  Length for Ifx_ERAY_PRTC1_Bits.BRP */
#define IFX_ERAY_PRTC1_BRP_LEN (2u)

/** \brief  Mask for Ifx_ERAY_PRTC1_Bits.BRP */
#define IFX_ERAY_PRTC1_BRP_MSK (0x3u)

/** \brief  Offset for Ifx_ERAY_PRTC1_Bits.BRP */
#define IFX_ERAY_PRTC1_BRP_OFF (14u)

/** \brief  Length for Ifx_ERAY_PRTC1_Bits.CASM */
#define IFX_ERAY_PRTC1_CASM_LEN (7u)

/** \brief  Mask for Ifx_ERAY_PRTC1_Bits.CASM */
#define IFX_ERAY_PRTC1_CASM_MSK (0x7fu)

/** \brief  Offset for Ifx_ERAY_PRTC1_Bits.CASM */
#define IFX_ERAY_PRTC1_CASM_OFF (4u)

/** \brief  Length for Ifx_ERAY_PRTC1_Bits.RWP */
#define IFX_ERAY_PRTC1_RWP_LEN (6u)

/** \brief  Mask for Ifx_ERAY_PRTC1_Bits.RWP */
#define IFX_ERAY_PRTC1_RWP_MSK (0x3fu)

/** \brief  Offset for Ifx_ERAY_PRTC1_Bits.RWP */
#define IFX_ERAY_PRTC1_RWP_OFF (26u)

/** \brief  Length for Ifx_ERAY_PRTC1_Bits.RXW */
#define IFX_ERAY_PRTC1_RXW_LEN (9u)

/** \brief  Mask for Ifx_ERAY_PRTC1_Bits.RXW */
#define IFX_ERAY_PRTC1_RXW_MSK (0x1ffu)

/** \brief  Offset for Ifx_ERAY_PRTC1_Bits.RXW */
#define IFX_ERAY_PRTC1_RXW_OFF (16u)

/** \brief  Length for Ifx_ERAY_PRTC1_Bits.SPP */
#define IFX_ERAY_PRTC1_SPP_LEN (2u)

/** \brief  Mask for Ifx_ERAY_PRTC1_Bits.SPP */
#define IFX_ERAY_PRTC1_SPP_MSK (0x3u)

/** \brief  Offset for Ifx_ERAY_PRTC1_Bits.SPP */
#define IFX_ERAY_PRTC1_SPP_OFF (12u)

/** \brief  Length for Ifx_ERAY_PRTC1_Bits.TSST */
#define IFX_ERAY_PRTC1_TSST_LEN (4u)

/** \brief  Mask for Ifx_ERAY_PRTC1_Bits.TSST */
#define IFX_ERAY_PRTC1_TSST_MSK (0xfu)

/** \brief  Offset for Ifx_ERAY_PRTC1_Bits.TSST */
#define IFX_ERAY_PRTC1_TSST_OFF (0u)

/** \brief  Length for Ifx_ERAY_PRTC2_Bits.RXI */
#define IFX_ERAY_PRTC2_RXI_LEN (6u)

/** \brief  Mask for Ifx_ERAY_PRTC2_Bits.RXI */
#define IFX_ERAY_PRTC2_RXI_MSK (0x3fu)

/** \brief  Offset for Ifx_ERAY_PRTC2_Bits.RXI */
#define IFX_ERAY_PRTC2_RXI_OFF (0u)

/** \brief  Length for Ifx_ERAY_PRTC2_Bits.RXL */
#define IFX_ERAY_PRTC2_RXL_LEN (6u)

/** \brief  Mask for Ifx_ERAY_PRTC2_Bits.RXL */
#define IFX_ERAY_PRTC2_RXL_MSK (0x3fu)

/** \brief  Offset for Ifx_ERAY_PRTC2_Bits.RXL */
#define IFX_ERAY_PRTC2_RXL_OFF (8u)

/** \brief  Length for Ifx_ERAY_PRTC2_Bits.TXI */
#define IFX_ERAY_PRTC2_TXI_LEN (8u)

/** \brief  Mask for Ifx_ERAY_PRTC2_Bits.TXI */
#define IFX_ERAY_PRTC2_TXI_MSK (0xffu)

/** \brief  Offset for Ifx_ERAY_PRTC2_Bits.TXI */
#define IFX_ERAY_PRTC2_TXI_OFF (16u)

/** \brief  Length for Ifx_ERAY_PRTC2_Bits.TXL */
#define IFX_ERAY_PRTC2_TXL_LEN (6u)

/** \brief  Mask for Ifx_ERAY_PRTC2_Bits.TXL */
#define IFX_ERAY_PRTC2_TXL_MSK (0x3fu)

/** \brief  Offset for Ifx_ERAY_PRTC2_Bits.TXL */
#define IFX_ERAY_PRTC2_TXL_OFF (24u)

/** \brief  Length for Ifx_ERAY_RCV_Bits.RCV */
#define IFX_ERAY_RCV_RCV_LEN (12u)

/** \brief  Mask for Ifx_ERAY_RCV_Bits.RCV */
#define IFX_ERAY_RCV_RCV_MSK (0xfffu)

/** \brief  Offset for Ifx_ERAY_RCV_Bits.RCV */
#define IFX_ERAY_RCV_RCV_OFF (0u)

/** \brief  Length for Ifx_ERAY_RDDS_Bits.MDRB0 */
#define IFX_ERAY_RDDS_MDRB0_LEN (8u)

/** \brief  Mask for Ifx_ERAY_RDDS_Bits.MDRB0 */
#define IFX_ERAY_RDDS_MDRB0_MSK (0xffu)

/** \brief  Offset for Ifx_ERAY_RDDS_Bits.MDRB0 */
#define IFX_ERAY_RDDS_MDRB0_OFF (0u)

/** \brief  Length for Ifx_ERAY_RDDS_Bits.MDRB1 */
#define IFX_ERAY_RDDS_MDRB1_LEN (8u)

/** \brief  Mask for Ifx_ERAY_RDDS_Bits.MDRB1 */
#define IFX_ERAY_RDDS_MDRB1_MSK (0xffu)

/** \brief  Offset for Ifx_ERAY_RDDS_Bits.MDRB1 */
#define IFX_ERAY_RDDS_MDRB1_OFF (8u)

/** \brief  Length for Ifx_ERAY_RDDS_Bits.MDRB2 */
#define IFX_ERAY_RDDS_MDRB2_LEN (8u)

/** \brief  Mask for Ifx_ERAY_RDDS_Bits.MDRB2 */
#define IFX_ERAY_RDDS_MDRB2_MSK (0xffu)

/** \brief  Offset for Ifx_ERAY_RDDS_Bits.MDRB2 */
#define IFX_ERAY_RDDS_MDRB2_OFF (16u)

/** \brief  Length for Ifx_ERAY_RDDS_Bits.MDRB3 */
#define IFX_ERAY_RDDS_MDRB3_LEN (8u)

/** \brief  Mask for Ifx_ERAY_RDDS_Bits.MDRB3 */
#define IFX_ERAY_RDDS_MDRB3_MSK (0xffu)

/** \brief  Offset for Ifx_ERAY_RDDS_Bits.MDRB3 */
#define IFX_ERAY_RDDS_MDRB3_OFF (24u)

/** \brief  Length for Ifx_ERAY_RDHS1_Bits.CFG */
#define IFX_ERAY_RDHS1_CFG_LEN (1u)

/** \brief  Mask for Ifx_ERAY_RDHS1_Bits.CFG */
#define IFX_ERAY_RDHS1_CFG_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_RDHS1_Bits.CFG */
#define IFX_ERAY_RDHS1_CFG_OFF (26u)

/** \brief  Length for Ifx_ERAY_RDHS1_Bits.CHA */
#define IFX_ERAY_RDHS1_CHA_LEN (1u)

/** \brief  Mask for Ifx_ERAY_RDHS1_Bits.CHA */
#define IFX_ERAY_RDHS1_CHA_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_RDHS1_Bits.CHA */
#define IFX_ERAY_RDHS1_CHA_OFF (24u)

/** \brief  Length for Ifx_ERAY_RDHS1_Bits.CHB */
#define IFX_ERAY_RDHS1_CHB_LEN (1u)

/** \brief  Mask for Ifx_ERAY_RDHS1_Bits.CHB */
#define IFX_ERAY_RDHS1_CHB_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_RDHS1_Bits.CHB */
#define IFX_ERAY_RDHS1_CHB_OFF (25u)

/** \brief  Length for Ifx_ERAY_RDHS1_Bits.CYC */
#define IFX_ERAY_RDHS1_CYC_LEN (7u)

/** \brief  Mask for Ifx_ERAY_RDHS1_Bits.CYC */
#define IFX_ERAY_RDHS1_CYC_MSK (0x7fu)

/** \brief  Offset for Ifx_ERAY_RDHS1_Bits.CYC */
#define IFX_ERAY_RDHS1_CYC_OFF (16u)

/** \brief  Length for Ifx_ERAY_RDHS1_Bits.FID */
#define IFX_ERAY_RDHS1_FID_LEN (11u)

/** \brief  Mask for Ifx_ERAY_RDHS1_Bits.FID */
#define IFX_ERAY_RDHS1_FID_MSK (0x7ffu)

/** \brief  Offset for Ifx_ERAY_RDHS1_Bits.FID */
#define IFX_ERAY_RDHS1_FID_OFF (0u)

/** \brief  Length for Ifx_ERAY_RDHS1_Bits.MBI */
#define IFX_ERAY_RDHS1_MBI_LEN (1u)

/** \brief  Mask for Ifx_ERAY_RDHS1_Bits.MBI */
#define IFX_ERAY_RDHS1_MBI_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_RDHS1_Bits.MBI */
#define IFX_ERAY_RDHS1_MBI_OFF (29u)

/** \brief  Length for Ifx_ERAY_RDHS1_Bits.PPIT */
#define IFX_ERAY_RDHS1_PPIT_LEN (1u)

/** \brief  Mask for Ifx_ERAY_RDHS1_Bits.PPIT */
#define IFX_ERAY_RDHS1_PPIT_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_RDHS1_Bits.PPIT */
#define IFX_ERAY_RDHS1_PPIT_OFF (27u)

/** \brief  Length for Ifx_ERAY_RDHS1_Bits.TXM */
#define IFX_ERAY_RDHS1_TXM_LEN (1u)

/** \brief  Mask for Ifx_ERAY_RDHS1_Bits.TXM */
#define IFX_ERAY_RDHS1_TXM_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_RDHS1_Bits.TXM */
#define IFX_ERAY_RDHS1_TXM_OFF (28u)

/** \brief  Length for Ifx_ERAY_RDHS2_Bits.CRC */
#define IFX_ERAY_RDHS2_CRC_LEN (11u)

/** \brief  Mask for Ifx_ERAY_RDHS2_Bits.CRC */
#define IFX_ERAY_RDHS2_CRC_MSK (0x7ffu)

/** \brief  Offset for Ifx_ERAY_RDHS2_Bits.CRC */
#define IFX_ERAY_RDHS2_CRC_OFF (0u)

/** \brief  Length for Ifx_ERAY_RDHS2_Bits.PLC */
#define IFX_ERAY_RDHS2_PLC_LEN (7u)

/** \brief  Mask for Ifx_ERAY_RDHS2_Bits.PLC */
#define IFX_ERAY_RDHS2_PLC_MSK (0x7fu)

/** \brief  Offset for Ifx_ERAY_RDHS2_Bits.PLC */
#define IFX_ERAY_RDHS2_PLC_OFF (16u)

/** \brief  Length for Ifx_ERAY_RDHS2_Bits.PLR */
#define IFX_ERAY_RDHS2_PLR_LEN (7u)

/** \brief  Mask for Ifx_ERAY_RDHS2_Bits.PLR */
#define IFX_ERAY_RDHS2_PLR_MSK (0x7fu)

/** \brief  Offset for Ifx_ERAY_RDHS2_Bits.PLR */
#define IFX_ERAY_RDHS2_PLR_OFF (24u)

/** \brief  Length for Ifx_ERAY_RDHS3_Bits.DP */
#define IFX_ERAY_RDHS3_DP_LEN (11u)

/** \brief  Mask for Ifx_ERAY_RDHS3_Bits.DP */
#define IFX_ERAY_RDHS3_DP_MSK (0x7ffu)

/** \brief  Offset for Ifx_ERAY_RDHS3_Bits.DP */
#define IFX_ERAY_RDHS3_DP_OFF (0u)

/** \brief  Length for Ifx_ERAY_RDHS3_Bits.NFI */
#define IFX_ERAY_RDHS3_NFI_LEN (1u)

/** \brief  Mask for Ifx_ERAY_RDHS3_Bits.NFI */
#define IFX_ERAY_RDHS3_NFI_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_RDHS3_Bits.NFI */
#define IFX_ERAY_RDHS3_NFI_OFF (27u)

/** \brief  Length for Ifx_ERAY_RDHS3_Bits.PPI */
#define IFX_ERAY_RDHS3_PPI_LEN (1u)

/** \brief  Mask for Ifx_ERAY_RDHS3_Bits.PPI */
#define IFX_ERAY_RDHS3_PPI_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_RDHS3_Bits.PPI */
#define IFX_ERAY_RDHS3_PPI_OFF (28u)

/** \brief  Length for Ifx_ERAY_RDHS3_Bits.RCC */
#define IFX_ERAY_RDHS3_RCC_LEN (6u)

/** \brief  Mask for Ifx_ERAY_RDHS3_Bits.RCC */
#define IFX_ERAY_RDHS3_RCC_MSK (0x3fu)

/** \brief  Offset for Ifx_ERAY_RDHS3_Bits.RCC */
#define IFX_ERAY_RDHS3_RCC_OFF (16u)

/** \brief  Length for Ifx_ERAY_RDHS3_Bits.RCI */
#define IFX_ERAY_RDHS3_RCI_LEN (1u)

/** \brief  Mask for Ifx_ERAY_RDHS3_Bits.RCI */
#define IFX_ERAY_RDHS3_RCI_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_RDHS3_Bits.RCI */
#define IFX_ERAY_RDHS3_RCI_OFF (24u)

/** \brief  Length for Ifx_ERAY_RDHS3_Bits.RES */
#define IFX_ERAY_RDHS3_RES_LEN (1u)

/** \brief  Mask for Ifx_ERAY_RDHS3_Bits.RES */
#define IFX_ERAY_RDHS3_RES_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_RDHS3_Bits.RES */
#define IFX_ERAY_RDHS3_RES_OFF (29u)

/** \brief  Length for Ifx_ERAY_RDHS3_Bits.SFI */
#define IFX_ERAY_RDHS3_SFI_LEN (1u)

/** \brief  Mask for Ifx_ERAY_RDHS3_Bits.SFI */
#define IFX_ERAY_RDHS3_SFI_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_RDHS3_Bits.SFI */
#define IFX_ERAY_RDHS3_SFI_OFF (25u)

/** \brief  Length for Ifx_ERAY_RDHS3_Bits.SYN */
#define IFX_ERAY_RDHS3_SYN_LEN (1u)

/** \brief  Mask for Ifx_ERAY_RDHS3_Bits.SYN */
#define IFX_ERAY_RDHS3_SYN_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_RDHS3_Bits.SYN */
#define IFX_ERAY_RDHS3_SYN_OFF (26u)

/** \brief  Length for Ifx_ERAY_SCV_Bits.SCCA */
#define IFX_ERAY_SCV_SCCA_LEN (11u)

/** \brief  Mask for Ifx_ERAY_SCV_Bits.SCCA */
#define IFX_ERAY_SCV_SCCA_MSK (0x7ffu)

/** \brief  Offset for Ifx_ERAY_SCV_Bits.SCCA */
#define IFX_ERAY_SCV_SCCA_OFF (0u)

/** \brief  Length for Ifx_ERAY_SCV_Bits.SCCB */
#define IFX_ERAY_SCV_SCCB_LEN (11u)

/** \brief  Mask for Ifx_ERAY_SCV_Bits.SCCB */
#define IFX_ERAY_SCV_SCCB_MSK (0x7ffu)

/** \brief  Offset for Ifx_ERAY_SCV_Bits.SCCB */
#define IFX_ERAY_SCV_SCCB_OFF (16u)

/** \brief  Length for Ifx_ERAY_SFS_Bits.MOCS */
#define IFX_ERAY_SFS_MOCS_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SFS_Bits.MOCS */
#define IFX_ERAY_SFS_MOCS_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SFS_Bits.MOCS */
#define IFX_ERAY_SFS_MOCS_OFF (16u)

/** \brief  Length for Ifx_ERAY_SFS_Bits.MRCS */
#define IFX_ERAY_SFS_MRCS_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SFS_Bits.MRCS */
#define IFX_ERAY_SFS_MRCS_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SFS_Bits.MRCS */
#define IFX_ERAY_SFS_MRCS_OFF (18u)

/** \brief  Length for Ifx_ERAY_SFS_Bits.OCLR */
#define IFX_ERAY_SFS_OCLR_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SFS_Bits.OCLR */
#define IFX_ERAY_SFS_OCLR_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SFS_Bits.OCLR */
#define IFX_ERAY_SFS_OCLR_OFF (17u)

/** \brief  Length for Ifx_ERAY_SFS_Bits.RCLR */
#define IFX_ERAY_SFS_RCLR_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SFS_Bits.RCLR */
#define IFX_ERAY_SFS_RCLR_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SFS_Bits.RCLR */
#define IFX_ERAY_SFS_RCLR_OFF (19u)

/** \brief  Length for Ifx_ERAY_SFS_Bits.VSAE */
#define IFX_ERAY_SFS_VSAE_LEN (4u)

/** \brief  Mask for Ifx_ERAY_SFS_Bits.VSAE */
#define IFX_ERAY_SFS_VSAE_MSK (0xfu)

/** \brief  Offset for Ifx_ERAY_SFS_Bits.VSAE */
#define IFX_ERAY_SFS_VSAE_OFF (0u)

/** \brief  Length for Ifx_ERAY_SFS_Bits.VSAO */
#define IFX_ERAY_SFS_VSAO_LEN (4u)

/** \brief  Mask for Ifx_ERAY_SFS_Bits.VSAO */
#define IFX_ERAY_SFS_VSAO_MSK (0xfu)

/** \brief  Offset for Ifx_ERAY_SFS_Bits.VSAO */
#define IFX_ERAY_SFS_VSAO_OFF (4u)

/** \brief  Length for Ifx_ERAY_SFS_Bits.VSBE */
#define IFX_ERAY_SFS_VSBE_LEN (4u)

/** \brief  Mask for Ifx_ERAY_SFS_Bits.VSBE */
#define IFX_ERAY_SFS_VSBE_MSK (0xfu)

/** \brief  Offset for Ifx_ERAY_SFS_Bits.VSBE */
#define IFX_ERAY_SFS_VSBE_OFF (8u)

/** \brief  Length for Ifx_ERAY_SFS_Bits.VSBO */
#define IFX_ERAY_SFS_VSBO_LEN (4u)

/** \brief  Mask for Ifx_ERAY_SFS_Bits.VSBO */
#define IFX_ERAY_SFS_VSBO_MSK (0xfu)

/** \brief  Offset for Ifx_ERAY_SFS_Bits.VSBO */
#define IFX_ERAY_SFS_VSBO_OFF (12u)

/** \brief  Length for Ifx_ERAY_SIER_Bits.CASE */
#define IFX_ERAY_SIER_CASE_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SIER_Bits.CASE */
#define IFX_ERAY_SIER_CASE_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SIER_Bits.CASE */
#define IFX_ERAY_SIER_CASE_OFF (1u)

/** \brief  Length for Ifx_ERAY_SIER_Bits.CYCSE */
#define IFX_ERAY_SIER_CYCSE_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SIER_Bits.CYCSE */
#define IFX_ERAY_SIER_CYCSE_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SIER_Bits.CYCSE */
#define IFX_ERAY_SIER_CYCSE_OFF (2u)

/** \brief  Length for Ifx_ERAY_SIER_Bits.MBSIE */
#define IFX_ERAY_SIER_MBSIE_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SIER_Bits.MBSIE */
#define IFX_ERAY_SIER_MBSIE_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SIER_Bits.MBSIE */
#define IFX_ERAY_SIER_MBSIE_OFF (14u)

/** \brief  Length for Ifx_ERAY_SIER_Bits.MTSAE */
#define IFX_ERAY_SIER_MTSAE_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SIER_Bits.MTSAE */
#define IFX_ERAY_SIER_MTSAE_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SIER_Bits.MTSAE */
#define IFX_ERAY_SIER_MTSAE_OFF (17u)

/** \brief  Length for Ifx_ERAY_SIER_Bits.MTSBE */
#define IFX_ERAY_SIER_MTSBE_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SIER_Bits.MTSBE */
#define IFX_ERAY_SIER_MTSBE_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SIER_Bits.MTSBE */
#define IFX_ERAY_SIER_MTSBE_OFF (25u)

/** \brief  Length for Ifx_ERAY_SIER_Bits.NMVCE */
#define IFX_ERAY_SIER_NMVCE_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SIER_Bits.NMVCE */
#define IFX_ERAY_SIER_NMVCE_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SIER_Bits.NMVCE */
#define IFX_ERAY_SIER_NMVCE_OFF (7u)

/** \brief  Length for Ifx_ERAY_SIER_Bits.RFCLE */
#define IFX_ERAY_SIER_RFCLE_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SIER_Bits.RFCLE */
#define IFX_ERAY_SIER_RFCLE_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SIER_Bits.RFCLE */
#define IFX_ERAY_SIER_RFCLE_OFF (6u)

/** \brief  Length for Ifx_ERAY_SIER_Bits.RFNEE */
#define IFX_ERAY_SIER_RFNEE_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SIER_Bits.RFNEE */
#define IFX_ERAY_SIER_RFNEE_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SIER_Bits.RFNEE */
#define IFX_ERAY_SIER_RFNEE_OFF (5u)

/** \brief  Length for Ifx_ERAY_SIER_Bits.RXIE */
#define IFX_ERAY_SIER_RXIE_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SIER_Bits.RXIE */
#define IFX_ERAY_SIER_RXIE_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SIER_Bits.RXIE */
#define IFX_ERAY_SIER_RXIE_OFF (4u)

/** \brief  Length for Ifx_ERAY_SIER_Bits.SDSE */
#define IFX_ERAY_SIER_SDSE_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SIER_Bits.SDSE */
#define IFX_ERAY_SIER_SDSE_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SIER_Bits.SDSE */
#define IFX_ERAY_SIER_SDSE_OFF (15u)

/** \brief  Length for Ifx_ERAY_SIER_Bits.SUCSE */
#define IFX_ERAY_SIER_SUCSE_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SIER_Bits.SUCSE */
#define IFX_ERAY_SIER_SUCSE_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SIER_Bits.SUCSE */
#define IFX_ERAY_SIER_SUCSE_OFF (13u)

/** \brief  Length for Ifx_ERAY_SIER_Bits.SWEE */
#define IFX_ERAY_SIER_SWEE_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SIER_Bits.SWEE */
#define IFX_ERAY_SIER_SWEE_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SIER_Bits.SWEE */
#define IFX_ERAY_SIER_SWEE_OFF (12u)

/** \brief  Length for Ifx_ERAY_SIER_Bits.TI0E */
#define IFX_ERAY_SIER_TI0E_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SIER_Bits.TI0E */
#define IFX_ERAY_SIER_TI0E_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SIER_Bits.TI0E */
#define IFX_ERAY_SIER_TI0E_OFF (8u)

/** \brief  Length for Ifx_ERAY_SIER_Bits.TI1E */
#define IFX_ERAY_SIER_TI1E_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SIER_Bits.TI1E */
#define IFX_ERAY_SIER_TI1E_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SIER_Bits.TI1E */
#define IFX_ERAY_SIER_TI1E_OFF (9u)

/** \brief  Length for Ifx_ERAY_SIER_Bits.TIBCE */
#define IFX_ERAY_SIER_TIBCE_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SIER_Bits.TIBCE */
#define IFX_ERAY_SIER_TIBCE_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SIER_Bits.TIBCE */
#define IFX_ERAY_SIER_TIBCE_OFF (10u)

/** \brief  Length for Ifx_ERAY_SIER_Bits.TOBCE */
#define IFX_ERAY_SIER_TOBCE_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SIER_Bits.TOBCE */
#define IFX_ERAY_SIER_TOBCE_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SIER_Bits.TOBCE */
#define IFX_ERAY_SIER_TOBCE_OFF (11u)

/** \brief  Length for Ifx_ERAY_SIER_Bits.TXIE */
#define IFX_ERAY_SIER_TXIE_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SIER_Bits.TXIE */
#define IFX_ERAY_SIER_TXIE_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SIER_Bits.TXIE */
#define IFX_ERAY_SIER_TXIE_OFF (3u)

/** \brief  Length for Ifx_ERAY_SIER_Bits.WSTE */
#define IFX_ERAY_SIER_WSTE_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SIER_Bits.WSTE */
#define IFX_ERAY_SIER_WSTE_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SIER_Bits.WSTE */
#define IFX_ERAY_SIER_WSTE_OFF (0u)

/** \brief  Length for Ifx_ERAY_SIER_Bits.WUPAE */
#define IFX_ERAY_SIER_WUPAE_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SIER_Bits.WUPAE */
#define IFX_ERAY_SIER_WUPAE_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SIER_Bits.WUPAE */
#define IFX_ERAY_SIER_WUPAE_OFF (16u)

/** \brief  Length for Ifx_ERAY_SIER_Bits.WUPBE */
#define IFX_ERAY_SIER_WUPBE_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SIER_Bits.WUPBE */
#define IFX_ERAY_SIER_WUPBE_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SIER_Bits.WUPBE */
#define IFX_ERAY_SIER_WUPBE_OFF (24u)

/** \brief  Length for Ifx_ERAY_SIES_Bits.CASE */
#define IFX_ERAY_SIES_CASE_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SIES_Bits.CASE */
#define IFX_ERAY_SIES_CASE_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SIES_Bits.CASE */
#define IFX_ERAY_SIES_CASE_OFF (1u)

/** \brief  Length for Ifx_ERAY_SIES_Bits.CYCSE */
#define IFX_ERAY_SIES_CYCSE_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SIES_Bits.CYCSE */
#define IFX_ERAY_SIES_CYCSE_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SIES_Bits.CYCSE */
#define IFX_ERAY_SIES_CYCSE_OFF (2u)

/** \brief  Length for Ifx_ERAY_SIES_Bits.MBSIE */
#define IFX_ERAY_SIES_MBSIE_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SIES_Bits.MBSIE */
#define IFX_ERAY_SIES_MBSIE_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SIES_Bits.MBSIE */
#define IFX_ERAY_SIES_MBSIE_OFF (14u)

/** \brief  Length for Ifx_ERAY_SIES_Bits.MTSAE */
#define IFX_ERAY_SIES_MTSAE_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SIES_Bits.MTSAE */
#define IFX_ERAY_SIES_MTSAE_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SIES_Bits.MTSAE */
#define IFX_ERAY_SIES_MTSAE_OFF (17u)

/** \brief  Length for Ifx_ERAY_SIES_Bits.MTSBE */
#define IFX_ERAY_SIES_MTSBE_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SIES_Bits.MTSBE */
#define IFX_ERAY_SIES_MTSBE_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SIES_Bits.MTSBE */
#define IFX_ERAY_SIES_MTSBE_OFF (25u)

/** \brief  Length for Ifx_ERAY_SIES_Bits.NMVCE */
#define IFX_ERAY_SIES_NMVCE_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SIES_Bits.NMVCE */
#define IFX_ERAY_SIES_NMVCE_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SIES_Bits.NMVCE */
#define IFX_ERAY_SIES_NMVCE_OFF (7u)

/** \brief  Length for Ifx_ERAY_SIES_Bits.RFCLE */
#define IFX_ERAY_SIES_RFCLE_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SIES_Bits.RFCLE */
#define IFX_ERAY_SIES_RFCLE_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SIES_Bits.RFCLE */
#define IFX_ERAY_SIES_RFCLE_OFF (6u)

/** \brief  Length for Ifx_ERAY_SIES_Bits.RFNEE */
#define IFX_ERAY_SIES_RFNEE_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SIES_Bits.RFNEE */
#define IFX_ERAY_SIES_RFNEE_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SIES_Bits.RFNEE */
#define IFX_ERAY_SIES_RFNEE_OFF (5u)

/** \brief  Length for Ifx_ERAY_SIES_Bits.RXIE */
#define IFX_ERAY_SIES_RXIE_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SIES_Bits.RXIE */
#define IFX_ERAY_SIES_RXIE_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SIES_Bits.RXIE */
#define IFX_ERAY_SIES_RXIE_OFF (4u)

/** \brief  Length for Ifx_ERAY_SIES_Bits.SDSE */
#define IFX_ERAY_SIES_SDSE_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SIES_Bits.SDSE */
#define IFX_ERAY_SIES_SDSE_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SIES_Bits.SDSE */
#define IFX_ERAY_SIES_SDSE_OFF (15u)

/** \brief  Length for Ifx_ERAY_SIES_Bits.SUCSE */
#define IFX_ERAY_SIES_SUCSE_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SIES_Bits.SUCSE */
#define IFX_ERAY_SIES_SUCSE_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SIES_Bits.SUCSE */
#define IFX_ERAY_SIES_SUCSE_OFF (13u)

/** \brief  Length for Ifx_ERAY_SIES_Bits.SWEE */
#define IFX_ERAY_SIES_SWEE_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SIES_Bits.SWEE */
#define IFX_ERAY_SIES_SWEE_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SIES_Bits.SWEE */
#define IFX_ERAY_SIES_SWEE_OFF (12u)

/** \brief  Length for Ifx_ERAY_SIES_Bits.TI0E */
#define IFX_ERAY_SIES_TI0E_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SIES_Bits.TI0E */
#define IFX_ERAY_SIES_TI0E_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SIES_Bits.TI0E */
#define IFX_ERAY_SIES_TI0E_OFF (8u)

/** \brief  Length for Ifx_ERAY_SIES_Bits.TI1E */
#define IFX_ERAY_SIES_TI1E_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SIES_Bits.TI1E */
#define IFX_ERAY_SIES_TI1E_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SIES_Bits.TI1E */
#define IFX_ERAY_SIES_TI1E_OFF (9u)

/** \brief  Length for Ifx_ERAY_SIES_Bits.TIBCE */
#define IFX_ERAY_SIES_TIBCE_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SIES_Bits.TIBCE */
#define IFX_ERAY_SIES_TIBCE_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SIES_Bits.TIBCE */
#define IFX_ERAY_SIES_TIBCE_OFF (10u)

/** \brief  Length for Ifx_ERAY_SIES_Bits.TOBCE */
#define IFX_ERAY_SIES_TOBCE_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SIES_Bits.TOBCE */
#define IFX_ERAY_SIES_TOBCE_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SIES_Bits.TOBCE */
#define IFX_ERAY_SIES_TOBCE_OFF (11u)

/** \brief  Length for Ifx_ERAY_SIES_Bits.TXIE */
#define IFX_ERAY_SIES_TXIE_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SIES_Bits.TXIE */
#define IFX_ERAY_SIES_TXIE_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SIES_Bits.TXIE */
#define IFX_ERAY_SIES_TXIE_OFF (3u)

/** \brief  Length for Ifx_ERAY_SIES_Bits.WSTE */
#define IFX_ERAY_SIES_WSTE_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SIES_Bits.WSTE */
#define IFX_ERAY_SIES_WSTE_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SIES_Bits.WSTE */
#define IFX_ERAY_SIES_WSTE_OFF (0u)

/** \brief  Length for Ifx_ERAY_SIES_Bits.WUPAE */
#define IFX_ERAY_SIES_WUPAE_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SIES_Bits.WUPAE */
#define IFX_ERAY_SIES_WUPAE_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SIES_Bits.WUPAE */
#define IFX_ERAY_SIES_WUPAE_OFF (16u)

/** \brief  Length for Ifx_ERAY_SIES_Bits.WUPBE */
#define IFX_ERAY_SIES_WUPBE_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SIES_Bits.WUPBE */
#define IFX_ERAY_SIES_WUPBE_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SIES_Bits.WUPBE */
#define IFX_ERAY_SIES_WUPBE_OFF (24u)

/** \brief  Length for Ifx_ERAY_SILS_Bits.CASL */
#define IFX_ERAY_SILS_CASL_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SILS_Bits.CASL */
#define IFX_ERAY_SILS_CASL_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SILS_Bits.CASL */
#define IFX_ERAY_SILS_CASL_OFF (1u)

/** \brief  Length for Ifx_ERAY_SILS_Bits.CYCSL */
#define IFX_ERAY_SILS_CYCSL_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SILS_Bits.CYCSL */
#define IFX_ERAY_SILS_CYCSL_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SILS_Bits.CYCSL */
#define IFX_ERAY_SILS_CYCSL_OFF (2u)

/** \brief  Length for Ifx_ERAY_SILS_Bits.MBSIL */
#define IFX_ERAY_SILS_MBSIL_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SILS_Bits.MBSIL */
#define IFX_ERAY_SILS_MBSIL_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SILS_Bits.MBSIL */
#define IFX_ERAY_SILS_MBSIL_OFF (14u)

/** \brief  Length for Ifx_ERAY_SILS_Bits.MTSAL */
#define IFX_ERAY_SILS_MTSAL_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SILS_Bits.MTSAL */
#define IFX_ERAY_SILS_MTSAL_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SILS_Bits.MTSAL */
#define IFX_ERAY_SILS_MTSAL_OFF (17u)

/** \brief  Length for Ifx_ERAY_SILS_Bits.MTSBL */
#define IFX_ERAY_SILS_MTSBL_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SILS_Bits.MTSBL */
#define IFX_ERAY_SILS_MTSBL_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SILS_Bits.MTSBL */
#define IFX_ERAY_SILS_MTSBL_OFF (25u)

/** \brief  Length for Ifx_ERAY_SILS_Bits.NMVCL */
#define IFX_ERAY_SILS_NMVCL_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SILS_Bits.NMVCL */
#define IFX_ERAY_SILS_NMVCL_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SILS_Bits.NMVCL */
#define IFX_ERAY_SILS_NMVCL_OFF (7u)

/** \brief  Length for Ifx_ERAY_SILS_Bits.RFCLL */
#define IFX_ERAY_SILS_RFCLL_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SILS_Bits.RFCLL */
#define IFX_ERAY_SILS_RFCLL_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SILS_Bits.RFCLL */
#define IFX_ERAY_SILS_RFCLL_OFF (6u)

/** \brief  Length for Ifx_ERAY_SILS_Bits.RFNEL */
#define IFX_ERAY_SILS_RFNEL_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SILS_Bits.RFNEL */
#define IFX_ERAY_SILS_RFNEL_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SILS_Bits.RFNEL */
#define IFX_ERAY_SILS_RFNEL_OFF (5u)

/** \brief  Length for Ifx_ERAY_SILS_Bits.RXIL */
#define IFX_ERAY_SILS_RXIL_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SILS_Bits.RXIL */
#define IFX_ERAY_SILS_RXIL_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SILS_Bits.RXIL */
#define IFX_ERAY_SILS_RXIL_OFF (4u)

/** \brief  Length for Ifx_ERAY_SILS_Bits.SDSL */
#define IFX_ERAY_SILS_SDSL_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SILS_Bits.SDSL */
#define IFX_ERAY_SILS_SDSL_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SILS_Bits.SDSL */
#define IFX_ERAY_SILS_SDSL_OFF (15u)

/** \brief  Length for Ifx_ERAY_SILS_Bits.SUCSL */
#define IFX_ERAY_SILS_SUCSL_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SILS_Bits.SUCSL */
#define IFX_ERAY_SILS_SUCSL_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SILS_Bits.SUCSL */
#define IFX_ERAY_SILS_SUCSL_OFF (13u)

/** \brief  Length for Ifx_ERAY_SILS_Bits.SWEL */
#define IFX_ERAY_SILS_SWEL_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SILS_Bits.SWEL */
#define IFX_ERAY_SILS_SWEL_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SILS_Bits.SWEL */
#define IFX_ERAY_SILS_SWEL_OFF (12u)

/** \brief  Length for Ifx_ERAY_SILS_Bits.TI0L */
#define IFX_ERAY_SILS_TI0L_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SILS_Bits.TI0L */
#define IFX_ERAY_SILS_TI0L_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SILS_Bits.TI0L */
#define IFX_ERAY_SILS_TI0L_OFF (8u)

/** \brief  Length for Ifx_ERAY_SILS_Bits.TI1L */
#define IFX_ERAY_SILS_TI1L_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SILS_Bits.TI1L */
#define IFX_ERAY_SILS_TI1L_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SILS_Bits.TI1L */
#define IFX_ERAY_SILS_TI1L_OFF (9u)

/** \brief  Length for Ifx_ERAY_SILS_Bits.TIBCL */
#define IFX_ERAY_SILS_TIBCL_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SILS_Bits.TIBCL */
#define IFX_ERAY_SILS_TIBCL_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SILS_Bits.TIBCL */
#define IFX_ERAY_SILS_TIBCL_OFF (10u)

/** \brief  Length for Ifx_ERAY_SILS_Bits.TOBCL */
#define IFX_ERAY_SILS_TOBCL_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SILS_Bits.TOBCL */
#define IFX_ERAY_SILS_TOBCL_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SILS_Bits.TOBCL */
#define IFX_ERAY_SILS_TOBCL_OFF (11u)

/** \brief  Length for Ifx_ERAY_SILS_Bits.TXIL */
#define IFX_ERAY_SILS_TXIL_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SILS_Bits.TXIL */
#define IFX_ERAY_SILS_TXIL_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SILS_Bits.TXIL */
#define IFX_ERAY_SILS_TXIL_OFF (3u)

/** \brief  Length for Ifx_ERAY_SILS_Bits.WSTL */
#define IFX_ERAY_SILS_WSTL_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SILS_Bits.WSTL */
#define IFX_ERAY_SILS_WSTL_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SILS_Bits.WSTL */
#define IFX_ERAY_SILS_WSTL_OFF (0u)

/** \brief  Length for Ifx_ERAY_SILS_Bits.WUPAL */
#define IFX_ERAY_SILS_WUPAL_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SILS_Bits.WUPAL */
#define IFX_ERAY_SILS_WUPAL_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SILS_Bits.WUPAL */
#define IFX_ERAY_SILS_WUPAL_OFF (16u)

/** \brief  Length for Ifx_ERAY_SILS_Bits.WUPBL */
#define IFX_ERAY_SILS_WUPBL_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SILS_Bits.WUPBL */
#define IFX_ERAY_SILS_WUPBL_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SILS_Bits.WUPBL */
#define IFX_ERAY_SILS_WUPBL_OFF (24u)

/** \brief  Length for Ifx_ERAY_SIR_Bits.CAS */
#define IFX_ERAY_SIR_CAS_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SIR_Bits.CAS */
#define IFX_ERAY_SIR_CAS_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SIR_Bits.CAS */
#define IFX_ERAY_SIR_CAS_OFF (1u)

/** \brief  Length for Ifx_ERAY_SIR_Bits.CYCS */
#define IFX_ERAY_SIR_CYCS_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SIR_Bits.CYCS */
#define IFX_ERAY_SIR_CYCS_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SIR_Bits.CYCS */
#define IFX_ERAY_SIR_CYCS_OFF (2u)

/** \brief  Length for Ifx_ERAY_SIR_Bits.MBSI */
#define IFX_ERAY_SIR_MBSI_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SIR_Bits.MBSI */
#define IFX_ERAY_SIR_MBSI_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SIR_Bits.MBSI */
#define IFX_ERAY_SIR_MBSI_OFF (14u)

/** \brief  Length for Ifx_ERAY_SIR_Bits.MTSA */
#define IFX_ERAY_SIR_MTSA_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SIR_Bits.MTSA */
#define IFX_ERAY_SIR_MTSA_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SIR_Bits.MTSA */
#define IFX_ERAY_SIR_MTSA_OFF (17u)

/** \brief  Length for Ifx_ERAY_SIR_Bits.MTSB */
#define IFX_ERAY_SIR_MTSB_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SIR_Bits.MTSB */
#define IFX_ERAY_SIR_MTSB_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SIR_Bits.MTSB */
#define IFX_ERAY_SIR_MTSB_OFF (25u)

/** \brief  Length for Ifx_ERAY_SIR_Bits.NMVC */
#define IFX_ERAY_SIR_NMVC_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SIR_Bits.NMVC */
#define IFX_ERAY_SIR_NMVC_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SIR_Bits.NMVC */
#define IFX_ERAY_SIR_NMVC_OFF (7u)

/** \brief  Length for Ifx_ERAY_SIR_Bits.RFCL */
#define IFX_ERAY_SIR_RFCL_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SIR_Bits.RFCL */
#define IFX_ERAY_SIR_RFCL_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SIR_Bits.RFCL */
#define IFX_ERAY_SIR_RFCL_OFF (6u)

/** \brief  Length for Ifx_ERAY_SIR_Bits.RFNE */
#define IFX_ERAY_SIR_RFNE_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SIR_Bits.RFNE */
#define IFX_ERAY_SIR_RFNE_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SIR_Bits.RFNE */
#define IFX_ERAY_SIR_RFNE_OFF (5u)

/** \brief  Length for Ifx_ERAY_SIR_Bits.RXI */
#define IFX_ERAY_SIR_RXI_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SIR_Bits.RXI */
#define IFX_ERAY_SIR_RXI_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SIR_Bits.RXI */
#define IFX_ERAY_SIR_RXI_OFF (4u)

/** \brief  Length for Ifx_ERAY_SIR_Bits.SDS */
#define IFX_ERAY_SIR_SDS_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SIR_Bits.SDS */
#define IFX_ERAY_SIR_SDS_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SIR_Bits.SDS */
#define IFX_ERAY_SIR_SDS_OFF (15u)

/** \brief  Length for Ifx_ERAY_SIR_Bits.SUCS */
#define IFX_ERAY_SIR_SUCS_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SIR_Bits.SUCS */
#define IFX_ERAY_SIR_SUCS_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SIR_Bits.SUCS */
#define IFX_ERAY_SIR_SUCS_OFF (13u)

/** \brief  Length for Ifx_ERAY_SIR_Bits.SWE */
#define IFX_ERAY_SIR_SWE_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SIR_Bits.SWE */
#define IFX_ERAY_SIR_SWE_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SIR_Bits.SWE */
#define IFX_ERAY_SIR_SWE_OFF (12u)

/** \brief  Length for Ifx_ERAY_SIR_Bits.TI0 */
#define IFX_ERAY_SIR_TI0_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SIR_Bits.TI0 */
#define IFX_ERAY_SIR_TI0_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SIR_Bits.TI0 */
#define IFX_ERAY_SIR_TI0_OFF (8u)

/** \brief  Length for Ifx_ERAY_SIR_Bits.TI1 */
#define IFX_ERAY_SIR_TI1_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SIR_Bits.TI1 */
#define IFX_ERAY_SIR_TI1_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SIR_Bits.TI1 */
#define IFX_ERAY_SIR_TI1_OFF (9u)

/** \brief  Length for Ifx_ERAY_SIR_Bits.TIBC */
#define IFX_ERAY_SIR_TIBC_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SIR_Bits.TIBC */
#define IFX_ERAY_SIR_TIBC_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SIR_Bits.TIBC */
#define IFX_ERAY_SIR_TIBC_OFF (10u)

/** \brief  Length for Ifx_ERAY_SIR_Bits.TOBC */
#define IFX_ERAY_SIR_TOBC_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SIR_Bits.TOBC */
#define IFX_ERAY_SIR_TOBC_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SIR_Bits.TOBC */
#define IFX_ERAY_SIR_TOBC_OFF (11u)

/** \brief  Length for Ifx_ERAY_SIR_Bits.TXI */
#define IFX_ERAY_SIR_TXI_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SIR_Bits.TXI */
#define IFX_ERAY_SIR_TXI_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SIR_Bits.TXI */
#define IFX_ERAY_SIR_TXI_OFF (3u)

/** \brief  Length for Ifx_ERAY_SIR_Bits.WST */
#define IFX_ERAY_SIR_WST_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SIR_Bits.WST */
#define IFX_ERAY_SIR_WST_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SIR_Bits.WST */
#define IFX_ERAY_SIR_WST_OFF (0u)

/** \brief  Length for Ifx_ERAY_SIR_Bits.WUPA */
#define IFX_ERAY_SIR_WUPA_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SIR_Bits.WUPA */
#define IFX_ERAY_SIR_WUPA_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SIR_Bits.WUPA */
#define IFX_ERAY_SIR_WUPA_OFF (16u)

/** \brief  Length for Ifx_ERAY_SIR_Bits.WUPB */
#define IFX_ERAY_SIR_WUPB_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SIR_Bits.WUPB */
#define IFX_ERAY_SIR_WUPB_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SIR_Bits.WUPB */
#define IFX_ERAY_SIR_WUPB_OFF (24u)

/** \brief  Length for Ifx_ERAY_STPW1_Bits.EDGE */
#define IFX_ERAY_STPW1_EDGE_LEN (1u)

/** \brief  Mask for Ifx_ERAY_STPW1_Bits.EDGE */
#define IFX_ERAY_STPW1_EDGE_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_STPW1_Bits.EDGE */
#define IFX_ERAY_STPW1_EDGE_OFF (2u)

/** \brief  Length for Ifx_ERAY_STPW1_Bits.EETP */
#define IFX_ERAY_STPW1_EETP_LEN (1u)

/** \brief  Mask for Ifx_ERAY_STPW1_Bits.EETP */
#define IFX_ERAY_STPW1_EETP_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_STPW1_Bits.EETP */
#define IFX_ERAY_STPW1_EETP_OFF (4u)

/** \brief  Length for Ifx_ERAY_STPW1_Bits.EINT0 */
#define IFX_ERAY_STPW1_EINT0_LEN (1u)

/** \brief  Mask for Ifx_ERAY_STPW1_Bits.EINT0 */
#define IFX_ERAY_STPW1_EINT0_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_STPW1_Bits.EINT0 */
#define IFX_ERAY_STPW1_EINT0_OFF (5u)

/** \brief  Length for Ifx_ERAY_STPW1_Bits.EINT1 */
#define IFX_ERAY_STPW1_EINT1_LEN (1u)

/** \brief  Mask for Ifx_ERAY_STPW1_Bits.EINT1 */
#define IFX_ERAY_STPW1_EINT1_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_STPW1_Bits.EINT1 */
#define IFX_ERAY_STPW1_EINT1_OFF (6u)

/** \brief  Length for Ifx_ERAY_STPW1_Bits.ESWT */
#define IFX_ERAY_STPW1_ESWT_LEN (1u)

/** \brief  Mask for Ifx_ERAY_STPW1_Bits.ESWT */
#define IFX_ERAY_STPW1_ESWT_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_STPW1_Bits.ESWT */
#define IFX_ERAY_STPW1_ESWT_OFF (0u)

/** \brief  Length for Ifx_ERAY_STPW1_Bits.SCCV */
#define IFX_ERAY_STPW1_SCCV_LEN (6u)

/** \brief  Mask for Ifx_ERAY_STPW1_Bits.SCCV */
#define IFX_ERAY_STPW1_SCCV_MSK (0x3fu)

/** \brief  Offset for Ifx_ERAY_STPW1_Bits.SCCV */
#define IFX_ERAY_STPW1_SCCV_OFF (8u)

/** \brief  Length for Ifx_ERAY_STPW1_Bits.SMTV */
#define IFX_ERAY_STPW1_SMTV_LEN (14u)

/** \brief  Mask for Ifx_ERAY_STPW1_Bits.SMTV */
#define IFX_ERAY_STPW1_SMTV_MSK (0x3fffu)

/** \brief  Offset for Ifx_ERAY_STPW1_Bits.SMTV */
#define IFX_ERAY_STPW1_SMTV_OFF (16u)

/** \brief  Length for Ifx_ERAY_STPW1_Bits.SSWT */
#define IFX_ERAY_STPW1_SSWT_LEN (1u)

/** \brief  Mask for Ifx_ERAY_STPW1_Bits.SSWT */
#define IFX_ERAY_STPW1_SSWT_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_STPW1_Bits.SSWT */
#define IFX_ERAY_STPW1_SSWT_OFF (3u)

/** \brief  Length for Ifx_ERAY_STPW1_Bits.SWMS */
#define IFX_ERAY_STPW1_SWMS_LEN (1u)

/** \brief  Mask for Ifx_ERAY_STPW1_Bits.SWMS */
#define IFX_ERAY_STPW1_SWMS_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_STPW1_Bits.SWMS */
#define IFX_ERAY_STPW1_SWMS_OFF (1u)

/** \brief  Length for Ifx_ERAY_STPW2_Bits.SSCVA */
#define IFX_ERAY_STPW2_SSCVA_LEN (11u)

/** \brief  Mask for Ifx_ERAY_STPW2_Bits.SSCVA */
#define IFX_ERAY_STPW2_SSCVA_MSK (0x7ffu)

/** \brief  Offset for Ifx_ERAY_STPW2_Bits.SSCVA */
#define IFX_ERAY_STPW2_SSCVA_OFF (0u)

/** \brief  Length for Ifx_ERAY_STPW2_Bits.SSCVB */
#define IFX_ERAY_STPW2_SSCVB_LEN (11u)

/** \brief  Mask for Ifx_ERAY_STPW2_Bits.SSCVB */
#define IFX_ERAY_STPW2_SSCVB_MSK (0x7ffu)

/** \brief  Offset for Ifx_ERAY_STPW2_Bits.SSCVB */
#define IFX_ERAY_STPW2_SSCVB_OFF (16u)

/** \brief  Length for Ifx_ERAY_SUCC1_Bits.CCHA */
#define IFX_ERAY_SUCC1_CCHA_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SUCC1_Bits.CCHA */
#define IFX_ERAY_SUCC1_CCHA_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SUCC1_Bits.CCHA */
#define IFX_ERAY_SUCC1_CCHA_OFF (26u)

/** \brief  Length for Ifx_ERAY_SUCC1_Bits.CCHB */
#define IFX_ERAY_SUCC1_CCHB_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SUCC1_Bits.CCHB */
#define IFX_ERAY_SUCC1_CCHB_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SUCC1_Bits.CCHB */
#define IFX_ERAY_SUCC1_CCHB_OFF (27u)

/** \brief  Length for Ifx_ERAY_SUCC1_Bits.CMD */
#define IFX_ERAY_SUCC1_CMD_LEN (4u)

/** \brief  Mask for Ifx_ERAY_SUCC1_Bits.CMD */
#define IFX_ERAY_SUCC1_CMD_MSK (0xfu)

/** \brief  Offset for Ifx_ERAY_SUCC1_Bits.CMD */
#define IFX_ERAY_SUCC1_CMD_OFF (0u)

/** \brief  Length for Ifx_ERAY_SUCC1_Bits.CSA */
#define IFX_ERAY_SUCC1_CSA_LEN (5u)

/** \brief  Mask for Ifx_ERAY_SUCC1_Bits.CSA */
#define IFX_ERAY_SUCC1_CSA_MSK (0x1fu)

/** \brief  Offset for Ifx_ERAY_SUCC1_Bits.CSA */
#define IFX_ERAY_SUCC1_CSA_OFF (11u)

/** \brief  Length for Ifx_ERAY_SUCC1_Bits.HCSE */
#define IFX_ERAY_SUCC1_HCSE_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SUCC1_Bits.HCSE */
#define IFX_ERAY_SUCC1_HCSE_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SUCC1_Bits.HCSE */
#define IFX_ERAY_SUCC1_HCSE_OFF (23u)

/** \brief  Length for Ifx_ERAY_SUCC1_Bits.MTSA */
#define IFX_ERAY_SUCC1_MTSA_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SUCC1_Bits.MTSA */
#define IFX_ERAY_SUCC1_MTSA_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SUCC1_Bits.MTSA */
#define IFX_ERAY_SUCC1_MTSA_OFF (24u)

/** \brief  Length for Ifx_ERAY_SUCC1_Bits.MTSB */
#define IFX_ERAY_SUCC1_MTSB_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SUCC1_Bits.MTSB */
#define IFX_ERAY_SUCC1_MTSB_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SUCC1_Bits.MTSB */
#define IFX_ERAY_SUCC1_MTSB_OFF (25u)

/** \brief  Length for Ifx_ERAY_SUCC1_Bits.PBSY */
#define IFX_ERAY_SUCC1_PBSY_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SUCC1_Bits.PBSY */
#define IFX_ERAY_SUCC1_PBSY_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SUCC1_Bits.PBSY */
#define IFX_ERAY_SUCC1_PBSY_OFF (7u)

/** \brief  Length for Ifx_ERAY_SUCC1_Bits.PTA */
#define IFX_ERAY_SUCC1_PTA_LEN (5u)

/** \brief  Mask for Ifx_ERAY_SUCC1_Bits.PTA */
#define IFX_ERAY_SUCC1_PTA_MSK (0x1fu)

/** \brief  Offset for Ifx_ERAY_SUCC1_Bits.PTA */
#define IFX_ERAY_SUCC1_PTA_OFF (16u)

/** \brief  Length for Ifx_ERAY_SUCC1_Bits.TSM */
#define IFX_ERAY_SUCC1_TSM_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SUCC1_Bits.TSM */
#define IFX_ERAY_SUCC1_TSM_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SUCC1_Bits.TSM */
#define IFX_ERAY_SUCC1_TSM_OFF (22u)

/** \brief  Length for Ifx_ERAY_SUCC1_Bits.TXST */
#define IFX_ERAY_SUCC1_TXST_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SUCC1_Bits.TXST */
#define IFX_ERAY_SUCC1_TXST_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SUCC1_Bits.TXST */
#define IFX_ERAY_SUCC1_TXST_OFF (8u)

/** \brief  Length for Ifx_ERAY_SUCC1_Bits.TXSY */
#define IFX_ERAY_SUCC1_TXSY_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SUCC1_Bits.TXSY */
#define IFX_ERAY_SUCC1_TXSY_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SUCC1_Bits.TXSY */
#define IFX_ERAY_SUCC1_TXSY_OFF (9u)

/** \brief  Length for Ifx_ERAY_SUCC1_Bits.WUCS */
#define IFX_ERAY_SUCC1_WUCS_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SUCC1_Bits.WUCS */
#define IFX_ERAY_SUCC1_WUCS_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SUCC1_Bits.WUCS */
#define IFX_ERAY_SUCC1_WUCS_OFF (21u)

/** \brief  Length for Ifx_ERAY_SUCC2_Bits.LT */
#define IFX_ERAY_SUCC2_LT_LEN (21u)

/** \brief  Mask for Ifx_ERAY_SUCC2_Bits.LT */
#define IFX_ERAY_SUCC2_LT_MSK (0x1fffffu)

/** \brief  Offset for Ifx_ERAY_SUCC2_Bits.LT */
#define IFX_ERAY_SUCC2_LT_OFF (0u)

/** \brief  Length for Ifx_ERAY_SUCC2_Bits.LTN */
#define IFX_ERAY_SUCC2_LTN_LEN (4u)

/** \brief  Mask for Ifx_ERAY_SUCC2_Bits.LTN */
#define IFX_ERAY_SUCC2_LTN_MSK (0xfu)

/** \brief  Offset for Ifx_ERAY_SUCC2_Bits.LTN */
#define IFX_ERAY_SUCC2_LTN_OFF (24u)

/** \brief  Length for Ifx_ERAY_SUCC3_Bits.WCF */
#define IFX_ERAY_SUCC3_WCF_LEN (4u)

/** \brief  Mask for Ifx_ERAY_SUCC3_Bits.WCF */
#define IFX_ERAY_SUCC3_WCF_MSK (0xfu)

/** \brief  Offset for Ifx_ERAY_SUCC3_Bits.WCF */
#define IFX_ERAY_SUCC3_WCF_OFF (4u)

/** \brief  Length for Ifx_ERAY_SUCC3_Bits.WCP */
#define IFX_ERAY_SUCC3_WCP_LEN (4u)

/** \brief  Mask for Ifx_ERAY_SUCC3_Bits.WCP */
#define IFX_ERAY_SUCC3_WCP_MSK (0xfu)

/** \brief  Offset for Ifx_ERAY_SUCC3_Bits.WCP */
#define IFX_ERAY_SUCC3_WCP_OFF (0u)

/** \brief  Length for Ifx_ERAY_SWNIT_Bits.MTSA */
#define IFX_ERAY_SWNIT_MTSA_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SWNIT_Bits.MTSA */
#define IFX_ERAY_SWNIT_MTSA_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SWNIT_Bits.MTSA */
#define IFX_ERAY_SWNIT_MTSA_OFF (6u)

/** \brief  Length for Ifx_ERAY_SWNIT_Bits.MTSB */
#define IFX_ERAY_SWNIT_MTSB_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SWNIT_Bits.MTSB */
#define IFX_ERAY_SWNIT_MTSB_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SWNIT_Bits.MTSB */
#define IFX_ERAY_SWNIT_MTSB_OFF (7u)

/** \brief  Length for Ifx_ERAY_SWNIT_Bits.SBNA */
#define IFX_ERAY_SWNIT_SBNA_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SWNIT_Bits.SBNA */
#define IFX_ERAY_SWNIT_SBNA_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SWNIT_Bits.SBNA */
#define IFX_ERAY_SWNIT_SBNA_OFF (9u)

/** \brief  Length for Ifx_ERAY_SWNIT_Bits.SBNB */
#define IFX_ERAY_SWNIT_SBNB_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SWNIT_Bits.SBNB */
#define IFX_ERAY_SWNIT_SBNB_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SWNIT_Bits.SBNB */
#define IFX_ERAY_SWNIT_SBNB_OFF (11u)

/** \brief  Length for Ifx_ERAY_SWNIT_Bits.SBSA */
#define IFX_ERAY_SWNIT_SBSA_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SWNIT_Bits.SBSA */
#define IFX_ERAY_SWNIT_SBSA_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SWNIT_Bits.SBSA */
#define IFX_ERAY_SWNIT_SBSA_OFF (1u)

/** \brief  Length for Ifx_ERAY_SWNIT_Bits.SBSB */
#define IFX_ERAY_SWNIT_SBSB_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SWNIT_Bits.SBSB */
#define IFX_ERAY_SWNIT_SBSB_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SWNIT_Bits.SBSB */
#define IFX_ERAY_SWNIT_SBSB_OFF (4u)

/** \brief  Length for Ifx_ERAY_SWNIT_Bits.SENA */
#define IFX_ERAY_SWNIT_SENA_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SWNIT_Bits.SENA */
#define IFX_ERAY_SWNIT_SENA_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SWNIT_Bits.SENA */
#define IFX_ERAY_SWNIT_SENA_OFF (8u)

/** \brief  Length for Ifx_ERAY_SWNIT_Bits.SENB */
#define IFX_ERAY_SWNIT_SENB_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SWNIT_Bits.SENB */
#define IFX_ERAY_SWNIT_SENB_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SWNIT_Bits.SENB */
#define IFX_ERAY_SWNIT_SENB_OFF (10u)

/** \brief  Length for Ifx_ERAY_SWNIT_Bits.SESA */
#define IFX_ERAY_SWNIT_SESA_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SWNIT_Bits.SESA */
#define IFX_ERAY_SWNIT_SESA_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SWNIT_Bits.SESA */
#define IFX_ERAY_SWNIT_SESA_OFF (0u)

/** \brief  Length for Ifx_ERAY_SWNIT_Bits.SESB */
#define IFX_ERAY_SWNIT_SESB_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SWNIT_Bits.SESB */
#define IFX_ERAY_SWNIT_SESB_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SWNIT_Bits.SESB */
#define IFX_ERAY_SWNIT_SESB_OFF (3u)

/** \brief  Length for Ifx_ERAY_SWNIT_Bits.TCSA */
#define IFX_ERAY_SWNIT_TCSA_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SWNIT_Bits.TCSA */
#define IFX_ERAY_SWNIT_TCSA_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SWNIT_Bits.TCSA */
#define IFX_ERAY_SWNIT_TCSA_OFF (2u)

/** \brief  Length for Ifx_ERAY_SWNIT_Bits.TCSB */
#define IFX_ERAY_SWNIT_TCSB_LEN (1u)

/** \brief  Mask for Ifx_ERAY_SWNIT_Bits.TCSB */
#define IFX_ERAY_SWNIT_TCSB_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_SWNIT_Bits.TCSB */
#define IFX_ERAY_SWNIT_TCSB_OFF (5u)

/** \brief  Length for Ifx_ERAY_T0C_Bits.T0CC */
#define IFX_ERAY_T0C_T0CC_LEN (7u)

/** \brief  Mask for Ifx_ERAY_T0C_Bits.T0CC */
#define IFX_ERAY_T0C_T0CC_MSK (0x7fu)

/** \brief  Offset for Ifx_ERAY_T0C_Bits.T0CC */
#define IFX_ERAY_T0C_T0CC_OFF (8u)

/** \brief  Length for Ifx_ERAY_T0C_Bits.T0MO */
#define IFX_ERAY_T0C_T0MO_LEN (14u)

/** \brief  Mask for Ifx_ERAY_T0C_Bits.T0MO */
#define IFX_ERAY_T0C_T0MO_MSK (0x3fffu)

/** \brief  Offset for Ifx_ERAY_T0C_Bits.T0MO */
#define IFX_ERAY_T0C_T0MO_OFF (16u)

/** \brief  Length for Ifx_ERAY_T0C_Bits.T0MS */
#define IFX_ERAY_T0C_T0MS_LEN (1u)

/** \brief  Mask for Ifx_ERAY_T0C_Bits.T0MS */
#define IFX_ERAY_T0C_T0MS_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_T0C_Bits.T0MS */
#define IFX_ERAY_T0C_T0MS_OFF (1u)

/** \brief  Length for Ifx_ERAY_T0C_Bits.T0RC */
#define IFX_ERAY_T0C_T0RC_LEN (1u)

/** \brief  Mask for Ifx_ERAY_T0C_Bits.T0RC */
#define IFX_ERAY_T0C_T0RC_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_T0C_Bits.T0RC */
#define IFX_ERAY_T0C_T0RC_OFF (0u)

/** \brief  Length for Ifx_ERAY_T1C_Bits.T1MC */
#define IFX_ERAY_T1C_T1MC_LEN (14u)

/** \brief  Mask for Ifx_ERAY_T1C_Bits.T1MC */
#define IFX_ERAY_T1C_T1MC_MSK (0x3fffu)

/** \brief  Offset for Ifx_ERAY_T1C_Bits.T1MC */
#define IFX_ERAY_T1C_T1MC_OFF (16u)

/** \brief  Length for Ifx_ERAY_T1C_Bits.T1MS */
#define IFX_ERAY_T1C_T1MS_LEN (1u)

/** \brief  Mask for Ifx_ERAY_T1C_Bits.T1MS */
#define IFX_ERAY_T1C_T1MS_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_T1C_Bits.T1MS */
#define IFX_ERAY_T1C_T1MS_OFF (1u)

/** \brief  Length for Ifx_ERAY_T1C_Bits.T1RC */
#define IFX_ERAY_T1C_T1RC_LEN (1u)

/** \brief  Mask for Ifx_ERAY_T1C_Bits.T1RC */
#define IFX_ERAY_T1C_T1RC_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_T1C_Bits.T1RC */
#define IFX_ERAY_T1C_T1RC_OFF (0u)

/** \brief  Length for Ifx_ERAY_TEST1_Bits.AOA */
#define IFX_ERAY_TEST1_AOA_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TEST1_Bits.AOA */
#define IFX_ERAY_TEST1_AOA_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TEST1_Bits.AOA */
#define IFX_ERAY_TEST1_AOA_OFF (8u)

/** \brief  Length for Ifx_ERAY_TEST1_Bits.AOB */
#define IFX_ERAY_TEST1_AOB_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TEST1_Bits.AOB */
#define IFX_ERAY_TEST1_AOB_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TEST1_Bits.AOB */
#define IFX_ERAY_TEST1_AOB_OFF (9u)

/** \brief  Length for Ifx_ERAY_TEST1_Bits.CERA */
#define IFX_ERAY_TEST1_CERA_LEN (4u)

/** \brief  Mask for Ifx_ERAY_TEST1_Bits.CERA */
#define IFX_ERAY_TEST1_CERA_MSK (0xfu)

/** \brief  Offset for Ifx_ERAY_TEST1_Bits.CERA */
#define IFX_ERAY_TEST1_CERA_OFF (24u)

/** \brief  Length for Ifx_ERAY_TEST1_Bits.CERB */
#define IFX_ERAY_TEST1_CERB_LEN (4u)

/** \brief  Mask for Ifx_ERAY_TEST1_Bits.CERB */
#define IFX_ERAY_TEST1_CERB_MSK (0xfu)

/** \brief  Offset for Ifx_ERAY_TEST1_Bits.CERB */
#define IFX_ERAY_TEST1_CERB_OFF (28u)

/** \brief  Length for Ifx_ERAY_TEST1_Bits.ELBE */
#define IFX_ERAY_TEST1_ELBE_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TEST1_Bits.ELBE */
#define IFX_ERAY_TEST1_ELBE_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TEST1_Bits.ELBE */
#define IFX_ERAY_TEST1_ELBE_OFF (1u)

/** \brief  Length for Ifx_ERAY_TEST1_Bits.RXA */
#define IFX_ERAY_TEST1_RXA_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TEST1_Bits.RXA */
#define IFX_ERAY_TEST1_RXA_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TEST1_Bits.RXA */
#define IFX_ERAY_TEST1_RXA_OFF (16u)

/** \brief  Length for Ifx_ERAY_TEST1_Bits.RXB */
#define IFX_ERAY_TEST1_RXB_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TEST1_Bits.RXB */
#define IFX_ERAY_TEST1_RXB_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TEST1_Bits.RXB */
#define IFX_ERAY_TEST1_RXB_OFF (17u)

/** \brief  Length for Ifx_ERAY_TEST1_Bits.TMC */
#define IFX_ERAY_TEST1_TMC_LEN (2u)

/** \brief  Mask for Ifx_ERAY_TEST1_Bits.TMC */
#define IFX_ERAY_TEST1_TMC_MSK (0x3u)

/** \brief  Offset for Ifx_ERAY_TEST1_Bits.TMC */
#define IFX_ERAY_TEST1_TMC_OFF (4u)

/** \brief  Length for Ifx_ERAY_TEST1_Bits.TXA */
#define IFX_ERAY_TEST1_TXA_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TEST1_Bits.TXA */
#define IFX_ERAY_TEST1_TXA_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TEST1_Bits.TXA */
#define IFX_ERAY_TEST1_TXA_OFF (18u)

/** \brief  Length for Ifx_ERAY_TEST1_Bits.TXB */
#define IFX_ERAY_TEST1_TXB_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TEST1_Bits.TXB */
#define IFX_ERAY_TEST1_TXB_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TEST1_Bits.TXB */
#define IFX_ERAY_TEST1_TXB_OFF (19u)

/** \brief  Length for Ifx_ERAY_TEST1_Bits.TXENA */
#define IFX_ERAY_TEST1_TXENA_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TEST1_Bits.TXENA */
#define IFX_ERAY_TEST1_TXENA_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TEST1_Bits.TXENA */
#define IFX_ERAY_TEST1_TXENA_OFF (20u)

/** \brief  Length for Ifx_ERAY_TEST1_Bits.TXENB */
#define IFX_ERAY_TEST1_TXENB_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TEST1_Bits.TXENB */
#define IFX_ERAY_TEST1_TXENB_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TEST1_Bits.TXENB */
#define IFX_ERAY_TEST1_TXENB_OFF (21u)

/** \brief  Length for Ifx_ERAY_TEST1_Bits.WRTEN */
#define IFX_ERAY_TEST1_WRTEN_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TEST1_Bits.WRTEN */
#define IFX_ERAY_TEST1_WRTEN_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TEST1_Bits.WRTEN */
#define IFX_ERAY_TEST1_WRTEN_OFF (0u)

/** \brief  Length for Ifx_ERAY_TEST2_Bits.RS */
#define IFX_ERAY_TEST2_RS_LEN (3u)

/** \brief  Mask for Ifx_ERAY_TEST2_Bits.RS */
#define IFX_ERAY_TEST2_RS_MSK (0x7u)

/** \brief  Offset for Ifx_ERAY_TEST2_Bits.RS */
#define IFX_ERAY_TEST2_RS_OFF (0u)

/** \brief  Length for Ifx_ERAY_TEST2_Bits.SSEL */
#define IFX_ERAY_TEST2_SSEL_LEN (3u)

/** \brief  Mask for Ifx_ERAY_TEST2_Bits.SSEL */
#define IFX_ERAY_TEST2_SSEL_MSK (0x7u)

/** \brief  Offset for Ifx_ERAY_TEST2_Bits.SSEL */
#define IFX_ERAY_TEST2_SSEL_OFF (4u)

/** \brief  Length for Ifx_ERAY_TEST2_Bits.WRECC */
#define IFX_ERAY_TEST2_WRECC_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TEST2_Bits.WRECC */
#define IFX_ERAY_TEST2_WRECC_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TEST2_Bits.WRECC */
#define IFX_ERAY_TEST2_WRECC_OFF (14u)

/** \brief  Length for Ifx_ERAY_TXRQ1_Bits.TXR0 */
#define IFX_ERAY_TXRQ1_TXR0_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ1_Bits.TXR0 */
#define IFX_ERAY_TXRQ1_TXR0_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ1_Bits.TXR0 */
#define IFX_ERAY_TXRQ1_TXR0_OFF (0u)

/** \brief  Length for Ifx_ERAY_TXRQ1_Bits.TXR10 */
#define IFX_ERAY_TXRQ1_TXR10_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ1_Bits.TXR10 */
#define IFX_ERAY_TXRQ1_TXR10_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ1_Bits.TXR10 */
#define IFX_ERAY_TXRQ1_TXR10_OFF (10u)

/** \brief  Length for Ifx_ERAY_TXRQ1_Bits.TXR11 */
#define IFX_ERAY_TXRQ1_TXR11_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ1_Bits.TXR11 */
#define IFX_ERAY_TXRQ1_TXR11_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ1_Bits.TXR11 */
#define IFX_ERAY_TXRQ1_TXR11_OFF (11u)

/** \brief  Length for Ifx_ERAY_TXRQ1_Bits.TXR12 */
#define IFX_ERAY_TXRQ1_TXR12_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ1_Bits.TXR12 */
#define IFX_ERAY_TXRQ1_TXR12_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ1_Bits.TXR12 */
#define IFX_ERAY_TXRQ1_TXR12_OFF (12u)

/** \brief  Length for Ifx_ERAY_TXRQ1_Bits.TXR13 */
#define IFX_ERAY_TXRQ1_TXR13_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ1_Bits.TXR13 */
#define IFX_ERAY_TXRQ1_TXR13_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ1_Bits.TXR13 */
#define IFX_ERAY_TXRQ1_TXR13_OFF (13u)

/** \brief  Length for Ifx_ERAY_TXRQ1_Bits.TXR14 */
#define IFX_ERAY_TXRQ1_TXR14_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ1_Bits.TXR14 */
#define IFX_ERAY_TXRQ1_TXR14_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ1_Bits.TXR14 */
#define IFX_ERAY_TXRQ1_TXR14_OFF (14u)

/** \brief  Length for Ifx_ERAY_TXRQ1_Bits.TXR15 */
#define IFX_ERAY_TXRQ1_TXR15_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ1_Bits.TXR15 */
#define IFX_ERAY_TXRQ1_TXR15_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ1_Bits.TXR15 */
#define IFX_ERAY_TXRQ1_TXR15_OFF (15u)

/** \brief  Length for Ifx_ERAY_TXRQ1_Bits.TXR16 */
#define IFX_ERAY_TXRQ1_TXR16_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ1_Bits.TXR16 */
#define IFX_ERAY_TXRQ1_TXR16_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ1_Bits.TXR16 */
#define IFX_ERAY_TXRQ1_TXR16_OFF (16u)

/** \brief  Length for Ifx_ERAY_TXRQ1_Bits.TXR17 */
#define IFX_ERAY_TXRQ1_TXR17_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ1_Bits.TXR17 */
#define IFX_ERAY_TXRQ1_TXR17_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ1_Bits.TXR17 */
#define IFX_ERAY_TXRQ1_TXR17_OFF (17u)

/** \brief  Length for Ifx_ERAY_TXRQ1_Bits.TXR18 */
#define IFX_ERAY_TXRQ1_TXR18_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ1_Bits.TXR18 */
#define IFX_ERAY_TXRQ1_TXR18_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ1_Bits.TXR18 */
#define IFX_ERAY_TXRQ1_TXR18_OFF (18u)

/** \brief  Length for Ifx_ERAY_TXRQ1_Bits.TXR19 */
#define IFX_ERAY_TXRQ1_TXR19_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ1_Bits.TXR19 */
#define IFX_ERAY_TXRQ1_TXR19_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ1_Bits.TXR19 */
#define IFX_ERAY_TXRQ1_TXR19_OFF (19u)

/** \brief  Length for Ifx_ERAY_TXRQ1_Bits.TXR1 */
#define IFX_ERAY_TXRQ1_TXR1_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ1_Bits.TXR1 */
#define IFX_ERAY_TXRQ1_TXR1_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ1_Bits.TXR1 */
#define IFX_ERAY_TXRQ1_TXR1_OFF (1u)

/** \brief  Length for Ifx_ERAY_TXRQ1_Bits.TXR20 */
#define IFX_ERAY_TXRQ1_TXR20_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ1_Bits.TXR20 */
#define IFX_ERAY_TXRQ1_TXR20_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ1_Bits.TXR20 */
#define IFX_ERAY_TXRQ1_TXR20_OFF (20u)

/** \brief  Length for Ifx_ERAY_TXRQ1_Bits.TXR21 */
#define IFX_ERAY_TXRQ1_TXR21_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ1_Bits.TXR21 */
#define IFX_ERAY_TXRQ1_TXR21_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ1_Bits.TXR21 */
#define IFX_ERAY_TXRQ1_TXR21_OFF (21u)

/** \brief  Length for Ifx_ERAY_TXRQ1_Bits.TXR22 */
#define IFX_ERAY_TXRQ1_TXR22_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ1_Bits.TXR22 */
#define IFX_ERAY_TXRQ1_TXR22_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ1_Bits.TXR22 */
#define IFX_ERAY_TXRQ1_TXR22_OFF (22u)

/** \brief  Length for Ifx_ERAY_TXRQ1_Bits.TXR23 */
#define IFX_ERAY_TXRQ1_TXR23_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ1_Bits.TXR23 */
#define IFX_ERAY_TXRQ1_TXR23_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ1_Bits.TXR23 */
#define IFX_ERAY_TXRQ1_TXR23_OFF (23u)

/** \brief  Length for Ifx_ERAY_TXRQ1_Bits.TXR24 */
#define IFX_ERAY_TXRQ1_TXR24_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ1_Bits.TXR24 */
#define IFX_ERAY_TXRQ1_TXR24_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ1_Bits.TXR24 */
#define IFX_ERAY_TXRQ1_TXR24_OFF (24u)

/** \brief  Length for Ifx_ERAY_TXRQ1_Bits.TXR25 */
#define IFX_ERAY_TXRQ1_TXR25_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ1_Bits.TXR25 */
#define IFX_ERAY_TXRQ1_TXR25_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ1_Bits.TXR25 */
#define IFX_ERAY_TXRQ1_TXR25_OFF (25u)

/** \brief  Length for Ifx_ERAY_TXRQ1_Bits.TXR26 */
#define IFX_ERAY_TXRQ1_TXR26_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ1_Bits.TXR26 */
#define IFX_ERAY_TXRQ1_TXR26_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ1_Bits.TXR26 */
#define IFX_ERAY_TXRQ1_TXR26_OFF (26u)

/** \brief  Length for Ifx_ERAY_TXRQ1_Bits.TXR27 */
#define IFX_ERAY_TXRQ1_TXR27_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ1_Bits.TXR27 */
#define IFX_ERAY_TXRQ1_TXR27_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ1_Bits.TXR27 */
#define IFX_ERAY_TXRQ1_TXR27_OFF (27u)

/** \brief  Length for Ifx_ERAY_TXRQ1_Bits.TXR28 */
#define IFX_ERAY_TXRQ1_TXR28_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ1_Bits.TXR28 */
#define IFX_ERAY_TXRQ1_TXR28_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ1_Bits.TXR28 */
#define IFX_ERAY_TXRQ1_TXR28_OFF (28u)

/** \brief  Length for Ifx_ERAY_TXRQ1_Bits.TXR29 */
#define IFX_ERAY_TXRQ1_TXR29_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ1_Bits.TXR29 */
#define IFX_ERAY_TXRQ1_TXR29_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ1_Bits.TXR29 */
#define IFX_ERAY_TXRQ1_TXR29_OFF (29u)

/** \brief  Length for Ifx_ERAY_TXRQ1_Bits.TXR2 */
#define IFX_ERAY_TXRQ1_TXR2_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ1_Bits.TXR2 */
#define IFX_ERAY_TXRQ1_TXR2_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ1_Bits.TXR2 */
#define IFX_ERAY_TXRQ1_TXR2_OFF (2u)

/** \brief  Length for Ifx_ERAY_TXRQ1_Bits.TXR30 */
#define IFX_ERAY_TXRQ1_TXR30_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ1_Bits.TXR30 */
#define IFX_ERAY_TXRQ1_TXR30_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ1_Bits.TXR30 */
#define IFX_ERAY_TXRQ1_TXR30_OFF (30u)

/** \brief  Length for Ifx_ERAY_TXRQ1_Bits.TXR31 */
#define IFX_ERAY_TXRQ1_TXR31_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ1_Bits.TXR31 */
#define IFX_ERAY_TXRQ1_TXR31_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ1_Bits.TXR31 */
#define IFX_ERAY_TXRQ1_TXR31_OFF (31u)

/** \brief  Length for Ifx_ERAY_TXRQ1_Bits.TXR3 */
#define IFX_ERAY_TXRQ1_TXR3_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ1_Bits.TXR3 */
#define IFX_ERAY_TXRQ1_TXR3_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ1_Bits.TXR3 */
#define IFX_ERAY_TXRQ1_TXR3_OFF (3u)

/** \brief  Length for Ifx_ERAY_TXRQ1_Bits.TXR4 */
#define IFX_ERAY_TXRQ1_TXR4_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ1_Bits.TXR4 */
#define IFX_ERAY_TXRQ1_TXR4_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ1_Bits.TXR4 */
#define IFX_ERAY_TXRQ1_TXR4_OFF (4u)

/** \brief  Length for Ifx_ERAY_TXRQ1_Bits.TXR5 */
#define IFX_ERAY_TXRQ1_TXR5_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ1_Bits.TXR5 */
#define IFX_ERAY_TXRQ1_TXR5_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ1_Bits.TXR5 */
#define IFX_ERAY_TXRQ1_TXR5_OFF (5u)

/** \brief  Length for Ifx_ERAY_TXRQ1_Bits.TXR6 */
#define IFX_ERAY_TXRQ1_TXR6_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ1_Bits.TXR6 */
#define IFX_ERAY_TXRQ1_TXR6_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ1_Bits.TXR6 */
#define IFX_ERAY_TXRQ1_TXR6_OFF (6u)

/** \brief  Length for Ifx_ERAY_TXRQ1_Bits.TXR7 */
#define IFX_ERAY_TXRQ1_TXR7_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ1_Bits.TXR7 */
#define IFX_ERAY_TXRQ1_TXR7_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ1_Bits.TXR7 */
#define IFX_ERAY_TXRQ1_TXR7_OFF (7u)

/** \brief  Length for Ifx_ERAY_TXRQ1_Bits.TXR8 */
#define IFX_ERAY_TXRQ1_TXR8_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ1_Bits.TXR8 */
#define IFX_ERAY_TXRQ1_TXR8_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ1_Bits.TXR8 */
#define IFX_ERAY_TXRQ1_TXR8_OFF (8u)

/** \brief  Length for Ifx_ERAY_TXRQ1_Bits.TXR9 */
#define IFX_ERAY_TXRQ1_TXR9_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ1_Bits.TXR9 */
#define IFX_ERAY_TXRQ1_TXR9_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ1_Bits.TXR9 */
#define IFX_ERAY_TXRQ1_TXR9_OFF (9u)

/** \brief  Length for Ifx_ERAY_TXRQ2_Bits.TXR32 */
#define IFX_ERAY_TXRQ2_TXR32_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ2_Bits.TXR32 */
#define IFX_ERAY_TXRQ2_TXR32_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ2_Bits.TXR32 */
#define IFX_ERAY_TXRQ2_TXR32_OFF (0u)

/** \brief  Length for Ifx_ERAY_TXRQ2_Bits.TXR33 */
#define IFX_ERAY_TXRQ2_TXR33_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ2_Bits.TXR33 */
#define IFX_ERAY_TXRQ2_TXR33_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ2_Bits.TXR33 */
#define IFX_ERAY_TXRQ2_TXR33_OFF (1u)

/** \brief  Length for Ifx_ERAY_TXRQ2_Bits.TXR34 */
#define IFX_ERAY_TXRQ2_TXR34_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ2_Bits.TXR34 */
#define IFX_ERAY_TXRQ2_TXR34_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ2_Bits.TXR34 */
#define IFX_ERAY_TXRQ2_TXR34_OFF (2u)

/** \brief  Length for Ifx_ERAY_TXRQ2_Bits.TXR35 */
#define IFX_ERAY_TXRQ2_TXR35_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ2_Bits.TXR35 */
#define IFX_ERAY_TXRQ2_TXR35_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ2_Bits.TXR35 */
#define IFX_ERAY_TXRQ2_TXR35_OFF (3u)

/** \brief  Length for Ifx_ERAY_TXRQ2_Bits.TXR36 */
#define IFX_ERAY_TXRQ2_TXR36_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ2_Bits.TXR36 */
#define IFX_ERAY_TXRQ2_TXR36_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ2_Bits.TXR36 */
#define IFX_ERAY_TXRQ2_TXR36_OFF (4u)

/** \brief  Length for Ifx_ERAY_TXRQ2_Bits.TXR37 */
#define IFX_ERAY_TXRQ2_TXR37_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ2_Bits.TXR37 */
#define IFX_ERAY_TXRQ2_TXR37_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ2_Bits.TXR37 */
#define IFX_ERAY_TXRQ2_TXR37_OFF (5u)

/** \brief  Length for Ifx_ERAY_TXRQ2_Bits.TXR38 */
#define IFX_ERAY_TXRQ2_TXR38_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ2_Bits.TXR38 */
#define IFX_ERAY_TXRQ2_TXR38_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ2_Bits.TXR38 */
#define IFX_ERAY_TXRQ2_TXR38_OFF (6u)

/** \brief  Length for Ifx_ERAY_TXRQ2_Bits.TXR39 */
#define IFX_ERAY_TXRQ2_TXR39_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ2_Bits.TXR39 */
#define IFX_ERAY_TXRQ2_TXR39_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ2_Bits.TXR39 */
#define IFX_ERAY_TXRQ2_TXR39_OFF (7u)

/** \brief  Length for Ifx_ERAY_TXRQ2_Bits.TXR40 */
#define IFX_ERAY_TXRQ2_TXR40_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ2_Bits.TXR40 */
#define IFX_ERAY_TXRQ2_TXR40_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ2_Bits.TXR40 */
#define IFX_ERAY_TXRQ2_TXR40_OFF (8u)

/** \brief  Length for Ifx_ERAY_TXRQ2_Bits.TXR41 */
#define IFX_ERAY_TXRQ2_TXR41_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ2_Bits.TXR41 */
#define IFX_ERAY_TXRQ2_TXR41_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ2_Bits.TXR41 */
#define IFX_ERAY_TXRQ2_TXR41_OFF (9u)

/** \brief  Length for Ifx_ERAY_TXRQ2_Bits.TXR42 */
#define IFX_ERAY_TXRQ2_TXR42_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ2_Bits.TXR42 */
#define IFX_ERAY_TXRQ2_TXR42_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ2_Bits.TXR42 */
#define IFX_ERAY_TXRQ2_TXR42_OFF (10u)

/** \brief  Length for Ifx_ERAY_TXRQ2_Bits.TXR43 */
#define IFX_ERAY_TXRQ2_TXR43_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ2_Bits.TXR43 */
#define IFX_ERAY_TXRQ2_TXR43_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ2_Bits.TXR43 */
#define IFX_ERAY_TXRQ2_TXR43_OFF (11u)

/** \brief  Length for Ifx_ERAY_TXRQ2_Bits.TXR44 */
#define IFX_ERAY_TXRQ2_TXR44_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ2_Bits.TXR44 */
#define IFX_ERAY_TXRQ2_TXR44_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ2_Bits.TXR44 */
#define IFX_ERAY_TXRQ2_TXR44_OFF (12u)

/** \brief  Length for Ifx_ERAY_TXRQ2_Bits.TXR45 */
#define IFX_ERAY_TXRQ2_TXR45_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ2_Bits.TXR45 */
#define IFX_ERAY_TXRQ2_TXR45_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ2_Bits.TXR45 */
#define IFX_ERAY_TXRQ2_TXR45_OFF (13u)

/** \brief  Length for Ifx_ERAY_TXRQ2_Bits.TXR46 */
#define IFX_ERAY_TXRQ2_TXR46_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ2_Bits.TXR46 */
#define IFX_ERAY_TXRQ2_TXR46_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ2_Bits.TXR46 */
#define IFX_ERAY_TXRQ2_TXR46_OFF (14u)

/** \brief  Length for Ifx_ERAY_TXRQ2_Bits.TXR47 */
#define IFX_ERAY_TXRQ2_TXR47_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ2_Bits.TXR47 */
#define IFX_ERAY_TXRQ2_TXR47_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ2_Bits.TXR47 */
#define IFX_ERAY_TXRQ2_TXR47_OFF (15u)

/** \brief  Length for Ifx_ERAY_TXRQ2_Bits.TXR48 */
#define IFX_ERAY_TXRQ2_TXR48_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ2_Bits.TXR48 */
#define IFX_ERAY_TXRQ2_TXR48_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ2_Bits.TXR48 */
#define IFX_ERAY_TXRQ2_TXR48_OFF (16u)

/** \brief  Length for Ifx_ERAY_TXRQ2_Bits.TXR49 */
#define IFX_ERAY_TXRQ2_TXR49_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ2_Bits.TXR49 */
#define IFX_ERAY_TXRQ2_TXR49_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ2_Bits.TXR49 */
#define IFX_ERAY_TXRQ2_TXR49_OFF (17u)

/** \brief  Length for Ifx_ERAY_TXRQ2_Bits.TXR50 */
#define IFX_ERAY_TXRQ2_TXR50_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ2_Bits.TXR50 */
#define IFX_ERAY_TXRQ2_TXR50_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ2_Bits.TXR50 */
#define IFX_ERAY_TXRQ2_TXR50_OFF (18u)

/** \brief  Length for Ifx_ERAY_TXRQ2_Bits.TXR51 */
#define IFX_ERAY_TXRQ2_TXR51_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ2_Bits.TXR51 */
#define IFX_ERAY_TXRQ2_TXR51_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ2_Bits.TXR51 */
#define IFX_ERAY_TXRQ2_TXR51_OFF (19u)

/** \brief  Length for Ifx_ERAY_TXRQ2_Bits.TXR52 */
#define IFX_ERAY_TXRQ2_TXR52_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ2_Bits.TXR52 */
#define IFX_ERAY_TXRQ2_TXR52_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ2_Bits.TXR52 */
#define IFX_ERAY_TXRQ2_TXR52_OFF (20u)

/** \brief  Length for Ifx_ERAY_TXRQ2_Bits.TXR53 */
#define IFX_ERAY_TXRQ2_TXR53_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ2_Bits.TXR53 */
#define IFX_ERAY_TXRQ2_TXR53_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ2_Bits.TXR53 */
#define IFX_ERAY_TXRQ2_TXR53_OFF (21u)

/** \brief  Length for Ifx_ERAY_TXRQ2_Bits.TXR54 */
#define IFX_ERAY_TXRQ2_TXR54_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ2_Bits.TXR54 */
#define IFX_ERAY_TXRQ2_TXR54_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ2_Bits.TXR54 */
#define IFX_ERAY_TXRQ2_TXR54_OFF (22u)

/** \brief  Length for Ifx_ERAY_TXRQ2_Bits.TXR55 */
#define IFX_ERAY_TXRQ2_TXR55_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ2_Bits.TXR55 */
#define IFX_ERAY_TXRQ2_TXR55_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ2_Bits.TXR55 */
#define IFX_ERAY_TXRQ2_TXR55_OFF (23u)

/** \brief  Length for Ifx_ERAY_TXRQ2_Bits.TXR56 */
#define IFX_ERAY_TXRQ2_TXR56_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ2_Bits.TXR56 */
#define IFX_ERAY_TXRQ2_TXR56_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ2_Bits.TXR56 */
#define IFX_ERAY_TXRQ2_TXR56_OFF (24u)

/** \brief  Length for Ifx_ERAY_TXRQ2_Bits.TXR57 */
#define IFX_ERAY_TXRQ2_TXR57_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ2_Bits.TXR57 */
#define IFX_ERAY_TXRQ2_TXR57_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ2_Bits.TXR57 */
#define IFX_ERAY_TXRQ2_TXR57_OFF (25u)

/** \brief  Length for Ifx_ERAY_TXRQ2_Bits.TXR58 */
#define IFX_ERAY_TXRQ2_TXR58_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ2_Bits.TXR58 */
#define IFX_ERAY_TXRQ2_TXR58_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ2_Bits.TXR58 */
#define IFX_ERAY_TXRQ2_TXR58_OFF (26u)

/** \brief  Length for Ifx_ERAY_TXRQ2_Bits.TXR59 */
#define IFX_ERAY_TXRQ2_TXR59_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ2_Bits.TXR59 */
#define IFX_ERAY_TXRQ2_TXR59_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ2_Bits.TXR59 */
#define IFX_ERAY_TXRQ2_TXR59_OFF (27u)

/** \brief  Length for Ifx_ERAY_TXRQ2_Bits.TXR60 */
#define IFX_ERAY_TXRQ2_TXR60_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ2_Bits.TXR60 */
#define IFX_ERAY_TXRQ2_TXR60_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ2_Bits.TXR60 */
#define IFX_ERAY_TXRQ2_TXR60_OFF (28u)

/** \brief  Length for Ifx_ERAY_TXRQ2_Bits.TXR61 */
#define IFX_ERAY_TXRQ2_TXR61_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ2_Bits.TXR61 */
#define IFX_ERAY_TXRQ2_TXR61_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ2_Bits.TXR61 */
#define IFX_ERAY_TXRQ2_TXR61_OFF (29u)

/** \brief  Length for Ifx_ERAY_TXRQ2_Bits.TXR62 */
#define IFX_ERAY_TXRQ2_TXR62_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ2_Bits.TXR62 */
#define IFX_ERAY_TXRQ2_TXR62_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ2_Bits.TXR62 */
#define IFX_ERAY_TXRQ2_TXR62_OFF (30u)

/** \brief  Length for Ifx_ERAY_TXRQ2_Bits.TXR63 */
#define IFX_ERAY_TXRQ2_TXR63_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ2_Bits.TXR63 */
#define IFX_ERAY_TXRQ2_TXR63_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ2_Bits.TXR63 */
#define IFX_ERAY_TXRQ2_TXR63_OFF (31u)

/** \brief  Length for Ifx_ERAY_TXRQ3_Bits.TXR64 */
#define IFX_ERAY_TXRQ3_TXR64_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ3_Bits.TXR64 */
#define IFX_ERAY_TXRQ3_TXR64_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ3_Bits.TXR64 */
#define IFX_ERAY_TXRQ3_TXR64_OFF (0u)

/** \brief  Length for Ifx_ERAY_TXRQ3_Bits.TXR65 */
#define IFX_ERAY_TXRQ3_TXR65_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ3_Bits.TXR65 */
#define IFX_ERAY_TXRQ3_TXR65_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ3_Bits.TXR65 */
#define IFX_ERAY_TXRQ3_TXR65_OFF (1u)

/** \brief  Length for Ifx_ERAY_TXRQ3_Bits.TXR66 */
#define IFX_ERAY_TXRQ3_TXR66_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ3_Bits.TXR66 */
#define IFX_ERAY_TXRQ3_TXR66_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ3_Bits.TXR66 */
#define IFX_ERAY_TXRQ3_TXR66_OFF (2u)

/** \brief  Length for Ifx_ERAY_TXRQ3_Bits.TXR67 */
#define IFX_ERAY_TXRQ3_TXR67_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ3_Bits.TXR67 */
#define IFX_ERAY_TXRQ3_TXR67_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ3_Bits.TXR67 */
#define IFX_ERAY_TXRQ3_TXR67_OFF (3u)

/** \brief  Length for Ifx_ERAY_TXRQ3_Bits.TXR68 */
#define IFX_ERAY_TXRQ3_TXR68_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ3_Bits.TXR68 */
#define IFX_ERAY_TXRQ3_TXR68_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ3_Bits.TXR68 */
#define IFX_ERAY_TXRQ3_TXR68_OFF (4u)

/** \brief  Length for Ifx_ERAY_TXRQ3_Bits.TXR69 */
#define IFX_ERAY_TXRQ3_TXR69_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ3_Bits.TXR69 */
#define IFX_ERAY_TXRQ3_TXR69_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ3_Bits.TXR69 */
#define IFX_ERAY_TXRQ3_TXR69_OFF (5u)

/** \brief  Length for Ifx_ERAY_TXRQ3_Bits.TXR70 */
#define IFX_ERAY_TXRQ3_TXR70_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ3_Bits.TXR70 */
#define IFX_ERAY_TXRQ3_TXR70_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ3_Bits.TXR70 */
#define IFX_ERAY_TXRQ3_TXR70_OFF (6u)

/** \brief  Length for Ifx_ERAY_TXRQ3_Bits.TXR71 */
#define IFX_ERAY_TXRQ3_TXR71_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ3_Bits.TXR71 */
#define IFX_ERAY_TXRQ3_TXR71_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ3_Bits.TXR71 */
#define IFX_ERAY_TXRQ3_TXR71_OFF (7u)

/** \brief  Length for Ifx_ERAY_TXRQ3_Bits.TXR72 */
#define IFX_ERAY_TXRQ3_TXR72_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ3_Bits.TXR72 */
#define IFX_ERAY_TXRQ3_TXR72_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ3_Bits.TXR72 */
#define IFX_ERAY_TXRQ3_TXR72_OFF (8u)

/** \brief  Length for Ifx_ERAY_TXRQ3_Bits.TXR73 */
#define IFX_ERAY_TXRQ3_TXR73_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ3_Bits.TXR73 */
#define IFX_ERAY_TXRQ3_TXR73_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ3_Bits.TXR73 */
#define IFX_ERAY_TXRQ3_TXR73_OFF (9u)

/** \brief  Length for Ifx_ERAY_TXRQ3_Bits.TXR74 */
#define IFX_ERAY_TXRQ3_TXR74_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ3_Bits.TXR74 */
#define IFX_ERAY_TXRQ3_TXR74_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ3_Bits.TXR74 */
#define IFX_ERAY_TXRQ3_TXR74_OFF (10u)

/** \brief  Length for Ifx_ERAY_TXRQ3_Bits.TXR75 */
#define IFX_ERAY_TXRQ3_TXR75_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ3_Bits.TXR75 */
#define IFX_ERAY_TXRQ3_TXR75_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ3_Bits.TXR75 */
#define IFX_ERAY_TXRQ3_TXR75_OFF (11u)

/** \brief  Length for Ifx_ERAY_TXRQ3_Bits.TXR76 */
#define IFX_ERAY_TXRQ3_TXR76_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ3_Bits.TXR76 */
#define IFX_ERAY_TXRQ3_TXR76_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ3_Bits.TXR76 */
#define IFX_ERAY_TXRQ3_TXR76_OFF (12u)

/** \brief  Length for Ifx_ERAY_TXRQ3_Bits.TXR77 */
#define IFX_ERAY_TXRQ3_TXR77_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ3_Bits.TXR77 */
#define IFX_ERAY_TXRQ3_TXR77_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ3_Bits.TXR77 */
#define IFX_ERAY_TXRQ3_TXR77_OFF (13u)

/** \brief  Length for Ifx_ERAY_TXRQ3_Bits.TXR78 */
#define IFX_ERAY_TXRQ3_TXR78_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ3_Bits.TXR78 */
#define IFX_ERAY_TXRQ3_TXR78_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ3_Bits.TXR78 */
#define IFX_ERAY_TXRQ3_TXR78_OFF (14u)

/** \brief  Length for Ifx_ERAY_TXRQ3_Bits.TXR79 */
#define IFX_ERAY_TXRQ3_TXR79_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ3_Bits.TXR79 */
#define IFX_ERAY_TXRQ3_TXR79_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ3_Bits.TXR79 */
#define IFX_ERAY_TXRQ3_TXR79_OFF (15u)

/** \brief  Length for Ifx_ERAY_TXRQ3_Bits.TXR80 */
#define IFX_ERAY_TXRQ3_TXR80_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ3_Bits.TXR80 */
#define IFX_ERAY_TXRQ3_TXR80_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ3_Bits.TXR80 */
#define IFX_ERAY_TXRQ3_TXR80_OFF (16u)

/** \brief  Length for Ifx_ERAY_TXRQ3_Bits.TXR81 */
#define IFX_ERAY_TXRQ3_TXR81_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ3_Bits.TXR81 */
#define IFX_ERAY_TXRQ3_TXR81_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ3_Bits.TXR81 */
#define IFX_ERAY_TXRQ3_TXR81_OFF (17u)

/** \brief  Length for Ifx_ERAY_TXRQ3_Bits.TXR82 */
#define IFX_ERAY_TXRQ3_TXR82_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ3_Bits.TXR82 */
#define IFX_ERAY_TXRQ3_TXR82_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ3_Bits.TXR82 */
#define IFX_ERAY_TXRQ3_TXR82_OFF (18u)

/** \brief  Length for Ifx_ERAY_TXRQ3_Bits.TXR83 */
#define IFX_ERAY_TXRQ3_TXR83_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ3_Bits.TXR83 */
#define IFX_ERAY_TXRQ3_TXR83_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ3_Bits.TXR83 */
#define IFX_ERAY_TXRQ3_TXR83_OFF (19u)

/** \brief  Length for Ifx_ERAY_TXRQ3_Bits.TXR84 */
#define IFX_ERAY_TXRQ3_TXR84_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ3_Bits.TXR84 */
#define IFX_ERAY_TXRQ3_TXR84_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ3_Bits.TXR84 */
#define IFX_ERAY_TXRQ3_TXR84_OFF (20u)

/** \brief  Length for Ifx_ERAY_TXRQ3_Bits.TXR85 */
#define IFX_ERAY_TXRQ3_TXR85_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ3_Bits.TXR85 */
#define IFX_ERAY_TXRQ3_TXR85_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ3_Bits.TXR85 */
#define IFX_ERAY_TXRQ3_TXR85_OFF (21u)

/** \brief  Length for Ifx_ERAY_TXRQ3_Bits.TXR86 */
#define IFX_ERAY_TXRQ3_TXR86_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ3_Bits.TXR86 */
#define IFX_ERAY_TXRQ3_TXR86_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ3_Bits.TXR86 */
#define IFX_ERAY_TXRQ3_TXR86_OFF (22u)

/** \brief  Length for Ifx_ERAY_TXRQ3_Bits.TXR87 */
#define IFX_ERAY_TXRQ3_TXR87_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ3_Bits.TXR87 */
#define IFX_ERAY_TXRQ3_TXR87_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ3_Bits.TXR87 */
#define IFX_ERAY_TXRQ3_TXR87_OFF (23u)

/** \brief  Length for Ifx_ERAY_TXRQ3_Bits.TXR88 */
#define IFX_ERAY_TXRQ3_TXR88_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ3_Bits.TXR88 */
#define IFX_ERAY_TXRQ3_TXR88_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ3_Bits.TXR88 */
#define IFX_ERAY_TXRQ3_TXR88_OFF (24u)

/** \brief  Length for Ifx_ERAY_TXRQ3_Bits.TXR89 */
#define IFX_ERAY_TXRQ3_TXR89_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ3_Bits.TXR89 */
#define IFX_ERAY_TXRQ3_TXR89_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ3_Bits.TXR89 */
#define IFX_ERAY_TXRQ3_TXR89_OFF (25u)

/** \brief  Length for Ifx_ERAY_TXRQ3_Bits.TXR90 */
#define IFX_ERAY_TXRQ3_TXR90_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ3_Bits.TXR90 */
#define IFX_ERAY_TXRQ3_TXR90_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ3_Bits.TXR90 */
#define IFX_ERAY_TXRQ3_TXR90_OFF (26u)

/** \brief  Length for Ifx_ERAY_TXRQ3_Bits.TXR91 */
#define IFX_ERAY_TXRQ3_TXR91_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ3_Bits.TXR91 */
#define IFX_ERAY_TXRQ3_TXR91_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ3_Bits.TXR91 */
#define IFX_ERAY_TXRQ3_TXR91_OFF (27u)

/** \brief  Length for Ifx_ERAY_TXRQ3_Bits.TXR92 */
#define IFX_ERAY_TXRQ3_TXR92_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ3_Bits.TXR92 */
#define IFX_ERAY_TXRQ3_TXR92_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ3_Bits.TXR92 */
#define IFX_ERAY_TXRQ3_TXR92_OFF (28u)

/** \brief  Length for Ifx_ERAY_TXRQ3_Bits.TXR93 */
#define IFX_ERAY_TXRQ3_TXR93_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ3_Bits.TXR93 */
#define IFX_ERAY_TXRQ3_TXR93_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ3_Bits.TXR93 */
#define IFX_ERAY_TXRQ3_TXR93_OFF (29u)

/** \brief  Length for Ifx_ERAY_TXRQ3_Bits.TXR94 */
#define IFX_ERAY_TXRQ3_TXR94_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ3_Bits.TXR94 */
#define IFX_ERAY_TXRQ3_TXR94_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ3_Bits.TXR94 */
#define IFX_ERAY_TXRQ3_TXR94_OFF (30u)

/** \brief  Length for Ifx_ERAY_TXRQ3_Bits.TXR95 */
#define IFX_ERAY_TXRQ3_TXR95_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ3_Bits.TXR95 */
#define IFX_ERAY_TXRQ3_TXR95_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ3_Bits.TXR95 */
#define IFX_ERAY_TXRQ3_TXR95_OFF (31u)

/** \brief  Length for Ifx_ERAY_TXRQ4_Bits.TXR100 */
#define IFX_ERAY_TXRQ4_TXR100_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ4_Bits.TXR100 */
#define IFX_ERAY_TXRQ4_TXR100_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ4_Bits.TXR100 */
#define IFX_ERAY_TXRQ4_TXR100_OFF (4u)

/** \brief  Length for Ifx_ERAY_TXRQ4_Bits.TXR101 */
#define IFX_ERAY_TXRQ4_TXR101_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ4_Bits.TXR101 */
#define IFX_ERAY_TXRQ4_TXR101_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ4_Bits.TXR101 */
#define IFX_ERAY_TXRQ4_TXR101_OFF (5u)

/** \brief  Length for Ifx_ERAY_TXRQ4_Bits.TXR102 */
#define IFX_ERAY_TXRQ4_TXR102_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ4_Bits.TXR102 */
#define IFX_ERAY_TXRQ4_TXR102_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ4_Bits.TXR102 */
#define IFX_ERAY_TXRQ4_TXR102_OFF (6u)

/** \brief  Length for Ifx_ERAY_TXRQ4_Bits.TXR103 */
#define IFX_ERAY_TXRQ4_TXR103_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ4_Bits.TXR103 */
#define IFX_ERAY_TXRQ4_TXR103_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ4_Bits.TXR103 */
#define IFX_ERAY_TXRQ4_TXR103_OFF (7u)

/** \brief  Length for Ifx_ERAY_TXRQ4_Bits.TXR104 */
#define IFX_ERAY_TXRQ4_TXR104_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ4_Bits.TXR104 */
#define IFX_ERAY_TXRQ4_TXR104_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ4_Bits.TXR104 */
#define IFX_ERAY_TXRQ4_TXR104_OFF (8u)

/** \brief  Length for Ifx_ERAY_TXRQ4_Bits.TXR105 */
#define IFX_ERAY_TXRQ4_TXR105_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ4_Bits.TXR105 */
#define IFX_ERAY_TXRQ4_TXR105_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ4_Bits.TXR105 */
#define IFX_ERAY_TXRQ4_TXR105_OFF (9u)

/** \brief  Length for Ifx_ERAY_TXRQ4_Bits.TXR106 */
#define IFX_ERAY_TXRQ4_TXR106_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ4_Bits.TXR106 */
#define IFX_ERAY_TXRQ4_TXR106_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ4_Bits.TXR106 */
#define IFX_ERAY_TXRQ4_TXR106_OFF (10u)

/** \brief  Length for Ifx_ERAY_TXRQ4_Bits.TXR107 */
#define IFX_ERAY_TXRQ4_TXR107_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ4_Bits.TXR107 */
#define IFX_ERAY_TXRQ4_TXR107_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ4_Bits.TXR107 */
#define IFX_ERAY_TXRQ4_TXR107_OFF (11u)

/** \brief  Length for Ifx_ERAY_TXRQ4_Bits.TXR108 */
#define IFX_ERAY_TXRQ4_TXR108_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ4_Bits.TXR108 */
#define IFX_ERAY_TXRQ4_TXR108_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ4_Bits.TXR108 */
#define IFX_ERAY_TXRQ4_TXR108_OFF (12u)

/** \brief  Length for Ifx_ERAY_TXRQ4_Bits.TXR109 */
#define IFX_ERAY_TXRQ4_TXR109_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ4_Bits.TXR109 */
#define IFX_ERAY_TXRQ4_TXR109_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ4_Bits.TXR109 */
#define IFX_ERAY_TXRQ4_TXR109_OFF (13u)

/** \brief  Length for Ifx_ERAY_TXRQ4_Bits.TXR110 */
#define IFX_ERAY_TXRQ4_TXR110_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ4_Bits.TXR110 */
#define IFX_ERAY_TXRQ4_TXR110_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ4_Bits.TXR110 */
#define IFX_ERAY_TXRQ4_TXR110_OFF (14u)

/** \brief  Length for Ifx_ERAY_TXRQ4_Bits.TXR111 */
#define IFX_ERAY_TXRQ4_TXR111_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ4_Bits.TXR111 */
#define IFX_ERAY_TXRQ4_TXR111_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ4_Bits.TXR111 */
#define IFX_ERAY_TXRQ4_TXR111_OFF (15u)

/** \brief  Length for Ifx_ERAY_TXRQ4_Bits.TXR112 */
#define IFX_ERAY_TXRQ4_TXR112_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ4_Bits.TXR112 */
#define IFX_ERAY_TXRQ4_TXR112_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ4_Bits.TXR112 */
#define IFX_ERAY_TXRQ4_TXR112_OFF (16u)

/** \brief  Length for Ifx_ERAY_TXRQ4_Bits.TXR113 */
#define IFX_ERAY_TXRQ4_TXR113_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ4_Bits.TXR113 */
#define IFX_ERAY_TXRQ4_TXR113_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ4_Bits.TXR113 */
#define IFX_ERAY_TXRQ4_TXR113_OFF (17u)

/** \brief  Length for Ifx_ERAY_TXRQ4_Bits.TXR114 */
#define IFX_ERAY_TXRQ4_TXR114_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ4_Bits.TXR114 */
#define IFX_ERAY_TXRQ4_TXR114_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ4_Bits.TXR114 */
#define IFX_ERAY_TXRQ4_TXR114_OFF (18u)

/** \brief  Length for Ifx_ERAY_TXRQ4_Bits.TXR115 */
#define IFX_ERAY_TXRQ4_TXR115_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ4_Bits.TXR115 */
#define IFX_ERAY_TXRQ4_TXR115_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ4_Bits.TXR115 */
#define IFX_ERAY_TXRQ4_TXR115_OFF (19u)

/** \brief  Length for Ifx_ERAY_TXRQ4_Bits.TXR116 */
#define IFX_ERAY_TXRQ4_TXR116_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ4_Bits.TXR116 */
#define IFX_ERAY_TXRQ4_TXR116_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ4_Bits.TXR116 */
#define IFX_ERAY_TXRQ4_TXR116_OFF (20u)

/** \brief  Length for Ifx_ERAY_TXRQ4_Bits.TXR117 */
#define IFX_ERAY_TXRQ4_TXR117_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ4_Bits.TXR117 */
#define IFX_ERAY_TXRQ4_TXR117_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ4_Bits.TXR117 */
#define IFX_ERAY_TXRQ4_TXR117_OFF (21u)

/** \brief  Length for Ifx_ERAY_TXRQ4_Bits.TXR118 */
#define IFX_ERAY_TXRQ4_TXR118_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ4_Bits.TXR118 */
#define IFX_ERAY_TXRQ4_TXR118_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ4_Bits.TXR118 */
#define IFX_ERAY_TXRQ4_TXR118_OFF (22u)

/** \brief  Length for Ifx_ERAY_TXRQ4_Bits.TXR119 */
#define IFX_ERAY_TXRQ4_TXR119_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ4_Bits.TXR119 */
#define IFX_ERAY_TXRQ4_TXR119_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ4_Bits.TXR119 */
#define IFX_ERAY_TXRQ4_TXR119_OFF (23u)

/** \brief  Length for Ifx_ERAY_TXRQ4_Bits.TXR120 */
#define IFX_ERAY_TXRQ4_TXR120_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ4_Bits.TXR120 */
#define IFX_ERAY_TXRQ4_TXR120_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ4_Bits.TXR120 */
#define IFX_ERAY_TXRQ4_TXR120_OFF (24u)

/** \brief  Length for Ifx_ERAY_TXRQ4_Bits.TXR121 */
#define IFX_ERAY_TXRQ4_TXR121_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ4_Bits.TXR121 */
#define IFX_ERAY_TXRQ4_TXR121_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ4_Bits.TXR121 */
#define IFX_ERAY_TXRQ4_TXR121_OFF (25u)

/** \brief  Length for Ifx_ERAY_TXRQ4_Bits.TXR122 */
#define IFX_ERAY_TXRQ4_TXR122_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ4_Bits.TXR122 */
#define IFX_ERAY_TXRQ4_TXR122_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ4_Bits.TXR122 */
#define IFX_ERAY_TXRQ4_TXR122_OFF (26u)

/** \brief  Length for Ifx_ERAY_TXRQ4_Bits.TXR123 */
#define IFX_ERAY_TXRQ4_TXR123_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ4_Bits.TXR123 */
#define IFX_ERAY_TXRQ4_TXR123_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ4_Bits.TXR123 */
#define IFX_ERAY_TXRQ4_TXR123_OFF (27u)

/** \brief  Length for Ifx_ERAY_TXRQ4_Bits.TXR124 */
#define IFX_ERAY_TXRQ4_TXR124_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ4_Bits.TXR124 */
#define IFX_ERAY_TXRQ4_TXR124_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ4_Bits.TXR124 */
#define IFX_ERAY_TXRQ4_TXR124_OFF (28u)

/** \brief  Length for Ifx_ERAY_TXRQ4_Bits.TXR125 */
#define IFX_ERAY_TXRQ4_TXR125_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ4_Bits.TXR125 */
#define IFX_ERAY_TXRQ4_TXR125_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ4_Bits.TXR125 */
#define IFX_ERAY_TXRQ4_TXR125_OFF (29u)

/** \brief  Length for Ifx_ERAY_TXRQ4_Bits.TXR126 */
#define IFX_ERAY_TXRQ4_TXR126_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ4_Bits.TXR126 */
#define IFX_ERAY_TXRQ4_TXR126_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ4_Bits.TXR126 */
#define IFX_ERAY_TXRQ4_TXR126_OFF (30u)

/** \brief  Length for Ifx_ERAY_TXRQ4_Bits.TXR127 */
#define IFX_ERAY_TXRQ4_TXR127_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ4_Bits.TXR127 */
#define IFX_ERAY_TXRQ4_TXR127_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ4_Bits.TXR127 */
#define IFX_ERAY_TXRQ4_TXR127_OFF (31u)

/** \brief  Length for Ifx_ERAY_TXRQ4_Bits.TXR96 */
#define IFX_ERAY_TXRQ4_TXR96_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ4_Bits.TXR96 */
#define IFX_ERAY_TXRQ4_TXR96_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ4_Bits.TXR96 */
#define IFX_ERAY_TXRQ4_TXR96_OFF (0u)

/** \brief  Length for Ifx_ERAY_TXRQ4_Bits.TXR97 */
#define IFX_ERAY_TXRQ4_TXR97_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ4_Bits.TXR97 */
#define IFX_ERAY_TXRQ4_TXR97_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ4_Bits.TXR97 */
#define IFX_ERAY_TXRQ4_TXR97_OFF (1u)

/** \brief  Length for Ifx_ERAY_TXRQ4_Bits.TXR98 */
#define IFX_ERAY_TXRQ4_TXR98_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ4_Bits.TXR98 */
#define IFX_ERAY_TXRQ4_TXR98_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ4_Bits.TXR98 */
#define IFX_ERAY_TXRQ4_TXR98_OFF (2u)

/** \brief  Length for Ifx_ERAY_TXRQ4_Bits.TXR99 */
#define IFX_ERAY_TXRQ4_TXR99_LEN (1u)

/** \brief  Mask for Ifx_ERAY_TXRQ4_Bits.TXR99 */
#define IFX_ERAY_TXRQ4_TXR99_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_TXRQ4_Bits.TXR99 */
#define IFX_ERAY_TXRQ4_TXR99_OFF (3u)

/** \brief  Length for Ifx_ERAY_WRDS_Bits.MDWB0 */
#define IFX_ERAY_WRDS_MDWB0_LEN (8u)

/** \brief  Mask for Ifx_ERAY_WRDS_Bits.MDWB0 */
#define IFX_ERAY_WRDS_MDWB0_MSK (0xffu)

/** \brief  Offset for Ifx_ERAY_WRDS_Bits.MDWB0 */
#define IFX_ERAY_WRDS_MDWB0_OFF (0u)

/** \brief  Length for Ifx_ERAY_WRDS_Bits.MDWB1 */
#define IFX_ERAY_WRDS_MDWB1_LEN (8u)

/** \brief  Mask for Ifx_ERAY_WRDS_Bits.MDWB1 */
#define IFX_ERAY_WRDS_MDWB1_MSK (0xffu)

/** \brief  Offset for Ifx_ERAY_WRDS_Bits.MDWB1 */
#define IFX_ERAY_WRDS_MDWB1_OFF (8u)

/** \brief  Length for Ifx_ERAY_WRDS_Bits.MDWB2 */
#define IFX_ERAY_WRDS_MDWB2_LEN (8u)

/** \brief  Mask for Ifx_ERAY_WRDS_Bits.MDWB2 */
#define IFX_ERAY_WRDS_MDWB2_MSK (0xffu)

/** \brief  Offset for Ifx_ERAY_WRDS_Bits.MDWB2 */
#define IFX_ERAY_WRDS_MDWB2_OFF (16u)

/** \brief  Length for Ifx_ERAY_WRDS_Bits.MDWB3 */
#define IFX_ERAY_WRDS_MDWB3_LEN (8u)

/** \brief  Mask for Ifx_ERAY_WRDS_Bits.MDWB3 */
#define IFX_ERAY_WRDS_MDWB3_MSK (0xffu)

/** \brief  Offset for Ifx_ERAY_WRDS_Bits.MDWB3 */
#define IFX_ERAY_WRDS_MDWB3_OFF (24u)

/** \brief  Length for Ifx_ERAY_WRHS1_Bits.CFG */
#define IFX_ERAY_WRHS1_CFG_LEN (1u)

/** \brief  Mask for Ifx_ERAY_WRHS1_Bits.CFG */
#define IFX_ERAY_WRHS1_CFG_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_WRHS1_Bits.CFG */
#define IFX_ERAY_WRHS1_CFG_OFF (26u)

/** \brief  Length for Ifx_ERAY_WRHS1_Bits.CHA */
#define IFX_ERAY_WRHS1_CHA_LEN (1u)

/** \brief  Mask for Ifx_ERAY_WRHS1_Bits.CHA */
#define IFX_ERAY_WRHS1_CHA_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_WRHS1_Bits.CHA */
#define IFX_ERAY_WRHS1_CHA_OFF (24u)

/** \brief  Length for Ifx_ERAY_WRHS1_Bits.CHB */
#define IFX_ERAY_WRHS1_CHB_LEN (1u)

/** \brief  Mask for Ifx_ERAY_WRHS1_Bits.CHB */
#define IFX_ERAY_WRHS1_CHB_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_WRHS1_Bits.CHB */
#define IFX_ERAY_WRHS1_CHB_OFF (25u)

/** \brief  Length for Ifx_ERAY_WRHS1_Bits.CYC */
#define IFX_ERAY_WRHS1_CYC_LEN (7u)

/** \brief  Mask for Ifx_ERAY_WRHS1_Bits.CYC */
#define IFX_ERAY_WRHS1_CYC_MSK (0x7fu)

/** \brief  Offset for Ifx_ERAY_WRHS1_Bits.CYC */
#define IFX_ERAY_WRHS1_CYC_OFF (16u)

/** \brief  Length for Ifx_ERAY_WRHS1_Bits.FID */
#define IFX_ERAY_WRHS1_FID_LEN (11u)

/** \brief  Mask for Ifx_ERAY_WRHS1_Bits.FID */
#define IFX_ERAY_WRHS1_FID_MSK (0x7ffu)

/** \brief  Offset for Ifx_ERAY_WRHS1_Bits.FID */
#define IFX_ERAY_WRHS1_FID_OFF (0u)

/** \brief  Length for Ifx_ERAY_WRHS1_Bits.MBI */
#define IFX_ERAY_WRHS1_MBI_LEN (1u)

/** \brief  Mask for Ifx_ERAY_WRHS1_Bits.MBI */
#define IFX_ERAY_WRHS1_MBI_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_WRHS1_Bits.MBI */
#define IFX_ERAY_WRHS1_MBI_OFF (29u)

/** \brief  Length for Ifx_ERAY_WRHS1_Bits.PPIT */
#define IFX_ERAY_WRHS1_PPIT_LEN (1u)

/** \brief  Mask for Ifx_ERAY_WRHS1_Bits.PPIT */
#define IFX_ERAY_WRHS1_PPIT_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_WRHS1_Bits.PPIT */
#define IFX_ERAY_WRHS1_PPIT_OFF (27u)

/** \brief  Length for Ifx_ERAY_WRHS1_Bits.TXM */
#define IFX_ERAY_WRHS1_TXM_LEN (1u)

/** \brief  Mask for Ifx_ERAY_WRHS1_Bits.TXM */
#define IFX_ERAY_WRHS1_TXM_MSK (0x1u)

/** \brief  Offset for Ifx_ERAY_WRHS1_Bits.TXM */
#define IFX_ERAY_WRHS1_TXM_OFF (28u)

/** \brief  Length for Ifx_ERAY_WRHS2_Bits.CRC */
#define IFX_ERAY_WRHS2_CRC_LEN (11u)

/** \brief  Mask for Ifx_ERAY_WRHS2_Bits.CRC */
#define IFX_ERAY_WRHS2_CRC_MSK (0x7ffu)

/** \brief  Offset for Ifx_ERAY_WRHS2_Bits.CRC */
#define IFX_ERAY_WRHS2_CRC_OFF (0u)

/** \brief  Length for Ifx_ERAY_WRHS2_Bits.PLC */
#define IFX_ERAY_WRHS2_PLC_LEN (7u)

/** \brief  Mask for Ifx_ERAY_WRHS2_Bits.PLC */
#define IFX_ERAY_WRHS2_PLC_MSK (0x7fu)

/** \brief  Offset for Ifx_ERAY_WRHS2_Bits.PLC */
#define IFX_ERAY_WRHS2_PLC_OFF (16u)

/** \brief  Length for Ifx_ERAY_WRHS3_Bits.DP */
#define IFX_ERAY_WRHS3_DP_LEN (11u)

/** \brief  Mask for Ifx_ERAY_WRHS3_Bits.DP */
#define IFX_ERAY_WRHS3_DP_MSK (0x7ffu)

/** \brief  Offset for Ifx_ERAY_WRHS3_Bits.DP */
#define IFX_ERAY_WRHS3_DP_OFF (0u)
/** \}  */
/******************************************************************************/
/******************************************************************************/
#endif /* IFXERAY_BF_H */
