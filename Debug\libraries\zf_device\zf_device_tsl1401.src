	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc35400a --dep-file=zf_device_tsl1401.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2\\\\xf_asr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/zf_device/zf_device_tsl1401.src ../libraries/zf_device/zf_device_tsl1401.c"
	.compiler_name		"ctc"
	;source	'../libraries/zf_device/zf_device_tsl1401.c'

	
$TC16X
	
	.sdecl	'.text.zf_device_tsl1401.tsl1401_collect_pit_handler',code,cluster('tsl1401_collect_pit_handler')
	.sect	'.text.zf_device_tsl1401.tsl1401_collect_pit_handler'
	.align	2
	
	.global	tsl1401_collect_pit_handler
; Function tsl1401_collect_pit_handler
.L32:
tsl1401_collect_pit_handler:	.type	func
	movh.a	a15,#@his(tsl1401_init_state)
	lea	a15,[a15]@los(tsl1401_init_state)
	ld.bu	d15,[a15]
	jne	d15,#0,.L2
.L110:
	j	.L3
.L2:
	mov	d15,#1
	jeq	d15,#0,.L4
	mov	d4,#0
	call	get_port
	add.a	a2,#4
	mov	d15,#1
	st.w	[a2],d15
	j	.L5
.L4:
	mov	d4,#0
	call	get_port
	add.a	a2,#4
	movh	d15,#1
	st.w	[a2],d15
.L5:
	mov	d15,#0
	jeq	d15,#0,.L6
	mov	d4,#1
	call	get_port
	add.a	a2,#4
	mov	d15,#2
	st.w	[a2],d15
	j	.L7
.L6:
	mov	d4,#1
	call	get_port
	add.a	a2,#4
	movh	d15,#2
	st.w	[a2],d15
.L7:
	mov	d15,#0
	jeq	d15,#0,.L8
	mov	d4,#0
	call	get_port
	add.a	a2,#4
	mov	d15,#1
	st.w	[a2],d15
	j	.L9
.L8:
	mov	d4,#0
	call	get_port
	add.a	a2,#4
	movh	d15,#1
	st.w	[a2],d15
.L9:
	mov	d15,#1
	jeq	d15,#0,.L10
	mov	d4,#1
	call	get_port
	add.a	a2,#4
	mov	d15,#2
	st.w	[a2],d15
	j	.L11
.L10:
	mov	d4,#1
	call	get_port
	add.a	a2,#4
	movh	d15,#2
	st.w	[a2],d15
.L11:
	mov	d15,#1
	jeq	d15,#0,.L12
	mov	d4,#0
	call	get_port
	add.a	a2,#4
	mov	d15,#1
	st.w	[a2],d15
	j	.L13
.L12:
	mov	d4,#0
	call	get_port
	add.a	a2,#4
	movh	d15,#1
	st.w	[a2],d15
.L13:
	mov	d15,#0
	jeq	d15,#0,.L14
	mov	d4,#1
	call	get_port
	add.a	a2,#4
	mov	d15,#2
	st.w	[a2],d15
	j	.L15
.L14:
	mov	d4,#1
	call	get_port
	add.a	a2,#4
	movh	d15,#2
	st.w	[a2],d15
.L15:
	mov	d8,#0
.L73:
	j	.L16
.L17:
	mov	d15,#0
	jeq	d15,#0,.L18
	mov	d4,#0
	call	get_port
	add.a	a2,#4
	mov	d15,#1
	st.w	[a2],d15
	j	.L19
.L18:
	mov	d4,#0
	call	get_port
	add.a	a2,#4
	movh	d15,#1
	st.w	[a2],d15
.L19:
	mul	d15,d8,#2
.L111:
	movh.a	a15,#@his(tsl1401_data)
	lea	a15,[a15]@los(tsl1401_data)
.L112:
	addsc.a	a15,a15,d15,#0
.L113:
	mov	d4,#4
	call	adc_convert
.L114:
	st.h	[a15],d2
.L115:
	mul	d15,d8,#2
.L116:
	movh.a	a15,#@his(tsl1401_data)
	lea	a15,[a15]@los(tsl1401_data)
.L117:
	addsc.a	a15,a15,d15,#0
.L118:
	mov	d4,#5
	call	adc_convert
.L119:
	st.h	[a15]256,d2
.L120:
	mov	d15,#1
	jeq	d15,#0,.L20
	mov	d4,#0
	call	get_port
	add.a	a2,#4
	mov	d15,#1
	st.w	[a2],d15
	j	.L21
.L20:
	mov	d4,#0
	call	get_port
	add.a	a2,#4
	movh	d15,#1
	st.w	[a2],d15
.L21:
	add	d8,#1
.L16:
	mov	d15,#128
.L121:
	jlt.u	d8,d15,.L17
.L122:
	movh.a	a15,#@his(tsl1401_finish_flag)
	lea	a15,[a15]@los(tsl1401_finish_flag)
.L123:
	mov	d15,#1
.L124:
	st.b	[a15],d15
.L3:
	ret
.L62:
	
__tsl1401_collect_pit_handler_function_end:
	.size	tsl1401_collect_pit_handler,__tsl1401_collect_pit_handler_function_end-tsl1401_collect_pit_handler
.L45:
	; End of function
	
	.sdecl	'.text.zf_device_tsl1401.tsl1401_send_data',code,cluster('tsl1401_send_data')
	.sect	'.text.zf_device_tsl1401.tsl1401_send_data'
	.align	2
	
	.global	tsl1401_send_data
; Function tsl1401_send_data
.L34:
tsl1401_send_data:	.type	func
	mov	e8,d5,d4
.L129:
	mov	d5,#0
.L74:
	mov	d4,d8
.L76:
	call	uart_write_byte
.L75:
	mov	d5,#255
.L77:
	mov	d4,d8
.L78:
	call	uart_write_byte
.L79:
	mov	d5,#1
.L80:
	mov	d4,d8
.L81:
	call	uart_write_byte
.L82:
	mov	d5,#0
.L83:
	mov	d4,d8
.L84:
	call	uart_write_byte
.L85:
	mov	d10,#0
.L86:
	j	.L22
.L23:
	mov	d15,#0
.L130:
	mov	d0,#0
	jeq	d15,d0,.L24
.L131:
	mov	d0,#1
	jeq	d15,d0,.L25
.L132:
	mov	d0,#2
	jeq	d15,d0,.L26
	j	.L27
.L24:
	mul	d15,d10,#2
.L133:
	mov	d0,#256
.L87:
	mul	d0,d9
.L88:
	movh.a	a15,#@his(tsl1401_data)
	lea	a15,[a15]@los(tsl1401_data)
.L134:
	addsc.a	a15,a15,d0,#0
.L135:
	addsc.a	a15,a15,d15,#0
	ld.hu	d15,[a15]0
.L136:
	extr.u	d5,d15,#0,#8
.L89:
	mov	d4,d8
.L90:
	call	uart_write_byte
.L91:
	j	.L28
.L25:
	mul	d15,d10,#2
.L137:
	mov	d0,#256
.L92:
	mul	d0,d9
.L93:
	movh.a	a15,#@his(tsl1401_data)
	lea	a15,[a15]@los(tsl1401_data)
.L138:
	addsc.a	a15,a15,d0,#0
.L139:
	addsc.a	a15,a15,d15,#0
	ld.hu	d15,[a15]0
.L140:
	sha	d15,#-2
.L141:
	extr.u	d5,d15,#0,#8
.L94:
	mov	d4,d8
.L95:
	call	uart_write_byte
.L96:
	j	.L29
.L26:
	mul	d15,d10,#2
.L142:
	mov	d0,#256
.L97:
	mul	d0,d9
.L98:
	movh.a	a15,#@his(tsl1401_data)
	lea	a15,[a15]@los(tsl1401_data)
.L143:
	addsc.a	a15,a15,d0,#0
.L144:
	addsc.a	a15,a15,d15,#0
	ld.hu	d15,[a15]0
.L145:
	sha	d15,#-4
.L146:
	extr.u	d5,d15,#0,#8
.L99:
	mov	d4,d8
.L100:
	call	uart_write_byte
.L101:
	j	.L30
.L27:
.L30:
.L29:
.L28:
	add	d10,#1
.L22:
	mov	d15,#128
.L147:
	jlt.u	d10,d15,.L23
.L148:
	ret
.L65:
	
__tsl1401_send_data_function_end:
	.size	tsl1401_send_data,__tsl1401_send_data_function_end-tsl1401_send_data
.L50:
	; End of function
	
	.sdecl	'.text.zf_device_tsl1401.tsl1401_init',code,cluster('tsl1401_init')
	.sect	'.text.zf_device_tsl1401.tsl1401_init'
	.align	2
	
	.global	tsl1401_init
; Function tsl1401_init
.L36:
tsl1401_init:	.type	func
	mov	d4,#4
.L153:
	mov	d5,#0
	call	adc_init
.L154:
	mov	d4,#5
.L155:
	mov	d5,#0
	call	adc_init
.L156:
	mov	d4,#0
.L157:
	mov	d5,#1
.L158:
	mov	d6,#0
.L159:
	mov	d7,#3
	call	gpio_init
.L160:
	mov	d4,#1
.L161:
	mov	d5,#1
.L162:
	mov	d6,#0
.L163:
	mov	d7,#3
	call	gpio_init
.L164:
	mov	d4,#3
	mov	d5,#10000
	call	pit_init
.L165:
	movh.a	a15,#@his(tsl1401_init_state)
	lea	a15,[a15]@los(tsl1401_init_state)
.L166:
	mov	d15,#1
.L167:
	st.b	[a15],d15
.L168:
	ret
.L70:
	
__tsl1401_init_function_end:
	.size	tsl1401_init,__tsl1401_init_function_end-tsl1401_init
.L55:
	; End of function
	
	.sdecl	'.bss.zf_device_tsl1401.tsl1401_data',data,cluster('tsl1401_data')
	.sect	'.bss.zf_device_tsl1401.tsl1401_data'
	.global	tsl1401_data
	.align	2
tsl1401_data:	.type	object
	.size	tsl1401_data,512
	.space	512
	.sdecl	'.data.zf_device_tsl1401.tsl1401_init_state',data,cluster('tsl1401_init_state')
	.sect	'.data.zf_device_tsl1401.tsl1401_init_state'
tsl1401_init_state:	.type	object
	.size	tsl1401_init_state,1
	.space	1
	.sdecl	'.bss.zf_device_tsl1401.tsl1401_finish_flag',data,cluster('tsl1401_finish_flag')
	.sect	'.bss.zf_device_tsl1401.tsl1401_finish_flag'
	.global	tsl1401_finish_flag
tsl1401_finish_flag:	.type	object
	.size	tsl1401_finish_flag,1
	.space	1
	.calls	'tsl1401_collect_pit_handler','get_port'
	.calls	'tsl1401_collect_pit_handler','adc_convert'
	.calls	'tsl1401_send_data','uart_write_byte'
	.calls	'tsl1401_init','adc_init'
	.calls	'tsl1401_init','gpio_init'
	.calls	'tsl1401_init','pit_init'
	.calls	'tsl1401_collect_pit_handler','',0
	.calls	'tsl1401_send_data','',0
	.extern	adc_convert
	.extern	adc_init
	.extern	get_port
	.extern	gpio_init
	.extern	pit_init
	.extern	uart_write_byte
	.calls	'tsl1401_init','',0
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L38:
	.word	103002
	.half	3
	.word	.L39
	.byte	4
.L37:
	.byte	1
	.byte	'../libraries/zf_device/zf_device_tsl1401.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L40
	.byte	2,1,1,3
	.word	205
	.byte	4
	.byte	'Ifx__jump_and_link',0,3,1,61,17,1,1,5
	.byte	'fun',0,1,61,43
	.word	208
	.byte	6,0,7
	.byte	'__fract',0,4,128,1,7
	.byte	'float',0,4,4,8
	.byte	'Ifx__float_to_fract',0,3,1,152,2,18
	.word	253
	.byte	1,1,5
	.byte	'a',0,1,152,2,44
	.word	265
	.byte	6,0,4
	.byte	'Ifx__stopPerfCounters',0,3,1,172,2,17,1,1,6,0,7
	.byte	'unsigned long long int',0,8,7,9
	.byte	'void',0,3
	.word	377
	.byte	8
	.byte	'__ld64',0,3,2,135,1,19
	.word	351
	.byte	1,1,5
	.byte	'addr',0,2,135,1,32
	.word	383
	.byte	6,0,4
	.byte	'__st64',0,3,2,143,1,17,1,1,5
	.byte	'addr',0,2,143,1,30
	.word	383
	.byte	5
	.byte	'value',0,2,143,1,43
	.word	351
	.byte	6,0,7
	.byte	'unsigned int',0,4,7,7
	.byte	'int',0,4,5,7
	.byte	'unsigned int',0,4,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON0_Bits',0,4,241,8,16,4,11
	.byte	'ENDINIT',0,4
	.word	492
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	492
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	492
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	492
	.byte	16,0,2,35,0,0,12,4,247,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	508
	.byte	4,2,35,0,0
.L63:
	.byte	7
	.byte	'unsigned char',0,1,8,7
	.byte	'unsigned short int',0,2,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON1_Bits',0,4,250,8,16,4,11
	.byte	'reserved_0',0,1
	.word	644
	.byte	2,6,2,35,0,11
	.byte	'IR0',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	644
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	644
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	644
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	644
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	644
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	661
	.byte	16,0,2,35,2,0,12,4,255,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	683
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_WDTCPU_SR_Bits',0,4,137,9,16,4,11
	.byte	'AE',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	644
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	644
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	644
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	644
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	644
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	661
	.byte	16,0,2,35,2,0,12,4,135,15,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	927
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_WDTCPU',0,4,175,15,25,12,13
	.byte	'CON0',0
	.word	604
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	887
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	1118
	.byte	4,2,35,8,0,14
	.word	1158
	.byte	3
	.word	1221
	.byte	4
	.byte	'IfxScuWdt_clearCpuEndinitInline',0,3,3,181,3,17,1,1,5
	.byte	'watchdog',0,3,181,3,65
	.word	1226
	.byte	5
	.byte	'password',0,3,181,3,82
	.word	661
	.byte	6,0,4
	.byte	'IfxScuWdt_setCpuEndinitInline',0,3,3,140,4,17,1,1,5
	.byte	'watchdog',0,3,140,4,63
	.word	1226
	.byte	5
	.byte	'password',0,3,140,4,80
	.word	661
	.byte	6,0,8
	.byte	'IfxScuWdt_getCpuWatchdogPasswordInline',0,3,3,227,3,19
	.word	661
	.byte	1,1,5
	.byte	'watchdog',0,3,227,3,74
	.word	1226
	.byte	6,0,10
	.byte	'_Ifx_P_OUT_Bits',0,6,143,3,16,4,11
	.byte	'P0',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	644
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	644
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	644
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	644
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	644
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	644
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	644
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	644
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	644
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	644
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	644
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	661
	.byte	16,0,2,35,2,0,12,6,181,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1456
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMR_Bits',0,6,169,2,16,4,11
	.byte	'PS0',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	644
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	644
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	644
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	644
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	644
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	644
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	644
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	644
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	644
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	644
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	644
	.byte	1,0,2,35,1,11
	.byte	'PCL0',0,1
	.word	644
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	644
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	644
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	644
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	644
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	644
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	644
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	644
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	644
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	644
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	644
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	644
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	644
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	644
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	644
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	644
	.byte	1,0,2,35,3,0,12,6,133,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1772
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ID_Bits',0,6,110,16,4,11
	.byte	'MODREV',0,1
	.word	644
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	644
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	661
	.byte	16,0,2,35,2,0,12,6,148,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2343
	.byte	4,2,35,0,0,15,4
	.word	644
	.byte	16,3,0,10
	.byte	'_Ifx_P_IOCR0_Bits',0,6,140,1,16,4,11
	.byte	'reserved_0',0,1
	.word	644
	.byte	3,5,2,35,0,11
	.byte	'PC0',0,1
	.word	644
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	644
	.byte	3,5,2,35,1,11
	.byte	'PC1',0,1
	.word	644
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	644
	.byte	3,5,2,35,2,11
	.byte	'PC2',0,1
	.word	644
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	644
	.byte	3,5,2,35,3,11
	.byte	'PC3',0,1
	.word	644
	.byte	5,0,2,35,3,0,12,6,164,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2471
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR4_Bits',0,6,166,1,16,4,11
	.byte	'reserved_0',0,1
	.word	644
	.byte	3,5,2,35,0,11
	.byte	'PC4',0,1
	.word	644
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	644
	.byte	3,5,2,35,1,11
	.byte	'PC5',0,1
	.word	644
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	644
	.byte	3,5,2,35,2,11
	.byte	'PC6',0,1
	.word	644
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	644
	.byte	3,5,2,35,3,11
	.byte	'PC7',0,1
	.word	644
	.byte	5,0,2,35,3,0,12,6,180,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2686
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR8_Bits',0,6,179,1,16,4,11
	.byte	'reserved_0',0,1
	.word	644
	.byte	3,5,2,35,0,11
	.byte	'PC8',0,1
	.word	644
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	644
	.byte	3,5,2,35,1,11
	.byte	'PC9',0,1
	.word	644
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	644
	.byte	3,5,2,35,2,11
	.byte	'PC10',0,1
	.word	644
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	644
	.byte	3,5,2,35,3,11
	.byte	'PC11',0,1
	.word	644
	.byte	5,0,2,35,3,0,12,6,188,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2901
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR12_Bits',0,6,153,1,16,4,11
	.byte	'reserved_0',0,1
	.word	644
	.byte	3,5,2,35,0,11
	.byte	'PC12',0,1
	.word	644
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	644
	.byte	3,5,2,35,1,11
	.byte	'PC13',0,1
	.word	644
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	644
	.byte	3,5,2,35,2,11
	.byte	'PC14',0,1
	.word	644
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	644
	.byte	3,5,2,35,3,11
	.byte	'PC15',0,1
	.word	644
	.byte	5,0,2,35,3,0,12,6,172,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3118
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IN_Bits',0,6,118,16,4,11
	.byte	'P0',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	644
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	644
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	644
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	644
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	644
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	644
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	644
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	644
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	644
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	644
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	644
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	661
	.byte	16,0,2,35,2,0,12,6,156,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3338
	.byte	4,2,35,0,0,15,24
	.word	644
	.byte	16,23,0,10
	.byte	'_Ifx_P_PDR0_Bits',0,6,205,3,16,4,11
	.byte	'PD0',0,1
	.word	644
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	644
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	644
	.byte	1,0,2,35,0,11
	.byte	'PD2',0,1
	.word	644
	.byte	3,5,2,35,1,11
	.byte	'PL2',0,1
	.word	644
	.byte	1,4,2,35,1,11
	.byte	'PD3',0,1
	.word	644
	.byte	3,1,2,35,1,11
	.byte	'PL3',0,1
	.word	644
	.byte	1,0,2,35,1,11
	.byte	'PD4',0,1
	.word	644
	.byte	3,5,2,35,2,11
	.byte	'PL4',0,1
	.word	644
	.byte	1,4,2,35,2,11
	.byte	'PD5',0,1
	.word	644
	.byte	3,1,2,35,2,11
	.byte	'PL5',0,1
	.word	644
	.byte	1,0,2,35,2,11
	.byte	'PD6',0,1
	.word	644
	.byte	3,5,2,35,3,11
	.byte	'PL6',0,1
	.word	644
	.byte	1,4,2,35,3,11
	.byte	'PD7',0,1
	.word	644
	.byte	3,1,2,35,3,11
	.byte	'PL7',0,1
	.word	644
	.byte	1,0,2,35,3,0,12,6,205,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3661
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PDR1_Bits',0,6,226,3,16,4,11
	.byte	'PD8',0,1
	.word	644
	.byte	3,5,2,35,0,11
	.byte	'PL8',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'PD9',0,1
	.word	644
	.byte	3,1,2,35,0,11
	.byte	'PL9',0,1
	.word	644
	.byte	1,0,2,35,0,11
	.byte	'PD10',0,1
	.word	644
	.byte	3,5,2,35,1,11
	.byte	'PL10',0,1
	.word	644
	.byte	1,4,2,35,1,11
	.byte	'PD11',0,1
	.word	644
	.byte	3,1,2,35,1,11
	.byte	'PL11',0,1
	.word	644
	.byte	1,0,2,35,1,11
	.byte	'PD12',0,1
	.word	644
	.byte	3,5,2,35,2,11
	.byte	'PL12',0,1
	.word	644
	.byte	1,4,2,35,2,11
	.byte	'PD13',0,1
	.word	644
	.byte	3,1,2,35,2,11
	.byte	'PL13',0,1
	.word	644
	.byte	1,0,2,35,2,11
	.byte	'PD14',0,1
	.word	644
	.byte	3,5,2,35,3,11
	.byte	'PL14',0,1
	.word	644
	.byte	1,4,2,35,3,11
	.byte	'PD15',0,1
	.word	644
	.byte	3,1,2,35,3,11
	.byte	'PL15',0,1
	.word	644
	.byte	1,0,2,35,3,0,12,6,213,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3965
	.byte	4,2,35,0,0,15,8
	.word	644
	.byte	16,7,0,10
	.byte	'_Ifx_P_ESR_Bits',0,6,88,16,4,11
	.byte	'EN0',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	644
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	644
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	644
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	644
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	644
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	644
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	644
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	644
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	644
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	644
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	644
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	661
	.byte	16,0,2,35,2,0,12,6,140,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4290
	.byte	4,2,35,0,0,15,12
	.word	644
	.byte	16,11,0,10
	.byte	'_Ifx_P_PDISC_Bits',0,6,183,3,16,4,11
	.byte	'PDIS0',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'PDIS2',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'PDIS3',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'PDIS4',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'PDIS5',0,1
	.word	644
	.byte	1,2,2,35,0,11
	.byte	'PDIS6',0,1
	.word	644
	.byte	1,1,2,35,0,11
	.byte	'PDIS7',0,1
	.word	644
	.byte	1,0,2,35,0,11
	.byte	'PDIS8',0,1
	.word	644
	.byte	1,7,2,35,1,11
	.byte	'PDIS9',0,1
	.word	644
	.byte	1,6,2,35,1,11
	.byte	'PDIS10',0,1
	.word	644
	.byte	1,5,2,35,1,11
	.byte	'PDIS11',0,1
	.word	644
	.byte	1,4,2,35,1,11
	.byte	'PDIS12',0,1
	.word	644
	.byte	1,3,2,35,1,11
	.byte	'PDIS13',0,1
	.word	644
	.byte	1,2,2,35,1,11
	.byte	'PDIS14',0,1
	.word	644
	.byte	1,1,2,35,1,11
	.byte	'PDIS15',0,1
	.word	644
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	661
	.byte	16,0,2,35,2,0,12,6,197,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4630
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PCSR_Bits',0,6,165,3,16,4,11
	.byte	'SEL0',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'SEL1',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'SEL2',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'SEL3',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'SEL4',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'SEL5',0,1
	.word	644
	.byte	1,2,2,35,0,11
	.byte	'SEL6',0,1
	.word	644
	.byte	1,1,2,35,0,11
	.byte	'SEL7',0,1
	.word	644
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	644
	.byte	2,6,2,35,1,11
	.byte	'SEL10',0,1
	.word	644
	.byte	1,5,2,35,1,11
	.byte	'SEL11',0,1
	.word	644
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	469
	.byte	19,1,2,35,0,11
	.byte	'LCK',0,1
	.word	644
	.byte	1,0,2,35,3,0,12,6,189,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4996
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR0_Bits',0,6,206,2,16,4,11
	.byte	'PS0',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	469
	.byte	28,0,2,35,0,0,12,6,149,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5282
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR4_Bits',0,6,227,2,16,4,11
	.byte	'reserved_0',0,1
	.word	644
	.byte	4,4,2,35,0,11
	.byte	'PS4',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	644
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	644
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	644
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	469
	.byte	24,0,2,35,0,0,12,6,165,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5429
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR8_Bits',0,6,238,2,16,4,11
	.byte	'reserved_0',0,1
	.word	644
	.byte	8,0,2,35,0,11
	.byte	'PS8',0,1
	.word	644
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	644
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	644
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	644
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	469
	.byte	20,0,2,35,0,0,12,6,173,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5598
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR12_Bits',0,6,216,2,16,4,11
	.byte	'reserved_0',0,2
	.word	661
	.byte	12,4,2,35,0,11
	.byte	'PS12',0,1
	.word	644
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	644
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	644
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	644
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	661
	.byte	16,0,2,35,2,0,12,6,157,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5770
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR0_Bits',0,6,232,1,16,4,11
	.byte	'reserved_0',0,2
	.word	661
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	644
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	644
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	644
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	644
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	661
	.byte	12,0,2,35,2,0,12,6,229,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5945
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR4_Bits',0,6,253,1,16,4,11
	.byte	'reserved_0',0,4
	.word	469
	.byte	20,12,2,35,0,11
	.byte	'PCL4',0,1
	.word	644
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	644
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	644
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	644
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	644
	.byte	8,0,2,35,3,0,12,6,245,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6119
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR8_Bits',0,6,136,2,16,4,11
	.byte	'reserved_0',0,4
	.word	469
	.byte	24,8,2,35,0,11
	.byte	'PCL8',0,1
	.word	644
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	644
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	644
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	644
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	644
	.byte	4,0,2,35,3,0,12,6,253,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6293
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR12_Bits',0,6,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	469
	.byte	28,4,2,35,0,11
	.byte	'PCL12',0,1
	.word	644
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	644
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	644
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	644
	.byte	1,0,2,35,3,0,12,6,237,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6469
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR_Bits',0,6,249,2,16,4,11
	.byte	'PS0',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	644
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	644
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	644
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	644
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	644
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	644
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	644
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	644
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	644
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	644
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	644
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	661
	.byte	16,0,2,35,2,0,12,6,141,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6625
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR_Bits',0,6,147,2,16,4,11
	.byte	'reserved_0',0,2
	.word	661
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	644
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	644
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	644
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	644
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	644
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	644
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	644
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	644
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	644
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	644
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	644
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	644
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	644
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	644
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	644
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	644
	.byte	1,0,2,35,3,0,12,6,221,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6958
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR0_Bits',0,6,192,1,16,4,11
	.byte	'reserved_0',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	469
	.byte	30,0,2,35,0,0,12,6,196,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7306
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_Bits',0,6,200,1,16,4,11
	.byte	'reserved_0',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	469
	.byte	30,0,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_P21_Bits',0,6,208,1,16,4,11
	.byte	'RDIS_CTRL',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'RX_DIS',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'TERM',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'LRXTERM',0,1
	.word	644
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	469
	.byte	24,0,2,35,0,0,12,6,204,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7430
	.byte	4,2,35,0,13
	.byte	'B_P21',0
	.word	7514
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR2_Bits',0,6,218,1,16,4,11
	.byte	'reserved_0',0,1
	.word	644
	.byte	8,0,2,35,0,11
	.byte	'LVDSR',0,1
	.word	644
	.byte	1,7,2,35,1,11
	.byte	'LVDSRL',0,1
	.word	644
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	644
	.byte	2,4,2,35,1,11
	.byte	'TDIS_CTRL',0,1
	.word	644
	.byte	1,3,2,35,1,11
	.byte	'TX_DIS',0,1
	.word	644
	.byte	1,2,2,35,1,11
	.byte	'TX_PD',0,1
	.word	644
	.byte	1,1,2,35,1,11
	.byte	'TX_PWDPD',0,1
	.word	644
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	661
	.byte	16,0,2,35,2,0,12,6,213,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7694
	.byte	4,2,35,0,0,15,76
	.word	644
	.byte	16,75,0,10
	.byte	'_Ifx_P_ACCEN1_Bits',0,6,82,16,4,11
	.byte	'reserved_0',0,4
	.word	469
	.byte	32,0,2,35,0,0,12,6,132,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7947
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ACCEN0_Bits',0,6,45,16,4,11
	.byte	'EN0',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	644
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	644
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	644
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	644
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	644
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	644
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	644
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	644
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	644
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	644
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	644
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	644
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	644
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	644
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	644
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	644
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	644
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	644
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	644
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	644
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	644
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	644
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	644
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	644
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	644
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	644
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	644
	.byte	1,0,2,35,3,0,12,6,252,3,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8034
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P',0,6,229,5,25,128,2,13
	.byte	'OUT',0
	.word	1732
	.byte	4,2,35,0,13
	.byte	'OMR',0
	.word	2303
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	2422
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	2462
	.byte	4,2,35,12,13
	.byte	'IOCR0',0
	.word	2646
	.byte	4,2,35,16,13
	.byte	'IOCR4',0
	.word	2861
	.byte	4,2,35,20,13
	.byte	'IOCR8',0
	.word	3078
	.byte	4,2,35,24,13
	.byte	'IOCR12',0
	.word	3298
	.byte	4,2,35,28,13
	.byte	'reserved_20',0
	.word	2462
	.byte	4,2,35,32,13
	.byte	'IN',0
	.word	3612
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	3652
	.byte	24,2,35,40,13
	.byte	'PDR0',0
	.word	3925
	.byte	4,2,35,64,13
	.byte	'PDR1',0
	.word	4241
	.byte	4,2,35,68,13
	.byte	'reserved_48',0
	.word	4281
	.byte	8,2,35,72,13
	.byte	'ESR',0
	.word	4581
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	4621
	.byte	12,2,35,84,13
	.byte	'PDISC',0
	.word	4956
	.byte	4,2,35,96,13
	.byte	'PCSR',0
	.word	5242
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	4281
	.byte	8,2,35,104,13
	.byte	'OMSR0',0
	.word	5389
	.byte	4,2,35,112,13
	.byte	'OMSR4',0
	.word	5558
	.byte	4,2,35,116,13
	.byte	'OMSR8',0
	.word	5730
	.byte	4,2,35,120,13
	.byte	'OMSR12',0
	.word	5905
	.byte	4,2,35,124,13
	.byte	'OMCR0',0
	.word	6079
	.byte	4,3,35,128,1,13
	.byte	'OMCR4',0
	.word	6253
	.byte	4,3,35,132,1,13
	.byte	'OMCR8',0
	.word	6429
	.byte	4,3,35,136,1,13
	.byte	'OMCR12',0
	.word	6585
	.byte	4,3,35,140,1,13
	.byte	'OMSR',0
	.word	6918
	.byte	4,3,35,144,1,13
	.byte	'OMCR',0
	.word	7266
	.byte	4,3,35,148,1,13
	.byte	'reserved_98',0
	.word	4281
	.byte	8,3,35,152,1,13
	.byte	'LPCR0',0
	.word	7390
	.byte	4,3,35,160,1,13
	.byte	'LPCR1',0
	.word	7639
	.byte	4,3,35,164,1,13
	.byte	'LPCR2',0
	.word	7898
	.byte	4,3,35,168,1,13
	.byte	'reserved_A4',0
	.word	7938
	.byte	76,3,35,172,1,13
	.byte	'ACCEN1',0
	.word	7994
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	8561
	.byte	4,3,35,252,1,0,14
	.word	8601
	.byte	3
	.word	9204
	.byte	17,5,83,9,1,18
	.byte	'IfxPort_InputMode_undefined',0,127,18
	.byte	'IfxPort_InputMode_noPullDevice',0,0,18
	.byte	'IfxPort_InputMode_pullDown',0,8,18
	.byte	'IfxPort_InputMode_pullUp',0,16,0,4
	.byte	'IfxPort_setPinModeInput',0,3,5,196,4,17,1,1,5
	.byte	'port',0,5,196,4,48
	.word	9209
	.byte	5
	.byte	'pinIndex',0,5,196,4,60
	.word	644
	.byte	5
	.byte	'mode',0,5,196,4,88
	.word	9214
	.byte	6,0,17,5,134,1,9,1,18
	.byte	'IfxPort_OutputMode_pushPull',0,128,1,18
	.byte	'IfxPort_OutputMode_openDrain',0,192,1,0,17,5,120,9,1,18
	.byte	'IfxPort_OutputIdx_general',0,128,1,18
	.byte	'IfxPort_OutputIdx_alt1',0,136,1,18
	.byte	'IfxPort_OutputIdx_alt2',0,144,1,18
	.byte	'IfxPort_OutputIdx_alt3',0,152,1,18
	.byte	'IfxPort_OutputIdx_alt4',0,160,1,18
	.byte	'IfxPort_OutputIdx_alt5',0,168,1,18
	.byte	'IfxPort_OutputIdx_alt6',0,176,1,18
	.byte	'IfxPort_OutputIdx_alt7',0,184,1,0,4
	.byte	'IfxPort_setPinModeOutput',0,3,5,202,4,17,1,1,5
	.byte	'port',0,5,202,4,49
	.word	9209
	.byte	5
	.byte	'pinIndex',0,5,202,4,61
	.word	644
	.byte	5
	.byte	'mode',0,5,202,4,90
	.word	9419
	.byte	5
	.byte	'index',0,5,202,4,114
	.word	9489
	.byte	6,0,17,5,172,1,9,4,18
	.byte	'IfxPort_State_notChanged',0,0,18
	.byte	'IfxPort_State_high',0,1,18
	.byte	'IfxPort_State_low',0,128,128,4,18
	.byte	'IfxPort_State_toggled',0,129,128,4,0,4
	.byte	'IfxPort_setPinState',0,3,5,208,4,17,1,1,5
	.byte	'port',0,5,208,4,44
	.word	9209
	.byte	5
	.byte	'pinIndex',0,5,208,4,56
	.word	644
	.byte	5
	.byte	'action',0,5,208,4,80
	.word	9802
	.byte	6,0,17,8,156,1,9,1,18
	.byte	'IfxCpu_ResourceCpu_0',0,0,18
	.byte	'IfxCpu_ResourceCpu_1',0,1,18
	.byte	'IfxCpu_ResourceCpu_none',0,2,0,8
	.byte	'IfxCpu_getCoreIndex',0,3,7,141,6,31
	.word	9983
	.byte	1,1,6,0,8
	.byte	'IfxCpu_areInterruptsEnabled',0,3,7,139,5,20
	.word	644
	.byte	1,1,6,0,8
	.byte	'IfxCpu_disableInterrupts',0,3,7,147,5,20
	.word	644
	.byte	1,1,19,6,0,0,4
	.byte	'IfxCpu_enableInterrupts',0,3,7,157,5,17,1,1,6,0,4
	.byte	'IfxCpu_forceDisableInterrupts',0,3,7,225,5,17,1,1,6,0,4
	.byte	'IfxCpu_restoreInterrupts',0,3,7,168,7,17,1,1,5
	.byte	'enabled',0,7,168,7,50
	.word	644
	.byte	6,0,7
	.byte	'unsigned long int',0,4,7,8
	.byte	'IfxCpu_getPerformanceCounter',0,3,7,161,6,19
	.word	10305
	.byte	1,1,5
	.byte	'address',0,7,161,6,55
	.word	661
	.byte	6,0,8
	.byte	'IfxCpu_getPerformanceCounterStickyOverflow',0,3,7,190,6,20
	.word	644
	.byte	1,1,5
	.byte	'address',0,7,190,6,70
	.word	661
	.byte	6,0,4
	.byte	'IfxCpu_updatePerformanceCounter',0,3,7,172,8,17,1,1,5
	.byte	'address',0,7,172,8,56
	.word	10305
	.byte	5
	.byte	'count',0,7,172,8,72
	.word	10305
	.byte	19,6,0,0,8
	.byte	'IfxScuCcu_getStmFrequency',0,3,9,226,8,20
	.word	265
	.byte	1,1,6,0,10
	.byte	'_Ifx_SRC_SRCR_Bits',0,11,45,16,4,11
	.byte	'SRPN',0,1
	.word	644
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	644
	.byte	2,6,2,35,1,11
	.byte	'SRE',0,1
	.word	644
	.byte	1,5,2,35,1,11
	.byte	'TOS',0,1
	.word	644
	.byte	2,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	644
	.byte	3,0,2,35,1,11
	.byte	'ECC',0,1
	.word	644
	.byte	6,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	644
	.byte	2,0,2,35,2,11
	.byte	'SRR',0,1
	.word	644
	.byte	1,7,2,35,3,11
	.byte	'CLRR',0,1
	.word	644
	.byte	1,6,2,35,3,11
	.byte	'SETR',0,1
	.word	644
	.byte	1,5,2,35,3,11
	.byte	'IOV',0,1
	.word	644
	.byte	1,4,2,35,3,11
	.byte	'IOVCLR',0,1
	.word	644
	.byte	1,3,2,35,3,11
	.byte	'SWS',0,1
	.word	644
	.byte	1,2,2,35,3,11
	.byte	'SWSCLR',0,1
	.word	644
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	644
	.byte	1,0,2,35,3,0,12,11,70,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10576
	.byte	4,2,35,0,0,14
	.word	10866
	.byte	3
	.word	10905
	.byte	4
	.byte	'IfxSrc_clearRequest',0,3,10,250,1,17,1,1,5
	.byte	'src',0,10,250,1,60
	.word	10910
	.byte	6,0,10
	.byte	'_Ifx_ASCLIN_CLC_Bits',0,13,118,16,4,11
	.byte	'DISR',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'DISS',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'EDIS',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	469
	.byte	28,0,2,35,0,0,12,13,207,3,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10958
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_IOCR_Bits',0,13,169,2,16,4,11
	.byte	'ALTI',0,1
	.word	644
	.byte	3,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'DEPTH',0,2
	.word	661
	.byte	6,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	644
	.byte	6,0,2,35,1,11
	.byte	'CTS',0,1
	.word	644
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	661
	.byte	7,7,2,35,2,11
	.byte	'RCPOL',0,1
	.word	644
	.byte	1,6,2,35,3,11
	.byte	'CPOL',0,1
	.word	644
	.byte	1,5,2,35,3,11
	.byte	'SPOL',0,1
	.word	644
	.byte	1,4,2,35,3,11
	.byte	'LB',0,1
	.word	644
	.byte	1,3,2,35,3,11
	.byte	'CTSEN',0,1
	.word	644
	.byte	1,2,2,35,3,11
	.byte	'RXM',0,1
	.word	644
	.byte	1,1,2,35,3,11
	.byte	'TXM',0,1
	.word	644
	.byte	1,0,2,35,3,0,12,13,151,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11117
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_ID_Bits',0,13,161,2,16,4,11
	.byte	'MODREV',0,1
	.word	644
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	644
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	661
	.byte	16,0,2,35,2,0,12,13,143,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11412
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_TXFIFOCON_Bits',0,13,149,3,16,4,11
	.byte	'FLUSH',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'ENO',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	644
	.byte	4,2,2,35,0,11
	.byte	'INW',0,1
	.word	644
	.byte	2,0,2,35,0,11
	.byte	'INTLEVEL',0,1
	.word	644
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	644
	.byte	4,0,2,35,1,11
	.byte	'FILL',0,1
	.word	644
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,2
	.word	661
	.byte	11,0,2,35,2,0,12,13,247,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11537
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_RXFIFOCON_Bits',0,13,129,3,16,4,11
	.byte	'FLUSH',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'ENI',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	644
	.byte	4,2,2,35,0,11
	.byte	'OUTW',0,1
	.word	644
	.byte	2,0,2,35,0,11
	.byte	'INTLEVEL',0,1
	.word	644
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	644
	.byte	4,0,2,35,1,11
	.byte	'FILL',0,1
	.word	644
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,2
	.word	661
	.byte	10,1,2,35,2,11
	.byte	'BUF',0,1
	.word	644
	.byte	1,0,2,35,3,0,12,13,231,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11762
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_BITCON_Bits',0,13,88,16,4,11
	.byte	'PRESCALER',0,2
	.word	661
	.byte	12,4,2,35,0,11
	.byte	'reserved_12',0,1
	.word	644
	.byte	4,0,2,35,1,11
	.byte	'OVERSAMPLING',0,1
	.word	644
	.byte	4,4,2,35,2,11
	.byte	'reserved_20',0,1
	.word	644
	.byte	4,0,2,35,2,11
	.byte	'SAMPLEPOINT',0,1
	.word	644
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	644
	.byte	3,1,2,35,3,11
	.byte	'SM',0,1
	.word	644
	.byte	1,0,2,35,3,0,12,13,183,3,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12003
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_FRAMECON_Bits',0,13,145,2,16,4,11
	.byte	'reserved_0',0,1
	.word	644
	.byte	6,2,2,35,0,11
	.byte	'IDLE',0,2
	.word	661
	.byte	3,7,2,35,0,11
	.byte	'STOP',0,1
	.word	644
	.byte	3,4,2,35,1,11
	.byte	'LEAD',0,1
	.word	644
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	644
	.byte	1,0,2,35,1,11
	.byte	'MODE',0,1
	.word	644
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	661
	.byte	10,4,2,35,2,11
	.byte	'MSB',0,1
	.word	644
	.byte	1,3,2,35,3,11
	.byte	'CEN',0,1
	.word	644
	.byte	1,2,2,35,3,11
	.byte	'PEN',0,1
	.word	644
	.byte	1,1,2,35,3,11
	.byte	'ODD',0,1
	.word	644
	.byte	1,0,2,35,3,0,12,13,135,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12224
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_DATCON_Bits',0,13,136,1,16,4,11
	.byte	'DATLEN',0,1
	.word	644
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	661
	.byte	9,3,2,35,0,11
	.byte	'HO',0,1
	.word	644
	.byte	1,2,2,35,1,11
	.byte	'RM',0,1
	.word	644
	.byte	1,1,2,35,1,11
	.byte	'CSM',0,1
	.word	644
	.byte	1,0,2,35,1,11
	.byte	'RESPONSE',0,1
	.word	644
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	644
	.byte	8,0,2,35,3,0,12,13,223,3,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12489
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_BRG_Bits',0,13,109,16,4,11
	.byte	'DENOMINATOR',0,2
	.word	661
	.byte	12,4,2,35,0,11
	.byte	'reserved_12',0,1
	.word	644
	.byte	4,0,2,35,1,11
	.byte	'NUMERATOR',0,2
	.word	661
	.byte	12,4,2,35,2,11
	.byte	'reserved_28',0,1
	.word	644
	.byte	4,0,2,35,3,0,12,13,199,3,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12686
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_BRD_Bits',0,13,100,16,4,11
	.byte	'LOWERLIMIT',0,1
	.word	644
	.byte	8,0,2,35,0,11
	.byte	'UPPERLIMIT',0,1
	.word	644
	.byte	8,0,2,35,1,11
	.byte	'MEASURED',0,2
	.word	661
	.byte	12,4,2,35,2,11
	.byte	'reserved_28',0,1
	.word	644
	.byte	4,0,2,35,3,0,12,13,191,3,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12843
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_LIN_CON_Bits',0,13,216,2,16,4,11
	.byte	'reserved_0',0,4
	.word	469
	.byte	23,9,2,35,0,11
	.byte	'CSI',0,1
	.word	644
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	644
	.byte	1,7,2,35,3,11
	.byte	'CSEN',0,1
	.word	644
	.byte	1,6,2,35,3,11
	.byte	'MS',0,1
	.word	644
	.byte	1,5,2,35,3,11
	.byte	'ABD',0,1
	.word	644
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	644
	.byte	4,0,2,35,3,0,12,13,191,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12997
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_LIN_BTIMER_Bits',0,13,209,2,16,4,11
	.byte	'BREAK',0,1
	.word	644
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	469
	.byte	26,0,2,35,0,0,12,13,183,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13197
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_LIN_HTIMER_Bits',0,13,228,2,16,4,11
	.byte	'HEADER',0,1
	.word	644
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	469
	.byte	24,0,2,35,0,0,12,13,199,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13311
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_LIN',0,13,135,5,25,12,13
	.byte	'CON',0
	.word	13157
	.byte	4,2,35,0,13
	.byte	'BTIMER',0
	.word	13271
	.byte	4,2,35,4,13
	.byte	'HTIMER',0
	.word	13386
	.byte	4,2,35,8,0,14
	.word	13426
	.byte	10
	.byte	'_Ifx_ASCLIN_FLAGS_Bits',0,13,148,1,16,4,11
	.byte	'TH',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'TR',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'RH',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'RR',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'FED',0,1
	.word	644
	.byte	1,2,2,35,0,11
	.byte	'RED',0,1
	.word	644
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	661
	.byte	6,3,2,35,0,11
	.byte	'TWRQ',0,1
	.word	644
	.byte	1,2,2,35,1,11
	.byte	'THRQ',0,1
	.word	644
	.byte	1,1,2,35,1,11
	.byte	'TRRQ',0,1
	.word	644
	.byte	1,0,2,35,1,11
	.byte	'PE',0,1
	.word	644
	.byte	1,7,2,35,2,11
	.byte	'TC',0,1
	.word	644
	.byte	1,6,2,35,2,11
	.byte	'FE',0,1
	.word	644
	.byte	1,5,2,35,2,11
	.byte	'HT',0,1
	.word	644
	.byte	1,4,2,35,2,11
	.byte	'RT',0,1
	.word	644
	.byte	1,3,2,35,2,11
	.byte	'BD',0,1
	.word	644
	.byte	1,2,2,35,2,11
	.byte	'LP',0,1
	.word	644
	.byte	1,1,2,35,2,11
	.byte	'LA',0,1
	.word	644
	.byte	1,0,2,35,2,11
	.byte	'LC',0,1
	.word	644
	.byte	1,7,2,35,3,11
	.byte	'CE',0,1
	.word	644
	.byte	1,6,2,35,3,11
	.byte	'RFO',0,1
	.word	644
	.byte	1,5,2,35,3,11
	.byte	'RFU',0,1
	.word	644
	.byte	1,4,2,35,3,11
	.byte	'RFL',0,1
	.word	644
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	644
	.byte	1,2,2,35,3,11
	.byte	'TFO',0,1
	.word	644
	.byte	1,1,2,35,3,11
	.byte	'TFL',0,1
	.word	644
	.byte	1,0,2,35,3,0,12,13,231,3,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13499
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_FLAGSSET_Bits',0,13,241,1,16,4,11
	.byte	'THS',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'TRS',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'RHS',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'RRS',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'FEDS',0,1
	.word	644
	.byte	1,2,2,35,0,11
	.byte	'REDS',0,1
	.word	644
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	661
	.byte	6,3,2,35,0,11
	.byte	'TWRQS',0,1
	.word	644
	.byte	1,2,2,35,1,11
	.byte	'THRQS',0,1
	.word	644
	.byte	1,1,2,35,1,11
	.byte	'TRRQS',0,1
	.word	644
	.byte	1,0,2,35,1,11
	.byte	'PES',0,1
	.word	644
	.byte	1,7,2,35,2,11
	.byte	'TCS',0,1
	.word	644
	.byte	1,6,2,35,2,11
	.byte	'FES',0,1
	.word	644
	.byte	1,5,2,35,2,11
	.byte	'HTS',0,1
	.word	644
	.byte	1,4,2,35,2,11
	.byte	'RTS',0,1
	.word	644
	.byte	1,3,2,35,2,11
	.byte	'BDS',0,1
	.word	644
	.byte	1,2,2,35,2,11
	.byte	'LPS',0,1
	.word	644
	.byte	1,1,2,35,2,11
	.byte	'LAS',0,1
	.word	644
	.byte	1,0,2,35,2,11
	.byte	'LCS',0,1
	.word	644
	.byte	1,7,2,35,3,11
	.byte	'CES',0,1
	.word	644
	.byte	1,6,2,35,3,11
	.byte	'RFOS',0,1
	.word	644
	.byte	1,5,2,35,3,11
	.byte	'RFUS',0,1
	.word	644
	.byte	1,4,2,35,3,11
	.byte	'RFLS',0,1
	.word	644
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	644
	.byte	1,2,2,35,3,11
	.byte	'TFOS',0,1
	.word	644
	.byte	1,1,2,35,3,11
	.byte	'TFLS',0,1
	.word	644
	.byte	1,0,2,35,3,0,12,13,255,3,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13985
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_FLAGSCLEAR_Bits',0,13,180,1,16,4,11
	.byte	'THC',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'TRC',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'RHC',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'RRC',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'FEDC',0,1
	.word	644
	.byte	1,2,2,35,0,11
	.byte	'REDC',0,1
	.word	644
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	661
	.byte	6,3,2,35,0,11
	.byte	'TWRQC',0,1
	.word	644
	.byte	1,2,2,35,1,11
	.byte	'THRQC',0,1
	.word	644
	.byte	1,1,2,35,1,11
	.byte	'TRRQC',0,1
	.word	644
	.byte	1,0,2,35,1,11
	.byte	'PEC',0,1
	.word	644
	.byte	1,7,2,35,2,11
	.byte	'TCC',0,1
	.word	644
	.byte	1,6,2,35,2,11
	.byte	'FEC',0,1
	.word	644
	.byte	1,5,2,35,2,11
	.byte	'HTC',0,1
	.word	644
	.byte	1,4,2,35,2,11
	.byte	'RTC',0,1
	.word	644
	.byte	1,3,2,35,2,11
	.byte	'BDC',0,1
	.word	644
	.byte	1,2,2,35,2,11
	.byte	'LPC',0,1
	.word	644
	.byte	1,1,2,35,2,11
	.byte	'LAC',0,1
	.word	644
	.byte	1,0,2,35,2,11
	.byte	'LCC',0,1
	.word	644
	.byte	1,7,2,35,3,11
	.byte	'CEC',0,1
	.word	644
	.byte	1,6,2,35,3,11
	.byte	'RFOC',0,1
	.word	644
	.byte	1,5,2,35,3,11
	.byte	'RFUC',0,1
	.word	644
	.byte	1,4,2,35,3,11
	.byte	'RFLC',0,1
	.word	644
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	644
	.byte	1,2,2,35,3,11
	.byte	'TFOC',0,1
	.word	644
	.byte	1,1,2,35,3,11
	.byte	'TFLC',0,1
	.word	644
	.byte	1,0,2,35,3,0,12,13,239,3,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14498
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_FLAGSENABLE_Bits',0,13,212,1,16,4,11
	.byte	'THE',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'TRE',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'RHE',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'RRE',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'FEDE',0,1
	.word	644
	.byte	1,2,2,35,0,11
	.byte	'REDE',0,1
	.word	644
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	661
	.byte	9,0,2,35,0,11
	.byte	'PEE',0,1
	.word	644
	.byte	1,7,2,35,2,11
	.byte	'TCE',0,1
	.word	644
	.byte	1,6,2,35,2,11
	.byte	'FEE',0,1
	.word	644
	.byte	1,5,2,35,2,11
	.byte	'HTE',0,1
	.word	644
	.byte	1,4,2,35,2,11
	.byte	'RTE',0,1
	.word	644
	.byte	1,3,2,35,2,11
	.byte	'BDE',0,1
	.word	644
	.byte	1,2,2,35,2,11
	.byte	'LPE',0,1
	.word	644
	.byte	1,1,2,35,2,11
	.byte	'ABE',0,1
	.word	644
	.byte	1,0,2,35,2,11
	.byte	'LCE',0,1
	.word	644
	.byte	1,7,2,35,3,11
	.byte	'CEE',0,1
	.word	644
	.byte	1,6,2,35,3,11
	.byte	'RFOE',0,1
	.word	644
	.byte	1,5,2,35,3,11
	.byte	'RFUE',0,1
	.word	644
	.byte	1,4,2,35,3,11
	.byte	'RFLE',0,1
	.word	644
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	644
	.byte	1,2,2,35,3,11
	.byte	'TFOE',0,1
	.word	644
	.byte	1,1,2,35,3,11
	.byte	'TFLE',0,1
	.word	644
	.byte	1,0,2,35,3,0,12,13,247,3,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15013
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_TXDATA_Bits',0,13,143,3,16,4,11
	.byte	'DATA',0,4
	.word	469
	.byte	32,0,2,35,0,0,12,13,239,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15478
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_RXDATA_Bits',0,13,245,2,16,4,11
	.byte	'DATA',0,4
	.word	469
	.byte	32,0,2,35,0,0,12,13,215,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15565
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_CSR_Bits',0,13,128,1,16,4,11
	.byte	'CLKSEL',0,1
	.word	644
	.byte	5,3,2,35,0,11
	.byte	'reserved_5',0,4
	.word	469
	.byte	26,1,2,35,0,11
	.byte	'CON',0,1
	.word	644
	.byte	1,0,2,35,3,0,12,13,215,3,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15652
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_RXDATAD_Bits',0,13,251,2,16,4,11
	.byte	'DATA',0,4
	.word	469
	.byte	32,0,2,35,0,0,12,13,223,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15775
	.byte	4,2,35,0,0,15,148,1
	.word	644
	.byte	16,147,1,0,10
	.byte	'_Ifx_ASCLIN_OCS_Bits',0,13,235,2,16,4,11
	.byte	'reserved_0',0,4
	.word	469
	.byte	24,8,2,35,0,11
	.byte	'SUS',0,1
	.word	644
	.byte	4,4,2,35,3,11
	.byte	'SUS_P',0,1
	.word	644
	.byte	1,3,2,35,3,11
	.byte	'SUSSTA',0,1
	.word	644
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	644
	.byte	2,0,2,35,3,0,12,13,207,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15874
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_KRSTCLR_Bits',0,13,202,2,16,4,11
	.byte	'CLR',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	469
	.byte	31,0,2,35,0,0,12,13,175,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16037
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_KRST1_Bits',0,13,195,2,16,4,11
	.byte	'RST',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	469
	.byte	31,0,2,35,0,0,12,13,167,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16146
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_KRST0_Bits',0,13,187,2,16,4,11
	.byte	'RST',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'RSTSTAT',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	469
	.byte	30,0,2,35,0,0,12,13,159,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16253
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_ACCEN1_Bits',0,13,82,16,4,11
	.byte	'reserved_0',0,4
	.word	469
	.byte	32,0,2,35,0,0,12,13,175,3,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16379
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_ACCEN0_Bits',0,13,45,16,4,11
	.byte	'EN0',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	644
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	644
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	644
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	644
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	644
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	644
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	644
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	644
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	644
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	644
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	644
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	644
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	644
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	644
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	644
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	644
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	644
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	644
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	644
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	644
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	644
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	644
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	644
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	644
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	644
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	644
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	644
	.byte	1,0,2,35,3,0,12,13,167,3,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16471
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN',0,13,153,5,25,128,2,13
	.byte	'CLC',0
	.word	11077
	.byte	4,2,35,0,13
	.byte	'IOCR',0
	.word	11372
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	11497
	.byte	4,2,35,8,13
	.byte	'TXFIFOCON',0
	.word	11722
	.byte	4,2,35,12,13
	.byte	'RXFIFOCON',0
	.word	11963
	.byte	4,2,35,16,13
	.byte	'BITCON',0
	.word	12184
	.byte	4,2,35,20,13
	.byte	'FRAMECON',0
	.word	12449
	.byte	4,2,35,24,13
	.byte	'DATCON',0
	.word	12646
	.byte	4,2,35,28,13
	.byte	'BRG',0
	.word	12803
	.byte	4,2,35,32,13
	.byte	'BRD',0
	.word	12957
	.byte	4,2,35,36,13
	.byte	'LIN',0
	.word	13494
	.byte	12,2,35,40,13
	.byte	'FLAGS',0
	.word	13945
	.byte	4,2,35,52,13
	.byte	'FLAGSSET',0
	.word	14458
	.byte	4,2,35,56,13
	.byte	'FLAGSCLEAR',0
	.word	14973
	.byte	4,2,35,60,13
	.byte	'FLAGSENABLE',0
	.word	15438
	.byte	4,2,35,64,13
	.byte	'TXDATA',0
	.word	15525
	.byte	4,2,35,68,13
	.byte	'RXDATA',0
	.word	15612
	.byte	4,2,35,72,13
	.byte	'CSR',0
	.word	15735
	.byte	4,2,35,76,13
	.byte	'RXDATAD',0
	.word	15823
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	15863
	.byte	148,1,2,35,84,13
	.byte	'OCS',0
	.word	15997
	.byte	4,3,35,232,1,13
	.byte	'KRSTCLR',0
	.word	16106
	.byte	4,3,35,236,1,13
	.byte	'KRST1',0
	.word	16213
	.byte	4,3,35,240,1,13
	.byte	'KRST0',0
	.word	16339
	.byte	4,3,35,244,1,13
	.byte	'ACCEN1',0
	.word	16431
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	17003
	.byte	4,3,35,252,1,0,14
	.word	17043
	.byte	3
	.word	17485
	.byte	4
	.byte	'IfxAsclin_enableCts',0,3,12,228,13,17,1,1,5
	.byte	'asclin',0,12,228,13,49
	.word	17490
	.byte	5
	.byte	'enable',0,12,228,13,65
	.word	644
	.byte	6,0,17,12,123,9,1,18
	.byte	'IfxAsclin_CtsInputSelect_0',0,0,18
	.byte	'IfxAsclin_CtsInputSelect_1',0,1,18
	.byte	'IfxAsclin_CtsInputSelect_2',0,2,18
	.byte	'IfxAsclin_CtsInputSelect_3',0,3,0,4
	.byte	'IfxAsclin_setCtsInput',0,3,12,169,17,17,1,1,5
	.byte	'asclin',0,12,169,17,51
	.word	17490
	.byte	5
	.byte	'ctsi',0,12,169,17,84
	.word	17557
	.byte	6,0,17,12,181,2,9,1,18
	.byte	'IfxAsclin_RxInputSelect_0',0,0,18
	.byte	'IfxAsclin_RxInputSelect_1',0,1,18
	.byte	'IfxAsclin_RxInputSelect_2',0,2,18
	.byte	'IfxAsclin_RxInputSelect_3',0,3,18
	.byte	'IfxAsclin_RxInputSelect_4',0,4,18
	.byte	'IfxAsclin_RxInputSelect_5',0,5,18
	.byte	'IfxAsclin_RxInputSelect_6',0,6,18
	.byte	'IfxAsclin_RxInputSelect_7',0,7,0,4
	.byte	'IfxAsclin_setRxInput',0,3,12,191,18,17,1,1,5
	.byte	'asclin',0,12,191,18,50
	.word	17490
	.byte	5
	.byte	'alti',0,12,191,18,82
	.word	17741
	.byte	6,0,7
	.byte	'short int',0,2,5,7
	.byte	'long int',0,4,5,20,14,60,9,12,13
	.byte	'count',0
	.word	18033
	.byte	2,2,35,0,13
	.byte	'readerWaitx',0
	.word	18046
	.byte	4,2,35,2,13
	.byte	'writerWaitx',0
	.word	18046
	.byte	4,2,35,6,13
	.byte	'maxcount',0
	.word	18033
	.byte	2,2,35,10,0,14
	.word	644
	.byte	14
	.word	644
	.byte	10
	.byte	'_Fifo',0,14,73,16,28,13
	.byte	'buffer',0
	.word	383
	.byte	4,2,35,0,13
	.byte	'shared',0
	.word	18058
	.byte	12,2,35,4,13
	.byte	'startIndex',0
	.word	18033
	.byte	2,2,35,16,13
	.byte	'endIndex',0
	.word	18033
	.byte	2,2,35,18,13
	.byte	'size',0
	.word	18033
	.byte	2,2,35,20,13
	.byte	'elementSize',0
	.word	18033
	.byte	2,2,35,22,13
	.byte	'eventReader',0
	.word	18139
	.byte	1,2,35,24,13
	.byte	'eventWriter',0
	.word	18144
	.byte	1,2,35,25,0,3
	.word	18149
	.byte	8
	.byte	'Ifx_Fifo_readCount',0,3,14,206,1,22
	.word	18033
	.byte	1,1,5
	.byte	'fifo',0,14,206,1,51
	.word	18308
	.byte	6,0,10
	.byte	'_Ifx_STM_CLC_Bits',0,16,100,16,4,11
	.byte	'DISR',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'DISS',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'EDIS',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	469
	.byte	28,0,2,35,0,0,12,16,149,2,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18360
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_ID_Bits',0,16,142,1,16,4,11
	.byte	'MODREV',0,1
	.word	644
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	644
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	661
	.byte	16,0,2,35,2,0,12,16,181,2,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18516
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM0_Bits',0,16,192,1,16,4,11
	.byte	'STM31_0',0,4
	.word	469
	.byte	32,0,2,35,0,0,12,16,229,2,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18638
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM1_Bits',0,16,204,1,16,4,11
	.byte	'STM35_4',0,4
	.word	469
	.byte	32,0,2,35,0,0,12,16,245,2,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18723
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM2_Bits',0,16,210,1,16,4,11
	.byte	'STM39_8',0,4
	.word	469
	.byte	32,0,2,35,0,0,12,16,253,2,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18808
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM3_Bits',0,16,216,1,16,4,11
	.byte	'STM43_12',0,4
	.word	469
	.byte	32,0,2,35,0,0,12,16,133,3,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18893
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM4_Bits',0,16,222,1,16,4,11
	.byte	'STM47_16',0,4
	.word	469
	.byte	32,0,2,35,0,0,12,16,141,3,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18979
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM5_Bits',0,16,228,1,16,4,11
	.byte	'STM51_20',0,4
	.word	469
	.byte	32,0,2,35,0,0,12,16,149,3,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19065
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM6_Bits',0,16,234,1,16,4,11
	.byte	'STM63_32',0,4
	.word	469
	.byte	32,0,2,35,0,0,12,16,157,3,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19151
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_CAP_Bits',0,16,88,16,4,11
	.byte	'STMCAP63_32',0,4
	.word	469
	.byte	32,0,2,35,0,0,12,16,133,2,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19237
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_CMP_Bits',0,16,123,16,4,11
	.byte	'CMPVAL',0,4
	.word	469
	.byte	32,0,2,35,0,0,12,16,165,2,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19324
	.byte	4,2,35,0,0,15,8
	.word	19366
	.byte	16,1,0,10
	.byte	'_Ifx_STM_CMCON_Bits',0,16,110,16,4,11
	.byte	'MSIZE0',0,1
	.word	644
	.byte	5,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	644
	.byte	3,0,2,35,0,11
	.byte	'MSTART0',0,1
	.word	644
	.byte	5,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	644
	.byte	3,0,2,35,1,11
	.byte	'MSIZE1',0,1
	.word	644
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	644
	.byte	3,0,2,35,2,11
	.byte	'MSTART1',0,1
	.word	644
	.byte	5,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	644
	.byte	3,0,2,35,3,0,12,16,157,2,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19415
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_ICR_Bits',0,16,129,1,16,4,11
	.byte	'CMP0EN',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'CMP0IR',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'CMP0OS',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'CMP1EN',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'CMP1IR',0,1
	.word	644
	.byte	1,2,2,35,0,11
	.byte	'CMP1OS',0,1
	.word	644
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,4
	.word	469
	.byte	25,0,2,35,0,0,12,16,173,2,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19646
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_ISCR_Bits',0,16,150,1,16,4,11
	.byte	'CMP0IRR',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'CMP0IRS',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'CMP1IRR',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'CMP1IRS',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	469
	.byte	28,0,2,35,0,0,12,16,189,2,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19863
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM0SV_Bits',0,16,198,1,16,4,11
	.byte	'STM31_0',0,4
	.word	469
	.byte	32,0,2,35,0,0,12,16,237,2,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20027
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_CAPSV_Bits',0,16,94,16,4,11
	.byte	'STMCAP63_32',0,4
	.word	469
	.byte	32,0,2,35,0,0,12,16,141,2,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20114
	.byte	4,2,35,0,0,15,144,1
	.word	644
	.byte	16,143,1,0,10
	.byte	'_Ifx_STM_OCS_Bits',0,16,182,1,16,4,11
	.byte	'reserved_0',0,4
	.word	469
	.byte	24,8,2,35,0,11
	.byte	'SUS',0,1
	.word	644
	.byte	4,4,2,35,3,11
	.byte	'SUS_P',0,1
	.word	644
	.byte	1,3,2,35,3,11
	.byte	'SUSSTA',0,1
	.word	644
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	644
	.byte	2,0,2,35,3,0,12,16,221,2,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20214
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_KRSTCLR_Bits',0,16,175,1,16,4,11
	.byte	'CLR',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	469
	.byte	31,0,2,35,0,0,12,16,213,2,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20374
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_KRST1_Bits',0,16,168,1,16,4,11
	.byte	'RST',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	469
	.byte	31,0,2,35,0,0,12,16,205,2,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20480
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_KRST0_Bits',0,16,160,1,16,4,11
	.byte	'RST',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'RSTSTAT',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	469
	.byte	30,0,2,35,0,0,12,16,197,2,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20584
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_ACCEN1_Bits',0,16,82,16,4,11
	.byte	'reserved_0',0,4
	.word	469
	.byte	32,0,2,35,0,0,12,16,253,1,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20707
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_ACCEN0_Bits',0,16,45,16,4,11
	.byte	'EN0',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	644
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	644
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	644
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	644
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	644
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	644
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	644
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	644
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	644
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	644
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	644
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	644
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	644
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	644
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	644
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	644
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	644
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	644
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	644
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	644
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	644
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	644
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	644
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	644
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	644
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	644
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	644
	.byte	1,0,2,35,3,0,12,16,245,1,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20796
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM',0,16,173,3,25,128,2,13
	.byte	'CLC',0
	.word	18476
	.byte	4,2,35,0,13
	.byte	'reserved_4',0
	.word	2462
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	18598
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	2462
	.byte	4,2,35,12,13
	.byte	'TIM0',0
	.word	18683
	.byte	4,2,35,16,13
	.byte	'TIM1',0
	.word	18768
	.byte	4,2,35,20,13
	.byte	'TIM2',0
	.word	18853
	.byte	4,2,35,24,13
	.byte	'TIM3',0
	.word	18939
	.byte	4,2,35,28,13
	.byte	'TIM4',0
	.word	19025
	.byte	4,2,35,32,13
	.byte	'TIM5',0
	.word	19111
	.byte	4,2,35,36,13
	.byte	'TIM6',0
	.word	19197
	.byte	4,2,35,40,13
	.byte	'CAP',0
	.word	19284
	.byte	4,2,35,44,13
	.byte	'CMP',0
	.word	19406
	.byte	8,2,35,48,13
	.byte	'CMCON',0
	.word	19606
	.byte	4,2,35,56,13
	.byte	'ICR',0
	.word	19823
	.byte	4,2,35,60,13
	.byte	'ISCR',0
	.word	19987
	.byte	4,2,35,64,13
	.byte	'reserved_44',0
	.word	4621
	.byte	12,2,35,68,13
	.byte	'TIM0SV',0
	.word	20074
	.byte	4,2,35,80,13
	.byte	'CAPSV',0
	.word	20163
	.byte	4,2,35,84,13
	.byte	'reserved_58',0
	.word	20203
	.byte	144,1,2,35,88,13
	.byte	'OCS',0
	.word	20334
	.byte	4,3,35,232,1,13
	.byte	'KRSTCLR',0
	.word	20440
	.byte	4,3,35,236,1,13
	.byte	'KRST1',0
	.word	20544
	.byte	4,3,35,240,1,13
	.byte	'KRST0',0
	.word	20667
	.byte	4,3,35,244,1,13
	.byte	'ACCEN1',0
	.word	20756
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	21325
	.byte	4,3,35,252,1,0,14
	.word	21365
	.byte	3
	.word	21785
	.byte	8
	.byte	'IfxStm_get',0,3,15,162,4,19
	.word	351
	.byte	1,1,5
	.byte	'stm',0,15,162,4,39
	.word	21790
	.byte	6,0,8
	.byte	'IfxStm_getFrequency',0,3,15,179,4,20
	.word	265
	.byte	1,1,5
	.byte	'stm',0,15,179,4,49
	.word	21790
	.byte	19,6,6,0,0,8
	.byte	'IfxStm_getLower',0,3,15,190,4,19
	.word	10305
	.byte	1,1,5
	.byte	'stm',0,15,190,4,44
	.word	21790
	.byte	6,0,8
	.byte	'disableInterrupts',0,3,17,108,20
	.word	644
	.byte	1,1,19,6,0,0,4
	.byte	'restoreInterrupts',0,3,17,142,1,17,1,1,5
	.byte	'enabled',0,17,142,1,43
	.word	644
	.byte	19,6,0,0,7
	.byte	'long long int',0,8,5,8
	.byte	'getDeadLine',0,3,17,164,2,25
	.word	22006
	.byte	1,1,5
	.byte	'timeout',0,17,164,2,50
	.word	22006
	.byte	19,6,0,0,8
	.byte	'isDeadLine',0,3,17,211,2,20
	.word	644
	.byte	1,1,5
	.byte	'deadLine',0,17,211,2,44
	.word	22006
	.byte	19,6,0,0,8
	.byte	'now',0,3,17,221,1,25
	.word	22006
	.byte	1,1,19,6,6,6,0,0,8
	.byte	'nowWithoutCriticalSection',0,3,17,240,1,25
	.word	22006
	.byte	1,1,19,6,0,0,17,18,41,9,1,18
	.byte	'ADC0_CH0_A0',0,0,18
	.byte	'ADC0_CH1_A1',0,1,18
	.byte	'ADC0_CH2_A2',0,2,18
	.byte	'ADC0_CH3_A3',0,3,18
	.byte	'ADC0_CH4_A4',0,4,18
	.byte	'ADC0_CH5_A5',0,5,18
	.byte	'ADC0_CH6_A6',0,6,18
	.byte	'ADC0_CH7_A7',0,7,18
	.byte	'ADC0_CH8_A8',0,8,18
	.byte	'ADC0_CH10_A10',0,10,18
	.byte	'ADC0_CH11_A11',0,11,18
	.byte	'ADC0_CH12_A12',0,12,18
	.byte	'ADC0_CH13_A13',0,13,18
	.byte	'ADC1_CH0_A16',0,16,18
	.byte	'ADC1_CH1_A17',0,17,18
	.byte	'ADC1_CH4_A20',0,20,18
	.byte	'ADC1_CH5_A21',0,21,18
	.byte	'ADC1_CH8_A24',0,24,18
	.byte	'ADC1_CH9_A25',0,25,18
	.byte	'ADC2_CH3_A35',0,35,18
	.byte	'ADC2_CH4_A36',0,36,18
	.byte	'ADC2_CH5_A37',0,37,18
	.byte	'ADC2_CH6_A38',0,38,18
	.byte	'ADC2_CH7_A39',0,39,18
	.byte	'ADC2_CH10_A44',0,42,18
	.byte	'ADC2_CH11_A45',0,43,18
	.byte	'ADC2_CH12_A46',0,44,18
	.byte	'ADC2_CH13_A47',0,45,18
	.byte	'ADC2_CH14_A48',0,46,18
	.byte	'ADC2_CH15_A49',0,47,0,21
	.byte	'adc_convert',0,18,89,9
	.word	661
	.byte	1,1,1,1,5
	.byte	'vadc_chn',0,18,89,51
	.word	22177
	.byte	0,17,18,81,9,1,18
	.byte	'ADC_8BIT',0,0,18
	.byte	'ADC_10BIT',0,1,18
	.byte	'ADC_12BIT',0,2,0,22
	.byte	'adc_init',0,18,91,9,1,1,1,1,5
	.byte	'vadc_chn',0,18,91,51
	.word	22177
	.byte	5
	.byte	'resolution',0,18,91,81
	.word	22676
	.byte	0,23
	.word	213
	.byte	24
	.word	239
	.byte	6,0,23
	.word	274
	.byte	24
	.word	306
	.byte	6,0,23
	.word	319
	.byte	6,0,23
	.word	388
	.byte	24
	.word	407
	.byte	6,0,23
	.word	423
	.byte	24
	.word	438
	.byte	24
	.word	452
	.byte	6,0,23
	.word	1231
	.byte	24
	.word	1271
	.byte	24
	.word	1289
	.byte	6,0,23
	.word	1309
	.byte	24
	.word	1347
	.byte	24
	.word	1365
	.byte	6,0,23
	.word	1385
	.byte	24
	.word	1436
	.byte	6,0,23
	.word	9339
	.byte	24
	.word	9371
	.byte	24
	.word	9385
	.byte	24
	.word	9403
	.byte	6,0,23
	.word	9706
	.byte	24
	.word	9739
	.byte	24
	.word	9753
	.byte	24
	.word	9771
	.byte	24
	.word	9785
	.byte	6,0,23
	.word	9905
	.byte	24
	.word	9933
	.byte	24
	.word	9947
	.byte	24
	.word	9965
	.byte	6,0,17,19,42,9,2,18
	.byte	'P00_0',0,0,18
	.byte	'P00_1',0,1,18
	.byte	'P00_2',0,2,18
	.byte	'P00_3',0,3,18
	.byte	'P00_4',0,4,18
	.byte	'P00_5',0,5,18
	.byte	'P00_6',0,6,18
	.byte	'P00_7',0,7,18
	.byte	'P00_8',0,8,18
	.byte	'P00_9',0,9,18
	.byte	'P00_10',0,10,18
	.byte	'P00_11',0,11,18
	.byte	'P00_12',0,12,18
	.byte	'P00_13',0,13,18
	.byte	'P00_14',0,14,18
	.byte	'P00_15',0,15,18
	.byte	'P02_0',0,192,0,18
	.byte	'P02_1',0,193,0,18
	.byte	'P02_2',0,194,0,18
	.byte	'P02_3',0,195,0,18
	.byte	'P02_4',0,196,0,18
	.byte	'P02_5',0,197,0,18
	.byte	'P02_6',0,198,0,18
	.byte	'P02_7',0,199,0,18
	.byte	'P02_8',0,200,0,18
	.byte	'P02_9',0,201,0,18
	.byte	'P02_10',0,202,0,18
	.byte	'P02_11',0,203,0,18
	.byte	'P02_12',0,204,0,18
	.byte	'P02_13',0,205,0,18
	.byte	'P02_14',0,206,0,18
	.byte	'P02_15',0,207,0,18
	.byte	'P10_0',0,192,2,18
	.byte	'P10_1',0,193,2,18
	.byte	'P10_2',0,194,2,18
	.byte	'P10_3',0,195,2,18
	.byte	'P10_4',0,196,2,18
	.byte	'P10_5',0,197,2,18
	.byte	'P10_6',0,198,2,18
	.byte	'P10_7',0,199,2,18
	.byte	'P10_8',0,200,2,18
	.byte	'P10_9',0,201,2,18
	.byte	'P10_10',0,202,2,18
	.byte	'P10_11',0,203,2,18
	.byte	'P10_12',0,204,2,18
	.byte	'P10_13',0,205,2,18
	.byte	'P10_14',0,206,2,18
	.byte	'P10_15',0,207,2,18
	.byte	'P11_0',0,224,2,18
	.byte	'P11_1',0,225,2,18
	.byte	'P11_2',0,226,2,18
	.byte	'P11_3',0,227,2,18
	.byte	'P11_4',0,228,2,18
	.byte	'P11_5',0,229,2,18
	.byte	'P11_6',0,230,2,18
	.byte	'P11_7',0,231,2,18
	.byte	'P11_8',0,232,2,18
	.byte	'P11_9',0,233,2,18
	.byte	'P11_10',0,234,2,18
	.byte	'P11_11',0,235,2,18
	.byte	'P11_12',0,236,2,18
	.byte	'P11_13',0,237,2,18
	.byte	'P11_14',0,238,2,18
	.byte	'P11_15',0,239,2,18
	.byte	'P13_0',0,160,3,18
	.byte	'P13_1',0,161,3,18
	.byte	'P13_2',0,162,3,18
	.byte	'P13_3',0,163,3,18
	.byte	'P13_4',0,164,3,18
	.byte	'P13_5',0,165,3,18
	.byte	'P13_6',0,166,3,18
	.byte	'P13_7',0,167,3,18
	.byte	'P13_8',0,168,3,18
	.byte	'P13_9',0,169,3,18
	.byte	'P13_10',0,170,3,18
	.byte	'P13_11',0,171,3,18
	.byte	'P13_12',0,172,3,18
	.byte	'P13_13',0,173,3,18
	.byte	'P13_14',0,174,3,18
	.byte	'P13_15',0,175,3,18
	.byte	'P14_0',0,192,3,18
	.byte	'P14_1',0,193,3,18
	.byte	'P14_2',0,194,3,18
	.byte	'P14_3',0,195,3,18
	.byte	'P14_4',0,196,3,18
	.byte	'P14_5',0,197,3,18
	.byte	'P14_6',0,198,3,18
	.byte	'P14_7',0,199,3,18
	.byte	'P14_8',0,200,3,18
	.byte	'P14_9',0,201,3,18
	.byte	'P14_10',0,202,3,18
	.byte	'P14_11',0,203,3,18
	.byte	'P14_12',0,204,3,18
	.byte	'P14_13',0,205,3,18
	.byte	'P14_14',0,206,3,18
	.byte	'P14_15',0,207,3,18
	.byte	'P15_0',0,224,3,18
	.byte	'P15_1',0,225,3,18
	.byte	'P15_2',0,226,3,18
	.byte	'P15_3',0,227,3,18
	.byte	'P15_4',0,228,3,18
	.byte	'P15_5',0,229,3,18
	.byte	'P15_6',0,230,3,18
	.byte	'P15_7',0,231,3,18
	.byte	'P15_8',0,232,3,18
	.byte	'P15_9',0,233,3,18
	.byte	'P15_10',0,234,3,18
	.byte	'P15_11',0,235,3,18
	.byte	'P15_12',0,236,3,18
	.byte	'P15_13',0,237,3,18
	.byte	'P15_14',0,238,3,18
	.byte	'P15_15',0,239,3,18
	.byte	'P20_0',0,128,5,18
	.byte	'P20_1',0,129,5,18
	.byte	'P20_2',0,130,5,18
	.byte	'P20_3',0,131,5,18
	.byte	'P20_4',0,132,5,18
	.byte	'P20_5',0,133,5,18
	.byte	'P20_6',0,134,5,18
	.byte	'P20_7',0,135,5,18
	.byte	'P20_8',0,136,5,18
	.byte	'P20_9',0,137,5,18
	.byte	'P20_10',0,138,5,18
	.byte	'P20_11',0,139,5,18
	.byte	'P20_12',0,140,5,18
	.byte	'P20_13',0,141,5,18
	.byte	'P20_14',0,142,5,18
	.byte	'P20_15',0,143,5,18
	.byte	'P21_0',0,160,5,18
	.byte	'P21_1',0,161,5,18
	.byte	'P21_2',0,162,5,18
	.byte	'P21_3',0,163,5,18
	.byte	'P21_4',0,164,5,18
	.byte	'P21_5',0,165,5,18
	.byte	'P21_6',0,166,5,18
	.byte	'P21_7',0,167,5,18
	.byte	'P21_8',0,168,5,18
	.byte	'P21_9',0,169,5,18
	.byte	'P21_10',0,170,5,18
	.byte	'P21_11',0,171,5,18
	.byte	'P21_12',0,172,5,18
	.byte	'P21_13',0,173,5,18
	.byte	'P21_14',0,174,5,18
	.byte	'P21_15',0,175,5,18
	.byte	'P22_0',0,192,5,18
	.byte	'P22_1',0,193,5,18
	.byte	'P22_2',0,194,5,18
	.byte	'P22_3',0,195,5,18
	.byte	'P22_4',0,196,5,18
	.byte	'P22_5',0,197,5,18
	.byte	'P22_6',0,198,5,18
	.byte	'P22_7',0,199,5,18
	.byte	'P22_8',0,200,5,18
	.byte	'P22_9',0,201,5,18
	.byte	'P22_10',0,202,5,18
	.byte	'P22_11',0,203,5,18
	.byte	'P22_12',0,204,5,18
	.byte	'P22_13',0,205,5,18
	.byte	'P22_14',0,206,5,18
	.byte	'P22_15',0,207,5,18
	.byte	'P23_0',0,224,5,18
	.byte	'P23_1',0,225,5,18
	.byte	'P23_2',0,226,5,18
	.byte	'P23_3',0,227,5,18
	.byte	'P23_4',0,228,5,18
	.byte	'P23_5',0,229,5,18
	.byte	'P23_6',0,230,5,18
	.byte	'P23_7',0,231,5,18
	.byte	'P23_8',0,232,5,18
	.byte	'P23_9',0,233,5,18
	.byte	'P23_10',0,234,5,18
	.byte	'P23_11',0,235,5,18
	.byte	'P23_12',0,236,5,18
	.byte	'P23_13',0,237,5,18
	.byte	'P23_14',0,238,5,18
	.byte	'P23_15',0,239,5,18
	.byte	'P32_0',0,128,8,18
	.byte	'P32_1',0,129,8,18
	.byte	'P32_2',0,130,8,18
	.byte	'P32_3',0,131,8,18
	.byte	'P32_4',0,132,8,18
	.byte	'P32_5',0,133,8,18
	.byte	'P32_6',0,134,8,18
	.byte	'P32_7',0,135,8,18
	.byte	'P32_8',0,136,8,18
	.byte	'P32_9',0,137,8,18
	.byte	'P32_10',0,138,8,18
	.byte	'P32_11',0,139,8,18
	.byte	'P32_12',0,140,8,18
	.byte	'P32_13',0,141,8,18
	.byte	'P32_14',0,142,8,18
	.byte	'P32_15',0,143,8,18
	.byte	'P33_0',0,160,8,18
	.byte	'P33_1',0,161,8,18
	.byte	'P33_2',0,162,8,18
	.byte	'P33_3',0,163,8,18
	.byte	'P33_4',0,164,8,18
	.byte	'P33_5',0,165,8,18
	.byte	'P33_6',0,166,8,18
	.byte	'P33_7',0,167,8,18
	.byte	'P33_8',0,168,8,18
	.byte	'P33_9',0,169,8,18
	.byte	'P33_10',0,170,8,18
	.byte	'P33_11',0,171,8,18
	.byte	'P33_12',0,172,8,18
	.byte	'P33_13',0,173,8,18
	.byte	'P33_14',0,174,8,18
	.byte	'P33_15',0,175,8,0,21
	.byte	'get_port',0,19,114,13
	.word	9209
	.byte	1,1,1,1,5
	.byte	'pin',0,19,114,56
	.word	22948
	.byte	0,17,19,91,9,1,18
	.byte	'GPI',0,0,18
	.byte	'GPO',0,1,0,17,19,103,9,1,18
	.byte	'GPI_FLOATING_IN',0,0,18
	.byte	'GPI_PULL_UP',0,1,18
	.byte	'GPI_PULL_DOWN',0,2,18
	.byte	'GPO_PUSH_PULL',0,3,18
	.byte	'GPO_OPEN_DTAIN',0,4,0,22
	.byte	'gpio_init',0,19,143,1,7,1,1,1,1,5
	.byte	'pin',0,19,143,1,40
	.word	22948
	.byte	5
	.byte	'dir',0,19,143,1,59
	.word	24922
	.byte	5
	.byte	'dat',0,19,143,1,70
	.word	644
	.byte	5
	.byte	'pinconf',0,19,143,1,90
	.word	24940
	.byte	0,17,20,41,9,1,18
	.byte	'CCU60_CH0',0,0,18
	.byte	'CCU60_CH1',0,1,18
	.byte	'CCU61_CH0',0,2,18
	.byte	'CCU61_CH1',0,3,0,22
	.byte	'pit_init',0,20,58,6,1,1,1,1,5
	.byte	'pit_index',0,20,58,46
	.word	25103
	.byte	5
	.byte	'time',0,20,58,64
	.word	10305
	.byte	0,23
	.word	10062
	.byte	6,0,23
	.word	10096
	.byte	6,0,23
	.word	10138
	.byte	19,25
	.word	10096
	.byte	26
	.word	10136
	.byte	0,6,0,0,23
	.word	10179
	.byte	6,0,23
	.word	10213
	.byte	6,0,23
	.word	10253
	.byte	24
	.word	10286
	.byte	6,0,23
	.word	10326
	.byte	24
	.word	10367
	.byte	6,0,23
	.word	10386
	.byte	24
	.word	10441
	.byte	6,0,23
	.word	10460
	.byte	24
	.word	10500
	.byte	24
	.word	10517
	.byte	19,6,0,0,23
	.word	10536
	.byte	6,0,23
	.word	10915
	.byte	24
	.word	10943
	.byte	6,0,23
	.word	17495
	.byte	24
	.word	17523
	.byte	24
	.word	17539
	.byte	6,0,23
	.word	17679
	.byte	24
	.word	17709
	.byte	24
	.word	17725
	.byte	6,0,23
	.word	17972
	.byte	24
	.word	18001
	.byte	24
	.word	18017
	.byte	6,0,23
	.word	18313
	.byte	24
	.word	18344
	.byte	6,0,23
	.word	21795
	.byte	24
	.word	21818
	.byte	6,0,23
	.word	21833
	.byte	24
	.word	21865
	.byte	19,19,25
	.word	10536
	.byte	26
	.word	10574
	.byte	0,0,6,0,0,23
	.word	21883
	.byte	24
	.word	21911
	.byte	6,0,23
	.word	21926
	.byte	19,25
	.word	10138
	.byte	27
	.word	10175
	.byte	25
	.word	10096
	.byte	26
	.word	10136
	.byte	0,26
	.word	10176
	.byte	0,0,6,0,0,23
	.word	21959
	.byte	24
	.word	21985
	.byte	19,25
	.word	10253
	.byte	24
	.word	10286
	.byte	26
	.word	10303
	.byte	0,6,0,0,23
	.word	22023
	.byte	24
	.word	22047
	.byte	19,25
	.word	22113
	.byte	27
	.word	22129
	.byte	25
	.word	21926
	.byte	27
	.word	21955
	.byte	25
	.word	10138
	.byte	27
	.word	10175
	.byte	25
	.word	10096
	.byte	26
	.word	10136
	.byte	0,26
	.word	10176
	.byte	0,0,26
	.word	21956
	.byte	0,0,26
	.word	22130
	.byte	25
	.word	21959
	.byte	24
	.word	21985
	.byte	27
	.word	22002
	.byte	25
	.word	10253
	.byte	24
	.word	10286
	.byte	26
	.word	10303
	.byte	0,26
	.word	22003
	.byte	0,0,26
	.word	22131
	.byte	25
	.word	21795
	.byte	24
	.word	21818
	.byte	26
	.word	21831
	.byte	0,26
	.word	22132
	.byte	0,0,6,0,0,23
	.word	22068
	.byte	24
	.word	22091
	.byte	19,25
	.word	22113
	.byte	27
	.word	22129
	.byte	25
	.word	21926
	.byte	27
	.word	21955
	.byte	25
	.word	10138
	.byte	27
	.word	10175
	.byte	25
	.word	10096
	.byte	26
	.word	10136
	.byte	0,26
	.word	10176
	.byte	0,0,26
	.word	21956
	.byte	0,0,26
	.word	22130
	.byte	25
	.word	21959
	.byte	24
	.word	21985
	.byte	27
	.word	22002
	.byte	25
	.word	10253
	.byte	24
	.word	10286
	.byte	26
	.word	10303
	.byte	0,26
	.word	22003
	.byte	0,0,26
	.word	22131
	.byte	25
	.word	21795
	.byte	24
	.word	21818
	.byte	26
	.word	21831
	.byte	0,26
	.word	22132
	.byte	0,0,6,0,0,23
	.word	22113
	.byte	19,25
	.word	21926
	.byte	27
	.word	21955
	.byte	25
	.word	10138
	.byte	27
	.word	10175
	.byte	25
	.word	10096
	.byte	26
	.word	10136
	.byte	0,26
	.word	10176
	.byte	0,0,26
	.word	21956
	.byte	0,0,6,25
	.word	21959
	.byte	24
	.word	21985
	.byte	27
	.word	22002
	.byte	25
	.word	10253
	.byte	24
	.word	10286
	.byte	26
	.word	10303
	.byte	0,26
	.word	22003
	.byte	0,0,6,25
	.word	21795
	.byte	24
	.word	21818
	.byte	26
	.word	21831
	.byte	0,6,0,0,23
	.word	22135
	.byte	19,25
	.word	21795
	.byte	24
	.word	21818
	.byte	26
	.word	21831
	.byte	0,6,0,0
.L66:
	.byte	17,21,103,9,1,18
	.byte	'UART_0',0,0,18
	.byte	'UART_1',0,1,18
	.byte	'UART_2',0,2,18
	.byte	'UART_3',0,3,0,28
	.word	644
	.byte	22
	.byte	'uart_write_byte',0,21,118,9,1,1,1,1,5
	.byte	'uartn',0,21,118,62
	.word	25924
	.byte	5
	.byte	'dat',0,21,118,81
	.word	25966
	.byte	0,29
	.byte	'__wchar_t',0,22,1,1
	.word	18033
	.byte	29
	.byte	'__size_t',0,22,1,1
	.word	469
	.byte	29
	.byte	'__ptrdiff_t',0,22,1,1
	.word	485
	.byte	30,1,3
	.word	26077
	.byte	29
	.byte	'__codeptr',0,22,1,1
	.word	26079
	.byte	29
	.byte	'__intptr_t',0,22,1,1
	.word	485
	.byte	29
	.byte	'__uintptr_t',0,22,1,1
	.word	469
	.byte	29
	.byte	'_iob_flag_t',0,23,82,25
	.word	661
	.byte	29
	.byte	'boolean',0,24,101,29
	.word	644
	.byte	29
	.byte	'uint8',0,24,105,29
	.word	644
	.byte	29
	.byte	'uint16',0,24,109,29
	.word	661
	.byte	29
	.byte	'uint32',0,24,113,29
	.word	10305
	.byte	29
	.byte	'uint64',0,24,118,29
	.word	351
	.byte	29
	.byte	'sint16',0,24,126,29
	.word	18033
	.byte	29
	.byte	'sint32',0,24,131,1,29
	.word	18046
	.byte	29
	.byte	'sint64',0,24,138,1,29
	.word	22006
	.byte	29
	.byte	'float32',0,24,167,1,29
	.word	265
	.byte	29
	.byte	'pvoid',0,25,57,28
	.word	383
	.byte	29
	.byte	'Ifx_TickTime',0,25,79,28
	.word	22006
	.byte	29
	.byte	'Ifx_SizeT',0,25,92,16
	.word	18033
	.byte	29
	.byte	'Ifx_Priority',0,25,103,16
	.word	661
	.byte	17,25,130,1,9,1,18
	.byte	'Ifx_RxSel_a',0,0,18
	.byte	'Ifx_RxSel_b',0,1,18
	.byte	'Ifx_RxSel_c',0,2,18
	.byte	'Ifx_RxSel_d',0,3,18
	.byte	'Ifx_RxSel_e',0,4,18
	.byte	'Ifx_RxSel_f',0,5,18
	.byte	'Ifx_RxSel_g',0,6,18
	.byte	'Ifx_RxSel_h',0,7,0,29
	.byte	'Ifx_RxSel',0,25,140,1,3
	.word	26374
	.byte	17,25,164,1,9,1,18
	.byte	'Ifx_DataBufferMode_normal',0,0,18
	.byte	'Ifx_DataBufferMode_timeStampSingle',0,1,0,29
	.byte	'Ifx_DataBufferMode',0,25,169,1,2
	.word	26512
	.byte	7
	.byte	'char',0,1,6,29
	.byte	'int8',0,26,54,29
	.word	26612
	.byte	29
	.byte	'int16',0,26,55,29
	.word	18033
	.byte	29
	.byte	'int32',0,26,56,29
	.word	485
	.byte	29
	.byte	'int64',0,26,57,29
	.word	22006
	.byte	14
	.word	644
	.byte	29
	.byte	'vuint8',0,26,59,29
	.word	26675
	.byte	29
	.byte	'adc_channel_enum',0,18,78,2
	.word	22177
	.byte	29
	.byte	'adc_resolution_enum',0,18,86,2
	.word	22676
	.byte	29
	.byte	'Ifx_P_ACCEN0_Bits',0,6,79,3
	.word	8034
	.byte	29
	.byte	'Ifx_P_ACCEN1_Bits',0,6,85,3
	.word	7947
	.byte	29
	.byte	'Ifx_P_ESR_Bits',0,6,107,3
	.word	4290
	.byte	29
	.byte	'Ifx_P_ID_Bits',0,6,115,3
	.word	2343
	.byte	29
	.byte	'Ifx_P_IN_Bits',0,6,137,1,3
	.word	3338
	.byte	29
	.byte	'Ifx_P_IOCR0_Bits',0,6,150,1,3
	.word	2471
	.byte	29
	.byte	'Ifx_P_IOCR12_Bits',0,6,163,1,3
	.word	3118
	.byte	29
	.byte	'Ifx_P_IOCR4_Bits',0,6,176,1,3
	.word	2686
	.byte	29
	.byte	'Ifx_P_IOCR8_Bits',0,6,189,1,3
	.word	2901
	.byte	29
	.byte	'Ifx_P_LPCR0_Bits',0,6,197,1,3
	.word	7306
	.byte	29
	.byte	'Ifx_P_LPCR1_Bits',0,6,205,1,3
	.word	7430
	.byte	29
	.byte	'Ifx_P_LPCR1_P21_Bits',0,6,215,1,3
	.word	7514
	.byte	29
	.byte	'Ifx_P_LPCR2_Bits',0,6,229,1,3
	.word	7694
	.byte	29
	.byte	'Ifx_P_OMCR0_Bits',0,6,240,1,3
	.word	5945
	.byte	29
	.byte	'Ifx_P_OMCR12_Bits',0,6,250,1,3
	.word	6469
	.byte	29
	.byte	'Ifx_P_OMCR4_Bits',0,6,133,2,3
	.word	6119
	.byte	29
	.byte	'Ifx_P_OMCR8_Bits',0,6,144,2,3
	.word	6293
	.byte	29
	.byte	'Ifx_P_OMCR_Bits',0,6,166,2,3
	.word	6958
	.byte	29
	.byte	'Ifx_P_OMR_Bits',0,6,203,2,3
	.word	1772
	.byte	29
	.byte	'Ifx_P_OMSR0_Bits',0,6,213,2,3
	.word	5282
	.byte	29
	.byte	'Ifx_P_OMSR12_Bits',0,6,224,2,3
	.word	5770
	.byte	29
	.byte	'Ifx_P_OMSR4_Bits',0,6,235,2,3
	.word	5429
	.byte	29
	.byte	'Ifx_P_OMSR8_Bits',0,6,246,2,3
	.word	5598
	.byte	29
	.byte	'Ifx_P_OMSR_Bits',0,6,140,3,3
	.word	6625
	.byte	29
	.byte	'Ifx_P_OUT_Bits',0,6,162,3,3
	.word	1456
	.byte	29
	.byte	'Ifx_P_PCSR_Bits',0,6,180,3,3
	.word	4996
	.byte	29
	.byte	'Ifx_P_PDISC_Bits',0,6,202,3,3
	.word	4630
	.byte	29
	.byte	'Ifx_P_PDR0_Bits',0,6,223,3,3
	.word	3661
	.byte	29
	.byte	'Ifx_P_PDR1_Bits',0,6,244,3,3
	.word	3965
	.byte	29
	.byte	'Ifx_P_ACCEN0',0,6,129,4,3
	.word	8561
	.byte	29
	.byte	'Ifx_P_ACCEN1',0,6,137,4,3
	.word	7994
	.byte	29
	.byte	'Ifx_P_ESR',0,6,145,4,3
	.word	4581
	.byte	29
	.byte	'Ifx_P_ID',0,6,153,4,3
	.word	2422
	.byte	29
	.byte	'Ifx_P_IN',0,6,161,4,3
	.word	3612
	.byte	29
	.byte	'Ifx_P_IOCR0',0,6,169,4,3
	.word	2646
	.byte	29
	.byte	'Ifx_P_IOCR12',0,6,177,4,3
	.word	3298
	.byte	29
	.byte	'Ifx_P_IOCR4',0,6,185,4,3
	.word	2861
	.byte	29
	.byte	'Ifx_P_IOCR8',0,6,193,4,3
	.word	3078
	.byte	29
	.byte	'Ifx_P_LPCR0',0,6,201,4,3
	.word	7390
	.byte	29
	.byte	'Ifx_P_LPCR1',0,6,210,4,3
	.word	7639
	.byte	29
	.byte	'Ifx_P_LPCR2',0,6,218,4,3
	.word	7898
	.byte	29
	.byte	'Ifx_P_OMCR',0,6,226,4,3
	.word	7266
	.byte	29
	.byte	'Ifx_P_OMCR0',0,6,234,4,3
	.word	6079
	.byte	29
	.byte	'Ifx_P_OMCR12',0,6,242,4,3
	.word	6585
	.byte	29
	.byte	'Ifx_P_OMCR4',0,6,250,4,3
	.word	6253
	.byte	29
	.byte	'Ifx_P_OMCR8',0,6,130,5,3
	.word	6429
	.byte	29
	.byte	'Ifx_P_OMR',0,6,138,5,3
	.word	2303
	.byte	29
	.byte	'Ifx_P_OMSR',0,6,146,5,3
	.word	6918
	.byte	29
	.byte	'Ifx_P_OMSR0',0,6,154,5,3
	.word	5389
	.byte	29
	.byte	'Ifx_P_OMSR12',0,6,162,5,3
	.word	5905
	.byte	29
	.byte	'Ifx_P_OMSR4',0,6,170,5,3
	.word	5558
	.byte	29
	.byte	'Ifx_P_OMSR8',0,6,178,5,3
	.word	5730
	.byte	29
	.byte	'Ifx_P_OUT',0,6,186,5,3
	.word	1732
	.byte	29
	.byte	'Ifx_P_PCSR',0,6,194,5,3
	.word	5242
	.byte	29
	.byte	'Ifx_P_PDISC',0,6,202,5,3
	.word	4956
	.byte	29
	.byte	'Ifx_P_PDR0',0,6,210,5,3
	.word	3925
	.byte	29
	.byte	'Ifx_P_PDR1',0,6,218,5,3
	.word	4241
	.byte	14
	.word	8601
	.byte	29
	.byte	'Ifx_P',0,6,139,6,3
	.word	28066
	.byte	17,27,236,10,9,1,18
	.byte	'IfxScu_CCUCON0_CLKSEL_fBack',0,0,18
	.byte	'IfxScu_CCUCON0_CLKSEL_fPll',0,1,0,29
	.byte	'IfxScu_CCUCON0_CLKSEL',0,27,240,10,3
	.word	28086
	.byte	17,27,250,10,9,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy16384',0,0,18
	.byte	'IfxScu_WDTCON1_IR_divBy256',0,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy64',0,2,0,29
	.byte	'IfxScu_WDTCON1_IR',0,27,255,10,3
	.word	28183
	.byte	10
	.byte	'_Ifx_SCU_ACCEN0_Bits',0,4,45,16,4,11
	.byte	'EN0',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	644
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	644
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	644
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	644
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	644
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	644
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	644
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	644
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	644
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	644
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	644
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	644
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	644
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	644
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	644
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	644
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	644
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	644
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	644
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	644
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	644
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	644
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	644
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	644
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	644
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	644
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	644
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_ACCEN0_Bits',0,4,79,3
	.word	28305
	.byte	10
	.byte	'_Ifx_SCU_ACCEN1_Bits',0,4,82,16,4,11
	.byte	'reserved_0',0,4
	.word	469
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_SCU_ACCEN1_Bits',0,4,85,3
	.word	28862
	.byte	10
	.byte	'_Ifx_SCU_ARSTDIS_Bits',0,4,88,16,4,11
	.byte	'STM0DIS',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'STM1DIS',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'STM2DIS',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	469
	.byte	29,0,2,35,0,0,29
	.byte	'Ifx_SCU_ARSTDIS_Bits',0,4,94,3
	.word	28939
	.byte	10
	.byte	'_Ifx_SCU_CCUCON0_Bits',0,4,97,16,4,11
	.byte	'BAUD1DIV',0,1
	.word	644
	.byte	4,4,2,35,0,11
	.byte	'BAUD2DIV',0,1
	.word	644
	.byte	4,0,2,35,0,11
	.byte	'SRIDIV',0,1
	.word	644
	.byte	4,4,2,35,1,11
	.byte	'LPDIV',0,1
	.word	644
	.byte	4,0,2,35,1,11
	.byte	'SPBDIV',0,1
	.word	644
	.byte	4,4,2,35,2,11
	.byte	'FSI2DIV',0,1
	.word	644
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	644
	.byte	2,0,2,35,2,11
	.byte	'FSIDIV',0,1
	.word	644
	.byte	2,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	644
	.byte	2,4,2,35,3,11
	.byte	'CLKSEL',0,1
	.word	644
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	644
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	644
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_CCUCON0_Bits',0,4,111,3
	.word	29075
	.byte	10
	.byte	'_Ifx_SCU_CCUCON1_Bits',0,4,114,16,4,11
	.byte	'CANDIV',0,1
	.word	644
	.byte	4,4,2,35,0,11
	.byte	'ERAYDIV',0,1
	.word	644
	.byte	4,0,2,35,0,11
	.byte	'STMDIV',0,1
	.word	644
	.byte	4,4,2,35,1,11
	.byte	'GTMDIV',0,1
	.word	644
	.byte	4,0,2,35,1,11
	.byte	'ETHDIV',0,1
	.word	644
	.byte	4,4,2,35,2,11
	.byte	'ASCLINFDIV',0,1
	.word	644
	.byte	4,0,2,35,2,11
	.byte	'ASCLINSDIV',0,1
	.word	644
	.byte	4,4,2,35,3,11
	.byte	'INSEL',0,1
	.word	644
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	644
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	644
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_CCUCON1_Bits',0,4,126,3
	.word	29355
	.byte	10
	.byte	'_Ifx_SCU_CCUCON2_Bits',0,4,129,1,16,4,11
	.byte	'BBBDIV',0,1
	.word	644
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	469
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	644
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	644
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_CCUCON2_Bits',0,4,135,1,3
	.word	29593
	.byte	10
	.byte	'_Ifx_SCU_CCUCON3_Bits',0,4,138,1,16,4,11
	.byte	'PLLDIV',0,1
	.word	644
	.byte	6,2,2,35,0,11
	.byte	'PLLSEL',0,1
	.word	644
	.byte	2,0,2,35,0,11
	.byte	'PLLERAYDIV',0,1
	.word	644
	.byte	6,2,2,35,1,11
	.byte	'PLLERAYSEL',0,1
	.word	644
	.byte	2,0,2,35,1,11
	.byte	'SRIDIV',0,1
	.word	644
	.byte	6,2,2,35,2,11
	.byte	'SRISEL',0,1
	.word	644
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	644
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	644
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	644
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	644
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_CCUCON3_Bits',0,4,150,1,3
	.word	29721
	.byte	10
	.byte	'_Ifx_SCU_CCUCON4_Bits',0,4,153,1,16,4,11
	.byte	'SPBDIV',0,1
	.word	644
	.byte	6,2,2,35,0,11
	.byte	'SPBSEL',0,1
	.word	644
	.byte	2,0,2,35,0,11
	.byte	'GTMDIV',0,1
	.word	644
	.byte	6,2,2,35,1,11
	.byte	'GTMSEL',0,1
	.word	644
	.byte	2,0,2,35,1,11
	.byte	'STMDIV',0,1
	.word	644
	.byte	6,2,2,35,2,11
	.byte	'STMSEL',0,1
	.word	644
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	644
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	644
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	644
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	644
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_CCUCON4_Bits',0,4,165,1,3
	.word	29964
	.byte	10
	.byte	'_Ifx_SCU_CCUCON5_Bits',0,4,168,1,16,4,11
	.byte	'MAXDIV',0,1
	.word	644
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	469
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	644
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	644
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_CCUCON5_Bits',0,4,174,1,3
	.word	30199
	.byte	10
	.byte	'_Ifx_SCU_CCUCON6_Bits',0,4,177,1,16,4,11
	.byte	'CPU0DIV',0,1
	.word	644
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	469
	.byte	26,0,2,35,0,0,29
	.byte	'Ifx_SCU_CCUCON6_Bits',0,4,181,1,3
	.word	30327
	.byte	10
	.byte	'_Ifx_SCU_CCUCON7_Bits',0,4,184,1,16,4,11
	.byte	'CPU1DIV',0,1
	.word	644
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	469
	.byte	26,0,2,35,0,0,29
	.byte	'Ifx_SCU_CCUCON7_Bits',0,4,188,1,3
	.word	30427
	.byte	10
	.byte	'_Ifx_SCU_CHIPID_Bits',0,4,191,1,16,4,11
	.byte	'CHREV',0,1
	.word	644
	.byte	6,2,2,35,0,11
	.byte	'CHTEC',0,1
	.word	644
	.byte	2,0,2,35,0,11
	.byte	'CHID',0,1
	.word	644
	.byte	8,0,2,35,1,11
	.byte	'EEA',0,1
	.word	644
	.byte	1,7,2,35,2,11
	.byte	'UCODE',0,1
	.word	644
	.byte	7,0,2,35,2,11
	.byte	'FSIZE',0,1
	.word	644
	.byte	4,4,2,35,3,11
	.byte	'SP',0,1
	.word	644
	.byte	2,2,2,35,3,11
	.byte	'SEC',0,1
	.word	644
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	644
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_CHIPID_Bits',0,4,202,1,3
	.word	30527
	.byte	10
	.byte	'_Ifx_SCU_DTSCON_Bits',0,4,205,1,16,4,11
	.byte	'PWD',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'START',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	644
	.byte	2,4,2,35,0,11
	.byte	'CAL',0,4
	.word	469
	.byte	20,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	644
	.byte	7,1,2,35,3,11
	.byte	'SLCK',0,1
	.word	644
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_DTSCON_Bits',0,4,213,1,3
	.word	30735
	.byte	10
	.byte	'_Ifx_SCU_DTSLIM_Bits',0,4,216,1,16,4,11
	.byte	'LOWER',0,2
	.word	661
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	644
	.byte	5,1,2,35,1,11
	.byte	'LLU',0,1
	.word	644
	.byte	1,0,2,35,1,11
	.byte	'UPPER',0,2
	.word	661
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	644
	.byte	4,2,2,35,3,11
	.byte	'SLCK',0,1
	.word	644
	.byte	1,1,2,35,3,11
	.byte	'UOF',0,1
	.word	644
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_DTSLIM_Bits',0,4,225,1,3
	.word	30900
	.byte	10
	.byte	'_Ifx_SCU_DTSSTAT_Bits',0,4,228,1,16,4,11
	.byte	'RESULT',0,2
	.word	661
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	644
	.byte	4,2,2,35,1,11
	.byte	'RDY',0,1
	.word	644
	.byte	1,1,2,35,1,11
	.byte	'BUSY',0,1
	.word	644
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	661
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_SCU_DTSSTAT_Bits',0,4,235,1,3
	.word	31083
	.byte	10
	.byte	'_Ifx_SCU_EICR_Bits',0,4,238,1,16,4,11
	.byte	'reserved_0',0,1
	.word	644
	.byte	4,4,2,35,0,11
	.byte	'EXIS0',0,1
	.word	644
	.byte	3,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	644
	.byte	1,0,2,35,0,11
	.byte	'FEN0',0,1
	.word	644
	.byte	1,7,2,35,1,11
	.byte	'REN0',0,1
	.word	644
	.byte	1,6,2,35,1,11
	.byte	'LDEN0',0,1
	.word	644
	.byte	1,5,2,35,1,11
	.byte	'EIEN0',0,1
	.word	644
	.byte	1,4,2,35,1,11
	.byte	'INP0',0,1
	.word	644
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	469
	.byte	5,12,2,35,0,11
	.byte	'EXIS1',0,1
	.word	644
	.byte	3,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	644
	.byte	1,0,2,35,2,11
	.byte	'FEN1',0,1
	.word	644
	.byte	1,7,2,35,3,11
	.byte	'REN1',0,1
	.word	644
	.byte	1,6,2,35,3,11
	.byte	'LDEN1',0,1
	.word	644
	.byte	1,5,2,35,3,11
	.byte	'EIEN1',0,1
	.word	644
	.byte	1,4,2,35,3,11
	.byte	'INP1',0,1
	.word	644
	.byte	3,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	644
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EICR_Bits',0,4,129,2,3
	.word	31237
	.byte	10
	.byte	'_Ifx_SCU_EIFR_Bits',0,4,132,2,16,4,11
	.byte	'INTF0',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'INTF1',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'INTF2',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'INTF3',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'INTF4',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'INTF5',0,1
	.word	644
	.byte	1,2,2,35,0,11
	.byte	'INTF6',0,1
	.word	644
	.byte	1,1,2,35,0,11
	.byte	'INTF7',0,1
	.word	644
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	469
	.byte	24,0,2,35,0,0,29
	.byte	'Ifx_SCU_EIFR_Bits',0,4,143,2,3
	.word	31601
	.byte	10
	.byte	'_Ifx_SCU_EMSR_Bits',0,4,146,2,16,4,11
	.byte	'POL',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'MODE',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'ENON',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'PSEL',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	661
	.byte	12,0,2,35,0,11
	.byte	'EMSF',0,1
	.word	644
	.byte	1,7,2,35,2,11
	.byte	'SEMSF',0,1
	.word	644
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	644
	.byte	6,0,2,35,2,11
	.byte	'EMSFM',0,1
	.word	644
	.byte	2,6,2,35,3,11
	.byte	'SEMSFM',0,1
	.word	644
	.byte	2,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	644
	.byte	4,0,2,35,3,0,29
	.byte	'Ifx_SCU_EMSR_Bits',0,4,159,2,3
	.word	31812
	.byte	10
	.byte	'_Ifx_SCU_ESRCFG_Bits',0,4,162,2,16,4,11
	.byte	'reserved_0',0,1
	.word	644
	.byte	7,1,2,35,0,11
	.byte	'EDCON',0,2
	.word	661
	.byte	2,7,2,35,0,11
	.byte	'reserved_9',0,4
	.word	469
	.byte	23,0,2,35,0,0,29
	.byte	'Ifx_SCU_ESRCFG_Bits',0,4,167,2,3
	.word	32064
	.byte	10
	.byte	'_Ifx_SCU_ESROCFG_Bits',0,4,170,2,16,4,11
	.byte	'ARI',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'ARC',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	469
	.byte	30,0,2,35,0,0,29
	.byte	'Ifx_SCU_ESROCFG_Bits',0,4,175,2,3
	.word	32182
	.byte	10
	.byte	'_Ifx_SCU_EVR13CON_Bits',0,4,178,2,16,4,11
	.byte	'reserved_0',0,4
	.word	469
	.byte	28,4,2,35,0,11
	.byte	'EVR13OFF',0,1
	.word	644
	.byte	1,3,2,35,3,11
	.byte	'BPEVR13OFF',0,1
	.word	644
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	644
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	644
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVR13CON_Bits',0,4,185,2,3
	.word	32293
	.byte	10
	.byte	'_Ifx_SCU_EVR33CON_Bits',0,4,188,2,16,4,11
	.byte	'reserved_0',0,4
	.word	469
	.byte	28,4,2,35,0,11
	.byte	'EVR33OFF',0,1
	.word	644
	.byte	1,3,2,35,3,11
	.byte	'BPEVR33OFF',0,1
	.word	644
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	644
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	644
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVR33CON_Bits',0,4,195,2,3
	.word	32456
	.byte	10
	.byte	'_Ifx_SCU_EVRADCSTAT_Bits',0,4,198,2,16,4,11
	.byte	'ADC13V',0,1
	.word	644
	.byte	8,0,2,35,0,11
	.byte	'ADC33V',0,1
	.word	644
	.byte	8,0,2,35,1,11
	.byte	'ADCSWDV',0,1
	.word	644
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	644
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	644
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRADCSTAT_Bits',0,4,205,2,3
	.word	32619
	.byte	10
	.byte	'_Ifx_SCU_EVRDVSTAT_Bits',0,4,208,2,16,4,11
	.byte	'DVS13TRIM',0,1
	.word	644
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	644
	.byte	8,0,2,35,1,11
	.byte	'DVS33TRIM',0,1
	.word	644
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	644
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	644
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRDVSTAT_Bits',0,4,215,2,3
	.word	32777
	.byte	10
	.byte	'_Ifx_SCU_EVRMONCTRL_Bits',0,4,218,2,16,4,11
	.byte	'EVR13OVMOD',0,1
	.word	644
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	644
	.byte	2,4,2,35,0,11
	.byte	'EVR13UVMOD',0,1
	.word	644
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	644
	.byte	2,0,2,35,0,11
	.byte	'EVR33OVMOD',0,1
	.word	644
	.byte	2,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	644
	.byte	2,4,2,35,1,11
	.byte	'EVR33UVMOD',0,1
	.word	644
	.byte	2,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	644
	.byte	2,0,2,35,1,11
	.byte	'SWDOVMOD',0,1
	.word	644
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	644
	.byte	2,4,2,35,2,11
	.byte	'SWDUVMOD',0,1
	.word	644
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,2
	.word	661
	.byte	10,0,2,35,2,0,29
	.byte	'Ifx_SCU_EVRMONCTRL_Bits',0,4,232,2,3
	.word	32942
	.byte	10
	.byte	'_Ifx_SCU_EVROSCCTRL_Bits',0,4,235,2,16,4,11
	.byte	'OSCTRIM',0,2
	.word	661
	.byte	10,6,2,35,0,11
	.byte	'OSCPTAT',0,1
	.word	644
	.byte	6,0,2,35,1,11
	.byte	'OSCANASEL',0,1
	.word	644
	.byte	4,4,2,35,2,11
	.byte	'HPBGTRIM',0,2
	.word	661
	.byte	7,5,2,35,2,11
	.byte	'HPBGCLKEN',0,1
	.word	644
	.byte	1,4,2,35,3,11
	.byte	'OSC3V3',0,1
	.word	644
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	644
	.byte	2,1,2,35,3,11
	.byte	'LCK',0,1
	.word	644
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVROSCCTRL_Bits',0,4,245,2,3
	.word	33271
	.byte	10
	.byte	'_Ifx_SCU_EVROVMON_Bits',0,4,248,2,16,4,11
	.byte	'EVR13OVVAL',0,1
	.word	644
	.byte	8,0,2,35,0,11
	.byte	'EVR33OVVAL',0,1
	.word	644
	.byte	8,0,2,35,1,11
	.byte	'SWDOVVAL',0,1
	.word	644
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	644
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	644
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVROVMON_Bits',0,4,255,2,3
	.word	33492
	.byte	10
	.byte	'_Ifx_SCU_EVRRSTCON_Bits',0,4,130,3,16,4,11
	.byte	'RST13TRIM',0,1
	.word	644
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	469
	.byte	16,8,2,35,0,11
	.byte	'RST13OFF',0,1
	.word	644
	.byte	1,7,2,35,3,11
	.byte	'BPRST13OFF',0,1
	.word	644
	.byte	1,6,2,35,3,11
	.byte	'RST33OFF',0,1
	.word	644
	.byte	1,5,2,35,3,11
	.byte	'BPRST33OFF',0,1
	.word	644
	.byte	1,4,2,35,3,11
	.byte	'RSTSWDOFF',0,1
	.word	644
	.byte	1,3,2,35,3,11
	.byte	'BPRSTSWDOFF',0,1
	.word	644
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	644
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	644
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRRSTCON_Bits',0,4,142,3,3
	.word	33655
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF1_Bits',0,4,145,3,16,4,11
	.byte	'SD5P',0,1
	.word	644
	.byte	8,0,2,35,0,11
	.byte	'SD5I',0,1
	.word	644
	.byte	8,0,2,35,1,11
	.byte	'SD5D',0,1
	.word	644
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	644
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	644
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF1_Bits',0,4,152,3,3
	.word	33927
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF2_Bits',0,4,155,3,16,4,11
	.byte	'SD33P',0,1
	.word	644
	.byte	8,0,2,35,0,11
	.byte	'SD33I',0,1
	.word	644
	.byte	8,0,2,35,1,11
	.byte	'SD33D',0,1
	.word	644
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	644
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	644
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF2_Bits',0,4,162,3,3
	.word	34080
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF3_Bits',0,4,165,3,16,4,11
	.byte	'CT5REG0',0,1
	.word	644
	.byte	8,0,2,35,0,11
	.byte	'CT5REG1',0,1
	.word	644
	.byte	8,0,2,35,1,11
	.byte	'CT5REG2',0,1
	.word	644
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	644
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	644
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF3_Bits',0,4,172,3,3
	.word	34236
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF4_Bits',0,4,175,3,16,4,11
	.byte	'CT5REG3',0,1
	.word	644
	.byte	8,0,2,35,0,11
	.byte	'CT5REG4',0,1
	.word	644
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	661
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	644
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF4_Bits',0,4,181,3,3
	.word	34398
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF5_Bits',0,4,184,3,16,4,11
	.byte	'CT33REG0',0,1
	.word	644
	.byte	8,0,2,35,0,11
	.byte	'CT33REG1',0,1
	.word	644
	.byte	8,0,2,35,1,11
	.byte	'CT33REG2',0,1
	.word	644
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	644
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	644
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF5_Bits',0,4,191,3,3
	.word	34541
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF6_Bits',0,4,194,3,16,4,11
	.byte	'CT33REG3',0,1
	.word	644
	.byte	8,0,2,35,0,11
	.byte	'CT33REG4',0,1
	.word	644
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	661
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	644
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF6_Bits',0,4,200,3,3
	.word	34706
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL1_Bits',0,4,203,3,16,4,11
	.byte	'SDFREQSPRD',0,2
	.word	661
	.byte	16,0,2,35,0,11
	.byte	'SDFREQ',0,1
	.word	644
	.byte	8,0,2,35,2,11
	.byte	'SDSTEP',0,1
	.word	644
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	644
	.byte	2,2,2,35,3,11
	.byte	'SDSAMPLE',0,1
	.word	644
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	644
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRSDCTRL1_Bits',0,4,211,3,3
	.word	34851
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL2_Bits',0,4,214,3,16,4,11
	.byte	'DRVP',0,1
	.word	644
	.byte	8,0,2,35,0,11
	.byte	'SDMINMAXDC',0,1
	.word	644
	.byte	8,0,2,35,1,11
	.byte	'DRVN',0,1
	.word	644
	.byte	8,0,2,35,2,11
	.byte	'SDLUT',0,1
	.word	644
	.byte	6,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	644
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	644
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRSDCTRL2_Bits',0,4,222,3,3
	.word	35032
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL3_Bits',0,4,225,3,16,4,11
	.byte	'SDPWMPRE',0,1
	.word	644
	.byte	8,0,2,35,0,11
	.byte	'SDPID',0,1
	.word	644
	.byte	8,0,2,35,1,11
	.byte	'SDVOKLVL',0,1
	.word	644
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	644
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	644
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRSDCTRL3_Bits',0,4,232,3,3
	.word	35206
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL4_Bits',0,4,235,3,16,4,11
	.byte	'reserved_0',0,1
	.word	644
	.byte	8,0,2,35,0,11
	.byte	'SYNCDIV',0,1
	.word	644
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	469
	.byte	20,1,2,35,0,11
	.byte	'LCK',0,1
	.word	644
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRSDCTRL4_Bits',0,4,241,3,3
	.word	35366
	.byte	10
	.byte	'_Ifx_SCU_EVRSTAT_Bits',0,4,244,3,16,4,11
	.byte	'EVR13',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'OV13',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'EVR33',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'OV33',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'OVSWD',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'UV13',0,1
	.word	644
	.byte	1,2,2,35,0,11
	.byte	'UV33',0,1
	.word	644
	.byte	1,1,2,35,0,11
	.byte	'UVSWD',0,1
	.word	644
	.byte	1,0,2,35,0,11
	.byte	'EXTPASS13',0,1
	.word	644
	.byte	1,7,2,35,1,11
	.byte	'EXTPASS33',0,1
	.word	644
	.byte	1,6,2,35,1,11
	.byte	'BGPROK',0,1
	.word	644
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	469
	.byte	21,0,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSTAT_Bits',0,4,130,4,3
	.word	35510
	.byte	10
	.byte	'_Ifx_SCU_EVRTRIM_Bits',0,4,133,4,16,4,11
	.byte	'EVR13TRIM',0,1
	.word	644
	.byte	8,0,2,35,0,11
	.byte	'SDVOUTSEL',0,1
	.word	644
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	661
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	644
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRTRIM_Bits',0,4,139,4,3
	.word	35784
	.byte	10
	.byte	'_Ifx_SCU_EVRUVMON_Bits',0,4,142,4,16,4,11
	.byte	'EVR13UVVAL',0,1
	.word	644
	.byte	8,0,2,35,0,11
	.byte	'EVR33UVVAL',0,1
	.word	644
	.byte	8,0,2,35,1,11
	.byte	'SWDUVVAL',0,1
	.word	644
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	644
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	644
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRUVMON_Bits',0,4,149,4,3
	.word	35923
	.byte	10
	.byte	'_Ifx_SCU_EXTCON_Bits',0,4,152,4,16,4,11
	.byte	'EN0',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'SEL0',0,1
	.word	644
	.byte	4,2,2,35,0,11
	.byte	'reserved_6',0,2
	.word	661
	.byte	10,0,2,35,0,11
	.byte	'EN1',0,1
	.word	644
	.byte	1,7,2,35,2,11
	.byte	'NSEL',0,1
	.word	644
	.byte	1,6,2,35,2,11
	.byte	'SEL1',0,1
	.word	644
	.byte	4,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	644
	.byte	2,0,2,35,2,11
	.byte	'DIV1',0,1
	.word	644
	.byte	8,0,2,35,3,0,29
	.byte	'Ifx_SCU_EXTCON_Bits',0,4,163,4,3
	.word	36086
	.byte	10
	.byte	'_Ifx_SCU_FDR_Bits',0,4,166,4,16,4,11
	.byte	'STEP',0,2
	.word	661
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	644
	.byte	4,2,2,35,1,11
	.byte	'DM',0,1
	.word	644
	.byte	2,0,2,35,1,11
	.byte	'RESULT',0,2
	.word	661
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	644
	.byte	5,1,2,35,3,11
	.byte	'DISCLK',0,1
	.word	644
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_FDR_Bits',0,4,174,4,3
	.word	36304
	.byte	10
	.byte	'_Ifx_SCU_FMR_Bits',0,4,177,4,16,4,11
	.byte	'FS0',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'FS1',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'FS2',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'FS3',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'FS4',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'FS5',0,1
	.word	644
	.byte	1,2,2,35,0,11
	.byte	'FS6',0,1
	.word	644
	.byte	1,1,2,35,0,11
	.byte	'FS7',0,1
	.word	644
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	644
	.byte	8,0,2,35,1,11
	.byte	'FC0',0,1
	.word	644
	.byte	1,7,2,35,2,11
	.byte	'FC1',0,1
	.word	644
	.byte	1,6,2,35,2,11
	.byte	'FC2',0,1
	.word	644
	.byte	1,5,2,35,2,11
	.byte	'FC3',0,1
	.word	644
	.byte	1,4,2,35,2,11
	.byte	'FC4',0,1
	.word	644
	.byte	1,3,2,35,2,11
	.byte	'FC5',0,1
	.word	644
	.byte	1,2,2,35,2,11
	.byte	'FC6',0,1
	.word	644
	.byte	1,1,2,35,2,11
	.byte	'FC7',0,1
	.word	644
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	644
	.byte	8,0,2,35,3,0,29
	.byte	'Ifx_SCU_FMR_Bits',0,4,197,4,3
	.word	36467
	.byte	10
	.byte	'_Ifx_SCU_ID_Bits',0,4,200,4,16,4,11
	.byte	'MODREV',0,1
	.word	644
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	644
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	661
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_SCU_ID_Bits',0,4,205,4,3
	.word	36803
	.byte	10
	.byte	'_Ifx_SCU_IGCR_Bits',0,4,208,4,16,4,11
	.byte	'IPEN00',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'IPEN01',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'IPEN02',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'IPEN03',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'IPEN04',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'IPEN05',0,1
	.word	644
	.byte	1,2,2,35,0,11
	.byte	'IPEN06',0,1
	.word	644
	.byte	1,1,2,35,0,11
	.byte	'IPEN07',0,1
	.word	644
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	644
	.byte	5,3,2,35,1,11
	.byte	'GEEN0',0,1
	.word	644
	.byte	1,2,2,35,1,11
	.byte	'IGP0',0,1
	.word	644
	.byte	2,0,2,35,1,11
	.byte	'IPEN10',0,1
	.word	644
	.byte	1,7,2,35,2,11
	.byte	'IPEN11',0,1
	.word	644
	.byte	1,6,2,35,2,11
	.byte	'IPEN12',0,1
	.word	644
	.byte	1,5,2,35,2,11
	.byte	'IPEN13',0,1
	.word	644
	.byte	1,4,2,35,2,11
	.byte	'IPEN14',0,1
	.word	644
	.byte	1,3,2,35,2,11
	.byte	'IPEN15',0,1
	.word	644
	.byte	1,2,2,35,2,11
	.byte	'IPEN16',0,1
	.word	644
	.byte	1,1,2,35,2,11
	.byte	'IPEN17',0,1
	.word	644
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	644
	.byte	5,3,2,35,3,11
	.byte	'GEEN1',0,1
	.word	644
	.byte	1,2,2,35,3,11
	.byte	'IGP1',0,1
	.word	644
	.byte	2,0,2,35,3,0,29
	.byte	'Ifx_SCU_IGCR_Bits',0,4,232,4,3
	.word	36910
	.byte	10
	.byte	'_Ifx_SCU_IN_Bits',0,4,235,4,16,4,11
	.byte	'P0',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	469
	.byte	30,0,2,35,0,0,29
	.byte	'Ifx_SCU_IN_Bits',0,4,240,4,3
	.word	37362
	.byte	10
	.byte	'_Ifx_SCU_IOCR_Bits',0,4,243,4,16,4,11
	.byte	'reserved_0',0,1
	.word	644
	.byte	4,4,2,35,0,11
	.byte	'PC0',0,1
	.word	644
	.byte	4,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	644
	.byte	4,4,2,35,1,11
	.byte	'PC1',0,1
	.word	644
	.byte	4,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	661
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_SCU_IOCR_Bits',0,4,250,4,3
	.word	37461
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL0_Bits',0,4,253,4,16,4,11
	.byte	'LBISTREQ',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'LBISTREQP',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'PATTERNS',0,2
	.word	661
	.byte	14,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	661
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_SCU_LBISTCTRL0_Bits',0,4,131,5,3
	.word	37611
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL1_Bits',0,4,134,5,16,4,11
	.byte	'SEED',0,4
	.word	469
	.byte	23,9,2,35,0,11
	.byte	'reserved_23',0,1
	.word	644
	.byte	1,0,2,35,2,11
	.byte	'SPLITSH',0,1
	.word	644
	.byte	3,5,2,35,3,11
	.byte	'BODY',0,1
	.word	644
	.byte	1,4,2,35,3,11
	.byte	'LBISTFREQU',0,1
	.word	644
	.byte	4,0,2,35,3,0,29
	.byte	'Ifx_SCU_LBISTCTRL1_Bits',0,4,141,5,3
	.word	37760
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL2_Bits',0,4,144,5,16,4,11
	.byte	'SIGNATURE',0,4
	.word	469
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	644
	.byte	7,1,2,35,3,11
	.byte	'LBISTDONE',0,1
	.word	644
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_LBISTCTRL2_Bits',0,4,149,5,3
	.word	37921
	.byte	10
	.byte	'_Ifx_SCU_LCLCON_Bits',0,4,152,5,16,4,11
	.byte	'reserved_0',0,2
	.word	661
	.byte	16,0,2,35,0,11
	.byte	'LS',0,1
	.word	644
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,2
	.word	661
	.byte	14,1,2,35,2,11
	.byte	'LSEN',0,1
	.word	644
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_LCLCON_Bits',0,4,158,5,3
	.word	38051
	.byte	10
	.byte	'_Ifx_SCU_LCLTEST_Bits',0,4,161,5,16,4,11
	.byte	'LCLT0',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'LCLT1',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	469
	.byte	30,0,2,35,0,0,29
	.byte	'Ifx_SCU_LCLTEST_Bits',0,4,166,5,3
	.word	38183
	.byte	10
	.byte	'_Ifx_SCU_MANID_Bits',0,4,169,5,16,4,11
	.byte	'DEPT',0,1
	.word	644
	.byte	5,3,2,35,0,11
	.byte	'MANUF',0,2
	.word	661
	.byte	11,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	661
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_SCU_MANID_Bits',0,4,174,5,3
	.word	38298
	.byte	10
	.byte	'_Ifx_SCU_OMR_Bits',0,4,177,5,16,4,11
	.byte	'PS0',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	661
	.byte	14,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	644
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	644
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	661
	.byte	14,0,2,35,2,0,29
	.byte	'Ifx_SCU_OMR_Bits',0,4,185,5,3
	.word	38409
	.byte	10
	.byte	'_Ifx_SCU_OSCCON_Bits',0,4,188,5,16,4,11
	.byte	'reserved_0',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'PLLLV',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'OSCRES',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'GAINSEL',0,1
	.word	644
	.byte	2,3,2,35,0,11
	.byte	'MODE',0,1
	.word	644
	.byte	2,1,2,35,0,11
	.byte	'SHBY',0,1
	.word	644
	.byte	1,0,2,35,0,11
	.byte	'PLLHV',0,1
	.word	644
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	644
	.byte	1,6,2,35,1,11
	.byte	'X1D',0,1
	.word	644
	.byte	1,5,2,35,1,11
	.byte	'X1DEN',0,1
	.word	644
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	644
	.byte	4,0,2,35,1,11
	.byte	'OSCVAL',0,1
	.word	644
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	644
	.byte	2,1,2,35,2,11
	.byte	'APREN',0,1
	.word	644
	.byte	1,0,2,35,2,11
	.byte	'CAP0EN',0,1
	.word	644
	.byte	1,7,2,35,3,11
	.byte	'CAP1EN',0,1
	.word	644
	.byte	1,6,2,35,3,11
	.byte	'CAP2EN',0,1
	.word	644
	.byte	1,5,2,35,3,11
	.byte	'CAP3EN',0,1
	.word	644
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	644
	.byte	4,0,2,35,3,0,29
	.byte	'Ifx_SCU_OSCCON_Bits',0,4,209,5,3
	.word	38567
	.byte	10
	.byte	'_Ifx_SCU_OUT_Bits',0,4,212,5,16,4,11
	.byte	'P0',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	469
	.byte	30,0,2,35,0,0,29
	.byte	'Ifx_SCU_OUT_Bits',0,4,217,5,3
	.word	38979
	.byte	10
	.byte	'_Ifx_SCU_OVCCON_Bits',0,4,220,5,16,4,11
	.byte	'CSEL0',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'CSEL1',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'CSEL2',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,2
	.word	661
	.byte	13,0,2,35,0,11
	.byte	'OVSTRT',0,1
	.word	644
	.byte	1,7,2,35,2,11
	.byte	'OVSTP',0,1
	.word	644
	.byte	1,6,2,35,2,11
	.byte	'DCINVAL',0,1
	.word	644
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	644
	.byte	5,0,2,35,2,11
	.byte	'OVCONF',0,1
	.word	644
	.byte	1,7,2,35,3,11
	.byte	'POVCONF',0,1
	.word	644
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	644
	.byte	6,0,2,35,3,0,29
	.byte	'Ifx_SCU_OVCCON_Bits',0,4,233,5,3
	.word	39080
	.byte	10
	.byte	'_Ifx_SCU_OVCENABLE_Bits',0,4,236,5,16,4,11
	.byte	'OVEN0',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'OVEN1',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'OVEN2',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	469
	.byte	29,0,2,35,0,0,29
	.byte	'Ifx_SCU_OVCENABLE_Bits',0,4,242,5,3
	.word	39347
	.byte	10
	.byte	'_Ifx_SCU_PDISC_Bits',0,4,245,5,16,4,11
	.byte	'PDIS0',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	469
	.byte	30,0,2,35,0,0,29
	.byte	'Ifx_SCU_PDISC_Bits',0,4,250,5,3
	.word	39483
	.byte	10
	.byte	'_Ifx_SCU_PDR_Bits',0,4,253,5,16,4,11
	.byte	'PD0',0,1
	.word	644
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	644
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	644
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	469
	.byte	24,0,2,35,0,0,29
	.byte	'Ifx_SCU_PDR_Bits',0,4,132,6,3
	.word	39594
	.byte	10
	.byte	'_Ifx_SCU_PDRR_Bits',0,4,135,6,16,4,11
	.byte	'PDR0',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'PDR1',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'PDR2',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'PDR3',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'PDR4',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'PDR5',0,1
	.word	644
	.byte	1,2,2,35,0,11
	.byte	'PDR6',0,1
	.word	644
	.byte	1,1,2,35,0,11
	.byte	'PDR7',0,1
	.word	644
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	469
	.byte	24,0,2,35,0,0,29
	.byte	'Ifx_SCU_PDRR_Bits',0,4,146,6,3
	.word	39727
	.byte	10
	.byte	'_Ifx_SCU_PLLCON0_Bits',0,4,149,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'MODEN',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	644
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	644
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	661
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	644
	.byte	7,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	644
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	644
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	644
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	644
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	644
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	644
	.byte	4,0,2,35,3,0,29
	.byte	'Ifx_SCU_PLLCON0_Bits',0,4,166,6,3
	.word	39930
	.byte	10
	.byte	'_Ifx_SCU_PLLCON1_Bits',0,4,169,6,16,4,11
	.byte	'K2DIV',0,1
	.word	644
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	644
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	644
	.byte	7,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	644
	.byte	1,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	644
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	661
	.byte	9,0,2,35,2,0,29
	.byte	'Ifx_SCU_PLLCON1_Bits',0,4,177,6,3
	.word	40286
	.byte	10
	.byte	'_Ifx_SCU_PLLCON2_Bits',0,4,180,6,16,4,11
	.byte	'MODCFG',0,2
	.word	661
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	661
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_SCU_PLLCON2_Bits',0,4,184,6,3
	.word	40464
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON0_Bits',0,4,187,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	644
	.byte	2,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	644
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	644
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	661
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	644
	.byte	5,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	644
	.byte	2,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	644
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	644
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	644
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	644
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	644
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	644
	.byte	4,0,2,35,3,0,29
	.byte	'Ifx_SCU_PLLERAYCON0_Bits',0,4,204,6,3
	.word	40564
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON1_Bits',0,4,207,6,16,4,11
	.byte	'K2DIV',0,1
	.word	644
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	644
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	644
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	644
	.byte	4,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	644
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	661
	.byte	9,0,2,35,2,0,29
	.byte	'Ifx_SCU_PLLERAYCON1_Bits',0,4,215,6,3
	.word	40934
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYSTAT_Bits',0,4,218,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'PWDSTAT',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	644
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	469
	.byte	26,0,2,35,0,0,29
	.byte	'Ifx_SCU_PLLERAYSTAT_Bits',0,4,227,6,3
	.word	41120
	.byte	10
	.byte	'_Ifx_SCU_PLLSTAT_Bits',0,4,230,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	644
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	644
	.byte	1,1,2,35,0,11
	.byte	'MODRUN',0,1
	.word	644
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	469
	.byte	24,0,2,35,0,0,29
	.byte	'Ifx_SCU_PLLSTAT_Bits',0,4,241,6,3
	.word	41318
	.byte	10
	.byte	'_Ifx_SCU_PMCSR_Bits',0,4,244,6,16,4,11
	.byte	'REQSLP',0,1
	.word	644
	.byte	2,6,2,35,0,11
	.byte	'SMUSLP',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	644
	.byte	5,0,2,35,0,11
	.byte	'PMST',0,1
	.word	644
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	469
	.byte	21,0,2,35,0,0,29
	.byte	'Ifx_SCU_PMCSR_Bits',0,4,251,6,3
	.word	41551
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR0_Bits',0,4,254,6,16,4,11
	.byte	'reserved_0',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'ESR1WKEN',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'PINAWKEN',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'PINBWKEN',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'ESR0DFEN',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'ESR0EDCON',0,1
	.word	644
	.byte	2,1,2,35,0,11
	.byte	'ESR1DFEN',0,1
	.word	644
	.byte	1,0,2,35,0,11
	.byte	'ESR1EDCON',0,1
	.word	644
	.byte	2,6,2,35,1,11
	.byte	'PINADFEN',0,1
	.word	644
	.byte	1,5,2,35,1,11
	.byte	'PINAEDCON',0,1
	.word	644
	.byte	2,3,2,35,1,11
	.byte	'PINBDFEN',0,1
	.word	644
	.byte	1,2,2,35,1,11
	.byte	'PINBEDCON',0,1
	.word	644
	.byte	2,0,2,35,1,11
	.byte	'SCREN',0,1
	.word	644
	.byte	1,7,2,35,2,11
	.byte	'STBYRAMSEL',0,1
	.word	644
	.byte	2,5,2,35,2,11
	.byte	'SCRCLKSEL',0,1
	.word	644
	.byte	1,4,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	644
	.byte	1,3,2,35,2,11
	.byte	'TRISTEN',0,1
	.word	644
	.byte	1,2,2,35,2,11
	.byte	'TRISTREQ',0,1
	.word	644
	.byte	1,1,2,35,2,11
	.byte	'PORSTDF',0,1
	.word	644
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	644
	.byte	1,7,2,35,3,11
	.byte	'DCDCSYNC',0,1
	.word	644
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	644
	.byte	3,3,2,35,3,11
	.byte	'ESR0TRIST',0,1
	.word	644
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	644
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	644
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_PMSWCR0_Bits',0,4,153,7,3
	.word	41703
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR1_Bits',0,4,156,7,16,4,11
	.byte	'SCRSTEN',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'SCRSTREQ',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	644
	.byte	6,0,2,35,0,11
	.byte	'CPUIDLSEL',0,1
	.word	644
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	644
	.byte	1,4,2,35,1,11
	.byte	'IRADIS',0,1
	.word	644
	.byte	1,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	644
	.byte	3,0,2,35,1,11
	.byte	'SCRCFG',0,1
	.word	644
	.byte	8,0,2,35,2,11
	.byte	'CPUSEL',0,1
	.word	644
	.byte	3,5,2,35,3,11
	.byte	'STBYEVEN',0,1
	.word	644
	.byte	1,4,2,35,3,11
	.byte	'STBYEV',0,1
	.word	644
	.byte	3,1,2,35,3,11
	.byte	'LCK',0,1
	.word	644
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_PMSWCR1_Bits',0,4,170,7,3
	.word	42270
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR2_Bits',0,4,173,7,16,4,11
	.byte	'SCRINT',0,1
	.word	644
	.byte	8,0,2,35,0,11
	.byte	'BUSY',0,1
	.word	644
	.byte	1,7,2,35,1,11
	.byte	'SCRECC',0,1
	.word	644
	.byte	1,6,2,35,1,11
	.byte	'SCRWDT',0,1
	.word	644
	.byte	1,5,2,35,1,11
	.byte	'SCRRST',0,1
	.word	644
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	644
	.byte	4,0,2,35,1,11
	.byte	'TCINT',0,1
	.word	644
	.byte	8,0,2,35,2,11
	.byte	'TCINTREQ',0,1
	.word	644
	.byte	1,7,2,35,3,11
	.byte	'SMURST',0,1
	.word	644
	.byte	1,6,2,35,3,11
	.byte	'RST',0,1
	.word	644
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	644
	.byte	4,1,2,35,3,11
	.byte	'LCK',0,1
	.word	644
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_PMSWCR2_Bits',0,4,187,7,3
	.word	42564
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTAT_Bits',0,4,190,7,16,4,11
	.byte	'reserved_0',0,1
	.word	644
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKP',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUN',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'PINAWKP',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUN',0,1
	.word	644
	.byte	1,2,2,35,0,11
	.byte	'PINBWKP',0,1
	.word	644
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUN',0,1
	.word	644
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	644
	.byte	1,7,2,35,1,11
	.byte	'PORSTDF',0,1
	.word	644
	.byte	1,6,2,35,1,11
	.byte	'HWCFGEVR',0,1
	.word	644
	.byte	3,3,2,35,1,11
	.byte	'STBYRAM',0,1
	.word	644
	.byte	2,1,2,35,1,11
	.byte	'TRIST',0,1
	.word	644
	.byte	1,0,2,35,1,11
	.byte	'SCRST',0,1
	.word	644
	.byte	1,7,2,35,2,11
	.byte	'SCRWKP',0,1
	.word	644
	.byte	1,6,2,35,2,11
	.byte	'SCR',0,1
	.word	644
	.byte	1,5,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	644
	.byte	1,4,2,35,2,11
	.byte	'ESR1WKEN',0,1
	.word	644
	.byte	1,3,2,35,2,11
	.byte	'PINAWKEN',0,1
	.word	644
	.byte	1,2,2,35,2,11
	.byte	'PINBWKEN',0,1
	.word	644
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	661
	.byte	4,5,2,35,2,11
	.byte	'ESR0TRIST',0,1
	.word	644
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	644
	.byte	4,0,2,35,3,0,29
	.byte	'Ifx_SCU_PMSWSTAT_Bits',0,4,214,7,3
	.word	42842
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTATCLR_Bits',0,4,217,7,16,4,11
	.byte	'reserved_0',0,1
	.word	644
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKPCLR',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUNCLR',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'PINAWKPCLR',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUNCLR',0,1
	.word	644
	.byte	1,2,2,35,0,11
	.byte	'PINBWKPCLR',0,1
	.word	644
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUNCLR',0,1
	.word	644
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	644
	.byte	8,0,2,35,1,11
	.byte	'SCRSTCLR',0,1
	.word	644
	.byte	1,7,2,35,2,11
	.byte	'SCRWKPCLR',0,1
	.word	644
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	661
	.byte	14,0,2,35,2,0,29
	.byte	'Ifx_SCU_PMSWSTATCLR_Bits',0,4,230,7,3
	.word	43338
	.byte	10
	.byte	'_Ifx_SCU_RSTCON2_Bits',0,4,233,7,16,4,11
	.byte	'reserved_0',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'CLRC',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	661
	.byte	10,4,2,35,0,11
	.byte	'CSS0',0,1
	.word	644
	.byte	1,3,2,35,1,11
	.byte	'CSS1',0,1
	.word	644
	.byte	1,2,2,35,1,11
	.byte	'CSS2',0,1
	.word	644
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	644
	.byte	1,0,2,35,1,11
	.byte	'USRINFO',0,2
	.word	661
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_SCU_RSTCON2_Bits',0,4,243,7,3
	.word	43651
	.byte	10
	.byte	'_Ifx_SCU_RSTCON_Bits',0,4,246,7,16,4,11
	.byte	'ESR0',0,1
	.word	644
	.byte	2,6,2,35,0,11
	.byte	'ESR1',0,1
	.word	644
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	644
	.byte	2,2,2,35,0,11
	.byte	'SMU',0,1
	.word	644
	.byte	2,0,2,35,0,11
	.byte	'SW',0,1
	.word	644
	.byte	2,6,2,35,1,11
	.byte	'STM0',0,1
	.word	644
	.byte	2,4,2,35,1,11
	.byte	'STM1',0,1
	.word	644
	.byte	2,2,2,35,1,11
	.byte	'STM2',0,1
	.word	644
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	661
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_SCU_RSTCON_Bits',0,4,129,8,3
	.word	43860
	.byte	10
	.byte	'_Ifx_SCU_RSTSTAT_Bits',0,4,132,8,16,4,11
	.byte	'ESR0',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'ESR1',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'SMU',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'SW',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'STM0',0,1
	.word	644
	.byte	1,2,2,35,0,11
	.byte	'STM1',0,1
	.word	644
	.byte	1,1,2,35,0,11
	.byte	'STM2',0,1
	.word	644
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	644
	.byte	8,0,2,35,1,11
	.byte	'PORST',0,1
	.word	644
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	644
	.byte	1,6,2,35,2,11
	.byte	'CB0',0,1
	.word	644
	.byte	1,5,2,35,2,11
	.byte	'CB1',0,1
	.word	644
	.byte	1,4,2,35,2,11
	.byte	'CB3',0,1
	.word	644
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	644
	.byte	2,1,2,35,2,11
	.byte	'EVR13',0,1
	.word	644
	.byte	1,0,2,35,2,11
	.byte	'EVR33',0,1
	.word	644
	.byte	1,7,2,35,3,11
	.byte	'SWD',0,1
	.word	644
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	644
	.byte	2,4,2,35,3,11
	.byte	'STBYR',0,1
	.word	644
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	644
	.byte	3,0,2,35,3,0,29
	.byte	'Ifx_SCU_RSTSTAT_Bits',0,4,155,8,3
	.word	44071
	.byte	10
	.byte	'_Ifx_SCU_SAFECON_Bits',0,4,158,8,16,4,11
	.byte	'HBT',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	469
	.byte	31,0,2,35,0,0,29
	.byte	'Ifx_SCU_SAFECON_Bits',0,4,162,8,3
	.word	44503
	.byte	10
	.byte	'_Ifx_SCU_STSTAT_Bits',0,4,165,8,16,4,11
	.byte	'HWCFG',0,1
	.word	644
	.byte	8,0,2,35,0,11
	.byte	'FTM',0,1
	.word	644
	.byte	7,1,2,35,1,11
	.byte	'MODE',0,1
	.word	644
	.byte	1,0,2,35,1,11
	.byte	'FCBAE',0,1
	.word	644
	.byte	1,7,2,35,2,11
	.byte	'LUDIS',0,1
	.word	644
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	644
	.byte	1,5,2,35,2,11
	.byte	'TRSTL',0,1
	.word	644
	.byte	1,4,2,35,2,11
	.byte	'SPDEN',0,1
	.word	644
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	644
	.byte	3,0,2,35,2,11
	.byte	'RAMINT',0,1
	.word	644
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	644
	.byte	7,0,2,35,3,0,29
	.byte	'Ifx_SCU_STSTAT_Bits',0,4,178,8,3
	.word	44599
	.byte	10
	.byte	'_Ifx_SCU_SWRSTCON_Bits',0,4,181,8,16,4,11
	.byte	'reserved_0',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'SWRSTREQ',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	469
	.byte	30,0,2,35,0,0,29
	.byte	'Ifx_SCU_SWRSTCON_Bits',0,4,186,8,3
	.word	44859
	.byte	10
	.byte	'_Ifx_SCU_SYSCON_Bits',0,4,189,8,16,4,11
	.byte	'CCTRIG0',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'RAMINTM',0,1
	.word	644
	.byte	2,4,2,35,0,11
	.byte	'SETLUDIS',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	644
	.byte	3,0,2,35,0,11
	.byte	'DATM',0,1
	.word	644
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,4
	.word	469
	.byte	23,0,2,35,0,0,29
	.byte	'Ifx_SCU_SYSCON_Bits',0,4,198,8,3
	.word	44984
	.byte	10
	.byte	'_Ifx_SCU_TRAPCLR_Bits',0,4,201,8,16,4,11
	.byte	'ESR0T',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	469
	.byte	28,0,2,35,0,0,29
	.byte	'Ifx_SCU_TRAPCLR_Bits',0,4,208,8,3
	.word	45181
	.byte	10
	.byte	'_Ifx_SCU_TRAPDIS_Bits',0,4,211,8,16,4,11
	.byte	'ESR0T',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	469
	.byte	28,0,2,35,0,0,29
	.byte	'Ifx_SCU_TRAPDIS_Bits',0,4,218,8,3
	.word	45334
	.byte	10
	.byte	'_Ifx_SCU_TRAPSET_Bits',0,4,221,8,16,4,11
	.byte	'ESR0T',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	469
	.byte	28,0,2,35,0,0,29
	.byte	'Ifx_SCU_TRAPSET_Bits',0,4,228,8,3
	.word	45487
	.byte	10
	.byte	'_Ifx_SCU_TRAPSTAT_Bits',0,4,231,8,16,4,11
	.byte	'ESR0T',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	469
	.byte	28,0,2,35,0,0,29
	.byte	'Ifx_SCU_TRAPSTAT_Bits',0,4,238,8,3
	.word	45640
	.byte	29
	.byte	'Ifx_SCU_WDTCPU_CON0_Bits',0,4,247,8,3
	.word	508
	.byte	29
	.byte	'Ifx_SCU_WDTCPU_CON1_Bits',0,4,134,9,3
	.word	683
	.byte	29
	.byte	'Ifx_SCU_WDTCPU_SR_Bits',0,4,150,9,3
	.word	927
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON0_Bits',0,4,153,9,16,4,11
	.byte	'ENDINIT',0,4
	.word	492
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	492
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	492
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	492
	.byte	16,0,2,35,0,0,29
	.byte	'Ifx_SCU_WDTS_CON0_Bits',0,4,159,9,3
	.word	45895
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON1_Bits',0,4,162,9,16,4,11
	.byte	'CLRIRF',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'IR0',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	644
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	644
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	644
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	644
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	644
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	661
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_SCU_WDTS_CON1_Bits',0,4,175,9,3
	.word	46021
	.byte	10
	.byte	'_Ifx_SCU_WDTS_SR_Bits',0,4,178,9,16,4,11
	.byte	'AE',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	644
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	644
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	644
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	644
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	644
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	661
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_SCU_WDTS_SR_Bits',0,4,191,9,3
	.word	46273
	.byte	12,4,199,9,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28305
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_ACCEN0',0,4,204,9,3
	.word	46492
	.byte	12,4,207,9,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28862
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_ACCEN1',0,4,212,9,3
	.word	46556
	.byte	12,4,215,9,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28939
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_ARSTDIS',0,4,220,9,3
	.word	46620
	.byte	12,4,223,9,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29075
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_CCUCON0',0,4,228,9,3
	.word	46685
	.byte	12,4,231,9,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29355
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_CCUCON1',0,4,236,9,3
	.word	46750
	.byte	12,4,239,9,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29593
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_CCUCON2',0,4,244,9,3
	.word	46815
	.byte	12,4,247,9,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29721
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_CCUCON3',0,4,252,9,3
	.word	46880
	.byte	12,4,255,9,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29964
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_CCUCON4',0,4,132,10,3
	.word	46945
	.byte	12,4,135,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30199
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_CCUCON5',0,4,140,10,3
	.word	47010
	.byte	12,4,143,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30327
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_CCUCON6',0,4,148,10,3
	.word	47075
	.byte	12,4,151,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30427
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_CCUCON7',0,4,156,10,3
	.word	47140
	.byte	12,4,159,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30527
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_CHIPID',0,4,164,10,3
	.word	47205
	.byte	12,4,167,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30735
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_DTSCON',0,4,172,10,3
	.word	47269
	.byte	12,4,175,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30900
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_DTSLIM',0,4,180,10,3
	.word	47333
	.byte	12,4,183,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31083
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_DTSSTAT',0,4,188,10,3
	.word	47397
	.byte	12,4,191,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31237
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EICR',0,4,196,10,3
	.word	47462
	.byte	12,4,199,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31601
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EIFR',0,4,204,10,3
	.word	47524
	.byte	12,4,207,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31812
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EMSR',0,4,212,10,3
	.word	47586
	.byte	12,4,215,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32064
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_ESRCFG',0,4,220,10,3
	.word	47648
	.byte	12,4,223,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32182
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_ESROCFG',0,4,228,10,3
	.word	47712
	.byte	12,4,231,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32293
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVR13CON',0,4,236,10,3
	.word	47777
	.byte	12,4,239,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32456
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVR33CON',0,4,244,10,3
	.word	47843
	.byte	12,4,247,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32619
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRADCSTAT',0,4,252,10,3
	.word	47909
	.byte	12,4,255,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32777
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRDVSTAT',0,4,132,11,3
	.word	47977
	.byte	12,4,135,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32942
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRMONCTRL',0,4,140,11,3
	.word	48044
	.byte	12,4,143,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33271
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVROSCCTRL',0,4,148,11,3
	.word	48112
	.byte	12,4,151,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33492
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVROVMON',0,4,156,11,3
	.word	48180
	.byte	12,4,159,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33655
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRRSTCON',0,4,164,11,3
	.word	48246
	.byte	12,4,167,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33927
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF1',0,4,172,11,3
	.word	48313
	.byte	12,4,175,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34080
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF2',0,4,180,11,3
	.word	48382
	.byte	12,4,183,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34236
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF3',0,4,188,11,3
	.word	48451
	.byte	12,4,191,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34398
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF4',0,4,196,11,3
	.word	48520
	.byte	12,4,199,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34541
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF5',0,4,204,11,3
	.word	48589
	.byte	12,4,207,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34706
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF6',0,4,212,11,3
	.word	48658
	.byte	12,4,215,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34851
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSDCTRL1',0,4,220,11,3
	.word	48727
	.byte	12,4,223,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35032
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSDCTRL2',0,4,228,11,3
	.word	48795
	.byte	12,4,231,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35206
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSDCTRL3',0,4,236,11,3
	.word	48863
	.byte	12,4,239,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35366
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSDCTRL4',0,4,244,11,3
	.word	48931
	.byte	12,4,247,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35510
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSTAT',0,4,252,11,3
	.word	48999
	.byte	12,4,255,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35784
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRTRIM',0,4,132,12,3
	.word	49064
	.byte	12,4,135,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35923
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRUVMON',0,4,140,12,3
	.word	49129
	.byte	12,4,143,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36086
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EXTCON',0,4,148,12,3
	.word	49195
	.byte	12,4,151,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36304
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_FDR',0,4,156,12,3
	.word	49259
	.byte	12,4,159,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36467
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_FMR',0,4,164,12,3
	.word	49320
	.byte	12,4,167,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36803
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_ID',0,4,172,12,3
	.word	49381
	.byte	12,4,175,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36910
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_IGCR',0,4,180,12,3
	.word	49441
	.byte	12,4,183,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37362
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_IN',0,4,188,12,3
	.word	49503
	.byte	12,4,191,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37461
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_IOCR',0,4,196,12,3
	.word	49563
	.byte	12,4,199,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37611
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_LBISTCTRL0',0,4,204,12,3
	.word	49625
	.byte	12,4,207,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37760
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_LBISTCTRL1',0,4,212,12,3
	.word	49693
	.byte	12,4,215,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37921
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_LBISTCTRL2',0,4,220,12,3
	.word	49761
	.byte	12,4,223,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38051
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_LCLCON',0,4,228,12,3
	.word	49829
	.byte	12,4,231,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38183
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_LCLTEST',0,4,236,12,3
	.word	49893
	.byte	12,4,239,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38298
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_MANID',0,4,244,12,3
	.word	49958
	.byte	12,4,247,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38409
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_OMR',0,4,252,12,3
	.word	50021
	.byte	12,4,255,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38567
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_OSCCON',0,4,132,13,3
	.word	50082
	.byte	12,4,135,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38979
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_OUT',0,4,140,13,3
	.word	50146
	.byte	12,4,143,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39080
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_OVCCON',0,4,148,13,3
	.word	50207
	.byte	12,4,151,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39347
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_OVCENABLE',0,4,156,13,3
	.word	50271
	.byte	12,4,159,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39483
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PDISC',0,4,164,13,3
	.word	50338
	.byte	12,4,167,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39594
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PDR',0,4,172,13,3
	.word	50401
	.byte	12,4,175,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39727
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PDRR',0,4,180,13,3
	.word	50462
	.byte	12,4,183,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39930
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PLLCON0',0,4,188,13,3
	.word	50524
	.byte	12,4,191,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40286
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PLLCON1',0,4,196,13,3
	.word	50589
	.byte	12,4,199,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40464
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PLLCON2',0,4,204,13,3
	.word	50654
	.byte	12,4,207,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40564
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PLLERAYCON0',0,4,212,13,3
	.word	50719
	.byte	12,4,215,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40934
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PLLERAYCON1',0,4,220,13,3
	.word	50788
	.byte	12,4,223,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41120
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PLLERAYSTAT',0,4,228,13,3
	.word	50857
	.byte	12,4,231,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41318
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PLLSTAT',0,4,236,13,3
	.word	50926
	.byte	12,4,239,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41551
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PMCSR',0,4,244,13,3
	.word	50991
	.byte	12,4,247,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41703
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PMSWCR0',0,4,252,13,3
	.word	51054
	.byte	12,4,255,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42270
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PMSWCR1',0,4,132,14,3
	.word	51119
	.byte	12,4,135,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42564
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PMSWCR2',0,4,140,14,3
	.word	51184
	.byte	12,4,143,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42842
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PMSWSTAT',0,4,148,14,3
	.word	51249
	.byte	12,4,151,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43338
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PMSWSTATCLR',0,4,156,14,3
	.word	51315
	.byte	12,4,159,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43860
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_RSTCON',0,4,164,14,3
	.word	51384
	.byte	12,4,167,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43651
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_RSTCON2',0,4,172,14,3
	.word	51448
	.byte	12,4,175,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44071
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_RSTSTAT',0,4,180,14,3
	.word	51513
	.byte	12,4,183,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44503
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_SAFECON',0,4,188,14,3
	.word	51578
	.byte	12,4,191,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44599
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_STSTAT',0,4,196,14,3
	.word	51643
	.byte	12,4,199,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44859
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_SWRSTCON',0,4,204,14,3
	.word	51707
	.byte	12,4,207,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44984
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_SYSCON',0,4,212,14,3
	.word	51773
	.byte	12,4,215,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45181
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_TRAPCLR',0,4,220,14,3
	.word	51837
	.byte	12,4,223,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45334
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_TRAPDIS',0,4,228,14,3
	.word	51902
	.byte	12,4,231,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45487
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_TRAPSET',0,4,236,14,3
	.word	51967
	.byte	12,4,239,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45640
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_TRAPSTAT',0,4,244,14,3
	.word	52032
	.byte	29
	.byte	'Ifx_SCU_WDTCPU_CON0',0,4,252,14,3
	.word	604
	.byte	29
	.byte	'Ifx_SCU_WDTCPU_CON1',0,4,132,15,3
	.word	887
	.byte	29
	.byte	'Ifx_SCU_WDTCPU_SR',0,4,140,15,3
	.word	1118
	.byte	12,4,143,15,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45895
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_WDTS_CON0',0,4,148,15,3
	.word	52183
	.byte	12,4,151,15,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46021
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_WDTS_CON1',0,4,156,15,3
	.word	52250
	.byte	12,4,159,15,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46273
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_WDTS_SR',0,4,164,15,3
	.word	52317
	.byte	14
	.word	1158
	.byte	29
	.byte	'Ifx_SCU_WDTCPU',0,4,180,15,3
	.word	52382
	.byte	10
	.byte	'_Ifx_SCU_WDTS',0,4,183,15,25,12,13
	.byte	'CON0',0
	.word	52183
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	52250
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	52317
	.byte	4,2,35,8,0,14
	.word	52411
	.byte	29
	.byte	'Ifx_SCU_WDTS',0,4,188,15,3
	.word	52472
	.byte	15,8
	.word	47648
	.byte	16,1,0,15,20
	.word	644
	.byte	16,19,0,15,8
	.word	50991
	.byte	16,1,0,14
	.word	52411
	.byte	15,24
	.word	1158
	.byte	16,1,0,14
	.word	52531
	.byte	15,16
	.word	644
	.byte	16,15,0,15,28
	.word	644
	.byte	16,27,0,15,40
	.word	644
	.byte	16,39,0,15,16
	.word	47462
	.byte	16,3,0,15,16
	.word	49441
	.byte	16,3,0,15,180,3
	.word	644
	.byte	16,179,3,0,10
	.byte	'_Ifx_SCU',0,4,201,15,25,128,8,13
	.byte	'reserved_0',0
	.word	4281
	.byte	8,2,35,0,13
	.byte	'ID',0
	.word	49381
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	2462
	.byte	4,2,35,12,13
	.byte	'OSCCON',0
	.word	50082
	.byte	4,2,35,16,13
	.byte	'PLLSTAT',0
	.word	50926
	.byte	4,2,35,20,13
	.byte	'PLLCON0',0
	.word	50524
	.byte	4,2,35,24,13
	.byte	'PLLCON1',0
	.word	50589
	.byte	4,2,35,28,13
	.byte	'PLLCON2',0
	.word	50654
	.byte	4,2,35,32,13
	.byte	'PLLERAYSTAT',0
	.word	50857
	.byte	4,2,35,36,13
	.byte	'PLLERAYCON0',0
	.word	50719
	.byte	4,2,35,40,13
	.byte	'PLLERAYCON1',0
	.word	50788
	.byte	4,2,35,44,13
	.byte	'CCUCON0',0
	.word	46685
	.byte	4,2,35,48,13
	.byte	'CCUCON1',0
	.word	46750
	.byte	4,2,35,52,13
	.byte	'FDR',0
	.word	49259
	.byte	4,2,35,56,13
	.byte	'EXTCON',0
	.word	49195
	.byte	4,2,35,60,13
	.byte	'CCUCON2',0
	.word	46815
	.byte	4,2,35,64,13
	.byte	'CCUCON3',0
	.word	46880
	.byte	4,2,35,68,13
	.byte	'CCUCON4',0
	.word	46945
	.byte	4,2,35,72,13
	.byte	'CCUCON5',0
	.word	47010
	.byte	4,2,35,76,13
	.byte	'RSTSTAT',0
	.word	51513
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	2462
	.byte	4,2,35,84,13
	.byte	'RSTCON',0
	.word	51384
	.byte	4,2,35,88,13
	.byte	'ARSTDIS',0
	.word	46620
	.byte	4,2,35,92,13
	.byte	'SWRSTCON',0
	.word	51707
	.byte	4,2,35,96,13
	.byte	'RSTCON2',0
	.word	51448
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	2462
	.byte	4,2,35,104,13
	.byte	'EVRRSTCON',0
	.word	48246
	.byte	4,2,35,108,13
	.byte	'ESRCFG',0
	.word	52499
	.byte	8,2,35,112,13
	.byte	'ESROCFG',0
	.word	47712
	.byte	4,2,35,120,13
	.byte	'SYSCON',0
	.word	51773
	.byte	4,2,35,124,13
	.byte	'CCUCON6',0
	.word	47075
	.byte	4,3,35,128,1,13
	.byte	'CCUCON7',0
	.word	47140
	.byte	4,3,35,132,1,13
	.byte	'reserved_88',0
	.word	52508
	.byte	20,3,35,136,1,13
	.byte	'PDR',0
	.word	50401
	.byte	4,3,35,156,1,13
	.byte	'IOCR',0
	.word	49563
	.byte	4,3,35,160,1,13
	.byte	'OUT',0
	.word	50146
	.byte	4,3,35,164,1,13
	.byte	'OMR',0
	.word	50021
	.byte	4,3,35,168,1,13
	.byte	'IN',0
	.word	49503
	.byte	4,3,35,172,1,13
	.byte	'EVRSTAT',0
	.word	48999
	.byte	4,3,35,176,1,13
	.byte	'EVRDVSTAT',0
	.word	47977
	.byte	4,3,35,180,1,13
	.byte	'EVR13CON',0
	.word	47777
	.byte	4,3,35,184,1,13
	.byte	'EVR33CON',0
	.word	47843
	.byte	4,3,35,188,1,13
	.byte	'STSTAT',0
	.word	51643
	.byte	4,3,35,192,1,13
	.byte	'reserved_C4',0
	.word	2462
	.byte	4,3,35,196,1,13
	.byte	'PMSWCR0',0
	.word	51054
	.byte	4,3,35,200,1,13
	.byte	'PMSWSTAT',0
	.word	51249
	.byte	4,3,35,204,1,13
	.byte	'PMSWSTATCLR',0
	.word	51315
	.byte	4,3,35,208,1,13
	.byte	'PMCSR',0
	.word	52517
	.byte	8,3,35,212,1,13
	.byte	'reserved_DC',0
	.word	2462
	.byte	4,3,35,220,1,13
	.byte	'DTSSTAT',0
	.word	47397
	.byte	4,3,35,224,1,13
	.byte	'DTSCON',0
	.word	47269
	.byte	4,3,35,228,1,13
	.byte	'PMSWCR1',0
	.word	51119
	.byte	4,3,35,232,1,13
	.byte	'PMSWCR2',0
	.word	51184
	.byte	4,3,35,236,1,13
	.byte	'WDTS',0
	.word	52526
	.byte	12,3,35,240,1,13
	.byte	'EMSR',0
	.word	47586
	.byte	4,3,35,252,1,13
	.byte	'WDTCPU',0
	.word	52540
	.byte	24,3,35,128,2,13
	.byte	'reserved_118',0
	.word	4621
	.byte	12,3,35,152,2,13
	.byte	'TRAPSTAT',0
	.word	52032
	.byte	4,3,35,164,2,13
	.byte	'TRAPSET',0
	.word	51967
	.byte	4,3,35,168,2,13
	.byte	'TRAPCLR',0
	.word	51837
	.byte	4,3,35,172,2,13
	.byte	'TRAPDIS',0
	.word	51902
	.byte	4,3,35,176,2,13
	.byte	'reserved_134',0
	.word	2462
	.byte	4,3,35,180,2,13
	.byte	'LCLCON1',0
	.word	49829
	.byte	4,3,35,184,2,13
	.byte	'LCLTEST',0
	.word	49893
	.byte	4,3,35,188,2,13
	.byte	'CHIPID',0
	.word	47205
	.byte	4,3,35,192,2,13
	.byte	'MANID',0
	.word	49958
	.byte	4,3,35,196,2,13
	.byte	'reserved_148',0
	.word	4281
	.byte	8,3,35,200,2,13
	.byte	'SAFECON',0
	.word	51578
	.byte	4,3,35,208,2,13
	.byte	'reserved_154',0
	.word	52545
	.byte	16,3,35,212,2,13
	.byte	'LBISTCTRL0',0
	.word	49625
	.byte	4,3,35,228,2,13
	.byte	'LBISTCTRL1',0
	.word	49693
	.byte	4,3,35,232,2,13
	.byte	'LBISTCTRL2',0
	.word	49761
	.byte	4,3,35,236,2,13
	.byte	'reserved_170',0
	.word	52554
	.byte	28,3,35,240,2,13
	.byte	'PDISC',0
	.word	50338
	.byte	4,3,35,140,3,13
	.byte	'reserved_190',0
	.word	4281
	.byte	8,3,35,144,3,13
	.byte	'EVRTRIM',0
	.word	49064
	.byte	4,3,35,152,3,13
	.byte	'EVRADCSTAT',0
	.word	47909
	.byte	4,3,35,156,3,13
	.byte	'EVRUVMON',0
	.word	49129
	.byte	4,3,35,160,3,13
	.byte	'EVROVMON',0
	.word	48180
	.byte	4,3,35,164,3,13
	.byte	'EVRMONCTRL',0
	.word	48044
	.byte	4,3,35,168,3,13
	.byte	'reserved_1AC',0
	.word	2462
	.byte	4,3,35,172,3,13
	.byte	'EVRSDCTRL1',0
	.word	48727
	.byte	4,3,35,176,3,13
	.byte	'EVRSDCTRL2',0
	.word	48795
	.byte	4,3,35,180,3,13
	.byte	'EVRSDCTRL3',0
	.word	48863
	.byte	4,3,35,184,3,13
	.byte	'EVRSDCTRL4',0
	.word	48931
	.byte	4,3,35,188,3,13
	.byte	'EVRSDCOEFF1',0
	.word	48313
	.byte	4,3,35,192,3,13
	.byte	'EVRSDCOEFF2',0
	.word	48382
	.byte	4,3,35,196,3,13
	.byte	'EVRSDCOEFF3',0
	.word	48451
	.byte	4,3,35,200,3,13
	.byte	'EVRSDCOEFF4',0
	.word	48520
	.byte	4,3,35,204,3,13
	.byte	'EVRSDCOEFF5',0
	.word	48589
	.byte	4,3,35,208,3,13
	.byte	'EVRSDCOEFF6',0
	.word	48658
	.byte	4,3,35,212,3,13
	.byte	'EVROSCCTRL',0
	.word	48112
	.byte	4,3,35,216,3,13
	.byte	'reserved_1DC',0
	.word	2462
	.byte	4,3,35,220,3,13
	.byte	'OVCENABLE',0
	.word	50271
	.byte	4,3,35,224,3,13
	.byte	'OVCCON',0
	.word	50207
	.byte	4,3,35,228,3,13
	.byte	'reserved_1E8',0
	.word	52563
	.byte	40,3,35,232,3,13
	.byte	'EICR',0
	.word	52572
	.byte	16,3,35,144,4,13
	.byte	'EIFR',0
	.word	47524
	.byte	4,3,35,160,4,13
	.byte	'FMR',0
	.word	49320
	.byte	4,3,35,164,4,13
	.byte	'PDRR',0
	.word	50462
	.byte	4,3,35,168,4,13
	.byte	'IGCR',0
	.word	52581
	.byte	16,3,35,172,4,13
	.byte	'reserved_23C',0
	.word	2462
	.byte	4,3,35,188,4,13
	.byte	'DTSLIM',0
	.word	47333
	.byte	4,3,35,192,4,13
	.byte	'reserved_244',0
	.word	52590
	.byte	180,3,3,35,196,4,13
	.byte	'ACCEN1',0
	.word	46556
	.byte	4,3,35,248,7,13
	.byte	'ACCEN0',0
	.word	46492
	.byte	4,3,35,252,7,0,14
	.word	52601
	.byte	29
	.byte	'Ifx_SCU',0,4,181,16,3
	.word	54591
	.byte	29
	.byte	'IfxPort_InputMode',0,5,89,3
	.word	9214
	.byte	29
	.byte	'IfxPort_OutputIdx',0,5,130,1,3
	.word	9489
	.byte	29
	.byte	'IfxPort_OutputMode',0,5,138,1,3
	.word	9419
	.byte	17,5,144,1,9,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed1',0,0,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed2',0,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed3',0,2,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed4',0,3,18
	.byte	'IfxPort_PadDriver_lvdsSpeed1',0,4,18
	.byte	'IfxPort_PadDriver_lvdsSpeed2',0,5,18
	.byte	'IfxPort_PadDriver_lvdsSpeed3',0,6,18
	.byte	'IfxPort_PadDriver_lvdsSpeed4',0,7,18
	.byte	'IfxPort_PadDriver_ttlSpeed1',0,8,18
	.byte	'IfxPort_PadDriver_ttlSpeed2',0,9,18
	.byte	'IfxPort_PadDriver_ttlSpeed3',0,10,18
	.byte	'IfxPort_PadDriver_ttlSpeed4',0,11,0,29
	.byte	'IfxPort_PadDriver',0,5,158,1,3
	.word	54694
	.byte	29
	.byte	'IfxPort_State',0,5,178,1,3
	.word	9802
	.byte	20,5,190,1,9,8,13
	.byte	'port',0
	.word	9209
	.byte	4,2,35,0,13
	.byte	'pinIndex',0
	.word	644
	.byte	1,2,35,4,0,29
	.byte	'IfxPort_Pin',0,5,194,1,3
	.word	55159
	.byte	29
	.byte	'gpio_pin_enum',0,19,89,2
	.word	22948
	.byte	29
	.byte	'gpio_dir_enum',0,19,95,2
	.word	24922
	.byte	29
	.byte	'gpio_mode_enum',0,19,111,2
	.word	24940
	.byte	29
	.byte	'pit_index_enum',0,20,47,2
	.word	25103
	.byte	29
	.byte	'Ifx_ASCLIN_ACCEN0_Bits',0,13,79,3
	.word	16471
	.byte	29
	.byte	'Ifx_ASCLIN_ACCEN1_Bits',0,13,85,3
	.word	16379
	.byte	29
	.byte	'Ifx_ASCLIN_BITCON_Bits',0,13,97,3
	.word	12003
	.byte	29
	.byte	'Ifx_ASCLIN_BRD_Bits',0,13,106,3
	.word	12843
	.byte	29
	.byte	'Ifx_ASCLIN_BRG_Bits',0,13,115,3
	.word	12686
	.byte	29
	.byte	'Ifx_ASCLIN_CLC_Bits',0,13,125,3
	.word	10958
	.byte	29
	.byte	'Ifx_ASCLIN_CSR_Bits',0,13,133,1,3
	.word	15652
	.byte	29
	.byte	'Ifx_ASCLIN_DATCON_Bits',0,13,145,1,3
	.word	12489
	.byte	29
	.byte	'Ifx_ASCLIN_FLAGS_Bits',0,13,177,1,3
	.word	13499
	.byte	29
	.byte	'Ifx_ASCLIN_FLAGSCLEAR_Bits',0,13,209,1,3
	.word	14498
	.byte	29
	.byte	'Ifx_ASCLIN_FLAGSENABLE_Bits',0,13,238,1,3
	.word	15013
	.byte	29
	.byte	'Ifx_ASCLIN_FLAGSSET_Bits',0,13,142,2,3
	.word	13985
	.byte	29
	.byte	'Ifx_ASCLIN_FRAMECON_Bits',0,13,158,2,3
	.word	12224
	.byte	29
	.byte	'Ifx_ASCLIN_ID_Bits',0,13,166,2,3
	.word	11412
	.byte	29
	.byte	'Ifx_ASCLIN_IOCR_Bits',0,13,184,2,3
	.word	11117
	.byte	29
	.byte	'Ifx_ASCLIN_KRST0_Bits',0,13,192,2,3
	.word	16253
	.byte	29
	.byte	'Ifx_ASCLIN_KRST1_Bits',0,13,199,2,3
	.word	16146
	.byte	29
	.byte	'Ifx_ASCLIN_KRSTCLR_Bits',0,13,206,2,3
	.word	16037
	.byte	29
	.byte	'Ifx_ASCLIN_LIN_BTIMER_Bits',0,13,213,2,3
	.word	13197
	.byte	29
	.byte	'Ifx_ASCLIN_LIN_CON_Bits',0,13,225,2,3
	.word	12997
	.byte	29
	.byte	'Ifx_ASCLIN_LIN_HTIMER_Bits',0,13,232,2,3
	.word	13311
	.byte	29
	.byte	'Ifx_ASCLIN_OCS_Bits',0,13,242,2,3
	.word	15874
	.byte	29
	.byte	'Ifx_ASCLIN_RXDATA_Bits',0,13,248,2,3
	.word	15565
	.byte	29
	.byte	'Ifx_ASCLIN_RXDATAD_Bits',0,13,254,2,3
	.word	15775
	.byte	29
	.byte	'Ifx_ASCLIN_RXFIFOCON_Bits',0,13,140,3,3
	.word	11762
	.byte	29
	.byte	'Ifx_ASCLIN_TXDATA_Bits',0,13,146,3,3
	.word	15478
	.byte	29
	.byte	'Ifx_ASCLIN_TXFIFOCON_Bits',0,13,159,3,3
	.word	11537
	.byte	29
	.byte	'Ifx_ASCLIN_ACCEN0',0,13,172,3,3
	.word	17003
	.byte	29
	.byte	'Ifx_ASCLIN_ACCEN1',0,13,180,3,3
	.word	16431
	.byte	29
	.byte	'Ifx_ASCLIN_BITCON',0,13,188,3,3
	.word	12184
	.byte	29
	.byte	'Ifx_ASCLIN_BRD',0,13,196,3,3
	.word	12957
	.byte	29
	.byte	'Ifx_ASCLIN_BRG',0,13,204,3,3
	.word	12803
	.byte	29
	.byte	'Ifx_ASCLIN_CLC',0,13,212,3,3
	.word	11077
	.byte	29
	.byte	'Ifx_ASCLIN_CSR',0,13,220,3,3
	.word	15735
	.byte	29
	.byte	'Ifx_ASCLIN_DATCON',0,13,228,3,3
	.word	12646
	.byte	29
	.byte	'Ifx_ASCLIN_FLAGS',0,13,236,3,3
	.word	13945
	.byte	29
	.byte	'Ifx_ASCLIN_FLAGSCLEAR',0,13,244,3,3
	.word	14973
	.byte	29
	.byte	'Ifx_ASCLIN_FLAGSENABLE',0,13,252,3,3
	.word	15438
	.byte	29
	.byte	'Ifx_ASCLIN_FLAGSSET',0,13,132,4,3
	.word	14458
	.byte	29
	.byte	'Ifx_ASCLIN_FRAMECON',0,13,140,4,3
	.word	12449
	.byte	29
	.byte	'Ifx_ASCLIN_ID',0,13,148,4,3
	.word	11497
	.byte	29
	.byte	'Ifx_ASCLIN_IOCR',0,13,156,4,3
	.word	11372
	.byte	29
	.byte	'Ifx_ASCLIN_KRST0',0,13,164,4,3
	.word	16339
	.byte	29
	.byte	'Ifx_ASCLIN_KRST1',0,13,172,4,3
	.word	16213
	.byte	29
	.byte	'Ifx_ASCLIN_KRSTCLR',0,13,180,4,3
	.word	16106
	.byte	29
	.byte	'Ifx_ASCLIN_LIN_BTIMER',0,13,188,4,3
	.word	13271
	.byte	29
	.byte	'Ifx_ASCLIN_LIN_CON',0,13,196,4,3
	.word	13157
	.byte	29
	.byte	'Ifx_ASCLIN_LIN_HTIMER',0,13,204,4,3
	.word	13386
	.byte	29
	.byte	'Ifx_ASCLIN_OCS',0,13,212,4,3
	.word	15997
	.byte	29
	.byte	'Ifx_ASCLIN_RXDATA',0,13,220,4,3
	.word	15612
	.byte	29
	.byte	'Ifx_ASCLIN_RXDATAD',0,13,228,4,3
	.word	15823
	.byte	29
	.byte	'Ifx_ASCLIN_RXFIFOCON',0,13,236,4,3
	.word	11963
	.byte	29
	.byte	'Ifx_ASCLIN_TXDATA',0,13,244,4,3
	.word	15525
	.byte	29
	.byte	'Ifx_ASCLIN_TXFIFOCON',0,13,252,4,3
	.word	11722
	.byte	14
	.word	13426
	.byte	29
	.byte	'Ifx_ASCLIN_LIN',0,13,140,5,3
	.word	56908
	.byte	14
	.word	17043
	.byte	29
	.byte	'Ifx_ASCLIN',0,13,181,5,3
	.word	56937
	.byte	10
	.byte	'_Ifx_CPU_A_Bits',0,28,45,16,4,11
	.byte	'ADDR',0,4
	.word	492
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_A_Bits',0,28,48,3
	.word	56962
	.byte	10
	.byte	'_Ifx_CPU_BIV_Bits',0,28,51,16,4,11
	.byte	'VSS',0,4
	.word	492
	.byte	1,31,2,35,0,11
	.byte	'BIV',0,4
	.word	492
	.byte	31,0,2,35,0,0,29
	.byte	'Ifx_CPU_BIV_Bits',0,28,55,3
	.word	57023
	.byte	10
	.byte	'_Ifx_CPU_BTV_Bits',0,28,58,16,4,11
	.byte	'reserved_0',0,4
	.word	492
	.byte	1,31,2,35,0,11
	.byte	'BTV',0,4
	.word	492
	.byte	31,0,2,35,0,0,29
	.byte	'Ifx_CPU_BTV_Bits',0,28,62,3
	.word	57102
	.byte	10
	.byte	'_Ifx_CPU_CCNT_Bits',0,28,65,16,4,11
	.byte	'CountValue',0,4
	.word	492
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	492
	.byte	1,0,2,35,0,0,29
	.byte	'Ifx_CPU_CCNT_Bits',0,28,69,3
	.word	57188
	.byte	10
	.byte	'_Ifx_CPU_CCTRL_Bits',0,28,72,16,4,11
	.byte	'CM',0,4
	.word	492
	.byte	1,31,2,35,0,11
	.byte	'CE',0,4
	.word	492
	.byte	1,30,2,35,0,11
	.byte	'M1',0,4
	.word	492
	.byte	3,27,2,35,0,11
	.byte	'M2',0,4
	.word	492
	.byte	3,24,2,35,0,11
	.byte	'M3',0,4
	.word	492
	.byte	3,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	492
	.byte	21,0,2,35,0,0,29
	.byte	'Ifx_CPU_CCTRL_Bits',0,28,80,3
	.word	57277
	.byte	10
	.byte	'_Ifx_CPU_COMPAT_Bits',0,28,83,16,4,11
	.byte	'reserved_0',0,4
	.word	492
	.byte	3,29,2,35,0,11
	.byte	'RM',0,4
	.word	492
	.byte	1,28,2,35,0,11
	.byte	'SP',0,4
	.word	492
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	492
	.byte	27,0,2,35,0,0,29
	.byte	'Ifx_CPU_COMPAT_Bits',0,28,89,3
	.word	57423
	.byte	10
	.byte	'_Ifx_CPU_CORE_ID_Bits',0,28,92,16,4,11
	.byte	'CORE_ID',0,4
	.word	492
	.byte	3,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	492
	.byte	29,0,2,35,0,0,29
	.byte	'Ifx_CPU_CORE_ID_Bits',0,28,96,3
	.word	57550
	.byte	10
	.byte	'_Ifx_CPU_CPR_L_Bits',0,28,99,16,4,11
	.byte	'reserved_0',0,4
	.word	492
	.byte	3,29,2,35,0,11
	.byte	'LOWBND',0,4
	.word	492
	.byte	29,0,2,35,0,0,29
	.byte	'Ifx_CPU_CPR_L_Bits',0,28,103,3
	.word	57648
	.byte	10
	.byte	'_Ifx_CPU_CPR_U_Bits',0,28,106,16,4,11
	.byte	'reserved_0',0,4
	.word	492
	.byte	3,29,2,35,0,11
	.byte	'UPPBND',0,4
	.word	492
	.byte	29,0,2,35,0,0,29
	.byte	'Ifx_CPU_CPR_U_Bits',0,28,110,3
	.word	57741
	.byte	10
	.byte	'_Ifx_CPU_CPU_ID_Bits',0,28,113,16,4,11
	.byte	'MODREV',0,4
	.word	492
	.byte	8,24,2,35,0,11
	.byte	'MOD_32B',0,4
	.word	492
	.byte	8,16,2,35,0,11
	.byte	'MOD',0,4
	.word	492
	.byte	16,0,2,35,0,0,29
	.byte	'Ifx_CPU_CPU_ID_Bits',0,28,118,3
	.word	57834
	.byte	10
	.byte	'_Ifx_CPU_CPXE_Bits',0,28,121,16,4,11
	.byte	'XE',0,4
	.word	492
	.byte	8,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	492
	.byte	24,0,2,35,0,0,29
	.byte	'Ifx_CPU_CPXE_Bits',0,28,125,3
	.word	57941
	.byte	10
	.byte	'_Ifx_CPU_CREVT_Bits',0,28,128,1,16,4,11
	.byte	'EVTA',0,4
	.word	492
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	492
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	492
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	492
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	492
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	492
	.byte	24,0,2,35,0,0,29
	.byte	'Ifx_CPU_CREVT_Bits',0,28,136,1,3
	.word	58028
	.byte	10
	.byte	'_Ifx_CPU_CUS_ID_Bits',0,28,139,1,16,4,11
	.byte	'CID',0,4
	.word	492
	.byte	3,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	492
	.byte	29,0,2,35,0,0,29
	.byte	'Ifx_CPU_CUS_ID_Bits',0,28,143,1,3
	.word	58182
	.byte	10
	.byte	'_Ifx_CPU_D_Bits',0,28,146,1,16,4,11
	.byte	'DATA',0,4
	.word	492
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_D_Bits',0,28,149,1,3
	.word	58276
	.byte	10
	.byte	'_Ifx_CPU_DATR_Bits',0,28,152,1,16,4,11
	.byte	'reserved_0',0,4
	.word	492
	.byte	3,29,2,35,0,11
	.byte	'SBE',0,4
	.word	492
	.byte	1,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	492
	.byte	5,23,2,35,0,11
	.byte	'CWE',0,4
	.word	492
	.byte	1,22,2,35,0,11
	.byte	'CFE',0,4
	.word	492
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	492
	.byte	3,18,2,35,0,11
	.byte	'SOE',0,4
	.word	492
	.byte	1,17,2,35,0,11
	.byte	'SME',0,4
	.word	492
	.byte	1,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	492
	.byte	16,0,2,35,0,0,29
	.byte	'Ifx_CPU_DATR_Bits',0,28,163,1,3
	.word	58339
	.byte	10
	.byte	'_Ifx_CPU_DBGSR_Bits',0,28,166,1,16,4,11
	.byte	'DE',0,4
	.word	492
	.byte	1,31,2,35,0,11
	.byte	'HALT',0,4
	.word	492
	.byte	2,29,2,35,0,11
	.byte	'SIH',0,4
	.word	492
	.byte	1,28,2,35,0,11
	.byte	'SUSP',0,4
	.word	492
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	492
	.byte	1,26,2,35,0,11
	.byte	'PREVSUSP',0,4
	.word	492
	.byte	1,25,2,35,0,11
	.byte	'PEVT',0,4
	.word	492
	.byte	1,24,2,35,0,11
	.byte	'EVTSRC',0,4
	.word	492
	.byte	5,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	492
	.byte	19,0,2,35,0,0,29
	.byte	'Ifx_CPU_DBGSR_Bits',0,28,177,1,3
	.word	58557
	.byte	10
	.byte	'_Ifx_CPU_DBGTCR_Bits',0,28,180,1,16,4,11
	.byte	'DTA',0,4
	.word	492
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	492
	.byte	31,0,2,35,0,0,29
	.byte	'Ifx_CPU_DBGTCR_Bits',0,28,184,1,3
	.word	58772
	.byte	10
	.byte	'_Ifx_CPU_DCON0_Bits',0,28,187,1,16,4,11
	.byte	'reserved_0',0,4
	.word	492
	.byte	1,31,2,35,0,11
	.byte	'DCBYP',0,4
	.word	492
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	492
	.byte	30,0,2,35,0,0,29
	.byte	'Ifx_CPU_DCON0_Bits',0,28,192,1,3
	.word	58866
	.byte	10
	.byte	'_Ifx_CPU_DCON2_Bits',0,28,195,1,16,4,11
	.byte	'DCACHE_SZE',0,4
	.word	492
	.byte	16,16,2,35,0,11
	.byte	'DSCRATCH_SZE',0,4
	.word	492
	.byte	16,0,2,35,0,0,29
	.byte	'Ifx_CPU_DCON2_Bits',0,28,199,1,3
	.word	58982
	.byte	10
	.byte	'_Ifx_CPU_DCX_Bits',0,28,202,1,16,4,11
	.byte	'reserved_0',0,4
	.word	492
	.byte	6,26,2,35,0,11
	.byte	'DCXValue',0,4
	.word	492
	.byte	26,0,2,35,0,0,29
	.byte	'Ifx_CPU_DCX_Bits',0,28,206,1,3
	.word	59083
	.byte	10
	.byte	'_Ifx_CPU_DEADD_Bits',0,28,209,1,16,4,11
	.byte	'ERROR_ADDRESS',0,4
	.word	492
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_DEADD_Bits',0,28,212,1,3
	.word	59176
	.byte	10
	.byte	'_Ifx_CPU_DIEAR_Bits',0,28,215,1,16,4,11
	.byte	'TA',0,4
	.word	492
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_DIEAR_Bits',0,28,218,1,3
	.word	59256
	.byte	10
	.byte	'_Ifx_CPU_DIETR_Bits',0,28,221,1,16,4,11
	.byte	'IED',0,4
	.word	492
	.byte	1,31,2,35,0,11
	.byte	'IE_T',0,4
	.word	492
	.byte	1,30,2,35,0,11
	.byte	'IE_C',0,4
	.word	492
	.byte	1,29,2,35,0,11
	.byte	'IE_S',0,4
	.word	492
	.byte	1,28,2,35,0,11
	.byte	'IE_BI',0,4
	.word	492
	.byte	1,27,2,35,0,11
	.byte	'E_INFO',0,4
	.word	492
	.byte	6,21,2,35,0,11
	.byte	'IE_DUAL',0,4
	.word	492
	.byte	1,20,2,35,0,11
	.byte	'IE_SP',0,4
	.word	492
	.byte	1,19,2,35,0,11
	.byte	'IE_BS',0,4
	.word	492
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	492
	.byte	18,0,2,35,0,0,29
	.byte	'Ifx_CPU_DIETR_Bits',0,28,233,1,3
	.word	59325
	.byte	10
	.byte	'_Ifx_CPU_DMS_Bits',0,28,236,1,16,4,11
	.byte	'reserved_0',0,4
	.word	492
	.byte	1,31,2,35,0,11
	.byte	'DMSValue',0,4
	.word	492
	.byte	31,0,2,35,0,0,29
	.byte	'Ifx_CPU_DMS_Bits',0,28,240,1,3
	.word	59554
	.byte	10
	.byte	'_Ifx_CPU_DPR_L_Bits',0,28,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	492
	.byte	3,29,2,35,0,11
	.byte	'LOWBND',0,4
	.word	492
	.byte	29,0,2,35,0,0,29
	.byte	'Ifx_CPU_DPR_L_Bits',0,28,247,1,3
	.word	59647
	.byte	10
	.byte	'_Ifx_CPU_DPR_U_Bits',0,28,250,1,16,4,11
	.byte	'reserved_0',0,4
	.word	492
	.byte	3,29,2,35,0,11
	.byte	'UPPBND',0,4
	.word	492
	.byte	29,0,2,35,0,0,29
	.byte	'Ifx_CPU_DPR_U_Bits',0,28,254,1,3
	.word	59742
	.byte	10
	.byte	'_Ifx_CPU_DPRE_Bits',0,28,129,2,16,4,11
	.byte	'RE',0,4
	.word	492
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	492
	.byte	16,0,2,35,0,0,29
	.byte	'Ifx_CPU_DPRE_Bits',0,28,133,2,3
	.word	59837
	.byte	10
	.byte	'_Ifx_CPU_DPWE_Bits',0,28,136,2,16,4,11
	.byte	'WE',0,4
	.word	492
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	492
	.byte	16,0,2,35,0,0,29
	.byte	'Ifx_CPU_DPWE_Bits',0,28,140,2,3
	.word	59927
	.byte	10
	.byte	'_Ifx_CPU_DSTR_Bits',0,28,143,2,16,4,11
	.byte	'SRE',0,4
	.word	492
	.byte	1,31,2,35,0,11
	.byte	'GAE',0,4
	.word	492
	.byte	1,30,2,35,0,11
	.byte	'LBE',0,4
	.word	492
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	492
	.byte	3,26,2,35,0,11
	.byte	'CRE',0,4
	.word	492
	.byte	1,25,2,35,0,11
	.byte	'reserved_7',0,4
	.word	492
	.byte	7,18,2,35,0,11
	.byte	'DTME',0,4
	.word	492
	.byte	1,17,2,35,0,11
	.byte	'LOE',0,4
	.word	492
	.byte	1,16,2,35,0,11
	.byte	'SDE',0,4
	.word	492
	.byte	1,15,2,35,0,11
	.byte	'SCE',0,4
	.word	492
	.byte	1,14,2,35,0,11
	.byte	'CAC',0,4
	.word	492
	.byte	1,13,2,35,0,11
	.byte	'MPE',0,4
	.word	492
	.byte	1,12,2,35,0,11
	.byte	'CLE',0,4
	.word	492
	.byte	1,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	492
	.byte	3,8,2,35,0,11
	.byte	'ALN',0,4
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	492
	.byte	7,0,2,35,0,0,29
	.byte	'Ifx_CPU_DSTR_Bits',0,28,161,2,3
	.word	60017
	.byte	10
	.byte	'_Ifx_CPU_EXEVT_Bits',0,28,164,2,16,4,11
	.byte	'EVTA',0,4
	.word	492
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	492
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	492
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	492
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	492
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	492
	.byte	24,0,2,35,0,0,29
	.byte	'Ifx_CPU_EXEVT_Bits',0,28,172,2,3
	.word	60341
	.byte	10
	.byte	'_Ifx_CPU_FCX_Bits',0,28,175,2,16,4,11
	.byte	'FCXO',0,4
	.word	492
	.byte	16,16,2,35,0,11
	.byte	'FCXS',0,4
	.word	492
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	492
	.byte	12,0,2,35,0,0,29
	.byte	'Ifx_CPU_FCX_Bits',0,28,180,2,3
	.word	60495
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_CON_Bits',0,28,183,2,16,4,11
	.byte	'TST',0,4
	.word	492
	.byte	1,31,2,35,0,11
	.byte	'TCL',0,4
	.word	492
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	492
	.byte	6,24,2,35,0,11
	.byte	'RM',0,4
	.word	492
	.byte	2,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	492
	.byte	8,14,2,35,0,11
	.byte	'FXE',0,4
	.word	492
	.byte	1,13,2,35,0,11
	.byte	'FUE',0,4
	.word	492
	.byte	1,12,2,35,0,11
	.byte	'FZE',0,4
	.word	492
	.byte	1,11,2,35,0,11
	.byte	'FVE',0,4
	.word	492
	.byte	1,10,2,35,0,11
	.byte	'FIE',0,4
	.word	492
	.byte	1,9,2,35,0,11
	.byte	'reserved_23',0,4
	.word	492
	.byte	3,6,2,35,0,11
	.byte	'FX',0,4
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'FU',0,4
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'FZ',0,4
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'FV',0,4
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'FI',0,4
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'reserved_31',0,4
	.word	492
	.byte	1,0,2,35,0,0,29
	.byte	'Ifx_CPU_FPU_TRAP_CON_Bits',0,28,202,2,3
	.word	60601
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_OPC_Bits',0,28,205,2,16,4,11
	.byte	'OPC',0,4
	.word	492
	.byte	8,24,2,35,0,11
	.byte	'FMT',0,4
	.word	492
	.byte	1,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	492
	.byte	7,16,2,35,0,11
	.byte	'DREG',0,4
	.word	492
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	492
	.byte	12,0,2,35,0,0,29
	.byte	'Ifx_CPU_FPU_TRAP_OPC_Bits',0,28,212,2,3
	.word	60950
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_PC_Bits',0,28,215,2,16,4,11
	.byte	'PC',0,4
	.word	492
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_FPU_TRAP_PC_Bits',0,28,218,2,3
	.word	61110
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC1_Bits',0,28,221,2,16,4,11
	.byte	'SRC1',0,4
	.word	492
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_FPU_TRAP_SRC1_Bits',0,28,224,2,3
	.word	61191
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC2_Bits',0,28,227,2,16,4,11
	.byte	'SRC2',0,4
	.word	492
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_FPU_TRAP_SRC2_Bits',0,28,230,2,3
	.word	61278
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC3_Bits',0,28,233,2,16,4,11
	.byte	'SRC3',0,4
	.word	492
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_FPU_TRAP_SRC3_Bits',0,28,236,2,3
	.word	61365
	.byte	10
	.byte	'_Ifx_CPU_ICNT_Bits',0,28,239,2,16,4,11
	.byte	'CountValue',0,4
	.word	492
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	492
	.byte	1,0,2,35,0,0,29
	.byte	'Ifx_CPU_ICNT_Bits',0,28,243,2,3
	.word	61452
	.byte	10
	.byte	'_Ifx_CPU_ICR_Bits',0,28,246,2,16,4,11
	.byte	'CCPN',0,4
	.word	492
	.byte	10,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	492
	.byte	5,17,2,35,0,11
	.byte	'IE',0,4
	.word	492
	.byte	1,16,2,35,0,11
	.byte	'PIPN',0,4
	.word	492
	.byte	10,6,2,35,0,11
	.byte	'reserved_26',0,4
	.word	492
	.byte	6,0,2,35,0,0,29
	.byte	'Ifx_CPU_ICR_Bits',0,28,253,2,3
	.word	61543
	.byte	10
	.byte	'_Ifx_CPU_ISP_Bits',0,28,128,3,16,4,11
	.byte	'ISP',0,4
	.word	492
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_ISP_Bits',0,28,131,3,3
	.word	61686
	.byte	10
	.byte	'_Ifx_CPU_LCX_Bits',0,28,134,3,16,4,11
	.byte	'LCXO',0,4
	.word	492
	.byte	16,16,2,35,0,11
	.byte	'LCXS',0,4
	.word	492
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	492
	.byte	12,0,2,35,0,0,29
	.byte	'Ifx_CPU_LCX_Bits',0,28,139,3,3
	.word	61752
	.byte	10
	.byte	'_Ifx_CPU_M1CNT_Bits',0,28,142,3,16,4,11
	.byte	'CountValue',0,4
	.word	492
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	492
	.byte	1,0,2,35,0,0,29
	.byte	'Ifx_CPU_M1CNT_Bits',0,28,146,3,3
	.word	61858
	.byte	10
	.byte	'_Ifx_CPU_M2CNT_Bits',0,28,149,3,16,4,11
	.byte	'CountValue',0,4
	.word	492
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	492
	.byte	1,0,2,35,0,0,29
	.byte	'Ifx_CPU_M2CNT_Bits',0,28,153,3,3
	.word	61951
	.byte	10
	.byte	'_Ifx_CPU_M3CNT_Bits',0,28,156,3,16,4,11
	.byte	'CountValue',0,4
	.word	492
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	492
	.byte	1,0,2,35,0,0,29
	.byte	'Ifx_CPU_M3CNT_Bits',0,28,160,3,3
	.word	62044
	.byte	10
	.byte	'_Ifx_CPU_PC_Bits',0,28,163,3,16,4,11
	.byte	'reserved_0',0,4
	.word	492
	.byte	1,31,2,35,0,11
	.byte	'PC',0,4
	.word	492
	.byte	31,0,2,35,0,0,29
	.byte	'Ifx_CPU_PC_Bits',0,28,167,3,3
	.word	62137
	.byte	10
	.byte	'_Ifx_CPU_PCON0_Bits',0,28,170,3,16,4,11
	.byte	'reserved_0',0,4
	.word	492
	.byte	1,31,2,35,0,11
	.byte	'PCBYP',0,4
	.word	492
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	492
	.byte	30,0,2,35,0,0,29
	.byte	'Ifx_CPU_PCON0_Bits',0,28,175,3,3
	.word	62222
	.byte	10
	.byte	'_Ifx_CPU_PCON1_Bits',0,28,178,3,16,4,11
	.byte	'PCINV',0,4
	.word	492
	.byte	1,31,2,35,0,11
	.byte	'PBINV',0,4
	.word	492
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	492
	.byte	30,0,2,35,0,0,29
	.byte	'Ifx_CPU_PCON1_Bits',0,28,183,3,3
	.word	62338
	.byte	10
	.byte	'_Ifx_CPU_PCON2_Bits',0,28,186,3,16,4,11
	.byte	'PCACHE_SZE',0,4
	.word	492
	.byte	16,16,2,35,0,11
	.byte	'PSCRATCH_SZE',0,4
	.word	492
	.byte	16,0,2,35,0,0,29
	.byte	'Ifx_CPU_PCON2_Bits',0,28,190,3,3
	.word	62449
	.byte	10
	.byte	'_Ifx_CPU_PCXI_Bits',0,28,193,3,16,4,11
	.byte	'PCXO',0,4
	.word	492
	.byte	16,16,2,35,0,11
	.byte	'PCXS',0,4
	.word	492
	.byte	4,12,2,35,0,11
	.byte	'UL',0,4
	.word	492
	.byte	1,11,2,35,0,11
	.byte	'PIE',0,4
	.word	492
	.byte	1,10,2,35,0,11
	.byte	'PCPN',0,4
	.word	492
	.byte	10,0,2,35,0,0,29
	.byte	'Ifx_CPU_PCXI_Bits',0,28,200,3,3
	.word	62550
	.byte	10
	.byte	'_Ifx_CPU_PIEAR_Bits',0,28,203,3,16,4,11
	.byte	'TA',0,4
	.word	492
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_PIEAR_Bits',0,28,206,3,3
	.word	62680
	.byte	10
	.byte	'_Ifx_CPU_PIETR_Bits',0,28,209,3,16,4,11
	.byte	'IED',0,4
	.word	492
	.byte	1,31,2,35,0,11
	.byte	'IE_T',0,4
	.word	492
	.byte	1,30,2,35,0,11
	.byte	'IE_C',0,4
	.word	492
	.byte	1,29,2,35,0,11
	.byte	'IE_S',0,4
	.word	492
	.byte	1,28,2,35,0,11
	.byte	'IE_BI',0,4
	.word	492
	.byte	1,27,2,35,0,11
	.byte	'E_INFO',0,4
	.word	492
	.byte	6,21,2,35,0,11
	.byte	'IE_DUAL',0,4
	.word	492
	.byte	1,20,2,35,0,11
	.byte	'IE_SP',0,4
	.word	492
	.byte	1,19,2,35,0,11
	.byte	'IE_BS',0,4
	.word	492
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	492
	.byte	18,0,2,35,0,0,29
	.byte	'Ifx_CPU_PIETR_Bits',0,28,221,3,3
	.word	62749
	.byte	10
	.byte	'_Ifx_CPU_PMA0_Bits',0,28,224,3,16,4,11
	.byte	'reserved_0',0,4
	.word	492
	.byte	13,19,2,35,0,11
	.byte	'DAC',0,4
	.word	492
	.byte	3,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	492
	.byte	16,0,2,35,0,0,29
	.byte	'Ifx_CPU_PMA0_Bits',0,28,229,3,3
	.word	62978
	.byte	10
	.byte	'_Ifx_CPU_PMA1_Bits',0,28,232,3,16,4,11
	.byte	'reserved_0',0,4
	.word	492
	.byte	14,18,2,35,0,11
	.byte	'CAC',0,4
	.word	492
	.byte	2,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	492
	.byte	16,0,2,35,0,0,29
	.byte	'Ifx_CPU_PMA1_Bits',0,28,237,3,3
	.word	63091
	.byte	10
	.byte	'_Ifx_CPU_PMA2_Bits',0,28,240,3,16,4,11
	.byte	'PSI',0,4
	.word	492
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	492
	.byte	16,0,2,35,0,0,29
	.byte	'Ifx_CPU_PMA2_Bits',0,28,244,3,3
	.word	63204
	.byte	10
	.byte	'_Ifx_CPU_PSTR_Bits',0,28,247,3,16,4,11
	.byte	'FRE',0,4
	.word	492
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	492
	.byte	1,30,2,35,0,11
	.byte	'FBE',0,4
	.word	492
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	492
	.byte	9,20,2,35,0,11
	.byte	'FPE',0,4
	.word	492
	.byte	1,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	492
	.byte	1,18,2,35,0,11
	.byte	'FME',0,4
	.word	492
	.byte	1,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	492
	.byte	17,0,2,35,0,0,29
	.byte	'Ifx_CPU_PSTR_Bits',0,28,129,4,3
	.word	63295
	.byte	10
	.byte	'_Ifx_CPU_PSW_Bits',0,28,132,4,16,4,11
	.byte	'CDC',0,4
	.word	492
	.byte	7,25,2,35,0,11
	.byte	'CDE',0,4
	.word	492
	.byte	1,24,2,35,0,11
	.byte	'GW',0,4
	.word	492
	.byte	1,23,2,35,0,11
	.byte	'IS',0,4
	.word	492
	.byte	1,22,2,35,0,11
	.byte	'IO',0,4
	.word	492
	.byte	2,20,2,35,0,11
	.byte	'PRS',0,4
	.word	492
	.byte	2,18,2,35,0,11
	.byte	'S',0,4
	.word	492
	.byte	1,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	492
	.byte	12,5,2,35,0,11
	.byte	'SAV',0,4
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'AV',0,4
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'SV',0,4
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'V',0,4
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'C',0,4
	.word	492
	.byte	1,0,2,35,0,0,29
	.byte	'Ifx_CPU_PSW_Bits',0,28,147,4,3
	.word	63498
	.byte	10
	.byte	'_Ifx_CPU_SEGEN_Bits',0,28,150,4,16,4,11
	.byte	'ADFLIP',0,4
	.word	492
	.byte	8,24,2,35,0,11
	.byte	'ADTYPE',0,4
	.word	492
	.byte	2,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	492
	.byte	21,1,2,35,0,11
	.byte	'AE',0,4
	.word	492
	.byte	1,0,2,35,0,0,29
	.byte	'Ifx_CPU_SEGEN_Bits',0,28,156,4,3
	.word	63741
	.byte	10
	.byte	'_Ifx_CPU_SMACON_Bits',0,28,159,4,16,4,11
	.byte	'PC',0,4
	.word	492
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	492
	.byte	1,30,2,35,0,11
	.byte	'PT',0,4
	.word	492
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	492
	.byte	5,24,2,35,0,11
	.byte	'DC',0,4
	.word	492
	.byte	1,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	492
	.byte	1,22,2,35,0,11
	.byte	'DT',0,4
	.word	492
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	492
	.byte	13,8,2,35,0,11
	.byte	'IODT',0,4
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	492
	.byte	7,0,2,35,0,0,29
	.byte	'Ifx_CPU_SMACON_Bits',0,28,171,4,3
	.word	63869
	.byte	10
	.byte	'_Ifx_CPU_SPROT_ACCENA_Bits',0,28,174,4,16,4,11
	.byte	'EN',0,4
	.word	469
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_SPROT_ACCENA_Bits',0,28,177,4,3
	.word	64110
	.byte	10
	.byte	'_Ifx_CPU_SPROT_ACCENB_Bits',0,28,180,4,16,4,11
	.byte	'reserved_0',0,4
	.word	469
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_SPROT_ACCENB_Bits',0,28,183,4,3
	.word	64193
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,28,186,4,16,4,11
	.byte	'EN',0,4
	.word	469
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,28,189,4,3
	.word	64284
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,28,192,4,16,4,11
	.byte	'reserved_0',0,4
	.word	469
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,28,195,4,3
	.word	64375
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_LA_Bits',0,28,198,4,16,4,11
	.byte	'reserved_0',0,1
	.word	644
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	469
	.byte	27,0,2,35,0,0,29
	.byte	'Ifx_CPU_SPROT_RGN_LA_Bits',0,28,202,4,3
	.word	64474
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_UA_Bits',0,28,205,4,16,4,11
	.byte	'reserved_0',0,1
	.word	644
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	469
	.byte	27,0,2,35,0,0,29
	.byte	'Ifx_CPU_SPROT_RGN_UA_Bits',0,28,209,4,3
	.word	64581
	.byte	10
	.byte	'_Ifx_CPU_SWEVT_Bits',0,28,212,4,16,4,11
	.byte	'EVTA',0,4
	.word	492
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	492
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	492
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	492
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	492
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	492
	.byte	24,0,2,35,0,0,29
	.byte	'Ifx_CPU_SWEVT_Bits',0,28,220,4,3
	.word	64688
	.byte	10
	.byte	'_Ifx_CPU_SYSCON_Bits',0,28,223,4,16,4,11
	.byte	'FCDSF',0,4
	.word	492
	.byte	1,31,2,35,0,11
	.byte	'PROTEN',0,4
	.word	492
	.byte	1,30,2,35,0,11
	.byte	'TPROTEN',0,4
	.word	492
	.byte	1,29,2,35,0,11
	.byte	'IS',0,4
	.word	492
	.byte	1,28,2,35,0,11
	.byte	'IT',0,4
	.word	492
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	492
	.byte	27,0,2,35,0,0,29
	.byte	'Ifx_CPU_SYSCON_Bits',0,28,231,4,3
	.word	64842
	.byte	10
	.byte	'_Ifx_CPU_TASK_ASI_Bits',0,28,234,4,16,4,11
	.byte	'ASI',0,4
	.word	492
	.byte	5,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	492
	.byte	27,0,2,35,0,0,29
	.byte	'Ifx_CPU_TASK_ASI_Bits',0,28,238,4,3
	.word	65003
	.byte	10
	.byte	'_Ifx_CPU_TPS_CON_Bits',0,28,241,4,16,4,11
	.byte	'TEXP0',0,4
	.word	492
	.byte	1,31,2,35,0,11
	.byte	'TEXP1',0,4
	.word	492
	.byte	1,30,2,35,0,11
	.byte	'TEXP2',0,4
	.word	492
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	492
	.byte	13,16,2,35,0,11
	.byte	'TTRAP',0,4
	.word	492
	.byte	1,15,2,35,0,11
	.byte	'reserved_17',0,4
	.word	492
	.byte	15,0,2,35,0,0,29
	.byte	'Ifx_CPU_TPS_CON_Bits',0,28,249,4,3
	.word	65101
	.byte	10
	.byte	'_Ifx_CPU_TPS_TIMER_Bits',0,28,252,4,16,4,11
	.byte	'Timer',0,4
	.word	492
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_TPS_TIMER_Bits',0,28,255,4,3
	.word	65273
	.byte	10
	.byte	'_Ifx_CPU_TR_ADR_Bits',0,28,130,5,16,4,11
	.byte	'ADDR',0,4
	.word	492
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_TR_ADR_Bits',0,28,133,5,3
	.word	65353
	.byte	10
	.byte	'_Ifx_CPU_TR_EVT_Bits',0,28,136,5,16,4,11
	.byte	'EVTA',0,4
	.word	492
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	492
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	492
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	492
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	492
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	492
	.byte	4,20,2,35,0,11
	.byte	'TYP',0,4
	.word	492
	.byte	1,19,2,35,0,11
	.byte	'RNG',0,4
	.word	492
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	492
	.byte	1,17,2,35,0,11
	.byte	'ASI_EN',0,4
	.word	492
	.byte	1,16,2,35,0,11
	.byte	'ASI',0,4
	.word	492
	.byte	5,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	492
	.byte	6,5,2,35,0,11
	.byte	'AST',0,4
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'ALD',0,4
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'reserved_29',0,4
	.word	492
	.byte	3,0,2,35,0,0,29
	.byte	'Ifx_CPU_TR_EVT_Bits',0,28,153,5,3
	.word	65426
	.byte	10
	.byte	'_Ifx_CPU_TRIG_ACC_Bits',0,28,156,5,16,4,11
	.byte	'T0',0,4
	.word	492
	.byte	1,31,2,35,0,11
	.byte	'T1',0,4
	.word	492
	.byte	1,30,2,35,0,11
	.byte	'T2',0,4
	.word	492
	.byte	1,29,2,35,0,11
	.byte	'T3',0,4
	.word	492
	.byte	1,28,2,35,0,11
	.byte	'T4',0,4
	.word	492
	.byte	1,27,2,35,0,11
	.byte	'T5',0,4
	.word	492
	.byte	1,26,2,35,0,11
	.byte	'T6',0,4
	.word	492
	.byte	1,25,2,35,0,11
	.byte	'T7',0,4
	.word	492
	.byte	1,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	492
	.byte	24,0,2,35,0,0,29
	.byte	'Ifx_CPU_TRIG_ACC_Bits',0,28,167,5,3
	.word	65744
	.byte	12,28,175,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56962
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_A',0,28,180,5,3
	.word	65939
	.byte	12,28,183,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57023
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_BIV',0,28,188,5,3
	.word	65998
	.byte	12,28,191,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57102
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_BTV',0,28,196,5,3
	.word	66059
	.byte	12,28,199,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57188
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_CCNT',0,28,204,5,3
	.word	66120
	.byte	12,28,207,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57277
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_CCTRL',0,28,212,5,3
	.word	66182
	.byte	12,28,215,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57423
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_COMPAT',0,28,220,5,3
	.word	66245
	.byte	12,28,223,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57550
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_CORE_ID',0,28,228,5,3
	.word	66309
	.byte	12,28,231,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57648
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_CPR_L',0,28,236,5,3
	.word	66374
	.byte	12,28,239,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57741
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_CPR_U',0,28,244,5,3
	.word	66437
	.byte	12,28,247,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57834
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_CPU_ID',0,28,252,5,3
	.word	66500
	.byte	12,28,255,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57941
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_CPXE',0,28,132,6,3
	.word	66564
	.byte	12,28,135,6,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58028
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_CREVT',0,28,140,6,3
	.word	66626
	.byte	12,28,143,6,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58182
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_CUS_ID',0,28,148,6,3
	.word	66689
	.byte	12,28,151,6,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58276
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_D',0,28,156,6,3
	.word	66753
	.byte	12,28,159,6,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58339
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_DATR',0,28,164,6,3
	.word	66812
	.byte	12,28,167,6,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58557
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_DBGSR',0,28,172,6,3
	.word	66874
	.byte	12,28,175,6,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58772
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_DBGTCR',0,28,180,6,3
	.word	66937
	.byte	12,28,183,6,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58866
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_DCON0',0,28,188,6,3
	.word	67001
	.byte	12,28,191,6,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58982
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_DCON2',0,28,196,6,3
	.word	67064
	.byte	12,28,199,6,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59083
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_DCX',0,28,204,6,3
	.word	67127
	.byte	12,28,207,6,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59176
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_DEADD',0,28,212,6,3
	.word	67188
	.byte	12,28,215,6,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59256
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_DIEAR',0,28,220,6,3
	.word	67251
	.byte	12,28,223,6,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59325
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_DIETR',0,28,228,6,3
	.word	67314
	.byte	12,28,231,6,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59554
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_DMS',0,28,236,6,3
	.word	67377
	.byte	12,28,239,6,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59647
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_DPR_L',0,28,244,6,3
	.word	67438
	.byte	12,28,247,6,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59742
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_DPR_U',0,28,252,6,3
	.word	67501
	.byte	12,28,255,6,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59837
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_DPRE',0,28,132,7,3
	.word	67564
	.byte	12,28,135,7,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59927
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_DPWE',0,28,140,7,3
	.word	67626
	.byte	12,28,143,7,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60017
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_DSTR',0,28,148,7,3
	.word	67688
	.byte	12,28,151,7,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60341
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_EXEVT',0,28,156,7,3
	.word	67750
	.byte	12,28,159,7,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60495
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_FCX',0,28,164,7,3
	.word	67813
	.byte	12,28,167,7,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60601
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_FPU_TRAP_CON',0,28,172,7,3
	.word	67874
	.byte	12,28,175,7,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60950
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_FPU_TRAP_OPC',0,28,180,7,3
	.word	67944
	.byte	12,28,183,7,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61110
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_FPU_TRAP_PC',0,28,188,7,3
	.word	68014
	.byte	12,28,191,7,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61191
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_FPU_TRAP_SRC1',0,28,196,7,3
	.word	68083
	.byte	12,28,199,7,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61278
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_FPU_TRAP_SRC2',0,28,204,7,3
	.word	68154
	.byte	12,28,207,7,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61365
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_FPU_TRAP_SRC3',0,28,212,7,3
	.word	68225
	.byte	12,28,215,7,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61452
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_ICNT',0,28,220,7,3
	.word	68296
	.byte	12,28,223,7,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61543
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_ICR',0,28,228,7,3
	.word	68358
	.byte	12,28,231,7,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61686
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_ISP',0,28,236,7,3
	.word	68419
	.byte	12,28,239,7,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61752
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_LCX',0,28,244,7,3
	.word	68480
	.byte	12,28,247,7,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61858
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_M1CNT',0,28,252,7,3
	.word	68541
	.byte	12,28,255,7,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61951
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_M2CNT',0,28,132,8,3
	.word	68604
	.byte	12,28,135,8,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62044
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_M3CNT',0,28,140,8,3
	.word	68667
	.byte	12,28,143,8,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62137
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_PC',0,28,148,8,3
	.word	68730
	.byte	12,28,151,8,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62222
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_PCON0',0,28,156,8,3
	.word	68790
	.byte	12,28,159,8,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62338
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_PCON1',0,28,164,8,3
	.word	68853
	.byte	12,28,167,8,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62449
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_PCON2',0,28,172,8,3
	.word	68916
	.byte	12,28,175,8,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62550
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_PCXI',0,28,180,8,3
	.word	68979
	.byte	12,28,183,8,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62680
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_PIEAR',0,28,188,8,3
	.word	69041
	.byte	12,28,191,8,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62749
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_PIETR',0,28,196,8,3
	.word	69104
	.byte	12,28,199,8,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62978
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_PMA0',0,28,204,8,3
	.word	69167
	.byte	12,28,207,8,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63091
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_PMA1',0,28,212,8,3
	.word	69229
	.byte	12,28,215,8,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63204
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_PMA2',0,28,220,8,3
	.word	69291
	.byte	12,28,223,8,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63295
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_PSTR',0,28,228,8,3
	.word	69353
	.byte	12,28,231,8,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63498
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_PSW',0,28,236,8,3
	.word	69415
	.byte	12,28,239,8,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63741
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_SEGEN',0,28,244,8,3
	.word	69476
	.byte	12,28,247,8,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63869
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_SMACON',0,28,252,8,3
	.word	69539
	.byte	12,28,255,8,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64110
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_SPROT_ACCENA',0,28,132,9,3
	.word	69603
	.byte	12,28,135,9,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64193
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_SPROT_ACCENB',0,28,140,9,3
	.word	69673
	.byte	12,28,143,9,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64284
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA',0,28,148,9,3
	.word	69743
	.byte	12,28,151,9,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64375
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB',0,28,156,9,3
	.word	69817
	.byte	12,28,159,9,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64474
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_SPROT_RGN_LA',0,28,164,9,3
	.word	69891
	.byte	12,28,167,9,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64581
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_SPROT_RGN_UA',0,28,172,9,3
	.word	69961
	.byte	12,28,175,9,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64688
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_SWEVT',0,28,180,9,3
	.word	70031
	.byte	12,28,183,9,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64842
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_SYSCON',0,28,188,9,3
	.word	70094
	.byte	12,28,191,9,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65003
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_TASK_ASI',0,28,196,9,3
	.word	70158
	.byte	12,28,199,9,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65101
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_TPS_CON',0,28,204,9,3
	.word	70224
	.byte	12,28,207,9,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65273
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_TPS_TIMER',0,28,212,9,3
	.word	70289
	.byte	12,28,215,9,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65353
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_TR_ADR',0,28,220,9,3
	.word	70356
	.byte	12,28,223,9,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65426
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_TR_EVT',0,28,228,9,3
	.word	70420
	.byte	12,28,231,9,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65744
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_TRIG_ACC',0,28,236,9,3
	.word	70484
	.byte	10
	.byte	'_Ifx_CPU_CPR',0,28,247,9,25,8,13
	.byte	'L',0
	.word	66374
	.byte	4,2,35,0,13
	.byte	'U',0
	.word	66437
	.byte	4,2,35,4,0,14
	.word	70550
	.byte	29
	.byte	'Ifx_CPU_CPR',0,28,251,9,3
	.word	70592
	.byte	10
	.byte	'_Ifx_CPU_DPR',0,28,254,9,25,8,13
	.byte	'L',0
	.word	67438
	.byte	4,2,35,0,13
	.byte	'U',0
	.word	67501
	.byte	4,2,35,4,0,14
	.word	70618
	.byte	29
	.byte	'Ifx_CPU_DPR',0,28,130,10,3
	.word	70660
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN',0,28,133,10,25,16,13
	.byte	'LA',0
	.word	69891
	.byte	4,2,35,0,13
	.byte	'UA',0
	.word	69961
	.byte	4,2,35,4,13
	.byte	'ACCENA',0
	.word	69743
	.byte	4,2,35,8,13
	.byte	'ACCENB',0
	.word	69817
	.byte	4,2,35,12,0,14
	.word	70686
	.byte	29
	.byte	'Ifx_CPU_SPROT_RGN',0,28,139,10,3
	.word	70768
	.byte	15,12
	.word	70289
	.byte	16,2,0,10
	.byte	'_Ifx_CPU_TPS',0,28,142,10,25,16,13
	.byte	'CON',0
	.word	70224
	.byte	4,2,35,0,13
	.byte	'TIMER',0
	.word	70800
	.byte	12,2,35,4,0,14
	.word	70809
	.byte	29
	.byte	'Ifx_CPU_TPS',0,28,146,10,3
	.word	70857
	.byte	10
	.byte	'_Ifx_CPU_TR',0,28,149,10,25,8,13
	.byte	'EVT',0
	.word	70420
	.byte	4,2,35,0,13
	.byte	'ADR',0
	.word	70356
	.byte	4,2,35,4,0,14
	.word	70883
	.byte	29
	.byte	'Ifx_CPU_TR',0,28,153,10,3
	.word	70928
	.byte	15,176,32
	.word	644
	.byte	16,175,32,0,15,208,223,1
	.word	644
	.byte	16,207,223,1,0,15,248,1
	.word	644
	.byte	16,247,1,0,15,244,29
	.word	644
	.byte	16,243,29,0,15,188,3
	.word	644
	.byte	16,187,3,0,15,232,3
	.word	644
	.byte	16,231,3,0,15,252,23
	.word	644
	.byte	16,251,23,0,15,228,63
	.word	644
	.byte	16,227,63,0,15,128,1
	.word	70618
	.byte	16,15,0,14
	.word	71043
	.byte	15,128,31
	.word	644
	.byte	16,255,30,0,15,64
	.word	70550
	.byte	16,7,0,14
	.word	71069
	.byte	15,192,31
	.word	644
	.byte	16,191,31,0,15,16
	.word	66564
	.byte	16,3,0,15,16
	.word	67564
	.byte	16,3,0,15,16
	.word	67626
	.byte	16,3,0,15,208,7
	.word	644
	.byte	16,207,7,0,14
	.word	70809
	.byte	15,240,23
	.word	644
	.byte	16,239,23,0,15,64
	.word	70883
	.byte	16,7,0,14
	.word	71148
	.byte	15,192,23
	.word	644
	.byte	16,191,23,0,15,232,1
	.word	644
	.byte	16,231,1,0,15,180,1
	.word	644
	.byte	16,179,1,0,15,172,1
	.word	644
	.byte	16,171,1,0,15,64
	.word	66753
	.byte	16,15,0,15,64
	.word	644
	.byte	16,63,0,15,64
	.word	65939
	.byte	16,15,0,10
	.byte	'_Ifx_CPU',0,28,166,10,25,128,128,4,13
	.byte	'reserved_0',0
	.word	70953
	.byte	176,32,2,35,0,13
	.byte	'SEGEN',0
	.word	69476
	.byte	4,3,35,176,32,13
	.byte	'reserved_1034',0
	.word	70964
	.byte	208,223,1,3,35,180,32,13
	.byte	'TASK_ASI',0
	.word	70158
	.byte	4,4,35,132,128,2,13
	.byte	'reserved_8008',0
	.word	70977
	.byte	248,1,4,35,136,128,2,13
	.byte	'PMA0',0
	.word	69167
	.byte	4,4,35,128,130,2,13
	.byte	'PMA1',0
	.word	69229
	.byte	4,4,35,132,130,2,13
	.byte	'PMA2',0
	.word	69291
	.byte	4,4,35,136,130,2,13
	.byte	'reserved_810C',0
	.word	70988
	.byte	244,29,4,35,140,130,2,13
	.byte	'DCON2',0
	.word	67064
	.byte	4,4,35,128,160,2,13
	.byte	'reserved_9004',0
	.word	4281
	.byte	8,4,35,132,160,2,13
	.byte	'SMACON',0
	.word	69539
	.byte	4,4,35,140,160,2,13
	.byte	'DSTR',0
	.word	67688
	.byte	4,4,35,144,160,2,13
	.byte	'reserved_9014',0
	.word	2462
	.byte	4,4,35,148,160,2,13
	.byte	'DATR',0
	.word	66812
	.byte	4,4,35,152,160,2,13
	.byte	'DEADD',0
	.word	67188
	.byte	4,4,35,156,160,2,13
	.byte	'DIEAR',0
	.word	67251
	.byte	4,4,35,160,160,2,13
	.byte	'DIETR',0
	.word	67314
	.byte	4,4,35,164,160,2,13
	.byte	'reserved_9028',0
	.word	3652
	.byte	24,4,35,168,160,2,13
	.byte	'DCON0',0
	.word	67001
	.byte	4,4,35,192,160,2,13
	.byte	'reserved_9044',0
	.word	70999
	.byte	188,3,4,35,196,160,2,13
	.byte	'PSTR',0
	.word	69353
	.byte	4,4,35,128,164,2,13
	.byte	'PCON1',0
	.word	68853
	.byte	4,4,35,132,164,2,13
	.byte	'PCON2',0
	.word	68916
	.byte	4,4,35,136,164,2,13
	.byte	'PCON0',0
	.word	68790
	.byte	4,4,35,140,164,2,13
	.byte	'PIEAR',0
	.word	69041
	.byte	4,4,35,144,164,2,13
	.byte	'PIETR',0
	.word	69104
	.byte	4,4,35,148,164,2,13
	.byte	'reserved_9218',0
	.word	71010
	.byte	232,3,4,35,152,164,2,13
	.byte	'COMPAT',0
	.word	66245
	.byte	4,4,35,128,168,2,13
	.byte	'reserved_9404',0
	.word	71021
	.byte	252,23,4,35,132,168,2,13
	.byte	'FPU_TRAP_CON',0
	.word	67874
	.byte	4,4,35,128,192,2,13
	.byte	'FPU_TRAP_PC',0
	.word	68014
	.byte	4,4,35,132,192,2,13
	.byte	'FPU_TRAP_OPC',0
	.word	67944
	.byte	4,4,35,136,192,2,13
	.byte	'reserved_A00C',0
	.word	2462
	.byte	4,4,35,140,192,2,13
	.byte	'FPU_TRAP_SRC1',0
	.word	68083
	.byte	4,4,35,144,192,2,13
	.byte	'FPU_TRAP_SRC2',0
	.word	68154
	.byte	4,4,35,148,192,2,13
	.byte	'FPU_TRAP_SRC3',0
	.word	68225
	.byte	4,4,35,152,192,2,13
	.byte	'reserved_A01C',0
	.word	71032
	.byte	228,63,4,35,156,192,2,13
	.byte	'DPR',0
	.word	71053
	.byte	128,1,4,35,128,128,3,13
	.byte	'reserved_C080',0
	.word	71058
	.byte	128,31,4,35,128,129,3,13
	.byte	'CPR',0
	.word	71078
	.byte	64,4,35,128,160,3,13
	.byte	'reserved_D040',0
	.word	71083
	.byte	192,31,4,35,192,160,3,13
	.byte	'CPXE',0
	.word	71094
	.byte	16,4,35,128,192,3,13
	.byte	'DPRE',0
	.word	71103
	.byte	16,4,35,144,192,3,13
	.byte	'DPWE',0
	.word	71112
	.byte	16,4,35,160,192,3,13
	.byte	'reserved_E030',0
	.word	71121
	.byte	208,7,4,35,176,192,3,13
	.byte	'TPS',0
	.word	71132
	.byte	16,4,35,128,200,3,13
	.byte	'reserved_E410',0
	.word	71137
	.byte	240,23,4,35,144,200,3,13
	.byte	'TR',0
	.word	71157
	.byte	64,4,35,128,224,3,13
	.byte	'reserved_F040',0
	.word	71162
	.byte	192,23,4,35,192,224,3,13
	.byte	'CCTRL',0
	.word	66182
	.byte	4,4,35,128,248,3,13
	.byte	'CCNT',0
	.word	66120
	.byte	4,4,35,132,248,3,13
	.byte	'ICNT',0
	.word	68296
	.byte	4,4,35,136,248,3,13
	.byte	'M1CNT',0
	.word	68541
	.byte	4,4,35,140,248,3,13
	.byte	'M2CNT',0
	.word	68604
	.byte	4,4,35,144,248,3,13
	.byte	'M3CNT',0
	.word	68667
	.byte	4,4,35,148,248,3,13
	.byte	'reserved_FC18',0
	.word	71173
	.byte	232,1,4,35,152,248,3,13
	.byte	'DBGSR',0
	.word	66874
	.byte	4,4,35,128,250,3,13
	.byte	'reserved_FD04',0
	.word	2462
	.byte	4,4,35,132,250,3,13
	.byte	'EXEVT',0
	.word	67750
	.byte	4,4,35,136,250,3,13
	.byte	'CREVT',0
	.word	66626
	.byte	4,4,35,140,250,3,13
	.byte	'SWEVT',0
	.word	70031
	.byte	4,4,35,144,250,3,13
	.byte	'reserved_FD14',0
	.word	52554
	.byte	28,4,35,148,250,3,13
	.byte	'TRIG_ACC',0
	.word	70484
	.byte	4,4,35,176,250,3,13
	.byte	'reserved_FD34',0
	.word	4621
	.byte	12,4,35,180,250,3,13
	.byte	'DMS',0
	.word	67377
	.byte	4,4,35,192,250,3,13
	.byte	'DCX',0
	.word	67127
	.byte	4,4,35,196,250,3,13
	.byte	'DBGTCR',0
	.word	66937
	.byte	4,4,35,200,250,3,13
	.byte	'reserved_FD4C',0
	.word	71184
	.byte	180,1,4,35,204,250,3,13
	.byte	'PCXI',0
	.word	68979
	.byte	4,4,35,128,252,3,13
	.byte	'PSW',0
	.word	69415
	.byte	4,4,35,132,252,3,13
	.byte	'PC',0
	.word	68730
	.byte	4,4,35,136,252,3,13
	.byte	'reserved_FE0C',0
	.word	4281
	.byte	8,4,35,140,252,3,13
	.byte	'SYSCON',0
	.word	70094
	.byte	4,4,35,148,252,3,13
	.byte	'CPU_ID',0
	.word	66500
	.byte	4,4,35,152,252,3,13
	.byte	'CORE_ID',0
	.word	66309
	.byte	4,4,35,156,252,3,13
	.byte	'BIV',0
	.word	65998
	.byte	4,4,35,160,252,3,13
	.byte	'BTV',0
	.word	66059
	.byte	4,4,35,164,252,3,13
	.byte	'ISP',0
	.word	68419
	.byte	4,4,35,168,252,3,13
	.byte	'ICR',0
	.word	68358
	.byte	4,4,35,172,252,3,13
	.byte	'reserved_FE30',0
	.word	4281
	.byte	8,4,35,176,252,3,13
	.byte	'FCX',0
	.word	67813
	.byte	4,4,35,184,252,3,13
	.byte	'LCX',0
	.word	68480
	.byte	4,4,35,188,252,3,13
	.byte	'reserved_FE40',0
	.word	52545
	.byte	16,4,35,192,252,3,13
	.byte	'CUS_ID',0
	.word	66689
	.byte	4,4,35,208,252,3,13
	.byte	'reserved_FE54',0
	.word	71195
	.byte	172,1,4,35,212,252,3,13
	.byte	'D',0
	.word	71206
	.byte	64,4,35,128,254,3,13
	.byte	'reserved_FF40',0
	.word	71215
	.byte	64,4,35,192,254,3,13
	.byte	'A',0
	.word	71224
	.byte	64,4,35,128,255,3,13
	.byte	'reserved_FFC0',0
	.word	71215
	.byte	64,4,35,192,255,3,0,14
	.word	71233
	.byte	29
	.byte	'Ifx_CPU',0,28,130,11,3
	.word	73024
	.byte	17,8,127,9,1,18
	.byte	'IfxCpu_Id_0',0,0,18
	.byte	'IfxCpu_Id_1',0,1,18
	.byte	'IfxCpu_Id_none',0,2,0,29
	.byte	'IfxCpu_Id',0,8,132,1,3
	.word	73046
	.byte	29
	.byte	'IfxCpu_ResourceCpu',0,8,161,1,3
	.word	9983
	.byte	29
	.byte	'Ifx_SRC_SRCR_Bits',0,11,62,3
	.word	10576
	.byte	29
	.byte	'Ifx_SRC_SRCR',0,11,75,3
	.word	10866
	.byte	10
	.byte	'_Ifx_SRC_AGBT',0,11,86,25,4,13
	.byte	'SR',0
	.word	10866
	.byte	4,2,35,0,0,14
	.word	73191
	.byte	29
	.byte	'Ifx_SRC_AGBT',0,11,89,3
	.word	73223
	.byte	10
	.byte	'_Ifx_SRC_ASCLIN',0,11,92,25,12,13
	.byte	'TX',0
	.word	10866
	.byte	4,2,35,0,13
	.byte	'RX',0
	.word	10866
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	10866
	.byte	4,2,35,8,0,14
	.word	73249
	.byte	29
	.byte	'Ifx_SRC_ASCLIN',0,11,97,3
	.word	73308
	.byte	10
	.byte	'_Ifx_SRC_BCUSPB',0,11,100,25,4,13
	.byte	'SBSRC',0
	.word	10866
	.byte	4,2,35,0,0,14
	.word	73336
	.byte	29
	.byte	'Ifx_SRC_BCUSPB',0,11,103,3
	.word	73373
	.byte	15,64
	.word	10866
	.byte	16,15,0,10
	.byte	'_Ifx_SRC_CAN',0,11,106,25,64,13
	.byte	'INT',0
	.word	73401
	.byte	64,2,35,0,0,14
	.word	73410
	.byte	29
	.byte	'Ifx_SRC_CAN',0,11,109,3
	.word	73442
	.byte	10
	.byte	'_Ifx_SRC_CCU6',0,11,112,25,16,13
	.byte	'SR0',0
	.word	10866
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10866
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	10866
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	10866
	.byte	4,2,35,12,0,14
	.word	73467
	.byte	29
	.byte	'Ifx_SRC_CCU6',0,11,118,3
	.word	73539
	.byte	15,8
	.word	10866
	.byte	16,1,0,10
	.byte	'_Ifx_SRC_CERBERUS',0,11,121,25,8,13
	.byte	'SR',0
	.word	73565
	.byte	8,2,35,0,0,14
	.word	73574
	.byte	29
	.byte	'Ifx_SRC_CERBERUS',0,11,124,3
	.word	73610
	.byte	10
	.byte	'_Ifx_SRC_CIF',0,11,127,25,16,13
	.byte	'MI',0
	.word	10866
	.byte	4,2,35,0,13
	.byte	'MIEP',0
	.word	10866
	.byte	4,2,35,4,13
	.byte	'ISP',0
	.word	10866
	.byte	4,2,35,8,13
	.byte	'MJPEG',0
	.word	10866
	.byte	4,2,35,12,0,14
	.word	73640
	.byte	29
	.byte	'Ifx_SRC_CIF',0,11,133,1,3
	.word	73713
	.byte	10
	.byte	'_Ifx_SRC_CPU',0,11,136,1,25,4,13
	.byte	'SBSRC',0
	.word	10866
	.byte	4,2,35,0,0,14
	.word	73739
	.byte	29
	.byte	'Ifx_SRC_CPU',0,11,139,1,3
	.word	73774
	.byte	15,192,1
	.word	10866
	.byte	16,47,0,10
	.byte	'_Ifx_SRC_DMA',0,11,142,1,25,208,1,13
	.byte	'ERR',0
	.word	10866
	.byte	4,2,35,0,13
	.byte	'reserved_4',0
	.word	4621
	.byte	12,2,35,4,13
	.byte	'CH',0
	.word	73800
	.byte	192,1,2,35,16,0,14
	.word	73810
	.byte	29
	.byte	'Ifx_SRC_DMA',0,11,147,1,3
	.word	73877
	.byte	10
	.byte	'_Ifx_SRC_DSADC',0,11,150,1,25,8,13
	.byte	'SRM',0
	.word	10866
	.byte	4,2,35,0,13
	.byte	'SRA',0
	.word	10866
	.byte	4,2,35,4,0,14
	.word	73903
	.byte	29
	.byte	'Ifx_SRC_DSADC',0,11,154,1,3
	.word	73951
	.byte	10
	.byte	'_Ifx_SRC_EMEM',0,11,157,1,25,4,13
	.byte	'SR',0
	.word	10866
	.byte	4,2,35,0,0,14
	.word	73979
	.byte	29
	.byte	'Ifx_SRC_EMEM',0,11,160,1,3
	.word	74012
	.byte	10
	.byte	'_Ifx_SRC_ERAY',0,11,163,1,25,80,13
	.byte	'INT',0
	.word	73565
	.byte	8,2,35,0,13
	.byte	'TINT',0
	.word	73565
	.byte	8,2,35,8,13
	.byte	'NDAT',0
	.word	73565
	.byte	8,2,35,16,13
	.byte	'MBSC',0
	.word	73565
	.byte	8,2,35,24,13
	.byte	'OBUSY',0
	.word	10866
	.byte	4,2,35,32,13
	.byte	'IBUSY',0
	.word	10866
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	52563
	.byte	40,2,35,40,0,14
	.word	74039
	.byte	29
	.byte	'Ifx_SRC_ERAY',0,11,172,1,3
	.word	74166
	.byte	10
	.byte	'_Ifx_SRC_ETH',0,11,175,1,25,4,13
	.byte	'SR',0
	.word	10866
	.byte	4,2,35,0,0,14
	.word	74193
	.byte	29
	.byte	'Ifx_SRC_ETH',0,11,178,1,3
	.word	74225
	.byte	10
	.byte	'_Ifx_SRC_FCE',0,11,181,1,25,4,13
	.byte	'SR',0
	.word	10866
	.byte	4,2,35,0,0,14
	.word	74251
	.byte	29
	.byte	'Ifx_SRC_FCE',0,11,184,1,3
	.word	74283
	.byte	10
	.byte	'_Ifx_SRC_FFT',0,11,187,1,25,12,13
	.byte	'DONE',0
	.word	10866
	.byte	4,2,35,0,13
	.byte	'ERR',0
	.word	10866
	.byte	4,2,35,4,13
	.byte	'RFS',0
	.word	10866
	.byte	4,2,35,8,0,14
	.word	74309
	.byte	29
	.byte	'Ifx_SRC_FFT',0,11,192,1,3
	.word	74369
	.byte	10
	.byte	'_Ifx_SRC_GPSR',0,11,195,1,25,32,13
	.byte	'SR0',0
	.word	10866
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10866
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	10866
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	10866
	.byte	4,2,35,12,13
	.byte	'reserved_10',0
	.word	52545
	.byte	16,2,35,16,0,14
	.word	74395
	.byte	29
	.byte	'Ifx_SRC_GPSR',0,11,202,1,3
	.word	74489
	.byte	10
	.byte	'_Ifx_SRC_GPT12',0,11,205,1,25,48,13
	.byte	'CIRQ',0
	.word	10866
	.byte	4,2,35,0,13
	.byte	'T2',0
	.word	10866
	.byte	4,2,35,4,13
	.byte	'T3',0
	.word	10866
	.byte	4,2,35,8,13
	.byte	'T4',0
	.word	10866
	.byte	4,2,35,12,13
	.byte	'T5',0
	.word	10866
	.byte	4,2,35,16,13
	.byte	'T6',0
	.word	10866
	.byte	4,2,35,20,13
	.byte	'reserved_18',0
	.word	3652
	.byte	24,2,35,24,0,14
	.word	74516
	.byte	29
	.byte	'Ifx_SRC_GPT12',0,11,214,1,3
	.word	74633
	.byte	15,12
	.word	10866
	.byte	16,2,0,15,32
	.word	10866
	.byte	16,7,0,15,32
	.word	74670
	.byte	16,0,0,15,88
	.word	644
	.byte	16,87,0,15,108
	.word	10866
	.byte	16,26,0,15,96
	.word	644
	.byte	16,95,0,15,96
	.word	74670
	.byte	16,2,0,15,160,3
	.word	644
	.byte	16,159,3,0,15,64
	.word	74670
	.byte	16,1,0,15,192,3
	.word	644
	.byte	16,191,3,0,15,16
	.word	10866
	.byte	16,3,0,15,64
	.word	74755
	.byte	16,3,0,15,192,2
	.word	644
	.byte	16,191,2,0,15,52
	.word	644
	.byte	16,51,0,10
	.byte	'_Ifx_SRC_GTM',0,11,217,1,25,204,18,13
	.byte	'AEIIRQ',0
	.word	10866
	.byte	4,2,35,0,13
	.byte	'ARUIRQ',0
	.word	74661
	.byte	12,2,35,4,13
	.byte	'reserved_10',0
	.word	2462
	.byte	4,2,35,16,13
	.byte	'BRCIRQ',0
	.word	10866
	.byte	4,2,35,20,13
	.byte	'CMPIRQ',0
	.word	10866
	.byte	4,2,35,24,13
	.byte	'SPEIRQ',0
	.word	73565
	.byte	8,2,35,28,13
	.byte	'reserved_24',0
	.word	4281
	.byte	8,2,35,36,13
	.byte	'PSM',0
	.word	74679
	.byte	32,2,35,44,13
	.byte	'reserved_4C',0
	.word	74688
	.byte	88,2,35,76,13
	.byte	'DPLL',0
	.word	74697
	.byte	108,3,35,164,1,13
	.byte	'reserved_110',0
	.word	74706
	.byte	96,3,35,144,2,13
	.byte	'ERR',0
	.word	10866
	.byte	4,3,35,240,2,13
	.byte	'reserved_174',0
	.word	4621
	.byte	12,3,35,244,2,13
	.byte	'TIM',0
	.word	74715
	.byte	96,3,35,128,3,13
	.byte	'reserved_1E0',0
	.word	74724
	.byte	160,3,3,35,224,3,13
	.byte	'MCS',0
	.word	74715
	.byte	96,3,35,128,7,13
	.byte	'reserved_3E0',0
	.word	74724
	.byte	160,3,3,35,224,7,13
	.byte	'TOM',0
	.word	74735
	.byte	64,3,35,128,11,13
	.byte	'reserved_5C0',0
	.word	74744
	.byte	192,3,3,35,192,11,13
	.byte	'ATOM',0
	.word	74764
	.byte	64,3,35,128,15,13
	.byte	'reserved_7C0',0
	.word	74773
	.byte	192,2,3,35,192,15,13
	.byte	'MCSW0',0
	.word	74661
	.byte	12,3,35,128,18,13
	.byte	'reserved_90C',0
	.word	74784
	.byte	52,3,35,140,18,13
	.byte	'MCSW1',0
	.word	74661
	.byte	12,3,35,192,18,0,14
	.word	74793
	.byte	29
	.byte	'Ifx_SRC_GTM',0,11,243,1,3
	.word	75253
	.byte	10
	.byte	'_Ifx_SRC_HSCT',0,11,246,1,25,4,13
	.byte	'SR',0
	.word	10866
	.byte	4,2,35,0,0,14
	.word	75279
	.byte	29
	.byte	'Ifx_SRC_HSCT',0,11,249,1,3
	.word	75312
	.byte	10
	.byte	'_Ifx_SRC_HSSL',0,11,252,1,25,16,13
	.byte	'COK',0
	.word	10866
	.byte	4,2,35,0,13
	.byte	'RDI',0
	.word	10866
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	10866
	.byte	4,2,35,8,13
	.byte	'TRG',0
	.word	10866
	.byte	4,2,35,12,0,14
	.word	75339
	.byte	29
	.byte	'Ifx_SRC_HSSL',0,11,130,2,3
	.word	75412
	.byte	15,56
	.word	644
	.byte	16,55,0,10
	.byte	'_Ifx_SRC_I2C',0,11,133,2,25,80,13
	.byte	'BREQ',0
	.word	10866
	.byte	4,2,35,0,13
	.byte	'LBREQ',0
	.word	10866
	.byte	4,2,35,4,13
	.byte	'SREQ',0
	.word	10866
	.byte	4,2,35,8,13
	.byte	'LSREQ',0
	.word	10866
	.byte	4,2,35,12,13
	.byte	'ERR',0
	.word	10866
	.byte	4,2,35,16,13
	.byte	'P',0
	.word	10866
	.byte	4,2,35,20,13
	.byte	'reserved_18',0
	.word	75439
	.byte	56,2,35,24,0,14
	.word	75448
	.byte	29
	.byte	'Ifx_SRC_I2C',0,11,142,2,3
	.word	75571
	.byte	10
	.byte	'_Ifx_SRC_LMU',0,11,145,2,25,4,13
	.byte	'SR',0
	.word	10866
	.byte	4,2,35,0,0,14
	.word	75597
	.byte	29
	.byte	'Ifx_SRC_LMU',0,11,148,2,3
	.word	75629
	.byte	10
	.byte	'_Ifx_SRC_MSC',0,11,151,2,25,20,13
	.byte	'SR0',0
	.word	10866
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10866
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	10866
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	10866
	.byte	4,2,35,12,13
	.byte	'SR4',0
	.word	10866
	.byte	4,2,35,16,0,14
	.word	75655
	.byte	29
	.byte	'Ifx_SRC_MSC',0,11,158,2,3
	.word	75740
	.byte	10
	.byte	'_Ifx_SRC_PMU',0,11,161,2,25,4,13
	.byte	'SR',0
	.word	10866
	.byte	4,2,35,0,0,14
	.word	75766
	.byte	29
	.byte	'Ifx_SRC_PMU',0,11,164,2,3
	.word	75798
	.byte	10
	.byte	'_Ifx_SRC_PSI5',0,11,167,2,25,32,13
	.byte	'SR',0
	.word	74670
	.byte	32,2,35,0,0,14
	.word	75824
	.byte	29
	.byte	'Ifx_SRC_PSI5',0,11,170,2,3
	.word	75857
	.byte	10
	.byte	'_Ifx_SRC_PSI5S',0,11,173,2,25,32,13
	.byte	'SR',0
	.word	74670
	.byte	32,2,35,0,0,14
	.word	75884
	.byte	29
	.byte	'Ifx_SRC_PSI5S',0,11,176,2,3
	.word	75918
	.byte	10
	.byte	'_Ifx_SRC_QSPI',0,11,179,2,25,24,13
	.byte	'TX',0
	.word	10866
	.byte	4,2,35,0,13
	.byte	'RX',0
	.word	10866
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	10866
	.byte	4,2,35,8,13
	.byte	'PT',0
	.word	10866
	.byte	4,2,35,12,13
	.byte	'HC',0
	.word	10866
	.byte	4,2,35,16,13
	.byte	'U',0
	.word	10866
	.byte	4,2,35,20,0,14
	.word	75946
	.byte	29
	.byte	'Ifx_SRC_QSPI',0,11,187,2,3
	.word	76039
	.byte	10
	.byte	'_Ifx_SRC_SCR',0,11,190,2,25,4,13
	.byte	'SR',0
	.word	10866
	.byte	4,2,35,0,0,14
	.word	76066
	.byte	29
	.byte	'Ifx_SRC_SCR',0,11,193,2,3
	.word	76098
	.byte	10
	.byte	'_Ifx_SRC_SCU',0,11,196,2,25,20,13
	.byte	'DTS',0
	.word	10866
	.byte	4,2,35,0,13
	.byte	'ERU',0
	.word	74755
	.byte	16,2,35,4,0,14
	.word	76124
	.byte	29
	.byte	'Ifx_SRC_SCU',0,11,200,2,3
	.word	76170
	.byte	15,24
	.word	10866
	.byte	16,5,0,10
	.byte	'_Ifx_SRC_SENT',0,11,203,2,25,24,13
	.byte	'SR',0
	.word	76196
	.byte	24,2,35,0,0,14
	.word	76205
	.byte	29
	.byte	'Ifx_SRC_SENT',0,11,206,2,3
	.word	76238
	.byte	10
	.byte	'_Ifx_SRC_SMU',0,11,209,2,25,12,13
	.byte	'SR',0
	.word	74661
	.byte	12,2,35,0,0,14
	.word	76265
	.byte	29
	.byte	'Ifx_SRC_SMU',0,11,212,2,3
	.word	76297
	.byte	10
	.byte	'_Ifx_SRC_STM',0,11,215,2,25,8,13
	.byte	'SR0',0
	.word	10866
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10866
	.byte	4,2,35,4,0,14
	.word	76323
	.byte	29
	.byte	'Ifx_SRC_STM',0,11,219,2,3
	.word	76369
	.byte	10
	.byte	'_Ifx_SRC_VADCCG',0,11,222,2,25,16,13
	.byte	'SR0',0
	.word	10866
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10866
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	10866
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	10866
	.byte	4,2,35,12,0,14
	.word	76395
	.byte	29
	.byte	'Ifx_SRC_VADCCG',0,11,228,2,3
	.word	76470
	.byte	10
	.byte	'_Ifx_SRC_VADCG',0,11,231,2,25,16,13
	.byte	'SR0',0
	.word	10866
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10866
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	10866
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	10866
	.byte	4,2,35,12,0,14
	.word	76499
	.byte	29
	.byte	'Ifx_SRC_VADCG',0,11,237,2,3
	.word	76573
	.byte	10
	.byte	'_Ifx_SRC_XBAR',0,11,240,2,25,4,13
	.byte	'SRC',0
	.word	10866
	.byte	4,2,35,0,0,14
	.word	76601
	.byte	29
	.byte	'Ifx_SRC_XBAR',0,11,243,2,3
	.word	76635
	.byte	15,4
	.word	73191
	.byte	16,0,0,14
	.word	76662
	.byte	10
	.byte	'_Ifx_SRC_GAGBT',0,11,128,3,25,4,13
	.byte	'AGBT',0
	.word	76671
	.byte	4,2,35,0,0,14
	.word	76676
	.byte	29
	.byte	'Ifx_SRC_GAGBT',0,11,131,3,3
	.word	76712
	.byte	15,48
	.word	73249
	.byte	16,3,0,14
	.word	76740
	.byte	10
	.byte	'_Ifx_SRC_GASCLIN',0,11,134,3,25,48,13
	.byte	'ASCLIN',0
	.word	76749
	.byte	48,2,35,0,0,14
	.word	76754
	.byte	29
	.byte	'Ifx_SRC_GASCLIN',0,11,137,3,3
	.word	76794
	.byte	14
	.word	73336
	.byte	10
	.byte	'_Ifx_SRC_GBCU',0,11,140,3,25,4,13
	.byte	'SPB',0
	.word	76824
	.byte	4,2,35,0,0,14
	.word	76829
	.byte	29
	.byte	'Ifx_SRC_GBCU',0,11,143,3,3
	.word	76863
	.byte	15,64
	.word	73410
	.byte	16,0,0,14
	.word	76890
	.byte	10
	.byte	'_Ifx_SRC_GCAN',0,11,146,3,25,64,13
	.byte	'CAN',0
	.word	76899
	.byte	64,2,35,0,0,14
	.word	76904
	.byte	29
	.byte	'Ifx_SRC_GCAN',0,11,149,3,3
	.word	76938
	.byte	15,32
	.word	73467
	.byte	16,1,0,14
	.word	76965
	.byte	10
	.byte	'_Ifx_SRC_GCCU6',0,11,152,3,25,32,13
	.byte	'CCU6',0
	.word	76974
	.byte	32,2,35,0,0,14
	.word	76979
	.byte	29
	.byte	'Ifx_SRC_GCCU6',0,11,155,3,3
	.word	77015
	.byte	14
	.word	73574
	.byte	10
	.byte	'_Ifx_SRC_GCERBERUS',0,11,158,3,25,8,13
	.byte	'CERBERUS',0
	.word	77043
	.byte	8,2,35,0,0,14
	.word	77048
	.byte	29
	.byte	'Ifx_SRC_GCERBERUS',0,11,161,3,3
	.word	77092
	.byte	15,16
	.word	73640
	.byte	16,0,0,14
	.word	77124
	.byte	10
	.byte	'_Ifx_SRC_GCIF',0,11,164,3,25,16,13
	.byte	'CIF',0
	.word	77133
	.byte	16,2,35,0,0,14
	.word	77138
	.byte	29
	.byte	'Ifx_SRC_GCIF',0,11,167,3,3
	.word	77172
	.byte	15,8
	.word	73739
	.byte	16,1,0,14
	.word	77199
	.byte	10
	.byte	'_Ifx_SRC_GCPU',0,11,170,3,25,8,13
	.byte	'CPU',0
	.word	77208
	.byte	8,2,35,0,0,14
	.word	77213
	.byte	29
	.byte	'Ifx_SRC_GCPU',0,11,173,3,3
	.word	77247
	.byte	15,208,1
	.word	73810
	.byte	16,0,0,14
	.word	77274
	.byte	10
	.byte	'_Ifx_SRC_GDMA',0,11,176,3,25,208,1,13
	.byte	'DMA',0
	.word	77284
	.byte	208,1,2,35,0,0,14
	.word	77289
	.byte	29
	.byte	'Ifx_SRC_GDMA',0,11,179,3,3
	.word	77325
	.byte	14
	.word	73903
	.byte	14
	.word	73903
	.byte	14
	.word	73903
	.byte	10
	.byte	'_Ifx_SRC_GDSADC',0,11,182,3,25,32,13
	.byte	'DSADC0',0
	.word	77352
	.byte	8,2,35,0,13
	.byte	'reserved_8',0
	.word	4281
	.byte	8,2,35,8,13
	.byte	'DSADC2',0
	.word	77357
	.byte	8,2,35,16,13
	.byte	'DSADC3',0
	.word	77362
	.byte	8,2,35,24,0,14
	.word	77367
	.byte	29
	.byte	'Ifx_SRC_GDSADC',0,11,188,3,3
	.word	77458
	.byte	15,4
	.word	73979
	.byte	16,0,0,14
	.word	77487
	.byte	10
	.byte	'_Ifx_SRC_GEMEM',0,11,191,3,25,4,13
	.byte	'EMEM',0
	.word	77496
	.byte	4,2,35,0,0,14
	.word	77501
	.byte	29
	.byte	'Ifx_SRC_GEMEM',0,11,194,3,3
	.word	77537
	.byte	15,80
	.word	74039
	.byte	16,0,0,14
	.word	77565
	.byte	10
	.byte	'_Ifx_SRC_GERAY',0,11,197,3,25,80,13
	.byte	'ERAY',0
	.word	77574
	.byte	80,2,35,0,0,14
	.word	77579
	.byte	29
	.byte	'Ifx_SRC_GERAY',0,11,200,3,3
	.word	77615
	.byte	15,4
	.word	74193
	.byte	16,0,0,14
	.word	77643
	.byte	10
	.byte	'_Ifx_SRC_GETH',0,11,203,3,25,4,13
	.byte	'ETH',0
	.word	77652
	.byte	4,2,35,0,0,14
	.word	77657
	.byte	29
	.byte	'Ifx_SRC_GETH',0,11,206,3,3
	.word	77691
	.byte	15,4
	.word	74251
	.byte	16,0,0,14
	.word	77718
	.byte	10
	.byte	'_Ifx_SRC_GFCE',0,11,209,3,25,4,13
	.byte	'FCE',0
	.word	77727
	.byte	4,2,35,0,0,14
	.word	77732
	.byte	29
	.byte	'Ifx_SRC_GFCE',0,11,212,3,3
	.word	77766
	.byte	15,12
	.word	74309
	.byte	16,0,0,14
	.word	77793
	.byte	10
	.byte	'_Ifx_SRC_GFFT',0,11,215,3,25,12,13
	.byte	'FFT',0
	.word	77802
	.byte	12,2,35,0,0,14
	.word	77807
	.byte	29
	.byte	'Ifx_SRC_GFFT',0,11,218,3,3
	.word	77841
	.byte	15,64
	.word	74395
	.byte	16,1,0,14
	.word	77868
	.byte	10
	.byte	'_Ifx_SRC_GGPSR',0,11,221,3,25,64,13
	.byte	'GPSR',0
	.word	77877
	.byte	64,2,35,0,0,14
	.word	77882
	.byte	29
	.byte	'Ifx_SRC_GGPSR',0,11,224,3,3
	.word	77918
	.byte	15,48
	.word	74516
	.byte	16,0,0,14
	.word	77946
	.byte	10
	.byte	'_Ifx_SRC_GGPT12',0,11,227,3,25,48,13
	.byte	'GPT12',0
	.word	77955
	.byte	48,2,35,0,0,14
	.word	77960
	.byte	29
	.byte	'Ifx_SRC_GGPT12',0,11,230,3,3
	.word	77998
	.byte	15,204,18
	.word	74793
	.byte	16,0,0,14
	.word	78027
	.byte	10
	.byte	'_Ifx_SRC_GGTM',0,11,233,3,25,204,18,13
	.byte	'GTM',0
	.word	78037
	.byte	204,18,2,35,0,0,14
	.word	78042
	.byte	29
	.byte	'Ifx_SRC_GGTM',0,11,236,3,3
	.word	78078
	.byte	15,4
	.word	75279
	.byte	16,0,0,14
	.word	78105
	.byte	10
	.byte	'_Ifx_SRC_GHSCT',0,11,239,3,25,4,13
	.byte	'HSCT',0
	.word	78114
	.byte	4,2,35,0,0,14
	.word	78119
	.byte	29
	.byte	'Ifx_SRC_GHSCT',0,11,242,3,3
	.word	78155
	.byte	15,64
	.word	75339
	.byte	16,3,0,14
	.word	78183
	.byte	10
	.byte	'_Ifx_SRC_GHSSL',0,11,245,3,25,68,13
	.byte	'HSSL',0
	.word	78192
	.byte	64,2,35,0,13
	.byte	'EXI',0
	.word	10866
	.byte	4,2,35,64,0,14
	.word	78197
	.byte	29
	.byte	'Ifx_SRC_GHSSL',0,11,249,3,3
	.word	78246
	.byte	15,80
	.word	75448
	.byte	16,0,0,14
	.word	78274
	.byte	10
	.byte	'_Ifx_SRC_GI2C',0,11,252,3,25,80,13
	.byte	'I2C',0
	.word	78283
	.byte	80,2,35,0,0,14
	.word	78288
	.byte	29
	.byte	'Ifx_SRC_GI2C',0,11,255,3,3
	.word	78322
	.byte	15,4
	.word	75597
	.byte	16,0,0,14
	.word	78349
	.byte	10
	.byte	'_Ifx_SRC_GLMU',0,11,130,4,25,4,13
	.byte	'LMU',0
	.word	78358
	.byte	4,2,35,0,0,14
	.word	78363
	.byte	29
	.byte	'Ifx_SRC_GLMU',0,11,133,4,3
	.word	78397
	.byte	15,40
	.word	75655
	.byte	16,1,0,14
	.word	78424
	.byte	10
	.byte	'_Ifx_SRC_GMSC',0,11,136,4,25,40,13
	.byte	'MSC',0
	.word	78433
	.byte	40,2,35,0,0,14
	.word	78438
	.byte	29
	.byte	'Ifx_SRC_GMSC',0,11,139,4,3
	.word	78472
	.byte	15,8
	.word	75766
	.byte	16,1,0,14
	.word	78499
	.byte	10
	.byte	'_Ifx_SRC_GPMU',0,11,142,4,25,8,13
	.byte	'PMU',0
	.word	78508
	.byte	8,2,35,0,0,14
	.word	78513
	.byte	29
	.byte	'Ifx_SRC_GPMU',0,11,145,4,3
	.word	78547
	.byte	15,32
	.word	75824
	.byte	16,0,0,14
	.word	78574
	.byte	10
	.byte	'_Ifx_SRC_GPSI5',0,11,148,4,25,32,13
	.byte	'PSI5',0
	.word	78583
	.byte	32,2,35,0,0,14
	.word	78588
	.byte	29
	.byte	'Ifx_SRC_GPSI5',0,11,151,4,3
	.word	78624
	.byte	15,32
	.word	75884
	.byte	16,0,0,14
	.word	78652
	.byte	10
	.byte	'_Ifx_SRC_GPSI5S',0,11,154,4,25,32,13
	.byte	'PSI5S',0
	.word	78661
	.byte	32,2,35,0,0,14
	.word	78666
	.byte	29
	.byte	'Ifx_SRC_GPSI5S',0,11,157,4,3
	.word	78704
	.byte	15,96
	.word	75946
	.byte	16,3,0,14
	.word	78733
	.byte	10
	.byte	'_Ifx_SRC_GQSPI',0,11,160,4,25,96,13
	.byte	'QSPI',0
	.word	78742
	.byte	96,2,35,0,0,14
	.word	78747
	.byte	29
	.byte	'Ifx_SRC_GQSPI',0,11,163,4,3
	.word	78783
	.byte	15,4
	.word	76066
	.byte	16,0,0,14
	.word	78811
	.byte	10
	.byte	'_Ifx_SRC_GSCR',0,11,166,4,25,4,13
	.byte	'SCR',0
	.word	78820
	.byte	4,2,35,0,0,14
	.word	78825
	.byte	29
	.byte	'Ifx_SRC_GSCR',0,11,169,4,3
	.word	78859
	.byte	14
	.word	76124
	.byte	10
	.byte	'_Ifx_SRC_GSCU',0,11,172,4,25,20,13
	.byte	'SCU',0
	.word	78886
	.byte	20,2,35,0,0,14
	.word	78891
	.byte	29
	.byte	'Ifx_SRC_GSCU',0,11,175,4,3
	.word	78925
	.byte	15,24
	.word	76205
	.byte	16,0,0,14
	.word	78952
	.byte	10
	.byte	'_Ifx_SRC_GSENT',0,11,178,4,25,24,13
	.byte	'SENT',0
	.word	78961
	.byte	24,2,35,0,0,14
	.word	78966
	.byte	29
	.byte	'Ifx_SRC_GSENT',0,11,181,4,3
	.word	79002
	.byte	15,12
	.word	76265
	.byte	16,0,0,14
	.word	79030
	.byte	10
	.byte	'_Ifx_SRC_GSMU',0,11,184,4,25,12,13
	.byte	'SMU',0
	.word	79039
	.byte	12,2,35,0,0,14
	.word	79044
	.byte	29
	.byte	'Ifx_SRC_GSMU',0,11,187,4,3
	.word	79078
	.byte	15,16
	.word	76323
	.byte	16,1,0,14
	.word	79105
	.byte	10
	.byte	'_Ifx_SRC_GSTM',0,11,190,4,25,16,13
	.byte	'STM',0
	.word	79114
	.byte	16,2,35,0,0,14
	.word	79119
	.byte	29
	.byte	'Ifx_SRC_GSTM',0,11,193,4,3
	.word	79153
	.byte	15,64
	.word	76499
	.byte	16,3,0,14
	.word	79180
	.byte	15,224,1
	.word	644
	.byte	16,223,1,0,15,32
	.word	76395
	.byte	16,1,0,14
	.word	79205
	.byte	10
	.byte	'_Ifx_SRC_GVADC',0,11,196,4,25,192,2,13
	.byte	'G',0
	.word	79189
	.byte	64,2,35,0,13
	.byte	'reserved_40',0
	.word	79194
	.byte	224,1,2,35,64,13
	.byte	'CG',0
	.word	79214
	.byte	32,3,35,160,2,0,14
	.word	79219
	.byte	29
	.byte	'Ifx_SRC_GVADC',0,11,201,4,3
	.word	79288
	.byte	14
	.word	76601
	.byte	10
	.byte	'_Ifx_SRC_GXBAR',0,11,204,4,25,4,13
	.byte	'XBAR',0
	.word	79316
	.byte	4,2,35,0,0,14
	.word	79321
	.byte	29
	.byte	'Ifx_SRC_GXBAR',0,11,207,4,3
	.word	79357
	.byte	29
	.byte	'Ifx_STM_ACCEN0_Bits',0,16,79,3
	.word	20796
	.byte	29
	.byte	'Ifx_STM_ACCEN1_Bits',0,16,85,3
	.word	20707
	.byte	29
	.byte	'Ifx_STM_CAP_Bits',0,16,91,3
	.word	19237
	.byte	29
	.byte	'Ifx_STM_CAPSV_Bits',0,16,97,3
	.word	20114
	.byte	29
	.byte	'Ifx_STM_CLC_Bits',0,16,107,3
	.word	18360
	.byte	29
	.byte	'Ifx_STM_CMCON_Bits',0,16,120,3
	.word	19415
	.byte	29
	.byte	'Ifx_STM_CMP_Bits',0,16,126,3
	.word	19324
	.byte	29
	.byte	'Ifx_STM_ICR_Bits',0,16,139,1,3
	.word	19646
	.byte	29
	.byte	'Ifx_STM_ID_Bits',0,16,147,1,3
	.word	18516
	.byte	29
	.byte	'Ifx_STM_ISCR_Bits',0,16,157,1,3
	.word	19863
	.byte	29
	.byte	'Ifx_STM_KRST0_Bits',0,16,165,1,3
	.word	20584
	.byte	29
	.byte	'Ifx_STM_KRST1_Bits',0,16,172,1,3
	.word	20480
	.byte	29
	.byte	'Ifx_STM_KRSTCLR_Bits',0,16,179,1,3
	.word	20374
	.byte	29
	.byte	'Ifx_STM_OCS_Bits',0,16,189,1,3
	.word	20214
	.byte	29
	.byte	'Ifx_STM_TIM0_Bits',0,16,195,1,3
	.word	18638
	.byte	29
	.byte	'Ifx_STM_TIM0SV_Bits',0,16,201,1,3
	.word	20027
	.byte	29
	.byte	'Ifx_STM_TIM1_Bits',0,16,207,1,3
	.word	18723
	.byte	29
	.byte	'Ifx_STM_TIM2_Bits',0,16,213,1,3
	.word	18808
	.byte	29
	.byte	'Ifx_STM_TIM3_Bits',0,16,219,1,3
	.word	18893
	.byte	29
	.byte	'Ifx_STM_TIM4_Bits',0,16,225,1,3
	.word	18979
	.byte	29
	.byte	'Ifx_STM_TIM5_Bits',0,16,231,1,3
	.word	19065
	.byte	29
	.byte	'Ifx_STM_TIM6_Bits',0,16,237,1,3
	.word	19151
	.byte	29
	.byte	'Ifx_STM_ACCEN0',0,16,250,1,3
	.word	21325
	.byte	29
	.byte	'Ifx_STM_ACCEN1',0,16,130,2,3
	.word	20756
	.byte	29
	.byte	'Ifx_STM_CAP',0,16,138,2,3
	.word	19284
	.byte	29
	.byte	'Ifx_STM_CAPSV',0,16,146,2,3
	.word	20163
	.byte	29
	.byte	'Ifx_STM_CLC',0,16,154,2,3
	.word	18476
	.byte	29
	.byte	'Ifx_STM_CMCON',0,16,162,2,3
	.word	19606
	.byte	29
	.byte	'Ifx_STM_CMP',0,16,170,2,3
	.word	19366
	.byte	29
	.byte	'Ifx_STM_ICR',0,16,178,2,3
	.word	19823
	.byte	29
	.byte	'Ifx_STM_ID',0,16,186,2,3
	.word	18598
	.byte	29
	.byte	'Ifx_STM_ISCR',0,16,194,2,3
	.word	19987
	.byte	29
	.byte	'Ifx_STM_KRST0',0,16,202,2,3
	.word	20667
	.byte	29
	.byte	'Ifx_STM_KRST1',0,16,210,2,3
	.word	20544
	.byte	29
	.byte	'Ifx_STM_KRSTCLR',0,16,218,2,3
	.word	20440
	.byte	29
	.byte	'Ifx_STM_OCS',0,16,226,2,3
	.word	20334
	.byte	29
	.byte	'Ifx_STM_TIM0',0,16,234,2,3
	.word	18683
	.byte	29
	.byte	'Ifx_STM_TIM0SV',0,16,242,2,3
	.word	20074
	.byte	29
	.byte	'Ifx_STM_TIM1',0,16,250,2,3
	.word	18768
	.byte	29
	.byte	'Ifx_STM_TIM2',0,16,130,3,3
	.word	18853
	.byte	29
	.byte	'Ifx_STM_TIM3',0,16,138,3,3
	.word	18939
	.byte	29
	.byte	'Ifx_STM_TIM4',0,16,146,3,3
	.word	19025
	.byte	29
	.byte	'Ifx_STM_TIM5',0,16,154,3,3
	.word	19111
	.byte	29
	.byte	'Ifx_STM_TIM6',0,16,162,3,3
	.word	19197
	.byte	14
	.word	21365
	.byte	29
	.byte	'Ifx_STM',0,16,201,3,3
	.word	80468
	.byte	17,7,144,1,9,1,18
	.byte	'IfxCpu_CounterMode_normal',0,0,18
	.byte	'IfxCpu_CounterMode_task',0,1,0,29
	.byte	'IfxCpu_CounterMode',0,7,148,1,3
	.word	80490
	.byte	20,7,160,1,9,6,13
	.byte	'counter',0
	.word	10305
	.byte	4,2,35,0,13
	.byte	'overlfow',0
	.word	644
	.byte	1,2,35,4,0,29
	.byte	'IfxCpu_Counter',0,7,164,1,3
	.word	80579
	.byte	20,7,172,1,9,32,13
	.byte	'instruction',0
	.word	80579
	.byte	6,2,35,0,13
	.byte	'clock',0
	.word	80579
	.byte	6,2,35,6,13
	.byte	'counter1',0
	.word	80579
	.byte	6,2,35,12,13
	.byte	'counter2',0
	.word	80579
	.byte	6,2,35,18,13
	.byte	'counter3',0
	.word	80579
	.byte	6,2,35,24,0,29
	.byte	'IfxCpu_Perf',0,7,179,1,3
	.word	80645
	.byte	10
	.byte	'_Ifx_FLASH_ACCEN0_Bits',0,29,45,16,4,11
	.byte	'EN0',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	644
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	644
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	644
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	644
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	644
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	644
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	644
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	644
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	644
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	644
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	644
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	644
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	644
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	644
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	644
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	644
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	644
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	644
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	644
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	644
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	644
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	644
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	644
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	644
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	644
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	644
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	644
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_FLASH_ACCEN0_Bits',0,29,79,3
	.word	80763
	.byte	10
	.byte	'_Ifx_FLASH_ACCEN1_Bits',0,29,82,16,4,11
	.byte	'reserved_0',0,4
	.word	469
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_FLASH_ACCEN1_Bits',0,29,85,3
	.word	81324
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_CFG_Bits',0,29,88,16,4,11
	.byte	'SEL',0,1
	.word	644
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	644
	.byte	2,0,2,35,0,11
	.byte	'CLR',0,1
	.word	644
	.byte	1,7,2,35,1,11
	.byte	'DIS',0,1
	.word	644
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	469
	.byte	22,0,2,35,0,0,29
	.byte	'Ifx_FLASH_CBAB_CFG_Bits',0,29,95,3
	.word	81405
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_STAT_Bits',0,29,98,16,4,11
	.byte	'VLD0',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'VLD1',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'VLD2',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'VLD3',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'VLD4',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'VLD5',0,1
	.word	644
	.byte	1,2,2,35,0,11
	.byte	'VLD6',0,1
	.word	644
	.byte	1,1,2,35,0,11
	.byte	'VLD7',0,1
	.word	644
	.byte	1,0,2,35,0,11
	.byte	'VLD8',0,1
	.word	644
	.byte	1,7,2,35,1,11
	.byte	'VLD9',0,1
	.word	644
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	469
	.byte	22,0,2,35,0,0,29
	.byte	'Ifx_FLASH_CBAB_STAT_Bits',0,29,111,3
	.word	81558
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_TOP_Bits',0,29,114,16,4,11
	.byte	'reserved_0',0,1
	.word	644
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	469
	.byte	19,8,2,35,0,11
	.byte	'ERR',0,1
	.word	644
	.byte	6,2,2,35,3,11
	.byte	'VLD',0,1
	.word	644
	.byte	1,1,2,35,3,11
	.byte	'CLR',0,1
	.word	644
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_FLASH_CBAB_TOP_Bits',0,29,121,3
	.word	81806
	.byte	10
	.byte	'_Ifx_FLASH_COMM0_Bits',0,29,124,16,4,11
	.byte	'STATUS',0,1
	.word	644
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	469
	.byte	24,0,2,35,0,0,29
	.byte	'Ifx_FLASH_COMM0_Bits',0,29,128,1,3
	.word	81952
	.byte	10
	.byte	'_Ifx_FLASH_COMM1_Bits',0,29,131,1,16,4,11
	.byte	'STATUS',0,1
	.word	644
	.byte	8,0,2,35,0,11
	.byte	'DATA',0,1
	.word	644
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	661
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_FLASH_COMM1_Bits',0,29,136,1,3
	.word	82050
	.byte	10
	.byte	'_Ifx_FLASH_COMM2_Bits',0,29,139,1,16,4,11
	.byte	'STATUS',0,1
	.word	644
	.byte	8,0,2,35,0,11
	.byte	'DATA',0,1
	.word	644
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	661
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_FLASH_COMM2_Bits',0,29,144,1,3
	.word	82166
	.byte	10
	.byte	'_Ifx_FLASH_ECCRD_Bits',0,29,147,1,16,4,11
	.byte	'RCODE',0,4
	.word	469
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	661
	.byte	8,2,2,35,2,11
	.byte	'EDCERRINJ',0,1
	.word	644
	.byte	1,1,2,35,3,11
	.byte	'ECCORDIS',0,1
	.word	644
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_FLASH_ECCRD_Bits',0,29,153,1,3
	.word	82282
	.byte	10
	.byte	'_Ifx_FLASH_ECCRP_Bits',0,29,156,1,16,4,11
	.byte	'RCODE',0,4
	.word	469
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	661
	.byte	8,2,2,35,2,11
	.byte	'EDCERRINJ',0,1
	.word	644
	.byte	1,1,2,35,3,11
	.byte	'ECCORDIS',0,1
	.word	644
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_FLASH_ECCRP_Bits',0,29,162,1,3
	.word	82422
	.byte	10
	.byte	'_Ifx_FLASH_ECCW_Bits',0,29,165,1,16,4,11
	.byte	'WCODE',0,4
	.word	469
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	661
	.byte	8,2,2,35,2,11
	.byte	'DECENCDIS',0,1
	.word	644
	.byte	1,1,2,35,3,11
	.byte	'PECENCDIS',0,1
	.word	644
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_FLASH_ECCW_Bits',0,29,171,1,3
	.word	82562
	.byte	10
	.byte	'_Ifx_FLASH_FCON_Bits',0,29,174,1,16,4,11
	.byte	'WSPFLASH',0,1
	.word	644
	.byte	4,4,2,35,0,11
	.byte	'WSECPF',0,1
	.word	644
	.byte	2,2,2,35,0,11
	.byte	'WSDFLASH',0,2
	.word	661
	.byte	6,4,2,35,0,11
	.byte	'WSECDF',0,1
	.word	644
	.byte	3,1,2,35,1,11
	.byte	'IDLE',0,1
	.word	644
	.byte	1,0,2,35,1,11
	.byte	'ESLDIS',0,1
	.word	644
	.byte	1,7,2,35,2,11
	.byte	'SLEEP',0,1
	.word	644
	.byte	1,6,2,35,2,11
	.byte	'NSAFECC',0,1
	.word	644
	.byte	1,5,2,35,2,11
	.byte	'STALL',0,1
	.word	644
	.byte	1,4,2,35,2,11
	.byte	'RES21',0,1
	.word	644
	.byte	2,2,2,35,2,11
	.byte	'RES23',0,1
	.word	644
	.byte	2,0,2,35,2,11
	.byte	'VOPERM',0,1
	.word	644
	.byte	1,7,2,35,3,11
	.byte	'SQERM',0,1
	.word	644
	.byte	1,6,2,35,3,11
	.byte	'PROERM',0,1
	.word	644
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	644
	.byte	3,2,2,35,3,11
	.byte	'PR5V',0,1
	.word	644
	.byte	1,1,2,35,3,11
	.byte	'EOBM',0,1
	.word	644
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_FLASH_FCON_Bits',0,29,193,1,3
	.word	82701
	.byte	10
	.byte	'_Ifx_FLASH_FPRO_Bits',0,29,196,1,16,4,11
	.byte	'PROINP',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'PRODISP',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'PROIND',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'PRODISD',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'PROINHSMCOTP',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'RES5',0,1
	.word	644
	.byte	1,2,2,35,0,11
	.byte	'PROINOTP',0,1
	.word	644
	.byte	1,1,2,35,0,11
	.byte	'RES7',0,1
	.word	644
	.byte	1,0,2,35,0,11
	.byte	'PROINDBG',0,1
	.word	644
	.byte	1,7,2,35,1,11
	.byte	'PRODISDBG',0,1
	.word	644
	.byte	1,6,2,35,1,11
	.byte	'PROINHSM',0,1
	.word	644
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	644
	.byte	5,0,2,35,1,11
	.byte	'DCFP',0,1
	.word	644
	.byte	1,7,2,35,2,11
	.byte	'DDFP',0,1
	.word	644
	.byte	1,6,2,35,2,11
	.byte	'DDFPX',0,1
	.word	644
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	644
	.byte	1,4,2,35,2,11
	.byte	'DDFD',0,1
	.word	644
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	644
	.byte	1,2,2,35,2,11
	.byte	'ENPE',0,1
	.word	644
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	644
	.byte	8,0,2,35,3,0,29
	.byte	'Ifx_FLASH_FPRO_Bits',0,29,218,1,3
	.word	83063
	.byte	10
	.byte	'_Ifx_FLASH_FSR_Bits',0,29,221,1,16,4,11
	.byte	'FABUSY',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'D0BUSY',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'RES1',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'P0BUSY',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'P1BUSY',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'RES5',0,1
	.word	644
	.byte	1,2,2,35,0,11
	.byte	'RES6',0,1
	.word	644
	.byte	1,1,2,35,0,11
	.byte	'PROG',0,1
	.word	644
	.byte	1,0,2,35,0,11
	.byte	'ERASE',0,1
	.word	644
	.byte	1,7,2,35,1,11
	.byte	'PFPAGE',0,1
	.word	644
	.byte	1,6,2,35,1,11
	.byte	'DFPAGE',0,1
	.word	644
	.byte	1,5,2,35,1,11
	.byte	'OPER',0,1
	.word	644
	.byte	1,4,2,35,1,11
	.byte	'SQER',0,1
	.word	644
	.byte	1,3,2,35,1,11
	.byte	'PROER',0,1
	.word	644
	.byte	1,2,2,35,1,11
	.byte	'PFSBER',0,1
	.word	644
	.byte	1,1,2,35,1,11
	.byte	'PFDBER',0,1
	.word	644
	.byte	1,0,2,35,1,11
	.byte	'PFMBER',0,1
	.word	644
	.byte	1,7,2,35,2,11
	.byte	'RES17',0,1
	.word	644
	.byte	1,6,2,35,2,11
	.byte	'DFSBER',0,1
	.word	644
	.byte	1,5,2,35,2,11
	.byte	'DFDBER',0,1
	.word	644
	.byte	1,4,2,35,2,11
	.byte	'DFTBER',0,1
	.word	644
	.byte	1,3,2,35,2,11
	.byte	'DFMBER',0,1
	.word	644
	.byte	1,2,2,35,2,11
	.byte	'SRIADDERR',0,1
	.word	644
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	661
	.byte	2,7,2,35,2,11
	.byte	'PVER',0,1
	.word	644
	.byte	1,6,2,35,3,11
	.byte	'EVER',0,1
	.word	644
	.byte	1,5,2,35,3,11
	.byte	'SPND',0,1
	.word	644
	.byte	1,4,2,35,3,11
	.byte	'SLM',0,1
	.word	644
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	644
	.byte	1,2,2,35,3,11
	.byte	'ORIER',0,1
	.word	644
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	644
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_FLASH_FSR_Bits',0,29,254,1,3
	.word	83504
	.byte	10
	.byte	'_Ifx_FLASH_ID_Bits',0,29,129,2,16,4,11
	.byte	'MODREV',0,1
	.word	644
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	644
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	661
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_FLASH_ID_Bits',0,29,134,2,3
	.word	84110
	.byte	10
	.byte	'_Ifx_FLASH_MARD_Bits',0,29,137,2,16,4,11
	.byte	'HMARGIN',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'SELD0',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'SPND',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'SPNDERR',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,2
	.word	661
	.byte	10,1,2,35,0,11
	.byte	'TRAPDIS',0,1
	.word	644
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	661
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_FLASH_MARD_Bits',0,29,147,2,3
	.word	84221
	.byte	10
	.byte	'_Ifx_FLASH_MARP_Bits',0,29,150,2,16,4,11
	.byte	'SELP0',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'SELP1',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'RES2',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'RES3',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	661
	.byte	11,1,2,35,0,11
	.byte	'TRAPDIS',0,1
	.word	644
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	661
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_FLASH_MARP_Bits',0,29,159,2,3
	.word	84435
	.byte	10
	.byte	'_Ifx_FLASH_PROCOND_Bits',0,29,162,2,16,4,11
	.byte	'L',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'NSAFECC',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'RAMIN',0,1
	.word	644
	.byte	2,4,2,35,0,11
	.byte	'RAMINSEL',0,1
	.word	644
	.byte	4,0,2,35,0,11
	.byte	'OSCCFG',0,1
	.word	644
	.byte	1,7,2,35,1,11
	.byte	'MODE',0,1
	.word	644
	.byte	2,5,2,35,1,11
	.byte	'APREN',0,1
	.word	644
	.byte	1,4,2,35,1,11
	.byte	'CAP0EN',0,1
	.word	644
	.byte	1,3,2,35,1,11
	.byte	'CAP1EN',0,1
	.word	644
	.byte	1,2,2,35,1,11
	.byte	'CAP2EN',0,1
	.word	644
	.byte	1,1,2,35,1,11
	.byte	'CAP3EN',0,1
	.word	644
	.byte	1,0,2,35,1,11
	.byte	'ESR0CNT',0,2
	.word	661
	.byte	12,4,2,35,2,11
	.byte	'RES29',0,1
	.word	644
	.byte	2,2,2,35,3,11
	.byte	'RES30',0,1
	.word	644
	.byte	1,1,2,35,3,11
	.byte	'RPRO',0,1
	.word	644
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_FLASH_PROCOND_Bits',0,29,179,2,3
	.word	84622
	.byte	10
	.byte	'_Ifx_FLASH_PROCONDBG_Bits',0,29,182,2,16,4,11
	.byte	'OCDSDIS',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'DBGIFLCK',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'EDM',0,1
	.word	644
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	469
	.byte	28,0,2,35,0,0,29
	.byte	'Ifx_FLASH_PROCONDBG_Bits',0,29,188,2,3
	.word	84946
	.byte	10
	.byte	'_Ifx_FLASH_PROCONHSM_Bits',0,29,191,2,16,4,11
	.byte	'HSMDBGDIS',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'DBGIFLCK',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'TSTIFLCK',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'HSMTSTDIS',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'RES15',0,2
	.word	661
	.byte	12,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	661
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_FLASH_PROCONHSM_Bits',0,29,199,2,3
	.word	85089
	.byte	10
	.byte	'_Ifx_FLASH_PROCONHSMCOTP_Bits',0,29,202,2,16,4,11
	.byte	'HSMBOOTEN',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'SSWWAIT',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'HSMDX',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'HSM6X',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'HSM16X',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'HSM17X',0,1
	.word	644
	.byte	1,2,2,35,0,11
	.byte	'S6ROM',0,1
	.word	644
	.byte	1,1,2,35,0,11
	.byte	'HSMENPINS',0,2
	.word	661
	.byte	2,7,2,35,0,11
	.byte	'HSMENRES',0,1
	.word	644
	.byte	2,5,2,35,1,11
	.byte	'DESTDBG',0,1
	.word	644
	.byte	2,3,2,35,1,11
	.byte	'BLKFLAN',0,1
	.word	644
	.byte	1,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	644
	.byte	2,0,2,35,1,11
	.byte	'S16ROM',0,1
	.word	644
	.byte	1,7,2,35,2,11
	.byte	'S17ROM',0,1
	.word	644
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	661
	.byte	14,0,2,35,2,0,29
	.byte	'Ifx_FLASH_PROCONHSMCOTP_Bits',0,29,219,2,3
	.word	85278
	.byte	10
	.byte	'_Ifx_FLASH_PROCONOTP_Bits',0,29,222,2,16,4,11
	.byte	'S0ROM',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'S1ROM',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'S2ROM',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'S3ROM',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'S4ROM',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'S5ROM',0,1
	.word	644
	.byte	1,2,2,35,0,11
	.byte	'S6ROM',0,1
	.word	644
	.byte	1,1,2,35,0,11
	.byte	'S7ROM',0,1
	.word	644
	.byte	1,0,2,35,0,11
	.byte	'S8ROM',0,1
	.word	644
	.byte	1,7,2,35,1,11
	.byte	'S9ROM',0,1
	.word	644
	.byte	1,6,2,35,1,11
	.byte	'S10ROM',0,1
	.word	644
	.byte	1,5,2,35,1,11
	.byte	'S11ROM',0,1
	.word	644
	.byte	1,4,2,35,1,11
	.byte	'S12ROM',0,1
	.word	644
	.byte	1,3,2,35,1,11
	.byte	'S13ROM',0,1
	.word	644
	.byte	1,2,2,35,1,11
	.byte	'S14ROM',0,1
	.word	644
	.byte	1,1,2,35,1,11
	.byte	'S15ROM',0,1
	.word	644
	.byte	1,0,2,35,1,11
	.byte	'S16ROM',0,1
	.word	644
	.byte	1,7,2,35,2,11
	.byte	'S17ROM',0,1
	.word	644
	.byte	1,6,2,35,2,11
	.byte	'S18ROM',0,1
	.word	644
	.byte	1,5,2,35,2,11
	.byte	'S19ROM',0,1
	.word	644
	.byte	1,4,2,35,2,11
	.byte	'S20ROM',0,1
	.word	644
	.byte	1,3,2,35,2,11
	.byte	'S21ROM',0,1
	.word	644
	.byte	1,2,2,35,2,11
	.byte	'S22ROM',0,1
	.word	644
	.byte	1,1,2,35,2,11
	.byte	'S23ROM',0,1
	.word	644
	.byte	1,0,2,35,2,11
	.byte	'S24ROM',0,1
	.word	644
	.byte	1,7,2,35,3,11
	.byte	'S25ROM',0,1
	.word	644
	.byte	1,6,2,35,3,11
	.byte	'S26ROM',0,1
	.word	644
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	644
	.byte	2,3,2,35,3,11
	.byte	'BML',0,1
	.word	644
	.byte	2,1,2,35,3,11
	.byte	'TP',0,1
	.word	644
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_FLASH_PROCONOTP_Bits',0,29,254,2,3
	.word	85641
	.byte	10
	.byte	'_Ifx_FLASH_PROCONP_Bits',0,29,129,3,16,4,11
	.byte	'S0L',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'S1L',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'S2L',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'S3L',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'S4L',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'S5L',0,1
	.word	644
	.byte	1,2,2,35,0,11
	.byte	'S6L',0,1
	.word	644
	.byte	1,1,2,35,0,11
	.byte	'S7L',0,1
	.word	644
	.byte	1,0,2,35,0,11
	.byte	'S8L',0,1
	.word	644
	.byte	1,7,2,35,1,11
	.byte	'S9L',0,1
	.word	644
	.byte	1,6,2,35,1,11
	.byte	'S10L',0,1
	.word	644
	.byte	1,5,2,35,1,11
	.byte	'S11L',0,1
	.word	644
	.byte	1,4,2,35,1,11
	.byte	'S12L',0,1
	.word	644
	.byte	1,3,2,35,1,11
	.byte	'S13L',0,1
	.word	644
	.byte	1,2,2,35,1,11
	.byte	'S14L',0,1
	.word	644
	.byte	1,1,2,35,1,11
	.byte	'S15L',0,1
	.word	644
	.byte	1,0,2,35,1,11
	.byte	'S16L',0,1
	.word	644
	.byte	1,7,2,35,2,11
	.byte	'S17L',0,1
	.word	644
	.byte	1,6,2,35,2,11
	.byte	'S18L',0,1
	.word	644
	.byte	1,5,2,35,2,11
	.byte	'S19L',0,1
	.word	644
	.byte	1,4,2,35,2,11
	.byte	'S20L',0,1
	.word	644
	.byte	1,3,2,35,2,11
	.byte	'S21L',0,1
	.word	644
	.byte	1,2,2,35,2,11
	.byte	'S22L',0,1
	.word	644
	.byte	1,1,2,35,2,11
	.byte	'S23L',0,1
	.word	644
	.byte	1,0,2,35,2,11
	.byte	'S24L',0,1
	.word	644
	.byte	1,7,2,35,3,11
	.byte	'S25L',0,1
	.word	644
	.byte	1,6,2,35,3,11
	.byte	'S26L',0,1
	.word	644
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	644
	.byte	4,1,2,35,3,11
	.byte	'RPRO',0,1
	.word	644
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_FLASH_PROCONP_Bits',0,29,160,3,3
	.word	86236
	.byte	10
	.byte	'_Ifx_FLASH_PROCONWOP_Bits',0,29,163,3,16,4,11
	.byte	'S0WOP',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'S1WOP',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'S2WOP',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'S3WOP',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'S4WOP',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'S5WOP',0,1
	.word	644
	.byte	1,2,2,35,0,11
	.byte	'S6WOP',0,1
	.word	644
	.byte	1,1,2,35,0,11
	.byte	'S7WOP',0,1
	.word	644
	.byte	1,0,2,35,0,11
	.byte	'S8WOP',0,1
	.word	644
	.byte	1,7,2,35,1,11
	.byte	'S9WOP',0,1
	.word	644
	.byte	1,6,2,35,1,11
	.byte	'S10WOP',0,1
	.word	644
	.byte	1,5,2,35,1,11
	.byte	'S11WOP',0,1
	.word	644
	.byte	1,4,2,35,1,11
	.byte	'S12WOP',0,1
	.word	644
	.byte	1,3,2,35,1,11
	.byte	'S13WOP',0,1
	.word	644
	.byte	1,2,2,35,1,11
	.byte	'S14WOP',0,1
	.word	644
	.byte	1,1,2,35,1,11
	.byte	'S15WOP',0,1
	.word	644
	.byte	1,0,2,35,1,11
	.byte	'S16WOP',0,1
	.word	644
	.byte	1,7,2,35,2,11
	.byte	'S17WOP',0,1
	.word	644
	.byte	1,6,2,35,2,11
	.byte	'S18WOP',0,1
	.word	644
	.byte	1,5,2,35,2,11
	.byte	'S19WOP',0,1
	.word	644
	.byte	1,4,2,35,2,11
	.byte	'S20WOP',0,1
	.word	644
	.byte	1,3,2,35,2,11
	.byte	'S21WOP',0,1
	.word	644
	.byte	1,2,2,35,2,11
	.byte	'S22WOP',0,1
	.word	644
	.byte	1,1,2,35,2,11
	.byte	'S23WOP',0,1
	.word	644
	.byte	1,0,2,35,2,11
	.byte	'S24WOP',0,1
	.word	644
	.byte	1,7,2,35,3,11
	.byte	'S25WOP',0,1
	.word	644
	.byte	1,6,2,35,3,11
	.byte	'S26WOP',0,1
	.word	644
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	644
	.byte	4,1,2,35,3,11
	.byte	'DATM',0,1
	.word	644
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_FLASH_PROCONWOP_Bits',0,29,194,3,3
	.word	86760
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG0_Bits',0,29,197,3,16,4,11
	.byte	'TAG',0,1
	.word	644
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	469
	.byte	26,0,2,35,0,0,29
	.byte	'Ifx_FLASH_RDB_CFG0_Bits',0,29,201,3,3
	.word	87342
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG1_Bits',0,29,204,3,16,4,11
	.byte	'TAG',0,1
	.word	644
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	469
	.byte	26,0,2,35,0,0,29
	.byte	'Ifx_FLASH_RDB_CFG1_Bits',0,29,208,3,3
	.word	87444
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG2_Bits',0,29,211,3,16,4,11
	.byte	'TAG',0,1
	.word	644
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	469
	.byte	26,0,2,35,0,0,29
	.byte	'Ifx_FLASH_RDB_CFG2_Bits',0,29,215,3,3
	.word	87546
	.byte	10
	.byte	'_Ifx_FLASH_RRAD_Bits',0,29,218,3,16,4,11
	.byte	'reserved_0',0,1
	.word	644
	.byte	3,5,2,35,0,11
	.byte	'ADD',0,4
	.word	469
	.byte	29,0,2,35,0,0,29
	.byte	'Ifx_FLASH_RRAD_Bits',0,29,222,3,3
	.word	87648
	.byte	10
	.byte	'_Ifx_FLASH_RRCT_Bits',0,29,225,3,16,4,11
	.byte	'STRT',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'STP',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'BUSY',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'DONE',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'ERR',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	644
	.byte	3,0,2,35,0,11
	.byte	'EOBM',0,1
	.word	644
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	644
	.byte	7,0,2,35,1,11
	.byte	'CNT',0,2
	.word	661
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_FLASH_RRCT_Bits',0,29,236,3,3
	.word	87742
	.byte	10
	.byte	'_Ifx_FLASH_RRD0_Bits',0,29,239,3,16,4,11
	.byte	'DATA',0,4
	.word	469
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_FLASH_RRD0_Bits',0,29,242,3,3
	.word	87952
	.byte	10
	.byte	'_Ifx_FLASH_RRD1_Bits',0,29,245,3,16,4,11
	.byte	'DATA',0,4
	.word	469
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_FLASH_RRD1_Bits',0,29,248,3,3
	.word	88025
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_CFG_Bits',0,29,251,3,16,4,11
	.byte	'SEL',0,1
	.word	644
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	644
	.byte	2,0,2,35,0,11
	.byte	'CLR',0,1
	.word	644
	.byte	1,7,2,35,1,11
	.byte	'DIS',0,1
	.word	644
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	469
	.byte	22,0,2,35,0,0,29
	.byte	'Ifx_FLASH_UBAB_CFG_Bits',0,29,130,4,3
	.word	88098
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_STAT_Bits',0,29,133,4,16,4,11
	.byte	'VLD0',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	469
	.byte	31,0,2,35,0,0,29
	.byte	'Ifx_FLASH_UBAB_STAT_Bits',0,29,137,4,3
	.word	88253
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_TOP_Bits',0,29,140,4,16,4,11
	.byte	'reserved_0',0,1
	.word	644
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	469
	.byte	19,8,2,35,0,11
	.byte	'ERR',0,1
	.word	644
	.byte	6,2,2,35,3,11
	.byte	'VLD',0,1
	.word	644
	.byte	1,1,2,35,3,11
	.byte	'CLR',0,1
	.word	644
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_FLASH_UBAB_TOP_Bits',0,29,147,4,3
	.word	88358
	.byte	12,29,155,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	80763
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_ACCEN0',0,29,160,4,3
	.word	88506
	.byte	12,29,163,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81324
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_ACCEN1',0,29,168,4,3
	.word	88572
	.byte	12,29,171,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81405
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_CBAB_CFG',0,29,176,4,3
	.word	88638
	.byte	12,29,179,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81558
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_CBAB_STAT',0,29,184,4,3
	.word	88706
	.byte	12,29,187,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81806
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_CBAB_TOP',0,29,192,4,3
	.word	88775
	.byte	12,29,195,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81952
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_COMM0',0,29,200,4,3
	.word	88843
	.byte	12,29,203,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	82050
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_COMM1',0,29,208,4,3
	.word	88908
	.byte	12,29,211,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	82166
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_COMM2',0,29,216,4,3
	.word	88973
	.byte	12,29,219,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	82282
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_ECCRD',0,29,224,4,3
	.word	89038
	.byte	12,29,227,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	82422
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_ECCRP',0,29,232,4,3
	.word	89103
	.byte	12,29,235,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	82562
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_ECCW',0,29,240,4,3
	.word	89168
	.byte	12,29,243,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	82701
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_FCON',0,29,248,4,3
	.word	89232
	.byte	12,29,251,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	83063
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_FPRO',0,29,128,5,3
	.word	89296
	.byte	12,29,131,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	83504
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_FSR',0,29,136,5,3
	.word	89360
	.byte	12,29,139,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	84110
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_ID',0,29,144,5,3
	.word	89423
	.byte	12,29,147,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	84221
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_MARD',0,29,152,5,3
	.word	89485
	.byte	12,29,155,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	84435
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_MARP',0,29,160,5,3
	.word	89549
	.byte	12,29,163,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	84622
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_PROCOND',0,29,168,5,3
	.word	89613
	.byte	12,29,171,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	84946
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_PROCONDBG',0,29,176,5,3
	.word	89680
	.byte	12,29,179,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	85089
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_PROCONHSM',0,29,184,5,3
	.word	89749
	.byte	12,29,187,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	85278
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_PROCONHSMCOTP',0,29,192,5,3
	.word	89818
	.byte	12,29,195,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	85641
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_PROCONOTP',0,29,200,5,3
	.word	89891
	.byte	12,29,203,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	86236
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_PROCONP',0,29,208,5,3
	.word	89960
	.byte	12,29,211,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	86760
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_PROCONWOP',0,29,216,5,3
	.word	90027
	.byte	12,29,219,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	87342
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_RDB_CFG0',0,29,224,5,3
	.word	90096
	.byte	12,29,227,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	87444
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_RDB_CFG1',0,29,232,5,3
	.word	90164
	.byte	12,29,235,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	87546
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_RDB_CFG2',0,29,240,5,3
	.word	90232
	.byte	12,29,243,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	87648
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_RRAD',0,29,248,5,3
	.word	90300
	.byte	12,29,251,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	87742
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_RRCT',0,29,128,6,3
	.word	90364
	.byte	12,29,131,6,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	87952
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_RRD0',0,29,136,6,3
	.word	90428
	.byte	12,29,139,6,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	88025
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_RRD1',0,29,144,6,3
	.word	90492
	.byte	12,29,147,6,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	88098
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_UBAB_CFG',0,29,152,6,3
	.word	90556
	.byte	12,29,155,6,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	88253
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_UBAB_STAT',0,29,160,6,3
	.word	90624
	.byte	12,29,163,6,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	88358
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_UBAB_TOP',0,29,168,6,3
	.word	90693
	.byte	10
	.byte	'_Ifx_FLASH_CBAB',0,29,179,6,25,12,13
	.byte	'CFG',0
	.word	88638
	.byte	4,2,35,0,13
	.byte	'STAT',0
	.word	88706
	.byte	4,2,35,4,13
	.byte	'TOP',0
	.word	88775
	.byte	4,2,35,8,0,14
	.word	90761
	.byte	29
	.byte	'Ifx_FLASH_CBAB',0,29,184,6,3
	.word	90824
	.byte	10
	.byte	'_Ifx_FLASH_RDB',0,29,187,6,25,12,13
	.byte	'CFG0',0
	.word	90096
	.byte	4,2,35,0,13
	.byte	'CFG1',0
	.word	90164
	.byte	4,2,35,4,13
	.byte	'CFG2',0
	.word	90232
	.byte	4,2,35,8,0,14
	.word	90853
	.byte	29
	.byte	'Ifx_FLASH_RDB',0,29,192,6,3
	.word	90917
	.byte	10
	.byte	'_Ifx_FLASH_UBAB',0,29,195,6,25,12,13
	.byte	'CFG',0
	.word	90556
	.byte	4,2,35,0,13
	.byte	'STAT',0
	.word	90624
	.byte	4,2,35,4,13
	.byte	'TOP',0
	.word	90693
	.byte	4,2,35,8,0,14
	.word	90945
	.byte	29
	.byte	'Ifx_FLASH_UBAB',0,29,200,6,3
	.word	91008
	.byte	29
	.byte	'IfxScuCcu_PllStepsFunctionHook',0,9,148,1,16
	.word	208
	.byte	20,9,212,5,9,8,13
	.byte	'value',0
	.word	10305
	.byte	4,2,35,0,13
	.byte	'mask',0
	.word	10305
	.byte	4,2,35,4,0,29
	.byte	'IfxScuCcu_CcuconRegConfig',0,9,216,5,3
	.word	91077
	.byte	20,9,221,5,9,8,13
	.byte	'pDivider',0
	.word	644
	.byte	1,2,35,0,13
	.byte	'nDivider',0
	.word	644
	.byte	1,2,35,1,13
	.byte	'k2Initial',0
	.word	644
	.byte	1,2,35,2,13
	.byte	'waitTime',0
	.word	265
	.byte	4,2,35,4,0,29
	.byte	'IfxScuCcu_InitialStepConfig',0,9,227,5,3
	.word	91148
	.byte	20,9,231,5,9,12,13
	.byte	'k2Step',0
	.word	644
	.byte	1,2,35,0,13
	.byte	'waitTime',0
	.word	265
	.byte	4,2,35,2,13
	.byte	'hookFunction',0
	.word	91037
	.byte	4,2,35,8,0,29
	.byte	'IfxScuCcu_PllStepsConfig',0,9,236,5,3
	.word	91265
	.byte	3
	.word	205
	.byte	20,9,244,5,9,48,13
	.byte	'ccucon0',0
	.word	91077
	.byte	8,2,35,0,13
	.byte	'ccucon1',0
	.word	91077
	.byte	8,2,35,8,13
	.byte	'ccucon2',0
	.word	91077
	.byte	8,2,35,16,13
	.byte	'ccucon5',0
	.word	91077
	.byte	8,2,35,24,13
	.byte	'ccucon6',0
	.word	91077
	.byte	8,2,35,32,13
	.byte	'ccucon7',0
	.word	91077
	.byte	8,2,35,40,0,29
	.byte	'IfxScuCcu_ClockDistributionConfig',0,9,252,5,3
	.word	91367
	.byte	20,9,128,6,9,8,13
	.byte	'value',0
	.word	10305
	.byte	4,2,35,0,13
	.byte	'mask',0
	.word	10305
	.byte	4,2,35,4,0,29
	.byte	'IfxScuCcu_FlashWaitstateConfig',0,9,132,6,3
	.word	91519
	.byte	3
	.word	91265
	.byte	20,9,137,6,9,16,13
	.byte	'numOfPllDividerSteps',0
	.word	644
	.byte	1,2,35,0,13
	.byte	'pllDividerStep',0
	.word	91595
	.byte	4,2,35,4,13
	.byte	'pllInitialStep',0
	.word	91148
	.byte	8,2,35,8,0,29
	.byte	'IfxScuCcu_SysPllConfig',0,9,142,6,3
	.word	91600
	.byte	17,30,69,9,1,18
	.byte	'IfxSrc_Tos_cpu0',0,0,18
	.byte	'IfxSrc_Tos_cpu1',0,1,18
	.byte	'IfxSrc_Tos_dma',0,3,0,29
	.byte	'IfxSrc_Tos',0,30,74,3
	.word	91717
	.byte	20,31,59,15,16,13
	.byte	'module',0
	.word	17490
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	55159
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	26374
	.byte	1,2,35,12,0,28
	.word	91795
	.byte	29
	.byte	'IfxAsclin_Cts_In',0,31,64,3
	.word	91846
	.byte	20,31,67,15,16,13
	.byte	'module',0
	.word	17490
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	55159
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	26374
	.byte	1,2,35,12,0,28
	.word	91876
	.byte	29
	.byte	'IfxAsclin_Rx_In',0,31,72,3
	.word	91927
	.byte	20,31,75,15,16,13
	.byte	'module',0
	.word	17490
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	55159
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	9489
	.byte	1,2,35,12,0,28
	.word	91956
	.byte	29
	.byte	'IfxAsclin_Rts_Out',0,31,80,3
	.word	92007
	.byte	20,31,83,15,16,13
	.byte	'module',0
	.word	17490
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	55159
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	9489
	.byte	1,2,35,12,0,28
	.word	92038
	.byte	29
	.byte	'IfxAsclin_Sclk_Out',0,31,88,3
	.word	92089
	.byte	20,31,91,15,16,13
	.byte	'module',0
	.word	17490
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	55159
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	9489
	.byte	1,2,35,12,0,28
	.word	92121
	.byte	29
	.byte	'IfxAsclin_Slso_Out',0,31,96,3
	.word	92172
	.byte	20,31,99,15,16,13
	.byte	'module',0
	.word	17490
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	55159
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	9489
	.byte	1,2,35,12,0,28
	.word	92204
	.byte	29
	.byte	'IfxAsclin_Tx_Out',0,31,104,3
	.word	92255
	.byte	17,12,82,9,1,18
	.byte	'IfxAsclin_Checksum_classic',0,0,18
	.byte	'IfxAsclin_Checksum_enhanced',0,1,0,29
	.byte	'IfxAsclin_Checksum',0,12,86,3
	.word	92285
	.byte	17,12,91,9,1,18
	.byte	'IfxAsclin_ChecksumInjection_notWritten',0,0,18
	.byte	'IfxAsclin_ChecksumInjection_written',0,1,0,29
	.byte	'IfxAsclin_ChecksumInjection',0,12,95,3
	.word	92377
	.byte	17,12,101,9,1,18
	.byte	'IfxAsclin_ClockPolarity_idleLow',0,0,18
	.byte	'IfxAsclin_ClockPolarity_idleHigh',0,1,0,29
	.byte	'IfxAsclin_ClockPolarity',0,12,105,3
	.word	92498
	.byte	17,12,110,9,1,18
	.byte	'IfxAsclin_ClockSource_noClock',0,0,18
	.byte	'IfxAsclin_ClockSource_kernelClock',0,1,18
	.byte	'IfxAsclin_ClockSource_oscillatorClock',0,2,18
	.byte	'IfxAsclin_ClockSource_flexRayClock',0,4,18
	.byte	'IfxAsclin_ClockSource_ascFastClock',0,8,18
	.byte	'IfxAsclin_ClockSource_ascSlowClock',0,16,0,29
	.byte	'IfxAsclin_ClockSource',0,12,118,3
	.word	92605
	.byte	29
	.byte	'IfxAsclin_CtsInputSelect',0,12,129,1,3
	.word	17557
	.byte	17,12,134,1,9,1,18
	.byte	'IfxAsclin_DataLength_1',0,0,18
	.byte	'IfxAsclin_DataLength_2',0,1,18
	.byte	'IfxAsclin_DataLength_3',0,2,18
	.byte	'IfxAsclin_DataLength_4',0,3,18
	.byte	'IfxAsclin_DataLength_5',0,4,18
	.byte	'IfxAsclin_DataLength_6',0,5,18
	.byte	'IfxAsclin_DataLength_7',0,6,18
	.byte	'IfxAsclin_DataLength_8',0,7,18
	.byte	'IfxAsclin_DataLength_9',0,8,18
	.byte	'IfxAsclin_DataLength_10',0,9,18
	.byte	'IfxAsclin_DataLength_11',0,10,18
	.byte	'IfxAsclin_DataLength_12',0,11,18
	.byte	'IfxAsclin_DataLength_13',0,12,18
	.byte	'IfxAsclin_DataLength_14',0,13,18
	.byte	'IfxAsclin_DataLength_15',0,14,18
	.byte	'IfxAsclin_DataLength_16',0,15,0,29
	.byte	'IfxAsclin_DataLength',0,12,152,1,3
	.word	92894
	.byte	17,12,157,1,9,1,18
	.byte	'IfxAsclin_FrameMode_initialise',0,0,18
	.byte	'IfxAsclin_FrameMode_asc',0,1,18
	.byte	'IfxAsclin_FrameMode_spi',0,2,18
	.byte	'IfxAsclin_FrameMode_lin',0,3,0,29
	.byte	'IfxAsclin_FrameMode',0,12,163,1,3
	.word	93338
	.byte	17,12,168,1,9,1,18
	.byte	'IfxAsclin_HeaderResponseSelect_headerAndResponse',0,0,18
	.byte	'IfxAsclin_HeaderResponseSelect_headerOnly',0,1,0,29
	.byte	'IfxAsclin_HeaderResponseSelect',0,12,172,1,3
	.word	93485
	.byte	17,12,179,1,9,1,18
	.byte	'IfxAsclin_IdleDelay_0',0,0,18
	.byte	'IfxAsclin_IdleDelay_1',0,1,18
	.byte	'IfxAsclin_IdleDelay_2',0,2,18
	.byte	'IfxAsclin_IdleDelay_3',0,3,18
	.byte	'IfxAsclin_IdleDelay_4',0,4,18
	.byte	'IfxAsclin_IdleDelay_5',0,5,18
	.byte	'IfxAsclin_IdleDelay_6',0,6,18
	.byte	'IfxAsclin_IdleDelay_7',0,7,0,29
	.byte	'IfxAsclin_IdleDelay',0,12,189,1,3
	.word	93627
	.byte	17,12,195,1,9,1,18
	.byte	'IfxAsclin_LeadDelay_0',0,0,18
	.byte	'IfxAsclin_LeadDelay_1',0,1,18
	.byte	'IfxAsclin_LeadDelay_2',0,2,18
	.byte	'IfxAsclin_LeadDelay_3',0,3,18
	.byte	'IfxAsclin_LeadDelay_4',0,4,18
	.byte	'IfxAsclin_LeadDelay_5',0,5,18
	.byte	'IfxAsclin_LeadDelay_6',0,6,18
	.byte	'IfxAsclin_LeadDelay_7',0,7,0,29
	.byte	'IfxAsclin_LeadDelay',0,12,205,1,3
	.word	93855
	.byte	17,12,210,1,9,1,18
	.byte	'IfxAsclin_LinMode_slave',0,0,18
	.byte	'IfxAsclin_LinMode_master',0,1,0,29
	.byte	'IfxAsclin_LinMode',0,12,214,1,3
	.word	94083
	.byte	17,12,219,1,9,1,18
	.byte	'IfxAsclin_LinResponseTimeoutMode_frameTimeout',0,0,18
	.byte	'IfxAsclin_LinResponseTimeoutMode_responseTimeout',0,1,0,29
	.byte	'IfxAsclin_LinResponseTimeoutMode',0,12,223,1,3
	.word	94170
	.byte	17,12,228,1,9,1,18
	.byte	'IfxAsclin_OversamplingFactor_4',0,3,18
	.byte	'IfxAsclin_OversamplingFactor_5',0,4,18
	.byte	'IfxAsclin_OversamplingFactor_6',0,5,18
	.byte	'IfxAsclin_OversamplingFactor_7',0,6,18
	.byte	'IfxAsclin_OversamplingFactor_8',0,7,18
	.byte	'IfxAsclin_OversamplingFactor_9',0,8,18
	.byte	'IfxAsclin_OversamplingFactor_10',0,9,18
	.byte	'IfxAsclin_OversamplingFactor_11',0,10,18
	.byte	'IfxAsclin_OversamplingFactor_12',0,11,18
	.byte	'IfxAsclin_OversamplingFactor_13',0,12,18
	.byte	'IfxAsclin_OversamplingFactor_14',0,13,18
	.byte	'IfxAsclin_OversamplingFactor_15',0,14,18
	.byte	'IfxAsclin_OversamplingFactor_16',0,15,0,29
	.byte	'IfxAsclin_OversamplingFactor',0,12,243,1,3
	.word	94318
	.byte	17,12,248,1,9,1,18
	.byte	'IfxAsclin_ParityType_even',0,0,18
	.byte	'IfxAsclin_ParityType_odd',0,1,0,29
	.byte	'IfxAsclin_ParityType',0,12,252,1,3
	.word	94799
	.byte	17,12,129,2,9,1,18
	.byte	'IfxAsclin_ReceiveBufferMode_rxFifo',0,0,18
	.byte	'IfxAsclin_ReceiveBufferMode_rxBuffer',0,1,0,29
	.byte	'IfxAsclin_ReceiveBufferMode',0,12,133,2,3
	.word	94891
	.byte	17,12,138,2,9,1,18
	.byte	'IfxAsclin_RtsCtsPolarity_activeHigh',0,0,18
	.byte	'IfxAsclin_RtsCtsPolarity_activeLow',0,1,0,29
	.byte	'IfxAsclin_RtsCtsPolarity',0,12,142,2,3
	.word	95011
	.byte	17,12,147,2,9,1,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_1',0,0,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_2',0,1,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_3',0,2,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_4',0,3,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_5',0,4,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_6',0,5,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_7',0,6,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_8',0,7,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_9',0,8,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_10',0,9,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_11',0,10,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_12',0,11,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_13',0,12,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_14',0,13,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_15',0,14,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_16',0,15,0,29
	.byte	'IfxAsclin_RxFifoInterruptLevel',0,12,165,2,3
	.word	95127
	.byte	17,12,170,2,9,1,18
	.byte	'IfxAsclin_RxFifoOutletWidth_0',0,0,18
	.byte	'IfxAsclin_RxFifoOutletWidth_1',0,1,18
	.byte	'IfxAsclin_RxFifoOutletWidth_2',0,2,18
	.byte	'IfxAsclin_RxFifoOutletWidth_3',0,3,0,29
	.byte	'IfxAsclin_RxFifoOutletWidth',0,12,176,2,3
	.word	95741
	.byte	29
	.byte	'IfxAsclin_RxInputSelect',0,12,191,2,3
	.word	17741
	.byte	17,12,196,2,9,1,18
	.byte	'IfxAsclin_SamplePointPosition_1',0,1,18
	.byte	'IfxAsclin_SamplePointPosition_2',0,2,18
	.byte	'IfxAsclin_SamplePointPosition_3',0,3,18
	.byte	'IfxAsclin_SamplePointPosition_4',0,4,18
	.byte	'IfxAsclin_SamplePointPosition_5',0,5,18
	.byte	'IfxAsclin_SamplePointPosition_6',0,6,18
	.byte	'IfxAsclin_SamplePointPosition_7',0,7,18
	.byte	'IfxAsclin_SamplePointPosition_8',0,8,18
	.byte	'IfxAsclin_SamplePointPosition_9',0,9,18
	.byte	'IfxAsclin_SamplePointPosition_10',0,10,18
	.byte	'IfxAsclin_SamplePointPosition_11',0,11,18
	.byte	'IfxAsclin_SamplePointPosition_12',0,12,18
	.byte	'IfxAsclin_SamplePointPosition_13',0,13,18
	.byte	'IfxAsclin_SamplePointPosition_14',0,14,18
	.byte	'IfxAsclin_SamplePointPosition_15',0,15,0,29
	.byte	'IfxAsclin_SamplePointPosition',0,12,213,2,3
	.word	95946
	.byte	17,12,218,2,9,1,18
	.byte	'IfxAsclin_SamplesPerBit_one',0,0,18
	.byte	'IfxAsclin_SamplesPerBit_three',0,1,0,29
	.byte	'IfxAsclin_SamplesPerBit',0,12,222,2,3
	.word	96508
	.byte	17,12,228,2,9,1,18
	.byte	'IfxAsclin_ShiftDirection_lsbFirst',0,0,18
	.byte	'IfxAsclin_ShiftDirection_msbFirst',0,1,0,29
	.byte	'IfxAsclin_ShiftDirection',0,12,232,2,3
	.word	96610
	.byte	17,12,238,2,9,1,18
	.byte	'IfxAsclin_SlavePolarity_idleLow',0,0,18
	.byte	'IfxAsclin_SlavePolarity_idlehigh',0,1,0,29
	.byte	'IfxAsclin_SlavePolarity',0,12,242,2,3
	.word	96723
	.byte	17,12,247,2,9,1,18
	.byte	'IfxAsclin_SleepMode_enable',0,0,18
	.byte	'IfxAsclin_SleepMode_disable',0,1,0,29
	.byte	'IfxAsclin_SleepMode',0,12,251,2,3
	.word	96832
	.byte	17,12,136,3,9,1,18
	.byte	'IfxAsclin_StopBit_0',0,0,18
	.byte	'IfxAsclin_StopBit_1',0,1,18
	.byte	'IfxAsclin_StopBit_2',0,2,18
	.byte	'IfxAsclin_StopBit_3',0,3,18
	.byte	'IfxAsclin_StopBit_4',0,4,18
	.byte	'IfxAsclin_StopBit_5',0,5,18
	.byte	'IfxAsclin_StopBit_6',0,6,18
	.byte	'IfxAsclin_StopBit_7',0,7,0,29
	.byte	'IfxAsclin_StopBit',0,12,146,3,3
	.word	96927
	.byte	17,12,150,3,9,1,18
	.byte	'IfxAsclin_SuspendMode_none',0,0,18
	.byte	'IfxAsclin_SuspendMode_hard',0,1,18
	.byte	'IfxAsclin_SuspendMode_soft',0,2,0,29
	.byte	'IfxAsclin_SuspendMode',0,12,155,3,3
	.word	97137
	.byte	17,12,160,3,9,1,18
	.byte	'IfxAsclin_TxFifoInletWidth_0',0,0,18
	.byte	'IfxAsclin_TxFifoInletWidth_1',0,1,18
	.byte	'IfxAsclin_TxFifoInletWidth_2',0,2,18
	.byte	'IfxAsclin_TxFifoInletWidth_3',0,3,0,29
	.byte	'IfxAsclin_TxFifoInletWidth',0,12,166,3,3
	.word	97262
	.byte	17,12,171,3,9,1,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_0',0,0,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_1',0,1,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_2',0,2,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_3',0,3,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_4',0,4,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_5',0,5,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_6',0,6,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_7',0,7,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_8',0,8,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_9',0,9,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_10',0,10,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_11',0,11,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_12',0,12,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_13',0,13,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_14',0,14,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_15',0,15,0,29
	.byte	'IfxAsclin_TxFifoInterruptLevel',0,12,189,3,3
	.word	97429
	.byte	29
	.byte	'Ifx_Fifo_Shared',0,14,66,3
	.word	18058
	.byte	29
	.byte	'Ifx_Fifo',0,14,83,3
	.word	18149
	.byte	17,15,151,1,9,1,18
	.byte	'IfxStm_Comparator_0',0,0,18
	.byte	'IfxStm_Comparator_1',0,1,0,29
	.byte	'IfxStm_Comparator',0,15,155,1,3
	.word	98083
	.byte	17,15,159,1,9,1,18
	.byte	'IfxStm_ComparatorInterrupt_ir0',0,0,18
	.byte	'IfxStm_ComparatorInterrupt_ir1',0,1,0,29
	.byte	'IfxStm_ComparatorInterrupt',0,15,163,1,3
	.word	98161
	.byte	17,15,167,1,9,1,18
	.byte	'IfxStm_ComparatorOffset_0',0,0,18
	.byte	'IfxStm_ComparatorOffset_1',0,1,18
	.byte	'IfxStm_ComparatorOffset_2',0,2,18
	.byte	'IfxStm_ComparatorOffset_3',0,3,18
	.byte	'IfxStm_ComparatorOffset_4',0,4,18
	.byte	'IfxStm_ComparatorOffset_5',0,5,18
	.byte	'IfxStm_ComparatorOffset_6',0,6,18
	.byte	'IfxStm_ComparatorOffset_7',0,7,18
	.byte	'IfxStm_ComparatorOffset_8',0,8,18
	.byte	'IfxStm_ComparatorOffset_9',0,9,18
	.byte	'IfxStm_ComparatorOffset_10',0,10,18
	.byte	'IfxStm_ComparatorOffset_11',0,11,18
	.byte	'IfxStm_ComparatorOffset_12',0,12,18
	.byte	'IfxStm_ComparatorOffset_13',0,13,18
	.byte	'IfxStm_ComparatorOffset_14',0,14,18
	.byte	'IfxStm_ComparatorOffset_15',0,15,18
	.byte	'IfxStm_ComparatorOffset_16',0,16,18
	.byte	'IfxStm_ComparatorOffset_17',0,17,18
	.byte	'IfxStm_ComparatorOffset_18',0,18,18
	.byte	'IfxStm_ComparatorOffset_19',0,19,18
	.byte	'IfxStm_ComparatorOffset_20',0,20,18
	.byte	'IfxStm_ComparatorOffset_21',0,21,18
	.byte	'IfxStm_ComparatorOffset_22',0,22,18
	.byte	'IfxStm_ComparatorOffset_23',0,23,18
	.byte	'IfxStm_ComparatorOffset_24',0,24,18
	.byte	'IfxStm_ComparatorOffset_25',0,25,18
	.byte	'IfxStm_ComparatorOffset_26',0,26,18
	.byte	'IfxStm_ComparatorOffset_27',0,27,18
	.byte	'IfxStm_ComparatorOffset_28',0,28,18
	.byte	'IfxStm_ComparatorOffset_29',0,29,18
	.byte	'IfxStm_ComparatorOffset_30',0,30,18
	.byte	'IfxStm_ComparatorOffset_31',0,31,0,29
	.byte	'IfxStm_ComparatorOffset',0,15,201,1,3
	.word	98270
	.byte	17,15,205,1,9,1,18
	.byte	'IfxStm_ComparatorSize_1Bit',0,0,18
	.byte	'IfxStm_ComparatorSize_2Bits',0,1,18
	.byte	'IfxStm_ComparatorSize_3Bits',0,2,18
	.byte	'IfxStm_ComparatorSize_4Bits',0,3,18
	.byte	'IfxStm_ComparatorSize_5Bits',0,4,18
	.byte	'IfxStm_ComparatorSize_6Bits',0,5,18
	.byte	'IfxStm_ComparatorSize_7Bits',0,6,18
	.byte	'IfxStm_ComparatorSize_8Bits',0,7,18
	.byte	'IfxStm_ComparatorSize_9Bits',0,8,18
	.byte	'IfxStm_ComparatorSize_10Bits',0,9,18
	.byte	'IfxStm_ComparatorSize_11Bits',0,10,18
	.byte	'IfxStm_ComparatorSize_12Bits',0,11,18
	.byte	'IfxStm_ComparatorSize_13Bits',0,12,18
	.byte	'IfxStm_ComparatorSize_14Bits',0,13,18
	.byte	'IfxStm_ComparatorSize_15Bits',0,14,18
	.byte	'IfxStm_ComparatorSize_16Bits',0,15,18
	.byte	'IfxStm_ComparatorSize_17Bits',0,16,18
	.byte	'IfxStm_ComparatorSize_18Bits',0,17,18
	.byte	'IfxStm_ComparatorSize_19Bits',0,18,18
	.byte	'IfxStm_ComparatorSize_20Bits',0,19,18
	.byte	'IfxStm_ComparatorSize_21Bits',0,20,18
	.byte	'IfxStm_ComparatorSize_22Bits',0,21,18
	.byte	'IfxStm_ComparatorSize_23Bits',0,22,18
	.byte	'IfxStm_ComparatorSize_24Bits',0,23,18
	.byte	'IfxStm_ComparatorSize_25Bits',0,24,18
	.byte	'IfxStm_ComparatorSize_26Bits',0,25,18
	.byte	'IfxStm_ComparatorSize_27Bits',0,26,18
	.byte	'IfxStm_ComparatorSize_28Bits',0,27,18
	.byte	'IfxStm_ComparatorSize_29Bits',0,28,18
	.byte	'IfxStm_ComparatorSize_30Bits',0,29,18
	.byte	'IfxStm_ComparatorSize_31Bits',0,30,18
	.byte	'IfxStm_ComparatorSize_32Bits',0,31,0,29
	.byte	'IfxStm_ComparatorSize',0,15,239,1,3
	.word	99228
	.byte	17,15,244,1,9,1,18
	.byte	'IfxStm_SleepMode_enable',0,0,18
	.byte	'IfxStm_SleepMode_disable',0,1,0,29
	.byte	'IfxStm_SleepMode',0,15,248,1,3
	.word	100248
	.byte	17,15,252,1,9,1,18
	.byte	'IfxStm_SuspendMode_none',0,0,18
	.byte	'IfxStm_SuspendMode_hard',0,1,18
	.byte	'IfxStm_SuspendMode_soft',0,2,0,29
	.byte	'IfxStm_SuspendMode',0,15,129,2,3
	.word	100334
	.byte	29
	.byte	'IfxStdIf_InterfaceDriver',0,32,118,15
	.word	383
	.byte	3
	.word	18033
	.byte	31
	.word	644
	.byte	1,1,32
	.word	383
	.byte	32
	.word	383
	.byte	32
	.word	100480
	.byte	32
	.word	22006
	.byte	0,3
	.word	100485
	.byte	29
	.byte	'IfxStdIf_DPipe_Write',0,33,92,19
	.word	100513
	.byte	29
	.byte	'IfxStdIf_DPipe_Read',0,33,107,19
	.word	100513
	.byte	31
	.word	18046
	.byte	1,1,32
	.word	383
	.byte	0,3
	.word	100575
	.byte	29
	.byte	'IfxStdIf_DPipe_GetReadCount',0,33,115,18
	.word	100588
	.byte	14
	.word	644
	.byte	3
	.word	100629
	.byte	31
	.word	100634
	.byte	1,1,32
	.word	383
	.byte	0,3
	.word	100639
	.byte	29
	.byte	'IfxStdIf_DPipe_GetReadEvent',0,33,123,36
	.word	100652
	.byte	29
	.byte	'IfxStdIf_DPipe_GetWriteCount',0,33,147,1,18
	.word	100588
	.byte	3
	.word	100639
	.byte	29
	.byte	'IfxStdIf_DPipe_GetWriteEvent',0,33,155,1,37
	.word	100731
	.byte	31
	.word	644
	.byte	1,1,32
	.word	383
	.byte	32
	.word	18033
	.byte	32
	.word	22006
	.byte	0,3
	.word	100774
	.byte	29
	.byte	'IfxStdIf_DPipe_CanReadCount',0,33,166,1,19
	.word	100797
	.byte	29
	.byte	'IfxStdIf_DPipe_CanWriteCount',0,33,177,1,19
	.word	100797
	.byte	31
	.word	644
	.byte	1,1,32
	.word	383
	.byte	32
	.word	22006
	.byte	0,3
	.word	100877
	.byte	29
	.byte	'IfxStdIf_DPipe_FlushTx',0,33,186,1,19
	.word	100895
	.byte	33,1,1,32
	.word	383
	.byte	0,3
	.word	100932
	.byte	29
	.byte	'IfxStdIf_DPipe_ClearTx',0,33,200,1,16
	.word	100941
	.byte	29
	.byte	'IfxStdIf_DPipe_ClearRx',0,33,193,1,16
	.word	100941
	.byte	29
	.byte	'IfxStdIf_DPipe_OnReceive',0,33,208,1,16
	.word	100941
	.byte	29
	.byte	'IfxStdIf_DPipe_OnTransmit',0,33,215,1,16
	.word	100941
	.byte	29
	.byte	'IfxStdIf_DPipe_OnError',0,33,222,1,16
	.word	100941
	.byte	31
	.word	10305
	.byte	1,1,32
	.word	383
	.byte	0,3
	.word	101111
	.byte	29
	.byte	'IfxStdIf_DPipe_GetSendCount',0,33,131,1,18
	.word	101124
	.byte	31
	.word	22006
	.byte	1,1,32
	.word	383
	.byte	0,3
	.word	101166
	.byte	29
	.byte	'IfxStdIf_DPipe_GetTxTimeStamp',0,33,139,1,24
	.word	101179
	.byte	29
	.byte	'IfxStdIf_DPipe_ResetSendCount',0,33,229,1,16
	.word	100941
	.byte	10
	.byte	'IfxStdIf_DPipe_',0,33,233,1,8,76,13
	.byte	'driver',0
	.word	100447
	.byte	4,2,35,0,13
	.byte	'txDisabled',0
	.word	644
	.byte	1,2,35,4,13
	.byte	'write',0
	.word	100518
	.byte	4,2,35,8,13
	.byte	'read',0
	.word	100547
	.byte	4,2,35,12,13
	.byte	'getReadCount',0
	.word	100593
	.byte	4,2,35,16,13
	.byte	'getReadEvent',0
	.word	100657
	.byte	4,2,35,20,13
	.byte	'getWriteCount',0
	.word	100693
	.byte	4,2,35,24,13
	.byte	'getWriteEvent',0
	.word	100736
	.byte	4,2,35,28,13
	.byte	'canReadCount',0
	.word	100802
	.byte	4,2,35,32,13
	.byte	'canWriteCount',0
	.word	100839
	.byte	4,2,35,36,13
	.byte	'flushTx',0
	.word	100900
	.byte	4,2,35,40,13
	.byte	'clearTx',0
	.word	100946
	.byte	4,2,35,44,13
	.byte	'clearRx',0
	.word	100978
	.byte	4,2,35,48,13
	.byte	'onReceive',0
	.word	101010
	.byte	4,2,35,52,13
	.byte	'onTransmit',0
	.word	101044
	.byte	4,2,35,56,13
	.byte	'onError',0
	.word	101079
	.byte	4,2,35,60,13
	.byte	'getSendCount',0
	.word	101129
	.byte	4,2,35,64,13
	.byte	'getTxTimeStamp',0
	.word	101184
	.byte	4,2,35,68,13
	.byte	'resetSendCount',0
	.word	101223
	.byte	4,2,35,72,0,29
	.byte	'IfxStdIf_DPipe',0,33,71,32
	.word	101262
	.byte	3
	.word	377
	.byte	3
	.word	100485
	.byte	3
	.word	100485
	.byte	3
	.word	100575
	.byte	3
	.word	100639
	.byte	3
	.word	100575
	.byte	3
	.word	100639
	.byte	3
	.word	100774
	.byte	3
	.word	100774
	.byte	3
	.word	100877
	.byte	3
	.word	100932
	.byte	3
	.word	100932
	.byte	3
	.word	100932
	.byte	3
	.word	100932
	.byte	3
	.word	100932
	.byte	3
	.word	101111
	.byte	3
	.word	101166
	.byte	3
	.word	100932
	.byte	14
	.word	644
	.byte	3
	.word	101775
	.byte	29
	.byte	'IfxStdIf_DPipe_WriteEvent',0,33,73,32
	.word	101780
	.byte	29
	.byte	'IfxStdIf_DPipe_ReadEvent',0,33,74,32
	.word	101780
	.byte	20,34,252,1,9,1,11
	.byte	'parityError',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'frameError',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'rxFifoOverflow',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'rxFifoUnderflow',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'txFifoOverflow',0,1
	.word	644
	.byte	1,3,2,35,0,0,29
	.byte	'IfxAsclin_Asc_ErrorFlags',0,34,131,2,3
	.word	101852
	.byte	20,34,137,2,9,8,13
	.byte	'baudrate',0
	.word	265
	.byte	4,2,35,0,13
	.byte	'prescaler',0
	.word	661
	.byte	2,2,35,4,13
	.byte	'oversampling',0
	.word	94318
	.byte	1,2,35,6,0,29
	.byte	'IfxAsclin_Asc_BaudRate',0,34,142,2,3
	.word	102017
	.byte	20,34,146,2,9,2,13
	.byte	'medianFilter',0
	.word	96508
	.byte	1,2,35,0,13
	.byte	'samplePointPosition',0
	.word	95946
	.byte	1,2,35,1,0,29
	.byte	'IfxAsclin_Asc_BitTimingControl',0,34,150,2,3
	.word	102115
	.byte	20,34,154,2,9,6,13
	.byte	'inWidth',0
	.word	97262
	.byte	1,2,35,0,13
	.byte	'outWidth',0
	.word	95741
	.byte	1,2,35,1,13
	.byte	'txFifoInterruptLevel',0
	.word	97429
	.byte	1,2,35,2,13
	.byte	'rxFifoInterruptLevel',0
	.word	95127
	.byte	1,2,35,3,13
	.byte	'buffMode',0
	.word	94891
	.byte	1,2,35,4,0,29
	.byte	'IfxAsclin_Asc_FifoControl',0,34,161,2,3
	.word	102213
	.byte	20,34,165,2,9,8,13
	.byte	'idleDelay',0
	.word	93627
	.byte	1,2,35,0,13
	.byte	'stopBit',0
	.word	96927
	.byte	1,2,35,1,13
	.byte	'frameMode',0
	.word	93338
	.byte	1,2,35,2,13
	.byte	'shiftDir',0
	.word	96610
	.byte	1,2,35,3,13
	.byte	'parityType',0
	.word	94799
	.byte	1,2,35,4,13
	.byte	'dataLength',0
	.word	92894
	.byte	1,2,35,5,13
	.byte	'parityBit',0
	.word	644
	.byte	1,2,35,6,0,29
	.byte	'IfxAsclin_Asc_FrameControl',0,34,174,2,3
	.word	102368
	.byte	20,34,178,2,9,8,13
	.byte	'txPriority',0
	.word	661
	.byte	2,2,35,0,13
	.byte	'rxPriority',0
	.word	661
	.byte	2,2,35,2,13
	.byte	'erPriority',0
	.word	661
	.byte	2,2,35,4,13
	.byte	'typeOfService',0
	.word	91717
	.byte	1,2,35,6,0,29
	.byte	'IfxAsclin_Asc_InterruptConfig',0,34,184,2,3
	.word	102543
	.byte	28
	.word	91795
	.byte	3
	.word	102672
	.byte	28
	.word	91876
	.byte	3
	.word	102682
	.byte	28
	.word	91956
	.byte	3
	.word	102692
	.byte	28
	.word	92204
	.byte	3
	.word	102702
	.byte	20,34,188,2,9,32,13
	.byte	'cts',0
	.word	102677
	.byte	4,2,35,0,13
	.byte	'ctsMode',0
	.word	9214
	.byte	1,2,35,4,13
	.byte	'rx',0
	.word	102687
	.byte	4,2,35,8,13
	.byte	'rxMode',0
	.word	9214
	.byte	1,2,35,12,13
	.byte	'rts',0
	.word	102697
	.byte	4,2,35,16,13
	.byte	'rtsMode',0
	.word	9419
	.byte	1,2,35,20,13
	.byte	'tx',0
	.word	102707
	.byte	4,2,35,24,13
	.byte	'txMode',0
	.word	9419
	.byte	1,2,35,28,13
	.byte	'pinDriver',0
	.word	54694
	.byte	1,2,35,29,0,29
	.byte	'IfxAsclin_Asc_Pins',0,34,199,2,3
	.word	102712
	.byte	12,34,205,2,9,1,13
	.byte	'ALL',0
	.word	644
	.byte	1,2,35,0,13
	.byte	'flags',0
	.word	101852
	.byte	1,2,35,0,0,29
	.byte	'IfxAsclin_Asc_ErrorFlagsUnion',0,34,209,2,3
	.word	102882
	.byte	29
	.byte	'uart_index_enum',0,21,109,2
	.word	25924
	.byte	15,128,2
	.word	661
	.byte	16,127,0
.L71:
	.byte	15,128,4
	.word	102980
	.byte	16,1,0
.L72:
	.byte	14
	.word	644
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L39:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,21,0,54,15,39,12,0,0,3,15,0,73,19,0,0,4,46,1,3,8,32,13
	.byte	58,15,59,15,57,15,54,15,39,12,0,0,5,5,0,3,8,58,15,59,15,57,15,73,19,0,0,6,11,0,0,0,7,36,0,3,8,11,15,62
	.byte	15,0,0,8,46,1,3,8,32,13,58,15,59,15,57,15,73,19,54,15,39,12,0,0,9,59,0,3,8,0,0,10,19,1,3,8,58,15,59,15
	.byte	57,15,11,15,0,0,11,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,12,23,1,58,15,59,15,57,15,11,15,0,0,13,13
	.byte	0,3,8,73,19,11,15,56,9,0,0,14,53,0,73,19,0,0,15,1,1,11,15,73,19,0,0,16,33,0,47,15,0,0,17,4,1,58,15,59
	.byte	15,57,15,11,15,0,0,18,40,0,3,8,28,13,0,0,19,11,1,0,0,20,19,1,58,15,59,15,57,15,11,15,0,0,21,46,1,3,8,58
	.byte	15,59,15,57,15,73,19,54,15,39,12,63,12,60,12,0,0,22,46,1,3,8,58,15,59,15,57,15,54,15,39,12,63,12,60,12
	.byte	0,0,23,46,1,49,19,0,0,24,5,0,49,19,0,0,25,29,1,49,19,0,0,26,11,0,49,19,0,0,27,11,1,49,19,0,0,28,38,0,73
	.byte	19,0,0,29,22,0,3,8,58,15,59,15,57,15,73,19,0,0,30,21,0,54,15,0,0,31,21,1,73,19,54,15,39,12,0,0,32,5,0
	.byte	73,19,0,0,33,21,1,54,15,39,12,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L40:
	.word	.L103-.L102
.L102:
	.half	3
	.word	.L105-.L104
.L104:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver',0
	.byte	'F:\\ADS\\AURIX-Studio-1.10.2\\tools\\Compilers\\Tasking_1.1r8\\ctc\\include\\',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc',0
	.byte	0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_Intrinsics.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Scu\\Std\\IfxScuWdt.h',0
	.byte	0,0,0
	.byte	'IfxScu_regdef.h',0,1,0,0
	.byte	'IFXPORT.h',0,2,0,0
	.byte	'IfxPort_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxCpu_cfg.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Scu\\Std\\IfxScuCcu.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Src\\Std\\IfxSrc.h',0
	.byte	0,0,0
	.byte	'IfxSrc_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Asclin\\Std\\IfxAsclin.h',0
	.byte	0,0,0
	.byte	'IfxAsclin_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Lib\\DataHandling\\Ifx_Fifo.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Stm\\Std\\IfxStm.h',0
	.byte	0,0,0
	.byte	'IfxStm_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\SysSe\\Bsp\\Bsp.h',0
	.byte	0,0,0
	.byte	'zf_driver_adc.h',0,3,0,0
	.byte	'zf_driver_gpio.h',0,3,0,0
	.byte	'zf_driver_pit.h',0,3,0,0
	.byte	'zf_driver_uart.h',0,3,0,0
	.byte	'../libraries/zf_device/zf_device_tsl1401.c',0,0,0,0
	.byte	'stdio.h',0,4,0,0
	.byte	'Platform_Types.h',0,5,0,0
	.byte	'ifx_types.h',0,5,0,0
	.byte	'zf_common_typedef.h',0,6,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxScu_cfg.h',0
	.byte	0,0,0
	.byte	'IfxCpu_regdef.h',0,1,0,0
	.byte	'IfxFlash_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxSrc_cfg.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_PinMap\\IfxAsclin_PinMap.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\StdIf\\IfxStdIf.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\StdIf\\IfxStdIf_DPipe.h',0
	.byte	0,0,0
	.byte	'ifxAsclin_Asc.h',0,7,0,0,0
.L105:
.L103:
	.sdecl	'.debug_info',debug,cluster('tsl1401_collect_pit_handler')
	.sect	'.debug_info'
.L41:
	.word	293
	.half	3
	.word	.L42
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_tsl1401.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L44,.L43
	.byte	2
	.word	.L37
	.byte	3
	.byte	'tsl1401_collect_pit_handler',0,1,67,6,1,1,1
	.word	.L32,.L62,.L31
	.byte	4
	.word	.L32,.L62
	.byte	4
	.word	.L2,.L62
	.byte	5
	.byte	'i',0,1,71,11
	.word	.L63,.L64
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('tsl1401_collect_pit_handler')
	.sect	'.debug_abbrev'
.L42:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('tsl1401_collect_pit_handler')
	.sect	'.debug_line'
.L43:
	.word	.L107-.L106
.L106:
	.half	3
	.word	.L109-.L108
.L108:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_tsl1401.c',0,0,0,0,0
.L109:
	.byte	5,9,7,0,5,2
	.word	.L32
	.byte	3,196,0,1,5,29,9
	.half	.L110-.L32
	.byte	1,5,5,9
	.half	.L2-.L110
	.byte	3,4,1,9
	.half	.L5-.L2
	.byte	3,1,1,9
	.half	.L7-.L5
	.byte	3,1,1,9
	.half	.L9-.L7
	.byte	3,1,1,9
	.half	.L11-.L9
	.byte	3,1,1,9
	.half	.L13-.L11
	.byte	3,1,1,5,11,9
	.half	.L15-.L13
	.byte	3,2,1,5,36,9
	.half	.L73-.L15
	.byte	1,5,9,9
	.half	.L17-.L73
	.byte	3,2,1,5,24,9
	.half	.L19-.L17
	.byte	3,1,1,5,9,9
	.half	.L111-.L19
	.byte	1,5,24,9
	.half	.L112-.L111
	.byte	1,5,42,9
	.half	.L113-.L112
	.byte	1,5,28,9
	.half	.L114-.L113
	.byte	1,5,24,9
	.half	.L115-.L114
	.byte	3,1,1,5,9,9
	.half	.L116-.L115
	.byte	1,5,24,9
	.half	.L117-.L116
	.byte	1,5,42,9
	.half	.L118-.L117
	.byte	1,5,28,9
	.half	.L119-.L118
	.byte	1,5,9,9
	.half	.L120-.L119
	.byte	3,1,1,5,40,9
	.half	.L21-.L120
	.byte	3,123,1,5,20,9
	.half	.L16-.L21
	.byte	1,5,36,9
	.half	.L121-.L16
	.byte	1,5,5,7,9
	.half	.L122-.L121
	.byte	3,8,1,5,27,9
	.half	.L123-.L122
	.byte	1,5,25,9
	.half	.L124-.L123
	.byte	1,5,1,9
	.half	.L3-.L124
	.byte	3,1,1,7,9
	.half	.L45-.L3
	.byte	0,1,1
.L107:
	.sdecl	'.debug_ranges',debug,cluster('tsl1401_collect_pit_handler')
	.sect	'.debug_ranges'
.L44:
	.word	-1,.L32,0,.L45-.L32,0,0
	.sdecl	'.debug_info',debug,cluster('tsl1401_send_data')
	.sect	'.debug_info'
.L46:
	.word	310
	.half	3
	.word	.L47
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_tsl1401.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L49,.L48
	.byte	2
	.word	.L37
	.byte	3
	.byte	'tsl1401_send_data',0,1,99,6,1,1,1
	.word	.L34,.L65,.L33
	.byte	4
	.byte	'uart_n',0,1,99,41
	.word	.L66,.L67
	.byte	4
	.byte	'index',0,1,99,55
	.word	.L63,.L68
	.byte	5
	.word	.L34,.L65
	.byte	6
	.byte	'i',0,1,101,11
	.word	.L63,.L69
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('tsl1401_send_data')
	.sect	'.debug_abbrev'
.L47:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('tsl1401_send_data')
	.sect	'.debug_line'
.L48:
	.word	.L126-.L125
.L125:
	.half	3
	.word	.L128-.L127
.L127:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_tsl1401.c',0,0,0,0,0
.L128:
	.byte	5,6,7,0,5,2
	.word	.L34
	.byte	3,226,0,1,5,29,9
	.half	.L129-.L34
	.byte	3,3,1,9
	.half	.L75-.L129
	.byte	3,1,1,9
	.half	.L79-.L75
	.byte	3,1,1,9
	.half	.L82-.L79
	.byte	3,1,1,5,10,9
	.half	.L85-.L82
	.byte	3,2,1,5,32,9
	.half	.L86-.L85
	.byte	1,5,16,9
	.half	.L23-.L86
	.byte	3,2,1,5,18,9
	.half	.L130-.L23
	.byte	3,2,1,9
	.half	.L131-.L130
	.byte	3,1,1,9
	.half	.L132-.L131
	.byte	3,1,1,5,80,9
	.half	.L24-.L132
	.byte	3,126,1,5,73,9
	.half	.L133-.L24
	.byte	1,5,61,9
	.half	.L88-.L133
	.byte	1,5,73,9
	.half	.L134-.L88
	.byte	1,5,80,9
	.half	.L135-.L134
	.byte	1,5,53,9
	.half	.L136-.L135
	.byte	1,5,92,9
	.half	.L91-.L136
	.byte	1,5,80,9
	.half	.L25-.L91
	.byte	3,1,1,5,73,9
	.half	.L137-.L25
	.byte	1,5,61,9
	.half	.L93-.L137
	.byte	1,5,73,9
	.half	.L138-.L93
	.byte	1,5,80,9
	.half	.L139-.L138
	.byte	1,5,84,9
	.half	.L140-.L139
	.byte	1,5,53,9
	.half	.L141-.L140
	.byte	1,5,92,9
	.half	.L96-.L141
	.byte	1,5,80,9
	.half	.L26-.L96
	.byte	3,1,1,5,73,9
	.half	.L142-.L26
	.byte	1,5,61,9
	.half	.L98-.L142
	.byte	1,5,73,9
	.half	.L143-.L98
	.byte	1,5,80,9
	.half	.L144-.L143
	.byte	1,5,84,9
	.half	.L145-.L144
	.byte	1,5,53,9
	.half	.L146-.L145
	.byte	1,5,92,9
	.half	.L101-.L146
	.byte	1,5,35,9
	.half	.L28-.L101
	.byte	3,122,1,5,16,9
	.half	.L22-.L28
	.byte	1,5,32,9
	.half	.L147-.L22
	.byte	1,5,1,7,9
	.half	.L148-.L147
	.byte	3,9,1,7,9
	.half	.L50-.L148
	.byte	0,1,1
.L126:
	.sdecl	'.debug_ranges',debug,cluster('tsl1401_send_data')
	.sect	'.debug_ranges'
.L49:
	.word	-1,.L34,0,.L50-.L34,0,0
	.sdecl	'.debug_info',debug,cluster('tsl1401_init')
	.sect	'.debug_info'
.L51:
	.word	253
	.half	3
	.word	.L52
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_tsl1401.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L54,.L53
	.byte	2
	.word	.L37
	.byte	3
	.byte	'tsl1401_init',0,1,125,6,1,1,1
	.word	.L36,.L70,.L35
	.byte	4
	.word	.L36,.L70
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('tsl1401_init')
	.sect	'.debug_abbrev'
.L52:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('tsl1401_init')
	.sect	'.debug_line'
.L53:
	.word	.L150-.L149
.L149:
	.half	3
	.word	.L152-.L151
.L151:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_tsl1401.c',0,0,0,0,0
.L152:
	.byte	5,14,7,0,5,2
	.word	.L36
	.byte	3,254,0,1,5,30,9
	.half	.L153-.L36
	.byte	1,5,14,9
	.half	.L154-.L153
	.byte	3,1,1,5,31,9
	.half	.L155-.L154
	.byte	1,5,15,9
	.half	.L156-.L155
	.byte	3,1,1,5,32,9
	.half	.L157-.L156
	.byte	1,5,37,9
	.half	.L158-.L157
	.byte	1,5,47,9
	.half	.L159-.L158
	.byte	1,5,15,9
	.half	.L160-.L159
	.byte	3,1,1,5,31,9
	.half	.L161-.L160
	.byte	1,5,36,9
	.half	.L162-.L161
	.byte	1,5,46,9
	.half	.L163-.L162
	.byte	1,5,5,9
	.half	.L164-.L163
	.byte	3,1,1,9
	.half	.L165-.L164
	.byte	3,1,1,5,26,9
	.half	.L166-.L165
	.byte	1,5,24,9
	.half	.L167-.L166
	.byte	1,5,1,9
	.half	.L168-.L167
	.byte	3,1,1,7,9
	.half	.L55-.L168
	.byte	0,1,1
.L150:
	.sdecl	'.debug_ranges',debug,cluster('tsl1401_init')
	.sect	'.debug_ranges'
.L54:
	.word	-1,.L36,0,.L55-.L36,0,0
	.sdecl	'.debug_info',debug,cluster('tsl1401_data')
	.sect	'.debug_info'
.L56:
	.word	231
	.half	3
	.word	.L57
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_tsl1401.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L37
	.byte	3
	.byte	'tsl1401_data',0,22,55,8
	.word	.L71
	.byte	1,5,3
	.word	tsl1401_data
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('tsl1401_data')
	.sect	'.debug_abbrev'
.L57:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('tsl1401_finish_flag')
	.sect	'.debug_info'
.L58:
	.word	238
	.half	3
	.word	.L59
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_tsl1401.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L37
	.byte	3
	.byte	'tsl1401_finish_flag',0,22,58,8
	.word	.L72
	.byte	1,5,3
	.word	tsl1401_finish_flag
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('tsl1401_finish_flag')
	.sect	'.debug_abbrev'
.L59:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('tsl1401_init_state')
	.sect	'.debug_info'
.L60:
	.word	236
	.half	3
	.word	.L61
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_tsl1401.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L37
	.byte	3
	.byte	'tsl1401_init_state',0,22,57,14
	.word	.L63
	.byte	5,3
	.word	tsl1401_init_state
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('tsl1401_init_state')
	.sect	'.debug_abbrev'
.L61:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_loc',debug,cluster('tsl1401_collect_pit_handler')
	.sect	'.debug_loc'
.L64:
	.word	-1,.L32,.L73-.L32,.L3-.L32
	.half	1
	.byte	88
	.word	0,0
.L31:
	.word	-1,.L32,0,.L62-.L32
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('tsl1401_init')
	.sect	'.debug_loc'
.L35:
	.word	-1,.L36,0,.L70-.L36
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('tsl1401_send_data')
	.sect	'.debug_loc'
.L69:
	.word	-1,.L34,.L86-.L34,.L65-.L34
	.half	1
	.byte	90
	.word	0,0
.L68:
	.word	-1,.L34,0,.L74-.L34
	.half	1
	.byte	85
	.word	.L87-.L34,.L88-.L34
	.half	1
	.byte	89
	.word	.L92-.L34,.L93-.L34
	.half	1
	.byte	89
	.word	.L97-.L34,.L98-.L34
	.half	1
	.byte	89
	.word	0,0
.L33:
	.word	-1,.L34,0,.L65-.L34
	.half	2
	.byte	138,0
	.word	0,0
.L67:
	.word	-1,.L34,0,.L75-.L34
	.half	1
	.byte	84
	.word	.L74-.L34,.L76-.L34
	.half	1
	.byte	88
	.word	.L77-.L34,.L78-.L34
	.half	1
	.byte	88
	.word	.L78-.L34,.L79-.L34
	.half	1
	.byte	84
	.word	.L80-.L34,.L81-.L34
	.half	1
	.byte	88
	.word	.L81-.L34,.L82-.L34
	.half	1
	.byte	84
	.word	.L83-.L34,.L84-.L34
	.half	1
	.byte	88
	.word	.L84-.L34,.L85-.L34
	.half	1
	.byte	84
	.word	.L89-.L34,.L90-.L34
	.half	1
	.byte	88
	.word	.L90-.L34,.L91-.L34
	.half	1
	.byte	84
	.word	.L94-.L34,.L95-.L34
	.half	1
	.byte	88
	.word	.L95-.L34,.L96-.L34
	.half	1
	.byte	84
	.word	.L99-.L34,.L100-.L34
	.half	1
	.byte	88
	.word	.L100-.L34,.L101-.L34
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L169:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('tsl1401_collect_pit_handler')
	.sect	'.debug_frame'
	.word	12
	.word	.L169,.L32,.L62-.L32
	.sdecl	'.debug_frame',debug,cluster('tsl1401_send_data')
	.sect	'.debug_frame'
	.word	12
	.word	.L169,.L34,.L65-.L34
	.sdecl	'.debug_frame',debug,cluster('tsl1401_init')
	.sect	'.debug_frame'
	.word	12
	.word	.L169,.L36,.L70-.L36
	; Module end
