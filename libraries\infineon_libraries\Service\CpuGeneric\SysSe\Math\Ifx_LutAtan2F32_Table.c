/**
 * \file Ifx_LutAtan2F32_Table.c
 * \brief Table data for ATAN2 lookup functions
 *
 *
 * \version disabled
 * \copyright Copyright (c) 2013 Infineon Technologies AG. All rights reserved.
 *
 *
 *                                 IMPORTANT NOTICE
 *
 *
 * Use of this file is subject to the terms of use agreed between (i) you or 
 * the company in which ordinary course of business you are acting and (ii) 
 * Infineon Technologies AG or its licensees. If and as long as no such 
 * terms of use are agreed, use of this file is subject to following:


 * Boost Software License - Version 1.0 - August 17th, 2003

 * Permission is hereby granted, free of charge, to any person or 
 * organization obtaining a copy of the software and accompanying 
 * documentation covered by this license (the "Software") to use, reproduce,
 * display, distribute, execute, and transmit the Software, and to prepare
 * derivative works of the Software, and to permit third-parties to whom the 
 * Software is furnished to do so, all subject to the following:

 * The copyright notices in the Software and this entire statement, including
 * the above license grant, this restriction and the following disclaimer, must
 * be included in all copies of the Software, in whole or in part, and all
 * derivative works of the Software, unless such copies or derivative works are
 * solely in the form of machine-executable object code generated by a source
 * language processor.

 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR 
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE, TITLE AND NON-INFRINGEMENT. IN NO EVENT
 * SHALL THE COPYRIGHT HOLDERS OR ANYONE DISTRIBUTING THE SOFTWARE BE LIABLE 
 * FOR ANY DAMAGES OR OTHER LIABILITY, WHETHER IN CONTRACT, TORT OR OTHERWISE,
 * ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
 * DEALINGS IN THE SOFTWARE.

 *
 */

#include "Ifx_LutAtan2F32.h"

#if IFX_LUT_TABLE_CONST == 0
/* FIXME Table size and type  not consistent  */
sint16 Ifx_g_LutAtan2F32_FxpAngle_table[IFX_LUTATAN2F32_SIZE];
#else

#if (IFX_LUTATAN2F32_SIZE != 1024)
#error "Inconsistent between Lookup Table and configuration. Please regenerate."
#endif

const Ifx_Lut_FxpAngle Ifx_g_LutAtan2F32_FxpAngle_table[1024 + 1] = {
    0,//IFX_LUT_F32_TO_FXPANGLE(0),
    IFX_LUT_F32_TO_FXPANGLE(0.000976562189559319),
    IFX_LUT_F32_TO_FXPANGLE(0.00195312251647882),
    IFX_LUT_F32_TO_FXPANGLE(0.00292967911813999),
    IFX_LUT_F32_TO_FXPANGLE(0.00390623013196697),
    IFX_LUT_F32_TO_FXPANGLE(0.00488277369544783),
    IFX_LUT_F32_TO_FXPANGLE(0.00585930794615589),
    IFX_LUT_F32_TO_FXPANGLE(0.00683583102177106),
    IFX_LUT_F32_TO_FXPANGLE(0.00781234106010111),
    IFX_LUT_F32_TO_FXPANGLE(0.008788836199103),
    IFX_LUT_F32_TO_FXPANGLE(0.00976531457690415),
    IFX_LUT_F32_TO_FXPANGLE(0.0107417743318238),
    IFX_LUT_F32_TO_FXPANGLE(0.0117182136023941),
    IFX_LUT_F32_TO_FXPANGLE(0.0126946305273818),
    IFX_LUT_F32_TO_FXPANGLE(0.0136710232458091),
    IFX_LUT_F32_TO_FXPANGLE(0.014647389896975),
    IFX_LUT_F32_TO_FXPANGLE(0.0156237286204768),
    IFX_LUT_F32_TO_FXPANGLE(0.0166000375562313),
    IFX_LUT_F32_TO_FXPANGLE(0.0175763148444956),
    IFX_LUT_F32_TO_FXPANGLE(0.018552558625889),
    IFX_LUT_F32_TO_FXPANGLE(0.0195287670414137),
    IFX_LUT_F32_TO_FXPANGLE(0.0205049382324764),
    IFX_LUT_F32_TO_FXPANGLE(0.0214810703409091),
    IFX_LUT_F32_TO_FXPANGLE(0.0224571615089906),
    IFX_LUT_F32_TO_FXPANGLE(0.0234332098794676),
    IFX_LUT_F32_TO_FXPANGLE(0.0244092135955758),
    IFX_LUT_F32_TO_FXPANGLE(0.0253851708010611),
    IFX_LUT_F32_TO_FXPANGLE(0.0263610796402008),
    IFX_LUT_F32_TO_FXPANGLE(0.0273369382578244),
    IFX_LUT_F32_TO_FXPANGLE(0.0283127447993352),
    IFX_LUT_F32_TO_FXPANGLE(0.029288497410731),
    IFX_LUT_F32_TO_FXPANGLE(0.0302641942386252),
    IFX_LUT_F32_TO_FXPANGLE(0.0312398334302683),
    IFX_LUT_F32_TO_FXPANGLE(0.0322154131335681),
    IFX_LUT_F32_TO_FXPANGLE(0.0331909314971116),
    IFX_LUT_F32_TO_FXPANGLE(0.0341663866701854),
    IFX_LUT_F32_TO_FXPANGLE(0.0351417768027968),
    IFX_LUT_F32_TO_FXPANGLE(0.036117100045695),
    IFX_LUT_F32_TO_FXPANGLE(0.0370923545503918),
    IFX_LUT_F32_TO_FXPANGLE(0.0380675384691825),
    IFX_LUT_F32_TO_FXPANGLE(0.039042649955167),
    IFX_LUT_F32_TO_FXPANGLE(0.0400176871622703),
    IFX_LUT_F32_TO_FXPANGLE(0.0409926482452638),
    IFX_LUT_F32_TO_FXPANGLE(0.0419675313597857),
    IFX_LUT_F32_TO_FXPANGLE(0.0429423346623622),
    IFX_LUT_F32_TO_FXPANGLE(0.0439170563104278),
    IFX_LUT_F32_TO_FXPANGLE(0.0448916944623465),
    IFX_LUT_F32_TO_FXPANGLE(0.0458662472774322),
    IFX_LUT_F32_TO_FXPANGLE(0.0468407129159697),
    IFX_LUT_F32_TO_FXPANGLE(0.0478150895392348),
    IFX_LUT_F32_TO_FXPANGLE(0.0487893753095156),
    IFX_LUT_F32_TO_FXPANGLE(0.0497635683901328),
    IFX_LUT_F32_TO_FXPANGLE(0.0507376669454602),
    IFX_LUT_F32_TO_FXPANGLE(0.0517116691409454),
    IFX_LUT_F32_TO_FXPANGLE(0.05268557314313),
    IFX_LUT_F32_TO_FXPANGLE(0.0536593771196708),
    IFX_LUT_F32_TO_FXPANGLE(0.0546330792393595),
    IFX_LUT_F32_TO_FXPANGLE(0.0556066776721433),
    IFX_LUT_F32_TO_FXPANGLE(0.0565801705891457),
    IFX_LUT_F32_TO_FXPANGLE(0.0575535561626864),
    IFX_LUT_F32_TO_FXPANGLE(0.0585268325663018),
    IFX_LUT_F32_TO_FXPANGLE(0.0594999979747652),
    IFX_LUT_F32_TO_FXPANGLE(0.0604730505641073),
    IFX_LUT_F32_TO_FXPANGLE(0.0614459885116361),
    IFX_LUT_F32_TO_FXPANGLE(0.0624188099959574),
    IFX_LUT_F32_TO_FXPANGLE(0.0633915131969944),
    IFX_LUT_F32_TO_FXPANGLE(0.0643640962960086),
    IFX_LUT_F32_TO_FXPANGLE(0.0653365574756192),
    IFX_LUT_F32_TO_FXPANGLE(0.0663088949198235),
    IFX_LUT_F32_TO_FXPANGLE(0.0672811068140167),
    IFX_LUT_F32_TO_FXPANGLE(0.068253191345012),
    IFX_LUT_F32_TO_FXPANGLE(0.0692251467010603),
    IFX_LUT_F32_TO_FXPANGLE(0.0701969710718705),
    IFX_LUT_F32_TO_FXPANGLE(0.0711686626486288),
    IFX_LUT_F32_TO_FXPANGLE(0.0721402196240188),
    IFX_LUT_F32_TO_FXPANGLE(0.0731116401922413),
    IFX_LUT_F32_TO_FXPANGLE(0.0740829225490337),
    IFX_LUT_F32_TO_FXPANGLE(0.0750540648916902),
    IFX_LUT_F32_TO_FXPANGLE(0.0760250654190807),
    IFX_LUT_F32_TO_FXPANGLE(0.0769959223316711),
    IFX_LUT_F32_TO_FXPANGLE(0.0779666338315423),
    IFX_LUT_F32_TO_FXPANGLE(0.07893719812241),
    IFX_LUT_F32_TO_FXPANGLE(0.0799076134096439),
    IFX_LUT_F32_TO_FXPANGLE(0.0808778779002873),
    IFX_LUT_F32_TO_FXPANGLE(0.0818479898030765),
    IFX_LUT_F32_TO_FXPANGLE(0.0828179473284599),
    IFX_LUT_F32_TO_FXPANGLE(0.0837877486886171),
    IFX_LUT_F32_TO_FXPANGLE(0.0847573920974787),
    IFX_LUT_F32_TO_FXPANGLE(0.0857268757707448),
    IFX_LUT_F32_TO_FXPANGLE(0.0866961979259047),
    IFX_LUT_F32_TO_FXPANGLE(0.0876653567822554),
    IFX_LUT_F32_TO_FXPANGLE(0.0886343505609211),
    IFX_LUT_F32_TO_FXPANGLE(0.0896031774848717),
    IFX_LUT_F32_TO_FXPANGLE(0.0905718357789422),
    IFX_LUT_F32_TO_FXPANGLE(0.0915403236698511),
    IFX_LUT_F32_TO_FXPANGLE(0.0925086393862194),
    IFX_LUT_F32_TO_FXPANGLE(0.0934767811585895),
    IFX_LUT_F32_TO_FXPANGLE(0.0944447472194436),
    IFX_LUT_F32_TO_FXPANGLE(0.0954125358032227),
    IFX_LUT_F32_TO_FXPANGLE(0.0963801451463448),
    IFX_LUT_F32_TO_FXPANGLE(0.0973475734872237),
    IFX_LUT_F32_TO_FXPANGLE(0.0983148190662873),
    IFX_LUT_F32_TO_FXPANGLE(0.0992818801259964),
    IFX_LUT_F32_TO_FXPANGLE(0.100248754910863),
    IFX_LUT_F32_TO_FXPANGLE(0.101215441667467),
    IFX_LUT_F32_TO_FXPANGLE(0.102181938644477),
    IFX_LUT_F32_TO_FXPANGLE(0.103148244092669),
    IFX_LUT_F32_TO_FXPANGLE(0.10411435626494),
    IFX_LUT_F32_TO_FXPANGLE(0.10508027341633),
    IFX_LUT_F32_TO_FXPANGLE(0.106045993804039),
    IFX_LUT_F32_TO_FXPANGLE(0.107011515687447),
    IFX_LUT_F32_TO_FXPANGLE(0.107976837328126),
    IFX_LUT_F32_TO_FXPANGLE(0.108941956989866),
    IFX_LUT_F32_TO_FXPANGLE(0.109906872938685),
    IFX_LUT_F32_TO_FXPANGLE(0.110871583442852),
    IFX_LUT_F32_TO_FXPANGLE(0.111836086772904),
    IFX_LUT_F32_TO_FXPANGLE(0.112800381201659),
    IFX_LUT_F32_TO_FXPANGLE(0.113764465004242),
    IFX_LUT_F32_TO_FXPANGLE(0.114728336458092),
    IFX_LUT_F32_TO_FXPANGLE(0.115691993842991),
    IFX_LUT_F32_TO_FXPANGLE(0.116655435441069),
    IFX_LUT_F32_TO_FXPANGLE(0.117618659536834),
    IFX_LUT_F32_TO_FXPANGLE(0.118581664417177),
    IFX_LUT_F32_TO_FXPANGLE(0.1195444483714),
    IFX_LUT_F32_TO_FXPANGLE(0.120507009691225),
    IFX_LUT_F32_TO_FXPANGLE(0.121469346670814),
    IFX_LUT_F32_TO_FXPANGLE(0.122431457606789),
    IFX_LUT_F32_TO_FXPANGLE(0.123393340798243),
    IFX_LUT_F32_TO_FXPANGLE(0.124354994546761),
    IFX_LUT_F32_TO_FXPANGLE(0.125316417156437),
    IFX_LUT_F32_TO_FXPANGLE(0.126277606933887),
    IFX_LUT_F32_TO_FXPANGLE(0.127238562188269),
    IFX_LUT_F32_TO_FXPANGLE(0.128199281231298),
    IFX_LUT_F32_TO_FXPANGLE(0.129159762377265),
    IFX_LUT_F32_TO_FXPANGLE(0.130120003943049),
    IFX_LUT_F32_TO_FXPANGLE(0.131080004248137),
    IFX_LUT_F32_TO_FXPANGLE(0.132039761614639),
    IFX_LUT_F32_TO_FXPANGLE(0.132999274367304),
    IFX_LUT_F32_TO_FXPANGLE(0.133958540833537),
    IFX_LUT_F32_TO_FXPANGLE(0.134917559343415),
    IFX_LUT_F32_TO_FXPANGLE(0.135876328229701),
    IFX_LUT_F32_TO_FXPANGLE(0.136834845827863),
    IFX_LUT_F32_TO_FXPANGLE(0.137793110476088),
    IFX_LUT_F32_TO_FXPANGLE(0.138751120515297),
    IFX_LUT_F32_TO_FXPANGLE(0.139708874289164),
    IFX_LUT_F32_TO_FXPANGLE(0.140666370144127),
    IFX_LUT_F32_TO_FXPANGLE(0.14162360642941),
    IFX_LUT_F32_TO_FXPANGLE(0.14258058149703),
    IFX_LUT_F32_TO_FXPANGLE(0.143537293701821),
    IFX_LUT_F32_TO_FXPANGLE(0.144493741401443),
    IFX_LUT_F32_TO_FXPANGLE(0.145449922956401),
    IFX_LUT_F32_TO_FXPANGLE(0.146405836730058),
    IFX_LUT_F32_TO_FXPANGLE(0.147361481088652),
    IFX_LUT_F32_TO_FXPANGLE(0.148316854401309),
    IFX_LUT_F32_TO_FXPANGLE(0.14927195504006),
    IFX_LUT_F32_TO_FXPANGLE(0.150226781379856),
    IFX_LUT_F32_TO_FXPANGLE(0.15118133179858),
    IFX_LUT_F32_TO_FXPANGLE(0.152135604677064),
    IFX_LUT_F32_TO_FXPANGLE(0.153089598399105),
    IFX_LUT_F32_TO_FXPANGLE(0.154043311351475),
    IFX_LUT_F32_TO_FXPANGLE(0.154996741923941),
    IFX_LUT_F32_TO_FXPANGLE(0.155949888509276),
    IFX_LUT_F32_TO_FXPANGLE(0.156902749503275),
    IFX_LUT_F32_TO_FXPANGLE(0.157855323304766),
    IFX_LUT_F32_TO_FXPANGLE(0.158807608315631),
    IFX_LUT_F32_TO_FXPANGLE(0.159759602940813),
    IFX_LUT_F32_TO_FXPANGLE(0.160711305588332),
    IFX_LUT_F32_TO_FXPANGLE(0.161662714669305),
    IFX_LUT_F32_TO_FXPANGLE(0.162613828597949),
    IFX_LUT_F32_TO_FXPANGLE(0.163564645791604),
    IFX_LUT_F32_TO_FXPANGLE(0.164515164670743),
    IFX_LUT_F32_TO_FXPANGLE(0.165465383658987),
    IFX_LUT_F32_TO_FXPANGLE(0.166415301183115),
    IFX_LUT_F32_TO_FXPANGLE(0.167364915673083),
    IFX_LUT_F32_TO_FXPANGLE(0.168314225562034),
    IFX_LUT_F32_TO_FXPANGLE(0.169263229286312),
    IFX_LUT_F32_TO_FXPANGLE(0.170211925285474),
    IFX_LUT_F32_TO_FXPANGLE(0.171160312002307),
    IFX_LUT_F32_TO_FXPANGLE(0.172108387882836),
    IFX_LUT_F32_TO_FXPANGLE(0.17305615137634),
    IFX_LUT_F32_TO_FXPANGLE(0.174003600935368),
    IFX_LUT_F32_TO_FXPANGLE(0.174950735015743),
    IFX_LUT_F32_TO_FXPANGLE(0.175897552076585),
    IFX_LUT_F32_TO_FXPANGLE(0.176844050580316),
    IFX_LUT_F32_TO_FXPANGLE(0.177790228992676),
    IFX_LUT_F32_TO_FXPANGLE(0.178736085782736),
    IFX_LUT_F32_TO_FXPANGLE(0.179681619422909),
    IFX_LUT_F32_TO_FXPANGLE(0.180626828388963),
    IFX_LUT_F32_TO_FXPANGLE(0.181571711160032),
    IFX_LUT_F32_TO_FXPANGLE(0.182516266218631),
    IFX_LUT_F32_TO_FXPANGLE(0.183460492050666),
    IFX_LUT_F32_TO_FXPANGLE(0.184404387145446),
    IFX_LUT_F32_TO_FXPANGLE(0.185347949995695),
    IFX_LUT_F32_TO_FXPANGLE(0.186291179097566),
    IFX_LUT_F32_TO_FXPANGLE(0.187234072950649),
    IFX_LUT_F32_TO_FXPANGLE(0.188176630057987),
    IFX_LUT_F32_TO_FXPANGLE(0.189118848926084),
    IFX_LUT_F32_TO_FXPANGLE(0.190060728064918),
    IFX_LUT_F32_TO_FXPANGLE(0.191002265987953),
    IFX_LUT_F32_TO_FXPANGLE(0.191943461212149),
    IFX_LUT_F32_TO_FXPANGLE(0.192884312257975),
    IFX_LUT_F32_TO_FXPANGLE(0.193824817649417),
    IFX_LUT_F32_TO_FXPANGLE(0.194764975913995),
    IFX_LUT_F32_TO_FXPANGLE(0.195704785582767),
    IFX_LUT_F32_TO_FXPANGLE(0.196644245190345),
    IFX_LUT_F32_TO_FXPANGLE(0.197583353274903),
    IFX_LUT_F32_TO_FXPANGLE(0.19852210837819),
    IFX_LUT_F32_TO_FXPANGLE(0.199460509045539),
    IFX_LUT_F32_TO_FXPANGLE(0.200398553825879),
    IFX_LUT_F32_TO_FXPANGLE(0.201336241271742),
    IFX_LUT_F32_TO_FXPANGLE(0.202273569939279),
    IFX_LUT_F32_TO_FXPANGLE(0.203210538388266),
    IFX_LUT_F32_TO_FXPANGLE(0.204147145182117),
    IFX_LUT_F32_TO_FXPANGLE(0.205083388887891),
    IFX_LUT_F32_TO_FXPANGLE(0.206019268076305),
    IFX_LUT_F32_TO_FXPANGLE(0.206954781321742),
    IFX_LUT_F32_TO_FXPANGLE(0.207889927202263),
    IFX_LUT_F32_TO_FXPANGLE(0.208824704299615),
    IFX_LUT_F32_TO_FXPANGLE(0.209759111199241),
    IFX_LUT_F32_TO_FXPANGLE(0.210693146490291),
    IFX_LUT_F32_TO_FXPANGLE(0.21162680876563),
    IFX_LUT_F32_TO_FXPANGLE(0.212560096621847),
    IFX_LUT_F32_TO_FXPANGLE(0.213493008659266),
    IFX_LUT_F32_TO_FXPANGLE(0.214425543481956),
    IFX_LUT_F32_TO_FXPANGLE(0.215357699697738),
    IFX_LUT_F32_TO_FXPANGLE(0.216289475918194),
    IFX_LUT_F32_TO_FXPANGLE(0.217220870758679),
    IFX_LUT_F32_TO_FXPANGLE(0.218151882838326),
    IFX_LUT_F32_TO_FXPANGLE(0.219082510780058),
    IFX_LUT_F32_TO_FXPANGLE(0.220012753210596),
    IFX_LUT_F32_TO_FXPANGLE(0.220942608760466),
    IFX_LUT_F32_TO_FXPANGLE(0.22187207606401),
    IFX_LUT_F32_TO_FXPANGLE(0.222801153759395),
    IFX_LUT_F32_TO_FXPANGLE(0.223729840488615),
    IFX_LUT_F32_TO_FXPANGLE(0.22465813489751),
    IFX_LUT_F32_TO_FXPANGLE(0.225586035635764),
    IFX_LUT_F32_TO_FXPANGLE(0.22651354135692),
    IFX_LUT_F32_TO_FXPANGLE(0.227440650718385),
    IFX_LUT_F32_TO_FXPANGLE(0.228367362381439),
    IFX_LUT_F32_TO_FXPANGLE(0.229293675011242),
    IFX_LUT_F32_TO_FXPANGLE(0.230219587276844),
    IFX_LUT_F32_TO_FXPANGLE(0.231145097851188),
    IFX_LUT_F32_TO_FXPANGLE(0.232070205411125),
    IFX_LUT_F32_TO_FXPANGLE(0.232994908637414),
    IFX_LUT_F32_TO_FXPANGLE(0.233919206214733),
    IFX_LUT_F32_TO_FXPANGLE(0.234843096831689),
    IFX_LUT_F32_TO_FXPANGLE(0.23576657918082),
    IFX_LUT_F32_TO_FXPANGLE(0.236689651958605),
    IFX_LUT_F32_TO_FXPANGLE(0.237612313865471),
    IFX_LUT_F32_TO_FXPANGLE(0.238534563605801),
    IFX_LUT_F32_TO_FXPANGLE(0.239456399887938),
    IFX_LUT_F32_TO_FXPANGLE(0.240377821424194),
    IFX_LUT_F32_TO_FXPANGLE(0.241298826930859),
    IFX_LUT_F32_TO_FXPANGLE(0.242219415128201),
    IFX_LUT_F32_TO_FXPANGLE(0.24313958474048),
    IFX_LUT_F32_TO_FXPANGLE(0.244059334495949),
    IFX_LUT_F32_TO_FXPANGLE(0.244978663126864),
    IFX_LUT_F32_TO_FXPANGLE(0.24589756936949),
    IFX_LUT_F32_TO_FXPANGLE(0.246816051964103),
    IFX_LUT_F32_TO_FXPANGLE(0.247734109655002),
    IFX_LUT_F32_TO_FXPANGLE(0.248651741190513),
    IFX_LUT_F32_TO_FXPANGLE(0.249568945322993),
    IFX_LUT_F32_TO_FXPANGLE(0.250485720808836),
    IFX_LUT_F32_TO_FXPANGLE(0.251402066408485),
    IFX_LUT_F32_TO_FXPANGLE(0.252317980886427),
    IFX_LUT_F32_TO_FXPANGLE(0.25323346301121),
    IFX_LUT_F32_TO_FXPANGLE(0.254148511555439),
    IFX_LUT_F32_TO_FXPANGLE(0.25506312529579),
    IFX_LUT_F32_TO_FXPANGLE(0.255977303013006),
    IFX_LUT_F32_TO_FXPANGLE(0.25689104349191),
    IFX_LUT_F32_TO_FXPANGLE(0.25780434552141),
    IFX_LUT_F32_TO_FXPANGLE(0.258717207894497),
    IFX_LUT_F32_TO_FXPANGLE(0.259629629408258),
    IFX_LUT_F32_TO_FXPANGLE(0.260541608863876),
    IFX_LUT_F32_TO_FXPANGLE(0.261453145066638),
    IFX_LUT_F32_TO_FXPANGLE(0.262364236825938),
    IFX_LUT_F32_TO_FXPANGLE(0.263274882955282),
    IFX_LUT_F32_TO_FXPANGLE(0.264185082272293),
    IFX_LUT_F32_TO_FXPANGLE(0.265094833598715),
    IFX_LUT_F32_TO_FXPANGLE(0.266004135760417),
    IFX_LUT_F32_TO_FXPANGLE(0.2669129875874),
    IFX_LUT_F32_TO_FXPANGLE(0.267821387913799),
    IFX_LUT_F32_TO_FXPANGLE(0.268729335577886),
    IFX_LUT_F32_TO_FXPANGLE(0.269636829422078),
    IFX_LUT_F32_TO_FXPANGLE(0.270543868292937),
    IFX_LUT_F32_TO_FXPANGLE(0.271450451041176),
    IFX_LUT_F32_TO_FXPANGLE(0.272356576521665),
    IFX_LUT_F32_TO_FXPANGLE(0.27326224359343),
    IFX_LUT_F32_TO_FXPANGLE(0.274167451119659),
    IFX_LUT_F32_TO_FXPANGLE(0.275072197967707),
    IFX_LUT_F32_TO_FXPANGLE(0.275976483009098),
    IFX_LUT_F32_TO_FXPANGLE(0.27688030511953),
    IFX_LUT_F32_TO_FXPANGLE(0.277783663178873),
    IFX_LUT_F32_TO_FXPANGLE(0.278686556071182),
    IFX_LUT_F32_TO_FXPANGLE(0.27958898268469),
    IFX_LUT_F32_TO_FXPANGLE(0.280490941911819),
    IFX_LUT_F32_TO_FXPANGLE(0.281392432649178),
    IFX_LUT_F32_TO_FXPANGLE(0.282293453797569),
    IFX_LUT_F32_TO_FXPANGLE(0.283194004261987),
    IFX_LUT_F32_TO_FXPANGLE(0.284094082951627),
    IFX_LUT_F32_TO_FXPANGLE(0.284993688779881),
    IFX_LUT_F32_TO_FXPANGLE(0.285892820664347),
    IFX_LUT_F32_TO_FXPANGLE(0.286791477526827),
    IFX_LUT_F32_TO_FXPANGLE(0.28768965829333),
    IFX_LUT_F32_TO_FXPANGLE(0.288587361894077),
    IFX_LUT_F32_TO_FXPANGLE(0.289484587263501),
    IFX_LUT_F32_TO_FXPANGLE(0.29038133334025),
    IFX_LUT_F32_TO_FXPANGLE(0.291277599067188),
    IFX_LUT_F32_TO_FXPANGLE(0.292173383391399),
    IFX_LUT_F32_TO_FXPANGLE(0.293068685264188),
    IFX_LUT_F32_TO_FXPANGLE(0.293963503641084),
    IFX_LUT_F32_TO_FXPANGLE(0.294857837481838),
    IFX_LUT_F32_TO_FXPANGLE(0.295751685750432),
    IFX_LUT_F32_TO_FXPANGLE(0.296645047415071),
    IFX_LUT_F32_TO_FXPANGLE(0.297537921448196),
    IFX_LUT_F32_TO_FXPANGLE(0.298430306826474),
    IFX_LUT_F32_TO_FXPANGLE(0.299322202530807),
    IFX_LUT_F32_TO_FXPANGLE(0.300213607546333),
    IFX_LUT_F32_TO_FXPANGLE(0.301104520862424),
    IFX_LUT_F32_TO_FXPANGLE(0.301994941472688),
    IFX_LUT_F32_TO_FXPANGLE(0.302884868374971),
    IFX_LUT_F32_TO_FXPANGLE(0.303774300571361),
    IFX_LUT_F32_TO_FXPANGLE(0.304663237068183),
    IFX_LUT_F32_TO_FXPANGLE(0.305551676876003),
    IFX_LUT_F32_TO_FXPANGLE(0.30643961900963),
    IFX_LUT_F32_TO_FXPANGLE(0.307327062488116),
    IFX_LUT_F32_TO_FXPANGLE(0.308214006334753),
    IFX_LUT_F32_TO_FXPANGLE(0.309100449577082),
    IFX_LUT_F32_TO_FXPANGLE(0.309986391246883),
    IFX_LUT_F32_TO_FXPANGLE(0.310871830380185),
    IFX_LUT_F32_TO_FXPANGLE(0.31175676601726),
    IFX_LUT_F32_TO_FXPANGLE(0.312641197202625),
    IFX_LUT_F32_TO_FXPANGLE(0.313525122985044),
    IFX_LUT_F32_TO_FXPANGLE(0.314408542417527),
    IFX_LUT_F32_TO_FXPANGLE(0.31529145455733),
    IFX_LUT_F32_TO_FXPANGLE(0.316173858465954),
    IFX_LUT_F32_TO_FXPANGLE(0.317055753209147),
    IFX_LUT_F32_TO_FXPANGLE(0.317937137856902),
    IFX_LUT_F32_TO_FXPANGLE(0.318818011483458),
    IFX_LUT_F32_TO_FXPANGLE(0.3196983731673),
    IFX_LUT_F32_TO_FXPANGLE(0.320578221991157),
    IFX_LUT_F32_TO_FXPANGLE(0.321457557042003),
    IFX_LUT_F32_TO_FXPANGLE(0.322336377411056),
    IFX_LUT_F32_TO_FXPANGLE(0.323214682193777),
    IFX_LUT_F32_TO_FXPANGLE(0.324092470489872),
    IFX_LUT_F32_TO_FXPANGLE(0.324969741403286),
    IFX_LUT_F32_TO_FXPANGLE(0.325846494042208),
    IFX_LUT_F32_TO_FXPANGLE(0.326722727519067),
    IFX_LUT_F32_TO_FXPANGLE(0.327598440950531),
    IFX_LUT_F32_TO_FXPANGLE(0.328473633457506),
    IFX_LUT_F32_TO_FXPANGLE(0.329348304165139),
    IFX_LUT_F32_TO_FXPANGLE(0.330222452202808),
    IFX_LUT_F32_TO_FXPANGLE(0.331096076704132),
    IFX_LUT_F32_TO_FXPANGLE(0.33196917680696),
    IFX_LUT_F32_TO_FXPANGLE(0.332841751653376),
    IFX_LUT_F32_TO_FXPANGLE(0.333713800389694),
    IFX_LUT_F32_TO_FXPANGLE(0.334585322166459),
    IFX_LUT_F32_TO_FXPANGLE(0.335456316138443),
    IFX_LUT_F32_TO_FXPANGLE(0.336326781464645),
    IFX_LUT_F32_TO_FXPANGLE(0.33719671730829),
    IFX_LUT_F32_TO_FXPANGLE(0.338066122836825),
    IFX_LUT_F32_TO_FXPANGLE(0.338934997221921),
    IFX_LUT_F32_TO_FXPANGLE(0.339803339639465),
    IFX_LUT_F32_TO_FXPANGLE(0.340671149269565),
    IFX_LUT_F32_TO_FXPANGLE(0.341538425296542),
    IFX_LUT_F32_TO_FXPANGLE(0.342405166908932),
    IFX_LUT_F32_TO_FXPANGLE(0.343271373299484),
    IFX_LUT_F32_TO_FXPANGLE(0.344137043665154),
    IFX_LUT_F32_TO_FXPANGLE(0.345002177207105),
    IFX_LUT_F32_TO_FXPANGLE(0.345866773130707),
    IFX_LUT_F32_TO_FXPANGLE(0.34673083064553),
    IFX_LUT_F32_TO_FXPANGLE(0.347594348965346),
    IFX_LUT_F32_TO_FXPANGLE(0.348457327308122),
    IFX_LUT_F32_TO_FXPANGLE(0.349319764896022),
    IFX_LUT_F32_TO_FXPANGLE(0.350181660955402),
    IFX_LUT_F32_TO_FXPANGLE(0.351043014716805),
    IFX_LUT_F32_TO_FXPANGLE(0.351903825414965),
    IFX_LUT_F32_TO_FXPANGLE(0.352764092288795),
    IFX_LUT_F32_TO_FXPANGLE(0.353623814581394),
    IFX_LUT_F32_TO_FXPANGLE(0.354482991540035),
    IFX_LUT_F32_TO_FXPANGLE(0.355341622416168),
    IFX_LUT_F32_TO_FXPANGLE(0.356199706465416),
    IFX_LUT_F32_TO_FXPANGLE(0.357057242947567),
    IFX_LUT_F32_TO_FXPANGLE(0.35791423112658),
    IFX_LUT_F32_TO_FXPANGLE(0.358770670270572),
    IFX_LUT_F32_TO_FXPANGLE(0.359626559651822),
    IFX_LUT_F32_TO_FXPANGLE(0.360481898546763),
    IFX_LUT_F32_TO_FXPANGLE(0.361336686235982),
    IFX_LUT_F32_TO_FXPANGLE(0.362190922004212),
    IFX_LUT_F32_TO_FXPANGLE(0.363044605140335),
    IFX_LUT_F32_TO_FXPANGLE(0.363897734937373),
    IFX_LUT_F32_TO_FXPANGLE(0.364750310692485),
    IFX_LUT_F32_TO_FXPANGLE(0.365602331706967),
    IFX_LUT_F32_TO_FXPANGLE(0.366453797286243),
    IFX_LUT_F32_TO_FXPANGLE(0.367304706739864),
    IFX_LUT_F32_TO_FXPANGLE(0.368155059381507),
    IFX_LUT_F32_TO_FXPANGLE(0.369004854528964),
    IFX_LUT_F32_TO_FXPANGLE(0.369854091504145),
    IFX_LUT_F32_TO_FXPANGLE(0.370702769633069),
    IFX_LUT_F32_TO_FXPANGLE(0.371550888245862),
    IFX_LUT_F32_TO_FXPANGLE(0.372398446676754),
    IFX_LUT_F32_TO_FXPANGLE(0.373245444264073),
    IFX_LUT_F32_TO_FXPANGLE(0.374091880350239),
    IFX_LUT_F32_TO_FXPANGLE(0.374937754281765),
    IFX_LUT_F32_TO_FXPANGLE(0.375783065409249),
    IFX_LUT_F32_TO_FXPANGLE(0.376627813087368),
    IFX_LUT_F32_TO_FXPANGLE(0.377471996674877),
    IFX_LUT_F32_TO_FXPANGLE(0.378315615534604),
    IFX_LUT_F32_TO_FXPANGLE(0.379158669033442),
    IFX_LUT_F32_TO_FXPANGLE(0.380001156542349),
    IFX_LUT_F32_TO_FXPANGLE(0.380843077436342),
    IFX_LUT_F32_TO_FXPANGLE(0.381684431094488),
    IFX_LUT_F32_TO_FXPANGLE(0.382525216899905),
    IFX_LUT_F32_TO_FXPANGLE(0.383365434239755),
    IFX_LUT_F32_TO_FXPANGLE(0.384205082505239),
    IFX_LUT_F32_TO_FXPANGLE(0.385044161091591),
    IFX_LUT_F32_TO_FXPANGLE(0.385882669398074),
    IFX_LUT_F32_TO_FXPANGLE(0.386720606827975),
    IFX_LUT_F32_TO_FXPANGLE(0.387557972788602),
    IFX_LUT_F32_TO_FXPANGLE(0.388394766691274),
    IFX_LUT_F32_TO_FXPANGLE(0.389230987951321),
    IFX_LUT_F32_TO_FXPANGLE(0.390066635988073),
    IFX_LUT_F32_TO_FXPANGLE(0.390901710224862),
    IFX_LUT_F32_TO_FXPANGLE(0.39173621008901),
    IFX_LUT_F32_TO_FXPANGLE(0.392570135011829),
    IFX_LUT_F32_TO_FXPANGLE(0.393403484428609),
    IFX_LUT_F32_TO_FXPANGLE(0.394236257778621),
    IFX_LUT_F32_TO_FXPANGLE(0.395068454505103),
    IFX_LUT_F32_TO_FXPANGLE(0.395900074055263),
    IFX_LUT_F32_TO_FXPANGLE(0.396731115880264),
    IFX_LUT_F32_TO_FXPANGLE(0.397561579435227),
    IFX_LUT_F32_TO_FXPANGLE(0.39839146417922),
    IFX_LUT_F32_TO_FXPANGLE(0.399220769575253),
    IFX_LUT_F32_TO_FXPANGLE(0.400049495090274),
    IFX_LUT_F32_TO_FXPANGLE(0.400877640195163),
    IFX_LUT_F32_TO_FXPANGLE(0.401705204364725),
    IFX_LUT_F32_TO_FXPANGLE(0.402532187077683),
    IFX_LUT_F32_TO_FXPANGLE(0.403358587816674),
    IFX_LUT_F32_TO_FXPANGLE(0.404184406068245),
    IFX_LUT_F32_TO_FXPANGLE(0.405009641322841),
    IFX_LUT_F32_TO_FXPANGLE(0.405834293074804),
    IFX_LUT_F32_TO_FXPANGLE(0.406658360822366),
    IFX_LUT_F32_TO_FXPANGLE(0.407481844067642),
    IFX_LUT_F32_TO_FXPANGLE(0.408304742316622),
    IFX_LUT_F32_TO_FXPANGLE(0.409127055079168),
    IFX_LUT_F32_TO_FXPANGLE(0.409948781869008),
    IFX_LUT_F32_TO_FXPANGLE(0.410769922203726),
    IFX_LUT_F32_TO_FXPANGLE(0.411590475604759),
    IFX_LUT_F32_TO_FXPANGLE(0.412410441597387),
    IFX_LUT_F32_TO_FXPANGLE(0.413229819710733),
    IFX_LUT_F32_TO_FXPANGLE(0.414048609477749),
    IFX_LUT_F32_TO_FXPANGLE(0.414866810435215),
    IFX_LUT_F32_TO_FXPANGLE(0.415684422123729),
    IFX_LUT_F32_TO_FXPANGLE(0.416501444087703),
    IFX_LUT_F32_TO_FXPANGLE(0.417317875875355),
    IFX_LUT_F32_TO_FXPANGLE(0.418133717038701),
    IFX_LUT_F32_TO_FXPANGLE(0.418948967133553),
    IFX_LUT_F32_TO_FXPANGLE(0.419763625719506),
    IFX_LUT_F32_TO_FXPANGLE(0.420577692359935),
    IFX_LUT_F32_TO_FXPANGLE(0.42139116662199),
    IFX_LUT_F32_TO_FXPANGLE(0.422204048076584),
    IFX_LUT_F32_TO_FXPANGLE(0.423016336298389),
    IFX_LUT_F32_TO_FXPANGLE(0.423828030865831),
    IFX_LUT_F32_TO_FXPANGLE(0.42463913136108),
    IFX_LUT_F32_TO_FXPANGLE(0.425449637370042),
    IFX_LUT_F32_TO_FXPANGLE(0.426259548482358),
    IFX_LUT_F32_TO_FXPANGLE(0.427068864291389),
    IFX_LUT_F32_TO_FXPANGLE(0.427877584394215),
    IFX_LUT_F32_TO_FXPANGLE(0.428685708391626),
    IFX_LUT_F32_TO_FXPANGLE(0.429493235888114),
    IFX_LUT_F32_TO_FXPANGLE(0.430300166491866),
    IFX_LUT_F32_TO_FXPANGLE(0.431106499814758),
    IFX_LUT_F32_TO_FXPANGLE(0.431912235472348),
    IFX_LUT_F32_TO_FXPANGLE(0.432717373083866),
    IFX_LUT_F32_TO_FXPANGLE(0.433521912272209),
    IFX_LUT_F32_TO_FXPANGLE(0.434325852663933),
    IFX_LUT_F32_TO_FXPANGLE(0.435129193889247),
    IFX_LUT_F32_TO_FXPANGLE(0.435931935582003),
    IFX_LUT_F32_TO_FXPANGLE(0.43673407737969),
    IFX_LUT_F32_TO_FXPANGLE(0.437535618923428),
    IFX_LUT_F32_TO_FXPANGLE(0.438336559857958),
    IFX_LUT_F32_TO_FXPANGLE(0.439136899831635),
    IFX_LUT_F32_TO_FXPANGLE(0.439936638496422),
    IFX_LUT_F32_TO_FXPANGLE(0.440735775507881),
    IFX_LUT_F32_TO_FXPANGLE(0.441534310525167),
    IFX_LUT_F32_TO_FXPANGLE(0.442332243211017),
    IFX_LUT_F32_TO_FXPANGLE(0.443129573231746),
    IFX_LUT_F32_TO_FXPANGLE(0.443926300257239),
    IFX_LUT_F32_TO_FXPANGLE(0.444722423960939),
    IFX_LUT_F32_TO_FXPANGLE(0.445517944019847),
    IFX_LUT_F32_TO_FXPANGLE(0.446312860114506),
    IFX_LUT_F32_TO_FXPANGLE(0.447107171928999),
    IFX_LUT_F32_TO_FXPANGLE(0.447900879150937),
    IFX_LUT_F32_TO_FXPANGLE(0.448693981471457),
    IFX_LUT_F32_TO_FXPANGLE(0.449486478585208),
    IFX_LUT_F32_TO_FXPANGLE(0.450278370190345),
    IFX_LUT_F32_TO_FXPANGLE(0.451069655988523),
    IFX_LUT_F32_TO_FXPANGLE(0.451860335684889),
    IFX_LUT_F32_TO_FXPANGLE(0.452650408988071),
    IFX_LUT_F32_TO_FXPANGLE(0.453439875610172),
    IFX_LUT_F32_TO_FXPANGLE(0.454228735266762),
    IFX_LUT_F32_TO_FXPANGLE(0.455016987676872),
    IFX_LUT_F32_TO_FXPANGLE(0.45580463256298),
    IFX_LUT_F32_TO_FXPANGLE(0.456591669651011),
    IFX_LUT_F32_TO_FXPANGLE(0.457378098670321),
    IFX_LUT_F32_TO_FXPANGLE(0.458163919353695),
    IFX_LUT_F32_TO_FXPANGLE(0.458949131437335),
    IFX_LUT_F32_TO_FXPANGLE(0.459733734660856),
    IFX_LUT_F32_TO_FXPANGLE(0.460517728767271),
    IFX_LUT_F32_TO_FXPANGLE(0.461301113502991),
    IFX_LUT_F32_TO_FXPANGLE(0.46208388861781),
    IFX_LUT_F32_TO_FXPANGLE(0.462866053864901),
    IFX_LUT_F32_TO_FXPANGLE(0.463647609000806),
    IFX_LUT_F32_TO_FXPANGLE(0.464428553785428),
    IFX_LUT_F32_TO_FXPANGLE(0.465208887982023),
    IFX_LUT_F32_TO_FXPANGLE(0.46598861135719),
    IFX_LUT_F32_TO_FXPANGLE(0.466767723680866),
    IFX_LUT_F32_TO_FXPANGLE(0.467546224726316),
    IFX_LUT_F32_TO_FXPANGLE(0.468324114270123),
    IFX_LUT_F32_TO_FXPANGLE(0.46910139209218),
    IFX_LUT_F32_TO_FXPANGLE(0.469878057975687),
    IFX_LUT_F32_TO_FXPANGLE(0.470654111707133),
    IFX_LUT_F32_TO_FXPANGLE(0.471429553076297),
    IFX_LUT_F32_TO_FXPANGLE(0.472204381876234),
    IFX_LUT_F32_TO_FXPANGLE(0.472978597903266),
    IFX_LUT_F32_TO_FXPANGLE(0.473752200956977),
    IFX_LUT_F32_TO_FXPANGLE(0.474525190840205),
    IFX_LUT_F32_TO_FXPANGLE(0.475297567359028),
    IFX_LUT_F32_TO_FXPANGLE(0.476069330322761),
    IFX_LUT_F32_TO_FXPANGLE(0.476840479543945),
    IFX_LUT_F32_TO_FXPANGLE(0.477611014838337),
    IFX_LUT_F32_TO_FXPANGLE(0.478380936024907),
    IFX_LUT_F32_TO_FXPANGLE(0.479150242925823),
    IFX_LUT_F32_TO_FXPANGLE(0.479918935366444),
    IFX_LUT_F32_TO_FXPANGLE(0.480687013175316),
    IFX_LUT_F32_TO_FXPANGLE(0.481454476184158),
    IFX_LUT_F32_TO_FXPANGLE(0.482221324227854),
    IFX_LUT_F32_TO_FXPANGLE(0.482987557144447),
    IFX_LUT_F32_TO_FXPANGLE(0.48375317477513),
    IFX_LUT_F32_TO_FXPANGLE(0.484518176964233),
    IFX_LUT_F32_TO_FXPANGLE(0.485282563559221),
    IFX_LUT_F32_TO_FXPANGLE(0.48604633441068),
    IFX_LUT_F32_TO_FXPANGLE(0.48680948937231),
    IFX_LUT_F32_TO_FXPANGLE(0.487572028300918),
    IFX_LUT_F32_TO_FXPANGLE(0.488333951056406),
    IFX_LUT_F32_TO_FXPANGLE(0.489095257501764),
    IFX_LUT_F32_TO_FXPANGLE(0.489855947503062),
    IFX_LUT_F32_TO_FXPANGLE(0.490616020929441),
    IFX_LUT_F32_TO_FXPANGLE(0.491375477653102),
    IFX_LUT_F32_TO_FXPANGLE(0.4921343175493),
    IFX_LUT_F32_TO_FXPANGLE(0.492892540496335),
    IFX_LUT_F32_TO_FXPANGLE(0.49365014637554),
    IFX_LUT_F32_TO_FXPANGLE(0.494407135071275),
    IFX_LUT_F32_TO_FXPANGLE(0.49516350647092),
    IFX_LUT_F32_TO_FXPANGLE(0.495919260464861),
    IFX_LUT_F32_TO_FXPANGLE(0.496674396946486),
    IFX_LUT_F32_TO_FXPANGLE(0.497428915812172),
    IFX_LUT_F32_TO_FXPANGLE(0.498182816961281),
    IFX_LUT_F32_TO_FXPANGLE(0.498936100296146),
    IFX_LUT_F32_TO_FXPANGLE(0.499688765722065),
    IFX_LUT_F32_TO_FXPANGLE(0.500440813147294),
    IFX_LUT_F32_TO_FXPANGLE(0.501192242483033),
    IFX_LUT_F32_TO_FXPANGLE(0.501943053643421),
    IFX_LUT_F32_TO_FXPANGLE(0.502693246545526),
    IFX_LUT_F32_TO_FXPANGLE(0.503442821109336),
    IFX_LUT_F32_TO_FXPANGLE(0.504191777257751),
    IFX_LUT_F32_TO_FXPANGLE(0.504940114916572),
    IFX_LUT_F32_TO_FXPANGLE(0.505687834014494),
    IFX_LUT_F32_TO_FXPANGLE(0.506434934483097),
    IFX_LUT_F32_TO_FXPANGLE(0.507181416256835),
    IFX_LUT_F32_TO_FXPANGLE(0.50792727927303),
    IFX_LUT_F32_TO_FXPANGLE(0.508672523471861),
    IFX_LUT_F32_TO_FXPANGLE(0.509417148796356),
    IFX_LUT_F32_TO_FXPANGLE(0.510161155192383),
    IFX_LUT_F32_TO_FXPANGLE(0.51090454260864),
    IFX_LUT_F32_TO_FXPANGLE(0.511647310996647),
    IFX_LUT_F32_TO_FXPANGLE(0.512389460310738),
    IFX_LUT_F32_TO_FXPANGLE(0.513130990508049),
    IFX_LUT_F32_TO_FXPANGLE(0.513871901548512),
    IFX_LUT_F32_TO_FXPANGLE(0.514612193394845),
    IFX_LUT_F32_TO_FXPANGLE(0.515351866012543),
    IFX_LUT_F32_TO_FXPANGLE(0.51609091936987),
    IFX_LUT_F32_TO_FXPANGLE(0.516829353437846),
    IFX_LUT_F32_TO_FXPANGLE(0.517567168190245),
    IFX_LUT_F32_TO_FXPANGLE(0.518304363603578),
    IFX_LUT_F32_TO_FXPANGLE(0.519040939657092),
    IFX_LUT_F32_TO_FXPANGLE(0.519776896332754),
    IFX_LUT_F32_TO_FXPANGLE(0.520512233615247),
    IFX_LUT_F32_TO_FXPANGLE(0.521246951491958),
    IFX_LUT_F32_TO_FXPANGLE(0.521981049952972),
    IFX_LUT_F32_TO_FXPANGLE(0.522714528991058),
    IFX_LUT_F32_TO_FXPANGLE(0.523447388601666),
    IFX_LUT_F32_TO_FXPANGLE(0.524179628782913),
    IFX_LUT_F32_TO_FXPANGLE(0.524911249535579),
    IFX_LUT_F32_TO_FXPANGLE(0.525642250863092),
    IFX_LUT_F32_TO_FXPANGLE(0.526372632771524),
    IFX_LUT_F32_TO_FXPANGLE(0.52710239526958),
    IFX_LUT_F32_TO_FXPANGLE(0.527831538368588),
    IFX_LUT_F32_TO_FXPANGLE(0.528560062082493),
    IFX_LUT_F32_TO_FXPANGLE(0.529287966427846),
    IFX_LUT_F32_TO_FXPANGLE(0.530015251423793),
    IFX_LUT_F32_TO_FXPANGLE(0.530741917092071),
    IFX_LUT_F32_TO_FXPANGLE(0.531467963456995),
    IFX_LUT_F32_TO_FXPANGLE(0.53219339054545),
    IFX_LUT_F32_TO_FXPANGLE(0.532918198386882),
    IFX_LUT_F32_TO_FXPANGLE(0.533642387013291),
    IFX_LUT_F32_TO_FXPANGLE(0.534365956459219),
    IFX_LUT_F32_TO_FXPANGLE(0.535088906761743),
    IFX_LUT_F32_TO_FXPANGLE(0.535811237960464),
    IFX_LUT_F32_TO_FXPANGLE(0.5365329500975),
    IFX_LUT_F32_TO_FXPANGLE(0.537254043217479),
    IFX_LUT_F32_TO_FXPANGLE(0.537974517367523),
    IFX_LUT_F32_TO_FXPANGLE(0.538694372597247),
    IFX_LUT_F32_TO_FXPANGLE(0.539413608958744),
    IFX_LUT_F32_TO_FXPANGLE(0.540132226506582),
    IFX_LUT_F32_TO_FXPANGLE(0.540850225297788),
    IFX_LUT_F32_TO_FXPANGLE(0.541567605391845),
    IFX_LUT_F32_TO_FXPANGLE(0.542284366850679),
    IFX_LUT_F32_TO_FXPANGLE(0.543000509738655),
    IFX_LUT_F32_TO_FXPANGLE(0.54371603412256),
    IFX_LUT_F32_TO_FXPANGLE(0.544430940071603),
    IFX_LUT_F32_TO_FXPANGLE(0.545145227657401),
    IFX_LUT_F32_TO_FXPANGLE(0.54585889695397),
    IFX_LUT_F32_TO_FXPANGLE(0.546571948037719),
    IFX_LUT_F32_TO_FXPANGLE(0.547284380987437),
    IFX_LUT_F32_TO_FXPANGLE(0.547996195884288),
    IFX_LUT_F32_TO_FXPANGLE(0.548707392811801),
    IFX_LUT_F32_TO_FXPANGLE(0.549417971855859),
    IFX_LUT_F32_TO_FXPANGLE(0.550127933104693),
    IFX_LUT_F32_TO_FXPANGLE(0.55083727664887),
    IFX_LUT_F32_TO_FXPANGLE(0.551546002581289),
    IFX_LUT_F32_TO_FXPANGLE(0.552254110997165),
    IFX_LUT_F32_TO_FXPANGLE(0.552961601994028),
    IFX_LUT_F32_TO_FXPANGLE(0.553668475671709),
    IFX_LUT_F32_TO_FXPANGLE(0.554374732132331),
    IFX_LUT_F32_TO_FXPANGLE(0.555080371480305),
    IFX_LUT_F32_TO_FXPANGLE(0.555785393822314),
    IFX_LUT_F32_TO_FXPANGLE(0.55648979926731),
    IFX_LUT_F32_TO_FXPANGLE(0.557193587926504),
    IFX_LUT_F32_TO_FXPANGLE(0.557896759913355),
    IFX_LUT_F32_TO_FXPANGLE(0.558599315343562),
    IFX_LUT_F32_TO_FXPANGLE(0.559301254335059),
    IFX_LUT_F32_TO_FXPANGLE(0.560002577007999),
    IFX_LUT_F32_TO_FXPANGLE(0.560703283484751),
    IFX_LUT_F32_TO_FXPANGLE(0.561403373889889),
    IFX_LUT_F32_TO_FXPANGLE(0.562102848350186),
    IFX_LUT_F32_TO_FXPANGLE(0.562801706994599),
    IFX_LUT_F32_TO_FXPANGLE(0.563499949954267),
    IFX_LUT_F32_TO_FXPANGLE(0.564197577362498),
    IFX_LUT_F32_TO_FXPANGLE(0.564894589354762),
    IFX_LUT_F32_TO_FXPANGLE(0.565590986068683),
    IFX_LUT_F32_TO_FXPANGLE(0.566286767644028),
    IFX_LUT_F32_TO_FXPANGLE(0.5669819342227),
    IFX_LUT_F32_TO_FXPANGLE(0.56767648594873),
    IFX_LUT_F32_TO_FXPANGLE(0.568370422968264),
    IFX_LUT_F32_TO_FXPANGLE(0.569063745429561),
    IFX_LUT_F32_TO_FXPANGLE(0.569756453482978),
    IFX_LUT_F32_TO_FXPANGLE(0.570448547280968),
    IFX_LUT_F32_TO_FXPANGLE(0.571140026978063),
    IFX_LUT_F32_TO_FXPANGLE(0.571830892730873),
    IFX_LUT_F32_TO_FXPANGLE(0.572521144698072),
    IFX_LUT_F32_TO_FXPANGLE(0.573210783040395),
    IFX_LUT_F32_TO_FXPANGLE(0.573899807920624),
    IFX_LUT_F32_TO_FXPANGLE(0.57458821950358),
    IFX_LUT_F32_TO_FXPANGLE(0.575276017956118),
    IFX_LUT_F32_TO_FXPANGLE(0.575963203447116),
    IFX_LUT_F32_TO_FXPANGLE(0.576649776147467),
    IFX_LUT_F32_TO_FXPANGLE(0.577335736230069),
    IFX_LUT_F32_TO_FXPANGLE(0.57802108386982),
    IFX_LUT_F32_TO_FXPANGLE(0.578705819243603),
    IFX_LUT_F32_TO_FXPANGLE(0.579389942530287),
    IFX_LUT_F32_TO_FXPANGLE(0.580073453910708),
    IFX_LUT_F32_TO_FXPANGLE(0.58075635356767),
    IFX_LUT_F32_TO_FXPANGLE(0.58143864168593),
    IFX_LUT_F32_TO_FXPANGLE(0.582120318452191),
    IFX_LUT_F32_TO_FXPANGLE(0.582801384055095),
    IFX_LUT_F32_TO_FXPANGLE(0.583481838685215),
    IFX_LUT_F32_TO_FXPANGLE(0.584161682535044),
    IFX_LUT_F32_TO_FXPANGLE(0.584840915798988),
    IFX_LUT_F32_TO_FXPANGLE(0.585519538673358),
    IFX_LUT_F32_TO_FXPANGLE(0.586197551356361),
    IFX_LUT_F32_TO_FXPANGLE(0.586874954048091),
    IFX_LUT_F32_TO_FXPANGLE(0.587551746950524),
    IFX_LUT_F32_TO_FXPANGLE(0.588227930267504),
    IFX_LUT_F32_TO_FXPANGLE(0.588903504204738),
    IFX_LUT_F32_TO_FXPANGLE(0.58957846896979),
    IFX_LUT_F32_TO_FXPANGLE(0.590252824772067),
    IFX_LUT_F32_TO_FXPANGLE(0.590926571822816),
    IFX_LUT_F32_TO_FXPANGLE(0.591599710335111),
    IFX_LUT_F32_TO_FXPANGLE(0.592272240523851),
    IFX_LUT_F32_TO_FXPANGLE(0.592944162605743),
    IFX_LUT_F32_TO_FXPANGLE(0.593615476799303),
    IFX_LUT_F32_TO_FXPANGLE(0.594286183324841),
    IFX_LUT_F32_TO_FXPANGLE(0.594956282404456),
    IFX_LUT_F32_TO_FXPANGLE(0.595625774262027),
    IFX_LUT_F32_TO_FXPANGLE(0.596294659123204),
    IFX_LUT_F32_TO_FXPANGLE(0.596962937215402),
    IFX_LUT_F32_TO_FXPANGLE(0.597630608767789),
    IFX_LUT_F32_TO_FXPANGLE(0.598297674011284),
    IFX_LUT_F32_TO_FXPANGLE(0.598964133178543),
    IFX_LUT_F32_TO_FXPANGLE(0.599629986503951),
    IFX_LUT_F32_TO_FXPANGLE(0.600295234223621),
    IFX_LUT_F32_TO_FXPANGLE(0.600959876575375),
    IFX_LUT_F32_TO_FXPANGLE(0.601623913798746),
    IFX_LUT_F32_TO_FXPANGLE(0.602287346134964),
    IFX_LUT_F32_TO_FXPANGLE(0.60295017382695),
    IFX_LUT_F32_TO_FXPANGLE(0.603612397119307),
    IFX_LUT_F32_TO_FXPANGLE(0.604274016258314),
    IFX_LUT_F32_TO_FXPANGLE(0.604935031491914),
    IFX_LUT_F32_TO_FXPANGLE(0.605595443069711),
    IFX_LUT_F32_TO_FXPANGLE(0.606255251242958),
    IFX_LUT_F32_TO_FXPANGLE(0.606914456264552),
    IFX_LUT_F32_TO_FXPANGLE(0.607573058389022),
    IFX_LUT_F32_TO_FXPANGLE(0.608231057872528),
    IFX_LUT_F32_TO_FXPANGLE(0.608888454972845),
    IFX_LUT_F32_TO_FXPANGLE(0.609545249949361),
    IFX_LUT_F32_TO_FXPANGLE(0.610201443063065),
    IFX_LUT_F32_TO_FXPANGLE(0.610857034576544),
    IFX_LUT_F32_TO_FXPANGLE(0.611512024753969),
    IFX_LUT_F32_TO_FXPANGLE(0.612166413861094),
    IFX_LUT_F32_TO_FXPANGLE(0.612820202165241),
    IFX_LUT_F32_TO_FXPANGLE(0.613473389935299),
    IFX_LUT_F32_TO_FXPANGLE(0.614125977441711),
    IFX_LUT_F32_TO_FXPANGLE(0.614777964956469),
    IFX_LUT_F32_TO_FXPANGLE(0.615429352753105),
    IFX_LUT_F32_TO_FXPANGLE(0.616080141106684),
    IFX_LUT_F32_TO_FXPANGLE(0.616730330293797),
    IFX_LUT_F32_TO_FXPANGLE(0.617379920592551),
    IFX_LUT_F32_TO_FXPANGLE(0.618028912282562),
    IFX_LUT_F32_TO_FXPANGLE(0.61867730564495),
    IFX_LUT_F32_TO_FXPANGLE(0.619325100962327),
    IFX_LUT_F32_TO_FXPANGLE(0.619972298518794),
    IFX_LUT_F32_TO_FXPANGLE(0.620618898599929),
    IFX_LUT_F32_TO_FXPANGLE(0.621264901492783),
    IFX_LUT_F32_TO_FXPANGLE(0.621910307485869),
    IFX_LUT_F32_TO_FXPANGLE(0.622555116869157),
    IFX_LUT_F32_TO_FXPANGLE(0.623199329934066),
    IFX_LUT_F32_TO_FXPANGLE(0.623842946973455),
    IFX_LUT_F32_TO_FXPANGLE(0.624485968281619),
    IFX_LUT_F32_TO_FXPANGLE(0.625128394154276),
    IFX_LUT_F32_TO_FXPANGLE(0.625770224888563),
    IFX_LUT_F32_TO_FXPANGLE(0.626411460783031),
    IFX_LUT_F32_TO_FXPANGLE(0.62705210213763),
    IFX_LUT_F32_TO_FXPANGLE(0.627692149253711),
    IFX_LUT_F32_TO_FXPANGLE(0.62833160243401),
    IFX_LUT_F32_TO_FXPANGLE(0.628970461982645),
    IFX_LUT_F32_TO_FXPANGLE(0.62960872820511),
    IFX_LUT_F32_TO_FXPANGLE(0.630246401408263),
    IFX_LUT_F32_TO_FXPANGLE(0.630883481900322),
    IFX_LUT_F32_TO_FXPANGLE(0.631519969990857),
    IFX_LUT_F32_TO_FXPANGLE(0.632155865990784),
    IFX_LUT_F32_TO_FXPANGLE(0.632791170212352),
    IFX_LUT_F32_TO_FXPANGLE(0.633425882969145),
    IFX_LUT_F32_TO_FXPANGLE(0.634060004576065),
    IFX_LUT_F32_TO_FXPANGLE(0.634693535349333),
    IFX_LUT_F32_TO_FXPANGLE(0.635326475606475),
    IFX_LUT_F32_TO_FXPANGLE(0.635958825666321),
    IFX_LUT_F32_TO_FXPANGLE(0.636590585848993),
    IFX_LUT_F32_TO_FXPANGLE(0.637221756475899),
    IFX_LUT_F32_TO_FXPANGLE(0.637852337869727),
    IFX_LUT_F32_TO_FXPANGLE(0.638482330354438),
    IFX_LUT_F32_TO_FXPANGLE(0.639111734255255),
    IFX_LUT_F32_TO_FXPANGLE(0.639740549898664),
    IFX_LUT_F32_TO_FXPANGLE(0.640368777612397),
    IFX_LUT_F32_TO_FXPANGLE(0.640996417725432),
    IFX_LUT_F32_TO_FXPANGLE(0.641623470567984),
    IFX_LUT_F32_TO_FXPANGLE(0.642249936471496),
    IFX_LUT_F32_TO_FXPANGLE(0.642875815768636),
    IFX_LUT_F32_TO_FXPANGLE(0.643501108793284),
    IFX_LUT_F32_TO_FXPANGLE(0.644125815880533),
    IFX_LUT_F32_TO_FXPANGLE(0.644749937366675),
    IFX_LUT_F32_TO_FXPANGLE(0.645373473589196),
    IFX_LUT_F32_TO_FXPANGLE(0.645996424886772),
    IFX_LUT_F32_TO_FXPANGLE(0.646618791599258),
    IFX_LUT_F32_TO_FXPANGLE(0.647240574067683),
    IFX_LUT_F32_TO_FXPANGLE(0.647861772634245),
    IFX_LUT_F32_TO_FXPANGLE(0.648482387642301),
    IFX_LUT_F32_TO_FXPANGLE(0.649102419436359),
    IFX_LUT_F32_TO_FXPANGLE(0.649721868362078),
    IFX_LUT_F32_TO_FXPANGLE(0.650340734766252),
    IFX_LUT_F32_TO_FXPANGLE(0.650959018996812),
    IFX_LUT_F32_TO_FXPANGLE(0.651576721402813),
    IFX_LUT_F32_TO_FXPANGLE(0.652193842334429),
    IFX_LUT_F32_TO_FXPANGLE(0.652810382142948),
    IFX_LUT_F32_TO_FXPANGLE(0.653426341180762),
    IFX_LUT_F32_TO_FXPANGLE(0.654041719801364),
    IFX_LUT_F32_TO_FXPANGLE(0.65465651835934),
    IFX_LUT_F32_TO_FXPANGLE(0.655270737210358),
    IFX_LUT_F32_TO_FXPANGLE(0.655884376711171),
    IFX_LUT_F32_TO_FXPANGLE(0.656497437219599),
    IFX_LUT_F32_TO_FXPANGLE(0.657109919094532),
    IFX_LUT_F32_TO_FXPANGLE(0.657721822695918),
    IFX_LUT_F32_TO_FXPANGLE(0.658333148384756),
    IFX_LUT_F32_TO_FXPANGLE(0.658943896523094),
    IFX_LUT_F32_TO_FXPANGLE(0.659554067474019),
    IFX_LUT_F32_TO_FXPANGLE(0.660163661601649),
    IFX_LUT_F32_TO_FXPANGLE(0.660772679271133),
    IFX_LUT_F32_TO_FXPANGLE(0.661381120848635),
    IFX_LUT_F32_TO_FXPANGLE(0.661988986701338),
    IFX_LUT_F32_TO_FXPANGLE(0.662596277197427),
    IFX_LUT_F32_TO_FXPANGLE(0.663202992706093),
    IFX_LUT_F32_TO_FXPANGLE(0.663809133597518),
    IFX_LUT_F32_TO_FXPANGLE(0.664414700242873),
    IFX_LUT_F32_TO_FXPANGLE(0.665019693014312),
    IFX_LUT_F32_TO_FXPANGLE(0.665624112284961),
    IFX_LUT_F32_TO_FXPANGLE(0.666227958428919),
    IFX_LUT_F32_TO_FXPANGLE(0.666831231821246),
    IFX_LUT_F32_TO_FXPANGLE(0.667433932837957),
    IFX_LUT_F32_TO_FXPANGLE(0.66803606185602),
    IFX_LUT_F32_TO_FXPANGLE(0.668637619253345),
    IFX_LUT_F32_TO_FXPANGLE(0.66923860540878),
    IFX_LUT_F32_TO_FXPANGLE(0.669839020702103),
    IFX_LUT_F32_TO_FXPANGLE(0.670438865514021),
    IFX_LUT_F32_TO_FXPANGLE(0.671038140226157),
    IFX_LUT_F32_TO_FXPANGLE(0.671636845221047),
    IFX_LUT_F32_TO_FXPANGLE(0.672234980882134),
    IFX_LUT_F32_TO_FXPANGLE(0.672832547593763),
    IFX_LUT_F32_TO_FXPANGLE(0.673429545741172),
    IFX_LUT_F32_TO_FXPANGLE(0.674025975710487),
    IFX_LUT_F32_TO_FXPANGLE(0.674621837888718),
    IFX_LUT_F32_TO_FXPANGLE(0.67521713266375),
    IFX_LUT_F32_TO_FXPANGLE(0.675811860424338),
    IFX_LUT_F32_TO_FXPANGLE(0.676406021560103),
    IFX_LUT_F32_TO_FXPANGLE(0.676999616461522),
    IFX_LUT_F32_TO_FXPANGLE(0.677592645519925),
    IFX_LUT_F32_TO_FXPANGLE(0.678185109127489),
    IFX_LUT_F32_TO_FXPANGLE(0.678777007677231),
    IFX_LUT_F32_TO_FXPANGLE(0.679368341563002),
    IFX_LUT_F32_TO_FXPANGLE(0.679959111179482),
    IFX_LUT_F32_TO_FXPANGLE(0.680549316922172),
    IFX_LUT_F32_TO_FXPANGLE(0.681138959187393),
    IFX_LUT_F32_TO_FXPANGLE(0.681728038372273),
    IFX_LUT_F32_TO_FXPANGLE(0.682316554874748),
    IFX_LUT_F32_TO_FXPANGLE(0.682904509093552),
    IFX_LUT_F32_TO_FXPANGLE(0.683491901428213),
    IFX_LUT_F32_TO_FXPANGLE(0.684078732279046),
    IFX_LUT_F32_TO_FXPANGLE(0.684665002047149),
    IFX_LUT_F32_TO_FXPANGLE(0.685250711134394),
    IFX_LUT_F32_TO_FXPANGLE(0.685835859943427),
    IFX_LUT_F32_TO_FXPANGLE(0.686420448877654),
    IFX_LUT_F32_TO_FXPANGLE(0.687004478341245),
    IFX_LUT_F32_TO_FXPANGLE(0.68758794873912),
    IFX_LUT_F32_TO_FXPANGLE(0.688170860476949),
    IFX_LUT_F32_TO_FXPANGLE(0.688753213961141),
    IFX_LUT_F32_TO_FXPANGLE(0.689335009598846),
    IFX_LUT_F32_TO_FXPANGLE(0.689916247797941),
    IFX_LUT_F32_TO_FXPANGLE(0.69049692896703),
    IFX_LUT_F32_TO_FXPANGLE(0.691077053515437),
    IFX_LUT_F32_TO_FXPANGLE(0.6916566218532),
    IFX_LUT_F32_TO_FXPANGLE(0.692235634391065),
    IFX_LUT_F32_TO_FXPANGLE(0.692814091540483),
    IFX_LUT_F32_TO_FXPANGLE(0.693391993713601),
    IFX_LUT_F32_TO_FXPANGLE(0.69396934132326),
    IFX_LUT_F32_TO_FXPANGLE(0.694546134782985),
    IFX_LUT_F32_TO_FXPANGLE(0.695122374506987),
    IFX_LUT_F32_TO_FXPANGLE(0.695698060910148),
    IFX_LUT_F32_TO_FXPANGLE(0.696273194408024),
    IFX_LUT_F32_TO_FXPANGLE(0.696847775416835),
    IFX_LUT_F32_TO_FXPANGLE(0.697421804353461),
    IFX_LUT_F32_TO_FXPANGLE(0.697995281635439),
    IFX_LUT_F32_TO_FXPANGLE(0.69856820768095),
    IFX_LUT_F32_TO_FXPANGLE(0.699140582908824),
    IFX_LUT_F32_TO_FXPANGLE(0.699712407738527),
    IFX_LUT_F32_TO_FXPANGLE(0.70028368259016),
    IFX_LUT_F32_TO_FXPANGLE(0.70085440788445),
    IFX_LUT_F32_TO_FXPANGLE(0.70142458404275),
    IFX_LUT_F32_TO_FXPANGLE(0.701994211487028),
    IFX_LUT_F32_TO_FXPANGLE(0.702563290639866),
    IFX_LUT_F32_TO_FXPANGLE(0.703131821924454),
    IFX_LUT_F32_TO_FXPANGLE(0.703699805764582),
    IFX_LUT_F32_TO_FXPANGLE(0.704267242584639),
    IFX_LUT_F32_TO_FXPANGLE(0.704834132809605),
    IFX_LUT_F32_TO_FXPANGLE(0.705400476865049),
    IFX_LUT_F32_TO_FXPANGLE(0.705966275177119),
    IFX_LUT_F32_TO_FXPANGLE(0.706531528172541),
    IFX_LUT_F32_TO_FXPANGLE(0.707096236278612),
    IFX_LUT_F32_TO_FXPANGLE(0.707660399923198),
    IFX_LUT_F32_TO_FXPANGLE(0.708224019534724),
    IFX_LUT_F32_TO_FXPANGLE(0.708787095542173),
    IFX_LUT_F32_TO_FXPANGLE(0.70934962837508),
    IFX_LUT_F32_TO_FXPANGLE(0.709911618463525),
    IFX_LUT_F32_TO_FXPANGLE(0.710473066238132),
    IFX_LUT_F32_TO_FXPANGLE(0.711033972130061),
    IFX_LUT_F32_TO_FXPANGLE(0.711594336571003),
    IFX_LUT_F32_TO_FXPANGLE(0.712154159993179),
    IFX_LUT_F32_TO_FXPANGLE(0.712713442829328),
    IFX_LUT_F32_TO_FXPANGLE(0.713272185512711),
    IFX_LUT_F32_TO_FXPANGLE(0.713830388477098),
    IFX_LUT_F32_TO_FXPANGLE(0.714388052156769),
    IFX_LUT_F32_TO_FXPANGLE(0.714945176986506),
    IFX_LUT_F32_TO_FXPANGLE(0.715501763401588),
    IFX_LUT_F32_TO_FXPANGLE(0.71605781183779),
    IFX_LUT_F32_TO_FXPANGLE(0.716613322731375),
    IFX_LUT_F32_TO_FXPANGLE(0.717168296519088),
    IFX_LUT_F32_TO_FXPANGLE(0.717722733638155),
    IFX_LUT_F32_TO_FXPANGLE(0.718276634526277),
    IFX_LUT_F32_TO_FXPANGLE(0.718829999621625),
    IFX_LUT_F32_TO_FXPANGLE(0.719382829362832),
    IFX_LUT_F32_TO_FXPANGLE(0.719935124188996),
    IFX_LUT_F32_TO_FXPANGLE(0.720486884539668),
    IFX_LUT_F32_TO_FXPANGLE(0.721038110854852),
    IFX_LUT_F32_TO_FXPANGLE(0.721588803574998),
    IFX_LUT_F32_TO_FXPANGLE(0.722138963140998),
    IFX_LUT_F32_TO_FXPANGLE(0.722688589994184),
    IFX_LUT_F32_TO_FXPANGLE(0.723237684576318),
    IFX_LUT_F32_TO_FXPANGLE(0.723786247329592),
    IFX_LUT_F32_TO_FXPANGLE(0.724334278696622),
    IFX_LUT_F32_TO_FXPANGLE(0.724881779120445),
    IFX_LUT_F32_TO_FXPANGLE(0.725428749044511),
    IFX_LUT_F32_TO_FXPANGLE(0.725975188912682),
    IFX_LUT_F32_TO_FXPANGLE(0.726521099169226),
    IFX_LUT_F32_TO_FXPANGLE(0.727066480258813),
    IFX_LUT_F32_TO_FXPANGLE(0.727611332626511),
    IFX_LUT_F32_TO_FXPANGLE(0.72815565671778),
    IFX_LUT_F32_TO_FXPANGLE(0.72869945297847),
    IFX_LUT_F32_TO_FXPANGLE(0.729242721854815),
    IFX_LUT_F32_TO_FXPANGLE(0.729785463793429),
    IFX_LUT_F32_TO_FXPANGLE(0.730327679241302),
    IFX_LUT_F32_TO_FXPANGLE(0.730869368645794),
    IFX_LUT_F32_TO_FXPANGLE(0.731410532454635),
    IFX_LUT_F32_TO_FXPANGLE(0.731951171115917),
    IFX_LUT_F32_TO_FXPANGLE(0.732491285078088),
    IFX_LUT_F32_TO_FXPANGLE(0.733030874789955),
    IFX_LUT_F32_TO_FXPANGLE(0.733569940700672),
    IFX_LUT_F32_TO_FXPANGLE(0.73410848325974),
    IFX_LUT_F32_TO_FXPANGLE(0.734646502917002),
    IFX_LUT_F32_TO_FXPANGLE(0.73518400012264),
    IFX_LUT_F32_TO_FXPANGLE(0.735720975327167),
    IFX_LUT_F32_TO_FXPANGLE(0.736257428981428),
    IFX_LUT_F32_TO_FXPANGLE(0.736793361536592),
    IFX_LUT_F32_TO_FXPANGLE(0.737328773444149),
    IFX_LUT_F32_TO_FXPANGLE(0.737863665155908),
    IFX_LUT_F32_TO_FXPANGLE(0.73839803712399),
    IFX_LUT_F32_TO_FXPANGLE(0.738931889800823),
    IFX_LUT_F32_TO_FXPANGLE(0.739465223639145),
    IFX_LUT_F32_TO_FXPANGLE(0.73999803909199),
    IFX_LUT_F32_TO_FXPANGLE(0.740530336612693),
    IFX_LUT_F32_TO_FXPANGLE(0.741062116654879),
    IFX_LUT_F32_TO_FXPANGLE(0.741593379672465),
    IFX_LUT_F32_TO_FXPANGLE(0.74212412611965),
    IFX_LUT_F32_TO_FXPANGLE(0.742654356450918),
    IFX_LUT_F32_TO_FXPANGLE(0.743184071121027),
    IFX_LUT_F32_TO_FXPANGLE(0.74371327058501),
    IFX_LUT_F32_TO_FXPANGLE(0.74424195529817),
    IFX_LUT_F32_TO_FXPANGLE(0.744770125716075),
    IFX_LUT_F32_TO_FXPANGLE(0.745297782294555),
    IFX_LUT_F32_TO_FXPANGLE(0.745824925489697),
    IFX_LUT_F32_TO_FXPANGLE(0.746351555757843),
    IFX_LUT_F32_TO_FXPANGLE(0.746877673555588),
    IFX_LUT_F32_TO_FXPANGLE(0.747403279339768),
    IFX_LUT_F32_TO_FXPANGLE(0.747928373567467),
    IFX_LUT_F32_TO_FXPANGLE(0.748452956696006),
    IFX_LUT_F32_TO_FXPANGLE(0.748977029182941),
    IFX_LUT_F32_TO_FXPANGLE(0.749500591486061),
    IFX_LUT_F32_TO_FXPANGLE(0.750023644063381),
    IFX_LUT_F32_TO_FXPANGLE(0.750546187373141),
    IFX_LUT_F32_TO_FXPANGLE(0.751068221873802),
    IFX_LUT_F32_TO_FXPANGLE(0.751589748024043),
    IFX_LUT_F32_TO_FXPANGLE(0.752110766282754),
    IFX_LUT_F32_TO_FXPANGLE(0.752631277109036),
    IFX_LUT_F32_TO_FXPANGLE(0.753151280962194),
    IFX_LUT_F32_TO_FXPANGLE(0.75367077830174),
    IFX_LUT_F32_TO_FXPANGLE(0.754189769587379),
    IFX_LUT_F32_TO_FXPANGLE(0.754708255279016),
    IFX_LUT_F32_TO_FXPANGLE(0.755226235836745),
    IFX_LUT_F32_TO_FXPANGLE(0.755743711720849),
    IFX_LUT_F32_TO_FXPANGLE(0.756260683391796),
    IFX_LUT_F32_TO_FXPANGLE(0.756777151310235),
    IFX_LUT_F32_TO_FXPANGLE(0.757293115936992),
    IFX_LUT_F32_TO_FXPANGLE(0.757808577733069),
    IFX_LUT_F32_TO_FXPANGLE(0.758323537159635),
    IFX_LUT_F32_TO_FXPANGLE(0.758837994678031),
    IFX_LUT_F32_TO_FXPANGLE(0.759351950749758),
    IFX_LUT_F32_TO_FXPANGLE(0.75986540583648),
    IFX_LUT_F32_TO_FXPANGLE(0.760378360400016),
    IFX_LUT_F32_TO_FXPANGLE(0.760890814902341),
    IFX_LUT_F32_TO_FXPANGLE(0.761402769805578),
    IFX_LUT_F32_TO_FXPANGLE(0.761914225571999),
    IFX_LUT_F32_TO_FXPANGLE(0.762425182664017),
    IFX_LUT_F32_TO_FXPANGLE(0.762935641544187),
    IFX_LUT_F32_TO_FXPANGLE(0.763445602675202),
    IFX_LUT_F32_TO_FXPANGLE(0.763955066519886),
    IFX_LUT_F32_TO_FXPANGLE(0.764464033541195),
    IFX_LUT_F32_TO_FXPANGLE(0.764972504202212),
    IFX_LUT_F32_TO_FXPANGLE(0.765480478966144),
    IFX_LUT_F32_TO_FXPANGLE(0.765987958296319),
    IFX_LUT_F32_TO_FXPANGLE(0.766494942656179),
    IFX_LUT_F32_TO_FXPANGLE(0.767001432509286),
    IFX_LUT_F32_TO_FXPANGLE(0.767507428319308),
    IFX_LUT_F32_TO_FXPANGLE(0.768012930550024),
    IFX_LUT_F32_TO_FXPANGLE(0.768517939665315),
    IFX_LUT_F32_TO_FXPANGLE(0.769022456129166),
    IFX_LUT_F32_TO_FXPANGLE(0.769526480405658),
    IFX_LUT_F32_TO_FXPANGLE(0.77003001295897),
    IFX_LUT_F32_TO_FXPANGLE(0.770533054253371),
    IFX_LUT_F32_TO_FXPANGLE(0.771035604753219),
    IFX_LUT_F32_TO_FXPANGLE(0.77153766492296),
    IFX_LUT_F32_TO_FXPANGLE(0.77203923522712),
    IFX_LUT_F32_TO_FXPANGLE(0.772540316130307),
    IFX_LUT_F32_TO_FXPANGLE(0.773040908097206),
    IFX_LUT_F32_TO_FXPANGLE(0.773541011592573),
    IFX_LUT_F32_TO_FXPANGLE(0.774040627081239),
    IFX_LUT_F32_TO_FXPANGLE(0.7745397550281),
    IFX_LUT_F32_TO_FXPANGLE(0.775038395898116),
    IFX_LUT_F32_TO_FXPANGLE(0.775536550156312),
    IFX_LUT_F32_TO_FXPANGLE(0.776034218267768),
    IFX_LUT_F32_TO_FXPANGLE(0.776531400697624),
    IFX_LUT_F32_TO_FXPANGLE(0.77702809791107),
    IFX_LUT_F32_TO_FXPANGLE(0.777524310373348),
    IFX_LUT_F32_TO_FXPANGLE(0.778020038549745),
    IFX_LUT_F32_TO_FXPANGLE(0.778515282905595),
    IFX_LUT_F32_TO_FXPANGLE(0.779010043906271),
    IFX_LUT_F32_TO_FXPANGLE(0.779504322017186),
    IFX_LUT_F32_TO_FXPANGLE(0.77999811770379),
    IFX_LUT_F32_TO_FXPANGLE(0.780491431431562),
    IFX_LUT_F32_TO_FXPANGLE(0.780984263666015),
    IFX_LUT_F32_TO_FXPANGLE(0.781476614872688),
    IFX_LUT_F32_TO_FXPANGLE(0.781968485517144),
    IFX_LUT_F32_TO_FXPANGLE(0.782459876064968),
    IFX_LUT_F32_TO_FXPANGLE(0.782950786981764),
    IFX_LUT_F32_TO_FXPANGLE(0.783441218733152),
    IFX_LUT_F32_TO_FXPANGLE(0.783931171784766),
    IFX_LUT_F32_TO_FXPANGLE(0.784420646602251),
    IFX_LUT_F32_TO_FXPANGLE(0.784909643651259),
    IFX_LUT_F32_TO_FXPANGLE(0.785398163397448),
};
#endif

const float32          Ifx_g_LutAtan2F32_table[IFX_LUTATAN2F32_SIZE + 1] =
{0,                    0.000976562189559319f, 0.00195312251647882f, 0.00292967911813999f, 0.00390623013196697f, 0.00488277369544783f, 0.00585930794615589f, 0.00683583102177106f, 0.00781234106010111f, 0.008788836199103f,
 0.00976531457690415f, 0.0107417743318238f,   0.0117182136023941f,  0.0126946305273818f,  0.0136710232458091f,  0.014647389896975f,   0.0156237286204768f,  0.0166000375562313f,  0.0175763148444956f,
 0.018552558625889f,   0.0195287670414137f,   0.0205049382324764f,  0.0214810703409091f,  0.0224571615089906f,  0.0234332098794676f,  0.0244092135955758f,  0.0253851708010611f,  0.0263610796402008f,
 0.0273369382578244f,  0.0283127447993352f,   0.029288497410731f,   0.0302641942386252f,  0.0312398334302683f,  0.0322154131335681f,  0.0331909314971116f,  0.0341663866701854f,  0.0351417768027968f,
 0.036117100045695f,   0.0370923545503918f,   0.0380675384691825f,  0.039042649955167f,   0.0400176871622703f,  0.0409926482452638f,  0.0419675313597857f,  0.0429423346623622f,  0.0439170563104278f,
 0.0448916944623465f,  0.0458662472774322f,   0.0468407129159697f,  0.0478150895392348f,  0.0487893753095156f,  0.0497635683901328f,  0.0507376669454602f,  0.0517116691409454f,  0.05268557314313f,
 0.0536593771196708f,  0.0546330792393595f,   0.0556066776721433f,  0.0565801705891457f,  0.0575535561626864f,  0.0585268325663018f,  0.0594999979747652f,  0.0604730505641073f,  0.0614459885116361f,
 0.0624188099959574f,  0.0633915131969944f,   0.0643640962960086f,  0.0653365574756192f,  0.0663088949198235f,  0.0672811068140167f,  0.068253191345012f,   0.0692251467010603f,  0.0701969710718705f,
 0.0711686626486288f,  0.0721402196240188f,   0.0731116401922413f,  0.0740829225490337f,  0.0750540648916902f,  0.0760250654190807f,  0.0769959223316711f,  0.0779666338315423f,  0.07893719812241f,
 0.0799076134096439f,  0.0808778779002873f,   0.0818479898030765f,  0.0828179473284599f,  0.0837877486886171f,  0.0847573920974787f,  0.0857268757707448f,  0.0866961979259047f,  0.0876653567822554f,
 0.0886343505609211f,  0.0896031774848717f,   0.0905718357789422f,  0.0915403236698511f,  0.0925086393862194f,  0.0934767811585895f,  0.0944447472194436f,  0.0954125358032227f,  0.0963801451463448f,
 0.0973475734872237f,  0.0983148190662873f,   0.0992818801259964f,  0.100248754910863f,   0.101215441667467f,   0.102181938644477f,   0.103148244092669f,   0.10411435626494f,    0.10508027341633f,    0.106045993804039f,
 0.107011515687447f,   0.107976837328126f,    0.108941956989866f,   0.109906872938685f,   0.110871583442852f,   0.111836086772904f,   0.112800381201659f,   0.113764465004242f,   0.114728336458092f,   0.115691993842991f,
 0.116655435441069f,   0.117618659536834f,    0.118581664417177f,   0.1195444483714f,     0.120507009691225f,   0.121469346670814f,   0.122431457606789f,   0.123393340798243f,   0.124354994546761f,   0.125316417156437f,
 0.126277606933887f,   0.127238562188269f,    0.128199281231298f,   0.129159762377265f,   0.130120003943049f,   0.131080004248137f,   0.132039761614639f,   0.132999274367304f,   0.133958540833537f,   0.134917559343415f,
 0.135876328229701f,   0.136834845827863f,    0.137793110476088f,   0.138751120515297f,   0.139708874289164f,   0.140666370144127f,   0.14162360642941f,    0.14258058149703f,    0.143537293701821f,   0.144493741401443f,
 0.145449922956401f,   0.146405836730058f,    0.147361481088652f,   0.148316854401309f,   0.14927195504006f,    0.150226781379856f,   0.15118133179858f,    0.152135604677064f,   0.153089598399105f,   0.154043311351475f,
 0.154996741923941f,   0.155949888509276f,    0.156902749503275f,   0.157855323304766f,   0.158807608315631f,   0.159759602940813f,   0.160711305588332f,   0.161662714669305f,   0.162613828597949f,   0.163564645791604f,
 0.164515164670743f,   0.165465383658987f,    0.166415301183115f,   0.167364915673083f,   0.168314225562034f,   0.169263229286312f,   0.170211925285474f,   0.171160312002307f,   0.172108387882836f,   0.17305615137634f,
 0.174003600935368f,   0.174950735015743f,    0.175897552076585f,   0.176844050580316f,   0.177790228992676f,   0.178736085782736f,   0.179681619422909f,   0.180626828388963f,   0.181571711160032f,   0.182516266218631f,
 0.183460492050666f,   0.184404387145446f,    0.185347949995695f,   0.186291179097566f,   0.187234072950649f,   0.188176630057987f,   0.189118848926084f,   0.190060728064918f,   0.191002265987953f,   0.191943461212149f,
 0.192884312257975f,   0.193824817649417f,    0.194764975913995f,   0.195704785582767f,   0.196644245190345f,   0.197583353274903f,   0.19852210837819f,    0.199460509045539f,   0.200398553825879f,   0.201336241271742f,
 0.202273569939279f,   0.203210538388266f,    0.204147145182117f,   0.205083388887891f,   0.206019268076305f,   0.206954781321742f,   0.207889927202263f,   0.208824704299615f,   0.209759111199241f,   0.210693146490291f,
 0.21162680876563f,    0.212560096621847f,    0.213493008659266f,   0.214425543481956f,   0.215357699697738f,   0.216289475918194f,   0.217220870758679f,   0.218151882838326f,   0.219082510780058f,   0.220012753210596f,
 0.220942608760466f,   0.22187207606401f,     0.222801153759395f,   0.223729840488615f,   0.22465813489751f,    0.225586035635764f,   0.22651354135692f,    0.227440650718385f,   0.228367362381439f,   0.229293675011242f,
 0.230219587276844f,   0.231145097851188f,    0.232070205411125f,   0.232994908637414f,   0.233919206214733f,   0.234843096831689f,   0.23576657918082f,    0.236689651958605f,   0.237612313865471f,   0.238534563605801f,
 0.239456399887938f,   0.240377821424194f,    0.241298826930859f,   0.242219415128201f,   0.24313958474048f,    0.244059334495949f,   0.244978663126864f,   0.24589756936949f,    0.246816051964103f,   0.247734109655002f,
 0.248651741190513f,   0.249568945322993f,    0.250485720808836f,   0.251402066408485f,   0.252317980886427f,   0.25323346301121f,    0.254148511555439f,   0.25506312529579f,    0.255977303013006f,   0.25689104349191f,
 0.25780434552141f,    0.258717207894497f,    0.259629629408258f,   0.260541608863876f,   0.261453145066638f,   0.262364236825938f,   0.263274882955282f,   0.264185082272293f,   0.265094833598715f,   0.266004135760417f,
 0.2669129875874f,     0.267821387913799f,    0.268729335577886f,   0.269636829422078f,   0.270543868292937f,   0.271450451041176f,   0.272356576521665f,   0.27326224359343f,    0.274167451119659f,   0.275072197967707f,
 0.275976483009098f,   0.27688030511953f,     0.277783663178873f,   0.278686556071182f,   0.27958898268469f,    0.280490941911819f,   0.281392432649178f,   0.282293453797569f,   0.283194004261987f,   0.284094082951627f,
 0.284993688779881f,   0.285892820664347f,    0.286791477526827f,   0.28768965829333f,    0.288587361894077f,   0.289484587263501f,   0.29038133334025f,    0.291277599067188f,   0.292173383391399f,   0.293068685264188f,
 0.293963503641084f,   0.294857837481838f,    0.295751685750432f,   0.296645047415071f,   0.297537921448196f,   0.298430306826474f,   0.299322202530807f,   0.300213607546333f,   0.301104520862424f,   0.301994941472688f,
 0.302884868374971f,   0.303774300571361f,    0.304663237068183f,   0.305551676876003f,   0.30643961900963f,    0.307327062488116f,   0.308214006334753f,   0.309100449577082f,   0.309986391246883f,   0.310871830380185f,
 0.31175676601726f,    0.312641197202625f,    0.313525122985044f,   0.314408542417527f,   0.31529145455733f,    0.316173858465954f,   0.317055753209147f,   0.317937137856902f,   0.318818011483458f,   0.3196983731673f,
 0.320578221991157f,   0.321457557042003f,    0.322336377411056f,   0.323214682193777f,   0.324092470489872f,   0.324969741403286f,   0.325846494042208f,   0.326722727519067f,   0.327598440950531f,   0.328473633457506f,
 0.329348304165139f,   0.330222452202808f,    0.331096076704132f,   0.33196917680696f,    0.332841751653376f,   0.333713800389694f,   0.334585322166459f,   0.335456316138443f,   0.336326781464645f,   0.33719671730829f,
 0.338066122836825f,   0.338934997221921f,    0.339803339639465f,   0.340671149269565f,   0.341538425296542f,   0.342405166908932f,   0.343271373299484f,   0.344137043665154f,   0.345002177207105f,   0.345866773130707f,
 0.34673083064553f,    0.347594348965346f,    0.348457327308122f,   0.349319764896022f,   0.350181660955402f,   0.351043014716805f,   0.351903825414965f,   0.352764092288795f,   0.353623814581394f,   0.354482991540035f,
 0.355341622416168f,   0.356199706465416f,    0.357057242947567f,   0.35791423112658f,    0.358770670270572f,   0.359626559651822f,   0.360481898546763f,   0.361336686235982f,   0.362190922004212f,   0.363044605140335f,
 0.363897734937373f,   0.364750310692485f,    0.365602331706967f,   0.366453797286243f,   0.367304706739864f,   0.368155059381507f,   0.369004854528964f,   0.369854091504145f,   0.370702769633069f,   0.371550888245862f,
 0.372398446676754f,   0.373245444264073f,    0.374091880350239f,   0.374937754281765f,   0.375783065409249f,   0.376627813087368f,   0.377471996674877f,   0.378315615534604f,   0.379158669033442f,   0.380001156542349f,
 0.380843077436342f,   0.381684431094488f,    0.382525216899905f,   0.383365434239755f,   0.384205082505239f,   0.385044161091591f,   0.385882669398074f,   0.386720606827975f,   0.387557972788602f,   0.388394766691274f,
 0.389230987951321f,   0.390066635988073f,    0.390901710224862f,   0.39173621008901f,    0.392570135011829f,   0.393403484428609f,   0.394236257778621f,   0.395068454505103f,   0.395900074055263f,   0.396731115880264f,
 0.397561579435227f,   0.39839146417922f,     0.399220769575253f,   0.400049495090274f,   0.400877640195163f,   0.401705204364725f,   0.402532187077683f,   0.403358587816674f,   0.404184406068245f,   0.405009641322841f,
 0.405834293074804f,   0.406658360822366f,    0.407481844067642f,   0.408304742316622f,   0.409127055079168f,   0.409948781869008f,   0.410769922203726f,   0.411590475604759f,   0.412410441597387f,   0.413229819710733f,
 0.414048609477749f,   0.414866810435215f,    0.415684422123729f,   0.416501444087703f,   0.417317875875355f,   0.418133717038701f,   0.418948967133553f,   0.419763625719506f,   0.420577692359935f,   0.42139116662199f,
 0.422204048076584f,   0.423016336298389f,    0.423828030865831f,   0.42463913136108f,    0.425449637370042f,   0.426259548482358f,   0.427068864291389f,   0.427877584394215f,   0.428685708391626f,   0.429493235888114f,
 0.430300166491866f,   0.431106499814758f,    0.431912235472348f,   0.432717373083866f,   0.433521912272209f,   0.434325852663933f,   0.435129193889247f,   0.435931935582003f,   0.43673407737969f,    0.437535618923428f,
 0.438336559857958f,   0.439136899831635f,    0.439936638496422f,   0.440735775507881f,   0.441534310525167f,   0.442332243211017f,   0.443129573231746f,   0.443926300257239f,   0.444722423960939f,   0.445517944019847f,
 0.446312860114506f,   0.447107171928999f,    0.447900879150937f,   0.448693981471457f,   0.449486478585208f,   0.450278370190345f,   0.451069655988523f,   0.451860335684889f,   0.452650408988071f,   0.453439875610172f,
 0.454228735266762f,   0.455016987676872f,    0.45580463256298f,    0.456591669651011f,   0.457378098670321f,   0.458163919353695f,   0.458949131437335f,   0.459733734660856f,   0.460517728767271f,   0.461301113502991f,
 0.46208388861781f,    0.462866053864901f,    0.463647609000806f,   0.464428553785428f,   0.465208887982023f,   0.46598861135719f,    0.466767723680866f,   0.467546224726316f,   0.468324114270123f,   0.46910139209218f,
 0.469878057975687f,   0.470654111707133f,    0.471429553076297f,   0.472204381876234f,   0.472978597903266f,   0.473752200956977f,   0.474525190840205f,   0.475297567359028f,   0.476069330322761f,   0.476840479543945f,
 0.477611014838337f,   0.478380936024907f,    0.479150242925823f,   0.479918935366444f,   0.480687013175316f,   0.481454476184158f,   0.482221324227854f,   0.482987557144447f,   0.48375317477513f,    0.484518176964233f,
 0.485282563559221f,   0.48604633441068f,     0.48680948937231f,    0.487572028300918f,   0.488333951056406f,   0.489095257501764f,   0.489855947503062f,   0.490616020929441f,   0.491375477653102f,   0.4921343175493f,
 0.492892540496335f,   0.49365014637554f,     0.494407135071275f,   0.49516350647092f,    0.495919260464861f,   0.496674396946486f,   0.497428915812172f,   0.498182816961281f,   0.498936100296146f,   0.499688765722065f,
 0.500440813147294f,   0.501192242483033f,    0.501943053643421f,   0.502693246545526f,   0.503442821109336f,   0.504191777257751f,   0.504940114916572f,   0.505687834014494f,   0.506434934483097f,   0.507181416256835f,
 0.50792727927303f,    0.508672523471861f,    0.509417148796356f,   0.510161155192383f,   0.51090454260864f,    0.511647310996647f,   0.512389460310738f,   0.513130990508049f,   0.513871901548512f,   0.514612193394845f,
 0.515351866012543f,   0.51609091936987f,     0.516829353437846f,   0.517567168190245f,   0.518304363603578f,   0.519040939657092f,   0.519776896332754f,   0.520512233615247f,   0.521246951491958f,   0.521981049952972f,
 0.522714528991058f,   0.523447388601666f,    0.524179628782913f,   0.524911249535579f,   0.525642250863092f,   0.526372632771524f,   0.52710239526958f,    0.527831538368588f,   0.528560062082493f,   0.529287966427846f,
 0.530015251423793f,   0.530741917092071f,    0.531467963456995f,   0.53219339054545f,    0.532918198386882f,   0.533642387013291f,   0.534365956459219f,   0.535088906761743f,   0.535811237960464f,   0.5365329500975f,
 0.537254043217479f,   0.537974517367523f,    0.538694372597247f,   0.539413608958744f,   0.540132226506582f,   0.540850225297788f,   0.541567605391845f,   0.542284366850679f,   0.543000509738655f,   0.54371603412256f,
 0.544430940071603f,   0.545145227657401f,    0.54585889695397f,    0.546571948037719f,   0.547284380987437f,   0.547996195884288f,   0.548707392811801f,   0.549417971855859f,   0.550127933104693f,   0.55083727664887f,
 0.551546002581289f,   0.552254110997165f,    0.552961601994028f,   0.553668475671709f,   0.554374732132331f,   0.555080371480305f,   0.555785393822314f,   0.55648979926731f,    0.557193587926504f,   0.557896759913355f,
 0.558599315343562f,   0.559301254335059f,    0.560002577007999f,   0.560703283484751f,   0.561403373889889f,   0.562102848350186f,   0.562801706994599f,   0.563499949954267f,   0.564197577362498f,   0.564894589354762f,
 0.565590986068683f,   0.566286767644028f,    0.5669819342227f,     0.56767648594873f,    0.568370422968264f,   0.569063745429561f,   0.569756453482978f,   0.570448547280968f,   0.571140026978063f,   0.571830892730873f,
 0.572521144698072f,   0.573210783040395f,    0.573899807920624f,   0.57458821950358f,    0.575276017956118f,   0.575963203447116f,   0.576649776147467f,   0.577335736230069f,   0.57802108386982f,    0.578705819243603f,
 0.579389942530287f,   0.580073453910708f,    0.58075635356767f,    0.58143864168593f,    0.582120318452191f,   0.582801384055095f,   0.583481838685215f,   0.584161682535044f,   0.584840915798988f,   0.585519538673358f,
 0.586197551356361f,   0.586874954048091f,    0.587551746950524f,   0.588227930267504f,   0.588903504204738f,   0.58957846896979f,    0.590252824772067f,   0.590926571822816f,   0.591599710335111f,   0.592272240523851f,
 0.592944162605743f,   0.593615476799303f,    0.594286183324841f,   0.594956282404456f,   0.595625774262027f,   0.596294659123204f,   0.596962937215402f,   0.597630608767789f,   0.598297674011284f,   0.598964133178543f,
 0.599629986503951f,   0.600295234223621f,    0.600959876575375f,   0.601623913798746f,   0.602287346134964f,   0.60295017382695f,    0.603612397119307f,   0.604274016258314f,   0.604935031491914f,   0.605595443069711f,
 0.606255251242958f,   0.606914456264552f,    0.607573058389022f,   0.608231057872528f,   0.608888454972845f,   0.609545249949361f,   0.610201443063065f,   0.610857034576544f,   0.611512024753969f,   0.612166413861094f,
 0.612820202165241f,   0.613473389935299f,    0.614125977441711f,   0.614777964956469f,   0.615429352753105f,   0.616080141106684f,   0.616730330293797f,   0.617379920592551f,   0.618028912282562f,   0.61867730564495f,
 0.619325100962327f,   0.619972298518794f,    0.620618898599929f,   0.621264901492783f,   0.621910307485869f,   0.622555116869157f,   0.623199329934066f,   0.623842946973455f,   0.624485968281619f,   0.625128394154276f,
 0.625770224888563f,   0.626411460783031f,    0.62705210213763f,    0.627692149253711f,   0.62833160243401f,    0.628970461982645f,   0.62960872820511f,    0.630246401408263f,   0.630883481900322f,   0.631519969990857f,
 0.632155865990784f,   0.632791170212352f,    0.633425882969145f,   0.634060004576065f,   0.634693535349333f,   0.635326475606475f,   0.635958825666321f,   0.636590585848993f,   0.637221756475899f,   0.637852337869727f,
 0.638482330354438f,   0.639111734255255f,    0.639740549898664f,   0.640368777612397f,   0.640996417725432f,   0.641623470567984f,   0.642249936471496f,   0.642875815768636f,   0.643501108793284f,   0.644125815880533f,
 0.644749937366675f,   0.645373473589196f,    0.645996424886772f,   0.646618791599258f,   0.647240574067683f,   0.647861772634245f,   0.648482387642301f,   0.649102419436359f,   0.649721868362078f,   0.650340734766252f,
 0.650959018996812f,   0.651576721402813f,    0.652193842334429f,   0.652810382142948f,   0.653426341180762f,   0.654041719801364f,   0.65465651835934f,    0.655270737210358f,   0.655884376711171f,   0.656497437219599f,
 0.657109919094532f,   0.657721822695918f,    0.658333148384756f,   0.658943896523094f,   0.659554067474019f,   0.660163661601649f,   0.660772679271133f,   0.661381120848635f,   0.661988986701338f,   0.662596277197427f,
 0.663202992706093f,   0.663809133597518f,    0.664414700242873f,   0.665019693014312f,   0.665624112284961f,   0.666227958428919f,   0.666831231821246f,   0.667433932837957f,   0.66803606185602f,    0.668637619253345f,
 0.66923860540878f,    0.669839020702103f,    0.670438865514021f,   0.671038140226157f,   0.671636845221047f,   0.672234980882134f,   0.672832547593763f,   0.673429545741172f,   0.674025975710487f,   0.674621837888718f,
 0.67521713266375f,    0.675811860424338f,    0.676406021560103f,   0.676999616461522f,   0.677592645519925f,   0.678185109127489f,   0.678777007677231f,   0.679368341563002f,   0.679959111179482f,   0.680549316922172f,
 0.681138959187393f,   0.681728038372273f,    0.682316554874748f,   0.682904509093552f,   0.683491901428213f,   0.684078732279046f,   0.684665002047149f,   0.685250711134394f,   0.685835859943427f,   0.686420448877654f,
 0.687004478341245f,   0.68758794873912f,     0.688170860476949f,   0.688753213961141f,   0.689335009598846f,   0.689916247797941f,   0.69049692896703f,    0.691077053515437f,   0.6916566218532f,     0.692235634391065f,
 0.692814091540483f,   0.693391993713601f,    0.69396934132326f,    0.694546134782985f,   0.695122374506987f,   0.695698060910148f,   0.696273194408024f,   0.696847775416835f,   0.697421804353461f,   0.697995281635439f,
 0.69856820768095f,    0.699140582908824f,    0.699712407738527f,   0.70028368259016f,    0.70085440788445f,    0.70142458404275f,    0.701994211487028f,   0.702563290639866f,   0.703131821924454f,   0.703699805764582f,
 0.704267242584639f,   0.704834132809605f,    0.705400476865049f,   0.705966275177119f,   0.706531528172541f,   0.707096236278612f,   0.707660399923198f,   0.708224019534724f,   0.708787095542173f,   0.70934962837508f,
 0.709911618463525f,   0.710473066238132f,    0.711033972130061f,   0.711594336571003f,   0.712154159993179f,   0.712713442829328f,   0.713272185512711f,   0.713830388477098f,   0.714388052156769f,   0.714945176986506f,
 0.715501763401588f,   0.71605781183779f,     0.716613322731375f,   0.717168296519088f,   0.717722733638155f,   0.718276634526277f,   0.718829999621625f,   0.719382829362832f,   0.719935124188996f,   0.720486884539668f,
 0.721038110854852f,   0.721588803574998f,    0.722138963140998f,   0.722688589994184f,   0.723237684576318f,   0.723786247329592f,   0.724334278696622f,   0.724881779120445f,   0.725428749044511f,   0.725975188912682f,
 0.726521099169226f,   0.727066480258813f,    0.727611332626511f,   0.72815565671778f,    0.72869945297847f,    0.729242721854815f,   0.729785463793429f,   0.730327679241302f,   0.730869368645794f,   0.731410532454635f,
 0.731951171115917f,   0.732491285078088f,    0.733030874789955f,   0.733569940700672f,   0.73410848325974f,    0.734646502917002f,   0.73518400012264f,    0.735720975327167f,   0.736257428981428f,   0.736793361536592f,
 0.737328773444149f,   0.737863665155908f,    0.73839803712399f,    0.738931889800823f,   0.739465223639145f,   0.73999803909199f,    0.740530336612693f,   0.741062116654879f,   0.741593379672465f,   0.74212412611965f,
 0.742654356450918f,   0.743184071121027f,    0.74371327058501f,    0.74424195529817f,    0.744770125716075f,   0.745297782294555f,   0.745824925489697f,   0.746351555757843f,   0.746877673555588f,   0.747403279339768f,
 0.747928373567467f,   0.748452956696006f,    0.748977029182941f,   0.749500591486061f,   0.750023644063381f,   0.750546187373141f,   0.751068221873802f,   0.751589748024043f,   0.752110766282754f,   0.752631277109036f,
 0.753151280962194f,   0.75367077830174f,     0.754189769587379f,   0.754708255279016f,   0.755226235836745f,   0.755743711720849f,   0.756260683391796f,   0.756777151310235f,   0.757293115936992f,   0.757808577733069f,
 0.758323537159635f,   0.758837994678031f,    0.759351950749758f,   0.75986540583648f,    0.760378360400016f,   0.760890814902341f,   0.761402769805578f,   0.761914225571999f,   0.762425182664017f,   0.762935641544187f,
 0.763445602675202f,   0.763955066519886f,    0.764464033541195f,   0.764972504202212f,   0.765480478966144f,   0.765987958296319f,   0.766494942656179f,   0.767001432509286f,   0.767507428319308f,   0.768012930550024f,
 0.768517939665315f,   0.769022456129166f,    0.769526480405658f,   0.77003001295897f,    0.770533054253371f,   0.771035604753219f,   0.77153766492296f,    0.77203923522712f,    0.772540316130307f,   0.773040908097206f,
 0.773541011592573f,   0.774040627081239f,    0.7745397550281f,     0.775038395898116f,   0.775536550156312f,   0.776034218267768f,   0.776531400697624f,   0.77702809791107f,    0.777524310373348f,   0.778020038549745f,
 0.778515282905595f,   0.779010043906271f,    0.779504322017186f,   0.77999811770379f,    0.780491431431562f,   0.780984263666015f,   0.781476614872688f,   0.781968485517144f,   0.782459876064968f,   0.782950786981764f,
 0.783441218733152f,   0.783931171784766f,    0.784420646602251f,   0.784909643651259f,   0.785398163397448f};
