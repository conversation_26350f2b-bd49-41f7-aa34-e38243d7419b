	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc2972a --dep-file=IfxMultican_PinMap.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMultican_PinMap.src ../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMultican_PinMap.c"
	.compiler_name		"ctc"
	;source	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMultican_PinMap.c'

	
$TC16X
	
	.sdecl	'.rodata.IfxMultican_PinMap.IfxMultican_RXD0A_P02_1_IN',data,rom,cluster('IfxMultican_RXD0A_P02_1_IN')
	.sect	'.rodata.IfxMultican_PinMap.IfxMultican_RXD0A_P02_1_IN'
	.global	IfxMultican_RXD0A_P02_1_IN
	.align	4
IfxMultican_RXD0A_P02_1_IN:	.type	object
	.size	IfxMultican_RXD0A_P02_1_IN,20
	.word	-268337152
	.space	4
	.word	-268197376
	.byte	1
	.space	7
	.sdecl	'.rodata.IfxMultican_PinMap.IfxMultican_RXD0B_P20_7_IN',data,rom,cluster('IfxMultican_RXD0B_P20_7_IN')
	.sect	'.rodata.IfxMultican_PinMap.IfxMultican_RXD0B_P20_7_IN'
	.global	IfxMultican_RXD0B_P20_7_IN
	.align	4
IfxMultican_RXD0B_P20_7_IN:	.type	object
	.size	IfxMultican_RXD0B_P20_7_IN,20
	.word	-268337152
	.space	4
	.word	-268189696
	.byte	7
	.space	3
	.byte	1
	.space	3
	.sdecl	'.rodata.IfxMultican_PinMap.IfxMultican_RXD0D_P02_4_IN',data,rom,cluster('IfxMultican_RXD0D_P02_4_IN')
	.sect	'.rodata.IfxMultican_PinMap.IfxMultican_RXD0D_P02_4_IN'
	.global	IfxMultican_RXD0D_P02_4_IN
	.align	4
IfxMultican_RXD0D_P02_4_IN:	.type	object
	.size	IfxMultican_RXD0D_P02_4_IN,20
	.word	-268337152
	.space	4
	.word	-268197376
	.byte	4
	.space	3
	.byte	3
	.space	3
	.sdecl	'.rodata.IfxMultican_PinMap.IfxMultican_RXD0E_P33_7_IN',data,rom,cluster('IfxMultican_RXD0E_P33_7_IN')
	.sect	'.rodata.IfxMultican_PinMap.IfxMultican_RXD0E_P33_7_IN'
	.global	IfxMultican_RXD0E_P33_7_IN
	.align	4
IfxMultican_RXD0E_P33_7_IN:	.type	object
	.size	IfxMultican_RXD0E_P33_7_IN,20
	.word	-268337152
	.space	4
	.word	-268184832
	.byte	7
	.space	3
	.byte	4
	.space	3
	.sdecl	'.rodata.IfxMultican_PinMap.IfxMultican_RXD1A_P15_3_IN',data,rom,cluster('IfxMultican_RXD1A_P15_3_IN')
	.sect	'.rodata.IfxMultican_PinMap.IfxMultican_RXD1A_P15_3_IN'
	.global	IfxMultican_RXD1A_P15_3_IN
	.align	4
IfxMultican_RXD1A_P15_3_IN:	.type	object
	.size	IfxMultican_RXD1A_P15_3_IN,20
	.word	-268337152
	.byte	1
	.space	3
	.word	-268192512
	.byte	3
	.space	7
	.sdecl	'.rodata.IfxMultican_PinMap.IfxMultican_RXD1B_P14_1_IN',data,rom,cluster('IfxMultican_RXD1B_P14_1_IN')
	.sect	'.rodata.IfxMultican_PinMap.IfxMultican_RXD1B_P14_1_IN'
	.global	IfxMultican_RXD1B_P14_1_IN
	.align	4
IfxMultican_RXD1B_P14_1_IN:	.type	object
	.size	IfxMultican_RXD1B_P14_1_IN,20
	.word	-268337152
	.byte	1
	.space	3
	.word	-268192768
	.byte	1
	.space	3
	.byte	1
	.space	3
	.sdecl	'.rodata.IfxMultican_PinMap.IfxMultican_RXD1D_P00_1_IN',data,rom,cluster('IfxMultican_RXD1D_P00_1_IN')
	.sect	'.rodata.IfxMultican_PinMap.IfxMultican_RXD1D_P00_1_IN'
	.global	IfxMultican_RXD1D_P00_1_IN
	.align	4
IfxMultican_RXD1D_P00_1_IN:	.type	object
	.size	IfxMultican_RXD1D_P00_1_IN,20
	.word	-268337152
	.byte	1
	.space	3
	.word	-268197888
	.byte	1
	.space	3
	.byte	3
	.space	3
	.sdecl	'.rodata.IfxMultican_PinMap.IfxMultican_RXD2A_P15_1_IN',data,rom,cluster('IfxMultican_RXD2A_P15_1_IN')
	.sect	'.rodata.IfxMultican_PinMap.IfxMultican_RXD2A_P15_1_IN'
	.global	IfxMultican_RXD2A_P15_1_IN
	.align	4
IfxMultican_RXD2A_P15_1_IN:	.type	object
	.size	IfxMultican_RXD2A_P15_1_IN,20
	.word	-268337152
	.byte	2
	.space	3
	.word	-268192512
	.byte	1
	.space	7
	.sdecl	'.rodata.IfxMultican_PinMap.IfxMultican_RXD2B_P02_3_IN',data,rom,cluster('IfxMultican_RXD2B_P02_3_IN')
	.sect	'.rodata.IfxMultican_PinMap.IfxMultican_RXD2B_P02_3_IN'
	.global	IfxMultican_RXD2B_P02_3_IN
	.align	4
IfxMultican_RXD2B_P02_3_IN:	.type	object
	.size	IfxMultican_RXD2B_P02_3_IN,20
	.word	-268337152
	.byte	2
	.space	3
	.word	-268197376
	.byte	3
	.space	3
	.byte	1
	.space	3
	.sdecl	'.rodata.IfxMultican_PinMap.IfxMultican_RXD2D_P14_8_IN',data,rom,cluster('IfxMultican_RXD2D_P14_8_IN')
	.sect	'.rodata.IfxMultican_PinMap.IfxMultican_RXD2D_P14_8_IN'
	.global	IfxMultican_RXD2D_P14_8_IN
	.align	4
IfxMultican_RXD2D_P14_8_IN:	.type	object
	.size	IfxMultican_RXD2D_P14_8_IN,20
	.word	-268337152
	.byte	2
	.space	3
	.word	-268192768
	.byte	8
	.space	3
	.byte	3
	.space	3
	.sdecl	'.rodata.IfxMultican_PinMap.IfxMultican_RXD2E_P10_2_IN',data,rom,cluster('IfxMultican_RXD2E_P10_2_IN')
	.sect	'.rodata.IfxMultican_PinMap.IfxMultican_RXD2E_P10_2_IN'
	.global	IfxMultican_RXD2E_P10_2_IN
	.align	4
IfxMultican_RXD2E_P10_2_IN:	.type	object
	.size	IfxMultican_RXD2E_P10_2_IN,20
	.word	-268337152
	.byte	2
	.space	3
	.word	-268193792
	.byte	2
	.space	3
	.byte	4
	.space	3
	.sdecl	'.rodata.IfxMultican_PinMap.IfxMultican_RXD3A_P00_3_IN',data,rom,cluster('IfxMultican_RXD3A_P00_3_IN')
	.sect	'.rodata.IfxMultican_PinMap.IfxMultican_RXD3A_P00_3_IN'
	.global	IfxMultican_RXD3A_P00_3_IN
	.align	4
IfxMultican_RXD3A_P00_3_IN:	.type	object
	.size	IfxMultican_RXD3A_P00_3_IN,20
	.word	-268337152
	.byte	3
	.space	3
	.word	-268197888
	.byte	3
	.space	7
	.sdecl	'.rodata.IfxMultican_PinMap.IfxMultican_RXD3B_P32_2_IN',data,rom,cluster('IfxMultican_RXD3B_P32_2_IN')
	.sect	'.rodata.IfxMultican_PinMap.IfxMultican_RXD3B_P32_2_IN'
	.global	IfxMultican_RXD3B_P32_2_IN
	.align	4
IfxMultican_RXD3B_P32_2_IN:	.type	object
	.size	IfxMultican_RXD3B_P32_2_IN,20
	.word	-268337152
	.byte	3
	.space	3
	.word	-268185088
	.byte	2
	.space	3
	.byte	1
	.space	3
	.sdecl	'.rodata.IfxMultican_PinMap.IfxMultican_RXD3C_P20_0_IN',data,rom,cluster('IfxMultican_RXD3C_P20_0_IN')
	.sect	'.rodata.IfxMultican_PinMap.IfxMultican_RXD3C_P20_0_IN'
	.global	IfxMultican_RXD3C_P20_0_IN
	.align	4
IfxMultican_RXD3C_P20_0_IN:	.type	object
	.size	IfxMultican_RXD3C_P20_0_IN,20
	.word	-268337152
	.byte	3
	.space	3
	.word	-268189696
	.space	4
	.byte	2
	.space	3
	.sdecl	'.rodata.IfxMultican_PinMap.IfxMultican_RXD3D_P11_10_IN',data,rom,cluster('IfxMultican_RXD3D_P11_10_IN')
	.sect	'.rodata.IfxMultican_PinMap.IfxMultican_RXD3D_P11_10_IN'
	.global	IfxMultican_RXD3D_P11_10_IN
	.align	4
IfxMultican_RXD3D_P11_10_IN:	.type	object
	.size	IfxMultican_RXD3D_P11_10_IN,20
	.word	-268337152
	.byte	3
	.space	3
	.word	-268193536
	.byte	10
	.space	3
	.byte	3
	.space	3
	.sdecl	'.rodata.IfxMultican_PinMap.IfxMultican_RXD3E_P20_9_IN',data,rom,cluster('IfxMultican_RXD3E_P20_9_IN')
	.sect	'.rodata.IfxMultican_PinMap.IfxMultican_RXD3E_P20_9_IN'
	.global	IfxMultican_RXD3E_P20_9_IN
	.align	4
IfxMultican_RXD3E_P20_9_IN:	.type	object
	.size	IfxMultican_RXD3E_P20_9_IN,20
	.word	-268337152
	.byte	3
	.space	3
	.word	-268189696
	.byte	9
	.space	3
	.byte	4
	.space	3
	.sdecl	'.rodata.IfxMultican_PinMap.IfxMultican_TXD0_P02_0_OUT',data,rom,cluster('IfxMultican_TXD0_P02_0_OUT')
	.sect	'.rodata.IfxMultican_PinMap.IfxMultican_TXD0_P02_0_OUT'
	.global	IfxMultican_TXD0_P02_0_OUT
	.align	4
IfxMultican_TXD0_P02_0_OUT:	.type	object
	.size	IfxMultican_TXD0_P02_0_OUT,20
	.word	-268337152
	.space	4
	.word	-268197376
	.space	4
	.byte	168
	.space	3
	.sdecl	'.rodata.IfxMultican_PinMap.IfxMultican_TXD0_P02_5_OUT',data,rom,cluster('IfxMultican_TXD0_P02_5_OUT')
	.sect	'.rodata.IfxMultican_PinMap.IfxMultican_TXD0_P02_5_OUT'
	.global	IfxMultican_TXD0_P02_5_OUT
	.align	4
IfxMultican_TXD0_P02_5_OUT:	.type	object
	.size	IfxMultican_TXD0_P02_5_OUT,20
	.word	-268337152
	.space	4
	.word	-268197376
	.byte	5
	.space	3
	.byte	144
	.space	3
	.sdecl	'.rodata.IfxMultican_PinMap.IfxMultican_TXD0_P20_8_OUT',data,rom,cluster('IfxMultican_TXD0_P20_8_OUT')
	.sect	'.rodata.IfxMultican_PinMap.IfxMultican_TXD0_P20_8_OUT'
	.global	IfxMultican_TXD0_P20_8_OUT
	.align	4
IfxMultican_TXD0_P20_8_OUT:	.type	object
	.size	IfxMultican_TXD0_P20_8_OUT,20
	.word	-268337152
	.space	4
	.word	-268189696
	.byte	8
	.space	3
	.byte	168
	.space	3
	.sdecl	'.rodata.IfxMultican_PinMap.IfxMultican_TXD0_P33_8_OUT',data,rom,cluster('IfxMultican_TXD0_P33_8_OUT')
	.sect	'.rodata.IfxMultican_PinMap.IfxMultican_TXD0_P33_8_OUT'
	.global	IfxMultican_TXD0_P33_8_OUT
	.align	4
IfxMultican_TXD0_P33_8_OUT:	.type	object
	.size	IfxMultican_TXD0_P33_8_OUT,20
	.word	-268337152
	.space	4
	.word	-268184832
	.byte	8
	.space	3
	.byte	168
	.space	3
	.sdecl	'.rodata.IfxMultican_PinMap.IfxMultican_TXD1_P00_0_OUT',data,rom,cluster('IfxMultican_TXD1_P00_0_OUT')
	.sect	'.rodata.IfxMultican_PinMap.IfxMultican_TXD1_P00_0_OUT'
	.global	IfxMultican_TXD1_P00_0_OUT
	.align	4
IfxMultican_TXD1_P00_0_OUT:	.type	object
	.size	IfxMultican_TXD1_P00_0_OUT,20
	.word	-268337152
	.byte	1
	.space	3
	.word	-268197888
	.space	4
	.byte	168
	.space	3
	.sdecl	'.rodata.IfxMultican_PinMap.IfxMultican_TXD1_P14_0_OUT',data,rom,cluster('IfxMultican_TXD1_P14_0_OUT')
	.sect	'.rodata.IfxMultican_PinMap.IfxMultican_TXD1_P14_0_OUT'
	.global	IfxMultican_TXD1_P14_0_OUT
	.align	4
IfxMultican_TXD1_P14_0_OUT:	.type	object
	.size	IfxMultican_TXD1_P14_0_OUT,20
	.word	-268337152
	.byte	1
	.space	3
	.word	-268192768
	.space	4
	.byte	168
	.space	3
	.sdecl	'.rodata.IfxMultican_PinMap.IfxMultican_TXD1_P15_2_OUT',data,rom,cluster('IfxMultican_TXD1_P15_2_OUT')
	.sect	'.rodata.IfxMultican_PinMap.IfxMultican_TXD1_P15_2_OUT'
	.global	IfxMultican_TXD1_P15_2_OUT
	.align	4
IfxMultican_TXD1_P15_2_OUT:	.type	object
	.size	IfxMultican_TXD1_P15_2_OUT,20
	.word	-268337152
	.byte	1
	.space	3
	.word	-268192512
	.byte	2
	.space	3
	.byte	168
	.space	3
	.sdecl	'.rodata.IfxMultican_PinMap.IfxMultican_TXD2_P02_2_OUT',data,rom,cluster('IfxMultican_TXD2_P02_2_OUT')
	.sect	'.rodata.IfxMultican_PinMap.IfxMultican_TXD2_P02_2_OUT'
	.global	IfxMultican_TXD2_P02_2_OUT
	.align	4
IfxMultican_TXD2_P02_2_OUT:	.type	object
	.size	IfxMultican_TXD2_P02_2_OUT,20
	.word	-268337152
	.byte	2
	.space	3
	.word	-268197376
	.byte	2
	.space	3
	.byte	168
	.space	3
	.sdecl	'.rodata.IfxMultican_PinMap.IfxMultican_TXD2_P10_3_OUT',data,rom,cluster('IfxMultican_TXD2_P10_3_OUT')
	.sect	'.rodata.IfxMultican_PinMap.IfxMultican_TXD2_P10_3_OUT'
	.global	IfxMultican_TXD2_P10_3_OUT
	.align	4
IfxMultican_TXD2_P10_3_OUT:	.type	object
	.size	IfxMultican_TXD2_P10_3_OUT,20
	.word	-268337152
	.byte	2
	.space	3
	.word	-268193792
	.byte	3
	.space	3
	.byte	176
	.space	3
	.sdecl	'.rodata.IfxMultican_PinMap.IfxMultican_TXD2_P14_10_OUT',data,rom,cluster('IfxMultican_TXD2_P14_10_OUT')
	.sect	'.rodata.IfxMultican_PinMap.IfxMultican_TXD2_P14_10_OUT'
	.global	IfxMultican_TXD2_P14_10_OUT
	.align	4
IfxMultican_TXD2_P14_10_OUT:	.type	object
	.size	IfxMultican_TXD2_P14_10_OUT,20
	.word	-268337152
	.byte	2
	.space	3
	.word	-268192768
	.byte	10
	.space	3
	.byte	168
	.space	3
	.sdecl	'.rodata.IfxMultican_PinMap.IfxMultican_TXD2_P15_0_OUT',data,rom,cluster('IfxMultican_TXD2_P15_0_OUT')
	.sect	'.rodata.IfxMultican_PinMap.IfxMultican_TXD2_P15_0_OUT'
	.global	IfxMultican_TXD2_P15_0_OUT
	.align	4
IfxMultican_TXD2_P15_0_OUT:	.type	object
	.size	IfxMultican_TXD2_P15_0_OUT,20
	.word	-268337152
	.byte	2
	.space	3
	.word	-268192512
	.space	4
	.byte	168
	.space	3
	.sdecl	'.rodata.IfxMultican_PinMap.IfxMultican_TXD3_P00_2_OUT',data,rom,cluster('IfxMultican_TXD3_P00_2_OUT')
	.sect	'.rodata.IfxMultican_PinMap.IfxMultican_TXD3_P00_2_OUT'
	.global	IfxMultican_TXD3_P00_2_OUT
	.align	4
IfxMultican_TXD3_P00_2_OUT:	.type	object
	.size	IfxMultican_TXD3_P00_2_OUT,20
	.word	-268337152
	.byte	3
	.space	3
	.word	-268197888
	.byte	2
	.space	3
	.byte	168
	.space	3
	.sdecl	'.rodata.IfxMultican_PinMap.IfxMultican_TXD3_P11_12_OUT',data,rom,cluster('IfxMultican_TXD3_P11_12_OUT')
	.sect	'.rodata.IfxMultican_PinMap.IfxMultican_TXD3_P11_12_OUT'
	.global	IfxMultican_TXD3_P11_12_OUT
	.align	4
IfxMultican_TXD3_P11_12_OUT:	.type	object
	.size	IfxMultican_TXD3_P11_12_OUT,20
	.word	-268337152
	.byte	3
	.space	3
	.word	-268193536
	.byte	12
	.space	3
	.byte	168
	.space	3
	.sdecl	'.rodata.IfxMultican_PinMap.IfxMultican_TXD3_P20_10_OUT',data,rom,cluster('IfxMultican_TXD3_P20_10_OUT')
	.sect	'.rodata.IfxMultican_PinMap.IfxMultican_TXD3_P20_10_OUT'
	.global	IfxMultican_TXD3_P20_10_OUT
	.align	4
IfxMultican_TXD3_P20_10_OUT:	.type	object
	.size	IfxMultican_TXD3_P20_10_OUT,20
	.word	-268337152
	.byte	3
	.space	3
	.word	-268189696
	.byte	10
	.space	3
	.byte	168
	.space	3
	.sdecl	'.rodata.IfxMultican_PinMap.IfxMultican_TXD3_P20_3_OUT',data,rom,cluster('IfxMultican_TXD3_P20_3_OUT')
	.sect	'.rodata.IfxMultican_PinMap.IfxMultican_TXD3_P20_3_OUT'
	.global	IfxMultican_TXD3_P20_3_OUT
	.align	4
IfxMultican_TXD3_P20_3_OUT:	.type	object
	.size	IfxMultican_TXD3_P20_3_OUT,20
	.word	-268337152
	.byte	3
	.space	3
	.word	-268189696
	.byte	3
	.space	3
	.byte	168
	.space	3
	.sdecl	'.rodata.IfxMultican_PinMap.IfxMultican_TXD3_P32_3_OUT',data,rom,cluster('IfxMultican_TXD3_P32_3_OUT')
	.sect	'.rodata.IfxMultican_PinMap.IfxMultican_TXD3_P32_3_OUT'
	.global	IfxMultican_TXD3_P32_3_OUT
	.align	4
IfxMultican_TXD3_P32_3_OUT:	.type	object
	.size	IfxMultican_TXD3_P32_3_OUT,20
	.word	-268337152
	.byte	3
	.space	3
	.word	-268185088
	.byte	3
	.space	3
	.byte	168
	.space	3
	.sdecl	'.data.IfxMultican_PinMap.IfxMultican_Rxd_In_pinTable',data,cluster('IfxMultican_Rxd_In_pinTable')
	.sect	'.data.IfxMultican_PinMap.IfxMultican_Rxd_In_pinTable'
	.global	IfxMultican_Rxd_In_pinTable
	.align	4
IfxMultican_Rxd_In_pinTable:	.type	object
	.size	IfxMultican_Rxd_In_pinTable,80
	.word	IfxMultican_RXD0A_P02_1_IN,IfxMultican_RXD0B_P20_7_IN
	.space	4
	.word	IfxMultican_RXD0D_P02_4_IN,IfxMultican_RXD0E_P33_7_IN,IfxMultican_RXD1A_P15_3_IN,IfxMultican_RXD1B_P14_1_IN
	.space	4
	.word	IfxMultican_RXD1D_P00_1_IN
	.space	4
	.word	IfxMultican_RXD2A_P15_1_IN,IfxMultican_RXD2B_P02_3_IN
	.space	4
	.word	IfxMultican_RXD2D_P14_8_IN,IfxMultican_RXD2E_P10_2_IN,IfxMultican_RXD3A_P00_3_IN,IfxMultican_RXD3B_P32_2_IN
	.word	IfxMultican_RXD3C_P20_0_IN,IfxMultican_RXD3D_P11_10_IN,IfxMultican_RXD3E_P20_9_IN
	.sdecl	'.data.IfxMultican_PinMap.IfxMultican_Txd_Out_pinTable',data,cluster('IfxMultican_Txd_Out_pinTable')
	.sect	'.data.IfxMultican_PinMap.IfxMultican_Txd_Out_pinTable'
	.global	IfxMultican_Txd_Out_pinTable
	.align	4
IfxMultican_Txd_Out_pinTable:	.type	object
	.size	IfxMultican_Txd_Out_pinTable,80
	.word	IfxMultican_TXD0_P02_0_OUT,IfxMultican_TXD0_P02_5_OUT,IfxMultican_TXD0_P20_8_OUT,IfxMultican_TXD0_P33_8_OUT
	.space	4
	.word	IfxMultican_TXD1_P00_0_OUT,IfxMultican_TXD1_P14_0_OUT,IfxMultican_TXD1_P15_2_OUT
	.space	8
	.word	IfxMultican_TXD2_P02_2_OUT,IfxMultican_TXD2_P10_3_OUT,IfxMultican_TXD2_P14_10_OUT,IfxMultican_TXD2_P15_0_OUT
	.space	4
	.word	IfxMultican_TXD3_P00_2_OUT,IfxMultican_TXD3_P11_12_OUT,IfxMultican_TXD3_P20_3_OUT,IfxMultican_TXD3_P20_10_OUT
	.word	IfxMultican_TXD3_P32_3_OUT
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L3:
	.word	49049
	.half	3
	.word	.L4
	.byte	4
.L2:
	.byte	1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMultican_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L5
	.byte	2,1,1,3
	.word	242
	.byte	4
	.byte	'Ifx__jump_and_link',0,3,1,61,17,1,1,5
	.byte	'fun',0,1,61,43
	.word	245
	.byte	6,0,7
	.byte	'__fract',0,4,128,1,7
	.byte	'float',0,4,4,8
	.byte	'Ifx__float_to_fract',0,3,1,152,2,18
	.word	290
	.byte	1,1,5
	.byte	'a',0,1,152,2,44
	.word	302
	.byte	6,0,7
	.byte	'unsigned long long int',0,8,7,9
	.byte	'void',0,3
	.word	382
	.byte	8
	.byte	'__ld64',0,3,2,135,1,19
	.word	356
	.byte	1,1,5
	.byte	'addr',0,2,135,1,32
	.word	388
	.byte	6,0,4
	.byte	'__st64',0,3,2,143,1,17,1,1,5
	.byte	'addr',0,2,143,1,30
	.word	388
	.byte	5
	.byte	'value',0,2,143,1,43
	.word	356
	.byte	6,0,7
	.byte	'unsigned int',0,4,7,7
	.byte	'int',0,4,5,7
	.byte	'unsigned char',0,1,8,7
	.byte	'unsigned short int',0,2,7,10
	.byte	'_Ifx_P_OUT_Bits',0,4,143,3,16,4,11
	.byte	'P0',0,1
	.word	497
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	497
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	497
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	497
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	497
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	497
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	497
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	497
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	497
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	497
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	497
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	497
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	497
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	497
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	497
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	497
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	514
	.byte	16,0,2,35,2,0,12,4,181,5,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	536
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMR_Bits',0,4,169,2,16,4,11
	.byte	'PS0',0,1
	.word	497
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	497
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	497
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	497
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	497
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	497
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	497
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	497
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	497
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	497
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	497
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	497
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	497
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	497
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	497
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	497
	.byte	1,0,2,35,1,11
	.byte	'PCL0',0,1
	.word	497
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	497
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	497
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	497
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	497
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	497
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	497
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	497
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	497
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	497
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	497
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	497
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	497
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	497
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	497
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	497
	.byte	1,0,2,35,3,0,12,4,133,5,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	852
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ID_Bits',0,4,110,16,4,11
	.byte	'MODREV',0,1
	.word	497
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	497
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	514
	.byte	16,0,2,35,2,0,12,4,148,4,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1423
	.byte	4,2,35,0,0,14,4
	.word	497
	.byte	15,3,0,10
	.byte	'_Ifx_P_IOCR0_Bits',0,4,140,1,16,4,11
	.byte	'reserved_0',0,1
	.word	497
	.byte	3,5,2,35,0,11
	.byte	'PC0',0,1
	.word	497
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	497
	.byte	3,5,2,35,1,11
	.byte	'PC1',0,1
	.word	497
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	497
	.byte	3,5,2,35,2,11
	.byte	'PC2',0,1
	.word	497
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	497
	.byte	3,5,2,35,3,11
	.byte	'PC3',0,1
	.word	497
	.byte	5,0,2,35,3,0,12,4,164,4,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1551
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR4_Bits',0,4,166,1,16,4,11
	.byte	'reserved_0',0,1
	.word	497
	.byte	3,5,2,35,0,11
	.byte	'PC4',0,1
	.word	497
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	497
	.byte	3,5,2,35,1,11
	.byte	'PC5',0,1
	.word	497
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	497
	.byte	3,5,2,35,2,11
	.byte	'PC6',0,1
	.word	497
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	497
	.byte	3,5,2,35,3,11
	.byte	'PC7',0,1
	.word	497
	.byte	5,0,2,35,3,0,12,4,180,4,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1766
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR8_Bits',0,4,179,1,16,4,11
	.byte	'reserved_0',0,1
	.word	497
	.byte	3,5,2,35,0,11
	.byte	'PC8',0,1
	.word	497
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	497
	.byte	3,5,2,35,1,11
	.byte	'PC9',0,1
	.word	497
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	497
	.byte	3,5,2,35,2,11
	.byte	'PC10',0,1
	.word	497
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	497
	.byte	3,5,2,35,3,11
	.byte	'PC11',0,1
	.word	497
	.byte	5,0,2,35,3,0,12,4,188,4,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1981
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR12_Bits',0,4,153,1,16,4,11
	.byte	'reserved_0',0,1
	.word	497
	.byte	3,5,2,35,0,11
	.byte	'PC12',0,1
	.word	497
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	497
	.byte	3,5,2,35,1,11
	.byte	'PC13',0,1
	.word	497
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	497
	.byte	3,5,2,35,2,11
	.byte	'PC14',0,1
	.word	497
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	497
	.byte	3,5,2,35,3,11
	.byte	'PC15',0,1
	.word	497
	.byte	5,0,2,35,3,0,12,4,172,4,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2198
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IN_Bits',0,4,118,16,4,11
	.byte	'P0',0,1
	.word	497
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	497
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	497
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	497
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	497
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	497
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	497
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	497
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	497
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	497
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	497
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	497
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	497
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	497
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	497
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	497
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	514
	.byte	16,0,2,35,2,0,12,4,156,4,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2418
	.byte	4,2,35,0,0,14,24
	.word	497
	.byte	15,23,0,10
	.byte	'_Ifx_P_PDR0_Bits',0,4,205,3,16,4,11
	.byte	'PD0',0,1
	.word	497
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	497
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	497
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	497
	.byte	1,0,2,35,0,11
	.byte	'PD2',0,1
	.word	497
	.byte	3,5,2,35,1,11
	.byte	'PL2',0,1
	.word	497
	.byte	1,4,2,35,1,11
	.byte	'PD3',0,1
	.word	497
	.byte	3,1,2,35,1,11
	.byte	'PL3',0,1
	.word	497
	.byte	1,0,2,35,1,11
	.byte	'PD4',0,1
	.word	497
	.byte	3,5,2,35,2,11
	.byte	'PL4',0,1
	.word	497
	.byte	1,4,2,35,2,11
	.byte	'PD5',0,1
	.word	497
	.byte	3,1,2,35,2,11
	.byte	'PL5',0,1
	.word	497
	.byte	1,0,2,35,2,11
	.byte	'PD6',0,1
	.word	497
	.byte	3,5,2,35,3,11
	.byte	'PL6',0,1
	.word	497
	.byte	1,4,2,35,3,11
	.byte	'PD7',0,1
	.word	497
	.byte	3,1,2,35,3,11
	.byte	'PL7',0,1
	.word	497
	.byte	1,0,2,35,3,0,12,4,205,5,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2741
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PDR1_Bits',0,4,226,3,16,4,11
	.byte	'PD8',0,1
	.word	497
	.byte	3,5,2,35,0,11
	.byte	'PL8',0,1
	.word	497
	.byte	1,4,2,35,0,11
	.byte	'PD9',0,1
	.word	497
	.byte	3,1,2,35,0,11
	.byte	'PL9',0,1
	.word	497
	.byte	1,0,2,35,0,11
	.byte	'PD10',0,1
	.word	497
	.byte	3,5,2,35,1,11
	.byte	'PL10',0,1
	.word	497
	.byte	1,4,2,35,1,11
	.byte	'PD11',0,1
	.word	497
	.byte	3,1,2,35,1,11
	.byte	'PL11',0,1
	.word	497
	.byte	1,0,2,35,1,11
	.byte	'PD12',0,1
	.word	497
	.byte	3,5,2,35,2,11
	.byte	'PL12',0,1
	.word	497
	.byte	1,4,2,35,2,11
	.byte	'PD13',0,1
	.word	497
	.byte	3,1,2,35,2,11
	.byte	'PL13',0,1
	.word	497
	.byte	1,0,2,35,2,11
	.byte	'PD14',0,1
	.word	497
	.byte	3,5,2,35,3,11
	.byte	'PL14',0,1
	.word	497
	.byte	1,4,2,35,3,11
	.byte	'PD15',0,1
	.word	497
	.byte	3,1,2,35,3,11
	.byte	'PL15',0,1
	.word	497
	.byte	1,0,2,35,3,0,12,4,213,5,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3045
	.byte	4,2,35,0,0,14,8
	.word	497
	.byte	15,7,0,10
	.byte	'_Ifx_P_ESR_Bits',0,4,88,16,4,11
	.byte	'EN0',0,1
	.word	497
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	497
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	497
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	497
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	497
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	497
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	497
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	497
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	497
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	497
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	497
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	497
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	497
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	497
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	497
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	497
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	514
	.byte	16,0,2,35,2,0,12,4,140,4,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3370
	.byte	4,2,35,0,0,14,12
	.word	497
	.byte	15,11,0,10
	.byte	'_Ifx_P_PDISC_Bits',0,4,183,3,16,4,11
	.byte	'PDIS0',0,1
	.word	497
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	497
	.byte	1,6,2,35,0,11
	.byte	'PDIS2',0,1
	.word	497
	.byte	1,5,2,35,0,11
	.byte	'PDIS3',0,1
	.word	497
	.byte	1,4,2,35,0,11
	.byte	'PDIS4',0,1
	.word	497
	.byte	1,3,2,35,0,11
	.byte	'PDIS5',0,1
	.word	497
	.byte	1,2,2,35,0,11
	.byte	'PDIS6',0,1
	.word	497
	.byte	1,1,2,35,0,11
	.byte	'PDIS7',0,1
	.word	497
	.byte	1,0,2,35,0,11
	.byte	'PDIS8',0,1
	.word	497
	.byte	1,7,2,35,1,11
	.byte	'PDIS9',0,1
	.word	497
	.byte	1,6,2,35,1,11
	.byte	'PDIS10',0,1
	.word	497
	.byte	1,5,2,35,1,11
	.byte	'PDIS11',0,1
	.word	497
	.byte	1,4,2,35,1,11
	.byte	'PDIS12',0,1
	.word	497
	.byte	1,3,2,35,1,11
	.byte	'PDIS13',0,1
	.word	497
	.byte	1,2,2,35,1,11
	.byte	'PDIS14',0,1
	.word	497
	.byte	1,1,2,35,1,11
	.byte	'PDIS15',0,1
	.word	497
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	514
	.byte	16,0,2,35,2,0,12,4,197,5,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3710
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PCSR_Bits',0,4,165,3,16,4,11
	.byte	'SEL0',0,1
	.word	497
	.byte	1,7,2,35,0,11
	.byte	'SEL1',0,1
	.word	497
	.byte	1,6,2,35,0,11
	.byte	'SEL2',0,1
	.word	497
	.byte	1,5,2,35,0,11
	.byte	'SEL3',0,1
	.word	497
	.byte	1,4,2,35,0,11
	.byte	'SEL4',0,1
	.word	497
	.byte	1,3,2,35,0,11
	.byte	'SEL5',0,1
	.word	497
	.byte	1,2,2,35,0,11
	.byte	'SEL6',0,1
	.word	497
	.byte	1,1,2,35,0,11
	.byte	'SEL7',0,1
	.word	497
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	497
	.byte	2,6,2,35,1,11
	.byte	'SEL10',0,1
	.word	497
	.byte	1,5,2,35,1,11
	.byte	'SEL11',0,1
	.word	497
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	474
	.byte	19,1,2,35,0,11
	.byte	'LCK',0,1
	.word	497
	.byte	1,0,2,35,3,0,12,4,189,5,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4076
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR0_Bits',0,4,206,2,16,4,11
	.byte	'PS0',0,1
	.word	497
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	497
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	497
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	497
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	474
	.byte	28,0,2,35,0,0,12,4,149,5,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4362
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR4_Bits',0,4,227,2,16,4,11
	.byte	'reserved_0',0,1
	.word	497
	.byte	4,4,2,35,0,11
	.byte	'PS4',0,1
	.word	497
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	497
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	497
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	497
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	474
	.byte	24,0,2,35,0,0,12,4,165,5,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4509
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR8_Bits',0,4,238,2,16,4,11
	.byte	'reserved_0',0,1
	.word	497
	.byte	8,0,2,35,0,11
	.byte	'PS8',0,1
	.word	497
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	497
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	497
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	497
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	474
	.byte	20,0,2,35,0,0,12,4,173,5,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4678
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR12_Bits',0,4,216,2,16,4,11
	.byte	'reserved_0',0,2
	.word	514
	.byte	12,4,2,35,0,11
	.byte	'PS12',0,1
	.word	497
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	497
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	497
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	497
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	514
	.byte	16,0,2,35,2,0,12,4,157,5,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4850
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR0_Bits',0,4,232,1,16,4,11
	.byte	'reserved_0',0,2
	.word	514
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	497
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	497
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	497
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	497
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	514
	.byte	12,0,2,35,2,0,12,4,229,4,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5025
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR4_Bits',0,4,253,1,16,4,11
	.byte	'reserved_0',0,4
	.word	474
	.byte	20,12,2,35,0,11
	.byte	'PCL4',0,1
	.word	497
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	497
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	497
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	497
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	497
	.byte	8,0,2,35,3,0,12,4,245,4,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5199
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR8_Bits',0,4,136,2,16,4,11
	.byte	'reserved_0',0,4
	.word	474
	.byte	24,8,2,35,0,11
	.byte	'PCL8',0,1
	.word	497
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	497
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	497
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	497
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	497
	.byte	4,0,2,35,3,0,12,4,253,4,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5373
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR12_Bits',0,4,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	474
	.byte	28,4,2,35,0,11
	.byte	'PCL12',0,1
	.word	497
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	497
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	497
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	497
	.byte	1,0,2,35,3,0,12,4,237,4,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5549
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR_Bits',0,4,249,2,16,4,11
	.byte	'PS0',0,1
	.word	497
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	497
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	497
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	497
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	497
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	497
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	497
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	497
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	497
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	497
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	497
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	497
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	497
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	497
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	497
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	497
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	514
	.byte	16,0,2,35,2,0,12,4,141,5,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5705
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR_Bits',0,4,147,2,16,4,11
	.byte	'reserved_0',0,2
	.word	514
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	497
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	497
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	497
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	497
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	497
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	497
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	497
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	497
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	497
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	497
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	497
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	497
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	497
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	497
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	497
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	497
	.byte	1,0,2,35,3,0,12,4,221,4,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6038
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR0_Bits',0,4,192,1,16,4,11
	.byte	'reserved_0',0,1
	.word	497
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	497
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	474
	.byte	30,0,2,35,0,0,12,4,196,4,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6386
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_Bits',0,4,200,1,16,4,11
	.byte	'reserved_0',0,1
	.word	497
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	497
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	474
	.byte	30,0,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_P21_Bits',0,4,208,1,16,4,11
	.byte	'RDIS_CTRL',0,1
	.word	497
	.byte	1,7,2,35,0,11
	.byte	'RX_DIS',0,1
	.word	497
	.byte	1,6,2,35,0,11
	.byte	'TERM',0,1
	.word	497
	.byte	1,5,2,35,0,11
	.byte	'LRXTERM',0,1
	.word	497
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	474
	.byte	24,0,2,35,0,0,12,4,204,4,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6510
	.byte	4,2,35,0,13
	.byte	'B_P21',0
	.word	6594
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR2_Bits',0,4,218,1,16,4,11
	.byte	'reserved_0',0,1
	.word	497
	.byte	8,0,2,35,0,11
	.byte	'LVDSR',0,1
	.word	497
	.byte	1,7,2,35,1,11
	.byte	'LVDSRL',0,1
	.word	497
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	497
	.byte	2,4,2,35,1,11
	.byte	'TDIS_CTRL',0,1
	.word	497
	.byte	1,3,2,35,1,11
	.byte	'TX_DIS',0,1
	.word	497
	.byte	1,2,2,35,1,11
	.byte	'TX_PD',0,1
	.word	497
	.byte	1,1,2,35,1,11
	.byte	'TX_PWDPD',0,1
	.word	497
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	514
	.byte	16,0,2,35,2,0,12,4,213,4,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6774
	.byte	4,2,35,0,0,14,76
	.word	497
	.byte	15,75,0,10
	.byte	'_Ifx_P_ACCEN1_Bits',0,4,82,16,4,11
	.byte	'reserved_0',0,4
	.word	474
	.byte	32,0,2,35,0,0,12,4,132,4,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7027
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ACCEN0_Bits',0,4,45,16,4,11
	.byte	'EN0',0,1
	.word	497
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	497
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	497
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	497
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	497
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	497
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	497
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	497
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	497
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	497
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	497
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	497
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	497
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	497
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	497
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	497
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	497
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	497
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	497
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	497
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	497
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	497
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	497
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	497
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	497
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	497
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	497
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	497
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	497
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	497
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	497
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	497
	.byte	1,0,2,35,3,0,12,4,252,3,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7114
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P',0,4,229,5,25,128,2,13
	.byte	'OUT',0
	.word	812
	.byte	4,2,35,0,13
	.byte	'OMR',0
	.word	1383
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	1502
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	1542
	.byte	4,2,35,12,13
	.byte	'IOCR0',0
	.word	1726
	.byte	4,2,35,16,13
	.byte	'IOCR4',0
	.word	1941
	.byte	4,2,35,20,13
	.byte	'IOCR8',0
	.word	2158
	.byte	4,2,35,24,13
	.byte	'IOCR12',0
	.word	2378
	.byte	4,2,35,28,13
	.byte	'reserved_20',0
	.word	1542
	.byte	4,2,35,32,13
	.byte	'IN',0
	.word	2692
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	2732
	.byte	24,2,35,40,13
	.byte	'PDR0',0
	.word	3005
	.byte	4,2,35,64,13
	.byte	'PDR1',0
	.word	3321
	.byte	4,2,35,68,13
	.byte	'reserved_48',0
	.word	3361
	.byte	8,2,35,72,13
	.byte	'ESR',0
	.word	3661
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	3701
	.byte	12,2,35,84,13
	.byte	'PDISC',0
	.word	4036
	.byte	4,2,35,96,13
	.byte	'PCSR',0
	.word	4322
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	3361
	.byte	8,2,35,104,13
	.byte	'OMSR0',0
	.word	4469
	.byte	4,2,35,112,13
	.byte	'OMSR4',0
	.word	4638
	.byte	4,2,35,116,13
	.byte	'OMSR8',0
	.word	4810
	.byte	4,2,35,120,13
	.byte	'OMSR12',0
	.word	4985
	.byte	4,2,35,124,13
	.byte	'OMCR0',0
	.word	5159
	.byte	4,3,35,128,1,13
	.byte	'OMCR4',0
	.word	5333
	.byte	4,3,35,132,1,13
	.byte	'OMCR8',0
	.word	5509
	.byte	4,3,35,136,1,13
	.byte	'OMCR12',0
	.word	5665
	.byte	4,3,35,140,1,13
	.byte	'OMSR',0
	.word	5998
	.byte	4,3,35,144,1,13
	.byte	'OMCR',0
	.word	6346
	.byte	4,3,35,148,1,13
	.byte	'reserved_98',0
	.word	3361
	.byte	8,3,35,152,1,13
	.byte	'LPCR0',0
	.word	6470
	.byte	4,3,35,160,1,13
	.byte	'LPCR1',0
	.word	6719
	.byte	4,3,35,164,1,13
	.byte	'LPCR2',0
	.word	6978
	.byte	4,3,35,168,1,13
	.byte	'reserved_A4',0
	.word	7018
	.byte	76,3,35,172,1,13
	.byte	'ACCEN1',0
	.word	7074
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	7641
	.byte	4,3,35,252,1,0,16
	.word	7681
	.byte	3
	.word	8284
	.byte	17,3,172,1,9,4,18
	.byte	'IfxPort_State_notChanged',0,0,18
	.byte	'IfxPort_State_high',0,1,18
	.byte	'IfxPort_State_low',0,128,128,4,18
	.byte	'IfxPort_State_toggled',0,129,128,4,0,4
	.byte	'IfxPort_setPinState',0,3,3,208,4,17,1,1,5
	.byte	'port',0,3,208,4,44
	.word	8289
	.byte	5
	.byte	'pinIndex',0,3,208,4,56
	.word	497
	.byte	5
	.byte	'action',0,3,208,4,80
	.word	8294
	.byte	6,0,19
	.word	250
	.byte	20
	.word	276
	.byte	6,0,19
	.word	311
	.byte	20
	.word	343
	.byte	6,0,19
	.word	393
	.byte	20
	.word	412
	.byte	6,0,19
	.word	428
	.byte	20
	.word	443
	.byte	20
	.word	457
	.byte	6,0,19
	.word	8397
	.byte	20
	.word	8425
	.byte	20
	.word	8439
	.byte	20
	.word	8457
	.byte	6,0,7
	.byte	'short int',0,2,5,21
	.byte	'__wchar_t',0,5,1,1
	.word	8550
	.byte	21
	.byte	'__size_t',0,5,1,1
	.word	474
	.byte	21
	.byte	'__ptrdiff_t',0,5,1,1
	.word	490
	.byte	22,1,3
	.word	8618
	.byte	21
	.byte	'__codeptr',0,5,1,1
	.word	8620
	.byte	10
	.byte	'_Ifx_CAN_ACCEN0_Bits',0,6,49,16,4,11
	.byte	'EN0',0,1
	.word	497
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	497
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	497
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	497
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	497
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	497
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	497
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	497
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	497
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	497
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	497
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	497
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	497
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	497
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	497
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	497
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	497
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	497
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	497
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	497
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	497
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	497
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	497
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	497
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	497
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	497
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	497
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	497
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	497
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	497
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	497
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	497
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_CAN_ACCEN0_Bits',0,6,83,3
	.word	8643
	.byte	10
	.byte	'_Ifx_CAN_ACCEN1_Bits',0,6,86,16,4,11
	.byte	'reserved_0',0,4
	.word	474
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_CAN_ACCEN1_Bits',0,6,89,3
	.word	9200
	.byte	10
	.byte	'_Ifx_CAN_CLC_Bits',0,6,92,16,4,11
	.byte	'DISR',0,1
	.word	497
	.byte	1,7,2,35,0,11
	.byte	'DISS',0,1
	.word	497
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	497
	.byte	1,5,2,35,0,11
	.byte	'EDIS',0,1
	.word	497
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	474
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_CAN_CLC_Bits',0,6,99,3
	.word	9277
	.byte	10
	.byte	'_Ifx_CAN_FDR_Bits',0,6,102,16,4,11
	.byte	'STEP',0,2
	.word	514
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	497
	.byte	4,2,2,35,1,11
	.byte	'DM',0,1
	.word	497
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	514
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_CAN_FDR_Bits',0,6,108,3
	.word	9418
	.byte	10
	.byte	'_Ifx_CAN_ID_Bits',0,6,111,16,4,11
	.byte	'MODREV',0,1
	.word	497
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	497
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	514
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_CAN_ID_Bits',0,6,116,3
	.word	9543
	.byte	10
	.byte	'_Ifx_CAN_KRST0_Bits',0,6,119,16,4,11
	.byte	'RST',0,1
	.word	497
	.byte	1,7,2,35,0,11
	.byte	'RSTSTAT',0,1
	.word	497
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	474
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_CAN_KRST0_Bits',0,6,124,3
	.word	9648
	.byte	10
	.byte	'_Ifx_CAN_KRST1_Bits',0,6,127,16,4,11
	.byte	'RST',0,1
	.word	497
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	474
	.byte	31,0,2,35,0,0,21
	.byte	'Ifx_CAN_KRST1_Bits',0,6,131,1,3
	.word	9757
	.byte	10
	.byte	'_Ifx_CAN_KRSTCLR_Bits',0,6,134,1,16,4,11
	.byte	'CLR',0,1
	.word	497
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	474
	.byte	31,0,2,35,0,0,21
	.byte	'Ifx_CAN_KRSTCLR_Bits',0,6,138,1,3
	.word	9848
	.byte	10
	.byte	'_Ifx_CAN_LIST_Bits',0,6,141,1,16,4,11
	.byte	'BEGIN',0,1
	.word	497
	.byte	8,0,2,35,0,11
	.byte	'END',0,1
	.word	497
	.byte	8,0,2,35,1,11
	.byte	'SIZE',0,1
	.word	497
	.byte	8,0,2,35,2,11
	.byte	'EMPTY',0,1
	.word	497
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	497
	.byte	7,0,2,35,3,0,21
	.byte	'Ifx_CAN_LIST_Bits',0,6,148,1,3
	.word	9944
	.byte	10
	.byte	'_Ifx_CAN_MCR_Bits',0,6,151,1,16,4,11
	.byte	'CLKSEL',0,1
	.word	497
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	514
	.byte	8,4,2,35,0,11
	.byte	'MPSEL',0,1
	.word	497
	.byte	4,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	514
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_CAN_MCR_Bits',0,6,157,1,3
	.word	10085
	.byte	10
	.byte	'_Ifx_CAN_MECR_Bits',0,6,160,1,16,4,11
	.byte	'TH',0,2
	.word	514
	.byte	16,0,2,35,0,11
	.byte	'INP',0,1
	.word	497
	.byte	4,4,2,35,2,11
	.byte	'NODE',0,1
	.word	497
	.byte	3,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	497
	.byte	1,0,2,35,2,11
	.byte	'ANYED',0,1
	.word	497
	.byte	1,7,2,35,3,11
	.byte	'CAPEIE',0,1
	.word	497
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	497
	.byte	1,5,2,35,3,11
	.byte	'DEPTH',0,1
	.word	497
	.byte	3,2,2,35,3,11
	.byte	'SOF',0,1
	.word	497
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	497
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_CAN_MECR_Bits',0,6,172,1,3
	.word	10216
	.byte	10
	.byte	'_Ifx_CAN_MESTAT_Bits',0,6,175,1,16,4,11
	.byte	'CAPT',0,2
	.word	514
	.byte	16,0,2,35,0,11
	.byte	'CAPRED',0,1
	.word	497
	.byte	1,7,2,35,2,11
	.byte	'CAPE',0,1
	.word	497
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	514
	.byte	14,0,2,35,2,0,21
	.byte	'Ifx_CAN_MESTAT_Bits',0,6,181,1,3
	.word	10450
	.byte	10
	.byte	'_Ifx_CAN_MITR_Bits',0,6,184,1,16,4,11
	.byte	'IT',0,2
	.word	514
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	514
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_CAN_MITR_Bits',0,6,188,1,3
	.word	10580
	.byte	10
	.byte	'_Ifx_CAN_MO_AMR_Bits',0,6,191,1,16,4,11
	.byte	'AM',0,4
	.word	474
	.byte	29,3,2,35,0,11
	.byte	'MIDE',0,1
	.word	497
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	497
	.byte	2,0,2,35,3,0,21
	.byte	'Ifx_CAN_MO_AMR_Bits',0,6,196,1,3
	.word	10670
	.byte	10
	.byte	'_Ifx_CAN_MO_AR_Bits',0,6,199,1,16,4,11
	.byte	'ID',0,4
	.word	474
	.byte	29,3,2,35,0,11
	.byte	'IDE',0,1
	.word	497
	.byte	1,2,2,35,3,11
	.byte	'PRI',0,1
	.word	497
	.byte	2,0,2,35,3,0,21
	.byte	'Ifx_CAN_MO_AR_Bits',0,6,204,1,3
	.word	10780
	.byte	10
	.byte	'_Ifx_CAN_MO_CTR_Bits',0,6,207,1,16,4,11
	.byte	'RESRXPND',0,1
	.word	497
	.byte	1,7,2,35,0,11
	.byte	'RESTXPND',0,1
	.word	497
	.byte	1,6,2,35,0,11
	.byte	'RESRXUPD',0,1
	.word	497
	.byte	1,5,2,35,0,11
	.byte	'RESNEWDAT',0,1
	.word	497
	.byte	1,4,2,35,0,11
	.byte	'RESMSGLST',0,1
	.word	497
	.byte	1,3,2,35,0,11
	.byte	'RESMSGVAL',0,1
	.word	497
	.byte	1,2,2,35,0,11
	.byte	'RESRTSEL',0,1
	.word	497
	.byte	1,1,2,35,0,11
	.byte	'RESRXEN',0,1
	.word	497
	.byte	1,0,2,35,0,11
	.byte	'RESTXRQ',0,1
	.word	497
	.byte	1,7,2,35,1,11
	.byte	'RESTXEN0',0,1
	.word	497
	.byte	1,6,2,35,1,11
	.byte	'RESTXEN1',0,1
	.word	497
	.byte	1,5,2,35,1,11
	.byte	'RESDIR',0,1
	.word	497
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	497
	.byte	4,0,2,35,1,11
	.byte	'SETRXPND',0,1
	.word	497
	.byte	1,7,2,35,2,11
	.byte	'SETTXPND',0,1
	.word	497
	.byte	1,6,2,35,2,11
	.byte	'SETRXUPD',0,1
	.word	497
	.byte	1,5,2,35,2,11
	.byte	'SETNEWDAT',0,1
	.word	497
	.byte	1,4,2,35,2,11
	.byte	'SETMSGLST',0,1
	.word	497
	.byte	1,3,2,35,2,11
	.byte	'SETMSGVAL',0,1
	.word	497
	.byte	1,2,2,35,2,11
	.byte	'SETRTSEL',0,1
	.word	497
	.byte	1,1,2,35,2,11
	.byte	'SETRXEN',0,1
	.word	497
	.byte	1,0,2,35,2,11
	.byte	'SETTXRQ',0,1
	.word	497
	.byte	1,7,2,35,3,11
	.byte	'SETTXEN0',0,1
	.word	497
	.byte	1,6,2,35,3,11
	.byte	'SETTXEN1',0,1
	.word	497
	.byte	1,5,2,35,3,11
	.byte	'SETDIR',0,1
	.word	497
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	497
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_CAN_MO_CTR_Bits',0,6,235,1,3
	.word	10879
	.byte	10
	.byte	'_Ifx_CAN_MO_DATAH_Bits',0,6,238,1,16,4,11
	.byte	'DB4',0,1
	.word	497
	.byte	8,0,2,35,0,11
	.byte	'DB5',0,1
	.word	497
	.byte	8,0,2,35,1,11
	.byte	'DB6',0,1
	.word	497
	.byte	8,0,2,35,2,11
	.byte	'DB7',0,1
	.word	497
	.byte	8,0,2,35,3,0,21
	.byte	'Ifx_CAN_MO_DATAH_Bits',0,6,244,1,3
	.word	11460
	.byte	10
	.byte	'_Ifx_CAN_MO_DATAL_Bits',0,6,247,1,16,4,11
	.byte	'DB0',0,1
	.word	497
	.byte	8,0,2,35,0,11
	.byte	'DB1',0,1
	.word	497
	.byte	8,0,2,35,1,11
	.byte	'DB2',0,1
	.word	497
	.byte	8,0,2,35,2,11
	.byte	'DB3',0,1
	.word	497
	.byte	8,0,2,35,3,0,21
	.byte	'Ifx_CAN_MO_DATAL_Bits',0,6,253,1,3
	.word	11581
	.byte	10
	.byte	'_Ifx_CAN_MO_EDATA0_Bits',0,6,128,2,16,4,11
	.byte	'DB0',0,1
	.word	497
	.byte	8,0,2,35,0,11
	.byte	'DB1',0,1
	.word	497
	.byte	8,0,2,35,1,11
	.byte	'DB2',0,1
	.word	497
	.byte	8,0,2,35,2,11
	.byte	'DB3',0,1
	.word	497
	.byte	8,0,2,35,3,0,21
	.byte	'Ifx_CAN_MO_EDATA0_Bits',0,6,134,2,3
	.word	11702
	.byte	10
	.byte	'_Ifx_CAN_MO_EDATA1_Bits',0,6,137,2,16,4,11
	.byte	'DB0',0,1
	.word	497
	.byte	8,0,2,35,0,11
	.byte	'DB1',0,1
	.word	497
	.byte	8,0,2,35,1,11
	.byte	'DB2',0,1
	.word	497
	.byte	8,0,2,35,2,11
	.byte	'DB3',0,1
	.word	497
	.byte	8,0,2,35,3,0,21
	.byte	'Ifx_CAN_MO_EDATA1_Bits',0,6,143,2,3
	.word	11825
	.byte	10
	.byte	'_Ifx_CAN_MO_EDATA2_Bits',0,6,146,2,16,4,11
	.byte	'DB0',0,1
	.word	497
	.byte	8,0,2,35,0,11
	.byte	'DB1',0,1
	.word	497
	.byte	8,0,2,35,1,11
	.byte	'DB2',0,1
	.word	497
	.byte	8,0,2,35,2,11
	.byte	'DB3',0,1
	.word	497
	.byte	8,0,2,35,3,0,21
	.byte	'Ifx_CAN_MO_EDATA2_Bits',0,6,152,2,3
	.word	11948
	.byte	10
	.byte	'_Ifx_CAN_MO_EDATA3_Bits',0,6,155,2,16,4,11
	.byte	'DB0',0,1
	.word	497
	.byte	8,0,2,35,0,11
	.byte	'DB1',0,1
	.word	497
	.byte	8,0,2,35,1,11
	.byte	'DB2',0,1
	.word	497
	.byte	8,0,2,35,2,11
	.byte	'DB3',0,1
	.word	497
	.byte	8,0,2,35,3,0,21
	.byte	'Ifx_CAN_MO_EDATA3_Bits',0,6,161,2,3
	.word	12071
	.byte	10
	.byte	'_Ifx_CAN_MO_EDATA4_Bits',0,6,164,2,16,4,11
	.byte	'DB0',0,1
	.word	497
	.byte	8,0,2,35,0,11
	.byte	'DB1',0,1
	.word	497
	.byte	8,0,2,35,1,11
	.byte	'DB2',0,1
	.word	497
	.byte	8,0,2,35,2,11
	.byte	'DB3',0,1
	.word	497
	.byte	8,0,2,35,3,0,21
	.byte	'Ifx_CAN_MO_EDATA4_Bits',0,6,170,2,3
	.word	12194
	.byte	10
	.byte	'_Ifx_CAN_MO_EDATA5_Bits',0,6,173,2,16,4,11
	.byte	'DB0',0,1
	.word	497
	.byte	8,0,2,35,0,11
	.byte	'DB1',0,1
	.word	497
	.byte	8,0,2,35,1,11
	.byte	'DB2',0,1
	.word	497
	.byte	8,0,2,35,2,11
	.byte	'DB3',0,1
	.word	497
	.byte	8,0,2,35,3,0,21
	.byte	'Ifx_CAN_MO_EDATA5_Bits',0,6,179,2,3
	.word	12317
	.byte	10
	.byte	'_Ifx_CAN_MO_EDATA6_Bits',0,6,182,2,16,4,11
	.byte	'DB0',0,1
	.word	497
	.byte	8,0,2,35,0,11
	.byte	'DB1',0,1
	.word	497
	.byte	8,0,2,35,1,11
	.byte	'DB2',0,1
	.word	497
	.byte	8,0,2,35,2,11
	.byte	'DB3',0,1
	.word	497
	.byte	8,0,2,35,3,0,21
	.byte	'Ifx_CAN_MO_EDATA6_Bits',0,6,188,2,3
	.word	12440
	.byte	10
	.byte	'_Ifx_CAN_MO_FCR_Bits',0,6,191,2,16,4,11
	.byte	'MMC',0,1
	.word	497
	.byte	4,4,2,35,0,11
	.byte	'RXTOE',0,1
	.word	497
	.byte	1,3,2,35,0,11
	.byte	'BRS',0,1
	.word	497
	.byte	1,2,2,35,0,11
	.byte	'FDF',0,1
	.word	497
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	497
	.byte	1,0,2,35,0,11
	.byte	'GDFS',0,1
	.word	497
	.byte	1,7,2,35,1,11
	.byte	'IDC',0,1
	.word	497
	.byte	1,6,2,35,1,11
	.byte	'DLCC',0,1
	.word	497
	.byte	1,5,2,35,1,11
	.byte	'DATC',0,1
	.word	497
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	497
	.byte	4,0,2,35,1,11
	.byte	'RXIE',0,1
	.word	497
	.byte	1,7,2,35,2,11
	.byte	'TXIE',0,1
	.word	497
	.byte	1,6,2,35,2,11
	.byte	'OVIE',0,1
	.word	497
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	497
	.byte	1,4,2,35,2,11
	.byte	'FRREN',0,1
	.word	497
	.byte	1,3,2,35,2,11
	.byte	'RMM',0,1
	.word	497
	.byte	1,2,2,35,2,11
	.byte	'SDT',0,1
	.word	497
	.byte	1,1,2,35,2,11
	.byte	'STT',0,1
	.word	497
	.byte	1,0,2,35,2,11
	.byte	'DLC',0,1
	.word	497
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	497
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_CAN_MO_FCR_Bits',0,6,213,2,3
	.word	12563
	.byte	10
	.byte	'_Ifx_CAN_MO_FGPR_Bits',0,6,216,2,16,4,11
	.byte	'BOT',0,1
	.word	497
	.byte	8,0,2,35,0,11
	.byte	'TOP',0,1
	.word	497
	.byte	8,0,2,35,1,11
	.byte	'CUR',0,1
	.word	497
	.byte	8,0,2,35,2,11
	.byte	'SEL',0,1
	.word	497
	.byte	8,0,2,35,3,0,21
	.byte	'Ifx_CAN_MO_FGPR_Bits',0,6,222,2,3
	.word	12961
	.byte	10
	.byte	'_Ifx_CAN_MO_IPR_Bits',0,6,225,2,16,4,11
	.byte	'RXINP',0,1
	.word	497
	.byte	4,4,2,35,0,11
	.byte	'TXINP',0,1
	.word	497
	.byte	4,0,2,35,0,11
	.byte	'MPN',0,1
	.word	497
	.byte	8,0,2,35,1,11
	.byte	'CFCVAL',0,2
	.word	514
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_CAN_MO_IPR_Bits',0,6,231,2,3
	.word	13080
	.byte	10
	.byte	'_Ifx_CAN_MO_STAT_Bits',0,6,234,2,16,4,11
	.byte	'RXPND',0,1
	.word	497
	.byte	1,7,2,35,0,11
	.byte	'TXPND',0,1
	.word	497
	.byte	1,6,2,35,0,11
	.byte	'RXUPD',0,1
	.word	497
	.byte	1,5,2,35,0,11
	.byte	'NEWDAT',0,1
	.word	497
	.byte	1,4,2,35,0,11
	.byte	'MSGLST',0,1
	.word	497
	.byte	1,3,2,35,0,11
	.byte	'MSGVAL',0,1
	.word	497
	.byte	1,2,2,35,0,11
	.byte	'RTSEL',0,1
	.word	497
	.byte	1,1,2,35,0,11
	.byte	'RXEN',0,1
	.word	497
	.byte	1,0,2,35,0,11
	.byte	'TXRQ',0,1
	.word	497
	.byte	1,7,2,35,1,11
	.byte	'TXEN0',0,1
	.word	497
	.byte	1,6,2,35,1,11
	.byte	'TXEN1',0,1
	.word	497
	.byte	1,5,2,35,1,11
	.byte	'DIR',0,1
	.word	497
	.byte	1,4,2,35,1,11
	.byte	'LIST',0,1
	.word	497
	.byte	4,0,2,35,1,11
	.byte	'PPREV',0,1
	.word	497
	.byte	8,0,2,35,2,11
	.byte	'PNEXT',0,1
	.word	497
	.byte	8,0,2,35,3,0,21
	.byte	'Ifx_CAN_MO_STAT_Bits',0,6,251,2,3
	.word	13204
	.byte	10
	.byte	'_Ifx_CAN_MSID_Bits',0,6,254,2,16,4,11
	.byte	'INDEX',0,1
	.word	497
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	474
	.byte	26,0,2,35,0,0,21
	.byte	'Ifx_CAN_MSID_Bits',0,6,130,3,3
	.word	13516
	.byte	10
	.byte	'_Ifx_CAN_MSIMASK_Bits',0,6,133,3,16,4,11
	.byte	'IM',0,4
	.word	474
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_CAN_MSIMASK_Bits',0,6,136,3,3
	.word	13608
	.byte	10
	.byte	'_Ifx_CAN_MSPND_Bits',0,6,139,3,16,4,11
	.byte	'PND',0,4
	.word	474
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_CAN_MSPND_Bits',0,6,142,3,3
	.word	13681
	.byte	10
	.byte	'_Ifx_CAN_N_BTEVR_Bits',0,6,145,3,16,4,11
	.byte	'BRP',0,1
	.word	497
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	497
	.byte	2,0,2,35,0,11
	.byte	'SJW',0,1
	.word	497
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	497
	.byte	3,1,2,35,1,11
	.byte	'DIV8',0,1
	.word	497
	.byte	1,0,2,35,1,11
	.byte	'TSEG2',0,1
	.word	497
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	497
	.byte	1,2,2,35,2,11
	.byte	'TSEG1',0,2
	.word	514
	.byte	6,4,2,35,2,11
	.byte	'reserved_28',0,1
	.word	497
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_CAN_N_BTEVR_Bits',0,6,156,3,3
	.word	13751
	.byte	10
	.byte	'_Ifx_CAN_N_BTR_Bits',0,6,159,3,16,4,11
	.byte	'BRP',0,1
	.word	497
	.byte	6,2,2,35,0,11
	.byte	'SJW',0,1
	.word	497
	.byte	2,0,2,35,0,11
	.byte	'TSEG1',0,1
	.word	497
	.byte	4,4,2,35,1,11
	.byte	'TSEG2',0,1
	.word	497
	.byte	3,1,2,35,1,11
	.byte	'DIV8',0,1
	.word	497
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	514
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_CAN_N_BTR_Bits',0,6,167,3,3
	.word	13981
	.byte	10
	.byte	'_Ifx_CAN_N_CR_Bits',0,6,170,3,16,4,11
	.byte	'INIT',0,1
	.word	497
	.byte	1,7,2,35,0,11
	.byte	'TRIE',0,1
	.word	497
	.byte	1,6,2,35,0,11
	.byte	'LECIE',0,1
	.word	497
	.byte	1,5,2,35,0,11
	.byte	'ALIE',0,1
	.word	497
	.byte	1,4,2,35,0,11
	.byte	'CANDIS',0,1
	.word	497
	.byte	1,3,2,35,0,11
	.byte	'TXDIS',0,1
	.word	497
	.byte	1,2,2,35,0,11
	.byte	'CCE',0,1
	.word	497
	.byte	1,1,2,35,0,11
	.byte	'CALM',0,1
	.word	497
	.byte	1,0,2,35,0,11
	.byte	'SUSEN',0,1
	.word	497
	.byte	1,7,2,35,1,11
	.byte	'FDEN',0,1
	.word	497
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	474
	.byte	22,0,2,35,0,0,21
	.byte	'Ifx_CAN_N_CR_Bits',0,6,183,3,3
	.word	14139
	.byte	10
	.byte	'_Ifx_CAN_N_ECNT_Bits',0,6,186,3,16,4,11
	.byte	'REC',0,1
	.word	497
	.byte	8,0,2,35,0,11
	.byte	'TEC',0,1
	.word	497
	.byte	8,0,2,35,1,11
	.byte	'EWRNLVL',0,1
	.word	497
	.byte	8,0,2,35,2,11
	.byte	'LETD',0,1
	.word	497
	.byte	1,7,2,35,3,11
	.byte	'LEINC',0,1
	.word	497
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	497
	.byte	6,0,2,35,3,0,21
	.byte	'Ifx_CAN_N_ECNT_Bits',0,6,194,3,3
	.word	14379
	.byte	10
	.byte	'_Ifx_CAN_N_FBTR_Bits',0,6,197,3,16,4,11
	.byte	'FBRP',0,1
	.word	497
	.byte	6,2,2,35,0,11
	.byte	'FSJW',0,1
	.word	497
	.byte	2,0,2,35,0,11
	.byte	'FTSEG1',0,1
	.word	497
	.byte	4,4,2,35,1,11
	.byte	'FTSEG2',0,1
	.word	497
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	474
	.byte	17,0,2,35,0,0,21
	.byte	'Ifx_CAN_N_FBTR_Bits',0,6,204,3,3
	.word	14541
	.byte	10
	.byte	'_Ifx_CAN_N_FCR_Bits',0,6,207,3,16,4,11
	.byte	'CFC',0,2
	.word	514
	.byte	16,0,2,35,0,11
	.byte	'CFSEL',0,1
	.word	497
	.byte	3,5,2,35,2,11
	.byte	'CFMOD',0,1
	.word	497
	.byte	2,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	497
	.byte	1,2,2,35,2,11
	.byte	'CFCIE',0,1
	.word	497
	.byte	1,1,2,35,2,11
	.byte	'CFCOV',0,1
	.word	497
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	497
	.byte	8,0,2,35,3,0,21
	.byte	'Ifx_CAN_N_FCR_Bits',0,6,216,3,3
	.word	14689
	.byte	10
	.byte	'_Ifx_CAN_N_IPR_Bits',0,6,219,3,16,4,11
	.byte	'ALINP',0,1
	.word	497
	.byte	4,4,2,35,0,11
	.byte	'LECINP',0,1
	.word	497
	.byte	4,0,2,35,0,11
	.byte	'TRINP',0,1
	.word	497
	.byte	4,4,2,35,1,11
	.byte	'CFCINP',0,1
	.word	497
	.byte	4,0,2,35,1,11
	.byte	'TEINP',0,1
	.word	497
	.byte	4,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	514
	.byte	12,0,2,35,2,0,21
	.byte	'Ifx_CAN_N_IPR_Bits',0,6,227,3,3
	.word	14873
	.byte	10
	.byte	'_Ifx_CAN_N_PCR_Bits',0,6,230,3,16,4,11
	.byte	'RXSEL',0,1
	.word	497
	.byte	3,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	497
	.byte	5,0,2,35,0,11
	.byte	'LBM',0,1
	.word	497
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,4
	.word	474
	.byte	23,0,2,35,0,0,21
	.byte	'Ifx_CAN_N_PCR_Bits',0,6,236,3,3
	.word	15038
	.byte	10
	.byte	'_Ifx_CAN_N_SR_Bits',0,6,239,3,16,4,11
	.byte	'LEC',0,1
	.word	497
	.byte	3,5,2,35,0,11
	.byte	'TXOK',0,1
	.word	497
	.byte	1,4,2,35,0,11
	.byte	'RXOK',0,1
	.word	497
	.byte	1,3,2,35,0,11
	.byte	'ALERT',0,1
	.word	497
	.byte	1,2,2,35,0,11
	.byte	'EWRN',0,1
	.word	497
	.byte	1,1,2,35,0,11
	.byte	'BOFF',0,1
	.word	497
	.byte	1,0,2,35,0,11
	.byte	'LLE',0,1
	.word	497
	.byte	1,7,2,35,1,11
	.byte	'LOE',0,1
	.word	497
	.byte	1,6,2,35,1,11
	.byte	'SUSACK',0,1
	.word	497
	.byte	1,5,2,35,1,11
	.byte	'RESI',0,1
	.word	497
	.byte	1,4,2,35,1,11
	.byte	'FLEC',0,1
	.word	497
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	474
	.byte	17,0,2,35,0,0,21
	.byte	'Ifx_CAN_N_SR_Bits',0,6,253,3,3
	.word	15169
	.byte	10
	.byte	'_Ifx_CAN_N_TCCR_Bits',0,6,128,4,16,4,11
	.byte	'reserved_0',0,1
	.word	497
	.byte	8,0,2,35,0,11
	.byte	'TPSC',0,1
	.word	497
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	474
	.byte	6,14,2,35,0,11
	.byte	'TRIGSRC',0,1
	.word	497
	.byte	3,3,2,35,2,11
	.byte	'reserved_21',0,2
	.word	514
	.byte	11,0,2,35,2,0,21
	.byte	'Ifx_CAN_N_TCCR_Bits',0,6,135,4,3
	.word	15421
	.byte	10
	.byte	'_Ifx_CAN_N_TDCR_Bits',0,6,138,4,16,4,11
	.byte	'TDCV',0,1
	.word	497
	.byte	5,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	497
	.byte	3,0,2,35,0,11
	.byte	'TDCO',0,1
	.word	497
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	497
	.byte	3,1,2,35,1,11
	.byte	'TDC',0,1
	.word	497
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	514
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_CAN_N_TDCR_Bits',0,6,146,4,3
	.word	15581
	.byte	10
	.byte	'_Ifx_CAN_N_TRTR_Bits',0,6,149,4,16,4,11
	.byte	'RELOAD',0,2
	.word	514
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,1
	.word	497
	.byte	6,2,2,35,2,11
	.byte	'TEIE',0,1
	.word	497
	.byte	1,1,2,35,2,11
	.byte	'TE',0,1
	.word	497
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	497
	.byte	8,0,2,35,3,0,21
	.byte	'Ifx_CAN_N_TRTR_Bits',0,6,156,4,3
	.word	15753
	.byte	10
	.byte	'_Ifx_CAN_N_TTTR_Bits',0,6,159,4,16,4,11
	.byte	'RELOAD',0,2
	.word	514
	.byte	16,0,2,35,0,11
	.byte	'TXMO',0,1
	.word	497
	.byte	8,0,2,35,2,11
	.byte	'STRT',0,1
	.word	497
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	497
	.byte	7,0,2,35,3,0,21
	.byte	'Ifx_CAN_N_TTTR_Bits',0,6,165,4,3
	.word	15904
	.byte	10
	.byte	'_Ifx_CAN_OCS_Bits',0,6,168,4,16,4,11
	.byte	'TGS',0,1
	.word	497
	.byte	2,6,2,35,0,11
	.byte	'TGB',0,1
	.word	497
	.byte	1,5,2,35,0,11
	.byte	'TG_P',0,1
	.word	497
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	474
	.byte	20,8,2,35,0,11
	.byte	'SUS',0,1
	.word	497
	.byte	4,4,2,35,3,11
	.byte	'SUS_P',0,1
	.word	497
	.byte	1,3,2,35,3,11
	.byte	'SUSSTA',0,1
	.word	497
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	497
	.byte	2,0,2,35,3,0,21
	.byte	'Ifx_CAN_OCS_Bits',0,6,178,4,3
	.word	16034
	.byte	10
	.byte	'_Ifx_CAN_PANCTR_Bits',0,6,181,4,16,4,11
	.byte	'PANCMD',0,1
	.word	497
	.byte	8,0,2,35,0,11
	.byte	'BUSY',0,1
	.word	497
	.byte	1,7,2,35,1,11
	.byte	'RBUSY',0,1
	.word	497
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	497
	.byte	6,0,2,35,1,11
	.byte	'PANAR1',0,1
	.word	497
	.byte	8,0,2,35,2,11
	.byte	'PANAR2',0,1
	.word	497
	.byte	8,0,2,35,3,0,21
	.byte	'Ifx_CAN_PANCTR_Bits',0,6,189,4,3
	.word	16226
	.byte	12,6,197,4,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8643
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CAN_ACCEN0',0,6,202,4,3
	.word	16393
	.byte	12,6,205,4,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9200
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CAN_ACCEN1',0,6,210,4,3
	.word	16457
	.byte	12,6,213,4,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9277
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CAN_CLC',0,6,218,4,3
	.word	16521
	.byte	12,6,221,4,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9418
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CAN_FDR',0,6,226,4,3
	.word	16582
	.byte	12,6,229,4,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9543
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CAN_ID',0,6,234,4,3
	.word	16643
	.byte	12,6,237,4,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9648
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CAN_KRST0',0,6,242,4,3
	.word	16703
	.byte	12,6,245,4,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9757
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CAN_KRST1',0,6,250,4,3
	.word	16766
	.byte	12,6,253,4,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9848
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CAN_KRSTCLR',0,6,130,5,3
	.word	16829
	.byte	12,6,133,5,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9944
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CAN_LIST',0,6,138,5,3
	.word	16894
	.byte	12,6,141,5,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10085
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CAN_MCR',0,6,146,5,3
	.word	16956
	.byte	12,6,149,5,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10216
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CAN_MECR',0,6,154,5,3
	.word	17017
	.byte	12,6,157,5,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10450
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CAN_MESTAT',0,6,162,5,3
	.word	17079
	.byte	12,6,165,5,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10580
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CAN_MITR',0,6,170,5,3
	.word	17143
	.byte	12,6,173,5,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10670
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CAN_MO_AMR',0,6,178,5,3
	.word	17205
	.byte	12,6,181,5,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10780
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CAN_MO_AR',0,6,186,5,3
	.word	17269
	.byte	12,6,189,5,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10879
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CAN_MO_CTR',0,6,194,5,3
	.word	17332
	.byte	12,6,197,5,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11460
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CAN_MO_DATAH',0,6,202,5,3
	.word	17396
	.byte	12,6,205,5,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11581
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CAN_MO_DATAL',0,6,210,5,3
	.word	17462
	.byte	12,6,213,5,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11702
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CAN_MO_EDATA0',0,6,218,5,3
	.word	17528
	.byte	12,6,221,5,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11825
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CAN_MO_EDATA1',0,6,226,5,3
	.word	17595
	.byte	12,6,229,5,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11948
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CAN_MO_EDATA2',0,6,234,5,3
	.word	17662
	.byte	12,6,237,5,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12071
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CAN_MO_EDATA3',0,6,242,5,3
	.word	17729
	.byte	12,6,245,5,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12194
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CAN_MO_EDATA4',0,6,250,5,3
	.word	17796
	.byte	12,6,253,5,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12317
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CAN_MO_EDATA5',0,6,130,6,3
	.word	17863
	.byte	12,6,133,6,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12440
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CAN_MO_EDATA6',0,6,138,6,3
	.word	17930
	.byte	12,6,141,6,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12563
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CAN_MO_FCR',0,6,146,6,3
	.word	17997
	.byte	12,6,149,6,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12961
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CAN_MO_FGPR',0,6,154,6,3
	.word	18061
	.byte	12,6,157,6,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13080
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CAN_MO_IPR',0,6,162,6,3
	.word	18126
	.byte	12,6,165,6,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13204
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CAN_MO_STAT',0,6,170,6,3
	.word	18190
	.byte	12,6,173,6,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13516
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CAN_MSID',0,6,178,6,3
	.word	18255
	.byte	12,6,181,6,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13608
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CAN_MSIMASK',0,6,186,6,3
	.word	18317
	.byte	12,6,189,6,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13681
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CAN_MSPND',0,6,194,6,3
	.word	18382
	.byte	12,6,197,6,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13751
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CAN_N_BTEVR',0,6,202,6,3
	.word	18445
	.byte	12,6,205,6,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13981
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CAN_N_BTR',0,6,210,6,3
	.word	18510
	.byte	12,6,213,6,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14139
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CAN_N_CR',0,6,218,6,3
	.word	18573
	.byte	12,6,221,6,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14379
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CAN_N_ECNT',0,6,226,6,3
	.word	18635
	.byte	12,6,229,6,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14541
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CAN_N_FBTR',0,6,234,6,3
	.word	18699
	.byte	12,6,237,6,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14689
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CAN_N_FCR',0,6,242,6,3
	.word	18763
	.byte	12,6,245,6,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14873
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CAN_N_IPR',0,6,250,6,3
	.word	18826
	.byte	12,6,253,6,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15038
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CAN_N_PCR',0,6,130,7,3
	.word	18889
	.byte	12,6,133,7,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15169
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CAN_N_SR',0,6,138,7,3
	.word	18952
	.byte	12,6,141,7,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15421
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CAN_N_TCCR',0,6,146,7,3
	.word	19014
	.byte	12,6,149,7,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15581
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CAN_N_TDCR',0,6,154,7,3
	.word	19078
	.byte	12,6,157,7,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15753
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CAN_N_TRTR',0,6,162,7,3
	.word	19142
	.byte	12,6,165,7,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15904
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CAN_N_TTTR',0,6,170,7,3
	.word	19206
	.byte	12,6,173,7,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16034
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CAN_OCS',0,6,178,7,3
	.word	19270
	.byte	12,6,181,7,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16226
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CAN_PANCTR',0,6,186,7,3
	.word	19331
	.byte	12,6,199,7,5,4,13
	.byte	'EDATA0',0
	.word	17528
	.byte	4,2,35,0,13
	.byte	'FCR',0
	.word	17997
	.byte	4,2,35,0,0,12,6,205,7,5,4,13
	.byte	'EDATA1',0
	.word	17595
	.byte	4,2,35,0,13
	.byte	'FGPR',0
	.word	18061
	.byte	4,2,35,0,0,12,6,211,7,5,4,13
	.byte	'EDATA2',0
	.word	17662
	.byte	4,2,35,0,13
	.byte	'IPR',0
	.word	18126
	.byte	4,2,35,0,0,12,6,217,7,5,4,13
	.byte	'AMR',0
	.word	17205
	.byte	4,2,35,0,13
	.byte	'EDATA3',0
	.word	17729
	.byte	4,2,35,0,0,12,6,223,7,5,4,13
	.byte	'DATAL',0
	.word	17462
	.byte	4,2,35,0,13
	.byte	'EDATA4',0
	.word	17796
	.byte	4,2,35,0,0,12,6,229,7,5,4,13
	.byte	'DATAH',0
	.word	17396
	.byte	4,2,35,0,13
	.byte	'EDATA5',0
	.word	17863
	.byte	4,2,35,0,0,12,6,235,7,5,4,13
	.byte	'AR',0
	.word	17269
	.byte	4,2,35,0,13
	.byte	'EDATA6',0
	.word	17930
	.byte	4,2,35,0,0,12,6,241,7,5,4,13
	.byte	'CTR',0
	.word	17332
	.byte	4,2,35,0,13
	.byte	'STAT',0
	.word	18190
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CAN_MO',0,6,197,7,25,32,23
	.word	19395
	.byte	4,2,35,0,23
	.word	19431
	.byte	4,2,35,4,23
	.word	19468
	.byte	4,2,35,8,23
	.word	19504
	.byte	4,2,35,12,23
	.word	19540
	.byte	4,2,35,16,23
	.word	19578
	.byte	4,2,35,20,23
	.word	19616
	.byte	4,2,35,24,23
	.word	19651
	.byte	4,2,35,28,0,16
	.word	19685
	.byte	21
	.byte	'Ifx_CAN_MO',0,6,247,7,3
	.word	19776
	.byte	12,6,128,8,5,4,13
	.byte	'BTEVR',0
	.word	18445
	.byte	4,2,35,0,13
	.byte	'BTR',0
	.word	18510
	.byte	4,2,35,0,0,14,192,1
	.word	497
	.byte	15,191,1,0,10
	.byte	'_Ifx_CAN_N',0,6,250,7,25,128,2,13
	.byte	'CR',0
	.word	18573
	.byte	4,2,35,0,13
	.byte	'SR',0
	.word	18952
	.byte	4,2,35,4,13
	.byte	'IPR',0
	.word	18826
	.byte	4,2,35,8,13
	.byte	'PCR',0
	.word	18889
	.byte	4,2,35,12,23
	.word	19801
	.byte	4,2,35,16,13
	.byte	'ECNT',0
	.word	18635
	.byte	4,2,35,20,13
	.byte	'FCR',0
	.word	18763
	.byte	4,2,35,24,13
	.byte	'TCCR',0
	.word	19014
	.byte	4,2,35,28,13
	.byte	'TRTR',0
	.word	19142
	.byte	4,2,35,32,13
	.byte	'TATTR',0
	.word	19206
	.byte	4,2,35,36,13
	.byte	'TBTTR',0
	.word	19206
	.byte	4,2,35,40,13
	.byte	'TCTTR',0
	.word	19206
	.byte	4,2,35,44,13
	.byte	'reserved_30',0
	.word	3361
	.byte	8,2,35,48,13
	.byte	'FBTR',0
	.word	18699
	.byte	4,2,35,56,13
	.byte	'TDCR',0
	.word	19078
	.byte	4,2,35,60,13
	.byte	'reserved_40',0
	.word	19836
	.byte	192,1,2,35,64,0,16
	.word	19847
	.byte	21
	.byte	'Ifx_CAN_N',0,6,145,8,3
	.word	20096
	.byte	14,216,1
	.word	497
	.byte	15,215,1,0,14,64
	.word	16894
	.byte	15,15,0,14,32
	.word	18382
	.byte	15,7,0,14,32
	.word	497
	.byte	15,31,0,14,32
	.word	18255
	.byte	15,7,0,14,40
	.word	497
	.byte	15,39,0,14,128,10
	.word	19847
	.byte	15,4,0,16
	.word	20176
	.byte	14,128,18
	.word	497
	.byte	15,255,17,0,14,128,64
	.word	19685
	.byte	15,255,1,0,16
	.word	20202
	.byte	14,128,32
	.word	497
	.byte	15,255,31,0,10
	.byte	'_Ifx_CAN',0,6,158,8,25,128,128,1,13
	.byte	'CLC',0
	.word	16521
	.byte	4,2,35,0,13
	.byte	'reserved_4',0
	.word	1542
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	16643
	.byte	4,2,35,8,13
	.byte	'FDR',0
	.word	16582
	.byte	4,2,35,12,13
	.byte	'reserved_10',0
	.word	20120
	.byte	216,1,2,35,16,13
	.byte	'OCS',0
	.word	19270
	.byte	4,3,35,232,1,13
	.byte	'KRSTCLR',0
	.word	16829
	.byte	4,3,35,236,1,13
	.byte	'KRST1',0
	.word	16766
	.byte	4,3,35,240,1,13
	.byte	'KRST0',0
	.word	16703
	.byte	4,3,35,244,1,13
	.byte	'ACCEN1',0
	.word	16457
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	16393
	.byte	4,3,35,252,1,13
	.byte	'LIST',0
	.word	20131
	.byte	64,3,35,128,2,13
	.byte	'MSPND',0
	.word	20140
	.byte	32,3,35,192,2,13
	.byte	'reserved_160',0
	.word	20149
	.byte	32,3,35,224,2,13
	.byte	'MSID',0
	.word	20158
	.byte	32,3,35,128,3,13
	.byte	'reserved_1A0',0
	.word	20149
	.byte	32,3,35,160,3,13
	.byte	'MSIMASK',0
	.word	18317
	.byte	4,3,35,192,3,13
	.byte	'PANCTR',0
	.word	19331
	.byte	4,3,35,196,3,13
	.byte	'MCR',0
	.word	16956
	.byte	4,3,35,200,3,13
	.byte	'MITR',0
	.word	17143
	.byte	4,3,35,204,3,13
	.byte	'MECR',0
	.word	17017
	.byte	4,3,35,208,3,13
	.byte	'MESTAT',0
	.word	17079
	.byte	4,3,35,212,3,13
	.byte	'reserved_1D8',0
	.word	20167
	.byte	40,3,35,216,3,13
	.byte	'N',0
	.word	20186
	.byte	128,10,3,35,128,4,13
	.byte	'reserved_700',0
	.word	20191
	.byte	128,18,3,35,128,14,13
	.byte	'MO',0
	.word	20213
	.byte	128,64,3,35,128,32,13
	.byte	'reserved_3000',0
	.word	20218
	.byte	128,32,3,35,128,96,0,16
	.word	20229
	.byte	21
	.byte	'Ifx_CAN',0,6,187,8,3
	.word	20712
	.byte	21
	.byte	'boolean',0,7,101,29
	.word	497
	.byte	21
	.byte	'uint8',0,7,105,29
	.word	497
	.byte	21
	.byte	'uint16',0,7,109,29
	.word	514
	.byte	7
	.byte	'unsigned long int',0,4,7,21
	.byte	'uint32',0,7,113,29
	.word	20779
	.byte	21
	.byte	'uint64',0,7,118,29
	.word	356
	.byte	21
	.byte	'sint16',0,7,126,29
	.word	8550
	.byte	7
	.byte	'long int',0,4,5,21
	.byte	'sint32',0,7,131,1,29
	.word	20845
	.byte	7
	.byte	'long long int',0,8,5,21
	.byte	'sint64',0,7,138,1,29
	.word	20873
	.byte	21
	.byte	'float32',0,7,167,1,29
	.word	302
	.byte	21
	.byte	'pvoid',0,8,57,28
	.word	388
	.byte	21
	.byte	'Ifx_TickTime',0,8,79,28
	.word	20873
	.byte	17,8,130,1,9,1,18
	.byte	'Ifx_RxSel_a',0,0,18
	.byte	'Ifx_RxSel_b',0,1,18
	.byte	'Ifx_RxSel_c',0,2,18
	.byte	'Ifx_RxSel_d',0,3,18
	.byte	'Ifx_RxSel_e',0,4,18
	.byte	'Ifx_RxSel_f',0,5,18
	.byte	'Ifx_RxSel_g',0,6,18
	.byte	'Ifx_RxSel_h',0,7,0,21
	.byte	'Ifx_RxSel',0,8,140,1,3
	.word	20958
	.byte	17,9,100,9,1,18
	.byte	'IfxMultican_NodeId_none',0,127,18
	.byte	'IfxMultican_NodeId_0',0,0,18
	.byte	'IfxMultican_NodeId_1',0,1,18
	.byte	'IfxMultican_NodeId_2',0,2,18
	.byte	'IfxMultican_NodeId_3',0,3,0,21
	.byte	'IfxMultican_NodeId',0,9,107,3
	.word	21096
	.byte	21
	.byte	'Ifx_P_ACCEN0_Bits',0,4,79,3
	.word	7114
	.byte	21
	.byte	'Ifx_P_ACCEN1_Bits',0,4,85,3
	.word	7027
	.byte	21
	.byte	'Ifx_P_ESR_Bits',0,4,107,3
	.word	3370
	.byte	21
	.byte	'Ifx_P_ID_Bits',0,4,115,3
	.word	1423
	.byte	21
	.byte	'Ifx_P_IN_Bits',0,4,137,1,3
	.word	2418
	.byte	21
	.byte	'Ifx_P_IOCR0_Bits',0,4,150,1,3
	.word	1551
	.byte	21
	.byte	'Ifx_P_IOCR12_Bits',0,4,163,1,3
	.word	2198
	.byte	21
	.byte	'Ifx_P_IOCR4_Bits',0,4,176,1,3
	.word	1766
	.byte	21
	.byte	'Ifx_P_IOCR8_Bits',0,4,189,1,3
	.word	1981
	.byte	21
	.byte	'Ifx_P_LPCR0_Bits',0,4,197,1,3
	.word	6386
	.byte	21
	.byte	'Ifx_P_LPCR1_Bits',0,4,205,1,3
	.word	6510
	.byte	21
	.byte	'Ifx_P_LPCR1_P21_Bits',0,4,215,1,3
	.word	6594
	.byte	21
	.byte	'Ifx_P_LPCR2_Bits',0,4,229,1,3
	.word	6774
	.byte	21
	.byte	'Ifx_P_OMCR0_Bits',0,4,240,1,3
	.word	5025
	.byte	21
	.byte	'Ifx_P_OMCR12_Bits',0,4,250,1,3
	.word	5549
	.byte	21
	.byte	'Ifx_P_OMCR4_Bits',0,4,133,2,3
	.word	5199
	.byte	21
	.byte	'Ifx_P_OMCR8_Bits',0,4,144,2,3
	.word	5373
	.byte	21
	.byte	'Ifx_P_OMCR_Bits',0,4,166,2,3
	.word	6038
	.byte	21
	.byte	'Ifx_P_OMR_Bits',0,4,203,2,3
	.word	852
	.byte	21
	.byte	'Ifx_P_OMSR0_Bits',0,4,213,2,3
	.word	4362
	.byte	21
	.byte	'Ifx_P_OMSR12_Bits',0,4,224,2,3
	.word	4850
	.byte	21
	.byte	'Ifx_P_OMSR4_Bits',0,4,235,2,3
	.word	4509
	.byte	21
	.byte	'Ifx_P_OMSR8_Bits',0,4,246,2,3
	.word	4678
	.byte	21
	.byte	'Ifx_P_OMSR_Bits',0,4,140,3,3
	.word	5705
	.byte	21
	.byte	'Ifx_P_OUT_Bits',0,4,162,3,3
	.word	536
	.byte	21
	.byte	'Ifx_P_PCSR_Bits',0,4,180,3,3
	.word	4076
	.byte	21
	.byte	'Ifx_P_PDISC_Bits',0,4,202,3,3
	.word	3710
	.byte	21
	.byte	'Ifx_P_PDR0_Bits',0,4,223,3,3
	.word	2741
	.byte	21
	.byte	'Ifx_P_PDR1_Bits',0,4,244,3,3
	.word	3045
	.byte	21
	.byte	'Ifx_P_ACCEN0',0,4,129,4,3
	.word	7641
	.byte	21
	.byte	'Ifx_P_ACCEN1',0,4,137,4,3
	.word	7074
	.byte	21
	.byte	'Ifx_P_ESR',0,4,145,4,3
	.word	3661
	.byte	21
	.byte	'Ifx_P_ID',0,4,153,4,3
	.word	1502
	.byte	21
	.byte	'Ifx_P_IN',0,4,161,4,3
	.word	2692
	.byte	21
	.byte	'Ifx_P_IOCR0',0,4,169,4,3
	.word	1726
	.byte	21
	.byte	'Ifx_P_IOCR12',0,4,177,4,3
	.word	2378
	.byte	21
	.byte	'Ifx_P_IOCR4',0,4,185,4,3
	.word	1941
	.byte	21
	.byte	'Ifx_P_IOCR8',0,4,193,4,3
	.word	2158
	.byte	21
	.byte	'Ifx_P_LPCR0',0,4,201,4,3
	.word	6470
	.byte	21
	.byte	'Ifx_P_LPCR1',0,4,210,4,3
	.word	6719
	.byte	21
	.byte	'Ifx_P_LPCR2',0,4,218,4,3
	.word	6978
	.byte	21
	.byte	'Ifx_P_OMCR',0,4,226,4,3
	.word	6346
	.byte	21
	.byte	'Ifx_P_OMCR0',0,4,234,4,3
	.word	5159
	.byte	21
	.byte	'Ifx_P_OMCR12',0,4,242,4,3
	.word	5665
	.byte	21
	.byte	'Ifx_P_OMCR4',0,4,250,4,3
	.word	5333
	.byte	21
	.byte	'Ifx_P_OMCR8',0,4,130,5,3
	.word	5509
	.byte	21
	.byte	'Ifx_P_OMR',0,4,138,5,3
	.word	1383
	.byte	21
	.byte	'Ifx_P_OMSR',0,4,146,5,3
	.word	5998
	.byte	21
	.byte	'Ifx_P_OMSR0',0,4,154,5,3
	.word	4469
	.byte	21
	.byte	'Ifx_P_OMSR12',0,4,162,5,3
	.word	4985
	.byte	21
	.byte	'Ifx_P_OMSR4',0,4,170,5,3
	.word	4638
	.byte	21
	.byte	'Ifx_P_OMSR8',0,4,178,5,3
	.word	4810
	.byte	21
	.byte	'Ifx_P_OUT',0,4,186,5,3
	.word	812
	.byte	21
	.byte	'Ifx_P_PCSR',0,4,194,5,3
	.word	4322
	.byte	21
	.byte	'Ifx_P_PDISC',0,4,202,5,3
	.word	4036
	.byte	21
	.byte	'Ifx_P_PDR0',0,4,210,5,3
	.word	3005
	.byte	21
	.byte	'Ifx_P_PDR1',0,4,218,5,3
	.word	3321
	.byte	16
	.word	7681
	.byte	21
	.byte	'Ifx_P',0,4,139,6,3
	.word	22565
	.byte	17,10,250,10,9,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy16384',0,0,18
	.byte	'IfxScu_WDTCON1_IR_divBy256',0,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy64',0,2,0,21
	.byte	'IfxScu_WDTCON1_IR',0,10,255,10,3
	.word	22585
	.byte	10
	.byte	'_Ifx_SCU_ACCEN0_Bits',0,11,45,16,4,11
	.byte	'EN0',0,1
	.word	497
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	497
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	497
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	497
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	497
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	497
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	497
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	497
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	497
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	497
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	497
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	497
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	497
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	497
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	497
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	497
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	497
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	497
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	497
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	497
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	497
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	497
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	497
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	497
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	497
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	497
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	497
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	497
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	497
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	497
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	497
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	497
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_ACCEN0_Bits',0,11,79,3
	.word	22707
	.byte	10
	.byte	'_Ifx_SCU_ACCEN1_Bits',0,11,82,16,4,11
	.byte	'reserved_0',0,4
	.word	474
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_SCU_ACCEN1_Bits',0,11,85,3
	.word	23264
	.byte	10
	.byte	'_Ifx_SCU_ARSTDIS_Bits',0,11,88,16,4,11
	.byte	'STM0DIS',0,1
	.word	497
	.byte	1,7,2,35,0,11
	.byte	'STM1DIS',0,1
	.word	497
	.byte	1,6,2,35,0,11
	.byte	'STM2DIS',0,1
	.word	497
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	474
	.byte	29,0,2,35,0,0,21
	.byte	'Ifx_SCU_ARSTDIS_Bits',0,11,94,3
	.word	23341
	.byte	10
	.byte	'_Ifx_SCU_CCUCON0_Bits',0,11,97,16,4,11
	.byte	'BAUD1DIV',0,1
	.word	497
	.byte	4,4,2,35,0,11
	.byte	'BAUD2DIV',0,1
	.word	497
	.byte	4,0,2,35,0,11
	.byte	'SRIDIV',0,1
	.word	497
	.byte	4,4,2,35,1,11
	.byte	'LPDIV',0,1
	.word	497
	.byte	4,0,2,35,1,11
	.byte	'SPBDIV',0,1
	.word	497
	.byte	4,4,2,35,2,11
	.byte	'FSI2DIV',0,1
	.word	497
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	497
	.byte	2,0,2,35,2,11
	.byte	'FSIDIV',0,1
	.word	497
	.byte	2,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	497
	.byte	2,4,2,35,3,11
	.byte	'CLKSEL',0,1
	.word	497
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	497
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	497
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON0_Bits',0,11,111,3
	.word	23477
	.byte	10
	.byte	'_Ifx_SCU_CCUCON1_Bits',0,11,114,16,4,11
	.byte	'CANDIV',0,1
	.word	497
	.byte	4,4,2,35,0,11
	.byte	'ERAYDIV',0,1
	.word	497
	.byte	4,0,2,35,0,11
	.byte	'STMDIV',0,1
	.word	497
	.byte	4,4,2,35,1,11
	.byte	'GTMDIV',0,1
	.word	497
	.byte	4,0,2,35,1,11
	.byte	'ETHDIV',0,1
	.word	497
	.byte	4,4,2,35,2,11
	.byte	'ASCLINFDIV',0,1
	.word	497
	.byte	4,0,2,35,2,11
	.byte	'ASCLINSDIV',0,1
	.word	497
	.byte	4,4,2,35,3,11
	.byte	'INSEL',0,1
	.word	497
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	497
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	497
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON1_Bits',0,11,126,3
	.word	23757
	.byte	10
	.byte	'_Ifx_SCU_CCUCON2_Bits',0,11,129,1,16,4,11
	.byte	'BBBDIV',0,1
	.word	497
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	474
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	497
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	497
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON2_Bits',0,11,135,1,3
	.word	23995
	.byte	10
	.byte	'_Ifx_SCU_CCUCON3_Bits',0,11,138,1,16,4,11
	.byte	'PLLDIV',0,1
	.word	497
	.byte	6,2,2,35,0,11
	.byte	'PLLSEL',0,1
	.word	497
	.byte	2,0,2,35,0,11
	.byte	'PLLERAYDIV',0,1
	.word	497
	.byte	6,2,2,35,1,11
	.byte	'PLLERAYSEL',0,1
	.word	497
	.byte	2,0,2,35,1,11
	.byte	'SRIDIV',0,1
	.word	497
	.byte	6,2,2,35,2,11
	.byte	'SRISEL',0,1
	.word	497
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	497
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	497
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	497
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	497
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON3_Bits',0,11,150,1,3
	.word	24123
	.byte	10
	.byte	'_Ifx_SCU_CCUCON4_Bits',0,11,153,1,16,4,11
	.byte	'SPBDIV',0,1
	.word	497
	.byte	6,2,2,35,0,11
	.byte	'SPBSEL',0,1
	.word	497
	.byte	2,0,2,35,0,11
	.byte	'GTMDIV',0,1
	.word	497
	.byte	6,2,2,35,1,11
	.byte	'GTMSEL',0,1
	.word	497
	.byte	2,0,2,35,1,11
	.byte	'STMDIV',0,1
	.word	497
	.byte	6,2,2,35,2,11
	.byte	'STMSEL',0,1
	.word	497
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	497
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	497
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	497
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	497
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON4_Bits',0,11,165,1,3
	.word	24366
	.byte	10
	.byte	'_Ifx_SCU_CCUCON5_Bits',0,11,168,1,16,4,11
	.byte	'MAXDIV',0,1
	.word	497
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	474
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	497
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	497
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON5_Bits',0,11,174,1,3
	.word	24601
	.byte	10
	.byte	'_Ifx_SCU_CCUCON6_Bits',0,11,177,1,16,4,11
	.byte	'CPU0DIV',0,1
	.word	497
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	474
	.byte	26,0,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON6_Bits',0,11,181,1,3
	.word	24729
	.byte	10
	.byte	'_Ifx_SCU_CCUCON7_Bits',0,11,184,1,16,4,11
	.byte	'CPU1DIV',0,1
	.word	497
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	474
	.byte	26,0,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON7_Bits',0,11,188,1,3
	.word	24829
	.byte	10
	.byte	'_Ifx_SCU_CHIPID_Bits',0,11,191,1,16,4,11
	.byte	'CHREV',0,1
	.word	497
	.byte	6,2,2,35,0,11
	.byte	'CHTEC',0,1
	.word	497
	.byte	2,0,2,35,0,11
	.byte	'CHID',0,1
	.word	497
	.byte	8,0,2,35,1,11
	.byte	'EEA',0,1
	.word	497
	.byte	1,7,2,35,2,11
	.byte	'UCODE',0,1
	.word	497
	.byte	7,0,2,35,2,11
	.byte	'FSIZE',0,1
	.word	497
	.byte	4,4,2,35,3,11
	.byte	'SP',0,1
	.word	497
	.byte	2,2,2,35,3,11
	.byte	'SEC',0,1
	.word	497
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	497
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CHIPID_Bits',0,11,202,1,3
	.word	24929
	.byte	10
	.byte	'_Ifx_SCU_DTSCON_Bits',0,11,205,1,16,4,11
	.byte	'PWD',0,1
	.word	497
	.byte	1,7,2,35,0,11
	.byte	'START',0,1
	.word	497
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	497
	.byte	2,4,2,35,0,11
	.byte	'CAL',0,4
	.word	474
	.byte	20,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	497
	.byte	7,1,2,35,3,11
	.byte	'SLCK',0,1
	.word	497
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_DTSCON_Bits',0,11,213,1,3
	.word	25137
	.byte	10
	.byte	'_Ifx_SCU_DTSLIM_Bits',0,11,216,1,16,4,11
	.byte	'LOWER',0,2
	.word	514
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	497
	.byte	5,1,2,35,1,11
	.byte	'LLU',0,1
	.word	497
	.byte	1,0,2,35,1,11
	.byte	'UPPER',0,2
	.word	514
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	497
	.byte	4,2,2,35,3,11
	.byte	'SLCK',0,1
	.word	497
	.byte	1,1,2,35,3,11
	.byte	'UOF',0,1
	.word	497
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_DTSLIM_Bits',0,11,225,1,3
	.word	25302
	.byte	10
	.byte	'_Ifx_SCU_DTSSTAT_Bits',0,11,228,1,16,4,11
	.byte	'RESULT',0,2
	.word	514
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	497
	.byte	4,2,2,35,1,11
	.byte	'RDY',0,1
	.word	497
	.byte	1,1,2,35,1,11
	.byte	'BUSY',0,1
	.word	497
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	514
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_DTSSTAT_Bits',0,11,235,1,3
	.word	25485
	.byte	10
	.byte	'_Ifx_SCU_EICR_Bits',0,11,238,1,16,4,11
	.byte	'reserved_0',0,1
	.word	497
	.byte	4,4,2,35,0,11
	.byte	'EXIS0',0,1
	.word	497
	.byte	3,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	497
	.byte	1,0,2,35,0,11
	.byte	'FEN0',0,1
	.word	497
	.byte	1,7,2,35,1,11
	.byte	'REN0',0,1
	.word	497
	.byte	1,6,2,35,1,11
	.byte	'LDEN0',0,1
	.word	497
	.byte	1,5,2,35,1,11
	.byte	'EIEN0',0,1
	.word	497
	.byte	1,4,2,35,1,11
	.byte	'INP0',0,1
	.word	497
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	474
	.byte	5,12,2,35,0,11
	.byte	'EXIS1',0,1
	.word	497
	.byte	3,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	497
	.byte	1,0,2,35,2,11
	.byte	'FEN1',0,1
	.word	497
	.byte	1,7,2,35,3,11
	.byte	'REN1',0,1
	.word	497
	.byte	1,6,2,35,3,11
	.byte	'LDEN1',0,1
	.word	497
	.byte	1,5,2,35,3,11
	.byte	'EIEN1',0,1
	.word	497
	.byte	1,4,2,35,3,11
	.byte	'INP1',0,1
	.word	497
	.byte	3,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	497
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EICR_Bits',0,11,129,2,3
	.word	25639
	.byte	10
	.byte	'_Ifx_SCU_EIFR_Bits',0,11,132,2,16,4,11
	.byte	'INTF0',0,1
	.word	497
	.byte	1,7,2,35,0,11
	.byte	'INTF1',0,1
	.word	497
	.byte	1,6,2,35,0,11
	.byte	'INTF2',0,1
	.word	497
	.byte	1,5,2,35,0,11
	.byte	'INTF3',0,1
	.word	497
	.byte	1,4,2,35,0,11
	.byte	'INTF4',0,1
	.word	497
	.byte	1,3,2,35,0,11
	.byte	'INTF5',0,1
	.word	497
	.byte	1,2,2,35,0,11
	.byte	'INTF6',0,1
	.word	497
	.byte	1,1,2,35,0,11
	.byte	'INTF7',0,1
	.word	497
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	474
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_SCU_EIFR_Bits',0,11,143,2,3
	.word	26003
	.byte	10
	.byte	'_Ifx_SCU_EMSR_Bits',0,11,146,2,16,4,11
	.byte	'POL',0,1
	.word	497
	.byte	1,7,2,35,0,11
	.byte	'MODE',0,1
	.word	497
	.byte	1,6,2,35,0,11
	.byte	'ENON',0,1
	.word	497
	.byte	1,5,2,35,0,11
	.byte	'PSEL',0,1
	.word	497
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	514
	.byte	12,0,2,35,0,11
	.byte	'EMSF',0,1
	.word	497
	.byte	1,7,2,35,2,11
	.byte	'SEMSF',0,1
	.word	497
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	497
	.byte	6,0,2,35,2,11
	.byte	'EMSFM',0,1
	.word	497
	.byte	2,6,2,35,3,11
	.byte	'SEMSFM',0,1
	.word	497
	.byte	2,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	497
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_EMSR_Bits',0,11,159,2,3
	.word	26214
	.byte	10
	.byte	'_Ifx_SCU_ESRCFG_Bits',0,11,162,2,16,4,11
	.byte	'reserved_0',0,1
	.word	497
	.byte	7,1,2,35,0,11
	.byte	'EDCON',0,2
	.word	514
	.byte	2,7,2,35,0,11
	.byte	'reserved_9',0,4
	.word	474
	.byte	23,0,2,35,0,0,21
	.byte	'Ifx_SCU_ESRCFG_Bits',0,11,167,2,3
	.word	26466
	.byte	10
	.byte	'_Ifx_SCU_ESROCFG_Bits',0,11,170,2,16,4,11
	.byte	'ARI',0,1
	.word	497
	.byte	1,7,2,35,0,11
	.byte	'ARC',0,1
	.word	497
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	474
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_ESROCFG_Bits',0,11,175,2,3
	.word	26584
	.byte	10
	.byte	'_Ifx_SCU_EVR13CON_Bits',0,11,178,2,16,4,11
	.byte	'reserved_0',0,4
	.word	474
	.byte	28,4,2,35,0,11
	.byte	'EVR13OFF',0,1
	.word	497
	.byte	1,3,2,35,3,11
	.byte	'BPEVR13OFF',0,1
	.word	497
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	497
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	497
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVR13CON_Bits',0,11,185,2,3
	.word	26695
	.byte	10
	.byte	'_Ifx_SCU_EVR33CON_Bits',0,11,188,2,16,4,11
	.byte	'reserved_0',0,4
	.word	474
	.byte	28,4,2,35,0,11
	.byte	'EVR33OFF',0,1
	.word	497
	.byte	1,3,2,35,3,11
	.byte	'BPEVR33OFF',0,1
	.word	497
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	497
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	497
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVR33CON_Bits',0,11,195,2,3
	.word	26858
	.byte	10
	.byte	'_Ifx_SCU_EVRADCSTAT_Bits',0,11,198,2,16,4,11
	.byte	'ADC13V',0,1
	.word	497
	.byte	8,0,2,35,0,11
	.byte	'ADC33V',0,1
	.word	497
	.byte	8,0,2,35,1,11
	.byte	'ADCSWDV',0,1
	.word	497
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	497
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	497
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRADCSTAT_Bits',0,11,205,2,3
	.word	27021
	.byte	10
	.byte	'_Ifx_SCU_EVRDVSTAT_Bits',0,11,208,2,16,4,11
	.byte	'DVS13TRIM',0,1
	.word	497
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	497
	.byte	8,0,2,35,1,11
	.byte	'DVS33TRIM',0,1
	.word	497
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	497
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	497
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRDVSTAT_Bits',0,11,215,2,3
	.word	27179
	.byte	10
	.byte	'_Ifx_SCU_EVRMONCTRL_Bits',0,11,218,2,16,4,11
	.byte	'EVR13OVMOD',0,1
	.word	497
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	497
	.byte	2,4,2,35,0,11
	.byte	'EVR13UVMOD',0,1
	.word	497
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	497
	.byte	2,0,2,35,0,11
	.byte	'EVR33OVMOD',0,1
	.word	497
	.byte	2,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	497
	.byte	2,4,2,35,1,11
	.byte	'EVR33UVMOD',0,1
	.word	497
	.byte	2,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	497
	.byte	2,0,2,35,1,11
	.byte	'SWDOVMOD',0,1
	.word	497
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	497
	.byte	2,4,2,35,2,11
	.byte	'SWDUVMOD',0,1
	.word	497
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,2
	.word	514
	.byte	10,0,2,35,2,0,21
	.byte	'Ifx_SCU_EVRMONCTRL_Bits',0,11,232,2,3
	.word	27344
	.byte	10
	.byte	'_Ifx_SCU_EVROSCCTRL_Bits',0,11,235,2,16,4,11
	.byte	'OSCTRIM',0,2
	.word	514
	.byte	10,6,2,35,0,11
	.byte	'OSCPTAT',0,1
	.word	497
	.byte	6,0,2,35,1,11
	.byte	'OSCANASEL',0,1
	.word	497
	.byte	4,4,2,35,2,11
	.byte	'HPBGTRIM',0,2
	.word	514
	.byte	7,5,2,35,2,11
	.byte	'HPBGCLKEN',0,1
	.word	497
	.byte	1,4,2,35,3,11
	.byte	'OSC3V3',0,1
	.word	497
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	497
	.byte	2,1,2,35,3,11
	.byte	'LCK',0,1
	.word	497
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVROSCCTRL_Bits',0,11,245,2,3
	.word	27673
	.byte	10
	.byte	'_Ifx_SCU_EVROVMON_Bits',0,11,248,2,16,4,11
	.byte	'EVR13OVVAL',0,1
	.word	497
	.byte	8,0,2,35,0,11
	.byte	'EVR33OVVAL',0,1
	.word	497
	.byte	8,0,2,35,1,11
	.byte	'SWDOVVAL',0,1
	.word	497
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	497
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	497
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVROVMON_Bits',0,11,255,2,3
	.word	27894
	.byte	10
	.byte	'_Ifx_SCU_EVRRSTCON_Bits',0,11,130,3,16,4,11
	.byte	'RST13TRIM',0,1
	.word	497
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	474
	.byte	16,8,2,35,0,11
	.byte	'RST13OFF',0,1
	.word	497
	.byte	1,7,2,35,3,11
	.byte	'BPRST13OFF',0,1
	.word	497
	.byte	1,6,2,35,3,11
	.byte	'RST33OFF',0,1
	.word	497
	.byte	1,5,2,35,3,11
	.byte	'BPRST33OFF',0,1
	.word	497
	.byte	1,4,2,35,3,11
	.byte	'RSTSWDOFF',0,1
	.word	497
	.byte	1,3,2,35,3,11
	.byte	'BPRSTSWDOFF',0,1
	.word	497
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	497
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	497
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRRSTCON_Bits',0,11,142,3,3
	.word	28057
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF1_Bits',0,11,145,3,16,4,11
	.byte	'SD5P',0,1
	.word	497
	.byte	8,0,2,35,0,11
	.byte	'SD5I',0,1
	.word	497
	.byte	8,0,2,35,1,11
	.byte	'SD5D',0,1
	.word	497
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	497
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	497
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF1_Bits',0,11,152,3,3
	.word	28329
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF2_Bits',0,11,155,3,16,4,11
	.byte	'SD33P',0,1
	.word	497
	.byte	8,0,2,35,0,11
	.byte	'SD33I',0,1
	.word	497
	.byte	8,0,2,35,1,11
	.byte	'SD33D',0,1
	.word	497
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	497
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	497
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF2_Bits',0,11,162,3,3
	.word	28482
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF3_Bits',0,11,165,3,16,4,11
	.byte	'CT5REG0',0,1
	.word	497
	.byte	8,0,2,35,0,11
	.byte	'CT5REG1',0,1
	.word	497
	.byte	8,0,2,35,1,11
	.byte	'CT5REG2',0,1
	.word	497
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	497
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	497
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF3_Bits',0,11,172,3,3
	.word	28638
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF4_Bits',0,11,175,3,16,4,11
	.byte	'CT5REG3',0,1
	.word	497
	.byte	8,0,2,35,0,11
	.byte	'CT5REG4',0,1
	.word	497
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	514
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	497
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF4_Bits',0,11,181,3,3
	.word	28800
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF5_Bits',0,11,184,3,16,4,11
	.byte	'CT33REG0',0,1
	.word	497
	.byte	8,0,2,35,0,11
	.byte	'CT33REG1',0,1
	.word	497
	.byte	8,0,2,35,1,11
	.byte	'CT33REG2',0,1
	.word	497
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	497
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	497
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF5_Bits',0,11,191,3,3
	.word	28943
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF6_Bits',0,11,194,3,16,4,11
	.byte	'CT33REG3',0,1
	.word	497
	.byte	8,0,2,35,0,11
	.byte	'CT33REG4',0,1
	.word	497
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	514
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	497
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF6_Bits',0,11,200,3,3
	.word	29108
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL1_Bits',0,11,203,3,16,4,11
	.byte	'SDFREQSPRD',0,2
	.word	514
	.byte	16,0,2,35,0,11
	.byte	'SDFREQ',0,1
	.word	497
	.byte	8,0,2,35,2,11
	.byte	'SDSTEP',0,1
	.word	497
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	497
	.byte	2,2,2,35,3,11
	.byte	'SDSAMPLE',0,1
	.word	497
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	497
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCTRL1_Bits',0,11,211,3,3
	.word	29253
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL2_Bits',0,11,214,3,16,4,11
	.byte	'DRVP',0,1
	.word	497
	.byte	8,0,2,35,0,11
	.byte	'SDMINMAXDC',0,1
	.word	497
	.byte	8,0,2,35,1,11
	.byte	'DRVN',0,1
	.word	497
	.byte	8,0,2,35,2,11
	.byte	'SDLUT',0,1
	.word	497
	.byte	6,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	497
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	497
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCTRL2_Bits',0,11,222,3,3
	.word	29434
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL3_Bits',0,11,225,3,16,4,11
	.byte	'SDPWMPRE',0,1
	.word	497
	.byte	8,0,2,35,0,11
	.byte	'SDPID',0,1
	.word	497
	.byte	8,0,2,35,1,11
	.byte	'SDVOKLVL',0,1
	.word	497
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	497
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	497
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCTRL3_Bits',0,11,232,3,3
	.word	29608
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL4_Bits',0,11,235,3,16,4,11
	.byte	'reserved_0',0,1
	.word	497
	.byte	8,0,2,35,0,11
	.byte	'SYNCDIV',0,1
	.word	497
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	474
	.byte	20,1,2,35,0,11
	.byte	'LCK',0,1
	.word	497
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCTRL4_Bits',0,11,241,3,3
	.word	29768
	.byte	10
	.byte	'_Ifx_SCU_EVRSTAT_Bits',0,11,244,3,16,4,11
	.byte	'EVR13',0,1
	.word	497
	.byte	1,7,2,35,0,11
	.byte	'OV13',0,1
	.word	497
	.byte	1,6,2,35,0,11
	.byte	'EVR33',0,1
	.word	497
	.byte	1,5,2,35,0,11
	.byte	'OV33',0,1
	.word	497
	.byte	1,4,2,35,0,11
	.byte	'OVSWD',0,1
	.word	497
	.byte	1,3,2,35,0,11
	.byte	'UV13',0,1
	.word	497
	.byte	1,2,2,35,0,11
	.byte	'UV33',0,1
	.word	497
	.byte	1,1,2,35,0,11
	.byte	'UVSWD',0,1
	.word	497
	.byte	1,0,2,35,0,11
	.byte	'EXTPASS13',0,1
	.word	497
	.byte	1,7,2,35,1,11
	.byte	'EXTPASS33',0,1
	.word	497
	.byte	1,6,2,35,1,11
	.byte	'BGPROK',0,1
	.word	497
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	474
	.byte	21,0,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSTAT_Bits',0,11,130,4,3
	.word	29912
	.byte	10
	.byte	'_Ifx_SCU_EVRTRIM_Bits',0,11,133,4,16,4,11
	.byte	'EVR13TRIM',0,1
	.word	497
	.byte	8,0,2,35,0,11
	.byte	'SDVOUTSEL',0,1
	.word	497
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	514
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	497
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRTRIM_Bits',0,11,139,4,3
	.word	30186
	.byte	10
	.byte	'_Ifx_SCU_EVRUVMON_Bits',0,11,142,4,16,4,11
	.byte	'EVR13UVVAL',0,1
	.word	497
	.byte	8,0,2,35,0,11
	.byte	'EVR33UVVAL',0,1
	.word	497
	.byte	8,0,2,35,1,11
	.byte	'SWDUVVAL',0,1
	.word	497
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	497
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	497
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRUVMON_Bits',0,11,149,4,3
	.word	30325
	.byte	10
	.byte	'_Ifx_SCU_EXTCON_Bits',0,11,152,4,16,4,11
	.byte	'EN0',0,1
	.word	497
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	497
	.byte	1,6,2,35,0,11
	.byte	'SEL0',0,1
	.word	497
	.byte	4,2,2,35,0,11
	.byte	'reserved_6',0,2
	.word	514
	.byte	10,0,2,35,0,11
	.byte	'EN1',0,1
	.word	497
	.byte	1,7,2,35,2,11
	.byte	'NSEL',0,1
	.word	497
	.byte	1,6,2,35,2,11
	.byte	'SEL1',0,1
	.word	497
	.byte	4,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	497
	.byte	2,0,2,35,2,11
	.byte	'DIV1',0,1
	.word	497
	.byte	8,0,2,35,3,0,21
	.byte	'Ifx_SCU_EXTCON_Bits',0,11,163,4,3
	.word	30488
	.byte	10
	.byte	'_Ifx_SCU_FDR_Bits',0,11,166,4,16,4,11
	.byte	'STEP',0,2
	.word	514
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	497
	.byte	4,2,2,35,1,11
	.byte	'DM',0,1
	.word	497
	.byte	2,0,2,35,1,11
	.byte	'RESULT',0,2
	.word	514
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	497
	.byte	5,1,2,35,3,11
	.byte	'DISCLK',0,1
	.word	497
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_FDR_Bits',0,11,174,4,3
	.word	30706
	.byte	10
	.byte	'_Ifx_SCU_FMR_Bits',0,11,177,4,16,4,11
	.byte	'FS0',0,1
	.word	497
	.byte	1,7,2,35,0,11
	.byte	'FS1',0,1
	.word	497
	.byte	1,6,2,35,0,11
	.byte	'FS2',0,1
	.word	497
	.byte	1,5,2,35,0,11
	.byte	'FS3',0,1
	.word	497
	.byte	1,4,2,35,0,11
	.byte	'FS4',0,1
	.word	497
	.byte	1,3,2,35,0,11
	.byte	'FS5',0,1
	.word	497
	.byte	1,2,2,35,0,11
	.byte	'FS6',0,1
	.word	497
	.byte	1,1,2,35,0,11
	.byte	'FS7',0,1
	.word	497
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	497
	.byte	8,0,2,35,1,11
	.byte	'FC0',0,1
	.word	497
	.byte	1,7,2,35,2,11
	.byte	'FC1',0,1
	.word	497
	.byte	1,6,2,35,2,11
	.byte	'FC2',0,1
	.word	497
	.byte	1,5,2,35,2,11
	.byte	'FC3',0,1
	.word	497
	.byte	1,4,2,35,2,11
	.byte	'FC4',0,1
	.word	497
	.byte	1,3,2,35,2,11
	.byte	'FC5',0,1
	.word	497
	.byte	1,2,2,35,2,11
	.byte	'FC6',0,1
	.word	497
	.byte	1,1,2,35,2,11
	.byte	'FC7',0,1
	.word	497
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	497
	.byte	8,0,2,35,3,0,21
	.byte	'Ifx_SCU_FMR_Bits',0,11,197,4,3
	.word	30869
	.byte	10
	.byte	'_Ifx_SCU_ID_Bits',0,11,200,4,16,4,11
	.byte	'MODREV',0,1
	.word	497
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	497
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	514
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_ID_Bits',0,11,205,4,3
	.word	31205
	.byte	10
	.byte	'_Ifx_SCU_IGCR_Bits',0,11,208,4,16,4,11
	.byte	'IPEN00',0,1
	.word	497
	.byte	1,7,2,35,0,11
	.byte	'IPEN01',0,1
	.word	497
	.byte	1,6,2,35,0,11
	.byte	'IPEN02',0,1
	.word	497
	.byte	1,5,2,35,0,11
	.byte	'IPEN03',0,1
	.word	497
	.byte	1,4,2,35,0,11
	.byte	'IPEN04',0,1
	.word	497
	.byte	1,3,2,35,0,11
	.byte	'IPEN05',0,1
	.word	497
	.byte	1,2,2,35,0,11
	.byte	'IPEN06',0,1
	.word	497
	.byte	1,1,2,35,0,11
	.byte	'IPEN07',0,1
	.word	497
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	497
	.byte	5,3,2,35,1,11
	.byte	'GEEN0',0,1
	.word	497
	.byte	1,2,2,35,1,11
	.byte	'IGP0',0,1
	.word	497
	.byte	2,0,2,35,1,11
	.byte	'IPEN10',0,1
	.word	497
	.byte	1,7,2,35,2,11
	.byte	'IPEN11',0,1
	.word	497
	.byte	1,6,2,35,2,11
	.byte	'IPEN12',0,1
	.word	497
	.byte	1,5,2,35,2,11
	.byte	'IPEN13',0,1
	.word	497
	.byte	1,4,2,35,2,11
	.byte	'IPEN14',0,1
	.word	497
	.byte	1,3,2,35,2,11
	.byte	'IPEN15',0,1
	.word	497
	.byte	1,2,2,35,2,11
	.byte	'IPEN16',0,1
	.word	497
	.byte	1,1,2,35,2,11
	.byte	'IPEN17',0,1
	.word	497
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	497
	.byte	5,3,2,35,3,11
	.byte	'GEEN1',0,1
	.word	497
	.byte	1,2,2,35,3,11
	.byte	'IGP1',0,1
	.word	497
	.byte	2,0,2,35,3,0,21
	.byte	'Ifx_SCU_IGCR_Bits',0,11,232,4,3
	.word	31312
	.byte	10
	.byte	'_Ifx_SCU_IN_Bits',0,11,235,4,16,4,11
	.byte	'P0',0,1
	.word	497
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	497
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	474
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_IN_Bits',0,11,240,4,3
	.word	31764
	.byte	10
	.byte	'_Ifx_SCU_IOCR_Bits',0,11,243,4,16,4,11
	.byte	'reserved_0',0,1
	.word	497
	.byte	4,4,2,35,0,11
	.byte	'PC0',0,1
	.word	497
	.byte	4,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	497
	.byte	4,4,2,35,1,11
	.byte	'PC1',0,1
	.word	497
	.byte	4,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	514
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_IOCR_Bits',0,11,250,4,3
	.word	31863
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL0_Bits',0,11,253,4,16,4,11
	.byte	'LBISTREQ',0,1
	.word	497
	.byte	1,7,2,35,0,11
	.byte	'LBISTREQP',0,1
	.word	497
	.byte	1,6,2,35,0,11
	.byte	'PATTERNS',0,2
	.word	514
	.byte	14,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	514
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_LBISTCTRL0_Bits',0,11,131,5,3
	.word	32013
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL1_Bits',0,11,134,5,16,4,11
	.byte	'SEED',0,4
	.word	474
	.byte	23,9,2,35,0,11
	.byte	'reserved_23',0,1
	.word	497
	.byte	1,0,2,35,2,11
	.byte	'SPLITSH',0,1
	.word	497
	.byte	3,5,2,35,3,11
	.byte	'BODY',0,1
	.word	497
	.byte	1,4,2,35,3,11
	.byte	'LBISTFREQU',0,1
	.word	497
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_LBISTCTRL1_Bits',0,11,141,5,3
	.word	32162
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL2_Bits',0,11,144,5,16,4,11
	.byte	'SIGNATURE',0,4
	.word	474
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	497
	.byte	7,1,2,35,3,11
	.byte	'LBISTDONE',0,1
	.word	497
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_LBISTCTRL2_Bits',0,11,149,5,3
	.word	32323
	.byte	10
	.byte	'_Ifx_SCU_LCLCON_Bits',0,11,152,5,16,4,11
	.byte	'reserved_0',0,2
	.word	514
	.byte	16,0,2,35,0,11
	.byte	'LS',0,1
	.word	497
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,2
	.word	514
	.byte	14,1,2,35,2,11
	.byte	'LSEN',0,1
	.word	497
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_LCLCON_Bits',0,11,158,5,3
	.word	32453
	.byte	10
	.byte	'_Ifx_SCU_LCLTEST_Bits',0,11,161,5,16,4,11
	.byte	'LCLT0',0,1
	.word	497
	.byte	1,7,2,35,0,11
	.byte	'LCLT1',0,1
	.word	497
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	474
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_LCLTEST_Bits',0,11,166,5,3
	.word	32585
	.byte	10
	.byte	'_Ifx_SCU_MANID_Bits',0,11,169,5,16,4,11
	.byte	'DEPT',0,1
	.word	497
	.byte	5,3,2,35,0,11
	.byte	'MANUF',0,2
	.word	514
	.byte	11,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	514
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_MANID_Bits',0,11,174,5,3
	.word	32700
	.byte	10
	.byte	'_Ifx_SCU_OMR_Bits',0,11,177,5,16,4,11
	.byte	'PS0',0,1
	.word	497
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	497
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	514
	.byte	14,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	497
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	497
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	514
	.byte	14,0,2,35,2,0,21
	.byte	'Ifx_SCU_OMR_Bits',0,11,185,5,3
	.word	32811
	.byte	10
	.byte	'_Ifx_SCU_OSCCON_Bits',0,11,188,5,16,4,11
	.byte	'reserved_0',0,1
	.word	497
	.byte	1,7,2,35,0,11
	.byte	'PLLLV',0,1
	.word	497
	.byte	1,6,2,35,0,11
	.byte	'OSCRES',0,1
	.word	497
	.byte	1,5,2,35,0,11
	.byte	'GAINSEL',0,1
	.word	497
	.byte	2,3,2,35,0,11
	.byte	'MODE',0,1
	.word	497
	.byte	2,1,2,35,0,11
	.byte	'SHBY',0,1
	.word	497
	.byte	1,0,2,35,0,11
	.byte	'PLLHV',0,1
	.word	497
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	497
	.byte	1,6,2,35,1,11
	.byte	'X1D',0,1
	.word	497
	.byte	1,5,2,35,1,11
	.byte	'X1DEN',0,1
	.word	497
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	497
	.byte	4,0,2,35,1,11
	.byte	'OSCVAL',0,1
	.word	497
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	497
	.byte	2,1,2,35,2,11
	.byte	'APREN',0,1
	.word	497
	.byte	1,0,2,35,2,11
	.byte	'CAP0EN',0,1
	.word	497
	.byte	1,7,2,35,3,11
	.byte	'CAP1EN',0,1
	.word	497
	.byte	1,6,2,35,3,11
	.byte	'CAP2EN',0,1
	.word	497
	.byte	1,5,2,35,3,11
	.byte	'CAP3EN',0,1
	.word	497
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	497
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_OSCCON_Bits',0,11,209,5,3
	.word	32969
	.byte	10
	.byte	'_Ifx_SCU_OUT_Bits',0,11,212,5,16,4,11
	.byte	'P0',0,1
	.word	497
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	497
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	474
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_OUT_Bits',0,11,217,5,3
	.word	33381
	.byte	10
	.byte	'_Ifx_SCU_OVCCON_Bits',0,11,220,5,16,4,11
	.byte	'CSEL0',0,1
	.word	497
	.byte	1,7,2,35,0,11
	.byte	'CSEL1',0,1
	.word	497
	.byte	1,6,2,35,0,11
	.byte	'CSEL2',0,1
	.word	497
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,2
	.word	514
	.byte	13,0,2,35,0,11
	.byte	'OVSTRT',0,1
	.word	497
	.byte	1,7,2,35,2,11
	.byte	'OVSTP',0,1
	.word	497
	.byte	1,6,2,35,2,11
	.byte	'DCINVAL',0,1
	.word	497
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	497
	.byte	5,0,2,35,2,11
	.byte	'OVCONF',0,1
	.word	497
	.byte	1,7,2,35,3,11
	.byte	'POVCONF',0,1
	.word	497
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	497
	.byte	6,0,2,35,3,0,21
	.byte	'Ifx_SCU_OVCCON_Bits',0,11,233,5,3
	.word	33482
	.byte	10
	.byte	'_Ifx_SCU_OVCENABLE_Bits',0,11,236,5,16,4,11
	.byte	'OVEN0',0,1
	.word	497
	.byte	1,7,2,35,0,11
	.byte	'OVEN1',0,1
	.word	497
	.byte	1,6,2,35,0,11
	.byte	'OVEN2',0,1
	.word	497
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	474
	.byte	29,0,2,35,0,0,21
	.byte	'Ifx_SCU_OVCENABLE_Bits',0,11,242,5,3
	.word	33749
	.byte	10
	.byte	'_Ifx_SCU_PDISC_Bits',0,11,245,5,16,4,11
	.byte	'PDIS0',0,1
	.word	497
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	497
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	474
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_PDISC_Bits',0,11,250,5,3
	.word	33885
	.byte	10
	.byte	'_Ifx_SCU_PDR_Bits',0,11,253,5,16,4,11
	.byte	'PD0',0,1
	.word	497
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	497
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	497
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	497
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	474
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_SCU_PDR_Bits',0,11,132,6,3
	.word	33996
	.byte	10
	.byte	'_Ifx_SCU_PDRR_Bits',0,11,135,6,16,4,11
	.byte	'PDR0',0,1
	.word	497
	.byte	1,7,2,35,0,11
	.byte	'PDR1',0,1
	.word	497
	.byte	1,6,2,35,0,11
	.byte	'PDR2',0,1
	.word	497
	.byte	1,5,2,35,0,11
	.byte	'PDR3',0,1
	.word	497
	.byte	1,4,2,35,0,11
	.byte	'PDR4',0,1
	.word	497
	.byte	1,3,2,35,0,11
	.byte	'PDR5',0,1
	.word	497
	.byte	1,2,2,35,0,11
	.byte	'PDR6',0,1
	.word	497
	.byte	1,1,2,35,0,11
	.byte	'PDR7',0,1
	.word	497
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	474
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_SCU_PDRR_Bits',0,11,146,6,3
	.word	34129
	.byte	10
	.byte	'_Ifx_SCU_PLLCON0_Bits',0,11,149,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	497
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	497
	.byte	1,6,2,35,0,11
	.byte	'MODEN',0,1
	.word	497
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	497
	.byte	1,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	497
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	497
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	497
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	514
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	497
	.byte	7,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	497
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	497
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	497
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	497
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	497
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	497
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_PLLCON0_Bits',0,11,166,6,3
	.word	34332
	.byte	10
	.byte	'_Ifx_SCU_PLLCON1_Bits',0,11,169,6,16,4,11
	.byte	'K2DIV',0,1
	.word	497
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	497
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	497
	.byte	7,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	497
	.byte	1,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	497
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	514
	.byte	9,0,2,35,2,0,21
	.byte	'Ifx_SCU_PLLCON1_Bits',0,11,177,6,3
	.word	34688
	.byte	10
	.byte	'_Ifx_SCU_PLLCON2_Bits',0,11,180,6,16,4,11
	.byte	'MODCFG',0,2
	.word	514
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	514
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_PLLCON2_Bits',0,11,184,6,3
	.word	34866
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON0_Bits',0,11,187,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	497
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	497
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	497
	.byte	2,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	497
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	497
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	497
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	514
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	497
	.byte	5,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	497
	.byte	2,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	497
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	497
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	497
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	497
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	497
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	497
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_PLLERAYCON0_Bits',0,11,204,6,3
	.word	34966
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON1_Bits',0,11,207,6,16,4,11
	.byte	'K2DIV',0,1
	.word	497
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	497
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	497
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	497
	.byte	4,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	497
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	514
	.byte	9,0,2,35,2,0,21
	.byte	'Ifx_SCU_PLLERAYCON1_Bits',0,11,215,6,3
	.word	35336
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYSTAT_Bits',0,11,218,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	497
	.byte	1,7,2,35,0,11
	.byte	'PWDSTAT',0,1
	.word	497
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	497
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	497
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	497
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	497
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	474
	.byte	26,0,2,35,0,0,21
	.byte	'Ifx_SCU_PLLERAYSTAT_Bits',0,11,227,6,3
	.word	35522
	.byte	10
	.byte	'_Ifx_SCU_PLLSTAT_Bits',0,11,230,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	497
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	497
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	497
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	497
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	497
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	497
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	497
	.byte	1,1,2,35,0,11
	.byte	'MODRUN',0,1
	.word	497
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	474
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_SCU_PLLSTAT_Bits',0,11,241,6,3
	.word	35720
	.byte	10
	.byte	'_Ifx_SCU_PMCSR_Bits',0,11,244,6,16,4,11
	.byte	'REQSLP',0,1
	.word	497
	.byte	2,6,2,35,0,11
	.byte	'SMUSLP',0,1
	.word	497
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	497
	.byte	5,0,2,35,0,11
	.byte	'PMST',0,1
	.word	497
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	474
	.byte	21,0,2,35,0,0,21
	.byte	'Ifx_SCU_PMCSR_Bits',0,11,251,6,3
	.word	35953
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR0_Bits',0,11,254,6,16,4,11
	.byte	'reserved_0',0,1
	.word	497
	.byte	1,7,2,35,0,11
	.byte	'ESR1WKEN',0,1
	.word	497
	.byte	1,6,2,35,0,11
	.byte	'PINAWKEN',0,1
	.word	497
	.byte	1,5,2,35,0,11
	.byte	'PINBWKEN',0,1
	.word	497
	.byte	1,4,2,35,0,11
	.byte	'ESR0DFEN',0,1
	.word	497
	.byte	1,3,2,35,0,11
	.byte	'ESR0EDCON',0,1
	.word	497
	.byte	2,1,2,35,0,11
	.byte	'ESR1DFEN',0,1
	.word	497
	.byte	1,0,2,35,0,11
	.byte	'ESR1EDCON',0,1
	.word	497
	.byte	2,6,2,35,1,11
	.byte	'PINADFEN',0,1
	.word	497
	.byte	1,5,2,35,1,11
	.byte	'PINAEDCON',0,1
	.word	497
	.byte	2,3,2,35,1,11
	.byte	'PINBDFEN',0,1
	.word	497
	.byte	1,2,2,35,1,11
	.byte	'PINBEDCON',0,1
	.word	497
	.byte	2,0,2,35,1,11
	.byte	'SCREN',0,1
	.word	497
	.byte	1,7,2,35,2,11
	.byte	'STBYRAMSEL',0,1
	.word	497
	.byte	2,5,2,35,2,11
	.byte	'SCRCLKSEL',0,1
	.word	497
	.byte	1,4,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	497
	.byte	1,3,2,35,2,11
	.byte	'TRISTEN',0,1
	.word	497
	.byte	1,2,2,35,2,11
	.byte	'TRISTREQ',0,1
	.word	497
	.byte	1,1,2,35,2,11
	.byte	'PORSTDF',0,1
	.word	497
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	497
	.byte	1,7,2,35,3,11
	.byte	'DCDCSYNC',0,1
	.word	497
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	497
	.byte	3,3,2,35,3,11
	.byte	'ESR0TRIST',0,1
	.word	497
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	497
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	497
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_PMSWCR0_Bits',0,11,153,7,3
	.word	36105
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR1_Bits',0,11,156,7,16,4,11
	.byte	'SCRSTEN',0,1
	.word	497
	.byte	1,7,2,35,0,11
	.byte	'SCRSTREQ',0,1
	.word	497
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	497
	.byte	6,0,2,35,0,11
	.byte	'CPUIDLSEL',0,1
	.word	497
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	497
	.byte	1,4,2,35,1,11
	.byte	'IRADIS',0,1
	.word	497
	.byte	1,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	497
	.byte	3,0,2,35,1,11
	.byte	'SCRCFG',0,1
	.word	497
	.byte	8,0,2,35,2,11
	.byte	'CPUSEL',0,1
	.word	497
	.byte	3,5,2,35,3,11
	.byte	'STBYEVEN',0,1
	.word	497
	.byte	1,4,2,35,3,11
	.byte	'STBYEV',0,1
	.word	497
	.byte	3,1,2,35,3,11
	.byte	'LCK',0,1
	.word	497
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_PMSWCR1_Bits',0,11,170,7,3
	.word	36672
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR2_Bits',0,11,173,7,16,4,11
	.byte	'SCRINT',0,1
	.word	497
	.byte	8,0,2,35,0,11
	.byte	'BUSY',0,1
	.word	497
	.byte	1,7,2,35,1,11
	.byte	'SCRECC',0,1
	.word	497
	.byte	1,6,2,35,1,11
	.byte	'SCRWDT',0,1
	.word	497
	.byte	1,5,2,35,1,11
	.byte	'SCRRST',0,1
	.word	497
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	497
	.byte	4,0,2,35,1,11
	.byte	'TCINT',0,1
	.word	497
	.byte	8,0,2,35,2,11
	.byte	'TCINTREQ',0,1
	.word	497
	.byte	1,7,2,35,3,11
	.byte	'SMURST',0,1
	.word	497
	.byte	1,6,2,35,3,11
	.byte	'RST',0,1
	.word	497
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	497
	.byte	4,1,2,35,3,11
	.byte	'LCK',0,1
	.word	497
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_PMSWCR2_Bits',0,11,187,7,3
	.word	36966
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTAT_Bits',0,11,190,7,16,4,11
	.byte	'reserved_0',0,1
	.word	497
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKP',0,1
	.word	497
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUN',0,1
	.word	497
	.byte	1,4,2,35,0,11
	.byte	'PINAWKP',0,1
	.word	497
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUN',0,1
	.word	497
	.byte	1,2,2,35,0,11
	.byte	'PINBWKP',0,1
	.word	497
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUN',0,1
	.word	497
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	497
	.byte	1,7,2,35,1,11
	.byte	'PORSTDF',0,1
	.word	497
	.byte	1,6,2,35,1,11
	.byte	'HWCFGEVR',0,1
	.word	497
	.byte	3,3,2,35,1,11
	.byte	'STBYRAM',0,1
	.word	497
	.byte	2,1,2,35,1,11
	.byte	'TRIST',0,1
	.word	497
	.byte	1,0,2,35,1,11
	.byte	'SCRST',0,1
	.word	497
	.byte	1,7,2,35,2,11
	.byte	'SCRWKP',0,1
	.word	497
	.byte	1,6,2,35,2,11
	.byte	'SCR',0,1
	.word	497
	.byte	1,5,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	497
	.byte	1,4,2,35,2,11
	.byte	'ESR1WKEN',0,1
	.word	497
	.byte	1,3,2,35,2,11
	.byte	'PINAWKEN',0,1
	.word	497
	.byte	1,2,2,35,2,11
	.byte	'PINBWKEN',0,1
	.word	497
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	514
	.byte	4,5,2,35,2,11
	.byte	'ESR0TRIST',0,1
	.word	497
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	497
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_PMSWSTAT_Bits',0,11,214,7,3
	.word	37244
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTATCLR_Bits',0,11,217,7,16,4,11
	.byte	'reserved_0',0,1
	.word	497
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKPCLR',0,1
	.word	497
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUNCLR',0,1
	.word	497
	.byte	1,4,2,35,0,11
	.byte	'PINAWKPCLR',0,1
	.word	497
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUNCLR',0,1
	.word	497
	.byte	1,2,2,35,0,11
	.byte	'PINBWKPCLR',0,1
	.word	497
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUNCLR',0,1
	.word	497
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	497
	.byte	8,0,2,35,1,11
	.byte	'SCRSTCLR',0,1
	.word	497
	.byte	1,7,2,35,2,11
	.byte	'SCRWKPCLR',0,1
	.word	497
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	514
	.byte	14,0,2,35,2,0,21
	.byte	'Ifx_SCU_PMSWSTATCLR_Bits',0,11,230,7,3
	.word	37740
	.byte	10
	.byte	'_Ifx_SCU_RSTCON2_Bits',0,11,233,7,16,4,11
	.byte	'reserved_0',0,1
	.word	497
	.byte	1,7,2,35,0,11
	.byte	'CLRC',0,1
	.word	497
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	514
	.byte	10,4,2,35,0,11
	.byte	'CSS0',0,1
	.word	497
	.byte	1,3,2,35,1,11
	.byte	'CSS1',0,1
	.word	497
	.byte	1,2,2,35,1,11
	.byte	'CSS2',0,1
	.word	497
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	497
	.byte	1,0,2,35,1,11
	.byte	'USRINFO',0,2
	.word	514
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_RSTCON2_Bits',0,11,243,7,3
	.word	38053
	.byte	10
	.byte	'_Ifx_SCU_RSTCON_Bits',0,11,246,7,16,4,11
	.byte	'ESR0',0,1
	.word	497
	.byte	2,6,2,35,0,11
	.byte	'ESR1',0,1
	.word	497
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	497
	.byte	2,2,2,35,0,11
	.byte	'SMU',0,1
	.word	497
	.byte	2,0,2,35,0,11
	.byte	'SW',0,1
	.word	497
	.byte	2,6,2,35,1,11
	.byte	'STM0',0,1
	.word	497
	.byte	2,4,2,35,1,11
	.byte	'STM1',0,1
	.word	497
	.byte	2,2,2,35,1,11
	.byte	'STM2',0,1
	.word	497
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	514
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_RSTCON_Bits',0,11,129,8,3
	.word	38262
	.byte	10
	.byte	'_Ifx_SCU_RSTSTAT_Bits',0,11,132,8,16,4,11
	.byte	'ESR0',0,1
	.word	497
	.byte	1,7,2,35,0,11
	.byte	'ESR1',0,1
	.word	497
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	497
	.byte	1,5,2,35,0,11
	.byte	'SMU',0,1
	.word	497
	.byte	1,4,2,35,0,11
	.byte	'SW',0,1
	.word	497
	.byte	1,3,2,35,0,11
	.byte	'STM0',0,1
	.word	497
	.byte	1,2,2,35,0,11
	.byte	'STM1',0,1
	.word	497
	.byte	1,1,2,35,0,11
	.byte	'STM2',0,1
	.word	497
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	497
	.byte	8,0,2,35,1,11
	.byte	'PORST',0,1
	.word	497
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	497
	.byte	1,6,2,35,2,11
	.byte	'CB0',0,1
	.word	497
	.byte	1,5,2,35,2,11
	.byte	'CB1',0,1
	.word	497
	.byte	1,4,2,35,2,11
	.byte	'CB3',0,1
	.word	497
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	497
	.byte	2,1,2,35,2,11
	.byte	'EVR13',0,1
	.word	497
	.byte	1,0,2,35,2,11
	.byte	'EVR33',0,1
	.word	497
	.byte	1,7,2,35,3,11
	.byte	'SWD',0,1
	.word	497
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	497
	.byte	2,4,2,35,3,11
	.byte	'STBYR',0,1
	.word	497
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	497
	.byte	3,0,2,35,3,0,21
	.byte	'Ifx_SCU_RSTSTAT_Bits',0,11,155,8,3
	.word	38473
	.byte	10
	.byte	'_Ifx_SCU_SAFECON_Bits',0,11,158,8,16,4,11
	.byte	'HBT',0,1
	.word	497
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	474
	.byte	31,0,2,35,0,0,21
	.byte	'Ifx_SCU_SAFECON_Bits',0,11,162,8,3
	.word	38905
	.byte	10
	.byte	'_Ifx_SCU_STSTAT_Bits',0,11,165,8,16,4,11
	.byte	'HWCFG',0,1
	.word	497
	.byte	8,0,2,35,0,11
	.byte	'FTM',0,1
	.word	497
	.byte	7,1,2,35,1,11
	.byte	'MODE',0,1
	.word	497
	.byte	1,0,2,35,1,11
	.byte	'FCBAE',0,1
	.word	497
	.byte	1,7,2,35,2,11
	.byte	'LUDIS',0,1
	.word	497
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	497
	.byte	1,5,2,35,2,11
	.byte	'TRSTL',0,1
	.word	497
	.byte	1,4,2,35,2,11
	.byte	'SPDEN',0,1
	.word	497
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	497
	.byte	3,0,2,35,2,11
	.byte	'RAMINT',0,1
	.word	497
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	497
	.byte	7,0,2,35,3,0,21
	.byte	'Ifx_SCU_STSTAT_Bits',0,11,178,8,3
	.word	39001
	.byte	10
	.byte	'_Ifx_SCU_SWRSTCON_Bits',0,11,181,8,16,4,11
	.byte	'reserved_0',0,1
	.word	497
	.byte	1,7,2,35,0,11
	.byte	'SWRSTREQ',0,1
	.word	497
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	474
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_SWRSTCON_Bits',0,11,186,8,3
	.word	39261
	.byte	10
	.byte	'_Ifx_SCU_SYSCON_Bits',0,11,189,8,16,4,11
	.byte	'CCTRIG0',0,1
	.word	497
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	497
	.byte	1,6,2,35,0,11
	.byte	'RAMINTM',0,1
	.word	497
	.byte	2,4,2,35,0,11
	.byte	'SETLUDIS',0,1
	.word	497
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	497
	.byte	3,0,2,35,0,11
	.byte	'DATM',0,1
	.word	497
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,4
	.word	474
	.byte	23,0,2,35,0,0,21
	.byte	'Ifx_SCU_SYSCON_Bits',0,11,198,8,3
	.word	39386
	.byte	10
	.byte	'_Ifx_SCU_TRAPCLR_Bits',0,11,201,8,16,4,11
	.byte	'ESR0T',0,1
	.word	497
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	497
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	497
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	497
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	474
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPCLR_Bits',0,11,208,8,3
	.word	39583
	.byte	10
	.byte	'_Ifx_SCU_TRAPDIS_Bits',0,11,211,8,16,4,11
	.byte	'ESR0T',0,1
	.word	497
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	497
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	497
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	497
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	474
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPDIS_Bits',0,11,218,8,3
	.word	39736
	.byte	10
	.byte	'_Ifx_SCU_TRAPSET_Bits',0,11,221,8,16,4,11
	.byte	'ESR0T',0,1
	.word	497
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	497
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	497
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	497
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	474
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPSET_Bits',0,11,228,8,3
	.word	39889
	.byte	10
	.byte	'_Ifx_SCU_TRAPSTAT_Bits',0,11,231,8,16,4,11
	.byte	'ESR0T',0,1
	.word	497
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	497
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	497
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	497
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	474
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPSTAT_Bits',0,11,238,8,3
	.word	40042
	.byte	7
	.byte	'unsigned int',0,4,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON0_Bits',0,11,241,8,16,4,11
	.byte	'ENDINIT',0,4
	.word	40197
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	40197
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	40197
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	40197
	.byte	16,0,2,35,0,0,21
	.byte	'Ifx_SCU_WDTCPU_CON0_Bits',0,11,247,8,3
	.word	40213
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU_CON1_Bits',0,11,250,8,16,4,11
	.byte	'reserved_0',0,1
	.word	497
	.byte	2,6,2,35,0,11
	.byte	'IR0',0,1
	.word	497
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	497
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	497
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	497
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	497
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	497
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	497
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	497
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	514
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_WDTCPU_CON1_Bits',0,11,134,9,3
	.word	40343
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU_SR_Bits',0,11,137,9,16,4,11
	.byte	'AE',0,1
	.word	497
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	497
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	497
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	497
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	497
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	497
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	497
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	497
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	497
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	497
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	514
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_WDTCPU_SR_Bits',0,11,150,9,3
	.word	40581
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON0_Bits',0,11,153,9,16,4,11
	.byte	'ENDINIT',0,4
	.word	40197
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	40197
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	40197
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	40197
	.byte	16,0,2,35,0,0,21
	.byte	'Ifx_SCU_WDTS_CON0_Bits',0,11,159,9,3
	.word	40804
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON1_Bits',0,11,162,9,16,4,11
	.byte	'CLRIRF',0,1
	.word	497
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	497
	.byte	1,6,2,35,0,11
	.byte	'IR0',0,1
	.word	497
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	497
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	497
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	497
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	497
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	497
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	497
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	497
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	514
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_WDTS_CON1_Bits',0,11,175,9,3
	.word	40930
	.byte	10
	.byte	'_Ifx_SCU_WDTS_SR_Bits',0,11,178,9,16,4,11
	.byte	'AE',0,1
	.word	497
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	497
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	497
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	497
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	497
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	497
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	497
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	497
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	497
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	497
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	514
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_WDTS_SR_Bits',0,11,191,9,3
	.word	41182
	.byte	12,11,199,9,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22707
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ACCEN0',0,11,204,9,3
	.word	41401
	.byte	12,11,207,9,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23264
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ACCEN1',0,11,212,9,3
	.word	41465
	.byte	12,11,215,9,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23341
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ARSTDIS',0,11,220,9,3
	.word	41529
	.byte	12,11,223,9,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23477
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON0',0,11,228,9,3
	.word	41594
	.byte	12,11,231,9,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23757
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON1',0,11,236,9,3
	.word	41659
	.byte	12,11,239,9,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23995
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON2',0,11,244,9,3
	.word	41724
	.byte	12,11,247,9,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24123
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON3',0,11,252,9,3
	.word	41789
	.byte	12,11,255,9,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24366
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON4',0,11,132,10,3
	.word	41854
	.byte	12,11,135,10,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24601
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON5',0,11,140,10,3
	.word	41919
	.byte	12,11,143,10,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24729
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON6',0,11,148,10,3
	.word	41984
	.byte	12,11,151,10,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24829
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON7',0,11,156,10,3
	.word	42049
	.byte	12,11,159,10,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24929
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CHIPID',0,11,164,10,3
	.word	42114
	.byte	12,11,167,10,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25137
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_DTSCON',0,11,172,10,3
	.word	42178
	.byte	12,11,175,10,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25302
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_DTSLIM',0,11,180,10,3
	.word	42242
	.byte	12,11,183,10,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25485
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_DTSSTAT',0,11,188,10,3
	.word	42306
	.byte	12,11,191,10,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25639
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EICR',0,11,196,10,3
	.word	42371
	.byte	12,11,199,10,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26003
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EIFR',0,11,204,10,3
	.word	42433
	.byte	12,11,207,10,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26214
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EMSR',0,11,212,10,3
	.word	42495
	.byte	12,11,215,10,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26466
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ESRCFG',0,11,220,10,3
	.word	42557
	.byte	12,11,223,10,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26584
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ESROCFG',0,11,228,10,3
	.word	42621
	.byte	12,11,231,10,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26695
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVR13CON',0,11,236,10,3
	.word	42686
	.byte	12,11,239,10,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26858
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVR33CON',0,11,244,10,3
	.word	42752
	.byte	12,11,247,10,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27021
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRADCSTAT',0,11,252,10,3
	.word	42818
	.byte	12,11,255,10,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27179
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRDVSTAT',0,11,132,11,3
	.word	42886
	.byte	12,11,135,11,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27344
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRMONCTRL',0,11,140,11,3
	.word	42953
	.byte	12,11,143,11,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27673
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVROSCCTRL',0,11,148,11,3
	.word	43021
	.byte	12,11,151,11,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27894
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVROVMON',0,11,156,11,3
	.word	43089
	.byte	12,11,159,11,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28057
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRRSTCON',0,11,164,11,3
	.word	43155
	.byte	12,11,167,11,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28329
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF1',0,11,172,11,3
	.word	43222
	.byte	12,11,175,11,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28482
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF2',0,11,180,11,3
	.word	43291
	.byte	12,11,183,11,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28638
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF3',0,11,188,11,3
	.word	43360
	.byte	12,11,191,11,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28800
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF4',0,11,196,11,3
	.word	43429
	.byte	12,11,199,11,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28943
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF5',0,11,204,11,3
	.word	43498
	.byte	12,11,207,11,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29108
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF6',0,11,212,11,3
	.word	43567
	.byte	12,11,215,11,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29253
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCTRL1',0,11,220,11,3
	.word	43636
	.byte	12,11,223,11,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29434
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCTRL2',0,11,228,11,3
	.word	43704
	.byte	12,11,231,11,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29608
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCTRL3',0,11,236,11,3
	.word	43772
	.byte	12,11,239,11,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29768
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCTRL4',0,11,244,11,3
	.word	43840
	.byte	12,11,247,11,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29912
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSTAT',0,11,252,11,3
	.word	43908
	.byte	12,11,255,11,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30186
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRTRIM',0,11,132,12,3
	.word	43973
	.byte	12,11,135,12,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30325
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRUVMON',0,11,140,12,3
	.word	44038
	.byte	12,11,143,12,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30488
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EXTCON',0,11,148,12,3
	.word	44104
	.byte	12,11,151,12,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30706
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_FDR',0,11,156,12,3
	.word	44168
	.byte	12,11,159,12,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30869
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_FMR',0,11,164,12,3
	.word	44229
	.byte	12,11,167,12,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31205
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ID',0,11,172,12,3
	.word	44290
	.byte	12,11,175,12,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31312
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_IGCR',0,11,180,12,3
	.word	44350
	.byte	12,11,183,12,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31764
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_IN',0,11,188,12,3
	.word	44412
	.byte	12,11,191,12,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31863
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_IOCR',0,11,196,12,3
	.word	44472
	.byte	12,11,199,12,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32013
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_LBISTCTRL0',0,11,204,12,3
	.word	44534
	.byte	12,11,207,12,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32162
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_LBISTCTRL1',0,11,212,12,3
	.word	44602
	.byte	12,11,215,12,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32323
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_LBISTCTRL2',0,11,220,12,3
	.word	44670
	.byte	12,11,223,12,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32453
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_LCLCON',0,11,228,12,3
	.word	44738
	.byte	12,11,231,12,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32585
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_LCLTEST',0,11,236,12,3
	.word	44802
	.byte	12,11,239,12,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32700
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_MANID',0,11,244,12,3
	.word	44867
	.byte	12,11,247,12,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32811
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_OMR',0,11,252,12,3
	.word	44930
	.byte	12,11,255,12,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32969
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_OSCCON',0,11,132,13,3
	.word	44991
	.byte	12,11,135,13,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33381
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_OUT',0,11,140,13,3
	.word	45055
	.byte	12,11,143,13,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33482
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_OVCCON',0,11,148,13,3
	.word	45116
	.byte	12,11,151,13,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33749
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_OVCENABLE',0,11,156,13,3
	.word	45180
	.byte	12,11,159,13,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33885
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PDISC',0,11,164,13,3
	.word	45247
	.byte	12,11,167,13,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33996
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PDR',0,11,172,13,3
	.word	45310
	.byte	12,11,175,13,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34129
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PDRR',0,11,180,13,3
	.word	45371
	.byte	12,11,183,13,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34332
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLCON0',0,11,188,13,3
	.word	45433
	.byte	12,11,191,13,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34688
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLCON1',0,11,196,13,3
	.word	45498
	.byte	12,11,199,13,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34866
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLCON2',0,11,204,13,3
	.word	45563
	.byte	12,11,207,13,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34966
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLERAYCON0',0,11,212,13,3
	.word	45628
	.byte	12,11,215,13,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35336
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLERAYCON1',0,11,220,13,3
	.word	45697
	.byte	12,11,223,13,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35522
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLERAYSTAT',0,11,228,13,3
	.word	45766
	.byte	12,11,231,13,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35720
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLSTAT',0,11,236,13,3
	.word	45835
	.byte	12,11,239,13,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35953
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMCSR',0,11,244,13,3
	.word	45900
	.byte	12,11,247,13,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36105
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMSWCR0',0,11,252,13,3
	.word	45963
	.byte	12,11,255,13,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36672
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMSWCR1',0,11,132,14,3
	.word	46028
	.byte	12,11,135,14,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36966
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMSWCR2',0,11,140,14,3
	.word	46093
	.byte	12,11,143,14,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37244
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMSWSTAT',0,11,148,14,3
	.word	46158
	.byte	12,11,151,14,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37740
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMSWSTATCLR',0,11,156,14,3
	.word	46224
	.byte	12,11,159,14,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38262
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_RSTCON',0,11,164,14,3
	.word	46293
	.byte	12,11,167,14,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38053
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_RSTCON2',0,11,172,14,3
	.word	46357
	.byte	12,11,175,14,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38473
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_RSTSTAT',0,11,180,14,3
	.word	46422
	.byte	12,11,183,14,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38905
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_SAFECON',0,11,188,14,3
	.word	46487
	.byte	12,11,191,14,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39001
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_STSTAT',0,11,196,14,3
	.word	46552
	.byte	12,11,199,14,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39261
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_SWRSTCON',0,11,204,14,3
	.word	46616
	.byte	12,11,207,14,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39386
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_SYSCON',0,11,212,14,3
	.word	46682
	.byte	12,11,215,14,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39583
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPCLR',0,11,220,14,3
	.word	46746
	.byte	12,11,223,14,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39736
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPDIS',0,11,228,14,3
	.word	46811
	.byte	12,11,231,14,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39889
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPSET',0,11,236,14,3
	.word	46876
	.byte	12,11,239,14,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40042
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPSTAT',0,11,244,14,3
	.word	46941
	.byte	12,11,247,14,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40213
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTCPU_CON0',0,11,252,14,3
	.word	47007
	.byte	12,11,255,14,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40343
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTCPU_CON1',0,11,132,15,3
	.word	47076
	.byte	12,11,135,15,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40581
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTCPU_SR',0,11,140,15,3
	.word	47145
	.byte	12,11,143,15,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40804
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTS_CON0',0,11,148,15,3
	.word	47212
	.byte	12,11,151,15,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40930
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTS_CON1',0,11,156,15,3
	.word	47279
	.byte	12,11,159,15,9,4,13
	.byte	'U',0
	.word	474
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	490
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41182
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTS_SR',0,11,164,15,3
	.word	47346
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU',0,11,175,15,25,12,13
	.byte	'CON0',0
	.word	47007
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	47076
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	47145
	.byte	4,2,35,8,0,16
	.word	47411
	.byte	21
	.byte	'Ifx_SCU_WDTCPU',0,11,180,15,3
	.word	47474
	.byte	10
	.byte	'_Ifx_SCU_WDTS',0,11,183,15,25,12,13
	.byte	'CON0',0
	.word	47212
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	47279
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	47346
	.byte	4,2,35,8,0,16
	.word	47503
	.byte	21
	.byte	'Ifx_SCU_WDTS',0,11,188,15,3
	.word	47564
	.byte	17,3,83,9,1,18
	.byte	'IfxPort_InputMode_undefined',0,127,18
	.byte	'IfxPort_InputMode_noPullDevice',0,0,18
	.byte	'IfxPort_InputMode_pullDown',0,8,18
	.byte	'IfxPort_InputMode_pullUp',0,16,0,21
	.byte	'IfxPort_InputMode',0,3,89,3
	.word	47591
	.byte	17,3,120,9,1,18
	.byte	'IfxPort_OutputIdx_general',0,128,1,18
	.byte	'IfxPort_OutputIdx_alt1',0,136,1,18
	.byte	'IfxPort_OutputIdx_alt2',0,144,1,18
	.byte	'IfxPort_OutputIdx_alt3',0,152,1,18
	.byte	'IfxPort_OutputIdx_alt4',0,160,1,18
	.byte	'IfxPort_OutputIdx_alt5',0,168,1,18
	.byte	'IfxPort_OutputIdx_alt6',0,176,1,18
	.byte	'IfxPort_OutputIdx_alt7',0,184,1,0,21
	.byte	'IfxPort_OutputIdx',0,3,130,1,3
	.word	47742
	.byte	17,3,134,1,9,1,18
	.byte	'IfxPort_OutputMode_pushPull',0,128,1,18
	.byte	'IfxPort_OutputMode_openDrain',0,192,1,0,21
	.byte	'IfxPort_OutputMode',0,3,138,1,3
	.word	47986
	.byte	17,3,144,1,9,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed1',0,0,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed2',0,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed3',0,2,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed4',0,3,18
	.byte	'IfxPort_PadDriver_lvdsSpeed1',0,4,18
	.byte	'IfxPort_PadDriver_lvdsSpeed2',0,5,18
	.byte	'IfxPort_PadDriver_lvdsSpeed3',0,6,18
	.byte	'IfxPort_PadDriver_lvdsSpeed4',0,7,18
	.byte	'IfxPort_PadDriver_ttlSpeed1',0,8,18
	.byte	'IfxPort_PadDriver_ttlSpeed2',0,9,18
	.byte	'IfxPort_PadDriver_ttlSpeed3',0,10,18
	.byte	'IfxPort_PadDriver_ttlSpeed4',0,11,0,21
	.byte	'IfxPort_PadDriver',0,3,158,1,3
	.word	48084
	.byte	21
	.byte	'IfxPort_State',0,3,178,1,3
	.word	8294
	.byte	24,3,190,1,9,8,13
	.byte	'port',0
	.word	8289
	.byte	4,2,35,0,13
	.byte	'pinIndex',0
	.word	497
	.byte	1,2,35,4,0,21
	.byte	'IfxPort_Pin',0,3,194,1,3
	.word	48549
	.byte	16
	.word	20229
	.byte	3
	.word	48609
	.byte	24,12,59,15,20,13
	.byte	'module',0
	.word	48614
	.byte	4,2,35,0,13
	.byte	'nodeId',0
	.word	21096
	.byte	1,2,35,4,13
	.byte	'pin',0
	.word	48549
	.byte	8,2,35,8,13
	.byte	'select',0
	.word	20958
	.byte	1,2,35,16,0,25
	.word	48619
	.byte	21
	.byte	'IfxMultican_Rxd_In',0,12,65,3
	.word	48686
	.byte	24,12,68,15,20,13
	.byte	'module',0
	.word	48614
	.byte	4,2,35,0,13
	.byte	'nodeId',0
	.word	21096
	.byte	1,2,35,4,13
	.byte	'pin',0
	.word	48549
	.byte	8,2,35,8,13
	.byte	'select',0
	.word	47742
	.byte	1,2,35,16,0,25
	.word	48718
	.byte	21
	.byte	'IfxMultican_Txd_Out',0,12,74,3
	.word	48785
.L74:
	.byte	25
	.word	48619
.L75:
	.byte	25
	.word	48619
.L76:
	.byte	25
	.word	48619
.L77:
	.byte	25
	.word	48619
.L78:
	.byte	25
	.word	48619
.L79:
	.byte	25
	.word	48619
.L80:
	.byte	25
	.word	48619
.L81:
	.byte	25
	.word	48619
.L82:
	.byte	25
	.word	48619
.L83:
	.byte	25
	.word	48619
.L84:
	.byte	25
	.word	48619
.L85:
	.byte	25
	.word	48619
.L86:
	.byte	25
	.word	48619
.L87:
	.byte	25
	.word	48619
.L88:
	.byte	25
	.word	48619
.L89:
	.byte	25
	.word	48619
.L90:
	.byte	25
	.word	48718
.L91:
	.byte	25
	.word	48718
.L92:
	.byte	25
	.word	48718
.L93:
	.byte	25
	.word	48718
.L94:
	.byte	25
	.word	48718
.L95:
	.byte	25
	.word	48718
.L96:
	.byte	25
	.word	48718
.L97:
	.byte	25
	.word	48718
.L98:
	.byte	25
	.word	48718
.L99:
	.byte	25
	.word	48718
.L100:
	.byte	25
	.word	48718
.L101:
	.byte	25
	.word	48718
.L102:
	.byte	25
	.word	48718
.L103:
	.byte	25
	.word	48718
.L104:
	.byte	25
	.word	48718
.L105:
	.byte	25
	.word	48718
	.byte	25
	.word	48619
	.byte	3
	.word	48978
	.byte	14,20
	.word	48983
	.byte	15,4,0,14,80
	.word	48988
	.byte	15,3,0
.L106:
	.byte	14,80
	.word	48997
	.byte	15,0,0,25
	.word	48718
	.byte	3
	.word	49015
	.byte	14,20
	.word	49020
	.byte	15,4,0,14,80
	.word	49025
	.byte	15,3,0
.L107:
	.byte	14,80
	.word	49034
	.byte	15,0,0,0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L4:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,21,0,54,15,39,12,0,0,3,15,0,73,19,0,0,4,46,1,3,8,32,13
	.byte	58,15,59,15,57,15,54,15,39,12,0,0,5,5,0,3,8,58,15,59,15,57,15,73,19,0,0,6,11,0,0,0,7,36,0,3,8,11,15,62
	.byte	15,0,0,8,46,1,3,8,32,13,58,15,59,15,57,15,73,19,54,15,39,12,0,0,9,59,0,3,8,0,0,10,19,1,3,8,58,15,59,15
	.byte	57,15,11,15,0,0,11,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,12,23,1,58,15,59,15,57,15,11,15,0,0,13,13
	.byte	0,3,8,73,19,11,15,56,9,0,0,14,1,1,11,15,73,19,0,0,15,33,0,47,15,0,0,16,53,0,73,19,0,0,17,4,1,58,15,59
	.byte	15,57,15,11,15,0,0,18,40,0,3,8,28,13,0,0,19,46,1,49,19,0,0,20,5,0,49,19,0,0,21,22,0,3,8,58,15,59,15,57
	.byte	15,73,19,0,0,22,21,0,54,15,0,0,23,13,0,73,19,11,15,56,9,0,0,24,19,1,58,15,59,15,57,15,11,15,0,0,25,38
	.byte	0,73,19,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L5:
	.word	.L109-.L108
.L108:
	.half	3
	.word	.L111-.L110
.L110:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg',0
	.byte	0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_Intrinsics.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Port\\Std\\IfxPort.h',0
	.byte	0,0,0
	.byte	'IfxPort_regdef.h',0,1,0,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMultican_PinMap.c',0,0,0,0
	.byte	'IfxCan_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Platform_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Ifx_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxMultican_cfg.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxScu_cfg.h',0
	.byte	0,0,0
	.byte	'IfxScu_regdef.h',0,1,0,0
	.byte	'..\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\_PinMap\\IfxMultican_PinMap.h',0,0,0,0,0
.L111:
.L109:
	.sdecl	'.debug_info',debug,cluster('IfxMultican_RXD0A_P02_1_IN')
	.sect	'.debug_info'
.L6:
	.word	282
	.half	3
	.word	.L7
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMultican_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMultican_RXD0A_P02_1_IN',0,5,48,20
	.word	.L74
	.byte	1,5,3
	.word	IfxMultican_RXD0A_P02_1_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMultican_RXD0A_P02_1_IN')
	.sect	'.debug_abbrev'
.L7:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMultican_RXD0B_P20_7_IN')
	.sect	'.debug_info'
.L8:
	.word	282
	.half	3
	.word	.L9
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMultican_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMultican_RXD0B_P20_7_IN',0,5,49,20
	.word	.L75
	.byte	1,5,3
	.word	IfxMultican_RXD0B_P20_7_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMultican_RXD0B_P20_7_IN')
	.sect	'.debug_abbrev'
.L9:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMultican_RXD0D_P02_4_IN')
	.sect	'.debug_info'
.L10:
	.word	282
	.half	3
	.word	.L11
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMultican_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMultican_RXD0D_P02_4_IN',0,5,50,20
	.word	.L76
	.byte	1,5,3
	.word	IfxMultican_RXD0D_P02_4_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMultican_RXD0D_P02_4_IN')
	.sect	'.debug_abbrev'
.L11:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMultican_RXD0E_P33_7_IN')
	.sect	'.debug_info'
.L12:
	.word	282
	.half	3
	.word	.L13
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMultican_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMultican_RXD0E_P33_7_IN',0,5,51,20
	.word	.L77
	.byte	1,5,3
	.word	IfxMultican_RXD0E_P33_7_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMultican_RXD0E_P33_7_IN')
	.sect	'.debug_abbrev'
.L13:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMultican_RXD1A_P15_3_IN')
	.sect	'.debug_info'
.L14:
	.word	282
	.half	3
	.word	.L15
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMultican_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMultican_RXD1A_P15_3_IN',0,5,52,20
	.word	.L78
	.byte	1,5,3
	.word	IfxMultican_RXD1A_P15_3_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMultican_RXD1A_P15_3_IN')
	.sect	'.debug_abbrev'
.L15:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMultican_RXD1B_P14_1_IN')
	.sect	'.debug_info'
.L16:
	.word	282
	.half	3
	.word	.L17
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMultican_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMultican_RXD1B_P14_1_IN',0,5,53,20
	.word	.L79
	.byte	1,5,3
	.word	IfxMultican_RXD1B_P14_1_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMultican_RXD1B_P14_1_IN')
	.sect	'.debug_abbrev'
.L17:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMultican_RXD1D_P00_1_IN')
	.sect	'.debug_info'
.L18:
	.word	282
	.half	3
	.word	.L19
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMultican_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMultican_RXD1D_P00_1_IN',0,5,54,20
	.word	.L80
	.byte	1,5,3
	.word	IfxMultican_RXD1D_P00_1_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMultican_RXD1D_P00_1_IN')
	.sect	'.debug_abbrev'
.L19:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMultican_RXD2A_P15_1_IN')
	.sect	'.debug_info'
.L20:
	.word	282
	.half	3
	.word	.L21
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMultican_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMultican_RXD2A_P15_1_IN',0,5,55,20
	.word	.L81
	.byte	1,5,3
	.word	IfxMultican_RXD2A_P15_1_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMultican_RXD2A_P15_1_IN')
	.sect	'.debug_abbrev'
.L21:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMultican_RXD2B_P02_3_IN')
	.sect	'.debug_info'
.L22:
	.word	282
	.half	3
	.word	.L23
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMultican_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMultican_RXD2B_P02_3_IN',0,5,56,20
	.word	.L82
	.byte	1,5,3
	.word	IfxMultican_RXD2B_P02_3_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMultican_RXD2B_P02_3_IN')
	.sect	'.debug_abbrev'
.L23:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMultican_RXD2D_P14_8_IN')
	.sect	'.debug_info'
.L24:
	.word	282
	.half	3
	.word	.L25
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMultican_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMultican_RXD2D_P14_8_IN',0,5,57,20
	.word	.L83
	.byte	1,5,3
	.word	IfxMultican_RXD2D_P14_8_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMultican_RXD2D_P14_8_IN')
	.sect	'.debug_abbrev'
.L25:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMultican_RXD2E_P10_2_IN')
	.sect	'.debug_info'
.L26:
	.word	282
	.half	3
	.word	.L27
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMultican_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMultican_RXD2E_P10_2_IN',0,5,58,20
	.word	.L84
	.byte	1,5,3
	.word	IfxMultican_RXD2E_P10_2_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMultican_RXD2E_P10_2_IN')
	.sect	'.debug_abbrev'
.L27:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMultican_RXD3A_P00_3_IN')
	.sect	'.debug_info'
.L28:
	.word	282
	.half	3
	.word	.L29
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMultican_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMultican_RXD3A_P00_3_IN',0,5,59,20
	.word	.L85
	.byte	1,5,3
	.word	IfxMultican_RXD3A_P00_3_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMultican_RXD3A_P00_3_IN')
	.sect	'.debug_abbrev'
.L29:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMultican_RXD3B_P32_2_IN')
	.sect	'.debug_info'
.L30:
	.word	282
	.half	3
	.word	.L31
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMultican_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMultican_RXD3B_P32_2_IN',0,5,60,20
	.word	.L86
	.byte	1,5,3
	.word	IfxMultican_RXD3B_P32_2_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMultican_RXD3B_P32_2_IN')
	.sect	'.debug_abbrev'
.L31:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMultican_RXD3C_P20_0_IN')
	.sect	'.debug_info'
.L32:
	.word	282
	.half	3
	.word	.L33
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMultican_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMultican_RXD3C_P20_0_IN',0,5,61,20
	.word	.L87
	.byte	1,5,3
	.word	IfxMultican_RXD3C_P20_0_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMultican_RXD3C_P20_0_IN')
	.sect	'.debug_abbrev'
.L33:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMultican_RXD3D_P11_10_IN')
	.sect	'.debug_info'
.L34:
	.word	283
	.half	3
	.word	.L35
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMultican_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMultican_RXD3D_P11_10_IN',0,5,62,20
	.word	.L88
	.byte	1,5,3
	.word	IfxMultican_RXD3D_P11_10_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMultican_RXD3D_P11_10_IN')
	.sect	'.debug_abbrev'
.L35:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMultican_RXD3E_P20_9_IN')
	.sect	'.debug_info'
.L36:
	.word	282
	.half	3
	.word	.L37
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMultican_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMultican_RXD3E_P20_9_IN',0,5,63,20
	.word	.L89
	.byte	1,5,3
	.word	IfxMultican_RXD3E_P20_9_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMultican_RXD3E_P20_9_IN')
	.sect	'.debug_abbrev'
.L37:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMultican_TXD0_P02_0_OUT')
	.sect	'.debug_info'
.L38:
	.word	282
	.half	3
	.word	.L39
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMultican_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMultican_TXD0_P02_0_OUT',0,5,64,21
	.word	.L90
	.byte	1,5,3
	.word	IfxMultican_TXD0_P02_0_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMultican_TXD0_P02_0_OUT')
	.sect	'.debug_abbrev'
.L39:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMultican_TXD0_P02_5_OUT')
	.sect	'.debug_info'
.L40:
	.word	282
	.half	3
	.word	.L41
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMultican_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMultican_TXD0_P02_5_OUT',0,5,65,21
	.word	.L91
	.byte	1,5,3
	.word	IfxMultican_TXD0_P02_5_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMultican_TXD0_P02_5_OUT')
	.sect	'.debug_abbrev'
.L41:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMultican_TXD0_P20_8_OUT')
	.sect	'.debug_info'
.L42:
	.word	282
	.half	3
	.word	.L43
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMultican_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMultican_TXD0_P20_8_OUT',0,5,66,21
	.word	.L92
	.byte	1,5,3
	.word	IfxMultican_TXD0_P20_8_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMultican_TXD0_P20_8_OUT')
	.sect	'.debug_abbrev'
.L43:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMultican_TXD0_P33_8_OUT')
	.sect	'.debug_info'
.L44:
	.word	282
	.half	3
	.word	.L45
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMultican_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMultican_TXD0_P33_8_OUT',0,5,67,21
	.word	.L93
	.byte	1,5,3
	.word	IfxMultican_TXD0_P33_8_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMultican_TXD0_P33_8_OUT')
	.sect	'.debug_abbrev'
.L45:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMultican_TXD1_P00_0_OUT')
	.sect	'.debug_info'
.L46:
	.word	282
	.half	3
	.word	.L47
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMultican_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMultican_TXD1_P00_0_OUT',0,5,68,21
	.word	.L94
	.byte	1,5,3
	.word	IfxMultican_TXD1_P00_0_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMultican_TXD1_P00_0_OUT')
	.sect	'.debug_abbrev'
.L47:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMultican_TXD1_P14_0_OUT')
	.sect	'.debug_info'
.L48:
	.word	282
	.half	3
	.word	.L49
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMultican_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMultican_TXD1_P14_0_OUT',0,5,69,21
	.word	.L95
	.byte	1,5,3
	.word	IfxMultican_TXD1_P14_0_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMultican_TXD1_P14_0_OUT')
	.sect	'.debug_abbrev'
.L49:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMultican_TXD1_P15_2_OUT')
	.sect	'.debug_info'
.L50:
	.word	282
	.half	3
	.word	.L51
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMultican_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMultican_TXD1_P15_2_OUT',0,5,70,21
	.word	.L96
	.byte	1,5,3
	.word	IfxMultican_TXD1_P15_2_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMultican_TXD1_P15_2_OUT')
	.sect	'.debug_abbrev'
.L51:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMultican_TXD2_P02_2_OUT')
	.sect	'.debug_info'
.L52:
	.word	282
	.half	3
	.word	.L53
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMultican_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMultican_TXD2_P02_2_OUT',0,5,71,21
	.word	.L97
	.byte	1,5,3
	.word	IfxMultican_TXD2_P02_2_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMultican_TXD2_P02_2_OUT')
	.sect	'.debug_abbrev'
.L53:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMultican_TXD2_P10_3_OUT')
	.sect	'.debug_info'
.L54:
	.word	282
	.half	3
	.word	.L55
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMultican_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMultican_TXD2_P10_3_OUT',0,5,72,21
	.word	.L98
	.byte	1,5,3
	.word	IfxMultican_TXD2_P10_3_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMultican_TXD2_P10_3_OUT')
	.sect	'.debug_abbrev'
.L55:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMultican_TXD2_P14_10_OUT')
	.sect	'.debug_info'
.L56:
	.word	283
	.half	3
	.word	.L57
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMultican_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMultican_TXD2_P14_10_OUT',0,5,73,21
	.word	.L99
	.byte	1,5,3
	.word	IfxMultican_TXD2_P14_10_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMultican_TXD2_P14_10_OUT')
	.sect	'.debug_abbrev'
.L57:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMultican_TXD2_P15_0_OUT')
	.sect	'.debug_info'
.L58:
	.word	282
	.half	3
	.word	.L59
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMultican_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMultican_TXD2_P15_0_OUT',0,5,74,21
	.word	.L100
	.byte	1,5,3
	.word	IfxMultican_TXD2_P15_0_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMultican_TXD2_P15_0_OUT')
	.sect	'.debug_abbrev'
.L59:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMultican_TXD3_P00_2_OUT')
	.sect	'.debug_info'
.L60:
	.word	282
	.half	3
	.word	.L61
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMultican_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMultican_TXD3_P00_2_OUT',0,5,75,21
	.word	.L101
	.byte	1,5,3
	.word	IfxMultican_TXD3_P00_2_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMultican_TXD3_P00_2_OUT')
	.sect	'.debug_abbrev'
.L61:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMultican_TXD3_P11_12_OUT')
	.sect	'.debug_info'
.L62:
	.word	283
	.half	3
	.word	.L63
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMultican_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMultican_TXD3_P11_12_OUT',0,5,76,21
	.word	.L102
	.byte	1,5,3
	.word	IfxMultican_TXD3_P11_12_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMultican_TXD3_P11_12_OUT')
	.sect	'.debug_abbrev'
.L63:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMultican_TXD3_P20_10_OUT')
	.sect	'.debug_info'
.L64:
	.word	283
	.half	3
	.word	.L65
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMultican_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMultican_TXD3_P20_10_OUT',0,5,77,21
	.word	.L103
	.byte	1,5,3
	.word	IfxMultican_TXD3_P20_10_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMultican_TXD3_P20_10_OUT')
	.sect	'.debug_abbrev'
.L65:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMultican_TXD3_P20_3_OUT')
	.sect	'.debug_info'
.L66:
	.word	282
	.half	3
	.word	.L67
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMultican_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMultican_TXD3_P20_3_OUT',0,5,78,21
	.word	.L104
	.byte	1,5,3
	.word	IfxMultican_TXD3_P20_3_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMultican_TXD3_P20_3_OUT')
	.sect	'.debug_abbrev'
.L67:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMultican_TXD3_P32_3_OUT')
	.sect	'.debug_info'
.L68:
	.word	282
	.half	3
	.word	.L69
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMultican_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMultican_TXD3_P32_3_OUT',0,5,79,21
	.word	.L105
	.byte	1,5,3
	.word	IfxMultican_TXD3_P32_3_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMultican_TXD3_P32_3_OUT')
	.sect	'.debug_abbrev'
.L69:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMultican_Rxd_In_pinTable')
	.sect	'.debug_info'
.L70:
	.word	283
	.half	3
	.word	.L71
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMultican_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMultican_Rxd_In_pinTable',0,5,82,27
	.word	.L106
	.byte	1,5,3
	.word	IfxMultican_Rxd_In_pinTable
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMultican_Rxd_In_pinTable')
	.sect	'.debug_abbrev'
.L71:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMultican_Txd_Out_pinTable')
	.sect	'.debug_info'
.L72:
	.word	284
	.half	3
	.word	.L73
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMultican_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMultican_Txd_Out_pinTable',0,5,115,28
	.word	.L107
	.byte	1,5,3
	.word	IfxMultican_Txd_Out_pinTable
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMultican_Txd_Out_pinTable')
	.sect	'.debug_abbrev'
.L73:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	; Module end
