################################################################################
# Automatically-generated file. Do not edit!
################################################################################

# Add inputs and outputs from these tool invocations to the build variables 
C_SRCS += \
../code/user2/xf_asr/asr_audio.c \
../code/user2/xf_asr/base64.c \
../code/user2/xf_asr/hmac_sha256.c \
../code/user2/xf_asr/sha1.c \
../code/user2/xf_asr/websocket_client.c 

COMPILED_SRCS += \
code/user2/xf_asr/asr_audio.src \
code/user2/xf_asr/base64.src \
code/user2/xf_asr/hmac_sha256.src \
code/user2/xf_asr/sha1.src \
code/user2/xf_asr/websocket_client.src 

C_DEPS += \
code/user2/xf_asr/asr_audio.d \
code/user2/xf_asr/base64.d \
code/user2/xf_asr/hmac_sha256.d \
code/user2/xf_asr/sha1.d \
code/user2/xf_asr/websocket_client.d 

OBJS += \
code/user2/xf_asr/asr_audio.o \
code/user2/xf_asr/base64.o \
code/user2/xf_asr/hmac_sha256.o \
code/user2/xf_asr/sha1.o \
code/user2/xf_asr/websocket_client.o 


# Each subdirectory must supply rules for building sources it contributes
code/user2/xf_asr/asr_audio.src: ../code/user2/xf_asr/asr_audio.c code/user2/xf_asr/subdir.mk
	cctc -cs --dep-file="$(*F).d" --misrac-version=2004 -D__CPU__=tc26xb "-fC:/Users/<USER>/Desktop/ORRN_Code_4G - 鍓湰/ORRN_Code/Mark_1/Debug/TASKING_C_C___Compiler-Include_paths__-I_.opt" --iso=99 --c++14 --language=+volatile --exceptions --anachronisms --fp-model=2 -O0 --tradeoff=4 --compact-max-size=200 -g -Wc-w544 -Wc-w557 -Ctc26xb -Y0 -N0 -Z0 -o "$@" "$<"
code/user2/xf_asr/asr_audio.o: code/user2/xf_asr/asr_audio.src code/user2/xf_asr/subdir.mk
	astc -Og -Os --no-warnings= --error-limit=42 -o  "$@" "$<"
code/user2/xf_asr/base64.src: ../code/user2/xf_asr/base64.c code/user2/xf_asr/subdir.mk
	cctc -cs --dep-file="$(*F).d" --misrac-version=2004 -D__CPU__=tc26xb "-fC:/Users/<USER>/Desktop/ORRN_Code_4G - 鍓湰/ORRN_Code/Mark_1/Debug/TASKING_C_C___Compiler-Include_paths__-I_.opt" --iso=99 --c++14 --language=+volatile --exceptions --anachronisms --fp-model=2 -O0 --tradeoff=4 --compact-max-size=200 -g -Wc-w544 -Wc-w557 -Ctc26xb -Y0 -N0 -Z0 -o "$@" "$<"
code/user2/xf_asr/base64.o: code/user2/xf_asr/base64.src code/user2/xf_asr/subdir.mk
	astc -Og -Os --no-warnings= --error-limit=42 -o  "$@" "$<"
code/user2/xf_asr/hmac_sha256.src: ../code/user2/xf_asr/hmac_sha256.c code/user2/xf_asr/subdir.mk
	cctc -cs --dep-file="$(*F).d" --misrac-version=2004 -D__CPU__=tc26xb "-fC:/Users/<USER>/Desktop/ORRN_Code_4G - 鍓湰/ORRN_Code/Mark_1/Debug/TASKING_C_C___Compiler-Include_paths__-I_.opt" --iso=99 --c++14 --language=+volatile --exceptions --anachronisms --fp-model=2 -O0 --tradeoff=4 --compact-max-size=200 -g -Wc-w544 -Wc-w557 -Ctc26xb -Y0 -N0 -Z0 -o "$@" "$<"
code/user2/xf_asr/hmac_sha256.o: code/user2/xf_asr/hmac_sha256.src code/user2/xf_asr/subdir.mk
	astc -Og -Os --no-warnings= --error-limit=42 -o  "$@" "$<"
code/user2/xf_asr/sha1.src: ../code/user2/xf_asr/sha1.c code/user2/xf_asr/subdir.mk
	cctc -cs --dep-file="$(*F).d" --misrac-version=2004 -D__CPU__=tc26xb "-fC:/Users/<USER>/Desktop/ORRN_Code_4G - 鍓湰/ORRN_Code/Mark_1/Debug/TASKING_C_C___Compiler-Include_paths__-I_.opt" --iso=99 --c++14 --language=+volatile --exceptions --anachronisms --fp-model=2 -O0 --tradeoff=4 --compact-max-size=200 -g -Wc-w544 -Wc-w557 -Ctc26xb -Y0 -N0 -Z0 -o "$@" "$<"
code/user2/xf_asr/sha1.o: code/user2/xf_asr/sha1.src code/user2/xf_asr/subdir.mk
	astc -Og -Os --no-warnings= --error-limit=42 -o  "$@" "$<"
code/user2/xf_asr/websocket_client.src: ../code/user2/xf_asr/websocket_client.c code/user2/xf_asr/subdir.mk
	cctc -cs --dep-file="$(*F).d" --misrac-version=2004 -D__CPU__=tc26xb "-fC:/Users/<USER>/Desktop/ORRN_Code_4G - 鍓湰/ORRN_Code/Mark_1/Debug/TASKING_C_C___Compiler-Include_paths__-I_.opt" --iso=99 --c++14 --language=+volatile --exceptions --anachronisms --fp-model=2 -O0 --tradeoff=4 --compact-max-size=200 -g -Wc-w544 -Wc-w557 -Ctc26xb -Y0 -N0 -Z0 -o "$@" "$<"
code/user2/xf_asr/websocket_client.o: code/user2/xf_asr/websocket_client.src code/user2/xf_asr/subdir.mk
	astc -Og -Os --no-warnings= --error-limit=42 -o  "$@" "$<"

clean: clean-code-2f-user2-2f-xf_asr

clean-code-2f-user2-2f-xf_asr:
	-$(RM) code/user2/xf_asr/asr_audio.d code/user2/xf_asr/asr_audio.o code/user2/xf_asr/asr_audio.src code/user2/xf_asr/base64.d code/user2/xf_asr/base64.o code/user2/xf_asr/base64.src code/user2/xf_asr/hmac_sha256.d code/user2/xf_asr/hmac_sha256.o code/user2/xf_asr/hmac_sha256.src code/user2/xf_asr/sha1.d code/user2/xf_asr/sha1.o code/user2/xf_asr/sha1.src code/user2/xf_asr/websocket_client.d code/user2/xf_asr/websocket_client.o code/user2/xf_asr/websocket_client.src

.PHONY: clean-code-2f-user2-2f-xf_asr

