	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc23476a --dep-file=Ifx_LutSincosF32_Table.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2\\\\xf_asr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_LutSincosF32_Table.src ../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_LutSincosF32_Table.c"
	.compiler_name		"ctc"
	;source	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_LutSincosF32_Table.c'

	
$TC16X
	
	.sdecl	'.rodata.Ifx_LutSincosF32_Table.Ifx_g_LutSincosF32_table',data,rom,cluster('Ifx_g_LutSincosF32_table')
	.sect	'.rodata.Ifx_LutSincosF32_Table.Ifx_g_LutSincosF32_table'
	.global	Ifx_g_LutSincosF32_table
	.align	2
Ifx_g_LutSincosF32_table:	.type	object
	.size	Ifx_g_LutSincosF32_table,4100
	.space	4
	.word	986255317,994643910,999738305,1003032456,1006326576,1008126808,1009773826,1011420816
	.word	1013067775,1014714699,1015691576,1016514998,1017338396,1018161769,1018985115,1019808432
	.word	1020631718,1021454970,1022278188,1023101370,1023667344,1024078895,1024490424,1024901932
	.word	1025313416,1025724875,1026136310,1026547719,1026959100,1027370453,1027781777,1028193072
	.word	1028604335,1029015566,1029426764,1029837929,1030249058,1030660152,1031071209,1031482228
	.word	1031845996,1032051466,1032256916,1032462346,1032667754,1032873140,1033078503,1033283845
	.word	1033489162,1033694457,1033899727,1034104972,1034310192,1034515386,1034720555,1034925696
	.word	1035130811,1035335898,1035540957,1035745987,1035950989,1036155961,1036360902,1036565814
	.word	1036770694,1036975543,1037180360,1037385145,1037589897,1037794615,1037999300,1038203950
	.word	1038408566,1038613146,1038817690,1039022198,1039226670,1039431104,1039635500,1039839859
	.word	1040044178,1040217925,1040320046,1040422146,1040524226,1040626286,1040728325,1040830342
	.word	1040932339,1041034314,1041136267,1041238199,1041340108,1041441994,1041543858,1041645699
	.word	1041747517,1041849312,1041951083,1042052830,1042154552,1042256251,1042357925,1042459574
	.word	1042561197,1042662796,1042764369,1042865916,1042967437,1043068932,1043170401,1043271842
	.word	1043373257,1043474644,1043576004,1043677336,1043778640,1043879916,1043981164,1044082383
	.word	1044183573,1044284734,1044385865,1044486967,1044588039,1044689081,1044790093,1044891074
	.word	1044992024,1045092943,1045193831,1045294688,1045395512,1045496305,1045597065,1045697793
	.word	1045798488,1045899151,1045999780,1046100375,1046200938,1046301466,1046401960,1046502419
	.word	1046602844,1046703235,1046803590,1046903910,1047004194,1047104442,1047204655,1047304831
	.word	1047404971,1047505074,1047605140,1047705169,1047805160,1047905114,1048005030,1048104908
	.word	1048204747,1048304548,1048404310,1048504033,1048589858,1048639680,1048689482,1048739264
	.word	1048789026,1048838768,1048888490,1048938190,1048987871,1049037530,1049087169,1049136787
	.word	1049186384,1049235959,1049285514,1049335047,1049384558,1049434048,1049483516,1049532962
	.word	1049582386,1049631788,1049681168,1049730525,1049779860,1049829173,1049878463,1049927729
	.word	1049976973,1050026194,1050075392,1050124567,1050173718,1050222846,1050271950,1050321030
	.word	1050370087,1050419119,1050468128,1050517112,1050566072,1050615007,1050663918,1050712805
	.word	1050761666,1050810503,1050859315,1050908101,1050956863,1051005599,1051054309,1051102994
	.word	1051151653,1051200287,1051248894,1051297476,1051346031,1051394561,1051443063,1051491540
	.word	1051539989,1051588412,1051636809,1051685178,1051733520,1051781835,1051830123,1051878383
	.word	1051926616,1051974821,1052022998,1052071148,1052119270,1052167363,1052215428,1052263466
	.word	1052311474,1052359454,1052407406,1052455328,1052503222,1052551087,1052598923,1052646730
	.word	1052694507,1052742255,1052789973,1052837662,1052885320,1052932949,1052980548,1053028117
	.word	1053075656,1053123164,1053170642,1053218089,1053265506,1053312892,1053360247,1053407571
	.word	1053454864,1053502126,1053549356,1053596555,1053643722,1053690858,1053737962,1053785034
	.word	1053832074,1053879082,1053926058,1053973001,1054019912,1054066791,1054113636,1054160449
	.word	1054207229,1054253977,1054300691,1054347371,1054394019,1054440633,1054487213,1054533760
	.word	1054580273,1054626753,1054673198,1054719609,1054765986,1054812329,1054858637,1054904911
	.word	1054951150,1054997354,1055043524,1055089658,1055135758,1055181822,1055227851,1055273845
	.word	1055319803,1055365725,1055411612,1055457463,1055503278,1055549057,1055594800,1055640507
	.word	1055686177,1055731811,1055777408,1055822969,1055868492,1055913979,1055959429,1056004842
	.word	1056050217,1056095555,1056140856,1056186119,1056231345,1056276533,1056321683,1056366795
	.word	1056411868,1056456904,1056501902,1056546861,1056591781,1056636663,1056681506,1056726311
	.word	1056771076,1056815803,1056860490,1056905138,1056949747,1056979462,1057001727,1057023972
	.word	1057046197,1057068403,1057090588,1057112753,1057134898,1057157023,1057179128,1057201213
	.word	1057223277,1057245321,1057267345,1057289348,1057311330,1057333292,1057355234,1057377154
	.word	1057399054,1057420934,1057442792,1057464630,1057486447,1057508242,1057530017,1057551771
	.word	1057573503,1057595214,1057616905,1057638573,1057660221,1057681847,1057703452,1057725035
	.word	1057746597,1057768137,1057789655,1057811152,1057832627,1057854081,1057875512,1057896922
	.word	1057918309,1057939675,1057961019,1057982340,1058003640,1058024917,1058046172,1058067405
	.word	1058088615,1058109803,1058130969,1058152112,1058173232,1058194330,1058215406,1058236458
	.word	1058257488,1058278495,1058299480,1058320441,1058341380,1058362295,1058383188,1058404057
	.word	1058424903,1058445727,1058466526,1058487303,1058508056,1058528786,1058549493,1058570176
	.word	1058590835,1058611471,1058632084,1058652672,1058673237,1058693779,1058714296,1058734790
	.word	1058755259,1058775705,1058796127,1058816524,1058836898,1058857247,1058877572,1058897873
	.word	1058918150,1058938402,1058958630,1058978834,1058999013,1059019167,1059039297,1059059403
	.word	1059079483,1059099539,1059119570,1059139577,1059159558,1059179515,1059199447,1059219353
	.word	1059239235,1059259091,1059278923,1059298729,1059318510,1059338266,1059357996,1059377701
	.word	1059397380,1059417035,1059436663,1059456266,1059475844,1059495395,1059514922,1059534422
	.word	1059553896,1059573345,1059592768,1059612165,1059631536,1059650881,1059670200,1059689493
	.word	1059708759,1059728000,1059747214,1059766402,1059785563,1059804699,1059823807,1059842890
	.word	1059861945,1059880975,1059899977,1059918953,1059937903,1059956825,1059975721,1059994590
	.word	1060013432,1060032247,1060051035,1060069797,1060088531,1060107238,1060125918,1060144571
	.word	1060163196,1060181794,1060200365,1060218909,1060237425,1060255914,1060274375,1060292809
	.word	1060311215,1060329594,1060347945,1060366268,1060384564,1060402831,1060421071,1060439283
	.word	1060457467,1060475623,1060493752,1060511852,1060529924,1060547967,1060565983,1060583971
	.word	1060601930,1060619861,1060637763,1060655638,1060673483,1060691301,1060709089,1060726850
	.word	1060744581,1060762284,1060779959,1060797604,1060815221,1060832809,1060850369,1060867899
	.word	1060885400,1060902873,1060920316,1060937731,1060955116,1060972472,1060989799,1061007097
	.word	1061024366,1061041605,1061058815,1061075995,1061093147,1061110268,1061127360,1061144423
	.word	1061161456,1061178460,1061195433,1061212378,1061229292,1061246177,1061263031,1061279856
	.word	1061296651,1061313417,1061330152,1061346857,1061363532,1061380177,1061396792,1061413376
	.word	1061429931,1061446455,1061462949,1061479413,1061495846,1061512249,1061528621,1061544963
	.word	1061561275,1061577556,1061593806,1061610026,1061626215,1061642373,1061658500,1061674597
	.word	1061690663,1061706698,1061722702,1061738675,1061754618,1061770529,1061786409,1061802258
	.word	1061818076,1061833863,1061849619,1061865343,1061881036,1061896698,1061912329,1061927928
	.word	1061943495,1061959032,1061974536,1061990009,1062005451,1062020861,1062036240,1062051586
	.word	1062066901,1062082185,1062097436,1062112656,1062127844,1062142999,1062158123,1062173215
	.word	1062188276,1062203304,1062218299,1062233263,1062248195,1062263095,1062277962,1062292797
	.word	1062307600,1062322370,1062337108,1062351814,1062366488,1062381129,1062395737,1062410313
	.word	1062424856,1062439367,1062453845,1062468291,1062482703,1062497083,1062511431,1062525745
	.word	1062540027,1062554276,1062568492,1062582675,1062596825,1062610942,1062625026,1062639077
	.word	1062653095,1062667080,1062681031,1062694950,1062708835,1062722687,1062736505,1062750291
	.word	1062764043,1062777761,1062791446,1062805098,1062818716,1062832301,1062845852,1062859370
	.word	1062872854,1062886304,1062899721,1062913104,1062926453,1062939769,1062953050,1062966298
	.word	1062979512,1062992692,1063005838,1063018951,1063032029,1063045073,1063058083,1063071059
	.word	1063084001,1063096909,1063109783,1063122622,1063135427,1063148198,1063160935,1063173637
	.word	1063186305,1063198939,1063211538,1063224103,1063236633,1063249129,1063261590,1063274017
	.word	1063286409,1063298767,1063311090,1063323378,1063335632,1063347850,1063360034,1063372184
	.word	1063384298,1063396378,1063408422,1063420432,1063432407,1063444347,1063456252,1063468122
	.word	1063479957,1063491756,1063503521,1063515251,1063526945,1063538604,1063550228,1063561817
	.word	1063573371,1063584889,1063596372,1063607819,1063619232,1063630608,1063641950,1063653256
	.word	1063664526,1063675761,1063686960,1063698124,1063709253,1063720345,1063731402,1063742424
	.word	1063753409,1063764359,1063775273,1063786152,1063796995,1063807801,1063818572,1063829308
	.word	1063840007,1063850670,1063861298,1063871889,1063882444,1063892964,1063903447,1063913895
	.word	1063924306,1063934681,1063945020,1063955323,1063965589,1063975820,1063986014,1063996172
	.word	1064006293,1064016379,1064026427,1064036440,1064046416,1064056356,1064066260,1064076126
	.word	1064085957,1064095751,1064105508,1064115229,1064124914,1064134561,1064144173,1064153747
	.word	1064163285,1064172786,1064182251,1064191678,1064201069,1064210424,1064219741,1064229022
	.word	1064238266,1064247472,1064256643,1064265776,1064274872,1064283931,1064292954,1064301939
	.word	1064310887,1064319799,1064328673,1064337510,1064346310,1064355073,1064363799,1064372488
	.word	1064381140,1064389754,1064398331,1064406871,1064415374,1064423839,1064432268,1064440658
	.word	1064449012,1064457328,1064465607,1064473848,1064482052,1064490219,1064498348,1064506439
	.word	1064514494,1064522510,1064530489,1064538431,1064546335,1064554201,1064562030,1064569821
	.word	1064577575,1064585291,1064592969,1064600610,1064608213,1064615778,1064623305,1064630795
	.word	1064638247,1064645661,1064653037,1064660375,1064667676,1064674939,1064682163,1064689350
	.word	1064696499,1064703610,1064710683,1064717719,1064724716,1064731675,1064738596,1064745479
	.word	1064752324,1064759131,1064765900,1064772631,1064779324,1064785978,1064792595,1064799173
	.word	1064805713,1064812215,1064818679,1064825104,1064831492,1064837841,1064844151,1064850424
	.word	1064856658,1064862854,1064869011,1064875131,1064881211,1064887254,1064893258,1064899224
	.word	1064905151,1064911040,1064916890,1064922702,1064928476,1064934211,1064939907,1064945565
	.word	1064951185,1064956766,1064962308,1064967812,1064973277,1064978704,1064984092,1064989442
	.word	1064994753,1065000025,1065005259,1065010454,1065015610,1065020727,1065025806,1065030846
	.word	1065035848,1065040811,1065045735,1065050620,1065055466,1065060274,1065065043,1065069773
	.word	1065074464,1065079117,1065083731,1065088305,1065092841,1065097338,1065101797,1065106216
	.word	1065110596,1065114938,1065119240,1065123504,1065127729,1065131914,1065136061,1065140169
	.word	1065144238,1065148268,1065152259,1065156211,1065160124,1065163997,1065167832,1065171628
	.word	1065175385,1065179102,1065182781,1065186420,1065190021,1065193582,1065197104,1065200588
	.word	1065204032,1065207436,1065210802,1065214129,1065217416,1065220664,1065223874,1065227044
	.word	1065230174,1065233266,1065236318,1065239331,1065242305,1065245240,1065248136,1065250992
	.word	1065253809,1065256587,1065259325,1065262025,1065264685,1065267305,1065269887,1065272429
	.word	1065274932,1065277396,1065279820,1065282205,1065284551,1065286857,1065289124,1065291352
	.word	1065293540,1065295689,1065297799,1065299869,1065301900,1065303892,1065305844,1065307757
	.word	1065309631,1065311465,1065313260,1065315015,1065316731,1065318408,1065320045,1065321643
	.word	1065323202,1065324721,1065326200,1065327640,1065329041,1065330403,1065331725,1065333007
	.word	1065334250,1065335454,1065336618,1065337743,1065338828,1065339874,1065340881,1065341847
	.word	1065342775,1065343663,1065344512,1065345321,1065346091,1065346821,1065347512,1065348163
	.word	1065348775,1065349347,1065349880,1065350374,1065350828,1065351242,1065351617,1065351953
	.word	1065352249,1065352505,1065352723,1065352900,1065353038,1065353137,1065353196,1065353216
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L3:
	.word	910
	.half	3
	.word	.L4
	.byte	4
.L2:
	.byte	1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_LutSincosF32_Table.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L5
	.byte	2,1,1,3
	.word	249
	.byte	4
	.byte	'Ifx__jump_and_link',0,3,1,61,17,1,1,5
	.byte	'fun',0,1,61,43
	.word	252
	.byte	6,0,7
	.byte	'__fract',0,4,128,1,7
	.byte	'float',0,4,4,8
	.byte	'Ifx__float_to_fract',0,3,1,152,2,18
	.word	297
	.byte	1,1,5
	.byte	'a',0,1,152,2,44
	.word	309
	.byte	6,0,7
	.byte	'unsigned long long int',0,8,7,9
	.byte	'void',0,3
	.word	389
	.byte	8
	.byte	'__ld64',0,3,2,135,1,19
	.word	363
	.byte	1,1,5
	.byte	'addr',0,2,135,1,32
	.word	395
	.byte	6,0,4
	.byte	'__st64',0,3,2,143,1,17,1,1,5
	.byte	'addr',0,2,143,1,30
	.word	395
	.byte	5
	.byte	'value',0,2,143,1,43
	.word	363
	.byte	6,0,10
	.word	257
	.byte	11
	.word	283
	.byte	6,0,10
	.word	318
	.byte	11
	.word	350
	.byte	6,0,10
	.word	400
	.byte	11
	.word	419
	.byte	6,0,10
	.word	435
	.byte	11
	.word	450
	.byte	11
	.word	464
	.byte	6,0,7
	.byte	'short int',0,2,5,12
	.byte	'__wchar_t',0,3,1,1
	.word	534
	.byte	7
	.byte	'unsigned int',0,4,7,12
	.byte	'__size_t',0,3,1,1
	.word	565
	.byte	7
	.byte	'int',0,4,5,12
	.byte	'__ptrdiff_t',0,3,1,1
	.word	598
	.byte	13,1,3
	.word	625
	.byte	12
	.byte	'__codeptr',0,3,1,1
	.word	627
	.byte	7
	.byte	'unsigned char',0,1,8,12
	.byte	'uint8',0,4,105,29
	.word	650
	.byte	7
	.byte	'unsigned short int',0,2,7,12
	.byte	'uint16',0,4,109,29
	.word	681
	.byte	7
	.byte	'unsigned long int',0,4,7,12
	.byte	'uint32',0,4,113,29
	.word	718
	.byte	12
	.byte	'uint64',0,4,118,29
	.word	363
	.byte	12
	.byte	'sint16',0,4,126,29
	.word	534
	.byte	7
	.byte	'long int',0,4,5,12
	.byte	'sint32',0,4,131,1,29
	.word	784
	.byte	7
	.byte	'long long int',0,8,5,12
	.byte	'sint64',0,4,138,1,29
	.word	812
	.byte	12
	.byte	'float32',0,4,167,1,29
	.word	309
	.byte	12
	.byte	'pvoid',0,5,57,28
	.word	395
	.byte	12
	.byte	'Ifx_TickTime',0,5,79,28
	.word	812
	.byte	14,132,32
	.word	309
	.byte	15,128,8,0
.L8:
	.byte	16
	.word	897
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L4:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,21,0,54,15,39,12,0,0,3,15,0,73,19,0,0,4,46,1,3,8,32,13
	.byte	58,15,59,15,57,15,54,15,39,12,0,0,5,5,0,3,8,58,15,59,15,57,15,73,19,0,0,6,11,0,0,0,7,36,0,3,8,11,15,62
	.byte	15,0,0,8,46,1,3,8,32,13,58,15,59,15,57,15,73,19,54,15,39,12,0,0,9,59,0,3,8,0,0,10,46,1,49,19,0,0,11,5
	.byte	0,49,19,0,0,12,22,0,3,8,58,15,59,15,57,15,73,19,0,0,13,21,0,54,15,0,0,14,1,1,11,15,73,19,0,0,15,33,0,47
	.byte	15,0,0,16,38,0,73,19,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L5:
	.word	.L10-.L9
.L9:
	.half	3
	.word	.L12-.L11
.L11:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_Intrinsics.h',0
	.byte	0,0,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_LutSincosF32_Table.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Platform_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Ifx_Types.h',0
	.byte	0,0,0,0
.L12:
.L10:
	.sdecl	'.debug_info',debug,cluster('Ifx_g_LutSincosF32_table')
	.sect	'.debug_info'
.L6:
	.word	287
	.half	3
	.word	.L7
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_LutSincosF32_Table.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'Ifx_g_LutSincosF32_table',0,3,61,15
	.word	.L8
	.byte	1,5,3
	.word	Ifx_g_LutSincosF32_table
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('Ifx_g_LutSincosF32_table')
	.sect	'.debug_abbrev'
.L7:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	; Module end
