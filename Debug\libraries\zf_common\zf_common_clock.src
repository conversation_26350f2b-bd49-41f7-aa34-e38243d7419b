	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc40796a --dep-file=zf_common_clock.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/zf_common/zf_common_clock.src ../libraries/zf_common/zf_common_clock.c"
	.compiler_name		"ctc"
	;source	'../libraries/zf_common/zf_common_clock.c'

	
$TC16X
	
	.sdecl	'.text.zf_common_clock.set_clock',code,cluster('set_clock')
	.sect	'.text.zf_common_clock.set_clock'
	.align	2
	
	.global	set_clock
; Function set_clock
.L15:
set_clock:	.type	func
	mov	d4,#0
.L107:
	mov.u	d5,#48160
	addih	d5,d5,#19774
	call	IfxScuCcu_setCpuFrequency
.L108:
	mov	d4,#1
.L109:
	mov.u	d5,#48160
	addih	d5,d5,#19774
	call	IfxScuCcu_setCpuFrequency
.L110:
	ret
.L57:
	
__set_clock_function_end:
	.size	set_clock,__set_clock_function_end-set_clock
.L32:
	; End of function
	
	.sdecl	'.text.zf_common_clock.get_clock',code,cluster('get_clock')
	.sect	'.text.zf_common_clock.get_clock'
	.align	2
	
	.global	get_clock
; Function get_clock
.L17:
get_clock:	.type	func
	movh.a	a15,#@his(g_AppCpu0)
	lea	a15,[a15]@los(g_AppCpu0)
.L115:
	call	IfxScuCcu_getPllFrequency
.L116:
	st.w	[a15]8,d2
.L60:
	mfcr	d15,#65052
.L89:
	and	d4,d15,#7
.L117:
	j	.L2
.L2:
	movh.a	a15,#@his(g_AppCpu0)
	lea	a15,[a15]@los(g_AppCpu0)
.L118:
	call	IfxScuCcu_getCpuFrequency
.L119:
	st.w	[a15]4,d2
.L120:
	movh.a	a15,#@his(g_AppCpu0)
	lea	a15,[a15]@los(g_AppCpu0)
.L121:
	call	IfxScuCcu_getSpbFrequency
.L122:
	st.w	[a15],d2
.L123:
	movh.a	a15,#61440
.L65:
	jz.a	a15,.L3
.L3:
	call	IfxScuCcu_getSourceFrequency
.L124:
	movh.a	a15,#61443
	ld.bu	d15,[a15]@los(0xf0036035)
.L90:
	and	d15,#15
	itof	d15,d15
.L125:
	div.f	d15,d2,d15
.L126:
	j	.L4
.L4:
	j	.L5
.L5:
	movh.a	a15,#@his(g_AppCpu0)
	lea	a15,[a15]@los(g_AppCpu0)
.L70:
	st.w	[a15]12,d15
.L71:
	ret
.L58:
	
__get_clock_function_end:
	.size	get_clock,__get_clock_function_end-get_clock
.L37:
	; End of function
	
	.sdecl	'.text.zf_common_clock.disable_Watchdog',code,cluster('disable_Watchdog')
	.sect	'.text.zf_common_clock.disable_Watchdog'
	.align	2
	
	.global	disable_Watchdog
; Function disable_Watchdog
.L19:
disable_Watchdog:	.type	func
	call	IfxScuWdt_getCpuWatchdogPassword
	mov	d4,d2
	call	IfxScuWdt_disableCpuWatchdog
.L131:
	call	IfxScuWdt_getSafetyWatchdogPassword
	mov	d4,d2
	call	IfxScuWdt_disableSafetyWatchdog
.L132:
	ret
.L77:
	
__disable_Watchdog_function_end:
	.size	disable_Watchdog,__disable_Watchdog_function_end-disable_Watchdog
.L42:
	; End of function
	
	.sdecl	'.text.zf_common_clock.clock_init',code,cluster('clock_init')
	.sect	'.text.zf_common_clock.clock_init'
	.align	2
	
	.global	clock_init
; Function clock_init
.L21:
clock_init:	.type	func
	call	interrupt_global_disable
.L137:
	call	disable_Watchdog
.L138:
	call	get_clock
.L139:
	call	system_delay_init
.L140:
	mov	d4,#0
	call	interrupt_global_enable
.L141:
	ret
.L78:
	
__clock_init_function_end:
	.size	clock_init,__clock_init_function_end-clock_init
.L47:
	; End of function
	
	.sdecl	'.text.zf_common_clock.cpu_wait_event_ready',code,cluster('cpu_wait_event_ready')
	.sect	'.text.zf_common_clock.cpu_wait_event_ready'
	.align	2
	
	.global	cpu_wait_event_ready
; Function cpu_wait_event_ready
.L23:
cpu_wait_event_ready:	.type	func
	mfcr	d15,#65052
.L91:
	and	d15,#7
.L92:
	j	.L6
.L6:
	jeq	d15,#0,.L7
.L146:
	j	.L8
.L9:
.L8:
	movh.a	a15,#@his(cpu_init_finish)
	lea	a15,[a15]@los(cpu_init_finish)
.L147:
	ld.bu	d15,[a15]
.L148:
	jeq	d15,#0,.L9
.L7:
	mfcr	d15,#65052
.L93:
	and	d15,#7
.L94:
	j	.L10
.L10:
	movh.a	a15,#@his(cpu_init_finish)
	lea	a15,[a15]@los(cpu_init_finish)
.L149:
	addsc.a	a15,a15,d15,#0
.L150:
	mov	d15,#1
.L151:
	st.b	[a15],d15
.L11:
	mov	d8,#1
.L95:
	mov	d15,#0
.L97:
	j	.L12
.L13:
	movh.a	a15,#@his(cpu_init_finish)
	lea	a15,[a15]@los(cpu_init_finish)
.L152:
	addsc.a	a15,a15,d15,#0
	ld.bu	d0,[a15]
.L153:
	mul	d8,d0
.L96:
	extr.u	d8,d8,#0,#8
.L98:
	add	d15,#1
.L12:
	jlt.u	d15,#2,.L13
.L154:
	mov	d4,#1
	call	system_delay_ms
.L155:
	jeq	d8,#0,.L11
.L156:
	ret
.L79:
	
__cpu_wait_event_ready_function_end:
	.size	cpu_wait_event_ready,__cpu_wait_event_ready_function_end-cpu_wait_event_ready
.L52:
	; End of function
	
	.sdecl	'.bss.zf_common_clock.g_AppCpu0',data,cluster('g_AppCpu0')
	.sect	'.bss.zf_common_clock.g_AppCpu0'
	.global	g_AppCpu0
	.align	4
g_AppCpu0:	.type	object
	.size	g_AppCpu0,16
	.space	16
	.sdecl	'.bss.zf_common_clock.cpu_init_finish',data,cluster('cpu_init_finish')
	.sect	'.bss.zf_common_clock.cpu_init_finish'
cpu_init_finish:	.type	object
	.size	cpu_init_finish,2
	.space	2
	.calls	'set_clock','IfxScuCcu_setCpuFrequency'
	.calls	'get_clock','IfxScuCcu_getPllFrequency'
	.calls	'get_clock','IfxScuCcu_getCpuFrequency'
	.calls	'get_clock','IfxScuCcu_getSpbFrequency'
	.calls	'get_clock','IfxScuCcu_getSourceFrequency'
	.calls	'disable_Watchdog','IfxScuWdt_getCpuWatchdogPassword'
	.calls	'disable_Watchdog','IfxScuWdt_disableCpuWatchdog'
	.calls	'disable_Watchdog','IfxScuWdt_getSafetyWatchdogPassword'
	.calls	'disable_Watchdog','IfxScuWdt_disableSafetyWatchdog'
	.calls	'clock_init','interrupt_global_disable'
	.calls	'clock_init','disable_Watchdog'
	.calls	'clock_init','get_clock'
	.calls	'clock_init','system_delay_init'
	.calls	'clock_init','interrupt_global_enable'
	.calls	'cpu_wait_event_ready','system_delay_ms'
	.calls	'set_clock','',0
	.calls	'get_clock','',0
	.calls	'disable_Watchdog','',0
	.calls	'clock_init','',0
	.extern	IfxScuWdt_disableCpuWatchdog
	.extern	IfxScuWdt_disableSafetyWatchdog
	.extern	IfxScuWdt_getCpuWatchdogPassword
	.extern	IfxScuWdt_getSafetyWatchdogPassword
	.extern	IfxScuCcu_getCpuFrequency
	.extern	IfxScuCcu_getPllFrequency
	.extern	IfxScuCcu_getSourceFrequency
	.extern	IfxScuCcu_getSpbFrequency
	.extern	IfxScuCcu_setCpuFrequency
	.extern	system_delay_ms
	.extern	system_delay_init
	.extern	interrupt_global_enable
	.extern	interrupt_global_disable
	.calls	'cpu_wait_event_ready','',0
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L25:
	.word	81721
	.half	3
	.word	.L26
	.byte	4
.L24:
	.byte	1
	.byte	'../libraries/zf_common/zf_common_clock.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L27
	.byte	2,1,1,3
	.word	203
	.byte	4
	.byte	'Ifx__jump_and_link',0,3,1,61,17,1,1,5
	.byte	'fun',0,1,61,43
	.word	206
	.byte	6,0,7
	.byte	'__fract',0,4,128,1
.L73:
	.byte	7
	.byte	'float',0,4,4,8
	.byte	'Ifx__float_to_fract',0,3,1,152,2,18
	.word	251
	.byte	1,1,5
	.byte	'a',0,1,152,2,44
	.word	263
	.byte	6,0,4
	.byte	'Ifx__stopPerfCounters',0,3,1,172,2,17,1,1,6,0,7
	.byte	'unsigned long long int',0,8,7,9
	.byte	'void',0,3
	.word	375
	.byte	8
	.byte	'__ld64',0,3,2,135,1,19
	.word	349
	.byte	1,1,5
	.byte	'addr',0,2,135,1,32
	.word	381
	.byte	6,0,4
	.byte	'__st64',0,3,2,143,1,17,1,1,5
	.byte	'addr',0,2,143,1,30
	.word	381
	.byte	5
	.byte	'value',0,2,143,1,43
	.word	349
	.byte	6,0,7
	.byte	'unsigned int',0,4,7,7
	.byte	'int',0,4,5,7
	.byte	'unsigned int',0,4,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON0_Bits',0,4,241,8,16,4,11
	.byte	'ENDINIT',0,4
	.word	490
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	490
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	490
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	490
	.byte	16,0,2,35,0,0,12,4,247,14,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	506
	.byte	4,2,35,0,0
.L80:
	.byte	7
	.byte	'unsigned char',0,1,8,7
	.byte	'unsigned short int',0,2,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON1_Bits',0,4,250,8,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	2,6,2,35,0,11
	.byte	'IR0',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	642
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	659
	.byte	16,0,2,35,2,0,12,4,255,14,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	681
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_WDTCPU_SR_Bits',0,4,137,9,16,4,11
	.byte	'AE',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	642
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	659
	.byte	16,0,2,35,2,0,12,4,135,15,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	925
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_WDTCPU',0,4,175,15,25,12,13
	.byte	'CON0',0
	.word	602
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	885
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	1116
	.byte	4,2,35,8,0,14
	.word	1156
	.byte	3
	.word	1219
	.byte	4
	.byte	'IfxScuWdt_clearCpuEndinitInline',0,3,3,181,3,17,1,1,5
	.byte	'watchdog',0,3,181,3,65
	.word	1224
	.byte	5
	.byte	'password',0,3,181,3,82
	.word	659
	.byte	6,0,4
	.byte	'IfxScuWdt_setCpuEndinitInline',0,3,3,140,4,17,1,1,5
	.byte	'watchdog',0,3,140,4,63
	.word	1224
	.byte	5
	.byte	'password',0,3,140,4,80
	.word	659
	.byte	6,0,8
	.byte	'IfxScuWdt_getCpuWatchdogPasswordInline',0,3,3,227,3,19
	.word	659
	.byte	1,1,5
	.byte	'watchdog',0,3,227,3,74
	.word	1224
	.byte	6,0,10
	.byte	'_Ifx_P_OUT_Bits',0,6,143,3,16,4,11
	.byte	'P0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	642
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	642
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	642
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	659
	.byte	16,0,2,35,2,0,12,6,181,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1454
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMR_Bits',0,6,169,2,16,4,11
	.byte	'PS0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	642
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	642
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	642
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'PCL0',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	642
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	642
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	642
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	642
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	642
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	642
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	642
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	642
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	642
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	642
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	642
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	642
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	642
	.byte	1,0,2,35,3,0,12,6,133,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1770
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ID_Bits',0,6,110,16,4,11
	.byte	'MODREV',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	659
	.byte	16,0,2,35,2,0,12,6,148,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2341
	.byte	4,2,35,0,0,15,4
	.word	642
	.byte	16,3,0,10
	.byte	'_Ifx_P_IOCR0_Bits',0,6,140,1,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	3,5,2,35,0,11
	.byte	'PC0',0,1
	.word	642
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	642
	.byte	3,5,2,35,1,11
	.byte	'PC1',0,1
	.word	642
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	642
	.byte	3,5,2,35,2,11
	.byte	'PC2',0,1
	.word	642
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	642
	.byte	3,5,2,35,3,11
	.byte	'PC3',0,1
	.word	642
	.byte	5,0,2,35,3,0,12,6,164,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2469
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR4_Bits',0,6,166,1,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	3,5,2,35,0,11
	.byte	'PC4',0,1
	.word	642
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	642
	.byte	3,5,2,35,1,11
	.byte	'PC5',0,1
	.word	642
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	642
	.byte	3,5,2,35,2,11
	.byte	'PC6',0,1
	.word	642
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	642
	.byte	3,5,2,35,3,11
	.byte	'PC7',0,1
	.word	642
	.byte	5,0,2,35,3,0,12,6,180,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2684
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR8_Bits',0,6,179,1,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	3,5,2,35,0,11
	.byte	'PC8',0,1
	.word	642
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	642
	.byte	3,5,2,35,1,11
	.byte	'PC9',0,1
	.word	642
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	642
	.byte	3,5,2,35,2,11
	.byte	'PC10',0,1
	.word	642
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	642
	.byte	3,5,2,35,3,11
	.byte	'PC11',0,1
	.word	642
	.byte	5,0,2,35,3,0,12,6,188,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2899
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR12_Bits',0,6,153,1,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	3,5,2,35,0,11
	.byte	'PC12',0,1
	.word	642
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	642
	.byte	3,5,2,35,1,11
	.byte	'PC13',0,1
	.word	642
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	642
	.byte	3,5,2,35,2,11
	.byte	'PC14',0,1
	.word	642
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	642
	.byte	3,5,2,35,3,11
	.byte	'PC15',0,1
	.word	642
	.byte	5,0,2,35,3,0,12,6,172,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3116
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IN_Bits',0,6,118,16,4,11
	.byte	'P0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	642
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	642
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	642
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	659
	.byte	16,0,2,35,2,0,12,6,156,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3336
	.byte	4,2,35,0,0,15,24
	.word	642
	.byte	16,23,0,10
	.byte	'_Ifx_P_PDR0_Bits',0,6,205,3,16,4,11
	.byte	'PD0',0,1
	.word	642
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	642
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'PD2',0,1
	.word	642
	.byte	3,5,2,35,1,11
	.byte	'PL2',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'PD3',0,1
	.word	642
	.byte	3,1,2,35,1,11
	.byte	'PL3',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'PD4',0,1
	.word	642
	.byte	3,5,2,35,2,11
	.byte	'PL4',0,1
	.word	642
	.byte	1,4,2,35,2,11
	.byte	'PD5',0,1
	.word	642
	.byte	3,1,2,35,2,11
	.byte	'PL5',0,1
	.word	642
	.byte	1,0,2,35,2,11
	.byte	'PD6',0,1
	.word	642
	.byte	3,5,2,35,3,11
	.byte	'PL6',0,1
	.word	642
	.byte	1,4,2,35,3,11
	.byte	'PD7',0,1
	.word	642
	.byte	3,1,2,35,3,11
	.byte	'PL7',0,1
	.word	642
	.byte	1,0,2,35,3,0,12,6,205,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3659
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PDR1_Bits',0,6,226,3,16,4,11
	.byte	'PD8',0,1
	.word	642
	.byte	3,5,2,35,0,11
	.byte	'PL8',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'PD9',0,1
	.word	642
	.byte	3,1,2,35,0,11
	.byte	'PL9',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'PD10',0,1
	.word	642
	.byte	3,5,2,35,1,11
	.byte	'PL10',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'PD11',0,1
	.word	642
	.byte	3,1,2,35,1,11
	.byte	'PL11',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'PD12',0,1
	.word	642
	.byte	3,5,2,35,2,11
	.byte	'PL12',0,1
	.word	642
	.byte	1,4,2,35,2,11
	.byte	'PD13',0,1
	.word	642
	.byte	3,1,2,35,2,11
	.byte	'PL13',0,1
	.word	642
	.byte	1,0,2,35,2,11
	.byte	'PD14',0,1
	.word	642
	.byte	3,5,2,35,3,11
	.byte	'PL14',0,1
	.word	642
	.byte	1,4,2,35,3,11
	.byte	'PD15',0,1
	.word	642
	.byte	3,1,2,35,3,11
	.byte	'PL15',0,1
	.word	642
	.byte	1,0,2,35,3,0,12,6,213,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3963
	.byte	4,2,35,0,0,15,8
	.word	642
	.byte	16,7,0,10
	.byte	'_Ifx_P_ESR_Bits',0,6,88,16,4,11
	.byte	'EN0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	642
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	642
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	642
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	659
	.byte	16,0,2,35,2,0,12,6,140,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4288
	.byte	4,2,35,0,0,15,12
	.word	642
	.byte	16,11,0,10
	.byte	'_Ifx_P_PDISC_Bits',0,6,183,3,16,4,11
	.byte	'PDIS0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'PDIS2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'PDIS3',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'PDIS4',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'PDIS5',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'PDIS6',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'PDIS7',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'PDIS8',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'PDIS9',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'PDIS10',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'PDIS11',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'PDIS12',0,1
	.word	642
	.byte	1,3,2,35,1,11
	.byte	'PDIS13',0,1
	.word	642
	.byte	1,2,2,35,1,11
	.byte	'PDIS14',0,1
	.word	642
	.byte	1,1,2,35,1,11
	.byte	'PDIS15',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	659
	.byte	16,0,2,35,2,0,12,6,197,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4628
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PCSR_Bits',0,6,165,3,16,4,11
	.byte	'SEL0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'SEL1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'SEL2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'SEL3',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'SEL4',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'SEL5',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'SEL6',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'SEL7',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	642
	.byte	2,6,2,35,1,11
	.byte	'SEL10',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'SEL11',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	467
	.byte	19,1,2,35,0,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,12,6,189,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4994
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR0_Bits',0,6,206,2,16,4,11
	.byte	'PS0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	467
	.byte	28,0,2,35,0,0,12,6,149,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5280
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR4_Bits',0,6,227,2,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	4,4,2,35,0,11
	.byte	'PS4',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	467
	.byte	24,0,2,35,0,0,12,6,165,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5427
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR8_Bits',0,6,238,2,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'PS8',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	467
	.byte	20,0,2,35,0,0,12,6,173,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5596
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR12_Bits',0,6,216,2,16,4,11
	.byte	'reserved_0',0,2
	.word	659
	.byte	12,4,2,35,0,11
	.byte	'PS12',0,1
	.word	642
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	642
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	642
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	659
	.byte	16,0,2,35,2,0,12,6,157,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5768
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR0_Bits',0,6,232,1,16,4,11
	.byte	'reserved_0',0,2
	.word	659
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	642
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	642
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	659
	.byte	12,0,2,35,2,0,12,6,229,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5943
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR4_Bits',0,6,253,1,16,4,11
	.byte	'reserved_0',0,4
	.word	467
	.byte	20,12,2,35,0,11
	.byte	'PCL4',0,1
	.word	642
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	642
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	642
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	642
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	642
	.byte	8,0,2,35,3,0,12,6,245,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6117
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR8_Bits',0,6,136,2,16,4,11
	.byte	'reserved_0',0,4
	.word	467
	.byte	24,8,2,35,0,11
	.byte	'PCL8',0,1
	.word	642
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	642
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	642
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	642
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	642
	.byte	4,0,2,35,3,0,12,6,253,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6291
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR12_Bits',0,6,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	467
	.byte	28,4,2,35,0,11
	.byte	'PCL12',0,1
	.word	642
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	642
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	642
	.byte	1,0,2,35,3,0,12,6,237,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6467
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR_Bits',0,6,249,2,16,4,11
	.byte	'PS0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	642
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	642
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	642
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	659
	.byte	16,0,2,35,2,0,12,6,141,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6623
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR_Bits',0,6,147,2,16,4,11
	.byte	'reserved_0',0,2
	.word	659
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	642
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	642
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	642
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	642
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	642
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	642
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	642
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	642
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	642
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	642
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	642
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	642
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	642
	.byte	1,0,2,35,3,0,12,6,221,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6956
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR0_Bits',0,6,192,1,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	467
	.byte	30,0,2,35,0,0,12,6,196,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7304
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_Bits',0,6,200,1,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	467
	.byte	30,0,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_P21_Bits',0,6,208,1,16,4,11
	.byte	'RDIS_CTRL',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'RX_DIS',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'TERM',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'LRXTERM',0,1
	.word	642
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	467
	.byte	24,0,2,35,0,0,12,6,204,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7428
	.byte	4,2,35,0,13
	.byte	'B_P21',0
	.word	7512
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR2_Bits',0,6,218,1,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'LVDSR',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'LVDSRL',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	642
	.byte	2,4,2,35,1,11
	.byte	'TDIS_CTRL',0,1
	.word	642
	.byte	1,3,2,35,1,11
	.byte	'TX_DIS',0,1
	.word	642
	.byte	1,2,2,35,1,11
	.byte	'TX_PD',0,1
	.word	642
	.byte	1,1,2,35,1,11
	.byte	'TX_PWDPD',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	659
	.byte	16,0,2,35,2,0,12,6,213,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7692
	.byte	4,2,35,0,0,15,76
	.word	642
	.byte	16,75,0,10
	.byte	'_Ifx_P_ACCEN1_Bits',0,6,82,16,4,11
	.byte	'reserved_0',0,4
	.word	467
	.byte	32,0,2,35,0,0,12,6,132,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7945
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ACCEN0_Bits',0,6,45,16,4,11
	.byte	'EN0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	642
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	642
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	642
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	642
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	642
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	642
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	642
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	642
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	642
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	642
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	642
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	642
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	642
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	642
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	642
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	642
	.byte	1,0,2,35,3,0,12,6,252,3,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8032
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P',0,6,229,5,25,128,2,13
	.byte	'OUT',0
	.word	1730
	.byte	4,2,35,0,13
	.byte	'OMR',0
	.word	2301
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	2420
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	2460
	.byte	4,2,35,12,13
	.byte	'IOCR0',0
	.word	2644
	.byte	4,2,35,16,13
	.byte	'IOCR4',0
	.word	2859
	.byte	4,2,35,20,13
	.byte	'IOCR8',0
	.word	3076
	.byte	4,2,35,24,13
	.byte	'IOCR12',0
	.word	3296
	.byte	4,2,35,28,13
	.byte	'reserved_20',0
	.word	2460
	.byte	4,2,35,32,13
	.byte	'IN',0
	.word	3610
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	3650
	.byte	24,2,35,40,13
	.byte	'PDR0',0
	.word	3923
	.byte	4,2,35,64,13
	.byte	'PDR1',0
	.word	4239
	.byte	4,2,35,68,13
	.byte	'reserved_48',0
	.word	4279
	.byte	8,2,35,72,13
	.byte	'ESR',0
	.word	4579
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	4619
	.byte	12,2,35,84,13
	.byte	'PDISC',0
	.word	4954
	.byte	4,2,35,96,13
	.byte	'PCSR',0
	.word	5240
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	4279
	.byte	8,2,35,104,13
	.byte	'OMSR0',0
	.word	5387
	.byte	4,2,35,112,13
	.byte	'OMSR4',0
	.word	5556
	.byte	4,2,35,116,13
	.byte	'OMSR8',0
	.word	5728
	.byte	4,2,35,120,13
	.byte	'OMSR12',0
	.word	5903
	.byte	4,2,35,124,13
	.byte	'OMCR0',0
	.word	6077
	.byte	4,3,35,128,1,13
	.byte	'OMCR4',0
	.word	6251
	.byte	4,3,35,132,1,13
	.byte	'OMCR8',0
	.word	6427
	.byte	4,3,35,136,1,13
	.byte	'OMCR12',0
	.word	6583
	.byte	4,3,35,140,1,13
	.byte	'OMSR',0
	.word	6916
	.byte	4,3,35,144,1,13
	.byte	'OMCR',0
	.word	7264
	.byte	4,3,35,148,1,13
	.byte	'reserved_98',0
	.word	4279
	.byte	8,3,35,152,1,13
	.byte	'LPCR0',0
	.word	7388
	.byte	4,3,35,160,1,13
	.byte	'LPCR1',0
	.word	7637
	.byte	4,3,35,164,1,13
	.byte	'LPCR2',0
	.word	7896
	.byte	4,3,35,168,1,13
	.byte	'reserved_A4',0
	.word	7936
	.byte	76,3,35,172,1,13
	.byte	'ACCEN1',0
	.word	7992
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	8559
	.byte	4,3,35,252,1,0,14
	.word	8599
	.byte	3
	.word	9202
	.byte	17,5,83,9,1,18
	.byte	'IfxPort_InputMode_undefined',0,127,18
	.byte	'IfxPort_InputMode_noPullDevice',0,0,18
	.byte	'IfxPort_InputMode_pullDown',0,8,18
	.byte	'IfxPort_InputMode_pullUp',0,16,0,4
	.byte	'IfxPort_setPinModeInput',0,3,5,196,4,17,1,1,5
	.byte	'port',0,5,196,4,48
	.word	9207
	.byte	5
	.byte	'pinIndex',0,5,196,4,60
	.word	642
	.byte	5
	.byte	'mode',0,5,196,4,88
	.word	9212
	.byte	6,0,17,5,172,1,9,4,18
	.byte	'IfxPort_State_notChanged',0,0,18
	.byte	'IfxPort_State_high',0,1,18
	.byte	'IfxPort_State_low',0,128,128,4,18
	.byte	'IfxPort_State_toggled',0,129,128,4,0,4
	.byte	'IfxPort_setPinState',0,3,5,208,4,17,1,1,5
	.byte	'port',0,5,208,4,44
	.word	9207
	.byte	5
	.byte	'pinIndex',0,5,208,4,56
	.word	642
	.byte	5
	.byte	'action',0,5,208,4,80
	.word	9417
	.byte	6,0
.L75:
	.byte	8
	.byte	'IfxScuCcu_getStmFrequency',0,3,7,226,8,20
	.word	263
	.byte	1,1
.L76:
	.byte	6,0,17,9,127,9,1,18
	.byte	'IfxCpu_Id_0',0,0,18
	.byte	'IfxCpu_Id_1',0,1,18
	.byte	'IfxCpu_Id_none',0,2,0
.L83:
	.byte	8
	.byte	'IfxCpu_getCoreId',0,3,8,133,6,22
	.word	9638
	.byte	1,1
.L84:
	.byte	6,0,17,9,156,1,9,1,18
	.byte	'IfxCpu_ResourceCpu_0',0,0,18
	.byte	'IfxCpu_ResourceCpu_1',0,1,18
	.byte	'IfxCpu_ResourceCpu_none',0,2,0
.L59:
	.byte	8
	.byte	'IfxCpu_getCoreIndex',0,3,8,141,6,31
	.word	9720
	.byte	1,1
.L61:
	.byte	6,0,8
	.byte	'IfxCpu_areInterruptsEnabled',0,3,8,139,5,20
	.word	642
	.byte	1,1,6,0,7
	.byte	'unsigned long int',0,4,7,8
	.byte	'IfxCpu_getPerformanceCounter',0,3,8,161,6,19
	.word	9875
	.byte	1,1,5
	.byte	'address',0,8,161,6,55
	.word	659
	.byte	6,0,8
	.byte	'IfxCpu_getPerformanceCounterStickyOverflow',0,3,8,190,6,20
	.word	642
	.byte	1,1,5
	.byte	'address',0,8,190,6,70
	.word	659
	.byte	6,0,4
	.byte	'IfxCpu_updatePerformanceCounter',0,3,8,172,8,17,1,1,5
	.byte	'address',0,8,172,8,56
	.word	9875
	.byte	5
	.byte	'count',0,8,172,8,72
	.word	9875
	.byte	19,6,0,0,10
	.byte	'_Ifx_SRC_SRCR_Bits',0,11,45,16,4,11
	.byte	'SRPN',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	642
	.byte	2,6,2,35,1,11
	.byte	'SRE',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'TOS',0,1
	.word	642
	.byte	2,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	642
	.byte	3,0,2,35,1,11
	.byte	'ECC',0,1
	.word	642
	.byte	6,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	642
	.byte	2,0,2,35,2,11
	.byte	'SRR',0,1
	.word	642
	.byte	1,7,2,35,3,11
	.byte	'CLRR',0,1
	.word	642
	.byte	1,6,2,35,3,11
	.byte	'SETR',0,1
	.word	642
	.byte	1,5,2,35,3,11
	.byte	'IOV',0,1
	.word	642
	.byte	1,4,2,35,3,11
	.byte	'IOVCLR',0,1
	.word	642
	.byte	1,3,2,35,3,11
	.byte	'SWS',0,1
	.word	642
	.byte	1,2,2,35,3,11
	.byte	'SWSCLR',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	642
	.byte	1,0,2,35,3,0,12,11,70,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10106
	.byte	4,2,35,0,0,14
	.word	10396
	.byte	3
	.word	10435
	.byte	4
	.byte	'IfxSrc_clearRequest',0,3,10,250,1,17,1,1,5
	.byte	'src',0,10,250,1,60
	.word	10440
	.byte	6,0,10
	.byte	'_Ifx_STM_CLC_Bits',0,13,100,16,4,11
	.byte	'DISR',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'DISS',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'EDIS',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	467
	.byte	28,0,2,35,0,0,12,13,149,2,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10488
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_ID_Bits',0,13,142,1,16,4,11
	.byte	'MODREV',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	659
	.byte	16,0,2,35,2,0,12,13,181,2,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10644
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM0_Bits',0,13,192,1,16,4,11
	.byte	'STM31_0',0,4
	.word	467
	.byte	32,0,2,35,0,0,12,13,229,2,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10766
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM1_Bits',0,13,204,1,16,4,11
	.byte	'STM35_4',0,4
	.word	467
	.byte	32,0,2,35,0,0,12,13,245,2,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10851
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM2_Bits',0,13,210,1,16,4,11
	.byte	'STM39_8',0,4
	.word	467
	.byte	32,0,2,35,0,0,12,13,253,2,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10936
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM3_Bits',0,13,216,1,16,4,11
	.byte	'STM43_12',0,4
	.word	467
	.byte	32,0,2,35,0,0,12,13,133,3,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11021
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM4_Bits',0,13,222,1,16,4,11
	.byte	'STM47_16',0,4
	.word	467
	.byte	32,0,2,35,0,0,12,13,141,3,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11107
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM5_Bits',0,13,228,1,16,4,11
	.byte	'STM51_20',0,4
	.word	467
	.byte	32,0,2,35,0,0,12,13,149,3,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11193
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM6_Bits',0,13,234,1,16,4,11
	.byte	'STM63_32',0,4
	.word	467
	.byte	32,0,2,35,0,0,12,13,157,3,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11279
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_CAP_Bits',0,13,88,16,4,11
	.byte	'STMCAP63_32',0,4
	.word	467
	.byte	32,0,2,35,0,0,12,13,133,2,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11365
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_CMP_Bits',0,13,123,16,4,11
	.byte	'CMPVAL',0,4
	.word	467
	.byte	32,0,2,35,0,0,12,13,165,2,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11452
	.byte	4,2,35,0,0,15,8
	.word	11494
	.byte	16,1,0,10
	.byte	'_Ifx_STM_CMCON_Bits',0,13,110,16,4,11
	.byte	'MSIZE0',0,1
	.word	642
	.byte	5,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	642
	.byte	3,0,2,35,0,11
	.byte	'MSTART0',0,1
	.word	642
	.byte	5,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	642
	.byte	3,0,2,35,1,11
	.byte	'MSIZE1',0,1
	.word	642
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	642
	.byte	3,0,2,35,2,11
	.byte	'MSTART1',0,1
	.word	642
	.byte	5,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	642
	.byte	3,0,2,35,3,0,12,13,157,2,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11543
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_ICR_Bits',0,13,129,1,16,4,11
	.byte	'CMP0EN',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'CMP0IR',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'CMP0OS',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'CMP1EN',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'CMP1IR',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'CMP1OS',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,4
	.word	467
	.byte	25,0,2,35,0,0,12,13,173,2,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11774
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_ISCR_Bits',0,13,150,1,16,4,11
	.byte	'CMP0IRR',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'CMP0IRS',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'CMP1IRR',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'CMP1IRS',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	467
	.byte	28,0,2,35,0,0,12,13,189,2,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11991
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM0SV_Bits',0,13,198,1,16,4,11
	.byte	'STM31_0',0,4
	.word	467
	.byte	32,0,2,35,0,0,12,13,237,2,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12155
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_CAPSV_Bits',0,13,94,16,4,11
	.byte	'STMCAP63_32',0,4
	.word	467
	.byte	32,0,2,35,0,0,12,13,141,2,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12242
	.byte	4,2,35,0,0,15,144,1
	.word	642
	.byte	16,143,1,0,10
	.byte	'_Ifx_STM_OCS_Bits',0,13,182,1,16,4,11
	.byte	'reserved_0',0,4
	.word	467
	.byte	24,8,2,35,0,11
	.byte	'SUS',0,1
	.word	642
	.byte	4,4,2,35,3,11
	.byte	'SUS_P',0,1
	.word	642
	.byte	1,3,2,35,3,11
	.byte	'SUSSTA',0,1
	.word	642
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	642
	.byte	2,0,2,35,3,0,12,13,221,2,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12342
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_KRSTCLR_Bits',0,13,175,1,16,4,11
	.byte	'CLR',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	467
	.byte	31,0,2,35,0,0,12,13,213,2,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12502
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_KRST1_Bits',0,13,168,1,16,4,11
	.byte	'RST',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	467
	.byte	31,0,2,35,0,0,12,13,205,2,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12608
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_KRST0_Bits',0,13,160,1,16,4,11
	.byte	'RST',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'RSTSTAT',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	467
	.byte	30,0,2,35,0,0,12,13,197,2,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12712
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_ACCEN1_Bits',0,13,82,16,4,11
	.byte	'reserved_0',0,4
	.word	467
	.byte	32,0,2,35,0,0,12,13,253,1,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12835
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_ACCEN0_Bits',0,13,45,16,4,11
	.byte	'EN0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	642
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	642
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	642
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	642
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	642
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	642
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	642
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	642
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	642
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	642
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	642
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	642
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	642
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	642
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	642
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	642
	.byte	1,0,2,35,3,0,12,13,245,1,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12924
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM',0,13,173,3,25,128,2,13
	.byte	'CLC',0
	.word	10604
	.byte	4,2,35,0,13
	.byte	'reserved_4',0
	.word	2460
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	10726
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	2460
	.byte	4,2,35,12,13
	.byte	'TIM0',0
	.word	10811
	.byte	4,2,35,16,13
	.byte	'TIM1',0
	.word	10896
	.byte	4,2,35,20,13
	.byte	'TIM2',0
	.word	10981
	.byte	4,2,35,24,13
	.byte	'TIM3',0
	.word	11067
	.byte	4,2,35,28,13
	.byte	'TIM4',0
	.word	11153
	.byte	4,2,35,32,13
	.byte	'TIM5',0
	.word	11239
	.byte	4,2,35,36,13
	.byte	'TIM6',0
	.word	11325
	.byte	4,2,35,40,13
	.byte	'CAP',0
	.word	11412
	.byte	4,2,35,44,13
	.byte	'CMP',0
	.word	11534
	.byte	8,2,35,48,13
	.byte	'CMCON',0
	.word	11734
	.byte	4,2,35,56,13
	.byte	'ICR',0
	.word	11951
	.byte	4,2,35,60,13
	.byte	'ISCR',0
	.word	12115
	.byte	4,2,35,64,13
	.byte	'reserved_44',0
	.word	4619
	.byte	12,2,35,68,13
	.byte	'TIM0SV',0
	.word	12202
	.byte	4,2,35,80,13
	.byte	'CAPSV',0
	.word	12291
	.byte	4,2,35,84,13
	.byte	'reserved_58',0
	.word	12331
	.byte	144,1,2,35,88,13
	.byte	'OCS',0
	.word	12462
	.byte	4,3,35,232,1,13
	.byte	'KRSTCLR',0
	.word	12568
	.byte	4,3,35,236,1,13
	.byte	'KRST1',0
	.word	12672
	.byte	4,3,35,240,1,13
	.byte	'KRST0',0
	.word	12795
	.byte	4,3,35,244,1,13
	.byte	'ACCEN1',0
	.word	12884
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	13453
	.byte	4,3,35,252,1,0,14
	.word	13493
	.byte	3
	.word	13913
	.byte	8
	.byte	'IfxStm_get',0,3,12,162,4,19
	.word	349
	.byte	1,1,5
	.byte	'stm',0,12,162,4,39
	.word	13918
	.byte	6,0
.L64:
	.byte	8
	.byte	'IfxStm_getFrequency',0,3,12,179,4,20
	.word	263
	.byte	1,1
.L66:
	.byte	5
	.byte	'stm',0,12,179,4,49
	.word	13918
.L68:
	.byte	19
.L72:
	.byte	6,6,0,0,8
	.byte	'IfxStm_getLower',0,3,12,190,4,19
	.word	9875
	.byte	1,1,5
	.byte	'stm',0,12,190,4,44
	.word	13918
	.byte	6,0,14
	.word	483
	.byte	20
	.byte	'__mfcr',0
	.word	14054
	.byte	1,1,1,1,21
	.word	483
	.byte	0,22
	.word	211
	.byte	23
	.word	237
	.byte	6,0,22
	.word	272
	.byte	23
	.word	304
	.byte	6,0,22
	.word	317
	.byte	6,0,22
	.word	386
	.byte	23
	.word	405
	.byte	6,0,22
	.word	421
	.byte	23
	.word	436
	.byte	23
	.word	450
	.byte	6,0,22
	.word	1229
	.byte	23
	.word	1269
	.byte	23
	.word	1287
	.byte	6,0,22
	.word	1307
	.byte	23
	.word	1345
	.byte	23
	.word	1363
	.byte	6,0,22
	.word	1383
	.byte	23
	.word	1434
	.byte	6,0,24
	.byte	'IfxScuWdt_disableCpuWatchdog',0,3,218,2,17,1,1,1,1,5
	.byte	'password',0,3,218,2,53
	.word	659
	.byte	0,24
	.byte	'IfxScuWdt_disableSafetyWatchdog',0,3,228,2,17,1,1,1,1,5
	.byte	'password',0,3,228,2,56
	.word	659
	.byte	0,25
	.byte	'IfxScuWdt_getCpuWatchdogPassword',0,3,129,3,19
	.word	659
	.byte	1,1,1,1,25
	.byte	'IfxScuWdt_getSafetyWatchdogPassword',0,3,143,3,19
	.word	659
	.byte	1,1,1,1,22
	.word	9337
	.byte	23
	.word	9369
	.byte	23
	.word	9383
	.byte	23
	.word	9401
	.byte	6,0,22
	.word	9520
	.byte	23
	.word	9548
	.byte	23
	.word	9562
	.byte	23
	.word	9580
	.byte	6,0,22
	.word	9598
	.byte	6,0,26
	.word	9720
	.byte	27
	.byte	'IfxScuCcu_getCpuFrequency',0,7,235,6,20
	.word	263
	.byte	1,1,1,1,5
	.byte	'cpu',0,7,235,6,71
	.word	14450
	.byte	0,25
	.byte	'IfxScuCcu_getPllFrequency',0,7,161,7,20
	.word	263
	.byte	1,1,1,1,25
	.byte	'IfxScuCcu_getSourceFrequency',0,7,173,7,20
	.word	263
	.byte	1,1,1,1,25
	.byte	'IfxScuCcu_getSpbFrequency',0,7,179,7,20
	.word	263
	.byte	1,1,1,1,27
	.byte	'IfxScuCcu_setCpuFrequency',0,7,193,7,20
	.word	263
	.byte	1,1,1,1,5
	.byte	'cpu',0,7,193,7,65
	.word	9720
	.byte	5
	.byte	'cpuFreq',0,7,193,7,78
	.word	263
	.byte	0,22
	.word	9689
	.byte	6,0,22
	.word	9799
	.byte	6,0,22
	.word	9833
	.byte	6,0,22
	.word	9896
	.byte	23
	.word	9937
	.byte	6,0,22
	.word	9956
	.byte	23
	.word	10011
	.byte	6,0,22
	.word	10030
	.byte	23
	.word	10070
	.byte	23
	.word	10087
	.byte	19,6,0,0,22
	.word	10445
	.byte	23
	.word	10473
	.byte	6,0,22
	.word	13923
	.byte	23
	.word	13946
	.byte	6,0,22
	.word	13961
	.byte	23
	.word	13993
	.byte	19,19,28
	.word	9598
	.byte	29
	.word	9636
	.byte	0,0,6,0,0,22
	.word	14011
	.byte	23
	.word	14039
	.byte	6,0,24
	.byte	'system_delay_ms',0,14,46,9,1,1,1,1,5
	.byte	'time',0,14,46,45
	.word	9875
	.byte	0,30
	.byte	'system_delay_init',0,14,47,9,1,1,1,1,24
	.byte	'interrupt_global_enable',0,15,41,8,1,1,1,1,5
	.byte	'primask',0,15,41,40
	.word	9875
	.byte	0,25
	.byte	'interrupt_global_disable',0,15,42,8
	.word	9875
	.byte	1,1,1,1,10
	.byte	'_Ifx_CPU_CORE_ID_Bits',0,16,92,16,4,11
	.byte	'CORE_ID',0,4
	.word	490
	.byte	3,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	490
	.byte	29,0,2,35,0,0
.L62:
	.byte	12,16,223,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14975
	.byte	4,2,35,0,0,7
	.byte	'short int',0,2,5,31
	.byte	'__wchar_t',0,17,1,1
	.word	15084
	.byte	31
	.byte	'__size_t',0,17,1,1
	.word	467
	.byte	31
	.byte	'__ptrdiff_t',0,17,1,1
	.word	483
	.byte	32,1,3
	.word	15152
	.byte	31
	.byte	'__codeptr',0,17,1,1
	.word	15154
	.byte	31
	.byte	'__intptr_t',0,17,1,1
	.word	483
	.byte	31
	.byte	'__uintptr_t',0,17,1,1
	.word	467
	.byte	17,18,236,10,9,1,18
	.byte	'IfxScu_CCUCON0_CLKSEL_fBack',0,0,18
	.byte	'IfxScu_CCUCON0_CLKSEL_fPll',0,1,0,31
	.byte	'IfxScu_CCUCON0_CLKSEL',0,18,240,10,3
	.word	15216
	.byte	17,18,250,10,9,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy16384',0,0,18
	.byte	'IfxScu_WDTCON1_IR_divBy256',0,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy64',0,2,0,31
	.byte	'IfxScu_WDTCON1_IR',0,18,255,10,3
	.word	15313
	.byte	10
	.byte	'_Ifx_SCU_ACCEN0_Bits',0,4,45,16,4,11
	.byte	'EN0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	642
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	642
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	642
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	642
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	642
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	642
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	642
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	642
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	642
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	642
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	642
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	642
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	642
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	642
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	642
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	642
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_ACCEN0_Bits',0,4,79,3
	.word	15435
	.byte	10
	.byte	'_Ifx_SCU_ACCEN1_Bits',0,4,82,16,4,11
	.byte	'reserved_0',0,4
	.word	467
	.byte	32,0,2,35,0,0,31
	.byte	'Ifx_SCU_ACCEN1_Bits',0,4,85,3
	.word	15992
	.byte	10
	.byte	'_Ifx_SCU_ARSTDIS_Bits',0,4,88,16,4,11
	.byte	'STM0DIS',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'STM1DIS',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'STM2DIS',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	467
	.byte	29,0,2,35,0,0,31
	.byte	'Ifx_SCU_ARSTDIS_Bits',0,4,94,3
	.word	16069
	.byte	10
	.byte	'_Ifx_SCU_CCUCON0_Bits',0,4,97,16,4,11
	.byte	'BAUD1DIV',0,1
	.word	642
	.byte	4,4,2,35,0,11
	.byte	'BAUD2DIV',0,1
	.word	642
	.byte	4,0,2,35,0,11
	.byte	'SRIDIV',0,1
	.word	642
	.byte	4,4,2,35,1,11
	.byte	'LPDIV',0,1
	.word	642
	.byte	4,0,2,35,1,11
	.byte	'SPBDIV',0,1
	.word	642
	.byte	4,4,2,35,2,11
	.byte	'FSI2DIV',0,1
	.word	642
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	642
	.byte	2,0,2,35,2,11
	.byte	'FSIDIV',0,1
	.word	642
	.byte	2,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	642
	.byte	2,4,2,35,3,11
	.byte	'CLKSEL',0,1
	.word	642
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_CCUCON0_Bits',0,4,111,3
	.word	16205
	.byte	10
	.byte	'_Ifx_SCU_CCUCON1_Bits',0,4,114,16,4,11
	.byte	'CANDIV',0,1
	.word	642
	.byte	4,4,2,35,0,11
	.byte	'ERAYDIV',0,1
	.word	642
	.byte	4,0,2,35,0,11
	.byte	'STMDIV',0,1
	.word	642
	.byte	4,4,2,35,1,11
	.byte	'GTMDIV',0,1
	.word	642
	.byte	4,0,2,35,1,11
	.byte	'ETHDIV',0,1
	.word	642
	.byte	4,4,2,35,2,11
	.byte	'ASCLINFDIV',0,1
	.word	642
	.byte	4,0,2,35,2,11
	.byte	'ASCLINSDIV',0,1
	.word	642
	.byte	4,4,2,35,3,11
	.byte	'INSEL',0,1
	.word	642
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_CCUCON1_Bits',0,4,126,3
	.word	16485
	.byte	10
	.byte	'_Ifx_SCU_CCUCON2_Bits',0,4,129,1,16,4,11
	.byte	'BBBDIV',0,1
	.word	642
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	467
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_CCUCON2_Bits',0,4,135,1,3
	.word	16723
	.byte	10
	.byte	'_Ifx_SCU_CCUCON3_Bits',0,4,138,1,16,4,11
	.byte	'PLLDIV',0,1
	.word	642
	.byte	6,2,2,35,0,11
	.byte	'PLLSEL',0,1
	.word	642
	.byte	2,0,2,35,0,11
	.byte	'PLLERAYDIV',0,1
	.word	642
	.byte	6,2,2,35,1,11
	.byte	'PLLERAYSEL',0,1
	.word	642
	.byte	2,0,2,35,1,11
	.byte	'SRIDIV',0,1
	.word	642
	.byte	6,2,2,35,2,11
	.byte	'SRISEL',0,1
	.word	642
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	642
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	642
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_CCUCON3_Bits',0,4,150,1,3
	.word	16851
	.byte	10
	.byte	'_Ifx_SCU_CCUCON4_Bits',0,4,153,1,16,4,11
	.byte	'SPBDIV',0,1
	.word	642
	.byte	6,2,2,35,0,11
	.byte	'SPBSEL',0,1
	.word	642
	.byte	2,0,2,35,0,11
	.byte	'GTMDIV',0,1
	.word	642
	.byte	6,2,2,35,1,11
	.byte	'GTMSEL',0,1
	.word	642
	.byte	2,0,2,35,1,11
	.byte	'STMDIV',0,1
	.word	642
	.byte	6,2,2,35,2,11
	.byte	'STMSEL',0,1
	.word	642
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	642
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	642
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_CCUCON4_Bits',0,4,165,1,3
	.word	17094
	.byte	10
	.byte	'_Ifx_SCU_CCUCON5_Bits',0,4,168,1,16,4,11
	.byte	'MAXDIV',0,1
	.word	642
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	467
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_CCUCON5_Bits',0,4,174,1,3
	.word	17329
	.byte	10
	.byte	'_Ifx_SCU_CCUCON6_Bits',0,4,177,1,16,4,11
	.byte	'CPU0DIV',0,1
	.word	642
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	467
	.byte	26,0,2,35,0,0,31
	.byte	'Ifx_SCU_CCUCON6_Bits',0,4,181,1,3
	.word	17457
	.byte	10
	.byte	'_Ifx_SCU_CCUCON7_Bits',0,4,184,1,16,4,11
	.byte	'CPU1DIV',0,1
	.word	642
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	467
	.byte	26,0,2,35,0,0,31
	.byte	'Ifx_SCU_CCUCON7_Bits',0,4,188,1,3
	.word	17557
	.byte	10
	.byte	'_Ifx_SCU_CHIPID_Bits',0,4,191,1,16,4,11
	.byte	'CHREV',0,1
	.word	642
	.byte	6,2,2,35,0,11
	.byte	'CHTEC',0,1
	.word	642
	.byte	2,0,2,35,0,11
	.byte	'CHID',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'EEA',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'UCODE',0,1
	.word	642
	.byte	7,0,2,35,2,11
	.byte	'FSIZE',0,1
	.word	642
	.byte	4,4,2,35,3,11
	.byte	'SP',0,1
	.word	642
	.byte	2,2,2,35,3,11
	.byte	'SEC',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	642
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_CHIPID_Bits',0,4,202,1,3
	.word	17657
	.byte	10
	.byte	'_Ifx_SCU_DTSCON_Bits',0,4,205,1,16,4,11
	.byte	'PWD',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'START',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	642
	.byte	2,4,2,35,0,11
	.byte	'CAL',0,4
	.word	467
	.byte	20,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	642
	.byte	7,1,2,35,3,11
	.byte	'SLCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_DTSCON_Bits',0,4,213,1,3
	.word	17865
	.byte	10
	.byte	'_Ifx_SCU_DTSLIM_Bits',0,4,216,1,16,4,11
	.byte	'LOWER',0,2
	.word	659
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	642
	.byte	5,1,2,35,1,11
	.byte	'LLU',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'UPPER',0,2
	.word	659
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	642
	.byte	4,2,2,35,3,11
	.byte	'SLCK',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'UOF',0,1
	.word	642
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_DTSLIM_Bits',0,4,225,1,3
	.word	18030
	.byte	10
	.byte	'_Ifx_SCU_DTSSTAT_Bits',0,4,228,1,16,4,11
	.byte	'RESULT',0,2
	.word	659
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	642
	.byte	4,2,2,35,1,11
	.byte	'RDY',0,1
	.word	642
	.byte	1,1,2,35,1,11
	.byte	'BUSY',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	659
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_SCU_DTSSTAT_Bits',0,4,235,1,3
	.word	18213
	.byte	10
	.byte	'_Ifx_SCU_EICR_Bits',0,4,238,1,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	4,4,2,35,0,11
	.byte	'EXIS0',0,1
	.word	642
	.byte	3,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'FEN0',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'REN0',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'LDEN0',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'EIEN0',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'INP0',0,1
	.word	642
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	467
	.byte	5,12,2,35,0,11
	.byte	'EXIS1',0,1
	.word	642
	.byte	3,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	642
	.byte	1,0,2,35,2,11
	.byte	'FEN1',0,1
	.word	642
	.byte	1,7,2,35,3,11
	.byte	'REN1',0,1
	.word	642
	.byte	1,6,2,35,3,11
	.byte	'LDEN1',0,1
	.word	642
	.byte	1,5,2,35,3,11
	.byte	'EIEN1',0,1
	.word	642
	.byte	1,4,2,35,3,11
	.byte	'INP1',0,1
	.word	642
	.byte	3,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	642
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EICR_Bits',0,4,129,2,3
	.word	18367
	.byte	10
	.byte	'_Ifx_SCU_EIFR_Bits',0,4,132,2,16,4,11
	.byte	'INTF0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'INTF1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'INTF2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'INTF3',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'INTF4',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'INTF5',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'INTF6',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'INTF7',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	467
	.byte	24,0,2,35,0,0,31
	.byte	'Ifx_SCU_EIFR_Bits',0,4,143,2,3
	.word	18731
	.byte	10
	.byte	'_Ifx_SCU_EMSR_Bits',0,4,146,2,16,4,11
	.byte	'POL',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'MODE',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'ENON',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'PSEL',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	659
	.byte	12,0,2,35,0,11
	.byte	'EMSF',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'SEMSF',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	642
	.byte	6,0,2,35,2,11
	.byte	'EMSFM',0,1
	.word	642
	.byte	2,6,2,35,3,11
	.byte	'SEMSFM',0,1
	.word	642
	.byte	2,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	642
	.byte	4,0,2,35,3,0,31
	.byte	'Ifx_SCU_EMSR_Bits',0,4,159,2,3
	.word	18942
	.byte	10
	.byte	'_Ifx_SCU_ESRCFG_Bits',0,4,162,2,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	7,1,2,35,0,11
	.byte	'EDCON',0,2
	.word	659
	.byte	2,7,2,35,0,11
	.byte	'reserved_9',0,4
	.word	467
	.byte	23,0,2,35,0,0,31
	.byte	'Ifx_SCU_ESRCFG_Bits',0,4,167,2,3
	.word	19194
	.byte	10
	.byte	'_Ifx_SCU_ESROCFG_Bits',0,4,170,2,16,4,11
	.byte	'ARI',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'ARC',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	467
	.byte	30,0,2,35,0,0,31
	.byte	'Ifx_SCU_ESROCFG_Bits',0,4,175,2,3
	.word	19312
	.byte	10
	.byte	'_Ifx_SCU_EVR13CON_Bits',0,4,178,2,16,4,11
	.byte	'reserved_0',0,4
	.word	467
	.byte	28,4,2,35,0,11
	.byte	'EVR13OFF',0,1
	.word	642
	.byte	1,3,2,35,3,11
	.byte	'BPEVR13OFF',0,1
	.word	642
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVR13CON_Bits',0,4,185,2,3
	.word	19423
	.byte	10
	.byte	'_Ifx_SCU_EVR33CON_Bits',0,4,188,2,16,4,11
	.byte	'reserved_0',0,4
	.word	467
	.byte	28,4,2,35,0,11
	.byte	'EVR33OFF',0,1
	.word	642
	.byte	1,3,2,35,3,11
	.byte	'BPEVR33OFF',0,1
	.word	642
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVR33CON_Bits',0,4,195,2,3
	.word	19586
	.byte	10
	.byte	'_Ifx_SCU_EVRADCSTAT_Bits',0,4,198,2,16,4,11
	.byte	'ADC13V',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'ADC33V',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'ADCSWDV',0,1
	.word	642
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	642
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	642
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVRADCSTAT_Bits',0,4,205,2,3
	.word	19749
	.byte	10
	.byte	'_Ifx_SCU_EVRDVSTAT_Bits',0,4,208,2,16,4,11
	.byte	'DVS13TRIM',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'DVS33TRIM',0,1
	.word	642
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	642
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	642
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVRDVSTAT_Bits',0,4,215,2,3
	.word	19907
	.byte	10
	.byte	'_Ifx_SCU_EVRMONCTRL_Bits',0,4,218,2,16,4,11
	.byte	'EVR13OVMOD',0,1
	.word	642
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	642
	.byte	2,4,2,35,0,11
	.byte	'EVR13UVMOD',0,1
	.word	642
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	642
	.byte	2,0,2,35,0,11
	.byte	'EVR33OVMOD',0,1
	.word	642
	.byte	2,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	642
	.byte	2,4,2,35,1,11
	.byte	'EVR33UVMOD',0,1
	.word	642
	.byte	2,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	642
	.byte	2,0,2,35,1,11
	.byte	'SWDOVMOD',0,1
	.word	642
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	642
	.byte	2,4,2,35,2,11
	.byte	'SWDUVMOD',0,1
	.word	642
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,2
	.word	659
	.byte	10,0,2,35,2,0,31
	.byte	'Ifx_SCU_EVRMONCTRL_Bits',0,4,232,2,3
	.word	20072
	.byte	10
	.byte	'_Ifx_SCU_EVROSCCTRL_Bits',0,4,235,2,16,4,11
	.byte	'OSCTRIM',0,2
	.word	659
	.byte	10,6,2,35,0,11
	.byte	'OSCPTAT',0,1
	.word	642
	.byte	6,0,2,35,1,11
	.byte	'OSCANASEL',0,1
	.word	642
	.byte	4,4,2,35,2,11
	.byte	'HPBGTRIM',0,2
	.word	659
	.byte	7,5,2,35,2,11
	.byte	'HPBGCLKEN',0,1
	.word	642
	.byte	1,4,2,35,3,11
	.byte	'OSC3V3',0,1
	.word	642
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	642
	.byte	2,1,2,35,3,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVROSCCTRL_Bits',0,4,245,2,3
	.word	20401
	.byte	10
	.byte	'_Ifx_SCU_EVROVMON_Bits',0,4,248,2,16,4,11
	.byte	'EVR13OVVAL',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'EVR33OVVAL',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'SWDOVVAL',0,1
	.word	642
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	642
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVROVMON_Bits',0,4,255,2,3
	.word	20622
	.byte	10
	.byte	'_Ifx_SCU_EVRRSTCON_Bits',0,4,130,3,16,4,11
	.byte	'RST13TRIM',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	467
	.byte	16,8,2,35,0,11
	.byte	'RST13OFF',0,1
	.word	642
	.byte	1,7,2,35,3,11
	.byte	'BPRST13OFF',0,1
	.word	642
	.byte	1,6,2,35,3,11
	.byte	'RST33OFF',0,1
	.word	642
	.byte	1,5,2,35,3,11
	.byte	'BPRST33OFF',0,1
	.word	642
	.byte	1,4,2,35,3,11
	.byte	'RSTSWDOFF',0,1
	.word	642
	.byte	1,3,2,35,3,11
	.byte	'BPRSTSWDOFF',0,1
	.word	642
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVRRSTCON_Bits',0,4,142,3,3
	.word	20785
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF1_Bits',0,4,145,3,16,4,11
	.byte	'SD5P',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'SD5I',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'SD5D',0,1
	.word	642
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	642
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVRSDCOEFF1_Bits',0,4,152,3,3
	.word	21057
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF2_Bits',0,4,155,3,16,4,11
	.byte	'SD33P',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'SD33I',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'SD33D',0,1
	.word	642
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	642
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVRSDCOEFF2_Bits',0,4,162,3,3
	.word	21210
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF3_Bits',0,4,165,3,16,4,11
	.byte	'CT5REG0',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'CT5REG1',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'CT5REG2',0,1
	.word	642
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	642
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVRSDCOEFF3_Bits',0,4,172,3,3
	.word	21366
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF4_Bits',0,4,175,3,16,4,11
	.byte	'CT5REG3',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'CT5REG4',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	659
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVRSDCOEFF4_Bits',0,4,181,3,3
	.word	21528
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF5_Bits',0,4,184,3,16,4,11
	.byte	'CT33REG0',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'CT33REG1',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'CT33REG2',0,1
	.word	642
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	642
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVRSDCOEFF5_Bits',0,4,191,3,3
	.word	21671
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF6_Bits',0,4,194,3,16,4,11
	.byte	'CT33REG3',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'CT33REG4',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	659
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVRSDCOEFF6_Bits',0,4,200,3,3
	.word	21836
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL1_Bits',0,4,203,3,16,4,11
	.byte	'SDFREQSPRD',0,2
	.word	659
	.byte	16,0,2,35,0,11
	.byte	'SDFREQ',0,1
	.word	642
	.byte	8,0,2,35,2,11
	.byte	'SDSTEP',0,1
	.word	642
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	642
	.byte	2,2,2,35,3,11
	.byte	'SDSAMPLE',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVRSDCTRL1_Bits',0,4,211,3,3
	.word	21981
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL2_Bits',0,4,214,3,16,4,11
	.byte	'DRVP',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'SDMINMAXDC',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'DRVN',0,1
	.word	642
	.byte	8,0,2,35,2,11
	.byte	'SDLUT',0,1
	.word	642
	.byte	6,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVRSDCTRL2_Bits',0,4,222,3,3
	.word	22162
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL3_Bits',0,4,225,3,16,4,11
	.byte	'SDPWMPRE',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'SDPID',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'SDVOKLVL',0,1
	.word	642
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	642
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVRSDCTRL3_Bits',0,4,232,3,3
	.word	22336
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL4_Bits',0,4,235,3,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'SYNCDIV',0,1
	.word	642
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	467
	.byte	20,1,2,35,0,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVRSDCTRL4_Bits',0,4,241,3,3
	.word	22496
	.byte	10
	.byte	'_Ifx_SCU_EVRSTAT_Bits',0,4,244,3,16,4,11
	.byte	'EVR13',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'OV13',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'EVR33',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'OV33',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'OVSWD',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'UV13',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'UV33',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'UVSWD',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'EXTPASS13',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'EXTPASS33',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'BGPROK',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	467
	.byte	21,0,2,35,0,0,31
	.byte	'Ifx_SCU_EVRSTAT_Bits',0,4,130,4,3
	.word	22640
	.byte	10
	.byte	'_Ifx_SCU_EVRTRIM_Bits',0,4,133,4,16,4,11
	.byte	'EVR13TRIM',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'SDVOUTSEL',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	659
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVRTRIM_Bits',0,4,139,4,3
	.word	22914
	.byte	10
	.byte	'_Ifx_SCU_EVRUVMON_Bits',0,4,142,4,16,4,11
	.byte	'EVR13UVVAL',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'EVR33UVVAL',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'SWDUVVAL',0,1
	.word	642
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	642
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVRUVMON_Bits',0,4,149,4,3
	.word	23053
	.byte	10
	.byte	'_Ifx_SCU_EXTCON_Bits',0,4,152,4,16,4,11
	.byte	'EN0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'SEL0',0,1
	.word	642
	.byte	4,2,2,35,0,11
	.byte	'reserved_6',0,2
	.word	659
	.byte	10,0,2,35,0,11
	.byte	'EN1',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'NSEL',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'SEL1',0,1
	.word	642
	.byte	4,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	642
	.byte	2,0,2,35,2,11
	.byte	'DIV1',0,1
	.word	642
	.byte	8,0,2,35,3,0,31
	.byte	'Ifx_SCU_EXTCON_Bits',0,4,163,4,3
	.word	23216
	.byte	10
	.byte	'_Ifx_SCU_FDR_Bits',0,4,166,4,16,4,11
	.byte	'STEP',0,2
	.word	659
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	642
	.byte	4,2,2,35,1,11
	.byte	'DM',0,1
	.word	642
	.byte	2,0,2,35,1,11
	.byte	'RESULT',0,2
	.word	659
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	642
	.byte	5,1,2,35,3,11
	.byte	'DISCLK',0,1
	.word	642
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_FDR_Bits',0,4,174,4,3
	.word	23434
	.byte	10
	.byte	'_Ifx_SCU_FMR_Bits',0,4,177,4,16,4,11
	.byte	'FS0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'FS1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'FS2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'FS3',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'FS4',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'FS5',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'FS6',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'FS7',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'FC0',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'FC1',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'FC2',0,1
	.word	642
	.byte	1,5,2,35,2,11
	.byte	'FC3',0,1
	.word	642
	.byte	1,4,2,35,2,11
	.byte	'FC4',0,1
	.word	642
	.byte	1,3,2,35,2,11
	.byte	'FC5',0,1
	.word	642
	.byte	1,2,2,35,2,11
	.byte	'FC6',0,1
	.word	642
	.byte	1,1,2,35,2,11
	.byte	'FC7',0,1
	.word	642
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	642
	.byte	8,0,2,35,3,0,31
	.byte	'Ifx_SCU_FMR_Bits',0,4,197,4,3
	.word	23597
	.byte	10
	.byte	'_Ifx_SCU_ID_Bits',0,4,200,4,16,4,11
	.byte	'MODREV',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	659
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_SCU_ID_Bits',0,4,205,4,3
	.word	23933
	.byte	10
	.byte	'_Ifx_SCU_IGCR_Bits',0,4,208,4,16,4,11
	.byte	'IPEN00',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'IPEN01',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'IPEN02',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'IPEN03',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'IPEN04',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'IPEN05',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'IPEN06',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'IPEN07',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	642
	.byte	5,3,2,35,1,11
	.byte	'GEEN0',0,1
	.word	642
	.byte	1,2,2,35,1,11
	.byte	'IGP0',0,1
	.word	642
	.byte	2,0,2,35,1,11
	.byte	'IPEN10',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'IPEN11',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'IPEN12',0,1
	.word	642
	.byte	1,5,2,35,2,11
	.byte	'IPEN13',0,1
	.word	642
	.byte	1,4,2,35,2,11
	.byte	'IPEN14',0,1
	.word	642
	.byte	1,3,2,35,2,11
	.byte	'IPEN15',0,1
	.word	642
	.byte	1,2,2,35,2,11
	.byte	'IPEN16',0,1
	.word	642
	.byte	1,1,2,35,2,11
	.byte	'IPEN17',0,1
	.word	642
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	642
	.byte	5,3,2,35,3,11
	.byte	'GEEN1',0,1
	.word	642
	.byte	1,2,2,35,3,11
	.byte	'IGP1',0,1
	.word	642
	.byte	2,0,2,35,3,0,31
	.byte	'Ifx_SCU_IGCR_Bits',0,4,232,4,3
	.word	24040
	.byte	10
	.byte	'_Ifx_SCU_IN_Bits',0,4,235,4,16,4,11
	.byte	'P0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	467
	.byte	30,0,2,35,0,0,31
	.byte	'Ifx_SCU_IN_Bits',0,4,240,4,3
	.word	24492
	.byte	10
	.byte	'_Ifx_SCU_IOCR_Bits',0,4,243,4,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	4,4,2,35,0,11
	.byte	'PC0',0,1
	.word	642
	.byte	4,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	642
	.byte	4,4,2,35,1,11
	.byte	'PC1',0,1
	.word	642
	.byte	4,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	659
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_SCU_IOCR_Bits',0,4,250,4,3
	.word	24591
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL0_Bits',0,4,253,4,16,4,11
	.byte	'LBISTREQ',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'LBISTREQP',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'PATTERNS',0,2
	.word	659
	.byte	14,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	659
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_SCU_LBISTCTRL0_Bits',0,4,131,5,3
	.word	24741
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL1_Bits',0,4,134,5,16,4,11
	.byte	'SEED',0,4
	.word	467
	.byte	23,9,2,35,0,11
	.byte	'reserved_23',0,1
	.word	642
	.byte	1,0,2,35,2,11
	.byte	'SPLITSH',0,1
	.word	642
	.byte	3,5,2,35,3,11
	.byte	'BODY',0,1
	.word	642
	.byte	1,4,2,35,3,11
	.byte	'LBISTFREQU',0,1
	.word	642
	.byte	4,0,2,35,3,0,31
	.byte	'Ifx_SCU_LBISTCTRL1_Bits',0,4,141,5,3
	.word	24890
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL2_Bits',0,4,144,5,16,4,11
	.byte	'SIGNATURE',0,4
	.word	467
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	642
	.byte	7,1,2,35,3,11
	.byte	'LBISTDONE',0,1
	.word	642
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_LBISTCTRL2_Bits',0,4,149,5,3
	.word	25051
	.byte	10
	.byte	'_Ifx_SCU_LCLCON_Bits',0,4,152,5,16,4,11
	.byte	'reserved_0',0,2
	.word	659
	.byte	16,0,2,35,0,11
	.byte	'LS',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,2
	.word	659
	.byte	14,1,2,35,2,11
	.byte	'LSEN',0,1
	.word	642
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_LCLCON_Bits',0,4,158,5,3
	.word	25181
	.byte	10
	.byte	'_Ifx_SCU_LCLTEST_Bits',0,4,161,5,16,4,11
	.byte	'LCLT0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'LCLT1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	467
	.byte	30,0,2,35,0,0,31
	.byte	'Ifx_SCU_LCLTEST_Bits',0,4,166,5,3
	.word	25313
	.byte	10
	.byte	'_Ifx_SCU_MANID_Bits',0,4,169,5,16,4,11
	.byte	'DEPT',0,1
	.word	642
	.byte	5,3,2,35,0,11
	.byte	'MANUF',0,2
	.word	659
	.byte	11,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	659
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_SCU_MANID_Bits',0,4,174,5,3
	.word	25428
	.byte	10
	.byte	'_Ifx_SCU_OMR_Bits',0,4,177,5,16,4,11
	.byte	'PS0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	659
	.byte	14,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	659
	.byte	14,0,2,35,2,0,31
	.byte	'Ifx_SCU_OMR_Bits',0,4,185,5,3
	.word	25539
	.byte	10
	.byte	'_Ifx_SCU_OSCCON_Bits',0,4,188,5,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'PLLLV',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'OSCRES',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'GAINSEL',0,1
	.word	642
	.byte	2,3,2,35,0,11
	.byte	'MODE',0,1
	.word	642
	.byte	2,1,2,35,0,11
	.byte	'SHBY',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'PLLHV',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'X1D',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'X1DEN',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	642
	.byte	4,0,2,35,1,11
	.byte	'OSCVAL',0,1
	.word	642
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	642
	.byte	2,1,2,35,2,11
	.byte	'APREN',0,1
	.word	642
	.byte	1,0,2,35,2,11
	.byte	'CAP0EN',0,1
	.word	642
	.byte	1,7,2,35,3,11
	.byte	'CAP1EN',0,1
	.word	642
	.byte	1,6,2,35,3,11
	.byte	'CAP2EN',0,1
	.word	642
	.byte	1,5,2,35,3,11
	.byte	'CAP3EN',0,1
	.word	642
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	642
	.byte	4,0,2,35,3,0,31
	.byte	'Ifx_SCU_OSCCON_Bits',0,4,209,5,3
	.word	25697
	.byte	10
	.byte	'_Ifx_SCU_OUT_Bits',0,4,212,5,16,4,11
	.byte	'P0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	467
	.byte	30,0,2,35,0,0,31
	.byte	'Ifx_SCU_OUT_Bits',0,4,217,5,3
	.word	26109
	.byte	10
	.byte	'_Ifx_SCU_OVCCON_Bits',0,4,220,5,16,4,11
	.byte	'CSEL0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'CSEL1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'CSEL2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,2
	.word	659
	.byte	13,0,2,35,0,11
	.byte	'OVSTRT',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'OVSTP',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'DCINVAL',0,1
	.word	642
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	642
	.byte	5,0,2,35,2,11
	.byte	'OVCONF',0,1
	.word	642
	.byte	1,7,2,35,3,11
	.byte	'POVCONF',0,1
	.word	642
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	642
	.byte	6,0,2,35,3,0,31
	.byte	'Ifx_SCU_OVCCON_Bits',0,4,233,5,3
	.word	26210
	.byte	10
	.byte	'_Ifx_SCU_OVCENABLE_Bits',0,4,236,5,16,4,11
	.byte	'OVEN0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'OVEN1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'OVEN2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	467
	.byte	29,0,2,35,0,0,31
	.byte	'Ifx_SCU_OVCENABLE_Bits',0,4,242,5,3
	.word	26477
	.byte	10
	.byte	'_Ifx_SCU_PDISC_Bits',0,4,245,5,16,4,11
	.byte	'PDIS0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	467
	.byte	30,0,2,35,0,0,31
	.byte	'Ifx_SCU_PDISC_Bits',0,4,250,5,3
	.word	26613
	.byte	10
	.byte	'_Ifx_SCU_PDR_Bits',0,4,253,5,16,4,11
	.byte	'PD0',0,1
	.word	642
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	642
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	467
	.byte	24,0,2,35,0,0,31
	.byte	'Ifx_SCU_PDR_Bits',0,4,132,6,3
	.word	26724
	.byte	10
	.byte	'_Ifx_SCU_PDRR_Bits',0,4,135,6,16,4,11
	.byte	'PDR0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'PDR1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'PDR2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'PDR3',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'PDR4',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'PDR5',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'PDR6',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'PDR7',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	467
	.byte	24,0,2,35,0,0,31
	.byte	'Ifx_SCU_PDRR_Bits',0,4,146,6,3
	.word	26857
	.byte	10
	.byte	'_Ifx_SCU_PLLCON0_Bits',0,4,149,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'MODEN',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	659
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	642
	.byte	7,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	642
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	642
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	642
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	642
	.byte	4,0,2,35,3,0,31
	.byte	'Ifx_SCU_PLLCON0_Bits',0,4,166,6,3
	.word	27060
	.byte	10
	.byte	'_Ifx_SCU_PLLCON1_Bits',0,4,169,6,16,4,11
	.byte	'K2DIV',0,1
	.word	642
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	642
	.byte	7,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	642
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	659
	.byte	9,0,2,35,2,0,31
	.byte	'Ifx_SCU_PLLCON1_Bits',0,4,177,6,3
	.word	27416
	.byte	10
	.byte	'_Ifx_SCU_PLLCON2_Bits',0,4,180,6,16,4,11
	.byte	'MODCFG',0,2
	.word	659
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	659
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_SCU_PLLCON2_Bits',0,4,184,6,3
	.word	27594
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON0_Bits',0,4,187,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	642
	.byte	2,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	659
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	642
	.byte	5,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	642
	.byte	2,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	642
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	642
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	642
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	642
	.byte	4,0,2,35,3,0,31
	.byte	'Ifx_SCU_PLLERAYCON0_Bits',0,4,204,6,3
	.word	27694
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON1_Bits',0,4,207,6,16,4,11
	.byte	'K2DIV',0,1
	.word	642
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	642
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	642
	.byte	4,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	642
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	659
	.byte	9,0,2,35,2,0,31
	.byte	'Ifx_SCU_PLLERAYCON1_Bits',0,4,215,6,3
	.word	28064
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYSTAT_Bits',0,4,218,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'PWDSTAT',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	467
	.byte	26,0,2,35,0,0,31
	.byte	'Ifx_SCU_PLLERAYSTAT_Bits',0,4,227,6,3
	.word	28250
	.byte	10
	.byte	'_Ifx_SCU_PLLSTAT_Bits',0,4,230,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'MODRUN',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	467
	.byte	24,0,2,35,0,0,31
	.byte	'Ifx_SCU_PLLSTAT_Bits',0,4,241,6,3
	.word	28448
	.byte	10
	.byte	'_Ifx_SCU_PMCSR_Bits',0,4,244,6,16,4,11
	.byte	'REQSLP',0,1
	.word	642
	.byte	2,6,2,35,0,11
	.byte	'SMUSLP',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	642
	.byte	5,0,2,35,0,11
	.byte	'PMST',0,1
	.word	642
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	467
	.byte	21,0,2,35,0,0,31
	.byte	'Ifx_SCU_PMCSR_Bits',0,4,251,6,3
	.word	28681
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR0_Bits',0,4,254,6,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'ESR1WKEN',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'PINAWKEN',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'PINBWKEN',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'ESR0DFEN',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'ESR0EDCON',0,1
	.word	642
	.byte	2,1,2,35,0,11
	.byte	'ESR1DFEN',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'ESR1EDCON',0,1
	.word	642
	.byte	2,6,2,35,1,11
	.byte	'PINADFEN',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'PINAEDCON',0,1
	.word	642
	.byte	2,3,2,35,1,11
	.byte	'PINBDFEN',0,1
	.word	642
	.byte	1,2,2,35,1,11
	.byte	'PINBEDCON',0,1
	.word	642
	.byte	2,0,2,35,1,11
	.byte	'SCREN',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'STBYRAMSEL',0,1
	.word	642
	.byte	2,5,2,35,2,11
	.byte	'SCRCLKSEL',0,1
	.word	642
	.byte	1,4,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	642
	.byte	1,3,2,35,2,11
	.byte	'TRISTEN',0,1
	.word	642
	.byte	1,2,2,35,2,11
	.byte	'TRISTREQ',0,1
	.word	642
	.byte	1,1,2,35,2,11
	.byte	'PORSTDF',0,1
	.word	642
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	642
	.byte	1,7,2,35,3,11
	.byte	'DCDCSYNC',0,1
	.word	642
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	642
	.byte	3,3,2,35,3,11
	.byte	'ESR0TRIST',0,1
	.word	642
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_PMSWCR0_Bits',0,4,153,7,3
	.word	28833
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR1_Bits',0,4,156,7,16,4,11
	.byte	'SCRSTEN',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'SCRSTREQ',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	642
	.byte	6,0,2,35,0,11
	.byte	'CPUIDLSEL',0,1
	.word	642
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'IRADIS',0,1
	.word	642
	.byte	1,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	642
	.byte	3,0,2,35,1,11
	.byte	'SCRCFG',0,1
	.word	642
	.byte	8,0,2,35,2,11
	.byte	'CPUSEL',0,1
	.word	642
	.byte	3,5,2,35,3,11
	.byte	'STBYEVEN',0,1
	.word	642
	.byte	1,4,2,35,3,11
	.byte	'STBYEV',0,1
	.word	642
	.byte	3,1,2,35,3,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_PMSWCR1_Bits',0,4,170,7,3
	.word	29400
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR2_Bits',0,4,173,7,16,4,11
	.byte	'SCRINT',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'BUSY',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'SCRECC',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'SCRWDT',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'SCRRST',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	642
	.byte	4,0,2,35,1,11
	.byte	'TCINT',0,1
	.word	642
	.byte	8,0,2,35,2,11
	.byte	'TCINTREQ',0,1
	.word	642
	.byte	1,7,2,35,3,11
	.byte	'SMURST',0,1
	.word	642
	.byte	1,6,2,35,3,11
	.byte	'RST',0,1
	.word	642
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	642
	.byte	4,1,2,35,3,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_PMSWCR2_Bits',0,4,187,7,3
	.word	29694
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTAT_Bits',0,4,190,7,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKP',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUN',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'PINAWKP',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUN',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'PINBWKP',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUN',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'PORSTDF',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'HWCFGEVR',0,1
	.word	642
	.byte	3,3,2,35,1,11
	.byte	'STBYRAM',0,1
	.word	642
	.byte	2,1,2,35,1,11
	.byte	'TRIST',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'SCRST',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'SCRWKP',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'SCR',0,1
	.word	642
	.byte	1,5,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	642
	.byte	1,4,2,35,2,11
	.byte	'ESR1WKEN',0,1
	.word	642
	.byte	1,3,2,35,2,11
	.byte	'PINAWKEN',0,1
	.word	642
	.byte	1,2,2,35,2,11
	.byte	'PINBWKEN',0,1
	.word	642
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	659
	.byte	4,5,2,35,2,11
	.byte	'ESR0TRIST',0,1
	.word	642
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	642
	.byte	4,0,2,35,3,0,31
	.byte	'Ifx_SCU_PMSWSTAT_Bits',0,4,214,7,3
	.word	29972
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTATCLR_Bits',0,4,217,7,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKPCLR',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUNCLR',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'PINAWKPCLR',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUNCLR',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'PINBWKPCLR',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUNCLR',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'SCRSTCLR',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'SCRWKPCLR',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	659
	.byte	14,0,2,35,2,0,31
	.byte	'Ifx_SCU_PMSWSTATCLR_Bits',0,4,230,7,3
	.word	30468
	.byte	10
	.byte	'_Ifx_SCU_RSTCON2_Bits',0,4,233,7,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'CLRC',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	659
	.byte	10,4,2,35,0,11
	.byte	'CSS0',0,1
	.word	642
	.byte	1,3,2,35,1,11
	.byte	'CSS1',0,1
	.word	642
	.byte	1,2,2,35,1,11
	.byte	'CSS2',0,1
	.word	642
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'USRINFO',0,2
	.word	659
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_SCU_RSTCON2_Bits',0,4,243,7,3
	.word	30781
	.byte	10
	.byte	'_Ifx_SCU_RSTCON_Bits',0,4,246,7,16,4,11
	.byte	'ESR0',0,1
	.word	642
	.byte	2,6,2,35,0,11
	.byte	'ESR1',0,1
	.word	642
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	642
	.byte	2,2,2,35,0,11
	.byte	'SMU',0,1
	.word	642
	.byte	2,0,2,35,0,11
	.byte	'SW',0,1
	.word	642
	.byte	2,6,2,35,1,11
	.byte	'STM0',0,1
	.word	642
	.byte	2,4,2,35,1,11
	.byte	'STM1',0,1
	.word	642
	.byte	2,2,2,35,1,11
	.byte	'STM2',0,1
	.word	642
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	659
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_SCU_RSTCON_Bits',0,4,129,8,3
	.word	30990
	.byte	10
	.byte	'_Ifx_SCU_RSTSTAT_Bits',0,4,132,8,16,4,11
	.byte	'ESR0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'ESR1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'SMU',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'SW',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'STM0',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'STM1',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'STM2',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'PORST',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'CB0',0,1
	.word	642
	.byte	1,5,2,35,2,11
	.byte	'CB1',0,1
	.word	642
	.byte	1,4,2,35,2,11
	.byte	'CB3',0,1
	.word	642
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	642
	.byte	2,1,2,35,2,11
	.byte	'EVR13',0,1
	.word	642
	.byte	1,0,2,35,2,11
	.byte	'EVR33',0,1
	.word	642
	.byte	1,7,2,35,3,11
	.byte	'SWD',0,1
	.word	642
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	642
	.byte	2,4,2,35,3,11
	.byte	'STBYR',0,1
	.word	642
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	642
	.byte	3,0,2,35,3,0,31
	.byte	'Ifx_SCU_RSTSTAT_Bits',0,4,155,8,3
	.word	31201
	.byte	10
	.byte	'_Ifx_SCU_SAFECON_Bits',0,4,158,8,16,4,11
	.byte	'HBT',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	467
	.byte	31,0,2,35,0,0,31
	.byte	'Ifx_SCU_SAFECON_Bits',0,4,162,8,3
	.word	31633
	.byte	10
	.byte	'_Ifx_SCU_STSTAT_Bits',0,4,165,8,16,4,11
	.byte	'HWCFG',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'FTM',0,1
	.word	642
	.byte	7,1,2,35,1,11
	.byte	'MODE',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'FCBAE',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'LUDIS',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	642
	.byte	1,5,2,35,2,11
	.byte	'TRSTL',0,1
	.word	642
	.byte	1,4,2,35,2,11
	.byte	'SPDEN',0,1
	.word	642
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	642
	.byte	3,0,2,35,2,11
	.byte	'RAMINT',0,1
	.word	642
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	642
	.byte	7,0,2,35,3,0,31
	.byte	'Ifx_SCU_STSTAT_Bits',0,4,178,8,3
	.word	31729
	.byte	10
	.byte	'_Ifx_SCU_SWRSTCON_Bits',0,4,181,8,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'SWRSTREQ',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	467
	.byte	30,0,2,35,0,0,31
	.byte	'Ifx_SCU_SWRSTCON_Bits',0,4,186,8,3
	.word	31989
	.byte	10
	.byte	'_Ifx_SCU_SYSCON_Bits',0,4,189,8,16,4,11
	.byte	'CCTRIG0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'RAMINTM',0,1
	.word	642
	.byte	2,4,2,35,0,11
	.byte	'SETLUDIS',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	642
	.byte	3,0,2,35,0,11
	.byte	'DATM',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,4
	.word	467
	.byte	23,0,2,35,0,0,31
	.byte	'Ifx_SCU_SYSCON_Bits',0,4,198,8,3
	.word	32114
	.byte	10
	.byte	'_Ifx_SCU_TRAPCLR_Bits',0,4,201,8,16,4,11
	.byte	'ESR0T',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	467
	.byte	28,0,2,35,0,0,31
	.byte	'Ifx_SCU_TRAPCLR_Bits',0,4,208,8,3
	.word	32311
	.byte	10
	.byte	'_Ifx_SCU_TRAPDIS_Bits',0,4,211,8,16,4,11
	.byte	'ESR0T',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	467
	.byte	28,0,2,35,0,0,31
	.byte	'Ifx_SCU_TRAPDIS_Bits',0,4,218,8,3
	.word	32464
	.byte	10
	.byte	'_Ifx_SCU_TRAPSET_Bits',0,4,221,8,16,4,11
	.byte	'ESR0T',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	467
	.byte	28,0,2,35,0,0,31
	.byte	'Ifx_SCU_TRAPSET_Bits',0,4,228,8,3
	.word	32617
	.byte	10
	.byte	'_Ifx_SCU_TRAPSTAT_Bits',0,4,231,8,16,4,11
	.byte	'ESR0T',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	467
	.byte	28,0,2,35,0,0,31
	.byte	'Ifx_SCU_TRAPSTAT_Bits',0,4,238,8,3
	.word	32770
	.byte	31
	.byte	'Ifx_SCU_WDTCPU_CON0_Bits',0,4,247,8,3
	.word	506
	.byte	31
	.byte	'Ifx_SCU_WDTCPU_CON1_Bits',0,4,134,9,3
	.word	681
	.byte	31
	.byte	'Ifx_SCU_WDTCPU_SR_Bits',0,4,150,9,3
	.word	925
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON0_Bits',0,4,153,9,16,4,11
	.byte	'ENDINIT',0,4
	.word	490
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	490
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	490
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	490
	.byte	16,0,2,35,0,0,31
	.byte	'Ifx_SCU_WDTS_CON0_Bits',0,4,159,9,3
	.word	33025
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON1_Bits',0,4,162,9,16,4,11
	.byte	'CLRIRF',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'IR0',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	642
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	659
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_SCU_WDTS_CON1_Bits',0,4,175,9,3
	.word	33151
	.byte	10
	.byte	'_Ifx_SCU_WDTS_SR_Bits',0,4,178,9,16,4,11
	.byte	'AE',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	642
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	659
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_SCU_WDTS_SR_Bits',0,4,191,9,3
	.word	33403
	.byte	12,4,199,9,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15435
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_ACCEN0',0,4,204,9,3
	.word	33622
	.byte	12,4,207,9,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15992
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_ACCEN1',0,4,212,9,3
	.word	33686
	.byte	12,4,215,9,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16069
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_ARSTDIS',0,4,220,9,3
	.word	33750
	.byte	12,4,223,9,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16205
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_CCUCON0',0,4,228,9,3
	.word	33815
	.byte	12,4,231,9,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16485
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_CCUCON1',0,4,236,9,3
	.word	33880
	.byte	12,4,239,9,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16723
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_CCUCON2',0,4,244,9,3
	.word	33945
	.byte	12,4,247,9,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16851
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_CCUCON3',0,4,252,9,3
	.word	34010
	.byte	12,4,255,9,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17094
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_CCUCON4',0,4,132,10,3
	.word	34075
	.byte	12,4,135,10,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17329
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_CCUCON5',0,4,140,10,3
	.word	34140
	.byte	12,4,143,10,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17457
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_CCUCON6',0,4,148,10,3
	.word	34205
	.byte	12,4,151,10,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17557
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_CCUCON7',0,4,156,10,3
	.word	34270
	.byte	12,4,159,10,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17657
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_CHIPID',0,4,164,10,3
	.word	34335
	.byte	12,4,167,10,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17865
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_DTSCON',0,4,172,10,3
	.word	34399
	.byte	12,4,175,10,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18030
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_DTSLIM',0,4,180,10,3
	.word	34463
	.byte	12,4,183,10,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18213
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_DTSSTAT',0,4,188,10,3
	.word	34527
	.byte	12,4,191,10,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18367
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EICR',0,4,196,10,3
	.word	34592
	.byte	12,4,199,10,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18731
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EIFR',0,4,204,10,3
	.word	34654
	.byte	12,4,207,10,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18942
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EMSR',0,4,212,10,3
	.word	34716
	.byte	12,4,215,10,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19194
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_ESRCFG',0,4,220,10,3
	.word	34778
	.byte	12,4,223,10,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19312
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_ESROCFG',0,4,228,10,3
	.word	34842
	.byte	12,4,231,10,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19423
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVR13CON',0,4,236,10,3
	.word	34907
	.byte	12,4,239,10,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19586
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVR33CON',0,4,244,10,3
	.word	34973
	.byte	12,4,247,10,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19749
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRADCSTAT',0,4,252,10,3
	.word	35039
	.byte	12,4,255,10,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19907
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRDVSTAT',0,4,132,11,3
	.word	35107
	.byte	12,4,135,11,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20072
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRMONCTRL',0,4,140,11,3
	.word	35174
	.byte	12,4,143,11,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20401
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVROSCCTRL',0,4,148,11,3
	.word	35242
	.byte	12,4,151,11,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20622
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVROVMON',0,4,156,11,3
	.word	35310
	.byte	12,4,159,11,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20785
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRRSTCON',0,4,164,11,3
	.word	35376
	.byte	12,4,167,11,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21057
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRSDCOEFF1',0,4,172,11,3
	.word	35443
	.byte	12,4,175,11,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21210
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRSDCOEFF2',0,4,180,11,3
	.word	35512
	.byte	12,4,183,11,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21366
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRSDCOEFF3',0,4,188,11,3
	.word	35581
	.byte	12,4,191,11,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21528
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRSDCOEFF4',0,4,196,11,3
	.word	35650
	.byte	12,4,199,11,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21671
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRSDCOEFF5',0,4,204,11,3
	.word	35719
	.byte	12,4,207,11,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21836
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRSDCOEFF6',0,4,212,11,3
	.word	35788
	.byte	12,4,215,11,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21981
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRSDCTRL1',0,4,220,11,3
	.word	35857
	.byte	12,4,223,11,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22162
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRSDCTRL2',0,4,228,11,3
	.word	35925
	.byte	12,4,231,11,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22336
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRSDCTRL3',0,4,236,11,3
	.word	35993
	.byte	12,4,239,11,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22496
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRSDCTRL4',0,4,244,11,3
	.word	36061
	.byte	12,4,247,11,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22640
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRSTAT',0,4,252,11,3
	.word	36129
	.byte	12,4,255,11,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22914
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRTRIM',0,4,132,12,3
	.word	36194
	.byte	12,4,135,12,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23053
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRUVMON',0,4,140,12,3
	.word	36259
	.byte	12,4,143,12,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23216
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EXTCON',0,4,148,12,3
	.word	36325
	.byte	12,4,151,12,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23434
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_FDR',0,4,156,12,3
	.word	36389
	.byte	12,4,159,12,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23597
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_FMR',0,4,164,12,3
	.word	36450
	.byte	12,4,167,12,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23933
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_ID',0,4,172,12,3
	.word	36511
	.byte	12,4,175,12,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24040
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_IGCR',0,4,180,12,3
	.word	36571
	.byte	12,4,183,12,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24492
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_IN',0,4,188,12,3
	.word	36633
	.byte	12,4,191,12,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24591
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_IOCR',0,4,196,12,3
	.word	36693
	.byte	12,4,199,12,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24741
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_LBISTCTRL0',0,4,204,12,3
	.word	36755
	.byte	12,4,207,12,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24890
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_LBISTCTRL1',0,4,212,12,3
	.word	36823
	.byte	12,4,215,12,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25051
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_LBISTCTRL2',0,4,220,12,3
	.word	36891
	.byte	12,4,223,12,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25181
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_LCLCON',0,4,228,12,3
	.word	36959
	.byte	12,4,231,12,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25313
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_LCLTEST',0,4,236,12,3
	.word	37023
	.byte	12,4,239,12,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25428
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_MANID',0,4,244,12,3
	.word	37088
	.byte	12,4,247,12,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25539
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_OMR',0,4,252,12,3
	.word	37151
	.byte	12,4,255,12,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25697
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_OSCCON',0,4,132,13,3
	.word	37212
	.byte	12,4,135,13,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26109
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_OUT',0,4,140,13,3
	.word	37276
	.byte	12,4,143,13,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26210
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_OVCCON',0,4,148,13,3
	.word	37337
	.byte	12,4,151,13,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26477
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_OVCENABLE',0,4,156,13,3
	.word	37401
	.byte	12,4,159,13,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26613
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PDISC',0,4,164,13,3
	.word	37468
	.byte	12,4,167,13,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26724
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PDR',0,4,172,13,3
	.word	37531
	.byte	12,4,175,13,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26857
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PDRR',0,4,180,13,3
	.word	37592
	.byte	12,4,183,13,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27060
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PLLCON0',0,4,188,13,3
	.word	37654
	.byte	12,4,191,13,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27416
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PLLCON1',0,4,196,13,3
	.word	37719
	.byte	12,4,199,13,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27594
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PLLCON2',0,4,204,13,3
	.word	37784
	.byte	12,4,207,13,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27694
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PLLERAYCON0',0,4,212,13,3
	.word	37849
	.byte	12,4,215,13,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28064
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PLLERAYCON1',0,4,220,13,3
	.word	37918
	.byte	12,4,223,13,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28250
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PLLERAYSTAT',0,4,228,13,3
	.word	37987
	.byte	12,4,231,13,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28448
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PLLSTAT',0,4,236,13,3
	.word	38056
	.byte	12,4,239,13,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28681
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PMCSR',0,4,244,13,3
	.word	38121
	.byte	12,4,247,13,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28833
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PMSWCR0',0,4,252,13,3
	.word	38184
	.byte	12,4,255,13,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29400
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PMSWCR1',0,4,132,14,3
	.word	38249
	.byte	12,4,135,14,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29694
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PMSWCR2',0,4,140,14,3
	.word	38314
	.byte	12,4,143,14,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29972
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PMSWSTAT',0,4,148,14,3
	.word	38379
	.byte	12,4,151,14,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30468
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PMSWSTATCLR',0,4,156,14,3
	.word	38445
	.byte	12,4,159,14,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30990
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_RSTCON',0,4,164,14,3
	.word	38514
	.byte	12,4,167,14,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30781
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_RSTCON2',0,4,172,14,3
	.word	38578
	.byte	12,4,175,14,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31201
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_RSTSTAT',0,4,180,14,3
	.word	38643
	.byte	12,4,183,14,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31633
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_SAFECON',0,4,188,14,3
	.word	38708
	.byte	12,4,191,14,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31729
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_STSTAT',0,4,196,14,3
	.word	38773
	.byte	12,4,199,14,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31989
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_SWRSTCON',0,4,204,14,3
	.word	38837
	.byte	12,4,207,14,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32114
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_SYSCON',0,4,212,14,3
	.word	38903
	.byte	12,4,215,14,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32311
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_TRAPCLR',0,4,220,14,3
	.word	38967
	.byte	12,4,223,14,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32464
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_TRAPDIS',0,4,228,14,3
	.word	39032
	.byte	12,4,231,14,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32617
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_TRAPSET',0,4,236,14,3
	.word	39097
	.byte	12,4,239,14,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32770
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_TRAPSTAT',0,4,244,14,3
	.word	39162
	.byte	31
	.byte	'Ifx_SCU_WDTCPU_CON0',0,4,252,14,3
	.word	602
	.byte	31
	.byte	'Ifx_SCU_WDTCPU_CON1',0,4,132,15,3
	.word	885
	.byte	31
	.byte	'Ifx_SCU_WDTCPU_SR',0,4,140,15,3
	.word	1116
	.byte	12,4,143,15,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33025
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_WDTS_CON0',0,4,148,15,3
	.word	39313
	.byte	12,4,151,15,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33151
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_WDTS_CON1',0,4,156,15,3
	.word	39380
	.byte	12,4,159,15,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33403
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_WDTS_SR',0,4,164,15,3
	.word	39447
	.byte	14
	.word	1156
	.byte	31
	.byte	'Ifx_SCU_WDTCPU',0,4,180,15,3
	.word	39512
	.byte	10
	.byte	'_Ifx_SCU_WDTS',0,4,183,15,25,12,13
	.byte	'CON0',0
	.word	39313
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	39380
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	39447
	.byte	4,2,35,8,0,14
	.word	39541
	.byte	31
	.byte	'Ifx_SCU_WDTS',0,4,188,15,3
	.word	39602
	.byte	15,8
	.word	34778
	.byte	16,1,0,15,20
	.word	642
	.byte	16,19,0,15,8
	.word	38121
	.byte	16,1,0,14
	.word	39541
	.byte	15,24
	.word	1156
	.byte	16,1,0,14
	.word	39661
	.byte	15,16
	.word	642
	.byte	16,15,0,15,28
	.word	642
	.byte	16,27,0,15,40
	.word	642
	.byte	16,39,0,15,16
	.word	34592
	.byte	16,3,0,15,16
	.word	36571
	.byte	16,3,0,15,180,3
	.word	642
	.byte	16,179,3,0,10
	.byte	'_Ifx_SCU',0,4,201,15,25,128,8,13
	.byte	'reserved_0',0
	.word	4279
	.byte	8,2,35,0,13
	.byte	'ID',0
	.word	36511
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	2460
	.byte	4,2,35,12,13
	.byte	'OSCCON',0
	.word	37212
	.byte	4,2,35,16,13
	.byte	'PLLSTAT',0
	.word	38056
	.byte	4,2,35,20,13
	.byte	'PLLCON0',0
	.word	37654
	.byte	4,2,35,24,13
	.byte	'PLLCON1',0
	.word	37719
	.byte	4,2,35,28,13
	.byte	'PLLCON2',0
	.word	37784
	.byte	4,2,35,32,13
	.byte	'PLLERAYSTAT',0
	.word	37987
	.byte	4,2,35,36,13
	.byte	'PLLERAYCON0',0
	.word	37849
	.byte	4,2,35,40,13
	.byte	'PLLERAYCON1',0
	.word	37918
	.byte	4,2,35,44,13
	.byte	'CCUCON0',0
	.word	33815
	.byte	4,2,35,48,13
	.byte	'CCUCON1',0
	.word	33880
	.byte	4,2,35,52,13
	.byte	'FDR',0
	.word	36389
	.byte	4,2,35,56,13
	.byte	'EXTCON',0
	.word	36325
	.byte	4,2,35,60,13
	.byte	'CCUCON2',0
	.word	33945
	.byte	4,2,35,64,13
	.byte	'CCUCON3',0
	.word	34010
	.byte	4,2,35,68,13
	.byte	'CCUCON4',0
	.word	34075
	.byte	4,2,35,72,13
	.byte	'CCUCON5',0
	.word	34140
	.byte	4,2,35,76,13
	.byte	'RSTSTAT',0
	.word	38643
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	2460
	.byte	4,2,35,84,13
	.byte	'RSTCON',0
	.word	38514
	.byte	4,2,35,88,13
	.byte	'ARSTDIS',0
	.word	33750
	.byte	4,2,35,92,13
	.byte	'SWRSTCON',0
	.word	38837
	.byte	4,2,35,96,13
	.byte	'RSTCON2',0
	.word	38578
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	2460
	.byte	4,2,35,104,13
	.byte	'EVRRSTCON',0
	.word	35376
	.byte	4,2,35,108,13
	.byte	'ESRCFG',0
	.word	39629
	.byte	8,2,35,112,13
	.byte	'ESROCFG',0
	.word	34842
	.byte	4,2,35,120,13
	.byte	'SYSCON',0
	.word	38903
	.byte	4,2,35,124,13
	.byte	'CCUCON6',0
	.word	34205
	.byte	4,3,35,128,1,13
	.byte	'CCUCON7',0
	.word	34270
	.byte	4,3,35,132,1,13
	.byte	'reserved_88',0
	.word	39638
	.byte	20,3,35,136,1,13
	.byte	'PDR',0
	.word	37531
	.byte	4,3,35,156,1,13
	.byte	'IOCR',0
	.word	36693
	.byte	4,3,35,160,1,13
	.byte	'OUT',0
	.word	37276
	.byte	4,3,35,164,1,13
	.byte	'OMR',0
	.word	37151
	.byte	4,3,35,168,1,13
	.byte	'IN',0
	.word	36633
	.byte	4,3,35,172,1,13
	.byte	'EVRSTAT',0
	.word	36129
	.byte	4,3,35,176,1,13
	.byte	'EVRDVSTAT',0
	.word	35107
	.byte	4,3,35,180,1,13
	.byte	'EVR13CON',0
	.word	34907
	.byte	4,3,35,184,1,13
	.byte	'EVR33CON',0
	.word	34973
	.byte	4,3,35,188,1,13
	.byte	'STSTAT',0
	.word	38773
	.byte	4,3,35,192,1,13
	.byte	'reserved_C4',0
	.word	2460
	.byte	4,3,35,196,1,13
	.byte	'PMSWCR0',0
	.word	38184
	.byte	4,3,35,200,1,13
	.byte	'PMSWSTAT',0
	.word	38379
	.byte	4,3,35,204,1,13
	.byte	'PMSWSTATCLR',0
	.word	38445
	.byte	4,3,35,208,1,13
	.byte	'PMCSR',0
	.word	39647
	.byte	8,3,35,212,1,13
	.byte	'reserved_DC',0
	.word	2460
	.byte	4,3,35,220,1,13
	.byte	'DTSSTAT',0
	.word	34527
	.byte	4,3,35,224,1,13
	.byte	'DTSCON',0
	.word	34399
	.byte	4,3,35,228,1,13
	.byte	'PMSWCR1',0
	.word	38249
	.byte	4,3,35,232,1,13
	.byte	'PMSWCR2',0
	.word	38314
	.byte	4,3,35,236,1,13
	.byte	'WDTS',0
	.word	39656
	.byte	12,3,35,240,1,13
	.byte	'EMSR',0
	.word	34716
	.byte	4,3,35,252,1,13
	.byte	'WDTCPU',0
	.word	39670
	.byte	24,3,35,128,2,13
	.byte	'reserved_118',0
	.word	4619
	.byte	12,3,35,152,2,13
	.byte	'TRAPSTAT',0
	.word	39162
	.byte	4,3,35,164,2,13
	.byte	'TRAPSET',0
	.word	39097
	.byte	4,3,35,168,2,13
	.byte	'TRAPCLR',0
	.word	38967
	.byte	4,3,35,172,2,13
	.byte	'TRAPDIS',0
	.word	39032
	.byte	4,3,35,176,2,13
	.byte	'reserved_134',0
	.word	2460
	.byte	4,3,35,180,2,13
	.byte	'LCLCON1',0
	.word	36959
	.byte	4,3,35,184,2,13
	.byte	'LCLTEST',0
	.word	37023
	.byte	4,3,35,188,2,13
	.byte	'CHIPID',0
	.word	34335
	.byte	4,3,35,192,2,13
	.byte	'MANID',0
	.word	37088
	.byte	4,3,35,196,2,13
	.byte	'reserved_148',0
	.word	4279
	.byte	8,3,35,200,2,13
	.byte	'SAFECON',0
	.word	38708
	.byte	4,3,35,208,2,13
	.byte	'reserved_154',0
	.word	39675
	.byte	16,3,35,212,2,13
	.byte	'LBISTCTRL0',0
	.word	36755
	.byte	4,3,35,228,2,13
	.byte	'LBISTCTRL1',0
	.word	36823
	.byte	4,3,35,232,2,13
	.byte	'LBISTCTRL2',0
	.word	36891
	.byte	4,3,35,236,2,13
	.byte	'reserved_170',0
	.word	39684
	.byte	28,3,35,240,2,13
	.byte	'PDISC',0
	.word	37468
	.byte	4,3,35,140,3,13
	.byte	'reserved_190',0
	.word	4279
	.byte	8,3,35,144,3,13
	.byte	'EVRTRIM',0
	.word	36194
	.byte	4,3,35,152,3,13
	.byte	'EVRADCSTAT',0
	.word	35039
	.byte	4,3,35,156,3,13
	.byte	'EVRUVMON',0
	.word	36259
	.byte	4,3,35,160,3,13
	.byte	'EVROVMON',0
	.word	35310
	.byte	4,3,35,164,3,13
	.byte	'EVRMONCTRL',0
	.word	35174
	.byte	4,3,35,168,3,13
	.byte	'reserved_1AC',0
	.word	2460
	.byte	4,3,35,172,3,13
	.byte	'EVRSDCTRL1',0
	.word	35857
	.byte	4,3,35,176,3,13
	.byte	'EVRSDCTRL2',0
	.word	35925
	.byte	4,3,35,180,3,13
	.byte	'EVRSDCTRL3',0
	.word	35993
	.byte	4,3,35,184,3,13
	.byte	'EVRSDCTRL4',0
	.word	36061
	.byte	4,3,35,188,3,13
	.byte	'EVRSDCOEFF1',0
	.word	35443
	.byte	4,3,35,192,3,13
	.byte	'EVRSDCOEFF2',0
	.word	35512
	.byte	4,3,35,196,3,13
	.byte	'EVRSDCOEFF3',0
	.word	35581
	.byte	4,3,35,200,3,13
	.byte	'EVRSDCOEFF4',0
	.word	35650
	.byte	4,3,35,204,3,13
	.byte	'EVRSDCOEFF5',0
	.word	35719
	.byte	4,3,35,208,3,13
	.byte	'EVRSDCOEFF6',0
	.word	35788
	.byte	4,3,35,212,3,13
	.byte	'EVROSCCTRL',0
	.word	35242
	.byte	4,3,35,216,3,13
	.byte	'reserved_1DC',0
	.word	2460
	.byte	4,3,35,220,3,13
	.byte	'OVCENABLE',0
	.word	37401
	.byte	4,3,35,224,3,13
	.byte	'OVCCON',0
	.word	37337
	.byte	4,3,35,228,3,13
	.byte	'reserved_1E8',0
	.word	39693
	.byte	40,3,35,232,3,13
	.byte	'EICR',0
	.word	39702
	.byte	16,3,35,144,4,13
	.byte	'EIFR',0
	.word	34654
	.byte	4,3,35,160,4,13
	.byte	'FMR',0
	.word	36450
	.byte	4,3,35,164,4,13
	.byte	'PDRR',0
	.word	37592
	.byte	4,3,35,168,4,13
	.byte	'IGCR',0
	.word	39711
	.byte	16,3,35,172,4,13
	.byte	'reserved_23C',0
	.word	2460
	.byte	4,3,35,188,4,13
	.byte	'DTSLIM',0
	.word	34463
	.byte	4,3,35,192,4,13
	.byte	'reserved_244',0
	.word	39720
	.byte	180,3,3,35,196,4,13
	.byte	'ACCEN1',0
	.word	33686
	.byte	4,3,35,248,7,13
	.byte	'ACCEN0',0
	.word	33622
	.byte	4,3,35,252,7,0,14
	.word	39731
	.byte	31
	.byte	'Ifx_SCU',0,4,181,16,3
	.word	41721
	.byte	31
	.byte	'boolean',0,19,101,29
	.word	642
	.byte	31
	.byte	'uint8',0,19,105,29
	.word	642
	.byte	31
	.byte	'uint16',0,19,109,29
	.word	659
	.byte	31
	.byte	'uint32',0,19,113,29
	.word	9875
	.byte	31
	.byte	'uint64',0,19,118,29
	.word	349
	.byte	31
	.byte	'sint16',0,19,126,29
	.word	15084
	.byte	7
	.byte	'long int',0,4,5,31
	.byte	'sint32',0,19,131,1,29
	.word	41833
	.byte	7
	.byte	'long long int',0,8,5,31
	.byte	'sint64',0,19,138,1,29
	.word	41861
	.byte	31
	.byte	'float32',0,19,167,1,29
	.word	263
	.byte	31
	.byte	'pvoid',0,20,57,28
	.word	381
	.byte	31
	.byte	'Ifx_TickTime',0,20,79,28
	.word	41861
	.byte	31
	.byte	'Ifx_Priority',0,20,103,16
	.word	659
	.byte	17,20,130,1,9,1,18
	.byte	'Ifx_RxSel_a',0,0,18
	.byte	'Ifx_RxSel_b',0,1,18
	.byte	'Ifx_RxSel_c',0,2,18
	.byte	'Ifx_RxSel_d',0,3,18
	.byte	'Ifx_RxSel_e',0,4,18
	.byte	'Ifx_RxSel_f',0,5,18
	.byte	'Ifx_RxSel_g',0,6,18
	.byte	'Ifx_RxSel_h',0,7,0,31
	.byte	'Ifx_RxSel',0,20,140,1,3
	.word	41967
	.byte	10
	.byte	'_Ifx_CPU_A_Bits',0,16,45,16,4,11
	.byte	'ADDR',0,4
	.word	490
	.byte	32,0,2,35,0,0,31
	.byte	'Ifx_CPU_A_Bits',0,16,48,3
	.word	42105
	.byte	10
	.byte	'_Ifx_CPU_BIV_Bits',0,16,51,16,4,11
	.byte	'VSS',0,4
	.word	490
	.byte	1,31,2,35,0,11
	.byte	'BIV',0,4
	.word	490
	.byte	31,0,2,35,0,0,31
	.byte	'Ifx_CPU_BIV_Bits',0,16,55,3
	.word	42166
	.byte	10
	.byte	'_Ifx_CPU_BTV_Bits',0,16,58,16,4,11
	.byte	'reserved_0',0,4
	.word	490
	.byte	1,31,2,35,0,11
	.byte	'BTV',0,4
	.word	490
	.byte	31,0,2,35,0,0,31
	.byte	'Ifx_CPU_BTV_Bits',0,16,62,3
	.word	42245
	.byte	10
	.byte	'_Ifx_CPU_CCNT_Bits',0,16,65,16,4,11
	.byte	'CountValue',0,4
	.word	490
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	490
	.byte	1,0,2,35,0,0,31
	.byte	'Ifx_CPU_CCNT_Bits',0,16,69,3
	.word	42331
	.byte	10
	.byte	'_Ifx_CPU_CCTRL_Bits',0,16,72,16,4,11
	.byte	'CM',0,4
	.word	490
	.byte	1,31,2,35,0,11
	.byte	'CE',0,4
	.word	490
	.byte	1,30,2,35,0,11
	.byte	'M1',0,4
	.word	490
	.byte	3,27,2,35,0,11
	.byte	'M2',0,4
	.word	490
	.byte	3,24,2,35,0,11
	.byte	'M3',0,4
	.word	490
	.byte	3,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	490
	.byte	21,0,2,35,0,0,31
	.byte	'Ifx_CPU_CCTRL_Bits',0,16,80,3
	.word	42420
	.byte	10
	.byte	'_Ifx_CPU_COMPAT_Bits',0,16,83,16,4,11
	.byte	'reserved_0',0,4
	.word	490
	.byte	3,29,2,35,0,11
	.byte	'RM',0,4
	.word	490
	.byte	1,28,2,35,0,11
	.byte	'SP',0,4
	.word	490
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	490
	.byte	27,0,2,35,0,0,31
	.byte	'Ifx_CPU_COMPAT_Bits',0,16,89,3
	.word	42566
	.byte	31
	.byte	'Ifx_CPU_CORE_ID_Bits',0,16,96,3
	.word	14975
	.byte	10
	.byte	'_Ifx_CPU_CPR_L_Bits',0,16,99,16,4,11
	.byte	'reserved_0',0,4
	.word	490
	.byte	3,29,2,35,0,11
	.byte	'LOWBND',0,4
	.word	490
	.byte	29,0,2,35,0,0,31
	.byte	'Ifx_CPU_CPR_L_Bits',0,16,103,3
	.word	42722
	.byte	10
	.byte	'_Ifx_CPU_CPR_U_Bits',0,16,106,16,4,11
	.byte	'reserved_0',0,4
	.word	490
	.byte	3,29,2,35,0,11
	.byte	'UPPBND',0,4
	.word	490
	.byte	29,0,2,35,0,0,31
	.byte	'Ifx_CPU_CPR_U_Bits',0,16,110,3
	.word	42815
	.byte	10
	.byte	'_Ifx_CPU_CPU_ID_Bits',0,16,113,16,4,11
	.byte	'MODREV',0,4
	.word	490
	.byte	8,24,2,35,0,11
	.byte	'MOD_32B',0,4
	.word	490
	.byte	8,16,2,35,0,11
	.byte	'MOD',0,4
	.word	490
	.byte	16,0,2,35,0,0,31
	.byte	'Ifx_CPU_CPU_ID_Bits',0,16,118,3
	.word	42908
	.byte	10
	.byte	'_Ifx_CPU_CPXE_Bits',0,16,121,16,4,11
	.byte	'XE',0,4
	.word	490
	.byte	8,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	490
	.byte	24,0,2,35,0,0,31
	.byte	'Ifx_CPU_CPXE_Bits',0,16,125,3
	.word	43015
	.byte	10
	.byte	'_Ifx_CPU_CREVT_Bits',0,16,128,1,16,4,11
	.byte	'EVTA',0,4
	.word	490
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	490
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	490
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	490
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	490
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	490
	.byte	24,0,2,35,0,0,31
	.byte	'Ifx_CPU_CREVT_Bits',0,16,136,1,3
	.word	43102
	.byte	10
	.byte	'_Ifx_CPU_CUS_ID_Bits',0,16,139,1,16,4,11
	.byte	'CID',0,4
	.word	490
	.byte	3,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	490
	.byte	29,0,2,35,0,0,31
	.byte	'Ifx_CPU_CUS_ID_Bits',0,16,143,1,3
	.word	43256
	.byte	10
	.byte	'_Ifx_CPU_D_Bits',0,16,146,1,16,4,11
	.byte	'DATA',0,4
	.word	490
	.byte	32,0,2,35,0,0,31
	.byte	'Ifx_CPU_D_Bits',0,16,149,1,3
	.word	43350
	.byte	10
	.byte	'_Ifx_CPU_DATR_Bits',0,16,152,1,16,4,11
	.byte	'reserved_0',0,4
	.word	490
	.byte	3,29,2,35,0,11
	.byte	'SBE',0,4
	.word	490
	.byte	1,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	490
	.byte	5,23,2,35,0,11
	.byte	'CWE',0,4
	.word	490
	.byte	1,22,2,35,0,11
	.byte	'CFE',0,4
	.word	490
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	490
	.byte	3,18,2,35,0,11
	.byte	'SOE',0,4
	.word	490
	.byte	1,17,2,35,0,11
	.byte	'SME',0,4
	.word	490
	.byte	1,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	490
	.byte	16,0,2,35,0,0,31
	.byte	'Ifx_CPU_DATR_Bits',0,16,163,1,3
	.word	43413
	.byte	10
	.byte	'_Ifx_CPU_DBGSR_Bits',0,16,166,1,16,4,11
	.byte	'DE',0,4
	.word	490
	.byte	1,31,2,35,0,11
	.byte	'HALT',0,4
	.word	490
	.byte	2,29,2,35,0,11
	.byte	'SIH',0,4
	.word	490
	.byte	1,28,2,35,0,11
	.byte	'SUSP',0,4
	.word	490
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	490
	.byte	1,26,2,35,0,11
	.byte	'PREVSUSP',0,4
	.word	490
	.byte	1,25,2,35,0,11
	.byte	'PEVT',0,4
	.word	490
	.byte	1,24,2,35,0,11
	.byte	'EVTSRC',0,4
	.word	490
	.byte	5,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	490
	.byte	19,0,2,35,0,0,31
	.byte	'Ifx_CPU_DBGSR_Bits',0,16,177,1,3
	.word	43631
	.byte	10
	.byte	'_Ifx_CPU_DBGTCR_Bits',0,16,180,1,16,4,11
	.byte	'DTA',0,4
	.word	490
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	490
	.byte	31,0,2,35,0,0,31
	.byte	'Ifx_CPU_DBGTCR_Bits',0,16,184,1,3
	.word	43846
	.byte	10
	.byte	'_Ifx_CPU_DCON0_Bits',0,16,187,1,16,4,11
	.byte	'reserved_0',0,4
	.word	490
	.byte	1,31,2,35,0,11
	.byte	'DCBYP',0,4
	.word	490
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	490
	.byte	30,0,2,35,0,0,31
	.byte	'Ifx_CPU_DCON0_Bits',0,16,192,1,3
	.word	43940
	.byte	10
	.byte	'_Ifx_CPU_DCON2_Bits',0,16,195,1,16,4,11
	.byte	'DCACHE_SZE',0,4
	.word	490
	.byte	16,16,2,35,0,11
	.byte	'DSCRATCH_SZE',0,4
	.word	490
	.byte	16,0,2,35,0,0,31
	.byte	'Ifx_CPU_DCON2_Bits',0,16,199,1,3
	.word	44056
	.byte	10
	.byte	'_Ifx_CPU_DCX_Bits',0,16,202,1,16,4,11
	.byte	'reserved_0',0,4
	.word	490
	.byte	6,26,2,35,0,11
	.byte	'DCXValue',0,4
	.word	490
	.byte	26,0,2,35,0,0,31
	.byte	'Ifx_CPU_DCX_Bits',0,16,206,1,3
	.word	44157
	.byte	10
	.byte	'_Ifx_CPU_DEADD_Bits',0,16,209,1,16,4,11
	.byte	'ERROR_ADDRESS',0,4
	.word	490
	.byte	32,0,2,35,0,0,31
	.byte	'Ifx_CPU_DEADD_Bits',0,16,212,1,3
	.word	44250
	.byte	10
	.byte	'_Ifx_CPU_DIEAR_Bits',0,16,215,1,16,4,11
	.byte	'TA',0,4
	.word	490
	.byte	32,0,2,35,0,0,31
	.byte	'Ifx_CPU_DIEAR_Bits',0,16,218,1,3
	.word	44330
	.byte	10
	.byte	'_Ifx_CPU_DIETR_Bits',0,16,221,1,16,4,11
	.byte	'IED',0,4
	.word	490
	.byte	1,31,2,35,0,11
	.byte	'IE_T',0,4
	.word	490
	.byte	1,30,2,35,0,11
	.byte	'IE_C',0,4
	.word	490
	.byte	1,29,2,35,0,11
	.byte	'IE_S',0,4
	.word	490
	.byte	1,28,2,35,0,11
	.byte	'IE_BI',0,4
	.word	490
	.byte	1,27,2,35,0,11
	.byte	'E_INFO',0,4
	.word	490
	.byte	6,21,2,35,0,11
	.byte	'IE_DUAL',0,4
	.word	490
	.byte	1,20,2,35,0,11
	.byte	'IE_SP',0,4
	.word	490
	.byte	1,19,2,35,0,11
	.byte	'IE_BS',0,4
	.word	490
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	490
	.byte	18,0,2,35,0,0,31
	.byte	'Ifx_CPU_DIETR_Bits',0,16,233,1,3
	.word	44399
	.byte	10
	.byte	'_Ifx_CPU_DMS_Bits',0,16,236,1,16,4,11
	.byte	'reserved_0',0,4
	.word	490
	.byte	1,31,2,35,0,11
	.byte	'DMSValue',0,4
	.word	490
	.byte	31,0,2,35,0,0,31
	.byte	'Ifx_CPU_DMS_Bits',0,16,240,1,3
	.word	44628
	.byte	10
	.byte	'_Ifx_CPU_DPR_L_Bits',0,16,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	490
	.byte	3,29,2,35,0,11
	.byte	'LOWBND',0,4
	.word	490
	.byte	29,0,2,35,0,0,31
	.byte	'Ifx_CPU_DPR_L_Bits',0,16,247,1,3
	.word	44721
	.byte	10
	.byte	'_Ifx_CPU_DPR_U_Bits',0,16,250,1,16,4,11
	.byte	'reserved_0',0,4
	.word	490
	.byte	3,29,2,35,0,11
	.byte	'UPPBND',0,4
	.word	490
	.byte	29,0,2,35,0,0,31
	.byte	'Ifx_CPU_DPR_U_Bits',0,16,254,1,3
	.word	44816
	.byte	10
	.byte	'_Ifx_CPU_DPRE_Bits',0,16,129,2,16,4,11
	.byte	'RE',0,4
	.word	490
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	490
	.byte	16,0,2,35,0,0,31
	.byte	'Ifx_CPU_DPRE_Bits',0,16,133,2,3
	.word	44911
	.byte	10
	.byte	'_Ifx_CPU_DPWE_Bits',0,16,136,2,16,4,11
	.byte	'WE',0,4
	.word	490
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	490
	.byte	16,0,2,35,0,0,31
	.byte	'Ifx_CPU_DPWE_Bits',0,16,140,2,3
	.word	45001
	.byte	10
	.byte	'_Ifx_CPU_DSTR_Bits',0,16,143,2,16,4,11
	.byte	'SRE',0,4
	.word	490
	.byte	1,31,2,35,0,11
	.byte	'GAE',0,4
	.word	490
	.byte	1,30,2,35,0,11
	.byte	'LBE',0,4
	.word	490
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	490
	.byte	3,26,2,35,0,11
	.byte	'CRE',0,4
	.word	490
	.byte	1,25,2,35,0,11
	.byte	'reserved_7',0,4
	.word	490
	.byte	7,18,2,35,0,11
	.byte	'DTME',0,4
	.word	490
	.byte	1,17,2,35,0,11
	.byte	'LOE',0,4
	.word	490
	.byte	1,16,2,35,0,11
	.byte	'SDE',0,4
	.word	490
	.byte	1,15,2,35,0,11
	.byte	'SCE',0,4
	.word	490
	.byte	1,14,2,35,0,11
	.byte	'CAC',0,4
	.word	490
	.byte	1,13,2,35,0,11
	.byte	'MPE',0,4
	.word	490
	.byte	1,12,2,35,0,11
	.byte	'CLE',0,4
	.word	490
	.byte	1,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	490
	.byte	3,8,2,35,0,11
	.byte	'ALN',0,4
	.word	490
	.byte	1,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	490
	.byte	7,0,2,35,0,0,31
	.byte	'Ifx_CPU_DSTR_Bits',0,16,161,2,3
	.word	45091
	.byte	10
	.byte	'_Ifx_CPU_EXEVT_Bits',0,16,164,2,16,4,11
	.byte	'EVTA',0,4
	.word	490
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	490
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	490
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	490
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	490
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	490
	.byte	24,0,2,35,0,0,31
	.byte	'Ifx_CPU_EXEVT_Bits',0,16,172,2,3
	.word	45415
	.byte	10
	.byte	'_Ifx_CPU_FCX_Bits',0,16,175,2,16,4,11
	.byte	'FCXO',0,4
	.word	490
	.byte	16,16,2,35,0,11
	.byte	'FCXS',0,4
	.word	490
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	490
	.byte	12,0,2,35,0,0,31
	.byte	'Ifx_CPU_FCX_Bits',0,16,180,2,3
	.word	45569
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_CON_Bits',0,16,183,2,16,4,11
	.byte	'TST',0,4
	.word	490
	.byte	1,31,2,35,0,11
	.byte	'TCL',0,4
	.word	490
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	490
	.byte	6,24,2,35,0,11
	.byte	'RM',0,4
	.word	490
	.byte	2,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	490
	.byte	8,14,2,35,0,11
	.byte	'FXE',0,4
	.word	490
	.byte	1,13,2,35,0,11
	.byte	'FUE',0,4
	.word	490
	.byte	1,12,2,35,0,11
	.byte	'FZE',0,4
	.word	490
	.byte	1,11,2,35,0,11
	.byte	'FVE',0,4
	.word	490
	.byte	1,10,2,35,0,11
	.byte	'FIE',0,4
	.word	490
	.byte	1,9,2,35,0,11
	.byte	'reserved_23',0,4
	.word	490
	.byte	3,6,2,35,0,11
	.byte	'FX',0,4
	.word	490
	.byte	1,5,2,35,0,11
	.byte	'FU',0,4
	.word	490
	.byte	1,4,2,35,0,11
	.byte	'FZ',0,4
	.word	490
	.byte	1,3,2,35,0,11
	.byte	'FV',0,4
	.word	490
	.byte	1,2,2,35,0,11
	.byte	'FI',0,4
	.word	490
	.byte	1,1,2,35,0,11
	.byte	'reserved_31',0,4
	.word	490
	.byte	1,0,2,35,0,0,31
	.byte	'Ifx_CPU_FPU_TRAP_CON_Bits',0,16,202,2,3
	.word	45675
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_OPC_Bits',0,16,205,2,16,4,11
	.byte	'OPC',0,4
	.word	490
	.byte	8,24,2,35,0,11
	.byte	'FMT',0,4
	.word	490
	.byte	1,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	490
	.byte	7,16,2,35,0,11
	.byte	'DREG',0,4
	.word	490
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	490
	.byte	12,0,2,35,0,0,31
	.byte	'Ifx_CPU_FPU_TRAP_OPC_Bits',0,16,212,2,3
	.word	46024
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_PC_Bits',0,16,215,2,16,4,11
	.byte	'PC',0,4
	.word	490
	.byte	32,0,2,35,0,0,31
	.byte	'Ifx_CPU_FPU_TRAP_PC_Bits',0,16,218,2,3
	.word	46184
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC1_Bits',0,16,221,2,16,4,11
	.byte	'SRC1',0,4
	.word	490
	.byte	32,0,2,35,0,0,31
	.byte	'Ifx_CPU_FPU_TRAP_SRC1_Bits',0,16,224,2,3
	.word	46265
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC2_Bits',0,16,227,2,16,4,11
	.byte	'SRC2',0,4
	.word	490
	.byte	32,0,2,35,0,0,31
	.byte	'Ifx_CPU_FPU_TRAP_SRC2_Bits',0,16,230,2,3
	.word	46352
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC3_Bits',0,16,233,2,16,4,11
	.byte	'SRC3',0,4
	.word	490
	.byte	32,0,2,35,0,0,31
	.byte	'Ifx_CPU_FPU_TRAP_SRC3_Bits',0,16,236,2,3
	.word	46439
	.byte	10
	.byte	'_Ifx_CPU_ICNT_Bits',0,16,239,2,16,4,11
	.byte	'CountValue',0,4
	.word	490
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	490
	.byte	1,0,2,35,0,0,31
	.byte	'Ifx_CPU_ICNT_Bits',0,16,243,2,3
	.word	46526
	.byte	10
	.byte	'_Ifx_CPU_ICR_Bits',0,16,246,2,16,4,11
	.byte	'CCPN',0,4
	.word	490
	.byte	10,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	490
	.byte	5,17,2,35,0,11
	.byte	'IE',0,4
	.word	490
	.byte	1,16,2,35,0,11
	.byte	'PIPN',0,4
	.word	490
	.byte	10,6,2,35,0,11
	.byte	'reserved_26',0,4
	.word	490
	.byte	6,0,2,35,0,0,31
	.byte	'Ifx_CPU_ICR_Bits',0,16,253,2,3
	.word	46617
	.byte	10
	.byte	'_Ifx_CPU_ISP_Bits',0,16,128,3,16,4,11
	.byte	'ISP',0,4
	.word	490
	.byte	32,0,2,35,0,0,31
	.byte	'Ifx_CPU_ISP_Bits',0,16,131,3,3
	.word	46760
	.byte	10
	.byte	'_Ifx_CPU_LCX_Bits',0,16,134,3,16,4,11
	.byte	'LCXO',0,4
	.word	490
	.byte	16,16,2,35,0,11
	.byte	'LCXS',0,4
	.word	490
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	490
	.byte	12,0,2,35,0,0,31
	.byte	'Ifx_CPU_LCX_Bits',0,16,139,3,3
	.word	46826
	.byte	10
	.byte	'_Ifx_CPU_M1CNT_Bits',0,16,142,3,16,4,11
	.byte	'CountValue',0,4
	.word	490
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	490
	.byte	1,0,2,35,0,0,31
	.byte	'Ifx_CPU_M1CNT_Bits',0,16,146,3,3
	.word	46932
	.byte	10
	.byte	'_Ifx_CPU_M2CNT_Bits',0,16,149,3,16,4,11
	.byte	'CountValue',0,4
	.word	490
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	490
	.byte	1,0,2,35,0,0,31
	.byte	'Ifx_CPU_M2CNT_Bits',0,16,153,3,3
	.word	47025
	.byte	10
	.byte	'_Ifx_CPU_M3CNT_Bits',0,16,156,3,16,4,11
	.byte	'CountValue',0,4
	.word	490
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	490
	.byte	1,0,2,35,0,0,31
	.byte	'Ifx_CPU_M3CNT_Bits',0,16,160,3,3
	.word	47118
	.byte	10
	.byte	'_Ifx_CPU_PC_Bits',0,16,163,3,16,4,11
	.byte	'reserved_0',0,4
	.word	490
	.byte	1,31,2,35,0,11
	.byte	'PC',0,4
	.word	490
	.byte	31,0,2,35,0,0,31
	.byte	'Ifx_CPU_PC_Bits',0,16,167,3,3
	.word	47211
	.byte	10
	.byte	'_Ifx_CPU_PCON0_Bits',0,16,170,3,16,4,11
	.byte	'reserved_0',0,4
	.word	490
	.byte	1,31,2,35,0,11
	.byte	'PCBYP',0,4
	.word	490
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	490
	.byte	30,0,2,35,0,0,31
	.byte	'Ifx_CPU_PCON0_Bits',0,16,175,3,3
	.word	47296
	.byte	10
	.byte	'_Ifx_CPU_PCON1_Bits',0,16,178,3,16,4,11
	.byte	'PCINV',0,4
	.word	490
	.byte	1,31,2,35,0,11
	.byte	'PBINV',0,4
	.word	490
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	490
	.byte	30,0,2,35,0,0,31
	.byte	'Ifx_CPU_PCON1_Bits',0,16,183,3,3
	.word	47412
	.byte	10
	.byte	'_Ifx_CPU_PCON2_Bits',0,16,186,3,16,4,11
	.byte	'PCACHE_SZE',0,4
	.word	490
	.byte	16,16,2,35,0,11
	.byte	'PSCRATCH_SZE',0,4
	.word	490
	.byte	16,0,2,35,0,0,31
	.byte	'Ifx_CPU_PCON2_Bits',0,16,190,3,3
	.word	47523
	.byte	10
	.byte	'_Ifx_CPU_PCXI_Bits',0,16,193,3,16,4,11
	.byte	'PCXO',0,4
	.word	490
	.byte	16,16,2,35,0,11
	.byte	'PCXS',0,4
	.word	490
	.byte	4,12,2,35,0,11
	.byte	'UL',0,4
	.word	490
	.byte	1,11,2,35,0,11
	.byte	'PIE',0,4
	.word	490
	.byte	1,10,2,35,0,11
	.byte	'PCPN',0,4
	.word	490
	.byte	10,0,2,35,0,0,31
	.byte	'Ifx_CPU_PCXI_Bits',0,16,200,3,3
	.word	47624
	.byte	10
	.byte	'_Ifx_CPU_PIEAR_Bits',0,16,203,3,16,4,11
	.byte	'TA',0,4
	.word	490
	.byte	32,0,2,35,0,0,31
	.byte	'Ifx_CPU_PIEAR_Bits',0,16,206,3,3
	.word	47754
	.byte	10
	.byte	'_Ifx_CPU_PIETR_Bits',0,16,209,3,16,4,11
	.byte	'IED',0,4
	.word	490
	.byte	1,31,2,35,0,11
	.byte	'IE_T',0,4
	.word	490
	.byte	1,30,2,35,0,11
	.byte	'IE_C',0,4
	.word	490
	.byte	1,29,2,35,0,11
	.byte	'IE_S',0,4
	.word	490
	.byte	1,28,2,35,0,11
	.byte	'IE_BI',0,4
	.word	490
	.byte	1,27,2,35,0,11
	.byte	'E_INFO',0,4
	.word	490
	.byte	6,21,2,35,0,11
	.byte	'IE_DUAL',0,4
	.word	490
	.byte	1,20,2,35,0,11
	.byte	'IE_SP',0,4
	.word	490
	.byte	1,19,2,35,0,11
	.byte	'IE_BS',0,4
	.word	490
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	490
	.byte	18,0,2,35,0,0,31
	.byte	'Ifx_CPU_PIETR_Bits',0,16,221,3,3
	.word	47823
	.byte	10
	.byte	'_Ifx_CPU_PMA0_Bits',0,16,224,3,16,4,11
	.byte	'reserved_0',0,4
	.word	490
	.byte	13,19,2,35,0,11
	.byte	'DAC',0,4
	.word	490
	.byte	3,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	490
	.byte	16,0,2,35,0,0,31
	.byte	'Ifx_CPU_PMA0_Bits',0,16,229,3,3
	.word	48052
	.byte	10
	.byte	'_Ifx_CPU_PMA1_Bits',0,16,232,3,16,4,11
	.byte	'reserved_0',0,4
	.word	490
	.byte	14,18,2,35,0,11
	.byte	'CAC',0,4
	.word	490
	.byte	2,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	490
	.byte	16,0,2,35,0,0,31
	.byte	'Ifx_CPU_PMA1_Bits',0,16,237,3,3
	.word	48165
	.byte	10
	.byte	'_Ifx_CPU_PMA2_Bits',0,16,240,3,16,4,11
	.byte	'PSI',0,4
	.word	490
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	490
	.byte	16,0,2,35,0,0,31
	.byte	'Ifx_CPU_PMA2_Bits',0,16,244,3,3
	.word	48278
	.byte	10
	.byte	'_Ifx_CPU_PSTR_Bits',0,16,247,3,16,4,11
	.byte	'FRE',0,4
	.word	490
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	490
	.byte	1,30,2,35,0,11
	.byte	'FBE',0,4
	.word	490
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	490
	.byte	9,20,2,35,0,11
	.byte	'FPE',0,4
	.word	490
	.byte	1,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	490
	.byte	1,18,2,35,0,11
	.byte	'FME',0,4
	.word	490
	.byte	1,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	490
	.byte	17,0,2,35,0,0,31
	.byte	'Ifx_CPU_PSTR_Bits',0,16,129,4,3
	.word	48369
	.byte	10
	.byte	'_Ifx_CPU_PSW_Bits',0,16,132,4,16,4,11
	.byte	'CDC',0,4
	.word	490
	.byte	7,25,2,35,0,11
	.byte	'CDE',0,4
	.word	490
	.byte	1,24,2,35,0,11
	.byte	'GW',0,4
	.word	490
	.byte	1,23,2,35,0,11
	.byte	'IS',0,4
	.word	490
	.byte	1,22,2,35,0,11
	.byte	'IO',0,4
	.word	490
	.byte	2,20,2,35,0,11
	.byte	'PRS',0,4
	.word	490
	.byte	2,18,2,35,0,11
	.byte	'S',0,4
	.word	490
	.byte	1,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	490
	.byte	12,5,2,35,0,11
	.byte	'SAV',0,4
	.word	490
	.byte	1,4,2,35,0,11
	.byte	'AV',0,4
	.word	490
	.byte	1,3,2,35,0,11
	.byte	'SV',0,4
	.word	490
	.byte	1,2,2,35,0,11
	.byte	'V',0,4
	.word	490
	.byte	1,1,2,35,0,11
	.byte	'C',0,4
	.word	490
	.byte	1,0,2,35,0,0,31
	.byte	'Ifx_CPU_PSW_Bits',0,16,147,4,3
	.word	48572
	.byte	10
	.byte	'_Ifx_CPU_SEGEN_Bits',0,16,150,4,16,4,11
	.byte	'ADFLIP',0,4
	.word	490
	.byte	8,24,2,35,0,11
	.byte	'ADTYPE',0,4
	.word	490
	.byte	2,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	490
	.byte	21,1,2,35,0,11
	.byte	'AE',0,4
	.word	490
	.byte	1,0,2,35,0,0,31
	.byte	'Ifx_CPU_SEGEN_Bits',0,16,156,4,3
	.word	48815
	.byte	10
	.byte	'_Ifx_CPU_SMACON_Bits',0,16,159,4,16,4,11
	.byte	'PC',0,4
	.word	490
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	490
	.byte	1,30,2,35,0,11
	.byte	'PT',0,4
	.word	490
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	490
	.byte	5,24,2,35,0,11
	.byte	'DC',0,4
	.word	490
	.byte	1,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	490
	.byte	1,22,2,35,0,11
	.byte	'DT',0,4
	.word	490
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	490
	.byte	13,8,2,35,0,11
	.byte	'IODT',0,4
	.word	490
	.byte	1,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	490
	.byte	7,0,2,35,0,0,31
	.byte	'Ifx_CPU_SMACON_Bits',0,16,171,4,3
	.word	48943
	.byte	10
	.byte	'_Ifx_CPU_SPROT_ACCENA_Bits',0,16,174,4,16,4,11
	.byte	'EN',0,4
	.word	467
	.byte	32,0,2,35,0,0,31
	.byte	'Ifx_CPU_SPROT_ACCENA_Bits',0,16,177,4,3
	.word	49184
	.byte	10
	.byte	'_Ifx_CPU_SPROT_ACCENB_Bits',0,16,180,4,16,4,11
	.byte	'reserved_0',0,4
	.word	467
	.byte	32,0,2,35,0,0,31
	.byte	'Ifx_CPU_SPROT_ACCENB_Bits',0,16,183,4,3
	.word	49267
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,16,186,4,16,4,11
	.byte	'EN',0,4
	.word	467
	.byte	32,0,2,35,0,0,31
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,16,189,4,3
	.word	49358
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,16,192,4,16,4,11
	.byte	'reserved_0',0,4
	.word	467
	.byte	32,0,2,35,0,0,31
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,16,195,4,3
	.word	49449
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_LA_Bits',0,16,198,4,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	467
	.byte	27,0,2,35,0,0,31
	.byte	'Ifx_CPU_SPROT_RGN_LA_Bits',0,16,202,4,3
	.word	49548
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_UA_Bits',0,16,205,4,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	467
	.byte	27,0,2,35,0,0,31
	.byte	'Ifx_CPU_SPROT_RGN_UA_Bits',0,16,209,4,3
	.word	49655
	.byte	10
	.byte	'_Ifx_CPU_SWEVT_Bits',0,16,212,4,16,4,11
	.byte	'EVTA',0,4
	.word	490
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	490
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	490
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	490
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	490
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	490
	.byte	24,0,2,35,0,0,31
	.byte	'Ifx_CPU_SWEVT_Bits',0,16,220,4,3
	.word	49762
	.byte	10
	.byte	'_Ifx_CPU_SYSCON_Bits',0,16,223,4,16,4,11
	.byte	'FCDSF',0,4
	.word	490
	.byte	1,31,2,35,0,11
	.byte	'PROTEN',0,4
	.word	490
	.byte	1,30,2,35,0,11
	.byte	'TPROTEN',0,4
	.word	490
	.byte	1,29,2,35,0,11
	.byte	'IS',0,4
	.word	490
	.byte	1,28,2,35,0,11
	.byte	'IT',0,4
	.word	490
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	490
	.byte	27,0,2,35,0,0,31
	.byte	'Ifx_CPU_SYSCON_Bits',0,16,231,4,3
	.word	49916
	.byte	10
	.byte	'_Ifx_CPU_TASK_ASI_Bits',0,16,234,4,16,4,11
	.byte	'ASI',0,4
	.word	490
	.byte	5,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	490
	.byte	27,0,2,35,0,0,31
	.byte	'Ifx_CPU_TASK_ASI_Bits',0,16,238,4,3
	.word	50077
	.byte	10
	.byte	'_Ifx_CPU_TPS_CON_Bits',0,16,241,4,16,4,11
	.byte	'TEXP0',0,4
	.word	490
	.byte	1,31,2,35,0,11
	.byte	'TEXP1',0,4
	.word	490
	.byte	1,30,2,35,0,11
	.byte	'TEXP2',0,4
	.word	490
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	490
	.byte	13,16,2,35,0,11
	.byte	'TTRAP',0,4
	.word	490
	.byte	1,15,2,35,0,11
	.byte	'reserved_17',0,4
	.word	490
	.byte	15,0,2,35,0,0,31
	.byte	'Ifx_CPU_TPS_CON_Bits',0,16,249,4,3
	.word	50175
	.byte	10
	.byte	'_Ifx_CPU_TPS_TIMER_Bits',0,16,252,4,16,4,11
	.byte	'Timer',0,4
	.word	490
	.byte	32,0,2,35,0,0,31
	.byte	'Ifx_CPU_TPS_TIMER_Bits',0,16,255,4,3
	.word	50347
	.byte	10
	.byte	'_Ifx_CPU_TR_ADR_Bits',0,16,130,5,16,4,11
	.byte	'ADDR',0,4
	.word	490
	.byte	32,0,2,35,0,0,31
	.byte	'Ifx_CPU_TR_ADR_Bits',0,16,133,5,3
	.word	50427
	.byte	10
	.byte	'_Ifx_CPU_TR_EVT_Bits',0,16,136,5,16,4,11
	.byte	'EVTA',0,4
	.word	490
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	490
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	490
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	490
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	490
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	490
	.byte	4,20,2,35,0,11
	.byte	'TYP',0,4
	.word	490
	.byte	1,19,2,35,0,11
	.byte	'RNG',0,4
	.word	490
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	490
	.byte	1,17,2,35,0,11
	.byte	'ASI_EN',0,4
	.word	490
	.byte	1,16,2,35,0,11
	.byte	'ASI',0,4
	.word	490
	.byte	5,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	490
	.byte	6,5,2,35,0,11
	.byte	'AST',0,4
	.word	490
	.byte	1,4,2,35,0,11
	.byte	'ALD',0,4
	.word	490
	.byte	1,3,2,35,0,11
	.byte	'reserved_29',0,4
	.word	490
	.byte	3,0,2,35,0,0,31
	.byte	'Ifx_CPU_TR_EVT_Bits',0,16,153,5,3
	.word	50500
	.byte	10
	.byte	'_Ifx_CPU_TRIG_ACC_Bits',0,16,156,5,16,4,11
	.byte	'T0',0,4
	.word	490
	.byte	1,31,2,35,0,11
	.byte	'T1',0,4
	.word	490
	.byte	1,30,2,35,0,11
	.byte	'T2',0,4
	.word	490
	.byte	1,29,2,35,0,11
	.byte	'T3',0,4
	.word	490
	.byte	1,28,2,35,0,11
	.byte	'T4',0,4
	.word	490
	.byte	1,27,2,35,0,11
	.byte	'T5',0,4
	.word	490
	.byte	1,26,2,35,0,11
	.byte	'T6',0,4
	.word	490
	.byte	1,25,2,35,0,11
	.byte	'T7',0,4
	.word	490
	.byte	1,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	490
	.byte	24,0,2,35,0,0,31
	.byte	'Ifx_CPU_TRIG_ACC_Bits',0,16,167,5,3
	.word	50818
	.byte	12,16,175,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42105
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_A',0,16,180,5,3
	.word	51013
	.byte	12,16,183,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42166
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_BIV',0,16,188,5,3
	.word	51072
	.byte	12,16,191,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42245
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_BTV',0,16,196,5,3
	.word	51133
	.byte	12,16,199,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42331
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_CCNT',0,16,204,5,3
	.word	51194
	.byte	12,16,207,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42420
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_CCTRL',0,16,212,5,3
	.word	51256
	.byte	12,16,215,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42566
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_COMPAT',0,16,220,5,3
	.word	51319
	.byte	31
	.byte	'Ifx_CPU_CORE_ID',0,16,228,5,3
	.word	15044
	.byte	12,16,231,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42722
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_CPR_L',0,16,236,5,3
	.word	51408
	.byte	12,16,239,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42815
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_CPR_U',0,16,244,5,3
	.word	51471
	.byte	12,16,247,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42908
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_CPU_ID',0,16,252,5,3
	.word	51534
	.byte	12,16,255,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43015
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_CPXE',0,16,132,6,3
	.word	51598
	.byte	12,16,135,6,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43102
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_CREVT',0,16,140,6,3
	.word	51660
	.byte	12,16,143,6,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43256
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_CUS_ID',0,16,148,6,3
	.word	51723
	.byte	12,16,151,6,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43350
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_D',0,16,156,6,3
	.word	51787
	.byte	12,16,159,6,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43413
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_DATR',0,16,164,6,3
	.word	51846
	.byte	12,16,167,6,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43631
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_DBGSR',0,16,172,6,3
	.word	51908
	.byte	12,16,175,6,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43846
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_DBGTCR',0,16,180,6,3
	.word	51971
	.byte	12,16,183,6,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43940
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_DCON0',0,16,188,6,3
	.word	52035
	.byte	12,16,191,6,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44056
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_DCON2',0,16,196,6,3
	.word	52098
	.byte	12,16,199,6,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44157
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_DCX',0,16,204,6,3
	.word	52161
	.byte	12,16,207,6,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44250
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_DEADD',0,16,212,6,3
	.word	52222
	.byte	12,16,215,6,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44330
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_DIEAR',0,16,220,6,3
	.word	52285
	.byte	12,16,223,6,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44399
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_DIETR',0,16,228,6,3
	.word	52348
	.byte	12,16,231,6,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44628
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_DMS',0,16,236,6,3
	.word	52411
	.byte	12,16,239,6,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44721
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_DPR_L',0,16,244,6,3
	.word	52472
	.byte	12,16,247,6,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44816
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_DPR_U',0,16,252,6,3
	.word	52535
	.byte	12,16,255,6,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44911
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_DPRE',0,16,132,7,3
	.word	52598
	.byte	12,16,135,7,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45001
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_DPWE',0,16,140,7,3
	.word	52660
	.byte	12,16,143,7,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45091
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_DSTR',0,16,148,7,3
	.word	52722
	.byte	12,16,151,7,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45415
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_EXEVT',0,16,156,7,3
	.word	52784
	.byte	12,16,159,7,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45569
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_FCX',0,16,164,7,3
	.word	52847
	.byte	12,16,167,7,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45675
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_FPU_TRAP_CON',0,16,172,7,3
	.word	52908
	.byte	12,16,175,7,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46024
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_FPU_TRAP_OPC',0,16,180,7,3
	.word	52978
	.byte	12,16,183,7,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46184
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_FPU_TRAP_PC',0,16,188,7,3
	.word	53048
	.byte	12,16,191,7,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46265
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_FPU_TRAP_SRC1',0,16,196,7,3
	.word	53117
	.byte	12,16,199,7,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46352
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_FPU_TRAP_SRC2',0,16,204,7,3
	.word	53188
	.byte	12,16,207,7,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46439
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_FPU_TRAP_SRC3',0,16,212,7,3
	.word	53259
	.byte	12,16,215,7,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46526
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_ICNT',0,16,220,7,3
	.word	53330
	.byte	12,16,223,7,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46617
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_ICR',0,16,228,7,3
	.word	53392
	.byte	12,16,231,7,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46760
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_ISP',0,16,236,7,3
	.word	53453
	.byte	12,16,239,7,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46826
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_LCX',0,16,244,7,3
	.word	53514
	.byte	12,16,247,7,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46932
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_M1CNT',0,16,252,7,3
	.word	53575
	.byte	12,16,255,7,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47025
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_M2CNT',0,16,132,8,3
	.word	53638
	.byte	12,16,135,8,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47118
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_M3CNT',0,16,140,8,3
	.word	53701
	.byte	12,16,143,8,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47211
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_PC',0,16,148,8,3
	.word	53764
	.byte	12,16,151,8,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47296
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_PCON0',0,16,156,8,3
	.word	53824
	.byte	12,16,159,8,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47412
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_PCON1',0,16,164,8,3
	.word	53887
	.byte	12,16,167,8,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47523
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_PCON2',0,16,172,8,3
	.word	53950
	.byte	12,16,175,8,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47624
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_PCXI',0,16,180,8,3
	.word	54013
	.byte	12,16,183,8,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47754
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_PIEAR',0,16,188,8,3
	.word	54075
	.byte	12,16,191,8,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47823
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_PIETR',0,16,196,8,3
	.word	54138
	.byte	12,16,199,8,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48052
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_PMA0',0,16,204,8,3
	.word	54201
	.byte	12,16,207,8,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48165
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_PMA1',0,16,212,8,3
	.word	54263
	.byte	12,16,215,8,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48278
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_PMA2',0,16,220,8,3
	.word	54325
	.byte	12,16,223,8,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48369
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_PSTR',0,16,228,8,3
	.word	54387
	.byte	12,16,231,8,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48572
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_PSW',0,16,236,8,3
	.word	54449
	.byte	12,16,239,8,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48815
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_SEGEN',0,16,244,8,3
	.word	54510
	.byte	12,16,247,8,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48943
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_SMACON',0,16,252,8,3
	.word	54573
	.byte	12,16,255,8,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49184
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_SPROT_ACCENA',0,16,132,9,3
	.word	54637
	.byte	12,16,135,9,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49267
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_SPROT_ACCENB',0,16,140,9,3
	.word	54707
	.byte	12,16,143,9,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49358
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA',0,16,148,9,3
	.word	54777
	.byte	12,16,151,9,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49449
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB',0,16,156,9,3
	.word	54851
	.byte	12,16,159,9,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49548
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_SPROT_RGN_LA',0,16,164,9,3
	.word	54925
	.byte	12,16,167,9,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49655
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_SPROT_RGN_UA',0,16,172,9,3
	.word	54995
	.byte	12,16,175,9,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49762
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_SWEVT',0,16,180,9,3
	.word	55065
	.byte	12,16,183,9,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49916
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_SYSCON',0,16,188,9,3
	.word	55128
	.byte	12,16,191,9,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50077
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_TASK_ASI',0,16,196,9,3
	.word	55192
	.byte	12,16,199,9,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50175
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_TPS_CON',0,16,204,9,3
	.word	55258
	.byte	12,16,207,9,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50347
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_TPS_TIMER',0,16,212,9,3
	.word	55323
	.byte	12,16,215,9,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50427
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_TR_ADR',0,16,220,9,3
	.word	55390
	.byte	12,16,223,9,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50500
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_TR_EVT',0,16,228,9,3
	.word	55454
	.byte	12,16,231,9,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50818
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_TRIG_ACC',0,16,236,9,3
	.word	55518
	.byte	10
	.byte	'_Ifx_CPU_CPR',0,16,247,9,25,8,13
	.byte	'L',0
	.word	51408
	.byte	4,2,35,0,13
	.byte	'U',0
	.word	51471
	.byte	4,2,35,4,0,14
	.word	55584
	.byte	31
	.byte	'Ifx_CPU_CPR',0,16,251,9,3
	.word	55626
	.byte	10
	.byte	'_Ifx_CPU_DPR',0,16,254,9,25,8,13
	.byte	'L',0
	.word	52472
	.byte	4,2,35,0,13
	.byte	'U',0
	.word	52535
	.byte	4,2,35,4,0,14
	.word	55652
	.byte	31
	.byte	'Ifx_CPU_DPR',0,16,130,10,3
	.word	55694
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN',0,16,133,10,25,16,13
	.byte	'LA',0
	.word	54925
	.byte	4,2,35,0,13
	.byte	'UA',0
	.word	54995
	.byte	4,2,35,4,13
	.byte	'ACCENA',0
	.word	54777
	.byte	4,2,35,8,13
	.byte	'ACCENB',0
	.word	54851
	.byte	4,2,35,12,0,14
	.word	55720
	.byte	31
	.byte	'Ifx_CPU_SPROT_RGN',0,16,139,10,3
	.word	55802
	.byte	15,12
	.word	55323
	.byte	16,2,0,10
	.byte	'_Ifx_CPU_TPS',0,16,142,10,25,16,13
	.byte	'CON',0
	.word	55258
	.byte	4,2,35,0,13
	.byte	'TIMER',0
	.word	55834
	.byte	12,2,35,4,0,14
	.word	55843
	.byte	31
	.byte	'Ifx_CPU_TPS',0,16,146,10,3
	.word	55891
	.byte	10
	.byte	'_Ifx_CPU_TR',0,16,149,10,25,8,13
	.byte	'EVT',0
	.word	55454
	.byte	4,2,35,0,13
	.byte	'ADR',0
	.word	55390
	.byte	4,2,35,4,0,14
	.word	55917
	.byte	31
	.byte	'Ifx_CPU_TR',0,16,153,10,3
	.word	55962
	.byte	15,176,32
	.word	642
	.byte	16,175,32,0,15,208,223,1
	.word	642
	.byte	16,207,223,1,0,15,248,1
	.word	642
	.byte	16,247,1,0,15,244,29
	.word	642
	.byte	16,243,29,0,15,188,3
	.word	642
	.byte	16,187,3,0,15,232,3
	.word	642
	.byte	16,231,3,0,15,252,23
	.word	642
	.byte	16,251,23,0,15,228,63
	.word	642
	.byte	16,227,63,0,15,128,1
	.word	55652
	.byte	16,15,0,14
	.word	56077
	.byte	15,128,31
	.word	642
	.byte	16,255,30,0,15,64
	.word	55584
	.byte	16,7,0,14
	.word	56103
	.byte	15,192,31
	.word	642
	.byte	16,191,31,0,15,16
	.word	51598
	.byte	16,3,0,15,16
	.word	52598
	.byte	16,3,0,15,16
	.word	52660
	.byte	16,3,0,15,208,7
	.word	642
	.byte	16,207,7,0,14
	.word	55843
	.byte	15,240,23
	.word	642
	.byte	16,239,23,0,15,64
	.word	55917
	.byte	16,7,0,14
	.word	56182
	.byte	15,192,23
	.word	642
	.byte	16,191,23,0,15,232,1
	.word	642
	.byte	16,231,1,0,15,180,1
	.word	642
	.byte	16,179,1,0,15,172,1
	.word	642
	.byte	16,171,1,0,15,64
	.word	51787
	.byte	16,15,0,15,64
	.word	642
	.byte	16,63,0,15,64
	.word	51013
	.byte	16,15,0,10
	.byte	'_Ifx_CPU',0,16,166,10,25,128,128,4,13
	.byte	'reserved_0',0
	.word	55987
	.byte	176,32,2,35,0,13
	.byte	'SEGEN',0
	.word	54510
	.byte	4,3,35,176,32,13
	.byte	'reserved_1034',0
	.word	55998
	.byte	208,223,1,3,35,180,32,13
	.byte	'TASK_ASI',0
	.word	55192
	.byte	4,4,35,132,128,2,13
	.byte	'reserved_8008',0
	.word	56011
	.byte	248,1,4,35,136,128,2,13
	.byte	'PMA0',0
	.word	54201
	.byte	4,4,35,128,130,2,13
	.byte	'PMA1',0
	.word	54263
	.byte	4,4,35,132,130,2,13
	.byte	'PMA2',0
	.word	54325
	.byte	4,4,35,136,130,2,13
	.byte	'reserved_810C',0
	.word	56022
	.byte	244,29,4,35,140,130,2,13
	.byte	'DCON2',0
	.word	52098
	.byte	4,4,35,128,160,2,13
	.byte	'reserved_9004',0
	.word	4279
	.byte	8,4,35,132,160,2,13
	.byte	'SMACON',0
	.word	54573
	.byte	4,4,35,140,160,2,13
	.byte	'DSTR',0
	.word	52722
	.byte	4,4,35,144,160,2,13
	.byte	'reserved_9014',0
	.word	2460
	.byte	4,4,35,148,160,2,13
	.byte	'DATR',0
	.word	51846
	.byte	4,4,35,152,160,2,13
	.byte	'DEADD',0
	.word	52222
	.byte	4,4,35,156,160,2,13
	.byte	'DIEAR',0
	.word	52285
	.byte	4,4,35,160,160,2,13
	.byte	'DIETR',0
	.word	52348
	.byte	4,4,35,164,160,2,13
	.byte	'reserved_9028',0
	.word	3650
	.byte	24,4,35,168,160,2,13
	.byte	'DCON0',0
	.word	52035
	.byte	4,4,35,192,160,2,13
	.byte	'reserved_9044',0
	.word	56033
	.byte	188,3,4,35,196,160,2,13
	.byte	'PSTR',0
	.word	54387
	.byte	4,4,35,128,164,2,13
	.byte	'PCON1',0
	.word	53887
	.byte	4,4,35,132,164,2,13
	.byte	'PCON2',0
	.word	53950
	.byte	4,4,35,136,164,2,13
	.byte	'PCON0',0
	.word	53824
	.byte	4,4,35,140,164,2,13
	.byte	'PIEAR',0
	.word	54075
	.byte	4,4,35,144,164,2,13
	.byte	'PIETR',0
	.word	54138
	.byte	4,4,35,148,164,2,13
	.byte	'reserved_9218',0
	.word	56044
	.byte	232,3,4,35,152,164,2,13
	.byte	'COMPAT',0
	.word	51319
	.byte	4,4,35,128,168,2,13
	.byte	'reserved_9404',0
	.word	56055
	.byte	252,23,4,35,132,168,2,13
	.byte	'FPU_TRAP_CON',0
	.word	52908
	.byte	4,4,35,128,192,2,13
	.byte	'FPU_TRAP_PC',0
	.word	53048
	.byte	4,4,35,132,192,2,13
	.byte	'FPU_TRAP_OPC',0
	.word	52978
	.byte	4,4,35,136,192,2,13
	.byte	'reserved_A00C',0
	.word	2460
	.byte	4,4,35,140,192,2,13
	.byte	'FPU_TRAP_SRC1',0
	.word	53117
	.byte	4,4,35,144,192,2,13
	.byte	'FPU_TRAP_SRC2',0
	.word	53188
	.byte	4,4,35,148,192,2,13
	.byte	'FPU_TRAP_SRC3',0
	.word	53259
	.byte	4,4,35,152,192,2,13
	.byte	'reserved_A01C',0
	.word	56066
	.byte	228,63,4,35,156,192,2,13
	.byte	'DPR',0
	.word	56087
	.byte	128,1,4,35,128,128,3,13
	.byte	'reserved_C080',0
	.word	56092
	.byte	128,31,4,35,128,129,3,13
	.byte	'CPR',0
	.word	56112
	.byte	64,4,35,128,160,3,13
	.byte	'reserved_D040',0
	.word	56117
	.byte	192,31,4,35,192,160,3,13
	.byte	'CPXE',0
	.word	56128
	.byte	16,4,35,128,192,3,13
	.byte	'DPRE',0
	.word	56137
	.byte	16,4,35,144,192,3,13
	.byte	'DPWE',0
	.word	56146
	.byte	16,4,35,160,192,3,13
	.byte	'reserved_E030',0
	.word	56155
	.byte	208,7,4,35,176,192,3,13
	.byte	'TPS',0
	.word	56166
	.byte	16,4,35,128,200,3,13
	.byte	'reserved_E410',0
	.word	56171
	.byte	240,23,4,35,144,200,3,13
	.byte	'TR',0
	.word	56191
	.byte	64,4,35,128,224,3,13
	.byte	'reserved_F040',0
	.word	56196
	.byte	192,23,4,35,192,224,3,13
	.byte	'CCTRL',0
	.word	51256
	.byte	4,4,35,128,248,3,13
	.byte	'CCNT',0
	.word	51194
	.byte	4,4,35,132,248,3,13
	.byte	'ICNT',0
	.word	53330
	.byte	4,4,35,136,248,3,13
	.byte	'M1CNT',0
	.word	53575
	.byte	4,4,35,140,248,3,13
	.byte	'M2CNT',0
	.word	53638
	.byte	4,4,35,144,248,3,13
	.byte	'M3CNT',0
	.word	53701
	.byte	4,4,35,148,248,3,13
	.byte	'reserved_FC18',0
	.word	56207
	.byte	232,1,4,35,152,248,3,13
	.byte	'DBGSR',0
	.word	51908
	.byte	4,4,35,128,250,3,13
	.byte	'reserved_FD04',0
	.word	2460
	.byte	4,4,35,132,250,3,13
	.byte	'EXEVT',0
	.word	52784
	.byte	4,4,35,136,250,3,13
	.byte	'CREVT',0
	.word	51660
	.byte	4,4,35,140,250,3,13
	.byte	'SWEVT',0
	.word	55065
	.byte	4,4,35,144,250,3,13
	.byte	'reserved_FD14',0
	.word	39684
	.byte	28,4,35,148,250,3,13
	.byte	'TRIG_ACC',0
	.word	55518
	.byte	4,4,35,176,250,3,13
	.byte	'reserved_FD34',0
	.word	4619
	.byte	12,4,35,180,250,3,13
	.byte	'DMS',0
	.word	52411
	.byte	4,4,35,192,250,3,13
	.byte	'DCX',0
	.word	52161
	.byte	4,4,35,196,250,3,13
	.byte	'DBGTCR',0
	.word	51971
	.byte	4,4,35,200,250,3,13
	.byte	'reserved_FD4C',0
	.word	56218
	.byte	180,1,4,35,204,250,3,13
	.byte	'PCXI',0
	.word	54013
	.byte	4,4,35,128,252,3,13
	.byte	'PSW',0
	.word	54449
	.byte	4,4,35,132,252,3,13
	.byte	'PC',0
	.word	53764
	.byte	4,4,35,136,252,3,13
	.byte	'reserved_FE0C',0
	.word	4279
	.byte	8,4,35,140,252,3,13
	.byte	'SYSCON',0
	.word	55128
	.byte	4,4,35,148,252,3,13
	.byte	'CPU_ID',0
	.word	51534
	.byte	4,4,35,152,252,3,13
	.byte	'CORE_ID',0
	.word	15044
	.byte	4,4,35,156,252,3,13
	.byte	'BIV',0
	.word	51072
	.byte	4,4,35,160,252,3,13
	.byte	'BTV',0
	.word	51133
	.byte	4,4,35,164,252,3,13
	.byte	'ISP',0
	.word	53453
	.byte	4,4,35,168,252,3,13
	.byte	'ICR',0
	.word	53392
	.byte	4,4,35,172,252,3,13
	.byte	'reserved_FE30',0
	.word	4279
	.byte	8,4,35,176,252,3,13
	.byte	'FCX',0
	.word	52847
	.byte	4,4,35,184,252,3,13
	.byte	'LCX',0
	.word	53514
	.byte	4,4,35,188,252,3,13
	.byte	'reserved_FE40',0
	.word	39675
	.byte	16,4,35,192,252,3,13
	.byte	'CUS_ID',0
	.word	51723
	.byte	4,4,35,208,252,3,13
	.byte	'reserved_FE54',0
	.word	56229
	.byte	172,1,4,35,212,252,3,13
	.byte	'D',0
	.word	56240
	.byte	64,4,35,128,254,3,13
	.byte	'reserved_FF40',0
	.word	56249
	.byte	64,4,35,192,254,3,13
	.byte	'A',0
	.word	56258
	.byte	64,4,35,128,255,3,13
	.byte	'reserved_FFC0',0
	.word	56249
	.byte	64,4,35,192,255,3,0,14
	.word	56267
	.byte	31
	.byte	'Ifx_CPU',0,16,130,11,3
	.word	58058
	.byte	31
	.byte	'IfxCpu_Id',0,9,132,1,3
	.word	9638
	.byte	31
	.byte	'IfxCpu_ResourceCpu',0,9,161,1,3
	.word	9720
	.byte	31
	.byte	'Ifx_SRC_SRCR_Bits',0,11,62,3
	.word	10106
	.byte	31
	.byte	'Ifx_SRC_SRCR',0,11,75,3
	.word	10396
	.byte	10
	.byte	'_Ifx_SRC_AGBT',0,11,86,25,4,13
	.byte	'SR',0
	.word	10396
	.byte	4,2,35,0,0,14
	.word	58174
	.byte	31
	.byte	'Ifx_SRC_AGBT',0,11,89,3
	.word	58206
	.byte	10
	.byte	'_Ifx_SRC_ASCLIN',0,11,92,25,12,13
	.byte	'TX',0
	.word	10396
	.byte	4,2,35,0,13
	.byte	'RX',0
	.word	10396
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	10396
	.byte	4,2,35,8,0,14
	.word	58232
	.byte	31
	.byte	'Ifx_SRC_ASCLIN',0,11,97,3
	.word	58291
	.byte	10
	.byte	'_Ifx_SRC_BCUSPB',0,11,100,25,4,13
	.byte	'SBSRC',0
	.word	10396
	.byte	4,2,35,0,0,14
	.word	58319
	.byte	31
	.byte	'Ifx_SRC_BCUSPB',0,11,103,3
	.word	58356
	.byte	15,64
	.word	10396
	.byte	16,15,0,10
	.byte	'_Ifx_SRC_CAN',0,11,106,25,64,13
	.byte	'INT',0
	.word	58384
	.byte	64,2,35,0,0,14
	.word	58393
	.byte	31
	.byte	'Ifx_SRC_CAN',0,11,109,3
	.word	58425
	.byte	10
	.byte	'_Ifx_SRC_CCU6',0,11,112,25,16,13
	.byte	'SR0',0
	.word	10396
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10396
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	10396
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	10396
	.byte	4,2,35,12,0,14
	.word	58450
	.byte	31
	.byte	'Ifx_SRC_CCU6',0,11,118,3
	.word	58522
	.byte	15,8
	.word	10396
	.byte	16,1,0,10
	.byte	'_Ifx_SRC_CERBERUS',0,11,121,25,8,13
	.byte	'SR',0
	.word	58548
	.byte	8,2,35,0,0,14
	.word	58557
	.byte	31
	.byte	'Ifx_SRC_CERBERUS',0,11,124,3
	.word	58593
	.byte	10
	.byte	'_Ifx_SRC_CIF',0,11,127,25,16,13
	.byte	'MI',0
	.word	10396
	.byte	4,2,35,0,13
	.byte	'MIEP',0
	.word	10396
	.byte	4,2,35,4,13
	.byte	'ISP',0
	.word	10396
	.byte	4,2,35,8,13
	.byte	'MJPEG',0
	.word	10396
	.byte	4,2,35,12,0,14
	.word	58623
	.byte	31
	.byte	'Ifx_SRC_CIF',0,11,133,1,3
	.word	58696
	.byte	10
	.byte	'_Ifx_SRC_CPU',0,11,136,1,25,4,13
	.byte	'SBSRC',0
	.word	10396
	.byte	4,2,35,0,0,14
	.word	58722
	.byte	31
	.byte	'Ifx_SRC_CPU',0,11,139,1,3
	.word	58757
	.byte	15,192,1
	.word	10396
	.byte	16,47,0,10
	.byte	'_Ifx_SRC_DMA',0,11,142,1,25,208,1,13
	.byte	'ERR',0
	.word	10396
	.byte	4,2,35,0,13
	.byte	'reserved_4',0
	.word	4619
	.byte	12,2,35,4,13
	.byte	'CH',0
	.word	58783
	.byte	192,1,2,35,16,0,14
	.word	58793
	.byte	31
	.byte	'Ifx_SRC_DMA',0,11,147,1,3
	.word	58860
	.byte	10
	.byte	'_Ifx_SRC_DSADC',0,11,150,1,25,8,13
	.byte	'SRM',0
	.word	10396
	.byte	4,2,35,0,13
	.byte	'SRA',0
	.word	10396
	.byte	4,2,35,4,0,14
	.word	58886
	.byte	31
	.byte	'Ifx_SRC_DSADC',0,11,154,1,3
	.word	58934
	.byte	10
	.byte	'_Ifx_SRC_EMEM',0,11,157,1,25,4,13
	.byte	'SR',0
	.word	10396
	.byte	4,2,35,0,0,14
	.word	58962
	.byte	31
	.byte	'Ifx_SRC_EMEM',0,11,160,1,3
	.word	58995
	.byte	10
	.byte	'_Ifx_SRC_ERAY',0,11,163,1,25,80,13
	.byte	'INT',0
	.word	58548
	.byte	8,2,35,0,13
	.byte	'TINT',0
	.word	58548
	.byte	8,2,35,8,13
	.byte	'NDAT',0
	.word	58548
	.byte	8,2,35,16,13
	.byte	'MBSC',0
	.word	58548
	.byte	8,2,35,24,13
	.byte	'OBUSY',0
	.word	10396
	.byte	4,2,35,32,13
	.byte	'IBUSY',0
	.word	10396
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	39693
	.byte	40,2,35,40,0,14
	.word	59022
	.byte	31
	.byte	'Ifx_SRC_ERAY',0,11,172,1,3
	.word	59149
	.byte	10
	.byte	'_Ifx_SRC_ETH',0,11,175,1,25,4,13
	.byte	'SR',0
	.word	10396
	.byte	4,2,35,0,0,14
	.word	59176
	.byte	31
	.byte	'Ifx_SRC_ETH',0,11,178,1,3
	.word	59208
	.byte	10
	.byte	'_Ifx_SRC_FCE',0,11,181,1,25,4,13
	.byte	'SR',0
	.word	10396
	.byte	4,2,35,0,0,14
	.word	59234
	.byte	31
	.byte	'Ifx_SRC_FCE',0,11,184,1,3
	.word	59266
	.byte	10
	.byte	'_Ifx_SRC_FFT',0,11,187,1,25,12,13
	.byte	'DONE',0
	.word	10396
	.byte	4,2,35,0,13
	.byte	'ERR',0
	.word	10396
	.byte	4,2,35,4,13
	.byte	'RFS',0
	.word	10396
	.byte	4,2,35,8,0,14
	.word	59292
	.byte	31
	.byte	'Ifx_SRC_FFT',0,11,192,1,3
	.word	59352
	.byte	10
	.byte	'_Ifx_SRC_GPSR',0,11,195,1,25,32,13
	.byte	'SR0',0
	.word	10396
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10396
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	10396
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	10396
	.byte	4,2,35,12,13
	.byte	'reserved_10',0
	.word	39675
	.byte	16,2,35,16,0,14
	.word	59378
	.byte	31
	.byte	'Ifx_SRC_GPSR',0,11,202,1,3
	.word	59472
	.byte	10
	.byte	'_Ifx_SRC_GPT12',0,11,205,1,25,48,13
	.byte	'CIRQ',0
	.word	10396
	.byte	4,2,35,0,13
	.byte	'T2',0
	.word	10396
	.byte	4,2,35,4,13
	.byte	'T3',0
	.word	10396
	.byte	4,2,35,8,13
	.byte	'T4',0
	.word	10396
	.byte	4,2,35,12,13
	.byte	'T5',0
	.word	10396
	.byte	4,2,35,16,13
	.byte	'T6',0
	.word	10396
	.byte	4,2,35,20,13
	.byte	'reserved_18',0
	.word	3650
	.byte	24,2,35,24,0,14
	.word	59499
	.byte	31
	.byte	'Ifx_SRC_GPT12',0,11,214,1,3
	.word	59616
	.byte	15,12
	.word	10396
	.byte	16,2,0,15,32
	.word	10396
	.byte	16,7,0,15,32
	.word	59653
	.byte	16,0,0,15,88
	.word	642
	.byte	16,87,0,15,108
	.word	10396
	.byte	16,26,0,15,96
	.word	642
	.byte	16,95,0,15,96
	.word	59653
	.byte	16,2,0,15,160,3
	.word	642
	.byte	16,159,3,0,15,64
	.word	59653
	.byte	16,1,0,15,192,3
	.word	642
	.byte	16,191,3,0,15,16
	.word	10396
	.byte	16,3,0,15,64
	.word	59738
	.byte	16,3,0,15,192,2
	.word	642
	.byte	16,191,2,0,15,52
	.word	642
	.byte	16,51,0,10
	.byte	'_Ifx_SRC_GTM',0,11,217,1,25,204,18,13
	.byte	'AEIIRQ',0
	.word	10396
	.byte	4,2,35,0,13
	.byte	'ARUIRQ',0
	.word	59644
	.byte	12,2,35,4,13
	.byte	'reserved_10',0
	.word	2460
	.byte	4,2,35,16,13
	.byte	'BRCIRQ',0
	.word	10396
	.byte	4,2,35,20,13
	.byte	'CMPIRQ',0
	.word	10396
	.byte	4,2,35,24,13
	.byte	'SPEIRQ',0
	.word	58548
	.byte	8,2,35,28,13
	.byte	'reserved_24',0
	.word	4279
	.byte	8,2,35,36,13
	.byte	'PSM',0
	.word	59662
	.byte	32,2,35,44,13
	.byte	'reserved_4C',0
	.word	59671
	.byte	88,2,35,76,13
	.byte	'DPLL',0
	.word	59680
	.byte	108,3,35,164,1,13
	.byte	'reserved_110',0
	.word	59689
	.byte	96,3,35,144,2,13
	.byte	'ERR',0
	.word	10396
	.byte	4,3,35,240,2,13
	.byte	'reserved_174',0
	.word	4619
	.byte	12,3,35,244,2,13
	.byte	'TIM',0
	.word	59698
	.byte	96,3,35,128,3,13
	.byte	'reserved_1E0',0
	.word	59707
	.byte	160,3,3,35,224,3,13
	.byte	'MCS',0
	.word	59698
	.byte	96,3,35,128,7,13
	.byte	'reserved_3E0',0
	.word	59707
	.byte	160,3,3,35,224,7,13
	.byte	'TOM',0
	.word	59718
	.byte	64,3,35,128,11,13
	.byte	'reserved_5C0',0
	.word	59727
	.byte	192,3,3,35,192,11,13
	.byte	'ATOM',0
	.word	59747
	.byte	64,3,35,128,15,13
	.byte	'reserved_7C0',0
	.word	59756
	.byte	192,2,3,35,192,15,13
	.byte	'MCSW0',0
	.word	59644
	.byte	12,3,35,128,18,13
	.byte	'reserved_90C',0
	.word	59767
	.byte	52,3,35,140,18,13
	.byte	'MCSW1',0
	.word	59644
	.byte	12,3,35,192,18,0,14
	.word	59776
	.byte	31
	.byte	'Ifx_SRC_GTM',0,11,243,1,3
	.word	60236
	.byte	10
	.byte	'_Ifx_SRC_HSCT',0,11,246,1,25,4,13
	.byte	'SR',0
	.word	10396
	.byte	4,2,35,0,0,14
	.word	60262
	.byte	31
	.byte	'Ifx_SRC_HSCT',0,11,249,1,3
	.word	60295
	.byte	10
	.byte	'_Ifx_SRC_HSSL',0,11,252,1,25,16,13
	.byte	'COK',0
	.word	10396
	.byte	4,2,35,0,13
	.byte	'RDI',0
	.word	10396
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	10396
	.byte	4,2,35,8,13
	.byte	'TRG',0
	.word	10396
	.byte	4,2,35,12,0,14
	.word	60322
	.byte	31
	.byte	'Ifx_SRC_HSSL',0,11,130,2,3
	.word	60395
	.byte	15,56
	.word	642
	.byte	16,55,0,10
	.byte	'_Ifx_SRC_I2C',0,11,133,2,25,80,13
	.byte	'BREQ',0
	.word	10396
	.byte	4,2,35,0,13
	.byte	'LBREQ',0
	.word	10396
	.byte	4,2,35,4,13
	.byte	'SREQ',0
	.word	10396
	.byte	4,2,35,8,13
	.byte	'LSREQ',0
	.word	10396
	.byte	4,2,35,12,13
	.byte	'ERR',0
	.word	10396
	.byte	4,2,35,16,13
	.byte	'P',0
	.word	10396
	.byte	4,2,35,20,13
	.byte	'reserved_18',0
	.word	60422
	.byte	56,2,35,24,0,14
	.word	60431
	.byte	31
	.byte	'Ifx_SRC_I2C',0,11,142,2,3
	.word	60554
	.byte	10
	.byte	'_Ifx_SRC_LMU',0,11,145,2,25,4,13
	.byte	'SR',0
	.word	10396
	.byte	4,2,35,0,0,14
	.word	60580
	.byte	31
	.byte	'Ifx_SRC_LMU',0,11,148,2,3
	.word	60612
	.byte	10
	.byte	'_Ifx_SRC_MSC',0,11,151,2,25,20,13
	.byte	'SR0',0
	.word	10396
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10396
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	10396
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	10396
	.byte	4,2,35,12,13
	.byte	'SR4',0
	.word	10396
	.byte	4,2,35,16,0,14
	.word	60638
	.byte	31
	.byte	'Ifx_SRC_MSC',0,11,158,2,3
	.word	60723
	.byte	10
	.byte	'_Ifx_SRC_PMU',0,11,161,2,25,4,13
	.byte	'SR',0
	.word	10396
	.byte	4,2,35,0,0,14
	.word	60749
	.byte	31
	.byte	'Ifx_SRC_PMU',0,11,164,2,3
	.word	60781
	.byte	10
	.byte	'_Ifx_SRC_PSI5',0,11,167,2,25,32,13
	.byte	'SR',0
	.word	59653
	.byte	32,2,35,0,0,14
	.word	60807
	.byte	31
	.byte	'Ifx_SRC_PSI5',0,11,170,2,3
	.word	60840
	.byte	10
	.byte	'_Ifx_SRC_PSI5S',0,11,173,2,25,32,13
	.byte	'SR',0
	.word	59653
	.byte	32,2,35,0,0,14
	.word	60867
	.byte	31
	.byte	'Ifx_SRC_PSI5S',0,11,176,2,3
	.word	60901
	.byte	10
	.byte	'_Ifx_SRC_QSPI',0,11,179,2,25,24,13
	.byte	'TX',0
	.word	10396
	.byte	4,2,35,0,13
	.byte	'RX',0
	.word	10396
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	10396
	.byte	4,2,35,8,13
	.byte	'PT',0
	.word	10396
	.byte	4,2,35,12,13
	.byte	'HC',0
	.word	10396
	.byte	4,2,35,16,13
	.byte	'U',0
	.word	10396
	.byte	4,2,35,20,0,14
	.word	60929
	.byte	31
	.byte	'Ifx_SRC_QSPI',0,11,187,2,3
	.word	61022
	.byte	10
	.byte	'_Ifx_SRC_SCR',0,11,190,2,25,4,13
	.byte	'SR',0
	.word	10396
	.byte	4,2,35,0,0,14
	.word	61049
	.byte	31
	.byte	'Ifx_SRC_SCR',0,11,193,2,3
	.word	61081
	.byte	10
	.byte	'_Ifx_SRC_SCU',0,11,196,2,25,20,13
	.byte	'DTS',0
	.word	10396
	.byte	4,2,35,0,13
	.byte	'ERU',0
	.word	59738
	.byte	16,2,35,4,0,14
	.word	61107
	.byte	31
	.byte	'Ifx_SRC_SCU',0,11,200,2,3
	.word	61153
	.byte	15,24
	.word	10396
	.byte	16,5,0,10
	.byte	'_Ifx_SRC_SENT',0,11,203,2,25,24,13
	.byte	'SR',0
	.word	61179
	.byte	24,2,35,0,0,14
	.word	61188
	.byte	31
	.byte	'Ifx_SRC_SENT',0,11,206,2,3
	.word	61221
	.byte	10
	.byte	'_Ifx_SRC_SMU',0,11,209,2,25,12,13
	.byte	'SR',0
	.word	59644
	.byte	12,2,35,0,0,14
	.word	61248
	.byte	31
	.byte	'Ifx_SRC_SMU',0,11,212,2,3
	.word	61280
	.byte	10
	.byte	'_Ifx_SRC_STM',0,11,215,2,25,8,13
	.byte	'SR0',0
	.word	10396
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10396
	.byte	4,2,35,4,0,14
	.word	61306
	.byte	31
	.byte	'Ifx_SRC_STM',0,11,219,2,3
	.word	61352
	.byte	10
	.byte	'_Ifx_SRC_VADCCG',0,11,222,2,25,16,13
	.byte	'SR0',0
	.word	10396
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10396
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	10396
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	10396
	.byte	4,2,35,12,0,14
	.word	61378
	.byte	31
	.byte	'Ifx_SRC_VADCCG',0,11,228,2,3
	.word	61453
	.byte	10
	.byte	'_Ifx_SRC_VADCG',0,11,231,2,25,16,13
	.byte	'SR0',0
	.word	10396
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10396
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	10396
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	10396
	.byte	4,2,35,12,0,14
	.word	61482
	.byte	31
	.byte	'Ifx_SRC_VADCG',0,11,237,2,3
	.word	61556
	.byte	10
	.byte	'_Ifx_SRC_XBAR',0,11,240,2,25,4,13
	.byte	'SRC',0
	.word	10396
	.byte	4,2,35,0,0,14
	.word	61584
	.byte	31
	.byte	'Ifx_SRC_XBAR',0,11,243,2,3
	.word	61618
	.byte	15,4
	.word	58174
	.byte	16,0,0,14
	.word	61645
	.byte	10
	.byte	'_Ifx_SRC_GAGBT',0,11,128,3,25,4,13
	.byte	'AGBT',0
	.word	61654
	.byte	4,2,35,0,0,14
	.word	61659
	.byte	31
	.byte	'Ifx_SRC_GAGBT',0,11,131,3,3
	.word	61695
	.byte	15,48
	.word	58232
	.byte	16,3,0,14
	.word	61723
	.byte	10
	.byte	'_Ifx_SRC_GASCLIN',0,11,134,3,25,48,13
	.byte	'ASCLIN',0
	.word	61732
	.byte	48,2,35,0,0,14
	.word	61737
	.byte	31
	.byte	'Ifx_SRC_GASCLIN',0,11,137,3,3
	.word	61777
	.byte	14
	.word	58319
	.byte	10
	.byte	'_Ifx_SRC_GBCU',0,11,140,3,25,4,13
	.byte	'SPB',0
	.word	61807
	.byte	4,2,35,0,0,14
	.word	61812
	.byte	31
	.byte	'Ifx_SRC_GBCU',0,11,143,3,3
	.word	61846
	.byte	15,64
	.word	58393
	.byte	16,0,0,14
	.word	61873
	.byte	10
	.byte	'_Ifx_SRC_GCAN',0,11,146,3,25,64,13
	.byte	'CAN',0
	.word	61882
	.byte	64,2,35,0,0,14
	.word	61887
	.byte	31
	.byte	'Ifx_SRC_GCAN',0,11,149,3,3
	.word	61921
	.byte	15,32
	.word	58450
	.byte	16,1,0,14
	.word	61948
	.byte	10
	.byte	'_Ifx_SRC_GCCU6',0,11,152,3,25,32,13
	.byte	'CCU6',0
	.word	61957
	.byte	32,2,35,0,0,14
	.word	61962
	.byte	31
	.byte	'Ifx_SRC_GCCU6',0,11,155,3,3
	.word	61998
	.byte	14
	.word	58557
	.byte	10
	.byte	'_Ifx_SRC_GCERBERUS',0,11,158,3,25,8,13
	.byte	'CERBERUS',0
	.word	62026
	.byte	8,2,35,0,0,14
	.word	62031
	.byte	31
	.byte	'Ifx_SRC_GCERBERUS',0,11,161,3,3
	.word	62075
	.byte	15,16
	.word	58623
	.byte	16,0,0,14
	.word	62107
	.byte	10
	.byte	'_Ifx_SRC_GCIF',0,11,164,3,25,16,13
	.byte	'CIF',0
	.word	62116
	.byte	16,2,35,0,0,14
	.word	62121
	.byte	31
	.byte	'Ifx_SRC_GCIF',0,11,167,3,3
	.word	62155
	.byte	15,8
	.word	58722
	.byte	16,1,0,14
	.word	62182
	.byte	10
	.byte	'_Ifx_SRC_GCPU',0,11,170,3,25,8,13
	.byte	'CPU',0
	.word	62191
	.byte	8,2,35,0,0,14
	.word	62196
	.byte	31
	.byte	'Ifx_SRC_GCPU',0,11,173,3,3
	.word	62230
	.byte	15,208,1
	.word	58793
	.byte	16,0,0,14
	.word	62257
	.byte	10
	.byte	'_Ifx_SRC_GDMA',0,11,176,3,25,208,1,13
	.byte	'DMA',0
	.word	62267
	.byte	208,1,2,35,0,0,14
	.word	62272
	.byte	31
	.byte	'Ifx_SRC_GDMA',0,11,179,3,3
	.word	62308
	.byte	14
	.word	58886
	.byte	14
	.word	58886
	.byte	14
	.word	58886
	.byte	10
	.byte	'_Ifx_SRC_GDSADC',0,11,182,3,25,32,13
	.byte	'DSADC0',0
	.word	62335
	.byte	8,2,35,0,13
	.byte	'reserved_8',0
	.word	4279
	.byte	8,2,35,8,13
	.byte	'DSADC2',0
	.word	62340
	.byte	8,2,35,16,13
	.byte	'DSADC3',0
	.word	62345
	.byte	8,2,35,24,0,14
	.word	62350
	.byte	31
	.byte	'Ifx_SRC_GDSADC',0,11,188,3,3
	.word	62441
	.byte	15,4
	.word	58962
	.byte	16,0,0,14
	.word	62470
	.byte	10
	.byte	'_Ifx_SRC_GEMEM',0,11,191,3,25,4,13
	.byte	'EMEM',0
	.word	62479
	.byte	4,2,35,0,0,14
	.word	62484
	.byte	31
	.byte	'Ifx_SRC_GEMEM',0,11,194,3,3
	.word	62520
	.byte	15,80
	.word	59022
	.byte	16,0,0,14
	.word	62548
	.byte	10
	.byte	'_Ifx_SRC_GERAY',0,11,197,3,25,80,13
	.byte	'ERAY',0
	.word	62557
	.byte	80,2,35,0,0,14
	.word	62562
	.byte	31
	.byte	'Ifx_SRC_GERAY',0,11,200,3,3
	.word	62598
	.byte	15,4
	.word	59176
	.byte	16,0,0,14
	.word	62626
	.byte	10
	.byte	'_Ifx_SRC_GETH',0,11,203,3,25,4,13
	.byte	'ETH',0
	.word	62635
	.byte	4,2,35,0,0,14
	.word	62640
	.byte	31
	.byte	'Ifx_SRC_GETH',0,11,206,3,3
	.word	62674
	.byte	15,4
	.word	59234
	.byte	16,0,0,14
	.word	62701
	.byte	10
	.byte	'_Ifx_SRC_GFCE',0,11,209,3,25,4,13
	.byte	'FCE',0
	.word	62710
	.byte	4,2,35,0,0,14
	.word	62715
	.byte	31
	.byte	'Ifx_SRC_GFCE',0,11,212,3,3
	.word	62749
	.byte	15,12
	.word	59292
	.byte	16,0,0,14
	.word	62776
	.byte	10
	.byte	'_Ifx_SRC_GFFT',0,11,215,3,25,12,13
	.byte	'FFT',0
	.word	62785
	.byte	12,2,35,0,0,14
	.word	62790
	.byte	31
	.byte	'Ifx_SRC_GFFT',0,11,218,3,3
	.word	62824
	.byte	15,64
	.word	59378
	.byte	16,1,0,14
	.word	62851
	.byte	10
	.byte	'_Ifx_SRC_GGPSR',0,11,221,3,25,64,13
	.byte	'GPSR',0
	.word	62860
	.byte	64,2,35,0,0,14
	.word	62865
	.byte	31
	.byte	'Ifx_SRC_GGPSR',0,11,224,3,3
	.word	62901
	.byte	15,48
	.word	59499
	.byte	16,0,0,14
	.word	62929
	.byte	10
	.byte	'_Ifx_SRC_GGPT12',0,11,227,3,25,48,13
	.byte	'GPT12',0
	.word	62938
	.byte	48,2,35,0,0,14
	.word	62943
	.byte	31
	.byte	'Ifx_SRC_GGPT12',0,11,230,3,3
	.word	62981
	.byte	15,204,18
	.word	59776
	.byte	16,0,0,14
	.word	63010
	.byte	10
	.byte	'_Ifx_SRC_GGTM',0,11,233,3,25,204,18,13
	.byte	'GTM',0
	.word	63020
	.byte	204,18,2,35,0,0,14
	.word	63025
	.byte	31
	.byte	'Ifx_SRC_GGTM',0,11,236,3,3
	.word	63061
	.byte	15,4
	.word	60262
	.byte	16,0,0,14
	.word	63088
	.byte	10
	.byte	'_Ifx_SRC_GHSCT',0,11,239,3,25,4,13
	.byte	'HSCT',0
	.word	63097
	.byte	4,2,35,0,0,14
	.word	63102
	.byte	31
	.byte	'Ifx_SRC_GHSCT',0,11,242,3,3
	.word	63138
	.byte	15,64
	.word	60322
	.byte	16,3,0,14
	.word	63166
	.byte	10
	.byte	'_Ifx_SRC_GHSSL',0,11,245,3,25,68,13
	.byte	'HSSL',0
	.word	63175
	.byte	64,2,35,0,13
	.byte	'EXI',0
	.word	10396
	.byte	4,2,35,64,0,14
	.word	63180
	.byte	31
	.byte	'Ifx_SRC_GHSSL',0,11,249,3,3
	.word	63229
	.byte	15,80
	.word	60431
	.byte	16,0,0,14
	.word	63257
	.byte	10
	.byte	'_Ifx_SRC_GI2C',0,11,252,3,25,80,13
	.byte	'I2C',0
	.word	63266
	.byte	80,2,35,0,0,14
	.word	63271
	.byte	31
	.byte	'Ifx_SRC_GI2C',0,11,255,3,3
	.word	63305
	.byte	15,4
	.word	60580
	.byte	16,0,0,14
	.word	63332
	.byte	10
	.byte	'_Ifx_SRC_GLMU',0,11,130,4,25,4,13
	.byte	'LMU',0
	.word	63341
	.byte	4,2,35,0,0,14
	.word	63346
	.byte	31
	.byte	'Ifx_SRC_GLMU',0,11,133,4,3
	.word	63380
	.byte	15,40
	.word	60638
	.byte	16,1,0,14
	.word	63407
	.byte	10
	.byte	'_Ifx_SRC_GMSC',0,11,136,4,25,40,13
	.byte	'MSC',0
	.word	63416
	.byte	40,2,35,0,0,14
	.word	63421
	.byte	31
	.byte	'Ifx_SRC_GMSC',0,11,139,4,3
	.word	63455
	.byte	15,8
	.word	60749
	.byte	16,1,0,14
	.word	63482
	.byte	10
	.byte	'_Ifx_SRC_GPMU',0,11,142,4,25,8,13
	.byte	'PMU',0
	.word	63491
	.byte	8,2,35,0,0,14
	.word	63496
	.byte	31
	.byte	'Ifx_SRC_GPMU',0,11,145,4,3
	.word	63530
	.byte	15,32
	.word	60807
	.byte	16,0,0,14
	.word	63557
	.byte	10
	.byte	'_Ifx_SRC_GPSI5',0,11,148,4,25,32,13
	.byte	'PSI5',0
	.word	63566
	.byte	32,2,35,0,0,14
	.word	63571
	.byte	31
	.byte	'Ifx_SRC_GPSI5',0,11,151,4,3
	.word	63607
	.byte	15,32
	.word	60867
	.byte	16,0,0,14
	.word	63635
	.byte	10
	.byte	'_Ifx_SRC_GPSI5S',0,11,154,4,25,32,13
	.byte	'PSI5S',0
	.word	63644
	.byte	32,2,35,0,0,14
	.word	63649
	.byte	31
	.byte	'Ifx_SRC_GPSI5S',0,11,157,4,3
	.word	63687
	.byte	15,96
	.word	60929
	.byte	16,3,0,14
	.word	63716
	.byte	10
	.byte	'_Ifx_SRC_GQSPI',0,11,160,4,25,96,13
	.byte	'QSPI',0
	.word	63725
	.byte	96,2,35,0,0,14
	.word	63730
	.byte	31
	.byte	'Ifx_SRC_GQSPI',0,11,163,4,3
	.word	63766
	.byte	15,4
	.word	61049
	.byte	16,0,0,14
	.word	63794
	.byte	10
	.byte	'_Ifx_SRC_GSCR',0,11,166,4,25,4,13
	.byte	'SCR',0
	.word	63803
	.byte	4,2,35,0,0,14
	.word	63808
	.byte	31
	.byte	'Ifx_SRC_GSCR',0,11,169,4,3
	.word	63842
	.byte	14
	.word	61107
	.byte	10
	.byte	'_Ifx_SRC_GSCU',0,11,172,4,25,20,13
	.byte	'SCU',0
	.word	63869
	.byte	20,2,35,0,0,14
	.word	63874
	.byte	31
	.byte	'Ifx_SRC_GSCU',0,11,175,4,3
	.word	63908
	.byte	15,24
	.word	61188
	.byte	16,0,0,14
	.word	63935
	.byte	10
	.byte	'_Ifx_SRC_GSENT',0,11,178,4,25,24,13
	.byte	'SENT',0
	.word	63944
	.byte	24,2,35,0,0,14
	.word	63949
	.byte	31
	.byte	'Ifx_SRC_GSENT',0,11,181,4,3
	.word	63985
	.byte	15,12
	.word	61248
	.byte	16,0,0,14
	.word	64013
	.byte	10
	.byte	'_Ifx_SRC_GSMU',0,11,184,4,25,12,13
	.byte	'SMU',0
	.word	64022
	.byte	12,2,35,0,0,14
	.word	64027
	.byte	31
	.byte	'Ifx_SRC_GSMU',0,11,187,4,3
	.word	64061
	.byte	15,16
	.word	61306
	.byte	16,1,0,14
	.word	64088
	.byte	10
	.byte	'_Ifx_SRC_GSTM',0,11,190,4,25,16,13
	.byte	'STM',0
	.word	64097
	.byte	16,2,35,0,0,14
	.word	64102
	.byte	31
	.byte	'Ifx_SRC_GSTM',0,11,193,4,3
	.word	64136
	.byte	15,64
	.word	61482
	.byte	16,3,0,14
	.word	64163
	.byte	15,224,1
	.word	642
	.byte	16,223,1,0,15,32
	.word	61378
	.byte	16,1,0,14
	.word	64188
	.byte	10
	.byte	'_Ifx_SRC_GVADC',0,11,196,4,25,192,2,13
	.byte	'G',0
	.word	64172
	.byte	64,2,35,0,13
	.byte	'reserved_40',0
	.word	64177
	.byte	224,1,2,35,64,13
	.byte	'CG',0
	.word	64197
	.byte	32,3,35,160,2,0,14
	.word	64202
	.byte	31
	.byte	'Ifx_SRC_GVADC',0,11,201,4,3
	.word	64271
	.byte	14
	.word	61584
	.byte	10
	.byte	'_Ifx_SRC_GXBAR',0,11,204,4,25,4,13
	.byte	'XBAR',0
	.word	64299
	.byte	4,2,35,0,0,14
	.word	64304
	.byte	31
	.byte	'Ifx_SRC_GXBAR',0,11,207,4,3
	.word	64340
	.byte	31
	.byte	'Ifx_STM_ACCEN0_Bits',0,13,79,3
	.word	12924
	.byte	31
	.byte	'Ifx_STM_ACCEN1_Bits',0,13,85,3
	.word	12835
	.byte	31
	.byte	'Ifx_STM_CAP_Bits',0,13,91,3
	.word	11365
	.byte	31
	.byte	'Ifx_STM_CAPSV_Bits',0,13,97,3
	.word	12242
	.byte	31
	.byte	'Ifx_STM_CLC_Bits',0,13,107,3
	.word	10488
	.byte	31
	.byte	'Ifx_STM_CMCON_Bits',0,13,120,3
	.word	11543
	.byte	31
	.byte	'Ifx_STM_CMP_Bits',0,13,126,3
	.word	11452
	.byte	31
	.byte	'Ifx_STM_ICR_Bits',0,13,139,1,3
	.word	11774
	.byte	31
	.byte	'Ifx_STM_ID_Bits',0,13,147,1,3
	.word	10644
	.byte	31
	.byte	'Ifx_STM_ISCR_Bits',0,13,157,1,3
	.word	11991
	.byte	31
	.byte	'Ifx_STM_KRST0_Bits',0,13,165,1,3
	.word	12712
	.byte	31
	.byte	'Ifx_STM_KRST1_Bits',0,13,172,1,3
	.word	12608
	.byte	31
	.byte	'Ifx_STM_KRSTCLR_Bits',0,13,179,1,3
	.word	12502
	.byte	31
	.byte	'Ifx_STM_OCS_Bits',0,13,189,1,3
	.word	12342
	.byte	31
	.byte	'Ifx_STM_TIM0_Bits',0,13,195,1,3
	.word	10766
	.byte	31
	.byte	'Ifx_STM_TIM0SV_Bits',0,13,201,1,3
	.word	12155
	.byte	31
	.byte	'Ifx_STM_TIM1_Bits',0,13,207,1,3
	.word	10851
	.byte	31
	.byte	'Ifx_STM_TIM2_Bits',0,13,213,1,3
	.word	10936
	.byte	31
	.byte	'Ifx_STM_TIM3_Bits',0,13,219,1,3
	.word	11021
	.byte	31
	.byte	'Ifx_STM_TIM4_Bits',0,13,225,1,3
	.word	11107
	.byte	31
	.byte	'Ifx_STM_TIM5_Bits',0,13,231,1,3
	.word	11193
	.byte	31
	.byte	'Ifx_STM_TIM6_Bits',0,13,237,1,3
	.word	11279
	.byte	31
	.byte	'Ifx_STM_ACCEN0',0,13,250,1,3
	.word	13453
	.byte	31
	.byte	'Ifx_STM_ACCEN1',0,13,130,2,3
	.word	12884
	.byte	31
	.byte	'Ifx_STM_CAP',0,13,138,2,3
	.word	11412
	.byte	31
	.byte	'Ifx_STM_CAPSV',0,13,146,2,3
	.word	12291
	.byte	31
	.byte	'Ifx_STM_CLC',0,13,154,2,3
	.word	10604
	.byte	31
	.byte	'Ifx_STM_CMCON',0,13,162,2,3
	.word	11734
	.byte	31
	.byte	'Ifx_STM_CMP',0,13,170,2,3
	.word	11494
	.byte	31
	.byte	'Ifx_STM_ICR',0,13,178,2,3
	.word	11951
	.byte	31
	.byte	'Ifx_STM_ID',0,13,186,2,3
	.word	10726
	.byte	31
	.byte	'Ifx_STM_ISCR',0,13,194,2,3
	.word	12115
	.byte	31
	.byte	'Ifx_STM_KRST0',0,13,202,2,3
	.word	12795
	.byte	31
	.byte	'Ifx_STM_KRST1',0,13,210,2,3
	.word	12672
	.byte	31
	.byte	'Ifx_STM_KRSTCLR',0,13,218,2,3
	.word	12568
	.byte	31
	.byte	'Ifx_STM_OCS',0,13,226,2,3
	.word	12462
	.byte	31
	.byte	'Ifx_STM_TIM0',0,13,234,2,3
	.word	10811
	.byte	31
	.byte	'Ifx_STM_TIM0SV',0,13,242,2,3
	.word	12202
	.byte	31
	.byte	'Ifx_STM_TIM1',0,13,250,2,3
	.word	10896
	.byte	31
	.byte	'Ifx_STM_TIM2',0,13,130,3,3
	.word	10981
	.byte	31
	.byte	'Ifx_STM_TIM3',0,13,138,3,3
	.word	11067
	.byte	31
	.byte	'Ifx_STM_TIM4',0,13,146,3,3
	.word	11153
	.byte	31
	.byte	'Ifx_STM_TIM5',0,13,154,3,3
	.word	11239
	.byte	31
	.byte	'Ifx_STM_TIM6',0,13,162,3,3
	.word	11325
	.byte	14
	.word	13493
	.byte	31
	.byte	'Ifx_STM',0,13,201,3,3
	.word	65451
	.byte	10
	.byte	'_Ifx_FLASH_ACCEN0_Bits',0,21,45,16,4,11
	.byte	'EN0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	642
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	642
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	642
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	642
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	642
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	642
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	642
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	642
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	642
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	642
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	642
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	642
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	642
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	642
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	642
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	642
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_FLASH_ACCEN0_Bits',0,21,79,3
	.word	65473
	.byte	10
	.byte	'_Ifx_FLASH_ACCEN1_Bits',0,21,82,16,4,11
	.byte	'reserved_0',0,4
	.word	467
	.byte	32,0,2,35,0,0,31
	.byte	'Ifx_FLASH_ACCEN1_Bits',0,21,85,3
	.word	66034
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_CFG_Bits',0,21,88,16,4,11
	.byte	'SEL',0,1
	.word	642
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	642
	.byte	2,0,2,35,0,11
	.byte	'CLR',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'DIS',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	467
	.byte	22,0,2,35,0,0,31
	.byte	'Ifx_FLASH_CBAB_CFG_Bits',0,21,95,3
	.word	66115
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_STAT_Bits',0,21,98,16,4,11
	.byte	'VLD0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'VLD1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'VLD2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'VLD3',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'VLD4',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'VLD5',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'VLD6',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'VLD7',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'VLD8',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'VLD9',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	467
	.byte	22,0,2,35,0,0,31
	.byte	'Ifx_FLASH_CBAB_STAT_Bits',0,21,111,3
	.word	66268
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_TOP_Bits',0,21,114,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	467
	.byte	19,8,2,35,0,11
	.byte	'ERR',0,1
	.word	642
	.byte	6,2,2,35,3,11
	.byte	'VLD',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'CLR',0,1
	.word	642
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_FLASH_CBAB_TOP_Bits',0,21,121,3
	.word	66516
	.byte	10
	.byte	'_Ifx_FLASH_COMM0_Bits',0,21,124,16,4,11
	.byte	'STATUS',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	467
	.byte	24,0,2,35,0,0,31
	.byte	'Ifx_FLASH_COMM0_Bits',0,21,128,1,3
	.word	66662
	.byte	10
	.byte	'_Ifx_FLASH_COMM1_Bits',0,21,131,1,16,4,11
	.byte	'STATUS',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'DATA',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	659
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_FLASH_COMM1_Bits',0,21,136,1,3
	.word	66760
	.byte	10
	.byte	'_Ifx_FLASH_COMM2_Bits',0,21,139,1,16,4,11
	.byte	'STATUS',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'DATA',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	659
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_FLASH_COMM2_Bits',0,21,144,1,3
	.word	66876
	.byte	10
	.byte	'_Ifx_FLASH_ECCRD_Bits',0,21,147,1,16,4,11
	.byte	'RCODE',0,4
	.word	467
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	659
	.byte	8,2,2,35,2,11
	.byte	'EDCERRINJ',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'ECCORDIS',0,1
	.word	642
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_FLASH_ECCRD_Bits',0,21,153,1,3
	.word	66992
	.byte	10
	.byte	'_Ifx_FLASH_ECCRP_Bits',0,21,156,1,16,4,11
	.byte	'RCODE',0,4
	.word	467
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	659
	.byte	8,2,2,35,2,11
	.byte	'EDCERRINJ',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'ECCORDIS',0,1
	.word	642
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_FLASH_ECCRP_Bits',0,21,162,1,3
	.word	67132
	.byte	10
	.byte	'_Ifx_FLASH_ECCW_Bits',0,21,165,1,16,4,11
	.byte	'WCODE',0,4
	.word	467
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	659
	.byte	8,2,2,35,2,11
	.byte	'DECENCDIS',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'PECENCDIS',0,1
	.word	642
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_FLASH_ECCW_Bits',0,21,171,1,3
	.word	67272
	.byte	10
	.byte	'_Ifx_FLASH_FCON_Bits',0,21,174,1,16,4,11
	.byte	'WSPFLASH',0,1
	.word	642
	.byte	4,4,2,35,0,11
	.byte	'WSECPF',0,1
	.word	642
	.byte	2,2,2,35,0,11
	.byte	'WSDFLASH',0,2
	.word	659
	.byte	6,4,2,35,0,11
	.byte	'WSECDF',0,1
	.word	642
	.byte	3,1,2,35,1,11
	.byte	'IDLE',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'ESLDIS',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'SLEEP',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'NSAFECC',0,1
	.word	642
	.byte	1,5,2,35,2,11
	.byte	'STALL',0,1
	.word	642
	.byte	1,4,2,35,2,11
	.byte	'RES21',0,1
	.word	642
	.byte	2,2,2,35,2,11
	.byte	'RES23',0,1
	.word	642
	.byte	2,0,2,35,2,11
	.byte	'VOPERM',0,1
	.word	642
	.byte	1,7,2,35,3,11
	.byte	'SQERM',0,1
	.word	642
	.byte	1,6,2,35,3,11
	.byte	'PROERM',0,1
	.word	642
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	642
	.byte	3,2,2,35,3,11
	.byte	'PR5V',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'EOBM',0,1
	.word	642
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_FLASH_FCON_Bits',0,21,193,1,3
	.word	67411
	.byte	10
	.byte	'_Ifx_FLASH_FPRO_Bits',0,21,196,1,16,4,11
	.byte	'PROINP',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'PRODISP',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'PROIND',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'PRODISD',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'PROINHSMCOTP',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'RES5',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'PROINOTP',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'RES7',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'PROINDBG',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'PRODISDBG',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'PROINHSM',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	642
	.byte	5,0,2,35,1,11
	.byte	'DCFP',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'DDFP',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'DDFPX',0,1
	.word	642
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	642
	.byte	1,4,2,35,2,11
	.byte	'DDFD',0,1
	.word	642
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	642
	.byte	1,2,2,35,2,11
	.byte	'ENPE',0,1
	.word	642
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	642
	.byte	8,0,2,35,3,0,31
	.byte	'Ifx_FLASH_FPRO_Bits',0,21,218,1,3
	.word	67773
	.byte	10
	.byte	'_Ifx_FLASH_FSR_Bits',0,21,221,1,16,4,11
	.byte	'FABUSY',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'D0BUSY',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'RES1',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'P0BUSY',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'P1BUSY',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'RES5',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'RES6',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'PROG',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'ERASE',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'PFPAGE',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'DFPAGE',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'OPER',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'SQER',0,1
	.word	642
	.byte	1,3,2,35,1,11
	.byte	'PROER',0,1
	.word	642
	.byte	1,2,2,35,1,11
	.byte	'PFSBER',0,1
	.word	642
	.byte	1,1,2,35,1,11
	.byte	'PFDBER',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'PFMBER',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'RES17',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'DFSBER',0,1
	.word	642
	.byte	1,5,2,35,2,11
	.byte	'DFDBER',0,1
	.word	642
	.byte	1,4,2,35,2,11
	.byte	'DFTBER',0,1
	.word	642
	.byte	1,3,2,35,2,11
	.byte	'DFMBER',0,1
	.word	642
	.byte	1,2,2,35,2,11
	.byte	'SRIADDERR',0,1
	.word	642
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	659
	.byte	2,7,2,35,2,11
	.byte	'PVER',0,1
	.word	642
	.byte	1,6,2,35,3,11
	.byte	'EVER',0,1
	.word	642
	.byte	1,5,2,35,3,11
	.byte	'SPND',0,1
	.word	642
	.byte	1,4,2,35,3,11
	.byte	'SLM',0,1
	.word	642
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	642
	.byte	1,2,2,35,3,11
	.byte	'ORIER',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	642
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_FLASH_FSR_Bits',0,21,254,1,3
	.word	68214
	.byte	10
	.byte	'_Ifx_FLASH_ID_Bits',0,21,129,2,16,4,11
	.byte	'MODREV',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	659
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_FLASH_ID_Bits',0,21,134,2,3
	.word	68820
	.byte	10
	.byte	'_Ifx_FLASH_MARD_Bits',0,21,137,2,16,4,11
	.byte	'HMARGIN',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'SELD0',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'SPND',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'SPNDERR',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,2
	.word	659
	.byte	10,1,2,35,0,11
	.byte	'TRAPDIS',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	659
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_FLASH_MARD_Bits',0,21,147,2,3
	.word	68931
	.byte	10
	.byte	'_Ifx_FLASH_MARP_Bits',0,21,150,2,16,4,11
	.byte	'SELP0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'SELP1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'RES2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'RES3',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	659
	.byte	11,1,2,35,0,11
	.byte	'TRAPDIS',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	659
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_FLASH_MARP_Bits',0,21,159,2,3
	.word	69145
	.byte	10
	.byte	'_Ifx_FLASH_PROCOND_Bits',0,21,162,2,16,4,11
	.byte	'L',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'NSAFECC',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'RAMIN',0,1
	.word	642
	.byte	2,4,2,35,0,11
	.byte	'RAMINSEL',0,1
	.word	642
	.byte	4,0,2,35,0,11
	.byte	'OSCCFG',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'MODE',0,1
	.word	642
	.byte	2,5,2,35,1,11
	.byte	'APREN',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'CAP0EN',0,1
	.word	642
	.byte	1,3,2,35,1,11
	.byte	'CAP1EN',0,1
	.word	642
	.byte	1,2,2,35,1,11
	.byte	'CAP2EN',0,1
	.word	642
	.byte	1,1,2,35,1,11
	.byte	'CAP3EN',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'ESR0CNT',0,2
	.word	659
	.byte	12,4,2,35,2,11
	.byte	'RES29',0,1
	.word	642
	.byte	2,2,2,35,3,11
	.byte	'RES30',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'RPRO',0,1
	.word	642
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_FLASH_PROCOND_Bits',0,21,179,2,3
	.word	69332
	.byte	10
	.byte	'_Ifx_FLASH_PROCONDBG_Bits',0,21,182,2,16,4,11
	.byte	'OCDSDIS',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'DBGIFLCK',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'EDM',0,1
	.word	642
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	467
	.byte	28,0,2,35,0,0,31
	.byte	'Ifx_FLASH_PROCONDBG_Bits',0,21,188,2,3
	.word	69656
	.byte	10
	.byte	'_Ifx_FLASH_PROCONHSM_Bits',0,21,191,2,16,4,11
	.byte	'HSMDBGDIS',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'DBGIFLCK',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'TSTIFLCK',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'HSMTSTDIS',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'RES15',0,2
	.word	659
	.byte	12,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	659
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_FLASH_PROCONHSM_Bits',0,21,199,2,3
	.word	69799
	.byte	10
	.byte	'_Ifx_FLASH_PROCONHSMCOTP_Bits',0,21,202,2,16,4,11
	.byte	'HSMBOOTEN',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'SSWWAIT',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'HSMDX',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'HSM6X',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'HSM16X',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'HSM17X',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'S6ROM',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'HSMENPINS',0,2
	.word	659
	.byte	2,7,2,35,0,11
	.byte	'HSMENRES',0,1
	.word	642
	.byte	2,5,2,35,1,11
	.byte	'DESTDBG',0,1
	.word	642
	.byte	2,3,2,35,1,11
	.byte	'BLKFLAN',0,1
	.word	642
	.byte	1,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	642
	.byte	2,0,2,35,1,11
	.byte	'S16ROM',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'S17ROM',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	659
	.byte	14,0,2,35,2,0,31
	.byte	'Ifx_FLASH_PROCONHSMCOTP_Bits',0,21,219,2,3
	.word	69988
	.byte	10
	.byte	'_Ifx_FLASH_PROCONOTP_Bits',0,21,222,2,16,4,11
	.byte	'S0ROM',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'S1ROM',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'S2ROM',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'S3ROM',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'S4ROM',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'S5ROM',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'S6ROM',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'S7ROM',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'S8ROM',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'S9ROM',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'S10ROM',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'S11ROM',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'S12ROM',0,1
	.word	642
	.byte	1,3,2,35,1,11
	.byte	'S13ROM',0,1
	.word	642
	.byte	1,2,2,35,1,11
	.byte	'S14ROM',0,1
	.word	642
	.byte	1,1,2,35,1,11
	.byte	'S15ROM',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'S16ROM',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'S17ROM',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'S18ROM',0,1
	.word	642
	.byte	1,5,2,35,2,11
	.byte	'S19ROM',0,1
	.word	642
	.byte	1,4,2,35,2,11
	.byte	'S20ROM',0,1
	.word	642
	.byte	1,3,2,35,2,11
	.byte	'S21ROM',0,1
	.word	642
	.byte	1,2,2,35,2,11
	.byte	'S22ROM',0,1
	.word	642
	.byte	1,1,2,35,2,11
	.byte	'S23ROM',0,1
	.word	642
	.byte	1,0,2,35,2,11
	.byte	'S24ROM',0,1
	.word	642
	.byte	1,7,2,35,3,11
	.byte	'S25ROM',0,1
	.word	642
	.byte	1,6,2,35,3,11
	.byte	'S26ROM',0,1
	.word	642
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	642
	.byte	2,3,2,35,3,11
	.byte	'BML',0,1
	.word	642
	.byte	2,1,2,35,3,11
	.byte	'TP',0,1
	.word	642
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_FLASH_PROCONOTP_Bits',0,21,254,2,3
	.word	70351
	.byte	10
	.byte	'_Ifx_FLASH_PROCONP_Bits',0,21,129,3,16,4,11
	.byte	'S0L',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'S1L',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'S2L',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'S3L',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'S4L',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'S5L',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'S6L',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'S7L',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'S8L',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'S9L',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'S10L',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'S11L',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'S12L',0,1
	.word	642
	.byte	1,3,2,35,1,11
	.byte	'S13L',0,1
	.word	642
	.byte	1,2,2,35,1,11
	.byte	'S14L',0,1
	.word	642
	.byte	1,1,2,35,1,11
	.byte	'S15L',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'S16L',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'S17L',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'S18L',0,1
	.word	642
	.byte	1,5,2,35,2,11
	.byte	'S19L',0,1
	.word	642
	.byte	1,4,2,35,2,11
	.byte	'S20L',0,1
	.word	642
	.byte	1,3,2,35,2,11
	.byte	'S21L',0,1
	.word	642
	.byte	1,2,2,35,2,11
	.byte	'S22L',0,1
	.word	642
	.byte	1,1,2,35,2,11
	.byte	'S23L',0,1
	.word	642
	.byte	1,0,2,35,2,11
	.byte	'S24L',0,1
	.word	642
	.byte	1,7,2,35,3,11
	.byte	'S25L',0,1
	.word	642
	.byte	1,6,2,35,3,11
	.byte	'S26L',0,1
	.word	642
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	642
	.byte	4,1,2,35,3,11
	.byte	'RPRO',0,1
	.word	642
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_FLASH_PROCONP_Bits',0,21,160,3,3
	.word	70946
	.byte	10
	.byte	'_Ifx_FLASH_PROCONWOP_Bits',0,21,163,3,16,4,11
	.byte	'S0WOP',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'S1WOP',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'S2WOP',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'S3WOP',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'S4WOP',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'S5WOP',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'S6WOP',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'S7WOP',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'S8WOP',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'S9WOP',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'S10WOP',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'S11WOP',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'S12WOP',0,1
	.word	642
	.byte	1,3,2,35,1,11
	.byte	'S13WOP',0,1
	.word	642
	.byte	1,2,2,35,1,11
	.byte	'S14WOP',0,1
	.word	642
	.byte	1,1,2,35,1,11
	.byte	'S15WOP',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'S16WOP',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'S17WOP',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'S18WOP',0,1
	.word	642
	.byte	1,5,2,35,2,11
	.byte	'S19WOP',0,1
	.word	642
	.byte	1,4,2,35,2,11
	.byte	'S20WOP',0,1
	.word	642
	.byte	1,3,2,35,2,11
	.byte	'S21WOP',0,1
	.word	642
	.byte	1,2,2,35,2,11
	.byte	'S22WOP',0,1
	.word	642
	.byte	1,1,2,35,2,11
	.byte	'S23WOP',0,1
	.word	642
	.byte	1,0,2,35,2,11
	.byte	'S24WOP',0,1
	.word	642
	.byte	1,7,2,35,3,11
	.byte	'S25WOP',0,1
	.word	642
	.byte	1,6,2,35,3,11
	.byte	'S26WOP',0,1
	.word	642
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	642
	.byte	4,1,2,35,3,11
	.byte	'DATM',0,1
	.word	642
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_FLASH_PROCONWOP_Bits',0,21,194,3,3
	.word	71470
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG0_Bits',0,21,197,3,16,4,11
	.byte	'TAG',0,1
	.word	642
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	467
	.byte	26,0,2,35,0,0,31
	.byte	'Ifx_FLASH_RDB_CFG0_Bits',0,21,201,3,3
	.word	72052
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG1_Bits',0,21,204,3,16,4,11
	.byte	'TAG',0,1
	.word	642
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	467
	.byte	26,0,2,35,0,0,31
	.byte	'Ifx_FLASH_RDB_CFG1_Bits',0,21,208,3,3
	.word	72154
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG2_Bits',0,21,211,3,16,4,11
	.byte	'TAG',0,1
	.word	642
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	467
	.byte	26,0,2,35,0,0,31
	.byte	'Ifx_FLASH_RDB_CFG2_Bits',0,21,215,3,3
	.word	72256
	.byte	10
	.byte	'_Ifx_FLASH_RRAD_Bits',0,21,218,3,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	3,5,2,35,0,11
	.byte	'ADD',0,4
	.word	467
	.byte	29,0,2,35,0,0,31
	.byte	'Ifx_FLASH_RRAD_Bits',0,21,222,3,3
	.word	72358
	.byte	10
	.byte	'_Ifx_FLASH_RRCT_Bits',0,21,225,3,16,4,11
	.byte	'STRT',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'STP',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'BUSY',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'DONE',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'ERR',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	642
	.byte	3,0,2,35,0,11
	.byte	'EOBM',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	642
	.byte	7,0,2,35,1,11
	.byte	'CNT',0,2
	.word	659
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_FLASH_RRCT_Bits',0,21,236,3,3
	.word	72452
	.byte	10
	.byte	'_Ifx_FLASH_RRD0_Bits',0,21,239,3,16,4,11
	.byte	'DATA',0,4
	.word	467
	.byte	32,0,2,35,0,0,31
	.byte	'Ifx_FLASH_RRD0_Bits',0,21,242,3,3
	.word	72662
	.byte	10
	.byte	'_Ifx_FLASH_RRD1_Bits',0,21,245,3,16,4,11
	.byte	'DATA',0,4
	.word	467
	.byte	32,0,2,35,0,0,31
	.byte	'Ifx_FLASH_RRD1_Bits',0,21,248,3,3
	.word	72735
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_CFG_Bits',0,21,251,3,16,4,11
	.byte	'SEL',0,1
	.word	642
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	642
	.byte	2,0,2,35,0,11
	.byte	'CLR',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'DIS',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	467
	.byte	22,0,2,35,0,0,31
	.byte	'Ifx_FLASH_UBAB_CFG_Bits',0,21,130,4,3
	.word	72808
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_STAT_Bits',0,21,133,4,16,4,11
	.byte	'VLD0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	467
	.byte	31,0,2,35,0,0,31
	.byte	'Ifx_FLASH_UBAB_STAT_Bits',0,21,137,4,3
	.word	72963
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_TOP_Bits',0,21,140,4,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	467
	.byte	19,8,2,35,0,11
	.byte	'ERR',0,1
	.word	642
	.byte	6,2,2,35,3,11
	.byte	'VLD',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'CLR',0,1
	.word	642
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_FLASH_UBAB_TOP_Bits',0,21,147,4,3
	.word	73068
	.byte	12,21,155,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65473
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_ACCEN0',0,21,160,4,3
	.word	73216
	.byte	12,21,163,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66034
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_ACCEN1',0,21,168,4,3
	.word	73282
	.byte	12,21,171,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66115
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_CBAB_CFG',0,21,176,4,3
	.word	73348
	.byte	12,21,179,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66268
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_CBAB_STAT',0,21,184,4,3
	.word	73416
	.byte	12,21,187,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66516
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_CBAB_TOP',0,21,192,4,3
	.word	73485
	.byte	12,21,195,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66662
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_COMM0',0,21,200,4,3
	.word	73553
	.byte	12,21,203,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66760
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_COMM1',0,21,208,4,3
	.word	73618
	.byte	12,21,211,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66876
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_COMM2',0,21,216,4,3
	.word	73683
	.byte	12,21,219,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66992
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_ECCRD',0,21,224,4,3
	.word	73748
	.byte	12,21,227,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67132
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_ECCRP',0,21,232,4,3
	.word	73813
	.byte	12,21,235,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67272
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_ECCW',0,21,240,4,3
	.word	73878
	.byte	12,21,243,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67411
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_FCON',0,21,248,4,3
	.word	73942
	.byte	12,21,251,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67773
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_FPRO',0,21,128,5,3
	.word	74006
	.byte	12,21,131,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68214
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_FSR',0,21,136,5,3
	.word	74070
	.byte	12,21,139,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68820
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_ID',0,21,144,5,3
	.word	74133
	.byte	12,21,147,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68931
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_MARD',0,21,152,5,3
	.word	74195
	.byte	12,21,155,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69145
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_MARP',0,21,160,5,3
	.word	74259
	.byte	12,21,163,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69332
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_PROCOND',0,21,168,5,3
	.word	74323
	.byte	12,21,171,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69656
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_PROCONDBG',0,21,176,5,3
	.word	74390
	.byte	12,21,179,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69799
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_PROCONHSM',0,21,184,5,3
	.word	74459
	.byte	12,21,187,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69988
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_PROCONHSMCOTP',0,21,192,5,3
	.word	74528
	.byte	12,21,195,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70351
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_PROCONOTP',0,21,200,5,3
	.word	74601
	.byte	12,21,203,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70946
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_PROCONP',0,21,208,5,3
	.word	74670
	.byte	12,21,211,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71470
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_PROCONWOP',0,21,216,5,3
	.word	74737
	.byte	12,21,219,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72052
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_RDB_CFG0',0,21,224,5,3
	.word	74806
	.byte	12,21,227,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72154
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_RDB_CFG1',0,21,232,5,3
	.word	74874
	.byte	12,21,235,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72256
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_RDB_CFG2',0,21,240,5,3
	.word	74942
	.byte	12,21,243,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72358
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_RRAD',0,21,248,5,3
	.word	75010
	.byte	12,21,251,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72452
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_RRCT',0,21,128,6,3
	.word	75074
	.byte	12,21,131,6,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72662
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_RRD0',0,21,136,6,3
	.word	75138
	.byte	12,21,139,6,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72735
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_RRD1',0,21,144,6,3
	.word	75202
	.byte	12,21,147,6,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72808
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_UBAB_CFG',0,21,152,6,3
	.word	75266
	.byte	12,21,155,6,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72963
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_UBAB_STAT',0,21,160,6,3
	.word	75334
	.byte	12,21,163,6,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	73068
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_UBAB_TOP',0,21,168,6,3
	.word	75403
	.byte	10
	.byte	'_Ifx_FLASH_CBAB',0,21,179,6,25,12,13
	.byte	'CFG',0
	.word	73348
	.byte	4,2,35,0,13
	.byte	'STAT',0
	.word	73416
	.byte	4,2,35,4,13
	.byte	'TOP',0
	.word	73485
	.byte	4,2,35,8,0,14
	.word	75471
	.byte	31
	.byte	'Ifx_FLASH_CBAB',0,21,184,6,3
	.word	75534
	.byte	10
	.byte	'_Ifx_FLASH_RDB',0,21,187,6,25,12,13
	.byte	'CFG0',0
	.word	74806
	.byte	4,2,35,0,13
	.byte	'CFG1',0
	.word	74874
	.byte	4,2,35,4,13
	.byte	'CFG2',0
	.word	74942
	.byte	4,2,35,8,0,14
	.word	75563
	.byte	31
	.byte	'Ifx_FLASH_RDB',0,21,192,6,3
	.word	75627
	.byte	10
	.byte	'_Ifx_FLASH_UBAB',0,21,195,6,25,12,13
	.byte	'CFG',0
	.word	75266
	.byte	4,2,35,0,13
	.byte	'STAT',0
	.word	75334
	.byte	4,2,35,4,13
	.byte	'TOP',0
	.word	75403
	.byte	4,2,35,8,0,14
	.word	75655
	.byte	31
	.byte	'Ifx_FLASH_UBAB',0,21,200,6,3
	.word	75718
	.byte	31
	.byte	'Ifx_P_ACCEN0_Bits',0,6,79,3
	.word	8032
	.byte	31
	.byte	'Ifx_P_ACCEN1_Bits',0,6,85,3
	.word	7945
	.byte	31
	.byte	'Ifx_P_ESR_Bits',0,6,107,3
	.word	4288
	.byte	31
	.byte	'Ifx_P_ID_Bits',0,6,115,3
	.word	2341
	.byte	31
	.byte	'Ifx_P_IN_Bits',0,6,137,1,3
	.word	3336
	.byte	31
	.byte	'Ifx_P_IOCR0_Bits',0,6,150,1,3
	.word	2469
	.byte	31
	.byte	'Ifx_P_IOCR12_Bits',0,6,163,1,3
	.word	3116
	.byte	31
	.byte	'Ifx_P_IOCR4_Bits',0,6,176,1,3
	.word	2684
	.byte	31
	.byte	'Ifx_P_IOCR8_Bits',0,6,189,1,3
	.word	2899
	.byte	31
	.byte	'Ifx_P_LPCR0_Bits',0,6,197,1,3
	.word	7304
	.byte	31
	.byte	'Ifx_P_LPCR1_Bits',0,6,205,1,3
	.word	7428
	.byte	31
	.byte	'Ifx_P_LPCR1_P21_Bits',0,6,215,1,3
	.word	7512
	.byte	31
	.byte	'Ifx_P_LPCR2_Bits',0,6,229,1,3
	.word	7692
	.byte	31
	.byte	'Ifx_P_OMCR0_Bits',0,6,240,1,3
	.word	5943
	.byte	31
	.byte	'Ifx_P_OMCR12_Bits',0,6,250,1,3
	.word	6467
	.byte	31
	.byte	'Ifx_P_OMCR4_Bits',0,6,133,2,3
	.word	6117
	.byte	31
	.byte	'Ifx_P_OMCR8_Bits',0,6,144,2,3
	.word	6291
	.byte	31
	.byte	'Ifx_P_OMCR_Bits',0,6,166,2,3
	.word	6956
	.byte	31
	.byte	'Ifx_P_OMR_Bits',0,6,203,2,3
	.word	1770
	.byte	31
	.byte	'Ifx_P_OMSR0_Bits',0,6,213,2,3
	.word	5280
	.byte	31
	.byte	'Ifx_P_OMSR12_Bits',0,6,224,2,3
	.word	5768
	.byte	31
	.byte	'Ifx_P_OMSR4_Bits',0,6,235,2,3
	.word	5427
	.byte	31
	.byte	'Ifx_P_OMSR8_Bits',0,6,246,2,3
	.word	5596
	.byte	31
	.byte	'Ifx_P_OMSR_Bits',0,6,140,3,3
	.word	6623
	.byte	31
	.byte	'Ifx_P_OUT_Bits',0,6,162,3,3
	.word	1454
	.byte	31
	.byte	'Ifx_P_PCSR_Bits',0,6,180,3,3
	.word	4994
	.byte	31
	.byte	'Ifx_P_PDISC_Bits',0,6,202,3,3
	.word	4628
	.byte	31
	.byte	'Ifx_P_PDR0_Bits',0,6,223,3,3
	.word	3659
	.byte	31
	.byte	'Ifx_P_PDR1_Bits',0,6,244,3,3
	.word	3963
	.byte	31
	.byte	'Ifx_P_ACCEN0',0,6,129,4,3
	.word	8559
	.byte	31
	.byte	'Ifx_P_ACCEN1',0,6,137,4,3
	.word	7992
	.byte	31
	.byte	'Ifx_P_ESR',0,6,145,4,3
	.word	4579
	.byte	31
	.byte	'Ifx_P_ID',0,6,153,4,3
	.word	2420
	.byte	31
	.byte	'Ifx_P_IN',0,6,161,4,3
	.word	3610
	.byte	31
	.byte	'Ifx_P_IOCR0',0,6,169,4,3
	.word	2644
	.byte	31
	.byte	'Ifx_P_IOCR12',0,6,177,4,3
	.word	3296
	.byte	31
	.byte	'Ifx_P_IOCR4',0,6,185,4,3
	.word	2859
	.byte	31
	.byte	'Ifx_P_IOCR8',0,6,193,4,3
	.word	3076
	.byte	31
	.byte	'Ifx_P_LPCR0',0,6,201,4,3
	.word	7388
	.byte	31
	.byte	'Ifx_P_LPCR1',0,6,210,4,3
	.word	7637
	.byte	31
	.byte	'Ifx_P_LPCR2',0,6,218,4,3
	.word	7896
	.byte	31
	.byte	'Ifx_P_OMCR',0,6,226,4,3
	.word	7264
	.byte	31
	.byte	'Ifx_P_OMCR0',0,6,234,4,3
	.word	6077
	.byte	31
	.byte	'Ifx_P_OMCR12',0,6,242,4,3
	.word	6583
	.byte	31
	.byte	'Ifx_P_OMCR4',0,6,250,4,3
	.word	6251
	.byte	31
	.byte	'Ifx_P_OMCR8',0,6,130,5,3
	.word	6427
	.byte	31
	.byte	'Ifx_P_OMR',0,6,138,5,3
	.word	2301
	.byte	31
	.byte	'Ifx_P_OMSR',0,6,146,5,3
	.word	6916
	.byte	31
	.byte	'Ifx_P_OMSR0',0,6,154,5,3
	.word	5387
	.byte	31
	.byte	'Ifx_P_OMSR12',0,6,162,5,3
	.word	5903
	.byte	31
	.byte	'Ifx_P_OMSR4',0,6,170,5,3
	.word	5556
	.byte	31
	.byte	'Ifx_P_OMSR8',0,6,178,5,3
	.word	5728
	.byte	31
	.byte	'Ifx_P_OUT',0,6,186,5,3
	.word	1730
	.byte	31
	.byte	'Ifx_P_PCSR',0,6,194,5,3
	.word	5240
	.byte	31
	.byte	'Ifx_P_PDISC',0,6,202,5,3
	.word	4954
	.byte	31
	.byte	'Ifx_P_PDR0',0,6,210,5,3
	.word	3923
	.byte	31
	.byte	'Ifx_P_PDR1',0,6,218,5,3
	.word	4239
	.byte	14
	.word	8599
	.byte	31
	.byte	'Ifx_P',0,6,139,6,3
	.word	77065
	.byte	31
	.byte	'IfxPort_InputMode',0,5,89,3
	.word	9212
	.byte	17,5,120,9,1,18
	.byte	'IfxPort_OutputIdx_general',0,128,1,18
	.byte	'IfxPort_OutputIdx_alt1',0,136,1,18
	.byte	'IfxPort_OutputIdx_alt2',0,144,1,18
	.byte	'IfxPort_OutputIdx_alt3',0,152,1,18
	.byte	'IfxPort_OutputIdx_alt4',0,160,1,18
	.byte	'IfxPort_OutputIdx_alt5',0,168,1,18
	.byte	'IfxPort_OutputIdx_alt6',0,176,1,18
	.byte	'IfxPort_OutputIdx_alt7',0,184,1,0,31
	.byte	'IfxPort_OutputIdx',0,5,130,1,3
	.word	77111
	.byte	17,5,134,1,9,1,18
	.byte	'IfxPort_OutputMode_pushPull',0,128,1,18
	.byte	'IfxPort_OutputMode_openDrain',0,192,1,0,31
	.byte	'IfxPort_OutputMode',0,5,138,1,3
	.word	77355
	.byte	17,5,144,1,9,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed1',0,0,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed2',0,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed3',0,2,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed4',0,3,18
	.byte	'IfxPort_PadDriver_lvdsSpeed1',0,4,18
	.byte	'IfxPort_PadDriver_lvdsSpeed2',0,5,18
	.byte	'IfxPort_PadDriver_lvdsSpeed3',0,6,18
	.byte	'IfxPort_PadDriver_lvdsSpeed4',0,7,18
	.byte	'IfxPort_PadDriver_ttlSpeed1',0,8,18
	.byte	'IfxPort_PadDriver_ttlSpeed2',0,9,18
	.byte	'IfxPort_PadDriver_ttlSpeed3',0,10,18
	.byte	'IfxPort_PadDriver_ttlSpeed4',0,11,0,31
	.byte	'IfxPort_PadDriver',0,5,158,1,3
	.word	77453
	.byte	31
	.byte	'IfxPort_State',0,5,178,1,3
	.word	9417
	.byte	33,5,190,1,9,8,13
	.byte	'port',0
	.word	9207
	.byte	4,2,35,0,13
	.byte	'pinIndex',0
	.word	642
	.byte	1,2,35,4,0,31
	.byte	'IfxPort_Pin',0,5,194,1,3
	.word	77918
	.byte	14
	.word	39731
	.byte	3
	.word	77978
	.byte	33,22,74,15,20,13
	.byte	'module',0
	.word	77983
	.byte	4,2,35,0,13
	.byte	'channelId',0
	.word	642
	.byte	1,2,35,4,13
	.byte	'pin',0
	.word	77918
	.byte	8,2,35,8,13
	.byte	'select',0
	.word	41967
	.byte	1,2,35,16,0,26
	.word	77988
	.byte	31
	.byte	'IfxScu_Req_In',0,22,80,3
	.word	78058
	.byte	31
	.byte	'IfxScuCcu_PllStepsFunctionHook',0,7,148,1,16
	.word	206
	.byte	33,7,212,5,9,8,13
	.byte	'value',0
	.word	9875
	.byte	4,2,35,0,13
	.byte	'mask',0
	.word	9875
	.byte	4,2,35,4,0,31
	.byte	'IfxScuCcu_CcuconRegConfig',0,7,216,5,3
	.word	78125
	.byte	33,7,221,5,9,8,13
	.byte	'pDivider',0
	.word	642
	.byte	1,2,35,0,13
	.byte	'nDivider',0
	.word	642
	.byte	1,2,35,1,13
	.byte	'k2Initial',0
	.word	642
	.byte	1,2,35,2,13
	.byte	'waitTime',0
	.word	263
	.byte	4,2,35,4,0,31
	.byte	'IfxScuCcu_InitialStepConfig',0,7,227,5,3
	.word	78196
	.byte	33,7,231,5,9,12,13
	.byte	'k2Step',0
	.word	642
	.byte	1,2,35,0,13
	.byte	'waitTime',0
	.word	263
	.byte	4,2,35,2,13
	.byte	'hookFunction',0
	.word	78085
	.byte	4,2,35,8,0,31
	.byte	'IfxScuCcu_PllStepsConfig',0,7,236,5,3
	.word	78313
	.byte	3
	.word	203
	.byte	33,7,244,5,9,48,13
	.byte	'ccucon0',0
	.word	78125
	.byte	8,2,35,0,13
	.byte	'ccucon1',0
	.word	78125
	.byte	8,2,35,8,13
	.byte	'ccucon2',0
	.word	78125
	.byte	8,2,35,16,13
	.byte	'ccucon5',0
	.word	78125
	.byte	8,2,35,24,13
	.byte	'ccucon6',0
	.word	78125
	.byte	8,2,35,32,13
	.byte	'ccucon7',0
	.word	78125
	.byte	8,2,35,40,0,31
	.byte	'IfxScuCcu_ClockDistributionConfig',0,7,252,5,3
	.word	78415
	.byte	33,7,128,6,9,8,13
	.byte	'value',0
	.word	9875
	.byte	4,2,35,0,13
	.byte	'mask',0
	.word	9875
	.byte	4,2,35,4,0,31
	.byte	'IfxScuCcu_FlashWaitstateConfig',0,7,132,6,3
	.word	78567
	.byte	3
	.word	78313
	.byte	33,7,137,6,9,16,13
	.byte	'numOfPllDividerSteps',0
	.word	642
	.byte	1,2,35,0,13
	.byte	'pllDividerStep',0
	.word	78643
	.byte	4,2,35,4,13
	.byte	'pllInitialStep',0
	.word	78196
	.byte	8,2,35,8,0,31
	.byte	'IfxScuCcu_SysPllConfig',0,7,142,6,3
	.word	78648
	.byte	17,8,144,1,9,1,18
	.byte	'IfxCpu_CounterMode_normal',0,0,18
	.byte	'IfxCpu_CounterMode_task',0,1,0,31
	.byte	'IfxCpu_CounterMode',0,8,148,1,3
	.word	78765
	.byte	33,8,160,1,9,6,13
	.byte	'counter',0
	.word	9875
	.byte	4,2,35,0,13
	.byte	'overlfow',0
	.word	642
	.byte	1,2,35,4,0,31
	.byte	'IfxCpu_Counter',0,8,164,1,3
	.word	78854
	.byte	33,8,172,1,9,32,13
	.byte	'instruction',0
	.word	78854
	.byte	6,2,35,0,13
	.byte	'clock',0
	.word	78854
	.byte	6,2,35,6,13
	.byte	'counter1',0
	.word	78854
	.byte	6,2,35,12,13
	.byte	'counter2',0
	.word	78854
	.byte	6,2,35,18,13
	.byte	'counter3',0
	.word	78854
	.byte	6,2,35,24,0,31
	.byte	'IfxCpu_Perf',0,8,179,1,3
	.word	78920
	.byte	17,23,69,9,1,18
	.byte	'IfxSrc_Tos_cpu0',0,0,18
	.byte	'IfxSrc_Tos_cpu1',0,1,18
	.byte	'IfxSrc_Tos_dma',0,3,0,31
	.byte	'IfxSrc_Tos',0,23,74,3
	.word	79038
	.byte	17,12,151,1,9,1,18
	.byte	'IfxStm_Comparator_0',0,0,18
	.byte	'IfxStm_Comparator_1',0,1,0,31
	.byte	'IfxStm_Comparator',0,12,155,1,3
	.word	79116
	.byte	17,12,159,1,9,1,18
	.byte	'IfxStm_ComparatorInterrupt_ir0',0,0,18
	.byte	'IfxStm_ComparatorInterrupt_ir1',0,1,0,31
	.byte	'IfxStm_ComparatorInterrupt',0,12,163,1,3
	.word	79194
	.byte	17,12,167,1,9,1,18
	.byte	'IfxStm_ComparatorOffset_0',0,0,18
	.byte	'IfxStm_ComparatorOffset_1',0,1,18
	.byte	'IfxStm_ComparatorOffset_2',0,2,18
	.byte	'IfxStm_ComparatorOffset_3',0,3,18
	.byte	'IfxStm_ComparatorOffset_4',0,4,18
	.byte	'IfxStm_ComparatorOffset_5',0,5,18
	.byte	'IfxStm_ComparatorOffset_6',0,6,18
	.byte	'IfxStm_ComparatorOffset_7',0,7,18
	.byte	'IfxStm_ComparatorOffset_8',0,8,18
	.byte	'IfxStm_ComparatorOffset_9',0,9,18
	.byte	'IfxStm_ComparatorOffset_10',0,10,18
	.byte	'IfxStm_ComparatorOffset_11',0,11,18
	.byte	'IfxStm_ComparatorOffset_12',0,12,18
	.byte	'IfxStm_ComparatorOffset_13',0,13,18
	.byte	'IfxStm_ComparatorOffset_14',0,14,18
	.byte	'IfxStm_ComparatorOffset_15',0,15,18
	.byte	'IfxStm_ComparatorOffset_16',0,16,18
	.byte	'IfxStm_ComparatorOffset_17',0,17,18
	.byte	'IfxStm_ComparatorOffset_18',0,18,18
	.byte	'IfxStm_ComparatorOffset_19',0,19,18
	.byte	'IfxStm_ComparatorOffset_20',0,20,18
	.byte	'IfxStm_ComparatorOffset_21',0,21,18
	.byte	'IfxStm_ComparatorOffset_22',0,22,18
	.byte	'IfxStm_ComparatorOffset_23',0,23,18
	.byte	'IfxStm_ComparatorOffset_24',0,24,18
	.byte	'IfxStm_ComparatorOffset_25',0,25,18
	.byte	'IfxStm_ComparatorOffset_26',0,26,18
	.byte	'IfxStm_ComparatorOffset_27',0,27,18
	.byte	'IfxStm_ComparatorOffset_28',0,28,18
	.byte	'IfxStm_ComparatorOffset_29',0,29,18
	.byte	'IfxStm_ComparatorOffset_30',0,30,18
	.byte	'IfxStm_ComparatorOffset_31',0,31,0,31
	.byte	'IfxStm_ComparatorOffset',0,12,201,1,3
	.word	79303
	.byte	17,12,205,1,9,1,18
	.byte	'IfxStm_ComparatorSize_1Bit',0,0,18
	.byte	'IfxStm_ComparatorSize_2Bits',0,1,18
	.byte	'IfxStm_ComparatorSize_3Bits',0,2,18
	.byte	'IfxStm_ComparatorSize_4Bits',0,3,18
	.byte	'IfxStm_ComparatorSize_5Bits',0,4,18
	.byte	'IfxStm_ComparatorSize_6Bits',0,5,18
	.byte	'IfxStm_ComparatorSize_7Bits',0,6,18
	.byte	'IfxStm_ComparatorSize_8Bits',0,7,18
	.byte	'IfxStm_ComparatorSize_9Bits',0,8,18
	.byte	'IfxStm_ComparatorSize_10Bits',0,9,18
	.byte	'IfxStm_ComparatorSize_11Bits',0,10,18
	.byte	'IfxStm_ComparatorSize_12Bits',0,11,18
	.byte	'IfxStm_ComparatorSize_13Bits',0,12,18
	.byte	'IfxStm_ComparatorSize_14Bits',0,13,18
	.byte	'IfxStm_ComparatorSize_15Bits',0,14,18
	.byte	'IfxStm_ComparatorSize_16Bits',0,15,18
	.byte	'IfxStm_ComparatorSize_17Bits',0,16,18
	.byte	'IfxStm_ComparatorSize_18Bits',0,17,18
	.byte	'IfxStm_ComparatorSize_19Bits',0,18,18
	.byte	'IfxStm_ComparatorSize_20Bits',0,19,18
	.byte	'IfxStm_ComparatorSize_21Bits',0,20,18
	.byte	'IfxStm_ComparatorSize_22Bits',0,21,18
	.byte	'IfxStm_ComparatorSize_23Bits',0,22,18
	.byte	'IfxStm_ComparatorSize_24Bits',0,23,18
	.byte	'IfxStm_ComparatorSize_25Bits',0,24,18
	.byte	'IfxStm_ComparatorSize_26Bits',0,25,18
	.byte	'IfxStm_ComparatorSize_27Bits',0,26,18
	.byte	'IfxStm_ComparatorSize_28Bits',0,27,18
	.byte	'IfxStm_ComparatorSize_29Bits',0,28,18
	.byte	'IfxStm_ComparatorSize_30Bits',0,29,18
	.byte	'IfxStm_ComparatorSize_31Bits',0,30,18
	.byte	'IfxStm_ComparatorSize_32Bits',0,31,0,31
	.byte	'IfxStm_ComparatorSize',0,12,239,1,3
	.word	80261
	.byte	17,12,244,1,9,1,18
	.byte	'IfxStm_SleepMode_enable',0,0,18
	.byte	'IfxStm_SleepMode_disable',0,1,0,31
	.byte	'IfxStm_SleepMode',0,12,248,1,3
	.word	81281
	.byte	17,12,252,1,9,1,18
	.byte	'IfxStm_SuspendMode_none',0,0,18
	.byte	'IfxStm_SuspendMode_hard',0,1,18
	.byte	'IfxStm_SuspendMode_soft',0,2,0,31
	.byte	'IfxStm_SuspendMode',0,12,129,2,3
	.word	81367
	.byte	33,24,65,9,16,13
	.byte	'sysFreq',0
	.word	263
	.byte	4,2,35,0,13
	.byte	'cpuFreq',0
	.word	263
	.byte	4,2,35,4,13
	.byte	'pllFreq',0
	.word	263
	.byte	4,2,35,8,13
	.byte	'stmFreq',0
	.word	263
	.byte	4,2,35,12,0,31
	.byte	'AppInfo',0,24,71,3
	.word	81480
.L87:
	.byte	33,24,74,9,16,13
	.byte	'info',0
	.word	81480
	.byte	16,2,35,0,0,31
	.byte	'App_Cpu0',0,24,77,3
	.word	81570
	.byte	31
	.byte	'_iob_flag_t',0,25,82,25
	.word	659
	.byte	7
	.byte	'char',0,1,6,31
	.byte	'int8',0,26,54,29
	.word	81627
	.byte	31
	.byte	'int16',0,26,55,29
	.word	15084
	.byte	31
	.byte	'int32',0,26,56,29
	.word	483
	.byte	31
	.byte	'int64',0,26,57,29
	.word	41861
	.byte	14
	.word	642
	.byte	31
	.byte	'vuint8',0,26,59,29
	.word	81690
	.byte	15,2
	.word	642
	.byte	16,1,0
.L88:
	.byte	14
	.word	81710
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L26:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,21,0,54,15,39,12,0,0,3,15,0,73,19,0,0,4,46,1,3,8,32,13
	.byte	58,15,59,15,57,15,54,15,39,12,0,0,5,5,0,3,8,58,15,59,15,57,15,73,19,0,0,6,11,0,0,0,7,36,0,3,8,11,15,62
	.byte	15,0,0,8,46,1,3,8,32,13,58,15,59,15,57,15,73,19,54,15,39,12,0,0,9,59,0,3,8,0,0,10,19,1,3,8,58,15,59,15
	.byte	57,15,11,15,0,0,11,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,12,23,1,58,15,59,15,57,15,11,15,0,0,13,13
	.byte	0,3,8,73,19,11,15,56,9,0,0,14,53,0,73,19,0,0,15,1,1,11,15,73,19,0,0,16,33,0,47,15,0,0,17,4,1,58,15,59
	.byte	15,57,15,11,15,0,0,18,40,0,3,8,28,13,0,0,19,11,1,0,0,20,46,1,3,8,73,19,54,15,39,12,63,12,60,12,0,0,21
	.byte	5,0,73,19,0,0,22,46,1,49,19,0,0,23,5,0,49,19,0,0,24,46,1,3,8,58,15,59,15,57,15,54,15,39,12,63,12,60,12
	.byte	0,0,25,46,0,3,8,58,15,59,15,57,15,73,19,54,15,39,12,63,12,60,12,0,0,26,38,0,73,19,0,0,27,46,1,3,8,58,15
	.byte	59,15,57,15,73,19,54,15,39,12,63,12,60,12,0,0,28,29,1,49,19,0,0,29,11,0,49,19,0,0,30,46,0,3,8,58,15,59
	.byte	15,57,15,54,15,39,12,63,12,60,12,0,0,31,22,0,3,8,58,15,59,15,57,15,73,19,0,0,32,21,0,54,15,0,0,33,19,1
	.byte	58,15,59,15,57,15,11,15,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L27:
	.word	.L100-.L99
.L99:
	.half	3
	.word	.L102-.L101
.L101:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user',0
	.byte	'F:\\ADS\\AURIX-Studio-1.10.2\\tools\\Compilers\\Tasking_1.1r8\\ctc\\include\\',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common',0
	.byte	0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_Intrinsics.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Scu\\Std\\IfxScuWdt.h',0
	.byte	0,0,0
	.byte	'IfxScu_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Port\\Std\\IfxPort.h',0
	.byte	0,0,0
	.byte	'IfxPort_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Scu\\Std\\IfxScuCcu.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxCpu_cfg.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Src\\Std\\IfxSrc.h',0
	.byte	0,0,0
	.byte	'IfxSrc_regdef.h',0,1,0,0
	.byte	'Ifxstm.h',0,2,0,0
	.byte	'IfxStm_regdef.h',0,1,0,0
	.byte	'zf_driver_delay.h',0,3,0,0
	.byte	'..\\libraries\\zf_common\\zf_common_interrupt.h',0,0,0,0
	.byte	'IfxCpu_regdef.h',0,1,0,0
	.byte	'../libraries/zf_common/zf_common_clock.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxScu_cfg.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Platform_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Ifx_Types.h',0
	.byte	0,0,0
	.byte	'IfxFlash_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_PinMap\\IfxScu_PinMap.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxSrc_cfg.h',0
	.byte	0,0,0
	.byte	'Cpu0_Main.h',0,4,0,0
	.byte	'stdio.h',0,5,0,0
	.byte	'zf_common_typedef.h',0,6,0,0,0
.L102:
.L100:
	.sdecl	'.debug_info',debug,cluster('set_clock')
	.sect	'.debug_info'
.L28:
	.word	248
	.half	3
	.word	.L29
	.byte	4,1
	.byte	'../libraries/zf_common/zf_common_clock.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L31,.L30
	.byte	2
	.word	.L24
	.byte	3
	.byte	'set_clock',0,1,56,6,1,1,1
	.word	.L15,.L57,.L14
	.byte	4
	.word	.L15,.L57
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('set_clock')
	.sect	'.debug_abbrev'
.L29:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('set_clock')
	.sect	'.debug_line'
.L30:
	.word	.L104-.L103
.L103:
	.half	3
	.word	.L106-.L105
.L105:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_common/zf_common_clock.c',0,0,0,0,0
.L106:
	.byte	5,31,7,0,5,2
	.word	.L15
	.byte	3,57,1,5,53,9
	.half	.L107-.L15
	.byte	1,5,31,9
	.half	.L108-.L107
	.byte	3,1,1,5,53,9
	.half	.L109-.L108
	.byte	1,5,1,9
	.half	.L110-.L109
	.byte	3,1,1,7,9
	.half	.L32-.L110
	.byte	0,1,1
.L104:
	.sdecl	'.debug_ranges',debug,cluster('set_clock')
	.sect	'.debug_ranges'
.L31:
	.word	-1,.L15,0,.L32-.L15,0,0
	.sdecl	'.debug_info',debug,cluster('get_clock')
	.sect	'.debug_info'
.L33:
	.word	411
	.half	3
	.word	.L34
	.byte	4,1
	.byte	'../libraries/zf_common/zf_common_clock.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L36,.L35
	.byte	2
	.word	.L24
	.byte	3
	.byte	'get_clock',0,1,68,6,1,1,1
	.word	.L17,.L58,.L16
	.byte	4
	.word	.L17,.L58
	.byte	5
	.word	.L59,.L60,.L2
	.byte	6
	.word	.L61,.L60,.L2
	.byte	7
	.byte	'reg',0,2,143,6,21
	.word	.L62,.L63
	.byte	0,0,5
	.word	.L64,.L65,.L5
	.byte	8
	.word	.L66,.L67
	.byte	9
	.word	.L68,.L69
	.byte	6
	.word	.L72,.L3,.L5
	.byte	7
	.byte	'result',0,3,182,4,13
	.word	.L73,.L74
	.byte	5
	.word	.L75,.L3,.L4
	.byte	10
	.word	.L76,.L3,.L4
	.byte	0,0,0,0,5
	.word	.L64,.L70,.L71
	.byte	8
	.word	.L66,.L67
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('get_clock')
	.sect	'.debug_abbrev'
.L34:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,29,1,49,16,17,1,18,1,0,0,6,11,1,49,16,17,1,18,1
	.byte	0,0,7,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,8,5,0,49,16,2,6,0,0,9,11,1,49,16,85,6,0,0,10,11,0,49,16
	.byte	17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('get_clock')
	.sect	'.debug_line'
.L35:
	.word	.L112-.L111
.L111:
	.half	3
	.word	.L114-.L113
.L113:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std',0
	.byte	0
	.byte	'../libraries/zf_common/zf_common_clock.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu.h',0
	.byte	0,0,0
	.byte	'Ifxstm.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Scu\\Std\\IfxScuCcu.h',0
	.byte	0,0,0,0
.L114:
	.byte	5,5,7,0,5,2
	.word	.L17
	.byte	3,198,0,1,5,55,9
	.half	.L115-.L17
	.byte	1,5,28,9
	.half	.L116-.L115
	.byte	1,4,2,5,19,9
	.half	.L60-.L116
	.byte	3,201,5,1,5,37,9
	.half	.L89-.L60
	.byte	3,1,1,5,5,9
	.half	.L117-.L89
	.byte	1,4,1,9
	.half	.L2-.L117
	.byte	3,183,122,1,5,75,9
	.half	.L118-.L2
	.byte	1,5,28,9
	.half	.L119-.L118
	.byte	1,5,5,9
	.half	.L120-.L119
	.byte	3,1,1,5,55,9
	.half	.L121-.L120
	.byte	1,5,28,9
	.half	.L122-.L121
	.byte	1,5,51,9
	.half	.L123-.L122
	.byte	3,1,1,4,3,5,5,9
	.half	.L65-.L123
	.byte	3,235,3,1,4,4,5,40,7,9
	.half	.L3-.L65
	.byte	3,175,4,1,5,58,9
	.half	.L124-.L3
	.byte	1,5,43,9
	.half	.L125-.L124
	.byte	1,5,5,9
	.half	.L126-.L125
	.byte	1,4,3,9
	.half	.L4-.L126
	.byte	3,214,123,1,4,1,9
	.half	.L5-.L4
	.byte	3,144,124,1,5,28,9
	.half	.L70-.L5
	.byte	1,5,1,9
	.half	.L71-.L70
	.byte	3,1,1,7,9
	.half	.L37-.L71
	.byte	0,1,1
.L112:
	.sdecl	'.debug_ranges',debug,cluster('get_clock')
	.sect	'.debug_ranges'
.L36:
	.word	-1,.L17,0,.L37-.L17,0,0
.L69:
	.word	-1,.L17,.L65-.L17,.L5-.L17,.L70-.L17,.L71-.L17,0,0
	.sdecl	'.debug_info',debug,cluster('disable_Watchdog')
	.sect	'.debug_info'
.L38:
	.word	255
	.half	3
	.word	.L39
	.byte	4,1
	.byte	'../libraries/zf_common/zf_common_clock.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L41,.L40
	.byte	2
	.word	.L24
	.byte	3
	.byte	'disable_Watchdog',0,1,83,6,1,1,1
	.word	.L19,.L77,.L18
	.byte	4
	.word	.L19,.L77
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('disable_Watchdog')
	.sect	'.debug_abbrev'
.L39:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('disable_Watchdog')
	.sect	'.debug_line'
.L40:
	.word	.L128-.L127
.L127:
	.half	3
	.word	.L130-.L129
.L129:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_common/zf_common_clock.c',0,0,0,0,0
.L130:
	.byte	5,66,7,0,5,2
	.word	.L19
	.byte	3,212,0,1,5,72,9
	.half	.L131-.L19
	.byte	3,1,1,5,1,9
	.half	.L132-.L131
	.byte	3,1,1,7,9
	.half	.L42-.L132
	.byte	0,1,1
.L128:
	.sdecl	'.debug_ranges',debug,cluster('disable_Watchdog')
	.sect	'.debug_ranges'
.L41:
	.word	-1,.L19,0,.L42-.L19,0,0
	.sdecl	'.debug_info',debug,cluster('clock_init')
	.sect	'.debug_info'
.L43:
	.word	249
	.half	3
	.word	.L44
	.byte	4,1
	.byte	'../libraries/zf_common/zf_common_clock.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L46,.L45
	.byte	2
	.word	.L24
	.byte	3
	.byte	'clock_init',0,1,95,6,1,1,1
	.word	.L21,.L78,.L20
	.byte	4
	.word	.L21,.L78
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('clock_init')
	.sect	'.debug_abbrev'
.L44:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('clock_init')
	.sect	'.debug_line'
.L45:
	.word	.L134-.L133
.L133:
	.half	3
	.word	.L136-.L135
.L135:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_common/zf_common_clock.c',0,0,0,0,0
.L136:
	.byte	5,29,7,0,5,2
	.word	.L21
	.byte	3,224,0,1,5,21,9
	.half	.L137-.L21
	.byte	3,1,1,5,14,9
	.half	.L138-.L137
	.byte	3,1,1,5,22,9
	.half	.L139-.L138
	.byte	3,1,1,5,29,9
	.half	.L140-.L139
	.byte	3,1,1,5,1,9
	.half	.L141-.L140
	.byte	3,1,1,7,9
	.half	.L47-.L141
	.byte	0,1,1
.L134:
	.sdecl	'.debug_ranges',debug,cluster('clock_init')
	.sect	'.debug_ranges'
.L46:
	.word	-1,.L21,0,.L47-.L21,0,0
	.sdecl	'.debug_info',debug,cluster('cpu_wait_event_ready')
	.sect	'.debug_info'
.L48:
	.word	396
	.half	3
	.word	.L49
	.byte	4,1
	.byte	'../libraries/zf_common/zf_common_clock.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L51,.L50
	.byte	2
	.word	.L24
	.byte	3
	.byte	'cpu_wait_event_ready',0,1,111,6,1,1,1
	.word	.L23,.L79,.L22
	.byte	4
	.word	.L23,.L79
	.byte	5
	.byte	'i',0,1,113,11
	.word	.L80,.L81
	.byte	5
	.byte	'all_cpu_init_finish',0,1,114,11
	.word	.L80,.L82
	.byte	6
	.word	.L83,.L23,.L6
	.byte	7
	.word	.L84,.L23,.L6
	.byte	5
	.byte	'reg',0,2,135,6,21
	.word	.L62,.L85
	.byte	0,0,6
	.word	.L83,.L7,.L10
	.byte	7
	.word	.L84,.L7,.L10
	.byte	5
	.byte	'reg',0,2,135,6,21
	.word	.L62,.L86
	.byte	0,0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('cpu_wait_event_ready')
	.sect	'.debug_abbrev'
.L49:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,6,29,1
	.byte	49,16,17,1,18,1,0,0,7,11,1,49,16,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('cpu_wait_event_ready')
	.sect	'.debug_line'
.L50:
	.word	.L143-.L142
.L142:
	.half	3
	.word	.L145-.L144
.L144:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_common/zf_common_clock.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu.h',0
	.byte	0,0,0,0
.L145:
	.byte	4,2,5,19,7,0,5,2
	.word	.L23
	.byte	3,135,6,1,5,28,9
	.half	.L91-.L23
	.byte	3,1,1,5,5,9
	.half	.L92-.L91
	.byte	1,4,1,9
	.half	.L6-.L92
	.byte	3,235,122,1,5,38,7,9
	.half	.L146-.L6
	.byte	3,2,1,5,15,9
	.half	.L8-.L146
	.byte	1,5,30,9
	.half	.L147-.L8
	.byte	1,5,38,9
	.half	.L148-.L147
	.byte	1,4,2,5,19,7,9
	.half	.L7-.L148
	.byte	3,146,5,1,5,28,9
	.half	.L93-.L7
	.byte	3,1,1,5,5,9
	.half	.L94-.L93
	.byte	1,4,1,9
	.half	.L10-.L94
	.byte	3,241,122,1,5,20,9
	.half	.L149-.L10
	.byte	1,5,43,9
	.half	.L150-.L149
	.byte	1,5,41,9
	.half	.L151-.L150
	.byte	1,5,29,9
	.half	.L11-.L151
	.byte	3,5,1,5,15,9
	.half	.L95-.L11
	.byte	3,1,1,5,38,9
	.half	.L97-.L95
	.byte	1,5,36,9
	.half	.L13-.L97
	.byte	3,2,1,5,51,9
	.half	.L152-.L13
	.byte	1,5,33,9
	.half	.L153-.L152
	.byte	1,5,41,9
	.half	.L98-.L153
	.byte	3,126,1,5,38,9
	.half	.L12-.L98
	.byte	1,5,25,7,9
	.half	.L154-.L12
	.byte	3,4,1,5,37,9
	.half	.L155-.L154
	.byte	3,1,1,5,1,7,9
	.half	.L156-.L155
	.byte	3,1,1,7,9
	.half	.L52-.L156
	.byte	0,1,1
.L143:
	.sdecl	'.debug_ranges',debug,cluster('cpu_wait_event_ready')
	.sect	'.debug_ranges'
.L51:
	.word	-1,.L23,0,.L52-.L23,0,0
	.sdecl	'.debug_info',debug,cluster('g_AppCpu0')
	.sect	'.debug_info'
.L53:
	.word	226
	.half	3
	.word	.L54
	.byte	4,1
	.byte	'../libraries/zf_common/zf_common_clock.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L24
	.byte	3
	.byte	'g_AppCpu0',0,17,46,10
	.word	.L87
	.byte	1,5,3
	.word	g_AppCpu0
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('g_AppCpu0')
	.sect	'.debug_abbrev'
.L54:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('cpu_init_finish')
	.sect	'.debug_info'
.L55:
	.word	231
	.half	3
	.word	.L56
	.byte	4,1
	.byte	'../libraries/zf_common/zf_common_clock.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L24
	.byte	3
	.byte	'cpu_init_finish',0,17,48,15
	.word	.L88
	.byte	5,3
	.word	cpu_init_finish
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('cpu_init_finish')
	.sect	'.debug_abbrev'
.L56:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_loc',debug,cluster('clock_init')
	.sect	'.debug_loc'
.L20:
	.word	-1,.L21,0,.L78-.L21
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('cpu_wait_event_ready')
	.sect	'.debug_loc'
.L82:
	.word	-1,.L23,.L95-.L23,.L96-.L23
	.half	1
	.byte	88
	.word	.L98-.L23,.L79-.L23
	.half	1
	.byte	88
	.word	0,0
.L22:
	.word	-1,.L23,0,.L79-.L23
	.half	2
	.byte	138,0
	.word	0,0
.L81:
	.word	-1,.L23,.L97-.L23,.L79-.L23
	.half	1
	.byte	95
	.word	0,0
.L86:
	.word	-1,.L23,.L93-.L23,.L94-.L23
	.half	1
	.byte	95
	.word	0,0
.L85:
	.word	-1,.L23,.L91-.L23,.L92-.L23
	.half	1
	.byte	95
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('disable_Watchdog')
	.sect	'.debug_loc'
.L18:
	.word	-1,.L19,0,.L77-.L19
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('get_clock')
	.sect	'.debug_loc'
.L16:
	.word	-1,.L17,0,.L58-.L17
	.half	2
	.byte	138,0
	.word	0,0
.L63:
	.word	-1,.L17,.L89-.L17,.L90-.L17
	.half	1
	.byte	95
	.word	0,0
.L74:
	.word	0,0
.L67:
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('set_clock')
	.sect	'.debug_loc'
.L14:
	.word	-1,.L15,0,.L57-.L15
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L157:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('set_clock')
	.sect	'.debug_frame'
	.word	12
	.word	.L157,.L15,.L57-.L15
	.sdecl	'.debug_frame',debug,cluster('get_clock')
	.sect	'.debug_frame'
	.word	12
	.word	.L157,.L17,.L58-.L17
	.sdecl	'.debug_frame',debug,cluster('disable_Watchdog')
	.sect	'.debug_frame'
	.word	12
	.word	.L157,.L19,.L77-.L19
	.sdecl	'.debug_frame',debug,cluster('clock_init')
	.sect	'.debug_frame'
	.word	12
	.word	.L157,.L21,.L78-.L21
	.sdecl	'.debug_frame',debug,cluster('cpu_wait_event_ready')
	.sect	'.debug_frame'
	.word	12
	.word	.L157,.L23,.L79-.L23
	; Module end
