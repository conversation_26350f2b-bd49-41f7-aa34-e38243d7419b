 <Root>
  <REG_HEADER>
   <Title>BF283F24-70B9-4f97-86E6-0AD5BAAE9E01</Title>
   <VersionHigh>0</VersionHigh>
   <VersionLow>1</VersionLow>
  </REG_HEADER>
  <REG_ROOT>
   <RegVer>1297076244</RegVer>
   <Project>
    <CompilerType>0</CompilerType>
    <GlobalOptions>
     <AHeaderExtension>INC</AHeaderExtension>
     <AInpExtensions>ASM</AInpExtensions>
     <AOutExtensions>OBJ</AOutExtensions>
     <AsmAdditionalOutExt></AsmAdditionalOutExt>
     <AssemblerRelPath></AssemblerRelPath>
     <BeepWhenDone>1</BeepWhenDone>
     <CaptureTranslatorsOutputAsm>1</CaptureTranslatorsOutputAsm>
     <CaptureTranslatorsOutputCmp>1</CaptureTranslatorsOutputCmp>
     <CaptureTranslatorsOutputLnk>1</CaptureTranslatorsOutputLnk>
     <ChangeWorkingDirectory>1</ChangeWorkingDirectory>
     <CInpExtensions>C;CPP</CInpExtensions>
     <CmpAdditionalOutExt></CmpAdditionalOutExt>
     <CompCmdLineIncPrefix>-I</CompCmdLineIncPrefix>
     <CompCmdLinePostfix></CompCmdLinePostfix>
     <CompCmdLinePrefix></CompCmdLinePrefix>
     <CompilerDirectory></CompilerDirectory>
     <CompilerRelPath></CompilerRelPath>
     <CopyOutputFilesToTargetDir>1</CopyOutputFilesToTargetDir>
     <COutExtensions>OBJ</COutExtensions>
     <DeleteTemporalyFiles>0</DeleteTemporalyFiles>
     <DF_AllowBlanks>True</DF_AllowBlanks>
     <DF_CheckSourceFileFolder>True</DF_CheckSourceFileFolder>
     <DF_End></DF_End>
     <DF_Keyword></DF_Keyword>
     <DF_ReqBlanks>True</DF_ReqBlanks>
     <DF_Start></DF_Start>
     <DF_Valid>False</DF_Valid>
     <DisplayParameters>0</DisplayParameters>
     <ExecutableFileName></ExecutableFileName>
     <ExternalMake>0</ExternalMake>
     <ExternalMakeFile></ExternalMakeFile>
     <HeaderExtension>H</HeaderExtension>
     <LinkerRelPath></LinkerRelPath>
     <LInpExtensions>OBJ;LIB</LInpExtensions>
     <LnkAdditionalOutExt></LnkAdditionalOutExt>
     <ParallelBuild>1</ParallelBuild>
     <ParallelBuildNum>0</ParallelBuildNum>
     <PathSeparator>\</PathSeparator>
     <RootDir></RootDir>
     <RunAfterAsm></RunAfterAsm>
     <RunAfterAsmParam></RunAfterAsmParam>
     <RunAfterComp></RunAfterComp>
     <RunAfterCompParam></RunAfterCompParam>
     <RunAfterLinker></RunAfterLinker>
     <RunBeforeBuild></RunBeforeBuild>
     <RunBeforeBuildParam></RunBeforeBuildParam>
     <RunBeforeMake></RunBeforeMake>
     <RunBeforeMakeParam></RunBeforeMakeParam>
     <RunCommandInterpreterAsm>1</RunCommandInterpreterAsm>
     <RunCommandInterpreterCmp>1</RunCommandInterpreterCmp>
     <RunCommandInterpreterLnk>1</RunCommandInterpreterLnk>
     <RunWithRelativePath>0</RunWithRelativePath>
     <ShowToolWindow>0</ShowToolWindow>
     <WarnIncludes>0</WarnIncludes>
     <mbBuild>
      <Mode>0</Mode>
      <Options></Options>
      <Path></Path>
     </mbBuild>
     <mbMake>
      <Mode>0</Mode>
      <Options></Options>
      <Path></Path>
     </mbMake>
    </GlobalOptions>
    <Project.0>
     <DefaultTargetName>ADS - Debug</DefaultTargetName>
     <FilterName>No filter</FilterName>
     <ProjectName></ProjectName>
     <AdvancedLinkerOptions>
      <AsmCustomFilterName></AsmCustomFilterName>
      <AsmUseDefaultName>True</AsmUseDefaultName>
      <CaptureErrOutOnly>False</CaptureErrOutOnly>
      <CmpCustomFilterName></CmpCustomFilterName>
      <CmpUseDefaultName>True</CmpUseDefaultName>
      <ExternalFilterPath></ExternalFilterPath>
      <LinkCustomFilterName></LinkCustomFilterName>
      <LinkUseDefaultName>True</LinkUseDefaultName>
      <UseExternalFilter>False</UseExternalFilter>
     </AdvancedLinkerOptions>
     <TargetOptions>
      <AsmArgFile></AsmArgFile>
      <BuildOptions></BuildOptions>
      <CompArgFile></CompArgFile>
      <CRLF>False</CRLF>
      <ExternalMakeFileParameters></ExternalMakeFileParameters>
      <IndFileLocation>0</IndFileLocation>
      <IndirectionFile></IndirectionFile>
      <LinkerOptions></LinkerOptions>
      <MakeOptions></MakeOptions>
      <RunAfterLinkParam></RunAfterLinkParam>
      <SkipObjectCheck>False</SkipObjectCheck>
      <TargetDirectory></TargetDirectory>
      <TargetDirectory2></TargetDirectory2>
      <TranslateCharacter>38</TranslateCharacter>
      <UseTargetDirectory2>False</UseTargetDirectory2>
     </TargetOptions>
     <Target.0>
      <TargetName>ADS - Debug</TargetName>
      <FileOptios>
       <AssemblerOptions>$(EDNAME)</AssemblerOptions>
       <CompilerOptions>$(EDNAME)</CompilerOptions>
      </FileOptios>
      <LinkGroup.0>
       <LinkGroupName>Source files</LinkGroupName>
      </LinkGroup.0>
      <TargetOptios>
       <AsmArgFile></AsmArgFile>
       <BuildOptions></BuildOptions>
       <CompArgFile></CompArgFile>
       <CRLF>False</CRLF>
       <ExternalMakeFileParameters></ExternalMakeFileParameters>
       <IndFileLocation>0</IndFileLocation>
       <IndirectionFile></IndirectionFile>
       <LinkerOptions></LinkerOptions>
       <MakeOptions></MakeOptions>
       <RunAfterLinkParam></RunAfterLinkParam>
       <SkipObjectCheck>False</SkipObjectCheck>
       <TargetDirectory></TargetDirectory>
       <TargetDirectory2></TargetDirectory2>
       <TranslateCharacter>38</TranslateCharacter>
       <UseTargetDirectory2>False</UseTargetDirectory2>
      </TargetOptios>
     </Target.0>
    </Project.0>
   </Project>
  </REG_ROOT>
 </Root>
