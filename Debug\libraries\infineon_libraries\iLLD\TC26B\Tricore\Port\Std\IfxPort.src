	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc8044a --dep-file=IfxPort.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/infineon_libraries/iLLD/TC26B/Tricore/Port/Std/IfxPort.src ../libraries/infineon_libraries/iLLD/TC26B/Tricore/Port/Std/IfxPort.c"
	.compiler_name		"ctc"
	;source	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Port/Std/IfxPort.c'

	
$TC16X
	
	.sdecl	'.text.IfxPort.IfxPort_disableEmergencyStop',code,cluster('IfxPort_disableEmergencyStop')
	.sect	'.text.IfxPort.IfxPort_disableEmergencyStop'
	.align	2
	
	.global	IfxPort_disableEmergencyStop
; Function IfxPort_disableEmergencyStop
.L54:
IfxPort_disableEmergencyStop:	.type	func
	mov	d2,#0
.L309:
	mov	d1,#0
.L310:
	j	.L2
.L3:
	mul	d15,d1,#8
.L439:
	movh.a	a15,#@his(IfxPort_cfg_esrMasks)
	lea	a15,[a15]@los(IfxPort_cfg_esrMasks)
.L440:
	addsc.a	a15,a15,d15,#0
.L441:
	ld.a	a15,[a15]
.L442:
	jne.a	a4,a15,.L4
.L443:
	mov	d0,#1
.L444:
	sh	d0,d0,d4
.L445:
	mul	d15,d1,#8
.L446:
	movh.a	a15,#@his(IfxPort_cfg_esrMasks)
	lea	a15,[a15]@los(IfxPort_cfg_esrMasks)
.L447:
	addsc.a	a15,a15,d15,#0
.L448:
	ld.hu	d15,[a15]4
.L449:
	and	d0,d15
.L450:
	jeq	d0,#0,.L5
.L451:
	call	IfxPort_resetESR
.L308:
	mov	d2,#1
.L5:
	j	.L6
.L4:
	add	d1,#1
.L2:
	mov	d15,#13
.L452:
	jlt	d1,d15,.L3
.L6:
	j	.L7
.L7:
	ret
.L149:
	
__IfxPort_disableEmergencyStop_function_end:
	.size	IfxPort_disableEmergencyStop,__IfxPort_disableEmergencyStop_function_end-IfxPort_disableEmergencyStop
.L87:
	; End of function
	
	.sdecl	'.text.IfxPort.IfxPort_enableEmergencyStop',code,cluster('IfxPort_enableEmergencyStop')
	.sect	'.text.IfxPort.IfxPort_enableEmergencyStop'
	.align	2
	
	.global	IfxPort_enableEmergencyStop
; Function IfxPort_enableEmergencyStop
.L56:
IfxPort_enableEmergencyStop:	.type	func
	mov.aa	a12,a4
.L311:
	mov	d8,d4
.L312:
	mov	d2,#0
.L313:
	mov	d9,#0
.L315:
	j	.L8
.L9:
	mul	d15,d9,#8
.L457:
	movh.a	a15,#@his(IfxPort_cfg_esrMasks)
	lea	a15,[a15]@los(IfxPort_cfg_esrMasks)
.L458:
	addsc.a	a15,a15,d15,#0
.L459:
	ld.a	a15,[a15]
.L460:
	jne.a	a12,a15,.L10
.L461:
	mov	d0,#1
.L462:
	sh	d0,d0,d8
.L463:
	mul	d15,d9,#8
.L464:
	movh.a	a15,#@his(IfxPort_cfg_esrMasks)
	lea	a15,[a15]@los(IfxPort_cfg_esrMasks)
.L465:
	addsc.a	a15,a15,d15,#0
.L466:
	ld.hu	d15,[a15]4
.L467:
	and	d0,d15
.L468:
	jeq	d0,#0,.L11
.L469:
	mov.aa	a4,a12
.L316:
	mov	d4,d8
.L317:
	call	IfxPort_setESR
.L314:
	mov	d2,#1
.L11:
.L10:
	add	d9,#1
.L8:
	mov	d15,#13
.L470:
	jlt	d9,d15,.L9
.L471:
	j	.L12
.L12:
	ret
.L156:
	
__IfxPort_enableEmergencyStop_function_end:
	.size	IfxPort_enableEmergencyStop,__IfxPort_enableEmergencyStop_function_end-IfxPort_enableEmergencyStop
.L92:
	; End of function
	
	.sdecl	'.text.IfxPort.IfxPort_getAddress',code,cluster('IfxPort_getAddress')
	.sect	'.text.IfxPort.IfxPort_getAddress'
	.align	2
	
	.global	IfxPort_getAddress
; Function IfxPort_getAddress
.L58:
IfxPort_getAddress:	.type	func
	mov.a	a2,#0
.L318:
	mov	d0,#0
.L319:
	j	.L13
.L14:
	mul	d15,d0,#8
.L490:
	movh.a	a15,#@his(IfxPort_cfg_indexMap)
	lea	a15,[a15]@los(IfxPort_cfg_indexMap)
.L491:
	addsc.a	a15,a15,d15,#0
.L492:
	ld.w	d15,[a15]4
.L493:
	jne	d15,d4,.L15
.L494:
	mul	d15,d0,#8
.L495:
	movh.a	a15,#@his(IfxPort_cfg_indexMap)
	lea	a15,[a15]@los(IfxPort_cfg_indexMap)
.L496:
	addsc.a	a15,a15,d15,#0
.L497:
	ld.a	a2,[a15]
.L15:
	add	d0,#1
.L13:
	jge.u	d0,#13,.L16
.L498:
	jz.a	a2,.L14
.L16:
	j	.L17
.L17:
	ret
.L199:
	
__IfxPort_getAddress_function_end:
	.size	IfxPort_getAddress,__IfxPort_getAddress_function_end-IfxPort_getAddress
.L107:
	; End of function
	
	.sdecl	'.text.IfxPort.IfxPort_getIndex',code,cluster('IfxPort_getIndex')
	.sect	'.text.IfxPort.IfxPort_getIndex'
	.align	2
	
	.global	IfxPort_getIndex
; Function IfxPort_getIndex
.L60:
IfxPort_getIndex:	.type	func
	mov	d2,#-1
.L320:
	mov	d0,#0
.L321:
	j	.L18
.L19:
	mul	d15,d0,#8
.L503:
	movh.a	a15,#@his(IfxPort_cfg_indexMap)
	lea	a15,[a15]@los(IfxPort_cfg_indexMap)
.L504:
	addsc.a	a15,a15,d15,#0
.L505:
	ld.a	a15,[a15]
.L506:
	jne.a	a15,a4,.L20
.L507:
	mul	d15,d0,#8
.L508:
	movh.a	a15,#@his(IfxPort_cfg_indexMap)
	lea	a15,[a15]@los(IfxPort_cfg_indexMap)
.L509:
	addsc.a	a15,a15,d15,#0
.L510:
	ld.w	d15,[a15]4
.L511:
	extr	d2,d15,#0,#8
.L512:
	j	.L21
.L20:
	add	d0,#1
.L18:
	jlt.u	d0,#13,.L19
.L21:
	j	.L22
.L22:
	ret
.L204:
	
__IfxPort_getIndex_function_end:
	.size	IfxPort_getIndex,__IfxPort_getIndex_function_end-IfxPort_getIndex
.L112:
	; End of function
	
	.sdecl	'.text.IfxPort.IfxPort_resetESR',code,cluster('IfxPort_resetESR')
	.sect	'.text.IfxPort.IfxPort_resetESR'
	.align	2
	
	.global	IfxPort_resetESR
; Function IfxPort_resetESR
.L62:
IfxPort_resetESR:	.type	func
	mov.aa	a15,a4
.L323:
	mov	d15,d4
.L325:
	call	IfxScuWdt_getCpuWatchdogPassword
.L322:
	mov	d8,d2
.L327:
	mov	d4,d8
.L326:
	call	IfxScuWdt_clearCpuEndinit
.L328:
	lea	a15,[a15]80
.L324:
	mov	d0,#1
	sh	d0,d0,d15
	mov	d15,#0
.L294:
	
		mov d3, d0 
	mov d2, d15 
	ldmst [a15],e2
	
.L295:
	mov	d4,d8
.L329:
	call	IfxScuWdt_setCpuEndinit
.L330:
	ret
.L290:
	
__IfxPort_resetESR_function_end:
	.size	IfxPort_resetESR,__IfxPort_resetESR_function_end-IfxPort_resetESR
.L142:
	; End of function
	
	.sdecl	'.text.IfxPort.IfxPort_setESR',code,cluster('IfxPort_setESR')
	.sect	'.text.IfxPort.IfxPort_setESR'
	.align	2
	
	.global	IfxPort_setESR
; Function IfxPort_setESR
.L64:
IfxPort_setESR:	.type	func
	mov.aa	a15,a4
.L332:
	mov	d15,d4
.L334:
	call	IfxScuWdt_getCpuWatchdogPassword
.L331:
	mov	d8,d2
.L336:
	mov	d4,d8
.L335:
	call	IfxScuWdt_clearCpuEndinit
.L337:
	lea	a15,[a15]80
.L333:
	mov	d0,#1
	sh	d0,d0,d15
	mov	d1,#1
	sh	d1,d1,d15
.L303:
	
		mov d3, d0 
	mov d2, d1 
	ldmst [a15],e2
	
.L304:
	mov	d4,d8
.L338:
	call	IfxScuWdt_setCpuEndinit
.L339:
	ret
.L299:
	
__IfxPort_setESR_function_end:
	.size	IfxPort_setESR,__IfxPort_setESR_function_end-IfxPort_setESR
.L147:
	; End of function
	
	.sdecl	'.text.IfxPort.IfxPort_setGroupModeInput',code,cluster('IfxPort_setGroupModeInput')
	.sect	'.text.IfxPort.IfxPort_setGroupModeInput'
	.align	2
	
	.global	IfxPort_setGroupModeInput
; Function IfxPort_setGroupModeInput
.L66:
IfxPort_setGroupModeInput:	.type	func
	sub.a	a10,#32
.L340:
	mov	d7,d4
.L341:
	mov	d0,d5
.L342:
	mov	d1,#0
.L344:
	j	.L23
.L24:
	mul	d15,d1,#4
	addsc.a	a15,a10,d15,#0
.L517:
	mov	d4,#0
.L518:
	st.w	[a15],d4
.L519:
	mul	d15,d1,#4
	addsc.a	a15,a10,d15,#0
.L520:
	mov	d15,#0
.L521:
	st.w	[a15]16,d15
.L522:
	add	d1,#1
.L23:
	jlt.u	d1,#4,.L24
.L219:
	sh	d0,d0,d7
.L343:
	j	.L25
.L26:
	mov	d1,#1
.L523:
	sh	d1,d1,d7
.L524:
	and	d1,d0
.L525:
	jeq	d1,#0,.L27
.L221:
	mov	d1,#4
.L526:
	div.u	e4,d7,d1
.L346:
	and	d15,d7,#3
.L527:
	mul	d8,d15,#8
.L347:
	mul	d15,d4,#4
	addsc.a	a15,a10,d15,#0
	mul	d15,d4,#4
	addsc.a	a2,a10,d15,#0
	ld.w	d1,[a2]16
.L528:
	mov	d15,#248
.L529:
	sh	d15,d15,d8
.L530:
	or	d1,d15
	st.w	[a15]16,d1
.L531:
	mul	d15,d4,#4
	addsc.a	a15,a10,d15,#0
	mul	d15,d4,#4
	addsc.a	a2,a10,d15,#0
	ld.w	d15,[a2]
.L532:
	sha	d1,d6,d8
.L533:
	or	d15,d1
	st.w	[a15],d15
.L27:
	add	d7,#1
.L25:
	mov	d15,#16
.L534:
	jlt.u	d7,d15,.L26
.L535:
	mov	d0,#0
.L345:
	j	.L28
.L29:
	mul	d15,d0,#4
	addsc.a	a15,a10,d15,#0
	ld.w	d15,[a15]16
.L536:
	jeq	d15,#0,.L30
.L537:
	mul	d15,d0,#4
	addsc.a	a15,a4,d15,#0
	lea	a2,[a15]16
	mul	d15,d0,#4
	addsc.a	a15,a10,d15,#0
	ld.w	d1,[a15]16
	mul	d15,d0,#4
	addsc.a	a15,a10,d15,#0
	ld.w	d15,[a15]
.L224:
	
		mov d3, d1 
	mov d2, d15 
	ldmst [a2],e2
	
.L30:
	add	d0,#1
.L28:
	jlt.u	d0,#4,.L29
.L538:
	ret
.L209:
	
__IfxPort_setGroupModeInput_function_end:
	.size	IfxPort_setGroupModeInput,__IfxPort_setGroupModeInput_function_end-IfxPort_setGroupModeInput
.L117:
	; End of function
	
	.sdecl	'.text.IfxPort.IfxPort_setGroupModeOutput',code,cluster('IfxPort_setGroupModeOutput')
	.sect	'.text.IfxPort.IfxPort_setGroupModeOutput'
	.align	2
	
	.global	IfxPort_setGroupModeOutput
; Function IfxPort_setGroupModeOutput
.L68:
IfxPort_setGroupModeOutput:	.type	func
	sub.a	a10,#32
.L348:
	mov	d8,d4
.L349:
	mov	d0,d5
.L350:
	mov	d15,#128
	jne	d15,d7,.L31
.L31:
	mov	d1,#0
.L352:
	j	.L32
.L33:
	mul	d15,d1,#4
	addsc.a	a15,a10,d15,#0
.L543:
	mov	d4,#0
.L544:
	st.w	[a15],d4
.L545:
	mul	d15,d1,#4
	addsc.a	a15,a10,d15,#0
.L546:
	mov	d15,#0
.L547:
	st.w	[a15]16,d15
.L548:
	add	d1,#1
.L32:
	jlt.u	d1,#4,.L33
.L239:
	sh	d0,d0,d8
.L351:
	j	.L34
.L35:
	mov	d1,#1
.L549:
	sh	d1,d1,d8
.L550:
	and	d1,d0
.L551:
	jeq	d1,#0,.L36
.L241:
	mov	d15,#4
.L552:
	div.u	e4,d8,d15
.L354:
	and	d15,d8,#3
.L553:
	mul	d7,d15,#8
.L356:
	mul	d15,d4,#4
	addsc.a	a15,a10,d15,#0
	mul	d15,d4,#4
	addsc.a	a2,a10,d15,#0
	ld.w	d1,[a2]16
.L554:
	mov	d15,#248
.L555:
	sh	d15,d15,d7
.L556:
	or	d1,d15
	st.w	[a15]16,d1
.L557:
	mul	d15,d4,#4
	addsc.a	a15,a10,d15,#0
	mul	d15,d4,#4
	addsc.a	a2,a10,d15,#0
	ld.w	d15,[a2]
.L558:
	or	d4,d6
.L355:
	sh	d4,d4,d7
.L559:
	or	d15,d4
	st.w	[a15],d15
.L36:
	add	d8,#1
.L34:
	mov	d15,#16
.L560:
	jlt.u	d8,d15,.L35
.L561:
	mov	d0,#0
.L353:
	j	.L37
.L38:
	mul	d15,d0,#4
	addsc.a	a15,a10,d15,#0
	ld.w	d15,[a15]16
.L562:
	jeq	d15,#0,.L39
.L563:
	mul	d15,d0,#4
	addsc.a	a15,a4,d15,#0
	lea	a2,[a15]16
	mul	d15,d0,#4
	addsc.a	a15,a10,d15,#0
	ld.w	d1,[a15]16
	mul	d15,d0,#4
	addsc.a	a15,a10,d15,#0
	ld.w	d15,[a15]
.L244:
	
		mov d3, d1 
	mov d2, d15 
	ldmst [a2],e2
	
.L39:
	add	d0,#1
.L37:
	jlt.u	d0,#4,.L38
.L564:
	ret
.L228:
	
__IfxPort_setGroupModeOutput_function_end:
	.size	IfxPort_setGroupModeOutput,__IfxPort_setGroupModeOutput_function_end-IfxPort_setGroupModeOutput
.L122:
	; End of function
	
	.sdecl	'.text.IfxPort.IfxPort_setGroupPadDriver',code,cluster('IfxPort_setGroupPadDriver')
	.sect	'.text.IfxPort.IfxPort_setGroupPadDriver'
	.align	2
	
	.global	IfxPort_setGroupPadDriver
; Function IfxPort_setGroupPadDriver
.L70:
IfxPort_setGroupPadDriver:	.type	func
	sub.a	a10,#16
.L357:
	mov.aa	a12,a4
.L359:
	mov	e8,d4,d5
	mov	d10,d6
.L360:
	call	IfxScuWdt_getCpuWatchdogPassword
.L358:
	mov	d11,d2
.L362:
	mov	d4,d11
.L361:
	call	IfxScuWdt_clearCpuEndinit
.L254:
	mov	d0,#0
.L363:
	j	.L40
.L41:
	mul	d15,d0,#4
	addsc.a	a15,a10,d15,#0
.L569:
	mov	d15,#0
.L570:
	st.w	[a15],d15
.L571:
	mul	d15,d0,#4
	addsc.a	a15,a10,d15,#0
.L572:
	mov	d15,#0
.L573:
	st.w	[a15]8,d15
.L574:
	add	d0,#1
.L40:
	jlt.u	d0,#2,.L41
.L260:
	sh	d8,d8,d9
.L364:
	j	.L42
.L43:
	mov	d0,#1
.L365:
	sh	d0,d0,d9
.L366:
	and	d0,d8
.L575:
	jeq	d0,#0,.L44
.L262:
	mov	d15,#8
.L367:
	div.u	e4,d9,d15
.L369:
	and	d15,d9,#7
.L368:
	mul	d1,d15,#4
.L370:
	mul	d15,d4,#4
	addsc.a	a15,a10,d15,#0
	mul	d15,d4,#4
	addsc.a	a2,a10,d15,#0
	ld.w	d0,[a2]8
.L576:
	mov	d15,#15
.L577:
	sh	d15,d15,d1
.L578:
	or	d0,d15
	st.w	[a15]8,d0
.L579:
	mul	d15,d4,#4
	addsc.a	a15,a10,d15,#0
	mul	d15,d4,#4
	addsc.a	a2,a10,d15,#0
	ld.w	d15,[a2]
.L580:
	sha	d0,d10,d1
.L581:
	or	d15,d0
	st.w	[a15],d15
.L44:
	add	d9,#1
.L42:
	mov	d15,#16
.L371:
	jlt.u	d9,d15,.L43
.L372:
	mov	d0,#0
.L373:
	j	.L45
.L46:
	mul	d15,d0,#4
	addsc.a	a15,a10,d15,#0
	ld.w	d15,[a15]8
.L582:
	jeq	d15,#0,.L47
.L583:
	mul	d15,d0,#4
	addsc.a	a15,a12,d15,#0
	lea	a2,[a15]64
	mul	d15,d0,#4
	addsc.a	a15,a10,d15,#0
	ld.w	d1,[a15]8
	mul	d15,d0,#4
	addsc.a	a15,a10,d15,#0
	ld.w	d15,[a15]
.L265:
	
		mov d3, d1 
	mov d2, d15 
	ldmst [a2],e2
	
.L47:
	add	d0,#1
.L45:
	jlt.u	d0,#2,.L46
.L255:
	mov	d4,d11
.L375:
	call	IfxScuWdt_setCpuEndinit
.L374:
	ret
.L248:
	
__IfxPort_setGroupPadDriver_function_end:
	.size	IfxPort_setGroupPadDriver,__IfxPort_setGroupPadDriver_function_end-IfxPort_setGroupPadDriver
.L127:
	; End of function
	
	.sdecl	'.text.IfxPort.IfxPort_setPinMode',code,cluster('IfxPort_setPinMode')
	.sect	'.text.IfxPort.IfxPort_setPinMode'
	.align	2
	
	.global	IfxPort_setPinMode
; Function IfxPort_setPinMode
.L72:
IfxPort_setPinMode:	.type	func
	mov.aa	a15,a4
.L377:
	mov	e10,d5,d4
.L476:
	lea	a12,[a15]16
.L379:
	mov	d0,#4
.L380:
	div	e8,d10,d0
.L382:
	and	d15,d10,#3
.L381:
	mul	d12,d15,#8
.L383:
	movh.a	a2,#61444
	lea	a2,[a2]@los(0xf003e000)
.L477:
	jne.a	a15,a2,.L48
.L170:
	call	IfxScuWdt_getCpuWatchdogPassword
.L376:
	mov	d15,d2
.L385:
	mov	d4,d15
.L384:
	call	IfxScuWdt_clearCpuEndinit
.L386:
	ld.w	d0,[a15]96
.L478:
	mov	d1,#1
.L387:
	sha	d1,d1,d10
.L388:
	mov	d2,#-1
	xor	d1,d2
.L479:
	and	d0,d1
	st.w	[a15]96,d0
.L480:
	mov	d4,d15
.L389:
	call	IfxScuWdt_setCpuEndinit
.L48:
	mul	d15,d8,#4
	addsc.a	a15,a12,d15,#0
.L378:
	mov	d15,#255
	sh	d15,d15,d12
.L390:
	sha	d11,d11,d12
.L174:
	
		mov d3, d15 
	mov d2, d11 
	ldmst [a15],e2
	
.L175:
	ret
.L161:
	
__IfxPort_setPinMode_function_end:
	.size	IfxPort_setPinMode,__IfxPort_setPinMode_function_end-IfxPort_setPinMode
.L97:
	; End of function
	
	.sdecl	'.text.IfxPort.IfxPort_setPinModeLvdsHigh',code,cluster('IfxPort_setPinModeLvdsHigh')
	.sect	'.text.IfxPort.IfxPort_setPinModeLvdsHigh'
	.align	2
	
	.global	IfxPort_setPinModeLvdsHigh
; Function IfxPort_setPinModeLvdsHigh
.L74:
IfxPort_setPinModeLvdsHigh:	.type	func
	mov.aa	a15,a4
.L392:
	mov	e8,d5,d4
	mov	d10,d6
.L393:
	call	IfxScuWdt_getCpuWatchdogPassword
.L391:
	mov	d11,d2
.L395:
	mov	d4,d11
.L394:
	call	IfxScuWdt_clearCpuEndinit
.L396:
	mov	d15,#128
.L397:
	jge.u	d9,d15,.L49
.L398:
	jge.u	d8,#2,.L50
.L399:
	j	.L51
.L50:
	ld.bu	d15,[a15]164
.L588:
	insert	d15,d15,d10,#0,#1
	st.b	[a15]164,d15
.L589:
	ld.bu	d15,[a15]164
.L590:
	insert	d15,d15,#0,#1,#1
	st.b	[a15]164,d15
.L51:
	j	.L52
.L49:
	ld.bu	d15,[a15]169
.L591:
	insert	d15,d15,d10,#4,#1
	st.b	[a15]169,d15
.L592:
	ld.bu	d15,[a15]169
.L593:
	insert	d15,d15,#0,#5,#1
	st.b	[a15]169,d15
.L594:
	ld.bu	d15,[a15]169
.L595:
	insert	d15,d15,#0,#6,#1
	st.b	[a15]169,d15
.L52:
	mov	d4,d11
.L400:
	call	IfxScuWdt_setCpuEndinit
.L401:
	ret
.L269:
	
__IfxPort_setPinModeLvdsHigh_function_end:
	.size	IfxPort_setPinModeLvdsHigh,__IfxPort_setPinModeLvdsHigh_function_end-IfxPort_setPinModeLvdsHigh
.L132:
	; End of function
	
	.sdecl	'.text.IfxPort.IfxPort_setPinModeLvdsMedium',code,cluster('IfxPort_setPinModeLvdsMedium')
	.sect	'.text.IfxPort.IfxPort_setPinModeLvdsMedium'
	.align	2
	
	.global	IfxPort_setPinModeLvdsMedium
; Function IfxPort_setPinModeLvdsMedium
.L76:
IfxPort_setPinModeLvdsMedium:	.type	func
	sub.a	a10,#8
.L402:
	mov	e8,d6,d5
.L600:
	mov	d15,#8
.L601:
	div	e10,d4,d15
.L404:
	mov	d15,#2
.L602:
	div	e0,d4,d15
.L603:
	mul	d14,d0,#8
.L405:
	mov	d15,#2
.L604:
	div	e12,d4,d15
.L406:
	lea	a15,[a4]64
.L407:
	lea	a12,[a4]160
.L409:
	call	IfxScuWdt_getCpuWatchdogPassword
.L403:
	mov	d4,d2
.L411:
	st.w	[a10],d2
.L412:
	call	IfxScuWdt_clearCpuEndinit
.L410:
	mul	d15,d10,#4
	addsc.a	a15,a15,d15,#0
.L408:
	sha	d8,d8,d14
.L413:
	st.w	[a15],d8
.L605:
	mul	d15,d12,#4
	addsc.a	a15,a12,d15,#0
	mul	d15,d12,#4
	addsc.a	a2,a12,d15,#0
.L606:
	ld.bu	d15,[a2]
.L414:
	insert	d15,d15,d9,#1,#1
.L415:
	st.b	[a15],d15
.L607:
	ld.w	d4,[a10]
.L416:
	call	IfxScuWdt_setCpuEndinit
.L417:
	ret
.L276:
	
__IfxPort_setPinModeLvdsMedium_function_end:
	.size	IfxPort_setPinModeLvdsMedium,__IfxPort_setPinModeLvdsMedium_function_end-IfxPort_setPinModeLvdsMedium
.L137:
	; End of function
	
	.sdecl	'.text.IfxPort.IfxPort_setPinPadDriver',code,cluster('IfxPort_setPinPadDriver')
	.sect	'.text.IfxPort.IfxPort_setPinPadDriver'
	.align	2
	
	.global	IfxPort_setPinPadDriver
; Function IfxPort_setPinPadDriver
.L78:
IfxPort_setPinPadDriver:	.type	func
	mov.aa	a15,a4
.L419:
	mov	e8,d5,d4
.L485:
	call	IfxScuWdt_getCpuWatchdogPassword
.L418:
	mov	d10,d2
.L422:
	mov	d4,d10
.L421:
	call	IfxScuWdt_clearCpuEndinit
.L189:
	lea	a15,[a15]64
.L420:
	mov	d15,#8
.L424:
	div	e0,d8,d15
.L426:
	and	d15,d8,#7
.L425:
	mul	d4,d15,#4
.L428:
	mul	d15,d0,#4
	addsc.a	a15,a15,d15,#0
.L423:
	mov	d15,#15
	sh	d15,d15,d4
.L430:
	sha	d9,d9,d4
.L195:
	
		mov d3, d15 
	mov d2, d9 
	ldmst [a15],e2
	
.L190:
	mov	d4,d10
.L429:
	call	IfxScuWdt_setCpuEndinit
.L427:
	ret
.L183:
	
__IfxPort_setPinPadDriver_function_end:
	.size	IfxPort_setPinPadDriver,__IfxPort_setPinPadDriver_function_end-IfxPort_setPinPadDriver
.L102:
	; End of function
	
	.calls	'IfxPort_disableEmergencyStop','IfxPort_resetESR'
	.calls	'IfxPort_enableEmergencyStop','IfxPort_setESR'
	.calls	'IfxPort_resetESR','IfxScuWdt_getCpuWatchdogPassword'
	.calls	'IfxPort_resetESR','IfxScuWdt_clearCpuEndinit'
	.calls	'IfxPort_resetESR','IfxScuWdt_setCpuEndinit'
	.calls	'IfxPort_setESR','IfxScuWdt_getCpuWatchdogPassword'
	.calls	'IfxPort_setESR','IfxScuWdt_clearCpuEndinit'
	.calls	'IfxPort_setESR','IfxScuWdt_setCpuEndinit'
	.calls	'IfxPort_setGroupPadDriver','IfxScuWdt_getCpuWatchdogPassword'
	.calls	'IfxPort_setGroupPadDriver','IfxScuWdt_clearCpuEndinit'
	.calls	'IfxPort_setGroupPadDriver','IfxScuWdt_setCpuEndinit'
	.calls	'IfxPort_setPinMode','IfxScuWdt_getCpuWatchdogPassword'
	.calls	'IfxPort_setPinMode','IfxScuWdt_clearCpuEndinit'
	.calls	'IfxPort_setPinMode','IfxScuWdt_setCpuEndinit'
	.calls	'IfxPort_setPinModeLvdsHigh','IfxScuWdt_getCpuWatchdogPassword'
	.calls	'IfxPort_setPinModeLvdsHigh','IfxScuWdt_clearCpuEndinit'
	.calls	'IfxPort_setPinModeLvdsHigh','IfxScuWdt_setCpuEndinit'
	.calls	'IfxPort_setPinModeLvdsMedium','IfxScuWdt_getCpuWatchdogPassword'
	.calls	'IfxPort_setPinModeLvdsMedium','IfxScuWdt_clearCpuEndinit'
	.calls	'IfxPort_setPinModeLvdsMedium','IfxScuWdt_setCpuEndinit'
	.calls	'IfxPort_setPinPadDriver','IfxScuWdt_getCpuWatchdogPassword'
	.calls	'IfxPort_setPinPadDriver','IfxScuWdt_clearCpuEndinit'
	.calls	'IfxPort_setPinPadDriver','IfxScuWdt_setCpuEndinit'
	.calls	'IfxPort_disableEmergencyStop','',0
	.calls	'IfxPort_enableEmergencyStop','',0
	.calls	'IfxPort_getAddress','',0
	.calls	'IfxPort_getIndex','',0
	.calls	'IfxPort_resetESR','',0
	.calls	'IfxPort_setESR','',0
	.calls	'IfxPort_setGroupModeInput','',32
	.calls	'IfxPort_setGroupModeOutput','',32
	.calls	'IfxPort_setGroupPadDriver','',16
	.calls	'IfxPort_setPinMode','',0
	.calls	'IfxPort_setPinModeLvdsHigh','',0
	.calls	'IfxPort_setPinModeLvdsMedium','',8
	.extern	IfxPort_cfg_esrMasks
	.extern	IfxPort_cfg_indexMap
	.extern	IfxScuWdt_clearCpuEndinit
	.extern	IfxScuWdt_setCpuEndinit
	.extern	IfxScuWdt_getCpuWatchdogPassword
	.calls	'IfxPort_setPinPadDriver','',0
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L80:
	.word	37838
	.half	3
	.word	.L81
	.byte	4
.L79:
	.byte	1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Port/Std/IfxPort.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L82
	.byte	2,1,1,3
	.word	232
	.byte	4
	.byte	'Ifx__jump_and_link',0,3,1,61,17,1,1,5
	.byte	'fun',0,1,61,43
	.word	235
	.byte	6,0,7
	.byte	'void',0,8
	.word	280
	.byte	3
	.word	286
.L206:
	.byte	9
	.byte	'unsigned long int',0,4,7
.L173:
	.byte	4
	.byte	'Ifx__ldmstAsm',0,3,1,141,2,17,1,1
.L176:
	.byte	5
	.byte	'addr',0,1,141,2,46
	.word	291
.L178:
	.byte	5
	.byte	'mask',0,1,141,2,59
	.word	296
.L180:
	.byte	5
	.byte	'data',0,1,141,2,72
	.word	296
.L182:
	.byte	6,0,9
	.byte	'__fract',0,4,128,1,9
	.byte	'float',0,4,4,10
	.byte	'Ifx__float_to_fract',0,3,1,152,2,18
	.word	383
	.byte	1,1,5
	.byte	'a',0,1,152,2,44
	.word	395
	.byte	6,0,9
	.byte	'unsigned long long int',0,8,7,3
	.word	280
	.byte	10
	.byte	'__ld64',0,3,2,135,1,19
	.word	449
	.byte	1,1,5
	.byte	'addr',0,2,135,1,32
	.word	475
	.byte	6,0,4
	.byte	'__st64',0,3,2,143,1,17,1,1,5
	.byte	'addr',0,2,143,1,30
	.word	475
	.byte	5
	.byte	'value',0,2,143,1,43
	.word	449
	.byte	6,0,9
	.byte	'unsigned int',0,4,7,9
	.byte	'int',0,4,5
.L148:
	.byte	9
	.byte	'unsigned char',0,1,8
.L171:
	.byte	9
	.byte	'unsigned short int',0,2,7,11
	.byte	'_Ifx_P_OUT_Bits',0,4,143,3,16,4,12
	.byte	'P0',0,1
	.word	584
	.byte	1,7,2,35,0,12
	.byte	'P1',0,1
	.word	584
	.byte	1,6,2,35,0,12
	.byte	'P2',0,1
	.word	584
	.byte	1,5,2,35,0,12
	.byte	'P3',0,1
	.word	584
	.byte	1,4,2,35,0,12
	.byte	'P4',0,1
	.word	584
	.byte	1,3,2,35,0,12
	.byte	'P5',0,1
	.word	584
	.byte	1,2,2,35,0,12
	.byte	'P6',0,1
	.word	584
	.byte	1,1,2,35,0,12
	.byte	'P7',0,1
	.word	584
	.byte	1,0,2,35,0,12
	.byte	'P8',0,1
	.word	584
	.byte	1,7,2,35,1,12
	.byte	'P9',0,1
	.word	584
	.byte	1,6,2,35,1,12
	.byte	'P10',0,1
	.word	584
	.byte	1,5,2,35,1,12
	.byte	'P11',0,1
	.word	584
	.byte	1,4,2,35,1,12
	.byte	'P12',0,1
	.word	584
	.byte	1,3,2,35,1,12
	.byte	'P13',0,1
	.word	584
	.byte	1,2,2,35,1,12
	.byte	'P14',0,1
	.word	584
	.byte	1,1,2,35,1,12
	.byte	'P15',0,1
	.word	584
	.byte	1,0,2,35,1,12
	.byte	'reserved_16',0,2
	.word	601
	.byte	16,0,2,35,2,0,13,4,181,5,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	623
	.byte	4,2,35,0,0,11
	.byte	'_Ifx_P_OMR_Bits',0,4,169,2,16,4,12
	.byte	'PS0',0,1
	.word	584
	.byte	1,7,2,35,0,12
	.byte	'PS1',0,1
	.word	584
	.byte	1,6,2,35,0,12
	.byte	'PS2',0,1
	.word	584
	.byte	1,5,2,35,0,12
	.byte	'PS3',0,1
	.word	584
	.byte	1,4,2,35,0,12
	.byte	'PS4',0,1
	.word	584
	.byte	1,3,2,35,0,12
	.byte	'PS5',0,1
	.word	584
	.byte	1,2,2,35,0,12
	.byte	'PS6',0,1
	.word	584
	.byte	1,1,2,35,0,12
	.byte	'PS7',0,1
	.word	584
	.byte	1,0,2,35,0,12
	.byte	'PS8',0,1
	.word	584
	.byte	1,7,2,35,1,12
	.byte	'PS9',0,1
	.word	584
	.byte	1,6,2,35,1,12
	.byte	'PS10',0,1
	.word	584
	.byte	1,5,2,35,1,12
	.byte	'PS11',0,1
	.word	584
	.byte	1,4,2,35,1,12
	.byte	'PS12',0,1
	.word	584
	.byte	1,3,2,35,1,12
	.byte	'PS13',0,1
	.word	584
	.byte	1,2,2,35,1,12
	.byte	'PS14',0,1
	.word	584
	.byte	1,1,2,35,1,12
	.byte	'PS15',0,1
	.word	584
	.byte	1,0,2,35,1,12
	.byte	'PCL0',0,1
	.word	584
	.byte	1,7,2,35,2,12
	.byte	'PCL1',0,1
	.word	584
	.byte	1,6,2,35,2,12
	.byte	'PCL2',0,1
	.word	584
	.byte	1,5,2,35,2,12
	.byte	'PCL3',0,1
	.word	584
	.byte	1,4,2,35,2,12
	.byte	'PCL4',0,1
	.word	584
	.byte	1,3,2,35,2,12
	.byte	'PCL5',0,1
	.word	584
	.byte	1,2,2,35,2,12
	.byte	'PCL6',0,1
	.word	584
	.byte	1,1,2,35,2,12
	.byte	'PCL7',0,1
	.word	584
	.byte	1,0,2,35,2,12
	.byte	'PCL8',0,1
	.word	584
	.byte	1,7,2,35,3,12
	.byte	'PCL9',0,1
	.word	584
	.byte	1,6,2,35,3,12
	.byte	'PCL10',0,1
	.word	584
	.byte	1,5,2,35,3,12
	.byte	'PCL11',0,1
	.word	584
	.byte	1,4,2,35,3,12
	.byte	'PCL12',0,1
	.word	584
	.byte	1,3,2,35,3,12
	.byte	'PCL13',0,1
	.word	584
	.byte	1,2,2,35,3,12
	.byte	'PCL14',0,1
	.word	584
	.byte	1,1,2,35,3,12
	.byte	'PCL15',0,1
	.word	584
	.byte	1,0,2,35,3,0,13,4,133,5,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	939
	.byte	4,2,35,0,0,11
	.byte	'_Ifx_P_ID_Bits',0,4,110,16,4,12
	.byte	'MODREV',0,1
	.word	584
	.byte	8,0,2,35,0,12
	.byte	'MODTYPE',0,1
	.word	584
	.byte	8,0,2,35,1,12
	.byte	'MODNUMBER',0,2
	.word	601
	.byte	16,0,2,35,2,0,13,4,148,4,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	1510
	.byte	4,2,35,0,0,15,4
	.word	584
	.byte	16,3,0,11
	.byte	'_Ifx_P_IOCR0_Bits',0,4,140,1,16,4,12
	.byte	'reserved_0',0,1
	.word	584
	.byte	3,5,2,35,0,12
	.byte	'PC0',0,1
	.word	584
	.byte	5,0,2,35,0,12
	.byte	'reserved_8',0,1
	.word	584
	.byte	3,5,2,35,1,12
	.byte	'PC1',0,1
	.word	584
	.byte	5,0,2,35,1,12
	.byte	'reserved_16',0,1
	.word	584
	.byte	3,5,2,35,2,12
	.byte	'PC2',0,1
	.word	584
	.byte	5,0,2,35,2,12
	.byte	'reserved_24',0,1
	.word	584
	.byte	3,5,2,35,3,12
	.byte	'PC3',0,1
	.word	584
	.byte	5,0,2,35,3,0,13,4,164,4,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	1638
	.byte	4,2,35,0,0,11
	.byte	'_Ifx_P_IOCR4_Bits',0,4,166,1,16,4,12
	.byte	'reserved_0',0,1
	.word	584
	.byte	3,5,2,35,0,12
	.byte	'PC4',0,1
	.word	584
	.byte	5,0,2,35,0,12
	.byte	'reserved_8',0,1
	.word	584
	.byte	3,5,2,35,1,12
	.byte	'PC5',0,1
	.word	584
	.byte	5,0,2,35,1,12
	.byte	'reserved_16',0,1
	.word	584
	.byte	3,5,2,35,2,12
	.byte	'PC6',0,1
	.word	584
	.byte	5,0,2,35,2,12
	.byte	'reserved_24',0,1
	.word	584
	.byte	3,5,2,35,3,12
	.byte	'PC7',0,1
	.word	584
	.byte	5,0,2,35,3,0,13,4,180,4,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	1853
	.byte	4,2,35,0,0,11
	.byte	'_Ifx_P_IOCR8_Bits',0,4,179,1,16,4,12
	.byte	'reserved_0',0,1
	.word	584
	.byte	3,5,2,35,0,12
	.byte	'PC8',0,1
	.word	584
	.byte	5,0,2,35,0,12
	.byte	'reserved_8',0,1
	.word	584
	.byte	3,5,2,35,1,12
	.byte	'PC9',0,1
	.word	584
	.byte	5,0,2,35,1,12
	.byte	'reserved_16',0,1
	.word	584
	.byte	3,5,2,35,2,12
	.byte	'PC10',0,1
	.word	584
	.byte	5,0,2,35,2,12
	.byte	'reserved_24',0,1
	.word	584
	.byte	3,5,2,35,3,12
	.byte	'PC11',0,1
	.word	584
	.byte	5,0,2,35,3,0,13,4,188,4,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	2068
	.byte	4,2,35,0,0,11
	.byte	'_Ifx_P_IOCR12_Bits',0,4,153,1,16,4,12
	.byte	'reserved_0',0,1
	.word	584
	.byte	3,5,2,35,0,12
	.byte	'PC12',0,1
	.word	584
	.byte	5,0,2,35,0,12
	.byte	'reserved_8',0,1
	.word	584
	.byte	3,5,2,35,1,12
	.byte	'PC13',0,1
	.word	584
	.byte	5,0,2,35,1,12
	.byte	'reserved_16',0,1
	.word	584
	.byte	3,5,2,35,2,12
	.byte	'PC14',0,1
	.word	584
	.byte	5,0,2,35,2,12
	.byte	'reserved_24',0,1
	.word	584
	.byte	3,5,2,35,3,12
	.byte	'PC15',0,1
	.word	584
	.byte	5,0,2,35,3,0,13,4,172,4,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	2285
	.byte	4,2,35,0,0,11
	.byte	'_Ifx_P_IN_Bits',0,4,118,16,4,12
	.byte	'P0',0,1
	.word	584
	.byte	1,7,2,35,0,12
	.byte	'P1',0,1
	.word	584
	.byte	1,6,2,35,0,12
	.byte	'P2',0,1
	.word	584
	.byte	1,5,2,35,0,12
	.byte	'P3',0,1
	.word	584
	.byte	1,4,2,35,0,12
	.byte	'P4',0,1
	.word	584
	.byte	1,3,2,35,0,12
	.byte	'P5',0,1
	.word	584
	.byte	1,2,2,35,0,12
	.byte	'P6',0,1
	.word	584
	.byte	1,1,2,35,0,12
	.byte	'P7',0,1
	.word	584
	.byte	1,0,2,35,0,12
	.byte	'P8',0,1
	.word	584
	.byte	1,7,2,35,1,12
	.byte	'P9',0,1
	.word	584
	.byte	1,6,2,35,1,12
	.byte	'P10',0,1
	.word	584
	.byte	1,5,2,35,1,12
	.byte	'P11',0,1
	.word	584
	.byte	1,4,2,35,1,12
	.byte	'P12',0,1
	.word	584
	.byte	1,3,2,35,1,12
	.byte	'P13',0,1
	.word	584
	.byte	1,2,2,35,1,12
	.byte	'P14',0,1
	.word	584
	.byte	1,1,2,35,1,12
	.byte	'P15',0,1
	.word	584
	.byte	1,0,2,35,1,12
	.byte	'reserved_16',0,2
	.word	601
	.byte	16,0,2,35,2,0,13,4,156,4,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	2505
	.byte	4,2,35,0,0,15,24
	.word	584
	.byte	16,23,0,11
	.byte	'_Ifx_P_PDR0_Bits',0,4,205,3,16,4,12
	.byte	'PD0',0,1
	.word	584
	.byte	3,5,2,35,0,12
	.byte	'PL0',0,1
	.word	584
	.byte	1,4,2,35,0,12
	.byte	'PD1',0,1
	.word	584
	.byte	3,1,2,35,0,12
	.byte	'PL1',0,1
	.word	584
	.byte	1,0,2,35,0,12
	.byte	'PD2',0,1
	.word	584
	.byte	3,5,2,35,1,12
	.byte	'PL2',0,1
	.word	584
	.byte	1,4,2,35,1,12
	.byte	'PD3',0,1
	.word	584
	.byte	3,1,2,35,1,12
	.byte	'PL3',0,1
	.word	584
	.byte	1,0,2,35,1,12
	.byte	'PD4',0,1
	.word	584
	.byte	3,5,2,35,2,12
	.byte	'PL4',0,1
	.word	584
	.byte	1,4,2,35,2,12
	.byte	'PD5',0,1
	.word	584
	.byte	3,1,2,35,2,12
	.byte	'PL5',0,1
	.word	584
	.byte	1,0,2,35,2,12
	.byte	'PD6',0,1
	.word	584
	.byte	3,5,2,35,3,12
	.byte	'PL6',0,1
	.word	584
	.byte	1,4,2,35,3,12
	.byte	'PD7',0,1
	.word	584
	.byte	3,1,2,35,3,12
	.byte	'PL7',0,1
	.word	584
	.byte	1,0,2,35,3,0,13,4,205,5,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	2828
	.byte	4,2,35,0,0,11
	.byte	'_Ifx_P_PDR1_Bits',0,4,226,3,16,4,12
	.byte	'PD8',0,1
	.word	584
	.byte	3,5,2,35,0,12
	.byte	'PL8',0,1
	.word	584
	.byte	1,4,2,35,0,12
	.byte	'PD9',0,1
	.word	584
	.byte	3,1,2,35,0,12
	.byte	'PL9',0,1
	.word	584
	.byte	1,0,2,35,0,12
	.byte	'PD10',0,1
	.word	584
	.byte	3,5,2,35,1,12
	.byte	'PL10',0,1
	.word	584
	.byte	1,4,2,35,1,12
	.byte	'PD11',0,1
	.word	584
	.byte	3,1,2,35,1,12
	.byte	'PL11',0,1
	.word	584
	.byte	1,0,2,35,1,12
	.byte	'PD12',0,1
	.word	584
	.byte	3,5,2,35,2,12
	.byte	'PL12',0,1
	.word	584
	.byte	1,4,2,35,2,12
	.byte	'PD13',0,1
	.word	584
	.byte	3,1,2,35,2,12
	.byte	'PL13',0,1
	.word	584
	.byte	1,0,2,35,2,12
	.byte	'PD14',0,1
	.word	584
	.byte	3,5,2,35,3,12
	.byte	'PL14',0,1
	.word	584
	.byte	1,4,2,35,3,12
	.byte	'PD15',0,1
	.word	584
	.byte	3,1,2,35,3,12
	.byte	'PL15',0,1
	.word	584
	.byte	1,0,2,35,3,0,13,4,213,5,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	3132
	.byte	4,2,35,0,0,15,8
	.word	584
	.byte	16,7,0,11
	.byte	'_Ifx_P_ESR_Bits',0,4,88,16,4,12
	.byte	'EN0',0,1
	.word	584
	.byte	1,7,2,35,0,12
	.byte	'EN1',0,1
	.word	584
	.byte	1,6,2,35,0,12
	.byte	'EN2',0,1
	.word	584
	.byte	1,5,2,35,0,12
	.byte	'EN3',0,1
	.word	584
	.byte	1,4,2,35,0,12
	.byte	'EN4',0,1
	.word	584
	.byte	1,3,2,35,0,12
	.byte	'EN5',0,1
	.word	584
	.byte	1,2,2,35,0,12
	.byte	'EN6',0,1
	.word	584
	.byte	1,1,2,35,0,12
	.byte	'EN7',0,1
	.word	584
	.byte	1,0,2,35,0,12
	.byte	'EN8',0,1
	.word	584
	.byte	1,7,2,35,1,12
	.byte	'EN9',0,1
	.word	584
	.byte	1,6,2,35,1,12
	.byte	'EN10',0,1
	.word	584
	.byte	1,5,2,35,1,12
	.byte	'EN11',0,1
	.word	584
	.byte	1,4,2,35,1,12
	.byte	'EN12',0,1
	.word	584
	.byte	1,3,2,35,1,12
	.byte	'EN13',0,1
	.word	584
	.byte	1,2,2,35,1,12
	.byte	'EN14',0,1
	.word	584
	.byte	1,1,2,35,1,12
	.byte	'EN15',0,1
	.word	584
	.byte	1,0,2,35,1,12
	.byte	'reserved_16',0,2
	.word	601
	.byte	16,0,2,35,2,0,13,4,140,4,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	3457
	.byte	4,2,35,0,0,15,12
	.word	584
	.byte	16,11,0,11
	.byte	'_Ifx_P_PDISC_Bits',0,4,183,3,16,4,12
	.byte	'PDIS0',0,1
	.word	584
	.byte	1,7,2,35,0,12
	.byte	'PDIS1',0,1
	.word	584
	.byte	1,6,2,35,0,12
	.byte	'PDIS2',0,1
	.word	584
	.byte	1,5,2,35,0,12
	.byte	'PDIS3',0,1
	.word	584
	.byte	1,4,2,35,0,12
	.byte	'PDIS4',0,1
	.word	584
	.byte	1,3,2,35,0,12
	.byte	'PDIS5',0,1
	.word	584
	.byte	1,2,2,35,0,12
	.byte	'PDIS6',0,1
	.word	584
	.byte	1,1,2,35,0,12
	.byte	'PDIS7',0,1
	.word	584
	.byte	1,0,2,35,0,12
	.byte	'PDIS8',0,1
	.word	584
	.byte	1,7,2,35,1,12
	.byte	'PDIS9',0,1
	.word	584
	.byte	1,6,2,35,1,12
	.byte	'PDIS10',0,1
	.word	584
	.byte	1,5,2,35,1,12
	.byte	'PDIS11',0,1
	.word	584
	.byte	1,4,2,35,1,12
	.byte	'PDIS12',0,1
	.word	584
	.byte	1,3,2,35,1,12
	.byte	'PDIS13',0,1
	.word	584
	.byte	1,2,2,35,1,12
	.byte	'PDIS14',0,1
	.word	584
	.byte	1,1,2,35,1,12
	.byte	'PDIS15',0,1
	.word	584
	.byte	1,0,2,35,1,12
	.byte	'reserved_16',0,2
	.word	601
	.byte	16,0,2,35,2,0,13,4,197,5,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	3797
	.byte	4,2,35,0,0,11
	.byte	'_Ifx_P_PCSR_Bits',0,4,165,3,16,4,12
	.byte	'SEL0',0,1
	.word	584
	.byte	1,7,2,35,0,12
	.byte	'SEL1',0,1
	.word	584
	.byte	1,6,2,35,0,12
	.byte	'SEL2',0,1
	.word	584
	.byte	1,5,2,35,0,12
	.byte	'SEL3',0,1
	.word	584
	.byte	1,4,2,35,0,12
	.byte	'SEL4',0,1
	.word	584
	.byte	1,3,2,35,0,12
	.byte	'SEL5',0,1
	.word	584
	.byte	1,2,2,35,0,12
	.byte	'SEL6',0,1
	.word	584
	.byte	1,1,2,35,0,12
	.byte	'SEL7',0,1
	.word	584
	.byte	1,0,2,35,0,12
	.byte	'reserved_8',0,1
	.word	584
	.byte	2,6,2,35,1,12
	.byte	'SEL10',0,1
	.word	584
	.byte	1,5,2,35,1,12
	.byte	'SEL11',0,1
	.word	584
	.byte	1,4,2,35,1,12
	.byte	'reserved_12',0,4
	.word	561
	.byte	19,1,2,35,0,12
	.byte	'LCK',0,1
	.word	584
	.byte	1,0,2,35,3,0,13,4,189,5,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	4163
	.byte	4,2,35,0,0,11
	.byte	'_Ifx_P_OMSR0_Bits',0,4,206,2,16,4,12
	.byte	'PS0',0,1
	.word	584
	.byte	1,7,2,35,0,12
	.byte	'PS1',0,1
	.word	584
	.byte	1,6,2,35,0,12
	.byte	'PS2',0,1
	.word	584
	.byte	1,5,2,35,0,12
	.byte	'PS3',0,1
	.word	584
	.byte	1,4,2,35,0,12
	.byte	'reserved_4',0,4
	.word	561
	.byte	28,0,2,35,0,0,13,4,149,5,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	4449
	.byte	4,2,35,0,0,11
	.byte	'_Ifx_P_OMSR4_Bits',0,4,227,2,16,4,12
	.byte	'reserved_0',0,1
	.word	584
	.byte	4,4,2,35,0,12
	.byte	'PS4',0,1
	.word	584
	.byte	1,3,2,35,0,12
	.byte	'PS5',0,1
	.word	584
	.byte	1,2,2,35,0,12
	.byte	'PS6',0,1
	.word	584
	.byte	1,1,2,35,0,12
	.byte	'PS7',0,1
	.word	584
	.byte	1,0,2,35,0,12
	.byte	'reserved_8',0,4
	.word	561
	.byte	24,0,2,35,0,0,13,4,165,5,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	4596
	.byte	4,2,35,0,0,11
	.byte	'_Ifx_P_OMSR8_Bits',0,4,238,2,16,4,12
	.byte	'reserved_0',0,1
	.word	584
	.byte	8,0,2,35,0,12
	.byte	'PS8',0,1
	.word	584
	.byte	1,7,2,35,1,12
	.byte	'PS9',0,1
	.word	584
	.byte	1,6,2,35,1,12
	.byte	'PS10',0,1
	.word	584
	.byte	1,5,2,35,1,12
	.byte	'PS11',0,1
	.word	584
	.byte	1,4,2,35,1,12
	.byte	'reserved_12',0,4
	.word	561
	.byte	20,0,2,35,0,0,13,4,173,5,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	4765
	.byte	4,2,35,0,0,11
	.byte	'_Ifx_P_OMSR12_Bits',0,4,216,2,16,4,12
	.byte	'reserved_0',0,2
	.word	601
	.byte	12,4,2,35,0,12
	.byte	'PS12',0,1
	.word	584
	.byte	1,3,2,35,1,12
	.byte	'PS13',0,1
	.word	584
	.byte	1,2,2,35,1,12
	.byte	'PS14',0,1
	.word	584
	.byte	1,1,2,35,1,12
	.byte	'PS15',0,1
	.word	584
	.byte	1,0,2,35,1,12
	.byte	'reserved_16',0,2
	.word	601
	.byte	16,0,2,35,2,0,13,4,157,5,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	4937
	.byte	4,2,35,0,0,11
	.byte	'_Ifx_P_OMCR0_Bits',0,4,232,1,16,4,12
	.byte	'reserved_0',0,2
	.word	601
	.byte	16,0,2,35,0,12
	.byte	'PCL0',0,1
	.word	584
	.byte	1,7,2,35,2,12
	.byte	'PCL1',0,1
	.word	584
	.byte	1,6,2,35,2,12
	.byte	'PCL2',0,1
	.word	584
	.byte	1,5,2,35,2,12
	.byte	'PCL3',0,1
	.word	584
	.byte	1,4,2,35,2,12
	.byte	'reserved_20',0,2
	.word	601
	.byte	12,0,2,35,2,0,13,4,229,4,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	5112
	.byte	4,2,35,0,0,11
	.byte	'_Ifx_P_OMCR4_Bits',0,4,253,1,16,4,12
	.byte	'reserved_0',0,4
	.word	561
	.byte	20,12,2,35,0,12
	.byte	'PCL4',0,1
	.word	584
	.byte	1,3,2,35,2,12
	.byte	'PCL5',0,1
	.word	584
	.byte	1,2,2,35,2,12
	.byte	'PCL6',0,1
	.word	584
	.byte	1,1,2,35,2,12
	.byte	'PCL7',0,1
	.word	584
	.byte	1,0,2,35,2,12
	.byte	'reserved_24',0,1
	.word	584
	.byte	8,0,2,35,3,0,13,4,245,4,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	5286
	.byte	4,2,35,0,0,11
	.byte	'_Ifx_P_OMCR8_Bits',0,4,136,2,16,4,12
	.byte	'reserved_0',0,4
	.word	561
	.byte	24,8,2,35,0,12
	.byte	'PCL8',0,1
	.word	584
	.byte	1,7,2,35,3,12
	.byte	'PCL9',0,1
	.word	584
	.byte	1,6,2,35,3,12
	.byte	'PCL10',0,1
	.word	584
	.byte	1,5,2,35,3,12
	.byte	'PCL11',0,1
	.word	584
	.byte	1,4,2,35,3,12
	.byte	'reserved_28',0,1
	.word	584
	.byte	4,0,2,35,3,0,13,4,253,4,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	5460
	.byte	4,2,35,0,0,11
	.byte	'_Ifx_P_OMCR12_Bits',0,4,243,1,16,4,12
	.byte	'reserved_0',0,4
	.word	561
	.byte	28,4,2,35,0,12
	.byte	'PCL12',0,1
	.word	584
	.byte	1,3,2,35,3,12
	.byte	'PCL13',0,1
	.word	584
	.byte	1,2,2,35,3,12
	.byte	'PCL14',0,1
	.word	584
	.byte	1,1,2,35,3,12
	.byte	'PCL15',0,1
	.word	584
	.byte	1,0,2,35,3,0,13,4,237,4,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	5636
	.byte	4,2,35,0,0,11
	.byte	'_Ifx_P_OMSR_Bits',0,4,249,2,16,4,12
	.byte	'PS0',0,1
	.word	584
	.byte	1,7,2,35,0,12
	.byte	'PS1',0,1
	.word	584
	.byte	1,6,2,35,0,12
	.byte	'PS2',0,1
	.word	584
	.byte	1,5,2,35,0,12
	.byte	'PS3',0,1
	.word	584
	.byte	1,4,2,35,0,12
	.byte	'PS4',0,1
	.word	584
	.byte	1,3,2,35,0,12
	.byte	'PS5',0,1
	.word	584
	.byte	1,2,2,35,0,12
	.byte	'PS6',0,1
	.word	584
	.byte	1,1,2,35,0,12
	.byte	'PS7',0,1
	.word	584
	.byte	1,0,2,35,0,12
	.byte	'PS8',0,1
	.word	584
	.byte	1,7,2,35,1,12
	.byte	'PS9',0,1
	.word	584
	.byte	1,6,2,35,1,12
	.byte	'PS10',0,1
	.word	584
	.byte	1,5,2,35,1,12
	.byte	'PS11',0,1
	.word	584
	.byte	1,4,2,35,1,12
	.byte	'PS12',0,1
	.word	584
	.byte	1,3,2,35,1,12
	.byte	'PS13',0,1
	.word	584
	.byte	1,2,2,35,1,12
	.byte	'PS14',0,1
	.word	584
	.byte	1,1,2,35,1,12
	.byte	'PS15',0,1
	.word	584
	.byte	1,0,2,35,1,12
	.byte	'reserved_16',0,2
	.word	601
	.byte	16,0,2,35,2,0,13,4,141,5,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	5792
	.byte	4,2,35,0,0,11
	.byte	'_Ifx_P_OMCR_Bits',0,4,147,2,16,4,12
	.byte	'reserved_0',0,2
	.word	601
	.byte	16,0,2,35,0,12
	.byte	'PCL0',0,1
	.word	584
	.byte	1,7,2,35,2,12
	.byte	'PCL1',0,1
	.word	584
	.byte	1,6,2,35,2,12
	.byte	'PCL2',0,1
	.word	584
	.byte	1,5,2,35,2,12
	.byte	'PCL3',0,1
	.word	584
	.byte	1,4,2,35,2,12
	.byte	'PCL4',0,1
	.word	584
	.byte	1,3,2,35,2,12
	.byte	'PCL5',0,1
	.word	584
	.byte	1,2,2,35,2,12
	.byte	'PCL6',0,1
	.word	584
	.byte	1,1,2,35,2,12
	.byte	'PCL7',0,1
	.word	584
	.byte	1,0,2,35,2,12
	.byte	'PCL8',0,1
	.word	584
	.byte	1,7,2,35,3,12
	.byte	'PCL9',0,1
	.word	584
	.byte	1,6,2,35,3,12
	.byte	'PCL10',0,1
	.word	584
	.byte	1,5,2,35,3,12
	.byte	'PCL11',0,1
	.word	584
	.byte	1,4,2,35,3,12
	.byte	'PCL12',0,1
	.word	584
	.byte	1,3,2,35,3,12
	.byte	'PCL13',0,1
	.word	584
	.byte	1,2,2,35,3,12
	.byte	'PCL14',0,1
	.word	584
	.byte	1,1,2,35,3,12
	.byte	'PCL15',0,1
	.word	584
	.byte	1,0,2,35,3,0,13,4,221,4,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	6125
	.byte	4,2,35,0,0,11
	.byte	'_Ifx_P_LPCR0_Bits',0,4,192,1,16,4,12
	.byte	'reserved_0',0,1
	.word	584
	.byte	1,7,2,35,0,12
	.byte	'PS1',0,1
	.word	584
	.byte	1,6,2,35,0,12
	.byte	'reserved_2',0,4
	.word	561
	.byte	30,0,2,35,0,0,13,4,196,4,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	6473
	.byte	4,2,35,0,0,11
	.byte	'_Ifx_P_LPCR1_Bits',0,4,200,1,16,4,12
	.byte	'reserved_0',0,1
	.word	584
	.byte	1,7,2,35,0,12
	.byte	'PS1',0,1
	.word	584
	.byte	1,6,2,35,0,12
	.byte	'reserved_2',0,4
	.word	561
	.byte	30,0,2,35,0,0,11
	.byte	'_Ifx_P_LPCR1_P21_Bits',0,4,208,1,16,4,12
	.byte	'RDIS_CTRL',0,1
	.word	584
	.byte	1,7,2,35,0,12
	.byte	'RX_DIS',0,1
	.word	584
	.byte	1,6,2,35,0,12
	.byte	'TERM',0,1
	.word	584
	.byte	1,5,2,35,0,12
	.byte	'LRXTERM',0,1
	.word	584
	.byte	5,0,2,35,0,12
	.byte	'reserved_8',0,4
	.word	561
	.byte	24,0,2,35,0,0,13,4,204,4,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	6597
	.byte	4,2,35,0,14
	.byte	'B_P21',0
	.word	6681
	.byte	4,2,35,0,0,11
	.byte	'_Ifx_P_LPCR2_Bits',0,4,218,1,16,4,12
	.byte	'reserved_0',0,1
	.word	584
	.byte	8,0,2,35,0,12
	.byte	'LVDSR',0,1
	.word	584
	.byte	1,7,2,35,1,12
	.byte	'LVDSRL',0,1
	.word	584
	.byte	1,6,2,35,1,12
	.byte	'reserved_10',0,1
	.word	584
	.byte	2,4,2,35,1,12
	.byte	'TDIS_CTRL',0,1
	.word	584
	.byte	1,3,2,35,1,12
	.byte	'TX_DIS',0,1
	.word	584
	.byte	1,2,2,35,1,12
	.byte	'TX_PD',0,1
	.word	584
	.byte	1,1,2,35,1,12
	.byte	'TX_PWDPD',0,1
	.word	584
	.byte	1,0,2,35,1,12
	.byte	'reserved_16',0,2
	.word	601
	.byte	16,0,2,35,2,0,13,4,213,4,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	6861
	.byte	4,2,35,0,0,15,76
	.word	584
	.byte	16,75,0,11
	.byte	'_Ifx_P_ACCEN1_Bits',0,4,82,16,4,12
	.byte	'reserved_0',0,4
	.word	561
	.byte	32,0,2,35,0,0,13,4,132,4,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	7114
	.byte	4,2,35,0,0,11
	.byte	'_Ifx_P_ACCEN0_Bits',0,4,45,16,4,12
	.byte	'EN0',0,1
	.word	584
	.byte	1,7,2,35,0,12
	.byte	'EN1',0,1
	.word	584
	.byte	1,6,2,35,0,12
	.byte	'EN2',0,1
	.word	584
	.byte	1,5,2,35,0,12
	.byte	'EN3',0,1
	.word	584
	.byte	1,4,2,35,0,12
	.byte	'EN4',0,1
	.word	584
	.byte	1,3,2,35,0,12
	.byte	'EN5',0,1
	.word	584
	.byte	1,2,2,35,0,12
	.byte	'EN6',0,1
	.word	584
	.byte	1,1,2,35,0,12
	.byte	'EN7',0,1
	.word	584
	.byte	1,0,2,35,0,12
	.byte	'EN8',0,1
	.word	584
	.byte	1,7,2,35,1,12
	.byte	'EN9',0,1
	.word	584
	.byte	1,6,2,35,1,12
	.byte	'EN10',0,1
	.word	584
	.byte	1,5,2,35,1,12
	.byte	'EN11',0,1
	.word	584
	.byte	1,4,2,35,1,12
	.byte	'EN12',0,1
	.word	584
	.byte	1,3,2,35,1,12
	.byte	'EN13',0,1
	.word	584
	.byte	1,2,2,35,1,12
	.byte	'EN14',0,1
	.word	584
	.byte	1,1,2,35,1,12
	.byte	'EN15',0,1
	.word	584
	.byte	1,0,2,35,1,12
	.byte	'EN16',0,1
	.word	584
	.byte	1,7,2,35,2,12
	.byte	'EN17',0,1
	.word	584
	.byte	1,6,2,35,2,12
	.byte	'EN18',0,1
	.word	584
	.byte	1,5,2,35,2,12
	.byte	'EN19',0,1
	.word	584
	.byte	1,4,2,35,2,12
	.byte	'EN20',0,1
	.word	584
	.byte	1,3,2,35,2,12
	.byte	'EN21',0,1
	.word	584
	.byte	1,2,2,35,2,12
	.byte	'EN22',0,1
	.word	584
	.byte	1,1,2,35,2,12
	.byte	'EN23',0,1
	.word	584
	.byte	1,0,2,35,2,12
	.byte	'EN24',0,1
	.word	584
	.byte	1,7,2,35,3,12
	.byte	'EN25',0,1
	.word	584
	.byte	1,6,2,35,3,12
	.byte	'EN26',0,1
	.word	584
	.byte	1,5,2,35,3,12
	.byte	'EN27',0,1
	.word	584
	.byte	1,4,2,35,3,12
	.byte	'EN28',0,1
	.word	584
	.byte	1,3,2,35,3,12
	.byte	'EN29',0,1
	.word	584
	.byte	1,2,2,35,3,12
	.byte	'EN30',0,1
	.word	584
	.byte	1,1,2,35,3,12
	.byte	'EN31',0,1
	.word	584
	.byte	1,0,2,35,3,0,13,4,252,3,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	7201
	.byte	4,2,35,0,0,11
	.byte	'_Ifx_P',0,4,229,5,25,128,2,14
	.byte	'OUT',0
	.word	899
	.byte	4,2,35,0,14
	.byte	'OMR',0
	.word	1470
	.byte	4,2,35,4,14
	.byte	'ID',0
	.word	1589
	.byte	4,2,35,8,14
	.byte	'reserved_C',0
	.word	1629
	.byte	4,2,35,12,14
	.byte	'IOCR0',0
	.word	1813
	.byte	4,2,35,16,14
	.byte	'IOCR4',0
	.word	2028
	.byte	4,2,35,20,14
	.byte	'IOCR8',0
	.word	2245
	.byte	4,2,35,24,14
	.byte	'IOCR12',0
	.word	2465
	.byte	4,2,35,28,14
	.byte	'reserved_20',0
	.word	1629
	.byte	4,2,35,32,14
	.byte	'IN',0
	.word	2779
	.byte	4,2,35,36,14
	.byte	'reserved_28',0
	.word	2819
	.byte	24,2,35,40,14
	.byte	'PDR0',0
	.word	3092
	.byte	4,2,35,64,14
	.byte	'PDR1',0
	.word	3408
	.byte	4,2,35,68,14
	.byte	'reserved_48',0
	.word	3448
	.byte	8,2,35,72,14
	.byte	'ESR',0
	.word	3748
	.byte	4,2,35,80,14
	.byte	'reserved_54',0
	.word	3788
	.byte	12,2,35,84,14
	.byte	'PDISC',0
	.word	4123
	.byte	4,2,35,96,14
	.byte	'PCSR',0
	.word	4409
	.byte	4,2,35,100,14
	.byte	'reserved_68',0
	.word	3448
	.byte	8,2,35,104,14
	.byte	'OMSR0',0
	.word	4556
	.byte	4,2,35,112,14
	.byte	'OMSR4',0
	.word	4725
	.byte	4,2,35,116,14
	.byte	'OMSR8',0
	.word	4897
	.byte	4,2,35,120,14
	.byte	'OMSR12',0
	.word	5072
	.byte	4,2,35,124,14
	.byte	'OMCR0',0
	.word	5246
	.byte	4,3,35,128,1,14
	.byte	'OMCR4',0
	.word	5420
	.byte	4,3,35,132,1,14
	.byte	'OMCR8',0
	.word	5596
	.byte	4,3,35,136,1,14
	.byte	'OMCR12',0
	.word	5752
	.byte	4,3,35,140,1,14
	.byte	'OMSR',0
	.word	6085
	.byte	4,3,35,144,1,14
	.byte	'OMCR',0
	.word	6433
	.byte	4,3,35,148,1,14
	.byte	'reserved_98',0
	.word	3448
	.byte	8,3,35,152,1,14
	.byte	'LPCR0',0
	.word	6557
	.byte	4,3,35,160,1,14
	.byte	'LPCR1',0
	.word	6806
	.byte	4,3,35,164,1,14
	.byte	'LPCR2',0
	.word	7065
	.byte	4,3,35,168,1,14
	.byte	'reserved_A4',0
	.word	7105
	.byte	76,3,35,172,1,14
	.byte	'ACCEN1',0
	.word	7161
	.byte	4,3,35,248,1,14
	.byte	'ACCEN0',0
	.word	7728
	.byte	4,3,35,252,1,0,8
	.word	7768
.L150:
	.byte	3
	.word	8371
	.byte	17,3,172,1,9,4,18
	.byte	'IfxPort_State_notChanged',0,0,18
	.byte	'IfxPort_State_high',0,1,18
	.byte	'IfxPort_State_low',0,128,128,4,18
	.byte	'IfxPort_State_toggled',0,129,128,4,0,4
	.byte	'IfxPort_setPinState',0,3,3,208,4,17,1,1,5
	.byte	'port',0,3,208,4,44
	.word	8376
	.byte	5
	.byte	'pinIndex',0,3,208,4,56
	.word	584
	.byte	5
	.byte	'action',0,3,208,4,80
	.word	8381
	.byte	6,0,19
	.word	240
	.byte	20
	.word	266
	.byte	6,0,19
	.word	317
	.byte	20
	.word	339
	.byte	20
	.word	353
	.byte	20
	.word	367
	.byte	6,0,19
	.word	404
	.byte	20
	.word	436
	.byte	6,0,19
	.word	480
	.byte	20
	.word	499
	.byte	6,0,19
	.word	515
	.byte	20
	.word	530
	.byte	20
	.word	544
	.byte	6,0,21
	.byte	'IfxScuWdt_clearCpuEndinit',0,5,217,1,17,1,1,1,1,5
	.byte	'password',0,5,217,1,50
	.word	601
	.byte	0,21
	.byte	'IfxScuWdt_setCpuEndinit',0,5,239,1,17,1,1,1,1,5
	.byte	'password',0,5,239,1,48
	.word	601
	.byte	0,22
	.byte	'IfxScuWdt_getCpuWatchdogPassword',0,5,129,3,19
	.word	601
	.byte	1,1,1,1,19
	.word	8484
	.byte	20
	.word	8512
	.byte	20
	.word	8526
	.byte	20
	.word	8544
	.byte	6,0
.L153:
	.byte	9
	.byte	'long int',0,4,5
.L164:
	.byte	17,3,95,9,1,18
	.byte	'IfxPort_Mode_inputNoPullDevice',0,0,18
	.byte	'IfxPort_Mode_inputPullDown',0,8,18
	.byte	'IfxPort_Mode_inputPullUp',0,16,18
	.byte	'IfxPort_Mode_outputPushPullGeneral',0,128,1,18
	.byte	'IfxPort_Mode_outputPushPullAlt1',0,136,1,18
	.byte	'IfxPort_Mode_outputPushPullAlt2',0,144,1,18
	.byte	'IfxPort_Mode_outputPushPullAlt3',0,152,1,18
	.byte	'IfxPort_Mode_outputPushPullAlt4',0,160,1,18
	.byte	'IfxPort_Mode_outputPushPullAlt5',0,168,1,18
	.byte	'IfxPort_Mode_outputPushPullAlt6',0,176,1,18
	.byte	'IfxPort_Mode_outputPushPullAlt7',0,184,1,18
	.byte	'IfxPort_Mode_outputOpenDrainGeneral',0,192,1,18
	.byte	'IfxPort_Mode_outputOpenDrainAlt1',0,200,1,18
	.byte	'IfxPort_Mode_outputOpenDrainAlt2',0,208,1,18
	.byte	'IfxPort_Mode_outputOpenDrainAlt3',0,216,1,18
	.byte	'IfxPort_Mode_outputOpenDrainAlt4',0,224,1,18
	.byte	'IfxPort_Mode_outputOpenDrainAlt5',0,232,1,18
	.byte	'IfxPort_Mode_outputOpenDrainAlt6',0,240,1,18
	.byte	'IfxPort_Mode_outputOpenDrainAlt7',0,248,1,0,8
	.word	1813
.L166:
	.byte	3
	.word	9492
.L186:
	.byte	17,3,144,1,9,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed1',0,0,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed2',0,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed3',0,2,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed4',0,3,18
	.byte	'IfxPort_PadDriver_lvdsSpeed1',0,4,18
	.byte	'IfxPort_PadDriver_lvdsSpeed2',0,5,18
	.byte	'IfxPort_PadDriver_lvdsSpeed3',0,6,18
	.byte	'IfxPort_PadDriver_lvdsSpeed4',0,7,18
	.byte	'IfxPort_PadDriver_ttlSpeed1',0,8,18
	.byte	'IfxPort_PadDriver_ttlSpeed2',0,9,18
	.byte	'IfxPort_PadDriver_ttlSpeed3',0,10,18
	.byte	'IfxPort_PadDriver_ttlSpeed4',0,11,0,8
	.word	296
.L191:
	.byte	3
	.word	9917
.L200:
	.byte	17,6,79,9,1,18
	.byte	'IfxPort_Index_none',0,127,18
	.byte	'IfxPort_Index_00',0,0,18
	.byte	'IfxPort_Index_02',0,2,18
	.byte	'IfxPort_Index_10',0,10,18
	.byte	'IfxPort_Index_11',0,11,18
	.byte	'IfxPort_Index_13',0,13,18
	.byte	'IfxPort_Index_14',0,14,18
	.byte	'IfxPort_Index_15',0,15,18
	.byte	'IfxPort_Index_20',0,20,18
	.byte	'IfxPort_Index_21',0,21,18
	.byte	'IfxPort_Index_22',0,22,18
	.byte	'IfxPort_Index_23',0,23,18
	.byte	'IfxPort_Index_32',0,32,18
	.byte	'IfxPort_Index_33',0,33,0
.L213:
	.byte	17,3,83,9,1,18
	.byte	'IfxPort_InputMode_undefined',0,127,18
	.byte	'IfxPort_InputMode_noPullDevice',0,0,18
	.byte	'IfxPort_InputMode_pullDown',0,8,18
	.byte	'IfxPort_InputMode_pullUp',0,16,0
.L216:
	.byte	15,16
	.word	296
	.byte	16,3,0
.L232:
	.byte	17,3,134,1,9,1,18
	.byte	'IfxPort_OutputMode_pushPull',0,128,1,18
	.byte	'IfxPort_OutputMode_openDrain',0,192,1,0
.L234:
	.byte	17,3,120,9,1,18
	.byte	'IfxPort_OutputIdx_general',0,128,1,18
	.byte	'IfxPort_OutputIdx_alt1',0,136,1,18
	.byte	'IfxPort_OutputIdx_alt2',0,144,1,18
	.byte	'IfxPort_OutputIdx_alt3',0,152,1,18
	.byte	'IfxPort_OutputIdx_alt4',0,160,1,18
	.byte	'IfxPort_OutputIdx_alt5',0,168,1,18
	.byte	'IfxPort_OutputIdx_alt6',0,176,1,18
	.byte	'IfxPort_OutputIdx_alt7',0,184,1,0
.L257:
	.byte	15,8
	.word	296
	.byte	16,1,0
.L273:
	.byte	17,3,75,9,1,18
	.byte	'IfxPort_ControlledBy_port',0,0,18
	.byte	'IfxPort_ControlledBy_hsct',0,1,0
.L280:
	.byte	17,3,162,1,9,1,18
	.byte	'IfxPort_PadSupply_5v',0,0,18
	.byte	'IfxPort_PadSupply_3v',0,1,0,8
	.word	3092
.L285:
	.byte	3
	.word	10746
	.byte	8
	.word	6557
.L287:
	.byte	3
	.word	10756
	.byte	9
	.byte	'short int',0,2,5,23
	.byte	'__wchar_t',0,7,1,1
	.word	10766
	.byte	23
	.byte	'__size_t',0,7,1,1
	.word	561
	.byte	23
	.byte	'__ptrdiff_t',0,7,1,1
	.word	577
	.byte	24,1,3
	.word	10834
	.byte	23
	.byte	'__codeptr',0,7,1,1
	.word	10836
	.byte	23
	.byte	'boolean',0,8,101,29
	.word	584
	.byte	23
	.byte	'uint8',0,8,105,29
	.word	584
	.byte	23
	.byte	'uint16',0,8,109,29
	.word	601
	.byte	23
	.byte	'uint32',0,8,113,29
	.word	296
	.byte	23
	.byte	'uint64',0,8,118,29
	.word	449
	.byte	23
	.byte	'sint16',0,8,126,29
	.word	10766
	.byte	23
	.byte	'sint32',0,8,131,1,29
	.word	8811
	.byte	9
	.byte	'long long int',0,8,5,23
	.byte	'sint64',0,8,138,1,29
	.word	10965
	.byte	23
	.byte	'float32',0,8,167,1,29
	.word	395
	.byte	23
	.byte	'pvoid',0,9,57,28
	.word	475
	.byte	23
	.byte	'Ifx_TickTime',0,9,79,28
	.word	10965
	.byte	25,9,143,1,9,8,14
	.byte	'module',0
	.word	291
	.byte	4,2,35,0,14
	.byte	'index',0
	.word	8811
	.byte	4,2,35,4,0,23
	.byte	'IfxModule_IndexMap',0,9,147,1,3
	.word	11050
	.byte	23
	.byte	'Ifx_P_ACCEN0_Bits',0,4,79,3
	.word	7201
	.byte	23
	.byte	'Ifx_P_ACCEN1_Bits',0,4,85,3
	.word	7114
	.byte	23
	.byte	'Ifx_P_ESR_Bits',0,4,107,3
	.word	3457
	.byte	23
	.byte	'Ifx_P_ID_Bits',0,4,115,3
	.word	1510
	.byte	23
	.byte	'Ifx_P_IN_Bits',0,4,137,1,3
	.word	2505
	.byte	23
	.byte	'Ifx_P_IOCR0_Bits',0,4,150,1,3
	.word	1638
	.byte	23
	.byte	'Ifx_P_IOCR12_Bits',0,4,163,1,3
	.word	2285
	.byte	23
	.byte	'Ifx_P_IOCR4_Bits',0,4,176,1,3
	.word	1853
	.byte	23
	.byte	'Ifx_P_IOCR8_Bits',0,4,189,1,3
	.word	2068
	.byte	23
	.byte	'Ifx_P_LPCR0_Bits',0,4,197,1,3
	.word	6473
	.byte	23
	.byte	'Ifx_P_LPCR1_Bits',0,4,205,1,3
	.word	6597
	.byte	23
	.byte	'Ifx_P_LPCR1_P21_Bits',0,4,215,1,3
	.word	6681
	.byte	23
	.byte	'Ifx_P_LPCR2_Bits',0,4,229,1,3
	.word	6861
	.byte	23
	.byte	'Ifx_P_OMCR0_Bits',0,4,240,1,3
	.word	5112
	.byte	23
	.byte	'Ifx_P_OMCR12_Bits',0,4,250,1,3
	.word	5636
	.byte	23
	.byte	'Ifx_P_OMCR4_Bits',0,4,133,2,3
	.word	5286
	.byte	23
	.byte	'Ifx_P_OMCR8_Bits',0,4,144,2,3
	.word	5460
	.byte	23
	.byte	'Ifx_P_OMCR_Bits',0,4,166,2,3
	.word	6125
	.byte	23
	.byte	'Ifx_P_OMR_Bits',0,4,203,2,3
	.word	939
	.byte	23
	.byte	'Ifx_P_OMSR0_Bits',0,4,213,2,3
	.word	4449
	.byte	23
	.byte	'Ifx_P_OMSR12_Bits',0,4,224,2,3
	.word	4937
	.byte	23
	.byte	'Ifx_P_OMSR4_Bits',0,4,235,2,3
	.word	4596
	.byte	23
	.byte	'Ifx_P_OMSR8_Bits',0,4,246,2,3
	.word	4765
	.byte	23
	.byte	'Ifx_P_OMSR_Bits',0,4,140,3,3
	.word	5792
	.byte	23
	.byte	'Ifx_P_OUT_Bits',0,4,162,3,3
	.word	623
	.byte	23
	.byte	'Ifx_P_PCSR_Bits',0,4,180,3,3
	.word	4163
	.byte	23
	.byte	'Ifx_P_PDISC_Bits',0,4,202,3,3
	.word	3797
	.byte	23
	.byte	'Ifx_P_PDR0_Bits',0,4,223,3,3
	.word	2828
	.byte	23
	.byte	'Ifx_P_PDR1_Bits',0,4,244,3,3
	.word	3132
	.byte	23
	.byte	'Ifx_P_ACCEN0',0,4,129,4,3
	.word	7728
	.byte	23
	.byte	'Ifx_P_ACCEN1',0,4,137,4,3
	.word	7161
	.byte	23
	.byte	'Ifx_P_ESR',0,4,145,4,3
	.word	3748
	.byte	23
	.byte	'Ifx_P_ID',0,4,153,4,3
	.word	1589
	.byte	23
	.byte	'Ifx_P_IN',0,4,161,4,3
	.word	2779
	.byte	23
	.byte	'Ifx_P_IOCR0',0,4,169,4,3
	.word	1813
	.byte	23
	.byte	'Ifx_P_IOCR12',0,4,177,4,3
	.word	2465
	.byte	23
	.byte	'Ifx_P_IOCR4',0,4,185,4,3
	.word	2028
	.byte	23
	.byte	'Ifx_P_IOCR8',0,4,193,4,3
	.word	2245
	.byte	23
	.byte	'Ifx_P_LPCR0',0,4,201,4,3
	.word	6557
	.byte	23
	.byte	'Ifx_P_LPCR1',0,4,210,4,3
	.word	6806
	.byte	23
	.byte	'Ifx_P_LPCR2',0,4,218,4,3
	.word	7065
	.byte	23
	.byte	'Ifx_P_OMCR',0,4,226,4,3
	.word	6433
	.byte	23
	.byte	'Ifx_P_OMCR0',0,4,234,4,3
	.word	5246
	.byte	23
	.byte	'Ifx_P_OMCR12',0,4,242,4,3
	.word	5752
	.byte	23
	.byte	'Ifx_P_OMCR4',0,4,250,4,3
	.word	5420
	.byte	23
	.byte	'Ifx_P_OMCR8',0,4,130,5,3
	.word	5596
	.byte	23
	.byte	'Ifx_P_OMR',0,4,138,5,3
	.word	1470
	.byte	23
	.byte	'Ifx_P_OMSR',0,4,146,5,3
	.word	6085
	.byte	23
	.byte	'Ifx_P_OMSR0',0,4,154,5,3
	.word	4556
	.byte	23
	.byte	'Ifx_P_OMSR12',0,4,162,5,3
	.word	5072
	.byte	23
	.byte	'Ifx_P_OMSR4',0,4,170,5,3
	.word	4725
	.byte	23
	.byte	'Ifx_P_OMSR8',0,4,178,5,3
	.word	4897
	.byte	23
	.byte	'Ifx_P_OUT',0,4,186,5,3
	.word	899
	.byte	23
	.byte	'Ifx_P_PCSR',0,4,194,5,3
	.word	4409
	.byte	23
	.byte	'Ifx_P_PDISC',0,4,202,5,3
	.word	4123
	.byte	23
	.byte	'Ifx_P_PDR0',0,4,210,5,3
	.word	3092
	.byte	23
	.byte	'Ifx_P_PDR1',0,4,218,5,3
	.word	3408
	.byte	8
	.word	7768
	.byte	23
	.byte	'Ifx_P',0,4,139,6,3
	.word	12434
	.byte	23
	.byte	'IfxPort_Index',0,6,95,3
	.word	9927
	.byte	25,6,103,9,8,14
	.byte	'port',0
	.word	8376
	.byte	4,2,35,0,14
	.byte	'masks',0
	.word	601
	.byte	2,2,35,4,0,23
	.byte	'IfxPort_Esr_Masks',0,6,107,3
	.word	12476
	.byte	15,104
	.word	12476
	.byte	16,12,0,26
	.word	12537
	.byte	27
	.byte	'IfxPort_cfg_esrMasks',0,6,113,41
	.word	12546
	.byte	1,1,15,104
	.word	11050
	.byte	16,12,0,26
	.word	12582
	.byte	27
	.byte	'IfxPort_cfg_indexMap',0,6,115,41
	.word	12591
	.byte	1,1,17,10,250,10,9,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy16384',0,0,18
	.byte	'IfxScu_WDTCON1_IR_divBy256',0,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy64',0,2,0,23
	.byte	'IfxScu_WDTCON1_IR',0,10,255,10,3
	.word	12627
	.byte	11
	.byte	'_Ifx_SCU_ACCEN0_Bits',0,11,45,16,4,12
	.byte	'EN0',0,1
	.word	584
	.byte	1,7,2,35,0,12
	.byte	'EN1',0,1
	.word	584
	.byte	1,6,2,35,0,12
	.byte	'EN2',0,1
	.word	584
	.byte	1,5,2,35,0,12
	.byte	'EN3',0,1
	.word	584
	.byte	1,4,2,35,0,12
	.byte	'EN4',0,1
	.word	584
	.byte	1,3,2,35,0,12
	.byte	'EN5',0,1
	.word	584
	.byte	1,2,2,35,0,12
	.byte	'EN6',0,1
	.word	584
	.byte	1,1,2,35,0,12
	.byte	'EN7',0,1
	.word	584
	.byte	1,0,2,35,0,12
	.byte	'EN8',0,1
	.word	584
	.byte	1,7,2,35,1,12
	.byte	'EN9',0,1
	.word	584
	.byte	1,6,2,35,1,12
	.byte	'EN10',0,1
	.word	584
	.byte	1,5,2,35,1,12
	.byte	'EN11',0,1
	.word	584
	.byte	1,4,2,35,1,12
	.byte	'EN12',0,1
	.word	584
	.byte	1,3,2,35,1,12
	.byte	'EN13',0,1
	.word	584
	.byte	1,2,2,35,1,12
	.byte	'EN14',0,1
	.word	584
	.byte	1,1,2,35,1,12
	.byte	'EN15',0,1
	.word	584
	.byte	1,0,2,35,1,12
	.byte	'EN16',0,1
	.word	584
	.byte	1,7,2,35,2,12
	.byte	'EN17',0,1
	.word	584
	.byte	1,6,2,35,2,12
	.byte	'EN18',0,1
	.word	584
	.byte	1,5,2,35,2,12
	.byte	'EN19',0,1
	.word	584
	.byte	1,4,2,35,2,12
	.byte	'EN20',0,1
	.word	584
	.byte	1,3,2,35,2,12
	.byte	'EN21',0,1
	.word	584
	.byte	1,2,2,35,2,12
	.byte	'EN22',0,1
	.word	584
	.byte	1,1,2,35,2,12
	.byte	'EN23',0,1
	.word	584
	.byte	1,0,2,35,2,12
	.byte	'EN24',0,1
	.word	584
	.byte	1,7,2,35,3,12
	.byte	'EN25',0,1
	.word	584
	.byte	1,6,2,35,3,12
	.byte	'EN26',0,1
	.word	584
	.byte	1,5,2,35,3,12
	.byte	'EN27',0,1
	.word	584
	.byte	1,4,2,35,3,12
	.byte	'EN28',0,1
	.word	584
	.byte	1,3,2,35,3,12
	.byte	'EN29',0,1
	.word	584
	.byte	1,2,2,35,3,12
	.byte	'EN30',0,1
	.word	584
	.byte	1,1,2,35,3,12
	.byte	'EN31',0,1
	.word	584
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_ACCEN0_Bits',0,11,79,3
	.word	12749
	.byte	11
	.byte	'_Ifx_SCU_ACCEN1_Bits',0,11,82,16,4,12
	.byte	'reserved_0',0,4
	.word	561
	.byte	32,0,2,35,0,0,23
	.byte	'Ifx_SCU_ACCEN1_Bits',0,11,85,3
	.word	13306
	.byte	11
	.byte	'_Ifx_SCU_ARSTDIS_Bits',0,11,88,16,4,12
	.byte	'STM0DIS',0,1
	.word	584
	.byte	1,7,2,35,0,12
	.byte	'STM1DIS',0,1
	.word	584
	.byte	1,6,2,35,0,12
	.byte	'STM2DIS',0,1
	.word	584
	.byte	1,5,2,35,0,12
	.byte	'reserved_3',0,4
	.word	561
	.byte	29,0,2,35,0,0,23
	.byte	'Ifx_SCU_ARSTDIS_Bits',0,11,94,3
	.word	13383
	.byte	11
	.byte	'_Ifx_SCU_CCUCON0_Bits',0,11,97,16,4,12
	.byte	'BAUD1DIV',0,1
	.word	584
	.byte	4,4,2,35,0,12
	.byte	'BAUD2DIV',0,1
	.word	584
	.byte	4,0,2,35,0,12
	.byte	'SRIDIV',0,1
	.word	584
	.byte	4,4,2,35,1,12
	.byte	'LPDIV',0,1
	.word	584
	.byte	4,0,2,35,1,12
	.byte	'SPBDIV',0,1
	.word	584
	.byte	4,4,2,35,2,12
	.byte	'FSI2DIV',0,1
	.word	584
	.byte	2,2,2,35,2,12
	.byte	'reserved_22',0,1
	.word	584
	.byte	2,0,2,35,2,12
	.byte	'FSIDIV',0,1
	.word	584
	.byte	2,6,2,35,3,12
	.byte	'reserved_26',0,1
	.word	584
	.byte	2,4,2,35,3,12
	.byte	'CLKSEL',0,1
	.word	584
	.byte	2,2,2,35,3,12
	.byte	'UP',0,1
	.word	584
	.byte	1,1,2,35,3,12
	.byte	'LCK',0,1
	.word	584
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_CCUCON0_Bits',0,11,111,3
	.word	13519
	.byte	11
	.byte	'_Ifx_SCU_CCUCON1_Bits',0,11,114,16,4,12
	.byte	'CANDIV',0,1
	.word	584
	.byte	4,4,2,35,0,12
	.byte	'ERAYDIV',0,1
	.word	584
	.byte	4,0,2,35,0,12
	.byte	'STMDIV',0,1
	.word	584
	.byte	4,4,2,35,1,12
	.byte	'GTMDIV',0,1
	.word	584
	.byte	4,0,2,35,1,12
	.byte	'ETHDIV',0,1
	.word	584
	.byte	4,4,2,35,2,12
	.byte	'ASCLINFDIV',0,1
	.word	584
	.byte	4,0,2,35,2,12
	.byte	'ASCLINSDIV',0,1
	.word	584
	.byte	4,4,2,35,3,12
	.byte	'INSEL',0,1
	.word	584
	.byte	2,2,2,35,3,12
	.byte	'UP',0,1
	.word	584
	.byte	1,1,2,35,3,12
	.byte	'LCK',0,1
	.word	584
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_CCUCON1_Bits',0,11,126,3
	.word	13799
	.byte	11
	.byte	'_Ifx_SCU_CCUCON2_Bits',0,11,129,1,16,4,12
	.byte	'BBBDIV',0,1
	.word	584
	.byte	4,4,2,35,0,12
	.byte	'reserved_4',0,4
	.word	561
	.byte	26,2,2,35,0,12
	.byte	'UP',0,1
	.word	584
	.byte	1,1,2,35,3,12
	.byte	'LCK',0,1
	.word	584
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_CCUCON2_Bits',0,11,135,1,3
	.word	14037
	.byte	11
	.byte	'_Ifx_SCU_CCUCON3_Bits',0,11,138,1,16,4,12
	.byte	'PLLDIV',0,1
	.word	584
	.byte	6,2,2,35,0,12
	.byte	'PLLSEL',0,1
	.word	584
	.byte	2,0,2,35,0,12
	.byte	'PLLERAYDIV',0,1
	.word	584
	.byte	6,2,2,35,1,12
	.byte	'PLLERAYSEL',0,1
	.word	584
	.byte	2,0,2,35,1,12
	.byte	'SRIDIV',0,1
	.word	584
	.byte	6,2,2,35,2,12
	.byte	'SRISEL',0,1
	.word	584
	.byte	2,0,2,35,2,12
	.byte	'reserved_24',0,1
	.word	584
	.byte	5,3,2,35,3,12
	.byte	'SLCK',0,1
	.word	584
	.byte	1,2,2,35,3,12
	.byte	'UP',0,1
	.word	584
	.byte	1,1,2,35,3,12
	.byte	'LCK',0,1
	.word	584
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_CCUCON3_Bits',0,11,150,1,3
	.word	14165
	.byte	11
	.byte	'_Ifx_SCU_CCUCON4_Bits',0,11,153,1,16,4,12
	.byte	'SPBDIV',0,1
	.word	584
	.byte	6,2,2,35,0,12
	.byte	'SPBSEL',0,1
	.word	584
	.byte	2,0,2,35,0,12
	.byte	'GTMDIV',0,1
	.word	584
	.byte	6,2,2,35,1,12
	.byte	'GTMSEL',0,1
	.word	584
	.byte	2,0,2,35,1,12
	.byte	'STMDIV',0,1
	.word	584
	.byte	6,2,2,35,2,12
	.byte	'STMSEL',0,1
	.word	584
	.byte	2,0,2,35,2,12
	.byte	'reserved_24',0,1
	.word	584
	.byte	5,3,2,35,3,12
	.byte	'SLCK',0,1
	.word	584
	.byte	1,2,2,35,3,12
	.byte	'UP',0,1
	.word	584
	.byte	1,1,2,35,3,12
	.byte	'LCK',0,1
	.word	584
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_CCUCON4_Bits',0,11,165,1,3
	.word	14408
	.byte	11
	.byte	'_Ifx_SCU_CCUCON5_Bits',0,11,168,1,16,4,12
	.byte	'MAXDIV',0,1
	.word	584
	.byte	4,4,2,35,0,12
	.byte	'reserved_4',0,4
	.word	561
	.byte	26,2,2,35,0,12
	.byte	'UP',0,1
	.word	584
	.byte	1,1,2,35,3,12
	.byte	'LCK',0,1
	.word	584
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_CCUCON5_Bits',0,11,174,1,3
	.word	14643
	.byte	11
	.byte	'_Ifx_SCU_CCUCON6_Bits',0,11,177,1,16,4,12
	.byte	'CPU0DIV',0,1
	.word	584
	.byte	6,2,2,35,0,12
	.byte	'reserved_6',0,4
	.word	561
	.byte	26,0,2,35,0,0,23
	.byte	'Ifx_SCU_CCUCON6_Bits',0,11,181,1,3
	.word	14771
	.byte	11
	.byte	'_Ifx_SCU_CCUCON7_Bits',0,11,184,1,16,4,12
	.byte	'CPU1DIV',0,1
	.word	584
	.byte	6,2,2,35,0,12
	.byte	'reserved_6',0,4
	.word	561
	.byte	26,0,2,35,0,0,23
	.byte	'Ifx_SCU_CCUCON7_Bits',0,11,188,1,3
	.word	14871
	.byte	11
	.byte	'_Ifx_SCU_CHIPID_Bits',0,11,191,1,16,4,12
	.byte	'CHREV',0,1
	.word	584
	.byte	6,2,2,35,0,12
	.byte	'CHTEC',0,1
	.word	584
	.byte	2,0,2,35,0,12
	.byte	'CHID',0,1
	.word	584
	.byte	8,0,2,35,1,12
	.byte	'EEA',0,1
	.word	584
	.byte	1,7,2,35,2,12
	.byte	'UCODE',0,1
	.word	584
	.byte	7,0,2,35,2,12
	.byte	'FSIZE',0,1
	.word	584
	.byte	4,4,2,35,3,12
	.byte	'SP',0,1
	.word	584
	.byte	2,2,2,35,3,12
	.byte	'SEC',0,1
	.word	584
	.byte	1,1,2,35,3,12
	.byte	'reserved_31',0,1
	.word	584
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_CHIPID_Bits',0,11,202,1,3
	.word	14971
	.byte	11
	.byte	'_Ifx_SCU_DTSCON_Bits',0,11,205,1,16,4,12
	.byte	'PWD',0,1
	.word	584
	.byte	1,7,2,35,0,12
	.byte	'START',0,1
	.word	584
	.byte	1,6,2,35,0,12
	.byte	'reserved_2',0,1
	.word	584
	.byte	2,4,2,35,0,12
	.byte	'CAL',0,4
	.word	561
	.byte	20,8,2,35,0,12
	.byte	'reserved_24',0,1
	.word	584
	.byte	7,1,2,35,3,12
	.byte	'SLCK',0,1
	.word	584
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_DTSCON_Bits',0,11,213,1,3
	.word	15179
	.byte	11
	.byte	'_Ifx_SCU_DTSLIM_Bits',0,11,216,1,16,4,12
	.byte	'LOWER',0,2
	.word	601
	.byte	10,6,2,35,0,12
	.byte	'reserved_10',0,1
	.word	584
	.byte	5,1,2,35,1,12
	.byte	'LLU',0,1
	.word	584
	.byte	1,0,2,35,1,12
	.byte	'UPPER',0,2
	.word	601
	.byte	10,6,2,35,2,12
	.byte	'reserved_26',0,1
	.word	584
	.byte	4,2,2,35,3,12
	.byte	'SLCK',0,1
	.word	584
	.byte	1,1,2,35,3,12
	.byte	'UOF',0,1
	.word	584
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_DTSLIM_Bits',0,11,225,1,3
	.word	15344
	.byte	11
	.byte	'_Ifx_SCU_DTSSTAT_Bits',0,11,228,1,16,4,12
	.byte	'RESULT',0,2
	.word	601
	.byte	10,6,2,35,0,12
	.byte	'reserved_10',0,1
	.word	584
	.byte	4,2,2,35,1,12
	.byte	'RDY',0,1
	.word	584
	.byte	1,1,2,35,1,12
	.byte	'BUSY',0,1
	.word	584
	.byte	1,0,2,35,1,12
	.byte	'reserved_16',0,2
	.word	601
	.byte	16,0,2,35,2,0,23
	.byte	'Ifx_SCU_DTSSTAT_Bits',0,11,235,1,3
	.word	15527
	.byte	11
	.byte	'_Ifx_SCU_EICR_Bits',0,11,238,1,16,4,12
	.byte	'reserved_0',0,1
	.word	584
	.byte	4,4,2,35,0,12
	.byte	'EXIS0',0,1
	.word	584
	.byte	3,1,2,35,0,12
	.byte	'reserved_7',0,1
	.word	584
	.byte	1,0,2,35,0,12
	.byte	'FEN0',0,1
	.word	584
	.byte	1,7,2,35,1,12
	.byte	'REN0',0,1
	.word	584
	.byte	1,6,2,35,1,12
	.byte	'LDEN0',0,1
	.word	584
	.byte	1,5,2,35,1,12
	.byte	'EIEN0',0,1
	.word	584
	.byte	1,4,2,35,1,12
	.byte	'INP0',0,1
	.word	584
	.byte	3,1,2,35,1,12
	.byte	'reserved_15',0,4
	.word	561
	.byte	5,12,2,35,0,12
	.byte	'EXIS1',0,1
	.word	584
	.byte	3,1,2,35,2,12
	.byte	'reserved_23',0,1
	.word	584
	.byte	1,0,2,35,2,12
	.byte	'FEN1',0,1
	.word	584
	.byte	1,7,2,35,3,12
	.byte	'REN1',0,1
	.word	584
	.byte	1,6,2,35,3,12
	.byte	'LDEN1',0,1
	.word	584
	.byte	1,5,2,35,3,12
	.byte	'EIEN1',0,1
	.word	584
	.byte	1,4,2,35,3,12
	.byte	'INP1',0,1
	.word	584
	.byte	3,1,2,35,3,12
	.byte	'reserved_31',0,1
	.word	584
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_EICR_Bits',0,11,129,2,3
	.word	15681
	.byte	11
	.byte	'_Ifx_SCU_EIFR_Bits',0,11,132,2,16,4,12
	.byte	'INTF0',0,1
	.word	584
	.byte	1,7,2,35,0,12
	.byte	'INTF1',0,1
	.word	584
	.byte	1,6,2,35,0,12
	.byte	'INTF2',0,1
	.word	584
	.byte	1,5,2,35,0,12
	.byte	'INTF3',0,1
	.word	584
	.byte	1,4,2,35,0,12
	.byte	'INTF4',0,1
	.word	584
	.byte	1,3,2,35,0,12
	.byte	'INTF5',0,1
	.word	584
	.byte	1,2,2,35,0,12
	.byte	'INTF6',0,1
	.word	584
	.byte	1,1,2,35,0,12
	.byte	'INTF7',0,1
	.word	584
	.byte	1,0,2,35,0,12
	.byte	'reserved_8',0,4
	.word	561
	.byte	24,0,2,35,0,0,23
	.byte	'Ifx_SCU_EIFR_Bits',0,11,143,2,3
	.word	16045
	.byte	11
	.byte	'_Ifx_SCU_EMSR_Bits',0,11,146,2,16,4,12
	.byte	'POL',0,1
	.word	584
	.byte	1,7,2,35,0,12
	.byte	'MODE',0,1
	.word	584
	.byte	1,6,2,35,0,12
	.byte	'ENON',0,1
	.word	584
	.byte	1,5,2,35,0,12
	.byte	'PSEL',0,1
	.word	584
	.byte	1,4,2,35,0,12
	.byte	'reserved_4',0,2
	.word	601
	.byte	12,0,2,35,0,12
	.byte	'EMSF',0,1
	.word	584
	.byte	1,7,2,35,2,12
	.byte	'SEMSF',0,1
	.word	584
	.byte	1,6,2,35,2,12
	.byte	'reserved_18',0,1
	.word	584
	.byte	6,0,2,35,2,12
	.byte	'EMSFM',0,1
	.word	584
	.byte	2,6,2,35,3,12
	.byte	'SEMSFM',0,1
	.word	584
	.byte	2,4,2,35,3,12
	.byte	'reserved_28',0,1
	.word	584
	.byte	4,0,2,35,3,0,23
	.byte	'Ifx_SCU_EMSR_Bits',0,11,159,2,3
	.word	16256
	.byte	11
	.byte	'_Ifx_SCU_ESRCFG_Bits',0,11,162,2,16,4,12
	.byte	'reserved_0',0,1
	.word	584
	.byte	7,1,2,35,0,12
	.byte	'EDCON',0,2
	.word	601
	.byte	2,7,2,35,0,12
	.byte	'reserved_9',0,4
	.word	561
	.byte	23,0,2,35,0,0,23
	.byte	'Ifx_SCU_ESRCFG_Bits',0,11,167,2,3
	.word	16508
	.byte	11
	.byte	'_Ifx_SCU_ESROCFG_Bits',0,11,170,2,16,4,12
	.byte	'ARI',0,1
	.word	584
	.byte	1,7,2,35,0,12
	.byte	'ARC',0,1
	.word	584
	.byte	1,6,2,35,0,12
	.byte	'reserved_2',0,4
	.word	561
	.byte	30,0,2,35,0,0,23
	.byte	'Ifx_SCU_ESROCFG_Bits',0,11,175,2,3
	.word	16626
	.byte	11
	.byte	'_Ifx_SCU_EVR13CON_Bits',0,11,178,2,16,4,12
	.byte	'reserved_0',0,4
	.word	561
	.byte	28,4,2,35,0,12
	.byte	'EVR13OFF',0,1
	.word	584
	.byte	1,3,2,35,3,12
	.byte	'BPEVR13OFF',0,1
	.word	584
	.byte	1,2,2,35,3,12
	.byte	'reserved_30',0,1
	.word	584
	.byte	1,1,2,35,3,12
	.byte	'LCK',0,1
	.word	584
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_EVR13CON_Bits',0,11,185,2,3
	.word	16737
	.byte	11
	.byte	'_Ifx_SCU_EVR33CON_Bits',0,11,188,2,16,4,12
	.byte	'reserved_0',0,4
	.word	561
	.byte	28,4,2,35,0,12
	.byte	'EVR33OFF',0,1
	.word	584
	.byte	1,3,2,35,3,12
	.byte	'BPEVR33OFF',0,1
	.word	584
	.byte	1,2,2,35,3,12
	.byte	'reserved_30',0,1
	.word	584
	.byte	1,1,2,35,3,12
	.byte	'LCK',0,1
	.word	584
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_EVR33CON_Bits',0,11,195,2,3
	.word	16900
	.byte	11
	.byte	'_Ifx_SCU_EVRADCSTAT_Bits',0,11,198,2,16,4,12
	.byte	'ADC13V',0,1
	.word	584
	.byte	8,0,2,35,0,12
	.byte	'ADC33V',0,1
	.word	584
	.byte	8,0,2,35,1,12
	.byte	'ADCSWDV',0,1
	.word	584
	.byte	8,0,2,35,2,12
	.byte	'reserved_24',0,1
	.word	584
	.byte	7,1,2,35,3,12
	.byte	'VAL',0,1
	.word	584
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_EVRADCSTAT_Bits',0,11,205,2,3
	.word	17063
	.byte	11
	.byte	'_Ifx_SCU_EVRDVSTAT_Bits',0,11,208,2,16,4,12
	.byte	'DVS13TRIM',0,1
	.word	584
	.byte	8,0,2,35,0,12
	.byte	'reserved_8',0,1
	.word	584
	.byte	8,0,2,35,1,12
	.byte	'DVS33TRIM',0,1
	.word	584
	.byte	8,0,2,35,2,12
	.byte	'reserved_24',0,1
	.word	584
	.byte	7,1,2,35,3,12
	.byte	'VAL',0,1
	.word	584
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_EVRDVSTAT_Bits',0,11,215,2,3
	.word	17221
	.byte	11
	.byte	'_Ifx_SCU_EVRMONCTRL_Bits',0,11,218,2,16,4,12
	.byte	'EVR13OVMOD',0,1
	.word	584
	.byte	2,6,2,35,0,12
	.byte	'reserved_2',0,1
	.word	584
	.byte	2,4,2,35,0,12
	.byte	'EVR13UVMOD',0,1
	.word	584
	.byte	2,2,2,35,0,12
	.byte	'reserved_6',0,1
	.word	584
	.byte	2,0,2,35,0,12
	.byte	'EVR33OVMOD',0,1
	.word	584
	.byte	2,6,2,35,1,12
	.byte	'reserved_10',0,1
	.word	584
	.byte	2,4,2,35,1,12
	.byte	'EVR33UVMOD',0,1
	.word	584
	.byte	2,2,2,35,1,12
	.byte	'reserved_14',0,1
	.word	584
	.byte	2,0,2,35,1,12
	.byte	'SWDOVMOD',0,1
	.word	584
	.byte	2,6,2,35,2,12
	.byte	'reserved_18',0,1
	.word	584
	.byte	2,4,2,35,2,12
	.byte	'SWDUVMOD',0,1
	.word	584
	.byte	2,2,2,35,2,12
	.byte	'reserved_22',0,2
	.word	601
	.byte	10,0,2,35,2,0,23
	.byte	'Ifx_SCU_EVRMONCTRL_Bits',0,11,232,2,3
	.word	17386
	.byte	11
	.byte	'_Ifx_SCU_EVROSCCTRL_Bits',0,11,235,2,16,4,12
	.byte	'OSCTRIM',0,2
	.word	601
	.byte	10,6,2,35,0,12
	.byte	'OSCPTAT',0,1
	.word	584
	.byte	6,0,2,35,1,12
	.byte	'OSCANASEL',0,1
	.word	584
	.byte	4,4,2,35,2,12
	.byte	'HPBGTRIM',0,2
	.word	601
	.byte	7,5,2,35,2,12
	.byte	'HPBGCLKEN',0,1
	.word	584
	.byte	1,4,2,35,3,12
	.byte	'OSC3V3',0,1
	.word	584
	.byte	1,3,2,35,3,12
	.byte	'reserved_29',0,1
	.word	584
	.byte	2,1,2,35,3,12
	.byte	'LCK',0,1
	.word	584
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_EVROSCCTRL_Bits',0,11,245,2,3
	.word	17715
	.byte	11
	.byte	'_Ifx_SCU_EVROVMON_Bits',0,11,248,2,16,4,12
	.byte	'EVR13OVVAL',0,1
	.word	584
	.byte	8,0,2,35,0,12
	.byte	'EVR33OVVAL',0,1
	.word	584
	.byte	8,0,2,35,1,12
	.byte	'SWDOVVAL',0,1
	.word	584
	.byte	8,0,2,35,2,12
	.byte	'reserved_24',0,1
	.word	584
	.byte	7,1,2,35,3,12
	.byte	'LCK',0,1
	.word	584
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_EVROVMON_Bits',0,11,255,2,3
	.word	17936
	.byte	11
	.byte	'_Ifx_SCU_EVRRSTCON_Bits',0,11,130,3,16,4,12
	.byte	'RST13TRIM',0,1
	.word	584
	.byte	8,0,2,35,0,12
	.byte	'reserved_8',0,4
	.word	561
	.byte	16,8,2,35,0,12
	.byte	'RST13OFF',0,1
	.word	584
	.byte	1,7,2,35,3,12
	.byte	'BPRST13OFF',0,1
	.word	584
	.byte	1,6,2,35,3,12
	.byte	'RST33OFF',0,1
	.word	584
	.byte	1,5,2,35,3,12
	.byte	'BPRST33OFF',0,1
	.word	584
	.byte	1,4,2,35,3,12
	.byte	'RSTSWDOFF',0,1
	.word	584
	.byte	1,3,2,35,3,12
	.byte	'BPRSTSWDOFF',0,1
	.word	584
	.byte	1,2,2,35,3,12
	.byte	'reserved_30',0,1
	.word	584
	.byte	1,1,2,35,3,12
	.byte	'LCK',0,1
	.word	584
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_EVRRSTCON_Bits',0,11,142,3,3
	.word	18099
	.byte	11
	.byte	'_Ifx_SCU_EVRSDCOEFF1_Bits',0,11,145,3,16,4,12
	.byte	'SD5P',0,1
	.word	584
	.byte	8,0,2,35,0,12
	.byte	'SD5I',0,1
	.word	584
	.byte	8,0,2,35,1,12
	.byte	'SD5D',0,1
	.word	584
	.byte	8,0,2,35,2,12
	.byte	'reserved_24',0,1
	.word	584
	.byte	7,1,2,35,3,12
	.byte	'LCK',0,1
	.word	584
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_EVRSDCOEFF1_Bits',0,11,152,3,3
	.word	18371
	.byte	11
	.byte	'_Ifx_SCU_EVRSDCOEFF2_Bits',0,11,155,3,16,4,12
	.byte	'SD33P',0,1
	.word	584
	.byte	8,0,2,35,0,12
	.byte	'SD33I',0,1
	.word	584
	.byte	8,0,2,35,1,12
	.byte	'SD33D',0,1
	.word	584
	.byte	8,0,2,35,2,12
	.byte	'reserved_24',0,1
	.word	584
	.byte	7,1,2,35,3,12
	.byte	'LCK',0,1
	.word	584
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_EVRSDCOEFF2_Bits',0,11,162,3,3
	.word	18524
	.byte	11
	.byte	'_Ifx_SCU_EVRSDCOEFF3_Bits',0,11,165,3,16,4,12
	.byte	'CT5REG0',0,1
	.word	584
	.byte	8,0,2,35,0,12
	.byte	'CT5REG1',0,1
	.word	584
	.byte	8,0,2,35,1,12
	.byte	'CT5REG2',0,1
	.word	584
	.byte	8,0,2,35,2,12
	.byte	'reserved_24',0,1
	.word	584
	.byte	7,1,2,35,3,12
	.byte	'LCK',0,1
	.word	584
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_EVRSDCOEFF3_Bits',0,11,172,3,3
	.word	18680
	.byte	11
	.byte	'_Ifx_SCU_EVRSDCOEFF4_Bits',0,11,175,3,16,4,12
	.byte	'CT5REG3',0,1
	.word	584
	.byte	8,0,2,35,0,12
	.byte	'CT5REG4',0,1
	.word	584
	.byte	8,0,2,35,1,12
	.byte	'reserved_16',0,2
	.word	601
	.byte	15,1,2,35,2,12
	.byte	'LCK',0,1
	.word	584
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_EVRSDCOEFF4_Bits',0,11,181,3,3
	.word	18842
	.byte	11
	.byte	'_Ifx_SCU_EVRSDCOEFF5_Bits',0,11,184,3,16,4,12
	.byte	'CT33REG0',0,1
	.word	584
	.byte	8,0,2,35,0,12
	.byte	'CT33REG1',0,1
	.word	584
	.byte	8,0,2,35,1,12
	.byte	'CT33REG2',0,1
	.word	584
	.byte	8,0,2,35,2,12
	.byte	'reserved_24',0,1
	.word	584
	.byte	7,1,2,35,3,12
	.byte	'LCK',0,1
	.word	584
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_EVRSDCOEFF5_Bits',0,11,191,3,3
	.word	18985
	.byte	11
	.byte	'_Ifx_SCU_EVRSDCOEFF6_Bits',0,11,194,3,16,4,12
	.byte	'CT33REG3',0,1
	.word	584
	.byte	8,0,2,35,0,12
	.byte	'CT33REG4',0,1
	.word	584
	.byte	8,0,2,35,1,12
	.byte	'reserved_16',0,2
	.word	601
	.byte	15,1,2,35,2,12
	.byte	'LCK',0,1
	.word	584
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_EVRSDCOEFF6_Bits',0,11,200,3,3
	.word	19150
	.byte	11
	.byte	'_Ifx_SCU_EVRSDCTRL1_Bits',0,11,203,3,16,4,12
	.byte	'SDFREQSPRD',0,2
	.word	601
	.byte	16,0,2,35,0,12
	.byte	'SDFREQ',0,1
	.word	584
	.byte	8,0,2,35,2,12
	.byte	'SDSTEP',0,1
	.word	584
	.byte	4,4,2,35,3,12
	.byte	'reserved_28',0,1
	.word	584
	.byte	2,2,2,35,3,12
	.byte	'SDSAMPLE',0,1
	.word	584
	.byte	1,1,2,35,3,12
	.byte	'LCK',0,1
	.word	584
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_EVRSDCTRL1_Bits',0,11,211,3,3
	.word	19295
	.byte	11
	.byte	'_Ifx_SCU_EVRSDCTRL2_Bits',0,11,214,3,16,4,12
	.byte	'DRVP',0,1
	.word	584
	.byte	8,0,2,35,0,12
	.byte	'SDMINMAXDC',0,1
	.word	584
	.byte	8,0,2,35,1,12
	.byte	'DRVN',0,1
	.word	584
	.byte	8,0,2,35,2,12
	.byte	'SDLUT',0,1
	.word	584
	.byte	6,2,2,35,3,12
	.byte	'reserved_30',0,1
	.word	584
	.byte	1,1,2,35,3,12
	.byte	'LCK',0,1
	.word	584
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_EVRSDCTRL2_Bits',0,11,222,3,3
	.word	19476
	.byte	11
	.byte	'_Ifx_SCU_EVRSDCTRL3_Bits',0,11,225,3,16,4,12
	.byte	'SDPWMPRE',0,1
	.word	584
	.byte	8,0,2,35,0,12
	.byte	'SDPID',0,1
	.word	584
	.byte	8,0,2,35,1,12
	.byte	'SDVOKLVL',0,1
	.word	584
	.byte	8,0,2,35,2,12
	.byte	'reserved_24',0,1
	.word	584
	.byte	7,1,2,35,3,12
	.byte	'LCK',0,1
	.word	584
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_EVRSDCTRL3_Bits',0,11,232,3,3
	.word	19650
	.byte	11
	.byte	'_Ifx_SCU_EVRSDCTRL4_Bits',0,11,235,3,16,4,12
	.byte	'reserved_0',0,1
	.word	584
	.byte	8,0,2,35,0,12
	.byte	'SYNCDIV',0,1
	.word	584
	.byte	3,5,2,35,1,12
	.byte	'reserved_11',0,4
	.word	561
	.byte	20,1,2,35,0,12
	.byte	'LCK',0,1
	.word	584
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_EVRSDCTRL4_Bits',0,11,241,3,3
	.word	19810
	.byte	11
	.byte	'_Ifx_SCU_EVRSTAT_Bits',0,11,244,3,16,4,12
	.byte	'EVR13',0,1
	.word	584
	.byte	1,7,2,35,0,12
	.byte	'OV13',0,1
	.word	584
	.byte	1,6,2,35,0,12
	.byte	'EVR33',0,1
	.word	584
	.byte	1,5,2,35,0,12
	.byte	'OV33',0,1
	.word	584
	.byte	1,4,2,35,0,12
	.byte	'OVSWD',0,1
	.word	584
	.byte	1,3,2,35,0,12
	.byte	'UV13',0,1
	.word	584
	.byte	1,2,2,35,0,12
	.byte	'UV33',0,1
	.word	584
	.byte	1,1,2,35,0,12
	.byte	'UVSWD',0,1
	.word	584
	.byte	1,0,2,35,0,12
	.byte	'EXTPASS13',0,1
	.word	584
	.byte	1,7,2,35,1,12
	.byte	'EXTPASS33',0,1
	.word	584
	.byte	1,6,2,35,1,12
	.byte	'BGPROK',0,1
	.word	584
	.byte	1,5,2,35,1,12
	.byte	'reserved_11',0,4
	.word	561
	.byte	21,0,2,35,0,0,23
	.byte	'Ifx_SCU_EVRSTAT_Bits',0,11,130,4,3
	.word	19954
	.byte	11
	.byte	'_Ifx_SCU_EVRTRIM_Bits',0,11,133,4,16,4,12
	.byte	'EVR13TRIM',0,1
	.word	584
	.byte	8,0,2,35,0,12
	.byte	'SDVOUTSEL',0,1
	.word	584
	.byte	8,0,2,35,1,12
	.byte	'reserved_16',0,2
	.word	601
	.byte	15,1,2,35,2,12
	.byte	'LCK',0,1
	.word	584
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_EVRTRIM_Bits',0,11,139,4,3
	.word	20228
	.byte	11
	.byte	'_Ifx_SCU_EVRUVMON_Bits',0,11,142,4,16,4,12
	.byte	'EVR13UVVAL',0,1
	.word	584
	.byte	8,0,2,35,0,12
	.byte	'EVR33UVVAL',0,1
	.word	584
	.byte	8,0,2,35,1,12
	.byte	'SWDUVVAL',0,1
	.word	584
	.byte	8,0,2,35,2,12
	.byte	'reserved_24',0,1
	.word	584
	.byte	7,1,2,35,3,12
	.byte	'LCK',0,1
	.word	584
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_EVRUVMON_Bits',0,11,149,4,3
	.word	20367
	.byte	11
	.byte	'_Ifx_SCU_EXTCON_Bits',0,11,152,4,16,4,12
	.byte	'EN0',0,1
	.word	584
	.byte	1,7,2,35,0,12
	.byte	'reserved_1',0,1
	.word	584
	.byte	1,6,2,35,0,12
	.byte	'SEL0',0,1
	.word	584
	.byte	4,2,2,35,0,12
	.byte	'reserved_6',0,2
	.word	601
	.byte	10,0,2,35,0,12
	.byte	'EN1',0,1
	.word	584
	.byte	1,7,2,35,2,12
	.byte	'NSEL',0,1
	.word	584
	.byte	1,6,2,35,2,12
	.byte	'SEL1',0,1
	.word	584
	.byte	4,2,2,35,2,12
	.byte	'reserved_22',0,1
	.word	584
	.byte	2,0,2,35,2,12
	.byte	'DIV1',0,1
	.word	584
	.byte	8,0,2,35,3,0,23
	.byte	'Ifx_SCU_EXTCON_Bits',0,11,163,4,3
	.word	20530
	.byte	11
	.byte	'_Ifx_SCU_FDR_Bits',0,11,166,4,16,4,12
	.byte	'STEP',0,2
	.word	601
	.byte	10,6,2,35,0,12
	.byte	'reserved_10',0,1
	.word	584
	.byte	4,2,2,35,1,12
	.byte	'DM',0,1
	.word	584
	.byte	2,0,2,35,1,12
	.byte	'RESULT',0,2
	.word	601
	.byte	10,6,2,35,2,12
	.byte	'reserved_26',0,1
	.word	584
	.byte	5,1,2,35,3,12
	.byte	'DISCLK',0,1
	.word	584
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_FDR_Bits',0,11,174,4,3
	.word	20748
	.byte	11
	.byte	'_Ifx_SCU_FMR_Bits',0,11,177,4,16,4,12
	.byte	'FS0',0,1
	.word	584
	.byte	1,7,2,35,0,12
	.byte	'FS1',0,1
	.word	584
	.byte	1,6,2,35,0,12
	.byte	'FS2',0,1
	.word	584
	.byte	1,5,2,35,0,12
	.byte	'FS3',0,1
	.word	584
	.byte	1,4,2,35,0,12
	.byte	'FS4',0,1
	.word	584
	.byte	1,3,2,35,0,12
	.byte	'FS5',0,1
	.word	584
	.byte	1,2,2,35,0,12
	.byte	'FS6',0,1
	.word	584
	.byte	1,1,2,35,0,12
	.byte	'FS7',0,1
	.word	584
	.byte	1,0,2,35,0,12
	.byte	'reserved_8',0,1
	.word	584
	.byte	8,0,2,35,1,12
	.byte	'FC0',0,1
	.word	584
	.byte	1,7,2,35,2,12
	.byte	'FC1',0,1
	.word	584
	.byte	1,6,2,35,2,12
	.byte	'FC2',0,1
	.word	584
	.byte	1,5,2,35,2,12
	.byte	'FC3',0,1
	.word	584
	.byte	1,4,2,35,2,12
	.byte	'FC4',0,1
	.word	584
	.byte	1,3,2,35,2,12
	.byte	'FC5',0,1
	.word	584
	.byte	1,2,2,35,2,12
	.byte	'FC6',0,1
	.word	584
	.byte	1,1,2,35,2,12
	.byte	'FC7',0,1
	.word	584
	.byte	1,0,2,35,2,12
	.byte	'reserved_24',0,1
	.word	584
	.byte	8,0,2,35,3,0,23
	.byte	'Ifx_SCU_FMR_Bits',0,11,197,4,3
	.word	20911
	.byte	11
	.byte	'_Ifx_SCU_ID_Bits',0,11,200,4,16,4,12
	.byte	'MODREV',0,1
	.word	584
	.byte	8,0,2,35,0,12
	.byte	'MODTYPE',0,1
	.word	584
	.byte	8,0,2,35,1,12
	.byte	'MODNUMBER',0,2
	.word	601
	.byte	16,0,2,35,2,0,23
	.byte	'Ifx_SCU_ID_Bits',0,11,205,4,3
	.word	21247
	.byte	11
	.byte	'_Ifx_SCU_IGCR_Bits',0,11,208,4,16,4,12
	.byte	'IPEN00',0,1
	.word	584
	.byte	1,7,2,35,0,12
	.byte	'IPEN01',0,1
	.word	584
	.byte	1,6,2,35,0,12
	.byte	'IPEN02',0,1
	.word	584
	.byte	1,5,2,35,0,12
	.byte	'IPEN03',0,1
	.word	584
	.byte	1,4,2,35,0,12
	.byte	'IPEN04',0,1
	.word	584
	.byte	1,3,2,35,0,12
	.byte	'IPEN05',0,1
	.word	584
	.byte	1,2,2,35,0,12
	.byte	'IPEN06',0,1
	.word	584
	.byte	1,1,2,35,0,12
	.byte	'IPEN07',0,1
	.word	584
	.byte	1,0,2,35,0,12
	.byte	'reserved_8',0,1
	.word	584
	.byte	5,3,2,35,1,12
	.byte	'GEEN0',0,1
	.word	584
	.byte	1,2,2,35,1,12
	.byte	'IGP0',0,1
	.word	584
	.byte	2,0,2,35,1,12
	.byte	'IPEN10',0,1
	.word	584
	.byte	1,7,2,35,2,12
	.byte	'IPEN11',0,1
	.word	584
	.byte	1,6,2,35,2,12
	.byte	'IPEN12',0,1
	.word	584
	.byte	1,5,2,35,2,12
	.byte	'IPEN13',0,1
	.word	584
	.byte	1,4,2,35,2,12
	.byte	'IPEN14',0,1
	.word	584
	.byte	1,3,2,35,2,12
	.byte	'IPEN15',0,1
	.word	584
	.byte	1,2,2,35,2,12
	.byte	'IPEN16',0,1
	.word	584
	.byte	1,1,2,35,2,12
	.byte	'IPEN17',0,1
	.word	584
	.byte	1,0,2,35,2,12
	.byte	'reserved_24',0,1
	.word	584
	.byte	5,3,2,35,3,12
	.byte	'GEEN1',0,1
	.word	584
	.byte	1,2,2,35,3,12
	.byte	'IGP1',0,1
	.word	584
	.byte	2,0,2,35,3,0,23
	.byte	'Ifx_SCU_IGCR_Bits',0,11,232,4,3
	.word	21354
	.byte	11
	.byte	'_Ifx_SCU_IN_Bits',0,11,235,4,16,4,12
	.byte	'P0',0,1
	.word	584
	.byte	1,7,2,35,0,12
	.byte	'P1',0,1
	.word	584
	.byte	1,6,2,35,0,12
	.byte	'reserved_2',0,4
	.word	561
	.byte	30,0,2,35,0,0,23
	.byte	'Ifx_SCU_IN_Bits',0,11,240,4,3
	.word	21806
	.byte	11
	.byte	'_Ifx_SCU_IOCR_Bits',0,11,243,4,16,4,12
	.byte	'reserved_0',0,1
	.word	584
	.byte	4,4,2,35,0,12
	.byte	'PC0',0,1
	.word	584
	.byte	4,0,2,35,0,12
	.byte	'reserved_8',0,1
	.word	584
	.byte	4,4,2,35,1,12
	.byte	'PC1',0,1
	.word	584
	.byte	4,0,2,35,1,12
	.byte	'reserved_16',0,2
	.word	601
	.byte	16,0,2,35,2,0,23
	.byte	'Ifx_SCU_IOCR_Bits',0,11,250,4,3
	.word	21905
	.byte	11
	.byte	'_Ifx_SCU_LBISTCTRL0_Bits',0,11,253,4,16,4,12
	.byte	'LBISTREQ',0,1
	.word	584
	.byte	1,7,2,35,0,12
	.byte	'LBISTREQP',0,1
	.word	584
	.byte	1,6,2,35,0,12
	.byte	'PATTERNS',0,2
	.word	601
	.byte	14,0,2,35,0,12
	.byte	'reserved_16',0,2
	.word	601
	.byte	16,0,2,35,2,0,23
	.byte	'Ifx_SCU_LBISTCTRL0_Bits',0,11,131,5,3
	.word	22055
	.byte	11
	.byte	'_Ifx_SCU_LBISTCTRL1_Bits',0,11,134,5,16,4,12
	.byte	'SEED',0,4
	.word	561
	.byte	23,9,2,35,0,12
	.byte	'reserved_23',0,1
	.word	584
	.byte	1,0,2,35,2,12
	.byte	'SPLITSH',0,1
	.word	584
	.byte	3,5,2,35,3,12
	.byte	'BODY',0,1
	.word	584
	.byte	1,4,2,35,3,12
	.byte	'LBISTFREQU',0,1
	.word	584
	.byte	4,0,2,35,3,0,23
	.byte	'Ifx_SCU_LBISTCTRL1_Bits',0,11,141,5,3
	.word	22204
	.byte	11
	.byte	'_Ifx_SCU_LBISTCTRL2_Bits',0,11,144,5,16,4,12
	.byte	'SIGNATURE',0,4
	.word	561
	.byte	24,8,2,35,0,12
	.byte	'reserved_24',0,1
	.word	584
	.byte	7,1,2,35,3,12
	.byte	'LBISTDONE',0,1
	.word	584
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_LBISTCTRL2_Bits',0,11,149,5,3
	.word	22365
	.byte	11
	.byte	'_Ifx_SCU_LCLCON_Bits',0,11,152,5,16,4,12
	.byte	'reserved_0',0,2
	.word	601
	.byte	16,0,2,35,0,12
	.byte	'LS',0,1
	.word	584
	.byte	1,7,2,35,2,12
	.byte	'reserved_17',0,2
	.word	601
	.byte	14,1,2,35,2,12
	.byte	'LSEN',0,1
	.word	584
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_LCLCON_Bits',0,11,158,5,3
	.word	22495
	.byte	11
	.byte	'_Ifx_SCU_LCLTEST_Bits',0,11,161,5,16,4,12
	.byte	'LCLT0',0,1
	.word	584
	.byte	1,7,2,35,0,12
	.byte	'LCLT1',0,1
	.word	584
	.byte	1,6,2,35,0,12
	.byte	'reserved_2',0,4
	.word	561
	.byte	30,0,2,35,0,0,23
	.byte	'Ifx_SCU_LCLTEST_Bits',0,11,166,5,3
	.word	22627
	.byte	11
	.byte	'_Ifx_SCU_MANID_Bits',0,11,169,5,16,4,12
	.byte	'DEPT',0,1
	.word	584
	.byte	5,3,2,35,0,12
	.byte	'MANUF',0,2
	.word	601
	.byte	11,0,2,35,0,12
	.byte	'reserved_16',0,2
	.word	601
	.byte	16,0,2,35,2,0,23
	.byte	'Ifx_SCU_MANID_Bits',0,11,174,5,3
	.word	22742
	.byte	11
	.byte	'_Ifx_SCU_OMR_Bits',0,11,177,5,16,4,12
	.byte	'PS0',0,1
	.word	584
	.byte	1,7,2,35,0,12
	.byte	'PS1',0,1
	.word	584
	.byte	1,6,2,35,0,12
	.byte	'reserved_2',0,2
	.word	601
	.byte	14,0,2,35,0,12
	.byte	'PCL0',0,1
	.word	584
	.byte	1,7,2,35,2,12
	.byte	'PCL1',0,1
	.word	584
	.byte	1,6,2,35,2,12
	.byte	'reserved_18',0,2
	.word	601
	.byte	14,0,2,35,2,0,23
	.byte	'Ifx_SCU_OMR_Bits',0,11,185,5,3
	.word	22853
	.byte	11
	.byte	'_Ifx_SCU_OSCCON_Bits',0,11,188,5,16,4,12
	.byte	'reserved_0',0,1
	.word	584
	.byte	1,7,2,35,0,12
	.byte	'PLLLV',0,1
	.word	584
	.byte	1,6,2,35,0,12
	.byte	'OSCRES',0,1
	.word	584
	.byte	1,5,2,35,0,12
	.byte	'GAINSEL',0,1
	.word	584
	.byte	2,3,2,35,0,12
	.byte	'MODE',0,1
	.word	584
	.byte	2,1,2,35,0,12
	.byte	'SHBY',0,1
	.word	584
	.byte	1,0,2,35,0,12
	.byte	'PLLHV',0,1
	.word	584
	.byte	1,7,2,35,1,12
	.byte	'reserved_9',0,1
	.word	584
	.byte	1,6,2,35,1,12
	.byte	'X1D',0,1
	.word	584
	.byte	1,5,2,35,1,12
	.byte	'X1DEN',0,1
	.word	584
	.byte	1,4,2,35,1,12
	.byte	'reserved_12',0,1
	.word	584
	.byte	4,0,2,35,1,12
	.byte	'OSCVAL',0,1
	.word	584
	.byte	5,3,2,35,2,12
	.byte	'reserved_21',0,1
	.word	584
	.byte	2,1,2,35,2,12
	.byte	'APREN',0,1
	.word	584
	.byte	1,0,2,35,2,12
	.byte	'CAP0EN',0,1
	.word	584
	.byte	1,7,2,35,3,12
	.byte	'CAP1EN',0,1
	.word	584
	.byte	1,6,2,35,3,12
	.byte	'CAP2EN',0,1
	.word	584
	.byte	1,5,2,35,3,12
	.byte	'CAP3EN',0,1
	.word	584
	.byte	1,4,2,35,3,12
	.byte	'reserved_28',0,1
	.word	584
	.byte	4,0,2,35,3,0,23
	.byte	'Ifx_SCU_OSCCON_Bits',0,11,209,5,3
	.word	23011
	.byte	11
	.byte	'_Ifx_SCU_OUT_Bits',0,11,212,5,16,4,12
	.byte	'P0',0,1
	.word	584
	.byte	1,7,2,35,0,12
	.byte	'P1',0,1
	.word	584
	.byte	1,6,2,35,0,12
	.byte	'reserved_2',0,4
	.word	561
	.byte	30,0,2,35,0,0,23
	.byte	'Ifx_SCU_OUT_Bits',0,11,217,5,3
	.word	23423
	.byte	11
	.byte	'_Ifx_SCU_OVCCON_Bits',0,11,220,5,16,4,12
	.byte	'CSEL0',0,1
	.word	584
	.byte	1,7,2,35,0,12
	.byte	'CSEL1',0,1
	.word	584
	.byte	1,6,2,35,0,12
	.byte	'CSEL2',0,1
	.word	584
	.byte	1,5,2,35,0,12
	.byte	'reserved_3',0,2
	.word	601
	.byte	13,0,2,35,0,12
	.byte	'OVSTRT',0,1
	.word	584
	.byte	1,7,2,35,2,12
	.byte	'OVSTP',0,1
	.word	584
	.byte	1,6,2,35,2,12
	.byte	'DCINVAL',0,1
	.word	584
	.byte	1,5,2,35,2,12
	.byte	'reserved_19',0,1
	.word	584
	.byte	5,0,2,35,2,12
	.byte	'OVCONF',0,1
	.word	584
	.byte	1,7,2,35,3,12
	.byte	'POVCONF',0,1
	.word	584
	.byte	1,6,2,35,3,12
	.byte	'reserved_26',0,1
	.word	584
	.byte	6,0,2,35,3,0,23
	.byte	'Ifx_SCU_OVCCON_Bits',0,11,233,5,3
	.word	23524
	.byte	11
	.byte	'_Ifx_SCU_OVCENABLE_Bits',0,11,236,5,16,4,12
	.byte	'OVEN0',0,1
	.word	584
	.byte	1,7,2,35,0,12
	.byte	'OVEN1',0,1
	.word	584
	.byte	1,6,2,35,0,12
	.byte	'OVEN2',0,1
	.word	584
	.byte	1,5,2,35,0,12
	.byte	'reserved_3',0,4
	.word	561
	.byte	29,0,2,35,0,0,23
	.byte	'Ifx_SCU_OVCENABLE_Bits',0,11,242,5,3
	.word	23791
	.byte	11
	.byte	'_Ifx_SCU_PDISC_Bits',0,11,245,5,16,4,12
	.byte	'PDIS0',0,1
	.word	584
	.byte	1,7,2,35,0,12
	.byte	'PDIS1',0,1
	.word	584
	.byte	1,6,2,35,0,12
	.byte	'reserved_2',0,4
	.word	561
	.byte	30,0,2,35,0,0,23
	.byte	'Ifx_SCU_PDISC_Bits',0,11,250,5,3
	.word	23927
	.byte	11
	.byte	'_Ifx_SCU_PDR_Bits',0,11,253,5,16,4,12
	.byte	'PD0',0,1
	.word	584
	.byte	3,5,2,35,0,12
	.byte	'PL0',0,1
	.word	584
	.byte	1,4,2,35,0,12
	.byte	'PD1',0,1
	.word	584
	.byte	3,1,2,35,0,12
	.byte	'PL1',0,1
	.word	584
	.byte	1,0,2,35,0,12
	.byte	'reserved_8',0,4
	.word	561
	.byte	24,0,2,35,0,0,23
	.byte	'Ifx_SCU_PDR_Bits',0,11,132,6,3
	.word	24038
	.byte	11
	.byte	'_Ifx_SCU_PDRR_Bits',0,11,135,6,16,4,12
	.byte	'PDR0',0,1
	.word	584
	.byte	1,7,2,35,0,12
	.byte	'PDR1',0,1
	.word	584
	.byte	1,6,2,35,0,12
	.byte	'PDR2',0,1
	.word	584
	.byte	1,5,2,35,0,12
	.byte	'PDR3',0,1
	.word	584
	.byte	1,4,2,35,0,12
	.byte	'PDR4',0,1
	.word	584
	.byte	1,3,2,35,0,12
	.byte	'PDR5',0,1
	.word	584
	.byte	1,2,2,35,0,12
	.byte	'PDR6',0,1
	.word	584
	.byte	1,1,2,35,0,12
	.byte	'PDR7',0,1
	.word	584
	.byte	1,0,2,35,0,12
	.byte	'reserved_8',0,4
	.word	561
	.byte	24,0,2,35,0,0,23
	.byte	'Ifx_SCU_PDRR_Bits',0,11,146,6,3
	.word	24171
	.byte	11
	.byte	'_Ifx_SCU_PLLCON0_Bits',0,11,149,6,16,4,12
	.byte	'VCOBYP',0,1
	.word	584
	.byte	1,7,2,35,0,12
	.byte	'VCOPWD',0,1
	.word	584
	.byte	1,6,2,35,0,12
	.byte	'MODEN',0,1
	.word	584
	.byte	1,5,2,35,0,12
	.byte	'reserved_3',0,1
	.word	584
	.byte	1,4,2,35,0,12
	.byte	'SETFINDIS',0,1
	.word	584
	.byte	1,3,2,35,0,12
	.byte	'CLRFINDIS',0,1
	.word	584
	.byte	1,2,2,35,0,12
	.byte	'OSCDISCDIS',0,1
	.word	584
	.byte	1,1,2,35,0,12
	.byte	'reserved_7',0,2
	.word	601
	.byte	2,7,2,35,0,12
	.byte	'NDIV',0,1
	.word	584
	.byte	7,0,2,35,1,12
	.byte	'PLLPWD',0,1
	.word	584
	.byte	1,7,2,35,2,12
	.byte	'reserved_17',0,1
	.word	584
	.byte	1,6,2,35,2,12
	.byte	'RESLD',0,1
	.word	584
	.byte	1,5,2,35,2,12
	.byte	'reserved_19',0,1
	.word	584
	.byte	5,0,2,35,2,12
	.byte	'PDIV',0,1
	.word	584
	.byte	4,4,2,35,3,12
	.byte	'reserved_28',0,1
	.word	584
	.byte	4,0,2,35,3,0,23
	.byte	'Ifx_SCU_PLLCON0_Bits',0,11,166,6,3
	.word	24374
	.byte	11
	.byte	'_Ifx_SCU_PLLCON1_Bits',0,11,169,6,16,4,12
	.byte	'K2DIV',0,1
	.word	584
	.byte	7,1,2,35,0,12
	.byte	'reserved_7',0,1
	.word	584
	.byte	1,0,2,35,0,12
	.byte	'K3DIV',0,1
	.word	584
	.byte	7,1,2,35,1,12
	.byte	'reserved_15',0,1
	.word	584
	.byte	1,0,2,35,1,12
	.byte	'K1DIV',0,1
	.word	584
	.byte	7,1,2,35,2,12
	.byte	'reserved_23',0,2
	.word	601
	.byte	9,0,2,35,2,0,23
	.byte	'Ifx_SCU_PLLCON1_Bits',0,11,177,6,3
	.word	24730
	.byte	11
	.byte	'_Ifx_SCU_PLLCON2_Bits',0,11,180,6,16,4,12
	.byte	'MODCFG',0,2
	.word	601
	.byte	16,0,2,35,0,12
	.byte	'reserved_16',0,2
	.word	601
	.byte	16,0,2,35,2,0,23
	.byte	'Ifx_SCU_PLLCON2_Bits',0,11,184,6,3
	.word	24908
	.byte	11
	.byte	'_Ifx_SCU_PLLERAYCON0_Bits',0,11,187,6,16,4,12
	.byte	'VCOBYP',0,1
	.word	584
	.byte	1,7,2,35,0,12
	.byte	'VCOPWD',0,1
	.word	584
	.byte	1,6,2,35,0,12
	.byte	'reserved_2',0,1
	.word	584
	.byte	2,4,2,35,0,12
	.byte	'SETFINDIS',0,1
	.word	584
	.byte	1,3,2,35,0,12
	.byte	'CLRFINDIS',0,1
	.word	584
	.byte	1,2,2,35,0,12
	.byte	'OSCDISCDIS',0,1
	.word	584
	.byte	1,1,2,35,0,12
	.byte	'reserved_7',0,2
	.word	601
	.byte	2,7,2,35,0,12
	.byte	'NDIV',0,1
	.word	584
	.byte	5,2,2,35,1,12
	.byte	'reserved_14',0,1
	.word	584
	.byte	2,0,2,35,1,12
	.byte	'PLLPWD',0,1
	.word	584
	.byte	1,7,2,35,2,12
	.byte	'reserved_17',0,1
	.word	584
	.byte	1,6,2,35,2,12
	.byte	'RESLD',0,1
	.word	584
	.byte	1,5,2,35,2,12
	.byte	'reserved_19',0,1
	.word	584
	.byte	5,0,2,35,2,12
	.byte	'PDIV',0,1
	.word	584
	.byte	4,4,2,35,3,12
	.byte	'reserved_28',0,1
	.word	584
	.byte	4,0,2,35,3,0,23
	.byte	'Ifx_SCU_PLLERAYCON0_Bits',0,11,204,6,3
	.word	25008
	.byte	11
	.byte	'_Ifx_SCU_PLLERAYCON1_Bits',0,11,207,6,16,4,12
	.byte	'K2DIV',0,1
	.word	584
	.byte	7,1,2,35,0,12
	.byte	'reserved_7',0,1
	.word	584
	.byte	1,0,2,35,0,12
	.byte	'K3DIV',0,1
	.word	584
	.byte	4,4,2,35,1,12
	.byte	'reserved_12',0,1
	.word	584
	.byte	4,0,2,35,1,12
	.byte	'K1DIV',0,1
	.word	584
	.byte	7,1,2,35,2,12
	.byte	'reserved_23',0,2
	.word	601
	.byte	9,0,2,35,2,0,23
	.byte	'Ifx_SCU_PLLERAYCON1_Bits',0,11,215,6,3
	.word	25378
	.byte	11
	.byte	'_Ifx_SCU_PLLERAYSTAT_Bits',0,11,218,6,16,4,12
	.byte	'VCOBYST',0,1
	.word	584
	.byte	1,7,2,35,0,12
	.byte	'PWDSTAT',0,1
	.word	584
	.byte	1,6,2,35,0,12
	.byte	'VCOLOCK',0,1
	.word	584
	.byte	1,5,2,35,0,12
	.byte	'FINDIS',0,1
	.word	584
	.byte	1,4,2,35,0,12
	.byte	'K1RDY',0,1
	.word	584
	.byte	1,3,2,35,0,12
	.byte	'K2RDY',0,1
	.word	584
	.byte	1,2,2,35,0,12
	.byte	'reserved_6',0,4
	.word	561
	.byte	26,0,2,35,0,0,23
	.byte	'Ifx_SCU_PLLERAYSTAT_Bits',0,11,227,6,3
	.word	25564
	.byte	11
	.byte	'_Ifx_SCU_PLLSTAT_Bits',0,11,230,6,16,4,12
	.byte	'VCOBYST',0,1
	.word	584
	.byte	1,7,2,35,0,12
	.byte	'reserved_1',0,1
	.word	584
	.byte	1,6,2,35,0,12
	.byte	'VCOLOCK',0,1
	.word	584
	.byte	1,5,2,35,0,12
	.byte	'FINDIS',0,1
	.word	584
	.byte	1,4,2,35,0,12
	.byte	'K1RDY',0,1
	.word	584
	.byte	1,3,2,35,0,12
	.byte	'K2RDY',0,1
	.word	584
	.byte	1,2,2,35,0,12
	.byte	'reserved_6',0,1
	.word	584
	.byte	1,1,2,35,0,12
	.byte	'MODRUN',0,1
	.word	584
	.byte	1,0,2,35,0,12
	.byte	'reserved_8',0,4
	.word	561
	.byte	24,0,2,35,0,0,23
	.byte	'Ifx_SCU_PLLSTAT_Bits',0,11,241,6,3
	.word	25762
	.byte	11
	.byte	'_Ifx_SCU_PMCSR_Bits',0,11,244,6,16,4,12
	.byte	'REQSLP',0,1
	.word	584
	.byte	2,6,2,35,0,12
	.byte	'SMUSLP',0,1
	.word	584
	.byte	1,5,2,35,0,12
	.byte	'reserved_3',0,1
	.word	584
	.byte	5,0,2,35,0,12
	.byte	'PMST',0,1
	.word	584
	.byte	3,5,2,35,1,12
	.byte	'reserved_11',0,4
	.word	561
	.byte	21,0,2,35,0,0,23
	.byte	'Ifx_SCU_PMCSR_Bits',0,11,251,6,3
	.word	25995
	.byte	11
	.byte	'_Ifx_SCU_PMSWCR0_Bits',0,11,254,6,16,4,12
	.byte	'reserved_0',0,1
	.word	584
	.byte	1,7,2,35,0,12
	.byte	'ESR1WKEN',0,1
	.word	584
	.byte	1,6,2,35,0,12
	.byte	'PINAWKEN',0,1
	.word	584
	.byte	1,5,2,35,0,12
	.byte	'PINBWKEN',0,1
	.word	584
	.byte	1,4,2,35,0,12
	.byte	'ESR0DFEN',0,1
	.word	584
	.byte	1,3,2,35,0,12
	.byte	'ESR0EDCON',0,1
	.word	584
	.byte	2,1,2,35,0,12
	.byte	'ESR1DFEN',0,1
	.word	584
	.byte	1,0,2,35,0,12
	.byte	'ESR1EDCON',0,1
	.word	584
	.byte	2,6,2,35,1,12
	.byte	'PINADFEN',0,1
	.word	584
	.byte	1,5,2,35,1,12
	.byte	'PINAEDCON',0,1
	.word	584
	.byte	2,3,2,35,1,12
	.byte	'PINBDFEN',0,1
	.word	584
	.byte	1,2,2,35,1,12
	.byte	'PINBEDCON',0,1
	.word	584
	.byte	2,0,2,35,1,12
	.byte	'SCREN',0,1
	.word	584
	.byte	1,7,2,35,2,12
	.byte	'STBYRAMSEL',0,1
	.word	584
	.byte	2,5,2,35,2,12
	.byte	'SCRCLKSEL',0,1
	.word	584
	.byte	1,4,2,35,2,12
	.byte	'SCRWKEN',0,1
	.word	584
	.byte	1,3,2,35,2,12
	.byte	'TRISTEN',0,1
	.word	584
	.byte	1,2,2,35,2,12
	.byte	'TRISTREQ',0,1
	.word	584
	.byte	1,1,2,35,2,12
	.byte	'PORSTDF',0,1
	.word	584
	.byte	1,0,2,35,2,12
	.byte	'reserved_24',0,1
	.word	584
	.byte	1,7,2,35,3,12
	.byte	'DCDCSYNC',0,1
	.word	584
	.byte	1,6,2,35,3,12
	.byte	'reserved_26',0,1
	.word	584
	.byte	3,3,2,35,3,12
	.byte	'ESR0TRIST',0,1
	.word	584
	.byte	1,2,2,35,3,12
	.byte	'reserved_30',0,1
	.word	584
	.byte	1,1,2,35,3,12
	.byte	'LCK',0,1
	.word	584
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_PMSWCR0_Bits',0,11,153,7,3
	.word	26147
	.byte	11
	.byte	'_Ifx_SCU_PMSWCR1_Bits',0,11,156,7,16,4,12
	.byte	'SCRSTEN',0,1
	.word	584
	.byte	1,7,2,35,0,12
	.byte	'SCRSTREQ',0,1
	.word	584
	.byte	1,6,2,35,0,12
	.byte	'reserved_2',0,1
	.word	584
	.byte	6,0,2,35,0,12
	.byte	'CPUIDLSEL',0,1
	.word	584
	.byte	3,5,2,35,1,12
	.byte	'reserved_11',0,1
	.word	584
	.byte	1,4,2,35,1,12
	.byte	'IRADIS',0,1
	.word	584
	.byte	1,3,2,35,1,12
	.byte	'reserved_13',0,1
	.word	584
	.byte	3,0,2,35,1,12
	.byte	'SCRCFG',0,1
	.word	584
	.byte	8,0,2,35,2,12
	.byte	'CPUSEL',0,1
	.word	584
	.byte	3,5,2,35,3,12
	.byte	'STBYEVEN',0,1
	.word	584
	.byte	1,4,2,35,3,12
	.byte	'STBYEV',0,1
	.word	584
	.byte	3,1,2,35,3,12
	.byte	'LCK',0,1
	.word	584
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_PMSWCR1_Bits',0,11,170,7,3
	.word	26714
	.byte	11
	.byte	'_Ifx_SCU_PMSWCR2_Bits',0,11,173,7,16,4,12
	.byte	'SCRINT',0,1
	.word	584
	.byte	8,0,2,35,0,12
	.byte	'BUSY',0,1
	.word	584
	.byte	1,7,2,35,1,12
	.byte	'SCRECC',0,1
	.word	584
	.byte	1,6,2,35,1,12
	.byte	'SCRWDT',0,1
	.word	584
	.byte	1,5,2,35,1,12
	.byte	'SCRRST',0,1
	.word	584
	.byte	1,4,2,35,1,12
	.byte	'reserved_12',0,1
	.word	584
	.byte	4,0,2,35,1,12
	.byte	'TCINT',0,1
	.word	584
	.byte	8,0,2,35,2,12
	.byte	'TCINTREQ',0,1
	.word	584
	.byte	1,7,2,35,3,12
	.byte	'SMURST',0,1
	.word	584
	.byte	1,6,2,35,3,12
	.byte	'RST',0,1
	.word	584
	.byte	1,5,2,35,3,12
	.byte	'reserved_27',0,1
	.word	584
	.byte	4,1,2,35,3,12
	.byte	'LCK',0,1
	.word	584
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_PMSWCR2_Bits',0,11,187,7,3
	.word	27008
	.byte	11
	.byte	'_Ifx_SCU_PMSWSTAT_Bits',0,11,190,7,16,4,12
	.byte	'reserved_0',0,1
	.word	584
	.byte	2,6,2,35,0,12
	.byte	'ESR1WKP',0,1
	.word	584
	.byte	1,5,2,35,0,12
	.byte	'ESR1OVRUN',0,1
	.word	584
	.byte	1,4,2,35,0,12
	.byte	'PINAWKP',0,1
	.word	584
	.byte	1,3,2,35,0,12
	.byte	'PINAOVRUN',0,1
	.word	584
	.byte	1,2,2,35,0,12
	.byte	'PINBWKP',0,1
	.word	584
	.byte	1,1,2,35,0,12
	.byte	'PINBOVRUN',0,1
	.word	584
	.byte	1,0,2,35,0,12
	.byte	'reserved_8',0,1
	.word	584
	.byte	1,7,2,35,1,12
	.byte	'PORSTDF',0,1
	.word	584
	.byte	1,6,2,35,1,12
	.byte	'HWCFGEVR',0,1
	.word	584
	.byte	3,3,2,35,1,12
	.byte	'STBYRAM',0,1
	.word	584
	.byte	2,1,2,35,1,12
	.byte	'TRIST',0,1
	.word	584
	.byte	1,0,2,35,1,12
	.byte	'SCRST',0,1
	.word	584
	.byte	1,7,2,35,2,12
	.byte	'SCRWKP',0,1
	.word	584
	.byte	1,6,2,35,2,12
	.byte	'SCR',0,1
	.word	584
	.byte	1,5,2,35,2,12
	.byte	'SCRWKEN',0,1
	.word	584
	.byte	1,4,2,35,2,12
	.byte	'ESR1WKEN',0,1
	.word	584
	.byte	1,3,2,35,2,12
	.byte	'PINAWKEN',0,1
	.word	584
	.byte	1,2,2,35,2,12
	.byte	'PINBWKEN',0,1
	.word	584
	.byte	1,1,2,35,2,12
	.byte	'reserved_23',0,2
	.word	601
	.byte	4,5,2,35,2,12
	.byte	'ESR0TRIST',0,1
	.word	584
	.byte	1,4,2,35,3,12
	.byte	'reserved_28',0,1
	.word	584
	.byte	4,0,2,35,3,0,23
	.byte	'Ifx_SCU_PMSWSTAT_Bits',0,11,214,7,3
	.word	27286
	.byte	11
	.byte	'_Ifx_SCU_PMSWSTATCLR_Bits',0,11,217,7,16,4,12
	.byte	'reserved_0',0,1
	.word	584
	.byte	2,6,2,35,0,12
	.byte	'ESR1WKPCLR',0,1
	.word	584
	.byte	1,5,2,35,0,12
	.byte	'ESR1OVRUNCLR',0,1
	.word	584
	.byte	1,4,2,35,0,12
	.byte	'PINAWKPCLR',0,1
	.word	584
	.byte	1,3,2,35,0,12
	.byte	'PINAOVRUNCLR',0,1
	.word	584
	.byte	1,2,2,35,0,12
	.byte	'PINBWKPCLR',0,1
	.word	584
	.byte	1,1,2,35,0,12
	.byte	'PINBOVRUNCLR',0,1
	.word	584
	.byte	1,0,2,35,0,12
	.byte	'reserved_8',0,1
	.word	584
	.byte	8,0,2,35,1,12
	.byte	'SCRSTCLR',0,1
	.word	584
	.byte	1,7,2,35,2,12
	.byte	'SCRWKPCLR',0,1
	.word	584
	.byte	1,6,2,35,2,12
	.byte	'reserved_18',0,2
	.word	601
	.byte	14,0,2,35,2,0,23
	.byte	'Ifx_SCU_PMSWSTATCLR_Bits',0,11,230,7,3
	.word	27782
	.byte	11
	.byte	'_Ifx_SCU_RSTCON2_Bits',0,11,233,7,16,4,12
	.byte	'reserved_0',0,1
	.word	584
	.byte	1,7,2,35,0,12
	.byte	'CLRC',0,1
	.word	584
	.byte	1,6,2,35,0,12
	.byte	'reserved_2',0,2
	.word	601
	.byte	10,4,2,35,0,12
	.byte	'CSS0',0,1
	.word	584
	.byte	1,3,2,35,1,12
	.byte	'CSS1',0,1
	.word	584
	.byte	1,2,2,35,1,12
	.byte	'CSS2',0,1
	.word	584
	.byte	1,1,2,35,1,12
	.byte	'reserved_15',0,1
	.word	584
	.byte	1,0,2,35,1,12
	.byte	'USRINFO',0,2
	.word	601
	.byte	16,0,2,35,2,0,23
	.byte	'Ifx_SCU_RSTCON2_Bits',0,11,243,7,3
	.word	28095
	.byte	11
	.byte	'_Ifx_SCU_RSTCON_Bits',0,11,246,7,16,4,12
	.byte	'ESR0',0,1
	.word	584
	.byte	2,6,2,35,0,12
	.byte	'ESR1',0,1
	.word	584
	.byte	2,4,2,35,0,12
	.byte	'reserved_4',0,1
	.word	584
	.byte	2,2,2,35,0,12
	.byte	'SMU',0,1
	.word	584
	.byte	2,0,2,35,0,12
	.byte	'SW',0,1
	.word	584
	.byte	2,6,2,35,1,12
	.byte	'STM0',0,1
	.word	584
	.byte	2,4,2,35,1,12
	.byte	'STM1',0,1
	.word	584
	.byte	2,2,2,35,1,12
	.byte	'STM2',0,1
	.word	584
	.byte	2,0,2,35,1,12
	.byte	'reserved_16',0,2
	.word	601
	.byte	16,0,2,35,2,0,23
	.byte	'Ifx_SCU_RSTCON_Bits',0,11,129,8,3
	.word	28304
	.byte	11
	.byte	'_Ifx_SCU_RSTSTAT_Bits',0,11,132,8,16,4,12
	.byte	'ESR0',0,1
	.word	584
	.byte	1,7,2,35,0,12
	.byte	'ESR1',0,1
	.word	584
	.byte	1,6,2,35,0,12
	.byte	'reserved_2',0,1
	.word	584
	.byte	1,5,2,35,0,12
	.byte	'SMU',0,1
	.word	584
	.byte	1,4,2,35,0,12
	.byte	'SW',0,1
	.word	584
	.byte	1,3,2,35,0,12
	.byte	'STM0',0,1
	.word	584
	.byte	1,2,2,35,0,12
	.byte	'STM1',0,1
	.word	584
	.byte	1,1,2,35,0,12
	.byte	'STM2',0,1
	.word	584
	.byte	1,0,2,35,0,12
	.byte	'reserved_8',0,1
	.word	584
	.byte	8,0,2,35,1,12
	.byte	'PORST',0,1
	.word	584
	.byte	1,7,2,35,2,12
	.byte	'reserved_17',0,1
	.word	584
	.byte	1,6,2,35,2,12
	.byte	'CB0',0,1
	.word	584
	.byte	1,5,2,35,2,12
	.byte	'CB1',0,1
	.word	584
	.byte	1,4,2,35,2,12
	.byte	'CB3',0,1
	.word	584
	.byte	1,3,2,35,2,12
	.byte	'reserved_21',0,1
	.word	584
	.byte	2,1,2,35,2,12
	.byte	'EVR13',0,1
	.word	584
	.byte	1,0,2,35,2,12
	.byte	'EVR33',0,1
	.word	584
	.byte	1,7,2,35,3,12
	.byte	'SWD',0,1
	.word	584
	.byte	1,6,2,35,3,12
	.byte	'reserved_26',0,1
	.word	584
	.byte	2,4,2,35,3,12
	.byte	'STBYR',0,1
	.word	584
	.byte	1,3,2,35,3,12
	.byte	'reserved_29',0,1
	.word	584
	.byte	3,0,2,35,3,0,23
	.byte	'Ifx_SCU_RSTSTAT_Bits',0,11,155,8,3
	.word	28515
	.byte	11
	.byte	'_Ifx_SCU_SAFECON_Bits',0,11,158,8,16,4,12
	.byte	'HBT',0,1
	.word	584
	.byte	1,7,2,35,0,12
	.byte	'reserved_1',0,4
	.word	561
	.byte	31,0,2,35,0,0,23
	.byte	'Ifx_SCU_SAFECON_Bits',0,11,162,8,3
	.word	28947
	.byte	11
	.byte	'_Ifx_SCU_STSTAT_Bits',0,11,165,8,16,4,12
	.byte	'HWCFG',0,1
	.word	584
	.byte	8,0,2,35,0,12
	.byte	'FTM',0,1
	.word	584
	.byte	7,1,2,35,1,12
	.byte	'MODE',0,1
	.word	584
	.byte	1,0,2,35,1,12
	.byte	'FCBAE',0,1
	.word	584
	.byte	1,7,2,35,2,12
	.byte	'LUDIS',0,1
	.word	584
	.byte	1,6,2,35,2,12
	.byte	'reserved_18',0,1
	.word	584
	.byte	1,5,2,35,2,12
	.byte	'TRSTL',0,1
	.word	584
	.byte	1,4,2,35,2,12
	.byte	'SPDEN',0,1
	.word	584
	.byte	1,3,2,35,2,12
	.byte	'reserved_21',0,1
	.word	584
	.byte	3,0,2,35,2,12
	.byte	'RAMINT',0,1
	.word	584
	.byte	1,7,2,35,3,12
	.byte	'reserved_25',0,1
	.word	584
	.byte	7,0,2,35,3,0,23
	.byte	'Ifx_SCU_STSTAT_Bits',0,11,178,8,3
	.word	29043
	.byte	11
	.byte	'_Ifx_SCU_SWRSTCON_Bits',0,11,181,8,16,4,12
	.byte	'reserved_0',0,1
	.word	584
	.byte	1,7,2,35,0,12
	.byte	'SWRSTREQ',0,1
	.word	584
	.byte	1,6,2,35,0,12
	.byte	'reserved_2',0,4
	.word	561
	.byte	30,0,2,35,0,0,23
	.byte	'Ifx_SCU_SWRSTCON_Bits',0,11,186,8,3
	.word	29303
	.byte	11
	.byte	'_Ifx_SCU_SYSCON_Bits',0,11,189,8,16,4,12
	.byte	'CCTRIG0',0,1
	.word	584
	.byte	1,7,2,35,0,12
	.byte	'reserved_1',0,1
	.word	584
	.byte	1,6,2,35,0,12
	.byte	'RAMINTM',0,1
	.word	584
	.byte	2,4,2,35,0,12
	.byte	'SETLUDIS',0,1
	.word	584
	.byte	1,3,2,35,0,12
	.byte	'reserved_5',0,1
	.word	584
	.byte	3,0,2,35,0,12
	.byte	'DATM',0,1
	.word	584
	.byte	1,7,2,35,1,12
	.byte	'reserved_9',0,4
	.word	561
	.byte	23,0,2,35,0,0,23
	.byte	'Ifx_SCU_SYSCON_Bits',0,11,198,8,3
	.word	29428
	.byte	11
	.byte	'_Ifx_SCU_TRAPCLR_Bits',0,11,201,8,16,4,12
	.byte	'ESR0T',0,1
	.word	584
	.byte	1,7,2,35,0,12
	.byte	'ESR1T',0,1
	.word	584
	.byte	1,6,2,35,0,12
	.byte	'reserved_2',0,1
	.word	584
	.byte	1,5,2,35,0,12
	.byte	'SMUT',0,1
	.word	584
	.byte	1,4,2,35,0,12
	.byte	'reserved_4',0,4
	.word	561
	.byte	28,0,2,35,0,0,23
	.byte	'Ifx_SCU_TRAPCLR_Bits',0,11,208,8,3
	.word	29625
	.byte	11
	.byte	'_Ifx_SCU_TRAPDIS_Bits',0,11,211,8,16,4,12
	.byte	'ESR0T',0,1
	.word	584
	.byte	1,7,2,35,0,12
	.byte	'ESR1T',0,1
	.word	584
	.byte	1,6,2,35,0,12
	.byte	'reserved_2',0,1
	.word	584
	.byte	1,5,2,35,0,12
	.byte	'SMUT',0,1
	.word	584
	.byte	1,4,2,35,0,12
	.byte	'reserved_4',0,4
	.word	561
	.byte	28,0,2,35,0,0,23
	.byte	'Ifx_SCU_TRAPDIS_Bits',0,11,218,8,3
	.word	29778
	.byte	11
	.byte	'_Ifx_SCU_TRAPSET_Bits',0,11,221,8,16,4,12
	.byte	'ESR0T',0,1
	.word	584
	.byte	1,7,2,35,0,12
	.byte	'ESR1T',0,1
	.word	584
	.byte	1,6,2,35,0,12
	.byte	'reserved_2',0,1
	.word	584
	.byte	1,5,2,35,0,12
	.byte	'SMUT',0,1
	.word	584
	.byte	1,4,2,35,0,12
	.byte	'reserved_4',0,4
	.word	561
	.byte	28,0,2,35,0,0,23
	.byte	'Ifx_SCU_TRAPSET_Bits',0,11,228,8,3
	.word	29931
	.byte	11
	.byte	'_Ifx_SCU_TRAPSTAT_Bits',0,11,231,8,16,4,12
	.byte	'ESR0T',0,1
	.word	584
	.byte	1,7,2,35,0,12
	.byte	'ESR1T',0,1
	.word	584
	.byte	1,6,2,35,0,12
	.byte	'reserved_2',0,1
	.word	584
	.byte	1,5,2,35,0,12
	.byte	'SMUT',0,1
	.word	584
	.byte	1,4,2,35,0,12
	.byte	'reserved_4',0,4
	.word	561
	.byte	28,0,2,35,0,0,23
	.byte	'Ifx_SCU_TRAPSTAT_Bits',0,11,238,8,3
	.word	30084
	.byte	9
	.byte	'unsigned int',0,4,7,11
	.byte	'_Ifx_SCU_WDTCPU_CON0_Bits',0,11,241,8,16,4,12
	.byte	'ENDINIT',0,4
	.word	30239
	.byte	1,31,2,35,0,12
	.byte	'LCK',0,4
	.word	30239
	.byte	1,30,2,35,0,12
	.byte	'PW',0,4
	.word	30239
	.byte	14,16,2,35,0,12
	.byte	'REL',0,4
	.word	30239
	.byte	16,0,2,35,0,0,23
	.byte	'Ifx_SCU_WDTCPU_CON0_Bits',0,11,247,8,3
	.word	30255
	.byte	11
	.byte	'_Ifx_SCU_WDTCPU_CON1_Bits',0,11,250,8,16,4,12
	.byte	'reserved_0',0,1
	.word	584
	.byte	2,6,2,35,0,12
	.byte	'IR0',0,1
	.word	584
	.byte	1,5,2,35,0,12
	.byte	'DR',0,1
	.word	584
	.byte	1,4,2,35,0,12
	.byte	'reserved_4',0,1
	.word	584
	.byte	1,3,2,35,0,12
	.byte	'IR1',0,1
	.word	584
	.byte	1,2,2,35,0,12
	.byte	'UR',0,1
	.word	584
	.byte	1,1,2,35,0,12
	.byte	'PAR',0,1
	.word	584
	.byte	1,0,2,35,0,12
	.byte	'TCR',0,1
	.word	584
	.byte	1,7,2,35,1,12
	.byte	'TCTR',0,1
	.word	584
	.byte	7,0,2,35,1,12
	.byte	'reserved_16',0,2
	.word	601
	.byte	16,0,2,35,2,0,23
	.byte	'Ifx_SCU_WDTCPU_CON1_Bits',0,11,134,9,3
	.word	30385
	.byte	11
	.byte	'_Ifx_SCU_WDTCPU_SR_Bits',0,11,137,9,16,4,12
	.byte	'AE',0,1
	.word	584
	.byte	1,7,2,35,0,12
	.byte	'OE',0,1
	.word	584
	.byte	1,6,2,35,0,12
	.byte	'IS0',0,1
	.word	584
	.byte	1,5,2,35,0,12
	.byte	'DS',0,1
	.word	584
	.byte	1,4,2,35,0,12
	.byte	'TO',0,1
	.word	584
	.byte	1,3,2,35,0,12
	.byte	'IS1',0,1
	.word	584
	.byte	1,2,2,35,0,12
	.byte	'US',0,1
	.word	584
	.byte	1,1,2,35,0,12
	.byte	'PAS',0,1
	.word	584
	.byte	1,0,2,35,0,12
	.byte	'TCS',0,1
	.word	584
	.byte	1,7,2,35,1,12
	.byte	'TCT',0,1
	.word	584
	.byte	7,0,2,35,1,12
	.byte	'TIM',0,2
	.word	601
	.byte	16,0,2,35,2,0,23
	.byte	'Ifx_SCU_WDTCPU_SR_Bits',0,11,150,9,3
	.word	30623
	.byte	11
	.byte	'_Ifx_SCU_WDTS_CON0_Bits',0,11,153,9,16,4,12
	.byte	'ENDINIT',0,4
	.word	30239
	.byte	1,31,2,35,0,12
	.byte	'LCK',0,4
	.word	30239
	.byte	1,30,2,35,0,12
	.byte	'PW',0,4
	.word	30239
	.byte	14,16,2,35,0,12
	.byte	'REL',0,4
	.word	30239
	.byte	16,0,2,35,0,0,23
	.byte	'Ifx_SCU_WDTS_CON0_Bits',0,11,159,9,3
	.word	30846
	.byte	11
	.byte	'_Ifx_SCU_WDTS_CON1_Bits',0,11,162,9,16,4,12
	.byte	'CLRIRF',0,1
	.word	584
	.byte	1,7,2,35,0,12
	.byte	'reserved_1',0,1
	.word	584
	.byte	1,6,2,35,0,12
	.byte	'IR0',0,1
	.word	584
	.byte	1,5,2,35,0,12
	.byte	'DR',0,1
	.word	584
	.byte	1,4,2,35,0,12
	.byte	'reserved_4',0,1
	.word	584
	.byte	1,3,2,35,0,12
	.byte	'IR1',0,1
	.word	584
	.byte	1,2,2,35,0,12
	.byte	'UR',0,1
	.word	584
	.byte	1,1,2,35,0,12
	.byte	'PAR',0,1
	.word	584
	.byte	1,0,2,35,0,12
	.byte	'TCR',0,1
	.word	584
	.byte	1,7,2,35,1,12
	.byte	'TCTR',0,1
	.word	584
	.byte	7,0,2,35,1,12
	.byte	'reserved_16',0,2
	.word	601
	.byte	16,0,2,35,2,0,23
	.byte	'Ifx_SCU_WDTS_CON1_Bits',0,11,175,9,3
	.word	30972
	.byte	11
	.byte	'_Ifx_SCU_WDTS_SR_Bits',0,11,178,9,16,4,12
	.byte	'AE',0,1
	.word	584
	.byte	1,7,2,35,0,12
	.byte	'OE',0,1
	.word	584
	.byte	1,6,2,35,0,12
	.byte	'IS0',0,1
	.word	584
	.byte	1,5,2,35,0,12
	.byte	'DS',0,1
	.word	584
	.byte	1,4,2,35,0,12
	.byte	'TO',0,1
	.word	584
	.byte	1,3,2,35,0,12
	.byte	'IS1',0,1
	.word	584
	.byte	1,2,2,35,0,12
	.byte	'US',0,1
	.word	584
	.byte	1,1,2,35,0,12
	.byte	'PAS',0,1
	.word	584
	.byte	1,0,2,35,0,12
	.byte	'TCS',0,1
	.word	584
	.byte	1,7,2,35,1,12
	.byte	'TCT',0,1
	.word	584
	.byte	7,0,2,35,1,12
	.byte	'TIM',0,2
	.word	601
	.byte	16,0,2,35,2,0,23
	.byte	'Ifx_SCU_WDTS_SR_Bits',0,11,191,9,3
	.word	31224
	.byte	13,11,199,9,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	12749
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_ACCEN0',0,11,204,9,3
	.word	31443
	.byte	13,11,207,9,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	13306
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_ACCEN1',0,11,212,9,3
	.word	31507
	.byte	13,11,215,9,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	13383
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_ARSTDIS',0,11,220,9,3
	.word	31571
	.byte	13,11,223,9,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	13519
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_CCUCON0',0,11,228,9,3
	.word	31636
	.byte	13,11,231,9,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	13799
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_CCUCON1',0,11,236,9,3
	.word	31701
	.byte	13,11,239,9,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	14037
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_CCUCON2',0,11,244,9,3
	.word	31766
	.byte	13,11,247,9,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	14165
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_CCUCON3',0,11,252,9,3
	.word	31831
	.byte	13,11,255,9,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	14408
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_CCUCON4',0,11,132,10,3
	.word	31896
	.byte	13,11,135,10,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	14643
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_CCUCON5',0,11,140,10,3
	.word	31961
	.byte	13,11,143,10,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	14771
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_CCUCON6',0,11,148,10,3
	.word	32026
	.byte	13,11,151,10,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	14871
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_CCUCON7',0,11,156,10,3
	.word	32091
	.byte	13,11,159,10,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	14971
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_CHIPID',0,11,164,10,3
	.word	32156
	.byte	13,11,167,10,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	15179
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_DTSCON',0,11,172,10,3
	.word	32220
	.byte	13,11,175,10,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	15344
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_DTSLIM',0,11,180,10,3
	.word	32284
	.byte	13,11,183,10,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	15527
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_DTSSTAT',0,11,188,10,3
	.word	32348
	.byte	13,11,191,10,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	15681
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_EICR',0,11,196,10,3
	.word	32413
	.byte	13,11,199,10,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	16045
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_EIFR',0,11,204,10,3
	.word	32475
	.byte	13,11,207,10,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	16256
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_EMSR',0,11,212,10,3
	.word	32537
	.byte	13,11,215,10,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	16508
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_ESRCFG',0,11,220,10,3
	.word	32599
	.byte	13,11,223,10,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	16626
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_ESROCFG',0,11,228,10,3
	.word	32663
	.byte	13,11,231,10,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	16737
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_EVR13CON',0,11,236,10,3
	.word	32728
	.byte	13,11,239,10,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	16900
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_EVR33CON',0,11,244,10,3
	.word	32794
	.byte	13,11,247,10,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	17063
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_EVRADCSTAT',0,11,252,10,3
	.word	32860
	.byte	13,11,255,10,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	17221
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_EVRDVSTAT',0,11,132,11,3
	.word	32928
	.byte	13,11,135,11,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	17386
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_EVRMONCTRL',0,11,140,11,3
	.word	32995
	.byte	13,11,143,11,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	17715
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_EVROSCCTRL',0,11,148,11,3
	.word	33063
	.byte	13,11,151,11,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	17936
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_EVROVMON',0,11,156,11,3
	.word	33131
	.byte	13,11,159,11,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	18099
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_EVRRSTCON',0,11,164,11,3
	.word	33197
	.byte	13,11,167,11,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	18371
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_EVRSDCOEFF1',0,11,172,11,3
	.word	33264
	.byte	13,11,175,11,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	18524
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_EVRSDCOEFF2',0,11,180,11,3
	.word	33333
	.byte	13,11,183,11,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	18680
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_EVRSDCOEFF3',0,11,188,11,3
	.word	33402
	.byte	13,11,191,11,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	18842
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_EVRSDCOEFF4',0,11,196,11,3
	.word	33471
	.byte	13,11,199,11,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	18985
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_EVRSDCOEFF5',0,11,204,11,3
	.word	33540
	.byte	13,11,207,11,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	19150
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_EVRSDCOEFF6',0,11,212,11,3
	.word	33609
	.byte	13,11,215,11,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	19295
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_EVRSDCTRL1',0,11,220,11,3
	.word	33678
	.byte	13,11,223,11,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	19476
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_EVRSDCTRL2',0,11,228,11,3
	.word	33746
	.byte	13,11,231,11,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	19650
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_EVRSDCTRL3',0,11,236,11,3
	.word	33814
	.byte	13,11,239,11,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	19810
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_EVRSDCTRL4',0,11,244,11,3
	.word	33882
	.byte	13,11,247,11,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	19954
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_EVRSTAT',0,11,252,11,3
	.word	33950
	.byte	13,11,255,11,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	20228
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_EVRTRIM',0,11,132,12,3
	.word	34015
	.byte	13,11,135,12,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	20367
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_EVRUVMON',0,11,140,12,3
	.word	34080
	.byte	13,11,143,12,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	20530
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_EXTCON',0,11,148,12,3
	.word	34146
	.byte	13,11,151,12,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	20748
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_FDR',0,11,156,12,3
	.word	34210
	.byte	13,11,159,12,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	20911
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_FMR',0,11,164,12,3
	.word	34271
	.byte	13,11,167,12,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	21247
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_ID',0,11,172,12,3
	.word	34332
	.byte	13,11,175,12,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	21354
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_IGCR',0,11,180,12,3
	.word	34392
	.byte	13,11,183,12,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	21806
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_IN',0,11,188,12,3
	.word	34454
	.byte	13,11,191,12,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	21905
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_IOCR',0,11,196,12,3
	.word	34514
	.byte	13,11,199,12,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	22055
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_LBISTCTRL0',0,11,204,12,3
	.word	34576
	.byte	13,11,207,12,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	22204
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_LBISTCTRL1',0,11,212,12,3
	.word	34644
	.byte	13,11,215,12,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	22365
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_LBISTCTRL2',0,11,220,12,3
	.word	34712
	.byte	13,11,223,12,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	22495
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_LCLCON',0,11,228,12,3
	.word	34780
	.byte	13,11,231,12,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	22627
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_LCLTEST',0,11,236,12,3
	.word	34844
	.byte	13,11,239,12,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	22742
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_MANID',0,11,244,12,3
	.word	34909
	.byte	13,11,247,12,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	22853
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_OMR',0,11,252,12,3
	.word	34972
	.byte	13,11,255,12,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	23011
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_OSCCON',0,11,132,13,3
	.word	35033
	.byte	13,11,135,13,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	23423
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_OUT',0,11,140,13,3
	.word	35097
	.byte	13,11,143,13,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	23524
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_OVCCON',0,11,148,13,3
	.word	35158
	.byte	13,11,151,13,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	23791
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_OVCENABLE',0,11,156,13,3
	.word	35222
	.byte	13,11,159,13,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	23927
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_PDISC',0,11,164,13,3
	.word	35289
	.byte	13,11,167,13,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	24038
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_PDR',0,11,172,13,3
	.word	35352
	.byte	13,11,175,13,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	24171
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_PDRR',0,11,180,13,3
	.word	35413
	.byte	13,11,183,13,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	24374
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_PLLCON0',0,11,188,13,3
	.word	35475
	.byte	13,11,191,13,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	24730
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_PLLCON1',0,11,196,13,3
	.word	35540
	.byte	13,11,199,13,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	24908
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_PLLCON2',0,11,204,13,3
	.word	35605
	.byte	13,11,207,13,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	25008
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_PLLERAYCON0',0,11,212,13,3
	.word	35670
	.byte	13,11,215,13,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	25378
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_PLLERAYCON1',0,11,220,13,3
	.word	35739
	.byte	13,11,223,13,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	25564
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_PLLERAYSTAT',0,11,228,13,3
	.word	35808
	.byte	13,11,231,13,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	25762
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_PLLSTAT',0,11,236,13,3
	.word	35877
	.byte	13,11,239,13,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	25995
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_PMCSR',0,11,244,13,3
	.word	35942
	.byte	13,11,247,13,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	26147
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_PMSWCR0',0,11,252,13,3
	.word	36005
	.byte	13,11,255,13,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	26714
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_PMSWCR1',0,11,132,14,3
	.word	36070
	.byte	13,11,135,14,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	27008
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_PMSWCR2',0,11,140,14,3
	.word	36135
	.byte	13,11,143,14,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	27286
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_PMSWSTAT',0,11,148,14,3
	.word	36200
	.byte	13,11,151,14,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	27782
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_PMSWSTATCLR',0,11,156,14,3
	.word	36266
	.byte	13,11,159,14,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	28304
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_RSTCON',0,11,164,14,3
	.word	36335
	.byte	13,11,167,14,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	28095
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_RSTCON2',0,11,172,14,3
	.word	36399
	.byte	13,11,175,14,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	28515
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_RSTSTAT',0,11,180,14,3
	.word	36464
	.byte	13,11,183,14,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	28947
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_SAFECON',0,11,188,14,3
	.word	36529
	.byte	13,11,191,14,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	29043
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_STSTAT',0,11,196,14,3
	.word	36594
	.byte	13,11,199,14,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	29303
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_SWRSTCON',0,11,204,14,3
	.word	36658
	.byte	13,11,207,14,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	29428
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_SYSCON',0,11,212,14,3
	.word	36724
	.byte	13,11,215,14,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	29625
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_TRAPCLR',0,11,220,14,3
	.word	36788
	.byte	13,11,223,14,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	29778
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_TRAPDIS',0,11,228,14,3
	.word	36853
	.byte	13,11,231,14,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	29931
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_TRAPSET',0,11,236,14,3
	.word	36918
	.byte	13,11,239,14,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	30084
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_TRAPSTAT',0,11,244,14,3
	.word	36983
	.byte	13,11,247,14,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	30255
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_WDTCPU_CON0',0,11,252,14,3
	.word	37049
	.byte	13,11,255,14,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	30385
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_WDTCPU_CON1',0,11,132,15,3
	.word	37118
	.byte	13,11,135,15,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	30623
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_WDTCPU_SR',0,11,140,15,3
	.word	37187
	.byte	13,11,143,15,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	30846
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_WDTS_CON0',0,11,148,15,3
	.word	37254
	.byte	13,11,151,15,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	30972
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_WDTS_CON1',0,11,156,15,3
	.word	37321
	.byte	13,11,159,15,9,4,14
	.byte	'U',0
	.word	561
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	577
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	31224
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_WDTS_SR',0,11,164,15,3
	.word	37388
	.byte	11
	.byte	'_Ifx_SCU_WDTCPU',0,11,175,15,25,12,14
	.byte	'CON0',0
	.word	37049
	.byte	4,2,35,0,14
	.byte	'CON1',0
	.word	37118
	.byte	4,2,35,4,14
	.byte	'SR',0
	.word	37187
	.byte	4,2,35,8,0,8
	.word	37453
	.byte	23
	.byte	'Ifx_SCU_WDTCPU',0,11,180,15,3
	.word	37516
	.byte	11
	.byte	'_Ifx_SCU_WDTS',0,11,183,15,25,12,14
	.byte	'CON0',0
	.word	37254
	.byte	4,2,35,0,14
	.byte	'CON1',0
	.word	37321
	.byte	4,2,35,4,14
	.byte	'SR',0
	.word	37388
	.byte	4,2,35,8,0,8
	.word	37545
	.byte	23
	.byte	'Ifx_SCU_WDTS',0,11,188,15,3
	.word	37606
	.byte	23
	.byte	'IfxPort_ControlledBy',0,3,79,3
	.word	10631
	.byte	23
	.byte	'IfxPort_InputMode',0,3,89,3
	.word	10201
	.byte	23
	.byte	'IfxPort_Mode',0,3,116,3
	.word	8823
	.byte	23
	.byte	'IfxPort_OutputIdx',0,3,130,1,3
	.word	10405
	.byte	23
	.byte	'IfxPort_OutputMode',0,3,138,1,3
	.word	10335
	.byte	23
	.byte	'IfxPort_PadDriver',0,3,158,1,3
	.word	9502
	.byte	23
	.byte	'IfxPort_PadSupply',0,3,166,1,3
	.word	10693
	.byte	23
	.byte	'IfxPort_State',0,3,178,1,3
	.word	8381
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L81:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,21,0,54,15,39,12,0,0,3,15,0,73,19,0,0,4,46,1,3,8,32,13
	.byte	58,15,59,15,57,15,54,15,39,12,0,0,5,5,0,3,8,58,15,59,15,57,15,73,19,0,0,6,11,0,0,0,7,59,0,3,8,0,0,8,53
	.byte	0,73,19,0,0,9,36,0,3,8,11,15,62,15,0,0,10,46,1,3,8,32,13,58,15,59,15,57,15,73,19,54,15,39,12,0,0,11,19
	.byte	1,3,8,58,15,59,15,57,15,11,15,0,0,12,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,13,23,1,58,15,59,15,57
	.byte	15,11,15,0,0,14,13,0,3,8,73,19,11,15,56,9,0,0,15,1,1,11,15,73,19,0,0,16,33,0,47,15,0,0,17,4,1,58,15,59
	.byte	15,57,15,11,15,0,0,18,40,0,3,8,28,13,0,0,19,46,1,49,19,0,0,20,5,0,49,19,0,0,21,46,1,3,8,58,15,59,15,57
	.byte	15,54,15,39,12,63,12,60,12,0,0,22,46,0,3,8,58,15,59,15,57,15,73,19,54,15,39,12,63,12,60,12,0,0,23,22,0
	.byte	3,8,58,15,59,15,57,15,73,19,0,0,24,21,0,54,15,0,0,25,19,1,58,15,59,15,57,15,11,15,0,0,26,38,0,73,19,0
	.byte	0,27,52,0,3,8,58,15,59,15,57,15,73,19,63,12,60,12,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L82:
	.word	.L432-.L431
.L431:
	.half	3
	.word	.L434-.L433
.L433:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg',0
	.byte	0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_Intrinsics.h',0
	.byte	0,0,0
	.byte	'..\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Port\\Std\\IfxPort.h',0,0,0,0
	.byte	'IfxPort_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Scu\\Std\\IfxScuWdt.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxPort_cfg.h',0
	.byte	0,0,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Port/Std/IfxPort.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Platform_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Ifx_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxScu_cfg.h',0
	.byte	0,0,0
	.byte	'IfxScu_regdef.h',0,1,0,0,0
.L434:
.L432:
	.sdecl	'.debug_info',debug,cluster('IfxPort_disableEmergencyStop')
	.sect	'.debug_info'
.L83:
	.word	380
	.half	3
	.word	.L84
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Port/Std/IfxPort.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L86,.L85
	.byte	2
	.word	.L79
	.byte	3
	.byte	'IfxPort_disableEmergencyStop',0,1,55,9
	.word	.L148
	.byte	1,1,1
	.word	.L54,.L149,.L53
	.byte	4
	.byte	'port',0,1,55,45
	.word	.L150,.L151
	.byte	4
	.byte	'pinIndex',0,1,55,57
	.word	.L148,.L152
	.byte	5
	.word	.L54,.L149
	.byte	6
	.byte	'portIndex',0,1,57,13
	.word	.L153,.L154
	.byte	6
	.byte	'result',0,1,58,13
	.word	.L148,.L155
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_disableEmergencyStop')
	.sect	'.debug_abbrev'
.L84:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxPort_disableEmergencyStop')
	.sect	'.debug_line'
.L85:
	.word	.L436-.L435
.L435:
	.half	3
	.word	.L438-.L437
.L437:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Port/Std/IfxPort.c',0,0,0,0,0
.L438:
	.byte	5,20,7,0,5,2
	.word	.L54
	.byte	3,57,1,9
	.half	.L309-.L54
	.byte	3,2,1,5,56,9
	.half	.L310-.L309
	.byte	1,5,41,9
	.half	.L3-.L310
	.byte	3,2,1,5,21,9
	.half	.L439-.L3
	.byte	1,5,41,9
	.half	.L440-.L439
	.byte	1,5,52,9
	.half	.L441-.L440
	.byte	1,5,9,9
	.half	.L442-.L441
	.byte	1,5,18,7,9
	.half	.L443-.L442
	.byte	3,2,1,5,21,9
	.half	.L444-.L443
	.byte	1,5,56,9
	.half	.L445-.L444
	.byte	1,5,36,9
	.half	.L446-.L445
	.byte	1,5,56,9
	.half	.L447-.L446
	.byte	1,5,67,9
	.half	.L448-.L447
	.byte	1,5,34,9
	.half	.L449-.L448
	.byte	1,5,13,9
	.half	.L450-.L449
	.byte	1,5,40,7,9
	.half	.L451-.L450
	.byte	3,2,1,5,24,9
	.half	.L308-.L451
	.byte	3,1,1,5,13,9
	.half	.L5-.L308
	.byte	3,3,1,5,67,9
	.half	.L4-.L5
	.byte	3,118,1,5,37,9
	.half	.L2-.L4
	.byte	1,5,56,9
	.half	.L452-.L2
	.byte	1,5,5,7,9
	.half	.L6-.L452
	.byte	3,14,1,5,1,9
	.half	.L7-.L6
	.byte	3,1,1,7,9
	.half	.L87-.L7
	.byte	0,1,1
.L436:
	.sdecl	'.debug_ranges',debug,cluster('IfxPort_disableEmergencyStop')
	.sect	'.debug_ranges'
.L86:
	.word	-1,.L54,0,.L87-.L54,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_enableEmergencyStop')
	.sect	'.debug_info'
.L88:
	.word	379
	.half	3
	.word	.L89
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Port/Std/IfxPort.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L91,.L90
	.byte	2
	.word	.L79
	.byte	3
	.byte	'IfxPort_enableEmergencyStop',0,1,78,9
	.word	.L148
	.byte	1,1,1
	.word	.L56,.L156,.L55
	.byte	4
	.byte	'port',0,1,78,44
	.word	.L150,.L157
	.byte	4
	.byte	'pinIndex',0,1,78,56
	.word	.L148,.L158
	.byte	5
	.word	.L56,.L156
	.byte	6
	.byte	'portIndex',0,1,80,13
	.word	.L153,.L159
	.byte	6
	.byte	'result',0,1,81,13
	.word	.L148,.L160
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_enableEmergencyStop')
	.sect	'.debug_abbrev'
.L89:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxPort_enableEmergencyStop')
	.sect	'.debug_line'
.L90:
	.word	.L454-.L453
.L453:
	.half	3
	.word	.L456-.L455
.L455:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Port/Std/IfxPort.c',0,0,0,0,0
.L456:
	.byte	5,9,7,0,5,2
	.word	.L56
	.byte	3,205,0,1,5,20,9
	.half	.L312-.L56
	.byte	3,3,1,9
	.half	.L313-.L312
	.byte	3,2,1,5,56,9
	.half	.L315-.L313
	.byte	1,5,41,9
	.half	.L9-.L315
	.byte	3,2,1,5,21,9
	.half	.L457-.L9
	.byte	1,5,41,9
	.half	.L458-.L457
	.byte	1,5,52,9
	.half	.L459-.L458
	.byte	1,5,9,9
	.half	.L460-.L459
	.byte	1,5,18,7,9
	.half	.L461-.L460
	.byte	3,2,1,5,21,9
	.half	.L462-.L461
	.byte	1,5,56,9
	.half	.L463-.L462
	.byte	1,5,36,9
	.half	.L464-.L463
	.byte	1,5,56,9
	.half	.L465-.L464
	.byte	1,5,67,9
	.half	.L466-.L465
	.byte	1,5,34,9
	.half	.L467-.L466
	.byte	1,5,13,9
	.half	.L468-.L467
	.byte	1,5,38,7,9
	.half	.L469-.L468
	.byte	3,2,1,5,24,9
	.half	.L314-.L469
	.byte	3,1,1,5,67,9
	.half	.L10-.L314
	.byte	3,121,1,5,37,9
	.half	.L8-.L10
	.byte	1,5,56,9
	.half	.L470-.L8
	.byte	1,5,5,7,9
	.half	.L471-.L470
	.byte	3,12,1,5,1,9
	.half	.L12-.L471
	.byte	3,1,1,7,9
	.half	.L92-.L12
	.byte	0,1,1
.L454:
	.sdecl	'.debug_ranges',debug,cluster('IfxPort_enableEmergencyStop')
	.sect	'.debug_ranges'
.L91:
	.word	-1,.L56,0,.L92-.L56,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_setPinMode')
	.sect	'.debug_info'
.L93:
	.word	490
	.half	3
	.word	.L94
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Port/Std/IfxPort.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L96,.L95
	.byte	2
	.word	.L79
	.byte	3
	.byte	'IfxPort_setPinMode',0,1,152,2,6,1,1,1
	.word	.L72,.L161,.L71
	.byte	4
	.byte	'port',0,1,152,2,32
	.word	.L150,.L162
	.byte	4
	.byte	'pinIndex',0,1,152,2,44
	.word	.L148,.L163
	.byte	4
	.byte	'mode',0,1,152,2,67
	.word	.L164,.L165
	.byte	5
	.word	.L72,.L161
	.byte	6
	.byte	'iocr',0,1,154,2,27
	.word	.L166,.L167
	.byte	6
	.byte	'iocrIndex',0,1,155,2,27
	.word	.L148,.L168
	.byte	6
	.byte	'shift',0,1,156,2,27
	.word	.L148,.L169
	.byte	5
	.word	.L170,.L48
	.byte	6
	.byte	'passwd',0,1,160,2,16
	.word	.L171,.L172
	.byte	0,7
	.word	.L173,.L174,.L175
	.byte	8
	.word	.L176,.L177
	.byte	8
	.word	.L178,.L179
	.byte	8
	.word	.L180,.L181
	.byte	9
	.word	.L182,.L174,.L175
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_setPinMode')
	.sect	'.debug_abbrev'
.L94:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,5,0,49,16,2,6,0,0,9,11,0,49,16,17,1,18
	.byte	1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxPort_setPinMode')
	.sect	'.debug_line'
.L95:
	.word	.L473-.L472
.L472:
	.half	3
	.word	.L475-.L474
.L474:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Port/Std/IfxPort.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0,0
.L475:
	.byte	5,6,7,0,5,2
	.word	.L72
	.byte	3,151,2,1,5,45,9
	.half	.L476-.L72
	.byte	3,2,1,5,51,9
	.half	.L379-.L476
	.byte	3,1,1,5,49,9
	.half	.L380-.L379
	.byte	1,9
	.half	.L382-.L380
	.byte	3,1,1,5,57,9
	.half	.L381-.L382
	.byte	1,5,18,9
	.half	.L383-.L381
	.byte	3,2,1,5,5,9
	.half	.L477-.L383
	.byte	1,5,57,7,9
	.half	.L170-.L477
	.byte	3,2,1,5,35,9
	.half	.L385-.L170
	.byte	3,1,1,5,20,9
	.half	.L386-.L385
	.byte	3,1,1,5,28,9
	.half	.L478-.L386
	.byte	1,5,30,9
	.half	.L387-.L478
	.byte	1,5,26,9
	.half	.L388-.L387
	.byte	1,5,23,9
	.half	.L479-.L388
	.byte	1,5,33,9
	.half	.L480-.L479
	.byte	3,1,1,5,5,9
	.half	.L48-.L480
	.byte	3,3,1,4,2,9
	.half	.L174-.L48
	.byte	3,105,1,4,1,5,1,9
	.half	.L175-.L174
	.byte	3,24,1,7,9
	.half	.L97-.L175
	.byte	0,1,1
.L473:
	.sdecl	'.debug_ranges',debug,cluster('IfxPort_setPinMode')
	.sect	'.debug_ranges'
.L96:
	.word	-1,.L72,0,.L97-.L72,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_setPinPadDriver')
	.sect	'.debug_info'
.L98:
	.word	498
	.half	3
	.word	.L99
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Port/Std/IfxPort.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L101,.L100
	.byte	2
	.word	.L79
	.byte	3
	.byte	'IfxPort_setPinPadDriver',0,1,215,2,6,1,1,1
	.word	.L78,.L183,.L77
	.byte	4
	.byte	'port',0,1,215,2,37
	.word	.L150,.L184
	.byte	4
	.byte	'pinIndex',0,1,215,2,49
	.word	.L148,.L185
	.byte	4
	.byte	'padDriver',0,1,215,2,77
	.word	.L186,.L187
	.byte	5
	.word	.L78,.L183
	.byte	6
	.byte	'passwd',0,1,217,2,12
	.word	.L171,.L188
	.byte	5
	.word	.L189,.L190
	.byte	6
	.byte	'pdr',0,1,221,2,26
	.word	.L191,.L192
	.byte	6
	.byte	'pdrIndex',0,1,222,2,26
	.word	.L148,.L193
	.byte	6
	.byte	'shift',0,1,223,2,26
	.word	.L148,.L194
	.byte	7
	.word	.L173,.L195,.L190
	.byte	8
	.word	.L176,.L196
	.byte	8
	.word	.L178,.L197
	.byte	8
	.word	.L180,.L198
	.byte	9
	.word	.L182,.L195,.L190
	.byte	0,0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_setPinPadDriver')
	.sect	'.debug_abbrev'
.L99:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,5,0,49,16,2,6,0,0,9,11,0,49,16,17,1,18
	.byte	1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxPort_setPinPadDriver')
	.sect	'.debug_line'
.L100:
	.word	.L482-.L481
.L481:
	.half	3
	.word	.L484-.L483
.L483:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Port/Std/IfxPort.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0,0
.L484:
	.byte	5,6,7,0,5,2
	.word	.L78
	.byte	3,214,2,1,5,53,9
	.half	.L485-.L78
	.byte	3,2,1,5,19,9
	.half	.L418-.L485
	.byte	1,5,31,9
	.half	.L422-.L418
	.byte	3,2,1,5,62,9
	.half	.L189-.L422
	.byte	3,2,1,5,49,9
	.half	.L420-.L189
	.byte	3,1,1,5,47,9
	.half	.L424-.L420
	.byte	1,9
	.half	.L426-.L424
	.byte	3,1,1,5,55,9
	.half	.L425-.L426
	.byte	1,5,9,9
	.half	.L428-.L425
	.byte	3,1,1,4,2,5,5,9
	.half	.L195-.L428
	.byte	3,175,127,1,4,1,5,29,9
	.half	.L190-.L195
	.byte	3,211,0,1,5,1,9
	.half	.L427-.L190
	.byte	3,1,1,7,9
	.half	.L102-.L427
	.byte	0,1,1
.L482:
	.sdecl	'.debug_ranges',debug,cluster('IfxPort_setPinPadDriver')
	.sect	'.debug_ranges'
.L101:
	.word	-1,.L78,0,.L102-.L78,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_getAddress')
	.sect	'.debug_info'
.L103:
	.word	341
	.half	3
	.word	.L104
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Port/Std/IfxPort.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L106,.L105
	.byte	2
	.word	.L79
	.byte	3
	.byte	'IfxPort_getAddress',0,1,99,8
	.word	.L150
	.byte	1,1,1
	.word	.L58,.L199,.L57
	.byte	4
	.byte	'port',0,1,99,41
	.word	.L200,.L201
	.byte	5
	.word	.L58,.L199
	.byte	6
	.byte	'module',0,1,101,12
	.word	.L150,.L202
	.byte	6
	.byte	'i',0,1,102,12
	.word	.L148,.L203
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_getAddress')
	.sect	'.debug_abbrev'
.L104:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxPort_getAddress')
	.sect	'.debug_line'
.L105:
	.word	.L487-.L486
.L486:
	.half	3
	.word	.L489-.L488
.L488:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Port/Std/IfxPort.c',0,0,0,0,0
.L489:
	.byte	5,19,7,0,5,2
	.word	.L58
	.byte	3,228,0,1,9
	.half	.L318-.L58
	.byte	3,1,1,5,61,9
	.half	.L319-.L318
	.byte	3,2,1,5,33,9
	.half	.L14-.L319
	.byte	3,2,1,5,13,9
	.half	.L490-.L14
	.byte	1,5,33,9
	.half	.L491-.L490
	.byte	1,5,36,9
	.half	.L492-.L491
	.byte	1,5,9,9
	.half	.L493-.L492
	.byte	1,5,42,7,9
	.half	.L494-.L493
	.byte	3,2,1,5,22,9
	.half	.L495-.L494
	.byte	1,5,42,9
	.half	.L496-.L495
	.byte	1,5,45,9
	.half	.L497-.L496
	.byte	1,5,10,9
	.half	.L15-.L497
	.byte	3,3,1,5,12,9
	.half	.L13-.L15
	.byte	3,121,1,5,49,7,9
	.half	.L498-.L13
	.byte	1,5,5,7,9
	.half	.L16-.L498
	.byte	3,10,1,5,1,9
	.half	.L17-.L16
	.byte	3,1,1,7,9
	.half	.L107-.L17
	.byte	0,1,1
.L487:
	.sdecl	'.debug_ranges',debug,cluster('IfxPort_getAddress')
	.sect	'.debug_ranges'
.L106:
	.word	-1,.L58,0,.L107-.L58,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_getIndex')
	.sect	'.debug_info'
.L108:
	.word	343
	.half	3
	.word	.L109
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Port/Std/IfxPort.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L111,.L110
	.byte	2
	.word	.L79
	.byte	3
	.byte	'IfxPort_getIndex',0,1,118,15
	.word	.L200
	.byte	1,1,1
	.word	.L60,.L204,.L59
	.byte	4
	.byte	'port',0,1,118,39
	.word	.L150,.L205
	.byte	5
	.word	.L60,.L204
	.byte	6
	.byte	'index',0,1,120,19
	.word	.L206,.L207
	.byte	6
	.byte	'result',0,1,121,19
	.word	.L200,.L208
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_getIndex')
	.sect	'.debug_abbrev'
.L109:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxPort_getIndex')
	.sect	'.debug_line'
.L110:
	.word	.L500-.L499
.L499:
	.half	3
	.word	.L502-.L501
.L501:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Port/Std/IfxPort.c',0,0,0,0,0
.L502:
	.byte	5,12,7,0,5,2
	.word	.L60
	.byte	3,250,0,1,5,16,9
	.half	.L320-.L60
	.byte	3,2,1,5,48,9
	.half	.L321-.L320
	.byte	1,5,33,9
	.half	.L19-.L321
	.byte	3,2,1,5,13,9
	.half	.L503-.L19
	.byte	1,5,33,9
	.half	.L504-.L503
	.byte	1,5,40,9
	.half	.L505-.L504
	.byte	1,5,9,9
	.half	.L506-.L505
	.byte	1,5,57,7,9
	.half	.L507-.L506
	.byte	3,2,1,5,37,9
	.half	.L508-.L507
	.byte	1,5,57,9
	.half	.L509-.L508
	.byte	1,5,64,9
	.half	.L510-.L509
	.byte	1,5,22,9
	.half	.L511-.L510
	.byte	1,5,13,9
	.half	.L512-.L511
	.byte	3,1,1,5,55,9
	.half	.L20-.L512
	.byte	3,123,1,5,48,9
	.half	.L18-.L20
	.byte	1,5,5,7,9
	.half	.L21-.L18
	.byte	3,9,1,5,1,9
	.half	.L22-.L21
	.byte	3,1,1,7,9
	.half	.L112-.L22
	.byte	0,1,1
.L500:
	.sdecl	'.debug_ranges',debug,cluster('IfxPort_getIndex')
	.sect	'.debug_ranges'
.L111:
	.word	-1,.L60,0,.L112-.L60,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_setGroupModeInput')
	.sect	'.debug_info'
.L113:
	.word	560
	.half	3
	.word	.L114
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Port/Std/IfxPort.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L116,.L115
	.byte	2
	.word	.L79
	.byte	3
	.byte	'IfxPort_setGroupModeInput',0,1,158,1,6,1,1,1
	.word	.L66,.L209,.L65
	.byte	4
	.byte	'port',0,1,158,1,39
	.word	.L150,.L210
	.byte	4
	.byte	'pinIndex',0,1,158,1,51
	.word	.L148,.L211
	.byte	4
	.byte	'mask',0,1,158,1,68
	.word	.L171,.L212
	.byte	4
	.byte	'mode',0,1,158,1,92
	.word	.L213,.L214
	.byte	5
	.word	.L66,.L209
	.byte	6
	.byte	'i',0,1,160,1,12
	.word	.L206,.L215
	.byte	6
	.byte	'iocrVal',0,1,161,1,12
	.word	.L216,.L217
	.byte	6
	.byte	'iocrMask',0,1,162,1,12
	.word	.L216,.L218
	.byte	5
	.word	.L219,.L209
	.byte	6
	.byte	'imask',0,1,172,1,12
	.word	.L206,.L220
	.byte	5
	.word	.L221,.L27
	.byte	6
	.byte	'index',0,1,178,1,20
	.word	.L206,.L222
	.byte	6
	.byte	'shift',0,1,179,1,20
	.word	.L206,.L223
	.byte	0,7
	.word	.L173,.L224,.L30
	.byte	8
	.word	.L176,.L225
	.byte	8
	.word	.L178,.L226
	.byte	8
	.word	.L180,.L227
	.byte	9
	.word	.L182,.L224,.L30
	.byte	0,0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_setGroupModeInput')
	.sect	'.debug_abbrev'
.L114:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,5,0,49,16,2,6,0,0,9,11,0,49,16,17,1,18
	.byte	1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxPort_setGroupModeInput')
	.sect	'.debug_line'
.L115:
	.word	.L514-.L513
.L513:
	.half	3
	.word	.L516-.L515
.L515:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Port/Std/IfxPort.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0,0
.L516:
	.byte	5,6,7,0,5,2
	.word	.L66
	.byte	3,157,1,1,5,12,9
	.half	.L342-.L66
	.byte	3,7,1,5,22,9
	.half	.L344-.L342
	.byte	1,5,16,9
	.half	.L24-.L344
	.byte	3,2,1,5,23,9
	.half	.L517-.L24
	.byte	1,5,21,9
	.half	.L518-.L517
	.byte	1,5,17,9
	.half	.L519-.L518
	.byte	3,1,1,5,23,9
	.half	.L520-.L519
	.byte	1,5,21,9
	.half	.L521-.L520
	.byte	1,5,25,9
	.half	.L522-.L521
	.byte	3,125,1,5,22,9
	.half	.L23-.L522
	.byte	1,5,33,7,9
	.half	.L219-.L23
	.byte	3,7,1,5,30,9
	.half	.L343-.L219
	.byte	3,2,1,5,23,9
	.half	.L26-.L343
	.byte	3,2,1,5,26,9
	.half	.L523-.L26
	.byte	1,5,20,9
	.half	.L524-.L523
	.byte	1,5,9,9
	.half	.L525-.L524
	.byte	1,5,32,7,9
	.half	.L221-.L525
	.byte	3,2,1,5,30,9
	.half	.L526-.L221
	.byte	1,5,31,9
	.half	.L346-.L526
	.byte	3,1,1,5,39,9
	.half	.L527-.L346
	.byte	1,5,21,9
	.half	.L347-.L527
	.byte	3,1,1,5,39,9
	.half	.L528-.L347
	.byte	1,5,45,9
	.half	.L529-.L528
	.byte	1,5,29,9
	.half	.L530-.L529
	.byte	1,5,20,9
	.half	.L531-.L530
	.byte	3,1,1,5,39,9
	.half	.L532-.L531
	.byte	1,5,29,9
	.half	.L533-.L532
	.byte	1,5,33,9
	.half	.L27-.L533
	.byte	3,121,1,5,28,9
	.half	.L25-.L27
	.byte	1,5,30,9
	.half	.L534-.L25
	.byte	1,5,12,7,9
	.half	.L535-.L534
	.byte	3,12,1,5,22,9
	.half	.L345-.L535
	.byte	1,5,21,9
	.half	.L29-.L345
	.byte	3,2,1,5,9,9
	.half	.L536-.L29
	.byte	1,5,13,7,9
	.half	.L537-.L536
	.byte	3,2,1,4,2,5,5,9
	.half	.L224-.L537
	.byte	3,209,0,1,4,1,5,25,9
	.half	.L30-.L224
	.byte	3,171,127,1,5,22,9
	.half	.L28-.L30
	.byte	1,5,1,7,9
	.half	.L538-.L28
	.byte	3,7,1,7,9
	.half	.L117-.L538
	.byte	0,1,1
.L514:
	.sdecl	'.debug_ranges',debug,cluster('IfxPort_setGroupModeInput')
	.sect	'.debug_ranges'
.L116:
	.word	-1,.L66,0,.L117-.L66,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_setGroupModeOutput')
	.sect	'.debug_info'
.L118:
	.word	580
	.half	3
	.word	.L119
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Port/Std/IfxPort.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L121,.L120
	.byte	2
	.word	.L79
	.byte	3
	.byte	'IfxPort_setGroupModeOutput',0,1,196,1,6,1,1,1
	.word	.L68,.L228,.L67
	.byte	4
	.byte	'port',0,1,196,1,40
	.word	.L150,.L229
	.byte	4
	.byte	'pinIndex',0,1,196,1,52
	.word	.L148,.L230
	.byte	4
	.byte	'mask',0,1,196,1,69
	.word	.L171,.L231
	.byte	4
	.byte	'mode',0,1,196,1,94
	.word	.L232,.L233
	.byte	4
	.byte	'index',0,1,196,1,118
	.word	.L234,.L235
	.byte	5
	.word	.L68,.L228
	.byte	6
	.byte	'i',0,1,198,1,12
	.word	.L206,.L236
	.byte	6
	.byte	'iocrVal',0,1,199,1,12
	.word	.L216,.L237
	.byte	6
	.byte	'iocrMask',0,1,200,1,12
	.word	.L216,.L238
	.byte	5
	.word	.L239,.L228
	.byte	6
	.byte	'imask',0,1,212,1,12
	.word	.L206,.L240
	.byte	5
	.word	.L241,.L36
	.byte	6
	.byte	'index',0,1,218,1,20
	.word	.L206,.L242
	.byte	6
	.byte	'shift',0,1,219,1,20
	.word	.L206,.L243
	.byte	0,7
	.word	.L173,.L244,.L39
	.byte	8
	.word	.L176,.L245
	.byte	8
	.word	.L178,.L246
	.byte	8
	.word	.L180,.L247
	.byte	9
	.word	.L182,.L244,.L39
	.byte	0,0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_setGroupModeOutput')
	.sect	'.debug_abbrev'
.L119:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,5,0,49,16,2,6,0,0,9,11,0,49,16,17,1,18
	.byte	1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxPort_setGroupModeOutput')
	.sect	'.debug_line'
.L120:
	.word	.L540-.L539
.L539:
	.half	3
	.word	.L542-.L541
.L541:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Port/Std/IfxPort.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0,0
.L542:
	.byte	5,6,7,0,5,2
	.word	.L68
	.byte	3,195,1,1,5,5,9
	.half	.L350-.L68
	.byte	3,6,1,5,12,9
	.half	.L31-.L350
	.byte	3,3,1,5,22,9
	.half	.L352-.L31
	.byte	1,5,16,9
	.half	.L33-.L352
	.byte	3,2,1,5,23,9
	.half	.L543-.L33
	.byte	1,5,21,9
	.half	.L544-.L543
	.byte	1,5,17,9
	.half	.L545-.L544
	.byte	3,1,1,5,23,9
	.half	.L546-.L545
	.byte	1,5,21,9
	.half	.L547-.L546
	.byte	1,5,25,9
	.half	.L548-.L547
	.byte	3,125,1,5,22,9
	.half	.L32-.L548
	.byte	1,5,33,7,9
	.half	.L239-.L32
	.byte	3,7,1,5,30,9
	.half	.L351-.L239
	.byte	3,2,1,5,23,9
	.half	.L35-.L351
	.byte	3,2,1,5,26,9
	.half	.L549-.L35
	.byte	1,5,20,9
	.half	.L550-.L549
	.byte	1,5,9,9
	.half	.L551-.L550
	.byte	1,5,32,7,9
	.half	.L241-.L551
	.byte	3,2,1,5,30,9
	.half	.L552-.L241
	.byte	1,5,31,9
	.half	.L354-.L552
	.byte	3,1,1,5,39,9
	.half	.L553-.L354
	.byte	1,5,21,9
	.half	.L356-.L553
	.byte	3,1,1,5,39,9
	.half	.L554-.L356
	.byte	1,5,45,9
	.half	.L555-.L554
	.byte	1,5,29,9
	.half	.L556-.L555
	.byte	1,5,20,9
	.half	.L557-.L556
	.byte	3,1,1,5,38,9
	.half	.L558-.L557
	.byte	1,5,47,9
	.half	.L355-.L558
	.byte	1,5,29,9
	.half	.L559-.L355
	.byte	1,5,33,9
	.half	.L36-.L559
	.byte	3,121,1,5,28,9
	.half	.L34-.L36
	.byte	1,5,30,9
	.half	.L560-.L34
	.byte	1,5,12,7,9
	.half	.L561-.L560
	.byte	3,12,1,5,22,9
	.half	.L353-.L561
	.byte	1,5,21,9
	.half	.L38-.L353
	.byte	3,2,1,5,9,9
	.half	.L562-.L38
	.byte	1,5,13,7,9
	.half	.L563-.L562
	.byte	3,2,1,4,2,5,5,9
	.half	.L244-.L563
	.byte	3,41,1,4,1,5,25,9
	.half	.L39-.L244
	.byte	3,83,1,5,22,9
	.half	.L37-.L39
	.byte	1,5,1,7,9
	.half	.L564-.L37
	.byte	3,7,1,7,9
	.half	.L122-.L564
	.byte	0,1,1
.L540:
	.sdecl	'.debug_ranges',debug,cluster('IfxPort_setGroupModeOutput')
	.sect	'.debug_ranges'
.L121:
	.word	-1,.L68,0,.L122-.L68,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_setGroupPadDriver')
	.sect	'.debug_info'
.L123:
	.word	593
	.half	3
	.word	.L124
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Port/Std/IfxPort.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L126,.L125
	.byte	2
	.word	.L79
	.byte	3
	.byte	'IfxPort_setGroupPadDriver',0,1,236,1,6,1,1,1
	.word	.L70,.L248,.L69
	.byte	4
	.byte	'port',0,1,236,1,39
	.word	.L150,.L249
	.byte	4
	.byte	'pinIndex',0,1,236,1,51
	.word	.L148,.L250
	.byte	4
	.byte	'mask',0,1,236,1,68
	.word	.L171,.L251
	.byte	4
	.byte	'padDriver',0,1,236,1,92
	.word	.L186,.L252
	.byte	5
	.word	.L70,.L248
	.byte	6
	.byte	'passwd',0,1,238,1,12
	.word	.L171,.L253
	.byte	5
	.word	.L254,.L255
	.byte	6
	.byte	'i',0,1,242,1,16
	.word	.L206,.L256
	.byte	6
	.byte	'pdrVal',0,1,243,1,16
	.word	.L257,.L258
	.byte	6
	.byte	'pdrMask',0,1,244,1,16
	.word	.L257,.L259
	.byte	5
	.word	.L260,.L255
	.byte	6
	.byte	'imask',0,1,254,1,16
	.word	.L206,.L261
	.byte	5
	.word	.L262,.L44
	.byte	6
	.byte	'index',0,1,132,2,24
	.word	.L206,.L263
	.byte	6
	.byte	'shift',0,1,133,2,24
	.word	.L206,.L264
	.byte	0,7
	.word	.L173,.L265,.L47
	.byte	8
	.word	.L176,.L266
	.byte	8
	.word	.L178,.L267
	.byte	8
	.word	.L180,.L268
	.byte	9
	.word	.L182,.L265,.L47
	.byte	0,0,0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_setGroupPadDriver')
	.sect	'.debug_abbrev'
.L124:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,5,0,49,16,2,6,0,0,9,11,0,49,16,17,1,18
	.byte	1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxPort_setGroupPadDriver')
	.sect	'.debug_line'
.L125:
	.word	.L566-.L565
.L565:
	.half	3
	.word	.L568-.L567
.L567:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Port/Std/IfxPort.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0,0
.L568:
	.byte	5,6,7,0,5,2
	.word	.L70
	.byte	3,235,1,1,5,53,9
	.half	.L360-.L70
	.byte	3,2,1,5,19,9
	.half	.L358-.L360
	.byte	1,5,31,9
	.half	.L362-.L358
	.byte	3,2,1,5,16,9
	.half	.L254-.L362
	.byte	3,7,1,5,26,9
	.half	.L363-.L254
	.byte	1,5,19,9
	.half	.L41-.L363
	.byte	3,2,1,5,26,9
	.half	.L569-.L41
	.byte	1,5,24,9
	.half	.L570-.L569
	.byte	1,5,20,9
	.half	.L571-.L570
	.byte	3,1,1,5,26,9
	.half	.L572-.L571
	.byte	1,5,24,9
	.half	.L573-.L572
	.byte	1,5,29,9
	.half	.L574-.L573
	.byte	3,125,1,5,26,9
	.half	.L40-.L574
	.byte	1,5,37,7,9
	.half	.L260-.L40
	.byte	3,7,1,5,34,9
	.half	.L364-.L260
	.byte	3,2,1,5,27,9
	.half	.L43-.L364
	.byte	3,2,1,5,30,9
	.half	.L365-.L43
	.byte	1,5,24,9
	.half	.L366-.L365
	.byte	1,5,13,9
	.half	.L575-.L366
	.byte	1,5,36,7,9
	.half	.L262-.L575
	.byte	3,2,1,5,34,9
	.half	.L367-.L262
	.byte	1,5,35,9
	.half	.L369-.L367
	.byte	3,1,1,5,43,9
	.half	.L368-.L369
	.byte	1,5,24,9
	.half	.L370-.L368
	.byte	3,1,1,5,36,9
	.half	.L576-.L370
	.byte	1,5,42,9
	.half	.L577-.L576
	.byte	1,5,32,9
	.half	.L578-.L577
	.byte	1,5,23,9
	.half	.L579-.L578
	.byte	3,1,1,5,46,9
	.half	.L580-.L579
	.byte	1,5,32,9
	.half	.L581-.L580
	.byte	1,5,37,9
	.half	.L44-.L581
	.byte	3,121,1,5,32,9
	.half	.L42-.L44
	.byte	1,5,34,9
	.half	.L371-.L42
	.byte	1,5,16,7,9
	.half	.L372-.L371
	.byte	3,12,1,5,26,9
	.half	.L373-.L372
	.byte	1,5,24,9
	.half	.L46-.L373
	.byte	3,2,1,5,13,9
	.half	.L582-.L46
	.byte	1,5,17,7,9
	.half	.L583-.L582
	.byte	3,2,1,4,2,5,5,9
	.half	.L265-.L583
	.byte	3,127,1,4,1,5,29,9
	.half	.L47-.L265
	.byte	3,125,1,5,26,9
	.half	.L45-.L47
	.byte	1,5,29,7,9
	.half	.L255-.L45
	.byte	3,8,1,5,1,9
	.half	.L374-.L255
	.byte	3,1,1,7,9
	.half	.L127-.L374
	.byte	0,1,1
.L566:
	.sdecl	'.debug_ranges',debug,cluster('IfxPort_setGroupPadDriver')
	.sect	'.debug_ranges'
.L126:
	.word	-1,.L70,0,.L127-.L70,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_setPinModeLvdsHigh')
	.sect	'.debug_info'
.L128:
	.word	408
	.half	3
	.word	.L129
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Port/Std/IfxPort.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L131,.L130
	.byte	2
	.word	.L79
	.byte	3
	.byte	'IfxPort_setPinModeLvdsHigh',0,1,170,2,6,1,1,1
	.word	.L74,.L269,.L73
	.byte	4
	.byte	'port',0,1,170,2,40
	.word	.L150,.L270
	.byte	4
	.byte	'pinIndex',0,1,170,2,52
	.word	.L148,.L271
	.byte	4
	.byte	'mode',0,1,170,2,75
	.word	.L164,.L272
	.byte	4
	.byte	'enablePortControlled',0,1,170,2,102
	.word	.L273,.L274
	.byte	5
	.word	.L74,.L269
	.byte	6
	.byte	'passwd',0,1,172,2,12
	.word	.L171,.L275
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_setPinModeLvdsHigh')
	.sect	'.debug_abbrev'
.L129:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxPort_setPinModeLvdsHigh')
	.sect	'.debug_line'
.L130:
	.word	.L585-.L584
.L584:
	.half	3
	.word	.L587-.L586
.L586:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Port/Std/IfxPort.c',0,0,0,0,0
.L587:
	.byte	5,6,7,0,5,2
	.word	.L74
	.byte	3,169,2,1,5,53,9
	.half	.L393-.L74
	.byte	3,2,1,5,19,9
	.half	.L391-.L393
	.byte	1,5,31,9
	.half	.L395-.L391
	.byte	3,2,1,5,16,9
	.half	.L396-.L395
	.byte	3,2,1,5,5,9
	.half	.L397-.L396
	.byte	1,5,9,7,9
	.half	.L398-.L397
	.byte	3,2,1,7,9
	.half	.L399-.L398
	.byte	1,5,30,9
	.half	.L50-.L399
	.byte	3,4,1,5,41,9
	.half	.L588-.L50
	.byte	1,5,30,9
	.half	.L589-.L588
	.byte	3,1,1,5,41,9
	.half	.L590-.L589
	.byte	1,5,9,9
	.half	.L51-.L590
	.byte	3,125,1,5,22,9
	.half	.L49-.L51
	.byte	3,8,1,5,33,9
	.half	.L591-.L49
	.byte	1,5,22,9
	.half	.L592-.L591
	.byte	3,1,1,5,33,9
	.half	.L593-.L592
	.byte	1,5,22,9
	.half	.L594-.L593
	.byte	3,1,1,5,33,9
	.half	.L595-.L594
	.byte	1,5,29,9
	.half	.L52-.L595
	.byte	3,3,1,5,1,9
	.half	.L401-.L52
	.byte	3,1,1,7,9
	.half	.L132-.L401
	.byte	0,1,1
.L585:
	.sdecl	'.debug_ranges',debug,cluster('IfxPort_setPinModeLvdsHigh')
	.sect	'.debug_ranges'
.L131:
	.word	-1,.L74,0,.L132-.L74,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_setPinModeLvdsMedium')
	.sect	'.debug_info'
.L133:
	.word	509
	.half	3
	.word	.L134
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Port/Std/IfxPort.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L136,.L135
	.byte	2
	.word	.L79
	.byte	3
	.byte	'IfxPort_setPinModeLvdsMedium',0,1,197,2,6,1,1,1
	.word	.L76,.L276,.L75
	.byte	4
	.byte	'port',0,1,197,2,42
	.word	.L150,.L277
	.byte	4
	.byte	'pinIndex',0,1,197,2,54
	.word	.L148,.L278
	.byte	4
	.byte	'lvdsPadDriver',0,1,197,2,82
	.word	.L186,.L279
	.byte	4
	.byte	'padSupply',0,1,197,2,115
	.word	.L280,.L281
	.byte	5
	.word	.L76,.L276
	.byte	6
	.byte	'pdrOffset',0,1,199,2,27
	.word	.L206,.L282
	.byte	6
	.byte	'shift',0,1,200,2,27
	.word	.L206,.L283
	.byte	6
	.byte	'lpcrOffset',0,1,201,2,27
	.word	.L206,.L284
	.byte	6
	.byte	'pdr',0,1,202,2,27
	.word	.L285,.L286
	.byte	6
	.byte	'lpcr',0,1,203,2,27
	.word	.L287,.L288
	.byte	6
	.byte	'passwd',0,1,204,2,27
	.word	.L171,.L289
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_setPinModeLvdsMedium')
	.sect	'.debug_abbrev'
.L134:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxPort_setPinModeLvdsMedium')
	.sect	'.debug_line'
.L135:
	.word	.L597-.L596
.L596:
	.half	3
	.word	.L599-.L598
.L598:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Port/Std/IfxPort.c',0,0,0,0,0
.L599:
	.byte	5,6,7,0,5,2
	.word	.L76
	.byte	3,196,2,1,5,52,9
	.half	.L600-.L76
	.byte	3,2,1,5,50,9
	.half	.L601-.L600
	.byte	1,5,53,9
	.half	.L404-.L601
	.byte	3,1,1,5,51,9
	.half	.L602-.L404
	.byte	1,5,56,9
	.half	.L603-.L602
	.byte	1,5,52,9
	.half	.L405-.L603
	.byte	3,1,1,5,50,9
	.half	.L604-.L405
	.byte	1,5,46,9
	.half	.L406-.L604
	.byte	3,1,1,9
	.half	.L407-.L406
	.byte	3,1,1,5,72,9
	.half	.L409-.L407
	.byte	3,1,1,5,31,9
	.half	.L412-.L409
	.byte	3,2,1,5,12,9
	.half	.L410-.L412
	.byte	3,2,1,5,49,9
	.half	.L408-.L410
	.byte	1,5,32,9
	.half	.L413-.L408
	.byte	1,5,13,9
	.half	.L605-.L413
	.byte	3,1,1,5,27,9
	.half	.L606-.L605
	.byte	1,5,32,9
	.half	.L414-.L606
	.byte	1,5,29,9
	.half	.L607-.L414
	.byte	3,2,1,5,1,9
	.half	.L417-.L607
	.byte	3,1,1,7,9
	.half	.L137-.L417
	.byte	0,1,1
.L597:
	.sdecl	'.debug_ranges',debug,cluster('IfxPort_setPinModeLvdsMedium')
	.sect	'.debug_ranges'
.L136:
	.word	-1,.L76,0,.L137-.L76,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_resetESR')
	.sect	'.debug_info'
.L138:
	.word	400
	.half	3
	.word	.L139
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Port/Std/IfxPort.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L141,.L140
	.byte	2
	.word	.L79
	.byte	3
	.byte	'IfxPort_resetESR',0,1,138,1,6,1,1,1
	.word	.L62,.L290,.L61
	.byte	4
	.byte	'port',0,1,138,1,30
	.word	.L150,.L291
	.byte	4
	.byte	'pinIndex',0,1,138,1,42
	.word	.L148,.L292
	.byte	5
	.word	.L62,.L290
	.byte	6
	.byte	'passwd',0,1,140,1,12
	.word	.L171,.L293
	.byte	7
	.word	.L173,.L294,.L295
	.byte	8
	.word	.L176,.L296
	.byte	8
	.word	.L178,.L297
	.byte	8
	.word	.L180,.L298
	.byte	9
	.word	.L182,.L294,.L295
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_resetESR')
	.sect	'.debug_abbrev'
.L139:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,5,0,49,16,2,6,0,0,9,11,0,49,16,17,1,18
	.byte	1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxPort_resetESR')
	.sect	'.debug_line'
.L140:
	.word	.L609-.L608
.L608:
	.half	3
	.word	.L611-.L610
.L610:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Port/Std/IfxPort.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0,0
.L611:
	.byte	5,6,7,0,5,2
	.word	.L62
	.byte	3,137,1,1,5,53,9
	.half	.L325-.L62
	.byte	3,2,1,5,19,9
	.half	.L322-.L325
	.byte	1,5,31,9
	.half	.L327-.L322
	.byte	3,2,1,5,5,9
	.half	.L328-.L327
	.byte	3,1,1,4,2,9
	.half	.L294-.L328
	.byte	3,128,1,1,4,1,5,29,9
	.half	.L295-.L294
	.byte	3,129,127,1,5,1,9
	.half	.L330-.L295
	.byte	3,1,1,7,9
	.half	.L142-.L330
	.byte	0,1,1
.L609:
	.sdecl	'.debug_ranges',debug,cluster('IfxPort_resetESR')
	.sect	'.debug_ranges'
.L141:
	.word	-1,.L62,0,.L142-.L62,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_setESR')
	.sect	'.debug_info'
.L143:
	.word	398
	.half	3
	.word	.L144
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Port/Std/IfxPort.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L146,.L145
	.byte	2
	.word	.L79
	.byte	3
	.byte	'IfxPort_setESR',0,1,148,1,6,1,1,1
	.word	.L64,.L299,.L63
	.byte	4
	.byte	'port',0,1,148,1,28
	.word	.L150,.L300
	.byte	4
	.byte	'pinIndex',0,1,148,1,40
	.word	.L148,.L301
	.byte	5
	.word	.L64,.L299
	.byte	6
	.byte	'passwd',0,1,150,1,12
	.word	.L171,.L302
	.byte	7
	.word	.L173,.L303,.L304
	.byte	8
	.word	.L176,.L305
	.byte	8
	.word	.L178,.L306
	.byte	8
	.word	.L180,.L307
	.byte	9
	.word	.L182,.L303,.L304
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_setESR')
	.sect	'.debug_abbrev'
.L144:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,5,0,49,16,2,6,0,0,9,11,0,49,16,17,1,18
	.byte	1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxPort_setESR')
	.sect	'.debug_line'
.L145:
	.word	.L613-.L612
.L612:
	.half	3
	.word	.L615-.L614
.L614:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Port/Std/IfxPort.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0,0
.L615:
	.byte	5,6,7,0,5,2
	.word	.L64
	.byte	3,147,1,1,5,53,9
	.half	.L334-.L64
	.byte	3,2,1,5,19,9
	.half	.L331-.L334
	.byte	1,5,31,9
	.half	.L336-.L331
	.byte	3,2,1,5,5,9
	.half	.L337-.L336
	.byte	3,1,1,4,2,9
	.half	.L303-.L337
	.byte	3,246,0,1,4,1,5,29,9
	.half	.L304-.L303
	.byte	3,139,127,1,5,1,9
	.half	.L339-.L304
	.byte	3,1,1,7,9
	.half	.L147-.L339
	.byte	0,1,1
.L613:
	.sdecl	'.debug_ranges',debug,cluster('IfxPort_setESR')
	.sect	'.debug_ranges'
.L146:
	.word	-1,.L64,0,.L147-.L64,0,0
	.sdecl	'.debug_loc',debug,cluster('IfxPort_disableEmergencyStop')
	.sect	'.debug_loc'
.L53:
	.word	-1,.L54,0,.L149-.L54
	.half	2
	.byte	138,0
	.word	0,0
.L152:
	.word	-1,.L54,0,.L308-.L54
	.half	1
	.byte	84
	.word	.L4-.L54,.L6-.L54
	.half	1
	.byte	84
	.word	0,0
.L151:
	.word	-1,.L54,0,.L308-.L54
	.half	1
	.byte	100
	.word	.L4-.L54,.L6-.L54
	.half	1
	.byte	100
	.word	0,0
.L154:
	.word	-1,.L54,.L310-.L54,.L308-.L54
	.half	1
	.byte	81
	.word	.L4-.L54,.L6-.L54
	.half	1
	.byte	81
	.word	0,0
.L155:
	.word	-1,.L54,.L309-.L54,.L308-.L54
	.half	1
	.byte	82
	.word	.L5-.L54,.L149-.L54
	.half	1
	.byte	82
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxPort_enableEmergencyStop')
	.sect	'.debug_loc'
.L55:
	.word	-1,.L56,0,.L156-.L56
	.half	2
	.byte	138,0
	.word	0,0
.L158:
	.word	-1,.L56,0,.L9-.L56
	.half	1
	.byte	84
	.word	.L312-.L56,.L156-.L56
	.half	1
	.byte	88
	.word	.L317-.L56,.L314-.L56
	.half	1
	.byte	84
	.word	0,0
.L157:
	.word	-1,.L56,0,.L9-.L56
	.half	1
	.byte	100
	.word	.L311-.L56,.L156-.L56
	.half	1
	.byte	108
	.word	.L316-.L56,.L314-.L56
	.half	1
	.byte	100
	.word	0,0
.L159:
	.word	-1,.L56,.L315-.L56,.L156-.L56
	.half	1
	.byte	89
	.word	0,0
.L160:
	.word	-1,.L56,.L313-.L56,.L314-.L56
	.half	1
	.byte	82
	.word	.L10-.L56,.L156-.L56
	.half	1
	.byte	82
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxPort_getAddress')
	.sect	'.debug_loc'
.L57:
	.word	-1,.L58,0,.L199-.L58
	.half	2
	.byte	138,0
	.word	0,0
.L203:
	.word	-1,.L58,.L319-.L58,.L199-.L58
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L202:
	.word	-1,.L58,.L318-.L58,.L199-.L58
	.half	1
	.byte	98
	.word	0,0
.L201:
	.word	-1,.L58,0,.L199-.L58
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxPort_getIndex')
	.sect	'.debug_loc'
.L59:
	.word	-1,.L60,0,.L204-.L60
	.half	2
	.byte	138,0
	.word	0,0
.L207:
	.word	-1,.L60,.L321-.L60,.L204-.L60
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L205:
	.word	-1,.L60,0,.L204-.L60
	.half	1
	.byte	100
	.word	0,0
.L208:
	.word	-1,.L60,.L320-.L60,.L204-.L60
	.half	1
	.byte	82
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxPort_resetESR')
	.sect	'.debug_loc'
.L61:
	.word	-1,.L62,0,.L290-.L62
	.half	2
	.byte	138,0
	.word	0,0
.L296:
	.word	0,0
.L298:
	.word	0,0
.L297:
	.word	0,0
.L293:
	.word	-1,.L62,.L322-.L62,.L326-.L62
	.half	1
	.byte	82
	.word	.L327-.L62,.L290-.L62
	.half	1
	.byte	88
	.word	.L326-.L62,.L328-.L62
	.half	1
	.byte	84
	.word	.L329-.L62,.L330-.L62
	.half	1
	.byte	84
	.word	0,0
.L292:
	.word	-1,.L62,0,.L322-.L62
	.half	1
	.byte	84
	.word	.L325-.L62,.L294-.L62
	.half	1
	.byte	95
	.word	0,0
.L291:
	.word	-1,.L62,0,.L322-.L62
	.half	1
	.byte	100
	.word	.L323-.L62,.L324-.L62
	.half	1
	.byte	111
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxPort_setESR')
	.sect	'.debug_loc'
.L63:
	.word	-1,.L64,0,.L299-.L64
	.half	2
	.byte	138,0
	.word	0,0
.L305:
	.word	0,0
.L307:
	.word	0,0
.L306:
	.word	0,0
.L302:
	.word	-1,.L64,.L331-.L64,.L335-.L64
	.half	1
	.byte	82
	.word	.L336-.L64,.L299-.L64
	.half	1
	.byte	88
	.word	.L335-.L64,.L337-.L64
	.half	1
	.byte	84
	.word	.L338-.L64,.L339-.L64
	.half	1
	.byte	84
	.word	0,0
.L301:
	.word	-1,.L64,0,.L331-.L64
	.half	1
	.byte	84
	.word	.L334-.L64,.L299-.L64
	.half	1
	.byte	95
	.word	0,0
.L300:
	.word	-1,.L64,0,.L331-.L64
	.half	1
	.byte	100
	.word	.L332-.L64,.L333-.L64
	.half	1
	.byte	111
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxPort_setGroupModeInput')
	.sect	'.debug_loc'
.L65:
	.word	-1,.L66,0,.L340-.L66
	.half	2
	.byte	138,0
	.word	.L340-.L66,.L209-.L66
	.half	2
	.byte	138,32
	.word	.L209-.L66,.L209-.L66
	.half	2
	.byte	138,0
	.word	0,0
.L225:
	.word	0,0
.L227:
	.word	0,0
.L215:
	.word	-1,.L66,.L344-.L66,.L26-.L66
	.half	1
	.byte	81
	.word	.L345-.L66,.L209-.L66
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L220:
	.word	-1,.L66,.L343-.L66,.L345-.L66
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L222:
	.word	-1,.L66,.L346-.L66,.L27-.L66
	.half	1
	.byte	84
	.word	0,0
.L218:
	.word	-1,.L66,0,.L209-.L66
	.half	2
	.byte	145,112
	.word	0,0
.L217:
	.word	-1,.L66,0,.L209-.L66
	.half	2
	.byte	145,96
	.word	0,0
.L212:
	.word	-1,.L66,0,.L26-.L66
	.half	1
	.byte	85
	.word	.L342-.L66,.L343-.L66
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L226:
	.word	0,0
.L214:
	.word	-1,.L66,0,.L209-.L66
	.half	1
	.byte	86
	.word	0,0
.L211:
	.word	-1,.L66,0,.L24-.L66
	.half	1
	.byte	84
	.word	.L341-.L66,.L209-.L66
	.half	1
	.byte	87
	.word	0,0
.L210:
	.word	-1,.L66,0,.L209-.L66
	.half	1
	.byte	100
	.word	0,0
.L223:
	.word	-1,.L66,.L347-.L66,.L27-.L66
	.half	1
	.byte	88
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxPort_setGroupModeOutput')
	.sect	'.debug_loc'
.L67:
	.word	-1,.L68,0,.L348-.L68
	.half	2
	.byte	138,0
	.word	.L348-.L68,.L228-.L68
	.half	2
	.byte	138,32
	.word	.L228-.L68,.L228-.L68
	.half	2
	.byte	138,0
	.word	0,0
.L245:
	.word	0,0
.L247:
	.word	0,0
.L236:
	.word	-1,.L68,.L352-.L68,.L35-.L68
	.half	1
	.byte	81
	.word	.L353-.L68,.L228-.L68
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L240:
	.word	-1,.L68,.L351-.L68,.L353-.L68
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L235:
	.word	-1,.L68,0,.L35-.L68
	.half	1
	.byte	87
	.word	0,0
.L242:
	.word	-1,.L68,.L354-.L68,.L355-.L68
	.half	1
	.byte	84
	.word	0,0
.L238:
	.word	-1,.L68,0,.L228-.L68
	.half	2
	.byte	145,112
	.word	0,0
.L237:
	.word	-1,.L68,0,.L228-.L68
	.half	2
	.byte	145,96
	.word	0,0
.L231:
	.word	-1,.L68,0,.L35-.L68
	.half	1
	.byte	85
	.word	.L350-.L68,.L351-.L68
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L246:
	.word	0,0
.L233:
	.word	-1,.L68,0,.L228-.L68
	.half	1
	.byte	86
	.word	0,0
.L230:
	.word	-1,.L68,0,.L33-.L68
	.half	1
	.byte	84
	.word	.L349-.L68,.L228-.L68
	.half	1
	.byte	88
	.word	0,0
.L229:
	.word	-1,.L68,0,.L228-.L68
	.half	1
	.byte	100
	.word	0,0
.L243:
	.word	-1,.L68,.L356-.L68,.L36-.L68
	.half	1
	.byte	87
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxPort_setGroupPadDriver')
	.sect	'.debug_loc'
.L69:
	.word	-1,.L70,0,.L357-.L70
	.half	2
	.byte	138,0
	.word	.L357-.L70,.L248-.L70
	.half	2
	.byte	138,16
	.word	.L248-.L70,.L248-.L70
	.half	2
	.byte	138,0
	.word	0,0
.L266:
	.word	0,0
.L268:
	.word	0,0
.L256:
	.word	-1,.L70,.L363-.L70,.L43-.L70
	.half	5
	.byte	144,32,157,32,0
	.word	.L260-.L70,.L364-.L70
	.half	1
	.byte	89
	.word	.L365-.L70,.L366-.L70
	.half	1
	.byte	89
	.word	.L367-.L70,.L368-.L70
	.half	1
	.byte	89
	.word	.L44-.L70,.L42-.L70
	.half	1
	.byte	89
	.word	.L371-.L70,.L372-.L70
	.half	1
	.byte	89
	.word	.L373-.L70,.L374-.L70
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L261:
	.word	-1,.L70,.L364-.L70,.L248-.L70
	.half	1
	.byte	88
	.word	0,0
.L263:
	.word	-1,.L70,.L369-.L70,.L44-.L70
	.half	1
	.byte	84
	.word	0,0
.L251:
	.word	-1,.L70,0,.L358-.L70
	.half	1
	.byte	85
	.word	.L260-.L70,.L364-.L70
	.half	1
	.byte	88
	.word	0,0
.L267:
	.word	0,0
.L252:
	.word	-1,.L70,0,.L358-.L70
	.half	1
	.byte	86
	.word	.L360-.L70,.L248-.L70
	.half	1
	.byte	90
	.word	0,0
.L253:
	.word	-1,.L70,.L358-.L70,.L361-.L70
	.half	1
	.byte	82
	.word	.L362-.L70,.L248-.L70
	.half	1
	.byte	91
	.word	.L361-.L70,.L254-.L70
	.half	1
	.byte	84
	.word	.L375-.L70,.L374-.L70
	.half	1
	.byte	84
	.word	0,0
.L259:
	.word	-1,.L70,0,.L248-.L70
	.half	2
	.byte	145,120
	.word	0,0
.L258:
	.word	-1,.L70,0,.L248-.L70
	.half	2
	.byte	145,112
	.word	0,0
.L250:
	.word	-1,.L70,0,.L358-.L70
	.half	1
	.byte	84
	.word	.L260-.L70,.L364-.L70
	.half	1
	.byte	89
	.word	.L365-.L70,.L366-.L70
	.half	1
	.byte	89
	.word	.L367-.L70,.L368-.L70
	.half	1
	.byte	89
	.word	.L44-.L70,.L42-.L70
	.half	1
	.byte	89
	.word	.L371-.L70,.L372-.L70
	.half	1
	.byte	89
	.word	0,0
.L249:
	.word	-1,.L70,0,.L358-.L70
	.half	1
	.byte	100
	.word	.L359-.L70,.L248-.L70
	.half	1
	.byte	108
	.word	0,0
.L264:
	.word	-1,.L70,.L370-.L70,.L44-.L70
	.half	1
	.byte	81
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxPort_setPinMode')
	.sect	'.debug_loc'
.L71:
	.word	-1,.L72,0,.L161-.L72
	.half	2
	.byte	138,0
	.word	0,0
.L177:
	.word	0,0
.L181:
	.word	0,0
.L167:
	.word	-1,.L72,.L379-.L72,.L161-.L72
	.half	1
	.byte	108
	.word	0,0
.L168:
	.word	-1,.L72,.L382-.L72,.L161-.L72
	.half	1
	.byte	88
	.word	0,0
.L179:
	.word	0,0
.L165:
	.word	-1,.L72,0,.L376-.L72
	.half	1
	.byte	85
	.word	.L390-.L72,.L174-.L72
	.half	1
	.byte	91
	.word	0,0
.L172:
	.word	-1,.L72,.L376-.L72,.L384-.L72
	.half	1
	.byte	82
	.word	.L385-.L72,.L48-.L72
	.half	1
	.byte	95
	.word	.L384-.L72,.L386-.L72
	.half	1
	.byte	84
	.word	.L389-.L72,.L48-.L72
	.half	1
	.byte	84
	.word	0,0
.L163:
	.word	-1,.L72,0,.L376-.L72
	.half	1
	.byte	84
	.word	.L380-.L72,.L381-.L72
	.half	1
	.byte	90
	.word	.L387-.L72,.L388-.L72
	.half	1
	.byte	90
	.word	0,0
.L162:
	.word	-1,.L72,0,.L376-.L72
	.half	1
	.byte	100
	.word	.L377-.L72,.L378-.L72
	.half	1
	.byte	111
	.word	0,0
.L169:
	.word	-1,.L72,.L383-.L72,.L161-.L72
	.half	1
	.byte	92
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxPort_setPinModeLvdsHigh')
	.sect	'.debug_loc'
.L73:
	.word	-1,.L74,0,.L269-.L74
	.half	2
	.byte	138,0
	.word	0,0
.L274:
	.word	-1,.L74,0,.L391-.L74
	.half	1
	.byte	86
	.word	.L393-.L74,.L269-.L74
	.half	1
	.byte	90
	.word	0,0
.L272:
	.word	-1,.L74,0,.L391-.L74
	.half	1
	.byte	85
	.word	.L397-.L74,.L398-.L74
	.half	1
	.byte	89
	.word	0,0
.L275:
	.word	-1,.L74,.L391-.L74,.L394-.L74
	.half	1
	.byte	82
	.word	.L395-.L74,.L269-.L74
	.half	1
	.byte	91
	.word	.L394-.L74,.L396-.L74
	.half	1
	.byte	84
	.word	.L400-.L74,.L401-.L74
	.half	1
	.byte	84
	.word	0,0
.L271:
	.word	-1,.L74,0,.L391-.L74
	.half	1
	.byte	84
	.word	.L398-.L74,.L399-.L74
	.half	1
	.byte	88
	.word	0,0
.L270:
	.word	-1,.L74,0,.L391-.L74
	.half	1
	.byte	100
	.word	.L392-.L74,.L269-.L74
	.half	1
	.byte	111
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxPort_setPinModeLvdsMedium')
	.sect	'.debug_loc'
.L75:
	.word	-1,.L76,0,.L402-.L76
	.half	2
	.byte	138,0
	.word	.L402-.L76,.L276-.L76
	.half	2
	.byte	138,8
	.word	.L276-.L76,.L276-.L76
	.half	2
	.byte	138,0
	.word	0,0
.L288:
	.word	-1,.L76,.L409-.L76,.L276-.L76
	.half	1
	.byte	108
	.word	0,0
.L284:
	.word	-1,.L76,.L406-.L76,.L276-.L76
	.half	1
	.byte	92
	.word	0,0
.L279:
	.word	-1,.L76,0,.L403-.L76
	.half	1
	.byte	85
	.word	.L408-.L76,.L413-.L76
	.half	1
	.byte	88
	.word	0,0
.L281:
	.word	-1,.L76,0,.L403-.L76
	.half	1
	.byte	86
	.word	.L414-.L76,.L415-.L76
	.half	1
	.byte	89
	.word	0,0
.L289:
	.word	-1,.L76,.L403-.L76,.L410-.L76
	.half	1
	.byte	82
	.word	.L411-.L76,.L410-.L76
	.half	1
	.byte	84
	.word	.L412-.L76,.L276-.L76
	.half	2
	.byte	145,120
	.word	.L416-.L76,.L417-.L76
	.half	1
	.byte	84
	.word	0,0
.L286:
	.word	-1,.L76,.L407-.L76,.L408-.L76
	.half	1
	.byte	111
	.word	0,0
.L282:
	.word	-1,.L76,.L404-.L76,.L276-.L76
	.half	1
	.byte	90
	.word	0,0
.L278:
	.word	-1,.L76,0,.L403-.L76
	.half	1
	.byte	84
	.word	0,0
.L277:
	.word	-1,.L76,0,.L403-.L76
	.half	1
	.byte	100
	.word	0,0
.L283:
	.word	-1,.L76,.L405-.L76,.L276-.L76
	.half	1
	.byte	94
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxPort_setPinPadDriver')
	.sect	'.debug_loc'
.L77:
	.word	-1,.L78,0,.L183-.L78
	.half	2
	.byte	138,0
	.word	0,0
.L196:
	.word	0,0
.L198:
	.word	0,0
.L197:
	.word	0,0
.L187:
	.word	-1,.L78,0,.L418-.L78
	.half	1
	.byte	85
	.word	.L430-.L78,.L195-.L78
	.half	1
	.byte	89
	.word	0,0
.L188:
	.word	-1,.L78,.L418-.L78,.L421-.L78
	.half	1
	.byte	82
	.word	.L422-.L78,.L183-.L78
	.half	1
	.byte	90
	.word	.L421-.L78,.L189-.L78
	.half	1
	.byte	84
	.word	.L429-.L78,.L427-.L78
	.half	1
	.byte	84
	.word	0,0
.L192:
	.word	-1,.L78,.L420-.L78,.L423-.L78
	.half	1
	.byte	111
	.word	0,0
.L193:
	.word	-1,.L78,.L426-.L78,.L427-.L78
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L185:
	.word	-1,.L78,0,.L418-.L78
	.half	1
	.byte	84
	.word	.L424-.L78,.L425-.L78
	.half	1
	.byte	88
	.word	0,0
.L184:
	.word	-1,.L78,0,.L418-.L78
	.half	1
	.byte	100
	.word	.L419-.L78,.L420-.L78
	.half	1
	.byte	111
	.word	0,0
.L194:
	.word	-1,.L78,.L428-.L78,.L429-.L78
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L616:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('IfxPort_disableEmergencyStop')
	.sect	'.debug_frame'
	.word	12
	.word	.L616,.L54,.L149-.L54
	.sdecl	'.debug_frame',debug,cluster('IfxPort_enableEmergencyStop')
	.sect	'.debug_frame'
	.word	12
	.word	.L616,.L56,.L156-.L56
	.sdecl	'.debug_frame',debug,cluster('IfxPort_getAddress')
	.sect	'.debug_frame'
	.word	24
	.word	.L616,.L58,.L199-.L58
	.byte	8,19,8,20,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxPort_getIndex')
	.sect	'.debug_frame'
	.word	24
	.word	.L616,.L60,.L204-.L60
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxPort_resetESR')
	.sect	'.debug_frame'
	.word	12
	.word	.L616,.L62,.L290-.L62
	.sdecl	'.debug_frame',debug,cluster('IfxPort_setESR')
	.sect	'.debug_frame'
	.word	12
	.word	.L616,.L64,.L299-.L64
	.sdecl	'.debug_frame',debug,cluster('IfxPort_setGroupModeInput')
	.sect	'.debug_frame'
	.word	44
	.word	.L616,.L66,.L209-.L66
	.byte	8,19,8,21,8,22,8,23,4
	.word	(.L340-.L66)/2
	.byte	19,32,22,26,3,19,138,32,4
	.word	(.L209-.L340)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxPort_setGroupModeOutput')
	.sect	'.debug_frame'
	.word	44
	.word	.L616,.L68,.L228-.L68
	.byte	8,19,8,21,8,22,8,23,4
	.word	(.L348-.L68)/2
	.byte	19,32,22,26,3,19,138,32,4
	.word	(.L228-.L348)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxPort_setGroupPadDriver')
	.sect	'.debug_frame'
	.word	36
	.word	.L616,.L70,.L248-.L70
	.byte	4
	.word	(.L357-.L70)/2
	.byte	19,16,22,26,3,19,138,16,4
	.word	(.L248-.L357)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxPort_setPinMode')
	.sect	'.debug_frame'
	.word	12
	.word	.L616,.L72,.L161-.L72
	.sdecl	'.debug_frame',debug,cluster('IfxPort_setPinModeLvdsHigh')
	.sect	'.debug_frame'
	.word	12
	.word	.L616,.L74,.L269-.L74
	.sdecl	'.debug_frame',debug,cluster('IfxPort_setPinModeLvdsMedium')
	.sect	'.debug_frame'
	.word	36
	.word	.L616,.L76,.L276-.L76
	.byte	4
	.word	(.L402-.L76)/2
	.byte	19,8,22,26,3,19,138,8,4
	.word	(.L276-.L402)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxPort_setPinPadDriver')
	.sect	'.debug_frame'
	.word	12
	.word	.L616,.L78,.L183-.L78
	; Module end
