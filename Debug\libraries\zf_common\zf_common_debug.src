	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc2608a --dep-file=zf_common_debug.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2\\\\xf_asr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/zf_common/zf_common_debug.src ../libraries/zf_common/zf_common_debug.c"
	.compiler_name		"ctc"
	;source	'../libraries/zf_common/zf_common_debug.c'

	
$TC16X
	
	.sdecl	'.text.zf_common_debug.debug_delay',code,cluster('debug_delay')
	.sect	'.text.zf_common_debug.debug_delay'
	.align	2
	
; Function debug_delay
.L53:
debug_delay:	.type	func
	sub.a	a10,#8
.L245:
	mov	d15,#0
.L427:
	st.w	[a10],d15
.L428:
	mov	d15,#0
.L429:
	st.w	[a10]4,d15
.L430:
	mov	d15,#0
.L431:
	st.w	[a10],d15
.L432:
	j	.L2
.L3:
	mov	d15,#0
.L433:
	st.w	[a10]4,d15
.L434:
	j	.L4
.L5:
	ld.w	d15,[a10]4
.L435:
	add	d15,#1
	st.w	[a10]4,d15
.L4:
	ld.w	d15,[a10]4
.L436:
	mov	d0,#8191
.L437:
	jge.u	d0,d15,.L5
.L438:
	ld.w	d15,[a10]
.L439:
	add	d15,#1
	st.w	[a10],d15
.L2:
	ld.w	d15,[a10]
.L440:
	mov	d0,#4095
.L441:
	jge.u	d0,d15,.L3
.L442:
	ret
.L211:
	
__debug_delay_function_end:
	.size	debug_delay,__debug_delay_function_end-debug_delay
.L150:
	; End of function
	
	.sdecl	'.text.zf_common_debug.debug_uart_str_output',code,cluster('debug_uart_str_output')
	.sect	'.text.zf_common_debug.debug_uart_str_output'
	.align	2
	
; Function debug_uart_str_output
.L55:
debug_uart_str_output:	.type	func
	mov	d4,#0
.L447:
	call	uart_write_string
.L246:
	ret
.L216:
	
__debug_uart_str_output_function_end:
	.size	debug_uart_str_output,__debug_uart_str_output_function_end-debug_uart_str_output
.L155:
	; End of function
	
	.sdecl	'.text.zf_common_debug.debug_output',code,cluster('debug_output')
	.sect	'.text.zf_common_debug.debug_output'
	.align	2
	
; Function debug_output
.L57:
debug_output:	.type	func
	lea	a10,[a10]-600
.L247:
	mov.aa	a12,a4
.L250:
	mov.aa	a13,a5
.L251:
	mov	d8,d4
.L252:
	mov.aa	a14,a6
.L253:
	mov	d0,#0
.L452:
	st.h	[a10]12,d0
.L453:
	mov	d15,#0
.L454:
	st.h	[a10]14,d15
.L455:
	mov	d15,#0
.L456:
	st.w	[a10]16,d15
.L457:
	mov	d15,#0
.L458:
	st.h	[a10]20,d15
.L459:
	mov	d15,#0
.L460:
	st.h	[a10]22,d15
.L461:
	mov.aa	a4,a13
.L248:
	call	strlen
.L249:
	st.w	[a10]16,d2
.L235:
	movh.a	a2,#@his(debug_output_info)
	lea	a2,[a2]@los(debug_output_info)
.L462:
	ld.hu	d15,[a2]0
.L463:
	jeq	d15,#0,.L6
.L464:
	movh.a	a15,#@his(debug_output_info)
	lea	a15,[a15]@los(debug_output_info)
.L465:
	ld.a	a15,[a15]16
.L466:
	calli	a15
.L6:
	movh.a	a15,#@his(zf_debug_init_flag)
	lea	a15,[a15]@los(zf_debug_init_flag)
	ld.bu	d0,[a15]
.L467:
	jeq	d0,#0,.L7
.L468:
	movh.a	a15,#@his(debug_output_info)
	lea	a15,[a15]@los(debug_output_info)
.L469:
	ld.hu	d15,[a15]0
.L470:
	jeq	d15,#0,.L8
.L471:
	ld.h	d0,[a10]22
	ld.h	d15,[a10]22
.L472:
	add	d15,#1
	st.h	[a10]22,d15
.L473:
	movh.a	a15,#@his(debug_output_info)
	lea	a15,[a15]@los(debug_output_info)
.L474:
	ld.a	a15,[a15]12
.L475:
	mov	d4,#0
.L476:
	extr.u	d5,d0,#0,#16
.L477:
	mov.aa	a4,a12
.L254:
	calli	a15
.L255:
	mov.aa	a4,a13
.L256:
	call	strlen
.L257:
	st.w	[a10]16,d2
.L478:
	movh.a	a15,#@his(debug_output_info)
	lea	a15,[a15]@los(debug_output_info)
.L479:
	ld.hu	d0,[a15]2
.L480:
	movh.a	a15,#@his(debug_output_info)
	lea	a15,[a15]@los(debug_output_info)
.L481:
	ld.bu	d15,[a15]6
.L482:
	div	e0,d0,d15
.L483:
	st.h	[a10]20,d0
.L484:
	j	.L9
.L10:
.L9:
	ld.b	d15,[a13]0
.L485:
	add.a	a13,#1
.L486:
	jne	d15,#0,.L10
.L487:
	mov	d15,#0
.L488:
	st.h	[a10]14,d15
.L489:
	j	.L11
.L12:
	add.a	a13,#-1
.L490:
	ld.b	d0,[a13]0
.L491:
	mov	d15,#47
.L492:
	jeq	d15,d0,.L13
.L493:
	ld.b	d0,[a13]0
.L494:
	mov	d15,#92
.L495:
	jne	d15,d0,.L14
.L13:
	ld.hu	d15,[a10]14
.L496:
	add	d15,#1
	st.h	[a10]14,d15
.L14:
	ld.w	d15,[a10]16
.L497:
	add	d15,#-1
	st.w	[a10]16,d15
.L11:
	ld.hu	d15,[a10]14
.L498:
	jge.u	d15,#2,.L15
.L499:
	ld.w	d15,[a10]16
.L500:
	jge	d15,#0,.L12
.L15:
	ld.w	d15,[a10]16
.L501:
	jlt	d15,#0,.L16
.L502:
	add.a	a13,#1
.L503:
	st.a	[a10],a13
.L504:
	lea	a4,[a10]24
.L505:
	movh.a	a5,#@his(.1.str)
	lea	a5,[a5]@los(.1.str)
	call	sprintf
.L506:
	j	.L17
.L16:
	ld.hu	d15,[a10]14
.L507:
	jne	d15,#0,.L18
.L508:
	st.a	[a10],a13
.L509:
	lea	a4,[a10]24
.L510:
	movh.a	a5,#@his(.2.str)
	lea	a5,[a5]@los(.2.str)
	call	sprintf
.L511:
	j	.L19
.L18:
	st.a	[a10],a13
.L512:
	lea	a4,[a10]24
.L513:
	movh.a	a5,#@his(.1.str)
	lea	a5,[a5]@los(.1.str)
	call	sprintf
.L19:
.L17:
	mov	d15,#0
.L514:
	st.h	[a10]12,d15
.L515:
	j	.L20
.L21:
	mov	d15,#0
.L516:
	st.h	[a10]14,d15
.L517:
	j	.L22
.L23:
	lea	a4,[a10]24
	call	strlen
.L518:
	ld.hu	d0,[a10]12
.L519:
	ld.h	d15,[a10]20
.L520:
	ld.hu	d1,[a10]14
.L521:
	madd	d15,d1,d0,d15
.L522:
	jge.u	d2,d15,.L24
.L523:
	j	.L25
.L24:
	ld.hu	d15,[a10]14
.L524:
	addsc.a	a15,a10,d15,#0
	lea	a15,[a15]280
.L525:
	ld.hu	d0,[a10]12
.L526:
	ld.h	d15,[a10]20
.L527:
	ld.hu	d1,[a10]14
.L528:
	madd	d15,d1,d0,d15
.L529:
	addsc.a	a2,a10,d15,#0
	ld.b	d15,[a2]24
.L530:
	st.b	[a15],d15
.L531:
	ld.hu	d15,[a10]14
.L532:
	add	d15,#1
	st.h	[a10]14,d15
.L22:
	ld.hu	d0,[a10]14
.L533:
	ld.h	d15,[a10]20
.L534:
	jlt	d0,d15,.L23
.L25:
	ld.hu	d15,[a10]14
.L535:
	addsc.a	a15,a10,d15,#0
	lea	a15,[a15]280
.L536:
	mov	d15,#0
.L537:
	st.b	[a15],d15
.L538:
	movh.a	a15,#@his(debug_output_info)
	lea	a15,[a15]@los(debug_output_info)
.L539:
	ld.bu	d0,[a15]7
.L540:
	ld.h	d15,[a10]22
.L541:
	mul	d0,d15
.L542:
	ld.h	d15,[a10]22
.L543:
	add	d15,#1
	st.h	[a10]22,d15
.L544:
	movh.a	a15,#@his(debug_output_info)
	lea	a15,[a15]@los(debug_output_info)
.L545:
	ld.a	a15,[a15]12
.L546:
	mov	d4,#0
.L547:
	extr.u	d5,d0,#0,#16
.L548:
	lea	a4,[a10]280
	calli	a15
.L549:
	ld.hu	d15,[a10]12
.L550:
	add	d15,#1
	st.h	[a10]12,d15
.L20:
	ld.hu	d9,[a10]12
.L551:
	lea	a4,[a10]24
	call	strlen
.L552:
	ld.h	d15,[a10]20
.L553:
	div.u	e0,d2,d15
.L554:
	add	d15,d0,#1
.L555:
	jlt.u	d9,d15,.L21
.L556:
	st.w	[a10],d8
.L557:
	lea	a4,[a10]24
.L558:
	movh.a	a5,#@his(.3.str)
	lea	a5,[a5]@los(.3.str)
	call	sprintf
.L559:
	movh.a	a15,#@his(debug_output_info)
	lea	a15,[a15]@los(debug_output_info)
.L560:
	ld.bu	d15,[a15]7
.L561:
	ld.h	d0,[a10]22
.L562:
	mul	d0,d15
.L563:
	ld.h	d15,[a10]22
.L564:
	add	d15,#1
	st.h	[a10]22,d15
.L565:
	movh.a	a15,#@his(debug_output_info)
	lea	a15,[a15]@los(debug_output_info)
.L566:
	ld.a	a15,[a15]12
.L567:
	mov	d4,#0
.L568:
	extr.u	d5,d0,#0,#16
.L569:
	lea	a4,[a10]24
	calli	a15
.L570:
	mov.a	a15,#0
.L571:
	jeq.a	a15,a14,.L26
.L572:
	mov	d15,#0
.L573:
	st.h	[a10]12,d15
.L574:
	j	.L27
.L28:
	mov	d15,#0
.L575:
	st.h	[a10]14,d15
.L576:
	j	.L29
.L30:
	mov.aa	a4,a14
.L258:
	call	strlen
.L259:
	ld.hu	d0,[a10]12
.L577:
	ld.h	d15,[a10]20
.L578:
	ld.hu	d1,[a10]14
.L579:
	madd	d15,d1,d0,d15
.L580:
	jge.u	d2,d15,.L31
.L581:
	j	.L32
.L31:
	ld.hu	d15,[a10]14
.L582:
	addsc.a	a15,a10,d15,#0
	lea	a15,[a15]280
.L583:
	ld.hu	d0,[a10]12
.L584:
	ld.h	d15,[a10]20
.L585:
	ld.hu	d1,[a10]14
.L586:
	madd	d15,d1,d0,d15
.L587:
	addsc.a	a2,a14,d15,#0
	ld.b	d15,[a2]0
.L588:
	st.b	[a15],d15
.L589:
	ld.hu	d15,[a10]14
.L590:
	add	d15,#1
	st.h	[a10]14,d15
.L29:
	ld.hu	d0,[a10]14
.L591:
	ld.h	d15,[a10]20
.L592:
	jlt	d0,d15,.L30
.L32:
	ld.hu	d15,[a10]14
.L593:
	addsc.a	a15,a10,d15,#0
	lea	a15,[a15]280
.L594:
	mov	d15,#0
.L595:
	st.b	[a15],d15
.L596:
	movh.a	a15,#@his(debug_output_info)
	lea	a15,[a15]@los(debug_output_info)
.L597:
	ld.bu	d15,[a15]7
.L598:
	ld.h	d0,[a10]22
.L599:
	mul	d0,d15
.L600:
	ld.h	d15,[a10]22
.L601:
	add	d15,#1
	st.h	[a10]22,d15
.L602:
	movh.a	a15,#@his(debug_output_info)
	lea	a15,[a15]@los(debug_output_info)
.L603:
	ld.a	a15,[a15]12
.L604:
	mov	d4,#0
.L605:
	extr.u	d5,d0,#0,#16
.L606:
	lea	a4,[a10]280
	calli	a15
.L607:
	ld.hu	d15,[a10]12
.L608:
	add	d15,#1
	st.h	[a10]12,d15
.L27:
	ld.hu	d8,[a10]12
.L609:
	mov.aa	a4,a14
.L260:
	call	strlen
.L261:
	ld.h	d15,[a10]20
.L610:
	div.u	e0,d2,d15
.L611:
	add	d15,d0,#1
.L612:
	jlt.u	d8,d15,.L28
.L26:
	j	.L33
.L8:
	lea	a4,[a10]344
.L613:
	mov	d4,#0
.L614:
	mov	d5,#256
	call	memset
.L615:
	movh.a	a15,#@his(debug_output_info)
	lea	a15,[a15]@los(debug_output_info)
.L616:
	ld.a	a15,[a15]8
.L617:
	mov.aa	a4,a12
.L262:
	calli	a15
.L263:
	mov.a	a15,#0
.L618:
	jeq.a	a15,a14,.L34
.L619:
	st.a	[a10],a13
.L620:
	st.w	[a10]4,d8
.L621:
	st.a	[a10]8,a14
.L622:
	lea	a4,[a10]344
.L623:
	movh.a	a5,#@his(.4.str)
	lea	a5,[a5]@los(.4.str)
	call	sprintf
.L624:
	j	.L35
.L34:
	st.a	[a10],a13
.L625:
	st.w	[a10]4,d8
.L626:
	lea	a4,[a10]344
.L627:
	movh.a	a5,#@his(.5.str)
	lea	a5,[a5]@los(.5.str)
	call	sprintf
.L35:
	movh.a	a15,#@his(debug_output_info)
	lea	a15,[a15]@los(debug_output_info)
.L628:
	ld.a	a15,[a15]8
.L629:
	lea	a4,[a10]344
	calli	a15
.L33:
.L7:
	ret
.L219:
	
__debug_output_function_end:
	.size	debug_output,__debug_output_function_end-debug_output
.L160:
	; End of function
	
	.sdecl	'.text.zf_common_debug.debug_send_buffer',code,cluster('debug_send_buffer')
	.sect	'.text.zf_common_debug.debug_send_buffer'
	.align	2
	
	.global	debug_send_buffer
; Function debug_send_buffer
.L59:
debug_send_buffer:	.type	func
	mov	d5,d4
.L266:
	mov	d4,#0
.L265:
	call	uart_write_buffer
.L264:
	mov	d2,#0
.L321:
	j	.L36
.L36:
	ret
.L186:
	
__debug_send_buffer_function_end:
	.size	debug_send_buffer,__debug_send_buffer_function_end-debug_send_buffer
.L110:
	; End of function
	
	.sdecl	'.text.zf_common_debug.debug_read_ring_buffer',code,cluster('debug_read_ring_buffer')
	.sect	'.text.zf_common_debug.debug_read_ring_buffer'
	.align	2
	
	.global	debug_read_ring_buffer
; Function debug_read_ring_buffer
.L61:
debug_read_ring_buffer:	.type	func
	sub.a	a10,#8
.L267:
	mov.aa	a5,a4
.L270:
	st.w	[a10],d4
.L313:
	movh.a	a4,#@his(debug_uart_fifo)
.L268:
	lea	a4,[a4]@los(debug_uart_fifo)
.L314:
	lea	a6,[a10]0
.L315:
	mov	d4,#0
.L269:
	call	fifo_read_buffer
.L271:
	ld.w	d2,[a10]
.L316:
	j	.L37
.L37:
	ret
.L182:
	
__debug_read_ring_buffer_function_end:
	.size	debug_read_ring_buffer,__debug_read_ring_buffer_function_end-debug_read_ring_buffer
.L105:
	; End of function
	
	.sdecl	'.text.zf_common_debug.debug_interrupr_handler',code,cluster('debug_interrupr_handler')
	.sect	'.text.zf_common_debug.debug_interrupr_handler'
	.align	2
	
	.global	debug_interrupr_handler
; Function debug_interrupr_handler
.L63:
debug_interrupr_handler:	.type	func
	movh.a	a15,#@his(zf_debug_init_flag)
	lea	a15,[a15]@los(zf_debug_init_flag)
	ld.bu	d15,[a15]
.L303:
	jeq	d15,#0,.L38
.L304:
	mov	d4,#0
.L305:
	movh.a	a4,#@his(debug_uart_data)
	lea	a4,[a4]@los(debug_uart_data)
	call	uart_query_byte
.L306:
	movh.a	a4,#@his(debug_uart_fifo)
	lea	a4,[a4]@los(debug_uart_fifo)
.L307:
	movh.a	a5,#@his(debug_uart_data)
	lea	a5,[a5]@los(debug_uart_data)
.L308:
	mov	d4,#1
	call	fifo_write_buffer
.L38:
	ret
.L180:
	
__debug_interrupr_handler_function_end:
	.size	debug_interrupr_handler,__debug_interrupr_handler_function_end-debug_interrupr_handler
.L100:
	; End of function
	
	.sdecl	'.text.zf_common_debug.fputc',code,cluster('fputc')
	.sect	'.text.zf_common_debug.fputc'
	.align	2
	
	.global	fputc
; Function fputc
.L65:
fputc:	.type	func
	mov	d15,d4
.L274:
	mov	d4,#0
.L272:
	extr.u	d5,d15,#0,#8
	call	uart_write_byte
.L273:
	mov	d2,d15
.L275:
	j	.L39
.L39:
	ret
.L177:
	
__fputc_function_end:
	.size	fputc,__fputc_function_end-fputc
.L95:
	; End of function
	
	.sdecl	'.text.zf_common_debug.fgetc',code,cluster('fgetc')
	.sect	'.text.zf_common_debug.fgetc'
	.align	2
	
	.global	fgetc
; Function fgetc
.L67:
fgetc:	.type	func
	mov	d4,#0
	call	uart_read_byte
.L276:
	j	.L40
.L40:
	ret
.L174:
	
__fgetc_function_end:
	.size	fgetc,__fgetc_function_end-fgetc
.L90:
	; End of function
	
	.sdecl	'.text.zf_common_debug.debug_assert_enable',code,cluster('debug_assert_enable')
	.sect	'.text.zf_common_debug.debug_assert_enable'
	.align	2
	
	.global	debug_assert_enable
; Function debug_assert_enable
.L69:
debug_assert_enable:	.type	func
	movh.a	a15,#@his(zf_debug_assert_enable)
	lea	a15,[a15]@los(zf_debug_assert_enable)
.L326:
	mov	d15,#1
.L327:
	st.b	[a15],d15
.L328:
	ret
.L190:
	
__debug_assert_enable_function_end:
	.size	debug_assert_enable,__debug_assert_enable_function_end-debug_assert_enable
.L115:
	; End of function
	
	.sdecl	'.text.zf_common_debug.debug_assert_disable',code,cluster('debug_assert_disable')
	.sect	'.text.zf_common_debug.debug_assert_disable'
	.align	2
	
	.global	debug_assert_disable
; Function debug_assert_disable
.L71:
debug_assert_disable:	.type	func
	movh.a	a15,#@his(zf_debug_assert_enable)
	lea	a15,[a15]@los(zf_debug_assert_enable)
.L333:
	mov	d15,#0
.L334:
	st.b	[a15],d15
.L335:
	ret
.L191:
	
__debug_assert_disable_function_end:
	.size	debug_assert_disable,__debug_assert_disable_function_end-debug_assert_disable
.L120:
	; End of function
	
	.sdecl	'.text.zf_common_debug.debug_assert_handler',code,cluster('debug_assert_handler')
	.sect	'.text.zf_common_debug.debug_assert_handler'
	.align	2
	
	.global	debug_assert_handler
; Function debug_assert_handler
.L73:
debug_assert_handler:	.type	func
	mov.aa	a15,a4
.L278:
	mov	d8,d5
.L279:
	jne	d4,#0,.L41
.L340:
	movh.a	a2,#@his(zf_debug_assert_enable)
	lea	a2,[a2]@los(zf_debug_assert_enable)
	ld.bu	d15,[a2]
	jne	d15,#0,.L42
.L41:
	j	.L43
.L42:
	movh.a	a2,#@his(_999001_assert_nest_index)
	lea	a2,[a2]@los(_999001_assert_nest_index)
	ld.bu	d15,[a2]
.L341:
	jeq	d15,#0,.L44
.L342:
	j	.L45
.L46:
.L45:
	j	.L46
.L44:
	movh.a	a2,#@his(_999001_assert_nest_index)
	lea	a2,[a2]@los(_999001_assert_nest_index)
	mov	d15,#0
.L343:
	add	d15,#1
	st.b	[a2],d15
.L344:
	call	assert_interrupt_config
.L277:
	j	.L47
.L48:
	movh.a	a4,#@his(.6.str)
	lea	a4,[a4]@los(.6.str)
.L345:
	mov.a	a6,#0
	mov.aa	a5,a15
.L280:
	mov	d4,d8
.L282:
	call	debug_output
.L281:
	call	debug_delay
.L47:
	j	.L48
.L43:
	ret
.L192:
	
__debug_assert_handler_function_end:
	.size	debug_assert_handler,__debug_assert_handler_function_end-debug_assert_handler
.L125:
	; End of function
	
	.sdecl	'.text.zf_common_debug.debug_log_handler',code,cluster('debug_log_handler')
	.sect	'.text.zf_common_debug.debug_log_handler'
	.align	2
	
	.global	debug_log_handler
; Function debug_log_handler
.L75:
debug_log_handler:	.type	func
	mov.aa	a6,a4
.L285:
	jeq	d4,#0,.L49
.L350:
	j	.L50
.L49:
	movh.a	a15,#@his(zf_debug_init_flag)
	lea	a15,[a15]@los(zf_debug_init_flag)
	ld.bu	d15,[a15]
.L351:
	jeq	d15,#0,.L51
.L352:
	movh.a	a4,#@his(.7.str)
.L284:
	lea	a4,[a4]@los(.7.str)
.L353:
	mov	d4,d5
.L283:
	call	debug_output
.L51:
.L50:
	ret
.L198:
	
__debug_log_handler_function_end:
	.size	debug_log_handler,__debug_log_handler_function_end-debug_log_handler
.L130:
	; End of function
	
	.sdecl	'.text.zf_common_debug.debug_output_struct_init',code,cluster('debug_output_struct_init')
	.sect	'.text.zf_common_debug.debug_output_struct_init'
	.align	2
	
	.global	debug_output_struct_init
; Function debug_output_struct_init
.L77:
debug_output_struct_init:	.type	func
	mov	d15,#0
.L358:
	st.h	[a4],d15
.L359:
	mov.u	d15,#65535
.L360:
	st.h	[a4]2,d15
.L361:
	mov.u	d15,#65535
.L362:
	st.h	[a4]4,d15
.L363:
	mov	d15,#255
.L364:
	st.b	[a4]6,d15
.L365:
	mov	d15,#255
.L366:
	st.b	[a4]7,d15
.L367:
	mov.a	a15,#0
.L368:
	st.a	[a4]8,a15
.L369:
	mov.a	a15,#0
.L370:
	st.a	[a4]12,a15
.L371:
	mov.a	a15,#0
.L372:
	st.a	[a4]16,a15
.L373:
	ret
.L203:
	
__debug_output_struct_init_function_end:
	.size	debug_output_struct_init,__debug_output_struct_init_function_end-debug_output_struct_init
.L135:
	; End of function
	
	.sdecl	'.text.zf_common_debug.debug_output_init',code,cluster('debug_output_init')
	.sect	'.text.zf_common_debug.debug_output_init'
	.align	2
	
	.global	debug_output_init
; Function debug_output_init
.L79:
debug_output_init:	.type	func
	movh.a	a15,#@his(debug_output_info)
	lea	a15,[a15]@los(debug_output_info)
.L378:
	ld.hu	d15,[a4]0
.L379:
	st.h	[a15],d15
.L380:
	movh.a	a15,#@his(debug_output_info)
	lea	a15,[a15]@los(debug_output_info)
.L381:
	ld.hu	d15,[a4]2
.L382:
	st.h	[a15]2,d15
.L383:
	movh.a	a15,#@his(debug_output_info)
	lea	a15,[a15]@los(debug_output_info)
.L384:
	ld.hu	d15,[a4]4
.L385:
	st.h	[a15]4,d15
.L386:
	movh.a	a15,#@his(debug_output_info)
	lea	a15,[a15]@los(debug_output_info)
.L387:
	ld.bu	d15,[a4]6
.L388:
	st.b	[a15]6,d15
.L389:
	movh.a	a15,#@his(debug_output_info)
	lea	a15,[a15]@los(debug_output_info)
.L390:
	ld.bu	d15,[a4]7
.L391:
	st.b	[a15]7,d15
.L392:
	movh.a	a15,#@his(debug_output_info)
	lea	a15,[a15]@los(debug_output_info)
.L393:
	ld.a	a2,[a4]8
.L394:
	st.a	[a15]8,a2
.L395:
	movh.a	a15,#@his(debug_output_info)
	lea	a15,[a15]@los(debug_output_info)
.L396:
	ld.a	a2,[a4]12
.L397:
	st.a	[a15]12,a2
.L398:
	movh.a	a2,#@his(debug_output_info)
	lea	a2,[a2]@los(debug_output_info)
.L399:
	ld.a	a15,[a4]16
.L400:
	st.a	[a2]16,a15
.L401:
	movh.a	a15,#@his(zf_debug_init_flag)
	lea	a15,[a15]@los(zf_debug_init_flag)
.L402:
	mov	d15,#1
.L403:
	st.b	[a15],d15
.L404:
	ret
.L206:
	
__debug_output_init_function_end:
	.size	debug_output_init,__debug_output_init_function_end-debug_output_init
.L140:
	; End of function
	
	.sdecl	'.text.zf_common_debug.debug_init',code,cluster('debug_init')
	.sect	'.text.zf_common_debug.debug_init'
	.align	2
	
	.global	debug_init
; Function debug_init
.L81:
debug_init:	.type	func
	sub.a	a10,#24
.L286:
	lea	a4,[a10]0
	call	debug_output_struct_init
.L409:
	movh.a	a15,#@his(debug_uart_str_output)
	lea	a15,[a15]@los(debug_uart_str_output)
.L410:
	st.a	[a10]8,a15
.L411:
	lea	a4,[a10]0
	call	debug_output_init
.L412:
	mov	d4,#0
.L413:
	mov.u	d5,#49664
	addih	d5,d5,#1
.L414:
	mov	d6,#0
.L415:
	mov	d7,#0
	call	uart_init
.L416:
	movh.a	a4,#@his(debug_uart_fifo)
	lea	a4,[a4]@los(debug_uart_fifo)
.L417:
	mov	d4,#0
.L418:
	movh.a	a5,#@his(debug_uart_buffer)
	lea	a5,[a5]@los(debug_uart_buffer)
.L419:
	mov	d5,#64
	call	fifo_init
.L420:
	mov	d4,#0
.L421:
	mov	d5,#1
	call	uart_rx_interrupt
.L422:
	ret
.L208:
	
__debug_init_function_end:
	.size	debug_init,__debug_init_function_end-debug_init
.L145:
	; End of function
	
	.sdecl	'.bss.zf_common_debug.debug_output_info',data,cluster('debug_output_info')
	.sect	'.bss.zf_common_debug.debug_output_info'
	.align	4
debug_output_info:	.type	object
	.size	debug_output_info,20
	.space	20
	.sdecl	'.data.zf_common_debug.zf_debug_init_flag',data,cluster('zf_debug_init_flag')
	.sect	'.data.zf_common_debug.zf_debug_init_flag'
zf_debug_init_flag:	.type	object
	.size	zf_debug_init_flag,1
	.space	1
	.sdecl	'.data.zf_common_debug.zf_debug_assert_enable',data,cluster('zf_debug_assert_enable')
	.sect	'.data.zf_common_debug.zf_debug_assert_enable'
zf_debug_assert_enable:	.type	object
	.size	zf_debug_assert_enable,1
	.byte	1
	.sdecl	'.bss.zf_common_debug.debug_uart_buffer',data,cluster('debug_uart_buffer')
	.sect	'.bss.zf_common_debug.debug_uart_buffer'
	.global	debug_uart_buffer
debug_uart_buffer:	.type	object
	.size	debug_uart_buffer,64
	.space	64
	.sdecl	'.bss.zf_common_debug.debug_uart_data',data,cluster('debug_uart_data')
	.sect	'.bss.zf_common_debug.debug_uart_data'
	.global	debug_uart_data
debug_uart_data:	.type	object
	.size	debug_uart_data,1
	.space	1
	.sdecl	'.bss.zf_common_debug.debug_uart_fifo',data,cluster('debug_uart_fifo')
	.sect	'.bss.zf_common_debug.debug_uart_fifo'
	.global	debug_uart_fifo
	.align	4
debug_uart_fifo:	.type	object
	.size	debug_uart_fifo,24
	.space	24
	.sdecl	'.rodata.zf_common_debug..1.str',data,rom
	.sect	'.rodata.zf_common_debug..1.str'
.1.str:	.type	object
	.size	.1.str,9
	.byte	102,105,108,101,58,32,37,115
	.space	1
	.sdecl	'.rodata.zf_common_debug..2.str',data,rom
	.sect	'.rodata.zf_common_debug..2.str'
.2.str:	.type	object
	.size	.2.str,13
	.byte	102,105,108,101,58,32,109,100
	.byte	107,47,37,115
	.space	1
	.sdecl	'.rodata.zf_common_debug..3.str',data,rom
	.sect	'.rodata.zf_common_debug..3.str'
.3.str:	.type	object
	.size	.3.str,9
	.byte	108,105,110,101,58,32,37,100
	.space	1
	.sdecl	'.rodata.zf_common_debug..4.str',data,rom
	.sect	'.rodata.zf_common_debug..4.str'
.4.str:	.type	object
	.size	.4.str,25
	.byte	13,10,102,105,108,101,32,37
	.byte	115,32,108,105,110,101,32,37
	.byte	100,58,32,37,115,46,13,10
	.space	1
	.sdecl	'.rodata.zf_common_debug..5.str',data,rom
	.sect	'.rodata.zf_common_debug..5.str'
.5.str:	.type	object
	.size	.5.str,21
	.byte	13,10,102,105,108,101,32,37
	.byte	115,32,108,105,110,101,32,37
	.byte	100,46,13,10
	.space	1
	.sdecl	'.data.zf_common_debug._999001_assert_nest_index',data,cluster('_999001_assert_nest_index')
	.sect	'.data.zf_common_debug._999001_assert_nest_index'
_999001_assert_nest_index:	.type	object
	.size	_999001_assert_nest_index,1
	.space	1
	.sdecl	'.rodata.zf_common_debug..6.str',data,rom
	.sect	'.rodata.zf_common_debug..6.str'
.6.str:	.type	object
	.size	.6.str,13
	.byte	65,115,115,101,114,116,32,101
	.byte	114,114,111,114
	.space	1
	.sdecl	'.rodata.zf_common_debug..7.str',data,rom
	.sect	'.rodata.zf_common_debug..7.str'
.7.str:	.type	object
	.size	.7.str,12
	.byte	76,111,103,32,109,101,115,115
	.byte	97,103,101
	.space	1
	.calls	'__INDIRECT__','debug_uart_str_output'
	.calls	'debug_uart_str_output','uart_write_string'
	.calls	'debug_output','strlen'
	.calls	'debug_output','__INDIRECT__'
	.calls	'debug_output','sprintf'
	.calls	'debug_output','memset'
	.calls	'debug_send_buffer','uart_write_buffer'
	.calls	'debug_read_ring_buffer','fifo_read_buffer'
	.calls	'debug_interrupr_handler','uart_query_byte'
	.calls	'debug_interrupr_handler','fifo_write_buffer'
	.calls	'fputc','uart_write_byte'
	.calls	'fgetc','uart_read_byte'
	.calls	'debug_assert_handler','assert_interrupt_config'
	.calls	'debug_assert_handler','debug_output'
	.calls	'debug_assert_handler','debug_delay'
	.calls	'debug_log_handler','debug_output'
	.calls	'debug_init','debug_output_struct_init'
	.calls	'debug_init','debug_output_init'
	.calls	'debug_init','uart_init'
	.calls	'debug_init','fifo_init'
	.calls	'debug_init','uart_rx_interrupt'
	.calls	'debug_delay','',8
	.calls	'debug_uart_str_output','',0
	.calls	'debug_output','',600
	.calls	'debug_send_buffer','',0
	.calls	'debug_read_ring_buffer','',8
	.calls	'debug_interrupr_handler','',0
	.calls	'fputc','',0
	.calls	'fgetc','',0
	.calls	'debug_assert_enable','',0
	.calls	'debug_assert_disable','',0
	.calls	'debug_assert_handler','',0
	.calls	'debug_log_handler','',0
	.calls	'debug_output_struct_init','',0
	.calls	'debug_output_init','',0
	.extern	sprintf
	.extern	strlen
	.extern	memset
	.extern	assert_interrupt_config
	.extern	fifo_write_buffer
	.extern	fifo_read_buffer
	.extern	fifo_init
	.extern	uart_write_byte
	.extern	uart_write_buffer
	.extern	uart_write_string
	.extern	uart_read_byte
	.extern	uart_query_byte
	.extern	uart_rx_interrupt
	.extern	uart_init
	.extern	__printf_int
	.extern	__INDIRECT__
	.calls	'debug_init','',24
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L83:
	.word	102512
	.half	3
	.word	.L84
	.byte	4
.L82:
	.byte	1
	.byte	'../libraries/zf_common/zf_common_debug.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L85
	.byte	2,1,1,3
	.word	203
	.byte	4
	.byte	'Ifx__jump_and_link',0,3,1,61,17,1,1,5
	.byte	'fun',0,1,61,43
	.word	206
	.byte	6,0,7
	.byte	'__fract',0,4,128,1,7
	.byte	'float',0,4,4,8
	.byte	'Ifx__float_to_fract',0,3,1,152,2,18
	.word	251
	.byte	1,1,5
	.byte	'a',0,1,152,2,44
	.word	263
	.byte	6,0,4
	.byte	'Ifx__stopPerfCounters',0,3,1,172,2,17,1,1,6,0,7
	.byte	'unsigned long long int',0,8,7,9
	.byte	'void',0,3
	.word	375
	.byte	8
	.byte	'__ld64',0,3,2,135,1,19
	.word	349
	.byte	1,1,5
	.byte	'addr',0,2,135,1,32
	.word	381
	.byte	6,0,4
	.byte	'__st64',0,3,2,143,1,17,1,1,5
	.byte	'addr',0,2,143,1,30
	.word	381
	.byte	5
	.byte	'value',0,2,143,1,43
	.word	349
	.byte	6,0,7
	.byte	'unsigned int',0,4,7
.L173:
	.byte	7
	.byte	'int',0,4,5,7
	.byte	'unsigned int',0,4,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON0_Bits',0,4,241,8,16,4,11
	.byte	'ENDINIT',0,4
	.word	490
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	490
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	490
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	490
	.byte	16,0,2,35,0,0,12,4,247,14,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	506
	.byte	4,2,35,0,0
.L193:
	.byte	7
	.byte	'unsigned char',0,1,8,7
	.byte	'unsigned short int',0,2,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON1_Bits',0,4,250,8,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	2,6,2,35,0,11
	.byte	'IR0',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	642
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	659
	.byte	16,0,2,35,2,0,12,4,255,14,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	681
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_WDTCPU_SR_Bits',0,4,137,9,16,4,11
	.byte	'AE',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	642
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	659
	.byte	16,0,2,35,2,0,12,4,135,15,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	925
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_WDTCPU',0,4,175,15,25,12,13
	.byte	'CON0',0
	.word	602
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	885
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	1116
	.byte	4,2,35,8,0,14
	.word	1156
	.byte	3
	.word	1219
	.byte	4
	.byte	'IfxScuWdt_clearCpuEndinitInline',0,3,3,181,3,17,1,1,5
	.byte	'watchdog',0,3,181,3,65
	.word	1224
	.byte	5
	.byte	'password',0,3,181,3,82
	.word	659
	.byte	6,0,4
	.byte	'IfxScuWdt_setCpuEndinitInline',0,3,3,140,4,17,1,1,5
	.byte	'watchdog',0,3,140,4,63
	.word	1224
	.byte	5
	.byte	'password',0,3,140,4,80
	.word	659
	.byte	6,0,8
	.byte	'IfxScuWdt_getCpuWatchdogPasswordInline',0,3,3,227,3,19
	.word	659
	.byte	1,1,5
	.byte	'watchdog',0,3,227,3,74
	.word	1224
	.byte	6,0,15,6,156,1,9,1,16
	.byte	'IfxCpu_ResourceCpu_0',0,0,16
	.byte	'IfxCpu_ResourceCpu_1',0,1,16
	.byte	'IfxCpu_ResourceCpu_none',0,2,0,8
	.byte	'IfxCpu_getCoreIndex',0,3,5,141,6,31
	.word	1454
	.byte	1,1,6,0,8
	.byte	'IfxCpu_areInterruptsEnabled',0,3,5,139,5,20
	.word	642
	.byte	1,1,6,0,8
	.byte	'IfxCpu_disableInterrupts',0,3,5,147,5,20
	.word	642
	.byte	1,1,17,6,0,0,4
	.byte	'IfxCpu_enableInterrupts',0,3,5,157,5,17,1,1,6,0,4
	.byte	'IfxCpu_forceDisableInterrupts',0,3,5,225,5,17,1,1,6,0,4
	.byte	'IfxCpu_restoreInterrupts',0,3,5,168,7,17,1,1,5
	.byte	'enabled',0,5,168,7,50
	.word	642
	.byte	6,0
.L181:
	.byte	7
	.byte	'unsigned long int',0,4,7,8
	.byte	'IfxCpu_getPerformanceCounter',0,3,5,161,6,19
	.word	1776
	.byte	1,1,5
	.byte	'address',0,5,161,6,55
	.word	659
	.byte	6,0,8
	.byte	'IfxCpu_getPerformanceCounterStickyOverflow',0,3,5,190,6,20
	.word	642
	.byte	1,1,5
	.byte	'address',0,5,190,6,70
	.word	659
	.byte	6,0,4
	.byte	'IfxCpu_updatePerformanceCounter',0,3,5,172,8,17,1,1,5
	.byte	'address',0,5,172,8,56
	.word	1776
	.byte	5
	.byte	'count',0,5,172,8,72
	.word	1776
	.byte	17,6,0,0,10
	.byte	'_Ifx_P_OUT_Bits',0,8,143,3,16,4,11
	.byte	'P0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	642
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	642
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	642
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	659
	.byte	16,0,2,35,2,0,12,8,181,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2007
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMR_Bits',0,8,169,2,16,4,11
	.byte	'PS0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	642
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	642
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	642
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'PCL0',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	642
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	642
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	642
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	642
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	642
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	642
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	642
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	642
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	642
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	642
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	642
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	642
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	642
	.byte	1,0,2,35,3,0,12,8,133,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2323
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ID_Bits',0,8,110,16,4,11
	.byte	'MODREV',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	659
	.byte	16,0,2,35,2,0,12,8,148,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2894
	.byte	4,2,35,0,0,18,4
	.word	642
	.byte	19,3,0,10
	.byte	'_Ifx_P_IOCR0_Bits',0,8,140,1,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	3,5,2,35,0,11
	.byte	'PC0',0,1
	.word	642
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	642
	.byte	3,5,2,35,1,11
	.byte	'PC1',0,1
	.word	642
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	642
	.byte	3,5,2,35,2,11
	.byte	'PC2',0,1
	.word	642
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	642
	.byte	3,5,2,35,3,11
	.byte	'PC3',0,1
	.word	642
	.byte	5,0,2,35,3,0,12,8,164,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3022
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR4_Bits',0,8,166,1,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	3,5,2,35,0,11
	.byte	'PC4',0,1
	.word	642
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	642
	.byte	3,5,2,35,1,11
	.byte	'PC5',0,1
	.word	642
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	642
	.byte	3,5,2,35,2,11
	.byte	'PC6',0,1
	.word	642
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	642
	.byte	3,5,2,35,3,11
	.byte	'PC7',0,1
	.word	642
	.byte	5,0,2,35,3,0,12,8,180,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3237
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR8_Bits',0,8,179,1,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	3,5,2,35,0,11
	.byte	'PC8',0,1
	.word	642
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	642
	.byte	3,5,2,35,1,11
	.byte	'PC9',0,1
	.word	642
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	642
	.byte	3,5,2,35,2,11
	.byte	'PC10',0,1
	.word	642
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	642
	.byte	3,5,2,35,3,11
	.byte	'PC11',0,1
	.word	642
	.byte	5,0,2,35,3,0,12,8,188,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3452
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR12_Bits',0,8,153,1,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	3,5,2,35,0,11
	.byte	'PC12',0,1
	.word	642
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	642
	.byte	3,5,2,35,1,11
	.byte	'PC13',0,1
	.word	642
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	642
	.byte	3,5,2,35,2,11
	.byte	'PC14',0,1
	.word	642
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	642
	.byte	3,5,2,35,3,11
	.byte	'PC15',0,1
	.word	642
	.byte	5,0,2,35,3,0,12,8,172,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3669
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IN_Bits',0,8,118,16,4,11
	.byte	'P0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	642
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	642
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	642
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	659
	.byte	16,0,2,35,2,0,12,8,156,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3889
	.byte	4,2,35,0,0,18,24
	.word	642
	.byte	19,23,0,10
	.byte	'_Ifx_P_PDR0_Bits',0,8,205,3,16,4,11
	.byte	'PD0',0,1
	.word	642
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	642
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'PD2',0,1
	.word	642
	.byte	3,5,2,35,1,11
	.byte	'PL2',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'PD3',0,1
	.word	642
	.byte	3,1,2,35,1,11
	.byte	'PL3',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'PD4',0,1
	.word	642
	.byte	3,5,2,35,2,11
	.byte	'PL4',0,1
	.word	642
	.byte	1,4,2,35,2,11
	.byte	'PD5',0,1
	.word	642
	.byte	3,1,2,35,2,11
	.byte	'PL5',0,1
	.word	642
	.byte	1,0,2,35,2,11
	.byte	'PD6',0,1
	.word	642
	.byte	3,5,2,35,3,11
	.byte	'PL6',0,1
	.word	642
	.byte	1,4,2,35,3,11
	.byte	'PD7',0,1
	.word	642
	.byte	3,1,2,35,3,11
	.byte	'PL7',0,1
	.word	642
	.byte	1,0,2,35,3,0,12,8,205,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4212
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PDR1_Bits',0,8,226,3,16,4,11
	.byte	'PD8',0,1
	.word	642
	.byte	3,5,2,35,0,11
	.byte	'PL8',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'PD9',0,1
	.word	642
	.byte	3,1,2,35,0,11
	.byte	'PL9',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'PD10',0,1
	.word	642
	.byte	3,5,2,35,1,11
	.byte	'PL10',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'PD11',0,1
	.word	642
	.byte	3,1,2,35,1,11
	.byte	'PL11',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'PD12',0,1
	.word	642
	.byte	3,5,2,35,2,11
	.byte	'PL12',0,1
	.word	642
	.byte	1,4,2,35,2,11
	.byte	'PD13',0,1
	.word	642
	.byte	3,1,2,35,2,11
	.byte	'PL13',0,1
	.word	642
	.byte	1,0,2,35,2,11
	.byte	'PD14',0,1
	.word	642
	.byte	3,5,2,35,3,11
	.byte	'PL14',0,1
	.word	642
	.byte	1,4,2,35,3,11
	.byte	'PD15',0,1
	.word	642
	.byte	3,1,2,35,3,11
	.byte	'PL15',0,1
	.word	642
	.byte	1,0,2,35,3,0,12,8,213,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4516
	.byte	4,2,35,0,0,18,8
	.word	642
	.byte	19,7,0,10
	.byte	'_Ifx_P_ESR_Bits',0,8,88,16,4,11
	.byte	'EN0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	642
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	642
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	642
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	659
	.byte	16,0,2,35,2,0,12,8,140,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4841
	.byte	4,2,35,0,0,18,12
	.word	642
	.byte	19,11,0,10
	.byte	'_Ifx_P_PDISC_Bits',0,8,183,3,16,4,11
	.byte	'PDIS0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'PDIS2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'PDIS3',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'PDIS4',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'PDIS5',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'PDIS6',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'PDIS7',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'PDIS8',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'PDIS9',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'PDIS10',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'PDIS11',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'PDIS12',0,1
	.word	642
	.byte	1,3,2,35,1,11
	.byte	'PDIS13',0,1
	.word	642
	.byte	1,2,2,35,1,11
	.byte	'PDIS14',0,1
	.word	642
	.byte	1,1,2,35,1,11
	.byte	'PDIS15',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	659
	.byte	16,0,2,35,2,0,12,8,197,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5181
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PCSR_Bits',0,8,165,3,16,4,11
	.byte	'SEL0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'SEL1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'SEL2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'SEL3',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'SEL4',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'SEL5',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'SEL6',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'SEL7',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	642
	.byte	2,6,2,35,1,11
	.byte	'SEL10',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'SEL11',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	467
	.byte	19,1,2,35,0,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,12,8,189,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5547
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR0_Bits',0,8,206,2,16,4,11
	.byte	'PS0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	467
	.byte	28,0,2,35,0,0,12,8,149,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5833
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR4_Bits',0,8,227,2,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	4,4,2,35,0,11
	.byte	'PS4',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	467
	.byte	24,0,2,35,0,0,12,8,165,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5980
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR8_Bits',0,8,238,2,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'PS8',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	467
	.byte	20,0,2,35,0,0,12,8,173,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6149
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR12_Bits',0,8,216,2,16,4,11
	.byte	'reserved_0',0,2
	.word	659
	.byte	12,4,2,35,0,11
	.byte	'PS12',0,1
	.word	642
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	642
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	642
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	659
	.byte	16,0,2,35,2,0,12,8,157,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6321
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR0_Bits',0,8,232,1,16,4,11
	.byte	'reserved_0',0,2
	.word	659
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	642
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	642
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	659
	.byte	12,0,2,35,2,0,12,8,229,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6496
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR4_Bits',0,8,253,1,16,4,11
	.byte	'reserved_0',0,4
	.word	467
	.byte	20,12,2,35,0,11
	.byte	'PCL4',0,1
	.word	642
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	642
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	642
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	642
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	642
	.byte	8,0,2,35,3,0,12,8,245,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6670
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR8_Bits',0,8,136,2,16,4,11
	.byte	'reserved_0',0,4
	.word	467
	.byte	24,8,2,35,0,11
	.byte	'PCL8',0,1
	.word	642
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	642
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	642
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	642
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	642
	.byte	4,0,2,35,3,0,12,8,253,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6844
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR12_Bits',0,8,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	467
	.byte	28,4,2,35,0,11
	.byte	'PCL12',0,1
	.word	642
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	642
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	642
	.byte	1,0,2,35,3,0,12,8,237,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7020
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR_Bits',0,8,249,2,16,4,11
	.byte	'PS0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	642
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	642
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	642
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	659
	.byte	16,0,2,35,2,0,12,8,141,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7176
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR_Bits',0,8,147,2,16,4,11
	.byte	'reserved_0',0,2
	.word	659
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	642
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	642
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	642
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	642
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	642
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	642
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	642
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	642
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	642
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	642
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	642
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	642
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	642
	.byte	1,0,2,35,3,0,12,8,221,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7509
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR0_Bits',0,8,192,1,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	467
	.byte	30,0,2,35,0,0,12,8,196,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7857
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_Bits',0,8,200,1,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	467
	.byte	30,0,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_P21_Bits',0,8,208,1,16,4,11
	.byte	'RDIS_CTRL',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'RX_DIS',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'TERM',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'LRXTERM',0,1
	.word	642
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	467
	.byte	24,0,2,35,0,0,12,8,204,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7981
	.byte	4,2,35,0,13
	.byte	'B_P21',0
	.word	8065
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR2_Bits',0,8,218,1,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'LVDSR',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'LVDSRL',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	642
	.byte	2,4,2,35,1,11
	.byte	'TDIS_CTRL',0,1
	.word	642
	.byte	1,3,2,35,1,11
	.byte	'TX_DIS',0,1
	.word	642
	.byte	1,2,2,35,1,11
	.byte	'TX_PD',0,1
	.word	642
	.byte	1,1,2,35,1,11
	.byte	'TX_PWDPD',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	659
	.byte	16,0,2,35,2,0,12,8,213,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8245
	.byte	4,2,35,0,0,18,76
	.word	642
	.byte	19,75,0,10
	.byte	'_Ifx_P_ACCEN1_Bits',0,8,82,16,4,11
	.byte	'reserved_0',0,4
	.word	467
	.byte	32,0,2,35,0,0,12,8,132,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8498
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ACCEN0_Bits',0,8,45,16,4,11
	.byte	'EN0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	642
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	642
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	642
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	642
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	642
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	642
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	642
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	642
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	642
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	642
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	642
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	642
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	642
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	642
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	642
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	642
	.byte	1,0,2,35,3,0,12,8,252,3,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8585
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P',0,8,229,5,25,128,2,13
	.byte	'OUT',0
	.word	2283
	.byte	4,2,35,0,13
	.byte	'OMR',0
	.word	2854
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	2973
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	3013
	.byte	4,2,35,12,13
	.byte	'IOCR0',0
	.word	3197
	.byte	4,2,35,16,13
	.byte	'IOCR4',0
	.word	3412
	.byte	4,2,35,20,13
	.byte	'IOCR8',0
	.word	3629
	.byte	4,2,35,24,13
	.byte	'IOCR12',0
	.word	3849
	.byte	4,2,35,28,13
	.byte	'reserved_20',0
	.word	3013
	.byte	4,2,35,32,13
	.byte	'IN',0
	.word	4163
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	4203
	.byte	24,2,35,40,13
	.byte	'PDR0',0
	.word	4476
	.byte	4,2,35,64,13
	.byte	'PDR1',0
	.word	4792
	.byte	4,2,35,68,13
	.byte	'reserved_48',0
	.word	4832
	.byte	8,2,35,72,13
	.byte	'ESR',0
	.word	5132
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	5172
	.byte	12,2,35,84,13
	.byte	'PDISC',0
	.word	5507
	.byte	4,2,35,96,13
	.byte	'PCSR',0
	.word	5793
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	4832
	.byte	8,2,35,104,13
	.byte	'OMSR0',0
	.word	5940
	.byte	4,2,35,112,13
	.byte	'OMSR4',0
	.word	6109
	.byte	4,2,35,116,13
	.byte	'OMSR8',0
	.word	6281
	.byte	4,2,35,120,13
	.byte	'OMSR12',0
	.word	6456
	.byte	4,2,35,124,13
	.byte	'OMCR0',0
	.word	6630
	.byte	4,3,35,128,1,13
	.byte	'OMCR4',0
	.word	6804
	.byte	4,3,35,132,1,13
	.byte	'OMCR8',0
	.word	6980
	.byte	4,3,35,136,1,13
	.byte	'OMCR12',0
	.word	7136
	.byte	4,3,35,140,1,13
	.byte	'OMSR',0
	.word	7469
	.byte	4,3,35,144,1,13
	.byte	'OMCR',0
	.word	7817
	.byte	4,3,35,148,1,13
	.byte	'reserved_98',0
	.word	4832
	.byte	8,3,35,152,1,13
	.byte	'LPCR0',0
	.word	7941
	.byte	4,3,35,160,1,13
	.byte	'LPCR1',0
	.word	8190
	.byte	4,3,35,164,1,13
	.byte	'LPCR2',0
	.word	8449
	.byte	4,3,35,168,1,13
	.byte	'reserved_A4',0
	.word	8489
	.byte	76,3,35,172,1,13
	.byte	'ACCEN1',0
	.word	8545
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	9112
	.byte	4,3,35,252,1,0,14
	.word	9152
	.byte	3
	.word	9755
	.byte	15,7,83,9,1,16
	.byte	'IfxPort_InputMode_undefined',0,127,16
	.byte	'IfxPort_InputMode_noPullDevice',0,0,16
	.byte	'IfxPort_InputMode_pullDown',0,8,16
	.byte	'IfxPort_InputMode_pullUp',0,16,0,4
	.byte	'IfxPort_setPinModeInput',0,3,7,196,4,17,1,1,5
	.byte	'port',0,7,196,4,48
	.word	9760
	.byte	5
	.byte	'pinIndex',0,7,196,4,60
	.word	642
	.byte	5
	.byte	'mode',0,7,196,4,88
	.word	9765
	.byte	6,0,15,7,134,1,9,1,16
	.byte	'IfxPort_OutputMode_pushPull',0,128,1,16
	.byte	'IfxPort_OutputMode_openDrain',0,192,1,0,15,7,120,9,1,16
	.byte	'IfxPort_OutputIdx_general',0,128,1,16
	.byte	'IfxPort_OutputIdx_alt1',0,136,1,16
	.byte	'IfxPort_OutputIdx_alt2',0,144,1,16
	.byte	'IfxPort_OutputIdx_alt3',0,152,1,16
	.byte	'IfxPort_OutputIdx_alt4',0,160,1,16
	.byte	'IfxPort_OutputIdx_alt5',0,168,1,16
	.byte	'IfxPort_OutputIdx_alt6',0,176,1,16
	.byte	'IfxPort_OutputIdx_alt7',0,184,1,0,4
	.byte	'IfxPort_setPinModeOutput',0,3,7,202,4,17,1,1,5
	.byte	'port',0,7,202,4,49
	.word	9760
	.byte	5
	.byte	'pinIndex',0,7,202,4,61
	.word	642
	.byte	5
	.byte	'mode',0,7,202,4,90
	.word	9970
	.byte	5
	.byte	'index',0,7,202,4,114
	.word	10040
	.byte	6,0,15,7,172,1,9,4,16
	.byte	'IfxPort_State_notChanged',0,0,16
	.byte	'IfxPort_State_high',0,1,16
	.byte	'IfxPort_State_low',0,128,128,4,16
	.byte	'IfxPort_State_toggled',0,129,128,4,0,4
	.byte	'IfxPort_setPinState',0,3,7,208,4,17,1,1,5
	.byte	'port',0,7,208,4,44
	.word	9760
	.byte	5
	.byte	'pinIndex',0,7,208,4,56
	.word	642
	.byte	5
	.byte	'action',0,7,208,4,80
	.word	10353
	.byte	6,0,8
	.byte	'IfxScuCcu_getStmFrequency',0,3,9,226,8,20
	.word	263
	.byte	1,1,6,0,10
	.byte	'_Ifx_SRC_SRCR_Bits',0,11,45,16,4,11
	.byte	'SRPN',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	642
	.byte	2,6,2,35,1,11
	.byte	'SRE',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'TOS',0,1
	.word	642
	.byte	2,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	642
	.byte	3,0,2,35,1,11
	.byte	'ECC',0,1
	.word	642
	.byte	6,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	642
	.byte	2,0,2,35,2,11
	.byte	'SRR',0,1
	.word	642
	.byte	1,7,2,35,3,11
	.byte	'CLRR',0,1
	.word	642
	.byte	1,6,2,35,3,11
	.byte	'SETR',0,1
	.word	642
	.byte	1,5,2,35,3,11
	.byte	'IOV',0,1
	.word	642
	.byte	1,4,2,35,3,11
	.byte	'IOVCLR',0,1
	.word	642
	.byte	1,3,2,35,3,11
	.byte	'SWS',0,1
	.word	642
	.byte	1,2,2,35,3,11
	.byte	'SWSCLR',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	642
	.byte	1,0,2,35,3,0,12,11,70,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10574
	.byte	4,2,35,0,0,14
	.word	10864
	.byte	3
	.word	10903
	.byte	4
	.byte	'IfxSrc_clearRequest',0,3,10,250,1,17,1,1,5
	.byte	'src',0,10,250,1,60
	.word	10908
	.byte	6,0,10
	.byte	'_Ifx_ASCLIN_CLC_Bits',0,13,118,16,4,11
	.byte	'DISR',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'DISS',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'EDIS',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	467
	.byte	28,0,2,35,0,0,12,13,207,3,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10956
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_IOCR_Bits',0,13,169,2,16,4,11
	.byte	'ALTI',0,1
	.word	642
	.byte	3,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'DEPTH',0,2
	.word	659
	.byte	6,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	642
	.byte	6,0,2,35,1,11
	.byte	'CTS',0,1
	.word	642
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	659
	.byte	7,7,2,35,2,11
	.byte	'RCPOL',0,1
	.word	642
	.byte	1,6,2,35,3,11
	.byte	'CPOL',0,1
	.word	642
	.byte	1,5,2,35,3,11
	.byte	'SPOL',0,1
	.word	642
	.byte	1,4,2,35,3,11
	.byte	'LB',0,1
	.word	642
	.byte	1,3,2,35,3,11
	.byte	'CTSEN',0,1
	.word	642
	.byte	1,2,2,35,3,11
	.byte	'RXM',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'TXM',0,1
	.word	642
	.byte	1,0,2,35,3,0,12,13,151,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11115
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_ID_Bits',0,13,161,2,16,4,11
	.byte	'MODREV',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	659
	.byte	16,0,2,35,2,0,12,13,143,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11410
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_TXFIFOCON_Bits',0,13,149,3,16,4,11
	.byte	'FLUSH',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'ENO',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	642
	.byte	4,2,2,35,0,11
	.byte	'INW',0,1
	.word	642
	.byte	2,0,2,35,0,11
	.byte	'INTLEVEL',0,1
	.word	642
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	642
	.byte	4,0,2,35,1,11
	.byte	'FILL',0,1
	.word	642
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,2
	.word	659
	.byte	11,0,2,35,2,0,12,13,247,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11535
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_RXFIFOCON_Bits',0,13,129,3,16,4,11
	.byte	'FLUSH',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'ENI',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	642
	.byte	4,2,2,35,0,11
	.byte	'OUTW',0,1
	.word	642
	.byte	2,0,2,35,0,11
	.byte	'INTLEVEL',0,1
	.word	642
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	642
	.byte	4,0,2,35,1,11
	.byte	'FILL',0,1
	.word	642
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,2
	.word	659
	.byte	10,1,2,35,2,11
	.byte	'BUF',0,1
	.word	642
	.byte	1,0,2,35,3,0,12,13,231,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11760
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_BITCON_Bits',0,13,88,16,4,11
	.byte	'PRESCALER',0,2
	.word	659
	.byte	12,4,2,35,0,11
	.byte	'reserved_12',0,1
	.word	642
	.byte	4,0,2,35,1,11
	.byte	'OVERSAMPLING',0,1
	.word	642
	.byte	4,4,2,35,2,11
	.byte	'reserved_20',0,1
	.word	642
	.byte	4,0,2,35,2,11
	.byte	'SAMPLEPOINT',0,1
	.word	642
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	642
	.byte	3,1,2,35,3,11
	.byte	'SM',0,1
	.word	642
	.byte	1,0,2,35,3,0,12,13,183,3,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12001
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_FRAMECON_Bits',0,13,145,2,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	6,2,2,35,0,11
	.byte	'IDLE',0,2
	.word	659
	.byte	3,7,2,35,0,11
	.byte	'STOP',0,1
	.word	642
	.byte	3,4,2,35,1,11
	.byte	'LEAD',0,1
	.word	642
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'MODE',0,1
	.word	642
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	659
	.byte	10,4,2,35,2,11
	.byte	'MSB',0,1
	.word	642
	.byte	1,3,2,35,3,11
	.byte	'CEN',0,1
	.word	642
	.byte	1,2,2,35,3,11
	.byte	'PEN',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'ODD',0,1
	.word	642
	.byte	1,0,2,35,3,0,12,13,135,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12222
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_DATCON_Bits',0,13,136,1,16,4,11
	.byte	'DATLEN',0,1
	.word	642
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	659
	.byte	9,3,2,35,0,11
	.byte	'HO',0,1
	.word	642
	.byte	1,2,2,35,1,11
	.byte	'RM',0,1
	.word	642
	.byte	1,1,2,35,1,11
	.byte	'CSM',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'RESPONSE',0,1
	.word	642
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	642
	.byte	8,0,2,35,3,0,12,13,223,3,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12487
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_BRG_Bits',0,13,109,16,4,11
	.byte	'DENOMINATOR',0,2
	.word	659
	.byte	12,4,2,35,0,11
	.byte	'reserved_12',0,1
	.word	642
	.byte	4,0,2,35,1,11
	.byte	'NUMERATOR',0,2
	.word	659
	.byte	12,4,2,35,2,11
	.byte	'reserved_28',0,1
	.word	642
	.byte	4,0,2,35,3,0,12,13,199,3,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12684
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_BRD_Bits',0,13,100,16,4,11
	.byte	'LOWERLIMIT',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'UPPERLIMIT',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'MEASURED',0,2
	.word	659
	.byte	12,4,2,35,2,11
	.byte	'reserved_28',0,1
	.word	642
	.byte	4,0,2,35,3,0,12,13,191,3,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12841
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_LIN_CON_Bits',0,13,216,2,16,4,11
	.byte	'reserved_0',0,4
	.word	467
	.byte	23,9,2,35,0,11
	.byte	'CSI',0,1
	.word	642
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	642
	.byte	1,7,2,35,3,11
	.byte	'CSEN',0,1
	.word	642
	.byte	1,6,2,35,3,11
	.byte	'MS',0,1
	.word	642
	.byte	1,5,2,35,3,11
	.byte	'ABD',0,1
	.word	642
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	642
	.byte	4,0,2,35,3,0,12,13,191,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12995
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_LIN_BTIMER_Bits',0,13,209,2,16,4,11
	.byte	'BREAK',0,1
	.word	642
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	467
	.byte	26,0,2,35,0,0,12,13,183,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13195
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_LIN_HTIMER_Bits',0,13,228,2,16,4,11
	.byte	'HEADER',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	467
	.byte	24,0,2,35,0,0,12,13,199,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13309
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_LIN',0,13,135,5,25,12,13
	.byte	'CON',0
	.word	13155
	.byte	4,2,35,0,13
	.byte	'BTIMER',0
	.word	13269
	.byte	4,2,35,4,13
	.byte	'HTIMER',0
	.word	13384
	.byte	4,2,35,8,0,14
	.word	13424
	.byte	10
	.byte	'_Ifx_ASCLIN_FLAGS_Bits',0,13,148,1,16,4,11
	.byte	'TH',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'TR',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'RH',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'RR',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'FED',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'RED',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	659
	.byte	6,3,2,35,0,11
	.byte	'TWRQ',0,1
	.word	642
	.byte	1,2,2,35,1,11
	.byte	'THRQ',0,1
	.word	642
	.byte	1,1,2,35,1,11
	.byte	'TRRQ',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'PE',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'TC',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'FE',0,1
	.word	642
	.byte	1,5,2,35,2,11
	.byte	'HT',0,1
	.word	642
	.byte	1,4,2,35,2,11
	.byte	'RT',0,1
	.word	642
	.byte	1,3,2,35,2,11
	.byte	'BD',0,1
	.word	642
	.byte	1,2,2,35,2,11
	.byte	'LP',0,1
	.word	642
	.byte	1,1,2,35,2,11
	.byte	'LA',0,1
	.word	642
	.byte	1,0,2,35,2,11
	.byte	'LC',0,1
	.word	642
	.byte	1,7,2,35,3,11
	.byte	'CE',0,1
	.word	642
	.byte	1,6,2,35,3,11
	.byte	'RFO',0,1
	.word	642
	.byte	1,5,2,35,3,11
	.byte	'RFU',0,1
	.word	642
	.byte	1,4,2,35,3,11
	.byte	'RFL',0,1
	.word	642
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	642
	.byte	1,2,2,35,3,11
	.byte	'TFO',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'TFL',0,1
	.word	642
	.byte	1,0,2,35,3,0,12,13,231,3,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13497
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_FLAGSSET_Bits',0,13,241,1,16,4,11
	.byte	'THS',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'TRS',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'RHS',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'RRS',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'FEDS',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'REDS',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	659
	.byte	6,3,2,35,0,11
	.byte	'TWRQS',0,1
	.word	642
	.byte	1,2,2,35,1,11
	.byte	'THRQS',0,1
	.word	642
	.byte	1,1,2,35,1,11
	.byte	'TRRQS',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'PES',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'TCS',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'FES',0,1
	.word	642
	.byte	1,5,2,35,2,11
	.byte	'HTS',0,1
	.word	642
	.byte	1,4,2,35,2,11
	.byte	'RTS',0,1
	.word	642
	.byte	1,3,2,35,2,11
	.byte	'BDS',0,1
	.word	642
	.byte	1,2,2,35,2,11
	.byte	'LPS',0,1
	.word	642
	.byte	1,1,2,35,2,11
	.byte	'LAS',0,1
	.word	642
	.byte	1,0,2,35,2,11
	.byte	'LCS',0,1
	.word	642
	.byte	1,7,2,35,3,11
	.byte	'CES',0,1
	.word	642
	.byte	1,6,2,35,3,11
	.byte	'RFOS',0,1
	.word	642
	.byte	1,5,2,35,3,11
	.byte	'RFUS',0,1
	.word	642
	.byte	1,4,2,35,3,11
	.byte	'RFLS',0,1
	.word	642
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	642
	.byte	1,2,2,35,3,11
	.byte	'TFOS',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'TFLS',0,1
	.word	642
	.byte	1,0,2,35,3,0,12,13,255,3,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13983
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_FLAGSCLEAR_Bits',0,13,180,1,16,4,11
	.byte	'THC',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'TRC',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'RHC',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'RRC',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'FEDC',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'REDC',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	659
	.byte	6,3,2,35,0,11
	.byte	'TWRQC',0,1
	.word	642
	.byte	1,2,2,35,1,11
	.byte	'THRQC',0,1
	.word	642
	.byte	1,1,2,35,1,11
	.byte	'TRRQC',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'PEC',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'TCC',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'FEC',0,1
	.word	642
	.byte	1,5,2,35,2,11
	.byte	'HTC',0,1
	.word	642
	.byte	1,4,2,35,2,11
	.byte	'RTC',0,1
	.word	642
	.byte	1,3,2,35,2,11
	.byte	'BDC',0,1
	.word	642
	.byte	1,2,2,35,2,11
	.byte	'LPC',0,1
	.word	642
	.byte	1,1,2,35,2,11
	.byte	'LAC',0,1
	.word	642
	.byte	1,0,2,35,2,11
	.byte	'LCC',0,1
	.word	642
	.byte	1,7,2,35,3,11
	.byte	'CEC',0,1
	.word	642
	.byte	1,6,2,35,3,11
	.byte	'RFOC',0,1
	.word	642
	.byte	1,5,2,35,3,11
	.byte	'RFUC',0,1
	.word	642
	.byte	1,4,2,35,3,11
	.byte	'RFLC',0,1
	.word	642
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	642
	.byte	1,2,2,35,3,11
	.byte	'TFOC',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'TFLC',0,1
	.word	642
	.byte	1,0,2,35,3,0,12,13,239,3,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14496
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_FLAGSENABLE_Bits',0,13,212,1,16,4,11
	.byte	'THE',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'TRE',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'RHE',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'RRE',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'FEDE',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'REDE',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	659
	.byte	9,0,2,35,0,11
	.byte	'PEE',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'TCE',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'FEE',0,1
	.word	642
	.byte	1,5,2,35,2,11
	.byte	'HTE',0,1
	.word	642
	.byte	1,4,2,35,2,11
	.byte	'RTE',0,1
	.word	642
	.byte	1,3,2,35,2,11
	.byte	'BDE',0,1
	.word	642
	.byte	1,2,2,35,2,11
	.byte	'LPE',0,1
	.word	642
	.byte	1,1,2,35,2,11
	.byte	'ABE',0,1
	.word	642
	.byte	1,0,2,35,2,11
	.byte	'LCE',0,1
	.word	642
	.byte	1,7,2,35,3,11
	.byte	'CEE',0,1
	.word	642
	.byte	1,6,2,35,3,11
	.byte	'RFOE',0,1
	.word	642
	.byte	1,5,2,35,3,11
	.byte	'RFUE',0,1
	.word	642
	.byte	1,4,2,35,3,11
	.byte	'RFLE',0,1
	.word	642
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	642
	.byte	1,2,2,35,3,11
	.byte	'TFOE',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'TFLE',0,1
	.word	642
	.byte	1,0,2,35,3,0,12,13,247,3,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15011
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_TXDATA_Bits',0,13,143,3,16,4,11
	.byte	'DATA',0,4
	.word	467
	.byte	32,0,2,35,0,0,12,13,239,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15476
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_RXDATA_Bits',0,13,245,2,16,4,11
	.byte	'DATA',0,4
	.word	467
	.byte	32,0,2,35,0,0,12,13,215,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15563
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_CSR_Bits',0,13,128,1,16,4,11
	.byte	'CLKSEL',0,1
	.word	642
	.byte	5,3,2,35,0,11
	.byte	'reserved_5',0,4
	.word	467
	.byte	26,1,2,35,0,11
	.byte	'CON',0,1
	.word	642
	.byte	1,0,2,35,3,0,12,13,215,3,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15650
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_RXDATAD_Bits',0,13,251,2,16,4,11
	.byte	'DATA',0,4
	.word	467
	.byte	32,0,2,35,0,0,12,13,223,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15773
	.byte	4,2,35,0,0,18,148,1
	.word	642
	.byte	19,147,1,0,10
	.byte	'_Ifx_ASCLIN_OCS_Bits',0,13,235,2,16,4,11
	.byte	'reserved_0',0,4
	.word	467
	.byte	24,8,2,35,0,11
	.byte	'SUS',0,1
	.word	642
	.byte	4,4,2,35,3,11
	.byte	'SUS_P',0,1
	.word	642
	.byte	1,3,2,35,3,11
	.byte	'SUSSTA',0,1
	.word	642
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	642
	.byte	2,0,2,35,3,0,12,13,207,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15872
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_KRSTCLR_Bits',0,13,202,2,16,4,11
	.byte	'CLR',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	467
	.byte	31,0,2,35,0,0,12,13,175,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16035
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_KRST1_Bits',0,13,195,2,16,4,11
	.byte	'RST',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	467
	.byte	31,0,2,35,0,0,12,13,167,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16144
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_KRST0_Bits',0,13,187,2,16,4,11
	.byte	'RST',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'RSTSTAT',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	467
	.byte	30,0,2,35,0,0,12,13,159,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16251
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_ACCEN1_Bits',0,13,82,16,4,11
	.byte	'reserved_0',0,4
	.word	467
	.byte	32,0,2,35,0,0,12,13,175,3,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16377
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_ACCEN0_Bits',0,13,45,16,4,11
	.byte	'EN0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	642
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	642
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	642
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	642
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	642
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	642
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	642
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	642
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	642
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	642
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	642
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	642
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	642
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	642
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	642
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	642
	.byte	1,0,2,35,3,0,12,13,167,3,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16469
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN',0,13,153,5,25,128,2,13
	.byte	'CLC',0
	.word	11075
	.byte	4,2,35,0,13
	.byte	'IOCR',0
	.word	11370
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	11495
	.byte	4,2,35,8,13
	.byte	'TXFIFOCON',0
	.word	11720
	.byte	4,2,35,12,13
	.byte	'RXFIFOCON',0
	.word	11961
	.byte	4,2,35,16,13
	.byte	'BITCON',0
	.word	12182
	.byte	4,2,35,20,13
	.byte	'FRAMECON',0
	.word	12447
	.byte	4,2,35,24,13
	.byte	'DATCON',0
	.word	12644
	.byte	4,2,35,28,13
	.byte	'BRG',0
	.word	12801
	.byte	4,2,35,32,13
	.byte	'BRD',0
	.word	12955
	.byte	4,2,35,36,13
	.byte	'LIN',0
	.word	13492
	.byte	12,2,35,40,13
	.byte	'FLAGS',0
	.word	13943
	.byte	4,2,35,52,13
	.byte	'FLAGSSET',0
	.word	14456
	.byte	4,2,35,56,13
	.byte	'FLAGSCLEAR',0
	.word	14971
	.byte	4,2,35,60,13
	.byte	'FLAGSENABLE',0
	.word	15436
	.byte	4,2,35,64,13
	.byte	'TXDATA',0
	.word	15523
	.byte	4,2,35,68,13
	.byte	'RXDATA',0
	.word	15610
	.byte	4,2,35,72,13
	.byte	'CSR',0
	.word	15733
	.byte	4,2,35,76,13
	.byte	'RXDATAD',0
	.word	15821
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	15861
	.byte	148,1,2,35,84,13
	.byte	'OCS',0
	.word	15995
	.byte	4,3,35,232,1,13
	.byte	'KRSTCLR',0
	.word	16104
	.byte	4,3,35,236,1,13
	.byte	'KRST1',0
	.word	16211
	.byte	4,3,35,240,1,13
	.byte	'KRST0',0
	.word	16337
	.byte	4,3,35,244,1,13
	.byte	'ACCEN1',0
	.word	16429
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	17001
	.byte	4,3,35,252,1,0,14
	.word	17041
	.byte	3
	.word	17483
	.byte	4
	.byte	'IfxAsclin_enableCts',0,3,12,228,13,17,1,1,5
	.byte	'asclin',0,12,228,13,49
	.word	17488
	.byte	5
	.byte	'enable',0,12,228,13,65
	.word	642
	.byte	6,0,15,12,123,9,1,16
	.byte	'IfxAsclin_CtsInputSelect_0',0,0,16
	.byte	'IfxAsclin_CtsInputSelect_1',0,1,16
	.byte	'IfxAsclin_CtsInputSelect_2',0,2,16
	.byte	'IfxAsclin_CtsInputSelect_3',0,3,0,4
	.byte	'IfxAsclin_setCtsInput',0,3,12,169,17,17,1,1,5
	.byte	'asclin',0,12,169,17,51
	.word	17488
	.byte	5
	.byte	'ctsi',0,12,169,17,84
	.word	17555
	.byte	6,0,15,12,181,2,9,1,16
	.byte	'IfxAsclin_RxInputSelect_0',0,0,16
	.byte	'IfxAsclin_RxInputSelect_1',0,1,16
	.byte	'IfxAsclin_RxInputSelect_2',0,2,16
	.byte	'IfxAsclin_RxInputSelect_3',0,3,16
	.byte	'IfxAsclin_RxInputSelect_4',0,4,16
	.byte	'IfxAsclin_RxInputSelect_5',0,5,16
	.byte	'IfxAsclin_RxInputSelect_6',0,6,16
	.byte	'IfxAsclin_RxInputSelect_7',0,7,0,4
	.byte	'IfxAsclin_setRxInput',0,3,12,191,18,17,1,1,5
	.byte	'asclin',0,12,191,18,50
	.word	17488
	.byte	5
	.byte	'alti',0,12,191,18,82
	.word	17739
	.byte	6,0,7
	.byte	'short int',0,2,5,7
	.byte	'long int',0,4,5,20,14,60,9,12,13
	.byte	'count',0
	.word	18031
	.byte	2,2,35,0,13
	.byte	'readerWaitx',0
	.word	18044
	.byte	4,2,35,2,13
	.byte	'writerWaitx',0
	.word	18044
	.byte	4,2,35,6,13
	.byte	'maxcount',0
	.word	18031
	.byte	2,2,35,10,0,14
	.word	642
	.byte	14
	.word	642
	.byte	10
	.byte	'_Fifo',0,14,73,16,28,13
	.byte	'buffer',0
	.word	381
	.byte	4,2,35,0,13
	.byte	'shared',0
	.word	18056
	.byte	12,2,35,4,13
	.byte	'startIndex',0
	.word	18031
	.byte	2,2,35,16,13
	.byte	'endIndex',0
	.word	18031
	.byte	2,2,35,18,13
	.byte	'size',0
	.word	18031
	.byte	2,2,35,20,13
	.byte	'elementSize',0
	.word	18031
	.byte	2,2,35,22,13
	.byte	'eventReader',0
	.word	18137
	.byte	1,2,35,24,13
	.byte	'eventWriter',0
	.word	18142
	.byte	1,2,35,25,0,3
	.word	18147
	.byte	8
	.byte	'Ifx_Fifo_readCount',0,3,14,206,1,22
	.word	18031
	.byte	1,1,5
	.byte	'fifo',0,14,206,1,51
	.word	18306
	.byte	6,0,10
	.byte	'_Ifx_STM_CLC_Bits',0,16,100,16,4,11
	.byte	'DISR',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'DISS',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'EDIS',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	467
	.byte	28,0,2,35,0,0,12,16,149,2,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18358
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_ID_Bits',0,16,142,1,16,4,11
	.byte	'MODREV',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	659
	.byte	16,0,2,35,2,0,12,16,181,2,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18514
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM0_Bits',0,16,192,1,16,4,11
	.byte	'STM31_0',0,4
	.word	467
	.byte	32,0,2,35,0,0,12,16,229,2,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18636
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM1_Bits',0,16,204,1,16,4,11
	.byte	'STM35_4',0,4
	.word	467
	.byte	32,0,2,35,0,0,12,16,245,2,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18721
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM2_Bits',0,16,210,1,16,4,11
	.byte	'STM39_8',0,4
	.word	467
	.byte	32,0,2,35,0,0,12,16,253,2,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18806
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM3_Bits',0,16,216,1,16,4,11
	.byte	'STM43_12',0,4
	.word	467
	.byte	32,0,2,35,0,0,12,16,133,3,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18891
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM4_Bits',0,16,222,1,16,4,11
	.byte	'STM47_16',0,4
	.word	467
	.byte	32,0,2,35,0,0,12,16,141,3,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18977
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM5_Bits',0,16,228,1,16,4,11
	.byte	'STM51_20',0,4
	.word	467
	.byte	32,0,2,35,0,0,12,16,149,3,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19063
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM6_Bits',0,16,234,1,16,4,11
	.byte	'STM63_32',0,4
	.word	467
	.byte	32,0,2,35,0,0,12,16,157,3,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19149
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_CAP_Bits',0,16,88,16,4,11
	.byte	'STMCAP63_32',0,4
	.word	467
	.byte	32,0,2,35,0,0,12,16,133,2,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19235
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_CMP_Bits',0,16,123,16,4,11
	.byte	'CMPVAL',0,4
	.word	467
	.byte	32,0,2,35,0,0,12,16,165,2,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19322
	.byte	4,2,35,0,0,18,8
	.word	19364
	.byte	19,1,0,10
	.byte	'_Ifx_STM_CMCON_Bits',0,16,110,16,4,11
	.byte	'MSIZE0',0,1
	.word	642
	.byte	5,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	642
	.byte	3,0,2,35,0,11
	.byte	'MSTART0',0,1
	.word	642
	.byte	5,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	642
	.byte	3,0,2,35,1,11
	.byte	'MSIZE1',0,1
	.word	642
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	642
	.byte	3,0,2,35,2,11
	.byte	'MSTART1',0,1
	.word	642
	.byte	5,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	642
	.byte	3,0,2,35,3,0,12,16,157,2,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19413
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_ICR_Bits',0,16,129,1,16,4,11
	.byte	'CMP0EN',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'CMP0IR',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'CMP0OS',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'CMP1EN',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'CMP1IR',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'CMP1OS',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,4
	.word	467
	.byte	25,0,2,35,0,0,12,16,173,2,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19644
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_ISCR_Bits',0,16,150,1,16,4,11
	.byte	'CMP0IRR',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'CMP0IRS',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'CMP1IRR',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'CMP1IRS',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	467
	.byte	28,0,2,35,0,0,12,16,189,2,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19861
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM0SV_Bits',0,16,198,1,16,4,11
	.byte	'STM31_0',0,4
	.word	467
	.byte	32,0,2,35,0,0,12,16,237,2,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20025
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_CAPSV_Bits',0,16,94,16,4,11
	.byte	'STMCAP63_32',0,4
	.word	467
	.byte	32,0,2,35,0,0,12,16,141,2,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20112
	.byte	4,2,35,0,0,18,144,1
	.word	642
	.byte	19,143,1,0,10
	.byte	'_Ifx_STM_OCS_Bits',0,16,182,1,16,4,11
	.byte	'reserved_0',0,4
	.word	467
	.byte	24,8,2,35,0,11
	.byte	'SUS',0,1
	.word	642
	.byte	4,4,2,35,3,11
	.byte	'SUS_P',0,1
	.word	642
	.byte	1,3,2,35,3,11
	.byte	'SUSSTA',0,1
	.word	642
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	642
	.byte	2,0,2,35,3,0,12,16,221,2,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20212
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_KRSTCLR_Bits',0,16,175,1,16,4,11
	.byte	'CLR',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	467
	.byte	31,0,2,35,0,0,12,16,213,2,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20372
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_KRST1_Bits',0,16,168,1,16,4,11
	.byte	'RST',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	467
	.byte	31,0,2,35,0,0,12,16,205,2,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20478
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_KRST0_Bits',0,16,160,1,16,4,11
	.byte	'RST',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'RSTSTAT',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	467
	.byte	30,0,2,35,0,0,12,16,197,2,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20582
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_ACCEN1_Bits',0,16,82,16,4,11
	.byte	'reserved_0',0,4
	.word	467
	.byte	32,0,2,35,0,0,12,16,253,1,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20705
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_ACCEN0_Bits',0,16,45,16,4,11
	.byte	'EN0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	642
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	642
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	642
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	642
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	642
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	642
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	642
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	642
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	642
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	642
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	642
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	642
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	642
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	642
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	642
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	642
	.byte	1,0,2,35,3,0,12,16,245,1,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20794
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM',0,16,173,3,25,128,2,13
	.byte	'CLC',0
	.word	18474
	.byte	4,2,35,0,13
	.byte	'reserved_4',0
	.word	3013
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	18596
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	3013
	.byte	4,2,35,12,13
	.byte	'TIM0',0
	.word	18681
	.byte	4,2,35,16,13
	.byte	'TIM1',0
	.word	18766
	.byte	4,2,35,20,13
	.byte	'TIM2',0
	.word	18851
	.byte	4,2,35,24,13
	.byte	'TIM3',0
	.word	18937
	.byte	4,2,35,28,13
	.byte	'TIM4',0
	.word	19023
	.byte	4,2,35,32,13
	.byte	'TIM5',0
	.word	19109
	.byte	4,2,35,36,13
	.byte	'TIM6',0
	.word	19195
	.byte	4,2,35,40,13
	.byte	'CAP',0
	.word	19282
	.byte	4,2,35,44,13
	.byte	'CMP',0
	.word	19404
	.byte	8,2,35,48,13
	.byte	'CMCON',0
	.word	19604
	.byte	4,2,35,56,13
	.byte	'ICR',0
	.word	19821
	.byte	4,2,35,60,13
	.byte	'ISCR',0
	.word	19985
	.byte	4,2,35,64,13
	.byte	'reserved_44',0
	.word	5172
	.byte	12,2,35,68,13
	.byte	'TIM0SV',0
	.word	20072
	.byte	4,2,35,80,13
	.byte	'CAPSV',0
	.word	20161
	.byte	4,2,35,84,13
	.byte	'reserved_58',0
	.word	20201
	.byte	144,1,2,35,88,13
	.byte	'OCS',0
	.word	20332
	.byte	4,3,35,232,1,13
	.byte	'KRSTCLR',0
	.word	20438
	.byte	4,3,35,236,1,13
	.byte	'KRST1',0
	.word	20542
	.byte	4,3,35,240,1,13
	.byte	'KRST0',0
	.word	20665
	.byte	4,3,35,244,1,13
	.byte	'ACCEN1',0
	.word	20754
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	21323
	.byte	4,3,35,252,1,0,14
	.word	21363
	.byte	3
	.word	21783
	.byte	8
	.byte	'IfxStm_get',0,3,15,162,4,19
	.word	349
	.byte	1,1,5
	.byte	'stm',0,15,162,4,39
	.word	21788
	.byte	6,0,8
	.byte	'IfxStm_getFrequency',0,3,15,179,4,20
	.word	263
	.byte	1,1,5
	.byte	'stm',0,15,179,4,49
	.word	21788
	.byte	17,6,6,0,0,8
	.byte	'IfxStm_getLower',0,3,15,190,4,19
	.word	1776
	.byte	1,1,5
	.byte	'stm',0,15,190,4,44
	.word	21788
	.byte	6,0,8
	.byte	'disableInterrupts',0,3,17,108,20
	.word	642
	.byte	1,1,17,6,0,0,4
	.byte	'restoreInterrupts',0,3,17,142,1,17,1,1,5
	.byte	'enabled',0,17,142,1,43
	.word	642
	.byte	17,6,0,0,7
	.byte	'long long int',0,8,5,8
	.byte	'getDeadLine',0,3,17,164,2,25
	.word	22004
	.byte	1,1,5
	.byte	'timeout',0,17,164,2,50
	.word	22004
	.byte	17,6,0,0,8
	.byte	'isDeadLine',0,3,17,211,2,20
	.word	642
	.byte	1,1,5
	.byte	'deadLine',0,17,211,2,44
	.word	22004
	.byte	17,6,0,0,8
	.byte	'now',0,3,17,221,1,25
	.word	22004
	.byte	1,1,17,6,6,6,0,0,8
	.byte	'nowWithoutCriticalSection',0,3,17,240,1,25
	.word	22004
	.byte	1,1,17,6,0,0,7
	.byte	'char',0,1,6
.L195:
	.byte	3
	.word	22175
	.byte	21
	.word	22183
	.byte	22
	.word	22175
.L217:
	.byte	3
	.word	22193
	.byte	21
	.word	22198
	.byte	23
	.byte	'sprintf',0,18,146,1,16
	.word	483
	.byte	1,1,1,1,24,18,146,1,32
	.word	22188
	.byte	24,18,146,1,55
	.word	22203
	.byte	25,18,146,1,67,0,10
	.byte	'_iobuf',0,18,85,8,20,13
	.byte	'_ptr',0
	.word	22183
	.byte	4,2,35,0,13
	.byte	'_base',0
	.word	22183
	.byte	4,2,35,4,13
	.byte	'_cnt',0
	.word	483
	.byte	4,2,35,8,13
	.byte	'_bufsiz',0
	.word	483
	.byte	4,2,35,12,13
	.byte	'_flag',0
	.word	659
	.byte	2,2,35,16,13
	.byte	'_file',0
	.word	642
	.byte	1,2,35,18,0
.L175:
	.byte	3
	.word	22253
	.byte	23
	.byte	'strlen',0,19,52,17
	.word	467
	.byte	1,1,1,1,24,19,52,39
	.word	22198
	.byte	0,23
	.byte	'memset',0,19,56,17
	.word	381
	.byte	1,1,1,1,24,19,56,33
	.word	381
	.byte	24,19,56,36
	.word	483
	.byte	24,19,56,41
	.word	467
	.byte	0,26
	.byte	'assert_interrupt_config',0,20,43,8,1,1,1,1,15,21,42,9,1,16
	.byte	'FIFO_SUCCESS',0,0,16
	.byte	'FIFO_RESET_UNDO',0,1,16
	.byte	'FIFO_CLEAR_UNDO',0,2,16
	.byte	'FIFO_BUFFER_NULL',0,3,16
	.byte	'FIFO_WRITE_UNDO',0,4,16
	.byte	'FIFO_SPACE_NO_ENOUGH',0,5,16
	.byte	'FIFO_READ_UNDO',0,6,16
	.byte	'FIFO_DATA_NO_ENOUGH',0,7,0,15,21,78,9,1,16
	.byte	'FIFO_DATA_8BIT',0,0,16
	.byte	'FIFO_DATA_16BIT',0,1,16
	.byte	'FIFO_DATA_32BIT',0,2,0
.L244:
	.byte	20,21,85,9,24,13
	.byte	'execution',0
	.word	642
	.byte	1,2,35,0,13
	.byte	'type',0
	.word	22621
	.byte	1,2,35,1,13
	.byte	'buffer',0
	.word	381
	.byte	4,2,35,4,13
	.byte	'head',0
	.word	1776
	.byte	4,2,35,8,13
	.byte	'end',0
	.word	1776
	.byte	4,2,35,12,13
	.byte	'size',0
	.word	1776
	.byte	4,2,35,16,13
	.byte	'max',0
	.word	1776
	.byte	4,2,35,20,0,3
	.word	22680
	.byte	23
	.byte	'fifo_write_buffer',0,21,100,17
	.word	22465
	.byte	1,1,1,1,5
	.byte	'fifo',0,21,100,55
	.word	22789
	.byte	5
	.byte	'dat',0,21,100,67
	.word	381
	.byte	5
	.byte	'length',0,21,100,79
	.word	1776
	.byte	0,3
	.word	1776
	.byte	15,21,72,9,1,16
	.byte	'FIFO_READ_AND_CLEAN',0,0,16
	.byte	'FIFO_READ_ONLY',0,1,0,23
	.byte	'fifo_read_buffer',0,21,102,17
	.word	22465
	.byte	1,1,1,1,5
	.byte	'fifo',0,21,102,55
	.word	22789
	.byte	5
	.byte	'dat',0,21,102,67
	.word	381
	.byte	5
	.byte	'length',0,21,102,80
	.word	22865
	.byte	5
	.byte	'flag',0,21,102,108
	.word	22870
	.byte	0,23
	.byte	'fifo_init',0,21,105,17
	.word	22465
	.byte	1,1,1,1,5
	.byte	'fifo',0,21,105,55
	.word	22789
	.byte	5
	.byte	'type',0,21,105,81
	.word	22621
	.byte	5
	.byte	'buffer_addr',0,21,105,93
	.word	381
	.byte	5
	.byte	'size',0,21,105,113
	.word	1776
	.byte	0,27
	.word	211
	.byte	28
	.word	237
	.byte	6,0,27
	.word	272
	.byte	28
	.word	304
	.byte	6,0,27
	.word	317
	.byte	6,0,27
	.word	386
	.byte	28
	.word	405
	.byte	6,0,27
	.word	421
	.byte	28
	.word	436
	.byte	28
	.word	450
	.byte	6,0,27
	.word	1229
	.byte	28
	.word	1269
	.byte	28
	.word	1287
	.byte	6,0,27
	.word	1307
	.byte	28
	.word	1345
	.byte	28
	.word	1363
	.byte	6,0,27
	.word	1383
	.byte	28
	.word	1434
	.byte	6,0,27
	.word	1533
	.byte	6,0,27
	.word	1567
	.byte	6,0,27
	.word	1609
	.byte	17,29
	.word	1567
	.byte	30
	.word	1607
	.byte	0,6,0,0,27
	.word	1650
	.byte	6,0,27
	.word	1684
	.byte	6,0,27
	.word	1724
	.byte	28
	.word	1757
	.byte	6,0,27
	.word	1797
	.byte	28
	.word	1838
	.byte	6,0,27
	.word	1857
	.byte	28
	.word	1912
	.byte	6,0,27
	.word	1931
	.byte	28
	.word	1971
	.byte	28
	.word	1988
	.byte	17,6,0,0,27
	.word	9890
	.byte	28
	.word	9922
	.byte	28
	.word	9936
	.byte	28
	.word	9954
	.byte	6,0,27
	.word	10257
	.byte	28
	.word	10290
	.byte	28
	.word	10304
	.byte	28
	.word	10322
	.byte	28
	.word	10336
	.byte	6,0,27
	.word	10456
	.byte	28
	.word	10484
	.byte	28
	.word	10498
	.byte	28
	.word	10516
	.byte	6,0,27
	.word	10534
	.byte	6,0,27
	.word	10913
	.byte	28
	.word	10941
	.byte	6,0,27
	.word	17493
	.byte	28
	.word	17521
	.byte	28
	.word	17537
	.byte	6,0,27
	.word	17677
	.byte	28
	.word	17707
	.byte	28
	.word	17723
	.byte	6,0,27
	.word	17970
	.byte	28
	.word	17999
	.byte	28
	.word	18015
	.byte	6,0,27
	.word	18311
	.byte	28
	.word	18342
	.byte	6,0,27
	.word	21793
	.byte	28
	.word	21816
	.byte	6,0,27
	.word	21831
	.byte	28
	.word	21863
	.byte	17,17,29
	.word	10534
	.byte	30
	.word	10572
	.byte	0,0,6,0,0,27
	.word	21881
	.byte	28
	.word	21909
	.byte	6,0,27
	.word	21924
	.byte	17,29
	.word	1609
	.byte	31
	.word	1646
	.byte	29
	.word	1567
	.byte	30
	.word	1607
	.byte	0,30
	.word	1647
	.byte	0,0,6,0,0,27
	.word	21957
	.byte	28
	.word	21983
	.byte	17,29
	.word	1724
	.byte	28
	.word	1757
	.byte	30
	.word	1774
	.byte	0,6,0,0,27
	.word	22021
	.byte	28
	.word	22045
	.byte	17,29
	.word	22111
	.byte	31
	.word	22127
	.byte	29
	.word	21924
	.byte	31
	.word	21953
	.byte	29
	.word	1609
	.byte	31
	.word	1646
	.byte	29
	.word	1567
	.byte	30
	.word	1607
	.byte	0,30
	.word	1647
	.byte	0,0,30
	.word	21954
	.byte	0,0,30
	.word	22128
	.byte	29
	.word	21957
	.byte	28
	.word	21983
	.byte	31
	.word	22000
	.byte	29
	.word	1724
	.byte	28
	.word	1757
	.byte	30
	.word	1774
	.byte	0,30
	.word	22001
	.byte	0,0,30
	.word	22129
	.byte	29
	.word	21793
	.byte	28
	.word	21816
	.byte	30
	.word	21829
	.byte	0,30
	.word	22130
	.byte	0,0,6,0,0,27
	.word	22066
	.byte	28
	.word	22089
	.byte	17,29
	.word	22111
	.byte	31
	.word	22127
	.byte	29
	.word	21924
	.byte	31
	.word	21953
	.byte	29
	.word	1609
	.byte	31
	.word	1646
	.byte	29
	.word	1567
	.byte	30
	.word	1607
	.byte	0,30
	.word	1647
	.byte	0,0,30
	.word	21954
	.byte	0,0,30
	.word	22128
	.byte	29
	.word	21957
	.byte	28
	.word	21983
	.byte	31
	.word	22000
	.byte	29
	.word	1724
	.byte	28
	.word	1757
	.byte	30
	.word	1774
	.byte	0,30
	.word	22001
	.byte	0,0,30
	.word	22129
	.byte	29
	.word	21793
	.byte	28
	.word	21816
	.byte	30
	.word	21829
	.byte	0,30
	.word	22130
	.byte	0,0,6,0,0,27
	.word	22111
	.byte	17,29
	.word	21924
	.byte	31
	.word	21953
	.byte	29
	.word	1609
	.byte	31
	.word	1646
	.byte	29
	.word	1567
	.byte	30
	.word	1607
	.byte	0,30
	.word	1647
	.byte	0,0,30
	.word	21954
	.byte	0,0,6,29
	.word	21957
	.byte	28
	.word	21983
	.byte	31
	.word	22000
	.byte	29
	.word	1724
	.byte	28
	.word	1757
	.byte	30
	.word	1774
	.byte	0,30
	.word	22001
	.byte	0,0,6,29
	.word	21793
	.byte	28
	.word	21816
	.byte	30
	.word	21829
	.byte	0,6,0,0,27
	.word	22133
	.byte	17,29
	.word	21793
	.byte	28
	.word	21816
	.byte	30
	.word	21829
	.byte	0,6,0,0,15,22,103,9,1,16
	.byte	'UART_0',0,0,16
	.byte	'UART_1',0,1,16
	.byte	'UART_2',0,2,16
	.byte	'UART_3',0,3,0,22
	.word	642
	.byte	32
	.byte	'uart_write_byte',0,22,118,9,1,1,1,1,5
	.byte	'uartn',0,22,118,62
	.word	23975
	.byte	5
	.byte	'dat',0,22,118,81
	.word	24017
	.byte	0,22
	.word	642
.L187:
	.byte	3
	.word	24073
	.byte	32
	.byte	'uart_write_buffer',0,22,119,9,1,1,1,1,5
	.byte	'uartn',0,22,119,62
	.word	23975
	.byte	5
	.byte	'buff',0,22,119,82
	.word	24078
	.byte	5
	.byte	'len',0,22,119,95
	.word	1776
	.byte	0,32
	.byte	'uart_write_string',0,22,120,9,1,1,1,1,5
	.byte	'uartn',0,22,120,62
	.word	23975
	.byte	5
	.byte	'str',0,22,120,81
	.word	22198
	.byte	0,23
	.byte	'uart_read_byte',0,22,122,9
	.word	642
	.byte	1,1,1,1,5
	.byte	'uartn',0,22,122,62
	.word	23975
	.byte	0
.L183:
	.byte	3
	.word	642
	.byte	23
	.byte	'uart_query_byte',0,22,123,9
	.word	642
	.byte	1,1,1,1,5
	.byte	'uartn',0,22,123,62
	.word	23975
	.byte	5
	.byte	'dat',0,22,123,76
	.word	24244
	.byte	0,32
	.byte	'uart_rx_interrupt',0,22,126,9,1,1,1,1,5
	.byte	'uartn',0,22,126,62
	.word	23975
	.byte	5
	.byte	'status',0,22,126,76
	.word	1776
	.byte	0,15,22,43,9,1,16
	.byte	'UART0_TX_P14_0',0,0,16
	.byte	'UART0_TX_P14_1',0,1,16
	.byte	'UART0_TX_P15_2',0,2,16
	.byte	'UART0_TX_P15_3',0,3,16
	.byte	'UART1_TX_P02_2',0,4,16
	.byte	'UART1_TX_P11_12',0,5,16
	.byte	'UART1_TX_P15_0',0,6,16
	.byte	'UART1_TX_P15_1',0,7,16
	.byte	'UART1_TX_P15_4',0,8,16
	.byte	'UART1_TX_P15_5',0,9,16
	.byte	'UART1_TX_P20_10',0,10,16
	.byte	'UART1_TX_P33_12',0,11,16
	.byte	'UART1_TX_P33_13',0,12,16
	.byte	'UART2_TX_P02_0',0,13,16
	.byte	'UART2_TX_P10_5',0,14,16
	.byte	'UART2_TX_P14_2',0,15,16
	.byte	'UART2_TX_P14_3',0,16,16
	.byte	'UART2_TX_P33_8',0,17,16
	.byte	'UART2_TX_P33_9',0,18,16
	.byte	'UART3_TX_P00_0',0,19,16
	.byte	'UART3_TX_P00_1',0,20,16
	.byte	'UART3_TX_P15_6',0,21,16
	.byte	'UART3_TX_P15_7',0,22,16
	.byte	'UART3_TX_P20_0',0,23,16
	.byte	'UART3_TX_P20_3',0,24,16
	.byte	'UART3_TX_P21_7',0,25,0,15,22,77,9,1,16
	.byte	'UART0_RX_P14_1',0,0,16
	.byte	'UART0_RX_P15_3',0,1,16
	.byte	'UART1_RX_P02_3',0,2,16
	.byte	'UART1_RX_P11_10',0,3,16
	.byte	'UART1_RX_P15_1',0,4,16
	.byte	'UART1_RX_P15_5',0,5,16
	.byte	'UART1_RX_P20_9',0,6,16
	.byte	'UART1_RX_P33_13',0,7,16
	.byte	'UART2_RX_P02_0',0,8,16
	.byte	'UART2_RX_P02_1',0,9,16
	.byte	'UART2_RX_P10_6',0,10,16
	.byte	'UART2_RX_P14_3',0,11,16
	.byte	'UART2_RX_P33_8',0,12,16
	.byte	'UART3_RX_P00_1',0,13,16
	.byte	'UART3_RX_P15_7',0,14,16
	.byte	'UART3_RX_P20_3',0,15,16
	.byte	'UART3_RX_P21_6',0,16,0,32
	.byte	'uart_init',0,22,129,1,9,1,1,1,1,5
	.byte	'uartn',0,22,129,1,62
	.word	23975
	.byte	5
	.byte	'baud',0,22,129,1,76
	.word	1776
	.byte	5
	.byte	'tx_pin',0,22,129,1,99
	.word	24360
	.byte	5
	.byte	'rx_pin',0,22,129,1,124
	.word	24812
	.byte	0,33,1,1,34
	.word	22198
	.byte	0,3
	.word	25190
	.byte	33,1,1,34
	.word	659
	.byte	34
	.word	659
	.byte	34
	.word	22198
	.byte	0,3
	.word	25204
.L209:
	.byte	20,23,86,9,20,13
	.byte	'type_index',0
	.word	659
	.byte	2,2,35,0,13
	.byte	'display_x_max',0
	.word	659
	.byte	2,2,35,2,13
	.byte	'display_y_max',0
	.word	659
	.byte	2,2,35,4,13
	.byte	'font_x_size',0
	.word	642
	.byte	1,2,35,6,13
	.byte	'font_y_size',0
	.word	642
	.byte	1,2,35,7,13
	.byte	'output_uart',0
	.word	25199
	.byte	4,2,35,8,13
	.byte	'output_screen',0
	.word	25223
	.byte	4,2,35,12,13
	.byte	'output_screen_clear',0
	.word	206
	.byte	4,2,35,16,0
.L204:
	.byte	3
	.word	25228
.L212:
	.byte	14
	.word	1776
.L214:
	.byte	14
	.word	1776
.L225:
	.byte	14
	.word	659
.L227:
	.byte	14
	.word	659
.L229:
	.byte	14
	.word	483
.L231:
	.byte	14
	.word	18031
.L233:
	.byte	14
	.word	18031
.L236:
	.byte	18,128,2
	.word	22175
	.byte	19,255,1,0
.L238:
	.byte	18,64
	.word	22175
	.byte	19,63,0,35
	.byte	'__INDIRECT__',0,24,1,1,1,1,1,36
	.byte	'__wchar_t',0,24,1,1
	.word	18031
	.byte	36
	.byte	'__size_t',0,24,1,1
	.word	467
	.byte	36
	.byte	'__ptrdiff_t',0,24,1,1
	.word	483
	.byte	37,1,3
	.word	25550
	.byte	36
	.byte	'__codeptr',0,24,1,1
	.word	25552
	.byte	36
	.byte	'__intptr_t',0,24,1,1
	.word	483
	.byte	36
	.byte	'__uintptr_t',0,24,1,1
	.word	467
	.byte	36
	.byte	'size_t',0,18,31,25
	.word	467
	.byte	36
	.byte	'_iob_flag_t',0,18,82,25
	.word	659
	.byte	36
	.byte	'FILE',0,18,96,25
	.word	22253
	.byte	36
	.byte	'boolean',0,25,101,29
	.word	642
	.byte	36
	.byte	'uint8',0,25,105,29
	.word	642
	.byte	36
	.byte	'uint16',0,25,109,29
	.word	659
	.byte	36
	.byte	'uint32',0,25,113,29
	.word	1776
	.byte	36
	.byte	'uint64',0,25,118,29
	.word	349
	.byte	36
	.byte	'sint16',0,25,126,29
	.word	18031
	.byte	36
	.byte	'sint32',0,25,131,1,29
	.word	18044
	.byte	36
	.byte	'sint64',0,25,138,1,29
	.word	22004
	.byte	36
	.byte	'float32',0,25,167,1,29
	.word	263
	.byte	36
	.byte	'pvoid',0,26,57,28
	.word	381
	.byte	36
	.byte	'Ifx_TickTime',0,26,79,28
	.word	22004
	.byte	36
	.byte	'Ifx_SizeT',0,26,92,16
	.word	18031
	.byte	36
	.byte	'Ifx_Priority',0,26,103,16
	.word	659
	.byte	15,26,130,1,9,1,16
	.byte	'Ifx_RxSel_a',0,0,16
	.byte	'Ifx_RxSel_b',0,1,16
	.byte	'Ifx_RxSel_c',0,2,16
	.byte	'Ifx_RxSel_d',0,3,16
	.byte	'Ifx_RxSel_e',0,4,16
	.byte	'Ifx_RxSel_f',0,5,16
	.byte	'Ifx_RxSel_g',0,6,16
	.byte	'Ifx_RxSel_h',0,7,0,36
	.byte	'Ifx_RxSel',0,26,140,1,3
	.word	25875
	.byte	15,26,164,1,9,1,16
	.byte	'Ifx_DataBufferMode_normal',0,0,16
	.byte	'Ifx_DataBufferMode_timeStampSingle',0,1,0,36
	.byte	'Ifx_DataBufferMode',0,26,169,1,2
	.word	26013
	.byte	7
	.byte	'char',0,1,6,36
	.byte	'int8',0,27,54,29
	.word	26113
	.byte	36
	.byte	'int16',0,27,55,29
	.word	18031
	.byte	36
	.byte	'int32',0,27,56,29
	.word	483
	.byte	36
	.byte	'int64',0,27,57,29
	.word	22004
	.byte	36
	.byte	'fifo_state_enum',0,21,53,2
	.word	22465
	.byte	36
	.byte	'fifo_operation_enum',0,21,76,2
	.word	22870
	.byte	36
	.byte	'fifo_data_type_enum',0,21,83,2
	.word	22621
	.byte	36
	.byte	'fifo_struct',0,21,94,2
	.word	22680
	.byte	36
	.byte	'Ifx_ASCLIN_ACCEN0_Bits',0,13,79,3
	.word	16469
	.byte	36
	.byte	'Ifx_ASCLIN_ACCEN1_Bits',0,13,85,3
	.word	16377
	.byte	36
	.byte	'Ifx_ASCLIN_BITCON_Bits',0,13,97,3
	.word	12001
	.byte	36
	.byte	'Ifx_ASCLIN_BRD_Bits',0,13,106,3
	.word	12841
	.byte	36
	.byte	'Ifx_ASCLIN_BRG_Bits',0,13,115,3
	.word	12684
	.byte	36
	.byte	'Ifx_ASCLIN_CLC_Bits',0,13,125,3
	.word	10956
	.byte	36
	.byte	'Ifx_ASCLIN_CSR_Bits',0,13,133,1,3
	.word	15650
	.byte	36
	.byte	'Ifx_ASCLIN_DATCON_Bits',0,13,145,1,3
	.word	12487
	.byte	36
	.byte	'Ifx_ASCLIN_FLAGS_Bits',0,13,177,1,3
	.word	13497
	.byte	36
	.byte	'Ifx_ASCLIN_FLAGSCLEAR_Bits',0,13,209,1,3
	.word	14496
	.byte	36
	.byte	'Ifx_ASCLIN_FLAGSENABLE_Bits',0,13,238,1,3
	.word	15011
	.byte	36
	.byte	'Ifx_ASCLIN_FLAGSSET_Bits',0,13,142,2,3
	.word	13983
	.byte	36
	.byte	'Ifx_ASCLIN_FRAMECON_Bits',0,13,158,2,3
	.word	12222
	.byte	36
	.byte	'Ifx_ASCLIN_ID_Bits',0,13,166,2,3
	.word	11410
	.byte	36
	.byte	'Ifx_ASCLIN_IOCR_Bits',0,13,184,2,3
	.word	11115
	.byte	36
	.byte	'Ifx_ASCLIN_KRST0_Bits',0,13,192,2,3
	.word	16251
	.byte	36
	.byte	'Ifx_ASCLIN_KRST1_Bits',0,13,199,2,3
	.word	16144
	.byte	36
	.byte	'Ifx_ASCLIN_KRSTCLR_Bits',0,13,206,2,3
	.word	16035
	.byte	36
	.byte	'Ifx_ASCLIN_LIN_BTIMER_Bits',0,13,213,2,3
	.word	13195
	.byte	36
	.byte	'Ifx_ASCLIN_LIN_CON_Bits',0,13,225,2,3
	.word	12995
	.byte	36
	.byte	'Ifx_ASCLIN_LIN_HTIMER_Bits',0,13,232,2,3
	.word	13309
	.byte	36
	.byte	'Ifx_ASCLIN_OCS_Bits',0,13,242,2,3
	.word	15872
	.byte	36
	.byte	'Ifx_ASCLIN_RXDATA_Bits',0,13,248,2,3
	.word	15563
	.byte	36
	.byte	'Ifx_ASCLIN_RXDATAD_Bits',0,13,254,2,3
	.word	15773
	.byte	36
	.byte	'Ifx_ASCLIN_RXFIFOCON_Bits',0,13,140,3,3
	.word	11760
	.byte	36
	.byte	'Ifx_ASCLIN_TXDATA_Bits',0,13,146,3,3
	.word	15476
	.byte	36
	.byte	'Ifx_ASCLIN_TXFIFOCON_Bits',0,13,159,3,3
	.word	11535
	.byte	36
	.byte	'Ifx_ASCLIN_ACCEN0',0,13,172,3,3
	.word	17001
	.byte	36
	.byte	'Ifx_ASCLIN_ACCEN1',0,13,180,3,3
	.word	16429
	.byte	36
	.byte	'Ifx_ASCLIN_BITCON',0,13,188,3,3
	.word	12182
	.byte	36
	.byte	'Ifx_ASCLIN_BRD',0,13,196,3,3
	.word	12955
	.byte	36
	.byte	'Ifx_ASCLIN_BRG',0,13,204,3,3
	.word	12801
	.byte	36
	.byte	'Ifx_ASCLIN_CLC',0,13,212,3,3
	.word	11075
	.byte	36
	.byte	'Ifx_ASCLIN_CSR',0,13,220,3,3
	.word	15733
	.byte	36
	.byte	'Ifx_ASCLIN_DATCON',0,13,228,3,3
	.word	12644
	.byte	36
	.byte	'Ifx_ASCLIN_FLAGS',0,13,236,3,3
	.word	13943
	.byte	36
	.byte	'Ifx_ASCLIN_FLAGSCLEAR',0,13,244,3,3
	.word	14971
	.byte	36
	.byte	'Ifx_ASCLIN_FLAGSENABLE',0,13,252,3,3
	.word	15436
	.byte	36
	.byte	'Ifx_ASCLIN_FLAGSSET',0,13,132,4,3
	.word	14456
	.byte	36
	.byte	'Ifx_ASCLIN_FRAMECON',0,13,140,4,3
	.word	12447
	.byte	36
	.byte	'Ifx_ASCLIN_ID',0,13,148,4,3
	.word	11495
	.byte	36
	.byte	'Ifx_ASCLIN_IOCR',0,13,156,4,3
	.word	11370
	.byte	36
	.byte	'Ifx_ASCLIN_KRST0',0,13,164,4,3
	.word	16337
	.byte	36
	.byte	'Ifx_ASCLIN_KRST1',0,13,172,4,3
	.word	16211
	.byte	36
	.byte	'Ifx_ASCLIN_KRSTCLR',0,13,180,4,3
	.word	16104
	.byte	36
	.byte	'Ifx_ASCLIN_LIN_BTIMER',0,13,188,4,3
	.word	13269
	.byte	36
	.byte	'Ifx_ASCLIN_LIN_CON',0,13,196,4,3
	.word	13155
	.byte	36
	.byte	'Ifx_ASCLIN_LIN_HTIMER',0,13,204,4,3
	.word	13384
	.byte	36
	.byte	'Ifx_ASCLIN_OCS',0,13,212,4,3
	.word	15995
	.byte	36
	.byte	'Ifx_ASCLIN_RXDATA',0,13,220,4,3
	.word	15610
	.byte	36
	.byte	'Ifx_ASCLIN_RXDATAD',0,13,228,4,3
	.word	15821
	.byte	36
	.byte	'Ifx_ASCLIN_RXFIFOCON',0,13,236,4,3
	.word	11961
	.byte	36
	.byte	'Ifx_ASCLIN_TXDATA',0,13,244,4,3
	.word	15523
	.byte	36
	.byte	'Ifx_ASCLIN_TXFIFOCON',0,13,252,4,3
	.word	11720
	.byte	14
	.word	13424
	.byte	36
	.byte	'Ifx_ASCLIN_LIN',0,13,140,5,3
	.word	27875
	.byte	14
	.word	17041
	.byte	36
	.byte	'Ifx_ASCLIN',0,13,181,5,3
	.word	27904
	.byte	15,28,236,10,9,1,16
	.byte	'IfxScu_CCUCON0_CLKSEL_fBack',0,0,16
	.byte	'IfxScu_CCUCON0_CLKSEL_fPll',0,1,0,36
	.byte	'IfxScu_CCUCON0_CLKSEL',0,28,240,10,3
	.word	27929
	.byte	15,28,250,10,9,1,16
	.byte	'IfxScu_WDTCON1_IR_divBy16384',0,0,16
	.byte	'IfxScu_WDTCON1_IR_divBy256',0,1,16
	.byte	'IfxScu_WDTCON1_IR_divBy64',0,2,0,36
	.byte	'IfxScu_WDTCON1_IR',0,28,255,10,3
	.word	28026
	.byte	10
	.byte	'_Ifx_SCU_ACCEN0_Bits',0,4,45,16,4,11
	.byte	'EN0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	642
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	642
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	642
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	642
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	642
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	642
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	642
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	642
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	642
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	642
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	642
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	642
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	642
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	642
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	642
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	642
	.byte	1,0,2,35,3,0,36
	.byte	'Ifx_SCU_ACCEN0_Bits',0,4,79,3
	.word	28148
	.byte	10
	.byte	'_Ifx_SCU_ACCEN1_Bits',0,4,82,16,4,11
	.byte	'reserved_0',0,4
	.word	467
	.byte	32,0,2,35,0,0,36
	.byte	'Ifx_SCU_ACCEN1_Bits',0,4,85,3
	.word	28705
	.byte	10
	.byte	'_Ifx_SCU_ARSTDIS_Bits',0,4,88,16,4,11
	.byte	'STM0DIS',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'STM1DIS',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'STM2DIS',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	467
	.byte	29,0,2,35,0,0,36
	.byte	'Ifx_SCU_ARSTDIS_Bits',0,4,94,3
	.word	28782
	.byte	10
	.byte	'_Ifx_SCU_CCUCON0_Bits',0,4,97,16,4,11
	.byte	'BAUD1DIV',0,1
	.word	642
	.byte	4,4,2,35,0,11
	.byte	'BAUD2DIV',0,1
	.word	642
	.byte	4,0,2,35,0,11
	.byte	'SRIDIV',0,1
	.word	642
	.byte	4,4,2,35,1,11
	.byte	'LPDIV',0,1
	.word	642
	.byte	4,0,2,35,1,11
	.byte	'SPBDIV',0,1
	.word	642
	.byte	4,4,2,35,2,11
	.byte	'FSI2DIV',0,1
	.word	642
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	642
	.byte	2,0,2,35,2,11
	.byte	'FSIDIV',0,1
	.word	642
	.byte	2,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	642
	.byte	2,4,2,35,3,11
	.byte	'CLKSEL',0,1
	.word	642
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,36
	.byte	'Ifx_SCU_CCUCON0_Bits',0,4,111,3
	.word	28918
	.byte	10
	.byte	'_Ifx_SCU_CCUCON1_Bits',0,4,114,16,4,11
	.byte	'CANDIV',0,1
	.word	642
	.byte	4,4,2,35,0,11
	.byte	'ERAYDIV',0,1
	.word	642
	.byte	4,0,2,35,0,11
	.byte	'STMDIV',0,1
	.word	642
	.byte	4,4,2,35,1,11
	.byte	'GTMDIV',0,1
	.word	642
	.byte	4,0,2,35,1,11
	.byte	'ETHDIV',0,1
	.word	642
	.byte	4,4,2,35,2,11
	.byte	'ASCLINFDIV',0,1
	.word	642
	.byte	4,0,2,35,2,11
	.byte	'ASCLINSDIV',0,1
	.word	642
	.byte	4,4,2,35,3,11
	.byte	'INSEL',0,1
	.word	642
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,36
	.byte	'Ifx_SCU_CCUCON1_Bits',0,4,126,3
	.word	29198
	.byte	10
	.byte	'_Ifx_SCU_CCUCON2_Bits',0,4,129,1,16,4,11
	.byte	'BBBDIV',0,1
	.word	642
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	467
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,36
	.byte	'Ifx_SCU_CCUCON2_Bits',0,4,135,1,3
	.word	29436
	.byte	10
	.byte	'_Ifx_SCU_CCUCON3_Bits',0,4,138,1,16,4,11
	.byte	'PLLDIV',0,1
	.word	642
	.byte	6,2,2,35,0,11
	.byte	'PLLSEL',0,1
	.word	642
	.byte	2,0,2,35,0,11
	.byte	'PLLERAYDIV',0,1
	.word	642
	.byte	6,2,2,35,1,11
	.byte	'PLLERAYSEL',0,1
	.word	642
	.byte	2,0,2,35,1,11
	.byte	'SRIDIV',0,1
	.word	642
	.byte	6,2,2,35,2,11
	.byte	'SRISEL',0,1
	.word	642
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	642
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	642
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,36
	.byte	'Ifx_SCU_CCUCON3_Bits',0,4,150,1,3
	.word	29564
	.byte	10
	.byte	'_Ifx_SCU_CCUCON4_Bits',0,4,153,1,16,4,11
	.byte	'SPBDIV',0,1
	.word	642
	.byte	6,2,2,35,0,11
	.byte	'SPBSEL',0,1
	.word	642
	.byte	2,0,2,35,0,11
	.byte	'GTMDIV',0,1
	.word	642
	.byte	6,2,2,35,1,11
	.byte	'GTMSEL',0,1
	.word	642
	.byte	2,0,2,35,1,11
	.byte	'STMDIV',0,1
	.word	642
	.byte	6,2,2,35,2,11
	.byte	'STMSEL',0,1
	.word	642
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	642
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	642
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,36
	.byte	'Ifx_SCU_CCUCON4_Bits',0,4,165,1,3
	.word	29807
	.byte	10
	.byte	'_Ifx_SCU_CCUCON5_Bits',0,4,168,1,16,4,11
	.byte	'MAXDIV',0,1
	.word	642
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	467
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,36
	.byte	'Ifx_SCU_CCUCON5_Bits',0,4,174,1,3
	.word	30042
	.byte	10
	.byte	'_Ifx_SCU_CCUCON6_Bits',0,4,177,1,16,4,11
	.byte	'CPU0DIV',0,1
	.word	642
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	467
	.byte	26,0,2,35,0,0,36
	.byte	'Ifx_SCU_CCUCON6_Bits',0,4,181,1,3
	.word	30170
	.byte	10
	.byte	'_Ifx_SCU_CCUCON7_Bits',0,4,184,1,16,4,11
	.byte	'CPU1DIV',0,1
	.word	642
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	467
	.byte	26,0,2,35,0,0,36
	.byte	'Ifx_SCU_CCUCON7_Bits',0,4,188,1,3
	.word	30270
	.byte	10
	.byte	'_Ifx_SCU_CHIPID_Bits',0,4,191,1,16,4,11
	.byte	'CHREV',0,1
	.word	642
	.byte	6,2,2,35,0,11
	.byte	'CHTEC',0,1
	.word	642
	.byte	2,0,2,35,0,11
	.byte	'CHID',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'EEA',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'UCODE',0,1
	.word	642
	.byte	7,0,2,35,2,11
	.byte	'FSIZE',0,1
	.word	642
	.byte	4,4,2,35,3,11
	.byte	'SP',0,1
	.word	642
	.byte	2,2,2,35,3,11
	.byte	'SEC',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	642
	.byte	1,0,2,35,3,0,36
	.byte	'Ifx_SCU_CHIPID_Bits',0,4,202,1,3
	.word	30370
	.byte	10
	.byte	'_Ifx_SCU_DTSCON_Bits',0,4,205,1,16,4,11
	.byte	'PWD',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'START',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	642
	.byte	2,4,2,35,0,11
	.byte	'CAL',0,4
	.word	467
	.byte	20,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	642
	.byte	7,1,2,35,3,11
	.byte	'SLCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,36
	.byte	'Ifx_SCU_DTSCON_Bits',0,4,213,1,3
	.word	30578
	.byte	10
	.byte	'_Ifx_SCU_DTSLIM_Bits',0,4,216,1,16,4,11
	.byte	'LOWER',0,2
	.word	659
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	642
	.byte	5,1,2,35,1,11
	.byte	'LLU',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'UPPER',0,2
	.word	659
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	642
	.byte	4,2,2,35,3,11
	.byte	'SLCK',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'UOF',0,1
	.word	642
	.byte	1,0,2,35,3,0,36
	.byte	'Ifx_SCU_DTSLIM_Bits',0,4,225,1,3
	.word	30743
	.byte	10
	.byte	'_Ifx_SCU_DTSSTAT_Bits',0,4,228,1,16,4,11
	.byte	'RESULT',0,2
	.word	659
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	642
	.byte	4,2,2,35,1,11
	.byte	'RDY',0,1
	.word	642
	.byte	1,1,2,35,1,11
	.byte	'BUSY',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	659
	.byte	16,0,2,35,2,0,36
	.byte	'Ifx_SCU_DTSSTAT_Bits',0,4,235,1,3
	.word	30926
	.byte	10
	.byte	'_Ifx_SCU_EICR_Bits',0,4,238,1,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	4,4,2,35,0,11
	.byte	'EXIS0',0,1
	.word	642
	.byte	3,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'FEN0',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'REN0',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'LDEN0',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'EIEN0',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'INP0',0,1
	.word	642
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	467
	.byte	5,12,2,35,0,11
	.byte	'EXIS1',0,1
	.word	642
	.byte	3,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	642
	.byte	1,0,2,35,2,11
	.byte	'FEN1',0,1
	.word	642
	.byte	1,7,2,35,3,11
	.byte	'REN1',0,1
	.word	642
	.byte	1,6,2,35,3,11
	.byte	'LDEN1',0,1
	.word	642
	.byte	1,5,2,35,3,11
	.byte	'EIEN1',0,1
	.word	642
	.byte	1,4,2,35,3,11
	.byte	'INP1',0,1
	.word	642
	.byte	3,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	642
	.byte	1,0,2,35,3,0,36
	.byte	'Ifx_SCU_EICR_Bits',0,4,129,2,3
	.word	31080
	.byte	10
	.byte	'_Ifx_SCU_EIFR_Bits',0,4,132,2,16,4,11
	.byte	'INTF0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'INTF1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'INTF2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'INTF3',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'INTF4',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'INTF5',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'INTF6',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'INTF7',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	467
	.byte	24,0,2,35,0,0,36
	.byte	'Ifx_SCU_EIFR_Bits',0,4,143,2,3
	.word	31444
	.byte	10
	.byte	'_Ifx_SCU_EMSR_Bits',0,4,146,2,16,4,11
	.byte	'POL',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'MODE',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'ENON',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'PSEL',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	659
	.byte	12,0,2,35,0,11
	.byte	'EMSF',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'SEMSF',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	642
	.byte	6,0,2,35,2,11
	.byte	'EMSFM',0,1
	.word	642
	.byte	2,6,2,35,3,11
	.byte	'SEMSFM',0,1
	.word	642
	.byte	2,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	642
	.byte	4,0,2,35,3,0,36
	.byte	'Ifx_SCU_EMSR_Bits',0,4,159,2,3
	.word	31655
	.byte	10
	.byte	'_Ifx_SCU_ESRCFG_Bits',0,4,162,2,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	7,1,2,35,0,11
	.byte	'EDCON',0,2
	.word	659
	.byte	2,7,2,35,0,11
	.byte	'reserved_9',0,4
	.word	467
	.byte	23,0,2,35,0,0,36
	.byte	'Ifx_SCU_ESRCFG_Bits',0,4,167,2,3
	.word	31907
	.byte	10
	.byte	'_Ifx_SCU_ESROCFG_Bits',0,4,170,2,16,4,11
	.byte	'ARI',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'ARC',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	467
	.byte	30,0,2,35,0,0,36
	.byte	'Ifx_SCU_ESROCFG_Bits',0,4,175,2,3
	.word	32025
	.byte	10
	.byte	'_Ifx_SCU_EVR13CON_Bits',0,4,178,2,16,4,11
	.byte	'reserved_0',0,4
	.word	467
	.byte	28,4,2,35,0,11
	.byte	'EVR13OFF',0,1
	.word	642
	.byte	1,3,2,35,3,11
	.byte	'BPEVR13OFF',0,1
	.word	642
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,36
	.byte	'Ifx_SCU_EVR13CON_Bits',0,4,185,2,3
	.word	32136
	.byte	10
	.byte	'_Ifx_SCU_EVR33CON_Bits',0,4,188,2,16,4,11
	.byte	'reserved_0',0,4
	.word	467
	.byte	28,4,2,35,0,11
	.byte	'EVR33OFF',0,1
	.word	642
	.byte	1,3,2,35,3,11
	.byte	'BPEVR33OFF',0,1
	.word	642
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,36
	.byte	'Ifx_SCU_EVR33CON_Bits',0,4,195,2,3
	.word	32299
	.byte	10
	.byte	'_Ifx_SCU_EVRADCSTAT_Bits',0,4,198,2,16,4,11
	.byte	'ADC13V',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'ADC33V',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'ADCSWDV',0,1
	.word	642
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	642
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	642
	.byte	1,0,2,35,3,0,36
	.byte	'Ifx_SCU_EVRADCSTAT_Bits',0,4,205,2,3
	.word	32462
	.byte	10
	.byte	'_Ifx_SCU_EVRDVSTAT_Bits',0,4,208,2,16,4,11
	.byte	'DVS13TRIM',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'DVS33TRIM',0,1
	.word	642
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	642
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	642
	.byte	1,0,2,35,3,0,36
	.byte	'Ifx_SCU_EVRDVSTAT_Bits',0,4,215,2,3
	.word	32620
	.byte	10
	.byte	'_Ifx_SCU_EVRMONCTRL_Bits',0,4,218,2,16,4,11
	.byte	'EVR13OVMOD',0,1
	.word	642
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	642
	.byte	2,4,2,35,0,11
	.byte	'EVR13UVMOD',0,1
	.word	642
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	642
	.byte	2,0,2,35,0,11
	.byte	'EVR33OVMOD',0,1
	.word	642
	.byte	2,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	642
	.byte	2,4,2,35,1,11
	.byte	'EVR33UVMOD',0,1
	.word	642
	.byte	2,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	642
	.byte	2,0,2,35,1,11
	.byte	'SWDOVMOD',0,1
	.word	642
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	642
	.byte	2,4,2,35,2,11
	.byte	'SWDUVMOD',0,1
	.word	642
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,2
	.word	659
	.byte	10,0,2,35,2,0,36
	.byte	'Ifx_SCU_EVRMONCTRL_Bits',0,4,232,2,3
	.word	32785
	.byte	10
	.byte	'_Ifx_SCU_EVROSCCTRL_Bits',0,4,235,2,16,4,11
	.byte	'OSCTRIM',0,2
	.word	659
	.byte	10,6,2,35,0,11
	.byte	'OSCPTAT',0,1
	.word	642
	.byte	6,0,2,35,1,11
	.byte	'OSCANASEL',0,1
	.word	642
	.byte	4,4,2,35,2,11
	.byte	'HPBGTRIM',0,2
	.word	659
	.byte	7,5,2,35,2,11
	.byte	'HPBGCLKEN',0,1
	.word	642
	.byte	1,4,2,35,3,11
	.byte	'OSC3V3',0,1
	.word	642
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	642
	.byte	2,1,2,35,3,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,36
	.byte	'Ifx_SCU_EVROSCCTRL_Bits',0,4,245,2,3
	.word	33114
	.byte	10
	.byte	'_Ifx_SCU_EVROVMON_Bits',0,4,248,2,16,4,11
	.byte	'EVR13OVVAL',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'EVR33OVVAL',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'SWDOVVAL',0,1
	.word	642
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	642
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,36
	.byte	'Ifx_SCU_EVROVMON_Bits',0,4,255,2,3
	.word	33335
	.byte	10
	.byte	'_Ifx_SCU_EVRRSTCON_Bits',0,4,130,3,16,4,11
	.byte	'RST13TRIM',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	467
	.byte	16,8,2,35,0,11
	.byte	'RST13OFF',0,1
	.word	642
	.byte	1,7,2,35,3,11
	.byte	'BPRST13OFF',0,1
	.word	642
	.byte	1,6,2,35,3,11
	.byte	'RST33OFF',0,1
	.word	642
	.byte	1,5,2,35,3,11
	.byte	'BPRST33OFF',0,1
	.word	642
	.byte	1,4,2,35,3,11
	.byte	'RSTSWDOFF',0,1
	.word	642
	.byte	1,3,2,35,3,11
	.byte	'BPRSTSWDOFF',0,1
	.word	642
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,36
	.byte	'Ifx_SCU_EVRRSTCON_Bits',0,4,142,3,3
	.word	33498
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF1_Bits',0,4,145,3,16,4,11
	.byte	'SD5P',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'SD5I',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'SD5D',0,1
	.word	642
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	642
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,36
	.byte	'Ifx_SCU_EVRSDCOEFF1_Bits',0,4,152,3,3
	.word	33770
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF2_Bits',0,4,155,3,16,4,11
	.byte	'SD33P',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'SD33I',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'SD33D',0,1
	.word	642
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	642
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,36
	.byte	'Ifx_SCU_EVRSDCOEFF2_Bits',0,4,162,3,3
	.word	33923
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF3_Bits',0,4,165,3,16,4,11
	.byte	'CT5REG0',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'CT5REG1',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'CT5REG2',0,1
	.word	642
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	642
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,36
	.byte	'Ifx_SCU_EVRSDCOEFF3_Bits',0,4,172,3,3
	.word	34079
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF4_Bits',0,4,175,3,16,4,11
	.byte	'CT5REG3',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'CT5REG4',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	659
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,36
	.byte	'Ifx_SCU_EVRSDCOEFF4_Bits',0,4,181,3,3
	.word	34241
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF5_Bits',0,4,184,3,16,4,11
	.byte	'CT33REG0',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'CT33REG1',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'CT33REG2',0,1
	.word	642
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	642
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,36
	.byte	'Ifx_SCU_EVRSDCOEFF5_Bits',0,4,191,3,3
	.word	34384
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF6_Bits',0,4,194,3,16,4,11
	.byte	'CT33REG3',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'CT33REG4',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	659
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,36
	.byte	'Ifx_SCU_EVRSDCOEFF6_Bits',0,4,200,3,3
	.word	34549
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL1_Bits',0,4,203,3,16,4,11
	.byte	'SDFREQSPRD',0,2
	.word	659
	.byte	16,0,2,35,0,11
	.byte	'SDFREQ',0,1
	.word	642
	.byte	8,0,2,35,2,11
	.byte	'SDSTEP',0,1
	.word	642
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	642
	.byte	2,2,2,35,3,11
	.byte	'SDSAMPLE',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,36
	.byte	'Ifx_SCU_EVRSDCTRL1_Bits',0,4,211,3,3
	.word	34694
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL2_Bits',0,4,214,3,16,4,11
	.byte	'DRVP',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'SDMINMAXDC',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'DRVN',0,1
	.word	642
	.byte	8,0,2,35,2,11
	.byte	'SDLUT',0,1
	.word	642
	.byte	6,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,36
	.byte	'Ifx_SCU_EVRSDCTRL2_Bits',0,4,222,3,3
	.word	34875
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL3_Bits',0,4,225,3,16,4,11
	.byte	'SDPWMPRE',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'SDPID',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'SDVOKLVL',0,1
	.word	642
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	642
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,36
	.byte	'Ifx_SCU_EVRSDCTRL3_Bits',0,4,232,3,3
	.word	35049
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL4_Bits',0,4,235,3,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'SYNCDIV',0,1
	.word	642
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	467
	.byte	20,1,2,35,0,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,36
	.byte	'Ifx_SCU_EVRSDCTRL4_Bits',0,4,241,3,3
	.word	35209
	.byte	10
	.byte	'_Ifx_SCU_EVRSTAT_Bits',0,4,244,3,16,4,11
	.byte	'EVR13',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'OV13',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'EVR33',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'OV33',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'OVSWD',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'UV13',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'UV33',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'UVSWD',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'EXTPASS13',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'EXTPASS33',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'BGPROK',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	467
	.byte	21,0,2,35,0,0,36
	.byte	'Ifx_SCU_EVRSTAT_Bits',0,4,130,4,3
	.word	35353
	.byte	10
	.byte	'_Ifx_SCU_EVRTRIM_Bits',0,4,133,4,16,4,11
	.byte	'EVR13TRIM',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'SDVOUTSEL',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	659
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,36
	.byte	'Ifx_SCU_EVRTRIM_Bits',0,4,139,4,3
	.word	35627
	.byte	10
	.byte	'_Ifx_SCU_EVRUVMON_Bits',0,4,142,4,16,4,11
	.byte	'EVR13UVVAL',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'EVR33UVVAL',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'SWDUVVAL',0,1
	.word	642
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	642
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,36
	.byte	'Ifx_SCU_EVRUVMON_Bits',0,4,149,4,3
	.word	35766
	.byte	10
	.byte	'_Ifx_SCU_EXTCON_Bits',0,4,152,4,16,4,11
	.byte	'EN0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'SEL0',0,1
	.word	642
	.byte	4,2,2,35,0,11
	.byte	'reserved_6',0,2
	.word	659
	.byte	10,0,2,35,0,11
	.byte	'EN1',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'NSEL',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'SEL1',0,1
	.word	642
	.byte	4,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	642
	.byte	2,0,2,35,2,11
	.byte	'DIV1',0,1
	.word	642
	.byte	8,0,2,35,3,0,36
	.byte	'Ifx_SCU_EXTCON_Bits',0,4,163,4,3
	.word	35929
	.byte	10
	.byte	'_Ifx_SCU_FDR_Bits',0,4,166,4,16,4,11
	.byte	'STEP',0,2
	.word	659
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	642
	.byte	4,2,2,35,1,11
	.byte	'DM',0,1
	.word	642
	.byte	2,0,2,35,1,11
	.byte	'RESULT',0,2
	.word	659
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	642
	.byte	5,1,2,35,3,11
	.byte	'DISCLK',0,1
	.word	642
	.byte	1,0,2,35,3,0,36
	.byte	'Ifx_SCU_FDR_Bits',0,4,174,4,3
	.word	36147
	.byte	10
	.byte	'_Ifx_SCU_FMR_Bits',0,4,177,4,16,4,11
	.byte	'FS0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'FS1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'FS2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'FS3',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'FS4',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'FS5',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'FS6',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'FS7',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'FC0',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'FC1',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'FC2',0,1
	.word	642
	.byte	1,5,2,35,2,11
	.byte	'FC3',0,1
	.word	642
	.byte	1,4,2,35,2,11
	.byte	'FC4',0,1
	.word	642
	.byte	1,3,2,35,2,11
	.byte	'FC5',0,1
	.word	642
	.byte	1,2,2,35,2,11
	.byte	'FC6',0,1
	.word	642
	.byte	1,1,2,35,2,11
	.byte	'FC7',0,1
	.word	642
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	642
	.byte	8,0,2,35,3,0,36
	.byte	'Ifx_SCU_FMR_Bits',0,4,197,4,3
	.word	36310
	.byte	10
	.byte	'_Ifx_SCU_ID_Bits',0,4,200,4,16,4,11
	.byte	'MODREV',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	659
	.byte	16,0,2,35,2,0,36
	.byte	'Ifx_SCU_ID_Bits',0,4,205,4,3
	.word	36646
	.byte	10
	.byte	'_Ifx_SCU_IGCR_Bits',0,4,208,4,16,4,11
	.byte	'IPEN00',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'IPEN01',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'IPEN02',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'IPEN03',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'IPEN04',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'IPEN05',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'IPEN06',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'IPEN07',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	642
	.byte	5,3,2,35,1,11
	.byte	'GEEN0',0,1
	.word	642
	.byte	1,2,2,35,1,11
	.byte	'IGP0',0,1
	.word	642
	.byte	2,0,2,35,1,11
	.byte	'IPEN10',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'IPEN11',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'IPEN12',0,1
	.word	642
	.byte	1,5,2,35,2,11
	.byte	'IPEN13',0,1
	.word	642
	.byte	1,4,2,35,2,11
	.byte	'IPEN14',0,1
	.word	642
	.byte	1,3,2,35,2,11
	.byte	'IPEN15',0,1
	.word	642
	.byte	1,2,2,35,2,11
	.byte	'IPEN16',0,1
	.word	642
	.byte	1,1,2,35,2,11
	.byte	'IPEN17',0,1
	.word	642
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	642
	.byte	5,3,2,35,3,11
	.byte	'GEEN1',0,1
	.word	642
	.byte	1,2,2,35,3,11
	.byte	'IGP1',0,1
	.word	642
	.byte	2,0,2,35,3,0,36
	.byte	'Ifx_SCU_IGCR_Bits',0,4,232,4,3
	.word	36753
	.byte	10
	.byte	'_Ifx_SCU_IN_Bits',0,4,235,4,16,4,11
	.byte	'P0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	467
	.byte	30,0,2,35,0,0,36
	.byte	'Ifx_SCU_IN_Bits',0,4,240,4,3
	.word	37205
	.byte	10
	.byte	'_Ifx_SCU_IOCR_Bits',0,4,243,4,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	4,4,2,35,0,11
	.byte	'PC0',0,1
	.word	642
	.byte	4,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	642
	.byte	4,4,2,35,1,11
	.byte	'PC1',0,1
	.word	642
	.byte	4,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	659
	.byte	16,0,2,35,2,0,36
	.byte	'Ifx_SCU_IOCR_Bits',0,4,250,4,3
	.word	37304
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL0_Bits',0,4,253,4,16,4,11
	.byte	'LBISTREQ',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'LBISTREQP',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'PATTERNS',0,2
	.word	659
	.byte	14,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	659
	.byte	16,0,2,35,2,0,36
	.byte	'Ifx_SCU_LBISTCTRL0_Bits',0,4,131,5,3
	.word	37454
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL1_Bits',0,4,134,5,16,4,11
	.byte	'SEED',0,4
	.word	467
	.byte	23,9,2,35,0,11
	.byte	'reserved_23',0,1
	.word	642
	.byte	1,0,2,35,2,11
	.byte	'SPLITSH',0,1
	.word	642
	.byte	3,5,2,35,3,11
	.byte	'BODY',0,1
	.word	642
	.byte	1,4,2,35,3,11
	.byte	'LBISTFREQU',0,1
	.word	642
	.byte	4,0,2,35,3,0,36
	.byte	'Ifx_SCU_LBISTCTRL1_Bits',0,4,141,5,3
	.word	37603
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL2_Bits',0,4,144,5,16,4,11
	.byte	'SIGNATURE',0,4
	.word	467
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	642
	.byte	7,1,2,35,3,11
	.byte	'LBISTDONE',0,1
	.word	642
	.byte	1,0,2,35,3,0,36
	.byte	'Ifx_SCU_LBISTCTRL2_Bits',0,4,149,5,3
	.word	37764
	.byte	10
	.byte	'_Ifx_SCU_LCLCON_Bits',0,4,152,5,16,4,11
	.byte	'reserved_0',0,2
	.word	659
	.byte	16,0,2,35,0,11
	.byte	'LS',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,2
	.word	659
	.byte	14,1,2,35,2,11
	.byte	'LSEN',0,1
	.word	642
	.byte	1,0,2,35,3,0,36
	.byte	'Ifx_SCU_LCLCON_Bits',0,4,158,5,3
	.word	37894
	.byte	10
	.byte	'_Ifx_SCU_LCLTEST_Bits',0,4,161,5,16,4,11
	.byte	'LCLT0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'LCLT1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	467
	.byte	30,0,2,35,0,0,36
	.byte	'Ifx_SCU_LCLTEST_Bits',0,4,166,5,3
	.word	38026
	.byte	10
	.byte	'_Ifx_SCU_MANID_Bits',0,4,169,5,16,4,11
	.byte	'DEPT',0,1
	.word	642
	.byte	5,3,2,35,0,11
	.byte	'MANUF',0,2
	.word	659
	.byte	11,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	659
	.byte	16,0,2,35,2,0,36
	.byte	'Ifx_SCU_MANID_Bits',0,4,174,5,3
	.word	38141
	.byte	10
	.byte	'_Ifx_SCU_OMR_Bits',0,4,177,5,16,4,11
	.byte	'PS0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	659
	.byte	14,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	659
	.byte	14,0,2,35,2,0,36
	.byte	'Ifx_SCU_OMR_Bits',0,4,185,5,3
	.word	38252
	.byte	10
	.byte	'_Ifx_SCU_OSCCON_Bits',0,4,188,5,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'PLLLV',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'OSCRES',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'GAINSEL',0,1
	.word	642
	.byte	2,3,2,35,0,11
	.byte	'MODE',0,1
	.word	642
	.byte	2,1,2,35,0,11
	.byte	'SHBY',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'PLLHV',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'X1D',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'X1DEN',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	642
	.byte	4,0,2,35,1,11
	.byte	'OSCVAL',0,1
	.word	642
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	642
	.byte	2,1,2,35,2,11
	.byte	'APREN',0,1
	.word	642
	.byte	1,0,2,35,2,11
	.byte	'CAP0EN',0,1
	.word	642
	.byte	1,7,2,35,3,11
	.byte	'CAP1EN',0,1
	.word	642
	.byte	1,6,2,35,3,11
	.byte	'CAP2EN',0,1
	.word	642
	.byte	1,5,2,35,3,11
	.byte	'CAP3EN',0,1
	.word	642
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	642
	.byte	4,0,2,35,3,0,36
	.byte	'Ifx_SCU_OSCCON_Bits',0,4,209,5,3
	.word	38410
	.byte	10
	.byte	'_Ifx_SCU_OUT_Bits',0,4,212,5,16,4,11
	.byte	'P0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	467
	.byte	30,0,2,35,0,0,36
	.byte	'Ifx_SCU_OUT_Bits',0,4,217,5,3
	.word	38822
	.byte	10
	.byte	'_Ifx_SCU_OVCCON_Bits',0,4,220,5,16,4,11
	.byte	'CSEL0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'CSEL1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'CSEL2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,2
	.word	659
	.byte	13,0,2,35,0,11
	.byte	'OVSTRT',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'OVSTP',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'DCINVAL',0,1
	.word	642
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	642
	.byte	5,0,2,35,2,11
	.byte	'OVCONF',0,1
	.word	642
	.byte	1,7,2,35,3,11
	.byte	'POVCONF',0,1
	.word	642
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	642
	.byte	6,0,2,35,3,0,36
	.byte	'Ifx_SCU_OVCCON_Bits',0,4,233,5,3
	.word	38923
	.byte	10
	.byte	'_Ifx_SCU_OVCENABLE_Bits',0,4,236,5,16,4,11
	.byte	'OVEN0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'OVEN1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'OVEN2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	467
	.byte	29,0,2,35,0,0,36
	.byte	'Ifx_SCU_OVCENABLE_Bits',0,4,242,5,3
	.word	39190
	.byte	10
	.byte	'_Ifx_SCU_PDISC_Bits',0,4,245,5,16,4,11
	.byte	'PDIS0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	467
	.byte	30,0,2,35,0,0,36
	.byte	'Ifx_SCU_PDISC_Bits',0,4,250,5,3
	.word	39326
	.byte	10
	.byte	'_Ifx_SCU_PDR_Bits',0,4,253,5,16,4,11
	.byte	'PD0',0,1
	.word	642
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	642
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	467
	.byte	24,0,2,35,0,0,36
	.byte	'Ifx_SCU_PDR_Bits',0,4,132,6,3
	.word	39437
	.byte	10
	.byte	'_Ifx_SCU_PDRR_Bits',0,4,135,6,16,4,11
	.byte	'PDR0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'PDR1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'PDR2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'PDR3',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'PDR4',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'PDR5',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'PDR6',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'PDR7',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	467
	.byte	24,0,2,35,0,0,36
	.byte	'Ifx_SCU_PDRR_Bits',0,4,146,6,3
	.word	39570
	.byte	10
	.byte	'_Ifx_SCU_PLLCON0_Bits',0,4,149,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'MODEN',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	659
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	642
	.byte	7,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	642
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	642
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	642
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	642
	.byte	4,0,2,35,3,0,36
	.byte	'Ifx_SCU_PLLCON0_Bits',0,4,166,6,3
	.word	39773
	.byte	10
	.byte	'_Ifx_SCU_PLLCON1_Bits',0,4,169,6,16,4,11
	.byte	'K2DIV',0,1
	.word	642
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	642
	.byte	7,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	642
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	659
	.byte	9,0,2,35,2,0,36
	.byte	'Ifx_SCU_PLLCON1_Bits',0,4,177,6,3
	.word	40129
	.byte	10
	.byte	'_Ifx_SCU_PLLCON2_Bits',0,4,180,6,16,4,11
	.byte	'MODCFG',0,2
	.word	659
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	659
	.byte	16,0,2,35,2,0,36
	.byte	'Ifx_SCU_PLLCON2_Bits',0,4,184,6,3
	.word	40307
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON0_Bits',0,4,187,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	642
	.byte	2,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	659
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	642
	.byte	5,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	642
	.byte	2,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	642
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	642
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	642
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	642
	.byte	4,0,2,35,3,0,36
	.byte	'Ifx_SCU_PLLERAYCON0_Bits',0,4,204,6,3
	.word	40407
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON1_Bits',0,4,207,6,16,4,11
	.byte	'K2DIV',0,1
	.word	642
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	642
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	642
	.byte	4,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	642
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	659
	.byte	9,0,2,35,2,0,36
	.byte	'Ifx_SCU_PLLERAYCON1_Bits',0,4,215,6,3
	.word	40777
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYSTAT_Bits',0,4,218,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'PWDSTAT',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	467
	.byte	26,0,2,35,0,0,36
	.byte	'Ifx_SCU_PLLERAYSTAT_Bits',0,4,227,6,3
	.word	40963
	.byte	10
	.byte	'_Ifx_SCU_PLLSTAT_Bits',0,4,230,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'MODRUN',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	467
	.byte	24,0,2,35,0,0,36
	.byte	'Ifx_SCU_PLLSTAT_Bits',0,4,241,6,3
	.word	41161
	.byte	10
	.byte	'_Ifx_SCU_PMCSR_Bits',0,4,244,6,16,4,11
	.byte	'REQSLP',0,1
	.word	642
	.byte	2,6,2,35,0,11
	.byte	'SMUSLP',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	642
	.byte	5,0,2,35,0,11
	.byte	'PMST',0,1
	.word	642
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	467
	.byte	21,0,2,35,0,0,36
	.byte	'Ifx_SCU_PMCSR_Bits',0,4,251,6,3
	.word	41394
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR0_Bits',0,4,254,6,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'ESR1WKEN',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'PINAWKEN',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'PINBWKEN',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'ESR0DFEN',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'ESR0EDCON',0,1
	.word	642
	.byte	2,1,2,35,0,11
	.byte	'ESR1DFEN',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'ESR1EDCON',0,1
	.word	642
	.byte	2,6,2,35,1,11
	.byte	'PINADFEN',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'PINAEDCON',0,1
	.word	642
	.byte	2,3,2,35,1,11
	.byte	'PINBDFEN',0,1
	.word	642
	.byte	1,2,2,35,1,11
	.byte	'PINBEDCON',0,1
	.word	642
	.byte	2,0,2,35,1,11
	.byte	'SCREN',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'STBYRAMSEL',0,1
	.word	642
	.byte	2,5,2,35,2,11
	.byte	'SCRCLKSEL',0,1
	.word	642
	.byte	1,4,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	642
	.byte	1,3,2,35,2,11
	.byte	'TRISTEN',0,1
	.word	642
	.byte	1,2,2,35,2,11
	.byte	'TRISTREQ',0,1
	.word	642
	.byte	1,1,2,35,2,11
	.byte	'PORSTDF',0,1
	.word	642
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	642
	.byte	1,7,2,35,3,11
	.byte	'DCDCSYNC',0,1
	.word	642
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	642
	.byte	3,3,2,35,3,11
	.byte	'ESR0TRIST',0,1
	.word	642
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,36
	.byte	'Ifx_SCU_PMSWCR0_Bits',0,4,153,7,3
	.word	41546
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR1_Bits',0,4,156,7,16,4,11
	.byte	'SCRSTEN',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'SCRSTREQ',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	642
	.byte	6,0,2,35,0,11
	.byte	'CPUIDLSEL',0,1
	.word	642
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'IRADIS',0,1
	.word	642
	.byte	1,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	642
	.byte	3,0,2,35,1,11
	.byte	'SCRCFG',0,1
	.word	642
	.byte	8,0,2,35,2,11
	.byte	'CPUSEL',0,1
	.word	642
	.byte	3,5,2,35,3,11
	.byte	'STBYEVEN',0,1
	.word	642
	.byte	1,4,2,35,3,11
	.byte	'STBYEV',0,1
	.word	642
	.byte	3,1,2,35,3,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,36
	.byte	'Ifx_SCU_PMSWCR1_Bits',0,4,170,7,3
	.word	42113
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR2_Bits',0,4,173,7,16,4,11
	.byte	'SCRINT',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'BUSY',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'SCRECC',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'SCRWDT',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'SCRRST',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	642
	.byte	4,0,2,35,1,11
	.byte	'TCINT',0,1
	.word	642
	.byte	8,0,2,35,2,11
	.byte	'TCINTREQ',0,1
	.word	642
	.byte	1,7,2,35,3,11
	.byte	'SMURST',0,1
	.word	642
	.byte	1,6,2,35,3,11
	.byte	'RST',0,1
	.word	642
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	642
	.byte	4,1,2,35,3,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,36
	.byte	'Ifx_SCU_PMSWCR2_Bits',0,4,187,7,3
	.word	42407
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTAT_Bits',0,4,190,7,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKP',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUN',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'PINAWKP',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUN',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'PINBWKP',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUN',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'PORSTDF',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'HWCFGEVR',0,1
	.word	642
	.byte	3,3,2,35,1,11
	.byte	'STBYRAM',0,1
	.word	642
	.byte	2,1,2,35,1,11
	.byte	'TRIST',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'SCRST',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'SCRWKP',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'SCR',0,1
	.word	642
	.byte	1,5,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	642
	.byte	1,4,2,35,2,11
	.byte	'ESR1WKEN',0,1
	.word	642
	.byte	1,3,2,35,2,11
	.byte	'PINAWKEN',0,1
	.word	642
	.byte	1,2,2,35,2,11
	.byte	'PINBWKEN',0,1
	.word	642
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	659
	.byte	4,5,2,35,2,11
	.byte	'ESR0TRIST',0,1
	.word	642
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	642
	.byte	4,0,2,35,3,0,36
	.byte	'Ifx_SCU_PMSWSTAT_Bits',0,4,214,7,3
	.word	42685
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTATCLR_Bits',0,4,217,7,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKPCLR',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUNCLR',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'PINAWKPCLR',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUNCLR',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'PINBWKPCLR',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUNCLR',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'SCRSTCLR',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'SCRWKPCLR',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	659
	.byte	14,0,2,35,2,0,36
	.byte	'Ifx_SCU_PMSWSTATCLR_Bits',0,4,230,7,3
	.word	43181
	.byte	10
	.byte	'_Ifx_SCU_RSTCON2_Bits',0,4,233,7,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'CLRC',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	659
	.byte	10,4,2,35,0,11
	.byte	'CSS0',0,1
	.word	642
	.byte	1,3,2,35,1,11
	.byte	'CSS1',0,1
	.word	642
	.byte	1,2,2,35,1,11
	.byte	'CSS2',0,1
	.word	642
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'USRINFO',0,2
	.word	659
	.byte	16,0,2,35,2,0,36
	.byte	'Ifx_SCU_RSTCON2_Bits',0,4,243,7,3
	.word	43494
	.byte	10
	.byte	'_Ifx_SCU_RSTCON_Bits',0,4,246,7,16,4,11
	.byte	'ESR0',0,1
	.word	642
	.byte	2,6,2,35,0,11
	.byte	'ESR1',0,1
	.word	642
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	642
	.byte	2,2,2,35,0,11
	.byte	'SMU',0,1
	.word	642
	.byte	2,0,2,35,0,11
	.byte	'SW',0,1
	.word	642
	.byte	2,6,2,35,1,11
	.byte	'STM0',0,1
	.word	642
	.byte	2,4,2,35,1,11
	.byte	'STM1',0,1
	.word	642
	.byte	2,2,2,35,1,11
	.byte	'STM2',0,1
	.word	642
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	659
	.byte	16,0,2,35,2,0,36
	.byte	'Ifx_SCU_RSTCON_Bits',0,4,129,8,3
	.word	43703
	.byte	10
	.byte	'_Ifx_SCU_RSTSTAT_Bits',0,4,132,8,16,4,11
	.byte	'ESR0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'ESR1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'SMU',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'SW',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'STM0',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'STM1',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'STM2',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'PORST',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'CB0',0,1
	.word	642
	.byte	1,5,2,35,2,11
	.byte	'CB1',0,1
	.word	642
	.byte	1,4,2,35,2,11
	.byte	'CB3',0,1
	.word	642
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	642
	.byte	2,1,2,35,2,11
	.byte	'EVR13',0,1
	.word	642
	.byte	1,0,2,35,2,11
	.byte	'EVR33',0,1
	.word	642
	.byte	1,7,2,35,3,11
	.byte	'SWD',0,1
	.word	642
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	642
	.byte	2,4,2,35,3,11
	.byte	'STBYR',0,1
	.word	642
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	642
	.byte	3,0,2,35,3,0,36
	.byte	'Ifx_SCU_RSTSTAT_Bits',0,4,155,8,3
	.word	43914
	.byte	10
	.byte	'_Ifx_SCU_SAFECON_Bits',0,4,158,8,16,4,11
	.byte	'HBT',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	467
	.byte	31,0,2,35,0,0,36
	.byte	'Ifx_SCU_SAFECON_Bits',0,4,162,8,3
	.word	44346
	.byte	10
	.byte	'_Ifx_SCU_STSTAT_Bits',0,4,165,8,16,4,11
	.byte	'HWCFG',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'FTM',0,1
	.word	642
	.byte	7,1,2,35,1,11
	.byte	'MODE',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'FCBAE',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'LUDIS',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	642
	.byte	1,5,2,35,2,11
	.byte	'TRSTL',0,1
	.word	642
	.byte	1,4,2,35,2,11
	.byte	'SPDEN',0,1
	.word	642
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	642
	.byte	3,0,2,35,2,11
	.byte	'RAMINT',0,1
	.word	642
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	642
	.byte	7,0,2,35,3,0,36
	.byte	'Ifx_SCU_STSTAT_Bits',0,4,178,8,3
	.word	44442
	.byte	10
	.byte	'_Ifx_SCU_SWRSTCON_Bits',0,4,181,8,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'SWRSTREQ',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	467
	.byte	30,0,2,35,0,0,36
	.byte	'Ifx_SCU_SWRSTCON_Bits',0,4,186,8,3
	.word	44702
	.byte	10
	.byte	'_Ifx_SCU_SYSCON_Bits',0,4,189,8,16,4,11
	.byte	'CCTRIG0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'RAMINTM',0,1
	.word	642
	.byte	2,4,2,35,0,11
	.byte	'SETLUDIS',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	642
	.byte	3,0,2,35,0,11
	.byte	'DATM',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,4
	.word	467
	.byte	23,0,2,35,0,0,36
	.byte	'Ifx_SCU_SYSCON_Bits',0,4,198,8,3
	.word	44827
	.byte	10
	.byte	'_Ifx_SCU_TRAPCLR_Bits',0,4,201,8,16,4,11
	.byte	'ESR0T',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	467
	.byte	28,0,2,35,0,0,36
	.byte	'Ifx_SCU_TRAPCLR_Bits',0,4,208,8,3
	.word	45024
	.byte	10
	.byte	'_Ifx_SCU_TRAPDIS_Bits',0,4,211,8,16,4,11
	.byte	'ESR0T',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	467
	.byte	28,0,2,35,0,0,36
	.byte	'Ifx_SCU_TRAPDIS_Bits',0,4,218,8,3
	.word	45177
	.byte	10
	.byte	'_Ifx_SCU_TRAPSET_Bits',0,4,221,8,16,4,11
	.byte	'ESR0T',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	467
	.byte	28,0,2,35,0,0,36
	.byte	'Ifx_SCU_TRAPSET_Bits',0,4,228,8,3
	.word	45330
	.byte	10
	.byte	'_Ifx_SCU_TRAPSTAT_Bits',0,4,231,8,16,4,11
	.byte	'ESR0T',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	467
	.byte	28,0,2,35,0,0,36
	.byte	'Ifx_SCU_TRAPSTAT_Bits',0,4,238,8,3
	.word	45483
	.byte	36
	.byte	'Ifx_SCU_WDTCPU_CON0_Bits',0,4,247,8,3
	.word	506
	.byte	36
	.byte	'Ifx_SCU_WDTCPU_CON1_Bits',0,4,134,9,3
	.word	681
	.byte	36
	.byte	'Ifx_SCU_WDTCPU_SR_Bits',0,4,150,9,3
	.word	925
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON0_Bits',0,4,153,9,16,4,11
	.byte	'ENDINIT',0,4
	.word	490
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	490
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	490
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	490
	.byte	16,0,2,35,0,0,36
	.byte	'Ifx_SCU_WDTS_CON0_Bits',0,4,159,9,3
	.word	45738
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON1_Bits',0,4,162,9,16,4,11
	.byte	'CLRIRF',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'IR0',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	642
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	659
	.byte	16,0,2,35,2,0,36
	.byte	'Ifx_SCU_WDTS_CON1_Bits',0,4,175,9,3
	.word	45864
	.byte	10
	.byte	'_Ifx_SCU_WDTS_SR_Bits',0,4,178,9,16,4,11
	.byte	'AE',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	642
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	659
	.byte	16,0,2,35,2,0,36
	.byte	'Ifx_SCU_WDTS_SR_Bits',0,4,191,9,3
	.word	46116
	.byte	12,4,199,9,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28148
	.byte	4,2,35,0,0,36
	.byte	'Ifx_SCU_ACCEN0',0,4,204,9,3
	.word	46335
	.byte	12,4,207,9,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28705
	.byte	4,2,35,0,0,36
	.byte	'Ifx_SCU_ACCEN1',0,4,212,9,3
	.word	46399
	.byte	12,4,215,9,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28782
	.byte	4,2,35,0,0,36
	.byte	'Ifx_SCU_ARSTDIS',0,4,220,9,3
	.word	46463
	.byte	12,4,223,9,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28918
	.byte	4,2,35,0,0,36
	.byte	'Ifx_SCU_CCUCON0',0,4,228,9,3
	.word	46528
	.byte	12,4,231,9,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29198
	.byte	4,2,35,0,0,36
	.byte	'Ifx_SCU_CCUCON1',0,4,236,9,3
	.word	46593
	.byte	12,4,239,9,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29436
	.byte	4,2,35,0,0,36
	.byte	'Ifx_SCU_CCUCON2',0,4,244,9,3
	.word	46658
	.byte	12,4,247,9,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29564
	.byte	4,2,35,0,0,36
	.byte	'Ifx_SCU_CCUCON3',0,4,252,9,3
	.word	46723
	.byte	12,4,255,9,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29807
	.byte	4,2,35,0,0,36
	.byte	'Ifx_SCU_CCUCON4',0,4,132,10,3
	.word	46788
	.byte	12,4,135,10,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30042
	.byte	4,2,35,0,0,36
	.byte	'Ifx_SCU_CCUCON5',0,4,140,10,3
	.word	46853
	.byte	12,4,143,10,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30170
	.byte	4,2,35,0,0,36
	.byte	'Ifx_SCU_CCUCON6',0,4,148,10,3
	.word	46918
	.byte	12,4,151,10,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30270
	.byte	4,2,35,0,0,36
	.byte	'Ifx_SCU_CCUCON7',0,4,156,10,3
	.word	46983
	.byte	12,4,159,10,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30370
	.byte	4,2,35,0,0,36
	.byte	'Ifx_SCU_CHIPID',0,4,164,10,3
	.word	47048
	.byte	12,4,167,10,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30578
	.byte	4,2,35,0,0,36
	.byte	'Ifx_SCU_DTSCON',0,4,172,10,3
	.word	47112
	.byte	12,4,175,10,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30743
	.byte	4,2,35,0,0,36
	.byte	'Ifx_SCU_DTSLIM',0,4,180,10,3
	.word	47176
	.byte	12,4,183,10,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30926
	.byte	4,2,35,0,0,36
	.byte	'Ifx_SCU_DTSSTAT',0,4,188,10,3
	.word	47240
	.byte	12,4,191,10,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31080
	.byte	4,2,35,0,0,36
	.byte	'Ifx_SCU_EICR',0,4,196,10,3
	.word	47305
	.byte	12,4,199,10,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31444
	.byte	4,2,35,0,0,36
	.byte	'Ifx_SCU_EIFR',0,4,204,10,3
	.word	47367
	.byte	12,4,207,10,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31655
	.byte	4,2,35,0,0,36
	.byte	'Ifx_SCU_EMSR',0,4,212,10,3
	.word	47429
	.byte	12,4,215,10,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31907
	.byte	4,2,35,0,0,36
	.byte	'Ifx_SCU_ESRCFG',0,4,220,10,3
	.word	47491
	.byte	12,4,223,10,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32025
	.byte	4,2,35,0,0,36
	.byte	'Ifx_SCU_ESROCFG',0,4,228,10,3
	.word	47555
	.byte	12,4,231,10,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32136
	.byte	4,2,35,0,0,36
	.byte	'Ifx_SCU_EVR13CON',0,4,236,10,3
	.word	47620
	.byte	12,4,239,10,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32299
	.byte	4,2,35,0,0,36
	.byte	'Ifx_SCU_EVR33CON',0,4,244,10,3
	.word	47686
	.byte	12,4,247,10,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32462
	.byte	4,2,35,0,0,36
	.byte	'Ifx_SCU_EVRADCSTAT',0,4,252,10,3
	.word	47752
	.byte	12,4,255,10,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32620
	.byte	4,2,35,0,0,36
	.byte	'Ifx_SCU_EVRDVSTAT',0,4,132,11,3
	.word	47820
	.byte	12,4,135,11,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32785
	.byte	4,2,35,0,0,36
	.byte	'Ifx_SCU_EVRMONCTRL',0,4,140,11,3
	.word	47887
	.byte	12,4,143,11,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33114
	.byte	4,2,35,0,0,36
	.byte	'Ifx_SCU_EVROSCCTRL',0,4,148,11,3
	.word	47955
	.byte	12,4,151,11,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33335
	.byte	4,2,35,0,0,36
	.byte	'Ifx_SCU_EVROVMON',0,4,156,11,3
	.word	48023
	.byte	12,4,159,11,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33498
	.byte	4,2,35,0,0,36
	.byte	'Ifx_SCU_EVRRSTCON',0,4,164,11,3
	.word	48089
	.byte	12,4,167,11,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33770
	.byte	4,2,35,0,0,36
	.byte	'Ifx_SCU_EVRSDCOEFF1',0,4,172,11,3
	.word	48156
	.byte	12,4,175,11,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33923
	.byte	4,2,35,0,0,36
	.byte	'Ifx_SCU_EVRSDCOEFF2',0,4,180,11,3
	.word	48225
	.byte	12,4,183,11,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34079
	.byte	4,2,35,0,0,36
	.byte	'Ifx_SCU_EVRSDCOEFF3',0,4,188,11,3
	.word	48294
	.byte	12,4,191,11,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34241
	.byte	4,2,35,0,0,36
	.byte	'Ifx_SCU_EVRSDCOEFF4',0,4,196,11,3
	.word	48363
	.byte	12,4,199,11,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34384
	.byte	4,2,35,0,0,36
	.byte	'Ifx_SCU_EVRSDCOEFF5',0,4,204,11,3
	.word	48432
	.byte	12,4,207,11,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34549
	.byte	4,2,35,0,0,36
	.byte	'Ifx_SCU_EVRSDCOEFF6',0,4,212,11,3
	.word	48501
	.byte	12,4,215,11,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34694
	.byte	4,2,35,0,0,36
	.byte	'Ifx_SCU_EVRSDCTRL1',0,4,220,11,3
	.word	48570
	.byte	12,4,223,11,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34875
	.byte	4,2,35,0,0,36
	.byte	'Ifx_SCU_EVRSDCTRL2',0,4,228,11,3
	.word	48638
	.byte	12,4,231,11,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35049
	.byte	4,2,35,0,0,36
	.byte	'Ifx_SCU_EVRSDCTRL3',0,4,236,11,3
	.word	48706
	.byte	12,4,239,11,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35209
	.byte	4,2,35,0,0,36
	.byte	'Ifx_SCU_EVRSDCTRL4',0,4,244,11,3
	.word	48774
	.byte	12,4,247,11,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35353
	.byte	4,2,35,0,0,36
	.byte	'Ifx_SCU_EVRSTAT',0,4,252,11,3
	.word	48842
	.byte	12,4,255,11,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35627
	.byte	4,2,35,0,0,36
	.byte	'Ifx_SCU_EVRTRIM',0,4,132,12,3
	.word	48907
	.byte	12,4,135,12,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35766
	.byte	4,2,35,0,0,36
	.byte	'Ifx_SCU_EVRUVMON',0,4,140,12,3
	.word	48972
	.byte	12,4,143,12,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35929
	.byte	4,2,35,0,0,36
	.byte	'Ifx_SCU_EXTCON',0,4,148,12,3
	.word	49038
	.byte	12,4,151,12,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36147
	.byte	4,2,35,0,0,36
	.byte	'Ifx_SCU_FDR',0,4,156,12,3
	.word	49102
	.byte	12,4,159,12,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36310
	.byte	4,2,35,0,0,36
	.byte	'Ifx_SCU_FMR',0,4,164,12,3
	.word	49163
	.byte	12,4,167,12,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36646
	.byte	4,2,35,0,0,36
	.byte	'Ifx_SCU_ID',0,4,172,12,3
	.word	49224
	.byte	12,4,175,12,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36753
	.byte	4,2,35,0,0,36
	.byte	'Ifx_SCU_IGCR',0,4,180,12,3
	.word	49284
	.byte	12,4,183,12,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37205
	.byte	4,2,35,0,0,36
	.byte	'Ifx_SCU_IN',0,4,188,12,3
	.word	49346
	.byte	12,4,191,12,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37304
	.byte	4,2,35,0,0,36
	.byte	'Ifx_SCU_IOCR',0,4,196,12,3
	.word	49406
	.byte	12,4,199,12,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37454
	.byte	4,2,35,0,0,36
	.byte	'Ifx_SCU_LBISTCTRL0',0,4,204,12,3
	.word	49468
	.byte	12,4,207,12,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37603
	.byte	4,2,35,0,0,36
	.byte	'Ifx_SCU_LBISTCTRL1',0,4,212,12,3
	.word	49536
	.byte	12,4,215,12,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37764
	.byte	4,2,35,0,0,36
	.byte	'Ifx_SCU_LBISTCTRL2',0,4,220,12,3
	.word	49604
	.byte	12,4,223,12,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37894
	.byte	4,2,35,0,0,36
	.byte	'Ifx_SCU_LCLCON',0,4,228,12,3
	.word	49672
	.byte	12,4,231,12,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38026
	.byte	4,2,35,0,0,36
	.byte	'Ifx_SCU_LCLTEST',0,4,236,12,3
	.word	49736
	.byte	12,4,239,12,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38141
	.byte	4,2,35,0,0,36
	.byte	'Ifx_SCU_MANID',0,4,244,12,3
	.word	49801
	.byte	12,4,247,12,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38252
	.byte	4,2,35,0,0,36
	.byte	'Ifx_SCU_OMR',0,4,252,12,3
	.word	49864
	.byte	12,4,255,12,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38410
	.byte	4,2,35,0,0,36
	.byte	'Ifx_SCU_OSCCON',0,4,132,13,3
	.word	49925
	.byte	12,4,135,13,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38822
	.byte	4,2,35,0,0,36
	.byte	'Ifx_SCU_OUT',0,4,140,13,3
	.word	49989
	.byte	12,4,143,13,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38923
	.byte	4,2,35,0,0,36
	.byte	'Ifx_SCU_OVCCON',0,4,148,13,3
	.word	50050
	.byte	12,4,151,13,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39190
	.byte	4,2,35,0,0,36
	.byte	'Ifx_SCU_OVCENABLE',0,4,156,13,3
	.word	50114
	.byte	12,4,159,13,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39326
	.byte	4,2,35,0,0,36
	.byte	'Ifx_SCU_PDISC',0,4,164,13,3
	.word	50181
	.byte	12,4,167,13,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39437
	.byte	4,2,35,0,0,36
	.byte	'Ifx_SCU_PDR',0,4,172,13,3
	.word	50244
	.byte	12,4,175,13,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39570
	.byte	4,2,35,0,0,36
	.byte	'Ifx_SCU_PDRR',0,4,180,13,3
	.word	50305
	.byte	12,4,183,13,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39773
	.byte	4,2,35,0,0,36
	.byte	'Ifx_SCU_PLLCON0',0,4,188,13,3
	.word	50367
	.byte	12,4,191,13,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40129
	.byte	4,2,35,0,0,36
	.byte	'Ifx_SCU_PLLCON1',0,4,196,13,3
	.word	50432
	.byte	12,4,199,13,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40307
	.byte	4,2,35,0,0,36
	.byte	'Ifx_SCU_PLLCON2',0,4,204,13,3
	.word	50497
	.byte	12,4,207,13,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40407
	.byte	4,2,35,0,0,36
	.byte	'Ifx_SCU_PLLERAYCON0',0,4,212,13,3
	.word	50562
	.byte	12,4,215,13,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40777
	.byte	4,2,35,0,0,36
	.byte	'Ifx_SCU_PLLERAYCON1',0,4,220,13,3
	.word	50631
	.byte	12,4,223,13,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40963
	.byte	4,2,35,0,0,36
	.byte	'Ifx_SCU_PLLERAYSTAT',0,4,228,13,3
	.word	50700
	.byte	12,4,231,13,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41161
	.byte	4,2,35,0,0,36
	.byte	'Ifx_SCU_PLLSTAT',0,4,236,13,3
	.word	50769
	.byte	12,4,239,13,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41394
	.byte	4,2,35,0,0,36
	.byte	'Ifx_SCU_PMCSR',0,4,244,13,3
	.word	50834
	.byte	12,4,247,13,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41546
	.byte	4,2,35,0,0,36
	.byte	'Ifx_SCU_PMSWCR0',0,4,252,13,3
	.word	50897
	.byte	12,4,255,13,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42113
	.byte	4,2,35,0,0,36
	.byte	'Ifx_SCU_PMSWCR1',0,4,132,14,3
	.word	50962
	.byte	12,4,135,14,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42407
	.byte	4,2,35,0,0,36
	.byte	'Ifx_SCU_PMSWCR2',0,4,140,14,3
	.word	51027
	.byte	12,4,143,14,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42685
	.byte	4,2,35,0,0,36
	.byte	'Ifx_SCU_PMSWSTAT',0,4,148,14,3
	.word	51092
	.byte	12,4,151,14,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43181
	.byte	4,2,35,0,0,36
	.byte	'Ifx_SCU_PMSWSTATCLR',0,4,156,14,3
	.word	51158
	.byte	12,4,159,14,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43703
	.byte	4,2,35,0,0,36
	.byte	'Ifx_SCU_RSTCON',0,4,164,14,3
	.word	51227
	.byte	12,4,167,14,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43494
	.byte	4,2,35,0,0,36
	.byte	'Ifx_SCU_RSTCON2',0,4,172,14,3
	.word	51291
	.byte	12,4,175,14,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43914
	.byte	4,2,35,0,0,36
	.byte	'Ifx_SCU_RSTSTAT',0,4,180,14,3
	.word	51356
	.byte	12,4,183,14,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44346
	.byte	4,2,35,0,0,36
	.byte	'Ifx_SCU_SAFECON',0,4,188,14,3
	.word	51421
	.byte	12,4,191,14,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44442
	.byte	4,2,35,0,0,36
	.byte	'Ifx_SCU_STSTAT',0,4,196,14,3
	.word	51486
	.byte	12,4,199,14,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44702
	.byte	4,2,35,0,0,36
	.byte	'Ifx_SCU_SWRSTCON',0,4,204,14,3
	.word	51550
	.byte	12,4,207,14,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44827
	.byte	4,2,35,0,0,36
	.byte	'Ifx_SCU_SYSCON',0,4,212,14,3
	.word	51616
	.byte	12,4,215,14,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45024
	.byte	4,2,35,0,0,36
	.byte	'Ifx_SCU_TRAPCLR',0,4,220,14,3
	.word	51680
	.byte	12,4,223,14,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45177
	.byte	4,2,35,0,0,36
	.byte	'Ifx_SCU_TRAPDIS',0,4,228,14,3
	.word	51745
	.byte	12,4,231,14,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45330
	.byte	4,2,35,0,0,36
	.byte	'Ifx_SCU_TRAPSET',0,4,236,14,3
	.word	51810
	.byte	12,4,239,14,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45483
	.byte	4,2,35,0,0,36
	.byte	'Ifx_SCU_TRAPSTAT',0,4,244,14,3
	.word	51875
	.byte	36
	.byte	'Ifx_SCU_WDTCPU_CON0',0,4,252,14,3
	.word	602
	.byte	36
	.byte	'Ifx_SCU_WDTCPU_CON1',0,4,132,15,3
	.word	885
	.byte	36
	.byte	'Ifx_SCU_WDTCPU_SR',0,4,140,15,3
	.word	1116
	.byte	12,4,143,15,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45738
	.byte	4,2,35,0,0,36
	.byte	'Ifx_SCU_WDTS_CON0',0,4,148,15,3
	.word	52026
	.byte	12,4,151,15,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45864
	.byte	4,2,35,0,0,36
	.byte	'Ifx_SCU_WDTS_CON1',0,4,156,15,3
	.word	52093
	.byte	12,4,159,15,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46116
	.byte	4,2,35,0,0,36
	.byte	'Ifx_SCU_WDTS_SR',0,4,164,15,3
	.word	52160
	.byte	14
	.word	1156
	.byte	36
	.byte	'Ifx_SCU_WDTCPU',0,4,180,15,3
	.word	52225
	.byte	10
	.byte	'_Ifx_SCU_WDTS',0,4,183,15,25,12,13
	.byte	'CON0',0
	.word	52026
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	52093
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	52160
	.byte	4,2,35,8,0,14
	.word	52254
	.byte	36
	.byte	'Ifx_SCU_WDTS',0,4,188,15,3
	.word	52315
	.byte	18,8
	.word	47491
	.byte	19,1,0,18,20
	.word	642
	.byte	19,19,0,18,8
	.word	50834
	.byte	19,1,0,14
	.word	52254
	.byte	18,24
	.word	1156
	.byte	19,1,0,14
	.word	52374
	.byte	18,16
	.word	642
	.byte	19,15,0,18,28
	.word	642
	.byte	19,27,0,18,40
	.word	642
	.byte	19,39,0,18,16
	.word	47305
	.byte	19,3,0,18,16
	.word	49284
	.byte	19,3,0,18,180,3
	.word	642
	.byte	19,179,3,0,10
	.byte	'_Ifx_SCU',0,4,201,15,25,128,8,13
	.byte	'reserved_0',0
	.word	4832
	.byte	8,2,35,0,13
	.byte	'ID',0
	.word	49224
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	3013
	.byte	4,2,35,12,13
	.byte	'OSCCON',0
	.word	49925
	.byte	4,2,35,16,13
	.byte	'PLLSTAT',0
	.word	50769
	.byte	4,2,35,20,13
	.byte	'PLLCON0',0
	.word	50367
	.byte	4,2,35,24,13
	.byte	'PLLCON1',0
	.word	50432
	.byte	4,2,35,28,13
	.byte	'PLLCON2',0
	.word	50497
	.byte	4,2,35,32,13
	.byte	'PLLERAYSTAT',0
	.word	50700
	.byte	4,2,35,36,13
	.byte	'PLLERAYCON0',0
	.word	50562
	.byte	4,2,35,40,13
	.byte	'PLLERAYCON1',0
	.word	50631
	.byte	4,2,35,44,13
	.byte	'CCUCON0',0
	.word	46528
	.byte	4,2,35,48,13
	.byte	'CCUCON1',0
	.word	46593
	.byte	4,2,35,52,13
	.byte	'FDR',0
	.word	49102
	.byte	4,2,35,56,13
	.byte	'EXTCON',0
	.word	49038
	.byte	4,2,35,60,13
	.byte	'CCUCON2',0
	.word	46658
	.byte	4,2,35,64,13
	.byte	'CCUCON3',0
	.word	46723
	.byte	4,2,35,68,13
	.byte	'CCUCON4',0
	.word	46788
	.byte	4,2,35,72,13
	.byte	'CCUCON5',0
	.word	46853
	.byte	4,2,35,76,13
	.byte	'RSTSTAT',0
	.word	51356
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	3013
	.byte	4,2,35,84,13
	.byte	'RSTCON',0
	.word	51227
	.byte	4,2,35,88,13
	.byte	'ARSTDIS',0
	.word	46463
	.byte	4,2,35,92,13
	.byte	'SWRSTCON',0
	.word	51550
	.byte	4,2,35,96,13
	.byte	'RSTCON2',0
	.word	51291
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	3013
	.byte	4,2,35,104,13
	.byte	'EVRRSTCON',0
	.word	48089
	.byte	4,2,35,108,13
	.byte	'ESRCFG',0
	.word	52342
	.byte	8,2,35,112,13
	.byte	'ESROCFG',0
	.word	47555
	.byte	4,2,35,120,13
	.byte	'SYSCON',0
	.word	51616
	.byte	4,2,35,124,13
	.byte	'CCUCON6',0
	.word	46918
	.byte	4,3,35,128,1,13
	.byte	'CCUCON7',0
	.word	46983
	.byte	4,3,35,132,1,13
	.byte	'reserved_88',0
	.word	52351
	.byte	20,3,35,136,1,13
	.byte	'PDR',0
	.word	50244
	.byte	4,3,35,156,1,13
	.byte	'IOCR',0
	.word	49406
	.byte	4,3,35,160,1,13
	.byte	'OUT',0
	.word	49989
	.byte	4,3,35,164,1,13
	.byte	'OMR',0
	.word	49864
	.byte	4,3,35,168,1,13
	.byte	'IN',0
	.word	49346
	.byte	4,3,35,172,1,13
	.byte	'EVRSTAT',0
	.word	48842
	.byte	4,3,35,176,1,13
	.byte	'EVRDVSTAT',0
	.word	47820
	.byte	4,3,35,180,1,13
	.byte	'EVR13CON',0
	.word	47620
	.byte	4,3,35,184,1,13
	.byte	'EVR33CON',0
	.word	47686
	.byte	4,3,35,188,1,13
	.byte	'STSTAT',0
	.word	51486
	.byte	4,3,35,192,1,13
	.byte	'reserved_C4',0
	.word	3013
	.byte	4,3,35,196,1,13
	.byte	'PMSWCR0',0
	.word	50897
	.byte	4,3,35,200,1,13
	.byte	'PMSWSTAT',0
	.word	51092
	.byte	4,3,35,204,1,13
	.byte	'PMSWSTATCLR',0
	.word	51158
	.byte	4,3,35,208,1,13
	.byte	'PMCSR',0
	.word	52360
	.byte	8,3,35,212,1,13
	.byte	'reserved_DC',0
	.word	3013
	.byte	4,3,35,220,1,13
	.byte	'DTSSTAT',0
	.word	47240
	.byte	4,3,35,224,1,13
	.byte	'DTSCON',0
	.word	47112
	.byte	4,3,35,228,1,13
	.byte	'PMSWCR1',0
	.word	50962
	.byte	4,3,35,232,1,13
	.byte	'PMSWCR2',0
	.word	51027
	.byte	4,3,35,236,1,13
	.byte	'WDTS',0
	.word	52369
	.byte	12,3,35,240,1,13
	.byte	'EMSR',0
	.word	47429
	.byte	4,3,35,252,1,13
	.byte	'WDTCPU',0
	.word	52383
	.byte	24,3,35,128,2,13
	.byte	'reserved_118',0
	.word	5172
	.byte	12,3,35,152,2,13
	.byte	'TRAPSTAT',0
	.word	51875
	.byte	4,3,35,164,2,13
	.byte	'TRAPSET',0
	.word	51810
	.byte	4,3,35,168,2,13
	.byte	'TRAPCLR',0
	.word	51680
	.byte	4,3,35,172,2,13
	.byte	'TRAPDIS',0
	.word	51745
	.byte	4,3,35,176,2,13
	.byte	'reserved_134',0
	.word	3013
	.byte	4,3,35,180,2,13
	.byte	'LCLCON1',0
	.word	49672
	.byte	4,3,35,184,2,13
	.byte	'LCLTEST',0
	.word	49736
	.byte	4,3,35,188,2,13
	.byte	'CHIPID',0
	.word	47048
	.byte	4,3,35,192,2,13
	.byte	'MANID',0
	.word	49801
	.byte	4,3,35,196,2,13
	.byte	'reserved_148',0
	.word	4832
	.byte	8,3,35,200,2,13
	.byte	'SAFECON',0
	.word	51421
	.byte	4,3,35,208,2,13
	.byte	'reserved_154',0
	.word	52388
	.byte	16,3,35,212,2,13
	.byte	'LBISTCTRL0',0
	.word	49468
	.byte	4,3,35,228,2,13
	.byte	'LBISTCTRL1',0
	.word	49536
	.byte	4,3,35,232,2,13
	.byte	'LBISTCTRL2',0
	.word	49604
	.byte	4,3,35,236,2,13
	.byte	'reserved_170',0
	.word	52397
	.byte	28,3,35,240,2,13
	.byte	'PDISC',0
	.word	50181
	.byte	4,3,35,140,3,13
	.byte	'reserved_190',0
	.word	4832
	.byte	8,3,35,144,3,13
	.byte	'EVRTRIM',0
	.word	48907
	.byte	4,3,35,152,3,13
	.byte	'EVRADCSTAT',0
	.word	47752
	.byte	4,3,35,156,3,13
	.byte	'EVRUVMON',0
	.word	48972
	.byte	4,3,35,160,3,13
	.byte	'EVROVMON',0
	.word	48023
	.byte	4,3,35,164,3,13
	.byte	'EVRMONCTRL',0
	.word	47887
	.byte	4,3,35,168,3,13
	.byte	'reserved_1AC',0
	.word	3013
	.byte	4,3,35,172,3,13
	.byte	'EVRSDCTRL1',0
	.word	48570
	.byte	4,3,35,176,3,13
	.byte	'EVRSDCTRL2',0
	.word	48638
	.byte	4,3,35,180,3,13
	.byte	'EVRSDCTRL3',0
	.word	48706
	.byte	4,3,35,184,3,13
	.byte	'EVRSDCTRL4',0
	.word	48774
	.byte	4,3,35,188,3,13
	.byte	'EVRSDCOEFF1',0
	.word	48156
	.byte	4,3,35,192,3,13
	.byte	'EVRSDCOEFF2',0
	.word	48225
	.byte	4,3,35,196,3,13
	.byte	'EVRSDCOEFF3',0
	.word	48294
	.byte	4,3,35,200,3,13
	.byte	'EVRSDCOEFF4',0
	.word	48363
	.byte	4,3,35,204,3,13
	.byte	'EVRSDCOEFF5',0
	.word	48432
	.byte	4,3,35,208,3,13
	.byte	'EVRSDCOEFF6',0
	.word	48501
	.byte	4,3,35,212,3,13
	.byte	'EVROSCCTRL',0
	.word	47955
	.byte	4,3,35,216,3,13
	.byte	'reserved_1DC',0
	.word	3013
	.byte	4,3,35,220,3,13
	.byte	'OVCENABLE',0
	.word	50114
	.byte	4,3,35,224,3,13
	.byte	'OVCCON',0
	.word	50050
	.byte	4,3,35,228,3,13
	.byte	'reserved_1E8',0
	.word	52406
	.byte	40,3,35,232,3,13
	.byte	'EICR',0
	.word	52415
	.byte	16,3,35,144,4,13
	.byte	'EIFR',0
	.word	47367
	.byte	4,3,35,160,4,13
	.byte	'FMR',0
	.word	49163
	.byte	4,3,35,164,4,13
	.byte	'PDRR',0
	.word	50305
	.byte	4,3,35,168,4,13
	.byte	'IGCR',0
	.word	52424
	.byte	16,3,35,172,4,13
	.byte	'reserved_23C',0
	.word	3013
	.byte	4,3,35,188,4,13
	.byte	'DTSLIM',0
	.word	47176
	.byte	4,3,35,192,4,13
	.byte	'reserved_244',0
	.word	52433
	.byte	180,3,3,35,196,4,13
	.byte	'ACCEN1',0
	.word	46399
	.byte	4,3,35,248,7,13
	.byte	'ACCEN0',0
	.word	46335
	.byte	4,3,35,252,7,0,14
	.word	52444
	.byte	36
	.byte	'Ifx_SCU',0,4,181,16,3
	.word	54434
	.byte	10
	.byte	'_Ifx_CPU_A_Bits',0,29,45,16,4,11
	.byte	'ADDR',0,4
	.word	490
	.byte	32,0,2,35,0,0,36
	.byte	'Ifx_CPU_A_Bits',0,29,48,3
	.word	54456
	.byte	10
	.byte	'_Ifx_CPU_BIV_Bits',0,29,51,16,4,11
	.byte	'VSS',0,4
	.word	490
	.byte	1,31,2,35,0,11
	.byte	'BIV',0,4
	.word	490
	.byte	31,0,2,35,0,0,36
	.byte	'Ifx_CPU_BIV_Bits',0,29,55,3
	.word	54517
	.byte	10
	.byte	'_Ifx_CPU_BTV_Bits',0,29,58,16,4,11
	.byte	'reserved_0',0,4
	.word	490
	.byte	1,31,2,35,0,11
	.byte	'BTV',0,4
	.word	490
	.byte	31,0,2,35,0,0,36
	.byte	'Ifx_CPU_BTV_Bits',0,29,62,3
	.word	54596
	.byte	10
	.byte	'_Ifx_CPU_CCNT_Bits',0,29,65,16,4,11
	.byte	'CountValue',0,4
	.word	490
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	490
	.byte	1,0,2,35,0,0,36
	.byte	'Ifx_CPU_CCNT_Bits',0,29,69,3
	.word	54682
	.byte	10
	.byte	'_Ifx_CPU_CCTRL_Bits',0,29,72,16,4,11
	.byte	'CM',0,4
	.word	490
	.byte	1,31,2,35,0,11
	.byte	'CE',0,4
	.word	490
	.byte	1,30,2,35,0,11
	.byte	'M1',0,4
	.word	490
	.byte	3,27,2,35,0,11
	.byte	'M2',0,4
	.word	490
	.byte	3,24,2,35,0,11
	.byte	'M3',0,4
	.word	490
	.byte	3,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	490
	.byte	21,0,2,35,0,0,36
	.byte	'Ifx_CPU_CCTRL_Bits',0,29,80,3
	.word	54771
	.byte	10
	.byte	'_Ifx_CPU_COMPAT_Bits',0,29,83,16,4,11
	.byte	'reserved_0',0,4
	.word	490
	.byte	3,29,2,35,0,11
	.byte	'RM',0,4
	.word	490
	.byte	1,28,2,35,0,11
	.byte	'SP',0,4
	.word	490
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	490
	.byte	27,0,2,35,0,0,36
	.byte	'Ifx_CPU_COMPAT_Bits',0,29,89,3
	.word	54917
	.byte	10
	.byte	'_Ifx_CPU_CORE_ID_Bits',0,29,92,16,4,11
	.byte	'CORE_ID',0,4
	.word	490
	.byte	3,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	490
	.byte	29,0,2,35,0,0,36
	.byte	'Ifx_CPU_CORE_ID_Bits',0,29,96,3
	.word	55044
	.byte	10
	.byte	'_Ifx_CPU_CPR_L_Bits',0,29,99,16,4,11
	.byte	'reserved_0',0,4
	.word	490
	.byte	3,29,2,35,0,11
	.byte	'LOWBND',0,4
	.word	490
	.byte	29,0,2,35,0,0,36
	.byte	'Ifx_CPU_CPR_L_Bits',0,29,103,3
	.word	55142
	.byte	10
	.byte	'_Ifx_CPU_CPR_U_Bits',0,29,106,16,4,11
	.byte	'reserved_0',0,4
	.word	490
	.byte	3,29,2,35,0,11
	.byte	'UPPBND',0,4
	.word	490
	.byte	29,0,2,35,0,0,36
	.byte	'Ifx_CPU_CPR_U_Bits',0,29,110,3
	.word	55235
	.byte	10
	.byte	'_Ifx_CPU_CPU_ID_Bits',0,29,113,16,4,11
	.byte	'MODREV',0,4
	.word	490
	.byte	8,24,2,35,0,11
	.byte	'MOD_32B',0,4
	.word	490
	.byte	8,16,2,35,0,11
	.byte	'MOD',0,4
	.word	490
	.byte	16,0,2,35,0,0,36
	.byte	'Ifx_CPU_CPU_ID_Bits',0,29,118,3
	.word	55328
	.byte	10
	.byte	'_Ifx_CPU_CPXE_Bits',0,29,121,16,4,11
	.byte	'XE',0,4
	.word	490
	.byte	8,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	490
	.byte	24,0,2,35,0,0,36
	.byte	'Ifx_CPU_CPXE_Bits',0,29,125,3
	.word	55435
	.byte	10
	.byte	'_Ifx_CPU_CREVT_Bits',0,29,128,1,16,4,11
	.byte	'EVTA',0,4
	.word	490
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	490
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	490
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	490
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	490
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	490
	.byte	24,0,2,35,0,0,36
	.byte	'Ifx_CPU_CREVT_Bits',0,29,136,1,3
	.word	55522
	.byte	10
	.byte	'_Ifx_CPU_CUS_ID_Bits',0,29,139,1,16,4,11
	.byte	'CID',0,4
	.word	490
	.byte	3,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	490
	.byte	29,0,2,35,0,0,36
	.byte	'Ifx_CPU_CUS_ID_Bits',0,29,143,1,3
	.word	55676
	.byte	10
	.byte	'_Ifx_CPU_D_Bits',0,29,146,1,16,4,11
	.byte	'DATA',0,4
	.word	490
	.byte	32,0,2,35,0,0,36
	.byte	'Ifx_CPU_D_Bits',0,29,149,1,3
	.word	55770
	.byte	10
	.byte	'_Ifx_CPU_DATR_Bits',0,29,152,1,16,4,11
	.byte	'reserved_0',0,4
	.word	490
	.byte	3,29,2,35,0,11
	.byte	'SBE',0,4
	.word	490
	.byte	1,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	490
	.byte	5,23,2,35,0,11
	.byte	'CWE',0,4
	.word	490
	.byte	1,22,2,35,0,11
	.byte	'CFE',0,4
	.word	490
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	490
	.byte	3,18,2,35,0,11
	.byte	'SOE',0,4
	.word	490
	.byte	1,17,2,35,0,11
	.byte	'SME',0,4
	.word	490
	.byte	1,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	490
	.byte	16,0,2,35,0,0,36
	.byte	'Ifx_CPU_DATR_Bits',0,29,163,1,3
	.word	55833
	.byte	10
	.byte	'_Ifx_CPU_DBGSR_Bits',0,29,166,1,16,4,11
	.byte	'DE',0,4
	.word	490
	.byte	1,31,2,35,0,11
	.byte	'HALT',0,4
	.word	490
	.byte	2,29,2,35,0,11
	.byte	'SIH',0,4
	.word	490
	.byte	1,28,2,35,0,11
	.byte	'SUSP',0,4
	.word	490
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	490
	.byte	1,26,2,35,0,11
	.byte	'PREVSUSP',0,4
	.word	490
	.byte	1,25,2,35,0,11
	.byte	'PEVT',0,4
	.word	490
	.byte	1,24,2,35,0,11
	.byte	'EVTSRC',0,4
	.word	490
	.byte	5,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	490
	.byte	19,0,2,35,0,0,36
	.byte	'Ifx_CPU_DBGSR_Bits',0,29,177,1,3
	.word	56051
	.byte	10
	.byte	'_Ifx_CPU_DBGTCR_Bits',0,29,180,1,16,4,11
	.byte	'DTA',0,4
	.word	490
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	490
	.byte	31,0,2,35,0,0,36
	.byte	'Ifx_CPU_DBGTCR_Bits',0,29,184,1,3
	.word	56266
	.byte	10
	.byte	'_Ifx_CPU_DCON0_Bits',0,29,187,1,16,4,11
	.byte	'reserved_0',0,4
	.word	490
	.byte	1,31,2,35,0,11
	.byte	'DCBYP',0,4
	.word	490
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	490
	.byte	30,0,2,35,0,0,36
	.byte	'Ifx_CPU_DCON0_Bits',0,29,192,1,3
	.word	56360
	.byte	10
	.byte	'_Ifx_CPU_DCON2_Bits',0,29,195,1,16,4,11
	.byte	'DCACHE_SZE',0,4
	.word	490
	.byte	16,16,2,35,0,11
	.byte	'DSCRATCH_SZE',0,4
	.word	490
	.byte	16,0,2,35,0,0,36
	.byte	'Ifx_CPU_DCON2_Bits',0,29,199,1,3
	.word	56476
	.byte	10
	.byte	'_Ifx_CPU_DCX_Bits',0,29,202,1,16,4,11
	.byte	'reserved_0',0,4
	.word	490
	.byte	6,26,2,35,0,11
	.byte	'DCXValue',0,4
	.word	490
	.byte	26,0,2,35,0,0,36
	.byte	'Ifx_CPU_DCX_Bits',0,29,206,1,3
	.word	56577
	.byte	10
	.byte	'_Ifx_CPU_DEADD_Bits',0,29,209,1,16,4,11
	.byte	'ERROR_ADDRESS',0,4
	.word	490
	.byte	32,0,2,35,0,0,36
	.byte	'Ifx_CPU_DEADD_Bits',0,29,212,1,3
	.word	56670
	.byte	10
	.byte	'_Ifx_CPU_DIEAR_Bits',0,29,215,1,16,4,11
	.byte	'TA',0,4
	.word	490
	.byte	32,0,2,35,0,0,36
	.byte	'Ifx_CPU_DIEAR_Bits',0,29,218,1,3
	.word	56750
	.byte	10
	.byte	'_Ifx_CPU_DIETR_Bits',0,29,221,1,16,4,11
	.byte	'IED',0,4
	.word	490
	.byte	1,31,2,35,0,11
	.byte	'IE_T',0,4
	.word	490
	.byte	1,30,2,35,0,11
	.byte	'IE_C',0,4
	.word	490
	.byte	1,29,2,35,0,11
	.byte	'IE_S',0,4
	.word	490
	.byte	1,28,2,35,0,11
	.byte	'IE_BI',0,4
	.word	490
	.byte	1,27,2,35,0,11
	.byte	'E_INFO',0,4
	.word	490
	.byte	6,21,2,35,0,11
	.byte	'IE_DUAL',0,4
	.word	490
	.byte	1,20,2,35,0,11
	.byte	'IE_SP',0,4
	.word	490
	.byte	1,19,2,35,0,11
	.byte	'IE_BS',0,4
	.word	490
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	490
	.byte	18,0,2,35,0,0,36
	.byte	'Ifx_CPU_DIETR_Bits',0,29,233,1,3
	.word	56819
	.byte	10
	.byte	'_Ifx_CPU_DMS_Bits',0,29,236,1,16,4,11
	.byte	'reserved_0',0,4
	.word	490
	.byte	1,31,2,35,0,11
	.byte	'DMSValue',0,4
	.word	490
	.byte	31,0,2,35,0,0,36
	.byte	'Ifx_CPU_DMS_Bits',0,29,240,1,3
	.word	57048
	.byte	10
	.byte	'_Ifx_CPU_DPR_L_Bits',0,29,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	490
	.byte	3,29,2,35,0,11
	.byte	'LOWBND',0,4
	.word	490
	.byte	29,0,2,35,0,0,36
	.byte	'Ifx_CPU_DPR_L_Bits',0,29,247,1,3
	.word	57141
	.byte	10
	.byte	'_Ifx_CPU_DPR_U_Bits',0,29,250,1,16,4,11
	.byte	'reserved_0',0,4
	.word	490
	.byte	3,29,2,35,0,11
	.byte	'UPPBND',0,4
	.word	490
	.byte	29,0,2,35,0,0,36
	.byte	'Ifx_CPU_DPR_U_Bits',0,29,254,1,3
	.word	57236
	.byte	10
	.byte	'_Ifx_CPU_DPRE_Bits',0,29,129,2,16,4,11
	.byte	'RE',0,4
	.word	490
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	490
	.byte	16,0,2,35,0,0,36
	.byte	'Ifx_CPU_DPRE_Bits',0,29,133,2,3
	.word	57331
	.byte	10
	.byte	'_Ifx_CPU_DPWE_Bits',0,29,136,2,16,4,11
	.byte	'WE',0,4
	.word	490
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	490
	.byte	16,0,2,35,0,0,36
	.byte	'Ifx_CPU_DPWE_Bits',0,29,140,2,3
	.word	57421
	.byte	10
	.byte	'_Ifx_CPU_DSTR_Bits',0,29,143,2,16,4,11
	.byte	'SRE',0,4
	.word	490
	.byte	1,31,2,35,0,11
	.byte	'GAE',0,4
	.word	490
	.byte	1,30,2,35,0,11
	.byte	'LBE',0,4
	.word	490
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	490
	.byte	3,26,2,35,0,11
	.byte	'CRE',0,4
	.word	490
	.byte	1,25,2,35,0,11
	.byte	'reserved_7',0,4
	.word	490
	.byte	7,18,2,35,0,11
	.byte	'DTME',0,4
	.word	490
	.byte	1,17,2,35,0,11
	.byte	'LOE',0,4
	.word	490
	.byte	1,16,2,35,0,11
	.byte	'SDE',0,4
	.word	490
	.byte	1,15,2,35,0,11
	.byte	'SCE',0,4
	.word	490
	.byte	1,14,2,35,0,11
	.byte	'CAC',0,4
	.word	490
	.byte	1,13,2,35,0,11
	.byte	'MPE',0,4
	.word	490
	.byte	1,12,2,35,0,11
	.byte	'CLE',0,4
	.word	490
	.byte	1,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	490
	.byte	3,8,2,35,0,11
	.byte	'ALN',0,4
	.word	490
	.byte	1,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	490
	.byte	7,0,2,35,0,0,36
	.byte	'Ifx_CPU_DSTR_Bits',0,29,161,2,3
	.word	57511
	.byte	10
	.byte	'_Ifx_CPU_EXEVT_Bits',0,29,164,2,16,4,11
	.byte	'EVTA',0,4
	.word	490
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	490
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	490
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	490
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	490
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	490
	.byte	24,0,2,35,0,0,36
	.byte	'Ifx_CPU_EXEVT_Bits',0,29,172,2,3
	.word	57835
	.byte	10
	.byte	'_Ifx_CPU_FCX_Bits',0,29,175,2,16,4,11
	.byte	'FCXO',0,4
	.word	490
	.byte	16,16,2,35,0,11
	.byte	'FCXS',0,4
	.word	490
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	490
	.byte	12,0,2,35,0,0,36
	.byte	'Ifx_CPU_FCX_Bits',0,29,180,2,3
	.word	57989
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_CON_Bits',0,29,183,2,16,4,11
	.byte	'TST',0,4
	.word	490
	.byte	1,31,2,35,0,11
	.byte	'TCL',0,4
	.word	490
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	490
	.byte	6,24,2,35,0,11
	.byte	'RM',0,4
	.word	490
	.byte	2,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	490
	.byte	8,14,2,35,0,11
	.byte	'FXE',0,4
	.word	490
	.byte	1,13,2,35,0,11
	.byte	'FUE',0,4
	.word	490
	.byte	1,12,2,35,0,11
	.byte	'FZE',0,4
	.word	490
	.byte	1,11,2,35,0,11
	.byte	'FVE',0,4
	.word	490
	.byte	1,10,2,35,0,11
	.byte	'FIE',0,4
	.word	490
	.byte	1,9,2,35,0,11
	.byte	'reserved_23',0,4
	.word	490
	.byte	3,6,2,35,0,11
	.byte	'FX',0,4
	.word	490
	.byte	1,5,2,35,0,11
	.byte	'FU',0,4
	.word	490
	.byte	1,4,2,35,0,11
	.byte	'FZ',0,4
	.word	490
	.byte	1,3,2,35,0,11
	.byte	'FV',0,4
	.word	490
	.byte	1,2,2,35,0,11
	.byte	'FI',0,4
	.word	490
	.byte	1,1,2,35,0,11
	.byte	'reserved_31',0,4
	.word	490
	.byte	1,0,2,35,0,0,36
	.byte	'Ifx_CPU_FPU_TRAP_CON_Bits',0,29,202,2,3
	.word	58095
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_OPC_Bits',0,29,205,2,16,4,11
	.byte	'OPC',0,4
	.word	490
	.byte	8,24,2,35,0,11
	.byte	'FMT',0,4
	.word	490
	.byte	1,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	490
	.byte	7,16,2,35,0,11
	.byte	'DREG',0,4
	.word	490
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	490
	.byte	12,0,2,35,0,0,36
	.byte	'Ifx_CPU_FPU_TRAP_OPC_Bits',0,29,212,2,3
	.word	58444
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_PC_Bits',0,29,215,2,16,4,11
	.byte	'PC',0,4
	.word	490
	.byte	32,0,2,35,0,0,36
	.byte	'Ifx_CPU_FPU_TRAP_PC_Bits',0,29,218,2,3
	.word	58604
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC1_Bits',0,29,221,2,16,4,11
	.byte	'SRC1',0,4
	.word	490
	.byte	32,0,2,35,0,0,36
	.byte	'Ifx_CPU_FPU_TRAP_SRC1_Bits',0,29,224,2,3
	.word	58685
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC2_Bits',0,29,227,2,16,4,11
	.byte	'SRC2',0,4
	.word	490
	.byte	32,0,2,35,0,0,36
	.byte	'Ifx_CPU_FPU_TRAP_SRC2_Bits',0,29,230,2,3
	.word	58772
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC3_Bits',0,29,233,2,16,4,11
	.byte	'SRC3',0,4
	.word	490
	.byte	32,0,2,35,0,0,36
	.byte	'Ifx_CPU_FPU_TRAP_SRC3_Bits',0,29,236,2,3
	.word	58859
	.byte	10
	.byte	'_Ifx_CPU_ICNT_Bits',0,29,239,2,16,4,11
	.byte	'CountValue',0,4
	.word	490
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	490
	.byte	1,0,2,35,0,0,36
	.byte	'Ifx_CPU_ICNT_Bits',0,29,243,2,3
	.word	58946
	.byte	10
	.byte	'_Ifx_CPU_ICR_Bits',0,29,246,2,16,4,11
	.byte	'CCPN',0,4
	.word	490
	.byte	10,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	490
	.byte	5,17,2,35,0,11
	.byte	'IE',0,4
	.word	490
	.byte	1,16,2,35,0,11
	.byte	'PIPN',0,4
	.word	490
	.byte	10,6,2,35,0,11
	.byte	'reserved_26',0,4
	.word	490
	.byte	6,0,2,35,0,0,36
	.byte	'Ifx_CPU_ICR_Bits',0,29,253,2,3
	.word	59037
	.byte	10
	.byte	'_Ifx_CPU_ISP_Bits',0,29,128,3,16,4,11
	.byte	'ISP',0,4
	.word	490
	.byte	32,0,2,35,0,0,36
	.byte	'Ifx_CPU_ISP_Bits',0,29,131,3,3
	.word	59180
	.byte	10
	.byte	'_Ifx_CPU_LCX_Bits',0,29,134,3,16,4,11
	.byte	'LCXO',0,4
	.word	490
	.byte	16,16,2,35,0,11
	.byte	'LCXS',0,4
	.word	490
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	490
	.byte	12,0,2,35,0,0,36
	.byte	'Ifx_CPU_LCX_Bits',0,29,139,3,3
	.word	59246
	.byte	10
	.byte	'_Ifx_CPU_M1CNT_Bits',0,29,142,3,16,4,11
	.byte	'CountValue',0,4
	.word	490
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	490
	.byte	1,0,2,35,0,0,36
	.byte	'Ifx_CPU_M1CNT_Bits',0,29,146,3,3
	.word	59352
	.byte	10
	.byte	'_Ifx_CPU_M2CNT_Bits',0,29,149,3,16,4,11
	.byte	'CountValue',0,4
	.word	490
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	490
	.byte	1,0,2,35,0,0,36
	.byte	'Ifx_CPU_M2CNT_Bits',0,29,153,3,3
	.word	59445
	.byte	10
	.byte	'_Ifx_CPU_M3CNT_Bits',0,29,156,3,16,4,11
	.byte	'CountValue',0,4
	.word	490
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	490
	.byte	1,0,2,35,0,0,36
	.byte	'Ifx_CPU_M3CNT_Bits',0,29,160,3,3
	.word	59538
	.byte	10
	.byte	'_Ifx_CPU_PC_Bits',0,29,163,3,16,4,11
	.byte	'reserved_0',0,4
	.word	490
	.byte	1,31,2,35,0,11
	.byte	'PC',0,4
	.word	490
	.byte	31,0,2,35,0,0,36
	.byte	'Ifx_CPU_PC_Bits',0,29,167,3,3
	.word	59631
	.byte	10
	.byte	'_Ifx_CPU_PCON0_Bits',0,29,170,3,16,4,11
	.byte	'reserved_0',0,4
	.word	490
	.byte	1,31,2,35,0,11
	.byte	'PCBYP',0,4
	.word	490
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	490
	.byte	30,0,2,35,0,0,36
	.byte	'Ifx_CPU_PCON0_Bits',0,29,175,3,3
	.word	59716
	.byte	10
	.byte	'_Ifx_CPU_PCON1_Bits',0,29,178,3,16,4,11
	.byte	'PCINV',0,4
	.word	490
	.byte	1,31,2,35,0,11
	.byte	'PBINV',0,4
	.word	490
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	490
	.byte	30,0,2,35,0,0,36
	.byte	'Ifx_CPU_PCON1_Bits',0,29,183,3,3
	.word	59832
	.byte	10
	.byte	'_Ifx_CPU_PCON2_Bits',0,29,186,3,16,4,11
	.byte	'PCACHE_SZE',0,4
	.word	490
	.byte	16,16,2,35,0,11
	.byte	'PSCRATCH_SZE',0,4
	.word	490
	.byte	16,0,2,35,0,0,36
	.byte	'Ifx_CPU_PCON2_Bits',0,29,190,3,3
	.word	59943
	.byte	10
	.byte	'_Ifx_CPU_PCXI_Bits',0,29,193,3,16,4,11
	.byte	'PCXO',0,4
	.word	490
	.byte	16,16,2,35,0,11
	.byte	'PCXS',0,4
	.word	490
	.byte	4,12,2,35,0,11
	.byte	'UL',0,4
	.word	490
	.byte	1,11,2,35,0,11
	.byte	'PIE',0,4
	.word	490
	.byte	1,10,2,35,0,11
	.byte	'PCPN',0,4
	.word	490
	.byte	10,0,2,35,0,0,36
	.byte	'Ifx_CPU_PCXI_Bits',0,29,200,3,3
	.word	60044
	.byte	10
	.byte	'_Ifx_CPU_PIEAR_Bits',0,29,203,3,16,4,11
	.byte	'TA',0,4
	.word	490
	.byte	32,0,2,35,0,0,36
	.byte	'Ifx_CPU_PIEAR_Bits',0,29,206,3,3
	.word	60174
	.byte	10
	.byte	'_Ifx_CPU_PIETR_Bits',0,29,209,3,16,4,11
	.byte	'IED',0,4
	.word	490
	.byte	1,31,2,35,0,11
	.byte	'IE_T',0,4
	.word	490
	.byte	1,30,2,35,0,11
	.byte	'IE_C',0,4
	.word	490
	.byte	1,29,2,35,0,11
	.byte	'IE_S',0,4
	.word	490
	.byte	1,28,2,35,0,11
	.byte	'IE_BI',0,4
	.word	490
	.byte	1,27,2,35,0,11
	.byte	'E_INFO',0,4
	.word	490
	.byte	6,21,2,35,0,11
	.byte	'IE_DUAL',0,4
	.word	490
	.byte	1,20,2,35,0,11
	.byte	'IE_SP',0,4
	.word	490
	.byte	1,19,2,35,0,11
	.byte	'IE_BS',0,4
	.word	490
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	490
	.byte	18,0,2,35,0,0,36
	.byte	'Ifx_CPU_PIETR_Bits',0,29,221,3,3
	.word	60243
	.byte	10
	.byte	'_Ifx_CPU_PMA0_Bits',0,29,224,3,16,4,11
	.byte	'reserved_0',0,4
	.word	490
	.byte	13,19,2,35,0,11
	.byte	'DAC',0,4
	.word	490
	.byte	3,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	490
	.byte	16,0,2,35,0,0,36
	.byte	'Ifx_CPU_PMA0_Bits',0,29,229,3,3
	.word	60472
	.byte	10
	.byte	'_Ifx_CPU_PMA1_Bits',0,29,232,3,16,4,11
	.byte	'reserved_0',0,4
	.word	490
	.byte	14,18,2,35,0,11
	.byte	'CAC',0,4
	.word	490
	.byte	2,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	490
	.byte	16,0,2,35,0,0,36
	.byte	'Ifx_CPU_PMA1_Bits',0,29,237,3,3
	.word	60585
	.byte	10
	.byte	'_Ifx_CPU_PMA2_Bits',0,29,240,3,16,4,11
	.byte	'PSI',0,4
	.word	490
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	490
	.byte	16,0,2,35,0,0,36
	.byte	'Ifx_CPU_PMA2_Bits',0,29,244,3,3
	.word	60698
	.byte	10
	.byte	'_Ifx_CPU_PSTR_Bits',0,29,247,3,16,4,11
	.byte	'FRE',0,4
	.word	490
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	490
	.byte	1,30,2,35,0,11
	.byte	'FBE',0,4
	.word	490
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	490
	.byte	9,20,2,35,0,11
	.byte	'FPE',0,4
	.word	490
	.byte	1,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	490
	.byte	1,18,2,35,0,11
	.byte	'FME',0,4
	.word	490
	.byte	1,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	490
	.byte	17,0,2,35,0,0,36
	.byte	'Ifx_CPU_PSTR_Bits',0,29,129,4,3
	.word	60789
	.byte	10
	.byte	'_Ifx_CPU_PSW_Bits',0,29,132,4,16,4,11
	.byte	'CDC',0,4
	.word	490
	.byte	7,25,2,35,0,11
	.byte	'CDE',0,4
	.word	490
	.byte	1,24,2,35,0,11
	.byte	'GW',0,4
	.word	490
	.byte	1,23,2,35,0,11
	.byte	'IS',0,4
	.word	490
	.byte	1,22,2,35,0,11
	.byte	'IO',0,4
	.word	490
	.byte	2,20,2,35,0,11
	.byte	'PRS',0,4
	.word	490
	.byte	2,18,2,35,0,11
	.byte	'S',0,4
	.word	490
	.byte	1,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	490
	.byte	12,5,2,35,0,11
	.byte	'SAV',0,4
	.word	490
	.byte	1,4,2,35,0,11
	.byte	'AV',0,4
	.word	490
	.byte	1,3,2,35,0,11
	.byte	'SV',0,4
	.word	490
	.byte	1,2,2,35,0,11
	.byte	'V',0,4
	.word	490
	.byte	1,1,2,35,0,11
	.byte	'C',0,4
	.word	490
	.byte	1,0,2,35,0,0,36
	.byte	'Ifx_CPU_PSW_Bits',0,29,147,4,3
	.word	60992
	.byte	10
	.byte	'_Ifx_CPU_SEGEN_Bits',0,29,150,4,16,4,11
	.byte	'ADFLIP',0,4
	.word	490
	.byte	8,24,2,35,0,11
	.byte	'ADTYPE',0,4
	.word	490
	.byte	2,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	490
	.byte	21,1,2,35,0,11
	.byte	'AE',0,4
	.word	490
	.byte	1,0,2,35,0,0,36
	.byte	'Ifx_CPU_SEGEN_Bits',0,29,156,4,3
	.word	61235
	.byte	10
	.byte	'_Ifx_CPU_SMACON_Bits',0,29,159,4,16,4,11
	.byte	'PC',0,4
	.word	490
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	490
	.byte	1,30,2,35,0,11
	.byte	'PT',0,4
	.word	490
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	490
	.byte	5,24,2,35,0,11
	.byte	'DC',0,4
	.word	490
	.byte	1,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	490
	.byte	1,22,2,35,0,11
	.byte	'DT',0,4
	.word	490
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	490
	.byte	13,8,2,35,0,11
	.byte	'IODT',0,4
	.word	490
	.byte	1,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	490
	.byte	7,0,2,35,0,0,36
	.byte	'Ifx_CPU_SMACON_Bits',0,29,171,4,3
	.word	61363
	.byte	10
	.byte	'_Ifx_CPU_SPROT_ACCENA_Bits',0,29,174,4,16,4,11
	.byte	'EN',0,4
	.word	467
	.byte	32,0,2,35,0,0,36
	.byte	'Ifx_CPU_SPROT_ACCENA_Bits',0,29,177,4,3
	.word	61604
	.byte	10
	.byte	'_Ifx_CPU_SPROT_ACCENB_Bits',0,29,180,4,16,4,11
	.byte	'reserved_0',0,4
	.word	467
	.byte	32,0,2,35,0,0,36
	.byte	'Ifx_CPU_SPROT_ACCENB_Bits',0,29,183,4,3
	.word	61687
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,29,186,4,16,4,11
	.byte	'EN',0,4
	.word	467
	.byte	32,0,2,35,0,0,36
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,29,189,4,3
	.word	61778
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,29,192,4,16,4,11
	.byte	'reserved_0',0,4
	.word	467
	.byte	32,0,2,35,0,0,36
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,29,195,4,3
	.word	61869
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_LA_Bits',0,29,198,4,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	467
	.byte	27,0,2,35,0,0,36
	.byte	'Ifx_CPU_SPROT_RGN_LA_Bits',0,29,202,4,3
	.word	61968
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_UA_Bits',0,29,205,4,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	467
	.byte	27,0,2,35,0,0,36
	.byte	'Ifx_CPU_SPROT_RGN_UA_Bits',0,29,209,4,3
	.word	62075
	.byte	10
	.byte	'_Ifx_CPU_SWEVT_Bits',0,29,212,4,16,4,11
	.byte	'EVTA',0,4
	.word	490
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	490
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	490
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	490
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	490
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	490
	.byte	24,0,2,35,0,0,36
	.byte	'Ifx_CPU_SWEVT_Bits',0,29,220,4,3
	.word	62182
	.byte	10
	.byte	'_Ifx_CPU_SYSCON_Bits',0,29,223,4,16,4,11
	.byte	'FCDSF',0,4
	.word	490
	.byte	1,31,2,35,0,11
	.byte	'PROTEN',0,4
	.word	490
	.byte	1,30,2,35,0,11
	.byte	'TPROTEN',0,4
	.word	490
	.byte	1,29,2,35,0,11
	.byte	'IS',0,4
	.word	490
	.byte	1,28,2,35,0,11
	.byte	'IT',0,4
	.word	490
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	490
	.byte	27,0,2,35,0,0,36
	.byte	'Ifx_CPU_SYSCON_Bits',0,29,231,4,3
	.word	62336
	.byte	10
	.byte	'_Ifx_CPU_TASK_ASI_Bits',0,29,234,4,16,4,11
	.byte	'ASI',0,4
	.word	490
	.byte	5,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	490
	.byte	27,0,2,35,0,0,36
	.byte	'Ifx_CPU_TASK_ASI_Bits',0,29,238,4,3
	.word	62497
	.byte	10
	.byte	'_Ifx_CPU_TPS_CON_Bits',0,29,241,4,16,4,11
	.byte	'TEXP0',0,4
	.word	490
	.byte	1,31,2,35,0,11
	.byte	'TEXP1',0,4
	.word	490
	.byte	1,30,2,35,0,11
	.byte	'TEXP2',0,4
	.word	490
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	490
	.byte	13,16,2,35,0,11
	.byte	'TTRAP',0,4
	.word	490
	.byte	1,15,2,35,0,11
	.byte	'reserved_17',0,4
	.word	490
	.byte	15,0,2,35,0,0,36
	.byte	'Ifx_CPU_TPS_CON_Bits',0,29,249,4,3
	.word	62595
	.byte	10
	.byte	'_Ifx_CPU_TPS_TIMER_Bits',0,29,252,4,16,4,11
	.byte	'Timer',0,4
	.word	490
	.byte	32,0,2,35,0,0,36
	.byte	'Ifx_CPU_TPS_TIMER_Bits',0,29,255,4,3
	.word	62767
	.byte	10
	.byte	'_Ifx_CPU_TR_ADR_Bits',0,29,130,5,16,4,11
	.byte	'ADDR',0,4
	.word	490
	.byte	32,0,2,35,0,0,36
	.byte	'Ifx_CPU_TR_ADR_Bits',0,29,133,5,3
	.word	62847
	.byte	10
	.byte	'_Ifx_CPU_TR_EVT_Bits',0,29,136,5,16,4,11
	.byte	'EVTA',0,4
	.word	490
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	490
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	490
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	490
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	490
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	490
	.byte	4,20,2,35,0,11
	.byte	'TYP',0,4
	.word	490
	.byte	1,19,2,35,0,11
	.byte	'RNG',0,4
	.word	490
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	490
	.byte	1,17,2,35,0,11
	.byte	'ASI_EN',0,4
	.word	490
	.byte	1,16,2,35,0,11
	.byte	'ASI',0,4
	.word	490
	.byte	5,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	490
	.byte	6,5,2,35,0,11
	.byte	'AST',0,4
	.word	490
	.byte	1,4,2,35,0,11
	.byte	'ALD',0,4
	.word	490
	.byte	1,3,2,35,0,11
	.byte	'reserved_29',0,4
	.word	490
	.byte	3,0,2,35,0,0,36
	.byte	'Ifx_CPU_TR_EVT_Bits',0,29,153,5,3
	.word	62920
	.byte	10
	.byte	'_Ifx_CPU_TRIG_ACC_Bits',0,29,156,5,16,4,11
	.byte	'T0',0,4
	.word	490
	.byte	1,31,2,35,0,11
	.byte	'T1',0,4
	.word	490
	.byte	1,30,2,35,0,11
	.byte	'T2',0,4
	.word	490
	.byte	1,29,2,35,0,11
	.byte	'T3',0,4
	.word	490
	.byte	1,28,2,35,0,11
	.byte	'T4',0,4
	.word	490
	.byte	1,27,2,35,0,11
	.byte	'T5',0,4
	.word	490
	.byte	1,26,2,35,0,11
	.byte	'T6',0,4
	.word	490
	.byte	1,25,2,35,0,11
	.byte	'T7',0,4
	.word	490
	.byte	1,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	490
	.byte	24,0,2,35,0,0,36
	.byte	'Ifx_CPU_TRIG_ACC_Bits',0,29,167,5,3
	.word	63238
	.byte	12,29,175,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54456
	.byte	4,2,35,0,0,36
	.byte	'Ifx_CPU_A',0,29,180,5,3
	.word	63433
	.byte	12,29,183,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54517
	.byte	4,2,35,0,0,36
	.byte	'Ifx_CPU_BIV',0,29,188,5,3
	.word	63492
	.byte	12,29,191,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54596
	.byte	4,2,35,0,0,36
	.byte	'Ifx_CPU_BTV',0,29,196,5,3
	.word	63553
	.byte	12,29,199,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54682
	.byte	4,2,35,0,0,36
	.byte	'Ifx_CPU_CCNT',0,29,204,5,3
	.word	63614
	.byte	12,29,207,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54771
	.byte	4,2,35,0,0,36
	.byte	'Ifx_CPU_CCTRL',0,29,212,5,3
	.word	63676
	.byte	12,29,215,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54917
	.byte	4,2,35,0,0,36
	.byte	'Ifx_CPU_COMPAT',0,29,220,5,3
	.word	63739
	.byte	12,29,223,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55044
	.byte	4,2,35,0,0,36
	.byte	'Ifx_CPU_CORE_ID',0,29,228,5,3
	.word	63803
	.byte	12,29,231,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55142
	.byte	4,2,35,0,0,36
	.byte	'Ifx_CPU_CPR_L',0,29,236,5,3
	.word	63868
	.byte	12,29,239,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55235
	.byte	4,2,35,0,0,36
	.byte	'Ifx_CPU_CPR_U',0,29,244,5,3
	.word	63931
	.byte	12,29,247,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55328
	.byte	4,2,35,0,0,36
	.byte	'Ifx_CPU_CPU_ID',0,29,252,5,3
	.word	63994
	.byte	12,29,255,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55435
	.byte	4,2,35,0,0,36
	.byte	'Ifx_CPU_CPXE',0,29,132,6,3
	.word	64058
	.byte	12,29,135,6,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55522
	.byte	4,2,35,0,0,36
	.byte	'Ifx_CPU_CREVT',0,29,140,6,3
	.word	64120
	.byte	12,29,143,6,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55676
	.byte	4,2,35,0,0,36
	.byte	'Ifx_CPU_CUS_ID',0,29,148,6,3
	.word	64183
	.byte	12,29,151,6,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55770
	.byte	4,2,35,0,0,36
	.byte	'Ifx_CPU_D',0,29,156,6,3
	.word	64247
	.byte	12,29,159,6,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55833
	.byte	4,2,35,0,0,36
	.byte	'Ifx_CPU_DATR',0,29,164,6,3
	.word	64306
	.byte	12,29,167,6,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56051
	.byte	4,2,35,0,0,36
	.byte	'Ifx_CPU_DBGSR',0,29,172,6,3
	.word	64368
	.byte	12,29,175,6,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56266
	.byte	4,2,35,0,0,36
	.byte	'Ifx_CPU_DBGTCR',0,29,180,6,3
	.word	64431
	.byte	12,29,183,6,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56360
	.byte	4,2,35,0,0,36
	.byte	'Ifx_CPU_DCON0',0,29,188,6,3
	.word	64495
	.byte	12,29,191,6,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56476
	.byte	4,2,35,0,0,36
	.byte	'Ifx_CPU_DCON2',0,29,196,6,3
	.word	64558
	.byte	12,29,199,6,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56577
	.byte	4,2,35,0,0,36
	.byte	'Ifx_CPU_DCX',0,29,204,6,3
	.word	64621
	.byte	12,29,207,6,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56670
	.byte	4,2,35,0,0,36
	.byte	'Ifx_CPU_DEADD',0,29,212,6,3
	.word	64682
	.byte	12,29,215,6,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56750
	.byte	4,2,35,0,0,36
	.byte	'Ifx_CPU_DIEAR',0,29,220,6,3
	.word	64745
	.byte	12,29,223,6,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56819
	.byte	4,2,35,0,0,36
	.byte	'Ifx_CPU_DIETR',0,29,228,6,3
	.word	64808
	.byte	12,29,231,6,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57048
	.byte	4,2,35,0,0,36
	.byte	'Ifx_CPU_DMS',0,29,236,6,3
	.word	64871
	.byte	12,29,239,6,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57141
	.byte	4,2,35,0,0,36
	.byte	'Ifx_CPU_DPR_L',0,29,244,6,3
	.word	64932
	.byte	12,29,247,6,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57236
	.byte	4,2,35,0,0,36
	.byte	'Ifx_CPU_DPR_U',0,29,252,6,3
	.word	64995
	.byte	12,29,255,6,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57331
	.byte	4,2,35,0,0,36
	.byte	'Ifx_CPU_DPRE',0,29,132,7,3
	.word	65058
	.byte	12,29,135,7,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57421
	.byte	4,2,35,0,0,36
	.byte	'Ifx_CPU_DPWE',0,29,140,7,3
	.word	65120
	.byte	12,29,143,7,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57511
	.byte	4,2,35,0,0,36
	.byte	'Ifx_CPU_DSTR',0,29,148,7,3
	.word	65182
	.byte	12,29,151,7,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57835
	.byte	4,2,35,0,0,36
	.byte	'Ifx_CPU_EXEVT',0,29,156,7,3
	.word	65244
	.byte	12,29,159,7,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57989
	.byte	4,2,35,0,0,36
	.byte	'Ifx_CPU_FCX',0,29,164,7,3
	.word	65307
	.byte	12,29,167,7,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58095
	.byte	4,2,35,0,0,36
	.byte	'Ifx_CPU_FPU_TRAP_CON',0,29,172,7,3
	.word	65368
	.byte	12,29,175,7,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58444
	.byte	4,2,35,0,0,36
	.byte	'Ifx_CPU_FPU_TRAP_OPC',0,29,180,7,3
	.word	65438
	.byte	12,29,183,7,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58604
	.byte	4,2,35,0,0,36
	.byte	'Ifx_CPU_FPU_TRAP_PC',0,29,188,7,3
	.word	65508
	.byte	12,29,191,7,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58685
	.byte	4,2,35,0,0,36
	.byte	'Ifx_CPU_FPU_TRAP_SRC1',0,29,196,7,3
	.word	65577
	.byte	12,29,199,7,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58772
	.byte	4,2,35,0,0,36
	.byte	'Ifx_CPU_FPU_TRAP_SRC2',0,29,204,7,3
	.word	65648
	.byte	12,29,207,7,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58859
	.byte	4,2,35,0,0,36
	.byte	'Ifx_CPU_FPU_TRAP_SRC3',0,29,212,7,3
	.word	65719
	.byte	12,29,215,7,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58946
	.byte	4,2,35,0,0,36
	.byte	'Ifx_CPU_ICNT',0,29,220,7,3
	.word	65790
	.byte	12,29,223,7,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59037
	.byte	4,2,35,0,0,36
	.byte	'Ifx_CPU_ICR',0,29,228,7,3
	.word	65852
	.byte	12,29,231,7,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59180
	.byte	4,2,35,0,0,36
	.byte	'Ifx_CPU_ISP',0,29,236,7,3
	.word	65913
	.byte	12,29,239,7,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59246
	.byte	4,2,35,0,0,36
	.byte	'Ifx_CPU_LCX',0,29,244,7,3
	.word	65974
	.byte	12,29,247,7,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59352
	.byte	4,2,35,0,0,36
	.byte	'Ifx_CPU_M1CNT',0,29,252,7,3
	.word	66035
	.byte	12,29,255,7,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59445
	.byte	4,2,35,0,0,36
	.byte	'Ifx_CPU_M2CNT',0,29,132,8,3
	.word	66098
	.byte	12,29,135,8,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59538
	.byte	4,2,35,0,0,36
	.byte	'Ifx_CPU_M3CNT',0,29,140,8,3
	.word	66161
	.byte	12,29,143,8,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59631
	.byte	4,2,35,0,0,36
	.byte	'Ifx_CPU_PC',0,29,148,8,3
	.word	66224
	.byte	12,29,151,8,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59716
	.byte	4,2,35,0,0,36
	.byte	'Ifx_CPU_PCON0',0,29,156,8,3
	.word	66284
	.byte	12,29,159,8,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59832
	.byte	4,2,35,0,0,36
	.byte	'Ifx_CPU_PCON1',0,29,164,8,3
	.word	66347
	.byte	12,29,167,8,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59943
	.byte	4,2,35,0,0,36
	.byte	'Ifx_CPU_PCON2',0,29,172,8,3
	.word	66410
	.byte	12,29,175,8,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60044
	.byte	4,2,35,0,0,36
	.byte	'Ifx_CPU_PCXI',0,29,180,8,3
	.word	66473
	.byte	12,29,183,8,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60174
	.byte	4,2,35,0,0,36
	.byte	'Ifx_CPU_PIEAR',0,29,188,8,3
	.word	66535
	.byte	12,29,191,8,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60243
	.byte	4,2,35,0,0,36
	.byte	'Ifx_CPU_PIETR',0,29,196,8,3
	.word	66598
	.byte	12,29,199,8,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60472
	.byte	4,2,35,0,0,36
	.byte	'Ifx_CPU_PMA0',0,29,204,8,3
	.word	66661
	.byte	12,29,207,8,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60585
	.byte	4,2,35,0,0,36
	.byte	'Ifx_CPU_PMA1',0,29,212,8,3
	.word	66723
	.byte	12,29,215,8,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60698
	.byte	4,2,35,0,0,36
	.byte	'Ifx_CPU_PMA2',0,29,220,8,3
	.word	66785
	.byte	12,29,223,8,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60789
	.byte	4,2,35,0,0,36
	.byte	'Ifx_CPU_PSTR',0,29,228,8,3
	.word	66847
	.byte	12,29,231,8,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60992
	.byte	4,2,35,0,0,36
	.byte	'Ifx_CPU_PSW',0,29,236,8,3
	.word	66909
	.byte	12,29,239,8,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61235
	.byte	4,2,35,0,0,36
	.byte	'Ifx_CPU_SEGEN',0,29,244,8,3
	.word	66970
	.byte	12,29,247,8,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61363
	.byte	4,2,35,0,0,36
	.byte	'Ifx_CPU_SMACON',0,29,252,8,3
	.word	67033
	.byte	12,29,255,8,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61604
	.byte	4,2,35,0,0,36
	.byte	'Ifx_CPU_SPROT_ACCENA',0,29,132,9,3
	.word	67097
	.byte	12,29,135,9,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61687
	.byte	4,2,35,0,0,36
	.byte	'Ifx_CPU_SPROT_ACCENB',0,29,140,9,3
	.word	67167
	.byte	12,29,143,9,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61778
	.byte	4,2,35,0,0,36
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA',0,29,148,9,3
	.word	67237
	.byte	12,29,151,9,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61869
	.byte	4,2,35,0,0,36
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB',0,29,156,9,3
	.word	67311
	.byte	12,29,159,9,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61968
	.byte	4,2,35,0,0,36
	.byte	'Ifx_CPU_SPROT_RGN_LA',0,29,164,9,3
	.word	67385
	.byte	12,29,167,9,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62075
	.byte	4,2,35,0,0,36
	.byte	'Ifx_CPU_SPROT_RGN_UA',0,29,172,9,3
	.word	67455
	.byte	12,29,175,9,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62182
	.byte	4,2,35,0,0,36
	.byte	'Ifx_CPU_SWEVT',0,29,180,9,3
	.word	67525
	.byte	12,29,183,9,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62336
	.byte	4,2,35,0,0,36
	.byte	'Ifx_CPU_SYSCON',0,29,188,9,3
	.word	67588
	.byte	12,29,191,9,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62497
	.byte	4,2,35,0,0,36
	.byte	'Ifx_CPU_TASK_ASI',0,29,196,9,3
	.word	67652
	.byte	12,29,199,9,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62595
	.byte	4,2,35,0,0,36
	.byte	'Ifx_CPU_TPS_CON',0,29,204,9,3
	.word	67718
	.byte	12,29,207,9,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62767
	.byte	4,2,35,0,0,36
	.byte	'Ifx_CPU_TPS_TIMER',0,29,212,9,3
	.word	67783
	.byte	12,29,215,9,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62847
	.byte	4,2,35,0,0,36
	.byte	'Ifx_CPU_TR_ADR',0,29,220,9,3
	.word	67850
	.byte	12,29,223,9,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62920
	.byte	4,2,35,0,0,36
	.byte	'Ifx_CPU_TR_EVT',0,29,228,9,3
	.word	67914
	.byte	12,29,231,9,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63238
	.byte	4,2,35,0,0,36
	.byte	'Ifx_CPU_TRIG_ACC',0,29,236,9,3
	.word	67978
	.byte	10
	.byte	'_Ifx_CPU_CPR',0,29,247,9,25,8,13
	.byte	'L',0
	.word	63868
	.byte	4,2,35,0,13
	.byte	'U',0
	.word	63931
	.byte	4,2,35,4,0,14
	.word	68044
	.byte	36
	.byte	'Ifx_CPU_CPR',0,29,251,9,3
	.word	68086
	.byte	10
	.byte	'_Ifx_CPU_DPR',0,29,254,9,25,8,13
	.byte	'L',0
	.word	64932
	.byte	4,2,35,0,13
	.byte	'U',0
	.word	64995
	.byte	4,2,35,4,0,14
	.word	68112
	.byte	36
	.byte	'Ifx_CPU_DPR',0,29,130,10,3
	.word	68154
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN',0,29,133,10,25,16,13
	.byte	'LA',0
	.word	67385
	.byte	4,2,35,0,13
	.byte	'UA',0
	.word	67455
	.byte	4,2,35,4,13
	.byte	'ACCENA',0
	.word	67237
	.byte	4,2,35,8,13
	.byte	'ACCENB',0
	.word	67311
	.byte	4,2,35,12,0,14
	.word	68180
	.byte	36
	.byte	'Ifx_CPU_SPROT_RGN',0,29,139,10,3
	.word	68262
	.byte	18,12
	.word	67783
	.byte	19,2,0,10
	.byte	'_Ifx_CPU_TPS',0,29,142,10,25,16,13
	.byte	'CON',0
	.word	67718
	.byte	4,2,35,0,13
	.byte	'TIMER',0
	.word	68294
	.byte	12,2,35,4,0,14
	.word	68303
	.byte	36
	.byte	'Ifx_CPU_TPS',0,29,146,10,3
	.word	68351
	.byte	10
	.byte	'_Ifx_CPU_TR',0,29,149,10,25,8,13
	.byte	'EVT',0
	.word	67914
	.byte	4,2,35,0,13
	.byte	'ADR',0
	.word	67850
	.byte	4,2,35,4,0,14
	.word	68377
	.byte	36
	.byte	'Ifx_CPU_TR',0,29,153,10,3
	.word	68422
	.byte	18,176,32
	.word	642
	.byte	19,175,32,0,18,208,223,1
	.word	642
	.byte	19,207,223,1,0,18,248,1
	.word	642
	.byte	19,247,1,0,18,244,29
	.word	642
	.byte	19,243,29,0,18,188,3
	.word	642
	.byte	19,187,3,0,18,232,3
	.word	642
	.byte	19,231,3,0,18,252,23
	.word	642
	.byte	19,251,23,0,18,228,63
	.word	642
	.byte	19,227,63,0,18,128,1
	.word	68112
	.byte	19,15,0,14
	.word	68537
	.byte	18,128,31
	.word	642
	.byte	19,255,30,0,18,64
	.word	68044
	.byte	19,7,0,14
	.word	68563
	.byte	18,192,31
	.word	642
	.byte	19,191,31,0,18,16
	.word	64058
	.byte	19,3,0,18,16
	.word	65058
	.byte	19,3,0,18,16
	.word	65120
	.byte	19,3,0,18,208,7
	.word	642
	.byte	19,207,7,0,14
	.word	68303
	.byte	18,240,23
	.word	642
	.byte	19,239,23,0,18,64
	.word	68377
	.byte	19,7,0,14
	.word	68642
	.byte	18,192,23
	.word	642
	.byte	19,191,23,0,18,232,1
	.word	642
	.byte	19,231,1,0,18,180,1
	.word	642
	.byte	19,179,1,0,18,172,1
	.word	642
	.byte	19,171,1,0,18,64
	.word	64247
	.byte	19,15,0,18,64
	.word	642
	.byte	19,63,0,18,64
	.word	63433
	.byte	19,15,0,10
	.byte	'_Ifx_CPU',0,29,166,10,25,128,128,4,13
	.byte	'reserved_0',0
	.word	68447
	.byte	176,32,2,35,0,13
	.byte	'SEGEN',0
	.word	66970
	.byte	4,3,35,176,32,13
	.byte	'reserved_1034',0
	.word	68458
	.byte	208,223,1,3,35,180,32,13
	.byte	'TASK_ASI',0
	.word	67652
	.byte	4,4,35,132,128,2,13
	.byte	'reserved_8008',0
	.word	68471
	.byte	248,1,4,35,136,128,2,13
	.byte	'PMA0',0
	.word	66661
	.byte	4,4,35,128,130,2,13
	.byte	'PMA1',0
	.word	66723
	.byte	4,4,35,132,130,2,13
	.byte	'PMA2',0
	.word	66785
	.byte	4,4,35,136,130,2,13
	.byte	'reserved_810C',0
	.word	68482
	.byte	244,29,4,35,140,130,2,13
	.byte	'DCON2',0
	.word	64558
	.byte	4,4,35,128,160,2,13
	.byte	'reserved_9004',0
	.word	4832
	.byte	8,4,35,132,160,2,13
	.byte	'SMACON',0
	.word	67033
	.byte	4,4,35,140,160,2,13
	.byte	'DSTR',0
	.word	65182
	.byte	4,4,35,144,160,2,13
	.byte	'reserved_9014',0
	.word	3013
	.byte	4,4,35,148,160,2,13
	.byte	'DATR',0
	.word	64306
	.byte	4,4,35,152,160,2,13
	.byte	'DEADD',0
	.word	64682
	.byte	4,4,35,156,160,2,13
	.byte	'DIEAR',0
	.word	64745
	.byte	4,4,35,160,160,2,13
	.byte	'DIETR',0
	.word	64808
	.byte	4,4,35,164,160,2,13
	.byte	'reserved_9028',0
	.word	4203
	.byte	24,4,35,168,160,2,13
	.byte	'DCON0',0
	.word	64495
	.byte	4,4,35,192,160,2,13
	.byte	'reserved_9044',0
	.word	68493
	.byte	188,3,4,35,196,160,2,13
	.byte	'PSTR',0
	.word	66847
	.byte	4,4,35,128,164,2,13
	.byte	'PCON1',0
	.word	66347
	.byte	4,4,35,132,164,2,13
	.byte	'PCON2',0
	.word	66410
	.byte	4,4,35,136,164,2,13
	.byte	'PCON0',0
	.word	66284
	.byte	4,4,35,140,164,2,13
	.byte	'PIEAR',0
	.word	66535
	.byte	4,4,35,144,164,2,13
	.byte	'PIETR',0
	.word	66598
	.byte	4,4,35,148,164,2,13
	.byte	'reserved_9218',0
	.word	68504
	.byte	232,3,4,35,152,164,2,13
	.byte	'COMPAT',0
	.word	63739
	.byte	4,4,35,128,168,2,13
	.byte	'reserved_9404',0
	.word	68515
	.byte	252,23,4,35,132,168,2,13
	.byte	'FPU_TRAP_CON',0
	.word	65368
	.byte	4,4,35,128,192,2,13
	.byte	'FPU_TRAP_PC',0
	.word	65508
	.byte	4,4,35,132,192,2,13
	.byte	'FPU_TRAP_OPC',0
	.word	65438
	.byte	4,4,35,136,192,2,13
	.byte	'reserved_A00C',0
	.word	3013
	.byte	4,4,35,140,192,2,13
	.byte	'FPU_TRAP_SRC1',0
	.word	65577
	.byte	4,4,35,144,192,2,13
	.byte	'FPU_TRAP_SRC2',0
	.word	65648
	.byte	4,4,35,148,192,2,13
	.byte	'FPU_TRAP_SRC3',0
	.word	65719
	.byte	4,4,35,152,192,2,13
	.byte	'reserved_A01C',0
	.word	68526
	.byte	228,63,4,35,156,192,2,13
	.byte	'DPR',0
	.word	68547
	.byte	128,1,4,35,128,128,3,13
	.byte	'reserved_C080',0
	.word	68552
	.byte	128,31,4,35,128,129,3,13
	.byte	'CPR',0
	.word	68572
	.byte	64,4,35,128,160,3,13
	.byte	'reserved_D040',0
	.word	68577
	.byte	192,31,4,35,192,160,3,13
	.byte	'CPXE',0
	.word	68588
	.byte	16,4,35,128,192,3,13
	.byte	'DPRE',0
	.word	68597
	.byte	16,4,35,144,192,3,13
	.byte	'DPWE',0
	.word	68606
	.byte	16,4,35,160,192,3,13
	.byte	'reserved_E030',0
	.word	68615
	.byte	208,7,4,35,176,192,3,13
	.byte	'TPS',0
	.word	68626
	.byte	16,4,35,128,200,3,13
	.byte	'reserved_E410',0
	.word	68631
	.byte	240,23,4,35,144,200,3,13
	.byte	'TR',0
	.word	68651
	.byte	64,4,35,128,224,3,13
	.byte	'reserved_F040',0
	.word	68656
	.byte	192,23,4,35,192,224,3,13
	.byte	'CCTRL',0
	.word	63676
	.byte	4,4,35,128,248,3,13
	.byte	'CCNT',0
	.word	63614
	.byte	4,4,35,132,248,3,13
	.byte	'ICNT',0
	.word	65790
	.byte	4,4,35,136,248,3,13
	.byte	'M1CNT',0
	.word	66035
	.byte	4,4,35,140,248,3,13
	.byte	'M2CNT',0
	.word	66098
	.byte	4,4,35,144,248,3,13
	.byte	'M3CNT',0
	.word	66161
	.byte	4,4,35,148,248,3,13
	.byte	'reserved_FC18',0
	.word	68667
	.byte	232,1,4,35,152,248,3,13
	.byte	'DBGSR',0
	.word	64368
	.byte	4,4,35,128,250,3,13
	.byte	'reserved_FD04',0
	.word	3013
	.byte	4,4,35,132,250,3,13
	.byte	'EXEVT',0
	.word	65244
	.byte	4,4,35,136,250,3,13
	.byte	'CREVT',0
	.word	64120
	.byte	4,4,35,140,250,3,13
	.byte	'SWEVT',0
	.word	67525
	.byte	4,4,35,144,250,3,13
	.byte	'reserved_FD14',0
	.word	52397
	.byte	28,4,35,148,250,3,13
	.byte	'TRIG_ACC',0
	.word	67978
	.byte	4,4,35,176,250,3,13
	.byte	'reserved_FD34',0
	.word	5172
	.byte	12,4,35,180,250,3,13
	.byte	'DMS',0
	.word	64871
	.byte	4,4,35,192,250,3,13
	.byte	'DCX',0
	.word	64621
	.byte	4,4,35,196,250,3,13
	.byte	'DBGTCR',0
	.word	64431
	.byte	4,4,35,200,250,3,13
	.byte	'reserved_FD4C',0
	.word	68678
	.byte	180,1,4,35,204,250,3,13
	.byte	'PCXI',0
	.word	66473
	.byte	4,4,35,128,252,3,13
	.byte	'PSW',0
	.word	66909
	.byte	4,4,35,132,252,3,13
	.byte	'PC',0
	.word	66224
	.byte	4,4,35,136,252,3,13
	.byte	'reserved_FE0C',0
	.word	4832
	.byte	8,4,35,140,252,3,13
	.byte	'SYSCON',0
	.word	67588
	.byte	4,4,35,148,252,3,13
	.byte	'CPU_ID',0
	.word	63994
	.byte	4,4,35,152,252,3,13
	.byte	'CORE_ID',0
	.word	63803
	.byte	4,4,35,156,252,3,13
	.byte	'BIV',0
	.word	63492
	.byte	4,4,35,160,252,3,13
	.byte	'BTV',0
	.word	63553
	.byte	4,4,35,164,252,3,13
	.byte	'ISP',0
	.word	65913
	.byte	4,4,35,168,252,3,13
	.byte	'ICR',0
	.word	65852
	.byte	4,4,35,172,252,3,13
	.byte	'reserved_FE30',0
	.word	4832
	.byte	8,4,35,176,252,3,13
	.byte	'FCX',0
	.word	65307
	.byte	4,4,35,184,252,3,13
	.byte	'LCX',0
	.word	65974
	.byte	4,4,35,188,252,3,13
	.byte	'reserved_FE40',0
	.word	52388
	.byte	16,4,35,192,252,3,13
	.byte	'CUS_ID',0
	.word	64183
	.byte	4,4,35,208,252,3,13
	.byte	'reserved_FE54',0
	.word	68689
	.byte	172,1,4,35,212,252,3,13
	.byte	'D',0
	.word	68700
	.byte	64,4,35,128,254,3,13
	.byte	'reserved_FF40',0
	.word	68709
	.byte	64,4,35,192,254,3,13
	.byte	'A',0
	.word	68718
	.byte	64,4,35,128,255,3,13
	.byte	'reserved_FFC0',0
	.word	68709
	.byte	64,4,35,192,255,3,0,14
	.word	68727
	.byte	36
	.byte	'Ifx_CPU',0,29,130,11,3
	.word	70518
	.byte	15,6,127,9,1,16
	.byte	'IfxCpu_Id_0',0,0,16
	.byte	'IfxCpu_Id_1',0,1,16
	.byte	'IfxCpu_Id_none',0,2,0,36
	.byte	'IfxCpu_Id',0,6,132,1,3
	.word	70540
	.byte	36
	.byte	'IfxCpu_ResourceCpu',0,6,161,1,3
	.word	1454
	.byte	36
	.byte	'Ifx_SRC_SRCR_Bits',0,11,62,3
	.word	10574
	.byte	36
	.byte	'Ifx_SRC_SRCR',0,11,75,3
	.word	10864
	.byte	10
	.byte	'_Ifx_SRC_AGBT',0,11,86,25,4,13
	.byte	'SR',0
	.word	10864
	.byte	4,2,35,0,0,14
	.word	70685
	.byte	36
	.byte	'Ifx_SRC_AGBT',0,11,89,3
	.word	70717
	.byte	10
	.byte	'_Ifx_SRC_ASCLIN',0,11,92,25,12,13
	.byte	'TX',0
	.word	10864
	.byte	4,2,35,0,13
	.byte	'RX',0
	.word	10864
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	10864
	.byte	4,2,35,8,0,14
	.word	70743
	.byte	36
	.byte	'Ifx_SRC_ASCLIN',0,11,97,3
	.word	70802
	.byte	10
	.byte	'_Ifx_SRC_BCUSPB',0,11,100,25,4,13
	.byte	'SBSRC',0
	.word	10864
	.byte	4,2,35,0,0,14
	.word	70830
	.byte	36
	.byte	'Ifx_SRC_BCUSPB',0,11,103,3
	.word	70867
	.byte	18,64
	.word	10864
	.byte	19,15,0,10
	.byte	'_Ifx_SRC_CAN',0,11,106,25,64,13
	.byte	'INT',0
	.word	70895
	.byte	64,2,35,0,0,14
	.word	70904
	.byte	36
	.byte	'Ifx_SRC_CAN',0,11,109,3
	.word	70936
	.byte	10
	.byte	'_Ifx_SRC_CCU6',0,11,112,25,16,13
	.byte	'SR0',0
	.word	10864
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10864
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	10864
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	10864
	.byte	4,2,35,12,0,14
	.word	70961
	.byte	36
	.byte	'Ifx_SRC_CCU6',0,11,118,3
	.word	71033
	.byte	18,8
	.word	10864
	.byte	19,1,0,10
	.byte	'_Ifx_SRC_CERBERUS',0,11,121,25,8,13
	.byte	'SR',0
	.word	71059
	.byte	8,2,35,0,0,14
	.word	71068
	.byte	36
	.byte	'Ifx_SRC_CERBERUS',0,11,124,3
	.word	71104
	.byte	10
	.byte	'_Ifx_SRC_CIF',0,11,127,25,16,13
	.byte	'MI',0
	.word	10864
	.byte	4,2,35,0,13
	.byte	'MIEP',0
	.word	10864
	.byte	4,2,35,4,13
	.byte	'ISP',0
	.word	10864
	.byte	4,2,35,8,13
	.byte	'MJPEG',0
	.word	10864
	.byte	4,2,35,12,0,14
	.word	71134
	.byte	36
	.byte	'Ifx_SRC_CIF',0,11,133,1,3
	.word	71207
	.byte	10
	.byte	'_Ifx_SRC_CPU',0,11,136,1,25,4,13
	.byte	'SBSRC',0
	.word	10864
	.byte	4,2,35,0,0,14
	.word	71233
	.byte	36
	.byte	'Ifx_SRC_CPU',0,11,139,1,3
	.word	71268
	.byte	18,192,1
	.word	10864
	.byte	19,47,0,10
	.byte	'_Ifx_SRC_DMA',0,11,142,1,25,208,1,13
	.byte	'ERR',0
	.word	10864
	.byte	4,2,35,0,13
	.byte	'reserved_4',0
	.word	5172
	.byte	12,2,35,4,13
	.byte	'CH',0
	.word	71294
	.byte	192,1,2,35,16,0,14
	.word	71304
	.byte	36
	.byte	'Ifx_SRC_DMA',0,11,147,1,3
	.word	71371
	.byte	10
	.byte	'_Ifx_SRC_DSADC',0,11,150,1,25,8,13
	.byte	'SRM',0
	.word	10864
	.byte	4,2,35,0,13
	.byte	'SRA',0
	.word	10864
	.byte	4,2,35,4,0,14
	.word	71397
	.byte	36
	.byte	'Ifx_SRC_DSADC',0,11,154,1,3
	.word	71445
	.byte	10
	.byte	'_Ifx_SRC_EMEM',0,11,157,1,25,4,13
	.byte	'SR',0
	.word	10864
	.byte	4,2,35,0,0,14
	.word	71473
	.byte	36
	.byte	'Ifx_SRC_EMEM',0,11,160,1,3
	.word	71506
	.byte	10
	.byte	'_Ifx_SRC_ERAY',0,11,163,1,25,80,13
	.byte	'INT',0
	.word	71059
	.byte	8,2,35,0,13
	.byte	'TINT',0
	.word	71059
	.byte	8,2,35,8,13
	.byte	'NDAT',0
	.word	71059
	.byte	8,2,35,16,13
	.byte	'MBSC',0
	.word	71059
	.byte	8,2,35,24,13
	.byte	'OBUSY',0
	.word	10864
	.byte	4,2,35,32,13
	.byte	'IBUSY',0
	.word	10864
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	52406
	.byte	40,2,35,40,0,14
	.word	71533
	.byte	36
	.byte	'Ifx_SRC_ERAY',0,11,172,1,3
	.word	71660
	.byte	10
	.byte	'_Ifx_SRC_ETH',0,11,175,1,25,4,13
	.byte	'SR',0
	.word	10864
	.byte	4,2,35,0,0,14
	.word	71687
	.byte	36
	.byte	'Ifx_SRC_ETH',0,11,178,1,3
	.word	71719
	.byte	10
	.byte	'_Ifx_SRC_FCE',0,11,181,1,25,4,13
	.byte	'SR',0
	.word	10864
	.byte	4,2,35,0,0,14
	.word	71745
	.byte	36
	.byte	'Ifx_SRC_FCE',0,11,184,1,3
	.word	71777
	.byte	10
	.byte	'_Ifx_SRC_FFT',0,11,187,1,25,12,13
	.byte	'DONE',0
	.word	10864
	.byte	4,2,35,0,13
	.byte	'ERR',0
	.word	10864
	.byte	4,2,35,4,13
	.byte	'RFS',0
	.word	10864
	.byte	4,2,35,8,0,14
	.word	71803
	.byte	36
	.byte	'Ifx_SRC_FFT',0,11,192,1,3
	.word	71863
	.byte	10
	.byte	'_Ifx_SRC_GPSR',0,11,195,1,25,32,13
	.byte	'SR0',0
	.word	10864
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10864
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	10864
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	10864
	.byte	4,2,35,12,13
	.byte	'reserved_10',0
	.word	52388
	.byte	16,2,35,16,0,14
	.word	71889
	.byte	36
	.byte	'Ifx_SRC_GPSR',0,11,202,1,3
	.word	71983
	.byte	10
	.byte	'_Ifx_SRC_GPT12',0,11,205,1,25,48,13
	.byte	'CIRQ',0
	.word	10864
	.byte	4,2,35,0,13
	.byte	'T2',0
	.word	10864
	.byte	4,2,35,4,13
	.byte	'T3',0
	.word	10864
	.byte	4,2,35,8,13
	.byte	'T4',0
	.word	10864
	.byte	4,2,35,12,13
	.byte	'T5',0
	.word	10864
	.byte	4,2,35,16,13
	.byte	'T6',0
	.word	10864
	.byte	4,2,35,20,13
	.byte	'reserved_18',0
	.word	4203
	.byte	24,2,35,24,0,14
	.word	72010
	.byte	36
	.byte	'Ifx_SRC_GPT12',0,11,214,1,3
	.word	72127
	.byte	18,12
	.word	10864
	.byte	19,2,0,18,32
	.word	10864
	.byte	19,7,0,18,32
	.word	72164
	.byte	19,0,0,18,88
	.word	642
	.byte	19,87,0,18,108
	.word	10864
	.byte	19,26,0,18,96
	.word	642
	.byte	19,95,0,18,96
	.word	72164
	.byte	19,2,0,18,160,3
	.word	642
	.byte	19,159,3,0,18,64
	.word	72164
	.byte	19,1,0,18,192,3
	.word	642
	.byte	19,191,3,0,18,16
	.word	10864
	.byte	19,3,0,18,64
	.word	72249
	.byte	19,3,0,18,192,2
	.word	642
	.byte	19,191,2,0,18,52
	.word	642
	.byte	19,51,0,10
	.byte	'_Ifx_SRC_GTM',0,11,217,1,25,204,18,13
	.byte	'AEIIRQ',0
	.word	10864
	.byte	4,2,35,0,13
	.byte	'ARUIRQ',0
	.word	72155
	.byte	12,2,35,4,13
	.byte	'reserved_10',0
	.word	3013
	.byte	4,2,35,16,13
	.byte	'BRCIRQ',0
	.word	10864
	.byte	4,2,35,20,13
	.byte	'CMPIRQ',0
	.word	10864
	.byte	4,2,35,24,13
	.byte	'SPEIRQ',0
	.word	71059
	.byte	8,2,35,28,13
	.byte	'reserved_24',0
	.word	4832
	.byte	8,2,35,36,13
	.byte	'PSM',0
	.word	72173
	.byte	32,2,35,44,13
	.byte	'reserved_4C',0
	.word	72182
	.byte	88,2,35,76,13
	.byte	'DPLL',0
	.word	72191
	.byte	108,3,35,164,1,13
	.byte	'reserved_110',0
	.word	72200
	.byte	96,3,35,144,2,13
	.byte	'ERR',0
	.word	10864
	.byte	4,3,35,240,2,13
	.byte	'reserved_174',0
	.word	5172
	.byte	12,3,35,244,2,13
	.byte	'TIM',0
	.word	72209
	.byte	96,3,35,128,3,13
	.byte	'reserved_1E0',0
	.word	72218
	.byte	160,3,3,35,224,3,13
	.byte	'MCS',0
	.word	72209
	.byte	96,3,35,128,7,13
	.byte	'reserved_3E0',0
	.word	72218
	.byte	160,3,3,35,224,7,13
	.byte	'TOM',0
	.word	72229
	.byte	64,3,35,128,11,13
	.byte	'reserved_5C0',0
	.word	72238
	.byte	192,3,3,35,192,11,13
	.byte	'ATOM',0
	.word	72258
	.byte	64,3,35,128,15,13
	.byte	'reserved_7C0',0
	.word	72267
	.byte	192,2,3,35,192,15,13
	.byte	'MCSW0',0
	.word	72155
	.byte	12,3,35,128,18,13
	.byte	'reserved_90C',0
	.word	72278
	.byte	52,3,35,140,18,13
	.byte	'MCSW1',0
	.word	72155
	.byte	12,3,35,192,18,0,14
	.word	72287
	.byte	36
	.byte	'Ifx_SRC_GTM',0,11,243,1,3
	.word	72747
	.byte	10
	.byte	'_Ifx_SRC_HSCT',0,11,246,1,25,4,13
	.byte	'SR',0
	.word	10864
	.byte	4,2,35,0,0,14
	.word	72773
	.byte	36
	.byte	'Ifx_SRC_HSCT',0,11,249,1,3
	.word	72806
	.byte	10
	.byte	'_Ifx_SRC_HSSL',0,11,252,1,25,16,13
	.byte	'COK',0
	.word	10864
	.byte	4,2,35,0,13
	.byte	'RDI',0
	.word	10864
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	10864
	.byte	4,2,35,8,13
	.byte	'TRG',0
	.word	10864
	.byte	4,2,35,12,0,14
	.word	72833
	.byte	36
	.byte	'Ifx_SRC_HSSL',0,11,130,2,3
	.word	72906
	.byte	18,56
	.word	642
	.byte	19,55,0,10
	.byte	'_Ifx_SRC_I2C',0,11,133,2,25,80,13
	.byte	'BREQ',0
	.word	10864
	.byte	4,2,35,0,13
	.byte	'LBREQ',0
	.word	10864
	.byte	4,2,35,4,13
	.byte	'SREQ',0
	.word	10864
	.byte	4,2,35,8,13
	.byte	'LSREQ',0
	.word	10864
	.byte	4,2,35,12,13
	.byte	'ERR',0
	.word	10864
	.byte	4,2,35,16,13
	.byte	'P',0
	.word	10864
	.byte	4,2,35,20,13
	.byte	'reserved_18',0
	.word	72933
	.byte	56,2,35,24,0,14
	.word	72942
	.byte	36
	.byte	'Ifx_SRC_I2C',0,11,142,2,3
	.word	73065
	.byte	10
	.byte	'_Ifx_SRC_LMU',0,11,145,2,25,4,13
	.byte	'SR',0
	.word	10864
	.byte	4,2,35,0,0,14
	.word	73091
	.byte	36
	.byte	'Ifx_SRC_LMU',0,11,148,2,3
	.word	73123
	.byte	10
	.byte	'_Ifx_SRC_MSC',0,11,151,2,25,20,13
	.byte	'SR0',0
	.word	10864
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10864
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	10864
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	10864
	.byte	4,2,35,12,13
	.byte	'SR4',0
	.word	10864
	.byte	4,2,35,16,0,14
	.word	73149
	.byte	36
	.byte	'Ifx_SRC_MSC',0,11,158,2,3
	.word	73234
	.byte	10
	.byte	'_Ifx_SRC_PMU',0,11,161,2,25,4,13
	.byte	'SR',0
	.word	10864
	.byte	4,2,35,0,0,14
	.word	73260
	.byte	36
	.byte	'Ifx_SRC_PMU',0,11,164,2,3
	.word	73292
	.byte	10
	.byte	'_Ifx_SRC_PSI5',0,11,167,2,25,32,13
	.byte	'SR',0
	.word	72164
	.byte	32,2,35,0,0,14
	.word	73318
	.byte	36
	.byte	'Ifx_SRC_PSI5',0,11,170,2,3
	.word	73351
	.byte	10
	.byte	'_Ifx_SRC_PSI5S',0,11,173,2,25,32,13
	.byte	'SR',0
	.word	72164
	.byte	32,2,35,0,0,14
	.word	73378
	.byte	36
	.byte	'Ifx_SRC_PSI5S',0,11,176,2,3
	.word	73412
	.byte	10
	.byte	'_Ifx_SRC_QSPI',0,11,179,2,25,24,13
	.byte	'TX',0
	.word	10864
	.byte	4,2,35,0,13
	.byte	'RX',0
	.word	10864
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	10864
	.byte	4,2,35,8,13
	.byte	'PT',0
	.word	10864
	.byte	4,2,35,12,13
	.byte	'HC',0
	.word	10864
	.byte	4,2,35,16,13
	.byte	'U',0
	.word	10864
	.byte	4,2,35,20,0,14
	.word	73440
	.byte	36
	.byte	'Ifx_SRC_QSPI',0,11,187,2,3
	.word	73533
	.byte	10
	.byte	'_Ifx_SRC_SCR',0,11,190,2,25,4,13
	.byte	'SR',0
	.word	10864
	.byte	4,2,35,0,0,14
	.word	73560
	.byte	36
	.byte	'Ifx_SRC_SCR',0,11,193,2,3
	.word	73592
	.byte	10
	.byte	'_Ifx_SRC_SCU',0,11,196,2,25,20,13
	.byte	'DTS',0
	.word	10864
	.byte	4,2,35,0,13
	.byte	'ERU',0
	.word	72249
	.byte	16,2,35,4,0,14
	.word	73618
	.byte	36
	.byte	'Ifx_SRC_SCU',0,11,200,2,3
	.word	73664
	.byte	18,24
	.word	10864
	.byte	19,5,0,10
	.byte	'_Ifx_SRC_SENT',0,11,203,2,25,24,13
	.byte	'SR',0
	.word	73690
	.byte	24,2,35,0,0,14
	.word	73699
	.byte	36
	.byte	'Ifx_SRC_SENT',0,11,206,2,3
	.word	73732
	.byte	10
	.byte	'_Ifx_SRC_SMU',0,11,209,2,25,12,13
	.byte	'SR',0
	.word	72155
	.byte	12,2,35,0,0,14
	.word	73759
	.byte	36
	.byte	'Ifx_SRC_SMU',0,11,212,2,3
	.word	73791
	.byte	10
	.byte	'_Ifx_SRC_STM',0,11,215,2,25,8,13
	.byte	'SR0',0
	.word	10864
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10864
	.byte	4,2,35,4,0,14
	.word	73817
	.byte	36
	.byte	'Ifx_SRC_STM',0,11,219,2,3
	.word	73863
	.byte	10
	.byte	'_Ifx_SRC_VADCCG',0,11,222,2,25,16,13
	.byte	'SR0',0
	.word	10864
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10864
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	10864
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	10864
	.byte	4,2,35,12,0,14
	.word	73889
	.byte	36
	.byte	'Ifx_SRC_VADCCG',0,11,228,2,3
	.word	73964
	.byte	10
	.byte	'_Ifx_SRC_VADCG',0,11,231,2,25,16,13
	.byte	'SR0',0
	.word	10864
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10864
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	10864
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	10864
	.byte	4,2,35,12,0,14
	.word	73993
	.byte	36
	.byte	'Ifx_SRC_VADCG',0,11,237,2,3
	.word	74067
	.byte	10
	.byte	'_Ifx_SRC_XBAR',0,11,240,2,25,4,13
	.byte	'SRC',0
	.word	10864
	.byte	4,2,35,0,0,14
	.word	74095
	.byte	36
	.byte	'Ifx_SRC_XBAR',0,11,243,2,3
	.word	74129
	.byte	18,4
	.word	70685
	.byte	19,0,0,14
	.word	74156
	.byte	10
	.byte	'_Ifx_SRC_GAGBT',0,11,128,3,25,4,13
	.byte	'AGBT',0
	.word	74165
	.byte	4,2,35,0,0,14
	.word	74170
	.byte	36
	.byte	'Ifx_SRC_GAGBT',0,11,131,3,3
	.word	74206
	.byte	18,48
	.word	70743
	.byte	19,3,0,14
	.word	74234
	.byte	10
	.byte	'_Ifx_SRC_GASCLIN',0,11,134,3,25,48,13
	.byte	'ASCLIN',0
	.word	74243
	.byte	48,2,35,0,0,14
	.word	74248
	.byte	36
	.byte	'Ifx_SRC_GASCLIN',0,11,137,3,3
	.word	74288
	.byte	14
	.word	70830
	.byte	10
	.byte	'_Ifx_SRC_GBCU',0,11,140,3,25,4,13
	.byte	'SPB',0
	.word	74318
	.byte	4,2,35,0,0,14
	.word	74323
	.byte	36
	.byte	'Ifx_SRC_GBCU',0,11,143,3,3
	.word	74357
	.byte	18,64
	.word	70904
	.byte	19,0,0,14
	.word	74384
	.byte	10
	.byte	'_Ifx_SRC_GCAN',0,11,146,3,25,64,13
	.byte	'CAN',0
	.word	74393
	.byte	64,2,35,0,0,14
	.word	74398
	.byte	36
	.byte	'Ifx_SRC_GCAN',0,11,149,3,3
	.word	74432
	.byte	18,32
	.word	70961
	.byte	19,1,0,14
	.word	74459
	.byte	10
	.byte	'_Ifx_SRC_GCCU6',0,11,152,3,25,32,13
	.byte	'CCU6',0
	.word	74468
	.byte	32,2,35,0,0,14
	.word	74473
	.byte	36
	.byte	'Ifx_SRC_GCCU6',0,11,155,3,3
	.word	74509
	.byte	14
	.word	71068
	.byte	10
	.byte	'_Ifx_SRC_GCERBERUS',0,11,158,3,25,8,13
	.byte	'CERBERUS',0
	.word	74537
	.byte	8,2,35,0,0,14
	.word	74542
	.byte	36
	.byte	'Ifx_SRC_GCERBERUS',0,11,161,3,3
	.word	74586
	.byte	18,16
	.word	71134
	.byte	19,0,0,14
	.word	74618
	.byte	10
	.byte	'_Ifx_SRC_GCIF',0,11,164,3,25,16,13
	.byte	'CIF',0
	.word	74627
	.byte	16,2,35,0,0,14
	.word	74632
	.byte	36
	.byte	'Ifx_SRC_GCIF',0,11,167,3,3
	.word	74666
	.byte	18,8
	.word	71233
	.byte	19,1,0,14
	.word	74693
	.byte	10
	.byte	'_Ifx_SRC_GCPU',0,11,170,3,25,8,13
	.byte	'CPU',0
	.word	74702
	.byte	8,2,35,0,0,14
	.word	74707
	.byte	36
	.byte	'Ifx_SRC_GCPU',0,11,173,3,3
	.word	74741
	.byte	18,208,1
	.word	71304
	.byte	19,0,0,14
	.word	74768
	.byte	10
	.byte	'_Ifx_SRC_GDMA',0,11,176,3,25,208,1,13
	.byte	'DMA',0
	.word	74778
	.byte	208,1,2,35,0,0,14
	.word	74783
	.byte	36
	.byte	'Ifx_SRC_GDMA',0,11,179,3,3
	.word	74819
	.byte	14
	.word	71397
	.byte	14
	.word	71397
	.byte	14
	.word	71397
	.byte	10
	.byte	'_Ifx_SRC_GDSADC',0,11,182,3,25,32,13
	.byte	'DSADC0',0
	.word	74846
	.byte	8,2,35,0,13
	.byte	'reserved_8',0
	.word	4832
	.byte	8,2,35,8,13
	.byte	'DSADC2',0
	.word	74851
	.byte	8,2,35,16,13
	.byte	'DSADC3',0
	.word	74856
	.byte	8,2,35,24,0,14
	.word	74861
	.byte	36
	.byte	'Ifx_SRC_GDSADC',0,11,188,3,3
	.word	74952
	.byte	18,4
	.word	71473
	.byte	19,0,0,14
	.word	74981
	.byte	10
	.byte	'_Ifx_SRC_GEMEM',0,11,191,3,25,4,13
	.byte	'EMEM',0
	.word	74990
	.byte	4,2,35,0,0,14
	.word	74995
	.byte	36
	.byte	'Ifx_SRC_GEMEM',0,11,194,3,3
	.word	75031
	.byte	18,80
	.word	71533
	.byte	19,0,0,14
	.word	75059
	.byte	10
	.byte	'_Ifx_SRC_GERAY',0,11,197,3,25,80,13
	.byte	'ERAY',0
	.word	75068
	.byte	80,2,35,0,0,14
	.word	75073
	.byte	36
	.byte	'Ifx_SRC_GERAY',0,11,200,3,3
	.word	75109
	.byte	18,4
	.word	71687
	.byte	19,0,0,14
	.word	75137
	.byte	10
	.byte	'_Ifx_SRC_GETH',0,11,203,3,25,4,13
	.byte	'ETH',0
	.word	75146
	.byte	4,2,35,0,0,14
	.word	75151
	.byte	36
	.byte	'Ifx_SRC_GETH',0,11,206,3,3
	.word	75185
	.byte	18,4
	.word	71745
	.byte	19,0,0,14
	.word	75212
	.byte	10
	.byte	'_Ifx_SRC_GFCE',0,11,209,3,25,4,13
	.byte	'FCE',0
	.word	75221
	.byte	4,2,35,0,0,14
	.word	75226
	.byte	36
	.byte	'Ifx_SRC_GFCE',0,11,212,3,3
	.word	75260
	.byte	18,12
	.word	71803
	.byte	19,0,0,14
	.word	75287
	.byte	10
	.byte	'_Ifx_SRC_GFFT',0,11,215,3,25,12,13
	.byte	'FFT',0
	.word	75296
	.byte	12,2,35,0,0,14
	.word	75301
	.byte	36
	.byte	'Ifx_SRC_GFFT',0,11,218,3,3
	.word	75335
	.byte	18,64
	.word	71889
	.byte	19,1,0,14
	.word	75362
	.byte	10
	.byte	'_Ifx_SRC_GGPSR',0,11,221,3,25,64,13
	.byte	'GPSR',0
	.word	75371
	.byte	64,2,35,0,0,14
	.word	75376
	.byte	36
	.byte	'Ifx_SRC_GGPSR',0,11,224,3,3
	.word	75412
	.byte	18,48
	.word	72010
	.byte	19,0,0,14
	.word	75440
	.byte	10
	.byte	'_Ifx_SRC_GGPT12',0,11,227,3,25,48,13
	.byte	'GPT12',0
	.word	75449
	.byte	48,2,35,0,0,14
	.word	75454
	.byte	36
	.byte	'Ifx_SRC_GGPT12',0,11,230,3,3
	.word	75492
	.byte	18,204,18
	.word	72287
	.byte	19,0,0,14
	.word	75521
	.byte	10
	.byte	'_Ifx_SRC_GGTM',0,11,233,3,25,204,18,13
	.byte	'GTM',0
	.word	75531
	.byte	204,18,2,35,0,0,14
	.word	75536
	.byte	36
	.byte	'Ifx_SRC_GGTM',0,11,236,3,3
	.word	75572
	.byte	18,4
	.word	72773
	.byte	19,0,0,14
	.word	75599
	.byte	10
	.byte	'_Ifx_SRC_GHSCT',0,11,239,3,25,4,13
	.byte	'HSCT',0
	.word	75608
	.byte	4,2,35,0,0,14
	.word	75613
	.byte	36
	.byte	'Ifx_SRC_GHSCT',0,11,242,3,3
	.word	75649
	.byte	18,64
	.word	72833
	.byte	19,3,0,14
	.word	75677
	.byte	10
	.byte	'_Ifx_SRC_GHSSL',0,11,245,3,25,68,13
	.byte	'HSSL',0
	.word	75686
	.byte	64,2,35,0,13
	.byte	'EXI',0
	.word	10864
	.byte	4,2,35,64,0,14
	.word	75691
	.byte	36
	.byte	'Ifx_SRC_GHSSL',0,11,249,3,3
	.word	75740
	.byte	18,80
	.word	72942
	.byte	19,0,0,14
	.word	75768
	.byte	10
	.byte	'_Ifx_SRC_GI2C',0,11,252,3,25,80,13
	.byte	'I2C',0
	.word	75777
	.byte	80,2,35,0,0,14
	.word	75782
	.byte	36
	.byte	'Ifx_SRC_GI2C',0,11,255,3,3
	.word	75816
	.byte	18,4
	.word	73091
	.byte	19,0,0,14
	.word	75843
	.byte	10
	.byte	'_Ifx_SRC_GLMU',0,11,130,4,25,4,13
	.byte	'LMU',0
	.word	75852
	.byte	4,2,35,0,0,14
	.word	75857
	.byte	36
	.byte	'Ifx_SRC_GLMU',0,11,133,4,3
	.word	75891
	.byte	18,40
	.word	73149
	.byte	19,1,0,14
	.word	75918
	.byte	10
	.byte	'_Ifx_SRC_GMSC',0,11,136,4,25,40,13
	.byte	'MSC',0
	.word	75927
	.byte	40,2,35,0,0,14
	.word	75932
	.byte	36
	.byte	'Ifx_SRC_GMSC',0,11,139,4,3
	.word	75966
	.byte	18,8
	.word	73260
	.byte	19,1,0,14
	.word	75993
	.byte	10
	.byte	'_Ifx_SRC_GPMU',0,11,142,4,25,8,13
	.byte	'PMU',0
	.word	76002
	.byte	8,2,35,0,0,14
	.word	76007
	.byte	36
	.byte	'Ifx_SRC_GPMU',0,11,145,4,3
	.word	76041
	.byte	18,32
	.word	73318
	.byte	19,0,0,14
	.word	76068
	.byte	10
	.byte	'_Ifx_SRC_GPSI5',0,11,148,4,25,32,13
	.byte	'PSI5',0
	.word	76077
	.byte	32,2,35,0,0,14
	.word	76082
	.byte	36
	.byte	'Ifx_SRC_GPSI5',0,11,151,4,3
	.word	76118
	.byte	18,32
	.word	73378
	.byte	19,0,0,14
	.word	76146
	.byte	10
	.byte	'_Ifx_SRC_GPSI5S',0,11,154,4,25,32,13
	.byte	'PSI5S',0
	.word	76155
	.byte	32,2,35,0,0,14
	.word	76160
	.byte	36
	.byte	'Ifx_SRC_GPSI5S',0,11,157,4,3
	.word	76198
	.byte	18,96
	.word	73440
	.byte	19,3,0,14
	.word	76227
	.byte	10
	.byte	'_Ifx_SRC_GQSPI',0,11,160,4,25,96,13
	.byte	'QSPI',0
	.word	76236
	.byte	96,2,35,0,0,14
	.word	76241
	.byte	36
	.byte	'Ifx_SRC_GQSPI',0,11,163,4,3
	.word	76277
	.byte	18,4
	.word	73560
	.byte	19,0,0,14
	.word	76305
	.byte	10
	.byte	'_Ifx_SRC_GSCR',0,11,166,4,25,4,13
	.byte	'SCR',0
	.word	76314
	.byte	4,2,35,0,0,14
	.word	76319
	.byte	36
	.byte	'Ifx_SRC_GSCR',0,11,169,4,3
	.word	76353
	.byte	14
	.word	73618
	.byte	10
	.byte	'_Ifx_SRC_GSCU',0,11,172,4,25,20,13
	.byte	'SCU',0
	.word	76380
	.byte	20,2,35,0,0,14
	.word	76385
	.byte	36
	.byte	'Ifx_SRC_GSCU',0,11,175,4,3
	.word	76419
	.byte	18,24
	.word	73699
	.byte	19,0,0,14
	.word	76446
	.byte	10
	.byte	'_Ifx_SRC_GSENT',0,11,178,4,25,24,13
	.byte	'SENT',0
	.word	76455
	.byte	24,2,35,0,0,14
	.word	76460
	.byte	36
	.byte	'Ifx_SRC_GSENT',0,11,181,4,3
	.word	76496
	.byte	18,12
	.word	73759
	.byte	19,0,0,14
	.word	76524
	.byte	10
	.byte	'_Ifx_SRC_GSMU',0,11,184,4,25,12,13
	.byte	'SMU',0
	.word	76533
	.byte	12,2,35,0,0,14
	.word	76538
	.byte	36
	.byte	'Ifx_SRC_GSMU',0,11,187,4,3
	.word	76572
	.byte	18,16
	.word	73817
	.byte	19,1,0,14
	.word	76599
	.byte	10
	.byte	'_Ifx_SRC_GSTM',0,11,190,4,25,16,13
	.byte	'STM',0
	.word	76608
	.byte	16,2,35,0,0,14
	.word	76613
	.byte	36
	.byte	'Ifx_SRC_GSTM',0,11,193,4,3
	.word	76647
	.byte	18,64
	.word	73993
	.byte	19,3,0,14
	.word	76674
	.byte	18,224,1
	.word	642
	.byte	19,223,1,0,18,32
	.word	73889
	.byte	19,1,0,14
	.word	76699
	.byte	10
	.byte	'_Ifx_SRC_GVADC',0,11,196,4,25,192,2,13
	.byte	'G',0
	.word	76683
	.byte	64,2,35,0,13
	.byte	'reserved_40',0
	.word	76688
	.byte	224,1,2,35,64,13
	.byte	'CG',0
	.word	76708
	.byte	32,3,35,160,2,0,14
	.word	76713
	.byte	36
	.byte	'Ifx_SRC_GVADC',0,11,201,4,3
	.word	76782
	.byte	14
	.word	74095
	.byte	10
	.byte	'_Ifx_SRC_GXBAR',0,11,204,4,25,4,13
	.byte	'XBAR',0
	.word	76810
	.byte	4,2,35,0,0,14
	.word	76815
	.byte	36
	.byte	'Ifx_SRC_GXBAR',0,11,207,4,3
	.word	76851
	.byte	36
	.byte	'Ifx_STM_ACCEN0_Bits',0,16,79,3
	.word	20794
	.byte	36
	.byte	'Ifx_STM_ACCEN1_Bits',0,16,85,3
	.word	20705
	.byte	36
	.byte	'Ifx_STM_CAP_Bits',0,16,91,3
	.word	19235
	.byte	36
	.byte	'Ifx_STM_CAPSV_Bits',0,16,97,3
	.word	20112
	.byte	36
	.byte	'Ifx_STM_CLC_Bits',0,16,107,3
	.word	18358
	.byte	36
	.byte	'Ifx_STM_CMCON_Bits',0,16,120,3
	.word	19413
	.byte	36
	.byte	'Ifx_STM_CMP_Bits',0,16,126,3
	.word	19322
	.byte	36
	.byte	'Ifx_STM_ICR_Bits',0,16,139,1,3
	.word	19644
	.byte	36
	.byte	'Ifx_STM_ID_Bits',0,16,147,1,3
	.word	18514
	.byte	36
	.byte	'Ifx_STM_ISCR_Bits',0,16,157,1,3
	.word	19861
	.byte	36
	.byte	'Ifx_STM_KRST0_Bits',0,16,165,1,3
	.word	20582
	.byte	36
	.byte	'Ifx_STM_KRST1_Bits',0,16,172,1,3
	.word	20478
	.byte	36
	.byte	'Ifx_STM_KRSTCLR_Bits',0,16,179,1,3
	.word	20372
	.byte	36
	.byte	'Ifx_STM_OCS_Bits',0,16,189,1,3
	.word	20212
	.byte	36
	.byte	'Ifx_STM_TIM0_Bits',0,16,195,1,3
	.word	18636
	.byte	36
	.byte	'Ifx_STM_TIM0SV_Bits',0,16,201,1,3
	.word	20025
	.byte	36
	.byte	'Ifx_STM_TIM1_Bits',0,16,207,1,3
	.word	18721
	.byte	36
	.byte	'Ifx_STM_TIM2_Bits',0,16,213,1,3
	.word	18806
	.byte	36
	.byte	'Ifx_STM_TIM3_Bits',0,16,219,1,3
	.word	18891
	.byte	36
	.byte	'Ifx_STM_TIM4_Bits',0,16,225,1,3
	.word	18977
	.byte	36
	.byte	'Ifx_STM_TIM5_Bits',0,16,231,1,3
	.word	19063
	.byte	36
	.byte	'Ifx_STM_TIM6_Bits',0,16,237,1,3
	.word	19149
	.byte	36
	.byte	'Ifx_STM_ACCEN0',0,16,250,1,3
	.word	21323
	.byte	36
	.byte	'Ifx_STM_ACCEN1',0,16,130,2,3
	.word	20754
	.byte	36
	.byte	'Ifx_STM_CAP',0,16,138,2,3
	.word	19282
	.byte	36
	.byte	'Ifx_STM_CAPSV',0,16,146,2,3
	.word	20161
	.byte	36
	.byte	'Ifx_STM_CLC',0,16,154,2,3
	.word	18474
	.byte	36
	.byte	'Ifx_STM_CMCON',0,16,162,2,3
	.word	19604
	.byte	36
	.byte	'Ifx_STM_CMP',0,16,170,2,3
	.word	19364
	.byte	36
	.byte	'Ifx_STM_ICR',0,16,178,2,3
	.word	19821
	.byte	36
	.byte	'Ifx_STM_ID',0,16,186,2,3
	.word	18596
	.byte	36
	.byte	'Ifx_STM_ISCR',0,16,194,2,3
	.word	19985
	.byte	36
	.byte	'Ifx_STM_KRST0',0,16,202,2,3
	.word	20665
	.byte	36
	.byte	'Ifx_STM_KRST1',0,16,210,2,3
	.word	20542
	.byte	36
	.byte	'Ifx_STM_KRSTCLR',0,16,218,2,3
	.word	20438
	.byte	36
	.byte	'Ifx_STM_OCS',0,16,226,2,3
	.word	20332
	.byte	36
	.byte	'Ifx_STM_TIM0',0,16,234,2,3
	.word	18681
	.byte	36
	.byte	'Ifx_STM_TIM0SV',0,16,242,2,3
	.word	20072
	.byte	36
	.byte	'Ifx_STM_TIM1',0,16,250,2,3
	.word	18766
	.byte	36
	.byte	'Ifx_STM_TIM2',0,16,130,3,3
	.word	18851
	.byte	36
	.byte	'Ifx_STM_TIM3',0,16,138,3,3
	.word	18937
	.byte	36
	.byte	'Ifx_STM_TIM4',0,16,146,3,3
	.word	19023
	.byte	36
	.byte	'Ifx_STM_TIM5',0,16,154,3,3
	.word	19109
	.byte	36
	.byte	'Ifx_STM_TIM6',0,16,162,3,3
	.word	19195
	.byte	14
	.word	21363
	.byte	36
	.byte	'Ifx_STM',0,16,201,3,3
	.word	77962
	.byte	15,5,144,1,9,1,16
	.byte	'IfxCpu_CounterMode_normal',0,0,16
	.byte	'IfxCpu_CounterMode_task',0,1,0,36
	.byte	'IfxCpu_CounterMode',0,5,148,1,3
	.word	77984
	.byte	20,5,160,1,9,6,13
	.byte	'counter',0
	.word	1776
	.byte	4,2,35,0,13
	.byte	'overlfow',0
	.word	642
	.byte	1,2,35,4,0,36
	.byte	'IfxCpu_Counter',0,5,164,1,3
	.word	78073
	.byte	20,5,172,1,9,32,13
	.byte	'instruction',0
	.word	78073
	.byte	6,2,35,0,13
	.byte	'clock',0
	.word	78073
	.byte	6,2,35,6,13
	.byte	'counter1',0
	.word	78073
	.byte	6,2,35,12,13
	.byte	'counter2',0
	.word	78073
	.byte	6,2,35,18,13
	.byte	'counter3',0
	.word	78073
	.byte	6,2,35,24,0,36
	.byte	'IfxCpu_Perf',0,5,179,1,3
	.word	78139
	.byte	10
	.byte	'_Ifx_FLASH_ACCEN0_Bits',0,30,45,16,4,11
	.byte	'EN0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	642
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	642
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	642
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	642
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	642
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	642
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	642
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	642
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	642
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	642
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	642
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	642
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	642
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	642
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	642
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	642
	.byte	1,0,2,35,3,0,36
	.byte	'Ifx_FLASH_ACCEN0_Bits',0,30,79,3
	.word	78257
	.byte	10
	.byte	'_Ifx_FLASH_ACCEN1_Bits',0,30,82,16,4,11
	.byte	'reserved_0',0,4
	.word	467
	.byte	32,0,2,35,0,0,36
	.byte	'Ifx_FLASH_ACCEN1_Bits',0,30,85,3
	.word	78818
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_CFG_Bits',0,30,88,16,4,11
	.byte	'SEL',0,1
	.word	642
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	642
	.byte	2,0,2,35,0,11
	.byte	'CLR',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'DIS',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	467
	.byte	22,0,2,35,0,0,36
	.byte	'Ifx_FLASH_CBAB_CFG_Bits',0,30,95,3
	.word	78899
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_STAT_Bits',0,30,98,16,4,11
	.byte	'VLD0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'VLD1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'VLD2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'VLD3',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'VLD4',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'VLD5',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'VLD6',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'VLD7',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'VLD8',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'VLD9',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	467
	.byte	22,0,2,35,0,0,36
	.byte	'Ifx_FLASH_CBAB_STAT_Bits',0,30,111,3
	.word	79052
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_TOP_Bits',0,30,114,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	467
	.byte	19,8,2,35,0,11
	.byte	'ERR',0,1
	.word	642
	.byte	6,2,2,35,3,11
	.byte	'VLD',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'CLR',0,1
	.word	642
	.byte	1,0,2,35,3,0,36
	.byte	'Ifx_FLASH_CBAB_TOP_Bits',0,30,121,3
	.word	79300
	.byte	10
	.byte	'_Ifx_FLASH_COMM0_Bits',0,30,124,16,4,11
	.byte	'STATUS',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	467
	.byte	24,0,2,35,0,0,36
	.byte	'Ifx_FLASH_COMM0_Bits',0,30,128,1,3
	.word	79446
	.byte	10
	.byte	'_Ifx_FLASH_COMM1_Bits',0,30,131,1,16,4,11
	.byte	'STATUS',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'DATA',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	659
	.byte	16,0,2,35,2,0,36
	.byte	'Ifx_FLASH_COMM1_Bits',0,30,136,1,3
	.word	79544
	.byte	10
	.byte	'_Ifx_FLASH_COMM2_Bits',0,30,139,1,16,4,11
	.byte	'STATUS',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'DATA',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	659
	.byte	16,0,2,35,2,0,36
	.byte	'Ifx_FLASH_COMM2_Bits',0,30,144,1,3
	.word	79660
	.byte	10
	.byte	'_Ifx_FLASH_ECCRD_Bits',0,30,147,1,16,4,11
	.byte	'RCODE',0,4
	.word	467
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	659
	.byte	8,2,2,35,2,11
	.byte	'EDCERRINJ',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'ECCORDIS',0,1
	.word	642
	.byte	1,0,2,35,3,0,36
	.byte	'Ifx_FLASH_ECCRD_Bits',0,30,153,1,3
	.word	79776
	.byte	10
	.byte	'_Ifx_FLASH_ECCRP_Bits',0,30,156,1,16,4,11
	.byte	'RCODE',0,4
	.word	467
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	659
	.byte	8,2,2,35,2,11
	.byte	'EDCERRINJ',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'ECCORDIS',0,1
	.word	642
	.byte	1,0,2,35,3,0,36
	.byte	'Ifx_FLASH_ECCRP_Bits',0,30,162,1,3
	.word	79916
	.byte	10
	.byte	'_Ifx_FLASH_ECCW_Bits',0,30,165,1,16,4,11
	.byte	'WCODE',0,4
	.word	467
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	659
	.byte	8,2,2,35,2,11
	.byte	'DECENCDIS',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'PECENCDIS',0,1
	.word	642
	.byte	1,0,2,35,3,0,36
	.byte	'Ifx_FLASH_ECCW_Bits',0,30,171,1,3
	.word	80056
	.byte	10
	.byte	'_Ifx_FLASH_FCON_Bits',0,30,174,1,16,4,11
	.byte	'WSPFLASH',0,1
	.word	642
	.byte	4,4,2,35,0,11
	.byte	'WSECPF',0,1
	.word	642
	.byte	2,2,2,35,0,11
	.byte	'WSDFLASH',0,2
	.word	659
	.byte	6,4,2,35,0,11
	.byte	'WSECDF',0,1
	.word	642
	.byte	3,1,2,35,1,11
	.byte	'IDLE',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'ESLDIS',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'SLEEP',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'NSAFECC',0,1
	.word	642
	.byte	1,5,2,35,2,11
	.byte	'STALL',0,1
	.word	642
	.byte	1,4,2,35,2,11
	.byte	'RES21',0,1
	.word	642
	.byte	2,2,2,35,2,11
	.byte	'RES23',0,1
	.word	642
	.byte	2,0,2,35,2,11
	.byte	'VOPERM',0,1
	.word	642
	.byte	1,7,2,35,3,11
	.byte	'SQERM',0,1
	.word	642
	.byte	1,6,2,35,3,11
	.byte	'PROERM',0,1
	.word	642
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	642
	.byte	3,2,2,35,3,11
	.byte	'PR5V',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'EOBM',0,1
	.word	642
	.byte	1,0,2,35,3,0,36
	.byte	'Ifx_FLASH_FCON_Bits',0,30,193,1,3
	.word	80195
	.byte	10
	.byte	'_Ifx_FLASH_FPRO_Bits',0,30,196,1,16,4,11
	.byte	'PROINP',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'PRODISP',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'PROIND',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'PRODISD',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'PROINHSMCOTP',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'RES5',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'PROINOTP',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'RES7',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'PROINDBG',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'PRODISDBG',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'PROINHSM',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	642
	.byte	5,0,2,35,1,11
	.byte	'DCFP',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'DDFP',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'DDFPX',0,1
	.word	642
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	642
	.byte	1,4,2,35,2,11
	.byte	'DDFD',0,1
	.word	642
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	642
	.byte	1,2,2,35,2,11
	.byte	'ENPE',0,1
	.word	642
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	642
	.byte	8,0,2,35,3,0,36
	.byte	'Ifx_FLASH_FPRO_Bits',0,30,218,1,3
	.word	80557
	.byte	10
	.byte	'_Ifx_FLASH_FSR_Bits',0,30,221,1,16,4,11
	.byte	'FABUSY',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'D0BUSY',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'RES1',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'P0BUSY',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'P1BUSY',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'RES5',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'RES6',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'PROG',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'ERASE',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'PFPAGE',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'DFPAGE',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'OPER',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'SQER',0,1
	.word	642
	.byte	1,3,2,35,1,11
	.byte	'PROER',0,1
	.word	642
	.byte	1,2,2,35,1,11
	.byte	'PFSBER',0,1
	.word	642
	.byte	1,1,2,35,1,11
	.byte	'PFDBER',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'PFMBER',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'RES17',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'DFSBER',0,1
	.word	642
	.byte	1,5,2,35,2,11
	.byte	'DFDBER',0,1
	.word	642
	.byte	1,4,2,35,2,11
	.byte	'DFTBER',0,1
	.word	642
	.byte	1,3,2,35,2,11
	.byte	'DFMBER',0,1
	.word	642
	.byte	1,2,2,35,2,11
	.byte	'SRIADDERR',0,1
	.word	642
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	659
	.byte	2,7,2,35,2,11
	.byte	'PVER',0,1
	.word	642
	.byte	1,6,2,35,3,11
	.byte	'EVER',0,1
	.word	642
	.byte	1,5,2,35,3,11
	.byte	'SPND',0,1
	.word	642
	.byte	1,4,2,35,3,11
	.byte	'SLM',0,1
	.word	642
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	642
	.byte	1,2,2,35,3,11
	.byte	'ORIER',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	642
	.byte	1,0,2,35,3,0,36
	.byte	'Ifx_FLASH_FSR_Bits',0,30,254,1,3
	.word	80998
	.byte	10
	.byte	'_Ifx_FLASH_ID_Bits',0,30,129,2,16,4,11
	.byte	'MODREV',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	659
	.byte	16,0,2,35,2,0,36
	.byte	'Ifx_FLASH_ID_Bits',0,30,134,2,3
	.word	81604
	.byte	10
	.byte	'_Ifx_FLASH_MARD_Bits',0,30,137,2,16,4,11
	.byte	'HMARGIN',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'SELD0',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'SPND',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'SPNDERR',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,2
	.word	659
	.byte	10,1,2,35,0,11
	.byte	'TRAPDIS',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	659
	.byte	16,0,2,35,2,0,36
	.byte	'Ifx_FLASH_MARD_Bits',0,30,147,2,3
	.word	81715
	.byte	10
	.byte	'_Ifx_FLASH_MARP_Bits',0,30,150,2,16,4,11
	.byte	'SELP0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'SELP1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'RES2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'RES3',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	659
	.byte	11,1,2,35,0,11
	.byte	'TRAPDIS',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	659
	.byte	16,0,2,35,2,0,36
	.byte	'Ifx_FLASH_MARP_Bits',0,30,159,2,3
	.word	81929
	.byte	10
	.byte	'_Ifx_FLASH_PROCOND_Bits',0,30,162,2,16,4,11
	.byte	'L',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'NSAFECC',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'RAMIN',0,1
	.word	642
	.byte	2,4,2,35,0,11
	.byte	'RAMINSEL',0,1
	.word	642
	.byte	4,0,2,35,0,11
	.byte	'OSCCFG',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'MODE',0,1
	.word	642
	.byte	2,5,2,35,1,11
	.byte	'APREN',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'CAP0EN',0,1
	.word	642
	.byte	1,3,2,35,1,11
	.byte	'CAP1EN',0,1
	.word	642
	.byte	1,2,2,35,1,11
	.byte	'CAP2EN',0,1
	.word	642
	.byte	1,1,2,35,1,11
	.byte	'CAP3EN',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'ESR0CNT',0,2
	.word	659
	.byte	12,4,2,35,2,11
	.byte	'RES29',0,1
	.word	642
	.byte	2,2,2,35,3,11
	.byte	'RES30',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'RPRO',0,1
	.word	642
	.byte	1,0,2,35,3,0,36
	.byte	'Ifx_FLASH_PROCOND_Bits',0,30,179,2,3
	.word	82116
	.byte	10
	.byte	'_Ifx_FLASH_PROCONDBG_Bits',0,30,182,2,16,4,11
	.byte	'OCDSDIS',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'DBGIFLCK',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'EDM',0,1
	.word	642
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	467
	.byte	28,0,2,35,0,0,36
	.byte	'Ifx_FLASH_PROCONDBG_Bits',0,30,188,2,3
	.word	82440
	.byte	10
	.byte	'_Ifx_FLASH_PROCONHSM_Bits',0,30,191,2,16,4,11
	.byte	'HSMDBGDIS',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'DBGIFLCK',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'TSTIFLCK',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'HSMTSTDIS',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'RES15',0,2
	.word	659
	.byte	12,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	659
	.byte	16,0,2,35,2,0,36
	.byte	'Ifx_FLASH_PROCONHSM_Bits',0,30,199,2,3
	.word	82583
	.byte	10
	.byte	'_Ifx_FLASH_PROCONHSMCOTP_Bits',0,30,202,2,16,4,11
	.byte	'HSMBOOTEN',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'SSWWAIT',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'HSMDX',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'HSM6X',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'HSM16X',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'HSM17X',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'S6ROM',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'HSMENPINS',0,2
	.word	659
	.byte	2,7,2,35,0,11
	.byte	'HSMENRES',0,1
	.word	642
	.byte	2,5,2,35,1,11
	.byte	'DESTDBG',0,1
	.word	642
	.byte	2,3,2,35,1,11
	.byte	'BLKFLAN',0,1
	.word	642
	.byte	1,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	642
	.byte	2,0,2,35,1,11
	.byte	'S16ROM',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'S17ROM',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	659
	.byte	14,0,2,35,2,0,36
	.byte	'Ifx_FLASH_PROCONHSMCOTP_Bits',0,30,219,2,3
	.word	82772
	.byte	10
	.byte	'_Ifx_FLASH_PROCONOTP_Bits',0,30,222,2,16,4,11
	.byte	'S0ROM',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'S1ROM',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'S2ROM',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'S3ROM',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'S4ROM',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'S5ROM',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'S6ROM',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'S7ROM',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'S8ROM',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'S9ROM',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'S10ROM',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'S11ROM',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'S12ROM',0,1
	.word	642
	.byte	1,3,2,35,1,11
	.byte	'S13ROM',0,1
	.word	642
	.byte	1,2,2,35,1,11
	.byte	'S14ROM',0,1
	.word	642
	.byte	1,1,2,35,1,11
	.byte	'S15ROM',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'S16ROM',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'S17ROM',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'S18ROM',0,1
	.word	642
	.byte	1,5,2,35,2,11
	.byte	'S19ROM',0,1
	.word	642
	.byte	1,4,2,35,2,11
	.byte	'S20ROM',0,1
	.word	642
	.byte	1,3,2,35,2,11
	.byte	'S21ROM',0,1
	.word	642
	.byte	1,2,2,35,2,11
	.byte	'S22ROM',0,1
	.word	642
	.byte	1,1,2,35,2,11
	.byte	'S23ROM',0,1
	.word	642
	.byte	1,0,2,35,2,11
	.byte	'S24ROM',0,1
	.word	642
	.byte	1,7,2,35,3,11
	.byte	'S25ROM',0,1
	.word	642
	.byte	1,6,2,35,3,11
	.byte	'S26ROM',0,1
	.word	642
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	642
	.byte	2,3,2,35,3,11
	.byte	'BML',0,1
	.word	642
	.byte	2,1,2,35,3,11
	.byte	'TP',0,1
	.word	642
	.byte	1,0,2,35,3,0,36
	.byte	'Ifx_FLASH_PROCONOTP_Bits',0,30,254,2,3
	.word	83135
	.byte	10
	.byte	'_Ifx_FLASH_PROCONP_Bits',0,30,129,3,16,4,11
	.byte	'S0L',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'S1L',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'S2L',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'S3L',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'S4L',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'S5L',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'S6L',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'S7L',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'S8L',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'S9L',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'S10L',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'S11L',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'S12L',0,1
	.word	642
	.byte	1,3,2,35,1,11
	.byte	'S13L',0,1
	.word	642
	.byte	1,2,2,35,1,11
	.byte	'S14L',0,1
	.word	642
	.byte	1,1,2,35,1,11
	.byte	'S15L',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'S16L',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'S17L',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'S18L',0,1
	.word	642
	.byte	1,5,2,35,2,11
	.byte	'S19L',0,1
	.word	642
	.byte	1,4,2,35,2,11
	.byte	'S20L',0,1
	.word	642
	.byte	1,3,2,35,2,11
	.byte	'S21L',0,1
	.word	642
	.byte	1,2,2,35,2,11
	.byte	'S22L',0,1
	.word	642
	.byte	1,1,2,35,2,11
	.byte	'S23L',0,1
	.word	642
	.byte	1,0,2,35,2,11
	.byte	'S24L',0,1
	.word	642
	.byte	1,7,2,35,3,11
	.byte	'S25L',0,1
	.word	642
	.byte	1,6,2,35,3,11
	.byte	'S26L',0,1
	.word	642
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	642
	.byte	4,1,2,35,3,11
	.byte	'RPRO',0,1
	.word	642
	.byte	1,0,2,35,3,0,36
	.byte	'Ifx_FLASH_PROCONP_Bits',0,30,160,3,3
	.word	83730
	.byte	10
	.byte	'_Ifx_FLASH_PROCONWOP_Bits',0,30,163,3,16,4,11
	.byte	'S0WOP',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'S1WOP',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'S2WOP',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'S3WOP',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'S4WOP',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'S5WOP',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'S6WOP',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'S7WOP',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'S8WOP',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'S9WOP',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'S10WOP',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'S11WOP',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'S12WOP',0,1
	.word	642
	.byte	1,3,2,35,1,11
	.byte	'S13WOP',0,1
	.word	642
	.byte	1,2,2,35,1,11
	.byte	'S14WOP',0,1
	.word	642
	.byte	1,1,2,35,1,11
	.byte	'S15WOP',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'S16WOP',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'S17WOP',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'S18WOP',0,1
	.word	642
	.byte	1,5,2,35,2,11
	.byte	'S19WOP',0,1
	.word	642
	.byte	1,4,2,35,2,11
	.byte	'S20WOP',0,1
	.word	642
	.byte	1,3,2,35,2,11
	.byte	'S21WOP',0,1
	.word	642
	.byte	1,2,2,35,2,11
	.byte	'S22WOP',0,1
	.word	642
	.byte	1,1,2,35,2,11
	.byte	'S23WOP',0,1
	.word	642
	.byte	1,0,2,35,2,11
	.byte	'S24WOP',0,1
	.word	642
	.byte	1,7,2,35,3,11
	.byte	'S25WOP',0,1
	.word	642
	.byte	1,6,2,35,3,11
	.byte	'S26WOP',0,1
	.word	642
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	642
	.byte	4,1,2,35,3,11
	.byte	'DATM',0,1
	.word	642
	.byte	1,0,2,35,3,0,36
	.byte	'Ifx_FLASH_PROCONWOP_Bits',0,30,194,3,3
	.word	84254
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG0_Bits',0,30,197,3,16,4,11
	.byte	'TAG',0,1
	.word	642
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	467
	.byte	26,0,2,35,0,0,36
	.byte	'Ifx_FLASH_RDB_CFG0_Bits',0,30,201,3,3
	.word	84836
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG1_Bits',0,30,204,3,16,4,11
	.byte	'TAG',0,1
	.word	642
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	467
	.byte	26,0,2,35,0,0,36
	.byte	'Ifx_FLASH_RDB_CFG1_Bits',0,30,208,3,3
	.word	84938
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG2_Bits',0,30,211,3,16,4,11
	.byte	'TAG',0,1
	.word	642
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	467
	.byte	26,0,2,35,0,0,36
	.byte	'Ifx_FLASH_RDB_CFG2_Bits',0,30,215,3,3
	.word	85040
	.byte	10
	.byte	'_Ifx_FLASH_RRAD_Bits',0,30,218,3,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	3,5,2,35,0,11
	.byte	'ADD',0,4
	.word	467
	.byte	29,0,2,35,0,0,36
	.byte	'Ifx_FLASH_RRAD_Bits',0,30,222,3,3
	.word	85142
	.byte	10
	.byte	'_Ifx_FLASH_RRCT_Bits',0,30,225,3,16,4,11
	.byte	'STRT',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'STP',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'BUSY',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'DONE',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'ERR',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	642
	.byte	3,0,2,35,0,11
	.byte	'EOBM',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	642
	.byte	7,0,2,35,1,11
	.byte	'CNT',0,2
	.word	659
	.byte	16,0,2,35,2,0,36
	.byte	'Ifx_FLASH_RRCT_Bits',0,30,236,3,3
	.word	85236
	.byte	10
	.byte	'_Ifx_FLASH_RRD0_Bits',0,30,239,3,16,4,11
	.byte	'DATA',0,4
	.word	467
	.byte	32,0,2,35,0,0,36
	.byte	'Ifx_FLASH_RRD0_Bits',0,30,242,3,3
	.word	85446
	.byte	10
	.byte	'_Ifx_FLASH_RRD1_Bits',0,30,245,3,16,4,11
	.byte	'DATA',0,4
	.word	467
	.byte	32,0,2,35,0,0,36
	.byte	'Ifx_FLASH_RRD1_Bits',0,30,248,3,3
	.word	85519
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_CFG_Bits',0,30,251,3,16,4,11
	.byte	'SEL',0,1
	.word	642
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	642
	.byte	2,0,2,35,0,11
	.byte	'CLR',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'DIS',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	467
	.byte	22,0,2,35,0,0,36
	.byte	'Ifx_FLASH_UBAB_CFG_Bits',0,30,130,4,3
	.word	85592
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_STAT_Bits',0,30,133,4,16,4,11
	.byte	'VLD0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	467
	.byte	31,0,2,35,0,0,36
	.byte	'Ifx_FLASH_UBAB_STAT_Bits',0,30,137,4,3
	.word	85747
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_TOP_Bits',0,30,140,4,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	467
	.byte	19,8,2,35,0,11
	.byte	'ERR',0,1
	.word	642
	.byte	6,2,2,35,3,11
	.byte	'VLD',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'CLR',0,1
	.word	642
	.byte	1,0,2,35,3,0,36
	.byte	'Ifx_FLASH_UBAB_TOP_Bits',0,30,147,4,3
	.word	85852
	.byte	12,30,155,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	78257
	.byte	4,2,35,0,0,36
	.byte	'Ifx_FLASH_ACCEN0',0,30,160,4,3
	.word	86000
	.byte	12,30,163,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	78818
	.byte	4,2,35,0,0,36
	.byte	'Ifx_FLASH_ACCEN1',0,30,168,4,3
	.word	86066
	.byte	12,30,171,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	78899
	.byte	4,2,35,0,0,36
	.byte	'Ifx_FLASH_CBAB_CFG',0,30,176,4,3
	.word	86132
	.byte	12,30,179,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	79052
	.byte	4,2,35,0,0,36
	.byte	'Ifx_FLASH_CBAB_STAT',0,30,184,4,3
	.word	86200
	.byte	12,30,187,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	79300
	.byte	4,2,35,0,0,36
	.byte	'Ifx_FLASH_CBAB_TOP',0,30,192,4,3
	.word	86269
	.byte	12,30,195,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	79446
	.byte	4,2,35,0,0,36
	.byte	'Ifx_FLASH_COMM0',0,30,200,4,3
	.word	86337
	.byte	12,30,203,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	79544
	.byte	4,2,35,0,0,36
	.byte	'Ifx_FLASH_COMM1',0,30,208,4,3
	.word	86402
	.byte	12,30,211,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	79660
	.byte	4,2,35,0,0,36
	.byte	'Ifx_FLASH_COMM2',0,30,216,4,3
	.word	86467
	.byte	12,30,219,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	79776
	.byte	4,2,35,0,0,36
	.byte	'Ifx_FLASH_ECCRD',0,30,224,4,3
	.word	86532
	.byte	12,30,227,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	79916
	.byte	4,2,35,0,0,36
	.byte	'Ifx_FLASH_ECCRP',0,30,232,4,3
	.word	86597
	.byte	12,30,235,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	80056
	.byte	4,2,35,0,0,36
	.byte	'Ifx_FLASH_ECCW',0,30,240,4,3
	.word	86662
	.byte	12,30,243,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	80195
	.byte	4,2,35,0,0,36
	.byte	'Ifx_FLASH_FCON',0,30,248,4,3
	.word	86726
	.byte	12,30,251,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	80557
	.byte	4,2,35,0,0,36
	.byte	'Ifx_FLASH_FPRO',0,30,128,5,3
	.word	86790
	.byte	12,30,131,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	80998
	.byte	4,2,35,0,0,36
	.byte	'Ifx_FLASH_FSR',0,30,136,5,3
	.word	86854
	.byte	12,30,139,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81604
	.byte	4,2,35,0,0,36
	.byte	'Ifx_FLASH_ID',0,30,144,5,3
	.word	86917
	.byte	12,30,147,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81715
	.byte	4,2,35,0,0,36
	.byte	'Ifx_FLASH_MARD',0,30,152,5,3
	.word	86979
	.byte	12,30,155,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81929
	.byte	4,2,35,0,0,36
	.byte	'Ifx_FLASH_MARP',0,30,160,5,3
	.word	87043
	.byte	12,30,163,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	82116
	.byte	4,2,35,0,0,36
	.byte	'Ifx_FLASH_PROCOND',0,30,168,5,3
	.word	87107
	.byte	12,30,171,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	82440
	.byte	4,2,35,0,0,36
	.byte	'Ifx_FLASH_PROCONDBG',0,30,176,5,3
	.word	87174
	.byte	12,30,179,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	82583
	.byte	4,2,35,0,0,36
	.byte	'Ifx_FLASH_PROCONHSM',0,30,184,5,3
	.word	87243
	.byte	12,30,187,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	82772
	.byte	4,2,35,0,0,36
	.byte	'Ifx_FLASH_PROCONHSMCOTP',0,30,192,5,3
	.word	87312
	.byte	12,30,195,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	83135
	.byte	4,2,35,0,0,36
	.byte	'Ifx_FLASH_PROCONOTP',0,30,200,5,3
	.word	87385
	.byte	12,30,203,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	83730
	.byte	4,2,35,0,0,36
	.byte	'Ifx_FLASH_PROCONP',0,30,208,5,3
	.word	87454
	.byte	12,30,211,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	84254
	.byte	4,2,35,0,0,36
	.byte	'Ifx_FLASH_PROCONWOP',0,30,216,5,3
	.word	87521
	.byte	12,30,219,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	84836
	.byte	4,2,35,0,0,36
	.byte	'Ifx_FLASH_RDB_CFG0',0,30,224,5,3
	.word	87590
	.byte	12,30,227,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	84938
	.byte	4,2,35,0,0,36
	.byte	'Ifx_FLASH_RDB_CFG1',0,30,232,5,3
	.word	87658
	.byte	12,30,235,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	85040
	.byte	4,2,35,0,0,36
	.byte	'Ifx_FLASH_RDB_CFG2',0,30,240,5,3
	.word	87726
	.byte	12,30,243,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	85142
	.byte	4,2,35,0,0,36
	.byte	'Ifx_FLASH_RRAD',0,30,248,5,3
	.word	87794
	.byte	12,30,251,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	85236
	.byte	4,2,35,0,0,36
	.byte	'Ifx_FLASH_RRCT',0,30,128,6,3
	.word	87858
	.byte	12,30,131,6,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	85446
	.byte	4,2,35,0,0,36
	.byte	'Ifx_FLASH_RRD0',0,30,136,6,3
	.word	87922
	.byte	12,30,139,6,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	85519
	.byte	4,2,35,0,0,36
	.byte	'Ifx_FLASH_RRD1',0,30,144,6,3
	.word	87986
	.byte	12,30,147,6,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	85592
	.byte	4,2,35,0,0,36
	.byte	'Ifx_FLASH_UBAB_CFG',0,30,152,6,3
	.word	88050
	.byte	12,30,155,6,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	85747
	.byte	4,2,35,0,0,36
	.byte	'Ifx_FLASH_UBAB_STAT',0,30,160,6,3
	.word	88118
	.byte	12,30,163,6,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	85852
	.byte	4,2,35,0,0,36
	.byte	'Ifx_FLASH_UBAB_TOP',0,30,168,6,3
	.word	88187
	.byte	10
	.byte	'_Ifx_FLASH_CBAB',0,30,179,6,25,12,13
	.byte	'CFG',0
	.word	86132
	.byte	4,2,35,0,13
	.byte	'STAT',0
	.word	86200
	.byte	4,2,35,4,13
	.byte	'TOP',0
	.word	86269
	.byte	4,2,35,8,0,14
	.word	88255
	.byte	36
	.byte	'Ifx_FLASH_CBAB',0,30,184,6,3
	.word	88318
	.byte	10
	.byte	'_Ifx_FLASH_RDB',0,30,187,6,25,12,13
	.byte	'CFG0',0
	.word	87590
	.byte	4,2,35,0,13
	.byte	'CFG1',0
	.word	87658
	.byte	4,2,35,4,13
	.byte	'CFG2',0
	.word	87726
	.byte	4,2,35,8,0,14
	.word	88347
	.byte	36
	.byte	'Ifx_FLASH_RDB',0,30,192,6,3
	.word	88411
	.byte	10
	.byte	'_Ifx_FLASH_UBAB',0,30,195,6,25,12,13
	.byte	'CFG',0
	.word	88050
	.byte	4,2,35,0,13
	.byte	'STAT',0
	.word	88118
	.byte	4,2,35,4,13
	.byte	'TOP',0
	.word	88187
	.byte	4,2,35,8,0,14
	.word	88439
	.byte	36
	.byte	'Ifx_FLASH_UBAB',0,30,200,6,3
	.word	88502
	.byte	36
	.byte	'Ifx_P_ACCEN0_Bits',0,8,79,3
	.word	8585
	.byte	36
	.byte	'Ifx_P_ACCEN1_Bits',0,8,85,3
	.word	8498
	.byte	36
	.byte	'Ifx_P_ESR_Bits',0,8,107,3
	.word	4841
	.byte	36
	.byte	'Ifx_P_ID_Bits',0,8,115,3
	.word	2894
	.byte	36
	.byte	'Ifx_P_IN_Bits',0,8,137,1,3
	.word	3889
	.byte	36
	.byte	'Ifx_P_IOCR0_Bits',0,8,150,1,3
	.word	3022
	.byte	36
	.byte	'Ifx_P_IOCR12_Bits',0,8,163,1,3
	.word	3669
	.byte	36
	.byte	'Ifx_P_IOCR4_Bits',0,8,176,1,3
	.word	3237
	.byte	36
	.byte	'Ifx_P_IOCR8_Bits',0,8,189,1,3
	.word	3452
	.byte	36
	.byte	'Ifx_P_LPCR0_Bits',0,8,197,1,3
	.word	7857
	.byte	36
	.byte	'Ifx_P_LPCR1_Bits',0,8,205,1,3
	.word	7981
	.byte	36
	.byte	'Ifx_P_LPCR1_P21_Bits',0,8,215,1,3
	.word	8065
	.byte	36
	.byte	'Ifx_P_LPCR2_Bits',0,8,229,1,3
	.word	8245
	.byte	36
	.byte	'Ifx_P_OMCR0_Bits',0,8,240,1,3
	.word	6496
	.byte	36
	.byte	'Ifx_P_OMCR12_Bits',0,8,250,1,3
	.word	7020
	.byte	36
	.byte	'Ifx_P_OMCR4_Bits',0,8,133,2,3
	.word	6670
	.byte	36
	.byte	'Ifx_P_OMCR8_Bits',0,8,144,2,3
	.word	6844
	.byte	36
	.byte	'Ifx_P_OMCR_Bits',0,8,166,2,3
	.word	7509
	.byte	36
	.byte	'Ifx_P_OMR_Bits',0,8,203,2,3
	.word	2323
	.byte	36
	.byte	'Ifx_P_OMSR0_Bits',0,8,213,2,3
	.word	5833
	.byte	36
	.byte	'Ifx_P_OMSR12_Bits',0,8,224,2,3
	.word	6321
	.byte	36
	.byte	'Ifx_P_OMSR4_Bits',0,8,235,2,3
	.word	5980
	.byte	36
	.byte	'Ifx_P_OMSR8_Bits',0,8,246,2,3
	.word	6149
	.byte	36
	.byte	'Ifx_P_OMSR_Bits',0,8,140,3,3
	.word	7176
	.byte	36
	.byte	'Ifx_P_OUT_Bits',0,8,162,3,3
	.word	2007
	.byte	36
	.byte	'Ifx_P_PCSR_Bits',0,8,180,3,3
	.word	5547
	.byte	36
	.byte	'Ifx_P_PDISC_Bits',0,8,202,3,3
	.word	5181
	.byte	36
	.byte	'Ifx_P_PDR0_Bits',0,8,223,3,3
	.word	4212
	.byte	36
	.byte	'Ifx_P_PDR1_Bits',0,8,244,3,3
	.word	4516
	.byte	36
	.byte	'Ifx_P_ACCEN0',0,8,129,4,3
	.word	9112
	.byte	36
	.byte	'Ifx_P_ACCEN1',0,8,137,4,3
	.word	8545
	.byte	36
	.byte	'Ifx_P_ESR',0,8,145,4,3
	.word	5132
	.byte	36
	.byte	'Ifx_P_ID',0,8,153,4,3
	.word	2973
	.byte	36
	.byte	'Ifx_P_IN',0,8,161,4,3
	.word	4163
	.byte	36
	.byte	'Ifx_P_IOCR0',0,8,169,4,3
	.word	3197
	.byte	36
	.byte	'Ifx_P_IOCR12',0,8,177,4,3
	.word	3849
	.byte	36
	.byte	'Ifx_P_IOCR4',0,8,185,4,3
	.word	3412
	.byte	36
	.byte	'Ifx_P_IOCR8',0,8,193,4,3
	.word	3629
	.byte	36
	.byte	'Ifx_P_LPCR0',0,8,201,4,3
	.word	7941
	.byte	36
	.byte	'Ifx_P_LPCR1',0,8,210,4,3
	.word	8190
	.byte	36
	.byte	'Ifx_P_LPCR2',0,8,218,4,3
	.word	8449
	.byte	36
	.byte	'Ifx_P_OMCR',0,8,226,4,3
	.word	7817
	.byte	36
	.byte	'Ifx_P_OMCR0',0,8,234,4,3
	.word	6630
	.byte	36
	.byte	'Ifx_P_OMCR12',0,8,242,4,3
	.word	7136
	.byte	36
	.byte	'Ifx_P_OMCR4',0,8,250,4,3
	.word	6804
	.byte	36
	.byte	'Ifx_P_OMCR8',0,8,130,5,3
	.word	6980
	.byte	36
	.byte	'Ifx_P_OMR',0,8,138,5,3
	.word	2854
	.byte	36
	.byte	'Ifx_P_OMSR',0,8,146,5,3
	.word	7469
	.byte	36
	.byte	'Ifx_P_OMSR0',0,8,154,5,3
	.word	5940
	.byte	36
	.byte	'Ifx_P_OMSR12',0,8,162,5,3
	.word	6456
	.byte	36
	.byte	'Ifx_P_OMSR4',0,8,170,5,3
	.word	6109
	.byte	36
	.byte	'Ifx_P_OMSR8',0,8,178,5,3
	.word	6281
	.byte	36
	.byte	'Ifx_P_OUT',0,8,186,5,3
	.word	2283
	.byte	36
	.byte	'Ifx_P_PCSR',0,8,194,5,3
	.word	5793
	.byte	36
	.byte	'Ifx_P_PDISC',0,8,202,5,3
	.word	5507
	.byte	36
	.byte	'Ifx_P_PDR0',0,8,210,5,3
	.word	4476
	.byte	36
	.byte	'Ifx_P_PDR1',0,8,218,5,3
	.word	4792
	.byte	14
	.word	9152
	.byte	36
	.byte	'Ifx_P',0,8,139,6,3
	.word	89849
	.byte	36
	.byte	'IfxPort_InputMode',0,7,89,3
	.word	9765
	.byte	36
	.byte	'IfxPort_OutputIdx',0,7,130,1,3
	.word	10040
	.byte	36
	.byte	'IfxPort_OutputMode',0,7,138,1,3
	.word	9970
	.byte	15,7,144,1,9,1,16
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed1',0,0,16
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed2',0,1,16
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed3',0,2,16
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed4',0,3,16
	.byte	'IfxPort_PadDriver_lvdsSpeed1',0,4,16
	.byte	'IfxPort_PadDriver_lvdsSpeed2',0,5,16
	.byte	'IfxPort_PadDriver_lvdsSpeed3',0,6,16
	.byte	'IfxPort_PadDriver_lvdsSpeed4',0,7,16
	.byte	'IfxPort_PadDriver_ttlSpeed1',0,8,16
	.byte	'IfxPort_PadDriver_ttlSpeed2',0,9,16
	.byte	'IfxPort_PadDriver_ttlSpeed3',0,10,16
	.byte	'IfxPort_PadDriver_ttlSpeed4',0,11,0,36
	.byte	'IfxPort_PadDriver',0,7,158,1,3
	.word	89950
	.byte	36
	.byte	'IfxPort_State',0,7,178,1,3
	.word	10353
	.byte	20,7,190,1,9,8,13
	.byte	'port',0
	.word	9760
	.byte	4,2,35,0,13
	.byte	'pinIndex',0
	.word	642
	.byte	1,2,35,4,0,36
	.byte	'IfxPort_Pin',0,7,194,1,3
	.word	90415
	.byte	36
	.byte	'IfxScuCcu_PllStepsFunctionHook',0,9,148,1,16
	.word	206
	.byte	20,9,212,5,9,8,13
	.byte	'value',0
	.word	1776
	.byte	4,2,35,0,13
	.byte	'mask',0
	.word	1776
	.byte	4,2,35,4,0,36
	.byte	'IfxScuCcu_CcuconRegConfig',0,9,216,5,3
	.word	90515
	.byte	20,9,221,5,9,8,13
	.byte	'pDivider',0
	.word	642
	.byte	1,2,35,0,13
	.byte	'nDivider',0
	.word	642
	.byte	1,2,35,1,13
	.byte	'k2Initial',0
	.word	642
	.byte	1,2,35,2,13
	.byte	'waitTime',0
	.word	263
	.byte	4,2,35,4,0,36
	.byte	'IfxScuCcu_InitialStepConfig',0,9,227,5,3
	.word	90586
	.byte	20,9,231,5,9,12,13
	.byte	'k2Step',0
	.word	642
	.byte	1,2,35,0,13
	.byte	'waitTime',0
	.word	263
	.byte	4,2,35,2,13
	.byte	'hookFunction',0
	.word	90475
	.byte	4,2,35,8,0,36
	.byte	'IfxScuCcu_PllStepsConfig',0,9,236,5,3
	.word	90703
	.byte	3
	.word	203
	.byte	20,9,244,5,9,48,13
	.byte	'ccucon0',0
	.word	90515
	.byte	8,2,35,0,13
	.byte	'ccucon1',0
	.word	90515
	.byte	8,2,35,8,13
	.byte	'ccucon2',0
	.word	90515
	.byte	8,2,35,16,13
	.byte	'ccucon5',0
	.word	90515
	.byte	8,2,35,24,13
	.byte	'ccucon6',0
	.word	90515
	.byte	8,2,35,32,13
	.byte	'ccucon7',0
	.word	90515
	.byte	8,2,35,40,0,36
	.byte	'IfxScuCcu_ClockDistributionConfig',0,9,252,5,3
	.word	90805
	.byte	20,9,128,6,9,8,13
	.byte	'value',0
	.word	1776
	.byte	4,2,35,0,13
	.byte	'mask',0
	.word	1776
	.byte	4,2,35,4,0,36
	.byte	'IfxScuCcu_FlashWaitstateConfig',0,9,132,6,3
	.word	90957
	.byte	3
	.word	90703
	.byte	20,9,137,6,9,16,13
	.byte	'numOfPllDividerSteps',0
	.word	642
	.byte	1,2,35,0,13
	.byte	'pllDividerStep',0
	.word	91033
	.byte	4,2,35,4,13
	.byte	'pllInitialStep',0
	.word	90586
	.byte	8,2,35,8,0,36
	.byte	'IfxScuCcu_SysPllConfig',0,9,142,6,3
	.word	91038
	.byte	15,31,69,9,1,16
	.byte	'IfxSrc_Tos_cpu0',0,0,16
	.byte	'IfxSrc_Tos_cpu1',0,1,16
	.byte	'IfxSrc_Tos_dma',0,3,0,36
	.byte	'IfxSrc_Tos',0,31,74,3
	.word	91155
	.byte	20,32,59,15,16,13
	.byte	'module',0
	.word	17488
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	90415
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	25875
	.byte	1,2,35,12,0,22
	.word	91233
	.byte	36
	.byte	'IfxAsclin_Cts_In',0,32,64,3
	.word	91284
	.byte	20,32,67,15,16,13
	.byte	'module',0
	.word	17488
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	90415
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	25875
	.byte	1,2,35,12,0,22
	.word	91314
	.byte	36
	.byte	'IfxAsclin_Rx_In',0,32,72,3
	.word	91365
	.byte	20,32,75,15,16,13
	.byte	'module',0
	.word	17488
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	90415
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	10040
	.byte	1,2,35,12,0,22
	.word	91394
	.byte	36
	.byte	'IfxAsclin_Rts_Out',0,32,80,3
	.word	91445
	.byte	20,32,83,15,16,13
	.byte	'module',0
	.word	17488
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	90415
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	10040
	.byte	1,2,35,12,0,22
	.word	91476
	.byte	36
	.byte	'IfxAsclin_Sclk_Out',0,32,88,3
	.word	91527
	.byte	20,32,91,15,16,13
	.byte	'module',0
	.word	17488
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	90415
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	10040
	.byte	1,2,35,12,0,22
	.word	91559
	.byte	36
	.byte	'IfxAsclin_Slso_Out',0,32,96,3
	.word	91610
	.byte	20,32,99,15,16,13
	.byte	'module',0
	.word	17488
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	90415
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	10040
	.byte	1,2,35,12,0,22
	.word	91642
	.byte	36
	.byte	'IfxAsclin_Tx_Out',0,32,104,3
	.word	91693
	.byte	15,12,82,9,1,16
	.byte	'IfxAsclin_Checksum_classic',0,0,16
	.byte	'IfxAsclin_Checksum_enhanced',0,1,0,36
	.byte	'IfxAsclin_Checksum',0,12,86,3
	.word	91723
	.byte	15,12,91,9,1,16
	.byte	'IfxAsclin_ChecksumInjection_notWritten',0,0,16
	.byte	'IfxAsclin_ChecksumInjection_written',0,1,0,36
	.byte	'IfxAsclin_ChecksumInjection',0,12,95,3
	.word	91815
	.byte	15,12,101,9,1,16
	.byte	'IfxAsclin_ClockPolarity_idleLow',0,0,16
	.byte	'IfxAsclin_ClockPolarity_idleHigh',0,1,0,36
	.byte	'IfxAsclin_ClockPolarity',0,12,105,3
	.word	91936
	.byte	15,12,110,9,1,16
	.byte	'IfxAsclin_ClockSource_noClock',0,0,16
	.byte	'IfxAsclin_ClockSource_kernelClock',0,1,16
	.byte	'IfxAsclin_ClockSource_oscillatorClock',0,2,16
	.byte	'IfxAsclin_ClockSource_flexRayClock',0,4,16
	.byte	'IfxAsclin_ClockSource_ascFastClock',0,8,16
	.byte	'IfxAsclin_ClockSource_ascSlowClock',0,16,0,36
	.byte	'IfxAsclin_ClockSource',0,12,118,3
	.word	92043
	.byte	36
	.byte	'IfxAsclin_CtsInputSelect',0,12,129,1,3
	.word	17555
	.byte	15,12,134,1,9,1,16
	.byte	'IfxAsclin_DataLength_1',0,0,16
	.byte	'IfxAsclin_DataLength_2',0,1,16
	.byte	'IfxAsclin_DataLength_3',0,2,16
	.byte	'IfxAsclin_DataLength_4',0,3,16
	.byte	'IfxAsclin_DataLength_5',0,4,16
	.byte	'IfxAsclin_DataLength_6',0,5,16
	.byte	'IfxAsclin_DataLength_7',0,6,16
	.byte	'IfxAsclin_DataLength_8',0,7,16
	.byte	'IfxAsclin_DataLength_9',0,8,16
	.byte	'IfxAsclin_DataLength_10',0,9,16
	.byte	'IfxAsclin_DataLength_11',0,10,16
	.byte	'IfxAsclin_DataLength_12',0,11,16
	.byte	'IfxAsclin_DataLength_13',0,12,16
	.byte	'IfxAsclin_DataLength_14',0,13,16
	.byte	'IfxAsclin_DataLength_15',0,14,16
	.byte	'IfxAsclin_DataLength_16',0,15,0,36
	.byte	'IfxAsclin_DataLength',0,12,152,1,3
	.word	92332
	.byte	15,12,157,1,9,1,16
	.byte	'IfxAsclin_FrameMode_initialise',0,0,16
	.byte	'IfxAsclin_FrameMode_asc',0,1,16
	.byte	'IfxAsclin_FrameMode_spi',0,2,16
	.byte	'IfxAsclin_FrameMode_lin',0,3,0,36
	.byte	'IfxAsclin_FrameMode',0,12,163,1,3
	.word	92776
	.byte	15,12,168,1,9,1,16
	.byte	'IfxAsclin_HeaderResponseSelect_headerAndResponse',0,0,16
	.byte	'IfxAsclin_HeaderResponseSelect_headerOnly',0,1,0,36
	.byte	'IfxAsclin_HeaderResponseSelect',0,12,172,1,3
	.word	92923
	.byte	15,12,179,1,9,1,16
	.byte	'IfxAsclin_IdleDelay_0',0,0,16
	.byte	'IfxAsclin_IdleDelay_1',0,1,16
	.byte	'IfxAsclin_IdleDelay_2',0,2,16
	.byte	'IfxAsclin_IdleDelay_3',0,3,16
	.byte	'IfxAsclin_IdleDelay_4',0,4,16
	.byte	'IfxAsclin_IdleDelay_5',0,5,16
	.byte	'IfxAsclin_IdleDelay_6',0,6,16
	.byte	'IfxAsclin_IdleDelay_7',0,7,0,36
	.byte	'IfxAsclin_IdleDelay',0,12,189,1,3
	.word	93065
	.byte	15,12,195,1,9,1,16
	.byte	'IfxAsclin_LeadDelay_0',0,0,16
	.byte	'IfxAsclin_LeadDelay_1',0,1,16
	.byte	'IfxAsclin_LeadDelay_2',0,2,16
	.byte	'IfxAsclin_LeadDelay_3',0,3,16
	.byte	'IfxAsclin_LeadDelay_4',0,4,16
	.byte	'IfxAsclin_LeadDelay_5',0,5,16
	.byte	'IfxAsclin_LeadDelay_6',0,6,16
	.byte	'IfxAsclin_LeadDelay_7',0,7,0,36
	.byte	'IfxAsclin_LeadDelay',0,12,205,1,3
	.word	93293
	.byte	15,12,210,1,9,1,16
	.byte	'IfxAsclin_LinMode_slave',0,0,16
	.byte	'IfxAsclin_LinMode_master',0,1,0,36
	.byte	'IfxAsclin_LinMode',0,12,214,1,3
	.word	93521
	.byte	15,12,219,1,9,1,16
	.byte	'IfxAsclin_LinResponseTimeoutMode_frameTimeout',0,0,16
	.byte	'IfxAsclin_LinResponseTimeoutMode_responseTimeout',0,1,0,36
	.byte	'IfxAsclin_LinResponseTimeoutMode',0,12,223,1,3
	.word	93608
	.byte	15,12,228,1,9,1,16
	.byte	'IfxAsclin_OversamplingFactor_4',0,3,16
	.byte	'IfxAsclin_OversamplingFactor_5',0,4,16
	.byte	'IfxAsclin_OversamplingFactor_6',0,5,16
	.byte	'IfxAsclin_OversamplingFactor_7',0,6,16
	.byte	'IfxAsclin_OversamplingFactor_8',0,7,16
	.byte	'IfxAsclin_OversamplingFactor_9',0,8,16
	.byte	'IfxAsclin_OversamplingFactor_10',0,9,16
	.byte	'IfxAsclin_OversamplingFactor_11',0,10,16
	.byte	'IfxAsclin_OversamplingFactor_12',0,11,16
	.byte	'IfxAsclin_OversamplingFactor_13',0,12,16
	.byte	'IfxAsclin_OversamplingFactor_14',0,13,16
	.byte	'IfxAsclin_OversamplingFactor_15',0,14,16
	.byte	'IfxAsclin_OversamplingFactor_16',0,15,0,36
	.byte	'IfxAsclin_OversamplingFactor',0,12,243,1,3
	.word	93756
	.byte	15,12,248,1,9,1,16
	.byte	'IfxAsclin_ParityType_even',0,0,16
	.byte	'IfxAsclin_ParityType_odd',0,1,0,36
	.byte	'IfxAsclin_ParityType',0,12,252,1,3
	.word	94237
	.byte	15,12,129,2,9,1,16
	.byte	'IfxAsclin_ReceiveBufferMode_rxFifo',0,0,16
	.byte	'IfxAsclin_ReceiveBufferMode_rxBuffer',0,1,0,36
	.byte	'IfxAsclin_ReceiveBufferMode',0,12,133,2,3
	.word	94329
	.byte	15,12,138,2,9,1,16
	.byte	'IfxAsclin_RtsCtsPolarity_activeHigh',0,0,16
	.byte	'IfxAsclin_RtsCtsPolarity_activeLow',0,1,0,36
	.byte	'IfxAsclin_RtsCtsPolarity',0,12,142,2,3
	.word	94449
	.byte	15,12,147,2,9,1,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_1',0,0,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_2',0,1,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_3',0,2,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_4',0,3,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_5',0,4,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_6',0,5,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_7',0,6,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_8',0,7,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_9',0,8,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_10',0,9,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_11',0,10,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_12',0,11,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_13',0,12,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_14',0,13,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_15',0,14,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_16',0,15,0,36
	.byte	'IfxAsclin_RxFifoInterruptLevel',0,12,165,2,3
	.word	94565
	.byte	15,12,170,2,9,1,16
	.byte	'IfxAsclin_RxFifoOutletWidth_0',0,0,16
	.byte	'IfxAsclin_RxFifoOutletWidth_1',0,1,16
	.byte	'IfxAsclin_RxFifoOutletWidth_2',0,2,16
	.byte	'IfxAsclin_RxFifoOutletWidth_3',0,3,0,36
	.byte	'IfxAsclin_RxFifoOutletWidth',0,12,176,2,3
	.word	95179
	.byte	36
	.byte	'IfxAsclin_RxInputSelect',0,12,191,2,3
	.word	17739
	.byte	15,12,196,2,9,1,16
	.byte	'IfxAsclin_SamplePointPosition_1',0,1,16
	.byte	'IfxAsclin_SamplePointPosition_2',0,2,16
	.byte	'IfxAsclin_SamplePointPosition_3',0,3,16
	.byte	'IfxAsclin_SamplePointPosition_4',0,4,16
	.byte	'IfxAsclin_SamplePointPosition_5',0,5,16
	.byte	'IfxAsclin_SamplePointPosition_6',0,6,16
	.byte	'IfxAsclin_SamplePointPosition_7',0,7,16
	.byte	'IfxAsclin_SamplePointPosition_8',0,8,16
	.byte	'IfxAsclin_SamplePointPosition_9',0,9,16
	.byte	'IfxAsclin_SamplePointPosition_10',0,10,16
	.byte	'IfxAsclin_SamplePointPosition_11',0,11,16
	.byte	'IfxAsclin_SamplePointPosition_12',0,12,16
	.byte	'IfxAsclin_SamplePointPosition_13',0,13,16
	.byte	'IfxAsclin_SamplePointPosition_14',0,14,16
	.byte	'IfxAsclin_SamplePointPosition_15',0,15,0,36
	.byte	'IfxAsclin_SamplePointPosition',0,12,213,2,3
	.word	95384
	.byte	15,12,218,2,9,1,16
	.byte	'IfxAsclin_SamplesPerBit_one',0,0,16
	.byte	'IfxAsclin_SamplesPerBit_three',0,1,0,36
	.byte	'IfxAsclin_SamplesPerBit',0,12,222,2,3
	.word	95946
	.byte	15,12,228,2,9,1,16
	.byte	'IfxAsclin_ShiftDirection_lsbFirst',0,0,16
	.byte	'IfxAsclin_ShiftDirection_msbFirst',0,1,0,36
	.byte	'IfxAsclin_ShiftDirection',0,12,232,2,3
	.word	96048
	.byte	15,12,238,2,9,1,16
	.byte	'IfxAsclin_SlavePolarity_idleLow',0,0,16
	.byte	'IfxAsclin_SlavePolarity_idlehigh',0,1,0,36
	.byte	'IfxAsclin_SlavePolarity',0,12,242,2,3
	.word	96161
	.byte	15,12,247,2,9,1,16
	.byte	'IfxAsclin_SleepMode_enable',0,0,16
	.byte	'IfxAsclin_SleepMode_disable',0,1,0,36
	.byte	'IfxAsclin_SleepMode',0,12,251,2,3
	.word	96270
	.byte	15,12,136,3,9,1,16
	.byte	'IfxAsclin_StopBit_0',0,0,16
	.byte	'IfxAsclin_StopBit_1',0,1,16
	.byte	'IfxAsclin_StopBit_2',0,2,16
	.byte	'IfxAsclin_StopBit_3',0,3,16
	.byte	'IfxAsclin_StopBit_4',0,4,16
	.byte	'IfxAsclin_StopBit_5',0,5,16
	.byte	'IfxAsclin_StopBit_6',0,6,16
	.byte	'IfxAsclin_StopBit_7',0,7,0,36
	.byte	'IfxAsclin_StopBit',0,12,146,3,3
	.word	96365
	.byte	15,12,150,3,9,1,16
	.byte	'IfxAsclin_SuspendMode_none',0,0,16
	.byte	'IfxAsclin_SuspendMode_hard',0,1,16
	.byte	'IfxAsclin_SuspendMode_soft',0,2,0,36
	.byte	'IfxAsclin_SuspendMode',0,12,155,3,3
	.word	96575
	.byte	15,12,160,3,9,1,16
	.byte	'IfxAsclin_TxFifoInletWidth_0',0,0,16
	.byte	'IfxAsclin_TxFifoInletWidth_1',0,1,16
	.byte	'IfxAsclin_TxFifoInletWidth_2',0,2,16
	.byte	'IfxAsclin_TxFifoInletWidth_3',0,3,0,36
	.byte	'IfxAsclin_TxFifoInletWidth',0,12,166,3,3
	.word	96700
	.byte	15,12,171,3,9,1,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_0',0,0,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_1',0,1,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_2',0,2,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_3',0,3,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_4',0,4,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_5',0,5,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_6',0,6,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_7',0,7,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_8',0,8,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_9',0,9,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_10',0,10,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_11',0,11,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_12',0,12,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_13',0,13,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_14',0,14,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_15',0,15,0,36
	.byte	'IfxAsclin_TxFifoInterruptLevel',0,12,189,3,3
	.word	96867
	.byte	36
	.byte	'Ifx_Fifo_Shared',0,14,66,3
	.word	18056
	.byte	36
	.byte	'Ifx_Fifo',0,14,83,3
	.word	18147
	.byte	15,15,151,1,9,1,16
	.byte	'IfxStm_Comparator_0',0,0,16
	.byte	'IfxStm_Comparator_1',0,1,0,36
	.byte	'IfxStm_Comparator',0,15,155,1,3
	.word	97521
	.byte	15,15,159,1,9,1,16
	.byte	'IfxStm_ComparatorInterrupt_ir0',0,0,16
	.byte	'IfxStm_ComparatorInterrupt_ir1',0,1,0,36
	.byte	'IfxStm_ComparatorInterrupt',0,15,163,1,3
	.word	97599
	.byte	15,15,167,1,9,1,16
	.byte	'IfxStm_ComparatorOffset_0',0,0,16
	.byte	'IfxStm_ComparatorOffset_1',0,1,16
	.byte	'IfxStm_ComparatorOffset_2',0,2,16
	.byte	'IfxStm_ComparatorOffset_3',0,3,16
	.byte	'IfxStm_ComparatorOffset_4',0,4,16
	.byte	'IfxStm_ComparatorOffset_5',0,5,16
	.byte	'IfxStm_ComparatorOffset_6',0,6,16
	.byte	'IfxStm_ComparatorOffset_7',0,7,16
	.byte	'IfxStm_ComparatorOffset_8',0,8,16
	.byte	'IfxStm_ComparatorOffset_9',0,9,16
	.byte	'IfxStm_ComparatorOffset_10',0,10,16
	.byte	'IfxStm_ComparatorOffset_11',0,11,16
	.byte	'IfxStm_ComparatorOffset_12',0,12,16
	.byte	'IfxStm_ComparatorOffset_13',0,13,16
	.byte	'IfxStm_ComparatorOffset_14',0,14,16
	.byte	'IfxStm_ComparatorOffset_15',0,15,16
	.byte	'IfxStm_ComparatorOffset_16',0,16,16
	.byte	'IfxStm_ComparatorOffset_17',0,17,16
	.byte	'IfxStm_ComparatorOffset_18',0,18,16
	.byte	'IfxStm_ComparatorOffset_19',0,19,16
	.byte	'IfxStm_ComparatorOffset_20',0,20,16
	.byte	'IfxStm_ComparatorOffset_21',0,21,16
	.byte	'IfxStm_ComparatorOffset_22',0,22,16
	.byte	'IfxStm_ComparatorOffset_23',0,23,16
	.byte	'IfxStm_ComparatorOffset_24',0,24,16
	.byte	'IfxStm_ComparatorOffset_25',0,25,16
	.byte	'IfxStm_ComparatorOffset_26',0,26,16
	.byte	'IfxStm_ComparatorOffset_27',0,27,16
	.byte	'IfxStm_ComparatorOffset_28',0,28,16
	.byte	'IfxStm_ComparatorOffset_29',0,29,16
	.byte	'IfxStm_ComparatorOffset_30',0,30,16
	.byte	'IfxStm_ComparatorOffset_31',0,31,0,36
	.byte	'IfxStm_ComparatorOffset',0,15,201,1,3
	.word	97708
	.byte	15,15,205,1,9,1,16
	.byte	'IfxStm_ComparatorSize_1Bit',0,0,16
	.byte	'IfxStm_ComparatorSize_2Bits',0,1,16
	.byte	'IfxStm_ComparatorSize_3Bits',0,2,16
	.byte	'IfxStm_ComparatorSize_4Bits',0,3,16
	.byte	'IfxStm_ComparatorSize_5Bits',0,4,16
	.byte	'IfxStm_ComparatorSize_6Bits',0,5,16
	.byte	'IfxStm_ComparatorSize_7Bits',0,6,16
	.byte	'IfxStm_ComparatorSize_8Bits',0,7,16
	.byte	'IfxStm_ComparatorSize_9Bits',0,8,16
	.byte	'IfxStm_ComparatorSize_10Bits',0,9,16
	.byte	'IfxStm_ComparatorSize_11Bits',0,10,16
	.byte	'IfxStm_ComparatorSize_12Bits',0,11,16
	.byte	'IfxStm_ComparatorSize_13Bits',0,12,16
	.byte	'IfxStm_ComparatorSize_14Bits',0,13,16
	.byte	'IfxStm_ComparatorSize_15Bits',0,14,16
	.byte	'IfxStm_ComparatorSize_16Bits',0,15,16
	.byte	'IfxStm_ComparatorSize_17Bits',0,16,16
	.byte	'IfxStm_ComparatorSize_18Bits',0,17,16
	.byte	'IfxStm_ComparatorSize_19Bits',0,18,16
	.byte	'IfxStm_ComparatorSize_20Bits',0,19,16
	.byte	'IfxStm_ComparatorSize_21Bits',0,20,16
	.byte	'IfxStm_ComparatorSize_22Bits',0,21,16
	.byte	'IfxStm_ComparatorSize_23Bits',0,22,16
	.byte	'IfxStm_ComparatorSize_24Bits',0,23,16
	.byte	'IfxStm_ComparatorSize_25Bits',0,24,16
	.byte	'IfxStm_ComparatorSize_26Bits',0,25,16
	.byte	'IfxStm_ComparatorSize_27Bits',0,26,16
	.byte	'IfxStm_ComparatorSize_28Bits',0,27,16
	.byte	'IfxStm_ComparatorSize_29Bits',0,28,16
	.byte	'IfxStm_ComparatorSize_30Bits',0,29,16
	.byte	'IfxStm_ComparatorSize_31Bits',0,30,16
	.byte	'IfxStm_ComparatorSize_32Bits',0,31,0,36
	.byte	'IfxStm_ComparatorSize',0,15,239,1,3
	.word	98666
	.byte	15,15,244,1,9,1,16
	.byte	'IfxStm_SleepMode_enable',0,0,16
	.byte	'IfxStm_SleepMode_disable',0,1,0,36
	.byte	'IfxStm_SleepMode',0,15,248,1,3
	.word	99686
	.byte	15,15,252,1,9,1,16
	.byte	'IfxStm_SuspendMode_none',0,0,16
	.byte	'IfxStm_SuspendMode_hard',0,1,16
	.byte	'IfxStm_SuspendMode_soft',0,2,0,36
	.byte	'IfxStm_SuspendMode',0,15,129,2,3
	.word	99772
	.byte	36
	.byte	'IfxStdIf_InterfaceDriver',0,33,118,15
	.word	381
	.byte	3
	.word	18031
	.byte	38
	.word	642
	.byte	1,1,34
	.word	381
	.byte	34
	.word	381
	.byte	34
	.word	99918
	.byte	34
	.word	22004
	.byte	0,3
	.word	99923
	.byte	36
	.byte	'IfxStdIf_DPipe_Write',0,34,92,19
	.word	99951
	.byte	36
	.byte	'IfxStdIf_DPipe_Read',0,34,107,19
	.word	99951
	.byte	38
	.word	18044
	.byte	1,1,34
	.word	381
	.byte	0,3
	.word	100013
	.byte	36
	.byte	'IfxStdIf_DPipe_GetReadCount',0,34,115,18
	.word	100026
	.byte	14
	.word	642
	.byte	3
	.word	100067
	.byte	38
	.word	100072
	.byte	1,1,34
	.word	381
	.byte	0,3
	.word	100077
	.byte	36
	.byte	'IfxStdIf_DPipe_GetReadEvent',0,34,123,36
	.word	100090
	.byte	36
	.byte	'IfxStdIf_DPipe_GetWriteCount',0,34,147,1,18
	.word	100026
	.byte	3
	.word	100077
	.byte	36
	.byte	'IfxStdIf_DPipe_GetWriteEvent',0,34,155,1,37
	.word	100169
	.byte	38
	.word	642
	.byte	1,1,34
	.word	381
	.byte	34
	.word	18031
	.byte	34
	.word	22004
	.byte	0,3
	.word	100212
	.byte	36
	.byte	'IfxStdIf_DPipe_CanReadCount',0,34,166,1,19
	.word	100235
	.byte	36
	.byte	'IfxStdIf_DPipe_CanWriteCount',0,34,177,1,19
	.word	100235
	.byte	38
	.word	642
	.byte	1,1,34
	.word	381
	.byte	34
	.word	22004
	.byte	0,3
	.word	100315
	.byte	36
	.byte	'IfxStdIf_DPipe_FlushTx',0,34,186,1,19
	.word	100333
	.byte	33,1,1,34
	.word	381
	.byte	0,3
	.word	100370
	.byte	36
	.byte	'IfxStdIf_DPipe_ClearTx',0,34,200,1,16
	.word	100379
	.byte	36
	.byte	'IfxStdIf_DPipe_ClearRx',0,34,193,1,16
	.word	100379
	.byte	36
	.byte	'IfxStdIf_DPipe_OnReceive',0,34,208,1,16
	.word	100379
	.byte	36
	.byte	'IfxStdIf_DPipe_OnTransmit',0,34,215,1,16
	.word	100379
	.byte	36
	.byte	'IfxStdIf_DPipe_OnError',0,34,222,1,16
	.word	100379
	.byte	38
	.word	1776
	.byte	1,1,34
	.word	381
	.byte	0,3
	.word	100549
	.byte	36
	.byte	'IfxStdIf_DPipe_GetSendCount',0,34,131,1,18
	.word	100562
	.byte	38
	.word	22004
	.byte	1,1,34
	.word	381
	.byte	0,3
	.word	100604
	.byte	36
	.byte	'IfxStdIf_DPipe_GetTxTimeStamp',0,34,139,1,24
	.word	100617
	.byte	36
	.byte	'IfxStdIf_DPipe_ResetSendCount',0,34,229,1,16
	.word	100379
	.byte	10
	.byte	'IfxStdIf_DPipe_',0,34,233,1,8,76,13
	.byte	'driver',0
	.word	99885
	.byte	4,2,35,0,13
	.byte	'txDisabled',0
	.word	642
	.byte	1,2,35,4,13
	.byte	'write',0
	.word	99956
	.byte	4,2,35,8,13
	.byte	'read',0
	.word	99985
	.byte	4,2,35,12,13
	.byte	'getReadCount',0
	.word	100031
	.byte	4,2,35,16,13
	.byte	'getReadEvent',0
	.word	100095
	.byte	4,2,35,20,13
	.byte	'getWriteCount',0
	.word	100131
	.byte	4,2,35,24,13
	.byte	'getWriteEvent',0
	.word	100174
	.byte	4,2,35,28,13
	.byte	'canReadCount',0
	.word	100240
	.byte	4,2,35,32,13
	.byte	'canWriteCount',0
	.word	100277
	.byte	4,2,35,36,13
	.byte	'flushTx',0
	.word	100338
	.byte	4,2,35,40,13
	.byte	'clearTx',0
	.word	100384
	.byte	4,2,35,44,13
	.byte	'clearRx',0
	.word	100416
	.byte	4,2,35,48,13
	.byte	'onReceive',0
	.word	100448
	.byte	4,2,35,52,13
	.byte	'onTransmit',0
	.word	100482
	.byte	4,2,35,56,13
	.byte	'onError',0
	.word	100517
	.byte	4,2,35,60,13
	.byte	'getSendCount',0
	.word	100567
	.byte	4,2,35,64,13
	.byte	'getTxTimeStamp',0
	.word	100622
	.byte	4,2,35,68,13
	.byte	'resetSendCount',0
	.word	100661
	.byte	4,2,35,72,0,36
	.byte	'IfxStdIf_DPipe',0,34,71,32
	.word	100700
	.byte	3
	.word	375
	.byte	3
	.word	99923
	.byte	3
	.word	99923
	.byte	3
	.word	100013
	.byte	3
	.word	100077
	.byte	3
	.word	100013
	.byte	3
	.word	100077
	.byte	3
	.word	100212
	.byte	3
	.word	100212
	.byte	3
	.word	100315
	.byte	3
	.word	100370
	.byte	3
	.word	100370
	.byte	3
	.word	100370
	.byte	3
	.word	100370
	.byte	3
	.word	100370
	.byte	3
	.word	100549
	.byte	3
	.word	100604
	.byte	3
	.word	100370
	.byte	14
	.word	642
	.byte	3
	.word	101213
	.byte	36
	.byte	'IfxStdIf_DPipe_WriteEvent',0,34,73,32
	.word	101218
	.byte	36
	.byte	'IfxStdIf_DPipe_ReadEvent',0,34,74,32
	.word	101218
	.byte	20,35,252,1,9,1,11
	.byte	'parityError',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'frameError',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'rxFifoOverflow',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'rxFifoUnderflow',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'txFifoOverflow',0,1
	.word	642
	.byte	1,3,2,35,0,0,36
	.byte	'IfxAsclin_Asc_ErrorFlags',0,35,131,2,3
	.word	101290
	.byte	20,35,137,2,9,8,13
	.byte	'baudrate',0
	.word	263
	.byte	4,2,35,0,13
	.byte	'prescaler',0
	.word	659
	.byte	2,2,35,4,13
	.byte	'oversampling',0
	.word	93756
	.byte	1,2,35,6,0,36
	.byte	'IfxAsclin_Asc_BaudRate',0,35,142,2,3
	.word	101455
	.byte	20,35,146,2,9,2,13
	.byte	'medianFilter',0
	.word	95946
	.byte	1,2,35,0,13
	.byte	'samplePointPosition',0
	.word	95384
	.byte	1,2,35,1,0,36
	.byte	'IfxAsclin_Asc_BitTimingControl',0,35,150,2,3
	.word	101553
	.byte	20,35,154,2,9,6,13
	.byte	'inWidth',0
	.word	96700
	.byte	1,2,35,0,13
	.byte	'outWidth',0
	.word	95179
	.byte	1,2,35,1,13
	.byte	'txFifoInterruptLevel',0
	.word	96867
	.byte	1,2,35,2,13
	.byte	'rxFifoInterruptLevel',0
	.word	94565
	.byte	1,2,35,3,13
	.byte	'buffMode',0
	.word	94329
	.byte	1,2,35,4,0,36
	.byte	'IfxAsclin_Asc_FifoControl',0,35,161,2,3
	.word	101651
	.byte	20,35,165,2,9,8,13
	.byte	'idleDelay',0
	.word	93065
	.byte	1,2,35,0,13
	.byte	'stopBit',0
	.word	96365
	.byte	1,2,35,1,13
	.byte	'frameMode',0
	.word	92776
	.byte	1,2,35,2,13
	.byte	'shiftDir',0
	.word	96048
	.byte	1,2,35,3,13
	.byte	'parityType',0
	.word	94237
	.byte	1,2,35,4,13
	.byte	'dataLength',0
	.word	92332
	.byte	1,2,35,5,13
	.byte	'parityBit',0
	.word	642
	.byte	1,2,35,6,0,36
	.byte	'IfxAsclin_Asc_FrameControl',0,35,174,2,3
	.word	101806
	.byte	20,35,178,2,9,8,13
	.byte	'txPriority',0
	.word	659
	.byte	2,2,35,0,13
	.byte	'rxPriority',0
	.word	659
	.byte	2,2,35,2,13
	.byte	'erPriority',0
	.word	659
	.byte	2,2,35,4,13
	.byte	'typeOfService',0
	.word	91155
	.byte	1,2,35,6,0,36
	.byte	'IfxAsclin_Asc_InterruptConfig',0,35,184,2,3
	.word	101981
	.byte	22
	.word	91233
	.byte	3
	.word	102110
	.byte	22
	.word	91314
	.byte	3
	.word	102120
	.byte	22
	.word	91394
	.byte	3
	.word	102130
	.byte	22
	.word	91642
	.byte	3
	.word	102140
	.byte	20,35,188,2,9,32,13
	.byte	'cts',0
	.word	102115
	.byte	4,2,35,0,13
	.byte	'ctsMode',0
	.word	9765
	.byte	1,2,35,4,13
	.byte	'rx',0
	.word	102125
	.byte	4,2,35,8,13
	.byte	'rxMode',0
	.word	9765
	.byte	1,2,35,12,13
	.byte	'rts',0
	.word	102135
	.byte	4,2,35,16,13
	.byte	'rtsMode',0
	.word	9970
	.byte	1,2,35,20,13
	.byte	'tx',0
	.word	102145
	.byte	4,2,35,24,13
	.byte	'txMode',0
	.word	9970
	.byte	1,2,35,28,13
	.byte	'pinDriver',0
	.word	89950
	.byte	1,2,35,29,0,36
	.byte	'IfxAsclin_Asc_Pins',0,35,199,2,3
	.word	102150
	.byte	12,35,205,2,9,1,13
	.byte	'ALL',0
	.word	642
	.byte	1,2,35,0,13
	.byte	'flags',0
	.word	101290
	.byte	1,2,35,0,0,36
	.byte	'IfxAsclin_Asc_ErrorFlagsUnion',0,35,209,2,3
	.word	102320
	.byte	36
	.byte	'uart_tx_pin_enum',0,22,74,2
	.word	24360
	.byte	36
	.byte	'uart_rx_pin_enum',0,22,100,2
	.word	24812
	.byte	36
	.byte	'uart_index_enum',0,22,109,2
	.word	23975
	.byte	36
	.byte	'debug_output_struct',0,23,99,2
	.word	25228
.L241:
	.byte	14
	.word	642
.L242:
	.byte	14
	.word	642
.L243:
	.byte	18,64
	.word	642
	.byte	19,63,0,0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L84:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,21,0,54,15,39,12,0,0,3,15,0,73,19,0,0,4,46,1,3,8,32,13
	.byte	58,15,59,15,57,15,54,15,39,12,0,0,5,5,0,3,8,58,15,59,15,57,15,73,19,0,0,6,11,0,0,0,7,36,0,3,8,11,15,62
	.byte	15,0,0,8,46,1,3,8,32,13,58,15,59,15,57,15,73,19,54,15,39,12,0,0,9,59,0,3,8,0,0,10,19,1,3,8,58,15,59,15
	.byte	57,15,11,15,0,0,11,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,12,23,1,58,15,59,15,57,15,11,15,0,0,13,13
	.byte	0,3,8,73,19,11,15,56,9,0,0,14,53,0,73,19,0,0,15,4,1,58,15,59,15,57,15,11,15,0,0,16,40,0,3,8,28,13,0,0
	.byte	17,11,1,0,0,18,1,1,11,15,73,19,0,0,19,33,0,47,15,0,0,20,19,1,58,15,59,15,57,15,11,15,0,0,21,55,0,73,19
	.byte	0,0,22,38,0,73,19,0,0,23,46,1,3,8,58,15,59,15,57,15,73,19,54,15,39,12,63,12,60,12,0,0,24,5,0,58,15,59
	.byte	15,57,15,73,19,0,0,25,24,0,58,15,59,15,57,15,0,0,26,46,0,3,8,58,15,59,15,57,15,54,15,39,12,63,12,60,12
	.byte	0,0,27,46,1,49,19,0,0,28,5,0,49,19,0,0,29,29,1,49,19,0,0,30,11,0,49,19,0,0,31,11,1,49,19,0,0,32,46,1,3
	.byte	8,58,15,59,15,57,15,54,15,39,12,63,12,60,12,0,0,33,21,1,54,15,39,12,0,0,34,5,0,73,19,0,0,35,46,0,3,8,58
	.byte	15,59,15,57,15,54,15,63,12,60,12,0,0,36,22,0,3,8,58,15,59,15,57,15,73,19,0,0,37,21,0,54,15,0,0,38,21,1
	.byte	73,19,54,15,39,12,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L85:
	.word	.L288-.L287
.L287:
	.half	3
	.word	.L290-.L289
.L289:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg',0
	.byte	'F:\\ADS\\AURIX-Studio-1.10.2\\tools\\Compilers\\Tasking_1.1r8\\ctc\\include\\',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc',0
	.byte	0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_Intrinsics.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Scu\\Std\\IfxScuWdt.h',0
	.byte	0,0,0
	.byte	'IfxScu_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxCpu_cfg.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Port\\Std\\IfxPort.h',0
	.byte	0,0,0
	.byte	'IfxPort_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Scu\\Std\\IfxScuCcu.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Src\\Std\\IfxSrc.h',0
	.byte	0,0,0
	.byte	'IfxSrc_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Asclin\\Std\\IfxAsclin.h',0
	.byte	0,0,0
	.byte	'IfxAsclin_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Lib\\DataHandling\\Ifx_Fifo.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Stm\\Std\\IfxStm.h',0
	.byte	0,0,0
	.byte	'IfxStm_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\SysSe\\Bsp\\Bsp.h',0
	.byte	0,0,0
	.byte	'stdio.h',0,2,0,0
	.byte	'string.h',0,2,0,0
	.byte	'..\\libraries\\zf_common\\zf_common_interrupt.h',0,0,0,0
	.byte	'..\\libraries\\zf_common\\zf_common_fifo.h',0,0,0,0
	.byte	'zf_driver_uart.h',0,3,0,0
	.byte	'..\\libraries\\zf_common\\zf_common_debug.h',0,0,0,0
	.byte	'../libraries/zf_common/zf_common_debug.c',0,0,0,0
	.byte	'Platform_Types.h',0,4,0,0
	.byte	'ifx_types.h',0,4,0,0
	.byte	'..\\libraries\\zf_common\\zf_common_typedef.h',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxScu_cfg.h',0
	.byte	0,0,0
	.byte	'IfxCpu_regdef.h',0,1,0,0
	.byte	'IfxFlash_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxSrc_cfg.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_PinMap\\IfxAsclin_PinMap.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\StdIf\\IfxStdIf.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\StdIf\\IfxStdIf_DPipe.h',0
	.byte	0,0,0
	.byte	'ifxAsclin_Asc.h',0,5,0,0,0
.L290:
.L288:
	.sdecl	'.debug_info',debug,cluster('fgetc')
	.sect	'.debug_info'
.L86:
	.word	264
	.half	3
	.word	.L87
	.byte	4,1
	.byte	'../libraries/zf_common/zf_common_debug.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L89,.L88
	.byte	2
	.word	.L82
	.byte	3
	.byte	'fgetc',0,1,149,2,5
	.word	.L173
	.byte	1,1,1
	.word	.L67,.L174,.L66
	.byte	4
	.byte	'f',0,1,149,2,17
	.word	.L175,.L176
	.byte	5
	.word	.L67,.L174
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('fgetc')
	.sect	'.debug_abbrev'
.L87:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('fgetc')
	.sect	'.debug_line'
.L88:
	.word	.L292-.L291
.L291:
	.half	3
	.word	.L294-.L293
.L293:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_common/zf_common_debug.c',0,0,0,0,0
.L294:
	.byte	5,27,7,0,5,2
	.word	.L67
	.byte	3,150,2,1,5,5,9
	.half	.L276-.L67
	.byte	1,5,1,9
	.half	.L40-.L276
	.byte	3,1,1,7,9
	.half	.L90-.L40
	.byte	0,1,1
.L292:
	.sdecl	'.debug_ranges',debug,cluster('fgetc')
	.sect	'.debug_ranges'
.L89:
	.word	-1,.L67,0,.L90-.L67,0,0
	.sdecl	'.debug_info',debug,cluster('fputc')
	.sect	'.debug_info'
.L91:
	.word	285
	.half	3
	.word	.L92
	.byte	4,1
	.byte	'../libraries/zf_common/zf_common_debug.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L94,.L93
	.byte	2
	.word	.L82
	.byte	3
	.byte	'fputc',0,1,138,2,5
	.word	.L173
	.byte	1,1,1
	.word	.L65,.L177,.L64
	.byte	4
	.byte	'ch',0,1,138,2,15
	.word	.L173,.L178
	.byte	4
	.byte	'stream',0,1,138,2,25
	.word	.L175,.L179
	.byte	5
	.word	.L65,.L177
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('fputc')
	.sect	'.debug_abbrev'
.L92:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('fputc')
	.sect	'.debug_line'
.L93:
	.word	.L296-.L295
.L295:
	.half	3
	.word	.L298-.L297
.L297:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_common/zf_common_debug.c',0,0,0,0,0
.L298:
	.byte	5,5,7,0,5,2
	.word	.L65
	.byte	3,137,2,1,5,21,9
	.half	.L274-.L65
	.byte	3,2,1,5,39,9
	.half	.L272-.L274
	.byte	1,5,5,9
	.half	.L273-.L272
	.byte	3,1,1,5,1,9
	.half	.L39-.L273
	.byte	3,1,1,7,9
	.half	.L95-.L39
	.byte	0,1,1
.L296:
	.sdecl	'.debug_ranges',debug,cluster('fputc')
	.sect	'.debug_ranges'
.L94:
	.word	-1,.L65,0,.L95-.L65,0,0
	.sdecl	'.debug_info',debug,cluster('debug_interrupr_handler')
	.sect	'.debug_info'
.L96:
	.word	263
	.half	3
	.word	.L97
	.byte	4,1
	.byte	'../libraries/zf_common/zf_common_debug.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L99,.L98
	.byte	2
	.word	.L82
	.byte	3
	.byte	'debug_interrupr_handler',0,1,247,1,6,1,1,1
	.word	.L63,.L180,.L62
	.byte	4
	.word	.L63,.L180
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('debug_interrupr_handler')
	.sect	'.debug_abbrev'
.L97:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('debug_interrupr_handler')
	.sect	'.debug_line'
.L98:
	.word	.L300-.L299
.L299:
	.half	3
	.word	.L302-.L301
.L301:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_common/zf_common_debug.c',0,0,0,0,0
.L302:
	.byte	5,8,7,0,5,2
	.word	.L63
	.byte	3,248,1,1,5,5,9
	.half	.L303-.L63
	.byte	1,5,25,7,9
	.half	.L304-.L303
	.byte	3,2,1,5,44,9
	.half	.L305-.L304
	.byte	1,5,28,9
	.half	.L306-.L305
	.byte	3,1,1,5,46,9
	.half	.L307-.L306
	.byte	1,5,63,9
	.half	.L308-.L307
	.byte	1,5,1,9
	.half	.L38-.L308
	.byte	3,2,1,7,9
	.half	.L100-.L38
	.byte	0,1,1
.L300:
	.sdecl	'.debug_ranges',debug,cluster('debug_interrupr_handler')
	.sect	'.debug_ranges'
.L99:
	.word	-1,.L63,0,.L100-.L63,0,0
	.sdecl	'.debug_info',debug,cluster('debug_read_ring_buffer')
	.sect	'.debug_info'
.L101:
	.word	301
	.half	3
	.word	.L102
	.byte	4,1
	.byte	'../libraries/zf_common/zf_common_debug.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L104,.L103
	.byte	2
	.word	.L82
	.byte	3
	.byte	'debug_read_ring_buffer',0,1,233,1,8
	.word	.L181
	.byte	1,1,1
	.word	.L61,.L182,.L60
	.byte	4
	.byte	'buff',0,1,233,1,39
	.word	.L183,.L184
	.byte	4
	.byte	'len',0,1,233,1,52
	.word	.L181,.L185
	.byte	5
	.word	.L61,.L182
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('debug_read_ring_buffer')
	.sect	'.debug_abbrev'
.L102:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('debug_read_ring_buffer')
	.sect	'.debug_line'
.L103:
	.word	.L310-.L309
.L309:
	.half	3
	.word	.L312-.L311
.L311:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_common/zf_common_debug.c',0,0,0,0,0
.L312:
	.byte	5,8,7,0,5,2
	.word	.L61
	.byte	3,232,1,1,5,23,9
	.half	.L313-.L61
	.byte	3,2,1,5,47,9
	.half	.L314-.L313
	.byte	1,5,52,9
	.half	.L315-.L314
	.byte	1,5,12,9
	.half	.L271-.L315
	.byte	3,1,1,5,5,9
	.half	.L316-.L271
	.byte	1,5,1,9
	.half	.L37-.L316
	.byte	3,1,1,7,9
	.half	.L105-.L37
	.byte	0,1,1
.L310:
	.sdecl	'.debug_ranges',debug,cluster('debug_read_ring_buffer')
	.sect	'.debug_ranges'
.L104:
	.word	-1,.L61,0,.L105-.L61,0,0
	.sdecl	'.debug_info',debug,cluster('debug_send_buffer')
	.sect	'.debug_info'
.L106:
	.word	296
	.half	3
	.word	.L107
	.byte	4,1
	.byte	'../libraries/zf_common/zf_common_debug.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L109,.L108
	.byte	2
	.word	.L82
	.byte	3
	.byte	'debug_send_buffer',0,1,217,1,8
	.word	.L181
	.byte	1,1,1
	.word	.L59,.L186,.L58
	.byte	4
	.byte	'buff',0,1,217,1,39
	.word	.L187,.L188
	.byte	4
	.byte	'len',0,1,217,1,52
	.word	.L181,.L189
	.byte	5
	.word	.L59,.L186
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('debug_send_buffer')
	.sect	'.debug_abbrev'
.L107:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('debug_send_buffer')
	.sect	'.debug_line'
.L108:
	.word	.L318-.L317
.L317:
	.half	3
	.word	.L320-.L319
.L319:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_common/zf_common_debug.c',0,0,0,0,0
.L320:
	.byte	5,8,7,0,5,2
	.word	.L59
	.byte	3,216,1,1,5,23,9
	.half	.L266-.L59
	.byte	3,2,1,5,47,9
	.half	.L265-.L266
	.byte	1,5,12,9
	.half	.L264-.L265
	.byte	3,1,1,5,5,9
	.half	.L321-.L264
	.byte	1,5,1,9
	.half	.L36-.L321
	.byte	3,1,1,7,9
	.half	.L110-.L36
	.byte	0,1,1
.L318:
	.sdecl	'.debug_ranges',debug,cluster('debug_send_buffer')
	.sect	'.debug_ranges'
.L109:
	.word	-1,.L59,0,.L110-.L59,0,0
	.sdecl	'.debug_info',debug,cluster('debug_assert_enable')
	.sect	'.debug_info'
.L111:
	.word	259
	.half	3
	.word	.L112
	.byte	4,1
	.byte	'../libraries/zf_common/zf_common_debug.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L114,.L113
	.byte	2
	.word	.L82
	.byte	3
	.byte	'debug_assert_enable',0,1,162,2,6,1,1,1
	.word	.L69,.L190,.L68
	.byte	4
	.word	.L69,.L190
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('debug_assert_enable')
	.sect	'.debug_abbrev'
.L112:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('debug_assert_enable')
	.sect	'.debug_line'
.L113:
	.word	.L323-.L322
.L322:
	.half	3
	.word	.L325-.L324
.L324:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_common/zf_common_debug.c',0,0,0,0,0
.L325:
	.byte	5,5,7,0,5,2
	.word	.L69
	.byte	3,163,2,1,5,30,9
	.half	.L326-.L69
	.byte	1,5,28,9
	.half	.L327-.L326
	.byte	1,5,1,9
	.half	.L328-.L327
	.byte	3,1,1,7,9
	.half	.L115-.L328
	.byte	0,1,1
.L323:
	.sdecl	'.debug_ranges',debug,cluster('debug_assert_enable')
	.sect	'.debug_ranges'
.L114:
	.word	-1,.L69,0,.L115-.L69,0,0
	.sdecl	'.debug_info',debug,cluster('debug_assert_disable')
	.sect	'.debug_info'
.L116:
	.word	260
	.half	3
	.word	.L117
	.byte	4,1
	.byte	'../libraries/zf_common/zf_common_debug.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L119,.L118
	.byte	2
	.word	.L82
	.byte	3
	.byte	'debug_assert_disable',0,1,174,2,6,1,1,1
	.word	.L71,.L191,.L70
	.byte	4
	.word	.L71,.L191
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('debug_assert_disable')
	.sect	'.debug_abbrev'
.L117:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('debug_assert_disable')
	.sect	'.debug_line'
.L118:
	.word	.L330-.L329
.L329:
	.half	3
	.word	.L332-.L331
.L331:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_common/zf_common_debug.c',0,0,0,0,0
.L332:
	.byte	5,5,7,0,5,2
	.word	.L71
	.byte	3,175,2,1,5,30,9
	.half	.L333-.L71
	.byte	1,5,28,9
	.half	.L334-.L333
	.byte	1,5,1,9
	.half	.L335-.L334
	.byte	3,1,1,7,9
	.half	.L120-.L335
	.byte	0,1,1
.L330:
	.sdecl	'.debug_ranges',debug,cluster('debug_assert_disable')
	.sect	'.debug_ranges'
.L119:
	.word	-1,.L71,0,.L120-.L71,0,0
	.sdecl	'.debug_info',debug,cluster('debug_assert_handler')
	.sect	'.debug_info'
.L121:
	.word	358
	.half	3
	.word	.L122
	.byte	4,1
	.byte	'../libraries/zf_common/zf_common_debug.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L124,.L123
	.byte	2
	.word	.L82
	.byte	3
	.byte	'debug_assert_handler',0,1,189,2,6,1,1,1
	.word	.L73,.L192,.L72
	.byte	4
	.byte	'pass',0,1,189,2,34
	.word	.L193,.L194
	.byte	4
	.byte	'file',0,1,189,2,46
	.word	.L195,.L196
	.byte	4
	.byte	'line',0,1,189,2,56
	.word	.L173,.L197
	.byte	5
	.word	.L73,.L192
	.byte	5
	.word	.L42,.L192
	.byte	6
	.byte	'assert_nest_index',0,1,196,2,18
	.word	.L193
	.byte	5,3
	.word	_999001_assert_nest_index
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('debug_assert_handler')
	.sect	'.debug_abbrev'
.L122:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_line',debug,cluster('debug_assert_handler')
	.sect	'.debug_line'
.L123:
	.word	.L337-.L336
.L336:
	.half	3
	.word	.L339-.L338
.L338:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_common/zf_common_debug.c',0,0,0,0,0
.L339:
	.byte	5,6,7,0,5,2
	.word	.L73
	.byte	3,188,2,1,5,8,9
	.half	.L279-.L73
	.byte	3,2,1,5,17,7,9
	.half	.L340-.L279
	.byte	1,5,9,9
	.half	.L41-.L340
	.byte	3,2,1,5,13,9
	.half	.L42-.L41
	.byte	3,5,1,5,5,9
	.half	.L341-.L42
	.byte	1,5,19,7,9
	.half	.L342-.L341
	.byte	3,2,1,5,5,9
	.half	.L44-.L342
	.byte	3,2,1,5,23,9
	.half	.L343-.L44
	.byte	1,5,28,9
	.half	.L344-.L343
	.byte	3,2,1,5,15,9
	.half	.L277-.L344
	.byte	3,2,1,5,22,9
	.half	.L48-.L277
	.byte	3,14,1,5,50,9
	.half	.L345-.L48
	.byte	1,5,20,9
	.half	.L281-.L345
	.byte	3,1,1,5,15,9
	.half	.L47-.L281
	.byte	3,113,1,5,1,9
	.half	.L43-.L47
	.byte	3,17,1,7,9
	.half	.L125-.L43
	.byte	0,1,1
.L337:
	.sdecl	'.debug_ranges',debug,cluster('debug_assert_handler')
	.sect	'.debug_ranges'
.L124:
	.word	-1,.L73,0,.L125-.L73,0,0
	.sdecl	'.debug_info',debug,cluster('debug_log_handler')
	.sect	'.debug_info'
.L126:
	.word	328
	.half	3
	.word	.L127
	.byte	4,1
	.byte	'../libraries/zf_common/zf_common_debug.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L129,.L128
	.byte	2
	.word	.L82
	.byte	3
	.byte	'debug_log_handler',0,1,236,2,6,1,1,1
	.word	.L75,.L198,.L74
	.byte	4
	.byte	'pass',0,1,236,2,31
	.word	.L193,.L199
	.byte	4
	.byte	'str',0,1,236,2,43
	.word	.L195,.L200
	.byte	4
	.byte	'file',0,1,236,2,54
	.word	.L195,.L201
	.byte	4
	.byte	'line',0,1,236,2,64
	.word	.L173,.L202
	.byte	5
	.word	.L75,.L198
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('debug_log_handler')
	.sect	'.debug_abbrev'
.L127:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('debug_log_handler')
	.sect	'.debug_line'
.L128:
	.word	.L347-.L346
.L346:
	.half	3
	.word	.L349-.L348
.L348:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_common/zf_common_debug.c',0,0,0,0,0
.L349:
	.byte	5,6,7,0,5,2
	.word	.L75
	.byte	3,235,2,1,5,5,9
	.half	.L285-.L75
	.byte	3,2,1,5,9,7,9
	.half	.L350-.L285
	.byte	3,2,1,5,8,9
	.half	.L49-.L350
	.byte	3,2,1,5,5,9
	.half	.L351-.L49
	.byte	1,5,22,7,9
	.half	.L352-.L351
	.byte	3,2,1,5,49,9
	.half	.L353-.L352
	.byte	1,5,1,9
	.half	.L50-.L353
	.byte	3,2,1,7,9
	.half	.L130-.L50
	.byte	0,1,1
.L347:
	.sdecl	'.debug_ranges',debug,cluster('debug_log_handler')
	.sect	'.debug_ranges'
.L129:
	.word	-1,.L75,0,.L130-.L75,0,0
	.sdecl	'.debug_info',debug,cluster('debug_output_struct_init')
	.sect	'.debug_info'
.L131:
	.word	282
	.half	3
	.word	.L132
	.byte	4,1
	.byte	'../libraries/zf_common/zf_common_debug.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L134,.L133
	.byte	2
	.word	.L82
	.byte	3
	.byte	'debug_output_struct_init',0,1,255,2,6,1,1,1
	.word	.L77,.L203,.L76
	.byte	4
	.byte	'info',0,1,255,2,53
	.word	.L204,.L205
	.byte	5
	.word	.L77,.L203
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('debug_output_struct_init')
	.sect	'.debug_abbrev'
.L132:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('debug_output_struct_init')
	.sect	'.debug_line'
.L133:
	.word	.L355-.L354
.L354:
	.half	3
	.word	.L357-.L356
.L356:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_common/zf_common_debug.c',0,0,0,0,0
.L357:
	.byte	5,35,7,0,5,2
	.word	.L77
	.byte	3,128,3,1,5,33,9
	.half	.L358-.L77
	.byte	1,5,35,9
	.half	.L359-.L358
	.byte	3,2,1,5,33,9
	.half	.L360-.L359
	.byte	1,5,35,9
	.half	.L361-.L360
	.byte	3,1,1,5,33,9
	.half	.L362-.L361
	.byte	1,5,35,9
	.half	.L363-.L362
	.byte	3,2,1,5,33,9
	.half	.L364-.L363
	.byte	1,5,35,9
	.half	.L365-.L364
	.byte	3,1,1,5,33,9
	.half	.L366-.L365
	.byte	1,5,35,9
	.half	.L367-.L366
	.byte	3,2,1,5,33,9
	.half	.L368-.L367
	.byte	1,5,35,9
	.half	.L369-.L368
	.byte	3,1,1,5,33,9
	.half	.L370-.L369
	.byte	1,5,35,9
	.half	.L371-.L370
	.byte	3,1,1,5,33,9
	.half	.L372-.L371
	.byte	1,5,1,9
	.half	.L373-.L372
	.byte	3,1,1,7,9
	.half	.L135-.L373
	.byte	0,1,1
.L355:
	.sdecl	'.debug_ranges',debug,cluster('debug_output_struct_init')
	.sect	'.debug_ranges'
.L134:
	.word	-1,.L77,0,.L135-.L77,0,0
	.sdecl	'.debug_info',debug,cluster('debug_output_init')
	.sect	'.debug_info'
.L136:
	.word	275
	.half	3
	.word	.L137
	.byte	4,1
	.byte	'../libraries/zf_common/zf_common_debug.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L139,.L138
	.byte	2
	.word	.L82
	.byte	3
	.byte	'debug_output_init',0,1,149,3,6,1,1,1
	.word	.L79,.L206,.L78
	.byte	4
	.byte	'info',0,1,149,3,46
	.word	.L204,.L207
	.byte	5
	.word	.L79,.L206
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('debug_output_init')
	.sect	'.debug_abbrev'
.L137:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('debug_output_init')
	.sect	'.debug_line'
.L138:
	.word	.L375-.L374
.L374:
	.half	3
	.word	.L377-.L376
.L376:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_common/zf_common_debug.c',0,0,0,0,0
.L377:
	.byte	5,5,7,0,5,2
	.word	.L79
	.byte	3,150,3,1,5,51,9
	.half	.L378-.L79
	.byte	1,5,45,9
	.half	.L379-.L378
	.byte	1,5,5,9
	.half	.L380-.L379
	.byte	3,2,1,5,51,9
	.half	.L381-.L380
	.byte	1,5,45,9
	.half	.L382-.L381
	.byte	1,5,5,9
	.half	.L383-.L382
	.byte	3,1,1,5,51,9
	.half	.L384-.L383
	.byte	1,5,45,9
	.half	.L385-.L384
	.byte	1,5,5,9
	.half	.L386-.L385
	.byte	3,2,1,5,51,9
	.half	.L387-.L386
	.byte	1,5,45,9
	.half	.L388-.L387
	.byte	1,5,5,9
	.half	.L389-.L388
	.byte	3,1,1,5,51,9
	.half	.L390-.L389
	.byte	1,5,45,9
	.half	.L391-.L390
	.byte	1,5,5,9
	.half	.L392-.L391
	.byte	3,2,1,5,51,9
	.half	.L393-.L392
	.byte	1,5,45,9
	.half	.L394-.L393
	.byte	1,5,5,9
	.half	.L395-.L394
	.byte	3,1,1,5,51,9
	.half	.L396-.L395
	.byte	1,5,45,9
	.half	.L397-.L396
	.byte	1,5,5,9
	.half	.L398-.L397
	.byte	3,1,1,5,51,9
	.half	.L399-.L398
	.byte	1,5,45,9
	.half	.L400-.L399
	.byte	1,5,5,9
	.half	.L401-.L400
	.byte	3,2,1,5,26,9
	.half	.L402-.L401
	.byte	1,5,24,9
	.half	.L403-.L402
	.byte	1,5,1,9
	.half	.L404-.L403
	.byte	3,1,1,7,9
	.half	.L140-.L404
	.byte	0,1,1
.L375:
	.sdecl	'.debug_ranges',debug,cluster('debug_output_init')
	.sect	'.debug_ranges'
.L139:
	.word	-1,.L79,0,.L140-.L79,0,0
	.sdecl	'.debug_info',debug,cluster('debug_init')
	.sect	'.debug_info'
.L141:
	.word	269
	.half	3
	.word	.L142
	.byte	4,1
	.byte	'../libraries/zf_common/zf_common_debug.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L144,.L143
	.byte	2
	.word	.L82
	.byte	3
	.byte	'debug_init',0,1,173,3,6,1,1,1
	.word	.L81,.L208,.L80
	.byte	4
	.word	.L81,.L208
	.byte	5
	.byte	'info',0,1,175,3,25
	.word	.L209,.L210
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('debug_init')
	.sect	'.debug_abbrev'
.L142:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('debug_init')
	.sect	'.debug_line'
.L143:
	.word	.L406-.L405
.L405:
	.half	3
	.word	.L408-.L407
.L407:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_common/zf_common_debug.c',0,0,0,0,0
.L408:
	.byte	5,6,7,0,5,2
	.word	.L81
	.byte	3,172,3,1,5,31,9
	.half	.L286-.L81
	.byte	3,3,1,5,24,9
	.half	.L409-.L286
	.byte	3,1,1,5,22,9
	.half	.L410-.L409
	.byte	1,5,24,9
	.half	.L411-.L410
	.byte	3,1,1,5,15,9
	.half	.L412-.L411
	.byte	3,2,1,9
	.half	.L413-.L412
	.byte	3,1,1,9
	.half	.L414-.L413
	.byte	3,1,1,9
	.half	.L415-.L414
	.byte	3,1,1,5,16,9
	.half	.L416-.L415
	.byte	3,2,1,5,33,9
	.half	.L417-.L416
	.byte	1,5,49,9
	.half	.L418-.L417
	.byte	1,5,68,9
	.half	.L419-.L418
	.byte	1,5,23,9
	.half	.L420-.L419
	.byte	3,1,1,5,41,9
	.half	.L421-.L420
	.byte	1,5,1,9
	.half	.L422-.L421
	.byte	3,2,1,7,9
	.half	.L145-.L422
	.byte	0,1,1
.L406:
	.sdecl	'.debug_ranges',debug,cluster('debug_init')
	.sect	'.debug_ranges'
.L144:
	.word	-1,.L81,0,.L145-.L81,0,0
	.sdecl	'.debug_info',debug,cluster('debug_delay')
	.sect	'.debug_info'
.L146:
	.word	288
	.half	3
	.word	.L147
	.byte	4,1
	.byte	'../libraries/zf_common/zf_common_debug.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L149,.L148
	.byte	2
	.word	.L82
	.byte	3
	.byte	'debug_delay',0,1,59,13,1,1
	.word	.L53,.L211,.L52
	.byte	4
	.word	.L53,.L211
	.byte	5
	.byte	'loop_1',0,1,61,13
	.word	.L212,.L213
	.byte	5
	.byte	'loop_2',0,1,61,25
	.word	.L214,.L215
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('debug_delay')
	.sect	'.debug_abbrev'
.L147:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('debug_delay')
	.sect	'.debug_line'
.L148:
	.word	.L424-.L423
.L423:
	.half	3
	.word	.L426-.L425
.L425:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_common/zf_common_debug.c',0,0,0,0,0
.L426:
	.byte	5,13,7,0,5,2
	.word	.L53
	.byte	3,58,1,5,22,9
	.half	.L245-.L53
	.byte	3,2,1,5,20,9
	.half	.L427-.L245
	.byte	1,5,34,9
	.half	.L428-.L427
	.byte	1,5,32,9
	.half	.L429-.L428
	.byte	1,5,18,9
	.half	.L430-.L429
	.byte	3,1,1,5,16,9
	.half	.L431-.L430
	.byte	1,5,36,9
	.half	.L432-.L431
	.byte	1,5,22,9
	.half	.L3-.L432
	.byte	3,1,1,5,20,9
	.half	.L433-.L3
	.byte	1,5,41,9
	.half	.L434-.L433
	.byte	1,5,43,9
	.half	.L5-.L434
	.byte	1,5,50,9
	.half	.L435-.L5
	.byte	1,5,25,9
	.half	.L4-.L435
	.byte	1,5,35,9
	.half	.L436-.L4
	.byte	1,5,41,9
	.half	.L437-.L436
	.byte	1,5,38,7,9
	.half	.L438-.L437
	.byte	3,127,1,5,45,9
	.half	.L439-.L438
	.byte	1,5,21,9
	.half	.L2-.L439
	.byte	1,5,31,9
	.half	.L440-.L2
	.byte	1,5,36,9
	.half	.L441-.L440
	.byte	1,5,1,7,9
	.half	.L442-.L441
	.byte	3,2,1,7,9
	.half	.L150-.L442
	.byte	0,1,1
.L424:
	.sdecl	'.debug_ranges',debug,cluster('debug_delay')
	.sect	'.debug_ranges'
.L149:
	.word	-1,.L53,0,.L150-.L53,0,0
	.sdecl	'.debug_info',debug,cluster('debug_uart_str_output')
	.sect	'.debug_info'
.L151:
	.word	275
	.half	3
	.word	.L152
	.byte	4,1
	.byte	'../libraries/zf_common/zf_common_debug.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L154,.L153
	.byte	2
	.word	.L82
	.byte	3
	.byte	'debug_uart_str_output',0,1,73,13,1,1
	.word	.L55,.L216,.L54
	.byte	4
	.byte	'str',0,1,73,48
	.word	.L217,.L218
	.byte	5
	.word	.L55,.L216
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('debug_uart_str_output')
	.sect	'.debug_abbrev'
.L152:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('debug_uart_str_output')
	.sect	'.debug_line'
.L153:
	.word	.L444-.L443
.L443:
	.half	3
	.word	.L446-.L445
.L445:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_common/zf_common_debug.c',0,0,0,0,0
.L446:
	.byte	5,23,7,0,5,2
	.word	.L55
	.byte	3,202,0,1,5,41,9
	.half	.L447-.L55
	.byte	1,5,1,9
	.half	.L246-.L447
	.byte	3,1,1,7,9
	.half	.L155-.L246
	.byte	0,1,1
.L444:
	.sdecl	'.debug_ranges',debug,cluster('debug_uart_str_output')
	.sect	'.debug_ranges'
.L154:
	.word	-1,.L55,0,.L155-.L55,0,0
	.sdecl	'.debug_info',debug,cluster('debug_output')
	.sect	'.debug_info'
.L156:
	.word	541
	.half	3
	.word	.L157
	.byte	4,1
	.byte	'../libraries/zf_common/zf_common_debug.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L159,.L158
	.byte	2
	.word	.L82
	.byte	3
	.byte	'debug_output',0,1,90,13,1,1
	.word	.L57,.L219,.L56
	.byte	4
	.byte	'type',0,1,90,33
	.word	.L195,.L220
	.byte	4
	.byte	'file',0,1,90,45
	.word	.L195,.L221
	.byte	4
	.byte	'line',0,1,90,55
	.word	.L173,.L222
	.byte	4
	.byte	'str',0,1,90,67
	.word	.L195,.L223
	.byte	5
	.word	.L57,.L219
	.byte	6
	.byte	'file_str',0,1,92,11
	.word	.L195,.L224
	.byte	6
	.byte	'i',0,1,93,13
	.word	.L225,.L226
	.byte	6
	.byte	'j',0,1,93,20
	.word	.L227,.L228
	.byte	6
	.byte	'len_origin',0,1,94,12
	.word	.L229,.L230
	.byte	6
	.byte	'show_len',0,1,95,12
	.word	.L231,.L232
	.byte	6
	.byte	'show_line_index',0,1,96,12
	.word	.L233,.L234
	.byte	5
	.word	.L235,.L219
	.byte	6
	.byte	'output_buffer',0,1,99,10
	.word	.L236,.L237
	.byte	6
	.byte	'file_path_buffer',0,1,100,10
	.word	.L238,.L239
	.byte	5
	.word	.L8,.L7
	.byte	6
	.byte	'output_buffer',0,1,193,1,18
	.word	.L236,.L240
	.byte	0,0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('debug_output')
	.sect	'.debug_abbrev'
.L157:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3,8,58
	.byte	15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('debug_output')
	.sect	'.debug_line'
.L158:
	.word	.L449-.L448
.L448:
	.half	3
	.word	.L451-.L450
.L450:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_common/zf_common_debug.c',0,0,0,0,0
.L451:
	.byte	5,13,7,0,5,2
	.word	.L57
	.byte	3,217,0,1,5,17,9
	.half	.L253-.L57
	.byte	3,3,1,5,15,9
	.half	.L452-.L253
	.byte	1,5,24,9
	.half	.L453-.L452
	.byte	1,5,22,9
	.half	.L454-.L453
	.byte	1,5,25,9
	.half	.L455-.L454
	.byte	3,1,1,5,23,9
	.half	.L456-.L455
	.byte	1,9
	.half	.L457-.L456
	.byte	3,1,1,5,21,9
	.half	.L458-.L457
	.byte	1,5,30,9
	.half	.L459-.L458
	.byte	3,1,1,5,28,9
	.half	.L460-.L459
	.byte	1,5,25,9
	.half	.L461-.L460
	.byte	3,1,1,5,16,9
	.half	.L249-.L461
	.byte	1,5,8,9
	.half	.L235-.L249
	.byte	3,5,1,5,25,9
	.half	.L462-.L235
	.byte	1,5,5,9
	.half	.L463-.L462
	.byte	1,5,9,7,9
	.half	.L464-.L463
	.byte	3,2,1,5,26,9
	.half	.L465-.L464
	.byte	1,5,46,9
	.half	.L466-.L465
	.byte	1,5,8,9
	.half	.L6-.L466
	.byte	3,3,1,5,5,9
	.half	.L467-.L6
	.byte	1,5,12,7,9
	.half	.L468-.L467
	.byte	3,2,1,5,29,9
	.half	.L469-.L468
	.byte	1,5,9,9
	.half	.L470-.L469
	.byte	1,5,48,7,9
	.half	.L471-.L470
	.byte	3,5,1,5,64,9
	.half	.L472-.L471
	.byte	1,5,13,9
	.half	.L473-.L472
	.byte	1,5,30,9
	.half	.L474-.L473
	.byte	1,5,45,9
	.half	.L475-.L474
	.byte	1,5,64,9
	.half	.L476-.L475
	.byte	1,5,68,9
	.half	.L477-.L476
	.byte	1,5,33,9
	.half	.L255-.L477
	.byte	3,3,1,5,24,9
	.half	.L257-.L255
	.byte	1,5,25,9
	.half	.L478-.L257
	.byte	3,1,1,5,42,9
	.half	.L479-.L478
	.byte	1,5,59,9
	.half	.L480-.L479
	.byte	1,5,76,9
	.half	.L481-.L480
	.byte	1,5,57,9
	.half	.L482-.L481
	.byte	1,5,22,9
	.half	.L483-.L482
	.byte	1,5,38,9
	.half	.L484-.L483
	.byte	3,2,1,5,19,9
	.half	.L9-.L484
	.byte	1,5,28,9
	.half	.L485-.L9
	.byte	1,5,38,9
	.half	.L486-.L485
	.byte	1,5,21,7,9
	.half	.L487-.L486
	.byte	3,3,1,5,19,9
	.half	.L488-.L487
	.byte	1,5,52,9
	.half	.L489-.L488
	.byte	1,5,26,9
	.half	.L12-.L489
	.byte	3,2,1,5,21,9
	.half	.L490-.L12
	.byte	3,1,1,5,34,9
	.half	.L491-.L490
	.byte	1,5,20,9
	.half	.L492-.L491
	.byte	1,5,43,7,9
	.half	.L493-.L492
	.byte	1,5,56,9
	.half	.L494-.L493
	.byte	1,5,53,9
	.half	.L495-.L494
	.byte	1,5,21,7,9
	.half	.L13-.L495
	.byte	3,2,1,5,23,9
	.half	.L496-.L13
	.byte	1,5,54,9
	.half	.L14-.L496
	.byte	3,123,1,5,65,9
	.half	.L497-.L14
	.byte	1,5,25,9
	.half	.L11-.L497
	.byte	1,5,24,9
	.half	.L498-.L11
	.byte	1,5,36,7,9
	.half	.L499-.L498
	.byte	1,5,47,9
	.half	.L500-.L499
	.byte	1,5,16,7,9
	.half	.L15-.L500
	.byte	3,10,1,5,13,9
	.half	.L501-.L15
	.byte	1,5,26,7,9
	.half	.L502-.L501
	.byte	3,2,1,5,52,9
	.half	.L503-.L502
	.byte	3,1,1,5,25,9
	.half	.L504-.L503
	.byte	1,5,40,9
	.half	.L505-.L504
	.byte	1,5,28,9
	.half	.L506-.L505
	.byte	3,127,1,5,25,9
	.half	.L16-.L506
	.byte	3,5,1,5,17,9
	.half	.L507-.L16
	.byte	1,5,60,7,9
	.half	.L508-.L507
	.byte	3,2,1,5,29,9
	.half	.L509-.L508
	.byte	1,5,44,9
	.half	.L510-.L509
	.byte	1,5,69,9
	.half	.L511-.L510
	.byte	1,5,56,9
	.half	.L18-.L511
	.byte	3,4,1,5,29,9
	.half	.L512-.L18
	.byte	1,5,44,9
	.half	.L513-.L512
	.byte	1,5,21,9
	.half	.L17-.L513
	.byte	3,5,1,5,19,9
	.half	.L514-.L17
	.byte	1,5,68,9
	.half	.L515-.L514
	.byte	1,5,25,9
	.half	.L21-.L515
	.byte	3,2,1,5,23,9
	.half	.L516-.L21
	.byte	1,5,40,9
	.half	.L517-.L516
	.byte	1,5,31,9
	.half	.L23-.L517
	.byte	3,2,1,5,53,9
	.half	.L518-.L23
	.byte	1,5,57,9
	.half	.L519-.L518
	.byte	1,5,49,9
	.half	.L520-.L519
	.byte	1,5,51,9
	.half	.L521-.L520
	.byte	1,5,21,9
	.half	.L522-.L521
	.byte	1,5,25,7,9
	.half	.L523-.L522
	.byte	3,2,1,5,38,9
	.half	.L24-.L523
	.byte	3,2,1,5,37,9
	.half	.L524-.L24
	.byte	1,5,61,9
	.half	.L525-.L524
	.byte	1,5,65,9
	.half	.L526-.L525
	.byte	1,5,57,9
	.half	.L527-.L526
	.byte	1,5,59,9
	.half	.L528-.L527
	.byte	1,5,56,9
	.half	.L529-.L528
	.byte	1,5,41,9
	.half	.L530-.L529
	.byte	1,5,42,9
	.half	.L531-.L530
	.byte	3,122,1,5,44,9
	.half	.L532-.L531
	.byte	1,5,28,9
	.half	.L22-.L532
	.byte	1,5,32,9
	.half	.L533-.L22
	.byte	1,5,40,9
	.half	.L534-.L533
	.byte	1,5,34,7,9
	.half	.L25-.L534
	.byte	3,9,1,5,33,9
	.half	.L535-.L25
	.byte	1,5,39,9
	.half	.L536-.L535
	.byte	1,5,37,9
	.half	.L537-.L536
	.byte	1,5,52,9
	.half	.L538-.L537
	.byte	3,2,1,5,69,9
	.half	.L539-.L538
	.byte	1,5,84,9
	.half	.L540-.L539
	.byte	1,5,82,9
	.half	.L541-.L540
	.byte	1,5,84,9
	.half	.L542-.L541
	.byte	1,5,100,9
	.half	.L543-.L542
	.byte	1,5,17,9
	.half	.L544-.L543
	.byte	1,5,34,9
	.half	.L545-.L544
	.byte	1,5,49,9
	.half	.L546-.L545
	.byte	1,5,100,9
	.half	.L547-.L546
	.byte	1,5,104,9
	.half	.L548-.L547
	.byte	1,5,70,9
	.half	.L549-.L548
	.byte	3,115,1,5,72,9
	.half	.L550-.L549
	.byte	1,5,24,9
	.half	.L20-.L550
	.byte	1,5,37,9
	.half	.L551-.L20
	.byte	1,5,54,9
	.half	.L552-.L551
	.byte	1,5,52,9
	.half	.L553-.L552
	.byte	1,5,64,9
	.half	.L554-.L553
	.byte	1,5,68,9
	.half	.L555-.L554
	.byte	1,5,48,7,9
	.half	.L556-.L555
	.byte	3,17,1,5,21,9
	.half	.L557-.L556
	.byte	1,5,36,9
	.half	.L558-.L557
	.byte	1,5,48,9
	.half	.L559-.L558
	.byte	3,1,1,5,65,9
	.half	.L560-.L559
	.byte	1,5,80,9
	.half	.L561-.L560
	.byte	1,5,78,9
	.half	.L562-.L561
	.byte	1,5,80,9
	.half	.L563-.L562
	.byte	1,5,96,9
	.half	.L564-.L563
	.byte	1,5,13,9
	.half	.L565-.L564
	.byte	1,5,30,9
	.half	.L566-.L565
	.byte	1,5,45,9
	.half	.L567-.L566
	.byte	1,5,96,9
	.half	.L568-.L567
	.byte	1,5,100,9
	.half	.L569-.L568
	.byte	1,5,16,9
	.half	.L570-.L569
	.byte	3,3,1,5,13,9
	.half	.L571-.L570
	.byte	1,5,25,7,9
	.half	.L572-.L571
	.byte	3,2,1,5,23,9
	.half	.L573-.L572
	.byte	1,5,62,9
	.half	.L574-.L573
	.byte	1,5,29,9
	.half	.L28-.L574
	.byte	3,2,1,5,27,9
	.half	.L575-.L28
	.byte	1,5,44,9
	.half	.L576-.L575
	.byte	1,5,35,9
	.half	.L30-.L576
	.byte	3,2,1,5,47,9
	.half	.L259-.L30
	.byte	1,5,51,9
	.half	.L577-.L259
	.byte	1,5,43,9
	.half	.L578-.L577
	.byte	1,5,45,9
	.half	.L579-.L578
	.byte	1,5,25,9
	.half	.L580-.L579
	.byte	1,5,29,7,9
	.half	.L581-.L580
	.byte	3,2,1,5,42,9
	.half	.L31-.L581
	.byte	3,2,1,5,41,9
	.half	.L582-.L31
	.byte	1,5,55,9
	.half	.L583-.L582
	.byte	1,5,59,9
	.half	.L584-.L583
	.byte	1,5,51,9
	.half	.L585-.L584
	.byte	1,5,53,9
	.half	.L586-.L585
	.byte	1,5,50,9
	.half	.L587-.L586
	.byte	1,5,45,9
	.half	.L588-.L587
	.byte	1,5,46,9
	.half	.L589-.L588
	.byte	3,122,1,5,48,9
	.half	.L590-.L589
	.byte	1,5,32,9
	.half	.L29-.L590
	.byte	1,5,36,9
	.half	.L591-.L29
	.byte	1,5,44,9
	.half	.L592-.L591
	.byte	1,5,38,7,9
	.half	.L32-.L592
	.byte	3,9,1,5,37,9
	.half	.L593-.L32
	.byte	1,5,43,9
	.half	.L594-.L593
	.byte	1,5,41,9
	.half	.L595-.L594
	.byte	1,5,56,9
	.half	.L596-.L595
	.byte	3,2,1,5,73,9
	.half	.L597-.L596
	.byte	1,5,88,9
	.half	.L598-.L597
	.byte	1,5,86,9
	.half	.L599-.L598
	.byte	1,5,88,9
	.half	.L600-.L599
	.byte	1,5,104,9
	.half	.L601-.L600
	.byte	1,5,21,9
	.half	.L602-.L601
	.byte	1,5,38,9
	.half	.L603-.L602
	.byte	1,5,53,9
	.half	.L604-.L603
	.byte	1,5,104,9
	.half	.L605-.L604
	.byte	1,5,108,9
	.half	.L606-.L605
	.byte	1,5,64,9
	.half	.L607-.L606
	.byte	3,115,1,5,66,9
	.half	.L608-.L607
	.byte	1,5,28,9
	.half	.L27-.L608
	.byte	1,5,41,9
	.half	.L609-.L27
	.byte	1,5,48,9
	.half	.L261-.L609
	.byte	1,5,46,9
	.half	.L610-.L261
	.byte	1,5,58,9
	.half	.L611-.L610
	.byte	1,5,62,9
	.half	.L612-.L611
	.byte	1,5,13,7,9
	.half	.L26-.L612
	.byte	3,126,1,5,20,9
	.half	.L8-.L26
	.byte	3,22,1,5,35,9
	.half	.L613-.L8
	.byte	1,5,38,9
	.half	.L614-.L613
	.byte	1,5,13,9
	.half	.L615-.L614
	.byte	3,1,1,5,30,9
	.half	.L616-.L615
	.byte	1,5,43,9
	.half	.L617-.L616
	.byte	1,5,16,9
	.half	.L263-.L617
	.byte	3,1,1,5,13,9
	.half	.L618-.L263
	.byte	1,5,72,7,9
	.half	.L619-.L618
	.byte	3,2,1,5,78,9
	.half	.L620-.L619
	.byte	1,5,84,9
	.half	.L621-.L620
	.byte	1,5,25,9
	.half	.L622-.L621
	.byte	1,5,40,9
	.half	.L623-.L622
	.byte	1,5,88,9
	.half	.L624-.L623
	.byte	1,5,68,9
	.half	.L34-.L624
	.byte	3,4,1,5,74,9
	.half	.L625-.L34
	.byte	1,5,25,9
	.half	.L626-.L625
	.byte	1,5,40,9
	.half	.L627-.L626
	.byte	1,5,13,9
	.half	.L35-.L627
	.byte	3,2,1,5,30,9
	.half	.L628-.L35
	.byte	1,5,43,9
	.half	.L629-.L628
	.byte	1,5,1,9
	.half	.L7-.L629
	.byte	3,3,1,7,9
	.half	.L160-.L7
	.byte	0,1,1
.L449:
	.sdecl	'.debug_ranges',debug,cluster('debug_output')
	.sect	'.debug_ranges'
.L159:
	.word	-1,.L57,0,.L160-.L57,0,0
	.sdecl	'.debug_info',debug,cluster('debug_output_info')
	.sect	'.debug_info'
.L161:
	.word	233
	.half	3
	.word	.L162
	.byte	4,1
	.byte	'../libraries/zf_common/zf_common_debug.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L82
	.byte	3
	.byte	'debug_output_info',0,24,42,29
	.word	.L209
	.byte	5,3
	.word	debug_output_info
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('debug_output_info')
	.sect	'.debug_abbrev'
.L162:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_info',debug,cluster('zf_debug_init_flag')
	.sect	'.debug_info'
.L163:
	.word	234
	.half	3
	.word	.L164
	.byte	4,1
	.byte	'../libraries/zf_common/zf_common_debug.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L82
	.byte	3
	.byte	'zf_debug_init_flag',0,24,43,29
	.word	.L241
	.byte	5,3
	.word	zf_debug_init_flag
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('zf_debug_init_flag')
	.sect	'.debug_abbrev'
.L164:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_info',debug,cluster('zf_debug_assert_enable')
	.sect	'.debug_info'
.L165:
	.word	238
	.half	3
	.word	.L166
	.byte	4,1
	.byte	'../libraries/zf_common/zf_common_debug.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L82
	.byte	3
	.byte	'zf_debug_assert_enable',0,24,44,29
	.word	.L242
	.byte	5,3
	.word	zf_debug_assert_enable
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('zf_debug_assert_enable')
	.sect	'.debug_abbrev'
.L166:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_info',debug,cluster('debug_uart_buffer')
	.sect	'.debug_info'
.L167:
	.word	234
	.half	3
	.word	.L168
	.byte	4,1
	.byte	'../libraries/zf_common/zf_common_debug.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L82
	.byte	3
	.byte	'debug_uart_buffer',0,24,47,29
	.word	.L243
	.byte	1,5,3
	.word	debug_uart_buffer
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('debug_uart_buffer')
	.sect	'.debug_abbrev'
.L168:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('debug_uart_data')
	.sect	'.debug_info'
.L169:
	.word	232
	.half	3
	.word	.L170
	.byte	4,1
	.byte	'../libraries/zf_common/zf_common_debug.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L82
	.byte	3
	.byte	'debug_uart_data',0,24,48,29
	.word	.L193
	.byte	1,5,3
	.word	debug_uart_data
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('debug_uart_data')
	.sect	'.debug_abbrev'
.L170:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('debug_uart_fifo')
	.sect	'.debug_info'
.L171:
	.word	232
	.half	3
	.word	.L172
	.byte	4,1
	.byte	'../libraries/zf_common/zf_common_debug.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L82
	.byte	3
	.byte	'debug_uart_fifo',0,24,49,29
	.word	.L244
	.byte	1,5,3
	.word	debug_uart_fifo
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('debug_uart_fifo')
	.sect	'.debug_abbrev'
.L172:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_loc',debug,cluster('debug_assert_disable')
	.sect	'.debug_loc'
.L70:
	.word	-1,.L71,0,.L191-.L71
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('debug_assert_enable')
	.sect	'.debug_loc'
.L68:
	.word	-1,.L69,0,.L190-.L69
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('debug_assert_handler')
	.sect	'.debug_loc'
.L72:
	.word	-1,.L73,0,.L192-.L73
	.half	2
	.byte	138,0
	.word	0,0
.L196:
	.word	-1,.L73,0,.L277-.L73
	.half	1
	.byte	100
	.word	.L278-.L73,.L192-.L73
	.half	1
	.byte	111
	.word	.L280-.L73,.L281-.L73
	.half	1
	.byte	101
	.word	.L43-.L73,.L192-.L73
	.half	1
	.byte	100
	.word	0,0
.L197:
	.word	-1,.L73,0,.L277-.L73
	.half	1
	.byte	85
	.word	.L279-.L73,.L192-.L73
	.half	1
	.byte	88
	.word	.L282-.L73,.L281-.L73
	.half	1
	.byte	84
	.word	.L43-.L73,.L192-.L73
	.half	1
	.byte	85
	.word	0,0
.L194:
	.word	-1,.L73,0,.L277-.L73
	.half	1
	.byte	84
	.word	.L43-.L73,.L192-.L73
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('debug_delay')
	.sect	'.debug_loc'
.L52:
	.word	-1,.L53,0,.L245-.L53
	.half	2
	.byte	138,0
	.word	.L245-.L53,.L211-.L53
	.half	2
	.byte	138,8
	.word	.L211-.L53,.L211-.L53
	.half	2
	.byte	138,0
	.word	0,0
.L213:
	.word	-1,.L53,0,.L211-.L53
	.half	2
	.byte	145,120
	.word	0,0
.L215:
	.word	-1,.L53,0,.L211-.L53
	.half	2
	.byte	145,124
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('debug_init')
	.sect	'.debug_loc'
.L80:
	.word	-1,.L81,0,.L286-.L81
	.half	2
	.byte	138,0
	.word	.L286-.L81,.L208-.L81
	.half	2
	.byte	138,24
	.word	.L208-.L81,.L208-.L81
	.half	2
	.byte	138,0
	.word	0,0
.L210:
	.word	-1,.L81,0,.L208-.L81
	.half	2
	.byte	145,104
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('debug_interrupr_handler')
	.sect	'.debug_loc'
.L62:
	.word	-1,.L63,0,.L180-.L63
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('debug_log_handler')
	.sect	'.debug_loc'
.L74:
	.word	-1,.L75,0,.L198-.L75
	.half	2
	.byte	138,0
	.word	0,0
.L201:
	.word	-1,.L75,0,.L50-.L75
	.half	1
	.byte	101
	.word	0,0
.L202:
	.word	-1,.L75,0,.L50-.L75
	.half	1
	.byte	85
	.word	.L283-.L75,.L50-.L75
	.half	1
	.byte	84
	.word	0,0
.L199:
	.word	-1,.L75,0,.L283-.L75
	.half	1
	.byte	84
	.word	0,0
.L200:
	.word	-1,.L75,0,.L284-.L75
	.half	1
	.byte	100
	.word	.L285-.L75,.L50-.L75
	.half	1
	.byte	102
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('debug_output')
	.sect	'.debug_loc'
.L56:
	.word	-1,.L57,0,.L247-.L57
	.half	2
	.byte	138,0
	.word	.L247-.L57,.L219-.L57
	.half	3
	.byte	138,216,4
	.word	.L219-.L57,.L219-.L57
	.half	2
	.byte	138,0
	.word	0,0
.L221:
	.word	-1,.L57,0,.L248-.L57
	.half	1
	.byte	101
	.word	.L251-.L57,.L219-.L57
	.half	1
	.byte	109
	.word	.L248-.L57,.L249-.L57
	.half	1
	.byte	100
	.word	.L256-.L57,.L257-.L57
	.half	1
	.byte	100
	.word	0,0
.L239:
	.word	-1,.L57,0,.L219-.L57
	.half	3
	.byte	145,192,125
	.word	0,0
.L224:
	.word	-1,.L57,.L251-.L57,.L219-.L57
	.half	1
	.byte	109
	.word	0,0
.L226:
	.word	-1,.L57,0,.L219-.L57
	.half	3
	.byte	145,180,123
	.word	0,0
.L228:
	.word	-1,.L57,0,.L219-.L57
	.half	3
	.byte	145,182,123
	.word	0,0
.L230:
	.word	-1,.L57,0,.L219-.L57
	.half	3
	.byte	145,184,123
	.word	0,0
.L222:
	.word	-1,.L57,0,.L249-.L57
	.half	1
	.byte	84
	.word	.L252-.L57,.L28-.L57
	.half	1
	.byte	88
	.word	.L8-.L57,.L7-.L57
	.half	1
	.byte	88
	.word	0,0
.L237:
	.word	-1,.L57,0,.L219-.L57
	.half	3
	.byte	145,192,123
	.word	0,0
.L240:
	.word	-1,.L57,0,.L219-.L57
	.half	3
	.byte	145,128,126
	.word	0,0
.L232:
	.word	-1,.L57,0,.L219-.L57
	.half	3
	.byte	145,188,123
	.word	0,0
.L234:
	.word	-1,.L57,0,.L219-.L57
	.half	3
	.byte	145,190,123
	.word	0,0
.L223:
	.word	-1,.L57,0,.L249-.L57
	.half	1
	.byte	102
	.word	.L253-.L57,.L219-.L57
	.half	1
	.byte	110
	.word	.L258-.L57,.L259-.L57
	.half	1
	.byte	100
	.word	.L260-.L57,.L261-.L57
	.half	1
	.byte	100
	.word	0,0
.L220:
	.word	-1,.L57,0,.L248-.L57
	.half	1
	.byte	100
	.word	.L250-.L57,.L219-.L57
	.half	1
	.byte	108
	.word	.L254-.L57,.L255-.L57
	.half	1
	.byte	100
	.word	.L262-.L57,.L263-.L57
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('debug_output_init')
	.sect	'.debug_loc'
.L78:
	.word	-1,.L79,0,.L206-.L79
	.half	2
	.byte	138,0
	.word	0,0
.L207:
	.word	-1,.L79,0,.L206-.L79
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('debug_output_struct_init')
	.sect	'.debug_loc'
.L76:
	.word	-1,.L77,0,.L203-.L77
	.half	2
	.byte	138,0
	.word	0,0
.L205:
	.word	-1,.L77,0,.L203-.L77
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('debug_read_ring_buffer')
	.sect	'.debug_loc'
.L184:
	.word	-1,.L61,0,.L268-.L61
	.half	1
	.byte	100
	.word	.L270-.L61,.L271-.L61
	.half	1
	.byte	101
	.word	0,0
.L60:
	.word	-1,.L61,0,.L267-.L61
	.half	2
	.byte	138,0
	.word	.L267-.L61,.L182-.L61
	.half	2
	.byte	138,8
	.word	.L182-.L61,.L182-.L61
	.half	2
	.byte	138,0
	.word	0,0
.L185:
	.word	-1,.L61,0,.L182-.L61
	.half	2
	.byte	145,120
	.word	0,.L269-.L61
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('debug_send_buffer')
	.sect	'.debug_loc'
.L188:
	.word	-1,.L59,0,.L264-.L59
	.half	1
	.byte	100
	.word	0,0
.L58:
	.word	-1,.L59,0,.L186-.L59
	.half	2
	.byte	138,0
	.word	0,0
.L189:
	.word	-1,.L59,0,.L265-.L59
	.half	1
	.byte	84
	.word	.L266-.L59,.L264-.L59
	.half	1
	.byte	85
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('debug_uart_str_output')
	.sect	'.debug_loc'
.L54:
	.word	-1,.L55,0,.L216-.L55
	.half	2
	.byte	138,0
	.word	0,0
.L218:
	.word	-1,.L55,0,.L246-.L55
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('fgetc')
	.sect	'.debug_loc'
.L176:
	.word	-1,.L67,0,.L276-.L67
	.half	1
	.byte	100
	.word	0,0
.L66:
	.word	-1,.L67,0,.L174-.L67
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('fputc')
	.sect	'.debug_loc'
.L178:
	.word	-1,.L65,0,.L272-.L65
	.half	1
	.byte	84
	.word	.L274-.L65,.L177-.L65
	.half	1
	.byte	95
	.word	.L275-.L65,.L177-.L65
	.half	1
	.byte	82
	.word	0,0
.L64:
	.word	-1,.L65,0,.L177-.L65
	.half	2
	.byte	138,0
	.word	0,0
.L179:
	.word	-1,.L65,0,.L273-.L65
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L630:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('debug_delay')
	.sect	'.debug_frame'
	.word	48
	.word	.L630,.L53,.L211-.L53
	.byte	8,18,8,19,8,20,8,21,8,22,8,23,4
	.word	(.L245-.L53)/2
	.byte	19,8,22,26,3,19,138,8,4
	.word	(.L211-.L245)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('debug_uart_str_output')
	.sect	'.debug_frame'
	.word	12
	.word	.L630,.L55,.L216-.L55
	.sdecl	'.debug_frame',debug,cluster('debug_output')
	.sect	'.debug_frame'
	.word	36
	.word	.L630,.L57,.L219-.L57
	.byte	4
	.word	(.L247-.L57)/2
	.byte	19,216,4,22,26,4,19,138,216,4,4
	.word	(.L219-.L247)/2
	.byte	19,0,8,26
	.sdecl	'.debug_frame',debug,cluster('debug_send_buffer')
	.sect	'.debug_frame'
	.word	12
	.word	.L630,.L59,.L186-.L59
	.sdecl	'.debug_frame',debug,cluster('debug_read_ring_buffer')
	.sect	'.debug_frame'
	.word	36
	.word	.L630,.L61,.L182-.L61
	.byte	4
	.word	(.L267-.L61)/2
	.byte	19,8,22,26,3,19,138,8,4
	.word	(.L182-.L267)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('debug_interrupr_handler')
	.sect	'.debug_frame'
	.word	12
	.word	.L630,.L63,.L180-.L63
	.sdecl	'.debug_frame',debug,cluster('fputc')
	.sect	'.debug_frame'
	.word	12
	.word	.L630,.L65,.L177-.L65
	.sdecl	'.debug_frame',debug,cluster('fgetc')
	.sect	'.debug_frame'
	.word	12
	.word	.L630,.L67,.L174-.L67
	.sdecl	'.debug_frame',debug,cluster('debug_assert_enable')
	.sect	'.debug_frame'
	.word	24
	.word	.L630,.L69,.L190-.L69
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('debug_assert_disable')
	.sect	'.debug_frame'
	.word	24
	.word	.L630,.L71,.L191-.L71
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('debug_assert_handler')
	.sect	'.debug_frame'
	.word	12
	.word	.L630,.L73,.L192-.L73
	.sdecl	'.debug_frame',debug,cluster('debug_log_handler')
	.sect	'.debug_frame'
	.word	12
	.word	.L630,.L75,.L198-.L75
	.sdecl	'.debug_frame',debug,cluster('debug_output_struct_init')
	.sect	'.debug_frame'
	.word	24
	.word	.L630,.L77,.L203-.L77
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('debug_output_init')
	.sect	'.debug_frame'
	.word	20
	.word	.L630,.L79,.L206-.L79
	.byte	8,19,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('debug_init')
	.sect	'.debug_frame'
	.word	36
	.word	.L630,.L81,.L208-.L81
	.byte	4
	.word	(.L286-.L81)/2
	.byte	19,24,22,26,3,19,138,24,4
	.word	(.L208-.L286)/2
	.byte	19,0,8,26,0,0
	; Module end
