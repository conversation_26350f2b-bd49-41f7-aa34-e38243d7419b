	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc37592a --dep-file=IfxMsc_PinMap.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2\\\\xf_asr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMsc_PinMap.src ../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMsc_PinMap.c"
	.compiler_name		"ctc"
	;source	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMsc_PinMap.c'

	
$TC16X
	
	.sdecl	'.rodata.IfxMsc_PinMap.IfxMsc0_EN0_P10_2_OUT',data,rom,cluster('IfxMsc0_EN0_P10_2_OUT')
	.sect	'.rodata.IfxMsc_PinMap.IfxMsc0_EN0_P10_2_OUT'
	.global	IfxMsc0_EN0_P10_2_OUT
	.align	4
IfxMsc0_EN0_P10_2_OUT:	.type	object
	.size	IfxMsc0_EN0_P10_2_OUT,20
	.word	-268425728
	.space	4
	.word	-268193792
	.byte	2
	.space	3
	.byte	160
	.space	3
	.sdecl	'.rodata.IfxMsc_PinMap.IfxMsc0_EN0_P10_3_OUT',data,rom,cluster('IfxMsc0_EN0_P10_3_OUT')
	.sect	'.rodata.IfxMsc_PinMap.IfxMsc0_EN0_P10_3_OUT'
	.global	IfxMsc0_EN0_P10_3_OUT
	.align	4
IfxMsc0_EN0_P10_3_OUT:	.type	object
	.size	IfxMsc0_EN0_P10_3_OUT,20
	.word	-268425728
	.space	4
	.word	-268193792
	.byte	3
	.space	3
	.byte	160
	.space	3
	.sdecl	'.rodata.IfxMsc_PinMap.IfxMsc0_EN0_P10_4_OUT',data,rom,cluster('IfxMsc0_EN0_P10_4_OUT')
	.sect	'.rodata.IfxMsc_PinMap.IfxMsc0_EN0_P10_4_OUT'
	.global	IfxMsc0_EN0_P10_4_OUT
	.align	4
IfxMsc0_EN0_P10_4_OUT:	.type	object
	.size	IfxMsc0_EN0_P10_4_OUT,20
	.word	-268425728
	.space	4
	.word	-268193792
	.byte	4
	.space	3
	.byte	168
	.space	3
	.sdecl	'.rodata.IfxMsc_PinMap.IfxMsc0_EN0_P11_11_OUT',data,rom,cluster('IfxMsc0_EN0_P11_11_OUT')
	.sect	'.rodata.IfxMsc_PinMap.IfxMsc0_EN0_P11_11_OUT'
	.global	IfxMsc0_EN0_P11_11_OUT
	.align	4
IfxMsc0_EN0_P11_11_OUT:	.type	object
	.size	IfxMsc0_EN0_P11_11_OUT,20
	.word	-268425728
	.space	4
	.word	-268193536
	.byte	11
	.space	3
	.byte	168
	.space	3
	.sdecl	'.rodata.IfxMsc_PinMap.IfxMsc0_EN0_P14_10_OUT',data,rom,cluster('IfxMsc0_EN0_P14_10_OUT')
	.sect	'.rodata.IfxMsc_PinMap.IfxMsc0_EN0_P14_10_OUT'
	.global	IfxMsc0_EN0_P14_10_OUT
	.align	4
IfxMsc0_EN0_P14_10_OUT:	.type	object
	.size	IfxMsc0_EN0_P14_10_OUT,20
	.word	-268425728
	.space	4
	.word	-268192768
	.byte	10
	.space	3
	.byte	152
	.space	3
	.sdecl	'.rodata.IfxMsc_PinMap.IfxMsc0_EN0_P15_5_OUT',data,rom,cluster('IfxMsc0_EN0_P15_5_OUT')
	.sect	'.rodata.IfxMsc_PinMap.IfxMsc0_EN0_P15_5_OUT'
	.global	IfxMsc0_EN0_P15_5_OUT
	.align	4
IfxMsc0_EN0_P15_5_OUT:	.type	object
	.size	IfxMsc0_EN0_P15_5_OUT,20
	.word	-268425728
	.space	4
	.word	-268192512
	.byte	5
	.space	3
	.byte	168
	.space	3
	.sdecl	'.rodata.IfxMsc_PinMap.IfxMsc0_EN1_P10_1_OUT',data,rom,cluster('IfxMsc0_EN1_P10_1_OUT')
	.sect	'.rodata.IfxMsc_PinMap.IfxMsc0_EN1_P10_1_OUT'
	.global	IfxMsc0_EN1_P10_1_OUT
	.align	4
IfxMsc0_EN1_P10_1_OUT:	.type	object
	.size	IfxMsc0_EN1_P10_1_OUT,20
	.word	-268425728
	.byte	1
	.space	3
	.word	-268193792
	.byte	1
	.space	3
	.byte	160
	.space	3
	.sdecl	'.rodata.IfxMsc_PinMap.IfxMsc0_EN1_P11_2_OUT',data,rom,cluster('IfxMsc0_EN1_P11_2_OUT')
	.sect	'.rodata.IfxMsc_PinMap.IfxMsc0_EN1_P11_2_OUT'
	.global	IfxMsc0_EN1_P11_2_OUT
	.align	4
IfxMsc0_EN1_P11_2_OUT:	.type	object
	.size	IfxMsc0_EN1_P11_2_OUT,20
	.word	-268425728
	.byte	1
	.space	3
	.word	-268193536
	.byte	2
	.space	3
	.byte	168
	.space	3
	.sdecl	'.rodata.IfxMsc_PinMap.IfxMsc0_EN1_P13_0_OUT',data,rom,cluster('IfxMsc0_EN1_P13_0_OUT')
	.sect	'.rodata.IfxMsc_PinMap.IfxMsc0_EN1_P13_0_OUT'
	.global	IfxMsc0_EN1_P13_0_OUT
	.align	4
IfxMsc0_EN1_P13_0_OUT:	.type	object
	.size	IfxMsc0_EN1_P13_0_OUT,20
	.word	-268425728
	.byte	1
	.space	3
	.word	-268193024
	.space	4
	.byte	160
	.space	3
	.sdecl	'.rodata.IfxMsc_PinMap.IfxMsc0_EN1_P14_9_OUT',data,rom,cluster('IfxMsc0_EN1_P14_9_OUT')
	.sect	'.rodata.IfxMsc_PinMap.IfxMsc0_EN1_P14_9_OUT'
	.global	IfxMsc0_EN1_P14_9_OUT
	.align	4
IfxMsc0_EN1_P14_9_OUT:	.type	object
	.size	IfxMsc0_EN1_P14_9_OUT,20
	.word	-268425728
	.byte	1
	.space	3
	.word	-268192768
	.byte	9
	.space	3
	.byte	152
	.space	3
	.sdecl	'.rodata.IfxMsc_PinMap.IfxMsc0_EN1_P15_3_OUT',data,rom,cluster('IfxMsc0_EN1_P15_3_OUT')
	.sect	'.rodata.IfxMsc_PinMap.IfxMsc0_EN1_P15_3_OUT'
	.global	IfxMsc0_EN1_P15_3_OUT
	.align	4
IfxMsc0_EN1_P15_3_OUT:	.type	object
	.size	IfxMsc0_EN1_P15_3_OUT,20
	.word	-268425728
	.byte	1
	.space	3
	.word	-268192512
	.byte	3
	.space	3
	.byte	168
	.space	3
	.sdecl	'.rodata.IfxMsc_PinMap.IfxMsc0_END2_P10_2_OUT',data,rom,cluster('IfxMsc0_END2_P10_2_OUT')
	.sect	'.rodata.IfxMsc_PinMap.IfxMsc0_END2_P10_2_OUT'
	.global	IfxMsc0_END2_P10_2_OUT
	.align	4
IfxMsc0_END2_P10_2_OUT:	.type	object
	.size	IfxMsc0_END2_P10_2_OUT,20
	.word	-268425728
	.byte	2
	.space	3
	.word	-268193792
	.byte	2
	.space	3
	.byte	176
	.space	3
	.sdecl	'.rodata.IfxMsc_PinMap.IfxMsc0_END2_P10_3_OUT',data,rom,cluster('IfxMsc0_END2_P10_3_OUT')
	.sect	'.rodata.IfxMsc_PinMap.IfxMsc0_END2_P10_3_OUT'
	.global	IfxMsc0_END2_P10_3_OUT
	.align	4
IfxMsc0_END2_P10_3_OUT:	.type	object
	.size	IfxMsc0_END2_P10_3_OUT,20
	.word	-268425728
	.byte	2
	.space	3
	.word	-268193792
	.byte	3
	.space	3
	.byte	168
	.space	3
	.sdecl	'.rodata.IfxMsc_PinMap.IfxMsc0_END2_P10_4_OUT',data,rom,cluster('IfxMsc0_END2_P10_4_OUT')
	.sect	'.rodata.IfxMsc_PinMap.IfxMsc0_END2_P10_4_OUT'
	.global	IfxMsc0_END2_P10_4_OUT
	.align	4
IfxMsc0_END2_P10_4_OUT:	.type	object
	.size	IfxMsc0_END2_P10_4_OUT,20
	.word	-268425728
	.byte	2
	.space	3
	.word	-268193792
	.byte	4
	.space	3
	.byte	176
	.space	3
	.sdecl	'.rodata.IfxMsc_PinMap.IfxMsc0_END2_P11_11_OUT',data,rom,cluster('IfxMsc0_END2_P11_11_OUT')
	.sect	'.rodata.IfxMsc_PinMap.IfxMsc0_END2_P11_11_OUT'
	.global	IfxMsc0_END2_P11_11_OUT
	.align	4
IfxMsc0_END2_P11_11_OUT:	.type	object
	.size	IfxMsc0_END2_P11_11_OUT,20
	.word	-268425728
	.byte	2
	.space	3
	.word	-268193536
	.byte	11
	.space	3
	.byte	144
	.space	3
	.sdecl	'.rodata.IfxMsc_PinMap.IfxMsc0_END2_P14_10_OUT',data,rom,cluster('IfxMsc0_END2_P14_10_OUT')
	.sect	'.rodata.IfxMsc_PinMap.IfxMsc0_END2_P14_10_OUT'
	.global	IfxMsc0_END2_P14_10_OUT
	.align	4
IfxMsc0_END2_P14_10_OUT:	.type	object
	.size	IfxMsc0_END2_P14_10_OUT,20
	.word	-268425728
	.byte	2
	.space	3
	.word	-268192768
	.byte	10
	.space	3
	.byte	144
	.space	3
	.sdecl	'.rodata.IfxMsc_PinMap.IfxMsc0_END2_P15_5_OUT',data,rom,cluster('IfxMsc0_END2_P15_5_OUT')
	.sect	'.rodata.IfxMsc_PinMap.IfxMsc0_END2_P15_5_OUT'
	.global	IfxMsc0_END2_P15_5_OUT
	.align	4
IfxMsc0_END2_P15_5_OUT:	.type	object
	.size	IfxMsc0_END2_P15_5_OUT,20
	.word	-268425728
	.byte	2
	.space	3
	.word	-268192512
	.byte	5
	.space	3
	.byte	160
	.space	3
	.sdecl	'.rodata.IfxMsc_PinMap.IfxMsc0_END3_P10_1_OUT',data,rom,cluster('IfxMsc0_END3_P10_1_OUT')
	.sect	'.rodata.IfxMsc_PinMap.IfxMsc0_END3_P10_1_OUT'
	.global	IfxMsc0_END3_P10_1_OUT
	.align	4
IfxMsc0_END3_P10_1_OUT:	.type	object
	.size	IfxMsc0_END3_P10_1_OUT,20
	.word	-268425728
	.byte	3
	.space	3
	.word	-268193792
	.byte	1
	.space	3
	.byte	176
	.space	3
	.sdecl	'.rodata.IfxMsc_PinMap.IfxMsc0_END3_P11_2_OUT',data,rom,cluster('IfxMsc0_END3_P11_2_OUT')
	.sect	'.rodata.IfxMsc_PinMap.IfxMsc0_END3_P11_2_OUT'
	.global	IfxMsc0_END3_P11_2_OUT
	.align	4
IfxMsc0_END3_P11_2_OUT:	.type	object
	.size	IfxMsc0_END3_P11_2_OUT,20
	.word	-268425728
	.byte	3
	.space	3
	.word	-268193536
	.byte	2
	.space	3
	.byte	144
	.space	3
	.sdecl	'.rodata.IfxMsc_PinMap.IfxMsc0_END3_P13_0_OUT',data,rom,cluster('IfxMsc0_END3_P13_0_OUT')
	.sect	'.rodata.IfxMsc_PinMap.IfxMsc0_END3_P13_0_OUT'
	.global	IfxMsc0_END3_P13_0_OUT
	.align	4
IfxMsc0_END3_P13_0_OUT:	.type	object
	.size	IfxMsc0_END3_P13_0_OUT,20
	.word	-268425728
	.byte	3
	.space	3
	.word	-268193024
	.space	4
	.byte	144
	.space	3
	.sdecl	'.rodata.IfxMsc_PinMap.IfxMsc0_END3_P14_9_OUT',data,rom,cluster('IfxMsc0_END3_P14_9_OUT')
	.sect	'.rodata.IfxMsc_PinMap.IfxMsc0_END3_P14_9_OUT'
	.global	IfxMsc0_END3_P14_9_OUT
	.align	4
IfxMsc0_END3_P14_9_OUT:	.type	object
	.size	IfxMsc0_END3_P14_9_OUT,20
	.word	-268425728
	.byte	3
	.space	3
	.word	-268192768
	.byte	9
	.space	3
	.byte	144
	.space	3
	.sdecl	'.rodata.IfxMsc_PinMap.IfxMsc0_END3_P15_3_OUT',data,rom,cluster('IfxMsc0_END3_P15_3_OUT')
	.sect	'.rodata.IfxMsc_PinMap.IfxMsc0_END3_P15_3_OUT'
	.global	IfxMsc0_END3_P15_3_OUT
	.align	4
IfxMsc0_END3_P15_3_OUT:	.type	object
	.size	IfxMsc0_END3_P15_3_OUT,20
	.word	-268425728
	.byte	3
	.space	3
	.word	-268192512
	.byte	3
	.space	3
	.byte	160
	.space	3
	.sdecl	'.rodata.IfxMsc_PinMap.IfxMsc1_EN0_P23_4_OUT',data,rom,cluster('IfxMsc1_EN0_P23_4_OUT')
	.sect	'.rodata.IfxMsc_PinMap.IfxMsc1_EN0_P23_4_OUT'
	.global	IfxMsc1_EN0_P23_4_OUT
	.align	4
IfxMsc1_EN0_P23_4_OUT:	.type	object
	.size	IfxMsc1_EN0_P23_4_OUT,20
	.word	-268425472
	.space	4
	.word	-268188928
	.byte	4
	.space	3
	.byte	168
	.space	3
	.sdecl	'.rodata.IfxMsc_PinMap.IfxMsc1_EN0_P32_4_OUT',data,rom,cluster('IfxMsc1_EN0_P32_4_OUT')
	.sect	'.rodata.IfxMsc_PinMap.IfxMsc1_EN0_P32_4_OUT'
	.global	IfxMsc1_EN0_P32_4_OUT
	.align	4
IfxMsc1_EN0_P32_4_OUT:	.type	object
	.size	IfxMsc1_EN0_P32_4_OUT,20
	.word	-268425472
	.space	4
	.word	-268185088
	.byte	4
	.space	3
	.byte	168
	.space	3
	.sdecl	'.rodata.IfxMsc_PinMap.IfxMsc1_EN1_P23_5_OUT',data,rom,cluster('IfxMsc1_EN1_P23_5_OUT')
	.sect	'.rodata.IfxMsc_PinMap.IfxMsc1_EN1_P23_5_OUT'
	.global	IfxMsc1_EN1_P23_5_OUT
	.align	4
IfxMsc1_EN1_P23_5_OUT:	.type	object
	.size	IfxMsc1_EN1_P23_5_OUT,20
	.word	-268425472
	.byte	1
	.space	3
	.word	-268188928
	.byte	5
	.space	3
	.byte	168
	.space	3
	.sdecl	'.rodata.IfxMsc_PinMap.IfxMsc1_END2_P23_4_OUT',data,rom,cluster('IfxMsc1_END2_P23_4_OUT')
	.sect	'.rodata.IfxMsc_PinMap.IfxMsc1_END2_P23_4_OUT'
	.global	IfxMsc1_END2_P23_4_OUT
	.align	4
IfxMsc1_END2_P23_4_OUT:	.type	object
	.size	IfxMsc1_END2_P23_4_OUT,20
	.word	-268425472
	.byte	2
	.space	3
	.word	-268188928
	.byte	4
	.space	3
	.byte	160
	.space	3
	.sdecl	'.rodata.IfxMsc_PinMap.IfxMsc1_END2_P32_4_OUT',data,rom,cluster('IfxMsc1_END2_P32_4_OUT')
	.sect	'.rodata.IfxMsc_PinMap.IfxMsc1_END2_P32_4_OUT'
	.global	IfxMsc1_END2_P32_4_OUT
	.align	4
IfxMsc1_END2_P32_4_OUT:	.type	object
	.size	IfxMsc1_END2_P32_4_OUT,20
	.word	-268425472
	.byte	2
	.space	3
	.word	-268185088
	.byte	4
	.space	3
	.byte	152
	.space	3
	.sdecl	'.rodata.IfxMsc_PinMap.IfxMsc1_END3_P23_5_OUT',data,rom,cluster('IfxMsc1_END3_P23_5_OUT')
	.sect	'.rodata.IfxMsc_PinMap.IfxMsc1_END3_P23_5_OUT'
	.global	IfxMsc1_END3_P23_5_OUT
	.align	4
IfxMsc1_END3_P23_5_OUT:	.type	object
	.size	IfxMsc1_END3_P23_5_OUT,20
	.word	-268425472
	.byte	3
	.space	3
	.word	-268188928
	.byte	5
	.space	3
	.byte	160
	.space	3
	.sdecl	'.rodata.IfxMsc_PinMap.IfxMsc0_FCLND_P13_0_OUT',data,rom,cluster('IfxMsc0_FCLND_P13_0_OUT')
	.sect	'.rodata.IfxMsc_PinMap.IfxMsc0_FCLND_P13_0_OUT'
	.global	IfxMsc0_FCLND_P13_0_OUT
	.align	4
IfxMsc0_FCLND_P13_0_OUT:	.type	object
	.size	IfxMsc0_FCLND_P13_0_OUT,16
	.word	-268425728,-268193024
	.space	4
	.byte	176
	.space	3
	.sdecl	'.rodata.IfxMsc_PinMap.IfxMsc0_FCLN_P13_0_OUT',data,rom,cluster('IfxMsc0_FCLN_P13_0_OUT')
	.sect	'.rodata.IfxMsc_PinMap.IfxMsc0_FCLN_P13_0_OUT'
	.global	IfxMsc0_FCLN_P13_0_OUT
	.align	4
IfxMsc0_FCLN_P13_0_OUT:	.type	object
	.size	IfxMsc0_FCLN_P13_0_OUT,16
	.word	-268425728,-268193024
	.space	4
	.byte	168
	.space	3
	.sdecl	'.rodata.IfxMsc_PinMap.IfxMsc1_FCLND_P22_0_OUT',data,rom,cluster('IfxMsc1_FCLND_P22_0_OUT')
	.sect	'.rodata.IfxMsc_PinMap.IfxMsc1_FCLND_P22_0_OUT'
	.global	IfxMsc1_FCLND_P22_0_OUT
	.align	4
IfxMsc1_FCLND_P22_0_OUT:	.type	object
	.size	IfxMsc1_FCLND_P22_0_OUT,16
	.word	-268425472,-268189184
	.space	4
	.byte	176
	.space	3
	.sdecl	'.rodata.IfxMsc_PinMap.IfxMsc1_FCLN_P22_0_OUT',data,rom,cluster('IfxMsc1_FCLN_P22_0_OUT')
	.sect	'.rodata.IfxMsc_PinMap.IfxMsc1_FCLN_P22_0_OUT'
	.global	IfxMsc1_FCLN_P22_0_OUT
	.align	4
IfxMsc1_FCLN_P22_0_OUT:	.type	object
	.size	IfxMsc1_FCLN_P22_0_OUT,16
	.word	-268425472,-268189184
	.space	4
	.byte	168
	.space	3
	.sdecl	'.rodata.IfxMsc_PinMap.IfxMsc0_FCLP_P11_6_OUT',data,rom,cluster('IfxMsc0_FCLP_P11_6_OUT')
	.sect	'.rodata.IfxMsc_PinMap.IfxMsc0_FCLP_P11_6_OUT'
	.global	IfxMsc0_FCLP_P11_6_OUT
	.align	4
IfxMsc0_FCLP_P11_6_OUT:	.type	object
	.size	IfxMsc0_FCLP_P11_6_OUT,16
	.word	-268425728,-268193536
	.byte	6
	.space	3
	.byte	168
	.space	3
	.sdecl	'.rodata.IfxMsc_PinMap.IfxMsc0_FCLP_P13_1_OUT',data,rom,cluster('IfxMsc0_FCLP_P13_1_OUT')
	.sect	'.rodata.IfxMsc_PinMap.IfxMsc0_FCLP_P13_1_OUT'
	.global	IfxMsc0_FCLP_P13_1_OUT
	.align	4
IfxMsc0_FCLP_P13_1_OUT:	.type	object
	.size	IfxMsc0_FCLP_P13_1_OUT,16
	.word	-268425728,-268193024
	.byte	1
	.space	3
	.byte	168
	.space	3
	.sdecl	'.rodata.IfxMsc_PinMap.IfxMsc0_FCLP_P13_2_OUT',data,rom,cluster('IfxMsc0_FCLP_P13_2_OUT')
	.sect	'.rodata.IfxMsc_PinMap.IfxMsc0_FCLP_P13_2_OUT'
	.global	IfxMsc0_FCLP_P13_2_OUT
	.align	4
IfxMsc0_FCLP_P13_2_OUT:	.type	object
	.size	IfxMsc0_FCLP_P13_2_OUT,16
	.word	-268425728,-268193024
	.byte	2
	.space	3
	.byte	160
	.space	3
	.sdecl	'.rodata.IfxMsc_PinMap.IfxMsc1_FCLP_P22_1_OUT',data,rom,cluster('IfxMsc1_FCLP_P22_1_OUT')
	.sect	'.rodata.IfxMsc_PinMap.IfxMsc1_FCLP_P22_1_OUT'
	.global	IfxMsc1_FCLP_P22_1_OUT
	.align	4
IfxMsc1_FCLP_P22_1_OUT:	.type	object
	.size	IfxMsc1_FCLP_P22_1_OUT,16
	.word	-268425472,-268189184
	.byte	1
	.space	3
	.byte	168
	.space	3
	.sdecl	'.rodata.IfxMsc_PinMap.IfxMsc0_INJ0_P00_0_IN',data,rom,cluster('IfxMsc0_INJ0_P00_0_IN')
	.sect	'.rodata.IfxMsc_PinMap.IfxMsc0_INJ0_P00_0_IN'
	.global	IfxMsc0_INJ0_P00_0_IN
	.align	4
IfxMsc0_INJ0_P00_0_IN:	.type	object
	.size	IfxMsc0_INJ0_P00_0_IN,16
	.word	-268425728,-268197888
	.space	8
	.sdecl	'.rodata.IfxMsc_PinMap.IfxMsc0_INJ1_P10_5_IN',data,rom,cluster('IfxMsc0_INJ1_P10_5_IN')
	.sect	'.rodata.IfxMsc_PinMap.IfxMsc0_INJ1_P10_5_IN'
	.global	IfxMsc0_INJ1_P10_5_IN
	.align	4
IfxMsc0_INJ1_P10_5_IN:	.type	object
	.size	IfxMsc0_INJ1_P10_5_IN,16
	.word	-268425728,-268193792
	.byte	5
	.space	3
	.byte	1
	.space	3
	.sdecl	'.rodata.IfxMsc_PinMap.IfxMsc1_INJ0_P23_3_IN',data,rom,cluster('IfxMsc1_INJ0_P23_3_IN')
	.sect	'.rodata.IfxMsc_PinMap.IfxMsc1_INJ0_P23_3_IN'
	.global	IfxMsc1_INJ0_P23_3_IN
	.align	4
IfxMsc1_INJ0_P23_3_IN:	.type	object
	.size	IfxMsc1_INJ0_P23_3_IN,16
	.word	-268425472,-268188928
	.byte	3
	.space	7
	.sdecl	'.rodata.IfxMsc_PinMap.IfxMsc1_INJ1_P33_13_IN',data,rom,cluster('IfxMsc1_INJ1_P33_13_IN')
	.sect	'.rodata.IfxMsc_PinMap.IfxMsc1_INJ1_P33_13_IN'
	.global	IfxMsc1_INJ1_P33_13_IN
	.align	4
IfxMsc1_INJ1_P33_13_IN:	.type	object
	.size	IfxMsc1_INJ1_P33_13_IN,16
	.word	-268425472,-268184832
	.byte	13
	.space	3
	.byte	1
	.space	3
	.sdecl	'.rodata.IfxMsc_PinMap.IfxMsc0_SDI0_P11_10_IN',data,rom,cluster('IfxMsc0_SDI0_P11_10_IN')
	.sect	'.rodata.IfxMsc_PinMap.IfxMsc0_SDI0_P11_10_IN'
	.global	IfxMsc0_SDI0_P11_10_IN
	.align	4
IfxMsc0_SDI0_P11_10_IN:	.type	object
	.size	IfxMsc0_SDI0_P11_10_IN,16
	.word	-268425728,-268193536
	.byte	10
	.space	7
	.sdecl	'.rodata.IfxMsc_PinMap.IfxMsc0_SDI1_P10_2_IN',data,rom,cluster('IfxMsc0_SDI1_P10_2_IN')
	.sect	'.rodata.IfxMsc_PinMap.IfxMsc0_SDI1_P10_2_IN'
	.global	IfxMsc0_SDI1_P10_2_IN
	.align	4
IfxMsc0_SDI1_P10_2_IN:	.type	object
	.size	IfxMsc0_SDI1_P10_2_IN,16
	.word	-268425728,-268193792
	.byte	2
	.space	3
	.byte	1
	.space	3
	.sdecl	'.rodata.IfxMsc_PinMap.IfxMsc0_SDI2_P14_3_IN',data,rom,cluster('IfxMsc0_SDI2_P14_3_IN')
	.sect	'.rodata.IfxMsc_PinMap.IfxMsc0_SDI2_P14_3_IN'
	.global	IfxMsc0_SDI2_P14_3_IN
	.align	4
IfxMsc0_SDI2_P14_3_IN:	.type	object
	.size	IfxMsc0_SDI2_P14_3_IN,16
	.word	-268425728,-268192768
	.byte	3
	.space	3
	.byte	2
	.space	3
	.sdecl	'.rodata.IfxMsc_PinMap.IfxMsc0_SDI3_P11_3_IN',data,rom,cluster('IfxMsc0_SDI3_P11_3_IN')
	.sect	'.rodata.IfxMsc_PinMap.IfxMsc0_SDI3_P11_3_IN'
	.global	IfxMsc0_SDI3_P11_3_IN
	.align	4
IfxMsc0_SDI3_P11_3_IN:	.type	object
	.size	IfxMsc0_SDI3_P11_3_IN,16
	.word	-268425728,-268193536
	.byte	3
	.space	3
	.byte	3
	.space	3
	.sdecl	'.rodata.IfxMsc_PinMap.IfxMsc1_SDI0_P23_1_IN',data,rom,cluster('IfxMsc1_SDI0_P23_1_IN')
	.sect	'.rodata.IfxMsc_PinMap.IfxMsc1_SDI0_P23_1_IN'
	.global	IfxMsc1_SDI0_P23_1_IN
	.align	4
IfxMsc1_SDI0_P23_1_IN:	.type	object
	.size	IfxMsc1_SDI0_P23_1_IN,16
	.word	-268425472,-268188928
	.byte	1
	.space	7
	.sdecl	'.rodata.IfxMsc_PinMap.IfxMsc1_SDI1_P02_3_IN',data,rom,cluster('IfxMsc1_SDI1_P02_3_IN')
	.sect	'.rodata.IfxMsc_PinMap.IfxMsc1_SDI1_P02_3_IN'
	.global	IfxMsc1_SDI1_P02_3_IN
	.align	4
IfxMsc1_SDI1_P02_3_IN:	.type	object
	.size	IfxMsc1_SDI1_P02_3_IN,16
	.word	-268425472,-268197376
	.byte	3
	.space	3
	.byte	1
	.space	3
	.sdecl	'.rodata.IfxMsc_PinMap.IfxMsc1_SDI2_P32_4_IN',data,rom,cluster('IfxMsc1_SDI2_P32_4_IN')
	.sect	'.rodata.IfxMsc_PinMap.IfxMsc1_SDI2_P32_4_IN'
	.global	IfxMsc1_SDI2_P32_4_IN
	.align	4
IfxMsc1_SDI2_P32_4_IN:	.type	object
	.size	IfxMsc1_SDI2_P32_4_IN,16
	.word	-268425472,-268185088
	.byte	4
	.space	3
	.byte	2
	.space	3
	.sdecl	'.rodata.IfxMsc_PinMap.IfxMsc0_SOND_P13_2_OUT',data,rom,cluster('IfxMsc0_SOND_P13_2_OUT')
	.sect	'.rodata.IfxMsc_PinMap.IfxMsc0_SOND_P13_2_OUT'
	.global	IfxMsc0_SOND_P13_2_OUT
	.align	4
IfxMsc0_SOND_P13_2_OUT:	.type	object
	.size	IfxMsc0_SOND_P13_2_OUT,16
	.word	-268425728,-268193024
	.byte	2
	.space	3
	.byte	184
	.space	3
	.sdecl	'.rodata.IfxMsc_PinMap.IfxMsc0_SON_P13_2_OUT',data,rom,cluster('IfxMsc0_SON_P13_2_OUT')
	.sect	'.rodata.IfxMsc_PinMap.IfxMsc0_SON_P13_2_OUT'
	.global	IfxMsc0_SON_P13_2_OUT
	.align	4
IfxMsc0_SON_P13_2_OUT:	.type	object
	.size	IfxMsc0_SON_P13_2_OUT,16
	.word	-268425728,-268193024
	.byte	2
	.space	3
	.byte	168
	.space	3
	.sdecl	'.rodata.IfxMsc_PinMap.IfxMsc1_SOND_P22_2_OUT',data,rom,cluster('IfxMsc1_SOND_P22_2_OUT')
	.sect	'.rodata.IfxMsc_PinMap.IfxMsc1_SOND_P22_2_OUT'
	.global	IfxMsc1_SOND_P22_2_OUT
	.align	4
IfxMsc1_SOND_P22_2_OUT:	.type	object
	.size	IfxMsc1_SOND_P22_2_OUT,16
	.word	-268425472,-268189184
	.byte	2
	.space	3
	.byte	176
	.space	3
	.sdecl	'.rodata.IfxMsc_PinMap.IfxMsc1_SON_P22_2_OUT',data,rom,cluster('IfxMsc1_SON_P22_2_OUT')
	.sect	'.rodata.IfxMsc_PinMap.IfxMsc1_SON_P22_2_OUT'
	.global	IfxMsc1_SON_P22_2_OUT
	.align	4
IfxMsc1_SON_P22_2_OUT:	.type	object
	.size	IfxMsc1_SON_P22_2_OUT,16
	.word	-268425472,-268189184
	.byte	2
	.space	3
	.byte	168
	.space	3
	.sdecl	'.rodata.IfxMsc_PinMap.IfxMsc0_SOP_P11_9_OUT',data,rom,cluster('IfxMsc0_SOP_P11_9_OUT')
	.sect	'.rodata.IfxMsc_PinMap.IfxMsc0_SOP_P11_9_OUT'
	.global	IfxMsc0_SOP_P11_9_OUT
	.align	4
IfxMsc0_SOP_P11_9_OUT:	.type	object
	.size	IfxMsc0_SOP_P11_9_OUT,16
	.word	-268425728,-268193536
	.byte	9
	.space	3
	.byte	168
	.space	3
	.sdecl	'.rodata.IfxMsc_PinMap.IfxMsc0_SOP_P13_3_OUT',data,rom,cluster('IfxMsc0_SOP_P13_3_OUT')
	.sect	'.rodata.IfxMsc_PinMap.IfxMsc0_SOP_P13_3_OUT'
	.global	IfxMsc0_SOP_P13_3_OUT
	.align	4
IfxMsc0_SOP_P13_3_OUT:	.type	object
	.size	IfxMsc0_SOP_P13_3_OUT,16
	.word	-268425728,-268193024
	.byte	3
	.space	3
	.byte	168
	.space	3
	.sdecl	'.rodata.IfxMsc_PinMap.IfxMsc1_SOP_P22_3_OUT',data,rom,cluster('IfxMsc1_SOP_P22_3_OUT')
	.sect	'.rodata.IfxMsc_PinMap.IfxMsc1_SOP_P22_3_OUT'
	.global	IfxMsc1_SOP_P22_3_OUT
	.align	4
IfxMsc1_SOP_P22_3_OUT:	.type	object
	.size	IfxMsc1_SOP_P22_3_OUT,16
	.word	-268425472,-268189184
	.byte	3
	.space	3
	.byte	168
	.space	3
	.sdecl	'.data.IfxMsc_PinMap.IfxMsc_En_Out_pinTable',data,cluster('IfxMsc_En_Out_pinTable')
	.sect	'.data.IfxMsc_PinMap.IfxMsc_En_Out_pinTable'
	.global	IfxMsc_En_Out_pinTable
	.align	4
IfxMsc_En_Out_pinTable:	.type	object
	.size	IfxMsc_En_Out_pinTable,192
	.word	IfxMsc0_EN0_P10_2_OUT,IfxMsc0_EN0_P10_3_OUT,IfxMsc0_EN0_P10_4_OUT,IfxMsc0_EN0_P11_11_OUT,IfxMsc0_EN0_P14_10_OUT,IfxMsc0_EN0_P15_5_OUT,IfxMsc0_EN1_P10_1_OUT,IfxMsc0_EN1_P11_2_OUT
	.word	IfxMsc0_EN1_P13_0_OUT,IfxMsc0_EN1_P14_9_OUT,IfxMsc0_EN1_P15_3_OUT
	.space	4
	.word	IfxMsc0_END2_P10_2_OUT,IfxMsc0_END2_P10_3_OUT,IfxMsc0_END2_P10_4_OUT,IfxMsc0_END2_P11_11_OUT,IfxMsc0_END2_P14_10_OUT,IfxMsc0_END2_P15_5_OUT,IfxMsc0_END3_P10_1_OUT,IfxMsc0_END3_P11_2_OUT
	.word	IfxMsc0_END3_P13_0_OUT,IfxMsc0_END3_P14_9_OUT,IfxMsc0_END3_P15_3_OUT
	.space	4
	.word	IfxMsc1_EN0_P23_4_OUT,IfxMsc1_EN0_P32_4_OUT
	.space	16
	.word	IfxMsc1_EN1_P23_5_OUT
	.space	20
	.word	IfxMsc1_END2_P23_4_OUT,IfxMsc1_END2_P32_4_OUT
	.space	16
	.word	IfxMsc1_END3_P23_5_OUT
	.space	20
	.sdecl	'.data.IfxMsc_PinMap.IfxMsc_Fcln_Out_pinTable',data,cluster('IfxMsc_Fcln_Out_pinTable')
	.sect	'.data.IfxMsc_PinMap.IfxMsc_Fcln_Out_pinTable'
	.global	IfxMsc_Fcln_Out_pinTable
	.align	4
IfxMsc_Fcln_Out_pinTable:	.type	object
	.size	IfxMsc_Fcln_Out_pinTable,16
	.word	IfxMsc0_FCLN_P13_0_OUT,IfxMsc0_FCLND_P13_0_OUT,IfxMsc1_FCLN_P22_0_OUT,IfxMsc1_FCLND_P22_0_OUT
	.sdecl	'.data.IfxMsc_PinMap.IfxMsc_Fclp_Out_pinTable',data,cluster('IfxMsc_Fclp_Out_pinTable')
	.sect	'.data.IfxMsc_PinMap.IfxMsc_Fclp_Out_pinTable'
	.global	IfxMsc_Fclp_Out_pinTable
	.align	4
IfxMsc_Fclp_Out_pinTable:	.type	object
	.size	IfxMsc_Fclp_Out_pinTable,24
	.word	IfxMsc0_FCLP_P11_6_OUT,IfxMsc0_FCLP_P13_1_OUT,IfxMsc0_FCLP_P13_2_OUT,IfxMsc1_FCLP_P22_1_OUT
	.space	8
	.sdecl	'.data.IfxMsc_PinMap.IfxMsc_Inj_In_pinTable',data,cluster('IfxMsc_Inj_In_pinTable')
	.sect	'.data.IfxMsc_PinMap.IfxMsc_Inj_In_pinTable'
	.global	IfxMsc_Inj_In_pinTable
	.align	4
IfxMsc_Inj_In_pinTable:	.type	object
	.size	IfxMsc_Inj_In_pinTable,16
	.word	IfxMsc0_INJ0_P00_0_IN,IfxMsc0_INJ1_P10_5_IN,IfxMsc1_INJ0_P23_3_IN,IfxMsc1_INJ1_P33_13_IN
	.sdecl	'.data.IfxMsc_PinMap.IfxMsc_Sdi_In_pinTable',data,cluster('IfxMsc_Sdi_In_pinTable')
	.sect	'.data.IfxMsc_PinMap.IfxMsc_Sdi_In_pinTable'
	.global	IfxMsc_Sdi_In_pinTable
	.align	4
IfxMsc_Sdi_In_pinTable:	.type	object
	.size	IfxMsc_Sdi_In_pinTable,32
	.word	IfxMsc0_SDI0_P11_10_IN,IfxMsc0_SDI1_P10_2_IN,IfxMsc0_SDI2_P14_3_IN,IfxMsc0_SDI3_P11_3_IN
	.word	IfxMsc1_SDI0_P23_1_IN,IfxMsc1_SDI1_P02_3_IN,IfxMsc1_SDI2_P32_4_IN
	.space	4
	.sdecl	'.data.IfxMsc_PinMap.IfxMsc_Son_Out_pinTable',data,cluster('IfxMsc_Son_Out_pinTable')
	.sect	'.data.IfxMsc_PinMap.IfxMsc_Son_Out_pinTable'
	.global	IfxMsc_Son_Out_pinTable
	.align	4
IfxMsc_Son_Out_pinTable:	.type	object
	.size	IfxMsc_Son_Out_pinTable,16
	.word	IfxMsc0_SON_P13_2_OUT,IfxMsc0_SOND_P13_2_OUT,IfxMsc1_SON_P22_2_OUT,IfxMsc1_SOND_P22_2_OUT
	.sdecl	'.data.IfxMsc_PinMap.IfxMsc_Sop_Out_pinTable',data,cluster('IfxMsc_Sop_Out_pinTable')
	.sect	'.data.IfxMsc_PinMap.IfxMsc_Sop_Out_pinTable'
	.global	IfxMsc_Sop_Out_pinTable
	.align	4
IfxMsc_Sop_Out_pinTable:	.type	object
	.size	IfxMsc_Sop_Out_pinTable,16
	.word	IfxMsc0_SOP_P11_9_OUT,IfxMsc0_SOP_P13_3_OUT,IfxMsc1_SOP_P22_3_OUT
	.space	4
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L3:
	.word	46886
	.half	3
	.word	.L4
	.byte	4
.L2:
	.byte	1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMsc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L5
	.byte	2,1,1,3
	.word	237
	.byte	4
	.byte	'Ifx__jump_and_link',0,3,1,61,17,1,1,5
	.byte	'fun',0,1,61,43
	.word	240
	.byte	6,0,7
	.byte	'__fract',0,4,128,1,7
	.byte	'float',0,4,4,8
	.byte	'Ifx__float_to_fract',0,3,1,152,2,18
	.word	285
	.byte	1,1,5
	.byte	'a',0,1,152,2,44
	.word	297
	.byte	6,0,7
	.byte	'unsigned long long int',0,8,7,9
	.byte	'void',0,3
	.word	377
	.byte	8
	.byte	'__ld64',0,3,2,135,1,19
	.word	351
	.byte	1,1,5
	.byte	'addr',0,2,135,1,32
	.word	383
	.byte	6,0,4
	.byte	'__st64',0,3,2,143,1,17,1,1,5
	.byte	'addr',0,2,143,1,30
	.word	383
	.byte	5
	.byte	'value',0,2,143,1,43
	.word	351
	.byte	6,0,7
	.byte	'unsigned int',0,4,7,7
	.byte	'int',0,4,5,7
	.byte	'unsigned char',0,1,8,7
	.byte	'unsigned short int',0,2,7,10
	.byte	'_Ifx_P_OUT_Bits',0,4,143,3,16,4,11
	.byte	'P0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	492
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	492
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	492
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	16,0,2,35,2,0,12,4,181,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	531
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMR_Bits',0,4,169,2,16,4,11
	.byte	'PS0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	492
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	492
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	492
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'PCL0',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	492
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	492
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	492
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	492
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	492
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	492
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	492
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	492
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	492
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	492
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	492
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	492
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	492
	.byte	1,0,2,35,3,0,12,4,133,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	847
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ID_Bits',0,4,110,16,4,11
	.byte	'MODREV',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	509
	.byte	16,0,2,35,2,0,12,4,148,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1418
	.byte	4,2,35,0,0,14,4
	.word	492
	.byte	15,3,0,10
	.byte	'_Ifx_P_IOCR0_Bits',0,4,140,1,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	3,5,2,35,0,11
	.byte	'PC0',0,1
	.word	492
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	492
	.byte	3,5,2,35,1,11
	.byte	'PC1',0,1
	.word	492
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	492
	.byte	3,5,2,35,2,11
	.byte	'PC2',0,1
	.word	492
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	3,5,2,35,3,11
	.byte	'PC3',0,1
	.word	492
	.byte	5,0,2,35,3,0,12,4,164,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1546
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR4_Bits',0,4,166,1,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	3,5,2,35,0,11
	.byte	'PC4',0,1
	.word	492
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	492
	.byte	3,5,2,35,1,11
	.byte	'PC5',0,1
	.word	492
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	492
	.byte	3,5,2,35,2,11
	.byte	'PC6',0,1
	.word	492
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	3,5,2,35,3,11
	.byte	'PC7',0,1
	.word	492
	.byte	5,0,2,35,3,0,12,4,180,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1761
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR8_Bits',0,4,179,1,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	3,5,2,35,0,11
	.byte	'PC8',0,1
	.word	492
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	492
	.byte	3,5,2,35,1,11
	.byte	'PC9',0,1
	.word	492
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	492
	.byte	3,5,2,35,2,11
	.byte	'PC10',0,1
	.word	492
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	3,5,2,35,3,11
	.byte	'PC11',0,1
	.word	492
	.byte	5,0,2,35,3,0,12,4,188,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1976
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR12_Bits',0,4,153,1,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	3,5,2,35,0,11
	.byte	'PC12',0,1
	.word	492
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	492
	.byte	3,5,2,35,1,11
	.byte	'PC13',0,1
	.word	492
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	492
	.byte	3,5,2,35,2,11
	.byte	'PC14',0,1
	.word	492
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	3,5,2,35,3,11
	.byte	'PC15',0,1
	.word	492
	.byte	5,0,2,35,3,0,12,4,172,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2193
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IN_Bits',0,4,118,16,4,11
	.byte	'P0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	492
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	492
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	492
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	16,0,2,35,2,0,12,4,156,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2413
	.byte	4,2,35,0,0,14,24
	.word	492
	.byte	15,23,0,10
	.byte	'_Ifx_P_PDR0_Bits',0,4,205,3,16,4,11
	.byte	'PD0',0,1
	.word	492
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	492
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'PD2',0,1
	.word	492
	.byte	3,5,2,35,1,11
	.byte	'PL2',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'PD3',0,1
	.word	492
	.byte	3,1,2,35,1,11
	.byte	'PL3',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'PD4',0,1
	.word	492
	.byte	3,5,2,35,2,11
	.byte	'PL4',0,1
	.word	492
	.byte	1,4,2,35,2,11
	.byte	'PD5',0,1
	.word	492
	.byte	3,1,2,35,2,11
	.byte	'PL5',0,1
	.word	492
	.byte	1,0,2,35,2,11
	.byte	'PD6',0,1
	.word	492
	.byte	3,5,2,35,3,11
	.byte	'PL6',0,1
	.word	492
	.byte	1,4,2,35,3,11
	.byte	'PD7',0,1
	.word	492
	.byte	3,1,2,35,3,11
	.byte	'PL7',0,1
	.word	492
	.byte	1,0,2,35,3,0,12,4,205,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2736
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PDR1_Bits',0,4,226,3,16,4,11
	.byte	'PD8',0,1
	.word	492
	.byte	3,5,2,35,0,11
	.byte	'PL8',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'PD9',0,1
	.word	492
	.byte	3,1,2,35,0,11
	.byte	'PL9',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'PD10',0,1
	.word	492
	.byte	3,5,2,35,1,11
	.byte	'PL10',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'PD11',0,1
	.word	492
	.byte	3,1,2,35,1,11
	.byte	'PL11',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'PD12',0,1
	.word	492
	.byte	3,5,2,35,2,11
	.byte	'PL12',0,1
	.word	492
	.byte	1,4,2,35,2,11
	.byte	'PD13',0,1
	.word	492
	.byte	3,1,2,35,2,11
	.byte	'PL13',0,1
	.word	492
	.byte	1,0,2,35,2,11
	.byte	'PD14',0,1
	.word	492
	.byte	3,5,2,35,3,11
	.byte	'PL14',0,1
	.word	492
	.byte	1,4,2,35,3,11
	.byte	'PD15',0,1
	.word	492
	.byte	3,1,2,35,3,11
	.byte	'PL15',0,1
	.word	492
	.byte	1,0,2,35,3,0,12,4,213,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3040
	.byte	4,2,35,0,0,14,8
	.word	492
	.byte	15,7,0,10
	.byte	'_Ifx_P_ESR_Bits',0,4,88,16,4,11
	.byte	'EN0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	492
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	492
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	492
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	16,0,2,35,2,0,12,4,140,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3365
	.byte	4,2,35,0,0,14,12
	.word	492
	.byte	15,11,0,10
	.byte	'_Ifx_P_PDISC_Bits',0,4,183,3,16,4,11
	.byte	'PDIS0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'PDIS2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'PDIS3',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'PDIS4',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'PDIS5',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'PDIS6',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'PDIS7',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'PDIS8',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'PDIS9',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'PDIS10',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'PDIS11',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'PDIS12',0,1
	.word	492
	.byte	1,3,2,35,1,11
	.byte	'PDIS13',0,1
	.word	492
	.byte	1,2,2,35,1,11
	.byte	'PDIS14',0,1
	.word	492
	.byte	1,1,2,35,1,11
	.byte	'PDIS15',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	16,0,2,35,2,0,12,4,197,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3705
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PCSR_Bits',0,4,165,3,16,4,11
	.byte	'SEL0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'SEL1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'SEL2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'SEL3',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'SEL4',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'SEL5',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'SEL6',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'SEL7',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	492
	.byte	2,6,2,35,1,11
	.byte	'SEL10',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'SEL11',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	469
	.byte	19,1,2,35,0,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,12,4,189,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4071
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR0_Bits',0,4,206,2,16,4,11
	.byte	'PS0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	469
	.byte	28,0,2,35,0,0,12,4,149,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4357
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR4_Bits',0,4,227,2,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	4,4,2,35,0,11
	.byte	'PS4',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	469
	.byte	24,0,2,35,0,0,12,4,165,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4504
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR8_Bits',0,4,238,2,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'PS8',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	469
	.byte	20,0,2,35,0,0,12,4,173,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4673
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR12_Bits',0,4,216,2,16,4,11
	.byte	'reserved_0',0,2
	.word	509
	.byte	12,4,2,35,0,11
	.byte	'PS12',0,1
	.word	492
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	492
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	492
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	16,0,2,35,2,0,12,4,157,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4845
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR0_Bits',0,4,232,1,16,4,11
	.byte	'reserved_0',0,2
	.word	509
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	492
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	492
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	509
	.byte	12,0,2,35,2,0,12,4,229,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5020
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR4_Bits',0,4,253,1,16,4,11
	.byte	'reserved_0',0,4
	.word	469
	.byte	20,12,2,35,0,11
	.byte	'PCL4',0,1
	.word	492
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	492
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	492
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	492
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	8,0,2,35,3,0,12,4,245,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5194
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR8_Bits',0,4,136,2,16,4,11
	.byte	'reserved_0',0,4
	.word	469
	.byte	24,8,2,35,0,11
	.byte	'PCL8',0,1
	.word	492
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	492
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	492
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	492
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	492
	.byte	4,0,2,35,3,0,12,4,253,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5368
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR12_Bits',0,4,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	469
	.byte	28,4,2,35,0,11
	.byte	'PCL12',0,1
	.word	492
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	492
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	492
	.byte	1,0,2,35,3,0,12,4,237,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5544
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR_Bits',0,4,249,2,16,4,11
	.byte	'PS0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	492
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	492
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	492
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	16,0,2,35,2,0,12,4,141,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5700
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR_Bits',0,4,147,2,16,4,11
	.byte	'reserved_0',0,2
	.word	509
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	492
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	492
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	492
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	492
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	492
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	492
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	492
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	492
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	492
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	492
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	492
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	492
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	492
	.byte	1,0,2,35,3,0,12,4,221,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6033
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR0_Bits',0,4,192,1,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	469
	.byte	30,0,2,35,0,0,12,4,196,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6381
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_Bits',0,4,200,1,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	469
	.byte	30,0,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_P21_Bits',0,4,208,1,16,4,11
	.byte	'RDIS_CTRL',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'RX_DIS',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'TERM',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'LRXTERM',0,1
	.word	492
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	469
	.byte	24,0,2,35,0,0,12,4,204,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6505
	.byte	4,2,35,0,13
	.byte	'B_P21',0
	.word	6589
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR2_Bits',0,4,218,1,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'LVDSR',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'LVDSRL',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	492
	.byte	2,4,2,35,1,11
	.byte	'TDIS_CTRL',0,1
	.word	492
	.byte	1,3,2,35,1,11
	.byte	'TX_DIS',0,1
	.word	492
	.byte	1,2,2,35,1,11
	.byte	'TX_PD',0,1
	.word	492
	.byte	1,1,2,35,1,11
	.byte	'TX_PWDPD',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	16,0,2,35,2,0,12,4,213,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6769
	.byte	4,2,35,0,0,14,76
	.word	492
	.byte	15,75,0,10
	.byte	'_Ifx_P_ACCEN1_Bits',0,4,82,16,4,11
	.byte	'reserved_0',0,4
	.word	469
	.byte	32,0,2,35,0,0,12,4,132,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7022
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ACCEN0_Bits',0,4,45,16,4,11
	.byte	'EN0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	492
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	492
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	492
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	492
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	492
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	492
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	492
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	492
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	492
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	492
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	492
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	492
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	492
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	492
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	492
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	492
	.byte	1,0,2,35,3,0,12,4,252,3,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7109
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P',0,4,229,5,25,128,2,13
	.byte	'OUT',0
	.word	807
	.byte	4,2,35,0,13
	.byte	'OMR',0
	.word	1378
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	1497
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	1537
	.byte	4,2,35,12,13
	.byte	'IOCR0',0
	.word	1721
	.byte	4,2,35,16,13
	.byte	'IOCR4',0
	.word	1936
	.byte	4,2,35,20,13
	.byte	'IOCR8',0
	.word	2153
	.byte	4,2,35,24,13
	.byte	'IOCR12',0
	.word	2373
	.byte	4,2,35,28,13
	.byte	'reserved_20',0
	.word	1537
	.byte	4,2,35,32,13
	.byte	'IN',0
	.word	2687
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	2727
	.byte	24,2,35,40,13
	.byte	'PDR0',0
	.word	3000
	.byte	4,2,35,64,13
	.byte	'PDR1',0
	.word	3316
	.byte	4,2,35,68,13
	.byte	'reserved_48',0
	.word	3356
	.byte	8,2,35,72,13
	.byte	'ESR',0
	.word	3656
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	3696
	.byte	12,2,35,84,13
	.byte	'PDISC',0
	.word	4031
	.byte	4,2,35,96,13
	.byte	'PCSR',0
	.word	4317
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	3356
	.byte	8,2,35,104,13
	.byte	'OMSR0',0
	.word	4464
	.byte	4,2,35,112,13
	.byte	'OMSR4',0
	.word	4633
	.byte	4,2,35,116,13
	.byte	'OMSR8',0
	.word	4805
	.byte	4,2,35,120,13
	.byte	'OMSR12',0
	.word	4980
	.byte	4,2,35,124,13
	.byte	'OMCR0',0
	.word	5154
	.byte	4,3,35,128,1,13
	.byte	'OMCR4',0
	.word	5328
	.byte	4,3,35,132,1,13
	.byte	'OMCR8',0
	.word	5504
	.byte	4,3,35,136,1,13
	.byte	'OMCR12',0
	.word	5660
	.byte	4,3,35,140,1,13
	.byte	'OMSR',0
	.word	5993
	.byte	4,3,35,144,1,13
	.byte	'OMCR',0
	.word	6341
	.byte	4,3,35,148,1,13
	.byte	'reserved_98',0
	.word	3356
	.byte	8,3,35,152,1,13
	.byte	'LPCR0',0
	.word	6465
	.byte	4,3,35,160,1,13
	.byte	'LPCR1',0
	.word	6714
	.byte	4,3,35,164,1,13
	.byte	'LPCR2',0
	.word	6973
	.byte	4,3,35,168,1,13
	.byte	'reserved_A4',0
	.word	7013
	.byte	76,3,35,172,1,13
	.byte	'ACCEN1',0
	.word	7069
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	7636
	.byte	4,3,35,252,1,0,16
	.word	7676
	.byte	3
	.word	8279
	.byte	17,3,172,1,9,4,18
	.byte	'IfxPort_State_notChanged',0,0,18
	.byte	'IfxPort_State_high',0,1,18
	.byte	'IfxPort_State_low',0,128,128,4,18
	.byte	'IfxPort_State_toggled',0,129,128,4,0,4
	.byte	'IfxPort_setPinState',0,3,3,208,4,17,1,1,5
	.byte	'port',0,3,208,4,44
	.word	8284
	.byte	5
	.byte	'pinIndex',0,3,208,4,56
	.word	492
	.byte	5
	.byte	'action',0,3,208,4,80
	.word	8289
	.byte	6,0,19
	.word	245
	.byte	20
	.word	271
	.byte	6,0,19
	.word	306
	.byte	20
	.word	338
	.byte	6,0,19
	.word	388
	.byte	20
	.word	407
	.byte	6,0,19
	.word	423
	.byte	20
	.word	438
	.byte	20
	.word	452
	.byte	6,0,19
	.word	8392
	.byte	20
	.word	8420
	.byte	20
	.word	8434
	.byte	20
	.word	8452
	.byte	6,0,7
	.byte	'short int',0,2,5,21
	.byte	'__wchar_t',0,5,1,1
	.word	8545
	.byte	21
	.byte	'__size_t',0,5,1,1
	.word	469
	.byte	21
	.byte	'__ptrdiff_t',0,5,1,1
	.word	485
	.byte	22,1,3
	.word	8613
	.byte	21
	.byte	'__codeptr',0,5,1,1
	.word	8615
	.byte	10
	.byte	'_Ifx_MSC_ABC_Bits',0,6,45,16,4,11
	.byte	'LOW',0,1
	.word	492
	.byte	4,4,2,35,0,11
	.byte	'HIGH',0,1
	.word	492
	.byte	4,0,2,35,0,11
	.byte	'OIP',0,1
	.word	492
	.byte	2,6,2,35,1,11
	.byte	'OASR',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'OVF',0,1
	.word	492
	.byte	1,3,2,35,1,11
	.byte	'OFM',0,1
	.word	492
	.byte	2,1,2,35,1,11
	.byte	'OIE',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'NDA',0,1
	.word	492
	.byte	3,5,2,35,2,11
	.byte	'UIP',0,1
	.word	492
	.byte	2,3,2,35,2,11
	.byte	'UASR',0,1
	.word	492
	.byte	1,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	492
	.byte	1,1,2,35,2,11
	.byte	'UNF',0,1
	.word	492
	.byte	1,0,2,35,2,11
	.byte	'UFM',0,1
	.word	492
	.byte	2,6,2,35,3,11
	.byte	'UIE',0,1
	.word	492
	.byte	1,5,2,35,3,11
	.byte	'CLKSEL',0,1
	.word	492
	.byte	3,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'ABB',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_MSC_ABC_Bits',0,6,65,3
	.word	8638
	.byte	10
	.byte	'_Ifx_MSC_ACCEN0_Bits',0,6,68,16,4,11
	.byte	'EN0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	492
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	492
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	492
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	492
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	492
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	492
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	492
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	492
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	492
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	492
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	492
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	492
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	492
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	492
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	492
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_MSC_ACCEN0_Bits',0,6,102,3
	.word	8987
	.byte	10
	.byte	'_Ifx_MSC_ACCEN1_Bits',0,6,105,16,4,11
	.byte	'reserved_0',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_MSC_ACCEN1_Bits',0,6,108,3
	.word	9544
	.byte	10
	.byte	'_Ifx_MSC_CLC_Bits',0,6,111,16,4,11
	.byte	'DISR',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'DISS',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'EDIS',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	469
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_MSC_CLC_Bits',0,6,118,3
	.word	9621
	.byte	10
	.byte	'_Ifx_MSC_DC_Bits',0,6,121,16,4,11
	.byte	'DCL',0,2
	.word	509
	.byte	16,0,2,35,0,11
	.byte	'DCH',0,2
	.word	509
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_MSC_DC_Bits',0,6,125,3
	.word	9762
	.byte	10
	.byte	'_Ifx_MSC_DD_Bits',0,6,128,1,16,4,11
	.byte	'DDL',0,2
	.word	509
	.byte	16,0,2,35,0,11
	.byte	'DDH',0,2
	.word	509
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_MSC_DD_Bits',0,6,132,1,3
	.word	9839
	.byte	10
	.byte	'_Ifx_MSC_DDE_Bits',0,6,135,1,16,4,11
	.byte	'DDLE',0,2
	.word	509
	.byte	16,0,2,35,0,11
	.byte	'DDHE',0,2
	.word	509
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_MSC_DDE_Bits',0,6,139,1,3
	.word	9918
	.byte	10
	.byte	'_Ifx_MSC_DDM_Bits',0,6,142,1,16,4,11
	.byte	'DDLM',0,2
	.word	509
	.byte	16,0,2,35,0,11
	.byte	'DDHM',0,2
	.word	509
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_MSC_DDM_Bits',0,6,146,1,3
	.word	10001
	.byte	10
	.byte	'_Ifx_MSC_DSC_Bits',0,6,149,1,16,4,11
	.byte	'TM',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'CP',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'DP',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'NDBL',0,1
	.word	492
	.byte	5,0,2,35,0,11
	.byte	'NDBH',0,1
	.word	492
	.byte	5,3,2,35,1,11
	.byte	'ENSELL',0,1
	.word	492
	.byte	1,2,2,35,1,11
	.byte	'ENSELH',0,1
	.word	492
	.byte	1,1,2,35,1,11
	.byte	'DSDIS',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'NBC',0,1
	.word	492
	.byte	6,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	492
	.byte	2,0,2,35,2,11
	.byte	'PPD',0,1
	.word	492
	.byte	5,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	492
	.byte	3,0,2,35,3,0,21
	.byte	'Ifx_MSC_DSC_Bits',0,6,163,1,3
	.word	10084
	.byte	10
	.byte	'_Ifx_MSC_DSCE_Bits',0,6,166,1,16,4,11
	.byte	'NDBHE',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'NDBLE',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	509
	.byte	12,2,2,35,0,11
	.byte	'EXEN',0,1
	.word	492
	.byte	1,1,2,35,1,11
	.byte	'CCF',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'INJENP0',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'INJPOSP0',0,1
	.word	492
	.byte	6,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	492
	.byte	1,0,2,35,2,11
	.byte	'INJENP1',0,1
	.word	492
	.byte	1,7,2,35,3,11
	.byte	'INJPOSP1',0,1
	.word	492
	.byte	6,1,2,35,3,11
	.byte	'CDCM',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_MSC_DSCE_Bits',0,6,179,1,3
	.word	10338
	.byte	10
	.byte	'_Ifx_MSC_DSDSH_Bits',0,6,182,1,16,4,11
	.byte	'SH0',0,1
	.word	492
	.byte	2,6,2,35,0,11
	.byte	'SH1',0,1
	.word	492
	.byte	2,4,2,35,0,11
	.byte	'SH2',0,1
	.word	492
	.byte	2,2,2,35,0,11
	.byte	'SH3',0,1
	.word	492
	.byte	2,0,2,35,0,11
	.byte	'SH4',0,1
	.word	492
	.byte	2,6,2,35,1,11
	.byte	'SH5',0,1
	.word	492
	.byte	2,4,2,35,1,11
	.byte	'SH6',0,1
	.word	492
	.byte	2,2,2,35,1,11
	.byte	'SH7',0,1
	.word	492
	.byte	2,0,2,35,1,11
	.byte	'SH8',0,1
	.word	492
	.byte	2,6,2,35,2,11
	.byte	'SH9',0,1
	.word	492
	.byte	2,4,2,35,2,11
	.byte	'SH10',0,1
	.word	492
	.byte	2,2,2,35,2,11
	.byte	'SH11',0,1
	.word	492
	.byte	2,0,2,35,2,11
	.byte	'SH12',0,1
	.word	492
	.byte	2,6,2,35,3,11
	.byte	'SH13',0,1
	.word	492
	.byte	2,4,2,35,3,11
	.byte	'SH14',0,1
	.word	492
	.byte	2,2,2,35,3,11
	.byte	'SH15',0,1
	.word	492
	.byte	2,0,2,35,3,0,21
	.byte	'Ifx_MSC_DSDSH_Bits',0,6,200,1,3
	.word	10595
	.byte	10
	.byte	'_Ifx_MSC_DSDSHE_Bits',0,6,203,1,16,4,11
	.byte	'SH16',0,1
	.word	492
	.byte	2,6,2,35,0,11
	.byte	'SH17',0,1
	.word	492
	.byte	2,4,2,35,0,11
	.byte	'SH18',0,1
	.word	492
	.byte	2,2,2,35,0,11
	.byte	'SH19',0,1
	.word	492
	.byte	2,0,2,35,0,11
	.byte	'SH20',0,1
	.word	492
	.byte	2,6,2,35,1,11
	.byte	'SH21',0,1
	.word	492
	.byte	2,4,2,35,1,11
	.byte	'SH22',0,1
	.word	492
	.byte	2,2,2,35,1,11
	.byte	'SH23',0,1
	.word	492
	.byte	2,0,2,35,1,11
	.byte	'SH24',0,1
	.word	492
	.byte	2,6,2,35,2,11
	.byte	'SH25',0,1
	.word	492
	.byte	2,4,2,35,2,11
	.byte	'SH26',0,1
	.word	492
	.byte	2,2,2,35,2,11
	.byte	'SH27',0,1
	.word	492
	.byte	2,0,2,35,2,11
	.byte	'SH28',0,1
	.word	492
	.byte	2,6,2,35,3,11
	.byte	'SH29',0,1
	.word	492
	.byte	2,4,2,35,3,11
	.byte	'SH30',0,1
	.word	492
	.byte	2,2,2,35,3,11
	.byte	'SH31',0,1
	.word	492
	.byte	2,0,2,35,3,0,21
	.byte	'Ifx_MSC_DSDSHE_Bits',0,6,221,1,3
	.word	10896
	.byte	10
	.byte	'_Ifx_MSC_DSDSL_Bits',0,6,224,1,16,4,11
	.byte	'SL0',0,1
	.word	492
	.byte	2,6,2,35,0,11
	.byte	'SL1',0,1
	.word	492
	.byte	2,4,2,35,0,11
	.byte	'SL2',0,1
	.word	492
	.byte	2,2,2,35,0,11
	.byte	'SL3',0,1
	.word	492
	.byte	2,0,2,35,0,11
	.byte	'SL4',0,1
	.word	492
	.byte	2,6,2,35,1,11
	.byte	'SL5',0,1
	.word	492
	.byte	2,4,2,35,1,11
	.byte	'SL6',0,1
	.word	492
	.byte	2,2,2,35,1,11
	.byte	'SL7',0,1
	.word	492
	.byte	2,0,2,35,1,11
	.byte	'SL8',0,1
	.word	492
	.byte	2,6,2,35,2,11
	.byte	'SL9',0,1
	.word	492
	.byte	2,4,2,35,2,11
	.byte	'SL10',0,1
	.word	492
	.byte	2,2,2,35,2,11
	.byte	'SL11',0,1
	.word	492
	.byte	2,0,2,35,2,11
	.byte	'SL12',0,1
	.word	492
	.byte	2,6,2,35,3,11
	.byte	'SL13',0,1
	.word	492
	.byte	2,4,2,35,3,11
	.byte	'SL14',0,1
	.word	492
	.byte	2,2,2,35,3,11
	.byte	'SL15',0,1
	.word	492
	.byte	2,0,2,35,3,0,21
	.byte	'Ifx_MSC_DSDSL_Bits',0,6,242,1,3
	.word	11209
	.byte	10
	.byte	'_Ifx_MSC_DSDSLE_Bits',0,6,245,1,16,4,11
	.byte	'SL16',0,1
	.word	492
	.byte	2,6,2,35,0,11
	.byte	'SL17',0,1
	.word	492
	.byte	2,4,2,35,0,11
	.byte	'SL18',0,1
	.word	492
	.byte	2,2,2,35,0,11
	.byte	'SL19',0,1
	.word	492
	.byte	2,0,2,35,0,11
	.byte	'SL20',0,1
	.word	492
	.byte	2,6,2,35,1,11
	.byte	'SL21',0,1
	.word	492
	.byte	2,4,2,35,1,11
	.byte	'SL22',0,1
	.word	492
	.byte	2,2,2,35,1,11
	.byte	'SL23',0,1
	.word	492
	.byte	2,0,2,35,1,11
	.byte	'SL24',0,1
	.word	492
	.byte	2,6,2,35,2,11
	.byte	'SL25',0,1
	.word	492
	.byte	2,4,2,35,2,11
	.byte	'SL26',0,1
	.word	492
	.byte	2,2,2,35,2,11
	.byte	'SL27',0,1
	.word	492
	.byte	2,0,2,35,2,11
	.byte	'SL28',0,1
	.word	492
	.byte	2,6,2,35,3,11
	.byte	'SL29',0,1
	.word	492
	.byte	2,4,2,35,3,11
	.byte	'SL30',0,1
	.word	492
	.byte	2,2,2,35,3,11
	.byte	'SL31',0,1
	.word	492
	.byte	2,0,2,35,3,0,21
	.byte	'Ifx_MSC_DSDSLE_Bits',0,6,135,2,3
	.word	11510
	.byte	10
	.byte	'_Ifx_MSC_DSS_Bits',0,6,138,2,16,4,11
	.byte	'PFC',0,1
	.word	492
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	492
	.byte	4,0,2,35,0,11
	.byte	'NPTF',0,1
	.word	492
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	492
	.byte	4,0,2,35,1,11
	.byte	'DC',0,1
	.word	492
	.byte	8,0,2,35,2,11
	.byte	'DFA',0,1
	.word	492
	.byte	1,7,2,35,3,11
	.byte	'CFA',0,1
	.word	492
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	492
	.byte	6,0,2,35,3,0,21
	.byte	'Ifx_MSC_DSS_Bits',0,6,148,2,3
	.word	11823
	.byte	10
	.byte	'_Ifx_MSC_DSTE_Bits',0,6,151,2,16,4,11
	.byte	'PPDE',0,1
	.word	492
	.byte	2,6,2,35,0,11
	.byte	'PPCE',0,1
	.word	492
	.byte	6,0,2,35,0,11
	.byte	'NDD',0,1
	.word	492
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	469
	.byte	20,0,2,35,0,0,21
	.byte	'Ifx_MSC_DSTE_Bits',0,6,157,2,3
	.word	12017
	.byte	10
	.byte	'_Ifx_MSC_ESR_Bits',0,6,160,2,16,4,11
	.byte	'ENL0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'ENL1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'ENL2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'ENL3',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'ENL4',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'ENL5',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'ENL6',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'ENL7',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'ENL8',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'ENL9',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'ENL10',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'ENL11',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'ENL12',0,1
	.word	492
	.byte	1,3,2,35,1,11
	.byte	'ENL13',0,1
	.word	492
	.byte	1,2,2,35,1,11
	.byte	'ENL14',0,1
	.word	492
	.byte	1,1,2,35,1,11
	.byte	'ENL15',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'ENH0',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'ENH1',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'ENH2',0,1
	.word	492
	.byte	1,5,2,35,2,11
	.byte	'ENH3',0,1
	.word	492
	.byte	1,4,2,35,2,11
	.byte	'ENH4',0,1
	.word	492
	.byte	1,3,2,35,2,11
	.byte	'ENH5',0,1
	.word	492
	.byte	1,2,2,35,2,11
	.byte	'ENH6',0,1
	.word	492
	.byte	1,1,2,35,2,11
	.byte	'ENH7',0,1
	.word	492
	.byte	1,0,2,35,2,11
	.byte	'ENH8',0,1
	.word	492
	.byte	1,7,2,35,3,11
	.byte	'ENH9',0,1
	.word	492
	.byte	1,6,2,35,3,11
	.byte	'ENH10',0,1
	.word	492
	.byte	1,5,2,35,3,11
	.byte	'ENH11',0,1
	.word	492
	.byte	1,4,2,35,3,11
	.byte	'ENH12',0,1
	.word	492
	.byte	1,3,2,35,3,11
	.byte	'ENH13',0,1
	.word	492
	.byte	1,2,2,35,3,11
	.byte	'ENH14',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'ENH15',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_MSC_ESR_Bits',0,6,194,2,3
	.word	12140
	.byte	10
	.byte	'_Ifx_MSC_ESRE_Bits',0,6,197,2,16,4,11
	.byte	'ENL16',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'ENL17',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'ENL18',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'ENL19',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'ENL20',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'ENL21',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'ENL22',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'ENL23',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'ENL24',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'ENL25',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'ENL26',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'ENL27',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'ENL28',0,1
	.word	492
	.byte	1,3,2,35,1,11
	.byte	'ENL29',0,1
	.word	492
	.byte	1,2,2,35,1,11
	.byte	'ENL30',0,1
	.word	492
	.byte	1,1,2,35,1,11
	.byte	'ENL31',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'ENH16',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'ENH17',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'ENH18',0,1
	.word	492
	.byte	1,5,2,35,2,11
	.byte	'ENH19',0,1
	.word	492
	.byte	1,4,2,35,2,11
	.byte	'ENH20',0,1
	.word	492
	.byte	1,3,2,35,2,11
	.byte	'ENH21',0,1
	.word	492
	.byte	1,2,2,35,2,11
	.byte	'ENH22',0,1
	.word	492
	.byte	1,1,2,35,2,11
	.byte	'ENH23',0,1
	.word	492
	.byte	1,0,2,35,2,11
	.byte	'ENH24',0,1
	.word	492
	.byte	1,7,2,35,3,11
	.byte	'ENH25',0,1
	.word	492
	.byte	1,6,2,35,3,11
	.byte	'ENH26',0,1
	.word	492
	.byte	1,5,2,35,3,11
	.byte	'ENH27',0,1
	.word	492
	.byte	1,4,2,35,3,11
	.byte	'ENH28',0,1
	.word	492
	.byte	1,3,2,35,3,11
	.byte	'ENH29',0,1
	.word	492
	.byte	1,2,2,35,3,11
	.byte	'ENH30',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'ENH31',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_MSC_ESRE_Bits',0,6,231,2,3
	.word	12715
	.byte	10
	.byte	'_Ifx_MSC_FDR_Bits',0,6,234,2,16,4,11
	.byte	'STEP',0,2
	.word	509
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	492
	.byte	4,2,2,35,1,11
	.byte	'DM',0,1
	.word	492
	.byte	2,0,2,35,1,11
	.byte	'RESULT',0,2
	.word	509
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	492
	.byte	4,2,2,35,3,11
	.byte	'ENHW',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'DISCLK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_MSC_FDR_Bits',0,6,243,2,3
	.word	13312
	.byte	10
	.byte	'_Ifx_MSC_ICR_Bits',0,6,246,2,16,4,11
	.byte	'EDIP',0,1
	.word	492
	.byte	2,6,2,35,0,11
	.byte	'EDIE',0,1
	.word	492
	.byte	2,4,2,35,0,11
	.byte	'ECIP',0,1
	.word	492
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'ECIE',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'TFIP',0,1
	.word	492
	.byte	2,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'TFIE',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'RDIP',0,1
	.word	492
	.byte	2,2,2,35,1,11
	.byte	'RDIE',0,1
	.word	492
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_MSC_ICR_Bits',0,6,131,3,3
	.word	13491
	.byte	10
	.byte	'_Ifx_MSC_ID_Bits',0,6,134,3,16,4,11
	.byte	'MODREV',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	509
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_MSC_ID_Bits',0,6,139,3,3
	.word	13738
	.byte	10
	.byte	'_Ifx_MSC_ISC_Bits',0,6,142,3,16,4,11
	.byte	'CDEDI',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'CDECI',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'CDTFI',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'CURDI',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'CDP',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'CCP',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'CDDIS',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	509
	.byte	9,0,2,35,0,11
	.byte	'SDEDI',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'SDECI',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'SDTFI',0,1
	.word	492
	.byte	1,5,2,35,2,11
	.byte	'SURDI',0,1
	.word	492
	.byte	1,4,2,35,2,11
	.byte	'SDP',0,1
	.word	492
	.byte	1,3,2,35,2,11
	.byte	'SCP',0,1
	.word	492
	.byte	1,2,2,35,2,11
	.byte	'SDDIS',0,1
	.word	492
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	509
	.byte	9,0,2,35,2,0,21
	.byte	'Ifx_MSC_ISC_Bits',0,6,160,3,3
	.word	13845
	.byte	10
	.byte	'_Ifx_MSC_ISR_Bits',0,6,163,3,16,4,11
	.byte	'DEDI',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'DECI',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'DTFI',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'URDI',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	469
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_MSC_ISR_Bits',0,6,170,3,3
	.word	14171
	.byte	10
	.byte	'_Ifx_MSC_KRST0_Bits',0,6,173,3,16,4,11
	.byte	'RST',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'RSTSTAT',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	469
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_MSC_KRST0_Bits',0,6,178,3,3
	.word	14308
	.byte	10
	.byte	'_Ifx_MSC_KRST1_Bits',0,6,181,3,16,4,11
	.byte	'RST',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	469
	.byte	31,0,2,35,0,0,21
	.byte	'Ifx_MSC_KRST1_Bits',0,6,185,3,3
	.word	14419
	.byte	10
	.byte	'_Ifx_MSC_KRSTCLR_Bits',0,6,188,3,16,4,11
	.byte	'CLR',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	469
	.byte	31,0,2,35,0,0,21
	.byte	'Ifx_MSC_KRSTCLR_Bits',0,6,192,3,3
	.word	14511
	.byte	10
	.byte	'_Ifx_MSC_OCR_Bits',0,6,195,3,16,4,11
	.byte	'CLP',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'SLP',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'CSLP',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'ILP',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	492
	.byte	4,0,2,35,0,11
	.byte	'CLKCTRL',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'CSL',0,1
	.word	492
	.byte	2,5,2,35,1,11
	.byte	'CSH',0,1
	.word	492
	.byte	2,3,2,35,1,11
	.byte	'CSC',0,1
	.word	492
	.byte	2,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'SDISEL',0,1
	.word	492
	.byte	3,5,2,35,2,11
	.byte	'reserved_19',0,2
	.word	509
	.byte	13,0,2,35,2,0,21
	.byte	'Ifx_MSC_OCR_Bits',0,6,209,3,3
	.word	14607
	.byte	10
	.byte	'_Ifx_MSC_OCS_Bits',0,6,212,3,16,4,11
	.byte	'reserved_0',0,4
	.word	469
	.byte	24,8,2,35,0,11
	.byte	'SUS',0,1
	.word	492
	.byte	4,4,2,35,3,11
	.byte	'SUS_P',0,1
	.word	492
	.byte	1,3,2,35,3,11
	.byte	'SUSSTA',0,1
	.word	492
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	492
	.byte	2,0,2,35,3,0,21
	.byte	'Ifx_MSC_OCS_Bits',0,6,219,3,3
	.word	14869
	.byte	10
	.byte	'_Ifx_MSC_UD_Bits',0,6,222,3,16,4,11
	.byte	'DATA',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'V',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'P',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'C',0,1
	.word	492
	.byte	1,5,2,35,2,11
	.byte	'LABF',0,1
	.word	492
	.byte	2,3,2,35,2,11
	.byte	'IPF',0,1
	.word	492
	.byte	1,2,2,35,2,11
	.byte	'PERR',0,1
	.word	492
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	509
	.byte	9,0,2,35,2,0,21
	.byte	'Ifx_MSC_UD_Bits',0,6,233,3,3
	.word	15015
	.byte	10
	.byte	'_Ifx_MSC_USCE_Bits',0,6,236,3,16,4,11
	.byte	'USTOPRE',0,1
	.word	492
	.byte	4,4,2,35,0,11
	.byte	'USTOVAL',0,1
	.word	492
	.byte	4,0,2,35,0,11
	.byte	'USTOEN',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'USTF',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'USTC',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'USTS',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	492
	.byte	1,3,2,35,1,11
	.byte	'UTASR',0,1
	.word	492
	.byte	1,2,2,35,1,11
	.byte	'USTOIP',0,1
	.word	492
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_MSC_USCE_Bits',0,6,248,3,3
	.word	15211
	.byte	10
	.byte	'_Ifx_MSC_USR_Bits',0,6,251,3,16,4,11
	.byte	'UFT',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'URR',0,1
	.word	492
	.byte	3,4,2,35,0,11
	.byte	'PCTR',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'SRDC',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,2
	.word	509
	.byte	10,0,2,35,0,11
	.byte	'UC',0,1
	.word	492
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,2
	.word	509
	.byte	11,0,2,35,2,0,21
	.byte	'Ifx_MSC_USR_Bits',0,6,132,4,3
	.word	15449
	.byte	12,6,140,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8638
	.byte	4,2,35,0,0,21
	.byte	'Ifx_MSC_ABC',0,6,145,4,3
	.word	15621
	.byte	12,6,148,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8987
	.byte	4,2,35,0,0,21
	.byte	'Ifx_MSC_ACCEN0',0,6,153,4,3
	.word	15682
	.byte	12,6,156,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9544
	.byte	4,2,35,0,0,21
	.byte	'Ifx_MSC_ACCEN1',0,6,161,4,3
	.word	15746
	.byte	12,6,164,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9621
	.byte	4,2,35,0,0,21
	.byte	'Ifx_MSC_CLC',0,6,169,4,3
	.word	15810
	.byte	12,6,172,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9762
	.byte	4,2,35,0,0,21
	.byte	'Ifx_MSC_DC',0,6,177,4,3
	.word	15871
	.byte	12,6,180,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9839
	.byte	4,2,35,0,0,21
	.byte	'Ifx_MSC_DD',0,6,185,4,3
	.word	15931
	.byte	12,6,188,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9918
	.byte	4,2,35,0,0,21
	.byte	'Ifx_MSC_DDE',0,6,193,4,3
	.word	15991
	.byte	12,6,196,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10001
	.byte	4,2,35,0,0,21
	.byte	'Ifx_MSC_DDM',0,6,201,4,3
	.word	16052
	.byte	12,6,204,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10084
	.byte	4,2,35,0,0,21
	.byte	'Ifx_MSC_DSC',0,6,209,4,3
	.word	16113
	.byte	12,6,212,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10338
	.byte	4,2,35,0,0,21
	.byte	'Ifx_MSC_DSCE',0,6,217,4,3
	.word	16174
	.byte	12,6,220,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10595
	.byte	4,2,35,0,0,21
	.byte	'Ifx_MSC_DSDSH',0,6,225,4,3
	.word	16236
	.byte	12,6,228,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10896
	.byte	4,2,35,0,0,21
	.byte	'Ifx_MSC_DSDSHE',0,6,233,4,3
	.word	16299
	.byte	12,6,236,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11209
	.byte	4,2,35,0,0,21
	.byte	'Ifx_MSC_DSDSL',0,6,241,4,3
	.word	16363
	.byte	12,6,244,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11510
	.byte	4,2,35,0,0,21
	.byte	'Ifx_MSC_DSDSLE',0,6,249,4,3
	.word	16426
	.byte	12,6,252,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11823
	.byte	4,2,35,0,0,21
	.byte	'Ifx_MSC_DSS',0,6,129,5,3
	.word	16490
	.byte	12,6,132,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12017
	.byte	4,2,35,0,0,21
	.byte	'Ifx_MSC_DSTE',0,6,137,5,3
	.word	16551
	.byte	12,6,140,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12140
	.byte	4,2,35,0,0,21
	.byte	'Ifx_MSC_ESR',0,6,145,5,3
	.word	16613
	.byte	12,6,148,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12715
	.byte	4,2,35,0,0,21
	.byte	'Ifx_MSC_ESRE',0,6,153,5,3
	.word	16674
	.byte	12,6,156,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13312
	.byte	4,2,35,0,0,21
	.byte	'Ifx_MSC_FDR',0,6,161,5,3
	.word	16736
	.byte	12,6,164,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13491
	.byte	4,2,35,0,0,21
	.byte	'Ifx_MSC_ICR',0,6,169,5,3
	.word	16797
	.byte	12,6,172,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13738
	.byte	4,2,35,0,0,21
	.byte	'Ifx_MSC_ID',0,6,177,5,3
	.word	16858
	.byte	12,6,180,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13845
	.byte	4,2,35,0,0,21
	.byte	'Ifx_MSC_ISC',0,6,185,5,3
	.word	16918
	.byte	12,6,188,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14171
	.byte	4,2,35,0,0,21
	.byte	'Ifx_MSC_ISR',0,6,193,5,3
	.word	16979
	.byte	12,6,196,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14308
	.byte	4,2,35,0,0,21
	.byte	'Ifx_MSC_KRST0',0,6,201,5,3
	.word	17040
	.byte	12,6,204,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14419
	.byte	4,2,35,0,0,21
	.byte	'Ifx_MSC_KRST1',0,6,209,5,3
	.word	17103
	.byte	12,6,212,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14511
	.byte	4,2,35,0,0,21
	.byte	'Ifx_MSC_KRSTCLR',0,6,217,5,3
	.word	17166
	.byte	12,6,220,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14607
	.byte	4,2,35,0,0,21
	.byte	'Ifx_MSC_OCR',0,6,225,5,3
	.word	17231
	.byte	12,6,228,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14869
	.byte	4,2,35,0,0,21
	.byte	'Ifx_MSC_OCS',0,6,233,5,3
	.word	17292
	.byte	12,6,236,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15015
	.byte	4,2,35,0,0,21
	.byte	'Ifx_MSC_UD',0,6,241,5,3
	.word	17353
	.byte	12,6,244,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15211
	.byte	4,2,35,0,0,21
	.byte	'Ifx_MSC_USCE',0,6,249,5,3
	.word	17413
	.byte	12,6,252,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15449
	.byte	4,2,35,0,0,21
	.byte	'Ifx_MSC_USR',0,6,129,6,3
	.word	17475
	.byte	14,16
	.word	17353
	.byte	15,3,0,14,100
	.word	492
	.byte	15,99,0,10
	.byte	'_Ifx_MSC',0,6,140,6,25,128,2,13
	.byte	'CLC',0
	.word	15810
	.byte	4,2,35,0,13
	.byte	'reserved_4',0
	.word	1537
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	16858
	.byte	4,2,35,8,13
	.byte	'FDR',0
	.word	16736
	.byte	4,2,35,12,13
	.byte	'USR',0
	.word	17475
	.byte	4,2,35,16,13
	.byte	'DSC',0
	.word	16113
	.byte	4,2,35,20,13
	.byte	'DSS',0
	.word	16490
	.byte	4,2,35,24,13
	.byte	'DD',0
	.word	15931
	.byte	4,2,35,28,13
	.byte	'DC',0
	.word	15871
	.byte	4,2,35,32,13
	.byte	'DSDSL',0
	.word	16363
	.byte	4,2,35,36,13
	.byte	'DSDSH',0
	.word	16236
	.byte	4,2,35,40,13
	.byte	'ESR',0
	.word	16613
	.byte	4,2,35,44,13
	.byte	'UD',0
	.word	17536
	.byte	16,2,35,48,13
	.byte	'ICR',0
	.word	16797
	.byte	4,2,35,64,13
	.byte	'ISR',0
	.word	16979
	.byte	4,2,35,68,13
	.byte	'ISC',0
	.word	16918
	.byte	4,2,35,72,13
	.byte	'OCR',0
	.word	17231
	.byte	4,2,35,76,13
	.byte	'reserved_50',0
	.word	3356
	.byte	8,2,35,80,13
	.byte	'DSCE',0
	.word	16174
	.byte	4,2,35,88,13
	.byte	'USCE',0
	.word	17413
	.byte	4,2,35,92,13
	.byte	'DSDSLE',0
	.word	16426
	.byte	4,2,35,96,13
	.byte	'DSDSHE',0
	.word	16299
	.byte	4,2,35,100,13
	.byte	'ESRE',0
	.word	16674
	.byte	4,2,35,104,13
	.byte	'DDE',0
	.word	15991
	.byte	4,2,35,108,13
	.byte	'DDM',0
	.word	16052
	.byte	4,2,35,112,13
	.byte	'DSTE',0
	.word	16551
	.byte	4,2,35,116,13
	.byte	'reserved_78',0
	.word	3356
	.byte	8,2,35,120,13
	.byte	'ABC',0
	.word	15621
	.byte	4,3,35,128,1,13
	.byte	'reserved_84',0
	.word	17545
	.byte	100,3,35,132,1,13
	.byte	'OCS',0
	.word	17292
	.byte	4,3,35,232,1,13
	.byte	'KRSTCLR',0
	.word	17166
	.byte	4,3,35,236,1,13
	.byte	'KRST1',0
	.word	17103
	.byte	4,3,35,240,1,13
	.byte	'KRST0',0
	.word	17040
	.byte	4,3,35,244,1,13
	.byte	'ACCEN1',0
	.word	15746
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	15682
	.byte	4,3,35,252,1,0,16
	.word	17554
	.byte	21
	.byte	'Ifx_MSC',0,6,177,6,3
	.word	18089
	.byte	21
	.byte	'boolean',0,7,101,29
	.word	492
	.byte	21
	.byte	'uint8',0,7,105,29
	.word	492
	.byte	21
	.byte	'uint16',0,7,109,29
	.word	509
	.byte	7
	.byte	'unsigned long int',0,4,7,21
	.byte	'uint32',0,7,113,29
	.word	18156
	.byte	21
	.byte	'uint64',0,7,118,29
	.word	351
	.byte	21
	.byte	'sint16',0,7,126,29
	.word	8545
	.byte	7
	.byte	'long int',0,4,5,21
	.byte	'sint32',0,7,131,1,29
	.word	18222
	.byte	7
	.byte	'long long int',0,8,5,21
	.byte	'sint64',0,7,138,1,29
	.word	18250
	.byte	21
	.byte	'float32',0,7,167,1,29
	.word	297
	.byte	21
	.byte	'pvoid',0,8,57,28
	.word	383
	.byte	21
	.byte	'Ifx_TickTime',0,8,79,28
	.word	18250
	.byte	17,8,130,1,9,1,18
	.byte	'Ifx_RxSel_a',0,0,18
	.byte	'Ifx_RxSel_b',0,1,18
	.byte	'Ifx_RxSel_c',0,2,18
	.byte	'Ifx_RxSel_d',0,3,18
	.byte	'Ifx_RxSel_e',0,4,18
	.byte	'Ifx_RxSel_f',0,5,18
	.byte	'Ifx_RxSel_g',0,6,18
	.byte	'Ifx_RxSel_h',0,7,0,21
	.byte	'Ifx_RxSel',0,8,140,1,3
	.word	18335
	.byte	21
	.byte	'Ifx_P_ACCEN0_Bits',0,4,79,3
	.word	7109
	.byte	21
	.byte	'Ifx_P_ACCEN1_Bits',0,4,85,3
	.word	7022
	.byte	21
	.byte	'Ifx_P_ESR_Bits',0,4,107,3
	.word	3365
	.byte	21
	.byte	'Ifx_P_ID_Bits',0,4,115,3
	.word	1418
	.byte	21
	.byte	'Ifx_P_IN_Bits',0,4,137,1,3
	.word	2413
	.byte	21
	.byte	'Ifx_P_IOCR0_Bits',0,4,150,1,3
	.word	1546
	.byte	21
	.byte	'Ifx_P_IOCR12_Bits',0,4,163,1,3
	.word	2193
	.byte	21
	.byte	'Ifx_P_IOCR4_Bits',0,4,176,1,3
	.word	1761
	.byte	21
	.byte	'Ifx_P_IOCR8_Bits',0,4,189,1,3
	.word	1976
	.byte	21
	.byte	'Ifx_P_LPCR0_Bits',0,4,197,1,3
	.word	6381
	.byte	21
	.byte	'Ifx_P_LPCR1_Bits',0,4,205,1,3
	.word	6505
	.byte	21
	.byte	'Ifx_P_LPCR1_P21_Bits',0,4,215,1,3
	.word	6589
	.byte	21
	.byte	'Ifx_P_LPCR2_Bits',0,4,229,1,3
	.word	6769
	.byte	21
	.byte	'Ifx_P_OMCR0_Bits',0,4,240,1,3
	.word	5020
	.byte	21
	.byte	'Ifx_P_OMCR12_Bits',0,4,250,1,3
	.word	5544
	.byte	21
	.byte	'Ifx_P_OMCR4_Bits',0,4,133,2,3
	.word	5194
	.byte	21
	.byte	'Ifx_P_OMCR8_Bits',0,4,144,2,3
	.word	5368
	.byte	21
	.byte	'Ifx_P_OMCR_Bits',0,4,166,2,3
	.word	6033
	.byte	21
	.byte	'Ifx_P_OMR_Bits',0,4,203,2,3
	.word	847
	.byte	21
	.byte	'Ifx_P_OMSR0_Bits',0,4,213,2,3
	.word	4357
	.byte	21
	.byte	'Ifx_P_OMSR12_Bits',0,4,224,2,3
	.word	4845
	.byte	21
	.byte	'Ifx_P_OMSR4_Bits',0,4,235,2,3
	.word	4504
	.byte	21
	.byte	'Ifx_P_OMSR8_Bits',0,4,246,2,3
	.word	4673
	.byte	21
	.byte	'Ifx_P_OMSR_Bits',0,4,140,3,3
	.word	5700
	.byte	21
	.byte	'Ifx_P_OUT_Bits',0,4,162,3,3
	.word	531
	.byte	21
	.byte	'Ifx_P_PCSR_Bits',0,4,180,3,3
	.word	4071
	.byte	21
	.byte	'Ifx_P_PDISC_Bits',0,4,202,3,3
	.word	3705
	.byte	21
	.byte	'Ifx_P_PDR0_Bits',0,4,223,3,3
	.word	2736
	.byte	21
	.byte	'Ifx_P_PDR1_Bits',0,4,244,3,3
	.word	3040
	.byte	21
	.byte	'Ifx_P_ACCEN0',0,4,129,4,3
	.word	7636
	.byte	21
	.byte	'Ifx_P_ACCEN1',0,4,137,4,3
	.word	7069
	.byte	21
	.byte	'Ifx_P_ESR',0,4,145,4,3
	.word	3656
	.byte	21
	.byte	'Ifx_P_ID',0,4,153,4,3
	.word	1497
	.byte	21
	.byte	'Ifx_P_IN',0,4,161,4,3
	.word	2687
	.byte	21
	.byte	'Ifx_P_IOCR0',0,4,169,4,3
	.word	1721
	.byte	21
	.byte	'Ifx_P_IOCR12',0,4,177,4,3
	.word	2373
	.byte	21
	.byte	'Ifx_P_IOCR4',0,4,185,4,3
	.word	1936
	.byte	21
	.byte	'Ifx_P_IOCR8',0,4,193,4,3
	.word	2153
	.byte	21
	.byte	'Ifx_P_LPCR0',0,4,201,4,3
	.word	6465
	.byte	21
	.byte	'Ifx_P_LPCR1',0,4,210,4,3
	.word	6714
	.byte	21
	.byte	'Ifx_P_LPCR2',0,4,218,4,3
	.word	6973
	.byte	21
	.byte	'Ifx_P_OMCR',0,4,226,4,3
	.word	6341
	.byte	21
	.byte	'Ifx_P_OMCR0',0,4,234,4,3
	.word	5154
	.byte	21
	.byte	'Ifx_P_OMCR12',0,4,242,4,3
	.word	5660
	.byte	21
	.byte	'Ifx_P_OMCR4',0,4,250,4,3
	.word	5328
	.byte	21
	.byte	'Ifx_P_OMCR8',0,4,130,5,3
	.word	5504
	.byte	21
	.byte	'Ifx_P_OMR',0,4,138,5,3
	.word	1378
	.byte	21
	.byte	'Ifx_P_OMSR',0,4,146,5,3
	.word	5993
	.byte	21
	.byte	'Ifx_P_OMSR0',0,4,154,5,3
	.word	4464
	.byte	21
	.byte	'Ifx_P_OMSR12',0,4,162,5,3
	.word	4980
	.byte	21
	.byte	'Ifx_P_OMSR4',0,4,170,5,3
	.word	4633
	.byte	21
	.byte	'Ifx_P_OMSR8',0,4,178,5,3
	.word	4805
	.byte	21
	.byte	'Ifx_P_OUT',0,4,186,5,3
	.word	807
	.byte	21
	.byte	'Ifx_P_PCSR',0,4,194,5,3
	.word	4317
	.byte	21
	.byte	'Ifx_P_PDISC',0,4,202,5,3
	.word	4031
	.byte	21
	.byte	'Ifx_P_PDR0',0,4,210,5,3
	.word	3000
	.byte	21
	.byte	'Ifx_P_PDR1',0,4,218,5,3
	.word	3316
	.byte	16
	.word	7676
	.byte	21
	.byte	'Ifx_P',0,4,139,6,3
	.word	19791
	.byte	17,9,250,10,9,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy16384',0,0,18
	.byte	'IfxScu_WDTCON1_IR_divBy256',0,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy64',0,2,0,21
	.byte	'IfxScu_WDTCON1_IR',0,9,255,10,3
	.word	19811
	.byte	10
	.byte	'_Ifx_SCU_ACCEN0_Bits',0,10,45,16,4,11
	.byte	'EN0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	492
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	492
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	492
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	492
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	492
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	492
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	492
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	492
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	492
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	492
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	492
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	492
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	492
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	492
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	492
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_ACCEN0_Bits',0,10,79,3
	.word	19933
	.byte	10
	.byte	'_Ifx_SCU_ACCEN1_Bits',0,10,82,16,4,11
	.byte	'reserved_0',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_SCU_ACCEN1_Bits',0,10,85,3
	.word	20490
	.byte	10
	.byte	'_Ifx_SCU_ARSTDIS_Bits',0,10,88,16,4,11
	.byte	'STM0DIS',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'STM1DIS',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'STM2DIS',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	469
	.byte	29,0,2,35,0,0,21
	.byte	'Ifx_SCU_ARSTDIS_Bits',0,10,94,3
	.word	20567
	.byte	10
	.byte	'_Ifx_SCU_CCUCON0_Bits',0,10,97,16,4,11
	.byte	'BAUD1DIV',0,1
	.word	492
	.byte	4,4,2,35,0,11
	.byte	'BAUD2DIV',0,1
	.word	492
	.byte	4,0,2,35,0,11
	.byte	'SRIDIV',0,1
	.word	492
	.byte	4,4,2,35,1,11
	.byte	'LPDIV',0,1
	.word	492
	.byte	4,0,2,35,1,11
	.byte	'SPBDIV',0,1
	.word	492
	.byte	4,4,2,35,2,11
	.byte	'FSI2DIV',0,1
	.word	492
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	492
	.byte	2,0,2,35,2,11
	.byte	'FSIDIV',0,1
	.word	492
	.byte	2,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	492
	.byte	2,4,2,35,3,11
	.byte	'CLKSEL',0,1
	.word	492
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON0_Bits',0,10,111,3
	.word	20703
	.byte	10
	.byte	'_Ifx_SCU_CCUCON1_Bits',0,10,114,16,4,11
	.byte	'CANDIV',0,1
	.word	492
	.byte	4,4,2,35,0,11
	.byte	'ERAYDIV',0,1
	.word	492
	.byte	4,0,2,35,0,11
	.byte	'STMDIV',0,1
	.word	492
	.byte	4,4,2,35,1,11
	.byte	'GTMDIV',0,1
	.word	492
	.byte	4,0,2,35,1,11
	.byte	'ETHDIV',0,1
	.word	492
	.byte	4,4,2,35,2,11
	.byte	'ASCLINFDIV',0,1
	.word	492
	.byte	4,0,2,35,2,11
	.byte	'ASCLINSDIV',0,1
	.word	492
	.byte	4,4,2,35,3,11
	.byte	'INSEL',0,1
	.word	492
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON1_Bits',0,10,126,3
	.word	20983
	.byte	10
	.byte	'_Ifx_SCU_CCUCON2_Bits',0,10,129,1,16,4,11
	.byte	'BBBDIV',0,1
	.word	492
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	469
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON2_Bits',0,10,135,1,3
	.word	21221
	.byte	10
	.byte	'_Ifx_SCU_CCUCON3_Bits',0,10,138,1,16,4,11
	.byte	'PLLDIV',0,1
	.word	492
	.byte	6,2,2,35,0,11
	.byte	'PLLSEL',0,1
	.word	492
	.byte	2,0,2,35,0,11
	.byte	'PLLERAYDIV',0,1
	.word	492
	.byte	6,2,2,35,1,11
	.byte	'PLLERAYSEL',0,1
	.word	492
	.byte	2,0,2,35,1,11
	.byte	'SRIDIV',0,1
	.word	492
	.byte	6,2,2,35,2,11
	.byte	'SRISEL',0,1
	.word	492
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	492
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON3_Bits',0,10,150,1,3
	.word	21349
	.byte	10
	.byte	'_Ifx_SCU_CCUCON4_Bits',0,10,153,1,16,4,11
	.byte	'SPBDIV',0,1
	.word	492
	.byte	6,2,2,35,0,11
	.byte	'SPBSEL',0,1
	.word	492
	.byte	2,0,2,35,0,11
	.byte	'GTMDIV',0,1
	.word	492
	.byte	6,2,2,35,1,11
	.byte	'GTMSEL',0,1
	.word	492
	.byte	2,0,2,35,1,11
	.byte	'STMDIV',0,1
	.word	492
	.byte	6,2,2,35,2,11
	.byte	'STMSEL',0,1
	.word	492
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	492
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON4_Bits',0,10,165,1,3
	.word	21592
	.byte	10
	.byte	'_Ifx_SCU_CCUCON5_Bits',0,10,168,1,16,4,11
	.byte	'MAXDIV',0,1
	.word	492
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	469
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON5_Bits',0,10,174,1,3
	.word	21827
	.byte	10
	.byte	'_Ifx_SCU_CCUCON6_Bits',0,10,177,1,16,4,11
	.byte	'CPU0DIV',0,1
	.word	492
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	469
	.byte	26,0,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON6_Bits',0,10,181,1,3
	.word	21955
	.byte	10
	.byte	'_Ifx_SCU_CCUCON7_Bits',0,10,184,1,16,4,11
	.byte	'CPU1DIV',0,1
	.word	492
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	469
	.byte	26,0,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON7_Bits',0,10,188,1,3
	.word	22055
	.byte	10
	.byte	'_Ifx_SCU_CHIPID_Bits',0,10,191,1,16,4,11
	.byte	'CHREV',0,1
	.word	492
	.byte	6,2,2,35,0,11
	.byte	'CHTEC',0,1
	.word	492
	.byte	2,0,2,35,0,11
	.byte	'CHID',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'EEA',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'UCODE',0,1
	.word	492
	.byte	7,0,2,35,2,11
	.byte	'FSIZE',0,1
	.word	492
	.byte	4,4,2,35,3,11
	.byte	'SP',0,1
	.word	492
	.byte	2,2,2,35,3,11
	.byte	'SEC',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CHIPID_Bits',0,10,202,1,3
	.word	22155
	.byte	10
	.byte	'_Ifx_SCU_DTSCON_Bits',0,10,205,1,16,4,11
	.byte	'PWD',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'START',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	492
	.byte	2,4,2,35,0,11
	.byte	'CAL',0,4
	.word	469
	.byte	20,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	7,1,2,35,3,11
	.byte	'SLCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_DTSCON_Bits',0,10,213,1,3
	.word	22363
	.byte	10
	.byte	'_Ifx_SCU_DTSLIM_Bits',0,10,216,1,16,4,11
	.byte	'LOWER',0,2
	.word	509
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	492
	.byte	5,1,2,35,1,11
	.byte	'LLU',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'UPPER',0,2
	.word	509
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	492
	.byte	4,2,2,35,3,11
	.byte	'SLCK',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'UOF',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_DTSLIM_Bits',0,10,225,1,3
	.word	22528
	.byte	10
	.byte	'_Ifx_SCU_DTSSTAT_Bits',0,10,228,1,16,4,11
	.byte	'RESULT',0,2
	.word	509
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	492
	.byte	4,2,2,35,1,11
	.byte	'RDY',0,1
	.word	492
	.byte	1,1,2,35,1,11
	.byte	'BUSY',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_DTSSTAT_Bits',0,10,235,1,3
	.word	22711
	.byte	10
	.byte	'_Ifx_SCU_EICR_Bits',0,10,238,1,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	4,4,2,35,0,11
	.byte	'EXIS0',0,1
	.word	492
	.byte	3,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'FEN0',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'REN0',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'LDEN0',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'EIEN0',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'INP0',0,1
	.word	492
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	469
	.byte	5,12,2,35,0,11
	.byte	'EXIS1',0,1
	.word	492
	.byte	3,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	492
	.byte	1,0,2,35,2,11
	.byte	'FEN1',0,1
	.word	492
	.byte	1,7,2,35,3,11
	.byte	'REN1',0,1
	.word	492
	.byte	1,6,2,35,3,11
	.byte	'LDEN1',0,1
	.word	492
	.byte	1,5,2,35,3,11
	.byte	'EIEN1',0,1
	.word	492
	.byte	1,4,2,35,3,11
	.byte	'INP1',0,1
	.word	492
	.byte	3,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EICR_Bits',0,10,129,2,3
	.word	22865
	.byte	10
	.byte	'_Ifx_SCU_EIFR_Bits',0,10,132,2,16,4,11
	.byte	'INTF0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'INTF1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'INTF2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'INTF3',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'INTF4',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'INTF5',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'INTF6',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'INTF7',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	469
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_SCU_EIFR_Bits',0,10,143,2,3
	.word	23229
	.byte	10
	.byte	'_Ifx_SCU_EMSR_Bits',0,10,146,2,16,4,11
	.byte	'POL',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'MODE',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'ENON',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'PSEL',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	509
	.byte	12,0,2,35,0,11
	.byte	'EMSF',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'SEMSF',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	492
	.byte	6,0,2,35,2,11
	.byte	'EMSFM',0,1
	.word	492
	.byte	2,6,2,35,3,11
	.byte	'SEMSFM',0,1
	.word	492
	.byte	2,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	492
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_EMSR_Bits',0,10,159,2,3
	.word	23440
	.byte	10
	.byte	'_Ifx_SCU_ESRCFG_Bits',0,10,162,2,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	7,1,2,35,0,11
	.byte	'EDCON',0,2
	.word	509
	.byte	2,7,2,35,0,11
	.byte	'reserved_9',0,4
	.word	469
	.byte	23,0,2,35,0,0,21
	.byte	'Ifx_SCU_ESRCFG_Bits',0,10,167,2,3
	.word	23692
	.byte	10
	.byte	'_Ifx_SCU_ESROCFG_Bits',0,10,170,2,16,4,11
	.byte	'ARI',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'ARC',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	469
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_ESROCFG_Bits',0,10,175,2,3
	.word	23810
	.byte	10
	.byte	'_Ifx_SCU_EVR13CON_Bits',0,10,178,2,16,4,11
	.byte	'reserved_0',0,4
	.word	469
	.byte	28,4,2,35,0,11
	.byte	'EVR13OFF',0,1
	.word	492
	.byte	1,3,2,35,3,11
	.byte	'BPEVR13OFF',0,1
	.word	492
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVR13CON_Bits',0,10,185,2,3
	.word	23921
	.byte	10
	.byte	'_Ifx_SCU_EVR33CON_Bits',0,10,188,2,16,4,11
	.byte	'reserved_0',0,4
	.word	469
	.byte	28,4,2,35,0,11
	.byte	'EVR33OFF',0,1
	.word	492
	.byte	1,3,2,35,3,11
	.byte	'BPEVR33OFF',0,1
	.word	492
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVR33CON_Bits',0,10,195,2,3
	.word	24084
	.byte	10
	.byte	'_Ifx_SCU_EVRADCSTAT_Bits',0,10,198,2,16,4,11
	.byte	'ADC13V',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'ADC33V',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'ADCSWDV',0,1
	.word	492
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRADCSTAT_Bits',0,10,205,2,3
	.word	24247
	.byte	10
	.byte	'_Ifx_SCU_EVRDVSTAT_Bits',0,10,208,2,16,4,11
	.byte	'DVS13TRIM',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'DVS33TRIM',0,1
	.word	492
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRDVSTAT_Bits',0,10,215,2,3
	.word	24405
	.byte	10
	.byte	'_Ifx_SCU_EVRMONCTRL_Bits',0,10,218,2,16,4,11
	.byte	'EVR13OVMOD',0,1
	.word	492
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	492
	.byte	2,4,2,35,0,11
	.byte	'EVR13UVMOD',0,1
	.word	492
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	492
	.byte	2,0,2,35,0,11
	.byte	'EVR33OVMOD',0,1
	.word	492
	.byte	2,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	492
	.byte	2,4,2,35,1,11
	.byte	'EVR33UVMOD',0,1
	.word	492
	.byte	2,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	492
	.byte	2,0,2,35,1,11
	.byte	'SWDOVMOD',0,1
	.word	492
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	492
	.byte	2,4,2,35,2,11
	.byte	'SWDUVMOD',0,1
	.word	492
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,2
	.word	509
	.byte	10,0,2,35,2,0,21
	.byte	'Ifx_SCU_EVRMONCTRL_Bits',0,10,232,2,3
	.word	24570
	.byte	10
	.byte	'_Ifx_SCU_EVROSCCTRL_Bits',0,10,235,2,16,4,11
	.byte	'OSCTRIM',0,2
	.word	509
	.byte	10,6,2,35,0,11
	.byte	'OSCPTAT',0,1
	.word	492
	.byte	6,0,2,35,1,11
	.byte	'OSCANASEL',0,1
	.word	492
	.byte	4,4,2,35,2,11
	.byte	'HPBGTRIM',0,2
	.word	509
	.byte	7,5,2,35,2,11
	.byte	'HPBGCLKEN',0,1
	.word	492
	.byte	1,4,2,35,3,11
	.byte	'OSC3V3',0,1
	.word	492
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	492
	.byte	2,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVROSCCTRL_Bits',0,10,245,2,3
	.word	24899
	.byte	10
	.byte	'_Ifx_SCU_EVROVMON_Bits',0,10,248,2,16,4,11
	.byte	'EVR13OVVAL',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'EVR33OVVAL',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'SWDOVVAL',0,1
	.word	492
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVROVMON_Bits',0,10,255,2,3
	.word	25120
	.byte	10
	.byte	'_Ifx_SCU_EVRRSTCON_Bits',0,10,130,3,16,4,11
	.byte	'RST13TRIM',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	469
	.byte	16,8,2,35,0,11
	.byte	'RST13OFF',0,1
	.word	492
	.byte	1,7,2,35,3,11
	.byte	'BPRST13OFF',0,1
	.word	492
	.byte	1,6,2,35,3,11
	.byte	'RST33OFF',0,1
	.word	492
	.byte	1,5,2,35,3,11
	.byte	'BPRST33OFF',0,1
	.word	492
	.byte	1,4,2,35,3,11
	.byte	'RSTSWDOFF',0,1
	.word	492
	.byte	1,3,2,35,3,11
	.byte	'BPRSTSWDOFF',0,1
	.word	492
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRRSTCON_Bits',0,10,142,3,3
	.word	25283
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF1_Bits',0,10,145,3,16,4,11
	.byte	'SD5P',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'SD5I',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'SD5D',0,1
	.word	492
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF1_Bits',0,10,152,3,3
	.word	25555
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF2_Bits',0,10,155,3,16,4,11
	.byte	'SD33P',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'SD33I',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'SD33D',0,1
	.word	492
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF2_Bits',0,10,162,3,3
	.word	25708
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF3_Bits',0,10,165,3,16,4,11
	.byte	'CT5REG0',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'CT5REG1',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'CT5REG2',0,1
	.word	492
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF3_Bits',0,10,172,3,3
	.word	25864
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF4_Bits',0,10,175,3,16,4,11
	.byte	'CT5REG3',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'CT5REG4',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF4_Bits',0,10,181,3,3
	.word	26026
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF5_Bits',0,10,184,3,16,4,11
	.byte	'CT33REG0',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'CT33REG1',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'CT33REG2',0,1
	.word	492
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF5_Bits',0,10,191,3,3
	.word	26169
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF6_Bits',0,10,194,3,16,4,11
	.byte	'CT33REG3',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'CT33REG4',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF6_Bits',0,10,200,3,3
	.word	26334
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL1_Bits',0,10,203,3,16,4,11
	.byte	'SDFREQSPRD',0,2
	.word	509
	.byte	16,0,2,35,0,11
	.byte	'SDFREQ',0,1
	.word	492
	.byte	8,0,2,35,2,11
	.byte	'SDSTEP',0,1
	.word	492
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	492
	.byte	2,2,2,35,3,11
	.byte	'SDSAMPLE',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCTRL1_Bits',0,10,211,3,3
	.word	26479
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL2_Bits',0,10,214,3,16,4,11
	.byte	'DRVP',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'SDMINMAXDC',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'DRVN',0,1
	.word	492
	.byte	8,0,2,35,2,11
	.byte	'SDLUT',0,1
	.word	492
	.byte	6,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCTRL2_Bits',0,10,222,3,3
	.word	26660
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL3_Bits',0,10,225,3,16,4,11
	.byte	'SDPWMPRE',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'SDPID',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'SDVOKLVL',0,1
	.word	492
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCTRL3_Bits',0,10,232,3,3
	.word	26834
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL4_Bits',0,10,235,3,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'SYNCDIV',0,1
	.word	492
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	469
	.byte	20,1,2,35,0,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCTRL4_Bits',0,10,241,3,3
	.word	26994
	.byte	10
	.byte	'_Ifx_SCU_EVRSTAT_Bits',0,10,244,3,16,4,11
	.byte	'EVR13',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'OV13',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'EVR33',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'OV33',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'OVSWD',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'UV13',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'UV33',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'UVSWD',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'EXTPASS13',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'EXTPASS33',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'BGPROK',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	469
	.byte	21,0,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSTAT_Bits',0,10,130,4,3
	.word	27138
	.byte	10
	.byte	'_Ifx_SCU_EVRTRIM_Bits',0,10,133,4,16,4,11
	.byte	'EVR13TRIM',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'SDVOUTSEL',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRTRIM_Bits',0,10,139,4,3
	.word	27412
	.byte	10
	.byte	'_Ifx_SCU_EVRUVMON_Bits',0,10,142,4,16,4,11
	.byte	'EVR13UVVAL',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'EVR33UVVAL',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'SWDUVVAL',0,1
	.word	492
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRUVMON_Bits',0,10,149,4,3
	.word	27551
	.byte	10
	.byte	'_Ifx_SCU_EXTCON_Bits',0,10,152,4,16,4,11
	.byte	'EN0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'SEL0',0,1
	.word	492
	.byte	4,2,2,35,0,11
	.byte	'reserved_6',0,2
	.word	509
	.byte	10,0,2,35,0,11
	.byte	'EN1',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'NSEL',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'SEL1',0,1
	.word	492
	.byte	4,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	492
	.byte	2,0,2,35,2,11
	.byte	'DIV1',0,1
	.word	492
	.byte	8,0,2,35,3,0,21
	.byte	'Ifx_SCU_EXTCON_Bits',0,10,163,4,3
	.word	27714
	.byte	10
	.byte	'_Ifx_SCU_FDR_Bits',0,10,166,4,16,4,11
	.byte	'STEP',0,2
	.word	509
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	492
	.byte	4,2,2,35,1,11
	.byte	'DM',0,1
	.word	492
	.byte	2,0,2,35,1,11
	.byte	'RESULT',0,2
	.word	509
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	492
	.byte	5,1,2,35,3,11
	.byte	'DISCLK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_FDR_Bits',0,10,174,4,3
	.word	27932
	.byte	10
	.byte	'_Ifx_SCU_FMR_Bits',0,10,177,4,16,4,11
	.byte	'FS0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'FS1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'FS2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'FS3',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'FS4',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'FS5',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'FS6',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'FS7',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'FC0',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'FC1',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'FC2',0,1
	.word	492
	.byte	1,5,2,35,2,11
	.byte	'FC3',0,1
	.word	492
	.byte	1,4,2,35,2,11
	.byte	'FC4',0,1
	.word	492
	.byte	1,3,2,35,2,11
	.byte	'FC5',0,1
	.word	492
	.byte	1,2,2,35,2,11
	.byte	'FC6',0,1
	.word	492
	.byte	1,1,2,35,2,11
	.byte	'FC7',0,1
	.word	492
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	8,0,2,35,3,0,21
	.byte	'Ifx_SCU_FMR_Bits',0,10,197,4,3
	.word	28095
	.byte	10
	.byte	'_Ifx_SCU_ID_Bits',0,10,200,4,16,4,11
	.byte	'MODREV',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	509
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_ID_Bits',0,10,205,4,3
	.word	28431
	.byte	10
	.byte	'_Ifx_SCU_IGCR_Bits',0,10,208,4,16,4,11
	.byte	'IPEN00',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'IPEN01',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'IPEN02',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'IPEN03',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'IPEN04',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'IPEN05',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'IPEN06',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'IPEN07',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	492
	.byte	5,3,2,35,1,11
	.byte	'GEEN0',0,1
	.word	492
	.byte	1,2,2,35,1,11
	.byte	'IGP0',0,1
	.word	492
	.byte	2,0,2,35,1,11
	.byte	'IPEN10',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'IPEN11',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'IPEN12',0,1
	.word	492
	.byte	1,5,2,35,2,11
	.byte	'IPEN13',0,1
	.word	492
	.byte	1,4,2,35,2,11
	.byte	'IPEN14',0,1
	.word	492
	.byte	1,3,2,35,2,11
	.byte	'IPEN15',0,1
	.word	492
	.byte	1,2,2,35,2,11
	.byte	'IPEN16',0,1
	.word	492
	.byte	1,1,2,35,2,11
	.byte	'IPEN17',0,1
	.word	492
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	5,3,2,35,3,11
	.byte	'GEEN1',0,1
	.word	492
	.byte	1,2,2,35,3,11
	.byte	'IGP1',0,1
	.word	492
	.byte	2,0,2,35,3,0,21
	.byte	'Ifx_SCU_IGCR_Bits',0,10,232,4,3
	.word	28538
	.byte	10
	.byte	'_Ifx_SCU_IN_Bits',0,10,235,4,16,4,11
	.byte	'P0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	469
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_IN_Bits',0,10,240,4,3
	.word	28990
	.byte	10
	.byte	'_Ifx_SCU_IOCR_Bits',0,10,243,4,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	4,4,2,35,0,11
	.byte	'PC0',0,1
	.word	492
	.byte	4,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	492
	.byte	4,4,2,35,1,11
	.byte	'PC1',0,1
	.word	492
	.byte	4,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_IOCR_Bits',0,10,250,4,3
	.word	29089
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL0_Bits',0,10,253,4,16,4,11
	.byte	'LBISTREQ',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'LBISTREQP',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'PATTERNS',0,2
	.word	509
	.byte	14,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_LBISTCTRL0_Bits',0,10,131,5,3
	.word	29239
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL1_Bits',0,10,134,5,16,4,11
	.byte	'SEED',0,4
	.word	469
	.byte	23,9,2,35,0,11
	.byte	'reserved_23',0,1
	.word	492
	.byte	1,0,2,35,2,11
	.byte	'SPLITSH',0,1
	.word	492
	.byte	3,5,2,35,3,11
	.byte	'BODY',0,1
	.word	492
	.byte	1,4,2,35,3,11
	.byte	'LBISTFREQU',0,1
	.word	492
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_LBISTCTRL1_Bits',0,10,141,5,3
	.word	29388
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL2_Bits',0,10,144,5,16,4,11
	.byte	'SIGNATURE',0,4
	.word	469
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	7,1,2,35,3,11
	.byte	'LBISTDONE',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_LBISTCTRL2_Bits',0,10,149,5,3
	.word	29549
	.byte	10
	.byte	'_Ifx_SCU_LCLCON_Bits',0,10,152,5,16,4,11
	.byte	'reserved_0',0,2
	.word	509
	.byte	16,0,2,35,0,11
	.byte	'LS',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,2
	.word	509
	.byte	14,1,2,35,2,11
	.byte	'LSEN',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_LCLCON_Bits',0,10,158,5,3
	.word	29679
	.byte	10
	.byte	'_Ifx_SCU_LCLTEST_Bits',0,10,161,5,16,4,11
	.byte	'LCLT0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'LCLT1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	469
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_LCLTEST_Bits',0,10,166,5,3
	.word	29811
	.byte	10
	.byte	'_Ifx_SCU_MANID_Bits',0,10,169,5,16,4,11
	.byte	'DEPT',0,1
	.word	492
	.byte	5,3,2,35,0,11
	.byte	'MANUF',0,2
	.word	509
	.byte	11,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_MANID_Bits',0,10,174,5,3
	.word	29926
	.byte	10
	.byte	'_Ifx_SCU_OMR_Bits',0,10,177,5,16,4,11
	.byte	'PS0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	509
	.byte	14,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	509
	.byte	14,0,2,35,2,0,21
	.byte	'Ifx_SCU_OMR_Bits',0,10,185,5,3
	.word	30037
	.byte	10
	.byte	'_Ifx_SCU_OSCCON_Bits',0,10,188,5,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'PLLLV',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'OSCRES',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'GAINSEL',0,1
	.word	492
	.byte	2,3,2,35,0,11
	.byte	'MODE',0,1
	.word	492
	.byte	2,1,2,35,0,11
	.byte	'SHBY',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'PLLHV',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'X1D',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'X1DEN',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	492
	.byte	4,0,2,35,1,11
	.byte	'OSCVAL',0,1
	.word	492
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	492
	.byte	2,1,2,35,2,11
	.byte	'APREN',0,1
	.word	492
	.byte	1,0,2,35,2,11
	.byte	'CAP0EN',0,1
	.word	492
	.byte	1,7,2,35,3,11
	.byte	'CAP1EN',0,1
	.word	492
	.byte	1,6,2,35,3,11
	.byte	'CAP2EN',0,1
	.word	492
	.byte	1,5,2,35,3,11
	.byte	'CAP3EN',0,1
	.word	492
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	492
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_OSCCON_Bits',0,10,209,5,3
	.word	30195
	.byte	10
	.byte	'_Ifx_SCU_OUT_Bits',0,10,212,5,16,4,11
	.byte	'P0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	469
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_OUT_Bits',0,10,217,5,3
	.word	30607
	.byte	10
	.byte	'_Ifx_SCU_OVCCON_Bits',0,10,220,5,16,4,11
	.byte	'CSEL0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'CSEL1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'CSEL2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,2
	.word	509
	.byte	13,0,2,35,0,11
	.byte	'OVSTRT',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'OVSTP',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'DCINVAL',0,1
	.word	492
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	492
	.byte	5,0,2,35,2,11
	.byte	'OVCONF',0,1
	.word	492
	.byte	1,7,2,35,3,11
	.byte	'POVCONF',0,1
	.word	492
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	492
	.byte	6,0,2,35,3,0,21
	.byte	'Ifx_SCU_OVCCON_Bits',0,10,233,5,3
	.word	30708
	.byte	10
	.byte	'_Ifx_SCU_OVCENABLE_Bits',0,10,236,5,16,4,11
	.byte	'OVEN0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'OVEN1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'OVEN2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	469
	.byte	29,0,2,35,0,0,21
	.byte	'Ifx_SCU_OVCENABLE_Bits',0,10,242,5,3
	.word	30975
	.byte	10
	.byte	'_Ifx_SCU_PDISC_Bits',0,10,245,5,16,4,11
	.byte	'PDIS0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	469
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_PDISC_Bits',0,10,250,5,3
	.word	31111
	.byte	10
	.byte	'_Ifx_SCU_PDR_Bits',0,10,253,5,16,4,11
	.byte	'PD0',0,1
	.word	492
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	492
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	469
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_SCU_PDR_Bits',0,10,132,6,3
	.word	31222
	.byte	10
	.byte	'_Ifx_SCU_PDRR_Bits',0,10,135,6,16,4,11
	.byte	'PDR0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'PDR1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'PDR2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'PDR3',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'PDR4',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'PDR5',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'PDR6',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'PDR7',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	469
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_SCU_PDRR_Bits',0,10,146,6,3
	.word	31355
	.byte	10
	.byte	'_Ifx_SCU_PLLCON0_Bits',0,10,149,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'MODEN',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	509
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	492
	.byte	7,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	492
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	492
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	492
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	492
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_PLLCON0_Bits',0,10,166,6,3
	.word	31558
	.byte	10
	.byte	'_Ifx_SCU_PLLCON1_Bits',0,10,169,6,16,4,11
	.byte	'K2DIV',0,1
	.word	492
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	492
	.byte	7,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	492
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	509
	.byte	9,0,2,35,2,0,21
	.byte	'Ifx_SCU_PLLCON1_Bits',0,10,177,6,3
	.word	31914
	.byte	10
	.byte	'_Ifx_SCU_PLLCON2_Bits',0,10,180,6,16,4,11
	.byte	'MODCFG',0,2
	.word	509
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_PLLCON2_Bits',0,10,184,6,3
	.word	32092
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON0_Bits',0,10,187,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	492
	.byte	2,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	509
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	492
	.byte	5,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	492
	.byte	2,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	492
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	492
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	492
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	492
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_PLLERAYCON0_Bits',0,10,204,6,3
	.word	32192
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON1_Bits',0,10,207,6,16,4,11
	.byte	'K2DIV',0,1
	.word	492
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	492
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	492
	.byte	4,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	492
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	509
	.byte	9,0,2,35,2,0,21
	.byte	'Ifx_SCU_PLLERAYCON1_Bits',0,10,215,6,3
	.word	32562
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYSTAT_Bits',0,10,218,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'PWDSTAT',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	469
	.byte	26,0,2,35,0,0,21
	.byte	'Ifx_SCU_PLLERAYSTAT_Bits',0,10,227,6,3
	.word	32748
	.byte	10
	.byte	'_Ifx_SCU_PLLSTAT_Bits',0,10,230,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'MODRUN',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	469
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_SCU_PLLSTAT_Bits',0,10,241,6,3
	.word	32946
	.byte	10
	.byte	'_Ifx_SCU_PMCSR_Bits',0,10,244,6,16,4,11
	.byte	'REQSLP',0,1
	.word	492
	.byte	2,6,2,35,0,11
	.byte	'SMUSLP',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	492
	.byte	5,0,2,35,0,11
	.byte	'PMST',0,1
	.word	492
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	469
	.byte	21,0,2,35,0,0,21
	.byte	'Ifx_SCU_PMCSR_Bits',0,10,251,6,3
	.word	33179
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR0_Bits',0,10,254,6,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'ESR1WKEN',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'PINAWKEN',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'PINBWKEN',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'ESR0DFEN',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'ESR0EDCON',0,1
	.word	492
	.byte	2,1,2,35,0,11
	.byte	'ESR1DFEN',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'ESR1EDCON',0,1
	.word	492
	.byte	2,6,2,35,1,11
	.byte	'PINADFEN',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'PINAEDCON',0,1
	.word	492
	.byte	2,3,2,35,1,11
	.byte	'PINBDFEN',0,1
	.word	492
	.byte	1,2,2,35,1,11
	.byte	'PINBEDCON',0,1
	.word	492
	.byte	2,0,2,35,1,11
	.byte	'SCREN',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'STBYRAMSEL',0,1
	.word	492
	.byte	2,5,2,35,2,11
	.byte	'SCRCLKSEL',0,1
	.word	492
	.byte	1,4,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	492
	.byte	1,3,2,35,2,11
	.byte	'TRISTEN',0,1
	.word	492
	.byte	1,2,2,35,2,11
	.byte	'TRISTREQ',0,1
	.word	492
	.byte	1,1,2,35,2,11
	.byte	'PORSTDF',0,1
	.word	492
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	1,7,2,35,3,11
	.byte	'DCDCSYNC',0,1
	.word	492
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	492
	.byte	3,3,2,35,3,11
	.byte	'ESR0TRIST',0,1
	.word	492
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_PMSWCR0_Bits',0,10,153,7,3
	.word	33331
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR1_Bits',0,10,156,7,16,4,11
	.byte	'SCRSTEN',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'SCRSTREQ',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	492
	.byte	6,0,2,35,0,11
	.byte	'CPUIDLSEL',0,1
	.word	492
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'IRADIS',0,1
	.word	492
	.byte	1,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	492
	.byte	3,0,2,35,1,11
	.byte	'SCRCFG',0,1
	.word	492
	.byte	8,0,2,35,2,11
	.byte	'CPUSEL',0,1
	.word	492
	.byte	3,5,2,35,3,11
	.byte	'STBYEVEN',0,1
	.word	492
	.byte	1,4,2,35,3,11
	.byte	'STBYEV',0,1
	.word	492
	.byte	3,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_PMSWCR1_Bits',0,10,170,7,3
	.word	33898
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR2_Bits',0,10,173,7,16,4,11
	.byte	'SCRINT',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'BUSY',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'SCRECC',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'SCRWDT',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'SCRRST',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	492
	.byte	4,0,2,35,1,11
	.byte	'TCINT',0,1
	.word	492
	.byte	8,0,2,35,2,11
	.byte	'TCINTREQ',0,1
	.word	492
	.byte	1,7,2,35,3,11
	.byte	'SMURST',0,1
	.word	492
	.byte	1,6,2,35,3,11
	.byte	'RST',0,1
	.word	492
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	492
	.byte	4,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_PMSWCR2_Bits',0,10,187,7,3
	.word	34192
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTAT_Bits',0,10,190,7,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKP',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUN',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'PINAWKP',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUN',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'PINBWKP',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUN',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'PORSTDF',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'HWCFGEVR',0,1
	.word	492
	.byte	3,3,2,35,1,11
	.byte	'STBYRAM',0,1
	.word	492
	.byte	2,1,2,35,1,11
	.byte	'TRIST',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'SCRST',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'SCRWKP',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'SCR',0,1
	.word	492
	.byte	1,5,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	492
	.byte	1,4,2,35,2,11
	.byte	'ESR1WKEN',0,1
	.word	492
	.byte	1,3,2,35,2,11
	.byte	'PINAWKEN',0,1
	.word	492
	.byte	1,2,2,35,2,11
	.byte	'PINBWKEN',0,1
	.word	492
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	509
	.byte	4,5,2,35,2,11
	.byte	'ESR0TRIST',0,1
	.word	492
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	492
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_PMSWSTAT_Bits',0,10,214,7,3
	.word	34470
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTATCLR_Bits',0,10,217,7,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKPCLR',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUNCLR',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'PINAWKPCLR',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUNCLR',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'PINBWKPCLR',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUNCLR',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'SCRSTCLR',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'SCRWKPCLR',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	509
	.byte	14,0,2,35,2,0,21
	.byte	'Ifx_SCU_PMSWSTATCLR_Bits',0,10,230,7,3
	.word	34966
	.byte	10
	.byte	'_Ifx_SCU_RSTCON2_Bits',0,10,233,7,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'CLRC',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	509
	.byte	10,4,2,35,0,11
	.byte	'CSS0',0,1
	.word	492
	.byte	1,3,2,35,1,11
	.byte	'CSS1',0,1
	.word	492
	.byte	1,2,2,35,1,11
	.byte	'CSS2',0,1
	.word	492
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'USRINFO',0,2
	.word	509
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_RSTCON2_Bits',0,10,243,7,3
	.word	35279
	.byte	10
	.byte	'_Ifx_SCU_RSTCON_Bits',0,10,246,7,16,4,11
	.byte	'ESR0',0,1
	.word	492
	.byte	2,6,2,35,0,11
	.byte	'ESR1',0,1
	.word	492
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	492
	.byte	2,2,2,35,0,11
	.byte	'SMU',0,1
	.word	492
	.byte	2,0,2,35,0,11
	.byte	'SW',0,1
	.word	492
	.byte	2,6,2,35,1,11
	.byte	'STM0',0,1
	.word	492
	.byte	2,4,2,35,1,11
	.byte	'STM1',0,1
	.word	492
	.byte	2,2,2,35,1,11
	.byte	'STM2',0,1
	.word	492
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_RSTCON_Bits',0,10,129,8,3
	.word	35488
	.byte	10
	.byte	'_Ifx_SCU_RSTSTAT_Bits',0,10,132,8,16,4,11
	.byte	'ESR0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'ESR1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'SMU',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'SW',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'STM0',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'STM1',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'STM2',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'PORST',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'CB0',0,1
	.word	492
	.byte	1,5,2,35,2,11
	.byte	'CB1',0,1
	.word	492
	.byte	1,4,2,35,2,11
	.byte	'CB3',0,1
	.word	492
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	492
	.byte	2,1,2,35,2,11
	.byte	'EVR13',0,1
	.word	492
	.byte	1,0,2,35,2,11
	.byte	'EVR33',0,1
	.word	492
	.byte	1,7,2,35,3,11
	.byte	'SWD',0,1
	.word	492
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	492
	.byte	2,4,2,35,3,11
	.byte	'STBYR',0,1
	.word	492
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	492
	.byte	3,0,2,35,3,0,21
	.byte	'Ifx_SCU_RSTSTAT_Bits',0,10,155,8,3
	.word	35699
	.byte	10
	.byte	'_Ifx_SCU_SAFECON_Bits',0,10,158,8,16,4,11
	.byte	'HBT',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	469
	.byte	31,0,2,35,0,0,21
	.byte	'Ifx_SCU_SAFECON_Bits',0,10,162,8,3
	.word	36131
	.byte	10
	.byte	'_Ifx_SCU_STSTAT_Bits',0,10,165,8,16,4,11
	.byte	'HWCFG',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'FTM',0,1
	.word	492
	.byte	7,1,2,35,1,11
	.byte	'MODE',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'FCBAE',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'LUDIS',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	492
	.byte	1,5,2,35,2,11
	.byte	'TRSTL',0,1
	.word	492
	.byte	1,4,2,35,2,11
	.byte	'SPDEN',0,1
	.word	492
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	492
	.byte	3,0,2,35,2,11
	.byte	'RAMINT',0,1
	.word	492
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	492
	.byte	7,0,2,35,3,0,21
	.byte	'Ifx_SCU_STSTAT_Bits',0,10,178,8,3
	.word	36227
	.byte	10
	.byte	'_Ifx_SCU_SWRSTCON_Bits',0,10,181,8,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'SWRSTREQ',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	469
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_SWRSTCON_Bits',0,10,186,8,3
	.word	36487
	.byte	10
	.byte	'_Ifx_SCU_SYSCON_Bits',0,10,189,8,16,4,11
	.byte	'CCTRIG0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'RAMINTM',0,1
	.word	492
	.byte	2,4,2,35,0,11
	.byte	'SETLUDIS',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	492
	.byte	3,0,2,35,0,11
	.byte	'DATM',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,4
	.word	469
	.byte	23,0,2,35,0,0,21
	.byte	'Ifx_SCU_SYSCON_Bits',0,10,198,8,3
	.word	36612
	.byte	10
	.byte	'_Ifx_SCU_TRAPCLR_Bits',0,10,201,8,16,4,11
	.byte	'ESR0T',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	469
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPCLR_Bits',0,10,208,8,3
	.word	36809
	.byte	10
	.byte	'_Ifx_SCU_TRAPDIS_Bits',0,10,211,8,16,4,11
	.byte	'ESR0T',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	469
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPDIS_Bits',0,10,218,8,3
	.word	36962
	.byte	10
	.byte	'_Ifx_SCU_TRAPSET_Bits',0,10,221,8,16,4,11
	.byte	'ESR0T',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	469
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPSET_Bits',0,10,228,8,3
	.word	37115
	.byte	10
	.byte	'_Ifx_SCU_TRAPSTAT_Bits',0,10,231,8,16,4,11
	.byte	'ESR0T',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	469
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPSTAT_Bits',0,10,238,8,3
	.word	37268
	.byte	7
	.byte	'unsigned int',0,4,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON0_Bits',0,10,241,8,16,4,11
	.byte	'ENDINIT',0,4
	.word	37423
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	37423
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	37423
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	37423
	.byte	16,0,2,35,0,0,21
	.byte	'Ifx_SCU_WDTCPU_CON0_Bits',0,10,247,8,3
	.word	37439
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU_CON1_Bits',0,10,250,8,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	2,6,2,35,0,11
	.byte	'IR0',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	492
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_WDTCPU_CON1_Bits',0,10,134,9,3
	.word	37569
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU_SR_Bits',0,10,137,9,16,4,11
	.byte	'AE',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	492
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	509
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_WDTCPU_SR_Bits',0,10,150,9,3
	.word	37807
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON0_Bits',0,10,153,9,16,4,11
	.byte	'ENDINIT',0,4
	.word	37423
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	37423
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	37423
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	37423
	.byte	16,0,2,35,0,0,21
	.byte	'Ifx_SCU_WDTS_CON0_Bits',0,10,159,9,3
	.word	38030
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON1_Bits',0,10,162,9,16,4,11
	.byte	'CLRIRF',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'IR0',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	492
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_WDTS_CON1_Bits',0,10,175,9,3
	.word	38156
	.byte	10
	.byte	'_Ifx_SCU_WDTS_SR_Bits',0,10,178,9,16,4,11
	.byte	'AE',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	492
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	509
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_WDTS_SR_Bits',0,10,191,9,3
	.word	38408
	.byte	12,10,199,9,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19933
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ACCEN0',0,10,204,9,3
	.word	38627
	.byte	12,10,207,9,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20490
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ACCEN1',0,10,212,9,3
	.word	38691
	.byte	12,10,215,9,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20567
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ARSTDIS',0,10,220,9,3
	.word	38755
	.byte	12,10,223,9,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20703
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON0',0,10,228,9,3
	.word	38820
	.byte	12,10,231,9,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20983
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON1',0,10,236,9,3
	.word	38885
	.byte	12,10,239,9,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21221
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON2',0,10,244,9,3
	.word	38950
	.byte	12,10,247,9,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21349
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON3',0,10,252,9,3
	.word	39015
	.byte	12,10,255,9,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21592
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON4',0,10,132,10,3
	.word	39080
	.byte	12,10,135,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21827
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON5',0,10,140,10,3
	.word	39145
	.byte	12,10,143,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21955
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON6',0,10,148,10,3
	.word	39210
	.byte	12,10,151,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22055
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON7',0,10,156,10,3
	.word	39275
	.byte	12,10,159,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22155
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CHIPID',0,10,164,10,3
	.word	39340
	.byte	12,10,167,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22363
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_DTSCON',0,10,172,10,3
	.word	39404
	.byte	12,10,175,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22528
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_DTSLIM',0,10,180,10,3
	.word	39468
	.byte	12,10,183,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22711
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_DTSSTAT',0,10,188,10,3
	.word	39532
	.byte	12,10,191,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22865
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EICR',0,10,196,10,3
	.word	39597
	.byte	12,10,199,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23229
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EIFR',0,10,204,10,3
	.word	39659
	.byte	12,10,207,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23440
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EMSR',0,10,212,10,3
	.word	39721
	.byte	12,10,215,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23692
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ESRCFG',0,10,220,10,3
	.word	39783
	.byte	12,10,223,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23810
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ESROCFG',0,10,228,10,3
	.word	39847
	.byte	12,10,231,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23921
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVR13CON',0,10,236,10,3
	.word	39912
	.byte	12,10,239,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24084
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVR33CON',0,10,244,10,3
	.word	39978
	.byte	12,10,247,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24247
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRADCSTAT',0,10,252,10,3
	.word	40044
	.byte	12,10,255,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24405
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRDVSTAT',0,10,132,11,3
	.word	40112
	.byte	12,10,135,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24570
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRMONCTRL',0,10,140,11,3
	.word	40179
	.byte	12,10,143,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24899
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVROSCCTRL',0,10,148,11,3
	.word	40247
	.byte	12,10,151,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25120
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVROVMON',0,10,156,11,3
	.word	40315
	.byte	12,10,159,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25283
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRRSTCON',0,10,164,11,3
	.word	40381
	.byte	12,10,167,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25555
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF1',0,10,172,11,3
	.word	40448
	.byte	12,10,175,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25708
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF2',0,10,180,11,3
	.word	40517
	.byte	12,10,183,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25864
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF3',0,10,188,11,3
	.word	40586
	.byte	12,10,191,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26026
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF4',0,10,196,11,3
	.word	40655
	.byte	12,10,199,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26169
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF5',0,10,204,11,3
	.word	40724
	.byte	12,10,207,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26334
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF6',0,10,212,11,3
	.word	40793
	.byte	12,10,215,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26479
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCTRL1',0,10,220,11,3
	.word	40862
	.byte	12,10,223,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26660
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCTRL2',0,10,228,11,3
	.word	40930
	.byte	12,10,231,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26834
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCTRL3',0,10,236,11,3
	.word	40998
	.byte	12,10,239,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26994
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCTRL4',0,10,244,11,3
	.word	41066
	.byte	12,10,247,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27138
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSTAT',0,10,252,11,3
	.word	41134
	.byte	12,10,255,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27412
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRTRIM',0,10,132,12,3
	.word	41199
	.byte	12,10,135,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27551
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRUVMON',0,10,140,12,3
	.word	41264
	.byte	12,10,143,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27714
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EXTCON',0,10,148,12,3
	.word	41330
	.byte	12,10,151,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27932
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_FDR',0,10,156,12,3
	.word	41394
	.byte	12,10,159,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28095
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_FMR',0,10,164,12,3
	.word	41455
	.byte	12,10,167,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28431
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ID',0,10,172,12,3
	.word	41516
	.byte	12,10,175,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28538
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_IGCR',0,10,180,12,3
	.word	41576
	.byte	12,10,183,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28990
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_IN',0,10,188,12,3
	.word	41638
	.byte	12,10,191,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29089
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_IOCR',0,10,196,12,3
	.word	41698
	.byte	12,10,199,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29239
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_LBISTCTRL0',0,10,204,12,3
	.word	41760
	.byte	12,10,207,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29388
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_LBISTCTRL1',0,10,212,12,3
	.word	41828
	.byte	12,10,215,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29549
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_LBISTCTRL2',0,10,220,12,3
	.word	41896
	.byte	12,10,223,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29679
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_LCLCON',0,10,228,12,3
	.word	41964
	.byte	12,10,231,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29811
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_LCLTEST',0,10,236,12,3
	.word	42028
	.byte	12,10,239,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29926
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_MANID',0,10,244,12,3
	.word	42093
	.byte	12,10,247,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30037
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_OMR',0,10,252,12,3
	.word	42156
	.byte	12,10,255,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30195
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_OSCCON',0,10,132,13,3
	.word	42217
	.byte	12,10,135,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30607
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_OUT',0,10,140,13,3
	.word	42281
	.byte	12,10,143,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30708
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_OVCCON',0,10,148,13,3
	.word	42342
	.byte	12,10,151,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30975
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_OVCENABLE',0,10,156,13,3
	.word	42406
	.byte	12,10,159,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31111
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PDISC',0,10,164,13,3
	.word	42473
	.byte	12,10,167,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31222
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PDR',0,10,172,13,3
	.word	42536
	.byte	12,10,175,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31355
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PDRR',0,10,180,13,3
	.word	42597
	.byte	12,10,183,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31558
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLCON0',0,10,188,13,3
	.word	42659
	.byte	12,10,191,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31914
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLCON1',0,10,196,13,3
	.word	42724
	.byte	12,10,199,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32092
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLCON2',0,10,204,13,3
	.word	42789
	.byte	12,10,207,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32192
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLERAYCON0',0,10,212,13,3
	.word	42854
	.byte	12,10,215,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32562
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLERAYCON1',0,10,220,13,3
	.word	42923
	.byte	12,10,223,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32748
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLERAYSTAT',0,10,228,13,3
	.word	42992
	.byte	12,10,231,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32946
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLSTAT',0,10,236,13,3
	.word	43061
	.byte	12,10,239,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33179
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMCSR',0,10,244,13,3
	.word	43126
	.byte	12,10,247,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33331
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMSWCR0',0,10,252,13,3
	.word	43189
	.byte	12,10,255,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33898
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMSWCR1',0,10,132,14,3
	.word	43254
	.byte	12,10,135,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34192
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMSWCR2',0,10,140,14,3
	.word	43319
	.byte	12,10,143,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34470
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMSWSTAT',0,10,148,14,3
	.word	43384
	.byte	12,10,151,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34966
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMSWSTATCLR',0,10,156,14,3
	.word	43450
	.byte	12,10,159,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35488
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_RSTCON',0,10,164,14,3
	.word	43519
	.byte	12,10,167,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35279
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_RSTCON2',0,10,172,14,3
	.word	43583
	.byte	12,10,175,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35699
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_RSTSTAT',0,10,180,14,3
	.word	43648
	.byte	12,10,183,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36131
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_SAFECON',0,10,188,14,3
	.word	43713
	.byte	12,10,191,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36227
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_STSTAT',0,10,196,14,3
	.word	43778
	.byte	12,10,199,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36487
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_SWRSTCON',0,10,204,14,3
	.word	43842
	.byte	12,10,207,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36612
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_SYSCON',0,10,212,14,3
	.word	43908
	.byte	12,10,215,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36809
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPCLR',0,10,220,14,3
	.word	43972
	.byte	12,10,223,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36962
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPDIS',0,10,228,14,3
	.word	44037
	.byte	12,10,231,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37115
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPSET',0,10,236,14,3
	.word	44102
	.byte	12,10,239,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37268
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPSTAT',0,10,244,14,3
	.word	44167
	.byte	12,10,247,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37439
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTCPU_CON0',0,10,252,14,3
	.word	44233
	.byte	12,10,255,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37569
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTCPU_CON1',0,10,132,15,3
	.word	44302
	.byte	12,10,135,15,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37807
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTCPU_SR',0,10,140,15,3
	.word	44371
	.byte	12,10,143,15,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38030
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTS_CON0',0,10,148,15,3
	.word	44438
	.byte	12,10,151,15,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38156
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTS_CON1',0,10,156,15,3
	.word	44505
	.byte	12,10,159,15,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38408
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTS_SR',0,10,164,15,3
	.word	44572
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU',0,10,175,15,25,12,13
	.byte	'CON0',0
	.word	44233
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	44302
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	44371
	.byte	4,2,35,8,0,16
	.word	44637
	.byte	21
	.byte	'Ifx_SCU_WDTCPU',0,10,180,15,3
	.word	44700
	.byte	10
	.byte	'_Ifx_SCU_WDTS',0,10,183,15,25,12,13
	.byte	'CON0',0
	.word	44438
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	44505
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	44572
	.byte	4,2,35,8,0,16
	.word	44729
	.byte	21
	.byte	'Ifx_SCU_WDTS',0,10,188,15,3
	.word	44790
	.byte	17,3,83,9,1,18
	.byte	'IfxPort_InputMode_undefined',0,127,18
	.byte	'IfxPort_InputMode_noPullDevice',0,0,18
	.byte	'IfxPort_InputMode_pullDown',0,8,18
	.byte	'IfxPort_InputMode_pullUp',0,16,0,21
	.byte	'IfxPort_InputMode',0,3,89,3
	.word	44817
	.byte	17,3,120,9,1,18
	.byte	'IfxPort_OutputIdx_general',0,128,1,18
	.byte	'IfxPort_OutputIdx_alt1',0,136,1,18
	.byte	'IfxPort_OutputIdx_alt2',0,144,1,18
	.byte	'IfxPort_OutputIdx_alt3',0,152,1,18
	.byte	'IfxPort_OutputIdx_alt4',0,160,1,18
	.byte	'IfxPort_OutputIdx_alt5',0,168,1,18
	.byte	'IfxPort_OutputIdx_alt6',0,176,1,18
	.byte	'IfxPort_OutputIdx_alt7',0,184,1,0,21
	.byte	'IfxPort_OutputIdx',0,3,130,1,3
	.word	44968
	.byte	17,3,134,1,9,1,18
	.byte	'IfxPort_OutputMode_pushPull',0,128,1,18
	.byte	'IfxPort_OutputMode_openDrain',0,192,1,0,21
	.byte	'IfxPort_OutputMode',0,3,138,1,3
	.word	45212
	.byte	17,3,144,1,9,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed1',0,0,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed2',0,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed3',0,2,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed4',0,3,18
	.byte	'IfxPort_PadDriver_lvdsSpeed1',0,4,18
	.byte	'IfxPort_PadDriver_lvdsSpeed2',0,5,18
	.byte	'IfxPort_PadDriver_lvdsSpeed3',0,6,18
	.byte	'IfxPort_PadDriver_lvdsSpeed4',0,7,18
	.byte	'IfxPort_PadDriver_ttlSpeed1',0,8,18
	.byte	'IfxPort_PadDriver_ttlSpeed2',0,9,18
	.byte	'IfxPort_PadDriver_ttlSpeed3',0,10,18
	.byte	'IfxPort_PadDriver_ttlSpeed4',0,11,0,21
	.byte	'IfxPort_PadDriver',0,3,158,1,3
	.word	45310
	.byte	21
	.byte	'IfxPort_State',0,3,178,1,3
	.word	8289
	.byte	23,3,190,1,9,8,13
	.byte	'port',0
	.word	8284
	.byte	4,2,35,0,13
	.byte	'pinIndex',0
	.word	492
	.byte	1,2,35,4,0,21
	.byte	'IfxPort_Pin',0,3,194,1,3
	.word	45775
	.byte	16
	.word	17554
	.byte	3
	.word	45835
	.byte	23,11,59,15,16,13
	.byte	'module',0
	.word	45840
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	45775
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	18335
	.byte	1,2,35,12,0,24
	.word	45845
	.byte	21
	.byte	'IfxMsc_Inj_In',0,11,64,3
	.word	45896
	.byte	23,11,67,15,16,13
	.byte	'module',0
	.word	45840
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	45775
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	18335
	.byte	1,2,35,12,0,24
	.word	45923
	.byte	21
	.byte	'IfxMsc_Sdi_In',0,11,72,3
	.word	45974
	.byte	23,11,75,15,20,13
	.byte	'module',0
	.word	45840
	.byte	4,2,35,0,13
	.byte	'target',0
	.word	492
	.byte	1,2,35,4,13
	.byte	'pin',0
	.word	45775
	.byte	8,2,35,8,13
	.byte	'select',0
	.word	44968
	.byte	1,2,35,16,0,24
	.word	46001
	.byte	21
	.byte	'IfxMsc_En_Out',0,11,81,3
	.word	46068
	.byte	23,11,84,15,16,13
	.byte	'module',0
	.word	45840
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	45775
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	44968
	.byte	1,2,35,12,0,24
	.word	46095
	.byte	21
	.byte	'IfxMsc_Fclp_Out',0,11,89,3
	.word	46146
	.byte	23,11,92,15,16,13
	.byte	'module',0
	.word	45840
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	45775
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	44968
	.byte	1,2,35,12,0,24
	.word	46175
	.byte	21
	.byte	'IfxMsc_Fcln_Out',0,11,97,3
	.word	46226
	.byte	23,11,100,15,16,13
	.byte	'module',0
	.word	45840
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	45775
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	44968
	.byte	1,2,35,12,0,24
	.word	46255
	.byte	21
	.byte	'IfxMsc_Sop_Out',0,11,105,3
	.word	46306
	.byte	23,11,108,15,16,13
	.byte	'module',0
	.word	45840
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	45775
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	44968
	.byte	1,2,35,12,0,24
	.word	46334
	.byte	21
	.byte	'IfxMsc_Son_Out',0,11,113,3
	.word	46385
.L128:
	.byte	24
	.word	46001
.L129:
	.byte	24
	.word	46001
.L130:
	.byte	24
	.word	46001
.L131:
	.byte	24
	.word	46001
.L132:
	.byte	24
	.word	46001
.L133:
	.byte	24
	.word	46001
.L134:
	.byte	24
	.word	46001
.L135:
	.byte	24
	.word	46001
.L136:
	.byte	24
	.word	46001
.L137:
	.byte	24
	.word	46001
.L138:
	.byte	24
	.word	46001
.L139:
	.byte	24
	.word	46001
.L140:
	.byte	24
	.word	46001
.L141:
	.byte	24
	.word	46001
.L142:
	.byte	24
	.word	46001
.L143:
	.byte	24
	.word	46001
.L144:
	.byte	24
	.word	46001
.L145:
	.byte	24
	.word	46001
.L146:
	.byte	24
	.word	46001
.L147:
	.byte	24
	.word	46001
.L148:
	.byte	24
	.word	46001
.L149:
	.byte	24
	.word	46001
.L150:
	.byte	24
	.word	46001
.L151:
	.byte	24
	.word	46001
.L152:
	.byte	24
	.word	46001
.L153:
	.byte	24
	.word	46001
.L154:
	.byte	24
	.word	46001
.L155:
	.byte	24
	.word	46001
.L156:
	.byte	24
	.word	46175
.L157:
	.byte	24
	.word	46175
.L158:
	.byte	24
	.word	46175
.L159:
	.byte	24
	.word	46175
.L160:
	.byte	24
	.word	46095
.L161:
	.byte	24
	.word	46095
.L162:
	.byte	24
	.word	46095
.L163:
	.byte	24
	.word	46095
.L164:
	.byte	24
	.word	45845
.L165:
	.byte	24
	.word	45845
.L166:
	.byte	24
	.word	45845
.L167:
	.byte	24
	.word	45845
.L168:
	.byte	24
	.word	45923
.L169:
	.byte	24
	.word	45923
.L170:
	.byte	24
	.word	45923
.L171:
	.byte	24
	.word	45923
.L172:
	.byte	24
	.word	45923
.L173:
	.byte	24
	.word	45923
.L174:
	.byte	24
	.word	45923
.L175:
	.byte	24
	.word	46334
.L176:
	.byte	24
	.word	46334
.L177:
	.byte	24
	.word	46334
.L178:
	.byte	24
	.word	46334
.L179:
	.byte	24
	.word	46255
.L180:
	.byte	24
	.word	46255
.L181:
	.byte	24
	.word	46255
	.byte	24
	.word	46001
	.byte	3
	.word	46683
	.byte	14,24
	.word	46688
	.byte	15,5,0,14,96
	.word	46693
	.byte	15,3,0
.L182:
	.byte	14,192,1
	.word	46702
	.byte	15,1,0,24
	.word	46175
	.byte	3
	.word	46721
	.byte	14,8
	.word	46726
	.byte	15,1,0
.L183:
	.byte	14,16
	.word	46731
	.byte	15,1,0,24
	.word	46095
	.byte	3
	.word	46749
	.byte	14,12
	.word	46754
	.byte	15,2,0
.L184:
	.byte	14,24
	.word	46759
	.byte	15,1,0,24
	.word	45845
	.byte	3
	.word	46777
	.byte	14,8
	.word	46782
	.byte	15,1,0
.L185:
	.byte	14,16
	.word	46787
	.byte	15,1,0,24
	.word	45923
	.byte	3
	.word	46805
	.byte	14,16
	.word	46810
	.byte	15,3,0
.L186:
	.byte	14,32
	.word	46815
	.byte	15,1,0,24
	.word	46334
	.byte	3
	.word	46833
	.byte	14,8
	.word	46838
	.byte	15,1,0
.L187:
	.byte	14,16
	.word	46843
	.byte	15,1,0,24
	.word	46255
	.byte	3
	.word	46861
	.byte	14,8
	.word	46866
	.byte	15,1,0
.L188:
	.byte	14,16
	.word	46871
	.byte	15,1,0,0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L4:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,21,0,54,15,39,12,0,0,3,15,0,73,19,0,0,4,46,1,3,8,32,13
	.byte	58,15,59,15,57,15,54,15,39,12,0,0,5,5,0,3,8,58,15,59,15,57,15,73,19,0,0,6,11,0,0,0,7,36,0,3,8,11,15,62
	.byte	15,0,0,8,46,1,3,8,32,13,58,15,59,15,57,15,73,19,54,15,39,12,0,0,9,59,0,3,8,0,0,10,19,1,3,8,58,15,59,15
	.byte	57,15,11,15,0,0,11,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,12,23,1,58,15,59,15,57,15,11,15,0,0,13,13
	.byte	0,3,8,73,19,11,15,56,9,0,0,14,1,1,11,15,73,19,0,0,15,33,0,47,15,0,0,16,53,0,73,19,0,0,17,4,1,58,15,59
	.byte	15,57,15,11,15,0,0,18,40,0,3,8,28,13,0,0,19,46,1,49,19,0,0,20,5,0,49,19,0,0,21,22,0,3,8,58,15,59,15,57
	.byte	15,73,19,0,0,22,21,0,54,15,0,0,23,19,1,58,15,59,15,57,15,11,15,0,0,24,38,0,73,19,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L5:
	.word	.L190-.L189
.L189:
	.half	3
	.word	.L192-.L191
.L191:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg',0
	.byte	0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_Intrinsics.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Port\\Std\\IfxPort.h',0
	.byte	0,0,0
	.byte	'IfxPort_regdef.h',0,1,0,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMsc_PinMap.c',0,0,0,0
	.byte	'IfxMsc_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Platform_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Ifx_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxScu_cfg.h',0
	.byte	0,0,0
	.byte	'IfxScu_regdef.h',0,1,0,0
	.byte	'..\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\_PinMap\\IfxMsc_PinMap.h',0,0,0,0,0
.L192:
.L190:
	.sdecl	'.debug_info',debug,cluster('IfxMsc0_EN0_P10_2_OUT')
	.sect	'.debug_info'
.L6:
	.word	272
	.half	3
	.word	.L7
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMsc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMsc0_EN0_P10_2_OUT',0,5,48,15
	.word	.L128
	.byte	1,5,3
	.word	IfxMsc0_EN0_P10_2_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMsc0_EN0_P10_2_OUT')
	.sect	'.debug_abbrev'
.L7:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMsc0_EN0_P10_3_OUT')
	.sect	'.debug_info'
.L8:
	.word	272
	.half	3
	.word	.L9
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMsc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMsc0_EN0_P10_3_OUT',0,5,49,15
	.word	.L129
	.byte	1,5,3
	.word	IfxMsc0_EN0_P10_3_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMsc0_EN0_P10_3_OUT')
	.sect	'.debug_abbrev'
.L9:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMsc0_EN0_P10_4_OUT')
	.sect	'.debug_info'
.L10:
	.word	272
	.half	3
	.word	.L11
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMsc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMsc0_EN0_P10_4_OUT',0,5,50,15
	.word	.L130
	.byte	1,5,3
	.word	IfxMsc0_EN0_P10_4_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMsc0_EN0_P10_4_OUT')
	.sect	'.debug_abbrev'
.L11:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMsc0_EN0_P11_11_OUT')
	.sect	'.debug_info'
.L12:
	.word	273
	.half	3
	.word	.L13
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMsc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMsc0_EN0_P11_11_OUT',0,5,51,15
	.word	.L131
	.byte	1,5,3
	.word	IfxMsc0_EN0_P11_11_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMsc0_EN0_P11_11_OUT')
	.sect	'.debug_abbrev'
.L13:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMsc0_EN0_P14_10_OUT')
	.sect	'.debug_info'
.L14:
	.word	273
	.half	3
	.word	.L15
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMsc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMsc0_EN0_P14_10_OUT',0,5,52,15
	.word	.L132
	.byte	1,5,3
	.word	IfxMsc0_EN0_P14_10_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMsc0_EN0_P14_10_OUT')
	.sect	'.debug_abbrev'
.L15:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMsc0_EN0_P15_5_OUT')
	.sect	'.debug_info'
.L16:
	.word	272
	.half	3
	.word	.L17
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMsc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMsc0_EN0_P15_5_OUT',0,5,53,15
	.word	.L133
	.byte	1,5,3
	.word	IfxMsc0_EN0_P15_5_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMsc0_EN0_P15_5_OUT')
	.sect	'.debug_abbrev'
.L17:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMsc0_EN1_P10_1_OUT')
	.sect	'.debug_info'
.L18:
	.word	272
	.half	3
	.word	.L19
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMsc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMsc0_EN1_P10_1_OUT',0,5,54,15
	.word	.L134
	.byte	1,5,3
	.word	IfxMsc0_EN1_P10_1_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMsc0_EN1_P10_1_OUT')
	.sect	'.debug_abbrev'
.L19:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMsc0_EN1_P11_2_OUT')
	.sect	'.debug_info'
.L20:
	.word	272
	.half	3
	.word	.L21
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMsc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMsc0_EN1_P11_2_OUT',0,5,55,15
	.word	.L135
	.byte	1,5,3
	.word	IfxMsc0_EN1_P11_2_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMsc0_EN1_P11_2_OUT')
	.sect	'.debug_abbrev'
.L21:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMsc0_EN1_P13_0_OUT')
	.sect	'.debug_info'
.L22:
	.word	272
	.half	3
	.word	.L23
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMsc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMsc0_EN1_P13_0_OUT',0,5,56,15
	.word	.L136
	.byte	1,5,3
	.word	IfxMsc0_EN1_P13_0_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMsc0_EN1_P13_0_OUT')
	.sect	'.debug_abbrev'
.L23:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMsc0_EN1_P14_9_OUT')
	.sect	'.debug_info'
.L24:
	.word	272
	.half	3
	.word	.L25
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMsc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMsc0_EN1_P14_9_OUT',0,5,57,15
	.word	.L137
	.byte	1,5,3
	.word	IfxMsc0_EN1_P14_9_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMsc0_EN1_P14_9_OUT')
	.sect	'.debug_abbrev'
.L25:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMsc0_EN1_P15_3_OUT')
	.sect	'.debug_info'
.L26:
	.word	272
	.half	3
	.word	.L27
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMsc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMsc0_EN1_P15_3_OUT',0,5,58,15
	.word	.L138
	.byte	1,5,3
	.word	IfxMsc0_EN1_P15_3_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMsc0_EN1_P15_3_OUT')
	.sect	'.debug_abbrev'
.L27:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMsc0_END2_P10_2_OUT')
	.sect	'.debug_info'
.L28:
	.word	273
	.half	3
	.word	.L29
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMsc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMsc0_END2_P10_2_OUT',0,5,59,15
	.word	.L139
	.byte	1,5,3
	.word	IfxMsc0_END2_P10_2_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMsc0_END2_P10_2_OUT')
	.sect	'.debug_abbrev'
.L29:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMsc0_END2_P10_3_OUT')
	.sect	'.debug_info'
.L30:
	.word	273
	.half	3
	.word	.L31
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMsc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMsc0_END2_P10_3_OUT',0,5,60,15
	.word	.L140
	.byte	1,5,3
	.word	IfxMsc0_END2_P10_3_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMsc0_END2_P10_3_OUT')
	.sect	'.debug_abbrev'
.L31:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMsc0_END2_P10_4_OUT')
	.sect	'.debug_info'
.L32:
	.word	273
	.half	3
	.word	.L33
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMsc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMsc0_END2_P10_4_OUT',0,5,61,15
	.word	.L141
	.byte	1,5,3
	.word	IfxMsc0_END2_P10_4_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMsc0_END2_P10_4_OUT')
	.sect	'.debug_abbrev'
.L33:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMsc0_END2_P11_11_OUT')
	.sect	'.debug_info'
.L34:
	.word	274
	.half	3
	.word	.L35
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMsc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMsc0_END2_P11_11_OUT',0,5,62,15
	.word	.L142
	.byte	1,5,3
	.word	IfxMsc0_END2_P11_11_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMsc0_END2_P11_11_OUT')
	.sect	'.debug_abbrev'
.L35:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMsc0_END2_P14_10_OUT')
	.sect	'.debug_info'
.L36:
	.word	274
	.half	3
	.word	.L37
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMsc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMsc0_END2_P14_10_OUT',0,5,63,15
	.word	.L143
	.byte	1,5,3
	.word	IfxMsc0_END2_P14_10_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMsc0_END2_P14_10_OUT')
	.sect	'.debug_abbrev'
.L37:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMsc0_END2_P15_5_OUT')
	.sect	'.debug_info'
.L38:
	.word	273
	.half	3
	.word	.L39
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMsc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMsc0_END2_P15_5_OUT',0,5,64,15
	.word	.L144
	.byte	1,5,3
	.word	IfxMsc0_END2_P15_5_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMsc0_END2_P15_5_OUT')
	.sect	'.debug_abbrev'
.L39:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMsc0_END3_P10_1_OUT')
	.sect	'.debug_info'
.L40:
	.word	273
	.half	3
	.word	.L41
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMsc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMsc0_END3_P10_1_OUT',0,5,65,15
	.word	.L145
	.byte	1,5,3
	.word	IfxMsc0_END3_P10_1_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMsc0_END3_P10_1_OUT')
	.sect	'.debug_abbrev'
.L41:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMsc0_END3_P11_2_OUT')
	.sect	'.debug_info'
.L42:
	.word	273
	.half	3
	.word	.L43
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMsc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMsc0_END3_P11_2_OUT',0,5,66,15
	.word	.L146
	.byte	1,5,3
	.word	IfxMsc0_END3_P11_2_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMsc0_END3_P11_2_OUT')
	.sect	'.debug_abbrev'
.L43:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMsc0_END3_P13_0_OUT')
	.sect	'.debug_info'
.L44:
	.word	273
	.half	3
	.word	.L45
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMsc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMsc0_END3_P13_0_OUT',0,5,67,15
	.word	.L147
	.byte	1,5,3
	.word	IfxMsc0_END3_P13_0_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMsc0_END3_P13_0_OUT')
	.sect	'.debug_abbrev'
.L45:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMsc0_END3_P14_9_OUT')
	.sect	'.debug_info'
.L46:
	.word	273
	.half	3
	.word	.L47
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMsc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMsc0_END3_P14_9_OUT',0,5,68,15
	.word	.L148
	.byte	1,5,3
	.word	IfxMsc0_END3_P14_9_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMsc0_END3_P14_9_OUT')
	.sect	'.debug_abbrev'
.L47:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMsc0_END3_P15_3_OUT')
	.sect	'.debug_info'
.L48:
	.word	273
	.half	3
	.word	.L49
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMsc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMsc0_END3_P15_3_OUT',0,5,69,15
	.word	.L149
	.byte	1,5,3
	.word	IfxMsc0_END3_P15_3_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMsc0_END3_P15_3_OUT')
	.sect	'.debug_abbrev'
.L49:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMsc1_EN0_P23_4_OUT')
	.sect	'.debug_info'
.L50:
	.word	272
	.half	3
	.word	.L51
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMsc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMsc1_EN0_P23_4_OUT',0,5,70,15
	.word	.L150
	.byte	1,5,3
	.word	IfxMsc1_EN0_P23_4_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMsc1_EN0_P23_4_OUT')
	.sect	'.debug_abbrev'
.L51:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMsc1_EN0_P32_4_OUT')
	.sect	'.debug_info'
.L52:
	.word	272
	.half	3
	.word	.L53
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMsc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMsc1_EN0_P32_4_OUT',0,5,71,15
	.word	.L151
	.byte	1,5,3
	.word	IfxMsc1_EN0_P32_4_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMsc1_EN0_P32_4_OUT')
	.sect	'.debug_abbrev'
.L53:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMsc1_EN1_P23_5_OUT')
	.sect	'.debug_info'
.L54:
	.word	272
	.half	3
	.word	.L55
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMsc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMsc1_EN1_P23_5_OUT',0,5,72,15
	.word	.L152
	.byte	1,5,3
	.word	IfxMsc1_EN1_P23_5_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMsc1_EN1_P23_5_OUT')
	.sect	'.debug_abbrev'
.L55:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMsc1_END2_P23_4_OUT')
	.sect	'.debug_info'
.L56:
	.word	273
	.half	3
	.word	.L57
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMsc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMsc1_END2_P23_4_OUT',0,5,73,15
	.word	.L153
	.byte	1,5,3
	.word	IfxMsc1_END2_P23_4_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMsc1_END2_P23_4_OUT')
	.sect	'.debug_abbrev'
.L57:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMsc1_END2_P32_4_OUT')
	.sect	'.debug_info'
.L58:
	.word	273
	.half	3
	.word	.L59
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMsc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMsc1_END2_P32_4_OUT',0,5,74,15
	.word	.L154
	.byte	1,5,3
	.word	IfxMsc1_END2_P32_4_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMsc1_END2_P32_4_OUT')
	.sect	'.debug_abbrev'
.L59:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMsc1_END3_P23_5_OUT')
	.sect	'.debug_info'
.L60:
	.word	273
	.half	3
	.word	.L61
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMsc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMsc1_END3_P23_5_OUT',0,5,75,15
	.word	.L155
	.byte	1,5,3
	.word	IfxMsc1_END3_P23_5_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMsc1_END3_P23_5_OUT')
	.sect	'.debug_abbrev'
.L61:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMsc0_FCLND_P13_0_OUT')
	.sect	'.debug_info'
.L62:
	.word	274
	.half	3
	.word	.L63
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMsc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMsc0_FCLND_P13_0_OUT',0,5,76,17
	.word	.L156
	.byte	1,5,3
	.word	IfxMsc0_FCLND_P13_0_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMsc0_FCLND_P13_0_OUT')
	.sect	'.debug_abbrev'
.L63:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMsc0_FCLN_P13_0_OUT')
	.sect	'.debug_info'
.L64:
	.word	273
	.half	3
	.word	.L65
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMsc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMsc0_FCLN_P13_0_OUT',0,5,77,17
	.word	.L157
	.byte	1,5,3
	.word	IfxMsc0_FCLN_P13_0_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMsc0_FCLN_P13_0_OUT')
	.sect	'.debug_abbrev'
.L65:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMsc1_FCLND_P22_0_OUT')
	.sect	'.debug_info'
.L66:
	.word	274
	.half	3
	.word	.L67
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMsc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMsc1_FCLND_P22_0_OUT',0,5,78,17
	.word	.L158
	.byte	1,5,3
	.word	IfxMsc1_FCLND_P22_0_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMsc1_FCLND_P22_0_OUT')
	.sect	'.debug_abbrev'
.L67:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMsc1_FCLN_P22_0_OUT')
	.sect	'.debug_info'
.L68:
	.word	273
	.half	3
	.word	.L69
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMsc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMsc1_FCLN_P22_0_OUT',0,5,79,17
	.word	.L159
	.byte	1,5,3
	.word	IfxMsc1_FCLN_P22_0_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMsc1_FCLN_P22_0_OUT')
	.sect	'.debug_abbrev'
.L69:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMsc0_FCLP_P11_6_OUT')
	.sect	'.debug_info'
.L70:
	.word	273
	.half	3
	.word	.L71
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMsc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMsc0_FCLP_P11_6_OUT',0,5,80,17
	.word	.L160
	.byte	1,5,3
	.word	IfxMsc0_FCLP_P11_6_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMsc0_FCLP_P11_6_OUT')
	.sect	'.debug_abbrev'
.L71:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMsc0_FCLP_P13_1_OUT')
	.sect	'.debug_info'
.L72:
	.word	273
	.half	3
	.word	.L73
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMsc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMsc0_FCLP_P13_1_OUT',0,5,81,17
	.word	.L161
	.byte	1,5,3
	.word	IfxMsc0_FCLP_P13_1_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMsc0_FCLP_P13_1_OUT')
	.sect	'.debug_abbrev'
.L73:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMsc0_FCLP_P13_2_OUT')
	.sect	'.debug_info'
.L74:
	.word	273
	.half	3
	.word	.L75
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMsc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMsc0_FCLP_P13_2_OUT',0,5,82,17
	.word	.L162
	.byte	1,5,3
	.word	IfxMsc0_FCLP_P13_2_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMsc0_FCLP_P13_2_OUT')
	.sect	'.debug_abbrev'
.L75:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMsc1_FCLP_P22_1_OUT')
	.sect	'.debug_info'
.L76:
	.word	273
	.half	3
	.word	.L77
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMsc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMsc1_FCLP_P22_1_OUT',0,5,83,17
	.word	.L163
	.byte	1,5,3
	.word	IfxMsc1_FCLP_P22_1_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMsc1_FCLP_P22_1_OUT')
	.sect	'.debug_abbrev'
.L77:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMsc0_INJ0_P00_0_IN')
	.sect	'.debug_info'
.L78:
	.word	272
	.half	3
	.word	.L79
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMsc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMsc0_INJ0_P00_0_IN',0,5,84,15
	.word	.L164
	.byte	1,5,3
	.word	IfxMsc0_INJ0_P00_0_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMsc0_INJ0_P00_0_IN')
	.sect	'.debug_abbrev'
.L79:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMsc0_INJ1_P10_5_IN')
	.sect	'.debug_info'
.L80:
	.word	272
	.half	3
	.word	.L81
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMsc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMsc0_INJ1_P10_5_IN',0,5,85,15
	.word	.L165
	.byte	1,5,3
	.word	IfxMsc0_INJ1_P10_5_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMsc0_INJ1_P10_5_IN')
	.sect	'.debug_abbrev'
.L81:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMsc1_INJ0_P23_3_IN')
	.sect	'.debug_info'
.L82:
	.word	272
	.half	3
	.word	.L83
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMsc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMsc1_INJ0_P23_3_IN',0,5,86,15
	.word	.L166
	.byte	1,5,3
	.word	IfxMsc1_INJ0_P23_3_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMsc1_INJ0_P23_3_IN')
	.sect	'.debug_abbrev'
.L83:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMsc1_INJ1_P33_13_IN')
	.sect	'.debug_info'
.L84:
	.word	273
	.half	3
	.word	.L85
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMsc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMsc1_INJ1_P33_13_IN',0,5,87,15
	.word	.L167
	.byte	1,5,3
	.word	IfxMsc1_INJ1_P33_13_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMsc1_INJ1_P33_13_IN')
	.sect	'.debug_abbrev'
.L85:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMsc0_SDI0_P11_10_IN')
	.sect	'.debug_info'
.L86:
	.word	273
	.half	3
	.word	.L87
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMsc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMsc0_SDI0_P11_10_IN',0,5,88,15
	.word	.L168
	.byte	1,5,3
	.word	IfxMsc0_SDI0_P11_10_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMsc0_SDI0_P11_10_IN')
	.sect	'.debug_abbrev'
.L87:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMsc0_SDI1_P10_2_IN')
	.sect	'.debug_info'
.L88:
	.word	272
	.half	3
	.word	.L89
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMsc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMsc0_SDI1_P10_2_IN',0,5,89,15
	.word	.L169
	.byte	1,5,3
	.word	IfxMsc0_SDI1_P10_2_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMsc0_SDI1_P10_2_IN')
	.sect	'.debug_abbrev'
.L89:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMsc0_SDI2_P14_3_IN')
	.sect	'.debug_info'
.L90:
	.word	272
	.half	3
	.word	.L91
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMsc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMsc0_SDI2_P14_3_IN',0,5,90,15
	.word	.L170
	.byte	1,5,3
	.word	IfxMsc0_SDI2_P14_3_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMsc0_SDI2_P14_3_IN')
	.sect	'.debug_abbrev'
.L91:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMsc0_SDI3_P11_3_IN')
	.sect	'.debug_info'
.L92:
	.word	272
	.half	3
	.word	.L93
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMsc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMsc0_SDI3_P11_3_IN',0,5,91,15
	.word	.L171
	.byte	1,5,3
	.word	IfxMsc0_SDI3_P11_3_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMsc0_SDI3_P11_3_IN')
	.sect	'.debug_abbrev'
.L93:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMsc1_SDI0_P23_1_IN')
	.sect	'.debug_info'
.L94:
	.word	272
	.half	3
	.word	.L95
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMsc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMsc1_SDI0_P23_1_IN',0,5,92,15
	.word	.L172
	.byte	1,5,3
	.word	IfxMsc1_SDI0_P23_1_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMsc1_SDI0_P23_1_IN')
	.sect	'.debug_abbrev'
.L95:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMsc1_SDI1_P02_3_IN')
	.sect	'.debug_info'
.L96:
	.word	272
	.half	3
	.word	.L97
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMsc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMsc1_SDI1_P02_3_IN',0,5,93,15
	.word	.L173
	.byte	1,5,3
	.word	IfxMsc1_SDI1_P02_3_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMsc1_SDI1_P02_3_IN')
	.sect	'.debug_abbrev'
.L97:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMsc1_SDI2_P32_4_IN')
	.sect	'.debug_info'
.L98:
	.word	272
	.half	3
	.word	.L99
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMsc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMsc1_SDI2_P32_4_IN',0,5,94,15
	.word	.L174
	.byte	1,5,3
	.word	IfxMsc1_SDI2_P32_4_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMsc1_SDI2_P32_4_IN')
	.sect	'.debug_abbrev'
.L99:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMsc0_SOND_P13_2_OUT')
	.sect	'.debug_info'
.L100:
	.word	273
	.half	3
	.word	.L101
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMsc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMsc0_SOND_P13_2_OUT',0,5,95,16
	.word	.L175
	.byte	1,5,3
	.word	IfxMsc0_SOND_P13_2_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMsc0_SOND_P13_2_OUT')
	.sect	'.debug_abbrev'
.L101:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMsc0_SON_P13_2_OUT')
	.sect	'.debug_info'
.L102:
	.word	272
	.half	3
	.word	.L103
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMsc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMsc0_SON_P13_2_OUT',0,5,96,16
	.word	.L176
	.byte	1,5,3
	.word	IfxMsc0_SON_P13_2_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMsc0_SON_P13_2_OUT')
	.sect	'.debug_abbrev'
.L103:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMsc1_SOND_P22_2_OUT')
	.sect	'.debug_info'
.L104:
	.word	273
	.half	3
	.word	.L105
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMsc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMsc1_SOND_P22_2_OUT',0,5,97,16
	.word	.L177
	.byte	1,5,3
	.word	IfxMsc1_SOND_P22_2_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMsc1_SOND_P22_2_OUT')
	.sect	'.debug_abbrev'
.L105:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMsc1_SON_P22_2_OUT')
	.sect	'.debug_info'
.L106:
	.word	272
	.half	3
	.word	.L107
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMsc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMsc1_SON_P22_2_OUT',0,5,98,16
	.word	.L178
	.byte	1,5,3
	.word	IfxMsc1_SON_P22_2_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMsc1_SON_P22_2_OUT')
	.sect	'.debug_abbrev'
.L107:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMsc0_SOP_P11_9_OUT')
	.sect	'.debug_info'
.L108:
	.word	272
	.half	3
	.word	.L109
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMsc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMsc0_SOP_P11_9_OUT',0,5,99,16
	.word	.L179
	.byte	1,5,3
	.word	IfxMsc0_SOP_P11_9_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMsc0_SOP_P11_9_OUT')
	.sect	'.debug_abbrev'
.L109:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMsc0_SOP_P13_3_OUT')
	.sect	'.debug_info'
.L110:
	.word	272
	.half	3
	.word	.L111
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMsc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMsc0_SOP_P13_3_OUT',0,5,100,16
	.word	.L180
	.byte	1,5,3
	.word	IfxMsc0_SOP_P13_3_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMsc0_SOP_P13_3_OUT')
	.sect	'.debug_abbrev'
.L111:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMsc1_SOP_P22_3_OUT')
	.sect	'.debug_info'
.L112:
	.word	272
	.half	3
	.word	.L113
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMsc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMsc1_SOP_P22_3_OUT',0,5,101,16
	.word	.L181
	.byte	1,5,3
	.word	IfxMsc1_SOP_P22_3_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMsc1_SOP_P22_3_OUT')
	.sect	'.debug_abbrev'
.L113:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMsc_En_Out_pinTable')
	.sect	'.debug_info'
.L114:
	.word	273
	.half	3
	.word	.L115
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMsc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMsc_En_Out_pinTable',0,5,104,22
	.word	.L182
	.byte	1,5,3
	.word	IfxMsc_En_Out_pinTable
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMsc_En_Out_pinTable')
	.sect	'.debug_abbrev'
.L115:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMsc_Fcln_Out_pinTable')
	.sect	'.debug_info'
.L116:
	.word	276
	.half	3
	.word	.L117
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMsc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMsc_Fcln_Out_pinTable',0,5,175,1,24
	.word	.L183
	.byte	1,5,3
	.word	IfxMsc_Fcln_Out_pinTable
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMsc_Fcln_Out_pinTable')
	.sect	'.debug_abbrev'
.L117:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMsc_Fclp_Out_pinTable')
	.sect	'.debug_info'
.L118:
	.word	276
	.half	3
	.word	.L119
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMsc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMsc_Fclp_Out_pinTable',0,5,186,1,24
	.word	.L184
	.byte	1,5,3
	.word	IfxMsc_Fclp_Out_pinTable
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMsc_Fclp_Out_pinTable')
	.sect	'.debug_abbrev'
.L119:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMsc_Inj_In_pinTable')
	.sect	'.debug_info'
.L120:
	.word	274
	.half	3
	.word	.L121
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMsc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMsc_Inj_In_pinTable',0,5,199,1,22
	.word	.L185
	.byte	1,5,3
	.word	IfxMsc_Inj_In_pinTable
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMsc_Inj_In_pinTable')
	.sect	'.debug_abbrev'
.L121:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMsc_Sdi_In_pinTable')
	.sect	'.debug_info'
.L122:
	.word	274
	.half	3
	.word	.L123
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMsc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMsc_Sdi_In_pinTable',0,5,210,1,22
	.word	.L186
	.byte	1,5,3
	.word	IfxMsc_Sdi_In_pinTable
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMsc_Sdi_In_pinTable')
	.sect	'.debug_abbrev'
.L123:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMsc_Son_Out_pinTable')
	.sect	'.debug_info'
.L124:
	.word	275
	.half	3
	.word	.L125
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMsc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMsc_Son_Out_pinTable',0,5,225,1,23
	.word	.L187
	.byte	1,5,3
	.word	IfxMsc_Son_Out_pinTable
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMsc_Son_Out_pinTable')
	.sect	'.debug_abbrev'
.L125:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMsc_Sop_Out_pinTable')
	.sect	'.debug_info'
.L126:
	.word	275
	.half	3
	.word	.L127
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxMsc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMsc_Sop_Out_pinTable',0,5,236,1,23
	.word	.L188
	.byte	1,5,3
	.word	IfxMsc_Sop_Out_pinTable
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMsc_Sop_Out_pinTable')
	.sect	'.debug_abbrev'
.L127:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	; Module end
