	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc14912a --dep-file=zf_driver_uart.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2\\\\xf_asr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/zf_driver/zf_driver_uart.src ../libraries/zf_driver/zf_driver_uart.c"
	.compiler_name		"ctc"
	;source	'../libraries/zf_driver/zf_driver_uart.c'

	
$TC16X
	
	.sdecl	'.text.zf_driver_uart.uart_set_interrupt_priority',code,cluster('uart_set_interrupt_priority')
	.sect	'.text.zf_driver_uart.uart_set_interrupt_priority'
	.align	2
	
	.global	uart_set_interrupt_priority
; Function uart_set_interrupt_priority
.L157:
uart_set_interrupt_priority:	.type	func
	mov	d15,#0
	jeq	d15,d4,.L2
.L660:
	mov	d15,#1
	jeq	d15,d4,.L3
.L661:
	mov	d15,#2
	jeq	d15,d4,.L4
.L662:
	mov	d15,#3
	jeq	d15,d4,.L5
	j	.L6
.L2:
	movh.a	a15,#@his(uart_config)
	lea	a15,[a15]@los(uart_config)
.L663:
	mov	d15,#11
.L664:
	st.h	[a15]32,d15
.L665:
	movh.a	a15,#@his(uart_config)
	lea	a15,[a15]@los(uart_config)
.L666:
	mov	d15,#10
.L667:
	st.h	[a15]34,d15
.L668:
	movh.a	a15,#@his(uart_config)
	lea	a15,[a15]@los(uart_config)
.L669:
	mov	d15,#12
.L670:
	st.h	[a15]36,d15
.L671:
	movh.a	a15,#@his(uart_config)
	lea	a15,[a15]@los(uart_config)
.L672:
	mov	d15,#0
.L673:
	st.b	[a15]38,d15
.L674:
	j	.L7
.L3:
	movh.a	a15,#@his(uart_config)
	lea	a15,[a15]@los(uart_config)
.L675:
	mov	d15,#13
.L676:
	st.h	[a15]32,d15
.L677:
	movh.a	a15,#@his(uart_config)
	lea	a15,[a15]@los(uart_config)
.L678:
	mov	d15,#14
.L679:
	st.h	[a15]34,d15
.L680:
	movh.a	a15,#@his(uart_config)
	lea	a15,[a15]@los(uart_config)
.L681:
	mov	d15,#15
.L682:
	st.h	[a15]36,d15
.L683:
	movh.a	a15,#@his(uart_config)
	lea	a15,[a15]@los(uart_config)
.L684:
	mov	d15,#0
.L685:
	st.b	[a15]38,d15
.L686:
	j	.L8
.L4:
	movh.a	a15,#@his(uart_config)
	lea	a15,[a15]@los(uart_config)
.L687:
	mov	d15,#16
.L688:
	st.h	[a15]32,d15
.L689:
	movh.a	a15,#@his(uart_config)
	lea	a15,[a15]@los(uart_config)
.L690:
	mov	d15,#17
.L691:
	st.h	[a15]34,d15
.L692:
	movh.a	a15,#@his(uart_config)
	lea	a15,[a15]@los(uart_config)
.L693:
	mov	d15,#18
.L694:
	st.h	[a15]36,d15
.L695:
	movh.a	a15,#@his(uart_config)
	lea	a15,[a15]@los(uart_config)
.L696:
	mov	d15,#0
.L697:
	st.b	[a15]38,d15
.L698:
	j	.L9
.L5:
	movh.a	a15,#@his(uart_config)
	lea	a15,[a15]@los(uart_config)
.L699:
	mov	d15,#19
.L700:
	st.h	[a15]32,d15
.L701:
	movh.a	a15,#@his(uart_config)
	lea	a15,[a15]@los(uart_config)
.L702:
	mov	d15,#20
.L703:
	st.h	[a15]34,d15
.L704:
	movh.a	a15,#@his(uart_config)
	lea	a15,[a15]@los(uart_config)
.L705:
	mov	d15,#21
.L706:
	st.h	[a15]36,d15
.L707:
	movh.a	a15,#@his(uart_config)
	lea	a15,[a15]@los(uart_config)
.L708:
	mov	d15,#0
.L709:
	st.b	[a15]38,d15
.L710:
	j	.L10
.L6:
	mov	d4,#0
.L424:
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#108
	call	debug_assert_handler
.L10:
.L9:
.L8:
.L7:
	ret
.L407:
	
__uart_set_interrupt_priority_function_end:
	.size	uart_set_interrupt_priority,__uart_set_interrupt_priority_function_end-uart_set_interrupt_priority
.L235:
	; End of function
	
	.sdecl	'.text.zf_driver_uart.uart_set_buffer',code,cluster('uart_set_buffer')
	.sect	'.text.zf_driver_uart.uart_set_buffer'
	.align	2
	
	.global	uart_set_buffer
; Function uart_set_buffer
.L159:
uart_set_buffer:	.type	func
	mov	d15,#0
	jeq	d15,d4,.L11
.L715:
	mov	d15,#1
	jeq	d15,d4,.L12
.L716:
	mov	d15,#2
	jeq	d15,d4,.L13
.L717:
	mov	d15,#3
	jeq	d15,d4,.L14
	j	.L15
.L11:
	movh.a	a15,#@his(uart_config)
	lea	a15,[a15]@los(uart_config)
.L718:
	movh.a	a2,#@his(uart0_tx_buffer)
	lea	a2,[a2]@los(uart0_tx_buffer)
.L719:
	st.a	[a15]48,a2
.L720:
	movh.a	a15,#@his(uart_config)
	lea	a15,[a15]@los(uart_config)
.L721:
	movh.a	a2,#@his(uart0_rx_buffer)
	lea	a2,[a2]@los(uart0_rx_buffer)
.L722:
	st.a	[a15]56,a2
.L723:
	movh.a	a15,#@his(uart_config)
	lea	a15,[a15]@los(uart_config)
.L724:
	mov	d15,#1
.L725:
	st.h	[a15]46,d15
.L726:
	movh.a	a15,#@his(uart_config)
	lea	a15,[a15]@los(uart_config)
.L727:
	mov	d15,#1
.L728:
	st.h	[a15]52,d15
.L729:
	j	.L16
.L12:
	movh.a	a15,#@his(uart_config)
	lea	a15,[a15]@los(uart_config)
.L730:
	movh.a	a2,#@his(uart1_tx_buffer)
	lea	a2,[a2]@los(uart1_tx_buffer)
.L731:
	st.a	[a15]48,a2
.L732:
	movh.a	a15,#@his(uart_config)
	lea	a15,[a15]@los(uart_config)
.L733:
	movh.a	a2,#@his(uart1_rx_buffer)
	lea	a2,[a2]@los(uart1_rx_buffer)
.L734:
	st.a	[a15]56,a2
.L735:
	movh.a	a15,#@his(uart_config)
	lea	a15,[a15]@los(uart_config)
.L736:
	mov	d15,#1
.L737:
	st.h	[a15]46,d15
.L738:
	movh.a	a15,#@his(uart_config)
	lea	a15,[a15]@los(uart_config)
.L739:
	mov	d15,#1
.L740:
	st.h	[a15]52,d15
.L741:
	j	.L17
.L13:
	movh.a	a15,#@his(uart_config)
	lea	a15,[a15]@los(uart_config)
.L742:
	movh.a	a2,#@his(uart2_tx_buffer)
	lea	a2,[a2]@los(uart2_tx_buffer)
.L743:
	st.a	[a15]48,a2
.L744:
	movh.a	a15,#@his(uart_config)
	lea	a15,[a15]@los(uart_config)
.L745:
	movh.a	a2,#@his(uart2_rx_buffer)
	lea	a2,[a2]@los(uart2_rx_buffer)
.L746:
	st.a	[a15]56,a2
.L747:
	movh.a	a15,#@his(uart_config)
	lea	a15,[a15]@los(uart_config)
.L748:
	mov	d15,#1
.L749:
	st.h	[a15]46,d15
.L750:
	movh.a	a15,#@his(uart_config)
	lea	a15,[a15]@los(uart_config)
.L751:
	mov	d15,#1
.L752:
	st.h	[a15]52,d15
.L753:
	j	.L18
.L14:
	movh.a	a15,#@his(uart_config)
	lea	a15,[a15]@los(uart_config)
.L754:
	movh.a	a2,#@his(uart3_tx_buffer)
	lea	a2,[a2]@los(uart3_tx_buffer)
.L755:
	st.a	[a15]48,a2
.L756:
	movh.a	a15,#@his(uart_config)
	lea	a15,[a15]@los(uart_config)
.L757:
	movh.a	a2,#@his(uart3_rx_buffer)
	lea	a2,[a2]@los(uart3_rx_buffer)
.L758:
	st.a	[a15]56,a2
.L759:
	movh.a	a15,#@his(uart_config)
	lea	a15,[a15]@los(uart_config)
.L760:
	mov	d15,#1
.L761:
	st.h	[a15]46,d15
.L762:
	movh.a	a15,#@his(uart_config)
	lea	a15,[a15]@los(uart_config)
.L763:
	mov	d15,#1
.L764:
	st.h	[a15]52,d15
.L765:
	j	.L19
.L15:
.L19:
.L18:
.L17:
.L16:
	ret
.L409:
	
__uart_set_buffer_function_end:
	.size	uart_set_buffer,__uart_set_buffer_function_end-uart_set_buffer
.L240:
	; End of function
	
	.sdecl	'.text.zf_driver_uart.uart_get_handle',code,cluster('uart_get_handle')
	.sect	'.text.zf_driver_uart.uart_get_handle'
	.align	2
	
	.global	uart_get_handle
; Function uart_get_handle
.L161:
uart_get_handle:	.type	func
	mov.a	a2,#0
.L425:
	mov	d15,#0
	jeq	d15,d4,.L20
.L770:
	mov	d15,#1
	jeq	d15,d4,.L21
.L771:
	mov	d15,#2
	jeq	d15,d4,.L22
.L772:
	mov	d15,#3
	jeq	d15,d4,.L23
	j	.L24
.L20:
	movh.a	a2,#@his(uart0_handle)
	lea	a2,[a2]@los(uart0_handle)
.L773:
	j	.L25
.L21:
	movh.a	a2,#@his(uart1_handle)
	lea	a2,[a2]@los(uart1_handle)
.L774:
	j	.L26
.L22:
	movh.a	a2,#@his(uart2_handle)
	lea	a2,[a2]@los(uart2_handle)
.L775:
	j	.L27
.L23:
	movh.a	a2,#@his(uart3_handle)
	lea	a2,[a2]@los(uart3_handle)
.L776:
	j	.L28
.L24:
.L28:
.L27:
.L26:
.L25:
	j	.L29
.L29:
	ret
.L411:
	
__uart_get_handle_function_end:
	.size	uart_get_handle,__uart_get_handle_function_end-uart_get_handle
.L245:
	; End of function
	
	.sdecl	'.text.zf_driver_uart.uart_mux',code,cluster('uart_mux')
	.sect	'.text.zf_driver_uart.uart_mux'
	.align	2
	
	.global	uart_mux
; Function uart_mux
.L163:
uart_mux:	.type	func
	mov	d15,#0
	jeq	d15,d4,.L30
.L781:
	mov	d15,#1
	jeq	d15,d4,.L31
.L782:
	mov	d15,#2
	jeq	d15,d4,.L32
.L783:
	mov	d15,#3
	jeq	d15,d4,.L33
	j	.L34
.L30:
	jne	d5,#0,.L35
.L784:
	movh.a	a15,#@his(IfxAsclin0_TX_P14_0_OUT)
	lea	a15,[a15]@los(IfxAsclin0_TX_P14_0_OUT)
	mov.d	d15,a15
.L785:
	st.w	[a4],d15
.L786:
	j	.L36
.L35:
	jne	d5,#1,.L37
.L787:
	movh.a	a15,#@his(IfxAsclin0_TX_P14_1_OUT)
	lea	a15,[a15]@los(IfxAsclin0_TX_P14_1_OUT)
	mov.d	d15,a15
.L788:
	st.w	[a4],d15
.L789:
	j	.L38
.L37:
	jne	d5,#2,.L39
.L790:
	movh.a	a15,#@his(IfxAsclin0_TX_P15_2_OUT)
	lea	a15,[a15]@los(IfxAsclin0_TX_P15_2_OUT)
	mov.d	d15,a15
.L791:
	st.w	[a4],d15
.L792:
	j	.L40
.L39:
	jne	d5,#3,.L41
.L793:
	movh.a	a15,#@his(IfxAsclin0_TX_P15_3_OUT)
	lea	a15,[a15]@los(IfxAsclin0_TX_P15_3_OUT)
	mov.d	d15,a15
.L794:
	st.w	[a4],d15
.L795:
	j	.L42
.L41:
.L42:
.L40:
.L38:
.L36:
	jne	d6,#0,.L43
.L796:
	movh.a	a15,#@his(IfxAsclin0_RXA_P14_1_IN)
	lea	a15,[a15]@los(IfxAsclin0_RXA_P14_1_IN)
	mov.d	d15,a15
.L797:
	st.w	[a5],d15
.L798:
	j	.L44
.L43:
	jne	d6,#1,.L45
.L799:
	movh.a	a15,#@his(IfxAsclin0_RXB_P15_3_IN)
	lea	a15,[a15]@los(IfxAsclin0_RXB_P15_3_IN)
	mov.d	d15,a15
.L800:
	st.w	[a5],d15
.L801:
	j	.L46
.L45:
.L46:
.L44:
	j	.L47
.L31:
	jne	d5,#4,.L48
.L802:
	movh.a	a15,#@his(IfxAsclin1_TX_P02_2_OUT)
	lea	a15,[a15]@los(IfxAsclin1_TX_P02_2_OUT)
	mov.d	d15,a15
.L803:
	st.w	[a4],d15
.L804:
	j	.L49
.L48:
	jne	d5,#5,.L50
.L805:
	movh.a	a15,#@his(IfxAsclin1_TX_P11_12_OUT)
	lea	a15,[a15]@los(IfxAsclin1_TX_P11_12_OUT)
	mov.d	d15,a15
.L806:
	st.w	[a4],d15
.L807:
	j	.L51
.L50:
	jne	d5,#6,.L52
.L808:
	movh.a	a15,#@his(IfxAsclin1_TX_P15_0_OUT)
	lea	a15,[a15]@los(IfxAsclin1_TX_P15_0_OUT)
	mov.d	d15,a15
.L809:
	st.w	[a4],d15
.L810:
	j	.L53
.L52:
	jne	d5,#7,.L54
.L811:
	movh.a	a15,#@his(IfxAsclin1_TX_P15_1_OUT)
	lea	a15,[a15]@los(IfxAsclin1_TX_P15_1_OUT)
	mov.d	d15,a15
.L812:
	st.w	[a4],d15
.L813:
	j	.L55
.L54:
	mov	d15,#8
.L814:
	jne	d15,d5,.L56
.L815:
	movh.a	a15,#@his(IfxAsclin1_TX_P15_4_OUT)
	lea	a15,[a15]@los(IfxAsclin1_TX_P15_4_OUT)
	mov.d	d15,a15
.L816:
	st.w	[a4],d15
.L817:
	j	.L57
.L56:
	mov	d15,#9
.L818:
	jne	d15,d5,.L58
.L819:
	movh.a	a15,#@his(IfxAsclin1_TX_P15_5_OUT)
	lea	a15,[a15]@los(IfxAsclin1_TX_P15_5_OUT)
	mov.d	d15,a15
.L820:
	st.w	[a4],d15
.L821:
	j	.L59
.L58:
	mov	d15,#10
.L822:
	jne	d15,d5,.L60
.L823:
	movh.a	a15,#@his(IfxAsclin1_TX_P20_10_OUT)
	lea	a15,[a15]@los(IfxAsclin1_TX_P20_10_OUT)
	mov.d	d15,a15
.L824:
	st.w	[a4],d15
.L825:
	j	.L61
.L60:
	mov	d15,#11
.L826:
	jne	d15,d5,.L62
.L827:
	movh.a	a15,#@his(IfxAsclin1_TX_P33_12_OUT)
	lea	a15,[a15]@los(IfxAsclin1_TX_P33_12_OUT)
	mov.d	d15,a15
.L828:
	st.w	[a4],d15
.L829:
	j	.L63
.L62:
	mov	d15,#12
.L830:
	jne	d15,d5,.L64
.L831:
	movh.a	a15,#@his(IfxAsclin1_TX_P33_13_OUT)
	lea	a15,[a15]@los(IfxAsclin1_TX_P33_13_OUT)
	mov.d	d15,a15
.L832:
	st.w	[a4],d15
.L833:
	j	.L65
.L64:
.L65:
.L63:
.L61:
.L59:
.L57:
.L55:
.L53:
.L51:
.L49:
	jne	d6,#4,.L66
.L834:
	movh.a	a15,#@his(IfxAsclin1_RXA_P15_1_IN)
	lea	a15,[a15]@los(IfxAsclin1_RXA_P15_1_IN)
	mov.d	d15,a15
.L835:
	st.w	[a5],d15
.L836:
	j	.L67
.L66:
	jne	d6,#5,.L68
.L837:
	movh.a	a15,#@his(IfxAsclin1_RXB_P15_5_IN)
	lea	a15,[a15]@los(IfxAsclin1_RXB_P15_5_IN)
	mov.d	d15,a15
.L838:
	st.w	[a5],d15
.L839:
	j	.L69
.L68:
	jne	d6,#6,.L70
.L840:
	movh.a	a15,#@his(IfxAsclin1_RXC_P20_9_IN)
	lea	a15,[a15]@los(IfxAsclin1_RXC_P20_9_IN)
	mov.d	d15,a15
.L841:
	st.w	[a5],d15
.L842:
	j	.L71
.L70:
	jne	d6,#3,.L72
.L843:
	movh.a	a15,#@his(IfxAsclin1_RXE_P11_10_IN)
	lea	a15,[a15]@los(IfxAsclin1_RXE_P11_10_IN)
	mov.d	d15,a15
.L844:
	st.w	[a5],d15
.L845:
	j	.L73
.L72:
	jne	d6,#7,.L74
.L846:
	movh.a	a15,#@his(IfxAsclin1_RXF_P33_13_IN)
	lea	a15,[a15]@los(IfxAsclin1_RXF_P33_13_IN)
	mov.d	d15,a15
.L847:
	st.w	[a5],d15
.L848:
	j	.L75
.L74:
	jne	d6,#2,.L76
.L849:
	movh.a	a15,#@his(IfxAsclin1_RXG_P02_3_IN)
	lea	a15,[a15]@los(IfxAsclin1_RXG_P02_3_IN)
	mov.d	d15,a15
.L850:
	st.w	[a5],d15
.L851:
	j	.L77
.L76:
.L77:
.L75:
.L73:
.L71:
.L69:
.L67:
	j	.L78
.L32:
	mov	d15,#13
.L852:
	jne	d15,d5,.L79
.L853:
	movh.a	a15,#@his(IfxAsclin2_TX_P02_0_OUT)
	lea	a15,[a15]@los(IfxAsclin2_TX_P02_0_OUT)
	mov.d	d15,a15
.L854:
	st.w	[a4],d15
.L855:
	j	.L80
.L79:
	mov	d15,#14
.L856:
	jne	d15,d5,.L81
.L857:
	movh.a	a15,#@his(IfxAsclin2_TX_P10_5_OUT)
	lea	a15,[a15]@los(IfxAsclin2_TX_P10_5_OUT)
	mov.d	d15,a15
.L858:
	st.w	[a4],d15
.L859:
	j	.L82
.L81:
	mov	d15,#15
.L860:
	jne	d15,d5,.L83
.L861:
	movh.a	a15,#@his(IfxAsclin2_TX_P14_2_OUT)
	lea	a15,[a15]@los(IfxAsclin2_TX_P14_2_OUT)
	mov.d	d15,a15
.L862:
	st.w	[a4],d15
.L863:
	j	.L84
.L83:
	mov	d15,#16
.L864:
	jne	d15,d5,.L85
.L865:
	movh.a	a15,#@his(IfxAsclin2_TX_P14_3_OUT)
	lea	a15,[a15]@los(IfxAsclin2_TX_P14_3_OUT)
	mov.d	d15,a15
.L866:
	st.w	[a4],d15
.L867:
	j	.L86
.L85:
	mov	d15,#17
.L868:
	jne	d15,d5,.L87
.L869:
	movh.a	a15,#@his(IfxAsclin2_TX_P33_8_OUT)
	lea	a15,[a15]@los(IfxAsclin2_TX_P33_8_OUT)
	mov.d	d15,a15
.L870:
	st.w	[a4],d15
.L871:
	j	.L88
.L87:
	mov	d15,#18
.L872:
	jne	d15,d5,.L89
.L873:
	movh.a	a15,#@his(IfxAsclin2_TX_P33_9_OUT)
	lea	a15,[a15]@los(IfxAsclin2_TX_P33_9_OUT)
	mov.d	d15,a15
.L874:
	st.w	[a4],d15
.L875:
	j	.L90
.L89:
.L90:
.L88:
.L86:
.L84:
.L82:
.L80:
	mov	d15,#11
.L876:
	jne	d15,d6,.L91
.L877:
	movh.a	a15,#@his(IfxAsclin2_RXA_P14_3_IN)
	lea	a15,[a15]@los(IfxAsclin2_RXA_P14_3_IN)
	mov.d	d15,a15
.L878:
	st.w	[a5],d15
.L879:
	j	.L92
.L91:
	mov	d15,#9
.L880:
	jne	d15,d6,.L93
.L881:
	movh.a	a15,#@his(IfxAsclin2_RXB_P02_1_IN)
	lea	a15,[a15]@los(IfxAsclin2_RXB_P02_1_IN)
	mov.d	d15,a15
.L882:
	st.w	[a5],d15
.L883:
	j	.L94
.L93:
	mov	d15,#10
.L884:
	jne	d15,d6,.L95
.L885:
	movh.a	a15,#@his(IfxAsclin2_RXD_P10_6_IN)
	lea	a15,[a15]@los(IfxAsclin2_RXD_P10_6_IN)
	mov.d	d15,a15
.L886:
	st.w	[a5],d15
.L887:
	j	.L96
.L95:
	mov	d15,#12
.L888:
	jne	d15,d6,.L97
.L889:
	movh.a	a15,#@his(IfxAsclin2_RXE_P33_8_IN)
	lea	a15,[a15]@los(IfxAsclin2_RXE_P33_8_IN)
	mov.d	d15,a15
.L890:
	st.w	[a5],d15
.L891:
	j	.L98
.L97:
	mov	d15,#8
.L892:
	jne	d15,d6,.L99
.L893:
	movh.a	a15,#@his(IfxAsclin2_RXG_P02_0_IN)
	lea	a15,[a15]@los(IfxAsclin2_RXG_P02_0_IN)
	mov.d	d15,a15
.L894:
	st.w	[a5],d15
.L895:
	j	.L100
.L99:
.L100:
.L98:
.L96:
.L94:
.L92:
	j	.L101
.L33:
	mov	d15,#19
.L896:
	jne	d15,d5,.L102
.L897:
	movh.a	a15,#@his(IfxAsclin3_TX_P00_0_OUT)
	lea	a15,[a15]@los(IfxAsclin3_TX_P00_0_OUT)
	mov.d	d15,a15
.L898:
	st.w	[a4],d15
.L899:
	j	.L103
.L102:
	mov	d15,#20
.L900:
	jne	d15,d5,.L104
.L901:
	movh.a	a15,#@his(IfxAsclin3_TX_P00_1_OUT)
	lea	a15,[a15]@los(IfxAsclin3_TX_P00_1_OUT)
	mov.d	d15,a15
.L902:
	st.w	[a4],d15
.L903:
	j	.L105
.L104:
	mov	d15,#21
.L904:
	jne	d15,d5,.L106
.L905:
	movh.a	a15,#@his(IfxAsclin3_TX_P15_6_OUT)
	lea	a15,[a15]@los(IfxAsclin3_TX_P15_6_OUT)
	mov.d	d15,a15
.L906:
	st.w	[a4],d15
.L907:
	j	.L107
.L106:
	mov	d15,#22
.L908:
	jne	d15,d5,.L108
.L909:
	movh.a	a15,#@his(IfxAsclin3_TX_P15_7_OUT)
	lea	a15,[a15]@los(IfxAsclin3_TX_P15_7_OUT)
	mov.d	d15,a15
.L910:
	st.w	[a4],d15
.L911:
	j	.L109
.L108:
	mov	d15,#23
.L912:
	jne	d15,d5,.L110
.L913:
	movh.a	a15,#@his(IfxAsclin3_TX_P20_0_OUT)
	lea	a15,[a15]@los(IfxAsclin3_TX_P20_0_OUT)
	mov.d	d15,a15
.L914:
	st.w	[a4],d15
.L915:
	j	.L111
.L110:
	mov	d15,#24
.L916:
	jne	d15,d5,.L112
.L917:
	movh.a	a15,#@his(IfxAsclin3_TX_P20_3_OUT)
	lea	a15,[a15]@los(IfxAsclin3_TX_P20_3_OUT)
	mov.d	d15,a15
.L918:
	st.w	[a4],d15
.L919:
	j	.L113
.L112:
	mov	d15,#25
.L920:
	jne	d15,d5,.L114
.L921:
	movh.a	a15,#@his(IfxAsclin3_TX_P21_7_OUT)
	lea	a15,[a15]@los(IfxAsclin3_TX_P21_7_OUT)
	mov.d	d15,a15
.L922:
	st.w	[a4],d15
.L923:
	j	.L115
.L114:
.L115:
.L113:
.L111:
.L109:
.L107:
.L105:
.L103:
	mov	d15,#14
.L924:
	jne	d15,d6,.L116
.L925:
	movh.a	a15,#@his(IfxAsclin3_RXA_P15_7_IN)
	lea	a15,[a15]@los(IfxAsclin3_RXA_P15_7_IN)
	mov.d	d15,a15
.L926:
	st.w	[a5],d15
.L927:
	j	.L117
.L116:
	mov	d15,#15
.L928:
	jne	d15,d6,.L118
.L929:
	movh.a	a15,#@his(IfxAsclin3_RXC_P20_3_IN)
	lea	a15,[a15]@los(IfxAsclin3_RXC_P20_3_IN)
	mov.d	d15,a15
.L930:
	st.w	[a5],d15
.L931:
	j	.L119
.L118:
	mov	d15,#13
.L932:
	jne	d15,d6,.L120
.L933:
	movh.a	a15,#@his(IfxAsclin3_RXE_P00_1_IN)
	lea	a15,[a15]@los(IfxAsclin3_RXE_P00_1_IN)
	mov.d	d15,a15
.L934:
	st.w	[a5],d15
.L935:
	j	.L121
.L120:
	mov	d15,#16
.L936:
	jne	d15,d6,.L122
.L937:
	movh.a	a15,#@his(IfxAsclin3_RXF_P21_6_IN)
	lea	a15,[a15]@los(IfxAsclin3_RXF_P21_6_IN)
	mov.d	d15,a15
.L938:
	st.w	[a5],d15
.L939:
	j	.L123
.L122:
.L123:
.L121:
.L119:
.L117:
	j	.L124
.L34:
	j	.L125
.L125:
.L124:
.L101:
.L78:
.L47:
	ret
.L414:
	
__uart_mux_function_end:
	.size	uart_mux,__uart_mux_function_end-uart_mux
.L250:
	; End of function
	
	.sdecl	'.text.zf_driver_uart.uart_write_byte',code,cluster('uart_write_byte')
	.sect	'.text.zf_driver_uart.uart_write_byte'
	.align	2
	
	.global	uart_write_byte
; Function uart_write_byte
.L165:
uart_write_byte:	.type	func
	sub.a	a10,#8
.L426:
	st.b	[a10],d5
.L524:
	call	uart_get_handle
.L427:
	j	.L126
.L127:
.L126:
	ld.a	a15,[a2]
.L285:
	ld.bu	d15,[a15]14
	and	d15,#31
.L525:
	j	.L128
.L128:
	jne	d15,#0,.L127
.L526:
	ld.a	a4,[a2]
.L527:
	lea	a5,[a10]0
.L528:
	mov	d4,#1
	call	IfxAsclin_write8
.L428:
	ret
.L277:
	
__uart_write_byte_function_end:
	.size	uart_write_byte,__uart_write_byte_function_end-uart_write_byte
.L190:
	; End of function
	
	.sdecl	'.text.zf_driver_uart.uart_write_buffer',code,cluster('uart_write_buffer')
	.sect	'.text.zf_driver_uart.uart_write_buffer'
	.align	2
	
	.global	uart_write_buffer
; Function uart_write_buffer
.L167:
uart_write_buffer:	.type	func
	mov	d8,d4
.L429:
	mov.aa	a15,a4
.L430:
	mov	d15,d5
.L431:
	j	.L129
.L130:
	ld.bu	d5,[a15]
	mov	d4,d8
.L432:
	call	uart_write_byte
.L433:
	add	d15,#-1
.L533:
	add.a	a15,#1
.L129:
	jne	d15,#0,.L130
.L534:
	ret
.L289:
	
__uart_write_buffer_function_end:
	.size	uart_write_buffer,__uart_write_buffer_function_end-uart_write_buffer
.L195:
	; End of function
	
	.sdecl	'.text.zf_driver_uart.uart_write_string',code,cluster('uart_write_string')
	.sect	'.text.zf_driver_uart.uart_write_string'
	.align	2
	
	.global	uart_write_string
; Function uart_write_string
.L169:
uart_write_string:	.type	func
	mov	d8,d4
.L434:
	mov.aa	a15,a4
.L435:
	j	.L131
.L132:
	ld.b	d15,[a15]0
.L539:
	add.a	a15,#1
	extr.u	d5,d15,#0,#8
	mov	d4,d8
.L436:
	call	uart_write_byte
.L131:
	ld.b	d15,[a15]0
.L540:
	jne	d15,#0,.L132
.L541:
	ret
.L295:
	
__uart_write_string_function_end:
	.size	uart_write_string,__uart_write_string_function_end-uart_write_string
.L200:
	; End of function
	
	.sdecl	'.text.zf_driver_uart.uart_read_byte',code,cluster('uart_read_byte')
	.sect	'.text.zf_driver_uart.uart_read_byte'
	.align	2
	
	.global	uart_read_byte
; Function uart_read_byte
.L171:
uart_read_byte:	.type	func
	sub.a	a10,#8
.L437:
	mov	d15,#0
.L546:
	st.b	[a10],d15
.L547:
	call	uart_get_handle
.L438:
	j	.L133
.L134:
.L133:
	ld.a	a15,[a2]
.L305:
	ld.bu	d15,[a15]18
	and	d15,#31
.L548:
	j	.L135
.L135:
	jeq	d15,#0,.L134
.L549:
	ld.a	a4,[a2]
.L550:
	lea	a5,[a10]0
.L551:
	mov	d4,#1
	call	IfxAsclin_read8
.L439:
	ld.bu	d2,[a10]
.L552:
	j	.L136
.L136:
	ret
.L300:
	
__uart_read_byte_function_end:
	.size	uart_read_byte,__uart_read_byte_function_end-uart_read_byte
.L205:
	; End of function
	
	.sdecl	'.text.zf_driver_uart.uart_query_byte',code,cluster('uart_query_byte')
	.sect	'.text.zf_driver_uart.uart_query_byte'
	.align	2
	
	.global	uart_query_byte
; Function uart_query_byte
.L173:
uart_query_byte:	.type	func
	mov.aa	a15,a4
.L441:
	mov	d8,#0
.L442:
	call	uart_get_handle
.L440:
	ld.a	a4,[a2]
.L315:
	ld.bu	d15,[a4]18
	and	d15,#31
.L557:
	j	.L137
.L137:
	jlt.u	d15,#1,.L138
.L558:
	ld.a	a4,[a2]
.L559:
	mov	d4,#1
	mov.aa	a5,a15
.L444:
	call	IfxAsclin_read8
.L443:
	mov	d8,#1
.L138:
	mov	d2,d8
.L445:
	j	.L139
.L139:
	ret
.L309:
	
__uart_query_byte_function_end:
	.size	uart_query_byte,__uart_query_byte_function_end-uart_query_byte
.L210:
	; End of function
	
	.sdecl	'.text.zf_driver_uart.uart_tx_interrupt',code,cluster('uart_tx_interrupt')
	.sect	'.text.zf_driver_uart.uart_tx_interrupt'
	.align	2
	
	.global	uart_tx_interrupt
; Function uart_tx_interrupt
.L175:
uart_tx_interrupt:	.type	func
	mov	d8,d5
.L448:
	movh.a	a15,#@his(uart_config)
	lea	a15,[a15]@los(uart_config)
.L564:
	ld.a	a15,[a15]
.L449:
	extr	d4,d4,#0,#8
.L447:
	call	IfxAsclin_getAddress
.L446:
	movh.a	a4,#@his(uart_config)
	lea	a4,[a4]@los(uart_config)
.L565:
	mov.aa	a5,a2
.L451:
	call	IfxAsclin_Asc_initModuleConfig
.L450:
	mov.aa	a4,a15
.L452:
	call	IfxAsclin_getSrcPointerTx
.L453:
	extr.u	d15,d8,#0,#8
.L326:
	jeq	d15,#0,.L140
.L566:
	mov	d15,#1
.L567:
	j	.L141
.L140:
	mov	d15,#0
.L141:
	ld.bu	d0,[a15]67
.L568:
	insert	d15,d0,d15,#7,#1
	st.b	[a15]67,d15
.L327:
	jeq	d8,#0,.L142
.L334:
	ld.bu	d15,[a2]1
.L569:
	or	d15,#4
	st.b	[a2]1,d15
.L335:
	j	.L143
.L142:
	ld.bu	d15,[a2]1
.L570:
	insert	d15,d15,#0,#2,#1
	st.b	[a2]1,d15
.L143:
	ret
.L317:
	
__uart_tx_interrupt_function_end:
	.size	uart_tx_interrupt,__uart_tx_interrupt_function_end-uart_tx_interrupt
.L215:
	; End of function
	
	.sdecl	'.text.zf_driver_uart.uart_rx_interrupt',code,cluster('uart_rx_interrupt')
	.sect	'.text.zf_driver_uart.uart_rx_interrupt'
	.align	2
	
	.global	uart_rx_interrupt
; Function uart_rx_interrupt
.L177:
uart_rx_interrupt:	.type	func
	mov	d8,d5
.L456:
	movh.a	a15,#@his(uart_config)
	lea	a15,[a15]@los(uart_config)
.L575:
	ld.a	a15,[a15]
.L457:
	extr	d4,d4,#0,#8
.L455:
	call	IfxAsclin_getAddress
.L454:
	movh.a	a4,#@his(uart_config)
	lea	a4,[a4]@los(uart_config)
.L576:
	mov.aa	a5,a2
.L459:
	call	IfxAsclin_Asc_initModuleConfig
.L458:
	mov.aa	a4,a15
.L460:
	call	IfxAsclin_getSrcPointerRx
.L461:
	extr.u	d15,d8,#0,#8
.L350:
	jeq	d15,#0,.L144
.L577:
	mov	d15,#1
.L578:
	j	.L145
.L144:
	mov	d15,#0
.L145:
	ld.bu	d0,[a15]67
.L579:
	insert	d15,d0,d15,#4,#1
	st.b	[a15]67,d15
.L351:
	jeq	d8,#0,.L146
.L357:
	ld.bu	d15,[a2]1
.L580:
	or	d15,#4
	st.b	[a2]1,d15
.L358:
	j	.L147
.L146:
	ld.bu	d15,[a2]1
.L581:
	insert	d15,d15,#0,#2,#1
	st.b	[a2]1,d15
.L147:
	ret
.L343:
	
__uart_rx_interrupt_function_end:
	.size	uart_rx_interrupt,__uart_rx_interrupt_function_end-uart_rx_interrupt
.L220:
	; End of function
	
	.sdecl	'.text.zf_driver_uart.uart_sbus_init',code,cluster('uart_sbus_init')
	.sect	'.text.zf_driver_uart.uart_sbus_init'
	.align	2
	
	.global	uart_sbus_init
; Function uart_sbus_init
.L179:
uart_sbus_init:	.type	func
	sub.a	a10,#32
.L462:
	mov	e10,d5,d4
	mov	e8,d6,d7
.L371:
	mfcr	d15,#65068
.L465:
	extr.u	d15,d15,#15,#1
.L466:
	ne	d12,d15,#0
.L467:
	j	.L148
.L148:
	disable
.L586:
	nop
.L587:
	j	.L149
.L149:
	j	.L150
.L150:
	extr	d4,d10,#0,#8
.L464:
	call	IfxAsclin_getAddress
.L463:
	movh.a	a4,#@his(uart_config)
	lea	a4,[a4]@los(uart_config)
.L588:
	mov.aa	a5,a2
.L469:
	call	IfxAsclin_Asc_initModuleConfig
.L468:
	mov	d4,d10
.L470:
	call	uart_set_buffer
.L471:
	mov	d4,d10
.L472:
	call	uart_set_interrupt_priority
.L473:
	movh.a	a15,#@his(uart_config)
	lea	a15,[a15]@los(uart_config)
.L589:
	mov	d15,#8
.L590:
	st.b	[a15]44,d15
.L591:
	movh.a	a15,#@his(uart_config)
	lea	a15,[a15]@los(uart_config)
.L592:
	mov	d15,#4
.L593:
	st.h	[a15]8,d15
.L594:
	movh.a	a15,#@his(uart_config)
	lea	a15,[a15]@los(uart_config)
.L474:
	utof	d15,d11
.L475:
	st.w	[a15]4,d15
.L595:
	movh.a	a15,#@his(uart_config)
	lea	a15,[a15]@los(uart_config)
.L596:
	mov	d15,#7
.L597:
	st.b	[a15]10,d15
.L598:
	movh.a	a15,#@his(uart_config)
	lea	a15,[a15]@los(uart_config)
.L599:
	mov	d15,#2
.L600:
	st.b	[a15]17,d15
.L601:
	movh.a	a15,#@his(uart_config)
	lea	a15,[a15]@los(uart_config)
.L602:
	mov	d15,#0
.L603:
	st.b	[a15]20,d15
.L604:
	movh.a	a15,#@his(uart_config)
	lea	a15,[a15]@los(uart_config)
.L605:
	mov	d15,#7
.L606:
	st.b	[a15]21,d15
.L607:
	movh.a	a15,#@his(uart_config)
	lea	a15,[a15]@los(uart_config)
.L608:
	mov	d15,#1
.L609:
	st.b	[a15]22,d15
.L380:
	mov.a	a15,#0
.L610:
	st.a	[a10],a15
.L611:
	mov.a	a15,#0
.L612:
	st.a	[a10]16,a15
.L613:
	lea	a4,[a10]24
.L614:
	lea	a5,[a10]8
.L476:
	mov	e4,d9,d10
.L477:
	mov	d6,d8
.L478:
	call	uart_mux
.L479:
	mov	d15,#16
.L615:
	st.b	[a10]12,d15
.L616:
	mov	d15,#128
.L617:
	st.b	[a10]28,d15
.L618:
	mov	d15,#0
.L619:
	st.b	[a10]29,d15
.L620:
	movh.a	a15,#@his(uart_config)
	lea	a15,[a15]@los(uart_config)
.L621:
	lea	a2,[a10]0
.L622:
	st.a	[a15]40,a2
.L480:
	mov	d4,d10
.L481:
	call	uart_get_handle
.L482:
	movh.a	a5,#@his(uart_config)
	lea	a5,[a5]@los(uart_config)
	mov.aa	a4,a2
	call	IfxAsclin_Asc_initModule
.L623:
	mov	d5,#1
.L483:
	mov	d4,d10
.L484:
	call	uart_rx_interrupt
.L485:
	mov	d5,#0
.L486:
	mov	d4,d10
.L487:
	call	uart_tx_interrupt
.L488:
	mov	d15,d12
.L384:
	jeq	d15,#0,.L151
.L624:
	enable
.L151:
	ret
.L361:
	
__uart_sbus_init_function_end:
	.size	uart_sbus_init,__uart_sbus_init_function_end-uart_sbus_init
.L225:
	; End of function
	
	.sdecl	'.text.zf_driver_uart.uart_init',code,cluster('uart_init')
	.sect	'.text.zf_driver_uart.uart_init'
	.align	2
	
	.global	uart_init
; Function uart_init
.L181:
uart_init:	.type	func
	sub.a	a10,#32
.L489:
	mov	e10,d5,d4
	mov	e8,d6,d7
.L399:
	mfcr	d15,#65068
.L492:
	extr.u	d15,d15,#15,#1
.L493:
	ne	d12,d15,#0
.L494:
	j	.L152
.L152:
	disable
.L629:
	nop
.L630:
	j	.L153
.L153:
	j	.L154
.L154:
	extr	d4,d10,#0,#8
.L491:
	call	IfxAsclin_getAddress
.L490:
	movh.a	a4,#@his(uart_config)
	lea	a4,[a4]@los(uart_config)
.L631:
	mov.aa	a5,a2
.L496:
	call	IfxAsclin_Asc_initModuleConfig
.L495:
	mov	d4,d10
.L497:
	call	uart_set_buffer
.L498:
	mov	d4,d10
.L499:
	call	uart_set_interrupt_priority
.L500:
	movh.a	a15,#@his(uart_config)
	lea	a15,[a15]@los(uart_config)
.L632:
	mov	d15,#8
.L633:
	st.b	[a15]44,d15
.L634:
	movh.a	a15,#@his(uart_config)
	lea	a15,[a15]@los(uart_config)
.L635:
	mov	d15,#4
.L636:
	st.h	[a15]8,d15
.L637:
	movh.a	a15,#@his(uart_config)
	lea	a15,[a15]@los(uart_config)
.L501:
	utof	d15,d11
.L502:
	st.w	[a15]4,d15
.L638:
	movh.a	a15,#@his(uart_config)
	lea	a15,[a15]@los(uart_config)
.L639:
	mov	d15,#7
.L640:
	st.b	[a15]10,d15
.L402:
	mov.a	a15,#0
.L641:
	st.a	[a10],a15
.L642:
	mov.a	a15,#0
.L643:
	st.a	[a10]16,a15
.L644:
	lea	a4,[a10]24
.L645:
	lea	a5,[a10]8
.L503:
	mov	e4,d9,d10
.L504:
	mov	d6,d8
.L505:
	call	uart_mux
.L506:
	mov	d15,#16
.L646:
	st.b	[a10]12,d15
.L647:
	mov	d15,#128
.L648:
	st.b	[a10]28,d15
.L649:
	mov	d15,#0
.L650:
	st.b	[a10]29,d15
.L651:
	movh.a	a15,#@his(uart_config)
	lea	a15,[a15]@los(uart_config)
.L652:
	lea	a2,[a10]0
.L653:
	st.a	[a15]40,a2
.L507:
	mov	d4,d10
.L508:
	call	uart_get_handle
.L509:
	movh.a	a5,#@his(uart_config)
	lea	a5,[a5]@los(uart_config)
	mov.aa	a4,a2
	call	IfxAsclin_Asc_initModule
.L654:
	mov	d5,#0
.L510:
	mov	d4,d10
.L511:
	call	uart_rx_interrupt
.L512:
	mov	d5,#0
.L513:
	mov	d4,d10
.L514:
	call	uart_tx_interrupt
.L515:
	mov	d15,d12
.L404:
	jeq	d15,#0,.L155
.L655:
	enable
.L155:
	ret
.L392:
	
__uart_init_function_end:
	.size	uart_init,__uart_init_function_end-uart_init
.L230:
	; End of function
	
	.sdecl	'.bss.zf_driver_uart.uart0_handle',data,cluster('uart0_handle')
	.sect	'.bss.zf_driver_uart.uart0_handle'
	.global	uart0_handle
	.align	4
uart0_handle:	.type	object
	.size	uart0_handle,28
	.space	28
	.sdecl	'.bss.zf_driver_uart.uart1_handle',data,cluster('uart1_handle')
	.sect	'.bss.zf_driver_uart.uart1_handle'
	.global	uart1_handle
	.align	4
uart1_handle:	.type	object
	.size	uart1_handle,28
	.space	28
	.sdecl	'.bss.zf_driver_uart.uart2_handle',data,cluster('uart2_handle')
	.sect	'.bss.zf_driver_uart.uart2_handle'
	.global	uart2_handle
	.align	4
uart2_handle:	.type	object
	.size	uart2_handle,28
	.space	28
	.sdecl	'.bss.zf_driver_uart.uart3_handle',data,cluster('uart3_handle')
	.sect	'.bss.zf_driver_uart.uart3_handle'
	.global	uart3_handle
	.align	4
uart3_handle:	.type	object
	.size	uart3_handle,28
	.space	28
	.sdecl	'.bss.zf_driver_uart.uart_config',data,cluster('uart_config')
	.sect	'.bss.zf_driver_uart.uart_config'
	.align	4
uart_config:	.type	object
	.size	uart_config,64
	.space	64
	.sdecl	'.bss.zf_driver_uart.uart0_tx_buffer',data,cluster('uart0_tx_buffer')
	.sect	'.bss.zf_driver_uart.uart0_tx_buffer'
uart0_tx_buffer:	.type	object
	.size	uart0_tx_buffer,37
	.space	37
	.sdecl	'.bss.zf_driver_uart.uart0_rx_buffer',data,cluster('uart0_rx_buffer')
	.sect	'.bss.zf_driver_uart.uart0_rx_buffer'
uart0_rx_buffer:	.type	object
	.size	uart0_rx_buffer,37
	.space	37
	.sdecl	'.bss.zf_driver_uart.uart1_tx_buffer',data,cluster('uart1_tx_buffer')
	.sect	'.bss.zf_driver_uart.uart1_tx_buffer'
uart1_tx_buffer:	.type	object
	.size	uart1_tx_buffer,37
	.space	37
	.sdecl	'.bss.zf_driver_uart.uart1_rx_buffer',data,cluster('uart1_rx_buffer')
	.sect	'.bss.zf_driver_uart.uart1_rx_buffer'
uart1_rx_buffer:	.type	object
	.size	uart1_rx_buffer,37
	.space	37
	.sdecl	'.bss.zf_driver_uart.uart2_tx_buffer',data,cluster('uart2_tx_buffer')
	.sect	'.bss.zf_driver_uart.uart2_tx_buffer'
uart2_tx_buffer:	.type	object
	.size	uart2_tx_buffer,37
	.space	37
	.sdecl	'.bss.zf_driver_uart.uart2_rx_buffer',data,cluster('uart2_rx_buffer')
	.sect	'.bss.zf_driver_uart.uart2_rx_buffer'
uart2_rx_buffer:	.type	object
	.size	uart2_rx_buffer,37
	.space	37
	.sdecl	'.bss.zf_driver_uart.uart3_tx_buffer',data,cluster('uart3_tx_buffer')
	.sect	'.bss.zf_driver_uart.uart3_tx_buffer'
uart3_tx_buffer:	.type	object
	.size	uart3_tx_buffer,37
	.space	37
	.sdecl	'.bss.zf_driver_uart.uart3_rx_buffer',data,cluster('uart3_rx_buffer')
	.sect	'.bss.zf_driver_uart.uart3_rx_buffer'
uart3_rx_buffer:	.type	object
	.size	uart3_rx_buffer,37
	.space	37
	.sdecl	'.rodata.zf_driver_uart..1.str',data,rom
	.sect	'.rodata.zf_driver_uart..1.str'
.1.str:	.type	object
	.size	.1.str,40
	.byte	46,46,47,108,105,98,114,97
	.byte	114,105,101,115,47,122,102,95
	.byte	100,114,105,118,101,114,47,122
	.byte	102,95,100,114,105,118,101,114
	.byte	95,117,97,114
	.byte	116,46,99
	.space	1
	.calls	'uart_set_interrupt_priority','debug_assert_handler'
	.calls	'uart_write_byte','uart_get_handle'
	.calls	'uart_write_byte','IfxAsclin_write8'
	.calls	'uart_write_buffer','uart_write_byte'
	.calls	'uart_write_string','uart_write_byte'
	.calls	'uart_read_byte','uart_get_handle'
	.calls	'uart_read_byte','IfxAsclin_read8'
	.calls	'uart_query_byte','uart_get_handle'
	.calls	'uart_query_byte','IfxAsclin_read8'
	.calls	'uart_tx_interrupt','IfxAsclin_getAddress'
	.calls	'uart_tx_interrupt','IfxAsclin_Asc_initModuleConfig'
	.calls	'uart_tx_interrupt','IfxAsclin_getSrcPointerTx'
	.calls	'uart_rx_interrupt','IfxAsclin_getAddress'
	.calls	'uart_rx_interrupt','IfxAsclin_Asc_initModuleConfig'
	.calls	'uart_rx_interrupt','IfxAsclin_getSrcPointerRx'
	.calls	'uart_sbus_init','IfxAsclin_getAddress'
	.calls	'uart_sbus_init','IfxAsclin_Asc_initModuleConfig'
	.calls	'uart_sbus_init','uart_set_buffer'
	.calls	'uart_sbus_init','uart_set_interrupt_priority'
	.calls	'uart_sbus_init','uart_mux'
	.calls	'uart_sbus_init','uart_get_handle'
	.calls	'uart_sbus_init','IfxAsclin_Asc_initModule'
	.calls	'uart_sbus_init','uart_rx_interrupt'
	.calls	'uart_sbus_init','uart_tx_interrupt'
	.calls	'uart_init','IfxAsclin_getAddress'
	.calls	'uart_init','IfxAsclin_Asc_initModuleConfig'
	.calls	'uart_init','uart_set_buffer'
	.calls	'uart_init','uart_set_interrupt_priority'
	.calls	'uart_init','uart_mux'
	.calls	'uart_init','uart_get_handle'
	.calls	'uart_init','IfxAsclin_Asc_initModule'
	.calls	'uart_init','uart_rx_interrupt'
	.calls	'uart_init','uart_tx_interrupt'
	.calls	'uart_set_interrupt_priority','',0
	.calls	'uart_set_buffer','',0
	.calls	'uart_get_handle','',0
	.calls	'uart_mux','',0
	.calls	'uart_write_byte','',8
	.calls	'uart_write_buffer','',0
	.calls	'uart_write_string','',0
	.calls	'uart_read_byte','',8
	.calls	'uart_query_byte','',0
	.calls	'uart_tx_interrupt','',0
	.calls	'uart_rx_interrupt','',0
	.calls	'uart_sbus_init','',32
	.extern	debug_assert_handler
	.extern	IfxAsclin0_RXA_P14_1_IN
	.extern	IfxAsclin0_RXB_P15_3_IN
	.extern	IfxAsclin1_RXA_P15_1_IN
	.extern	IfxAsclin1_RXB_P15_5_IN
	.extern	IfxAsclin1_RXC_P20_9_IN
	.extern	IfxAsclin1_RXE_P11_10_IN
	.extern	IfxAsclin1_RXF_P33_13_IN
	.extern	IfxAsclin1_RXG_P02_3_IN
	.extern	IfxAsclin2_RXA_P14_3_IN
	.extern	IfxAsclin2_RXB_P02_1_IN
	.extern	IfxAsclin2_RXD_P10_6_IN
	.extern	IfxAsclin2_RXE_P33_8_IN
	.extern	IfxAsclin2_RXG_P02_0_IN
	.extern	IfxAsclin3_RXA_P15_7_IN
	.extern	IfxAsclin3_RXC_P20_3_IN
	.extern	IfxAsclin3_RXE_P00_1_IN
	.extern	IfxAsclin3_RXF_P21_6_IN
	.extern	IfxAsclin0_TX_P14_0_OUT
	.extern	IfxAsclin0_TX_P14_1_OUT
	.extern	IfxAsclin0_TX_P15_2_OUT
	.extern	IfxAsclin0_TX_P15_3_OUT
	.extern	IfxAsclin1_TX_P02_2_OUT
	.extern	IfxAsclin1_TX_P11_12_OUT
	.extern	IfxAsclin1_TX_P15_0_OUT
	.extern	IfxAsclin1_TX_P15_1_OUT
	.extern	IfxAsclin1_TX_P15_4_OUT
	.extern	IfxAsclin1_TX_P15_5_OUT
	.extern	IfxAsclin1_TX_P20_10_OUT
	.extern	IfxAsclin1_TX_P33_12_OUT
	.extern	IfxAsclin1_TX_P33_13_OUT
	.extern	IfxAsclin2_TX_P02_0_OUT
	.extern	IfxAsclin2_TX_P10_5_OUT
	.extern	IfxAsclin2_TX_P14_2_OUT
	.extern	IfxAsclin2_TX_P14_3_OUT
	.extern	IfxAsclin2_TX_P33_8_OUT
	.extern	IfxAsclin2_TX_P33_9_OUT
	.extern	IfxAsclin3_TX_P00_0_OUT
	.extern	IfxAsclin3_TX_P00_1_OUT
	.extern	IfxAsclin3_TX_P15_6_OUT
	.extern	IfxAsclin3_TX_P15_7_OUT
	.extern	IfxAsclin3_TX_P20_0_OUT
	.extern	IfxAsclin3_TX_P20_3_OUT
	.extern	IfxAsclin3_TX_P21_7_OUT
	.extern	IfxAsclin_getAddress
	.extern	IfxAsclin_getSrcPointerRx
	.extern	IfxAsclin_getSrcPointerTx
	.extern	IfxAsclin_read8
	.extern	IfxAsclin_write8
	.extern	IfxAsclin_Asc_initModule
	.extern	IfxAsclin_Asc_initModuleConfig
	.calls	'uart_init','',32
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L183:
	.word	104299
	.half	3
	.word	.L184
	.byte	4
.L182:
	.byte	1
	.byte	'../libraries/zf_driver/zf_driver_uart.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L185
	.byte	2,1,1,3
	.word	202
	.byte	4
	.byte	'Ifx__jump_and_link',0,3,1,61,17,1,1,5
	.byte	'fun',0,1,61,43
	.word	205
	.byte	6,0,7
	.byte	'__fract',0,4,128,1,7
	.byte	'float',0,4,4,8
	.byte	'Ifx__float_to_fract',0,3,1,152,2,18
	.word	250
	.byte	1,1,5
	.byte	'a',0,1,152,2,44
	.word	262
	.byte	6,0,4
	.byte	'Ifx__stopPerfCounters',0,3,1,172,2,17,1,1,6,0,7
	.byte	'unsigned long long int',0,8,7,9
	.byte	'void',0,3
	.word	374
	.byte	8
	.byte	'__ld64',0,3,2,135,1,19
	.word	348
	.byte	1,1,5
	.byte	'addr',0,2,135,1,32
	.word	380
	.byte	6,0,4
	.byte	'__st64',0,3,2,143,1,17,1,1,5
	.byte	'addr',0,2,143,1,30
	.word	380
	.byte	5
	.byte	'value',0,2,143,1,43
	.word	348
	.byte	6,0,7
	.byte	'unsigned int',0,4,7,7
	.byte	'int',0,4,5,7
	.byte	'unsigned int',0,4,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON0_Bits',0,4,241,8,16,4,11
	.byte	'ENDINIT',0,4
	.word	489
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	489
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	489
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	489
	.byte	16,0,2,35,0,0,12,4,247,14,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	505
	.byte	4,2,35,0,0
.L299:
	.byte	7
	.byte	'unsigned char',0,1,8,7
	.byte	'unsigned short int',0,2,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON1_Bits',0,4,250,8,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	2,6,2,35,0,11
	.byte	'IR0',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	641
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	658
	.byte	16,0,2,35,2,0,12,4,255,14,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	680
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_WDTCPU_SR_Bits',0,4,137,9,16,4,11
	.byte	'AE',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	641
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	658
	.byte	16,0,2,35,2,0,12,4,135,15,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	924
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_WDTCPU',0,4,175,15,25,12,13
	.byte	'CON0',0
	.word	601
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	884
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	1115
	.byte	4,2,35,8,0,14
	.word	1155
	.byte	3
	.word	1218
	.byte	4
	.byte	'IfxScuWdt_clearCpuEndinitInline',0,3,3,181,3,17,1,1,5
	.byte	'watchdog',0,3,181,3,65
	.word	1223
	.byte	5
	.byte	'password',0,3,181,3,82
	.word	658
	.byte	6,0,4
	.byte	'IfxScuWdt_setCpuEndinitInline',0,3,3,140,4,17,1,1,5
	.byte	'watchdog',0,3,140,4,63
	.word	1223
	.byte	5
	.byte	'password',0,3,140,4,80
	.word	658
	.byte	6,0,8
	.byte	'IfxScuWdt_getCpuWatchdogPasswordInline',0,3,3,227,3,19
	.word	658
	.byte	1,1,5
	.byte	'watchdog',0,3,227,3,74
	.word	1223
	.byte	6,0,10
	.byte	'_Ifx_P_OUT_Bits',0,6,143,3,16,4,11
	.byte	'P0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	641
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	641
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	658
	.byte	16,0,2,35,2,0,12,6,181,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1453
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMR_Bits',0,6,169,2,16,4,11
	.byte	'PS0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	641
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	641
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'PCL0',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	641
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	641
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	641
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	641
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	641
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	641
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	641
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	641
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	641
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	641
	.byte	1,0,2,35,3,0,12,6,133,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1769
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ID_Bits',0,6,110,16,4,11
	.byte	'MODREV',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	658
	.byte	16,0,2,35,2,0,12,6,148,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2340
	.byte	4,2,35,0,0,15,4
	.word	641
	.byte	16,3,0,10
	.byte	'_Ifx_P_IOCR0_Bits',0,6,140,1,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	3,5,2,35,0,11
	.byte	'PC0',0,1
	.word	641
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	641
	.byte	3,5,2,35,1,11
	.byte	'PC1',0,1
	.word	641
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	641
	.byte	3,5,2,35,2,11
	.byte	'PC2',0,1
	.word	641
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	641
	.byte	3,5,2,35,3,11
	.byte	'PC3',0,1
	.word	641
	.byte	5,0,2,35,3,0,12,6,164,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2468
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR4_Bits',0,6,166,1,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	3,5,2,35,0,11
	.byte	'PC4',0,1
	.word	641
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	641
	.byte	3,5,2,35,1,11
	.byte	'PC5',0,1
	.word	641
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	641
	.byte	3,5,2,35,2,11
	.byte	'PC6',0,1
	.word	641
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	641
	.byte	3,5,2,35,3,11
	.byte	'PC7',0,1
	.word	641
	.byte	5,0,2,35,3,0,12,6,180,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2683
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR8_Bits',0,6,179,1,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	3,5,2,35,0,11
	.byte	'PC8',0,1
	.word	641
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	641
	.byte	3,5,2,35,1,11
	.byte	'PC9',0,1
	.word	641
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	641
	.byte	3,5,2,35,2,11
	.byte	'PC10',0,1
	.word	641
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	641
	.byte	3,5,2,35,3,11
	.byte	'PC11',0,1
	.word	641
	.byte	5,0,2,35,3,0,12,6,188,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2898
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR12_Bits',0,6,153,1,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	3,5,2,35,0,11
	.byte	'PC12',0,1
	.word	641
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	641
	.byte	3,5,2,35,1,11
	.byte	'PC13',0,1
	.word	641
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	641
	.byte	3,5,2,35,2,11
	.byte	'PC14',0,1
	.word	641
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	641
	.byte	3,5,2,35,3,11
	.byte	'PC15',0,1
	.word	641
	.byte	5,0,2,35,3,0,12,6,172,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3115
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IN_Bits',0,6,118,16,4,11
	.byte	'P0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	641
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	641
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	658
	.byte	16,0,2,35,2,0,12,6,156,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3335
	.byte	4,2,35,0,0,15,24
	.word	641
	.byte	16,23,0,10
	.byte	'_Ifx_P_PDR0_Bits',0,6,205,3,16,4,11
	.byte	'PD0',0,1
	.word	641
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	641
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'PD2',0,1
	.word	641
	.byte	3,5,2,35,1,11
	.byte	'PL2',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'PD3',0,1
	.word	641
	.byte	3,1,2,35,1,11
	.byte	'PL3',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'PD4',0,1
	.word	641
	.byte	3,5,2,35,2,11
	.byte	'PL4',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'PD5',0,1
	.word	641
	.byte	3,1,2,35,2,11
	.byte	'PL5',0,1
	.word	641
	.byte	1,0,2,35,2,11
	.byte	'PD6',0,1
	.word	641
	.byte	3,5,2,35,3,11
	.byte	'PL6',0,1
	.word	641
	.byte	1,4,2,35,3,11
	.byte	'PD7',0,1
	.word	641
	.byte	3,1,2,35,3,11
	.byte	'PL7',0,1
	.word	641
	.byte	1,0,2,35,3,0,12,6,205,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3658
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PDR1_Bits',0,6,226,3,16,4,11
	.byte	'PD8',0,1
	.word	641
	.byte	3,5,2,35,0,11
	.byte	'PL8',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'PD9',0,1
	.word	641
	.byte	3,1,2,35,0,11
	.byte	'PL9',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'PD10',0,1
	.word	641
	.byte	3,5,2,35,1,11
	.byte	'PL10',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'PD11',0,1
	.word	641
	.byte	3,1,2,35,1,11
	.byte	'PL11',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'PD12',0,1
	.word	641
	.byte	3,5,2,35,2,11
	.byte	'PL12',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'PD13',0,1
	.word	641
	.byte	3,1,2,35,2,11
	.byte	'PL13',0,1
	.word	641
	.byte	1,0,2,35,2,11
	.byte	'PD14',0,1
	.word	641
	.byte	3,5,2,35,3,11
	.byte	'PL14',0,1
	.word	641
	.byte	1,4,2,35,3,11
	.byte	'PD15',0,1
	.word	641
	.byte	3,1,2,35,3,11
	.byte	'PL15',0,1
	.word	641
	.byte	1,0,2,35,3,0,12,6,213,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3962
	.byte	4,2,35,0,0,15,8
	.word	641
	.byte	16,7,0,10
	.byte	'_Ifx_P_ESR_Bits',0,6,88,16,4,11
	.byte	'EN0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	641
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	641
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	658
	.byte	16,0,2,35,2,0,12,6,140,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4287
	.byte	4,2,35,0,0,15,12
	.word	641
	.byte	16,11,0,10
	.byte	'_Ifx_P_PDISC_Bits',0,6,183,3,16,4,11
	.byte	'PDIS0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'PDIS2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'PDIS3',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'PDIS4',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'PDIS5',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'PDIS6',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'PDIS7',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'PDIS8',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'PDIS9',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'PDIS10',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'PDIS11',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'PDIS12',0,1
	.word	641
	.byte	1,3,2,35,1,11
	.byte	'PDIS13',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'PDIS14',0,1
	.word	641
	.byte	1,1,2,35,1,11
	.byte	'PDIS15',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	658
	.byte	16,0,2,35,2,0,12,6,197,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4627
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PCSR_Bits',0,6,165,3,16,4,11
	.byte	'SEL0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'SEL1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'SEL2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'SEL3',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'SEL4',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'SEL5',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'SEL6',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'SEL7',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	641
	.byte	2,6,2,35,1,11
	.byte	'SEL10',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'SEL11',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	466
	.byte	19,1,2,35,0,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,12,6,189,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4993
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR0_Bits',0,6,206,2,16,4,11
	.byte	'PS0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	466
	.byte	28,0,2,35,0,0,12,6,149,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5279
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR4_Bits',0,6,227,2,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	4,4,2,35,0,11
	.byte	'PS4',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	466
	.byte	24,0,2,35,0,0,12,6,165,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5426
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR8_Bits',0,6,238,2,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'PS8',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	466
	.byte	20,0,2,35,0,0,12,6,173,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5595
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR12_Bits',0,6,216,2,16,4,11
	.byte	'reserved_0',0,2
	.word	658
	.byte	12,4,2,35,0,11
	.byte	'PS12',0,1
	.word	641
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	641
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	658
	.byte	16,0,2,35,2,0,12,6,157,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5767
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR0_Bits',0,6,232,1,16,4,11
	.byte	'reserved_0',0,2
	.word	658
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	658
	.byte	12,0,2,35,2,0,12,6,229,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5942
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR4_Bits',0,6,253,1,16,4,11
	.byte	'reserved_0',0,4
	.word	466
	.byte	20,12,2,35,0,11
	.byte	'PCL4',0,1
	.word	641
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	641
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	641
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	641
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	641
	.byte	8,0,2,35,3,0,12,6,245,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6116
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR8_Bits',0,6,136,2,16,4,11
	.byte	'reserved_0',0,4
	.word	466
	.byte	24,8,2,35,0,11
	.byte	'PCL8',0,1
	.word	641
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	641
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	641
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	641
	.byte	4,0,2,35,3,0,12,6,253,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6290
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR12_Bits',0,6,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	466
	.byte	28,4,2,35,0,11
	.byte	'PCL12',0,1
	.word	641
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	641
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	641
	.byte	1,0,2,35,3,0,12,6,237,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6466
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR_Bits',0,6,249,2,16,4,11
	.byte	'PS0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	641
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	641
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	658
	.byte	16,0,2,35,2,0,12,6,141,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6622
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR_Bits',0,6,147,2,16,4,11
	.byte	'reserved_0',0,2
	.word	658
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	641
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	641
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	641
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	641
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	641
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	641
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	641
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	641
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	641
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	641
	.byte	1,0,2,35,3,0,12,6,221,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6955
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR0_Bits',0,6,192,1,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	466
	.byte	30,0,2,35,0,0,12,6,196,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7303
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_Bits',0,6,200,1,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	466
	.byte	30,0,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_P21_Bits',0,6,208,1,16,4,11
	.byte	'RDIS_CTRL',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'RX_DIS',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'TERM',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'LRXTERM',0,1
	.word	641
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	466
	.byte	24,0,2,35,0,0,12,6,204,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7427
	.byte	4,2,35,0,13
	.byte	'B_P21',0
	.word	7511
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR2_Bits',0,6,218,1,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'LVDSR',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'LVDSRL',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	641
	.byte	2,4,2,35,1,11
	.byte	'TDIS_CTRL',0,1
	.word	641
	.byte	1,3,2,35,1,11
	.byte	'TX_DIS',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'TX_PD',0,1
	.word	641
	.byte	1,1,2,35,1,11
	.byte	'TX_PWDPD',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	658
	.byte	16,0,2,35,2,0,12,6,213,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7691
	.byte	4,2,35,0,0,15,76
	.word	641
	.byte	16,75,0,10
	.byte	'_Ifx_P_ACCEN1_Bits',0,6,82,16,4,11
	.byte	'reserved_0',0,4
	.word	466
	.byte	32,0,2,35,0,0,12,6,132,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7944
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ACCEN0_Bits',0,6,45,16,4,11
	.byte	'EN0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	641
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	641
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	641
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	641
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	641
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	641
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	641
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	641
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	641
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	641
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	641
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	641
	.byte	1,0,2,35,3,0,12,6,252,3,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8031
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P',0,6,229,5,25,128,2,13
	.byte	'OUT',0
	.word	1729
	.byte	4,2,35,0,13
	.byte	'OMR',0
	.word	2300
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	2419
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	2459
	.byte	4,2,35,12,13
	.byte	'IOCR0',0
	.word	2643
	.byte	4,2,35,16,13
	.byte	'IOCR4',0
	.word	2858
	.byte	4,2,35,20,13
	.byte	'IOCR8',0
	.word	3075
	.byte	4,2,35,24,13
	.byte	'IOCR12',0
	.word	3295
	.byte	4,2,35,28,13
	.byte	'reserved_20',0
	.word	2459
	.byte	4,2,35,32,13
	.byte	'IN',0
	.word	3609
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	3649
	.byte	24,2,35,40,13
	.byte	'PDR0',0
	.word	3922
	.byte	4,2,35,64,13
	.byte	'PDR1',0
	.word	4238
	.byte	4,2,35,68,13
	.byte	'reserved_48',0
	.word	4278
	.byte	8,2,35,72,13
	.byte	'ESR',0
	.word	4578
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	4618
	.byte	12,2,35,84,13
	.byte	'PDISC',0
	.word	4953
	.byte	4,2,35,96,13
	.byte	'PCSR',0
	.word	5239
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	4278
	.byte	8,2,35,104,13
	.byte	'OMSR0',0
	.word	5386
	.byte	4,2,35,112,13
	.byte	'OMSR4',0
	.word	5555
	.byte	4,2,35,116,13
	.byte	'OMSR8',0
	.word	5727
	.byte	4,2,35,120,13
	.byte	'OMSR12',0
	.word	5902
	.byte	4,2,35,124,13
	.byte	'OMCR0',0
	.word	6076
	.byte	4,3,35,128,1,13
	.byte	'OMCR4',0
	.word	6250
	.byte	4,3,35,132,1,13
	.byte	'OMCR8',0
	.word	6426
	.byte	4,3,35,136,1,13
	.byte	'OMCR12',0
	.word	6582
	.byte	4,3,35,140,1,13
	.byte	'OMSR',0
	.word	6915
	.byte	4,3,35,144,1,13
	.byte	'OMCR',0
	.word	7263
	.byte	4,3,35,148,1,13
	.byte	'reserved_98',0
	.word	4278
	.byte	8,3,35,152,1,13
	.byte	'LPCR0',0
	.word	7387
	.byte	4,3,35,160,1,13
	.byte	'LPCR1',0
	.word	7636
	.byte	4,3,35,164,1,13
	.byte	'LPCR2',0
	.word	7895
	.byte	4,3,35,168,1,13
	.byte	'reserved_A4',0
	.word	7935
	.byte	76,3,35,172,1,13
	.byte	'ACCEN1',0
	.word	7991
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	8558
	.byte	4,3,35,252,1,0,14
	.word	8598
	.byte	3
	.word	9201
	.byte	17,5,83,9,1,18
	.byte	'IfxPort_InputMode_undefined',0,127,18
	.byte	'IfxPort_InputMode_noPullDevice',0,0,18
	.byte	'IfxPort_InputMode_pullDown',0,8,18
	.byte	'IfxPort_InputMode_pullUp',0,16,0,4
	.byte	'IfxPort_setPinModeInput',0,3,5,196,4,17,1,1,5
	.byte	'port',0,5,196,4,48
	.word	9206
	.byte	5
	.byte	'pinIndex',0,5,196,4,60
	.word	641
	.byte	5
	.byte	'mode',0,5,196,4,88
	.word	9211
	.byte	6,0,17,5,134,1,9,1,18
	.byte	'IfxPort_OutputMode_pushPull',0,128,1,18
	.byte	'IfxPort_OutputMode_openDrain',0,192,1,0,17,5,120,9,1,18
	.byte	'IfxPort_OutputIdx_general',0,128,1,18
	.byte	'IfxPort_OutputIdx_alt1',0,136,1,18
	.byte	'IfxPort_OutputIdx_alt2',0,144,1,18
	.byte	'IfxPort_OutputIdx_alt3',0,152,1,18
	.byte	'IfxPort_OutputIdx_alt4',0,160,1,18
	.byte	'IfxPort_OutputIdx_alt5',0,168,1,18
	.byte	'IfxPort_OutputIdx_alt6',0,176,1,18
	.byte	'IfxPort_OutputIdx_alt7',0,184,1,0,4
	.byte	'IfxPort_setPinModeOutput',0,3,5,202,4,17,1,1,5
	.byte	'port',0,5,202,4,49
	.word	9206
	.byte	5
	.byte	'pinIndex',0,5,202,4,61
	.word	641
	.byte	5
	.byte	'mode',0,5,202,4,90
	.word	9416
	.byte	5
	.byte	'index',0,5,202,4,114
	.word	9486
	.byte	6,0,17,5,172,1,9,4,18
	.byte	'IfxPort_State_notChanged',0,0,18
	.byte	'IfxPort_State_high',0,1,18
	.byte	'IfxPort_State_low',0,128,128,4,18
	.byte	'IfxPort_State_toggled',0,129,128,4,0,4
	.byte	'IfxPort_setPinState',0,3,5,208,4,17,1,1,5
	.byte	'port',0,5,208,4,44
	.word	9206
	.byte	5
	.byte	'pinIndex',0,5,208,4,56
	.word	641
	.byte	5
	.byte	'action',0,5,208,4,80
	.word	9799
	.byte	6,0,8
	.byte	'IfxScuCcu_getStmFrequency',0,3,7,226,8,20
	.word	262
	.byte	1,1,6,0,17,9,156,1,9,1,18
	.byte	'IfxCpu_ResourceCpu_0',0,0,18
	.byte	'IfxCpu_ResourceCpu_1',0,1,18
	.byte	'IfxCpu_ResourceCpu_none',0,2,0,8
	.byte	'IfxCpu_getCoreIndex',0,3,8,141,6,31
	.word	10020
	.byte	1,1,6,0
.L376:
	.byte	8
	.byte	'IfxCpu_areInterruptsEnabled',0,3,8,139,5,20
	.word	641
	.byte	1,1
.L377:
	.byte	6,0
.L373:
	.byte	8
	.byte	'IfxCpu_disableInterrupts',0,3,8,147,5,20
	.word	641
	.byte	1,1
.L374:
	.byte	19,6,0,0,4
	.byte	'IfxCpu_enableInterrupts',0,3,8,157,5,17,1,1,6,0,4
	.byte	'IfxCpu_forceDisableInterrupts',0,3,8,225,5,17,1,1,6,0
.L388:
	.byte	4
	.byte	'IfxCpu_restoreInterrupts',0,3,8,168,7,17,1,1
.L389:
	.byte	5
	.byte	'enabled',0,8,168,7,50
	.word	641
.L391:
	.byte	6,0
.L293:
	.byte	7
	.byte	'unsigned long int',0,4,7,8
	.byte	'IfxCpu_getPerformanceCounter',0,3,8,161,6,19
	.word	10342
	.byte	1,1,5
	.byte	'address',0,8,161,6,55
	.word	658
	.byte	6,0,8
	.byte	'IfxCpu_getPerformanceCounterStickyOverflow',0,3,8,190,6,20
	.word	641
	.byte	1,1,5
	.byte	'address',0,8,190,6,70
	.word	658
	.byte	6,0,4
	.byte	'IfxCpu_updatePerformanceCounter',0,3,8,172,8,17,1,1,5
	.byte	'address',0,8,172,8,56
	.word	10342
	.byte	5
	.byte	'count',0,8,172,8,72
	.word	10342
	.byte	19,6,0,0,10
	.byte	'_Ifx_SRC_SRCR_Bits',0,11,45,16,4,11
	.byte	'SRPN',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	641
	.byte	2,6,2,35,1,11
	.byte	'SRE',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'TOS',0,1
	.word	641
	.byte	2,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	641
	.byte	3,0,2,35,1,11
	.byte	'ECC',0,1
	.word	641
	.byte	6,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	641
	.byte	2,0,2,35,2,11
	.byte	'SRR',0,1
	.word	641
	.byte	1,7,2,35,3,11
	.byte	'CLRR',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'SETR',0,1
	.word	641
	.byte	1,5,2,35,3,11
	.byte	'IOV',0,1
	.word	641
	.byte	1,4,2,35,3,11
	.byte	'IOVCLR',0,1
	.word	641
	.byte	1,3,2,35,3,11
	.byte	'SWS',0,1
	.word	641
	.byte	1,2,2,35,3,11
	.byte	'SWSCLR',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	641
	.byte	1,0,2,35,3,0,12,11,70,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10573
	.byte	4,2,35,0,0,14
	.word	10863
.L322:
	.byte	3
	.word	10902
	.byte	4
	.byte	'IfxSrc_clearRequest',0,3,10,250,1,17,1,1,5
	.byte	'src',0,10,250,1,60
	.word	10907
	.byte	6,0
.L339:
	.byte	4
	.byte	'IfxSrc_disable',0,3,10,134,2,17,1,1
.L340:
	.byte	5
	.byte	'src',0,10,134,2,55
	.word	10907
.L342:
	.byte	6,0
.L333:
	.byte	4
	.byte	'IfxSrc_enable',0,3,10,140,2,17,1,1
.L336:
	.byte	5
	.byte	'src',0,10,140,2,54
	.word	10907
.L338:
	.byte	6,0,10
	.byte	'_Ifx_STM_CLC_Bits',0,13,100,16,4,11
	.byte	'DISR',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'DISS',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'EDIS',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	466
	.byte	28,0,2,35,0,0,12,13,149,2,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11030
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_ID_Bits',0,13,142,1,16,4,11
	.byte	'MODREV',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	658
	.byte	16,0,2,35,2,0,12,13,181,2,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11186
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM0_Bits',0,13,192,1,16,4,11
	.byte	'STM31_0',0,4
	.word	466
	.byte	32,0,2,35,0,0,12,13,229,2,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11308
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM1_Bits',0,13,204,1,16,4,11
	.byte	'STM35_4',0,4
	.word	466
	.byte	32,0,2,35,0,0,12,13,245,2,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11393
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM2_Bits',0,13,210,1,16,4,11
	.byte	'STM39_8',0,4
	.word	466
	.byte	32,0,2,35,0,0,12,13,253,2,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11478
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM3_Bits',0,13,216,1,16,4,11
	.byte	'STM43_12',0,4
	.word	466
	.byte	32,0,2,35,0,0,12,13,133,3,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11563
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM4_Bits',0,13,222,1,16,4,11
	.byte	'STM47_16',0,4
	.word	466
	.byte	32,0,2,35,0,0,12,13,141,3,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11649
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM5_Bits',0,13,228,1,16,4,11
	.byte	'STM51_20',0,4
	.word	466
	.byte	32,0,2,35,0,0,12,13,149,3,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11735
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM6_Bits',0,13,234,1,16,4,11
	.byte	'STM63_32',0,4
	.word	466
	.byte	32,0,2,35,0,0,12,13,157,3,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11821
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_CAP_Bits',0,13,88,16,4,11
	.byte	'STMCAP63_32',0,4
	.word	466
	.byte	32,0,2,35,0,0,12,13,133,2,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11907
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_CMP_Bits',0,13,123,16,4,11
	.byte	'CMPVAL',0,4
	.word	466
	.byte	32,0,2,35,0,0,12,13,165,2,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11994
	.byte	4,2,35,0,0,15,8
	.word	12036
	.byte	16,1,0,10
	.byte	'_Ifx_STM_CMCON_Bits',0,13,110,16,4,11
	.byte	'MSIZE0',0,1
	.word	641
	.byte	5,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	641
	.byte	3,0,2,35,0,11
	.byte	'MSTART0',0,1
	.word	641
	.byte	5,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	641
	.byte	3,0,2,35,1,11
	.byte	'MSIZE1',0,1
	.word	641
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	641
	.byte	3,0,2,35,2,11
	.byte	'MSTART1',0,1
	.word	641
	.byte	5,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	641
	.byte	3,0,2,35,3,0,12,13,157,2,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12085
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_ICR_Bits',0,13,129,1,16,4,11
	.byte	'CMP0EN',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'CMP0IR',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'CMP0OS',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'CMP1EN',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'CMP1IR',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'CMP1OS',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,4
	.word	466
	.byte	25,0,2,35,0,0,12,13,173,2,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12316
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_ISCR_Bits',0,13,150,1,16,4,11
	.byte	'CMP0IRR',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'CMP0IRS',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'CMP1IRR',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'CMP1IRS',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	466
	.byte	28,0,2,35,0,0,12,13,189,2,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12533
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM0SV_Bits',0,13,198,1,16,4,11
	.byte	'STM31_0',0,4
	.word	466
	.byte	32,0,2,35,0,0,12,13,237,2,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12697
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_CAPSV_Bits',0,13,94,16,4,11
	.byte	'STMCAP63_32',0,4
	.word	466
	.byte	32,0,2,35,0,0,12,13,141,2,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12784
	.byte	4,2,35,0,0,15,144,1
	.word	641
	.byte	16,143,1,0,10
	.byte	'_Ifx_STM_OCS_Bits',0,13,182,1,16,4,11
	.byte	'reserved_0',0,4
	.word	466
	.byte	24,8,2,35,0,11
	.byte	'SUS',0,1
	.word	641
	.byte	4,4,2,35,3,11
	.byte	'SUS_P',0,1
	.word	641
	.byte	1,3,2,35,3,11
	.byte	'SUSSTA',0,1
	.word	641
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	641
	.byte	2,0,2,35,3,0,12,13,221,2,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12884
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_KRSTCLR_Bits',0,13,175,1,16,4,11
	.byte	'CLR',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	466
	.byte	31,0,2,35,0,0,12,13,213,2,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13044
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_KRST1_Bits',0,13,168,1,16,4,11
	.byte	'RST',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	466
	.byte	31,0,2,35,0,0,12,13,205,2,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13150
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_KRST0_Bits',0,13,160,1,16,4,11
	.byte	'RST',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'RSTSTAT',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	466
	.byte	30,0,2,35,0,0,12,13,197,2,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13254
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_ACCEN1_Bits',0,13,82,16,4,11
	.byte	'reserved_0',0,4
	.word	466
	.byte	32,0,2,35,0,0,12,13,253,1,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13377
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_ACCEN0_Bits',0,13,45,16,4,11
	.byte	'EN0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	641
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	641
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	641
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	641
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	641
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	641
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	641
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	641
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	641
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	641
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	641
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	641
	.byte	1,0,2,35,3,0,12,13,245,1,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13466
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM',0,13,173,3,25,128,2,13
	.byte	'CLC',0
	.word	11146
	.byte	4,2,35,0,13
	.byte	'reserved_4',0
	.word	2459
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	11268
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	2459
	.byte	4,2,35,12,13
	.byte	'TIM0',0
	.word	11353
	.byte	4,2,35,16,13
	.byte	'TIM1',0
	.word	11438
	.byte	4,2,35,20,13
	.byte	'TIM2',0
	.word	11523
	.byte	4,2,35,24,13
	.byte	'TIM3',0
	.word	11609
	.byte	4,2,35,28,13
	.byte	'TIM4',0
	.word	11695
	.byte	4,2,35,32,13
	.byte	'TIM5',0
	.word	11781
	.byte	4,2,35,36,13
	.byte	'TIM6',0
	.word	11867
	.byte	4,2,35,40,13
	.byte	'CAP',0
	.word	11954
	.byte	4,2,35,44,13
	.byte	'CMP',0
	.word	12076
	.byte	8,2,35,48,13
	.byte	'CMCON',0
	.word	12276
	.byte	4,2,35,56,13
	.byte	'ICR',0
	.word	12493
	.byte	4,2,35,60,13
	.byte	'ISCR',0
	.word	12657
	.byte	4,2,35,64,13
	.byte	'reserved_44',0
	.word	4618
	.byte	12,2,35,68,13
	.byte	'TIM0SV',0
	.word	12744
	.byte	4,2,35,80,13
	.byte	'CAPSV',0
	.word	12833
	.byte	4,2,35,84,13
	.byte	'reserved_58',0
	.word	12873
	.byte	144,1,2,35,88,13
	.byte	'OCS',0
	.word	13004
	.byte	4,3,35,232,1,13
	.byte	'KRSTCLR',0
	.word	13110
	.byte	4,3,35,236,1,13
	.byte	'KRST1',0
	.word	13214
	.byte	4,3,35,240,1,13
	.byte	'KRST0',0
	.word	13337
	.byte	4,3,35,244,1,13
	.byte	'ACCEN1',0
	.word	13426
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	13995
	.byte	4,3,35,252,1,0,14
	.word	14035
	.byte	3
	.word	14455
	.byte	8
	.byte	'IfxStm_get',0,3,12,162,4,19
	.word	348
	.byte	1,1,5
	.byte	'stm',0,12,162,4,39
	.word	14460
	.byte	6,0,8
	.byte	'IfxStm_getFrequency',0,3,12,179,4,20
	.word	262
	.byte	1,1,5
	.byte	'stm',0,12,179,4,49
	.word	14460
	.byte	19,6,6,0,0,8
	.byte	'IfxStm_getLower',0,3,12,190,4,19
	.word	10342
	.byte	1,1,5
	.byte	'stm',0,12,190,4,44
	.word	14460
	.byte	6,0
.L370:
	.byte	8
	.byte	'disableInterrupts',0,3,14,108,20
	.word	641
	.byte	1,1
.L372:
	.byte	19,6,0,0
.L383:
	.byte	4
	.byte	'restoreInterrupts',0,3,14,142,1,17,1,1
.L385:
	.byte	5
	.byte	'enabled',0,14,142,1,43
	.word	641
.L387:
	.byte	19,6,0,0,7
	.byte	'long long int',0,8,5,8
	.byte	'getDeadLine',0,3,14,164,2,25
	.word	14676
	.byte	1,1,5
	.byte	'timeout',0,14,164,2,50
	.word	14676
	.byte	19,6,0,0,8
	.byte	'isDeadLine',0,3,14,211,2,20
	.word	641
	.byte	1,1,5
	.byte	'deadLine',0,14,211,2,44
	.word	14676
	.byte	19,6,0,0,8
	.byte	'now',0,3,14,221,1,25
	.word	14676
	.byte	1,1,19,6,6,6,0,0,8
	.byte	'nowWithoutCriticalSection',0,3,14,240,1,25
	.word	14676
	.byte	1,1,19,6,0,0,10
	.byte	'_Ifx_ASCLIN_CLC_Bits',0,16,118,16,4,11
	.byte	'DISR',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'DISS',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'EDIS',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	466
	.byte	28,0,2,35,0,0,12,16,207,3,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14847
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_IOCR_Bits',0,16,169,2,16,4,11
	.byte	'ALTI',0,1
	.word	641
	.byte	3,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'DEPTH',0,2
	.word	658
	.byte	6,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	641
	.byte	6,0,2,35,1,11
	.byte	'CTS',0,1
	.word	641
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	658
	.byte	7,7,2,35,2,11
	.byte	'RCPOL',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'CPOL',0,1
	.word	641
	.byte	1,5,2,35,3,11
	.byte	'SPOL',0,1
	.word	641
	.byte	1,4,2,35,3,11
	.byte	'LB',0,1
	.word	641
	.byte	1,3,2,35,3,11
	.byte	'CTSEN',0,1
	.word	641
	.byte	1,2,2,35,3,11
	.byte	'RXM',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'TXM',0,1
	.word	641
	.byte	1,0,2,35,3,0,12,16,151,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15006
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_ID_Bits',0,16,161,2,16,4,11
	.byte	'MODREV',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	658
	.byte	16,0,2,35,2,0,12,16,143,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15301
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_TXFIFOCON_Bits',0,16,149,3,16,4,11
	.byte	'FLUSH',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'ENO',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	641
	.byte	4,2,2,35,0,11
	.byte	'INW',0,1
	.word	641
	.byte	2,0,2,35,0,11
	.byte	'INTLEVEL',0,1
	.word	641
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	641
	.byte	4,0,2,35,1,11
	.byte	'FILL',0,1
	.word	641
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,2
	.word	658
	.byte	11,0,2,35,2,0,12,16,247,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15426
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_RXFIFOCON_Bits',0,16,129,3,16,4,11
	.byte	'FLUSH',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'ENI',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	641
	.byte	4,2,2,35,0,11
	.byte	'OUTW',0,1
	.word	641
	.byte	2,0,2,35,0,11
	.byte	'INTLEVEL',0,1
	.word	641
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	641
	.byte	4,0,2,35,1,11
	.byte	'FILL',0,1
	.word	641
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,2
	.word	658
	.byte	10,1,2,35,2,11
	.byte	'BUF',0,1
	.word	641
	.byte	1,0,2,35,3,0,12,16,231,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15651
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_BITCON_Bits',0,16,88,16,4,11
	.byte	'PRESCALER',0,2
	.word	658
	.byte	12,4,2,35,0,11
	.byte	'reserved_12',0,1
	.word	641
	.byte	4,0,2,35,1,11
	.byte	'OVERSAMPLING',0,1
	.word	641
	.byte	4,4,2,35,2,11
	.byte	'reserved_20',0,1
	.word	641
	.byte	4,0,2,35,2,11
	.byte	'SAMPLEPOINT',0,1
	.word	641
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	641
	.byte	3,1,2,35,3,11
	.byte	'SM',0,1
	.word	641
	.byte	1,0,2,35,3,0,12,16,183,3,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15892
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_FRAMECON_Bits',0,16,145,2,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	6,2,2,35,0,11
	.byte	'IDLE',0,2
	.word	658
	.byte	3,7,2,35,0,11
	.byte	'STOP',0,1
	.word	641
	.byte	3,4,2,35,1,11
	.byte	'LEAD',0,1
	.word	641
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'MODE',0,1
	.word	641
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	658
	.byte	10,4,2,35,2,11
	.byte	'MSB',0,1
	.word	641
	.byte	1,3,2,35,3,11
	.byte	'CEN',0,1
	.word	641
	.byte	1,2,2,35,3,11
	.byte	'PEN',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'ODD',0,1
	.word	641
	.byte	1,0,2,35,3,0,12,16,135,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16113
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_DATCON_Bits',0,16,136,1,16,4,11
	.byte	'DATLEN',0,1
	.word	641
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	658
	.byte	9,3,2,35,0,11
	.byte	'HO',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'RM',0,1
	.word	641
	.byte	1,1,2,35,1,11
	.byte	'CSM',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'RESPONSE',0,1
	.word	641
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	641
	.byte	8,0,2,35,3,0,12,16,223,3,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16378
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_BRG_Bits',0,16,109,16,4,11
	.byte	'DENOMINATOR',0,2
	.word	658
	.byte	12,4,2,35,0,11
	.byte	'reserved_12',0,1
	.word	641
	.byte	4,0,2,35,1,11
	.byte	'NUMERATOR',0,2
	.word	658
	.byte	12,4,2,35,2,11
	.byte	'reserved_28',0,1
	.word	641
	.byte	4,0,2,35,3,0,12,16,199,3,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16575
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_BRD_Bits',0,16,100,16,4,11
	.byte	'LOWERLIMIT',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'UPPERLIMIT',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'MEASURED',0,2
	.word	658
	.byte	12,4,2,35,2,11
	.byte	'reserved_28',0,1
	.word	641
	.byte	4,0,2,35,3,0,12,16,191,3,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16732
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_LIN_CON_Bits',0,16,216,2,16,4,11
	.byte	'reserved_0',0,4
	.word	466
	.byte	23,9,2,35,0,11
	.byte	'CSI',0,1
	.word	641
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	641
	.byte	1,7,2,35,3,11
	.byte	'CSEN',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'MS',0,1
	.word	641
	.byte	1,5,2,35,3,11
	.byte	'ABD',0,1
	.word	641
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	641
	.byte	4,0,2,35,3,0,12,16,191,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16886
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_LIN_BTIMER_Bits',0,16,209,2,16,4,11
	.byte	'BREAK',0,1
	.word	641
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	466
	.byte	26,0,2,35,0,0,12,16,183,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17086
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_LIN_HTIMER_Bits',0,16,228,2,16,4,11
	.byte	'HEADER',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	466
	.byte	24,0,2,35,0,0,12,16,199,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17200
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_LIN',0,16,135,5,25,12,13
	.byte	'CON',0
	.word	17046
	.byte	4,2,35,0,13
	.byte	'BTIMER',0
	.word	17160
	.byte	4,2,35,4,13
	.byte	'HTIMER',0
	.word	17275
	.byte	4,2,35,8,0,14
	.word	17315
	.byte	10
	.byte	'_Ifx_ASCLIN_FLAGS_Bits',0,16,148,1,16,4,11
	.byte	'TH',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'TR',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'RH',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'RR',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'FED',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'RED',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	658
	.byte	6,3,2,35,0,11
	.byte	'TWRQ',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'THRQ',0,1
	.word	641
	.byte	1,1,2,35,1,11
	.byte	'TRRQ',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'PE',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'TC',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'FE',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'HT',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'RT',0,1
	.word	641
	.byte	1,3,2,35,2,11
	.byte	'BD',0,1
	.word	641
	.byte	1,2,2,35,2,11
	.byte	'LP',0,1
	.word	641
	.byte	1,1,2,35,2,11
	.byte	'LA',0,1
	.word	641
	.byte	1,0,2,35,2,11
	.byte	'LC',0,1
	.word	641
	.byte	1,7,2,35,3,11
	.byte	'CE',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'RFO',0,1
	.word	641
	.byte	1,5,2,35,3,11
	.byte	'RFU',0,1
	.word	641
	.byte	1,4,2,35,3,11
	.byte	'RFL',0,1
	.word	641
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	641
	.byte	1,2,2,35,3,11
	.byte	'TFO',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'TFL',0,1
	.word	641
	.byte	1,0,2,35,3,0,12,16,231,3,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17388
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_FLAGSSET_Bits',0,16,241,1,16,4,11
	.byte	'THS',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'TRS',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'RHS',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'RRS',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'FEDS',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'REDS',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	658
	.byte	6,3,2,35,0,11
	.byte	'TWRQS',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'THRQS',0,1
	.word	641
	.byte	1,1,2,35,1,11
	.byte	'TRRQS',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'PES',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'TCS',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'FES',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'HTS',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'RTS',0,1
	.word	641
	.byte	1,3,2,35,2,11
	.byte	'BDS',0,1
	.word	641
	.byte	1,2,2,35,2,11
	.byte	'LPS',0,1
	.word	641
	.byte	1,1,2,35,2,11
	.byte	'LAS',0,1
	.word	641
	.byte	1,0,2,35,2,11
	.byte	'LCS',0,1
	.word	641
	.byte	1,7,2,35,3,11
	.byte	'CES',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'RFOS',0,1
	.word	641
	.byte	1,5,2,35,3,11
	.byte	'RFUS',0,1
	.word	641
	.byte	1,4,2,35,3,11
	.byte	'RFLS',0,1
	.word	641
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	641
	.byte	1,2,2,35,3,11
	.byte	'TFOS',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'TFLS',0,1
	.word	641
	.byte	1,0,2,35,3,0,12,16,255,3,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17874
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_FLAGSCLEAR_Bits',0,16,180,1,16,4,11
	.byte	'THC',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'TRC',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'RHC',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'RRC',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'FEDC',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'REDC',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	658
	.byte	6,3,2,35,0,11
	.byte	'TWRQC',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'THRQC',0,1
	.word	641
	.byte	1,1,2,35,1,11
	.byte	'TRRQC',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'PEC',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'TCC',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'FEC',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'HTC',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'RTC',0,1
	.word	641
	.byte	1,3,2,35,2,11
	.byte	'BDC',0,1
	.word	641
	.byte	1,2,2,35,2,11
	.byte	'LPC',0,1
	.word	641
	.byte	1,1,2,35,2,11
	.byte	'LAC',0,1
	.word	641
	.byte	1,0,2,35,2,11
	.byte	'LCC',0,1
	.word	641
	.byte	1,7,2,35,3,11
	.byte	'CEC',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'RFOC',0,1
	.word	641
	.byte	1,5,2,35,3,11
	.byte	'RFUC',0,1
	.word	641
	.byte	1,4,2,35,3,11
	.byte	'RFLC',0,1
	.word	641
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	641
	.byte	1,2,2,35,3,11
	.byte	'TFOC',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'TFLC',0,1
	.word	641
	.byte	1,0,2,35,3,0,12,16,239,3,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18387
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_FLAGSENABLE_Bits',0,16,212,1,16,4,11
	.byte	'THE',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'TRE',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'RHE',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'RRE',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'FEDE',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'REDE',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	658
	.byte	9,0,2,35,0,11
	.byte	'PEE',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'TCE',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'FEE',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'HTE',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'RTE',0,1
	.word	641
	.byte	1,3,2,35,2,11
	.byte	'BDE',0,1
	.word	641
	.byte	1,2,2,35,2,11
	.byte	'LPE',0,1
	.word	641
	.byte	1,1,2,35,2,11
	.byte	'ABE',0,1
	.word	641
	.byte	1,0,2,35,2,11
	.byte	'LCE',0,1
	.word	641
	.byte	1,7,2,35,3,11
	.byte	'CEE',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'RFOE',0,1
	.word	641
	.byte	1,5,2,35,3,11
	.byte	'RFUE',0,1
	.word	641
	.byte	1,4,2,35,3,11
	.byte	'RFLE',0,1
	.word	641
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	641
	.byte	1,2,2,35,3,11
	.byte	'TFOE',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'TFLE',0,1
	.word	641
	.byte	1,0,2,35,3,0,12,16,247,3,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18902
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_TXDATA_Bits',0,16,143,3,16,4,11
	.byte	'DATA',0,4
	.word	466
	.byte	32,0,2,35,0,0,12,16,239,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19367
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_RXDATA_Bits',0,16,245,2,16,4,11
	.byte	'DATA',0,4
	.word	466
	.byte	32,0,2,35,0,0,12,16,215,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19454
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_CSR_Bits',0,16,128,1,16,4,11
	.byte	'CLKSEL',0,1
	.word	641
	.byte	5,3,2,35,0,11
	.byte	'reserved_5',0,4
	.word	466
	.byte	26,1,2,35,0,11
	.byte	'CON',0,1
	.word	641
	.byte	1,0,2,35,3,0,12,16,215,3,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19541
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_RXDATAD_Bits',0,16,251,2,16,4,11
	.byte	'DATA',0,4
	.word	466
	.byte	32,0,2,35,0,0,12,16,223,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19664
	.byte	4,2,35,0,0,15,148,1
	.word	641
	.byte	16,147,1,0,10
	.byte	'_Ifx_ASCLIN_OCS_Bits',0,16,235,2,16,4,11
	.byte	'reserved_0',0,4
	.word	466
	.byte	24,8,2,35,0,11
	.byte	'SUS',0,1
	.word	641
	.byte	4,4,2,35,3,11
	.byte	'SUS_P',0,1
	.word	641
	.byte	1,3,2,35,3,11
	.byte	'SUSSTA',0,1
	.word	641
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	641
	.byte	2,0,2,35,3,0,12,16,207,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19763
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_KRSTCLR_Bits',0,16,202,2,16,4,11
	.byte	'CLR',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	466
	.byte	31,0,2,35,0,0,12,16,175,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19926
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_KRST1_Bits',0,16,195,2,16,4,11
	.byte	'RST',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	466
	.byte	31,0,2,35,0,0,12,16,167,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20035
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_KRST0_Bits',0,16,187,2,16,4,11
	.byte	'RST',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'RSTSTAT',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	466
	.byte	30,0,2,35,0,0,12,16,159,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20142
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_ACCEN1_Bits',0,16,82,16,4,11
	.byte	'reserved_0',0,4
	.word	466
	.byte	32,0,2,35,0,0,12,16,175,3,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20268
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_ACCEN0_Bits',0,16,45,16,4,11
	.byte	'EN0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	641
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	641
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	641
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	641
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	641
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	641
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	641
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	641
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	641
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	641
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	641
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	641
	.byte	1,0,2,35,3,0,12,16,167,3,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20360
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN',0,16,153,5,25,128,2,13
	.byte	'CLC',0
	.word	14966
	.byte	4,2,35,0,13
	.byte	'IOCR',0
	.word	15261
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	15386
	.byte	4,2,35,8,13
	.byte	'TXFIFOCON',0
	.word	15611
	.byte	4,2,35,12,13
	.byte	'RXFIFOCON',0
	.word	15852
	.byte	4,2,35,16,13
	.byte	'BITCON',0
	.word	16073
	.byte	4,2,35,20,13
	.byte	'FRAMECON',0
	.word	16338
	.byte	4,2,35,24,13
	.byte	'DATCON',0
	.word	16535
	.byte	4,2,35,28,13
	.byte	'BRG',0
	.word	16692
	.byte	4,2,35,32,13
	.byte	'BRD',0
	.word	16846
	.byte	4,2,35,36,13
	.byte	'LIN',0
	.word	17383
	.byte	12,2,35,40,13
	.byte	'FLAGS',0
	.word	17834
	.byte	4,2,35,52,13
	.byte	'FLAGSSET',0
	.word	18347
	.byte	4,2,35,56,13
	.byte	'FLAGSCLEAR',0
	.word	18862
	.byte	4,2,35,60,13
	.byte	'FLAGSENABLE',0
	.word	19327
	.byte	4,2,35,64,13
	.byte	'TXDATA',0
	.word	19414
	.byte	4,2,35,68,13
	.byte	'RXDATA',0
	.word	19501
	.byte	4,2,35,72,13
	.byte	'CSR',0
	.word	19624
	.byte	4,2,35,76,13
	.byte	'RXDATAD',0
	.word	19712
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	19752
	.byte	148,1,2,35,84,13
	.byte	'OCS',0
	.word	19886
	.byte	4,3,35,232,1,13
	.byte	'KRSTCLR',0
	.word	19995
	.byte	4,3,35,236,1,13
	.byte	'KRST1',0
	.word	20102
	.byte	4,3,35,240,1,13
	.byte	'KRST0',0
	.word	20228
	.byte	4,3,35,244,1,13
	.byte	'ACCEN1',0
	.word	20320
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	20892
	.byte	4,3,35,252,1,0,14
	.word	20932
.L320:
	.byte	3
	.word	21374
.L304:
	.byte	8
	.byte	'IfxAsclin_getRxFifoFillLevel',0,3,15,236,15,18
	.word	641
	.byte	1,1
.L306:
	.byte	5
	.byte	'asclin',0,15,236,15,59
	.word	21379
.L308:
	.byte	6,0
.L284:
	.byte	8
	.byte	'IfxAsclin_getTxFifoFillLevel',0,3,15,162,16,18
	.word	641
	.byte	1,1
.L286:
	.byte	5
	.byte	'asclin',0,15,162,16,59
	.word	21379
.L288:
	.byte	6,0,4
	.byte	'IfxAsclin_enableCts',0,3,15,228,13,17,1,1,5
	.byte	'asclin',0,15,228,13,49
	.word	21379
	.byte	5
	.byte	'enable',0,15,228,13,65
	.word	641
	.byte	6,0
.L349:
	.byte	4
	.byte	'IfxAsclin_enableRxFifoFillLevelFlag',0,3,15,178,14,17,1,1
.L352:
	.byte	5
	.byte	'asclin',0,15,178,14,65
	.word	21379
.L354:
	.byte	5
	.byte	'enable',0,15,178,14,81
	.word	641
.L356:
	.byte	6,0
.L325:
	.byte	4
	.byte	'IfxAsclin_enableTxFifoFillLevelFlag',0,3,15,220,14,17,1,1
.L328:
	.byte	5
	.byte	'asclin',0,15,220,14,65
	.word	21379
.L330:
	.byte	5
	.byte	'enable',0,15,220,14,81
	.word	641
.L332:
	.byte	6,0,17,15,123,9,1,18
	.byte	'IfxAsclin_CtsInputSelect_0',0,0,18
	.byte	'IfxAsclin_CtsInputSelect_1',0,1,18
	.byte	'IfxAsclin_CtsInputSelect_2',0,2,18
	.byte	'IfxAsclin_CtsInputSelect_3',0,3,0,4
	.byte	'IfxAsclin_setCtsInput',0,3,15,169,17,17,1,1,5
	.byte	'asclin',0,15,169,17,51
	.word	21379
	.byte	5
	.byte	'ctsi',0,15,169,17,84
	.word	21720
	.byte	6,0,17,15,181,2,9,1,18
	.byte	'IfxAsclin_RxInputSelect_0',0,0,18
	.byte	'IfxAsclin_RxInputSelect_1',0,1,18
	.byte	'IfxAsclin_RxInputSelect_2',0,2,18
	.byte	'IfxAsclin_RxInputSelect_3',0,3,18
	.byte	'IfxAsclin_RxInputSelect_4',0,4,18
	.byte	'IfxAsclin_RxInputSelect_5',0,5,18
	.byte	'IfxAsclin_RxInputSelect_6',0,6,18
	.byte	'IfxAsclin_RxInputSelect_7',0,7,0,4
	.byte	'IfxAsclin_setRxInput',0,3,15,191,18,17,1,1,5
	.byte	'asclin',0,15,191,18,50
	.word	21379
	.byte	5
	.byte	'alti',0,15,191,18,82
	.word	21904
	.byte	6,0,7
	.byte	'short int',0,2,5,7
	.byte	'long int',0,4,5,20,17,60,9,12,13
	.byte	'count',0
	.word	22196
	.byte	2,2,35,0,13
	.byte	'readerWaitx',0
	.word	22209
	.byte	4,2,35,2,13
	.byte	'writerWaitx',0
	.word	22209
	.byte	4,2,35,6,13
	.byte	'maxcount',0
	.word	22196
	.byte	2,2,35,10,0,14
	.word	641
	.byte	14
	.word	641
	.byte	10
	.byte	'_Fifo',0,17,73,16,28,13
	.byte	'buffer',0
	.word	380
	.byte	4,2,35,0,13
	.byte	'shared',0
	.word	22221
	.byte	12,2,35,4,13
	.byte	'startIndex',0
	.word	22196
	.byte	2,2,35,16,13
	.byte	'endIndex',0
	.word	22196
	.byte	2,2,35,18,13
	.byte	'size',0
	.word	22196
	.byte	2,2,35,20,13
	.byte	'elementSize',0
	.word	22196
	.byte	2,2,35,22,13
	.byte	'eventReader',0
	.word	22302
	.byte	1,2,35,24,13
	.byte	'eventWriter',0
	.word	22307
	.byte	1,2,35,25,0,3
	.word	22312
	.byte	8
	.byte	'Ifx_Fifo_readCount',0,3,17,206,1,22
	.word	22196
	.byte	1,1,5
	.byte	'fifo',0,17,206,1,51
	.word	22471
	.byte	6,0,14
	.word	482
	.byte	21
	.byte	'__mfcr',0
	.word	22523
	.byte	1,1,1,1,22
	.word	482
	.byte	0,23
	.byte	'__nop',0,1,1,1,1,23
	.byte	'__disable',0,1,1,1,1,23
	.byte	'__enable',0,1,1,1,1,24
	.word	210
	.byte	25
	.word	236
	.byte	6,0,24
	.word	271
	.byte	25
	.word	303
	.byte	6,0,24
	.word	316
	.byte	6,0,24
	.word	385
	.byte	25
	.word	404
	.byte	6,0,24
	.word	420
	.byte	25
	.word	435
	.byte	25
	.word	449
	.byte	6,0,24
	.word	1228
	.byte	25
	.word	1268
	.byte	25
	.word	1286
	.byte	6,0,24
	.word	1306
	.byte	25
	.word	1344
	.byte	25
	.word	1362
	.byte	6,0,24
	.word	1382
	.byte	25
	.word	1433
	.byte	6,0,24
	.word	9336
	.byte	25
	.word	9368
	.byte	25
	.word	9382
	.byte	25
	.word	9400
	.byte	6,0,24
	.word	9703
	.byte	25
	.word	9736
	.byte	25
	.word	9750
	.byte	25
	.word	9768
	.byte	25
	.word	9782
	.byte	6,0,24
	.word	9902
	.byte	25
	.word	9930
	.byte	25
	.word	9944
	.byte	25
	.word	9962
	.byte	6,0,24
	.word	9980
	.byte	6,0,24
	.word	10099
	.byte	6,0,24
	.word	10133
	.byte	6,0,24
	.word	10175
	.byte	19,26
	.word	10133
	.byte	27
	.word	10173
	.byte	0,6,0,0,24
	.word	10216
	.byte	6,0,24
	.word	10250
	.byte	6,0,24
	.word	10290
	.byte	25
	.word	10323
	.byte	6,0,24
	.word	10363
	.byte	25
	.word	10404
	.byte	6,0,24
	.word	10423
	.byte	25
	.word	10478
	.byte	6,0,24
	.word	10497
	.byte	25
	.word	10537
	.byte	25
	.word	10554
	.byte	19,6,0,0,24
	.word	10912
	.byte	25
	.word	10940
	.byte	6,0,24
	.word	10955
	.byte	25
	.word	10978
	.byte	6,0,24
	.word	10993
	.byte	25
	.word	11015
	.byte	6,0,24
	.word	14465
	.byte	25
	.word	14488
	.byte	6,0,24
	.word	14503
	.byte	25
	.word	14535
	.byte	19,19,26
	.word	9980
	.byte	27
	.word	10018
	.byte	0,0,6,0,0,24
	.word	14553
	.byte	25
	.word	14581
	.byte	6,0,24
	.word	14596
	.byte	19,26
	.word	10175
	.byte	28
	.word	10212
	.byte	26
	.word	10133
	.byte	27
	.word	10173
	.byte	0,27
	.word	10213
	.byte	0,0,6,0,0,24
	.word	14629
	.byte	25
	.word	14655
	.byte	19,26
	.word	10290
	.byte	25
	.word	10323
	.byte	27
	.word	10340
	.byte	0,6,0,0,24
	.word	14693
	.byte	25
	.word	14717
	.byte	19,26
	.word	14783
	.byte	28
	.word	14799
	.byte	26
	.word	14596
	.byte	28
	.word	14625
	.byte	26
	.word	10175
	.byte	28
	.word	10212
	.byte	26
	.word	10133
	.byte	27
	.word	10173
	.byte	0,27
	.word	10213
	.byte	0,0,27
	.word	14626
	.byte	0,0,27
	.word	14800
	.byte	26
	.word	14629
	.byte	25
	.word	14655
	.byte	28
	.word	14672
	.byte	26
	.word	10290
	.byte	25
	.word	10323
	.byte	27
	.word	10340
	.byte	0,27
	.word	14673
	.byte	0,0,27
	.word	14801
	.byte	26
	.word	14465
	.byte	25
	.word	14488
	.byte	27
	.word	14501
	.byte	0,27
	.word	14802
	.byte	0,0,6,0,0,24
	.word	14738
	.byte	25
	.word	14761
	.byte	19,26
	.word	14783
	.byte	28
	.word	14799
	.byte	26
	.word	14596
	.byte	28
	.word	14625
	.byte	26
	.word	10175
	.byte	28
	.word	10212
	.byte	26
	.word	10133
	.byte	27
	.word	10173
	.byte	0,27
	.word	10213
	.byte	0,0,27
	.word	14626
	.byte	0,0,27
	.word	14800
	.byte	26
	.word	14629
	.byte	25
	.word	14655
	.byte	28
	.word	14672
	.byte	26
	.word	10290
	.byte	25
	.word	10323
	.byte	27
	.word	10340
	.byte	0,27
	.word	14673
	.byte	0,0,27
	.word	14801
	.byte	26
	.word	14465
	.byte	25
	.word	14488
	.byte	27
	.word	14501
	.byte	0,27
	.word	14802
	.byte	0,0,6,0,0,24
	.word	14783
	.byte	19,26
	.word	14596
	.byte	28
	.word	14625
	.byte	26
	.word	10175
	.byte	28
	.word	10212
	.byte	26
	.word	10133
	.byte	27
	.word	10173
	.byte	0,27
	.word	10213
	.byte	0,0,27
	.word	14626
	.byte	0,0,6,26
	.word	14629
	.byte	25
	.word	14655
	.byte	28
	.word	14672
	.byte	26
	.word	10290
	.byte	25
	.word	10323
	.byte	27
	.word	10340
	.byte	0,27
	.word	14673
	.byte	0,0,6,26
	.word	14465
	.byte	25
	.word	14488
	.byte	27
	.word	14501
	.byte	0,6,0,0,24
	.word	14805
	.byte	19,26
	.word	14465
	.byte	25
	.word	14488
	.byte	27
	.word	14501
	.byte	0,6,0,0,7
	.byte	'char',0,1,6,3
	.word	23446
	.byte	29
	.byte	'debug_assert_handler',0,18,112,9,1,1,1,1,5
	.byte	'pass',0,18,112,47
	.word	641
	.byte	5
	.byte	'file',0,18,112,59
	.word	23454
	.byte	5
	.byte	'line',0,18,112,69
	.word	482
	.byte	0,24
	.word	21384
	.byte	25
	.word	21425
	.byte	6,0,24
	.word	21443
	.byte	25
	.word	21484
	.byte	6,0,17,19,78,9,1,18
	.byte	'IfxAsclin_Index_none',0,127,18
	.byte	'IfxAsclin_Index_0',0,0,18
	.byte	'IfxAsclin_Index_1',0,1,18
	.byte	'IfxAsclin_Index_2',0,2,18
	.byte	'IfxAsclin_Index_3',0,3,0,30
	.byte	'IfxAsclin_getAddress',0,15,164,8,24
	.word	21379
	.byte	1,1,1,1,5
	.byte	'asclin',0,15,164,8,61
	.word	23552
	.byte	0,24
	.word	21502
	.byte	25
	.word	21530
	.byte	25
	.word	21546
	.byte	6,0,24
	.word	21564
	.byte	25
	.word	21608
	.byte	25
	.word	21624
	.byte	6,0,24
	.word	21642
	.byte	25
	.word	21686
	.byte	25
	.word	21702
	.byte	6,0,24
	.word	21842
	.byte	25
	.word	21872
	.byte	25
	.word	21888
	.byte	6,0,24
	.word	22135
	.byte	25
	.word	22164
	.byte	25
	.word	22180
	.byte	6,0,30
	.byte	'IfxAsclin_getSrcPointerRx',0,15,146,11,35
	.word	10907
	.byte	1,1,1,1,5
	.byte	'asclin',0,15,146,11,73
	.word	21379
	.byte	0,30
	.byte	'IfxAsclin_getSrcPointerTx',0,15,151,11,35
	.word	10907
	.byte	1,1,1,1,5
	.byte	'asclin',0,15,151,11,73
	.word	21379
	.byte	0
.L311:
	.byte	3
	.word	641
	.byte	30
	.byte	'IfxAsclin_read8',0,15,175,11,19
	.word	10342
	.byte	1,1,1,1,5
	.byte	'asclin',0,15,175,11,47
	.word	21379
	.byte	5
	.byte	'data',0,15,175,11,62
	.word	23909
	.byte	5
	.byte	'count',0,15,175,11,75
	.word	10342
	.byte	0,31
	.word	641
.L291:
	.byte	3
	.word	23989
	.byte	30
	.byte	'IfxAsclin_write8',0,15,216,11,19
	.word	10342
	.byte	1,1,1,1,5
	.byte	'asclin',0,15,216,11,48
	.word	21379
	.byte	5
	.byte	'data',0,15,216,11,69
	.word	23994
	.byte	5
	.byte	'count',0,15,216,11,82
	.word	10342
	.byte	0,24
	.word	22476
	.byte	25
	.word	22507
	.byte	6,0,17,15,255,2,9,1,18
	.byte	'IfxAsclin_Status_configurationError',0,0,18
	.byte	'IfxAsclin_Status_noError',0,1,0,14
	.word	641
	.byte	14
	.word	641
	.byte	20,20,252,1,9,1,11
	.byte	'parityError',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'frameError',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'rxFifoOverflow',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'rxFifoUnderflow',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'txFifoOverflow',0,1
	.word	641
	.byte	1,3,2,35,0,0,12,20,205,2,9,1,13
	.byte	'ALL',0
	.word	641
	.byte	1,2,35,0,13
	.byte	'flags',0
	.word	24169
	.byte	1,2,35,0,0,17,21,164,1,9,1,18
	.byte	'Ifx_DataBufferMode_normal',0,0,18
	.byte	'Ifx_DataBufferMode_timeStampSingle',0,1,0,14
	.word	10342
	.byte	14
	.word	14676
.L421:
	.byte	20,20,215,2,9,28,13
	.byte	'asclin',0
	.word	21379
	.byte	4,2,35,0,13
	.byte	'tx',0
	.word	22471
	.byte	4,2,35,4,13
	.byte	'rx',0
	.word	22471
	.byte	4,2,35,8,13
	.byte	'txInProgress',0
	.word	24159
	.byte	1,2,35,12,13
	.byte	'rxSwFifoOverflow',0
	.word	24164
	.byte	1,2,35,13,13
	.byte	'errorFlags',0
	.word	24300
	.byte	1,2,35,14,13
	.byte	'dataBufferMode',0
	.word	24335
	.byte	1,2,35,15,13
	.byte	'sendCount',0
	.word	24407
	.byte	4,2,35,16,13
	.byte	'txTimestamp',0
	.word	24412
	.byte	8,2,35,20,0
.L282:
	.byte	3
	.word	24417
	.byte	17,15,228,1,9,1,18
	.byte	'IfxAsclin_OversamplingFactor_4',0,3,18
	.byte	'IfxAsclin_OversamplingFactor_5',0,4,18
	.byte	'IfxAsclin_OversamplingFactor_6',0,5,18
	.byte	'IfxAsclin_OversamplingFactor_7',0,6,18
	.byte	'IfxAsclin_OversamplingFactor_8',0,7,18
	.byte	'IfxAsclin_OversamplingFactor_9',0,8,18
	.byte	'IfxAsclin_OversamplingFactor_10',0,9,18
	.byte	'IfxAsclin_OversamplingFactor_11',0,10,18
	.byte	'IfxAsclin_OversamplingFactor_12',0,11,18
	.byte	'IfxAsclin_OversamplingFactor_13',0,12,18
	.byte	'IfxAsclin_OversamplingFactor_14',0,13,18
	.byte	'IfxAsclin_OversamplingFactor_15',0,14,18
	.byte	'IfxAsclin_OversamplingFactor_16',0,15,0,20,20,137,2,9,8,13
	.byte	'baudrate',0
	.word	262
	.byte	4,2,35,0,13
	.byte	'prescaler',0
	.word	658
	.byte	2,2,35,4,13
	.byte	'oversampling',0
	.word	24601
	.byte	1,2,35,6,0,17,15,218,2,9,1,18
	.byte	'IfxAsclin_SamplesPerBit_one',0,0,18
	.byte	'IfxAsclin_SamplesPerBit_three',0,1,0,17,15,196,2,9,1,18
	.byte	'IfxAsclin_SamplePointPosition_1',0,1,18
	.byte	'IfxAsclin_SamplePointPosition_2',0,2,18
	.byte	'IfxAsclin_SamplePointPosition_3',0,3,18
	.byte	'IfxAsclin_SamplePointPosition_4',0,4,18
	.byte	'IfxAsclin_SamplePointPosition_5',0,5,18
	.byte	'IfxAsclin_SamplePointPosition_6',0,6,18
	.byte	'IfxAsclin_SamplePointPosition_7',0,7,18
	.byte	'IfxAsclin_SamplePointPosition_8',0,8,18
	.byte	'IfxAsclin_SamplePointPosition_9',0,9,18
	.byte	'IfxAsclin_SamplePointPosition_10',0,10,18
	.byte	'IfxAsclin_SamplePointPosition_11',0,11,18
	.byte	'IfxAsclin_SamplePointPosition_12',0,12,18
	.byte	'IfxAsclin_SamplePointPosition_13',0,13,18
	.byte	'IfxAsclin_SamplePointPosition_14',0,14,18
	.byte	'IfxAsclin_SamplePointPosition_15',0,15,0,20,20,146,2,9,2,13
	.byte	'medianFilter',0
	.word	25110
	.byte	1,2,35,0,13
	.byte	'samplePointPosition',0
	.word	25179
	.byte	1,2,35,1,0,17,15,179,1,9,1,18
	.byte	'IfxAsclin_IdleDelay_0',0,0,18
	.byte	'IfxAsclin_IdleDelay_1',0,1,18
	.byte	'IfxAsclin_IdleDelay_2',0,2,18
	.byte	'IfxAsclin_IdleDelay_3',0,3,18
	.byte	'IfxAsclin_IdleDelay_4',0,4,18
	.byte	'IfxAsclin_IdleDelay_5',0,5,18
	.byte	'IfxAsclin_IdleDelay_6',0,6,18
	.byte	'IfxAsclin_IdleDelay_7',0,7,0,17,15,136,3,9,1,18
	.byte	'IfxAsclin_StopBit_0',0,0,18
	.byte	'IfxAsclin_StopBit_1',0,1,18
	.byte	'IfxAsclin_StopBit_2',0,2,18
	.byte	'IfxAsclin_StopBit_3',0,3,18
	.byte	'IfxAsclin_StopBit_4',0,4,18
	.byte	'IfxAsclin_StopBit_5',0,5,18
	.byte	'IfxAsclin_StopBit_6',0,6,18
	.byte	'IfxAsclin_StopBit_7',0,7,0,17,15,157,1,9,1,18
	.byte	'IfxAsclin_FrameMode_initialise',0,0,18
	.byte	'IfxAsclin_FrameMode_asc',0,1,18
	.byte	'IfxAsclin_FrameMode_spi',0,2,18
	.byte	'IfxAsclin_FrameMode_lin',0,3,0,17,15,228,2,9,1,18
	.byte	'IfxAsclin_ShiftDirection_lsbFirst',0,0,18
	.byte	'IfxAsclin_ShiftDirection_msbFirst',0,1,0,17,15,248,1,9,1,18
	.byte	'IfxAsclin_ParityType_even',0,0,18
	.byte	'IfxAsclin_ParityType_odd',0,1,0,17,15,134,1,9,1,18
	.byte	'IfxAsclin_DataLength_1',0,0,18
	.byte	'IfxAsclin_DataLength_2',0,1,18
	.byte	'IfxAsclin_DataLength_3',0,2,18
	.byte	'IfxAsclin_DataLength_4',0,3,18
	.byte	'IfxAsclin_DataLength_5',0,4,18
	.byte	'IfxAsclin_DataLength_6',0,5,18
	.byte	'IfxAsclin_DataLength_7',0,6,18
	.byte	'IfxAsclin_DataLength_8',0,7,18
	.byte	'IfxAsclin_DataLength_9',0,8,18
	.byte	'IfxAsclin_DataLength_10',0,9,18
	.byte	'IfxAsclin_DataLength_11',0,10,18
	.byte	'IfxAsclin_DataLength_12',0,11,18
	.byte	'IfxAsclin_DataLength_13',0,12,18
	.byte	'IfxAsclin_DataLength_14',0,13,18
	.byte	'IfxAsclin_DataLength_15',0,14,18
	.byte	'IfxAsclin_DataLength_16',0,15,0,20,20,165,2,9,8,13
	.byte	'idleDelay',0
	.word	25760
	.byte	1,2,35,0,13
	.byte	'stopBit',0
	.word	25959
	.byte	1,2,35,1,13
	.byte	'frameMode',0
	.word	26142
	.byte	1,2,35,2,13
	.byte	'shiftDir',0
	.word	26260
	.byte	1,2,35,3,13
	.byte	'parityType',0
	.word	26339
	.byte	1,2,35,4,13
	.byte	'dataLength',0
	.word	26401
	.byte	1,2,35,5,13
	.byte	'parityBit',0
	.word	641
	.byte	1,2,35,6,0,17,15,160,3,9,1,18
	.byte	'IfxAsclin_TxFifoInletWidth_0',0,0,18
	.byte	'IfxAsclin_TxFifoInletWidth_1',0,1,18
	.byte	'IfxAsclin_TxFifoInletWidth_2',0,2,18
	.byte	'IfxAsclin_TxFifoInletWidth_3',0,3,0,17,15,170,2,9,1,18
	.byte	'IfxAsclin_RxFifoOutletWidth_0',0,0,18
	.byte	'IfxAsclin_RxFifoOutletWidth_1',0,1,18
	.byte	'IfxAsclin_RxFifoOutletWidth_2',0,2,18
	.byte	'IfxAsclin_RxFifoOutletWidth_3',0,3,0,17,15,171,3,9,1,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_0',0,0,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_1',0,1,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_2',0,2,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_3',0,3,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_4',0,4,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_5',0,5,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_6',0,6,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_7',0,7,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_8',0,8,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_9',0,9,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_10',0,10,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_11',0,11,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_12',0,12,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_13',0,13,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_14',0,14,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_15',0,15,0,17,15,147,2,9,1,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_1',0,0,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_2',0,1,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_3',0,2,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_4',0,3,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_5',0,4,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_6',0,5,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_7',0,6,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_8',0,7,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_9',0,8,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_10',0,9,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_11',0,10,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_12',0,11,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_13',0,12,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_14',0,13,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_15',0,14,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_16',0,15,0,17,15,129,2,9,1,18
	.byte	'IfxAsclin_ReceiveBufferMode_rxFifo',0,0,18
	.byte	'IfxAsclin_ReceiveBufferMode_rxBuffer',0,1,0,20,20,154,2,9,6,13
	.byte	'inWidth',0
	.word	26954
	.byte	1,2,35,0,13
	.byte	'outWidth',0
	.word	27085
	.byte	1,2,35,1,13
	.byte	'txFifoInterruptLevel',0
	.word	27220
	.byte	1,2,35,2,13
	.byte	'rxFifoInterruptLevel',0
	.word	27793
	.byte	1,2,35,3,13
	.byte	'buffMode',0
	.word	28367
	.byte	1,2,35,4,0,17,22,69,9,1,18
	.byte	'IfxSrc_Tos_cpu0',0,0,18
	.byte	'IfxSrc_Tos_cpu1',0,1,18
	.byte	'IfxSrc_Tos_dma',0,3,0,20,20,178,2,9,8,13
	.byte	'txPriority',0
	.word	658
	.byte	2,2,35,0,13
	.byte	'rxPriority',0
	.word	658
	.byte	2,2,35,2,13
	.byte	'erPriority',0
	.word	658
	.byte	2,2,35,4,13
	.byte	'typeOfService',0
	.word	28570
	.byte	1,2,35,6,0,20,5,190,1,9,8,13
	.byte	'port',0
	.word	9206
	.byte	4,2,35,0,13
	.byte	'pinIndex',0
	.word	641
	.byte	1,2,35,4,0,17,21,130,1,9,1,18
	.byte	'Ifx_RxSel_a',0,0,18
	.byte	'Ifx_RxSel_b',0,1,18
	.byte	'Ifx_RxSel_c',0,2,18
	.byte	'Ifx_RxSel_d',0,3,18
	.byte	'Ifx_RxSel_e',0,4,18
	.byte	'Ifx_RxSel_f',0,5,18
	.byte	'Ifx_RxSel_g',0,6,18
	.byte	'Ifx_RxSel_h',0,7,0,20,23,59,15,16,13
	.byte	'module',0
	.word	21379
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	28719
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	28758
	.byte	1,2,35,12,0,31
	.word	28877
	.byte	3
	.word	28928
	.byte	20,23,67,15,16,13
	.byte	'module',0
	.word	21379
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	28719
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	28758
	.byte	1,2,35,12,0,31
	.word	28938
	.byte	3
	.word	28989
	.byte	20,23,75,15,16,13
	.byte	'module',0
	.word	21379
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	28719
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	9486
	.byte	1,2,35,12,0,31
	.word	28999
	.byte	3
	.word	29050
	.byte	20,23,99,15,16,13
	.byte	'module',0
	.word	21379
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	28719
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	9486
	.byte	1,2,35,12,0,31
	.word	29060
	.byte	3
	.word	29111
	.byte	17,5,144,1,9,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed1',0,0,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed2',0,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed3',0,2,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed4',0,3,18
	.byte	'IfxPort_PadDriver_lvdsSpeed1',0,4,18
	.byte	'IfxPort_PadDriver_lvdsSpeed2',0,5,18
	.byte	'IfxPort_PadDriver_lvdsSpeed3',0,6,18
	.byte	'IfxPort_PadDriver_lvdsSpeed4',0,7,18
	.byte	'IfxPort_PadDriver_ttlSpeed1',0,8,18
	.byte	'IfxPort_PadDriver_ttlSpeed2',0,9,18
	.byte	'IfxPort_PadDriver_ttlSpeed3',0,10,18
	.byte	'IfxPort_PadDriver_ttlSpeed4',0,11,0
.L381:
	.byte	20,20,188,2,9,32,13
	.byte	'cts',0
	.word	28933
	.byte	4,2,35,0,13
	.byte	'ctsMode',0
	.word	9211
	.byte	1,2,35,4,13
	.byte	'rx',0
	.word	28994
	.byte	4,2,35,8,13
	.byte	'rxMode',0
	.word	9211
	.byte	1,2,35,12,13
	.byte	'rts',0
	.word	29055
	.byte	4,2,35,16,13
	.byte	'rtsMode',0
	.word	9416
	.byte	1,2,35,20,13
	.byte	'tx',0
	.word	29116
	.byte	4,2,35,24,13
	.byte	'txMode',0
	.word	9416
	.byte	1,2,35,28,13
	.byte	'pinDriver',0
	.word	29121
	.byte	1,2,35,29,0,31
	.word	29536
	.byte	3
	.word	29678
	.byte	17,15,110,9,1,18
	.byte	'IfxAsclin_ClockSource_noClock',0,0,18
	.byte	'IfxAsclin_ClockSource_kernelClock',0,1,18
	.byte	'IfxAsclin_ClockSource_oscillatorClock',0,2,18
	.byte	'IfxAsclin_ClockSource_flexRayClock',0,4,18
	.byte	'IfxAsclin_ClockSource_ascFastClock',0,8,18
	.byte	'IfxAsclin_ClockSource_ascSlowClock',0,16,0
.L422:
	.byte	20,20,230,2,9,64,13
	.byte	'asclin',0
	.word	21379
	.byte	4,2,35,0,13
	.byte	'baudrate',0
	.word	25044
	.byte	8,2,35,4,13
	.byte	'bitTiming',0
	.word	25702
	.byte	2,2,35,12,13
	.byte	'frame',0
	.word	26815
	.byte	8,2,35,16,13
	.byte	'fifo',0
	.word	28450
	.byte	6,2,35,24,13
	.byte	'interrupt',0
	.word	28629
	.byte	8,2,35,32,13
	.byte	'pins',0
	.word	29683
	.byte	4,2,35,40,13
	.byte	'clockSource',0
	.word	29688
	.byte	1,2,35,44,13
	.byte	'errorFlags',0
	.word	24300
	.byte	1,2,35,45,13
	.byte	'txBufferSize',0
	.word	22196
	.byte	2,2,35,46,13
	.byte	'txBuffer',0
	.word	380
	.byte	4,2,35,48,13
	.byte	'rxBufferSize',0
	.word	22196
	.byte	2,2,35,52,13
	.byte	'rxBuffer',0
	.word	380
	.byte	4,2,35,56,13
	.byte	'loopBack',0
	.word	641
	.byte	1,2,35,60,13
	.byte	'dataBufferMode',0
	.word	24335
	.byte	1,2,35,61,0,31
	.word	29913
	.byte	3
	.word	30198
	.byte	30
	.byte	'IfxAsclin_Asc_initModule',0,20,197,4,29
	.word	24087
	.byte	1,1,1,1,5
	.byte	'asclin',0,20,197,4,69
	.word	24596
	.byte	5
	.byte	'config',0,20,197,4,105
	.word	30203
	.byte	0,3
	.word	29913
	.byte	29
	.byte	'IfxAsclin_Asc_initModuleConfig',0,20,207,4,17,1,1,1,1,5
	.byte	'config',0,20,207,4,70
	.word	30279
	.byte	5
	.byte	'asclin',0,20,207,4,90
	.word	21379
	.byte	0
.L278:
	.byte	17,24,103,9,1,18
	.byte	'UART_0',0,0,18
	.byte	'UART_1',0,1,18
	.byte	'UART_2',0,2,18
	.byte	'UART_3',0,3,0
.L280:
	.byte	31
	.word	641
	.byte	31
	.word	23446
.L297:
	.byte	3
	.word	30404
.L364:
	.byte	17,24,43,9,1,18
	.byte	'UART0_TX_P14_0',0,0,18
	.byte	'UART0_TX_P14_1',0,1,18
	.byte	'UART0_TX_P15_2',0,2,18
	.byte	'UART0_TX_P15_3',0,3,18
	.byte	'UART1_TX_P02_2',0,4,18
	.byte	'UART1_TX_P11_12',0,5,18
	.byte	'UART1_TX_P15_0',0,6,18
	.byte	'UART1_TX_P15_1',0,7,18
	.byte	'UART1_TX_P15_4',0,8,18
	.byte	'UART1_TX_P15_5',0,9,18
	.byte	'UART1_TX_P20_10',0,10,18
	.byte	'UART1_TX_P33_12',0,11,18
	.byte	'UART1_TX_P33_13',0,12,18
	.byte	'UART2_TX_P02_0',0,13,18
	.byte	'UART2_TX_P10_5',0,14,18
	.byte	'UART2_TX_P14_2',0,15,18
	.byte	'UART2_TX_P14_3',0,16,18
	.byte	'UART2_TX_P33_8',0,17,18
	.byte	'UART2_TX_P33_9',0,18,18
	.byte	'UART3_TX_P00_0',0,19,18
	.byte	'UART3_TX_P00_1',0,20,18
	.byte	'UART3_TX_P15_6',0,21,18
	.byte	'UART3_TX_P15_7',0,22,18
	.byte	'UART3_TX_P20_0',0,23,18
	.byte	'UART3_TX_P20_3',0,24,18
	.byte	'UART3_TX_P21_7',0,25,0
.L366:
	.byte	17,24,77,9,1,18
	.byte	'UART0_RX_P14_1',0,0,18
	.byte	'UART0_RX_P15_3',0,1,18
	.byte	'UART1_RX_P02_3',0,2,18
	.byte	'UART1_RX_P11_10',0,3,18
	.byte	'UART1_RX_P15_1',0,4,18
	.byte	'UART1_RX_P15_5',0,5,18
	.byte	'UART1_RX_P20_9',0,6,18
	.byte	'UART1_RX_P33_13',0,7,18
	.byte	'UART2_RX_P02_0',0,8,18
	.byte	'UART2_RX_P02_1',0,9,18
	.byte	'UART2_RX_P10_6',0,10,18
	.byte	'UART2_RX_P14_3',0,11,18
	.byte	'UART2_RX_P33_8',0,12,18
	.byte	'UART3_RX_P00_1',0,13,18
	.byte	'UART3_RX_P15_7',0,14,18
	.byte	'UART3_RX_P20_3',0,15,18
	.byte	'UART3_RX_P21_6',0,16,0,10
	.byte	'_Ifx_CPU_ICR_Bits',0,25,246,2,16,4,11
	.byte	'CCPN',0,4
	.word	489
	.byte	10,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	489
	.byte	5,17,2,35,0,11
	.byte	'IE',0,4
	.word	489
	.byte	1,16,2,35,0,11
	.byte	'PIPN',0,4
	.word	489
	.byte	10,6,2,35,0,11
	.byte	'reserved_26',0,4
	.word	489
	.byte	6,0,2,35,0,0
.L378:
	.byte	12,25,223,7,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31163
	.byte	4,2,35,0,0
.L418:
	.byte	3
	.word	10342
	.byte	32
	.byte	'__wchar_t',0,26,1,1
	.word	22196
	.byte	32
	.byte	'__size_t',0,26,1,1
	.word	466
	.byte	32
	.byte	'__ptrdiff_t',0,26,1,1
	.word	482
	.byte	33,1,3
	.word	31380
	.byte	32
	.byte	'__codeptr',0,26,1,1
	.word	31382
	.byte	32
	.byte	'__intptr_t',0,26,1,1
	.word	482
	.byte	32
	.byte	'__uintptr_t',0,26,1,1
	.word	466
	.byte	32
	.byte	'boolean',0,27,101,29
	.word	641
	.byte	32
	.byte	'uint8',0,27,105,29
	.word	641
	.byte	32
	.byte	'uint16',0,27,109,29
	.word	658
	.byte	32
	.byte	'uint32',0,27,113,29
	.word	10342
	.byte	32
	.byte	'uint64',0,27,118,29
	.word	348
	.byte	32
	.byte	'sint16',0,27,126,29
	.word	22196
	.byte	32
	.byte	'sint32',0,27,131,1,29
	.word	22209
	.byte	32
	.byte	'sint64',0,27,138,1,29
	.word	14676
	.byte	32
	.byte	'float32',0,27,167,1,29
	.word	262
	.byte	32
	.byte	'pvoid',0,21,57,28
	.word	380
	.byte	32
	.byte	'Ifx_TickTime',0,21,79,28
	.word	14676
	.byte	32
	.byte	'Ifx_SizeT',0,21,92,16
	.word	22196
	.byte	32
	.byte	'Ifx_Priority',0,21,103,16
	.word	658
	.byte	32
	.byte	'Ifx_RxSel',0,21,140,1,3
	.word	28758
	.byte	32
	.byte	'Ifx_DataBufferMode',0,21,169,1,2
	.word	24335
	.byte	32
	.byte	'Ifx_P_ACCEN0_Bits',0,6,79,3
	.word	8031
	.byte	32
	.byte	'Ifx_P_ACCEN1_Bits',0,6,85,3
	.word	7944
	.byte	32
	.byte	'Ifx_P_ESR_Bits',0,6,107,3
	.word	4287
	.byte	32
	.byte	'Ifx_P_ID_Bits',0,6,115,3
	.word	2340
	.byte	32
	.byte	'Ifx_P_IN_Bits',0,6,137,1,3
	.word	3335
	.byte	32
	.byte	'Ifx_P_IOCR0_Bits',0,6,150,1,3
	.word	2468
	.byte	32
	.byte	'Ifx_P_IOCR12_Bits',0,6,163,1,3
	.word	3115
	.byte	32
	.byte	'Ifx_P_IOCR4_Bits',0,6,176,1,3
	.word	2683
	.byte	32
	.byte	'Ifx_P_IOCR8_Bits',0,6,189,1,3
	.word	2898
	.byte	32
	.byte	'Ifx_P_LPCR0_Bits',0,6,197,1,3
	.word	7303
	.byte	32
	.byte	'Ifx_P_LPCR1_Bits',0,6,205,1,3
	.word	7427
	.byte	32
	.byte	'Ifx_P_LPCR1_P21_Bits',0,6,215,1,3
	.word	7511
	.byte	32
	.byte	'Ifx_P_LPCR2_Bits',0,6,229,1,3
	.word	7691
	.byte	32
	.byte	'Ifx_P_OMCR0_Bits',0,6,240,1,3
	.word	5942
	.byte	32
	.byte	'Ifx_P_OMCR12_Bits',0,6,250,1,3
	.word	6466
	.byte	32
	.byte	'Ifx_P_OMCR4_Bits',0,6,133,2,3
	.word	6116
	.byte	32
	.byte	'Ifx_P_OMCR8_Bits',0,6,144,2,3
	.word	6290
	.byte	32
	.byte	'Ifx_P_OMCR_Bits',0,6,166,2,3
	.word	6955
	.byte	32
	.byte	'Ifx_P_OMR_Bits',0,6,203,2,3
	.word	1769
	.byte	32
	.byte	'Ifx_P_OMSR0_Bits',0,6,213,2,3
	.word	5279
	.byte	32
	.byte	'Ifx_P_OMSR12_Bits',0,6,224,2,3
	.word	5767
	.byte	32
	.byte	'Ifx_P_OMSR4_Bits',0,6,235,2,3
	.word	5426
	.byte	32
	.byte	'Ifx_P_OMSR8_Bits',0,6,246,2,3
	.word	5595
	.byte	32
	.byte	'Ifx_P_OMSR_Bits',0,6,140,3,3
	.word	6622
	.byte	32
	.byte	'Ifx_P_OUT_Bits',0,6,162,3,3
	.word	1453
	.byte	32
	.byte	'Ifx_P_PCSR_Bits',0,6,180,3,3
	.word	4993
	.byte	32
	.byte	'Ifx_P_PDISC_Bits',0,6,202,3,3
	.word	4627
	.byte	32
	.byte	'Ifx_P_PDR0_Bits',0,6,223,3,3
	.word	3658
	.byte	32
	.byte	'Ifx_P_PDR1_Bits',0,6,244,3,3
	.word	3962
	.byte	32
	.byte	'Ifx_P_ACCEN0',0,6,129,4,3
	.word	8558
	.byte	32
	.byte	'Ifx_P_ACCEN1',0,6,137,4,3
	.word	7991
	.byte	32
	.byte	'Ifx_P_ESR',0,6,145,4,3
	.word	4578
	.byte	32
	.byte	'Ifx_P_ID',0,6,153,4,3
	.word	2419
	.byte	32
	.byte	'Ifx_P_IN',0,6,161,4,3
	.word	3609
	.byte	32
	.byte	'Ifx_P_IOCR0',0,6,169,4,3
	.word	2643
	.byte	32
	.byte	'Ifx_P_IOCR12',0,6,177,4,3
	.word	3295
	.byte	32
	.byte	'Ifx_P_IOCR4',0,6,185,4,3
	.word	2858
	.byte	32
	.byte	'Ifx_P_IOCR8',0,6,193,4,3
	.word	3075
	.byte	32
	.byte	'Ifx_P_LPCR0',0,6,201,4,3
	.word	7387
	.byte	32
	.byte	'Ifx_P_LPCR1',0,6,210,4,3
	.word	7636
	.byte	32
	.byte	'Ifx_P_LPCR2',0,6,218,4,3
	.word	7895
	.byte	32
	.byte	'Ifx_P_OMCR',0,6,226,4,3
	.word	7263
	.byte	32
	.byte	'Ifx_P_OMCR0',0,6,234,4,3
	.word	6076
	.byte	32
	.byte	'Ifx_P_OMCR12',0,6,242,4,3
	.word	6582
	.byte	32
	.byte	'Ifx_P_OMCR4',0,6,250,4,3
	.word	6250
	.byte	32
	.byte	'Ifx_P_OMCR8',0,6,130,5,3
	.word	6426
	.byte	32
	.byte	'Ifx_P_OMR',0,6,138,5,3
	.word	2300
	.byte	32
	.byte	'Ifx_P_OMSR',0,6,146,5,3
	.word	6915
	.byte	32
	.byte	'Ifx_P_OMSR0',0,6,154,5,3
	.word	5386
	.byte	32
	.byte	'Ifx_P_OMSR12',0,6,162,5,3
	.word	5902
	.byte	32
	.byte	'Ifx_P_OMSR4',0,6,170,5,3
	.word	5555
	.byte	32
	.byte	'Ifx_P_OMSR8',0,6,178,5,3
	.word	5727
	.byte	32
	.byte	'Ifx_P_OUT',0,6,186,5,3
	.word	1729
	.byte	32
	.byte	'Ifx_P_PCSR',0,6,194,5,3
	.word	5239
	.byte	32
	.byte	'Ifx_P_PDISC',0,6,202,5,3
	.word	4953
	.byte	32
	.byte	'Ifx_P_PDR0',0,6,210,5,3
	.word	3922
	.byte	32
	.byte	'Ifx_P_PDR1',0,6,218,5,3
	.word	4238
	.byte	14
	.word	8598
	.byte	32
	.byte	'Ifx_P',0,6,139,6,3
	.word	33022
	.byte	17,28,236,10,9,1,18
	.byte	'IfxScu_CCUCON0_CLKSEL_fBack',0,0,18
	.byte	'IfxScu_CCUCON0_CLKSEL_fPll',0,1,0,32
	.byte	'IfxScu_CCUCON0_CLKSEL',0,28,240,10,3
	.word	33042
	.byte	17,28,250,10,9,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy16384',0,0,18
	.byte	'IfxScu_WDTCON1_IR_divBy256',0,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy64',0,2,0,32
	.byte	'IfxScu_WDTCON1_IR',0,28,255,10,3
	.word	33139
	.byte	10
	.byte	'_Ifx_SCU_ACCEN0_Bits',0,4,45,16,4,11
	.byte	'EN0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	641
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	641
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	641
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	641
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	641
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	641
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	641
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	641
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	641
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	641
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	641
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	641
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_SCU_ACCEN0_Bits',0,4,79,3
	.word	33261
	.byte	10
	.byte	'_Ifx_SCU_ACCEN1_Bits',0,4,82,16,4,11
	.byte	'reserved_0',0,4
	.word	466
	.byte	32,0,2,35,0,0,32
	.byte	'Ifx_SCU_ACCEN1_Bits',0,4,85,3
	.word	33818
	.byte	10
	.byte	'_Ifx_SCU_ARSTDIS_Bits',0,4,88,16,4,11
	.byte	'STM0DIS',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'STM1DIS',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'STM2DIS',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	466
	.byte	29,0,2,35,0,0,32
	.byte	'Ifx_SCU_ARSTDIS_Bits',0,4,94,3
	.word	33895
	.byte	10
	.byte	'_Ifx_SCU_CCUCON0_Bits',0,4,97,16,4,11
	.byte	'BAUD1DIV',0,1
	.word	641
	.byte	4,4,2,35,0,11
	.byte	'BAUD2DIV',0,1
	.word	641
	.byte	4,0,2,35,0,11
	.byte	'SRIDIV',0,1
	.word	641
	.byte	4,4,2,35,1,11
	.byte	'LPDIV',0,1
	.word	641
	.byte	4,0,2,35,1,11
	.byte	'SPBDIV',0,1
	.word	641
	.byte	4,4,2,35,2,11
	.byte	'FSI2DIV',0,1
	.word	641
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	641
	.byte	2,0,2,35,2,11
	.byte	'FSIDIV',0,1
	.word	641
	.byte	2,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	641
	.byte	2,4,2,35,3,11
	.byte	'CLKSEL',0,1
	.word	641
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_SCU_CCUCON0_Bits',0,4,111,3
	.word	34031
	.byte	10
	.byte	'_Ifx_SCU_CCUCON1_Bits',0,4,114,16,4,11
	.byte	'CANDIV',0,1
	.word	641
	.byte	4,4,2,35,0,11
	.byte	'ERAYDIV',0,1
	.word	641
	.byte	4,0,2,35,0,11
	.byte	'STMDIV',0,1
	.word	641
	.byte	4,4,2,35,1,11
	.byte	'GTMDIV',0,1
	.word	641
	.byte	4,0,2,35,1,11
	.byte	'ETHDIV',0,1
	.word	641
	.byte	4,4,2,35,2,11
	.byte	'ASCLINFDIV',0,1
	.word	641
	.byte	4,0,2,35,2,11
	.byte	'ASCLINSDIV',0,1
	.word	641
	.byte	4,4,2,35,3,11
	.byte	'INSEL',0,1
	.word	641
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_SCU_CCUCON1_Bits',0,4,126,3
	.word	34311
	.byte	10
	.byte	'_Ifx_SCU_CCUCON2_Bits',0,4,129,1,16,4,11
	.byte	'BBBDIV',0,1
	.word	641
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	466
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_SCU_CCUCON2_Bits',0,4,135,1,3
	.word	34549
	.byte	10
	.byte	'_Ifx_SCU_CCUCON3_Bits',0,4,138,1,16,4,11
	.byte	'PLLDIV',0,1
	.word	641
	.byte	6,2,2,35,0,11
	.byte	'PLLSEL',0,1
	.word	641
	.byte	2,0,2,35,0,11
	.byte	'PLLERAYDIV',0,1
	.word	641
	.byte	6,2,2,35,1,11
	.byte	'PLLERAYSEL',0,1
	.word	641
	.byte	2,0,2,35,1,11
	.byte	'SRIDIV',0,1
	.word	641
	.byte	6,2,2,35,2,11
	.byte	'SRISEL',0,1
	.word	641
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	641
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	641
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_SCU_CCUCON3_Bits',0,4,150,1,3
	.word	34677
	.byte	10
	.byte	'_Ifx_SCU_CCUCON4_Bits',0,4,153,1,16,4,11
	.byte	'SPBDIV',0,1
	.word	641
	.byte	6,2,2,35,0,11
	.byte	'SPBSEL',0,1
	.word	641
	.byte	2,0,2,35,0,11
	.byte	'GTMDIV',0,1
	.word	641
	.byte	6,2,2,35,1,11
	.byte	'GTMSEL',0,1
	.word	641
	.byte	2,0,2,35,1,11
	.byte	'STMDIV',0,1
	.word	641
	.byte	6,2,2,35,2,11
	.byte	'STMSEL',0,1
	.word	641
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	641
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	641
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_SCU_CCUCON4_Bits',0,4,165,1,3
	.word	34920
	.byte	10
	.byte	'_Ifx_SCU_CCUCON5_Bits',0,4,168,1,16,4,11
	.byte	'MAXDIV',0,1
	.word	641
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	466
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_SCU_CCUCON5_Bits',0,4,174,1,3
	.word	35155
	.byte	10
	.byte	'_Ifx_SCU_CCUCON6_Bits',0,4,177,1,16,4,11
	.byte	'CPU0DIV',0,1
	.word	641
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	466
	.byte	26,0,2,35,0,0,32
	.byte	'Ifx_SCU_CCUCON6_Bits',0,4,181,1,3
	.word	35283
	.byte	10
	.byte	'_Ifx_SCU_CCUCON7_Bits',0,4,184,1,16,4,11
	.byte	'CPU1DIV',0,1
	.word	641
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	466
	.byte	26,0,2,35,0,0,32
	.byte	'Ifx_SCU_CCUCON7_Bits',0,4,188,1,3
	.word	35383
	.byte	10
	.byte	'_Ifx_SCU_CHIPID_Bits',0,4,191,1,16,4,11
	.byte	'CHREV',0,1
	.word	641
	.byte	6,2,2,35,0,11
	.byte	'CHTEC',0,1
	.word	641
	.byte	2,0,2,35,0,11
	.byte	'CHID',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'EEA',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'UCODE',0,1
	.word	641
	.byte	7,0,2,35,2,11
	.byte	'FSIZE',0,1
	.word	641
	.byte	4,4,2,35,3,11
	.byte	'SP',0,1
	.word	641
	.byte	2,2,2,35,3,11
	.byte	'SEC',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	641
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_SCU_CHIPID_Bits',0,4,202,1,3
	.word	35483
	.byte	10
	.byte	'_Ifx_SCU_DTSCON_Bits',0,4,205,1,16,4,11
	.byte	'PWD',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'START',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	641
	.byte	2,4,2,35,0,11
	.byte	'CAL',0,4
	.word	466
	.byte	20,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	641
	.byte	7,1,2,35,3,11
	.byte	'SLCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_SCU_DTSCON_Bits',0,4,213,1,3
	.word	35691
	.byte	10
	.byte	'_Ifx_SCU_DTSLIM_Bits',0,4,216,1,16,4,11
	.byte	'LOWER',0,2
	.word	658
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	641
	.byte	5,1,2,35,1,11
	.byte	'LLU',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'UPPER',0,2
	.word	658
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	641
	.byte	4,2,2,35,3,11
	.byte	'SLCK',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'UOF',0,1
	.word	641
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_SCU_DTSLIM_Bits',0,4,225,1,3
	.word	35856
	.byte	10
	.byte	'_Ifx_SCU_DTSSTAT_Bits',0,4,228,1,16,4,11
	.byte	'RESULT',0,2
	.word	658
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	641
	.byte	4,2,2,35,1,11
	.byte	'RDY',0,1
	.word	641
	.byte	1,1,2,35,1,11
	.byte	'BUSY',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	658
	.byte	16,0,2,35,2,0,32
	.byte	'Ifx_SCU_DTSSTAT_Bits',0,4,235,1,3
	.word	36039
	.byte	10
	.byte	'_Ifx_SCU_EICR_Bits',0,4,238,1,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	4,4,2,35,0,11
	.byte	'EXIS0',0,1
	.word	641
	.byte	3,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'FEN0',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'REN0',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'LDEN0',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'EIEN0',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'INP0',0,1
	.word	641
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	466
	.byte	5,12,2,35,0,11
	.byte	'EXIS1',0,1
	.word	641
	.byte	3,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	641
	.byte	1,0,2,35,2,11
	.byte	'FEN1',0,1
	.word	641
	.byte	1,7,2,35,3,11
	.byte	'REN1',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'LDEN1',0,1
	.word	641
	.byte	1,5,2,35,3,11
	.byte	'EIEN1',0,1
	.word	641
	.byte	1,4,2,35,3,11
	.byte	'INP1',0,1
	.word	641
	.byte	3,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	641
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_SCU_EICR_Bits',0,4,129,2,3
	.word	36193
	.byte	10
	.byte	'_Ifx_SCU_EIFR_Bits',0,4,132,2,16,4,11
	.byte	'INTF0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'INTF1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'INTF2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'INTF3',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'INTF4',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'INTF5',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'INTF6',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'INTF7',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	466
	.byte	24,0,2,35,0,0,32
	.byte	'Ifx_SCU_EIFR_Bits',0,4,143,2,3
	.word	36557
	.byte	10
	.byte	'_Ifx_SCU_EMSR_Bits',0,4,146,2,16,4,11
	.byte	'POL',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'MODE',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'ENON',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'PSEL',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	658
	.byte	12,0,2,35,0,11
	.byte	'EMSF',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'SEMSF',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	641
	.byte	6,0,2,35,2,11
	.byte	'EMSFM',0,1
	.word	641
	.byte	2,6,2,35,3,11
	.byte	'SEMSFM',0,1
	.word	641
	.byte	2,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	641
	.byte	4,0,2,35,3,0,32
	.byte	'Ifx_SCU_EMSR_Bits',0,4,159,2,3
	.word	36768
	.byte	10
	.byte	'_Ifx_SCU_ESRCFG_Bits',0,4,162,2,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	7,1,2,35,0,11
	.byte	'EDCON',0,2
	.word	658
	.byte	2,7,2,35,0,11
	.byte	'reserved_9',0,4
	.word	466
	.byte	23,0,2,35,0,0,32
	.byte	'Ifx_SCU_ESRCFG_Bits',0,4,167,2,3
	.word	37020
	.byte	10
	.byte	'_Ifx_SCU_ESROCFG_Bits',0,4,170,2,16,4,11
	.byte	'ARI',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'ARC',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	466
	.byte	30,0,2,35,0,0,32
	.byte	'Ifx_SCU_ESROCFG_Bits',0,4,175,2,3
	.word	37138
	.byte	10
	.byte	'_Ifx_SCU_EVR13CON_Bits',0,4,178,2,16,4,11
	.byte	'reserved_0',0,4
	.word	466
	.byte	28,4,2,35,0,11
	.byte	'EVR13OFF',0,1
	.word	641
	.byte	1,3,2,35,3,11
	.byte	'BPEVR13OFF',0,1
	.word	641
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_SCU_EVR13CON_Bits',0,4,185,2,3
	.word	37249
	.byte	10
	.byte	'_Ifx_SCU_EVR33CON_Bits',0,4,188,2,16,4,11
	.byte	'reserved_0',0,4
	.word	466
	.byte	28,4,2,35,0,11
	.byte	'EVR33OFF',0,1
	.word	641
	.byte	1,3,2,35,3,11
	.byte	'BPEVR33OFF',0,1
	.word	641
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_SCU_EVR33CON_Bits',0,4,195,2,3
	.word	37412
	.byte	10
	.byte	'_Ifx_SCU_EVRADCSTAT_Bits',0,4,198,2,16,4,11
	.byte	'ADC13V',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'ADC33V',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'ADCSWDV',0,1
	.word	641
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	641
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	641
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_SCU_EVRADCSTAT_Bits',0,4,205,2,3
	.word	37575
	.byte	10
	.byte	'_Ifx_SCU_EVRDVSTAT_Bits',0,4,208,2,16,4,11
	.byte	'DVS13TRIM',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'DVS33TRIM',0,1
	.word	641
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	641
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	641
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_SCU_EVRDVSTAT_Bits',0,4,215,2,3
	.word	37733
	.byte	10
	.byte	'_Ifx_SCU_EVRMONCTRL_Bits',0,4,218,2,16,4,11
	.byte	'EVR13OVMOD',0,1
	.word	641
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	641
	.byte	2,4,2,35,0,11
	.byte	'EVR13UVMOD',0,1
	.word	641
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	641
	.byte	2,0,2,35,0,11
	.byte	'EVR33OVMOD',0,1
	.word	641
	.byte	2,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	641
	.byte	2,4,2,35,1,11
	.byte	'EVR33UVMOD',0,1
	.word	641
	.byte	2,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	641
	.byte	2,0,2,35,1,11
	.byte	'SWDOVMOD',0,1
	.word	641
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	641
	.byte	2,4,2,35,2,11
	.byte	'SWDUVMOD',0,1
	.word	641
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,2
	.word	658
	.byte	10,0,2,35,2,0,32
	.byte	'Ifx_SCU_EVRMONCTRL_Bits',0,4,232,2,3
	.word	37898
	.byte	10
	.byte	'_Ifx_SCU_EVROSCCTRL_Bits',0,4,235,2,16,4,11
	.byte	'OSCTRIM',0,2
	.word	658
	.byte	10,6,2,35,0,11
	.byte	'OSCPTAT',0,1
	.word	641
	.byte	6,0,2,35,1,11
	.byte	'OSCANASEL',0,1
	.word	641
	.byte	4,4,2,35,2,11
	.byte	'HPBGTRIM',0,2
	.word	658
	.byte	7,5,2,35,2,11
	.byte	'HPBGCLKEN',0,1
	.word	641
	.byte	1,4,2,35,3,11
	.byte	'OSC3V3',0,1
	.word	641
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	641
	.byte	2,1,2,35,3,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_SCU_EVROSCCTRL_Bits',0,4,245,2,3
	.word	38227
	.byte	10
	.byte	'_Ifx_SCU_EVROVMON_Bits',0,4,248,2,16,4,11
	.byte	'EVR13OVVAL',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'EVR33OVVAL',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'SWDOVVAL',0,1
	.word	641
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	641
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_SCU_EVROVMON_Bits',0,4,255,2,3
	.word	38448
	.byte	10
	.byte	'_Ifx_SCU_EVRRSTCON_Bits',0,4,130,3,16,4,11
	.byte	'RST13TRIM',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	466
	.byte	16,8,2,35,0,11
	.byte	'RST13OFF',0,1
	.word	641
	.byte	1,7,2,35,3,11
	.byte	'BPRST13OFF',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'RST33OFF',0,1
	.word	641
	.byte	1,5,2,35,3,11
	.byte	'BPRST33OFF',0,1
	.word	641
	.byte	1,4,2,35,3,11
	.byte	'RSTSWDOFF',0,1
	.word	641
	.byte	1,3,2,35,3,11
	.byte	'BPRSTSWDOFF',0,1
	.word	641
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_SCU_EVRRSTCON_Bits',0,4,142,3,3
	.word	38611
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF1_Bits',0,4,145,3,16,4,11
	.byte	'SD5P',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'SD5I',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'SD5D',0,1
	.word	641
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	641
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_SCU_EVRSDCOEFF1_Bits',0,4,152,3,3
	.word	38883
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF2_Bits',0,4,155,3,16,4,11
	.byte	'SD33P',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'SD33I',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'SD33D',0,1
	.word	641
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	641
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_SCU_EVRSDCOEFF2_Bits',0,4,162,3,3
	.word	39036
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF3_Bits',0,4,165,3,16,4,11
	.byte	'CT5REG0',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'CT5REG1',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'CT5REG2',0,1
	.word	641
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	641
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_SCU_EVRSDCOEFF3_Bits',0,4,172,3,3
	.word	39192
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF4_Bits',0,4,175,3,16,4,11
	.byte	'CT5REG3',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'CT5REG4',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	658
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_SCU_EVRSDCOEFF4_Bits',0,4,181,3,3
	.word	39354
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF5_Bits',0,4,184,3,16,4,11
	.byte	'CT33REG0',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'CT33REG1',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'CT33REG2',0,1
	.word	641
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	641
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_SCU_EVRSDCOEFF5_Bits',0,4,191,3,3
	.word	39497
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF6_Bits',0,4,194,3,16,4,11
	.byte	'CT33REG3',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'CT33REG4',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	658
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_SCU_EVRSDCOEFF6_Bits',0,4,200,3,3
	.word	39662
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL1_Bits',0,4,203,3,16,4,11
	.byte	'SDFREQSPRD',0,2
	.word	658
	.byte	16,0,2,35,0,11
	.byte	'SDFREQ',0,1
	.word	641
	.byte	8,0,2,35,2,11
	.byte	'SDSTEP',0,1
	.word	641
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	641
	.byte	2,2,2,35,3,11
	.byte	'SDSAMPLE',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_SCU_EVRSDCTRL1_Bits',0,4,211,3,3
	.word	39807
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL2_Bits',0,4,214,3,16,4,11
	.byte	'DRVP',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'SDMINMAXDC',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'DRVN',0,1
	.word	641
	.byte	8,0,2,35,2,11
	.byte	'SDLUT',0,1
	.word	641
	.byte	6,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_SCU_EVRSDCTRL2_Bits',0,4,222,3,3
	.word	39988
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL3_Bits',0,4,225,3,16,4,11
	.byte	'SDPWMPRE',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'SDPID',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'SDVOKLVL',0,1
	.word	641
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	641
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_SCU_EVRSDCTRL3_Bits',0,4,232,3,3
	.word	40162
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL4_Bits',0,4,235,3,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'SYNCDIV',0,1
	.word	641
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	466
	.byte	20,1,2,35,0,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_SCU_EVRSDCTRL4_Bits',0,4,241,3,3
	.word	40322
	.byte	10
	.byte	'_Ifx_SCU_EVRSTAT_Bits',0,4,244,3,16,4,11
	.byte	'EVR13',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'OV13',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'EVR33',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'OV33',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'OVSWD',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'UV13',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'UV33',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'UVSWD',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'EXTPASS13',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'EXTPASS33',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'BGPROK',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	466
	.byte	21,0,2,35,0,0,32
	.byte	'Ifx_SCU_EVRSTAT_Bits',0,4,130,4,3
	.word	40466
	.byte	10
	.byte	'_Ifx_SCU_EVRTRIM_Bits',0,4,133,4,16,4,11
	.byte	'EVR13TRIM',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'SDVOUTSEL',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	658
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_SCU_EVRTRIM_Bits',0,4,139,4,3
	.word	40740
	.byte	10
	.byte	'_Ifx_SCU_EVRUVMON_Bits',0,4,142,4,16,4,11
	.byte	'EVR13UVVAL',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'EVR33UVVAL',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'SWDUVVAL',0,1
	.word	641
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	641
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_SCU_EVRUVMON_Bits',0,4,149,4,3
	.word	40879
	.byte	10
	.byte	'_Ifx_SCU_EXTCON_Bits',0,4,152,4,16,4,11
	.byte	'EN0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'SEL0',0,1
	.word	641
	.byte	4,2,2,35,0,11
	.byte	'reserved_6',0,2
	.word	658
	.byte	10,0,2,35,0,11
	.byte	'EN1',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'NSEL',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'SEL1',0,1
	.word	641
	.byte	4,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	641
	.byte	2,0,2,35,2,11
	.byte	'DIV1',0,1
	.word	641
	.byte	8,0,2,35,3,0,32
	.byte	'Ifx_SCU_EXTCON_Bits',0,4,163,4,3
	.word	41042
	.byte	10
	.byte	'_Ifx_SCU_FDR_Bits',0,4,166,4,16,4,11
	.byte	'STEP',0,2
	.word	658
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	641
	.byte	4,2,2,35,1,11
	.byte	'DM',0,1
	.word	641
	.byte	2,0,2,35,1,11
	.byte	'RESULT',0,2
	.word	658
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	641
	.byte	5,1,2,35,3,11
	.byte	'DISCLK',0,1
	.word	641
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_SCU_FDR_Bits',0,4,174,4,3
	.word	41260
	.byte	10
	.byte	'_Ifx_SCU_FMR_Bits',0,4,177,4,16,4,11
	.byte	'FS0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'FS1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'FS2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'FS3',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'FS4',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'FS5',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'FS6',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'FS7',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'FC0',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'FC1',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'FC2',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'FC3',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'FC4',0,1
	.word	641
	.byte	1,3,2,35,2,11
	.byte	'FC5',0,1
	.word	641
	.byte	1,2,2,35,2,11
	.byte	'FC6',0,1
	.word	641
	.byte	1,1,2,35,2,11
	.byte	'FC7',0,1
	.word	641
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	641
	.byte	8,0,2,35,3,0,32
	.byte	'Ifx_SCU_FMR_Bits',0,4,197,4,3
	.word	41423
	.byte	10
	.byte	'_Ifx_SCU_ID_Bits',0,4,200,4,16,4,11
	.byte	'MODREV',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	658
	.byte	16,0,2,35,2,0,32
	.byte	'Ifx_SCU_ID_Bits',0,4,205,4,3
	.word	41759
	.byte	10
	.byte	'_Ifx_SCU_IGCR_Bits',0,4,208,4,16,4,11
	.byte	'IPEN00',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'IPEN01',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'IPEN02',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'IPEN03',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'IPEN04',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'IPEN05',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'IPEN06',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'IPEN07',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	641
	.byte	5,3,2,35,1,11
	.byte	'GEEN0',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'IGP0',0,1
	.word	641
	.byte	2,0,2,35,1,11
	.byte	'IPEN10',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'IPEN11',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'IPEN12',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'IPEN13',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'IPEN14',0,1
	.word	641
	.byte	1,3,2,35,2,11
	.byte	'IPEN15',0,1
	.word	641
	.byte	1,2,2,35,2,11
	.byte	'IPEN16',0,1
	.word	641
	.byte	1,1,2,35,2,11
	.byte	'IPEN17',0,1
	.word	641
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	641
	.byte	5,3,2,35,3,11
	.byte	'GEEN1',0,1
	.word	641
	.byte	1,2,2,35,3,11
	.byte	'IGP1',0,1
	.word	641
	.byte	2,0,2,35,3,0,32
	.byte	'Ifx_SCU_IGCR_Bits',0,4,232,4,3
	.word	41866
	.byte	10
	.byte	'_Ifx_SCU_IN_Bits',0,4,235,4,16,4,11
	.byte	'P0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	466
	.byte	30,0,2,35,0,0,32
	.byte	'Ifx_SCU_IN_Bits',0,4,240,4,3
	.word	42318
	.byte	10
	.byte	'_Ifx_SCU_IOCR_Bits',0,4,243,4,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	4,4,2,35,0,11
	.byte	'PC0',0,1
	.word	641
	.byte	4,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	641
	.byte	4,4,2,35,1,11
	.byte	'PC1',0,1
	.word	641
	.byte	4,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	658
	.byte	16,0,2,35,2,0,32
	.byte	'Ifx_SCU_IOCR_Bits',0,4,250,4,3
	.word	42417
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL0_Bits',0,4,253,4,16,4,11
	.byte	'LBISTREQ',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'LBISTREQP',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'PATTERNS',0,2
	.word	658
	.byte	14,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	658
	.byte	16,0,2,35,2,0,32
	.byte	'Ifx_SCU_LBISTCTRL0_Bits',0,4,131,5,3
	.word	42567
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL1_Bits',0,4,134,5,16,4,11
	.byte	'SEED',0,4
	.word	466
	.byte	23,9,2,35,0,11
	.byte	'reserved_23',0,1
	.word	641
	.byte	1,0,2,35,2,11
	.byte	'SPLITSH',0,1
	.word	641
	.byte	3,5,2,35,3,11
	.byte	'BODY',0,1
	.word	641
	.byte	1,4,2,35,3,11
	.byte	'LBISTFREQU',0,1
	.word	641
	.byte	4,0,2,35,3,0,32
	.byte	'Ifx_SCU_LBISTCTRL1_Bits',0,4,141,5,3
	.word	42716
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL2_Bits',0,4,144,5,16,4,11
	.byte	'SIGNATURE',0,4
	.word	466
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	641
	.byte	7,1,2,35,3,11
	.byte	'LBISTDONE',0,1
	.word	641
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_SCU_LBISTCTRL2_Bits',0,4,149,5,3
	.word	42877
	.byte	10
	.byte	'_Ifx_SCU_LCLCON_Bits',0,4,152,5,16,4,11
	.byte	'reserved_0',0,2
	.word	658
	.byte	16,0,2,35,0,11
	.byte	'LS',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,2
	.word	658
	.byte	14,1,2,35,2,11
	.byte	'LSEN',0,1
	.word	641
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_SCU_LCLCON_Bits',0,4,158,5,3
	.word	43007
	.byte	10
	.byte	'_Ifx_SCU_LCLTEST_Bits',0,4,161,5,16,4,11
	.byte	'LCLT0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'LCLT1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	466
	.byte	30,0,2,35,0,0,32
	.byte	'Ifx_SCU_LCLTEST_Bits',0,4,166,5,3
	.word	43139
	.byte	10
	.byte	'_Ifx_SCU_MANID_Bits',0,4,169,5,16,4,11
	.byte	'DEPT',0,1
	.word	641
	.byte	5,3,2,35,0,11
	.byte	'MANUF',0,2
	.word	658
	.byte	11,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	658
	.byte	16,0,2,35,2,0,32
	.byte	'Ifx_SCU_MANID_Bits',0,4,174,5,3
	.word	43254
	.byte	10
	.byte	'_Ifx_SCU_OMR_Bits',0,4,177,5,16,4,11
	.byte	'PS0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	658
	.byte	14,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	658
	.byte	14,0,2,35,2,0,32
	.byte	'Ifx_SCU_OMR_Bits',0,4,185,5,3
	.word	43365
	.byte	10
	.byte	'_Ifx_SCU_OSCCON_Bits',0,4,188,5,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'PLLLV',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'OSCRES',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'GAINSEL',0,1
	.word	641
	.byte	2,3,2,35,0,11
	.byte	'MODE',0,1
	.word	641
	.byte	2,1,2,35,0,11
	.byte	'SHBY',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'PLLHV',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'X1D',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'X1DEN',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	641
	.byte	4,0,2,35,1,11
	.byte	'OSCVAL',0,1
	.word	641
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	641
	.byte	2,1,2,35,2,11
	.byte	'APREN',0,1
	.word	641
	.byte	1,0,2,35,2,11
	.byte	'CAP0EN',0,1
	.word	641
	.byte	1,7,2,35,3,11
	.byte	'CAP1EN',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'CAP2EN',0,1
	.word	641
	.byte	1,5,2,35,3,11
	.byte	'CAP3EN',0,1
	.word	641
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	641
	.byte	4,0,2,35,3,0,32
	.byte	'Ifx_SCU_OSCCON_Bits',0,4,209,5,3
	.word	43523
	.byte	10
	.byte	'_Ifx_SCU_OUT_Bits',0,4,212,5,16,4,11
	.byte	'P0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	466
	.byte	30,0,2,35,0,0,32
	.byte	'Ifx_SCU_OUT_Bits',0,4,217,5,3
	.word	43935
	.byte	10
	.byte	'_Ifx_SCU_OVCCON_Bits',0,4,220,5,16,4,11
	.byte	'CSEL0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'CSEL1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'CSEL2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,2
	.word	658
	.byte	13,0,2,35,0,11
	.byte	'OVSTRT',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'OVSTP',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'DCINVAL',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	641
	.byte	5,0,2,35,2,11
	.byte	'OVCONF',0,1
	.word	641
	.byte	1,7,2,35,3,11
	.byte	'POVCONF',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	641
	.byte	6,0,2,35,3,0,32
	.byte	'Ifx_SCU_OVCCON_Bits',0,4,233,5,3
	.word	44036
	.byte	10
	.byte	'_Ifx_SCU_OVCENABLE_Bits',0,4,236,5,16,4,11
	.byte	'OVEN0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'OVEN1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'OVEN2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	466
	.byte	29,0,2,35,0,0,32
	.byte	'Ifx_SCU_OVCENABLE_Bits',0,4,242,5,3
	.word	44303
	.byte	10
	.byte	'_Ifx_SCU_PDISC_Bits',0,4,245,5,16,4,11
	.byte	'PDIS0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	466
	.byte	30,0,2,35,0,0,32
	.byte	'Ifx_SCU_PDISC_Bits',0,4,250,5,3
	.word	44439
	.byte	10
	.byte	'_Ifx_SCU_PDR_Bits',0,4,253,5,16,4,11
	.byte	'PD0',0,1
	.word	641
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	641
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	466
	.byte	24,0,2,35,0,0,32
	.byte	'Ifx_SCU_PDR_Bits',0,4,132,6,3
	.word	44550
	.byte	10
	.byte	'_Ifx_SCU_PDRR_Bits',0,4,135,6,16,4,11
	.byte	'PDR0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'PDR1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'PDR2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'PDR3',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'PDR4',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'PDR5',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'PDR6',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'PDR7',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	466
	.byte	24,0,2,35,0,0,32
	.byte	'Ifx_SCU_PDRR_Bits',0,4,146,6,3
	.word	44683
	.byte	10
	.byte	'_Ifx_SCU_PLLCON0_Bits',0,4,149,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'MODEN',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	658
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	641
	.byte	7,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	641
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	641
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	641
	.byte	4,0,2,35,3,0,32
	.byte	'Ifx_SCU_PLLCON0_Bits',0,4,166,6,3
	.word	44886
	.byte	10
	.byte	'_Ifx_SCU_PLLCON1_Bits',0,4,169,6,16,4,11
	.byte	'K2DIV',0,1
	.word	641
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	641
	.byte	7,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	641
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	658
	.byte	9,0,2,35,2,0,32
	.byte	'Ifx_SCU_PLLCON1_Bits',0,4,177,6,3
	.word	45242
	.byte	10
	.byte	'_Ifx_SCU_PLLCON2_Bits',0,4,180,6,16,4,11
	.byte	'MODCFG',0,2
	.word	658
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	658
	.byte	16,0,2,35,2,0,32
	.byte	'Ifx_SCU_PLLCON2_Bits',0,4,184,6,3
	.word	45420
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON0_Bits',0,4,187,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	641
	.byte	2,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	658
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	641
	.byte	5,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	641
	.byte	2,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	641
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	641
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	641
	.byte	4,0,2,35,3,0,32
	.byte	'Ifx_SCU_PLLERAYCON0_Bits',0,4,204,6,3
	.word	45520
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON1_Bits',0,4,207,6,16,4,11
	.byte	'K2DIV',0,1
	.word	641
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	641
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	641
	.byte	4,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	641
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	658
	.byte	9,0,2,35,2,0,32
	.byte	'Ifx_SCU_PLLERAYCON1_Bits',0,4,215,6,3
	.word	45890
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYSTAT_Bits',0,4,218,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'PWDSTAT',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	466
	.byte	26,0,2,35,0,0,32
	.byte	'Ifx_SCU_PLLERAYSTAT_Bits',0,4,227,6,3
	.word	46076
	.byte	10
	.byte	'_Ifx_SCU_PLLSTAT_Bits',0,4,230,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'MODRUN',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	466
	.byte	24,0,2,35,0,0,32
	.byte	'Ifx_SCU_PLLSTAT_Bits',0,4,241,6,3
	.word	46274
	.byte	10
	.byte	'_Ifx_SCU_PMCSR_Bits',0,4,244,6,16,4,11
	.byte	'REQSLP',0,1
	.word	641
	.byte	2,6,2,35,0,11
	.byte	'SMUSLP',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	641
	.byte	5,0,2,35,0,11
	.byte	'PMST',0,1
	.word	641
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	466
	.byte	21,0,2,35,0,0,32
	.byte	'Ifx_SCU_PMCSR_Bits',0,4,251,6,3
	.word	46507
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR0_Bits',0,4,254,6,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'ESR1WKEN',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'PINAWKEN',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'PINBWKEN',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'ESR0DFEN',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'ESR0EDCON',0,1
	.word	641
	.byte	2,1,2,35,0,11
	.byte	'ESR1DFEN',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'ESR1EDCON',0,1
	.word	641
	.byte	2,6,2,35,1,11
	.byte	'PINADFEN',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'PINAEDCON',0,1
	.word	641
	.byte	2,3,2,35,1,11
	.byte	'PINBDFEN',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'PINBEDCON',0,1
	.word	641
	.byte	2,0,2,35,1,11
	.byte	'SCREN',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'STBYRAMSEL',0,1
	.word	641
	.byte	2,5,2,35,2,11
	.byte	'SCRCLKSEL',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	641
	.byte	1,3,2,35,2,11
	.byte	'TRISTEN',0,1
	.word	641
	.byte	1,2,2,35,2,11
	.byte	'TRISTREQ',0,1
	.word	641
	.byte	1,1,2,35,2,11
	.byte	'PORSTDF',0,1
	.word	641
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	641
	.byte	1,7,2,35,3,11
	.byte	'DCDCSYNC',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	641
	.byte	3,3,2,35,3,11
	.byte	'ESR0TRIST',0,1
	.word	641
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_SCU_PMSWCR0_Bits',0,4,153,7,3
	.word	46659
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR1_Bits',0,4,156,7,16,4,11
	.byte	'SCRSTEN',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'SCRSTREQ',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	641
	.byte	6,0,2,35,0,11
	.byte	'CPUIDLSEL',0,1
	.word	641
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'IRADIS',0,1
	.word	641
	.byte	1,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	641
	.byte	3,0,2,35,1,11
	.byte	'SCRCFG',0,1
	.word	641
	.byte	8,0,2,35,2,11
	.byte	'CPUSEL',0,1
	.word	641
	.byte	3,5,2,35,3,11
	.byte	'STBYEVEN',0,1
	.word	641
	.byte	1,4,2,35,3,11
	.byte	'STBYEV',0,1
	.word	641
	.byte	3,1,2,35,3,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_SCU_PMSWCR1_Bits',0,4,170,7,3
	.word	47226
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR2_Bits',0,4,173,7,16,4,11
	.byte	'SCRINT',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'BUSY',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'SCRECC',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'SCRWDT',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'SCRRST',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	641
	.byte	4,0,2,35,1,11
	.byte	'TCINT',0,1
	.word	641
	.byte	8,0,2,35,2,11
	.byte	'TCINTREQ',0,1
	.word	641
	.byte	1,7,2,35,3,11
	.byte	'SMURST',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'RST',0,1
	.word	641
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	641
	.byte	4,1,2,35,3,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_SCU_PMSWCR2_Bits',0,4,187,7,3
	.word	47520
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTAT_Bits',0,4,190,7,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKP',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUN',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'PINAWKP',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUN',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'PINBWKP',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUN',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'PORSTDF',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'HWCFGEVR',0,1
	.word	641
	.byte	3,3,2,35,1,11
	.byte	'STBYRAM',0,1
	.word	641
	.byte	2,1,2,35,1,11
	.byte	'TRIST',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'SCRST',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'SCRWKP',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'SCR',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'ESR1WKEN',0,1
	.word	641
	.byte	1,3,2,35,2,11
	.byte	'PINAWKEN',0,1
	.word	641
	.byte	1,2,2,35,2,11
	.byte	'PINBWKEN',0,1
	.word	641
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	658
	.byte	4,5,2,35,2,11
	.byte	'ESR0TRIST',0,1
	.word	641
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	641
	.byte	4,0,2,35,3,0,32
	.byte	'Ifx_SCU_PMSWSTAT_Bits',0,4,214,7,3
	.word	47798
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTATCLR_Bits',0,4,217,7,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKPCLR',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUNCLR',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'PINAWKPCLR',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUNCLR',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'PINBWKPCLR',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUNCLR',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'SCRSTCLR',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'SCRWKPCLR',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	658
	.byte	14,0,2,35,2,0,32
	.byte	'Ifx_SCU_PMSWSTATCLR_Bits',0,4,230,7,3
	.word	48294
	.byte	10
	.byte	'_Ifx_SCU_RSTCON2_Bits',0,4,233,7,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'CLRC',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	658
	.byte	10,4,2,35,0,11
	.byte	'CSS0',0,1
	.word	641
	.byte	1,3,2,35,1,11
	.byte	'CSS1',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'CSS2',0,1
	.word	641
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'USRINFO',0,2
	.word	658
	.byte	16,0,2,35,2,0,32
	.byte	'Ifx_SCU_RSTCON2_Bits',0,4,243,7,3
	.word	48607
	.byte	10
	.byte	'_Ifx_SCU_RSTCON_Bits',0,4,246,7,16,4,11
	.byte	'ESR0',0,1
	.word	641
	.byte	2,6,2,35,0,11
	.byte	'ESR1',0,1
	.word	641
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	641
	.byte	2,2,2,35,0,11
	.byte	'SMU',0,1
	.word	641
	.byte	2,0,2,35,0,11
	.byte	'SW',0,1
	.word	641
	.byte	2,6,2,35,1,11
	.byte	'STM0',0,1
	.word	641
	.byte	2,4,2,35,1,11
	.byte	'STM1',0,1
	.word	641
	.byte	2,2,2,35,1,11
	.byte	'STM2',0,1
	.word	641
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	658
	.byte	16,0,2,35,2,0,32
	.byte	'Ifx_SCU_RSTCON_Bits',0,4,129,8,3
	.word	48816
	.byte	10
	.byte	'_Ifx_SCU_RSTSTAT_Bits',0,4,132,8,16,4,11
	.byte	'ESR0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'ESR1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'SMU',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'SW',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'STM0',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'STM1',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'STM2',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'PORST',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'CB0',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'CB1',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'CB3',0,1
	.word	641
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	641
	.byte	2,1,2,35,2,11
	.byte	'EVR13',0,1
	.word	641
	.byte	1,0,2,35,2,11
	.byte	'EVR33',0,1
	.word	641
	.byte	1,7,2,35,3,11
	.byte	'SWD',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	641
	.byte	2,4,2,35,3,11
	.byte	'STBYR',0,1
	.word	641
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	641
	.byte	3,0,2,35,3,0,32
	.byte	'Ifx_SCU_RSTSTAT_Bits',0,4,155,8,3
	.word	49027
	.byte	10
	.byte	'_Ifx_SCU_SAFECON_Bits',0,4,158,8,16,4,11
	.byte	'HBT',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	466
	.byte	31,0,2,35,0,0,32
	.byte	'Ifx_SCU_SAFECON_Bits',0,4,162,8,3
	.word	49459
	.byte	10
	.byte	'_Ifx_SCU_STSTAT_Bits',0,4,165,8,16,4,11
	.byte	'HWCFG',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'FTM',0,1
	.word	641
	.byte	7,1,2,35,1,11
	.byte	'MODE',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'FCBAE',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'LUDIS',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'TRSTL',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'SPDEN',0,1
	.word	641
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	641
	.byte	3,0,2,35,2,11
	.byte	'RAMINT',0,1
	.word	641
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	641
	.byte	7,0,2,35,3,0,32
	.byte	'Ifx_SCU_STSTAT_Bits',0,4,178,8,3
	.word	49555
	.byte	10
	.byte	'_Ifx_SCU_SWRSTCON_Bits',0,4,181,8,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'SWRSTREQ',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	466
	.byte	30,0,2,35,0,0,32
	.byte	'Ifx_SCU_SWRSTCON_Bits',0,4,186,8,3
	.word	49815
	.byte	10
	.byte	'_Ifx_SCU_SYSCON_Bits',0,4,189,8,16,4,11
	.byte	'CCTRIG0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'RAMINTM',0,1
	.word	641
	.byte	2,4,2,35,0,11
	.byte	'SETLUDIS',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	641
	.byte	3,0,2,35,0,11
	.byte	'DATM',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,4
	.word	466
	.byte	23,0,2,35,0,0,32
	.byte	'Ifx_SCU_SYSCON_Bits',0,4,198,8,3
	.word	49940
	.byte	10
	.byte	'_Ifx_SCU_TRAPCLR_Bits',0,4,201,8,16,4,11
	.byte	'ESR0T',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	466
	.byte	28,0,2,35,0,0,32
	.byte	'Ifx_SCU_TRAPCLR_Bits',0,4,208,8,3
	.word	50137
	.byte	10
	.byte	'_Ifx_SCU_TRAPDIS_Bits',0,4,211,8,16,4,11
	.byte	'ESR0T',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	466
	.byte	28,0,2,35,0,0,32
	.byte	'Ifx_SCU_TRAPDIS_Bits',0,4,218,8,3
	.word	50290
	.byte	10
	.byte	'_Ifx_SCU_TRAPSET_Bits',0,4,221,8,16,4,11
	.byte	'ESR0T',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	466
	.byte	28,0,2,35,0,0,32
	.byte	'Ifx_SCU_TRAPSET_Bits',0,4,228,8,3
	.word	50443
	.byte	10
	.byte	'_Ifx_SCU_TRAPSTAT_Bits',0,4,231,8,16,4,11
	.byte	'ESR0T',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	466
	.byte	28,0,2,35,0,0,32
	.byte	'Ifx_SCU_TRAPSTAT_Bits',0,4,238,8,3
	.word	50596
	.byte	32
	.byte	'Ifx_SCU_WDTCPU_CON0_Bits',0,4,247,8,3
	.word	505
	.byte	32
	.byte	'Ifx_SCU_WDTCPU_CON1_Bits',0,4,134,9,3
	.word	680
	.byte	32
	.byte	'Ifx_SCU_WDTCPU_SR_Bits',0,4,150,9,3
	.word	924
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON0_Bits',0,4,153,9,16,4,11
	.byte	'ENDINIT',0,4
	.word	489
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	489
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	489
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	489
	.byte	16,0,2,35,0,0,32
	.byte	'Ifx_SCU_WDTS_CON0_Bits',0,4,159,9,3
	.word	50851
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON1_Bits',0,4,162,9,16,4,11
	.byte	'CLRIRF',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'IR0',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	641
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	658
	.byte	16,0,2,35,2,0,32
	.byte	'Ifx_SCU_WDTS_CON1_Bits',0,4,175,9,3
	.word	50977
	.byte	10
	.byte	'_Ifx_SCU_WDTS_SR_Bits',0,4,178,9,16,4,11
	.byte	'AE',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	641
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	658
	.byte	16,0,2,35,2,0,32
	.byte	'Ifx_SCU_WDTS_SR_Bits',0,4,191,9,3
	.word	51229
	.byte	12,4,199,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33261
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_ACCEN0',0,4,204,9,3
	.word	51448
	.byte	12,4,207,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33818
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_ACCEN1',0,4,212,9,3
	.word	51512
	.byte	12,4,215,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33895
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_ARSTDIS',0,4,220,9,3
	.word	51576
	.byte	12,4,223,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34031
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_CCUCON0',0,4,228,9,3
	.word	51641
	.byte	12,4,231,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34311
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_CCUCON1',0,4,236,9,3
	.word	51706
	.byte	12,4,239,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34549
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_CCUCON2',0,4,244,9,3
	.word	51771
	.byte	12,4,247,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34677
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_CCUCON3',0,4,252,9,3
	.word	51836
	.byte	12,4,255,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34920
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_CCUCON4',0,4,132,10,3
	.word	51901
	.byte	12,4,135,10,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35155
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_CCUCON5',0,4,140,10,3
	.word	51966
	.byte	12,4,143,10,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35283
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_CCUCON6',0,4,148,10,3
	.word	52031
	.byte	12,4,151,10,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35383
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_CCUCON7',0,4,156,10,3
	.word	52096
	.byte	12,4,159,10,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35483
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_CHIPID',0,4,164,10,3
	.word	52161
	.byte	12,4,167,10,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35691
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_DTSCON',0,4,172,10,3
	.word	52225
	.byte	12,4,175,10,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35856
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_DTSLIM',0,4,180,10,3
	.word	52289
	.byte	12,4,183,10,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36039
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_DTSSTAT',0,4,188,10,3
	.word	52353
	.byte	12,4,191,10,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36193
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_EICR',0,4,196,10,3
	.word	52418
	.byte	12,4,199,10,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36557
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_EIFR',0,4,204,10,3
	.word	52480
	.byte	12,4,207,10,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36768
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_EMSR',0,4,212,10,3
	.word	52542
	.byte	12,4,215,10,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37020
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_ESRCFG',0,4,220,10,3
	.word	52604
	.byte	12,4,223,10,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37138
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_ESROCFG',0,4,228,10,3
	.word	52668
	.byte	12,4,231,10,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37249
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_EVR13CON',0,4,236,10,3
	.word	52733
	.byte	12,4,239,10,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37412
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_EVR33CON',0,4,244,10,3
	.word	52799
	.byte	12,4,247,10,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37575
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_EVRADCSTAT',0,4,252,10,3
	.word	52865
	.byte	12,4,255,10,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37733
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_EVRDVSTAT',0,4,132,11,3
	.word	52933
	.byte	12,4,135,11,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37898
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_EVRMONCTRL',0,4,140,11,3
	.word	53000
	.byte	12,4,143,11,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38227
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_EVROSCCTRL',0,4,148,11,3
	.word	53068
	.byte	12,4,151,11,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38448
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_EVROVMON',0,4,156,11,3
	.word	53136
	.byte	12,4,159,11,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38611
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_EVRRSTCON',0,4,164,11,3
	.word	53202
	.byte	12,4,167,11,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38883
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_EVRSDCOEFF1',0,4,172,11,3
	.word	53269
	.byte	12,4,175,11,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39036
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_EVRSDCOEFF2',0,4,180,11,3
	.word	53338
	.byte	12,4,183,11,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39192
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_EVRSDCOEFF3',0,4,188,11,3
	.word	53407
	.byte	12,4,191,11,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39354
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_EVRSDCOEFF4',0,4,196,11,3
	.word	53476
	.byte	12,4,199,11,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39497
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_EVRSDCOEFF5',0,4,204,11,3
	.word	53545
	.byte	12,4,207,11,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39662
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_EVRSDCOEFF6',0,4,212,11,3
	.word	53614
	.byte	12,4,215,11,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39807
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_EVRSDCTRL1',0,4,220,11,3
	.word	53683
	.byte	12,4,223,11,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39988
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_EVRSDCTRL2',0,4,228,11,3
	.word	53751
	.byte	12,4,231,11,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40162
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_EVRSDCTRL3',0,4,236,11,3
	.word	53819
	.byte	12,4,239,11,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40322
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_EVRSDCTRL4',0,4,244,11,3
	.word	53887
	.byte	12,4,247,11,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40466
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_EVRSTAT',0,4,252,11,3
	.word	53955
	.byte	12,4,255,11,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40740
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_EVRTRIM',0,4,132,12,3
	.word	54020
	.byte	12,4,135,12,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40879
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_EVRUVMON',0,4,140,12,3
	.word	54085
	.byte	12,4,143,12,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41042
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_EXTCON',0,4,148,12,3
	.word	54151
	.byte	12,4,151,12,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41260
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_FDR',0,4,156,12,3
	.word	54215
	.byte	12,4,159,12,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41423
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_FMR',0,4,164,12,3
	.word	54276
	.byte	12,4,167,12,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41759
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_ID',0,4,172,12,3
	.word	54337
	.byte	12,4,175,12,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41866
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_IGCR',0,4,180,12,3
	.word	54397
	.byte	12,4,183,12,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42318
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_IN',0,4,188,12,3
	.word	54459
	.byte	12,4,191,12,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42417
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_IOCR',0,4,196,12,3
	.word	54519
	.byte	12,4,199,12,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42567
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_LBISTCTRL0',0,4,204,12,3
	.word	54581
	.byte	12,4,207,12,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42716
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_LBISTCTRL1',0,4,212,12,3
	.word	54649
	.byte	12,4,215,12,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42877
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_LBISTCTRL2',0,4,220,12,3
	.word	54717
	.byte	12,4,223,12,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43007
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_LCLCON',0,4,228,12,3
	.word	54785
	.byte	12,4,231,12,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43139
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_LCLTEST',0,4,236,12,3
	.word	54849
	.byte	12,4,239,12,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43254
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_MANID',0,4,244,12,3
	.word	54914
	.byte	12,4,247,12,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43365
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_OMR',0,4,252,12,3
	.word	54977
	.byte	12,4,255,12,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43523
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_OSCCON',0,4,132,13,3
	.word	55038
	.byte	12,4,135,13,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43935
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_OUT',0,4,140,13,3
	.word	55102
	.byte	12,4,143,13,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44036
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_OVCCON',0,4,148,13,3
	.word	55163
	.byte	12,4,151,13,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44303
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_OVCENABLE',0,4,156,13,3
	.word	55227
	.byte	12,4,159,13,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44439
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_PDISC',0,4,164,13,3
	.word	55294
	.byte	12,4,167,13,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44550
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_PDR',0,4,172,13,3
	.word	55357
	.byte	12,4,175,13,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44683
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_PDRR',0,4,180,13,3
	.word	55418
	.byte	12,4,183,13,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44886
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_PLLCON0',0,4,188,13,3
	.word	55480
	.byte	12,4,191,13,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45242
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_PLLCON1',0,4,196,13,3
	.word	55545
	.byte	12,4,199,13,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45420
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_PLLCON2',0,4,204,13,3
	.word	55610
	.byte	12,4,207,13,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45520
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_PLLERAYCON0',0,4,212,13,3
	.word	55675
	.byte	12,4,215,13,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45890
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_PLLERAYCON1',0,4,220,13,3
	.word	55744
	.byte	12,4,223,13,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46076
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_PLLERAYSTAT',0,4,228,13,3
	.word	55813
	.byte	12,4,231,13,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46274
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_PLLSTAT',0,4,236,13,3
	.word	55882
	.byte	12,4,239,13,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46507
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_PMCSR',0,4,244,13,3
	.word	55947
	.byte	12,4,247,13,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46659
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_PMSWCR0',0,4,252,13,3
	.word	56010
	.byte	12,4,255,13,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47226
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_PMSWCR1',0,4,132,14,3
	.word	56075
	.byte	12,4,135,14,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47520
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_PMSWCR2',0,4,140,14,3
	.word	56140
	.byte	12,4,143,14,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47798
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_PMSWSTAT',0,4,148,14,3
	.word	56205
	.byte	12,4,151,14,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48294
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_PMSWSTATCLR',0,4,156,14,3
	.word	56271
	.byte	12,4,159,14,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48816
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_RSTCON',0,4,164,14,3
	.word	56340
	.byte	12,4,167,14,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48607
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_RSTCON2',0,4,172,14,3
	.word	56404
	.byte	12,4,175,14,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49027
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_RSTSTAT',0,4,180,14,3
	.word	56469
	.byte	12,4,183,14,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49459
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_SAFECON',0,4,188,14,3
	.word	56534
	.byte	12,4,191,14,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49555
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_STSTAT',0,4,196,14,3
	.word	56599
	.byte	12,4,199,14,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49815
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_SWRSTCON',0,4,204,14,3
	.word	56663
	.byte	12,4,207,14,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49940
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_SYSCON',0,4,212,14,3
	.word	56729
	.byte	12,4,215,14,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50137
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_TRAPCLR',0,4,220,14,3
	.word	56793
	.byte	12,4,223,14,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50290
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_TRAPDIS',0,4,228,14,3
	.word	56858
	.byte	12,4,231,14,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50443
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_TRAPSET',0,4,236,14,3
	.word	56923
	.byte	12,4,239,14,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50596
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_TRAPSTAT',0,4,244,14,3
	.word	56988
	.byte	32
	.byte	'Ifx_SCU_WDTCPU_CON0',0,4,252,14,3
	.word	601
	.byte	32
	.byte	'Ifx_SCU_WDTCPU_CON1',0,4,132,15,3
	.word	884
	.byte	32
	.byte	'Ifx_SCU_WDTCPU_SR',0,4,140,15,3
	.word	1115
	.byte	12,4,143,15,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50851
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_WDTS_CON0',0,4,148,15,3
	.word	57139
	.byte	12,4,151,15,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50977
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_WDTS_CON1',0,4,156,15,3
	.word	57206
	.byte	12,4,159,15,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51229
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_WDTS_SR',0,4,164,15,3
	.word	57273
	.byte	14
	.word	1155
	.byte	32
	.byte	'Ifx_SCU_WDTCPU',0,4,180,15,3
	.word	57338
	.byte	10
	.byte	'_Ifx_SCU_WDTS',0,4,183,15,25,12,13
	.byte	'CON0',0
	.word	57139
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	57206
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	57273
	.byte	4,2,35,8,0,14
	.word	57367
	.byte	32
	.byte	'Ifx_SCU_WDTS',0,4,188,15,3
	.word	57428
	.byte	15,8
	.word	52604
	.byte	16,1,0,15,20
	.word	641
	.byte	16,19,0,15,8
	.word	55947
	.byte	16,1,0,14
	.word	57367
	.byte	15,24
	.word	1155
	.byte	16,1,0,14
	.word	57487
	.byte	15,16
	.word	641
	.byte	16,15,0,15,28
	.word	641
	.byte	16,27,0,15,40
	.word	641
	.byte	16,39,0,15,16
	.word	52418
	.byte	16,3,0,15,16
	.word	54397
	.byte	16,3,0,15,180,3
	.word	641
	.byte	16,179,3,0,10
	.byte	'_Ifx_SCU',0,4,201,15,25,128,8,13
	.byte	'reserved_0',0
	.word	4278
	.byte	8,2,35,0,13
	.byte	'ID',0
	.word	54337
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	2459
	.byte	4,2,35,12,13
	.byte	'OSCCON',0
	.word	55038
	.byte	4,2,35,16,13
	.byte	'PLLSTAT',0
	.word	55882
	.byte	4,2,35,20,13
	.byte	'PLLCON0',0
	.word	55480
	.byte	4,2,35,24,13
	.byte	'PLLCON1',0
	.word	55545
	.byte	4,2,35,28,13
	.byte	'PLLCON2',0
	.word	55610
	.byte	4,2,35,32,13
	.byte	'PLLERAYSTAT',0
	.word	55813
	.byte	4,2,35,36,13
	.byte	'PLLERAYCON0',0
	.word	55675
	.byte	4,2,35,40,13
	.byte	'PLLERAYCON1',0
	.word	55744
	.byte	4,2,35,44,13
	.byte	'CCUCON0',0
	.word	51641
	.byte	4,2,35,48,13
	.byte	'CCUCON1',0
	.word	51706
	.byte	4,2,35,52,13
	.byte	'FDR',0
	.word	54215
	.byte	4,2,35,56,13
	.byte	'EXTCON',0
	.word	54151
	.byte	4,2,35,60,13
	.byte	'CCUCON2',0
	.word	51771
	.byte	4,2,35,64,13
	.byte	'CCUCON3',0
	.word	51836
	.byte	4,2,35,68,13
	.byte	'CCUCON4',0
	.word	51901
	.byte	4,2,35,72,13
	.byte	'CCUCON5',0
	.word	51966
	.byte	4,2,35,76,13
	.byte	'RSTSTAT',0
	.word	56469
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	2459
	.byte	4,2,35,84,13
	.byte	'RSTCON',0
	.word	56340
	.byte	4,2,35,88,13
	.byte	'ARSTDIS',0
	.word	51576
	.byte	4,2,35,92,13
	.byte	'SWRSTCON',0
	.word	56663
	.byte	4,2,35,96,13
	.byte	'RSTCON2',0
	.word	56404
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	2459
	.byte	4,2,35,104,13
	.byte	'EVRRSTCON',0
	.word	53202
	.byte	4,2,35,108,13
	.byte	'ESRCFG',0
	.word	57455
	.byte	8,2,35,112,13
	.byte	'ESROCFG',0
	.word	52668
	.byte	4,2,35,120,13
	.byte	'SYSCON',0
	.word	56729
	.byte	4,2,35,124,13
	.byte	'CCUCON6',0
	.word	52031
	.byte	4,3,35,128,1,13
	.byte	'CCUCON7',0
	.word	52096
	.byte	4,3,35,132,1,13
	.byte	'reserved_88',0
	.word	57464
	.byte	20,3,35,136,1,13
	.byte	'PDR',0
	.word	55357
	.byte	4,3,35,156,1,13
	.byte	'IOCR',0
	.word	54519
	.byte	4,3,35,160,1,13
	.byte	'OUT',0
	.word	55102
	.byte	4,3,35,164,1,13
	.byte	'OMR',0
	.word	54977
	.byte	4,3,35,168,1,13
	.byte	'IN',0
	.word	54459
	.byte	4,3,35,172,1,13
	.byte	'EVRSTAT',0
	.word	53955
	.byte	4,3,35,176,1,13
	.byte	'EVRDVSTAT',0
	.word	52933
	.byte	4,3,35,180,1,13
	.byte	'EVR13CON',0
	.word	52733
	.byte	4,3,35,184,1,13
	.byte	'EVR33CON',0
	.word	52799
	.byte	4,3,35,188,1,13
	.byte	'STSTAT',0
	.word	56599
	.byte	4,3,35,192,1,13
	.byte	'reserved_C4',0
	.word	2459
	.byte	4,3,35,196,1,13
	.byte	'PMSWCR0',0
	.word	56010
	.byte	4,3,35,200,1,13
	.byte	'PMSWSTAT',0
	.word	56205
	.byte	4,3,35,204,1,13
	.byte	'PMSWSTATCLR',0
	.word	56271
	.byte	4,3,35,208,1,13
	.byte	'PMCSR',0
	.word	57473
	.byte	8,3,35,212,1,13
	.byte	'reserved_DC',0
	.word	2459
	.byte	4,3,35,220,1,13
	.byte	'DTSSTAT',0
	.word	52353
	.byte	4,3,35,224,1,13
	.byte	'DTSCON',0
	.word	52225
	.byte	4,3,35,228,1,13
	.byte	'PMSWCR1',0
	.word	56075
	.byte	4,3,35,232,1,13
	.byte	'PMSWCR2',0
	.word	56140
	.byte	4,3,35,236,1,13
	.byte	'WDTS',0
	.word	57482
	.byte	12,3,35,240,1,13
	.byte	'EMSR',0
	.word	52542
	.byte	4,3,35,252,1,13
	.byte	'WDTCPU',0
	.word	57496
	.byte	24,3,35,128,2,13
	.byte	'reserved_118',0
	.word	4618
	.byte	12,3,35,152,2,13
	.byte	'TRAPSTAT',0
	.word	56988
	.byte	4,3,35,164,2,13
	.byte	'TRAPSET',0
	.word	56923
	.byte	4,3,35,168,2,13
	.byte	'TRAPCLR',0
	.word	56793
	.byte	4,3,35,172,2,13
	.byte	'TRAPDIS',0
	.word	56858
	.byte	4,3,35,176,2,13
	.byte	'reserved_134',0
	.word	2459
	.byte	4,3,35,180,2,13
	.byte	'LCLCON1',0
	.word	54785
	.byte	4,3,35,184,2,13
	.byte	'LCLTEST',0
	.word	54849
	.byte	4,3,35,188,2,13
	.byte	'CHIPID',0
	.word	52161
	.byte	4,3,35,192,2,13
	.byte	'MANID',0
	.word	54914
	.byte	4,3,35,196,2,13
	.byte	'reserved_148',0
	.word	4278
	.byte	8,3,35,200,2,13
	.byte	'SAFECON',0
	.word	56534
	.byte	4,3,35,208,2,13
	.byte	'reserved_154',0
	.word	57501
	.byte	16,3,35,212,2,13
	.byte	'LBISTCTRL0',0
	.word	54581
	.byte	4,3,35,228,2,13
	.byte	'LBISTCTRL1',0
	.word	54649
	.byte	4,3,35,232,2,13
	.byte	'LBISTCTRL2',0
	.word	54717
	.byte	4,3,35,236,2,13
	.byte	'reserved_170',0
	.word	57510
	.byte	28,3,35,240,2,13
	.byte	'PDISC',0
	.word	55294
	.byte	4,3,35,140,3,13
	.byte	'reserved_190',0
	.word	4278
	.byte	8,3,35,144,3,13
	.byte	'EVRTRIM',0
	.word	54020
	.byte	4,3,35,152,3,13
	.byte	'EVRADCSTAT',0
	.word	52865
	.byte	4,3,35,156,3,13
	.byte	'EVRUVMON',0
	.word	54085
	.byte	4,3,35,160,3,13
	.byte	'EVROVMON',0
	.word	53136
	.byte	4,3,35,164,3,13
	.byte	'EVRMONCTRL',0
	.word	53000
	.byte	4,3,35,168,3,13
	.byte	'reserved_1AC',0
	.word	2459
	.byte	4,3,35,172,3,13
	.byte	'EVRSDCTRL1',0
	.word	53683
	.byte	4,3,35,176,3,13
	.byte	'EVRSDCTRL2',0
	.word	53751
	.byte	4,3,35,180,3,13
	.byte	'EVRSDCTRL3',0
	.word	53819
	.byte	4,3,35,184,3,13
	.byte	'EVRSDCTRL4',0
	.word	53887
	.byte	4,3,35,188,3,13
	.byte	'EVRSDCOEFF1',0
	.word	53269
	.byte	4,3,35,192,3,13
	.byte	'EVRSDCOEFF2',0
	.word	53338
	.byte	4,3,35,196,3,13
	.byte	'EVRSDCOEFF3',0
	.word	53407
	.byte	4,3,35,200,3,13
	.byte	'EVRSDCOEFF4',0
	.word	53476
	.byte	4,3,35,204,3,13
	.byte	'EVRSDCOEFF5',0
	.word	53545
	.byte	4,3,35,208,3,13
	.byte	'EVRSDCOEFF6',0
	.word	53614
	.byte	4,3,35,212,3,13
	.byte	'EVROSCCTRL',0
	.word	53068
	.byte	4,3,35,216,3,13
	.byte	'reserved_1DC',0
	.word	2459
	.byte	4,3,35,220,3,13
	.byte	'OVCENABLE',0
	.word	55227
	.byte	4,3,35,224,3,13
	.byte	'OVCCON',0
	.word	55163
	.byte	4,3,35,228,3,13
	.byte	'reserved_1E8',0
	.word	57519
	.byte	40,3,35,232,3,13
	.byte	'EICR',0
	.word	57528
	.byte	16,3,35,144,4,13
	.byte	'EIFR',0
	.word	52480
	.byte	4,3,35,160,4,13
	.byte	'FMR',0
	.word	54276
	.byte	4,3,35,164,4,13
	.byte	'PDRR',0
	.word	55418
	.byte	4,3,35,168,4,13
	.byte	'IGCR',0
	.word	57537
	.byte	16,3,35,172,4,13
	.byte	'reserved_23C',0
	.word	2459
	.byte	4,3,35,188,4,13
	.byte	'DTSLIM',0
	.word	52289
	.byte	4,3,35,192,4,13
	.byte	'reserved_244',0
	.word	57546
	.byte	180,3,3,35,196,4,13
	.byte	'ACCEN1',0
	.word	51512
	.byte	4,3,35,248,7,13
	.byte	'ACCEN0',0
	.word	51448
	.byte	4,3,35,252,7,0,14
	.word	57557
	.byte	32
	.byte	'Ifx_SCU',0,4,181,16,3
	.word	59547
	.byte	32
	.byte	'IfxPort_InputMode',0,5,89,3
	.word	9211
	.byte	32
	.byte	'IfxPort_OutputIdx',0,5,130,1,3
	.word	9486
	.byte	32
	.byte	'IfxPort_OutputMode',0,5,138,1,3
	.word	9416
	.byte	32
	.byte	'IfxPort_PadDriver',0,5,158,1,3
	.word	29121
	.byte	32
	.byte	'IfxPort_State',0,5,178,1,3
	.word	9799
	.byte	32
	.byte	'IfxPort_Pin',0,5,194,1,3
	.word	28719
	.byte	32
	.byte	'Ifx_ASCLIN_ACCEN0_Bits',0,16,79,3
	.word	20360
	.byte	32
	.byte	'Ifx_ASCLIN_ACCEN1_Bits',0,16,85,3
	.word	20268
	.byte	32
	.byte	'Ifx_ASCLIN_BITCON_Bits',0,16,97,3
	.word	15892
	.byte	32
	.byte	'Ifx_ASCLIN_BRD_Bits',0,16,106,3
	.word	16732
	.byte	32
	.byte	'Ifx_ASCLIN_BRG_Bits',0,16,115,3
	.word	16575
	.byte	32
	.byte	'Ifx_ASCLIN_CLC_Bits',0,16,125,3
	.word	14847
	.byte	32
	.byte	'Ifx_ASCLIN_CSR_Bits',0,16,133,1,3
	.word	19541
	.byte	32
	.byte	'Ifx_ASCLIN_DATCON_Bits',0,16,145,1,3
	.word	16378
	.byte	32
	.byte	'Ifx_ASCLIN_FLAGS_Bits',0,16,177,1,3
	.word	17388
	.byte	32
	.byte	'Ifx_ASCLIN_FLAGSCLEAR_Bits',0,16,209,1,3
	.word	18387
	.byte	32
	.byte	'Ifx_ASCLIN_FLAGSENABLE_Bits',0,16,238,1,3
	.word	18902
	.byte	32
	.byte	'Ifx_ASCLIN_FLAGSSET_Bits',0,16,142,2,3
	.word	17874
	.byte	32
	.byte	'Ifx_ASCLIN_FRAMECON_Bits',0,16,158,2,3
	.word	16113
	.byte	32
	.byte	'Ifx_ASCLIN_ID_Bits',0,16,166,2,3
	.word	15301
	.byte	32
	.byte	'Ifx_ASCLIN_IOCR_Bits',0,16,184,2,3
	.word	15006
	.byte	32
	.byte	'Ifx_ASCLIN_KRST0_Bits',0,16,192,2,3
	.word	20142
	.byte	32
	.byte	'Ifx_ASCLIN_KRST1_Bits',0,16,199,2,3
	.word	20035
	.byte	32
	.byte	'Ifx_ASCLIN_KRSTCLR_Bits',0,16,206,2,3
	.word	19926
	.byte	32
	.byte	'Ifx_ASCLIN_LIN_BTIMER_Bits',0,16,213,2,3
	.word	17086
	.byte	32
	.byte	'Ifx_ASCLIN_LIN_CON_Bits',0,16,225,2,3
	.word	16886
	.byte	32
	.byte	'Ifx_ASCLIN_LIN_HTIMER_Bits',0,16,232,2,3
	.word	17200
	.byte	32
	.byte	'Ifx_ASCLIN_OCS_Bits',0,16,242,2,3
	.word	19763
	.byte	32
	.byte	'Ifx_ASCLIN_RXDATA_Bits',0,16,248,2,3
	.word	19454
	.byte	32
	.byte	'Ifx_ASCLIN_RXDATAD_Bits',0,16,254,2,3
	.word	19664
	.byte	32
	.byte	'Ifx_ASCLIN_RXFIFOCON_Bits',0,16,140,3,3
	.word	15651
	.byte	32
	.byte	'Ifx_ASCLIN_TXDATA_Bits',0,16,146,3,3
	.word	19367
	.byte	32
	.byte	'Ifx_ASCLIN_TXFIFOCON_Bits',0,16,159,3,3
	.word	15426
	.byte	32
	.byte	'Ifx_ASCLIN_ACCEN0',0,16,172,3,3
	.word	20892
	.byte	32
	.byte	'Ifx_ASCLIN_ACCEN1',0,16,180,3,3
	.word	20320
	.byte	32
	.byte	'Ifx_ASCLIN_BITCON',0,16,188,3,3
	.word	16073
	.byte	32
	.byte	'Ifx_ASCLIN_BRD',0,16,196,3,3
	.word	16846
	.byte	32
	.byte	'Ifx_ASCLIN_BRG',0,16,204,3,3
	.word	16692
	.byte	32
	.byte	'Ifx_ASCLIN_CLC',0,16,212,3,3
	.word	14966
	.byte	32
	.byte	'Ifx_ASCLIN_CSR',0,16,220,3,3
	.word	19624
	.byte	32
	.byte	'Ifx_ASCLIN_DATCON',0,16,228,3,3
	.word	16535
	.byte	32
	.byte	'Ifx_ASCLIN_FLAGS',0,16,236,3,3
	.word	17834
	.byte	32
	.byte	'Ifx_ASCLIN_FLAGSCLEAR',0,16,244,3,3
	.word	18862
	.byte	32
	.byte	'Ifx_ASCLIN_FLAGSENABLE',0,16,252,3,3
	.word	19327
	.byte	32
	.byte	'Ifx_ASCLIN_FLAGSSET',0,16,132,4,3
	.word	18347
	.byte	32
	.byte	'Ifx_ASCLIN_FRAMECON',0,16,140,4,3
	.word	16338
	.byte	32
	.byte	'Ifx_ASCLIN_ID',0,16,148,4,3
	.word	15386
	.byte	32
	.byte	'Ifx_ASCLIN_IOCR',0,16,156,4,3
	.word	15261
	.byte	32
	.byte	'Ifx_ASCLIN_KRST0',0,16,164,4,3
	.word	20228
	.byte	32
	.byte	'Ifx_ASCLIN_KRST1',0,16,172,4,3
	.word	20102
	.byte	32
	.byte	'Ifx_ASCLIN_KRSTCLR',0,16,180,4,3
	.word	19995
	.byte	32
	.byte	'Ifx_ASCLIN_LIN_BTIMER',0,16,188,4,3
	.word	17160
	.byte	32
	.byte	'Ifx_ASCLIN_LIN_CON',0,16,196,4,3
	.word	17046
	.byte	32
	.byte	'Ifx_ASCLIN_LIN_HTIMER',0,16,204,4,3
	.word	17275
	.byte	32
	.byte	'Ifx_ASCLIN_OCS',0,16,212,4,3
	.word	19886
	.byte	32
	.byte	'Ifx_ASCLIN_RXDATA',0,16,220,4,3
	.word	19501
	.byte	32
	.byte	'Ifx_ASCLIN_RXDATAD',0,16,228,4,3
	.word	19712
	.byte	32
	.byte	'Ifx_ASCLIN_RXFIFOCON',0,16,236,4,3
	.word	15852
	.byte	32
	.byte	'Ifx_ASCLIN_TXDATA',0,16,244,4,3
	.word	19414
	.byte	32
	.byte	'Ifx_ASCLIN_TXFIFOCON',0,16,252,4,3
	.word	15611
	.byte	14
	.word	17315
	.byte	32
	.byte	'Ifx_ASCLIN_LIN',0,16,140,5,3
	.word	61320
	.byte	14
	.word	20932
	.byte	32
	.byte	'Ifx_ASCLIN',0,16,181,5,3
	.word	61349
	.byte	10
	.byte	'_Ifx_CPU_A_Bits',0,25,45,16,4,11
	.byte	'ADDR',0,4
	.word	489
	.byte	32,0,2,35,0,0,32
	.byte	'Ifx_CPU_A_Bits',0,25,48,3
	.word	61374
	.byte	10
	.byte	'_Ifx_CPU_BIV_Bits',0,25,51,16,4,11
	.byte	'VSS',0,4
	.word	489
	.byte	1,31,2,35,0,11
	.byte	'BIV',0,4
	.word	489
	.byte	31,0,2,35,0,0,32
	.byte	'Ifx_CPU_BIV_Bits',0,25,55,3
	.word	61435
	.byte	10
	.byte	'_Ifx_CPU_BTV_Bits',0,25,58,16,4,11
	.byte	'reserved_0',0,4
	.word	489
	.byte	1,31,2,35,0,11
	.byte	'BTV',0,4
	.word	489
	.byte	31,0,2,35,0,0,32
	.byte	'Ifx_CPU_BTV_Bits',0,25,62,3
	.word	61514
	.byte	10
	.byte	'_Ifx_CPU_CCNT_Bits',0,25,65,16,4,11
	.byte	'CountValue',0,4
	.word	489
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	489
	.byte	1,0,2,35,0,0,32
	.byte	'Ifx_CPU_CCNT_Bits',0,25,69,3
	.word	61600
	.byte	10
	.byte	'_Ifx_CPU_CCTRL_Bits',0,25,72,16,4,11
	.byte	'CM',0,4
	.word	489
	.byte	1,31,2,35,0,11
	.byte	'CE',0,4
	.word	489
	.byte	1,30,2,35,0,11
	.byte	'M1',0,4
	.word	489
	.byte	3,27,2,35,0,11
	.byte	'M2',0,4
	.word	489
	.byte	3,24,2,35,0,11
	.byte	'M3',0,4
	.word	489
	.byte	3,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	489
	.byte	21,0,2,35,0,0,32
	.byte	'Ifx_CPU_CCTRL_Bits',0,25,80,3
	.word	61689
	.byte	10
	.byte	'_Ifx_CPU_COMPAT_Bits',0,25,83,16,4,11
	.byte	'reserved_0',0,4
	.word	489
	.byte	3,29,2,35,0,11
	.byte	'RM',0,4
	.word	489
	.byte	1,28,2,35,0,11
	.byte	'SP',0,4
	.word	489
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	489
	.byte	27,0,2,35,0,0,32
	.byte	'Ifx_CPU_COMPAT_Bits',0,25,89,3
	.word	61835
	.byte	10
	.byte	'_Ifx_CPU_CORE_ID_Bits',0,25,92,16,4,11
	.byte	'CORE_ID',0,4
	.word	489
	.byte	3,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	489
	.byte	29,0,2,35,0,0,32
	.byte	'Ifx_CPU_CORE_ID_Bits',0,25,96,3
	.word	61962
	.byte	10
	.byte	'_Ifx_CPU_CPR_L_Bits',0,25,99,16,4,11
	.byte	'reserved_0',0,4
	.word	489
	.byte	3,29,2,35,0,11
	.byte	'LOWBND',0,4
	.word	489
	.byte	29,0,2,35,0,0,32
	.byte	'Ifx_CPU_CPR_L_Bits',0,25,103,3
	.word	62060
	.byte	10
	.byte	'_Ifx_CPU_CPR_U_Bits',0,25,106,16,4,11
	.byte	'reserved_0',0,4
	.word	489
	.byte	3,29,2,35,0,11
	.byte	'UPPBND',0,4
	.word	489
	.byte	29,0,2,35,0,0,32
	.byte	'Ifx_CPU_CPR_U_Bits',0,25,110,3
	.word	62153
	.byte	10
	.byte	'_Ifx_CPU_CPU_ID_Bits',0,25,113,16,4,11
	.byte	'MODREV',0,4
	.word	489
	.byte	8,24,2,35,0,11
	.byte	'MOD_32B',0,4
	.word	489
	.byte	8,16,2,35,0,11
	.byte	'MOD',0,4
	.word	489
	.byte	16,0,2,35,0,0,32
	.byte	'Ifx_CPU_CPU_ID_Bits',0,25,118,3
	.word	62246
	.byte	10
	.byte	'_Ifx_CPU_CPXE_Bits',0,25,121,16,4,11
	.byte	'XE',0,4
	.word	489
	.byte	8,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	489
	.byte	24,0,2,35,0,0,32
	.byte	'Ifx_CPU_CPXE_Bits',0,25,125,3
	.word	62353
	.byte	10
	.byte	'_Ifx_CPU_CREVT_Bits',0,25,128,1,16,4,11
	.byte	'EVTA',0,4
	.word	489
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	489
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	489
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	489
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	489
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	489
	.byte	24,0,2,35,0,0,32
	.byte	'Ifx_CPU_CREVT_Bits',0,25,136,1,3
	.word	62440
	.byte	10
	.byte	'_Ifx_CPU_CUS_ID_Bits',0,25,139,1,16,4,11
	.byte	'CID',0,4
	.word	489
	.byte	3,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	489
	.byte	29,0,2,35,0,0,32
	.byte	'Ifx_CPU_CUS_ID_Bits',0,25,143,1,3
	.word	62594
	.byte	10
	.byte	'_Ifx_CPU_D_Bits',0,25,146,1,16,4,11
	.byte	'DATA',0,4
	.word	489
	.byte	32,0,2,35,0,0,32
	.byte	'Ifx_CPU_D_Bits',0,25,149,1,3
	.word	62688
	.byte	10
	.byte	'_Ifx_CPU_DATR_Bits',0,25,152,1,16,4,11
	.byte	'reserved_0',0,4
	.word	489
	.byte	3,29,2,35,0,11
	.byte	'SBE',0,4
	.word	489
	.byte	1,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	489
	.byte	5,23,2,35,0,11
	.byte	'CWE',0,4
	.word	489
	.byte	1,22,2,35,0,11
	.byte	'CFE',0,4
	.word	489
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	489
	.byte	3,18,2,35,0,11
	.byte	'SOE',0,4
	.word	489
	.byte	1,17,2,35,0,11
	.byte	'SME',0,4
	.word	489
	.byte	1,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	489
	.byte	16,0,2,35,0,0,32
	.byte	'Ifx_CPU_DATR_Bits',0,25,163,1,3
	.word	62751
	.byte	10
	.byte	'_Ifx_CPU_DBGSR_Bits',0,25,166,1,16,4,11
	.byte	'DE',0,4
	.word	489
	.byte	1,31,2,35,0,11
	.byte	'HALT',0,4
	.word	489
	.byte	2,29,2,35,0,11
	.byte	'SIH',0,4
	.word	489
	.byte	1,28,2,35,0,11
	.byte	'SUSP',0,4
	.word	489
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	489
	.byte	1,26,2,35,0,11
	.byte	'PREVSUSP',0,4
	.word	489
	.byte	1,25,2,35,0,11
	.byte	'PEVT',0,4
	.word	489
	.byte	1,24,2,35,0,11
	.byte	'EVTSRC',0,4
	.word	489
	.byte	5,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	489
	.byte	19,0,2,35,0,0,32
	.byte	'Ifx_CPU_DBGSR_Bits',0,25,177,1,3
	.word	62969
	.byte	10
	.byte	'_Ifx_CPU_DBGTCR_Bits',0,25,180,1,16,4,11
	.byte	'DTA',0,4
	.word	489
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	489
	.byte	31,0,2,35,0,0,32
	.byte	'Ifx_CPU_DBGTCR_Bits',0,25,184,1,3
	.word	63184
	.byte	10
	.byte	'_Ifx_CPU_DCON0_Bits',0,25,187,1,16,4,11
	.byte	'reserved_0',0,4
	.word	489
	.byte	1,31,2,35,0,11
	.byte	'DCBYP',0,4
	.word	489
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	489
	.byte	30,0,2,35,0,0,32
	.byte	'Ifx_CPU_DCON0_Bits',0,25,192,1,3
	.word	63278
	.byte	10
	.byte	'_Ifx_CPU_DCON2_Bits',0,25,195,1,16,4,11
	.byte	'DCACHE_SZE',0,4
	.word	489
	.byte	16,16,2,35,0,11
	.byte	'DSCRATCH_SZE',0,4
	.word	489
	.byte	16,0,2,35,0,0,32
	.byte	'Ifx_CPU_DCON2_Bits',0,25,199,1,3
	.word	63394
	.byte	10
	.byte	'_Ifx_CPU_DCX_Bits',0,25,202,1,16,4,11
	.byte	'reserved_0',0,4
	.word	489
	.byte	6,26,2,35,0,11
	.byte	'DCXValue',0,4
	.word	489
	.byte	26,0,2,35,0,0,32
	.byte	'Ifx_CPU_DCX_Bits',0,25,206,1,3
	.word	63495
	.byte	10
	.byte	'_Ifx_CPU_DEADD_Bits',0,25,209,1,16,4,11
	.byte	'ERROR_ADDRESS',0,4
	.word	489
	.byte	32,0,2,35,0,0,32
	.byte	'Ifx_CPU_DEADD_Bits',0,25,212,1,3
	.word	63588
	.byte	10
	.byte	'_Ifx_CPU_DIEAR_Bits',0,25,215,1,16,4,11
	.byte	'TA',0,4
	.word	489
	.byte	32,0,2,35,0,0,32
	.byte	'Ifx_CPU_DIEAR_Bits',0,25,218,1,3
	.word	63668
	.byte	10
	.byte	'_Ifx_CPU_DIETR_Bits',0,25,221,1,16,4,11
	.byte	'IED',0,4
	.word	489
	.byte	1,31,2,35,0,11
	.byte	'IE_T',0,4
	.word	489
	.byte	1,30,2,35,0,11
	.byte	'IE_C',0,4
	.word	489
	.byte	1,29,2,35,0,11
	.byte	'IE_S',0,4
	.word	489
	.byte	1,28,2,35,0,11
	.byte	'IE_BI',0,4
	.word	489
	.byte	1,27,2,35,0,11
	.byte	'E_INFO',0,4
	.word	489
	.byte	6,21,2,35,0,11
	.byte	'IE_DUAL',0,4
	.word	489
	.byte	1,20,2,35,0,11
	.byte	'IE_SP',0,4
	.word	489
	.byte	1,19,2,35,0,11
	.byte	'IE_BS',0,4
	.word	489
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	489
	.byte	18,0,2,35,0,0,32
	.byte	'Ifx_CPU_DIETR_Bits',0,25,233,1,3
	.word	63737
	.byte	10
	.byte	'_Ifx_CPU_DMS_Bits',0,25,236,1,16,4,11
	.byte	'reserved_0',0,4
	.word	489
	.byte	1,31,2,35,0,11
	.byte	'DMSValue',0,4
	.word	489
	.byte	31,0,2,35,0,0,32
	.byte	'Ifx_CPU_DMS_Bits',0,25,240,1,3
	.word	63966
	.byte	10
	.byte	'_Ifx_CPU_DPR_L_Bits',0,25,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	489
	.byte	3,29,2,35,0,11
	.byte	'LOWBND',0,4
	.word	489
	.byte	29,0,2,35,0,0,32
	.byte	'Ifx_CPU_DPR_L_Bits',0,25,247,1,3
	.word	64059
	.byte	10
	.byte	'_Ifx_CPU_DPR_U_Bits',0,25,250,1,16,4,11
	.byte	'reserved_0',0,4
	.word	489
	.byte	3,29,2,35,0,11
	.byte	'UPPBND',0,4
	.word	489
	.byte	29,0,2,35,0,0,32
	.byte	'Ifx_CPU_DPR_U_Bits',0,25,254,1,3
	.word	64154
	.byte	10
	.byte	'_Ifx_CPU_DPRE_Bits',0,25,129,2,16,4,11
	.byte	'RE',0,4
	.word	489
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	489
	.byte	16,0,2,35,0,0,32
	.byte	'Ifx_CPU_DPRE_Bits',0,25,133,2,3
	.word	64249
	.byte	10
	.byte	'_Ifx_CPU_DPWE_Bits',0,25,136,2,16,4,11
	.byte	'WE',0,4
	.word	489
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	489
	.byte	16,0,2,35,0,0,32
	.byte	'Ifx_CPU_DPWE_Bits',0,25,140,2,3
	.word	64339
	.byte	10
	.byte	'_Ifx_CPU_DSTR_Bits',0,25,143,2,16,4,11
	.byte	'SRE',0,4
	.word	489
	.byte	1,31,2,35,0,11
	.byte	'GAE',0,4
	.word	489
	.byte	1,30,2,35,0,11
	.byte	'LBE',0,4
	.word	489
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	489
	.byte	3,26,2,35,0,11
	.byte	'CRE',0,4
	.word	489
	.byte	1,25,2,35,0,11
	.byte	'reserved_7',0,4
	.word	489
	.byte	7,18,2,35,0,11
	.byte	'DTME',0,4
	.word	489
	.byte	1,17,2,35,0,11
	.byte	'LOE',0,4
	.word	489
	.byte	1,16,2,35,0,11
	.byte	'SDE',0,4
	.word	489
	.byte	1,15,2,35,0,11
	.byte	'SCE',0,4
	.word	489
	.byte	1,14,2,35,0,11
	.byte	'CAC',0,4
	.word	489
	.byte	1,13,2,35,0,11
	.byte	'MPE',0,4
	.word	489
	.byte	1,12,2,35,0,11
	.byte	'CLE',0,4
	.word	489
	.byte	1,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	489
	.byte	3,8,2,35,0,11
	.byte	'ALN',0,4
	.word	489
	.byte	1,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	489
	.byte	7,0,2,35,0,0,32
	.byte	'Ifx_CPU_DSTR_Bits',0,25,161,2,3
	.word	64429
	.byte	10
	.byte	'_Ifx_CPU_EXEVT_Bits',0,25,164,2,16,4,11
	.byte	'EVTA',0,4
	.word	489
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	489
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	489
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	489
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	489
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	489
	.byte	24,0,2,35,0,0,32
	.byte	'Ifx_CPU_EXEVT_Bits',0,25,172,2,3
	.word	64753
	.byte	10
	.byte	'_Ifx_CPU_FCX_Bits',0,25,175,2,16,4,11
	.byte	'FCXO',0,4
	.word	489
	.byte	16,16,2,35,0,11
	.byte	'FCXS',0,4
	.word	489
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	489
	.byte	12,0,2,35,0,0,32
	.byte	'Ifx_CPU_FCX_Bits',0,25,180,2,3
	.word	64907
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_CON_Bits',0,25,183,2,16,4,11
	.byte	'TST',0,4
	.word	489
	.byte	1,31,2,35,0,11
	.byte	'TCL',0,4
	.word	489
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	489
	.byte	6,24,2,35,0,11
	.byte	'RM',0,4
	.word	489
	.byte	2,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	489
	.byte	8,14,2,35,0,11
	.byte	'FXE',0,4
	.word	489
	.byte	1,13,2,35,0,11
	.byte	'FUE',0,4
	.word	489
	.byte	1,12,2,35,0,11
	.byte	'FZE',0,4
	.word	489
	.byte	1,11,2,35,0,11
	.byte	'FVE',0,4
	.word	489
	.byte	1,10,2,35,0,11
	.byte	'FIE',0,4
	.word	489
	.byte	1,9,2,35,0,11
	.byte	'reserved_23',0,4
	.word	489
	.byte	3,6,2,35,0,11
	.byte	'FX',0,4
	.word	489
	.byte	1,5,2,35,0,11
	.byte	'FU',0,4
	.word	489
	.byte	1,4,2,35,0,11
	.byte	'FZ',0,4
	.word	489
	.byte	1,3,2,35,0,11
	.byte	'FV',0,4
	.word	489
	.byte	1,2,2,35,0,11
	.byte	'FI',0,4
	.word	489
	.byte	1,1,2,35,0,11
	.byte	'reserved_31',0,4
	.word	489
	.byte	1,0,2,35,0,0,32
	.byte	'Ifx_CPU_FPU_TRAP_CON_Bits',0,25,202,2,3
	.word	65013
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_OPC_Bits',0,25,205,2,16,4,11
	.byte	'OPC',0,4
	.word	489
	.byte	8,24,2,35,0,11
	.byte	'FMT',0,4
	.word	489
	.byte	1,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	489
	.byte	7,16,2,35,0,11
	.byte	'DREG',0,4
	.word	489
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	489
	.byte	12,0,2,35,0,0,32
	.byte	'Ifx_CPU_FPU_TRAP_OPC_Bits',0,25,212,2,3
	.word	65362
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_PC_Bits',0,25,215,2,16,4,11
	.byte	'PC',0,4
	.word	489
	.byte	32,0,2,35,0,0,32
	.byte	'Ifx_CPU_FPU_TRAP_PC_Bits',0,25,218,2,3
	.word	65522
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC1_Bits',0,25,221,2,16,4,11
	.byte	'SRC1',0,4
	.word	489
	.byte	32,0,2,35,0,0,32
	.byte	'Ifx_CPU_FPU_TRAP_SRC1_Bits',0,25,224,2,3
	.word	65603
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC2_Bits',0,25,227,2,16,4,11
	.byte	'SRC2',0,4
	.word	489
	.byte	32,0,2,35,0,0,32
	.byte	'Ifx_CPU_FPU_TRAP_SRC2_Bits',0,25,230,2,3
	.word	65690
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC3_Bits',0,25,233,2,16,4,11
	.byte	'SRC3',0,4
	.word	489
	.byte	32,0,2,35,0,0,32
	.byte	'Ifx_CPU_FPU_TRAP_SRC3_Bits',0,25,236,2,3
	.word	65777
	.byte	10
	.byte	'_Ifx_CPU_ICNT_Bits',0,25,239,2,16,4,11
	.byte	'CountValue',0,4
	.word	489
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	489
	.byte	1,0,2,35,0,0,32
	.byte	'Ifx_CPU_ICNT_Bits',0,25,243,2,3
	.word	65864
	.byte	32
	.byte	'Ifx_CPU_ICR_Bits',0,25,253,2,3
	.word	31163
	.byte	10
	.byte	'_Ifx_CPU_ISP_Bits',0,25,128,3,16,4,11
	.byte	'ISP',0,4
	.word	489
	.byte	32,0,2,35,0,0,32
	.byte	'Ifx_CPU_ISP_Bits',0,25,131,3,3
	.word	65981
	.byte	10
	.byte	'_Ifx_CPU_LCX_Bits',0,25,134,3,16,4,11
	.byte	'LCXO',0,4
	.word	489
	.byte	16,16,2,35,0,11
	.byte	'LCXS',0,4
	.word	489
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	489
	.byte	12,0,2,35,0,0,32
	.byte	'Ifx_CPU_LCX_Bits',0,25,139,3,3
	.word	66047
	.byte	10
	.byte	'_Ifx_CPU_M1CNT_Bits',0,25,142,3,16,4,11
	.byte	'CountValue',0,4
	.word	489
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	489
	.byte	1,0,2,35,0,0,32
	.byte	'Ifx_CPU_M1CNT_Bits',0,25,146,3,3
	.word	66153
	.byte	10
	.byte	'_Ifx_CPU_M2CNT_Bits',0,25,149,3,16,4,11
	.byte	'CountValue',0,4
	.word	489
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	489
	.byte	1,0,2,35,0,0,32
	.byte	'Ifx_CPU_M2CNT_Bits',0,25,153,3,3
	.word	66246
	.byte	10
	.byte	'_Ifx_CPU_M3CNT_Bits',0,25,156,3,16,4,11
	.byte	'CountValue',0,4
	.word	489
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	489
	.byte	1,0,2,35,0,0,32
	.byte	'Ifx_CPU_M3CNT_Bits',0,25,160,3,3
	.word	66339
	.byte	10
	.byte	'_Ifx_CPU_PC_Bits',0,25,163,3,16,4,11
	.byte	'reserved_0',0,4
	.word	489
	.byte	1,31,2,35,0,11
	.byte	'PC',0,4
	.word	489
	.byte	31,0,2,35,0,0,32
	.byte	'Ifx_CPU_PC_Bits',0,25,167,3,3
	.word	66432
	.byte	10
	.byte	'_Ifx_CPU_PCON0_Bits',0,25,170,3,16,4,11
	.byte	'reserved_0',0,4
	.word	489
	.byte	1,31,2,35,0,11
	.byte	'PCBYP',0,4
	.word	489
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	489
	.byte	30,0,2,35,0,0,32
	.byte	'Ifx_CPU_PCON0_Bits',0,25,175,3,3
	.word	66517
	.byte	10
	.byte	'_Ifx_CPU_PCON1_Bits',0,25,178,3,16,4,11
	.byte	'PCINV',0,4
	.word	489
	.byte	1,31,2,35,0,11
	.byte	'PBINV',0,4
	.word	489
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	489
	.byte	30,0,2,35,0,0,32
	.byte	'Ifx_CPU_PCON1_Bits',0,25,183,3,3
	.word	66633
	.byte	10
	.byte	'_Ifx_CPU_PCON2_Bits',0,25,186,3,16,4,11
	.byte	'PCACHE_SZE',0,4
	.word	489
	.byte	16,16,2,35,0,11
	.byte	'PSCRATCH_SZE',0,4
	.word	489
	.byte	16,0,2,35,0,0,32
	.byte	'Ifx_CPU_PCON2_Bits',0,25,190,3,3
	.word	66744
	.byte	10
	.byte	'_Ifx_CPU_PCXI_Bits',0,25,193,3,16,4,11
	.byte	'PCXO',0,4
	.word	489
	.byte	16,16,2,35,0,11
	.byte	'PCXS',0,4
	.word	489
	.byte	4,12,2,35,0,11
	.byte	'UL',0,4
	.word	489
	.byte	1,11,2,35,0,11
	.byte	'PIE',0,4
	.word	489
	.byte	1,10,2,35,0,11
	.byte	'PCPN',0,4
	.word	489
	.byte	10,0,2,35,0,0,32
	.byte	'Ifx_CPU_PCXI_Bits',0,25,200,3,3
	.word	66845
	.byte	10
	.byte	'_Ifx_CPU_PIEAR_Bits',0,25,203,3,16,4,11
	.byte	'TA',0,4
	.word	489
	.byte	32,0,2,35,0,0,32
	.byte	'Ifx_CPU_PIEAR_Bits',0,25,206,3,3
	.word	66975
	.byte	10
	.byte	'_Ifx_CPU_PIETR_Bits',0,25,209,3,16,4,11
	.byte	'IED',0,4
	.word	489
	.byte	1,31,2,35,0,11
	.byte	'IE_T',0,4
	.word	489
	.byte	1,30,2,35,0,11
	.byte	'IE_C',0,4
	.word	489
	.byte	1,29,2,35,0,11
	.byte	'IE_S',0,4
	.word	489
	.byte	1,28,2,35,0,11
	.byte	'IE_BI',0,4
	.word	489
	.byte	1,27,2,35,0,11
	.byte	'E_INFO',0,4
	.word	489
	.byte	6,21,2,35,0,11
	.byte	'IE_DUAL',0,4
	.word	489
	.byte	1,20,2,35,0,11
	.byte	'IE_SP',0,4
	.word	489
	.byte	1,19,2,35,0,11
	.byte	'IE_BS',0,4
	.word	489
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	489
	.byte	18,0,2,35,0,0,32
	.byte	'Ifx_CPU_PIETR_Bits',0,25,221,3,3
	.word	67044
	.byte	10
	.byte	'_Ifx_CPU_PMA0_Bits',0,25,224,3,16,4,11
	.byte	'reserved_0',0,4
	.word	489
	.byte	13,19,2,35,0,11
	.byte	'DAC',0,4
	.word	489
	.byte	3,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	489
	.byte	16,0,2,35,0,0,32
	.byte	'Ifx_CPU_PMA0_Bits',0,25,229,3,3
	.word	67273
	.byte	10
	.byte	'_Ifx_CPU_PMA1_Bits',0,25,232,3,16,4,11
	.byte	'reserved_0',0,4
	.word	489
	.byte	14,18,2,35,0,11
	.byte	'CAC',0,4
	.word	489
	.byte	2,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	489
	.byte	16,0,2,35,0,0,32
	.byte	'Ifx_CPU_PMA1_Bits',0,25,237,3,3
	.word	67386
	.byte	10
	.byte	'_Ifx_CPU_PMA2_Bits',0,25,240,3,16,4,11
	.byte	'PSI',0,4
	.word	489
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	489
	.byte	16,0,2,35,0,0,32
	.byte	'Ifx_CPU_PMA2_Bits',0,25,244,3,3
	.word	67499
	.byte	10
	.byte	'_Ifx_CPU_PSTR_Bits',0,25,247,3,16,4,11
	.byte	'FRE',0,4
	.word	489
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	489
	.byte	1,30,2,35,0,11
	.byte	'FBE',0,4
	.word	489
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	489
	.byte	9,20,2,35,0,11
	.byte	'FPE',0,4
	.word	489
	.byte	1,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	489
	.byte	1,18,2,35,0,11
	.byte	'FME',0,4
	.word	489
	.byte	1,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	489
	.byte	17,0,2,35,0,0,32
	.byte	'Ifx_CPU_PSTR_Bits',0,25,129,4,3
	.word	67590
	.byte	10
	.byte	'_Ifx_CPU_PSW_Bits',0,25,132,4,16,4,11
	.byte	'CDC',0,4
	.word	489
	.byte	7,25,2,35,0,11
	.byte	'CDE',0,4
	.word	489
	.byte	1,24,2,35,0,11
	.byte	'GW',0,4
	.word	489
	.byte	1,23,2,35,0,11
	.byte	'IS',0,4
	.word	489
	.byte	1,22,2,35,0,11
	.byte	'IO',0,4
	.word	489
	.byte	2,20,2,35,0,11
	.byte	'PRS',0,4
	.word	489
	.byte	2,18,2,35,0,11
	.byte	'S',0,4
	.word	489
	.byte	1,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	489
	.byte	12,5,2,35,0,11
	.byte	'SAV',0,4
	.word	489
	.byte	1,4,2,35,0,11
	.byte	'AV',0,4
	.word	489
	.byte	1,3,2,35,0,11
	.byte	'SV',0,4
	.word	489
	.byte	1,2,2,35,0,11
	.byte	'V',0,4
	.word	489
	.byte	1,1,2,35,0,11
	.byte	'C',0,4
	.word	489
	.byte	1,0,2,35,0,0,32
	.byte	'Ifx_CPU_PSW_Bits',0,25,147,4,3
	.word	67793
	.byte	10
	.byte	'_Ifx_CPU_SEGEN_Bits',0,25,150,4,16,4,11
	.byte	'ADFLIP',0,4
	.word	489
	.byte	8,24,2,35,0,11
	.byte	'ADTYPE',0,4
	.word	489
	.byte	2,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	489
	.byte	21,1,2,35,0,11
	.byte	'AE',0,4
	.word	489
	.byte	1,0,2,35,0,0,32
	.byte	'Ifx_CPU_SEGEN_Bits',0,25,156,4,3
	.word	68036
	.byte	10
	.byte	'_Ifx_CPU_SMACON_Bits',0,25,159,4,16,4,11
	.byte	'PC',0,4
	.word	489
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	489
	.byte	1,30,2,35,0,11
	.byte	'PT',0,4
	.word	489
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	489
	.byte	5,24,2,35,0,11
	.byte	'DC',0,4
	.word	489
	.byte	1,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	489
	.byte	1,22,2,35,0,11
	.byte	'DT',0,4
	.word	489
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	489
	.byte	13,8,2,35,0,11
	.byte	'IODT',0,4
	.word	489
	.byte	1,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	489
	.byte	7,0,2,35,0,0,32
	.byte	'Ifx_CPU_SMACON_Bits',0,25,171,4,3
	.word	68164
	.byte	10
	.byte	'_Ifx_CPU_SPROT_ACCENA_Bits',0,25,174,4,16,4,11
	.byte	'EN',0,4
	.word	466
	.byte	32,0,2,35,0,0,32
	.byte	'Ifx_CPU_SPROT_ACCENA_Bits',0,25,177,4,3
	.word	68405
	.byte	10
	.byte	'_Ifx_CPU_SPROT_ACCENB_Bits',0,25,180,4,16,4,11
	.byte	'reserved_0',0,4
	.word	466
	.byte	32,0,2,35,0,0,32
	.byte	'Ifx_CPU_SPROT_ACCENB_Bits',0,25,183,4,3
	.word	68488
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,25,186,4,16,4,11
	.byte	'EN',0,4
	.word	466
	.byte	32,0,2,35,0,0,32
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,25,189,4,3
	.word	68579
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,25,192,4,16,4,11
	.byte	'reserved_0',0,4
	.word	466
	.byte	32,0,2,35,0,0,32
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,25,195,4,3
	.word	68670
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_LA_Bits',0,25,198,4,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	466
	.byte	27,0,2,35,0,0,32
	.byte	'Ifx_CPU_SPROT_RGN_LA_Bits',0,25,202,4,3
	.word	68769
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_UA_Bits',0,25,205,4,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	466
	.byte	27,0,2,35,0,0,32
	.byte	'Ifx_CPU_SPROT_RGN_UA_Bits',0,25,209,4,3
	.word	68876
	.byte	10
	.byte	'_Ifx_CPU_SWEVT_Bits',0,25,212,4,16,4,11
	.byte	'EVTA',0,4
	.word	489
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	489
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	489
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	489
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	489
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	489
	.byte	24,0,2,35,0,0,32
	.byte	'Ifx_CPU_SWEVT_Bits',0,25,220,4,3
	.word	68983
	.byte	10
	.byte	'_Ifx_CPU_SYSCON_Bits',0,25,223,4,16,4,11
	.byte	'FCDSF',0,4
	.word	489
	.byte	1,31,2,35,0,11
	.byte	'PROTEN',0,4
	.word	489
	.byte	1,30,2,35,0,11
	.byte	'TPROTEN',0,4
	.word	489
	.byte	1,29,2,35,0,11
	.byte	'IS',0,4
	.word	489
	.byte	1,28,2,35,0,11
	.byte	'IT',0,4
	.word	489
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	489
	.byte	27,0,2,35,0,0,32
	.byte	'Ifx_CPU_SYSCON_Bits',0,25,231,4,3
	.word	69137
	.byte	10
	.byte	'_Ifx_CPU_TASK_ASI_Bits',0,25,234,4,16,4,11
	.byte	'ASI',0,4
	.word	489
	.byte	5,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	489
	.byte	27,0,2,35,0,0,32
	.byte	'Ifx_CPU_TASK_ASI_Bits',0,25,238,4,3
	.word	69298
	.byte	10
	.byte	'_Ifx_CPU_TPS_CON_Bits',0,25,241,4,16,4,11
	.byte	'TEXP0',0,4
	.word	489
	.byte	1,31,2,35,0,11
	.byte	'TEXP1',0,4
	.word	489
	.byte	1,30,2,35,0,11
	.byte	'TEXP2',0,4
	.word	489
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	489
	.byte	13,16,2,35,0,11
	.byte	'TTRAP',0,4
	.word	489
	.byte	1,15,2,35,0,11
	.byte	'reserved_17',0,4
	.word	489
	.byte	15,0,2,35,0,0,32
	.byte	'Ifx_CPU_TPS_CON_Bits',0,25,249,4,3
	.word	69396
	.byte	10
	.byte	'_Ifx_CPU_TPS_TIMER_Bits',0,25,252,4,16,4,11
	.byte	'Timer',0,4
	.word	489
	.byte	32,0,2,35,0,0,32
	.byte	'Ifx_CPU_TPS_TIMER_Bits',0,25,255,4,3
	.word	69568
	.byte	10
	.byte	'_Ifx_CPU_TR_ADR_Bits',0,25,130,5,16,4,11
	.byte	'ADDR',0,4
	.word	489
	.byte	32,0,2,35,0,0,32
	.byte	'Ifx_CPU_TR_ADR_Bits',0,25,133,5,3
	.word	69648
	.byte	10
	.byte	'_Ifx_CPU_TR_EVT_Bits',0,25,136,5,16,4,11
	.byte	'EVTA',0,4
	.word	489
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	489
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	489
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	489
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	489
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	489
	.byte	4,20,2,35,0,11
	.byte	'TYP',0,4
	.word	489
	.byte	1,19,2,35,0,11
	.byte	'RNG',0,4
	.word	489
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	489
	.byte	1,17,2,35,0,11
	.byte	'ASI_EN',0,4
	.word	489
	.byte	1,16,2,35,0,11
	.byte	'ASI',0,4
	.word	489
	.byte	5,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	489
	.byte	6,5,2,35,0,11
	.byte	'AST',0,4
	.word	489
	.byte	1,4,2,35,0,11
	.byte	'ALD',0,4
	.word	489
	.byte	1,3,2,35,0,11
	.byte	'reserved_29',0,4
	.word	489
	.byte	3,0,2,35,0,0,32
	.byte	'Ifx_CPU_TR_EVT_Bits',0,25,153,5,3
	.word	69721
	.byte	10
	.byte	'_Ifx_CPU_TRIG_ACC_Bits',0,25,156,5,16,4,11
	.byte	'T0',0,4
	.word	489
	.byte	1,31,2,35,0,11
	.byte	'T1',0,4
	.word	489
	.byte	1,30,2,35,0,11
	.byte	'T2',0,4
	.word	489
	.byte	1,29,2,35,0,11
	.byte	'T3',0,4
	.word	489
	.byte	1,28,2,35,0,11
	.byte	'T4',0,4
	.word	489
	.byte	1,27,2,35,0,11
	.byte	'T5',0,4
	.word	489
	.byte	1,26,2,35,0,11
	.byte	'T6',0,4
	.word	489
	.byte	1,25,2,35,0,11
	.byte	'T7',0,4
	.word	489
	.byte	1,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	489
	.byte	24,0,2,35,0,0,32
	.byte	'Ifx_CPU_TRIG_ACC_Bits',0,25,167,5,3
	.word	70039
	.byte	12,25,175,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61374
	.byte	4,2,35,0,0,32
	.byte	'Ifx_CPU_A',0,25,180,5,3
	.word	70234
	.byte	12,25,183,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61435
	.byte	4,2,35,0,0,32
	.byte	'Ifx_CPU_BIV',0,25,188,5,3
	.word	70293
	.byte	12,25,191,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61514
	.byte	4,2,35,0,0,32
	.byte	'Ifx_CPU_BTV',0,25,196,5,3
	.word	70354
	.byte	12,25,199,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61600
	.byte	4,2,35,0,0,32
	.byte	'Ifx_CPU_CCNT',0,25,204,5,3
	.word	70415
	.byte	12,25,207,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61689
	.byte	4,2,35,0,0,32
	.byte	'Ifx_CPU_CCTRL',0,25,212,5,3
	.word	70477
	.byte	12,25,215,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61835
	.byte	4,2,35,0,0,32
	.byte	'Ifx_CPU_COMPAT',0,25,220,5,3
	.word	70540
	.byte	12,25,223,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61962
	.byte	4,2,35,0,0,32
	.byte	'Ifx_CPU_CORE_ID',0,25,228,5,3
	.word	70604
	.byte	12,25,231,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62060
	.byte	4,2,35,0,0,32
	.byte	'Ifx_CPU_CPR_L',0,25,236,5,3
	.word	70669
	.byte	12,25,239,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62153
	.byte	4,2,35,0,0,32
	.byte	'Ifx_CPU_CPR_U',0,25,244,5,3
	.word	70732
	.byte	12,25,247,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62246
	.byte	4,2,35,0,0,32
	.byte	'Ifx_CPU_CPU_ID',0,25,252,5,3
	.word	70795
	.byte	12,25,255,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62353
	.byte	4,2,35,0,0,32
	.byte	'Ifx_CPU_CPXE',0,25,132,6,3
	.word	70859
	.byte	12,25,135,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62440
	.byte	4,2,35,0,0,32
	.byte	'Ifx_CPU_CREVT',0,25,140,6,3
	.word	70921
	.byte	12,25,143,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62594
	.byte	4,2,35,0,0,32
	.byte	'Ifx_CPU_CUS_ID',0,25,148,6,3
	.word	70984
	.byte	12,25,151,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62688
	.byte	4,2,35,0,0,32
	.byte	'Ifx_CPU_D',0,25,156,6,3
	.word	71048
	.byte	12,25,159,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62751
	.byte	4,2,35,0,0,32
	.byte	'Ifx_CPU_DATR',0,25,164,6,3
	.word	71107
	.byte	12,25,167,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62969
	.byte	4,2,35,0,0,32
	.byte	'Ifx_CPU_DBGSR',0,25,172,6,3
	.word	71169
	.byte	12,25,175,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63184
	.byte	4,2,35,0,0,32
	.byte	'Ifx_CPU_DBGTCR',0,25,180,6,3
	.word	71232
	.byte	12,25,183,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63278
	.byte	4,2,35,0,0,32
	.byte	'Ifx_CPU_DCON0',0,25,188,6,3
	.word	71296
	.byte	12,25,191,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63394
	.byte	4,2,35,0,0,32
	.byte	'Ifx_CPU_DCON2',0,25,196,6,3
	.word	71359
	.byte	12,25,199,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63495
	.byte	4,2,35,0,0,32
	.byte	'Ifx_CPU_DCX',0,25,204,6,3
	.word	71422
	.byte	12,25,207,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63588
	.byte	4,2,35,0,0,32
	.byte	'Ifx_CPU_DEADD',0,25,212,6,3
	.word	71483
	.byte	12,25,215,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63668
	.byte	4,2,35,0,0,32
	.byte	'Ifx_CPU_DIEAR',0,25,220,6,3
	.word	71546
	.byte	12,25,223,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63737
	.byte	4,2,35,0,0,32
	.byte	'Ifx_CPU_DIETR',0,25,228,6,3
	.word	71609
	.byte	12,25,231,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63966
	.byte	4,2,35,0,0,32
	.byte	'Ifx_CPU_DMS',0,25,236,6,3
	.word	71672
	.byte	12,25,239,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64059
	.byte	4,2,35,0,0,32
	.byte	'Ifx_CPU_DPR_L',0,25,244,6,3
	.word	71733
	.byte	12,25,247,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64154
	.byte	4,2,35,0,0,32
	.byte	'Ifx_CPU_DPR_U',0,25,252,6,3
	.word	71796
	.byte	12,25,255,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64249
	.byte	4,2,35,0,0,32
	.byte	'Ifx_CPU_DPRE',0,25,132,7,3
	.word	71859
	.byte	12,25,135,7,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64339
	.byte	4,2,35,0,0,32
	.byte	'Ifx_CPU_DPWE',0,25,140,7,3
	.word	71921
	.byte	12,25,143,7,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64429
	.byte	4,2,35,0,0,32
	.byte	'Ifx_CPU_DSTR',0,25,148,7,3
	.word	71983
	.byte	12,25,151,7,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64753
	.byte	4,2,35,0,0,32
	.byte	'Ifx_CPU_EXEVT',0,25,156,7,3
	.word	72045
	.byte	12,25,159,7,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64907
	.byte	4,2,35,0,0,32
	.byte	'Ifx_CPU_FCX',0,25,164,7,3
	.word	72108
	.byte	12,25,167,7,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65013
	.byte	4,2,35,0,0,32
	.byte	'Ifx_CPU_FPU_TRAP_CON',0,25,172,7,3
	.word	72169
	.byte	12,25,175,7,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65362
	.byte	4,2,35,0,0,32
	.byte	'Ifx_CPU_FPU_TRAP_OPC',0,25,180,7,3
	.word	72239
	.byte	12,25,183,7,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65522
	.byte	4,2,35,0,0,32
	.byte	'Ifx_CPU_FPU_TRAP_PC',0,25,188,7,3
	.word	72309
	.byte	12,25,191,7,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65603
	.byte	4,2,35,0,0,32
	.byte	'Ifx_CPU_FPU_TRAP_SRC1',0,25,196,7,3
	.word	72378
	.byte	12,25,199,7,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65690
	.byte	4,2,35,0,0,32
	.byte	'Ifx_CPU_FPU_TRAP_SRC2',0,25,204,7,3
	.word	72449
	.byte	12,25,207,7,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65777
	.byte	4,2,35,0,0,32
	.byte	'Ifx_CPU_FPU_TRAP_SRC3',0,25,212,7,3
	.word	72520
	.byte	12,25,215,7,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65864
	.byte	4,2,35,0,0,32
	.byte	'Ifx_CPU_ICNT',0,25,220,7,3
	.word	72591
	.byte	32
	.byte	'Ifx_CPU_ICR',0,25,228,7,3
	.word	31280
	.byte	12,25,231,7,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65981
	.byte	4,2,35,0,0,32
	.byte	'Ifx_CPU_ISP',0,25,236,7,3
	.word	72674
	.byte	12,25,239,7,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66047
	.byte	4,2,35,0,0,32
	.byte	'Ifx_CPU_LCX',0,25,244,7,3
	.word	72735
	.byte	12,25,247,7,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66153
	.byte	4,2,35,0,0,32
	.byte	'Ifx_CPU_M1CNT',0,25,252,7,3
	.word	72796
	.byte	12,25,255,7,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66246
	.byte	4,2,35,0,0,32
	.byte	'Ifx_CPU_M2CNT',0,25,132,8,3
	.word	72859
	.byte	12,25,135,8,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66339
	.byte	4,2,35,0,0,32
	.byte	'Ifx_CPU_M3CNT',0,25,140,8,3
	.word	72922
	.byte	12,25,143,8,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66432
	.byte	4,2,35,0,0,32
	.byte	'Ifx_CPU_PC',0,25,148,8,3
	.word	72985
	.byte	12,25,151,8,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66517
	.byte	4,2,35,0,0,32
	.byte	'Ifx_CPU_PCON0',0,25,156,8,3
	.word	73045
	.byte	12,25,159,8,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66633
	.byte	4,2,35,0,0,32
	.byte	'Ifx_CPU_PCON1',0,25,164,8,3
	.word	73108
	.byte	12,25,167,8,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66744
	.byte	4,2,35,0,0,32
	.byte	'Ifx_CPU_PCON2',0,25,172,8,3
	.word	73171
	.byte	12,25,175,8,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66845
	.byte	4,2,35,0,0,32
	.byte	'Ifx_CPU_PCXI',0,25,180,8,3
	.word	73234
	.byte	12,25,183,8,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66975
	.byte	4,2,35,0,0,32
	.byte	'Ifx_CPU_PIEAR',0,25,188,8,3
	.word	73296
	.byte	12,25,191,8,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67044
	.byte	4,2,35,0,0,32
	.byte	'Ifx_CPU_PIETR',0,25,196,8,3
	.word	73359
	.byte	12,25,199,8,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67273
	.byte	4,2,35,0,0,32
	.byte	'Ifx_CPU_PMA0',0,25,204,8,3
	.word	73422
	.byte	12,25,207,8,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67386
	.byte	4,2,35,0,0,32
	.byte	'Ifx_CPU_PMA1',0,25,212,8,3
	.word	73484
	.byte	12,25,215,8,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67499
	.byte	4,2,35,0,0,32
	.byte	'Ifx_CPU_PMA2',0,25,220,8,3
	.word	73546
	.byte	12,25,223,8,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67590
	.byte	4,2,35,0,0,32
	.byte	'Ifx_CPU_PSTR',0,25,228,8,3
	.word	73608
	.byte	12,25,231,8,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67793
	.byte	4,2,35,0,0,32
	.byte	'Ifx_CPU_PSW',0,25,236,8,3
	.word	73670
	.byte	12,25,239,8,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68036
	.byte	4,2,35,0,0,32
	.byte	'Ifx_CPU_SEGEN',0,25,244,8,3
	.word	73731
	.byte	12,25,247,8,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68164
	.byte	4,2,35,0,0,32
	.byte	'Ifx_CPU_SMACON',0,25,252,8,3
	.word	73794
	.byte	12,25,255,8,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68405
	.byte	4,2,35,0,0,32
	.byte	'Ifx_CPU_SPROT_ACCENA',0,25,132,9,3
	.word	73858
	.byte	12,25,135,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68488
	.byte	4,2,35,0,0,32
	.byte	'Ifx_CPU_SPROT_ACCENB',0,25,140,9,3
	.word	73928
	.byte	12,25,143,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68579
	.byte	4,2,35,0,0,32
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA',0,25,148,9,3
	.word	73998
	.byte	12,25,151,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68670
	.byte	4,2,35,0,0,32
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB',0,25,156,9,3
	.word	74072
	.byte	12,25,159,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68769
	.byte	4,2,35,0,0,32
	.byte	'Ifx_CPU_SPROT_RGN_LA',0,25,164,9,3
	.word	74146
	.byte	12,25,167,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68876
	.byte	4,2,35,0,0,32
	.byte	'Ifx_CPU_SPROT_RGN_UA',0,25,172,9,3
	.word	74216
	.byte	12,25,175,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68983
	.byte	4,2,35,0,0,32
	.byte	'Ifx_CPU_SWEVT',0,25,180,9,3
	.word	74286
	.byte	12,25,183,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69137
	.byte	4,2,35,0,0,32
	.byte	'Ifx_CPU_SYSCON',0,25,188,9,3
	.word	74349
	.byte	12,25,191,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69298
	.byte	4,2,35,0,0,32
	.byte	'Ifx_CPU_TASK_ASI',0,25,196,9,3
	.word	74413
	.byte	12,25,199,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69396
	.byte	4,2,35,0,0,32
	.byte	'Ifx_CPU_TPS_CON',0,25,204,9,3
	.word	74479
	.byte	12,25,207,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69568
	.byte	4,2,35,0,0,32
	.byte	'Ifx_CPU_TPS_TIMER',0,25,212,9,3
	.word	74544
	.byte	12,25,215,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69648
	.byte	4,2,35,0,0,32
	.byte	'Ifx_CPU_TR_ADR',0,25,220,9,3
	.word	74611
	.byte	12,25,223,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69721
	.byte	4,2,35,0,0,32
	.byte	'Ifx_CPU_TR_EVT',0,25,228,9,3
	.word	74675
	.byte	12,25,231,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70039
	.byte	4,2,35,0,0,32
	.byte	'Ifx_CPU_TRIG_ACC',0,25,236,9,3
	.word	74739
	.byte	10
	.byte	'_Ifx_CPU_CPR',0,25,247,9,25,8,13
	.byte	'L',0
	.word	70669
	.byte	4,2,35,0,13
	.byte	'U',0
	.word	70732
	.byte	4,2,35,4,0,14
	.word	74805
	.byte	32
	.byte	'Ifx_CPU_CPR',0,25,251,9,3
	.word	74847
	.byte	10
	.byte	'_Ifx_CPU_DPR',0,25,254,9,25,8,13
	.byte	'L',0
	.word	71733
	.byte	4,2,35,0,13
	.byte	'U',0
	.word	71796
	.byte	4,2,35,4,0,14
	.word	74873
	.byte	32
	.byte	'Ifx_CPU_DPR',0,25,130,10,3
	.word	74915
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN',0,25,133,10,25,16,13
	.byte	'LA',0
	.word	74146
	.byte	4,2,35,0,13
	.byte	'UA',0
	.word	74216
	.byte	4,2,35,4,13
	.byte	'ACCENA',0
	.word	73998
	.byte	4,2,35,8,13
	.byte	'ACCENB',0
	.word	74072
	.byte	4,2,35,12,0,14
	.word	74941
	.byte	32
	.byte	'Ifx_CPU_SPROT_RGN',0,25,139,10,3
	.word	75023
	.byte	15,12
	.word	74544
	.byte	16,2,0,10
	.byte	'_Ifx_CPU_TPS',0,25,142,10,25,16,13
	.byte	'CON',0
	.word	74479
	.byte	4,2,35,0,13
	.byte	'TIMER',0
	.word	75055
	.byte	12,2,35,4,0,14
	.word	75064
	.byte	32
	.byte	'Ifx_CPU_TPS',0,25,146,10,3
	.word	75112
	.byte	10
	.byte	'_Ifx_CPU_TR',0,25,149,10,25,8,13
	.byte	'EVT',0
	.word	74675
	.byte	4,2,35,0,13
	.byte	'ADR',0
	.word	74611
	.byte	4,2,35,4,0,14
	.word	75138
	.byte	32
	.byte	'Ifx_CPU_TR',0,25,153,10,3
	.word	75183
	.byte	15,176,32
	.word	641
	.byte	16,175,32,0,15,208,223,1
	.word	641
	.byte	16,207,223,1,0,15,248,1
	.word	641
	.byte	16,247,1,0,15,244,29
	.word	641
	.byte	16,243,29,0,15,188,3
	.word	641
	.byte	16,187,3,0,15,232,3
	.word	641
	.byte	16,231,3,0,15,252,23
	.word	641
	.byte	16,251,23,0,15,228,63
	.word	641
	.byte	16,227,63,0,15,128,1
	.word	74873
	.byte	16,15,0,14
	.word	75298
	.byte	15,128,31
	.word	641
	.byte	16,255,30,0,15,64
	.word	74805
	.byte	16,7,0,14
	.word	75324
	.byte	15,192,31
	.word	641
	.byte	16,191,31,0,15,16
	.word	70859
	.byte	16,3,0,15,16
	.word	71859
	.byte	16,3,0,15,16
	.word	71921
	.byte	16,3,0,15,208,7
	.word	641
	.byte	16,207,7,0,14
	.word	75064
	.byte	15,240,23
	.word	641
	.byte	16,239,23,0,15,64
	.word	75138
	.byte	16,7,0,14
	.word	75403
	.byte	15,192,23
	.word	641
	.byte	16,191,23,0,15,232,1
	.word	641
	.byte	16,231,1,0,15,180,1
	.word	641
	.byte	16,179,1,0,15,172,1
	.word	641
	.byte	16,171,1,0,15,64
	.word	71048
	.byte	16,15,0,15,64
	.word	641
	.byte	16,63,0,15,64
	.word	70234
	.byte	16,15,0,10
	.byte	'_Ifx_CPU',0,25,166,10,25,128,128,4,13
	.byte	'reserved_0',0
	.word	75208
	.byte	176,32,2,35,0,13
	.byte	'SEGEN',0
	.word	73731
	.byte	4,3,35,176,32,13
	.byte	'reserved_1034',0
	.word	75219
	.byte	208,223,1,3,35,180,32,13
	.byte	'TASK_ASI',0
	.word	74413
	.byte	4,4,35,132,128,2,13
	.byte	'reserved_8008',0
	.word	75232
	.byte	248,1,4,35,136,128,2,13
	.byte	'PMA0',0
	.word	73422
	.byte	4,4,35,128,130,2,13
	.byte	'PMA1',0
	.word	73484
	.byte	4,4,35,132,130,2,13
	.byte	'PMA2',0
	.word	73546
	.byte	4,4,35,136,130,2,13
	.byte	'reserved_810C',0
	.word	75243
	.byte	244,29,4,35,140,130,2,13
	.byte	'DCON2',0
	.word	71359
	.byte	4,4,35,128,160,2,13
	.byte	'reserved_9004',0
	.word	4278
	.byte	8,4,35,132,160,2,13
	.byte	'SMACON',0
	.word	73794
	.byte	4,4,35,140,160,2,13
	.byte	'DSTR',0
	.word	71983
	.byte	4,4,35,144,160,2,13
	.byte	'reserved_9014',0
	.word	2459
	.byte	4,4,35,148,160,2,13
	.byte	'DATR',0
	.word	71107
	.byte	4,4,35,152,160,2,13
	.byte	'DEADD',0
	.word	71483
	.byte	4,4,35,156,160,2,13
	.byte	'DIEAR',0
	.word	71546
	.byte	4,4,35,160,160,2,13
	.byte	'DIETR',0
	.word	71609
	.byte	4,4,35,164,160,2,13
	.byte	'reserved_9028',0
	.word	3649
	.byte	24,4,35,168,160,2,13
	.byte	'DCON0',0
	.word	71296
	.byte	4,4,35,192,160,2,13
	.byte	'reserved_9044',0
	.word	75254
	.byte	188,3,4,35,196,160,2,13
	.byte	'PSTR',0
	.word	73608
	.byte	4,4,35,128,164,2,13
	.byte	'PCON1',0
	.word	73108
	.byte	4,4,35,132,164,2,13
	.byte	'PCON2',0
	.word	73171
	.byte	4,4,35,136,164,2,13
	.byte	'PCON0',0
	.word	73045
	.byte	4,4,35,140,164,2,13
	.byte	'PIEAR',0
	.word	73296
	.byte	4,4,35,144,164,2,13
	.byte	'PIETR',0
	.word	73359
	.byte	4,4,35,148,164,2,13
	.byte	'reserved_9218',0
	.word	75265
	.byte	232,3,4,35,152,164,2,13
	.byte	'COMPAT',0
	.word	70540
	.byte	4,4,35,128,168,2,13
	.byte	'reserved_9404',0
	.word	75276
	.byte	252,23,4,35,132,168,2,13
	.byte	'FPU_TRAP_CON',0
	.word	72169
	.byte	4,4,35,128,192,2,13
	.byte	'FPU_TRAP_PC',0
	.word	72309
	.byte	4,4,35,132,192,2,13
	.byte	'FPU_TRAP_OPC',0
	.word	72239
	.byte	4,4,35,136,192,2,13
	.byte	'reserved_A00C',0
	.word	2459
	.byte	4,4,35,140,192,2,13
	.byte	'FPU_TRAP_SRC1',0
	.word	72378
	.byte	4,4,35,144,192,2,13
	.byte	'FPU_TRAP_SRC2',0
	.word	72449
	.byte	4,4,35,148,192,2,13
	.byte	'FPU_TRAP_SRC3',0
	.word	72520
	.byte	4,4,35,152,192,2,13
	.byte	'reserved_A01C',0
	.word	75287
	.byte	228,63,4,35,156,192,2,13
	.byte	'DPR',0
	.word	75308
	.byte	128,1,4,35,128,128,3,13
	.byte	'reserved_C080',0
	.word	75313
	.byte	128,31,4,35,128,129,3,13
	.byte	'CPR',0
	.word	75333
	.byte	64,4,35,128,160,3,13
	.byte	'reserved_D040',0
	.word	75338
	.byte	192,31,4,35,192,160,3,13
	.byte	'CPXE',0
	.word	75349
	.byte	16,4,35,128,192,3,13
	.byte	'DPRE',0
	.word	75358
	.byte	16,4,35,144,192,3,13
	.byte	'DPWE',0
	.word	75367
	.byte	16,4,35,160,192,3,13
	.byte	'reserved_E030',0
	.word	75376
	.byte	208,7,4,35,176,192,3,13
	.byte	'TPS',0
	.word	75387
	.byte	16,4,35,128,200,3,13
	.byte	'reserved_E410',0
	.word	75392
	.byte	240,23,4,35,144,200,3,13
	.byte	'TR',0
	.word	75412
	.byte	64,4,35,128,224,3,13
	.byte	'reserved_F040',0
	.word	75417
	.byte	192,23,4,35,192,224,3,13
	.byte	'CCTRL',0
	.word	70477
	.byte	4,4,35,128,248,3,13
	.byte	'CCNT',0
	.word	70415
	.byte	4,4,35,132,248,3,13
	.byte	'ICNT',0
	.word	72591
	.byte	4,4,35,136,248,3,13
	.byte	'M1CNT',0
	.word	72796
	.byte	4,4,35,140,248,3,13
	.byte	'M2CNT',0
	.word	72859
	.byte	4,4,35,144,248,3,13
	.byte	'M3CNT',0
	.word	72922
	.byte	4,4,35,148,248,3,13
	.byte	'reserved_FC18',0
	.word	75428
	.byte	232,1,4,35,152,248,3,13
	.byte	'DBGSR',0
	.word	71169
	.byte	4,4,35,128,250,3,13
	.byte	'reserved_FD04',0
	.word	2459
	.byte	4,4,35,132,250,3,13
	.byte	'EXEVT',0
	.word	72045
	.byte	4,4,35,136,250,3,13
	.byte	'CREVT',0
	.word	70921
	.byte	4,4,35,140,250,3,13
	.byte	'SWEVT',0
	.word	74286
	.byte	4,4,35,144,250,3,13
	.byte	'reserved_FD14',0
	.word	57510
	.byte	28,4,35,148,250,3,13
	.byte	'TRIG_ACC',0
	.word	74739
	.byte	4,4,35,176,250,3,13
	.byte	'reserved_FD34',0
	.word	4618
	.byte	12,4,35,180,250,3,13
	.byte	'DMS',0
	.word	71672
	.byte	4,4,35,192,250,3,13
	.byte	'DCX',0
	.word	71422
	.byte	4,4,35,196,250,3,13
	.byte	'DBGTCR',0
	.word	71232
	.byte	4,4,35,200,250,3,13
	.byte	'reserved_FD4C',0
	.word	75439
	.byte	180,1,4,35,204,250,3,13
	.byte	'PCXI',0
	.word	73234
	.byte	4,4,35,128,252,3,13
	.byte	'PSW',0
	.word	73670
	.byte	4,4,35,132,252,3,13
	.byte	'PC',0
	.word	72985
	.byte	4,4,35,136,252,3,13
	.byte	'reserved_FE0C',0
	.word	4278
	.byte	8,4,35,140,252,3,13
	.byte	'SYSCON',0
	.word	74349
	.byte	4,4,35,148,252,3,13
	.byte	'CPU_ID',0
	.word	70795
	.byte	4,4,35,152,252,3,13
	.byte	'CORE_ID',0
	.word	70604
	.byte	4,4,35,156,252,3,13
	.byte	'BIV',0
	.word	70293
	.byte	4,4,35,160,252,3,13
	.byte	'BTV',0
	.word	70354
	.byte	4,4,35,164,252,3,13
	.byte	'ISP',0
	.word	72674
	.byte	4,4,35,168,252,3,13
	.byte	'ICR',0
	.word	31280
	.byte	4,4,35,172,252,3,13
	.byte	'reserved_FE30',0
	.word	4278
	.byte	8,4,35,176,252,3,13
	.byte	'FCX',0
	.word	72108
	.byte	4,4,35,184,252,3,13
	.byte	'LCX',0
	.word	72735
	.byte	4,4,35,188,252,3,13
	.byte	'reserved_FE40',0
	.word	57501
	.byte	16,4,35,192,252,3,13
	.byte	'CUS_ID',0
	.word	70984
	.byte	4,4,35,208,252,3,13
	.byte	'reserved_FE54',0
	.word	75450
	.byte	172,1,4,35,212,252,3,13
	.byte	'D',0
	.word	75461
	.byte	64,4,35,128,254,3,13
	.byte	'reserved_FF40',0
	.word	75470
	.byte	64,4,35,192,254,3,13
	.byte	'A',0
	.word	75479
	.byte	64,4,35,128,255,3,13
	.byte	'reserved_FFC0',0
	.word	75470
	.byte	64,4,35,192,255,3,0,14
	.word	75488
	.byte	32
	.byte	'Ifx_CPU',0,25,130,11,3
	.word	77279
	.byte	17,9,127,9,1,18
	.byte	'IfxCpu_Id_0',0,0,18
	.byte	'IfxCpu_Id_1',0,1,18
	.byte	'IfxCpu_Id_none',0,2,0,32
	.byte	'IfxCpu_Id',0,9,132,1,3
	.word	77301
	.byte	32
	.byte	'IfxCpu_ResourceCpu',0,9,161,1,3
	.word	10020
	.byte	32
	.byte	'Ifx_SRC_SRCR_Bits',0,11,62,3
	.word	10573
	.byte	32
	.byte	'Ifx_SRC_SRCR',0,11,75,3
	.word	10863
	.byte	10
	.byte	'_Ifx_SRC_AGBT',0,11,86,25,4,13
	.byte	'SR',0
	.word	10863
	.byte	4,2,35,0,0,14
	.word	77446
	.byte	32
	.byte	'Ifx_SRC_AGBT',0,11,89,3
	.word	77478
	.byte	10
	.byte	'_Ifx_SRC_ASCLIN',0,11,92,25,12,13
	.byte	'TX',0
	.word	10863
	.byte	4,2,35,0,13
	.byte	'RX',0
	.word	10863
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	10863
	.byte	4,2,35,8,0,14
	.word	77504
	.byte	32
	.byte	'Ifx_SRC_ASCLIN',0,11,97,3
	.word	77563
	.byte	10
	.byte	'_Ifx_SRC_BCUSPB',0,11,100,25,4,13
	.byte	'SBSRC',0
	.word	10863
	.byte	4,2,35,0,0,14
	.word	77591
	.byte	32
	.byte	'Ifx_SRC_BCUSPB',0,11,103,3
	.word	77628
	.byte	15,64
	.word	10863
	.byte	16,15,0,10
	.byte	'_Ifx_SRC_CAN',0,11,106,25,64,13
	.byte	'INT',0
	.word	77656
	.byte	64,2,35,0,0,14
	.word	77665
	.byte	32
	.byte	'Ifx_SRC_CAN',0,11,109,3
	.word	77697
	.byte	10
	.byte	'_Ifx_SRC_CCU6',0,11,112,25,16,13
	.byte	'SR0',0
	.word	10863
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10863
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	10863
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	10863
	.byte	4,2,35,12,0,14
	.word	77722
	.byte	32
	.byte	'Ifx_SRC_CCU6',0,11,118,3
	.word	77794
	.byte	15,8
	.word	10863
	.byte	16,1,0,10
	.byte	'_Ifx_SRC_CERBERUS',0,11,121,25,8,13
	.byte	'SR',0
	.word	77820
	.byte	8,2,35,0,0,14
	.word	77829
	.byte	32
	.byte	'Ifx_SRC_CERBERUS',0,11,124,3
	.word	77865
	.byte	10
	.byte	'_Ifx_SRC_CIF',0,11,127,25,16,13
	.byte	'MI',0
	.word	10863
	.byte	4,2,35,0,13
	.byte	'MIEP',0
	.word	10863
	.byte	4,2,35,4,13
	.byte	'ISP',0
	.word	10863
	.byte	4,2,35,8,13
	.byte	'MJPEG',0
	.word	10863
	.byte	4,2,35,12,0,14
	.word	77895
	.byte	32
	.byte	'Ifx_SRC_CIF',0,11,133,1,3
	.word	77968
	.byte	10
	.byte	'_Ifx_SRC_CPU',0,11,136,1,25,4,13
	.byte	'SBSRC',0
	.word	10863
	.byte	4,2,35,0,0,14
	.word	77994
	.byte	32
	.byte	'Ifx_SRC_CPU',0,11,139,1,3
	.word	78029
	.byte	15,192,1
	.word	10863
	.byte	16,47,0,10
	.byte	'_Ifx_SRC_DMA',0,11,142,1,25,208,1,13
	.byte	'ERR',0
	.word	10863
	.byte	4,2,35,0,13
	.byte	'reserved_4',0
	.word	4618
	.byte	12,2,35,4,13
	.byte	'CH',0
	.word	78055
	.byte	192,1,2,35,16,0,14
	.word	78065
	.byte	32
	.byte	'Ifx_SRC_DMA',0,11,147,1,3
	.word	78132
	.byte	10
	.byte	'_Ifx_SRC_DSADC',0,11,150,1,25,8,13
	.byte	'SRM',0
	.word	10863
	.byte	4,2,35,0,13
	.byte	'SRA',0
	.word	10863
	.byte	4,2,35,4,0,14
	.word	78158
	.byte	32
	.byte	'Ifx_SRC_DSADC',0,11,154,1,3
	.word	78206
	.byte	10
	.byte	'_Ifx_SRC_EMEM',0,11,157,1,25,4,13
	.byte	'SR',0
	.word	10863
	.byte	4,2,35,0,0,14
	.word	78234
	.byte	32
	.byte	'Ifx_SRC_EMEM',0,11,160,1,3
	.word	78267
	.byte	10
	.byte	'_Ifx_SRC_ERAY',0,11,163,1,25,80,13
	.byte	'INT',0
	.word	77820
	.byte	8,2,35,0,13
	.byte	'TINT',0
	.word	77820
	.byte	8,2,35,8,13
	.byte	'NDAT',0
	.word	77820
	.byte	8,2,35,16,13
	.byte	'MBSC',0
	.word	77820
	.byte	8,2,35,24,13
	.byte	'OBUSY',0
	.word	10863
	.byte	4,2,35,32,13
	.byte	'IBUSY',0
	.word	10863
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	57519
	.byte	40,2,35,40,0,14
	.word	78294
	.byte	32
	.byte	'Ifx_SRC_ERAY',0,11,172,1,3
	.word	78421
	.byte	10
	.byte	'_Ifx_SRC_ETH',0,11,175,1,25,4,13
	.byte	'SR',0
	.word	10863
	.byte	4,2,35,0,0,14
	.word	78448
	.byte	32
	.byte	'Ifx_SRC_ETH',0,11,178,1,3
	.word	78480
	.byte	10
	.byte	'_Ifx_SRC_FCE',0,11,181,1,25,4,13
	.byte	'SR',0
	.word	10863
	.byte	4,2,35,0,0,14
	.word	78506
	.byte	32
	.byte	'Ifx_SRC_FCE',0,11,184,1,3
	.word	78538
	.byte	10
	.byte	'_Ifx_SRC_FFT',0,11,187,1,25,12,13
	.byte	'DONE',0
	.word	10863
	.byte	4,2,35,0,13
	.byte	'ERR',0
	.word	10863
	.byte	4,2,35,4,13
	.byte	'RFS',0
	.word	10863
	.byte	4,2,35,8,0,14
	.word	78564
	.byte	32
	.byte	'Ifx_SRC_FFT',0,11,192,1,3
	.word	78624
	.byte	10
	.byte	'_Ifx_SRC_GPSR',0,11,195,1,25,32,13
	.byte	'SR0',0
	.word	10863
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10863
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	10863
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	10863
	.byte	4,2,35,12,13
	.byte	'reserved_10',0
	.word	57501
	.byte	16,2,35,16,0,14
	.word	78650
	.byte	32
	.byte	'Ifx_SRC_GPSR',0,11,202,1,3
	.word	78744
	.byte	10
	.byte	'_Ifx_SRC_GPT12',0,11,205,1,25,48,13
	.byte	'CIRQ',0
	.word	10863
	.byte	4,2,35,0,13
	.byte	'T2',0
	.word	10863
	.byte	4,2,35,4,13
	.byte	'T3',0
	.word	10863
	.byte	4,2,35,8,13
	.byte	'T4',0
	.word	10863
	.byte	4,2,35,12,13
	.byte	'T5',0
	.word	10863
	.byte	4,2,35,16,13
	.byte	'T6',0
	.word	10863
	.byte	4,2,35,20,13
	.byte	'reserved_18',0
	.word	3649
	.byte	24,2,35,24,0,14
	.word	78771
	.byte	32
	.byte	'Ifx_SRC_GPT12',0,11,214,1,3
	.word	78888
	.byte	15,12
	.word	10863
	.byte	16,2,0,15,32
	.word	10863
	.byte	16,7,0,15,32
	.word	78925
	.byte	16,0,0,15,88
	.word	641
	.byte	16,87,0,15,108
	.word	10863
	.byte	16,26,0,15,96
	.word	641
	.byte	16,95,0,15,96
	.word	78925
	.byte	16,2,0,15,160,3
	.word	641
	.byte	16,159,3,0,15,64
	.word	78925
	.byte	16,1,0,15,192,3
	.word	641
	.byte	16,191,3,0,15,16
	.word	10863
	.byte	16,3,0,15,64
	.word	79010
	.byte	16,3,0,15,192,2
	.word	641
	.byte	16,191,2,0,15,52
	.word	641
	.byte	16,51,0,10
	.byte	'_Ifx_SRC_GTM',0,11,217,1,25,204,18,13
	.byte	'AEIIRQ',0
	.word	10863
	.byte	4,2,35,0,13
	.byte	'ARUIRQ',0
	.word	78916
	.byte	12,2,35,4,13
	.byte	'reserved_10',0
	.word	2459
	.byte	4,2,35,16,13
	.byte	'BRCIRQ',0
	.word	10863
	.byte	4,2,35,20,13
	.byte	'CMPIRQ',0
	.word	10863
	.byte	4,2,35,24,13
	.byte	'SPEIRQ',0
	.word	77820
	.byte	8,2,35,28,13
	.byte	'reserved_24',0
	.word	4278
	.byte	8,2,35,36,13
	.byte	'PSM',0
	.word	78934
	.byte	32,2,35,44,13
	.byte	'reserved_4C',0
	.word	78943
	.byte	88,2,35,76,13
	.byte	'DPLL',0
	.word	78952
	.byte	108,3,35,164,1,13
	.byte	'reserved_110',0
	.word	78961
	.byte	96,3,35,144,2,13
	.byte	'ERR',0
	.word	10863
	.byte	4,3,35,240,2,13
	.byte	'reserved_174',0
	.word	4618
	.byte	12,3,35,244,2,13
	.byte	'TIM',0
	.word	78970
	.byte	96,3,35,128,3,13
	.byte	'reserved_1E0',0
	.word	78979
	.byte	160,3,3,35,224,3,13
	.byte	'MCS',0
	.word	78970
	.byte	96,3,35,128,7,13
	.byte	'reserved_3E0',0
	.word	78979
	.byte	160,3,3,35,224,7,13
	.byte	'TOM',0
	.word	78990
	.byte	64,3,35,128,11,13
	.byte	'reserved_5C0',0
	.word	78999
	.byte	192,3,3,35,192,11,13
	.byte	'ATOM',0
	.word	79019
	.byte	64,3,35,128,15,13
	.byte	'reserved_7C0',0
	.word	79028
	.byte	192,2,3,35,192,15,13
	.byte	'MCSW0',0
	.word	78916
	.byte	12,3,35,128,18,13
	.byte	'reserved_90C',0
	.word	79039
	.byte	52,3,35,140,18,13
	.byte	'MCSW1',0
	.word	78916
	.byte	12,3,35,192,18,0,14
	.word	79048
	.byte	32
	.byte	'Ifx_SRC_GTM',0,11,243,1,3
	.word	79508
	.byte	10
	.byte	'_Ifx_SRC_HSCT',0,11,246,1,25,4,13
	.byte	'SR',0
	.word	10863
	.byte	4,2,35,0,0,14
	.word	79534
	.byte	32
	.byte	'Ifx_SRC_HSCT',0,11,249,1,3
	.word	79567
	.byte	10
	.byte	'_Ifx_SRC_HSSL',0,11,252,1,25,16,13
	.byte	'COK',0
	.word	10863
	.byte	4,2,35,0,13
	.byte	'RDI',0
	.word	10863
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	10863
	.byte	4,2,35,8,13
	.byte	'TRG',0
	.word	10863
	.byte	4,2,35,12,0,14
	.word	79594
	.byte	32
	.byte	'Ifx_SRC_HSSL',0,11,130,2,3
	.word	79667
	.byte	15,56
	.word	641
	.byte	16,55,0,10
	.byte	'_Ifx_SRC_I2C',0,11,133,2,25,80,13
	.byte	'BREQ',0
	.word	10863
	.byte	4,2,35,0,13
	.byte	'LBREQ',0
	.word	10863
	.byte	4,2,35,4,13
	.byte	'SREQ',0
	.word	10863
	.byte	4,2,35,8,13
	.byte	'LSREQ',0
	.word	10863
	.byte	4,2,35,12,13
	.byte	'ERR',0
	.word	10863
	.byte	4,2,35,16,13
	.byte	'P',0
	.word	10863
	.byte	4,2,35,20,13
	.byte	'reserved_18',0
	.word	79694
	.byte	56,2,35,24,0,14
	.word	79703
	.byte	32
	.byte	'Ifx_SRC_I2C',0,11,142,2,3
	.word	79826
	.byte	10
	.byte	'_Ifx_SRC_LMU',0,11,145,2,25,4,13
	.byte	'SR',0
	.word	10863
	.byte	4,2,35,0,0,14
	.word	79852
	.byte	32
	.byte	'Ifx_SRC_LMU',0,11,148,2,3
	.word	79884
	.byte	10
	.byte	'_Ifx_SRC_MSC',0,11,151,2,25,20,13
	.byte	'SR0',0
	.word	10863
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10863
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	10863
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	10863
	.byte	4,2,35,12,13
	.byte	'SR4',0
	.word	10863
	.byte	4,2,35,16,0,14
	.word	79910
	.byte	32
	.byte	'Ifx_SRC_MSC',0,11,158,2,3
	.word	79995
	.byte	10
	.byte	'_Ifx_SRC_PMU',0,11,161,2,25,4,13
	.byte	'SR',0
	.word	10863
	.byte	4,2,35,0,0,14
	.word	80021
	.byte	32
	.byte	'Ifx_SRC_PMU',0,11,164,2,3
	.word	80053
	.byte	10
	.byte	'_Ifx_SRC_PSI5',0,11,167,2,25,32,13
	.byte	'SR',0
	.word	78925
	.byte	32,2,35,0,0,14
	.word	80079
	.byte	32
	.byte	'Ifx_SRC_PSI5',0,11,170,2,3
	.word	80112
	.byte	10
	.byte	'_Ifx_SRC_PSI5S',0,11,173,2,25,32,13
	.byte	'SR',0
	.word	78925
	.byte	32,2,35,0,0,14
	.word	80139
	.byte	32
	.byte	'Ifx_SRC_PSI5S',0,11,176,2,3
	.word	80173
	.byte	10
	.byte	'_Ifx_SRC_QSPI',0,11,179,2,25,24,13
	.byte	'TX',0
	.word	10863
	.byte	4,2,35,0,13
	.byte	'RX',0
	.word	10863
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	10863
	.byte	4,2,35,8,13
	.byte	'PT',0
	.word	10863
	.byte	4,2,35,12,13
	.byte	'HC',0
	.word	10863
	.byte	4,2,35,16,13
	.byte	'U',0
	.word	10863
	.byte	4,2,35,20,0,14
	.word	80201
	.byte	32
	.byte	'Ifx_SRC_QSPI',0,11,187,2,3
	.word	80294
	.byte	10
	.byte	'_Ifx_SRC_SCR',0,11,190,2,25,4,13
	.byte	'SR',0
	.word	10863
	.byte	4,2,35,0,0,14
	.word	80321
	.byte	32
	.byte	'Ifx_SRC_SCR',0,11,193,2,3
	.word	80353
	.byte	10
	.byte	'_Ifx_SRC_SCU',0,11,196,2,25,20,13
	.byte	'DTS',0
	.word	10863
	.byte	4,2,35,0,13
	.byte	'ERU',0
	.word	79010
	.byte	16,2,35,4,0,14
	.word	80379
	.byte	32
	.byte	'Ifx_SRC_SCU',0,11,200,2,3
	.word	80425
	.byte	15,24
	.word	10863
	.byte	16,5,0,10
	.byte	'_Ifx_SRC_SENT',0,11,203,2,25,24,13
	.byte	'SR',0
	.word	80451
	.byte	24,2,35,0,0,14
	.word	80460
	.byte	32
	.byte	'Ifx_SRC_SENT',0,11,206,2,3
	.word	80493
	.byte	10
	.byte	'_Ifx_SRC_SMU',0,11,209,2,25,12,13
	.byte	'SR',0
	.word	78916
	.byte	12,2,35,0,0,14
	.word	80520
	.byte	32
	.byte	'Ifx_SRC_SMU',0,11,212,2,3
	.word	80552
	.byte	10
	.byte	'_Ifx_SRC_STM',0,11,215,2,25,8,13
	.byte	'SR0',0
	.word	10863
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10863
	.byte	4,2,35,4,0,14
	.word	80578
	.byte	32
	.byte	'Ifx_SRC_STM',0,11,219,2,3
	.word	80624
	.byte	10
	.byte	'_Ifx_SRC_VADCCG',0,11,222,2,25,16,13
	.byte	'SR0',0
	.word	10863
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10863
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	10863
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	10863
	.byte	4,2,35,12,0,14
	.word	80650
	.byte	32
	.byte	'Ifx_SRC_VADCCG',0,11,228,2,3
	.word	80725
	.byte	10
	.byte	'_Ifx_SRC_VADCG',0,11,231,2,25,16,13
	.byte	'SR0',0
	.word	10863
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10863
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	10863
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	10863
	.byte	4,2,35,12,0,14
	.word	80754
	.byte	32
	.byte	'Ifx_SRC_VADCG',0,11,237,2,3
	.word	80828
	.byte	10
	.byte	'_Ifx_SRC_XBAR',0,11,240,2,25,4,13
	.byte	'SRC',0
	.word	10863
	.byte	4,2,35,0,0,14
	.word	80856
	.byte	32
	.byte	'Ifx_SRC_XBAR',0,11,243,2,3
	.word	80890
	.byte	15,4
	.word	77446
	.byte	16,0,0,14
	.word	80917
	.byte	10
	.byte	'_Ifx_SRC_GAGBT',0,11,128,3,25,4,13
	.byte	'AGBT',0
	.word	80926
	.byte	4,2,35,0,0,14
	.word	80931
	.byte	32
	.byte	'Ifx_SRC_GAGBT',0,11,131,3,3
	.word	80967
	.byte	15,48
	.word	77504
	.byte	16,3,0,14
	.word	80995
	.byte	10
	.byte	'_Ifx_SRC_GASCLIN',0,11,134,3,25,48,13
	.byte	'ASCLIN',0
	.word	81004
	.byte	48,2,35,0,0,14
	.word	81009
	.byte	32
	.byte	'Ifx_SRC_GASCLIN',0,11,137,3,3
	.word	81049
	.byte	14
	.word	77591
	.byte	10
	.byte	'_Ifx_SRC_GBCU',0,11,140,3,25,4,13
	.byte	'SPB',0
	.word	81079
	.byte	4,2,35,0,0,14
	.word	81084
	.byte	32
	.byte	'Ifx_SRC_GBCU',0,11,143,3,3
	.word	81118
	.byte	15,64
	.word	77665
	.byte	16,0,0,14
	.word	81145
	.byte	10
	.byte	'_Ifx_SRC_GCAN',0,11,146,3,25,64,13
	.byte	'CAN',0
	.word	81154
	.byte	64,2,35,0,0,14
	.word	81159
	.byte	32
	.byte	'Ifx_SRC_GCAN',0,11,149,3,3
	.word	81193
	.byte	15,32
	.word	77722
	.byte	16,1,0,14
	.word	81220
	.byte	10
	.byte	'_Ifx_SRC_GCCU6',0,11,152,3,25,32,13
	.byte	'CCU6',0
	.word	81229
	.byte	32,2,35,0,0,14
	.word	81234
	.byte	32
	.byte	'Ifx_SRC_GCCU6',0,11,155,3,3
	.word	81270
	.byte	14
	.word	77829
	.byte	10
	.byte	'_Ifx_SRC_GCERBERUS',0,11,158,3,25,8,13
	.byte	'CERBERUS',0
	.word	81298
	.byte	8,2,35,0,0,14
	.word	81303
	.byte	32
	.byte	'Ifx_SRC_GCERBERUS',0,11,161,3,3
	.word	81347
	.byte	15,16
	.word	77895
	.byte	16,0,0,14
	.word	81379
	.byte	10
	.byte	'_Ifx_SRC_GCIF',0,11,164,3,25,16,13
	.byte	'CIF',0
	.word	81388
	.byte	16,2,35,0,0,14
	.word	81393
	.byte	32
	.byte	'Ifx_SRC_GCIF',0,11,167,3,3
	.word	81427
	.byte	15,8
	.word	77994
	.byte	16,1,0,14
	.word	81454
	.byte	10
	.byte	'_Ifx_SRC_GCPU',0,11,170,3,25,8,13
	.byte	'CPU',0
	.word	81463
	.byte	8,2,35,0,0,14
	.word	81468
	.byte	32
	.byte	'Ifx_SRC_GCPU',0,11,173,3,3
	.word	81502
	.byte	15,208,1
	.word	78065
	.byte	16,0,0,14
	.word	81529
	.byte	10
	.byte	'_Ifx_SRC_GDMA',0,11,176,3,25,208,1,13
	.byte	'DMA',0
	.word	81539
	.byte	208,1,2,35,0,0,14
	.word	81544
	.byte	32
	.byte	'Ifx_SRC_GDMA',0,11,179,3,3
	.word	81580
	.byte	14
	.word	78158
	.byte	14
	.word	78158
	.byte	14
	.word	78158
	.byte	10
	.byte	'_Ifx_SRC_GDSADC',0,11,182,3,25,32,13
	.byte	'DSADC0',0
	.word	81607
	.byte	8,2,35,0,13
	.byte	'reserved_8',0
	.word	4278
	.byte	8,2,35,8,13
	.byte	'DSADC2',0
	.word	81612
	.byte	8,2,35,16,13
	.byte	'DSADC3',0
	.word	81617
	.byte	8,2,35,24,0,14
	.word	81622
	.byte	32
	.byte	'Ifx_SRC_GDSADC',0,11,188,3,3
	.word	81713
	.byte	15,4
	.word	78234
	.byte	16,0,0,14
	.word	81742
	.byte	10
	.byte	'_Ifx_SRC_GEMEM',0,11,191,3,25,4,13
	.byte	'EMEM',0
	.word	81751
	.byte	4,2,35,0,0,14
	.word	81756
	.byte	32
	.byte	'Ifx_SRC_GEMEM',0,11,194,3,3
	.word	81792
	.byte	15,80
	.word	78294
	.byte	16,0,0,14
	.word	81820
	.byte	10
	.byte	'_Ifx_SRC_GERAY',0,11,197,3,25,80,13
	.byte	'ERAY',0
	.word	81829
	.byte	80,2,35,0,0,14
	.word	81834
	.byte	32
	.byte	'Ifx_SRC_GERAY',0,11,200,3,3
	.word	81870
	.byte	15,4
	.word	78448
	.byte	16,0,0,14
	.word	81898
	.byte	10
	.byte	'_Ifx_SRC_GETH',0,11,203,3,25,4,13
	.byte	'ETH',0
	.word	81907
	.byte	4,2,35,0,0,14
	.word	81912
	.byte	32
	.byte	'Ifx_SRC_GETH',0,11,206,3,3
	.word	81946
	.byte	15,4
	.word	78506
	.byte	16,0,0,14
	.word	81973
	.byte	10
	.byte	'_Ifx_SRC_GFCE',0,11,209,3,25,4,13
	.byte	'FCE',0
	.word	81982
	.byte	4,2,35,0,0,14
	.word	81987
	.byte	32
	.byte	'Ifx_SRC_GFCE',0,11,212,3,3
	.word	82021
	.byte	15,12
	.word	78564
	.byte	16,0,0,14
	.word	82048
	.byte	10
	.byte	'_Ifx_SRC_GFFT',0,11,215,3,25,12,13
	.byte	'FFT',0
	.word	82057
	.byte	12,2,35,0,0,14
	.word	82062
	.byte	32
	.byte	'Ifx_SRC_GFFT',0,11,218,3,3
	.word	82096
	.byte	15,64
	.word	78650
	.byte	16,1,0,14
	.word	82123
	.byte	10
	.byte	'_Ifx_SRC_GGPSR',0,11,221,3,25,64,13
	.byte	'GPSR',0
	.word	82132
	.byte	64,2,35,0,0,14
	.word	82137
	.byte	32
	.byte	'Ifx_SRC_GGPSR',0,11,224,3,3
	.word	82173
	.byte	15,48
	.word	78771
	.byte	16,0,0,14
	.word	82201
	.byte	10
	.byte	'_Ifx_SRC_GGPT12',0,11,227,3,25,48,13
	.byte	'GPT12',0
	.word	82210
	.byte	48,2,35,0,0,14
	.word	82215
	.byte	32
	.byte	'Ifx_SRC_GGPT12',0,11,230,3,3
	.word	82253
	.byte	15,204,18
	.word	79048
	.byte	16,0,0,14
	.word	82282
	.byte	10
	.byte	'_Ifx_SRC_GGTM',0,11,233,3,25,204,18,13
	.byte	'GTM',0
	.word	82292
	.byte	204,18,2,35,0,0,14
	.word	82297
	.byte	32
	.byte	'Ifx_SRC_GGTM',0,11,236,3,3
	.word	82333
	.byte	15,4
	.word	79534
	.byte	16,0,0,14
	.word	82360
	.byte	10
	.byte	'_Ifx_SRC_GHSCT',0,11,239,3,25,4,13
	.byte	'HSCT',0
	.word	82369
	.byte	4,2,35,0,0,14
	.word	82374
	.byte	32
	.byte	'Ifx_SRC_GHSCT',0,11,242,3,3
	.word	82410
	.byte	15,64
	.word	79594
	.byte	16,3,0,14
	.word	82438
	.byte	10
	.byte	'_Ifx_SRC_GHSSL',0,11,245,3,25,68,13
	.byte	'HSSL',0
	.word	82447
	.byte	64,2,35,0,13
	.byte	'EXI',0
	.word	10863
	.byte	4,2,35,64,0,14
	.word	82452
	.byte	32
	.byte	'Ifx_SRC_GHSSL',0,11,249,3,3
	.word	82501
	.byte	15,80
	.word	79703
	.byte	16,0,0,14
	.word	82529
	.byte	10
	.byte	'_Ifx_SRC_GI2C',0,11,252,3,25,80,13
	.byte	'I2C',0
	.word	82538
	.byte	80,2,35,0,0,14
	.word	82543
	.byte	32
	.byte	'Ifx_SRC_GI2C',0,11,255,3,3
	.word	82577
	.byte	15,4
	.word	79852
	.byte	16,0,0,14
	.word	82604
	.byte	10
	.byte	'_Ifx_SRC_GLMU',0,11,130,4,25,4,13
	.byte	'LMU',0
	.word	82613
	.byte	4,2,35,0,0,14
	.word	82618
	.byte	32
	.byte	'Ifx_SRC_GLMU',0,11,133,4,3
	.word	82652
	.byte	15,40
	.word	79910
	.byte	16,1,0,14
	.word	82679
	.byte	10
	.byte	'_Ifx_SRC_GMSC',0,11,136,4,25,40,13
	.byte	'MSC',0
	.word	82688
	.byte	40,2,35,0,0,14
	.word	82693
	.byte	32
	.byte	'Ifx_SRC_GMSC',0,11,139,4,3
	.word	82727
	.byte	15,8
	.word	80021
	.byte	16,1,0,14
	.word	82754
	.byte	10
	.byte	'_Ifx_SRC_GPMU',0,11,142,4,25,8,13
	.byte	'PMU',0
	.word	82763
	.byte	8,2,35,0,0,14
	.word	82768
	.byte	32
	.byte	'Ifx_SRC_GPMU',0,11,145,4,3
	.word	82802
	.byte	15,32
	.word	80079
	.byte	16,0,0,14
	.word	82829
	.byte	10
	.byte	'_Ifx_SRC_GPSI5',0,11,148,4,25,32,13
	.byte	'PSI5',0
	.word	82838
	.byte	32,2,35,0,0,14
	.word	82843
	.byte	32
	.byte	'Ifx_SRC_GPSI5',0,11,151,4,3
	.word	82879
	.byte	15,32
	.word	80139
	.byte	16,0,0,14
	.word	82907
	.byte	10
	.byte	'_Ifx_SRC_GPSI5S',0,11,154,4,25,32,13
	.byte	'PSI5S',0
	.word	82916
	.byte	32,2,35,0,0,14
	.word	82921
	.byte	32
	.byte	'Ifx_SRC_GPSI5S',0,11,157,4,3
	.word	82959
	.byte	15,96
	.word	80201
	.byte	16,3,0,14
	.word	82988
	.byte	10
	.byte	'_Ifx_SRC_GQSPI',0,11,160,4,25,96,13
	.byte	'QSPI',0
	.word	82997
	.byte	96,2,35,0,0,14
	.word	83002
	.byte	32
	.byte	'Ifx_SRC_GQSPI',0,11,163,4,3
	.word	83038
	.byte	15,4
	.word	80321
	.byte	16,0,0,14
	.word	83066
	.byte	10
	.byte	'_Ifx_SRC_GSCR',0,11,166,4,25,4,13
	.byte	'SCR',0
	.word	83075
	.byte	4,2,35,0,0,14
	.word	83080
	.byte	32
	.byte	'Ifx_SRC_GSCR',0,11,169,4,3
	.word	83114
	.byte	14
	.word	80379
	.byte	10
	.byte	'_Ifx_SRC_GSCU',0,11,172,4,25,20,13
	.byte	'SCU',0
	.word	83141
	.byte	20,2,35,0,0,14
	.word	83146
	.byte	32
	.byte	'Ifx_SRC_GSCU',0,11,175,4,3
	.word	83180
	.byte	15,24
	.word	80460
	.byte	16,0,0,14
	.word	83207
	.byte	10
	.byte	'_Ifx_SRC_GSENT',0,11,178,4,25,24,13
	.byte	'SENT',0
	.word	83216
	.byte	24,2,35,0,0,14
	.word	83221
	.byte	32
	.byte	'Ifx_SRC_GSENT',0,11,181,4,3
	.word	83257
	.byte	15,12
	.word	80520
	.byte	16,0,0,14
	.word	83285
	.byte	10
	.byte	'_Ifx_SRC_GSMU',0,11,184,4,25,12,13
	.byte	'SMU',0
	.word	83294
	.byte	12,2,35,0,0,14
	.word	83299
	.byte	32
	.byte	'Ifx_SRC_GSMU',0,11,187,4,3
	.word	83333
	.byte	15,16
	.word	80578
	.byte	16,1,0,14
	.word	83360
	.byte	10
	.byte	'_Ifx_SRC_GSTM',0,11,190,4,25,16,13
	.byte	'STM',0
	.word	83369
	.byte	16,2,35,0,0,14
	.word	83374
	.byte	32
	.byte	'Ifx_SRC_GSTM',0,11,193,4,3
	.word	83408
	.byte	15,64
	.word	80754
	.byte	16,3,0,14
	.word	83435
	.byte	15,224,1
	.word	641
	.byte	16,223,1,0,15,32
	.word	80650
	.byte	16,1,0,14
	.word	83460
	.byte	10
	.byte	'_Ifx_SRC_GVADC',0,11,196,4,25,192,2,13
	.byte	'G',0
	.word	83444
	.byte	64,2,35,0,13
	.byte	'reserved_40',0
	.word	83449
	.byte	224,1,2,35,64,13
	.byte	'CG',0
	.word	83469
	.byte	32,3,35,160,2,0,14
	.word	83474
	.byte	32
	.byte	'Ifx_SRC_GVADC',0,11,201,4,3
	.word	83543
	.byte	14
	.word	80856
	.byte	10
	.byte	'_Ifx_SRC_GXBAR',0,11,204,4,25,4,13
	.byte	'XBAR',0
	.word	83571
	.byte	4,2,35,0,0,14
	.word	83576
	.byte	32
	.byte	'Ifx_SRC_GXBAR',0,11,207,4,3
	.word	83612
	.byte	32
	.byte	'Ifx_STM_ACCEN0_Bits',0,13,79,3
	.word	13466
	.byte	32
	.byte	'Ifx_STM_ACCEN1_Bits',0,13,85,3
	.word	13377
	.byte	32
	.byte	'Ifx_STM_CAP_Bits',0,13,91,3
	.word	11907
	.byte	32
	.byte	'Ifx_STM_CAPSV_Bits',0,13,97,3
	.word	12784
	.byte	32
	.byte	'Ifx_STM_CLC_Bits',0,13,107,3
	.word	11030
	.byte	32
	.byte	'Ifx_STM_CMCON_Bits',0,13,120,3
	.word	12085
	.byte	32
	.byte	'Ifx_STM_CMP_Bits',0,13,126,3
	.word	11994
	.byte	32
	.byte	'Ifx_STM_ICR_Bits',0,13,139,1,3
	.word	12316
	.byte	32
	.byte	'Ifx_STM_ID_Bits',0,13,147,1,3
	.word	11186
	.byte	32
	.byte	'Ifx_STM_ISCR_Bits',0,13,157,1,3
	.word	12533
	.byte	32
	.byte	'Ifx_STM_KRST0_Bits',0,13,165,1,3
	.word	13254
	.byte	32
	.byte	'Ifx_STM_KRST1_Bits',0,13,172,1,3
	.word	13150
	.byte	32
	.byte	'Ifx_STM_KRSTCLR_Bits',0,13,179,1,3
	.word	13044
	.byte	32
	.byte	'Ifx_STM_OCS_Bits',0,13,189,1,3
	.word	12884
	.byte	32
	.byte	'Ifx_STM_TIM0_Bits',0,13,195,1,3
	.word	11308
	.byte	32
	.byte	'Ifx_STM_TIM0SV_Bits',0,13,201,1,3
	.word	12697
	.byte	32
	.byte	'Ifx_STM_TIM1_Bits',0,13,207,1,3
	.word	11393
	.byte	32
	.byte	'Ifx_STM_TIM2_Bits',0,13,213,1,3
	.word	11478
	.byte	32
	.byte	'Ifx_STM_TIM3_Bits',0,13,219,1,3
	.word	11563
	.byte	32
	.byte	'Ifx_STM_TIM4_Bits',0,13,225,1,3
	.word	11649
	.byte	32
	.byte	'Ifx_STM_TIM5_Bits',0,13,231,1,3
	.word	11735
	.byte	32
	.byte	'Ifx_STM_TIM6_Bits',0,13,237,1,3
	.word	11821
	.byte	32
	.byte	'Ifx_STM_ACCEN0',0,13,250,1,3
	.word	13995
	.byte	32
	.byte	'Ifx_STM_ACCEN1',0,13,130,2,3
	.word	13426
	.byte	32
	.byte	'Ifx_STM_CAP',0,13,138,2,3
	.word	11954
	.byte	32
	.byte	'Ifx_STM_CAPSV',0,13,146,2,3
	.word	12833
	.byte	32
	.byte	'Ifx_STM_CLC',0,13,154,2,3
	.word	11146
	.byte	32
	.byte	'Ifx_STM_CMCON',0,13,162,2,3
	.word	12276
	.byte	32
	.byte	'Ifx_STM_CMP',0,13,170,2,3
	.word	12036
	.byte	32
	.byte	'Ifx_STM_ICR',0,13,178,2,3
	.word	12493
	.byte	32
	.byte	'Ifx_STM_ID',0,13,186,2,3
	.word	11268
	.byte	32
	.byte	'Ifx_STM_ISCR',0,13,194,2,3
	.word	12657
	.byte	32
	.byte	'Ifx_STM_KRST0',0,13,202,2,3
	.word	13337
	.byte	32
	.byte	'Ifx_STM_KRST1',0,13,210,2,3
	.word	13214
	.byte	32
	.byte	'Ifx_STM_KRSTCLR',0,13,218,2,3
	.word	13110
	.byte	32
	.byte	'Ifx_STM_OCS',0,13,226,2,3
	.word	13004
	.byte	32
	.byte	'Ifx_STM_TIM0',0,13,234,2,3
	.word	11353
	.byte	32
	.byte	'Ifx_STM_TIM0SV',0,13,242,2,3
	.word	12744
	.byte	32
	.byte	'Ifx_STM_TIM1',0,13,250,2,3
	.word	11438
	.byte	32
	.byte	'Ifx_STM_TIM2',0,13,130,3,3
	.word	11523
	.byte	32
	.byte	'Ifx_STM_TIM3',0,13,138,3,3
	.word	11609
	.byte	32
	.byte	'Ifx_STM_TIM4',0,13,146,3,3
	.word	11695
	.byte	32
	.byte	'Ifx_STM_TIM5',0,13,154,3,3
	.word	11781
	.byte	32
	.byte	'Ifx_STM_TIM6',0,13,162,3,3
	.word	11867
	.byte	14
	.word	14035
	.byte	32
	.byte	'Ifx_STM',0,13,201,3,3
	.word	84723
	.byte	10
	.byte	'_Ifx_FLASH_ACCEN0_Bits',0,29,45,16,4,11
	.byte	'EN0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	641
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	641
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	641
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	641
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	641
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	641
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	641
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	641
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	641
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	641
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	641
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	641
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_FLASH_ACCEN0_Bits',0,29,79,3
	.word	84745
	.byte	10
	.byte	'_Ifx_FLASH_ACCEN1_Bits',0,29,82,16,4,11
	.byte	'reserved_0',0,4
	.word	466
	.byte	32,0,2,35,0,0,32
	.byte	'Ifx_FLASH_ACCEN1_Bits',0,29,85,3
	.word	85306
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_CFG_Bits',0,29,88,16,4,11
	.byte	'SEL',0,1
	.word	641
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	641
	.byte	2,0,2,35,0,11
	.byte	'CLR',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'DIS',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	466
	.byte	22,0,2,35,0,0,32
	.byte	'Ifx_FLASH_CBAB_CFG_Bits',0,29,95,3
	.word	85387
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_STAT_Bits',0,29,98,16,4,11
	.byte	'VLD0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'VLD1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'VLD2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'VLD3',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'VLD4',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'VLD5',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'VLD6',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'VLD7',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'VLD8',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'VLD9',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	466
	.byte	22,0,2,35,0,0,32
	.byte	'Ifx_FLASH_CBAB_STAT_Bits',0,29,111,3
	.word	85540
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_TOP_Bits',0,29,114,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	466
	.byte	19,8,2,35,0,11
	.byte	'ERR',0,1
	.word	641
	.byte	6,2,2,35,3,11
	.byte	'VLD',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'CLR',0,1
	.word	641
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_FLASH_CBAB_TOP_Bits',0,29,121,3
	.word	85788
	.byte	10
	.byte	'_Ifx_FLASH_COMM0_Bits',0,29,124,16,4,11
	.byte	'STATUS',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	466
	.byte	24,0,2,35,0,0,32
	.byte	'Ifx_FLASH_COMM0_Bits',0,29,128,1,3
	.word	85934
	.byte	10
	.byte	'_Ifx_FLASH_COMM1_Bits',0,29,131,1,16,4,11
	.byte	'STATUS',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'DATA',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	658
	.byte	16,0,2,35,2,0,32
	.byte	'Ifx_FLASH_COMM1_Bits',0,29,136,1,3
	.word	86032
	.byte	10
	.byte	'_Ifx_FLASH_COMM2_Bits',0,29,139,1,16,4,11
	.byte	'STATUS',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'DATA',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	658
	.byte	16,0,2,35,2,0,32
	.byte	'Ifx_FLASH_COMM2_Bits',0,29,144,1,3
	.word	86148
	.byte	10
	.byte	'_Ifx_FLASH_ECCRD_Bits',0,29,147,1,16,4,11
	.byte	'RCODE',0,4
	.word	466
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	658
	.byte	8,2,2,35,2,11
	.byte	'EDCERRINJ',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'ECCORDIS',0,1
	.word	641
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_FLASH_ECCRD_Bits',0,29,153,1,3
	.word	86264
	.byte	10
	.byte	'_Ifx_FLASH_ECCRP_Bits',0,29,156,1,16,4,11
	.byte	'RCODE',0,4
	.word	466
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	658
	.byte	8,2,2,35,2,11
	.byte	'EDCERRINJ',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'ECCORDIS',0,1
	.word	641
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_FLASH_ECCRP_Bits',0,29,162,1,3
	.word	86404
	.byte	10
	.byte	'_Ifx_FLASH_ECCW_Bits',0,29,165,1,16,4,11
	.byte	'WCODE',0,4
	.word	466
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	658
	.byte	8,2,2,35,2,11
	.byte	'DECENCDIS',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'PECENCDIS',0,1
	.word	641
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_FLASH_ECCW_Bits',0,29,171,1,3
	.word	86544
	.byte	10
	.byte	'_Ifx_FLASH_FCON_Bits',0,29,174,1,16,4,11
	.byte	'WSPFLASH',0,1
	.word	641
	.byte	4,4,2,35,0,11
	.byte	'WSECPF',0,1
	.word	641
	.byte	2,2,2,35,0,11
	.byte	'WSDFLASH',0,2
	.word	658
	.byte	6,4,2,35,0,11
	.byte	'WSECDF',0,1
	.word	641
	.byte	3,1,2,35,1,11
	.byte	'IDLE',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'ESLDIS',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'SLEEP',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'NSAFECC',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'STALL',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'RES21',0,1
	.word	641
	.byte	2,2,2,35,2,11
	.byte	'RES23',0,1
	.word	641
	.byte	2,0,2,35,2,11
	.byte	'VOPERM',0,1
	.word	641
	.byte	1,7,2,35,3,11
	.byte	'SQERM',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'PROERM',0,1
	.word	641
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	641
	.byte	3,2,2,35,3,11
	.byte	'PR5V',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'EOBM',0,1
	.word	641
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_FLASH_FCON_Bits',0,29,193,1,3
	.word	86683
	.byte	10
	.byte	'_Ifx_FLASH_FPRO_Bits',0,29,196,1,16,4,11
	.byte	'PROINP',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'PRODISP',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'PROIND',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'PRODISD',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'PROINHSMCOTP',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'RES5',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'PROINOTP',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'RES7',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'PROINDBG',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'PRODISDBG',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'PROINHSM',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	641
	.byte	5,0,2,35,1,11
	.byte	'DCFP',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'DDFP',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'DDFPX',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'DDFD',0,1
	.word	641
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	641
	.byte	1,2,2,35,2,11
	.byte	'ENPE',0,1
	.word	641
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	641
	.byte	8,0,2,35,3,0,32
	.byte	'Ifx_FLASH_FPRO_Bits',0,29,218,1,3
	.word	87045
	.byte	10
	.byte	'_Ifx_FLASH_FSR_Bits',0,29,221,1,16,4,11
	.byte	'FABUSY',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'D0BUSY',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'RES1',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'P0BUSY',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'P1BUSY',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'RES5',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'RES6',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'PROG',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'ERASE',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'PFPAGE',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'DFPAGE',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'OPER',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'SQER',0,1
	.word	641
	.byte	1,3,2,35,1,11
	.byte	'PROER',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'PFSBER',0,1
	.word	641
	.byte	1,1,2,35,1,11
	.byte	'PFDBER',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'PFMBER',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'RES17',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'DFSBER',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'DFDBER',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'DFTBER',0,1
	.word	641
	.byte	1,3,2,35,2,11
	.byte	'DFMBER',0,1
	.word	641
	.byte	1,2,2,35,2,11
	.byte	'SRIADDERR',0,1
	.word	641
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	658
	.byte	2,7,2,35,2,11
	.byte	'PVER',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'EVER',0,1
	.word	641
	.byte	1,5,2,35,3,11
	.byte	'SPND',0,1
	.word	641
	.byte	1,4,2,35,3,11
	.byte	'SLM',0,1
	.word	641
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	641
	.byte	1,2,2,35,3,11
	.byte	'ORIER',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	641
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_FLASH_FSR_Bits',0,29,254,1,3
	.word	87486
	.byte	10
	.byte	'_Ifx_FLASH_ID_Bits',0,29,129,2,16,4,11
	.byte	'MODREV',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	658
	.byte	16,0,2,35,2,0,32
	.byte	'Ifx_FLASH_ID_Bits',0,29,134,2,3
	.word	88092
	.byte	10
	.byte	'_Ifx_FLASH_MARD_Bits',0,29,137,2,16,4,11
	.byte	'HMARGIN',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'SELD0',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'SPND',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'SPNDERR',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,2
	.word	658
	.byte	10,1,2,35,0,11
	.byte	'TRAPDIS',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	658
	.byte	16,0,2,35,2,0,32
	.byte	'Ifx_FLASH_MARD_Bits',0,29,147,2,3
	.word	88203
	.byte	10
	.byte	'_Ifx_FLASH_MARP_Bits',0,29,150,2,16,4,11
	.byte	'SELP0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'SELP1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'RES2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'RES3',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	658
	.byte	11,1,2,35,0,11
	.byte	'TRAPDIS',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	658
	.byte	16,0,2,35,2,0,32
	.byte	'Ifx_FLASH_MARP_Bits',0,29,159,2,3
	.word	88417
	.byte	10
	.byte	'_Ifx_FLASH_PROCOND_Bits',0,29,162,2,16,4,11
	.byte	'L',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'NSAFECC',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'RAMIN',0,1
	.word	641
	.byte	2,4,2,35,0,11
	.byte	'RAMINSEL',0,1
	.word	641
	.byte	4,0,2,35,0,11
	.byte	'OSCCFG',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'MODE',0,1
	.word	641
	.byte	2,5,2,35,1,11
	.byte	'APREN',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'CAP0EN',0,1
	.word	641
	.byte	1,3,2,35,1,11
	.byte	'CAP1EN',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'CAP2EN',0,1
	.word	641
	.byte	1,1,2,35,1,11
	.byte	'CAP3EN',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'ESR0CNT',0,2
	.word	658
	.byte	12,4,2,35,2,11
	.byte	'RES29',0,1
	.word	641
	.byte	2,2,2,35,3,11
	.byte	'RES30',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'RPRO',0,1
	.word	641
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_FLASH_PROCOND_Bits',0,29,179,2,3
	.word	88604
	.byte	10
	.byte	'_Ifx_FLASH_PROCONDBG_Bits',0,29,182,2,16,4,11
	.byte	'OCDSDIS',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'DBGIFLCK',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'EDM',0,1
	.word	641
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	466
	.byte	28,0,2,35,0,0,32
	.byte	'Ifx_FLASH_PROCONDBG_Bits',0,29,188,2,3
	.word	88928
	.byte	10
	.byte	'_Ifx_FLASH_PROCONHSM_Bits',0,29,191,2,16,4,11
	.byte	'HSMDBGDIS',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'DBGIFLCK',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'TSTIFLCK',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'HSMTSTDIS',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'RES15',0,2
	.word	658
	.byte	12,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	658
	.byte	16,0,2,35,2,0,32
	.byte	'Ifx_FLASH_PROCONHSM_Bits',0,29,199,2,3
	.word	89071
	.byte	10
	.byte	'_Ifx_FLASH_PROCONHSMCOTP_Bits',0,29,202,2,16,4,11
	.byte	'HSMBOOTEN',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'SSWWAIT',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'HSMDX',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'HSM6X',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'HSM16X',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'HSM17X',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'S6ROM',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'HSMENPINS',0,2
	.word	658
	.byte	2,7,2,35,0,11
	.byte	'HSMENRES',0,1
	.word	641
	.byte	2,5,2,35,1,11
	.byte	'DESTDBG',0,1
	.word	641
	.byte	2,3,2,35,1,11
	.byte	'BLKFLAN',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	641
	.byte	2,0,2,35,1,11
	.byte	'S16ROM',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'S17ROM',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	658
	.byte	14,0,2,35,2,0,32
	.byte	'Ifx_FLASH_PROCONHSMCOTP_Bits',0,29,219,2,3
	.word	89260
	.byte	10
	.byte	'_Ifx_FLASH_PROCONOTP_Bits',0,29,222,2,16,4,11
	.byte	'S0ROM',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'S1ROM',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'S2ROM',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'S3ROM',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'S4ROM',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'S5ROM',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'S6ROM',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'S7ROM',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'S8ROM',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'S9ROM',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'S10ROM',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'S11ROM',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'S12ROM',0,1
	.word	641
	.byte	1,3,2,35,1,11
	.byte	'S13ROM',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'S14ROM',0,1
	.word	641
	.byte	1,1,2,35,1,11
	.byte	'S15ROM',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'S16ROM',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'S17ROM',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'S18ROM',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'S19ROM',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'S20ROM',0,1
	.word	641
	.byte	1,3,2,35,2,11
	.byte	'S21ROM',0,1
	.word	641
	.byte	1,2,2,35,2,11
	.byte	'S22ROM',0,1
	.word	641
	.byte	1,1,2,35,2,11
	.byte	'S23ROM',0,1
	.word	641
	.byte	1,0,2,35,2,11
	.byte	'S24ROM',0,1
	.word	641
	.byte	1,7,2,35,3,11
	.byte	'S25ROM',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'S26ROM',0,1
	.word	641
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	641
	.byte	2,3,2,35,3,11
	.byte	'BML',0,1
	.word	641
	.byte	2,1,2,35,3,11
	.byte	'TP',0,1
	.word	641
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_FLASH_PROCONOTP_Bits',0,29,254,2,3
	.word	89623
	.byte	10
	.byte	'_Ifx_FLASH_PROCONP_Bits',0,29,129,3,16,4,11
	.byte	'S0L',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'S1L',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'S2L',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'S3L',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'S4L',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'S5L',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'S6L',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'S7L',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'S8L',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'S9L',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'S10L',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'S11L',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'S12L',0,1
	.word	641
	.byte	1,3,2,35,1,11
	.byte	'S13L',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'S14L',0,1
	.word	641
	.byte	1,1,2,35,1,11
	.byte	'S15L',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'S16L',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'S17L',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'S18L',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'S19L',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'S20L',0,1
	.word	641
	.byte	1,3,2,35,2,11
	.byte	'S21L',0,1
	.word	641
	.byte	1,2,2,35,2,11
	.byte	'S22L',0,1
	.word	641
	.byte	1,1,2,35,2,11
	.byte	'S23L',0,1
	.word	641
	.byte	1,0,2,35,2,11
	.byte	'S24L',0,1
	.word	641
	.byte	1,7,2,35,3,11
	.byte	'S25L',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'S26L',0,1
	.word	641
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	641
	.byte	4,1,2,35,3,11
	.byte	'RPRO',0,1
	.word	641
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_FLASH_PROCONP_Bits',0,29,160,3,3
	.word	90218
	.byte	10
	.byte	'_Ifx_FLASH_PROCONWOP_Bits',0,29,163,3,16,4,11
	.byte	'S0WOP',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'S1WOP',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'S2WOP',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'S3WOP',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'S4WOP',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'S5WOP',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'S6WOP',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'S7WOP',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'S8WOP',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'S9WOP',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'S10WOP',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'S11WOP',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'S12WOP',0,1
	.word	641
	.byte	1,3,2,35,1,11
	.byte	'S13WOP',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'S14WOP',0,1
	.word	641
	.byte	1,1,2,35,1,11
	.byte	'S15WOP',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'S16WOP',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'S17WOP',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'S18WOP',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'S19WOP',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'S20WOP',0,1
	.word	641
	.byte	1,3,2,35,2,11
	.byte	'S21WOP',0,1
	.word	641
	.byte	1,2,2,35,2,11
	.byte	'S22WOP',0,1
	.word	641
	.byte	1,1,2,35,2,11
	.byte	'S23WOP',0,1
	.word	641
	.byte	1,0,2,35,2,11
	.byte	'S24WOP',0,1
	.word	641
	.byte	1,7,2,35,3,11
	.byte	'S25WOP',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'S26WOP',0,1
	.word	641
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	641
	.byte	4,1,2,35,3,11
	.byte	'DATM',0,1
	.word	641
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_FLASH_PROCONWOP_Bits',0,29,194,3,3
	.word	90742
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG0_Bits',0,29,197,3,16,4,11
	.byte	'TAG',0,1
	.word	641
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	466
	.byte	26,0,2,35,0,0,32
	.byte	'Ifx_FLASH_RDB_CFG0_Bits',0,29,201,3,3
	.word	91324
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG1_Bits',0,29,204,3,16,4,11
	.byte	'TAG',0,1
	.word	641
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	466
	.byte	26,0,2,35,0,0,32
	.byte	'Ifx_FLASH_RDB_CFG1_Bits',0,29,208,3,3
	.word	91426
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG2_Bits',0,29,211,3,16,4,11
	.byte	'TAG',0,1
	.word	641
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	466
	.byte	26,0,2,35,0,0,32
	.byte	'Ifx_FLASH_RDB_CFG2_Bits',0,29,215,3,3
	.word	91528
	.byte	10
	.byte	'_Ifx_FLASH_RRAD_Bits',0,29,218,3,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	3,5,2,35,0,11
	.byte	'ADD',0,4
	.word	466
	.byte	29,0,2,35,0,0,32
	.byte	'Ifx_FLASH_RRAD_Bits',0,29,222,3,3
	.word	91630
	.byte	10
	.byte	'_Ifx_FLASH_RRCT_Bits',0,29,225,3,16,4,11
	.byte	'STRT',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'STP',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'BUSY',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'DONE',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'ERR',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	641
	.byte	3,0,2,35,0,11
	.byte	'EOBM',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	641
	.byte	7,0,2,35,1,11
	.byte	'CNT',0,2
	.word	658
	.byte	16,0,2,35,2,0,32
	.byte	'Ifx_FLASH_RRCT_Bits',0,29,236,3,3
	.word	91724
	.byte	10
	.byte	'_Ifx_FLASH_RRD0_Bits',0,29,239,3,16,4,11
	.byte	'DATA',0,4
	.word	466
	.byte	32,0,2,35,0,0,32
	.byte	'Ifx_FLASH_RRD0_Bits',0,29,242,3,3
	.word	91934
	.byte	10
	.byte	'_Ifx_FLASH_RRD1_Bits',0,29,245,3,16,4,11
	.byte	'DATA',0,4
	.word	466
	.byte	32,0,2,35,0,0,32
	.byte	'Ifx_FLASH_RRD1_Bits',0,29,248,3,3
	.word	92007
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_CFG_Bits',0,29,251,3,16,4,11
	.byte	'SEL',0,1
	.word	641
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	641
	.byte	2,0,2,35,0,11
	.byte	'CLR',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'DIS',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	466
	.byte	22,0,2,35,0,0,32
	.byte	'Ifx_FLASH_UBAB_CFG_Bits',0,29,130,4,3
	.word	92080
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_STAT_Bits',0,29,133,4,16,4,11
	.byte	'VLD0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	466
	.byte	31,0,2,35,0,0,32
	.byte	'Ifx_FLASH_UBAB_STAT_Bits',0,29,137,4,3
	.word	92235
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_TOP_Bits',0,29,140,4,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	466
	.byte	19,8,2,35,0,11
	.byte	'ERR',0,1
	.word	641
	.byte	6,2,2,35,3,11
	.byte	'VLD',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'CLR',0,1
	.word	641
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_FLASH_UBAB_TOP_Bits',0,29,147,4,3
	.word	92340
	.byte	12,29,155,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	84745
	.byte	4,2,35,0,0,32
	.byte	'Ifx_FLASH_ACCEN0',0,29,160,4,3
	.word	92488
	.byte	12,29,163,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	85306
	.byte	4,2,35,0,0,32
	.byte	'Ifx_FLASH_ACCEN1',0,29,168,4,3
	.word	92554
	.byte	12,29,171,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	85387
	.byte	4,2,35,0,0,32
	.byte	'Ifx_FLASH_CBAB_CFG',0,29,176,4,3
	.word	92620
	.byte	12,29,179,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	85540
	.byte	4,2,35,0,0,32
	.byte	'Ifx_FLASH_CBAB_STAT',0,29,184,4,3
	.word	92688
	.byte	12,29,187,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	85788
	.byte	4,2,35,0,0,32
	.byte	'Ifx_FLASH_CBAB_TOP',0,29,192,4,3
	.word	92757
	.byte	12,29,195,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	85934
	.byte	4,2,35,0,0,32
	.byte	'Ifx_FLASH_COMM0',0,29,200,4,3
	.word	92825
	.byte	12,29,203,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	86032
	.byte	4,2,35,0,0,32
	.byte	'Ifx_FLASH_COMM1',0,29,208,4,3
	.word	92890
	.byte	12,29,211,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	86148
	.byte	4,2,35,0,0,32
	.byte	'Ifx_FLASH_COMM2',0,29,216,4,3
	.word	92955
	.byte	12,29,219,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	86264
	.byte	4,2,35,0,0,32
	.byte	'Ifx_FLASH_ECCRD',0,29,224,4,3
	.word	93020
	.byte	12,29,227,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	86404
	.byte	4,2,35,0,0,32
	.byte	'Ifx_FLASH_ECCRP',0,29,232,4,3
	.word	93085
	.byte	12,29,235,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	86544
	.byte	4,2,35,0,0,32
	.byte	'Ifx_FLASH_ECCW',0,29,240,4,3
	.word	93150
	.byte	12,29,243,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	86683
	.byte	4,2,35,0,0,32
	.byte	'Ifx_FLASH_FCON',0,29,248,4,3
	.word	93214
	.byte	12,29,251,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	87045
	.byte	4,2,35,0,0,32
	.byte	'Ifx_FLASH_FPRO',0,29,128,5,3
	.word	93278
	.byte	12,29,131,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	87486
	.byte	4,2,35,0,0,32
	.byte	'Ifx_FLASH_FSR',0,29,136,5,3
	.word	93342
	.byte	12,29,139,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	88092
	.byte	4,2,35,0,0,32
	.byte	'Ifx_FLASH_ID',0,29,144,5,3
	.word	93405
	.byte	12,29,147,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	88203
	.byte	4,2,35,0,0,32
	.byte	'Ifx_FLASH_MARD',0,29,152,5,3
	.word	93467
	.byte	12,29,155,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	88417
	.byte	4,2,35,0,0,32
	.byte	'Ifx_FLASH_MARP',0,29,160,5,3
	.word	93531
	.byte	12,29,163,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	88604
	.byte	4,2,35,0,0,32
	.byte	'Ifx_FLASH_PROCOND',0,29,168,5,3
	.word	93595
	.byte	12,29,171,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	88928
	.byte	4,2,35,0,0,32
	.byte	'Ifx_FLASH_PROCONDBG',0,29,176,5,3
	.word	93662
	.byte	12,29,179,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	89071
	.byte	4,2,35,0,0,32
	.byte	'Ifx_FLASH_PROCONHSM',0,29,184,5,3
	.word	93731
	.byte	12,29,187,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	89260
	.byte	4,2,35,0,0,32
	.byte	'Ifx_FLASH_PROCONHSMCOTP',0,29,192,5,3
	.word	93800
	.byte	12,29,195,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	89623
	.byte	4,2,35,0,0,32
	.byte	'Ifx_FLASH_PROCONOTP',0,29,200,5,3
	.word	93873
	.byte	12,29,203,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	90218
	.byte	4,2,35,0,0,32
	.byte	'Ifx_FLASH_PROCONP',0,29,208,5,3
	.word	93942
	.byte	12,29,211,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	90742
	.byte	4,2,35,0,0,32
	.byte	'Ifx_FLASH_PROCONWOP',0,29,216,5,3
	.word	94009
	.byte	12,29,219,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	91324
	.byte	4,2,35,0,0,32
	.byte	'Ifx_FLASH_RDB_CFG0',0,29,224,5,3
	.word	94078
	.byte	12,29,227,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	91426
	.byte	4,2,35,0,0,32
	.byte	'Ifx_FLASH_RDB_CFG1',0,29,232,5,3
	.word	94146
	.byte	12,29,235,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	91528
	.byte	4,2,35,0,0,32
	.byte	'Ifx_FLASH_RDB_CFG2',0,29,240,5,3
	.word	94214
	.byte	12,29,243,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	91630
	.byte	4,2,35,0,0,32
	.byte	'Ifx_FLASH_RRAD',0,29,248,5,3
	.word	94282
	.byte	12,29,251,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	91724
	.byte	4,2,35,0,0,32
	.byte	'Ifx_FLASH_RRCT',0,29,128,6,3
	.word	94346
	.byte	12,29,131,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	91934
	.byte	4,2,35,0,0,32
	.byte	'Ifx_FLASH_RRD0',0,29,136,6,3
	.word	94410
	.byte	12,29,139,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	92007
	.byte	4,2,35,0,0,32
	.byte	'Ifx_FLASH_RRD1',0,29,144,6,3
	.word	94474
	.byte	12,29,147,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	92080
	.byte	4,2,35,0,0,32
	.byte	'Ifx_FLASH_UBAB_CFG',0,29,152,6,3
	.word	94538
	.byte	12,29,155,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	92235
	.byte	4,2,35,0,0,32
	.byte	'Ifx_FLASH_UBAB_STAT',0,29,160,6,3
	.word	94606
	.byte	12,29,163,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	92340
	.byte	4,2,35,0,0,32
	.byte	'Ifx_FLASH_UBAB_TOP',0,29,168,6,3
	.word	94675
	.byte	10
	.byte	'_Ifx_FLASH_CBAB',0,29,179,6,25,12,13
	.byte	'CFG',0
	.word	92620
	.byte	4,2,35,0,13
	.byte	'STAT',0
	.word	92688
	.byte	4,2,35,4,13
	.byte	'TOP',0
	.word	92757
	.byte	4,2,35,8,0,14
	.word	94743
	.byte	32
	.byte	'Ifx_FLASH_CBAB',0,29,184,6,3
	.word	94806
	.byte	10
	.byte	'_Ifx_FLASH_RDB',0,29,187,6,25,12,13
	.byte	'CFG0',0
	.word	94078
	.byte	4,2,35,0,13
	.byte	'CFG1',0
	.word	94146
	.byte	4,2,35,4,13
	.byte	'CFG2',0
	.word	94214
	.byte	4,2,35,8,0,14
	.word	94835
	.byte	32
	.byte	'Ifx_FLASH_RDB',0,29,192,6,3
	.word	94899
	.byte	10
	.byte	'_Ifx_FLASH_UBAB',0,29,195,6,25,12,13
	.byte	'CFG',0
	.word	94538
	.byte	4,2,35,0,13
	.byte	'STAT',0
	.word	94606
	.byte	4,2,35,4,13
	.byte	'TOP',0
	.word	94675
	.byte	4,2,35,8,0,14
	.word	94927
	.byte	32
	.byte	'Ifx_FLASH_UBAB',0,29,200,6,3
	.word	94990
	.byte	32
	.byte	'IfxScuCcu_PllStepsFunctionHook',0,7,148,1,16
	.word	205
	.byte	20,7,212,5,9,8,13
	.byte	'value',0
	.word	10342
	.byte	4,2,35,0,13
	.byte	'mask',0
	.word	10342
	.byte	4,2,35,4,0,32
	.byte	'IfxScuCcu_CcuconRegConfig',0,7,216,5,3
	.word	95059
	.byte	20,7,221,5,9,8,13
	.byte	'pDivider',0
	.word	641
	.byte	1,2,35,0,13
	.byte	'nDivider',0
	.word	641
	.byte	1,2,35,1,13
	.byte	'k2Initial',0
	.word	641
	.byte	1,2,35,2,13
	.byte	'waitTime',0
	.word	262
	.byte	4,2,35,4,0,32
	.byte	'IfxScuCcu_InitialStepConfig',0,7,227,5,3
	.word	95130
	.byte	20,7,231,5,9,12,13
	.byte	'k2Step',0
	.word	641
	.byte	1,2,35,0,13
	.byte	'waitTime',0
	.word	262
	.byte	4,2,35,2,13
	.byte	'hookFunction',0
	.word	95019
	.byte	4,2,35,8,0,32
	.byte	'IfxScuCcu_PllStepsConfig',0,7,236,5,3
	.word	95247
	.byte	3
	.word	202
	.byte	20,7,244,5,9,48,13
	.byte	'ccucon0',0
	.word	95059
	.byte	8,2,35,0,13
	.byte	'ccucon1',0
	.word	95059
	.byte	8,2,35,8,13
	.byte	'ccucon2',0
	.word	95059
	.byte	8,2,35,16,13
	.byte	'ccucon5',0
	.word	95059
	.byte	8,2,35,24,13
	.byte	'ccucon6',0
	.word	95059
	.byte	8,2,35,32,13
	.byte	'ccucon7',0
	.word	95059
	.byte	8,2,35,40,0,32
	.byte	'IfxScuCcu_ClockDistributionConfig',0,7,252,5,3
	.word	95349
	.byte	20,7,128,6,9,8,13
	.byte	'value',0
	.word	10342
	.byte	4,2,35,0,13
	.byte	'mask',0
	.word	10342
	.byte	4,2,35,4,0,32
	.byte	'IfxScuCcu_FlashWaitstateConfig',0,7,132,6,3
	.word	95501
	.byte	3
	.word	95247
	.byte	20,7,137,6,9,16,13
	.byte	'numOfPllDividerSteps',0
	.word	641
	.byte	1,2,35,0,13
	.byte	'pllDividerStep',0
	.word	95577
	.byte	4,2,35,4,13
	.byte	'pllInitialStep',0
	.word	95130
	.byte	8,2,35,8,0,32
	.byte	'IfxScuCcu_SysPllConfig',0,7,142,6,3
	.word	95582
	.byte	17,8,144,1,9,1,18
	.byte	'IfxCpu_CounterMode_normal',0,0,18
	.byte	'IfxCpu_CounterMode_task',0,1,0,32
	.byte	'IfxCpu_CounterMode',0,8,148,1,3
	.word	95699
	.byte	20,8,160,1,9,6,13
	.byte	'counter',0
	.word	10342
	.byte	4,2,35,0,13
	.byte	'overlfow',0
	.word	641
	.byte	1,2,35,4,0,32
	.byte	'IfxCpu_Counter',0,8,164,1,3
	.word	95788
	.byte	20,8,172,1,9,32,13
	.byte	'instruction',0
	.word	95788
	.byte	6,2,35,0,13
	.byte	'clock',0
	.word	95788
	.byte	6,2,35,6,13
	.byte	'counter1',0
	.word	95788
	.byte	6,2,35,12,13
	.byte	'counter2',0
	.word	95788
	.byte	6,2,35,18,13
	.byte	'counter3',0
	.word	95788
	.byte	6,2,35,24,0,32
	.byte	'IfxCpu_Perf',0,8,179,1,3
	.word	95854
	.byte	32
	.byte	'IfxSrc_Tos',0,22,74,3
	.word	28570
	.byte	32
	.byte	'IfxAsclin_Index',0,19,85,3
	.word	23552
	.byte	17,12,151,1,9,1,18
	.byte	'IfxStm_Comparator_0',0,0,18
	.byte	'IfxStm_Comparator_1',0,1,0,32
	.byte	'IfxStm_Comparator',0,12,155,1,3
	.word	96015
	.byte	17,12,159,1,9,1,18
	.byte	'IfxStm_ComparatorInterrupt_ir0',0,0,18
	.byte	'IfxStm_ComparatorInterrupt_ir1',0,1,0,32
	.byte	'IfxStm_ComparatorInterrupt',0,12,163,1,3
	.word	96093
	.byte	17,12,167,1,9,1,18
	.byte	'IfxStm_ComparatorOffset_0',0,0,18
	.byte	'IfxStm_ComparatorOffset_1',0,1,18
	.byte	'IfxStm_ComparatorOffset_2',0,2,18
	.byte	'IfxStm_ComparatorOffset_3',0,3,18
	.byte	'IfxStm_ComparatorOffset_4',0,4,18
	.byte	'IfxStm_ComparatorOffset_5',0,5,18
	.byte	'IfxStm_ComparatorOffset_6',0,6,18
	.byte	'IfxStm_ComparatorOffset_7',0,7,18
	.byte	'IfxStm_ComparatorOffset_8',0,8,18
	.byte	'IfxStm_ComparatorOffset_9',0,9,18
	.byte	'IfxStm_ComparatorOffset_10',0,10,18
	.byte	'IfxStm_ComparatorOffset_11',0,11,18
	.byte	'IfxStm_ComparatorOffset_12',0,12,18
	.byte	'IfxStm_ComparatorOffset_13',0,13,18
	.byte	'IfxStm_ComparatorOffset_14',0,14,18
	.byte	'IfxStm_ComparatorOffset_15',0,15,18
	.byte	'IfxStm_ComparatorOffset_16',0,16,18
	.byte	'IfxStm_ComparatorOffset_17',0,17,18
	.byte	'IfxStm_ComparatorOffset_18',0,18,18
	.byte	'IfxStm_ComparatorOffset_19',0,19,18
	.byte	'IfxStm_ComparatorOffset_20',0,20,18
	.byte	'IfxStm_ComparatorOffset_21',0,21,18
	.byte	'IfxStm_ComparatorOffset_22',0,22,18
	.byte	'IfxStm_ComparatorOffset_23',0,23,18
	.byte	'IfxStm_ComparatorOffset_24',0,24,18
	.byte	'IfxStm_ComparatorOffset_25',0,25,18
	.byte	'IfxStm_ComparatorOffset_26',0,26,18
	.byte	'IfxStm_ComparatorOffset_27',0,27,18
	.byte	'IfxStm_ComparatorOffset_28',0,28,18
	.byte	'IfxStm_ComparatorOffset_29',0,29,18
	.byte	'IfxStm_ComparatorOffset_30',0,30,18
	.byte	'IfxStm_ComparatorOffset_31',0,31,0,32
	.byte	'IfxStm_ComparatorOffset',0,12,201,1,3
	.word	96202
	.byte	17,12,205,1,9,1,18
	.byte	'IfxStm_ComparatorSize_1Bit',0,0,18
	.byte	'IfxStm_ComparatorSize_2Bits',0,1,18
	.byte	'IfxStm_ComparatorSize_3Bits',0,2,18
	.byte	'IfxStm_ComparatorSize_4Bits',0,3,18
	.byte	'IfxStm_ComparatorSize_5Bits',0,4,18
	.byte	'IfxStm_ComparatorSize_6Bits',0,5,18
	.byte	'IfxStm_ComparatorSize_7Bits',0,6,18
	.byte	'IfxStm_ComparatorSize_8Bits',0,7,18
	.byte	'IfxStm_ComparatorSize_9Bits',0,8,18
	.byte	'IfxStm_ComparatorSize_10Bits',0,9,18
	.byte	'IfxStm_ComparatorSize_11Bits',0,10,18
	.byte	'IfxStm_ComparatorSize_12Bits',0,11,18
	.byte	'IfxStm_ComparatorSize_13Bits',0,12,18
	.byte	'IfxStm_ComparatorSize_14Bits',0,13,18
	.byte	'IfxStm_ComparatorSize_15Bits',0,14,18
	.byte	'IfxStm_ComparatorSize_16Bits',0,15,18
	.byte	'IfxStm_ComparatorSize_17Bits',0,16,18
	.byte	'IfxStm_ComparatorSize_18Bits',0,17,18
	.byte	'IfxStm_ComparatorSize_19Bits',0,18,18
	.byte	'IfxStm_ComparatorSize_20Bits',0,19,18
	.byte	'IfxStm_ComparatorSize_21Bits',0,20,18
	.byte	'IfxStm_ComparatorSize_22Bits',0,21,18
	.byte	'IfxStm_ComparatorSize_23Bits',0,22,18
	.byte	'IfxStm_ComparatorSize_24Bits',0,23,18
	.byte	'IfxStm_ComparatorSize_25Bits',0,24,18
	.byte	'IfxStm_ComparatorSize_26Bits',0,25,18
	.byte	'IfxStm_ComparatorSize_27Bits',0,26,18
	.byte	'IfxStm_ComparatorSize_28Bits',0,27,18
	.byte	'IfxStm_ComparatorSize_29Bits',0,28,18
	.byte	'IfxStm_ComparatorSize_30Bits',0,29,18
	.byte	'IfxStm_ComparatorSize_31Bits',0,30,18
	.byte	'IfxStm_ComparatorSize_32Bits',0,31,0,32
	.byte	'IfxStm_ComparatorSize',0,12,239,1,3
	.word	97160
	.byte	17,12,244,1,9,1,18
	.byte	'IfxStm_SleepMode_enable',0,0,18
	.byte	'IfxStm_SleepMode_disable',0,1,0,32
	.byte	'IfxStm_SleepMode',0,12,248,1,3
	.word	98180
	.byte	17,12,252,1,9,1,18
	.byte	'IfxStm_SuspendMode_none',0,0,18
	.byte	'IfxStm_SuspendMode_hard',0,1,18
	.byte	'IfxStm_SuspendMode_soft',0,2,0,32
	.byte	'IfxStm_SuspendMode',0,12,129,2,3
	.word	98266
	.byte	32
	.byte	'_iob_flag_t',0,30,82,25
	.word	658
	.byte	7
	.byte	'char',0,1,6,32
	.byte	'int8',0,31,54,29
	.word	98399
	.byte	32
	.byte	'int16',0,31,55,29
	.word	22196
	.byte	32
	.byte	'int32',0,31,56,29
	.word	482
	.byte	32
	.byte	'int64',0,31,57,29
	.word	14676
	.byte	31
	.word	28877
	.byte	32
	.byte	'IfxAsclin_Cts_In',0,23,64,3
	.word	98462
	.byte	31
	.word	28938
	.byte	32
	.byte	'IfxAsclin_Rx_In',0,23,72,3
	.word	98492
	.byte	31
	.word	28999
	.byte	32
	.byte	'IfxAsclin_Rts_Out',0,23,80,3
	.word	98521
	.byte	20,23,83,15,16,13
	.byte	'module',0
	.word	21379
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	28719
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	9486
	.byte	1,2,35,12,0,31
	.word	98552
	.byte	32
	.byte	'IfxAsclin_Sclk_Out',0,23,88,3
	.word	98603
	.byte	20,23,91,15,16,13
	.byte	'module',0
	.word	21379
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	28719
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	9486
	.byte	1,2,35,12,0,31
	.word	98635
	.byte	32
	.byte	'IfxAsclin_Slso_Out',0,23,96,3
	.word	98686
	.byte	31
	.word	29060
	.byte	32
	.byte	'IfxAsclin_Tx_Out',0,23,104,3
	.word	98718
	.byte	31
	.word	28938
	.byte	34
	.byte	'IfxAsclin0_RXA_P14_1_IN',0,23,118,28
	.word	98748
	.byte	1,1,31
	.word	28938
	.byte	34
	.byte	'IfxAsclin0_RXB_P15_3_IN',0,23,119,28
	.word	98787
	.byte	1,1,31
	.word	28938
	.byte	34
	.byte	'IfxAsclin1_RXA_P15_1_IN',0,23,120,28
	.word	98826
	.byte	1,1,31
	.word	28938
	.byte	34
	.byte	'IfxAsclin1_RXB_P15_5_IN',0,23,121,28
	.word	98865
	.byte	1,1,31
	.word	28938
	.byte	34
	.byte	'IfxAsclin1_RXC_P20_9_IN',0,23,122,28
	.word	98904
	.byte	1,1,31
	.word	28938
	.byte	34
	.byte	'IfxAsclin1_RXE_P11_10_IN',0,23,124,28
	.word	98943
	.byte	1,1,31
	.word	28938
	.byte	34
	.byte	'IfxAsclin1_RXF_P33_13_IN',0,23,125,28
	.word	98983
	.byte	1,1,31
	.word	28938
	.byte	34
	.byte	'IfxAsclin1_RXG_P02_3_IN',0,23,126,28
	.word	99023
	.byte	1,1,31
	.word	28938
	.byte	34
	.byte	'IfxAsclin2_RXA_P14_3_IN',0,23,127,28
	.word	99062
	.byte	1,1,31
	.word	28938
	.byte	34
	.byte	'IfxAsclin2_RXB_P02_1_IN',0,23,128,1,28
	.word	99101
	.byte	1,1,31
	.word	28938
	.byte	34
	.byte	'IfxAsclin2_RXD_P10_6_IN',0,23,129,1,28
	.word	99141
	.byte	1,1,31
	.word	28938
	.byte	34
	.byte	'IfxAsclin2_RXE_P33_8_IN',0,23,130,1,28
	.word	99181
	.byte	1,1,31
	.word	28938
	.byte	34
	.byte	'IfxAsclin2_RXG_P02_0_IN',0,23,131,1,28
	.word	99221
	.byte	1,1,31
	.word	28938
	.byte	34
	.byte	'IfxAsclin3_RXA_P15_7_IN',0,23,132,1,28
	.word	99261
	.byte	1,1,31
	.word	28938
	.byte	34
	.byte	'IfxAsclin3_RXC_P20_3_IN',0,23,133,1,28
	.word	99301
	.byte	1,1,31
	.word	28938
	.byte	34
	.byte	'IfxAsclin3_RXE_P00_1_IN',0,23,135,1,28
	.word	99341
	.byte	1,1,31
	.word	28938
	.byte	34
	.byte	'IfxAsclin3_RXF_P21_6_IN',0,23,136,1,28
	.word	99381
	.byte	1,1,31
	.word	29060
	.byte	34
	.byte	'IfxAsclin0_TX_P14_0_OUT',0,23,168,1,29
	.word	99421
	.byte	1,1,31
	.word	29060
	.byte	34
	.byte	'IfxAsclin0_TX_P14_1_OUT',0,23,169,1,29
	.word	99461
	.byte	1,1,31
	.word	29060
	.byte	34
	.byte	'IfxAsclin0_TX_P15_2_OUT',0,23,170,1,29
	.word	99501
	.byte	1,1,31
	.word	29060
	.byte	34
	.byte	'IfxAsclin0_TX_P15_3_OUT',0,23,171,1,29
	.word	99541
	.byte	1,1,31
	.word	29060
	.byte	34
	.byte	'IfxAsclin1_TX_P02_2_OUT',0,23,172,1,29
	.word	99581
	.byte	1,1,31
	.word	29060
	.byte	34
	.byte	'IfxAsclin1_TX_P11_12_OUT',0,23,173,1,29
	.word	99621
	.byte	1,1,31
	.word	29060
	.byte	34
	.byte	'IfxAsclin1_TX_P15_0_OUT',0,23,175,1,29
	.word	99662
	.byte	1,1,31
	.word	29060
	.byte	34
	.byte	'IfxAsclin1_TX_P15_1_OUT',0,23,176,1,29
	.word	99702
	.byte	1,1,31
	.word	29060
	.byte	34
	.byte	'IfxAsclin1_TX_P15_4_OUT',0,23,177,1,29
	.word	99742
	.byte	1,1,31
	.word	29060
	.byte	34
	.byte	'IfxAsclin1_TX_P15_5_OUT',0,23,178,1,29
	.word	99782
	.byte	1,1,31
	.word	29060
	.byte	34
	.byte	'IfxAsclin1_TX_P20_10_OUT',0,23,179,1,29
	.word	99822
	.byte	1,1,31
	.word	29060
	.byte	34
	.byte	'IfxAsclin1_TX_P33_12_OUT',0,23,180,1,29
	.word	99863
	.byte	1,1,31
	.word	29060
	.byte	34
	.byte	'IfxAsclin1_TX_P33_13_OUT',0,23,181,1,29
	.word	99904
	.byte	1,1,31
	.word	29060
	.byte	34
	.byte	'IfxAsclin2_TX_P02_0_OUT',0,23,182,1,29
	.word	99945
	.byte	1,1,31
	.word	29060
	.byte	34
	.byte	'IfxAsclin2_TX_P10_5_OUT',0,23,183,1,29
	.word	99985
	.byte	1,1,31
	.word	29060
	.byte	34
	.byte	'IfxAsclin2_TX_P14_2_OUT',0,23,184,1,29
	.word	100025
	.byte	1,1,31
	.word	29060
	.byte	34
	.byte	'IfxAsclin2_TX_P14_3_OUT',0,23,185,1,29
	.word	100065
	.byte	1,1,31
	.word	29060
	.byte	34
	.byte	'IfxAsclin2_TX_P33_8_OUT',0,23,186,1,29
	.word	100105
	.byte	1,1,31
	.word	29060
	.byte	34
	.byte	'IfxAsclin2_TX_P33_9_OUT',0,23,187,1,29
	.word	100145
	.byte	1,1,31
	.word	29060
	.byte	34
	.byte	'IfxAsclin3_TX_P00_0_OUT',0,23,188,1,29
	.word	100185
	.byte	1,1,31
	.word	29060
	.byte	34
	.byte	'IfxAsclin3_TX_P00_1_OUT',0,23,189,1,29
	.word	100225
	.byte	1,1,31
	.word	29060
	.byte	34
	.byte	'IfxAsclin3_TX_P15_6_OUT',0,23,190,1,29
	.word	100265
	.byte	1,1,31
	.word	29060
	.byte	34
	.byte	'IfxAsclin3_TX_P15_7_OUT',0,23,191,1,29
	.word	100305
	.byte	1,1,31
	.word	29060
	.byte	34
	.byte	'IfxAsclin3_TX_P20_0_OUT',0,23,192,1,29
	.word	100345
	.byte	1,1,31
	.word	29060
	.byte	34
	.byte	'IfxAsclin3_TX_P20_3_OUT',0,23,193,1,29
	.word	100385
	.byte	1,1,31
	.word	29060
	.byte	34
	.byte	'IfxAsclin3_TX_P21_7_OUT',0,23,194,1,29
	.word	100425
	.byte	1,1,17,15,82,9,1,18
	.byte	'IfxAsclin_Checksum_classic',0,0,18
	.byte	'IfxAsclin_Checksum_enhanced',0,1,0,32
	.byte	'IfxAsclin_Checksum',0,15,86,3
	.word	100465
	.byte	17,15,91,9,1,18
	.byte	'IfxAsclin_ChecksumInjection_notWritten',0,0,18
	.byte	'IfxAsclin_ChecksumInjection_written',0,1,0,32
	.byte	'IfxAsclin_ChecksumInjection',0,15,95,3
	.word	100557
	.byte	17,15,101,9,1,18
	.byte	'IfxAsclin_ClockPolarity_idleLow',0,0,18
	.byte	'IfxAsclin_ClockPolarity_idleHigh',0,1,0,32
	.byte	'IfxAsclin_ClockPolarity',0,15,105,3
	.word	100678
	.byte	32
	.byte	'IfxAsclin_ClockSource',0,15,118,3
	.word	29688
	.byte	32
	.byte	'IfxAsclin_CtsInputSelect',0,15,129,1,3
	.word	21720
	.byte	32
	.byte	'IfxAsclin_DataLength',0,15,152,1,3
	.word	26401
	.byte	32
	.byte	'IfxAsclin_FrameMode',0,15,163,1,3
	.word	26142
	.byte	17,15,168,1,9,1,18
	.byte	'IfxAsclin_HeaderResponseSelect_headerAndResponse',0,0,18
	.byte	'IfxAsclin_HeaderResponseSelect_headerOnly',0,1,0,32
	.byte	'IfxAsclin_HeaderResponseSelect',0,15,172,1,3
	.word	100908
	.byte	32
	.byte	'IfxAsclin_IdleDelay',0,15,189,1,3
	.word	25760
	.byte	17,15,195,1,9,1,18
	.byte	'IfxAsclin_LeadDelay_0',0,0,18
	.byte	'IfxAsclin_LeadDelay_1',0,1,18
	.byte	'IfxAsclin_LeadDelay_2',0,2,18
	.byte	'IfxAsclin_LeadDelay_3',0,3,18
	.byte	'IfxAsclin_LeadDelay_4',0,4,18
	.byte	'IfxAsclin_LeadDelay_5',0,5,18
	.byte	'IfxAsclin_LeadDelay_6',0,6,18
	.byte	'IfxAsclin_LeadDelay_7',0,7,0,32
	.byte	'IfxAsclin_LeadDelay',0,15,205,1,3
	.word	101079
	.byte	17,15,210,1,9,1,18
	.byte	'IfxAsclin_LinMode_slave',0,0,18
	.byte	'IfxAsclin_LinMode_master',0,1,0,32
	.byte	'IfxAsclin_LinMode',0,15,214,1,3
	.word	101307
	.byte	17,15,219,1,9,1,18
	.byte	'IfxAsclin_LinResponseTimeoutMode_frameTimeout',0,0,18
	.byte	'IfxAsclin_LinResponseTimeoutMode_responseTimeout',0,1,0,32
	.byte	'IfxAsclin_LinResponseTimeoutMode',0,15,223,1,3
	.word	101394
	.byte	32
	.byte	'IfxAsclin_OversamplingFactor',0,15,243,1,3
	.word	24601
	.byte	32
	.byte	'IfxAsclin_ParityType',0,15,252,1,3
	.word	26339
	.byte	32
	.byte	'IfxAsclin_ReceiveBufferMode',0,15,133,2,3
	.word	28367
	.byte	17,15,138,2,9,1,18
	.byte	'IfxAsclin_RtsCtsPolarity_activeHigh',0,0,18
	.byte	'IfxAsclin_RtsCtsPolarity_activeLow',0,1,0,32
	.byte	'IfxAsclin_RtsCtsPolarity',0,15,142,2,3
	.word	101647
	.byte	32
	.byte	'IfxAsclin_RxFifoInterruptLevel',0,15,165,2,3
	.word	27793
	.byte	32
	.byte	'IfxAsclin_RxFifoOutletWidth',0,15,176,2,3
	.word	27085
	.byte	32
	.byte	'IfxAsclin_RxInputSelect',0,15,191,2,3
	.word	21904
	.byte	32
	.byte	'IfxAsclin_SamplePointPosition',0,15,213,2,3
	.word	25179
	.byte	32
	.byte	'IfxAsclin_SamplesPerBit',0,15,222,2,3
	.word	25110
	.byte	32
	.byte	'IfxAsclin_ShiftDirection',0,15,232,2,3
	.word	26260
	.byte	17,15,238,2,9,1,18
	.byte	'IfxAsclin_SlavePolarity_idleLow',0,0,18
	.byte	'IfxAsclin_SlavePolarity_idlehigh',0,1,0,32
	.byte	'IfxAsclin_SlavePolarity',0,15,242,2,3
	.word	101979
	.byte	17,15,247,2,9,1,18
	.byte	'IfxAsclin_SleepMode_enable',0,0,18
	.byte	'IfxAsclin_SleepMode_disable',0,1,0,32
	.byte	'IfxAsclin_SleepMode',0,15,251,2,3
	.word	102088
	.byte	32
	.byte	'IfxAsclin_Status',0,15,131,3,3
	.word	24087
	.byte	32
	.byte	'IfxAsclin_StopBit',0,15,146,3,3
	.word	25959
	.byte	17,15,150,3,9,1,18
	.byte	'IfxAsclin_SuspendMode_none',0,0,18
	.byte	'IfxAsclin_SuspendMode_hard',0,1,18
	.byte	'IfxAsclin_SuspendMode_soft',0,2,0,32
	.byte	'IfxAsclin_SuspendMode',0,15,155,3,3
	.word	102236
	.byte	32
	.byte	'IfxAsclin_TxFifoInletWidth',0,15,166,3,3
	.word	26954
	.byte	32
	.byte	'IfxAsclin_TxFifoInterruptLevel',0,15,189,3,3
	.word	27220
	.byte	32
	.byte	'Ifx_Fifo_Shared',0,17,66,3
	.word	22221
	.byte	32
	.byte	'Ifx_Fifo',0,17,83,3
	.word	22312
	.byte	32
	.byte	'IfxStdIf_InterfaceDriver',0,32,118,15
	.word	380
	.byte	3
	.word	22196
	.byte	35
	.word	641
	.byte	1,1,22
	.word	380
	.byte	22
	.word	380
	.byte	22
	.word	102511
	.byte	22
	.word	14676
	.byte	0,3
	.word	102516
	.byte	32
	.byte	'IfxStdIf_DPipe_Write',0,33,92,19
	.word	102544
	.byte	32
	.byte	'IfxStdIf_DPipe_Read',0,33,107,19
	.word	102544
	.byte	35
	.word	22209
	.byte	1,1,22
	.word	380
	.byte	0,3
	.word	102606
	.byte	32
	.byte	'IfxStdIf_DPipe_GetReadCount',0,33,115,18
	.word	102619
	.byte	14
	.word	641
	.byte	3
	.word	102660
	.byte	35
	.word	102665
	.byte	1,1,22
	.word	380
	.byte	0,3
	.word	102670
	.byte	32
	.byte	'IfxStdIf_DPipe_GetReadEvent',0,33,123,36
	.word	102683
	.byte	32
	.byte	'IfxStdIf_DPipe_GetWriteCount',0,33,147,1,18
	.word	102619
	.byte	3
	.word	102670
	.byte	32
	.byte	'IfxStdIf_DPipe_GetWriteEvent',0,33,155,1,37
	.word	102762
	.byte	35
	.word	641
	.byte	1,1,22
	.word	380
	.byte	22
	.word	22196
	.byte	22
	.word	14676
	.byte	0,3
	.word	102805
	.byte	32
	.byte	'IfxStdIf_DPipe_CanReadCount',0,33,166,1,19
	.word	102828
	.byte	32
	.byte	'IfxStdIf_DPipe_CanWriteCount',0,33,177,1,19
	.word	102828
	.byte	35
	.word	641
	.byte	1,1,22
	.word	380
	.byte	22
	.word	14676
	.byte	0,3
	.word	102908
	.byte	32
	.byte	'IfxStdIf_DPipe_FlushTx',0,33,186,1,19
	.word	102926
	.byte	36,1,1,22
	.word	380
	.byte	0,3
	.word	102963
	.byte	32
	.byte	'IfxStdIf_DPipe_ClearTx',0,33,200,1,16
	.word	102972
	.byte	32
	.byte	'IfxStdIf_DPipe_ClearRx',0,33,193,1,16
	.word	102972
	.byte	32
	.byte	'IfxStdIf_DPipe_OnReceive',0,33,208,1,16
	.word	102972
	.byte	32
	.byte	'IfxStdIf_DPipe_OnTransmit',0,33,215,1,16
	.word	102972
	.byte	32
	.byte	'IfxStdIf_DPipe_OnError',0,33,222,1,16
	.word	102972
	.byte	35
	.word	10342
	.byte	1,1,22
	.word	380
	.byte	0,3
	.word	103142
	.byte	32
	.byte	'IfxStdIf_DPipe_GetSendCount',0,33,131,1,18
	.word	103155
	.byte	35
	.word	14676
	.byte	1,1,22
	.word	380
	.byte	0,3
	.word	103197
	.byte	32
	.byte	'IfxStdIf_DPipe_GetTxTimeStamp',0,33,139,1,24
	.word	103210
	.byte	32
	.byte	'IfxStdIf_DPipe_ResetSendCount',0,33,229,1,16
	.word	102972
	.byte	10
	.byte	'IfxStdIf_DPipe_',0,33,233,1,8,76,13
	.byte	'driver',0
	.word	102478
	.byte	4,2,35,0,13
	.byte	'txDisabled',0
	.word	641
	.byte	1,2,35,4,13
	.byte	'write',0
	.word	102549
	.byte	4,2,35,8,13
	.byte	'read',0
	.word	102578
	.byte	4,2,35,12,13
	.byte	'getReadCount',0
	.word	102624
	.byte	4,2,35,16,13
	.byte	'getReadEvent',0
	.word	102688
	.byte	4,2,35,20,13
	.byte	'getWriteCount',0
	.word	102724
	.byte	4,2,35,24,13
	.byte	'getWriteEvent',0
	.word	102767
	.byte	4,2,35,28,13
	.byte	'canReadCount',0
	.word	102833
	.byte	4,2,35,32,13
	.byte	'canWriteCount',0
	.word	102870
	.byte	4,2,35,36,13
	.byte	'flushTx',0
	.word	102931
	.byte	4,2,35,40,13
	.byte	'clearTx',0
	.word	102977
	.byte	4,2,35,44,13
	.byte	'clearRx',0
	.word	103009
	.byte	4,2,35,48,13
	.byte	'onReceive',0
	.word	103041
	.byte	4,2,35,52,13
	.byte	'onTransmit',0
	.word	103075
	.byte	4,2,35,56,13
	.byte	'onError',0
	.word	103110
	.byte	4,2,35,60,13
	.byte	'getSendCount',0
	.word	103160
	.byte	4,2,35,64,13
	.byte	'getTxTimeStamp',0
	.word	103215
	.byte	4,2,35,68,13
	.byte	'resetSendCount',0
	.word	103254
	.byte	4,2,35,72,0,32
	.byte	'IfxStdIf_DPipe',0,33,71,32
	.word	103293
	.byte	3
	.word	374
	.byte	3
	.word	102516
	.byte	3
	.word	102516
	.byte	3
	.word	102606
	.byte	3
	.word	102670
	.byte	3
	.word	102606
	.byte	3
	.word	102670
	.byte	3
	.word	102805
	.byte	3
	.word	102805
	.byte	3
	.word	102908
	.byte	3
	.word	102963
	.byte	3
	.word	102963
	.byte	3
	.word	102963
	.byte	3
	.word	102963
	.byte	3
	.word	102963
	.byte	3
	.word	103142
	.byte	3
	.word	103197
	.byte	3
	.word	102963
	.byte	14
	.word	641
	.byte	3
	.word	103806
	.byte	32
	.byte	'IfxStdIf_DPipe_WriteEvent',0,33,73,32
	.word	103811
	.byte	32
	.byte	'IfxStdIf_DPipe_ReadEvent',0,33,74,32
	.word	103811
	.byte	32
	.byte	'IfxAsclin_Asc_ErrorFlags',0,20,131,2,3
	.word	24169
	.byte	32
	.byte	'IfxAsclin_Asc_BaudRate',0,20,142,2,3
	.word	25044
	.byte	32
	.byte	'IfxAsclin_Asc_BitTimingControl',0,20,150,2,3
	.word	25702
	.byte	32
	.byte	'IfxAsclin_Asc_FifoControl',0,20,161,2,3
	.word	28450
	.byte	32
	.byte	'IfxAsclin_Asc_FrameControl',0,20,174,2,3
	.word	26815
	.byte	32
	.byte	'IfxAsclin_Asc_InterruptConfig',0,20,184,2,3
	.word	28629
	.byte	32
	.byte	'IfxAsclin_Asc_Pins',0,20,199,2,3
	.word	29536
	.byte	32
	.byte	'IfxAsclin_Asc_ErrorFlagsUnion',0,20,209,2,3
	.word	24300
	.byte	32
	.byte	'IfxAsclin_Asc',0,20,226,2,3
	.word	24417
	.byte	32
	.byte	'IfxAsclin_Asc_Config',0,20,255,2,3
	.word	29913
	.byte	32
	.byte	'uart_tx_pin_enum',0,24,74,2
	.word	30414
	.byte	32
	.byte	'uart_rx_pin_enum',0,24,100,2
	.word	30866
	.byte	32
	.byte	'uart_index_enum',0,24,109,2
	.word	30357
.L423:
	.byte	15,37
	.word	641
	.byte	16,36,0,0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L184:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,21,0,54,15,39,12,0,0,3,15,0,73,19,0,0,4,46,1,3,8,32,13
	.byte	58,15,59,15,57,15,54,15,39,12,0,0,5,5,0,3,8,58,15,59,15,57,15,73,19,0,0,6,11,0,0,0,7,36,0,3,8,11,15,62
	.byte	15,0,0,8,46,1,3,8,32,13,58,15,59,15,57,15,73,19,54,15,39,12,0,0,9,59,0,3,8,0,0,10,19,1,3,8,58,15,59,15
	.byte	57,15,11,15,0,0,11,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,12,23,1,58,15,59,15,57,15,11,15,0,0,13,13
	.byte	0,3,8,73,19,11,15,56,9,0,0,14,53,0,73,19,0,0,15,1,1,11,15,73,19,0,0,16,33,0,47,15,0,0,17,4,1,58,15,59
	.byte	15,57,15,11,15,0,0,18,40,0,3,8,28,13,0,0,19,11,1,0,0,20,19,1,58,15,59,15,57,15,11,15,0,0,21,46,1,3,8,73
	.byte	19,54,15,39,12,63,12,60,12,0,0,22,5,0,73,19,0,0,23,46,0,3,8,54,15,39,12,63,12,60,12,0,0,24,46,1,49,19
	.byte	0,0,25,5,0,49,19,0,0,26,29,1,49,19,0,0,27,11,0,49,19,0,0,28,11,1,49,19,0,0,29,46,1,3,8,58,15,59,15,57
	.byte	15,54,15,39,12,63,12,60,12,0,0,30,46,1,3,8,58,15,59,15,57,15,73,19,54,15,39,12,63,12,60,12,0,0,31,38,0
	.byte	73,19,0,0,32,22,0,3,8,58,15,59,15,57,15,73,19,0,0,33,21,0,54,15,0,0,34,52,0,3,8,58,15,59,15,57,15,73,19
	.byte	63,12,60,12,0,0,35,21,1,73,19,54,15,39,12,0,0,36,21,1,54,15,39,12,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L185:
	.word	.L517-.L516
.L516:
	.half	3
	.word	.L519-.L518
.L518:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc',0
	.byte	'F:\\ADS\\AURIX-Studio-1.10.2\\tools\\Compilers\\Tasking_1.1r8\\ctc\\include\\',0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_Intrinsics.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Scu\\Std\\IfxScuWdt.h',0
	.byte	0,0,0
	.byte	'IfxScu_regdef.h',0,1,0,0
	.byte	'IFXPORT.h',0,2,0,0
	.byte	'IfxPort_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Scu\\Std\\IfxScuCcu.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxCpu_cfg.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Src\\Std\\IfxSrc.h',0
	.byte	0,0,0
	.byte	'IfxSrc_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Stm\\Std\\IfxStm.h',0
	.byte	0,0,0
	.byte	'IfxStm_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\SysSe\\Bsp\\Bsp.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Asclin\\Std\\IfxAsclin.h',0
	.byte	0,0,0
	.byte	'IfxAsclin_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Lib\\DataHandling\\Ifx_Fifo.h',0
	.byte	0,0,0
	.byte	'zf_common_debug.h',0,3,0,0
	.byte	'IFXASCLIN_CFG.h',0,4,0,0
	.byte	'ifxAsclin_Asc.h',0,5,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Ifx_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxSrc_cfg.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_PinMap\\IfxAsclin_PinMap.h',0
	.byte	0,0,0
	.byte	'..\\libraries\\zf_driver\\zf_driver_uart.h',0,0,0,0
	.byte	'IfxCpu_regdef.h',0,1,0,0
	.byte	'../libraries/zf_driver/zf_driver_uart.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Platform_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxScu_cfg.h',0
	.byte	0,0,0
	.byte	'IfxFlash_regdef.h',0,1,0,0
	.byte	'stdio.h',0,6,0,0
	.byte	'zf_common_typedef.h',0,3,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\StdIf\\IfxStdIf.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\StdIf\\IfxStdIf_DPipe.h',0
	.byte	0,0,0,0
.L519:
.L517:
	.sdecl	'.debug_info',debug,cluster('uart_write_byte')
	.sect	'.debug_info'
.L186:
	.word	353
	.half	3
	.word	.L187
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_uart.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L189,.L188
	.byte	2
	.word	.L182
	.byte	3
	.byte	'uart_write_byte',0,1,137,2,6,1,1,1
	.word	.L165,.L277,.L164
	.byte	4
	.byte	'uart_n',0,1,137,2,39
	.word	.L278,.L279
	.byte	4
	.byte	'dat',0,1,137,2,59
	.word	.L280,.L281
	.byte	5
	.word	.L165,.L277
	.byte	6
	.byte	'uart_handle',0,1,139,2,20
	.word	.L282,.L283
	.byte	7
	.word	.L284,.L285,.L128
	.byte	8
	.word	.L286,.L287
	.byte	9
	.word	.L288,.L285,.L128
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('uart_write_byte')
	.sect	'.debug_abbrev'
.L187:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,5,0,49,16,2,6,0,0,9,11,0,49,16,17,1,18
	.byte	1,0,0,0
	.sdecl	'.debug_line',debug,cluster('uart_write_byte')
	.sect	'.debug_line'
.L188:
	.word	.L521-.L520
.L520:
	.half	3
	.word	.L523-.L522
.L522:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_uart.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Asclin\\Std\\IfxAsclin.h',0
	.byte	0,0,0,0
.L523:
	.byte	5,6,7,0,5,2
	.word	.L165
	.byte	3,136,2,1,5,35,9
	.half	.L524-.L165
	.byte	3,3,1,5,65,9
	.half	.L427-.L524
	.byte	3,2,1,5,51,9
	.half	.L126-.L427
	.byte	1,4,2,5,31,9
	.half	.L285-.L126
	.byte	3,150,14,1,5,5,9
	.half	.L525-.L285
	.byte	1,4,1,5,65,9
	.half	.L128-.L525
	.byte	3,234,113,1,5,33,7,9
	.half	.L526-.L128
	.byte	3,1,1,5,44,9
	.half	.L527-.L526
	.byte	1,5,49,9
	.half	.L528-.L527
	.byte	1,5,1,9
	.half	.L428-.L528
	.byte	3,1,1,7,9
	.half	.L190-.L428
	.byte	0,1,1
.L521:
	.sdecl	'.debug_ranges',debug,cluster('uart_write_byte')
	.sect	'.debug_ranges'
.L189:
	.word	-1,.L165,0,.L190-.L165,0,0
	.sdecl	'.debug_info',debug,cluster('uart_write_buffer')
	.sect	'.debug_info'
.L191:
	.word	311
	.half	3
	.word	.L192
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_uart.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L194,.L193
	.byte	2
	.word	.L182
	.byte	3
	.byte	'uart_write_buffer',0,1,155,2,6,1,1,1
	.word	.L167,.L289,.L166
	.byte	4
	.byte	'uart_n',0,1,155,2,41
	.word	.L278,.L290
	.byte	4
	.byte	'buff',0,1,155,2,62
	.word	.L291,.L292
	.byte	4
	.byte	'len',0,1,155,2,75
	.word	.L293,.L294
	.byte	5
	.word	.L167,.L289
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('uart_write_buffer')
	.sect	'.debug_abbrev'
.L192:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('uart_write_buffer')
	.sect	'.debug_line'
.L193:
	.word	.L530-.L529
.L529:
	.half	3
	.word	.L532-.L531
.L531:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_uart.c',0,0,0,0,0
.L532:
	.byte	5,6,7,0,5,2
	.word	.L167
	.byte	3,154,2,1,5,14,9
	.half	.L431-.L167
	.byte	3,2,1,5,33,9
	.half	.L130-.L431
	.byte	3,2,1,5,12,9
	.half	.L433-.L130
	.byte	3,1,1,5,13,9
	.half	.L533-.L433
	.byte	3,1,1,5,14,9
	.half	.L129-.L533
	.byte	3,124,1,5,1,7,9
	.half	.L534-.L129
	.byte	3,6,1,7,9
	.half	.L195-.L534
	.byte	0,1,1
.L530:
	.sdecl	'.debug_ranges',debug,cluster('uart_write_buffer')
	.sect	'.debug_ranges'
.L194:
	.word	-1,.L167,0,.L195-.L167,0,0
	.sdecl	'.debug_info',debug,cluster('uart_write_string')
	.sect	'.debug_info'
.L196:
	.word	293
	.half	3
	.word	.L197
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_uart.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L199,.L198
	.byte	2
	.word	.L182
	.byte	3
	.byte	'uart_write_string',0,1,174,2,6,1,1,1
	.word	.L169,.L295,.L168
	.byte	4
	.byte	'uart_n',0,1,174,2,41
	.word	.L278,.L296
	.byte	4
	.byte	'str',0,1,174,2,61
	.word	.L297,.L298
	.byte	5
	.word	.L169,.L295
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('uart_write_string')
	.sect	'.debug_abbrev'
.L197:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('uart_write_string')
	.sect	'.debug_line'
.L198:
	.word	.L536-.L535
.L535:
	.half	3
	.word	.L538-.L537
.L537:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_uart.c',0,0,0,0,0
.L538:
	.byte	5,6,7,0,5,2
	.word	.L169
	.byte	3,173,2,1,5,15,9
	.half	.L435-.L169
	.byte	3,2,1,5,33,9
	.half	.L132-.L435
	.byte	3,2,1,5,37,9
	.half	.L539-.L132
	.byte	1,5,11,9
	.half	.L131-.L539
	.byte	3,126,1,5,15,9
	.half	.L540-.L131
	.byte	1,5,1,7,9
	.half	.L541-.L540
	.byte	3,4,1,7,9
	.half	.L200-.L541
	.byte	0,1,1
.L536:
	.sdecl	'.debug_ranges',debug,cluster('uart_write_string')
	.sect	'.debug_ranges'
.L199:
	.word	-1,.L169,0,.L200-.L169,0,0
	.sdecl	'.debug_info',debug,cluster('uart_read_byte')
	.sect	'.debug_info'
.L201:
	.word	363
	.half	3
	.word	.L202
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_uart.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L204,.L203
	.byte	2
	.word	.L182
	.byte	3
	.byte	'uart_read_byte',0,1,190,2,7
	.word	.L299
	.byte	1,1,1
	.word	.L171,.L300,.L170
	.byte	4
	.byte	'uart_n',0,1,190,2,39
	.word	.L278,.L301
	.byte	5
	.word	.L171,.L300
	.byte	6
	.byte	'return_num',0,1,193,2,11
	.word	.L299,.L302
	.byte	6
	.byte	'uart_handle',0,1,194,2,20
	.word	.L282,.L303
	.byte	7
	.word	.L304,.L305,.L135
	.byte	8
	.word	.L306,.L307
	.byte	9
	.word	.L308,.L305,.L135
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('uart_read_byte')
	.sect	'.debug_abbrev'
.L202:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,5,0,49,16,2,6,0,0,9,11,0,49,16,17
	.byte	1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('uart_read_byte')
	.sect	'.debug_line'
.L203:
	.word	.L543-.L542
.L542:
	.half	3
	.word	.L545-.L544
.L544:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_uart.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Asclin\\Std\\IfxAsclin.h',0
	.byte	0,0,0,0
.L545:
	.byte	5,7,7,0,5,2
	.word	.L171
	.byte	3,189,2,1,5,24,9
	.half	.L437-.L171
	.byte	3,3,1,5,22,9
	.half	.L546-.L437
	.byte	1,5,35,9
	.half	.L547-.L546
	.byte	3,2,1,5,65,9
	.half	.L438-.L547
	.byte	3,1,1,5,51,9
	.half	.L133-.L438
	.byte	1,4,2,5,31,9
	.half	.L305-.L133
	.byte	3,170,13,1,5,5,9
	.half	.L548-.L305
	.byte	1,4,1,5,65,9
	.half	.L135-.L548
	.byte	3,214,114,1,5,32,7,9
	.half	.L549-.L135
	.byte	3,1,1,5,43,9
	.half	.L550-.L549
	.byte	1,5,55,9
	.half	.L551-.L550
	.byte	1,5,12,9
	.half	.L439-.L551
	.byte	3,2,1,5,5,9
	.half	.L552-.L439
	.byte	1,5,1,9
	.half	.L136-.L552
	.byte	3,1,1,7,9
	.half	.L205-.L136
	.byte	0,1,1
.L543:
	.sdecl	'.debug_ranges',debug,cluster('uart_read_byte')
	.sect	'.debug_ranges'
.L204:
	.word	-1,.L171,0,.L205-.L171,0,0
	.sdecl	'.debug_info',debug,cluster('uart_query_byte')
	.sect	'.debug_info'
.L206:
	.word	381
	.half	3
	.word	.L207
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_uart.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L209,.L208
	.byte	2
	.word	.L182
	.byte	3
	.byte	'uart_query_byte',0,1,211,2,7
	.word	.L299
	.byte	1,1,1
	.word	.L173,.L309,.L172
	.byte	4
	.byte	'uart_n',0,1,211,2,40
	.word	.L278,.L310
	.byte	4
	.byte	'dat',0,1,211,2,55
	.word	.L311,.L312
	.byte	5
	.word	.L173,.L309
	.byte	6
	.byte	'return_num',0,1,213,2,11
	.word	.L299,.L313
	.byte	6
	.byte	'uart_handle',0,1,214,2,20
	.word	.L282,.L314
	.byte	7
	.word	.L304,.L315,.L137
	.byte	8
	.word	.L306,.L316
	.byte	9
	.word	.L308,.L315,.L137
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('uart_query_byte')
	.sect	'.debug_abbrev'
.L207:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,5,0,49,16,2,6,0,0,9,11,0,49,16,17
	.byte	1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('uart_query_byte')
	.sect	'.debug_line'
.L208:
	.word	.L554-.L553
.L553:
	.half	3
	.word	.L556-.L555
.L555:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_uart.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Asclin\\Std\\IfxAsclin.h',0
	.byte	0,0,0,0
.L556:
	.byte	5,7,7,0,5,2
	.word	.L173
	.byte	3,210,2,1,5,22,9
	.half	.L441-.L173
	.byte	3,2,1,5,35,9
	.half	.L442-.L441
	.byte	3,2,1,5,48,9
	.half	.L440-.L442
	.byte	3,1,1,4,2,5,31,9
	.half	.L315-.L440
	.byte	3,150,13,1,5,5,9
	.half	.L557-.L315
	.byte	1,4,1,9
	.half	.L137-.L557
	.byte	3,234,114,1,5,36,7,9
	.half	.L558-.L137
	.byte	3,2,1,5,51,9
	.half	.L559-.L558
	.byte	1,5,20,9
	.half	.L443-.L559
	.byte	3,1,1,5,5,9
	.half	.L138-.L443
	.byte	3,2,1,5,1,9
	.half	.L139-.L138
	.byte	3,1,1,7,9
	.half	.L210-.L139
	.byte	0,1,1
.L554:
	.sdecl	'.debug_ranges',debug,cluster('uart_query_byte')
	.sect	'.debug_ranges'
.L209:
	.word	-1,.L173,0,.L210-.L173,0,0
	.sdecl	'.debug_info',debug,cluster('uart_tx_interrupt')
	.sect	'.debug_info'
.L211:
	.word	474
	.half	3
	.word	.L212
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_uart.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L214,.L213
	.byte	2
	.word	.L182
	.byte	3
	.byte	'uart_tx_interrupt',0,1,232,2,6,1,1,1
	.word	.L175,.L317,.L174
	.byte	4
	.byte	'uart_n',0,1,232,2,41
	.word	.L278,.L318
	.byte	4
	.byte	'status',0,1,232,2,56
	.word	.L293,.L319
	.byte	5
	.word	.L175,.L317
	.byte	6
	.byte	'asclinSFR',0,1,234,2,22
	.word	.L320,.L321
	.byte	6
	.byte	'src',0,1,235,2,28
	.word	.L322,.L323
	.byte	6
	.byte	'moudle',0,1,236,2,26
	.word	.L320,.L324
	.byte	7
	.word	.L325,.L326,.L327
	.byte	8
	.word	.L328,.L329
	.byte	8
	.word	.L330,.L331
	.byte	9
	.word	.L332,.L326,.L327
	.byte	0,7
	.word	.L333,.L334,.L335
	.byte	8
	.word	.L336,.L337
	.byte	9
	.word	.L338,.L334,.L335
	.byte	0,7
	.word	.L339,.L142,.L143
	.byte	8
	.word	.L340,.L341
	.byte	9
	.word	.L342,.L142,.L143
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('uart_tx_interrupt')
	.sect	'.debug_abbrev'
.L212:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,5,0,49,16,2,6,0,0,9,11,0,49,16,17,1,18
	.byte	1,0,0,0
	.sdecl	'.debug_line',debug,cluster('uart_tx_interrupt')
	.sect	'.debug_line'
.L213:
	.word	.L561-.L560
.L560:
	.half	3
	.word	.L563-.L562
.L562:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_uart.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Asclin\\Std\\IfxAsclin.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Src\\Std\\IfxSrc.h',0
	.byte	0,0,0,0
.L563:
	.byte	5,6,7,0,5,2
	.word	.L175
	.byte	3,231,2,1,5,34,9
	.half	.L448-.L175
	.byte	3,2,1,5,45,9
	.half	.L564-.L448
	.byte	1,5,56,9
	.half	.L449-.L564
	.byte	3,2,1,5,37,9
	.half	.L446-.L449
	.byte	3,2,1,5,50,9
	.half	.L565-.L446
	.byte	1,5,37,9
	.half	.L450-.L565
	.byte	3,1,1,5,52,9
	.half	.L453-.L450
	.byte	3,1,1,4,2,5,34,9
	.half	.L326-.L453
	.byte	3,238,11,1,5,41,7,9
	.half	.L566-.L326
	.byte	1,5,45,9
	.half	.L567-.L566
	.byte	1,5,41,9
	.half	.L140-.L567
	.byte	1,5,26,9
	.half	.L141-.L140
	.byte	1,5,32,9
	.half	.L568-.L141
	.byte	1,4,1,5,5,9
	.half	.L327-.L568
	.byte	3,147,116,1,4,3,5,11,7,9
	.half	.L334-.L327
	.byte	3,157,127,1,5,16,9
	.half	.L569-.L334
	.byte	1,4,1,5,27,9
	.half	.L335-.L569
	.byte	3,229,0,1,4,3,5,11,9
	.half	.L142-.L335
	.byte	3,149,127,1,5,16,9
	.half	.L570-.L142
	.byte	1,4,1,5,1,9
	.half	.L143-.L570
	.byte	3,241,0,1,7,9
	.half	.L215-.L143
	.byte	0,1,1
.L561:
	.sdecl	'.debug_ranges',debug,cluster('uart_tx_interrupt')
	.sect	'.debug_ranges'
.L214:
	.word	-1,.L175,0,.L215-.L175,0,0
	.sdecl	'.debug_info',debug,cluster('uart_rx_interrupt')
	.sect	'.debug_info'
.L216:
	.word	474
	.half	3
	.word	.L217
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_uart.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L219,.L218
	.byte	2
	.word	.L182
	.byte	3
	.byte	'uart_rx_interrupt',0,1,132,3,6,1,1,1
	.word	.L177,.L343,.L176
	.byte	4
	.byte	'uart_n',0,1,132,3,41
	.word	.L278,.L344
	.byte	4
	.byte	'status',0,1,132,3,56
	.word	.L293,.L345
	.byte	5
	.word	.L177,.L343
	.byte	6
	.byte	'asclinSFR',0,1,134,3,22
	.word	.L320,.L346
	.byte	6
	.byte	'src',0,1,135,3,28
	.word	.L322,.L347
	.byte	6
	.byte	'moudle',0,1,136,3,26
	.word	.L320,.L348
	.byte	7
	.word	.L349,.L350,.L351
	.byte	8
	.word	.L352,.L353
	.byte	8
	.word	.L354,.L355
	.byte	9
	.word	.L356,.L350,.L351
	.byte	0,7
	.word	.L333,.L357,.L358
	.byte	8
	.word	.L336,.L359
	.byte	9
	.word	.L338,.L357,.L358
	.byte	0,7
	.word	.L339,.L146,.L147
	.byte	8
	.word	.L340,.L360
	.byte	9
	.word	.L342,.L146,.L147
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('uart_rx_interrupt')
	.sect	'.debug_abbrev'
.L217:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,5,0,49,16,2,6,0,0,9,11,0,49,16,17,1,18
	.byte	1,0,0,0
	.sdecl	'.debug_line',debug,cluster('uart_rx_interrupt')
	.sect	'.debug_line'
.L218:
	.word	.L572-.L571
.L571:
	.half	3
	.word	.L574-.L573
.L573:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_uart.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Asclin\\Std\\IfxAsclin.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Src\\Std\\IfxSrc.h',0
	.byte	0,0,0,0
.L574:
	.byte	5,6,7,0,5,2
	.word	.L177
	.byte	3,131,3,1,5,34,9
	.half	.L456-.L177
	.byte	3,2,1,5,45,9
	.half	.L575-.L456
	.byte	1,5,56,9
	.half	.L457-.L575
	.byte	3,2,1,5,37,9
	.half	.L454-.L457
	.byte	3,2,1,5,50,9
	.half	.L576-.L454
	.byte	1,5,37,9
	.half	.L458-.L576
	.byte	3,1,1,5,52,9
	.half	.L461-.L458
	.byte	3,1,1,4,2,5,34,9
	.half	.L350-.L461
	.byte	3,168,11,1,5,41,7,9
	.half	.L577-.L350
	.byte	1,5,45,9
	.half	.L578-.L577
	.byte	1,5,41,9
	.half	.L144-.L578
	.byte	1,5,26,9
	.half	.L145-.L144
	.byte	1,5,32,9
	.half	.L579-.L145
	.byte	1,4,1,5,5,9
	.half	.L351-.L579
	.byte	3,217,116,1,4,3,5,11,7,9
	.half	.L357-.L351
	.byte	3,129,127,1,5,16,9
	.half	.L580-.L357
	.byte	1,4,1,5,27,9
	.half	.L358-.L580
	.byte	3,129,1,1,4,3,5,11,9
	.half	.L146-.L358
	.byte	3,249,126,1,5,16,9
	.half	.L581-.L146
	.byte	1,4,1,5,1,9
	.half	.L147-.L581
	.byte	3,141,1,1,7,9
	.half	.L220-.L147
	.byte	0,1,1
.L572:
	.sdecl	'.debug_ranges',debug,cluster('uart_rx_interrupt')
	.sect	'.debug_ranges'
.L219:
	.word	-1,.L177,0,.L220-.L177,0,0
	.sdecl	'.debug_info',debug,cluster('uart_sbus_init')
	.sect	'.debug_info'
.L221:
	.word	603
	.half	3
	.word	.L222
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_uart.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L224,.L223
	.byte	2
	.word	.L182
	.byte	3
	.byte	'uart_sbus_init',0,1,161,3,6,1,1,1
	.word	.L179,.L361,.L178
	.byte	4
	.byte	'uartn',0,1,161,3,38
	.word	.L278,.L362
	.byte	4
	.byte	'baud',0,1,161,3,52
	.word	.L293,.L363
	.byte	4
	.byte	'tx_pin',0,1,161,3,75
	.word	.L364,.L365
	.byte	4
	.byte	'rx_pin',0,1,161,3,100
	.word	.L366,.L367
	.byte	5
	.word	.L179,.L361
	.byte	6
	.byte	'interrupt_state',0,1,164,3,13
	.word	.L299,.L368
	.byte	6
	.byte	'moudle',0,1,166,3,26
	.word	.L320,.L369
	.byte	7
	.word	.L370,.L371,.L150
	.byte	8
	.word	.L372,.L371,.L150
	.byte	7
	.word	.L373,.L371,.L149
	.byte	8
	.word	.L374,.L371,.L149
	.byte	6
	.byte	'enabled',0,2,149,5,13
	.word	.L299,.L375
	.byte	7
	.word	.L376,.L371,.L148
	.byte	8
	.word	.L377,.L371,.L148
	.byte	6
	.byte	'reg',0,2,141,5,17
	.word	.L378,.L379
	.byte	0,0,0,0,0,0,5
	.word	.L380,.L361
	.byte	6
	.byte	'pins',0,1,184,3,24
	.word	.L381,.L382
	.byte	7
	.word	.L383,.L384,.L151
	.byte	9
	.word	.L385,.L386
	.byte	8
	.word	.L387,.L384,.L151
	.byte	7
	.word	.L388,.L384,.L151
	.byte	9
	.word	.L389,.L390
	.byte	10
	.word	.L391,.L384,.L151
	.byte	0,0,0,0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('uart_sbus_init')
	.sect	'.debug_abbrev'
.L222:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,11,1,49,16,17,1,18,1,0,0,9,5,0,49,16,2
	.byte	6,0,0,10,11,0,49,16,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('uart_sbus_init')
	.sect	'.debug_line'
.L223:
	.word	.L583-.L582
.L582:
	.half	3
	.word	.L585-.L584
.L584:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_uart.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\SysSe\\Bsp\\Bsp.h',0
	.byte	0,0,0,0
.L585:
	.byte	5,6,7,0,5,2
	.word	.L179
	.byte	3,160,3,1,4,2,5,19,9
	.half	.L371-.L179
	.byte	3,237,1,1,5,17,9
	.half	.L465-.L371
	.byte	3,1,1,5,21,9
	.half	.L466-.L465
	.byte	1,5,5,9
	.half	.L467-.L466
	.byte	1,5,14,9
	.half	.L148-.L467
	.byte	3,8,1,5,10,9
	.half	.L586-.L148
	.byte	3,1,1,5,5,9
	.half	.L587-.L586
	.byte	3,1,1,4,3,9
	.half	.L149-.L587
	.byte	3,213,123,1,4,1,5,56,9
	.half	.L150-.L149
	.byte	3,184,2,1,5,37,9
	.half	.L463-.L150
	.byte	3,2,1,5,50,9
	.half	.L588-.L463
	.byte	1,5,21,9
	.half	.L468-.L588
	.byte	3,2,1,5,33,9
	.half	.L471-.L468
	.byte	3,2,1,5,5,9
	.half	.L473-.L471
	.byte	3,2,1,5,41,9
	.half	.L589-.L473
	.byte	1,5,39,9
	.half	.L590-.L589
	.byte	1,5,5,9
	.half	.L591-.L590
	.byte	3,1,1,5,41,9
	.half	.L592-.L591
	.byte	1,5,39,9
	.half	.L593-.L592
	.byte	1,5,5,9
	.half	.L594-.L593
	.byte	3,1,1,5,41,9
	.half	.L474-.L594
	.byte	1,5,39,9
	.half	.L475-.L474
	.byte	1,5,5,9
	.half	.L595-.L475
	.byte	3,1,1,5,41,9
	.half	.L596-.L595
	.byte	1,5,39,9
	.half	.L597-.L596
	.byte	1,5,5,9
	.half	.L598-.L597
	.byte	3,2,1,5,42,9
	.half	.L599-.L598
	.byte	1,5,39,9
	.half	.L600-.L599
	.byte	1,5,5,9
	.half	.L601-.L600
	.byte	3,1,1,5,41,9
	.half	.L602-.L601
	.byte	1,5,39,9
	.half	.L603-.L602
	.byte	1,5,5,9
	.half	.L604-.L603
	.byte	3,1,1,5,41,9
	.half	.L605-.L604
	.byte	1,5,39,9
	.half	.L606-.L605
	.byte	1,5,5,9
	.half	.L607-.L606
	.byte	3,1,1,5,41,9
	.half	.L608-.L607
	.byte	1,5,39,9
	.half	.L609-.L608
	.byte	1,5,16,9
	.half	.L380-.L609
	.byte	3,3,1,5,14,9
	.half	.L610-.L380
	.byte	1,5,16,9
	.half	.L611-.L610
	.byte	3,1,1,5,14,9
	.half	.L612-.L611
	.byte	1,5,52,9
	.half	.L613-.L612
	.byte	3,1,1,5,72,9
	.half	.L614-.L613
	.byte	1,5,57,9
	.half	.L476-.L614
	.byte	1,5,19,9
	.half	.L479-.L476
	.byte	3,1,1,5,17,9
	.half	.L615-.L479
	.byte	1,5,19,9
	.half	.L616-.L615
	.byte	3,1,1,5,17,9
	.half	.L617-.L616
	.byte	1,5,22,9
	.half	.L618-.L617
	.byte	3,1,1,5,20,9
	.half	.L619-.L618
	.byte	1,5,5,9
	.half	.L620-.L619
	.byte	3,1,1,5,25,9
	.half	.L621-.L620
	.byte	1,5,22,9
	.half	.L622-.L621
	.byte	1,5,46,9
	.half	.L480-.L622
	.byte	3,2,1,5,55,9
	.half	.L482-.L480
	.byte	1,5,30,9
	.half	.L623-.L482
	.byte	3,1,1,9
	.half	.L485-.L623
	.byte	3,1,1,5,23,9
	.half	.L488-.L485
	.byte	3,1,1,4,2,5,5,9
	.half	.L384-.L488
	.byte	3,230,3,1,5,17,7,9
	.half	.L624-.L384
	.byte	3,2,1,4,1,5,1,9
	.half	.L151-.L624
	.byte	3,154,124,1,7,9
	.half	.L225-.L151
	.byte	0,1,1
.L583:
	.sdecl	'.debug_ranges',debug,cluster('uart_sbus_init')
	.sect	'.debug_ranges'
.L224:
	.word	-1,.L179,0,.L225-.L179,0,0
	.sdecl	'.debug_info',debug,cluster('uart_init')
	.sect	'.debug_info'
.L226:
	.word	599
	.half	3
	.word	.L227
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_uart.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L229,.L228
	.byte	2
	.word	.L182
	.byte	3
	.byte	'uart_init',0,1,210,3,6,1,1,1
	.word	.L181,.L392,.L180
	.byte	4
	.byte	'uart_n',0,1,210,3,33
	.word	.L278,.L393
	.byte	4
	.byte	'baud',0,1,210,3,48
	.word	.L293,.L394
	.byte	4
	.byte	'tx_pin',0,1,210,3,71
	.word	.L364,.L395
	.byte	4
	.byte	'rx_pin',0,1,210,3,96
	.word	.L366,.L396
	.byte	5
	.word	.L181,.L392
	.byte	6
	.byte	'interrupt_state',0,1,212,3,13
	.word	.L299,.L397
	.byte	6
	.byte	'moudle',0,1,214,3,26
	.word	.L320,.L398
	.byte	7
	.word	.L370,.L399,.L154
	.byte	8
	.word	.L372,.L399,.L154
	.byte	7
	.word	.L373,.L399,.L153
	.byte	8
	.word	.L374,.L399,.L153
	.byte	6
	.byte	'enabled',0,2,149,5,13
	.word	.L299,.L400
	.byte	7
	.word	.L376,.L399,.L152
	.byte	8
	.word	.L377,.L399,.L152
	.byte	6
	.byte	'reg',0,2,141,5,17
	.word	.L378,.L401
	.byte	0,0,0,0,0,0,5
	.word	.L402,.L392
	.byte	6
	.byte	'pins',0,1,227,3,24
	.word	.L381,.L403
	.byte	7
	.word	.L383,.L404,.L155
	.byte	9
	.word	.L385,.L405
	.byte	8
	.word	.L387,.L404,.L155
	.byte	7
	.word	.L388,.L404,.L155
	.byte	9
	.word	.L389,.L406
	.byte	10
	.word	.L391,.L404,.L155
	.byte	0,0,0,0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('uart_init')
	.sect	'.debug_abbrev'
.L227:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,11,1,49,16,17,1,18,1,0,0,9,5,0,49,16,2
	.byte	6,0,0,10,11,0,49,16,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('uart_init')
	.sect	'.debug_line'
.L228:
	.word	.L626-.L625
.L625:
	.half	3
	.word	.L628-.L627
.L627:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_uart.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\SysSe\\Bsp\\Bsp.h',0
	.byte	0,0,0,0
.L628:
	.byte	5,6,7,0,5,2
	.word	.L181
	.byte	3,209,3,1,4,2,5,19,9
	.half	.L399-.L181
	.byte	3,188,1,1,5,17,9
	.half	.L492-.L399
	.byte	3,1,1,5,21,9
	.half	.L493-.L492
	.byte	1,5,5,9
	.half	.L494-.L493
	.byte	1,5,14,9
	.half	.L152-.L494
	.byte	3,8,1,5,10,9
	.half	.L629-.L152
	.byte	3,1,1,5,5,9
	.half	.L630-.L629
	.byte	3,1,1,4,3,9
	.half	.L153-.L630
	.byte	3,213,123,1,4,1,5,56,9
	.half	.L154-.L153
	.byte	3,232,2,1,5,37,9
	.half	.L490-.L154
	.byte	3,2,1,5,50,9
	.half	.L631-.L490
	.byte	1,5,21,9
	.half	.L495-.L631
	.byte	3,2,1,5,33,9
	.half	.L498-.L495
	.byte	3,2,1,5,5,9
	.half	.L500-.L498
	.byte	3,2,1,5,41,9
	.half	.L632-.L500
	.byte	1,5,39,9
	.half	.L633-.L632
	.byte	1,5,5,9
	.half	.L634-.L633
	.byte	3,1,1,5,41,9
	.half	.L635-.L634
	.byte	1,5,39,9
	.half	.L636-.L635
	.byte	1,5,5,9
	.half	.L637-.L636
	.byte	3,1,1,5,41,9
	.half	.L501-.L637
	.byte	1,5,39,9
	.half	.L502-.L501
	.byte	1,5,5,9
	.half	.L638-.L502
	.byte	3,1,1,5,41,9
	.half	.L639-.L638
	.byte	1,5,39,9
	.half	.L640-.L639
	.byte	1,5,16,9
	.half	.L402-.L640
	.byte	3,3,1,5,14,9
	.half	.L641-.L402
	.byte	1,5,16,9
	.half	.L642-.L641
	.byte	3,1,1,5,14,9
	.half	.L643-.L642
	.byte	1,5,53,9
	.half	.L644-.L643
	.byte	3,1,1,5,73,9
	.half	.L645-.L644
	.byte	1,5,58,9
	.half	.L503-.L645
	.byte	1,5,19,9
	.half	.L506-.L503
	.byte	3,1,1,5,17,9
	.half	.L646-.L506
	.byte	1,5,19,9
	.half	.L647-.L646
	.byte	3,1,1,5,17,9
	.half	.L648-.L647
	.byte	1,5,22,9
	.half	.L649-.L648
	.byte	3,1,1,5,20,9
	.half	.L650-.L649
	.byte	1,5,5,9
	.half	.L651-.L650
	.byte	3,1,1,5,25,9
	.half	.L652-.L651
	.byte	1,5,22,9
	.half	.L653-.L652
	.byte	1,5,46,9
	.half	.L507-.L653
	.byte	3,2,1,5,56,9
	.half	.L509-.L507
	.byte	1,5,31,9
	.half	.L654-.L509
	.byte	3,1,1,9
	.half	.L512-.L654
	.byte	3,1,1,5,23,9
	.half	.L515-.L512
	.byte	3,1,1,4,2,5,5,9
	.half	.L404-.L515
	.byte	3,187,3,1,5,17,7,9
	.half	.L655-.L404
	.byte	3,2,1,4,1,5,1,9
	.half	.L155-.L655
	.byte	3,196,124,1,7,9
	.half	.L230-.L155
	.byte	0,1,1
.L626:
	.sdecl	'.debug_ranges',debug,cluster('uart_init')
	.sect	'.debug_ranges'
.L229:
	.word	-1,.L181,0,.L230-.L181,0,0
	.sdecl	'.debug_info',debug,cluster('uart_set_interrupt_priority')
	.sect	'.debug_info'
.L231:
	.word	283
	.half	3
	.word	.L232
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_uart.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L234,.L233
	.byte	2
	.word	.L182
	.byte	3
	.byte	'uart_set_interrupt_priority',0,1,76,6,1,1,1
	.word	.L157,.L407,.L156
	.byte	4
	.byte	'uartn',0,1,76,51
	.word	.L278,.L408
	.byte	5
	.word	.L157,.L407
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('uart_set_interrupt_priority')
	.sect	'.debug_abbrev'
.L232:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('uart_set_interrupt_priority')
	.sect	'.debug_line'
.L233:
	.word	.L657-.L656
.L656:
	.half	3
	.word	.L659-.L658
.L658:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_uart.c',0,0,0,0,0
.L659:
	.byte	5,14,7,0,5,2
	.word	.L157
	.byte	3,207,0,1,9
	.half	.L660-.L157
	.byte	3,7,1,9
	.half	.L661-.L660
	.byte	3,7,1,9
	.half	.L662-.L661
	.byte	3,7,1,5,13,9
	.half	.L2-.L662
	.byte	3,109,1,5,51,9
	.half	.L663-.L2
	.byte	1,5,49,9
	.half	.L664-.L663
	.byte	1,5,13,9
	.half	.L665-.L664
	.byte	3,1,1,5,51,9
	.half	.L666-.L665
	.byte	1,5,49,9
	.half	.L667-.L666
	.byte	1,5,13,9
	.half	.L668-.L667
	.byte	3,1,1,5,51,9
	.half	.L669-.L668
	.byte	1,5,49,9
	.half	.L670-.L669
	.byte	1,5,13,9
	.half	.L671-.L670
	.byte	3,1,1,5,51,9
	.half	.L672-.L671
	.byte	1,5,49,9
	.half	.L673-.L672
	.byte	1,5,10,9
	.half	.L674-.L673
	.byte	3,1,1,5,13,9
	.half	.L3-.L674
	.byte	3,3,1,5,51,9
	.half	.L675-.L3
	.byte	1,5,49,9
	.half	.L676-.L675
	.byte	1,5,13,9
	.half	.L677-.L676
	.byte	3,1,1,5,51,9
	.half	.L678-.L677
	.byte	1,5,49,9
	.half	.L679-.L678
	.byte	1,5,13,9
	.half	.L680-.L679
	.byte	3,1,1,5,51,9
	.half	.L681-.L680
	.byte	1,5,49,9
	.half	.L682-.L681
	.byte	1,5,13,9
	.half	.L683-.L682
	.byte	3,1,1,5,51,9
	.half	.L684-.L683
	.byte	1,5,49,9
	.half	.L685-.L684
	.byte	1,5,10,9
	.half	.L686-.L685
	.byte	3,1,1,5,13,9
	.half	.L4-.L686
	.byte	3,3,1,5,51,9
	.half	.L687-.L4
	.byte	1,5,49,9
	.half	.L688-.L687
	.byte	1,5,13,9
	.half	.L689-.L688
	.byte	3,1,1,5,51,9
	.half	.L690-.L689
	.byte	1,5,49,9
	.half	.L691-.L690
	.byte	1,5,13,9
	.half	.L692-.L691
	.byte	3,1,1,5,51,9
	.half	.L693-.L692
	.byte	1,5,49,9
	.half	.L694-.L693
	.byte	1,5,13,9
	.half	.L695-.L694
	.byte	3,1,1,5,51,9
	.half	.L696-.L695
	.byte	1,5,49,9
	.half	.L697-.L696
	.byte	1,5,10,9
	.half	.L698-.L697
	.byte	3,1,1,5,13,9
	.half	.L5-.L698
	.byte	3,3,1,5,51,9
	.half	.L699-.L5
	.byte	1,5,49,9
	.half	.L700-.L699
	.byte	1,5,13,9
	.half	.L701-.L700
	.byte	3,1,1,5,51,9
	.half	.L702-.L701
	.byte	1,5,49,9
	.half	.L703-.L702
	.byte	1,5,13,9
	.half	.L704-.L703
	.byte	3,1,1,5,51,9
	.half	.L705-.L704
	.byte	1,5,49,9
	.half	.L706-.L705
	.byte	1,5,13,9
	.half	.L707-.L706
	.byte	3,1,1,5,51,9
	.half	.L708-.L707
	.byte	1,5,49,9
	.half	.L709-.L708
	.byte	1,5,10,9
	.half	.L710-.L709
	.byte	3,1,1,5,18,9
	.half	.L6-.L710
	.byte	3,1,1,5,1,9
	.half	.L7-.L6
	.byte	3,2,1,7,9
	.half	.L235-.L7
	.byte	0,1,1
.L657:
	.sdecl	'.debug_ranges',debug,cluster('uart_set_interrupt_priority')
	.sect	'.debug_ranges'
.L234:
	.word	-1,.L157,0,.L235-.L157,0,0
	.sdecl	'.debug_info',debug,cluster('uart_set_buffer')
	.sect	'.debug_info'
.L236:
	.word	271
	.half	3
	.word	.L237
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_uart.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L239,.L238
	.byte	2
	.word	.L182
	.byte	3
	.byte	'uart_set_buffer',0,1,119,6,1,1,1
	.word	.L159,.L409,.L158
	.byte	4
	.byte	'uartn',0,1,119,39
	.word	.L278,.L410
	.byte	5
	.word	.L159,.L409
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('uart_set_buffer')
	.sect	'.debug_abbrev'
.L237:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('uart_set_buffer')
	.sect	'.debug_line'
.L238:
	.word	.L712-.L711
.L711:
	.half	3
	.word	.L714-.L713
.L713:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_uart.c',0,0,0,0,0
.L714:
	.byte	5,14,7,0,5,2
	.word	.L159
	.byte	3,250,0,1,9
	.half	.L715-.L159
	.byte	3,7,1,9
	.half	.L716-.L715
	.byte	3,7,1,9
	.half	.L717-.L716
	.byte	3,7,1,5,13,9
	.half	.L11-.L717
	.byte	3,109,1,5,41,9
	.half	.L718-.L11
	.byte	1,5,38,9
	.half	.L719-.L718
	.byte	1,5,13,9
	.half	.L720-.L719
	.byte	3,1,1,5,41,9
	.half	.L721-.L720
	.byte	1,5,38,9
	.half	.L722-.L721
	.byte	1,5,13,9
	.half	.L723-.L722
	.byte	3,1,1,5,40,9
	.half	.L724-.L723
	.byte	1,5,38,9
	.half	.L725-.L724
	.byte	1,5,13,9
	.half	.L726-.L725
	.byte	3,1,1,5,40,9
	.half	.L727-.L726
	.byte	1,5,38,9
	.half	.L728-.L727
	.byte	1,5,10,9
	.half	.L729-.L728
	.byte	3,1,1,5,13,9
	.half	.L12-.L729
	.byte	3,3,1,5,41,9
	.half	.L730-.L12
	.byte	1,5,38,9
	.half	.L731-.L730
	.byte	1,5,13,9
	.half	.L732-.L731
	.byte	3,1,1,5,41,9
	.half	.L733-.L732
	.byte	1,5,38,9
	.half	.L734-.L733
	.byte	1,5,13,9
	.half	.L735-.L734
	.byte	3,1,1,5,40,9
	.half	.L736-.L735
	.byte	1,5,38,9
	.half	.L737-.L736
	.byte	1,5,13,9
	.half	.L738-.L737
	.byte	3,1,1,5,40,9
	.half	.L739-.L738
	.byte	1,5,38,9
	.half	.L740-.L739
	.byte	1,5,10,9
	.half	.L741-.L740
	.byte	3,1,1,5,13,9
	.half	.L13-.L741
	.byte	3,3,1,5,41,9
	.half	.L742-.L13
	.byte	1,5,38,9
	.half	.L743-.L742
	.byte	1,5,13,9
	.half	.L744-.L743
	.byte	3,1,1,5,41,9
	.half	.L745-.L744
	.byte	1,5,38,9
	.half	.L746-.L745
	.byte	1,5,13,9
	.half	.L747-.L746
	.byte	3,1,1,5,40,9
	.half	.L748-.L747
	.byte	1,5,38,9
	.half	.L749-.L748
	.byte	1,5,13,9
	.half	.L750-.L749
	.byte	3,1,1,5,40,9
	.half	.L751-.L750
	.byte	1,5,38,9
	.half	.L752-.L751
	.byte	1,5,10,9
	.half	.L753-.L752
	.byte	3,1,1,5,13,9
	.half	.L14-.L753
	.byte	3,3,1,5,41,9
	.half	.L754-.L14
	.byte	1,5,38,9
	.half	.L755-.L754
	.byte	1,5,13,9
	.half	.L756-.L755
	.byte	3,1,1,5,41,9
	.half	.L757-.L756
	.byte	1,5,38,9
	.half	.L758-.L757
	.byte	1,5,13,9
	.half	.L759-.L758
	.byte	3,1,1,5,40,9
	.half	.L760-.L759
	.byte	1,5,38,9
	.half	.L761-.L760
	.byte	1,5,13,9
	.half	.L762-.L761
	.byte	3,1,1,5,40,9
	.half	.L763-.L762
	.byte	1,5,38,9
	.half	.L764-.L763
	.byte	1,5,10,9
	.half	.L765-.L764
	.byte	3,1,1,5,1,9
	.half	.L16-.L765
	.byte	3,3,1,7,9
	.half	.L240-.L16
	.byte	0,1,1
.L712:
	.sdecl	'.debug_ranges',debug,cluster('uart_set_buffer')
	.sect	'.debug_ranges'
.L239:
	.word	-1,.L159,0,.L240-.L159,0,0
	.sdecl	'.debug_info',debug,cluster('uart_get_handle')
	.sect	'.debug_info'
.L241:
	.word	303
	.half	3
	.word	.L242
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_uart.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L244,.L243
	.byte	2
	.word	.L182
	.byte	3
	.byte	'uart_get_handle',0,1,162,1,16
	.word	.L282
	.byte	1,1,1
	.word	.L161,.L411,.L160
	.byte	4
	.byte	'uartn',0,1,162,1,49
	.word	.L278,.L412
	.byte	5
	.word	.L161,.L411
	.byte	6
	.byte	'uart_handle',0,1,164,1,20
	.word	.L282,.L413
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('uart_get_handle')
	.sect	'.debug_abbrev'
.L242:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('uart_get_handle')
	.sect	'.debug_line'
.L243:
	.word	.L767-.L766
.L766:
	.half	3
	.word	.L769-.L768
.L768:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_uart.c',0,0,0,0,0
.L769:
	.byte	5,32,7,0,5,2
	.word	.L161
	.byte	3,163,1,1,5,14,9
	.half	.L425-.L161
	.byte	3,3,1,9
	.half	.L770-.L425
	.byte	3,1,1,9
	.half	.L771-.L770
	.byte	3,1,1,9
	.half	.L772-.L771
	.byte	3,1,1,5,37,9
	.half	.L20-.L772
	.byte	3,125,1,5,51,9
	.half	.L773-.L20
	.byte	1,5,37,9
	.half	.L21-.L773
	.byte	3,1,1,5,51,9
	.half	.L774-.L21
	.byte	1,5,37,9
	.half	.L22-.L774
	.byte	3,1,1,5,51,9
	.half	.L775-.L22
	.byte	1,5,37,9
	.half	.L23-.L775
	.byte	3,1,1,5,51,9
	.half	.L776-.L23
	.byte	1,5,5,9
	.half	.L25-.L776
	.byte	3,3,1,5,1,9
	.half	.L29-.L25
	.byte	3,1,1,7,9
	.half	.L245-.L29
	.byte	0,1,1
.L767:
	.sdecl	'.debug_ranges',debug,cluster('uart_get_handle')
	.sect	'.debug_ranges'
.L244:
	.word	-1,.L161,0,.L245-.L161,0,0
	.sdecl	'.debug_info',debug,cluster('uart_mux')
	.sect	'.debug_info'
.L246:
	.word	354
	.half	3
	.word	.L247
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_uart.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L249,.L248
	.byte	2
	.word	.L182
	.byte	3
	.byte	'uart_mux',0,1,177,1,6,1,1,1
	.word	.L163,.L414,.L162
	.byte	4
	.byte	'uartn',0,1,177,1,32
	.word	.L278,.L415
	.byte	4
	.byte	'tx_pin',0,1,177,1,56
	.word	.L364,.L416
	.byte	4
	.byte	'rx_pin',0,1,177,1,81
	.word	.L366,.L417
	.byte	4
	.byte	'set_tx_pin',0,1,177,1,97
	.word	.L418,.L419
	.byte	4
	.byte	'set_rx_pin',0,1,177,1,117
	.word	.L418,.L420
	.byte	5
	.word	.L163,.L414
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('uart_mux')
	.sect	'.debug_abbrev'
.L247:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('uart_mux')
	.sect	'.debug_line'
.L248:
	.word	.L778-.L777
.L777:
	.half	3
	.word	.L780-.L779
.L779:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_uart.c',0,0,0,0,0
.L780:
	.byte	5,14,7,0,5,2
	.word	.L163
	.byte	3,180,1,1,9
	.half	.L781-.L163
	.byte	3,13,1,9
	.half	.L782-.L781
	.byte	3,22,1,9
	.half	.L783-.L782
	.byte	3,19,1,5,13,9
	.half	.L30-.L783
	.byte	3,76,1,5,72,7,9
	.half	.L784-.L30
	.byte	1,5,61,9
	.half	.L785-.L784
	.byte	1,5,95,9
	.half	.L786-.L785
	.byte	1,5,18,9
	.half	.L35-.L786
	.byte	3,1,1,5,72,7,9
	.half	.L787-.L35
	.byte	1,5,61,9
	.half	.L788-.L787
	.byte	1,5,95,9
	.half	.L789-.L788
	.byte	1,5,18,9
	.half	.L37-.L789
	.byte	3,1,1,5,72,7,9
	.half	.L790-.L37
	.byte	1,5,61,9
	.half	.L791-.L790
	.byte	1,5,95,9
	.half	.L792-.L791
	.byte	1,5,18,9
	.half	.L39-.L792
	.byte	3,1,1,5,72,7,9
	.half	.L793-.L39
	.byte	1,5,61,9
	.half	.L794-.L793
	.byte	1,5,95,9
	.half	.L795-.L794
	.byte	1,5,13,9
	.half	.L36-.L795
	.byte	3,3,1,5,72,7,9
	.half	.L796-.L36
	.byte	1,5,61,9
	.half	.L797-.L796
	.byte	1,5,95,9
	.half	.L798-.L797
	.byte	1,5,18,9
	.half	.L43-.L798
	.byte	3,1,1,5,72,7,9
	.half	.L799-.L43
	.byte	1,5,61,9
	.half	.L800-.L799
	.byte	1,5,95,9
	.half	.L801-.L800
	.byte	1,5,10,9
	.half	.L44-.L801
	.byte	3,3,1,5,13,9
	.half	.L31-.L44
	.byte	3,3,1,5,72,7,9
	.half	.L802-.L31
	.byte	1,5,61,9
	.half	.L803-.L802
	.byte	1,5,95,9
	.half	.L804-.L803
	.byte	1,5,18,9
	.half	.L48-.L804
	.byte	3,1,1,5,72,7,9
	.half	.L805-.L48
	.byte	1,5,61,9
	.half	.L806-.L805
	.byte	1,5,96,9
	.half	.L807-.L806
	.byte	1,5,18,9
	.half	.L50-.L807
	.byte	3,1,1,5,72,7,9
	.half	.L808-.L50
	.byte	1,5,61,9
	.half	.L809-.L808
	.byte	1,5,95,9
	.half	.L810-.L809
	.byte	1,5,18,9
	.half	.L52-.L810
	.byte	3,1,1,5,72,7,9
	.half	.L811-.L52
	.byte	1,5,61,9
	.half	.L812-.L811
	.byte	1,5,95,9
	.half	.L813-.L812
	.byte	1,5,21,9
	.half	.L54-.L813
	.byte	3,1,1,5,18,9
	.half	.L814-.L54
	.byte	1,5,72,7,9
	.half	.L815-.L814
	.byte	1,5,61,9
	.half	.L816-.L815
	.byte	1,5,95,9
	.half	.L817-.L816
	.byte	1,5,21,9
	.half	.L56-.L817
	.byte	3,1,1,5,18,9
	.half	.L818-.L56
	.byte	1,5,72,7,9
	.half	.L819-.L818
	.byte	1,5,61,9
	.half	.L820-.L819
	.byte	1,5,95,9
	.half	.L821-.L820
	.byte	1,5,21,9
	.half	.L58-.L821
	.byte	3,1,1,5,18,9
	.half	.L822-.L58
	.byte	1,5,72,7,9
	.half	.L823-.L822
	.byte	1,5,61,9
	.half	.L824-.L823
	.byte	1,5,96,9
	.half	.L825-.L824
	.byte	1,5,21,9
	.half	.L60-.L825
	.byte	3,1,1,5,18,9
	.half	.L826-.L60
	.byte	1,5,72,7,9
	.half	.L827-.L826
	.byte	1,5,61,9
	.half	.L828-.L827
	.byte	1,5,96,9
	.half	.L829-.L828
	.byte	1,5,21,9
	.half	.L62-.L829
	.byte	3,1,1,5,18,9
	.half	.L830-.L62
	.byte	1,5,72,7,9
	.half	.L831-.L830
	.byte	1,5,61,9
	.half	.L832-.L831
	.byte	1,5,96,9
	.half	.L833-.L832
	.byte	1,5,13,9
	.half	.L49-.L833
	.byte	3,3,1,5,72,7,9
	.half	.L834-.L49
	.byte	1,5,61,9
	.half	.L835-.L834
	.byte	1,5,95,9
	.half	.L836-.L835
	.byte	1,5,18,9
	.half	.L66-.L836
	.byte	3,1,1,5,72,7,9
	.half	.L837-.L66
	.byte	1,5,61,9
	.half	.L838-.L837
	.byte	1,5,95,9
	.half	.L839-.L838
	.byte	1,5,18,9
	.half	.L68-.L839
	.byte	3,1,1,5,72,7,9
	.half	.L840-.L68
	.byte	1,5,61,9
	.half	.L841-.L840
	.byte	1,5,95,9
	.half	.L842-.L841
	.byte	1,5,18,9
	.half	.L70-.L842
	.byte	3,1,1,5,72,7,9
	.half	.L843-.L70
	.byte	1,5,61,9
	.half	.L844-.L843
	.byte	1,5,96,9
	.half	.L845-.L844
	.byte	1,5,18,9
	.half	.L72-.L845
	.byte	3,1,1,5,72,7,9
	.half	.L846-.L72
	.byte	1,5,61,9
	.half	.L847-.L846
	.byte	1,5,96,9
	.half	.L848-.L847
	.byte	1,5,18,9
	.half	.L74-.L848
	.byte	3,1,1,5,72,7,9
	.half	.L849-.L74
	.byte	1,5,61,9
	.half	.L850-.L849
	.byte	1,5,95,9
	.half	.L851-.L850
	.byte	1,5,10,9
	.half	.L67-.L851
	.byte	3,3,1,5,21,9
	.half	.L32-.L67
	.byte	3,3,1,5,13,9
	.half	.L852-.L32
	.byte	1,5,72,7,9
	.half	.L853-.L852
	.byte	1,5,61,9
	.half	.L854-.L853
	.byte	1,5,95,9
	.half	.L855-.L854
	.byte	1,5,21,9
	.half	.L79-.L855
	.byte	3,1,1,5,18,9
	.half	.L856-.L79
	.byte	1,5,72,7,9
	.half	.L857-.L856
	.byte	1,5,61,9
	.half	.L858-.L857
	.byte	1,5,95,9
	.half	.L859-.L858
	.byte	1,5,21,9
	.half	.L81-.L859
	.byte	3,1,1,5,18,9
	.half	.L860-.L81
	.byte	1,5,72,7,9
	.half	.L861-.L860
	.byte	1,5,61,9
	.half	.L862-.L861
	.byte	1,5,95,9
	.half	.L863-.L862
	.byte	1,5,21,9
	.half	.L83-.L863
	.byte	3,1,1,5,18,9
	.half	.L864-.L83
	.byte	1,5,72,7,9
	.half	.L865-.L864
	.byte	1,5,61,9
	.half	.L866-.L865
	.byte	1,5,95,9
	.half	.L867-.L866
	.byte	1,5,21,9
	.half	.L85-.L867
	.byte	3,1,1,5,18,9
	.half	.L868-.L85
	.byte	1,5,72,7,9
	.half	.L869-.L868
	.byte	1,5,61,9
	.half	.L870-.L869
	.byte	1,5,95,9
	.half	.L871-.L870
	.byte	1,5,21,9
	.half	.L87-.L871
	.byte	3,1,1,5,18,9
	.half	.L872-.L87
	.byte	1,5,72,7,9
	.half	.L873-.L872
	.byte	1,5,61,9
	.half	.L874-.L873
	.byte	1,5,95,9
	.half	.L875-.L874
	.byte	1,5,21,9
	.half	.L80-.L875
	.byte	3,3,1,5,13,9
	.half	.L876-.L80
	.byte	1,5,72,7,9
	.half	.L877-.L876
	.byte	1,5,61,9
	.half	.L878-.L877
	.byte	1,5,95,9
	.half	.L879-.L878
	.byte	1,5,21,9
	.half	.L91-.L879
	.byte	3,1,1,5,18,9
	.half	.L880-.L91
	.byte	1,5,72,7,9
	.half	.L881-.L880
	.byte	1,5,61,9
	.half	.L882-.L881
	.byte	1,5,95,9
	.half	.L883-.L882
	.byte	1,5,21,9
	.half	.L93-.L883
	.byte	3,1,1,5,18,9
	.half	.L884-.L93
	.byte	1,5,72,7,9
	.half	.L885-.L884
	.byte	1,5,61,9
	.half	.L886-.L885
	.byte	1,5,95,9
	.half	.L887-.L886
	.byte	1,5,21,9
	.half	.L95-.L887
	.byte	3,1,1,5,18,9
	.half	.L888-.L95
	.byte	1,5,72,7,9
	.half	.L889-.L888
	.byte	1,5,61,9
	.half	.L890-.L889
	.byte	1,5,95,9
	.half	.L891-.L890
	.byte	1,5,21,9
	.half	.L97-.L891
	.byte	3,1,1,5,18,9
	.half	.L892-.L97
	.byte	1,5,72,7,9
	.half	.L893-.L892
	.byte	1,5,61,9
	.half	.L894-.L893
	.byte	1,5,95,9
	.half	.L895-.L894
	.byte	1,5,10,9
	.half	.L92-.L895
	.byte	3,4,1,5,21,9
	.half	.L33-.L92
	.byte	3,3,1,5,13,9
	.half	.L896-.L33
	.byte	1,5,72,7,9
	.half	.L897-.L896
	.byte	1,5,61,9
	.half	.L898-.L897
	.byte	1,5,95,9
	.half	.L899-.L898
	.byte	1,5,21,9
	.half	.L102-.L899
	.byte	3,1,1,5,18,9
	.half	.L900-.L102
	.byte	1,5,72,7,9
	.half	.L901-.L900
	.byte	1,5,61,9
	.half	.L902-.L901
	.byte	1,5,95,9
	.half	.L903-.L902
	.byte	1,5,21,9
	.half	.L104-.L903
	.byte	3,1,1,5,18,9
	.half	.L904-.L104
	.byte	1,5,72,7,9
	.half	.L905-.L904
	.byte	1,5,61,9
	.half	.L906-.L905
	.byte	1,5,95,9
	.half	.L907-.L906
	.byte	1,5,21,9
	.half	.L106-.L907
	.byte	3,1,1,5,18,9
	.half	.L908-.L106
	.byte	1,5,72,7,9
	.half	.L909-.L908
	.byte	1,5,61,9
	.half	.L910-.L909
	.byte	1,5,95,9
	.half	.L911-.L910
	.byte	1,5,21,9
	.half	.L108-.L911
	.byte	3,1,1,5,18,9
	.half	.L912-.L108
	.byte	1,5,72,7,9
	.half	.L913-.L912
	.byte	1,5,61,9
	.half	.L914-.L913
	.byte	1,5,95,9
	.half	.L915-.L914
	.byte	1,5,21,9
	.half	.L110-.L915
	.byte	3,1,1,5,18,9
	.half	.L916-.L110
	.byte	1,5,72,7,9
	.half	.L917-.L916
	.byte	1,5,61,9
	.half	.L918-.L917
	.byte	1,5,95,9
	.half	.L919-.L918
	.byte	1,5,21,9
	.half	.L112-.L919
	.byte	3,1,1,5,18,9
	.half	.L920-.L112
	.byte	1,5,72,7,9
	.half	.L921-.L920
	.byte	1,5,61,9
	.half	.L922-.L921
	.byte	1,5,95,9
	.half	.L923-.L922
	.byte	1,5,21,9
	.half	.L103-.L923
	.byte	3,3,1,5,13,9
	.half	.L924-.L103
	.byte	1,5,72,7,9
	.half	.L925-.L924
	.byte	1,5,61,9
	.half	.L926-.L925
	.byte	1,5,95,9
	.half	.L927-.L926
	.byte	1,5,21,9
	.half	.L116-.L927
	.byte	3,1,1,5,18,9
	.half	.L928-.L116
	.byte	1,5,72,7,9
	.half	.L929-.L928
	.byte	1,5,61,9
	.half	.L930-.L929
	.byte	1,5,95,9
	.half	.L931-.L930
	.byte	1,5,21,9
	.half	.L118-.L931
	.byte	3,1,1,5,18,9
	.half	.L932-.L118
	.byte	1,5,72,7,9
	.half	.L933-.L932
	.byte	1,5,61,9
	.half	.L934-.L933
	.byte	1,5,95,9
	.half	.L935-.L934
	.byte	1,5,21,9
	.half	.L120-.L935
	.byte	3,1,1,5,18,9
	.half	.L936-.L120
	.byte	1,5,72,7,9
	.half	.L937-.L936
	.byte	1,5,61,9
	.half	.L938-.L937
	.byte	1,5,95,9
	.half	.L939-.L938
	.byte	1,5,10,9
	.half	.L117-.L939
	.byte	3,3,1,5,17,9
	.half	.L34-.L117
	.byte	3,1,1,5,1,9
	.half	.L47-.L34
	.byte	3,2,1,7,9
	.half	.L250-.L47
	.byte	0,1,1
.L778:
	.sdecl	'.debug_ranges',debug,cluster('uart_mux')
	.sect	'.debug_ranges'
.L249:
	.word	-1,.L163,0,.L250-.L163,0,0
	.sdecl	'.debug_info',debug,cluster('uart0_handle')
	.sect	'.debug_info'
.L251:
	.word	228
	.half	3
	.word	.L252
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_uart.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L182
	.byte	3
	.byte	'uart0_handle',0,26,50,15
	.word	.L421
	.byte	1,5,3
	.word	uart0_handle
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('uart0_handle')
	.sect	'.debug_abbrev'
.L252:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('uart1_handle')
	.sect	'.debug_info'
.L253:
	.word	228
	.half	3
	.word	.L254
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_uart.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L182
	.byte	3
	.byte	'uart1_handle',0,26,51,15
	.word	.L421
	.byte	1,5,3
	.word	uart1_handle
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('uart1_handle')
	.sect	'.debug_abbrev'
.L254:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('uart2_handle')
	.sect	'.debug_info'
.L255:
	.word	228
	.half	3
	.word	.L256
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_uart.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L182
	.byte	3
	.byte	'uart2_handle',0,26,52,15
	.word	.L421
	.byte	1,5,3
	.word	uart2_handle
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('uart2_handle')
	.sect	'.debug_abbrev'
.L256:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('uart3_handle')
	.sect	'.debug_info'
.L257:
	.word	228
	.half	3
	.word	.L258
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_uart.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L182
	.byte	3
	.byte	'uart3_handle',0,26,53,15
	.word	.L421
	.byte	1,5,3
	.word	uart3_handle
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('uart3_handle')
	.sect	'.debug_abbrev'
.L258:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('uart_config')
	.sect	'.debug_info'
.L259:
	.word	226
	.half	3
	.word	.L260
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_uart.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L182
	.byte	3
	.byte	'uart_config',0,26,56,29
	.word	.L422
	.byte	5,3
	.word	uart_config
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('uart_config')
	.sect	'.debug_abbrev'
.L260:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_info',debug,cluster('uart0_tx_buffer')
	.sect	'.debug_info'
.L261:
	.word	230
	.half	3
	.word	.L262
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_uart.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L182
	.byte	3
	.byte	'uart0_tx_buffer',0,26,60,14
	.word	.L423
	.byte	5,3
	.word	uart0_tx_buffer
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('uart0_tx_buffer')
	.sect	'.debug_abbrev'
.L262:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_info',debug,cluster('uart0_rx_buffer')
	.sect	'.debug_info'
.L263:
	.word	230
	.half	3
	.word	.L264
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_uart.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L182
	.byte	3
	.byte	'uart0_rx_buffer',0,26,61,14
	.word	.L423
	.byte	5,3
	.word	uart0_rx_buffer
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('uart0_rx_buffer')
	.sect	'.debug_abbrev'
.L264:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_info',debug,cluster('uart1_tx_buffer')
	.sect	'.debug_info'
.L265:
	.word	230
	.half	3
	.word	.L266
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_uart.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L182
	.byte	3
	.byte	'uart1_tx_buffer',0,26,62,14
	.word	.L423
	.byte	5,3
	.word	uart1_tx_buffer
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('uart1_tx_buffer')
	.sect	'.debug_abbrev'
.L266:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_info',debug,cluster('uart1_rx_buffer')
	.sect	'.debug_info'
.L267:
	.word	230
	.half	3
	.word	.L268
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_uart.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L182
	.byte	3
	.byte	'uart1_rx_buffer',0,26,63,14
	.word	.L423
	.byte	5,3
	.word	uart1_rx_buffer
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('uart1_rx_buffer')
	.sect	'.debug_abbrev'
.L268:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_info',debug,cluster('uart2_tx_buffer')
	.sect	'.debug_info'
.L269:
	.word	230
	.half	3
	.word	.L270
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_uart.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L182
	.byte	3
	.byte	'uart2_tx_buffer',0,26,64,14
	.word	.L423
	.byte	5,3
	.word	uart2_tx_buffer
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('uart2_tx_buffer')
	.sect	'.debug_abbrev'
.L270:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_info',debug,cluster('uart2_rx_buffer')
	.sect	'.debug_info'
.L271:
	.word	230
	.half	3
	.word	.L272
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_uart.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L182
	.byte	3
	.byte	'uart2_rx_buffer',0,26,65,14
	.word	.L423
	.byte	5,3
	.word	uart2_rx_buffer
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('uart2_rx_buffer')
	.sect	'.debug_abbrev'
.L272:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_info',debug,cluster('uart3_tx_buffer')
	.sect	'.debug_info'
.L273:
	.word	230
	.half	3
	.word	.L274
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_uart.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L182
	.byte	3
	.byte	'uart3_tx_buffer',0,26,66,14
	.word	.L423
	.byte	5,3
	.word	uart3_tx_buffer
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('uart3_tx_buffer')
	.sect	'.debug_abbrev'
.L274:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_info',debug,cluster('uart3_rx_buffer')
	.sect	'.debug_info'
.L275:
	.word	230
	.half	3
	.word	.L276
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_uart.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L182
	.byte	3
	.byte	'uart3_rx_buffer',0,26,67,14
	.word	.L423
	.byte	5,3
	.word	uart3_rx_buffer
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('uart3_rx_buffer')
	.sect	'.debug_abbrev'
.L276:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_loc',debug,cluster('uart_get_handle')
	.sect	'.debug_loc'
.L160:
	.word	-1,.L161,0,.L411-.L161
	.half	2
	.byte	138,0
	.word	0,0
.L413:
	.word	-1,.L161,.L425-.L161,.L411-.L161
	.half	1
	.byte	98
	.word	0,0
.L412:
	.word	-1,.L161,0,.L411-.L161
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('uart_init')
	.sect	'.debug_loc'
.L394:
	.word	-1,.L181,0,.L490-.L181
	.half	1
	.byte	85
	.word	.L501-.L181,.L502-.L181
	.half	1
	.byte	91
	.word	0,0
.L405:
	.word	0,0
.L400:
	.word	0,0
.L406:
	.word	0,0
.L397:
	.word	-1,.L181,.L494-.L181,.L392-.L181
	.half	1
	.byte	92
	.word	0,0
.L398:
	.word	-1,.L181,.L490-.L181,.L495-.L181
	.half	1
	.byte	98
	.word	.L496-.L181,.L495-.L181
	.half	1
	.byte	101
	.word	0,0
.L403:
	.word	-1,.L181,0,.L392-.L181
	.half	2
	.byte	145,96
	.word	0,0
.L401:
	.word	-1,.L181,.L492-.L181,.L493-.L181
	.half	1
	.byte	95
	.word	0,0
.L396:
	.word	-1,.L181,0,.L490-.L181
	.half	1
	.byte	87
	.word	.L504-.L181,.L505-.L181
	.half	1
	.byte	88
	.word	.L505-.L181,.L506-.L181
	.half	1
	.byte	86
	.word	0,0
.L395:
	.word	-1,.L181,0,.L490-.L181
	.half	1
	.byte	86
	.word	.L503-.L181,.L504-.L181
	.half	1
	.byte	89
	.word	0,0
.L180:
	.word	-1,.L181,0,.L489-.L181
	.half	2
	.byte	138,0
	.word	.L489-.L181,.L392-.L181
	.half	2
	.byte	138,32
	.word	.L392-.L181,.L392-.L181
	.half	2
	.byte	138,0
	.word	0,0
.L393:
	.word	-1,.L181,0,.L491-.L181
	.half	1
	.byte	84
	.word	.L154-.L181,.L491-.L181
	.half	1
	.byte	90
	.word	.L495-.L181,.L497-.L181
	.half	1
	.byte	90
	.word	.L497-.L181,.L498-.L181
	.half	1
	.byte	84
	.word	.L498-.L181,.L499-.L181
	.half	1
	.byte	90
	.word	.L499-.L181,.L500-.L181
	.half	1
	.byte	84
	.word	.L503-.L181,.L504-.L181
	.half	1
	.byte	90
	.word	.L507-.L181,.L508-.L181
	.half	1
	.byte	90
	.word	.L508-.L181,.L509-.L181
	.half	1
	.byte	84
	.word	.L510-.L181,.L511-.L181
	.half	1
	.byte	90
	.word	.L511-.L181,.L512-.L181
	.half	1
	.byte	84
	.word	.L513-.L181,.L514-.L181
	.half	1
	.byte	90
	.word	.L514-.L181,.L515-.L181
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('uart_mux')
	.sect	'.debug_loc'
.L417:
	.word	-1,.L163,0,.L414-.L163
	.half	1
	.byte	86
	.word	0,0
.L420:
	.word	-1,.L163,0,.L414-.L163
	.half	1
	.byte	101
	.word	0,0
.L419:
	.word	-1,.L163,0,.L414-.L163
	.half	1
	.byte	100
	.word	0,0
.L416:
	.word	-1,.L163,0,.L414-.L163
	.half	1
	.byte	85
	.word	0,0
.L162:
	.word	-1,.L163,0,.L414-.L163
	.half	2
	.byte	138,0
	.word	0,0
.L415:
	.word	-1,.L163,0,.L414-.L163
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('uart_query_byte')
	.sect	'.debug_loc'
.L316:
	.word	0,0
.L312:
	.word	-1,.L173,0,.L440-.L173
	.half	1
	.byte	100
	.word	.L441-.L173,.L309-.L173
	.half	1
	.byte	111
	.word	.L444-.L173,.L443-.L173
	.half	1
	.byte	101
	.word	0,0
.L313:
	.word	-1,.L173,.L442-.L173,.L309-.L173
	.half	1
	.byte	88
	.word	.L445-.L173,.L309-.L173
	.half	1
	.byte	82
	.word	0,0
.L314:
	.word	-1,.L173,.L440-.L173,.L443-.L173
	.half	1
	.byte	98
	.word	0,0
.L310:
	.word	-1,.L173,0,.L440-.L173
	.half	1
	.byte	84
	.word	0,0
.L172:
	.word	-1,.L173,0,.L309-.L173
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('uart_read_byte')
	.sect	'.debug_loc'
.L307:
	.word	0,0
.L302:
	.word	-1,.L171,0,.L300-.L171
	.half	2
	.byte	145,120
	.word	0,0
.L303:
	.word	-1,.L171,.L438-.L171,.L439-.L171
	.half	1
	.byte	98
	.word	0,0
.L301:
	.word	-1,.L171,0,.L438-.L171
	.half	1
	.byte	84
	.word	0,0
.L170:
	.word	-1,.L171,0,.L437-.L171
	.half	2
	.byte	138,0
	.word	.L437-.L171,.L300-.L171
	.half	2
	.byte	138,8
	.word	.L300-.L171,.L300-.L171
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('uart_rx_interrupt')
	.sect	'.debug_loc'
.L353:
	.word	0,0
.L346:
	.word	-1,.L177,.L457-.L177,.L343-.L177
	.half	1
	.byte	111
	.word	.L460-.L177,.L461-.L177
	.half	1
	.byte	100
	.word	0,0
.L355:
	.word	0,0
.L348:
	.word	-1,.L177,.L454-.L177,.L458-.L177
	.half	1
	.byte	98
	.word	.L459-.L177,.L458-.L177
	.half	1
	.byte	101
	.word	0,0
.L360:
	.word	0,0
.L359:
	.word	0,0
.L347:
	.word	-1,.L177,.L461-.L177,.L343-.L177
	.half	1
	.byte	98
	.word	0,0
.L345:
	.word	-1,.L177,0,.L454-.L177
	.half	1
	.byte	85
	.word	.L456-.L177,.L343-.L177
	.half	1
	.byte	88
	.word	0,0
.L344:
	.word	-1,.L177,0,.L455-.L177
	.half	1
	.byte	84
	.word	0,0
.L176:
	.word	-1,.L177,0,.L343-.L177
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('uart_sbus_init')
	.sect	'.debug_loc'
.L363:
	.word	-1,.L179,0,.L463-.L179
	.half	1
	.byte	85
	.word	.L474-.L179,.L475-.L179
	.half	1
	.byte	91
	.word	0,0
.L386:
	.word	0,0
.L375:
	.word	0,0
.L390:
	.word	0,0
.L368:
	.word	-1,.L179,.L467-.L179,.L361-.L179
	.half	1
	.byte	92
	.word	0,0
.L369:
	.word	-1,.L179,.L463-.L179,.L468-.L179
	.half	1
	.byte	98
	.word	.L469-.L179,.L468-.L179
	.half	1
	.byte	101
	.word	0,0
.L382:
	.word	-1,.L179,0,.L361-.L179
	.half	2
	.byte	145,96
	.word	0,0
.L379:
	.word	-1,.L179,.L465-.L179,.L466-.L179
	.half	1
	.byte	95
	.word	0,0
.L367:
	.word	-1,.L179,0,.L463-.L179
	.half	1
	.byte	87
	.word	.L477-.L179,.L478-.L179
	.half	1
	.byte	88
	.word	.L478-.L179,.L479-.L179
	.half	1
	.byte	86
	.word	0,0
.L365:
	.word	-1,.L179,0,.L463-.L179
	.half	1
	.byte	86
	.word	.L476-.L179,.L477-.L179
	.half	1
	.byte	89
	.word	0,0
.L178:
	.word	-1,.L179,0,.L462-.L179
	.half	2
	.byte	138,0
	.word	.L462-.L179,.L361-.L179
	.half	2
	.byte	138,32
	.word	.L361-.L179,.L361-.L179
	.half	2
	.byte	138,0
	.word	0,0
.L362:
	.word	-1,.L179,0,.L464-.L179
	.half	1
	.byte	84
	.word	.L150-.L179,.L464-.L179
	.half	1
	.byte	90
	.word	.L468-.L179,.L470-.L179
	.half	1
	.byte	90
	.word	.L470-.L179,.L471-.L179
	.half	1
	.byte	84
	.word	.L471-.L179,.L472-.L179
	.half	1
	.byte	90
	.word	.L472-.L179,.L473-.L179
	.half	1
	.byte	84
	.word	.L476-.L179,.L477-.L179
	.half	1
	.byte	90
	.word	.L480-.L179,.L481-.L179
	.half	1
	.byte	90
	.word	.L481-.L179,.L482-.L179
	.half	1
	.byte	84
	.word	.L483-.L179,.L484-.L179
	.half	1
	.byte	90
	.word	.L484-.L179,.L485-.L179
	.half	1
	.byte	84
	.word	.L486-.L179,.L487-.L179
	.half	1
	.byte	90
	.word	.L487-.L179,.L488-.L179
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('uart_set_buffer')
	.sect	'.debug_loc'
.L158:
	.word	-1,.L159,0,.L409-.L159
	.half	2
	.byte	138,0
	.word	0,0
.L410:
	.word	-1,.L159,0,.L409-.L159
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('uart_set_interrupt_priority')
	.sect	'.debug_loc'
.L156:
	.word	-1,.L157,0,.L407-.L157
	.half	2
	.byte	138,0
	.word	0,0
.L408:
	.word	-1,.L157,0,.L424-.L157
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('uart_tx_interrupt')
	.sect	'.debug_loc'
.L329:
	.word	0,0
.L321:
	.word	-1,.L175,.L449-.L175,.L317-.L175
	.half	1
	.byte	111
	.word	.L452-.L175,.L453-.L175
	.half	1
	.byte	100
	.word	0,0
.L331:
	.word	0,0
.L324:
	.word	-1,.L175,.L446-.L175,.L450-.L175
	.half	1
	.byte	98
	.word	.L451-.L175,.L450-.L175
	.half	1
	.byte	101
	.word	0,0
.L341:
	.word	0,0
.L337:
	.word	0,0
.L323:
	.word	-1,.L175,.L453-.L175,.L317-.L175
	.half	1
	.byte	98
	.word	0,0
.L319:
	.word	-1,.L175,0,.L446-.L175
	.half	1
	.byte	85
	.word	.L448-.L175,.L317-.L175
	.half	1
	.byte	88
	.word	0,0
.L318:
	.word	-1,.L175,0,.L447-.L175
	.half	1
	.byte	84
	.word	0,0
.L174:
	.word	-1,.L175,0,.L317-.L175
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('uart_write_buffer')
	.sect	'.debug_loc'
.L292:
	.word	-1,.L167,0,.L130-.L167
	.half	1
	.byte	100
	.word	.L430-.L167,.L289-.L167
	.half	1
	.byte	111
	.word	0,0
.L294:
	.word	-1,.L167,0,.L130-.L167
	.half	1
	.byte	85
	.word	.L431-.L167,.L289-.L167
	.half	1
	.byte	95
	.word	0,0
.L290:
	.word	-1,.L167,0,.L130-.L167
	.half	1
	.byte	84
	.word	.L429-.L167,.L289-.L167
	.half	1
	.byte	88
	.word	.L432-.L167,.L433-.L167
	.half	1
	.byte	84
	.word	0,0
.L166:
	.word	-1,.L167,0,.L289-.L167
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('uart_write_byte')
	.sect	'.debug_loc'
.L287:
	.word	0,0
.L281:
	.word	-1,.L165,0,.L427-.L165
	.half	1
	.byte	85
	.word	0,.L277-.L165
	.half	2
	.byte	145,120
	.word	0,0
.L283:
	.word	-1,.L165,.L427-.L165,.L428-.L165
	.half	1
	.byte	98
	.word	0,0
.L279:
	.word	-1,.L165,0,.L427-.L165
	.half	1
	.byte	84
	.word	0,0
.L164:
	.word	-1,.L165,0,.L426-.L165
	.half	2
	.byte	138,0
	.word	.L426-.L165,.L277-.L165
	.half	2
	.byte	138,8
	.word	.L277-.L165,.L277-.L165
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('uart_write_string')
	.sect	'.debug_loc'
.L298:
	.word	-1,.L169,0,.L132-.L169
	.half	1
	.byte	100
	.word	.L435-.L169,.L295-.L169
	.half	1
	.byte	111
	.word	0,0
.L296:
	.word	-1,.L169,0,.L132-.L169
	.half	1
	.byte	84
	.word	.L434-.L169,.L295-.L169
	.half	1
	.byte	88
	.word	.L436-.L169,.L131-.L169
	.half	1
	.byte	84
	.word	0,0
.L168:
	.word	-1,.L169,0,.L295-.L169
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L940:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('uart_set_interrupt_priority')
	.sect	'.debug_frame'
	.word	12
	.word	.L940,.L157,.L407-.L157
	.sdecl	'.debug_frame',debug,cluster('uart_set_buffer')
	.sect	'.debug_frame'
	.word	24
	.word	.L940,.L159,.L409-.L159
	.byte	8,19,8,20,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('uart_get_handle')
	.sect	'.debug_frame'
	.word	24
	.word	.L940,.L161,.L411-.L161
	.byte	8,19,8,20,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('uart_mux')
	.sect	'.debug_frame'
	.word	20
	.word	.L940,.L163,.L414-.L163
	.byte	8,18,8,19,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('uart_write_byte')
	.sect	'.debug_frame'
	.word	36
	.word	.L940,.L165,.L277-.L165
	.byte	4
	.word	(.L426-.L165)/2
	.byte	19,8,22,26,3,19,138,8,4
	.word	(.L277-.L426)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('uart_write_buffer')
	.sect	'.debug_frame'
	.word	12
	.word	.L940,.L167,.L289-.L167
	.sdecl	'.debug_frame',debug,cluster('uart_write_string')
	.sect	'.debug_frame'
	.word	12
	.word	.L940,.L169,.L295-.L169
	.sdecl	'.debug_frame',debug,cluster('uart_read_byte')
	.sect	'.debug_frame'
	.word	36
	.word	.L940,.L171,.L300-.L171
	.byte	4
	.word	(.L437-.L171)/2
	.byte	19,8,22,26,3,19,138,8,4
	.word	(.L300-.L437)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('uart_query_byte')
	.sect	'.debug_frame'
	.word	12
	.word	.L940,.L173,.L309-.L173
	.sdecl	'.debug_frame',debug,cluster('uart_tx_interrupt')
	.sect	'.debug_frame'
	.word	12
	.word	.L940,.L175,.L317-.L175
	.sdecl	'.debug_frame',debug,cluster('uart_rx_interrupt')
	.sect	'.debug_frame'
	.word	12
	.word	.L940,.L177,.L343-.L177
	.sdecl	'.debug_frame',debug,cluster('uart_sbus_init')
	.sect	'.debug_frame'
	.word	36
	.word	.L940,.L179,.L361-.L179
	.byte	4
	.word	(.L462-.L179)/2
	.byte	19,32,22,26,3,19,138,32,4
	.word	(.L361-.L462)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('uart_init')
	.sect	'.debug_frame'
	.word	36
	.word	.L940,.L181,.L392-.L181
	.byte	4
	.word	(.L489-.L181)/2
	.byte	19,32,22,26,3,19,138,32,4
	.word	(.L392-.L489)/2
	.byte	19,0,8,26,0,0
	; Module end
