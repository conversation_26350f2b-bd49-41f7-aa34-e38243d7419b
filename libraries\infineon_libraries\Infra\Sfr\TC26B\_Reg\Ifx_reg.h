/**
 * \file Ifx_reg.h
 * \brief
 * \copyright Copyright (c) 2012 Infineon Technologies AG. All rights reserved.
 *
 * Version: TC26XB_UM_V1.2.R0
 * Specification: Refer to module specific file heading
 * MAY BE CHANGED BY USER [yes/no]: No
 *
 *                                 IMPORTANT NOTICE
 *
 *
 * Use of this file is subject to the terms of use agreed between (i) you or 
 * the company in which ordinary course of business you are acting and (ii) 
 * Infineon Technologies AG or its licensees. If and as long as no such 
 * terms of use are agreed, use of this file is subject to following:


 * Boost Software License - Version 1.0 - August 17th, 2003

 * Permission is hereby granted, free of charge, to any person or 
 * organization obtaining a copy of the software and accompanying 
 * documentation covered by this license (the "Software") to use, reproduce,
 * display, distribute, execute, and transmit the Software, and to prepare
 * derivative works of the Software, and to permit third-parties to whom the 
 * Software is furnished to do so, all subject to the following:

 * The copyright notices in the Software and this entire statement, including
 * the above license grant, this restriction and the following disclaimer, must
 * be included in all copies of the Software, in whole or in part, and all
 * derivative works of the Software, unless such copies or derivative works are
 * solely in the form of machine-executable object code generated by a source
 * language processor.

 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR 
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE, TITLE AND NON-INFRINGEMENT. IN NO EVENT
 * SHALL THE COPYRIGHT HOLDERS OR ANYONE DISTRIBUTING THE SOFTWARE BE LIABLE 
 * FOR ANY DAMAGES OR OTHER LIABILITY, WHETHER IN CONTRACT, TORT OR OTHERWISE,
 * ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
 * DEALINGS IN THE SOFTWARE.

 *
 */
#ifndef IFX_REG_H
#define IFX_REG_H 1

#include "IfxAsclin_reg.h"
#include "IfxCan_reg.h"
#include "IfxCbs_reg.h"
#include "IfxCcu6_reg.h"
#include "IfxCif_reg.h"
#include "IfxCpu_reg.h"
#include "IfxDma_reg.h"
#include "IfxDsadc_reg.h"
#include "IfxEbcu_reg.h"
#include "IfxEmem_reg.h"
#include "IfxEray_reg.h"
#include "IfxEth_reg.h"
#include "IfxFce_reg.h"
#include "IfxFft_reg.h"
#include "IfxFlash_reg.h"
#include "IfxGpt12_reg.h"
#include "IfxGtm_reg.h"
#include "IfxHsct_reg.h"
#include "IfxHssl_reg.h"
#include "IfxI2c_reg.h"
#include "IfxInt_reg.h"
#include "IfxIom_reg.h"
#include "IfxLmu_reg.h"
#include "IfxMc_reg.h"
#include "IfxMsc_reg.h"
#include "IfxMtu_reg.h"
#include "IfxOvc_reg.h"
#include "IfxPmu_reg.h"
#include "IfxPort_reg.h"
#include "IfxPsi5_reg.h"
#include "IfxPsi5s_reg.h"
#include "IfxQspi_reg.h"
#include "IfxSbcu_reg.h"
#include "IfxScu_reg.h"
#include "IfxSent_reg.h"
#include "IfxSmu_reg.h"
#include "IfxSrc_reg.h"
#include "IfxStm_reg.h"
#include "IfxVadc_reg.h"
#include "IfxXbar_reg.h"

#endif /*IFX_REG_H*/

