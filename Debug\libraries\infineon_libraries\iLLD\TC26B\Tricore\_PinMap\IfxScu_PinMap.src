	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc14836a --dep-file=IfxScu_PinMap.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxScu_PinMap.src ../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxScu_PinMap.c"
	.compiler_name		"ctc"
	;source	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxScu_PinMap.c'

	
$TC16X
	
	.sdecl	'.rodata.IfxScu_PinMap.IfxScu_DCDCSYNC_P32_2_OUT',data,rom,cluster('IfxScu_DCDCSYNC_P32_2_OUT')
	.sect	'.rodata.IfxScu_PinMap.IfxScu_DCDCSYNC_P32_2_OUT'
	.global	IfxScu_DCDCSYNC_P32_2_OUT
	.align	4
IfxScu_DCDCSYNC_P32_2_OUT:	.type	object
	.size	IfxScu_DCDCSYNC_P32_2_OUT,16
	.word	-268214272,-268185088
	.byte	2
	.space	3
	.byte	176
	.space	3
	.sdecl	'.rodata.IfxScu_PinMap.IfxScu_DCDCSYNC_P33_13_OUT',data,rom,cluster('IfxScu_DCDCSYNC_P33_13_OUT')
	.sect	'.rodata.IfxScu_PinMap.IfxScu_DCDCSYNC_P33_13_OUT'
	.global	IfxScu_DCDCSYNC_P33_13_OUT
	.align	4
IfxScu_DCDCSYNC_P33_13_OUT:	.type	object
	.size	IfxScu_DCDCSYNC_P33_13_OUT,16
	.word	-268214272,-268184832
	.byte	13
	.space	3
	.byte	176
	.space	3
	.sdecl	'.rodata.IfxScu_PinMap.IfxScu_EMGSTOPA_P33_8_IN',data,rom,cluster('IfxScu_EMGSTOPA_P33_8_IN')
	.sect	'.rodata.IfxScu_PinMap.IfxScu_EMGSTOPA_P33_8_IN'
	.global	IfxScu_EMGSTOPA_P33_8_IN
	.align	4
IfxScu_EMGSTOPA_P33_8_IN:	.type	object
	.size	IfxScu_EMGSTOPA_P33_8_IN,16
	.word	-268214272,-268184832
	.byte	8
	.space	7
	.sdecl	'.rodata.IfxScu_PinMap.IfxScu_EMGSTOPB_P21_2_IN',data,rom,cluster('IfxScu_EMGSTOPB_P21_2_IN')
	.sect	'.rodata.IfxScu_PinMap.IfxScu_EMGSTOPB_P21_2_IN'
	.global	IfxScu_EMGSTOPB_P21_2_IN
	.align	4
IfxScu_EMGSTOPB_P21_2_IN:	.type	object
	.size	IfxScu_EMGSTOPB_P21_2_IN,16
	.word	-268214272,-268189440
	.byte	2
	.space	3
	.byte	1
	.space	3
	.sdecl	'.rodata.IfxScu_PinMap.IfxScu_EVRWUPA_P14_1_IN',data,rom,cluster('IfxScu_EVRWUPA_P14_1_IN')
	.sect	'.rodata.IfxScu_PinMap.IfxScu_EVRWUPA_P14_1_IN'
	.global	IfxScu_EVRWUPA_P14_1_IN
	.align	4
IfxScu_EVRWUPA_P14_1_IN:	.type	object
	.size	IfxScu_EVRWUPA_P14_1_IN,16
	.word	-268214272,-268192768
	.byte	1
	.space	7
	.sdecl	'.rodata.IfxScu_PinMap.IfxScu_EVRWUPB_P15_1_IN',data,rom,cluster('IfxScu_EVRWUPB_P15_1_IN')
	.sect	'.rodata.IfxScu_PinMap.IfxScu_EVRWUPB_P15_1_IN'
	.global	IfxScu_EVRWUPB_P15_1_IN
	.align	4
IfxScu_EVRWUPB_P15_1_IN:	.type	object
	.size	IfxScu_EVRWUPB_P15_1_IN,16
	.word	-268214272,-268192512
	.byte	1
	.space	3
	.byte	1
	.space	3
	.sdecl	'.rodata.IfxScu_PinMap.IfxScu_EXTCLK0_P23_1_OUT',data,rom,cluster('IfxScu_EXTCLK0_P23_1_OUT')
	.sect	'.rodata.IfxScu_PinMap.IfxScu_EXTCLK0_P23_1_OUT'
	.global	IfxScu_EXTCLK0_P23_1_OUT
	.align	4
IfxScu_EXTCLK0_P23_1_OUT:	.type	object
	.size	IfxScu_EXTCLK0_P23_1_OUT,16
	.word	-268214272,-268188928
	.byte	1
	.space	3
	.byte	176
	.space	3
	.sdecl	'.rodata.IfxScu_PinMap.IfxScu_EXTCLK1_P11_12_OUT',data,rom,cluster('IfxScu_EXTCLK1_P11_12_OUT')
	.sect	'.rodata.IfxScu_PinMap.IfxScu_EXTCLK1_P11_12_OUT'
	.global	IfxScu_EXTCLK1_P11_12_OUT
	.align	4
IfxScu_EXTCLK1_P11_12_OUT:	.type	object
	.size	IfxScu_EXTCLK1_P11_12_OUT,16
	.word	-268214272,-268193536
	.byte	12
	.space	3
	.byte	176
	.space	3
	.sdecl	'.rodata.IfxScu_PinMap.IfxScu_EXTCLK1_P32_4_OUT',data,rom,cluster('IfxScu_EXTCLK1_P32_4_OUT')
	.sect	'.rodata.IfxScu_PinMap.IfxScu_EXTCLK1_P32_4_OUT'
	.global	IfxScu_EXTCLK1_P32_4_OUT
	.align	4
IfxScu_EXTCLK1_P32_4_OUT:	.type	object
	.size	IfxScu_EXTCLK1_P32_4_OUT,16
	.word	-268214272,-268185088
	.byte	4
	.space	3
	.byte	176
	.space	3
	.sdecl	'.rodata.IfxScu_PinMap.IfxScu_HWCFG0DCLDO_P14_6_IN',data,rom,cluster('IfxScu_HWCFG0DCLDO_P14_6_IN')
	.sect	'.rodata.IfxScu_PinMap.IfxScu_HWCFG0DCLDO_P14_6_IN'
	.global	IfxScu_HWCFG0DCLDO_P14_6_IN
	.align	4
IfxScu_HWCFG0DCLDO_P14_6_IN:	.type	object
	.size	IfxScu_HWCFG0DCLDO_P14_6_IN,12
	.word	-268214272,-268192768
	.byte	6
	.space	3
	.sdecl	'.rodata.IfxScu_PinMap.IfxScu_HWCFG1EVR33_P14_5_IN',data,rom,cluster('IfxScu_HWCFG1EVR33_P14_5_IN')
	.sect	'.rodata.IfxScu_PinMap.IfxScu_HWCFG1EVR33_P14_5_IN'
	.global	IfxScu_HWCFG1EVR33_P14_5_IN
	.align	4
IfxScu_HWCFG1EVR33_P14_5_IN:	.type	object
	.size	IfxScu_HWCFG1EVR33_P14_5_IN,12
	.word	-268214272,-268192768
	.byte	5
	.space	3
	.sdecl	'.rodata.IfxScu_PinMap.IfxScu_HWCFG2EVR13_P14_2_IN',data,rom,cluster('IfxScu_HWCFG2EVR13_P14_2_IN')
	.sect	'.rodata.IfxScu_PinMap.IfxScu_HWCFG2EVR13_P14_2_IN'
	.global	IfxScu_HWCFG2EVR13_P14_2_IN
	.align	4
IfxScu_HWCFG2EVR13_P14_2_IN:	.type	object
	.size	IfxScu_HWCFG2EVR13_P14_2_IN,12
	.word	-268214272,-268192768
	.byte	2
	.space	3
	.sdecl	'.rodata.IfxScu_PinMap.IfxScu_HWCFG3_BMI_P14_3_IN',data,rom,cluster('IfxScu_HWCFG3_BMI_P14_3_IN')
	.sect	'.rodata.IfxScu_PinMap.IfxScu_HWCFG3_BMI_P14_3_IN'
	.global	IfxScu_HWCFG3_BMI_P14_3_IN
	.align	4
IfxScu_HWCFG3_BMI_P14_3_IN:	.type	object
	.size	IfxScu_HWCFG3_BMI_P14_3_IN,12
	.word	-268214272,-268192768
	.byte	3
	.space	3
	.sdecl	'.rodata.IfxScu_PinMap.IfxScu_HWCFG4_P10_5_IN',data,rom,cluster('IfxScu_HWCFG4_P10_5_IN')
	.sect	'.rodata.IfxScu_PinMap.IfxScu_HWCFG4_P10_5_IN'
	.global	IfxScu_HWCFG4_P10_5_IN
	.align	4
IfxScu_HWCFG4_P10_5_IN:	.type	object
	.size	IfxScu_HWCFG4_P10_5_IN,12
	.word	-268214272,-268193792
	.byte	5
	.space	3
	.sdecl	'.rodata.IfxScu_PinMap.IfxScu_HWCFG5_P10_6_IN',data,rom,cluster('IfxScu_HWCFG5_P10_6_IN')
	.sect	'.rodata.IfxScu_PinMap.IfxScu_HWCFG5_P10_6_IN'
	.global	IfxScu_HWCFG5_P10_6_IN
	.align	4
IfxScu_HWCFG5_P10_6_IN:	.type	object
	.size	IfxScu_HWCFG5_P10_6_IN,12
	.word	-268214272,-268193792
	.byte	6
	.space	3
	.sdecl	'.rodata.IfxScu_PinMap.IfxScu_HWCFG6_P14_4_IN',data,rom,cluster('IfxScu_HWCFG6_P14_4_IN')
	.sect	'.rodata.IfxScu_PinMap.IfxScu_HWCFG6_P14_4_IN'
	.global	IfxScu_HWCFG6_P14_4_IN
	.align	4
IfxScu_HWCFG6_P14_4_IN:	.type	object
	.size	IfxScu_HWCFG6_P14_4_IN,12
	.word	-268214272,-268192768
	.byte	4
	.space	3
	.sdecl	'.rodata.IfxScu_PinMap.IfxScu_REQ0_P15_4_IN',data,rom,cluster('IfxScu_REQ0_P15_4_IN')
	.sect	'.rodata.IfxScu_PinMap.IfxScu_REQ0_P15_4_IN'
	.global	IfxScu_REQ0_P15_4_IN
	.align	4
IfxScu_REQ0_P15_4_IN:	.type	object
	.size	IfxScu_REQ0_P15_4_IN,20
	.word	-268214272
	.space	4
	.word	-268192512
	.byte	4
	.space	7
	.sdecl	'.rodata.IfxScu_PinMap.IfxScu_REQ10_P14_3_IN',data,rom,cluster('IfxScu_REQ10_P14_3_IN')
	.sect	'.rodata.IfxScu_PinMap.IfxScu_REQ10_P14_3_IN'
	.global	IfxScu_REQ10_P14_3_IN
	.align	4
IfxScu_REQ10_P14_3_IN:	.type	object
	.size	IfxScu_REQ10_P14_3_IN,20
	.word	-268214272
	.byte	1
	.space	3
	.word	-268192768
	.byte	3
	.space	7
	.sdecl	'.rodata.IfxScu_PinMap.IfxScu_REQ11_P20_9_IN',data,rom,cluster('IfxScu_REQ11_P20_9_IN')
	.sect	'.rodata.IfxScu_PinMap.IfxScu_REQ11_P20_9_IN'
	.global	IfxScu_REQ11_P20_9_IN
	.align	4
IfxScu_REQ11_P20_9_IN:	.type	object
	.size	IfxScu_REQ11_P20_9_IN,20
	.word	-268214272
	.byte	7
	.space	3
	.word	-268189696
	.byte	9
	.space	7
	.sdecl	'.rodata.IfxScu_PinMap.IfxScu_REQ12_P11_10_IN',data,rom,cluster('IfxScu_REQ12_P11_10_IN')
	.sect	'.rodata.IfxScu_PinMap.IfxScu_REQ12_P11_10_IN'
	.global	IfxScu_REQ12_P11_10_IN
	.align	4
IfxScu_REQ12_P11_10_IN:	.type	object
	.size	IfxScu_REQ12_P11_10_IN,20
	.word	-268214272
	.byte	6
	.space	3
	.word	-268193536
	.byte	10
	.space	3
	.byte	3
	.space	3
	.sdecl	'.rodata.IfxScu_PinMap.IfxScu_REQ13_P15_5_IN',data,rom,cluster('IfxScu_REQ13_P15_5_IN')
	.sect	'.rodata.IfxScu_PinMap.IfxScu_REQ13_P15_5_IN'
	.global	IfxScu_REQ13_P15_5_IN
	.align	4
IfxScu_REQ13_P15_5_IN:	.type	object
	.size	IfxScu_REQ13_P15_5_IN,20
	.word	-268214272
	.byte	4
	.space	3
	.word	-268192512
	.byte	5
	.space	3
	.byte	3
	.space	3
	.sdecl	'.rodata.IfxScu_PinMap.IfxScu_REQ14_P02_1_IN',data,rom,cluster('IfxScu_REQ14_P02_1_IN')
	.sect	'.rodata.IfxScu_PinMap.IfxScu_REQ14_P02_1_IN'
	.global	IfxScu_REQ14_P02_1_IN
	.align	4
IfxScu_REQ14_P02_1_IN:	.type	object
	.size	IfxScu_REQ14_P02_1_IN,20
	.word	-268214272
	.byte	2
	.space	3
	.word	-268197376
	.byte	1
	.space	3
	.byte	1
	.space	3
	.sdecl	'.rodata.IfxScu_PinMap.IfxScu_REQ15_P14_1_IN',data,rom,cluster('IfxScu_REQ15_P14_1_IN')
	.sect	'.rodata.IfxScu_PinMap.IfxScu_REQ15_P14_1_IN'
	.global	IfxScu_REQ15_P14_1_IN
	.align	4
IfxScu_REQ15_P14_1_IN:	.type	object
	.size	IfxScu_REQ15_P14_1_IN,20
	.word	-268214272
	.byte	3
	.space	3
	.word	-268192768
	.byte	1
	.space	3
	.byte	1
	.space	3
	.sdecl	'.rodata.IfxScu_PinMap.IfxScu_REQ16_P15_1_IN',data,rom,cluster('IfxScu_REQ16_P15_1_IN')
	.sect	'.rodata.IfxScu_PinMap.IfxScu_REQ16_P15_1_IN'
	.global	IfxScu_REQ16_P15_1_IN
	.align	4
IfxScu_REQ16_P15_1_IN:	.type	object
	.size	IfxScu_REQ16_P15_1_IN,20
	.word	-268214272
	.byte	7
	.space	3
	.word	-268192512
	.byte	1
	.space	3
	.byte	2
	.space	3
	.sdecl	'.rodata.IfxScu_PinMap.IfxScu_REQ1_P15_8_IN',data,rom,cluster('IfxScu_REQ1_P15_8_IN')
	.sect	'.rodata.IfxScu_PinMap.IfxScu_REQ1_P15_8_IN'
	.global	IfxScu_REQ1_P15_8_IN
	.align	4
IfxScu_REQ1_P15_8_IN:	.type	object
	.size	IfxScu_REQ1_P15_8_IN,20
	.word	-268214272
	.byte	5
	.space	3
	.word	-268192512
	.byte	8
	.space	7
	.sdecl	'.rodata.IfxScu_PinMap.IfxScu_REQ2_P10_2_IN',data,rom,cluster('IfxScu_REQ2_P10_2_IN')
	.sect	'.rodata.IfxScu_PinMap.IfxScu_REQ2_P10_2_IN'
	.global	IfxScu_REQ2_P10_2_IN
	.align	4
IfxScu_REQ2_P10_2_IN:	.type	object
	.size	IfxScu_REQ2_P10_2_IN,20
	.word	-268214272
	.byte	2
	.space	3
	.word	-268193792
	.byte	2
	.space	7
	.sdecl	'.rodata.IfxScu_PinMap.IfxScu_REQ3_P10_3_IN',data,rom,cluster('IfxScu_REQ3_P10_3_IN')
	.sect	'.rodata.IfxScu_PinMap.IfxScu_REQ3_P10_3_IN'
	.global	IfxScu_REQ3_P10_3_IN
	.align	4
IfxScu_REQ3_P10_3_IN:	.type	object
	.size	IfxScu_REQ3_P10_3_IN,20
	.word	-268214272
	.byte	3
	.space	3
	.word	-268193792
	.byte	3
	.space	7
	.sdecl	'.rodata.IfxScu_PinMap.IfxScu_REQ4_P10_7_IN',data,rom,cluster('IfxScu_REQ4_P10_7_IN')
	.sect	'.rodata.IfxScu_PinMap.IfxScu_REQ4_P10_7_IN'
	.global	IfxScu_REQ4_P10_7_IN
	.align	4
IfxScu_REQ4_P10_7_IN:	.type	object
	.size	IfxScu_REQ4_P10_7_IN,20
	.word	-268214272
	.space	4
	.word	-268193792
	.byte	7
	.space	3
	.byte	2
	.space	3
	.sdecl	'.rodata.IfxScu_PinMap.IfxScu_REQ5_P10_8_IN',data,rom,cluster('IfxScu_REQ5_P10_8_IN')
	.sect	'.rodata.IfxScu_PinMap.IfxScu_REQ5_P10_8_IN'
	.global	IfxScu_REQ5_P10_8_IN
	.align	4
IfxScu_REQ5_P10_8_IN:	.type	object
	.size	IfxScu_REQ5_P10_8_IN,20
	.word	-268214272
	.byte	1
	.space	3
	.word	-268193792
	.byte	8
	.space	3
	.byte	2
	.space	3
	.sdecl	'.rodata.IfxScu_PinMap.IfxScu_REQ6_P02_0_IN',data,rom,cluster('IfxScu_REQ6_P02_0_IN')
	.sect	'.rodata.IfxScu_PinMap.IfxScu_REQ6_P02_0_IN'
	.global	IfxScu_REQ6_P02_0_IN
	.align	4
IfxScu_REQ6_P02_0_IN:	.type	object
	.size	IfxScu_REQ6_P02_0_IN,20
	.word	-268214272
	.byte	3
	.space	3
	.word	-268197376
	.space	4
	.byte	2
	.space	3
	.sdecl	'.rodata.IfxScu_PinMap.IfxScu_REQ7_P00_4_IN',data,rom,cluster('IfxScu_REQ7_P00_4_IN')
	.sect	'.rodata.IfxScu_PinMap.IfxScu_REQ7_P00_4_IN'
	.global	IfxScu_REQ7_P00_4_IN
	.align	4
IfxScu_REQ7_P00_4_IN:	.type	object
	.size	IfxScu_REQ7_P00_4_IN,20
	.word	-268214272
	.byte	2
	.space	3
	.word	-268197888
	.byte	4
	.space	3
	.byte	2
	.space	3
	.sdecl	'.rodata.IfxScu_PinMap.IfxScu_REQ8_P33_7_IN',data,rom,cluster('IfxScu_REQ8_P33_7_IN')
	.sect	'.rodata.IfxScu_PinMap.IfxScu_REQ8_P33_7_IN'
	.global	IfxScu_REQ8_P33_7_IN
	.align	4
IfxScu_REQ8_P33_7_IN:	.type	object
	.size	IfxScu_REQ8_P33_7_IN,20
	.word	-268214272
	.byte	4
	.space	3
	.word	-268184832
	.byte	7
	.space	7
	.sdecl	'.rodata.IfxScu_PinMap.IfxScu_REQ9_P20_0_IN',data,rom,cluster('IfxScu_REQ9_P20_0_IN')
	.sect	'.rodata.IfxScu_PinMap.IfxScu_REQ9_P20_0_IN'
	.global	IfxScu_REQ9_P20_0_IN
	.align	4
IfxScu_REQ9_P20_0_IN:	.type	object
	.size	IfxScu_REQ9_P20_0_IN,20
	.word	-268214272
	.byte	6
	.space	3
	.word	-268189696
	.space	8
	.sdecl	'.rodata.IfxScu_PinMap.IfxScu_WDT0LCK_P20_8_OUT',data,rom,cluster('IfxScu_WDT0LCK_P20_8_OUT')
	.sect	'.rodata.IfxScu_PinMap.IfxScu_WDT0LCK_P20_8_OUT'
	.global	IfxScu_WDT0LCK_P20_8_OUT
	.align	4
IfxScu_WDT0LCK_P20_8_OUT:	.type	object
	.size	IfxScu_WDT0LCK_P20_8_OUT,16
	.word	-268214272,-268189696
	.byte	8
	.space	3
	.byte	176
	.space	3
	.sdecl	'.rodata.IfxScu_PinMap.IfxScu_WDT1LCK_P20_7_OUT',data,rom,cluster('IfxScu_WDT1LCK_P20_7_OUT')
	.sect	'.rodata.IfxScu_PinMap.IfxScu_WDT1LCK_P20_7_OUT'
	.global	IfxScu_WDT1LCK_P20_7_OUT
	.align	4
IfxScu_WDT1LCK_P20_7_OUT:	.type	object
	.size	IfxScu_WDT1LCK_P20_7_OUT,16
	.word	-268214272,-268189696
	.byte	7
	.space	3
	.byte	176
	.space	3
	.sdecl	'.rodata.IfxScu_PinMap.IfxScu_WDTSLCK_P20_9_OUT',data,rom,cluster('IfxScu_WDTSLCK_P20_9_OUT')
	.sect	'.rodata.IfxScu_PinMap.IfxScu_WDTSLCK_P20_9_OUT'
	.global	IfxScu_WDTSLCK_P20_9_OUT
	.align	4
IfxScu_WDTSLCK_P20_9_OUT:	.type	object
	.size	IfxScu_WDTSLCK_P20_9_OUT,16
	.word	-268214272,-268189696
	.byte	9
	.space	3
	.byte	176
	.space	3
	.sdecl	'.data.IfxScu_PinMap.IfxScu_Dcdcsync_Out_pinTable',data,cluster('IfxScu_Dcdcsync_Out_pinTable')
	.sect	'.data.IfxScu_PinMap.IfxScu_Dcdcsync_Out_pinTable'
	.global	IfxScu_Dcdcsync_Out_pinTable
	.align	4
IfxScu_Dcdcsync_Out_pinTable:	.type	object
	.size	IfxScu_Dcdcsync_Out_pinTable,8
	.word	IfxScu_DCDCSYNC_P32_2_OUT,IfxScu_DCDCSYNC_P33_13_OUT
	.sdecl	'.data.IfxScu_PinMap.IfxScu_Emgstop_In_pinTable',data,cluster('IfxScu_Emgstop_In_pinTable')
	.sect	'.data.IfxScu_PinMap.IfxScu_Emgstop_In_pinTable'
	.global	IfxScu_Emgstop_In_pinTable
	.align	4
IfxScu_Emgstop_In_pinTable:	.type	object
	.size	IfxScu_Emgstop_In_pinTable,8
	.word	IfxScu_EMGSTOPA_P33_8_IN,IfxScu_EMGSTOPB_P21_2_IN
	.sdecl	'.data.IfxScu_PinMap.IfxScu_Evrwup_In_pinTable',data,cluster('IfxScu_Evrwup_In_pinTable')
	.sect	'.data.IfxScu_PinMap.IfxScu_Evrwup_In_pinTable'
	.global	IfxScu_Evrwup_In_pinTable
	.align	4
IfxScu_Evrwup_In_pinTable:	.type	object
	.size	IfxScu_Evrwup_In_pinTable,8
	.word	IfxScu_EVRWUPA_P14_1_IN,IfxScu_EVRWUPB_P15_1_IN
	.sdecl	'.data.IfxScu_PinMap.IfxScu_Extclk_Out_pinTable',data,cluster('IfxScu_Extclk_Out_pinTable')
	.sect	'.data.IfxScu_PinMap.IfxScu_Extclk_Out_pinTable'
	.global	IfxScu_Extclk_Out_pinTable
	.align	4
IfxScu_Extclk_Out_pinTable:	.type	object
	.size	IfxScu_Extclk_Out_pinTable,12
	.word	IfxScu_EXTCLK1_P11_12_OUT,IfxScu_EXTCLK0_P23_1_OUT,IfxScu_EXTCLK1_P32_4_OUT
	.sdecl	'.data.IfxScu_PinMap.IfxScu_Hwcfg_In_pinTable',data,cluster('IfxScu_Hwcfg_In_pinTable')
	.sect	'.data.IfxScu_PinMap.IfxScu_Hwcfg_In_pinTable'
	.global	IfxScu_Hwcfg_In_pinTable
	.align	4
IfxScu_Hwcfg_In_pinTable:	.type	object
	.size	IfxScu_Hwcfg_In_pinTable,28
	.word	IfxScu_HWCFG4_P10_5_IN,IfxScu_HWCFG5_P10_6_IN,IfxScu_HWCFG2EVR13_P14_2_IN,IfxScu_HWCFG3_BMI_P14_3_IN
	.word	IfxScu_HWCFG6_P14_4_IN,IfxScu_HWCFG1EVR33_P14_5_IN,IfxScu_HWCFG0DCLDO_P14_6_IN
	.sdecl	'.data.IfxScu_PinMap.IfxScu_Req_In_pinTable',data,cluster('IfxScu_Req_In_pinTable')
	.sect	'.data.IfxScu_PinMap.IfxScu_Req_In_pinTable'
	.global	IfxScu_Req_In_pinTable
	.align	4
IfxScu_Req_In_pinTable:	.type	object
	.size	IfxScu_Req_In_pinTable,128
	.word	IfxScu_REQ0_P15_4_IN
	.space	4
	.word	IfxScu_REQ4_P10_7_IN
	.space	4
	.word	IfxScu_REQ10_P14_3_IN
	.space	4
	.word	IfxScu_REQ5_P10_8_IN
	.space	4
	.word	IfxScu_REQ2_P10_2_IN,IfxScu_REQ14_P02_1_IN,IfxScu_REQ7_P00_4_IN
	.space	4
	.word	IfxScu_REQ3_P10_3_IN,IfxScu_REQ15_P14_1_IN,IfxScu_REQ6_P02_0_IN
	.space	4
	.word	IfxScu_REQ8_P33_7_IN
	.space	8
	.word	IfxScu_REQ13_P15_5_IN,IfxScu_REQ1_P15_8_IN
	.space	12
	.word	IfxScu_REQ9_P20_0_IN
	.space	8
	.word	IfxScu_REQ12_P11_10_IN,IfxScu_REQ11_P20_9_IN
	.space	4
	.word	IfxScu_REQ16_P15_1_IN
	.space	4
	.sdecl	'.data.IfxScu_PinMap.IfxScu_Wdtlck_Out_pinTable',data,cluster('IfxScu_Wdtlck_Out_pinTable')
	.sect	'.data.IfxScu_PinMap.IfxScu_Wdtlck_Out_pinTable'
	.global	IfxScu_Wdtlck_Out_pinTable
	.align	4
IfxScu_Wdtlck_Out_pinTable:	.type	object
	.size	IfxScu_Wdtlck_Out_pinTable,12
	.word	IfxScu_WDT1LCK_P20_7_OUT,IfxScu_WDT0LCK_P20_8_OUT,IfxScu_WDTSLCK_P20_9_OUT
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L3:
	.word	39442
	.half	3
	.word	.L4
	.byte	4
.L2:
	.byte	1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxScu_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L5
	.byte	2,1,1,3
	.word	237
	.byte	4
	.byte	'Ifx__jump_and_link',0,3,1,61,17,1,1,5
	.byte	'fun',0,1,61,43
	.word	240
	.byte	6,0,7
	.byte	'__fract',0,4,128,1,7
	.byte	'float',0,4,4,8
	.byte	'Ifx__float_to_fract',0,3,1,152,2,18
	.word	285
	.byte	1,1,5
	.byte	'a',0,1,152,2,44
	.word	297
	.byte	6,0,7
	.byte	'unsigned long long int',0,8,7,9
	.byte	'void',0,3
	.word	377
	.byte	8
	.byte	'__ld64',0,3,2,135,1,19
	.word	351
	.byte	1,1,5
	.byte	'addr',0,2,135,1,32
	.word	383
	.byte	6,0,4
	.byte	'__st64',0,3,2,143,1,17,1,1,5
	.byte	'addr',0,2,143,1,30
	.word	383
	.byte	5
	.byte	'value',0,2,143,1,43
	.word	351
	.byte	6,0,7
	.byte	'unsigned int',0,4,7,7
	.byte	'int',0,4,5,7
	.byte	'unsigned char',0,1,8,7
	.byte	'unsigned short int',0,2,7,10
	.byte	'_Ifx_P_OUT_Bits',0,4,143,3,16,4,11
	.byte	'P0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	492
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	492
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	492
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	16,0,2,35,2,0,12,4,181,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	531
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMR_Bits',0,4,169,2,16,4,11
	.byte	'PS0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	492
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	492
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	492
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'PCL0',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	492
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	492
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	492
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	492
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	492
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	492
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	492
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	492
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	492
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	492
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	492
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	492
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	492
	.byte	1,0,2,35,3,0,12,4,133,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	847
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ID_Bits',0,4,110,16,4,11
	.byte	'MODREV',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	509
	.byte	16,0,2,35,2,0,12,4,148,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1418
	.byte	4,2,35,0,0,14,4
	.word	492
	.byte	15,3,0,10
	.byte	'_Ifx_P_IOCR0_Bits',0,4,140,1,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	3,5,2,35,0,11
	.byte	'PC0',0,1
	.word	492
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	492
	.byte	3,5,2,35,1,11
	.byte	'PC1',0,1
	.word	492
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	492
	.byte	3,5,2,35,2,11
	.byte	'PC2',0,1
	.word	492
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	3,5,2,35,3,11
	.byte	'PC3',0,1
	.word	492
	.byte	5,0,2,35,3,0,12,4,164,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1546
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR4_Bits',0,4,166,1,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	3,5,2,35,0,11
	.byte	'PC4',0,1
	.word	492
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	492
	.byte	3,5,2,35,1,11
	.byte	'PC5',0,1
	.word	492
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	492
	.byte	3,5,2,35,2,11
	.byte	'PC6',0,1
	.word	492
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	3,5,2,35,3,11
	.byte	'PC7',0,1
	.word	492
	.byte	5,0,2,35,3,0,12,4,180,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1761
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR8_Bits',0,4,179,1,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	3,5,2,35,0,11
	.byte	'PC8',0,1
	.word	492
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	492
	.byte	3,5,2,35,1,11
	.byte	'PC9',0,1
	.word	492
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	492
	.byte	3,5,2,35,2,11
	.byte	'PC10',0,1
	.word	492
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	3,5,2,35,3,11
	.byte	'PC11',0,1
	.word	492
	.byte	5,0,2,35,3,0,12,4,188,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1976
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR12_Bits',0,4,153,1,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	3,5,2,35,0,11
	.byte	'PC12',0,1
	.word	492
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	492
	.byte	3,5,2,35,1,11
	.byte	'PC13',0,1
	.word	492
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	492
	.byte	3,5,2,35,2,11
	.byte	'PC14',0,1
	.word	492
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	3,5,2,35,3,11
	.byte	'PC15',0,1
	.word	492
	.byte	5,0,2,35,3,0,12,4,172,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2193
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IN_Bits',0,4,118,16,4,11
	.byte	'P0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	492
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	492
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	492
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	16,0,2,35,2,0,12,4,156,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2413
	.byte	4,2,35,0,0,14,24
	.word	492
	.byte	15,23,0,10
	.byte	'_Ifx_P_PDR0_Bits',0,4,205,3,16,4,11
	.byte	'PD0',0,1
	.word	492
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	492
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'PD2',0,1
	.word	492
	.byte	3,5,2,35,1,11
	.byte	'PL2',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'PD3',0,1
	.word	492
	.byte	3,1,2,35,1,11
	.byte	'PL3',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'PD4',0,1
	.word	492
	.byte	3,5,2,35,2,11
	.byte	'PL4',0,1
	.word	492
	.byte	1,4,2,35,2,11
	.byte	'PD5',0,1
	.word	492
	.byte	3,1,2,35,2,11
	.byte	'PL5',0,1
	.word	492
	.byte	1,0,2,35,2,11
	.byte	'PD6',0,1
	.word	492
	.byte	3,5,2,35,3,11
	.byte	'PL6',0,1
	.word	492
	.byte	1,4,2,35,3,11
	.byte	'PD7',0,1
	.word	492
	.byte	3,1,2,35,3,11
	.byte	'PL7',0,1
	.word	492
	.byte	1,0,2,35,3,0,12,4,205,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2736
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PDR1_Bits',0,4,226,3,16,4,11
	.byte	'PD8',0,1
	.word	492
	.byte	3,5,2,35,0,11
	.byte	'PL8',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'PD9',0,1
	.word	492
	.byte	3,1,2,35,0,11
	.byte	'PL9',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'PD10',0,1
	.word	492
	.byte	3,5,2,35,1,11
	.byte	'PL10',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'PD11',0,1
	.word	492
	.byte	3,1,2,35,1,11
	.byte	'PL11',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'PD12',0,1
	.word	492
	.byte	3,5,2,35,2,11
	.byte	'PL12',0,1
	.word	492
	.byte	1,4,2,35,2,11
	.byte	'PD13',0,1
	.word	492
	.byte	3,1,2,35,2,11
	.byte	'PL13',0,1
	.word	492
	.byte	1,0,2,35,2,11
	.byte	'PD14',0,1
	.word	492
	.byte	3,5,2,35,3,11
	.byte	'PL14',0,1
	.word	492
	.byte	1,4,2,35,3,11
	.byte	'PD15',0,1
	.word	492
	.byte	3,1,2,35,3,11
	.byte	'PL15',0,1
	.word	492
	.byte	1,0,2,35,3,0,12,4,213,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3040
	.byte	4,2,35,0,0,14,8
	.word	492
	.byte	15,7,0,10
	.byte	'_Ifx_P_ESR_Bits',0,4,88,16,4,11
	.byte	'EN0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	492
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	492
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	492
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	16,0,2,35,2,0,12,4,140,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3365
	.byte	4,2,35,0,0,14,12
	.word	492
	.byte	15,11,0,10
	.byte	'_Ifx_P_PDISC_Bits',0,4,183,3,16,4,11
	.byte	'PDIS0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'PDIS2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'PDIS3',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'PDIS4',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'PDIS5',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'PDIS6',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'PDIS7',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'PDIS8',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'PDIS9',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'PDIS10',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'PDIS11',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'PDIS12',0,1
	.word	492
	.byte	1,3,2,35,1,11
	.byte	'PDIS13',0,1
	.word	492
	.byte	1,2,2,35,1,11
	.byte	'PDIS14',0,1
	.word	492
	.byte	1,1,2,35,1,11
	.byte	'PDIS15',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	16,0,2,35,2,0,12,4,197,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3705
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PCSR_Bits',0,4,165,3,16,4,11
	.byte	'SEL0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'SEL1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'SEL2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'SEL3',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'SEL4',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'SEL5',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'SEL6',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'SEL7',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	492
	.byte	2,6,2,35,1,11
	.byte	'SEL10',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'SEL11',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	469
	.byte	19,1,2,35,0,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,12,4,189,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4071
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR0_Bits',0,4,206,2,16,4,11
	.byte	'PS0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	469
	.byte	28,0,2,35,0,0,12,4,149,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4357
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR4_Bits',0,4,227,2,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	4,4,2,35,0,11
	.byte	'PS4',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	469
	.byte	24,0,2,35,0,0,12,4,165,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4504
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR8_Bits',0,4,238,2,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'PS8',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	469
	.byte	20,0,2,35,0,0,12,4,173,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4673
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR12_Bits',0,4,216,2,16,4,11
	.byte	'reserved_0',0,2
	.word	509
	.byte	12,4,2,35,0,11
	.byte	'PS12',0,1
	.word	492
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	492
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	492
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	16,0,2,35,2,0,12,4,157,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4845
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR0_Bits',0,4,232,1,16,4,11
	.byte	'reserved_0',0,2
	.word	509
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	492
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	492
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	509
	.byte	12,0,2,35,2,0,12,4,229,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5020
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR4_Bits',0,4,253,1,16,4,11
	.byte	'reserved_0',0,4
	.word	469
	.byte	20,12,2,35,0,11
	.byte	'PCL4',0,1
	.word	492
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	492
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	492
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	492
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	8,0,2,35,3,0,12,4,245,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5194
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR8_Bits',0,4,136,2,16,4,11
	.byte	'reserved_0',0,4
	.word	469
	.byte	24,8,2,35,0,11
	.byte	'PCL8',0,1
	.word	492
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	492
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	492
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	492
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	492
	.byte	4,0,2,35,3,0,12,4,253,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5368
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR12_Bits',0,4,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	469
	.byte	28,4,2,35,0,11
	.byte	'PCL12',0,1
	.word	492
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	492
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	492
	.byte	1,0,2,35,3,0,12,4,237,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5544
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR_Bits',0,4,249,2,16,4,11
	.byte	'PS0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	492
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	492
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	492
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	16,0,2,35,2,0,12,4,141,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5700
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR_Bits',0,4,147,2,16,4,11
	.byte	'reserved_0',0,2
	.word	509
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	492
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	492
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	492
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	492
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	492
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	492
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	492
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	492
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	492
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	492
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	492
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	492
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	492
	.byte	1,0,2,35,3,0,12,4,221,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6033
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR0_Bits',0,4,192,1,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	469
	.byte	30,0,2,35,0,0,12,4,196,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6381
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_Bits',0,4,200,1,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	469
	.byte	30,0,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_P21_Bits',0,4,208,1,16,4,11
	.byte	'RDIS_CTRL',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'RX_DIS',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'TERM',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'LRXTERM',0,1
	.word	492
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	469
	.byte	24,0,2,35,0,0,12,4,204,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6505
	.byte	4,2,35,0,13
	.byte	'B_P21',0
	.word	6589
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR2_Bits',0,4,218,1,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'LVDSR',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'LVDSRL',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	492
	.byte	2,4,2,35,1,11
	.byte	'TDIS_CTRL',0,1
	.word	492
	.byte	1,3,2,35,1,11
	.byte	'TX_DIS',0,1
	.word	492
	.byte	1,2,2,35,1,11
	.byte	'TX_PD',0,1
	.word	492
	.byte	1,1,2,35,1,11
	.byte	'TX_PWDPD',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	16,0,2,35,2,0,12,4,213,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6769
	.byte	4,2,35,0,0,14,76
	.word	492
	.byte	15,75,0,10
	.byte	'_Ifx_P_ACCEN1_Bits',0,4,82,16,4,11
	.byte	'reserved_0',0,4
	.word	469
	.byte	32,0,2,35,0,0,12,4,132,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7022
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ACCEN0_Bits',0,4,45,16,4,11
	.byte	'EN0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	492
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	492
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	492
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	492
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	492
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	492
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	492
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	492
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	492
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	492
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	492
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	492
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	492
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	492
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	492
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	492
	.byte	1,0,2,35,3,0,12,4,252,3,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7109
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P',0,4,229,5,25,128,2,13
	.byte	'OUT',0
	.word	807
	.byte	4,2,35,0,13
	.byte	'OMR',0
	.word	1378
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	1497
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	1537
	.byte	4,2,35,12,13
	.byte	'IOCR0',0
	.word	1721
	.byte	4,2,35,16,13
	.byte	'IOCR4',0
	.word	1936
	.byte	4,2,35,20,13
	.byte	'IOCR8',0
	.word	2153
	.byte	4,2,35,24,13
	.byte	'IOCR12',0
	.word	2373
	.byte	4,2,35,28,13
	.byte	'reserved_20',0
	.word	1537
	.byte	4,2,35,32,13
	.byte	'IN',0
	.word	2687
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	2727
	.byte	24,2,35,40,13
	.byte	'PDR0',0
	.word	3000
	.byte	4,2,35,64,13
	.byte	'PDR1',0
	.word	3316
	.byte	4,2,35,68,13
	.byte	'reserved_48',0
	.word	3356
	.byte	8,2,35,72,13
	.byte	'ESR',0
	.word	3656
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	3696
	.byte	12,2,35,84,13
	.byte	'PDISC',0
	.word	4031
	.byte	4,2,35,96,13
	.byte	'PCSR',0
	.word	4317
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	3356
	.byte	8,2,35,104,13
	.byte	'OMSR0',0
	.word	4464
	.byte	4,2,35,112,13
	.byte	'OMSR4',0
	.word	4633
	.byte	4,2,35,116,13
	.byte	'OMSR8',0
	.word	4805
	.byte	4,2,35,120,13
	.byte	'OMSR12',0
	.word	4980
	.byte	4,2,35,124,13
	.byte	'OMCR0',0
	.word	5154
	.byte	4,3,35,128,1,13
	.byte	'OMCR4',0
	.word	5328
	.byte	4,3,35,132,1,13
	.byte	'OMCR8',0
	.word	5504
	.byte	4,3,35,136,1,13
	.byte	'OMCR12',0
	.word	5660
	.byte	4,3,35,140,1,13
	.byte	'OMSR',0
	.word	5993
	.byte	4,3,35,144,1,13
	.byte	'OMCR',0
	.word	6341
	.byte	4,3,35,148,1,13
	.byte	'reserved_98',0
	.word	3356
	.byte	8,3,35,152,1,13
	.byte	'LPCR0',0
	.word	6465
	.byte	4,3,35,160,1,13
	.byte	'LPCR1',0
	.word	6714
	.byte	4,3,35,164,1,13
	.byte	'LPCR2',0
	.word	6973
	.byte	4,3,35,168,1,13
	.byte	'reserved_A4',0
	.word	7013
	.byte	76,3,35,172,1,13
	.byte	'ACCEN1',0
	.word	7069
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	7636
	.byte	4,3,35,252,1,0,16
	.word	7676
	.byte	3
	.word	8279
	.byte	17,3,172,1,9,4,18
	.byte	'IfxPort_State_notChanged',0,0,18
	.byte	'IfxPort_State_high',0,1,18
	.byte	'IfxPort_State_low',0,128,128,4,18
	.byte	'IfxPort_State_toggled',0,129,128,4,0,4
	.byte	'IfxPort_setPinState',0,3,3,208,4,17,1,1,5
	.byte	'port',0,3,208,4,44
	.word	8284
	.byte	5
	.byte	'pinIndex',0,3,208,4,56
	.word	492
	.byte	5
	.byte	'action',0,3,208,4,80
	.word	8289
	.byte	6,0,19
	.word	245
	.byte	20
	.word	271
	.byte	6,0,19
	.word	306
	.byte	20
	.word	338
	.byte	6,0,19
	.word	388
	.byte	20
	.word	407
	.byte	6,0,19
	.word	423
	.byte	20
	.word	438
	.byte	20
	.word	452
	.byte	6,0,19
	.word	8392
	.byte	20
	.word	8420
	.byte	20
	.word	8434
	.byte	20
	.word	8452
	.byte	6,0,7
	.byte	'short int',0,2,5,21
	.byte	'__wchar_t',0,5,1,1
	.word	8545
	.byte	21
	.byte	'__size_t',0,5,1,1
	.word	469
	.byte	21
	.byte	'__ptrdiff_t',0,5,1,1
	.word	485
	.byte	22,1,3
	.word	8613
	.byte	21
	.byte	'__codeptr',0,5,1,1
	.word	8615
	.byte	10
	.byte	'_Ifx_SCU_ACCEN0_Bits',0,6,45,16,4,11
	.byte	'EN0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	492
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	492
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	492
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	492
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	492
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	492
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	492
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	492
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	492
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	492
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	492
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	492
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	492
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	492
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	492
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_ACCEN0_Bits',0,6,79,3
	.word	8638
	.byte	10
	.byte	'_Ifx_SCU_ACCEN1_Bits',0,6,82,16,4,11
	.byte	'reserved_0',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_SCU_ACCEN1_Bits',0,6,85,3
	.word	9195
	.byte	10
	.byte	'_Ifx_SCU_ARSTDIS_Bits',0,6,88,16,4,11
	.byte	'STM0DIS',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'STM1DIS',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'STM2DIS',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	469
	.byte	29,0,2,35,0,0,21
	.byte	'Ifx_SCU_ARSTDIS_Bits',0,6,94,3
	.word	9272
	.byte	10
	.byte	'_Ifx_SCU_CCUCON0_Bits',0,6,97,16,4,11
	.byte	'BAUD1DIV',0,1
	.word	492
	.byte	4,4,2,35,0,11
	.byte	'BAUD2DIV',0,1
	.word	492
	.byte	4,0,2,35,0,11
	.byte	'SRIDIV',0,1
	.word	492
	.byte	4,4,2,35,1,11
	.byte	'LPDIV',0,1
	.word	492
	.byte	4,0,2,35,1,11
	.byte	'SPBDIV',0,1
	.word	492
	.byte	4,4,2,35,2,11
	.byte	'FSI2DIV',0,1
	.word	492
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	492
	.byte	2,0,2,35,2,11
	.byte	'FSIDIV',0,1
	.word	492
	.byte	2,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	492
	.byte	2,4,2,35,3,11
	.byte	'CLKSEL',0,1
	.word	492
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON0_Bits',0,6,111,3
	.word	9408
	.byte	10
	.byte	'_Ifx_SCU_CCUCON1_Bits',0,6,114,16,4,11
	.byte	'CANDIV',0,1
	.word	492
	.byte	4,4,2,35,0,11
	.byte	'ERAYDIV',0,1
	.word	492
	.byte	4,0,2,35,0,11
	.byte	'STMDIV',0,1
	.word	492
	.byte	4,4,2,35,1,11
	.byte	'GTMDIV',0,1
	.word	492
	.byte	4,0,2,35,1,11
	.byte	'ETHDIV',0,1
	.word	492
	.byte	4,4,2,35,2,11
	.byte	'ASCLINFDIV',0,1
	.word	492
	.byte	4,0,2,35,2,11
	.byte	'ASCLINSDIV',0,1
	.word	492
	.byte	4,4,2,35,3,11
	.byte	'INSEL',0,1
	.word	492
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON1_Bits',0,6,126,3
	.word	9688
	.byte	10
	.byte	'_Ifx_SCU_CCUCON2_Bits',0,6,129,1,16,4,11
	.byte	'BBBDIV',0,1
	.word	492
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	469
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON2_Bits',0,6,135,1,3
	.word	9926
	.byte	10
	.byte	'_Ifx_SCU_CCUCON3_Bits',0,6,138,1,16,4,11
	.byte	'PLLDIV',0,1
	.word	492
	.byte	6,2,2,35,0,11
	.byte	'PLLSEL',0,1
	.word	492
	.byte	2,0,2,35,0,11
	.byte	'PLLERAYDIV',0,1
	.word	492
	.byte	6,2,2,35,1,11
	.byte	'PLLERAYSEL',0,1
	.word	492
	.byte	2,0,2,35,1,11
	.byte	'SRIDIV',0,1
	.word	492
	.byte	6,2,2,35,2,11
	.byte	'SRISEL',0,1
	.word	492
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	492
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON3_Bits',0,6,150,1,3
	.word	10054
	.byte	10
	.byte	'_Ifx_SCU_CCUCON4_Bits',0,6,153,1,16,4,11
	.byte	'SPBDIV',0,1
	.word	492
	.byte	6,2,2,35,0,11
	.byte	'SPBSEL',0,1
	.word	492
	.byte	2,0,2,35,0,11
	.byte	'GTMDIV',0,1
	.word	492
	.byte	6,2,2,35,1,11
	.byte	'GTMSEL',0,1
	.word	492
	.byte	2,0,2,35,1,11
	.byte	'STMDIV',0,1
	.word	492
	.byte	6,2,2,35,2,11
	.byte	'STMSEL',0,1
	.word	492
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	492
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON4_Bits',0,6,165,1,3
	.word	10297
	.byte	10
	.byte	'_Ifx_SCU_CCUCON5_Bits',0,6,168,1,16,4,11
	.byte	'MAXDIV',0,1
	.word	492
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	469
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON5_Bits',0,6,174,1,3
	.word	10532
	.byte	10
	.byte	'_Ifx_SCU_CCUCON6_Bits',0,6,177,1,16,4,11
	.byte	'CPU0DIV',0,1
	.word	492
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	469
	.byte	26,0,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON6_Bits',0,6,181,1,3
	.word	10660
	.byte	10
	.byte	'_Ifx_SCU_CCUCON7_Bits',0,6,184,1,16,4,11
	.byte	'CPU1DIV',0,1
	.word	492
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	469
	.byte	26,0,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON7_Bits',0,6,188,1,3
	.word	10760
	.byte	10
	.byte	'_Ifx_SCU_CHIPID_Bits',0,6,191,1,16,4,11
	.byte	'CHREV',0,1
	.word	492
	.byte	6,2,2,35,0,11
	.byte	'CHTEC',0,1
	.word	492
	.byte	2,0,2,35,0,11
	.byte	'CHID',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'EEA',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'UCODE',0,1
	.word	492
	.byte	7,0,2,35,2,11
	.byte	'FSIZE',0,1
	.word	492
	.byte	4,4,2,35,3,11
	.byte	'SP',0,1
	.word	492
	.byte	2,2,2,35,3,11
	.byte	'SEC',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CHIPID_Bits',0,6,202,1,3
	.word	10860
	.byte	10
	.byte	'_Ifx_SCU_DTSCON_Bits',0,6,205,1,16,4,11
	.byte	'PWD',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'START',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	492
	.byte	2,4,2,35,0,11
	.byte	'CAL',0,4
	.word	469
	.byte	20,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	7,1,2,35,3,11
	.byte	'SLCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_DTSCON_Bits',0,6,213,1,3
	.word	11068
	.byte	10
	.byte	'_Ifx_SCU_DTSLIM_Bits',0,6,216,1,16,4,11
	.byte	'LOWER',0,2
	.word	509
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	492
	.byte	5,1,2,35,1,11
	.byte	'LLU',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'UPPER',0,2
	.word	509
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	492
	.byte	4,2,2,35,3,11
	.byte	'SLCK',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'UOF',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_DTSLIM_Bits',0,6,225,1,3
	.word	11233
	.byte	10
	.byte	'_Ifx_SCU_DTSSTAT_Bits',0,6,228,1,16,4,11
	.byte	'RESULT',0,2
	.word	509
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	492
	.byte	4,2,2,35,1,11
	.byte	'RDY',0,1
	.word	492
	.byte	1,1,2,35,1,11
	.byte	'BUSY',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_DTSSTAT_Bits',0,6,235,1,3
	.word	11416
	.byte	10
	.byte	'_Ifx_SCU_EICR_Bits',0,6,238,1,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	4,4,2,35,0,11
	.byte	'EXIS0',0,1
	.word	492
	.byte	3,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'FEN0',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'REN0',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'LDEN0',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'EIEN0',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'INP0',0,1
	.word	492
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	469
	.byte	5,12,2,35,0,11
	.byte	'EXIS1',0,1
	.word	492
	.byte	3,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	492
	.byte	1,0,2,35,2,11
	.byte	'FEN1',0,1
	.word	492
	.byte	1,7,2,35,3,11
	.byte	'REN1',0,1
	.word	492
	.byte	1,6,2,35,3,11
	.byte	'LDEN1',0,1
	.word	492
	.byte	1,5,2,35,3,11
	.byte	'EIEN1',0,1
	.word	492
	.byte	1,4,2,35,3,11
	.byte	'INP1',0,1
	.word	492
	.byte	3,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EICR_Bits',0,6,129,2,3
	.word	11570
	.byte	10
	.byte	'_Ifx_SCU_EIFR_Bits',0,6,132,2,16,4,11
	.byte	'INTF0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'INTF1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'INTF2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'INTF3',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'INTF4',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'INTF5',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'INTF6',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'INTF7',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	469
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_SCU_EIFR_Bits',0,6,143,2,3
	.word	11934
	.byte	10
	.byte	'_Ifx_SCU_EMSR_Bits',0,6,146,2,16,4,11
	.byte	'POL',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'MODE',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'ENON',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'PSEL',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	509
	.byte	12,0,2,35,0,11
	.byte	'EMSF',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'SEMSF',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	492
	.byte	6,0,2,35,2,11
	.byte	'EMSFM',0,1
	.word	492
	.byte	2,6,2,35,3,11
	.byte	'SEMSFM',0,1
	.word	492
	.byte	2,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	492
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_EMSR_Bits',0,6,159,2,3
	.word	12145
	.byte	10
	.byte	'_Ifx_SCU_ESRCFG_Bits',0,6,162,2,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	7,1,2,35,0,11
	.byte	'EDCON',0,2
	.word	509
	.byte	2,7,2,35,0,11
	.byte	'reserved_9',0,4
	.word	469
	.byte	23,0,2,35,0,0,21
	.byte	'Ifx_SCU_ESRCFG_Bits',0,6,167,2,3
	.word	12397
	.byte	10
	.byte	'_Ifx_SCU_ESROCFG_Bits',0,6,170,2,16,4,11
	.byte	'ARI',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'ARC',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	469
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_ESROCFG_Bits',0,6,175,2,3
	.word	12515
	.byte	10
	.byte	'_Ifx_SCU_EVR13CON_Bits',0,6,178,2,16,4,11
	.byte	'reserved_0',0,4
	.word	469
	.byte	28,4,2,35,0,11
	.byte	'EVR13OFF',0,1
	.word	492
	.byte	1,3,2,35,3,11
	.byte	'BPEVR13OFF',0,1
	.word	492
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVR13CON_Bits',0,6,185,2,3
	.word	12626
	.byte	10
	.byte	'_Ifx_SCU_EVR33CON_Bits',0,6,188,2,16,4,11
	.byte	'reserved_0',0,4
	.word	469
	.byte	28,4,2,35,0,11
	.byte	'EVR33OFF',0,1
	.word	492
	.byte	1,3,2,35,3,11
	.byte	'BPEVR33OFF',0,1
	.word	492
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVR33CON_Bits',0,6,195,2,3
	.word	12789
	.byte	10
	.byte	'_Ifx_SCU_EVRADCSTAT_Bits',0,6,198,2,16,4,11
	.byte	'ADC13V',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'ADC33V',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'ADCSWDV',0,1
	.word	492
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRADCSTAT_Bits',0,6,205,2,3
	.word	12952
	.byte	10
	.byte	'_Ifx_SCU_EVRDVSTAT_Bits',0,6,208,2,16,4,11
	.byte	'DVS13TRIM',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'DVS33TRIM',0,1
	.word	492
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRDVSTAT_Bits',0,6,215,2,3
	.word	13110
	.byte	10
	.byte	'_Ifx_SCU_EVRMONCTRL_Bits',0,6,218,2,16,4,11
	.byte	'EVR13OVMOD',0,1
	.word	492
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	492
	.byte	2,4,2,35,0,11
	.byte	'EVR13UVMOD',0,1
	.word	492
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	492
	.byte	2,0,2,35,0,11
	.byte	'EVR33OVMOD',0,1
	.word	492
	.byte	2,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	492
	.byte	2,4,2,35,1,11
	.byte	'EVR33UVMOD',0,1
	.word	492
	.byte	2,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	492
	.byte	2,0,2,35,1,11
	.byte	'SWDOVMOD',0,1
	.word	492
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	492
	.byte	2,4,2,35,2,11
	.byte	'SWDUVMOD',0,1
	.word	492
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,2
	.word	509
	.byte	10,0,2,35,2,0,21
	.byte	'Ifx_SCU_EVRMONCTRL_Bits',0,6,232,2,3
	.word	13275
	.byte	10
	.byte	'_Ifx_SCU_EVROSCCTRL_Bits',0,6,235,2,16,4,11
	.byte	'OSCTRIM',0,2
	.word	509
	.byte	10,6,2,35,0,11
	.byte	'OSCPTAT',0,1
	.word	492
	.byte	6,0,2,35,1,11
	.byte	'OSCANASEL',0,1
	.word	492
	.byte	4,4,2,35,2,11
	.byte	'HPBGTRIM',0,2
	.word	509
	.byte	7,5,2,35,2,11
	.byte	'HPBGCLKEN',0,1
	.word	492
	.byte	1,4,2,35,3,11
	.byte	'OSC3V3',0,1
	.word	492
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	492
	.byte	2,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVROSCCTRL_Bits',0,6,245,2,3
	.word	13604
	.byte	10
	.byte	'_Ifx_SCU_EVROVMON_Bits',0,6,248,2,16,4,11
	.byte	'EVR13OVVAL',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'EVR33OVVAL',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'SWDOVVAL',0,1
	.word	492
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVROVMON_Bits',0,6,255,2,3
	.word	13825
	.byte	10
	.byte	'_Ifx_SCU_EVRRSTCON_Bits',0,6,130,3,16,4,11
	.byte	'RST13TRIM',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	469
	.byte	16,8,2,35,0,11
	.byte	'RST13OFF',0,1
	.word	492
	.byte	1,7,2,35,3,11
	.byte	'BPRST13OFF',0,1
	.word	492
	.byte	1,6,2,35,3,11
	.byte	'RST33OFF',0,1
	.word	492
	.byte	1,5,2,35,3,11
	.byte	'BPRST33OFF',0,1
	.word	492
	.byte	1,4,2,35,3,11
	.byte	'RSTSWDOFF',0,1
	.word	492
	.byte	1,3,2,35,3,11
	.byte	'BPRSTSWDOFF',0,1
	.word	492
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRRSTCON_Bits',0,6,142,3,3
	.word	13988
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF1_Bits',0,6,145,3,16,4,11
	.byte	'SD5P',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'SD5I',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'SD5D',0,1
	.word	492
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF1_Bits',0,6,152,3,3
	.word	14260
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF2_Bits',0,6,155,3,16,4,11
	.byte	'SD33P',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'SD33I',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'SD33D',0,1
	.word	492
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF2_Bits',0,6,162,3,3
	.word	14413
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF3_Bits',0,6,165,3,16,4,11
	.byte	'CT5REG0',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'CT5REG1',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'CT5REG2',0,1
	.word	492
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF3_Bits',0,6,172,3,3
	.word	14569
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF4_Bits',0,6,175,3,16,4,11
	.byte	'CT5REG3',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'CT5REG4',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF4_Bits',0,6,181,3,3
	.word	14731
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF5_Bits',0,6,184,3,16,4,11
	.byte	'CT33REG0',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'CT33REG1',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'CT33REG2',0,1
	.word	492
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF5_Bits',0,6,191,3,3
	.word	14874
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF6_Bits',0,6,194,3,16,4,11
	.byte	'CT33REG3',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'CT33REG4',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF6_Bits',0,6,200,3,3
	.word	15039
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL1_Bits',0,6,203,3,16,4,11
	.byte	'SDFREQSPRD',0,2
	.word	509
	.byte	16,0,2,35,0,11
	.byte	'SDFREQ',0,1
	.word	492
	.byte	8,0,2,35,2,11
	.byte	'SDSTEP',0,1
	.word	492
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	492
	.byte	2,2,2,35,3,11
	.byte	'SDSAMPLE',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCTRL1_Bits',0,6,211,3,3
	.word	15184
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL2_Bits',0,6,214,3,16,4,11
	.byte	'DRVP',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'SDMINMAXDC',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'DRVN',0,1
	.word	492
	.byte	8,0,2,35,2,11
	.byte	'SDLUT',0,1
	.word	492
	.byte	6,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCTRL2_Bits',0,6,222,3,3
	.word	15365
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL3_Bits',0,6,225,3,16,4,11
	.byte	'SDPWMPRE',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'SDPID',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'SDVOKLVL',0,1
	.word	492
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCTRL3_Bits',0,6,232,3,3
	.word	15539
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL4_Bits',0,6,235,3,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'SYNCDIV',0,1
	.word	492
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	469
	.byte	20,1,2,35,0,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCTRL4_Bits',0,6,241,3,3
	.word	15699
	.byte	10
	.byte	'_Ifx_SCU_EVRSTAT_Bits',0,6,244,3,16,4,11
	.byte	'EVR13',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'OV13',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'EVR33',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'OV33',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'OVSWD',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'UV13',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'UV33',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'UVSWD',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'EXTPASS13',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'EXTPASS33',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'BGPROK',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	469
	.byte	21,0,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSTAT_Bits',0,6,130,4,3
	.word	15843
	.byte	10
	.byte	'_Ifx_SCU_EVRTRIM_Bits',0,6,133,4,16,4,11
	.byte	'EVR13TRIM',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'SDVOUTSEL',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRTRIM_Bits',0,6,139,4,3
	.word	16117
	.byte	10
	.byte	'_Ifx_SCU_EVRUVMON_Bits',0,6,142,4,16,4,11
	.byte	'EVR13UVVAL',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'EVR33UVVAL',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'SWDUVVAL',0,1
	.word	492
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRUVMON_Bits',0,6,149,4,3
	.word	16256
	.byte	10
	.byte	'_Ifx_SCU_EXTCON_Bits',0,6,152,4,16,4,11
	.byte	'EN0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'SEL0',0,1
	.word	492
	.byte	4,2,2,35,0,11
	.byte	'reserved_6',0,2
	.word	509
	.byte	10,0,2,35,0,11
	.byte	'EN1',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'NSEL',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'SEL1',0,1
	.word	492
	.byte	4,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	492
	.byte	2,0,2,35,2,11
	.byte	'DIV1',0,1
	.word	492
	.byte	8,0,2,35,3,0,21
	.byte	'Ifx_SCU_EXTCON_Bits',0,6,163,4,3
	.word	16419
	.byte	10
	.byte	'_Ifx_SCU_FDR_Bits',0,6,166,4,16,4,11
	.byte	'STEP',0,2
	.word	509
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	492
	.byte	4,2,2,35,1,11
	.byte	'DM',0,1
	.word	492
	.byte	2,0,2,35,1,11
	.byte	'RESULT',0,2
	.word	509
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	492
	.byte	5,1,2,35,3,11
	.byte	'DISCLK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_FDR_Bits',0,6,174,4,3
	.word	16637
	.byte	10
	.byte	'_Ifx_SCU_FMR_Bits',0,6,177,4,16,4,11
	.byte	'FS0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'FS1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'FS2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'FS3',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'FS4',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'FS5',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'FS6',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'FS7',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'FC0',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'FC1',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'FC2',0,1
	.word	492
	.byte	1,5,2,35,2,11
	.byte	'FC3',0,1
	.word	492
	.byte	1,4,2,35,2,11
	.byte	'FC4',0,1
	.word	492
	.byte	1,3,2,35,2,11
	.byte	'FC5',0,1
	.word	492
	.byte	1,2,2,35,2,11
	.byte	'FC6',0,1
	.word	492
	.byte	1,1,2,35,2,11
	.byte	'FC7',0,1
	.word	492
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	8,0,2,35,3,0,21
	.byte	'Ifx_SCU_FMR_Bits',0,6,197,4,3
	.word	16800
	.byte	10
	.byte	'_Ifx_SCU_ID_Bits',0,6,200,4,16,4,11
	.byte	'MODREV',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	509
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_ID_Bits',0,6,205,4,3
	.word	17136
	.byte	10
	.byte	'_Ifx_SCU_IGCR_Bits',0,6,208,4,16,4,11
	.byte	'IPEN00',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'IPEN01',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'IPEN02',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'IPEN03',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'IPEN04',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'IPEN05',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'IPEN06',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'IPEN07',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	492
	.byte	5,3,2,35,1,11
	.byte	'GEEN0',0,1
	.word	492
	.byte	1,2,2,35,1,11
	.byte	'IGP0',0,1
	.word	492
	.byte	2,0,2,35,1,11
	.byte	'IPEN10',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'IPEN11',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'IPEN12',0,1
	.word	492
	.byte	1,5,2,35,2,11
	.byte	'IPEN13',0,1
	.word	492
	.byte	1,4,2,35,2,11
	.byte	'IPEN14',0,1
	.word	492
	.byte	1,3,2,35,2,11
	.byte	'IPEN15',0,1
	.word	492
	.byte	1,2,2,35,2,11
	.byte	'IPEN16',0,1
	.word	492
	.byte	1,1,2,35,2,11
	.byte	'IPEN17',0,1
	.word	492
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	5,3,2,35,3,11
	.byte	'GEEN1',0,1
	.word	492
	.byte	1,2,2,35,3,11
	.byte	'IGP1',0,1
	.word	492
	.byte	2,0,2,35,3,0,21
	.byte	'Ifx_SCU_IGCR_Bits',0,6,232,4,3
	.word	17243
	.byte	10
	.byte	'_Ifx_SCU_IN_Bits',0,6,235,4,16,4,11
	.byte	'P0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	469
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_IN_Bits',0,6,240,4,3
	.word	17695
	.byte	10
	.byte	'_Ifx_SCU_IOCR_Bits',0,6,243,4,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	4,4,2,35,0,11
	.byte	'PC0',0,1
	.word	492
	.byte	4,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	492
	.byte	4,4,2,35,1,11
	.byte	'PC1',0,1
	.word	492
	.byte	4,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_IOCR_Bits',0,6,250,4,3
	.word	17794
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL0_Bits',0,6,253,4,16,4,11
	.byte	'LBISTREQ',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'LBISTREQP',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'PATTERNS',0,2
	.word	509
	.byte	14,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_LBISTCTRL0_Bits',0,6,131,5,3
	.word	17944
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL1_Bits',0,6,134,5,16,4,11
	.byte	'SEED',0,4
	.word	469
	.byte	23,9,2,35,0,11
	.byte	'reserved_23',0,1
	.word	492
	.byte	1,0,2,35,2,11
	.byte	'SPLITSH',0,1
	.word	492
	.byte	3,5,2,35,3,11
	.byte	'BODY',0,1
	.word	492
	.byte	1,4,2,35,3,11
	.byte	'LBISTFREQU',0,1
	.word	492
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_LBISTCTRL1_Bits',0,6,141,5,3
	.word	18093
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL2_Bits',0,6,144,5,16,4,11
	.byte	'SIGNATURE',0,4
	.word	469
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	7,1,2,35,3,11
	.byte	'LBISTDONE',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_LBISTCTRL2_Bits',0,6,149,5,3
	.word	18254
	.byte	10
	.byte	'_Ifx_SCU_LCLCON_Bits',0,6,152,5,16,4,11
	.byte	'reserved_0',0,2
	.word	509
	.byte	16,0,2,35,0,11
	.byte	'LS',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,2
	.word	509
	.byte	14,1,2,35,2,11
	.byte	'LSEN',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_LCLCON_Bits',0,6,158,5,3
	.word	18384
	.byte	10
	.byte	'_Ifx_SCU_LCLTEST_Bits',0,6,161,5,16,4,11
	.byte	'LCLT0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'LCLT1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	469
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_LCLTEST_Bits',0,6,166,5,3
	.word	18516
	.byte	10
	.byte	'_Ifx_SCU_MANID_Bits',0,6,169,5,16,4,11
	.byte	'DEPT',0,1
	.word	492
	.byte	5,3,2,35,0,11
	.byte	'MANUF',0,2
	.word	509
	.byte	11,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_MANID_Bits',0,6,174,5,3
	.word	18631
	.byte	10
	.byte	'_Ifx_SCU_OMR_Bits',0,6,177,5,16,4,11
	.byte	'PS0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	509
	.byte	14,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	509
	.byte	14,0,2,35,2,0,21
	.byte	'Ifx_SCU_OMR_Bits',0,6,185,5,3
	.word	18742
	.byte	10
	.byte	'_Ifx_SCU_OSCCON_Bits',0,6,188,5,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'PLLLV',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'OSCRES',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'GAINSEL',0,1
	.word	492
	.byte	2,3,2,35,0,11
	.byte	'MODE',0,1
	.word	492
	.byte	2,1,2,35,0,11
	.byte	'SHBY',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'PLLHV',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'X1D',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'X1DEN',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	492
	.byte	4,0,2,35,1,11
	.byte	'OSCVAL',0,1
	.word	492
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	492
	.byte	2,1,2,35,2,11
	.byte	'APREN',0,1
	.word	492
	.byte	1,0,2,35,2,11
	.byte	'CAP0EN',0,1
	.word	492
	.byte	1,7,2,35,3,11
	.byte	'CAP1EN',0,1
	.word	492
	.byte	1,6,2,35,3,11
	.byte	'CAP2EN',0,1
	.word	492
	.byte	1,5,2,35,3,11
	.byte	'CAP3EN',0,1
	.word	492
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	492
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_OSCCON_Bits',0,6,209,5,3
	.word	18900
	.byte	10
	.byte	'_Ifx_SCU_OUT_Bits',0,6,212,5,16,4,11
	.byte	'P0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	469
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_OUT_Bits',0,6,217,5,3
	.word	19312
	.byte	10
	.byte	'_Ifx_SCU_OVCCON_Bits',0,6,220,5,16,4,11
	.byte	'CSEL0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'CSEL1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'CSEL2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,2
	.word	509
	.byte	13,0,2,35,0,11
	.byte	'OVSTRT',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'OVSTP',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'DCINVAL',0,1
	.word	492
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	492
	.byte	5,0,2,35,2,11
	.byte	'OVCONF',0,1
	.word	492
	.byte	1,7,2,35,3,11
	.byte	'POVCONF',0,1
	.word	492
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	492
	.byte	6,0,2,35,3,0,21
	.byte	'Ifx_SCU_OVCCON_Bits',0,6,233,5,3
	.word	19413
	.byte	10
	.byte	'_Ifx_SCU_OVCENABLE_Bits',0,6,236,5,16,4,11
	.byte	'OVEN0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'OVEN1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'OVEN2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	469
	.byte	29,0,2,35,0,0,21
	.byte	'Ifx_SCU_OVCENABLE_Bits',0,6,242,5,3
	.word	19680
	.byte	10
	.byte	'_Ifx_SCU_PDISC_Bits',0,6,245,5,16,4,11
	.byte	'PDIS0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	469
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_PDISC_Bits',0,6,250,5,3
	.word	19816
	.byte	10
	.byte	'_Ifx_SCU_PDR_Bits',0,6,253,5,16,4,11
	.byte	'PD0',0,1
	.word	492
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	492
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	469
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_SCU_PDR_Bits',0,6,132,6,3
	.word	19927
	.byte	10
	.byte	'_Ifx_SCU_PDRR_Bits',0,6,135,6,16,4,11
	.byte	'PDR0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'PDR1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'PDR2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'PDR3',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'PDR4',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'PDR5',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'PDR6',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'PDR7',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	469
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_SCU_PDRR_Bits',0,6,146,6,3
	.word	20060
	.byte	10
	.byte	'_Ifx_SCU_PLLCON0_Bits',0,6,149,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'MODEN',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	509
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	492
	.byte	7,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	492
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	492
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	492
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	492
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_PLLCON0_Bits',0,6,166,6,3
	.word	20263
	.byte	10
	.byte	'_Ifx_SCU_PLLCON1_Bits',0,6,169,6,16,4,11
	.byte	'K2DIV',0,1
	.word	492
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	492
	.byte	7,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	492
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	509
	.byte	9,0,2,35,2,0,21
	.byte	'Ifx_SCU_PLLCON1_Bits',0,6,177,6,3
	.word	20619
	.byte	10
	.byte	'_Ifx_SCU_PLLCON2_Bits',0,6,180,6,16,4,11
	.byte	'MODCFG',0,2
	.word	509
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_PLLCON2_Bits',0,6,184,6,3
	.word	20797
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON0_Bits',0,6,187,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	492
	.byte	2,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	509
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	492
	.byte	5,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	492
	.byte	2,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	492
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	492
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	492
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	492
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_PLLERAYCON0_Bits',0,6,204,6,3
	.word	20897
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON1_Bits',0,6,207,6,16,4,11
	.byte	'K2DIV',0,1
	.word	492
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	492
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	492
	.byte	4,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	492
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	509
	.byte	9,0,2,35,2,0,21
	.byte	'Ifx_SCU_PLLERAYCON1_Bits',0,6,215,6,3
	.word	21267
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYSTAT_Bits',0,6,218,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'PWDSTAT',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	469
	.byte	26,0,2,35,0,0,21
	.byte	'Ifx_SCU_PLLERAYSTAT_Bits',0,6,227,6,3
	.word	21453
	.byte	10
	.byte	'_Ifx_SCU_PLLSTAT_Bits',0,6,230,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'MODRUN',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	469
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_SCU_PLLSTAT_Bits',0,6,241,6,3
	.word	21651
	.byte	10
	.byte	'_Ifx_SCU_PMCSR_Bits',0,6,244,6,16,4,11
	.byte	'REQSLP',0,1
	.word	492
	.byte	2,6,2,35,0,11
	.byte	'SMUSLP',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	492
	.byte	5,0,2,35,0,11
	.byte	'PMST',0,1
	.word	492
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	469
	.byte	21,0,2,35,0,0,21
	.byte	'Ifx_SCU_PMCSR_Bits',0,6,251,6,3
	.word	21884
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR0_Bits',0,6,254,6,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'ESR1WKEN',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'PINAWKEN',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'PINBWKEN',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'ESR0DFEN',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'ESR0EDCON',0,1
	.word	492
	.byte	2,1,2,35,0,11
	.byte	'ESR1DFEN',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'ESR1EDCON',0,1
	.word	492
	.byte	2,6,2,35,1,11
	.byte	'PINADFEN',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'PINAEDCON',0,1
	.word	492
	.byte	2,3,2,35,1,11
	.byte	'PINBDFEN',0,1
	.word	492
	.byte	1,2,2,35,1,11
	.byte	'PINBEDCON',0,1
	.word	492
	.byte	2,0,2,35,1,11
	.byte	'SCREN',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'STBYRAMSEL',0,1
	.word	492
	.byte	2,5,2,35,2,11
	.byte	'SCRCLKSEL',0,1
	.word	492
	.byte	1,4,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	492
	.byte	1,3,2,35,2,11
	.byte	'TRISTEN',0,1
	.word	492
	.byte	1,2,2,35,2,11
	.byte	'TRISTREQ',0,1
	.word	492
	.byte	1,1,2,35,2,11
	.byte	'PORSTDF',0,1
	.word	492
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	1,7,2,35,3,11
	.byte	'DCDCSYNC',0,1
	.word	492
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	492
	.byte	3,3,2,35,3,11
	.byte	'ESR0TRIST',0,1
	.word	492
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_PMSWCR0_Bits',0,6,153,7,3
	.word	22036
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR1_Bits',0,6,156,7,16,4,11
	.byte	'SCRSTEN',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'SCRSTREQ',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	492
	.byte	6,0,2,35,0,11
	.byte	'CPUIDLSEL',0,1
	.word	492
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'IRADIS',0,1
	.word	492
	.byte	1,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	492
	.byte	3,0,2,35,1,11
	.byte	'SCRCFG',0,1
	.word	492
	.byte	8,0,2,35,2,11
	.byte	'CPUSEL',0,1
	.word	492
	.byte	3,5,2,35,3,11
	.byte	'STBYEVEN',0,1
	.word	492
	.byte	1,4,2,35,3,11
	.byte	'STBYEV',0,1
	.word	492
	.byte	3,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_PMSWCR1_Bits',0,6,170,7,3
	.word	22603
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR2_Bits',0,6,173,7,16,4,11
	.byte	'SCRINT',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'BUSY',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'SCRECC',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'SCRWDT',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'SCRRST',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	492
	.byte	4,0,2,35,1,11
	.byte	'TCINT',0,1
	.word	492
	.byte	8,0,2,35,2,11
	.byte	'TCINTREQ',0,1
	.word	492
	.byte	1,7,2,35,3,11
	.byte	'SMURST',0,1
	.word	492
	.byte	1,6,2,35,3,11
	.byte	'RST',0,1
	.word	492
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	492
	.byte	4,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_PMSWCR2_Bits',0,6,187,7,3
	.word	22897
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTAT_Bits',0,6,190,7,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKP',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUN',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'PINAWKP',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUN',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'PINBWKP',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUN',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'PORSTDF',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'HWCFGEVR',0,1
	.word	492
	.byte	3,3,2,35,1,11
	.byte	'STBYRAM',0,1
	.word	492
	.byte	2,1,2,35,1,11
	.byte	'TRIST',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'SCRST',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'SCRWKP',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'SCR',0,1
	.word	492
	.byte	1,5,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	492
	.byte	1,4,2,35,2,11
	.byte	'ESR1WKEN',0,1
	.word	492
	.byte	1,3,2,35,2,11
	.byte	'PINAWKEN',0,1
	.word	492
	.byte	1,2,2,35,2,11
	.byte	'PINBWKEN',0,1
	.word	492
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	509
	.byte	4,5,2,35,2,11
	.byte	'ESR0TRIST',0,1
	.word	492
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	492
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_PMSWSTAT_Bits',0,6,214,7,3
	.word	23175
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTATCLR_Bits',0,6,217,7,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKPCLR',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUNCLR',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'PINAWKPCLR',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUNCLR',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'PINBWKPCLR',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUNCLR',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'SCRSTCLR',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'SCRWKPCLR',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	509
	.byte	14,0,2,35,2,0,21
	.byte	'Ifx_SCU_PMSWSTATCLR_Bits',0,6,230,7,3
	.word	23671
	.byte	10
	.byte	'_Ifx_SCU_RSTCON2_Bits',0,6,233,7,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'CLRC',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	509
	.byte	10,4,2,35,0,11
	.byte	'CSS0',0,1
	.word	492
	.byte	1,3,2,35,1,11
	.byte	'CSS1',0,1
	.word	492
	.byte	1,2,2,35,1,11
	.byte	'CSS2',0,1
	.word	492
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'USRINFO',0,2
	.word	509
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_RSTCON2_Bits',0,6,243,7,3
	.word	23984
	.byte	10
	.byte	'_Ifx_SCU_RSTCON_Bits',0,6,246,7,16,4,11
	.byte	'ESR0',0,1
	.word	492
	.byte	2,6,2,35,0,11
	.byte	'ESR1',0,1
	.word	492
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	492
	.byte	2,2,2,35,0,11
	.byte	'SMU',0,1
	.word	492
	.byte	2,0,2,35,0,11
	.byte	'SW',0,1
	.word	492
	.byte	2,6,2,35,1,11
	.byte	'STM0',0,1
	.word	492
	.byte	2,4,2,35,1,11
	.byte	'STM1',0,1
	.word	492
	.byte	2,2,2,35,1,11
	.byte	'STM2',0,1
	.word	492
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_RSTCON_Bits',0,6,129,8,3
	.word	24193
	.byte	10
	.byte	'_Ifx_SCU_RSTSTAT_Bits',0,6,132,8,16,4,11
	.byte	'ESR0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'ESR1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'SMU',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'SW',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'STM0',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'STM1',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'STM2',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'PORST',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'CB0',0,1
	.word	492
	.byte	1,5,2,35,2,11
	.byte	'CB1',0,1
	.word	492
	.byte	1,4,2,35,2,11
	.byte	'CB3',0,1
	.word	492
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	492
	.byte	2,1,2,35,2,11
	.byte	'EVR13',0,1
	.word	492
	.byte	1,0,2,35,2,11
	.byte	'EVR33',0,1
	.word	492
	.byte	1,7,2,35,3,11
	.byte	'SWD',0,1
	.word	492
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	492
	.byte	2,4,2,35,3,11
	.byte	'STBYR',0,1
	.word	492
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	492
	.byte	3,0,2,35,3,0,21
	.byte	'Ifx_SCU_RSTSTAT_Bits',0,6,155,8,3
	.word	24404
	.byte	10
	.byte	'_Ifx_SCU_SAFECON_Bits',0,6,158,8,16,4,11
	.byte	'HBT',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	469
	.byte	31,0,2,35,0,0,21
	.byte	'Ifx_SCU_SAFECON_Bits',0,6,162,8,3
	.word	24836
	.byte	10
	.byte	'_Ifx_SCU_STSTAT_Bits',0,6,165,8,16,4,11
	.byte	'HWCFG',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'FTM',0,1
	.word	492
	.byte	7,1,2,35,1,11
	.byte	'MODE',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'FCBAE',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'LUDIS',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	492
	.byte	1,5,2,35,2,11
	.byte	'TRSTL',0,1
	.word	492
	.byte	1,4,2,35,2,11
	.byte	'SPDEN',0,1
	.word	492
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	492
	.byte	3,0,2,35,2,11
	.byte	'RAMINT',0,1
	.word	492
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	492
	.byte	7,0,2,35,3,0,21
	.byte	'Ifx_SCU_STSTAT_Bits',0,6,178,8,3
	.word	24932
	.byte	10
	.byte	'_Ifx_SCU_SWRSTCON_Bits',0,6,181,8,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'SWRSTREQ',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	469
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_SWRSTCON_Bits',0,6,186,8,3
	.word	25192
	.byte	10
	.byte	'_Ifx_SCU_SYSCON_Bits',0,6,189,8,16,4,11
	.byte	'CCTRIG0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'RAMINTM',0,1
	.word	492
	.byte	2,4,2,35,0,11
	.byte	'SETLUDIS',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	492
	.byte	3,0,2,35,0,11
	.byte	'DATM',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,4
	.word	469
	.byte	23,0,2,35,0,0,21
	.byte	'Ifx_SCU_SYSCON_Bits',0,6,198,8,3
	.word	25317
	.byte	10
	.byte	'_Ifx_SCU_TRAPCLR_Bits',0,6,201,8,16,4,11
	.byte	'ESR0T',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	469
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPCLR_Bits',0,6,208,8,3
	.word	25514
	.byte	10
	.byte	'_Ifx_SCU_TRAPDIS_Bits',0,6,211,8,16,4,11
	.byte	'ESR0T',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	469
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPDIS_Bits',0,6,218,8,3
	.word	25667
	.byte	10
	.byte	'_Ifx_SCU_TRAPSET_Bits',0,6,221,8,16,4,11
	.byte	'ESR0T',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	469
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPSET_Bits',0,6,228,8,3
	.word	25820
	.byte	10
	.byte	'_Ifx_SCU_TRAPSTAT_Bits',0,6,231,8,16,4,11
	.byte	'ESR0T',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	469
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPSTAT_Bits',0,6,238,8,3
	.word	25973
	.byte	7
	.byte	'unsigned int',0,4,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON0_Bits',0,6,241,8,16,4,11
	.byte	'ENDINIT',0,4
	.word	26128
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	26128
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	26128
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	26128
	.byte	16,0,2,35,0,0,21
	.byte	'Ifx_SCU_WDTCPU_CON0_Bits',0,6,247,8,3
	.word	26144
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU_CON1_Bits',0,6,250,8,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	2,6,2,35,0,11
	.byte	'IR0',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	492
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_WDTCPU_CON1_Bits',0,6,134,9,3
	.word	26274
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU_SR_Bits',0,6,137,9,16,4,11
	.byte	'AE',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	492
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	509
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_WDTCPU_SR_Bits',0,6,150,9,3
	.word	26512
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON0_Bits',0,6,153,9,16,4,11
	.byte	'ENDINIT',0,4
	.word	26128
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	26128
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	26128
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	26128
	.byte	16,0,2,35,0,0,21
	.byte	'Ifx_SCU_WDTS_CON0_Bits',0,6,159,9,3
	.word	26735
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON1_Bits',0,6,162,9,16,4,11
	.byte	'CLRIRF',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'IR0',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	492
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_WDTS_CON1_Bits',0,6,175,9,3
	.word	26861
	.byte	10
	.byte	'_Ifx_SCU_WDTS_SR_Bits',0,6,178,9,16,4,11
	.byte	'AE',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	492
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	509
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_WDTS_SR_Bits',0,6,191,9,3
	.word	27113
	.byte	12,6,199,9,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8638
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ACCEN0',0,6,204,9,3
	.word	27332
	.byte	12,6,207,9,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9195
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ACCEN1',0,6,212,9,3
	.word	27396
	.byte	12,6,215,9,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9272
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ARSTDIS',0,6,220,9,3
	.word	27460
	.byte	12,6,223,9,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9408
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON0',0,6,228,9,3
	.word	27525
	.byte	12,6,231,9,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9688
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON1',0,6,236,9,3
	.word	27590
	.byte	12,6,239,9,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9926
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON2',0,6,244,9,3
	.word	27655
	.byte	12,6,247,9,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10054
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON3',0,6,252,9,3
	.word	27720
	.byte	12,6,255,9,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10297
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON4',0,6,132,10,3
	.word	27785
	.byte	12,6,135,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10532
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON5',0,6,140,10,3
	.word	27850
	.byte	12,6,143,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10660
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON6',0,6,148,10,3
	.word	27915
	.byte	12,6,151,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10760
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON7',0,6,156,10,3
	.word	27980
	.byte	12,6,159,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10860
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CHIPID',0,6,164,10,3
	.word	28045
	.byte	12,6,167,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11068
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_DTSCON',0,6,172,10,3
	.word	28109
	.byte	12,6,175,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11233
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_DTSLIM',0,6,180,10,3
	.word	28173
	.byte	12,6,183,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11416
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_DTSSTAT',0,6,188,10,3
	.word	28237
	.byte	12,6,191,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11570
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EICR',0,6,196,10,3
	.word	28302
	.byte	12,6,199,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11934
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EIFR',0,6,204,10,3
	.word	28364
	.byte	12,6,207,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12145
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EMSR',0,6,212,10,3
	.word	28426
	.byte	12,6,215,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12397
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ESRCFG',0,6,220,10,3
	.word	28488
	.byte	12,6,223,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12515
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ESROCFG',0,6,228,10,3
	.word	28552
	.byte	12,6,231,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12626
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVR13CON',0,6,236,10,3
	.word	28617
	.byte	12,6,239,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12789
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVR33CON',0,6,244,10,3
	.word	28683
	.byte	12,6,247,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12952
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRADCSTAT',0,6,252,10,3
	.word	28749
	.byte	12,6,255,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13110
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRDVSTAT',0,6,132,11,3
	.word	28817
	.byte	12,6,135,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13275
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRMONCTRL',0,6,140,11,3
	.word	28884
	.byte	12,6,143,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13604
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVROSCCTRL',0,6,148,11,3
	.word	28952
	.byte	12,6,151,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13825
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVROVMON',0,6,156,11,3
	.word	29020
	.byte	12,6,159,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13988
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRRSTCON',0,6,164,11,3
	.word	29086
	.byte	12,6,167,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14260
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF1',0,6,172,11,3
	.word	29153
	.byte	12,6,175,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14413
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF2',0,6,180,11,3
	.word	29222
	.byte	12,6,183,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14569
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF3',0,6,188,11,3
	.word	29291
	.byte	12,6,191,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14731
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF4',0,6,196,11,3
	.word	29360
	.byte	12,6,199,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14874
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF5',0,6,204,11,3
	.word	29429
	.byte	12,6,207,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15039
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF6',0,6,212,11,3
	.word	29498
	.byte	12,6,215,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15184
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCTRL1',0,6,220,11,3
	.word	29567
	.byte	12,6,223,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15365
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCTRL2',0,6,228,11,3
	.word	29635
	.byte	12,6,231,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15539
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCTRL3',0,6,236,11,3
	.word	29703
	.byte	12,6,239,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15699
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCTRL4',0,6,244,11,3
	.word	29771
	.byte	12,6,247,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15843
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSTAT',0,6,252,11,3
	.word	29839
	.byte	12,6,255,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16117
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRTRIM',0,6,132,12,3
	.word	29904
	.byte	12,6,135,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16256
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRUVMON',0,6,140,12,3
	.word	29969
	.byte	12,6,143,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16419
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EXTCON',0,6,148,12,3
	.word	30035
	.byte	12,6,151,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16637
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_FDR',0,6,156,12,3
	.word	30099
	.byte	12,6,159,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16800
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_FMR',0,6,164,12,3
	.word	30160
	.byte	12,6,167,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17136
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ID',0,6,172,12,3
	.word	30221
	.byte	12,6,175,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17243
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_IGCR',0,6,180,12,3
	.word	30281
	.byte	12,6,183,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17695
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_IN',0,6,188,12,3
	.word	30343
	.byte	12,6,191,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17794
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_IOCR',0,6,196,12,3
	.word	30403
	.byte	12,6,199,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17944
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_LBISTCTRL0',0,6,204,12,3
	.word	30465
	.byte	12,6,207,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18093
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_LBISTCTRL1',0,6,212,12,3
	.word	30533
	.byte	12,6,215,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18254
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_LBISTCTRL2',0,6,220,12,3
	.word	30601
	.byte	12,6,223,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18384
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_LCLCON',0,6,228,12,3
	.word	30669
	.byte	12,6,231,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18516
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_LCLTEST',0,6,236,12,3
	.word	30733
	.byte	12,6,239,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18631
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_MANID',0,6,244,12,3
	.word	30798
	.byte	12,6,247,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18742
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_OMR',0,6,252,12,3
	.word	30861
	.byte	12,6,255,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18900
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_OSCCON',0,6,132,13,3
	.word	30922
	.byte	12,6,135,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19312
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_OUT',0,6,140,13,3
	.word	30986
	.byte	12,6,143,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19413
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_OVCCON',0,6,148,13,3
	.word	31047
	.byte	12,6,151,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19680
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_OVCENABLE',0,6,156,13,3
	.word	31111
	.byte	12,6,159,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19816
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PDISC',0,6,164,13,3
	.word	31178
	.byte	12,6,167,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19927
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PDR',0,6,172,13,3
	.word	31241
	.byte	12,6,175,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20060
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PDRR',0,6,180,13,3
	.word	31302
	.byte	12,6,183,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20263
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLCON0',0,6,188,13,3
	.word	31364
	.byte	12,6,191,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20619
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLCON1',0,6,196,13,3
	.word	31429
	.byte	12,6,199,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20797
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLCON2',0,6,204,13,3
	.word	31494
	.byte	12,6,207,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20897
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLERAYCON0',0,6,212,13,3
	.word	31559
	.byte	12,6,215,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21267
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLERAYCON1',0,6,220,13,3
	.word	31628
	.byte	12,6,223,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21453
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLERAYSTAT',0,6,228,13,3
	.word	31697
	.byte	12,6,231,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21651
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLSTAT',0,6,236,13,3
	.word	31766
	.byte	12,6,239,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21884
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMCSR',0,6,244,13,3
	.word	31831
	.byte	12,6,247,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22036
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMSWCR0',0,6,252,13,3
	.word	31894
	.byte	12,6,255,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22603
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMSWCR1',0,6,132,14,3
	.word	31959
	.byte	12,6,135,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22897
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMSWCR2',0,6,140,14,3
	.word	32024
	.byte	12,6,143,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23175
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMSWSTAT',0,6,148,14,3
	.word	32089
	.byte	12,6,151,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23671
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMSWSTATCLR',0,6,156,14,3
	.word	32155
	.byte	12,6,159,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24193
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_RSTCON',0,6,164,14,3
	.word	32224
	.byte	12,6,167,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23984
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_RSTCON2',0,6,172,14,3
	.word	32288
	.byte	12,6,175,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24404
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_RSTSTAT',0,6,180,14,3
	.word	32353
	.byte	12,6,183,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24836
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_SAFECON',0,6,188,14,3
	.word	32418
	.byte	12,6,191,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24932
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_STSTAT',0,6,196,14,3
	.word	32483
	.byte	12,6,199,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25192
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_SWRSTCON',0,6,204,14,3
	.word	32547
	.byte	12,6,207,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25317
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_SYSCON',0,6,212,14,3
	.word	32613
	.byte	12,6,215,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25514
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPCLR',0,6,220,14,3
	.word	32677
	.byte	12,6,223,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25667
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPDIS',0,6,228,14,3
	.word	32742
	.byte	12,6,231,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25820
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPSET',0,6,236,14,3
	.word	32807
	.byte	12,6,239,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25973
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPSTAT',0,6,244,14,3
	.word	32872
	.byte	12,6,247,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26144
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTCPU_CON0',0,6,252,14,3
	.word	32938
	.byte	12,6,255,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26274
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTCPU_CON1',0,6,132,15,3
	.word	33007
	.byte	12,6,135,15,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26512
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTCPU_SR',0,6,140,15,3
	.word	33076
	.byte	12,6,143,15,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26735
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTS_CON0',0,6,148,15,3
	.word	33143
	.byte	12,6,151,15,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26861
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTS_CON1',0,6,156,15,3
	.word	33210
	.byte	12,6,159,15,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27113
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTS_SR',0,6,164,15,3
	.word	33277
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU',0,6,175,15,25,12,13
	.byte	'CON0',0
	.word	32938
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	33007
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	33076
	.byte	4,2,35,8,0,16
	.word	33342
	.byte	21
	.byte	'Ifx_SCU_WDTCPU',0,6,180,15,3
	.word	33405
	.byte	10
	.byte	'_Ifx_SCU_WDTS',0,6,183,15,25,12,13
	.byte	'CON0',0
	.word	33143
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	33210
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	33277
	.byte	4,2,35,8,0,16
	.word	33434
	.byte	21
	.byte	'Ifx_SCU_WDTS',0,6,188,15,3
	.word	33495
	.byte	14,8
	.word	28488
	.byte	15,1,0,14,20
	.word	492
	.byte	15,19,0,14,8
	.word	31831
	.byte	15,1,0,16
	.word	33434
	.byte	14,24
	.word	33342
	.byte	15,1,0,16
	.word	33554
	.byte	14,16
	.word	492
	.byte	15,15,0,14,28
	.word	492
	.byte	15,27,0,14,40
	.word	492
	.byte	15,39,0,14,16
	.word	28302
	.byte	15,3,0,14,16
	.word	30281
	.byte	15,3,0,14,180,3
	.word	492
	.byte	15,179,3,0,10
	.byte	'_Ifx_SCU',0,6,201,15,25,128,8,13
	.byte	'reserved_0',0
	.word	3356
	.byte	8,2,35,0,13
	.byte	'ID',0
	.word	30221
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	1537
	.byte	4,2,35,12,13
	.byte	'OSCCON',0
	.word	30922
	.byte	4,2,35,16,13
	.byte	'PLLSTAT',0
	.word	31766
	.byte	4,2,35,20,13
	.byte	'PLLCON0',0
	.word	31364
	.byte	4,2,35,24,13
	.byte	'PLLCON1',0
	.word	31429
	.byte	4,2,35,28,13
	.byte	'PLLCON2',0
	.word	31494
	.byte	4,2,35,32,13
	.byte	'PLLERAYSTAT',0
	.word	31697
	.byte	4,2,35,36,13
	.byte	'PLLERAYCON0',0
	.word	31559
	.byte	4,2,35,40,13
	.byte	'PLLERAYCON1',0
	.word	31628
	.byte	4,2,35,44,13
	.byte	'CCUCON0',0
	.word	27525
	.byte	4,2,35,48,13
	.byte	'CCUCON1',0
	.word	27590
	.byte	4,2,35,52,13
	.byte	'FDR',0
	.word	30099
	.byte	4,2,35,56,13
	.byte	'EXTCON',0
	.word	30035
	.byte	4,2,35,60,13
	.byte	'CCUCON2',0
	.word	27655
	.byte	4,2,35,64,13
	.byte	'CCUCON3',0
	.word	27720
	.byte	4,2,35,68,13
	.byte	'CCUCON4',0
	.word	27785
	.byte	4,2,35,72,13
	.byte	'CCUCON5',0
	.word	27850
	.byte	4,2,35,76,13
	.byte	'RSTSTAT',0
	.word	32353
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	1537
	.byte	4,2,35,84,13
	.byte	'RSTCON',0
	.word	32224
	.byte	4,2,35,88,13
	.byte	'ARSTDIS',0
	.word	27460
	.byte	4,2,35,92,13
	.byte	'SWRSTCON',0
	.word	32547
	.byte	4,2,35,96,13
	.byte	'RSTCON2',0
	.word	32288
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	1537
	.byte	4,2,35,104,13
	.byte	'EVRRSTCON',0
	.word	29086
	.byte	4,2,35,108,13
	.byte	'ESRCFG',0
	.word	33522
	.byte	8,2,35,112,13
	.byte	'ESROCFG',0
	.word	28552
	.byte	4,2,35,120,13
	.byte	'SYSCON',0
	.word	32613
	.byte	4,2,35,124,13
	.byte	'CCUCON6',0
	.word	27915
	.byte	4,3,35,128,1,13
	.byte	'CCUCON7',0
	.word	27980
	.byte	4,3,35,132,1,13
	.byte	'reserved_88',0
	.word	33531
	.byte	20,3,35,136,1,13
	.byte	'PDR',0
	.word	31241
	.byte	4,3,35,156,1,13
	.byte	'IOCR',0
	.word	30403
	.byte	4,3,35,160,1,13
	.byte	'OUT',0
	.word	30986
	.byte	4,3,35,164,1,13
	.byte	'OMR',0
	.word	30861
	.byte	4,3,35,168,1,13
	.byte	'IN',0
	.word	30343
	.byte	4,3,35,172,1,13
	.byte	'EVRSTAT',0
	.word	29839
	.byte	4,3,35,176,1,13
	.byte	'EVRDVSTAT',0
	.word	28817
	.byte	4,3,35,180,1,13
	.byte	'EVR13CON',0
	.word	28617
	.byte	4,3,35,184,1,13
	.byte	'EVR33CON',0
	.word	28683
	.byte	4,3,35,188,1,13
	.byte	'STSTAT',0
	.word	32483
	.byte	4,3,35,192,1,13
	.byte	'reserved_C4',0
	.word	1537
	.byte	4,3,35,196,1,13
	.byte	'PMSWCR0',0
	.word	31894
	.byte	4,3,35,200,1,13
	.byte	'PMSWSTAT',0
	.word	32089
	.byte	4,3,35,204,1,13
	.byte	'PMSWSTATCLR',0
	.word	32155
	.byte	4,3,35,208,1,13
	.byte	'PMCSR',0
	.word	33540
	.byte	8,3,35,212,1,13
	.byte	'reserved_DC',0
	.word	1537
	.byte	4,3,35,220,1,13
	.byte	'DTSSTAT',0
	.word	28237
	.byte	4,3,35,224,1,13
	.byte	'DTSCON',0
	.word	28109
	.byte	4,3,35,228,1,13
	.byte	'PMSWCR1',0
	.word	31959
	.byte	4,3,35,232,1,13
	.byte	'PMSWCR2',0
	.word	32024
	.byte	4,3,35,236,1,13
	.byte	'WDTS',0
	.word	33549
	.byte	12,3,35,240,1,13
	.byte	'EMSR',0
	.word	28426
	.byte	4,3,35,252,1,13
	.byte	'WDTCPU',0
	.word	33563
	.byte	24,3,35,128,2,13
	.byte	'reserved_118',0
	.word	3696
	.byte	12,3,35,152,2,13
	.byte	'TRAPSTAT',0
	.word	32872
	.byte	4,3,35,164,2,13
	.byte	'TRAPSET',0
	.word	32807
	.byte	4,3,35,168,2,13
	.byte	'TRAPCLR',0
	.word	32677
	.byte	4,3,35,172,2,13
	.byte	'TRAPDIS',0
	.word	32742
	.byte	4,3,35,176,2,13
	.byte	'reserved_134',0
	.word	1537
	.byte	4,3,35,180,2,13
	.byte	'LCLCON1',0
	.word	30669
	.byte	4,3,35,184,2,13
	.byte	'LCLTEST',0
	.word	30733
	.byte	4,3,35,188,2,13
	.byte	'CHIPID',0
	.word	28045
	.byte	4,3,35,192,2,13
	.byte	'MANID',0
	.word	30798
	.byte	4,3,35,196,2,13
	.byte	'reserved_148',0
	.word	3356
	.byte	8,3,35,200,2,13
	.byte	'SAFECON',0
	.word	32418
	.byte	4,3,35,208,2,13
	.byte	'reserved_154',0
	.word	33568
	.byte	16,3,35,212,2,13
	.byte	'LBISTCTRL0',0
	.word	30465
	.byte	4,3,35,228,2,13
	.byte	'LBISTCTRL1',0
	.word	30533
	.byte	4,3,35,232,2,13
	.byte	'LBISTCTRL2',0
	.word	30601
	.byte	4,3,35,236,2,13
	.byte	'reserved_170',0
	.word	33577
	.byte	28,3,35,240,2,13
	.byte	'PDISC',0
	.word	31178
	.byte	4,3,35,140,3,13
	.byte	'reserved_190',0
	.word	3356
	.byte	8,3,35,144,3,13
	.byte	'EVRTRIM',0
	.word	29904
	.byte	4,3,35,152,3,13
	.byte	'EVRADCSTAT',0
	.word	28749
	.byte	4,3,35,156,3,13
	.byte	'EVRUVMON',0
	.word	29969
	.byte	4,3,35,160,3,13
	.byte	'EVROVMON',0
	.word	29020
	.byte	4,3,35,164,3,13
	.byte	'EVRMONCTRL',0
	.word	28884
	.byte	4,3,35,168,3,13
	.byte	'reserved_1AC',0
	.word	1537
	.byte	4,3,35,172,3,13
	.byte	'EVRSDCTRL1',0
	.word	29567
	.byte	4,3,35,176,3,13
	.byte	'EVRSDCTRL2',0
	.word	29635
	.byte	4,3,35,180,3,13
	.byte	'EVRSDCTRL3',0
	.word	29703
	.byte	4,3,35,184,3,13
	.byte	'EVRSDCTRL4',0
	.word	29771
	.byte	4,3,35,188,3,13
	.byte	'EVRSDCOEFF1',0
	.word	29153
	.byte	4,3,35,192,3,13
	.byte	'EVRSDCOEFF2',0
	.word	29222
	.byte	4,3,35,196,3,13
	.byte	'EVRSDCOEFF3',0
	.word	29291
	.byte	4,3,35,200,3,13
	.byte	'EVRSDCOEFF4',0
	.word	29360
	.byte	4,3,35,204,3,13
	.byte	'EVRSDCOEFF5',0
	.word	29429
	.byte	4,3,35,208,3,13
	.byte	'EVRSDCOEFF6',0
	.word	29498
	.byte	4,3,35,212,3,13
	.byte	'EVROSCCTRL',0
	.word	28952
	.byte	4,3,35,216,3,13
	.byte	'reserved_1DC',0
	.word	1537
	.byte	4,3,35,220,3,13
	.byte	'OVCENABLE',0
	.word	31111
	.byte	4,3,35,224,3,13
	.byte	'OVCCON',0
	.word	31047
	.byte	4,3,35,228,3,13
	.byte	'reserved_1E8',0
	.word	33586
	.byte	40,3,35,232,3,13
	.byte	'EICR',0
	.word	33595
	.byte	16,3,35,144,4,13
	.byte	'EIFR',0
	.word	28364
	.byte	4,3,35,160,4,13
	.byte	'FMR',0
	.word	30160
	.byte	4,3,35,164,4,13
	.byte	'PDRR',0
	.word	31302
	.byte	4,3,35,168,4,13
	.byte	'IGCR',0
	.word	33604
	.byte	16,3,35,172,4,13
	.byte	'reserved_23C',0
	.word	1537
	.byte	4,3,35,188,4,13
	.byte	'DTSLIM',0
	.word	28173
	.byte	4,3,35,192,4,13
	.byte	'reserved_244',0
	.word	33613
	.byte	180,3,3,35,196,4,13
	.byte	'ACCEN1',0
	.word	27396
	.byte	4,3,35,248,7,13
	.byte	'ACCEN0',0
	.word	27332
	.byte	4,3,35,252,7,0,16
	.word	33624
	.byte	21
	.byte	'Ifx_SCU',0,6,181,16,3
	.word	35614
	.byte	17,7,250,10,9,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy16384',0,0,18
	.byte	'IfxScu_WDTCON1_IR_divBy256',0,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy64',0,2,0,21
	.byte	'IfxScu_WDTCON1_IR',0,7,255,10,3
	.word	35636
	.byte	21
	.byte	'boolean',0,8,101,29
	.word	492
	.byte	21
	.byte	'uint8',0,8,105,29
	.word	492
	.byte	21
	.byte	'uint16',0,8,109,29
	.word	509
	.byte	7
	.byte	'unsigned long int',0,4,7,21
	.byte	'uint32',0,8,113,29
	.word	35803
	.byte	21
	.byte	'uint64',0,8,118,29
	.word	351
	.byte	21
	.byte	'sint16',0,8,126,29
	.word	8545
	.byte	7
	.byte	'long int',0,4,5,21
	.byte	'sint32',0,8,131,1,29
	.word	35869
	.byte	7
	.byte	'long long int',0,8,5,21
	.byte	'sint64',0,8,138,1,29
	.word	35897
	.byte	21
	.byte	'float32',0,8,167,1,29
	.word	297
	.byte	21
	.byte	'pvoid',0,9,57,28
	.word	383
	.byte	21
	.byte	'Ifx_TickTime',0,9,79,28
	.word	35897
	.byte	17,9,130,1,9,1,18
	.byte	'Ifx_RxSel_a',0,0,18
	.byte	'Ifx_RxSel_b',0,1,18
	.byte	'Ifx_RxSel_c',0,2,18
	.byte	'Ifx_RxSel_d',0,3,18
	.byte	'Ifx_RxSel_e',0,4,18
	.byte	'Ifx_RxSel_f',0,5,18
	.byte	'Ifx_RxSel_g',0,6,18
	.byte	'Ifx_RxSel_h',0,7,0,21
	.byte	'Ifx_RxSel',0,9,140,1,3
	.word	35982
	.byte	21
	.byte	'Ifx_P_ACCEN0_Bits',0,4,79,3
	.word	7109
	.byte	21
	.byte	'Ifx_P_ACCEN1_Bits',0,4,85,3
	.word	7022
	.byte	21
	.byte	'Ifx_P_ESR_Bits',0,4,107,3
	.word	3365
	.byte	21
	.byte	'Ifx_P_ID_Bits',0,4,115,3
	.word	1418
	.byte	21
	.byte	'Ifx_P_IN_Bits',0,4,137,1,3
	.word	2413
	.byte	21
	.byte	'Ifx_P_IOCR0_Bits',0,4,150,1,3
	.word	1546
	.byte	21
	.byte	'Ifx_P_IOCR12_Bits',0,4,163,1,3
	.word	2193
	.byte	21
	.byte	'Ifx_P_IOCR4_Bits',0,4,176,1,3
	.word	1761
	.byte	21
	.byte	'Ifx_P_IOCR8_Bits',0,4,189,1,3
	.word	1976
	.byte	21
	.byte	'Ifx_P_LPCR0_Bits',0,4,197,1,3
	.word	6381
	.byte	21
	.byte	'Ifx_P_LPCR1_Bits',0,4,205,1,3
	.word	6505
	.byte	21
	.byte	'Ifx_P_LPCR1_P21_Bits',0,4,215,1,3
	.word	6589
	.byte	21
	.byte	'Ifx_P_LPCR2_Bits',0,4,229,1,3
	.word	6769
	.byte	21
	.byte	'Ifx_P_OMCR0_Bits',0,4,240,1,3
	.word	5020
	.byte	21
	.byte	'Ifx_P_OMCR12_Bits',0,4,250,1,3
	.word	5544
	.byte	21
	.byte	'Ifx_P_OMCR4_Bits',0,4,133,2,3
	.word	5194
	.byte	21
	.byte	'Ifx_P_OMCR8_Bits',0,4,144,2,3
	.word	5368
	.byte	21
	.byte	'Ifx_P_OMCR_Bits',0,4,166,2,3
	.word	6033
	.byte	21
	.byte	'Ifx_P_OMR_Bits',0,4,203,2,3
	.word	847
	.byte	21
	.byte	'Ifx_P_OMSR0_Bits',0,4,213,2,3
	.word	4357
	.byte	21
	.byte	'Ifx_P_OMSR12_Bits',0,4,224,2,3
	.word	4845
	.byte	21
	.byte	'Ifx_P_OMSR4_Bits',0,4,235,2,3
	.word	4504
	.byte	21
	.byte	'Ifx_P_OMSR8_Bits',0,4,246,2,3
	.word	4673
	.byte	21
	.byte	'Ifx_P_OMSR_Bits',0,4,140,3,3
	.word	5700
	.byte	21
	.byte	'Ifx_P_OUT_Bits',0,4,162,3,3
	.word	531
	.byte	21
	.byte	'Ifx_P_PCSR_Bits',0,4,180,3,3
	.word	4071
	.byte	21
	.byte	'Ifx_P_PDISC_Bits',0,4,202,3,3
	.word	3705
	.byte	21
	.byte	'Ifx_P_PDR0_Bits',0,4,223,3,3
	.word	2736
	.byte	21
	.byte	'Ifx_P_PDR1_Bits',0,4,244,3,3
	.word	3040
	.byte	21
	.byte	'Ifx_P_ACCEN0',0,4,129,4,3
	.word	7636
	.byte	21
	.byte	'Ifx_P_ACCEN1',0,4,137,4,3
	.word	7069
	.byte	21
	.byte	'Ifx_P_ESR',0,4,145,4,3
	.word	3656
	.byte	21
	.byte	'Ifx_P_ID',0,4,153,4,3
	.word	1497
	.byte	21
	.byte	'Ifx_P_IN',0,4,161,4,3
	.word	2687
	.byte	21
	.byte	'Ifx_P_IOCR0',0,4,169,4,3
	.word	1721
	.byte	21
	.byte	'Ifx_P_IOCR12',0,4,177,4,3
	.word	2373
	.byte	21
	.byte	'Ifx_P_IOCR4',0,4,185,4,3
	.word	1936
	.byte	21
	.byte	'Ifx_P_IOCR8',0,4,193,4,3
	.word	2153
	.byte	21
	.byte	'Ifx_P_LPCR0',0,4,201,4,3
	.word	6465
	.byte	21
	.byte	'Ifx_P_LPCR1',0,4,210,4,3
	.word	6714
	.byte	21
	.byte	'Ifx_P_LPCR2',0,4,218,4,3
	.word	6973
	.byte	21
	.byte	'Ifx_P_OMCR',0,4,226,4,3
	.word	6341
	.byte	21
	.byte	'Ifx_P_OMCR0',0,4,234,4,3
	.word	5154
	.byte	21
	.byte	'Ifx_P_OMCR12',0,4,242,4,3
	.word	5660
	.byte	21
	.byte	'Ifx_P_OMCR4',0,4,250,4,3
	.word	5328
	.byte	21
	.byte	'Ifx_P_OMCR8',0,4,130,5,3
	.word	5504
	.byte	21
	.byte	'Ifx_P_OMR',0,4,138,5,3
	.word	1378
	.byte	21
	.byte	'Ifx_P_OMSR',0,4,146,5,3
	.word	5993
	.byte	21
	.byte	'Ifx_P_OMSR0',0,4,154,5,3
	.word	4464
	.byte	21
	.byte	'Ifx_P_OMSR12',0,4,162,5,3
	.word	4980
	.byte	21
	.byte	'Ifx_P_OMSR4',0,4,170,5,3
	.word	4633
	.byte	21
	.byte	'Ifx_P_OMSR8',0,4,178,5,3
	.word	4805
	.byte	21
	.byte	'Ifx_P_OUT',0,4,186,5,3
	.word	807
	.byte	21
	.byte	'Ifx_P_PCSR',0,4,194,5,3
	.word	4317
	.byte	21
	.byte	'Ifx_P_PDISC',0,4,202,5,3
	.word	4031
	.byte	21
	.byte	'Ifx_P_PDR0',0,4,210,5,3
	.word	3000
	.byte	21
	.byte	'Ifx_P_PDR1',0,4,218,5,3
	.word	3316
	.byte	16
	.word	7676
	.byte	21
	.byte	'Ifx_P',0,4,139,6,3
	.word	37438
	.byte	17,3,83,9,1,18
	.byte	'IfxPort_InputMode_undefined',0,127,18
	.byte	'IfxPort_InputMode_noPullDevice',0,0,18
	.byte	'IfxPort_InputMode_pullDown',0,8,18
	.byte	'IfxPort_InputMode_pullUp',0,16,0,21
	.byte	'IfxPort_InputMode',0,3,89,3
	.word	37458
	.byte	17,3,120,9,1,18
	.byte	'IfxPort_OutputIdx_general',0,128,1,18
	.byte	'IfxPort_OutputIdx_alt1',0,136,1,18
	.byte	'IfxPort_OutputIdx_alt2',0,144,1,18
	.byte	'IfxPort_OutputIdx_alt3',0,152,1,18
	.byte	'IfxPort_OutputIdx_alt4',0,160,1,18
	.byte	'IfxPort_OutputIdx_alt5',0,168,1,18
	.byte	'IfxPort_OutputIdx_alt6',0,176,1,18
	.byte	'IfxPort_OutputIdx_alt7',0,184,1,0,21
	.byte	'IfxPort_OutputIdx',0,3,130,1,3
	.word	37609
	.byte	17,3,134,1,9,1,18
	.byte	'IfxPort_OutputMode_pushPull',0,128,1,18
	.byte	'IfxPort_OutputMode_openDrain',0,192,1,0,21
	.byte	'IfxPort_OutputMode',0,3,138,1,3
	.word	37853
	.byte	17,3,144,1,9,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed1',0,0,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed2',0,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed3',0,2,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed4',0,3,18
	.byte	'IfxPort_PadDriver_lvdsSpeed1',0,4,18
	.byte	'IfxPort_PadDriver_lvdsSpeed2',0,5,18
	.byte	'IfxPort_PadDriver_lvdsSpeed3',0,6,18
	.byte	'IfxPort_PadDriver_lvdsSpeed4',0,7,18
	.byte	'IfxPort_PadDriver_ttlSpeed1',0,8,18
	.byte	'IfxPort_PadDriver_ttlSpeed2',0,9,18
	.byte	'IfxPort_PadDriver_ttlSpeed3',0,10,18
	.byte	'IfxPort_PadDriver_ttlSpeed4',0,11,0,21
	.byte	'IfxPort_PadDriver',0,3,158,1,3
	.word	37951
	.byte	21
	.byte	'IfxPort_State',0,3,178,1,3
	.word	8289
	.byte	23,3,190,1,9,8,13
	.byte	'port',0
	.word	8284
	.byte	4,2,35,0,13
	.byte	'pinIndex',0
	.word	492
	.byte	1,2,35,4,0,21
	.byte	'IfxPort_Pin',0,3,194,1,3
	.word	38416
	.byte	16
	.word	33624
	.byte	3
	.word	38476
	.byte	23,10,59,15,16,13
	.byte	'module',0
	.word	38481
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	38416
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	35982
	.byte	1,2,35,12,0,24
	.word	38486
	.byte	21
	.byte	'IfxScu_Evrwup_In',0,10,64,3
	.word	38537
	.byte	23,10,67,15,12,13
	.byte	'module',0
	.word	38481
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	38416
	.byte	8,2,35,4,0,24
	.word	38567
	.byte	21
	.byte	'IfxScu_Hwcfg_In',0,10,71,3
	.word	38602
	.byte	23,10,74,15,20,13
	.byte	'module',0
	.word	38481
	.byte	4,2,35,0,13
	.byte	'channelId',0
	.word	492
	.byte	1,2,35,4,13
	.byte	'pin',0
	.word	38416
	.byte	8,2,35,8,13
	.byte	'select',0
	.word	35982
	.byte	1,2,35,16,0,24
	.word	38631
	.byte	21
	.byte	'IfxScu_Req_In',0,10,80,3
	.word	38701
	.byte	23,10,83,15,16,13
	.byte	'module',0
	.word	38481
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	38416
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	37609
	.byte	1,2,35,12,0,24
	.word	38728
	.byte	21
	.byte	'IfxScu_Dcdcsync_Out',0,10,88,3
	.word	38779
	.byte	23,10,91,15,16,13
	.byte	'module',0
	.word	38481
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	38416
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	35982
	.byte	1,2,35,12,0,24
	.word	38812
	.byte	21
	.byte	'IfxScu_Emgstop_In',0,10,96,3
	.word	38863
	.byte	23,10,99,15,16,13
	.byte	'module',0
	.word	38481
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	38416
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	37609
	.byte	1,2,35,12,0,24
	.word	38894
	.byte	21
	.byte	'IfxScu_Wdtlck_Out',0,10,104,3
	.word	38945
	.byte	23,10,107,15,16,13
	.byte	'module',0
	.word	38481
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	38416
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	37609
	.byte	1,2,35,12,0,24
	.word	38976
	.byte	21
	.byte	'IfxScu_Extclk_Out',0,10,112,3
	.word	39027
.L92:
	.byte	24
	.word	38728
.L93:
	.byte	24
	.word	38728
.L94:
	.byte	24
	.word	38812
.L95:
	.byte	24
	.word	38812
.L96:
	.byte	24
	.word	38486
.L97:
	.byte	24
	.word	38486
.L98:
	.byte	24
	.word	38976
.L99:
	.byte	24
	.word	38976
.L100:
	.byte	24
	.word	38976
.L101:
	.byte	24
	.word	38567
.L102:
	.byte	24
	.word	38567
.L103:
	.byte	24
	.word	38567
.L104:
	.byte	24
	.word	38567
.L105:
	.byte	24
	.word	38567
.L106:
	.byte	24
	.word	38567
.L107:
	.byte	24
	.word	38567
.L108:
	.byte	24
	.word	38631
.L109:
	.byte	24
	.word	38631
.L110:
	.byte	24
	.word	38631
.L111:
	.byte	24
	.word	38631
.L112:
	.byte	24
	.word	38631
.L113:
	.byte	24
	.word	38631
.L114:
	.byte	24
	.word	38631
.L115:
	.byte	24
	.word	38631
.L116:
	.byte	24
	.word	38631
.L117:
	.byte	24
	.word	38631
.L118:
	.byte	24
	.word	38631
.L119:
	.byte	24
	.word	38631
.L120:
	.byte	24
	.word	38631
.L121:
	.byte	24
	.word	38631
.L122:
	.byte	24
	.word	38631
.L123:
	.byte	24
	.word	38631
.L124:
	.byte	24
	.word	38631
.L125:
	.byte	24
	.word	38894
.L126:
	.byte	24
	.word	38894
.L127:
	.byte	24
	.word	38894
	.byte	24
	.word	38728
	.byte	3
	.word	39238
	.byte	14,8
	.word	39243
	.byte	15,1,0
.L128:
	.byte	14,8
	.word	39248
	.byte	15,0,0,24
	.word	38812
	.byte	3
	.word	39266
	.byte	14,8
	.word	39271
	.byte	15,1,0
.L129:
	.byte	14,8
	.word	39276
	.byte	15,0,0,24
	.word	38486
	.byte	3
	.word	39294
	.byte	14,8
	.word	39299
	.byte	15,1,0
.L130:
	.byte	14,8
	.word	39304
	.byte	15,0,0,24
	.word	38976
	.byte	3
	.word	39322
	.byte	14,12
	.word	39327
	.byte	15,2,0
.L131:
	.byte	14,12
	.word	39332
	.byte	15,0,0,24
	.word	38567
	.byte	3
	.word	39350
	.byte	14,28
	.word	39355
	.byte	15,6,0
.L132:
	.byte	14,28
	.word	39360
	.byte	15,0,0,24
	.word	38631
	.byte	3
	.word	39378
	.byte	14,16
	.word	39383
	.byte	15,3,0,14,128,1
	.word	39388
	.byte	15,7,0
.L133:
	.byte	14,128,1
	.word	39397
	.byte	15,0,0,24
	.word	38894
	.byte	3
	.word	39417
	.byte	14,12
	.word	39422
	.byte	15,2,0
.L134:
	.byte	14,12
	.word	39427
	.byte	15,0,0,0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L4:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,21,0,54,15,39,12,0,0,3,15,0,73,19,0,0,4,46,1,3,8,32,13
	.byte	58,15,59,15,57,15,54,15,39,12,0,0,5,5,0,3,8,58,15,59,15,57,15,73,19,0,0,6,11,0,0,0,7,36,0,3,8,11,15,62
	.byte	15,0,0,8,46,1,3,8,32,13,58,15,59,15,57,15,73,19,54,15,39,12,0,0,9,59,0,3,8,0,0,10,19,1,3,8,58,15,59,15
	.byte	57,15,11,15,0,0,11,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,12,23,1,58,15,59,15,57,15,11,15,0,0,13,13
	.byte	0,3,8,73,19,11,15,56,9,0,0,14,1,1,11,15,73,19,0,0,15,33,0,47,15,0,0,16,53,0,73,19,0,0,17,4,1,58,15,59
	.byte	15,57,15,11,15,0,0,18,40,0,3,8,28,13,0,0,19,46,1,49,19,0,0,20,5,0,49,19,0,0,21,22,0,3,8,58,15,59,15,57
	.byte	15,73,19,0,0,22,21,0,54,15,0,0,23,19,1,58,15,59,15,57,15,11,15,0,0,24,38,0,73,19,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L5:
	.word	.L136-.L135
.L135:
	.half	3
	.word	.L138-.L137
.L137:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg',0
	.byte	0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_Intrinsics.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Port\\Std\\IfxPort.h',0
	.byte	0,0,0
	.byte	'IfxPort_regdef.h',0,1,0,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxScu_PinMap.c',0,0,0,0
	.byte	'IfxScu_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxScu_cfg.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Platform_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Ifx_Types.h',0
	.byte	0,0,0
	.byte	'..\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\_PinMap\\IfxScu_PinMap.h',0,0,0,0,0
.L138:
.L136:
	.sdecl	'.debug_info',debug,cluster('IfxScu_DCDCSYNC_P32_2_OUT')
	.sect	'.debug_info'
.L6:
	.word	276
	.half	3
	.word	.L7
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxScu_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxScu_DCDCSYNC_P32_2_OUT',0,5,48,21
	.word	.L92
	.byte	1,5,3
	.word	IfxScu_DCDCSYNC_P32_2_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScu_DCDCSYNC_P32_2_OUT')
	.sect	'.debug_abbrev'
.L7:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScu_DCDCSYNC_P33_13_OUT')
	.sect	'.debug_info'
.L8:
	.word	277
	.half	3
	.word	.L9
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxScu_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxScu_DCDCSYNC_P33_13_OUT',0,5,49,21
	.word	.L93
	.byte	1,5,3
	.word	IfxScu_DCDCSYNC_P33_13_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScu_DCDCSYNC_P33_13_OUT')
	.sect	'.debug_abbrev'
.L9:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScu_EMGSTOPA_P33_8_IN')
	.sect	'.debug_info'
.L10:
	.word	275
	.half	3
	.word	.L11
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxScu_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxScu_EMGSTOPA_P33_8_IN',0,5,50,19
	.word	.L94
	.byte	1,5,3
	.word	IfxScu_EMGSTOPA_P33_8_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScu_EMGSTOPA_P33_8_IN')
	.sect	'.debug_abbrev'
.L11:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScu_EMGSTOPB_P21_2_IN')
	.sect	'.debug_info'
.L12:
	.word	275
	.half	3
	.word	.L13
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxScu_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxScu_EMGSTOPB_P21_2_IN',0,5,51,19
	.word	.L95
	.byte	1,5,3
	.word	IfxScu_EMGSTOPB_P21_2_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScu_EMGSTOPB_P21_2_IN')
	.sect	'.debug_abbrev'
.L13:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScu_EVRWUPA_P14_1_IN')
	.sect	'.debug_info'
.L14:
	.word	274
	.half	3
	.word	.L15
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxScu_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxScu_EVRWUPA_P14_1_IN',0,5,52,18
	.word	.L96
	.byte	1,5,3
	.word	IfxScu_EVRWUPA_P14_1_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScu_EVRWUPA_P14_1_IN')
	.sect	'.debug_abbrev'
.L15:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScu_EVRWUPB_P15_1_IN')
	.sect	'.debug_info'
.L16:
	.word	274
	.half	3
	.word	.L17
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxScu_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxScu_EVRWUPB_P15_1_IN',0,5,53,18
	.word	.L97
	.byte	1,5,3
	.word	IfxScu_EVRWUPB_P15_1_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScu_EVRWUPB_P15_1_IN')
	.sect	'.debug_abbrev'
.L17:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScu_EXTCLK0_P23_1_OUT')
	.sect	'.debug_info'
.L18:
	.word	275
	.half	3
	.word	.L19
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxScu_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxScu_EXTCLK0_P23_1_OUT',0,5,54,19
	.word	.L98
	.byte	1,5,3
	.word	IfxScu_EXTCLK0_P23_1_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScu_EXTCLK0_P23_1_OUT')
	.sect	'.debug_abbrev'
.L19:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScu_EXTCLK1_P11_12_OUT')
	.sect	'.debug_info'
.L20:
	.word	276
	.half	3
	.word	.L21
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxScu_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxScu_EXTCLK1_P11_12_OUT',0,5,55,19
	.word	.L99
	.byte	1,5,3
	.word	IfxScu_EXTCLK1_P11_12_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScu_EXTCLK1_P11_12_OUT')
	.sect	'.debug_abbrev'
.L21:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScu_EXTCLK1_P32_4_OUT')
	.sect	'.debug_info'
.L22:
	.word	275
	.half	3
	.word	.L23
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxScu_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxScu_EXTCLK1_P32_4_OUT',0,5,56,19
	.word	.L100
	.byte	1,5,3
	.word	IfxScu_EXTCLK1_P32_4_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScu_EXTCLK1_P32_4_OUT')
	.sect	'.debug_abbrev'
.L23:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScu_HWCFG0DCLDO_P14_6_IN')
	.sect	'.debug_info'
.L24:
	.word	278
	.half	3
	.word	.L25
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxScu_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxScu_HWCFG0DCLDO_P14_6_IN',0,5,57,17
	.word	.L101
	.byte	1,5,3
	.word	IfxScu_HWCFG0DCLDO_P14_6_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScu_HWCFG0DCLDO_P14_6_IN')
	.sect	'.debug_abbrev'
.L25:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScu_HWCFG1EVR33_P14_5_IN')
	.sect	'.debug_info'
.L26:
	.word	278
	.half	3
	.word	.L27
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxScu_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxScu_HWCFG1EVR33_P14_5_IN',0,5,58,17
	.word	.L102
	.byte	1,5,3
	.word	IfxScu_HWCFG1EVR33_P14_5_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScu_HWCFG1EVR33_P14_5_IN')
	.sect	'.debug_abbrev'
.L27:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScu_HWCFG2EVR13_P14_2_IN')
	.sect	'.debug_info'
.L28:
	.word	278
	.half	3
	.word	.L29
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxScu_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxScu_HWCFG2EVR13_P14_2_IN',0,5,59,17
	.word	.L103
	.byte	1,5,3
	.word	IfxScu_HWCFG2EVR13_P14_2_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScu_HWCFG2EVR13_P14_2_IN')
	.sect	'.debug_abbrev'
.L29:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScu_HWCFG3_BMI_P14_3_IN')
	.sect	'.debug_info'
.L30:
	.word	277
	.half	3
	.word	.L31
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxScu_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxScu_HWCFG3_BMI_P14_3_IN',0,5,60,17
	.word	.L104
	.byte	1,5,3
	.word	IfxScu_HWCFG3_BMI_P14_3_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScu_HWCFG3_BMI_P14_3_IN')
	.sect	'.debug_abbrev'
.L31:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScu_HWCFG4_P10_5_IN')
	.sect	'.debug_info'
.L32:
	.word	273
	.half	3
	.word	.L33
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxScu_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxScu_HWCFG4_P10_5_IN',0,5,61,17
	.word	.L105
	.byte	1,5,3
	.word	IfxScu_HWCFG4_P10_5_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScu_HWCFG4_P10_5_IN')
	.sect	'.debug_abbrev'
.L33:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScu_HWCFG5_P10_6_IN')
	.sect	'.debug_info'
.L34:
	.word	273
	.half	3
	.word	.L35
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxScu_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxScu_HWCFG5_P10_6_IN',0,5,62,17
	.word	.L106
	.byte	1,5,3
	.word	IfxScu_HWCFG5_P10_6_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScu_HWCFG5_P10_6_IN')
	.sect	'.debug_abbrev'
.L35:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScu_HWCFG6_P14_4_IN')
	.sect	'.debug_info'
.L36:
	.word	273
	.half	3
	.word	.L37
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxScu_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxScu_HWCFG6_P14_4_IN',0,5,63,17
	.word	.L107
	.byte	1,5,3
	.word	IfxScu_HWCFG6_P14_4_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScu_HWCFG6_P14_4_IN')
	.sect	'.debug_abbrev'
.L37:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScu_REQ0_P15_4_IN')
	.sect	'.debug_info'
.L38:
	.word	271
	.half	3
	.word	.L39
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxScu_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxScu_REQ0_P15_4_IN',0,5,64,15
	.word	.L108
	.byte	1,5,3
	.word	IfxScu_REQ0_P15_4_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScu_REQ0_P15_4_IN')
	.sect	'.debug_abbrev'
.L39:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScu_REQ10_P14_3_IN')
	.sect	'.debug_info'
.L40:
	.word	272
	.half	3
	.word	.L41
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxScu_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxScu_REQ10_P14_3_IN',0,5,65,15
	.word	.L109
	.byte	1,5,3
	.word	IfxScu_REQ10_P14_3_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScu_REQ10_P14_3_IN')
	.sect	'.debug_abbrev'
.L41:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScu_REQ11_P20_9_IN')
	.sect	'.debug_info'
.L42:
	.word	272
	.half	3
	.word	.L43
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxScu_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxScu_REQ11_P20_9_IN',0,5,66,15
	.word	.L110
	.byte	1,5,3
	.word	IfxScu_REQ11_P20_9_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScu_REQ11_P20_9_IN')
	.sect	'.debug_abbrev'
.L43:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScu_REQ12_P11_10_IN')
	.sect	'.debug_info'
.L44:
	.word	273
	.half	3
	.word	.L45
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxScu_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxScu_REQ12_P11_10_IN',0,5,67,15
	.word	.L111
	.byte	1,5,3
	.word	IfxScu_REQ12_P11_10_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScu_REQ12_P11_10_IN')
	.sect	'.debug_abbrev'
.L45:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScu_REQ13_P15_5_IN')
	.sect	'.debug_info'
.L46:
	.word	272
	.half	3
	.word	.L47
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxScu_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxScu_REQ13_P15_5_IN',0,5,68,15
	.word	.L112
	.byte	1,5,3
	.word	IfxScu_REQ13_P15_5_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScu_REQ13_P15_5_IN')
	.sect	'.debug_abbrev'
.L47:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScu_REQ14_P02_1_IN')
	.sect	'.debug_info'
.L48:
	.word	272
	.half	3
	.word	.L49
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxScu_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxScu_REQ14_P02_1_IN',0,5,69,15
	.word	.L113
	.byte	1,5,3
	.word	IfxScu_REQ14_P02_1_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScu_REQ14_P02_1_IN')
	.sect	'.debug_abbrev'
.L49:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScu_REQ15_P14_1_IN')
	.sect	'.debug_info'
.L50:
	.word	272
	.half	3
	.word	.L51
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxScu_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxScu_REQ15_P14_1_IN',0,5,70,15
	.word	.L114
	.byte	1,5,3
	.word	IfxScu_REQ15_P14_1_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScu_REQ15_P14_1_IN')
	.sect	'.debug_abbrev'
.L51:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScu_REQ16_P15_1_IN')
	.sect	'.debug_info'
.L52:
	.word	272
	.half	3
	.word	.L53
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxScu_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxScu_REQ16_P15_1_IN',0,5,71,15
	.word	.L115
	.byte	1,5,3
	.word	IfxScu_REQ16_P15_1_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScu_REQ16_P15_1_IN')
	.sect	'.debug_abbrev'
.L53:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScu_REQ1_P15_8_IN')
	.sect	'.debug_info'
.L54:
	.word	271
	.half	3
	.word	.L55
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxScu_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxScu_REQ1_P15_8_IN',0,5,72,15
	.word	.L116
	.byte	1,5,3
	.word	IfxScu_REQ1_P15_8_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScu_REQ1_P15_8_IN')
	.sect	'.debug_abbrev'
.L55:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScu_REQ2_P10_2_IN')
	.sect	'.debug_info'
.L56:
	.word	271
	.half	3
	.word	.L57
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxScu_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxScu_REQ2_P10_2_IN',0,5,73,15
	.word	.L117
	.byte	1,5,3
	.word	IfxScu_REQ2_P10_2_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScu_REQ2_P10_2_IN')
	.sect	'.debug_abbrev'
.L57:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScu_REQ3_P10_3_IN')
	.sect	'.debug_info'
.L58:
	.word	271
	.half	3
	.word	.L59
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxScu_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxScu_REQ3_P10_3_IN',0,5,74,15
	.word	.L118
	.byte	1,5,3
	.word	IfxScu_REQ3_P10_3_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScu_REQ3_P10_3_IN')
	.sect	'.debug_abbrev'
.L59:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScu_REQ4_P10_7_IN')
	.sect	'.debug_info'
.L60:
	.word	271
	.half	3
	.word	.L61
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxScu_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxScu_REQ4_P10_7_IN',0,5,75,15
	.word	.L119
	.byte	1,5,3
	.word	IfxScu_REQ4_P10_7_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScu_REQ4_P10_7_IN')
	.sect	'.debug_abbrev'
.L61:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScu_REQ5_P10_8_IN')
	.sect	'.debug_info'
.L62:
	.word	271
	.half	3
	.word	.L63
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxScu_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxScu_REQ5_P10_8_IN',0,5,76,15
	.word	.L120
	.byte	1,5,3
	.word	IfxScu_REQ5_P10_8_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScu_REQ5_P10_8_IN')
	.sect	'.debug_abbrev'
.L63:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScu_REQ6_P02_0_IN')
	.sect	'.debug_info'
.L64:
	.word	271
	.half	3
	.word	.L65
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxScu_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxScu_REQ6_P02_0_IN',0,5,77,15
	.word	.L121
	.byte	1,5,3
	.word	IfxScu_REQ6_P02_0_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScu_REQ6_P02_0_IN')
	.sect	'.debug_abbrev'
.L65:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScu_REQ7_P00_4_IN')
	.sect	'.debug_info'
.L66:
	.word	271
	.half	3
	.word	.L67
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxScu_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxScu_REQ7_P00_4_IN',0,5,78,15
	.word	.L122
	.byte	1,5,3
	.word	IfxScu_REQ7_P00_4_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScu_REQ7_P00_4_IN')
	.sect	'.debug_abbrev'
.L67:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScu_REQ8_P33_7_IN')
	.sect	'.debug_info'
.L68:
	.word	271
	.half	3
	.word	.L69
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxScu_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxScu_REQ8_P33_7_IN',0,5,79,15
	.word	.L123
	.byte	1,5,3
	.word	IfxScu_REQ8_P33_7_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScu_REQ8_P33_7_IN')
	.sect	'.debug_abbrev'
.L69:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScu_REQ9_P20_0_IN')
	.sect	'.debug_info'
.L70:
	.word	271
	.half	3
	.word	.L71
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxScu_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxScu_REQ9_P20_0_IN',0,5,80,15
	.word	.L124
	.byte	1,5,3
	.word	IfxScu_REQ9_P20_0_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScu_REQ9_P20_0_IN')
	.sect	'.debug_abbrev'
.L71:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScu_WDT0LCK_P20_8_OUT')
	.sect	'.debug_info'
.L72:
	.word	275
	.half	3
	.word	.L73
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxScu_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxScu_WDT0LCK_P20_8_OUT',0,5,81,19
	.word	.L125
	.byte	1,5,3
	.word	IfxScu_WDT0LCK_P20_8_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScu_WDT0LCK_P20_8_OUT')
	.sect	'.debug_abbrev'
.L73:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScu_WDT1LCK_P20_7_OUT')
	.sect	'.debug_info'
.L74:
	.word	275
	.half	3
	.word	.L75
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxScu_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxScu_WDT1LCK_P20_7_OUT',0,5,82,19
	.word	.L126
	.byte	1,5,3
	.word	IfxScu_WDT1LCK_P20_7_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScu_WDT1LCK_P20_7_OUT')
	.sect	'.debug_abbrev'
.L75:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScu_WDTSLCK_P20_9_OUT')
	.sect	'.debug_info'
.L76:
	.word	275
	.half	3
	.word	.L77
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxScu_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxScu_WDTSLCK_P20_9_OUT',0,5,83,19
	.word	.L127
	.byte	1,5,3
	.word	IfxScu_WDTSLCK_P20_9_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScu_WDTSLCK_P20_9_OUT')
	.sect	'.debug_abbrev'
.L77:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScu_Dcdcsync_Out_pinTable')
	.sect	'.debug_info'
.L78:
	.word	279
	.half	3
	.word	.L79
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxScu_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxScu_Dcdcsync_Out_pinTable',0,5,86,28
	.word	.L128
	.byte	1,5,3
	.word	IfxScu_Dcdcsync_Out_pinTable
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScu_Dcdcsync_Out_pinTable')
	.sect	'.debug_abbrev'
.L79:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScu_Emgstop_In_pinTable')
	.sect	'.debug_info'
.L80:
	.word	277
	.half	3
	.word	.L81
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxScu_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxScu_Emgstop_In_pinTable',0,5,93,26
	.word	.L129
	.byte	1,5,3
	.word	IfxScu_Emgstop_In_pinTable
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScu_Emgstop_In_pinTable')
	.sect	'.debug_abbrev'
.L81:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScu_Evrwup_In_pinTable')
	.sect	'.debug_info'
.L82:
	.word	276
	.half	3
	.word	.L83
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxScu_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxScu_Evrwup_In_pinTable',0,5,100,25
	.word	.L130
	.byte	1,5,3
	.word	IfxScu_Evrwup_In_pinTable
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScu_Evrwup_In_pinTable')
	.sect	'.debug_abbrev'
.L83:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScu_Extclk_Out_pinTable')
	.sect	'.debug_info'
.L84:
	.word	277
	.half	3
	.word	.L85
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxScu_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxScu_Extclk_Out_pinTable',0,5,107,26
	.word	.L131
	.byte	1,5,3
	.word	IfxScu_Extclk_Out_pinTable
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScu_Extclk_Out_pinTable')
	.sect	'.debug_abbrev'
.L85:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScu_Hwcfg_In_pinTable')
	.sect	'.debug_info'
.L86:
	.word	275
	.half	3
	.word	.L87
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxScu_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxScu_Hwcfg_In_pinTable',0,5,115,24
	.word	.L132
	.byte	1,5,3
	.word	IfxScu_Hwcfg_In_pinTable
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScu_Hwcfg_In_pinTable')
	.sect	'.debug_abbrev'
.L87:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScu_Req_In_pinTable')
	.sect	'.debug_info'
.L88:
	.word	273
	.half	3
	.word	.L89
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxScu_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxScu_Req_In_pinTable',0,5,127,22
	.word	.L133
	.byte	1,5,3
	.word	IfxScu_Req_In_pinTable
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScu_Req_In_pinTable')
	.sect	'.debug_abbrev'
.L89:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScu_Wdtlck_Out_pinTable')
	.sect	'.debug_info'
.L90:
	.word	278
	.half	3
	.word	.L91
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxScu_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxScu_Wdtlck_Out_pinTable',0,5,180,1,26
	.word	.L134
	.byte	1,5,3
	.word	IfxScu_Wdtlck_Out_pinTable
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScu_Wdtlck_Out_pinTable')
	.sect	'.debug_abbrev'
.L91:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	; Module end
