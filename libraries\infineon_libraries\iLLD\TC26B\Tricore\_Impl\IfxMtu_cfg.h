/**
 * \file IfxMtu_cfg.h
 * \brief Mtu on-chip implementation data
 * \ingroup IfxLld_Mtu
 *
 * \version iLLD_1_0_1_11_0
 * \copyright Copyright (c) 2016 Infineon Technologies AG. All rights reserved.
 *
 *
 *                                 IMPORTANT NOTICE
 *
 *
 * Use of this file is subject to the terms of use agreed between (i) you or 
 * the company in which ordinary course of business you are acting and (ii) 
 * Infineon Technologies AG or its licensees. If and as long as no such 
 * terms of use are agreed, use of this file is subject to following:


 * Boost Software License - Version 1.0 - August 17th, 2003

 * Permission is hereby granted, free of charge, to any person or 
 * organization obtaining a copy of the software and accompanying 
 * documentation covered by this license (the "Software") to use, reproduce,
 * display, distribute, execute, and transmit the Software, and to prepare
 * derivative works of the Software, and to permit third-parties to whom the 
 * Software is furnished to do so, all subject to the following:

 * The copyright notices in the Software and this entire statement, including
 * the above license grant, this restriction and the following disclaimer, must
 * be included in all copies of the Software, in whole or in part, and all
 * derivative works of the Software, unless such copies or derivative works are
 * solely in the form of machine-executable object code generated by a source
 * language processor.

 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR 
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE, TITLE AND NON-INFRINGEMENT. IN NO EVENT
 * SHALL THE COPYRIGHT HOLDERS OR ANYONE DISTRIBUTING THE SOFTWARE BE LIABLE 
 * FOR ANY DAMAGES OR OTHER LIABILITY, WHETHER IN CONTRACT, TORT OR OTHERWISE,
 * ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
 * DEALINGS IN THE SOFTWARE.

 *
 * \defgroup IfxLld_Mtu MTU
 * \ingroup IfxLld
 * \defgroup IfxLld_Mtu_Impl Implementation
 * \ingroup IfxLld_Mtu
 * \defgroup IfxLld_Mtu_Std Standard Driver
 * \ingroup IfxLld_Mtu
 */

#ifndef IFXMTU_CFG_H
#define IFXMTU_CFG_H 1

/******************************************************************************/
/*----------------------------------Includes----------------------------------*/
/******************************************************************************/

#include "Cpu/Std/IfxCpu.h"
#include "Cpu/Std/IfxCpu_Intrinsics.h"
#include "IfxMtu_reg.h"
#include "IfxMc_reg.h"

/******************************************************************************/
/*-----------------------------------Macros-----------------------------------*/
/******************************************************************************/

/** \brief Base address of first MBIST Control Block
 */
#define IFXMTU_MC_ADDRESS_BASE       (0xF0061000u)

/** \brief Number of MBIST Table items
 */
#define IFXMTU_NUM_MBIST_TABLE_ITEMS (88)

/** \brief Maximum number of tracked SRAM addresses (ETTR)
 */
#define IFXMTU_MAX_TRACKED_ADDRESSES (5)

/******************************************************************************/
/*-------------------------------Enumerations---------------------------------*/
/******************************************************************************/

/** \brief MBIST Selection
 */
typedef enum
{
    IfxMtu_MbistSel_none       = -1,
    IfxMtu_MbistSel_cpu1Dspr   = 6,
    IfxMtu_MbistSel_cpu1Dtag   = 8,
    IfxMtu_MbistSel_cpu1Pspr   = 9,
    IfxMtu_MbistSel_cpu1Ptag   = 11,
    IfxMtu_MbistSel_cpu0Dspr   = 14,
    IfxMtu_MbistSel_cpu0Pspr   = 16,
    IfxMtu_MbistSel_cpu0Ptag   = 17,
    IfxMtu_MbistSel_ethermac   = 22,
    IfxMtu_MbistSel_mod4       = 26,
    IfxMtu_MbistSel_gtmFifo    = 28,
    IfxMtu_MbistSel_gtmMcs0    = 29,
    IfxMtu_MbistSel_gtmMcs1    = 30,
    IfxMtu_MbistSel_gtmDpll1a  = 31,
    IfxMtu_MbistSel_gtmDpll1b  = 32,
    IfxMtu_MbistSel_gtmDpll2   = 33,
    IfxMtu_MbistSel_psi5       = 34,
    IfxMtu_MbistSel_mcan       = 36,
    IfxMtu_MbistSel_erayObf    = 38,
    IfxMtu_MbistSel_erayIbfTbf = 39,
    IfxMtu_MbistSel_erayMbf    = 40,
    IfxMtu_MbistSel_stdbyRam1  = 44,
    IfxMtu_MbistSel_mcds       = 45,
    IfxMtu_MbistSel_emem0      = 46,
    IfxMtu_MbistSel_emem1      = 47,
    IfxMtu_MbistSel_emem2      = 48,
    IfxMtu_MbistSel_emem3      = 49,
    IfxMtu_MbistSel_emem4      = 50,
    IfxMtu_MbistSel_emem5      = 51,
    IfxMtu_MbistSel_emem6      = 52,
    IfxMtu_MbistSel_emem7      = 53,
    IfxMtu_MbistSel_cifJpeg1_4 = 78,
    IfxMtu_MbistSel_cifJpeg3   = 80,
    IfxMtu_MbistSel_cifCif     = 81,
    IfxMtu_MbistSel_stdbyRam2  = 82,
    IfxMtu_MbistSel_dma        = 83,
    IfxMtu_MbistSel_ememXtm0   = 84,
    IfxMtu_MbistSel_ememXtm1   = 85,
    IfxMtu_MbistSel_fft0       = 86,
    IfxMtu_MbistSel_fft1       = 87
} IfxMtu_MbistSel;

/******************************************************************************/
/*-----------------------------Data Structures--------------------------------*/
/******************************************************************************/

/** \brief Describes physical parameters of a SRAM memory
 */
typedef struct
{
    uint8  numBlocks;      /**< \brief number of SRAM blocks */
    uint16 dataSize;       /**< \brief Data Size of each memory block */
    uint8  eccSize;        /**< \brief ECC Size of each memory block */
    uint8  eccInvPos0;     /**< \brief First ECC bit which needs to be inverted */
    uint8  eccInvPos1;     /**< \brief Second ECC bit which needs to be inverted */
    uint32 mbistDelay;     /**< \brief Mbist Delay  */
} IfxMtu_SramItem;

/******************************************************************************/
/*-------------------Global Exported Variables/Constants----------------------*/
/******************************************************************************/

IFX_EXTERN const IfxMtu_SramItem IfxMtu_sramTable[IFXMTU_NUM_MBIST_TABLE_ITEMS];

#endif /* IFXMTU_CFG_H */
