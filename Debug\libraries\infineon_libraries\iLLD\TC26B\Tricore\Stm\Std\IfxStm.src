	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc18812a --dep-file=IfxStm.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2\\\\xf_asr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/infineon_libraries/iLLD/TC26B/Tricore/Stm/Std/IfxStm.src ../libraries/infineon_libraries/iLLD/TC26B/Tricore/Stm/Std/IfxStm.c"
	.compiler_name		"ctc"
	;source	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Stm/Std/IfxStm.c'

	
$TC16X
	
	.sdecl	'.text.IfxStm.IfxStm_clearCompareFlag',code,cluster('IfxStm_clearCompareFlag')
	.sect	'.text.IfxStm.IfxStm_clearCompareFlag'
	.align	2
	
	.global	IfxStm_clearCompareFlag
; Function IfxStm_clearCompareFlag
.L41:
IfxStm_clearCompareFlag:	.type	func
	jne	d4,#0,.L2
.L336:
	ld.bu	d15,[a4]64
.L337:
	or	d15,#1
	st.b	[a4]64,d15
.L338:
	j	.L3
.L2:
	jne	d4,#1,.L4
.L339:
	ld.bu	d15,[a4]64
.L340:
	or	d15,#4
	st.b	[a4]64,d15
.L4:
.L3:
	ret
.L153:
	
__IfxStm_clearCompareFlag_function_end:
	.size	IfxStm_clearCompareFlag,__IfxStm_clearCompareFlag_function_end-IfxStm_clearCompareFlag
.L94:
	; End of function
	
	.sdecl	'.text.IfxStm.IfxStm_disableComparatorInterrupt',code,cluster('IfxStm_disableComparatorInterrupt')
	.sect	'.text.IfxStm.IfxStm_disableComparatorInterrupt'
	.align	2
	
	.global	IfxStm_disableComparatorInterrupt
; Function IfxStm_disableComparatorInterrupt
.L43:
IfxStm_disableComparatorInterrupt:	.type	func
	jne	d4,#0,.L5
.L345:
	ld.bu	d15,[a4]60
.L346:
	insert	d15,d15,#0,#0,#1
	st.b	[a4]60,d15
.L347:
	j	.L6
.L5:
	ld.bu	d15,[a4]60
.L348:
	insert	d15,d15,#0,#4,#1
	st.b	[a4]60,d15
.L6:
	ret
.L157:
	
__IfxStm_disableComparatorInterrupt_function_end:
	.size	IfxStm_disableComparatorInterrupt,__IfxStm_disableComparatorInterrupt_function_end-IfxStm_disableComparatorInterrupt
.L99:
	; End of function
	
	.sdecl	'.text.IfxStm.IfxStm_disableModule',code,cluster('IfxStm_disableModule')
	.sect	'.text.IfxStm.IfxStm_disableModule'
	.align	2
	
	.global	IfxStm_disableModule
; Function IfxStm_disableModule
.L45:
IfxStm_disableModule:	.type	func
	mov.aa	a15,a4
.L239:
	call	IfxScuWdt_getCpuWatchdogPassword
.L238:
	mov	d8,d2
.L241:
	mov	d4,d8
.L240:
	call	IfxScuWdt_clearCpuEndinit
.L242:
	ld.bu	d15,[a15]
.L298:
	or	d15,#1
	st.b	[a15],d15
.L299:
	mov	d4,d8
.L243:
	call	IfxScuWdt_setCpuEndinit
.L244:
	ret
.L135:
	
__IfxStm_disableModule_function_end:
	.size	IfxStm_disableModule,__IfxStm_disableModule_function_end-IfxStm_disableModule
.L74:
	; End of function
	
	.sdecl	'.text.IfxStm.IfxStm_enableComparatorInterrupt',code,cluster('IfxStm_enableComparatorInterrupt')
	.sect	'.text.IfxStm.IfxStm_enableComparatorInterrupt'
	.align	2
	
	.global	IfxStm_enableComparatorInterrupt
; Function IfxStm_enableComparatorInterrupt
.L47:
IfxStm_enableComparatorInterrupt:	.type	func
	jne	d4,#0,.L7
.L353:
	ld.bu	d15,[a4]60
.L354:
	or	d15,#1
	st.b	[a4]60,d15
.L355:
	j	.L8
.L7:
	jne	d4,#1,.L9
.L356:
	ld.bu	d15,[a4]60
.L357:
	or	d15,#16
	st.b	[a4]60,d15
.L9:
.L8:
	ret
.L160:
	
__IfxStm_enableComparatorInterrupt_function_end:
	.size	IfxStm_enableComparatorInterrupt,__IfxStm_enableComparatorInterrupt_function_end-IfxStm_enableComparatorInterrupt
.L104:
	; End of function
	
	.sdecl	'.text.IfxStm.IfxStm_enableOcdsSuspend',code,cluster('IfxStm_enableOcdsSuspend')
	.sect	'.text.IfxStm.IfxStm_enableOcdsSuspend'
	.align	2
	
	.global	IfxStm_enableOcdsSuspend
; Function IfxStm_enableOcdsSuspend
.L49:
IfxStm_enableOcdsSuspend:	.type	func
	ld.w	d15,[a4]232
.L245:
	mov	d0,#1
.L304:
	insert	d15,d15,d0,#28,#1
.L305:
	mov	d0,#2
.L306:
	insert	d15,d15,d0,#24,#4
.L307:
	st.w	[a4]232,d15
.L308:
	ld.bu	d15,[a4]235
.L246:
	insert	d15,d15,#0,#4,#1
	st.b	[a4]235,d15
.L309:
	ret
.L140:
	
__IfxStm_enableOcdsSuspend_function_end:
	.size	IfxStm_enableOcdsSuspend,__IfxStm_enableOcdsSuspend_function_end-IfxStm_enableOcdsSuspend
.L79:
	; End of function
	
	.sdecl	'.text.IfxStm.IfxStm_getAddress',code,cluster('IfxStm_getAddress')
	.sect	'.text.IfxStm.IfxStm_getAddress'
	.align	2
	
	.global	IfxStm_getAddress
; Function IfxStm_getAddress
.L51:
IfxStm_getAddress:	.type	func
	jge	d4,#2,.L10
.L314:
	mul	d15,d4,#8
.L315:
	movh.a	a15,#@his(IfxStm_cfg_indexMap)
	lea	a15,[a15]@los(IfxStm_cfg_indexMap)
.L316:
	addsc.a	a15,a15,d15,#0
.L317:
	ld.a	a2,[a15]
.L247:
	j	.L11
.L10:
	mov.a	a2,#0
.L11:
	j	.L12
.L12:
	ret
.L144:
	
__IfxStm_getAddress_function_end:
	.size	IfxStm_getAddress,__IfxStm_getAddress_function_end-IfxStm_getAddress
.L84:
	; End of function
	
	.sdecl	'.text.IfxStm.IfxStm_getIndex',code,cluster('IfxStm_getIndex')
	.sect	'.text.IfxStm.IfxStm_getIndex'
	.align	2
	
	.global	IfxStm_getIndex
; Function IfxStm_getIndex
.L53:
IfxStm_getIndex:	.type	func
	mov	d2,#-1
.L248:
	mov	d0,#0
.L249:
	j	.L13
.L14:
	mul	d15,d0,#8
.L322:
	movh.a	a15,#@his(IfxStm_cfg_indexMap)
	lea	a15,[a15]@los(IfxStm_cfg_indexMap)
.L323:
	addsc.a	a15,a15,d15,#0
.L324:
	ld.a	a15,[a15]
.L325:
	jne.a	a15,a4,.L15
.L326:
	mul	d15,d0,#8
.L327:
	movh.a	a15,#@his(IfxStm_cfg_indexMap)
	lea	a15,[a15]@los(IfxStm_cfg_indexMap)
.L328:
	addsc.a	a15,a15,d15,#0
.L329:
	ld.w	d15,[a15]4
.L330:
	extr	d2,d15,#0,#8
.L331:
	j	.L16
.L15:
	add	d0,#1
.L13:
	jlt.u	d0,#2,.L14
.L16:
	j	.L17
.L17:
	ret
.L148:
	
__IfxStm_getIndex_function_end:
	.size	IfxStm_getIndex,__IfxStm_getIndex_function_end-IfxStm_getIndex
.L89:
	; End of function
	
	.sdecl	'.text.IfxStm.IfxStm_getSrcPointer',code,cluster('IfxStm_getSrcPointer')
	.sect	'.text.IfxStm.IfxStm_getSrcPointer'
	.align	2
	
	.global	IfxStm_getSrcPointer
; Function IfxStm_getSrcPointer
.L55:
IfxStm_getSrcPointer:	.type	func
	mov	d15,d4
.L251:
	call	IfxStm_getIndex
.L250:
	jne	d15,#0,.L18
.L362:
	mul	d15,d2,#8
.L252:
	mov.a	a15,d15
	movh.a	a3,#61444
	add.a	a3,a15
	lea	a2,[a3]-31600
.L363:
	j	.L19
.L18:
	mul	d15,d2,#8
.L253:
	mov.a	a15,d15
	movh.a	a3,#61444
	add.a	a3,a15
	lea	a2,[a3]-31600
.L364:
	add.a	a2,#4
.L19:
	j	.L20
.L20:
	ret
.L164:
	
__IfxStm_getSrcPointer_function_end:
	.size	IfxStm_getSrcPointer,__IfxStm_getSrcPointer_function_end-IfxStm_getSrcPointer
.L109:
	; End of function
	
	.sdecl	'.text.IfxStm.IfxStm_initCompare',code,cluster('IfxStm_initCompare')
	.sect	'.text.IfxStm.IfxStm_initCompare'
	.align	2
	
	.global	IfxStm_initCompare
; Function IfxStm_initCompare
.L57:
IfxStm_initCompare:	.type	func
	mov.aa	a12,a4
.L255:
	mov.aa	a15,a5
.L256:
	ld.w	d0,[a12]56
.L257:
	ld.w	d1,[a12]60
.L258:
	ld.bu	d15,[a15]
.L369:
	jne	d15,#0,.L21
.L370:
	ld.bu	d15,[a15]3
.L371:
	insert	d0,d0,d15,#0,#5
.L372:
	ld.bu	d15,[a15]2
.L373:
	insert	d0,d0,d15,#8,#5
.L374:
	ld.bu	d15,[a15]1
.L375:
	insert	d1,d1,d15,#2,#1
.L376:
	mov	d8,#1
.L259:
	j	.L22
.L21:
	ld.bu	d15,[a15]
.L377:
	jne	d15,#1,.L23
.L378:
	ld.bu	d15,[a15]3
.L379:
	insert	d0,d0,d15,#16,#5
.L380:
	ld.bu	d15,[a15]2
.L381:
	insert	d0,d0,d15,#24,#5
.L382:
	ld.bu	d15,[a15]1
.L383:
	insert	d1,d1,d15,#6,#1
.L384:
	mov	d8,#1
.L260:
	j	.L24
.L23:
	mov	d8,#0
.L24:
.L22:
	st.w	[a12]60,d1
.L385:
	st.w	[a12]56,d0
.L386:
	mov.aa	a4,a12
	call	IfxStm_getIndex
.L254:
	ld.hu	d15,[a15]8
.L387:
	jlt.u	d15,#1,.L25
.L180:
	ld.bu	d15,[a15]1
.L388:
	jne	d15,#0,.L26
.L389:
	movh.a	a2,#61444
	lea	a2,[a2]@los(0xf0038490)
.L390:
	mul	d15,d2,#8
	addsc.a	a2,a2,d15,#0
.L262:
	j	.L27
.L26:
	movh.a	a2,#61444
	lea	a2,[a2]@los(0xf0038490)
.L391:
	mul	d15,d2,#8
	addsc.a	a2,a2,d15,#0
.L263:
	add.a	a2,#4
.L27:
	ld.bu	d0,[a15]10
.L392:
	ld.hu	d15,[a15]8
.L183:
	ld.bu	d1,[a2]
.L393:
	extr.u	d15,d15,#0,#8
.L394:
	insert	d15,d1,d15,#0,#8
	st.b	[a2],d15
.L395:
	ld.bu	d15,[a2]1
.L396:
	insert	d15,d15,d0,#3,#2
	st.b	[a2]1,d15
.L193:
	ld.bu	d15,[a2]3
.L397:
	or	d15,#2
	st.b	[a2]3,d15
.L184:
	ld.bu	d15,[a2]1
.L398:
	or	d15,#4
	st.b	[a2]1,d15
.L25:
	ld.bu	d6,[a15]2
.L202:
	ld.w	d15,[a12]16
	mov	d1,#0
	mov	d0,d15
.L264:
	ld.w	d3,[a12]44
.L399:
	mov	d2,#0
.L261:
	or	d4,d0,d2
.L265:
	or	d5,d1,d3
.L266:
	j	.L28
.L28:
	call	__ll_ushr64
.L267:
	j	.L29
.L29:
	ld.bu	d15,[a15]
.L400:
	mul	d15,d15,#4
	addsc.a	a2,a12,d15,#0
.L401:
	st.w	[a2]48,d2
.L402:
	ld.bu	d4,[a15]
	mov.aa	a4,a12
.L268:
	call	IfxStm_clearCompareFlag
.L269:
	ld.bu	d4,[a15]
	mov.aa	a4,a12
.L270:
	call	IfxStm_enableComparatorInterrupt
.L271:
	ld.bu	d6,[a15]2
.L215:
	ld.w	d15,[a12]16
	mov	d1,#0
	mov	d0,d15
.L272:
	ld.w	d3,[a12]44
.L403:
	mov	d2,#0
.L404:
	or	d4,d0,d2
.L273:
	or	d5,d1,d3
.L274:
	j	.L30
.L30:
	call	__ll_ushr64
.L275:
	j	.L31
.L31:
	ld.bu	d15,[a15]
.L405:
	mul	d15,d15,#4
	addsc.a	a2,a12,d15,#0
.L406:
	ld.w	d15,[a15]4
.L407:
	add	d2,d15
.L408:
	st.w	[a2]48,d2
.L409:
	mov	d2,d8
.L276:
	j	.L32
.L32:
	ret
.L169:
	
__IfxStm_initCompare_function_end:
	.size	IfxStm_initCompare,__IfxStm_initCompare_function_end-IfxStm_initCompare
.L114:
	; End of function
	
	.sdecl	'.text.IfxStm.IfxStm_initCompareConfig',code,cluster('IfxStm_initCompareConfig')
	.sect	'.text.IfxStm.IfxStm_initCompareConfig'
	.align	2
	
	.global	IfxStm_initCompareConfig
; Function IfxStm_initCompareConfig
.L59:
IfxStm_initCompareConfig:	.type	func
	mov	d15,#0
.L414:
	st.b	[a4],d15
.L415:
	mov	d15,#0
.L416:
	st.b	[a4]2,d15
.L417:
	mov	d15,#31
.L418:
	st.b	[a4]3,d15
.L419:
	mov	d15,#0
.L420:
	st.b	[a4]1,d15
.L421:
	mov	d15,#-1
.L422:
	st.w	[a4]4,d15
.L423:
	mov	d15,#0
.L424:
	st.h	[a4]8,d15
.L425:
	mov	d15,#0
.L426:
	st.b	[a4]10,d15
.L427:
	ret
.L218:
	
__IfxStm_initCompareConfig_function_end:
	.size	IfxStm_initCompareConfig,__IfxStm_initCompareConfig_function_end-IfxStm_initCompareConfig
.L119:
	; End of function
	
	.sdecl	'.text.IfxStm.IfxStm_isCompareFlagSet',code,cluster('IfxStm_isCompareFlagSet')
	.sect	'.text.IfxStm.IfxStm_isCompareFlagSet'
	.align	2
	
	.global	IfxStm_isCompareFlagSet
; Function IfxStm_isCompareFlagSet
.L61:
IfxStm_isCompareFlagSet:	.type	func
	jne	d4,#0,.L33
.L432:
	ld.bu	d15,[a4]60
	extr.u	d2,d15,#1,#1
.L433:
	j	.L34
.L33:
	ld.bu	d15,[a4]60
	extr.u	d2,d15,#5,#1
.L434:
	j	.L35
.L35:
.L34:
	ret
.L221:
	
__IfxStm_isCompareFlagSet_function_end:
	.size	IfxStm_isCompareFlagSet,__IfxStm_isCompareFlagSet_function_end-IfxStm_isCompareFlagSet
.L124:
	; End of function
	
	.sdecl	'.text.IfxStm.IfxStm_resetModule',code,cluster('IfxStm_resetModule')
	.sect	'.text.IfxStm.IfxStm_resetModule'
	.align	2
	
	.global	IfxStm_resetModule
; Function IfxStm_resetModule
.L63:
IfxStm_resetModule:	.type	func
	mov.aa	a15,a4
.L278:
	call	IfxScuWdt_getCpuWatchdogPassword
.L277:
	mov	d8,d2
.L280:
	mov	d4,d8
.L279:
	call	IfxScuWdt_clearCpuEndinit
.L281:
	ld.bu	d15,[a15]244
.L451:
	or	d15,#1
	st.b	[a15]244,d15
.L452:
	ld.bu	d15,[a15]240
.L453:
	or	d15,#1
	st.b	[a15]240,d15
.L454:
	mov	d4,d8
.L282:
	call	IfxScuWdt_setCpuEndinit
.L283:
	j	.L36
.L37:
.L36:
	ld.bu	d15,[a15]244
.L455:
	jz.t	d15:1,.L37
.L456:
	mov	d4,d8
.L284:
	call	IfxScuWdt_clearCpuEndinit
.L285:
	ld.bu	d15,[a15]236
.L457:
	or	d15,#1
	st.b	[a15]236,d15
.L458:
	mov	d4,d8
.L286:
	call	IfxScuWdt_setCpuEndinit
.L287:
	ret
.L235:
	
__IfxStm_resetModule_function_end:
	.size	IfxStm_resetModule,__IfxStm_resetModule_function_end-IfxStm_resetModule
.L134:
	; End of function
	
	.sdecl	'.text.IfxStm.IfxStm_setCompareControl',code,cluster('IfxStm_setCompareControl')
	.sect	'.text.IfxStm.IfxStm_setCompareControl'
	.align	2
	
	.global	IfxStm_setCompareControl
; Function IfxStm_setCompareControl
.L65:
IfxStm_setCompareControl:	.type	func
	ld.w	d15,[a4]56
.L288:
	ld.w	d0,[a4]60
.L289:
	jne	d4,#0,.L38
.L439:
	insert	d15,d15,d6,#0,#5
.L440:
	insert	d15,d15,d5,#8,#5
.L441:
	insert	d0,d0,d7,#2,#1
.L442:
	j	.L39
.L38:
	insert	d15,d15,d6,#16,#5
.L443:
	insert	d15,d15,d5,#24,#5
.L444:
	insert	d0,d0,d7,#6,#1
.L39:
	st.w	[a4]60,d0
.L445:
	st.w	[a4]56,d15
.L446:
	ret
.L224:
	
__IfxStm_setCompareControl_function_end:
	.size	IfxStm_setCompareControl,__IfxStm_setCompareControl_function_end-IfxStm_setCompareControl
.L129:
	; End of function
	
	.calls	'IfxStm_initCompare','__ll_ushr64'
	.calls	'IfxStm_disableModule','IfxScuWdt_getCpuWatchdogPassword'
	.calls	'IfxStm_disableModule','IfxScuWdt_clearCpuEndinit'
	.calls	'IfxStm_disableModule','IfxScuWdt_setCpuEndinit'
	.calls	'IfxStm_getSrcPointer','IfxStm_getIndex'
	.calls	'IfxStm_initCompare','IfxStm_getIndex'
	.calls	'IfxStm_initCompare','IfxStm_clearCompareFlag'
	.calls	'IfxStm_initCompare','IfxStm_enableComparatorInterrupt'
	.calls	'IfxStm_resetModule','IfxScuWdt_getCpuWatchdogPassword'
	.calls	'IfxStm_resetModule','IfxScuWdt_clearCpuEndinit'
	.calls	'IfxStm_resetModule','IfxScuWdt_setCpuEndinit'
	.calls	'IfxStm_clearCompareFlag','',0
	.calls	'IfxStm_disableComparatorInterrupt','',0
	.calls	'IfxStm_disableModule','',0
	.calls	'IfxStm_enableComparatorInterrupt','',0
	.calls	'IfxStm_enableOcdsSuspend','',0
	.calls	'IfxStm_getAddress','',0
	.calls	'IfxStm_getIndex','',0
	.calls	'IfxStm_getSrcPointer','',0
	.calls	'IfxStm_initCompare','',0
	.calls	'IfxStm_initCompareConfig','',0
	.calls	'IfxStm_isCompareFlagSet','',0
	.calls	'IfxStm_resetModule','',0
	.extern	IfxStm_cfg_indexMap
	.extern	IfxScuWdt_clearCpuEndinit
	.extern	IfxScuWdt_setCpuEndinit
	.extern	IfxScuWdt_getCpuWatchdogPassword
	.extern	__ll_ushr64
	.calls	'IfxStm_setCompareControl','',0
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L67:
	.word	81410
	.half	3
	.word	.L68
	.byte	4
.L66:
	.byte	1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Stm/Std/IfxStm.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L69
	.byte	2,1,1,3
	.word	230
	.byte	4
	.byte	'Ifx__jump_and_link',0,3,1,61,17,1,1,5
	.byte	'fun',0,1,61,43
	.word	233
	.byte	6,0,7
	.byte	'__fract',0,4,128,1,7
	.byte	'float',0,4,4,8
	.byte	'Ifx__float_to_fract',0,3,1,152,2,18
	.word	278
	.byte	1,1,5
	.byte	'a',0,1,152,2,44
	.word	290
	.byte	6,0,4
	.byte	'Ifx__stopPerfCounters',0,3,1,172,2,17,1,1,6,0
.L208:
	.byte	7
	.byte	'unsigned long long int',0,8,7,9
	.byte	'void',0,3
	.word	402
	.byte	8
	.byte	'__ld64',0,3,2,135,1,19
	.word	376
	.byte	1,1,5
	.byte	'addr',0,2,135,1,32
	.word	408
	.byte	6,0,4
	.byte	'__st64',0,3,2,143,1,17,1,1,5
	.byte	'addr',0,2,143,1,30
	.word	408
	.byte	5
	.byte	'value',0,2,143,1,43
	.word	376
	.byte	6,0,7
	.byte	'unsigned int',0,4,7,7
	.byte	'int',0,4,5,7
	.byte	'unsigned int',0,4,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON0_Bits',0,4,241,8,16,4,11
	.byte	'ENDINIT',0,4
	.word	517
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	517
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	517
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	517
	.byte	16,0,2,35,0,0,12,4,247,14,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	533
	.byte	4,2,35,0,0
.L168:
	.byte	7
	.byte	'unsigned char',0,1,8
.L138:
	.byte	7
	.byte	'unsigned short int',0,2,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON1_Bits',0,4,250,8,16,4,11
	.byte	'reserved_0',0,1
	.word	669
	.byte	2,6,2,35,0,11
	.byte	'IR0',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	669
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	669
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	669
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	669
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	669
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	669
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	686
	.byte	16,0,2,35,2,0,12,4,255,14,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	708
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_WDTCPU_SR_Bits',0,4,137,9,16,4,11
	.byte	'AE',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	669
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	669
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	669
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	669
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	669
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	669
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	686
	.byte	16,0,2,35,2,0,12,4,135,15,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	952
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_WDTCPU',0,4,175,15,25,12,13
	.byte	'CON0',0
	.word	629
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	912
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	1143
	.byte	4,2,35,8,0,14
	.word	1183
	.byte	3
	.word	1246
	.byte	4
	.byte	'IfxScuWdt_clearCpuEndinitInline',0,3,3,181,3,17,1,1,5
	.byte	'watchdog',0,3,181,3,65
	.word	1251
	.byte	5
	.byte	'password',0,3,181,3,82
	.word	686
	.byte	6,0,4
	.byte	'IfxScuWdt_setCpuEndinitInline',0,3,3,140,4,17,1,1,5
	.byte	'watchdog',0,3,140,4,63
	.word	1251
	.byte	5
	.byte	'password',0,3,140,4,80
	.word	686
	.byte	6,0,8
	.byte	'IfxScuWdt_getCpuWatchdogPasswordInline',0,3,3,227,3,19
	.word	686
	.byte	1,1,5
	.byte	'watchdog',0,3,227,3,74
	.word	1251
	.byte	6,0,15,6,156,1,9,1,16
	.byte	'IfxCpu_ResourceCpu_0',0,0,16
	.byte	'IfxCpu_ResourceCpu_1',0,1,16
	.byte	'IfxCpu_ResourceCpu_none',0,2,0,8
	.byte	'IfxCpu_getCoreIndex',0,3,5,141,6,31
	.word	1481
	.byte	1,1,6,0,8
	.byte	'IfxCpu_areInterruptsEnabled',0,3,5,139,5,20
	.word	669
	.byte	1,1,6,0
.L150:
	.byte	7
	.byte	'unsigned long int',0,4,7,8
	.byte	'IfxCpu_getPerformanceCounter',0,3,5,161,6,19
	.word	1636
	.byte	1,1,5
	.byte	'address',0,5,161,6,55
	.word	686
	.byte	6,0,8
	.byte	'IfxCpu_getPerformanceCounterStickyOverflow',0,3,5,190,6,20
	.word	669
	.byte	1,1,5
	.byte	'address',0,5,190,6,70
	.word	686
	.byte	6,0,4
	.byte	'IfxCpu_updatePerformanceCounter',0,3,5,172,8,17,1,1,5
	.byte	'address',0,5,172,8,56
	.word	1636
	.byte	5
	.byte	'count',0,5,172,8,72
	.word	1636
	.byte	17,6,0,0,10
	.byte	'_Ifx_P_OUT_Bits',0,8,143,3,16,4,11
	.byte	'P0',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	669
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	669
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	669
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	669
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	669
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	669
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	669
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	669
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	669
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	669
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	669
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	669
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	686
	.byte	16,0,2,35,2,0,12,8,181,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1867
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMR_Bits',0,8,169,2,16,4,11
	.byte	'PS0',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	669
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	669
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	669
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	669
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	669
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	669
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	669
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	669
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	669
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	669
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	669
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	669
	.byte	1,0,2,35,1,11
	.byte	'PCL0',0,1
	.word	669
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	669
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	669
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	669
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	669
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	669
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	669
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	669
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	669
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	669
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	669
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	669
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	669
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	669
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	669
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	669
	.byte	1,0,2,35,3,0,12,8,133,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2183
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ID_Bits',0,8,110,16,4,11
	.byte	'MODREV',0,1
	.word	669
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	669
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	686
	.byte	16,0,2,35,2,0,12,8,148,4,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2754
	.byte	4,2,35,0,0,18,4
	.word	669
	.byte	19,3,0,10
	.byte	'_Ifx_P_IOCR0_Bits',0,8,140,1,16,4,11
	.byte	'reserved_0',0,1
	.word	669
	.byte	3,5,2,35,0,11
	.byte	'PC0',0,1
	.word	669
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	669
	.byte	3,5,2,35,1,11
	.byte	'PC1',0,1
	.word	669
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	669
	.byte	3,5,2,35,2,11
	.byte	'PC2',0,1
	.word	669
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	669
	.byte	3,5,2,35,3,11
	.byte	'PC3',0,1
	.word	669
	.byte	5,0,2,35,3,0,12,8,164,4,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2882
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR4_Bits',0,8,166,1,16,4,11
	.byte	'reserved_0',0,1
	.word	669
	.byte	3,5,2,35,0,11
	.byte	'PC4',0,1
	.word	669
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	669
	.byte	3,5,2,35,1,11
	.byte	'PC5',0,1
	.word	669
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	669
	.byte	3,5,2,35,2,11
	.byte	'PC6',0,1
	.word	669
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	669
	.byte	3,5,2,35,3,11
	.byte	'PC7',0,1
	.word	669
	.byte	5,0,2,35,3,0,12,8,180,4,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3097
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR8_Bits',0,8,179,1,16,4,11
	.byte	'reserved_0',0,1
	.word	669
	.byte	3,5,2,35,0,11
	.byte	'PC8',0,1
	.word	669
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	669
	.byte	3,5,2,35,1,11
	.byte	'PC9',0,1
	.word	669
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	669
	.byte	3,5,2,35,2,11
	.byte	'PC10',0,1
	.word	669
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	669
	.byte	3,5,2,35,3,11
	.byte	'PC11',0,1
	.word	669
	.byte	5,0,2,35,3,0,12,8,188,4,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3312
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR12_Bits',0,8,153,1,16,4,11
	.byte	'reserved_0',0,1
	.word	669
	.byte	3,5,2,35,0,11
	.byte	'PC12',0,1
	.word	669
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	669
	.byte	3,5,2,35,1,11
	.byte	'PC13',0,1
	.word	669
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	669
	.byte	3,5,2,35,2,11
	.byte	'PC14',0,1
	.word	669
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	669
	.byte	3,5,2,35,3,11
	.byte	'PC15',0,1
	.word	669
	.byte	5,0,2,35,3,0,12,8,172,4,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3529
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IN_Bits',0,8,118,16,4,11
	.byte	'P0',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	669
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	669
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	669
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	669
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	669
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	669
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	669
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	669
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	669
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	669
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	669
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	669
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	686
	.byte	16,0,2,35,2,0,12,8,156,4,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3749
	.byte	4,2,35,0,0,18,24
	.word	669
	.byte	19,23,0,10
	.byte	'_Ifx_P_PDR0_Bits',0,8,205,3,16,4,11
	.byte	'PD0',0,1
	.word	669
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	669
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	669
	.byte	1,0,2,35,0,11
	.byte	'PD2',0,1
	.word	669
	.byte	3,5,2,35,1,11
	.byte	'PL2',0,1
	.word	669
	.byte	1,4,2,35,1,11
	.byte	'PD3',0,1
	.word	669
	.byte	3,1,2,35,1,11
	.byte	'PL3',0,1
	.word	669
	.byte	1,0,2,35,1,11
	.byte	'PD4',0,1
	.word	669
	.byte	3,5,2,35,2,11
	.byte	'PL4',0,1
	.word	669
	.byte	1,4,2,35,2,11
	.byte	'PD5',0,1
	.word	669
	.byte	3,1,2,35,2,11
	.byte	'PL5',0,1
	.word	669
	.byte	1,0,2,35,2,11
	.byte	'PD6',0,1
	.word	669
	.byte	3,5,2,35,3,11
	.byte	'PL6',0,1
	.word	669
	.byte	1,4,2,35,3,11
	.byte	'PD7',0,1
	.word	669
	.byte	3,1,2,35,3,11
	.byte	'PL7',0,1
	.word	669
	.byte	1,0,2,35,3,0,12,8,205,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4072
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PDR1_Bits',0,8,226,3,16,4,11
	.byte	'PD8',0,1
	.word	669
	.byte	3,5,2,35,0,11
	.byte	'PL8',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'PD9',0,1
	.word	669
	.byte	3,1,2,35,0,11
	.byte	'PL9',0,1
	.word	669
	.byte	1,0,2,35,0,11
	.byte	'PD10',0,1
	.word	669
	.byte	3,5,2,35,1,11
	.byte	'PL10',0,1
	.word	669
	.byte	1,4,2,35,1,11
	.byte	'PD11',0,1
	.word	669
	.byte	3,1,2,35,1,11
	.byte	'PL11',0,1
	.word	669
	.byte	1,0,2,35,1,11
	.byte	'PD12',0,1
	.word	669
	.byte	3,5,2,35,2,11
	.byte	'PL12',0,1
	.word	669
	.byte	1,4,2,35,2,11
	.byte	'PD13',0,1
	.word	669
	.byte	3,1,2,35,2,11
	.byte	'PL13',0,1
	.word	669
	.byte	1,0,2,35,2,11
	.byte	'PD14',0,1
	.word	669
	.byte	3,5,2,35,3,11
	.byte	'PL14',0,1
	.word	669
	.byte	1,4,2,35,3,11
	.byte	'PD15',0,1
	.word	669
	.byte	3,1,2,35,3,11
	.byte	'PL15',0,1
	.word	669
	.byte	1,0,2,35,3,0,12,8,213,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4376
	.byte	4,2,35,0,0,18,8
	.word	669
	.byte	19,7,0,10
	.byte	'_Ifx_P_ESR_Bits',0,8,88,16,4,11
	.byte	'EN0',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	669
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	669
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	669
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	669
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	669
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	669
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	669
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	669
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	669
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	669
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	669
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	669
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	686
	.byte	16,0,2,35,2,0,12,8,140,4,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4701
	.byte	4,2,35,0,0,18,12
	.word	669
	.byte	19,11,0,10
	.byte	'_Ifx_P_PDISC_Bits',0,8,183,3,16,4,11
	.byte	'PDIS0',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'PDIS2',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'PDIS3',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'PDIS4',0,1
	.word	669
	.byte	1,3,2,35,0,11
	.byte	'PDIS5',0,1
	.word	669
	.byte	1,2,2,35,0,11
	.byte	'PDIS6',0,1
	.word	669
	.byte	1,1,2,35,0,11
	.byte	'PDIS7',0,1
	.word	669
	.byte	1,0,2,35,0,11
	.byte	'PDIS8',0,1
	.word	669
	.byte	1,7,2,35,1,11
	.byte	'PDIS9',0,1
	.word	669
	.byte	1,6,2,35,1,11
	.byte	'PDIS10',0,1
	.word	669
	.byte	1,5,2,35,1,11
	.byte	'PDIS11',0,1
	.word	669
	.byte	1,4,2,35,1,11
	.byte	'PDIS12',0,1
	.word	669
	.byte	1,3,2,35,1,11
	.byte	'PDIS13',0,1
	.word	669
	.byte	1,2,2,35,1,11
	.byte	'PDIS14',0,1
	.word	669
	.byte	1,1,2,35,1,11
	.byte	'PDIS15',0,1
	.word	669
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	686
	.byte	16,0,2,35,2,0,12,8,197,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5041
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PCSR_Bits',0,8,165,3,16,4,11
	.byte	'SEL0',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'SEL1',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'SEL2',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'SEL3',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'SEL4',0,1
	.word	669
	.byte	1,3,2,35,0,11
	.byte	'SEL5',0,1
	.word	669
	.byte	1,2,2,35,0,11
	.byte	'SEL6',0,1
	.word	669
	.byte	1,1,2,35,0,11
	.byte	'SEL7',0,1
	.word	669
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	669
	.byte	2,6,2,35,1,11
	.byte	'SEL10',0,1
	.word	669
	.byte	1,5,2,35,1,11
	.byte	'SEL11',0,1
	.word	669
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	494
	.byte	19,1,2,35,0,11
	.byte	'LCK',0,1
	.word	669
	.byte	1,0,2,35,3,0,12,8,189,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5407
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR0_Bits',0,8,206,2,16,4,11
	.byte	'PS0',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	494
	.byte	28,0,2,35,0,0,12,8,149,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5693
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR4_Bits',0,8,227,2,16,4,11
	.byte	'reserved_0',0,1
	.word	669
	.byte	4,4,2,35,0,11
	.byte	'PS4',0,1
	.word	669
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	669
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	669
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	669
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	494
	.byte	24,0,2,35,0,0,12,8,165,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5840
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR8_Bits',0,8,238,2,16,4,11
	.byte	'reserved_0',0,1
	.word	669
	.byte	8,0,2,35,0,11
	.byte	'PS8',0,1
	.word	669
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	669
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	669
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	669
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	494
	.byte	20,0,2,35,0,0,12,8,173,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6009
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR12_Bits',0,8,216,2,16,4,11
	.byte	'reserved_0',0,2
	.word	686
	.byte	12,4,2,35,0,11
	.byte	'PS12',0,1
	.word	669
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	669
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	669
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	669
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	686
	.byte	16,0,2,35,2,0,12,8,157,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6181
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR0_Bits',0,8,232,1,16,4,11
	.byte	'reserved_0',0,2
	.word	686
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	669
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	669
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	669
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	669
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	686
	.byte	12,0,2,35,2,0,12,8,229,4,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6356
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR4_Bits',0,8,253,1,16,4,11
	.byte	'reserved_0',0,4
	.word	494
	.byte	20,12,2,35,0,11
	.byte	'PCL4',0,1
	.word	669
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	669
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	669
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	669
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	669
	.byte	8,0,2,35,3,0,12,8,245,4,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6530
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR8_Bits',0,8,136,2,16,4,11
	.byte	'reserved_0',0,4
	.word	494
	.byte	24,8,2,35,0,11
	.byte	'PCL8',0,1
	.word	669
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	669
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	669
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	669
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	669
	.byte	4,0,2,35,3,0,12,8,253,4,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6704
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR12_Bits',0,8,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	494
	.byte	28,4,2,35,0,11
	.byte	'PCL12',0,1
	.word	669
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	669
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	669
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	669
	.byte	1,0,2,35,3,0,12,8,237,4,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6880
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR_Bits',0,8,249,2,16,4,11
	.byte	'PS0',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	669
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	669
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	669
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	669
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	669
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	669
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	669
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	669
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	669
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	669
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	669
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	669
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	686
	.byte	16,0,2,35,2,0,12,8,141,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7036
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR_Bits',0,8,147,2,16,4,11
	.byte	'reserved_0',0,2
	.word	686
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	669
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	669
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	669
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	669
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	669
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	669
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	669
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	669
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	669
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	669
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	669
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	669
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	669
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	669
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	669
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	669
	.byte	1,0,2,35,3,0,12,8,221,4,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7369
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR0_Bits',0,8,192,1,16,4,11
	.byte	'reserved_0',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	494
	.byte	30,0,2,35,0,0,12,8,196,4,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7717
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_Bits',0,8,200,1,16,4,11
	.byte	'reserved_0',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	494
	.byte	30,0,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_P21_Bits',0,8,208,1,16,4,11
	.byte	'RDIS_CTRL',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'RX_DIS',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'TERM',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'LRXTERM',0,1
	.word	669
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	494
	.byte	24,0,2,35,0,0,12,8,204,4,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7841
	.byte	4,2,35,0,13
	.byte	'B_P21',0
	.word	7925
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR2_Bits',0,8,218,1,16,4,11
	.byte	'reserved_0',0,1
	.word	669
	.byte	8,0,2,35,0,11
	.byte	'LVDSR',0,1
	.word	669
	.byte	1,7,2,35,1,11
	.byte	'LVDSRL',0,1
	.word	669
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	669
	.byte	2,4,2,35,1,11
	.byte	'TDIS_CTRL',0,1
	.word	669
	.byte	1,3,2,35,1,11
	.byte	'TX_DIS',0,1
	.word	669
	.byte	1,2,2,35,1,11
	.byte	'TX_PD',0,1
	.word	669
	.byte	1,1,2,35,1,11
	.byte	'TX_PWDPD',0,1
	.word	669
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	686
	.byte	16,0,2,35,2,0,12,8,213,4,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8105
	.byte	4,2,35,0,0,18,76
	.word	669
	.byte	19,75,0,10
	.byte	'_Ifx_P_ACCEN1_Bits',0,8,82,16,4,11
	.byte	'reserved_0',0,4
	.word	494
	.byte	32,0,2,35,0,0,12,8,132,4,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8358
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ACCEN0_Bits',0,8,45,16,4,11
	.byte	'EN0',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	669
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	669
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	669
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	669
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	669
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	669
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	669
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	669
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	669
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	669
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	669
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	669
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	669
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	669
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	669
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	669
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	669
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	669
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	669
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	669
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	669
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	669
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	669
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	669
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	669
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	669
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	669
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	669
	.byte	1,0,2,35,3,0,12,8,252,3,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8445
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P',0,8,229,5,25,128,2,13
	.byte	'OUT',0
	.word	2143
	.byte	4,2,35,0,13
	.byte	'OMR',0
	.word	2714
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	2833
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	2873
	.byte	4,2,35,12,13
	.byte	'IOCR0',0
	.word	3057
	.byte	4,2,35,16,13
	.byte	'IOCR4',0
	.word	3272
	.byte	4,2,35,20,13
	.byte	'IOCR8',0
	.word	3489
	.byte	4,2,35,24,13
	.byte	'IOCR12',0
	.word	3709
	.byte	4,2,35,28,13
	.byte	'reserved_20',0
	.word	2873
	.byte	4,2,35,32,13
	.byte	'IN',0
	.word	4023
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	4063
	.byte	24,2,35,40,13
	.byte	'PDR0',0
	.word	4336
	.byte	4,2,35,64,13
	.byte	'PDR1',0
	.word	4652
	.byte	4,2,35,68,13
	.byte	'reserved_48',0
	.word	4692
	.byte	8,2,35,72,13
	.byte	'ESR',0
	.word	4992
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	5032
	.byte	12,2,35,84,13
	.byte	'PDISC',0
	.word	5367
	.byte	4,2,35,96,13
	.byte	'PCSR',0
	.word	5653
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	4692
	.byte	8,2,35,104,13
	.byte	'OMSR0',0
	.word	5800
	.byte	4,2,35,112,13
	.byte	'OMSR4',0
	.word	5969
	.byte	4,2,35,116,13
	.byte	'OMSR8',0
	.word	6141
	.byte	4,2,35,120,13
	.byte	'OMSR12',0
	.word	6316
	.byte	4,2,35,124,13
	.byte	'OMCR0',0
	.word	6490
	.byte	4,3,35,128,1,13
	.byte	'OMCR4',0
	.word	6664
	.byte	4,3,35,132,1,13
	.byte	'OMCR8',0
	.word	6840
	.byte	4,3,35,136,1,13
	.byte	'OMCR12',0
	.word	6996
	.byte	4,3,35,140,1,13
	.byte	'OMSR',0
	.word	7329
	.byte	4,3,35,144,1,13
	.byte	'OMCR',0
	.word	7677
	.byte	4,3,35,148,1,13
	.byte	'reserved_98',0
	.word	4692
	.byte	8,3,35,152,1,13
	.byte	'LPCR0',0
	.word	7801
	.byte	4,3,35,160,1,13
	.byte	'LPCR1',0
	.word	8050
	.byte	4,3,35,164,1,13
	.byte	'LPCR2',0
	.word	8309
	.byte	4,3,35,168,1,13
	.byte	'reserved_A4',0
	.word	8349
	.byte	76,3,35,172,1,13
	.byte	'ACCEN1',0
	.word	8405
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	8972
	.byte	4,3,35,252,1,0,14
	.word	9012
	.byte	3
	.word	9615
	.byte	15,7,172,1,9,4,16
	.byte	'IfxPort_State_notChanged',0,0,16
	.byte	'IfxPort_State_high',0,1,16
	.byte	'IfxPort_State_low',0,128,128,4,16
	.byte	'IfxPort_State_toggled',0,129,128,4,0,4
	.byte	'IfxPort_setPinState',0,3,7,208,4,17,1,1,5
	.byte	'port',0,7,208,4,44
	.word	9620
	.byte	5
	.byte	'pinIndex',0,7,208,4,56
	.word	669
	.byte	5
	.byte	'action',0,7,208,4,80
	.word	9625
	.byte	6,0,8
	.byte	'IfxScuCcu_getStmFrequency',0,3,9,226,8,20
	.word	290
	.byte	1,1,6,0,10
	.byte	'_Ifx_SRC_SRCR_Bits',0,11,45,16,4,11
	.byte	'SRPN',0,1
	.word	669
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	669
	.byte	2,6,2,35,1,11
	.byte	'SRE',0,1
	.word	669
	.byte	1,5,2,35,1,11
	.byte	'TOS',0,1
	.word	669
	.byte	2,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	669
	.byte	3,0,2,35,1,11
	.byte	'ECC',0,1
	.word	669
	.byte	6,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	669
	.byte	2,0,2,35,2,11
	.byte	'SRR',0,1
	.word	669
	.byte	1,7,2,35,3,11
	.byte	'CLRR',0,1
	.word	669
	.byte	1,6,2,35,3,11
	.byte	'SETR',0,1
	.word	669
	.byte	1,5,2,35,3,11
	.byte	'IOV',0,1
	.word	669
	.byte	1,4,2,35,3,11
	.byte	'IOVCLR',0,1
	.word	669
	.byte	1,3,2,35,3,11
	.byte	'SWS',0,1
	.word	669
	.byte	1,2,2,35,3,11
	.byte	'SWSCLR',0,1
	.word	669
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	669
	.byte	1,0,2,35,3,0,12,11,70,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9846
	.byte	4,2,35,0,0,14
	.word	10136
.L163:
	.byte	3
	.word	10175
.L192:
	.byte	4
	.byte	'IfxSrc_clearRequest',0,3,10,250,1,17,1,1
.L194:
	.byte	5
	.byte	'src',0,10,250,1,60
	.word	10180
.L196:
	.byte	6,0
.L197:
	.byte	4
	.byte	'IfxSrc_enable',0,3,10,140,2,17,1,1
.L198:
	.byte	5
	.byte	'src',0,10,140,2,54
	.word	10180
.L200:
	.byte	6,0,15,12,69,9,1,16
	.byte	'IfxSrc_Tos_cpu0',0,0,16
	.byte	'IfxSrc_Tos_cpu1',0,1,16
	.byte	'IfxSrc_Tos_dma',0,3,0
.L182:
	.byte	4
	.byte	'IfxSrc_init',0,3,10,146,2,17,1,1
.L185:
	.byte	5
	.byte	'src',0,10,146,2,52
	.word	10180
.L187:
	.byte	5
	.byte	'typOfService',0,10,146,2,68
	.word	10265
.L189:
	.byte	5
	.byte	'priority',0,10,146,2,95
	.word	686
.L191:
	.byte	17,6,0,0,10
	.byte	'_Ifx_STM_CLC_Bits',0,14,100,16,4,11
	.byte	'DISR',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'DISS',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'EDIS',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	494
	.byte	28,0,2,35,0,0,12,14,149,2,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10401
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_ID_Bits',0,14,142,1,16,4,11
	.byte	'MODREV',0,1
	.word	669
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	669
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	686
	.byte	16,0,2,35,2,0,12,14,181,2,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10557
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM0_Bits',0,14,192,1,16,4,11
	.byte	'STM31_0',0,4
	.word	494
	.byte	32,0,2,35,0,0,12,14,229,2,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10679
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM1_Bits',0,14,204,1,16,4,11
	.byte	'STM35_4',0,4
	.word	494
	.byte	32,0,2,35,0,0,12,14,245,2,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10764
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM2_Bits',0,14,210,1,16,4,11
	.byte	'STM39_8',0,4
	.word	494
	.byte	32,0,2,35,0,0,12,14,253,2,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10849
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM3_Bits',0,14,216,1,16,4,11
	.byte	'STM43_12',0,4
	.word	494
	.byte	32,0,2,35,0,0,12,14,133,3,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10934
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM4_Bits',0,14,222,1,16,4,11
	.byte	'STM47_16',0,4
	.word	494
	.byte	32,0,2,35,0,0,12,14,141,3,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11020
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM5_Bits',0,14,228,1,16,4,11
	.byte	'STM51_20',0,4
	.word	494
	.byte	32,0,2,35,0,0,12,14,149,3,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11106
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM6_Bits',0,14,234,1,16,4,11
	.byte	'STM63_32',0,4
	.word	494
	.byte	32,0,2,35,0,0,12,14,157,3,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11192
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_CAP_Bits',0,14,88,16,4,11
	.byte	'STMCAP63_32',0,4
	.word	494
	.byte	32,0,2,35,0,0,12,14,133,2,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11278
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_CMP_Bits',0,14,123,16,4,11
	.byte	'CMPVAL',0,4
	.word	494
	.byte	32,0,2,35,0,0,12,14,165,2,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11365
	.byte	4,2,35,0,0,18,8
	.word	11407
	.byte	19,1,0,10
	.byte	'_Ifx_STM_CMCON_Bits',0,14,110,16,4,11
	.byte	'MSIZE0',0,1
	.word	669
	.byte	5,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	669
	.byte	3,0,2,35,0,11
	.byte	'MSTART0',0,1
	.word	669
	.byte	5,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	669
	.byte	3,0,2,35,1,11
	.byte	'MSIZE1',0,1
	.word	669
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	669
	.byte	3,0,2,35,2,11
	.byte	'MSTART1',0,1
	.word	669
	.byte	5,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	669
	.byte	3,0,2,35,3,0
.L176:
	.byte	12,14,157,2,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11456
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_ICR_Bits',0,14,129,1,16,4,11
	.byte	'CMP0EN',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'CMP0IR',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'CMP0OS',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'CMP1EN',0,1
	.word	669
	.byte	1,3,2,35,0,11
	.byte	'CMP1IR',0,1
	.word	669
	.byte	1,2,2,35,0,11
	.byte	'CMP1OS',0,1
	.word	669
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,4
	.word	494
	.byte	25,0,2,35,0,0
.L178:
	.byte	12,14,173,2,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11687
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_ISCR_Bits',0,14,150,1,16,4,11
	.byte	'CMP0IRR',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'CMP0IRS',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'CMP1IRR',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'CMP1IRS',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	494
	.byte	28,0,2,35,0,0,12,14,189,2,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11904
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM0SV_Bits',0,14,198,1,16,4,11
	.byte	'STM31_0',0,4
	.word	494
	.byte	32,0,2,35,0,0,12,14,237,2,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12068
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_CAPSV_Bits',0,14,94,16,4,11
	.byte	'STMCAP63_32',0,4
	.word	494
	.byte	32,0,2,35,0,0,12,14,141,2,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12155
	.byte	4,2,35,0,0,18,144,1
	.word	669
	.byte	19,143,1,0,10
	.byte	'_Ifx_STM_OCS_Bits',0,14,182,1,16,4,11
	.byte	'reserved_0',0,4
	.word	494
	.byte	24,8,2,35,0,11
	.byte	'SUS',0,1
	.word	669
	.byte	4,4,2,35,3,11
	.byte	'SUS_P',0,1
	.word	669
	.byte	1,3,2,35,3,11
	.byte	'SUSSTA',0,1
	.word	669
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	669
	.byte	2,0,2,35,3,0
.L142:
	.byte	12,14,221,2,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12255
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_KRSTCLR_Bits',0,14,175,1,16,4,11
	.byte	'CLR',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	494
	.byte	31,0,2,35,0,0,12,14,213,2,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12415
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_KRST1_Bits',0,14,168,1,16,4,11
	.byte	'RST',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	494
	.byte	31,0,2,35,0,0,12,14,205,2,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12521
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_KRST0_Bits',0,14,160,1,16,4,11
	.byte	'RST',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'RSTSTAT',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	494
	.byte	30,0,2,35,0,0,12,14,197,2,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12625
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_ACCEN1_Bits',0,14,82,16,4,11
	.byte	'reserved_0',0,4
	.word	494
	.byte	32,0,2,35,0,0,12,14,253,1,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12748
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_ACCEN0_Bits',0,14,45,16,4,11
	.byte	'EN0',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	669
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	669
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	669
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	669
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	669
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	669
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	669
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	669
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	669
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	669
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	669
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	669
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	669
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	669
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	669
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	669
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	669
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	669
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	669
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	669
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	669
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	669
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	669
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	669
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	669
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	669
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	669
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	669
	.byte	1,0,2,35,3,0,12,14,245,1,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12837
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM',0,14,173,3,25,128,2,13
	.byte	'CLC',0
	.word	10517
	.byte	4,2,35,0,13
	.byte	'reserved_4',0
	.word	2873
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	10639
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	2873
	.byte	4,2,35,12,13
	.byte	'TIM0',0
	.word	10724
	.byte	4,2,35,16,13
	.byte	'TIM1',0
	.word	10809
	.byte	4,2,35,20,13
	.byte	'TIM2',0
	.word	10894
	.byte	4,2,35,24,13
	.byte	'TIM3',0
	.word	10980
	.byte	4,2,35,28,13
	.byte	'TIM4',0
	.word	11066
	.byte	4,2,35,32,13
	.byte	'TIM5',0
	.word	11152
	.byte	4,2,35,36,13
	.byte	'TIM6',0
	.word	11238
	.byte	4,2,35,40,13
	.byte	'CAP',0
	.word	11325
	.byte	4,2,35,44,13
	.byte	'CMP',0
	.word	11447
	.byte	8,2,35,48,13
	.byte	'CMCON',0
	.word	11647
	.byte	4,2,35,56,13
	.byte	'ICR',0
	.word	11864
	.byte	4,2,35,60,13
	.byte	'ISCR',0
	.word	12028
	.byte	4,2,35,64,13
	.byte	'reserved_44',0
	.word	5032
	.byte	12,2,35,68,13
	.byte	'TIM0SV',0
	.word	12115
	.byte	4,2,35,80,13
	.byte	'CAPSV',0
	.word	12204
	.byte	4,2,35,84,13
	.byte	'reserved_58',0
	.word	12244
	.byte	144,1,2,35,88,13
	.byte	'OCS',0
	.word	12375
	.byte	4,3,35,232,1,13
	.byte	'KRSTCLR',0
	.word	12481
	.byte	4,3,35,236,1,13
	.byte	'KRST1',0
	.word	12585
	.byte	4,3,35,240,1,13
	.byte	'KRST0',0
	.word	12708
	.byte	4,3,35,244,1,13
	.byte	'ACCEN1',0
	.word	12797
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	13366
	.byte	4,3,35,252,1,0,14
	.word	13406
.L136:
	.byte	3
	.word	13826
.L210:
	.byte	8
	.byte	'IfxStm_get',0,3,13,162,4,19
	.word	376
	.byte	1,1
.L211:
	.byte	5
	.byte	'stm',0,13,162,4,39
	.word	13831
.L213:
	.byte	6,0,8
	.byte	'IfxStm_getFrequency',0,3,13,179,4,20
	.word	290
	.byte	1,1,5
	.byte	'stm',0,13,179,4,49
	.word	13831
	.byte	17,6,6,0,0,8
	.byte	'IfxStm_getLower',0,3,13,190,4,19
	.word	1636
	.byte	1,1,5
	.byte	'stm',0,13,190,4,44
	.word	13831
	.byte	6,0
.L201:
	.byte	8
	.byte	'IfxStm_getOffsetTimer',0,3,13,232,4,19
	.word	1636
	.byte	1,1
.L203:
	.byte	5
	.byte	'stm',0,13,232,4,50
	.word	13831
.L205:
	.byte	5
	.byte	'offset',0,13,232,4,61
	.word	669
.L207:
	.byte	17,6,0,0,20
	.word	238
	.byte	21
	.word	264
	.byte	6,0,20
	.word	299
	.byte	21
	.word	331
	.byte	6,0,20
	.word	344
	.byte	6,0,20
	.word	413
	.byte	21
	.word	432
	.byte	6,0,20
	.word	448
	.byte	21
	.word	463
	.byte	21
	.word	477
	.byte	6,0,20
	.word	1256
	.byte	21
	.word	1296
	.byte	21
	.word	1314
	.byte	6,0,20
	.word	1334
	.byte	21
	.word	1372
	.byte	21
	.word	1390
	.byte	6,0,22
	.byte	'IfxScuWdt_clearCpuEndinit',0,3,217,1,17,1,1,1,1,5
	.byte	'password',0,3,217,1,50
	.word	686
	.byte	0,22
	.byte	'IfxScuWdt_setCpuEndinit',0,3,239,1,17,1,1,1,1,5
	.byte	'password',0,3,239,1,48
	.word	686
	.byte	0,20
	.word	1410
	.byte	21
	.word	1461
	.byte	6,0,23
	.byte	'IfxScuWdt_getCpuWatchdogPassword',0,3,129,3,19
	.word	686
	.byte	1,1,1,1,20
	.word	1560
	.byte	6,0,20
	.word	1594
	.byte	6,0,20
	.word	1657
	.byte	21
	.word	1698
	.byte	6,0,20
	.word	1717
	.byte	21
	.word	1772
	.byte	6,0,20
	.word	1791
	.byte	21
	.word	1831
	.byte	21
	.word	1848
	.byte	17,6,0,0,20
	.word	9728
	.byte	21
	.word	9756
	.byte	21
	.word	9770
	.byte	21
	.word	9788
	.byte	6,0,20
	.word	9806
	.byte	6,0,20
	.word	10185
	.byte	21
	.word	10213
	.byte	6,0,20
	.word	10228
	.byte	21
	.word	10250
	.byte	6,0,20
	.word	10324
	.byte	21
	.word	10344
	.byte	21
	.word	10357
	.byte	21
	.word	10379
	.byte	17,24
	.word	10185
	.byte	21
	.word	10213
	.byte	25
	.word	10226
	.byte	0,6,0,0,20
	.word	13836
	.byte	21
	.word	13859
	.byte	6,0,20
	.word	13874
	.byte	21
	.word	13906
	.byte	17,17,24
	.word	9806
	.byte	25
	.word	9844
	.byte	0,0,6,0,0
.L145:
	.byte	15,15,87,9,1,16
	.byte	'IfxStm_Index_none',0,127,16
	.byte	'IfxStm_Index_0',0,0,16
	.byte	'IfxStm_Index_1',0,1,0,20
	.word	13924
	.byte	21
	.word	13952
	.byte	6,0,20
	.word	13967
	.byte	21
	.word	14001
	.byte	21
	.word	14014
	.byte	17,24
	.word	13836
	.byte	21
	.word	13859
	.byte	25
	.word	13872
	.byte	0,6,0,0
.L155:
	.byte	15,13,151,1,9,1,16
	.byte	'IfxStm_Comparator_0',0,0,16
	.byte	'IfxStm_Comparator_1',0,1,0
.L231:
	.byte	15,13,159,1,9,1,16
	.byte	'IfxStm_ComparatorInterrupt_ir0',0,0,16
	.byte	'IfxStm_ComparatorInterrupt_ir1',0,1,0
.L227:
	.byte	15,13,167,1,9,1,16
	.byte	'IfxStm_ComparatorOffset_0',0,0,16
	.byte	'IfxStm_ComparatorOffset_1',0,1,16
	.byte	'IfxStm_ComparatorOffset_2',0,2,16
	.byte	'IfxStm_ComparatorOffset_3',0,3,16
	.byte	'IfxStm_ComparatorOffset_4',0,4,16
	.byte	'IfxStm_ComparatorOffset_5',0,5,16
	.byte	'IfxStm_ComparatorOffset_6',0,6,16
	.byte	'IfxStm_ComparatorOffset_7',0,7,16
	.byte	'IfxStm_ComparatorOffset_8',0,8,16
	.byte	'IfxStm_ComparatorOffset_9',0,9,16
	.byte	'IfxStm_ComparatorOffset_10',0,10,16
	.byte	'IfxStm_ComparatorOffset_11',0,11,16
	.byte	'IfxStm_ComparatorOffset_12',0,12,16
	.byte	'IfxStm_ComparatorOffset_13',0,13,16
	.byte	'IfxStm_ComparatorOffset_14',0,14,16
	.byte	'IfxStm_ComparatorOffset_15',0,15,16
	.byte	'IfxStm_ComparatorOffset_16',0,16,16
	.byte	'IfxStm_ComparatorOffset_17',0,17,16
	.byte	'IfxStm_ComparatorOffset_18',0,18,16
	.byte	'IfxStm_ComparatorOffset_19',0,19,16
	.byte	'IfxStm_ComparatorOffset_20',0,20,16
	.byte	'IfxStm_ComparatorOffset_21',0,21,16
	.byte	'IfxStm_ComparatorOffset_22',0,22,16
	.byte	'IfxStm_ComparatorOffset_23',0,23,16
	.byte	'IfxStm_ComparatorOffset_24',0,24,16
	.byte	'IfxStm_ComparatorOffset_25',0,25,16
	.byte	'IfxStm_ComparatorOffset_26',0,26,16
	.byte	'IfxStm_ComparatorOffset_27',0,27,16
	.byte	'IfxStm_ComparatorOffset_28',0,28,16
	.byte	'IfxStm_ComparatorOffset_29',0,29,16
	.byte	'IfxStm_ComparatorOffset_30',0,30,16
	.byte	'IfxStm_ComparatorOffset_31',0,31,0
.L229:
	.byte	15,13,205,1,9,1,16
	.byte	'IfxStm_ComparatorSize_1Bit',0,0,16
	.byte	'IfxStm_ComparatorSize_2Bits',0,1,16
	.byte	'IfxStm_ComparatorSize_3Bits',0,2,16
	.byte	'IfxStm_ComparatorSize_4Bits',0,3,16
	.byte	'IfxStm_ComparatorSize_5Bits',0,4,16
	.byte	'IfxStm_ComparatorSize_6Bits',0,5,16
	.byte	'IfxStm_ComparatorSize_7Bits',0,6,16
	.byte	'IfxStm_ComparatorSize_8Bits',0,7,16
	.byte	'IfxStm_ComparatorSize_9Bits',0,8,16
	.byte	'IfxStm_ComparatorSize_10Bits',0,9,16
	.byte	'IfxStm_ComparatorSize_11Bits',0,10,16
	.byte	'IfxStm_ComparatorSize_12Bits',0,11,16
	.byte	'IfxStm_ComparatorSize_13Bits',0,12,16
	.byte	'IfxStm_ComparatorSize_14Bits',0,13,16
	.byte	'IfxStm_ComparatorSize_15Bits',0,14,16
	.byte	'IfxStm_ComparatorSize_16Bits',0,15,16
	.byte	'IfxStm_ComparatorSize_17Bits',0,16,16
	.byte	'IfxStm_ComparatorSize_18Bits',0,17,16
	.byte	'IfxStm_ComparatorSize_19Bits',0,18,16
	.byte	'IfxStm_ComparatorSize_20Bits',0,19,16
	.byte	'IfxStm_ComparatorSize_21Bits',0,20,16
	.byte	'IfxStm_ComparatorSize_22Bits',0,21,16
	.byte	'IfxStm_ComparatorSize_23Bits',0,22,16
	.byte	'IfxStm_ComparatorSize_24Bits',0,23,16
	.byte	'IfxStm_ComparatorSize_25Bits',0,24,16
	.byte	'IfxStm_ComparatorSize_26Bits',0,25,16
	.byte	'IfxStm_ComparatorSize_27Bits',0,26,16
	.byte	'IfxStm_ComparatorSize_28Bits',0,27,16
	.byte	'IfxStm_ComparatorSize_29Bits',0,28,16
	.byte	'IfxStm_ComparatorSize_30Bits',0,29,16
	.byte	'IfxStm_ComparatorSize_31Bits',0,30,16
	.byte	'IfxStm_ComparatorSize_32Bits',0,31,0,26,13,141,2,9,12,13
	.byte	'comparator',0
	.word	14588
	.byte	1,2,35,0,13
	.byte	'comparatorInterrupt',0
	.word	14639
	.byte	1,2,35,1,13
	.byte	'compareOffset',0
	.word	14712
	.byte	1,2,35,2,13
	.byte	'compareSize',0
	.word	15637
	.byte	1,2,35,3,13
	.byte	'ticks',0
	.word	1636
	.byte	4,2,35,4,13
	.byte	'triggerPriority',0
	.word	686
	.byte	2,2,35,8,13
	.byte	'typeOfService',0
	.word	10265
	.byte	1,2,35,10,0,27
	.word	16626
.L171:
	.byte	3
	.word	16789
.L173:
	.byte	7
	.byte	'long int',0,4,5
.L219:
	.byte	3
	.word	16626
	.byte	7
	.byte	'short int',0,2,5,28
	.byte	'__wchar_t',0,16,1,1
	.word	16816
	.byte	28
	.byte	'__size_t',0,16,1,1
	.word	494
	.byte	28
	.byte	'__ptrdiff_t',0,16,1,1
	.word	510
	.byte	29,1,3
	.word	16884
	.byte	28
	.byte	'__codeptr',0,16,1,1
	.word	16886
	.byte	28
	.byte	'boolean',0,17,101,29
	.word	669
	.byte	28
	.byte	'uint8',0,17,105,29
	.word	669
	.byte	28
	.byte	'uint16',0,17,109,29
	.word	686
	.byte	28
	.byte	'uint32',0,17,113,29
	.word	1636
	.byte	28
	.byte	'uint64',0,17,118,29
	.word	376
	.byte	28
	.byte	'sint16',0,17,126,29
	.word	16816
	.byte	28
	.byte	'sint32',0,17,131,1,29
	.word	16799
	.byte	7
	.byte	'long long int',0,8,5,28
	.byte	'sint64',0,17,138,1,29
	.word	17015
	.byte	28
	.byte	'float32',0,17,167,1,29
	.word	290
	.byte	28
	.byte	'pvoid',0,18,57,28
	.word	408
	.byte	28
	.byte	'Ifx_TickTime',0,18,79,28
	.word	17015
	.byte	28
	.byte	'Ifx_Priority',0,18,103,16
	.word	686
	.byte	15,18,130,1,9,1,16
	.byte	'Ifx_RxSel_a',0,0,16
	.byte	'Ifx_RxSel_b',0,1,16
	.byte	'Ifx_RxSel_c',0,2,16
	.byte	'Ifx_RxSel_d',0,3,16
	.byte	'Ifx_RxSel_e',0,4,16
	.byte	'Ifx_RxSel_f',0,5,16
	.byte	'Ifx_RxSel_g',0,6,16
	.byte	'Ifx_RxSel_h',0,7,0,28
	.byte	'Ifx_RxSel',0,18,140,1,3
	.word	17121
	.byte	14
	.word	402
	.byte	3
	.word	17259
	.byte	26,18,143,1,9,8,13
	.byte	'module',0
	.word	17264
	.byte	4,2,35,0,13
	.byte	'index',0
	.word	16799
	.byte	4,2,35,4,0,28
	.byte	'IfxModule_IndexMap',0,18,147,1,3
	.word	17269
	.byte	28
	.byte	'Ifx_STM_ACCEN0_Bits',0,14,79,3
	.word	12837
	.byte	28
	.byte	'Ifx_STM_ACCEN1_Bits',0,14,85,3
	.word	12748
	.byte	28
	.byte	'Ifx_STM_CAP_Bits',0,14,91,3
	.word	11278
	.byte	28
	.byte	'Ifx_STM_CAPSV_Bits',0,14,97,3
	.word	12155
	.byte	28
	.byte	'Ifx_STM_CLC_Bits',0,14,107,3
	.word	10401
	.byte	28
	.byte	'Ifx_STM_CMCON_Bits',0,14,120,3
	.word	11456
	.byte	28
	.byte	'Ifx_STM_CMP_Bits',0,14,126,3
	.word	11365
	.byte	28
	.byte	'Ifx_STM_ICR_Bits',0,14,139,1,3
	.word	11687
	.byte	28
	.byte	'Ifx_STM_ID_Bits',0,14,147,1,3
	.word	10557
	.byte	28
	.byte	'Ifx_STM_ISCR_Bits',0,14,157,1,3
	.word	11904
	.byte	28
	.byte	'Ifx_STM_KRST0_Bits',0,14,165,1,3
	.word	12625
	.byte	28
	.byte	'Ifx_STM_KRST1_Bits',0,14,172,1,3
	.word	12521
	.byte	28
	.byte	'Ifx_STM_KRSTCLR_Bits',0,14,179,1,3
	.word	12415
	.byte	28
	.byte	'Ifx_STM_OCS_Bits',0,14,189,1,3
	.word	12255
	.byte	28
	.byte	'Ifx_STM_TIM0_Bits',0,14,195,1,3
	.word	10679
	.byte	28
	.byte	'Ifx_STM_TIM0SV_Bits',0,14,201,1,3
	.word	12068
	.byte	28
	.byte	'Ifx_STM_TIM1_Bits',0,14,207,1,3
	.word	10764
	.byte	28
	.byte	'Ifx_STM_TIM2_Bits',0,14,213,1,3
	.word	10849
	.byte	28
	.byte	'Ifx_STM_TIM3_Bits',0,14,219,1,3
	.word	10934
	.byte	28
	.byte	'Ifx_STM_TIM4_Bits',0,14,225,1,3
	.word	11020
	.byte	28
	.byte	'Ifx_STM_TIM5_Bits',0,14,231,1,3
	.word	11106
	.byte	28
	.byte	'Ifx_STM_TIM6_Bits',0,14,237,1,3
	.word	11192
	.byte	28
	.byte	'Ifx_STM_ACCEN0',0,14,250,1,3
	.word	13366
	.byte	28
	.byte	'Ifx_STM_ACCEN1',0,14,130,2,3
	.word	12797
	.byte	28
	.byte	'Ifx_STM_CAP',0,14,138,2,3
	.word	11325
	.byte	28
	.byte	'Ifx_STM_CAPSV',0,14,146,2,3
	.word	12204
	.byte	28
	.byte	'Ifx_STM_CLC',0,14,154,2,3
	.word	10517
	.byte	28
	.byte	'Ifx_STM_CMCON',0,14,162,2,3
	.word	11647
	.byte	28
	.byte	'Ifx_STM_CMP',0,14,170,2,3
	.word	11407
	.byte	28
	.byte	'Ifx_STM_ICR',0,14,178,2,3
	.word	11864
	.byte	28
	.byte	'Ifx_STM_ID',0,14,186,2,3
	.word	10639
	.byte	28
	.byte	'Ifx_STM_ISCR',0,14,194,2,3
	.word	12028
	.byte	28
	.byte	'Ifx_STM_KRST0',0,14,202,2,3
	.word	12708
	.byte	28
	.byte	'Ifx_STM_KRST1',0,14,210,2,3
	.word	12585
	.byte	28
	.byte	'Ifx_STM_KRSTCLR',0,14,218,2,3
	.word	12481
	.byte	28
	.byte	'Ifx_STM_OCS',0,14,226,2,3
	.word	12375
	.byte	28
	.byte	'Ifx_STM_TIM0',0,14,234,2,3
	.word	10724
	.byte	28
	.byte	'Ifx_STM_TIM0SV',0,14,242,2,3
	.word	12115
	.byte	28
	.byte	'Ifx_STM_TIM1',0,14,250,2,3
	.word	10809
	.byte	28
	.byte	'Ifx_STM_TIM2',0,14,130,3,3
	.word	10894
	.byte	28
	.byte	'Ifx_STM_TIM3',0,14,138,3,3
	.word	10980
	.byte	28
	.byte	'Ifx_STM_TIM4',0,14,146,3,3
	.word	11066
	.byte	28
	.byte	'Ifx_STM_TIM5',0,14,154,3,3
	.word	11152
	.byte	28
	.byte	'Ifx_STM_TIM6',0,14,162,3,3
	.word	11238
	.byte	14
	.word	13406
	.byte	28
	.byte	'Ifx_STM',0,14,201,3,3
	.word	18418
	.byte	28
	.byte	'IfxStm_Index',0,15,92,3
	.word	14481
	.byte	18,16
	.word	17269
	.byte	19,1,0,27
	.word	18461
	.byte	30
	.byte	'IfxStm_cfg_indexMap',0,15,103,41
	.word	18470
	.byte	1,1,15,19,236,10,9,1,16
	.byte	'IfxScu_CCUCON0_CLKSEL_fBack',0,0,16
	.byte	'IfxScu_CCUCON0_CLKSEL_fPll',0,1,0,28
	.byte	'IfxScu_CCUCON0_CLKSEL',0,19,240,10,3
	.word	18505
	.byte	15,19,250,10,9,1,16
	.byte	'IfxScu_WDTCON1_IR_divBy16384',0,0,16
	.byte	'IfxScu_WDTCON1_IR_divBy256',0,1,16
	.byte	'IfxScu_WDTCON1_IR_divBy64',0,2,0,28
	.byte	'IfxScu_WDTCON1_IR',0,19,255,10,3
	.word	18602
	.byte	10
	.byte	'_Ifx_SCU_ACCEN0_Bits',0,4,45,16,4,11
	.byte	'EN0',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	669
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	669
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	669
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	669
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	669
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	669
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	669
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	669
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	669
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	669
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	669
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	669
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	669
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	669
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	669
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	669
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	669
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	669
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	669
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	669
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	669
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	669
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	669
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	669
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	669
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	669
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	669
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	669
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_ACCEN0_Bits',0,4,79,3
	.word	18724
	.byte	10
	.byte	'_Ifx_SCU_ACCEN1_Bits',0,4,82,16,4,11
	.byte	'reserved_0',0,4
	.word	494
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_SCU_ACCEN1_Bits',0,4,85,3
	.word	19281
	.byte	10
	.byte	'_Ifx_SCU_ARSTDIS_Bits',0,4,88,16,4,11
	.byte	'STM0DIS',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'STM1DIS',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'STM2DIS',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	494
	.byte	29,0,2,35,0,0,28
	.byte	'Ifx_SCU_ARSTDIS_Bits',0,4,94,3
	.word	19358
	.byte	10
	.byte	'_Ifx_SCU_CCUCON0_Bits',0,4,97,16,4,11
	.byte	'BAUD1DIV',0,1
	.word	669
	.byte	4,4,2,35,0,11
	.byte	'BAUD2DIV',0,1
	.word	669
	.byte	4,0,2,35,0,11
	.byte	'SRIDIV',0,1
	.word	669
	.byte	4,4,2,35,1,11
	.byte	'LPDIV',0,1
	.word	669
	.byte	4,0,2,35,1,11
	.byte	'SPBDIV',0,1
	.word	669
	.byte	4,4,2,35,2,11
	.byte	'FSI2DIV',0,1
	.word	669
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	669
	.byte	2,0,2,35,2,11
	.byte	'FSIDIV',0,1
	.word	669
	.byte	2,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	669
	.byte	2,4,2,35,3,11
	.byte	'CLKSEL',0,1
	.word	669
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	669
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	669
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_CCUCON0_Bits',0,4,111,3
	.word	19494
	.byte	10
	.byte	'_Ifx_SCU_CCUCON1_Bits',0,4,114,16,4,11
	.byte	'CANDIV',0,1
	.word	669
	.byte	4,4,2,35,0,11
	.byte	'ERAYDIV',0,1
	.word	669
	.byte	4,0,2,35,0,11
	.byte	'STMDIV',0,1
	.word	669
	.byte	4,4,2,35,1,11
	.byte	'GTMDIV',0,1
	.word	669
	.byte	4,0,2,35,1,11
	.byte	'ETHDIV',0,1
	.word	669
	.byte	4,4,2,35,2,11
	.byte	'ASCLINFDIV',0,1
	.word	669
	.byte	4,0,2,35,2,11
	.byte	'ASCLINSDIV',0,1
	.word	669
	.byte	4,4,2,35,3,11
	.byte	'INSEL',0,1
	.word	669
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	669
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	669
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_CCUCON1_Bits',0,4,126,3
	.word	19774
	.byte	10
	.byte	'_Ifx_SCU_CCUCON2_Bits',0,4,129,1,16,4,11
	.byte	'BBBDIV',0,1
	.word	669
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	494
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	669
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	669
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_CCUCON2_Bits',0,4,135,1,3
	.word	20012
	.byte	10
	.byte	'_Ifx_SCU_CCUCON3_Bits',0,4,138,1,16,4,11
	.byte	'PLLDIV',0,1
	.word	669
	.byte	6,2,2,35,0,11
	.byte	'PLLSEL',0,1
	.word	669
	.byte	2,0,2,35,0,11
	.byte	'PLLERAYDIV',0,1
	.word	669
	.byte	6,2,2,35,1,11
	.byte	'PLLERAYSEL',0,1
	.word	669
	.byte	2,0,2,35,1,11
	.byte	'SRIDIV',0,1
	.word	669
	.byte	6,2,2,35,2,11
	.byte	'SRISEL',0,1
	.word	669
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	669
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	669
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	669
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	669
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_CCUCON3_Bits',0,4,150,1,3
	.word	20140
	.byte	10
	.byte	'_Ifx_SCU_CCUCON4_Bits',0,4,153,1,16,4,11
	.byte	'SPBDIV',0,1
	.word	669
	.byte	6,2,2,35,0,11
	.byte	'SPBSEL',0,1
	.word	669
	.byte	2,0,2,35,0,11
	.byte	'GTMDIV',0,1
	.word	669
	.byte	6,2,2,35,1,11
	.byte	'GTMSEL',0,1
	.word	669
	.byte	2,0,2,35,1,11
	.byte	'STMDIV',0,1
	.word	669
	.byte	6,2,2,35,2,11
	.byte	'STMSEL',0,1
	.word	669
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	669
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	669
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	669
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	669
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_CCUCON4_Bits',0,4,165,1,3
	.word	20383
	.byte	10
	.byte	'_Ifx_SCU_CCUCON5_Bits',0,4,168,1,16,4,11
	.byte	'MAXDIV',0,1
	.word	669
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	494
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	669
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	669
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_CCUCON5_Bits',0,4,174,1,3
	.word	20618
	.byte	10
	.byte	'_Ifx_SCU_CCUCON6_Bits',0,4,177,1,16,4,11
	.byte	'CPU0DIV',0,1
	.word	669
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	494
	.byte	26,0,2,35,0,0,28
	.byte	'Ifx_SCU_CCUCON6_Bits',0,4,181,1,3
	.word	20746
	.byte	10
	.byte	'_Ifx_SCU_CCUCON7_Bits',0,4,184,1,16,4,11
	.byte	'CPU1DIV',0,1
	.word	669
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	494
	.byte	26,0,2,35,0,0,28
	.byte	'Ifx_SCU_CCUCON7_Bits',0,4,188,1,3
	.word	20846
	.byte	10
	.byte	'_Ifx_SCU_CHIPID_Bits',0,4,191,1,16,4,11
	.byte	'CHREV',0,1
	.word	669
	.byte	6,2,2,35,0,11
	.byte	'CHTEC',0,1
	.word	669
	.byte	2,0,2,35,0,11
	.byte	'CHID',0,1
	.word	669
	.byte	8,0,2,35,1,11
	.byte	'EEA',0,1
	.word	669
	.byte	1,7,2,35,2,11
	.byte	'UCODE',0,1
	.word	669
	.byte	7,0,2,35,2,11
	.byte	'FSIZE',0,1
	.word	669
	.byte	4,4,2,35,3,11
	.byte	'SP',0,1
	.word	669
	.byte	2,2,2,35,3,11
	.byte	'SEC',0,1
	.word	669
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	669
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_CHIPID_Bits',0,4,202,1,3
	.word	20946
	.byte	10
	.byte	'_Ifx_SCU_DTSCON_Bits',0,4,205,1,16,4,11
	.byte	'PWD',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'START',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	669
	.byte	2,4,2,35,0,11
	.byte	'CAL',0,4
	.word	494
	.byte	20,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	669
	.byte	7,1,2,35,3,11
	.byte	'SLCK',0,1
	.word	669
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_DTSCON_Bits',0,4,213,1,3
	.word	21154
	.byte	10
	.byte	'_Ifx_SCU_DTSLIM_Bits',0,4,216,1,16,4,11
	.byte	'LOWER',0,2
	.word	686
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	669
	.byte	5,1,2,35,1,11
	.byte	'LLU',0,1
	.word	669
	.byte	1,0,2,35,1,11
	.byte	'UPPER',0,2
	.word	686
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	669
	.byte	4,2,2,35,3,11
	.byte	'SLCK',0,1
	.word	669
	.byte	1,1,2,35,3,11
	.byte	'UOF',0,1
	.word	669
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_DTSLIM_Bits',0,4,225,1,3
	.word	21319
	.byte	10
	.byte	'_Ifx_SCU_DTSSTAT_Bits',0,4,228,1,16,4,11
	.byte	'RESULT',0,2
	.word	686
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	669
	.byte	4,2,2,35,1,11
	.byte	'RDY',0,1
	.word	669
	.byte	1,1,2,35,1,11
	.byte	'BUSY',0,1
	.word	669
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	686
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_SCU_DTSSTAT_Bits',0,4,235,1,3
	.word	21502
	.byte	10
	.byte	'_Ifx_SCU_EICR_Bits',0,4,238,1,16,4,11
	.byte	'reserved_0',0,1
	.word	669
	.byte	4,4,2,35,0,11
	.byte	'EXIS0',0,1
	.word	669
	.byte	3,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	669
	.byte	1,0,2,35,0,11
	.byte	'FEN0',0,1
	.word	669
	.byte	1,7,2,35,1,11
	.byte	'REN0',0,1
	.word	669
	.byte	1,6,2,35,1,11
	.byte	'LDEN0',0,1
	.word	669
	.byte	1,5,2,35,1,11
	.byte	'EIEN0',0,1
	.word	669
	.byte	1,4,2,35,1,11
	.byte	'INP0',0,1
	.word	669
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	494
	.byte	5,12,2,35,0,11
	.byte	'EXIS1',0,1
	.word	669
	.byte	3,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	669
	.byte	1,0,2,35,2,11
	.byte	'FEN1',0,1
	.word	669
	.byte	1,7,2,35,3,11
	.byte	'REN1',0,1
	.word	669
	.byte	1,6,2,35,3,11
	.byte	'LDEN1',0,1
	.word	669
	.byte	1,5,2,35,3,11
	.byte	'EIEN1',0,1
	.word	669
	.byte	1,4,2,35,3,11
	.byte	'INP1',0,1
	.word	669
	.byte	3,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	669
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EICR_Bits',0,4,129,2,3
	.word	21656
	.byte	10
	.byte	'_Ifx_SCU_EIFR_Bits',0,4,132,2,16,4,11
	.byte	'INTF0',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'INTF1',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'INTF2',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'INTF3',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'INTF4',0,1
	.word	669
	.byte	1,3,2,35,0,11
	.byte	'INTF5',0,1
	.word	669
	.byte	1,2,2,35,0,11
	.byte	'INTF6',0,1
	.word	669
	.byte	1,1,2,35,0,11
	.byte	'INTF7',0,1
	.word	669
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	494
	.byte	24,0,2,35,0,0,28
	.byte	'Ifx_SCU_EIFR_Bits',0,4,143,2,3
	.word	22020
	.byte	10
	.byte	'_Ifx_SCU_EMSR_Bits',0,4,146,2,16,4,11
	.byte	'POL',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'MODE',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'ENON',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'PSEL',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	686
	.byte	12,0,2,35,0,11
	.byte	'EMSF',0,1
	.word	669
	.byte	1,7,2,35,2,11
	.byte	'SEMSF',0,1
	.word	669
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	669
	.byte	6,0,2,35,2,11
	.byte	'EMSFM',0,1
	.word	669
	.byte	2,6,2,35,3,11
	.byte	'SEMSFM',0,1
	.word	669
	.byte	2,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	669
	.byte	4,0,2,35,3,0,28
	.byte	'Ifx_SCU_EMSR_Bits',0,4,159,2,3
	.word	22231
	.byte	10
	.byte	'_Ifx_SCU_ESRCFG_Bits',0,4,162,2,16,4,11
	.byte	'reserved_0',0,1
	.word	669
	.byte	7,1,2,35,0,11
	.byte	'EDCON',0,2
	.word	686
	.byte	2,7,2,35,0,11
	.byte	'reserved_9',0,4
	.word	494
	.byte	23,0,2,35,0,0,28
	.byte	'Ifx_SCU_ESRCFG_Bits',0,4,167,2,3
	.word	22483
	.byte	10
	.byte	'_Ifx_SCU_ESROCFG_Bits',0,4,170,2,16,4,11
	.byte	'ARI',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'ARC',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	494
	.byte	30,0,2,35,0,0,28
	.byte	'Ifx_SCU_ESROCFG_Bits',0,4,175,2,3
	.word	22601
	.byte	10
	.byte	'_Ifx_SCU_EVR13CON_Bits',0,4,178,2,16,4,11
	.byte	'reserved_0',0,4
	.word	494
	.byte	28,4,2,35,0,11
	.byte	'EVR13OFF',0,1
	.word	669
	.byte	1,3,2,35,3,11
	.byte	'BPEVR13OFF',0,1
	.word	669
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	669
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	669
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVR13CON_Bits',0,4,185,2,3
	.word	22712
	.byte	10
	.byte	'_Ifx_SCU_EVR33CON_Bits',0,4,188,2,16,4,11
	.byte	'reserved_0',0,4
	.word	494
	.byte	28,4,2,35,0,11
	.byte	'EVR33OFF',0,1
	.word	669
	.byte	1,3,2,35,3,11
	.byte	'BPEVR33OFF',0,1
	.word	669
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	669
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	669
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVR33CON_Bits',0,4,195,2,3
	.word	22875
	.byte	10
	.byte	'_Ifx_SCU_EVRADCSTAT_Bits',0,4,198,2,16,4,11
	.byte	'ADC13V',0,1
	.word	669
	.byte	8,0,2,35,0,11
	.byte	'ADC33V',0,1
	.word	669
	.byte	8,0,2,35,1,11
	.byte	'ADCSWDV',0,1
	.word	669
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	669
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	669
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVRADCSTAT_Bits',0,4,205,2,3
	.word	23038
	.byte	10
	.byte	'_Ifx_SCU_EVRDVSTAT_Bits',0,4,208,2,16,4,11
	.byte	'DVS13TRIM',0,1
	.word	669
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	669
	.byte	8,0,2,35,1,11
	.byte	'DVS33TRIM',0,1
	.word	669
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	669
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	669
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVRDVSTAT_Bits',0,4,215,2,3
	.word	23196
	.byte	10
	.byte	'_Ifx_SCU_EVRMONCTRL_Bits',0,4,218,2,16,4,11
	.byte	'EVR13OVMOD',0,1
	.word	669
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	669
	.byte	2,4,2,35,0,11
	.byte	'EVR13UVMOD',0,1
	.word	669
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	669
	.byte	2,0,2,35,0,11
	.byte	'EVR33OVMOD',0,1
	.word	669
	.byte	2,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	669
	.byte	2,4,2,35,1,11
	.byte	'EVR33UVMOD',0,1
	.word	669
	.byte	2,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	669
	.byte	2,0,2,35,1,11
	.byte	'SWDOVMOD',0,1
	.word	669
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	669
	.byte	2,4,2,35,2,11
	.byte	'SWDUVMOD',0,1
	.word	669
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,2
	.word	686
	.byte	10,0,2,35,2,0,28
	.byte	'Ifx_SCU_EVRMONCTRL_Bits',0,4,232,2,3
	.word	23361
	.byte	10
	.byte	'_Ifx_SCU_EVROSCCTRL_Bits',0,4,235,2,16,4,11
	.byte	'OSCTRIM',0,2
	.word	686
	.byte	10,6,2,35,0,11
	.byte	'OSCPTAT',0,1
	.word	669
	.byte	6,0,2,35,1,11
	.byte	'OSCANASEL',0,1
	.word	669
	.byte	4,4,2,35,2,11
	.byte	'HPBGTRIM',0,2
	.word	686
	.byte	7,5,2,35,2,11
	.byte	'HPBGCLKEN',0,1
	.word	669
	.byte	1,4,2,35,3,11
	.byte	'OSC3V3',0,1
	.word	669
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	669
	.byte	2,1,2,35,3,11
	.byte	'LCK',0,1
	.word	669
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVROSCCTRL_Bits',0,4,245,2,3
	.word	23690
	.byte	10
	.byte	'_Ifx_SCU_EVROVMON_Bits',0,4,248,2,16,4,11
	.byte	'EVR13OVVAL',0,1
	.word	669
	.byte	8,0,2,35,0,11
	.byte	'EVR33OVVAL',0,1
	.word	669
	.byte	8,0,2,35,1,11
	.byte	'SWDOVVAL',0,1
	.word	669
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	669
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	669
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVROVMON_Bits',0,4,255,2,3
	.word	23911
	.byte	10
	.byte	'_Ifx_SCU_EVRRSTCON_Bits',0,4,130,3,16,4,11
	.byte	'RST13TRIM',0,1
	.word	669
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	494
	.byte	16,8,2,35,0,11
	.byte	'RST13OFF',0,1
	.word	669
	.byte	1,7,2,35,3,11
	.byte	'BPRST13OFF',0,1
	.word	669
	.byte	1,6,2,35,3,11
	.byte	'RST33OFF',0,1
	.word	669
	.byte	1,5,2,35,3,11
	.byte	'BPRST33OFF',0,1
	.word	669
	.byte	1,4,2,35,3,11
	.byte	'RSTSWDOFF',0,1
	.word	669
	.byte	1,3,2,35,3,11
	.byte	'BPRSTSWDOFF',0,1
	.word	669
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	669
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	669
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVRRSTCON_Bits',0,4,142,3,3
	.word	24074
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF1_Bits',0,4,145,3,16,4,11
	.byte	'SD5P',0,1
	.word	669
	.byte	8,0,2,35,0,11
	.byte	'SD5I',0,1
	.word	669
	.byte	8,0,2,35,1,11
	.byte	'SD5D',0,1
	.word	669
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	669
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	669
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVRSDCOEFF1_Bits',0,4,152,3,3
	.word	24346
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF2_Bits',0,4,155,3,16,4,11
	.byte	'SD33P',0,1
	.word	669
	.byte	8,0,2,35,0,11
	.byte	'SD33I',0,1
	.word	669
	.byte	8,0,2,35,1,11
	.byte	'SD33D',0,1
	.word	669
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	669
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	669
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVRSDCOEFF2_Bits',0,4,162,3,3
	.word	24499
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF3_Bits',0,4,165,3,16,4,11
	.byte	'CT5REG0',0,1
	.word	669
	.byte	8,0,2,35,0,11
	.byte	'CT5REG1',0,1
	.word	669
	.byte	8,0,2,35,1,11
	.byte	'CT5REG2',0,1
	.word	669
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	669
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	669
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVRSDCOEFF3_Bits',0,4,172,3,3
	.word	24655
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF4_Bits',0,4,175,3,16,4,11
	.byte	'CT5REG3',0,1
	.word	669
	.byte	8,0,2,35,0,11
	.byte	'CT5REG4',0,1
	.word	669
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	686
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	669
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVRSDCOEFF4_Bits',0,4,181,3,3
	.word	24817
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF5_Bits',0,4,184,3,16,4,11
	.byte	'CT33REG0',0,1
	.word	669
	.byte	8,0,2,35,0,11
	.byte	'CT33REG1',0,1
	.word	669
	.byte	8,0,2,35,1,11
	.byte	'CT33REG2',0,1
	.word	669
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	669
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	669
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVRSDCOEFF5_Bits',0,4,191,3,3
	.word	24960
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF6_Bits',0,4,194,3,16,4,11
	.byte	'CT33REG3',0,1
	.word	669
	.byte	8,0,2,35,0,11
	.byte	'CT33REG4',0,1
	.word	669
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	686
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	669
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVRSDCOEFF6_Bits',0,4,200,3,3
	.word	25125
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL1_Bits',0,4,203,3,16,4,11
	.byte	'SDFREQSPRD',0,2
	.word	686
	.byte	16,0,2,35,0,11
	.byte	'SDFREQ',0,1
	.word	669
	.byte	8,0,2,35,2,11
	.byte	'SDSTEP',0,1
	.word	669
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	669
	.byte	2,2,2,35,3,11
	.byte	'SDSAMPLE',0,1
	.word	669
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	669
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVRSDCTRL1_Bits',0,4,211,3,3
	.word	25270
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL2_Bits',0,4,214,3,16,4,11
	.byte	'DRVP',0,1
	.word	669
	.byte	8,0,2,35,0,11
	.byte	'SDMINMAXDC',0,1
	.word	669
	.byte	8,0,2,35,1,11
	.byte	'DRVN',0,1
	.word	669
	.byte	8,0,2,35,2,11
	.byte	'SDLUT',0,1
	.word	669
	.byte	6,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	669
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	669
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVRSDCTRL2_Bits',0,4,222,3,3
	.word	25451
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL3_Bits',0,4,225,3,16,4,11
	.byte	'SDPWMPRE',0,1
	.word	669
	.byte	8,0,2,35,0,11
	.byte	'SDPID',0,1
	.word	669
	.byte	8,0,2,35,1,11
	.byte	'SDVOKLVL',0,1
	.word	669
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	669
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	669
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVRSDCTRL3_Bits',0,4,232,3,3
	.word	25625
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL4_Bits',0,4,235,3,16,4,11
	.byte	'reserved_0',0,1
	.word	669
	.byte	8,0,2,35,0,11
	.byte	'SYNCDIV',0,1
	.word	669
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	494
	.byte	20,1,2,35,0,11
	.byte	'LCK',0,1
	.word	669
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVRSDCTRL4_Bits',0,4,241,3,3
	.word	25785
	.byte	10
	.byte	'_Ifx_SCU_EVRSTAT_Bits',0,4,244,3,16,4,11
	.byte	'EVR13',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'OV13',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'EVR33',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'OV33',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'OVSWD',0,1
	.word	669
	.byte	1,3,2,35,0,11
	.byte	'UV13',0,1
	.word	669
	.byte	1,2,2,35,0,11
	.byte	'UV33',0,1
	.word	669
	.byte	1,1,2,35,0,11
	.byte	'UVSWD',0,1
	.word	669
	.byte	1,0,2,35,0,11
	.byte	'EXTPASS13',0,1
	.word	669
	.byte	1,7,2,35,1,11
	.byte	'EXTPASS33',0,1
	.word	669
	.byte	1,6,2,35,1,11
	.byte	'BGPROK',0,1
	.word	669
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	494
	.byte	21,0,2,35,0,0,28
	.byte	'Ifx_SCU_EVRSTAT_Bits',0,4,130,4,3
	.word	25929
	.byte	10
	.byte	'_Ifx_SCU_EVRTRIM_Bits',0,4,133,4,16,4,11
	.byte	'EVR13TRIM',0,1
	.word	669
	.byte	8,0,2,35,0,11
	.byte	'SDVOUTSEL',0,1
	.word	669
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	686
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	669
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVRTRIM_Bits',0,4,139,4,3
	.word	26203
	.byte	10
	.byte	'_Ifx_SCU_EVRUVMON_Bits',0,4,142,4,16,4,11
	.byte	'EVR13UVVAL',0,1
	.word	669
	.byte	8,0,2,35,0,11
	.byte	'EVR33UVVAL',0,1
	.word	669
	.byte	8,0,2,35,1,11
	.byte	'SWDUVVAL',0,1
	.word	669
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	669
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	669
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVRUVMON_Bits',0,4,149,4,3
	.word	26342
	.byte	10
	.byte	'_Ifx_SCU_EXTCON_Bits',0,4,152,4,16,4,11
	.byte	'EN0',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'SEL0',0,1
	.word	669
	.byte	4,2,2,35,0,11
	.byte	'reserved_6',0,2
	.word	686
	.byte	10,0,2,35,0,11
	.byte	'EN1',0,1
	.word	669
	.byte	1,7,2,35,2,11
	.byte	'NSEL',0,1
	.word	669
	.byte	1,6,2,35,2,11
	.byte	'SEL1',0,1
	.word	669
	.byte	4,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	669
	.byte	2,0,2,35,2,11
	.byte	'DIV1',0,1
	.word	669
	.byte	8,0,2,35,3,0,28
	.byte	'Ifx_SCU_EXTCON_Bits',0,4,163,4,3
	.word	26505
	.byte	10
	.byte	'_Ifx_SCU_FDR_Bits',0,4,166,4,16,4,11
	.byte	'STEP',0,2
	.word	686
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	669
	.byte	4,2,2,35,1,11
	.byte	'DM',0,1
	.word	669
	.byte	2,0,2,35,1,11
	.byte	'RESULT',0,2
	.word	686
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	669
	.byte	5,1,2,35,3,11
	.byte	'DISCLK',0,1
	.word	669
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_FDR_Bits',0,4,174,4,3
	.word	26723
	.byte	10
	.byte	'_Ifx_SCU_FMR_Bits',0,4,177,4,16,4,11
	.byte	'FS0',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'FS1',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'FS2',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'FS3',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'FS4',0,1
	.word	669
	.byte	1,3,2,35,0,11
	.byte	'FS5',0,1
	.word	669
	.byte	1,2,2,35,0,11
	.byte	'FS6',0,1
	.word	669
	.byte	1,1,2,35,0,11
	.byte	'FS7',0,1
	.word	669
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	669
	.byte	8,0,2,35,1,11
	.byte	'FC0',0,1
	.word	669
	.byte	1,7,2,35,2,11
	.byte	'FC1',0,1
	.word	669
	.byte	1,6,2,35,2,11
	.byte	'FC2',0,1
	.word	669
	.byte	1,5,2,35,2,11
	.byte	'FC3',0,1
	.word	669
	.byte	1,4,2,35,2,11
	.byte	'FC4',0,1
	.word	669
	.byte	1,3,2,35,2,11
	.byte	'FC5',0,1
	.word	669
	.byte	1,2,2,35,2,11
	.byte	'FC6',0,1
	.word	669
	.byte	1,1,2,35,2,11
	.byte	'FC7',0,1
	.word	669
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	669
	.byte	8,0,2,35,3,0,28
	.byte	'Ifx_SCU_FMR_Bits',0,4,197,4,3
	.word	26886
	.byte	10
	.byte	'_Ifx_SCU_ID_Bits',0,4,200,4,16,4,11
	.byte	'MODREV',0,1
	.word	669
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	669
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	686
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_SCU_ID_Bits',0,4,205,4,3
	.word	27222
	.byte	10
	.byte	'_Ifx_SCU_IGCR_Bits',0,4,208,4,16,4,11
	.byte	'IPEN00',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'IPEN01',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'IPEN02',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'IPEN03',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'IPEN04',0,1
	.word	669
	.byte	1,3,2,35,0,11
	.byte	'IPEN05',0,1
	.word	669
	.byte	1,2,2,35,0,11
	.byte	'IPEN06',0,1
	.word	669
	.byte	1,1,2,35,0,11
	.byte	'IPEN07',0,1
	.word	669
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	669
	.byte	5,3,2,35,1,11
	.byte	'GEEN0',0,1
	.word	669
	.byte	1,2,2,35,1,11
	.byte	'IGP0',0,1
	.word	669
	.byte	2,0,2,35,1,11
	.byte	'IPEN10',0,1
	.word	669
	.byte	1,7,2,35,2,11
	.byte	'IPEN11',0,1
	.word	669
	.byte	1,6,2,35,2,11
	.byte	'IPEN12',0,1
	.word	669
	.byte	1,5,2,35,2,11
	.byte	'IPEN13',0,1
	.word	669
	.byte	1,4,2,35,2,11
	.byte	'IPEN14',0,1
	.word	669
	.byte	1,3,2,35,2,11
	.byte	'IPEN15',0,1
	.word	669
	.byte	1,2,2,35,2,11
	.byte	'IPEN16',0,1
	.word	669
	.byte	1,1,2,35,2,11
	.byte	'IPEN17',0,1
	.word	669
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	669
	.byte	5,3,2,35,3,11
	.byte	'GEEN1',0,1
	.word	669
	.byte	1,2,2,35,3,11
	.byte	'IGP1',0,1
	.word	669
	.byte	2,0,2,35,3,0,28
	.byte	'Ifx_SCU_IGCR_Bits',0,4,232,4,3
	.word	27329
	.byte	10
	.byte	'_Ifx_SCU_IN_Bits',0,4,235,4,16,4,11
	.byte	'P0',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	494
	.byte	30,0,2,35,0,0,28
	.byte	'Ifx_SCU_IN_Bits',0,4,240,4,3
	.word	27781
	.byte	10
	.byte	'_Ifx_SCU_IOCR_Bits',0,4,243,4,16,4,11
	.byte	'reserved_0',0,1
	.word	669
	.byte	4,4,2,35,0,11
	.byte	'PC0',0,1
	.word	669
	.byte	4,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	669
	.byte	4,4,2,35,1,11
	.byte	'PC1',0,1
	.word	669
	.byte	4,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	686
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_SCU_IOCR_Bits',0,4,250,4,3
	.word	27880
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL0_Bits',0,4,253,4,16,4,11
	.byte	'LBISTREQ',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'LBISTREQP',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'PATTERNS',0,2
	.word	686
	.byte	14,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	686
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_SCU_LBISTCTRL0_Bits',0,4,131,5,3
	.word	28030
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL1_Bits',0,4,134,5,16,4,11
	.byte	'SEED',0,4
	.word	494
	.byte	23,9,2,35,0,11
	.byte	'reserved_23',0,1
	.word	669
	.byte	1,0,2,35,2,11
	.byte	'SPLITSH',0,1
	.word	669
	.byte	3,5,2,35,3,11
	.byte	'BODY',0,1
	.word	669
	.byte	1,4,2,35,3,11
	.byte	'LBISTFREQU',0,1
	.word	669
	.byte	4,0,2,35,3,0,28
	.byte	'Ifx_SCU_LBISTCTRL1_Bits',0,4,141,5,3
	.word	28179
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL2_Bits',0,4,144,5,16,4,11
	.byte	'SIGNATURE',0,4
	.word	494
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	669
	.byte	7,1,2,35,3,11
	.byte	'LBISTDONE',0,1
	.word	669
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_LBISTCTRL2_Bits',0,4,149,5,3
	.word	28340
	.byte	10
	.byte	'_Ifx_SCU_LCLCON_Bits',0,4,152,5,16,4,11
	.byte	'reserved_0',0,2
	.word	686
	.byte	16,0,2,35,0,11
	.byte	'LS',0,1
	.word	669
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,2
	.word	686
	.byte	14,1,2,35,2,11
	.byte	'LSEN',0,1
	.word	669
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_LCLCON_Bits',0,4,158,5,3
	.word	28470
	.byte	10
	.byte	'_Ifx_SCU_LCLTEST_Bits',0,4,161,5,16,4,11
	.byte	'LCLT0',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'LCLT1',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	494
	.byte	30,0,2,35,0,0,28
	.byte	'Ifx_SCU_LCLTEST_Bits',0,4,166,5,3
	.word	28602
	.byte	10
	.byte	'_Ifx_SCU_MANID_Bits',0,4,169,5,16,4,11
	.byte	'DEPT',0,1
	.word	669
	.byte	5,3,2,35,0,11
	.byte	'MANUF',0,2
	.word	686
	.byte	11,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	686
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_SCU_MANID_Bits',0,4,174,5,3
	.word	28717
	.byte	10
	.byte	'_Ifx_SCU_OMR_Bits',0,4,177,5,16,4,11
	.byte	'PS0',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	686
	.byte	14,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	669
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	669
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	686
	.byte	14,0,2,35,2,0,28
	.byte	'Ifx_SCU_OMR_Bits',0,4,185,5,3
	.word	28828
	.byte	10
	.byte	'_Ifx_SCU_OSCCON_Bits',0,4,188,5,16,4,11
	.byte	'reserved_0',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'PLLLV',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'OSCRES',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'GAINSEL',0,1
	.word	669
	.byte	2,3,2,35,0,11
	.byte	'MODE',0,1
	.word	669
	.byte	2,1,2,35,0,11
	.byte	'SHBY',0,1
	.word	669
	.byte	1,0,2,35,0,11
	.byte	'PLLHV',0,1
	.word	669
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	669
	.byte	1,6,2,35,1,11
	.byte	'X1D',0,1
	.word	669
	.byte	1,5,2,35,1,11
	.byte	'X1DEN',0,1
	.word	669
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	669
	.byte	4,0,2,35,1,11
	.byte	'OSCVAL',0,1
	.word	669
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	669
	.byte	2,1,2,35,2,11
	.byte	'APREN',0,1
	.word	669
	.byte	1,0,2,35,2,11
	.byte	'CAP0EN',0,1
	.word	669
	.byte	1,7,2,35,3,11
	.byte	'CAP1EN',0,1
	.word	669
	.byte	1,6,2,35,3,11
	.byte	'CAP2EN',0,1
	.word	669
	.byte	1,5,2,35,3,11
	.byte	'CAP3EN',0,1
	.word	669
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	669
	.byte	4,0,2,35,3,0,28
	.byte	'Ifx_SCU_OSCCON_Bits',0,4,209,5,3
	.word	28986
	.byte	10
	.byte	'_Ifx_SCU_OUT_Bits',0,4,212,5,16,4,11
	.byte	'P0',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	494
	.byte	30,0,2,35,0,0,28
	.byte	'Ifx_SCU_OUT_Bits',0,4,217,5,3
	.word	29398
	.byte	10
	.byte	'_Ifx_SCU_OVCCON_Bits',0,4,220,5,16,4,11
	.byte	'CSEL0',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'CSEL1',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'CSEL2',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,2
	.word	686
	.byte	13,0,2,35,0,11
	.byte	'OVSTRT',0,1
	.word	669
	.byte	1,7,2,35,2,11
	.byte	'OVSTP',0,1
	.word	669
	.byte	1,6,2,35,2,11
	.byte	'DCINVAL',0,1
	.word	669
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	669
	.byte	5,0,2,35,2,11
	.byte	'OVCONF',0,1
	.word	669
	.byte	1,7,2,35,3,11
	.byte	'POVCONF',0,1
	.word	669
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	669
	.byte	6,0,2,35,3,0,28
	.byte	'Ifx_SCU_OVCCON_Bits',0,4,233,5,3
	.word	29499
	.byte	10
	.byte	'_Ifx_SCU_OVCENABLE_Bits',0,4,236,5,16,4,11
	.byte	'OVEN0',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'OVEN1',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'OVEN2',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	494
	.byte	29,0,2,35,0,0,28
	.byte	'Ifx_SCU_OVCENABLE_Bits',0,4,242,5,3
	.word	29766
	.byte	10
	.byte	'_Ifx_SCU_PDISC_Bits',0,4,245,5,16,4,11
	.byte	'PDIS0',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	494
	.byte	30,0,2,35,0,0,28
	.byte	'Ifx_SCU_PDISC_Bits',0,4,250,5,3
	.word	29902
	.byte	10
	.byte	'_Ifx_SCU_PDR_Bits',0,4,253,5,16,4,11
	.byte	'PD0',0,1
	.word	669
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	669
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	669
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	494
	.byte	24,0,2,35,0,0,28
	.byte	'Ifx_SCU_PDR_Bits',0,4,132,6,3
	.word	30013
	.byte	10
	.byte	'_Ifx_SCU_PDRR_Bits',0,4,135,6,16,4,11
	.byte	'PDR0',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'PDR1',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'PDR2',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'PDR3',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'PDR4',0,1
	.word	669
	.byte	1,3,2,35,0,11
	.byte	'PDR5',0,1
	.word	669
	.byte	1,2,2,35,0,11
	.byte	'PDR6',0,1
	.word	669
	.byte	1,1,2,35,0,11
	.byte	'PDR7',0,1
	.word	669
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	494
	.byte	24,0,2,35,0,0,28
	.byte	'Ifx_SCU_PDRR_Bits',0,4,146,6,3
	.word	30146
	.byte	10
	.byte	'_Ifx_SCU_PLLCON0_Bits',0,4,149,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'MODEN',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	669
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	669
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	669
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	686
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	669
	.byte	7,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	669
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	669
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	669
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	669
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	669
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	669
	.byte	4,0,2,35,3,0,28
	.byte	'Ifx_SCU_PLLCON0_Bits',0,4,166,6,3
	.word	30349
	.byte	10
	.byte	'_Ifx_SCU_PLLCON1_Bits',0,4,169,6,16,4,11
	.byte	'K2DIV',0,1
	.word	669
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	669
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	669
	.byte	7,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	669
	.byte	1,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	669
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	686
	.byte	9,0,2,35,2,0,28
	.byte	'Ifx_SCU_PLLCON1_Bits',0,4,177,6,3
	.word	30705
	.byte	10
	.byte	'_Ifx_SCU_PLLCON2_Bits',0,4,180,6,16,4,11
	.byte	'MODCFG',0,2
	.word	686
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	686
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_SCU_PLLCON2_Bits',0,4,184,6,3
	.word	30883
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON0_Bits',0,4,187,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	669
	.byte	2,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	669
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	669
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	669
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	686
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	669
	.byte	5,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	669
	.byte	2,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	669
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	669
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	669
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	669
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	669
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	669
	.byte	4,0,2,35,3,0,28
	.byte	'Ifx_SCU_PLLERAYCON0_Bits',0,4,204,6,3
	.word	30983
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON1_Bits',0,4,207,6,16,4,11
	.byte	'K2DIV',0,1
	.word	669
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	669
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	669
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	669
	.byte	4,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	669
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	686
	.byte	9,0,2,35,2,0,28
	.byte	'Ifx_SCU_PLLERAYCON1_Bits',0,4,215,6,3
	.word	31353
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYSTAT_Bits',0,4,218,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'PWDSTAT',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	669
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	669
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	494
	.byte	26,0,2,35,0,0,28
	.byte	'Ifx_SCU_PLLERAYSTAT_Bits',0,4,227,6,3
	.word	31539
	.byte	10
	.byte	'_Ifx_SCU_PLLSTAT_Bits',0,4,230,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	669
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	669
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	669
	.byte	1,1,2,35,0,11
	.byte	'MODRUN',0,1
	.word	669
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	494
	.byte	24,0,2,35,0,0,28
	.byte	'Ifx_SCU_PLLSTAT_Bits',0,4,241,6,3
	.word	31737
	.byte	10
	.byte	'_Ifx_SCU_PMCSR_Bits',0,4,244,6,16,4,11
	.byte	'REQSLP',0,1
	.word	669
	.byte	2,6,2,35,0,11
	.byte	'SMUSLP',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	669
	.byte	5,0,2,35,0,11
	.byte	'PMST',0,1
	.word	669
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	494
	.byte	21,0,2,35,0,0,28
	.byte	'Ifx_SCU_PMCSR_Bits',0,4,251,6,3
	.word	31970
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR0_Bits',0,4,254,6,16,4,11
	.byte	'reserved_0',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'ESR1WKEN',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'PINAWKEN',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'PINBWKEN',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'ESR0DFEN',0,1
	.word	669
	.byte	1,3,2,35,0,11
	.byte	'ESR0EDCON',0,1
	.word	669
	.byte	2,1,2,35,0,11
	.byte	'ESR1DFEN',0,1
	.word	669
	.byte	1,0,2,35,0,11
	.byte	'ESR1EDCON',0,1
	.word	669
	.byte	2,6,2,35,1,11
	.byte	'PINADFEN',0,1
	.word	669
	.byte	1,5,2,35,1,11
	.byte	'PINAEDCON',0,1
	.word	669
	.byte	2,3,2,35,1,11
	.byte	'PINBDFEN',0,1
	.word	669
	.byte	1,2,2,35,1,11
	.byte	'PINBEDCON',0,1
	.word	669
	.byte	2,0,2,35,1,11
	.byte	'SCREN',0,1
	.word	669
	.byte	1,7,2,35,2,11
	.byte	'STBYRAMSEL',0,1
	.word	669
	.byte	2,5,2,35,2,11
	.byte	'SCRCLKSEL',0,1
	.word	669
	.byte	1,4,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	669
	.byte	1,3,2,35,2,11
	.byte	'TRISTEN',0,1
	.word	669
	.byte	1,2,2,35,2,11
	.byte	'TRISTREQ',0,1
	.word	669
	.byte	1,1,2,35,2,11
	.byte	'PORSTDF',0,1
	.word	669
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	669
	.byte	1,7,2,35,3,11
	.byte	'DCDCSYNC',0,1
	.word	669
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	669
	.byte	3,3,2,35,3,11
	.byte	'ESR0TRIST',0,1
	.word	669
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	669
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	669
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_PMSWCR0_Bits',0,4,153,7,3
	.word	32122
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR1_Bits',0,4,156,7,16,4,11
	.byte	'SCRSTEN',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'SCRSTREQ',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	669
	.byte	6,0,2,35,0,11
	.byte	'CPUIDLSEL',0,1
	.word	669
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	669
	.byte	1,4,2,35,1,11
	.byte	'IRADIS',0,1
	.word	669
	.byte	1,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	669
	.byte	3,0,2,35,1,11
	.byte	'SCRCFG',0,1
	.word	669
	.byte	8,0,2,35,2,11
	.byte	'CPUSEL',0,1
	.word	669
	.byte	3,5,2,35,3,11
	.byte	'STBYEVEN',0,1
	.word	669
	.byte	1,4,2,35,3,11
	.byte	'STBYEV',0,1
	.word	669
	.byte	3,1,2,35,3,11
	.byte	'LCK',0,1
	.word	669
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_PMSWCR1_Bits',0,4,170,7,3
	.word	32689
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR2_Bits',0,4,173,7,16,4,11
	.byte	'SCRINT',0,1
	.word	669
	.byte	8,0,2,35,0,11
	.byte	'BUSY',0,1
	.word	669
	.byte	1,7,2,35,1,11
	.byte	'SCRECC',0,1
	.word	669
	.byte	1,6,2,35,1,11
	.byte	'SCRWDT',0,1
	.word	669
	.byte	1,5,2,35,1,11
	.byte	'SCRRST',0,1
	.word	669
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	669
	.byte	4,0,2,35,1,11
	.byte	'TCINT',0,1
	.word	669
	.byte	8,0,2,35,2,11
	.byte	'TCINTREQ',0,1
	.word	669
	.byte	1,7,2,35,3,11
	.byte	'SMURST',0,1
	.word	669
	.byte	1,6,2,35,3,11
	.byte	'RST',0,1
	.word	669
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	669
	.byte	4,1,2,35,3,11
	.byte	'LCK',0,1
	.word	669
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_PMSWCR2_Bits',0,4,187,7,3
	.word	32983
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTAT_Bits',0,4,190,7,16,4,11
	.byte	'reserved_0',0,1
	.word	669
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKP',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUN',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'PINAWKP',0,1
	.word	669
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUN',0,1
	.word	669
	.byte	1,2,2,35,0,11
	.byte	'PINBWKP',0,1
	.word	669
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUN',0,1
	.word	669
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	669
	.byte	1,7,2,35,1,11
	.byte	'PORSTDF',0,1
	.word	669
	.byte	1,6,2,35,1,11
	.byte	'HWCFGEVR',0,1
	.word	669
	.byte	3,3,2,35,1,11
	.byte	'STBYRAM',0,1
	.word	669
	.byte	2,1,2,35,1,11
	.byte	'TRIST',0,1
	.word	669
	.byte	1,0,2,35,1,11
	.byte	'SCRST',0,1
	.word	669
	.byte	1,7,2,35,2,11
	.byte	'SCRWKP',0,1
	.word	669
	.byte	1,6,2,35,2,11
	.byte	'SCR',0,1
	.word	669
	.byte	1,5,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	669
	.byte	1,4,2,35,2,11
	.byte	'ESR1WKEN',0,1
	.word	669
	.byte	1,3,2,35,2,11
	.byte	'PINAWKEN',0,1
	.word	669
	.byte	1,2,2,35,2,11
	.byte	'PINBWKEN',0,1
	.word	669
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	686
	.byte	4,5,2,35,2,11
	.byte	'ESR0TRIST',0,1
	.word	669
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	669
	.byte	4,0,2,35,3,0,28
	.byte	'Ifx_SCU_PMSWSTAT_Bits',0,4,214,7,3
	.word	33261
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTATCLR_Bits',0,4,217,7,16,4,11
	.byte	'reserved_0',0,1
	.word	669
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKPCLR',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUNCLR',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'PINAWKPCLR',0,1
	.word	669
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUNCLR',0,1
	.word	669
	.byte	1,2,2,35,0,11
	.byte	'PINBWKPCLR',0,1
	.word	669
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUNCLR',0,1
	.word	669
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	669
	.byte	8,0,2,35,1,11
	.byte	'SCRSTCLR',0,1
	.word	669
	.byte	1,7,2,35,2,11
	.byte	'SCRWKPCLR',0,1
	.word	669
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	686
	.byte	14,0,2,35,2,0,28
	.byte	'Ifx_SCU_PMSWSTATCLR_Bits',0,4,230,7,3
	.word	33757
	.byte	10
	.byte	'_Ifx_SCU_RSTCON2_Bits',0,4,233,7,16,4,11
	.byte	'reserved_0',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'CLRC',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	686
	.byte	10,4,2,35,0,11
	.byte	'CSS0',0,1
	.word	669
	.byte	1,3,2,35,1,11
	.byte	'CSS1',0,1
	.word	669
	.byte	1,2,2,35,1,11
	.byte	'CSS2',0,1
	.word	669
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	669
	.byte	1,0,2,35,1,11
	.byte	'USRINFO',0,2
	.word	686
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_SCU_RSTCON2_Bits',0,4,243,7,3
	.word	34070
	.byte	10
	.byte	'_Ifx_SCU_RSTCON_Bits',0,4,246,7,16,4,11
	.byte	'ESR0',0,1
	.word	669
	.byte	2,6,2,35,0,11
	.byte	'ESR1',0,1
	.word	669
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	669
	.byte	2,2,2,35,0,11
	.byte	'SMU',0,1
	.word	669
	.byte	2,0,2,35,0,11
	.byte	'SW',0,1
	.word	669
	.byte	2,6,2,35,1,11
	.byte	'STM0',0,1
	.word	669
	.byte	2,4,2,35,1,11
	.byte	'STM1',0,1
	.word	669
	.byte	2,2,2,35,1,11
	.byte	'STM2',0,1
	.word	669
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	686
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_SCU_RSTCON_Bits',0,4,129,8,3
	.word	34279
	.byte	10
	.byte	'_Ifx_SCU_RSTSTAT_Bits',0,4,132,8,16,4,11
	.byte	'ESR0',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'ESR1',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'SMU',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'SW',0,1
	.word	669
	.byte	1,3,2,35,0,11
	.byte	'STM0',0,1
	.word	669
	.byte	1,2,2,35,0,11
	.byte	'STM1',0,1
	.word	669
	.byte	1,1,2,35,0,11
	.byte	'STM2',0,1
	.word	669
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	669
	.byte	8,0,2,35,1,11
	.byte	'PORST',0,1
	.word	669
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	669
	.byte	1,6,2,35,2,11
	.byte	'CB0',0,1
	.word	669
	.byte	1,5,2,35,2,11
	.byte	'CB1',0,1
	.word	669
	.byte	1,4,2,35,2,11
	.byte	'CB3',0,1
	.word	669
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	669
	.byte	2,1,2,35,2,11
	.byte	'EVR13',0,1
	.word	669
	.byte	1,0,2,35,2,11
	.byte	'EVR33',0,1
	.word	669
	.byte	1,7,2,35,3,11
	.byte	'SWD',0,1
	.word	669
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	669
	.byte	2,4,2,35,3,11
	.byte	'STBYR',0,1
	.word	669
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	669
	.byte	3,0,2,35,3,0,28
	.byte	'Ifx_SCU_RSTSTAT_Bits',0,4,155,8,3
	.word	34490
	.byte	10
	.byte	'_Ifx_SCU_SAFECON_Bits',0,4,158,8,16,4,11
	.byte	'HBT',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	494
	.byte	31,0,2,35,0,0,28
	.byte	'Ifx_SCU_SAFECON_Bits',0,4,162,8,3
	.word	34922
	.byte	10
	.byte	'_Ifx_SCU_STSTAT_Bits',0,4,165,8,16,4,11
	.byte	'HWCFG',0,1
	.word	669
	.byte	8,0,2,35,0,11
	.byte	'FTM',0,1
	.word	669
	.byte	7,1,2,35,1,11
	.byte	'MODE',0,1
	.word	669
	.byte	1,0,2,35,1,11
	.byte	'FCBAE',0,1
	.word	669
	.byte	1,7,2,35,2,11
	.byte	'LUDIS',0,1
	.word	669
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	669
	.byte	1,5,2,35,2,11
	.byte	'TRSTL',0,1
	.word	669
	.byte	1,4,2,35,2,11
	.byte	'SPDEN',0,1
	.word	669
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	669
	.byte	3,0,2,35,2,11
	.byte	'RAMINT',0,1
	.word	669
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	669
	.byte	7,0,2,35,3,0,28
	.byte	'Ifx_SCU_STSTAT_Bits',0,4,178,8,3
	.word	35018
	.byte	10
	.byte	'_Ifx_SCU_SWRSTCON_Bits',0,4,181,8,16,4,11
	.byte	'reserved_0',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'SWRSTREQ',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	494
	.byte	30,0,2,35,0,0,28
	.byte	'Ifx_SCU_SWRSTCON_Bits',0,4,186,8,3
	.word	35278
	.byte	10
	.byte	'_Ifx_SCU_SYSCON_Bits',0,4,189,8,16,4,11
	.byte	'CCTRIG0',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'RAMINTM',0,1
	.word	669
	.byte	2,4,2,35,0,11
	.byte	'SETLUDIS',0,1
	.word	669
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	669
	.byte	3,0,2,35,0,11
	.byte	'DATM',0,1
	.word	669
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,4
	.word	494
	.byte	23,0,2,35,0,0,28
	.byte	'Ifx_SCU_SYSCON_Bits',0,4,198,8,3
	.word	35403
	.byte	10
	.byte	'_Ifx_SCU_TRAPCLR_Bits',0,4,201,8,16,4,11
	.byte	'ESR0T',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	494
	.byte	28,0,2,35,0,0,28
	.byte	'Ifx_SCU_TRAPCLR_Bits',0,4,208,8,3
	.word	35600
	.byte	10
	.byte	'_Ifx_SCU_TRAPDIS_Bits',0,4,211,8,16,4,11
	.byte	'ESR0T',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	494
	.byte	28,0,2,35,0,0,28
	.byte	'Ifx_SCU_TRAPDIS_Bits',0,4,218,8,3
	.word	35753
	.byte	10
	.byte	'_Ifx_SCU_TRAPSET_Bits',0,4,221,8,16,4,11
	.byte	'ESR0T',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	494
	.byte	28,0,2,35,0,0,28
	.byte	'Ifx_SCU_TRAPSET_Bits',0,4,228,8,3
	.word	35906
	.byte	10
	.byte	'_Ifx_SCU_TRAPSTAT_Bits',0,4,231,8,16,4,11
	.byte	'ESR0T',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	494
	.byte	28,0,2,35,0,0,28
	.byte	'Ifx_SCU_TRAPSTAT_Bits',0,4,238,8,3
	.word	36059
	.byte	28
	.byte	'Ifx_SCU_WDTCPU_CON0_Bits',0,4,247,8,3
	.word	533
	.byte	28
	.byte	'Ifx_SCU_WDTCPU_CON1_Bits',0,4,134,9,3
	.word	708
	.byte	28
	.byte	'Ifx_SCU_WDTCPU_SR_Bits',0,4,150,9,3
	.word	952
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON0_Bits',0,4,153,9,16,4,11
	.byte	'ENDINIT',0,4
	.word	517
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	517
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	517
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	517
	.byte	16,0,2,35,0,0,28
	.byte	'Ifx_SCU_WDTS_CON0_Bits',0,4,159,9,3
	.word	36314
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON1_Bits',0,4,162,9,16,4,11
	.byte	'CLRIRF',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'IR0',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	669
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	669
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	669
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	669
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	669
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	669
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	686
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_SCU_WDTS_CON1_Bits',0,4,175,9,3
	.word	36440
	.byte	10
	.byte	'_Ifx_SCU_WDTS_SR_Bits',0,4,178,9,16,4,11
	.byte	'AE',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	669
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	669
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	669
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	669
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	669
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	669
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	686
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_SCU_WDTS_SR_Bits',0,4,191,9,3
	.word	36692
	.byte	12,4,199,9,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18724
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_ACCEN0',0,4,204,9,3
	.word	36911
	.byte	12,4,207,9,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19281
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_ACCEN1',0,4,212,9,3
	.word	36975
	.byte	12,4,215,9,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19358
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_ARSTDIS',0,4,220,9,3
	.word	37039
	.byte	12,4,223,9,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19494
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_CCUCON0',0,4,228,9,3
	.word	37104
	.byte	12,4,231,9,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19774
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_CCUCON1',0,4,236,9,3
	.word	37169
	.byte	12,4,239,9,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20012
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_CCUCON2',0,4,244,9,3
	.word	37234
	.byte	12,4,247,9,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20140
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_CCUCON3',0,4,252,9,3
	.word	37299
	.byte	12,4,255,9,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20383
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_CCUCON4',0,4,132,10,3
	.word	37364
	.byte	12,4,135,10,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20618
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_CCUCON5',0,4,140,10,3
	.word	37429
	.byte	12,4,143,10,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20746
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_CCUCON6',0,4,148,10,3
	.word	37494
	.byte	12,4,151,10,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20846
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_CCUCON7',0,4,156,10,3
	.word	37559
	.byte	12,4,159,10,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20946
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_CHIPID',0,4,164,10,3
	.word	37624
	.byte	12,4,167,10,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21154
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_DTSCON',0,4,172,10,3
	.word	37688
	.byte	12,4,175,10,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21319
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_DTSLIM',0,4,180,10,3
	.word	37752
	.byte	12,4,183,10,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21502
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_DTSSTAT',0,4,188,10,3
	.word	37816
	.byte	12,4,191,10,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21656
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EICR',0,4,196,10,3
	.word	37881
	.byte	12,4,199,10,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22020
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EIFR',0,4,204,10,3
	.word	37943
	.byte	12,4,207,10,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22231
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EMSR',0,4,212,10,3
	.word	38005
	.byte	12,4,215,10,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22483
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_ESRCFG',0,4,220,10,3
	.word	38067
	.byte	12,4,223,10,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22601
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_ESROCFG',0,4,228,10,3
	.word	38131
	.byte	12,4,231,10,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22712
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVR13CON',0,4,236,10,3
	.word	38196
	.byte	12,4,239,10,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22875
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVR33CON',0,4,244,10,3
	.word	38262
	.byte	12,4,247,10,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23038
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRADCSTAT',0,4,252,10,3
	.word	38328
	.byte	12,4,255,10,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23196
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRDVSTAT',0,4,132,11,3
	.word	38396
	.byte	12,4,135,11,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23361
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRMONCTRL',0,4,140,11,3
	.word	38463
	.byte	12,4,143,11,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23690
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVROSCCTRL',0,4,148,11,3
	.word	38531
	.byte	12,4,151,11,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23911
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVROVMON',0,4,156,11,3
	.word	38599
	.byte	12,4,159,11,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24074
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRRSTCON',0,4,164,11,3
	.word	38665
	.byte	12,4,167,11,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24346
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRSDCOEFF1',0,4,172,11,3
	.word	38732
	.byte	12,4,175,11,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24499
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRSDCOEFF2',0,4,180,11,3
	.word	38801
	.byte	12,4,183,11,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24655
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRSDCOEFF3',0,4,188,11,3
	.word	38870
	.byte	12,4,191,11,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24817
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRSDCOEFF4',0,4,196,11,3
	.word	38939
	.byte	12,4,199,11,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24960
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRSDCOEFF5',0,4,204,11,3
	.word	39008
	.byte	12,4,207,11,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25125
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRSDCOEFF6',0,4,212,11,3
	.word	39077
	.byte	12,4,215,11,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25270
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRSDCTRL1',0,4,220,11,3
	.word	39146
	.byte	12,4,223,11,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25451
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRSDCTRL2',0,4,228,11,3
	.word	39214
	.byte	12,4,231,11,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25625
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRSDCTRL3',0,4,236,11,3
	.word	39282
	.byte	12,4,239,11,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25785
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRSDCTRL4',0,4,244,11,3
	.word	39350
	.byte	12,4,247,11,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25929
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRSTAT',0,4,252,11,3
	.word	39418
	.byte	12,4,255,11,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26203
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRTRIM',0,4,132,12,3
	.word	39483
	.byte	12,4,135,12,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26342
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRUVMON',0,4,140,12,3
	.word	39548
	.byte	12,4,143,12,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26505
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EXTCON',0,4,148,12,3
	.word	39614
	.byte	12,4,151,12,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26723
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_FDR',0,4,156,12,3
	.word	39678
	.byte	12,4,159,12,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26886
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_FMR',0,4,164,12,3
	.word	39739
	.byte	12,4,167,12,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27222
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_ID',0,4,172,12,3
	.word	39800
	.byte	12,4,175,12,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27329
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_IGCR',0,4,180,12,3
	.word	39860
	.byte	12,4,183,12,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27781
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_IN',0,4,188,12,3
	.word	39922
	.byte	12,4,191,12,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27880
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_IOCR',0,4,196,12,3
	.word	39982
	.byte	12,4,199,12,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28030
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_LBISTCTRL0',0,4,204,12,3
	.word	40044
	.byte	12,4,207,12,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28179
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_LBISTCTRL1',0,4,212,12,3
	.word	40112
	.byte	12,4,215,12,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28340
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_LBISTCTRL2',0,4,220,12,3
	.word	40180
	.byte	12,4,223,12,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28470
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_LCLCON',0,4,228,12,3
	.word	40248
	.byte	12,4,231,12,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28602
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_LCLTEST',0,4,236,12,3
	.word	40312
	.byte	12,4,239,12,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28717
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_MANID',0,4,244,12,3
	.word	40377
	.byte	12,4,247,12,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28828
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_OMR',0,4,252,12,3
	.word	40440
	.byte	12,4,255,12,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28986
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_OSCCON',0,4,132,13,3
	.word	40501
	.byte	12,4,135,13,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29398
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_OUT',0,4,140,13,3
	.word	40565
	.byte	12,4,143,13,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29499
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_OVCCON',0,4,148,13,3
	.word	40626
	.byte	12,4,151,13,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29766
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_OVCENABLE',0,4,156,13,3
	.word	40690
	.byte	12,4,159,13,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29902
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PDISC',0,4,164,13,3
	.word	40757
	.byte	12,4,167,13,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30013
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PDR',0,4,172,13,3
	.word	40820
	.byte	12,4,175,13,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30146
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PDRR',0,4,180,13,3
	.word	40881
	.byte	12,4,183,13,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30349
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PLLCON0',0,4,188,13,3
	.word	40943
	.byte	12,4,191,13,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30705
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PLLCON1',0,4,196,13,3
	.word	41008
	.byte	12,4,199,13,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30883
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PLLCON2',0,4,204,13,3
	.word	41073
	.byte	12,4,207,13,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30983
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PLLERAYCON0',0,4,212,13,3
	.word	41138
	.byte	12,4,215,13,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31353
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PLLERAYCON1',0,4,220,13,3
	.word	41207
	.byte	12,4,223,13,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31539
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PLLERAYSTAT',0,4,228,13,3
	.word	41276
	.byte	12,4,231,13,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31737
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PLLSTAT',0,4,236,13,3
	.word	41345
	.byte	12,4,239,13,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31970
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PMCSR',0,4,244,13,3
	.word	41410
	.byte	12,4,247,13,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32122
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PMSWCR0',0,4,252,13,3
	.word	41473
	.byte	12,4,255,13,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32689
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PMSWCR1',0,4,132,14,3
	.word	41538
	.byte	12,4,135,14,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32983
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PMSWCR2',0,4,140,14,3
	.word	41603
	.byte	12,4,143,14,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33261
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PMSWSTAT',0,4,148,14,3
	.word	41668
	.byte	12,4,151,14,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33757
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PMSWSTATCLR',0,4,156,14,3
	.word	41734
	.byte	12,4,159,14,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34279
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_RSTCON',0,4,164,14,3
	.word	41803
	.byte	12,4,167,14,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34070
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_RSTCON2',0,4,172,14,3
	.word	41867
	.byte	12,4,175,14,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34490
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_RSTSTAT',0,4,180,14,3
	.word	41932
	.byte	12,4,183,14,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34922
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_SAFECON',0,4,188,14,3
	.word	41997
	.byte	12,4,191,14,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35018
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_STSTAT',0,4,196,14,3
	.word	42062
	.byte	12,4,199,14,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35278
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_SWRSTCON',0,4,204,14,3
	.word	42126
	.byte	12,4,207,14,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35403
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_SYSCON',0,4,212,14,3
	.word	42192
	.byte	12,4,215,14,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35600
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_TRAPCLR',0,4,220,14,3
	.word	42256
	.byte	12,4,223,14,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35753
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_TRAPDIS',0,4,228,14,3
	.word	42321
	.byte	12,4,231,14,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35906
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_TRAPSET',0,4,236,14,3
	.word	42386
	.byte	12,4,239,14,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36059
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_TRAPSTAT',0,4,244,14,3
	.word	42451
	.byte	28
	.byte	'Ifx_SCU_WDTCPU_CON0',0,4,252,14,3
	.word	629
	.byte	28
	.byte	'Ifx_SCU_WDTCPU_CON1',0,4,132,15,3
	.word	912
	.byte	28
	.byte	'Ifx_SCU_WDTCPU_SR',0,4,140,15,3
	.word	1143
	.byte	12,4,143,15,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36314
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_WDTS_CON0',0,4,148,15,3
	.word	42602
	.byte	12,4,151,15,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36440
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_WDTS_CON1',0,4,156,15,3
	.word	42669
	.byte	12,4,159,15,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36692
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_WDTS_SR',0,4,164,15,3
	.word	42736
	.byte	14
	.word	1183
	.byte	28
	.byte	'Ifx_SCU_WDTCPU',0,4,180,15,3
	.word	42801
	.byte	10
	.byte	'_Ifx_SCU_WDTS',0,4,183,15,25,12,13
	.byte	'CON0',0
	.word	42602
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	42669
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	42736
	.byte	4,2,35,8,0,14
	.word	42830
	.byte	28
	.byte	'Ifx_SCU_WDTS',0,4,188,15,3
	.word	42891
	.byte	18,8
	.word	38067
	.byte	19,1,0,18,20
	.word	669
	.byte	19,19,0,18,8
	.word	41410
	.byte	19,1,0,14
	.word	42830
	.byte	18,24
	.word	1183
	.byte	19,1,0,14
	.word	42950
	.byte	18,16
	.word	669
	.byte	19,15,0,18,28
	.word	669
	.byte	19,27,0,18,40
	.word	669
	.byte	19,39,0,18,16
	.word	37881
	.byte	19,3,0,18,16
	.word	39860
	.byte	19,3,0,18,180,3
	.word	669
	.byte	19,179,3,0,10
	.byte	'_Ifx_SCU',0,4,201,15,25,128,8,13
	.byte	'reserved_0',0
	.word	4692
	.byte	8,2,35,0,13
	.byte	'ID',0
	.word	39800
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	2873
	.byte	4,2,35,12,13
	.byte	'OSCCON',0
	.word	40501
	.byte	4,2,35,16,13
	.byte	'PLLSTAT',0
	.word	41345
	.byte	4,2,35,20,13
	.byte	'PLLCON0',0
	.word	40943
	.byte	4,2,35,24,13
	.byte	'PLLCON1',0
	.word	41008
	.byte	4,2,35,28,13
	.byte	'PLLCON2',0
	.word	41073
	.byte	4,2,35,32,13
	.byte	'PLLERAYSTAT',0
	.word	41276
	.byte	4,2,35,36,13
	.byte	'PLLERAYCON0',0
	.word	41138
	.byte	4,2,35,40,13
	.byte	'PLLERAYCON1',0
	.word	41207
	.byte	4,2,35,44,13
	.byte	'CCUCON0',0
	.word	37104
	.byte	4,2,35,48,13
	.byte	'CCUCON1',0
	.word	37169
	.byte	4,2,35,52,13
	.byte	'FDR',0
	.word	39678
	.byte	4,2,35,56,13
	.byte	'EXTCON',0
	.word	39614
	.byte	4,2,35,60,13
	.byte	'CCUCON2',0
	.word	37234
	.byte	4,2,35,64,13
	.byte	'CCUCON3',0
	.word	37299
	.byte	4,2,35,68,13
	.byte	'CCUCON4',0
	.word	37364
	.byte	4,2,35,72,13
	.byte	'CCUCON5',0
	.word	37429
	.byte	4,2,35,76,13
	.byte	'RSTSTAT',0
	.word	41932
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	2873
	.byte	4,2,35,84,13
	.byte	'RSTCON',0
	.word	41803
	.byte	4,2,35,88,13
	.byte	'ARSTDIS',0
	.word	37039
	.byte	4,2,35,92,13
	.byte	'SWRSTCON',0
	.word	42126
	.byte	4,2,35,96,13
	.byte	'RSTCON2',0
	.word	41867
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	2873
	.byte	4,2,35,104,13
	.byte	'EVRRSTCON',0
	.word	38665
	.byte	4,2,35,108,13
	.byte	'ESRCFG',0
	.word	42918
	.byte	8,2,35,112,13
	.byte	'ESROCFG',0
	.word	38131
	.byte	4,2,35,120,13
	.byte	'SYSCON',0
	.word	42192
	.byte	4,2,35,124,13
	.byte	'CCUCON6',0
	.word	37494
	.byte	4,3,35,128,1,13
	.byte	'CCUCON7',0
	.word	37559
	.byte	4,3,35,132,1,13
	.byte	'reserved_88',0
	.word	42927
	.byte	20,3,35,136,1,13
	.byte	'PDR',0
	.word	40820
	.byte	4,3,35,156,1,13
	.byte	'IOCR',0
	.word	39982
	.byte	4,3,35,160,1,13
	.byte	'OUT',0
	.word	40565
	.byte	4,3,35,164,1,13
	.byte	'OMR',0
	.word	40440
	.byte	4,3,35,168,1,13
	.byte	'IN',0
	.word	39922
	.byte	4,3,35,172,1,13
	.byte	'EVRSTAT',0
	.word	39418
	.byte	4,3,35,176,1,13
	.byte	'EVRDVSTAT',0
	.word	38396
	.byte	4,3,35,180,1,13
	.byte	'EVR13CON',0
	.word	38196
	.byte	4,3,35,184,1,13
	.byte	'EVR33CON',0
	.word	38262
	.byte	4,3,35,188,1,13
	.byte	'STSTAT',0
	.word	42062
	.byte	4,3,35,192,1,13
	.byte	'reserved_C4',0
	.word	2873
	.byte	4,3,35,196,1,13
	.byte	'PMSWCR0',0
	.word	41473
	.byte	4,3,35,200,1,13
	.byte	'PMSWSTAT',0
	.word	41668
	.byte	4,3,35,204,1,13
	.byte	'PMSWSTATCLR',0
	.word	41734
	.byte	4,3,35,208,1,13
	.byte	'PMCSR',0
	.word	42936
	.byte	8,3,35,212,1,13
	.byte	'reserved_DC',0
	.word	2873
	.byte	4,3,35,220,1,13
	.byte	'DTSSTAT',0
	.word	37816
	.byte	4,3,35,224,1,13
	.byte	'DTSCON',0
	.word	37688
	.byte	4,3,35,228,1,13
	.byte	'PMSWCR1',0
	.word	41538
	.byte	4,3,35,232,1,13
	.byte	'PMSWCR2',0
	.word	41603
	.byte	4,3,35,236,1,13
	.byte	'WDTS',0
	.word	42945
	.byte	12,3,35,240,1,13
	.byte	'EMSR',0
	.word	38005
	.byte	4,3,35,252,1,13
	.byte	'WDTCPU',0
	.word	42959
	.byte	24,3,35,128,2,13
	.byte	'reserved_118',0
	.word	5032
	.byte	12,3,35,152,2,13
	.byte	'TRAPSTAT',0
	.word	42451
	.byte	4,3,35,164,2,13
	.byte	'TRAPSET',0
	.word	42386
	.byte	4,3,35,168,2,13
	.byte	'TRAPCLR',0
	.word	42256
	.byte	4,3,35,172,2,13
	.byte	'TRAPDIS',0
	.word	42321
	.byte	4,3,35,176,2,13
	.byte	'reserved_134',0
	.word	2873
	.byte	4,3,35,180,2,13
	.byte	'LCLCON1',0
	.word	40248
	.byte	4,3,35,184,2,13
	.byte	'LCLTEST',0
	.word	40312
	.byte	4,3,35,188,2,13
	.byte	'CHIPID',0
	.word	37624
	.byte	4,3,35,192,2,13
	.byte	'MANID',0
	.word	40377
	.byte	4,3,35,196,2,13
	.byte	'reserved_148',0
	.word	4692
	.byte	8,3,35,200,2,13
	.byte	'SAFECON',0
	.word	41997
	.byte	4,3,35,208,2,13
	.byte	'reserved_154',0
	.word	42964
	.byte	16,3,35,212,2,13
	.byte	'LBISTCTRL0',0
	.word	40044
	.byte	4,3,35,228,2,13
	.byte	'LBISTCTRL1',0
	.word	40112
	.byte	4,3,35,232,2,13
	.byte	'LBISTCTRL2',0
	.word	40180
	.byte	4,3,35,236,2,13
	.byte	'reserved_170',0
	.word	42973
	.byte	28,3,35,240,2,13
	.byte	'PDISC',0
	.word	40757
	.byte	4,3,35,140,3,13
	.byte	'reserved_190',0
	.word	4692
	.byte	8,3,35,144,3,13
	.byte	'EVRTRIM',0
	.word	39483
	.byte	4,3,35,152,3,13
	.byte	'EVRADCSTAT',0
	.word	38328
	.byte	4,3,35,156,3,13
	.byte	'EVRUVMON',0
	.word	39548
	.byte	4,3,35,160,3,13
	.byte	'EVROVMON',0
	.word	38599
	.byte	4,3,35,164,3,13
	.byte	'EVRMONCTRL',0
	.word	38463
	.byte	4,3,35,168,3,13
	.byte	'reserved_1AC',0
	.word	2873
	.byte	4,3,35,172,3,13
	.byte	'EVRSDCTRL1',0
	.word	39146
	.byte	4,3,35,176,3,13
	.byte	'EVRSDCTRL2',0
	.word	39214
	.byte	4,3,35,180,3,13
	.byte	'EVRSDCTRL3',0
	.word	39282
	.byte	4,3,35,184,3,13
	.byte	'EVRSDCTRL4',0
	.word	39350
	.byte	4,3,35,188,3,13
	.byte	'EVRSDCOEFF1',0
	.word	38732
	.byte	4,3,35,192,3,13
	.byte	'EVRSDCOEFF2',0
	.word	38801
	.byte	4,3,35,196,3,13
	.byte	'EVRSDCOEFF3',0
	.word	38870
	.byte	4,3,35,200,3,13
	.byte	'EVRSDCOEFF4',0
	.word	38939
	.byte	4,3,35,204,3,13
	.byte	'EVRSDCOEFF5',0
	.word	39008
	.byte	4,3,35,208,3,13
	.byte	'EVRSDCOEFF6',0
	.word	39077
	.byte	4,3,35,212,3,13
	.byte	'EVROSCCTRL',0
	.word	38531
	.byte	4,3,35,216,3,13
	.byte	'reserved_1DC',0
	.word	2873
	.byte	4,3,35,220,3,13
	.byte	'OVCENABLE',0
	.word	40690
	.byte	4,3,35,224,3,13
	.byte	'OVCCON',0
	.word	40626
	.byte	4,3,35,228,3,13
	.byte	'reserved_1E8',0
	.word	42982
	.byte	40,3,35,232,3,13
	.byte	'EICR',0
	.word	42991
	.byte	16,3,35,144,4,13
	.byte	'EIFR',0
	.word	37943
	.byte	4,3,35,160,4,13
	.byte	'FMR',0
	.word	39739
	.byte	4,3,35,164,4,13
	.byte	'PDRR',0
	.word	40881
	.byte	4,3,35,168,4,13
	.byte	'IGCR',0
	.word	43000
	.byte	16,3,35,172,4,13
	.byte	'reserved_23C',0
	.word	2873
	.byte	4,3,35,188,4,13
	.byte	'DTSLIM',0
	.word	37752
	.byte	4,3,35,192,4,13
	.byte	'reserved_244',0
	.word	43009
	.byte	180,3,3,35,196,4,13
	.byte	'ACCEN1',0
	.word	36975
	.byte	4,3,35,248,7,13
	.byte	'ACCEN0',0
	.word	36911
	.byte	4,3,35,252,7,0,14
	.word	43020
	.byte	28
	.byte	'Ifx_SCU',0,4,181,16,3
	.word	45010
	.byte	10
	.byte	'_Ifx_CPU_A_Bits',0,20,45,16,4,11
	.byte	'ADDR',0,4
	.word	517
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_CPU_A_Bits',0,20,48,3
	.word	45032
	.byte	10
	.byte	'_Ifx_CPU_BIV_Bits',0,20,51,16,4,11
	.byte	'VSS',0,4
	.word	517
	.byte	1,31,2,35,0,11
	.byte	'BIV',0,4
	.word	517
	.byte	31,0,2,35,0,0,28
	.byte	'Ifx_CPU_BIV_Bits',0,20,55,3
	.word	45093
	.byte	10
	.byte	'_Ifx_CPU_BTV_Bits',0,20,58,16,4,11
	.byte	'reserved_0',0,4
	.word	517
	.byte	1,31,2,35,0,11
	.byte	'BTV',0,4
	.word	517
	.byte	31,0,2,35,0,0,28
	.byte	'Ifx_CPU_BTV_Bits',0,20,62,3
	.word	45172
	.byte	10
	.byte	'_Ifx_CPU_CCNT_Bits',0,20,65,16,4,11
	.byte	'CountValue',0,4
	.word	517
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	517
	.byte	1,0,2,35,0,0,28
	.byte	'Ifx_CPU_CCNT_Bits',0,20,69,3
	.word	45258
	.byte	10
	.byte	'_Ifx_CPU_CCTRL_Bits',0,20,72,16,4,11
	.byte	'CM',0,4
	.word	517
	.byte	1,31,2,35,0,11
	.byte	'CE',0,4
	.word	517
	.byte	1,30,2,35,0,11
	.byte	'M1',0,4
	.word	517
	.byte	3,27,2,35,0,11
	.byte	'M2',0,4
	.word	517
	.byte	3,24,2,35,0,11
	.byte	'M3',0,4
	.word	517
	.byte	3,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	517
	.byte	21,0,2,35,0,0,28
	.byte	'Ifx_CPU_CCTRL_Bits',0,20,80,3
	.word	45347
	.byte	10
	.byte	'_Ifx_CPU_COMPAT_Bits',0,20,83,16,4,11
	.byte	'reserved_0',0,4
	.word	517
	.byte	3,29,2,35,0,11
	.byte	'RM',0,4
	.word	517
	.byte	1,28,2,35,0,11
	.byte	'SP',0,4
	.word	517
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	517
	.byte	27,0,2,35,0,0,28
	.byte	'Ifx_CPU_COMPAT_Bits',0,20,89,3
	.word	45493
	.byte	10
	.byte	'_Ifx_CPU_CORE_ID_Bits',0,20,92,16,4,11
	.byte	'CORE_ID',0,4
	.word	517
	.byte	3,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	517
	.byte	29,0,2,35,0,0,28
	.byte	'Ifx_CPU_CORE_ID_Bits',0,20,96,3
	.word	45620
	.byte	10
	.byte	'_Ifx_CPU_CPR_L_Bits',0,20,99,16,4,11
	.byte	'reserved_0',0,4
	.word	517
	.byte	3,29,2,35,0,11
	.byte	'LOWBND',0,4
	.word	517
	.byte	29,0,2,35,0,0,28
	.byte	'Ifx_CPU_CPR_L_Bits',0,20,103,3
	.word	45718
	.byte	10
	.byte	'_Ifx_CPU_CPR_U_Bits',0,20,106,16,4,11
	.byte	'reserved_0',0,4
	.word	517
	.byte	3,29,2,35,0,11
	.byte	'UPPBND',0,4
	.word	517
	.byte	29,0,2,35,0,0,28
	.byte	'Ifx_CPU_CPR_U_Bits',0,20,110,3
	.word	45811
	.byte	10
	.byte	'_Ifx_CPU_CPU_ID_Bits',0,20,113,16,4,11
	.byte	'MODREV',0,4
	.word	517
	.byte	8,24,2,35,0,11
	.byte	'MOD_32B',0,4
	.word	517
	.byte	8,16,2,35,0,11
	.byte	'MOD',0,4
	.word	517
	.byte	16,0,2,35,0,0,28
	.byte	'Ifx_CPU_CPU_ID_Bits',0,20,118,3
	.word	45904
	.byte	10
	.byte	'_Ifx_CPU_CPXE_Bits',0,20,121,16,4,11
	.byte	'XE',0,4
	.word	517
	.byte	8,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	517
	.byte	24,0,2,35,0,0,28
	.byte	'Ifx_CPU_CPXE_Bits',0,20,125,3
	.word	46011
	.byte	10
	.byte	'_Ifx_CPU_CREVT_Bits',0,20,128,1,16,4,11
	.byte	'EVTA',0,4
	.word	517
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	517
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	517
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	517
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	517
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	517
	.byte	24,0,2,35,0,0,28
	.byte	'Ifx_CPU_CREVT_Bits',0,20,136,1,3
	.word	46098
	.byte	10
	.byte	'_Ifx_CPU_CUS_ID_Bits',0,20,139,1,16,4,11
	.byte	'CID',0,4
	.word	517
	.byte	3,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	517
	.byte	29,0,2,35,0,0,28
	.byte	'Ifx_CPU_CUS_ID_Bits',0,20,143,1,3
	.word	46252
	.byte	10
	.byte	'_Ifx_CPU_D_Bits',0,20,146,1,16,4,11
	.byte	'DATA',0,4
	.word	517
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_CPU_D_Bits',0,20,149,1,3
	.word	46346
	.byte	10
	.byte	'_Ifx_CPU_DATR_Bits',0,20,152,1,16,4,11
	.byte	'reserved_0',0,4
	.word	517
	.byte	3,29,2,35,0,11
	.byte	'SBE',0,4
	.word	517
	.byte	1,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	517
	.byte	5,23,2,35,0,11
	.byte	'CWE',0,4
	.word	517
	.byte	1,22,2,35,0,11
	.byte	'CFE',0,4
	.word	517
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	517
	.byte	3,18,2,35,0,11
	.byte	'SOE',0,4
	.word	517
	.byte	1,17,2,35,0,11
	.byte	'SME',0,4
	.word	517
	.byte	1,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	517
	.byte	16,0,2,35,0,0,28
	.byte	'Ifx_CPU_DATR_Bits',0,20,163,1,3
	.word	46409
	.byte	10
	.byte	'_Ifx_CPU_DBGSR_Bits',0,20,166,1,16,4,11
	.byte	'DE',0,4
	.word	517
	.byte	1,31,2,35,0,11
	.byte	'HALT',0,4
	.word	517
	.byte	2,29,2,35,0,11
	.byte	'SIH',0,4
	.word	517
	.byte	1,28,2,35,0,11
	.byte	'SUSP',0,4
	.word	517
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	517
	.byte	1,26,2,35,0,11
	.byte	'PREVSUSP',0,4
	.word	517
	.byte	1,25,2,35,0,11
	.byte	'PEVT',0,4
	.word	517
	.byte	1,24,2,35,0,11
	.byte	'EVTSRC',0,4
	.word	517
	.byte	5,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	517
	.byte	19,0,2,35,0,0,28
	.byte	'Ifx_CPU_DBGSR_Bits',0,20,177,1,3
	.word	46627
	.byte	10
	.byte	'_Ifx_CPU_DBGTCR_Bits',0,20,180,1,16,4,11
	.byte	'DTA',0,4
	.word	517
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	517
	.byte	31,0,2,35,0,0,28
	.byte	'Ifx_CPU_DBGTCR_Bits',0,20,184,1,3
	.word	46842
	.byte	10
	.byte	'_Ifx_CPU_DCON0_Bits',0,20,187,1,16,4,11
	.byte	'reserved_0',0,4
	.word	517
	.byte	1,31,2,35,0,11
	.byte	'DCBYP',0,4
	.word	517
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	517
	.byte	30,0,2,35,0,0,28
	.byte	'Ifx_CPU_DCON0_Bits',0,20,192,1,3
	.word	46936
	.byte	10
	.byte	'_Ifx_CPU_DCON2_Bits',0,20,195,1,16,4,11
	.byte	'DCACHE_SZE',0,4
	.word	517
	.byte	16,16,2,35,0,11
	.byte	'DSCRATCH_SZE',0,4
	.word	517
	.byte	16,0,2,35,0,0,28
	.byte	'Ifx_CPU_DCON2_Bits',0,20,199,1,3
	.word	47052
	.byte	10
	.byte	'_Ifx_CPU_DCX_Bits',0,20,202,1,16,4,11
	.byte	'reserved_0',0,4
	.word	517
	.byte	6,26,2,35,0,11
	.byte	'DCXValue',0,4
	.word	517
	.byte	26,0,2,35,0,0,28
	.byte	'Ifx_CPU_DCX_Bits',0,20,206,1,3
	.word	47153
	.byte	10
	.byte	'_Ifx_CPU_DEADD_Bits',0,20,209,1,16,4,11
	.byte	'ERROR_ADDRESS',0,4
	.word	517
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_CPU_DEADD_Bits',0,20,212,1,3
	.word	47246
	.byte	10
	.byte	'_Ifx_CPU_DIEAR_Bits',0,20,215,1,16,4,11
	.byte	'TA',0,4
	.word	517
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_CPU_DIEAR_Bits',0,20,218,1,3
	.word	47326
	.byte	10
	.byte	'_Ifx_CPU_DIETR_Bits',0,20,221,1,16,4,11
	.byte	'IED',0,4
	.word	517
	.byte	1,31,2,35,0,11
	.byte	'IE_T',0,4
	.word	517
	.byte	1,30,2,35,0,11
	.byte	'IE_C',0,4
	.word	517
	.byte	1,29,2,35,0,11
	.byte	'IE_S',0,4
	.word	517
	.byte	1,28,2,35,0,11
	.byte	'IE_BI',0,4
	.word	517
	.byte	1,27,2,35,0,11
	.byte	'E_INFO',0,4
	.word	517
	.byte	6,21,2,35,0,11
	.byte	'IE_DUAL',0,4
	.word	517
	.byte	1,20,2,35,0,11
	.byte	'IE_SP',0,4
	.word	517
	.byte	1,19,2,35,0,11
	.byte	'IE_BS',0,4
	.word	517
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	517
	.byte	18,0,2,35,0,0,28
	.byte	'Ifx_CPU_DIETR_Bits',0,20,233,1,3
	.word	47395
	.byte	10
	.byte	'_Ifx_CPU_DMS_Bits',0,20,236,1,16,4,11
	.byte	'reserved_0',0,4
	.word	517
	.byte	1,31,2,35,0,11
	.byte	'DMSValue',0,4
	.word	517
	.byte	31,0,2,35,0,0,28
	.byte	'Ifx_CPU_DMS_Bits',0,20,240,1,3
	.word	47624
	.byte	10
	.byte	'_Ifx_CPU_DPR_L_Bits',0,20,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	517
	.byte	3,29,2,35,0,11
	.byte	'LOWBND',0,4
	.word	517
	.byte	29,0,2,35,0,0,28
	.byte	'Ifx_CPU_DPR_L_Bits',0,20,247,1,3
	.word	47717
	.byte	10
	.byte	'_Ifx_CPU_DPR_U_Bits',0,20,250,1,16,4,11
	.byte	'reserved_0',0,4
	.word	517
	.byte	3,29,2,35,0,11
	.byte	'UPPBND',0,4
	.word	517
	.byte	29,0,2,35,0,0,28
	.byte	'Ifx_CPU_DPR_U_Bits',0,20,254,1,3
	.word	47812
	.byte	10
	.byte	'_Ifx_CPU_DPRE_Bits',0,20,129,2,16,4,11
	.byte	'RE',0,4
	.word	517
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	517
	.byte	16,0,2,35,0,0,28
	.byte	'Ifx_CPU_DPRE_Bits',0,20,133,2,3
	.word	47907
	.byte	10
	.byte	'_Ifx_CPU_DPWE_Bits',0,20,136,2,16,4,11
	.byte	'WE',0,4
	.word	517
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	517
	.byte	16,0,2,35,0,0,28
	.byte	'Ifx_CPU_DPWE_Bits',0,20,140,2,3
	.word	47997
	.byte	10
	.byte	'_Ifx_CPU_DSTR_Bits',0,20,143,2,16,4,11
	.byte	'SRE',0,4
	.word	517
	.byte	1,31,2,35,0,11
	.byte	'GAE',0,4
	.word	517
	.byte	1,30,2,35,0,11
	.byte	'LBE',0,4
	.word	517
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	517
	.byte	3,26,2,35,0,11
	.byte	'CRE',0,4
	.word	517
	.byte	1,25,2,35,0,11
	.byte	'reserved_7',0,4
	.word	517
	.byte	7,18,2,35,0,11
	.byte	'DTME',0,4
	.word	517
	.byte	1,17,2,35,0,11
	.byte	'LOE',0,4
	.word	517
	.byte	1,16,2,35,0,11
	.byte	'SDE',0,4
	.word	517
	.byte	1,15,2,35,0,11
	.byte	'SCE',0,4
	.word	517
	.byte	1,14,2,35,0,11
	.byte	'CAC',0,4
	.word	517
	.byte	1,13,2,35,0,11
	.byte	'MPE',0,4
	.word	517
	.byte	1,12,2,35,0,11
	.byte	'CLE',0,4
	.word	517
	.byte	1,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	517
	.byte	3,8,2,35,0,11
	.byte	'ALN',0,4
	.word	517
	.byte	1,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	517
	.byte	7,0,2,35,0,0,28
	.byte	'Ifx_CPU_DSTR_Bits',0,20,161,2,3
	.word	48087
	.byte	10
	.byte	'_Ifx_CPU_EXEVT_Bits',0,20,164,2,16,4,11
	.byte	'EVTA',0,4
	.word	517
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	517
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	517
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	517
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	517
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	517
	.byte	24,0,2,35,0,0,28
	.byte	'Ifx_CPU_EXEVT_Bits',0,20,172,2,3
	.word	48411
	.byte	10
	.byte	'_Ifx_CPU_FCX_Bits',0,20,175,2,16,4,11
	.byte	'FCXO',0,4
	.word	517
	.byte	16,16,2,35,0,11
	.byte	'FCXS',0,4
	.word	517
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	517
	.byte	12,0,2,35,0,0,28
	.byte	'Ifx_CPU_FCX_Bits',0,20,180,2,3
	.word	48565
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_CON_Bits',0,20,183,2,16,4,11
	.byte	'TST',0,4
	.word	517
	.byte	1,31,2,35,0,11
	.byte	'TCL',0,4
	.word	517
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	517
	.byte	6,24,2,35,0,11
	.byte	'RM',0,4
	.word	517
	.byte	2,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	517
	.byte	8,14,2,35,0,11
	.byte	'FXE',0,4
	.word	517
	.byte	1,13,2,35,0,11
	.byte	'FUE',0,4
	.word	517
	.byte	1,12,2,35,0,11
	.byte	'FZE',0,4
	.word	517
	.byte	1,11,2,35,0,11
	.byte	'FVE',0,4
	.word	517
	.byte	1,10,2,35,0,11
	.byte	'FIE',0,4
	.word	517
	.byte	1,9,2,35,0,11
	.byte	'reserved_23',0,4
	.word	517
	.byte	3,6,2,35,0,11
	.byte	'FX',0,4
	.word	517
	.byte	1,5,2,35,0,11
	.byte	'FU',0,4
	.word	517
	.byte	1,4,2,35,0,11
	.byte	'FZ',0,4
	.word	517
	.byte	1,3,2,35,0,11
	.byte	'FV',0,4
	.word	517
	.byte	1,2,2,35,0,11
	.byte	'FI',0,4
	.word	517
	.byte	1,1,2,35,0,11
	.byte	'reserved_31',0,4
	.word	517
	.byte	1,0,2,35,0,0,28
	.byte	'Ifx_CPU_FPU_TRAP_CON_Bits',0,20,202,2,3
	.word	48671
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_OPC_Bits',0,20,205,2,16,4,11
	.byte	'OPC',0,4
	.word	517
	.byte	8,24,2,35,0,11
	.byte	'FMT',0,4
	.word	517
	.byte	1,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	517
	.byte	7,16,2,35,0,11
	.byte	'DREG',0,4
	.word	517
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	517
	.byte	12,0,2,35,0,0,28
	.byte	'Ifx_CPU_FPU_TRAP_OPC_Bits',0,20,212,2,3
	.word	49020
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_PC_Bits',0,20,215,2,16,4,11
	.byte	'PC',0,4
	.word	517
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_CPU_FPU_TRAP_PC_Bits',0,20,218,2,3
	.word	49180
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC1_Bits',0,20,221,2,16,4,11
	.byte	'SRC1',0,4
	.word	517
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_CPU_FPU_TRAP_SRC1_Bits',0,20,224,2,3
	.word	49261
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC2_Bits',0,20,227,2,16,4,11
	.byte	'SRC2',0,4
	.word	517
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_CPU_FPU_TRAP_SRC2_Bits',0,20,230,2,3
	.word	49348
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC3_Bits',0,20,233,2,16,4,11
	.byte	'SRC3',0,4
	.word	517
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_CPU_FPU_TRAP_SRC3_Bits',0,20,236,2,3
	.word	49435
	.byte	10
	.byte	'_Ifx_CPU_ICNT_Bits',0,20,239,2,16,4,11
	.byte	'CountValue',0,4
	.word	517
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	517
	.byte	1,0,2,35,0,0,28
	.byte	'Ifx_CPU_ICNT_Bits',0,20,243,2,3
	.word	49522
	.byte	10
	.byte	'_Ifx_CPU_ICR_Bits',0,20,246,2,16,4,11
	.byte	'CCPN',0,4
	.word	517
	.byte	10,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	517
	.byte	5,17,2,35,0,11
	.byte	'IE',0,4
	.word	517
	.byte	1,16,2,35,0,11
	.byte	'PIPN',0,4
	.word	517
	.byte	10,6,2,35,0,11
	.byte	'reserved_26',0,4
	.word	517
	.byte	6,0,2,35,0,0,28
	.byte	'Ifx_CPU_ICR_Bits',0,20,253,2,3
	.word	49613
	.byte	10
	.byte	'_Ifx_CPU_ISP_Bits',0,20,128,3,16,4,11
	.byte	'ISP',0,4
	.word	517
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_CPU_ISP_Bits',0,20,131,3,3
	.word	49756
	.byte	10
	.byte	'_Ifx_CPU_LCX_Bits',0,20,134,3,16,4,11
	.byte	'LCXO',0,4
	.word	517
	.byte	16,16,2,35,0,11
	.byte	'LCXS',0,4
	.word	517
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	517
	.byte	12,0,2,35,0,0,28
	.byte	'Ifx_CPU_LCX_Bits',0,20,139,3,3
	.word	49822
	.byte	10
	.byte	'_Ifx_CPU_M1CNT_Bits',0,20,142,3,16,4,11
	.byte	'CountValue',0,4
	.word	517
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	517
	.byte	1,0,2,35,0,0,28
	.byte	'Ifx_CPU_M1CNT_Bits',0,20,146,3,3
	.word	49928
	.byte	10
	.byte	'_Ifx_CPU_M2CNT_Bits',0,20,149,3,16,4,11
	.byte	'CountValue',0,4
	.word	517
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	517
	.byte	1,0,2,35,0,0,28
	.byte	'Ifx_CPU_M2CNT_Bits',0,20,153,3,3
	.word	50021
	.byte	10
	.byte	'_Ifx_CPU_M3CNT_Bits',0,20,156,3,16,4,11
	.byte	'CountValue',0,4
	.word	517
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	517
	.byte	1,0,2,35,0,0,28
	.byte	'Ifx_CPU_M3CNT_Bits',0,20,160,3,3
	.word	50114
	.byte	10
	.byte	'_Ifx_CPU_PC_Bits',0,20,163,3,16,4,11
	.byte	'reserved_0',0,4
	.word	517
	.byte	1,31,2,35,0,11
	.byte	'PC',0,4
	.word	517
	.byte	31,0,2,35,0,0,28
	.byte	'Ifx_CPU_PC_Bits',0,20,167,3,3
	.word	50207
	.byte	10
	.byte	'_Ifx_CPU_PCON0_Bits',0,20,170,3,16,4,11
	.byte	'reserved_0',0,4
	.word	517
	.byte	1,31,2,35,0,11
	.byte	'PCBYP',0,4
	.word	517
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	517
	.byte	30,0,2,35,0,0,28
	.byte	'Ifx_CPU_PCON0_Bits',0,20,175,3,3
	.word	50292
	.byte	10
	.byte	'_Ifx_CPU_PCON1_Bits',0,20,178,3,16,4,11
	.byte	'PCINV',0,4
	.word	517
	.byte	1,31,2,35,0,11
	.byte	'PBINV',0,4
	.word	517
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	517
	.byte	30,0,2,35,0,0,28
	.byte	'Ifx_CPU_PCON1_Bits',0,20,183,3,3
	.word	50408
	.byte	10
	.byte	'_Ifx_CPU_PCON2_Bits',0,20,186,3,16,4,11
	.byte	'PCACHE_SZE',0,4
	.word	517
	.byte	16,16,2,35,0,11
	.byte	'PSCRATCH_SZE',0,4
	.word	517
	.byte	16,0,2,35,0,0,28
	.byte	'Ifx_CPU_PCON2_Bits',0,20,190,3,3
	.word	50519
	.byte	10
	.byte	'_Ifx_CPU_PCXI_Bits',0,20,193,3,16,4,11
	.byte	'PCXO',0,4
	.word	517
	.byte	16,16,2,35,0,11
	.byte	'PCXS',0,4
	.word	517
	.byte	4,12,2,35,0,11
	.byte	'UL',0,4
	.word	517
	.byte	1,11,2,35,0,11
	.byte	'PIE',0,4
	.word	517
	.byte	1,10,2,35,0,11
	.byte	'PCPN',0,4
	.word	517
	.byte	10,0,2,35,0,0,28
	.byte	'Ifx_CPU_PCXI_Bits',0,20,200,3,3
	.word	50620
	.byte	10
	.byte	'_Ifx_CPU_PIEAR_Bits',0,20,203,3,16,4,11
	.byte	'TA',0,4
	.word	517
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_CPU_PIEAR_Bits',0,20,206,3,3
	.word	50750
	.byte	10
	.byte	'_Ifx_CPU_PIETR_Bits',0,20,209,3,16,4,11
	.byte	'IED',0,4
	.word	517
	.byte	1,31,2,35,0,11
	.byte	'IE_T',0,4
	.word	517
	.byte	1,30,2,35,0,11
	.byte	'IE_C',0,4
	.word	517
	.byte	1,29,2,35,0,11
	.byte	'IE_S',0,4
	.word	517
	.byte	1,28,2,35,0,11
	.byte	'IE_BI',0,4
	.word	517
	.byte	1,27,2,35,0,11
	.byte	'E_INFO',0,4
	.word	517
	.byte	6,21,2,35,0,11
	.byte	'IE_DUAL',0,4
	.word	517
	.byte	1,20,2,35,0,11
	.byte	'IE_SP',0,4
	.word	517
	.byte	1,19,2,35,0,11
	.byte	'IE_BS',0,4
	.word	517
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	517
	.byte	18,0,2,35,0,0,28
	.byte	'Ifx_CPU_PIETR_Bits',0,20,221,3,3
	.word	50819
	.byte	10
	.byte	'_Ifx_CPU_PMA0_Bits',0,20,224,3,16,4,11
	.byte	'reserved_0',0,4
	.word	517
	.byte	13,19,2,35,0,11
	.byte	'DAC',0,4
	.word	517
	.byte	3,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	517
	.byte	16,0,2,35,0,0,28
	.byte	'Ifx_CPU_PMA0_Bits',0,20,229,3,3
	.word	51048
	.byte	10
	.byte	'_Ifx_CPU_PMA1_Bits',0,20,232,3,16,4,11
	.byte	'reserved_0',0,4
	.word	517
	.byte	14,18,2,35,0,11
	.byte	'CAC',0,4
	.word	517
	.byte	2,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	517
	.byte	16,0,2,35,0,0,28
	.byte	'Ifx_CPU_PMA1_Bits',0,20,237,3,3
	.word	51161
	.byte	10
	.byte	'_Ifx_CPU_PMA2_Bits',0,20,240,3,16,4,11
	.byte	'PSI',0,4
	.word	517
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	517
	.byte	16,0,2,35,0,0,28
	.byte	'Ifx_CPU_PMA2_Bits',0,20,244,3,3
	.word	51274
	.byte	10
	.byte	'_Ifx_CPU_PSTR_Bits',0,20,247,3,16,4,11
	.byte	'FRE',0,4
	.word	517
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	517
	.byte	1,30,2,35,0,11
	.byte	'FBE',0,4
	.word	517
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	517
	.byte	9,20,2,35,0,11
	.byte	'FPE',0,4
	.word	517
	.byte	1,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	517
	.byte	1,18,2,35,0,11
	.byte	'FME',0,4
	.word	517
	.byte	1,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	517
	.byte	17,0,2,35,0,0,28
	.byte	'Ifx_CPU_PSTR_Bits',0,20,129,4,3
	.word	51365
	.byte	10
	.byte	'_Ifx_CPU_PSW_Bits',0,20,132,4,16,4,11
	.byte	'CDC',0,4
	.word	517
	.byte	7,25,2,35,0,11
	.byte	'CDE',0,4
	.word	517
	.byte	1,24,2,35,0,11
	.byte	'GW',0,4
	.word	517
	.byte	1,23,2,35,0,11
	.byte	'IS',0,4
	.word	517
	.byte	1,22,2,35,0,11
	.byte	'IO',0,4
	.word	517
	.byte	2,20,2,35,0,11
	.byte	'PRS',0,4
	.word	517
	.byte	2,18,2,35,0,11
	.byte	'S',0,4
	.word	517
	.byte	1,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	517
	.byte	12,5,2,35,0,11
	.byte	'SAV',0,4
	.word	517
	.byte	1,4,2,35,0,11
	.byte	'AV',0,4
	.word	517
	.byte	1,3,2,35,0,11
	.byte	'SV',0,4
	.word	517
	.byte	1,2,2,35,0,11
	.byte	'V',0,4
	.word	517
	.byte	1,1,2,35,0,11
	.byte	'C',0,4
	.word	517
	.byte	1,0,2,35,0,0,28
	.byte	'Ifx_CPU_PSW_Bits',0,20,147,4,3
	.word	51568
	.byte	10
	.byte	'_Ifx_CPU_SEGEN_Bits',0,20,150,4,16,4,11
	.byte	'ADFLIP',0,4
	.word	517
	.byte	8,24,2,35,0,11
	.byte	'ADTYPE',0,4
	.word	517
	.byte	2,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	517
	.byte	21,1,2,35,0,11
	.byte	'AE',0,4
	.word	517
	.byte	1,0,2,35,0,0,28
	.byte	'Ifx_CPU_SEGEN_Bits',0,20,156,4,3
	.word	51811
	.byte	10
	.byte	'_Ifx_CPU_SMACON_Bits',0,20,159,4,16,4,11
	.byte	'PC',0,4
	.word	517
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	517
	.byte	1,30,2,35,0,11
	.byte	'PT',0,4
	.word	517
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	517
	.byte	5,24,2,35,0,11
	.byte	'DC',0,4
	.word	517
	.byte	1,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	517
	.byte	1,22,2,35,0,11
	.byte	'DT',0,4
	.word	517
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	517
	.byte	13,8,2,35,0,11
	.byte	'IODT',0,4
	.word	517
	.byte	1,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	517
	.byte	7,0,2,35,0,0,28
	.byte	'Ifx_CPU_SMACON_Bits',0,20,171,4,3
	.word	51939
	.byte	10
	.byte	'_Ifx_CPU_SPROT_ACCENA_Bits',0,20,174,4,16,4,11
	.byte	'EN',0,4
	.word	494
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_CPU_SPROT_ACCENA_Bits',0,20,177,4,3
	.word	52180
	.byte	10
	.byte	'_Ifx_CPU_SPROT_ACCENB_Bits',0,20,180,4,16,4,11
	.byte	'reserved_0',0,4
	.word	494
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_CPU_SPROT_ACCENB_Bits',0,20,183,4,3
	.word	52263
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,20,186,4,16,4,11
	.byte	'EN',0,4
	.word	494
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,20,189,4,3
	.word	52354
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,20,192,4,16,4,11
	.byte	'reserved_0',0,4
	.word	494
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,20,195,4,3
	.word	52445
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_LA_Bits',0,20,198,4,16,4,11
	.byte	'reserved_0',0,1
	.word	669
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	494
	.byte	27,0,2,35,0,0,28
	.byte	'Ifx_CPU_SPROT_RGN_LA_Bits',0,20,202,4,3
	.word	52544
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_UA_Bits',0,20,205,4,16,4,11
	.byte	'reserved_0',0,1
	.word	669
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	494
	.byte	27,0,2,35,0,0,28
	.byte	'Ifx_CPU_SPROT_RGN_UA_Bits',0,20,209,4,3
	.word	52651
	.byte	10
	.byte	'_Ifx_CPU_SWEVT_Bits',0,20,212,4,16,4,11
	.byte	'EVTA',0,4
	.word	517
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	517
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	517
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	517
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	517
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	517
	.byte	24,0,2,35,0,0,28
	.byte	'Ifx_CPU_SWEVT_Bits',0,20,220,4,3
	.word	52758
	.byte	10
	.byte	'_Ifx_CPU_SYSCON_Bits',0,20,223,4,16,4,11
	.byte	'FCDSF',0,4
	.word	517
	.byte	1,31,2,35,0,11
	.byte	'PROTEN',0,4
	.word	517
	.byte	1,30,2,35,0,11
	.byte	'TPROTEN',0,4
	.word	517
	.byte	1,29,2,35,0,11
	.byte	'IS',0,4
	.word	517
	.byte	1,28,2,35,0,11
	.byte	'IT',0,4
	.word	517
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	517
	.byte	27,0,2,35,0,0,28
	.byte	'Ifx_CPU_SYSCON_Bits',0,20,231,4,3
	.word	52912
	.byte	10
	.byte	'_Ifx_CPU_TASK_ASI_Bits',0,20,234,4,16,4,11
	.byte	'ASI',0,4
	.word	517
	.byte	5,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	517
	.byte	27,0,2,35,0,0,28
	.byte	'Ifx_CPU_TASK_ASI_Bits',0,20,238,4,3
	.word	53073
	.byte	10
	.byte	'_Ifx_CPU_TPS_CON_Bits',0,20,241,4,16,4,11
	.byte	'TEXP0',0,4
	.word	517
	.byte	1,31,2,35,0,11
	.byte	'TEXP1',0,4
	.word	517
	.byte	1,30,2,35,0,11
	.byte	'TEXP2',0,4
	.word	517
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	517
	.byte	13,16,2,35,0,11
	.byte	'TTRAP',0,4
	.word	517
	.byte	1,15,2,35,0,11
	.byte	'reserved_17',0,4
	.word	517
	.byte	15,0,2,35,0,0,28
	.byte	'Ifx_CPU_TPS_CON_Bits',0,20,249,4,3
	.word	53171
	.byte	10
	.byte	'_Ifx_CPU_TPS_TIMER_Bits',0,20,252,4,16,4,11
	.byte	'Timer',0,4
	.word	517
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_CPU_TPS_TIMER_Bits',0,20,255,4,3
	.word	53343
	.byte	10
	.byte	'_Ifx_CPU_TR_ADR_Bits',0,20,130,5,16,4,11
	.byte	'ADDR',0,4
	.word	517
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_CPU_TR_ADR_Bits',0,20,133,5,3
	.word	53423
	.byte	10
	.byte	'_Ifx_CPU_TR_EVT_Bits',0,20,136,5,16,4,11
	.byte	'EVTA',0,4
	.word	517
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	517
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	517
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	517
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	517
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	517
	.byte	4,20,2,35,0,11
	.byte	'TYP',0,4
	.word	517
	.byte	1,19,2,35,0,11
	.byte	'RNG',0,4
	.word	517
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	517
	.byte	1,17,2,35,0,11
	.byte	'ASI_EN',0,4
	.word	517
	.byte	1,16,2,35,0,11
	.byte	'ASI',0,4
	.word	517
	.byte	5,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	517
	.byte	6,5,2,35,0,11
	.byte	'AST',0,4
	.word	517
	.byte	1,4,2,35,0,11
	.byte	'ALD',0,4
	.word	517
	.byte	1,3,2,35,0,11
	.byte	'reserved_29',0,4
	.word	517
	.byte	3,0,2,35,0,0,28
	.byte	'Ifx_CPU_TR_EVT_Bits',0,20,153,5,3
	.word	53496
	.byte	10
	.byte	'_Ifx_CPU_TRIG_ACC_Bits',0,20,156,5,16,4,11
	.byte	'T0',0,4
	.word	517
	.byte	1,31,2,35,0,11
	.byte	'T1',0,4
	.word	517
	.byte	1,30,2,35,0,11
	.byte	'T2',0,4
	.word	517
	.byte	1,29,2,35,0,11
	.byte	'T3',0,4
	.word	517
	.byte	1,28,2,35,0,11
	.byte	'T4',0,4
	.word	517
	.byte	1,27,2,35,0,11
	.byte	'T5',0,4
	.word	517
	.byte	1,26,2,35,0,11
	.byte	'T6',0,4
	.word	517
	.byte	1,25,2,35,0,11
	.byte	'T7',0,4
	.word	517
	.byte	1,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	517
	.byte	24,0,2,35,0,0,28
	.byte	'Ifx_CPU_TRIG_ACC_Bits',0,20,167,5,3
	.word	53814
	.byte	12,20,175,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45032
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_A',0,20,180,5,3
	.word	54009
	.byte	12,20,183,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45093
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_BIV',0,20,188,5,3
	.word	54068
	.byte	12,20,191,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45172
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_BTV',0,20,196,5,3
	.word	54129
	.byte	12,20,199,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45258
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_CCNT',0,20,204,5,3
	.word	54190
	.byte	12,20,207,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45347
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_CCTRL',0,20,212,5,3
	.word	54252
	.byte	12,20,215,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45493
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_COMPAT',0,20,220,5,3
	.word	54315
	.byte	12,20,223,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45620
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_CORE_ID',0,20,228,5,3
	.word	54379
	.byte	12,20,231,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45718
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_CPR_L',0,20,236,5,3
	.word	54444
	.byte	12,20,239,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45811
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_CPR_U',0,20,244,5,3
	.word	54507
	.byte	12,20,247,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45904
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_CPU_ID',0,20,252,5,3
	.word	54570
	.byte	12,20,255,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46011
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_CPXE',0,20,132,6,3
	.word	54634
	.byte	12,20,135,6,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46098
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_CREVT',0,20,140,6,3
	.word	54696
	.byte	12,20,143,6,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46252
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_CUS_ID',0,20,148,6,3
	.word	54759
	.byte	12,20,151,6,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46346
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_D',0,20,156,6,3
	.word	54823
	.byte	12,20,159,6,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46409
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_DATR',0,20,164,6,3
	.word	54882
	.byte	12,20,167,6,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46627
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_DBGSR',0,20,172,6,3
	.word	54944
	.byte	12,20,175,6,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46842
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_DBGTCR',0,20,180,6,3
	.word	55007
	.byte	12,20,183,6,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46936
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_DCON0',0,20,188,6,3
	.word	55071
	.byte	12,20,191,6,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47052
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_DCON2',0,20,196,6,3
	.word	55134
	.byte	12,20,199,6,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47153
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_DCX',0,20,204,6,3
	.word	55197
	.byte	12,20,207,6,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47246
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_DEADD',0,20,212,6,3
	.word	55258
	.byte	12,20,215,6,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47326
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_DIEAR',0,20,220,6,3
	.word	55321
	.byte	12,20,223,6,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47395
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_DIETR',0,20,228,6,3
	.word	55384
	.byte	12,20,231,6,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47624
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_DMS',0,20,236,6,3
	.word	55447
	.byte	12,20,239,6,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47717
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_DPR_L',0,20,244,6,3
	.word	55508
	.byte	12,20,247,6,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47812
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_DPR_U',0,20,252,6,3
	.word	55571
	.byte	12,20,255,6,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47907
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_DPRE',0,20,132,7,3
	.word	55634
	.byte	12,20,135,7,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47997
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_DPWE',0,20,140,7,3
	.word	55696
	.byte	12,20,143,7,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48087
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_DSTR',0,20,148,7,3
	.word	55758
	.byte	12,20,151,7,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48411
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_EXEVT',0,20,156,7,3
	.word	55820
	.byte	12,20,159,7,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48565
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_FCX',0,20,164,7,3
	.word	55883
	.byte	12,20,167,7,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48671
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_FPU_TRAP_CON',0,20,172,7,3
	.word	55944
	.byte	12,20,175,7,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49020
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_FPU_TRAP_OPC',0,20,180,7,3
	.word	56014
	.byte	12,20,183,7,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49180
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_FPU_TRAP_PC',0,20,188,7,3
	.word	56084
	.byte	12,20,191,7,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49261
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_FPU_TRAP_SRC1',0,20,196,7,3
	.word	56153
	.byte	12,20,199,7,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49348
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_FPU_TRAP_SRC2',0,20,204,7,3
	.word	56224
	.byte	12,20,207,7,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49435
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_FPU_TRAP_SRC3',0,20,212,7,3
	.word	56295
	.byte	12,20,215,7,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49522
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_ICNT',0,20,220,7,3
	.word	56366
	.byte	12,20,223,7,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49613
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_ICR',0,20,228,7,3
	.word	56428
	.byte	12,20,231,7,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49756
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_ISP',0,20,236,7,3
	.word	56489
	.byte	12,20,239,7,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49822
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_LCX',0,20,244,7,3
	.word	56550
	.byte	12,20,247,7,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49928
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_M1CNT',0,20,252,7,3
	.word	56611
	.byte	12,20,255,7,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50021
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_M2CNT',0,20,132,8,3
	.word	56674
	.byte	12,20,135,8,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50114
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_M3CNT',0,20,140,8,3
	.word	56737
	.byte	12,20,143,8,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50207
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_PC',0,20,148,8,3
	.word	56800
	.byte	12,20,151,8,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50292
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_PCON0',0,20,156,8,3
	.word	56860
	.byte	12,20,159,8,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50408
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_PCON1',0,20,164,8,3
	.word	56923
	.byte	12,20,167,8,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50519
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_PCON2',0,20,172,8,3
	.word	56986
	.byte	12,20,175,8,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50620
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_PCXI',0,20,180,8,3
	.word	57049
	.byte	12,20,183,8,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50750
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_PIEAR',0,20,188,8,3
	.word	57111
	.byte	12,20,191,8,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50819
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_PIETR',0,20,196,8,3
	.word	57174
	.byte	12,20,199,8,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51048
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_PMA0',0,20,204,8,3
	.word	57237
	.byte	12,20,207,8,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51161
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_PMA1',0,20,212,8,3
	.word	57299
	.byte	12,20,215,8,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51274
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_PMA2',0,20,220,8,3
	.word	57361
	.byte	12,20,223,8,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51365
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_PSTR',0,20,228,8,3
	.word	57423
	.byte	12,20,231,8,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51568
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_PSW',0,20,236,8,3
	.word	57485
	.byte	12,20,239,8,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51811
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_SEGEN',0,20,244,8,3
	.word	57546
	.byte	12,20,247,8,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51939
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_SMACON',0,20,252,8,3
	.word	57609
	.byte	12,20,255,8,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52180
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_SPROT_ACCENA',0,20,132,9,3
	.word	57673
	.byte	12,20,135,9,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52263
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_SPROT_ACCENB',0,20,140,9,3
	.word	57743
	.byte	12,20,143,9,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52354
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA',0,20,148,9,3
	.word	57813
	.byte	12,20,151,9,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52445
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB',0,20,156,9,3
	.word	57887
	.byte	12,20,159,9,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52544
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_SPROT_RGN_LA',0,20,164,9,3
	.word	57961
	.byte	12,20,167,9,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52651
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_SPROT_RGN_UA',0,20,172,9,3
	.word	58031
	.byte	12,20,175,9,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52758
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_SWEVT',0,20,180,9,3
	.word	58101
	.byte	12,20,183,9,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52912
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_SYSCON',0,20,188,9,3
	.word	58164
	.byte	12,20,191,9,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53073
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_TASK_ASI',0,20,196,9,3
	.word	58228
	.byte	12,20,199,9,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53171
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_TPS_CON',0,20,204,9,3
	.word	58294
	.byte	12,20,207,9,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53343
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_TPS_TIMER',0,20,212,9,3
	.word	58359
	.byte	12,20,215,9,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53423
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_TR_ADR',0,20,220,9,3
	.word	58426
	.byte	12,20,223,9,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53496
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_TR_EVT',0,20,228,9,3
	.word	58490
	.byte	12,20,231,9,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53814
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_TRIG_ACC',0,20,236,9,3
	.word	58554
	.byte	10
	.byte	'_Ifx_CPU_CPR',0,20,247,9,25,8,13
	.byte	'L',0
	.word	54444
	.byte	4,2,35,0,13
	.byte	'U',0
	.word	54507
	.byte	4,2,35,4,0,14
	.word	58620
	.byte	28
	.byte	'Ifx_CPU_CPR',0,20,251,9,3
	.word	58662
	.byte	10
	.byte	'_Ifx_CPU_DPR',0,20,254,9,25,8,13
	.byte	'L',0
	.word	55508
	.byte	4,2,35,0,13
	.byte	'U',0
	.word	55571
	.byte	4,2,35,4,0,14
	.word	58688
	.byte	28
	.byte	'Ifx_CPU_DPR',0,20,130,10,3
	.word	58730
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN',0,20,133,10,25,16,13
	.byte	'LA',0
	.word	57961
	.byte	4,2,35,0,13
	.byte	'UA',0
	.word	58031
	.byte	4,2,35,4,13
	.byte	'ACCENA',0
	.word	57813
	.byte	4,2,35,8,13
	.byte	'ACCENB',0
	.word	57887
	.byte	4,2,35,12,0,14
	.word	58756
	.byte	28
	.byte	'Ifx_CPU_SPROT_RGN',0,20,139,10,3
	.word	58838
	.byte	18,12
	.word	58359
	.byte	19,2,0,10
	.byte	'_Ifx_CPU_TPS',0,20,142,10,25,16,13
	.byte	'CON',0
	.word	58294
	.byte	4,2,35,0,13
	.byte	'TIMER',0
	.word	58870
	.byte	12,2,35,4,0,14
	.word	58879
	.byte	28
	.byte	'Ifx_CPU_TPS',0,20,146,10,3
	.word	58927
	.byte	10
	.byte	'_Ifx_CPU_TR',0,20,149,10,25,8,13
	.byte	'EVT',0
	.word	58490
	.byte	4,2,35,0,13
	.byte	'ADR',0
	.word	58426
	.byte	4,2,35,4,0,14
	.word	58953
	.byte	28
	.byte	'Ifx_CPU_TR',0,20,153,10,3
	.word	58998
	.byte	18,176,32
	.word	669
	.byte	19,175,32,0,18,208,223,1
	.word	669
	.byte	19,207,223,1,0,18,248,1
	.word	669
	.byte	19,247,1,0,18,244,29
	.word	669
	.byte	19,243,29,0,18,188,3
	.word	669
	.byte	19,187,3,0,18,232,3
	.word	669
	.byte	19,231,3,0,18,252,23
	.word	669
	.byte	19,251,23,0,18,228,63
	.word	669
	.byte	19,227,63,0,18,128,1
	.word	58688
	.byte	19,15,0,14
	.word	59113
	.byte	18,128,31
	.word	669
	.byte	19,255,30,0,18,64
	.word	58620
	.byte	19,7,0,14
	.word	59139
	.byte	18,192,31
	.word	669
	.byte	19,191,31,0,18,16
	.word	54634
	.byte	19,3,0,18,16
	.word	55634
	.byte	19,3,0,18,16
	.word	55696
	.byte	19,3,0,18,208,7
	.word	669
	.byte	19,207,7,0,14
	.word	58879
	.byte	18,240,23
	.word	669
	.byte	19,239,23,0,18,64
	.word	58953
	.byte	19,7,0,14
	.word	59218
	.byte	18,192,23
	.word	669
	.byte	19,191,23,0,18,232,1
	.word	669
	.byte	19,231,1,0,18,180,1
	.word	669
	.byte	19,179,1,0,18,172,1
	.word	669
	.byte	19,171,1,0,18,64
	.word	54823
	.byte	19,15,0,18,64
	.word	669
	.byte	19,63,0,18,64
	.word	54009
	.byte	19,15,0,10
	.byte	'_Ifx_CPU',0,20,166,10,25,128,128,4,13
	.byte	'reserved_0',0
	.word	59023
	.byte	176,32,2,35,0,13
	.byte	'SEGEN',0
	.word	57546
	.byte	4,3,35,176,32,13
	.byte	'reserved_1034',0
	.word	59034
	.byte	208,223,1,3,35,180,32,13
	.byte	'TASK_ASI',0
	.word	58228
	.byte	4,4,35,132,128,2,13
	.byte	'reserved_8008',0
	.word	59047
	.byte	248,1,4,35,136,128,2,13
	.byte	'PMA0',0
	.word	57237
	.byte	4,4,35,128,130,2,13
	.byte	'PMA1',0
	.word	57299
	.byte	4,4,35,132,130,2,13
	.byte	'PMA2',0
	.word	57361
	.byte	4,4,35,136,130,2,13
	.byte	'reserved_810C',0
	.word	59058
	.byte	244,29,4,35,140,130,2,13
	.byte	'DCON2',0
	.word	55134
	.byte	4,4,35,128,160,2,13
	.byte	'reserved_9004',0
	.word	4692
	.byte	8,4,35,132,160,2,13
	.byte	'SMACON',0
	.word	57609
	.byte	4,4,35,140,160,2,13
	.byte	'DSTR',0
	.word	55758
	.byte	4,4,35,144,160,2,13
	.byte	'reserved_9014',0
	.word	2873
	.byte	4,4,35,148,160,2,13
	.byte	'DATR',0
	.word	54882
	.byte	4,4,35,152,160,2,13
	.byte	'DEADD',0
	.word	55258
	.byte	4,4,35,156,160,2,13
	.byte	'DIEAR',0
	.word	55321
	.byte	4,4,35,160,160,2,13
	.byte	'DIETR',0
	.word	55384
	.byte	4,4,35,164,160,2,13
	.byte	'reserved_9028',0
	.word	4063
	.byte	24,4,35,168,160,2,13
	.byte	'DCON0',0
	.word	55071
	.byte	4,4,35,192,160,2,13
	.byte	'reserved_9044',0
	.word	59069
	.byte	188,3,4,35,196,160,2,13
	.byte	'PSTR',0
	.word	57423
	.byte	4,4,35,128,164,2,13
	.byte	'PCON1',0
	.word	56923
	.byte	4,4,35,132,164,2,13
	.byte	'PCON2',0
	.word	56986
	.byte	4,4,35,136,164,2,13
	.byte	'PCON0',0
	.word	56860
	.byte	4,4,35,140,164,2,13
	.byte	'PIEAR',0
	.word	57111
	.byte	4,4,35,144,164,2,13
	.byte	'PIETR',0
	.word	57174
	.byte	4,4,35,148,164,2,13
	.byte	'reserved_9218',0
	.word	59080
	.byte	232,3,4,35,152,164,2,13
	.byte	'COMPAT',0
	.word	54315
	.byte	4,4,35,128,168,2,13
	.byte	'reserved_9404',0
	.word	59091
	.byte	252,23,4,35,132,168,2,13
	.byte	'FPU_TRAP_CON',0
	.word	55944
	.byte	4,4,35,128,192,2,13
	.byte	'FPU_TRAP_PC',0
	.word	56084
	.byte	4,4,35,132,192,2,13
	.byte	'FPU_TRAP_OPC',0
	.word	56014
	.byte	4,4,35,136,192,2,13
	.byte	'reserved_A00C',0
	.word	2873
	.byte	4,4,35,140,192,2,13
	.byte	'FPU_TRAP_SRC1',0
	.word	56153
	.byte	4,4,35,144,192,2,13
	.byte	'FPU_TRAP_SRC2',0
	.word	56224
	.byte	4,4,35,148,192,2,13
	.byte	'FPU_TRAP_SRC3',0
	.word	56295
	.byte	4,4,35,152,192,2,13
	.byte	'reserved_A01C',0
	.word	59102
	.byte	228,63,4,35,156,192,2,13
	.byte	'DPR',0
	.word	59123
	.byte	128,1,4,35,128,128,3,13
	.byte	'reserved_C080',0
	.word	59128
	.byte	128,31,4,35,128,129,3,13
	.byte	'CPR',0
	.word	59148
	.byte	64,4,35,128,160,3,13
	.byte	'reserved_D040',0
	.word	59153
	.byte	192,31,4,35,192,160,3,13
	.byte	'CPXE',0
	.word	59164
	.byte	16,4,35,128,192,3,13
	.byte	'DPRE',0
	.word	59173
	.byte	16,4,35,144,192,3,13
	.byte	'DPWE',0
	.word	59182
	.byte	16,4,35,160,192,3,13
	.byte	'reserved_E030',0
	.word	59191
	.byte	208,7,4,35,176,192,3,13
	.byte	'TPS',0
	.word	59202
	.byte	16,4,35,128,200,3,13
	.byte	'reserved_E410',0
	.word	59207
	.byte	240,23,4,35,144,200,3,13
	.byte	'TR',0
	.word	59227
	.byte	64,4,35,128,224,3,13
	.byte	'reserved_F040',0
	.word	59232
	.byte	192,23,4,35,192,224,3,13
	.byte	'CCTRL',0
	.word	54252
	.byte	4,4,35,128,248,3,13
	.byte	'CCNT',0
	.word	54190
	.byte	4,4,35,132,248,3,13
	.byte	'ICNT',0
	.word	56366
	.byte	4,4,35,136,248,3,13
	.byte	'M1CNT',0
	.word	56611
	.byte	4,4,35,140,248,3,13
	.byte	'M2CNT',0
	.word	56674
	.byte	4,4,35,144,248,3,13
	.byte	'M3CNT',0
	.word	56737
	.byte	4,4,35,148,248,3,13
	.byte	'reserved_FC18',0
	.word	59243
	.byte	232,1,4,35,152,248,3,13
	.byte	'DBGSR',0
	.word	54944
	.byte	4,4,35,128,250,3,13
	.byte	'reserved_FD04',0
	.word	2873
	.byte	4,4,35,132,250,3,13
	.byte	'EXEVT',0
	.word	55820
	.byte	4,4,35,136,250,3,13
	.byte	'CREVT',0
	.word	54696
	.byte	4,4,35,140,250,3,13
	.byte	'SWEVT',0
	.word	58101
	.byte	4,4,35,144,250,3,13
	.byte	'reserved_FD14',0
	.word	42973
	.byte	28,4,35,148,250,3,13
	.byte	'TRIG_ACC',0
	.word	58554
	.byte	4,4,35,176,250,3,13
	.byte	'reserved_FD34',0
	.word	5032
	.byte	12,4,35,180,250,3,13
	.byte	'DMS',0
	.word	55447
	.byte	4,4,35,192,250,3,13
	.byte	'DCX',0
	.word	55197
	.byte	4,4,35,196,250,3,13
	.byte	'DBGTCR',0
	.word	55007
	.byte	4,4,35,200,250,3,13
	.byte	'reserved_FD4C',0
	.word	59254
	.byte	180,1,4,35,204,250,3,13
	.byte	'PCXI',0
	.word	57049
	.byte	4,4,35,128,252,3,13
	.byte	'PSW',0
	.word	57485
	.byte	4,4,35,132,252,3,13
	.byte	'PC',0
	.word	56800
	.byte	4,4,35,136,252,3,13
	.byte	'reserved_FE0C',0
	.word	4692
	.byte	8,4,35,140,252,3,13
	.byte	'SYSCON',0
	.word	58164
	.byte	4,4,35,148,252,3,13
	.byte	'CPU_ID',0
	.word	54570
	.byte	4,4,35,152,252,3,13
	.byte	'CORE_ID',0
	.word	54379
	.byte	4,4,35,156,252,3,13
	.byte	'BIV',0
	.word	54068
	.byte	4,4,35,160,252,3,13
	.byte	'BTV',0
	.word	54129
	.byte	4,4,35,164,252,3,13
	.byte	'ISP',0
	.word	56489
	.byte	4,4,35,168,252,3,13
	.byte	'ICR',0
	.word	56428
	.byte	4,4,35,172,252,3,13
	.byte	'reserved_FE30',0
	.word	4692
	.byte	8,4,35,176,252,3,13
	.byte	'FCX',0
	.word	55883
	.byte	4,4,35,184,252,3,13
	.byte	'LCX',0
	.word	56550
	.byte	4,4,35,188,252,3,13
	.byte	'reserved_FE40',0
	.word	42964
	.byte	16,4,35,192,252,3,13
	.byte	'CUS_ID',0
	.word	54759
	.byte	4,4,35,208,252,3,13
	.byte	'reserved_FE54',0
	.word	59265
	.byte	172,1,4,35,212,252,3,13
	.byte	'D',0
	.word	59276
	.byte	64,4,35,128,254,3,13
	.byte	'reserved_FF40',0
	.word	59285
	.byte	64,4,35,192,254,3,13
	.byte	'A',0
	.word	59294
	.byte	64,4,35,128,255,3,13
	.byte	'reserved_FFC0',0
	.word	59285
	.byte	64,4,35,192,255,3,0,14
	.word	59303
	.byte	28
	.byte	'Ifx_CPU',0,20,130,11,3
	.word	61094
	.byte	15,6,127,9,1,16
	.byte	'IfxCpu_Id_0',0,0,16
	.byte	'IfxCpu_Id_1',0,1,16
	.byte	'IfxCpu_Id_none',0,2,0,28
	.byte	'IfxCpu_Id',0,6,132,1,3
	.word	61116
	.byte	28
	.byte	'IfxCpu_ResourceCpu',0,6,161,1,3
	.word	1481
	.byte	28
	.byte	'Ifx_SRC_SRCR_Bits',0,11,62,3
	.word	9846
	.byte	28
	.byte	'Ifx_SRC_SRCR',0,11,75,3
	.word	10136
	.byte	10
	.byte	'_Ifx_SRC_AGBT',0,11,86,25,4,13
	.byte	'SR',0
	.word	10136
	.byte	4,2,35,0,0,14
	.word	61261
	.byte	28
	.byte	'Ifx_SRC_AGBT',0,11,89,3
	.word	61293
	.byte	10
	.byte	'_Ifx_SRC_ASCLIN',0,11,92,25,12,13
	.byte	'TX',0
	.word	10136
	.byte	4,2,35,0,13
	.byte	'RX',0
	.word	10136
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	10136
	.byte	4,2,35,8,0,14
	.word	61319
	.byte	28
	.byte	'Ifx_SRC_ASCLIN',0,11,97,3
	.word	61378
	.byte	10
	.byte	'_Ifx_SRC_BCUSPB',0,11,100,25,4,13
	.byte	'SBSRC',0
	.word	10136
	.byte	4,2,35,0,0,14
	.word	61406
	.byte	28
	.byte	'Ifx_SRC_BCUSPB',0,11,103,3
	.word	61443
	.byte	18,64
	.word	10136
	.byte	19,15,0,10
	.byte	'_Ifx_SRC_CAN',0,11,106,25,64,13
	.byte	'INT',0
	.word	61471
	.byte	64,2,35,0,0,14
	.word	61480
	.byte	28
	.byte	'Ifx_SRC_CAN',0,11,109,3
	.word	61512
	.byte	10
	.byte	'_Ifx_SRC_CCU6',0,11,112,25,16,13
	.byte	'SR0',0
	.word	10136
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10136
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	10136
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	10136
	.byte	4,2,35,12,0,14
	.word	61537
	.byte	28
	.byte	'Ifx_SRC_CCU6',0,11,118,3
	.word	61609
	.byte	18,8
	.word	10136
	.byte	19,1,0,10
	.byte	'_Ifx_SRC_CERBERUS',0,11,121,25,8,13
	.byte	'SR',0
	.word	61635
	.byte	8,2,35,0,0,14
	.word	61644
	.byte	28
	.byte	'Ifx_SRC_CERBERUS',0,11,124,3
	.word	61680
	.byte	10
	.byte	'_Ifx_SRC_CIF',0,11,127,25,16,13
	.byte	'MI',0
	.word	10136
	.byte	4,2,35,0,13
	.byte	'MIEP',0
	.word	10136
	.byte	4,2,35,4,13
	.byte	'ISP',0
	.word	10136
	.byte	4,2,35,8,13
	.byte	'MJPEG',0
	.word	10136
	.byte	4,2,35,12,0,14
	.word	61710
	.byte	28
	.byte	'Ifx_SRC_CIF',0,11,133,1,3
	.word	61783
	.byte	10
	.byte	'_Ifx_SRC_CPU',0,11,136,1,25,4,13
	.byte	'SBSRC',0
	.word	10136
	.byte	4,2,35,0,0,14
	.word	61809
	.byte	28
	.byte	'Ifx_SRC_CPU',0,11,139,1,3
	.word	61844
	.byte	18,192,1
	.word	10136
	.byte	19,47,0,10
	.byte	'_Ifx_SRC_DMA',0,11,142,1,25,208,1,13
	.byte	'ERR',0
	.word	10136
	.byte	4,2,35,0,13
	.byte	'reserved_4',0
	.word	5032
	.byte	12,2,35,4,13
	.byte	'CH',0
	.word	61870
	.byte	192,1,2,35,16,0,14
	.word	61880
	.byte	28
	.byte	'Ifx_SRC_DMA',0,11,147,1,3
	.word	61947
	.byte	10
	.byte	'_Ifx_SRC_DSADC',0,11,150,1,25,8,13
	.byte	'SRM',0
	.word	10136
	.byte	4,2,35,0,13
	.byte	'SRA',0
	.word	10136
	.byte	4,2,35,4,0,14
	.word	61973
	.byte	28
	.byte	'Ifx_SRC_DSADC',0,11,154,1,3
	.word	62021
	.byte	10
	.byte	'_Ifx_SRC_EMEM',0,11,157,1,25,4,13
	.byte	'SR',0
	.word	10136
	.byte	4,2,35,0,0,14
	.word	62049
	.byte	28
	.byte	'Ifx_SRC_EMEM',0,11,160,1,3
	.word	62082
	.byte	10
	.byte	'_Ifx_SRC_ERAY',0,11,163,1,25,80,13
	.byte	'INT',0
	.word	61635
	.byte	8,2,35,0,13
	.byte	'TINT',0
	.word	61635
	.byte	8,2,35,8,13
	.byte	'NDAT',0
	.word	61635
	.byte	8,2,35,16,13
	.byte	'MBSC',0
	.word	61635
	.byte	8,2,35,24,13
	.byte	'OBUSY',0
	.word	10136
	.byte	4,2,35,32,13
	.byte	'IBUSY',0
	.word	10136
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	42982
	.byte	40,2,35,40,0,14
	.word	62109
	.byte	28
	.byte	'Ifx_SRC_ERAY',0,11,172,1,3
	.word	62236
	.byte	10
	.byte	'_Ifx_SRC_ETH',0,11,175,1,25,4,13
	.byte	'SR',0
	.word	10136
	.byte	4,2,35,0,0,14
	.word	62263
	.byte	28
	.byte	'Ifx_SRC_ETH',0,11,178,1,3
	.word	62295
	.byte	10
	.byte	'_Ifx_SRC_FCE',0,11,181,1,25,4,13
	.byte	'SR',0
	.word	10136
	.byte	4,2,35,0,0,14
	.word	62321
	.byte	28
	.byte	'Ifx_SRC_FCE',0,11,184,1,3
	.word	62353
	.byte	10
	.byte	'_Ifx_SRC_FFT',0,11,187,1,25,12,13
	.byte	'DONE',0
	.word	10136
	.byte	4,2,35,0,13
	.byte	'ERR',0
	.word	10136
	.byte	4,2,35,4,13
	.byte	'RFS',0
	.word	10136
	.byte	4,2,35,8,0,14
	.word	62379
	.byte	28
	.byte	'Ifx_SRC_FFT',0,11,192,1,3
	.word	62439
	.byte	10
	.byte	'_Ifx_SRC_GPSR',0,11,195,1,25,32,13
	.byte	'SR0',0
	.word	10136
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10136
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	10136
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	10136
	.byte	4,2,35,12,13
	.byte	'reserved_10',0
	.word	42964
	.byte	16,2,35,16,0,14
	.word	62465
	.byte	28
	.byte	'Ifx_SRC_GPSR',0,11,202,1,3
	.word	62559
	.byte	10
	.byte	'_Ifx_SRC_GPT12',0,11,205,1,25,48,13
	.byte	'CIRQ',0
	.word	10136
	.byte	4,2,35,0,13
	.byte	'T2',0
	.word	10136
	.byte	4,2,35,4,13
	.byte	'T3',0
	.word	10136
	.byte	4,2,35,8,13
	.byte	'T4',0
	.word	10136
	.byte	4,2,35,12,13
	.byte	'T5',0
	.word	10136
	.byte	4,2,35,16,13
	.byte	'T6',0
	.word	10136
	.byte	4,2,35,20,13
	.byte	'reserved_18',0
	.word	4063
	.byte	24,2,35,24,0,14
	.word	62586
	.byte	28
	.byte	'Ifx_SRC_GPT12',0,11,214,1,3
	.word	62703
	.byte	18,12
	.word	10136
	.byte	19,2,0,18,32
	.word	10136
	.byte	19,7,0,18,32
	.word	62740
	.byte	19,0,0,18,88
	.word	669
	.byte	19,87,0,18,108
	.word	10136
	.byte	19,26,0,18,96
	.word	669
	.byte	19,95,0,18,96
	.word	62740
	.byte	19,2,0,18,160,3
	.word	669
	.byte	19,159,3,0,18,64
	.word	62740
	.byte	19,1,0,18,192,3
	.word	669
	.byte	19,191,3,0,18,16
	.word	10136
	.byte	19,3,0,18,64
	.word	62825
	.byte	19,3,0,18,192,2
	.word	669
	.byte	19,191,2,0,18,52
	.word	669
	.byte	19,51,0,10
	.byte	'_Ifx_SRC_GTM',0,11,217,1,25,204,18,13
	.byte	'AEIIRQ',0
	.word	10136
	.byte	4,2,35,0,13
	.byte	'ARUIRQ',0
	.word	62731
	.byte	12,2,35,4,13
	.byte	'reserved_10',0
	.word	2873
	.byte	4,2,35,16,13
	.byte	'BRCIRQ',0
	.word	10136
	.byte	4,2,35,20,13
	.byte	'CMPIRQ',0
	.word	10136
	.byte	4,2,35,24,13
	.byte	'SPEIRQ',0
	.word	61635
	.byte	8,2,35,28,13
	.byte	'reserved_24',0
	.word	4692
	.byte	8,2,35,36,13
	.byte	'PSM',0
	.word	62749
	.byte	32,2,35,44,13
	.byte	'reserved_4C',0
	.word	62758
	.byte	88,2,35,76,13
	.byte	'DPLL',0
	.word	62767
	.byte	108,3,35,164,1,13
	.byte	'reserved_110',0
	.word	62776
	.byte	96,3,35,144,2,13
	.byte	'ERR',0
	.word	10136
	.byte	4,3,35,240,2,13
	.byte	'reserved_174',0
	.word	5032
	.byte	12,3,35,244,2,13
	.byte	'TIM',0
	.word	62785
	.byte	96,3,35,128,3,13
	.byte	'reserved_1E0',0
	.word	62794
	.byte	160,3,3,35,224,3,13
	.byte	'MCS',0
	.word	62785
	.byte	96,3,35,128,7,13
	.byte	'reserved_3E0',0
	.word	62794
	.byte	160,3,3,35,224,7,13
	.byte	'TOM',0
	.word	62805
	.byte	64,3,35,128,11,13
	.byte	'reserved_5C0',0
	.word	62814
	.byte	192,3,3,35,192,11,13
	.byte	'ATOM',0
	.word	62834
	.byte	64,3,35,128,15,13
	.byte	'reserved_7C0',0
	.word	62843
	.byte	192,2,3,35,192,15,13
	.byte	'MCSW0',0
	.word	62731
	.byte	12,3,35,128,18,13
	.byte	'reserved_90C',0
	.word	62854
	.byte	52,3,35,140,18,13
	.byte	'MCSW1',0
	.word	62731
	.byte	12,3,35,192,18,0,14
	.word	62863
	.byte	28
	.byte	'Ifx_SRC_GTM',0,11,243,1,3
	.word	63323
	.byte	10
	.byte	'_Ifx_SRC_HSCT',0,11,246,1,25,4,13
	.byte	'SR',0
	.word	10136
	.byte	4,2,35,0,0,14
	.word	63349
	.byte	28
	.byte	'Ifx_SRC_HSCT',0,11,249,1,3
	.word	63382
	.byte	10
	.byte	'_Ifx_SRC_HSSL',0,11,252,1,25,16,13
	.byte	'COK',0
	.word	10136
	.byte	4,2,35,0,13
	.byte	'RDI',0
	.word	10136
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	10136
	.byte	4,2,35,8,13
	.byte	'TRG',0
	.word	10136
	.byte	4,2,35,12,0,14
	.word	63409
	.byte	28
	.byte	'Ifx_SRC_HSSL',0,11,130,2,3
	.word	63482
	.byte	18,56
	.word	669
	.byte	19,55,0,10
	.byte	'_Ifx_SRC_I2C',0,11,133,2,25,80,13
	.byte	'BREQ',0
	.word	10136
	.byte	4,2,35,0,13
	.byte	'LBREQ',0
	.word	10136
	.byte	4,2,35,4,13
	.byte	'SREQ',0
	.word	10136
	.byte	4,2,35,8,13
	.byte	'LSREQ',0
	.word	10136
	.byte	4,2,35,12,13
	.byte	'ERR',0
	.word	10136
	.byte	4,2,35,16,13
	.byte	'P',0
	.word	10136
	.byte	4,2,35,20,13
	.byte	'reserved_18',0
	.word	63509
	.byte	56,2,35,24,0,14
	.word	63518
	.byte	28
	.byte	'Ifx_SRC_I2C',0,11,142,2,3
	.word	63641
	.byte	10
	.byte	'_Ifx_SRC_LMU',0,11,145,2,25,4,13
	.byte	'SR',0
	.word	10136
	.byte	4,2,35,0,0,14
	.word	63667
	.byte	28
	.byte	'Ifx_SRC_LMU',0,11,148,2,3
	.word	63699
	.byte	10
	.byte	'_Ifx_SRC_MSC',0,11,151,2,25,20,13
	.byte	'SR0',0
	.word	10136
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10136
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	10136
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	10136
	.byte	4,2,35,12,13
	.byte	'SR4',0
	.word	10136
	.byte	4,2,35,16,0,14
	.word	63725
	.byte	28
	.byte	'Ifx_SRC_MSC',0,11,158,2,3
	.word	63810
	.byte	10
	.byte	'_Ifx_SRC_PMU',0,11,161,2,25,4,13
	.byte	'SR',0
	.word	10136
	.byte	4,2,35,0,0,14
	.word	63836
	.byte	28
	.byte	'Ifx_SRC_PMU',0,11,164,2,3
	.word	63868
	.byte	10
	.byte	'_Ifx_SRC_PSI5',0,11,167,2,25,32,13
	.byte	'SR',0
	.word	62740
	.byte	32,2,35,0,0,14
	.word	63894
	.byte	28
	.byte	'Ifx_SRC_PSI5',0,11,170,2,3
	.word	63927
	.byte	10
	.byte	'_Ifx_SRC_PSI5S',0,11,173,2,25,32,13
	.byte	'SR',0
	.word	62740
	.byte	32,2,35,0,0,14
	.word	63954
	.byte	28
	.byte	'Ifx_SRC_PSI5S',0,11,176,2,3
	.word	63988
	.byte	10
	.byte	'_Ifx_SRC_QSPI',0,11,179,2,25,24,13
	.byte	'TX',0
	.word	10136
	.byte	4,2,35,0,13
	.byte	'RX',0
	.word	10136
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	10136
	.byte	4,2,35,8,13
	.byte	'PT',0
	.word	10136
	.byte	4,2,35,12,13
	.byte	'HC',0
	.word	10136
	.byte	4,2,35,16,13
	.byte	'U',0
	.word	10136
	.byte	4,2,35,20,0,14
	.word	64016
	.byte	28
	.byte	'Ifx_SRC_QSPI',0,11,187,2,3
	.word	64109
	.byte	10
	.byte	'_Ifx_SRC_SCR',0,11,190,2,25,4,13
	.byte	'SR',0
	.word	10136
	.byte	4,2,35,0,0,14
	.word	64136
	.byte	28
	.byte	'Ifx_SRC_SCR',0,11,193,2,3
	.word	64168
	.byte	10
	.byte	'_Ifx_SRC_SCU',0,11,196,2,25,20,13
	.byte	'DTS',0
	.word	10136
	.byte	4,2,35,0,13
	.byte	'ERU',0
	.word	62825
	.byte	16,2,35,4,0,14
	.word	64194
	.byte	28
	.byte	'Ifx_SRC_SCU',0,11,200,2,3
	.word	64240
	.byte	18,24
	.word	10136
	.byte	19,5,0,10
	.byte	'_Ifx_SRC_SENT',0,11,203,2,25,24,13
	.byte	'SR',0
	.word	64266
	.byte	24,2,35,0,0,14
	.word	64275
	.byte	28
	.byte	'Ifx_SRC_SENT',0,11,206,2,3
	.word	64308
	.byte	10
	.byte	'_Ifx_SRC_SMU',0,11,209,2,25,12,13
	.byte	'SR',0
	.word	62731
	.byte	12,2,35,0,0,14
	.word	64335
	.byte	28
	.byte	'Ifx_SRC_SMU',0,11,212,2,3
	.word	64367
	.byte	10
	.byte	'_Ifx_SRC_STM',0,11,215,2,25,8,13
	.byte	'SR0',0
	.word	10136
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10136
	.byte	4,2,35,4,0,14
	.word	64393
	.byte	28
	.byte	'Ifx_SRC_STM',0,11,219,2,3
	.word	64439
	.byte	10
	.byte	'_Ifx_SRC_VADCCG',0,11,222,2,25,16,13
	.byte	'SR0',0
	.word	10136
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10136
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	10136
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	10136
	.byte	4,2,35,12,0,14
	.word	64465
	.byte	28
	.byte	'Ifx_SRC_VADCCG',0,11,228,2,3
	.word	64540
	.byte	10
	.byte	'_Ifx_SRC_VADCG',0,11,231,2,25,16,13
	.byte	'SR0',0
	.word	10136
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10136
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	10136
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	10136
	.byte	4,2,35,12,0,14
	.word	64569
	.byte	28
	.byte	'Ifx_SRC_VADCG',0,11,237,2,3
	.word	64643
	.byte	10
	.byte	'_Ifx_SRC_XBAR',0,11,240,2,25,4,13
	.byte	'SRC',0
	.word	10136
	.byte	4,2,35,0,0,14
	.word	64671
	.byte	28
	.byte	'Ifx_SRC_XBAR',0,11,243,2,3
	.word	64705
	.byte	18,4
	.word	61261
	.byte	19,0,0,14
	.word	64732
	.byte	10
	.byte	'_Ifx_SRC_GAGBT',0,11,128,3,25,4,13
	.byte	'AGBT',0
	.word	64741
	.byte	4,2,35,0,0,14
	.word	64746
	.byte	28
	.byte	'Ifx_SRC_GAGBT',0,11,131,3,3
	.word	64782
	.byte	18,48
	.word	61319
	.byte	19,3,0,14
	.word	64810
	.byte	10
	.byte	'_Ifx_SRC_GASCLIN',0,11,134,3,25,48,13
	.byte	'ASCLIN',0
	.word	64819
	.byte	48,2,35,0,0,14
	.word	64824
	.byte	28
	.byte	'Ifx_SRC_GASCLIN',0,11,137,3,3
	.word	64864
	.byte	14
	.word	61406
	.byte	10
	.byte	'_Ifx_SRC_GBCU',0,11,140,3,25,4,13
	.byte	'SPB',0
	.word	64894
	.byte	4,2,35,0,0,14
	.word	64899
	.byte	28
	.byte	'Ifx_SRC_GBCU',0,11,143,3,3
	.word	64933
	.byte	18,64
	.word	61480
	.byte	19,0,0,14
	.word	64960
	.byte	10
	.byte	'_Ifx_SRC_GCAN',0,11,146,3,25,64,13
	.byte	'CAN',0
	.word	64969
	.byte	64,2,35,0,0,14
	.word	64974
	.byte	28
	.byte	'Ifx_SRC_GCAN',0,11,149,3,3
	.word	65008
	.byte	18,32
	.word	61537
	.byte	19,1,0,14
	.word	65035
	.byte	10
	.byte	'_Ifx_SRC_GCCU6',0,11,152,3,25,32,13
	.byte	'CCU6',0
	.word	65044
	.byte	32,2,35,0,0,14
	.word	65049
	.byte	28
	.byte	'Ifx_SRC_GCCU6',0,11,155,3,3
	.word	65085
	.byte	14
	.word	61644
	.byte	10
	.byte	'_Ifx_SRC_GCERBERUS',0,11,158,3,25,8,13
	.byte	'CERBERUS',0
	.word	65113
	.byte	8,2,35,0,0,14
	.word	65118
	.byte	28
	.byte	'Ifx_SRC_GCERBERUS',0,11,161,3,3
	.word	65162
	.byte	18,16
	.word	61710
	.byte	19,0,0,14
	.word	65194
	.byte	10
	.byte	'_Ifx_SRC_GCIF',0,11,164,3,25,16,13
	.byte	'CIF',0
	.word	65203
	.byte	16,2,35,0,0,14
	.word	65208
	.byte	28
	.byte	'Ifx_SRC_GCIF',0,11,167,3,3
	.word	65242
	.byte	18,8
	.word	61809
	.byte	19,1,0,14
	.word	65269
	.byte	10
	.byte	'_Ifx_SRC_GCPU',0,11,170,3,25,8,13
	.byte	'CPU',0
	.word	65278
	.byte	8,2,35,0,0,14
	.word	65283
	.byte	28
	.byte	'Ifx_SRC_GCPU',0,11,173,3,3
	.word	65317
	.byte	18,208,1
	.word	61880
	.byte	19,0,0,14
	.word	65344
	.byte	10
	.byte	'_Ifx_SRC_GDMA',0,11,176,3,25,208,1,13
	.byte	'DMA',0
	.word	65354
	.byte	208,1,2,35,0,0,14
	.word	65359
	.byte	28
	.byte	'Ifx_SRC_GDMA',0,11,179,3,3
	.word	65395
	.byte	14
	.word	61973
	.byte	14
	.word	61973
	.byte	14
	.word	61973
	.byte	10
	.byte	'_Ifx_SRC_GDSADC',0,11,182,3,25,32,13
	.byte	'DSADC0',0
	.word	65422
	.byte	8,2,35,0,13
	.byte	'reserved_8',0
	.word	4692
	.byte	8,2,35,8,13
	.byte	'DSADC2',0
	.word	65427
	.byte	8,2,35,16,13
	.byte	'DSADC3',0
	.word	65432
	.byte	8,2,35,24,0,14
	.word	65437
	.byte	28
	.byte	'Ifx_SRC_GDSADC',0,11,188,3,3
	.word	65528
	.byte	18,4
	.word	62049
	.byte	19,0,0,14
	.word	65557
	.byte	10
	.byte	'_Ifx_SRC_GEMEM',0,11,191,3,25,4,13
	.byte	'EMEM',0
	.word	65566
	.byte	4,2,35,0,0,14
	.word	65571
	.byte	28
	.byte	'Ifx_SRC_GEMEM',0,11,194,3,3
	.word	65607
	.byte	18,80
	.word	62109
	.byte	19,0,0,14
	.word	65635
	.byte	10
	.byte	'_Ifx_SRC_GERAY',0,11,197,3,25,80,13
	.byte	'ERAY',0
	.word	65644
	.byte	80,2,35,0,0,14
	.word	65649
	.byte	28
	.byte	'Ifx_SRC_GERAY',0,11,200,3,3
	.word	65685
	.byte	18,4
	.word	62263
	.byte	19,0,0,14
	.word	65713
	.byte	10
	.byte	'_Ifx_SRC_GETH',0,11,203,3,25,4,13
	.byte	'ETH',0
	.word	65722
	.byte	4,2,35,0,0,14
	.word	65727
	.byte	28
	.byte	'Ifx_SRC_GETH',0,11,206,3,3
	.word	65761
	.byte	18,4
	.word	62321
	.byte	19,0,0,14
	.word	65788
	.byte	10
	.byte	'_Ifx_SRC_GFCE',0,11,209,3,25,4,13
	.byte	'FCE',0
	.word	65797
	.byte	4,2,35,0,0,14
	.word	65802
	.byte	28
	.byte	'Ifx_SRC_GFCE',0,11,212,3,3
	.word	65836
	.byte	18,12
	.word	62379
	.byte	19,0,0,14
	.word	65863
	.byte	10
	.byte	'_Ifx_SRC_GFFT',0,11,215,3,25,12,13
	.byte	'FFT',0
	.word	65872
	.byte	12,2,35,0,0,14
	.word	65877
	.byte	28
	.byte	'Ifx_SRC_GFFT',0,11,218,3,3
	.word	65911
	.byte	18,64
	.word	62465
	.byte	19,1,0,14
	.word	65938
	.byte	10
	.byte	'_Ifx_SRC_GGPSR',0,11,221,3,25,64,13
	.byte	'GPSR',0
	.word	65947
	.byte	64,2,35,0,0,14
	.word	65952
	.byte	28
	.byte	'Ifx_SRC_GGPSR',0,11,224,3,3
	.word	65988
	.byte	18,48
	.word	62586
	.byte	19,0,0,14
	.word	66016
	.byte	10
	.byte	'_Ifx_SRC_GGPT12',0,11,227,3,25,48,13
	.byte	'GPT12',0
	.word	66025
	.byte	48,2,35,0,0,14
	.word	66030
	.byte	28
	.byte	'Ifx_SRC_GGPT12',0,11,230,3,3
	.word	66068
	.byte	18,204,18
	.word	62863
	.byte	19,0,0,14
	.word	66097
	.byte	10
	.byte	'_Ifx_SRC_GGTM',0,11,233,3,25,204,18,13
	.byte	'GTM',0
	.word	66107
	.byte	204,18,2,35,0,0,14
	.word	66112
	.byte	28
	.byte	'Ifx_SRC_GGTM',0,11,236,3,3
	.word	66148
	.byte	18,4
	.word	63349
	.byte	19,0,0,14
	.word	66175
	.byte	10
	.byte	'_Ifx_SRC_GHSCT',0,11,239,3,25,4,13
	.byte	'HSCT',0
	.word	66184
	.byte	4,2,35,0,0,14
	.word	66189
	.byte	28
	.byte	'Ifx_SRC_GHSCT',0,11,242,3,3
	.word	66225
	.byte	18,64
	.word	63409
	.byte	19,3,0,14
	.word	66253
	.byte	10
	.byte	'_Ifx_SRC_GHSSL',0,11,245,3,25,68,13
	.byte	'HSSL',0
	.word	66262
	.byte	64,2,35,0,13
	.byte	'EXI',0
	.word	10136
	.byte	4,2,35,64,0,14
	.word	66267
	.byte	28
	.byte	'Ifx_SRC_GHSSL',0,11,249,3,3
	.word	66316
	.byte	18,80
	.word	63518
	.byte	19,0,0,14
	.word	66344
	.byte	10
	.byte	'_Ifx_SRC_GI2C',0,11,252,3,25,80,13
	.byte	'I2C',0
	.word	66353
	.byte	80,2,35,0,0,14
	.word	66358
	.byte	28
	.byte	'Ifx_SRC_GI2C',0,11,255,3,3
	.word	66392
	.byte	18,4
	.word	63667
	.byte	19,0,0,14
	.word	66419
	.byte	10
	.byte	'_Ifx_SRC_GLMU',0,11,130,4,25,4,13
	.byte	'LMU',0
	.word	66428
	.byte	4,2,35,0,0,14
	.word	66433
	.byte	28
	.byte	'Ifx_SRC_GLMU',0,11,133,4,3
	.word	66467
	.byte	18,40
	.word	63725
	.byte	19,1,0,14
	.word	66494
	.byte	10
	.byte	'_Ifx_SRC_GMSC',0,11,136,4,25,40,13
	.byte	'MSC',0
	.word	66503
	.byte	40,2,35,0,0,14
	.word	66508
	.byte	28
	.byte	'Ifx_SRC_GMSC',0,11,139,4,3
	.word	66542
	.byte	18,8
	.word	63836
	.byte	19,1,0,14
	.word	66569
	.byte	10
	.byte	'_Ifx_SRC_GPMU',0,11,142,4,25,8,13
	.byte	'PMU',0
	.word	66578
	.byte	8,2,35,0,0,14
	.word	66583
	.byte	28
	.byte	'Ifx_SRC_GPMU',0,11,145,4,3
	.word	66617
	.byte	18,32
	.word	63894
	.byte	19,0,0,14
	.word	66644
	.byte	10
	.byte	'_Ifx_SRC_GPSI5',0,11,148,4,25,32,13
	.byte	'PSI5',0
	.word	66653
	.byte	32,2,35,0,0,14
	.word	66658
	.byte	28
	.byte	'Ifx_SRC_GPSI5',0,11,151,4,3
	.word	66694
	.byte	18,32
	.word	63954
	.byte	19,0,0,14
	.word	66722
	.byte	10
	.byte	'_Ifx_SRC_GPSI5S',0,11,154,4,25,32,13
	.byte	'PSI5S',0
	.word	66731
	.byte	32,2,35,0,0,14
	.word	66736
	.byte	28
	.byte	'Ifx_SRC_GPSI5S',0,11,157,4,3
	.word	66774
	.byte	18,96
	.word	64016
	.byte	19,3,0,14
	.word	66803
	.byte	10
	.byte	'_Ifx_SRC_GQSPI',0,11,160,4,25,96,13
	.byte	'QSPI',0
	.word	66812
	.byte	96,2,35,0,0,14
	.word	66817
	.byte	28
	.byte	'Ifx_SRC_GQSPI',0,11,163,4,3
	.word	66853
	.byte	18,4
	.word	64136
	.byte	19,0,0,14
	.word	66881
	.byte	10
	.byte	'_Ifx_SRC_GSCR',0,11,166,4,25,4,13
	.byte	'SCR',0
	.word	66890
	.byte	4,2,35,0,0,14
	.word	66895
	.byte	28
	.byte	'Ifx_SRC_GSCR',0,11,169,4,3
	.word	66929
	.byte	14
	.word	64194
	.byte	10
	.byte	'_Ifx_SRC_GSCU',0,11,172,4,25,20,13
	.byte	'SCU',0
	.word	66956
	.byte	20,2,35,0,0,14
	.word	66961
	.byte	28
	.byte	'Ifx_SRC_GSCU',0,11,175,4,3
	.word	66995
	.byte	18,24
	.word	64275
	.byte	19,0,0,14
	.word	67022
	.byte	10
	.byte	'_Ifx_SRC_GSENT',0,11,178,4,25,24,13
	.byte	'SENT',0
	.word	67031
	.byte	24,2,35,0,0,14
	.word	67036
	.byte	28
	.byte	'Ifx_SRC_GSENT',0,11,181,4,3
	.word	67072
	.byte	18,12
	.word	64335
	.byte	19,0,0,14
	.word	67100
	.byte	10
	.byte	'_Ifx_SRC_GSMU',0,11,184,4,25,12,13
	.byte	'SMU',0
	.word	67109
	.byte	12,2,35,0,0,14
	.word	67114
	.byte	28
	.byte	'Ifx_SRC_GSMU',0,11,187,4,3
	.word	67148
	.byte	18,16
	.word	64393
	.byte	19,1,0,14
	.word	67175
	.byte	10
	.byte	'_Ifx_SRC_GSTM',0,11,190,4,25,16,13
	.byte	'STM',0
	.word	67184
	.byte	16,2,35,0,0,14
	.word	67189
	.byte	28
	.byte	'Ifx_SRC_GSTM',0,11,193,4,3
	.word	67223
	.byte	18,64
	.word	64569
	.byte	19,3,0,14
	.word	67250
	.byte	18,224,1
	.word	669
	.byte	19,223,1,0,18,32
	.word	64465
	.byte	19,1,0,14
	.word	67275
	.byte	10
	.byte	'_Ifx_SRC_GVADC',0,11,196,4,25,192,2,13
	.byte	'G',0
	.word	67259
	.byte	64,2,35,0,13
	.byte	'reserved_40',0
	.word	67264
	.byte	224,1,2,35,64,13
	.byte	'CG',0
	.word	67284
	.byte	32,3,35,160,2,0,14
	.word	67289
	.byte	28
	.byte	'Ifx_SRC_GVADC',0,11,201,4,3
	.word	67358
	.byte	14
	.word	64671
	.byte	10
	.byte	'_Ifx_SRC_GXBAR',0,11,204,4,25,4,13
	.byte	'XBAR',0
	.word	67386
	.byte	4,2,35,0,0,14
	.word	67391
	.byte	28
	.byte	'Ifx_SRC_GXBAR',0,11,207,4,3
	.word	67427
	.byte	15,5,144,1,9,1,16
	.byte	'IfxCpu_CounterMode_normal',0,0,16
	.byte	'IfxCpu_CounterMode_task',0,1,0,28
	.byte	'IfxCpu_CounterMode',0,5,148,1,3
	.word	67455
	.byte	26,5,160,1,9,6,13
	.byte	'counter',0
	.word	1636
	.byte	4,2,35,0,13
	.byte	'overlfow',0
	.word	669
	.byte	1,2,35,4,0,28
	.byte	'IfxCpu_Counter',0,5,164,1,3
	.word	67544
	.byte	26,5,172,1,9,32,13
	.byte	'instruction',0
	.word	67544
	.byte	6,2,35,0,13
	.byte	'clock',0
	.word	67544
	.byte	6,2,35,6,13
	.byte	'counter1',0
	.word	67544
	.byte	6,2,35,12,13
	.byte	'counter2',0
	.word	67544
	.byte	6,2,35,18,13
	.byte	'counter3',0
	.word	67544
	.byte	6,2,35,24,0,28
	.byte	'IfxCpu_Perf',0,5,179,1,3
	.word	67610
	.byte	10
	.byte	'_Ifx_FLASH_ACCEN0_Bits',0,21,45,16,4,11
	.byte	'EN0',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	669
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	669
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	669
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	669
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	669
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	669
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	669
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	669
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	669
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	669
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	669
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	669
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	669
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	669
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	669
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	669
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	669
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	669
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	669
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	669
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	669
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	669
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	669
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	669
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	669
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	669
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	669
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	669
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_FLASH_ACCEN0_Bits',0,21,79,3
	.word	67728
	.byte	10
	.byte	'_Ifx_FLASH_ACCEN1_Bits',0,21,82,16,4,11
	.byte	'reserved_0',0,4
	.word	494
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_FLASH_ACCEN1_Bits',0,21,85,3
	.word	68289
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_CFG_Bits',0,21,88,16,4,11
	.byte	'SEL',0,1
	.word	669
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	669
	.byte	2,0,2,35,0,11
	.byte	'CLR',0,1
	.word	669
	.byte	1,7,2,35,1,11
	.byte	'DIS',0,1
	.word	669
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	494
	.byte	22,0,2,35,0,0,28
	.byte	'Ifx_FLASH_CBAB_CFG_Bits',0,21,95,3
	.word	68370
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_STAT_Bits',0,21,98,16,4,11
	.byte	'VLD0',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'VLD1',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'VLD2',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'VLD3',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'VLD4',0,1
	.word	669
	.byte	1,3,2,35,0,11
	.byte	'VLD5',0,1
	.word	669
	.byte	1,2,2,35,0,11
	.byte	'VLD6',0,1
	.word	669
	.byte	1,1,2,35,0,11
	.byte	'VLD7',0,1
	.word	669
	.byte	1,0,2,35,0,11
	.byte	'VLD8',0,1
	.word	669
	.byte	1,7,2,35,1,11
	.byte	'VLD9',0,1
	.word	669
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	494
	.byte	22,0,2,35,0,0,28
	.byte	'Ifx_FLASH_CBAB_STAT_Bits',0,21,111,3
	.word	68523
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_TOP_Bits',0,21,114,16,4,11
	.byte	'reserved_0',0,1
	.word	669
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	494
	.byte	19,8,2,35,0,11
	.byte	'ERR',0,1
	.word	669
	.byte	6,2,2,35,3,11
	.byte	'VLD',0,1
	.word	669
	.byte	1,1,2,35,3,11
	.byte	'CLR',0,1
	.word	669
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_FLASH_CBAB_TOP_Bits',0,21,121,3
	.word	68771
	.byte	10
	.byte	'_Ifx_FLASH_COMM0_Bits',0,21,124,16,4,11
	.byte	'STATUS',0,1
	.word	669
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	494
	.byte	24,0,2,35,0,0,28
	.byte	'Ifx_FLASH_COMM0_Bits',0,21,128,1,3
	.word	68917
	.byte	10
	.byte	'_Ifx_FLASH_COMM1_Bits',0,21,131,1,16,4,11
	.byte	'STATUS',0,1
	.word	669
	.byte	8,0,2,35,0,11
	.byte	'DATA',0,1
	.word	669
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	686
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_FLASH_COMM1_Bits',0,21,136,1,3
	.word	69015
	.byte	10
	.byte	'_Ifx_FLASH_COMM2_Bits',0,21,139,1,16,4,11
	.byte	'STATUS',0,1
	.word	669
	.byte	8,0,2,35,0,11
	.byte	'DATA',0,1
	.word	669
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	686
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_FLASH_COMM2_Bits',0,21,144,1,3
	.word	69131
	.byte	10
	.byte	'_Ifx_FLASH_ECCRD_Bits',0,21,147,1,16,4,11
	.byte	'RCODE',0,4
	.word	494
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	686
	.byte	8,2,2,35,2,11
	.byte	'EDCERRINJ',0,1
	.word	669
	.byte	1,1,2,35,3,11
	.byte	'ECCORDIS',0,1
	.word	669
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_FLASH_ECCRD_Bits',0,21,153,1,3
	.word	69247
	.byte	10
	.byte	'_Ifx_FLASH_ECCRP_Bits',0,21,156,1,16,4,11
	.byte	'RCODE',0,4
	.word	494
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	686
	.byte	8,2,2,35,2,11
	.byte	'EDCERRINJ',0,1
	.word	669
	.byte	1,1,2,35,3,11
	.byte	'ECCORDIS',0,1
	.word	669
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_FLASH_ECCRP_Bits',0,21,162,1,3
	.word	69387
	.byte	10
	.byte	'_Ifx_FLASH_ECCW_Bits',0,21,165,1,16,4,11
	.byte	'WCODE',0,4
	.word	494
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	686
	.byte	8,2,2,35,2,11
	.byte	'DECENCDIS',0,1
	.word	669
	.byte	1,1,2,35,3,11
	.byte	'PECENCDIS',0,1
	.word	669
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_FLASH_ECCW_Bits',0,21,171,1,3
	.word	69527
	.byte	10
	.byte	'_Ifx_FLASH_FCON_Bits',0,21,174,1,16,4,11
	.byte	'WSPFLASH',0,1
	.word	669
	.byte	4,4,2,35,0,11
	.byte	'WSECPF',0,1
	.word	669
	.byte	2,2,2,35,0,11
	.byte	'WSDFLASH',0,2
	.word	686
	.byte	6,4,2,35,0,11
	.byte	'WSECDF',0,1
	.word	669
	.byte	3,1,2,35,1,11
	.byte	'IDLE',0,1
	.word	669
	.byte	1,0,2,35,1,11
	.byte	'ESLDIS',0,1
	.word	669
	.byte	1,7,2,35,2,11
	.byte	'SLEEP',0,1
	.word	669
	.byte	1,6,2,35,2,11
	.byte	'NSAFECC',0,1
	.word	669
	.byte	1,5,2,35,2,11
	.byte	'STALL',0,1
	.word	669
	.byte	1,4,2,35,2,11
	.byte	'RES21',0,1
	.word	669
	.byte	2,2,2,35,2,11
	.byte	'RES23',0,1
	.word	669
	.byte	2,0,2,35,2,11
	.byte	'VOPERM',0,1
	.word	669
	.byte	1,7,2,35,3,11
	.byte	'SQERM',0,1
	.word	669
	.byte	1,6,2,35,3,11
	.byte	'PROERM',0,1
	.word	669
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	669
	.byte	3,2,2,35,3,11
	.byte	'PR5V',0,1
	.word	669
	.byte	1,1,2,35,3,11
	.byte	'EOBM',0,1
	.word	669
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_FLASH_FCON_Bits',0,21,193,1,3
	.word	69666
	.byte	10
	.byte	'_Ifx_FLASH_FPRO_Bits',0,21,196,1,16,4,11
	.byte	'PROINP',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'PRODISP',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'PROIND',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'PRODISD',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'PROINHSMCOTP',0,1
	.word	669
	.byte	1,3,2,35,0,11
	.byte	'RES5',0,1
	.word	669
	.byte	1,2,2,35,0,11
	.byte	'PROINOTP',0,1
	.word	669
	.byte	1,1,2,35,0,11
	.byte	'RES7',0,1
	.word	669
	.byte	1,0,2,35,0,11
	.byte	'PROINDBG',0,1
	.word	669
	.byte	1,7,2,35,1,11
	.byte	'PRODISDBG',0,1
	.word	669
	.byte	1,6,2,35,1,11
	.byte	'PROINHSM',0,1
	.word	669
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	669
	.byte	5,0,2,35,1,11
	.byte	'DCFP',0,1
	.word	669
	.byte	1,7,2,35,2,11
	.byte	'DDFP',0,1
	.word	669
	.byte	1,6,2,35,2,11
	.byte	'DDFPX',0,1
	.word	669
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	669
	.byte	1,4,2,35,2,11
	.byte	'DDFD',0,1
	.word	669
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	669
	.byte	1,2,2,35,2,11
	.byte	'ENPE',0,1
	.word	669
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	669
	.byte	8,0,2,35,3,0,28
	.byte	'Ifx_FLASH_FPRO_Bits',0,21,218,1,3
	.word	70028
	.byte	10
	.byte	'_Ifx_FLASH_FSR_Bits',0,21,221,1,16,4,11
	.byte	'FABUSY',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'D0BUSY',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'RES1',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'P0BUSY',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'P1BUSY',0,1
	.word	669
	.byte	1,3,2,35,0,11
	.byte	'RES5',0,1
	.word	669
	.byte	1,2,2,35,0,11
	.byte	'RES6',0,1
	.word	669
	.byte	1,1,2,35,0,11
	.byte	'PROG',0,1
	.word	669
	.byte	1,0,2,35,0,11
	.byte	'ERASE',0,1
	.word	669
	.byte	1,7,2,35,1,11
	.byte	'PFPAGE',0,1
	.word	669
	.byte	1,6,2,35,1,11
	.byte	'DFPAGE',0,1
	.word	669
	.byte	1,5,2,35,1,11
	.byte	'OPER',0,1
	.word	669
	.byte	1,4,2,35,1,11
	.byte	'SQER',0,1
	.word	669
	.byte	1,3,2,35,1,11
	.byte	'PROER',0,1
	.word	669
	.byte	1,2,2,35,1,11
	.byte	'PFSBER',0,1
	.word	669
	.byte	1,1,2,35,1,11
	.byte	'PFDBER',0,1
	.word	669
	.byte	1,0,2,35,1,11
	.byte	'PFMBER',0,1
	.word	669
	.byte	1,7,2,35,2,11
	.byte	'RES17',0,1
	.word	669
	.byte	1,6,2,35,2,11
	.byte	'DFSBER',0,1
	.word	669
	.byte	1,5,2,35,2,11
	.byte	'DFDBER',0,1
	.word	669
	.byte	1,4,2,35,2,11
	.byte	'DFTBER',0,1
	.word	669
	.byte	1,3,2,35,2,11
	.byte	'DFMBER',0,1
	.word	669
	.byte	1,2,2,35,2,11
	.byte	'SRIADDERR',0,1
	.word	669
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	686
	.byte	2,7,2,35,2,11
	.byte	'PVER',0,1
	.word	669
	.byte	1,6,2,35,3,11
	.byte	'EVER',0,1
	.word	669
	.byte	1,5,2,35,3,11
	.byte	'SPND',0,1
	.word	669
	.byte	1,4,2,35,3,11
	.byte	'SLM',0,1
	.word	669
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	669
	.byte	1,2,2,35,3,11
	.byte	'ORIER',0,1
	.word	669
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	669
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_FLASH_FSR_Bits',0,21,254,1,3
	.word	70469
	.byte	10
	.byte	'_Ifx_FLASH_ID_Bits',0,21,129,2,16,4,11
	.byte	'MODREV',0,1
	.word	669
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	669
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	686
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_FLASH_ID_Bits',0,21,134,2,3
	.word	71075
	.byte	10
	.byte	'_Ifx_FLASH_MARD_Bits',0,21,137,2,16,4,11
	.byte	'HMARGIN',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'SELD0',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'SPND',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'SPNDERR',0,1
	.word	669
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,2
	.word	686
	.byte	10,1,2,35,0,11
	.byte	'TRAPDIS',0,1
	.word	669
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	686
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_FLASH_MARD_Bits',0,21,147,2,3
	.word	71186
	.byte	10
	.byte	'_Ifx_FLASH_MARP_Bits',0,21,150,2,16,4,11
	.byte	'SELP0',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'SELP1',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'RES2',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'RES3',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	686
	.byte	11,1,2,35,0,11
	.byte	'TRAPDIS',0,1
	.word	669
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	686
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_FLASH_MARP_Bits',0,21,159,2,3
	.word	71400
	.byte	10
	.byte	'_Ifx_FLASH_PROCOND_Bits',0,21,162,2,16,4,11
	.byte	'L',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'NSAFECC',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'RAMIN',0,1
	.word	669
	.byte	2,4,2,35,0,11
	.byte	'RAMINSEL',0,1
	.word	669
	.byte	4,0,2,35,0,11
	.byte	'OSCCFG',0,1
	.word	669
	.byte	1,7,2,35,1,11
	.byte	'MODE',0,1
	.word	669
	.byte	2,5,2,35,1,11
	.byte	'APREN',0,1
	.word	669
	.byte	1,4,2,35,1,11
	.byte	'CAP0EN',0,1
	.word	669
	.byte	1,3,2,35,1,11
	.byte	'CAP1EN',0,1
	.word	669
	.byte	1,2,2,35,1,11
	.byte	'CAP2EN',0,1
	.word	669
	.byte	1,1,2,35,1,11
	.byte	'CAP3EN',0,1
	.word	669
	.byte	1,0,2,35,1,11
	.byte	'ESR0CNT',0,2
	.word	686
	.byte	12,4,2,35,2,11
	.byte	'RES29',0,1
	.word	669
	.byte	2,2,2,35,3,11
	.byte	'RES30',0,1
	.word	669
	.byte	1,1,2,35,3,11
	.byte	'RPRO',0,1
	.word	669
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_FLASH_PROCOND_Bits',0,21,179,2,3
	.word	71587
	.byte	10
	.byte	'_Ifx_FLASH_PROCONDBG_Bits',0,21,182,2,16,4,11
	.byte	'OCDSDIS',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'DBGIFLCK',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'EDM',0,1
	.word	669
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	494
	.byte	28,0,2,35,0,0,28
	.byte	'Ifx_FLASH_PROCONDBG_Bits',0,21,188,2,3
	.word	71911
	.byte	10
	.byte	'_Ifx_FLASH_PROCONHSM_Bits',0,21,191,2,16,4,11
	.byte	'HSMDBGDIS',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'DBGIFLCK',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'TSTIFLCK',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'HSMTSTDIS',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'RES15',0,2
	.word	686
	.byte	12,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	686
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_FLASH_PROCONHSM_Bits',0,21,199,2,3
	.word	72054
	.byte	10
	.byte	'_Ifx_FLASH_PROCONHSMCOTP_Bits',0,21,202,2,16,4,11
	.byte	'HSMBOOTEN',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'SSWWAIT',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'HSMDX',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'HSM6X',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'HSM16X',0,1
	.word	669
	.byte	1,3,2,35,0,11
	.byte	'HSM17X',0,1
	.word	669
	.byte	1,2,2,35,0,11
	.byte	'S6ROM',0,1
	.word	669
	.byte	1,1,2,35,0,11
	.byte	'HSMENPINS',0,2
	.word	686
	.byte	2,7,2,35,0,11
	.byte	'HSMENRES',0,1
	.word	669
	.byte	2,5,2,35,1,11
	.byte	'DESTDBG',0,1
	.word	669
	.byte	2,3,2,35,1,11
	.byte	'BLKFLAN',0,1
	.word	669
	.byte	1,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	669
	.byte	2,0,2,35,1,11
	.byte	'S16ROM',0,1
	.word	669
	.byte	1,7,2,35,2,11
	.byte	'S17ROM',0,1
	.word	669
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	686
	.byte	14,0,2,35,2,0,28
	.byte	'Ifx_FLASH_PROCONHSMCOTP_Bits',0,21,219,2,3
	.word	72243
	.byte	10
	.byte	'_Ifx_FLASH_PROCONOTP_Bits',0,21,222,2,16,4,11
	.byte	'S0ROM',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'S1ROM',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'S2ROM',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'S3ROM',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'S4ROM',0,1
	.word	669
	.byte	1,3,2,35,0,11
	.byte	'S5ROM',0,1
	.word	669
	.byte	1,2,2,35,0,11
	.byte	'S6ROM',0,1
	.word	669
	.byte	1,1,2,35,0,11
	.byte	'S7ROM',0,1
	.word	669
	.byte	1,0,2,35,0,11
	.byte	'S8ROM',0,1
	.word	669
	.byte	1,7,2,35,1,11
	.byte	'S9ROM',0,1
	.word	669
	.byte	1,6,2,35,1,11
	.byte	'S10ROM',0,1
	.word	669
	.byte	1,5,2,35,1,11
	.byte	'S11ROM',0,1
	.word	669
	.byte	1,4,2,35,1,11
	.byte	'S12ROM',0,1
	.word	669
	.byte	1,3,2,35,1,11
	.byte	'S13ROM',0,1
	.word	669
	.byte	1,2,2,35,1,11
	.byte	'S14ROM',0,1
	.word	669
	.byte	1,1,2,35,1,11
	.byte	'S15ROM',0,1
	.word	669
	.byte	1,0,2,35,1,11
	.byte	'S16ROM',0,1
	.word	669
	.byte	1,7,2,35,2,11
	.byte	'S17ROM',0,1
	.word	669
	.byte	1,6,2,35,2,11
	.byte	'S18ROM',0,1
	.word	669
	.byte	1,5,2,35,2,11
	.byte	'S19ROM',0,1
	.word	669
	.byte	1,4,2,35,2,11
	.byte	'S20ROM',0,1
	.word	669
	.byte	1,3,2,35,2,11
	.byte	'S21ROM',0,1
	.word	669
	.byte	1,2,2,35,2,11
	.byte	'S22ROM',0,1
	.word	669
	.byte	1,1,2,35,2,11
	.byte	'S23ROM',0,1
	.word	669
	.byte	1,0,2,35,2,11
	.byte	'S24ROM',0,1
	.word	669
	.byte	1,7,2,35,3,11
	.byte	'S25ROM',0,1
	.word	669
	.byte	1,6,2,35,3,11
	.byte	'S26ROM',0,1
	.word	669
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	669
	.byte	2,3,2,35,3,11
	.byte	'BML',0,1
	.word	669
	.byte	2,1,2,35,3,11
	.byte	'TP',0,1
	.word	669
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_FLASH_PROCONOTP_Bits',0,21,254,2,3
	.word	72606
	.byte	10
	.byte	'_Ifx_FLASH_PROCONP_Bits',0,21,129,3,16,4,11
	.byte	'S0L',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'S1L',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'S2L',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'S3L',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'S4L',0,1
	.word	669
	.byte	1,3,2,35,0,11
	.byte	'S5L',0,1
	.word	669
	.byte	1,2,2,35,0,11
	.byte	'S6L',0,1
	.word	669
	.byte	1,1,2,35,0,11
	.byte	'S7L',0,1
	.word	669
	.byte	1,0,2,35,0,11
	.byte	'S8L',0,1
	.word	669
	.byte	1,7,2,35,1,11
	.byte	'S9L',0,1
	.word	669
	.byte	1,6,2,35,1,11
	.byte	'S10L',0,1
	.word	669
	.byte	1,5,2,35,1,11
	.byte	'S11L',0,1
	.word	669
	.byte	1,4,2,35,1,11
	.byte	'S12L',0,1
	.word	669
	.byte	1,3,2,35,1,11
	.byte	'S13L',0,1
	.word	669
	.byte	1,2,2,35,1,11
	.byte	'S14L',0,1
	.word	669
	.byte	1,1,2,35,1,11
	.byte	'S15L',0,1
	.word	669
	.byte	1,0,2,35,1,11
	.byte	'S16L',0,1
	.word	669
	.byte	1,7,2,35,2,11
	.byte	'S17L',0,1
	.word	669
	.byte	1,6,2,35,2,11
	.byte	'S18L',0,1
	.word	669
	.byte	1,5,2,35,2,11
	.byte	'S19L',0,1
	.word	669
	.byte	1,4,2,35,2,11
	.byte	'S20L',0,1
	.word	669
	.byte	1,3,2,35,2,11
	.byte	'S21L',0,1
	.word	669
	.byte	1,2,2,35,2,11
	.byte	'S22L',0,1
	.word	669
	.byte	1,1,2,35,2,11
	.byte	'S23L',0,1
	.word	669
	.byte	1,0,2,35,2,11
	.byte	'S24L',0,1
	.word	669
	.byte	1,7,2,35,3,11
	.byte	'S25L',0,1
	.word	669
	.byte	1,6,2,35,3,11
	.byte	'S26L',0,1
	.word	669
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	669
	.byte	4,1,2,35,3,11
	.byte	'RPRO',0,1
	.word	669
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_FLASH_PROCONP_Bits',0,21,160,3,3
	.word	73201
	.byte	10
	.byte	'_Ifx_FLASH_PROCONWOP_Bits',0,21,163,3,16,4,11
	.byte	'S0WOP',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'S1WOP',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'S2WOP',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'S3WOP',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'S4WOP',0,1
	.word	669
	.byte	1,3,2,35,0,11
	.byte	'S5WOP',0,1
	.word	669
	.byte	1,2,2,35,0,11
	.byte	'S6WOP',0,1
	.word	669
	.byte	1,1,2,35,0,11
	.byte	'S7WOP',0,1
	.word	669
	.byte	1,0,2,35,0,11
	.byte	'S8WOP',0,1
	.word	669
	.byte	1,7,2,35,1,11
	.byte	'S9WOP',0,1
	.word	669
	.byte	1,6,2,35,1,11
	.byte	'S10WOP',0,1
	.word	669
	.byte	1,5,2,35,1,11
	.byte	'S11WOP',0,1
	.word	669
	.byte	1,4,2,35,1,11
	.byte	'S12WOP',0,1
	.word	669
	.byte	1,3,2,35,1,11
	.byte	'S13WOP',0,1
	.word	669
	.byte	1,2,2,35,1,11
	.byte	'S14WOP',0,1
	.word	669
	.byte	1,1,2,35,1,11
	.byte	'S15WOP',0,1
	.word	669
	.byte	1,0,2,35,1,11
	.byte	'S16WOP',0,1
	.word	669
	.byte	1,7,2,35,2,11
	.byte	'S17WOP',0,1
	.word	669
	.byte	1,6,2,35,2,11
	.byte	'S18WOP',0,1
	.word	669
	.byte	1,5,2,35,2,11
	.byte	'S19WOP',0,1
	.word	669
	.byte	1,4,2,35,2,11
	.byte	'S20WOP',0,1
	.word	669
	.byte	1,3,2,35,2,11
	.byte	'S21WOP',0,1
	.word	669
	.byte	1,2,2,35,2,11
	.byte	'S22WOP',0,1
	.word	669
	.byte	1,1,2,35,2,11
	.byte	'S23WOP',0,1
	.word	669
	.byte	1,0,2,35,2,11
	.byte	'S24WOP',0,1
	.word	669
	.byte	1,7,2,35,3,11
	.byte	'S25WOP',0,1
	.word	669
	.byte	1,6,2,35,3,11
	.byte	'S26WOP',0,1
	.word	669
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	669
	.byte	4,1,2,35,3,11
	.byte	'DATM',0,1
	.word	669
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_FLASH_PROCONWOP_Bits',0,21,194,3,3
	.word	73725
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG0_Bits',0,21,197,3,16,4,11
	.byte	'TAG',0,1
	.word	669
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	494
	.byte	26,0,2,35,0,0,28
	.byte	'Ifx_FLASH_RDB_CFG0_Bits',0,21,201,3,3
	.word	74307
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG1_Bits',0,21,204,3,16,4,11
	.byte	'TAG',0,1
	.word	669
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	494
	.byte	26,0,2,35,0,0,28
	.byte	'Ifx_FLASH_RDB_CFG1_Bits',0,21,208,3,3
	.word	74409
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG2_Bits',0,21,211,3,16,4,11
	.byte	'TAG',0,1
	.word	669
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	494
	.byte	26,0,2,35,0,0,28
	.byte	'Ifx_FLASH_RDB_CFG2_Bits',0,21,215,3,3
	.word	74511
	.byte	10
	.byte	'_Ifx_FLASH_RRAD_Bits',0,21,218,3,16,4,11
	.byte	'reserved_0',0,1
	.word	669
	.byte	3,5,2,35,0,11
	.byte	'ADD',0,4
	.word	494
	.byte	29,0,2,35,0,0,28
	.byte	'Ifx_FLASH_RRAD_Bits',0,21,222,3,3
	.word	74613
	.byte	10
	.byte	'_Ifx_FLASH_RRCT_Bits',0,21,225,3,16,4,11
	.byte	'STRT',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'STP',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'BUSY',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'DONE',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'ERR',0,1
	.word	669
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	669
	.byte	3,0,2,35,0,11
	.byte	'EOBM',0,1
	.word	669
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	669
	.byte	7,0,2,35,1,11
	.byte	'CNT',0,2
	.word	686
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_FLASH_RRCT_Bits',0,21,236,3,3
	.word	74707
	.byte	10
	.byte	'_Ifx_FLASH_RRD0_Bits',0,21,239,3,16,4,11
	.byte	'DATA',0,4
	.word	494
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_FLASH_RRD0_Bits',0,21,242,3,3
	.word	74917
	.byte	10
	.byte	'_Ifx_FLASH_RRD1_Bits',0,21,245,3,16,4,11
	.byte	'DATA',0,4
	.word	494
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_FLASH_RRD1_Bits',0,21,248,3,3
	.word	74990
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_CFG_Bits',0,21,251,3,16,4,11
	.byte	'SEL',0,1
	.word	669
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	669
	.byte	2,0,2,35,0,11
	.byte	'CLR',0,1
	.word	669
	.byte	1,7,2,35,1,11
	.byte	'DIS',0,1
	.word	669
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	494
	.byte	22,0,2,35,0,0,28
	.byte	'Ifx_FLASH_UBAB_CFG_Bits',0,21,130,4,3
	.word	75063
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_STAT_Bits',0,21,133,4,16,4,11
	.byte	'VLD0',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	494
	.byte	31,0,2,35,0,0,28
	.byte	'Ifx_FLASH_UBAB_STAT_Bits',0,21,137,4,3
	.word	75218
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_TOP_Bits',0,21,140,4,16,4,11
	.byte	'reserved_0',0,1
	.word	669
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	494
	.byte	19,8,2,35,0,11
	.byte	'ERR',0,1
	.word	669
	.byte	6,2,2,35,3,11
	.byte	'VLD',0,1
	.word	669
	.byte	1,1,2,35,3,11
	.byte	'CLR',0,1
	.word	669
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_FLASH_UBAB_TOP_Bits',0,21,147,4,3
	.word	75323
	.byte	12,21,155,4,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67728
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_ACCEN0',0,21,160,4,3
	.word	75471
	.byte	12,21,163,4,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68289
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_ACCEN1',0,21,168,4,3
	.word	75537
	.byte	12,21,171,4,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68370
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_CBAB_CFG',0,21,176,4,3
	.word	75603
	.byte	12,21,179,4,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68523
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_CBAB_STAT',0,21,184,4,3
	.word	75671
	.byte	12,21,187,4,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68771
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_CBAB_TOP',0,21,192,4,3
	.word	75740
	.byte	12,21,195,4,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68917
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_COMM0',0,21,200,4,3
	.word	75808
	.byte	12,21,203,4,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69015
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_COMM1',0,21,208,4,3
	.word	75873
	.byte	12,21,211,4,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69131
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_COMM2',0,21,216,4,3
	.word	75938
	.byte	12,21,219,4,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69247
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_ECCRD',0,21,224,4,3
	.word	76003
	.byte	12,21,227,4,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69387
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_ECCRP',0,21,232,4,3
	.word	76068
	.byte	12,21,235,4,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69527
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_ECCW',0,21,240,4,3
	.word	76133
	.byte	12,21,243,4,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69666
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_FCON',0,21,248,4,3
	.word	76197
	.byte	12,21,251,4,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70028
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_FPRO',0,21,128,5,3
	.word	76261
	.byte	12,21,131,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70469
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_FSR',0,21,136,5,3
	.word	76325
	.byte	12,21,139,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71075
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_ID',0,21,144,5,3
	.word	76388
	.byte	12,21,147,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71186
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_MARD',0,21,152,5,3
	.word	76450
	.byte	12,21,155,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71400
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_MARP',0,21,160,5,3
	.word	76514
	.byte	12,21,163,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71587
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_PROCOND',0,21,168,5,3
	.word	76578
	.byte	12,21,171,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71911
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_PROCONDBG',0,21,176,5,3
	.word	76645
	.byte	12,21,179,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72054
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_PROCONHSM',0,21,184,5,3
	.word	76714
	.byte	12,21,187,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72243
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_PROCONHSMCOTP',0,21,192,5,3
	.word	76783
	.byte	12,21,195,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72606
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_PROCONOTP',0,21,200,5,3
	.word	76856
	.byte	12,21,203,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	73201
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_PROCONP',0,21,208,5,3
	.word	76925
	.byte	12,21,211,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	73725
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_PROCONWOP',0,21,216,5,3
	.word	76992
	.byte	12,21,219,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	74307
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_RDB_CFG0',0,21,224,5,3
	.word	77061
	.byte	12,21,227,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	74409
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_RDB_CFG1',0,21,232,5,3
	.word	77129
	.byte	12,21,235,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	74511
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_RDB_CFG2',0,21,240,5,3
	.word	77197
	.byte	12,21,243,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	74613
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_RRAD',0,21,248,5,3
	.word	77265
	.byte	12,21,251,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	74707
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_RRCT',0,21,128,6,3
	.word	77329
	.byte	12,21,131,6,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	74917
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_RRD0',0,21,136,6,3
	.word	77393
	.byte	12,21,139,6,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	74990
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_RRD1',0,21,144,6,3
	.word	77457
	.byte	12,21,147,6,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	75063
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_UBAB_CFG',0,21,152,6,3
	.word	77521
	.byte	12,21,155,6,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	75218
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_UBAB_STAT',0,21,160,6,3
	.word	77589
	.byte	12,21,163,6,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	75323
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_UBAB_TOP',0,21,168,6,3
	.word	77658
	.byte	10
	.byte	'_Ifx_FLASH_CBAB',0,21,179,6,25,12,13
	.byte	'CFG',0
	.word	75603
	.byte	4,2,35,0,13
	.byte	'STAT',0
	.word	75671
	.byte	4,2,35,4,13
	.byte	'TOP',0
	.word	75740
	.byte	4,2,35,8,0,14
	.word	77726
	.byte	28
	.byte	'Ifx_FLASH_CBAB',0,21,184,6,3
	.word	77789
	.byte	10
	.byte	'_Ifx_FLASH_RDB',0,21,187,6,25,12,13
	.byte	'CFG0',0
	.word	77061
	.byte	4,2,35,0,13
	.byte	'CFG1',0
	.word	77129
	.byte	4,2,35,4,13
	.byte	'CFG2',0
	.word	77197
	.byte	4,2,35,8,0,14
	.word	77818
	.byte	28
	.byte	'Ifx_FLASH_RDB',0,21,192,6,3
	.word	77882
	.byte	10
	.byte	'_Ifx_FLASH_UBAB',0,21,195,6,25,12,13
	.byte	'CFG',0
	.word	77521
	.byte	4,2,35,0,13
	.byte	'STAT',0
	.word	77589
	.byte	4,2,35,4,13
	.byte	'TOP',0
	.word	77658
	.byte	4,2,35,8,0,14
	.word	77910
	.byte	28
	.byte	'Ifx_FLASH_UBAB',0,21,200,6,3
	.word	77973
	.byte	28
	.byte	'Ifx_P_ACCEN0_Bits',0,8,79,3
	.word	8445
	.byte	28
	.byte	'Ifx_P_ACCEN1_Bits',0,8,85,3
	.word	8358
	.byte	28
	.byte	'Ifx_P_ESR_Bits',0,8,107,3
	.word	4701
	.byte	28
	.byte	'Ifx_P_ID_Bits',0,8,115,3
	.word	2754
	.byte	28
	.byte	'Ifx_P_IN_Bits',0,8,137,1,3
	.word	3749
	.byte	28
	.byte	'Ifx_P_IOCR0_Bits',0,8,150,1,3
	.word	2882
	.byte	28
	.byte	'Ifx_P_IOCR12_Bits',0,8,163,1,3
	.word	3529
	.byte	28
	.byte	'Ifx_P_IOCR4_Bits',0,8,176,1,3
	.word	3097
	.byte	28
	.byte	'Ifx_P_IOCR8_Bits',0,8,189,1,3
	.word	3312
	.byte	28
	.byte	'Ifx_P_LPCR0_Bits',0,8,197,1,3
	.word	7717
	.byte	28
	.byte	'Ifx_P_LPCR1_Bits',0,8,205,1,3
	.word	7841
	.byte	28
	.byte	'Ifx_P_LPCR1_P21_Bits',0,8,215,1,3
	.word	7925
	.byte	28
	.byte	'Ifx_P_LPCR2_Bits',0,8,229,1,3
	.word	8105
	.byte	28
	.byte	'Ifx_P_OMCR0_Bits',0,8,240,1,3
	.word	6356
	.byte	28
	.byte	'Ifx_P_OMCR12_Bits',0,8,250,1,3
	.word	6880
	.byte	28
	.byte	'Ifx_P_OMCR4_Bits',0,8,133,2,3
	.word	6530
	.byte	28
	.byte	'Ifx_P_OMCR8_Bits',0,8,144,2,3
	.word	6704
	.byte	28
	.byte	'Ifx_P_OMCR_Bits',0,8,166,2,3
	.word	7369
	.byte	28
	.byte	'Ifx_P_OMR_Bits',0,8,203,2,3
	.word	2183
	.byte	28
	.byte	'Ifx_P_OMSR0_Bits',0,8,213,2,3
	.word	5693
	.byte	28
	.byte	'Ifx_P_OMSR12_Bits',0,8,224,2,3
	.word	6181
	.byte	28
	.byte	'Ifx_P_OMSR4_Bits',0,8,235,2,3
	.word	5840
	.byte	28
	.byte	'Ifx_P_OMSR8_Bits',0,8,246,2,3
	.word	6009
	.byte	28
	.byte	'Ifx_P_OMSR_Bits',0,8,140,3,3
	.word	7036
	.byte	28
	.byte	'Ifx_P_OUT_Bits',0,8,162,3,3
	.word	1867
	.byte	28
	.byte	'Ifx_P_PCSR_Bits',0,8,180,3,3
	.word	5407
	.byte	28
	.byte	'Ifx_P_PDISC_Bits',0,8,202,3,3
	.word	5041
	.byte	28
	.byte	'Ifx_P_PDR0_Bits',0,8,223,3,3
	.word	4072
	.byte	28
	.byte	'Ifx_P_PDR1_Bits',0,8,244,3,3
	.word	4376
	.byte	28
	.byte	'Ifx_P_ACCEN0',0,8,129,4,3
	.word	8972
	.byte	28
	.byte	'Ifx_P_ACCEN1',0,8,137,4,3
	.word	8405
	.byte	28
	.byte	'Ifx_P_ESR',0,8,145,4,3
	.word	4992
	.byte	28
	.byte	'Ifx_P_ID',0,8,153,4,3
	.word	2833
	.byte	28
	.byte	'Ifx_P_IN',0,8,161,4,3
	.word	4023
	.byte	28
	.byte	'Ifx_P_IOCR0',0,8,169,4,3
	.word	3057
	.byte	28
	.byte	'Ifx_P_IOCR12',0,8,177,4,3
	.word	3709
	.byte	28
	.byte	'Ifx_P_IOCR4',0,8,185,4,3
	.word	3272
	.byte	28
	.byte	'Ifx_P_IOCR8',0,8,193,4,3
	.word	3489
	.byte	28
	.byte	'Ifx_P_LPCR0',0,8,201,4,3
	.word	7801
	.byte	28
	.byte	'Ifx_P_LPCR1',0,8,210,4,3
	.word	8050
	.byte	28
	.byte	'Ifx_P_LPCR2',0,8,218,4,3
	.word	8309
	.byte	28
	.byte	'Ifx_P_OMCR',0,8,226,4,3
	.word	7677
	.byte	28
	.byte	'Ifx_P_OMCR0',0,8,234,4,3
	.word	6490
	.byte	28
	.byte	'Ifx_P_OMCR12',0,8,242,4,3
	.word	6996
	.byte	28
	.byte	'Ifx_P_OMCR4',0,8,250,4,3
	.word	6664
	.byte	28
	.byte	'Ifx_P_OMCR8',0,8,130,5,3
	.word	6840
	.byte	28
	.byte	'Ifx_P_OMR',0,8,138,5,3
	.word	2714
	.byte	28
	.byte	'Ifx_P_OMSR',0,8,146,5,3
	.word	7329
	.byte	28
	.byte	'Ifx_P_OMSR0',0,8,154,5,3
	.word	5800
	.byte	28
	.byte	'Ifx_P_OMSR12',0,8,162,5,3
	.word	6316
	.byte	28
	.byte	'Ifx_P_OMSR4',0,8,170,5,3
	.word	5969
	.byte	28
	.byte	'Ifx_P_OMSR8',0,8,178,5,3
	.word	6141
	.byte	28
	.byte	'Ifx_P_OUT',0,8,186,5,3
	.word	2143
	.byte	28
	.byte	'Ifx_P_PCSR',0,8,194,5,3
	.word	5653
	.byte	28
	.byte	'Ifx_P_PDISC',0,8,202,5,3
	.word	5367
	.byte	28
	.byte	'Ifx_P_PDR0',0,8,210,5,3
	.word	4336
	.byte	28
	.byte	'Ifx_P_PDR1',0,8,218,5,3
	.word	4652
	.byte	14
	.word	9012
	.byte	28
	.byte	'Ifx_P',0,8,139,6,3
	.word	79320
	.byte	15,7,83,9,1,16
	.byte	'IfxPort_InputMode_undefined',0,127,16
	.byte	'IfxPort_InputMode_noPullDevice',0,0,16
	.byte	'IfxPort_InputMode_pullDown',0,8,16
	.byte	'IfxPort_InputMode_pullUp',0,16,0,28
	.byte	'IfxPort_InputMode',0,7,89,3
	.word	79340
	.byte	15,7,120,9,1,16
	.byte	'IfxPort_OutputIdx_general',0,128,1,16
	.byte	'IfxPort_OutputIdx_alt1',0,136,1,16
	.byte	'IfxPort_OutputIdx_alt2',0,144,1,16
	.byte	'IfxPort_OutputIdx_alt3',0,152,1,16
	.byte	'IfxPort_OutputIdx_alt4',0,160,1,16
	.byte	'IfxPort_OutputIdx_alt5',0,168,1,16
	.byte	'IfxPort_OutputIdx_alt6',0,176,1,16
	.byte	'IfxPort_OutputIdx_alt7',0,184,1,0,28
	.byte	'IfxPort_OutputIdx',0,7,130,1,3
	.word	79491
	.byte	15,7,134,1,9,1,16
	.byte	'IfxPort_OutputMode_pushPull',0,128,1,16
	.byte	'IfxPort_OutputMode_openDrain',0,192,1,0,28
	.byte	'IfxPort_OutputMode',0,7,138,1,3
	.word	79735
	.byte	15,7,144,1,9,1,16
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed1',0,0,16
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed2',0,1,16
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed3',0,2,16
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed4',0,3,16
	.byte	'IfxPort_PadDriver_lvdsSpeed1',0,4,16
	.byte	'IfxPort_PadDriver_lvdsSpeed2',0,5,16
	.byte	'IfxPort_PadDriver_lvdsSpeed3',0,6,16
	.byte	'IfxPort_PadDriver_lvdsSpeed4',0,7,16
	.byte	'IfxPort_PadDriver_ttlSpeed1',0,8,16
	.byte	'IfxPort_PadDriver_ttlSpeed2',0,9,16
	.byte	'IfxPort_PadDriver_ttlSpeed3',0,10,16
	.byte	'IfxPort_PadDriver_ttlSpeed4',0,11,0,28
	.byte	'IfxPort_PadDriver',0,7,158,1,3
	.word	79833
	.byte	28
	.byte	'IfxPort_State',0,7,178,1,3
	.word	9625
	.byte	26,7,190,1,9,8,13
	.byte	'port',0
	.word	9620
	.byte	4,2,35,0,13
	.byte	'pinIndex',0
	.word	669
	.byte	1,2,35,4,0,28
	.byte	'IfxPort_Pin',0,7,194,1,3
	.word	80298
	.byte	28
	.byte	'IfxScuCcu_PllStepsFunctionHook',0,9,148,1,16
	.word	233
	.byte	26,9,212,5,9,8,13
	.byte	'value',0
	.word	1636
	.byte	4,2,35,0,13
	.byte	'mask',0
	.word	1636
	.byte	4,2,35,4,0,28
	.byte	'IfxScuCcu_CcuconRegConfig',0,9,216,5,3
	.word	80398
	.byte	26,9,221,5,9,8,13
	.byte	'pDivider',0
	.word	669
	.byte	1,2,35,0,13
	.byte	'nDivider',0
	.word	669
	.byte	1,2,35,1,13
	.byte	'k2Initial',0
	.word	669
	.byte	1,2,35,2,13
	.byte	'waitTime',0
	.word	290
	.byte	4,2,35,4,0,28
	.byte	'IfxScuCcu_InitialStepConfig',0,9,227,5,3
	.word	80469
	.byte	26,9,231,5,9,12,13
	.byte	'k2Step',0
	.word	669
	.byte	1,2,35,0,13
	.byte	'waitTime',0
	.word	290
	.byte	4,2,35,2,13
	.byte	'hookFunction',0
	.word	80358
	.byte	4,2,35,8,0,28
	.byte	'IfxScuCcu_PllStepsConfig',0,9,236,5,3
	.word	80586
	.byte	3
	.word	230
	.byte	26,9,244,5,9,48,13
	.byte	'ccucon0',0
	.word	80398
	.byte	8,2,35,0,13
	.byte	'ccucon1',0
	.word	80398
	.byte	8,2,35,8,13
	.byte	'ccucon2',0
	.word	80398
	.byte	8,2,35,16,13
	.byte	'ccucon5',0
	.word	80398
	.byte	8,2,35,24,13
	.byte	'ccucon6',0
	.word	80398
	.byte	8,2,35,32,13
	.byte	'ccucon7',0
	.word	80398
	.byte	8,2,35,40,0,28
	.byte	'IfxScuCcu_ClockDistributionConfig',0,9,252,5,3
	.word	80688
	.byte	26,9,128,6,9,8,13
	.byte	'value',0
	.word	1636
	.byte	4,2,35,0,13
	.byte	'mask',0
	.word	1636
	.byte	4,2,35,4,0,28
	.byte	'IfxScuCcu_FlashWaitstateConfig',0,9,132,6,3
	.word	80840
	.byte	3
	.word	80586
	.byte	26,9,137,6,9,16,13
	.byte	'numOfPllDividerSteps',0
	.word	669
	.byte	1,2,35,0,13
	.byte	'pllDividerStep',0
	.word	80916
	.byte	4,2,35,4,13
	.byte	'pllInitialStep',0
	.word	80469
	.byte	8,2,35,8,0,28
	.byte	'IfxScuCcu_SysPllConfig',0,9,142,6,3
	.word	80921
	.byte	28
	.byte	'IfxSrc_Tos',0,12,74,3
	.word	10265
	.byte	28
	.byte	'IfxStm_Comparator',0,13,155,1,3
	.word	14588
	.byte	28
	.byte	'IfxStm_ComparatorInterrupt',0,13,163,1,3
	.word	14639
	.byte	28
	.byte	'IfxStm_ComparatorOffset',0,13,201,1,3
	.word	14712
	.byte	28
	.byte	'IfxStm_ComparatorSize',0,13,239,1,3
	.word	15637
	.byte	15,13,244,1,9,1,16
	.byte	'IfxStm_SleepMode_enable',0,0,16
	.byte	'IfxStm_SleepMode_disable',0,1,0,28
	.byte	'IfxStm_SleepMode',0,13,248,1,3
	.word	81184
	.byte	15,13,252,1,9,1,16
	.byte	'IfxStm_SuspendMode_none',0,0,16
	.byte	'IfxStm_SuspendMode_hard',0,1,16
	.byte	'IfxStm_SuspendMode_soft',0,2,0,28
	.byte	'IfxStm_SuspendMode',0,13,129,2,3
	.word	81270
	.byte	28
	.byte	'IfxStm_CompareConfig',0,13,150,2,3
	.word	16626
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L68:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,21,0,54,15,39,12,0,0,3,15,0,73,19,0,0,4,46,1,3,8,32,13
	.byte	58,15,59,15,57,15,54,15,39,12,0,0,5,5,0,3,8,58,15,59,15,57,15,73,19,0,0,6,11,0,0,0,7,36,0,3,8,11,15,62
	.byte	15,0,0,8,46,1,3,8,32,13,58,15,59,15,57,15,73,19,54,15,39,12,0,0,9,59,0,3,8,0,0,10,19,1,3,8,58,15,59,15
	.byte	57,15,11,15,0,0,11,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,12,23,1,58,15,59,15,57,15,11,15,0,0,13,13
	.byte	0,3,8,73,19,11,15,56,9,0,0,14,53,0,73,19,0,0,15,4,1,58,15,59,15,57,15,11,15,0,0,16,40,0,3,8,28,13,0,0
	.byte	17,11,1,0,0,18,1,1,11,15,73,19,0,0,19,33,0,47,15,0,0,20,46,1,49,19,0,0,21,5,0,49,19,0,0,22,46,1,3,8,58
	.byte	15,59,15,57,15,54,15,39,12,63,12,60,12,0,0,23,46,0,3,8,58,15,59,15,57,15,73,19,54,15,39,12,63,12,60,12
	.byte	0,0,24,29,1,49,19,0,0,25,11,0,49,19,0,0,26,19,1,58,15,59,15,57,15,11,15,0,0,27,38,0,73,19,0,0,28,22,0
	.byte	3,8,58,15,59,15,57,15,73,19,0,0,29,21,0,54,15,0,0,30,52,0,3,8,58,15,59,15,57,15,73,19,63,12,60,12,0,0
	.byte	0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L69:
	.word	.L291-.L290
.L290:
	.half	3
	.word	.L293-.L292
.L292:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg',0
	.byte	0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_Intrinsics.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Scu\\Std\\IfxScuWdt.h',0
	.byte	0,0,0
	.byte	'IfxScu_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxCpu_cfg.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Port\\Std\\IfxPort.h',0
	.byte	0,0,0
	.byte	'IfxPort_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Scu\\Std\\IfxScuCcu.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Src\\Std\\IfxSrc.h',0
	.byte	0,0,0
	.byte	'IfxSrc_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxSrc_cfg.h',0
	.byte	0,0,0
	.byte	'..\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Stm\\Std\\IfxStm.h',0,0,0,0
	.byte	'IfxStm_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxStm_cfg.h',0
	.byte	0,0,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Stm/Std/IfxStm.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Platform_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Ifx_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxScu_cfg.h',0
	.byte	0,0,0
	.byte	'IfxCpu_regdef.h',0,1,0,0
	.byte	'IfxFlash_regdef.h',0,1,0,0,0
.L293:
.L291:
	.sdecl	'.debug_info',debug,cluster('IfxStm_disableModule')
	.sect	'.debug_info'
.L70:
	.word	322
	.half	3
	.word	.L71
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Stm/Std/IfxStm.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L73,.L72
	.byte	2
	.word	.L66
	.byte	3
	.byte	'IfxStm_disableModule',0,1,81,6,1,1,1
	.word	.L45,.L135,.L44
	.byte	4
	.byte	'stm',0,1,81,36
	.word	.L136,.L137
	.byte	5
	.word	.L45,.L135
	.byte	6
	.byte	'passwd',0,1,83,12
	.word	.L138,.L139
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxStm_disableModule')
	.sect	'.debug_abbrev'
.L71:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxStm_disableModule')
	.sect	'.debug_line'
.L72:
	.word	.L295-.L294
.L294:
	.half	3
	.word	.L297-.L296
.L296:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Stm/Std/IfxStm.c',0,0,0,0,0
.L297:
	.byte	5,6,7,0,5,2
	.word	.L45
	.byte	3,208,0,1,5,53,9
	.half	.L239-.L45
	.byte	3,2,1,5,19,9
	.half	.L238-.L239
	.byte	1,5,31,9
	.half	.L241-.L238
	.byte	3,2,1,5,15,9
	.half	.L242-.L241
	.byte	3,1,1,5,21,9
	.half	.L298-.L242
	.byte	1,5,29,9
	.half	.L299-.L298
	.byte	3,1,1,5,1,9
	.half	.L244-.L299
	.byte	3,1,1,7,9
	.half	.L74-.L244
	.byte	0,1,1
.L295:
	.sdecl	'.debug_ranges',debug,cluster('IfxStm_disableModule')
	.sect	'.debug_ranges'
.L73:
	.word	-1,.L45,0,.L74-.L45,0,0
	.sdecl	'.debug_info',debug,cluster('IfxStm_enableOcdsSuspend')
	.sect	'.debug_info'
.L75:
	.word	323
	.half	3
	.word	.L76
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Stm/Std/IfxStm.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L78,.L77
	.byte	2
	.word	.L66
	.byte	3
	.byte	'IfxStm_enableOcdsSuspend',0,1,104,6,1,1,1
	.word	.L49,.L140,.L48
	.byte	4
	.byte	'stm',0,1,104,40
	.word	.L136,.L141
	.byte	5
	.word	.L49,.L140
	.byte	6
	.byte	'ocs',0,1,106,17
	.word	.L142,.L143
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxStm_enableOcdsSuspend')
	.sect	'.debug_abbrev'
.L76:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxStm_enableOcdsSuspend')
	.sect	'.debug_line'
.L77:
	.word	.L301-.L300
.L300:
	.half	3
	.word	.L303-.L302
.L302:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Stm/Std/IfxStm.c',0,0,0,0,0
.L303:
	.byte	5,26,7,0,5,2
	.word	.L49
	.byte	3,233,0,1,5,24,9
	.half	.L245-.L49
	.byte	3,2,1,5,22,9
	.half	.L304-.L245
	.byte	1,5,24,9
	.half	.L305-.L304
	.byte	3,1,1,5,22,9
	.half	.L306-.L305
	.byte	1,9
	.half	.L307-.L306
	.byte	3,1,1,5,15,9
	.half	.L308-.L307
	.byte	3,1,1,5,22,9
	.half	.L246-.L308
	.byte	1,5,1,9
	.half	.L309-.L246
	.byte	3,1,1,7,9
	.half	.L79-.L309
	.byte	0,1,1
.L301:
	.sdecl	'.debug_ranges',debug,cluster('IfxStm_enableOcdsSuspend')
	.sect	'.debug_ranges'
.L78:
	.word	-1,.L49,0,.L79-.L49,0,0
	.sdecl	'.debug_info',debug,cluster('IfxStm_getAddress')
	.sect	'.debug_info'
.L80:
	.word	323
	.half	3
	.word	.L81
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Stm/Std/IfxStm.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L83,.L82
	.byte	2
	.word	.L66
	.byte	3
	.byte	'IfxStm_getAddress',0,1,115,10
	.word	.L136
	.byte	1,1,1
	.word	.L51,.L144,.L50
	.byte	4
	.byte	'stm',0,1,115,41
	.word	.L145,.L146
	.byte	5
	.word	.L51,.L144
	.byte	6
	.byte	'module',0,1,117,14
	.word	.L136,.L147
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxStm_getAddress')
	.sect	'.debug_abbrev'
.L81:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxStm_getAddress')
	.sect	'.debug_line'
.L82:
	.word	.L311-.L310
.L310:
	.half	3
	.word	.L313-.L312
.L312:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Stm/Std/IfxStm.c',0,0,0,0,0
.L313:
	.byte	5,5,7,0,5,2
	.word	.L51
	.byte	3,246,0,1,5,48,7,9
	.half	.L314-.L51
	.byte	3,2,1,5,29,9
	.half	.L315-.L314
	.byte	1,5,48,9
	.half	.L316-.L315
	.byte	1,5,53,9
	.half	.L317-.L316
	.byte	1,5,60,9
	.half	.L247-.L317
	.byte	1,5,16,9
	.half	.L10-.L247
	.byte	3,4,1,5,5,9
	.half	.L11-.L10
	.byte	3,3,1,5,1,9
	.half	.L12-.L11
	.byte	3,1,1,7,9
	.half	.L84-.L12
	.byte	0,1,1
.L311:
	.sdecl	'.debug_ranges',debug,cluster('IfxStm_getAddress')
	.sect	'.debug_ranges'
.L83:
	.word	-1,.L51,0,.L84-.L51,0,0
	.sdecl	'.debug_info',debug,cluster('IfxStm_getIndex')
	.sect	'.debug_info'
.L85:
	.word	343
	.half	3
	.word	.L86
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Stm/Std/IfxStm.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L88,.L87
	.byte	2
	.word	.L66
	.byte	3
	.byte	'IfxStm_getIndex',0,1,132,1,14
	.word	.L145
	.byte	1,1,1
	.word	.L53,.L148,.L52
	.byte	4
	.byte	'stm',0,1,132,1,39
	.word	.L136,.L149
	.byte	5
	.word	.L53,.L148
	.byte	6
	.byte	'index',0,1,134,1,18
	.word	.L150,.L151
	.byte	6
	.byte	'result',0,1,135,1,18
	.word	.L145,.L152
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxStm_getIndex')
	.sect	'.debug_abbrev'
.L86:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxStm_getIndex')
	.sect	'.debug_line'
.L87:
	.word	.L319-.L318
.L318:
	.half	3
	.word	.L321-.L320
.L320:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Stm/Std/IfxStm.c',0,0,0,0,0
.L321:
	.byte	5,12,7,0,5,2
	.word	.L53
	.byte	3,136,1,1,5,16,9
	.half	.L248-.L53
	.byte	3,2,1,5,47,9
	.half	.L249-.L248
	.byte	1,5,32,9
	.half	.L14-.L249
	.byte	3,2,1,5,13,9
	.half	.L322-.L14
	.byte	1,5,32,9
	.half	.L323-.L322
	.byte	1,5,39,9
	.half	.L324-.L323
	.byte	1,5,9,9
	.half	.L325-.L324
	.byte	1,5,55,7,9
	.half	.L326-.L325
	.byte	3,2,1,5,36,9
	.half	.L327-.L326
	.byte	1,5,55,9
	.half	.L328-.L327
	.byte	1,5,62,9
	.half	.L329-.L328
	.byte	1,5,22,9
	.half	.L330-.L329
	.byte	1,5,13,9
	.half	.L331-.L330
	.byte	3,1,1,5,54,9
	.half	.L15-.L331
	.byte	3,123,1,5,47,9
	.half	.L13-.L15
	.byte	1,5,5,7,9
	.half	.L16-.L13
	.byte	3,9,1,5,1,9
	.half	.L17-.L16
	.byte	3,1,1,7,9
	.half	.L89-.L17
	.byte	0,1,1
.L319:
	.sdecl	'.debug_ranges',debug,cluster('IfxStm_getIndex')
	.sect	'.debug_ranges'
.L88:
	.word	-1,.L53,0,.L89-.L53,0,0
	.sdecl	'.debug_info',debug,cluster('IfxStm_clearCompareFlag')
	.sect	'.debug_info'
.L90:
	.word	328
	.half	3
	.word	.L91
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Stm/Std/IfxStm.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L93,.L92
	.byte	2
	.word	.L66
	.byte	3
	.byte	'IfxStm_clearCompareFlag',0,1,55,6,1,1,1
	.word	.L41,.L153,.L40
	.byte	4
	.byte	'stm',0,1,55,39
	.word	.L136,.L154
	.byte	4
	.byte	'comparator',0,1,55,62
	.word	.L155,.L156
	.byte	5
	.word	.L41,.L153
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxStm_clearCompareFlag')
	.sect	'.debug_abbrev'
.L91:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxStm_clearCompareFlag')
	.sect	'.debug_line'
.L92:
	.word	.L333-.L332
.L332:
	.half	3
	.word	.L335-.L334
.L334:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Stm/Std/IfxStm.c',0,0,0,0,0
.L335:
	.byte	5,5,7,0,5,2
	.word	.L41
	.byte	3,56,1,5,20,7,9
	.half	.L336-.L41
	.byte	3,2,1,5,29,9
	.half	.L337-.L336
	.byte	1,5,33,9
	.half	.L338-.L337
	.byte	1,5,10,9
	.half	.L2-.L338
	.byte	3,2,1,5,20,7,9
	.half	.L339-.L2
	.byte	3,2,1,5,29,9
	.half	.L340-.L339
	.byte	1,5,1,9
	.half	.L3-.L340
	.byte	3,2,1,7,9
	.half	.L94-.L3
	.byte	0,1,1
.L333:
	.sdecl	'.debug_ranges',debug,cluster('IfxStm_clearCompareFlag')
	.sect	'.debug_ranges'
.L93:
	.word	-1,.L41,0,.L94-.L41,0,0
	.sdecl	'.debug_info',debug,cluster('IfxStm_disableComparatorInterrupt')
	.sect	'.debug_info'
.L95:
	.word	338
	.half	3
	.word	.L96
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Stm/Std/IfxStm.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L98,.L97
	.byte	2
	.word	.L66
	.byte	3
	.byte	'IfxStm_disableComparatorInterrupt',0,1,68,6,1,1,1
	.word	.L43,.L157,.L42
	.byte	4
	.byte	'stm',0,1,68,49
	.word	.L136,.L158
	.byte	4
	.byte	'comparator',0,1,68,72
	.word	.L155,.L159
	.byte	5
	.word	.L43,.L157
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxStm_disableComparatorInterrupt')
	.sect	'.debug_abbrev'
.L96:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxStm_disableComparatorInterrupt')
	.sect	'.debug_line'
.L97:
	.word	.L342-.L341
.L341:
	.half	3
	.word	.L344-.L343
.L343:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Stm/Std/IfxStm.c',0,0,0,0,0
.L344:
	.byte	5,5,7,0,5,2
	.word	.L43
	.byte	3,197,0,1,5,19,7,9
	.half	.L345-.L43
	.byte	3,2,1,5,27,9
	.half	.L346-.L345
	.byte	1,5,31,9
	.half	.L347-.L346
	.byte	1,5,19,9
	.half	.L5-.L347
	.byte	3,4,1,5,27,9
	.half	.L348-.L5
	.byte	1,5,1,9
	.half	.L6-.L348
	.byte	3,2,1,7,9
	.half	.L99-.L6
	.byte	0,1,1
.L342:
	.sdecl	'.debug_ranges',debug,cluster('IfxStm_disableComparatorInterrupt')
	.sect	'.debug_ranges'
.L98:
	.word	-1,.L43,0,.L99-.L43,0,0
	.sdecl	'.debug_info',debug,cluster('IfxStm_enableComparatorInterrupt')
	.sect	'.debug_info'
.L100:
	.word	337
	.half	3
	.word	.L101
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Stm/Std/IfxStm.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L103,.L102
	.byte	2
	.word	.L66
	.byte	3
	.byte	'IfxStm_enableComparatorInterrupt',0,1,91,6,1,1,1
	.word	.L47,.L160,.L46
	.byte	4
	.byte	'stm',0,1,91,48
	.word	.L136,.L161
	.byte	4
	.byte	'comparator',0,1,91,71
	.word	.L155,.L162
	.byte	5
	.word	.L47,.L160
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxStm_enableComparatorInterrupt')
	.sect	'.debug_abbrev'
.L101:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxStm_enableComparatorInterrupt')
	.sect	'.debug_line'
.L102:
	.word	.L350-.L349
.L349:
	.half	3
	.word	.L352-.L351
.L351:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Stm/Std/IfxStm.c',0,0,0,0,0
.L352:
	.byte	5,5,7,0,5,2
	.word	.L47
	.byte	3,220,0,1,5,19,7,9
	.half	.L353-.L47
	.byte	3,2,1,5,27,9
	.half	.L354-.L353
	.byte	1,5,31,9
	.half	.L355-.L354
	.byte	1,5,10,9
	.half	.L7-.L355
	.byte	3,2,1,5,19,7,9
	.half	.L356-.L7
	.byte	3,2,1,5,27,9
	.half	.L357-.L356
	.byte	1,5,1,9
	.half	.L8-.L357
	.byte	3,2,1,7,9
	.half	.L104-.L8
	.byte	0,1,1
.L350:
	.sdecl	'.debug_ranges',debug,cluster('IfxStm_enableComparatorInterrupt')
	.sect	'.debug_ranges'
.L103:
	.word	-1,.L47,0,.L104-.L47,0,0
	.sdecl	'.debug_info',debug,cluster('IfxStm_getSrcPointer')
	.sect	'.debug_info'
.L105:
	.word	352
	.half	3
	.word	.L106
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Stm/Std/IfxStm.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L108,.L107
	.byte	2
	.word	.L66
	.byte	3
	.byte	'IfxStm_getSrcPointer',0,1,152,1,24
	.word	.L163
	.byte	1,1,1
	.word	.L55,.L164,.L54
	.byte	4
	.byte	'stm',0,1,152,1,54
	.word	.L136,.L165
	.byte	4
	.byte	'comparator',0,1,152,1,77
	.word	.L155,.L166
	.byte	5
	.word	.L55,.L164
	.byte	6
	.byte	'index',0,1,154,1,18
	.word	.L145,.L167
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxStm_getSrcPointer')
	.sect	'.debug_abbrev'
.L106:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxStm_getSrcPointer')
	.sect	'.debug_line'
.L107:
	.word	.L359-.L358
.L358:
	.half	3
	.word	.L361-.L360
.L360:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Stm/Std/IfxStm.c',0,0,0,0,0
.L361:
	.byte	5,24,7,0,5,2
	.word	.L55
	.byte	3,151,1,1,5,29,9
	.half	.L251-.L55
	.byte	3,3,1,5,12,9
	.half	.L250-.L251
	.byte	3,1,1,5,67,7,9
	.half	.L362-.L250
	.byte	1,5,79,9
	.half	.L363-.L362
	.byte	1,5,100,9
	.half	.L18-.L363
	.byte	1,5,107,9
	.half	.L364-.L18
	.byte	1,5,5,9
	.half	.L19-.L364
	.byte	1,5,1,9
	.half	.L20-.L19
	.byte	3,1,1,7,9
	.half	.L109-.L20
	.byte	0,1,1
.L359:
	.sdecl	'.debug_ranges',debug,cluster('IfxStm_getSrcPointer')
	.sect	'.debug_ranges'
.L108:
	.word	-1,.L55,0,.L109-.L55,0,0
	.sdecl	'.debug_info',debug,cluster('IfxStm_initCompare')
	.sect	'.debug_info'
.L110:
	.word	798
	.half	3
	.word	.L111
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Stm/Std/IfxStm.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L113,.L112
	.byte	2
	.word	.L66
	.byte	3
	.byte	'IfxStm_initCompare',0,1,160,1,9
	.word	.L168
	.byte	1,1,1
	.word	.L57,.L169,.L56
	.byte	4
	.byte	'stm',0,1,160,1,37
	.word	.L136,.L170
	.byte	4
	.byte	'config',0,1,160,1,70
	.word	.L171,.L172
	.byte	5
	.word	.L57,.L169
	.byte	6
	.byte	'index',0,1,162,1,19
	.word	.L173,.L174
	.byte	6
	.byte	'result',0,1,163,1,19
	.word	.L168,.L175
	.byte	6
	.byte	'comcon',0,1,164,1,19
	.word	.L176,.L177
	.byte	6
	.byte	'icr',0,1,165,1,19
	.word	.L178,.L179
	.byte	5
	.word	.L180,.L25
	.byte	6
	.byte	'srcr',0,1,195,1,32
	.word	.L163,.L181
	.byte	7
	.word	.L182,.L183,.L184
	.byte	8
	.word	.L185,.L186
	.byte	8
	.word	.L187,.L188
	.byte	8
	.word	.L189,.L190
	.byte	9
	.word	.L191,.L183,.L184
	.byte	7
	.word	.L192,.L193,.L184
	.byte	8
	.word	.L194,.L195
	.byte	10
	.word	.L196,.L193,.L184
	.byte	0,0,0,7
	.word	.L197,.L184,.L25
	.byte	8
	.word	.L198,.L199
	.byte	10
	.word	.L200,.L184,.L25
	.byte	0,0,7
	.word	.L201,.L202,.L29
	.byte	8
	.word	.L203,.L204
	.byte	8
	.word	.L205,.L206
	.byte	9
	.word	.L207,.L202,.L29
	.byte	6
	.byte	'now',0,2,234,4,12
	.word	.L208,.L209
	.byte	7
	.word	.L210,.L202,.L28
	.byte	8
	.word	.L211,.L212
	.byte	9
	.word	.L213,.L202,.L28
	.byte	6
	.byte	'result',0,2,164,4,12
	.word	.L208,.L214
	.byte	0,0,0,0,7
	.word	.L201,.L215,.L31
	.byte	8
	.word	.L203,.L204
	.byte	8
	.word	.L205,.L206
	.byte	9
	.word	.L207,.L215,.L31
	.byte	6
	.byte	'now',0,2,234,4,12
	.word	.L208,.L216
	.byte	7
	.word	.L210,.L215,.L30
	.byte	8
	.word	.L211,.L212
	.byte	9
	.word	.L213,.L215,.L30
	.byte	6
	.byte	'result',0,2,164,4,12
	.word	.L208,.L217
	.byte	0,0,0,0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxStm_initCompare')
	.sect	'.debug_abbrev'
.L111:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,5,0,49,16,2,6,0,0,9,11,1,49,16,17
	.byte	1,18,1,0,0,10,11,0,49,16,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxStm_initCompare')
	.sect	'.debug_line'
.L112:
	.word	.L366-.L365
.L365:
	.half	3
	.word	.L368-.L367
.L367:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Stm/Std/IfxStm.c',0,0,0,0
	.byte	'..\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Stm\\Std\\IfxStm.h',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Src\\Std\\IfxSrc.h',0
	.byte	0,0,0,0
.L368:
	.byte	5,9,7,0,5,2
	.word	.L57
	.byte	3,159,1,1,5,31,9
	.half	.L256-.L57
	.byte	3,4,1,9
	.half	.L257-.L256
	.byte	3,1,1,5,15,9
	.half	.L258-.L257
	.byte	3,2,1,5,5,9
	.half	.L369-.L258
	.byte	1,5,34,7,9
	.half	.L370-.L369
	.byte	3,2,1,5,26,9
	.half	.L371-.L370
	.byte	1,5,34,9
	.half	.L372-.L371
	.byte	3,1,1,5,26,9
	.half	.L373-.L372
	.byte	1,5,34,9
	.half	.L374-.L373
	.byte	3,1,1,5,26,9
	.half	.L375-.L374
	.byte	1,9
	.half	.L376-.L375
	.byte	3,1,1,5,47,9
	.half	.L259-.L376
	.byte	3,125,1,5,20,9
	.half	.L21-.L259
	.byte	3,5,1,5,10,9
	.half	.L377-.L21
	.byte	1,5,34,7,9
	.half	.L378-.L377
	.byte	3,2,1,5,26,9
	.half	.L379-.L378
	.byte	1,5,34,9
	.half	.L380-.L379
	.byte	3,1,1,5,26,9
	.half	.L381-.L380
	.byte	1,5,34,9
	.half	.L382-.L381
	.byte	3,1,1,5,26,9
	.half	.L383-.L382
	.byte	1,9
	.half	.L384-.L383
	.byte	3,1,1,5,47,9
	.half	.L260-.L384
	.byte	3,125,1,5,16,9
	.half	.L23-.L260
	.byte	3,8,1,5,18,9
	.half	.L22-.L23
	.byte	3,3,1,9
	.half	.L385-.L22
	.byte	3,1,1,5,29,9
	.half	.L386-.L385
	.byte	3,3,1,5,15,9
	.half	.L254-.L386
	.byte	3,2,1,5,5,9
	.half	.L387-.L254
	.byte	1,5,19,7,9
	.half	.L180-.L387
	.byte	3,4,1,5,9,9
	.half	.L388-.L180
	.byte	1,5,36,7,9
	.half	.L389-.L388
	.byte	3,2,1,5,40,9
	.half	.L390-.L389
	.byte	1,5,52,9
	.half	.L262-.L390
	.byte	1,5,36,9
	.half	.L26-.L262
	.byte	3,4,1,5,40,9
	.half	.L391-.L26
	.byte	1,5,47,9
	.half	.L263-.L391
	.byte	1,5,33,9
	.half	.L27-.L263
	.byte	3,3,1,5,56,9
	.half	.L392-.L27
	.byte	1,4,3,5,11,9
	.half	.L183-.L392
	.byte	3,198,0,1,5,19,9
	.half	.L393-.L183
	.byte	1,5,17,9
	.half	.L394-.L393
	.byte	1,5,11,9
	.half	.L395-.L394
	.byte	3,1,1,5,17,9
	.half	.L396-.L395
	.byte	1,5,11,9
	.half	.L193-.L396
	.byte	3,103,1,5,17,9
	.half	.L397-.L193
	.byte	1,5,11,9
	.half	.L184-.L397
	.byte	3,18,1,5,16,9
	.half	.L398-.L184
	.byte	1,4,1,5,78,9
	.half	.L25-.L398
	.byte	3,69,1,4,2,5,24,9
	.half	.L202-.L25
	.byte	3,211,2,1,5,32,9
	.half	.L264-.L202
	.byte	3,1,1,5,36,9
	.half	.L399-.L264
	.byte	1,5,12,9
	.half	.L261-.L399
	.byte	1,5,5,9
	.half	.L266-.L261
	.byte	3,2,1,5,25,9
	.half	.L28-.L266
	.byte	3,197,0,1,5,5,9
	.half	.L267-.L28
	.byte	1,4,1,5,20,9
	.half	.L29-.L267
	.byte	3,229,124,1,5,13,9
	.half	.L400-.L29
	.byte	1,5,36,9
	.half	.L401-.L400
	.byte	1,5,40,9
	.half	.L402-.L401
	.byte	3,4,1,5,49,9
	.half	.L269-.L402
	.byte	3,2,1,5,78,9
	.half	.L271-.L269
	.byte	3,3,1,4,2,5,24,9
	.half	.L215-.L271
	.byte	3,202,2,1,5,32,9
	.half	.L272-.L215
	.byte	3,1,1,5,36,9
	.half	.L403-.L272
	.byte	1,5,12,9
	.half	.L404-.L403
	.byte	1,5,5,9
	.half	.L274-.L404
	.byte	3,2,1,5,25,9
	.half	.L30-.L274
	.byte	3,197,0,1,5,5,9
	.half	.L275-.L30
	.byte	1,4,1,5,20,9
	.half	.L31-.L275
	.byte	3,238,124,1,5,13,9
	.half	.L405-.L31
	.byte	1,5,103,9
	.half	.L406-.L405
	.byte	1,5,95,9
	.half	.L407-.L406
	.byte	1,5,36,9
	.half	.L408-.L407
	.byte	1,5,5,9
	.half	.L409-.L408
	.byte	3,2,1,5,1,9
	.half	.L32-.L409
	.byte	3,1,1,7,9
	.half	.L114-.L32
	.byte	0,1,1
.L366:
	.sdecl	'.debug_ranges',debug,cluster('IfxStm_initCompare')
	.sect	'.debug_ranges'
.L113:
	.word	-1,.L57,0,.L114-.L57,0,0
	.sdecl	'.debug_info',debug,cluster('IfxStm_initCompareConfig')
	.sect	'.debug_info'
.L115:
	.word	311
	.half	3
	.word	.L116
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Stm/Std/IfxStm.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L118,.L117
	.byte	2
	.word	.L66
	.byte	3
	.byte	'IfxStm_initCompareConfig',0,1,226,1,6,1,1,1
	.word	.L59,.L218,.L58
	.byte	4
	.byte	'config',0,1,226,1,53
	.word	.L219,.L220
	.byte	5
	.word	.L59,.L218
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxStm_initCompareConfig')
	.sect	'.debug_abbrev'
.L116:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxStm_initCompareConfig')
	.sect	'.debug_line'
.L117:
	.word	.L411-.L410
.L410:
	.half	3
	.word	.L413-.L412
.L412:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Stm/Std/IfxStm.c',0,0,0,0,0
.L413:
	.byte	5,35,7,0,5,2
	.word	.L59
	.byte	3,227,1,1,5,33,9
	.half	.L414-.L59
	.byte	1,5,35,9
	.half	.L415-.L414
	.byte	3,1,1,5,33,9
	.half	.L416-.L415
	.byte	1,5,35,9
	.half	.L417-.L416
	.byte	3,1,1,5,33,9
	.half	.L418-.L417
	.byte	1,5,35,9
	.half	.L419-.L418
	.byte	3,1,1,5,33,9
	.half	.L420-.L419
	.byte	1,5,35,9
	.half	.L421-.L420
	.byte	3,1,1,5,33,9
	.half	.L422-.L421
	.byte	1,5,35,9
	.half	.L423-.L422
	.byte	3,1,1,5,33,9
	.half	.L424-.L423
	.byte	1,5,35,9
	.half	.L425-.L424
	.byte	3,1,1,5,33,9
	.half	.L426-.L425
	.byte	1,5,1,9
	.half	.L427-.L426
	.byte	3,1,1,7,9
	.half	.L119-.L427
	.byte	0,1,1
.L411:
	.sdecl	'.debug_ranges',debug,cluster('IfxStm_initCompareConfig')
	.sect	'.debug_ranges'
.L118:
	.word	-1,.L59,0,.L119-.L59,0,0
	.sdecl	'.debug_info',debug,cluster('IfxStm_isCompareFlagSet')
	.sect	'.debug_info'
.L120:
	.word	335
	.half	3
	.word	.L121
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Stm/Std/IfxStm.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L123,.L122
	.byte	2
	.word	.L66
	.byte	3
	.byte	'IfxStm_isCompareFlagSet',0,1,238,1,9
	.word	.L168
	.byte	1,1,1
	.word	.L61,.L221,.L60
	.byte	4
	.byte	'stm',0,1,238,1,42
	.word	.L136,.L222
	.byte	4
	.byte	'comparator',0,1,238,1,65
	.word	.L155,.L223
	.byte	5
	.word	.L61,.L221
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxStm_isCompareFlagSet')
	.sect	'.debug_abbrev'
.L121:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxStm_isCompareFlagSet')
	.sect	'.debug_line'
.L122:
	.word	.L429-.L428
.L428:
	.half	3
	.word	.L431-.L430
.L430:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Stm/Std/IfxStm.c',0,0,0,0,0
.L431:
	.byte	5,5,7,0,5,2
	.word	.L61
	.byte	3,239,1,1,5,26,7,9
	.half	.L432-.L61
	.byte	3,2,1,5,9,9
	.half	.L433-.L432
	.byte	1,5,26,9
	.half	.L33-.L433
	.byte	3,4,1,5,9,9
	.half	.L434-.L33
	.byte	1,5,1,9
	.half	.L34-.L434
	.byte	3,2,1,7,9
	.half	.L124-.L34
	.byte	0,1,1
.L429:
	.sdecl	'.debug_ranges',debug,cluster('IfxStm_isCompareFlagSet')
	.sect	'.debug_ranges'
.L123:
	.word	-1,.L61,0,.L124-.L61,0,0
	.sdecl	'.debug_info',debug,cluster('IfxStm_setCompareControl')
	.sect	'.debug_info'
.L125:
	.word	433
	.half	3
	.word	.L126
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Stm/Std/IfxStm.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L128,.L127
	.byte	2
	.word	.L66
	.byte	3
	.byte	'IfxStm_setCompareControl',0,1,143,2,6,1,1,1
	.word	.L65,.L224,.L64
	.byte	4
	.byte	'stm',0,1,143,2,40
	.word	.L136,.L225
	.byte	4
	.byte	'comparator',0,1,143,2,63
	.word	.L155,.L226
	.byte	4
	.byte	'offset',0,1,143,2,99
	.word	.L227,.L228
	.byte	4
	.byte	'size',0,1,143,2,129,1
	.word	.L229,.L230
	.byte	4
	.byte	'interrupt',0,1,143,2,162,1
	.word	.L231,.L232
	.byte	5
	.word	.L65,.L224
	.byte	6
	.byte	'comcon',0,1,145,2,19
	.word	.L176,.L233
	.byte	6
	.byte	'icr',0,1,146,2,19
	.word	.L178,.L234
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxStm_setCompareControl')
	.sect	'.debug_abbrev'
.L126:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxStm_setCompareControl')
	.sect	'.debug_line'
.L127:
	.word	.L436-.L435
.L435:
	.half	3
	.word	.L438-.L437
.L437:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Stm/Std/IfxStm.c',0,0,0,0,0
.L438:
	.byte	5,31,7,0,5,2
	.word	.L65
	.byte	3,144,2,1,9
	.half	.L288-.L65
	.byte	3,1,1,5,5,9
	.half	.L289-.L288
	.byte	3,2,1,5,26,7,9
	.half	.L439-.L289
	.byte	3,2,1,9
	.half	.L440-.L439
	.byte	3,1,1,9
	.half	.L441-.L440
	.byte	3,1,1,5,32,9
	.half	.L442-.L441
	.byte	3,126,1,5,26,9
	.half	.L38-.L442
	.byte	3,6,1,9
	.half	.L443-.L38
	.byte	3,1,1,9
	.half	.L444-.L443
	.byte	3,1,1,5,18,9
	.half	.L39-.L444
	.byte	3,3,1,9
	.half	.L445-.L39
	.byte	3,1,1,5,1,9
	.half	.L446-.L445
	.byte	3,1,1,7,9
	.half	.L129-.L446
	.byte	0,1,1
.L436:
	.sdecl	'.debug_ranges',debug,cluster('IfxStm_setCompareControl')
	.sect	'.debug_ranges'
.L128:
	.word	-1,.L65,0,.L129-.L65,0,0
	.sdecl	'.debug_info',debug,cluster('IfxStm_resetModule')
	.sect	'.debug_info'
.L130:
	.word	323
	.half	3
	.word	.L131
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Stm/Std/IfxStm.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L133,.L132
	.byte	2
	.word	.L66
	.byte	3
	.byte	'IfxStm_resetModule',0,1,251,1,6,1,1,1
	.word	.L63,.L235,.L62
	.byte	4
	.byte	'stm',0,1,251,1,34
	.word	.L136,.L236
	.byte	5
	.word	.L63,.L235
	.byte	6
	.byte	'passwd',0,1,253,1,12
	.word	.L138,.L237
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxStm_resetModule')
	.sect	'.debug_abbrev'
.L131:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxStm_resetModule')
	.sect	'.debug_line'
.L132:
	.word	.L448-.L447
.L447:
	.half	3
	.word	.L450-.L449
.L449:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Stm/Std/IfxStm.c',0,0,0,0,0
.L450:
	.byte	5,6,7,0,5,2
	.word	.L63
	.byte	3,250,1,1,5,53,9
	.half	.L278-.L63
	.byte	3,2,1,5,19,9
	.half	.L277-.L278
	.byte	1,5,31,9
	.half	.L280-.L277
	.byte	3,2,1,5,17,9
	.half	.L281-.L280
	.byte	3,1,1,5,22,9
	.half	.L451-.L281
	.byte	1,5,17,9
	.half	.L452-.L451
	.byte	3,1,1,5,22,9
	.half	.L453-.L452
	.byte	1,5,29,9
	.half	.L454-.L453
	.byte	3,1,1,5,37,9
	.half	.L283-.L454
	.byte	3,2,1,5,29,9
	.half	.L36-.L283
	.byte	1,5,37,9
	.half	.L455-.L36
	.byte	1,5,31,7,9
	.half	.L456-.L455
	.byte	3,4,1,5,19,9
	.half	.L285-.L456
	.byte	3,1,1,5,24,9
	.half	.L457-.L285
	.byte	1,5,29,9
	.half	.L458-.L457
	.byte	3,2,1,5,1,9
	.half	.L287-.L458
	.byte	3,1,1,7,9
	.half	.L134-.L287
	.byte	0,1,1
.L448:
	.sdecl	'.debug_ranges',debug,cluster('IfxStm_resetModule')
	.sect	'.debug_ranges'
.L133:
	.word	-1,.L63,0,.L134-.L63,0,0
	.sdecl	'.debug_loc',debug,cluster('IfxStm_clearCompareFlag')
	.sect	'.debug_loc'
.L40:
	.word	-1,.L41,0,.L153-.L41
	.half	2
	.byte	138,0
	.word	0,0
.L156:
	.word	-1,.L41,0,.L153-.L41
	.half	1
	.byte	84
	.word	0,0
.L154:
	.word	-1,.L41,0,.L153-.L41
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxStm_disableComparatorInterrupt')
	.sect	'.debug_loc'
.L42:
	.word	-1,.L43,0,.L157-.L43
	.half	2
	.byte	138,0
	.word	0,0
.L159:
	.word	-1,.L43,0,.L157-.L43
	.half	1
	.byte	84
	.word	0,0
.L158:
	.word	-1,.L43,0,.L157-.L43
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxStm_disableModule')
	.sect	'.debug_loc'
.L44:
	.word	-1,.L45,0,.L135-.L45
	.half	2
	.byte	138,0
	.word	0,0
.L139:
	.word	-1,.L45,.L238-.L45,.L240-.L45
	.half	1
	.byte	82
	.word	.L241-.L45,.L135-.L45
	.half	1
	.byte	88
	.word	.L240-.L45,.L242-.L45
	.half	1
	.byte	84
	.word	.L243-.L45,.L244-.L45
	.half	1
	.byte	84
	.word	0,0
.L137:
	.word	-1,.L45,0,.L238-.L45
	.half	1
	.byte	100
	.word	.L239-.L45,.L135-.L45
	.half	1
	.byte	111
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxStm_enableComparatorInterrupt')
	.sect	'.debug_loc'
.L46:
	.word	-1,.L47,0,.L160-.L47
	.half	2
	.byte	138,0
	.word	0,0
.L162:
	.word	-1,.L47,0,.L160-.L47
	.half	1
	.byte	84
	.word	0,0
.L161:
	.word	-1,.L47,0,.L160-.L47
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxStm_enableOcdsSuspend')
	.sect	'.debug_loc'
.L48:
	.word	-1,.L49,0,.L140-.L49
	.half	2
	.byte	138,0
	.word	0,0
.L143:
	.word	-1,.L49,.L245-.L49,.L246-.L49
	.half	1
	.byte	95
	.word	0,0
.L141:
	.word	-1,.L49,0,.L140-.L49
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxStm_getAddress')
	.sect	'.debug_loc'
.L50:
	.word	-1,.L51,0,.L144-.L51
	.half	2
	.byte	138,0
	.word	0,0
.L147:
	.word	-1,.L51,.L247-.L51,.L10-.L51
	.half	1
	.byte	98
	.word	.L11-.L51,.L144-.L51
	.half	1
	.byte	98
	.word	0,0
.L146:
	.word	-1,.L51,0,.L144-.L51
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxStm_getIndex')
	.sect	'.debug_loc'
.L52:
	.word	-1,.L53,0,.L148-.L53
	.half	2
	.byte	138,0
	.word	0,0
.L151:
	.word	-1,.L53,.L249-.L53,.L148-.L53
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L152:
	.word	-1,.L53,.L248-.L53,.L148-.L53
	.half	1
	.byte	82
	.word	0,0
.L149:
	.word	-1,.L53,0,.L148-.L53
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxStm_getSrcPointer')
	.sect	'.debug_loc'
.L54:
	.word	-1,.L55,0,.L164-.L55
	.half	2
	.byte	138,0
	.word	0,0
.L166:
	.word	-1,.L55,0,.L250-.L55
	.half	1
	.byte	84
	.word	.L251-.L55,.L252-.L55
	.half	1
	.byte	95
	.word	.L18-.L55,.L253-.L55
	.half	1
	.byte	95
	.word	0,0
.L167:
	.word	-1,.L55,.L250-.L55,.L164-.L55
	.half	1
	.byte	82
	.word	0,0
.L165:
	.word	-1,.L55,0,.L250-.L55
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxStm_initCompare')
	.sect	'.debug_loc'
.L56:
	.word	-1,.L57,0,.L169-.L57
	.half	2
	.byte	138,0
	.word	0,0
.L177:
	.word	-1,.L57,.L257-.L57,.L254-.L57
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L172:
	.word	-1,.L57,0,.L254-.L57
	.half	1
	.byte	101
	.word	.L256-.L57,.L169-.L57
	.half	1
	.byte	111
	.word	0,0
.L179:
	.word	-1,.L57,.L258-.L57,.L254-.L57
	.half	1
	.byte	81
	.word	0,0
.L174:
	.word	-1,.L57,.L254-.L57,.L261-.L57
	.half	1
	.byte	82
	.word	0,0
.L216:
	.word	0,0
.L209:
	.word	0,0
.L206:
	.word	0,0
.L190:
	.word	0,0
.L175:
	.word	-1,.L57,.L259-.L57,.L21-.L57
	.half	1
	.byte	88
	.word	.L260-.L57,.L23-.L57
	.half	1
	.byte	88
	.word	.L22-.L57,.L169-.L57
	.half	1
	.byte	88
	.word	.L276-.L57,.L169-.L57
	.half	1
	.byte	82
	.word	0,0
.L217:
	.word	-1,.L57,.L272-.L57,.L273-.L57
	.half	2
	.byte	144,32
	.word	.L274-.L57,.L275-.L57
	.half	2
	.byte	144,34
	.word	0,0
.L214:
	.word	-1,.L57,.L264-.L57,.L265-.L57
	.half	2
	.byte	144,32
	.word	.L266-.L57,.L267-.L57
	.half	2
	.byte	144,34
	.word	0,0
.L195:
	.word	0,0
.L199:
	.word	0,0
.L186:
	.word	0,0
.L181:
	.word	-1,.L57,.L262-.L57,.L26-.L57
	.half	1
	.byte	98
	.word	.L263-.L57,.L25-.L57
	.half	1
	.byte	98
	.word	0,0
.L170:
	.word	-1,.L57,0,.L254-.L57
	.half	1
	.byte	100
	.word	.L255-.L57,.L169-.L57
	.half	1
	.byte	108
	.word	.L268-.L57,.L269-.L57
	.half	1
	.byte	100
	.word	.L270-.L57,.L271-.L57
	.half	1
	.byte	100
	.word	0,0
.L212:
	.word	0,0
.L204:
	.word	0,0
.L188:
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxStm_initCompareConfig')
	.sect	'.debug_loc'
.L58:
	.word	-1,.L59,0,.L218-.L59
	.half	2
	.byte	138,0
	.word	0,0
.L220:
	.word	-1,.L59,0,.L218-.L59
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxStm_isCompareFlagSet')
	.sect	'.debug_loc'
.L60:
	.word	-1,.L61,0,.L221-.L61
	.half	2
	.byte	138,0
	.word	0,0
.L223:
	.word	-1,.L61,0,.L221-.L61
	.half	1
	.byte	84
	.word	0,0
.L222:
	.word	-1,.L61,0,.L221-.L61
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxStm_resetModule')
	.sect	'.debug_loc'
.L62:
	.word	-1,.L63,0,.L235-.L63
	.half	2
	.byte	138,0
	.word	0,0
.L237:
	.word	-1,.L63,.L277-.L63,.L279-.L63
	.half	1
	.byte	82
	.word	.L280-.L63,.L235-.L63
	.half	1
	.byte	88
	.word	.L279-.L63,.L281-.L63
	.half	1
	.byte	84
	.word	.L282-.L63,.L283-.L63
	.half	1
	.byte	84
	.word	.L284-.L63,.L285-.L63
	.half	1
	.byte	84
	.word	.L286-.L63,.L287-.L63
	.half	1
	.byte	84
	.word	0,0
.L236:
	.word	-1,.L63,0,.L277-.L63
	.half	1
	.byte	100
	.word	.L278-.L63,.L235-.L63
	.half	1
	.byte	111
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxStm_setCompareControl')
	.sect	'.debug_loc'
.L64:
	.word	-1,.L65,0,.L224-.L65
	.half	2
	.byte	138,0
	.word	0,0
.L233:
	.word	-1,.L65,.L288-.L65,.L224-.L65
	.half	1
	.byte	95
	.word	0,0
.L226:
	.word	-1,.L65,0,.L224-.L65
	.half	1
	.byte	84
	.word	0,0
.L234:
	.word	-1,.L65,.L289-.L65,.L224-.L65
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L232:
	.word	-1,.L65,0,.L224-.L65
	.half	1
	.byte	87
	.word	0,0
.L228:
	.word	-1,.L65,0,.L224-.L65
	.half	1
	.byte	85
	.word	0,0
.L230:
	.word	-1,.L65,0,.L224-.L65
	.half	1
	.byte	86
	.word	0,0
.L225:
	.word	-1,.L65,0,.L224-.L65
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L459:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('IfxStm_clearCompareFlag')
	.sect	'.debug_frame'
	.word	24
	.word	.L459,.L41,.L153-.L41
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxStm_disableComparatorInterrupt')
	.sect	'.debug_frame'
	.word	24
	.word	.L459,.L43,.L157-.L43
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxStm_disableModule')
	.sect	'.debug_frame'
	.word	12
	.word	.L459,.L45,.L135-.L45
	.sdecl	'.debug_frame',debug,cluster('IfxStm_enableComparatorInterrupt')
	.sect	'.debug_frame'
	.word	24
	.word	.L459,.L47,.L160-.L47
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxStm_enableOcdsSuspend')
	.sect	'.debug_frame'
	.word	24
	.word	.L459,.L49,.L140-.L49
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxStm_getAddress')
	.sect	'.debug_frame'
	.word	24
	.word	.L459,.L51,.L144-.L51
	.byte	8,19,8,20,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxStm_getIndex')
	.sect	'.debug_frame'
	.word	24
	.word	.L459,.L53,.L148-.L53
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxStm_getSrcPointer')
	.sect	'.debug_frame'
	.word	12
	.word	.L459,.L55,.L164-.L55
	.sdecl	'.debug_frame',debug,cluster('IfxStm_initCompare')
	.sect	'.debug_frame'
	.word	12
	.word	.L459,.L57,.L169-.L57
	.sdecl	'.debug_frame',debug,cluster('IfxStm_initCompareConfig')
	.sect	'.debug_frame'
	.word	24
	.word	.L459,.L59,.L218-.L59
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxStm_isCompareFlagSet')
	.sect	'.debug_frame'
	.word	24
	.word	.L459,.L61,.L221-.L61
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxStm_resetModule')
	.sect	'.debug_frame'
	.word	12
	.word	.L459,.L63,.L235-.L63
	.sdecl	'.debug_frame',debug,cluster('IfxStm_setCompareControl')
	.sect	'.debug_frame'
	.word	24
	.word	.L459,.L65,.L224-.L65
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	; Module end
