	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc21996a --dep-file=Ifx_Cf32.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2\\\\xf_asr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_Cf32.src ../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_Cf32.c"
	.compiler_name		"ctc"
	;source	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_Cf32.c'

	
$TC16X
	
	.sdecl	'.text.Ifx_Cf32.CplxVecRst_f32',code,cluster('CplxVecRst_f32')
	.sect	'.text.Ifx_Cf32.CplxVecRst_f32'
	.align	2
	
	.global	CplxVecRst_f32
; Function CplxVecRst_f32
.L60:
CplxVecRst_f32:	.type	func
	j	.L2
.L3:
	mov	d15,#0
	st.w	[a4]4,d15
.L384:
	st.w	[a4],d15
.L385:
	lea	a4,[a4]8
.L386:
	add	d4,#-1
.L2:
	jge	d4,#1,.L3
.L387:
	ret
.L197:
	
__CplxVecRst_f32_function_end:
	.size	CplxVecRst_f32,__CplxVecRst_f32_function_end-CplxVecRst_f32
.L108:
	; End of function
	
	.sdecl	'.text.Ifx_Cf32.CplxVecCpy_f32S',code,cluster('CplxVecCpy_f32S')
	.sect	'.text.Ifx_Cf32.CplxVecCpy_f32S'
	.align	2
	
	.global	CplxVecCpy_f32S
; Function CplxVecCpy_f32S
.L62:
CplxVecCpy_f32S:	.type	func
	j	.L4
.L5:
	mov	d15,#0
.L373:
	st.w	[a4]4,d15
.L374:
	ld.h	d15,[a5]
	itof	d15,d15
.L375:
	st.w	[a4],d15
.L376:
	lea	a4,[a4]8
.L377:
	mul	d15,d5,#2
	addsc.a	a5,a5,d15,#0
.L378:
	add	d4,#-1
.L4:
	jge	d4,#1,.L5
.L379:
	ret
.L189:
	
__CplxVecCpy_f32S_function_end:
	.size	CplxVecCpy_f32S,__CplxVecCpy_f32S_function_end-CplxVecCpy_f32S
.L103:
	; End of function
	
	.sdecl	'.text.Ifx_Cf32.CplxVecCpy_f32',code,cluster('CplxVecCpy_f32')
	.sect	'.text.Ifx_Cf32.CplxVecCpy_f32'
	.align	2
	
	.global	CplxVecCpy_f32
; Function CplxVecCpy_f32
.L64:
CplxVecCpy_f32:	.type	func
	j	.L6
.L7:
	ld.da	a2/a3,[a5]0
.L392:
	st.da	[a4]0,a2/a3
.L393:
	lea	a4,[a4]8
.L394:
	lea	a5,[a5]8
.L395:
	add	d4,#-1
.L6:
	jge	d4,#1,.L7
.L396:
	ret
.L200:
	
__CplxVecCpy_f32_function_end:
	.size	CplxVecCpy_f32,__CplxVecCpy_f32_function_end-CplxVecCpy_f32
.L113:
	; End of function
	
	.sdecl	'.text.Ifx_Cf32.CplxVecPwr_f32',code,cluster('CplxVecPwr_f32')
	.sect	'.text.Ifx_Cf32.CplxVecPwr_f32'
	.align	2
	
	.global	CplxVecPwr_f32
; Function CplxVecPwr_f32
.L66:
CplxVecPwr_f32:	.type	func
	mov.aa	a15,a4
.L310:
	mov	d0,#0
.L311:
	j	.L8
.L9:
	ld.w	d1,[a4]
.L401:
	ld.w	d2,[a4]
.L402:
	ld.w	d15,[a4]4
.L403:
	ld.w	d3,[a4]4
.L404:
	mul.f	d15,d15,d3
.L405:
	madd.f	d15,d15,d1,d2
.L406:
	j	.L10
.L10:
	st.w	[a15],d15
.L407:
	add.a	a15,#4
.L408:
	lea	a4,[a4]8
.L409:
	add	d0,#1
.L312:
	extr.u	d0,d0,#0,#16
.L8:
	jlt	d0,d4,.L9
.L410:
	rsub	d4,#0
.L411:
	mul	d15,d4,#4
	addsc.a	a2,a15,d15,#0
.L412:
	j	.L11
.L11:
	ret
.L205:
	
__CplxVecPwr_f32_function_end:
	.size	CplxVecPwr_f32,__CplxVecPwr_f32_function_end-CplxVecPwr_f32
.L118:
	; End of function
	
	.sdecl	'.text.Ifx_Cf32.CplxVecMag_f32',code,cluster('CplxVecMag_f32')
	.sect	'.text.Ifx_Cf32.CplxVecMag_f32'
	.align	2
	
	.global	CplxVecMag_f32
; Function CplxVecMag_f32
.L68:
CplxVecMag_f32:	.type	func
	mov.aa	a15,a4
.L313:
	mov	d9,d4
.L314:
	mov.aa	a12,a15
.L315:
	mov	d8,#0
.L316:
	j	.L12
.L13:
	ld.w	d0,[a15]
.L417:
	ld.w	d1,[a15]
.L418:
	ld.w	d15,[a15]4
.L419:
	ld.w	d2,[a15]4
.L420:
	mul.f	d15,d15,d2
.L421:
	madd.f	d4,d15,d0,d1
.L422:
	j	.L14
.L14:
	call	sqrtf
.L423:
	j	.L15
.L15:
	st.w	[a12],d2
.L424:
	add.a	a12,#4
.L425:
	lea	a15,[a15]8
.L426:
	add	d8,#1
.L317:
	extr.u	d8,d8,#0,#16
.L12:
	jlt	d8,d9,.L13
.L427:
	rsub	d9,#0
.L428:
	mul	d15,d9,#4
	addsc.a	a2,a12,d15,#0
.L429:
	j	.L16
.L16:
	ret
.L215:
	
__CplxVecMag_f32_function_end:
	.size	CplxVecMag_f32,__CplxVecMag_f32_function_end-CplxVecMag_f32
.L123:
	; End of function
	
	.sdecl	'.text.Ifx_Cf32.CplxVecMul_f32',code,cluster('CplxVecMul_f32')
	.sect	'.text.Ifx_Cf32.CplxVecMul_f32'
	.align	2
	
	.global	CplxVecMul_f32
; Function CplxVecMul_f32
.L70:
CplxVecMul_f32:	.type	func
	j	.L17
.L18:
	ld.w	d15,[a4]
.L434:
	ld.w	d0,[a5]
.L435:
	mul.f	d0,d15,d0
.L436:
	ld.w	d15,[a4]4
.L437:
	ld.w	d1,[a5]4
.L438:
	msub.f	d0,d0,d15,d1
.L439:
	ld.w	d2,[a4]4
.L440:
	ld.w	d3,[a5]
.L441:
	ld.w	d5,[a4]
.L442:
	ld.w	d15,[a5]4
.L443:
	mul.f	d15,d5,d15
.L444:
	madd.f	d15,d15,d2,d3
.L445:
	mov	d1,d15
.L318:
	j	.L19
.L19:
	st.d	[a4]0,e0
.L231:
	lea	a4,[a4]8
.L446:
	add	d4,#-1
.L17:
	jge	d4,#1,.L18
.L447:
	ret
.L225:
	
__CplxVecMul_f32_function_end:
	.size	CplxVecMul_f32,__CplxVecMul_f32_function_end-CplxVecMul_f32
.L128:
	; End of function
	
	.sdecl	'.text.Ifx_Cf32.VecPwrdB_f32',code,cluster('VecPwrdB_f32')
	.sect	'.text.Ifx_Cf32.VecPwrdB_f32'
	.align	2
	
	.global	VecPwrdB_f32
; Function VecPwrdB_f32
.L72:
VecPwrdB_f32:	.type	func
	mov.aa	a15,a4
.L319:
	mov	d8,d4
.L320:
	mov	d15,#0
.L321:
	j	.L20
.L21:
	ld.w	d4,[a15]
	call	log10f
.L472:
	movh	d0,#16800
.L473:
	mul.f	d0,d2,d0
.L474:
	st.w	[a15],d0
.L475:
	add.a	a15,#4
.L476:
	add	d15,#1
.L322:
	extr.u	d15,d15,#0,#16
.L20:
	jlt	d15,d8,.L21
.L477:
	ret
.L249:
	
__VecPwrdB_f32_function_end:
	.size	VecPwrdB_f32,__VecPwrdB_f32_function_end-VecPwrdB_f32
.L138:
	; End of function
	
	.sdecl	'.text.Ifx_Cf32.VecPwrdB_SF',code,cluster('VecPwrdB_SF')
	.sect	'.text.Ifx_Cf32.VecPwrdB_SF'
	.align	2
	
	.global	VecPwrdB_SF
; Function VecPwrdB_SF
.L74:
VecPwrdB_SF:	.type	func
	mov.aa	a15,a4
.L323:
	mov.aa	a12,a5
.L324:
	mov	d8,d4
.L325:
	mov	d15,#0
.L326:
	j	.L22
.L23:
	ld.w	d4,[a12]
	call	log10f
.L482:
	mov	d4,d2
	call	__f_ftod
	mov	e4,d3,d2
.L483:
	mov	d6,#0
	mov	d7,#0
	addih	d7,d7,#16436
.L484:
	call	__d_mul
	mov	e4,d3,d2
.L485:
	call	__d_dtos
.L486:
	st.h	[a15],d2
.L487:
	add.a	a15,#2
.L488:
	add.a	a12,#4
.L489:
	add	d15,#1
.L327:
	extr.u	d15,d15,#0,#16
.L22:
	jlt	d15,d8,.L23
.L490:
	ret
.L253:
	
__VecPwrdB_SF_function_end:
	.size	VecPwrdB_SF,__VecPwrdB_SF_function_end-VecPwrdB_SF
.L143:
	; End of function
	
	.sdecl	'.text.Ifx_Cf32.VecMaxIdx_f32',code,cluster('VecMaxIdx_f32')
	.sect	'.text.Ifx_Cf32.VecMaxIdx_f32'
	.align	2
	
	.global	VecMaxIdx_f32
; Function VecMaxIdx_f32
.L76:
VecMaxIdx_f32:	.type	func
	movh	d2,#128
.L328:
	mov	d0,#0
.L329:
	j	.L24
.L25:
	ld.w	d15,[a4]
.L552:
	cmp.f	d15,d15,d2
	extr.u	d15,d15,#2,#1
.L553:
	jeq	d15,#0,.L26
.L554:
	ld.w	d2,[a4]
.L555:
	st.h	[a5],d0
.L26:
	ld.w	d15,[a4]
.L556:
	cmp.f	d15,d15,d2
	and	d15,#6
	ne	d15,d15,#0
.L557:
	jeq	d15,#0,.L27
.L558:
	st.h	[a6],d0
.L27:
	add.a	a4,#4
.L559:
	add	d0,#1
.L330:
	extr.u	d0,d0,#0,#16
.L24:
	jlt	d0,d4,.L25
.L560:
	j	.L28
.L28:
	ret
.L295:
	
__VecMaxIdx_f32_function_end:
	.size	VecMaxIdx_f32,__VecMaxIdx_f32_function_end-VecMaxIdx_f32
.L183:
	; End of function
	
	.sdecl	'.text.Ifx_Cf32.VecMinIdx_f32',code,cluster('VecMinIdx_f32')
	.sect	'.text.Ifx_Cf32.VecMinIdx_f32'
	.align	2
	
	.global	VecMinIdx_f32
; Function VecMinIdx_f32
.L78:
VecMinIdx_f32:	.type	func
	mov.u	d2,#65535
.L331:
	addih	d2,d2,#32639
.L538:
	mov	d0,#0
.L332:
	j	.L29
.L30:
	ld.w	d15,[a4]
.L539:
	cmp.f	d15,d15,d2
	extr.u	d15,d15,#0,#1
.L540:
	jeq	d15,#0,.L31
.L541:
	ld.w	d2,[a4]
.L542:
	st.h	[a5],d0
.L31:
	ld.w	d15,[a4]
.L543:
	cmp.f	d15,d15,d2
	and	d15,#3
	ne	d15,d15,#0
.L544:
	jeq	d15,#0,.L32
.L545:
	st.h	[a6],d0
.L32:
	add.a	a4,#4
.L546:
	add	d0,#1
.L333:
	extr	d0,d0,#0,#16
.L29:
	jlt	d0,d4,.L30
.L547:
	j	.L33
.L33:
	ret
.L288:
	
__VecMinIdx_f32_function_end:
	.size	VecMinIdx_f32,__VecMinIdx_f32_function_end-VecMinIdx_f32
.L178:
	; End of function
	
	.sdecl	'.text.Ifx_Cf32.VecOfs_f32',code,cluster('VecOfs_f32')
	.sect	'.text.Ifx_Cf32.VecOfs_f32'
	.align	2
	
	.global	VecOfs_f32
; Function VecOfs_f32
.L80:
VecOfs_f32:	.type	func
	mov	d15,#0
.L334:
	j	.L34
.L35:
	ld.w	d0,[a4]
.L504:
	sub.f	d0,d0,d4
	st.w	[a4],d0
.L505:
	add.a	a4,#4
.L506:
	add	d15,#1
.L335:
	extr.u	d15,d15,#0,#16
.L34:
	jlt	d15,d5,.L35
.L507:
	ret
.L265:
	
__VecOfs_f32_function_end:
	.size	VecOfs_f32,__VecOfs_f32_function_end-VecOfs_f32
.L153:
	; End of function
	
	.sdecl	'.text.Ifx_Cf32.VecGain_f32',code,cluster('VecGain_f32')
	.sect	'.text.Ifx_Cf32.VecGain_f32'
	.align	2
	
	.global	VecGain_f32
; Function VecGain_f32
.L82:
VecGain_f32:	.type	func
	mov	d15,#0
.L336:
	j	.L36
.L37:
	ld.w	d0,[a4]
.L495:
	mul.f	d0,d0,d4
.L496:
	st.w	[a4],d0
.L497:
	add.a	a4,#4
.L498:
	add	d15,#1
.L337:
	extr.u	d15,d15,#0,#16
.L36:
	jlt	d15,d5,.L37
.L499:
	ret
.L259:
	
__VecGain_f32_function_end:
	.size	VecGain_f32,__VecGain_f32_function_end-VecGain_f32
.L148:
	; End of function
	
	.sdecl	'.text.Ifx_Cf32.VecSum_f32',code,cluster('VecSum_f32')
	.sect	'.text.Ifx_Cf32.VecSum_f32'
	.align	2
	
	.global	VecSum_f32
; Function VecSum_f32
.L84:
VecSum_f32:	.type	func
	mov	d2,#0
.L338:
	mov	d15,#0
.L339:
	j	.L38
.L39:
	ld.w	d0,[a4]
.L512:
	add.f	d2,d2,d0
.L513:
	add.a	a4,#4
.L514:
	add	d15,#1
.L340:
	extr.u	d15,d15,#0,#16
.L38:
	jlt	d15,d4,.L39
.L515:
	j	.L40
.L40:
	ret
.L270:
	
__VecSum_f32_function_end:
	.size	VecSum_f32,__VecSum_f32_function_end-VecSum_f32
.L158:
	; End of function
	
	.sdecl	'.text.Ifx_Cf32.VecAvg_f32',code,cluster('VecAvg_f32')
	.sect	'.text.Ifx_Cf32.VecAvg_f32'
	.align	2
	
	.global	VecAvg_f32
; Function VecAvg_f32
.L86:
VecAvg_f32:	.type	func
	mov	d15,d4
.L342:
	mov	d4,d15
	call	VecSum_f32
.L341:
	itof	d15,d15
.L343:
	div.f	d2,d2,d15
.L520:
	j	.L41
.L41:
	ret
.L275:
	
__VecAvg_f32_function_end:
	.size	VecAvg_f32,__VecAvg_f32_function_end-VecAvg_f32
.L163:
	; End of function
	
	.sdecl	'.text.Ifx_Cf32.VecMax_f32',code,cluster('VecMax_f32')
	.sect	'.text.Ifx_Cf32.VecMax_f32'
	.align	2
	
	.global	VecMax_f32
; Function VecMax_f32
.L88:
VecMax_f32:	.type	func
	movh	d2,#128
.L344:
	mov	d0,#0
.L345:
	j	.L42
.L43:
	ld.w	d15,[a4]
	cmp.f	d15,d2,d15
	jnz.t	d15:2,.L44
	ld.w	d2,[a4]
	j	.L45
.L44:
.L45:
	add.a	a4,#4
.L525:
	add	d0,#1
.L346:
	extr.u	d0,d0,#0,#16
.L42:
	jlt	d0,d4,.L43
.L526:
	j	.L46
.L46:
	ret
.L278:
	
__VecMax_f32_function_end:
	.size	VecMax_f32,__VecMax_f32_function_end-VecMax_f32
.L168:
	; End of function
	
	.sdecl	'.text.Ifx_Cf32.VecMin_f32',code,cluster('VecMin_f32')
	.sect	'.text.Ifx_Cf32.VecMin_f32'
	.align	2
	
	.global	VecMin_f32
; Function VecMin_f32
.L90:
VecMin_f32:	.type	func
	mov.u	d2,#65535
.L347:
	addih	d2,d2,#32639
.L531:
	mov	d0,#0
.L348:
	j	.L47
.L48:
	ld.w	d15,[a4]
	cmp.f	d15,d2,d15
	jnz.t	d15:0,.L49
	ld.w	d2,[a4]
	j	.L50
.L49:
.L50:
	add.a	a4,#4
.L532:
	add	d0,#1
.L349:
	extr.u	d0,d0,#0,#16
.L47:
	jlt	d0,d4,.L48
.L533:
	j	.L51
.L51:
	ret
.L283:
	
__VecMin_f32_function_end:
	.size	VecMin_f32,__VecMin_f32_function_end-VecMin_f32
.L173:
	; End of function
	
	.sdecl	'.text.Ifx_Cf32.VecHalfSwap_f32',code,cluster('VecHalfSwap_f32')
	.sect	'.text.Ifx_Cf32.VecHalfSwap_f32'
	.align	2
	
	.global	VecHalfSwap_f32
; Function VecHalfSwap_f32
.L92:
VecHalfSwap_f32:	.type	func
	mov	d15,#2
.L565:
	div	e0,d4,d15
	extr.u	d0,d0,#0,#16
.L350:
	mul	d15,d0,#4
	addsc.a	a15,a4,d15,#0
.L351:
	mov	d15,#0
.L352:
	j	.L52
.L53:
	ld.w	d1,[a15]
.L354:
	ld.w	d2,[a4]
.L566:
	st.w	[a15],d2
.L567:
	st.w	[a4],d1
.L568:
	add.a	a15,#4
.L569:
	add.a	a4,#4
.L308:
	add	d15,#1
.L353:
	extr.u	d15,d15,#0,#16
.L52:
	jlt.u	d15,d0,.L53
.L570:
	ret
.L302:
	
__VecHalfSwap_f32_function_end:
	.size	VecHalfSwap_f32,__VecHalfSwap_f32_function_end-VecHalfSwap_f32
.L188:
	; End of function
	
	.sdecl	'.text.Ifx_Cf32.VecWin_f32',code,cluster('VecWin_f32')
	.sect	'.text.Ifx_Cf32.VecWin_f32'
	.align	2
	
	.global	VecWin_f32
; Function VecWin_f32
.L94:
VecWin_f32:	.type	func
	mov	d15,d7
.L356:
	div	e0,d5,d4
	extr	d2,d0,#0,#16
.L357:
	jeq	d15,#0,.L54
.L452:
	mov	d3,#0
.L359:
	j	.L55
.L56:
	ld.w	d0,[a4]
.L453:
	ld.w	d1,[a5]
.L454:
	mul.f	d0,d0,d1
.L455:
	st.w	[a4],d0
.L456:
	mul	d15,d6,#4
	addsc.a	a4,a4,d15,#0
.L457:
	mul	d15,d2,#4
	addsc.a	a5,a5,d15,#0
.L458:
	add	d15,d3,#1
	extr	d3,d15,#0,#16
.L55:
	mov	d0,#2
.L459:
	div	e0,d4,d0
.L460:
	jlt	d3,d0,.L56
.L461:
	mov	d0,d2
.L361:
	rsub	d0,#0
.L358:
	mul	d15,d0,#4
	addsc.a	a15,a5,d15,#0
.L355:
	j	.L57
.L58:
	ld.w	d0,[a4]
.L462:
	ld.w	d1,[a15]
.L463:
	mul.f	d15,d0,d1
.L464:
	st.w	[a4],d15
.L465:
	mul	d15,d6,#4
	addsc.a	a4,a4,d15,#0
.L362:
	mov	d15,d2
.L363:
	rsub	d15,#0
.L466:
	mul	d15,d15,#4
.L364:
	addsc.a	a15,a15,d15,#0
.L467:
	add	d3,#1
.L360:
	extr	d3,d3,#0,#16
.L57:
	jlt	d3,d4,.L58
.L54:
	ret
.L239:
	
__VecWin_f32_function_end:
	.size	VecWin_f32,__VecWin_f32_function_end-VecWin_f32
.L133:
	; End of function
	
	.calls	'VecPwrdB_SF','__f_ftod'
	.calls	'VecPwrdB_SF','__d_mul'
	.calls	'VecPwrdB_SF','__d_dtos'
	.calls	'CplxVecMag_f32','sqrtf'
	.calls	'VecPwrdB_f32','log10f'
	.calls	'VecPwrdB_SF','log10f'
	.calls	'VecAvg_f32','VecSum_f32'
	.calls	'CplxVecRst_f32','',0
	.calls	'CplxVecCpy_f32S','',0
	.calls	'CplxVecCpy_f32','',0
	.calls	'CplxVecPwr_f32','',0
	.calls	'CplxVecMag_f32','',0
	.calls	'CplxVecMul_f32','',0
	.calls	'VecPwrdB_f32','',0
	.calls	'VecPwrdB_SF','',0
	.calls	'VecMaxIdx_f32','',0
	.calls	'VecMinIdx_f32','',0
	.calls	'VecOfs_f32','',0
	.calls	'VecGain_f32','',0
	.calls	'VecSum_f32','',0
	.calls	'VecAvg_f32','',0
	.calls	'VecMax_f32','',0
	.calls	'VecMin_f32','',0
	.calls	'VecHalfSwap_f32','',0
	.extern	log10f
	.extern	sqrtf
	.extern	__f_ftod
	.extern	__d_mul
	.extern	__d_dtos
	.calls	'VecWin_f32','',0
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L96:
	.word	1305
	.half	3
	.word	.L97
	.byte	4
.L95:
	.byte	1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_Cf32.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L98
	.byte	2,1,1,3
	.word	235
	.byte	4
	.byte	'Ifx__jump_and_link',0,3,1,61,17,1,1,5
	.byte	'fun',0,1,61,43
	.word	238
	.byte	6,0,7
	.byte	'__fract',0,4,128,1
.L261:
	.byte	7
	.byte	'float',0,4,4,8
	.byte	'Ifx__float_to_fract',0,3,1,152,2,18
	.word	283
	.byte	1,1,5
	.byte	'a',0,1,152,2,44
	.word	295
	.byte	6,0,7
	.byte	'unsigned long long int',0,8,7,9
	.byte	'void',0,3
	.word	375
	.byte	8
	.byte	'__ld64',0,3,2,135,1,19
	.word	349
	.byte	1,1,5
	.byte	'addr',0,2,135,1,32
	.word	381
	.byte	6,0,4
	.byte	'__st64',0,3,2,143,1,17,1,1,5
	.byte	'addr',0,2,143,1,30
	.word	381
	.byte	5
	.byte	'value',0,2,143,1,43
	.word	349
	.byte	6,0
.L237:
	.byte	10,4,61,9,8,11
	.byte	'real',0
	.word	295
	.byte	4,2,35,0,11
	.byte	'imag',0
	.word	295
	.byte	4,2,35,4,0,12
	.word	467
.L227:
	.byte	3
	.word	501
.L230:
	.byte	8
	.byte	'IFX_Cf32_mul',0,3,3,67,21
	.word	467
	.byte	1,1
.L232:
	.byte	5
	.byte	'a',0,3,67,50
	.word	506
.L234:
	.byte	5
	.byte	'b',0,3,67,69
	.word	506
.L236:
	.byte	6,0
.L211:
	.byte	8
	.byte	'IFX_Cf32_dot',0,3,3,85,20
	.word	295
	.byte	1,1
.L212:
	.byte	5
	.byte	'b',0,3,85,49
	.word	506
.L214:
	.byte	6,0
.L220:
	.byte	8
	.byte	'IFX_Cf32_mag',0,3,3,91,20
	.word	295
	.byte	1,1
.L221:
	.byte	5
	.byte	'c',0,3,91,49
	.word	506
.L223:
	.byte	13,6,0,0
.L190:
	.byte	3
	.word	467
	.byte	4
	.byte	'IFX_Cf32_set',0,3,3,125,17,1,1,5
	.byte	'a',0,3,125,40
	.word	631
	.byte	5
	.byte	're',0,3,125,51
	.word	295
	.byte	5
	.byte	'im',0,3,125,63
	.word	295
	.byte	6,0,14
	.word	243
	.byte	15
	.word	269
	.byte	6,0,14
	.word	304
	.byte	15
	.word	336
	.byte	6,0,14
	.word	386
	.byte	15
	.word	405
	.byte	6,0,14
	.word	421
	.byte	15
	.word	436
	.byte	15
	.word	450
	.byte	6,0,16
	.byte	'log10f',0,5,149,1,25
	.word	295
	.byte	1,1,1,1,17,5,149,1,43
	.word	295
	.byte	0,16
	.byte	'sqrtf',0,5,187,1,25
	.word	295
	.byte	1,1,1,1,17,5,187,1,43
	.word	295
	.byte	0,14
	.word	511
	.byte	15
	.word	535
	.byte	15
	.word	545
	.byte	6,0,14
	.word	557
	.byte	15
	.word	581
	.byte	6,0,14
	.word	593
	.byte	15
	.word	617
	.byte	13,18
	.word	557
	.byte	15
	.word	581
	.byte	19
	.word	591
	.byte	0,6,0,0,14
	.word	636
	.byte	15
	.word	656
	.byte	15
	.word	666
	.byte	15
	.word	677
	.byte	6,0
.L194:
	.byte	7
	.byte	'short int',0,2,5
.L192:
	.byte	3
	.word	883
.L204:
	.byte	3
	.word	295
.L208:
	.byte	7
	.byte	'unsigned short int',0,2,7,12
	.word	295
.L241:
	.byte	3
	.word	928
.L254:
	.byte	3
	.word	883
	.byte	20
	.byte	'__wchar_t',0,6,1,1
	.word	883
	.byte	7
	.byte	'unsigned int',0,4,7,20
	.byte	'__size_t',0,6,1,1
	.word	961
	.byte	7
	.byte	'int',0,4,5,20
	.byte	'__ptrdiff_t',0,6,1,1
	.word	994
	.byte	21,1,3
	.word	1021
	.byte	20
	.byte	'__codeptr',0,6,1,1
	.word	1023
	.byte	7
	.byte	'unsigned char',0,1,8,20
	.byte	'uint8',0,7,105,29
	.word	1046
	.byte	20
	.byte	'uint16',0,7,109,29
	.word	906
	.byte	7
	.byte	'unsigned long int',0,4,7,20
	.byte	'uint32',0,7,113,29
	.word	1092
	.byte	20
	.byte	'uint64',0,7,118,29
	.word	349
	.byte	20
	.byte	'sint16',0,7,126,29
	.word	883
	.byte	7
	.byte	'long int',0,4,5,20
	.byte	'sint32',0,7,131,1,29
	.word	1158
	.byte	7
	.byte	'long long int',0,8,5,20
	.byte	'sint64',0,7,138,1,29
	.word	1186
	.byte	20
	.byte	'float32',0,7,167,1,29
	.word	295
	.byte	20
	.byte	'pvoid',0,4,57,28
	.word	381
	.byte	20
	.byte	'cfloat32',0,4,65,3
	.word	467
	.byte	20
	.byte	'Ifx_TickTime',0,4,79,28
	.word	1186
	.byte	20
	.byte	'_iob_flag_t',0,8,82,25
	.word	906
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L97:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,21,0,54,15,39,12,0,0,3,15,0,73,19,0,0,4,46,1,3,8,32,13
	.byte	58,15,59,15,57,15,54,15,39,12,0,0,5,5,0,3,8,58,15,59,15,57,15,73,19,0,0,6,11,0,0,0,7,36,0,3,8,11,15,62
	.byte	15,0,0,8,46,1,3,8,32,13,58,15,59,15,57,15,73,19,54,15,39,12,0,0,9,59,0,3,8,0,0,10,19,1,58,15,59,15,57
	.byte	15,11,15,0,0,11,13,0,3,8,73,19,11,15,56,9,0,0,12,38,0,73,19,0,0,13,11,1,0,0,14,46,1,49,19,0,0,15,5,0,49
	.byte	19,0,0,16,46,1,3,8,58,15,59,15,57,15,73,19,54,15,39,12,63,12,60,12,0,0,17,5,0,58,15,59,15,57,15,73,19
	.byte	0,0,18,29,1,49,19,0,0,19,11,0,49,19,0,0,20,22,0,3,8,58,15,59,15,57,15,73,19,0,0,21,21,0,54,15,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L98:
	.word	.L366-.L365
.L365:
	.half	3
	.word	.L368-.L367
.L367:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'F:\\ADS\\AURIX-Studio-1.10.2\\tools\\Compilers\\Tasking_1.1r8\\ctc\\include\\',0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_Intrinsics.h',0
	.byte	0,0,0
	.byte	'..\\libraries\\infineon_libraries\\Service\\CpuGeneric\\SysSe\\Math\\Ifx_Cf32.h',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Ifx_Types.h',0
	.byte	0,0,0
	.byte	'math.h',0,1,0,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_Cf32.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Platform_Types.h',0
	.byte	0,0,0
	.byte	'stdio.h',0,1,0,0,0
.L368:
.L366:
	.sdecl	'.debug_info',debug,cluster('CplxVecCpy_f32S')
	.sect	'.debug_info'
.L99:
	.word	347
	.half	3
	.word	.L100
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_Cf32.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L102,.L101
	.byte	2
	.word	.L95
	.byte	3
	.byte	'CplxVecCpy_f32S',0,1,59,6,1,1,1
	.word	.L62,.L189,.L61
	.byte	4
	.byte	'X',0,1,59,32
	.word	.L190,.L191
	.byte	4
	.byte	'S',0,1,59,42
	.word	.L192,.L193
	.byte	4
	.byte	'nS',0,1,59,51
	.word	.L194,.L195
	.byte	4
	.byte	'incrS',0,1,59,61
	.word	.L194,.L196
	.byte	5
	.word	.L62,.L189
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('CplxVecCpy_f32S')
	.sect	'.debug_abbrev'
.L100:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('CplxVecCpy_f32S')
	.sect	'.debug_line'
.L101:
	.word	.L370-.L369
.L369:
	.half	3
	.word	.L372-.L371
.L371:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_Cf32.c',0,0,0,0,0
.L372:
	.byte	5,19,7,0,5,2
	.word	.L62
	.byte	3,60,1,5,21,9
	.half	.L5-.L62
	.byte	3,2,1,5,19,9
	.half	.L373-.L5
	.byte	1,5,21,9
	.half	.L374-.L373
	.byte	3,1,1,5,19,9
	.half	.L375-.L374
	.byte	1,5,10,9
	.half	.L376-.L375
	.byte	3,1,1,5,18,9
	.half	.L377-.L376
	.byte	1,5,23,9
	.half	.L378-.L377
	.byte	3,124,1,5,19,9
	.half	.L4-.L378
	.byte	1,5,1,7,9
	.half	.L379-.L4
	.byte	3,6,1,7,9
	.half	.L103-.L379
	.byte	0,1,1
.L370:
	.sdecl	'.debug_ranges',debug,cluster('CplxVecCpy_f32S')
	.sect	'.debug_ranges'
.L102:
	.word	-1,.L62,0,.L103-.L62,0,0
	.sdecl	'.debug_info',debug,cluster('CplxVecRst_f32')
	.sect	'.debug_info'
.L104:
	.word	314
	.half	3
	.word	.L105
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_Cf32.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L107,.L106
	.byte	2
	.word	.L95
	.byte	3
	.byte	'CplxVecRst_f32',0,1,49,6,1,1,1
	.word	.L60,.L197,.L59
	.byte	4
	.byte	'X',0,1,49,31
	.word	.L190,.L198
	.byte	4
	.byte	'nX',0,1,49,40
	.word	.L194,.L199
	.byte	5
	.word	.L60,.L197
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('CplxVecRst_f32')
	.sect	'.debug_abbrev'
.L105:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('CplxVecRst_f32')
	.sect	'.debug_line'
.L106:
	.word	.L381-.L380
.L380:
	.half	3
	.word	.L383-.L382
.L382:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_Cf32.c',0,0,0,0,0
.L383:
	.byte	5,19,7,0,5,2
	.word	.L60
	.byte	3,50,1,5,27,9
	.half	.L3-.L60
	.byte	3,2,1,5,17,9
	.half	.L384-.L3
	.byte	1,5,10,9
	.half	.L385-.L384
	.byte	3,1,1,5,23,9
	.half	.L386-.L385
	.byte	3,125,1,5,19,9
	.half	.L2-.L386
	.byte	1,5,1,7,9
	.half	.L387-.L2
	.byte	3,5,1,7,9
	.half	.L108-.L387
	.byte	0,1,1
.L381:
	.sdecl	'.debug_ranges',debug,cluster('CplxVecRst_f32')
	.sect	'.debug_ranges'
.L107:
	.word	-1,.L60,0,.L108-.L60,0,0
	.sdecl	'.debug_info',debug,cluster('CplxVecCpy_f32')
	.sect	'.debug_info'
.L109:
	.word	328
	.half	3
	.word	.L110
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_Cf32.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L112,.L111
	.byte	2
	.word	.L95
	.byte	3
	.byte	'CplxVecCpy_f32',0,1,70,6,1,1,1
	.word	.L64,.L200,.L63
	.byte	4
	.byte	'X',0,1,70,31
	.word	.L190,.L201
	.byte	4
	.byte	'S',0,1,70,44
	.word	.L190,.L202
	.byte	4
	.byte	'nS',0,1,70,53
	.word	.L194,.L203
	.byte	5
	.word	.L64,.L200
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('CplxVecCpy_f32')
	.sect	'.debug_abbrev'
.L110:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('CplxVecCpy_f32')
	.sect	'.debug_line'
.L111:
	.word	.L389-.L388
.L388:
	.half	3
	.word	.L391-.L390
.L390:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_Cf32.c',0,0,0,0,0
.L391:
	.byte	5,19,7,0,5,2
	.word	.L64
	.byte	3,199,0,1,5,14,9
	.half	.L7-.L64
	.byte	3,2,1,5,12,9
	.half	.L392-.L7
	.byte	1,5,10,9
	.half	.L393-.L392
	.byte	3,1,1,5,15,9
	.half	.L394-.L393
	.byte	1,5,23,9
	.half	.L395-.L394
	.byte	3,125,1,5,19,9
	.half	.L6-.L395
	.byte	1,5,1,7,9
	.half	.L396-.L6
	.byte	3,5,1,7,9
	.half	.L113-.L396
	.byte	0,1,1
.L389:
	.sdecl	'.debug_ranges',debug,cluster('CplxVecCpy_f32')
	.sect	'.debug_ranges'
.L112:
	.word	-1,.L64,0,.L113-.L64,0,0
	.sdecl	'.debug_info',debug,cluster('CplxVecPwr_f32')
	.sect	'.debug_info'
.L114:
	.word	383
	.half	3
	.word	.L115
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_Cf32.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L117,.L116
	.byte	2
	.word	.L95
	.byte	3
	.byte	'CplxVecPwr_f32',0,1,80,10
	.word	.L204
	.byte	1,1,1
	.word	.L66,.L205,.L65
	.byte	4
	.byte	'X',0,1,80,35
	.word	.L190,.L206
	.byte	4
	.byte	'nX',0,1,80,44
	.word	.L194,.L207
	.byte	5
	.word	.L66,.L205
	.byte	6
	.byte	'i',0,1,82,20
	.word	.L208,.L209
	.byte	6
	.byte	'r',0,1,83,20
	.word	.L204,.L210
	.byte	7
	.word	.L211,.L9,.L10
	.byte	8
	.word	.L212,.L213
	.byte	9
	.word	.L214,.L9,.L10
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('CplxVecPwr_f32')
	.sect	'.debug_abbrev'
.L115:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,5,0,49,16,2,6,0,0,9,11,0,49,16,17
	.byte	1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('CplxVecPwr_f32')
	.sect	'.debug_line'
.L116:
	.word	.L398-.L397
.L397:
	.half	3
	.word	.L400-.L399
.L399:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_Cf32.c',0,0,0,0
	.byte	'..\\libraries\\infineon_libraries\\Service\\CpuGeneric\\SysSe\\Math\\Ifx_Cf32.h',0,0,0,0,0
.L400:
	.byte	5,22,7,0,5,2
	.word	.L66
	.byte	3,210,0,1,5,12,9
	.half	.L310-.L66
	.byte	3,2,1,5,23,9
	.half	.L311-.L310
	.byte	1,4,2,5,14,9
	.half	.L9-.L311
	.byte	3,2,1,5,24,9
	.half	.L401-.L9
	.byte	1,5,36,9
	.half	.L402-.L401
	.byte	1,5,46,9
	.half	.L403-.L402
	.byte	1,5,43,9
	.half	.L404-.L403
	.byte	1,5,32,9
	.half	.L405-.L404
	.byte	1,5,5,9
	.half	.L406-.L405
	.byte	1,4,1,5,12,9
	.half	.L10-.L406
	.byte	1,5,10,9
	.half	.L407-.L10
	.byte	3,1,1,5,15,9
	.half	.L408-.L407
	.byte	1,5,26,9
	.half	.L409-.L408
	.byte	3,125,1,5,23,9
	.half	.L8-.L409
	.byte	1,5,15,7,9
	.half	.L410-.L8
	.byte	3,6,1,5,14,9
	.half	.L411-.L410
	.byte	1,5,5,9
	.half	.L412-.L411
	.byte	1,5,1,9
	.half	.L11-.L412
	.byte	3,1,1,7,9
	.half	.L118-.L11
	.byte	0,1,1
.L398:
	.sdecl	'.debug_ranges',debug,cluster('CplxVecPwr_f32')
	.sect	'.debug_ranges'
.L117:
	.word	-1,.L66,0,.L118-.L66,0,0
	.sdecl	'.debug_info',debug,cluster('CplxVecMag_f32')
	.sect	'.debug_info'
.L119:
	.word	420
	.half	3
	.word	.L120
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_Cf32.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L122,.L121
	.byte	2
	.word	.L95
	.byte	3
	.byte	'CplxVecMag_f32',0,1,95,10
	.word	.L204
	.byte	1,1,1
	.word	.L68,.L215,.L67
	.byte	4
	.byte	'X',0,1,95,35
	.word	.L190,.L216
	.byte	4
	.byte	'nX',0,1,95,44
	.word	.L194,.L217
	.byte	5
	.word	.L68,.L215
	.byte	6
	.byte	'i',0,1,97,20
	.word	.L208,.L218
	.byte	6
	.byte	'r',0,1,98,20
	.word	.L204,.L219
	.byte	7
	.word	.L220,.L13,.L15
	.byte	8
	.word	.L221,.L222
	.byte	9
	.word	.L223,.L13,.L15
	.byte	7
	.word	.L211,.L13,.L14
	.byte	8
	.word	.L212,.L224
	.byte	10
	.word	.L214,.L13,.L14
	.byte	0,0,0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('CplxVecMag_f32')
	.sect	'.debug_abbrev'
.L120:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,5,0,49,16,2,6,0,0,9,11,1,49,16,17
	.byte	1,18,1,0,0,10,11,0,49,16,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('CplxVecMag_f32')
	.sect	'.debug_line'
.L121:
	.word	.L414-.L413
.L413:
	.half	3
	.word	.L416-.L415
.L415:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_Cf32.c',0,0,0,0
	.byte	'..\\libraries\\infineon_libraries\\Service\\CpuGeneric\\SysSe\\Math\\Ifx_Cf32.h',0,0,0,0,0
.L416:
	.byte	5,10,7,0,5,2
	.word	.L68
	.byte	3,222,0,1,5,22,9
	.half	.L314-.L68
	.byte	3,3,1,5,12,9
	.half	.L315-.L314
	.byte	3,2,1,5,23,9
	.half	.L316-.L315
	.byte	1,4,2,5,14,9
	.half	.L13-.L316
	.byte	3,115,1,5,24,9
	.half	.L417-.L13
	.byte	1,5,36,9
	.half	.L418-.L417
	.byte	1,5,46,9
	.half	.L419-.L418
	.byte	1,5,43,9
	.half	.L420-.L419
	.byte	1,5,32,9
	.half	.L421-.L420
	.byte	1,5,5,9
	.half	.L422-.L421
	.byte	1,5,39,9
	.half	.L14-.L422
	.byte	3,6,1,5,5,9
	.half	.L423-.L14
	.byte	1,4,1,5,12,9
	.half	.L15-.L423
	.byte	3,9,1,5,10,9
	.half	.L424-.L15
	.byte	3,1,1,5,15,9
	.half	.L425-.L424
	.byte	1,5,26,9
	.half	.L426-.L425
	.byte	3,125,1,5,23,9
	.half	.L12-.L426
	.byte	1,5,15,7,9
	.half	.L427-.L12
	.byte	3,6,1,5,14,9
	.half	.L428-.L427
	.byte	1,5,5,9
	.half	.L429-.L428
	.byte	1,5,1,9
	.half	.L16-.L429
	.byte	3,1,1,7,9
	.half	.L123-.L16
	.byte	0,1,1
.L414:
	.sdecl	'.debug_ranges',debug,cluster('CplxVecMag_f32')
	.sect	'.debug_ranges'
.L122:
	.word	-1,.L68,0,.L123-.L68,0,0
	.sdecl	'.debug_info',debug,cluster('CplxVecMul_f32')
	.sect	'.debug_info'
.L124:
	.word	391
	.half	3
	.word	.L125
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_Cf32.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L127,.L126
	.byte	2
	.word	.L95
	.byte	3
	.byte	'CplxVecMul_f32',0,1,110,6,1,1,1
	.word	.L70,.L225,.L69
	.byte	4
	.byte	'X',0,1,110,31
	.word	.L190,.L226
	.byte	4
	.byte	'mul',0,1,110,50
	.word	.L227,.L228
	.byte	4
	.byte	'nX',0,1,110,61
	.word	.L194,.L229
	.byte	5
	.word	.L70,.L225
	.byte	6
	.word	.L230,.L18,.L231
	.byte	7
	.word	.L232,.L233
	.byte	7
	.word	.L234,.L235
	.byte	8
	.word	.L236,.L18,.L231
	.byte	9
	.byte	'R',0,2,69,14
	.word	.L237,.L238
	.byte	0,0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('CplxVecMul_f32')
	.sect	'.debug_abbrev'
.L125:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,29,1,49
	.byte	16,17,1,18,1,0,0,7,5,0,49,16,2,6,0,0,8,11,1,49,16,17,1,18,1,0,0,9,52,0,3,8,58,15,59,15,57,15,73,16,2,6
	.byte	0,0,0
	.sdecl	'.debug_line',debug,cluster('CplxVecMul_f32')
	.sect	'.debug_line'
.L126:
	.word	.L431-.L430
.L430:
	.half	3
	.word	.L433-.L432
.L432:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_Cf32.c',0,0,0,0
	.byte	'..\\libraries\\infineon_libraries\\Service\\CpuGeneric\\SysSe\\Math\\Ifx_Cf32.h',0,0,0,0,0
.L433:
	.byte	5,19,7,0,5,2
	.word	.L70
	.byte	3,239,0,1,4,2,5,16,9
	.half	.L18-.L70
	.byte	3,86,1,5,26,9
	.half	.L434-.L18
	.byte	1,5,23,9
	.half	.L435-.L434
	.byte	1,5,38,9
	.half	.L436-.L435
	.byte	1,5,48,9
	.half	.L437-.L436
	.byte	1,5,34,9
	.half	.L438-.L437
	.byte	1,5,16,9
	.half	.L439-.L438
	.byte	3,1,1,5,26,9
	.half	.L440-.L439
	.byte	1,5,38,9
	.half	.L441-.L440
	.byte	1,5,48,9
	.half	.L442-.L441
	.byte	1,5,45,9
	.half	.L443-.L442
	.byte	1,5,34,9
	.half	.L444-.L443
	.byte	1,5,12,9
	.half	.L445-.L444
	.byte	1,5,5,9
	.half	.L318-.L445
	.byte	3,1,1,4,1,5,12,9
	.half	.L19-.L318
	.byte	3,42,1,5,10,9
	.half	.L231-.L19
	.byte	3,1,1,5,23,9
	.half	.L446-.L231
	.byte	3,125,1,5,19,9
	.half	.L17-.L446
	.byte	1,5,1,7,9
	.half	.L447-.L17
	.byte	3,5,1,7,9
	.half	.L128-.L447
	.byte	0,1,1
.L431:
	.sdecl	'.debug_ranges',debug,cluster('CplxVecMul_f32')
	.sect	'.debug_ranges'
.L127:
	.word	-1,.L70,0,.L128-.L70,0,0
	.sdecl	'.debug_info',debug,cluster('VecWin_f32')
	.sect	'.debug_info'
.L129:
	.word	415
	.half	3
	.word	.L130
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_Cf32.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L132,.L131
	.byte	2
	.word	.L95
	.byte	3
	.byte	'VecWin_f32',0,1,160,2,6,1,1,1
	.word	.L94,.L239,.L93
	.byte	4
	.byte	'X',0,1,160,2,26
	.word	.L204,.L240
	.byte	4
	.byte	'W',0,1,160,2,44
	.word	.L241,.L242
	.byte	4
	.byte	'nX',0,1,160,2,53
	.word	.L194,.L243
	.byte	4
	.byte	'nW',0,1,160,2,63
	.word	.L194,.L244
	.byte	4
	.byte	'incrX',0,1,160,2,73
	.word	.L194,.L245
	.byte	4
	.byte	'symW',0,1,160,2,86
	.word	.L194,.L246
	.byte	5
	.word	.L94,.L239
	.byte	6
	.byte	'step',0,1,162,2,11
	.word	.L194,.L247
	.byte	6
	.byte	'i',0,1,163,2,11
	.word	.L194,.L248
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('VecWin_f32')
	.sect	'.debug_abbrev'
.L130:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('VecWin_f32')
	.sect	'.debug_line'
.L131:
	.word	.L449-.L448
.L448:
	.half	3
	.word	.L451-.L450
.L450:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_Cf32.c',0,0,0,0,0
.L451:
	.byte	5,6,7,0,5,2
	.word	.L94
	.byte	3,159,2,1,5,21,9
	.half	.L356-.L94
	.byte	3,2,1,5,5,9
	.half	.L357-.L356
	.byte	3,3,1,5,16,7,9
	.half	.L452-.L357
	.byte	3,2,1,5,31,9
	.half	.L359-.L452
	.byte	1,5,18,9
	.half	.L56-.L359
	.byte	3,2,1,5,23,9
	.half	.L453-.L56
	.byte	1,5,21,9
	.half	.L454-.L453
	.byte	1,5,16,9
	.half	.L455-.L454
	.byte	1,5,20,9
	.half	.L456-.L455
	.byte	3,1,1,9
	.half	.L457-.L456
	.byte	3,1,1,5,34,9
	.half	.L458-.L457
	.byte	3,124,1,5,30,9
	.half	.L55-.L458
	.byte	1,5,28,9
	.half	.L459-.L55
	.byte	1,5,31,9
	.half	.L460-.L459
	.byte	1,5,17,7,9
	.half	.L461-.L460
	.byte	3,7,1,5,16,9
	.half	.L361-.L461
	.byte	1,5,15,9
	.half	.L358-.L361
	.byte	1,5,23,9
	.half	.L355-.L358
	.byte	3,2,1,5,18,9
	.half	.L58-.L355
	.byte	3,2,1,5,23,9
	.half	.L462-.L58
	.byte	1,5,21,9
	.half	.L463-.L462
	.byte	1,5,16,9
	.half	.L464-.L463
	.byte	1,5,20,9
	.half	.L465-.L464
	.byte	3,1,1,5,22,9
	.half	.L362-.L465
	.byte	3,1,1,5,21,9
	.half	.L363-.L362
	.byte	1,5,20,9
	.half	.L466-.L363
	.byte	1,5,26,9
	.half	.L467-.L466
	.byte	3,124,1,5,23,9
	.half	.L57-.L467
	.byte	1,5,1,7,9
	.half	.L54-.L57
	.byte	3,7,1,7,9
	.half	.L133-.L54
	.byte	0,1,1
.L449:
	.sdecl	'.debug_ranges',debug,cluster('VecWin_f32')
	.sect	'.debug_ranges'
.L132:
	.word	-1,.L94,0,.L133-.L94,0,0
	.sdecl	'.debug_info',debug,cluster('VecPwrdB_f32')
	.sect	'.debug_info'
.L134:
	.word	327
	.half	3
	.word	.L135
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_Cf32.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L137,.L136
	.byte	2
	.word	.L95
	.byte	3
	.byte	'VecPwrdB_f32',0,1,120,6,1,1,1
	.word	.L72,.L249,.L71
	.byte	4
	.byte	'X',0,1,120,28
	.word	.L204,.L250
	.byte	4
	.byte	'nX',0,1,120,37
	.word	.L194,.L251
	.byte	5
	.word	.L72,.L249
	.byte	6
	.byte	'i',0,1,122,20
	.word	.L208,.L252
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('VecPwrdB_f32')
	.sect	'.debug_abbrev'
.L135:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('VecPwrdB_f32')
	.sect	'.debug_line'
.L136:
	.word	.L469-.L468
.L468:
	.half	3
	.word	.L471-.L470
.L470:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_Cf32.c',0,0,0,0,0
.L471:
	.byte	5,6,7,0,5,2
	.word	.L72
	.byte	3,247,0,1,5,12,9
	.half	.L320-.L72
	.byte	3,4,1,5,23,9
	.half	.L321-.L320
	.byte	1,5,37,9
	.half	.L21-.L321
	.byte	3,2,1,5,14,9
	.half	.L472-.L21
	.byte	1,5,36,9
	.half	.L473-.L472
	.byte	1,5,12,9
	.half	.L474-.L473
	.byte	1,5,10,9
	.half	.L475-.L474
	.byte	3,1,1,5,26,9
	.half	.L476-.L475
	.byte	3,125,1,5,23,9
	.half	.L20-.L476
	.byte	1,5,1,7,9
	.half	.L477-.L20
	.byte	3,5,1,7,9
	.half	.L138-.L477
	.byte	0,1,1
.L469:
	.sdecl	'.debug_ranges',debug,cluster('VecPwrdB_f32')
	.sect	'.debug_ranges'
.L137:
	.word	-1,.L72,0,.L138-.L72,0,0
	.sdecl	'.debug_info',debug,cluster('VecPwrdB_SF')
	.sect	'.debug_info'
.L139:
	.word	345
	.half	3
	.word	.L140
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_Cf32.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L142,.L141
	.byte	2
	.word	.L95
	.byte	3
	.byte	'VecPwrdB_SF',0,1,132,1,6,1,1,1
	.word	.L74,.L253,.L73
	.byte	4
	.byte	'R',0,1,132,1,26
	.word	.L254,.L255
	.byte	4
	.byte	'X',0,1,132,1,38
	.word	.L204,.L256
	.byte	4
	.byte	'nX',0,1,132,1,47
	.word	.L194,.L257
	.byte	5
	.word	.L74,.L253
	.byte	6
	.byte	'i',0,1,134,1,20
	.word	.L208,.L258
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('VecPwrdB_SF')
	.sect	'.debug_abbrev'
.L140:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('VecPwrdB_SF')
	.sect	'.debug_line'
.L141:
	.word	.L479-.L478
.L478:
	.half	3
	.word	.L481-.L480
.L480:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_Cf32.c',0,0,0,0,0
.L481:
	.byte	5,6,7,0,5,2
	.word	.L74
	.byte	3,131,1,1,5,12,9
	.half	.L325-.L74
	.byte	3,4,1,5,23,9
	.half	.L326-.L325
	.byte	1,5,37,9
	.half	.L23-.L326
	.byte	3,2,1,5,36,9
	.half	.L482-.L23
	.byte	1,5,23,9
	.half	.L483-.L482
	.byte	1,5,28,9
	.half	.L484-.L483
	.byte	1,5,14,9
	.half	.L485-.L484
	.byte	1,5,12,9
	.half	.L486-.L485
	.byte	1,5,10,9
	.half	.L487-.L486
	.byte	3,1,1,5,15,9
	.half	.L488-.L487
	.byte	1,5,26,9
	.half	.L489-.L488
	.byte	3,125,1,5,23,9
	.half	.L22-.L489
	.byte	1,5,1,7,9
	.half	.L490-.L22
	.byte	3,5,1,7,9
	.half	.L143-.L490
	.byte	0,1,1
.L479:
	.sdecl	'.debug_ranges',debug,cluster('VecPwrdB_SF')
	.sect	'.debug_ranges'
.L142:
	.word	-1,.L74,0,.L143-.L74,0,0
	.sdecl	'.debug_info',debug,cluster('VecGain_f32')
	.sect	'.debug_info'
.L144:
	.word	348
	.half	3
	.word	.L145
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_Cf32.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L147,.L146
	.byte	2
	.word	.L95
	.byte	3
	.byte	'VecGain_f32',0,1,206,1,6,1,1,1
	.word	.L82,.L259,.L81
	.byte	4
	.byte	'X',0,1,206,1,27
	.word	.L204,.L260
	.byte	4
	.byte	'gain',0,1,206,1,38
	.word	.L261,.L262
	.byte	4
	.byte	'nX',0,1,206,1,50
	.word	.L194,.L263
	.byte	5
	.word	.L82,.L259
	.byte	6
	.byte	'i',0,1,208,1,20
	.word	.L208,.L264
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('VecGain_f32')
	.sect	'.debug_abbrev'
.L145:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('VecGain_f32')
	.sect	'.debug_line'
.L146:
	.word	.L492-.L491
.L491:
	.half	3
	.word	.L494-.L493
.L493:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_Cf32.c',0,0,0,0,0
.L494:
	.byte	5,12,7,0,5,2
	.word	.L82
	.byte	3,209,1,1,5,23,9
	.half	.L336-.L82
	.byte	1,5,14,9
	.half	.L37-.L336
	.byte	3,2,1,5,17,9
	.half	.L495-.L37
	.byte	1,5,12,9
	.half	.L496-.L495
	.byte	1,5,10,9
	.half	.L497-.L496
	.byte	3,1,1,5,26,9
	.half	.L498-.L497
	.byte	3,125,1,5,23,9
	.half	.L36-.L498
	.byte	1,5,1,7,9
	.half	.L499-.L36
	.byte	3,5,1,7,9
	.half	.L148-.L499
	.byte	0,1,1
.L492:
	.sdecl	'.debug_ranges',debug,cluster('VecGain_f32')
	.sect	'.debug_ranges'
.L147:
	.word	-1,.L82,0,.L148-.L82,0,0
	.sdecl	'.debug_info',debug,cluster('VecOfs_f32')
	.sect	'.debug_info'
.L149:
	.word	349
	.half	3
	.word	.L150
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_Cf32.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L152,.L151
	.byte	2
	.word	.L95
	.byte	3
	.byte	'VecOfs_f32',0,1,194,1,6,1,1,1
	.word	.L80,.L265,.L79
	.byte	4
	.byte	'X',0,1,194,1,26
	.word	.L204,.L266
	.byte	4
	.byte	'offset',0,1,194,1,37
	.word	.L261,.L267
	.byte	4
	.byte	'nX',0,1,194,1,51
	.word	.L194,.L268
	.byte	5
	.word	.L80,.L265
	.byte	6
	.byte	'i',0,1,196,1,20
	.word	.L208,.L269
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('VecOfs_f32')
	.sect	'.debug_abbrev'
.L150:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('VecOfs_f32')
	.sect	'.debug_line'
.L151:
	.word	.L501-.L500
.L500:
	.half	3
	.word	.L503-.L502
.L502:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_Cf32.c',0,0,0,0,0
.L503:
	.byte	5,12,7,0,5,2
	.word	.L80
	.byte	3,197,1,1,5,23,9
	.half	.L334-.L80
	.byte	1,5,10,9
	.half	.L35-.L334
	.byte	3,2,1,5,12,9
	.half	.L504-.L35
	.byte	1,5,10,9
	.half	.L505-.L504
	.byte	3,1,1,5,26,9
	.half	.L506-.L505
	.byte	3,125,1,5,23,9
	.half	.L34-.L506
	.byte	1,5,1,7,9
	.half	.L507-.L34
	.byte	3,5,1,7,9
	.half	.L153-.L507
	.byte	0,1,1
.L501:
	.sdecl	'.debug_ranges',debug,cluster('VecOfs_f32')
	.sect	'.debug_ranges'
.L152:
	.word	-1,.L80,0,.L153-.L80,0,0
	.sdecl	'.debug_info',debug,cluster('VecSum_f32')
	.sect	'.debug_info'
.L154:
	.word	351
	.half	3
	.word	.L155
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_Cf32.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L157,.L156
	.byte	2
	.word	.L95
	.byte	3
	.byte	'VecSum_f32',0,1,218,1,9
	.word	.L261
	.byte	1,1,1
	.word	.L84,.L270,.L83
	.byte	4
	.byte	'X',0,1,218,1,29
	.word	.L204,.L271
	.byte	4
	.byte	'nX',0,1,218,1,38
	.word	.L194,.L272
	.byte	5
	.word	.L84,.L270
	.byte	6
	.byte	'sumX',0,1,220,1,20
	.word	.L261,.L273
	.byte	6
	.byte	'i',0,1,221,1,20
	.word	.L208,.L274
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('VecSum_f32')
	.sect	'.debug_abbrev'
.L155:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('VecSum_f32')
	.sect	'.debug_line'
.L156:
	.word	.L509-.L508
.L508:
	.half	3
	.word	.L511-.L510
.L510:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_Cf32.c',0,0,0,0,0
.L511:
	.byte	5,25,7,0,5,2
	.word	.L84
	.byte	3,219,1,1,5,12,9
	.half	.L338-.L84
	.byte	3,3,1,5,23,9
	.half	.L339-.L338
	.byte	1,5,17,9
	.half	.L39-.L339
	.byte	3,2,1,5,14,9
	.half	.L512-.L39
	.byte	1,5,10,9
	.half	.L513-.L512
	.byte	3,1,1,5,26,9
	.half	.L514-.L513
	.byte	3,125,1,5,23,9
	.half	.L38-.L514
	.byte	1,5,5,7,9
	.half	.L515-.L38
	.byte	3,6,1,5,1,9
	.half	.L40-.L515
	.byte	3,1,1,7,9
	.half	.L158-.L40
	.byte	0,1,1
.L509:
	.sdecl	'.debug_ranges',debug,cluster('VecSum_f32')
	.sect	'.debug_ranges'
.L157:
	.word	-1,.L84,0,.L158-.L84,0,0
	.sdecl	'.debug_info',debug,cluster('VecAvg_f32')
	.sect	'.debug_info'
.L159:
	.word	317
	.half	3
	.word	.L160
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_Cf32.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L162,.L161
	.byte	2
	.word	.L95
	.byte	3
	.byte	'VecAvg_f32',0,1,233,1,9
	.word	.L261
	.byte	1,1,1
	.word	.L86,.L275,.L85
	.byte	4
	.byte	'X',0,1,233,1,29
	.word	.L204,.L276
	.byte	4
	.byte	'nX',0,1,233,1,38
	.word	.L194,.L277
	.byte	5
	.word	.L86,.L275
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('VecAvg_f32')
	.sect	'.debug_abbrev'
.L160:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('VecAvg_f32')
	.sect	'.debug_line'
.L161:
	.word	.L517-.L516
.L516:
	.half	3
	.word	.L519-.L518
.L518:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_Cf32.c',0,0,0,0,0
.L519:
	.byte	5,9,7,0,5,2
	.word	.L86
	.byte	3,232,1,1,5,26,9
	.half	.L342-.L86
	.byte	3,2,1,5,32,9
	.half	.L341-.L342
	.byte	1,5,30,9
	.half	.L343-.L341
	.byte	1,5,5,9
	.half	.L520-.L343
	.byte	1,5,1,9
	.half	.L41-.L520
	.byte	3,1,1,7,9
	.half	.L163-.L41
	.byte	0,1,1
.L517:
	.sdecl	'.debug_ranges',debug,cluster('VecAvg_f32')
	.sect	'.debug_ranges'
.L162:
	.word	-1,.L86,0,.L163-.L86,0,0
	.sdecl	'.debug_info',debug,cluster('VecMax_f32')
	.sect	'.debug_info'
.L164:
	.word	348
	.half	3
	.word	.L165
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_Cf32.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L167,.L166
	.byte	2
	.word	.L95
	.byte	3
	.byte	'VecMax_f32',0,1,239,1,9
	.word	.L261
	.byte	1,1,1
	.word	.L88,.L278,.L87
	.byte	4
	.byte	'X',0,1,239,1,29
	.word	.L204,.L279
	.byte	4
	.byte	'nX',0,1,239,1,38
	.word	.L194,.L280
	.byte	5
	.word	.L88,.L278
	.byte	6
	.byte	'i',0,1,241,1,20
	.word	.L208,.L281
	.byte	6
	.byte	'r',0,1,242,1,20
	.word	.L261,.L282
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('VecMax_f32')
	.sect	'.debug_abbrev'
.L165:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('VecMax_f32')
	.sect	'.debug_line'
.L166:
	.word	.L522-.L521
.L521:
	.half	3
	.word	.L524-.L523
.L523:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_Cf32.c',0,0,0,0,0
.L524:
	.byte	5,24,7,0,5,2
	.word	.L88
	.byte	3,241,1,1,5,12,9
	.half	.L344-.L88
	.byte	3,2,1,5,23,9
	.half	.L345-.L344
	.byte	1,5,13,9
	.half	.L43-.L345
	.byte	3,2,1,5,10,9
	.half	.L45-.L43
	.byte	3,1,1,5,26,9
	.half	.L525-.L45
	.byte	3,125,1,5,23,9
	.half	.L42-.L525
	.byte	1,5,5,7,9
	.half	.L526-.L42
	.byte	3,6,1,5,1,9
	.half	.L46-.L526
	.byte	3,1,1,7,9
	.half	.L168-.L46
	.byte	0,1,1
.L522:
	.sdecl	'.debug_ranges',debug,cluster('VecMax_f32')
	.sect	'.debug_ranges'
.L167:
	.word	-1,.L88,0,.L168-.L88,0,0
	.sdecl	'.debug_info',debug,cluster('VecMin_f32')
	.sect	'.debug_info'
.L169:
	.word	348
	.half	3
	.word	.L170
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_Cf32.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L172,.L171
	.byte	2
	.word	.L95
	.byte	3
	.byte	'VecMin_f32',0,1,254,1,9
	.word	.L261
	.byte	1,1,1
	.word	.L90,.L283,.L89
	.byte	4
	.byte	'X',0,1,254,1,29
	.word	.L204,.L284
	.byte	4
	.byte	'nX',0,1,254,1,38
	.word	.L194,.L285
	.byte	5
	.word	.L90,.L283
	.byte	6
	.byte	'i',0,1,128,2,20
	.word	.L208,.L286
	.byte	6
	.byte	'r',0,1,129,2,20
	.word	.L261,.L287
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('VecMin_f32')
	.sect	'.debug_abbrev'
.L170:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('VecMin_f32')
	.sect	'.debug_line'
.L171:
	.word	.L528-.L527
.L527:
	.half	3
	.word	.L530-.L529
.L529:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_Cf32.c',0,0,0,0,0
.L530:
	.byte	5,24,7,0,5,2
	.word	.L90
	.byte	3,128,2,1,5,12,9
	.half	.L531-.L90
	.byte	3,2,1,5,23,9
	.half	.L348-.L531
	.byte	1,5,13,9
	.half	.L48-.L348
	.byte	3,2,1,5,10,9
	.half	.L50-.L48
	.byte	3,1,1,5,26,9
	.half	.L532-.L50
	.byte	3,125,1,5,23,9
	.half	.L47-.L532
	.byte	1,5,5,7,9
	.half	.L533-.L47
	.byte	3,6,1,5,1,9
	.half	.L51-.L533
	.byte	3,1,1,7,9
	.half	.L173-.L51
	.byte	0,1,1
.L528:
	.sdecl	'.debug_ranges',debug,cluster('VecMin_f32')
	.sect	'.debug_ranges'
.L172:
	.word	-1,.L90,0,.L173-.L90,0,0
	.sdecl	'.debug_info',debug,cluster('VecMinIdx_f32')
	.sect	'.debug_info'
.L174:
	.word	397
	.half	3
	.word	.L175
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_Cf32.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L177,.L176
	.byte	2
	.word	.L95
	.byte	3
	.byte	'VecMinIdx_f32',0,1,169,1,9
	.word	.L261
	.byte	1,1,1
	.word	.L78,.L288,.L77
	.byte	4
	.byte	'X',0,1,169,1,32
	.word	.L204,.L289
	.byte	4
	.byte	'nX',0,1,169,1,41
	.word	.L194,.L290
	.byte	4
	.byte	'minIdx',0,1,169,1,53
	.word	.L254,.L291
	.byte	4
	.byte	'maxIdx',0,1,169,1,69
	.word	.L254,.L292
	.byte	5
	.word	.L78,.L288
	.byte	6
	.byte	'minPeak',0,1,171,1,13
	.word	.L261,.L293
	.byte	6
	.byte	'm',0,1,172,1,13
	.word	.L194,.L294
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('VecMinIdx_f32')
	.sect	'.debug_abbrev'
.L175:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('VecMinIdx_f32')
	.sect	'.debug_line'
.L176:
	.word	.L535-.L534
.L534:
	.half	3
	.word	.L537-.L536
.L536:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_Cf32.c',0,0,0,0,0
.L537:
	.byte	5,23,7,0,5,2
	.word	.L78
	.byte	3,170,1,1,5,12,9
	.half	.L538-.L78
	.byte	3,3,1,5,23,9
	.half	.L332-.L538
	.byte	1,5,13,9
	.half	.L30-.L332
	.byte	3,2,1,5,16,9
	.half	.L539-.L30
	.byte	1,5,9,9
	.half	.L540-.L539
	.byte	1,5,23,7,9
	.half	.L541-.L540
	.byte	3,2,1,5,21,9
	.half	.L542-.L541
	.byte	3,1,1,5,13,9
	.half	.L31-.L542
	.byte	3,3,1,5,16,9
	.half	.L543-.L31
	.byte	1,5,9,9
	.half	.L544-.L543
	.byte	1,5,21,7,9
	.half	.L545-.L544
	.byte	3,2,1,5,10,9
	.half	.L32-.L545
	.byte	3,3,1,5,26,9
	.half	.L546-.L32
	.byte	3,115,1,5,23,9
	.half	.L29-.L546
	.byte	1,5,5,7,9
	.half	.L547-.L29
	.byte	3,16,1,5,1,9
	.half	.L33-.L547
	.byte	3,1,1,7,9
	.half	.L178-.L33
	.byte	0,1,1
.L535:
	.sdecl	'.debug_ranges',debug,cluster('VecMinIdx_f32')
	.sect	'.debug_ranges'
.L177:
	.word	-1,.L78,0,.L178-.L78,0,0
	.sdecl	'.debug_info',debug,cluster('VecMaxIdx_f32')
	.sect	'.debug_info'
.L179:
	.word	397
	.half	3
	.word	.L180
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_Cf32.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L182,.L181
	.byte	2
	.word	.L95
	.byte	3
	.byte	'VecMaxIdx_f32',0,1,144,1,9
	.word	.L261
	.byte	1,1,1
	.word	.L76,.L295,.L75
	.byte	4
	.byte	'X',0,1,144,1,32
	.word	.L204,.L296
	.byte	4
	.byte	'nX',0,1,144,1,41
	.word	.L194,.L297
	.byte	4
	.byte	'minIdx',0,1,144,1,53
	.word	.L254,.L298
	.byte	4
	.byte	'maxIdx',0,1,144,1,69
	.word	.L254,.L299
	.byte	5
	.word	.L76,.L295
	.byte	6
	.byte	'maxPeak',0,1,146,1,20
	.word	.L261,.L300
	.byte	6
	.byte	'm',0,1,147,1,20
	.word	.L208,.L301
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('VecMaxIdx_f32')
	.sect	'.debug_abbrev'
.L180:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('VecMaxIdx_f32')
	.sect	'.debug_line'
.L181:
	.word	.L549-.L548
.L548:
	.half	3
	.word	.L551-.L550
.L550:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_Cf32.c',0,0,0,0,0
.L551:
	.byte	5,30,7,0,5,2
	.word	.L76
	.byte	3,145,1,1,5,12,9
	.half	.L328-.L76
	.byte	3,3,1,5,23,9
	.half	.L329-.L328
	.byte	1,5,13,9
	.half	.L25-.L329
	.byte	3,2,1,5,16,9
	.half	.L552-.L25
	.byte	1,5,9,9
	.half	.L553-.L552
	.byte	1,5,23,7,9
	.half	.L554-.L553
	.byte	3,2,1,5,21,9
	.half	.L555-.L554
	.byte	3,1,1,5,13,9
	.half	.L26-.L555
	.byte	3,3,1,5,16,9
	.half	.L556-.L26
	.byte	1,5,9,9
	.half	.L557-.L556
	.byte	1,5,21,7,9
	.half	.L558-.L557
	.byte	3,2,1,5,10,9
	.half	.L27-.L558
	.byte	3,3,1,5,26,9
	.half	.L559-.L27
	.byte	3,115,1,5,23,9
	.half	.L24-.L559
	.byte	1,5,5,7,9
	.half	.L560-.L24
	.byte	3,16,1,5,1,9
	.half	.L28-.L560
	.byte	3,1,1,7,9
	.half	.L183-.L28
	.byte	0,1,1
.L549:
	.sdecl	'.debug_ranges',debug,cluster('VecMaxIdx_f32')
	.sect	'.debug_ranges'
.L182:
	.word	-1,.L76,0,.L183-.L76,0,0
	.sdecl	'.debug_info',debug,cluster('VecHalfSwap_f32')
	.sect	'.debug_info'
.L184:
	.word	394
	.half	3
	.word	.L185
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_Cf32.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L187,.L186
	.byte	2
	.word	.L95
	.byte	3
	.byte	'VecHalfSwap_f32',0,1,141,2,6,1,1,1
	.word	.L92,.L302,.L91
	.byte	4
	.byte	'X',0,1,141,2,31
	.word	.L204,.L303
	.byte	4
	.byte	'nX',0,1,141,2,40
	.word	.L194,.L304
	.byte	5
	.word	.L92,.L302
	.byte	6
	.byte	'i',0,1,143,2,20
	.word	.L208,.L305
	.byte	6
	.byte	'half',0,1,144,2,20
	.word	.L208,.L306
	.byte	6
	.byte	'F',0,1,145,2,20
	.word	.L204,.L307
	.byte	5
	.word	.L53,.L308
	.byte	6
	.byte	'tmp',0,1,149,2,17
	.word	.L261,.L309
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('VecHalfSwap_f32')
	.sect	'.debug_abbrev'
.L185:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('VecHalfSwap_f32')
	.sect	'.debug_line'
.L186:
	.word	.L562-.L561
.L561:
	.half	3
	.word	.L564-.L563
.L563:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_Cf32.c',0,0,0,0,0
.L564:
	.byte	5,32,7,0,5,2
	.word	.L92
	.byte	3,143,2,1,5,30,9
	.half	.L565-.L92
	.byte	1,5,29,9
	.half	.L350-.L565
	.byte	3,1,1,5,12,9
	.half	.L351-.L350
	.byte	3,2,1,5,25,9
	.half	.L352-.L351
	.byte	1,5,23,9
	.half	.L53-.L352
	.byte	3,2,1,5,14,9
	.half	.L354-.L53
	.byte	3,1,1,5,12,9
	.half	.L566-.L354
	.byte	1,9
	.half	.L567-.L566
	.byte	3,1,1,5,10,9
	.half	.L568-.L567
	.byte	3,1,1,5,15,9
	.half	.L569-.L568
	.byte	1,5,28,9
	.half	.L308-.L569
	.byte	3,123,1,5,25,9
	.half	.L52-.L308
	.byte	1,5,1,7,9
	.half	.L570-.L52
	.byte	3,7,1,7,9
	.half	.L188-.L570
	.byte	0,1,1
.L562:
	.sdecl	'.debug_ranges',debug,cluster('VecHalfSwap_f32')
	.sect	'.debug_ranges'
.L187:
	.word	-1,.L92,0,.L188-.L92,0,0
	.sdecl	'.debug_loc',debug,cluster('CplxVecCpy_f32')
	.sect	'.debug_loc'
.L63:
	.word	-1,.L64,0,.L200-.L64
	.half	2
	.byte	138,0
	.word	0,0
.L202:
	.word	-1,.L64,0,.L200-.L64
	.half	1
	.byte	101
	.word	0,0
.L201:
	.word	-1,.L64,0,.L200-.L64
	.half	1
	.byte	100
	.word	0,0
.L203:
	.word	-1,.L64,0,.L200-.L64
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('CplxVecCpy_f32S')
	.sect	'.debug_loc'
.L61:
	.word	-1,.L62,0,.L189-.L62
	.half	2
	.byte	138,0
	.word	0,0
.L193:
	.word	-1,.L62,0,.L189-.L62
	.half	1
	.byte	101
	.word	0,0
.L191:
	.word	-1,.L62,0,.L189-.L62
	.half	1
	.byte	100
	.word	0,0
.L196:
	.word	-1,.L62,0,.L189-.L62
	.half	1
	.byte	85
	.word	0,0
.L195:
	.word	-1,.L62,0,.L189-.L62
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('CplxVecMag_f32')
	.sect	'.debug_loc'
.L67:
	.word	-1,.L68,0,.L215-.L68
	.half	2
	.byte	138,0
	.word	0,0
.L216:
	.word	-1,.L68,0,.L13-.L68
	.half	1
	.byte	100
	.word	.L313-.L68,.L215-.L68
	.half	1
	.byte	111
	.word	0,0
.L224:
	.word	0,0
.L222:
	.word	0,0
.L218:
	.word	-1,.L68,.L316-.L68,.L317-.L68
	.half	1
	.byte	88
	.word	.L12-.L68,.L215-.L68
	.half	1
	.byte	88
	.word	0,0
.L217:
	.word	-1,.L68,0,.L13-.L68
	.half	1
	.byte	84
	.word	.L314-.L68,.L215-.L68
	.half	1
	.byte	89
	.word	0,0
.L219:
	.word	-1,.L68,.L315-.L68,.L215-.L68
	.half	1
	.byte	108
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('CplxVecMul_f32')
	.sect	'.debug_loc'
.L69:
	.word	-1,.L70,0,.L225-.L70
	.half	2
	.byte	138,0
	.word	0,0
.L238:
	.word	-1,.L70,.L318-.L70,.L17-.L70
	.half	2
	.byte	144,32
	.word	0,0
.L226:
	.word	-1,.L70,0,.L225-.L70
	.half	1
	.byte	100
	.word	0,0
.L233:
	.word	0,0
.L235:
	.word	0,0
.L228:
	.word	-1,.L70,0,.L225-.L70
	.half	1
	.byte	101
	.word	0,0
.L229:
	.word	-1,.L70,0,.L225-.L70
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('CplxVecPwr_f32')
	.sect	'.debug_loc'
.L65:
	.word	-1,.L66,0,.L205-.L66
	.half	2
	.byte	138,0
	.word	0,0
.L206:
	.word	-1,.L66,0,.L205-.L66
	.half	1
	.byte	100
	.word	0,0
.L213:
	.word	0,0
.L209:
	.word	-1,.L66,.L311-.L66,.L312-.L66
	.half	5
	.byte	144,32,157,32,0
	.word	.L8-.L66,.L205-.L66
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L207:
	.word	-1,.L66,0,.L205-.L66
	.half	1
	.byte	84
	.word	0,0
.L210:
	.word	-1,.L66,.L310-.L66,.L205-.L66
	.half	1
	.byte	111
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('CplxVecRst_f32')
	.sect	'.debug_loc'
.L59:
	.word	-1,.L60,0,.L197-.L60
	.half	2
	.byte	138,0
	.word	0,0
.L198:
	.word	-1,.L60,0,.L197-.L60
	.half	1
	.byte	100
	.word	0,0
.L199:
	.word	-1,.L60,0,.L197-.L60
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('VecAvg_f32')
	.sect	'.debug_loc'
.L85:
	.word	-1,.L86,0,.L275-.L86
	.half	2
	.byte	138,0
	.word	0,0
.L276:
	.word	-1,.L86,0,.L341-.L86
	.half	1
	.byte	100
	.word	0,0
.L277:
	.word	-1,.L86,0,.L341-.L86
	.half	1
	.byte	84
	.word	.L342-.L86,.L343-.L86
	.half	1
	.byte	95
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('VecGain_f32')
	.sect	'.debug_loc'
.L81:
	.word	-1,.L82,0,.L259-.L82
	.half	2
	.byte	138,0
	.word	0,0
.L260:
	.word	-1,.L82,0,.L259-.L82
	.half	1
	.byte	100
	.word	0,0
.L262:
	.word	-1,.L82,0,.L259-.L82
	.half	1
	.byte	84
	.word	0,0
.L264:
	.word	-1,.L82,.L336-.L82,.L337-.L82
	.half	1
	.byte	95
	.word	.L36-.L82,.L259-.L82
	.half	1
	.byte	95
	.word	0,0
.L263:
	.word	-1,.L82,0,.L259-.L82
	.half	1
	.byte	85
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('VecHalfSwap_f32')
	.sect	'.debug_loc'
.L307:
	.word	-1,.L92,.L351-.L92,.L302-.L92
	.half	1
	.byte	111
	.word	0,0
.L91:
	.word	-1,.L92,0,.L302-.L92
	.half	2
	.byte	138,0
	.word	0,0
.L303:
	.word	-1,.L92,0,.L302-.L92
	.half	1
	.byte	100
	.word	0,0
.L306:
	.word	-1,.L92,.L350-.L92,.L302-.L92
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L305:
	.word	-1,.L92,.L352-.L92,.L353-.L92
	.half	1
	.byte	95
	.word	.L52-.L92,.L302-.L92
	.half	1
	.byte	95
	.word	0,0
.L304:
	.word	-1,.L92,0,.L302-.L92
	.half	1
	.byte	84
	.word	0,0
.L309:
	.word	-1,.L92,.L354-.L92,.L52-.L92
	.half	1
	.byte	81
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('VecMaxIdx_f32')
	.sect	'.debug_loc'
.L75:
	.word	-1,.L76,0,.L295-.L76
	.half	2
	.byte	138,0
	.word	0,0
.L296:
	.word	-1,.L76,0,.L295-.L76
	.half	1
	.byte	100
	.word	0,0
.L301:
	.word	-1,.L76,.L329-.L76,.L330-.L76
	.half	5
	.byte	144,32,157,32,0
	.word	.L24-.L76,.L295-.L76
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L299:
	.word	-1,.L76,0,.L295-.L76
	.half	1
	.byte	102
	.word	0,0
.L300:
	.word	-1,.L76,.L328-.L76,.L295-.L76
	.half	1
	.byte	82
	.word	0,0
.L298:
	.word	-1,.L76,0,.L295-.L76
	.half	1
	.byte	101
	.word	0,0
.L297:
	.word	-1,.L76,0,.L295-.L76
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('VecMax_f32')
	.sect	'.debug_loc'
.L87:
	.word	-1,.L88,0,.L278-.L88
	.half	2
	.byte	138,0
	.word	0,0
.L279:
	.word	-1,.L88,0,.L278-.L88
	.half	1
	.byte	100
	.word	0,0
.L281:
	.word	-1,.L88,.L345-.L88,.L346-.L88
	.half	5
	.byte	144,32,157,32,0
	.word	.L42-.L88,.L278-.L88
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L280:
	.word	-1,.L88,0,.L278-.L88
	.half	1
	.byte	84
	.word	0,0
.L282:
	.word	-1,.L88,.L344-.L88,.L278-.L88
	.half	1
	.byte	82
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('VecMinIdx_f32')
	.sect	'.debug_loc'
.L77:
	.word	-1,.L78,0,.L288-.L78
	.half	2
	.byte	138,0
	.word	0,0
.L289:
	.word	-1,.L78,0,.L288-.L78
	.half	1
	.byte	100
	.word	0,0
.L294:
	.word	-1,.L78,.L332-.L78,.L333-.L78
	.half	5
	.byte	144,32,157,32,0
	.word	.L29-.L78,.L288-.L78
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L292:
	.word	-1,.L78,0,.L288-.L78
	.half	1
	.byte	102
	.word	0,0
.L291:
	.word	-1,.L78,0,.L288-.L78
	.half	1
	.byte	101
	.word	0,0
.L293:
	.word	-1,.L78,.L331-.L78,.L288-.L78
	.half	1
	.byte	82
	.word	0,0
.L290:
	.word	-1,.L78,0,.L288-.L78
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('VecMin_f32')
	.sect	'.debug_loc'
.L89:
	.word	-1,.L90,0,.L283-.L90
	.half	2
	.byte	138,0
	.word	0,0
.L284:
	.word	-1,.L90,0,.L283-.L90
	.half	1
	.byte	100
	.word	0,0
.L286:
	.word	-1,.L90,.L348-.L90,.L349-.L90
	.half	5
	.byte	144,32,157,32,0
	.word	.L47-.L90,.L283-.L90
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L285:
	.word	-1,.L90,0,.L283-.L90
	.half	1
	.byte	84
	.word	0,0
.L287:
	.word	-1,.L90,.L347-.L90,.L283-.L90
	.half	1
	.byte	82
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('VecOfs_f32')
	.sect	'.debug_loc'
.L79:
	.word	-1,.L80,0,.L265-.L80
	.half	2
	.byte	138,0
	.word	0,0
.L266:
	.word	-1,.L80,0,.L265-.L80
	.half	1
	.byte	100
	.word	0,0
.L269:
	.word	-1,.L80,.L334-.L80,.L335-.L80
	.half	1
	.byte	95
	.word	.L34-.L80,.L265-.L80
	.half	1
	.byte	95
	.word	0,0
.L268:
	.word	-1,.L80,0,.L265-.L80
	.half	1
	.byte	85
	.word	0,0
.L267:
	.word	-1,.L80,0,.L265-.L80
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('VecPwrdB_SF')
	.sect	'.debug_loc'
.L255:
	.word	-1,.L74,0,.L23-.L74
	.half	1
	.byte	100
	.word	.L323-.L74,.L253-.L74
	.half	1
	.byte	111
	.word	0,0
.L73:
	.word	-1,.L74,0,.L253-.L74
	.half	2
	.byte	138,0
	.word	0,0
.L256:
	.word	-1,.L74,0,.L23-.L74
	.half	1
	.byte	101
	.word	.L324-.L74,.L253-.L74
	.half	1
	.byte	108
	.word	0,0
.L258:
	.word	-1,.L74,.L326-.L74,.L327-.L74
	.half	1
	.byte	95
	.word	.L22-.L74,.L253-.L74
	.half	1
	.byte	95
	.word	0,0
.L257:
	.word	-1,.L74,0,.L23-.L74
	.half	1
	.byte	84
	.word	.L325-.L74,.L253-.L74
	.half	1
	.byte	88
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('VecPwrdB_f32')
	.sect	'.debug_loc'
.L71:
	.word	-1,.L72,0,.L249-.L72
	.half	2
	.byte	138,0
	.word	0,0
.L250:
	.word	-1,.L72,0,.L21-.L72
	.half	1
	.byte	100
	.word	.L319-.L72,.L249-.L72
	.half	1
	.byte	111
	.word	0,0
.L252:
	.word	-1,.L72,.L321-.L72,.L322-.L72
	.half	1
	.byte	95
	.word	.L20-.L72,.L249-.L72
	.half	1
	.byte	95
	.word	0,0
.L251:
	.word	-1,.L72,0,.L21-.L72
	.half	1
	.byte	84
	.word	.L320-.L72,.L249-.L72
	.half	1
	.byte	88
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('VecSum_f32')
	.sect	'.debug_loc'
.L83:
	.word	-1,.L84,0,.L270-.L84
	.half	2
	.byte	138,0
	.word	0,0
.L271:
	.word	-1,.L84,0,.L270-.L84
	.half	1
	.byte	100
	.word	0,0
.L274:
	.word	-1,.L84,.L339-.L84,.L340-.L84
	.half	1
	.byte	95
	.word	.L38-.L84,.L270-.L84
	.half	1
	.byte	95
	.word	0,0
.L272:
	.word	-1,.L84,0,.L270-.L84
	.half	1
	.byte	84
	.word	0,0
.L273:
	.word	-1,.L84,.L338-.L84,.L270-.L84
	.half	1
	.byte	82
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('VecWin_f32')
	.sect	'.debug_loc'
.L93:
	.word	-1,.L94,0,.L239-.L94
	.half	2
	.byte	138,0
	.word	0,0
.L242:
	.word	-1,.L94,0,.L355-.L94
	.half	1
	.byte	101
	.word	.L355-.L94,.L54-.L94
	.half	1
	.byte	111
	.word	0,0
.L240:
	.word	-1,.L94,0,.L239-.L94
	.half	1
	.byte	100
	.word	0,0
.L248:
	.word	-1,.L94,.L359-.L94,.L360-.L94
	.half	1
	.byte	83
	.word	.L57-.L94,.L54-.L94
	.half	1
	.byte	83
	.word	0,0
.L245:
	.word	-1,.L94,0,.L239-.L94
	.half	1
	.byte	86
	.word	0,0
.L244:
	.word	-1,.L94,0,.L239-.L94
	.half	1
	.byte	85
	.word	0,0
.L243:
	.word	-1,.L94,0,.L239-.L94
	.half	1
	.byte	84
	.word	0,0
.L247:
	.word	-1,.L94,.L357-.L94,.L358-.L94
	.half	1
	.byte	82
	.word	.L361-.L94,.L58-.L94
	.half	5
	.byte	144,32,157,32,0
	.word	.L362-.L94,.L363-.L94
	.half	1
	.byte	82
	.word	.L363-.L94,.L364-.L94
	.half	1
	.byte	95
	.word	0,0
.L246:
	.word	-1,.L94,0,.L239-.L94
	.half	1
	.byte	87
	.word	.L356-.L94,.L56-.L94
	.half	1
	.byte	95
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L571:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('CplxVecRst_f32')
	.sect	'.debug_frame'
	.word	24
	.word	.L571,.L60,.L197-.L60
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('CplxVecCpy_f32S')
	.sect	'.debug_frame'
	.word	20
	.word	.L571,.L62,.L189-.L62
	.byte	8,18,8,19,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('CplxVecCpy_f32')
	.sect	'.debug_frame'
	.word	16
	.word	.L571,.L64,.L200-.L64
	.byte	8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('CplxVecPwr_f32')
	.sect	'.debug_frame'
	.word	20
	.word	.L571,.L66,.L205-.L66
	.byte	8,19,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('CplxVecMag_f32')
	.sect	'.debug_frame'
	.word	12
	.word	.L571,.L68,.L215-.L68
	.sdecl	'.debug_frame',debug,cluster('CplxVecMul_f32')
	.sect	'.debug_frame'
	.word	20
	.word	.L571,.L70,.L225-.L70
	.byte	8,18,8,19,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('VecPwrdB_f32')
	.sect	'.debug_frame'
	.word	12
	.word	.L571,.L72,.L249-.L72
	.sdecl	'.debug_frame',debug,cluster('VecPwrdB_SF')
	.sect	'.debug_frame'
	.word	12
	.word	.L571,.L74,.L253-.L74
	.sdecl	'.debug_frame',debug,cluster('VecMaxIdx_f32')
	.sect	'.debug_frame'
	.word	20
	.word	.L571,.L76,.L295-.L76
	.byte	8,18,8,19,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('VecMinIdx_f32')
	.sect	'.debug_frame'
	.word	20
	.word	.L571,.L78,.L288-.L78
	.byte	8,18,8,19,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('VecOfs_f32')
	.sect	'.debug_frame'
	.word	24
	.word	.L571,.L80,.L265-.L80
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('VecGain_f32')
	.sect	'.debug_frame'
	.word	24
	.word	.L571,.L82,.L259-.L82
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('VecSum_f32')
	.sect	'.debug_frame'
	.word	24
	.word	.L571,.L84,.L270-.L84
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('VecAvg_f32')
	.sect	'.debug_frame'
	.word	12
	.word	.L571,.L86,.L275-.L86
	.sdecl	'.debug_frame',debug,cluster('VecMax_f32')
	.sect	'.debug_frame'
	.word	24
	.word	.L571,.L88,.L278-.L88
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('VecMin_f32')
	.sect	'.debug_frame'
	.word	24
	.word	.L571,.L90,.L283-.L90
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('VecHalfSwap_f32')
	.sect	'.debug_frame'
	.word	24
	.word	.L571,.L92,.L302-.L92
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('VecWin_f32')
	.sect	'.debug_frame'
	.word	20
	.word	.L571,.L94,.L239-.L94
	.byte	8,18,8,19,8,22,8,23
	; Module end
