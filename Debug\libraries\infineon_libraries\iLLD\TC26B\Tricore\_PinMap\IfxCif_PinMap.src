	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc27260a --dep-file=IfxCif_PinMap.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2\\\\xf_asr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCif_PinMap.src ../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCif_PinMap.c"
	.compiler_name		"ctc"
	;source	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCif_PinMap.c'

	
$TC16X
	
	.sdecl	'.rodata.IfxCif_PinMap.IfxCif_CLK_P00_7_IN',data,rom,cluster('IfxCif_CLK_P00_7_IN')
	.sect	'.rodata.IfxCif_PinMap.IfxCif_CLK_P00_7_IN'
	.global	IfxCif_CLK_P00_7_IN
	.align	4
IfxCif_CLK_P00_7_IN:	.type	object
	.size	IfxCif_CLK_P00_7_IN,12
	.word	-116515072,-268197888
	.byte	7
	.space	3
	.sdecl	'.rodata.IfxCif_PinMap.IfxCif_D0_P02_0_IN',data,rom,cluster('IfxCif_D0_P02_0_IN')
	.sect	'.rodata.IfxCif_PinMap.IfxCif_D0_P02_0_IN'
	.global	IfxCif_D0_P02_0_IN
	.align	4
IfxCif_D0_P02_0_IN:	.type	object
	.size	IfxCif_D0_P02_0_IN,12
	.word	-116515072,-268197376
	.space	4
	.sdecl	'.rodata.IfxCif_PinMap.IfxCif_D10_P00_1_IN',data,rom,cluster('IfxCif_D10_P00_1_IN')
	.sect	'.rodata.IfxCif_PinMap.IfxCif_D10_P00_1_IN'
	.global	IfxCif_D10_P00_1_IN
	.align	4
IfxCif_D10_P00_1_IN:	.type	object
	.size	IfxCif_D10_P00_1_IN,12
	.word	-116515072,-268197888
	.byte	1
	.space	3
	.sdecl	'.rodata.IfxCif_PinMap.IfxCif_D11_P00_2_IN',data,rom,cluster('IfxCif_D11_P00_2_IN')
	.sect	'.rodata.IfxCif_PinMap.IfxCif_D11_P00_2_IN'
	.global	IfxCif_D11_P00_2_IN
	.align	4
IfxCif_D11_P00_2_IN:	.type	object
	.size	IfxCif_D11_P00_2_IN,12
	.word	-116515072,-268197888
	.byte	2
	.space	3
	.sdecl	'.rodata.IfxCif_PinMap.IfxCif_D12_P00_3_IN',data,rom,cluster('IfxCif_D12_P00_3_IN')
	.sect	'.rodata.IfxCif_PinMap.IfxCif_D12_P00_3_IN'
	.global	IfxCif_D12_P00_3_IN
	.align	4
IfxCif_D12_P00_3_IN:	.type	object
	.size	IfxCif_D12_P00_3_IN,12
	.word	-116515072,-268197888
	.byte	3
	.space	3
	.sdecl	'.rodata.IfxCif_PinMap.IfxCif_D13_P00_4_IN',data,rom,cluster('IfxCif_D13_P00_4_IN')
	.sect	'.rodata.IfxCif_PinMap.IfxCif_D13_P00_4_IN'
	.global	IfxCif_D13_P00_4_IN
	.align	4
IfxCif_D13_P00_4_IN:	.type	object
	.size	IfxCif_D13_P00_4_IN,12
	.word	-116515072,-268197888
	.byte	4
	.space	3
	.sdecl	'.rodata.IfxCif_PinMap.IfxCif_D14_P00_5_IN',data,rom,cluster('IfxCif_D14_P00_5_IN')
	.sect	'.rodata.IfxCif_PinMap.IfxCif_D14_P00_5_IN'
	.global	IfxCif_D14_P00_5_IN
	.align	4
IfxCif_D14_P00_5_IN:	.type	object
	.size	IfxCif_D14_P00_5_IN,12
	.word	-116515072,-268197888
	.byte	5
	.space	3
	.sdecl	'.rodata.IfxCif_PinMap.IfxCif_D15_P00_6_IN',data,rom,cluster('IfxCif_D15_P00_6_IN')
	.sect	'.rodata.IfxCif_PinMap.IfxCif_D15_P00_6_IN'
	.global	IfxCif_D15_P00_6_IN
	.align	4
IfxCif_D15_P00_6_IN:	.type	object
	.size	IfxCif_D15_P00_6_IN,12
	.word	-116515072,-268197888
	.byte	6
	.space	3
	.sdecl	'.rodata.IfxCif_PinMap.IfxCif_D1_P02_1_IN',data,rom,cluster('IfxCif_D1_P02_1_IN')
	.sect	'.rodata.IfxCif_PinMap.IfxCif_D1_P02_1_IN'
	.global	IfxCif_D1_P02_1_IN
	.align	4
IfxCif_D1_P02_1_IN:	.type	object
	.size	IfxCif_D1_P02_1_IN,12
	.word	-116515072,-268197376
	.byte	1
	.space	3
	.sdecl	'.rodata.IfxCif_PinMap.IfxCif_D2_P02_2_IN',data,rom,cluster('IfxCif_D2_P02_2_IN')
	.sect	'.rodata.IfxCif_PinMap.IfxCif_D2_P02_2_IN'
	.global	IfxCif_D2_P02_2_IN
	.align	4
IfxCif_D2_P02_2_IN:	.type	object
	.size	IfxCif_D2_P02_2_IN,12
	.word	-116515072,-268197376
	.byte	2
	.space	3
	.sdecl	'.rodata.IfxCif_PinMap.IfxCif_D3_P02_3_IN',data,rom,cluster('IfxCif_D3_P02_3_IN')
	.sect	'.rodata.IfxCif_PinMap.IfxCif_D3_P02_3_IN'
	.global	IfxCif_D3_P02_3_IN
	.align	4
IfxCif_D3_P02_3_IN:	.type	object
	.size	IfxCif_D3_P02_3_IN,12
	.word	-116515072,-268197376
	.byte	3
	.space	3
	.sdecl	'.rodata.IfxCif_PinMap.IfxCif_D4_P02_4_IN',data,rom,cluster('IfxCif_D4_P02_4_IN')
	.sect	'.rodata.IfxCif_PinMap.IfxCif_D4_P02_4_IN'
	.global	IfxCif_D4_P02_4_IN
	.align	4
IfxCif_D4_P02_4_IN:	.type	object
	.size	IfxCif_D4_P02_4_IN,12
	.word	-116515072,-268197376
	.byte	4
	.space	3
	.sdecl	'.rodata.IfxCif_PinMap.IfxCif_D5_P02_5_IN',data,rom,cluster('IfxCif_D5_P02_5_IN')
	.sect	'.rodata.IfxCif_PinMap.IfxCif_D5_P02_5_IN'
	.global	IfxCif_D5_P02_5_IN
	.align	4
IfxCif_D5_P02_5_IN:	.type	object
	.size	IfxCif_D5_P02_5_IN,12
	.word	-116515072,-268197376
	.byte	5
	.space	3
	.sdecl	'.rodata.IfxCif_PinMap.IfxCif_D6_P02_6_IN',data,rom,cluster('IfxCif_D6_P02_6_IN')
	.sect	'.rodata.IfxCif_PinMap.IfxCif_D6_P02_6_IN'
	.global	IfxCif_D6_P02_6_IN
	.align	4
IfxCif_D6_P02_6_IN:	.type	object
	.size	IfxCif_D6_P02_6_IN,12
	.word	-116515072,-268197376
	.byte	6
	.space	3
	.sdecl	'.rodata.IfxCif_PinMap.IfxCif_D7_P02_7_IN',data,rom,cluster('IfxCif_D7_P02_7_IN')
	.sect	'.rodata.IfxCif_PinMap.IfxCif_D7_P02_7_IN'
	.global	IfxCif_D7_P02_7_IN
	.align	4
IfxCif_D7_P02_7_IN:	.type	object
	.size	IfxCif_D7_P02_7_IN,12
	.word	-116515072,-268197376
	.byte	7
	.space	3
	.sdecl	'.rodata.IfxCif_PinMap.IfxCif_D8_P02_8_IN',data,rom,cluster('IfxCif_D8_P02_8_IN')
	.sect	'.rodata.IfxCif_PinMap.IfxCif_D8_P02_8_IN'
	.global	IfxCif_D8_P02_8_IN
	.align	4
IfxCif_D8_P02_8_IN:	.type	object
	.size	IfxCif_D8_P02_8_IN,12
	.word	-116515072,-268197376
	.byte	8
	.space	3
	.sdecl	'.rodata.IfxCif_PinMap.IfxCif_D9_P00_0_IN',data,rom,cluster('IfxCif_D9_P00_0_IN')
	.sect	'.rodata.IfxCif_PinMap.IfxCif_D9_P00_0_IN'
	.global	IfxCif_D9_P00_0_IN
	.align	4
IfxCif_D9_P00_0_IN:	.type	object
	.size	IfxCif_D9_P00_0_IN,12
	.word	-116515072,-268197888
	.space	4
	.sdecl	'.rodata.IfxCif_PinMap.IfxCif_HSNC_P00_9_IN',data,rom,cluster('IfxCif_HSNC_P00_9_IN')
	.sect	'.rodata.IfxCif_PinMap.IfxCif_HSNC_P00_9_IN'
	.global	IfxCif_HSNC_P00_9_IN
	.align	4
IfxCif_HSNC_P00_9_IN:	.type	object
	.size	IfxCif_HSNC_P00_9_IN,12
	.word	-116515072,-268197888
	.byte	9
	.space	3
	.sdecl	'.rodata.IfxCif_PinMap.IfxCif_VSNC_P00_8_IN',data,rom,cluster('IfxCif_VSNC_P00_8_IN')
	.sect	'.rodata.IfxCif_PinMap.IfxCif_VSNC_P00_8_IN'
	.global	IfxCif_VSNC_P00_8_IN
	.align	4
IfxCif_VSNC_P00_8_IN:	.type	object
	.size	IfxCif_VSNC_P00_8_IN,12
	.word	-116515072,-268197888
	.byte	8
	.space	3
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L3:
	.word	83026
	.half	3
	.word	.L4
	.byte	4
.L2:
	.byte	1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCif_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L5
	.byte	2,1,1,3
	.word	237
	.byte	4
	.byte	'Ifx__jump_and_link',0,3,1,61,17,1,1,5
	.byte	'fun',0,1,61,43
	.word	240
	.byte	6,0,7
	.byte	'__fract',0,4,128,1,7
	.byte	'float',0,4,4,8
	.byte	'Ifx__float_to_fract',0,3,1,152,2,18
	.word	285
	.byte	1,1,5
	.byte	'a',0,1,152,2,44
	.word	297
	.byte	6,0,7
	.byte	'unsigned long long int',0,8,7,9
	.byte	'void',0,3
	.word	377
	.byte	8
	.byte	'__ld64',0,3,2,135,1,19
	.word	351
	.byte	1,1,5
	.byte	'addr',0,2,135,1,32
	.word	383
	.byte	6,0,4
	.byte	'__st64',0,3,2,143,1,17,1,1,5
	.byte	'addr',0,2,143,1,30
	.word	383
	.byte	5
	.byte	'value',0,2,143,1,43
	.word	351
	.byte	6,0,7
	.byte	'unsigned int',0,4,7,7
	.byte	'int',0,4,5,7
	.byte	'unsigned char',0,1,8,7
	.byte	'unsigned short int',0,2,7,10
	.byte	'_Ifx_P_OUT_Bits',0,4,143,3,16,4,11
	.byte	'P0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	492
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	492
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	492
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	16,0,2,35,2,0,12,4,181,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	531
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMR_Bits',0,4,169,2,16,4,11
	.byte	'PS0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	492
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	492
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	492
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'PCL0',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	492
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	492
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	492
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	492
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	492
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	492
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	492
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	492
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	492
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	492
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	492
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	492
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	492
	.byte	1,0,2,35,3,0,12,4,133,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	847
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ID_Bits',0,4,110,16,4,11
	.byte	'MODREV',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	509
	.byte	16,0,2,35,2,0,12,4,148,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1418
	.byte	4,2,35,0,0,14,4
	.word	492
	.byte	15,3,0,10
	.byte	'_Ifx_P_IOCR0_Bits',0,4,140,1,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	3,5,2,35,0,11
	.byte	'PC0',0,1
	.word	492
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	492
	.byte	3,5,2,35,1,11
	.byte	'PC1',0,1
	.word	492
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	492
	.byte	3,5,2,35,2,11
	.byte	'PC2',0,1
	.word	492
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	3,5,2,35,3,11
	.byte	'PC3',0,1
	.word	492
	.byte	5,0,2,35,3,0,12,4,164,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1546
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR4_Bits',0,4,166,1,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	3,5,2,35,0,11
	.byte	'PC4',0,1
	.word	492
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	492
	.byte	3,5,2,35,1,11
	.byte	'PC5',0,1
	.word	492
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	492
	.byte	3,5,2,35,2,11
	.byte	'PC6',0,1
	.word	492
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	3,5,2,35,3,11
	.byte	'PC7',0,1
	.word	492
	.byte	5,0,2,35,3,0,12,4,180,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1761
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR8_Bits',0,4,179,1,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	3,5,2,35,0,11
	.byte	'PC8',0,1
	.word	492
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	492
	.byte	3,5,2,35,1,11
	.byte	'PC9',0,1
	.word	492
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	492
	.byte	3,5,2,35,2,11
	.byte	'PC10',0,1
	.word	492
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	3,5,2,35,3,11
	.byte	'PC11',0,1
	.word	492
	.byte	5,0,2,35,3,0,12,4,188,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1976
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR12_Bits',0,4,153,1,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	3,5,2,35,0,11
	.byte	'PC12',0,1
	.word	492
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	492
	.byte	3,5,2,35,1,11
	.byte	'PC13',0,1
	.word	492
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	492
	.byte	3,5,2,35,2,11
	.byte	'PC14',0,1
	.word	492
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	3,5,2,35,3,11
	.byte	'PC15',0,1
	.word	492
	.byte	5,0,2,35,3,0,12,4,172,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2193
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IN_Bits',0,4,118,16,4,11
	.byte	'P0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	492
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	492
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	492
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	16,0,2,35,2,0,12,4,156,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2413
	.byte	4,2,35,0,0,14,24
	.word	492
	.byte	15,23,0,10
	.byte	'_Ifx_P_PDR0_Bits',0,4,205,3,16,4,11
	.byte	'PD0',0,1
	.word	492
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	492
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'PD2',0,1
	.word	492
	.byte	3,5,2,35,1,11
	.byte	'PL2',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'PD3',0,1
	.word	492
	.byte	3,1,2,35,1,11
	.byte	'PL3',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'PD4',0,1
	.word	492
	.byte	3,5,2,35,2,11
	.byte	'PL4',0,1
	.word	492
	.byte	1,4,2,35,2,11
	.byte	'PD5',0,1
	.word	492
	.byte	3,1,2,35,2,11
	.byte	'PL5',0,1
	.word	492
	.byte	1,0,2,35,2,11
	.byte	'PD6',0,1
	.word	492
	.byte	3,5,2,35,3,11
	.byte	'PL6',0,1
	.word	492
	.byte	1,4,2,35,3,11
	.byte	'PD7',0,1
	.word	492
	.byte	3,1,2,35,3,11
	.byte	'PL7',0,1
	.word	492
	.byte	1,0,2,35,3,0,12,4,205,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2736
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PDR1_Bits',0,4,226,3,16,4,11
	.byte	'PD8',0,1
	.word	492
	.byte	3,5,2,35,0,11
	.byte	'PL8',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'PD9',0,1
	.word	492
	.byte	3,1,2,35,0,11
	.byte	'PL9',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'PD10',0,1
	.word	492
	.byte	3,5,2,35,1,11
	.byte	'PL10',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'PD11',0,1
	.word	492
	.byte	3,1,2,35,1,11
	.byte	'PL11',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'PD12',0,1
	.word	492
	.byte	3,5,2,35,2,11
	.byte	'PL12',0,1
	.word	492
	.byte	1,4,2,35,2,11
	.byte	'PD13',0,1
	.word	492
	.byte	3,1,2,35,2,11
	.byte	'PL13',0,1
	.word	492
	.byte	1,0,2,35,2,11
	.byte	'PD14',0,1
	.word	492
	.byte	3,5,2,35,3,11
	.byte	'PL14',0,1
	.word	492
	.byte	1,4,2,35,3,11
	.byte	'PD15',0,1
	.word	492
	.byte	3,1,2,35,3,11
	.byte	'PL15',0,1
	.word	492
	.byte	1,0,2,35,3,0,12,4,213,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3040
	.byte	4,2,35,0,0,14,8
	.word	492
	.byte	15,7,0,10
	.byte	'_Ifx_P_ESR_Bits',0,4,88,16,4,11
	.byte	'EN0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	492
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	492
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	492
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	16,0,2,35,2,0,12,4,140,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3365
	.byte	4,2,35,0,0,14,12
	.word	492
	.byte	15,11,0,10
	.byte	'_Ifx_P_PDISC_Bits',0,4,183,3,16,4,11
	.byte	'PDIS0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'PDIS2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'PDIS3',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'PDIS4',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'PDIS5',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'PDIS6',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'PDIS7',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'PDIS8',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'PDIS9',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'PDIS10',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'PDIS11',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'PDIS12',0,1
	.word	492
	.byte	1,3,2,35,1,11
	.byte	'PDIS13',0,1
	.word	492
	.byte	1,2,2,35,1,11
	.byte	'PDIS14',0,1
	.word	492
	.byte	1,1,2,35,1,11
	.byte	'PDIS15',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	16,0,2,35,2,0,12,4,197,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3705
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PCSR_Bits',0,4,165,3,16,4,11
	.byte	'SEL0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'SEL1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'SEL2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'SEL3',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'SEL4',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'SEL5',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'SEL6',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'SEL7',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	492
	.byte	2,6,2,35,1,11
	.byte	'SEL10',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'SEL11',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	469
	.byte	19,1,2,35,0,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,12,4,189,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4071
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR0_Bits',0,4,206,2,16,4,11
	.byte	'PS0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	469
	.byte	28,0,2,35,0,0,12,4,149,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4357
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR4_Bits',0,4,227,2,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	4,4,2,35,0,11
	.byte	'PS4',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	469
	.byte	24,0,2,35,0,0,12,4,165,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4504
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR8_Bits',0,4,238,2,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'PS8',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	469
	.byte	20,0,2,35,0,0,12,4,173,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4673
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR12_Bits',0,4,216,2,16,4,11
	.byte	'reserved_0',0,2
	.word	509
	.byte	12,4,2,35,0,11
	.byte	'PS12',0,1
	.word	492
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	492
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	492
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	16,0,2,35,2,0,12,4,157,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4845
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR0_Bits',0,4,232,1,16,4,11
	.byte	'reserved_0',0,2
	.word	509
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	492
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	492
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	509
	.byte	12,0,2,35,2,0,12,4,229,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5020
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR4_Bits',0,4,253,1,16,4,11
	.byte	'reserved_0',0,4
	.word	469
	.byte	20,12,2,35,0,11
	.byte	'PCL4',0,1
	.word	492
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	492
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	492
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	492
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	8,0,2,35,3,0,12,4,245,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5194
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR8_Bits',0,4,136,2,16,4,11
	.byte	'reserved_0',0,4
	.word	469
	.byte	24,8,2,35,0,11
	.byte	'PCL8',0,1
	.word	492
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	492
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	492
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	492
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	492
	.byte	4,0,2,35,3,0,12,4,253,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5368
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR12_Bits',0,4,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	469
	.byte	28,4,2,35,0,11
	.byte	'PCL12',0,1
	.word	492
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	492
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	492
	.byte	1,0,2,35,3,0,12,4,237,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5544
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR_Bits',0,4,249,2,16,4,11
	.byte	'PS0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	492
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	492
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	492
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	16,0,2,35,2,0,12,4,141,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5700
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR_Bits',0,4,147,2,16,4,11
	.byte	'reserved_0',0,2
	.word	509
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	492
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	492
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	492
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	492
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	492
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	492
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	492
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	492
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	492
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	492
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	492
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	492
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	492
	.byte	1,0,2,35,3,0,12,4,221,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6033
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR0_Bits',0,4,192,1,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	469
	.byte	30,0,2,35,0,0,12,4,196,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6381
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_Bits',0,4,200,1,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	469
	.byte	30,0,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_P21_Bits',0,4,208,1,16,4,11
	.byte	'RDIS_CTRL',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'RX_DIS',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'TERM',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'LRXTERM',0,1
	.word	492
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	469
	.byte	24,0,2,35,0,0,12,4,204,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6505
	.byte	4,2,35,0,13
	.byte	'B_P21',0
	.word	6589
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR2_Bits',0,4,218,1,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'LVDSR',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'LVDSRL',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	492
	.byte	2,4,2,35,1,11
	.byte	'TDIS_CTRL',0,1
	.word	492
	.byte	1,3,2,35,1,11
	.byte	'TX_DIS',0,1
	.word	492
	.byte	1,2,2,35,1,11
	.byte	'TX_PD',0,1
	.word	492
	.byte	1,1,2,35,1,11
	.byte	'TX_PWDPD',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	16,0,2,35,2,0,12,4,213,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6769
	.byte	4,2,35,0,0,14,76
	.word	492
	.byte	15,75,0,10
	.byte	'_Ifx_P_ACCEN1_Bits',0,4,82,16,4,11
	.byte	'reserved_0',0,4
	.word	469
	.byte	32,0,2,35,0,0,12,4,132,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7022
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ACCEN0_Bits',0,4,45,16,4,11
	.byte	'EN0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	492
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	492
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	492
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	492
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	492
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	492
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	492
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	492
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	492
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	492
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	492
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	492
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	492
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	492
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	492
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	492
	.byte	1,0,2,35,3,0,12,4,252,3,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7109
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P',0,4,229,5,25,128,2,13
	.byte	'OUT',0
	.word	807
	.byte	4,2,35,0,13
	.byte	'OMR',0
	.word	1378
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	1497
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	1537
	.byte	4,2,35,12,13
	.byte	'IOCR0',0
	.word	1721
	.byte	4,2,35,16,13
	.byte	'IOCR4',0
	.word	1936
	.byte	4,2,35,20,13
	.byte	'IOCR8',0
	.word	2153
	.byte	4,2,35,24,13
	.byte	'IOCR12',0
	.word	2373
	.byte	4,2,35,28,13
	.byte	'reserved_20',0
	.word	1537
	.byte	4,2,35,32,13
	.byte	'IN',0
	.word	2687
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	2727
	.byte	24,2,35,40,13
	.byte	'PDR0',0
	.word	3000
	.byte	4,2,35,64,13
	.byte	'PDR1',0
	.word	3316
	.byte	4,2,35,68,13
	.byte	'reserved_48',0
	.word	3356
	.byte	8,2,35,72,13
	.byte	'ESR',0
	.word	3656
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	3696
	.byte	12,2,35,84,13
	.byte	'PDISC',0
	.word	4031
	.byte	4,2,35,96,13
	.byte	'PCSR',0
	.word	4317
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	3356
	.byte	8,2,35,104,13
	.byte	'OMSR0',0
	.word	4464
	.byte	4,2,35,112,13
	.byte	'OMSR4',0
	.word	4633
	.byte	4,2,35,116,13
	.byte	'OMSR8',0
	.word	4805
	.byte	4,2,35,120,13
	.byte	'OMSR12',0
	.word	4980
	.byte	4,2,35,124,13
	.byte	'OMCR0',0
	.word	5154
	.byte	4,3,35,128,1,13
	.byte	'OMCR4',0
	.word	5328
	.byte	4,3,35,132,1,13
	.byte	'OMCR8',0
	.word	5504
	.byte	4,3,35,136,1,13
	.byte	'OMCR12',0
	.word	5660
	.byte	4,3,35,140,1,13
	.byte	'OMSR',0
	.word	5993
	.byte	4,3,35,144,1,13
	.byte	'OMCR',0
	.word	6341
	.byte	4,3,35,148,1,13
	.byte	'reserved_98',0
	.word	3356
	.byte	8,3,35,152,1,13
	.byte	'LPCR0',0
	.word	6465
	.byte	4,3,35,160,1,13
	.byte	'LPCR1',0
	.word	6714
	.byte	4,3,35,164,1,13
	.byte	'LPCR2',0
	.word	6973
	.byte	4,3,35,168,1,13
	.byte	'reserved_A4',0
	.word	7013
	.byte	76,3,35,172,1,13
	.byte	'ACCEN1',0
	.word	7069
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	7636
	.byte	4,3,35,252,1,0,16
	.word	7676
	.byte	3
	.word	8279
	.byte	17,3,172,1,9,4,18
	.byte	'IfxPort_State_notChanged',0,0,18
	.byte	'IfxPort_State_high',0,1,18
	.byte	'IfxPort_State_low',0,128,128,4,18
	.byte	'IfxPort_State_toggled',0,129,128,4,0,4
	.byte	'IfxPort_setPinState',0,3,3,208,4,17,1,1,5
	.byte	'port',0,3,208,4,44
	.word	8284
	.byte	5
	.byte	'pinIndex',0,3,208,4,56
	.word	492
	.byte	5
	.byte	'action',0,3,208,4,80
	.word	8289
	.byte	6,0,19
	.word	245
	.byte	20
	.word	271
	.byte	6,0,19
	.word	306
	.byte	20
	.word	338
	.byte	6,0,19
	.word	388
	.byte	20
	.word	407
	.byte	6,0,19
	.word	423
	.byte	20
	.word	438
	.byte	20
	.word	452
	.byte	6,0,19
	.word	8392
	.byte	20
	.word	8420
	.byte	20
	.word	8434
	.byte	20
	.word	8452
	.byte	6,0,7
	.byte	'short int',0,2,5,21
	.byte	'__wchar_t',0,5,1,1
	.word	8545
	.byte	21
	.byte	'__size_t',0,5,1,1
	.word	469
	.byte	21
	.byte	'__ptrdiff_t',0,5,1,1
	.word	485
	.byte	22,1,3
	.word	8613
	.byte	21
	.byte	'__codeptr',0,5,1,1
	.word	8615
	.byte	7
	.byte	'unsigned int',0,4,7,10
	.byte	'_Ifx_CIF_BBB_ACCEN0_Bits',0,6,45,16,4,11
	.byte	'EN0',0,4
	.word	8638
	.byte	1,31,2,35,0,11
	.byte	'EN1',0,4
	.word	8638
	.byte	1,30,2,35,0,11
	.byte	'EN2',0,4
	.word	8638
	.byte	1,29,2,35,0,11
	.byte	'EN3',0,4
	.word	8638
	.byte	1,28,2,35,0,11
	.byte	'EN4',0,4
	.word	8638
	.byte	1,27,2,35,0,11
	.byte	'EN5',0,4
	.word	8638
	.byte	1,26,2,35,0,11
	.byte	'EN6',0,4
	.word	8638
	.byte	1,25,2,35,0,11
	.byte	'EN7',0,4
	.word	8638
	.byte	1,24,2,35,0,11
	.byte	'EN8',0,4
	.word	8638
	.byte	1,23,2,35,0,11
	.byte	'EN9',0,4
	.word	8638
	.byte	1,22,2,35,0,11
	.byte	'EN10',0,4
	.word	8638
	.byte	1,21,2,35,0,11
	.byte	'EN11',0,4
	.word	8638
	.byte	1,20,2,35,0,11
	.byte	'EN12',0,4
	.word	8638
	.byte	1,19,2,35,0,11
	.byte	'EN13',0,4
	.word	8638
	.byte	1,18,2,35,0,11
	.byte	'EN14',0,4
	.word	8638
	.byte	1,17,2,35,0,11
	.byte	'EN15',0,4
	.word	8638
	.byte	1,16,2,35,0,11
	.byte	'EN16',0,4
	.word	8638
	.byte	1,15,2,35,0,11
	.byte	'EN17',0,4
	.word	8638
	.byte	1,14,2,35,0,11
	.byte	'EN18',0,4
	.word	8638
	.byte	1,13,2,35,0,11
	.byte	'EN19',0,4
	.word	8638
	.byte	1,12,2,35,0,11
	.byte	'EN20',0,4
	.word	8638
	.byte	1,11,2,35,0,11
	.byte	'EN21',0,4
	.word	8638
	.byte	1,10,2,35,0,11
	.byte	'EN22',0,4
	.word	8638
	.byte	1,9,2,35,0,11
	.byte	'EN23',0,4
	.word	8638
	.byte	1,8,2,35,0,11
	.byte	'EN24',0,4
	.word	8638
	.byte	1,7,2,35,0,11
	.byte	'EN25',0,4
	.word	8638
	.byte	1,6,2,35,0,11
	.byte	'EN26',0,4
	.word	8638
	.byte	1,5,2,35,0,11
	.byte	'EN27',0,4
	.word	8638
	.byte	1,4,2,35,0,11
	.byte	'EN28',0,4
	.word	8638
	.byte	1,3,2,35,0,11
	.byte	'EN29',0,4
	.word	8638
	.byte	1,2,2,35,0,11
	.byte	'EN30',0,4
	.word	8638
	.byte	1,1,2,35,0,11
	.byte	'EN31',0,4
	.word	8638
	.byte	1,0,2,35,0,0,21
	.byte	'Ifx_CIF_BBB_ACCEN0_Bits',0,6,79,3
	.word	8654
	.byte	10
	.byte	'_Ifx_CIF_BBB_ACCEN1_Bits',0,6,82,16,4,11
	.byte	'reserved_0',0,4
	.word	8638
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_CIF_BBB_ACCEN1_Bits',0,6,85,3
	.word	9219
	.byte	10
	.byte	'_Ifx_CIF_BBB_CLC_Bits',0,6,88,16,4,11
	.byte	'DISR',0,4
	.word	8638
	.byte	1,31,2,35,0,11
	.byte	'DISS',0,4
	.word	8638
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	8638
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_CIF_BBB_CLC_Bits',0,6,93,3
	.word	9304
	.byte	10
	.byte	'_Ifx_CIF_BBB_GPCTL_Bits',0,6,96,16,4,11
	.byte	'PISEL',0,4
	.word	8638
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	8638
	.byte	31,0,2,35,0,0,21
	.byte	'Ifx_CIF_BBB_GPCTL_Bits',0,6,100,3
	.word	9415
	.byte	10
	.byte	'_Ifx_CIF_BBB_KRST0_Bits',0,6,103,16,4,11
	.byte	'RST',0,4
	.word	8638
	.byte	1,31,2,35,0,11
	.byte	'RSTSTAT',0,4
	.word	8638
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	8638
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_CIF_BBB_KRST0_Bits',0,6,108,3
	.word	9515
	.byte	10
	.byte	'_Ifx_CIF_BBB_KRST1_Bits',0,6,111,16,4,11
	.byte	'RST',0,4
	.word	8638
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	8638
	.byte	31,0,2,35,0,0,21
	.byte	'Ifx_CIF_BBB_KRST1_Bits',0,6,115,3
	.word	9632
	.byte	10
	.byte	'_Ifx_CIF_BBB_KRSTCLR_Bits',0,6,118,16,4,11
	.byte	'CLR',0,4
	.word	8638
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	8638
	.byte	31,0,2,35,0,0,21
	.byte	'Ifx_CIF_BBB_KRSTCLR_Bits',0,6,122,3
	.word	9730
	.byte	10
	.byte	'_Ifx_CIF_BBB_MODID_Bits',0,6,125,16,4,11
	.byte	'MODREV',0,4
	.word	8638
	.byte	8,24,2,35,0,11
	.byte	'MODTYPE',0,4
	.word	8638
	.byte	8,16,2,35,0,11
	.byte	'MODNUMBER',0,4
	.word	8638
	.byte	16,0,2,35,0,0,21
	.byte	'Ifx_CIF_BBB_MODID_Bits',0,6,130,1,3
	.word	9832
	.byte	10
	.byte	'_Ifx_CIF_CCL_Bits',0,6,133,1,16,4,11
	.byte	'reserved_0',0,4
	.word	8638
	.byte	1,31,2,35,0,11
	.byte	'CIF_CCLDISS',0,4
	.word	8638
	.byte	1,30,2,35,0,11
	.byte	'CIF_CCLFDIS',0,4
	.word	8638
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	8638
	.byte	29,0,2,35,0,0,21
	.byte	'Ifx_CIF_CCL_Bits',0,6,139,1,3
	.word	9952
	.byte	10
	.byte	'_Ifx_CIF_DP_CTRL_Bits',0,6,142,1,16,4,11
	.byte	'DP_EN',0,4
	.word	8638
	.byte	1,31,2,35,0,11
	.byte	'DP_SEL',0,4
	.word	8638
	.byte	3,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	8638
	.byte	4,24,2,35,0,11
	.byte	'RST_FNC',0,4
	.word	8638
	.byte	1,23,2,35,0,11
	.byte	'RST_LNC',0,4
	.word	8638
	.byte	1,22,2,35,0,11
	.byte	'RST_TSC',0,4
	.word	8638
	.byte	1,21,2,35,0,11
	.byte	'RST_PD',0,4
	.word	8638
	.byte	1,20,2,35,0,11
	.byte	'reserved_12',0,4
	.word	8638
	.byte	1,19,2,35,0,11
	.byte	'FNC_EN',0,4
	.word	8638
	.byte	1,18,2,35,0,11
	.byte	'LNC_EN',0,4
	.word	8638
	.byte	1,17,2,35,0,11
	.byte	'TSC_EN',0,4
	.word	8638
	.byte	1,16,2,35,0,11
	.byte	'UDS1',0,4
	.word	8638
	.byte	1,15,2,35,0,11
	.byte	'UDS2',0,4
	.word	8638
	.byte	1,14,2,35,0,11
	.byte	'UDS3',0,4
	.word	8638
	.byte	1,13,2,35,0,11
	.byte	'UDS4',0,4
	.word	8638
	.byte	1,12,2,35,0,11
	.byte	'UDS5',0,4
	.word	8638
	.byte	1,11,2,35,0,11
	.byte	'UDS6',0,4
	.word	8638
	.byte	1,10,2,35,0,11
	.byte	'UDS7',0,4
	.word	8638
	.byte	1,9,2,35,0,11
	.byte	'UDS8',0,4
	.word	8638
	.byte	1,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8638
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_CIF_DP_CTRL_Bits',0,6,164,1,3
	.word	10093
	.byte	10
	.byte	'_Ifx_CIF_DP_FLC_STAT_Bits',0,6,167,1,16,4,11
	.byte	'FNC_VAL',0,4
	.word	8638
	.byte	15,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	8638
	.byte	1,16,2,35,0,11
	.byte	'LNC_VAL',0,4
	.word	8638
	.byte	15,1,2,35,0,11
	.byte	'reserved_31',0,4
	.word	8638
	.byte	1,0,2,35,0,0,21
	.byte	'Ifx_CIF_DP_FLC_STAT_Bits',0,6,173,1,3
	.word	10512
	.byte	10
	.byte	'_Ifx_CIF_DP_PDIV_CTRL_Bits',0,6,176,1,16,4,11
	.byte	'PDIV_VAL',0,4
	.word	8638
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_CIF_DP_PDIV_CTRL_Bits',0,6,179,1,3
	.word	10663
	.byte	10
	.byte	'_Ifx_CIF_DP_PDIV_STAT_Bits',0,6,182,1,16,4,11
	.byte	'PDIV_VAL',0,4
	.word	8638
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_CIF_DP_PDIV_STAT_Bits',0,6,185,1,3
	.word	10752
	.byte	10
	.byte	'_Ifx_CIF_DP_TSC_STAT_Bits',0,6,188,1,16,4,11
	.byte	'TSC_VAL',0,4
	.word	8638
	.byte	30,2,2,35,0,11
	.byte	'reserved_30',0,4
	.word	8638
	.byte	2,0,2,35,0,0,21
	.byte	'Ifx_CIF_DP_TSC_STAT_Bits',0,6,192,1,3
	.word	10841
	.byte	10
	.byte	'_Ifx_CIF_DP_UDS_Bits',0,6,195,1,16,4,11
	.byte	'UDS',0,4
	.word	8638
	.byte	15,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	8638
	.byte	17,0,2,35,0,0,21
	.byte	'Ifx_CIF_DP_UDS_Bits',0,6,199,1,3
	.word	10950
	.byte	10
	.byte	'_Ifx_CIF_DPCL_Bits',0,6,202,1,16,4,11
	.byte	'CIF_MP_MUX',0,4
	.word	8638
	.byte	2,30,2,35,0,11
	.byte	'CIF_CHAN_MODE',0,4
	.word	8638
	.byte	2,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	8638
	.byte	4,24,2,35,0,11
	.byte	'IF_SELECT',0,4
	.word	8638
	.byte	2,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	8638
	.byte	22,0,2,35,0,0,21
	.byte	'Ifx_CIF_DPCL_Bits',0,6,209,1,3
	.word	11045
	.byte	10
	.byte	'_Ifx_CIF_EP_IC_CTRL_Bits',0,6,212,1,16,4,11
	.byte	'IC_EN',0,4
	.word	8638
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	8638
	.byte	31,0,2,35,0,0,21
	.byte	'Ifx_CIF_EP_IC_CTRL_Bits',0,6,216,1,3
	.word	11211
	.byte	10
	.byte	'_Ifx_CIF_EP_IC_DISPLACE_Bits',0,6,219,1,16,4,11
	.byte	'DX',0,4
	.word	8638
	.byte	12,20,2,35,0,11
	.byte	'reserved_12',0,4
	.word	8638
	.byte	4,16,2,35,0,11
	.byte	'DY',0,4
	.word	8638
	.byte	12,4,2,35,0,11
	.byte	'reserved_28',0,4
	.word	8638
	.byte	4,0,2,35,0,0,21
	.byte	'Ifx_CIF_EP_IC_DISPLACE_Bits',0,6,225,1,3
	.word	11315
	.byte	10
	.byte	'_Ifx_CIF_EP_IC_H_OFFS_Bits',0,6,229,1,16,4,11
	.byte	'H_OFFS',0,4
	.word	8638
	.byte	12,20,2,35,0,11
	.byte	'reserved_12',0,4
	.word	8638
	.byte	20,0,2,35,0,0,21
	.byte	'Ifx_CIF_EP_IC_H_OFFS_Bits',0,6,233,1,3
	.word	11462
	.byte	10
	.byte	'_Ifx_CIF_EP_IC_H_OFFS_SHD_Bits',0,6,237,1,16,4,11
	.byte	'H_OFFS_SHD',0,4
	.word	8638
	.byte	13,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	8638
	.byte	19,0,2,35,0,0,21
	.byte	'Ifx_CIF_EP_IC_H_OFFS_SHD_Bits',0,6,241,1,3
	.word	11572
	.byte	10
	.byte	'_Ifx_CIF_EP_IC_H_SIZE_Bits',0,6,244,1,16,4,11
	.byte	'H_SIZE',0,4
	.word	8638
	.byte	12,20,2,35,0,11
	.byte	'reserved_12',0,4
	.word	8638
	.byte	20,0,2,35,0,0,21
	.byte	'Ifx_CIF_EP_IC_H_SIZE_Bits',0,6,248,1,3
	.word	11694
	.byte	10
	.byte	'_Ifx_CIF_EP_IC_H_SIZE_SHD_Bits',0,6,252,1,16,4,11
	.byte	'H_SIZE_SHD',0,4
	.word	8638
	.byte	13,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	8638
	.byte	19,0,2,35,0,0,21
	.byte	'Ifx_CIF_EP_IC_H_SIZE_SHD_Bits',0,6,128,2,3
	.word	11804
	.byte	10
	.byte	'_Ifx_CIF_EP_IC_MAX_DX_Bits',0,6,131,2,16,4,11
	.byte	'MAX_DX',0,4
	.word	8638
	.byte	12,20,2,35,0,11
	.byte	'reserved_12',0,4
	.word	8638
	.byte	20,0,2,35,0,0,21
	.byte	'Ifx_CIF_EP_IC_MAX_DX_Bits',0,6,135,2,3
	.word	11926
	.byte	10
	.byte	'_Ifx_CIF_EP_IC_MAX_DY_Bits',0,6,138,2,16,4,11
	.byte	'MAX_DY',0,4
	.word	8638
	.byte	12,20,2,35,0,11
	.byte	'reserved_12',0,4
	.word	8638
	.byte	20,0,2,35,0,0,21
	.byte	'Ifx_CIF_EP_IC_MAX_DY_Bits',0,6,142,2,3
	.word	12036
	.byte	10
	.byte	'_Ifx_CIF_EP_IC_RECENTER_Bits',0,6,145,2,16,4,11
	.byte	'RECENTER',0,4
	.word	8638
	.byte	3,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	8638
	.byte	29,0,2,35,0,0,21
	.byte	'Ifx_CIF_EP_IC_RECENTER_Bits',0,6,149,2,3
	.word	12146
	.byte	10
	.byte	'_Ifx_CIF_EP_IC_V_OFFS_Bits',0,6,152,2,16,4,11
	.byte	'V_OFFS',0,4
	.word	8638
	.byte	12,20,2,35,0,11
	.byte	'reserved_12',0,4
	.word	8638
	.byte	20,0,2,35,0,0,21
	.byte	'Ifx_CIF_EP_IC_V_OFFS_Bits',0,6,156,2,3
	.word	12261
	.byte	10
	.byte	'_Ifx_CIF_EP_IC_V_OFFS_SHD_Bits',0,6,160,2,16,4,11
	.byte	'V_OFFS_SHD',0,4
	.word	8638
	.byte	12,20,2,35,0,11
	.byte	'reserved_12',0,4
	.word	8638
	.byte	20,0,2,35,0,0,21
	.byte	'Ifx_CIF_EP_IC_V_OFFS_SHD_Bits',0,6,164,2,3
	.word	12371
	.byte	10
	.byte	'_Ifx_CIF_EP_IC_V_SIZE_Bits',0,6,167,2,16,4,11
	.byte	'V_SIZE',0,4
	.word	8638
	.byte	12,20,2,35,0,11
	.byte	'reserved_12',0,4
	.word	8638
	.byte	20,0,2,35,0,0,21
	.byte	'Ifx_CIF_EP_IC_V_SIZE_Bits',0,6,171,2,3
	.word	12493
	.byte	10
	.byte	'_Ifx_CIF_EP_IC_V_SIZE_SHD_Bits',0,6,175,2,16,4,11
	.byte	'V_SIZE_SHD',0,4
	.word	8638
	.byte	12,20,2,35,0,11
	.byte	'reserved_12',0,4
	.word	8638
	.byte	20,0,2,35,0,0,21
	.byte	'Ifx_CIF_EP_IC_V_SIZE_SHD_Bits',0,6,179,2,3
	.word	12603
	.byte	10
	.byte	'_Ifx_CIF_ICCL_Bits',0,6,182,2,16,4,11
	.byte	'CIF_ISP_CLK_ENABLE',0,4
	.word	8638
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	8638
	.byte	4,27,2,35,0,11
	.byte	'CIF_JPEG_CLK_ENABLE',0,4
	.word	8638
	.byte	1,26,2,35,0,11
	.byte	'CIF_MI_CLK_ENABLE',0,4
	.word	8638
	.byte	1,25,2,35,0,11
	.byte	'reserved_7',0,4
	.word	8638
	.byte	9,16,2,35,0,11
	.byte	'CIF_WATCHDOG_CLK_ENABLE',0,4
	.word	8638
	.byte	1,15,2,35,0,11
	.byte	'CIF_LIN_DSCALER_CLK_ENABLE',0,4
	.word	8638
	.byte	1,14,2,35,0,11
	.byte	'CIF_EXTRA_PATHS_CLK_ENABLE',0,4
	.word	8638
	.byte	1,13,2,35,0,11
	.byte	'CIF_DEBUG_PATH_CLK_EN',0,4
	.word	8638
	.byte	1,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	8638
	.byte	12,0,2,35,0,0,21
	.byte	'Ifx_CIF_ICCL_Bits',0,6,194,2,3
	.word	12725
	.byte	10
	.byte	'_Ifx_CIF_ID_Bits',0,6,197,2,16,4,11
	.byte	'MODREV',0,4
	.word	8638
	.byte	8,24,2,35,0,11
	.byte	'MODTYPE',0,4
	.word	8638
	.byte	8,16,2,35,0,11
	.byte	'MODNUMBER',0,4
	.word	8638
	.byte	16,0,2,35,0,0,21
	.byte	'Ifx_CIF_ID_Bits',0,6,202,2,3
	.word	13079
	.byte	10
	.byte	'_Ifx_CIF_IRCL_Bits',0,6,205,2,16,4,11
	.byte	'CIF_ISP_SOFT_RST',0,4
	.word	8638
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	8638
	.byte	1,30,2,35,0,11
	.byte	'CIF_YCS_SOFT_RST',0,4
	.word	8638
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	8638
	.byte	2,27,2,35,0,11
	.byte	'CIF_JPEG_SOFT_RST',0,4
	.word	8638
	.byte	1,26,2,35,0,11
	.byte	'CIF_MI_SOFT_RST',0,4
	.word	8638
	.byte	1,25,2,35,0,11
	.byte	'CIF_GLOBAL_RST',0,4
	.word	8638
	.byte	1,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	8638
	.byte	8,16,2,35,0,11
	.byte	'CIF_WATCHDOG_RST',0,4
	.word	8638
	.byte	1,15,2,35,0,11
	.byte	'CIF_LIN_DSCALER_RST',0,4
	.word	8638
	.byte	1,14,2,35,0,11
	.byte	'CIF_EXTRA_PATHS_RST',0,4
	.word	8638
	.byte	1,13,2,35,0,11
	.byte	'CIF_DEBUG_PATH_RST',0,4
	.word	8638
	.byte	1,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	8638
	.byte	12,0,2,35,0,0,21
	.byte	'Ifx_CIF_IRCL_Bits',0,6,220,2,3
	.word	13186
	.byte	10
	.byte	'_Ifx_CIF_ISP_ACQ_H_OFFS_Bits',0,6,223,2,16,4,11
	.byte	'ACQ_H_OFFS',0,4
	.word	8638
	.byte	13,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	8638
	.byte	19,0,2,35,0,0,21
	.byte	'Ifx_CIF_ISP_ACQ_H_OFFS_Bits',0,6,227,2,3
	.word	13586
	.byte	10
	.byte	'_Ifx_CIF_ISP_ACQ_H_SIZE_Bits',0,6,230,2,16,4,11
	.byte	'ACQ_H_SIZE',0,4
	.word	8638
	.byte	13,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	8638
	.byte	19,0,2,35,0,0,21
	.byte	'Ifx_CIF_ISP_ACQ_H_SIZE_Bits',0,6,234,2,3
	.word	13704
	.byte	10
	.byte	'_Ifx_CIF_ISP_ACQ_NR_FRAMES_Bits',0,6,237,2,16,4,11
	.byte	'ACQ_NR_FRAMES',0,4
	.word	8638
	.byte	10,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	8638
	.byte	22,0,2,35,0,0,21
	.byte	'Ifx_CIF_ISP_ACQ_NR_FRAMES_Bits',0,6,241,2,3
	.word	13822
	.byte	10
	.byte	'_Ifx_CIF_ISP_ACQ_PROP_Bits',0,6,244,2,16,4,11
	.byte	'SAMPLE_EDGE',0,4
	.word	8638
	.byte	1,31,2,35,0,11
	.byte	'HSYNC_POL',0,4
	.word	8638
	.byte	1,30,2,35,0,11
	.byte	'VSYNC_POL',0,4
	.word	8638
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	8638
	.byte	4,25,2,35,0,11
	.byte	'CCIR_SEQ',0,4
	.word	8638
	.byte	2,23,2,35,0,11
	.byte	'FIELD_SELECTION',0,4
	.word	8638
	.byte	2,21,2,35,0,11
	.byte	'FIELD_INVERT',0,4
	.word	8638
	.byte	1,20,2,35,0,11
	.byte	'INPUT_SELECTION',0,4
	.word	8638
	.byte	4,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	8638
	.byte	4,12,2,35,0,11
	.byte	'INPUT_SELECTION_NO_APP',0,4
	.word	8638
	.byte	1,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	8638
	.byte	11,0,2,35,0,0,21
	.byte	'Ifx_CIF_ISP_ACQ_PROP_Bits',0,6,129,3,3
	.word	13949
	.byte	10
	.byte	'_Ifx_CIF_ISP_ACQ_V_OFFS_Bits',0,6,132,3,16,4,11
	.byte	'ACQ_V_OFFS',0,4
	.word	8638
	.byte	12,20,2,35,0,11
	.byte	'reserved_12',0,4
	.word	8638
	.byte	20,0,2,35,0,0,21
	.byte	'Ifx_CIF_ISP_ACQ_V_OFFS_Bits',0,6,136,3,3
	.word	14283
	.byte	10
	.byte	'_Ifx_CIF_ISP_ACQ_V_SIZE_Bits',0,6,139,3,16,4,11
	.byte	'ACQ_V_SIZE',0,4
	.word	8638
	.byte	12,20,2,35,0,11
	.byte	'reserved_12',0,4
	.word	8638
	.byte	20,0,2,35,0,0,21
	.byte	'Ifx_CIF_ISP_ACQ_V_SIZE_Bits',0,6,143,3,3
	.word	14401
	.byte	10
	.byte	'_Ifx_CIF_ISP_CTRL_Bits',0,6,146,3,16,4,11
	.byte	'ISP_ENABLE',0,4
	.word	8638
	.byte	1,31,2,35,0,11
	.byte	'ISP_MODE',0,4
	.word	8638
	.byte	3,28,2,35,0,11
	.byte	'ISP_INFORM_ENABLE',0,4
	.word	8638
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	8638
	.byte	4,23,2,35,0,11
	.byte	'ISP_CFG_UPD',0,4
	.word	8638
	.byte	1,22,2,35,0,11
	.byte	'ISP_GEN_CFG_UPD',0,4
	.word	8638
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	8638
	.byte	2,19,2,35,0,11
	.byte	'ISP_CSM_Y_RANGE',0,4
	.word	8638
	.byte	1,18,2,35,0,11
	.byte	'ISP_CSM_C_RANGE',0,4
	.word	8638
	.byte	1,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	8638
	.byte	17,0,2,35,0,0,21
	.byte	'Ifx_CIF_ISP_CTRL_Bits',0,6,158,3,3
	.word	14519
	.byte	10
	.byte	'_Ifx_CIF_ISP_ERR_Bits',0,6,161,3,16,4,11
	.byte	'INFORM_SIZE_ERR',0,4
	.word	8638
	.byte	1,31,2,35,0,11
	.byte	'IS_SIZE_ERR',0,4
	.word	8638
	.byte	1,30,2,35,0,11
	.byte	'OUTFORM_SIZE_ERR',0,4
	.word	8638
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	8638
	.byte	29,0,2,35,0,0,21
	.byte	'Ifx_CIF_ISP_ERR_Bits',0,6,167,3,3
	.word	14823
	.byte	10
	.byte	'_Ifx_CIF_ISP_ERR_CLR_Bits',0,6,170,3,16,4,11
	.byte	'INFORM_SIZE_ERR_CLR',0,4
	.word	8638
	.byte	1,31,2,35,0,11
	.byte	'IS_SIZE_ERR_CLR',0,4
	.word	8638
	.byte	1,30,2,35,0,11
	.byte	'OUTFORM_SIZE_ERR_CLR',0,4
	.word	8638
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	8638
	.byte	29,0,2,35,0,0,21
	.byte	'Ifx_CIF_ISP_ERR_CLR_Bits',0,6,176,3,3
	.word	14982
	.byte	10
	.byte	'_Ifx_CIF_ISP_FLAGS_SHD_Bits',0,6,179,3,16,4,11
	.byte	'ISP_ENABLE_SHD',0,4
	.word	8638
	.byte	1,31,2,35,0,11
	.byte	'ISP_INFORM_ENABLE_SHD',0,4
	.word	8638
	.byte	1,30,2,35,0,11
	.byte	'INFORM_FIELD',0,4
	.word	8638
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	8638
	.byte	11,18,2,35,0,11
	.byte	'S_DATA',0,4
	.word	8638
	.byte	16,2,2,35,0,11
	.byte	'S_VSYNC',0,4
	.word	8638
	.byte	1,1,2,35,0,11
	.byte	'S_HSYNC',0,4
	.word	8638
	.byte	1,0,2,35,0,0,21
	.byte	'Ifx_CIF_ISP_FLAGS_SHD_Bits',0,6,188,3,3
	.word	15161
	.byte	10
	.byte	'_Ifx_CIF_ISP_FRAME_COUNT_Bits',0,6,191,3,16,4,11
	.byte	'FRAME_COUNTER',0,4
	.word	8638
	.byte	10,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	8638
	.byte	22,0,2,35,0,0,21
	.byte	'Ifx_CIF_ISP_FRAME_COUNT_Bits',0,6,195,3,3
	.word	15393
	.byte	10
	.byte	'_Ifx_CIF_ISP_ICR_Bits',0,6,198,3,16,4,11
	.byte	'ICR_ISP_OFF',0,4
	.word	8638
	.byte	1,31,2,35,0,11
	.byte	'ICR_FRAME',0,4
	.word	8638
	.byte	1,30,2,35,0,11
	.byte	'ICR_DATA_LOSS',0,4
	.word	8638
	.byte	1,29,2,35,0,11
	.byte	'ICR_PIC_SIZE_ERR',0,4
	.word	8638
	.byte	1,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	8638
	.byte	1,27,2,35,0,11
	.byte	'ICR_FRAME_IN',0,4
	.word	8638
	.byte	1,26,2,35,0,11
	.byte	'ICR_V_START',0,4
	.word	8638
	.byte	1,25,2,35,0,11
	.byte	'ICR_H_START',0,4
	.word	8638
	.byte	1,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	8638
	.byte	11,13,2,35,0,11
	.byte	'ICR_WD_TRIG',0,4
	.word	8638
	.byte	1,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	8638
	.byte	12,0,2,35,0,0,21
	.byte	'Ifx_CIF_ISP_ICR_Bits',0,6,211,3,3
	.word	15516
	.byte	10
	.byte	'_Ifx_CIF_ISP_IMSC_Bits',0,6,214,3,16,4,11
	.byte	'IMSC_ISP_OFF',0,4
	.word	8638
	.byte	1,31,2,35,0,11
	.byte	'IMSC_FRAME',0,4
	.word	8638
	.byte	1,30,2,35,0,11
	.byte	'IMSC_DATA_LOSS',0,4
	.word	8638
	.byte	1,29,2,35,0,11
	.byte	'IMSC_PIC_SIZE_ERR',0,4
	.word	8638
	.byte	1,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	8638
	.byte	1,27,2,35,0,11
	.byte	'IMSC_FRAME_IN',0,4
	.word	8638
	.byte	1,26,2,35,0,11
	.byte	'IMSC_V_START',0,4
	.word	8638
	.byte	1,25,2,35,0,11
	.byte	'IMSC_H_START',0,4
	.word	8638
	.byte	1,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	8638
	.byte	11,13,2,35,0,11
	.byte	'IMSC_WD_TRIG',0,4
	.word	8638
	.byte	1,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	8638
	.byte	12,0,2,35,0,0,21
	.byte	'Ifx_CIF_ISP_IMSC_Bits',0,6,227,3,3
	.word	15832
	.byte	10
	.byte	'_Ifx_CIF_ISP_ISR_Bits',0,6,230,3,16,4,11
	.byte	'ISR_ISP_OFF',0,4
	.word	8638
	.byte	1,31,2,35,0,11
	.byte	'ISR_FRAME',0,4
	.word	8638
	.byte	1,30,2,35,0,11
	.byte	'ISR_DATA_LOSS',0,4
	.word	8638
	.byte	1,29,2,35,0,11
	.byte	'ISR_PIC_SIZE_ERR',0,4
	.word	8638
	.byte	1,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	8638
	.byte	1,27,2,35,0,11
	.byte	'ISR_FRAME_IN',0,4
	.word	8638
	.byte	1,26,2,35,0,11
	.byte	'ISR_V_START',0,4
	.word	8638
	.byte	1,25,2,35,0,11
	.byte	'ISR_H_START',0,4
	.word	8638
	.byte	1,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	8638
	.byte	11,13,2,35,0,11
	.byte	'ISR_WD_TRIG',0,4
	.word	8638
	.byte	1,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	8638
	.byte	12,0,2,35,0,0,21
	.byte	'Ifx_CIF_ISP_ISR_Bits',0,6,243,3,3
	.word	16158
	.byte	10
	.byte	'_Ifx_CIF_ISP_MIS_Bits',0,6,246,3,16,4,11
	.byte	'MIS_ISP_OFF',0,4
	.word	8638
	.byte	1,31,2,35,0,11
	.byte	'MIS_FRAME',0,4
	.word	8638
	.byte	1,30,2,35,0,11
	.byte	'MIS_DATA_LOSS',0,4
	.word	8638
	.byte	1,29,2,35,0,11
	.byte	'MIS_PIC_SIZE_ERR',0,4
	.word	8638
	.byte	1,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	8638
	.byte	1,27,2,35,0,11
	.byte	'MIS_FRAME_IN',0,4
	.word	8638
	.byte	1,26,2,35,0,11
	.byte	'MIS_V_START',0,4
	.word	8638
	.byte	1,25,2,35,0,11
	.byte	'MIS_H_START',0,4
	.word	8638
	.byte	1,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	8638
	.byte	11,13,2,35,0,11
	.byte	'MIS_WD_TRIG',0,4
	.word	8638
	.byte	1,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	8638
	.byte	12,0,2,35,0,0,21
	.byte	'Ifx_CIF_ISP_MIS_Bits',0,6,131,4,3
	.word	16474
	.byte	10
	.byte	'_Ifx_CIF_ISP_OUT_H_OFFS_Bits',0,6,134,4,16,4,11
	.byte	'ISP_OUT_H_OFFS',0,4
	.word	8638
	.byte	12,20,2,35,0,11
	.byte	'reserved_12',0,4
	.word	8638
	.byte	20,0,2,35,0,0,21
	.byte	'Ifx_CIF_ISP_OUT_H_OFFS_Bits',0,6,138,4,3
	.word	16790
	.byte	10
	.byte	'_Ifx_CIF_ISP_OUT_H_OFFS_SHD_Bits',0,6,141,4,16,4,11
	.byte	'ISP_OUT_H_OFFS_SHD',0,4
	.word	8638
	.byte	12,20,2,35,0,11
	.byte	'reserved_12',0,4
	.word	8638
	.byte	20,0,2,35,0,0,21
	.byte	'Ifx_CIF_ISP_OUT_H_OFFS_SHD_Bits',0,6,145,4,3
	.word	16912
	.byte	10
	.byte	'_Ifx_CIF_ISP_OUT_H_SIZE_Bits',0,6,148,4,16,4,11
	.byte	'ISP_OUT_H_SIZE',0,4
	.word	8638
	.byte	12,20,2,35,0,11
	.byte	'reserved_12',0,4
	.word	8638
	.byte	20,0,2,35,0,0,21
	.byte	'Ifx_CIF_ISP_OUT_H_SIZE_Bits',0,6,152,4,3
	.word	17046
	.byte	10
	.byte	'_Ifx_CIF_ISP_OUT_H_SIZE_SHD_Bits',0,6,155,4,16,4,11
	.byte	'ISP_OUT_H_SIZE_SHD',0,4
	.word	8638
	.byte	13,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	8638
	.byte	19,0,2,35,0,0,21
	.byte	'Ifx_CIF_ISP_OUT_H_SIZE_SHD_Bits',0,6,159,4,3
	.word	17168
	.byte	10
	.byte	'_Ifx_CIF_ISP_OUT_V_OFFS_Bits',0,6,162,4,16,4,11
	.byte	'ISP_OUT_V_OFFS',0,4
	.word	8638
	.byte	12,20,2,35,0,11
	.byte	'reserved_12',0,4
	.word	8638
	.byte	20,0,2,35,0,0,21
	.byte	'Ifx_CIF_ISP_OUT_V_OFFS_Bits',0,6,166,4,3
	.word	17302
	.byte	10
	.byte	'_Ifx_CIF_ISP_OUT_V_OFFS_SHD_Bits',0,6,169,4,16,4,11
	.byte	'ISP_OUT_V_OFFS_SHD',0,4
	.word	8638
	.byte	12,20,2,35,0,11
	.byte	'reserved_12',0,4
	.word	8638
	.byte	20,0,2,35,0,0,21
	.byte	'Ifx_CIF_ISP_OUT_V_OFFS_SHD_Bits',0,6,173,4,3
	.word	17424
	.byte	10
	.byte	'_Ifx_CIF_ISP_OUT_V_SIZE_Bits',0,6,176,4,16,4,11
	.byte	'ISP_OUT_V_SIZE',0,4
	.word	8638
	.byte	12,20,2,35,0,11
	.byte	'reserved_12',0,4
	.word	8638
	.byte	20,0,2,35,0,0,21
	.byte	'Ifx_CIF_ISP_OUT_V_SIZE_Bits',0,6,180,4,3
	.word	17558
	.byte	10
	.byte	'_Ifx_CIF_ISP_OUT_V_SIZE_SHD_Bits',0,6,183,4,16,4,11
	.byte	'ISP_OUT_V_SIZE_SHD',0,4
	.word	8638
	.byte	12,20,2,35,0,11
	.byte	'reserved_12',0,4
	.word	8638
	.byte	20,0,2,35,0,0,21
	.byte	'Ifx_CIF_ISP_OUT_V_SIZE_SHD_Bits',0,6,187,4,3
	.word	17680
	.byte	10
	.byte	'_Ifx_CIF_ISP_RIS_Bits',0,6,190,4,16,4,11
	.byte	'RIS_ISP_OFF',0,4
	.word	8638
	.byte	1,31,2,35,0,11
	.byte	'RIS_FRAME',0,4
	.word	8638
	.byte	1,30,2,35,0,11
	.byte	'RIS_DATA_LOSS',0,4
	.word	8638
	.byte	1,29,2,35,0,11
	.byte	'RIS_PIC_SIZE_ERR',0,4
	.word	8638
	.byte	1,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	8638
	.byte	1,27,2,35,0,11
	.byte	'RIS_FRAME_IN',0,4
	.word	8638
	.byte	1,26,2,35,0,11
	.byte	'RIS_V_START',0,4
	.word	8638
	.byte	1,25,2,35,0,11
	.byte	'RIS_H_START',0,4
	.word	8638
	.byte	1,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	8638
	.byte	11,13,2,35,0,11
	.byte	'RIS_WD_TRIG',0,4
	.word	8638
	.byte	1,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	8638
	.byte	12,0,2,35,0,0,21
	.byte	'Ifx_CIF_ISP_RIS_Bits',0,6,203,4,3
	.word	17814
	.byte	10
	.byte	'_Ifx_CIF_ISPIS_CTRL_Bits',0,6,206,4,16,4,11
	.byte	'IS_EN',0,4
	.word	8638
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	8638
	.byte	31,0,2,35,0,0,21
	.byte	'Ifx_CIF_ISPIS_CTRL_Bits',0,6,210,4,3
	.word	18130
	.byte	10
	.byte	'_Ifx_CIF_ISPIS_DISPLACE_Bits',0,6,213,4,16,4,11
	.byte	'DX',0,4
	.word	8638
	.byte	12,20,2,35,0,11
	.byte	'reserved_12',0,4
	.word	8638
	.byte	4,16,2,35,0,11
	.byte	'DY',0,4
	.word	8638
	.byte	12,4,2,35,0,11
	.byte	'reserved_28',0,4
	.word	8638
	.byte	4,0,2,35,0,0,21
	.byte	'Ifx_CIF_ISPIS_DISPLACE_Bits',0,6,219,4,3
	.word	18234
	.byte	10
	.byte	'_Ifx_CIF_ISPIS_H_OFFS_Bits',0,6,222,4,16,4,11
	.byte	'IS_H_OFFS',0,4
	.word	8638
	.byte	12,20,2,35,0,11
	.byte	'reserved_12',0,4
	.word	8638
	.byte	20,0,2,35,0,0,21
	.byte	'Ifx_CIF_ISPIS_H_OFFS_Bits',0,6,226,4,3
	.word	18381
	.byte	10
	.byte	'_Ifx_CIF_ISPIS_H_OFFS_SHD_Bits',0,6,229,4,16,4,11
	.byte	'IS_H_OFFS_SHD',0,4
	.word	8638
	.byte	13,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	8638
	.byte	19,0,2,35,0,0,21
	.byte	'Ifx_CIF_ISPIS_H_OFFS_SHD_Bits',0,6,233,4,3
	.word	18494
	.byte	10
	.byte	'_Ifx_CIF_ISPIS_H_SIZE_Bits',0,6,236,4,16,4,11
	.byte	'IS_H_SIZE',0,4
	.word	8638
	.byte	12,20,2,35,0,11
	.byte	'reserved_12',0,4
	.word	8638
	.byte	20,0,2,35,0,0,21
	.byte	'Ifx_CIF_ISPIS_H_SIZE_Bits',0,6,240,4,3
	.word	18619
	.byte	10
	.byte	'_Ifx_CIF_ISPIS_H_SIZE_SHD_Bits',0,6,243,4,16,4,11
	.byte	'ISP_H_SIZE_SHD',0,4
	.word	8638
	.byte	13,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	8638
	.byte	19,0,2,35,0,0,21
	.byte	'Ifx_CIF_ISPIS_H_SIZE_SHD_Bits',0,6,247,4,3
	.word	18732
	.byte	10
	.byte	'_Ifx_CIF_ISPIS_MAX_DX_Bits',0,6,250,4,16,4,11
	.byte	'IS_MAX_DX',0,4
	.word	8638
	.byte	12,20,2,35,0,11
	.byte	'reserved_12',0,4
	.word	8638
	.byte	20,0,2,35,0,0,21
	.byte	'Ifx_CIF_ISPIS_MAX_DX_Bits',0,6,254,4,3
	.word	18858
	.byte	10
	.byte	'_Ifx_CIF_ISPIS_MAX_DY_Bits',0,6,129,5,16,4,11
	.byte	'IS_MAX_DY',0,4
	.word	8638
	.byte	12,20,2,35,0,11
	.byte	'reserved_12',0,4
	.word	8638
	.byte	20,0,2,35,0,0,21
	.byte	'Ifx_CIF_ISPIS_MAX_DY_Bits',0,6,133,5,3
	.word	18971
	.byte	10
	.byte	'_Ifx_CIF_ISPIS_RECENTER_Bits',0,6,136,5,16,4,11
	.byte	'RECENTER',0,4
	.word	8638
	.byte	3,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	8638
	.byte	29,0,2,35,0,0,21
	.byte	'Ifx_CIF_ISPIS_RECENTER_Bits',0,6,140,5,3
	.word	19084
	.byte	10
	.byte	'_Ifx_CIF_ISPIS_V_OFFS_Bits',0,6,143,5,16,4,11
	.byte	'IS_V_OFFS',0,4
	.word	8638
	.byte	12,20,2,35,0,11
	.byte	'reserved_12',0,4
	.word	8638
	.byte	20,0,2,35,0,0,21
	.byte	'Ifx_CIF_ISPIS_V_OFFS_Bits',0,6,147,5,3
	.word	19199
	.byte	10
	.byte	'_Ifx_CIF_ISPIS_V_OFFS_SHD_Bits',0,6,150,5,16,4,11
	.byte	'IS_V_OFFS_SHD',0,4
	.word	8638
	.byte	12,20,2,35,0,11
	.byte	'reserved_12',0,4
	.word	8638
	.byte	20,0,2,35,0,0,21
	.byte	'Ifx_CIF_ISPIS_V_OFFS_SHD_Bits',0,6,154,5,3
	.word	19312
	.byte	10
	.byte	'_Ifx_CIF_ISPIS_V_SIZE_Bits',0,6,157,5,16,4,11
	.byte	'IS_V_SIZE',0,4
	.word	8638
	.byte	12,20,2,35,0,11
	.byte	'reserved_12',0,4
	.word	8638
	.byte	20,0,2,35,0,0,21
	.byte	'Ifx_CIF_ISPIS_V_SIZE_Bits',0,6,161,5,3
	.word	19437
	.byte	10
	.byte	'_Ifx_CIF_ISPIS_V_SIZE_SHD_Bits',0,6,164,5,16,4,11
	.byte	'ISP_V_SIZE_SHD',0,4
	.word	8638
	.byte	12,20,2,35,0,11
	.byte	'reserved_12',0,4
	.word	8638
	.byte	20,0,2,35,0,0,21
	.byte	'Ifx_CIF_ISPIS_V_SIZE_SHD_Bits',0,6,168,5,3
	.word	19550
	.byte	10
	.byte	'_Ifx_CIF_JPE_AC_TABLE_SELECT_Bits',0,6,171,5,16,4,11
	.byte	'AC_TABLE_SELECT',0,4
	.word	8638
	.byte	3,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	8638
	.byte	29,0,2,35,0,0,21
	.byte	'Ifx_CIF_JPE_AC_TABLE_SELECT_Bits',0,6,175,5,3
	.word	19676
	.byte	10
	.byte	'_Ifx_CIF_JPE_CBCR_SCALE_EN_Bits',0,6,178,5,16,4,11
	.byte	'CBCR_SCALE_EN',0,4
	.word	8638
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	8638
	.byte	31,0,2,35,0,0,21
	.byte	'Ifx_CIF_JPE_CBCR_SCALE_EN_Bits',0,6,182,5,3
	.word	19808
	.byte	10
	.byte	'_Ifx_CIF_JPE_DC_TABLE_SELECT_Bits',0,6,185,5,16,4,11
	.byte	'DC_TABLE_SELECT',0,4
	.word	8638
	.byte	3,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	8638
	.byte	29,0,2,35,0,0,21
	.byte	'Ifx_CIF_JPE_DC_TABLE_SELECT_Bits',0,6,189,5,3
	.word	19934
	.byte	10
	.byte	'_Ifx_CIF_JPE_DEBUG_Bits',0,6,192,5,16,4,11
	.byte	'reserved_0',0,4
	.word	8638
	.byte	2,30,2,35,0,11
	.byte	'DEB_QIQ_TABLE_ACC',0,4
	.word	8638
	.byte	1,29,2,35,0,11
	.byte	'DEB_VLC_ENCODE_BUSY',0,4
	.word	8638
	.byte	1,28,2,35,0,11
	.byte	'DEB_R2B_MEMORY_FULL',0,4
	.word	8638
	.byte	1,27,2,35,0,11
	.byte	'DEB_VLC_TABLE_BUSY',0,4
	.word	8638
	.byte	1,26,2,35,0,11
	.byte	'reserved_6',0,4
	.word	8638
	.byte	2,24,2,35,0,11
	.byte	'DEB_BAD_TABLE_ACCESS',0,4
	.word	8638
	.byte	1,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	8638
	.byte	23,0,2,35,0,0,21
	.byte	'Ifx_CIF_JPE_DEBUG_Bits',0,6,202,5,3
	.word	20066
	.byte	10
	.byte	'_Ifx_CIF_JPE_ENC_HSIZE_Bits',0,6,205,5,16,4,11
	.byte	'ENC_HSIZE',0,4
	.word	8638
	.byte	12,20,2,35,0,11
	.byte	'reserved_12',0,4
	.word	8638
	.byte	20,0,2,35,0,0,21
	.byte	'Ifx_CIF_JPE_ENC_HSIZE_Bits',0,6,209,5,3
	.word	20348
	.byte	10
	.byte	'_Ifx_CIF_JPE_ENC_VSIZE_Bits',0,6,212,5,16,4,11
	.byte	'ENC_VSIZE',0,4
	.word	8638
	.byte	12,20,2,35,0,11
	.byte	'reserved_12',0,4
	.word	8638
	.byte	20,0,2,35,0,0,21
	.byte	'Ifx_CIF_JPE_ENC_VSIZE_Bits',0,6,216,5,3
	.word	20463
	.byte	10
	.byte	'_Ifx_CIF_JPE_ENCODE_Bits',0,6,219,5,16,4,11
	.byte	'ENCODE',0,4
	.word	8638
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	8638
	.byte	3,28,2,35,0,11
	.byte	'CONT_MODE',0,4
	.word	8638
	.byte	2,26,2,35,0,11
	.byte	'reserved_6',0,4
	.word	8638
	.byte	26,0,2,35,0,0,21
	.byte	'Ifx_CIF_JPE_ENCODE_Bits',0,6,225,5,3
	.word	20578
	.byte	10
	.byte	'_Ifx_CIF_JPE_ENCODE_MODE_Bits',0,6,228,5,16,4,11
	.byte	'ENCODE_MODE',0,4
	.word	8638
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	8638
	.byte	31,0,2,35,0,0,21
	.byte	'Ifx_CIF_JPE_ENCODE_MODE_Bits',0,6,232,5,3
	.word	20726
	.byte	10
	.byte	'_Ifx_CIF_JPE_ENCODER_BUSY_Bits',0,6,235,5,16,4,11
	.byte	'CODEC_BUSY',0,4
	.word	8638
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	8638
	.byte	31,0,2,35,0,0,21
	.byte	'Ifx_CIF_JPE_ENCODER_BUSY_Bits',0,6,239,5,3
	.word	20846
	.byte	10
	.byte	'_Ifx_CIF_JPE_ERROR_ICR_Bits',0,6,242,5,16,4,11
	.byte	'reserved_0',0,4
	.word	8638
	.byte	4,28,2,35,0,11
	.byte	'VLC_SYMBOL_ERR',0,4
	.word	8638
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	8638
	.byte	2,25,2,35,0,11
	.byte	'DCT_ERR',0,4
	.word	8638
	.byte	1,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	8638
	.byte	1,23,2,35,0,11
	.byte	'R2B_IMG_SIZE_ERR',0,4
	.word	8638
	.byte	1,22,2,35,0,11
	.byte	'VLC_TABLE_ERR',0,4
	.word	8638
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	8638
	.byte	21,0,2,35,0,0,21
	.byte	'Ifx_CIF_JPE_ERROR_ICR_Bits',0,6,252,5,3
	.word	20967
	.byte	10
	.byte	'_Ifx_CIF_JPE_ERROR_IMR_Bits',0,6,255,5,16,4,11
	.byte	'reserved_0',0,4
	.word	8638
	.byte	4,28,2,35,0,11
	.byte	'VLC_SYMBOL_ERR',0,4
	.word	8638
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	8638
	.byte	2,25,2,35,0,11
	.byte	'DCT_ERR',0,4
	.word	8638
	.byte	1,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	8638
	.byte	1,23,2,35,0,11
	.byte	'R2B_IMG_SIZE_ERR',0,4
	.word	8638
	.byte	1,22,2,35,0,11
	.byte	'VLC_TABLE_ERR',0,4
	.word	8638
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	8638
	.byte	21,0,2,35,0,0,21
	.byte	'Ifx_CIF_JPE_ERROR_IMR_Bits',0,6,137,6,3
	.word	21225
	.byte	10
	.byte	'_Ifx_CIF_JPE_ERROR_ISR_Bits',0,6,140,6,16,4,11
	.byte	'reserved_0',0,4
	.word	8638
	.byte	4,28,2,35,0,11
	.byte	'VLC_SYMBOL_ERR',0,4
	.word	8638
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	8638
	.byte	2,25,2,35,0,11
	.byte	'DCT_ERR',0,4
	.word	8638
	.byte	1,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	8638
	.byte	1,23,2,35,0,11
	.byte	'R2B_IMG_SIZE_ERR',0,4
	.word	8638
	.byte	1,22,2,35,0,11
	.byte	'VLC_TABLE_ERR',0,4
	.word	8638
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	8638
	.byte	21,0,2,35,0,0,21
	.byte	'Ifx_CIF_JPE_ERROR_ISR_Bits',0,6,150,6,3
	.word	21483
	.byte	10
	.byte	'_Ifx_CIF_JPE_ERROR_MIS_Bits',0,6,153,6,16,4,11
	.byte	'reserved_0',0,4
	.word	8638
	.byte	4,28,2,35,0,11
	.byte	'VLC_SYMBOL_ERR',0,4
	.word	8638
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	8638
	.byte	2,25,2,35,0,11
	.byte	'DCT_ERR',0,4
	.word	8638
	.byte	1,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	8638
	.byte	1,23,2,35,0,11
	.byte	'R2B_IMG_SIZE_ERR',0,4
	.word	8638
	.byte	1,22,2,35,0,11
	.byte	'VLC_TABLE_ERR',0,4
	.word	8638
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	8638
	.byte	21,0,2,35,0,0,21
	.byte	'Ifx_CIF_JPE_ERROR_MIS_Bits',0,6,163,6,3
	.word	21741
	.byte	10
	.byte	'_Ifx_CIF_JPE_ERROR_RIS_Bits',0,6,166,6,16,4,11
	.byte	'reserved_0',0,4
	.word	8638
	.byte	4,28,2,35,0,11
	.byte	'VLC_SYMBOL_ERR',0,4
	.word	8638
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	8638
	.byte	2,25,2,35,0,11
	.byte	'DCT_ERR',0,4
	.word	8638
	.byte	1,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	8638
	.byte	1,23,2,35,0,11
	.byte	'R2B_IMG_SIZE_ERR',0,4
	.word	8638
	.byte	1,22,2,35,0,11
	.byte	'VLC_TABLE_ERR',0,4
	.word	8638
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	8638
	.byte	21,0,2,35,0,0,21
	.byte	'Ifx_CIF_JPE_ERROR_RIS_Bits',0,6,176,6,3
	.word	21999
	.byte	10
	.byte	'_Ifx_CIF_JPE_GEN_HEADER_Bits',0,6,179,6,16,4,11
	.byte	'GEN_HEADER',0,4
	.word	8638
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	8638
	.byte	31,0,2,35,0,0,21
	.byte	'Ifx_CIF_JPE_GEN_HEADER_Bits',0,6,183,6,3
	.word	22257
	.byte	10
	.byte	'_Ifx_CIF_JPE_HEADER_MODE_Bits',0,6,186,6,16,4,11
	.byte	'HEADER_MODE',0,4
	.word	8638
	.byte	2,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	8638
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_CIF_JPE_HEADER_MODE_Bits',0,6,190,6,3
	.word	22374
	.byte	10
	.byte	'_Ifx_CIF_JPE_INIT_Bits',0,6,193,6,16,4,11
	.byte	'JP_INIT',0,4
	.word	8638
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	8638
	.byte	31,0,2,35,0,0,21
	.byte	'Ifx_CIF_JPE_INIT_Bits',0,6,197,6,3
	.word	22494
	.byte	10
	.byte	'_Ifx_CIF_JPE_PIC_FORMAT_Bits',0,6,200,6,16,4,11
	.byte	'ENC_PIC_FORMAT',0,4
	.word	8638
	.byte	3,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	8638
	.byte	29,0,2,35,0,0,21
	.byte	'Ifx_CIF_JPE_PIC_FORMAT_Bits',0,6,204,6,3
	.word	22596
	.byte	10
	.byte	'_Ifx_CIF_JPE_RESTART_INTERVAL_Bits',0,6,207,6,16,4,11
	.byte	'RESTART_INTERVAL',0,4
	.word	8638
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	8638
	.byte	16,0,2,35,0,0,21
	.byte	'Ifx_CIF_JPE_RESTART_INTERVAL_Bits',0,6,211,6,3
	.word	22717
	.byte	10
	.byte	'_Ifx_CIF_JPE_STATUS_ICR_Bits',0,6,214,6,16,4,11
	.byte	'reserved_0',0,4
	.word	8638
	.byte	4,28,2,35,0,11
	.byte	'ENCODE_DONE',0,4
	.word	8638
	.byte	1,27,2,35,0,11
	.byte	'GEN_HEADER_DONE',0,4
	.word	8638
	.byte	1,26,2,35,0,11
	.byte	'reserved_6',0,4
	.word	8638
	.byte	26,0,2,35,0,0,21
	.byte	'Ifx_CIF_JPE_STATUS_ICR_Bits',0,6,220,6,3
	.word	22853
	.byte	10
	.byte	'_Ifx_CIF_JPE_STATUS_IMR_Bits',0,6,223,6,16,4,11
	.byte	'reserved_0',0,4
	.word	8638
	.byte	4,28,2,35,0,11
	.byte	'ENCODE_DONE',0,4
	.word	8638
	.byte	1,27,2,35,0,11
	.byte	'GEN_HEADER_DONE',0,4
	.word	8638
	.byte	1,26,2,35,0,11
	.byte	'reserved_6',0,4
	.word	8638
	.byte	26,0,2,35,0,0,21
	.byte	'Ifx_CIF_JPE_STATUS_IMR_Bits',0,6,229,6,3
	.word	23020
	.byte	10
	.byte	'_Ifx_CIF_JPE_STATUS_ISR_Bits',0,6,232,6,16,4,11
	.byte	'reserved_0',0,4
	.word	8638
	.byte	4,28,2,35,0,11
	.byte	'ENCODE_DONE',0,4
	.word	8638
	.byte	1,27,2,35,0,11
	.byte	'GEN_HEADER_DONE',0,4
	.word	8638
	.byte	1,26,2,35,0,11
	.byte	'reserved_6',0,4
	.word	8638
	.byte	26,0,2,35,0,0,21
	.byte	'Ifx_CIF_JPE_STATUS_ISR_Bits',0,6,238,6,3
	.word	23187
	.byte	10
	.byte	'_Ifx_CIF_JPE_STATUS_MIS_Bits',0,6,241,6,16,4,11
	.byte	'reserved_0',0,4
	.word	8638
	.byte	4,28,2,35,0,11
	.byte	'ENCODE_DONE',0,4
	.word	8638
	.byte	1,27,2,35,0,11
	.byte	'GEN_HEADER_DONE',0,4
	.word	8638
	.byte	1,26,2,35,0,11
	.byte	'reserved_6',0,4
	.word	8638
	.byte	26,0,2,35,0,0,21
	.byte	'Ifx_CIF_JPE_STATUS_MIS_Bits',0,6,247,6,3
	.word	23354
	.byte	10
	.byte	'_Ifx_CIF_JPE_STATUS_RIS_Bits',0,6,250,6,16,4,11
	.byte	'reserved_0',0,4
	.word	8638
	.byte	4,28,2,35,0,11
	.byte	'ENCODE_DONE',0,4
	.word	8638
	.byte	1,27,2,35,0,11
	.byte	'GEN_HEADER_DONE',0,4
	.word	8638
	.byte	1,26,2,35,0,11
	.byte	'reserved_6',0,4
	.word	8638
	.byte	26,0,2,35,0,0,21
	.byte	'Ifx_CIF_JPE_STATUS_RIS_Bits',0,6,128,7,3
	.word	23521
	.byte	10
	.byte	'_Ifx_CIF_JPE_TABLE_DATA_Bits',0,6,131,7,16,4,11
	.byte	'TABLE_WDATA_L',0,4
	.word	8638
	.byte	8,24,2,35,0,11
	.byte	'TABLE_WDATA_H',0,4
	.word	8638
	.byte	8,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	8638
	.byte	16,0,2,35,0,0,21
	.byte	'Ifx_CIF_JPE_TABLE_DATA_Bits',0,6,136,7,3
	.word	23688
	.byte	10
	.byte	'_Ifx_CIF_JPE_TABLE_FLUSH_Bits',0,6,139,7,16,4,11
	.byte	'TABLE_FLUSH',0,4
	.word	8638
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	8638
	.byte	31,0,2,35,0,0,21
	.byte	'Ifx_CIF_JPE_TABLE_FLUSH_Bits',0,6,143,7,3
	.word	23834
	.byte	10
	.byte	'_Ifx_CIF_JPE_TABLE_ID_Bits',0,6,146,7,16,4,11
	.byte	'TABLE_ID',0,4
	.word	8638
	.byte	4,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	8638
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_CIF_JPE_TABLE_ID_Bits',0,6,150,7,3
	.word	23954
	.byte	10
	.byte	'_Ifx_CIF_JPE_TAC0_LEN_Bits',0,6,153,7,16,4,11
	.byte	'TAC0_LEN',0,4
	.word	8638
	.byte	8,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	8638
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_CIF_JPE_TAC0_LEN_Bits',0,6,157,7,3
	.word	24065
	.byte	10
	.byte	'_Ifx_CIF_JPE_TAC1_LEN_Bits',0,6,160,7,16,4,11
	.byte	'TAC1_LEN',0,4
	.word	8638
	.byte	8,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	8638
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_CIF_JPE_TAC1_LEN_Bits',0,6,164,7,3
	.word	24176
	.byte	10
	.byte	'_Ifx_CIF_JPE_TDC0_LEN_Bits',0,6,167,7,16,4,11
	.byte	'TDC0_LEN',0,4
	.word	8638
	.byte	8,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	8638
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_CIF_JPE_TDC0_LEN_Bits',0,6,171,7,3
	.word	24287
	.byte	10
	.byte	'_Ifx_CIF_JPE_TDC1_LEN_Bits',0,6,174,7,16,4,11
	.byte	'TDC1_LEN',0,4
	.word	8638
	.byte	8,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	8638
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_CIF_JPE_TDC1_LEN_Bits',0,6,178,7,3
	.word	24398
	.byte	10
	.byte	'_Ifx_CIF_JPE_TQ_U_SELECT_Bits',0,6,181,7,16,4,11
	.byte	'TQ1_SELECT',0,4
	.word	8638
	.byte	2,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	8638
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_CIF_JPE_TQ_U_SELECT_Bits',0,6,185,7,3
	.word	24509
	.byte	10
	.byte	'_Ifx_CIF_JPE_TQ_V_SELECT_Bits',0,6,188,7,16,4,11
	.byte	'TQ2_SELECT',0,4
	.word	8638
	.byte	2,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	8638
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_CIF_JPE_TQ_V_SELECT_Bits',0,6,192,7,3
	.word	24628
	.byte	10
	.byte	'_Ifx_CIF_JPE_TQ_Y_SELECT_Bits',0,6,195,7,16,4,11
	.byte	'TQ0_SELECT',0,4
	.word	8638
	.byte	2,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	8638
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_CIF_JPE_TQ_Y_SELECT_Bits',0,6,199,7,3
	.word	24747
	.byte	10
	.byte	'_Ifx_CIF_JPE_Y_SCALE_EN_Bits',0,6,202,7,16,4,11
	.byte	'Y_SCALE_EN',0,4
	.word	8638
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	8638
	.byte	31,0,2,35,0,0,21
	.byte	'Ifx_CIF_JPE_Y_SCALE_EN_Bits',0,6,206,7,3
	.word	24866
	.byte	10
	.byte	'_Ifx_CIF_LDS_CTRL_Bits',0,6,209,7,16,4,11
	.byte	'LDS_V_EN',0,4
	.word	8638
	.byte	1,31,2,35,0,11
	.byte	'LDS_H_EN',0,4
	.word	8638
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	8638
	.byte	2,28,2,35,0,11
	.byte	'LDS_V_MODE',0,4
	.word	8638
	.byte	2,26,2,35,0,11
	.byte	'reserved_6',0,4
	.word	8638
	.byte	2,24,2,35,0,11
	.byte	'LDS_H_MODE',0,4
	.word	8638
	.byte	2,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	8638
	.byte	22,0,2,35,0,0,21
	.byte	'Ifx_CIF_LDS_CTRL_Bits',0,6,218,7,3
	.word	24983
	.byte	10
	.byte	'_Ifx_CIF_LDS_FAC_Bits',0,6,221,7,16,4,11
	.byte	'LDS_V_FAC',0,4
	.word	8638
	.byte	8,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	8638
	.byte	8,16,2,35,0,11
	.byte	'LDS_H_FAC',0,4
	.word	8638
	.byte	8,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8638
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_CIF_LDS_FAC_Bits',0,6,227,7,3
	.word	25195
	.byte	10
	.byte	'_Ifx_CIF_MI_BYTE_CNT_Bits',0,6,230,7,16,4,11
	.byte	'BYTE_CNT',0,4
	.word	8638
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8638
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_CIF_MI_BYTE_CNT_Bits',0,6,234,7,3
	.word	25341
	.byte	10
	.byte	'_Ifx_CIF_MI_CTRL_Bits',0,6,237,7,16,4,11
	.byte	'MP_ENABLE',0,4
	.word	8638
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	8638
	.byte	1,30,2,35,0,11
	.byte	'JPEG_ENABLE',0,4
	.word	8638
	.byte	1,29,2,35,0,11
	.byte	'RAW_ENABLE',0,4
	.word	8638
	.byte	1,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	8638
	.byte	3,25,2,35,0,11
	.byte	'BYTE_SWAP',0,4
	.word	8638
	.byte	1,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	8638
	.byte	8,16,2,35,0,11
	.byte	'BURST_LEN_LUM',0,4
	.word	8638
	.byte	1,15,2,35,0,11
	.byte	'reserved_17',0,4
	.word	8638
	.byte	1,14,2,35,0,11
	.byte	'BURST_LEN_CHROM',0,4
	.word	8638
	.byte	1,13,2,35,0,11
	.byte	'reserved_19',0,4
	.word	8638
	.byte	1,12,2,35,0,11
	.byte	'INIT_BASE_EN',0,4
	.word	8638
	.byte	1,11,2,35,0,11
	.byte	'INIT_OFFSET_EN',0,4
	.word	8638
	.byte	1,10,2,35,0,11
	.byte	'MP_WRITE_FORMAT',0,4
	.word	8638
	.byte	2,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8638
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_CIF_MI_CTRL_Bits',0,6,254,7,3
	.word	25451
	.byte	10
	.byte	'_Ifx_CIF_MI_CTRL_SHD_Bits',0,6,129,8,16,4,11
	.byte	'MP_ENABLE_IN',0,4
	.word	8638
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	8638
	.byte	3,28,2,35,0,11
	.byte	'JPEG_ENABLE_IN',0,4
	.word	8638
	.byte	1,27,2,35,0,11
	.byte	'RAW_ENABLE_IN',0,4
	.word	8638
	.byte	1,26,2,35,0,11
	.byte	'reserved_6',0,4
	.word	8638
	.byte	10,16,2,35,0,11
	.byte	'MP_ENABLE_OUT',0,4
	.word	8638
	.byte	1,15,2,35,0,11
	.byte	'reserved_17',0,4
	.word	8638
	.byte	1,14,2,35,0,11
	.byte	'JPEG_ENABLE_OUT',0,4
	.word	8638
	.byte	1,13,2,35,0,11
	.byte	'RAW_ENABLE_OUT',0,4
	.word	8638
	.byte	1,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	8638
	.byte	12,0,2,35,0,0,21
	.byte	'Ifx_CIF_MI_CTRL_SHD_Bits',0,6,141,8,3
	.word	25861
	.byte	10
	.byte	'_Ifx_CIF_MI_ICR_Bits',0,6,144,8,16,4,11
	.byte	'MP_FRAME_END',0,4
	.word	8638
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	8638
	.byte	1,30,2,35,0,11
	.byte	'MBLK_LINE',0,4
	.word	8638
	.byte	1,29,2,35,0,11
	.byte	'FILL_MPY',0,4
	.word	8638
	.byte	1,28,2,35,0,11
	.byte	'WRAP_MP_Y',0,4
	.word	8638
	.byte	1,27,2,35,0,11
	.byte	'WRAP_MP_CB',0,4
	.word	8638
	.byte	1,26,2,35,0,11
	.byte	'WRAP_MP_CR',0,4
	.word	8638
	.byte	1,25,2,35,0,11
	.byte	'reserved_7',0,4
	.word	8638
	.byte	3,22,2,35,0,11
	.byte	'BUS_ERROR',0,4
	.word	8638
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	8638
	.byte	21,0,2,35,0,0,21
	.byte	'Ifx_CIF_MI_ICR_Bits',0,6,156,8,3
	.word	26171
	.byte	10
	.byte	'_Ifx_CIF_MI_IMSC_Bits',0,6,159,8,16,4,11
	.byte	'MP_FRAME_END',0,4
	.word	8638
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	8638
	.byte	1,30,2,35,0,11
	.byte	'MBLK_LINE',0,4
	.word	8638
	.byte	1,29,2,35,0,11
	.byte	'FILL_MP_Y',0,4
	.word	8638
	.byte	1,28,2,35,0,11
	.byte	'WRAP_MP_Y',0,4
	.word	8638
	.byte	1,27,2,35,0,11
	.byte	'WRAP_MP_CB',0,4
	.word	8638
	.byte	1,26,2,35,0,11
	.byte	'WRAP_MP_CR',0,4
	.word	8638
	.byte	1,25,2,35,0,11
	.byte	'reserved_7',0,4
	.word	8638
	.byte	3,22,2,35,0,11
	.byte	'BUS_ERROR',0,4
	.word	8638
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	8638
	.byte	21,0,2,35,0,0,21
	.byte	'Ifx_CIF_MI_IMSC_Bits',0,6,171,8,3
	.word	26446
	.byte	10
	.byte	'_Ifx_CIF_MI_INIT_Bits',0,6,175,8,16,4,11
	.byte	'reserved_0',0,4
	.word	8638
	.byte	2,30,2,35,0,11
	.byte	'MI_SKIP',0,4
	.word	8638
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	8638
	.byte	1,28,2,35,0,11
	.byte	'MI_CFG_UPD',0,4
	.word	8638
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	8638
	.byte	27,0,2,35,0,0,21
	.byte	'Ifx_CIF_MI_INIT_Bits',0,6,182,8,3
	.word	26724
	.byte	10
	.byte	'_Ifx_CIF_MI_ISR_Bits',0,6,185,8,16,4,11
	.byte	'MP_FRAME_END',0,4
	.word	8638
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	8638
	.byte	1,30,2,35,0,11
	.byte	'MBLK_LINE',0,4
	.word	8638
	.byte	1,29,2,35,0,11
	.byte	'FILL_MP_Y',0,4
	.word	8638
	.byte	1,28,2,35,0,11
	.byte	'WRAP_MP_Y',0,4
	.word	8638
	.byte	1,27,2,35,0,11
	.byte	'WRAP_MP_CB',0,4
	.word	8638
	.byte	1,26,2,35,0,11
	.byte	'WRAP_MP_CR',0,4
	.word	8638
	.byte	1,25,2,35,0,11
	.byte	'reserved_7',0,4
	.word	8638
	.byte	3,22,2,35,0,11
	.byte	'Bus_ERROR',0,4
	.word	8638
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	8638
	.byte	21,0,2,35,0,0,21
	.byte	'Ifx_CIF_MI_ISR_Bits',0,6,197,8,3
	.word	26890
	.byte	10
	.byte	'_Ifx_CIF_MI_MIS_Bits',0,6,200,8,16,4,11
	.byte	'MP_FRAME_END',0,4
	.word	8638
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	8638
	.byte	1,30,2,35,0,11
	.byte	'MBLK_LINE',0,4
	.word	8638
	.byte	1,29,2,35,0,11
	.byte	'FILL_MP_Y',0,4
	.word	8638
	.byte	1,28,2,35,0,11
	.byte	'WRAP_MP_Y',0,4
	.word	8638
	.byte	1,27,2,35,0,11
	.byte	'WRAP_MP_CB',0,4
	.word	8638
	.byte	1,26,2,35,0,11
	.byte	'WRAP_MP_CR',0,4
	.word	8638
	.byte	1,25,2,35,0,11
	.byte	'reserved_7',0,4
	.word	8638
	.byte	3,22,2,35,0,11
	.byte	'BUS_ERROR',0,4
	.word	8638
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	8638
	.byte	21,0,2,35,0,0,21
	.byte	'Ifx_CIF_MI_MIS_Bits',0,6,212,8,3
	.word	27166
	.byte	10
	.byte	'_Ifx_CIF_MI_MP_CB_BASE_AD_INIT_Bits',0,6,216,8,16,4,11
	.byte	'FIXED_TO_00',0,4
	.word	8638
	.byte	2,30,2,35,0,11
	.byte	'MP_CB_BASEAD_INIT',0,4
	.word	8638
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_CIF_MI_MP_CB_BASE_AD_INIT_Bits',0,6,220,8,3
	.word	27442
	.byte	10
	.byte	'_Ifx_CIF_MI_MP_CB_BASE_AD_SHD_Bits',0,6,224,8,16,4,11
	.byte	'FIXED_TO_00',0,4
	.word	8638
	.byte	2,30,2,35,0,11
	.byte	'MP_CB_BASE_AD',0,4
	.word	8638
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_CIF_MI_MP_CB_BASE_AD_SHD_Bits',0,6,228,8,3
	.word	27581
	.byte	10
	.byte	'_Ifx_CIF_MI_MP_CB_OFFS_CNT_INIT_Bits',0,6,232,8,16,4,11
	.byte	'FIXED_TO_00',0,4
	.word	8638
	.byte	2,30,2,35,0,11
	.byte	'MP_CB_OFFS_CNT_INIT',0,4
	.word	8638
	.byte	22,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8638
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_CIF_MI_MP_CB_OFFS_CNT_INIT_Bits',0,6,237,8,3
	.word	27714
	.byte	10
	.byte	'_Ifx_CIF_MI_MP_CB_OFFS_CNT_SHD_Bits',0,6,241,8,16,4,11
	.byte	'FIXED_TO_00',0,4
	.word	8638
	.byte	2,30,2,35,0,11
	.byte	'MP_CB_OFFS_CNT',0,4
	.word	8638
	.byte	22,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8638
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_CIF_MI_MP_CB_OFFS_CNT_SHD_Bits',0,6,246,8,3
	.word	27880
	.byte	10
	.byte	'_Ifx_CIF_MI_MP_CB_OFFS_CNT_START_Bits',0,6,250,8,16,4,11
	.byte	'FIXED_TO_00',0,4
	.word	8638
	.byte	2,30,2,35,0,11
	.byte	'MP_CB_OFFS_CNT_START',0,4
	.word	8638
	.byte	22,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8638
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_CIF_MI_MP_CB_OFFS_CNT_START_Bits',0,6,255,8,3
	.word	28039
	.byte	10
	.byte	'_Ifx_CIF_MI_MP_CB_SIZE_INIT_Bits',0,6,131,9,16,4,11
	.byte	'FIXED_TO_00',0,4
	.word	8638
	.byte	2,30,2,35,0,11
	.byte	'MP_CB_SIZE_INIT',0,4
	.word	8638
	.byte	22,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8638
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_CIF_MI_MP_CB_SIZE_INIT_Bits',0,6,136,9,3
	.word	28208
	.byte	10
	.byte	'_Ifx_CIF_MI_MP_CB_SIZE_SHD_Bits',0,6,140,9,16,4,11
	.byte	'FIXED_TO_00',0,4
	.word	8638
	.byte	2,30,2,35,0,11
	.byte	'MP_CB_SIZE',0,4
	.word	8638
	.byte	22,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8638
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_CIF_MI_MP_CB_SIZE_SHD_Bits',0,6,145,9,3
	.word	28362
	.byte	10
	.byte	'_Ifx_CIF_MI_MP_CR_BASE_AD_INIT_Bits',0,6,149,9,16,4,11
	.byte	'FIXED_TO_00',0,4
	.word	8638
	.byte	2,30,2,35,0,11
	.byte	'MP_CR_BASE_AD_INIT',0,4
	.word	8638
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_CIF_MI_MP_CR_BASE_AD_INIT_Bits',0,6,153,9,3
	.word	28509
	.byte	10
	.byte	'_Ifx_CIF_MI_MP_CR_BASE_AD_SHD_Bits',0,6,157,9,16,4,11
	.byte	'FIXED_TO_00',0,4
	.word	8638
	.byte	2,30,2,35,0,11
	.byte	'MP_CR_BASE_AD',0,4
	.word	8638
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_CIF_MI_MP_CR_BASE_AD_SHD_Bits',0,6,161,9,3
	.word	28649
	.byte	10
	.byte	'_Ifx_CIF_MI_MP_CR_OFFS_CNT_INIT_Bits',0,6,165,9,16,4,11
	.byte	'FIXED_TO_00',0,4
	.word	8638
	.byte	2,30,2,35,0,11
	.byte	'MP_CR_OFFS_CNT_INIT',0,4
	.word	8638
	.byte	22,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8638
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_CIF_MI_MP_CR_OFFS_CNT_INIT_Bits',0,6,170,9,3
	.word	28782
	.byte	10
	.byte	'_Ifx_CIF_MI_MP_CR_OFFS_CNT_SHD_Bits',0,6,174,9,16,4,11
	.byte	'FIXED_TO_00',0,4
	.word	8638
	.byte	2,30,2,35,0,11
	.byte	'MP_CR_OFFS_CNT',0,4
	.word	8638
	.byte	22,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8638
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_CIF_MI_MP_CR_OFFS_CNT_SHD_Bits',0,6,179,9,3
	.word	28948
	.byte	10
	.byte	'_Ifx_CIF_MI_MP_CR_OFFS_CNT_START_Bits',0,6,183,9,16,4,11
	.byte	'FIXED_TO_00',0,4
	.word	8638
	.byte	2,30,2,35,0,11
	.byte	'MP_CR_OFFS_CNT_START',0,4
	.word	8638
	.byte	22,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8638
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_CIF_MI_MP_CR_OFFS_CNT_START_Bits',0,6,188,9,3
	.word	29107
	.byte	10
	.byte	'_Ifx_CIF_MI_MP_CR_SIZE_INIT_Bits',0,6,192,9,16,4,11
	.byte	'FIXED_TO_00',0,4
	.word	8638
	.byte	2,30,2,35,0,11
	.byte	'MP_CR_SIZE_INIT',0,4
	.word	8638
	.byte	22,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8638
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_CIF_MI_MP_CR_SIZE_INIT_Bits',0,6,197,9,3
	.word	29276
	.byte	10
	.byte	'_Ifx_CIF_MI_MP_CR_SIZE_SHD_Bits',0,6,201,9,16,4,11
	.byte	'FIXED_TO_00',0,4
	.word	8638
	.byte	2,30,2,35,0,11
	.byte	'MP_CR_SIZE',0,4
	.word	8638
	.byte	22,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8638
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_CIF_MI_MP_CR_SIZE_SHD_Bits',0,6,206,9,3
	.word	29430
	.byte	10
	.byte	'_Ifx_CIF_MI_MP_Y_BASE_AD_INIT_Bits',0,6,210,9,16,4,11
	.byte	'FIXED_TO_00',0,4
	.word	8638
	.byte	2,30,2,35,0,11
	.byte	'MP_Y_BASE_AD_INIT',0,4
	.word	8638
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_CIF_MI_MP_Y_BASE_AD_INIT_Bits',0,6,214,9,3
	.word	29577
	.byte	10
	.byte	'_Ifx_CIF_MI_MP_Y_BASE_AD_SHD_Bits',0,6,218,9,16,4,11
	.byte	'FIXED_TO_00',0,4
	.word	8638
	.byte	2,30,2,35,0,11
	.byte	'MP_Y_BASE_AD',0,4
	.word	8638
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_CIF_MI_MP_Y_BASE_AD_SHD_Bits',0,6,222,9,3
	.word	29714
	.byte	10
	.byte	'_Ifx_CIF_MI_MP_Y_IRQ_OFFS_INIT_Bits',0,6,226,9,16,4,11
	.byte	'FIXED_TO_00',0,4
	.word	8638
	.byte	2,30,2,35,0,11
	.byte	'MP_Y_IRQ_OFFS_INIT',0,4
	.word	8638
	.byte	22,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8638
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_CIF_MI_MP_Y_IRQ_OFFS_INIT_Bits',0,6,231,9,3
	.word	29844
	.byte	10
	.byte	'_Ifx_CIF_MI_MP_Y_IRQ_OFFS_SHD_Bits',0,6,235,9,16,4,11
	.byte	'FIXED_TO_00',0,4
	.word	8638
	.byte	2,30,2,35,0,11
	.byte	'MP_Y_IRQ_OFFS',0,4
	.word	8638
	.byte	22,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8638
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_CIF_MI_MP_Y_IRQ_OFFS_SHD_Bits',0,6,240,9,3
	.word	30007
	.byte	10
	.byte	'_Ifx_CIF_MI_MP_Y_OFFS_CNT_INIT_Bits',0,6,244,9,16,4,11
	.byte	'FIXED_TO_00',0,4
	.word	8638
	.byte	2,30,2,35,0,11
	.byte	'MP_Y_OFFS_CNT_INIT',0,4
	.word	8638
	.byte	22,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8638
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_CIF_MI_MP_Y_OFFS_CNT_INIT_Bits',0,6,249,9,3
	.word	30163
	.byte	10
	.byte	'_Ifx_CIF_MI_MP_Y_OFFS_CNT_SHD_Bits',0,6,253,9,16,4,11
	.byte	'FIXED_TO_00',0,4
	.word	8638
	.byte	2,30,2,35,0,11
	.byte	'MP_Y_OFFS_CNT',0,4
	.word	8638
	.byte	22,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8638
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_CIF_MI_MP_Y_OFFS_CNT_SHD_Bits',0,6,130,10,3
	.word	30326
	.byte	10
	.byte	'_Ifx_CIF_MI_MP_Y_OFFS_CNT_START_Bits',0,6,134,10,16,4,11
	.byte	'FIXED_TO_00',0,4
	.word	8638
	.byte	2,30,2,35,0,11
	.byte	'MP_Y_OFFS_CNT_START',0,4
	.word	8638
	.byte	22,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8638
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_CIF_MI_MP_Y_OFFS_CNT_START_Bits',0,6,139,10,3
	.word	30482
	.byte	10
	.byte	'_Ifx_CIF_MI_MP_Y_SIZE_INIT_Bits',0,6,143,10,16,4,11
	.byte	'FIXED_TO_00',0,4
	.word	8638
	.byte	2,30,2,35,0,11
	.byte	'MP_Y_SIZE_INIT',0,4
	.word	8638
	.byte	22,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8638
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_CIF_MI_MP_Y_SIZE_INIT_Bits',0,6,148,10,3
	.word	30648
	.byte	10
	.byte	'_Ifx_CIF_MI_MP_Y_SIZE_SHD_Bits',0,6,152,10,16,4,11
	.byte	'FIXED_TO_00',0,4
	.word	8638
	.byte	2,30,2,35,0,11
	.byte	'MP_Y_SIZE',0,4
	.word	8638
	.byte	22,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8638
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_CIF_MI_MP_Y_SIZE_SHD_Bits',0,6,157,10,3
	.word	30799
	.byte	10
	.byte	'_Ifx_CIF_MI_RIS_Bits',0,6,160,10,16,4,11
	.byte	'MP_FRAME_END',0,4
	.word	8638
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	8638
	.byte	1,30,2,35,0,11
	.byte	'MBLK_LINE',0,4
	.word	8638
	.byte	1,29,2,35,0,11
	.byte	'FILL_MP_Y',0,4
	.word	8638
	.byte	1,28,2,35,0,11
	.byte	'WRAP_MP_Y',0,4
	.word	8638
	.byte	1,27,2,35,0,11
	.byte	'WRAP_MP_CB',0,4
	.word	8638
	.byte	1,26,2,35,0,11
	.byte	'WRAP_MP_CR',0,4
	.word	8638
	.byte	1,25,2,35,0,11
	.byte	'reserved_7',0,4
	.word	8638
	.byte	3,22,2,35,0,11
	.byte	'BUS_ERROR',0,4
	.word	8638
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	8638
	.byte	21,0,2,35,0,0,21
	.byte	'Ifx_CIF_MI_RIS_Bits',0,6,172,10,3
	.word	30943
	.byte	10
	.byte	'_Ifx_CIF_MI_STATUS_Bits',0,6,175,10,16,4,11
	.byte	'MP_Y_FIFO_FULL',0,4
	.word	8638
	.byte	1,31,2,35,0,11
	.byte	'MP_CB_FIFO_FULL',0,4
	.word	8638
	.byte	1,30,2,35,0,11
	.byte	'MP_CR_FIFO_FULL',0,4
	.word	8638
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	8638
	.byte	5,24,2,35,0,11
	.byte	'BUS_WRITE_ERROR',0,4
	.word	8638
	.byte	1,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	8638
	.byte	23,0,2,35,0,0,21
	.byte	'Ifx_CIF_MI_STATUS_Bits',0,6,183,10,3
	.word	31219
	.byte	10
	.byte	'_Ifx_CIF_MI_STATUS_CLR_Bits',0,6,186,10,16,4,11
	.byte	'MP_Y_FIFO_FULL',0,4
	.word	8638
	.byte	1,31,2,35,0,11
	.byte	'MP_CB_FIFO_FULL',0,4
	.word	8638
	.byte	1,30,2,35,0,11
	.byte	'MP_CR_FIFO_FULL',0,4
	.word	8638
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	8638
	.byte	5,24,2,35,0,11
	.byte	'BUS_WRITE_ERROR',0,4
	.word	8638
	.byte	1,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	8638
	.byte	15,8,2,35,0,11
	.byte	'EP_1_FIFO_FULL',0,4
	.word	8638
	.byte	1,7,2,35,0,11
	.byte	'EP_2_FIFO_FULL',0,4
	.word	8638
	.byte	1,6,2,35,0,11
	.byte	'EP_3_FIFO_FULL',0,4
	.word	8638
	.byte	1,5,2,35,0,11
	.byte	'EP_4_FIFO_FULL',0,4
	.word	8638
	.byte	1,4,2,35,0,11
	.byte	'EP_5_FIFO_FULL',0,4
	.word	8638
	.byte	1,3,2,35,0,11
	.byte	'reserved_29',0,4
	.word	8638
	.byte	3,0,2,35,0,0,21
	.byte	'Ifx_CIF_MI_STATUS_CLR_Bits',0,6,200,10,3
	.word	31433
	.byte	10
	.byte	'_Ifx_CIF_MIEP_CH_BASE_AD_INIT_Bits',0,6,203,10,16,4,11
	.byte	'FIXED_TO_00',0,4
	.word	8638
	.byte	2,30,2,35,0,11
	.byte	'EP_BASE_AD_INIT',0,4
	.word	8638
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_CIF_MIEP_CH_BASE_AD_INIT_Bits',0,6,207,10,3
	.word	31808
	.byte	10
	.byte	'_Ifx_CIF_MIEP_CH_BASE_AD_SHD_Bits',0,6,211,10,16,4,11
	.byte	'FIXED_TO_00',0,4
	.word	8638
	.byte	2,30,2,35,0,11
	.byte	'EP_BASE_AD',0,4
	.word	8638
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_CIF_MIEP_CH_BASE_AD_SHD_Bits',0,6,215,10,3
	.word	31943
	.byte	10
	.byte	'_Ifx_CIF_MIEP_CH_CTRL_Bits',0,6,218,10,16,4,11
	.byte	'EP_ENABLE',0,4
	.word	8638
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	8638
	.byte	6,25,2,35,0,11
	.byte	'BYTE_SWAP',0,4
	.word	8638
	.byte	1,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	8638
	.byte	12,12,2,35,0,11
	.byte	'INIT_BASE_EN',0,4
	.word	8638
	.byte	1,11,2,35,0,11
	.byte	'INIT_OFFSET_EN',0,4
	.word	8638
	.byte	1,10,2,35,0,11
	.byte	'EP_WRITE_FORMAT',0,4
	.word	8638
	.byte	2,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8638
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_CIF_MIEP_CH_CTRL_Bits',0,6,228,10,3
	.word	32071
	.byte	10
	.byte	'_Ifx_CIF_MIEP_CH_CTRL_SHD_Bits',0,6,231,10,16,4,11
	.byte	'EP_ENABLE_IN',0,4
	.word	8638
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	8638
	.byte	15,16,2,35,0,11
	.byte	'EP_ENABLE_OUT',0,4
	.word	8638
	.byte	1,15,2,35,0,11
	.byte	'reserved_17',0,4
	.word	8638
	.byte	15,0,2,35,0,0,21
	.byte	'Ifx_CIF_MIEP_CH_CTRL_SHD_Bits',0,6,237,10,3
	.word	32326
	.byte	10
	.byte	'_Ifx_CIF_MIEP_CH_INIT_Bits',0,6,241,10,16,4,11
	.byte	'reserved_0',0,4
	.word	8638
	.byte	2,30,2,35,0,11
	.byte	'MI_EP_SKIP',0,4
	.word	8638
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	8638
	.byte	1,28,2,35,0,11
	.byte	'MI_EP_CFG_UPD',0,4
	.word	8638
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	8638
	.byte	27,0,2,35,0,0,21
	.byte	'Ifx_CIF_MIEP_CH_INIT_Bits',0,6,248,10,3
	.word	32497
	.byte	10
	.byte	'_Ifx_CIF_MIEP_CH_IRQ_OFFS_INIT_Bits',0,6,252,10,16,4,11
	.byte	'FIXED_TO_00',0,4
	.word	8638
	.byte	2,30,2,35,0,11
	.byte	'EP_IRQ_OFFS_INIT',0,4
	.word	8638
	.byte	22,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8638
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_CIF_MIEP_CH_IRQ_OFFS_INIT_Bits',0,6,129,11,3
	.word	32679
	.byte	10
	.byte	'_Ifx_CIF_MIEP_CH_IRQ_OFFS_SHD_Bits',0,6,133,11,16,4,11
	.byte	'FIXED_TO_00',0,4
	.word	8638
	.byte	2,30,2,35,0,11
	.byte	'EP_IRQ_OFFS',0,4
	.word	8638
	.byte	22,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8638
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_CIF_MIEP_CH_IRQ_OFFS_SHD_Bits',0,6,138,11,3
	.word	32840
	.byte	10
	.byte	'_Ifx_CIF_MIEP_CH_OFFS_CNT_INIT_Bits',0,6,142,11,16,4,11
	.byte	'FIXED_TO_00',0,4
	.word	8638
	.byte	2,30,2,35,0,11
	.byte	'EP_OFFS_CNT_INIT',0,4
	.word	8638
	.byte	22,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8638
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_CIF_MIEP_CH_OFFS_CNT_INIT_Bits',0,6,147,11,3
	.word	32994
	.byte	10
	.byte	'_Ifx_CIF_MIEP_CH_OFFS_CNT_SHD_Bits',0,6,151,11,16,4,11
	.byte	'FIXED_TO_00',0,4
	.word	8638
	.byte	2,30,2,35,0,11
	.byte	'EP_OFFS_CNT',0,4
	.word	8638
	.byte	22,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8638
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_CIF_MIEP_CH_OFFS_CNT_SHD_Bits',0,6,156,11,3
	.word	33155
	.byte	10
	.byte	'_Ifx_CIF_MIEP_CH_OFFS_CNT_START_Bits',0,6,159,11,16,4,11
	.byte	'FIXED_TO_00',0,4
	.word	8638
	.byte	2,30,2,35,0,11
	.byte	'EP_OFFS_CNT_START',0,4
	.word	8638
	.byte	22,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8638
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_CIF_MIEP_CH_OFFS_CNT_START_Bits',0,6,164,11,3
	.word	33309
	.byte	10
	.byte	'_Ifx_CIF_MIEP_CH_SIZE_INIT_Bits',0,6,167,11,16,4,11
	.byte	'FIXED_TO_00',0,4
	.word	8638
	.byte	2,30,2,35,0,11
	.byte	'EP_SIZE_INIT',0,4
	.word	8638
	.byte	22,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8638
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_CIF_MIEP_CH_SIZE_INIT_Bits',0,6,172,11,3
	.word	33473
	.byte	10
	.byte	'_Ifx_CIF_MIEP_CH_SIZE_SHD_Bits',0,6,175,11,16,4,11
	.byte	'FIXED_TO_00',0,4
	.word	8638
	.byte	2,30,2,35,0,11
	.byte	'EP_SIZE',0,4
	.word	8638
	.byte	22,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8638
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_CIF_MIEP_CH_SIZE_SHD_Bits',0,6,180,11,3
	.word	33622
	.byte	10
	.byte	'_Ifx_CIF_MIEP_ICR_Bits',0,6,183,11,16,4,11
	.byte	'FRAME_END_EP_1',0,4
	.word	8638
	.byte	1,31,2,35,0,11
	.byte	'FILL_EP_1',0,4
	.word	8638
	.byte	1,30,2,35,0,11
	.byte	'WRAP_EP_1',0,4
	.word	8638
	.byte	1,29,2,35,0,11
	.byte	'MBLK_LINE_EP_1',0,4
	.word	8638
	.byte	1,28,2,35,0,11
	.byte	'FRAME_END_EP_2',0,4
	.word	8638
	.byte	1,27,2,35,0,11
	.byte	'FILL_EP_2',0,4
	.word	8638
	.byte	1,26,2,35,0,11
	.byte	'WRAP_EP_2',0,4
	.word	8638
	.byte	1,25,2,35,0,11
	.byte	'reserved_7',0,4
	.word	8638
	.byte	1,24,2,35,0,11
	.byte	'FRAME_END_EP_3',0,4
	.word	8638
	.byte	1,23,2,35,0,11
	.byte	'FILL_EP_3',0,4
	.word	8638
	.byte	1,22,2,35,0,11
	.byte	'WRAP_EP_3',0,4
	.word	8638
	.byte	1,21,2,35,0,11
	.byte	'MBLK_LINE_EP_3',0,4
	.word	8638
	.byte	1,20,2,35,0,11
	.byte	'FRAME_END_EP_4',0,4
	.word	8638
	.byte	1,19,2,35,0,11
	.byte	'FILL_EP_4',0,4
	.word	8638
	.byte	1,18,2,35,0,11
	.byte	'WRAP_EP_4',0,4
	.word	8638
	.byte	1,17,2,35,0,11
	.byte	'MBLK_LINE_EP_4',0,4
	.word	8638
	.byte	1,16,2,35,0,11
	.byte	'FRAME_END_EP_5',0,4
	.word	8638
	.byte	1,15,2,35,0,11
	.byte	'FILL_EP_5',0,4
	.word	8638
	.byte	1,14,2,35,0,11
	.byte	'WRAP_EP_5',0,4
	.word	8638
	.byte	1,13,2,35,0,11
	.byte	'MBLK_LINE_EP_5',0,4
	.word	8638
	.byte	1,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	8638
	.byte	12,0,2,35,0,0,21
	.byte	'Ifx_CIF_MIEP_ICR_Bits',0,6,206,11,3
	.word	33764
	.byte	10
	.byte	'_Ifx_CIF_MIEP_IMSC_Bits',0,6,210,11,16,4,11
	.byte	'FRAME_END_EP_1',0,4
	.word	8638
	.byte	1,31,2,35,0,11
	.byte	'FILL_EP_1',0,4
	.word	8638
	.byte	1,30,2,35,0,11
	.byte	'WRAP_EP_1',0,4
	.word	8638
	.byte	1,29,2,35,0,11
	.byte	'MBLK_LINE_EP_1',0,4
	.word	8638
	.byte	1,28,2,35,0,11
	.byte	'FRAME_END_EP_2',0,4
	.word	8638
	.byte	1,27,2,35,0,11
	.byte	'FILL_EP_2',0,4
	.word	8638
	.byte	1,26,2,35,0,11
	.byte	'WRAP_EP_2',0,4
	.word	8638
	.byte	1,25,2,35,0,11
	.byte	'MBLK_LINE_EP_2',0,4
	.word	8638
	.byte	1,24,2,35,0,11
	.byte	'FRAME_END_EP_3',0,4
	.word	8638
	.byte	1,23,2,35,0,11
	.byte	'FILL_EP_3',0,4
	.word	8638
	.byte	1,22,2,35,0,11
	.byte	'WRAP_EP_3',0,4
	.word	8638
	.byte	1,21,2,35,0,11
	.byte	'MBLK_LINE_EP_3',0,4
	.word	8638
	.byte	1,20,2,35,0,11
	.byte	'FRAME_END_EP_4',0,4
	.word	8638
	.byte	1,19,2,35,0,11
	.byte	'FILL_EP_4',0,4
	.word	8638
	.byte	1,18,2,35,0,11
	.byte	'WRAP_EP_4',0,4
	.word	8638
	.byte	1,17,2,35,0,11
	.byte	'MBLK_LINE_EP_4',0,4
	.word	8638
	.byte	1,16,2,35,0,11
	.byte	'FRAME_END_EP_5',0,4
	.word	8638
	.byte	1,15,2,35,0,11
	.byte	'FILL_EP_5',0,4
	.word	8638
	.byte	1,14,2,35,0,11
	.byte	'WRAP_EP_5',0,4
	.word	8638
	.byte	1,13,2,35,0,11
	.byte	'MBLK_LINE_EP_5',0,4
	.word	8638
	.byte	1,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	8638
	.byte	12,0,2,35,0,0,21
	.byte	'Ifx_CIF_MIEP_IMSC_Bits',0,6,233,11,3
	.word	34314
	.byte	10
	.byte	'_Ifx_CIF_MIEP_ISR_Bits',0,6,236,11,16,4,11
	.byte	'FRAME_END_EP_1',0,4
	.word	8638
	.byte	1,31,2,35,0,11
	.byte	'FILL_EP_1',0,4
	.word	8638
	.byte	1,30,2,35,0,11
	.byte	'WRAP_EP_1',0,4
	.word	8638
	.byte	1,29,2,35,0,11
	.byte	'MBLK_LINE_EP_1',0,4
	.word	8638
	.byte	1,28,2,35,0,11
	.byte	'FRAME_END_EP_2',0,4
	.word	8638
	.byte	1,27,2,35,0,11
	.byte	'FILL_EP_2',0,4
	.word	8638
	.byte	1,26,2,35,0,11
	.byte	'WRAP_EP_2',0,4
	.word	8638
	.byte	1,25,2,35,0,11
	.byte	'MBLK_LINE_EP_2',0,4
	.word	8638
	.byte	1,24,2,35,0,11
	.byte	'FRAME_END_EP_3',0,4
	.word	8638
	.byte	1,23,2,35,0,11
	.byte	'FILL_EP_3',0,4
	.word	8638
	.byte	1,22,2,35,0,11
	.byte	'WRAP_EP_3',0,4
	.word	8638
	.byte	1,21,2,35,0,11
	.byte	'MBLK_LINE_EP_3',0,4
	.word	8638
	.byte	1,20,2,35,0,11
	.byte	'FRAME_END_EP_4',0,4
	.word	8638
	.byte	1,19,2,35,0,11
	.byte	'FILL_EP_4',0,4
	.word	8638
	.byte	1,18,2,35,0,11
	.byte	'WRAP_EP_4',0,4
	.word	8638
	.byte	1,17,2,35,0,11
	.byte	'MBLK_LINE_EP_4',0,4
	.word	8638
	.byte	1,16,2,35,0,11
	.byte	'FRAME_END_EP_5',0,4
	.word	8638
	.byte	1,15,2,35,0,11
	.byte	'FILL_EP_5',0,4
	.word	8638
	.byte	1,14,2,35,0,11
	.byte	'WRAP_EP_5',0,4
	.word	8638
	.byte	1,13,2,35,0,11
	.byte	'MBLK_LINE_EP_5',0,4
	.word	8638
	.byte	1,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	8638
	.byte	12,0,2,35,0,0,21
	.byte	'Ifx_CIF_MIEP_ISR_Bits',0,6,131,12,3
	.word	34870
	.byte	10
	.byte	'_Ifx_CIF_MIEP_MIS_Bits',0,6,134,12,16,4,11
	.byte	'FRAME_END_EP_1',0,4
	.word	8638
	.byte	1,31,2,35,0,11
	.byte	'FILL_EP_1',0,4
	.word	8638
	.byte	1,30,2,35,0,11
	.byte	'WRAP_EP_1',0,4
	.word	8638
	.byte	1,29,2,35,0,11
	.byte	'MBLK_LINE_EP_1',0,4
	.word	8638
	.byte	1,28,2,35,0,11
	.byte	'FRAME_END_EP_2',0,4
	.word	8638
	.byte	1,27,2,35,0,11
	.byte	'FILL_EP_2',0,4
	.word	8638
	.byte	1,26,2,35,0,11
	.byte	'WRAP_EP_2',0,4
	.word	8638
	.byte	1,25,2,35,0,11
	.byte	'MBLK_LINE_EP_2',0,4
	.word	8638
	.byte	1,24,2,35,0,11
	.byte	'FRAME_END_EP_3',0,4
	.word	8638
	.byte	1,23,2,35,0,11
	.byte	'FILL_EP_3',0,4
	.word	8638
	.byte	1,22,2,35,0,11
	.byte	'WRAP_EP_3',0,4
	.word	8638
	.byte	1,21,2,35,0,11
	.byte	'MBLK_LINE_EP_3',0,4
	.word	8638
	.byte	1,20,2,35,0,11
	.byte	'FRAME_END_EP_4',0,4
	.word	8638
	.byte	1,19,2,35,0,11
	.byte	'FILL_EP_4',0,4
	.word	8638
	.byte	1,18,2,35,0,11
	.byte	'WRAP_EP_4',0,4
	.word	8638
	.byte	1,17,2,35,0,11
	.byte	'MBLK_LINE_EP_4',0,4
	.word	8638
	.byte	1,16,2,35,0,11
	.byte	'FRAME_END_EP_5',0,4
	.word	8638
	.byte	1,15,2,35,0,11
	.byte	'FILL_EP_5',0,4
	.word	8638
	.byte	1,14,2,35,0,11
	.byte	'WRAP_EP_5',0,4
	.word	8638
	.byte	1,13,2,35,0,11
	.byte	'MBLK_LINE_EP_5',0,4
	.word	8638
	.byte	1,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	8638
	.byte	12,0,2,35,0,0,21
	.byte	'Ifx_CIF_MIEP_MIS_Bits',0,6,157,12,3
	.word	35424
	.byte	10
	.byte	'_Ifx_CIF_MIEP_RIS_Bits',0,6,160,12,16,4,11
	.byte	'FRAME_END_EP_1',0,4
	.word	8638
	.byte	1,31,2,35,0,11
	.byte	'FILL_EP_1',0,4
	.word	8638
	.byte	1,30,2,35,0,11
	.byte	'WRAP_EP_1',0,4
	.word	8638
	.byte	1,29,2,35,0,11
	.byte	'MBLK_LINE_EP_1',0,4
	.word	8638
	.byte	1,28,2,35,0,11
	.byte	'FRAME_END_EP_2',0,4
	.word	8638
	.byte	1,27,2,35,0,11
	.byte	'FILL_EP_2',0,4
	.word	8638
	.byte	1,26,2,35,0,11
	.byte	'WRAP_EP_2',0,4
	.word	8638
	.byte	1,25,2,35,0,11
	.byte	'MBLK_LINE_EP_2',0,4
	.word	8638
	.byte	1,24,2,35,0,11
	.byte	'FRAME_END_EP_3',0,4
	.word	8638
	.byte	1,23,2,35,0,11
	.byte	'FILL_EP_3',0,4
	.word	8638
	.byte	1,22,2,35,0,11
	.byte	'WRAP_EP_3',0,4
	.word	8638
	.byte	1,21,2,35,0,11
	.byte	'MBLK_LINE_EP_3',0,4
	.word	8638
	.byte	1,20,2,35,0,11
	.byte	'FRAME_END_EP_4',0,4
	.word	8638
	.byte	1,19,2,35,0,11
	.byte	'FILL_EP_4',0,4
	.word	8638
	.byte	1,18,2,35,0,11
	.byte	'WRAP_EP_4',0,4
	.word	8638
	.byte	1,17,2,35,0,11
	.byte	'MBLK_LINE_EP_4',0,4
	.word	8638
	.byte	1,16,2,35,0,11
	.byte	'FRAME_END_EP_5',0,4
	.word	8638
	.byte	1,15,2,35,0,11
	.byte	'FILL_EP_5',0,4
	.word	8638
	.byte	1,14,2,35,0,11
	.byte	'WRAP_EP_5',0,4
	.word	8638
	.byte	1,13,2,35,0,11
	.byte	'MBLK_LINE_EP_5',0,4
	.word	8638
	.byte	1,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	8638
	.byte	12,0,2,35,0,0,21
	.byte	'Ifx_CIF_MIEP_RIS_Bits',0,6,183,12,3
	.word	35978
	.byte	10
	.byte	'_Ifx_CIF_MIEP_STA_ERR_Bits',0,6,186,12,16,4,11
	.byte	'reserved_0',0,4
	.word	8638
	.byte	1,31,2,35,0,11
	.byte	'EP_1_IC_SIZE_ERR',0,4
	.word	8638
	.byte	1,30,2,35,0,11
	.byte	'EP_2_IC_SIZE_ERR',0,4
	.word	8638
	.byte	1,29,2,35,0,11
	.byte	'EP_3_IC_SIZE_ERR',0,4
	.word	8638
	.byte	1,28,2,35,0,11
	.byte	'EP_4_IC_SIZE_ERR',0,4
	.word	8638
	.byte	1,27,2,35,0,11
	.byte	'EP_5_IC_SIZE_ERR',0,4
	.word	8638
	.byte	1,26,2,35,0,11
	.byte	'reserved_6',0,4
	.word	8638
	.byte	11,15,2,35,0,11
	.byte	'EP_1_FIFO_FULL',0,4
	.word	8638
	.byte	1,14,2,35,0,11
	.byte	'EP_2_FIFO_FULL',0,4
	.word	8638
	.byte	1,13,2,35,0,11
	.byte	'EP_3_FIFO_FULL',0,4
	.word	8638
	.byte	1,12,2,35,0,11
	.byte	'EP_4_FIFO_FULL',0,4
	.word	8638
	.byte	1,11,2,35,0,11
	.byte	'EP_5_FIFO_FULL',0,4
	.word	8638
	.byte	1,10,2,35,0,11
	.byte	'reserved_22',0,4
	.word	8638
	.byte	10,0,2,35,0,0,21
	.byte	'Ifx_CIF_MIEP_STA_ERR_Bits',0,6,201,12,3
	.word	36532
	.byte	10
	.byte	'_Ifx_CIF_MIEP_STA_ERR_CLR_Bits',0,6,204,12,16,4,11
	.byte	'reserved_0',0,4
	.word	8638
	.byte	1,31,2,35,0,11
	.byte	'EP_1_IC_SIZE_ERR_CLR',0,4
	.word	8638
	.byte	1,30,2,35,0,11
	.byte	'EP_2_IC_SIZE_ERR_CLR',0,4
	.word	8638
	.byte	1,29,2,35,0,11
	.byte	'EP_3_IC_SIZE_ERR_CLR',0,4
	.word	8638
	.byte	1,28,2,35,0,11
	.byte	'EP_4_IC_SIZE_ERR_CLR',0,4
	.word	8638
	.byte	1,27,2,35,0,11
	.byte	'EP_5_IC_SIZE_ERR_CLR',0,4
	.word	8638
	.byte	1,26,2,35,0,11
	.byte	'reserved_6',0,4
	.word	8638
	.byte	26,0,2,35,0,0,21
	.byte	'Ifx_CIF_MIEP_STA_ERR_CLR_Bits',0,6,213,12,3
	.word	36938
	.byte	10
	.byte	'_Ifx_CIF_WD_CTRL_Bits',0,6,216,12,16,4,11
	.byte	'WD_EN',0,4
	.word	8638
	.byte	1,31,2,35,0,11
	.byte	'RST_H_CNT',0,4
	.word	8638
	.byte	1,30,2,35,0,11
	.byte	'RST_V_CNT',0,4
	.word	8638
	.byte	1,29,2,35,0,11
	.byte	'RST_PD_CNT',0,4
	.word	8638
	.byte	1,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	8638
	.byte	12,16,2,35,0,11
	.byte	'WD_PREDIV',0,4
	.word	8638
	.byte	16,0,2,35,0,0,21
	.byte	'Ifx_CIF_WD_CTRL_Bits',0,6,224,12,3
	.word	37219
	.byte	10
	.byte	'_Ifx_CIF_WD_H_TIMEOUT_Bits',0,6,227,12,16,4,11
	.byte	'WD_HSE_TO',0,4
	.word	8638
	.byte	16,16,2,35,0,11
	.byte	'WD_HES_TO',0,4
	.word	8638
	.byte	16,0,2,35,0,0,21
	.byte	'Ifx_CIF_WD_H_TIMEOUT_Bits',0,6,231,12,3
	.word	37402
	.byte	10
	.byte	'_Ifx_CIF_WD_ICR_Bits',0,6,234,12,16,4,11
	.byte	'ICR_WD_HSE_TO',0,4
	.word	8638
	.byte	1,31,2,35,0,11
	.byte	'ICR_WD_HES_TO',0,4
	.word	8638
	.byte	1,30,2,35,0,11
	.byte	'ICR_WD_VSE_TO',0,4
	.word	8638
	.byte	1,29,2,35,0,11
	.byte	'ICR_WD_VES_TO',0,4
	.word	8638
	.byte	1,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	8638
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_CIF_WD_ICR_Bits',0,6,241,12,3
	.word	37513
	.byte	10
	.byte	'_Ifx_CIF_WD_IMSC_Bits',0,6,244,12,16,4,11
	.byte	'IMSC_WD_HSE_TO',0,4
	.word	8638
	.byte	1,31,2,35,0,11
	.byte	'IMSC_WD_HES_TO',0,4
	.word	8638
	.byte	1,30,2,35,0,11
	.byte	'IMSC_WD_VSE_TO',0,4
	.word	8638
	.byte	1,29,2,35,0,11
	.byte	'IMSC_WD_VES_TO',0,4
	.word	8638
	.byte	1,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	8638
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_CIF_WD_IMSC_Bits',0,6,251,12,3
	.word	37692
	.byte	10
	.byte	'_Ifx_CIF_WD_ISR_Bits',0,6,254,12,16,4,11
	.byte	'ISR_WD_HSE_TO',0,4
	.word	8638
	.byte	1,31,2,35,0,11
	.byte	'ISR_WD_HES_TO',0,4
	.word	8638
	.byte	1,30,2,35,0,11
	.byte	'ISR_WD_VSE_TO',0,4
	.word	8638
	.byte	1,29,2,35,0,11
	.byte	'ISR_WD_VES_TO',0,4
	.word	8638
	.byte	1,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	8638
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_CIF_WD_ISR_Bits',0,6,133,13,3
	.word	37877
	.byte	10
	.byte	'_Ifx_CIF_WD_MIS_Bits',0,6,136,13,16,4,11
	.byte	'MIS_WD_HSE_TO',0,4
	.word	8638
	.byte	1,31,2,35,0,11
	.byte	'MIS_WD_HES_TO',0,4
	.word	8638
	.byte	1,30,2,35,0,11
	.byte	'MIS_WD_VSE_TO',0,4
	.word	8638
	.byte	1,29,2,35,0,11
	.byte	'MIS_WD_VES_TO',0,4
	.word	8638
	.byte	1,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	8638
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_CIF_WD_MIS_Bits',0,6,143,13,3
	.word	38056
	.byte	10
	.byte	'_Ifx_CIF_WD_RIS_Bits',0,6,146,13,16,4,11
	.byte	'RIS_WD_HSE_TO',0,4
	.word	8638
	.byte	1,31,2,35,0,11
	.byte	'RIS_WD_HES_TO',0,4
	.word	8638
	.byte	1,30,2,35,0,11
	.byte	'RIS_WD_VSE_TO',0,4
	.word	8638
	.byte	1,29,2,35,0,11
	.byte	'RIS_WD_VES_TO',0,4
	.word	8638
	.byte	1,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	8638
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_CIF_WD_RIS_Bits',0,6,153,13,3
	.word	38235
	.byte	10
	.byte	'_Ifx_CIF_WD_V_TIMEOUT_Bits',0,6,156,13,16,4,11
	.byte	'WD_VSE_TO',0,4
	.word	8638
	.byte	16,16,2,35,0,11
	.byte	'WD_VES_TO',0,4
	.word	8638
	.byte	16,0,2,35,0,0,21
	.byte	'Ifx_CIF_WD_V_TIMEOUT_Bits',0,6,160,13,3
	.word	38414
	.byte	12,6,168,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8654
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_BBB_ACCEN0',0,6,173,13,3
	.word	38525
	.byte	12,6,176,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9219
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_BBB_ACCEN1',0,6,181,13,3
	.word	38593
	.byte	12,6,184,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9304
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_BBB_CLC',0,6,189,13,3
	.word	38661
	.byte	12,6,192,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9415
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_BBB_GPCTL',0,6,197,13,3
	.word	38726
	.byte	12,6,200,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9515
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_BBB_KRST0',0,6,205,13,3
	.word	38793
	.byte	12,6,208,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9632
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_BBB_KRST1',0,6,213,13,3
	.word	38860
	.byte	12,6,216,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9730
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_BBB_KRSTCLR',0,6,221,13,3
	.word	38927
	.byte	12,6,224,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9832
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_BBB_MODID',0,6,229,13,3
	.word	38996
	.byte	12,6,232,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9952
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_CCL',0,6,237,13,3
	.word	39063
	.byte	12,6,240,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10093
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_DP_CTRL',0,6,245,13,3
	.word	39124
	.byte	12,6,248,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10512
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_DP_FLC_STAT',0,6,253,13,3
	.word	39189
	.byte	12,6,128,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10663
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_DP_PDIV_CTRL',0,6,133,14,3
	.word	39258
	.byte	12,6,136,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10752
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_DP_PDIV_STAT',0,6,141,14,3
	.word	39328
	.byte	12,6,144,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10841
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_DP_TSC_STAT',0,6,149,14,3
	.word	39398
	.byte	12,6,152,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10950
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_DP_UDS',0,6,157,14,3
	.word	39467
	.byte	12,6,160,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11045
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_DPCL',0,6,165,14,3
	.word	39531
	.byte	12,6,168,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11211
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_EP_IC_CTRL',0,6,173,14,3
	.word	39593
	.byte	12,6,176,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11315
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_EP_IC_DISPLACE',0,6,181,14,3
	.word	39661
	.byte	12,6,185,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11462
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_EP_IC_H_OFFS',0,6,190,14,3
	.word	39733
	.byte	12,6,194,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11572
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_EP_IC_H_OFFS_SHD',0,6,199,14,3
	.word	39803
	.byte	12,6,202,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11694
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_EP_IC_H_SIZE',0,6,207,14,3
	.word	39877
	.byte	12,6,211,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11804
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_EP_IC_H_SIZE_SHD',0,6,216,14,3
	.word	39947
	.byte	12,6,219,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11926
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_EP_IC_MAX_DX',0,6,224,14,3
	.word	40021
	.byte	12,6,227,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12036
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_EP_IC_MAX_DY',0,6,232,14,3
	.word	40091
	.byte	12,6,235,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12146
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_EP_IC_RECENTER',0,6,240,14,3
	.word	40161
	.byte	12,6,243,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12261
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_EP_IC_V_OFFS',0,6,248,14,3
	.word	40233
	.byte	12,6,252,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12371
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_EP_IC_V_OFFS_SHD',0,6,129,15,3
	.word	40303
	.byte	12,6,132,15,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12493
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_EP_IC_V_SIZE',0,6,137,15,3
	.word	40377
	.byte	12,6,141,15,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12603
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_EP_IC_V_SIZE_SHD',0,6,146,15,3
	.word	40447
	.byte	12,6,149,15,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12725
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_ICCL',0,6,154,15,3
	.word	40521
	.byte	12,6,157,15,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13079
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_ID',0,6,162,15,3
	.word	40583
	.byte	12,6,165,15,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13186
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_IRCL',0,6,170,15,3
	.word	40643
	.byte	12,6,173,15,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13586
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_ISP_ACQ_H_OFFS',0,6,178,15,3
	.word	40705
	.byte	12,6,181,15,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13704
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_ISP_ACQ_H_SIZE',0,6,186,15,3
	.word	40777
	.byte	12,6,189,15,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13822
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_ISP_ACQ_NR_FRAMES',0,6,194,15,3
	.word	40849
	.byte	12,6,197,15,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13949
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_ISP_ACQ_PROP',0,6,202,15,3
	.word	40924
	.byte	12,6,205,15,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14283
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_ISP_ACQ_V_OFFS',0,6,210,15,3
	.word	40994
	.byte	12,6,213,15,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14401
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_ISP_ACQ_V_SIZE',0,6,218,15,3
	.word	41066
	.byte	12,6,221,15,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14519
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_ISP_CTRL',0,6,226,15,3
	.word	41138
	.byte	12,6,229,15,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14823
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_ISP_ERR',0,6,234,15,3
	.word	41204
	.byte	12,6,237,15,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14982
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_ISP_ERR_CLR',0,6,242,15,3
	.word	41269
	.byte	12,6,245,15,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15161
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_ISP_FLAGS_SHD',0,6,250,15,3
	.word	41338
	.byte	12,6,253,15,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15393
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_ISP_FRAME_COUNT',0,6,130,16,3
	.word	41409
	.byte	12,6,133,16,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15516
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_ISP_ICR',0,6,138,16,3
	.word	41482
	.byte	12,6,141,16,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15832
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_ISP_IMSC',0,6,146,16,3
	.word	41547
	.byte	12,6,149,16,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16158
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_ISP_ISR',0,6,154,16,3
	.word	41613
	.byte	12,6,157,16,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16474
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_ISP_MIS',0,6,162,16,3
	.word	41678
	.byte	12,6,165,16,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16790
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_ISP_OUT_H_OFFS',0,6,170,16,3
	.word	41743
	.byte	12,6,173,16,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16912
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_ISP_OUT_H_OFFS_SHD',0,6,178,16,3
	.word	41815
	.byte	12,6,181,16,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17046
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_ISP_OUT_H_SIZE',0,6,186,16,3
	.word	41891
	.byte	12,6,189,16,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17168
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_ISP_OUT_H_SIZE_SHD',0,6,194,16,3
	.word	41963
	.byte	12,6,197,16,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17302
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_ISP_OUT_V_OFFS',0,6,202,16,3
	.word	42039
	.byte	12,6,205,16,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17424
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_ISP_OUT_V_OFFS_SHD',0,6,210,16,3
	.word	42111
	.byte	12,6,213,16,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17558
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_ISP_OUT_V_SIZE',0,6,218,16,3
	.word	42187
	.byte	12,6,221,16,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17680
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_ISP_OUT_V_SIZE_SHD',0,6,226,16,3
	.word	42259
	.byte	12,6,229,16,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17814
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_ISP_RIS',0,6,234,16,3
	.word	42335
	.byte	12,6,237,16,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18130
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_ISPIS_CTRL',0,6,242,16,3
	.word	42400
	.byte	12,6,245,16,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18234
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_ISPIS_DISPLACE',0,6,250,16,3
	.word	42468
	.byte	12,6,253,16,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18381
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_ISPIS_H_OFFS',0,6,130,17,3
	.word	42540
	.byte	12,6,133,17,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18494
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_ISPIS_H_OFFS_SHD',0,6,138,17,3
	.word	42610
	.byte	12,6,141,17,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18619
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_ISPIS_H_SIZE',0,6,146,17,3
	.word	42684
	.byte	12,6,149,17,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18732
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_ISPIS_H_SIZE_SHD',0,6,154,17,3
	.word	42754
	.byte	12,6,157,17,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18858
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_ISPIS_MAX_DX',0,6,162,17,3
	.word	42828
	.byte	12,6,165,17,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18971
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_ISPIS_MAX_DY',0,6,170,17,3
	.word	42898
	.byte	12,6,173,17,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19084
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_ISPIS_RECENTER',0,6,178,17,3
	.word	42968
	.byte	12,6,181,17,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19199
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_ISPIS_V_OFFS',0,6,186,17,3
	.word	43040
	.byte	12,6,189,17,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19312
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_ISPIS_V_OFFS_SHD',0,6,194,17,3
	.word	43110
	.byte	12,6,197,17,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19437
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_ISPIS_V_SIZE',0,6,202,17,3
	.word	43184
	.byte	12,6,205,17,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19550
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_ISPIS_V_SIZE_SHD',0,6,210,17,3
	.word	43254
	.byte	12,6,213,17,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19676
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_JPE_AC_TABLE_SELECT',0,6,218,17,3
	.word	43328
	.byte	12,6,221,17,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19808
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_JPE_CBCR_SCALE_EN',0,6,226,17,3
	.word	43405
	.byte	12,6,229,17,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19934
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_JPE_DC_TABLE_SELECT',0,6,234,17,3
	.word	43480
	.byte	12,6,237,17,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20066
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_JPE_DEBUG',0,6,242,17,3
	.word	43557
	.byte	12,6,245,17,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20348
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_JPE_ENC_HSIZE',0,6,250,17,3
	.word	43624
	.byte	12,6,253,17,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20463
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_JPE_ENC_VSIZE',0,6,130,18,3
	.word	43695
	.byte	12,6,133,18,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20578
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_JPE_ENCODE',0,6,138,18,3
	.word	43766
	.byte	12,6,141,18,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20726
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_JPE_ENCODE_MODE',0,6,146,18,3
	.word	43834
	.byte	12,6,149,18,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20846
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_JPE_ENCODER_BUSY',0,6,154,18,3
	.word	43907
	.byte	12,6,157,18,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20967
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_JPE_ERROR_ICR',0,6,162,18,3
	.word	43981
	.byte	12,6,165,18,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21225
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_JPE_ERROR_IMR',0,6,170,18,3
	.word	44052
	.byte	12,6,173,18,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21483
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_JPE_ERROR_ISR',0,6,178,18,3
	.word	44123
	.byte	12,6,181,18,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21741
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_JPE_ERROR_MIS',0,6,186,18,3
	.word	44194
	.byte	12,6,189,18,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21999
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_JPE_ERROR_RIS',0,6,194,18,3
	.word	44265
	.byte	12,6,197,18,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22257
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_JPE_GEN_HEADER',0,6,202,18,3
	.word	44336
	.byte	12,6,205,18,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22374
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_JPE_HEADER_MODE',0,6,210,18,3
	.word	44408
	.byte	12,6,213,18,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22494
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_JPE_INIT',0,6,218,18,3
	.word	44481
	.byte	12,6,221,18,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22596
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_JPE_PIC_FORMAT',0,6,226,18,3
	.word	44547
	.byte	12,6,229,18,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22717
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_JPE_RESTART_INTERVAL',0,6,234,18,3
	.word	44619
	.byte	12,6,237,18,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22853
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_JPE_STATUS_ICR',0,6,242,18,3
	.word	44697
	.byte	12,6,245,18,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23020
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_JPE_STATUS_IMR',0,6,250,18,3
	.word	44769
	.byte	12,6,253,18,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23187
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_JPE_STATUS_ISR',0,6,130,19,3
	.word	44841
	.byte	12,6,133,19,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23354
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_JPE_STATUS_MIS',0,6,138,19,3
	.word	44913
	.byte	12,6,141,19,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23521
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_JPE_STATUS_RIS',0,6,146,19,3
	.word	44985
	.byte	12,6,149,19,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23688
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_JPE_TABLE_DATA',0,6,154,19,3
	.word	45057
	.byte	12,6,157,19,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23834
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_JPE_TABLE_FLUSH',0,6,162,19,3
	.word	45129
	.byte	12,6,165,19,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23954
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_JPE_TABLE_ID',0,6,170,19,3
	.word	45202
	.byte	12,6,173,19,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24065
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_JPE_TAC0_LEN',0,6,178,19,3
	.word	45272
	.byte	12,6,181,19,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24176
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_JPE_TAC1_LEN',0,6,186,19,3
	.word	45342
	.byte	12,6,189,19,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24287
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_JPE_TDC0_LEN',0,6,194,19,3
	.word	45412
	.byte	12,6,197,19,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24398
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_JPE_TDC1_LEN',0,6,202,19,3
	.word	45482
	.byte	12,6,205,19,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24509
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_JPE_TQ_U_SELECT',0,6,210,19,3
	.word	45552
	.byte	12,6,213,19,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24628
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_JPE_TQ_V_SELECT',0,6,218,19,3
	.word	45625
	.byte	12,6,221,19,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24747
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_JPE_TQ_Y_SELECT',0,6,226,19,3
	.word	45698
	.byte	12,6,229,19,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24866
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_JPE_Y_SCALE_EN',0,6,234,19,3
	.word	45771
	.byte	12,6,237,19,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24983
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_LDS_CTRL',0,6,242,19,3
	.word	45843
	.byte	12,6,245,19,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25195
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_LDS_FAC',0,6,250,19,3
	.word	45909
	.byte	12,6,253,19,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25341
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_MI_BYTE_CNT',0,6,130,20,3
	.word	45974
	.byte	12,6,133,20,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25451
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_MI_CTRL',0,6,138,20,3
	.word	46043
	.byte	12,6,141,20,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25861
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_MI_CTRL_SHD',0,6,146,20,3
	.word	46108
	.byte	12,6,149,20,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26171
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_MI_ICR',0,6,154,20,3
	.word	46177
	.byte	12,6,157,20,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26446
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_MI_IMSC',0,6,162,20,3
	.word	46241
	.byte	12,6,166,20,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26724
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_MI_INIT',0,6,171,20,3
	.word	46306
	.byte	12,6,174,20,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26890
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_MI_ISR',0,6,179,20,3
	.word	46371
	.byte	12,6,182,20,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27166
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_MI_MIS',0,6,187,20,3
	.word	46435
	.byte	12,6,191,20,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27442
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_MI_MP_CB_BASE_AD_INIT',0,6,196,20,3
	.word	46499
	.byte	12,6,200,20,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27581
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_MI_MP_CB_BASE_AD_SHD',0,6,205,20,3
	.word	46578
	.byte	12,6,209,20,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27714
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_MI_MP_CB_OFFS_CNT_INIT',0,6,214,20,3
	.word	46656
	.byte	12,6,218,20,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27880
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_MI_MP_CB_OFFS_CNT_SHD',0,6,223,20,3
	.word	46736
	.byte	12,6,227,20,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28039
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_MI_MP_CB_OFFS_CNT_START',0,6,232,20,3
	.word	46815
	.byte	12,6,236,20,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28208
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_MI_MP_CB_SIZE_INIT',0,6,241,20,3
	.word	46896
	.byte	12,6,245,20,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28362
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_MI_MP_CB_SIZE_SHD',0,6,250,20,3
	.word	46972
	.byte	12,6,254,20,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28509
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_MI_MP_CR_BASE_AD_INIT',0,6,131,21,3
	.word	47047
	.byte	12,6,135,21,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28649
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_MI_MP_CR_BASE_AD_SHD',0,6,140,21,3
	.word	47126
	.byte	12,6,144,21,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28782
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_MI_MP_CR_OFFS_CNT_INIT',0,6,149,21,3
	.word	47204
	.byte	12,6,153,21,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28948
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_MI_MP_CR_OFFS_CNT_SHD',0,6,158,21,3
	.word	47284
	.byte	12,6,162,21,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29107
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_MI_MP_CR_OFFS_CNT_START',0,6,167,21,3
	.word	47363
	.byte	12,6,171,21,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29276
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_MI_MP_CR_SIZE_INIT',0,6,176,21,3
	.word	47444
	.byte	12,6,180,21,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29430
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_MI_MP_CR_SIZE_SHD',0,6,185,21,3
	.word	47520
	.byte	12,6,189,21,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29577
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_MI_MP_Y_BASE_AD_INIT',0,6,194,21,3
	.word	47595
	.byte	12,6,198,21,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29714
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_MI_MP_Y_BASE_AD_SHD',0,6,203,21,3
	.word	47673
	.byte	12,6,207,21,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29844
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_MI_MP_Y_IRQ_OFFS_INIT',0,6,212,21,3
	.word	47750
	.byte	12,6,216,21,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30007
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_MI_MP_Y_IRQ_OFFS_SHD',0,6,221,21,3
	.word	47829
	.byte	12,6,225,21,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30163
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_MI_MP_Y_OFFS_CNT_INIT',0,6,230,21,3
	.word	47907
	.byte	12,6,234,21,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30326
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_MI_MP_Y_OFFS_CNT_SHD',0,6,239,21,3
	.word	47986
	.byte	12,6,243,21,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30482
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_MI_MP_Y_OFFS_CNT_START',0,6,248,21,3
	.word	48064
	.byte	12,6,252,21,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30648
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_MI_MP_Y_SIZE_INIT',0,6,129,22,3
	.word	48144
	.byte	12,6,133,22,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30799
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_MI_MP_Y_SIZE_SHD',0,6,138,22,3
	.word	48219
	.byte	12,6,141,22,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30943
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_MI_RIS',0,6,146,22,3
	.word	48293
	.byte	12,6,149,22,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31219
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_MI_STATUS',0,6,154,22,3
	.word	48357
	.byte	12,6,157,22,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31433
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_MI_STATUS_CLR',0,6,162,22,3
	.word	48424
	.byte	12,6,165,22,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31808
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_MIEP_CH_BASE_AD_INIT',0,6,170,22,3
	.word	48495
	.byte	12,6,174,22,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31943
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_MIEP_CH_BASE_AD_SHD',0,6,179,22,3
	.word	48573
	.byte	12,6,182,22,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32071
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_MIEP_CH_CTRL',0,6,187,22,3
	.word	48650
	.byte	12,6,190,22,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32326
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_MIEP_CH_CTRL_SHD',0,6,195,22,3
	.word	48720
	.byte	12,6,199,22,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32497
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_MIEP_CH_INIT',0,6,204,22,3
	.word	48794
	.byte	12,6,208,22,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32679
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_MIEP_CH_IRQ_OFFS_INIT',0,6,213,22,3
	.word	48864
	.byte	12,6,217,22,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32840
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_MIEP_CH_IRQ_OFFS_SHD',0,6,222,22,3
	.word	48943
	.byte	12,6,226,22,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32994
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_MIEP_CH_OFFS_CNT_INIT',0,6,231,22,3
	.word	49021
	.byte	12,6,235,22,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33155
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_MIEP_CH_OFFS_CNT_SHD',0,6,240,22,3
	.word	49100
	.byte	12,6,243,22,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33309
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_MIEP_CH_OFFS_CNT_START',0,6,248,22,3
	.word	49178
	.byte	12,6,251,22,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33473
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_MIEP_CH_SIZE_INIT',0,6,128,23,3
	.word	49258
	.byte	12,6,131,23,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33622
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_MIEP_CH_SIZE_SHD',0,6,136,23,3
	.word	49333
	.byte	12,6,139,23,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33764
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_MIEP_ICR',0,6,144,23,3
	.word	49407
	.byte	12,6,148,23,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34314
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_MIEP_IMSC',0,6,153,23,3
	.word	49473
	.byte	12,6,156,23,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34870
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_MIEP_ISR',0,6,161,23,3
	.word	49540
	.byte	12,6,164,23,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35424
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_MIEP_MIS',0,6,169,23,3
	.word	49606
	.byte	12,6,172,23,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35978
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_MIEP_RIS',0,6,177,23,3
	.word	49672
	.byte	12,6,180,23,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36532
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_MIEP_STA_ERR',0,6,185,23,3
	.word	49738
	.byte	12,6,188,23,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36938
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_MIEP_STA_ERR_CLR',0,6,193,23,3
	.word	49808
	.byte	12,6,196,23,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37219
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_WD_CTRL',0,6,201,23,3
	.word	49882
	.byte	12,6,204,23,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37402
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_WD_H_TIMEOUT',0,6,209,23,3
	.word	49947
	.byte	12,6,212,23,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37513
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_WD_ICR',0,6,217,23,3
	.word	50017
	.byte	12,6,220,23,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37692
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_WD_IMSC',0,6,225,23,3
	.word	50081
	.byte	12,6,228,23,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37877
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_WD_ISR',0,6,233,23,3
	.word	50146
	.byte	12,6,236,23,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38056
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_WD_MIS',0,6,241,23,3
	.word	50210
	.byte	12,6,244,23,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38235
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_WD_RIS',0,6,249,23,3
	.word	50274
	.byte	12,6,252,23,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38414
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CIF_WD_V_TIMEOUT',0,6,129,24,3
	.word	50338
	.byte	14,208,1
	.word	492
	.byte	15,207,1,0,10
	.byte	'_Ifx_CIF_MIEP_CH',0,6,140,24,25,128,2,13
	.byte	'CTRL',0
	.word	48650
	.byte	4,2,35,0,13
	.byte	'INIT',0
	.word	48794
	.byte	4,2,35,4,13
	.byte	'BASE_AD_INIT',0
	.word	48495
	.byte	4,2,35,8,13
	.byte	'SIZE_INIT',0
	.word	49258
	.byte	4,2,35,12,13
	.byte	'OFFS_CNT_INIT',0
	.word	49021
	.byte	4,2,35,16,13
	.byte	'OFFS_CNT_START',0
	.word	49178
	.byte	4,2,35,20,13
	.byte	'IRQ_OFFS_INIT',0
	.word	48864
	.byte	4,2,35,24,13
	.byte	'CTRL_SHD',0
	.word	48720
	.byte	4,2,35,28,13
	.byte	'BASE_AD_SHD',0
	.word	48573
	.byte	4,2,35,32,13
	.byte	'SIZE_SHD',0
	.word	49333
	.byte	4,2,35,36,13
	.byte	'OFFS_CNT_SHD',0
	.word	49100
	.byte	4,2,35,40,13
	.byte	'IRQ_OFFS_SHD',0
	.word	48943
	.byte	4,2,35,44,13
	.byte	'reserved_30',0
	.word	50408
	.byte	208,1,2,35,48,0,16
	.word	50419
	.byte	21
	.byte	'Ifx_CIF_MIEP_CH',0,6,155,24,3
	.word	50706
	.byte	10
	.byte	'_Ifx_CIF_BBB',0,6,168,24,25,32,13
	.byte	'CLC',0
	.word	38661
	.byte	4,2,35,0,13
	.byte	'MODID',0
	.word	38996
	.byte	4,2,35,4,13
	.byte	'GPCTL',0
	.word	38726
	.byte	4,2,35,8,13
	.byte	'ACCEN0',0
	.word	38525
	.byte	4,2,35,12,13
	.byte	'ACCEN1',0
	.word	38593
	.byte	4,2,35,16,13
	.byte	'KRST0',0
	.word	38793
	.byte	4,2,35,20,13
	.byte	'KRST1',0
	.word	38860
	.byte	4,2,35,24,13
	.byte	'KRSTCLR',0
	.word	38927
	.byte	4,2,35,28,0,16
	.word	50736
	.byte	21
	.byte	'Ifx_CIF_BBB',0,6,178,24,3
	.word	50878
	.byte	14,32
	.word	39467
	.byte	15,7,0,10
	.byte	'_Ifx_CIF_DP',0,6,181,24,25,52,13
	.byte	'CTRL',0
	.word	39124
	.byte	4,2,35,0,13
	.byte	'PDIV_CTRL',0
	.word	39258
	.byte	4,2,35,4,13
	.byte	'FLC_STAT',0
	.word	39189
	.byte	4,2,35,8,13
	.byte	'PDIV_STAT',0
	.word	39328
	.byte	4,2,35,12,13
	.byte	'TSC_STAT',0
	.word	39398
	.byte	4,2,35,16,13
	.byte	'UDS_1S',0
	.word	50904
	.byte	32,2,35,20,0,16
	.word	50913
	.byte	21
	.byte	'Ifx_CIF_DP',0,6,189,24,3
	.word	51036
	.byte	14,204,1
	.word	492
	.byte	15,203,1,0,10
	.byte	'_Ifx_CIF_EP_IC',0,6,192,24,25,128,2,13
	.byte	'CTRL',0
	.word	39593
	.byte	4,2,35,0,13
	.byte	'RECENTER',0
	.word	40161
	.byte	4,2,35,4,13
	.byte	'H_OFFS',0
	.word	39733
	.byte	4,2,35,8,13
	.byte	'V_OFFS',0
	.word	40233
	.byte	4,2,35,12,13
	.byte	'H_SIZE',0
	.word	39877
	.byte	4,2,35,16,13
	.byte	'V_SIZE',0
	.word	40377
	.byte	4,2,35,20,13
	.byte	'MAX_DX',0
	.word	40021
	.byte	4,2,35,24,13
	.byte	'MAX_DY',0
	.word	40091
	.byte	4,2,35,28,13
	.byte	'DISPLACE',0
	.word	39661
	.byte	4,2,35,32,13
	.byte	'H_OFFS_SHD',0
	.word	39803
	.byte	4,2,35,36,13
	.byte	'V_OFFS_SHD',0
	.word	40303
	.byte	4,2,35,40,13
	.byte	'H_SIZE_SHD',0
	.word	39947
	.byte	4,2,35,44,13
	.byte	'V_SIZE_SHD',0
	.word	40447
	.byte	4,2,35,48,13
	.byte	'reserved_34',0
	.word	51061
	.byte	204,1,2,35,52,0,16
	.word	51072
	.byte	21
	.byte	'Ifx_CIF_EP_IC',0,6,208,24,3
	.word	51343
	.byte	14,248,2
	.word	492
	.byte	15,247,2,0,14,108
	.word	492
	.byte	15,107,0,10
	.byte	'_Ifx_CIF_ISP',0,6,211,24,25,200,4,13
	.byte	'CTRL',0
	.word	41138
	.byte	4,2,35,0,13
	.byte	'ACQ_PROP',0
	.word	40924
	.byte	4,2,35,4,13
	.byte	'ACQ_H_OFFS',0
	.word	40705
	.byte	4,2,35,8,13
	.byte	'ACQ_V_OFFS',0
	.word	40994
	.byte	4,2,35,12,13
	.byte	'ACQ_H_SIZE',0
	.word	40777
	.byte	4,2,35,16,13
	.byte	'ACQ_V_SIZE',0
	.word	41066
	.byte	4,2,35,20,13
	.byte	'ACQ_NR_FRAMES',0
	.word	40849
	.byte	4,2,35,24,13
	.byte	'reserved_1C',0
	.word	51371
	.byte	248,2,2,35,28,13
	.byte	'OUT_H_OFFS',0
	.word	41743
	.byte	4,3,35,148,3,13
	.byte	'OUT_V_OFFS',0
	.word	42039
	.byte	4,3,35,152,3,13
	.byte	'OUT_H_SIZE',0
	.word	41891
	.byte	4,3,35,156,3,13
	.byte	'OUT_V_SIZE',0
	.word	42187
	.byte	4,3,35,160,3,13
	.byte	'reserved_1A4',0
	.word	1537
	.byte	4,3,35,164,3,13
	.byte	'FLAGS_SHD',0
	.word	41338
	.byte	4,3,35,168,3,13
	.byte	'OUT_H_OFFS_SHD',0
	.word	41815
	.byte	4,3,35,172,3,13
	.byte	'OUT_V_OFFS_SHD',0
	.word	42111
	.byte	4,3,35,176,3,13
	.byte	'OUT_H_SIZE_SHD',0
	.word	41963
	.byte	4,3,35,180,3,13
	.byte	'OUT_V_SIZE_SHD',0
	.word	42259
	.byte	4,3,35,184,3,13
	.byte	'IMSC',0
	.word	41547
	.byte	4,3,35,188,3,13
	.byte	'RIS',0
	.word	42335
	.byte	4,3,35,192,3,13
	.byte	'MIS',0
	.word	41678
	.byte	4,3,35,196,3,13
	.byte	'ICR',0
	.word	41482
	.byte	4,3,35,200,3,13
	.byte	'ISR',0
	.word	41613
	.byte	4,3,35,204,3,13
	.byte	'reserved_1D0',0
	.word	51382
	.byte	108,3,35,208,3,13
	.byte	'ERR',0
	.word	41204
	.byte	4,3,35,188,4,13
	.byte	'ERR_CLR',0
	.word	41269
	.byte	4,3,35,192,4,13
	.byte	'FRAME_COUNT',0
	.word	41409
	.byte	4,3,35,196,4,0,16
	.word	51391
	.byte	21
	.byte	'Ifx_CIF_ISP',0,6,240,24,3
	.word	51944
	.byte	10
	.byte	'_Ifx_CIF_ISPIS',0,6,243,24,25,52,13
	.byte	'CTRL',0
	.word	42400
	.byte	4,2,35,0,13
	.byte	'RECENTER',0
	.word	42968
	.byte	4,2,35,4,13
	.byte	'H_OFFS',0
	.word	42540
	.byte	4,2,35,8,13
	.byte	'V_OFFS',0
	.word	43040
	.byte	4,2,35,12,13
	.byte	'H_SIZE',0
	.word	42684
	.byte	4,2,35,16,13
	.byte	'V_SIZE',0
	.word	43184
	.byte	4,2,35,20,13
	.byte	'MAX_DX',0
	.word	42828
	.byte	4,2,35,24,13
	.byte	'MAX_DY',0
	.word	42898
	.byte	4,2,35,28,13
	.byte	'DISPLACE',0
	.word	42468
	.byte	4,2,35,32,13
	.byte	'H_OFFS_SHD',0
	.word	42610
	.byte	4,2,35,36,13
	.byte	'V_OFFS_SHD',0
	.word	43110
	.byte	4,2,35,40,13
	.byte	'H_SIZE_SHD',0
	.word	42754
	.byte	4,2,35,44,13
	.byte	'V_SIZE_SHD',0
	.word	43254
	.byte	4,2,35,48,0,16
	.word	51970
	.byte	21
	.byte	'Ifx_CIF_ISPIS',0,6,130,25,3
	.word	52218
	.byte	10
	.byte	'_Ifx_CIF_JPE',0,6,133,25,25,144,1,13
	.byte	'GEN_HEADER',0
	.word	44336
	.byte	4,2,35,0,13
	.byte	'ENCODE',0
	.word	43766
	.byte	4,2,35,4,13
	.byte	'INIT',0
	.word	44481
	.byte	4,2,35,8,13
	.byte	'Y_SCALE_EN',0
	.word	45771
	.byte	4,2,35,12,13
	.byte	'CBCR_SCALE_EN',0
	.word	43405
	.byte	4,2,35,16,13
	.byte	'TABLE_FLUSH',0
	.word	45129
	.byte	4,2,35,20,13
	.byte	'ENC_HSIZE',0
	.word	43624
	.byte	4,2,35,24,13
	.byte	'ENC_VSIZE',0
	.word	43695
	.byte	4,2,35,28,13
	.byte	'PIC_FORMAT',0
	.word	44547
	.byte	4,2,35,32,13
	.byte	'RESTART_INTERVAL',0
	.word	44619
	.byte	4,2,35,36,13
	.byte	'TQ_Y_SELECT',0
	.word	45698
	.byte	4,2,35,40,13
	.byte	'TQ_U_SELECT',0
	.word	45552
	.byte	4,2,35,44,13
	.byte	'TQ_V_SELECT',0
	.word	45625
	.byte	4,2,35,48,13
	.byte	'DC_TABLE_SELECT',0
	.word	43480
	.byte	4,2,35,52,13
	.byte	'AC_TABLE_SELECT',0
	.word	43328
	.byte	4,2,35,56,13
	.byte	'TABLE_DATA',0
	.word	45057
	.byte	4,2,35,60,13
	.byte	'TABLE_ID',0
	.word	45202
	.byte	4,2,35,64,13
	.byte	'TAC0_LEN',0
	.word	45272
	.byte	4,2,35,68,13
	.byte	'TDC0_LEN',0
	.word	45412
	.byte	4,2,35,72,13
	.byte	'TAC1_LEN',0
	.word	45342
	.byte	4,2,35,76,13
	.byte	'TDC1_LEN',0
	.word	45482
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	1537
	.byte	4,2,35,84,13
	.byte	'ENCODER_BUSY',0
	.word	43907
	.byte	4,2,35,88,13
	.byte	'HEADER_MODE',0
	.word	44408
	.byte	4,2,35,92,13
	.byte	'ENCODE_MODE',0
	.word	43834
	.byte	4,2,35,96,13
	.byte	'DEBUG',0
	.word	43557
	.byte	4,2,35,100,13
	.byte	'ERROR_IMR',0
	.word	44052
	.byte	4,2,35,104,13
	.byte	'ERROR_RIS',0
	.word	44265
	.byte	4,2,35,108,13
	.byte	'ERROR_MIS',0
	.word	44194
	.byte	4,2,35,112,13
	.byte	'ERROR_ICR',0
	.word	43981
	.byte	4,2,35,116,13
	.byte	'ERROR_ISR',0
	.word	44123
	.byte	4,2,35,120,13
	.byte	'STATUS_IMR',0
	.word	44769
	.byte	4,2,35,124,13
	.byte	'STATUS_RIS',0
	.word	44985
	.byte	4,3,35,128,1,13
	.byte	'STATUS_MIS',0
	.word	44913
	.byte	4,3,35,132,1,13
	.byte	'STATUS_ICR',0
	.word	44697
	.byte	4,3,35,136,1,13
	.byte	'STATUS_ISR',0
	.word	44841
	.byte	4,3,35,140,1,0,16
	.word	52246
	.byte	21
	.byte	'Ifx_CIF_JPE',0,6,171,25,3
	.word	52987
	.byte	10
	.byte	'_Ifx_CIF_LDS',0,6,174,25,25,8,13
	.byte	'CTRL',0
	.word	45843
	.byte	4,2,35,0,13
	.byte	'FAC',0
	.word	45909
	.byte	4,2,35,4,0,16
	.word	53013
	.byte	21
	.byte	'Ifx_CIF_LDS',0,6,178,25,3
	.word	53060
	.byte	14,52
	.word	492
	.byte	15,51,0,14,88
	.word	492
	.byte	15,87,0,10
	.byte	'_Ifx_CIF_MI',0,6,181,25,25,148,2,13
	.byte	'CTRL',0
	.word	46043
	.byte	4,2,35,0,13
	.byte	'INIT',0
	.word	46306
	.byte	4,2,35,4,13
	.byte	'MP_Y_BASE_AD_INIT',0
	.word	47595
	.byte	4,2,35,8,13
	.byte	'MP_Y_SIZE_INIT',0
	.word	48144
	.byte	4,2,35,12,13
	.byte	'MP_Y_OFFS_CNT_INIT',0
	.word	47907
	.byte	4,2,35,16,13
	.byte	'MP_Y_OFFS_CNT_START',0
	.word	48064
	.byte	4,2,35,20,13
	.byte	'MP_Y_IRQ_OFFS_INIT',0
	.word	47750
	.byte	4,2,35,24,13
	.byte	'MP_CB_BASE_AD_INIT',0
	.word	46499
	.byte	4,2,35,28,13
	.byte	'MP_CB_SIZE_INIT',0
	.word	46896
	.byte	4,2,35,32,13
	.byte	'MP_CB_OFFS_CNT_INIT',0
	.word	46656
	.byte	4,2,35,36,13
	.byte	'MP_CB_OFFS_CNT_START',0
	.word	46815
	.byte	4,2,35,40,13
	.byte	'MP_CR_BASE_AD_INIT',0
	.word	47047
	.byte	4,2,35,44,13
	.byte	'MP_CR_SIZE_INIT',0
	.word	47444
	.byte	4,2,35,48,13
	.byte	'MP_CR_OFFS_CNT_INIT',0
	.word	47204
	.byte	4,2,35,52,13
	.byte	'MP_CR_OFFS_CNT_START',0
	.word	47363
	.byte	4,2,35,56,13
	.byte	'reserved_3C',0
	.word	53086
	.byte	52,2,35,60,13
	.byte	'BYTE_CNT',0
	.word	45974
	.byte	4,2,35,112,13
	.byte	'CTRL_SHD',0
	.word	46108
	.byte	4,2,35,116,13
	.byte	'MP_Y_BASE_AD_SHD',0
	.word	47673
	.byte	4,2,35,120,13
	.byte	'MP_Y_SIZE_SHD',0
	.word	48219
	.byte	4,2,35,124,13
	.byte	'MP_Y_OFFS_CNT_SHD',0
	.word	47986
	.byte	4,3,35,128,1,13
	.byte	'MP_Y_IRQ_OFFS_SHD',0
	.word	47829
	.byte	4,3,35,132,1,13
	.byte	'MP_CB_BASE_AD_SHD',0
	.word	46578
	.byte	4,3,35,136,1,13
	.byte	'MP_CB_SIZE_SHD',0
	.word	46972
	.byte	4,3,35,140,1,13
	.byte	'MP_CB_OFFS_CNT_SHD',0
	.word	46736
	.byte	4,3,35,144,1,13
	.byte	'MP_CR_BASE_AD_SHD',0
	.word	47126
	.byte	4,3,35,148,1,13
	.byte	'MP_CR_SIZE_SHD',0
	.word	47520
	.byte	4,3,35,152,1,13
	.byte	'MP_CR_OFFS_CNT_SHD',0
	.word	47284
	.byte	4,3,35,156,1,13
	.byte	'reserved_A0',0
	.word	53095
	.byte	88,3,35,160,1,13
	.byte	'IMSC',0
	.word	46241
	.byte	4,3,35,248,1,13
	.byte	'RIS',0
	.word	48293
	.byte	4,3,35,252,1,13
	.byte	'MIS',0
	.word	46435
	.byte	4,3,35,128,2,13
	.byte	'ICR',0
	.word	46177
	.byte	4,3,35,132,2,13
	.byte	'ISR',0
	.word	46371
	.byte	4,3,35,136,2,13
	.byte	'STATUS',0
	.word	48357
	.byte	4,3,35,140,2,13
	.byte	'STATUS_CLR',0
	.word	48424
	.byte	4,3,35,144,2,0,16
	.word	53104
	.byte	21
	.byte	'Ifx_CIF_MI',0,6,219,25,3
	.word	53969
	.byte	14,228,1
	.word	492
	.byte	15,227,1,0,14,128,10
	.word	50419
	.byte	15,4,0,16
	.word	54005
	.byte	10
	.byte	'_Ifx_CIF_MIEP',0,6,222,25,25,128,12,13
	.byte	'STA_ERR',0
	.word	49738
	.byte	4,2,35,0,13
	.byte	'STA_ERR_CLR',0
	.word	49808
	.byte	4,2,35,4,13
	.byte	'IMSC',0
	.word	49473
	.byte	4,2,35,8,13
	.byte	'RIS',0
	.word	49672
	.byte	4,2,35,12,13
	.byte	'MIS',0
	.word	49606
	.byte	4,2,35,16,13
	.byte	'ICR',0
	.word	49407
	.byte	4,2,35,20,13
	.byte	'ISR',0
	.word	49540
	.byte	4,2,35,24,13
	.byte	'reserved_1C',0
	.word	53994
	.byte	228,1,2,35,28,13
	.byte	'CH_1S',0
	.word	54015
	.byte	128,10,3,35,128,2,0,16
	.word	54020
	.byte	21
	.byte	'Ifx_CIF_MIEP',0,6,233,25,3
	.word	54185
	.byte	10
	.byte	'_Ifx_CIF_WD',0,6,236,25,25,32,13
	.byte	'CTRL',0
	.word	49882
	.byte	4,2,35,0,13
	.byte	'V_TIMEOUT',0
	.word	50338
	.byte	4,2,35,4,13
	.byte	'H_TIMEOUT',0
	.word	49947
	.byte	4,2,35,8,13
	.byte	'IMSC',0
	.word	50081
	.byte	4,2,35,12,13
	.byte	'RIS',0
	.word	50274
	.byte	4,2,35,16,13
	.byte	'MIS',0
	.word	50210
	.byte	4,2,35,20,13
	.byte	'ICR',0
	.word	50017
	.byte	4,2,35,24,13
	.byte	'ISR',0
	.word	50146
	.byte	4,2,35,28,0,16
	.word	54212
	.byte	21
	.byte	'Ifx_CIF_WD',0,6,246,25,3
	.word	54349
	.byte	16
	.word	50736
	.byte	14,224,1
	.word	492
	.byte	15,223,1,0,14,228,7
	.word	492
	.byte	15,227,7,0,16
	.word	51391
	.byte	14,184,27
	.word	492
	.byte	15,183,27,0,16
	.word	53104
	.byte	14,236,5
	.word	492
	.byte	15,235,5,0,16
	.word	52246
	.byte	14,240,20
	.word	492
	.byte	15,239,20,0,16
	.word	51970
	.byte	16
	.word	54212
	.byte	16
	.word	53013
	.byte	14,248,3
	.word	492
	.byte	15,247,3,0,16
	.word	50913
	.byte	14,204,3
	.word	492
	.byte	15,203,3,0,14,128,10
	.word	51072
	.byte	15,4,0,16
	.word	54491
	.byte	14,128,12
	.word	492
	.byte	15,255,11,0,16
	.word	54020
	.byte	14,1
	.word	492
	.byte	15,0,0,10
	.byte	'_Ifx_CIF',0,6,131,26,25,132,118,13
	.byte	'BBB',0
	.word	54374
	.byte	32,2,35,0,13
	.byte	'reserved_20',0
	.word	54379
	.byte	224,1,2,35,32,13
	.byte	'CCL',0
	.word	39063
	.byte	4,3,35,128,2,13
	.byte	'reserved_104',0
	.word	1537
	.byte	4,3,35,132,2,13
	.byte	'ID',0
	.word	40583
	.byte	4,3,35,136,2,13
	.byte	'reserved_10C',0
	.word	1537
	.byte	4,3,35,140,2,13
	.byte	'ICCL',0
	.word	40521
	.byte	4,3,35,144,2,13
	.byte	'IRCL',0
	.word	40643
	.byte	4,3,35,148,2,13
	.byte	'DPCL',0
	.word	39531
	.byte	4,3,35,152,2,13
	.byte	'reserved_11C',0
	.word	54390
	.byte	228,7,3,35,156,2,13
	.byte	'ISP',0
	.word	54401
	.byte	200,4,3,35,128,10,13
	.byte	'reserved_748',0
	.word	54406
	.byte	184,27,3,35,200,14,13
	.byte	'MI',0
	.word	54417
	.byte	148,2,3,35,128,42,13
	.byte	'reserved_1614',0
	.word	54422
	.byte	236,5,3,35,148,44,13
	.byte	'JPE',0
	.word	54433
	.byte	144,1,3,35,128,50,13
	.byte	'reserved_1990',0
	.word	54438
	.byte	240,20,3,35,144,51,13
	.byte	'ISPIS',0
	.word	54449
	.byte	52,3,35,128,72,13
	.byte	'reserved_2434',0
	.word	51061
	.byte	204,1,3,35,180,72,13
	.byte	'WD',0
	.word	54454
	.byte	32,3,35,128,74,13
	.byte	'reserved_2520',0
	.word	54379
	.byte	224,1,3,35,160,74,13
	.byte	'LDS',0
	.word	54459
	.byte	8,3,35,128,76,13
	.byte	'reserved_2608',0
	.word	54464
	.byte	248,3,3,35,136,76,13
	.byte	'DP',0
	.word	54475
	.byte	52,3,35,128,80,13
	.byte	'reserved_2834',0
	.word	54480
	.byte	204,3,3,35,180,80,13
	.byte	'EP_IC_1S',0
	.word	54501
	.byte	128,10,3,35,128,84,13
	.byte	'reserved_2F00',0
	.word	54506
	.byte	128,12,3,35,128,94,13
	.byte	'MIEP',0
	.word	54517
	.byte	128,12,3,35,128,106,13
	.byte	'reserved_3B00',0
	.word	54522
	.byte	1,3,35,128,118,0,16
	.word	54531
	.byte	21
	.byte	'Ifx_CIF',0,6,161,26,3
	.word	55084
	.byte	21
	.byte	'boolean',0,7,101,29
	.word	492
	.byte	21
	.byte	'uint8',0,7,105,29
	.word	492
	.byte	21
	.byte	'uint16',0,7,109,29
	.word	509
	.byte	7
	.byte	'unsigned long int',0,4,7,21
	.byte	'uint32',0,7,113,29
	.word	55151
	.byte	21
	.byte	'uint64',0,7,118,29
	.word	351
	.byte	21
	.byte	'sint16',0,7,126,29
	.word	8545
	.byte	7
	.byte	'long int',0,4,5,21
	.byte	'sint32',0,7,131,1,29
	.word	55217
	.byte	7
	.byte	'long long int',0,8,5,21
	.byte	'sint64',0,7,138,1,29
	.word	55245
	.byte	21
	.byte	'float32',0,7,167,1,29
	.word	297
	.byte	21
	.byte	'pvoid',0,8,57,28
	.word	383
	.byte	21
	.byte	'Ifx_TickTime',0,8,79,28
	.word	55245
	.byte	21
	.byte	'Ifx_P_ACCEN0_Bits',0,4,79,3
	.word	7109
	.byte	21
	.byte	'Ifx_P_ACCEN1_Bits',0,4,85,3
	.word	7022
	.byte	21
	.byte	'Ifx_P_ESR_Bits',0,4,107,3
	.word	3365
	.byte	21
	.byte	'Ifx_P_ID_Bits',0,4,115,3
	.word	1418
	.byte	21
	.byte	'Ifx_P_IN_Bits',0,4,137,1,3
	.word	2413
	.byte	21
	.byte	'Ifx_P_IOCR0_Bits',0,4,150,1,3
	.word	1546
	.byte	21
	.byte	'Ifx_P_IOCR12_Bits',0,4,163,1,3
	.word	2193
	.byte	21
	.byte	'Ifx_P_IOCR4_Bits',0,4,176,1,3
	.word	1761
	.byte	21
	.byte	'Ifx_P_IOCR8_Bits',0,4,189,1,3
	.word	1976
	.byte	21
	.byte	'Ifx_P_LPCR0_Bits',0,4,197,1,3
	.word	6381
	.byte	21
	.byte	'Ifx_P_LPCR1_Bits',0,4,205,1,3
	.word	6505
	.byte	21
	.byte	'Ifx_P_LPCR1_P21_Bits',0,4,215,1,3
	.word	6589
	.byte	21
	.byte	'Ifx_P_LPCR2_Bits',0,4,229,1,3
	.word	6769
	.byte	21
	.byte	'Ifx_P_OMCR0_Bits',0,4,240,1,3
	.word	5020
	.byte	21
	.byte	'Ifx_P_OMCR12_Bits',0,4,250,1,3
	.word	5544
	.byte	21
	.byte	'Ifx_P_OMCR4_Bits',0,4,133,2,3
	.word	5194
	.byte	21
	.byte	'Ifx_P_OMCR8_Bits',0,4,144,2,3
	.word	5368
	.byte	21
	.byte	'Ifx_P_OMCR_Bits',0,4,166,2,3
	.word	6033
	.byte	21
	.byte	'Ifx_P_OMR_Bits',0,4,203,2,3
	.word	847
	.byte	21
	.byte	'Ifx_P_OMSR0_Bits',0,4,213,2,3
	.word	4357
	.byte	21
	.byte	'Ifx_P_OMSR12_Bits',0,4,224,2,3
	.word	4845
	.byte	21
	.byte	'Ifx_P_OMSR4_Bits',0,4,235,2,3
	.word	4504
	.byte	21
	.byte	'Ifx_P_OMSR8_Bits',0,4,246,2,3
	.word	4673
	.byte	21
	.byte	'Ifx_P_OMSR_Bits',0,4,140,3,3
	.word	5700
	.byte	21
	.byte	'Ifx_P_OUT_Bits',0,4,162,3,3
	.word	531
	.byte	21
	.byte	'Ifx_P_PCSR_Bits',0,4,180,3,3
	.word	4071
	.byte	21
	.byte	'Ifx_P_PDISC_Bits',0,4,202,3,3
	.word	3705
	.byte	21
	.byte	'Ifx_P_PDR0_Bits',0,4,223,3,3
	.word	2736
	.byte	21
	.byte	'Ifx_P_PDR1_Bits',0,4,244,3,3
	.word	3040
	.byte	21
	.byte	'Ifx_P_ACCEN0',0,4,129,4,3
	.word	7636
	.byte	21
	.byte	'Ifx_P_ACCEN1',0,4,137,4,3
	.word	7069
	.byte	21
	.byte	'Ifx_P_ESR',0,4,145,4,3
	.word	3656
	.byte	21
	.byte	'Ifx_P_ID',0,4,153,4,3
	.word	1497
	.byte	21
	.byte	'Ifx_P_IN',0,4,161,4,3
	.word	2687
	.byte	21
	.byte	'Ifx_P_IOCR0',0,4,169,4,3
	.word	1721
	.byte	21
	.byte	'Ifx_P_IOCR12',0,4,177,4,3
	.word	2373
	.byte	21
	.byte	'Ifx_P_IOCR4',0,4,185,4,3
	.word	1936
	.byte	21
	.byte	'Ifx_P_IOCR8',0,4,193,4,3
	.word	2153
	.byte	21
	.byte	'Ifx_P_LPCR0',0,4,201,4,3
	.word	6465
	.byte	21
	.byte	'Ifx_P_LPCR1',0,4,210,4,3
	.word	6714
	.byte	21
	.byte	'Ifx_P_LPCR2',0,4,218,4,3
	.word	6973
	.byte	21
	.byte	'Ifx_P_OMCR',0,4,226,4,3
	.word	6341
	.byte	21
	.byte	'Ifx_P_OMCR0',0,4,234,4,3
	.word	5154
	.byte	21
	.byte	'Ifx_P_OMCR12',0,4,242,4,3
	.word	5660
	.byte	21
	.byte	'Ifx_P_OMCR4',0,4,250,4,3
	.word	5328
	.byte	21
	.byte	'Ifx_P_OMCR8',0,4,130,5,3
	.word	5504
	.byte	21
	.byte	'Ifx_P_OMR',0,4,138,5,3
	.word	1378
	.byte	21
	.byte	'Ifx_P_OMSR',0,4,146,5,3
	.word	5993
	.byte	21
	.byte	'Ifx_P_OMSR0',0,4,154,5,3
	.word	4464
	.byte	21
	.byte	'Ifx_P_OMSR12',0,4,162,5,3
	.word	4980
	.byte	21
	.byte	'Ifx_P_OMSR4',0,4,170,5,3
	.word	4633
	.byte	21
	.byte	'Ifx_P_OMSR8',0,4,178,5,3
	.word	4805
	.byte	21
	.byte	'Ifx_P_OUT',0,4,186,5,3
	.word	807
	.byte	21
	.byte	'Ifx_P_PCSR',0,4,194,5,3
	.word	4317
	.byte	21
	.byte	'Ifx_P_PDISC',0,4,202,5,3
	.word	4031
	.byte	21
	.byte	'Ifx_P_PDR0',0,4,210,5,3
	.word	3000
	.byte	21
	.byte	'Ifx_P_PDR1',0,4,218,5,3
	.word	3316
	.byte	16
	.word	7676
	.byte	21
	.byte	'Ifx_P',0,4,139,6,3
	.word	56648
	.byte	17,9,250,10,9,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy16384',0,0,18
	.byte	'IfxScu_WDTCON1_IR_divBy256',0,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy64',0,2,0,21
	.byte	'IfxScu_WDTCON1_IR',0,9,255,10,3
	.word	56668
	.byte	10
	.byte	'_Ifx_SCU_ACCEN0_Bits',0,10,45,16,4,11
	.byte	'EN0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	492
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	492
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	492
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	492
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	492
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	492
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	492
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	492
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	492
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	492
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	492
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	492
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	492
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	492
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	492
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_ACCEN0_Bits',0,10,79,3
	.word	56790
	.byte	10
	.byte	'_Ifx_SCU_ACCEN1_Bits',0,10,82,16,4,11
	.byte	'reserved_0',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_SCU_ACCEN1_Bits',0,10,85,3
	.word	57347
	.byte	10
	.byte	'_Ifx_SCU_ARSTDIS_Bits',0,10,88,16,4,11
	.byte	'STM0DIS',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'STM1DIS',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'STM2DIS',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	469
	.byte	29,0,2,35,0,0,21
	.byte	'Ifx_SCU_ARSTDIS_Bits',0,10,94,3
	.word	57424
	.byte	10
	.byte	'_Ifx_SCU_CCUCON0_Bits',0,10,97,16,4,11
	.byte	'BAUD1DIV',0,1
	.word	492
	.byte	4,4,2,35,0,11
	.byte	'BAUD2DIV',0,1
	.word	492
	.byte	4,0,2,35,0,11
	.byte	'SRIDIV',0,1
	.word	492
	.byte	4,4,2,35,1,11
	.byte	'LPDIV',0,1
	.word	492
	.byte	4,0,2,35,1,11
	.byte	'SPBDIV',0,1
	.word	492
	.byte	4,4,2,35,2,11
	.byte	'FSI2DIV',0,1
	.word	492
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	492
	.byte	2,0,2,35,2,11
	.byte	'FSIDIV',0,1
	.word	492
	.byte	2,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	492
	.byte	2,4,2,35,3,11
	.byte	'CLKSEL',0,1
	.word	492
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON0_Bits',0,10,111,3
	.word	57560
	.byte	10
	.byte	'_Ifx_SCU_CCUCON1_Bits',0,10,114,16,4,11
	.byte	'CANDIV',0,1
	.word	492
	.byte	4,4,2,35,0,11
	.byte	'ERAYDIV',0,1
	.word	492
	.byte	4,0,2,35,0,11
	.byte	'STMDIV',0,1
	.word	492
	.byte	4,4,2,35,1,11
	.byte	'GTMDIV',0,1
	.word	492
	.byte	4,0,2,35,1,11
	.byte	'ETHDIV',0,1
	.word	492
	.byte	4,4,2,35,2,11
	.byte	'ASCLINFDIV',0,1
	.word	492
	.byte	4,0,2,35,2,11
	.byte	'ASCLINSDIV',0,1
	.word	492
	.byte	4,4,2,35,3,11
	.byte	'INSEL',0,1
	.word	492
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON1_Bits',0,10,126,3
	.word	57840
	.byte	10
	.byte	'_Ifx_SCU_CCUCON2_Bits',0,10,129,1,16,4,11
	.byte	'BBBDIV',0,1
	.word	492
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	469
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON2_Bits',0,10,135,1,3
	.word	58078
	.byte	10
	.byte	'_Ifx_SCU_CCUCON3_Bits',0,10,138,1,16,4,11
	.byte	'PLLDIV',0,1
	.word	492
	.byte	6,2,2,35,0,11
	.byte	'PLLSEL',0,1
	.word	492
	.byte	2,0,2,35,0,11
	.byte	'PLLERAYDIV',0,1
	.word	492
	.byte	6,2,2,35,1,11
	.byte	'PLLERAYSEL',0,1
	.word	492
	.byte	2,0,2,35,1,11
	.byte	'SRIDIV',0,1
	.word	492
	.byte	6,2,2,35,2,11
	.byte	'SRISEL',0,1
	.word	492
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	492
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON3_Bits',0,10,150,1,3
	.word	58206
	.byte	10
	.byte	'_Ifx_SCU_CCUCON4_Bits',0,10,153,1,16,4,11
	.byte	'SPBDIV',0,1
	.word	492
	.byte	6,2,2,35,0,11
	.byte	'SPBSEL',0,1
	.word	492
	.byte	2,0,2,35,0,11
	.byte	'GTMDIV',0,1
	.word	492
	.byte	6,2,2,35,1,11
	.byte	'GTMSEL',0,1
	.word	492
	.byte	2,0,2,35,1,11
	.byte	'STMDIV',0,1
	.word	492
	.byte	6,2,2,35,2,11
	.byte	'STMSEL',0,1
	.word	492
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	492
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON4_Bits',0,10,165,1,3
	.word	58449
	.byte	10
	.byte	'_Ifx_SCU_CCUCON5_Bits',0,10,168,1,16,4,11
	.byte	'MAXDIV',0,1
	.word	492
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	469
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON5_Bits',0,10,174,1,3
	.word	58684
	.byte	10
	.byte	'_Ifx_SCU_CCUCON6_Bits',0,10,177,1,16,4,11
	.byte	'CPU0DIV',0,1
	.word	492
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	469
	.byte	26,0,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON6_Bits',0,10,181,1,3
	.word	58812
	.byte	10
	.byte	'_Ifx_SCU_CCUCON7_Bits',0,10,184,1,16,4,11
	.byte	'CPU1DIV',0,1
	.word	492
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	469
	.byte	26,0,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON7_Bits',0,10,188,1,3
	.word	58912
	.byte	10
	.byte	'_Ifx_SCU_CHIPID_Bits',0,10,191,1,16,4,11
	.byte	'CHREV',0,1
	.word	492
	.byte	6,2,2,35,0,11
	.byte	'CHTEC',0,1
	.word	492
	.byte	2,0,2,35,0,11
	.byte	'CHID',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'EEA',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'UCODE',0,1
	.word	492
	.byte	7,0,2,35,2,11
	.byte	'FSIZE',0,1
	.word	492
	.byte	4,4,2,35,3,11
	.byte	'SP',0,1
	.word	492
	.byte	2,2,2,35,3,11
	.byte	'SEC',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CHIPID_Bits',0,10,202,1,3
	.word	59012
	.byte	10
	.byte	'_Ifx_SCU_DTSCON_Bits',0,10,205,1,16,4,11
	.byte	'PWD',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'START',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	492
	.byte	2,4,2,35,0,11
	.byte	'CAL',0,4
	.word	469
	.byte	20,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	7,1,2,35,3,11
	.byte	'SLCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_DTSCON_Bits',0,10,213,1,3
	.word	59220
	.byte	10
	.byte	'_Ifx_SCU_DTSLIM_Bits',0,10,216,1,16,4,11
	.byte	'LOWER',0,2
	.word	509
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	492
	.byte	5,1,2,35,1,11
	.byte	'LLU',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'UPPER',0,2
	.word	509
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	492
	.byte	4,2,2,35,3,11
	.byte	'SLCK',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'UOF',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_DTSLIM_Bits',0,10,225,1,3
	.word	59385
	.byte	10
	.byte	'_Ifx_SCU_DTSSTAT_Bits',0,10,228,1,16,4,11
	.byte	'RESULT',0,2
	.word	509
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	492
	.byte	4,2,2,35,1,11
	.byte	'RDY',0,1
	.word	492
	.byte	1,1,2,35,1,11
	.byte	'BUSY',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_DTSSTAT_Bits',0,10,235,1,3
	.word	59568
	.byte	10
	.byte	'_Ifx_SCU_EICR_Bits',0,10,238,1,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	4,4,2,35,0,11
	.byte	'EXIS0',0,1
	.word	492
	.byte	3,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'FEN0',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'REN0',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'LDEN0',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'EIEN0',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'INP0',0,1
	.word	492
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	469
	.byte	5,12,2,35,0,11
	.byte	'EXIS1',0,1
	.word	492
	.byte	3,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	492
	.byte	1,0,2,35,2,11
	.byte	'FEN1',0,1
	.word	492
	.byte	1,7,2,35,3,11
	.byte	'REN1',0,1
	.word	492
	.byte	1,6,2,35,3,11
	.byte	'LDEN1',0,1
	.word	492
	.byte	1,5,2,35,3,11
	.byte	'EIEN1',0,1
	.word	492
	.byte	1,4,2,35,3,11
	.byte	'INP1',0,1
	.word	492
	.byte	3,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EICR_Bits',0,10,129,2,3
	.word	59722
	.byte	10
	.byte	'_Ifx_SCU_EIFR_Bits',0,10,132,2,16,4,11
	.byte	'INTF0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'INTF1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'INTF2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'INTF3',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'INTF4',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'INTF5',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'INTF6',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'INTF7',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	469
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_SCU_EIFR_Bits',0,10,143,2,3
	.word	60086
	.byte	10
	.byte	'_Ifx_SCU_EMSR_Bits',0,10,146,2,16,4,11
	.byte	'POL',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'MODE',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'ENON',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'PSEL',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	509
	.byte	12,0,2,35,0,11
	.byte	'EMSF',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'SEMSF',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	492
	.byte	6,0,2,35,2,11
	.byte	'EMSFM',0,1
	.word	492
	.byte	2,6,2,35,3,11
	.byte	'SEMSFM',0,1
	.word	492
	.byte	2,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	492
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_EMSR_Bits',0,10,159,2,3
	.word	60297
	.byte	10
	.byte	'_Ifx_SCU_ESRCFG_Bits',0,10,162,2,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	7,1,2,35,0,11
	.byte	'EDCON',0,2
	.word	509
	.byte	2,7,2,35,0,11
	.byte	'reserved_9',0,4
	.word	469
	.byte	23,0,2,35,0,0,21
	.byte	'Ifx_SCU_ESRCFG_Bits',0,10,167,2,3
	.word	60549
	.byte	10
	.byte	'_Ifx_SCU_ESROCFG_Bits',0,10,170,2,16,4,11
	.byte	'ARI',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'ARC',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	469
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_ESROCFG_Bits',0,10,175,2,3
	.word	60667
	.byte	10
	.byte	'_Ifx_SCU_EVR13CON_Bits',0,10,178,2,16,4,11
	.byte	'reserved_0',0,4
	.word	469
	.byte	28,4,2,35,0,11
	.byte	'EVR13OFF',0,1
	.word	492
	.byte	1,3,2,35,3,11
	.byte	'BPEVR13OFF',0,1
	.word	492
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVR13CON_Bits',0,10,185,2,3
	.word	60778
	.byte	10
	.byte	'_Ifx_SCU_EVR33CON_Bits',0,10,188,2,16,4,11
	.byte	'reserved_0',0,4
	.word	469
	.byte	28,4,2,35,0,11
	.byte	'EVR33OFF',0,1
	.word	492
	.byte	1,3,2,35,3,11
	.byte	'BPEVR33OFF',0,1
	.word	492
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVR33CON_Bits',0,10,195,2,3
	.word	60941
	.byte	10
	.byte	'_Ifx_SCU_EVRADCSTAT_Bits',0,10,198,2,16,4,11
	.byte	'ADC13V',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'ADC33V',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'ADCSWDV',0,1
	.word	492
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRADCSTAT_Bits',0,10,205,2,3
	.word	61104
	.byte	10
	.byte	'_Ifx_SCU_EVRDVSTAT_Bits',0,10,208,2,16,4,11
	.byte	'DVS13TRIM',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'DVS33TRIM',0,1
	.word	492
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRDVSTAT_Bits',0,10,215,2,3
	.word	61262
	.byte	10
	.byte	'_Ifx_SCU_EVRMONCTRL_Bits',0,10,218,2,16,4,11
	.byte	'EVR13OVMOD',0,1
	.word	492
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	492
	.byte	2,4,2,35,0,11
	.byte	'EVR13UVMOD',0,1
	.word	492
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	492
	.byte	2,0,2,35,0,11
	.byte	'EVR33OVMOD',0,1
	.word	492
	.byte	2,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	492
	.byte	2,4,2,35,1,11
	.byte	'EVR33UVMOD',0,1
	.word	492
	.byte	2,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	492
	.byte	2,0,2,35,1,11
	.byte	'SWDOVMOD',0,1
	.word	492
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	492
	.byte	2,4,2,35,2,11
	.byte	'SWDUVMOD',0,1
	.word	492
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,2
	.word	509
	.byte	10,0,2,35,2,0,21
	.byte	'Ifx_SCU_EVRMONCTRL_Bits',0,10,232,2,3
	.word	61427
	.byte	10
	.byte	'_Ifx_SCU_EVROSCCTRL_Bits',0,10,235,2,16,4,11
	.byte	'OSCTRIM',0,2
	.word	509
	.byte	10,6,2,35,0,11
	.byte	'OSCPTAT',0,1
	.word	492
	.byte	6,0,2,35,1,11
	.byte	'OSCANASEL',0,1
	.word	492
	.byte	4,4,2,35,2,11
	.byte	'HPBGTRIM',0,2
	.word	509
	.byte	7,5,2,35,2,11
	.byte	'HPBGCLKEN',0,1
	.word	492
	.byte	1,4,2,35,3,11
	.byte	'OSC3V3',0,1
	.word	492
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	492
	.byte	2,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVROSCCTRL_Bits',0,10,245,2,3
	.word	61756
	.byte	10
	.byte	'_Ifx_SCU_EVROVMON_Bits',0,10,248,2,16,4,11
	.byte	'EVR13OVVAL',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'EVR33OVVAL',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'SWDOVVAL',0,1
	.word	492
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVROVMON_Bits',0,10,255,2,3
	.word	61977
	.byte	10
	.byte	'_Ifx_SCU_EVRRSTCON_Bits',0,10,130,3,16,4,11
	.byte	'RST13TRIM',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	469
	.byte	16,8,2,35,0,11
	.byte	'RST13OFF',0,1
	.word	492
	.byte	1,7,2,35,3,11
	.byte	'BPRST13OFF',0,1
	.word	492
	.byte	1,6,2,35,3,11
	.byte	'RST33OFF',0,1
	.word	492
	.byte	1,5,2,35,3,11
	.byte	'BPRST33OFF',0,1
	.word	492
	.byte	1,4,2,35,3,11
	.byte	'RSTSWDOFF',0,1
	.word	492
	.byte	1,3,2,35,3,11
	.byte	'BPRSTSWDOFF',0,1
	.word	492
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRRSTCON_Bits',0,10,142,3,3
	.word	62140
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF1_Bits',0,10,145,3,16,4,11
	.byte	'SD5P',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'SD5I',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'SD5D',0,1
	.word	492
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF1_Bits',0,10,152,3,3
	.word	62412
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF2_Bits',0,10,155,3,16,4,11
	.byte	'SD33P',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'SD33I',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'SD33D',0,1
	.word	492
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF2_Bits',0,10,162,3,3
	.word	62565
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF3_Bits',0,10,165,3,16,4,11
	.byte	'CT5REG0',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'CT5REG1',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'CT5REG2',0,1
	.word	492
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF3_Bits',0,10,172,3,3
	.word	62721
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF4_Bits',0,10,175,3,16,4,11
	.byte	'CT5REG3',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'CT5REG4',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF4_Bits',0,10,181,3,3
	.word	62883
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF5_Bits',0,10,184,3,16,4,11
	.byte	'CT33REG0',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'CT33REG1',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'CT33REG2',0,1
	.word	492
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF5_Bits',0,10,191,3,3
	.word	63026
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF6_Bits',0,10,194,3,16,4,11
	.byte	'CT33REG3',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'CT33REG4',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF6_Bits',0,10,200,3,3
	.word	63191
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL1_Bits',0,10,203,3,16,4,11
	.byte	'SDFREQSPRD',0,2
	.word	509
	.byte	16,0,2,35,0,11
	.byte	'SDFREQ',0,1
	.word	492
	.byte	8,0,2,35,2,11
	.byte	'SDSTEP',0,1
	.word	492
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	492
	.byte	2,2,2,35,3,11
	.byte	'SDSAMPLE',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCTRL1_Bits',0,10,211,3,3
	.word	63336
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL2_Bits',0,10,214,3,16,4,11
	.byte	'DRVP',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'SDMINMAXDC',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'DRVN',0,1
	.word	492
	.byte	8,0,2,35,2,11
	.byte	'SDLUT',0,1
	.word	492
	.byte	6,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCTRL2_Bits',0,10,222,3,3
	.word	63517
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL3_Bits',0,10,225,3,16,4,11
	.byte	'SDPWMPRE',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'SDPID',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'SDVOKLVL',0,1
	.word	492
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCTRL3_Bits',0,10,232,3,3
	.word	63691
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL4_Bits',0,10,235,3,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'SYNCDIV',0,1
	.word	492
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	469
	.byte	20,1,2,35,0,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCTRL4_Bits',0,10,241,3,3
	.word	63851
	.byte	10
	.byte	'_Ifx_SCU_EVRSTAT_Bits',0,10,244,3,16,4,11
	.byte	'EVR13',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'OV13',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'EVR33',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'OV33',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'OVSWD',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'UV13',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'UV33',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'UVSWD',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'EXTPASS13',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'EXTPASS33',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'BGPROK',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	469
	.byte	21,0,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSTAT_Bits',0,10,130,4,3
	.word	63995
	.byte	10
	.byte	'_Ifx_SCU_EVRTRIM_Bits',0,10,133,4,16,4,11
	.byte	'EVR13TRIM',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'SDVOUTSEL',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRTRIM_Bits',0,10,139,4,3
	.word	64269
	.byte	10
	.byte	'_Ifx_SCU_EVRUVMON_Bits',0,10,142,4,16,4,11
	.byte	'EVR13UVVAL',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'EVR33UVVAL',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'SWDUVVAL',0,1
	.word	492
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRUVMON_Bits',0,10,149,4,3
	.word	64408
	.byte	10
	.byte	'_Ifx_SCU_EXTCON_Bits',0,10,152,4,16,4,11
	.byte	'EN0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'SEL0',0,1
	.word	492
	.byte	4,2,2,35,0,11
	.byte	'reserved_6',0,2
	.word	509
	.byte	10,0,2,35,0,11
	.byte	'EN1',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'NSEL',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'SEL1',0,1
	.word	492
	.byte	4,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	492
	.byte	2,0,2,35,2,11
	.byte	'DIV1',0,1
	.word	492
	.byte	8,0,2,35,3,0,21
	.byte	'Ifx_SCU_EXTCON_Bits',0,10,163,4,3
	.word	64571
	.byte	10
	.byte	'_Ifx_SCU_FDR_Bits',0,10,166,4,16,4,11
	.byte	'STEP',0,2
	.word	509
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	492
	.byte	4,2,2,35,1,11
	.byte	'DM',0,1
	.word	492
	.byte	2,0,2,35,1,11
	.byte	'RESULT',0,2
	.word	509
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	492
	.byte	5,1,2,35,3,11
	.byte	'DISCLK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_FDR_Bits',0,10,174,4,3
	.word	64789
	.byte	10
	.byte	'_Ifx_SCU_FMR_Bits',0,10,177,4,16,4,11
	.byte	'FS0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'FS1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'FS2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'FS3',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'FS4',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'FS5',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'FS6',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'FS7',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'FC0',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'FC1',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'FC2',0,1
	.word	492
	.byte	1,5,2,35,2,11
	.byte	'FC3',0,1
	.word	492
	.byte	1,4,2,35,2,11
	.byte	'FC4',0,1
	.word	492
	.byte	1,3,2,35,2,11
	.byte	'FC5',0,1
	.word	492
	.byte	1,2,2,35,2,11
	.byte	'FC6',0,1
	.word	492
	.byte	1,1,2,35,2,11
	.byte	'FC7',0,1
	.word	492
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	8,0,2,35,3,0,21
	.byte	'Ifx_SCU_FMR_Bits',0,10,197,4,3
	.word	64952
	.byte	10
	.byte	'_Ifx_SCU_ID_Bits',0,10,200,4,16,4,11
	.byte	'MODREV',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	509
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_ID_Bits',0,10,205,4,3
	.word	65288
	.byte	10
	.byte	'_Ifx_SCU_IGCR_Bits',0,10,208,4,16,4,11
	.byte	'IPEN00',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'IPEN01',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'IPEN02',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'IPEN03',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'IPEN04',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'IPEN05',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'IPEN06',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'IPEN07',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	492
	.byte	5,3,2,35,1,11
	.byte	'GEEN0',0,1
	.word	492
	.byte	1,2,2,35,1,11
	.byte	'IGP0',0,1
	.word	492
	.byte	2,0,2,35,1,11
	.byte	'IPEN10',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'IPEN11',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'IPEN12',0,1
	.word	492
	.byte	1,5,2,35,2,11
	.byte	'IPEN13',0,1
	.word	492
	.byte	1,4,2,35,2,11
	.byte	'IPEN14',0,1
	.word	492
	.byte	1,3,2,35,2,11
	.byte	'IPEN15',0,1
	.word	492
	.byte	1,2,2,35,2,11
	.byte	'IPEN16',0,1
	.word	492
	.byte	1,1,2,35,2,11
	.byte	'IPEN17',0,1
	.word	492
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	5,3,2,35,3,11
	.byte	'GEEN1',0,1
	.word	492
	.byte	1,2,2,35,3,11
	.byte	'IGP1',0,1
	.word	492
	.byte	2,0,2,35,3,0,21
	.byte	'Ifx_SCU_IGCR_Bits',0,10,232,4,3
	.word	65395
	.byte	10
	.byte	'_Ifx_SCU_IN_Bits',0,10,235,4,16,4,11
	.byte	'P0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	469
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_IN_Bits',0,10,240,4,3
	.word	65847
	.byte	10
	.byte	'_Ifx_SCU_IOCR_Bits',0,10,243,4,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	4,4,2,35,0,11
	.byte	'PC0',0,1
	.word	492
	.byte	4,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	492
	.byte	4,4,2,35,1,11
	.byte	'PC1',0,1
	.word	492
	.byte	4,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_IOCR_Bits',0,10,250,4,3
	.word	65946
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL0_Bits',0,10,253,4,16,4,11
	.byte	'LBISTREQ',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'LBISTREQP',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'PATTERNS',0,2
	.word	509
	.byte	14,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_LBISTCTRL0_Bits',0,10,131,5,3
	.word	66096
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL1_Bits',0,10,134,5,16,4,11
	.byte	'SEED',0,4
	.word	469
	.byte	23,9,2,35,0,11
	.byte	'reserved_23',0,1
	.word	492
	.byte	1,0,2,35,2,11
	.byte	'SPLITSH',0,1
	.word	492
	.byte	3,5,2,35,3,11
	.byte	'BODY',0,1
	.word	492
	.byte	1,4,2,35,3,11
	.byte	'LBISTFREQU',0,1
	.word	492
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_LBISTCTRL1_Bits',0,10,141,5,3
	.word	66245
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL2_Bits',0,10,144,5,16,4,11
	.byte	'SIGNATURE',0,4
	.word	469
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	7,1,2,35,3,11
	.byte	'LBISTDONE',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_LBISTCTRL2_Bits',0,10,149,5,3
	.word	66406
	.byte	10
	.byte	'_Ifx_SCU_LCLCON_Bits',0,10,152,5,16,4,11
	.byte	'reserved_0',0,2
	.word	509
	.byte	16,0,2,35,0,11
	.byte	'LS',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,2
	.word	509
	.byte	14,1,2,35,2,11
	.byte	'LSEN',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_LCLCON_Bits',0,10,158,5,3
	.word	66536
	.byte	10
	.byte	'_Ifx_SCU_LCLTEST_Bits',0,10,161,5,16,4,11
	.byte	'LCLT0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'LCLT1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	469
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_LCLTEST_Bits',0,10,166,5,3
	.word	66668
	.byte	10
	.byte	'_Ifx_SCU_MANID_Bits',0,10,169,5,16,4,11
	.byte	'DEPT',0,1
	.word	492
	.byte	5,3,2,35,0,11
	.byte	'MANUF',0,2
	.word	509
	.byte	11,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_MANID_Bits',0,10,174,5,3
	.word	66783
	.byte	10
	.byte	'_Ifx_SCU_OMR_Bits',0,10,177,5,16,4,11
	.byte	'PS0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	509
	.byte	14,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	509
	.byte	14,0,2,35,2,0,21
	.byte	'Ifx_SCU_OMR_Bits',0,10,185,5,3
	.word	66894
	.byte	10
	.byte	'_Ifx_SCU_OSCCON_Bits',0,10,188,5,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'PLLLV',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'OSCRES',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'GAINSEL',0,1
	.word	492
	.byte	2,3,2,35,0,11
	.byte	'MODE',0,1
	.word	492
	.byte	2,1,2,35,0,11
	.byte	'SHBY',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'PLLHV',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'X1D',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'X1DEN',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	492
	.byte	4,0,2,35,1,11
	.byte	'OSCVAL',0,1
	.word	492
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	492
	.byte	2,1,2,35,2,11
	.byte	'APREN',0,1
	.word	492
	.byte	1,0,2,35,2,11
	.byte	'CAP0EN',0,1
	.word	492
	.byte	1,7,2,35,3,11
	.byte	'CAP1EN',0,1
	.word	492
	.byte	1,6,2,35,3,11
	.byte	'CAP2EN',0,1
	.word	492
	.byte	1,5,2,35,3,11
	.byte	'CAP3EN',0,1
	.word	492
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	492
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_OSCCON_Bits',0,10,209,5,3
	.word	67052
	.byte	10
	.byte	'_Ifx_SCU_OUT_Bits',0,10,212,5,16,4,11
	.byte	'P0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	469
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_OUT_Bits',0,10,217,5,3
	.word	67464
	.byte	10
	.byte	'_Ifx_SCU_OVCCON_Bits',0,10,220,5,16,4,11
	.byte	'CSEL0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'CSEL1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'CSEL2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,2
	.word	509
	.byte	13,0,2,35,0,11
	.byte	'OVSTRT',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'OVSTP',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'DCINVAL',0,1
	.word	492
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	492
	.byte	5,0,2,35,2,11
	.byte	'OVCONF',0,1
	.word	492
	.byte	1,7,2,35,3,11
	.byte	'POVCONF',0,1
	.word	492
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	492
	.byte	6,0,2,35,3,0,21
	.byte	'Ifx_SCU_OVCCON_Bits',0,10,233,5,3
	.word	67565
	.byte	10
	.byte	'_Ifx_SCU_OVCENABLE_Bits',0,10,236,5,16,4,11
	.byte	'OVEN0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'OVEN1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'OVEN2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	469
	.byte	29,0,2,35,0,0,21
	.byte	'Ifx_SCU_OVCENABLE_Bits',0,10,242,5,3
	.word	67832
	.byte	10
	.byte	'_Ifx_SCU_PDISC_Bits',0,10,245,5,16,4,11
	.byte	'PDIS0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	469
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_PDISC_Bits',0,10,250,5,3
	.word	67968
	.byte	10
	.byte	'_Ifx_SCU_PDR_Bits',0,10,253,5,16,4,11
	.byte	'PD0',0,1
	.word	492
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	492
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	469
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_SCU_PDR_Bits',0,10,132,6,3
	.word	68079
	.byte	10
	.byte	'_Ifx_SCU_PDRR_Bits',0,10,135,6,16,4,11
	.byte	'PDR0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'PDR1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'PDR2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'PDR3',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'PDR4',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'PDR5',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'PDR6',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'PDR7',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	469
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_SCU_PDRR_Bits',0,10,146,6,3
	.word	68212
	.byte	10
	.byte	'_Ifx_SCU_PLLCON0_Bits',0,10,149,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'MODEN',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	509
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	492
	.byte	7,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	492
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	492
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	492
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	492
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_PLLCON0_Bits',0,10,166,6,3
	.word	68415
	.byte	10
	.byte	'_Ifx_SCU_PLLCON1_Bits',0,10,169,6,16,4,11
	.byte	'K2DIV',0,1
	.word	492
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	492
	.byte	7,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	492
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	509
	.byte	9,0,2,35,2,0,21
	.byte	'Ifx_SCU_PLLCON1_Bits',0,10,177,6,3
	.word	68771
	.byte	10
	.byte	'_Ifx_SCU_PLLCON2_Bits',0,10,180,6,16,4,11
	.byte	'MODCFG',0,2
	.word	509
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_PLLCON2_Bits',0,10,184,6,3
	.word	68949
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON0_Bits',0,10,187,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	492
	.byte	2,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	509
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	492
	.byte	5,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	492
	.byte	2,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	492
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	492
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	492
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	492
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_PLLERAYCON0_Bits',0,10,204,6,3
	.word	69049
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON1_Bits',0,10,207,6,16,4,11
	.byte	'K2DIV',0,1
	.word	492
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	492
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	492
	.byte	4,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	492
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	509
	.byte	9,0,2,35,2,0,21
	.byte	'Ifx_SCU_PLLERAYCON1_Bits',0,10,215,6,3
	.word	69419
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYSTAT_Bits',0,10,218,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'PWDSTAT',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	469
	.byte	26,0,2,35,0,0,21
	.byte	'Ifx_SCU_PLLERAYSTAT_Bits',0,10,227,6,3
	.word	69605
	.byte	10
	.byte	'_Ifx_SCU_PLLSTAT_Bits',0,10,230,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'MODRUN',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	469
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_SCU_PLLSTAT_Bits',0,10,241,6,3
	.word	69803
	.byte	10
	.byte	'_Ifx_SCU_PMCSR_Bits',0,10,244,6,16,4,11
	.byte	'REQSLP',0,1
	.word	492
	.byte	2,6,2,35,0,11
	.byte	'SMUSLP',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	492
	.byte	5,0,2,35,0,11
	.byte	'PMST',0,1
	.word	492
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	469
	.byte	21,0,2,35,0,0,21
	.byte	'Ifx_SCU_PMCSR_Bits',0,10,251,6,3
	.word	70036
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR0_Bits',0,10,254,6,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'ESR1WKEN',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'PINAWKEN',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'PINBWKEN',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'ESR0DFEN',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'ESR0EDCON',0,1
	.word	492
	.byte	2,1,2,35,0,11
	.byte	'ESR1DFEN',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'ESR1EDCON',0,1
	.word	492
	.byte	2,6,2,35,1,11
	.byte	'PINADFEN',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'PINAEDCON',0,1
	.word	492
	.byte	2,3,2,35,1,11
	.byte	'PINBDFEN',0,1
	.word	492
	.byte	1,2,2,35,1,11
	.byte	'PINBEDCON',0,1
	.word	492
	.byte	2,0,2,35,1,11
	.byte	'SCREN',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'STBYRAMSEL',0,1
	.word	492
	.byte	2,5,2,35,2,11
	.byte	'SCRCLKSEL',0,1
	.word	492
	.byte	1,4,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	492
	.byte	1,3,2,35,2,11
	.byte	'TRISTEN',0,1
	.word	492
	.byte	1,2,2,35,2,11
	.byte	'TRISTREQ',0,1
	.word	492
	.byte	1,1,2,35,2,11
	.byte	'PORSTDF',0,1
	.word	492
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	1,7,2,35,3,11
	.byte	'DCDCSYNC',0,1
	.word	492
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	492
	.byte	3,3,2,35,3,11
	.byte	'ESR0TRIST',0,1
	.word	492
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_PMSWCR0_Bits',0,10,153,7,3
	.word	70188
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR1_Bits',0,10,156,7,16,4,11
	.byte	'SCRSTEN',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'SCRSTREQ',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	492
	.byte	6,0,2,35,0,11
	.byte	'CPUIDLSEL',0,1
	.word	492
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'IRADIS',0,1
	.word	492
	.byte	1,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	492
	.byte	3,0,2,35,1,11
	.byte	'SCRCFG',0,1
	.word	492
	.byte	8,0,2,35,2,11
	.byte	'CPUSEL',0,1
	.word	492
	.byte	3,5,2,35,3,11
	.byte	'STBYEVEN',0,1
	.word	492
	.byte	1,4,2,35,3,11
	.byte	'STBYEV',0,1
	.word	492
	.byte	3,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_PMSWCR1_Bits',0,10,170,7,3
	.word	70755
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR2_Bits',0,10,173,7,16,4,11
	.byte	'SCRINT',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'BUSY',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'SCRECC',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'SCRWDT',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'SCRRST',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	492
	.byte	4,0,2,35,1,11
	.byte	'TCINT',0,1
	.word	492
	.byte	8,0,2,35,2,11
	.byte	'TCINTREQ',0,1
	.word	492
	.byte	1,7,2,35,3,11
	.byte	'SMURST',0,1
	.word	492
	.byte	1,6,2,35,3,11
	.byte	'RST',0,1
	.word	492
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	492
	.byte	4,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_PMSWCR2_Bits',0,10,187,7,3
	.word	71049
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTAT_Bits',0,10,190,7,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKP',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUN',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'PINAWKP',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUN',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'PINBWKP',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUN',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'PORSTDF',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'HWCFGEVR',0,1
	.word	492
	.byte	3,3,2,35,1,11
	.byte	'STBYRAM',0,1
	.word	492
	.byte	2,1,2,35,1,11
	.byte	'TRIST',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'SCRST',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'SCRWKP',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'SCR',0,1
	.word	492
	.byte	1,5,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	492
	.byte	1,4,2,35,2,11
	.byte	'ESR1WKEN',0,1
	.word	492
	.byte	1,3,2,35,2,11
	.byte	'PINAWKEN',0,1
	.word	492
	.byte	1,2,2,35,2,11
	.byte	'PINBWKEN',0,1
	.word	492
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	509
	.byte	4,5,2,35,2,11
	.byte	'ESR0TRIST',0,1
	.word	492
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	492
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_PMSWSTAT_Bits',0,10,214,7,3
	.word	71327
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTATCLR_Bits',0,10,217,7,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKPCLR',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUNCLR',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'PINAWKPCLR',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUNCLR',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'PINBWKPCLR',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUNCLR',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'SCRSTCLR',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'SCRWKPCLR',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	509
	.byte	14,0,2,35,2,0,21
	.byte	'Ifx_SCU_PMSWSTATCLR_Bits',0,10,230,7,3
	.word	71823
	.byte	10
	.byte	'_Ifx_SCU_RSTCON2_Bits',0,10,233,7,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'CLRC',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	509
	.byte	10,4,2,35,0,11
	.byte	'CSS0',0,1
	.word	492
	.byte	1,3,2,35,1,11
	.byte	'CSS1',0,1
	.word	492
	.byte	1,2,2,35,1,11
	.byte	'CSS2',0,1
	.word	492
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'USRINFO',0,2
	.word	509
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_RSTCON2_Bits',0,10,243,7,3
	.word	72136
	.byte	10
	.byte	'_Ifx_SCU_RSTCON_Bits',0,10,246,7,16,4,11
	.byte	'ESR0',0,1
	.word	492
	.byte	2,6,2,35,0,11
	.byte	'ESR1',0,1
	.word	492
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	492
	.byte	2,2,2,35,0,11
	.byte	'SMU',0,1
	.word	492
	.byte	2,0,2,35,0,11
	.byte	'SW',0,1
	.word	492
	.byte	2,6,2,35,1,11
	.byte	'STM0',0,1
	.word	492
	.byte	2,4,2,35,1,11
	.byte	'STM1',0,1
	.word	492
	.byte	2,2,2,35,1,11
	.byte	'STM2',0,1
	.word	492
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_RSTCON_Bits',0,10,129,8,3
	.word	72345
	.byte	10
	.byte	'_Ifx_SCU_RSTSTAT_Bits',0,10,132,8,16,4,11
	.byte	'ESR0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'ESR1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'SMU',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'SW',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'STM0',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'STM1',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'STM2',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'PORST',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'CB0',0,1
	.word	492
	.byte	1,5,2,35,2,11
	.byte	'CB1',0,1
	.word	492
	.byte	1,4,2,35,2,11
	.byte	'CB3',0,1
	.word	492
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	492
	.byte	2,1,2,35,2,11
	.byte	'EVR13',0,1
	.word	492
	.byte	1,0,2,35,2,11
	.byte	'EVR33',0,1
	.word	492
	.byte	1,7,2,35,3,11
	.byte	'SWD',0,1
	.word	492
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	492
	.byte	2,4,2,35,3,11
	.byte	'STBYR',0,1
	.word	492
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	492
	.byte	3,0,2,35,3,0,21
	.byte	'Ifx_SCU_RSTSTAT_Bits',0,10,155,8,3
	.word	72556
	.byte	10
	.byte	'_Ifx_SCU_SAFECON_Bits',0,10,158,8,16,4,11
	.byte	'HBT',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	469
	.byte	31,0,2,35,0,0,21
	.byte	'Ifx_SCU_SAFECON_Bits',0,10,162,8,3
	.word	72988
	.byte	10
	.byte	'_Ifx_SCU_STSTAT_Bits',0,10,165,8,16,4,11
	.byte	'HWCFG',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'FTM',0,1
	.word	492
	.byte	7,1,2,35,1,11
	.byte	'MODE',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'FCBAE',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'LUDIS',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	492
	.byte	1,5,2,35,2,11
	.byte	'TRSTL',0,1
	.word	492
	.byte	1,4,2,35,2,11
	.byte	'SPDEN',0,1
	.word	492
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	492
	.byte	3,0,2,35,2,11
	.byte	'RAMINT',0,1
	.word	492
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	492
	.byte	7,0,2,35,3,0,21
	.byte	'Ifx_SCU_STSTAT_Bits',0,10,178,8,3
	.word	73084
	.byte	10
	.byte	'_Ifx_SCU_SWRSTCON_Bits',0,10,181,8,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'SWRSTREQ',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	469
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_SWRSTCON_Bits',0,10,186,8,3
	.word	73344
	.byte	10
	.byte	'_Ifx_SCU_SYSCON_Bits',0,10,189,8,16,4,11
	.byte	'CCTRIG0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'RAMINTM',0,1
	.word	492
	.byte	2,4,2,35,0,11
	.byte	'SETLUDIS',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	492
	.byte	3,0,2,35,0,11
	.byte	'DATM',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,4
	.word	469
	.byte	23,0,2,35,0,0,21
	.byte	'Ifx_SCU_SYSCON_Bits',0,10,198,8,3
	.word	73469
	.byte	10
	.byte	'_Ifx_SCU_TRAPCLR_Bits',0,10,201,8,16,4,11
	.byte	'ESR0T',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	469
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPCLR_Bits',0,10,208,8,3
	.word	73666
	.byte	10
	.byte	'_Ifx_SCU_TRAPDIS_Bits',0,10,211,8,16,4,11
	.byte	'ESR0T',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	469
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPDIS_Bits',0,10,218,8,3
	.word	73819
	.byte	10
	.byte	'_Ifx_SCU_TRAPSET_Bits',0,10,221,8,16,4,11
	.byte	'ESR0T',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	469
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPSET_Bits',0,10,228,8,3
	.word	73972
	.byte	10
	.byte	'_Ifx_SCU_TRAPSTAT_Bits',0,10,231,8,16,4,11
	.byte	'ESR0T',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	469
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPSTAT_Bits',0,10,238,8,3
	.word	74125
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU_CON0_Bits',0,10,241,8,16,4,11
	.byte	'ENDINIT',0,4
	.word	8638
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	8638
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	8638
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	8638
	.byte	16,0,2,35,0,0,21
	.byte	'Ifx_SCU_WDTCPU_CON0_Bits',0,10,247,8,3
	.word	74280
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU_CON1_Bits',0,10,250,8,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	2,6,2,35,0,11
	.byte	'IR0',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	492
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_WDTCPU_CON1_Bits',0,10,134,9,3
	.word	74410
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU_SR_Bits',0,10,137,9,16,4,11
	.byte	'AE',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	492
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	509
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_WDTCPU_SR_Bits',0,10,150,9,3
	.word	74648
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON0_Bits',0,10,153,9,16,4,11
	.byte	'ENDINIT',0,4
	.word	8638
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	8638
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	8638
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	8638
	.byte	16,0,2,35,0,0,21
	.byte	'Ifx_SCU_WDTS_CON0_Bits',0,10,159,9,3
	.word	74871
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON1_Bits',0,10,162,9,16,4,11
	.byte	'CLRIRF',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'IR0',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	492
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_WDTS_CON1_Bits',0,10,175,9,3
	.word	74997
	.byte	10
	.byte	'_Ifx_SCU_WDTS_SR_Bits',0,10,178,9,16,4,11
	.byte	'AE',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	492
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	509
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_WDTS_SR_Bits',0,10,191,9,3
	.word	75249
	.byte	12,10,199,9,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56790
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ACCEN0',0,10,204,9,3
	.word	75468
	.byte	12,10,207,9,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57347
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ACCEN1',0,10,212,9,3
	.word	75532
	.byte	12,10,215,9,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57424
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ARSTDIS',0,10,220,9,3
	.word	75596
	.byte	12,10,223,9,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57560
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON0',0,10,228,9,3
	.word	75661
	.byte	12,10,231,9,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57840
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON1',0,10,236,9,3
	.word	75726
	.byte	12,10,239,9,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58078
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON2',0,10,244,9,3
	.word	75791
	.byte	12,10,247,9,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58206
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON3',0,10,252,9,3
	.word	75856
	.byte	12,10,255,9,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58449
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON4',0,10,132,10,3
	.word	75921
	.byte	12,10,135,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58684
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON5',0,10,140,10,3
	.word	75986
	.byte	12,10,143,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58812
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON6',0,10,148,10,3
	.word	76051
	.byte	12,10,151,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58912
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON7',0,10,156,10,3
	.word	76116
	.byte	12,10,159,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59012
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CHIPID',0,10,164,10,3
	.word	76181
	.byte	12,10,167,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59220
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_DTSCON',0,10,172,10,3
	.word	76245
	.byte	12,10,175,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59385
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_DTSLIM',0,10,180,10,3
	.word	76309
	.byte	12,10,183,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59568
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_DTSSTAT',0,10,188,10,3
	.word	76373
	.byte	12,10,191,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59722
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EICR',0,10,196,10,3
	.word	76438
	.byte	12,10,199,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60086
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EIFR',0,10,204,10,3
	.word	76500
	.byte	12,10,207,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60297
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EMSR',0,10,212,10,3
	.word	76562
	.byte	12,10,215,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60549
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ESRCFG',0,10,220,10,3
	.word	76624
	.byte	12,10,223,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60667
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ESROCFG',0,10,228,10,3
	.word	76688
	.byte	12,10,231,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60778
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVR13CON',0,10,236,10,3
	.word	76753
	.byte	12,10,239,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60941
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVR33CON',0,10,244,10,3
	.word	76819
	.byte	12,10,247,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61104
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRADCSTAT',0,10,252,10,3
	.word	76885
	.byte	12,10,255,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61262
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRDVSTAT',0,10,132,11,3
	.word	76953
	.byte	12,10,135,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61427
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRMONCTRL',0,10,140,11,3
	.word	77020
	.byte	12,10,143,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61756
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVROSCCTRL',0,10,148,11,3
	.word	77088
	.byte	12,10,151,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61977
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVROVMON',0,10,156,11,3
	.word	77156
	.byte	12,10,159,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62140
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRRSTCON',0,10,164,11,3
	.word	77222
	.byte	12,10,167,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62412
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF1',0,10,172,11,3
	.word	77289
	.byte	12,10,175,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62565
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF2',0,10,180,11,3
	.word	77358
	.byte	12,10,183,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62721
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF3',0,10,188,11,3
	.word	77427
	.byte	12,10,191,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62883
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF4',0,10,196,11,3
	.word	77496
	.byte	12,10,199,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63026
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF5',0,10,204,11,3
	.word	77565
	.byte	12,10,207,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63191
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF6',0,10,212,11,3
	.word	77634
	.byte	12,10,215,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63336
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCTRL1',0,10,220,11,3
	.word	77703
	.byte	12,10,223,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63517
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCTRL2',0,10,228,11,3
	.word	77771
	.byte	12,10,231,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63691
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCTRL3',0,10,236,11,3
	.word	77839
	.byte	12,10,239,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63851
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCTRL4',0,10,244,11,3
	.word	77907
	.byte	12,10,247,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63995
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSTAT',0,10,252,11,3
	.word	77975
	.byte	12,10,255,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64269
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRTRIM',0,10,132,12,3
	.word	78040
	.byte	12,10,135,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64408
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRUVMON',0,10,140,12,3
	.word	78105
	.byte	12,10,143,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64571
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EXTCON',0,10,148,12,3
	.word	78171
	.byte	12,10,151,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64789
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_FDR',0,10,156,12,3
	.word	78235
	.byte	12,10,159,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64952
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_FMR',0,10,164,12,3
	.word	78296
	.byte	12,10,167,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65288
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ID',0,10,172,12,3
	.word	78357
	.byte	12,10,175,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65395
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_IGCR',0,10,180,12,3
	.word	78417
	.byte	12,10,183,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65847
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_IN',0,10,188,12,3
	.word	78479
	.byte	12,10,191,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65946
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_IOCR',0,10,196,12,3
	.word	78539
	.byte	12,10,199,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66096
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_LBISTCTRL0',0,10,204,12,3
	.word	78601
	.byte	12,10,207,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66245
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_LBISTCTRL1',0,10,212,12,3
	.word	78669
	.byte	12,10,215,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66406
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_LBISTCTRL2',0,10,220,12,3
	.word	78737
	.byte	12,10,223,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66536
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_LCLCON',0,10,228,12,3
	.word	78805
	.byte	12,10,231,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66668
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_LCLTEST',0,10,236,12,3
	.word	78869
	.byte	12,10,239,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66783
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_MANID',0,10,244,12,3
	.word	78934
	.byte	12,10,247,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66894
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_OMR',0,10,252,12,3
	.word	78997
	.byte	12,10,255,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67052
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_OSCCON',0,10,132,13,3
	.word	79058
	.byte	12,10,135,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67464
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_OUT',0,10,140,13,3
	.word	79122
	.byte	12,10,143,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67565
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_OVCCON',0,10,148,13,3
	.word	79183
	.byte	12,10,151,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67832
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_OVCENABLE',0,10,156,13,3
	.word	79247
	.byte	12,10,159,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67968
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PDISC',0,10,164,13,3
	.word	79314
	.byte	12,10,167,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68079
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PDR',0,10,172,13,3
	.word	79377
	.byte	12,10,175,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68212
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PDRR',0,10,180,13,3
	.word	79438
	.byte	12,10,183,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68415
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLCON0',0,10,188,13,3
	.word	79500
	.byte	12,10,191,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68771
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLCON1',0,10,196,13,3
	.word	79565
	.byte	12,10,199,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68949
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLCON2',0,10,204,13,3
	.word	79630
	.byte	12,10,207,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69049
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLERAYCON0',0,10,212,13,3
	.word	79695
	.byte	12,10,215,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69419
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLERAYCON1',0,10,220,13,3
	.word	79764
	.byte	12,10,223,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69605
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLERAYSTAT',0,10,228,13,3
	.word	79833
	.byte	12,10,231,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69803
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLSTAT',0,10,236,13,3
	.word	79902
	.byte	12,10,239,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70036
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMCSR',0,10,244,13,3
	.word	79967
	.byte	12,10,247,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70188
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMSWCR0',0,10,252,13,3
	.word	80030
	.byte	12,10,255,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70755
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMSWCR1',0,10,132,14,3
	.word	80095
	.byte	12,10,135,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71049
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMSWCR2',0,10,140,14,3
	.word	80160
	.byte	12,10,143,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71327
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMSWSTAT',0,10,148,14,3
	.word	80225
	.byte	12,10,151,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71823
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMSWSTATCLR',0,10,156,14,3
	.word	80291
	.byte	12,10,159,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72345
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_RSTCON',0,10,164,14,3
	.word	80360
	.byte	12,10,167,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72136
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_RSTCON2',0,10,172,14,3
	.word	80424
	.byte	12,10,175,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72556
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_RSTSTAT',0,10,180,14,3
	.word	80489
	.byte	12,10,183,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72988
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_SAFECON',0,10,188,14,3
	.word	80554
	.byte	12,10,191,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	73084
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_STSTAT',0,10,196,14,3
	.word	80619
	.byte	12,10,199,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	73344
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_SWRSTCON',0,10,204,14,3
	.word	80683
	.byte	12,10,207,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	73469
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_SYSCON',0,10,212,14,3
	.word	80749
	.byte	12,10,215,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	73666
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPCLR',0,10,220,14,3
	.word	80813
	.byte	12,10,223,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	73819
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPDIS',0,10,228,14,3
	.word	80878
	.byte	12,10,231,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	73972
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPSET',0,10,236,14,3
	.word	80943
	.byte	12,10,239,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	74125
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPSTAT',0,10,244,14,3
	.word	81008
	.byte	12,10,247,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	74280
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTCPU_CON0',0,10,252,14,3
	.word	81074
	.byte	12,10,255,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	74410
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTCPU_CON1',0,10,132,15,3
	.word	81143
	.byte	12,10,135,15,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	74648
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTCPU_SR',0,10,140,15,3
	.word	81212
	.byte	12,10,143,15,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	74871
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTS_CON0',0,10,148,15,3
	.word	81279
	.byte	12,10,151,15,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	74997
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTS_CON1',0,10,156,15,3
	.word	81346
	.byte	12,10,159,15,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	75249
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTS_SR',0,10,164,15,3
	.word	81413
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU',0,10,175,15,25,12,13
	.byte	'CON0',0
	.word	81074
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	81143
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	81212
	.byte	4,2,35,8,0,16
	.word	81478
	.byte	21
	.byte	'Ifx_SCU_WDTCPU',0,10,180,15,3
	.word	81541
	.byte	10
	.byte	'_Ifx_SCU_WDTS',0,10,183,15,25,12,13
	.byte	'CON0',0
	.word	81279
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	81346
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	81413
	.byte	4,2,35,8,0,16
	.word	81570
	.byte	21
	.byte	'Ifx_SCU_WDTS',0,10,188,15,3
	.word	81631
	.byte	17,3,83,9,1,18
	.byte	'IfxPort_InputMode_undefined',0,127,18
	.byte	'IfxPort_InputMode_noPullDevice',0,0,18
	.byte	'IfxPort_InputMode_pullDown',0,8,18
	.byte	'IfxPort_InputMode_pullUp',0,16,0,21
	.byte	'IfxPort_InputMode',0,3,89,3
	.word	81658
	.byte	17,3,120,9,1,18
	.byte	'IfxPort_OutputIdx_general',0,128,1,18
	.byte	'IfxPort_OutputIdx_alt1',0,136,1,18
	.byte	'IfxPort_OutputIdx_alt2',0,144,1,18
	.byte	'IfxPort_OutputIdx_alt3',0,152,1,18
	.byte	'IfxPort_OutputIdx_alt4',0,160,1,18
	.byte	'IfxPort_OutputIdx_alt5',0,168,1,18
	.byte	'IfxPort_OutputIdx_alt6',0,176,1,18
	.byte	'IfxPort_OutputIdx_alt7',0,184,1,0,21
	.byte	'IfxPort_OutputIdx',0,3,130,1,3
	.word	81809
	.byte	17,3,134,1,9,1,18
	.byte	'IfxPort_OutputMode_pushPull',0,128,1,18
	.byte	'IfxPort_OutputMode_openDrain',0,192,1,0,21
	.byte	'IfxPort_OutputMode',0,3,138,1,3
	.word	82053
	.byte	17,3,144,1,9,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed1',0,0,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed2',0,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed3',0,2,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed4',0,3,18
	.byte	'IfxPort_PadDriver_lvdsSpeed1',0,4,18
	.byte	'IfxPort_PadDriver_lvdsSpeed2',0,5,18
	.byte	'IfxPort_PadDriver_lvdsSpeed3',0,6,18
	.byte	'IfxPort_PadDriver_lvdsSpeed4',0,7,18
	.byte	'IfxPort_PadDriver_ttlSpeed1',0,8,18
	.byte	'IfxPort_PadDriver_ttlSpeed2',0,9,18
	.byte	'IfxPort_PadDriver_ttlSpeed3',0,10,18
	.byte	'IfxPort_PadDriver_ttlSpeed4',0,11,0,21
	.byte	'IfxPort_PadDriver',0,3,158,1,3
	.word	82151
	.byte	21
	.byte	'IfxPort_State',0,3,178,1,3
	.word	8289
	.byte	23,3,190,1,9,8,13
	.byte	'port',0
	.word	8284
	.byte	4,2,35,0,13
	.byte	'pinIndex',0
	.word	492
	.byte	1,2,35,4,0,21
	.byte	'IfxPort_Pin',0,3,194,1,3
	.word	82616
	.byte	16
	.word	54531
	.byte	3
	.word	82676
	.byte	23,11,59,15,12,13
	.byte	'module',0
	.word	82681
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	82616
	.byte	8,2,35,4,0,24
	.word	82686
	.byte	21
	.byte	'IfxCif_Clk_In',0,11,63,3
	.word	82721
	.byte	23,11,66,15,12,13
	.byte	'module',0
	.word	82681
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	82616
	.byte	8,2,35,4,0,24
	.word	82748
	.byte	21
	.byte	'IfxCif_D_In',0,11,70,3
	.word	82783
	.byte	23,11,73,15,12,13
	.byte	'module',0
	.word	82681
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	82616
	.byte	8,2,35,4,0,24
	.word	82808
	.byte	21
	.byte	'IfxCif_Hsnc_In',0,11,77,3
	.word	82843
	.byte	23,11,80,15,12,13
	.byte	'module',0
	.word	82681
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	82616
	.byte	8,2,35,4,0,24
	.word	82871
	.byte	21
	.byte	'IfxCif_Vsnc_In',0,11,84,3
	.word	82906
.L44:
	.byte	24
	.word	82686
.L45:
	.byte	24
	.word	82748
.L46:
	.byte	24
	.word	82748
.L47:
	.byte	24
	.word	82748
.L48:
	.byte	24
	.word	82748
.L49:
	.byte	24
	.word	82748
.L50:
	.byte	24
	.word	82748
.L51:
	.byte	24
	.word	82748
.L52:
	.byte	24
	.word	82748
.L53:
	.byte	24
	.word	82748
.L54:
	.byte	24
	.word	82748
.L55:
	.byte	24
	.word	82748
.L56:
	.byte	24
	.word	82748
.L57:
	.byte	24
	.word	82748
.L58:
	.byte	24
	.word	82748
.L59:
	.byte	24
	.word	82748
.L60:
	.byte	24
	.word	82748
.L61:
	.byte	24
	.word	82808
.L62:
	.byte	24
	.word	82871
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L4:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,21,0,54,15,39,12,0,0,3,15,0,73,19,0,0,4,46,1,3,8,32,13
	.byte	58,15,59,15,57,15,54,15,39,12,0,0,5,5,0,3,8,58,15,59,15,57,15,73,19,0,0,6,11,0,0,0,7,36,0,3,8,11,15,62
	.byte	15,0,0,8,46,1,3,8,32,13,58,15,59,15,57,15,73,19,54,15,39,12,0,0,9,59,0,3,8,0,0,10,19,1,3,8,58,15,59,15
	.byte	57,15,11,15,0,0,11,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,12,23,1,58,15,59,15,57,15,11,15,0,0,13,13
	.byte	0,3,8,73,19,11,15,56,9,0,0,14,1,1,11,15,73,19,0,0,15,33,0,47,15,0,0,16,53,0,73,19,0,0,17,4,1,58,15,59
	.byte	15,57,15,11,15,0,0,18,40,0,3,8,28,13,0,0,19,46,1,49,19,0,0,20,5,0,49,19,0,0,21,22,0,3,8,58,15,59,15,57
	.byte	15,73,19,0,0,22,21,0,54,15,0,0,23,19,1,58,15,59,15,57,15,11,15,0,0,24,38,0,73,19,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L5:
	.word	.L64-.L63
.L63:
	.half	3
	.word	.L66-.L65
.L65:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg',0
	.byte	0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_Intrinsics.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Port\\Std\\IfxPort.h',0
	.byte	0,0,0
	.byte	'IfxPort_regdef.h',0,1,0,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCif_PinMap.c',0,0,0,0
	.byte	'IfxCif_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Platform_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Ifx_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxScu_cfg.h',0
	.byte	0,0,0
	.byte	'IfxScu_regdef.h',0,1,0,0
	.byte	'..\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\_PinMap\\IfxCif_PinMap.h',0,0,0,0,0
.L66:
.L64:
	.sdecl	'.debug_info',debug,cluster('IfxCif_CLK_P00_7_IN')
	.sect	'.debug_info'
.L6:
	.word	270
	.half	3
	.word	.L7
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCif_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCif_CLK_P00_7_IN',0,5,48,15
	.word	.L44
	.byte	1,5,3
	.word	IfxCif_CLK_P00_7_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCif_CLK_P00_7_IN')
	.sect	'.debug_abbrev'
.L7:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCif_D0_P02_0_IN')
	.sect	'.debug_info'
.L8:
	.word	269
	.half	3
	.word	.L9
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCif_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCif_D0_P02_0_IN',0,5,49,13
	.word	.L45
	.byte	1,5,3
	.word	IfxCif_D0_P02_0_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCif_D0_P02_0_IN')
	.sect	'.debug_abbrev'
.L9:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCif_D10_P00_1_IN')
	.sect	'.debug_info'
.L10:
	.word	270
	.half	3
	.word	.L11
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCif_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCif_D10_P00_1_IN',0,5,50,13
	.word	.L46
	.byte	1,5,3
	.word	IfxCif_D10_P00_1_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCif_D10_P00_1_IN')
	.sect	'.debug_abbrev'
.L11:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCif_D11_P00_2_IN')
	.sect	'.debug_info'
.L12:
	.word	270
	.half	3
	.word	.L13
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCif_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCif_D11_P00_2_IN',0,5,51,13
	.word	.L47
	.byte	1,5,3
	.word	IfxCif_D11_P00_2_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCif_D11_P00_2_IN')
	.sect	'.debug_abbrev'
.L13:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCif_D12_P00_3_IN')
	.sect	'.debug_info'
.L14:
	.word	270
	.half	3
	.word	.L15
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCif_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCif_D12_P00_3_IN',0,5,52,13
	.word	.L48
	.byte	1,5,3
	.word	IfxCif_D12_P00_3_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCif_D12_P00_3_IN')
	.sect	'.debug_abbrev'
.L15:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCif_D13_P00_4_IN')
	.sect	'.debug_info'
.L16:
	.word	270
	.half	3
	.word	.L17
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCif_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCif_D13_P00_4_IN',0,5,53,13
	.word	.L49
	.byte	1,5,3
	.word	IfxCif_D13_P00_4_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCif_D13_P00_4_IN')
	.sect	'.debug_abbrev'
.L17:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCif_D14_P00_5_IN')
	.sect	'.debug_info'
.L18:
	.word	270
	.half	3
	.word	.L19
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCif_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCif_D14_P00_5_IN',0,5,54,13
	.word	.L50
	.byte	1,5,3
	.word	IfxCif_D14_P00_5_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCif_D14_P00_5_IN')
	.sect	'.debug_abbrev'
.L19:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCif_D15_P00_6_IN')
	.sect	'.debug_info'
.L20:
	.word	270
	.half	3
	.word	.L21
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCif_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCif_D15_P00_6_IN',0,5,55,13
	.word	.L51
	.byte	1,5,3
	.word	IfxCif_D15_P00_6_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCif_D15_P00_6_IN')
	.sect	'.debug_abbrev'
.L21:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCif_D1_P02_1_IN')
	.sect	'.debug_info'
.L22:
	.word	269
	.half	3
	.word	.L23
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCif_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCif_D1_P02_1_IN',0,5,56,13
	.word	.L52
	.byte	1,5,3
	.word	IfxCif_D1_P02_1_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCif_D1_P02_1_IN')
	.sect	'.debug_abbrev'
.L23:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCif_D2_P02_2_IN')
	.sect	'.debug_info'
.L24:
	.word	269
	.half	3
	.word	.L25
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCif_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCif_D2_P02_2_IN',0,5,57,13
	.word	.L53
	.byte	1,5,3
	.word	IfxCif_D2_P02_2_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCif_D2_P02_2_IN')
	.sect	'.debug_abbrev'
.L25:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCif_D3_P02_3_IN')
	.sect	'.debug_info'
.L26:
	.word	269
	.half	3
	.word	.L27
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCif_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCif_D3_P02_3_IN',0,5,58,13
	.word	.L54
	.byte	1,5,3
	.word	IfxCif_D3_P02_3_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCif_D3_P02_3_IN')
	.sect	'.debug_abbrev'
.L27:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCif_D4_P02_4_IN')
	.sect	'.debug_info'
.L28:
	.word	269
	.half	3
	.word	.L29
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCif_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCif_D4_P02_4_IN',0,5,59,13
	.word	.L55
	.byte	1,5,3
	.word	IfxCif_D4_P02_4_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCif_D4_P02_4_IN')
	.sect	'.debug_abbrev'
.L29:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCif_D5_P02_5_IN')
	.sect	'.debug_info'
.L30:
	.word	269
	.half	3
	.word	.L31
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCif_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCif_D5_P02_5_IN',0,5,60,13
	.word	.L56
	.byte	1,5,3
	.word	IfxCif_D5_P02_5_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCif_D5_P02_5_IN')
	.sect	'.debug_abbrev'
.L31:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCif_D6_P02_6_IN')
	.sect	'.debug_info'
.L32:
	.word	269
	.half	3
	.word	.L33
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCif_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCif_D6_P02_6_IN',0,5,61,13
	.word	.L57
	.byte	1,5,3
	.word	IfxCif_D6_P02_6_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCif_D6_P02_6_IN')
	.sect	'.debug_abbrev'
.L33:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCif_D7_P02_7_IN')
	.sect	'.debug_info'
.L34:
	.word	269
	.half	3
	.word	.L35
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCif_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCif_D7_P02_7_IN',0,5,62,13
	.word	.L58
	.byte	1,5,3
	.word	IfxCif_D7_P02_7_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCif_D7_P02_7_IN')
	.sect	'.debug_abbrev'
.L35:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCif_D8_P02_8_IN')
	.sect	'.debug_info'
.L36:
	.word	269
	.half	3
	.word	.L37
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCif_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCif_D8_P02_8_IN',0,5,63,13
	.word	.L59
	.byte	1,5,3
	.word	IfxCif_D8_P02_8_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCif_D8_P02_8_IN')
	.sect	'.debug_abbrev'
.L37:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCif_D9_P00_0_IN')
	.sect	'.debug_info'
.L38:
	.word	269
	.half	3
	.word	.L39
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCif_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCif_D9_P00_0_IN',0,5,64,13
	.word	.L60
	.byte	1,5,3
	.word	IfxCif_D9_P00_0_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCif_D9_P00_0_IN')
	.sect	'.debug_abbrev'
.L39:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCif_HSNC_P00_9_IN')
	.sect	'.debug_info'
.L40:
	.word	271
	.half	3
	.word	.L41
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCif_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCif_HSNC_P00_9_IN',0,5,65,16
	.word	.L61
	.byte	1,5,3
	.word	IfxCif_HSNC_P00_9_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCif_HSNC_P00_9_IN')
	.sect	'.debug_abbrev'
.L41:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCif_VSNC_P00_8_IN')
	.sect	'.debug_info'
.L42:
	.word	271
	.half	3
	.word	.L43
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCif_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCif_VSNC_P00_8_IN',0,5,66,16
	.word	.L62
	.byte	1,5,3
	.word	IfxCif_VSNC_P00_8_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCif_VSNC_P00_8_IN')
	.sect	'.debug_abbrev'
.L43:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	; Module end
