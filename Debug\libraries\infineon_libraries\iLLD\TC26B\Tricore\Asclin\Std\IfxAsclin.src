	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc36188a --dep-file=IfxAsclin.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2\\\\xf_asr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/infineon_libraries/iLLD/TC26B/Tricore/Asclin/Std/IfxAsclin.src ../libraries/infineon_libraries/iLLD/TC26B/Tricore/Asclin/Std/IfxAsclin.c"
	.compiler_name		"ctc"
	;source	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Asclin/Std/IfxAsclin.c'

	
$TC16X
	.sdecl	'.rodata.IfxAsclin..1.cnt',data,rom
	.sect	'.rodata.IfxAsclin..1.cnt'
	.align	4
.1.cnt:	.type	object
	.size	.1.cnt,8
	.word	-755914244,1062232653
	
	.sdecl	'.text.IfxAsclin.IfxAsclin_disableModule',code,cluster('IfxAsclin_disableModule')
	.sect	'.text.IfxAsclin.IfxAsclin_disableModule'
	.align	2
	
	.global	IfxAsclin_disableModule
; Function IfxAsclin_disableModule
.L88:
IfxAsclin_disableModule:	.type	func
	mov.aa	a15,a4
.L429:
	call	IfxScuWdt_getCpuWatchdogPassword
.L428:
	mov	d8,d2
.L431:
	mov	d4,d8
.L430:
	call	IfxScuWdt_clearCpuEndinit
.L308:
	ld.bu	d15,[a15]
.L682:
	or	d15,#1
	st.b	[a15],d15
.L309:
	mov	d4,d8
.L432:
	call	IfxScuWdt_setCpuEndinit
.L433:
	ret
.L304:
	
__IfxAsclin_disableModule_function_end:
	.size	IfxAsclin_disableModule,__IfxAsclin_disableModule_function_end-IfxAsclin_disableModule
.L179:
	; End of function
	
	.sdecl	'.text.IfxAsclin.IfxAsclin_enableAscErrorFlags',code,cluster('IfxAsclin_enableAscErrorFlags')
	.sect	'.text.IfxAsclin.IfxAsclin_enableAscErrorFlags'
	.align	2
	
	.global	IfxAsclin_enableAscErrorFlags
; Function IfxAsclin_enableAscErrorFlags
.L90:
IfxAsclin_enableAscErrorFlags:	.type	func
	jeq	d4,#0,.L2
.L687:
	mov	d15,#1
.L688:
	j	.L3
.L2:
	mov	d15,#0
.L3:
	ld.bu	d0,[a4]66
.L689:
	insert	d15,d0,d15,#0,#1
	st.b	[a4]66,d15
.L318:
	jeq	d5,#0,.L4
.L690:
	mov	d15,#1
.L691:
	j	.L5
.L4:
	mov	d15,#0
.L5:
	ld.bu	d0,[a4]67
.L692:
	insert	d15,d0,d15,#2,#1
	st.b	[a4]67,d15
.L325:
	ret
.L313:
	
__IfxAsclin_enableAscErrorFlags_function_end:
	.size	IfxAsclin_enableAscErrorFlags,__IfxAsclin_enableAscErrorFlags_function_end-IfxAsclin_enableAscErrorFlags
.L184:
	; End of function
	
	.sdecl	'.text.IfxAsclin.IfxAsclin_enableModule',code,cluster('IfxAsclin_enableModule')
	.sect	'.text.IfxAsclin.IfxAsclin_enableModule'
	.align	2
	
	.global	IfxAsclin_enableModule
; Function IfxAsclin_enableModule
.L92:
IfxAsclin_enableModule:	.type	func
	mov.aa	a15,a4
.L435:
	call	IfxScuWdt_getCpuWatchdogPassword
.L434:
	mov	d15,d2
.L437:
	mov	d4,d15
.L436:
	call	IfxScuWdt_clearCpuEndinit
.L335:
	ld.bu	d0,[a15]
.L697:
	insert	d0,d0,#0,#0,#1
	st.b	[a15],d0
.L336:
	mov	d4,d15
.L438:
	call	IfxScuWdt_setCpuEndinit
.L439:
	ret
.L331:
	
__IfxAsclin_enableModule_function_end:
	.size	IfxAsclin_enableModule,__IfxAsclin_enableModule_function_end-IfxAsclin_enableModule
.L189:
	; End of function
	
	.sdecl	'.text.IfxAsclin.IfxAsclin_getAddress',code,cluster('IfxAsclin_getAddress')
	.sect	'.text.IfxAsclin.IfxAsclin_getAddress'
	.align	2
	
	.global	IfxAsclin_getAddress
; Function IfxAsclin_getAddress
.L94:
IfxAsclin_getAddress:	.type	func
	jge	d4,#4,.L6
.L571:
	mul	d15,d4,#8
.L572:
	movh.a	a15,#@his(IfxAsclin_cfg_indexMap)
	lea	a15,[a15]@los(IfxAsclin_cfg_indexMap)
.L573:
	addsc.a	a15,a15,d15,#0
.L574:
	ld.a	a2,[a15]
.L440:
	j	.L7
.L6:
	mov.a	a2,#0
.L7:
	j	.L8
.L8:
	ret
.L246:
	
__IfxAsclin_getAddress_function_end:
	.size	IfxAsclin_getAddress,__IfxAsclin_getAddress_function_end-IfxAsclin_getAddress
.L139:
	; End of function
	
	.sdecl	'.text.IfxAsclin.IfxAsclin_getFaFrequency',code,cluster('IfxAsclin_getFaFrequency')
	.sect	'.text.IfxAsclin.IfxAsclin_getFaFrequency'
	.align	2
	
	.global	IfxAsclin_getFaFrequency
; Function IfxAsclin_getFaFrequency
.L96:
IfxAsclin_getFaFrequency:	.type	func
	ld.bu	d15,[a4]76
	and	d15,#31
.L579:
	j	.L9
.L9:
	mov	d0,#0
	jeq	d15,d0,.L10
.L580:
	mov	d0,#1
	jeq	d15,d0,.L11
.L581:
	mov	d0,#2
	jeq	d15,d0,.L12
.L582:
	mov	d0,#4
	jeq	d15,d0,.L13
.L583:
	mov	d0,#8
	jeq	d15,d0,.L14
.L584:
	mov	d0,#16
	jeq	d15,d0,.L15
	j	.L16
.L10:
	mov	d2,#0
.L442:
	j	.L17
.L11:
	call	IfxScuCcu_getSpbFrequency
.L441:
	j	.L18
.L12:
	call	IfxScuCcu_getOsc0Frequency
.L443:
	j	.L19
.L13:
	call	IfxScuCcu_getPllErayFrequency
.L444:
	j	.L20
.L14:
	call	IfxScuCcu_getBaud2Frequency
.L445:
	j	.L21
.L15:
	call	IfxScuCcu_getBaud1Frequency
.L446:
	j	.L22
.L16:
	mov	d2,#0
.L447:
	j	.L23
.L23:
.L22:
.L21:
.L20:
.L19:
.L18:
.L17:
	j	.L24
.L24:
	ret
.L251:
	
__IfxAsclin_getFaFrequency_function_end:
	.size	IfxAsclin_getFaFrequency,__IfxAsclin_getFaFrequency_function_end-IfxAsclin_getFaFrequency
.L144:
	; End of function
	
	.sdecl	'.text.IfxAsclin.IfxAsclin_getIndex',code,cluster('IfxAsclin_getIndex')
	.sect	'.text.IfxAsclin.IfxAsclin_getIndex'
	.align	2
	
	.global	IfxAsclin_getIndex
; Function IfxAsclin_getIndex
.L98:
IfxAsclin_getIndex:	.type	func
	mov	d2,#-1
.L448:
	mov	d0,#0
.L449:
	j	.L25
.L26:
	mul	d15,d0,#8
.L589:
	movh.a	a15,#@his(IfxAsclin_cfg_indexMap)
	lea	a15,[a15]@los(IfxAsclin_cfg_indexMap)
.L590:
	addsc.a	a15,a15,d15,#0
.L591:
	ld.a	a15,[a15]
.L592:
	jne.a	a15,a4,.L27
.L593:
	mul	d15,d0,#8
.L594:
	movh.a	a15,#@his(IfxAsclin_cfg_indexMap)
	lea	a15,[a15]@los(IfxAsclin_cfg_indexMap)
.L595:
	addsc.a	a15,a15,d15,#0
.L596:
	ld.w	d15,[a15]4
.L597:
	extr	d2,d15,#0,#8
.L598:
	j	.L28
.L27:
	add	d0,#1
.L25:
	jlt.u	d0,#4,.L26
.L28:
	j	.L29
.L29:
	ret
.L260:
	
__IfxAsclin_getIndex_function_end:
	.size	IfxAsclin_getIndex,__IfxAsclin_getIndex_function_end-IfxAsclin_getIndex
.L149:
	; End of function
	
	.sdecl	'.text.IfxAsclin.IfxAsclin_getOvsFrequency',code,cluster('IfxAsclin_getOvsFrequency')
	.sect	'.text.IfxAsclin.IfxAsclin_getOvsFrequency'
	.align	2
	
	.global	IfxAsclin_getOvsFrequency
; Function IfxAsclin_getOvsFrequency
.L100:
IfxAsclin_getOvsFrequency:	.type	func
	mov.aa	a15,a4
.L451:
	mov.aa	a4,a15
	call	IfxAsclin_getPdFrequency
.L450:
	ld.hu	d15,[a15]34
	extr.u	d15,d15,#0,#12
	itof	d15,d15
.L603:
	mul.f	d15,d2,d15
.L604:
	ld.hu	d0,[a15]32
	extr.u	d0,d0,#0,#12
	itof	d0,d0
.L605:
	div.f	d2,d15,d0
.L606:
	j	.L30
.L30:
	ret
.L265:
	
__IfxAsclin_getOvsFrequency_function_end:
	.size	IfxAsclin_getOvsFrequency,__IfxAsclin_getOvsFrequency_function_end-IfxAsclin_getOvsFrequency
.L154:
	; End of function
	
	.sdecl	'.text.IfxAsclin.IfxAsclin_getPdFrequency',code,cluster('IfxAsclin_getPdFrequency')
	.sect	'.text.IfxAsclin.IfxAsclin_getPdFrequency'
	.align	2
	
	.global	IfxAsclin_getPdFrequency
; Function IfxAsclin_getPdFrequency
.L102:
IfxAsclin_getPdFrequency:	.type	func
	mov.aa	a15,a4
.L453:
	mov.aa	a4,a15
	call	IfxAsclin_getFaFrequency
.L452:
	ld.hu	d15,[a15]20
	extr.u	d15,d15,#0,#12
.L611:
	add	d15,#1
	itof	d15,d15
.L612:
	div.f	d2,d2,d15
.L613:
	j	.L31
.L31:
	ret
.L267:
	
__IfxAsclin_getPdFrequency_function_end:
	.size	IfxAsclin_getPdFrequency,__IfxAsclin_getPdFrequency_function_end-IfxAsclin_getPdFrequency
.L159:
	; End of function
	
	.sdecl	'.text.IfxAsclin.IfxAsclin_getShiftFrequency',code,cluster('IfxAsclin_getShiftFrequency')
	.sect	'.text.IfxAsclin.IfxAsclin_getShiftFrequency'
	.align	2
	
	.global	IfxAsclin_getShiftFrequency
; Function IfxAsclin_getShiftFrequency
.L104:
IfxAsclin_getShiftFrequency:	.type	func
	mov.aa	a15,a4
.L455:
	mov.aa	a4,a15
	call	IfxAsclin_getOvsFrequency
.L454:
	ld.bu	d15,[a15]22
	and	d15,#15
	itof	d15,d15
.L618:
	div.f	d2,d2,d15
.L619:
	j	.L32
.L32:
	ret
.L269:
	
__IfxAsclin_getShiftFrequency_function_end:
	.size	IfxAsclin_getShiftFrequency,__IfxAsclin_getShiftFrequency_function_end-IfxAsclin_getShiftFrequency
.L164:
	; End of function
	
	.sdecl	'.text.IfxAsclin.IfxAsclin_getSrcPointerEr',code,cluster('IfxAsclin_getSrcPointerEr')
	.sect	'.text.IfxAsclin.IfxAsclin_getSrcPointerEr'
	.align	2
	
	.global	IfxAsclin_getSrcPointerEr
; Function IfxAsclin_getSrcPointerEr
.L106:
IfxAsclin_getSrcPointerEr:	.type	func
	call	IfxAsclin_getIndex
.L456:
	mul	d15,d2,#12
	mov.a	a15,d15
	movh.a	a3,#61444
	add.a	a3,a15
	lea	a15,[a3]-32640
.L702:
	lea	a2,[a15]8
.L703:
	j	.L33
.L33:
	ret
.L341:
	
__IfxAsclin_getSrcPointerEr_function_end:
	.size	IfxAsclin_getSrcPointerEr,__IfxAsclin_getSrcPointerEr_function_end-IfxAsclin_getSrcPointerEr
.L194:
	; End of function
	
	.sdecl	'.text.IfxAsclin.IfxAsclin_getSrcPointerRx',code,cluster('IfxAsclin_getSrcPointerRx')
	.sect	'.text.IfxAsclin.IfxAsclin_getSrcPointerRx'
	.align	2
	
	.global	IfxAsclin_getSrcPointerRx
; Function IfxAsclin_getSrcPointerRx
.L108:
IfxAsclin_getSrcPointerRx:	.type	func
	call	IfxAsclin_getIndex
.L457:
	mul	d15,d2,#12
	mov.a	a15,d15
	movh.a	a3,#61444
	add.a	a3,a15
	lea	a2,[a3]-32640
.L708:
	add.a	a2,#4
.L709:
	j	.L34
.L34:
	ret
.L343:
	
__IfxAsclin_getSrcPointerRx_function_end:
	.size	IfxAsclin_getSrcPointerRx,__IfxAsclin_getSrcPointerRx_function_end-IfxAsclin_getSrcPointerRx
.L199:
	; End of function
	
	.sdecl	'.text.IfxAsclin.IfxAsclin_getSrcPointerTx',code,cluster('IfxAsclin_getSrcPointerTx')
	.sect	'.text.IfxAsclin.IfxAsclin_getSrcPointerTx'
	.align	2
	
	.global	IfxAsclin_getSrcPointerTx
; Function IfxAsclin_getSrcPointerTx
.L110:
IfxAsclin_getSrcPointerTx:	.type	func
	call	IfxAsclin_getIndex
.L458:
	mul	d15,d2,#12
	mov.a	a15,d15
	movh.a	a3,#61444
	add.a	a3,a15
	lea	a2,[a3]-32640
.L714:
	j	.L35
.L35:
	ret
.L345:
	
__IfxAsclin_getSrcPointerTx_function_end:
	.size	IfxAsclin_getSrcPointerTx,__IfxAsclin_getSrcPointerTx_function_end-IfxAsclin_getSrcPointerTx
.L204:
	; End of function
	
	.sdecl	'.text.IfxAsclin.IfxAsclin_read16',code,cluster('IfxAsclin_read16')
	.sect	'.text.IfxAsclin.IfxAsclin_read16'
	.align	2
	
	.global	IfxAsclin_read16
; Function IfxAsclin_read16
.L112:
IfxAsclin_read16:	.type	func
	lea	a15,[a4]72
.L459:
	j	.L36
.L37:
	ld.w	d15,[a15]
.L719:
	st.h	[a5],d15
.L720:
	add.a	a5,#2
.L721:
	add	d4,#-1
.L36:
	jge.u	d4,#1,.L37
.L722:
	mov	d2,d4
.L460:
	j	.L38
.L38:
	ret
.L347:
	
__IfxAsclin_read16_function_end:
	.size	IfxAsclin_read16,__IfxAsclin_read16_function_end-IfxAsclin_read16
.L209:
	; End of function
	
	.sdecl	'.text.IfxAsclin.IfxAsclin_read32',code,cluster('IfxAsclin_read32')
	.sect	'.text.IfxAsclin.IfxAsclin_read32'
	.align	2
	
	.global	IfxAsclin_read32
; Function IfxAsclin_read32
.L114:
IfxAsclin_read32:	.type	func
	lea	a15,[a4]72
.L461:
	j	.L39
.L40:
	ld.w	d15,[a15]
.L727:
	st.w	[a5],d15
.L728:
	add.a	a5,#4
.L729:
	add	d4,#-1
.L39:
	jge.u	d4,#1,.L40
.L730:
	mov	d2,d4
.L462:
	j	.L41
.L41:
	ret
.L354:
	
__IfxAsclin_read32_function_end:
	.size	IfxAsclin_read32,__IfxAsclin_read32_function_end-IfxAsclin_read32
.L214:
	; End of function
	
	.sdecl	'.text.IfxAsclin.IfxAsclin_read8',code,cluster('IfxAsclin_read8')
	.sect	'.text.IfxAsclin.IfxAsclin_read8'
	.align	2
	
	.global	IfxAsclin_read8
; Function IfxAsclin_read8
.L116:
IfxAsclin_read8:	.type	func
	lea	a15,[a4]72
.L463:
	j	.L42
.L43:
	ld.w	d15,[a15]
.L735:
	st.b	[a5],d15
.L736:
	add.a	a5,#1
.L737:
	add	d4,#-1
.L42:
	jge.u	d4,#1,.L43
.L738:
	mov	d2,d4
.L464:
	j	.L44
.L44:
	ret
.L360:
	
__IfxAsclin_read8_function_end:
	.size	IfxAsclin_read8,__IfxAsclin_read8_function_end-IfxAsclin_read8
.L219:
	; End of function
	
	.sdecl	'.text.IfxAsclin.IfxAsclin_resetModule',code,cluster('IfxAsclin_resetModule')
	.sect	'.text.IfxAsclin.IfxAsclin_resetModule'
	.align	2
	
	.global	IfxAsclin_resetModule
; Function IfxAsclin_resetModule
.L118:
IfxAsclin_resetModule:	.type	func
	mov.aa	a15,a4
.L466:
	call	IfxScuWdt_getCpuWatchdogPassword
.L465:
	mov	d8,d2
.L468:
	mov	d4,d8
.L467:
	call	IfxScuWdt_clearCpuEndinit
.L469:
	ld.bu	d15,[a15]244
.L624:
	or	d15,#1
	st.b	[a15]244,d15
.L625:
	ld.bu	d15,[a15]240
.L626:
	or	d15,#1
	st.b	[a15]240,d15
.L627:
	mov	d4,d8
.L470:
	call	IfxScuWdt_setCpuEndinit
.L471:
	j	.L45
.L46:
.L45:
	ld.bu	d15,[a15]244
.L628:
	jz.t	d15:1,.L46
.L629:
	mov	d4,d8
.L472:
	call	IfxScuWdt_clearCpuEndinit
.L473:
	ld.bu	d15,[a15]236
.L630:
	or	d15,#1
	st.b	[a15]236,d15
.L631:
	mov	d4,d8
.L474:
	call	IfxScuWdt_setCpuEndinit
.L475:
	ret
.L271:
	
__IfxAsclin_resetModule_function_end:
	.size	IfxAsclin_resetModule,__IfxAsclin_resetModule_function_end-IfxAsclin_resetModule
.L169:
	; End of function
	
	.sdecl	'.text.IfxAsclin.IfxAsclin_setBaudrateBitFields',code,cluster('IfxAsclin_setBaudrateBitFields')
	.sect	'.text.IfxAsclin.IfxAsclin_setBaudrateBitFields'
	.align	2
	
	.global	IfxAsclin_setBaudrateBitFields
; Function IfxAsclin_setBaudrateBitFields
.L120:
IfxAsclin_setBaudrateBitFields:	.type	func
	mov.aa	a15,a4
.L477:
	mov	e8,d5,d4
	mov	e10,d7,d6
.L373:
	ld.bu	d15,[a15]76
	and	d12,d15,#31
.L743:
	j	.L47
.L47:
	mov	d4,#0
.L476:
	mov.aa	a4,a15
	call	IfxAsclin_setClockSource
.L376:
	ld.hu	d0,[a15]20
.L744:
	add	d8,#-1
	extr.u	d1,d8,#0,#16
.L745:
	insert	d15,d0,d1,#0,#12
	st.h	[a15]20,d15
.L377:
	ld.hu	d15,[a15]34
.L478:
	insert	d15,d15,d9,#0,#12
.L479:
	st.h	[a15]34,d15
.L384:
	ld.hu	d15,[a15]32
.L480:
	insert	d15,d15,d10,#0,#12
.L481:
	st.h	[a15]32,d15
.L391:
	ld.bu	d15,[a15]22
.L482:
	insert	d15,d15,d11,#0,#4
.L483:
	st.b	[a15]22,d15
.L398:
	mov.aa	a4,a15
.L484:
	mov	d4,d12
	call	IfxAsclin_setClockSource
.L485:
	ret
.L366:
	
__IfxAsclin_setBaudrateBitFields_function_end:
	.size	IfxAsclin_setBaudrateBitFields,__IfxAsclin_setBaudrateBitFields_function_end-IfxAsclin_setBaudrateBitFields
.L224:
	; End of function
	
	.sdecl	'.text.IfxAsclin.IfxAsclin_setBitTiming',code,cluster('IfxAsclin_setBitTiming')
	.sect	'.text.IfxAsclin.IfxAsclin_setBitTiming'
	.align	2
	
	.global	IfxAsclin_setBitTiming
; Function IfxAsclin_setBitTiming
.L122:
IfxAsclin_setBitTiming:	.type	func
	sub.a	a10,#48
.L486:
	mov.aa	a15,a4
.L488:
	mov	d10,d4
.L489:
	mov	e8,d6,d5
	st.w	[a10]36,d7
.L293:
	ld.bu	d15,[a15]76
	and	d15,#31
	st.w	[a10]40,d15
.L636:
	j	.L48
.L48:
	mov.aa	a4,a15
	call	IfxAsclin_getPdFrequency
.L487:
	st.w	[a10]16,d2
.L491:
	add	d8,#1
.L637:
	max.u	d15,d8,#4
.L638:
	extr.u	d15,d15,#0,#8
.L492:
	st.w	[a10]28,d15
.L494:
	max.u	d15,d9,#1
.L493:
	extr.u	d15,d15,#0,#8
.L495:
	st.w	[a10]32,d15
.L497:
	ld.w	d15,[a10]28
.L496:
	utof	d15,d15
.L498:
	mul.f	d14,d10,d15
.L295:
	mov	d4,d14
.L499:
	call	__f_ftod
.L490:
	mov	e4,d3,d2
.L639:
	movh.a	a2,#@his(.1.cnt)
	lea	a2,[a2]@los(.1.cnt)
	ld.d	e6,[a2]0
.L640:
	call	__d_mul
	mov	e4,d3,d2
	call	__d_dtof
.L500:
	st.w	[a10]20,d2
.L501:
	mov	d15,#0
.L502:
	st.w	[a10]24,d15
.L504:
	ld.w	d15,[a10]16
.L503:
	div.f	d15,d15,d14
.L505:
	ftouz	d15,d15
.L506:
	st.w	[a10],d15
.L508:
	mov	d0,#1
.L509:
	sh	d15,d15,#-12
.L507:
	jeq	d15,#0,.L49
.L641:
	movh	d15,#16768
.L642:
	mul.f	d1,d10,d15
.L643:
	ld.w	d15,[a10]16
.L511:
	div.f	d15,d15,d1
.L512:
	ftouz	d15,d15
.L644:
	sh	d15,d15,#-12
.L645:
	jeq	d15,#0,.L50
.L646:
	j	.L51
.L50:
.L51:
.L49:
	ld.w	d15,[a10]
.L513:
	st.w	[a10]12,d15
.L515:
	mov	d15,#1
.L514:
	st.w	[a10]8,d15
.L517:
	mov	d15,#0
.L516:
	st.w	[a10]4,d15
.L519:
	utof	d0,d0
.L510:
	ld.w	d15,[a10]16
.L518:
	mul.f	d15,d15,d0
.L520:
	ld.w	d0,[a10]
.L521:
	utof	d0,d0
.L522:
	div.f	d15,d15,d0
.L523:
	sub.f	d4,d14,d15
	call	__f_ftod
	mov	e6,d3,d2
	mov	d4,#0
	mov	d5,#0
	call	__d_fgt
	jne	d2,#0,.L52
	sub.f	d8,d14,d15
.L525:
	j	.L53
.L52:
	sub.f	d15,d14,d15
.L524:
	insn.t	d8,d15:31,d15:31
.L53:
	ld.w	d15,[a10]20
.L526:
	cmp.f	d15,d8,d15
.L527:
	and	d15,#3
	ne	d15,d15,#0
.L647:
	jeq	d15,#0,.L54
.L648:
	mov	d15,#1
.L528:
	st.w	[a10]24,d15
.L54:
	mov	d9,#2
.L529:
	j	.L55
.L56:
	jne	d9,#2,.L57
.L649:
	mov	d12,#1
.L530:
	mov	d10,#1
.L531:
	j	.L58
.L57:
	ld.w	d15,[a10]4
.L532:
	mul	d0,d15,d9
.L650:
	ld.w	d15,[a10]8
.L533:
	div.u	e12,d0,d15
.L534:
	add	d10,d12,#1
.L58:
	j	.L59
.L60:
	utof	d0,d9
.L651:
	ld.w	d15,[a10]16
.L535:
	mul.f	d15,d15,d0
.L536:
	ld.w	d0,[a10]
.L537:
	madd	d0,d12,d9,d0
.L538:
	utof	d0,d0
.L652:
	div.f	d11,d15,d0
.L539:
	sub.f	d4,d14,d11
	call	__f_ftod
	mov	e6,d3,d2
	mov	d4,#0
	mov	d5,#0
	call	__d_fgt
	mov	d15,d2
	jne	d15,#0,.L61
	sub.f	d0,d14,d11
	j	.L62
.L61:
	sub.f	d15,d14,d11
	insn.t	d0,d15:31,d15:31
.L62:
	cmp.f	d15,d8,d0
	extr.u	d15,d15,#2,#1
.L653:
	jeq	d15,#0,.L63
.L654:
	mov	d8,d0
.L655:
	st.w	[a10]8,d9
.L656:
	ld.w	d15,[a10]
.L540:
	madd	d15,d12,d9,d15
.L541:
	st.w	[a10]12,d15
.L657:
	st.w	[a10]4,d12
.L63:
	add	d12,#1
.L59:
	jge.u	d10,d12,.L60
.L658:
	ld.w	d15,[a10]20
.L542:
	cmp.f	d15,d8,d15
.L543:
	and	d15,#3
	ne	d15,d15,#0
.L659:
	jeq	d15,#0,.L64
.L660:
	j	.L65
.L64:
	add	d9,#1
.L55:
	ld.w	d15,[a10]24
.L544:
	jne	d15,#0,.L66
.L661:
	ld.w	d15,[a10]
.L545:
	mul	d15,d9
.L546:
	mov	d0,#4095
.L662:
	jge.u	d0,d15,.L56
.L66:
.L65:
	mov	d4,#0
	mov.aa	a4,a15
.L547:
	call	IfxAsclin_setClockSource
.L548:
	ld.hu	d0,[a15]32
.L663:
	ld.hu	d15,[a10]12
.L664:
	insert	d15,d0,d15,#0,#12
	st.h	[a15]32,d15
.L665:
	ld.hu	d0,[a15]34
.L666:
	ld.hu	d15,[a10]8
.L667:
	insert	d15,d0,d15,#0,#12
	st.h	[a15]34,d15
.L668:
	ld.bu	d0,[a15]22
.L669:
	ld.w	d15,[a10]28
.L549:
	add	d15,#-1
.L550:
	extr.u	d15,d15,#0,#8
.L670:
	insert	d15,d0,d15,#0,#4
	st.b	[a15]22,d15
.L671:
	ld.bu	d0,[a15]23
.L672:
	ld.w	d15,[a10]32
.L551:
	insert	d15,d0,d15,#0,#4
.L552:
	st.b	[a15]23,d15
.L673:
	ld.w	d15,[a10]36
.L553:
	jeq	d15,#0,.L67
.L674:
	mov	d15,#1
.L554:
	j	.L68
.L67:
	mov	d15,#0
.L68:
	ld.bu	d0,[a15]23
.L675:
	insert	d15,d0,d15,#7,#1
	st.b	[a15]23,d15
.L676:
	mov.aa	a4,a15
.L555:
	ld.w	d4,[a10]40
	call	IfxAsclin_setClockSource
.L556:
	mov	d2,#1
.L677:
	j	.L69
.L69:
	ret
.L276:
	
__IfxAsclin_setBitTiming_function_end:
	.size	IfxAsclin_setBitTiming,__IfxAsclin_setBitTiming_function_end-IfxAsclin_setBitTiming
.L174:
	; End of function
	
	.sdecl	'.text.IfxAsclin.IfxAsclin_setClockSource',code,cluster('IfxAsclin_setClockSource')
	.sect	'.text.IfxAsclin.IfxAsclin_setClockSource'
	.align	2
	
	.global	IfxAsclin_setClockSource
; Function IfxAsclin_setClockSource
.L124:
IfxAsclin_setClockSource:	.type	func
	ld.bu	d15,[a4]76
.L750:
	insert	d15,d15,d4,#0,#5
	st.b	[a4]76,d15
.L751:
	jne	d4,#0,.L70
.L752:
	j	.L71
.L72:
.L71:
	ld.bu	d15,[a4]79
	extr.u	d15,d15,#7,#1
.L753:
	j	.L73
.L73:
	jne	d15,#0,.L72
.L754:
	j	.L74
.L70:
	j	.L75
.L76:
.L75:
	ld.bu	d15,[a4]79
	extr.u	d15,d15,#7,#1
.L755:
	j	.L77
.L77:
	jne	d15,#1,.L76
.L74:
	ret
.L404:
	
__IfxAsclin_setClockSource_function_end:
	.size	IfxAsclin_setClockSource,__IfxAsclin_setClockSource_function_end-IfxAsclin_setClockSource
.L229:
	; End of function
	
	.sdecl	'.text.IfxAsclin.IfxAsclin_write16',code,cluster('IfxAsclin_write16')
	.sect	'.text.IfxAsclin.IfxAsclin_write16'
	.align	2
	
	.global	IfxAsclin_write16
; Function IfxAsclin_write16
.L126:
IfxAsclin_write16:	.type	func
	lea	a15,[a4]68
.L557:
	j	.L78
.L79:
	ld.hu	d15,[a5]0
.L760:
	st.w	[a15],d15
.L761:
	add.a	a5,#2
.L762:
	add	d4,#-1
.L78:
	jge.u	d4,#1,.L79
.L763:
	mov	d2,d4
.L558:
	j	.L80
.L80:
	ret
.L411:
	
__IfxAsclin_write16_function_end:
	.size	IfxAsclin_write16,__IfxAsclin_write16_function_end-IfxAsclin_write16
.L234:
	; End of function
	
	.sdecl	'.text.IfxAsclin.IfxAsclin_write32',code,cluster('IfxAsclin_write32')
	.sect	'.text.IfxAsclin.IfxAsclin_write32'
	.align	2
	
	.global	IfxAsclin_write32
; Function IfxAsclin_write32
.L128:
IfxAsclin_write32:	.type	func
	lea	a15,[a4]68
.L559:
	j	.L81
.L82:
	ld.w	d15,[a5]
.L768:
	st.w	[a15],d15
.L769:
	add.a	a5,#4
.L770:
	add	d4,#-1
.L81:
	jge.u	d4,#1,.L82
.L771:
	mov	d2,d4
.L560:
	j	.L83
.L83:
	ret
.L417:
	
__IfxAsclin_write32_function_end:
	.size	IfxAsclin_write32,__IfxAsclin_write32_function_end-IfxAsclin_write32
.L239:
	; End of function
	
	.sdecl	'.text.IfxAsclin.IfxAsclin_write8',code,cluster('IfxAsclin_write8')
	.sect	'.text.IfxAsclin.IfxAsclin_write8'
	.align	2
	
	.global	IfxAsclin_write8
; Function IfxAsclin_write8
.L130:
IfxAsclin_write8:	.type	func
	lea	a15,[a4]68
.L561:
	j	.L84
.L85:
	ld.bu	d15,[a5]
.L776:
	st.w	[a15],d15
.L777:
	add.a	a5,#1
.L778:
	add	d4,#-1
.L84:
	jge.u	d4,#1,.L85
.L779:
	mov	d2,d4
.L562:
	j	.L86
.L86:
	ret
.L422:
	
__IfxAsclin_write8_function_end:
	.size	IfxAsclin_write8,__IfxAsclin_write8_function_end-IfxAsclin_write8
.L244:
	; End of function
	
	.calls	'IfxAsclin_setBitTiming','__f_ftod'
	.calls	'IfxAsclin_setBitTiming','__d_mul'
	.calls	'IfxAsclin_setBitTiming','__d_dtof'
	.calls	'IfxAsclin_setBitTiming','__d_fgt'
	.calls	'IfxAsclin_disableModule','IfxScuWdt_getCpuWatchdogPassword'
	.calls	'IfxAsclin_disableModule','IfxScuWdt_clearCpuEndinit'
	.calls	'IfxAsclin_disableModule','IfxScuWdt_setCpuEndinit'
	.calls	'IfxAsclin_enableModule','IfxScuWdt_getCpuWatchdogPassword'
	.calls	'IfxAsclin_enableModule','IfxScuWdt_clearCpuEndinit'
	.calls	'IfxAsclin_enableModule','IfxScuWdt_setCpuEndinit'
	.calls	'IfxAsclin_getFaFrequency','IfxScuCcu_getSpbFrequency'
	.calls	'IfxAsclin_getFaFrequency','IfxScuCcu_getOsc0Frequency'
	.calls	'IfxAsclin_getFaFrequency','IfxScuCcu_getPllErayFrequency'
	.calls	'IfxAsclin_getFaFrequency','IfxScuCcu_getBaud2Frequency'
	.calls	'IfxAsclin_getFaFrequency','IfxScuCcu_getBaud1Frequency'
	.calls	'IfxAsclin_getOvsFrequency','IfxAsclin_getPdFrequency'
	.calls	'IfxAsclin_getPdFrequency','IfxAsclin_getFaFrequency'
	.calls	'IfxAsclin_getShiftFrequency','IfxAsclin_getOvsFrequency'
	.calls	'IfxAsclin_getSrcPointerEr','IfxAsclin_getIndex'
	.calls	'IfxAsclin_getSrcPointerRx','IfxAsclin_getIndex'
	.calls	'IfxAsclin_getSrcPointerTx','IfxAsclin_getIndex'
	.calls	'IfxAsclin_resetModule','IfxScuWdt_getCpuWatchdogPassword'
	.calls	'IfxAsclin_resetModule','IfxScuWdt_clearCpuEndinit'
	.calls	'IfxAsclin_resetModule','IfxScuWdt_setCpuEndinit'
	.calls	'IfxAsclin_setBaudrateBitFields','IfxAsclin_setClockSource'
	.calls	'IfxAsclin_setBitTiming','IfxAsclin_getPdFrequency'
	.calls	'IfxAsclin_setBitTiming','IfxAsclin_setClockSource'
	.calls	'IfxAsclin_disableModule','',0
	.calls	'IfxAsclin_enableAscErrorFlags','',0
	.calls	'IfxAsclin_enableModule','',0
	.calls	'IfxAsclin_getAddress','',0
	.calls	'IfxAsclin_getFaFrequency','',0
	.calls	'IfxAsclin_getIndex','',0
	.calls	'IfxAsclin_getOvsFrequency','',0
	.calls	'IfxAsclin_getPdFrequency','',0
	.calls	'IfxAsclin_getShiftFrequency','',0
	.calls	'IfxAsclin_getSrcPointerEr','',0
	.calls	'IfxAsclin_getSrcPointerRx','',0
	.calls	'IfxAsclin_getSrcPointerTx','',0
	.calls	'IfxAsclin_read16','',0
	.calls	'IfxAsclin_read32','',0
	.calls	'IfxAsclin_read8','',0
	.calls	'IfxAsclin_resetModule','',0
	.calls	'IfxAsclin_setBaudrateBitFields','',0
	.calls	'IfxAsclin_setBitTiming','',48
	.calls	'IfxAsclin_setClockSource','',0
	.calls	'IfxAsclin_write16','',0
	.calls	'IfxAsclin_write32','',0
	.extern	IfxAsclin_cfg_indexMap
	.extern	IfxScuWdt_clearCpuEndinit
	.extern	IfxScuWdt_setCpuEndinit
	.extern	IfxScuWdt_getCpuWatchdogPassword
	.extern	IfxScuCcu_getBaud1Frequency
	.extern	IfxScuCcu_getBaud2Frequency
	.extern	IfxScuCcu_getOsc0Frequency
	.extern	IfxScuCcu_getPllErayFrequency
	.extern	IfxScuCcu_getSpbFrequency
	.extern	__f_ftod
	.extern	__d_mul
	.extern	__d_dtof
	.extern	__d_fgt
	.calls	'IfxAsclin_write8','',0
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L132:
	.word	94266
	.half	3
	.word	.L133
	.byte	4
.L131:
	.byte	1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Asclin/Std/IfxAsclin.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L134
	.byte	2,1,1,3
	.word	236
	.byte	4
	.byte	'Ifx__jump_and_link',0,3,1,61,17,1,1,5
	.byte	'fun',0,1,61,43
	.word	239
	.byte	6,0,7
	.byte	'__fract',0,4,128,1
.L250:
	.byte	7
	.byte	'float',0,4,4,8
	.byte	'Ifx__float_to_fract',0,3,1,152,2,18
	.word	284
	.byte	1,1,5
	.byte	'a',0,1,152,2,44
	.word	296
	.byte	6,0,4
	.byte	'Ifx__stopPerfCounters',0,3,1,172,2,17,1,1,6,0,7
	.byte	'unsigned long long int',0,8,7,9
	.byte	'void',0,3
	.word	408
	.byte	8
	.byte	'__ld64',0,3,2,135,1,19
	.word	382
	.byte	1,1,5
	.byte	'addr',0,2,135,1,32
	.word	414
	.byte	6,0,4
	.byte	'__st64',0,3,2,143,1,17,1,1,5
	.byte	'addr',0,2,143,1,30
	.word	414
	.byte	5
	.byte	'value',0,2,143,1,43
	.word	382
	.byte	6,0,7
	.byte	'unsigned int',0,4,7,7
	.byte	'int',0,4,5,7
	.byte	'unsigned int',0,4,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON0_Bits',0,4,241,8,16,4,11
	.byte	'ENDINIT',0,4
	.word	523
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	523
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	523
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	523
	.byte	16,0,2,35,0,0,12,4,247,14,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	539
	.byte	4,2,35,0,0
.L275:
	.byte	7
	.byte	'unsigned char',0,1,8
.L273:
	.byte	7
	.byte	'unsigned short int',0,2,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON1_Bits',0,4,250,8,16,4,11
	.byte	'reserved_0',0,1
	.word	675
	.byte	2,6,2,35,0,11
	.byte	'IR0',0,1
	.word	675
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	675
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	675
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	675
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	675
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	675
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	675
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	675
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	692
	.byte	16,0,2,35,2,0,12,4,255,14,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	714
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_WDTCPU_SR_Bits',0,4,137,9,16,4,11
	.byte	'AE',0,1
	.word	675
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	675
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	675
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	675
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	675
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	675
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	675
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	675
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	675
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	675
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	692
	.byte	16,0,2,35,2,0,12,4,135,15,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	958
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_WDTCPU',0,4,175,15,25,12,13
	.byte	'CON0',0
	.word	635
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	918
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	1149
	.byte	4,2,35,8,0,14
	.word	1189
	.byte	3
	.word	1252
	.byte	4
	.byte	'IfxScuWdt_clearCpuEndinitInline',0,3,3,181,3,17,1,1,5
	.byte	'watchdog',0,3,181,3,65
	.word	1257
	.byte	5
	.byte	'password',0,3,181,3,82
	.word	692
	.byte	6,0,4
	.byte	'IfxScuWdt_setCpuEndinitInline',0,3,3,140,4,17,1,1,5
	.byte	'watchdog',0,3,140,4,63
	.word	1257
	.byte	5
	.byte	'password',0,3,140,4,80
	.word	692
	.byte	6,0,8
	.byte	'IfxScuWdt_getCpuWatchdogPasswordInline',0,3,3,227,3,19
	.word	692
	.byte	1,1,5
	.byte	'watchdog',0,3,227,3,74
	.word	1257
	.byte	6,0,15,6,156,1,9,1,16
	.byte	'IfxCpu_ResourceCpu_0',0,0,16
	.byte	'IfxCpu_ResourceCpu_1',0,1,16
	.byte	'IfxCpu_ResourceCpu_none',0,2,0,8
	.byte	'IfxCpu_getCoreIndex',0,3,5,141,6,31
	.word	1487
	.byte	1,1,6,0,8
	.byte	'IfxCpu_areInterruptsEnabled',0,3,5,139,5,20
	.word	675
	.byte	1,1,6,0
.L262:
	.byte	7
	.byte	'unsigned long int',0,4,7,8
	.byte	'IfxCpu_getPerformanceCounter',0,3,5,161,6,19
	.word	1642
	.byte	1,1,5
	.byte	'address',0,5,161,6,55
	.word	692
	.byte	6,0,8
	.byte	'IfxCpu_getPerformanceCounterStickyOverflow',0,3,5,190,6,20
	.word	675
	.byte	1,1,5
	.byte	'address',0,5,190,6,70
	.word	692
	.byte	6,0,4
	.byte	'IfxCpu_updatePerformanceCounter',0,3,5,172,8,17,1,1,5
	.byte	'address',0,5,172,8,56
	.word	1642
	.byte	5
	.byte	'count',0,5,172,8,72
	.word	1642
	.byte	17,6,0,0,10
	.byte	'_Ifx_P_OUT_Bits',0,8,143,3,16,4,11
	.byte	'P0',0,1
	.word	675
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	675
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	675
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	675
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	675
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	675
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	675
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	675
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	675
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	675
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	675
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	675
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	675
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	675
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	675
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	675
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	692
	.byte	16,0,2,35,2,0,12,8,181,5,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1873
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMR_Bits',0,8,169,2,16,4,11
	.byte	'PS0',0,1
	.word	675
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	675
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	675
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	675
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	675
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	675
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	675
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	675
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	675
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	675
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	675
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	675
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	675
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	675
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	675
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	675
	.byte	1,0,2,35,1,11
	.byte	'PCL0',0,1
	.word	675
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	675
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	675
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	675
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	675
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	675
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	675
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	675
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	675
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	675
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	675
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	675
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	675
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	675
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	675
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	675
	.byte	1,0,2,35,3,0,12,8,133,5,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2189
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ID_Bits',0,8,110,16,4,11
	.byte	'MODREV',0,1
	.word	675
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	675
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	692
	.byte	16,0,2,35,2,0,12,8,148,4,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2760
	.byte	4,2,35,0,0,18,4
	.word	675
	.byte	19,3,0,10
	.byte	'_Ifx_P_IOCR0_Bits',0,8,140,1,16,4,11
	.byte	'reserved_0',0,1
	.word	675
	.byte	3,5,2,35,0,11
	.byte	'PC0',0,1
	.word	675
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	675
	.byte	3,5,2,35,1,11
	.byte	'PC1',0,1
	.word	675
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	675
	.byte	3,5,2,35,2,11
	.byte	'PC2',0,1
	.word	675
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	675
	.byte	3,5,2,35,3,11
	.byte	'PC3',0,1
	.word	675
	.byte	5,0,2,35,3,0,12,8,164,4,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2888
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR4_Bits',0,8,166,1,16,4,11
	.byte	'reserved_0',0,1
	.word	675
	.byte	3,5,2,35,0,11
	.byte	'PC4',0,1
	.word	675
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	675
	.byte	3,5,2,35,1,11
	.byte	'PC5',0,1
	.word	675
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	675
	.byte	3,5,2,35,2,11
	.byte	'PC6',0,1
	.word	675
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	675
	.byte	3,5,2,35,3,11
	.byte	'PC7',0,1
	.word	675
	.byte	5,0,2,35,3,0,12,8,180,4,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3103
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR8_Bits',0,8,179,1,16,4,11
	.byte	'reserved_0',0,1
	.word	675
	.byte	3,5,2,35,0,11
	.byte	'PC8',0,1
	.word	675
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	675
	.byte	3,5,2,35,1,11
	.byte	'PC9',0,1
	.word	675
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	675
	.byte	3,5,2,35,2,11
	.byte	'PC10',0,1
	.word	675
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	675
	.byte	3,5,2,35,3,11
	.byte	'PC11',0,1
	.word	675
	.byte	5,0,2,35,3,0,12,8,188,4,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3318
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR12_Bits',0,8,153,1,16,4,11
	.byte	'reserved_0',0,1
	.word	675
	.byte	3,5,2,35,0,11
	.byte	'PC12',0,1
	.word	675
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	675
	.byte	3,5,2,35,1,11
	.byte	'PC13',0,1
	.word	675
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	675
	.byte	3,5,2,35,2,11
	.byte	'PC14',0,1
	.word	675
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	675
	.byte	3,5,2,35,3,11
	.byte	'PC15',0,1
	.word	675
	.byte	5,0,2,35,3,0,12,8,172,4,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3535
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IN_Bits',0,8,118,16,4,11
	.byte	'P0',0,1
	.word	675
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	675
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	675
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	675
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	675
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	675
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	675
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	675
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	675
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	675
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	675
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	675
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	675
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	675
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	675
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	675
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	692
	.byte	16,0,2,35,2,0,12,8,156,4,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3755
	.byte	4,2,35,0,0,18,24
	.word	675
	.byte	19,23,0,10
	.byte	'_Ifx_P_PDR0_Bits',0,8,205,3,16,4,11
	.byte	'PD0',0,1
	.word	675
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	675
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	675
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	675
	.byte	1,0,2,35,0,11
	.byte	'PD2',0,1
	.word	675
	.byte	3,5,2,35,1,11
	.byte	'PL2',0,1
	.word	675
	.byte	1,4,2,35,1,11
	.byte	'PD3',0,1
	.word	675
	.byte	3,1,2,35,1,11
	.byte	'PL3',0,1
	.word	675
	.byte	1,0,2,35,1,11
	.byte	'PD4',0,1
	.word	675
	.byte	3,5,2,35,2,11
	.byte	'PL4',0,1
	.word	675
	.byte	1,4,2,35,2,11
	.byte	'PD5',0,1
	.word	675
	.byte	3,1,2,35,2,11
	.byte	'PL5',0,1
	.word	675
	.byte	1,0,2,35,2,11
	.byte	'PD6',0,1
	.word	675
	.byte	3,5,2,35,3,11
	.byte	'PL6',0,1
	.word	675
	.byte	1,4,2,35,3,11
	.byte	'PD7',0,1
	.word	675
	.byte	3,1,2,35,3,11
	.byte	'PL7',0,1
	.word	675
	.byte	1,0,2,35,3,0,12,8,205,5,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4078
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PDR1_Bits',0,8,226,3,16,4,11
	.byte	'PD8',0,1
	.word	675
	.byte	3,5,2,35,0,11
	.byte	'PL8',0,1
	.word	675
	.byte	1,4,2,35,0,11
	.byte	'PD9',0,1
	.word	675
	.byte	3,1,2,35,0,11
	.byte	'PL9',0,1
	.word	675
	.byte	1,0,2,35,0,11
	.byte	'PD10',0,1
	.word	675
	.byte	3,5,2,35,1,11
	.byte	'PL10',0,1
	.word	675
	.byte	1,4,2,35,1,11
	.byte	'PD11',0,1
	.word	675
	.byte	3,1,2,35,1,11
	.byte	'PL11',0,1
	.word	675
	.byte	1,0,2,35,1,11
	.byte	'PD12',0,1
	.word	675
	.byte	3,5,2,35,2,11
	.byte	'PL12',0,1
	.word	675
	.byte	1,4,2,35,2,11
	.byte	'PD13',0,1
	.word	675
	.byte	3,1,2,35,2,11
	.byte	'PL13',0,1
	.word	675
	.byte	1,0,2,35,2,11
	.byte	'PD14',0,1
	.word	675
	.byte	3,5,2,35,3,11
	.byte	'PL14',0,1
	.word	675
	.byte	1,4,2,35,3,11
	.byte	'PD15',0,1
	.word	675
	.byte	3,1,2,35,3,11
	.byte	'PL15',0,1
	.word	675
	.byte	1,0,2,35,3,0,12,8,213,5,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4382
	.byte	4,2,35,0,0,18,8
	.word	675
	.byte	19,7,0,10
	.byte	'_Ifx_P_ESR_Bits',0,8,88,16,4,11
	.byte	'EN0',0,1
	.word	675
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	675
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	675
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	675
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	675
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	675
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	675
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	675
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	675
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	675
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	675
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	675
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	675
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	675
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	675
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	675
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	692
	.byte	16,0,2,35,2,0,12,8,140,4,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4707
	.byte	4,2,35,0,0,18,12
	.word	675
	.byte	19,11,0,10
	.byte	'_Ifx_P_PDISC_Bits',0,8,183,3,16,4,11
	.byte	'PDIS0',0,1
	.word	675
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	675
	.byte	1,6,2,35,0,11
	.byte	'PDIS2',0,1
	.word	675
	.byte	1,5,2,35,0,11
	.byte	'PDIS3',0,1
	.word	675
	.byte	1,4,2,35,0,11
	.byte	'PDIS4',0,1
	.word	675
	.byte	1,3,2,35,0,11
	.byte	'PDIS5',0,1
	.word	675
	.byte	1,2,2,35,0,11
	.byte	'PDIS6',0,1
	.word	675
	.byte	1,1,2,35,0,11
	.byte	'PDIS7',0,1
	.word	675
	.byte	1,0,2,35,0,11
	.byte	'PDIS8',0,1
	.word	675
	.byte	1,7,2,35,1,11
	.byte	'PDIS9',0,1
	.word	675
	.byte	1,6,2,35,1,11
	.byte	'PDIS10',0,1
	.word	675
	.byte	1,5,2,35,1,11
	.byte	'PDIS11',0,1
	.word	675
	.byte	1,4,2,35,1,11
	.byte	'PDIS12',0,1
	.word	675
	.byte	1,3,2,35,1,11
	.byte	'PDIS13',0,1
	.word	675
	.byte	1,2,2,35,1,11
	.byte	'PDIS14',0,1
	.word	675
	.byte	1,1,2,35,1,11
	.byte	'PDIS15',0,1
	.word	675
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	692
	.byte	16,0,2,35,2,0,12,8,197,5,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5047
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PCSR_Bits',0,8,165,3,16,4,11
	.byte	'SEL0',0,1
	.word	675
	.byte	1,7,2,35,0,11
	.byte	'SEL1',0,1
	.word	675
	.byte	1,6,2,35,0,11
	.byte	'SEL2',0,1
	.word	675
	.byte	1,5,2,35,0,11
	.byte	'SEL3',0,1
	.word	675
	.byte	1,4,2,35,0,11
	.byte	'SEL4',0,1
	.word	675
	.byte	1,3,2,35,0,11
	.byte	'SEL5',0,1
	.word	675
	.byte	1,2,2,35,0,11
	.byte	'SEL6',0,1
	.word	675
	.byte	1,1,2,35,0,11
	.byte	'SEL7',0,1
	.word	675
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	675
	.byte	2,6,2,35,1,11
	.byte	'SEL10',0,1
	.word	675
	.byte	1,5,2,35,1,11
	.byte	'SEL11',0,1
	.word	675
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	500
	.byte	19,1,2,35,0,11
	.byte	'LCK',0,1
	.word	675
	.byte	1,0,2,35,3,0,12,8,189,5,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5413
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR0_Bits',0,8,206,2,16,4,11
	.byte	'PS0',0,1
	.word	675
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	675
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	675
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	675
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	500
	.byte	28,0,2,35,0,0,12,8,149,5,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5699
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR4_Bits',0,8,227,2,16,4,11
	.byte	'reserved_0',0,1
	.word	675
	.byte	4,4,2,35,0,11
	.byte	'PS4',0,1
	.word	675
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	675
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	675
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	675
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	500
	.byte	24,0,2,35,0,0,12,8,165,5,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5846
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR8_Bits',0,8,238,2,16,4,11
	.byte	'reserved_0',0,1
	.word	675
	.byte	8,0,2,35,0,11
	.byte	'PS8',0,1
	.word	675
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	675
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	675
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	675
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	500
	.byte	20,0,2,35,0,0,12,8,173,5,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6015
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR12_Bits',0,8,216,2,16,4,11
	.byte	'reserved_0',0,2
	.word	692
	.byte	12,4,2,35,0,11
	.byte	'PS12',0,1
	.word	675
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	675
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	675
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	675
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	692
	.byte	16,0,2,35,2,0,12,8,157,5,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6187
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR0_Bits',0,8,232,1,16,4,11
	.byte	'reserved_0',0,2
	.word	692
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	675
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	675
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	675
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	675
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	692
	.byte	12,0,2,35,2,0,12,8,229,4,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6362
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR4_Bits',0,8,253,1,16,4,11
	.byte	'reserved_0',0,4
	.word	500
	.byte	20,12,2,35,0,11
	.byte	'PCL4',0,1
	.word	675
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	675
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	675
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	675
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	675
	.byte	8,0,2,35,3,0,12,8,245,4,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6536
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR8_Bits',0,8,136,2,16,4,11
	.byte	'reserved_0',0,4
	.word	500
	.byte	24,8,2,35,0,11
	.byte	'PCL8',0,1
	.word	675
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	675
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	675
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	675
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	675
	.byte	4,0,2,35,3,0,12,8,253,4,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6710
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR12_Bits',0,8,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	500
	.byte	28,4,2,35,0,11
	.byte	'PCL12',0,1
	.word	675
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	675
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	675
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	675
	.byte	1,0,2,35,3,0,12,8,237,4,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6886
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR_Bits',0,8,249,2,16,4,11
	.byte	'PS0',0,1
	.word	675
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	675
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	675
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	675
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	675
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	675
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	675
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	675
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	675
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	675
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	675
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	675
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	675
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	675
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	675
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	675
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	692
	.byte	16,0,2,35,2,0,12,8,141,5,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7042
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR_Bits',0,8,147,2,16,4,11
	.byte	'reserved_0',0,2
	.word	692
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	675
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	675
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	675
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	675
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	675
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	675
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	675
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	675
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	675
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	675
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	675
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	675
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	675
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	675
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	675
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	675
	.byte	1,0,2,35,3,0,12,8,221,4,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7375
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR0_Bits',0,8,192,1,16,4,11
	.byte	'reserved_0',0,1
	.word	675
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	675
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	500
	.byte	30,0,2,35,0,0,12,8,196,4,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7723
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_Bits',0,8,200,1,16,4,11
	.byte	'reserved_0',0,1
	.word	675
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	675
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	500
	.byte	30,0,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_P21_Bits',0,8,208,1,16,4,11
	.byte	'RDIS_CTRL',0,1
	.word	675
	.byte	1,7,2,35,0,11
	.byte	'RX_DIS',0,1
	.word	675
	.byte	1,6,2,35,0,11
	.byte	'TERM',0,1
	.word	675
	.byte	1,5,2,35,0,11
	.byte	'LRXTERM',0,1
	.word	675
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	500
	.byte	24,0,2,35,0,0,12,8,204,4,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7847
	.byte	4,2,35,0,13
	.byte	'B_P21',0
	.word	7931
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR2_Bits',0,8,218,1,16,4,11
	.byte	'reserved_0',0,1
	.word	675
	.byte	8,0,2,35,0,11
	.byte	'LVDSR',0,1
	.word	675
	.byte	1,7,2,35,1,11
	.byte	'LVDSRL',0,1
	.word	675
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	675
	.byte	2,4,2,35,1,11
	.byte	'TDIS_CTRL',0,1
	.word	675
	.byte	1,3,2,35,1,11
	.byte	'TX_DIS',0,1
	.word	675
	.byte	1,2,2,35,1,11
	.byte	'TX_PD',0,1
	.word	675
	.byte	1,1,2,35,1,11
	.byte	'TX_PWDPD',0,1
	.word	675
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	692
	.byte	16,0,2,35,2,0,12,8,213,4,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8111
	.byte	4,2,35,0,0,18,76
	.word	675
	.byte	19,75,0,10
	.byte	'_Ifx_P_ACCEN1_Bits',0,8,82,16,4,11
	.byte	'reserved_0',0,4
	.word	500
	.byte	32,0,2,35,0,0,12,8,132,4,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8364
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ACCEN0_Bits',0,8,45,16,4,11
	.byte	'EN0',0,1
	.word	675
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	675
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	675
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	675
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	675
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	675
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	675
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	675
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	675
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	675
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	675
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	675
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	675
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	675
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	675
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	675
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	675
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	675
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	675
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	675
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	675
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	675
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	675
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	675
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	675
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	675
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	675
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	675
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	675
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	675
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	675
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	675
	.byte	1,0,2,35,3,0,12,8,252,3,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8451
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P',0,8,229,5,25,128,2,13
	.byte	'OUT',0
	.word	2149
	.byte	4,2,35,0,13
	.byte	'OMR',0
	.word	2720
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	2839
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	2879
	.byte	4,2,35,12,13
	.byte	'IOCR0',0
	.word	3063
	.byte	4,2,35,16,13
	.byte	'IOCR4',0
	.word	3278
	.byte	4,2,35,20,13
	.byte	'IOCR8',0
	.word	3495
	.byte	4,2,35,24,13
	.byte	'IOCR12',0
	.word	3715
	.byte	4,2,35,28,13
	.byte	'reserved_20',0
	.word	2879
	.byte	4,2,35,32,13
	.byte	'IN',0
	.word	4029
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	4069
	.byte	24,2,35,40,13
	.byte	'PDR0',0
	.word	4342
	.byte	4,2,35,64,13
	.byte	'PDR1',0
	.word	4658
	.byte	4,2,35,68,13
	.byte	'reserved_48',0
	.word	4698
	.byte	8,2,35,72,13
	.byte	'ESR',0
	.word	4998
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	5038
	.byte	12,2,35,84,13
	.byte	'PDISC',0
	.word	5373
	.byte	4,2,35,96,13
	.byte	'PCSR',0
	.word	5659
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	4698
	.byte	8,2,35,104,13
	.byte	'OMSR0',0
	.word	5806
	.byte	4,2,35,112,13
	.byte	'OMSR4',0
	.word	5975
	.byte	4,2,35,116,13
	.byte	'OMSR8',0
	.word	6147
	.byte	4,2,35,120,13
	.byte	'OMSR12',0
	.word	6322
	.byte	4,2,35,124,13
	.byte	'OMCR0',0
	.word	6496
	.byte	4,3,35,128,1,13
	.byte	'OMCR4',0
	.word	6670
	.byte	4,3,35,132,1,13
	.byte	'OMCR8',0
	.word	6846
	.byte	4,3,35,136,1,13
	.byte	'OMCR12',0
	.word	7002
	.byte	4,3,35,140,1,13
	.byte	'OMSR',0
	.word	7335
	.byte	4,3,35,144,1,13
	.byte	'OMCR',0
	.word	7683
	.byte	4,3,35,148,1,13
	.byte	'reserved_98',0
	.word	4698
	.byte	8,3,35,152,1,13
	.byte	'LPCR0',0
	.word	7807
	.byte	4,3,35,160,1,13
	.byte	'LPCR1',0
	.word	8056
	.byte	4,3,35,164,1,13
	.byte	'LPCR2',0
	.word	8315
	.byte	4,3,35,168,1,13
	.byte	'reserved_A4',0
	.word	8355
	.byte	76,3,35,172,1,13
	.byte	'ACCEN1',0
	.word	8411
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	8978
	.byte	4,3,35,252,1,0,14
	.word	9018
	.byte	3
	.word	9621
	.byte	15,7,83,9,1,16
	.byte	'IfxPort_InputMode_undefined',0,127,16
	.byte	'IfxPort_InputMode_noPullDevice',0,0,16
	.byte	'IfxPort_InputMode_pullDown',0,8,16
	.byte	'IfxPort_InputMode_pullUp',0,16,0,4
	.byte	'IfxPort_setPinModeInput',0,3,7,196,4,17,1,1,5
	.byte	'port',0,7,196,4,48
	.word	9626
	.byte	5
	.byte	'pinIndex',0,7,196,4,60
	.word	675
	.byte	5
	.byte	'mode',0,7,196,4,88
	.word	9631
	.byte	6,0,15,7,134,1,9,1,16
	.byte	'IfxPort_OutputMode_pushPull',0,128,1,16
	.byte	'IfxPort_OutputMode_openDrain',0,192,1,0,15,7,120,9,1,16
	.byte	'IfxPort_OutputIdx_general',0,128,1,16
	.byte	'IfxPort_OutputIdx_alt1',0,136,1,16
	.byte	'IfxPort_OutputIdx_alt2',0,144,1,16
	.byte	'IfxPort_OutputIdx_alt3',0,152,1,16
	.byte	'IfxPort_OutputIdx_alt4',0,160,1,16
	.byte	'IfxPort_OutputIdx_alt5',0,168,1,16
	.byte	'IfxPort_OutputIdx_alt6',0,176,1,16
	.byte	'IfxPort_OutputIdx_alt7',0,184,1,0,4
	.byte	'IfxPort_setPinModeOutput',0,3,7,202,4,17,1,1,5
	.byte	'port',0,7,202,4,49
	.word	9626
	.byte	5
	.byte	'pinIndex',0,7,202,4,61
	.word	675
	.byte	5
	.byte	'mode',0,7,202,4,90
	.word	9836
	.byte	5
	.byte	'index',0,7,202,4,114
	.word	9906
	.byte	6,0,15,7,172,1,9,4,16
	.byte	'IfxPort_State_notChanged',0,0,16
	.byte	'IfxPort_State_high',0,1,16
	.byte	'IfxPort_State_low',0,128,128,4,16
	.byte	'IfxPort_State_toggled',0,129,128,4,0,4
	.byte	'IfxPort_setPinState',0,3,7,208,4,17,1,1,5
	.byte	'port',0,7,208,4,44
	.word	9626
	.byte	5
	.byte	'pinIndex',0,7,208,4,56
	.word	675
	.byte	5
	.byte	'action',0,7,208,4,80
	.word	10219
	.byte	6,0,10
	.byte	'_Ifx_SRC_SRCR_Bits',0,10,45,16,4,11
	.byte	'SRPN',0,1
	.word	675
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	675
	.byte	2,6,2,35,1,11
	.byte	'SRE',0,1
	.word	675
	.byte	1,5,2,35,1,11
	.byte	'TOS',0,1
	.word	675
	.byte	2,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	675
	.byte	3,0,2,35,1,11
	.byte	'ECC',0,1
	.word	675
	.byte	6,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	675
	.byte	2,0,2,35,2,11
	.byte	'SRR',0,1
	.word	675
	.byte	1,7,2,35,3,11
	.byte	'CLRR',0,1
	.word	675
	.byte	1,6,2,35,3,11
	.byte	'SETR',0,1
	.word	675
	.byte	1,5,2,35,3,11
	.byte	'IOV',0,1
	.word	675
	.byte	1,4,2,35,3,11
	.byte	'IOVCLR',0,1
	.word	675
	.byte	1,3,2,35,3,11
	.byte	'SWS',0,1
	.word	675
	.byte	1,2,2,35,3,11
	.byte	'SWSCLR',0,1
	.word	675
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	675
	.byte	1,0,2,35,3,0,12,10,70,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10400
	.byte	4,2,35,0,0,14
	.word	10690
.L340:
	.byte	3
	.word	10729
	.byte	4
	.byte	'IfxSrc_clearRequest',0,3,9,250,1,17,1,1,5
	.byte	'src',0,9,250,1,60
	.word	10734
	.byte	6,0,10
	.byte	'_Ifx_ASCLIN_CLC_Bits',0,12,118,16,4,11
	.byte	'DISR',0,1
	.word	675
	.byte	1,7,2,35,0,11
	.byte	'DISS',0,1
	.word	675
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	675
	.byte	1,5,2,35,0,11
	.byte	'EDIS',0,1
	.word	675
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	500
	.byte	28,0,2,35,0,0,12,12,207,3,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10782
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_IOCR_Bits',0,12,169,2,16,4,11
	.byte	'ALTI',0,1
	.word	675
	.byte	3,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	675
	.byte	1,4,2,35,0,11
	.byte	'DEPTH',0,2
	.word	692
	.byte	6,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	675
	.byte	6,0,2,35,1,11
	.byte	'CTS',0,1
	.word	675
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	692
	.byte	7,7,2,35,2,11
	.byte	'RCPOL',0,1
	.word	675
	.byte	1,6,2,35,3,11
	.byte	'CPOL',0,1
	.word	675
	.byte	1,5,2,35,3,11
	.byte	'SPOL',0,1
	.word	675
	.byte	1,4,2,35,3,11
	.byte	'LB',0,1
	.word	675
	.byte	1,3,2,35,3,11
	.byte	'CTSEN',0,1
	.word	675
	.byte	1,2,2,35,3,11
	.byte	'RXM',0,1
	.word	675
	.byte	1,1,2,35,3,11
	.byte	'TXM',0,1
	.word	675
	.byte	1,0,2,35,3,0,12,12,151,4,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10941
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_ID_Bits',0,12,161,2,16,4,11
	.byte	'MODREV',0,1
	.word	675
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	675
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	692
	.byte	16,0,2,35,2,0,12,12,143,4,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11236
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_TXFIFOCON_Bits',0,12,149,3,16,4,11
	.byte	'FLUSH',0,1
	.word	675
	.byte	1,7,2,35,0,11
	.byte	'ENO',0,1
	.word	675
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	675
	.byte	4,2,2,35,0,11
	.byte	'INW',0,1
	.word	675
	.byte	2,0,2,35,0,11
	.byte	'INTLEVEL',0,1
	.word	675
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	675
	.byte	4,0,2,35,1,11
	.byte	'FILL',0,1
	.word	675
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,2
	.word	692
	.byte	11,0,2,35,2,0,12,12,247,4,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11361
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_RXFIFOCON_Bits',0,12,129,3,16,4,11
	.byte	'FLUSH',0,1
	.word	675
	.byte	1,7,2,35,0,11
	.byte	'ENI',0,1
	.word	675
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	675
	.byte	4,2,2,35,0,11
	.byte	'OUTW',0,1
	.word	675
	.byte	2,0,2,35,0,11
	.byte	'INTLEVEL',0,1
	.word	675
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	675
	.byte	4,0,2,35,1,11
	.byte	'FILL',0,1
	.word	675
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,2
	.word	692
	.byte	10,1,2,35,2,11
	.byte	'BUF',0,1
	.word	675
	.byte	1,0,2,35,3,0,12,12,231,4,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11586
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_BITCON_Bits',0,12,88,16,4,11
	.byte	'PRESCALER',0,2
	.word	692
	.byte	12,4,2,35,0,11
	.byte	'reserved_12',0,1
	.word	675
	.byte	4,0,2,35,1,11
	.byte	'OVERSAMPLING',0,1
	.word	675
	.byte	4,4,2,35,2,11
	.byte	'reserved_20',0,1
	.word	675
	.byte	4,0,2,35,2,11
	.byte	'SAMPLEPOINT',0,1
	.word	675
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	675
	.byte	3,1,2,35,3,11
	.byte	'SM',0,1
	.word	675
	.byte	1,0,2,35,3,0,12,12,183,3,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11827
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_FRAMECON_Bits',0,12,145,2,16,4,11
	.byte	'reserved_0',0,1
	.word	675
	.byte	6,2,2,35,0,11
	.byte	'IDLE',0,2
	.word	692
	.byte	3,7,2,35,0,11
	.byte	'STOP',0,1
	.word	675
	.byte	3,4,2,35,1,11
	.byte	'LEAD',0,1
	.word	675
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	675
	.byte	1,0,2,35,1,11
	.byte	'MODE',0,1
	.word	675
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	692
	.byte	10,4,2,35,2,11
	.byte	'MSB',0,1
	.word	675
	.byte	1,3,2,35,3,11
	.byte	'CEN',0,1
	.word	675
	.byte	1,2,2,35,3,11
	.byte	'PEN',0,1
	.word	675
	.byte	1,1,2,35,3,11
	.byte	'ODD',0,1
	.word	675
	.byte	1,0,2,35,3,0,12,12,135,4,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12048
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_DATCON_Bits',0,12,136,1,16,4,11
	.byte	'DATLEN',0,1
	.word	675
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	692
	.byte	9,3,2,35,0,11
	.byte	'HO',0,1
	.word	675
	.byte	1,2,2,35,1,11
	.byte	'RM',0,1
	.word	675
	.byte	1,1,2,35,1,11
	.byte	'CSM',0,1
	.word	675
	.byte	1,0,2,35,1,11
	.byte	'RESPONSE',0,1
	.word	675
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	675
	.byte	8,0,2,35,3,0,12,12,223,3,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12313
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_BRG_Bits',0,12,109,16,4,11
	.byte	'DENOMINATOR',0,2
	.word	692
	.byte	12,4,2,35,0,11
	.byte	'reserved_12',0,1
	.word	675
	.byte	4,0,2,35,1,11
	.byte	'NUMERATOR',0,2
	.word	692
	.byte	12,4,2,35,2,11
	.byte	'reserved_28',0,1
	.word	675
	.byte	4,0,2,35,3,0,12,12,199,3,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12510
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_BRD_Bits',0,12,100,16,4,11
	.byte	'LOWERLIMIT',0,1
	.word	675
	.byte	8,0,2,35,0,11
	.byte	'UPPERLIMIT',0,1
	.word	675
	.byte	8,0,2,35,1,11
	.byte	'MEASURED',0,2
	.word	692
	.byte	12,4,2,35,2,11
	.byte	'reserved_28',0,1
	.word	675
	.byte	4,0,2,35,3,0,12,12,191,3,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12667
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_LIN_CON_Bits',0,12,216,2,16,4,11
	.byte	'reserved_0',0,4
	.word	500
	.byte	23,9,2,35,0,11
	.byte	'CSI',0,1
	.word	675
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	675
	.byte	1,7,2,35,3,11
	.byte	'CSEN',0,1
	.word	675
	.byte	1,6,2,35,3,11
	.byte	'MS',0,1
	.word	675
	.byte	1,5,2,35,3,11
	.byte	'ABD',0,1
	.word	675
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	675
	.byte	4,0,2,35,3,0,12,12,191,4,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12821
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_LIN_BTIMER_Bits',0,12,209,2,16,4,11
	.byte	'BREAK',0,1
	.word	675
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	500
	.byte	26,0,2,35,0,0,12,12,183,4,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13021
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_LIN_HTIMER_Bits',0,12,228,2,16,4,11
	.byte	'HEADER',0,1
	.word	675
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	500
	.byte	24,0,2,35,0,0,12,12,199,4,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13135
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_LIN',0,12,135,5,25,12,13
	.byte	'CON',0
	.word	12981
	.byte	4,2,35,0,13
	.byte	'BTIMER',0
	.word	13095
	.byte	4,2,35,4,13
	.byte	'HTIMER',0
	.word	13210
	.byte	4,2,35,8,0,14
	.word	13250
	.byte	10
	.byte	'_Ifx_ASCLIN_FLAGS_Bits',0,12,148,1,16,4,11
	.byte	'TH',0,1
	.word	675
	.byte	1,7,2,35,0,11
	.byte	'TR',0,1
	.word	675
	.byte	1,6,2,35,0,11
	.byte	'RH',0,1
	.word	675
	.byte	1,5,2,35,0,11
	.byte	'RR',0,1
	.word	675
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	675
	.byte	1,3,2,35,0,11
	.byte	'FED',0,1
	.word	675
	.byte	1,2,2,35,0,11
	.byte	'RED',0,1
	.word	675
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	692
	.byte	6,3,2,35,0,11
	.byte	'TWRQ',0,1
	.word	675
	.byte	1,2,2,35,1,11
	.byte	'THRQ',0,1
	.word	675
	.byte	1,1,2,35,1,11
	.byte	'TRRQ',0,1
	.word	675
	.byte	1,0,2,35,1,11
	.byte	'PE',0,1
	.word	675
	.byte	1,7,2,35,2,11
	.byte	'TC',0,1
	.word	675
	.byte	1,6,2,35,2,11
	.byte	'FE',0,1
	.word	675
	.byte	1,5,2,35,2,11
	.byte	'HT',0,1
	.word	675
	.byte	1,4,2,35,2,11
	.byte	'RT',0,1
	.word	675
	.byte	1,3,2,35,2,11
	.byte	'BD',0,1
	.word	675
	.byte	1,2,2,35,2,11
	.byte	'LP',0,1
	.word	675
	.byte	1,1,2,35,2,11
	.byte	'LA',0,1
	.word	675
	.byte	1,0,2,35,2,11
	.byte	'LC',0,1
	.word	675
	.byte	1,7,2,35,3,11
	.byte	'CE',0,1
	.word	675
	.byte	1,6,2,35,3,11
	.byte	'RFO',0,1
	.word	675
	.byte	1,5,2,35,3,11
	.byte	'RFU',0,1
	.word	675
	.byte	1,4,2,35,3,11
	.byte	'RFL',0,1
	.word	675
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	675
	.byte	1,2,2,35,3,11
	.byte	'TFO',0,1
	.word	675
	.byte	1,1,2,35,3,11
	.byte	'TFL',0,1
	.word	675
	.byte	1,0,2,35,3,0,12,12,231,3,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13323
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_FLAGSSET_Bits',0,12,241,1,16,4,11
	.byte	'THS',0,1
	.word	675
	.byte	1,7,2,35,0,11
	.byte	'TRS',0,1
	.word	675
	.byte	1,6,2,35,0,11
	.byte	'RHS',0,1
	.word	675
	.byte	1,5,2,35,0,11
	.byte	'RRS',0,1
	.word	675
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	675
	.byte	1,3,2,35,0,11
	.byte	'FEDS',0,1
	.word	675
	.byte	1,2,2,35,0,11
	.byte	'REDS',0,1
	.word	675
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	692
	.byte	6,3,2,35,0,11
	.byte	'TWRQS',0,1
	.word	675
	.byte	1,2,2,35,1,11
	.byte	'THRQS',0,1
	.word	675
	.byte	1,1,2,35,1,11
	.byte	'TRRQS',0,1
	.word	675
	.byte	1,0,2,35,1,11
	.byte	'PES',0,1
	.word	675
	.byte	1,7,2,35,2,11
	.byte	'TCS',0,1
	.word	675
	.byte	1,6,2,35,2,11
	.byte	'FES',0,1
	.word	675
	.byte	1,5,2,35,2,11
	.byte	'HTS',0,1
	.word	675
	.byte	1,4,2,35,2,11
	.byte	'RTS',0,1
	.word	675
	.byte	1,3,2,35,2,11
	.byte	'BDS',0,1
	.word	675
	.byte	1,2,2,35,2,11
	.byte	'LPS',0,1
	.word	675
	.byte	1,1,2,35,2,11
	.byte	'LAS',0,1
	.word	675
	.byte	1,0,2,35,2,11
	.byte	'LCS',0,1
	.word	675
	.byte	1,7,2,35,3,11
	.byte	'CES',0,1
	.word	675
	.byte	1,6,2,35,3,11
	.byte	'RFOS',0,1
	.word	675
	.byte	1,5,2,35,3,11
	.byte	'RFUS',0,1
	.word	675
	.byte	1,4,2,35,3,11
	.byte	'RFLS',0,1
	.word	675
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	675
	.byte	1,2,2,35,3,11
	.byte	'TFOS',0,1
	.word	675
	.byte	1,1,2,35,3,11
	.byte	'TFLS',0,1
	.word	675
	.byte	1,0,2,35,3,0,12,12,255,3,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13809
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_FLAGSCLEAR_Bits',0,12,180,1,16,4,11
	.byte	'THC',0,1
	.word	675
	.byte	1,7,2,35,0,11
	.byte	'TRC',0,1
	.word	675
	.byte	1,6,2,35,0,11
	.byte	'RHC',0,1
	.word	675
	.byte	1,5,2,35,0,11
	.byte	'RRC',0,1
	.word	675
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	675
	.byte	1,3,2,35,0,11
	.byte	'FEDC',0,1
	.word	675
	.byte	1,2,2,35,0,11
	.byte	'REDC',0,1
	.word	675
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	692
	.byte	6,3,2,35,0,11
	.byte	'TWRQC',0,1
	.word	675
	.byte	1,2,2,35,1,11
	.byte	'THRQC',0,1
	.word	675
	.byte	1,1,2,35,1,11
	.byte	'TRRQC',0,1
	.word	675
	.byte	1,0,2,35,1,11
	.byte	'PEC',0,1
	.word	675
	.byte	1,7,2,35,2,11
	.byte	'TCC',0,1
	.word	675
	.byte	1,6,2,35,2,11
	.byte	'FEC',0,1
	.word	675
	.byte	1,5,2,35,2,11
	.byte	'HTC',0,1
	.word	675
	.byte	1,4,2,35,2,11
	.byte	'RTC',0,1
	.word	675
	.byte	1,3,2,35,2,11
	.byte	'BDC',0,1
	.word	675
	.byte	1,2,2,35,2,11
	.byte	'LPC',0,1
	.word	675
	.byte	1,1,2,35,2,11
	.byte	'LAC',0,1
	.word	675
	.byte	1,0,2,35,2,11
	.byte	'LCC',0,1
	.word	675
	.byte	1,7,2,35,3,11
	.byte	'CEC',0,1
	.word	675
	.byte	1,6,2,35,3,11
	.byte	'RFOC',0,1
	.word	675
	.byte	1,5,2,35,3,11
	.byte	'RFUC',0,1
	.word	675
	.byte	1,4,2,35,3,11
	.byte	'RFLC',0,1
	.word	675
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	675
	.byte	1,2,2,35,3,11
	.byte	'TFOC',0,1
	.word	675
	.byte	1,1,2,35,3,11
	.byte	'TFLC',0,1
	.word	675
	.byte	1,0,2,35,3,0,12,12,239,3,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14322
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_FLAGSENABLE_Bits',0,12,212,1,16,4,11
	.byte	'THE',0,1
	.word	675
	.byte	1,7,2,35,0,11
	.byte	'TRE',0,1
	.word	675
	.byte	1,6,2,35,0,11
	.byte	'RHE',0,1
	.word	675
	.byte	1,5,2,35,0,11
	.byte	'RRE',0,1
	.word	675
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	675
	.byte	1,3,2,35,0,11
	.byte	'FEDE',0,1
	.word	675
	.byte	1,2,2,35,0,11
	.byte	'REDE',0,1
	.word	675
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	692
	.byte	9,0,2,35,0,11
	.byte	'PEE',0,1
	.word	675
	.byte	1,7,2,35,2,11
	.byte	'TCE',0,1
	.word	675
	.byte	1,6,2,35,2,11
	.byte	'FEE',0,1
	.word	675
	.byte	1,5,2,35,2,11
	.byte	'HTE',0,1
	.word	675
	.byte	1,4,2,35,2,11
	.byte	'RTE',0,1
	.word	675
	.byte	1,3,2,35,2,11
	.byte	'BDE',0,1
	.word	675
	.byte	1,2,2,35,2,11
	.byte	'LPE',0,1
	.word	675
	.byte	1,1,2,35,2,11
	.byte	'ABE',0,1
	.word	675
	.byte	1,0,2,35,2,11
	.byte	'LCE',0,1
	.word	675
	.byte	1,7,2,35,3,11
	.byte	'CEE',0,1
	.word	675
	.byte	1,6,2,35,3,11
	.byte	'RFOE',0,1
	.word	675
	.byte	1,5,2,35,3,11
	.byte	'RFUE',0,1
	.word	675
	.byte	1,4,2,35,3,11
	.byte	'RFLE',0,1
	.word	675
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	675
	.byte	1,2,2,35,3,11
	.byte	'TFOE',0,1
	.word	675
	.byte	1,1,2,35,3,11
	.byte	'TFLE',0,1
	.word	675
	.byte	1,0,2,35,3,0,12,12,247,3,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14837
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_TXDATA_Bits',0,12,143,3,16,4,11
	.byte	'DATA',0,4
	.word	500
	.byte	32,0,2,35,0,0,12,12,239,4,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15302
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_RXDATA_Bits',0,12,245,2,16,4,11
	.byte	'DATA',0,4
	.word	500
	.byte	32,0,2,35,0,0,12,12,215,4,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15389
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_CSR_Bits',0,12,128,1,16,4,11
	.byte	'CLKSEL',0,1
	.word	675
	.byte	5,3,2,35,0,11
	.byte	'reserved_5',0,4
	.word	500
	.byte	26,1,2,35,0,11
	.byte	'CON',0,1
	.word	675
	.byte	1,0,2,35,3,0,12,12,215,3,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15476
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_RXDATAD_Bits',0,12,251,2,16,4,11
	.byte	'DATA',0,4
	.word	500
	.byte	32,0,2,35,0,0,12,12,223,4,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15599
	.byte	4,2,35,0,0,18,148,1
	.word	675
	.byte	19,147,1,0,10
	.byte	'_Ifx_ASCLIN_OCS_Bits',0,12,235,2,16,4,11
	.byte	'reserved_0',0,4
	.word	500
	.byte	24,8,2,35,0,11
	.byte	'SUS',0,1
	.word	675
	.byte	4,4,2,35,3,11
	.byte	'SUS_P',0,1
	.word	675
	.byte	1,3,2,35,3,11
	.byte	'SUSSTA',0,1
	.word	675
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	675
	.byte	2,0,2,35,3,0,12,12,207,4,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15698
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_KRSTCLR_Bits',0,12,202,2,16,4,11
	.byte	'CLR',0,1
	.word	675
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	500
	.byte	31,0,2,35,0,0,12,12,175,4,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15861
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_KRST1_Bits',0,12,195,2,16,4,11
	.byte	'RST',0,1
	.word	675
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	500
	.byte	31,0,2,35,0,0,12,12,167,4,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15970
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_KRST0_Bits',0,12,187,2,16,4,11
	.byte	'RST',0,1
	.word	675
	.byte	1,7,2,35,0,11
	.byte	'RSTSTAT',0,1
	.word	675
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	500
	.byte	30,0,2,35,0,0,12,12,159,4,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16077
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_ACCEN1_Bits',0,12,82,16,4,11
	.byte	'reserved_0',0,4
	.word	500
	.byte	32,0,2,35,0,0,12,12,175,3,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16203
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_ACCEN0_Bits',0,12,45,16,4,11
	.byte	'EN0',0,1
	.word	675
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	675
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	675
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	675
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	675
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	675
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	675
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	675
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	675
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	675
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	675
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	675
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	675
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	675
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	675
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	675
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	675
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	675
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	675
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	675
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	675
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	675
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	675
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	675
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	675
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	675
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	675
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	675
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	675
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	675
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	675
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	675
	.byte	1,0,2,35,3,0,12,12,167,3,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16295
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN',0,12,153,5,25,128,2,13
	.byte	'CLC',0
	.word	10901
	.byte	4,2,35,0,13
	.byte	'IOCR',0
	.word	11196
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	11321
	.byte	4,2,35,8,13
	.byte	'TXFIFOCON',0
	.word	11546
	.byte	4,2,35,12,13
	.byte	'RXFIFOCON',0
	.word	11787
	.byte	4,2,35,16,13
	.byte	'BITCON',0
	.word	12008
	.byte	4,2,35,20,13
	.byte	'FRAMECON',0
	.word	12273
	.byte	4,2,35,24,13
	.byte	'DATCON',0
	.word	12470
	.byte	4,2,35,28,13
	.byte	'BRG',0
	.word	12627
	.byte	4,2,35,32,13
	.byte	'BRD',0
	.word	12781
	.byte	4,2,35,36,13
	.byte	'LIN',0
	.word	13318
	.byte	12,2,35,40,13
	.byte	'FLAGS',0
	.word	13769
	.byte	4,2,35,52,13
	.byte	'FLAGSSET',0
	.word	14282
	.byte	4,2,35,56,13
	.byte	'FLAGSCLEAR',0
	.word	14797
	.byte	4,2,35,60,13
	.byte	'FLAGSENABLE',0
	.word	15262
	.byte	4,2,35,64,13
	.byte	'TXDATA',0
	.word	15349
	.byte	4,2,35,68,13
	.byte	'RXDATA',0
	.word	15436
	.byte	4,2,35,72,13
	.byte	'CSR',0
	.word	15559
	.byte	4,2,35,76,13
	.byte	'RXDATAD',0
	.word	15647
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	15687
	.byte	148,1,2,35,84,13
	.byte	'OCS',0
	.word	15821
	.byte	4,3,35,232,1,13
	.byte	'KRSTCLR',0
	.word	15930
	.byte	4,3,35,236,1,13
	.byte	'KRST1',0
	.word	16037
	.byte	4,3,35,240,1,13
	.byte	'KRST0',0
	.word	16163
	.byte	4,3,35,244,1,13
	.byte	'ACCEN1',0
	.word	16255
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	16827
	.byte	4,3,35,252,1,0,14
	.word	16867
.L245:
	.byte	3
	.word	17309
.L390:
	.byte	4
	.byte	'IfxAsclin_setDenominator',0,3,11,181,17,17,1,1
.L392:
	.byte	5
	.byte	'asclin',0,11,181,17,54
	.word	17314
.L394:
	.byte	5
	.byte	'denominator',0,11,181,17,69
	.word	692
.L396:
	.byte	6,0
.L307:
	.byte	4
	.byte	'IfxAsclin_setDisableModuleRequest',0,3,11,187,17,17,1,1
.L310:
	.byte	5
	.byte	'asclin',0,11,187,17,63
	.word	17314
.L312:
	.byte	6,0
.L334:
	.byte	4
	.byte	'IfxAsclin_setEnableModuleRequest',0,3,11,193,17,17,1,1
.L337:
	.byte	5
	.byte	'asclin',0,11,193,17,62
	.word	17314
.L339:
	.byte	6,0
.L383:
	.byte	4
	.byte	'IfxAsclin_setNumerator',0,3,11,143,18,17,1,1
.L385:
	.byte	5
	.byte	'asclin',0,11,143,18,52
	.word	17314
.L387:
	.byte	5
	.byte	'numerator',0,11,143,18,67
	.word	692
.L389:
	.byte	6,0
.L279:
	.byte	15,11,228,1,9,1,16
	.byte	'IfxAsclin_OversamplingFactor_4',0,3,16
	.byte	'IfxAsclin_OversamplingFactor_5',0,4,16
	.byte	'IfxAsclin_OversamplingFactor_6',0,5,16
	.byte	'IfxAsclin_OversamplingFactor_7',0,6,16
	.byte	'IfxAsclin_OversamplingFactor_8',0,7,16
	.byte	'IfxAsclin_OversamplingFactor_9',0,8,16
	.byte	'IfxAsclin_OversamplingFactor_10',0,9,16
	.byte	'IfxAsclin_OversamplingFactor_11',0,10,16
	.byte	'IfxAsclin_OversamplingFactor_12',0,11,16
	.byte	'IfxAsclin_OversamplingFactor_13',0,12,16
	.byte	'IfxAsclin_OversamplingFactor_14',0,13,16
	.byte	'IfxAsclin_OversamplingFactor_15',0,14,16
	.byte	'IfxAsclin_OversamplingFactor_16',0,15,0
.L397:
	.byte	4
	.byte	'IfxAsclin_setOversampling',0,3,11,149,18,17,1,1
.L399:
	.byte	5
	.byte	'asclin',0,11,149,18,55
	.word	17314
.L401:
	.byte	5
	.byte	'ovsFactor',0,11,149,18,92
	.word	17578
.L403:
	.byte	6,0
.L375:
	.byte	4
	.byte	'IfxAsclin_setPrescaler',0,3,11,161,18,17,1,1
.L378:
	.byte	5
	.byte	'asclin',0,11,161,18,52
	.word	17314
.L380:
	.byte	5
	.byte	'prescaler',0,11,161,18,67
	.word	692
.L382:
	.byte	6,0
.L256:
	.byte	8
	.byte	'IfxAsclin_getClockSource',0,3,11,140,15,18
	.word	675
	.byte	1,1
.L257:
	.byte	5
	.byte	'asclin',0,11,140,15,55
	.word	17314
.L259:
	.byte	6,0
.L407:
	.byte	8
	.byte	'IfxAsclin_getClockStatus',0,3,11,146,15,20
	.word	675
	.byte	1,1
.L408:
	.byte	5
	.byte	'asclin',0,11,146,15,57
	.word	17314
.L410:
	.byte	6,0,4
	.byte	'IfxAsclin_enableCts',0,3,11,228,13,17,1,1,5
	.byte	'asclin',0,11,228,13,49
	.word	17314
	.byte	5
	.byte	'enable',0,11,228,13,65
	.word	675
	.byte	6,0
.L317:
	.byte	4
	.byte	'IfxAsclin_enableParityErrorFlag',0,3,11,160,14,17,1,1
.L319:
	.byte	5
	.byte	'asclin',0,11,160,14,61
	.word	17314
.L321:
	.byte	5
	.byte	'enable',0,11,160,14,77
	.word	675
.L323:
	.byte	6,0
.L324:
	.byte	4
	.byte	'IfxAsclin_enableRxFifoOverflowFlag',0,3,11,190,14,17,1,1
.L326:
	.byte	5
	.byte	'asclin',0,11,190,14,64
	.word	17314
.L328:
	.byte	5
	.byte	'enable',0,11,190,14,80
	.word	675
.L330:
	.byte	6,0,15,11,123,9,1,16
	.byte	'IfxAsclin_CtsInputSelect_0',0,0,16
	.byte	'IfxAsclin_CtsInputSelect_1',0,1,16
	.byte	'IfxAsclin_CtsInputSelect_2',0,2,16
	.byte	'IfxAsclin_CtsInputSelect_3',0,3,0,4
	.byte	'IfxAsclin_setCtsInput',0,3,11,169,17,17,1,1,5
	.byte	'asclin',0,11,169,17,51
	.word	17314
	.byte	5
	.byte	'ctsi',0,11,169,17,84
	.word	18483
	.byte	6,0,15,11,181,2,9,1,16
	.byte	'IfxAsclin_RxInputSelect_0',0,0,16
	.byte	'IfxAsclin_RxInputSelect_1',0,1,16
	.byte	'IfxAsclin_RxInputSelect_2',0,2,16
	.byte	'IfxAsclin_RxInputSelect_3',0,3,16
	.byte	'IfxAsclin_RxInputSelect_4',0,4,16
	.byte	'IfxAsclin_RxInputSelect_5',0,5,16
	.byte	'IfxAsclin_RxInputSelect_6',0,6,16
	.byte	'IfxAsclin_RxInputSelect_7',0,7,0,4
	.byte	'IfxAsclin_setRxInput',0,3,11,191,18,17,1,1,5
	.byte	'asclin',0,11,191,18,50
	.word	17314
	.byte	5
	.byte	'alti',0,11,191,18,82
	.word	18667
	.byte	6,0,20
	.byte	'__maxu',0
	.word	500
	.byte	1,1,1,1,21
	.word	500
	.byte	21
	.word	500
	.byte	0,22
	.word	244
	.byte	23
	.word	270
	.byte	6,0,22
	.word	305
	.byte	23
	.word	337
	.byte	6,0,22
	.word	350
	.byte	6,0,22
	.word	419
	.byte	23
	.word	438
	.byte	6,0,22
	.word	454
	.byte	23
	.word	469
	.byte	23
	.word	483
	.byte	6,0,22
	.word	1262
	.byte	23
	.word	1302
	.byte	23
	.word	1320
	.byte	6,0,22
	.word	1340
	.byte	23
	.word	1378
	.byte	23
	.word	1396
	.byte	6,0,24
	.byte	'IfxScuWdt_clearCpuEndinit',0,3,217,1,17,1,1,1,1,5
	.byte	'password',0,3,217,1,50
	.word	692
	.byte	0,24
	.byte	'IfxScuWdt_setCpuEndinit',0,3,239,1,17,1,1,1,1,5
	.byte	'password',0,3,239,1,48
	.word	692
	.byte	0,22
	.word	1416
	.byte	23
	.word	1467
	.byte	6,0,25
	.byte	'IfxScuWdt_getCpuWatchdogPassword',0,3,129,3,19
	.word	692
	.byte	1,1,1,1,22
	.word	1566
	.byte	6,0,22
	.word	1600
	.byte	6,0,22
	.word	1663
	.byte	23
	.word	1704
	.byte	6,0,22
	.word	1723
	.byte	23
	.word	1778
	.byte	6,0,22
	.word	1797
	.byte	23
	.word	1837
	.byte	23
	.word	1854
	.byte	17,6,0,0,22
	.word	9756
	.byte	23
	.word	9788
	.byte	23
	.word	9802
	.byte	23
	.word	9820
	.byte	6,0,22
	.word	10123
	.byte	23
	.word	10156
	.byte	23
	.word	10170
	.byte	23
	.word	10188
	.byte	23
	.word	10202
	.byte	6,0,22
	.word	10322
	.byte	23
	.word	10350
	.byte	23
	.word	10364
	.byte	23
	.word	10382
	.byte	6,0,25
	.byte	'IfxScuCcu_getBaud1Frequency',0,13,217,6,20
	.word	296
	.byte	1,1,1,1,25
	.byte	'IfxScuCcu_getBaud2Frequency',0,13,223,6,20
	.word	296
	.byte	1,1,1,1,25
	.byte	'IfxScuCcu_getOsc0Frequency',0,13,137,7,20
	.word	296
	.byte	1,1,1,1,25
	.byte	'IfxScuCcu_getPllErayFrequency',0,13,149,7,20
	.word	296
	.byte	1,1,1,1,25
	.byte	'IfxScuCcu_getSpbFrequency',0,13,179,7,20
	.word	296
	.byte	1,1,1,1,22
	.word	10739
	.byte	23
	.word	10767
	.byte	6,0,22
	.word	17319
	.byte	23
	.word	17352
	.byte	23
	.word	17368
	.byte	6,0,22
	.word	17391
	.byte	23
	.word	17433
	.byte	6,0,22
	.word	17451
	.byte	23
	.word	17492
	.byte	6,0,22
	.word	17510
	.byte	23
	.word	17541
	.byte	23
	.word	17557
	.byte	6,0,22
	.word	18021
	.byte	23
	.word	18055
	.byte	23
	.word	18071
	.byte	6,0,22
	.word	18092
	.byte	23
	.word	18123
	.byte	23
	.word	18139
	.byte	6,0,22
	.word	18160
	.byte	23
	.word	18197
	.byte	6,0,22
	.word	18215
	.byte	23
	.word	18252
	.byte	6,0
.L247:
	.byte	15,14,78,9,1,16
	.byte	'IfxAsclin_Index_none',0,127,16
	.byte	'IfxAsclin_Index_0',0,0,16
	.byte	'IfxAsclin_Index_1',0,1,16
	.byte	'IfxAsclin_Index_2',0,2,16
	.byte	'IfxAsclin_Index_3',0,3,0
.L254:
	.byte	15,11,110,9,1,16
	.byte	'IfxAsclin_ClockSource_noClock',0,0,16
	.byte	'IfxAsclin_ClockSource_kernelClock',0,1,16
	.byte	'IfxAsclin_ClockSource_oscillatorClock',0,2,16
	.byte	'IfxAsclin_ClockSource_flexRayClock',0,4,16
	.byte	'IfxAsclin_ClockSource_ascFastClock',0,8,16
	.byte	'IfxAsclin_ClockSource_ascSlowClock',0,16,0,22
	.word	18270
	.byte	23
	.word	18298
	.byte	23
	.word	18314
	.byte	6,0,22
	.word	18332
	.byte	23
	.word	18372
	.byte	23
	.word	18388
	.byte	6,0,22
	.word	18406
	.byte	23
	.word	18449
	.byte	23
	.word	18465
	.byte	6,0,22
	.word	18605
	.byte	23
	.word	18635
	.byte	23
	.word	18651
	.byte	6,0,22
	.word	18898
	.byte	23
	.word	18927
	.byte	23
	.word	18943
	.byte	6,0
.L281:
	.byte	15,11,196,2,9,1,16
	.byte	'IfxAsclin_SamplePointPosition_1',0,1,16
	.byte	'IfxAsclin_SamplePointPosition_2',0,2,16
	.byte	'IfxAsclin_SamplePointPosition_3',0,3,16
	.byte	'IfxAsclin_SamplePointPosition_4',0,4,16
	.byte	'IfxAsclin_SamplePointPosition_5',0,5,16
	.byte	'IfxAsclin_SamplePointPosition_6',0,6,16
	.byte	'IfxAsclin_SamplePointPosition_7',0,7,16
	.byte	'IfxAsclin_SamplePointPosition_8',0,8,16
	.byte	'IfxAsclin_SamplePointPosition_9',0,9,16
	.byte	'IfxAsclin_SamplePointPosition_10',0,10,16
	.byte	'IfxAsclin_SamplePointPosition_11',0,11,16
	.byte	'IfxAsclin_SamplePointPosition_12',0,12,16
	.byte	'IfxAsclin_SamplePointPosition_13',0,13,16
	.byte	'IfxAsclin_SamplePointPosition_14',0,14,16
	.byte	'IfxAsclin_SamplePointPosition_15',0,15,0
.L283:
	.byte	15,11,218,2,9,1,16
	.byte	'IfxAsclin_SamplesPerBit_one',0,0,16
	.byte	'IfxAsclin_SamplesPerBit_three',0,1,0
.L349:
	.byte	3
	.word	692
	.byte	14
	.word	15436
.L352:
	.byte	3
	.word	20720
.L356:
	.byte	3
	.word	1642
.L362:
	.byte	3
	.word	675
	.byte	14
	.word	15349
.L415:
	.byte	3
	.word	20740
	.byte	26
	.word	675
.L424:
	.byte	3
	.word	20750
	.byte	7
	.byte	'short int',0,2,5,27
	.byte	'__wchar_t',0,15,1,1
	.word	20760
	.byte	27
	.byte	'__size_t',0,15,1,1
	.word	500
	.byte	27
	.byte	'__ptrdiff_t',0,15,1,1
	.word	516
	.byte	28,1,3
	.word	20828
	.byte	27
	.byte	'__codeptr',0,15,1,1
	.word	20830
	.byte	27
	.byte	'boolean',0,16,101,29
	.word	675
	.byte	27
	.byte	'uint8',0,16,105,29
	.word	675
	.byte	27
	.byte	'uint16',0,16,109,29
	.word	692
	.byte	27
	.byte	'uint32',0,16,113,29
	.word	1642
	.byte	27
	.byte	'uint64',0,16,118,29
	.word	382
	.byte	27
	.byte	'sint16',0,16,126,29
	.word	20760
	.byte	7
	.byte	'long int',0,4,5,27
	.byte	'sint32',0,16,131,1,29
	.word	20943
	.byte	7
	.byte	'long long int',0,8,5,27
	.byte	'sint64',0,16,138,1,29
	.word	20971
	.byte	27
	.byte	'float32',0,16,167,1,29
	.word	296
	.byte	27
	.byte	'pvoid',0,17,57,28
	.word	414
	.byte	27
	.byte	'Ifx_TickTime',0,17,79,28
	.word	20971
	.byte	27
	.byte	'Ifx_Priority',0,17,103,16
	.word	692
	.byte	15,17,130,1,9,1,16
	.byte	'Ifx_RxSel_a',0,0,16
	.byte	'Ifx_RxSel_b',0,1,16
	.byte	'Ifx_RxSel_c',0,2,16
	.byte	'Ifx_RxSel_d',0,3,16
	.byte	'Ifx_RxSel_e',0,4,16
	.byte	'Ifx_RxSel_f',0,5,16
	.byte	'Ifx_RxSel_g',0,6,16
	.byte	'Ifx_RxSel_h',0,7,0,27
	.byte	'Ifx_RxSel',0,17,140,1,3
	.word	21077
	.byte	14
	.word	408
	.byte	3
	.word	21215
	.byte	29,17,143,1,9,8,13
	.byte	'module',0
	.word	21220
	.byte	4,2,35,0,13
	.byte	'index',0
	.word	20943
	.byte	4,2,35,4,0,27
	.byte	'IfxModule_IndexMap',0,17,147,1,3
	.word	21225
	.byte	27
	.byte	'Ifx_ASCLIN_ACCEN0_Bits',0,12,79,3
	.word	16295
	.byte	27
	.byte	'Ifx_ASCLIN_ACCEN1_Bits',0,12,85,3
	.word	16203
	.byte	27
	.byte	'Ifx_ASCLIN_BITCON_Bits',0,12,97,3
	.word	11827
	.byte	27
	.byte	'Ifx_ASCLIN_BRD_Bits',0,12,106,3
	.word	12667
	.byte	27
	.byte	'Ifx_ASCLIN_BRG_Bits',0,12,115,3
	.word	12510
	.byte	27
	.byte	'Ifx_ASCLIN_CLC_Bits',0,12,125,3
	.word	10782
	.byte	27
	.byte	'Ifx_ASCLIN_CSR_Bits',0,12,133,1,3
	.word	15476
	.byte	27
	.byte	'Ifx_ASCLIN_DATCON_Bits',0,12,145,1,3
	.word	12313
	.byte	27
	.byte	'Ifx_ASCLIN_FLAGS_Bits',0,12,177,1,3
	.word	13323
	.byte	27
	.byte	'Ifx_ASCLIN_FLAGSCLEAR_Bits',0,12,209,1,3
	.word	14322
	.byte	27
	.byte	'Ifx_ASCLIN_FLAGSENABLE_Bits',0,12,238,1,3
	.word	14837
	.byte	27
	.byte	'Ifx_ASCLIN_FLAGSSET_Bits',0,12,142,2,3
	.word	13809
	.byte	27
	.byte	'Ifx_ASCLIN_FRAMECON_Bits',0,12,158,2,3
	.word	12048
	.byte	27
	.byte	'Ifx_ASCLIN_ID_Bits',0,12,166,2,3
	.word	11236
	.byte	27
	.byte	'Ifx_ASCLIN_IOCR_Bits',0,12,184,2,3
	.word	10941
	.byte	27
	.byte	'Ifx_ASCLIN_KRST0_Bits',0,12,192,2,3
	.word	16077
	.byte	27
	.byte	'Ifx_ASCLIN_KRST1_Bits',0,12,199,2,3
	.word	15970
	.byte	27
	.byte	'Ifx_ASCLIN_KRSTCLR_Bits',0,12,206,2,3
	.word	15861
	.byte	27
	.byte	'Ifx_ASCLIN_LIN_BTIMER_Bits',0,12,213,2,3
	.word	13021
	.byte	27
	.byte	'Ifx_ASCLIN_LIN_CON_Bits',0,12,225,2,3
	.word	12821
	.byte	27
	.byte	'Ifx_ASCLIN_LIN_HTIMER_Bits',0,12,232,2,3
	.word	13135
	.byte	27
	.byte	'Ifx_ASCLIN_OCS_Bits',0,12,242,2,3
	.word	15698
	.byte	27
	.byte	'Ifx_ASCLIN_RXDATA_Bits',0,12,248,2,3
	.word	15389
	.byte	27
	.byte	'Ifx_ASCLIN_RXDATAD_Bits',0,12,254,2,3
	.word	15599
	.byte	27
	.byte	'Ifx_ASCLIN_RXFIFOCON_Bits',0,12,140,3,3
	.word	11586
	.byte	27
	.byte	'Ifx_ASCLIN_TXDATA_Bits',0,12,146,3,3
	.word	15302
	.byte	27
	.byte	'Ifx_ASCLIN_TXFIFOCON_Bits',0,12,159,3,3
	.word	11361
	.byte	27
	.byte	'Ifx_ASCLIN_ACCEN0',0,12,172,3,3
	.word	16827
	.byte	27
	.byte	'Ifx_ASCLIN_ACCEN1',0,12,180,3,3
	.word	16255
	.byte	27
	.byte	'Ifx_ASCLIN_BITCON',0,12,188,3,3
	.word	12008
	.byte	27
	.byte	'Ifx_ASCLIN_BRD',0,12,196,3,3
	.word	12781
	.byte	27
	.byte	'Ifx_ASCLIN_BRG',0,12,204,3,3
	.word	12627
	.byte	27
	.byte	'Ifx_ASCLIN_CLC',0,12,212,3,3
	.word	10901
	.byte	27
	.byte	'Ifx_ASCLIN_CSR',0,12,220,3,3
	.word	15559
	.byte	27
	.byte	'Ifx_ASCLIN_DATCON',0,12,228,3,3
	.word	12470
	.byte	27
	.byte	'Ifx_ASCLIN_FLAGS',0,12,236,3,3
	.word	13769
	.byte	27
	.byte	'Ifx_ASCLIN_FLAGSCLEAR',0,12,244,3,3
	.word	14797
	.byte	27
	.byte	'Ifx_ASCLIN_FLAGSENABLE',0,12,252,3,3
	.word	15262
	.byte	27
	.byte	'Ifx_ASCLIN_FLAGSSET',0,12,132,4,3
	.word	14282
	.byte	27
	.byte	'Ifx_ASCLIN_FRAMECON',0,12,140,4,3
	.word	12273
	.byte	27
	.byte	'Ifx_ASCLIN_ID',0,12,148,4,3
	.word	11321
	.byte	27
	.byte	'Ifx_ASCLIN_IOCR',0,12,156,4,3
	.word	11196
	.byte	27
	.byte	'Ifx_ASCLIN_KRST0',0,12,164,4,3
	.word	16163
	.byte	27
	.byte	'Ifx_ASCLIN_KRST1',0,12,172,4,3
	.word	16037
	.byte	27
	.byte	'Ifx_ASCLIN_KRSTCLR',0,12,180,4,3
	.word	15930
	.byte	27
	.byte	'Ifx_ASCLIN_LIN_BTIMER',0,12,188,4,3
	.word	13095
	.byte	27
	.byte	'Ifx_ASCLIN_LIN_CON',0,12,196,4,3
	.word	12981
	.byte	27
	.byte	'Ifx_ASCLIN_LIN_HTIMER',0,12,204,4,3
	.word	13210
	.byte	27
	.byte	'Ifx_ASCLIN_OCS',0,12,212,4,3
	.word	15821
	.byte	27
	.byte	'Ifx_ASCLIN_RXDATA',0,12,220,4,3
	.word	15436
	.byte	27
	.byte	'Ifx_ASCLIN_RXDATAD',0,12,228,4,3
	.word	15647
	.byte	27
	.byte	'Ifx_ASCLIN_RXFIFOCON',0,12,236,4,3
	.word	11787
	.byte	27
	.byte	'Ifx_ASCLIN_TXDATA',0,12,244,4,3
	.word	15349
	.byte	27
	.byte	'Ifx_ASCLIN_TXFIFOCON',0,12,252,4,3
	.word	11546
	.byte	14
	.word	13250
	.byte	27
	.byte	'Ifx_ASCLIN_LIN',0,12,140,5,3
	.word	22890
	.byte	14
	.word	16867
	.byte	27
	.byte	'Ifx_ASCLIN',0,12,181,5,3
	.word	22919
	.byte	27
	.byte	'IfxAsclin_Index',0,14,85,3
	.word	19704
	.byte	18,32
	.word	21225
	.byte	19,3,0,26
	.word	22968
	.byte	30
	.byte	'IfxAsclin_cfg_indexMap',0,14,93,41
	.word	22977
	.byte	1,1,15,18,236,10,9,1,16
	.byte	'IfxScu_CCUCON0_CLKSEL_fBack',0,0,16
	.byte	'IfxScu_CCUCON0_CLKSEL_fPll',0,1,0,27
	.byte	'IfxScu_CCUCON0_CLKSEL',0,18,240,10,3
	.word	23015
	.byte	15,18,250,10,9,1,16
	.byte	'IfxScu_WDTCON1_IR_divBy16384',0,0,16
	.byte	'IfxScu_WDTCON1_IR_divBy256',0,1,16
	.byte	'IfxScu_WDTCON1_IR_divBy64',0,2,0,27
	.byte	'IfxScu_WDTCON1_IR',0,18,255,10,3
	.word	23112
	.byte	10
	.byte	'_Ifx_SCU_ACCEN0_Bits',0,4,45,16,4,11
	.byte	'EN0',0,1
	.word	675
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	675
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	675
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	675
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	675
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	675
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	675
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	675
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	675
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	675
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	675
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	675
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	675
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	675
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	675
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	675
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	675
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	675
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	675
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	675
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	675
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	675
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	675
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	675
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	675
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	675
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	675
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	675
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	675
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	675
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	675
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	675
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_ACCEN0_Bits',0,4,79,3
	.word	23234
	.byte	10
	.byte	'_Ifx_SCU_ACCEN1_Bits',0,4,82,16,4,11
	.byte	'reserved_0',0,4
	.word	500
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_SCU_ACCEN1_Bits',0,4,85,3
	.word	23791
	.byte	10
	.byte	'_Ifx_SCU_ARSTDIS_Bits',0,4,88,16,4,11
	.byte	'STM0DIS',0,1
	.word	675
	.byte	1,7,2,35,0,11
	.byte	'STM1DIS',0,1
	.word	675
	.byte	1,6,2,35,0,11
	.byte	'STM2DIS',0,1
	.word	675
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	500
	.byte	29,0,2,35,0,0,27
	.byte	'Ifx_SCU_ARSTDIS_Bits',0,4,94,3
	.word	23868
	.byte	10
	.byte	'_Ifx_SCU_CCUCON0_Bits',0,4,97,16,4,11
	.byte	'BAUD1DIV',0,1
	.word	675
	.byte	4,4,2,35,0,11
	.byte	'BAUD2DIV',0,1
	.word	675
	.byte	4,0,2,35,0,11
	.byte	'SRIDIV',0,1
	.word	675
	.byte	4,4,2,35,1,11
	.byte	'LPDIV',0,1
	.word	675
	.byte	4,0,2,35,1,11
	.byte	'SPBDIV',0,1
	.word	675
	.byte	4,4,2,35,2,11
	.byte	'FSI2DIV',0,1
	.word	675
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	675
	.byte	2,0,2,35,2,11
	.byte	'FSIDIV',0,1
	.word	675
	.byte	2,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	675
	.byte	2,4,2,35,3,11
	.byte	'CLKSEL',0,1
	.word	675
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	675
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	675
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_CCUCON0_Bits',0,4,111,3
	.word	24004
	.byte	10
	.byte	'_Ifx_SCU_CCUCON1_Bits',0,4,114,16,4,11
	.byte	'CANDIV',0,1
	.word	675
	.byte	4,4,2,35,0,11
	.byte	'ERAYDIV',0,1
	.word	675
	.byte	4,0,2,35,0,11
	.byte	'STMDIV',0,1
	.word	675
	.byte	4,4,2,35,1,11
	.byte	'GTMDIV',0,1
	.word	675
	.byte	4,0,2,35,1,11
	.byte	'ETHDIV',0,1
	.word	675
	.byte	4,4,2,35,2,11
	.byte	'ASCLINFDIV',0,1
	.word	675
	.byte	4,0,2,35,2,11
	.byte	'ASCLINSDIV',0,1
	.word	675
	.byte	4,4,2,35,3,11
	.byte	'INSEL',0,1
	.word	675
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	675
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	675
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_CCUCON1_Bits',0,4,126,3
	.word	24284
	.byte	10
	.byte	'_Ifx_SCU_CCUCON2_Bits',0,4,129,1,16,4,11
	.byte	'BBBDIV',0,1
	.word	675
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	500
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	675
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	675
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_CCUCON2_Bits',0,4,135,1,3
	.word	24522
	.byte	10
	.byte	'_Ifx_SCU_CCUCON3_Bits',0,4,138,1,16,4,11
	.byte	'PLLDIV',0,1
	.word	675
	.byte	6,2,2,35,0,11
	.byte	'PLLSEL',0,1
	.word	675
	.byte	2,0,2,35,0,11
	.byte	'PLLERAYDIV',0,1
	.word	675
	.byte	6,2,2,35,1,11
	.byte	'PLLERAYSEL',0,1
	.word	675
	.byte	2,0,2,35,1,11
	.byte	'SRIDIV',0,1
	.word	675
	.byte	6,2,2,35,2,11
	.byte	'SRISEL',0,1
	.word	675
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	675
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	675
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	675
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	675
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_CCUCON3_Bits',0,4,150,1,3
	.word	24650
	.byte	10
	.byte	'_Ifx_SCU_CCUCON4_Bits',0,4,153,1,16,4,11
	.byte	'SPBDIV',0,1
	.word	675
	.byte	6,2,2,35,0,11
	.byte	'SPBSEL',0,1
	.word	675
	.byte	2,0,2,35,0,11
	.byte	'GTMDIV',0,1
	.word	675
	.byte	6,2,2,35,1,11
	.byte	'GTMSEL',0,1
	.word	675
	.byte	2,0,2,35,1,11
	.byte	'STMDIV',0,1
	.word	675
	.byte	6,2,2,35,2,11
	.byte	'STMSEL',0,1
	.word	675
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	675
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	675
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	675
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	675
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_CCUCON4_Bits',0,4,165,1,3
	.word	24893
	.byte	10
	.byte	'_Ifx_SCU_CCUCON5_Bits',0,4,168,1,16,4,11
	.byte	'MAXDIV',0,1
	.word	675
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	500
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	675
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	675
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_CCUCON5_Bits',0,4,174,1,3
	.word	25128
	.byte	10
	.byte	'_Ifx_SCU_CCUCON6_Bits',0,4,177,1,16,4,11
	.byte	'CPU0DIV',0,1
	.word	675
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	500
	.byte	26,0,2,35,0,0,27
	.byte	'Ifx_SCU_CCUCON6_Bits',0,4,181,1,3
	.word	25256
	.byte	10
	.byte	'_Ifx_SCU_CCUCON7_Bits',0,4,184,1,16,4,11
	.byte	'CPU1DIV',0,1
	.word	675
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	500
	.byte	26,0,2,35,0,0,27
	.byte	'Ifx_SCU_CCUCON7_Bits',0,4,188,1,3
	.word	25356
	.byte	10
	.byte	'_Ifx_SCU_CHIPID_Bits',0,4,191,1,16,4,11
	.byte	'CHREV',0,1
	.word	675
	.byte	6,2,2,35,0,11
	.byte	'CHTEC',0,1
	.word	675
	.byte	2,0,2,35,0,11
	.byte	'CHID',0,1
	.word	675
	.byte	8,0,2,35,1,11
	.byte	'EEA',0,1
	.word	675
	.byte	1,7,2,35,2,11
	.byte	'UCODE',0,1
	.word	675
	.byte	7,0,2,35,2,11
	.byte	'FSIZE',0,1
	.word	675
	.byte	4,4,2,35,3,11
	.byte	'SP',0,1
	.word	675
	.byte	2,2,2,35,3,11
	.byte	'SEC',0,1
	.word	675
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	675
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_CHIPID_Bits',0,4,202,1,3
	.word	25456
	.byte	10
	.byte	'_Ifx_SCU_DTSCON_Bits',0,4,205,1,16,4,11
	.byte	'PWD',0,1
	.word	675
	.byte	1,7,2,35,0,11
	.byte	'START',0,1
	.word	675
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	675
	.byte	2,4,2,35,0,11
	.byte	'CAL',0,4
	.word	500
	.byte	20,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	675
	.byte	7,1,2,35,3,11
	.byte	'SLCK',0,1
	.word	675
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_DTSCON_Bits',0,4,213,1,3
	.word	25664
	.byte	10
	.byte	'_Ifx_SCU_DTSLIM_Bits',0,4,216,1,16,4,11
	.byte	'LOWER',0,2
	.word	692
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	675
	.byte	5,1,2,35,1,11
	.byte	'LLU',0,1
	.word	675
	.byte	1,0,2,35,1,11
	.byte	'UPPER',0,2
	.word	692
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	675
	.byte	4,2,2,35,3,11
	.byte	'SLCK',0,1
	.word	675
	.byte	1,1,2,35,3,11
	.byte	'UOF',0,1
	.word	675
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_DTSLIM_Bits',0,4,225,1,3
	.word	25829
	.byte	10
	.byte	'_Ifx_SCU_DTSSTAT_Bits',0,4,228,1,16,4,11
	.byte	'RESULT',0,2
	.word	692
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	675
	.byte	4,2,2,35,1,11
	.byte	'RDY',0,1
	.word	675
	.byte	1,1,2,35,1,11
	.byte	'BUSY',0,1
	.word	675
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	692
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_SCU_DTSSTAT_Bits',0,4,235,1,3
	.word	26012
	.byte	10
	.byte	'_Ifx_SCU_EICR_Bits',0,4,238,1,16,4,11
	.byte	'reserved_0',0,1
	.word	675
	.byte	4,4,2,35,0,11
	.byte	'EXIS0',0,1
	.word	675
	.byte	3,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	675
	.byte	1,0,2,35,0,11
	.byte	'FEN0',0,1
	.word	675
	.byte	1,7,2,35,1,11
	.byte	'REN0',0,1
	.word	675
	.byte	1,6,2,35,1,11
	.byte	'LDEN0',0,1
	.word	675
	.byte	1,5,2,35,1,11
	.byte	'EIEN0',0,1
	.word	675
	.byte	1,4,2,35,1,11
	.byte	'INP0',0,1
	.word	675
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	500
	.byte	5,12,2,35,0,11
	.byte	'EXIS1',0,1
	.word	675
	.byte	3,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	675
	.byte	1,0,2,35,2,11
	.byte	'FEN1',0,1
	.word	675
	.byte	1,7,2,35,3,11
	.byte	'REN1',0,1
	.word	675
	.byte	1,6,2,35,3,11
	.byte	'LDEN1',0,1
	.word	675
	.byte	1,5,2,35,3,11
	.byte	'EIEN1',0,1
	.word	675
	.byte	1,4,2,35,3,11
	.byte	'INP1',0,1
	.word	675
	.byte	3,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	675
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EICR_Bits',0,4,129,2,3
	.word	26166
	.byte	10
	.byte	'_Ifx_SCU_EIFR_Bits',0,4,132,2,16,4,11
	.byte	'INTF0',0,1
	.word	675
	.byte	1,7,2,35,0,11
	.byte	'INTF1',0,1
	.word	675
	.byte	1,6,2,35,0,11
	.byte	'INTF2',0,1
	.word	675
	.byte	1,5,2,35,0,11
	.byte	'INTF3',0,1
	.word	675
	.byte	1,4,2,35,0,11
	.byte	'INTF4',0,1
	.word	675
	.byte	1,3,2,35,0,11
	.byte	'INTF5',0,1
	.word	675
	.byte	1,2,2,35,0,11
	.byte	'INTF6',0,1
	.word	675
	.byte	1,1,2,35,0,11
	.byte	'INTF7',0,1
	.word	675
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	500
	.byte	24,0,2,35,0,0,27
	.byte	'Ifx_SCU_EIFR_Bits',0,4,143,2,3
	.word	26530
	.byte	10
	.byte	'_Ifx_SCU_EMSR_Bits',0,4,146,2,16,4,11
	.byte	'POL',0,1
	.word	675
	.byte	1,7,2,35,0,11
	.byte	'MODE',0,1
	.word	675
	.byte	1,6,2,35,0,11
	.byte	'ENON',0,1
	.word	675
	.byte	1,5,2,35,0,11
	.byte	'PSEL',0,1
	.word	675
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	692
	.byte	12,0,2,35,0,11
	.byte	'EMSF',0,1
	.word	675
	.byte	1,7,2,35,2,11
	.byte	'SEMSF',0,1
	.word	675
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	675
	.byte	6,0,2,35,2,11
	.byte	'EMSFM',0,1
	.word	675
	.byte	2,6,2,35,3,11
	.byte	'SEMSFM',0,1
	.word	675
	.byte	2,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	675
	.byte	4,0,2,35,3,0,27
	.byte	'Ifx_SCU_EMSR_Bits',0,4,159,2,3
	.word	26741
	.byte	10
	.byte	'_Ifx_SCU_ESRCFG_Bits',0,4,162,2,16,4,11
	.byte	'reserved_0',0,1
	.word	675
	.byte	7,1,2,35,0,11
	.byte	'EDCON',0,2
	.word	692
	.byte	2,7,2,35,0,11
	.byte	'reserved_9',0,4
	.word	500
	.byte	23,0,2,35,0,0,27
	.byte	'Ifx_SCU_ESRCFG_Bits',0,4,167,2,3
	.word	26993
	.byte	10
	.byte	'_Ifx_SCU_ESROCFG_Bits',0,4,170,2,16,4,11
	.byte	'ARI',0,1
	.word	675
	.byte	1,7,2,35,0,11
	.byte	'ARC',0,1
	.word	675
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	500
	.byte	30,0,2,35,0,0,27
	.byte	'Ifx_SCU_ESROCFG_Bits',0,4,175,2,3
	.word	27111
	.byte	10
	.byte	'_Ifx_SCU_EVR13CON_Bits',0,4,178,2,16,4,11
	.byte	'reserved_0',0,4
	.word	500
	.byte	28,4,2,35,0,11
	.byte	'EVR13OFF',0,1
	.word	675
	.byte	1,3,2,35,3,11
	.byte	'BPEVR13OFF',0,1
	.word	675
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	675
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	675
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVR13CON_Bits',0,4,185,2,3
	.word	27222
	.byte	10
	.byte	'_Ifx_SCU_EVR33CON_Bits',0,4,188,2,16,4,11
	.byte	'reserved_0',0,4
	.word	500
	.byte	28,4,2,35,0,11
	.byte	'EVR33OFF',0,1
	.word	675
	.byte	1,3,2,35,3,11
	.byte	'BPEVR33OFF',0,1
	.word	675
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	675
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	675
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVR33CON_Bits',0,4,195,2,3
	.word	27385
	.byte	10
	.byte	'_Ifx_SCU_EVRADCSTAT_Bits',0,4,198,2,16,4,11
	.byte	'ADC13V',0,1
	.word	675
	.byte	8,0,2,35,0,11
	.byte	'ADC33V',0,1
	.word	675
	.byte	8,0,2,35,1,11
	.byte	'ADCSWDV',0,1
	.word	675
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	675
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	675
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVRADCSTAT_Bits',0,4,205,2,3
	.word	27548
	.byte	10
	.byte	'_Ifx_SCU_EVRDVSTAT_Bits',0,4,208,2,16,4,11
	.byte	'DVS13TRIM',0,1
	.word	675
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	675
	.byte	8,0,2,35,1,11
	.byte	'DVS33TRIM',0,1
	.word	675
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	675
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	675
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVRDVSTAT_Bits',0,4,215,2,3
	.word	27706
	.byte	10
	.byte	'_Ifx_SCU_EVRMONCTRL_Bits',0,4,218,2,16,4,11
	.byte	'EVR13OVMOD',0,1
	.word	675
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	675
	.byte	2,4,2,35,0,11
	.byte	'EVR13UVMOD',0,1
	.word	675
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	675
	.byte	2,0,2,35,0,11
	.byte	'EVR33OVMOD',0,1
	.word	675
	.byte	2,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	675
	.byte	2,4,2,35,1,11
	.byte	'EVR33UVMOD',0,1
	.word	675
	.byte	2,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	675
	.byte	2,0,2,35,1,11
	.byte	'SWDOVMOD',0,1
	.word	675
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	675
	.byte	2,4,2,35,2,11
	.byte	'SWDUVMOD',0,1
	.word	675
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,2
	.word	692
	.byte	10,0,2,35,2,0,27
	.byte	'Ifx_SCU_EVRMONCTRL_Bits',0,4,232,2,3
	.word	27871
	.byte	10
	.byte	'_Ifx_SCU_EVROSCCTRL_Bits',0,4,235,2,16,4,11
	.byte	'OSCTRIM',0,2
	.word	692
	.byte	10,6,2,35,0,11
	.byte	'OSCPTAT',0,1
	.word	675
	.byte	6,0,2,35,1,11
	.byte	'OSCANASEL',0,1
	.word	675
	.byte	4,4,2,35,2,11
	.byte	'HPBGTRIM',0,2
	.word	692
	.byte	7,5,2,35,2,11
	.byte	'HPBGCLKEN',0,1
	.word	675
	.byte	1,4,2,35,3,11
	.byte	'OSC3V3',0,1
	.word	675
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	675
	.byte	2,1,2,35,3,11
	.byte	'LCK',0,1
	.word	675
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVROSCCTRL_Bits',0,4,245,2,3
	.word	28200
	.byte	10
	.byte	'_Ifx_SCU_EVROVMON_Bits',0,4,248,2,16,4,11
	.byte	'EVR13OVVAL',0,1
	.word	675
	.byte	8,0,2,35,0,11
	.byte	'EVR33OVVAL',0,1
	.word	675
	.byte	8,0,2,35,1,11
	.byte	'SWDOVVAL',0,1
	.word	675
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	675
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	675
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVROVMON_Bits',0,4,255,2,3
	.word	28421
	.byte	10
	.byte	'_Ifx_SCU_EVRRSTCON_Bits',0,4,130,3,16,4,11
	.byte	'RST13TRIM',0,1
	.word	675
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	500
	.byte	16,8,2,35,0,11
	.byte	'RST13OFF',0,1
	.word	675
	.byte	1,7,2,35,3,11
	.byte	'BPRST13OFF',0,1
	.word	675
	.byte	1,6,2,35,3,11
	.byte	'RST33OFF',0,1
	.word	675
	.byte	1,5,2,35,3,11
	.byte	'BPRST33OFF',0,1
	.word	675
	.byte	1,4,2,35,3,11
	.byte	'RSTSWDOFF',0,1
	.word	675
	.byte	1,3,2,35,3,11
	.byte	'BPRSTSWDOFF',0,1
	.word	675
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	675
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	675
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVRRSTCON_Bits',0,4,142,3,3
	.word	28584
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF1_Bits',0,4,145,3,16,4,11
	.byte	'SD5P',0,1
	.word	675
	.byte	8,0,2,35,0,11
	.byte	'SD5I',0,1
	.word	675
	.byte	8,0,2,35,1,11
	.byte	'SD5D',0,1
	.word	675
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	675
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	675
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVRSDCOEFF1_Bits',0,4,152,3,3
	.word	28856
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF2_Bits',0,4,155,3,16,4,11
	.byte	'SD33P',0,1
	.word	675
	.byte	8,0,2,35,0,11
	.byte	'SD33I',0,1
	.word	675
	.byte	8,0,2,35,1,11
	.byte	'SD33D',0,1
	.word	675
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	675
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	675
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVRSDCOEFF2_Bits',0,4,162,3,3
	.word	29009
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF3_Bits',0,4,165,3,16,4,11
	.byte	'CT5REG0',0,1
	.word	675
	.byte	8,0,2,35,0,11
	.byte	'CT5REG1',0,1
	.word	675
	.byte	8,0,2,35,1,11
	.byte	'CT5REG2',0,1
	.word	675
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	675
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	675
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVRSDCOEFF3_Bits',0,4,172,3,3
	.word	29165
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF4_Bits',0,4,175,3,16,4,11
	.byte	'CT5REG3',0,1
	.word	675
	.byte	8,0,2,35,0,11
	.byte	'CT5REG4',0,1
	.word	675
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	692
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	675
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVRSDCOEFF4_Bits',0,4,181,3,3
	.word	29327
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF5_Bits',0,4,184,3,16,4,11
	.byte	'CT33REG0',0,1
	.word	675
	.byte	8,0,2,35,0,11
	.byte	'CT33REG1',0,1
	.word	675
	.byte	8,0,2,35,1,11
	.byte	'CT33REG2',0,1
	.word	675
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	675
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	675
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVRSDCOEFF5_Bits',0,4,191,3,3
	.word	29470
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF6_Bits',0,4,194,3,16,4,11
	.byte	'CT33REG3',0,1
	.word	675
	.byte	8,0,2,35,0,11
	.byte	'CT33REG4',0,1
	.word	675
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	692
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	675
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVRSDCOEFF6_Bits',0,4,200,3,3
	.word	29635
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL1_Bits',0,4,203,3,16,4,11
	.byte	'SDFREQSPRD',0,2
	.word	692
	.byte	16,0,2,35,0,11
	.byte	'SDFREQ',0,1
	.word	675
	.byte	8,0,2,35,2,11
	.byte	'SDSTEP',0,1
	.word	675
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	675
	.byte	2,2,2,35,3,11
	.byte	'SDSAMPLE',0,1
	.word	675
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	675
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVRSDCTRL1_Bits',0,4,211,3,3
	.word	29780
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL2_Bits',0,4,214,3,16,4,11
	.byte	'DRVP',0,1
	.word	675
	.byte	8,0,2,35,0,11
	.byte	'SDMINMAXDC',0,1
	.word	675
	.byte	8,0,2,35,1,11
	.byte	'DRVN',0,1
	.word	675
	.byte	8,0,2,35,2,11
	.byte	'SDLUT',0,1
	.word	675
	.byte	6,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	675
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	675
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVRSDCTRL2_Bits',0,4,222,3,3
	.word	29961
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL3_Bits',0,4,225,3,16,4,11
	.byte	'SDPWMPRE',0,1
	.word	675
	.byte	8,0,2,35,0,11
	.byte	'SDPID',0,1
	.word	675
	.byte	8,0,2,35,1,11
	.byte	'SDVOKLVL',0,1
	.word	675
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	675
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	675
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVRSDCTRL3_Bits',0,4,232,3,3
	.word	30135
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL4_Bits',0,4,235,3,16,4,11
	.byte	'reserved_0',0,1
	.word	675
	.byte	8,0,2,35,0,11
	.byte	'SYNCDIV',0,1
	.word	675
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	500
	.byte	20,1,2,35,0,11
	.byte	'LCK',0,1
	.word	675
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVRSDCTRL4_Bits',0,4,241,3,3
	.word	30295
	.byte	10
	.byte	'_Ifx_SCU_EVRSTAT_Bits',0,4,244,3,16,4,11
	.byte	'EVR13',0,1
	.word	675
	.byte	1,7,2,35,0,11
	.byte	'OV13',0,1
	.word	675
	.byte	1,6,2,35,0,11
	.byte	'EVR33',0,1
	.word	675
	.byte	1,5,2,35,0,11
	.byte	'OV33',0,1
	.word	675
	.byte	1,4,2,35,0,11
	.byte	'OVSWD',0,1
	.word	675
	.byte	1,3,2,35,0,11
	.byte	'UV13',0,1
	.word	675
	.byte	1,2,2,35,0,11
	.byte	'UV33',0,1
	.word	675
	.byte	1,1,2,35,0,11
	.byte	'UVSWD',0,1
	.word	675
	.byte	1,0,2,35,0,11
	.byte	'EXTPASS13',0,1
	.word	675
	.byte	1,7,2,35,1,11
	.byte	'EXTPASS33',0,1
	.word	675
	.byte	1,6,2,35,1,11
	.byte	'BGPROK',0,1
	.word	675
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	500
	.byte	21,0,2,35,0,0,27
	.byte	'Ifx_SCU_EVRSTAT_Bits',0,4,130,4,3
	.word	30439
	.byte	10
	.byte	'_Ifx_SCU_EVRTRIM_Bits',0,4,133,4,16,4,11
	.byte	'EVR13TRIM',0,1
	.word	675
	.byte	8,0,2,35,0,11
	.byte	'SDVOUTSEL',0,1
	.word	675
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	692
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	675
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVRTRIM_Bits',0,4,139,4,3
	.word	30713
	.byte	10
	.byte	'_Ifx_SCU_EVRUVMON_Bits',0,4,142,4,16,4,11
	.byte	'EVR13UVVAL',0,1
	.word	675
	.byte	8,0,2,35,0,11
	.byte	'EVR33UVVAL',0,1
	.word	675
	.byte	8,0,2,35,1,11
	.byte	'SWDUVVAL',0,1
	.word	675
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	675
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	675
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVRUVMON_Bits',0,4,149,4,3
	.word	30852
	.byte	10
	.byte	'_Ifx_SCU_EXTCON_Bits',0,4,152,4,16,4,11
	.byte	'EN0',0,1
	.word	675
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	675
	.byte	1,6,2,35,0,11
	.byte	'SEL0',0,1
	.word	675
	.byte	4,2,2,35,0,11
	.byte	'reserved_6',0,2
	.word	692
	.byte	10,0,2,35,0,11
	.byte	'EN1',0,1
	.word	675
	.byte	1,7,2,35,2,11
	.byte	'NSEL',0,1
	.word	675
	.byte	1,6,2,35,2,11
	.byte	'SEL1',0,1
	.word	675
	.byte	4,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	675
	.byte	2,0,2,35,2,11
	.byte	'DIV1',0,1
	.word	675
	.byte	8,0,2,35,3,0,27
	.byte	'Ifx_SCU_EXTCON_Bits',0,4,163,4,3
	.word	31015
	.byte	10
	.byte	'_Ifx_SCU_FDR_Bits',0,4,166,4,16,4,11
	.byte	'STEP',0,2
	.word	692
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	675
	.byte	4,2,2,35,1,11
	.byte	'DM',0,1
	.word	675
	.byte	2,0,2,35,1,11
	.byte	'RESULT',0,2
	.word	692
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	675
	.byte	5,1,2,35,3,11
	.byte	'DISCLK',0,1
	.word	675
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_FDR_Bits',0,4,174,4,3
	.word	31233
	.byte	10
	.byte	'_Ifx_SCU_FMR_Bits',0,4,177,4,16,4,11
	.byte	'FS0',0,1
	.word	675
	.byte	1,7,2,35,0,11
	.byte	'FS1',0,1
	.word	675
	.byte	1,6,2,35,0,11
	.byte	'FS2',0,1
	.word	675
	.byte	1,5,2,35,0,11
	.byte	'FS3',0,1
	.word	675
	.byte	1,4,2,35,0,11
	.byte	'FS4',0,1
	.word	675
	.byte	1,3,2,35,0,11
	.byte	'FS5',0,1
	.word	675
	.byte	1,2,2,35,0,11
	.byte	'FS6',0,1
	.word	675
	.byte	1,1,2,35,0,11
	.byte	'FS7',0,1
	.word	675
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	675
	.byte	8,0,2,35,1,11
	.byte	'FC0',0,1
	.word	675
	.byte	1,7,2,35,2,11
	.byte	'FC1',0,1
	.word	675
	.byte	1,6,2,35,2,11
	.byte	'FC2',0,1
	.word	675
	.byte	1,5,2,35,2,11
	.byte	'FC3',0,1
	.word	675
	.byte	1,4,2,35,2,11
	.byte	'FC4',0,1
	.word	675
	.byte	1,3,2,35,2,11
	.byte	'FC5',0,1
	.word	675
	.byte	1,2,2,35,2,11
	.byte	'FC6',0,1
	.word	675
	.byte	1,1,2,35,2,11
	.byte	'FC7',0,1
	.word	675
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	675
	.byte	8,0,2,35,3,0,27
	.byte	'Ifx_SCU_FMR_Bits',0,4,197,4,3
	.word	31396
	.byte	10
	.byte	'_Ifx_SCU_ID_Bits',0,4,200,4,16,4,11
	.byte	'MODREV',0,1
	.word	675
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	675
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	692
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_SCU_ID_Bits',0,4,205,4,3
	.word	31732
	.byte	10
	.byte	'_Ifx_SCU_IGCR_Bits',0,4,208,4,16,4,11
	.byte	'IPEN00',0,1
	.word	675
	.byte	1,7,2,35,0,11
	.byte	'IPEN01',0,1
	.word	675
	.byte	1,6,2,35,0,11
	.byte	'IPEN02',0,1
	.word	675
	.byte	1,5,2,35,0,11
	.byte	'IPEN03',0,1
	.word	675
	.byte	1,4,2,35,0,11
	.byte	'IPEN04',0,1
	.word	675
	.byte	1,3,2,35,0,11
	.byte	'IPEN05',0,1
	.word	675
	.byte	1,2,2,35,0,11
	.byte	'IPEN06',0,1
	.word	675
	.byte	1,1,2,35,0,11
	.byte	'IPEN07',0,1
	.word	675
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	675
	.byte	5,3,2,35,1,11
	.byte	'GEEN0',0,1
	.word	675
	.byte	1,2,2,35,1,11
	.byte	'IGP0',0,1
	.word	675
	.byte	2,0,2,35,1,11
	.byte	'IPEN10',0,1
	.word	675
	.byte	1,7,2,35,2,11
	.byte	'IPEN11',0,1
	.word	675
	.byte	1,6,2,35,2,11
	.byte	'IPEN12',0,1
	.word	675
	.byte	1,5,2,35,2,11
	.byte	'IPEN13',0,1
	.word	675
	.byte	1,4,2,35,2,11
	.byte	'IPEN14',0,1
	.word	675
	.byte	1,3,2,35,2,11
	.byte	'IPEN15',0,1
	.word	675
	.byte	1,2,2,35,2,11
	.byte	'IPEN16',0,1
	.word	675
	.byte	1,1,2,35,2,11
	.byte	'IPEN17',0,1
	.word	675
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	675
	.byte	5,3,2,35,3,11
	.byte	'GEEN1',0,1
	.word	675
	.byte	1,2,2,35,3,11
	.byte	'IGP1',0,1
	.word	675
	.byte	2,0,2,35,3,0,27
	.byte	'Ifx_SCU_IGCR_Bits',0,4,232,4,3
	.word	31839
	.byte	10
	.byte	'_Ifx_SCU_IN_Bits',0,4,235,4,16,4,11
	.byte	'P0',0,1
	.word	675
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	675
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	500
	.byte	30,0,2,35,0,0,27
	.byte	'Ifx_SCU_IN_Bits',0,4,240,4,3
	.word	32291
	.byte	10
	.byte	'_Ifx_SCU_IOCR_Bits',0,4,243,4,16,4,11
	.byte	'reserved_0',0,1
	.word	675
	.byte	4,4,2,35,0,11
	.byte	'PC0',0,1
	.word	675
	.byte	4,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	675
	.byte	4,4,2,35,1,11
	.byte	'PC1',0,1
	.word	675
	.byte	4,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	692
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_SCU_IOCR_Bits',0,4,250,4,3
	.word	32390
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL0_Bits',0,4,253,4,16,4,11
	.byte	'LBISTREQ',0,1
	.word	675
	.byte	1,7,2,35,0,11
	.byte	'LBISTREQP',0,1
	.word	675
	.byte	1,6,2,35,0,11
	.byte	'PATTERNS',0,2
	.word	692
	.byte	14,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	692
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_SCU_LBISTCTRL0_Bits',0,4,131,5,3
	.word	32540
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL1_Bits',0,4,134,5,16,4,11
	.byte	'SEED',0,4
	.word	500
	.byte	23,9,2,35,0,11
	.byte	'reserved_23',0,1
	.word	675
	.byte	1,0,2,35,2,11
	.byte	'SPLITSH',0,1
	.word	675
	.byte	3,5,2,35,3,11
	.byte	'BODY',0,1
	.word	675
	.byte	1,4,2,35,3,11
	.byte	'LBISTFREQU',0,1
	.word	675
	.byte	4,0,2,35,3,0,27
	.byte	'Ifx_SCU_LBISTCTRL1_Bits',0,4,141,5,3
	.word	32689
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL2_Bits',0,4,144,5,16,4,11
	.byte	'SIGNATURE',0,4
	.word	500
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	675
	.byte	7,1,2,35,3,11
	.byte	'LBISTDONE',0,1
	.word	675
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_LBISTCTRL2_Bits',0,4,149,5,3
	.word	32850
	.byte	10
	.byte	'_Ifx_SCU_LCLCON_Bits',0,4,152,5,16,4,11
	.byte	'reserved_0',0,2
	.word	692
	.byte	16,0,2,35,0,11
	.byte	'LS',0,1
	.word	675
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,2
	.word	692
	.byte	14,1,2,35,2,11
	.byte	'LSEN',0,1
	.word	675
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_LCLCON_Bits',0,4,158,5,3
	.word	32980
	.byte	10
	.byte	'_Ifx_SCU_LCLTEST_Bits',0,4,161,5,16,4,11
	.byte	'LCLT0',0,1
	.word	675
	.byte	1,7,2,35,0,11
	.byte	'LCLT1',0,1
	.word	675
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	500
	.byte	30,0,2,35,0,0,27
	.byte	'Ifx_SCU_LCLTEST_Bits',0,4,166,5,3
	.word	33112
	.byte	10
	.byte	'_Ifx_SCU_MANID_Bits',0,4,169,5,16,4,11
	.byte	'DEPT',0,1
	.word	675
	.byte	5,3,2,35,0,11
	.byte	'MANUF',0,2
	.word	692
	.byte	11,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	692
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_SCU_MANID_Bits',0,4,174,5,3
	.word	33227
	.byte	10
	.byte	'_Ifx_SCU_OMR_Bits',0,4,177,5,16,4,11
	.byte	'PS0',0,1
	.word	675
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	675
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	692
	.byte	14,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	675
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	675
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	692
	.byte	14,0,2,35,2,0,27
	.byte	'Ifx_SCU_OMR_Bits',0,4,185,5,3
	.word	33338
	.byte	10
	.byte	'_Ifx_SCU_OSCCON_Bits',0,4,188,5,16,4,11
	.byte	'reserved_0',0,1
	.word	675
	.byte	1,7,2,35,0,11
	.byte	'PLLLV',0,1
	.word	675
	.byte	1,6,2,35,0,11
	.byte	'OSCRES',0,1
	.word	675
	.byte	1,5,2,35,0,11
	.byte	'GAINSEL',0,1
	.word	675
	.byte	2,3,2,35,0,11
	.byte	'MODE',0,1
	.word	675
	.byte	2,1,2,35,0,11
	.byte	'SHBY',0,1
	.word	675
	.byte	1,0,2,35,0,11
	.byte	'PLLHV',0,1
	.word	675
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	675
	.byte	1,6,2,35,1,11
	.byte	'X1D',0,1
	.word	675
	.byte	1,5,2,35,1,11
	.byte	'X1DEN',0,1
	.word	675
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	675
	.byte	4,0,2,35,1,11
	.byte	'OSCVAL',0,1
	.word	675
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	675
	.byte	2,1,2,35,2,11
	.byte	'APREN',0,1
	.word	675
	.byte	1,0,2,35,2,11
	.byte	'CAP0EN',0,1
	.word	675
	.byte	1,7,2,35,3,11
	.byte	'CAP1EN',0,1
	.word	675
	.byte	1,6,2,35,3,11
	.byte	'CAP2EN',0,1
	.word	675
	.byte	1,5,2,35,3,11
	.byte	'CAP3EN',0,1
	.word	675
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	675
	.byte	4,0,2,35,3,0,27
	.byte	'Ifx_SCU_OSCCON_Bits',0,4,209,5,3
	.word	33496
	.byte	10
	.byte	'_Ifx_SCU_OUT_Bits',0,4,212,5,16,4,11
	.byte	'P0',0,1
	.word	675
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	675
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	500
	.byte	30,0,2,35,0,0,27
	.byte	'Ifx_SCU_OUT_Bits',0,4,217,5,3
	.word	33908
	.byte	10
	.byte	'_Ifx_SCU_OVCCON_Bits',0,4,220,5,16,4,11
	.byte	'CSEL0',0,1
	.word	675
	.byte	1,7,2,35,0,11
	.byte	'CSEL1',0,1
	.word	675
	.byte	1,6,2,35,0,11
	.byte	'CSEL2',0,1
	.word	675
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,2
	.word	692
	.byte	13,0,2,35,0,11
	.byte	'OVSTRT',0,1
	.word	675
	.byte	1,7,2,35,2,11
	.byte	'OVSTP',0,1
	.word	675
	.byte	1,6,2,35,2,11
	.byte	'DCINVAL',0,1
	.word	675
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	675
	.byte	5,0,2,35,2,11
	.byte	'OVCONF',0,1
	.word	675
	.byte	1,7,2,35,3,11
	.byte	'POVCONF',0,1
	.word	675
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	675
	.byte	6,0,2,35,3,0,27
	.byte	'Ifx_SCU_OVCCON_Bits',0,4,233,5,3
	.word	34009
	.byte	10
	.byte	'_Ifx_SCU_OVCENABLE_Bits',0,4,236,5,16,4,11
	.byte	'OVEN0',0,1
	.word	675
	.byte	1,7,2,35,0,11
	.byte	'OVEN1',0,1
	.word	675
	.byte	1,6,2,35,0,11
	.byte	'OVEN2',0,1
	.word	675
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	500
	.byte	29,0,2,35,0,0,27
	.byte	'Ifx_SCU_OVCENABLE_Bits',0,4,242,5,3
	.word	34276
	.byte	10
	.byte	'_Ifx_SCU_PDISC_Bits',0,4,245,5,16,4,11
	.byte	'PDIS0',0,1
	.word	675
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	675
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	500
	.byte	30,0,2,35,0,0,27
	.byte	'Ifx_SCU_PDISC_Bits',0,4,250,5,3
	.word	34412
	.byte	10
	.byte	'_Ifx_SCU_PDR_Bits',0,4,253,5,16,4,11
	.byte	'PD0',0,1
	.word	675
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	675
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	675
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	675
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	500
	.byte	24,0,2,35,0,0,27
	.byte	'Ifx_SCU_PDR_Bits',0,4,132,6,3
	.word	34523
	.byte	10
	.byte	'_Ifx_SCU_PDRR_Bits',0,4,135,6,16,4,11
	.byte	'PDR0',0,1
	.word	675
	.byte	1,7,2,35,0,11
	.byte	'PDR1',0,1
	.word	675
	.byte	1,6,2,35,0,11
	.byte	'PDR2',0,1
	.word	675
	.byte	1,5,2,35,0,11
	.byte	'PDR3',0,1
	.word	675
	.byte	1,4,2,35,0,11
	.byte	'PDR4',0,1
	.word	675
	.byte	1,3,2,35,0,11
	.byte	'PDR5',0,1
	.word	675
	.byte	1,2,2,35,0,11
	.byte	'PDR6',0,1
	.word	675
	.byte	1,1,2,35,0,11
	.byte	'PDR7',0,1
	.word	675
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	500
	.byte	24,0,2,35,0,0,27
	.byte	'Ifx_SCU_PDRR_Bits',0,4,146,6,3
	.word	34656
	.byte	10
	.byte	'_Ifx_SCU_PLLCON0_Bits',0,4,149,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	675
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	675
	.byte	1,6,2,35,0,11
	.byte	'MODEN',0,1
	.word	675
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	675
	.byte	1,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	675
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	675
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	675
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	692
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	675
	.byte	7,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	675
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	675
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	675
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	675
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	675
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	675
	.byte	4,0,2,35,3,0,27
	.byte	'Ifx_SCU_PLLCON0_Bits',0,4,166,6,3
	.word	34859
	.byte	10
	.byte	'_Ifx_SCU_PLLCON1_Bits',0,4,169,6,16,4,11
	.byte	'K2DIV',0,1
	.word	675
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	675
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	675
	.byte	7,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	675
	.byte	1,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	675
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	692
	.byte	9,0,2,35,2,0,27
	.byte	'Ifx_SCU_PLLCON1_Bits',0,4,177,6,3
	.word	35215
	.byte	10
	.byte	'_Ifx_SCU_PLLCON2_Bits',0,4,180,6,16,4,11
	.byte	'MODCFG',0,2
	.word	692
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	692
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_SCU_PLLCON2_Bits',0,4,184,6,3
	.word	35393
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON0_Bits',0,4,187,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	675
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	675
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	675
	.byte	2,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	675
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	675
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	675
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	692
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	675
	.byte	5,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	675
	.byte	2,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	675
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	675
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	675
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	675
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	675
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	675
	.byte	4,0,2,35,3,0,27
	.byte	'Ifx_SCU_PLLERAYCON0_Bits',0,4,204,6,3
	.word	35493
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON1_Bits',0,4,207,6,16,4,11
	.byte	'K2DIV',0,1
	.word	675
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	675
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	675
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	675
	.byte	4,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	675
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	692
	.byte	9,0,2,35,2,0,27
	.byte	'Ifx_SCU_PLLERAYCON1_Bits',0,4,215,6,3
	.word	35863
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYSTAT_Bits',0,4,218,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	675
	.byte	1,7,2,35,0,11
	.byte	'PWDSTAT',0,1
	.word	675
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	675
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	675
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	675
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	675
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	500
	.byte	26,0,2,35,0,0,27
	.byte	'Ifx_SCU_PLLERAYSTAT_Bits',0,4,227,6,3
	.word	36049
	.byte	10
	.byte	'_Ifx_SCU_PLLSTAT_Bits',0,4,230,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	675
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	675
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	675
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	675
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	675
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	675
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	675
	.byte	1,1,2,35,0,11
	.byte	'MODRUN',0,1
	.word	675
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	500
	.byte	24,0,2,35,0,0,27
	.byte	'Ifx_SCU_PLLSTAT_Bits',0,4,241,6,3
	.word	36247
	.byte	10
	.byte	'_Ifx_SCU_PMCSR_Bits',0,4,244,6,16,4,11
	.byte	'REQSLP',0,1
	.word	675
	.byte	2,6,2,35,0,11
	.byte	'SMUSLP',0,1
	.word	675
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	675
	.byte	5,0,2,35,0,11
	.byte	'PMST',0,1
	.word	675
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	500
	.byte	21,0,2,35,0,0,27
	.byte	'Ifx_SCU_PMCSR_Bits',0,4,251,6,3
	.word	36480
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR0_Bits',0,4,254,6,16,4,11
	.byte	'reserved_0',0,1
	.word	675
	.byte	1,7,2,35,0,11
	.byte	'ESR1WKEN',0,1
	.word	675
	.byte	1,6,2,35,0,11
	.byte	'PINAWKEN',0,1
	.word	675
	.byte	1,5,2,35,0,11
	.byte	'PINBWKEN',0,1
	.word	675
	.byte	1,4,2,35,0,11
	.byte	'ESR0DFEN',0,1
	.word	675
	.byte	1,3,2,35,0,11
	.byte	'ESR0EDCON',0,1
	.word	675
	.byte	2,1,2,35,0,11
	.byte	'ESR1DFEN',0,1
	.word	675
	.byte	1,0,2,35,0,11
	.byte	'ESR1EDCON',0,1
	.word	675
	.byte	2,6,2,35,1,11
	.byte	'PINADFEN',0,1
	.word	675
	.byte	1,5,2,35,1,11
	.byte	'PINAEDCON',0,1
	.word	675
	.byte	2,3,2,35,1,11
	.byte	'PINBDFEN',0,1
	.word	675
	.byte	1,2,2,35,1,11
	.byte	'PINBEDCON',0,1
	.word	675
	.byte	2,0,2,35,1,11
	.byte	'SCREN',0,1
	.word	675
	.byte	1,7,2,35,2,11
	.byte	'STBYRAMSEL',0,1
	.word	675
	.byte	2,5,2,35,2,11
	.byte	'SCRCLKSEL',0,1
	.word	675
	.byte	1,4,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	675
	.byte	1,3,2,35,2,11
	.byte	'TRISTEN',0,1
	.word	675
	.byte	1,2,2,35,2,11
	.byte	'TRISTREQ',0,1
	.word	675
	.byte	1,1,2,35,2,11
	.byte	'PORSTDF',0,1
	.word	675
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	675
	.byte	1,7,2,35,3,11
	.byte	'DCDCSYNC',0,1
	.word	675
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	675
	.byte	3,3,2,35,3,11
	.byte	'ESR0TRIST',0,1
	.word	675
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	675
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	675
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_PMSWCR0_Bits',0,4,153,7,3
	.word	36632
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR1_Bits',0,4,156,7,16,4,11
	.byte	'SCRSTEN',0,1
	.word	675
	.byte	1,7,2,35,0,11
	.byte	'SCRSTREQ',0,1
	.word	675
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	675
	.byte	6,0,2,35,0,11
	.byte	'CPUIDLSEL',0,1
	.word	675
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	675
	.byte	1,4,2,35,1,11
	.byte	'IRADIS',0,1
	.word	675
	.byte	1,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	675
	.byte	3,0,2,35,1,11
	.byte	'SCRCFG',0,1
	.word	675
	.byte	8,0,2,35,2,11
	.byte	'CPUSEL',0,1
	.word	675
	.byte	3,5,2,35,3,11
	.byte	'STBYEVEN',0,1
	.word	675
	.byte	1,4,2,35,3,11
	.byte	'STBYEV',0,1
	.word	675
	.byte	3,1,2,35,3,11
	.byte	'LCK',0,1
	.word	675
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_PMSWCR1_Bits',0,4,170,7,3
	.word	37199
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR2_Bits',0,4,173,7,16,4,11
	.byte	'SCRINT',0,1
	.word	675
	.byte	8,0,2,35,0,11
	.byte	'BUSY',0,1
	.word	675
	.byte	1,7,2,35,1,11
	.byte	'SCRECC',0,1
	.word	675
	.byte	1,6,2,35,1,11
	.byte	'SCRWDT',0,1
	.word	675
	.byte	1,5,2,35,1,11
	.byte	'SCRRST',0,1
	.word	675
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	675
	.byte	4,0,2,35,1,11
	.byte	'TCINT',0,1
	.word	675
	.byte	8,0,2,35,2,11
	.byte	'TCINTREQ',0,1
	.word	675
	.byte	1,7,2,35,3,11
	.byte	'SMURST',0,1
	.word	675
	.byte	1,6,2,35,3,11
	.byte	'RST',0,1
	.word	675
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	675
	.byte	4,1,2,35,3,11
	.byte	'LCK',0,1
	.word	675
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_PMSWCR2_Bits',0,4,187,7,3
	.word	37493
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTAT_Bits',0,4,190,7,16,4,11
	.byte	'reserved_0',0,1
	.word	675
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKP',0,1
	.word	675
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUN',0,1
	.word	675
	.byte	1,4,2,35,0,11
	.byte	'PINAWKP',0,1
	.word	675
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUN',0,1
	.word	675
	.byte	1,2,2,35,0,11
	.byte	'PINBWKP',0,1
	.word	675
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUN',0,1
	.word	675
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	675
	.byte	1,7,2,35,1,11
	.byte	'PORSTDF',0,1
	.word	675
	.byte	1,6,2,35,1,11
	.byte	'HWCFGEVR',0,1
	.word	675
	.byte	3,3,2,35,1,11
	.byte	'STBYRAM',0,1
	.word	675
	.byte	2,1,2,35,1,11
	.byte	'TRIST',0,1
	.word	675
	.byte	1,0,2,35,1,11
	.byte	'SCRST',0,1
	.word	675
	.byte	1,7,2,35,2,11
	.byte	'SCRWKP',0,1
	.word	675
	.byte	1,6,2,35,2,11
	.byte	'SCR',0,1
	.word	675
	.byte	1,5,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	675
	.byte	1,4,2,35,2,11
	.byte	'ESR1WKEN',0,1
	.word	675
	.byte	1,3,2,35,2,11
	.byte	'PINAWKEN',0,1
	.word	675
	.byte	1,2,2,35,2,11
	.byte	'PINBWKEN',0,1
	.word	675
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	692
	.byte	4,5,2,35,2,11
	.byte	'ESR0TRIST',0,1
	.word	675
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	675
	.byte	4,0,2,35,3,0,27
	.byte	'Ifx_SCU_PMSWSTAT_Bits',0,4,214,7,3
	.word	37771
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTATCLR_Bits',0,4,217,7,16,4,11
	.byte	'reserved_0',0,1
	.word	675
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKPCLR',0,1
	.word	675
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUNCLR',0,1
	.word	675
	.byte	1,4,2,35,0,11
	.byte	'PINAWKPCLR',0,1
	.word	675
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUNCLR',0,1
	.word	675
	.byte	1,2,2,35,0,11
	.byte	'PINBWKPCLR',0,1
	.word	675
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUNCLR',0,1
	.word	675
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	675
	.byte	8,0,2,35,1,11
	.byte	'SCRSTCLR',0,1
	.word	675
	.byte	1,7,2,35,2,11
	.byte	'SCRWKPCLR',0,1
	.word	675
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	692
	.byte	14,0,2,35,2,0,27
	.byte	'Ifx_SCU_PMSWSTATCLR_Bits',0,4,230,7,3
	.word	38267
	.byte	10
	.byte	'_Ifx_SCU_RSTCON2_Bits',0,4,233,7,16,4,11
	.byte	'reserved_0',0,1
	.word	675
	.byte	1,7,2,35,0,11
	.byte	'CLRC',0,1
	.word	675
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	692
	.byte	10,4,2,35,0,11
	.byte	'CSS0',0,1
	.word	675
	.byte	1,3,2,35,1,11
	.byte	'CSS1',0,1
	.word	675
	.byte	1,2,2,35,1,11
	.byte	'CSS2',0,1
	.word	675
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	675
	.byte	1,0,2,35,1,11
	.byte	'USRINFO',0,2
	.word	692
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_SCU_RSTCON2_Bits',0,4,243,7,3
	.word	38580
	.byte	10
	.byte	'_Ifx_SCU_RSTCON_Bits',0,4,246,7,16,4,11
	.byte	'ESR0',0,1
	.word	675
	.byte	2,6,2,35,0,11
	.byte	'ESR1',0,1
	.word	675
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	675
	.byte	2,2,2,35,0,11
	.byte	'SMU',0,1
	.word	675
	.byte	2,0,2,35,0,11
	.byte	'SW',0,1
	.word	675
	.byte	2,6,2,35,1,11
	.byte	'STM0',0,1
	.word	675
	.byte	2,4,2,35,1,11
	.byte	'STM1',0,1
	.word	675
	.byte	2,2,2,35,1,11
	.byte	'STM2',0,1
	.word	675
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	692
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_SCU_RSTCON_Bits',0,4,129,8,3
	.word	38789
	.byte	10
	.byte	'_Ifx_SCU_RSTSTAT_Bits',0,4,132,8,16,4,11
	.byte	'ESR0',0,1
	.word	675
	.byte	1,7,2,35,0,11
	.byte	'ESR1',0,1
	.word	675
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	675
	.byte	1,5,2,35,0,11
	.byte	'SMU',0,1
	.word	675
	.byte	1,4,2,35,0,11
	.byte	'SW',0,1
	.word	675
	.byte	1,3,2,35,0,11
	.byte	'STM0',0,1
	.word	675
	.byte	1,2,2,35,0,11
	.byte	'STM1',0,1
	.word	675
	.byte	1,1,2,35,0,11
	.byte	'STM2',0,1
	.word	675
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	675
	.byte	8,0,2,35,1,11
	.byte	'PORST',0,1
	.word	675
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	675
	.byte	1,6,2,35,2,11
	.byte	'CB0',0,1
	.word	675
	.byte	1,5,2,35,2,11
	.byte	'CB1',0,1
	.word	675
	.byte	1,4,2,35,2,11
	.byte	'CB3',0,1
	.word	675
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	675
	.byte	2,1,2,35,2,11
	.byte	'EVR13',0,1
	.word	675
	.byte	1,0,2,35,2,11
	.byte	'EVR33',0,1
	.word	675
	.byte	1,7,2,35,3,11
	.byte	'SWD',0,1
	.word	675
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	675
	.byte	2,4,2,35,3,11
	.byte	'STBYR',0,1
	.word	675
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	675
	.byte	3,0,2,35,3,0,27
	.byte	'Ifx_SCU_RSTSTAT_Bits',0,4,155,8,3
	.word	39000
	.byte	10
	.byte	'_Ifx_SCU_SAFECON_Bits',0,4,158,8,16,4,11
	.byte	'HBT',0,1
	.word	675
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	500
	.byte	31,0,2,35,0,0,27
	.byte	'Ifx_SCU_SAFECON_Bits',0,4,162,8,3
	.word	39432
	.byte	10
	.byte	'_Ifx_SCU_STSTAT_Bits',0,4,165,8,16,4,11
	.byte	'HWCFG',0,1
	.word	675
	.byte	8,0,2,35,0,11
	.byte	'FTM',0,1
	.word	675
	.byte	7,1,2,35,1,11
	.byte	'MODE',0,1
	.word	675
	.byte	1,0,2,35,1,11
	.byte	'FCBAE',0,1
	.word	675
	.byte	1,7,2,35,2,11
	.byte	'LUDIS',0,1
	.word	675
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	675
	.byte	1,5,2,35,2,11
	.byte	'TRSTL',0,1
	.word	675
	.byte	1,4,2,35,2,11
	.byte	'SPDEN',0,1
	.word	675
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	675
	.byte	3,0,2,35,2,11
	.byte	'RAMINT',0,1
	.word	675
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	675
	.byte	7,0,2,35,3,0,27
	.byte	'Ifx_SCU_STSTAT_Bits',0,4,178,8,3
	.word	39528
	.byte	10
	.byte	'_Ifx_SCU_SWRSTCON_Bits',0,4,181,8,16,4,11
	.byte	'reserved_0',0,1
	.word	675
	.byte	1,7,2,35,0,11
	.byte	'SWRSTREQ',0,1
	.word	675
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	500
	.byte	30,0,2,35,0,0,27
	.byte	'Ifx_SCU_SWRSTCON_Bits',0,4,186,8,3
	.word	39788
	.byte	10
	.byte	'_Ifx_SCU_SYSCON_Bits',0,4,189,8,16,4,11
	.byte	'CCTRIG0',0,1
	.word	675
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	675
	.byte	1,6,2,35,0,11
	.byte	'RAMINTM',0,1
	.word	675
	.byte	2,4,2,35,0,11
	.byte	'SETLUDIS',0,1
	.word	675
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	675
	.byte	3,0,2,35,0,11
	.byte	'DATM',0,1
	.word	675
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,4
	.word	500
	.byte	23,0,2,35,0,0,27
	.byte	'Ifx_SCU_SYSCON_Bits',0,4,198,8,3
	.word	39913
	.byte	10
	.byte	'_Ifx_SCU_TRAPCLR_Bits',0,4,201,8,16,4,11
	.byte	'ESR0T',0,1
	.word	675
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	675
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	675
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	675
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	500
	.byte	28,0,2,35,0,0,27
	.byte	'Ifx_SCU_TRAPCLR_Bits',0,4,208,8,3
	.word	40110
	.byte	10
	.byte	'_Ifx_SCU_TRAPDIS_Bits',0,4,211,8,16,4,11
	.byte	'ESR0T',0,1
	.word	675
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	675
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	675
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	675
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	500
	.byte	28,0,2,35,0,0,27
	.byte	'Ifx_SCU_TRAPDIS_Bits',0,4,218,8,3
	.word	40263
	.byte	10
	.byte	'_Ifx_SCU_TRAPSET_Bits',0,4,221,8,16,4,11
	.byte	'ESR0T',0,1
	.word	675
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	675
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	675
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	675
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	500
	.byte	28,0,2,35,0,0,27
	.byte	'Ifx_SCU_TRAPSET_Bits',0,4,228,8,3
	.word	40416
	.byte	10
	.byte	'_Ifx_SCU_TRAPSTAT_Bits',0,4,231,8,16,4,11
	.byte	'ESR0T',0,1
	.word	675
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	675
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	675
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	675
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	500
	.byte	28,0,2,35,0,0,27
	.byte	'Ifx_SCU_TRAPSTAT_Bits',0,4,238,8,3
	.word	40569
	.byte	27
	.byte	'Ifx_SCU_WDTCPU_CON0_Bits',0,4,247,8,3
	.word	539
	.byte	27
	.byte	'Ifx_SCU_WDTCPU_CON1_Bits',0,4,134,9,3
	.word	714
	.byte	27
	.byte	'Ifx_SCU_WDTCPU_SR_Bits',0,4,150,9,3
	.word	958
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON0_Bits',0,4,153,9,16,4,11
	.byte	'ENDINIT',0,4
	.word	523
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	523
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	523
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	523
	.byte	16,0,2,35,0,0,27
	.byte	'Ifx_SCU_WDTS_CON0_Bits',0,4,159,9,3
	.word	40824
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON1_Bits',0,4,162,9,16,4,11
	.byte	'CLRIRF',0,1
	.word	675
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	675
	.byte	1,6,2,35,0,11
	.byte	'IR0',0,1
	.word	675
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	675
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	675
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	675
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	675
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	675
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	675
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	675
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	692
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_SCU_WDTS_CON1_Bits',0,4,175,9,3
	.word	40950
	.byte	10
	.byte	'_Ifx_SCU_WDTS_SR_Bits',0,4,178,9,16,4,11
	.byte	'AE',0,1
	.word	675
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	675
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	675
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	675
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	675
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	675
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	675
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	675
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	675
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	675
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	692
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_SCU_WDTS_SR_Bits',0,4,191,9,3
	.word	41202
	.byte	12,4,199,9,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23234
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_ACCEN0',0,4,204,9,3
	.word	41421
	.byte	12,4,207,9,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23791
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_ACCEN1',0,4,212,9,3
	.word	41485
	.byte	12,4,215,9,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23868
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_ARSTDIS',0,4,220,9,3
	.word	41549
	.byte	12,4,223,9,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24004
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_CCUCON0',0,4,228,9,3
	.word	41614
	.byte	12,4,231,9,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24284
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_CCUCON1',0,4,236,9,3
	.word	41679
	.byte	12,4,239,9,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24522
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_CCUCON2',0,4,244,9,3
	.word	41744
	.byte	12,4,247,9,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24650
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_CCUCON3',0,4,252,9,3
	.word	41809
	.byte	12,4,255,9,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24893
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_CCUCON4',0,4,132,10,3
	.word	41874
	.byte	12,4,135,10,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25128
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_CCUCON5',0,4,140,10,3
	.word	41939
	.byte	12,4,143,10,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25256
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_CCUCON6',0,4,148,10,3
	.word	42004
	.byte	12,4,151,10,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25356
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_CCUCON7',0,4,156,10,3
	.word	42069
	.byte	12,4,159,10,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25456
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_CHIPID',0,4,164,10,3
	.word	42134
	.byte	12,4,167,10,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25664
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_DTSCON',0,4,172,10,3
	.word	42198
	.byte	12,4,175,10,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25829
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_DTSLIM',0,4,180,10,3
	.word	42262
	.byte	12,4,183,10,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26012
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_DTSSTAT',0,4,188,10,3
	.word	42326
	.byte	12,4,191,10,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26166
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EICR',0,4,196,10,3
	.word	42391
	.byte	12,4,199,10,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26530
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EIFR',0,4,204,10,3
	.word	42453
	.byte	12,4,207,10,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26741
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EMSR',0,4,212,10,3
	.word	42515
	.byte	12,4,215,10,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26993
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_ESRCFG',0,4,220,10,3
	.word	42577
	.byte	12,4,223,10,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27111
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_ESROCFG',0,4,228,10,3
	.word	42641
	.byte	12,4,231,10,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27222
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVR13CON',0,4,236,10,3
	.word	42706
	.byte	12,4,239,10,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27385
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVR33CON',0,4,244,10,3
	.word	42772
	.byte	12,4,247,10,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27548
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVRADCSTAT',0,4,252,10,3
	.word	42838
	.byte	12,4,255,10,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27706
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVRDVSTAT',0,4,132,11,3
	.word	42906
	.byte	12,4,135,11,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27871
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVRMONCTRL',0,4,140,11,3
	.word	42973
	.byte	12,4,143,11,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28200
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVROSCCTRL',0,4,148,11,3
	.word	43041
	.byte	12,4,151,11,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28421
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVROVMON',0,4,156,11,3
	.word	43109
	.byte	12,4,159,11,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28584
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVRRSTCON',0,4,164,11,3
	.word	43175
	.byte	12,4,167,11,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28856
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVRSDCOEFF1',0,4,172,11,3
	.word	43242
	.byte	12,4,175,11,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29009
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVRSDCOEFF2',0,4,180,11,3
	.word	43311
	.byte	12,4,183,11,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29165
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVRSDCOEFF3',0,4,188,11,3
	.word	43380
	.byte	12,4,191,11,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29327
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVRSDCOEFF4',0,4,196,11,3
	.word	43449
	.byte	12,4,199,11,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29470
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVRSDCOEFF5',0,4,204,11,3
	.word	43518
	.byte	12,4,207,11,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29635
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVRSDCOEFF6',0,4,212,11,3
	.word	43587
	.byte	12,4,215,11,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29780
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVRSDCTRL1',0,4,220,11,3
	.word	43656
	.byte	12,4,223,11,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29961
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVRSDCTRL2',0,4,228,11,3
	.word	43724
	.byte	12,4,231,11,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30135
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVRSDCTRL3',0,4,236,11,3
	.word	43792
	.byte	12,4,239,11,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30295
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVRSDCTRL4',0,4,244,11,3
	.word	43860
	.byte	12,4,247,11,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30439
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVRSTAT',0,4,252,11,3
	.word	43928
	.byte	12,4,255,11,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30713
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVRTRIM',0,4,132,12,3
	.word	43993
	.byte	12,4,135,12,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30852
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVRUVMON',0,4,140,12,3
	.word	44058
	.byte	12,4,143,12,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31015
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EXTCON',0,4,148,12,3
	.word	44124
	.byte	12,4,151,12,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31233
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_FDR',0,4,156,12,3
	.word	44188
	.byte	12,4,159,12,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31396
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_FMR',0,4,164,12,3
	.word	44249
	.byte	12,4,167,12,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31732
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_ID',0,4,172,12,3
	.word	44310
	.byte	12,4,175,12,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31839
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_IGCR',0,4,180,12,3
	.word	44370
	.byte	12,4,183,12,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32291
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_IN',0,4,188,12,3
	.word	44432
	.byte	12,4,191,12,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32390
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_IOCR',0,4,196,12,3
	.word	44492
	.byte	12,4,199,12,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32540
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_LBISTCTRL0',0,4,204,12,3
	.word	44554
	.byte	12,4,207,12,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32689
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_LBISTCTRL1',0,4,212,12,3
	.word	44622
	.byte	12,4,215,12,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32850
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_LBISTCTRL2',0,4,220,12,3
	.word	44690
	.byte	12,4,223,12,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32980
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_LCLCON',0,4,228,12,3
	.word	44758
	.byte	12,4,231,12,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33112
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_LCLTEST',0,4,236,12,3
	.word	44822
	.byte	12,4,239,12,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33227
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_MANID',0,4,244,12,3
	.word	44887
	.byte	12,4,247,12,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33338
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_OMR',0,4,252,12,3
	.word	44950
	.byte	12,4,255,12,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33496
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_OSCCON',0,4,132,13,3
	.word	45011
	.byte	12,4,135,13,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33908
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_OUT',0,4,140,13,3
	.word	45075
	.byte	12,4,143,13,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34009
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_OVCCON',0,4,148,13,3
	.word	45136
	.byte	12,4,151,13,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34276
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_OVCENABLE',0,4,156,13,3
	.word	45200
	.byte	12,4,159,13,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34412
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_PDISC',0,4,164,13,3
	.word	45267
	.byte	12,4,167,13,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34523
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_PDR',0,4,172,13,3
	.word	45330
	.byte	12,4,175,13,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34656
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_PDRR',0,4,180,13,3
	.word	45391
	.byte	12,4,183,13,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34859
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_PLLCON0',0,4,188,13,3
	.word	45453
	.byte	12,4,191,13,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35215
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_PLLCON1',0,4,196,13,3
	.word	45518
	.byte	12,4,199,13,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35393
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_PLLCON2',0,4,204,13,3
	.word	45583
	.byte	12,4,207,13,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35493
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_PLLERAYCON0',0,4,212,13,3
	.word	45648
	.byte	12,4,215,13,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35863
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_PLLERAYCON1',0,4,220,13,3
	.word	45717
	.byte	12,4,223,13,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36049
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_PLLERAYSTAT',0,4,228,13,3
	.word	45786
	.byte	12,4,231,13,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36247
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_PLLSTAT',0,4,236,13,3
	.word	45855
	.byte	12,4,239,13,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36480
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_PMCSR',0,4,244,13,3
	.word	45920
	.byte	12,4,247,13,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36632
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_PMSWCR0',0,4,252,13,3
	.word	45983
	.byte	12,4,255,13,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37199
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_PMSWCR1',0,4,132,14,3
	.word	46048
	.byte	12,4,135,14,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37493
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_PMSWCR2',0,4,140,14,3
	.word	46113
	.byte	12,4,143,14,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37771
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_PMSWSTAT',0,4,148,14,3
	.word	46178
	.byte	12,4,151,14,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38267
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_PMSWSTATCLR',0,4,156,14,3
	.word	46244
	.byte	12,4,159,14,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38789
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_RSTCON',0,4,164,14,3
	.word	46313
	.byte	12,4,167,14,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38580
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_RSTCON2',0,4,172,14,3
	.word	46377
	.byte	12,4,175,14,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39000
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_RSTSTAT',0,4,180,14,3
	.word	46442
	.byte	12,4,183,14,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39432
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_SAFECON',0,4,188,14,3
	.word	46507
	.byte	12,4,191,14,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39528
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_STSTAT',0,4,196,14,3
	.word	46572
	.byte	12,4,199,14,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39788
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_SWRSTCON',0,4,204,14,3
	.word	46636
	.byte	12,4,207,14,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39913
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_SYSCON',0,4,212,14,3
	.word	46702
	.byte	12,4,215,14,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40110
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_TRAPCLR',0,4,220,14,3
	.word	46766
	.byte	12,4,223,14,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40263
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_TRAPDIS',0,4,228,14,3
	.word	46831
	.byte	12,4,231,14,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40416
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_TRAPSET',0,4,236,14,3
	.word	46896
	.byte	12,4,239,14,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40569
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_TRAPSTAT',0,4,244,14,3
	.word	46961
	.byte	27
	.byte	'Ifx_SCU_WDTCPU_CON0',0,4,252,14,3
	.word	635
	.byte	27
	.byte	'Ifx_SCU_WDTCPU_CON1',0,4,132,15,3
	.word	918
	.byte	27
	.byte	'Ifx_SCU_WDTCPU_SR',0,4,140,15,3
	.word	1149
	.byte	12,4,143,15,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40824
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_WDTS_CON0',0,4,148,15,3
	.word	47112
	.byte	12,4,151,15,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40950
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_WDTS_CON1',0,4,156,15,3
	.word	47179
	.byte	12,4,159,15,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41202
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_WDTS_SR',0,4,164,15,3
	.word	47246
	.byte	14
	.word	1189
	.byte	27
	.byte	'Ifx_SCU_WDTCPU',0,4,180,15,3
	.word	47311
	.byte	10
	.byte	'_Ifx_SCU_WDTS',0,4,183,15,25,12,13
	.byte	'CON0',0
	.word	47112
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	47179
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	47246
	.byte	4,2,35,8,0,14
	.word	47340
	.byte	27
	.byte	'Ifx_SCU_WDTS',0,4,188,15,3
	.word	47401
	.byte	18,8
	.word	42577
	.byte	19,1,0,18,20
	.word	675
	.byte	19,19,0,18,8
	.word	45920
	.byte	19,1,0,14
	.word	47340
	.byte	18,24
	.word	1189
	.byte	19,1,0,14
	.word	47460
	.byte	18,16
	.word	675
	.byte	19,15,0,18,28
	.word	675
	.byte	19,27,0,18,40
	.word	675
	.byte	19,39,0,18,16
	.word	42391
	.byte	19,3,0,18,16
	.word	44370
	.byte	19,3,0,18,180,3
	.word	675
	.byte	19,179,3,0,10
	.byte	'_Ifx_SCU',0,4,201,15,25,128,8,13
	.byte	'reserved_0',0
	.word	4698
	.byte	8,2,35,0,13
	.byte	'ID',0
	.word	44310
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	2879
	.byte	4,2,35,12,13
	.byte	'OSCCON',0
	.word	45011
	.byte	4,2,35,16,13
	.byte	'PLLSTAT',0
	.word	45855
	.byte	4,2,35,20,13
	.byte	'PLLCON0',0
	.word	45453
	.byte	4,2,35,24,13
	.byte	'PLLCON1',0
	.word	45518
	.byte	4,2,35,28,13
	.byte	'PLLCON2',0
	.word	45583
	.byte	4,2,35,32,13
	.byte	'PLLERAYSTAT',0
	.word	45786
	.byte	4,2,35,36,13
	.byte	'PLLERAYCON0',0
	.word	45648
	.byte	4,2,35,40,13
	.byte	'PLLERAYCON1',0
	.word	45717
	.byte	4,2,35,44,13
	.byte	'CCUCON0',0
	.word	41614
	.byte	4,2,35,48,13
	.byte	'CCUCON1',0
	.word	41679
	.byte	4,2,35,52,13
	.byte	'FDR',0
	.word	44188
	.byte	4,2,35,56,13
	.byte	'EXTCON',0
	.word	44124
	.byte	4,2,35,60,13
	.byte	'CCUCON2',0
	.word	41744
	.byte	4,2,35,64,13
	.byte	'CCUCON3',0
	.word	41809
	.byte	4,2,35,68,13
	.byte	'CCUCON4',0
	.word	41874
	.byte	4,2,35,72,13
	.byte	'CCUCON5',0
	.word	41939
	.byte	4,2,35,76,13
	.byte	'RSTSTAT',0
	.word	46442
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	2879
	.byte	4,2,35,84,13
	.byte	'RSTCON',0
	.word	46313
	.byte	4,2,35,88,13
	.byte	'ARSTDIS',0
	.word	41549
	.byte	4,2,35,92,13
	.byte	'SWRSTCON',0
	.word	46636
	.byte	4,2,35,96,13
	.byte	'RSTCON2',0
	.word	46377
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	2879
	.byte	4,2,35,104,13
	.byte	'EVRRSTCON',0
	.word	43175
	.byte	4,2,35,108,13
	.byte	'ESRCFG',0
	.word	47428
	.byte	8,2,35,112,13
	.byte	'ESROCFG',0
	.word	42641
	.byte	4,2,35,120,13
	.byte	'SYSCON',0
	.word	46702
	.byte	4,2,35,124,13
	.byte	'CCUCON6',0
	.word	42004
	.byte	4,3,35,128,1,13
	.byte	'CCUCON7',0
	.word	42069
	.byte	4,3,35,132,1,13
	.byte	'reserved_88',0
	.word	47437
	.byte	20,3,35,136,1,13
	.byte	'PDR',0
	.word	45330
	.byte	4,3,35,156,1,13
	.byte	'IOCR',0
	.word	44492
	.byte	4,3,35,160,1,13
	.byte	'OUT',0
	.word	45075
	.byte	4,3,35,164,1,13
	.byte	'OMR',0
	.word	44950
	.byte	4,3,35,168,1,13
	.byte	'IN',0
	.word	44432
	.byte	4,3,35,172,1,13
	.byte	'EVRSTAT',0
	.word	43928
	.byte	4,3,35,176,1,13
	.byte	'EVRDVSTAT',0
	.word	42906
	.byte	4,3,35,180,1,13
	.byte	'EVR13CON',0
	.word	42706
	.byte	4,3,35,184,1,13
	.byte	'EVR33CON',0
	.word	42772
	.byte	4,3,35,188,1,13
	.byte	'STSTAT',0
	.word	46572
	.byte	4,3,35,192,1,13
	.byte	'reserved_C4',0
	.word	2879
	.byte	4,3,35,196,1,13
	.byte	'PMSWCR0',0
	.word	45983
	.byte	4,3,35,200,1,13
	.byte	'PMSWSTAT',0
	.word	46178
	.byte	4,3,35,204,1,13
	.byte	'PMSWSTATCLR',0
	.word	46244
	.byte	4,3,35,208,1,13
	.byte	'PMCSR',0
	.word	47446
	.byte	8,3,35,212,1,13
	.byte	'reserved_DC',0
	.word	2879
	.byte	4,3,35,220,1,13
	.byte	'DTSSTAT',0
	.word	42326
	.byte	4,3,35,224,1,13
	.byte	'DTSCON',0
	.word	42198
	.byte	4,3,35,228,1,13
	.byte	'PMSWCR1',0
	.word	46048
	.byte	4,3,35,232,1,13
	.byte	'PMSWCR2',0
	.word	46113
	.byte	4,3,35,236,1,13
	.byte	'WDTS',0
	.word	47455
	.byte	12,3,35,240,1,13
	.byte	'EMSR',0
	.word	42515
	.byte	4,3,35,252,1,13
	.byte	'WDTCPU',0
	.word	47469
	.byte	24,3,35,128,2,13
	.byte	'reserved_118',0
	.word	5038
	.byte	12,3,35,152,2,13
	.byte	'TRAPSTAT',0
	.word	46961
	.byte	4,3,35,164,2,13
	.byte	'TRAPSET',0
	.word	46896
	.byte	4,3,35,168,2,13
	.byte	'TRAPCLR',0
	.word	46766
	.byte	4,3,35,172,2,13
	.byte	'TRAPDIS',0
	.word	46831
	.byte	4,3,35,176,2,13
	.byte	'reserved_134',0
	.word	2879
	.byte	4,3,35,180,2,13
	.byte	'LCLCON1',0
	.word	44758
	.byte	4,3,35,184,2,13
	.byte	'LCLTEST',0
	.word	44822
	.byte	4,3,35,188,2,13
	.byte	'CHIPID',0
	.word	42134
	.byte	4,3,35,192,2,13
	.byte	'MANID',0
	.word	44887
	.byte	4,3,35,196,2,13
	.byte	'reserved_148',0
	.word	4698
	.byte	8,3,35,200,2,13
	.byte	'SAFECON',0
	.word	46507
	.byte	4,3,35,208,2,13
	.byte	'reserved_154',0
	.word	47474
	.byte	16,3,35,212,2,13
	.byte	'LBISTCTRL0',0
	.word	44554
	.byte	4,3,35,228,2,13
	.byte	'LBISTCTRL1',0
	.word	44622
	.byte	4,3,35,232,2,13
	.byte	'LBISTCTRL2',0
	.word	44690
	.byte	4,3,35,236,2,13
	.byte	'reserved_170',0
	.word	47483
	.byte	28,3,35,240,2,13
	.byte	'PDISC',0
	.word	45267
	.byte	4,3,35,140,3,13
	.byte	'reserved_190',0
	.word	4698
	.byte	8,3,35,144,3,13
	.byte	'EVRTRIM',0
	.word	43993
	.byte	4,3,35,152,3,13
	.byte	'EVRADCSTAT',0
	.word	42838
	.byte	4,3,35,156,3,13
	.byte	'EVRUVMON',0
	.word	44058
	.byte	4,3,35,160,3,13
	.byte	'EVROVMON',0
	.word	43109
	.byte	4,3,35,164,3,13
	.byte	'EVRMONCTRL',0
	.word	42973
	.byte	4,3,35,168,3,13
	.byte	'reserved_1AC',0
	.word	2879
	.byte	4,3,35,172,3,13
	.byte	'EVRSDCTRL1',0
	.word	43656
	.byte	4,3,35,176,3,13
	.byte	'EVRSDCTRL2',0
	.word	43724
	.byte	4,3,35,180,3,13
	.byte	'EVRSDCTRL3',0
	.word	43792
	.byte	4,3,35,184,3,13
	.byte	'EVRSDCTRL4',0
	.word	43860
	.byte	4,3,35,188,3,13
	.byte	'EVRSDCOEFF1',0
	.word	43242
	.byte	4,3,35,192,3,13
	.byte	'EVRSDCOEFF2',0
	.word	43311
	.byte	4,3,35,196,3,13
	.byte	'EVRSDCOEFF3',0
	.word	43380
	.byte	4,3,35,200,3,13
	.byte	'EVRSDCOEFF4',0
	.word	43449
	.byte	4,3,35,204,3,13
	.byte	'EVRSDCOEFF5',0
	.word	43518
	.byte	4,3,35,208,3,13
	.byte	'EVRSDCOEFF6',0
	.word	43587
	.byte	4,3,35,212,3,13
	.byte	'EVROSCCTRL',0
	.word	43041
	.byte	4,3,35,216,3,13
	.byte	'reserved_1DC',0
	.word	2879
	.byte	4,3,35,220,3,13
	.byte	'OVCENABLE',0
	.word	45200
	.byte	4,3,35,224,3,13
	.byte	'OVCCON',0
	.word	45136
	.byte	4,3,35,228,3,13
	.byte	'reserved_1E8',0
	.word	47492
	.byte	40,3,35,232,3,13
	.byte	'EICR',0
	.word	47501
	.byte	16,3,35,144,4,13
	.byte	'EIFR',0
	.word	42453
	.byte	4,3,35,160,4,13
	.byte	'FMR',0
	.word	44249
	.byte	4,3,35,164,4,13
	.byte	'PDRR',0
	.word	45391
	.byte	4,3,35,168,4,13
	.byte	'IGCR',0
	.word	47510
	.byte	16,3,35,172,4,13
	.byte	'reserved_23C',0
	.word	2879
	.byte	4,3,35,188,4,13
	.byte	'DTSLIM',0
	.word	42262
	.byte	4,3,35,192,4,13
	.byte	'reserved_244',0
	.word	47519
	.byte	180,3,3,35,196,4,13
	.byte	'ACCEN1',0
	.word	41485
	.byte	4,3,35,248,7,13
	.byte	'ACCEN0',0
	.word	41421
	.byte	4,3,35,252,7,0,14
	.word	47530
	.byte	27
	.byte	'Ifx_SCU',0,4,181,16,3
	.word	49520
	.byte	10
	.byte	'_Ifx_CPU_A_Bits',0,19,45,16,4,11
	.byte	'ADDR',0,4
	.word	523
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_CPU_A_Bits',0,19,48,3
	.word	49542
	.byte	10
	.byte	'_Ifx_CPU_BIV_Bits',0,19,51,16,4,11
	.byte	'VSS',0,4
	.word	523
	.byte	1,31,2,35,0,11
	.byte	'BIV',0,4
	.word	523
	.byte	31,0,2,35,0,0,27
	.byte	'Ifx_CPU_BIV_Bits',0,19,55,3
	.word	49603
	.byte	10
	.byte	'_Ifx_CPU_BTV_Bits',0,19,58,16,4,11
	.byte	'reserved_0',0,4
	.word	523
	.byte	1,31,2,35,0,11
	.byte	'BTV',0,4
	.word	523
	.byte	31,0,2,35,0,0,27
	.byte	'Ifx_CPU_BTV_Bits',0,19,62,3
	.word	49682
	.byte	10
	.byte	'_Ifx_CPU_CCNT_Bits',0,19,65,16,4,11
	.byte	'CountValue',0,4
	.word	523
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	523
	.byte	1,0,2,35,0,0,27
	.byte	'Ifx_CPU_CCNT_Bits',0,19,69,3
	.word	49768
	.byte	10
	.byte	'_Ifx_CPU_CCTRL_Bits',0,19,72,16,4,11
	.byte	'CM',0,4
	.word	523
	.byte	1,31,2,35,0,11
	.byte	'CE',0,4
	.word	523
	.byte	1,30,2,35,0,11
	.byte	'M1',0,4
	.word	523
	.byte	3,27,2,35,0,11
	.byte	'M2',0,4
	.word	523
	.byte	3,24,2,35,0,11
	.byte	'M3',0,4
	.word	523
	.byte	3,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	523
	.byte	21,0,2,35,0,0,27
	.byte	'Ifx_CPU_CCTRL_Bits',0,19,80,3
	.word	49857
	.byte	10
	.byte	'_Ifx_CPU_COMPAT_Bits',0,19,83,16,4,11
	.byte	'reserved_0',0,4
	.word	523
	.byte	3,29,2,35,0,11
	.byte	'RM',0,4
	.word	523
	.byte	1,28,2,35,0,11
	.byte	'SP',0,4
	.word	523
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	523
	.byte	27,0,2,35,0,0,27
	.byte	'Ifx_CPU_COMPAT_Bits',0,19,89,3
	.word	50003
	.byte	10
	.byte	'_Ifx_CPU_CORE_ID_Bits',0,19,92,16,4,11
	.byte	'CORE_ID',0,4
	.word	523
	.byte	3,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	523
	.byte	29,0,2,35,0,0,27
	.byte	'Ifx_CPU_CORE_ID_Bits',0,19,96,3
	.word	50130
	.byte	10
	.byte	'_Ifx_CPU_CPR_L_Bits',0,19,99,16,4,11
	.byte	'reserved_0',0,4
	.word	523
	.byte	3,29,2,35,0,11
	.byte	'LOWBND',0,4
	.word	523
	.byte	29,0,2,35,0,0,27
	.byte	'Ifx_CPU_CPR_L_Bits',0,19,103,3
	.word	50228
	.byte	10
	.byte	'_Ifx_CPU_CPR_U_Bits',0,19,106,16,4,11
	.byte	'reserved_0',0,4
	.word	523
	.byte	3,29,2,35,0,11
	.byte	'UPPBND',0,4
	.word	523
	.byte	29,0,2,35,0,0,27
	.byte	'Ifx_CPU_CPR_U_Bits',0,19,110,3
	.word	50321
	.byte	10
	.byte	'_Ifx_CPU_CPU_ID_Bits',0,19,113,16,4,11
	.byte	'MODREV',0,4
	.word	523
	.byte	8,24,2,35,0,11
	.byte	'MOD_32B',0,4
	.word	523
	.byte	8,16,2,35,0,11
	.byte	'MOD',0,4
	.word	523
	.byte	16,0,2,35,0,0,27
	.byte	'Ifx_CPU_CPU_ID_Bits',0,19,118,3
	.word	50414
	.byte	10
	.byte	'_Ifx_CPU_CPXE_Bits',0,19,121,16,4,11
	.byte	'XE',0,4
	.word	523
	.byte	8,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	523
	.byte	24,0,2,35,0,0,27
	.byte	'Ifx_CPU_CPXE_Bits',0,19,125,3
	.word	50521
	.byte	10
	.byte	'_Ifx_CPU_CREVT_Bits',0,19,128,1,16,4,11
	.byte	'EVTA',0,4
	.word	523
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	523
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	523
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	523
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	523
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	523
	.byte	24,0,2,35,0,0,27
	.byte	'Ifx_CPU_CREVT_Bits',0,19,136,1,3
	.word	50608
	.byte	10
	.byte	'_Ifx_CPU_CUS_ID_Bits',0,19,139,1,16,4,11
	.byte	'CID',0,4
	.word	523
	.byte	3,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	523
	.byte	29,0,2,35,0,0,27
	.byte	'Ifx_CPU_CUS_ID_Bits',0,19,143,1,3
	.word	50762
	.byte	10
	.byte	'_Ifx_CPU_D_Bits',0,19,146,1,16,4,11
	.byte	'DATA',0,4
	.word	523
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_CPU_D_Bits',0,19,149,1,3
	.word	50856
	.byte	10
	.byte	'_Ifx_CPU_DATR_Bits',0,19,152,1,16,4,11
	.byte	'reserved_0',0,4
	.word	523
	.byte	3,29,2,35,0,11
	.byte	'SBE',0,4
	.word	523
	.byte	1,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	523
	.byte	5,23,2,35,0,11
	.byte	'CWE',0,4
	.word	523
	.byte	1,22,2,35,0,11
	.byte	'CFE',0,4
	.word	523
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	523
	.byte	3,18,2,35,0,11
	.byte	'SOE',0,4
	.word	523
	.byte	1,17,2,35,0,11
	.byte	'SME',0,4
	.word	523
	.byte	1,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	523
	.byte	16,0,2,35,0,0,27
	.byte	'Ifx_CPU_DATR_Bits',0,19,163,1,3
	.word	50919
	.byte	10
	.byte	'_Ifx_CPU_DBGSR_Bits',0,19,166,1,16,4,11
	.byte	'DE',0,4
	.word	523
	.byte	1,31,2,35,0,11
	.byte	'HALT',0,4
	.word	523
	.byte	2,29,2,35,0,11
	.byte	'SIH',0,4
	.word	523
	.byte	1,28,2,35,0,11
	.byte	'SUSP',0,4
	.word	523
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	523
	.byte	1,26,2,35,0,11
	.byte	'PREVSUSP',0,4
	.word	523
	.byte	1,25,2,35,0,11
	.byte	'PEVT',0,4
	.word	523
	.byte	1,24,2,35,0,11
	.byte	'EVTSRC',0,4
	.word	523
	.byte	5,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	523
	.byte	19,0,2,35,0,0,27
	.byte	'Ifx_CPU_DBGSR_Bits',0,19,177,1,3
	.word	51137
	.byte	10
	.byte	'_Ifx_CPU_DBGTCR_Bits',0,19,180,1,16,4,11
	.byte	'DTA',0,4
	.word	523
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	523
	.byte	31,0,2,35,0,0,27
	.byte	'Ifx_CPU_DBGTCR_Bits',0,19,184,1,3
	.word	51352
	.byte	10
	.byte	'_Ifx_CPU_DCON0_Bits',0,19,187,1,16,4,11
	.byte	'reserved_0',0,4
	.word	523
	.byte	1,31,2,35,0,11
	.byte	'DCBYP',0,4
	.word	523
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	523
	.byte	30,0,2,35,0,0,27
	.byte	'Ifx_CPU_DCON0_Bits',0,19,192,1,3
	.word	51446
	.byte	10
	.byte	'_Ifx_CPU_DCON2_Bits',0,19,195,1,16,4,11
	.byte	'DCACHE_SZE',0,4
	.word	523
	.byte	16,16,2,35,0,11
	.byte	'DSCRATCH_SZE',0,4
	.word	523
	.byte	16,0,2,35,0,0,27
	.byte	'Ifx_CPU_DCON2_Bits',0,19,199,1,3
	.word	51562
	.byte	10
	.byte	'_Ifx_CPU_DCX_Bits',0,19,202,1,16,4,11
	.byte	'reserved_0',0,4
	.word	523
	.byte	6,26,2,35,0,11
	.byte	'DCXValue',0,4
	.word	523
	.byte	26,0,2,35,0,0,27
	.byte	'Ifx_CPU_DCX_Bits',0,19,206,1,3
	.word	51663
	.byte	10
	.byte	'_Ifx_CPU_DEADD_Bits',0,19,209,1,16,4,11
	.byte	'ERROR_ADDRESS',0,4
	.word	523
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_CPU_DEADD_Bits',0,19,212,1,3
	.word	51756
	.byte	10
	.byte	'_Ifx_CPU_DIEAR_Bits',0,19,215,1,16,4,11
	.byte	'TA',0,4
	.word	523
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_CPU_DIEAR_Bits',0,19,218,1,3
	.word	51836
	.byte	10
	.byte	'_Ifx_CPU_DIETR_Bits',0,19,221,1,16,4,11
	.byte	'IED',0,4
	.word	523
	.byte	1,31,2,35,0,11
	.byte	'IE_T',0,4
	.word	523
	.byte	1,30,2,35,0,11
	.byte	'IE_C',0,4
	.word	523
	.byte	1,29,2,35,0,11
	.byte	'IE_S',0,4
	.word	523
	.byte	1,28,2,35,0,11
	.byte	'IE_BI',0,4
	.word	523
	.byte	1,27,2,35,0,11
	.byte	'E_INFO',0,4
	.word	523
	.byte	6,21,2,35,0,11
	.byte	'IE_DUAL',0,4
	.word	523
	.byte	1,20,2,35,0,11
	.byte	'IE_SP',0,4
	.word	523
	.byte	1,19,2,35,0,11
	.byte	'IE_BS',0,4
	.word	523
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	523
	.byte	18,0,2,35,0,0,27
	.byte	'Ifx_CPU_DIETR_Bits',0,19,233,1,3
	.word	51905
	.byte	10
	.byte	'_Ifx_CPU_DMS_Bits',0,19,236,1,16,4,11
	.byte	'reserved_0',0,4
	.word	523
	.byte	1,31,2,35,0,11
	.byte	'DMSValue',0,4
	.word	523
	.byte	31,0,2,35,0,0,27
	.byte	'Ifx_CPU_DMS_Bits',0,19,240,1,3
	.word	52134
	.byte	10
	.byte	'_Ifx_CPU_DPR_L_Bits',0,19,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	523
	.byte	3,29,2,35,0,11
	.byte	'LOWBND',0,4
	.word	523
	.byte	29,0,2,35,0,0,27
	.byte	'Ifx_CPU_DPR_L_Bits',0,19,247,1,3
	.word	52227
	.byte	10
	.byte	'_Ifx_CPU_DPR_U_Bits',0,19,250,1,16,4,11
	.byte	'reserved_0',0,4
	.word	523
	.byte	3,29,2,35,0,11
	.byte	'UPPBND',0,4
	.word	523
	.byte	29,0,2,35,0,0,27
	.byte	'Ifx_CPU_DPR_U_Bits',0,19,254,1,3
	.word	52322
	.byte	10
	.byte	'_Ifx_CPU_DPRE_Bits',0,19,129,2,16,4,11
	.byte	'RE',0,4
	.word	523
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	523
	.byte	16,0,2,35,0,0,27
	.byte	'Ifx_CPU_DPRE_Bits',0,19,133,2,3
	.word	52417
	.byte	10
	.byte	'_Ifx_CPU_DPWE_Bits',0,19,136,2,16,4,11
	.byte	'WE',0,4
	.word	523
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	523
	.byte	16,0,2,35,0,0,27
	.byte	'Ifx_CPU_DPWE_Bits',0,19,140,2,3
	.word	52507
	.byte	10
	.byte	'_Ifx_CPU_DSTR_Bits',0,19,143,2,16,4,11
	.byte	'SRE',0,4
	.word	523
	.byte	1,31,2,35,0,11
	.byte	'GAE',0,4
	.word	523
	.byte	1,30,2,35,0,11
	.byte	'LBE',0,4
	.word	523
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	523
	.byte	3,26,2,35,0,11
	.byte	'CRE',0,4
	.word	523
	.byte	1,25,2,35,0,11
	.byte	'reserved_7',0,4
	.word	523
	.byte	7,18,2,35,0,11
	.byte	'DTME',0,4
	.word	523
	.byte	1,17,2,35,0,11
	.byte	'LOE',0,4
	.word	523
	.byte	1,16,2,35,0,11
	.byte	'SDE',0,4
	.word	523
	.byte	1,15,2,35,0,11
	.byte	'SCE',0,4
	.word	523
	.byte	1,14,2,35,0,11
	.byte	'CAC',0,4
	.word	523
	.byte	1,13,2,35,0,11
	.byte	'MPE',0,4
	.word	523
	.byte	1,12,2,35,0,11
	.byte	'CLE',0,4
	.word	523
	.byte	1,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	523
	.byte	3,8,2,35,0,11
	.byte	'ALN',0,4
	.word	523
	.byte	1,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	523
	.byte	7,0,2,35,0,0,27
	.byte	'Ifx_CPU_DSTR_Bits',0,19,161,2,3
	.word	52597
	.byte	10
	.byte	'_Ifx_CPU_EXEVT_Bits',0,19,164,2,16,4,11
	.byte	'EVTA',0,4
	.word	523
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	523
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	523
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	523
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	523
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	523
	.byte	24,0,2,35,0,0,27
	.byte	'Ifx_CPU_EXEVT_Bits',0,19,172,2,3
	.word	52921
	.byte	10
	.byte	'_Ifx_CPU_FCX_Bits',0,19,175,2,16,4,11
	.byte	'FCXO',0,4
	.word	523
	.byte	16,16,2,35,0,11
	.byte	'FCXS',0,4
	.word	523
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	523
	.byte	12,0,2,35,0,0,27
	.byte	'Ifx_CPU_FCX_Bits',0,19,180,2,3
	.word	53075
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_CON_Bits',0,19,183,2,16,4,11
	.byte	'TST',0,4
	.word	523
	.byte	1,31,2,35,0,11
	.byte	'TCL',0,4
	.word	523
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	523
	.byte	6,24,2,35,0,11
	.byte	'RM',0,4
	.word	523
	.byte	2,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	523
	.byte	8,14,2,35,0,11
	.byte	'FXE',0,4
	.word	523
	.byte	1,13,2,35,0,11
	.byte	'FUE',0,4
	.word	523
	.byte	1,12,2,35,0,11
	.byte	'FZE',0,4
	.word	523
	.byte	1,11,2,35,0,11
	.byte	'FVE',0,4
	.word	523
	.byte	1,10,2,35,0,11
	.byte	'FIE',0,4
	.word	523
	.byte	1,9,2,35,0,11
	.byte	'reserved_23',0,4
	.word	523
	.byte	3,6,2,35,0,11
	.byte	'FX',0,4
	.word	523
	.byte	1,5,2,35,0,11
	.byte	'FU',0,4
	.word	523
	.byte	1,4,2,35,0,11
	.byte	'FZ',0,4
	.word	523
	.byte	1,3,2,35,0,11
	.byte	'FV',0,4
	.word	523
	.byte	1,2,2,35,0,11
	.byte	'FI',0,4
	.word	523
	.byte	1,1,2,35,0,11
	.byte	'reserved_31',0,4
	.word	523
	.byte	1,0,2,35,0,0,27
	.byte	'Ifx_CPU_FPU_TRAP_CON_Bits',0,19,202,2,3
	.word	53181
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_OPC_Bits',0,19,205,2,16,4,11
	.byte	'OPC',0,4
	.word	523
	.byte	8,24,2,35,0,11
	.byte	'FMT',0,4
	.word	523
	.byte	1,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	523
	.byte	7,16,2,35,0,11
	.byte	'DREG',0,4
	.word	523
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	523
	.byte	12,0,2,35,0,0,27
	.byte	'Ifx_CPU_FPU_TRAP_OPC_Bits',0,19,212,2,3
	.word	53530
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_PC_Bits',0,19,215,2,16,4,11
	.byte	'PC',0,4
	.word	523
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_CPU_FPU_TRAP_PC_Bits',0,19,218,2,3
	.word	53690
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC1_Bits',0,19,221,2,16,4,11
	.byte	'SRC1',0,4
	.word	523
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_CPU_FPU_TRAP_SRC1_Bits',0,19,224,2,3
	.word	53771
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC2_Bits',0,19,227,2,16,4,11
	.byte	'SRC2',0,4
	.word	523
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_CPU_FPU_TRAP_SRC2_Bits',0,19,230,2,3
	.word	53858
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC3_Bits',0,19,233,2,16,4,11
	.byte	'SRC3',0,4
	.word	523
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_CPU_FPU_TRAP_SRC3_Bits',0,19,236,2,3
	.word	53945
	.byte	10
	.byte	'_Ifx_CPU_ICNT_Bits',0,19,239,2,16,4,11
	.byte	'CountValue',0,4
	.word	523
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	523
	.byte	1,0,2,35,0,0,27
	.byte	'Ifx_CPU_ICNT_Bits',0,19,243,2,3
	.word	54032
	.byte	10
	.byte	'_Ifx_CPU_ICR_Bits',0,19,246,2,16,4,11
	.byte	'CCPN',0,4
	.word	523
	.byte	10,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	523
	.byte	5,17,2,35,0,11
	.byte	'IE',0,4
	.word	523
	.byte	1,16,2,35,0,11
	.byte	'PIPN',0,4
	.word	523
	.byte	10,6,2,35,0,11
	.byte	'reserved_26',0,4
	.word	523
	.byte	6,0,2,35,0,0,27
	.byte	'Ifx_CPU_ICR_Bits',0,19,253,2,3
	.word	54123
	.byte	10
	.byte	'_Ifx_CPU_ISP_Bits',0,19,128,3,16,4,11
	.byte	'ISP',0,4
	.word	523
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_CPU_ISP_Bits',0,19,131,3,3
	.word	54266
	.byte	10
	.byte	'_Ifx_CPU_LCX_Bits',0,19,134,3,16,4,11
	.byte	'LCXO',0,4
	.word	523
	.byte	16,16,2,35,0,11
	.byte	'LCXS',0,4
	.word	523
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	523
	.byte	12,0,2,35,0,0,27
	.byte	'Ifx_CPU_LCX_Bits',0,19,139,3,3
	.word	54332
	.byte	10
	.byte	'_Ifx_CPU_M1CNT_Bits',0,19,142,3,16,4,11
	.byte	'CountValue',0,4
	.word	523
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	523
	.byte	1,0,2,35,0,0,27
	.byte	'Ifx_CPU_M1CNT_Bits',0,19,146,3,3
	.word	54438
	.byte	10
	.byte	'_Ifx_CPU_M2CNT_Bits',0,19,149,3,16,4,11
	.byte	'CountValue',0,4
	.word	523
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	523
	.byte	1,0,2,35,0,0,27
	.byte	'Ifx_CPU_M2CNT_Bits',0,19,153,3,3
	.word	54531
	.byte	10
	.byte	'_Ifx_CPU_M3CNT_Bits',0,19,156,3,16,4,11
	.byte	'CountValue',0,4
	.word	523
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	523
	.byte	1,0,2,35,0,0,27
	.byte	'Ifx_CPU_M3CNT_Bits',0,19,160,3,3
	.word	54624
	.byte	10
	.byte	'_Ifx_CPU_PC_Bits',0,19,163,3,16,4,11
	.byte	'reserved_0',0,4
	.word	523
	.byte	1,31,2,35,0,11
	.byte	'PC',0,4
	.word	523
	.byte	31,0,2,35,0,0,27
	.byte	'Ifx_CPU_PC_Bits',0,19,167,3,3
	.word	54717
	.byte	10
	.byte	'_Ifx_CPU_PCON0_Bits',0,19,170,3,16,4,11
	.byte	'reserved_0',0,4
	.word	523
	.byte	1,31,2,35,0,11
	.byte	'PCBYP',0,4
	.word	523
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	523
	.byte	30,0,2,35,0,0,27
	.byte	'Ifx_CPU_PCON0_Bits',0,19,175,3,3
	.word	54802
	.byte	10
	.byte	'_Ifx_CPU_PCON1_Bits',0,19,178,3,16,4,11
	.byte	'PCINV',0,4
	.word	523
	.byte	1,31,2,35,0,11
	.byte	'PBINV',0,4
	.word	523
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	523
	.byte	30,0,2,35,0,0,27
	.byte	'Ifx_CPU_PCON1_Bits',0,19,183,3,3
	.word	54918
	.byte	10
	.byte	'_Ifx_CPU_PCON2_Bits',0,19,186,3,16,4,11
	.byte	'PCACHE_SZE',0,4
	.word	523
	.byte	16,16,2,35,0,11
	.byte	'PSCRATCH_SZE',0,4
	.word	523
	.byte	16,0,2,35,0,0,27
	.byte	'Ifx_CPU_PCON2_Bits',0,19,190,3,3
	.word	55029
	.byte	10
	.byte	'_Ifx_CPU_PCXI_Bits',0,19,193,3,16,4,11
	.byte	'PCXO',0,4
	.word	523
	.byte	16,16,2,35,0,11
	.byte	'PCXS',0,4
	.word	523
	.byte	4,12,2,35,0,11
	.byte	'UL',0,4
	.word	523
	.byte	1,11,2,35,0,11
	.byte	'PIE',0,4
	.word	523
	.byte	1,10,2,35,0,11
	.byte	'PCPN',0,4
	.word	523
	.byte	10,0,2,35,0,0,27
	.byte	'Ifx_CPU_PCXI_Bits',0,19,200,3,3
	.word	55130
	.byte	10
	.byte	'_Ifx_CPU_PIEAR_Bits',0,19,203,3,16,4,11
	.byte	'TA',0,4
	.word	523
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_CPU_PIEAR_Bits',0,19,206,3,3
	.word	55260
	.byte	10
	.byte	'_Ifx_CPU_PIETR_Bits',0,19,209,3,16,4,11
	.byte	'IED',0,4
	.word	523
	.byte	1,31,2,35,0,11
	.byte	'IE_T',0,4
	.word	523
	.byte	1,30,2,35,0,11
	.byte	'IE_C',0,4
	.word	523
	.byte	1,29,2,35,0,11
	.byte	'IE_S',0,4
	.word	523
	.byte	1,28,2,35,0,11
	.byte	'IE_BI',0,4
	.word	523
	.byte	1,27,2,35,0,11
	.byte	'E_INFO',0,4
	.word	523
	.byte	6,21,2,35,0,11
	.byte	'IE_DUAL',0,4
	.word	523
	.byte	1,20,2,35,0,11
	.byte	'IE_SP',0,4
	.word	523
	.byte	1,19,2,35,0,11
	.byte	'IE_BS',0,4
	.word	523
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	523
	.byte	18,0,2,35,0,0,27
	.byte	'Ifx_CPU_PIETR_Bits',0,19,221,3,3
	.word	55329
	.byte	10
	.byte	'_Ifx_CPU_PMA0_Bits',0,19,224,3,16,4,11
	.byte	'reserved_0',0,4
	.word	523
	.byte	13,19,2,35,0,11
	.byte	'DAC',0,4
	.word	523
	.byte	3,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	523
	.byte	16,0,2,35,0,0,27
	.byte	'Ifx_CPU_PMA0_Bits',0,19,229,3,3
	.word	55558
	.byte	10
	.byte	'_Ifx_CPU_PMA1_Bits',0,19,232,3,16,4,11
	.byte	'reserved_0',0,4
	.word	523
	.byte	14,18,2,35,0,11
	.byte	'CAC',0,4
	.word	523
	.byte	2,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	523
	.byte	16,0,2,35,0,0,27
	.byte	'Ifx_CPU_PMA1_Bits',0,19,237,3,3
	.word	55671
	.byte	10
	.byte	'_Ifx_CPU_PMA2_Bits',0,19,240,3,16,4,11
	.byte	'PSI',0,4
	.word	523
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	523
	.byte	16,0,2,35,0,0,27
	.byte	'Ifx_CPU_PMA2_Bits',0,19,244,3,3
	.word	55784
	.byte	10
	.byte	'_Ifx_CPU_PSTR_Bits',0,19,247,3,16,4,11
	.byte	'FRE',0,4
	.word	523
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	523
	.byte	1,30,2,35,0,11
	.byte	'FBE',0,4
	.word	523
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	523
	.byte	9,20,2,35,0,11
	.byte	'FPE',0,4
	.word	523
	.byte	1,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	523
	.byte	1,18,2,35,0,11
	.byte	'FME',0,4
	.word	523
	.byte	1,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	523
	.byte	17,0,2,35,0,0,27
	.byte	'Ifx_CPU_PSTR_Bits',0,19,129,4,3
	.word	55875
	.byte	10
	.byte	'_Ifx_CPU_PSW_Bits',0,19,132,4,16,4,11
	.byte	'CDC',0,4
	.word	523
	.byte	7,25,2,35,0,11
	.byte	'CDE',0,4
	.word	523
	.byte	1,24,2,35,0,11
	.byte	'GW',0,4
	.word	523
	.byte	1,23,2,35,0,11
	.byte	'IS',0,4
	.word	523
	.byte	1,22,2,35,0,11
	.byte	'IO',0,4
	.word	523
	.byte	2,20,2,35,0,11
	.byte	'PRS',0,4
	.word	523
	.byte	2,18,2,35,0,11
	.byte	'S',0,4
	.word	523
	.byte	1,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	523
	.byte	12,5,2,35,0,11
	.byte	'SAV',0,4
	.word	523
	.byte	1,4,2,35,0,11
	.byte	'AV',0,4
	.word	523
	.byte	1,3,2,35,0,11
	.byte	'SV',0,4
	.word	523
	.byte	1,2,2,35,0,11
	.byte	'V',0,4
	.word	523
	.byte	1,1,2,35,0,11
	.byte	'C',0,4
	.word	523
	.byte	1,0,2,35,0,0,27
	.byte	'Ifx_CPU_PSW_Bits',0,19,147,4,3
	.word	56078
	.byte	10
	.byte	'_Ifx_CPU_SEGEN_Bits',0,19,150,4,16,4,11
	.byte	'ADFLIP',0,4
	.word	523
	.byte	8,24,2,35,0,11
	.byte	'ADTYPE',0,4
	.word	523
	.byte	2,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	523
	.byte	21,1,2,35,0,11
	.byte	'AE',0,4
	.word	523
	.byte	1,0,2,35,0,0,27
	.byte	'Ifx_CPU_SEGEN_Bits',0,19,156,4,3
	.word	56321
	.byte	10
	.byte	'_Ifx_CPU_SMACON_Bits',0,19,159,4,16,4,11
	.byte	'PC',0,4
	.word	523
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	523
	.byte	1,30,2,35,0,11
	.byte	'PT',0,4
	.word	523
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	523
	.byte	5,24,2,35,0,11
	.byte	'DC',0,4
	.word	523
	.byte	1,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	523
	.byte	1,22,2,35,0,11
	.byte	'DT',0,4
	.word	523
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	523
	.byte	13,8,2,35,0,11
	.byte	'IODT',0,4
	.word	523
	.byte	1,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	523
	.byte	7,0,2,35,0,0,27
	.byte	'Ifx_CPU_SMACON_Bits',0,19,171,4,3
	.word	56449
	.byte	10
	.byte	'_Ifx_CPU_SPROT_ACCENA_Bits',0,19,174,4,16,4,11
	.byte	'EN',0,4
	.word	500
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_CPU_SPROT_ACCENA_Bits',0,19,177,4,3
	.word	56690
	.byte	10
	.byte	'_Ifx_CPU_SPROT_ACCENB_Bits',0,19,180,4,16,4,11
	.byte	'reserved_0',0,4
	.word	500
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_CPU_SPROT_ACCENB_Bits',0,19,183,4,3
	.word	56773
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,19,186,4,16,4,11
	.byte	'EN',0,4
	.word	500
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,19,189,4,3
	.word	56864
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,19,192,4,16,4,11
	.byte	'reserved_0',0,4
	.word	500
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,19,195,4,3
	.word	56955
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_LA_Bits',0,19,198,4,16,4,11
	.byte	'reserved_0',0,1
	.word	675
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	500
	.byte	27,0,2,35,0,0,27
	.byte	'Ifx_CPU_SPROT_RGN_LA_Bits',0,19,202,4,3
	.word	57054
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_UA_Bits',0,19,205,4,16,4,11
	.byte	'reserved_0',0,1
	.word	675
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	500
	.byte	27,0,2,35,0,0,27
	.byte	'Ifx_CPU_SPROT_RGN_UA_Bits',0,19,209,4,3
	.word	57161
	.byte	10
	.byte	'_Ifx_CPU_SWEVT_Bits',0,19,212,4,16,4,11
	.byte	'EVTA',0,4
	.word	523
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	523
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	523
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	523
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	523
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	523
	.byte	24,0,2,35,0,0,27
	.byte	'Ifx_CPU_SWEVT_Bits',0,19,220,4,3
	.word	57268
	.byte	10
	.byte	'_Ifx_CPU_SYSCON_Bits',0,19,223,4,16,4,11
	.byte	'FCDSF',0,4
	.word	523
	.byte	1,31,2,35,0,11
	.byte	'PROTEN',0,4
	.word	523
	.byte	1,30,2,35,0,11
	.byte	'TPROTEN',0,4
	.word	523
	.byte	1,29,2,35,0,11
	.byte	'IS',0,4
	.word	523
	.byte	1,28,2,35,0,11
	.byte	'IT',0,4
	.word	523
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	523
	.byte	27,0,2,35,0,0,27
	.byte	'Ifx_CPU_SYSCON_Bits',0,19,231,4,3
	.word	57422
	.byte	10
	.byte	'_Ifx_CPU_TASK_ASI_Bits',0,19,234,4,16,4,11
	.byte	'ASI',0,4
	.word	523
	.byte	5,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	523
	.byte	27,0,2,35,0,0,27
	.byte	'Ifx_CPU_TASK_ASI_Bits',0,19,238,4,3
	.word	57583
	.byte	10
	.byte	'_Ifx_CPU_TPS_CON_Bits',0,19,241,4,16,4,11
	.byte	'TEXP0',0,4
	.word	523
	.byte	1,31,2,35,0,11
	.byte	'TEXP1',0,4
	.word	523
	.byte	1,30,2,35,0,11
	.byte	'TEXP2',0,4
	.word	523
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	523
	.byte	13,16,2,35,0,11
	.byte	'TTRAP',0,4
	.word	523
	.byte	1,15,2,35,0,11
	.byte	'reserved_17',0,4
	.word	523
	.byte	15,0,2,35,0,0,27
	.byte	'Ifx_CPU_TPS_CON_Bits',0,19,249,4,3
	.word	57681
	.byte	10
	.byte	'_Ifx_CPU_TPS_TIMER_Bits',0,19,252,4,16,4,11
	.byte	'Timer',0,4
	.word	523
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_CPU_TPS_TIMER_Bits',0,19,255,4,3
	.word	57853
	.byte	10
	.byte	'_Ifx_CPU_TR_ADR_Bits',0,19,130,5,16,4,11
	.byte	'ADDR',0,4
	.word	523
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_CPU_TR_ADR_Bits',0,19,133,5,3
	.word	57933
	.byte	10
	.byte	'_Ifx_CPU_TR_EVT_Bits',0,19,136,5,16,4,11
	.byte	'EVTA',0,4
	.word	523
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	523
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	523
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	523
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	523
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	523
	.byte	4,20,2,35,0,11
	.byte	'TYP',0,4
	.word	523
	.byte	1,19,2,35,0,11
	.byte	'RNG',0,4
	.word	523
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	523
	.byte	1,17,2,35,0,11
	.byte	'ASI_EN',0,4
	.word	523
	.byte	1,16,2,35,0,11
	.byte	'ASI',0,4
	.word	523
	.byte	5,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	523
	.byte	6,5,2,35,0,11
	.byte	'AST',0,4
	.word	523
	.byte	1,4,2,35,0,11
	.byte	'ALD',0,4
	.word	523
	.byte	1,3,2,35,0,11
	.byte	'reserved_29',0,4
	.word	523
	.byte	3,0,2,35,0,0,27
	.byte	'Ifx_CPU_TR_EVT_Bits',0,19,153,5,3
	.word	58006
	.byte	10
	.byte	'_Ifx_CPU_TRIG_ACC_Bits',0,19,156,5,16,4,11
	.byte	'T0',0,4
	.word	523
	.byte	1,31,2,35,0,11
	.byte	'T1',0,4
	.word	523
	.byte	1,30,2,35,0,11
	.byte	'T2',0,4
	.word	523
	.byte	1,29,2,35,0,11
	.byte	'T3',0,4
	.word	523
	.byte	1,28,2,35,0,11
	.byte	'T4',0,4
	.word	523
	.byte	1,27,2,35,0,11
	.byte	'T5',0,4
	.word	523
	.byte	1,26,2,35,0,11
	.byte	'T6',0,4
	.word	523
	.byte	1,25,2,35,0,11
	.byte	'T7',0,4
	.word	523
	.byte	1,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	523
	.byte	24,0,2,35,0,0,27
	.byte	'Ifx_CPU_TRIG_ACC_Bits',0,19,167,5,3
	.word	58324
	.byte	12,19,175,5,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49542
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_A',0,19,180,5,3
	.word	58519
	.byte	12,19,183,5,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49603
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_BIV',0,19,188,5,3
	.word	58578
	.byte	12,19,191,5,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49682
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_BTV',0,19,196,5,3
	.word	58639
	.byte	12,19,199,5,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49768
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_CCNT',0,19,204,5,3
	.word	58700
	.byte	12,19,207,5,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49857
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_CCTRL',0,19,212,5,3
	.word	58762
	.byte	12,19,215,5,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50003
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_COMPAT',0,19,220,5,3
	.word	58825
	.byte	12,19,223,5,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50130
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_CORE_ID',0,19,228,5,3
	.word	58889
	.byte	12,19,231,5,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50228
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_CPR_L',0,19,236,5,3
	.word	58954
	.byte	12,19,239,5,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50321
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_CPR_U',0,19,244,5,3
	.word	59017
	.byte	12,19,247,5,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50414
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_CPU_ID',0,19,252,5,3
	.word	59080
	.byte	12,19,255,5,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50521
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_CPXE',0,19,132,6,3
	.word	59144
	.byte	12,19,135,6,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50608
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_CREVT',0,19,140,6,3
	.word	59206
	.byte	12,19,143,6,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50762
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_CUS_ID',0,19,148,6,3
	.word	59269
	.byte	12,19,151,6,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50856
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_D',0,19,156,6,3
	.word	59333
	.byte	12,19,159,6,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50919
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_DATR',0,19,164,6,3
	.word	59392
	.byte	12,19,167,6,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51137
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_DBGSR',0,19,172,6,3
	.word	59454
	.byte	12,19,175,6,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51352
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_DBGTCR',0,19,180,6,3
	.word	59517
	.byte	12,19,183,6,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51446
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_DCON0',0,19,188,6,3
	.word	59581
	.byte	12,19,191,6,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51562
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_DCON2',0,19,196,6,3
	.word	59644
	.byte	12,19,199,6,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51663
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_DCX',0,19,204,6,3
	.word	59707
	.byte	12,19,207,6,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51756
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_DEADD',0,19,212,6,3
	.word	59768
	.byte	12,19,215,6,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51836
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_DIEAR',0,19,220,6,3
	.word	59831
	.byte	12,19,223,6,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51905
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_DIETR',0,19,228,6,3
	.word	59894
	.byte	12,19,231,6,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52134
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_DMS',0,19,236,6,3
	.word	59957
	.byte	12,19,239,6,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52227
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_DPR_L',0,19,244,6,3
	.word	60018
	.byte	12,19,247,6,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52322
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_DPR_U',0,19,252,6,3
	.word	60081
	.byte	12,19,255,6,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52417
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_DPRE',0,19,132,7,3
	.word	60144
	.byte	12,19,135,7,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52507
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_DPWE',0,19,140,7,3
	.word	60206
	.byte	12,19,143,7,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52597
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_DSTR',0,19,148,7,3
	.word	60268
	.byte	12,19,151,7,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52921
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_EXEVT',0,19,156,7,3
	.word	60330
	.byte	12,19,159,7,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53075
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_FCX',0,19,164,7,3
	.word	60393
	.byte	12,19,167,7,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53181
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_FPU_TRAP_CON',0,19,172,7,3
	.word	60454
	.byte	12,19,175,7,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53530
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_FPU_TRAP_OPC',0,19,180,7,3
	.word	60524
	.byte	12,19,183,7,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53690
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_FPU_TRAP_PC',0,19,188,7,3
	.word	60594
	.byte	12,19,191,7,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53771
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_FPU_TRAP_SRC1',0,19,196,7,3
	.word	60663
	.byte	12,19,199,7,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53858
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_FPU_TRAP_SRC2',0,19,204,7,3
	.word	60734
	.byte	12,19,207,7,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53945
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_FPU_TRAP_SRC3',0,19,212,7,3
	.word	60805
	.byte	12,19,215,7,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54032
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_ICNT',0,19,220,7,3
	.word	60876
	.byte	12,19,223,7,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54123
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_ICR',0,19,228,7,3
	.word	60938
	.byte	12,19,231,7,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54266
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_ISP',0,19,236,7,3
	.word	60999
	.byte	12,19,239,7,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54332
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_LCX',0,19,244,7,3
	.word	61060
	.byte	12,19,247,7,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54438
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_M1CNT',0,19,252,7,3
	.word	61121
	.byte	12,19,255,7,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54531
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_M2CNT',0,19,132,8,3
	.word	61184
	.byte	12,19,135,8,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54624
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_M3CNT',0,19,140,8,3
	.word	61247
	.byte	12,19,143,8,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54717
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_PC',0,19,148,8,3
	.word	61310
	.byte	12,19,151,8,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54802
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_PCON0',0,19,156,8,3
	.word	61370
	.byte	12,19,159,8,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54918
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_PCON1',0,19,164,8,3
	.word	61433
	.byte	12,19,167,8,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55029
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_PCON2',0,19,172,8,3
	.word	61496
	.byte	12,19,175,8,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55130
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_PCXI',0,19,180,8,3
	.word	61559
	.byte	12,19,183,8,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55260
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_PIEAR',0,19,188,8,3
	.word	61621
	.byte	12,19,191,8,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55329
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_PIETR',0,19,196,8,3
	.word	61684
	.byte	12,19,199,8,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55558
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_PMA0',0,19,204,8,3
	.word	61747
	.byte	12,19,207,8,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55671
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_PMA1',0,19,212,8,3
	.word	61809
	.byte	12,19,215,8,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55784
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_PMA2',0,19,220,8,3
	.word	61871
	.byte	12,19,223,8,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55875
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_PSTR',0,19,228,8,3
	.word	61933
	.byte	12,19,231,8,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56078
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_PSW',0,19,236,8,3
	.word	61995
	.byte	12,19,239,8,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56321
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_SEGEN',0,19,244,8,3
	.word	62056
	.byte	12,19,247,8,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56449
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_SMACON',0,19,252,8,3
	.word	62119
	.byte	12,19,255,8,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56690
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_SPROT_ACCENA',0,19,132,9,3
	.word	62183
	.byte	12,19,135,9,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56773
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_SPROT_ACCENB',0,19,140,9,3
	.word	62253
	.byte	12,19,143,9,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56864
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA',0,19,148,9,3
	.word	62323
	.byte	12,19,151,9,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56955
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB',0,19,156,9,3
	.word	62397
	.byte	12,19,159,9,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57054
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_SPROT_RGN_LA',0,19,164,9,3
	.word	62471
	.byte	12,19,167,9,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57161
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_SPROT_RGN_UA',0,19,172,9,3
	.word	62541
	.byte	12,19,175,9,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57268
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_SWEVT',0,19,180,9,3
	.word	62611
	.byte	12,19,183,9,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57422
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_SYSCON',0,19,188,9,3
	.word	62674
	.byte	12,19,191,9,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57583
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_TASK_ASI',0,19,196,9,3
	.word	62738
	.byte	12,19,199,9,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57681
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_TPS_CON',0,19,204,9,3
	.word	62804
	.byte	12,19,207,9,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57853
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_TPS_TIMER',0,19,212,9,3
	.word	62869
	.byte	12,19,215,9,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57933
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_TR_ADR',0,19,220,9,3
	.word	62936
	.byte	12,19,223,9,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58006
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_TR_EVT',0,19,228,9,3
	.word	63000
	.byte	12,19,231,9,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58324
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_TRIG_ACC',0,19,236,9,3
	.word	63064
	.byte	10
	.byte	'_Ifx_CPU_CPR',0,19,247,9,25,8,13
	.byte	'L',0
	.word	58954
	.byte	4,2,35,0,13
	.byte	'U',0
	.word	59017
	.byte	4,2,35,4,0,14
	.word	63130
	.byte	27
	.byte	'Ifx_CPU_CPR',0,19,251,9,3
	.word	63172
	.byte	10
	.byte	'_Ifx_CPU_DPR',0,19,254,9,25,8,13
	.byte	'L',0
	.word	60018
	.byte	4,2,35,0,13
	.byte	'U',0
	.word	60081
	.byte	4,2,35,4,0,14
	.word	63198
	.byte	27
	.byte	'Ifx_CPU_DPR',0,19,130,10,3
	.word	63240
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN',0,19,133,10,25,16,13
	.byte	'LA',0
	.word	62471
	.byte	4,2,35,0,13
	.byte	'UA',0
	.word	62541
	.byte	4,2,35,4,13
	.byte	'ACCENA',0
	.word	62323
	.byte	4,2,35,8,13
	.byte	'ACCENB',0
	.word	62397
	.byte	4,2,35,12,0,14
	.word	63266
	.byte	27
	.byte	'Ifx_CPU_SPROT_RGN',0,19,139,10,3
	.word	63348
	.byte	18,12
	.word	62869
	.byte	19,2,0,10
	.byte	'_Ifx_CPU_TPS',0,19,142,10,25,16,13
	.byte	'CON',0
	.word	62804
	.byte	4,2,35,0,13
	.byte	'TIMER',0
	.word	63380
	.byte	12,2,35,4,0,14
	.word	63389
	.byte	27
	.byte	'Ifx_CPU_TPS',0,19,146,10,3
	.word	63437
	.byte	10
	.byte	'_Ifx_CPU_TR',0,19,149,10,25,8,13
	.byte	'EVT',0
	.word	63000
	.byte	4,2,35,0,13
	.byte	'ADR',0
	.word	62936
	.byte	4,2,35,4,0,14
	.word	63463
	.byte	27
	.byte	'Ifx_CPU_TR',0,19,153,10,3
	.word	63508
	.byte	18,176,32
	.word	675
	.byte	19,175,32,0,18,208,223,1
	.word	675
	.byte	19,207,223,1,0,18,248,1
	.word	675
	.byte	19,247,1,0,18,244,29
	.word	675
	.byte	19,243,29,0,18,188,3
	.word	675
	.byte	19,187,3,0,18,232,3
	.word	675
	.byte	19,231,3,0,18,252,23
	.word	675
	.byte	19,251,23,0,18,228,63
	.word	675
	.byte	19,227,63,0,18,128,1
	.word	63198
	.byte	19,15,0,14
	.word	63623
	.byte	18,128,31
	.word	675
	.byte	19,255,30,0,18,64
	.word	63130
	.byte	19,7,0,14
	.word	63649
	.byte	18,192,31
	.word	675
	.byte	19,191,31,0,18,16
	.word	59144
	.byte	19,3,0,18,16
	.word	60144
	.byte	19,3,0,18,16
	.word	60206
	.byte	19,3,0,18,208,7
	.word	675
	.byte	19,207,7,0,14
	.word	63389
	.byte	18,240,23
	.word	675
	.byte	19,239,23,0,18,64
	.word	63463
	.byte	19,7,0,14
	.word	63728
	.byte	18,192,23
	.word	675
	.byte	19,191,23,0,18,232,1
	.word	675
	.byte	19,231,1,0,18,180,1
	.word	675
	.byte	19,179,1,0,18,172,1
	.word	675
	.byte	19,171,1,0,18,64
	.word	59333
	.byte	19,15,0,18,64
	.word	675
	.byte	19,63,0,18,64
	.word	58519
	.byte	19,15,0,10
	.byte	'_Ifx_CPU',0,19,166,10,25,128,128,4,13
	.byte	'reserved_0',0
	.word	63533
	.byte	176,32,2,35,0,13
	.byte	'SEGEN',0
	.word	62056
	.byte	4,3,35,176,32,13
	.byte	'reserved_1034',0
	.word	63544
	.byte	208,223,1,3,35,180,32,13
	.byte	'TASK_ASI',0
	.word	62738
	.byte	4,4,35,132,128,2,13
	.byte	'reserved_8008',0
	.word	63557
	.byte	248,1,4,35,136,128,2,13
	.byte	'PMA0',0
	.word	61747
	.byte	4,4,35,128,130,2,13
	.byte	'PMA1',0
	.word	61809
	.byte	4,4,35,132,130,2,13
	.byte	'PMA2',0
	.word	61871
	.byte	4,4,35,136,130,2,13
	.byte	'reserved_810C',0
	.word	63568
	.byte	244,29,4,35,140,130,2,13
	.byte	'DCON2',0
	.word	59644
	.byte	4,4,35,128,160,2,13
	.byte	'reserved_9004',0
	.word	4698
	.byte	8,4,35,132,160,2,13
	.byte	'SMACON',0
	.word	62119
	.byte	4,4,35,140,160,2,13
	.byte	'DSTR',0
	.word	60268
	.byte	4,4,35,144,160,2,13
	.byte	'reserved_9014',0
	.word	2879
	.byte	4,4,35,148,160,2,13
	.byte	'DATR',0
	.word	59392
	.byte	4,4,35,152,160,2,13
	.byte	'DEADD',0
	.word	59768
	.byte	4,4,35,156,160,2,13
	.byte	'DIEAR',0
	.word	59831
	.byte	4,4,35,160,160,2,13
	.byte	'DIETR',0
	.word	59894
	.byte	4,4,35,164,160,2,13
	.byte	'reserved_9028',0
	.word	4069
	.byte	24,4,35,168,160,2,13
	.byte	'DCON0',0
	.word	59581
	.byte	4,4,35,192,160,2,13
	.byte	'reserved_9044',0
	.word	63579
	.byte	188,3,4,35,196,160,2,13
	.byte	'PSTR',0
	.word	61933
	.byte	4,4,35,128,164,2,13
	.byte	'PCON1',0
	.word	61433
	.byte	4,4,35,132,164,2,13
	.byte	'PCON2',0
	.word	61496
	.byte	4,4,35,136,164,2,13
	.byte	'PCON0',0
	.word	61370
	.byte	4,4,35,140,164,2,13
	.byte	'PIEAR',0
	.word	61621
	.byte	4,4,35,144,164,2,13
	.byte	'PIETR',0
	.word	61684
	.byte	4,4,35,148,164,2,13
	.byte	'reserved_9218',0
	.word	63590
	.byte	232,3,4,35,152,164,2,13
	.byte	'COMPAT',0
	.word	58825
	.byte	4,4,35,128,168,2,13
	.byte	'reserved_9404',0
	.word	63601
	.byte	252,23,4,35,132,168,2,13
	.byte	'FPU_TRAP_CON',0
	.word	60454
	.byte	4,4,35,128,192,2,13
	.byte	'FPU_TRAP_PC',0
	.word	60594
	.byte	4,4,35,132,192,2,13
	.byte	'FPU_TRAP_OPC',0
	.word	60524
	.byte	4,4,35,136,192,2,13
	.byte	'reserved_A00C',0
	.word	2879
	.byte	4,4,35,140,192,2,13
	.byte	'FPU_TRAP_SRC1',0
	.word	60663
	.byte	4,4,35,144,192,2,13
	.byte	'FPU_TRAP_SRC2',0
	.word	60734
	.byte	4,4,35,148,192,2,13
	.byte	'FPU_TRAP_SRC3',0
	.word	60805
	.byte	4,4,35,152,192,2,13
	.byte	'reserved_A01C',0
	.word	63612
	.byte	228,63,4,35,156,192,2,13
	.byte	'DPR',0
	.word	63633
	.byte	128,1,4,35,128,128,3,13
	.byte	'reserved_C080',0
	.word	63638
	.byte	128,31,4,35,128,129,3,13
	.byte	'CPR',0
	.word	63658
	.byte	64,4,35,128,160,3,13
	.byte	'reserved_D040',0
	.word	63663
	.byte	192,31,4,35,192,160,3,13
	.byte	'CPXE',0
	.word	63674
	.byte	16,4,35,128,192,3,13
	.byte	'DPRE',0
	.word	63683
	.byte	16,4,35,144,192,3,13
	.byte	'DPWE',0
	.word	63692
	.byte	16,4,35,160,192,3,13
	.byte	'reserved_E030',0
	.word	63701
	.byte	208,7,4,35,176,192,3,13
	.byte	'TPS',0
	.word	63712
	.byte	16,4,35,128,200,3,13
	.byte	'reserved_E410',0
	.word	63717
	.byte	240,23,4,35,144,200,3,13
	.byte	'TR',0
	.word	63737
	.byte	64,4,35,128,224,3,13
	.byte	'reserved_F040',0
	.word	63742
	.byte	192,23,4,35,192,224,3,13
	.byte	'CCTRL',0
	.word	58762
	.byte	4,4,35,128,248,3,13
	.byte	'CCNT',0
	.word	58700
	.byte	4,4,35,132,248,3,13
	.byte	'ICNT',0
	.word	60876
	.byte	4,4,35,136,248,3,13
	.byte	'M1CNT',0
	.word	61121
	.byte	4,4,35,140,248,3,13
	.byte	'M2CNT',0
	.word	61184
	.byte	4,4,35,144,248,3,13
	.byte	'M3CNT',0
	.word	61247
	.byte	4,4,35,148,248,3,13
	.byte	'reserved_FC18',0
	.word	63753
	.byte	232,1,4,35,152,248,3,13
	.byte	'DBGSR',0
	.word	59454
	.byte	4,4,35,128,250,3,13
	.byte	'reserved_FD04',0
	.word	2879
	.byte	4,4,35,132,250,3,13
	.byte	'EXEVT',0
	.word	60330
	.byte	4,4,35,136,250,3,13
	.byte	'CREVT',0
	.word	59206
	.byte	4,4,35,140,250,3,13
	.byte	'SWEVT',0
	.word	62611
	.byte	4,4,35,144,250,3,13
	.byte	'reserved_FD14',0
	.word	47483
	.byte	28,4,35,148,250,3,13
	.byte	'TRIG_ACC',0
	.word	63064
	.byte	4,4,35,176,250,3,13
	.byte	'reserved_FD34',0
	.word	5038
	.byte	12,4,35,180,250,3,13
	.byte	'DMS',0
	.word	59957
	.byte	4,4,35,192,250,3,13
	.byte	'DCX',0
	.word	59707
	.byte	4,4,35,196,250,3,13
	.byte	'DBGTCR',0
	.word	59517
	.byte	4,4,35,200,250,3,13
	.byte	'reserved_FD4C',0
	.word	63764
	.byte	180,1,4,35,204,250,3,13
	.byte	'PCXI',0
	.word	61559
	.byte	4,4,35,128,252,3,13
	.byte	'PSW',0
	.word	61995
	.byte	4,4,35,132,252,3,13
	.byte	'PC',0
	.word	61310
	.byte	4,4,35,136,252,3,13
	.byte	'reserved_FE0C',0
	.word	4698
	.byte	8,4,35,140,252,3,13
	.byte	'SYSCON',0
	.word	62674
	.byte	4,4,35,148,252,3,13
	.byte	'CPU_ID',0
	.word	59080
	.byte	4,4,35,152,252,3,13
	.byte	'CORE_ID',0
	.word	58889
	.byte	4,4,35,156,252,3,13
	.byte	'BIV',0
	.word	58578
	.byte	4,4,35,160,252,3,13
	.byte	'BTV',0
	.word	58639
	.byte	4,4,35,164,252,3,13
	.byte	'ISP',0
	.word	60999
	.byte	4,4,35,168,252,3,13
	.byte	'ICR',0
	.word	60938
	.byte	4,4,35,172,252,3,13
	.byte	'reserved_FE30',0
	.word	4698
	.byte	8,4,35,176,252,3,13
	.byte	'FCX',0
	.word	60393
	.byte	4,4,35,184,252,3,13
	.byte	'LCX',0
	.word	61060
	.byte	4,4,35,188,252,3,13
	.byte	'reserved_FE40',0
	.word	47474
	.byte	16,4,35,192,252,3,13
	.byte	'CUS_ID',0
	.word	59269
	.byte	4,4,35,208,252,3,13
	.byte	'reserved_FE54',0
	.word	63775
	.byte	172,1,4,35,212,252,3,13
	.byte	'D',0
	.word	63786
	.byte	64,4,35,128,254,3,13
	.byte	'reserved_FF40',0
	.word	63795
	.byte	64,4,35,192,254,3,13
	.byte	'A',0
	.word	63804
	.byte	64,4,35,128,255,3,13
	.byte	'reserved_FFC0',0
	.word	63795
	.byte	64,4,35,192,255,3,0,14
	.word	63813
	.byte	27
	.byte	'Ifx_CPU',0,19,130,11,3
	.word	65604
	.byte	15,6,127,9,1,16
	.byte	'IfxCpu_Id_0',0,0,16
	.byte	'IfxCpu_Id_1',0,1,16
	.byte	'IfxCpu_Id_none',0,2,0,27
	.byte	'IfxCpu_Id',0,6,132,1,3
	.word	65626
	.byte	27
	.byte	'IfxCpu_ResourceCpu',0,6,161,1,3
	.word	1487
	.byte	27
	.byte	'Ifx_SRC_SRCR_Bits',0,10,62,3
	.word	10400
	.byte	27
	.byte	'Ifx_SRC_SRCR',0,10,75,3
	.word	10690
	.byte	10
	.byte	'_Ifx_SRC_AGBT',0,10,86,25,4,13
	.byte	'SR',0
	.word	10690
	.byte	4,2,35,0,0,14
	.word	65771
	.byte	27
	.byte	'Ifx_SRC_AGBT',0,10,89,3
	.word	65803
	.byte	10
	.byte	'_Ifx_SRC_ASCLIN',0,10,92,25,12,13
	.byte	'TX',0
	.word	10690
	.byte	4,2,35,0,13
	.byte	'RX',0
	.word	10690
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	10690
	.byte	4,2,35,8,0,14
	.word	65829
	.byte	27
	.byte	'Ifx_SRC_ASCLIN',0,10,97,3
	.word	65888
	.byte	10
	.byte	'_Ifx_SRC_BCUSPB',0,10,100,25,4,13
	.byte	'SBSRC',0
	.word	10690
	.byte	4,2,35,0,0,14
	.word	65916
	.byte	27
	.byte	'Ifx_SRC_BCUSPB',0,10,103,3
	.word	65953
	.byte	18,64
	.word	10690
	.byte	19,15,0,10
	.byte	'_Ifx_SRC_CAN',0,10,106,25,64,13
	.byte	'INT',0
	.word	65981
	.byte	64,2,35,0,0,14
	.word	65990
	.byte	27
	.byte	'Ifx_SRC_CAN',0,10,109,3
	.word	66022
	.byte	10
	.byte	'_Ifx_SRC_CCU6',0,10,112,25,16,13
	.byte	'SR0',0
	.word	10690
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10690
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	10690
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	10690
	.byte	4,2,35,12,0,14
	.word	66047
	.byte	27
	.byte	'Ifx_SRC_CCU6',0,10,118,3
	.word	66119
	.byte	18,8
	.word	10690
	.byte	19,1,0,10
	.byte	'_Ifx_SRC_CERBERUS',0,10,121,25,8,13
	.byte	'SR',0
	.word	66145
	.byte	8,2,35,0,0,14
	.word	66154
	.byte	27
	.byte	'Ifx_SRC_CERBERUS',0,10,124,3
	.word	66190
	.byte	10
	.byte	'_Ifx_SRC_CIF',0,10,127,25,16,13
	.byte	'MI',0
	.word	10690
	.byte	4,2,35,0,13
	.byte	'MIEP',0
	.word	10690
	.byte	4,2,35,4,13
	.byte	'ISP',0
	.word	10690
	.byte	4,2,35,8,13
	.byte	'MJPEG',0
	.word	10690
	.byte	4,2,35,12,0,14
	.word	66220
	.byte	27
	.byte	'Ifx_SRC_CIF',0,10,133,1,3
	.word	66293
	.byte	10
	.byte	'_Ifx_SRC_CPU',0,10,136,1,25,4,13
	.byte	'SBSRC',0
	.word	10690
	.byte	4,2,35,0,0,14
	.word	66319
	.byte	27
	.byte	'Ifx_SRC_CPU',0,10,139,1,3
	.word	66354
	.byte	18,192,1
	.word	10690
	.byte	19,47,0,10
	.byte	'_Ifx_SRC_DMA',0,10,142,1,25,208,1,13
	.byte	'ERR',0
	.word	10690
	.byte	4,2,35,0,13
	.byte	'reserved_4',0
	.word	5038
	.byte	12,2,35,4,13
	.byte	'CH',0
	.word	66380
	.byte	192,1,2,35,16,0,14
	.word	66390
	.byte	27
	.byte	'Ifx_SRC_DMA',0,10,147,1,3
	.word	66457
	.byte	10
	.byte	'_Ifx_SRC_DSADC',0,10,150,1,25,8,13
	.byte	'SRM',0
	.word	10690
	.byte	4,2,35,0,13
	.byte	'SRA',0
	.word	10690
	.byte	4,2,35,4,0,14
	.word	66483
	.byte	27
	.byte	'Ifx_SRC_DSADC',0,10,154,1,3
	.word	66531
	.byte	10
	.byte	'_Ifx_SRC_EMEM',0,10,157,1,25,4,13
	.byte	'SR',0
	.word	10690
	.byte	4,2,35,0,0,14
	.word	66559
	.byte	27
	.byte	'Ifx_SRC_EMEM',0,10,160,1,3
	.word	66592
	.byte	10
	.byte	'_Ifx_SRC_ERAY',0,10,163,1,25,80,13
	.byte	'INT',0
	.word	66145
	.byte	8,2,35,0,13
	.byte	'TINT',0
	.word	66145
	.byte	8,2,35,8,13
	.byte	'NDAT',0
	.word	66145
	.byte	8,2,35,16,13
	.byte	'MBSC',0
	.word	66145
	.byte	8,2,35,24,13
	.byte	'OBUSY',0
	.word	10690
	.byte	4,2,35,32,13
	.byte	'IBUSY',0
	.word	10690
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	47492
	.byte	40,2,35,40,0,14
	.word	66619
	.byte	27
	.byte	'Ifx_SRC_ERAY',0,10,172,1,3
	.word	66746
	.byte	10
	.byte	'_Ifx_SRC_ETH',0,10,175,1,25,4,13
	.byte	'SR',0
	.word	10690
	.byte	4,2,35,0,0,14
	.word	66773
	.byte	27
	.byte	'Ifx_SRC_ETH',0,10,178,1,3
	.word	66805
	.byte	10
	.byte	'_Ifx_SRC_FCE',0,10,181,1,25,4,13
	.byte	'SR',0
	.word	10690
	.byte	4,2,35,0,0,14
	.word	66831
	.byte	27
	.byte	'Ifx_SRC_FCE',0,10,184,1,3
	.word	66863
	.byte	10
	.byte	'_Ifx_SRC_FFT',0,10,187,1,25,12,13
	.byte	'DONE',0
	.word	10690
	.byte	4,2,35,0,13
	.byte	'ERR',0
	.word	10690
	.byte	4,2,35,4,13
	.byte	'RFS',0
	.word	10690
	.byte	4,2,35,8,0,14
	.word	66889
	.byte	27
	.byte	'Ifx_SRC_FFT',0,10,192,1,3
	.word	66949
	.byte	10
	.byte	'_Ifx_SRC_GPSR',0,10,195,1,25,32,13
	.byte	'SR0',0
	.word	10690
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10690
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	10690
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	10690
	.byte	4,2,35,12,13
	.byte	'reserved_10',0
	.word	47474
	.byte	16,2,35,16,0,14
	.word	66975
	.byte	27
	.byte	'Ifx_SRC_GPSR',0,10,202,1,3
	.word	67069
	.byte	10
	.byte	'_Ifx_SRC_GPT12',0,10,205,1,25,48,13
	.byte	'CIRQ',0
	.word	10690
	.byte	4,2,35,0,13
	.byte	'T2',0
	.word	10690
	.byte	4,2,35,4,13
	.byte	'T3',0
	.word	10690
	.byte	4,2,35,8,13
	.byte	'T4',0
	.word	10690
	.byte	4,2,35,12,13
	.byte	'T5',0
	.word	10690
	.byte	4,2,35,16,13
	.byte	'T6',0
	.word	10690
	.byte	4,2,35,20,13
	.byte	'reserved_18',0
	.word	4069
	.byte	24,2,35,24,0,14
	.word	67096
	.byte	27
	.byte	'Ifx_SRC_GPT12',0,10,214,1,3
	.word	67213
	.byte	18,12
	.word	10690
	.byte	19,2,0,18,32
	.word	10690
	.byte	19,7,0,18,32
	.word	67250
	.byte	19,0,0,18,88
	.word	675
	.byte	19,87,0,18,108
	.word	10690
	.byte	19,26,0,18,96
	.word	675
	.byte	19,95,0,18,96
	.word	67250
	.byte	19,2,0,18,160,3
	.word	675
	.byte	19,159,3,0,18,64
	.word	67250
	.byte	19,1,0,18,192,3
	.word	675
	.byte	19,191,3,0,18,16
	.word	10690
	.byte	19,3,0,18,64
	.word	67335
	.byte	19,3,0,18,192,2
	.word	675
	.byte	19,191,2,0,18,52
	.word	675
	.byte	19,51,0,10
	.byte	'_Ifx_SRC_GTM',0,10,217,1,25,204,18,13
	.byte	'AEIIRQ',0
	.word	10690
	.byte	4,2,35,0,13
	.byte	'ARUIRQ',0
	.word	67241
	.byte	12,2,35,4,13
	.byte	'reserved_10',0
	.word	2879
	.byte	4,2,35,16,13
	.byte	'BRCIRQ',0
	.word	10690
	.byte	4,2,35,20,13
	.byte	'CMPIRQ',0
	.word	10690
	.byte	4,2,35,24,13
	.byte	'SPEIRQ',0
	.word	66145
	.byte	8,2,35,28,13
	.byte	'reserved_24',0
	.word	4698
	.byte	8,2,35,36,13
	.byte	'PSM',0
	.word	67259
	.byte	32,2,35,44,13
	.byte	'reserved_4C',0
	.word	67268
	.byte	88,2,35,76,13
	.byte	'DPLL',0
	.word	67277
	.byte	108,3,35,164,1,13
	.byte	'reserved_110',0
	.word	67286
	.byte	96,3,35,144,2,13
	.byte	'ERR',0
	.word	10690
	.byte	4,3,35,240,2,13
	.byte	'reserved_174',0
	.word	5038
	.byte	12,3,35,244,2,13
	.byte	'TIM',0
	.word	67295
	.byte	96,3,35,128,3,13
	.byte	'reserved_1E0',0
	.word	67304
	.byte	160,3,3,35,224,3,13
	.byte	'MCS',0
	.word	67295
	.byte	96,3,35,128,7,13
	.byte	'reserved_3E0',0
	.word	67304
	.byte	160,3,3,35,224,7,13
	.byte	'TOM',0
	.word	67315
	.byte	64,3,35,128,11,13
	.byte	'reserved_5C0',0
	.word	67324
	.byte	192,3,3,35,192,11,13
	.byte	'ATOM',0
	.word	67344
	.byte	64,3,35,128,15,13
	.byte	'reserved_7C0',0
	.word	67353
	.byte	192,2,3,35,192,15,13
	.byte	'MCSW0',0
	.word	67241
	.byte	12,3,35,128,18,13
	.byte	'reserved_90C',0
	.word	67364
	.byte	52,3,35,140,18,13
	.byte	'MCSW1',0
	.word	67241
	.byte	12,3,35,192,18,0,14
	.word	67373
	.byte	27
	.byte	'Ifx_SRC_GTM',0,10,243,1,3
	.word	67833
	.byte	10
	.byte	'_Ifx_SRC_HSCT',0,10,246,1,25,4,13
	.byte	'SR',0
	.word	10690
	.byte	4,2,35,0,0,14
	.word	67859
	.byte	27
	.byte	'Ifx_SRC_HSCT',0,10,249,1,3
	.word	67892
	.byte	10
	.byte	'_Ifx_SRC_HSSL',0,10,252,1,25,16,13
	.byte	'COK',0
	.word	10690
	.byte	4,2,35,0,13
	.byte	'RDI',0
	.word	10690
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	10690
	.byte	4,2,35,8,13
	.byte	'TRG',0
	.word	10690
	.byte	4,2,35,12,0,14
	.word	67919
	.byte	27
	.byte	'Ifx_SRC_HSSL',0,10,130,2,3
	.word	67992
	.byte	18,56
	.word	675
	.byte	19,55,0,10
	.byte	'_Ifx_SRC_I2C',0,10,133,2,25,80,13
	.byte	'BREQ',0
	.word	10690
	.byte	4,2,35,0,13
	.byte	'LBREQ',0
	.word	10690
	.byte	4,2,35,4,13
	.byte	'SREQ',0
	.word	10690
	.byte	4,2,35,8,13
	.byte	'LSREQ',0
	.word	10690
	.byte	4,2,35,12,13
	.byte	'ERR',0
	.word	10690
	.byte	4,2,35,16,13
	.byte	'P',0
	.word	10690
	.byte	4,2,35,20,13
	.byte	'reserved_18',0
	.word	68019
	.byte	56,2,35,24,0,14
	.word	68028
	.byte	27
	.byte	'Ifx_SRC_I2C',0,10,142,2,3
	.word	68151
	.byte	10
	.byte	'_Ifx_SRC_LMU',0,10,145,2,25,4,13
	.byte	'SR',0
	.word	10690
	.byte	4,2,35,0,0,14
	.word	68177
	.byte	27
	.byte	'Ifx_SRC_LMU',0,10,148,2,3
	.word	68209
	.byte	10
	.byte	'_Ifx_SRC_MSC',0,10,151,2,25,20,13
	.byte	'SR0',0
	.word	10690
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10690
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	10690
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	10690
	.byte	4,2,35,12,13
	.byte	'SR4',0
	.word	10690
	.byte	4,2,35,16,0,14
	.word	68235
	.byte	27
	.byte	'Ifx_SRC_MSC',0,10,158,2,3
	.word	68320
	.byte	10
	.byte	'_Ifx_SRC_PMU',0,10,161,2,25,4,13
	.byte	'SR',0
	.word	10690
	.byte	4,2,35,0,0,14
	.word	68346
	.byte	27
	.byte	'Ifx_SRC_PMU',0,10,164,2,3
	.word	68378
	.byte	10
	.byte	'_Ifx_SRC_PSI5',0,10,167,2,25,32,13
	.byte	'SR',0
	.word	67250
	.byte	32,2,35,0,0,14
	.word	68404
	.byte	27
	.byte	'Ifx_SRC_PSI5',0,10,170,2,3
	.word	68437
	.byte	10
	.byte	'_Ifx_SRC_PSI5S',0,10,173,2,25,32,13
	.byte	'SR',0
	.word	67250
	.byte	32,2,35,0,0,14
	.word	68464
	.byte	27
	.byte	'Ifx_SRC_PSI5S',0,10,176,2,3
	.word	68498
	.byte	10
	.byte	'_Ifx_SRC_QSPI',0,10,179,2,25,24,13
	.byte	'TX',0
	.word	10690
	.byte	4,2,35,0,13
	.byte	'RX',0
	.word	10690
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	10690
	.byte	4,2,35,8,13
	.byte	'PT',0
	.word	10690
	.byte	4,2,35,12,13
	.byte	'HC',0
	.word	10690
	.byte	4,2,35,16,13
	.byte	'U',0
	.word	10690
	.byte	4,2,35,20,0,14
	.word	68526
	.byte	27
	.byte	'Ifx_SRC_QSPI',0,10,187,2,3
	.word	68619
	.byte	10
	.byte	'_Ifx_SRC_SCR',0,10,190,2,25,4,13
	.byte	'SR',0
	.word	10690
	.byte	4,2,35,0,0,14
	.word	68646
	.byte	27
	.byte	'Ifx_SRC_SCR',0,10,193,2,3
	.word	68678
	.byte	10
	.byte	'_Ifx_SRC_SCU',0,10,196,2,25,20,13
	.byte	'DTS',0
	.word	10690
	.byte	4,2,35,0,13
	.byte	'ERU',0
	.word	67335
	.byte	16,2,35,4,0,14
	.word	68704
	.byte	27
	.byte	'Ifx_SRC_SCU',0,10,200,2,3
	.word	68750
	.byte	18,24
	.word	10690
	.byte	19,5,0,10
	.byte	'_Ifx_SRC_SENT',0,10,203,2,25,24,13
	.byte	'SR',0
	.word	68776
	.byte	24,2,35,0,0,14
	.word	68785
	.byte	27
	.byte	'Ifx_SRC_SENT',0,10,206,2,3
	.word	68818
	.byte	10
	.byte	'_Ifx_SRC_SMU',0,10,209,2,25,12,13
	.byte	'SR',0
	.word	67241
	.byte	12,2,35,0,0,14
	.word	68845
	.byte	27
	.byte	'Ifx_SRC_SMU',0,10,212,2,3
	.word	68877
	.byte	10
	.byte	'_Ifx_SRC_STM',0,10,215,2,25,8,13
	.byte	'SR0',0
	.word	10690
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10690
	.byte	4,2,35,4,0,14
	.word	68903
	.byte	27
	.byte	'Ifx_SRC_STM',0,10,219,2,3
	.word	68949
	.byte	10
	.byte	'_Ifx_SRC_VADCCG',0,10,222,2,25,16,13
	.byte	'SR0',0
	.word	10690
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10690
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	10690
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	10690
	.byte	4,2,35,12,0,14
	.word	68975
	.byte	27
	.byte	'Ifx_SRC_VADCCG',0,10,228,2,3
	.word	69050
	.byte	10
	.byte	'_Ifx_SRC_VADCG',0,10,231,2,25,16,13
	.byte	'SR0',0
	.word	10690
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10690
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	10690
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	10690
	.byte	4,2,35,12,0,14
	.word	69079
	.byte	27
	.byte	'Ifx_SRC_VADCG',0,10,237,2,3
	.word	69153
	.byte	10
	.byte	'_Ifx_SRC_XBAR',0,10,240,2,25,4,13
	.byte	'SRC',0
	.word	10690
	.byte	4,2,35,0,0,14
	.word	69181
	.byte	27
	.byte	'Ifx_SRC_XBAR',0,10,243,2,3
	.word	69215
	.byte	18,4
	.word	65771
	.byte	19,0,0,14
	.word	69242
	.byte	10
	.byte	'_Ifx_SRC_GAGBT',0,10,128,3,25,4,13
	.byte	'AGBT',0
	.word	69251
	.byte	4,2,35,0,0,14
	.word	69256
	.byte	27
	.byte	'Ifx_SRC_GAGBT',0,10,131,3,3
	.word	69292
	.byte	18,48
	.word	65829
	.byte	19,3,0,14
	.word	69320
	.byte	10
	.byte	'_Ifx_SRC_GASCLIN',0,10,134,3,25,48,13
	.byte	'ASCLIN',0
	.word	69329
	.byte	48,2,35,0,0,14
	.word	69334
	.byte	27
	.byte	'Ifx_SRC_GASCLIN',0,10,137,3,3
	.word	69374
	.byte	14
	.word	65916
	.byte	10
	.byte	'_Ifx_SRC_GBCU',0,10,140,3,25,4,13
	.byte	'SPB',0
	.word	69404
	.byte	4,2,35,0,0,14
	.word	69409
	.byte	27
	.byte	'Ifx_SRC_GBCU',0,10,143,3,3
	.word	69443
	.byte	18,64
	.word	65990
	.byte	19,0,0,14
	.word	69470
	.byte	10
	.byte	'_Ifx_SRC_GCAN',0,10,146,3,25,64,13
	.byte	'CAN',0
	.word	69479
	.byte	64,2,35,0,0,14
	.word	69484
	.byte	27
	.byte	'Ifx_SRC_GCAN',0,10,149,3,3
	.word	69518
	.byte	18,32
	.word	66047
	.byte	19,1,0,14
	.word	69545
	.byte	10
	.byte	'_Ifx_SRC_GCCU6',0,10,152,3,25,32,13
	.byte	'CCU6',0
	.word	69554
	.byte	32,2,35,0,0,14
	.word	69559
	.byte	27
	.byte	'Ifx_SRC_GCCU6',0,10,155,3,3
	.word	69595
	.byte	14
	.word	66154
	.byte	10
	.byte	'_Ifx_SRC_GCERBERUS',0,10,158,3,25,8,13
	.byte	'CERBERUS',0
	.word	69623
	.byte	8,2,35,0,0,14
	.word	69628
	.byte	27
	.byte	'Ifx_SRC_GCERBERUS',0,10,161,3,3
	.word	69672
	.byte	18,16
	.word	66220
	.byte	19,0,0,14
	.word	69704
	.byte	10
	.byte	'_Ifx_SRC_GCIF',0,10,164,3,25,16,13
	.byte	'CIF',0
	.word	69713
	.byte	16,2,35,0,0,14
	.word	69718
	.byte	27
	.byte	'Ifx_SRC_GCIF',0,10,167,3,3
	.word	69752
	.byte	18,8
	.word	66319
	.byte	19,1,0,14
	.word	69779
	.byte	10
	.byte	'_Ifx_SRC_GCPU',0,10,170,3,25,8,13
	.byte	'CPU',0
	.word	69788
	.byte	8,2,35,0,0,14
	.word	69793
	.byte	27
	.byte	'Ifx_SRC_GCPU',0,10,173,3,3
	.word	69827
	.byte	18,208,1
	.word	66390
	.byte	19,0,0,14
	.word	69854
	.byte	10
	.byte	'_Ifx_SRC_GDMA',0,10,176,3,25,208,1,13
	.byte	'DMA',0
	.word	69864
	.byte	208,1,2,35,0,0,14
	.word	69869
	.byte	27
	.byte	'Ifx_SRC_GDMA',0,10,179,3,3
	.word	69905
	.byte	14
	.word	66483
	.byte	14
	.word	66483
	.byte	14
	.word	66483
	.byte	10
	.byte	'_Ifx_SRC_GDSADC',0,10,182,3,25,32,13
	.byte	'DSADC0',0
	.word	69932
	.byte	8,2,35,0,13
	.byte	'reserved_8',0
	.word	4698
	.byte	8,2,35,8,13
	.byte	'DSADC2',0
	.word	69937
	.byte	8,2,35,16,13
	.byte	'DSADC3',0
	.word	69942
	.byte	8,2,35,24,0,14
	.word	69947
	.byte	27
	.byte	'Ifx_SRC_GDSADC',0,10,188,3,3
	.word	70038
	.byte	18,4
	.word	66559
	.byte	19,0,0,14
	.word	70067
	.byte	10
	.byte	'_Ifx_SRC_GEMEM',0,10,191,3,25,4,13
	.byte	'EMEM',0
	.word	70076
	.byte	4,2,35,0,0,14
	.word	70081
	.byte	27
	.byte	'Ifx_SRC_GEMEM',0,10,194,3,3
	.word	70117
	.byte	18,80
	.word	66619
	.byte	19,0,0,14
	.word	70145
	.byte	10
	.byte	'_Ifx_SRC_GERAY',0,10,197,3,25,80,13
	.byte	'ERAY',0
	.word	70154
	.byte	80,2,35,0,0,14
	.word	70159
	.byte	27
	.byte	'Ifx_SRC_GERAY',0,10,200,3,3
	.word	70195
	.byte	18,4
	.word	66773
	.byte	19,0,0,14
	.word	70223
	.byte	10
	.byte	'_Ifx_SRC_GETH',0,10,203,3,25,4,13
	.byte	'ETH',0
	.word	70232
	.byte	4,2,35,0,0,14
	.word	70237
	.byte	27
	.byte	'Ifx_SRC_GETH',0,10,206,3,3
	.word	70271
	.byte	18,4
	.word	66831
	.byte	19,0,0,14
	.word	70298
	.byte	10
	.byte	'_Ifx_SRC_GFCE',0,10,209,3,25,4,13
	.byte	'FCE',0
	.word	70307
	.byte	4,2,35,0,0,14
	.word	70312
	.byte	27
	.byte	'Ifx_SRC_GFCE',0,10,212,3,3
	.word	70346
	.byte	18,12
	.word	66889
	.byte	19,0,0,14
	.word	70373
	.byte	10
	.byte	'_Ifx_SRC_GFFT',0,10,215,3,25,12,13
	.byte	'FFT',0
	.word	70382
	.byte	12,2,35,0,0,14
	.word	70387
	.byte	27
	.byte	'Ifx_SRC_GFFT',0,10,218,3,3
	.word	70421
	.byte	18,64
	.word	66975
	.byte	19,1,0,14
	.word	70448
	.byte	10
	.byte	'_Ifx_SRC_GGPSR',0,10,221,3,25,64,13
	.byte	'GPSR',0
	.word	70457
	.byte	64,2,35,0,0,14
	.word	70462
	.byte	27
	.byte	'Ifx_SRC_GGPSR',0,10,224,3,3
	.word	70498
	.byte	18,48
	.word	67096
	.byte	19,0,0,14
	.word	70526
	.byte	10
	.byte	'_Ifx_SRC_GGPT12',0,10,227,3,25,48,13
	.byte	'GPT12',0
	.word	70535
	.byte	48,2,35,0,0,14
	.word	70540
	.byte	27
	.byte	'Ifx_SRC_GGPT12',0,10,230,3,3
	.word	70578
	.byte	18,204,18
	.word	67373
	.byte	19,0,0,14
	.word	70607
	.byte	10
	.byte	'_Ifx_SRC_GGTM',0,10,233,3,25,204,18,13
	.byte	'GTM',0
	.word	70617
	.byte	204,18,2,35,0,0,14
	.word	70622
	.byte	27
	.byte	'Ifx_SRC_GGTM',0,10,236,3,3
	.word	70658
	.byte	18,4
	.word	67859
	.byte	19,0,0,14
	.word	70685
	.byte	10
	.byte	'_Ifx_SRC_GHSCT',0,10,239,3,25,4,13
	.byte	'HSCT',0
	.word	70694
	.byte	4,2,35,0,0,14
	.word	70699
	.byte	27
	.byte	'Ifx_SRC_GHSCT',0,10,242,3,3
	.word	70735
	.byte	18,64
	.word	67919
	.byte	19,3,0,14
	.word	70763
	.byte	10
	.byte	'_Ifx_SRC_GHSSL',0,10,245,3,25,68,13
	.byte	'HSSL',0
	.word	70772
	.byte	64,2,35,0,13
	.byte	'EXI',0
	.word	10690
	.byte	4,2,35,64,0,14
	.word	70777
	.byte	27
	.byte	'Ifx_SRC_GHSSL',0,10,249,3,3
	.word	70826
	.byte	18,80
	.word	68028
	.byte	19,0,0,14
	.word	70854
	.byte	10
	.byte	'_Ifx_SRC_GI2C',0,10,252,3,25,80,13
	.byte	'I2C',0
	.word	70863
	.byte	80,2,35,0,0,14
	.word	70868
	.byte	27
	.byte	'Ifx_SRC_GI2C',0,10,255,3,3
	.word	70902
	.byte	18,4
	.word	68177
	.byte	19,0,0,14
	.word	70929
	.byte	10
	.byte	'_Ifx_SRC_GLMU',0,10,130,4,25,4,13
	.byte	'LMU',0
	.word	70938
	.byte	4,2,35,0,0,14
	.word	70943
	.byte	27
	.byte	'Ifx_SRC_GLMU',0,10,133,4,3
	.word	70977
	.byte	18,40
	.word	68235
	.byte	19,1,0,14
	.word	71004
	.byte	10
	.byte	'_Ifx_SRC_GMSC',0,10,136,4,25,40,13
	.byte	'MSC',0
	.word	71013
	.byte	40,2,35,0,0,14
	.word	71018
	.byte	27
	.byte	'Ifx_SRC_GMSC',0,10,139,4,3
	.word	71052
	.byte	18,8
	.word	68346
	.byte	19,1,0,14
	.word	71079
	.byte	10
	.byte	'_Ifx_SRC_GPMU',0,10,142,4,25,8,13
	.byte	'PMU',0
	.word	71088
	.byte	8,2,35,0,0,14
	.word	71093
	.byte	27
	.byte	'Ifx_SRC_GPMU',0,10,145,4,3
	.word	71127
	.byte	18,32
	.word	68404
	.byte	19,0,0,14
	.word	71154
	.byte	10
	.byte	'_Ifx_SRC_GPSI5',0,10,148,4,25,32,13
	.byte	'PSI5',0
	.word	71163
	.byte	32,2,35,0,0,14
	.word	71168
	.byte	27
	.byte	'Ifx_SRC_GPSI5',0,10,151,4,3
	.word	71204
	.byte	18,32
	.word	68464
	.byte	19,0,0,14
	.word	71232
	.byte	10
	.byte	'_Ifx_SRC_GPSI5S',0,10,154,4,25,32,13
	.byte	'PSI5S',0
	.word	71241
	.byte	32,2,35,0,0,14
	.word	71246
	.byte	27
	.byte	'Ifx_SRC_GPSI5S',0,10,157,4,3
	.word	71284
	.byte	18,96
	.word	68526
	.byte	19,3,0,14
	.word	71313
	.byte	10
	.byte	'_Ifx_SRC_GQSPI',0,10,160,4,25,96,13
	.byte	'QSPI',0
	.word	71322
	.byte	96,2,35,0,0,14
	.word	71327
	.byte	27
	.byte	'Ifx_SRC_GQSPI',0,10,163,4,3
	.word	71363
	.byte	18,4
	.word	68646
	.byte	19,0,0,14
	.word	71391
	.byte	10
	.byte	'_Ifx_SRC_GSCR',0,10,166,4,25,4,13
	.byte	'SCR',0
	.word	71400
	.byte	4,2,35,0,0,14
	.word	71405
	.byte	27
	.byte	'Ifx_SRC_GSCR',0,10,169,4,3
	.word	71439
	.byte	14
	.word	68704
	.byte	10
	.byte	'_Ifx_SRC_GSCU',0,10,172,4,25,20,13
	.byte	'SCU',0
	.word	71466
	.byte	20,2,35,0,0,14
	.word	71471
	.byte	27
	.byte	'Ifx_SRC_GSCU',0,10,175,4,3
	.word	71505
	.byte	18,24
	.word	68785
	.byte	19,0,0,14
	.word	71532
	.byte	10
	.byte	'_Ifx_SRC_GSENT',0,10,178,4,25,24,13
	.byte	'SENT',0
	.word	71541
	.byte	24,2,35,0,0,14
	.word	71546
	.byte	27
	.byte	'Ifx_SRC_GSENT',0,10,181,4,3
	.word	71582
	.byte	18,12
	.word	68845
	.byte	19,0,0,14
	.word	71610
	.byte	10
	.byte	'_Ifx_SRC_GSMU',0,10,184,4,25,12,13
	.byte	'SMU',0
	.word	71619
	.byte	12,2,35,0,0,14
	.word	71624
	.byte	27
	.byte	'Ifx_SRC_GSMU',0,10,187,4,3
	.word	71658
	.byte	18,16
	.word	68903
	.byte	19,1,0,14
	.word	71685
	.byte	10
	.byte	'_Ifx_SRC_GSTM',0,10,190,4,25,16,13
	.byte	'STM',0
	.word	71694
	.byte	16,2,35,0,0,14
	.word	71699
	.byte	27
	.byte	'Ifx_SRC_GSTM',0,10,193,4,3
	.word	71733
	.byte	18,64
	.word	69079
	.byte	19,3,0,14
	.word	71760
	.byte	18,224,1
	.word	675
	.byte	19,223,1,0,18,32
	.word	68975
	.byte	19,1,0,14
	.word	71785
	.byte	10
	.byte	'_Ifx_SRC_GVADC',0,10,196,4,25,192,2,13
	.byte	'G',0
	.word	71769
	.byte	64,2,35,0,13
	.byte	'reserved_40',0
	.word	71774
	.byte	224,1,2,35,64,13
	.byte	'CG',0
	.word	71794
	.byte	32,3,35,160,2,0,14
	.word	71799
	.byte	27
	.byte	'Ifx_SRC_GVADC',0,10,201,4,3
	.word	71868
	.byte	14
	.word	69181
	.byte	10
	.byte	'_Ifx_SRC_GXBAR',0,10,204,4,25,4,13
	.byte	'XBAR',0
	.word	71896
	.byte	4,2,35,0,0,14
	.word	71901
	.byte	27
	.byte	'Ifx_SRC_GXBAR',0,10,207,4,3
	.word	71937
	.byte	10
	.byte	'_Ifx_STM_ACCEN0_Bits',0,20,45,16,4,11
	.byte	'EN0',0,1
	.word	675
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	675
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	675
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	675
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	675
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	675
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	675
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	675
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	675
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	675
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	675
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	675
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	675
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	675
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	675
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	675
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	675
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	675
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	675
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	675
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	675
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	675
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	675
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	675
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	675
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	675
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	675
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	675
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	675
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	675
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	675
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	675
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_STM_ACCEN0_Bits',0,20,79,3
	.word	71965
	.byte	10
	.byte	'_Ifx_STM_ACCEN1_Bits',0,20,82,16,4,11
	.byte	'reserved_0',0,4
	.word	500
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_STM_ACCEN1_Bits',0,20,85,3
	.word	72522
	.byte	10
	.byte	'_Ifx_STM_CAP_Bits',0,20,88,16,4,11
	.byte	'STMCAP63_32',0,4
	.word	500
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_STM_CAP_Bits',0,20,91,3
	.word	72599
	.byte	10
	.byte	'_Ifx_STM_CAPSV_Bits',0,20,94,16,4,11
	.byte	'STMCAP63_32',0,4
	.word	500
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_STM_CAPSV_Bits',0,20,97,3
	.word	72671
	.byte	10
	.byte	'_Ifx_STM_CLC_Bits',0,20,100,16,4,11
	.byte	'DISR',0,1
	.word	675
	.byte	1,7,2,35,0,11
	.byte	'DISS',0,1
	.word	675
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	675
	.byte	1,5,2,35,0,11
	.byte	'EDIS',0,1
	.word	675
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	500
	.byte	28,0,2,35,0,0,27
	.byte	'Ifx_STM_CLC_Bits',0,20,107,3
	.word	72747
	.byte	10
	.byte	'_Ifx_STM_CMCON_Bits',0,20,110,16,4,11
	.byte	'MSIZE0',0,1
	.word	675
	.byte	5,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	675
	.byte	3,0,2,35,0,11
	.byte	'MSTART0',0,1
	.word	675
	.byte	5,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	675
	.byte	3,0,2,35,1,11
	.byte	'MSIZE1',0,1
	.word	675
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	675
	.byte	3,0,2,35,2,11
	.byte	'MSTART1',0,1
	.word	675
	.byte	5,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	675
	.byte	3,0,2,35,3,0,27
	.byte	'Ifx_STM_CMCON_Bits',0,20,120,3
	.word	72888
	.byte	10
	.byte	'_Ifx_STM_CMP_Bits',0,20,123,16,4,11
	.byte	'CMPVAL',0,4
	.word	500
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_STM_CMP_Bits',0,20,126,3
	.word	73106
	.byte	10
	.byte	'_Ifx_STM_ICR_Bits',0,20,129,1,16,4,11
	.byte	'CMP0EN',0,1
	.word	675
	.byte	1,7,2,35,0,11
	.byte	'CMP0IR',0,1
	.word	675
	.byte	1,6,2,35,0,11
	.byte	'CMP0OS',0,1
	.word	675
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	675
	.byte	1,4,2,35,0,11
	.byte	'CMP1EN',0,1
	.word	675
	.byte	1,3,2,35,0,11
	.byte	'CMP1IR',0,1
	.word	675
	.byte	1,2,2,35,0,11
	.byte	'CMP1OS',0,1
	.word	675
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,4
	.word	500
	.byte	25,0,2,35,0,0,27
	.byte	'Ifx_STM_ICR_Bits',0,20,139,1,3
	.word	73173
	.byte	10
	.byte	'_Ifx_STM_ID_Bits',0,20,142,1,16,4,11
	.byte	'MODREV',0,1
	.word	675
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	675
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	692
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_STM_ID_Bits',0,20,147,1,3
	.word	73376
	.byte	10
	.byte	'_Ifx_STM_ISCR_Bits',0,20,150,1,16,4,11
	.byte	'CMP0IRR',0,1
	.word	675
	.byte	1,7,2,35,0,11
	.byte	'CMP0IRS',0,1
	.word	675
	.byte	1,6,2,35,0,11
	.byte	'CMP1IRR',0,1
	.word	675
	.byte	1,5,2,35,0,11
	.byte	'CMP1IRS',0,1
	.word	675
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	500
	.byte	28,0,2,35,0,0,27
	.byte	'Ifx_STM_ISCR_Bits',0,20,157,1,3
	.word	73483
	.byte	10
	.byte	'_Ifx_STM_KRST0_Bits',0,20,160,1,16,4,11
	.byte	'RST',0,1
	.word	675
	.byte	1,7,2,35,0,11
	.byte	'RSTSTAT',0,1
	.word	675
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	500
	.byte	30,0,2,35,0,0,27
	.byte	'Ifx_STM_KRST0_Bits',0,20,165,1,3
	.word	73634
	.byte	10
	.byte	'_Ifx_STM_KRST1_Bits',0,20,168,1,16,4,11
	.byte	'RST',0,1
	.word	675
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	500
	.byte	31,0,2,35,0,0,27
	.byte	'Ifx_STM_KRST1_Bits',0,20,172,1,3
	.word	73745
	.byte	10
	.byte	'_Ifx_STM_KRSTCLR_Bits',0,20,175,1,16,4,11
	.byte	'CLR',0,1
	.word	675
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	500
	.byte	31,0,2,35,0,0,27
	.byte	'Ifx_STM_KRSTCLR_Bits',0,20,179,1,3
	.word	73837
	.byte	10
	.byte	'_Ifx_STM_OCS_Bits',0,20,182,1,16,4,11
	.byte	'reserved_0',0,4
	.word	500
	.byte	24,8,2,35,0,11
	.byte	'SUS',0,1
	.word	675
	.byte	4,4,2,35,3,11
	.byte	'SUS_P',0,1
	.word	675
	.byte	1,3,2,35,3,11
	.byte	'SUSSTA',0,1
	.word	675
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	675
	.byte	2,0,2,35,3,0,27
	.byte	'Ifx_STM_OCS_Bits',0,20,189,1,3
	.word	73933
	.byte	10
	.byte	'_Ifx_STM_TIM0_Bits',0,20,192,1,16,4,11
	.byte	'STM31_0',0,4
	.word	500
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_STM_TIM0_Bits',0,20,195,1,3
	.word	74079
	.byte	10
	.byte	'_Ifx_STM_TIM0SV_Bits',0,20,198,1,16,4,11
	.byte	'STM31_0',0,4
	.word	500
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_STM_TIM0SV_Bits',0,20,201,1,3
	.word	74151
	.byte	10
	.byte	'_Ifx_STM_TIM1_Bits',0,20,204,1,16,4,11
	.byte	'STM35_4',0,4
	.word	500
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_STM_TIM1_Bits',0,20,207,1,3
	.word	74227
	.byte	10
	.byte	'_Ifx_STM_TIM2_Bits',0,20,210,1,16,4,11
	.byte	'STM39_8',0,4
	.word	500
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_STM_TIM2_Bits',0,20,213,1,3
	.word	74299
	.byte	10
	.byte	'_Ifx_STM_TIM3_Bits',0,20,216,1,16,4,11
	.byte	'STM43_12',0,4
	.word	500
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_STM_TIM3_Bits',0,20,219,1,3
	.word	74371
	.byte	10
	.byte	'_Ifx_STM_TIM4_Bits',0,20,222,1,16,4,11
	.byte	'STM47_16',0,4
	.word	500
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_STM_TIM4_Bits',0,20,225,1,3
	.word	74444
	.byte	10
	.byte	'_Ifx_STM_TIM5_Bits',0,20,228,1,16,4,11
	.byte	'STM51_20',0,4
	.word	500
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_STM_TIM5_Bits',0,20,231,1,3
	.word	74517
	.byte	10
	.byte	'_Ifx_STM_TIM6_Bits',0,20,234,1,16,4,11
	.byte	'STM63_32',0,4
	.word	500
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_STM_TIM6_Bits',0,20,237,1,3
	.word	74590
	.byte	12,20,245,1,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71965
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_ACCEN0',0,20,250,1,3
	.word	74663
	.byte	12,20,253,1,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72522
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_ACCEN1',0,20,130,2,3
	.word	74727
	.byte	12,20,133,2,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72599
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_CAP',0,20,138,2,3
	.word	74791
	.byte	12,20,141,2,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72671
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_CAPSV',0,20,146,2,3
	.word	74852
	.byte	12,20,149,2,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72747
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_CLC',0,20,154,2,3
	.word	74915
	.byte	12,20,157,2,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72888
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_CMCON',0,20,162,2,3
	.word	74976
	.byte	12,20,165,2,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	73106
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_CMP',0,20,170,2,3
	.word	75039
	.byte	12,20,173,2,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	73173
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_ICR',0,20,178,2,3
	.word	75100
	.byte	12,20,181,2,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	73376
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_ID',0,20,186,2,3
	.word	75161
	.byte	12,20,189,2,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	73483
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_ISCR',0,20,194,2,3
	.word	75221
	.byte	12,20,197,2,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	73634
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_KRST0',0,20,202,2,3
	.word	75283
	.byte	12,20,205,2,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	73745
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_KRST1',0,20,210,2,3
	.word	75346
	.byte	12,20,213,2,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	73837
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_KRSTCLR',0,20,218,2,3
	.word	75409
	.byte	12,20,221,2,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	73933
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_OCS',0,20,226,2,3
	.word	75474
	.byte	12,20,229,2,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	74079
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_TIM0',0,20,234,2,3
	.word	75535
	.byte	12,20,237,2,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	74151
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_TIM0SV',0,20,242,2,3
	.word	75597
	.byte	12,20,245,2,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	74227
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_TIM1',0,20,250,2,3
	.word	75661
	.byte	12,20,253,2,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	74299
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_TIM2',0,20,130,3,3
	.word	75723
	.byte	12,20,133,3,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	74371
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_TIM3',0,20,138,3,3
	.word	75785
	.byte	12,20,141,3,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	74444
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_TIM4',0,20,146,3,3
	.word	75847
	.byte	12,20,149,3,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	74517
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_TIM5',0,20,154,3,3
	.word	75909
	.byte	12,20,157,3,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	74590
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_TIM6',0,20,162,3,3
	.word	75971
	.byte	15,5,144,1,9,1,16
	.byte	'IfxCpu_CounterMode_normal',0,0,16
	.byte	'IfxCpu_CounterMode_task',0,1,0,27
	.byte	'IfxCpu_CounterMode',0,5,148,1,3
	.word	76033
	.byte	29,5,160,1,9,6,13
	.byte	'counter',0
	.word	1642
	.byte	4,2,35,0,13
	.byte	'overlfow',0
	.word	675
	.byte	1,2,35,4,0,27
	.byte	'IfxCpu_Counter',0,5,164,1,3
	.word	76122
	.byte	29,5,172,1,9,32,13
	.byte	'instruction',0
	.word	76122
	.byte	6,2,35,0,13
	.byte	'clock',0
	.word	76122
	.byte	6,2,35,6,13
	.byte	'counter1',0
	.word	76122
	.byte	6,2,35,12,13
	.byte	'counter2',0
	.word	76122
	.byte	6,2,35,18,13
	.byte	'counter3',0
	.word	76122
	.byte	6,2,35,24,0,27
	.byte	'IfxCpu_Perf',0,5,179,1,3
	.word	76188
	.byte	10
	.byte	'_Ifx_FLASH_ACCEN0_Bits',0,21,45,16,4,11
	.byte	'EN0',0,1
	.word	675
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	675
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	675
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	675
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	675
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	675
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	675
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	675
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	675
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	675
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	675
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	675
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	675
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	675
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	675
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	675
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	675
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	675
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	675
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	675
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	675
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	675
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	675
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	675
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	675
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	675
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	675
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	675
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	675
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	675
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	675
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	675
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_FLASH_ACCEN0_Bits',0,21,79,3
	.word	76306
	.byte	10
	.byte	'_Ifx_FLASH_ACCEN1_Bits',0,21,82,16,4,11
	.byte	'reserved_0',0,4
	.word	500
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_FLASH_ACCEN1_Bits',0,21,85,3
	.word	76867
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_CFG_Bits',0,21,88,16,4,11
	.byte	'SEL',0,1
	.word	675
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	675
	.byte	2,0,2,35,0,11
	.byte	'CLR',0,1
	.word	675
	.byte	1,7,2,35,1,11
	.byte	'DIS',0,1
	.word	675
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	500
	.byte	22,0,2,35,0,0,27
	.byte	'Ifx_FLASH_CBAB_CFG_Bits',0,21,95,3
	.word	76948
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_STAT_Bits',0,21,98,16,4,11
	.byte	'VLD0',0,1
	.word	675
	.byte	1,7,2,35,0,11
	.byte	'VLD1',0,1
	.word	675
	.byte	1,6,2,35,0,11
	.byte	'VLD2',0,1
	.word	675
	.byte	1,5,2,35,0,11
	.byte	'VLD3',0,1
	.word	675
	.byte	1,4,2,35,0,11
	.byte	'VLD4',0,1
	.word	675
	.byte	1,3,2,35,0,11
	.byte	'VLD5',0,1
	.word	675
	.byte	1,2,2,35,0,11
	.byte	'VLD6',0,1
	.word	675
	.byte	1,1,2,35,0,11
	.byte	'VLD7',0,1
	.word	675
	.byte	1,0,2,35,0,11
	.byte	'VLD8',0,1
	.word	675
	.byte	1,7,2,35,1,11
	.byte	'VLD9',0,1
	.word	675
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	500
	.byte	22,0,2,35,0,0,27
	.byte	'Ifx_FLASH_CBAB_STAT_Bits',0,21,111,3
	.word	77101
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_TOP_Bits',0,21,114,16,4,11
	.byte	'reserved_0',0,1
	.word	675
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	500
	.byte	19,8,2,35,0,11
	.byte	'ERR',0,1
	.word	675
	.byte	6,2,2,35,3,11
	.byte	'VLD',0,1
	.word	675
	.byte	1,1,2,35,3,11
	.byte	'CLR',0,1
	.word	675
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_FLASH_CBAB_TOP_Bits',0,21,121,3
	.word	77349
	.byte	10
	.byte	'_Ifx_FLASH_COMM0_Bits',0,21,124,16,4,11
	.byte	'STATUS',0,1
	.word	675
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	500
	.byte	24,0,2,35,0,0,27
	.byte	'Ifx_FLASH_COMM0_Bits',0,21,128,1,3
	.word	77495
	.byte	10
	.byte	'_Ifx_FLASH_COMM1_Bits',0,21,131,1,16,4,11
	.byte	'STATUS',0,1
	.word	675
	.byte	8,0,2,35,0,11
	.byte	'DATA',0,1
	.word	675
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	692
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_FLASH_COMM1_Bits',0,21,136,1,3
	.word	77593
	.byte	10
	.byte	'_Ifx_FLASH_COMM2_Bits',0,21,139,1,16,4,11
	.byte	'STATUS',0,1
	.word	675
	.byte	8,0,2,35,0,11
	.byte	'DATA',0,1
	.word	675
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	692
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_FLASH_COMM2_Bits',0,21,144,1,3
	.word	77709
	.byte	10
	.byte	'_Ifx_FLASH_ECCRD_Bits',0,21,147,1,16,4,11
	.byte	'RCODE',0,4
	.word	500
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	692
	.byte	8,2,2,35,2,11
	.byte	'EDCERRINJ',0,1
	.word	675
	.byte	1,1,2,35,3,11
	.byte	'ECCORDIS',0,1
	.word	675
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_FLASH_ECCRD_Bits',0,21,153,1,3
	.word	77825
	.byte	10
	.byte	'_Ifx_FLASH_ECCRP_Bits',0,21,156,1,16,4,11
	.byte	'RCODE',0,4
	.word	500
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	692
	.byte	8,2,2,35,2,11
	.byte	'EDCERRINJ',0,1
	.word	675
	.byte	1,1,2,35,3,11
	.byte	'ECCORDIS',0,1
	.word	675
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_FLASH_ECCRP_Bits',0,21,162,1,3
	.word	77965
	.byte	10
	.byte	'_Ifx_FLASH_ECCW_Bits',0,21,165,1,16,4,11
	.byte	'WCODE',0,4
	.word	500
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	692
	.byte	8,2,2,35,2,11
	.byte	'DECENCDIS',0,1
	.word	675
	.byte	1,1,2,35,3,11
	.byte	'PECENCDIS',0,1
	.word	675
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_FLASH_ECCW_Bits',0,21,171,1,3
	.word	78105
	.byte	10
	.byte	'_Ifx_FLASH_FCON_Bits',0,21,174,1,16,4,11
	.byte	'WSPFLASH',0,1
	.word	675
	.byte	4,4,2,35,0,11
	.byte	'WSECPF',0,1
	.word	675
	.byte	2,2,2,35,0,11
	.byte	'WSDFLASH',0,2
	.word	692
	.byte	6,4,2,35,0,11
	.byte	'WSECDF',0,1
	.word	675
	.byte	3,1,2,35,1,11
	.byte	'IDLE',0,1
	.word	675
	.byte	1,0,2,35,1,11
	.byte	'ESLDIS',0,1
	.word	675
	.byte	1,7,2,35,2,11
	.byte	'SLEEP',0,1
	.word	675
	.byte	1,6,2,35,2,11
	.byte	'NSAFECC',0,1
	.word	675
	.byte	1,5,2,35,2,11
	.byte	'STALL',0,1
	.word	675
	.byte	1,4,2,35,2,11
	.byte	'RES21',0,1
	.word	675
	.byte	2,2,2,35,2,11
	.byte	'RES23',0,1
	.word	675
	.byte	2,0,2,35,2,11
	.byte	'VOPERM',0,1
	.word	675
	.byte	1,7,2,35,3,11
	.byte	'SQERM',0,1
	.word	675
	.byte	1,6,2,35,3,11
	.byte	'PROERM',0,1
	.word	675
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	675
	.byte	3,2,2,35,3,11
	.byte	'PR5V',0,1
	.word	675
	.byte	1,1,2,35,3,11
	.byte	'EOBM',0,1
	.word	675
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_FLASH_FCON_Bits',0,21,193,1,3
	.word	78244
	.byte	10
	.byte	'_Ifx_FLASH_FPRO_Bits',0,21,196,1,16,4,11
	.byte	'PROINP',0,1
	.word	675
	.byte	1,7,2,35,0,11
	.byte	'PRODISP',0,1
	.word	675
	.byte	1,6,2,35,0,11
	.byte	'PROIND',0,1
	.word	675
	.byte	1,5,2,35,0,11
	.byte	'PRODISD',0,1
	.word	675
	.byte	1,4,2,35,0,11
	.byte	'PROINHSMCOTP',0,1
	.word	675
	.byte	1,3,2,35,0,11
	.byte	'RES5',0,1
	.word	675
	.byte	1,2,2,35,0,11
	.byte	'PROINOTP',0,1
	.word	675
	.byte	1,1,2,35,0,11
	.byte	'RES7',0,1
	.word	675
	.byte	1,0,2,35,0,11
	.byte	'PROINDBG',0,1
	.word	675
	.byte	1,7,2,35,1,11
	.byte	'PRODISDBG',0,1
	.word	675
	.byte	1,6,2,35,1,11
	.byte	'PROINHSM',0,1
	.word	675
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	675
	.byte	5,0,2,35,1,11
	.byte	'DCFP',0,1
	.word	675
	.byte	1,7,2,35,2,11
	.byte	'DDFP',0,1
	.word	675
	.byte	1,6,2,35,2,11
	.byte	'DDFPX',0,1
	.word	675
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	675
	.byte	1,4,2,35,2,11
	.byte	'DDFD',0,1
	.word	675
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	675
	.byte	1,2,2,35,2,11
	.byte	'ENPE',0,1
	.word	675
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	675
	.byte	8,0,2,35,3,0,27
	.byte	'Ifx_FLASH_FPRO_Bits',0,21,218,1,3
	.word	78606
	.byte	10
	.byte	'_Ifx_FLASH_FSR_Bits',0,21,221,1,16,4,11
	.byte	'FABUSY',0,1
	.word	675
	.byte	1,7,2,35,0,11
	.byte	'D0BUSY',0,1
	.word	675
	.byte	1,6,2,35,0,11
	.byte	'RES1',0,1
	.word	675
	.byte	1,5,2,35,0,11
	.byte	'P0BUSY',0,1
	.word	675
	.byte	1,4,2,35,0,11
	.byte	'P1BUSY',0,1
	.word	675
	.byte	1,3,2,35,0,11
	.byte	'RES5',0,1
	.word	675
	.byte	1,2,2,35,0,11
	.byte	'RES6',0,1
	.word	675
	.byte	1,1,2,35,0,11
	.byte	'PROG',0,1
	.word	675
	.byte	1,0,2,35,0,11
	.byte	'ERASE',0,1
	.word	675
	.byte	1,7,2,35,1,11
	.byte	'PFPAGE',0,1
	.word	675
	.byte	1,6,2,35,1,11
	.byte	'DFPAGE',0,1
	.word	675
	.byte	1,5,2,35,1,11
	.byte	'OPER',0,1
	.word	675
	.byte	1,4,2,35,1,11
	.byte	'SQER',0,1
	.word	675
	.byte	1,3,2,35,1,11
	.byte	'PROER',0,1
	.word	675
	.byte	1,2,2,35,1,11
	.byte	'PFSBER',0,1
	.word	675
	.byte	1,1,2,35,1,11
	.byte	'PFDBER',0,1
	.word	675
	.byte	1,0,2,35,1,11
	.byte	'PFMBER',0,1
	.word	675
	.byte	1,7,2,35,2,11
	.byte	'RES17',0,1
	.word	675
	.byte	1,6,2,35,2,11
	.byte	'DFSBER',0,1
	.word	675
	.byte	1,5,2,35,2,11
	.byte	'DFDBER',0,1
	.word	675
	.byte	1,4,2,35,2,11
	.byte	'DFTBER',0,1
	.word	675
	.byte	1,3,2,35,2,11
	.byte	'DFMBER',0,1
	.word	675
	.byte	1,2,2,35,2,11
	.byte	'SRIADDERR',0,1
	.word	675
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	692
	.byte	2,7,2,35,2,11
	.byte	'PVER',0,1
	.word	675
	.byte	1,6,2,35,3,11
	.byte	'EVER',0,1
	.word	675
	.byte	1,5,2,35,3,11
	.byte	'SPND',0,1
	.word	675
	.byte	1,4,2,35,3,11
	.byte	'SLM',0,1
	.word	675
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	675
	.byte	1,2,2,35,3,11
	.byte	'ORIER',0,1
	.word	675
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	675
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_FLASH_FSR_Bits',0,21,254,1,3
	.word	79047
	.byte	10
	.byte	'_Ifx_FLASH_ID_Bits',0,21,129,2,16,4,11
	.byte	'MODREV',0,1
	.word	675
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	675
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	692
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_FLASH_ID_Bits',0,21,134,2,3
	.word	79653
	.byte	10
	.byte	'_Ifx_FLASH_MARD_Bits',0,21,137,2,16,4,11
	.byte	'HMARGIN',0,1
	.word	675
	.byte	1,7,2,35,0,11
	.byte	'SELD0',0,1
	.word	675
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	675
	.byte	1,5,2,35,0,11
	.byte	'SPND',0,1
	.word	675
	.byte	1,4,2,35,0,11
	.byte	'SPNDERR',0,1
	.word	675
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,2
	.word	692
	.byte	10,1,2,35,0,11
	.byte	'TRAPDIS',0,1
	.word	675
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	692
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_FLASH_MARD_Bits',0,21,147,2,3
	.word	79764
	.byte	10
	.byte	'_Ifx_FLASH_MARP_Bits',0,21,150,2,16,4,11
	.byte	'SELP0',0,1
	.word	675
	.byte	1,7,2,35,0,11
	.byte	'SELP1',0,1
	.word	675
	.byte	1,6,2,35,0,11
	.byte	'RES2',0,1
	.word	675
	.byte	1,5,2,35,0,11
	.byte	'RES3',0,1
	.word	675
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	692
	.byte	11,1,2,35,0,11
	.byte	'TRAPDIS',0,1
	.word	675
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	692
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_FLASH_MARP_Bits',0,21,159,2,3
	.word	79978
	.byte	10
	.byte	'_Ifx_FLASH_PROCOND_Bits',0,21,162,2,16,4,11
	.byte	'L',0,1
	.word	675
	.byte	1,7,2,35,0,11
	.byte	'NSAFECC',0,1
	.word	675
	.byte	1,6,2,35,0,11
	.byte	'RAMIN',0,1
	.word	675
	.byte	2,4,2,35,0,11
	.byte	'RAMINSEL',0,1
	.word	675
	.byte	4,0,2,35,0,11
	.byte	'OSCCFG',0,1
	.word	675
	.byte	1,7,2,35,1,11
	.byte	'MODE',0,1
	.word	675
	.byte	2,5,2,35,1,11
	.byte	'APREN',0,1
	.word	675
	.byte	1,4,2,35,1,11
	.byte	'CAP0EN',0,1
	.word	675
	.byte	1,3,2,35,1,11
	.byte	'CAP1EN',0,1
	.word	675
	.byte	1,2,2,35,1,11
	.byte	'CAP2EN',0,1
	.word	675
	.byte	1,1,2,35,1,11
	.byte	'CAP3EN',0,1
	.word	675
	.byte	1,0,2,35,1,11
	.byte	'ESR0CNT',0,2
	.word	692
	.byte	12,4,2,35,2,11
	.byte	'RES29',0,1
	.word	675
	.byte	2,2,2,35,3,11
	.byte	'RES30',0,1
	.word	675
	.byte	1,1,2,35,3,11
	.byte	'RPRO',0,1
	.word	675
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_FLASH_PROCOND_Bits',0,21,179,2,3
	.word	80165
	.byte	10
	.byte	'_Ifx_FLASH_PROCONDBG_Bits',0,21,182,2,16,4,11
	.byte	'OCDSDIS',0,1
	.word	675
	.byte	1,7,2,35,0,11
	.byte	'DBGIFLCK',0,1
	.word	675
	.byte	1,6,2,35,0,11
	.byte	'EDM',0,1
	.word	675
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	500
	.byte	28,0,2,35,0,0,27
	.byte	'Ifx_FLASH_PROCONDBG_Bits',0,21,188,2,3
	.word	80489
	.byte	10
	.byte	'_Ifx_FLASH_PROCONHSM_Bits',0,21,191,2,16,4,11
	.byte	'HSMDBGDIS',0,1
	.word	675
	.byte	1,7,2,35,0,11
	.byte	'DBGIFLCK',0,1
	.word	675
	.byte	1,6,2,35,0,11
	.byte	'TSTIFLCK',0,1
	.word	675
	.byte	1,5,2,35,0,11
	.byte	'HSMTSTDIS',0,1
	.word	675
	.byte	1,4,2,35,0,11
	.byte	'RES15',0,2
	.word	692
	.byte	12,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	692
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_FLASH_PROCONHSM_Bits',0,21,199,2,3
	.word	80632
	.byte	10
	.byte	'_Ifx_FLASH_PROCONHSMCOTP_Bits',0,21,202,2,16,4,11
	.byte	'HSMBOOTEN',0,1
	.word	675
	.byte	1,7,2,35,0,11
	.byte	'SSWWAIT',0,1
	.word	675
	.byte	1,6,2,35,0,11
	.byte	'HSMDX',0,1
	.word	675
	.byte	1,5,2,35,0,11
	.byte	'HSM6X',0,1
	.word	675
	.byte	1,4,2,35,0,11
	.byte	'HSM16X',0,1
	.word	675
	.byte	1,3,2,35,0,11
	.byte	'HSM17X',0,1
	.word	675
	.byte	1,2,2,35,0,11
	.byte	'S6ROM',0,1
	.word	675
	.byte	1,1,2,35,0,11
	.byte	'HSMENPINS',0,2
	.word	692
	.byte	2,7,2,35,0,11
	.byte	'HSMENRES',0,1
	.word	675
	.byte	2,5,2,35,1,11
	.byte	'DESTDBG',0,1
	.word	675
	.byte	2,3,2,35,1,11
	.byte	'BLKFLAN',0,1
	.word	675
	.byte	1,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	675
	.byte	2,0,2,35,1,11
	.byte	'S16ROM',0,1
	.word	675
	.byte	1,7,2,35,2,11
	.byte	'S17ROM',0,1
	.word	675
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	692
	.byte	14,0,2,35,2,0,27
	.byte	'Ifx_FLASH_PROCONHSMCOTP_Bits',0,21,219,2,3
	.word	80821
	.byte	10
	.byte	'_Ifx_FLASH_PROCONOTP_Bits',0,21,222,2,16,4,11
	.byte	'S0ROM',0,1
	.word	675
	.byte	1,7,2,35,0,11
	.byte	'S1ROM',0,1
	.word	675
	.byte	1,6,2,35,0,11
	.byte	'S2ROM',0,1
	.word	675
	.byte	1,5,2,35,0,11
	.byte	'S3ROM',0,1
	.word	675
	.byte	1,4,2,35,0,11
	.byte	'S4ROM',0,1
	.word	675
	.byte	1,3,2,35,0,11
	.byte	'S5ROM',0,1
	.word	675
	.byte	1,2,2,35,0,11
	.byte	'S6ROM',0,1
	.word	675
	.byte	1,1,2,35,0,11
	.byte	'S7ROM',0,1
	.word	675
	.byte	1,0,2,35,0,11
	.byte	'S8ROM',0,1
	.word	675
	.byte	1,7,2,35,1,11
	.byte	'S9ROM',0,1
	.word	675
	.byte	1,6,2,35,1,11
	.byte	'S10ROM',0,1
	.word	675
	.byte	1,5,2,35,1,11
	.byte	'S11ROM',0,1
	.word	675
	.byte	1,4,2,35,1,11
	.byte	'S12ROM',0,1
	.word	675
	.byte	1,3,2,35,1,11
	.byte	'S13ROM',0,1
	.word	675
	.byte	1,2,2,35,1,11
	.byte	'S14ROM',0,1
	.word	675
	.byte	1,1,2,35,1,11
	.byte	'S15ROM',0,1
	.word	675
	.byte	1,0,2,35,1,11
	.byte	'S16ROM',0,1
	.word	675
	.byte	1,7,2,35,2,11
	.byte	'S17ROM',0,1
	.word	675
	.byte	1,6,2,35,2,11
	.byte	'S18ROM',0,1
	.word	675
	.byte	1,5,2,35,2,11
	.byte	'S19ROM',0,1
	.word	675
	.byte	1,4,2,35,2,11
	.byte	'S20ROM',0,1
	.word	675
	.byte	1,3,2,35,2,11
	.byte	'S21ROM',0,1
	.word	675
	.byte	1,2,2,35,2,11
	.byte	'S22ROM',0,1
	.word	675
	.byte	1,1,2,35,2,11
	.byte	'S23ROM',0,1
	.word	675
	.byte	1,0,2,35,2,11
	.byte	'S24ROM',0,1
	.word	675
	.byte	1,7,2,35,3,11
	.byte	'S25ROM',0,1
	.word	675
	.byte	1,6,2,35,3,11
	.byte	'S26ROM',0,1
	.word	675
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	675
	.byte	2,3,2,35,3,11
	.byte	'BML',0,1
	.word	675
	.byte	2,1,2,35,3,11
	.byte	'TP',0,1
	.word	675
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_FLASH_PROCONOTP_Bits',0,21,254,2,3
	.word	81184
	.byte	10
	.byte	'_Ifx_FLASH_PROCONP_Bits',0,21,129,3,16,4,11
	.byte	'S0L',0,1
	.word	675
	.byte	1,7,2,35,0,11
	.byte	'S1L',0,1
	.word	675
	.byte	1,6,2,35,0,11
	.byte	'S2L',0,1
	.word	675
	.byte	1,5,2,35,0,11
	.byte	'S3L',0,1
	.word	675
	.byte	1,4,2,35,0,11
	.byte	'S4L',0,1
	.word	675
	.byte	1,3,2,35,0,11
	.byte	'S5L',0,1
	.word	675
	.byte	1,2,2,35,0,11
	.byte	'S6L',0,1
	.word	675
	.byte	1,1,2,35,0,11
	.byte	'S7L',0,1
	.word	675
	.byte	1,0,2,35,0,11
	.byte	'S8L',0,1
	.word	675
	.byte	1,7,2,35,1,11
	.byte	'S9L',0,1
	.word	675
	.byte	1,6,2,35,1,11
	.byte	'S10L',0,1
	.word	675
	.byte	1,5,2,35,1,11
	.byte	'S11L',0,1
	.word	675
	.byte	1,4,2,35,1,11
	.byte	'S12L',0,1
	.word	675
	.byte	1,3,2,35,1,11
	.byte	'S13L',0,1
	.word	675
	.byte	1,2,2,35,1,11
	.byte	'S14L',0,1
	.word	675
	.byte	1,1,2,35,1,11
	.byte	'S15L',0,1
	.word	675
	.byte	1,0,2,35,1,11
	.byte	'S16L',0,1
	.word	675
	.byte	1,7,2,35,2,11
	.byte	'S17L',0,1
	.word	675
	.byte	1,6,2,35,2,11
	.byte	'S18L',0,1
	.word	675
	.byte	1,5,2,35,2,11
	.byte	'S19L',0,1
	.word	675
	.byte	1,4,2,35,2,11
	.byte	'S20L',0,1
	.word	675
	.byte	1,3,2,35,2,11
	.byte	'S21L',0,1
	.word	675
	.byte	1,2,2,35,2,11
	.byte	'S22L',0,1
	.word	675
	.byte	1,1,2,35,2,11
	.byte	'S23L',0,1
	.word	675
	.byte	1,0,2,35,2,11
	.byte	'S24L',0,1
	.word	675
	.byte	1,7,2,35,3,11
	.byte	'S25L',0,1
	.word	675
	.byte	1,6,2,35,3,11
	.byte	'S26L',0,1
	.word	675
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	675
	.byte	4,1,2,35,3,11
	.byte	'RPRO',0,1
	.word	675
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_FLASH_PROCONP_Bits',0,21,160,3,3
	.word	81779
	.byte	10
	.byte	'_Ifx_FLASH_PROCONWOP_Bits',0,21,163,3,16,4,11
	.byte	'S0WOP',0,1
	.word	675
	.byte	1,7,2,35,0,11
	.byte	'S1WOP',0,1
	.word	675
	.byte	1,6,2,35,0,11
	.byte	'S2WOP',0,1
	.word	675
	.byte	1,5,2,35,0,11
	.byte	'S3WOP',0,1
	.word	675
	.byte	1,4,2,35,0,11
	.byte	'S4WOP',0,1
	.word	675
	.byte	1,3,2,35,0,11
	.byte	'S5WOP',0,1
	.word	675
	.byte	1,2,2,35,0,11
	.byte	'S6WOP',0,1
	.word	675
	.byte	1,1,2,35,0,11
	.byte	'S7WOP',0,1
	.word	675
	.byte	1,0,2,35,0,11
	.byte	'S8WOP',0,1
	.word	675
	.byte	1,7,2,35,1,11
	.byte	'S9WOP',0,1
	.word	675
	.byte	1,6,2,35,1,11
	.byte	'S10WOP',0,1
	.word	675
	.byte	1,5,2,35,1,11
	.byte	'S11WOP',0,1
	.word	675
	.byte	1,4,2,35,1,11
	.byte	'S12WOP',0,1
	.word	675
	.byte	1,3,2,35,1,11
	.byte	'S13WOP',0,1
	.word	675
	.byte	1,2,2,35,1,11
	.byte	'S14WOP',0,1
	.word	675
	.byte	1,1,2,35,1,11
	.byte	'S15WOP',0,1
	.word	675
	.byte	1,0,2,35,1,11
	.byte	'S16WOP',0,1
	.word	675
	.byte	1,7,2,35,2,11
	.byte	'S17WOP',0,1
	.word	675
	.byte	1,6,2,35,2,11
	.byte	'S18WOP',0,1
	.word	675
	.byte	1,5,2,35,2,11
	.byte	'S19WOP',0,1
	.word	675
	.byte	1,4,2,35,2,11
	.byte	'S20WOP',0,1
	.word	675
	.byte	1,3,2,35,2,11
	.byte	'S21WOP',0,1
	.word	675
	.byte	1,2,2,35,2,11
	.byte	'S22WOP',0,1
	.word	675
	.byte	1,1,2,35,2,11
	.byte	'S23WOP',0,1
	.word	675
	.byte	1,0,2,35,2,11
	.byte	'S24WOP',0,1
	.word	675
	.byte	1,7,2,35,3,11
	.byte	'S25WOP',0,1
	.word	675
	.byte	1,6,2,35,3,11
	.byte	'S26WOP',0,1
	.word	675
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	675
	.byte	4,1,2,35,3,11
	.byte	'DATM',0,1
	.word	675
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_FLASH_PROCONWOP_Bits',0,21,194,3,3
	.word	82303
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG0_Bits',0,21,197,3,16,4,11
	.byte	'TAG',0,1
	.word	675
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	500
	.byte	26,0,2,35,0,0,27
	.byte	'Ifx_FLASH_RDB_CFG0_Bits',0,21,201,3,3
	.word	82885
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG1_Bits',0,21,204,3,16,4,11
	.byte	'TAG',0,1
	.word	675
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	500
	.byte	26,0,2,35,0,0,27
	.byte	'Ifx_FLASH_RDB_CFG1_Bits',0,21,208,3,3
	.word	82987
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG2_Bits',0,21,211,3,16,4,11
	.byte	'TAG',0,1
	.word	675
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	500
	.byte	26,0,2,35,0,0,27
	.byte	'Ifx_FLASH_RDB_CFG2_Bits',0,21,215,3,3
	.word	83089
	.byte	10
	.byte	'_Ifx_FLASH_RRAD_Bits',0,21,218,3,16,4,11
	.byte	'reserved_0',0,1
	.word	675
	.byte	3,5,2,35,0,11
	.byte	'ADD',0,4
	.word	500
	.byte	29,0,2,35,0,0,27
	.byte	'Ifx_FLASH_RRAD_Bits',0,21,222,3,3
	.word	83191
	.byte	10
	.byte	'_Ifx_FLASH_RRCT_Bits',0,21,225,3,16,4,11
	.byte	'STRT',0,1
	.word	675
	.byte	1,7,2,35,0,11
	.byte	'STP',0,1
	.word	675
	.byte	1,6,2,35,0,11
	.byte	'BUSY',0,1
	.word	675
	.byte	1,5,2,35,0,11
	.byte	'DONE',0,1
	.word	675
	.byte	1,4,2,35,0,11
	.byte	'ERR',0,1
	.word	675
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	675
	.byte	3,0,2,35,0,11
	.byte	'EOBM',0,1
	.word	675
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	675
	.byte	7,0,2,35,1,11
	.byte	'CNT',0,2
	.word	692
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_FLASH_RRCT_Bits',0,21,236,3,3
	.word	83285
	.byte	10
	.byte	'_Ifx_FLASH_RRD0_Bits',0,21,239,3,16,4,11
	.byte	'DATA',0,4
	.word	500
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_FLASH_RRD0_Bits',0,21,242,3,3
	.word	83495
	.byte	10
	.byte	'_Ifx_FLASH_RRD1_Bits',0,21,245,3,16,4,11
	.byte	'DATA',0,4
	.word	500
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_FLASH_RRD1_Bits',0,21,248,3,3
	.word	83568
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_CFG_Bits',0,21,251,3,16,4,11
	.byte	'SEL',0,1
	.word	675
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	675
	.byte	2,0,2,35,0,11
	.byte	'CLR',0,1
	.word	675
	.byte	1,7,2,35,1,11
	.byte	'DIS',0,1
	.word	675
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	500
	.byte	22,0,2,35,0,0,27
	.byte	'Ifx_FLASH_UBAB_CFG_Bits',0,21,130,4,3
	.word	83641
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_STAT_Bits',0,21,133,4,16,4,11
	.byte	'VLD0',0,1
	.word	675
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	500
	.byte	31,0,2,35,0,0,27
	.byte	'Ifx_FLASH_UBAB_STAT_Bits',0,21,137,4,3
	.word	83796
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_TOP_Bits',0,21,140,4,16,4,11
	.byte	'reserved_0',0,1
	.word	675
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	500
	.byte	19,8,2,35,0,11
	.byte	'ERR',0,1
	.word	675
	.byte	6,2,2,35,3,11
	.byte	'VLD',0,1
	.word	675
	.byte	1,1,2,35,3,11
	.byte	'CLR',0,1
	.word	675
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_FLASH_UBAB_TOP_Bits',0,21,147,4,3
	.word	83901
	.byte	12,21,155,4,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	76306
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_ACCEN0',0,21,160,4,3
	.word	84049
	.byte	12,21,163,4,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	76867
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_ACCEN1',0,21,168,4,3
	.word	84115
	.byte	12,21,171,4,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	76948
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_CBAB_CFG',0,21,176,4,3
	.word	84181
	.byte	12,21,179,4,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	77101
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_CBAB_STAT',0,21,184,4,3
	.word	84249
	.byte	12,21,187,4,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	77349
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_CBAB_TOP',0,21,192,4,3
	.word	84318
	.byte	12,21,195,4,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	77495
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_COMM0',0,21,200,4,3
	.word	84386
	.byte	12,21,203,4,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	77593
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_COMM1',0,21,208,4,3
	.word	84451
	.byte	12,21,211,4,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	77709
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_COMM2',0,21,216,4,3
	.word	84516
	.byte	12,21,219,4,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	77825
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_ECCRD',0,21,224,4,3
	.word	84581
	.byte	12,21,227,4,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	77965
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_ECCRP',0,21,232,4,3
	.word	84646
	.byte	12,21,235,4,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	78105
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_ECCW',0,21,240,4,3
	.word	84711
	.byte	12,21,243,4,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	78244
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_FCON',0,21,248,4,3
	.word	84775
	.byte	12,21,251,4,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	78606
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_FPRO',0,21,128,5,3
	.word	84839
	.byte	12,21,131,5,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	79047
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_FSR',0,21,136,5,3
	.word	84903
	.byte	12,21,139,5,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	79653
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_ID',0,21,144,5,3
	.word	84966
	.byte	12,21,147,5,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	79764
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_MARD',0,21,152,5,3
	.word	85028
	.byte	12,21,155,5,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	79978
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_MARP',0,21,160,5,3
	.word	85092
	.byte	12,21,163,5,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	80165
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_PROCOND',0,21,168,5,3
	.word	85156
	.byte	12,21,171,5,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	80489
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_PROCONDBG',0,21,176,5,3
	.word	85223
	.byte	12,21,179,5,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	80632
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_PROCONHSM',0,21,184,5,3
	.word	85292
	.byte	12,21,187,5,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	80821
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_PROCONHSMCOTP',0,21,192,5,3
	.word	85361
	.byte	12,21,195,5,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81184
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_PROCONOTP',0,21,200,5,3
	.word	85434
	.byte	12,21,203,5,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81779
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_PROCONP',0,21,208,5,3
	.word	85503
	.byte	12,21,211,5,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	82303
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_PROCONWOP',0,21,216,5,3
	.word	85570
	.byte	12,21,219,5,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	82885
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_RDB_CFG0',0,21,224,5,3
	.word	85639
	.byte	12,21,227,5,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	82987
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_RDB_CFG1',0,21,232,5,3
	.word	85707
	.byte	12,21,235,5,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	83089
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_RDB_CFG2',0,21,240,5,3
	.word	85775
	.byte	12,21,243,5,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	83191
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_RRAD',0,21,248,5,3
	.word	85843
	.byte	12,21,251,5,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	83285
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_RRCT',0,21,128,6,3
	.word	85907
	.byte	12,21,131,6,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	83495
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_RRD0',0,21,136,6,3
	.word	85971
	.byte	12,21,139,6,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	83568
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_RRD1',0,21,144,6,3
	.word	86035
	.byte	12,21,147,6,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	83641
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_UBAB_CFG',0,21,152,6,3
	.word	86099
	.byte	12,21,155,6,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	83796
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_UBAB_STAT',0,21,160,6,3
	.word	86167
	.byte	12,21,163,6,9,4,13
	.byte	'U',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	516
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	83901
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_UBAB_TOP',0,21,168,6,3
	.word	86236
	.byte	10
	.byte	'_Ifx_FLASH_CBAB',0,21,179,6,25,12,13
	.byte	'CFG',0
	.word	84181
	.byte	4,2,35,0,13
	.byte	'STAT',0
	.word	84249
	.byte	4,2,35,4,13
	.byte	'TOP',0
	.word	84318
	.byte	4,2,35,8,0,14
	.word	86304
	.byte	27
	.byte	'Ifx_FLASH_CBAB',0,21,184,6,3
	.word	86367
	.byte	10
	.byte	'_Ifx_FLASH_RDB',0,21,187,6,25,12,13
	.byte	'CFG0',0
	.word	85639
	.byte	4,2,35,0,13
	.byte	'CFG1',0
	.word	85707
	.byte	4,2,35,4,13
	.byte	'CFG2',0
	.word	85775
	.byte	4,2,35,8,0,14
	.word	86396
	.byte	27
	.byte	'Ifx_FLASH_RDB',0,21,192,6,3
	.word	86460
	.byte	10
	.byte	'_Ifx_FLASH_UBAB',0,21,195,6,25,12,13
	.byte	'CFG',0
	.word	86099
	.byte	4,2,35,0,13
	.byte	'STAT',0
	.word	86167
	.byte	4,2,35,4,13
	.byte	'TOP',0
	.word	86236
	.byte	4,2,35,8,0,14
	.word	86488
	.byte	27
	.byte	'Ifx_FLASH_UBAB',0,21,200,6,3
	.word	86551
	.byte	27
	.byte	'Ifx_P_ACCEN0_Bits',0,8,79,3
	.word	8451
	.byte	27
	.byte	'Ifx_P_ACCEN1_Bits',0,8,85,3
	.word	8364
	.byte	27
	.byte	'Ifx_P_ESR_Bits',0,8,107,3
	.word	4707
	.byte	27
	.byte	'Ifx_P_ID_Bits',0,8,115,3
	.word	2760
	.byte	27
	.byte	'Ifx_P_IN_Bits',0,8,137,1,3
	.word	3755
	.byte	27
	.byte	'Ifx_P_IOCR0_Bits',0,8,150,1,3
	.word	2888
	.byte	27
	.byte	'Ifx_P_IOCR12_Bits',0,8,163,1,3
	.word	3535
	.byte	27
	.byte	'Ifx_P_IOCR4_Bits',0,8,176,1,3
	.word	3103
	.byte	27
	.byte	'Ifx_P_IOCR8_Bits',0,8,189,1,3
	.word	3318
	.byte	27
	.byte	'Ifx_P_LPCR0_Bits',0,8,197,1,3
	.word	7723
	.byte	27
	.byte	'Ifx_P_LPCR1_Bits',0,8,205,1,3
	.word	7847
	.byte	27
	.byte	'Ifx_P_LPCR1_P21_Bits',0,8,215,1,3
	.word	7931
	.byte	27
	.byte	'Ifx_P_LPCR2_Bits',0,8,229,1,3
	.word	8111
	.byte	27
	.byte	'Ifx_P_OMCR0_Bits',0,8,240,1,3
	.word	6362
	.byte	27
	.byte	'Ifx_P_OMCR12_Bits',0,8,250,1,3
	.word	6886
	.byte	27
	.byte	'Ifx_P_OMCR4_Bits',0,8,133,2,3
	.word	6536
	.byte	27
	.byte	'Ifx_P_OMCR8_Bits',0,8,144,2,3
	.word	6710
	.byte	27
	.byte	'Ifx_P_OMCR_Bits',0,8,166,2,3
	.word	7375
	.byte	27
	.byte	'Ifx_P_OMR_Bits',0,8,203,2,3
	.word	2189
	.byte	27
	.byte	'Ifx_P_OMSR0_Bits',0,8,213,2,3
	.word	5699
	.byte	27
	.byte	'Ifx_P_OMSR12_Bits',0,8,224,2,3
	.word	6187
	.byte	27
	.byte	'Ifx_P_OMSR4_Bits',0,8,235,2,3
	.word	5846
	.byte	27
	.byte	'Ifx_P_OMSR8_Bits',0,8,246,2,3
	.word	6015
	.byte	27
	.byte	'Ifx_P_OMSR_Bits',0,8,140,3,3
	.word	7042
	.byte	27
	.byte	'Ifx_P_OUT_Bits',0,8,162,3,3
	.word	1873
	.byte	27
	.byte	'Ifx_P_PCSR_Bits',0,8,180,3,3
	.word	5413
	.byte	27
	.byte	'Ifx_P_PDISC_Bits',0,8,202,3,3
	.word	5047
	.byte	27
	.byte	'Ifx_P_PDR0_Bits',0,8,223,3,3
	.word	4078
	.byte	27
	.byte	'Ifx_P_PDR1_Bits',0,8,244,3,3
	.word	4382
	.byte	27
	.byte	'Ifx_P_ACCEN0',0,8,129,4,3
	.word	8978
	.byte	27
	.byte	'Ifx_P_ACCEN1',0,8,137,4,3
	.word	8411
	.byte	27
	.byte	'Ifx_P_ESR',0,8,145,4,3
	.word	4998
	.byte	27
	.byte	'Ifx_P_ID',0,8,153,4,3
	.word	2839
	.byte	27
	.byte	'Ifx_P_IN',0,8,161,4,3
	.word	4029
	.byte	27
	.byte	'Ifx_P_IOCR0',0,8,169,4,3
	.word	3063
	.byte	27
	.byte	'Ifx_P_IOCR12',0,8,177,4,3
	.word	3715
	.byte	27
	.byte	'Ifx_P_IOCR4',0,8,185,4,3
	.word	3278
	.byte	27
	.byte	'Ifx_P_IOCR8',0,8,193,4,3
	.word	3495
	.byte	27
	.byte	'Ifx_P_LPCR0',0,8,201,4,3
	.word	7807
	.byte	27
	.byte	'Ifx_P_LPCR1',0,8,210,4,3
	.word	8056
	.byte	27
	.byte	'Ifx_P_LPCR2',0,8,218,4,3
	.word	8315
	.byte	27
	.byte	'Ifx_P_OMCR',0,8,226,4,3
	.word	7683
	.byte	27
	.byte	'Ifx_P_OMCR0',0,8,234,4,3
	.word	6496
	.byte	27
	.byte	'Ifx_P_OMCR12',0,8,242,4,3
	.word	7002
	.byte	27
	.byte	'Ifx_P_OMCR4',0,8,250,4,3
	.word	6670
	.byte	27
	.byte	'Ifx_P_OMCR8',0,8,130,5,3
	.word	6846
	.byte	27
	.byte	'Ifx_P_OMR',0,8,138,5,3
	.word	2720
	.byte	27
	.byte	'Ifx_P_OMSR',0,8,146,5,3
	.word	7335
	.byte	27
	.byte	'Ifx_P_OMSR0',0,8,154,5,3
	.word	5806
	.byte	27
	.byte	'Ifx_P_OMSR12',0,8,162,5,3
	.word	6322
	.byte	27
	.byte	'Ifx_P_OMSR4',0,8,170,5,3
	.word	5975
	.byte	27
	.byte	'Ifx_P_OMSR8',0,8,178,5,3
	.word	6147
	.byte	27
	.byte	'Ifx_P_OUT',0,8,186,5,3
	.word	2149
	.byte	27
	.byte	'Ifx_P_PCSR',0,8,194,5,3
	.word	5659
	.byte	27
	.byte	'Ifx_P_PDISC',0,8,202,5,3
	.word	5373
	.byte	27
	.byte	'Ifx_P_PDR0',0,8,210,5,3
	.word	4342
	.byte	27
	.byte	'Ifx_P_PDR1',0,8,218,5,3
	.word	4658
	.byte	14
	.word	9018
	.byte	27
	.byte	'Ifx_P',0,8,139,6,3
	.word	87898
	.byte	27
	.byte	'IfxPort_InputMode',0,7,89,3
	.word	9631
	.byte	27
	.byte	'IfxPort_OutputIdx',0,7,130,1,3
	.word	9906
	.byte	27
	.byte	'IfxPort_OutputMode',0,7,138,1,3
	.word	9836
	.byte	15,7,144,1,9,1,16
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed1',0,0,16
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed2',0,1,16
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed3',0,2,16
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed4',0,3,16
	.byte	'IfxPort_PadDriver_lvdsSpeed1',0,4,16
	.byte	'IfxPort_PadDriver_lvdsSpeed2',0,5,16
	.byte	'IfxPort_PadDriver_lvdsSpeed3',0,6,16
	.byte	'IfxPort_PadDriver_lvdsSpeed4',0,7,16
	.byte	'IfxPort_PadDriver_ttlSpeed1',0,8,16
	.byte	'IfxPort_PadDriver_ttlSpeed2',0,9,16
	.byte	'IfxPort_PadDriver_ttlSpeed3',0,10,16
	.byte	'IfxPort_PadDriver_ttlSpeed4',0,11,0,27
	.byte	'IfxPort_PadDriver',0,7,158,1,3
	.word	87999
	.byte	27
	.byte	'IfxPort_State',0,7,178,1,3
	.word	10219
	.byte	29,7,190,1,9,8,13
	.byte	'port',0
	.word	9626
	.byte	4,2,35,0,13
	.byte	'pinIndex',0
	.word	675
	.byte	1,2,35,4,0,27
	.byte	'IfxPort_Pin',0,7,194,1,3
	.word	88464
	.byte	27
	.byte	'IfxScuCcu_PllStepsFunctionHook',0,13,148,1,16
	.word	239
	.byte	29,13,212,5,9,8,13
	.byte	'value',0
	.word	1642
	.byte	4,2,35,0,13
	.byte	'mask',0
	.word	1642
	.byte	4,2,35,4,0,27
	.byte	'IfxScuCcu_CcuconRegConfig',0,13,216,5,3
	.word	88564
	.byte	29,13,221,5,9,8,13
	.byte	'pDivider',0
	.word	675
	.byte	1,2,35,0,13
	.byte	'nDivider',0
	.word	675
	.byte	1,2,35,1,13
	.byte	'k2Initial',0
	.word	675
	.byte	1,2,35,2,13
	.byte	'waitTime',0
	.word	296
	.byte	4,2,35,4,0,27
	.byte	'IfxScuCcu_InitialStepConfig',0,13,227,5,3
	.word	88635
	.byte	29,13,231,5,9,12,13
	.byte	'k2Step',0
	.word	675
	.byte	1,2,35,0,13
	.byte	'waitTime',0
	.word	296
	.byte	4,2,35,2,13
	.byte	'hookFunction',0
	.word	88524
	.byte	4,2,35,8,0,27
	.byte	'IfxScuCcu_PllStepsConfig',0,13,236,5,3
	.word	88752
	.byte	3
	.word	236
	.byte	29,13,244,5,9,48,13
	.byte	'ccucon0',0
	.word	88564
	.byte	8,2,35,0,13
	.byte	'ccucon1',0
	.word	88564
	.byte	8,2,35,8,13
	.byte	'ccucon2',0
	.word	88564
	.byte	8,2,35,16,13
	.byte	'ccucon5',0
	.word	88564
	.byte	8,2,35,24,13
	.byte	'ccucon6',0
	.word	88564
	.byte	8,2,35,32,13
	.byte	'ccucon7',0
	.word	88564
	.byte	8,2,35,40,0,27
	.byte	'IfxScuCcu_ClockDistributionConfig',0,13,252,5,3
	.word	88854
	.byte	29,13,128,6,9,8,13
	.byte	'value',0
	.word	1642
	.byte	4,2,35,0,13
	.byte	'mask',0
	.word	1642
	.byte	4,2,35,4,0,27
	.byte	'IfxScuCcu_FlashWaitstateConfig',0,13,132,6,3
	.word	89006
	.byte	3
	.word	88752
	.byte	29,13,137,6,9,16,13
	.byte	'numOfPllDividerSteps',0
	.word	675
	.byte	1,2,35,0,13
	.byte	'pllDividerStep',0
	.word	89082
	.byte	4,2,35,4,13
	.byte	'pllInitialStep',0
	.word	88635
	.byte	8,2,35,8,0,27
	.byte	'IfxScuCcu_SysPllConfig',0,13,142,6,3
	.word	89087
	.byte	15,22,69,9,1,16
	.byte	'IfxSrc_Tos_cpu0',0,0,16
	.byte	'IfxSrc_Tos_cpu1',0,1,16
	.byte	'IfxSrc_Tos_dma',0,3,0,27
	.byte	'IfxSrc_Tos',0,22,74,3
	.word	89204
	.byte	29,23,59,15,16,13
	.byte	'module',0
	.word	17314
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	88464
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	21077
	.byte	1,2,35,12,0,26
	.word	89282
	.byte	27
	.byte	'IfxAsclin_Cts_In',0,23,64,3
	.word	89333
	.byte	29,23,67,15,16,13
	.byte	'module',0
	.word	17314
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	88464
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	21077
	.byte	1,2,35,12,0,26
	.word	89363
	.byte	27
	.byte	'IfxAsclin_Rx_In',0,23,72,3
	.word	89414
	.byte	29,23,75,15,16,13
	.byte	'module',0
	.word	17314
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	88464
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	9906
	.byte	1,2,35,12,0,26
	.word	89443
	.byte	27
	.byte	'IfxAsclin_Rts_Out',0,23,80,3
	.word	89494
	.byte	29,23,83,15,16,13
	.byte	'module',0
	.word	17314
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	88464
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	9906
	.byte	1,2,35,12,0,26
	.word	89525
	.byte	27
	.byte	'IfxAsclin_Sclk_Out',0,23,88,3
	.word	89576
	.byte	29,23,91,15,16,13
	.byte	'module',0
	.word	17314
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	88464
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	9906
	.byte	1,2,35,12,0,26
	.word	89608
	.byte	27
	.byte	'IfxAsclin_Slso_Out',0,23,96,3
	.word	89659
	.byte	29,23,99,15,16,13
	.byte	'module',0
	.word	17314
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	88464
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	9906
	.byte	1,2,35,12,0,26
	.word	89691
	.byte	27
	.byte	'IfxAsclin_Tx_Out',0,23,104,3
	.word	89742
	.byte	15,11,82,9,1,16
	.byte	'IfxAsclin_Checksum_classic',0,0,16
	.byte	'IfxAsclin_Checksum_enhanced',0,1,0,27
	.byte	'IfxAsclin_Checksum',0,11,86,3
	.word	89772
	.byte	15,11,91,9,1,16
	.byte	'IfxAsclin_ChecksumInjection_notWritten',0,0,16
	.byte	'IfxAsclin_ChecksumInjection_written',0,1,0,27
	.byte	'IfxAsclin_ChecksumInjection',0,11,95,3
	.word	89864
	.byte	15,11,101,9,1,16
	.byte	'IfxAsclin_ClockPolarity_idleLow',0,0,16
	.byte	'IfxAsclin_ClockPolarity_idleHigh',0,1,0,27
	.byte	'IfxAsclin_ClockPolarity',0,11,105,3
	.word	89985
	.byte	27
	.byte	'IfxAsclin_ClockSource',0,11,118,3
	.word	19813
	.byte	27
	.byte	'IfxAsclin_CtsInputSelect',0,11,129,1,3
	.word	18483
	.byte	15,11,134,1,9,1,16
	.byte	'IfxAsclin_DataLength_1',0,0,16
	.byte	'IfxAsclin_DataLength_2',0,1,16
	.byte	'IfxAsclin_DataLength_3',0,2,16
	.byte	'IfxAsclin_DataLength_4',0,3,16
	.byte	'IfxAsclin_DataLength_5',0,4,16
	.byte	'IfxAsclin_DataLength_6',0,5,16
	.byte	'IfxAsclin_DataLength_7',0,6,16
	.byte	'IfxAsclin_DataLength_8',0,7,16
	.byte	'IfxAsclin_DataLength_9',0,8,16
	.byte	'IfxAsclin_DataLength_10',0,9,16
	.byte	'IfxAsclin_DataLength_11',0,10,16
	.byte	'IfxAsclin_DataLength_12',0,11,16
	.byte	'IfxAsclin_DataLength_13',0,12,16
	.byte	'IfxAsclin_DataLength_14',0,13,16
	.byte	'IfxAsclin_DataLength_15',0,14,16
	.byte	'IfxAsclin_DataLength_16',0,15,0,27
	.byte	'IfxAsclin_DataLength',0,11,152,1,3
	.word	90156
	.byte	15,11,157,1,9,1,16
	.byte	'IfxAsclin_FrameMode_initialise',0,0,16
	.byte	'IfxAsclin_FrameMode_asc',0,1,16
	.byte	'IfxAsclin_FrameMode_spi',0,2,16
	.byte	'IfxAsclin_FrameMode_lin',0,3,0,27
	.byte	'IfxAsclin_FrameMode',0,11,163,1,3
	.word	90600
	.byte	15,11,168,1,9,1,16
	.byte	'IfxAsclin_HeaderResponseSelect_headerAndResponse',0,0,16
	.byte	'IfxAsclin_HeaderResponseSelect_headerOnly',0,1,0,27
	.byte	'IfxAsclin_HeaderResponseSelect',0,11,172,1,3
	.word	90747
	.byte	15,11,179,1,9,1,16
	.byte	'IfxAsclin_IdleDelay_0',0,0,16
	.byte	'IfxAsclin_IdleDelay_1',0,1,16
	.byte	'IfxAsclin_IdleDelay_2',0,2,16
	.byte	'IfxAsclin_IdleDelay_3',0,3,16
	.byte	'IfxAsclin_IdleDelay_4',0,4,16
	.byte	'IfxAsclin_IdleDelay_5',0,5,16
	.byte	'IfxAsclin_IdleDelay_6',0,6,16
	.byte	'IfxAsclin_IdleDelay_7',0,7,0,27
	.byte	'IfxAsclin_IdleDelay',0,11,189,1,3
	.word	90889
	.byte	15,11,195,1,9,1,16
	.byte	'IfxAsclin_LeadDelay_0',0,0,16
	.byte	'IfxAsclin_LeadDelay_1',0,1,16
	.byte	'IfxAsclin_LeadDelay_2',0,2,16
	.byte	'IfxAsclin_LeadDelay_3',0,3,16
	.byte	'IfxAsclin_LeadDelay_4',0,4,16
	.byte	'IfxAsclin_LeadDelay_5',0,5,16
	.byte	'IfxAsclin_LeadDelay_6',0,6,16
	.byte	'IfxAsclin_LeadDelay_7',0,7,0,27
	.byte	'IfxAsclin_LeadDelay',0,11,205,1,3
	.word	91117
	.byte	15,11,210,1,9,1,16
	.byte	'IfxAsclin_LinMode_slave',0,0,16
	.byte	'IfxAsclin_LinMode_master',0,1,0,27
	.byte	'IfxAsclin_LinMode',0,11,214,1,3
	.word	91345
	.byte	15,11,219,1,9,1,16
	.byte	'IfxAsclin_LinResponseTimeoutMode_frameTimeout',0,0,16
	.byte	'IfxAsclin_LinResponseTimeoutMode_responseTimeout',0,1,0,27
	.byte	'IfxAsclin_LinResponseTimeoutMode',0,11,223,1,3
	.word	91432
	.byte	27
	.byte	'IfxAsclin_OversamplingFactor',0,11,243,1,3
	.word	17578
	.byte	15,11,248,1,9,1,16
	.byte	'IfxAsclin_ParityType_even',0,0,16
	.byte	'IfxAsclin_ParityType_odd',0,1,0,27
	.byte	'IfxAsclin_ParityType',0,11,252,1,3
	.word	91618
	.byte	15,11,129,2,9,1,16
	.byte	'IfxAsclin_ReceiveBufferMode_rxFifo',0,0,16
	.byte	'IfxAsclin_ReceiveBufferMode_rxBuffer',0,1,0,27
	.byte	'IfxAsclin_ReceiveBufferMode',0,11,133,2,3
	.word	91710
	.byte	15,11,138,2,9,1,16
	.byte	'IfxAsclin_RtsCtsPolarity_activeHigh',0,0,16
	.byte	'IfxAsclin_RtsCtsPolarity_activeLow',0,1,0,27
	.byte	'IfxAsclin_RtsCtsPolarity',0,11,142,2,3
	.word	91830
	.byte	15,11,147,2,9,1,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_1',0,0,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_2',0,1,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_3',0,2,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_4',0,3,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_5',0,4,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_6',0,5,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_7',0,6,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_8',0,7,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_9',0,8,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_10',0,9,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_11',0,10,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_12',0,11,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_13',0,12,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_14',0,13,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_15',0,14,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_16',0,15,0,27
	.byte	'IfxAsclin_RxFifoInterruptLevel',0,11,165,2,3
	.word	91946
	.byte	15,11,170,2,9,1,16
	.byte	'IfxAsclin_RxFifoOutletWidth_0',0,0,16
	.byte	'IfxAsclin_RxFifoOutletWidth_1',0,1,16
	.byte	'IfxAsclin_RxFifoOutletWidth_2',0,2,16
	.byte	'IfxAsclin_RxFifoOutletWidth_3',0,3,0,27
	.byte	'IfxAsclin_RxFifoOutletWidth',0,11,176,2,3
	.word	92560
	.byte	27
	.byte	'IfxAsclin_RxInputSelect',0,11,191,2,3
	.word	18667
	.byte	27
	.byte	'IfxAsclin_SamplePointPosition',0,11,213,2,3
	.word	20123
	.byte	27
	.byte	'IfxAsclin_SamplesPerBit',0,11,222,2,3
	.word	20646
	.byte	15,11,228,2,9,1,16
	.byte	'IfxAsclin_ShiftDirection_lsbFirst',0,0,16
	.byte	'IfxAsclin_ShiftDirection_msbFirst',0,1,0,27
	.byte	'IfxAsclin_ShiftDirection',0,11,232,2,3
	.word	92837
	.byte	15,11,238,2,9,1,16
	.byte	'IfxAsclin_SlavePolarity_idleLow',0,0,16
	.byte	'IfxAsclin_SlavePolarity_idlehigh',0,1,0,27
	.byte	'IfxAsclin_SlavePolarity',0,11,242,2,3
	.word	92950
	.byte	15,11,247,2,9,1,16
	.byte	'IfxAsclin_SleepMode_enable',0,0,16
	.byte	'IfxAsclin_SleepMode_disable',0,1,0,27
	.byte	'IfxAsclin_SleepMode',0,11,251,2,3
	.word	93059
	.byte	15,11,136,3,9,1,16
	.byte	'IfxAsclin_StopBit_0',0,0,16
	.byte	'IfxAsclin_StopBit_1',0,1,16
	.byte	'IfxAsclin_StopBit_2',0,2,16
	.byte	'IfxAsclin_StopBit_3',0,3,16
	.byte	'IfxAsclin_StopBit_4',0,4,16
	.byte	'IfxAsclin_StopBit_5',0,5,16
	.byte	'IfxAsclin_StopBit_6',0,6,16
	.byte	'IfxAsclin_StopBit_7',0,7,0,27
	.byte	'IfxAsclin_StopBit',0,11,146,3,3
	.word	93154
	.byte	15,11,150,3,9,1,16
	.byte	'IfxAsclin_SuspendMode_none',0,0,16
	.byte	'IfxAsclin_SuspendMode_hard',0,1,16
	.byte	'IfxAsclin_SuspendMode_soft',0,2,0,27
	.byte	'IfxAsclin_SuspendMode',0,11,155,3,3
	.word	93364
	.byte	15,11,160,3,9,1,16
	.byte	'IfxAsclin_TxFifoInletWidth_0',0,0,16
	.byte	'IfxAsclin_TxFifoInletWidth_1',0,1,16
	.byte	'IfxAsclin_TxFifoInletWidth_2',0,2,16
	.byte	'IfxAsclin_TxFifoInletWidth_3',0,3,0,27
	.byte	'IfxAsclin_TxFifoInletWidth',0,11,166,3,3
	.word	93489
	.byte	15,11,171,3,9,1,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_0',0,0,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_1',0,1,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_2',0,2,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_3',0,3,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_4',0,4,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_5',0,5,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_6',0,6,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_7',0,7,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_8',0,8,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_9',0,9,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_10',0,10,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_11',0,11,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_12',0,12,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_13',0,13,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_14',0,14,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_15',0,15,0,27
	.byte	'IfxAsclin_TxFifoInterruptLevel',0,11,189,3,3
	.word	93656
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L133:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,21,0,54,15,39,12,0,0,3,15,0,73,19,0,0,4,46,1,3,8,32,13
	.byte	58,15,59,15,57,15,54,15,39,12,0,0,5,5,0,3,8,58,15,59,15,57,15,73,19,0,0,6,11,0,0,0,7,36,0,3,8,11,15,62
	.byte	15,0,0,8,46,1,3,8,32,13,58,15,59,15,57,15,73,19,54,15,39,12,0,0,9,59,0,3,8,0,0,10,19,1,3,8,58,15,59,15
	.byte	57,15,11,15,0,0,11,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,12,23,1,58,15,59,15,57,15,11,15,0,0,13,13
	.byte	0,3,8,73,19,11,15,56,9,0,0,14,53,0,73,19,0,0,15,4,1,58,15,59,15,57,15,11,15,0,0,16,40,0,3,8,28,13,0,0
	.byte	17,11,1,0,0,18,1,1,11,15,73,19,0,0,19,33,0,47,15,0,0,20,46,1,3,8,73,19,54,15,39,12,63,12,60,12,0,0,21
	.byte	5,0,73,19,0,0,22,46,1,49,19,0,0,23,5,0,49,19,0,0,24,46,1,3,8,58,15,59,15,57,15,54,15,39,12,63,12,60,12
	.byte	0,0,25,46,0,3,8,58,15,59,15,57,15,73,19,54,15,39,12,63,12,60,12,0,0,26,38,0,73,19,0,0,27,22,0,3,8,58,15
	.byte	59,15,57,15,73,19,0,0,28,21,0,54,15,0,0,29,19,1,58,15,59,15,57,15,11,15,0,0,30,52,0,3,8,58,15,59,15,57
	.byte	15,73,19,63,12,60,12,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L134:
	.word	.L564-.L563
.L563:
	.half	3
	.word	.L566-.L565
.L565:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg',0
	.byte	0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_Intrinsics.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Scu\\Std\\IfxScuWdt.h',0
	.byte	0,0,0
	.byte	'IfxScu_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxCpu_cfg.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Port\\Std\\IfxPort.h',0
	.byte	0,0,0
	.byte	'IfxPort_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Src\\Std\\IfxSrc.h',0
	.byte	0,0,0
	.byte	'IfxSrc_regdef.h',0,1,0,0
	.byte	'..\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Asclin\\Std\\IfxAsclin.h',0,0,0,0
	.byte	'IfxAsclin_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Scu\\Std\\IfxScuCcu.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxAsclin_cfg.h',0
	.byte	0,0,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Asclin/Std/IfxAsclin.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Platform_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Ifx_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxScu_cfg.h',0
	.byte	0,0,0
	.byte	'IfxCpu_regdef.h',0,1,0,0
	.byte	'IfxStm_regdef.h',0,1,0,0
	.byte	'IfxFlash_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxSrc_cfg.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_PinMap\\IfxAsclin_PinMap.h',0
	.byte	0,0,0,0
.L566:
.L564:
	.sdecl	'.debug_info',debug,cluster('IfxAsclin_getAddress')
	.sect	'.debug_info'
.L135:
	.word	335
	.half	3
	.word	.L136
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Asclin/Std/IfxAsclin.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L138,.L137
	.byte	2
	.word	.L131
	.byte	3
	.byte	'IfxAsclin_getAddress',0,1,80,13
	.word	.L245
	.byte	1,1,1
	.word	.L94,.L246,.L93
	.byte	4
	.byte	'asclin',0,1,80,50
	.word	.L247,.L248
	.byte	5
	.word	.L94,.L246
	.byte	6
	.byte	'module',0,1,82,17
	.word	.L245,.L249
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin_getAddress')
	.sect	'.debug_abbrev'
.L136:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxAsclin_getAddress')
	.sect	'.debug_line'
.L137:
	.word	.L568-.L567
.L567:
	.half	3
	.word	.L570-.L569
.L569:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Asclin/Std/IfxAsclin.c',0,0,0,0,0
.L570:
	.byte	5,5,7,0,5,2
	.word	.L94
	.byte	3,211,0,1,5,54,7,9
	.half	.L571-.L94
	.byte	3,2,1,5,32,9
	.half	.L572-.L571
	.byte	1,5,54,9
	.half	.L573-.L572
	.byte	1,5,62,9
	.half	.L574-.L573
	.byte	1,5,69,9
	.half	.L440-.L574
	.byte	1,5,16,9
	.half	.L6-.L440
	.byte	3,4,1,5,5,9
	.half	.L7-.L6
	.byte	3,3,1,5,1,9
	.half	.L8-.L7
	.byte	3,1,1,7,9
	.half	.L139-.L8
	.byte	0,1,1
.L568:
	.sdecl	'.debug_ranges',debug,cluster('IfxAsclin_getAddress')
	.sect	'.debug_ranges'
.L138:
	.word	-1,.L94,0,.L139-.L94,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin_getFaFrequency')
	.sect	'.debug_info'
.L140:
	.word	402
	.half	3
	.word	.L141
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Asclin/Std/IfxAsclin.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L143,.L142
	.byte	2
	.word	.L131
	.byte	3
	.byte	'IfxAsclin_getFaFrequency',0,1,97,9
	.word	.L250
	.byte	1,1,1
	.word	.L96,.L251,.L95
	.byte	4
	.byte	'asclin',0,1,97,46
	.word	.L245,.L252
	.byte	5
	.word	.L96,.L251
	.byte	6
	.byte	'frequency',0,1,99,27
	.word	.L250,.L253
	.byte	6
	.byte	'clockSource',0,1,100,27
	.word	.L254,.L255
	.byte	7
	.word	.L256,.L96,.L9
	.byte	8
	.word	.L257,.L258
	.byte	9
	.word	.L259,.L96,.L9
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin_getFaFrequency')
	.sect	'.debug_abbrev'
.L141:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,5,0,49,16,2,6,0,0,9,11,0,49,16,17
	.byte	1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxAsclin_getFaFrequency')
	.sect	'.debug_line'
.L142:
	.word	.L576-.L575
.L575:
	.half	3
	.word	.L578-.L577
.L577:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Asclin/Std/IfxAsclin.c',0,0,0,0
	.byte	'..\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Asclin\\Std\\IfxAsclin.h',0,0,0,0,0
.L578:
	.byte	4,2,5,25,7,0,5,2
	.word	.L96
	.byte	3,141,15,1,5,5,9
	.half	.L579-.L96
	.byte	1,4,1,5,10,9
	.half	.L9-.L579
	.byte	3,218,113,1,9
	.half	.L580-.L9
	.byte	3,3,1,9
	.half	.L581-.L580
	.byte	3,3,1,9
	.half	.L582-.L581
	.byte	3,3,1,9
	.half	.L583-.L582
	.byte	3,3,1,9
	.half	.L584-.L583
	.byte	3,3,1,5,19,9
	.half	.L10-.L584
	.byte	3,114,1,5,9,9
	.half	.L442-.L10
	.byte	3,1,1,5,46,9
	.half	.L11-.L442
	.byte	3,2,1,5,9,9
	.half	.L441-.L11
	.byte	3,1,1,5,47,9
	.half	.L12-.L441
	.byte	3,2,1,5,9,9
	.half	.L443-.L12
	.byte	3,1,1,5,50,9
	.half	.L13-.L443
	.byte	3,2,1,5,9,9
	.half	.L444-.L13
	.byte	3,1,1,5,48,9
	.half	.L14-.L444
	.byte	3,2,1,5,9,9
	.half	.L445-.L14
	.byte	3,1,1,5,48,9
	.half	.L15-.L445
	.byte	3,2,1,5,9,9
	.half	.L446-.L15
	.byte	3,1,1,5,19,9
	.half	.L16-.L446
	.byte	3,2,1,5,9,9
	.half	.L447-.L16
	.byte	3,1,1,5,5,9
	.half	.L17-.L447
	.byte	3,3,1,5,1,9
	.half	.L24-.L17
	.byte	3,1,1,7,9
	.half	.L144-.L24
	.byte	0,1,1
.L576:
	.sdecl	'.debug_ranges',debug,cluster('IfxAsclin_getFaFrequency')
	.sect	'.debug_ranges'
.L143:
	.word	-1,.L96,0,.L144-.L96,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin_getIndex')
	.sect	'.debug_info'
.L145:
	.word	355
	.half	3
	.word	.L146
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Asclin/Std/IfxAsclin.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L148,.L147
	.byte	2
	.word	.L131
	.byte	3
	.byte	'IfxAsclin_getIndex',0,1,131,1,17
	.word	.L247
	.byte	1,1,1
	.word	.L98,.L260,.L97
	.byte	4
	.byte	'asclin',0,1,131,1,48
	.word	.L245,.L261
	.byte	5
	.word	.L98,.L260
	.byte	6
	.byte	'index',0,1,133,1,21
	.word	.L262,.L263
	.byte	6
	.byte	'result',0,1,134,1,21
	.word	.L247,.L264
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin_getIndex')
	.sect	'.debug_abbrev'
.L146:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxAsclin_getIndex')
	.sect	'.debug_line'
.L147:
	.word	.L586-.L585
.L585:
	.half	3
	.word	.L588-.L587
.L587:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Asclin/Std/IfxAsclin.c',0,0,0,0,0
.L588:
	.byte	5,12,7,0,5,2
	.word	.L98
	.byte	3,135,1,1,5,16,9
	.half	.L448-.L98
	.byte	3,2,1,5,50,9
	.half	.L449-.L448
	.byte	1,5,35,9
	.half	.L26-.L449
	.byte	3,2,1,5,13,9
	.half	.L589-.L26
	.byte	1,5,35,9
	.half	.L590-.L589
	.byte	1,5,42,9
	.half	.L591-.L590
	.byte	1,5,9,9
	.half	.L592-.L591
	.byte	1,5,61,7,9
	.half	.L593-.L592
	.byte	3,2,1,5,39,9
	.half	.L594-.L593
	.byte	1,5,61,9
	.half	.L595-.L594
	.byte	1,5,68,9
	.half	.L596-.L595
	.byte	1,5,22,9
	.half	.L597-.L596
	.byte	1,5,13,9
	.half	.L598-.L597
	.byte	3,1,1,5,57,9
	.half	.L27-.L598
	.byte	3,123,1,5,50,9
	.half	.L25-.L27
	.byte	1,5,5,7,9
	.half	.L28-.L25
	.byte	3,9,1,5,1,9
	.half	.L29-.L28
	.byte	3,1,1,7,9
	.half	.L149-.L29
	.byte	0,1,1
.L586:
	.sdecl	'.debug_ranges',debug,cluster('IfxAsclin_getIndex')
	.sect	'.debug_ranges'
.L148:
	.word	-1,.L98,0,.L149-.L98,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin_getOvsFrequency')
	.sect	'.debug_info'
.L150:
	.word	322
	.half	3
	.word	.L151
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Asclin/Std/IfxAsclin.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L153,.L152
	.byte	2
	.word	.L131
	.byte	3
	.byte	'IfxAsclin_getOvsFrequency',0,1,151,1,9
	.word	.L250
	.byte	1,1,1
	.word	.L100,.L265,.L99
	.byte	4
	.byte	'asclin',0,1,151,1,47
	.word	.L245,.L266
	.byte	5
	.word	.L100,.L265
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin_getOvsFrequency')
	.sect	'.debug_abbrev'
.L151:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxAsclin_getOvsFrequency')
	.sect	'.debug_line'
.L152:
	.word	.L600-.L599
.L599:
	.half	3
	.word	.L602-.L601
.L601:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Asclin/Std/IfxAsclin.c',0,0,0,0,0
.L602:
	.byte	5,9,7,0,5,2
	.word	.L100
	.byte	3,150,1,1,5,38,9
	.half	.L451-.L100
	.byte	3,2,1,5,61,9
	.half	.L450-.L451
	.byte	1,5,46,9
	.half	.L603-.L450
	.byte	1,5,88,9
	.half	.L604-.L603
	.byte	1,5,73,9
	.half	.L605-.L604
	.byte	1,5,5,9
	.half	.L606-.L605
	.byte	1,5,1,9
	.half	.L30-.L606
	.byte	3,1,1,7,9
	.half	.L154-.L30
	.byte	0,1,1
.L600:
	.sdecl	'.debug_ranges',debug,cluster('IfxAsclin_getOvsFrequency')
	.sect	'.debug_ranges'
.L153:
	.word	-1,.L100,0,.L154-.L100,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin_getPdFrequency')
	.sect	'.debug_info'
.L155:
	.word	321
	.half	3
	.word	.L156
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Asclin/Std/IfxAsclin.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L158,.L157
	.byte	2
	.word	.L131
	.byte	3
	.byte	'IfxAsclin_getPdFrequency',0,1,157,1,9
	.word	.L250
	.byte	1,1,1
	.word	.L102,.L267,.L101
	.byte	4
	.byte	'asclin',0,1,157,1,46
	.word	.L245,.L268
	.byte	5
	.word	.L102,.L267
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin_getPdFrequency')
	.sect	'.debug_abbrev'
.L156:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxAsclin_getPdFrequency')
	.sect	'.debug_line'
.L157:
	.word	.L608-.L607
.L607:
	.half	3
	.word	.L610-.L609
.L609:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Asclin/Std/IfxAsclin.c',0,0,0,0,0
.L610:
	.byte	5,9,7,0,5,2
	.word	.L102
	.byte	3,156,1,1,5,37,9
	.half	.L453-.L102
	.byte	3,2,1,5,64,9
	.half	.L452-.L453
	.byte	1,5,75,9
	.half	.L611-.L452
	.byte	1,5,45,9
	.half	.L612-.L611
	.byte	1,5,5,9
	.half	.L613-.L612
	.byte	1,5,1,9
	.half	.L31-.L613
	.byte	3,1,1,7,9
	.half	.L159-.L31
	.byte	0,1,1
.L608:
	.sdecl	'.debug_ranges',debug,cluster('IfxAsclin_getPdFrequency')
	.sect	'.debug_ranges'
.L158:
	.word	-1,.L102,0,.L159-.L102,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin_getShiftFrequency')
	.sect	'.debug_info'
.L160:
	.word	324
	.half	3
	.word	.L161
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Asclin/Std/IfxAsclin.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L163,.L162
	.byte	2
	.word	.L131
	.byte	3
	.byte	'IfxAsclin_getShiftFrequency',0,1,163,1,9
	.word	.L250
	.byte	1,1,1
	.word	.L104,.L269,.L103
	.byte	4
	.byte	'asclin',0,1,163,1,49
	.word	.L245,.L270
	.byte	5
	.word	.L104,.L269
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin_getShiftFrequency')
	.sect	'.debug_abbrev'
.L161:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxAsclin_getShiftFrequency')
	.sect	'.debug_line'
.L162:
	.word	.L615-.L614
.L614:
	.half	3
	.word	.L617-.L616
.L616:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Asclin/Std/IfxAsclin.c',0,0,0,0,0
.L617:
	.byte	5,9,7,0,5,2
	.word	.L104
	.byte	3,162,1,1,5,38,9
	.half	.L455-.L104
	.byte	3,2,1,5,64,9
	.half	.L454-.L455
	.byte	1,5,46,9
	.half	.L618-.L454
	.byte	1,5,5,9
	.half	.L619-.L618
	.byte	1,5,1,9
	.half	.L32-.L619
	.byte	3,1,1,7,9
	.half	.L164-.L32
	.byte	0,1,1
.L615:
	.sdecl	'.debug_ranges',debug,cluster('IfxAsclin_getShiftFrequency')
	.sect	'.debug_ranges'
.L163:
	.word	-1,.L104,0,.L164-.L104,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin_resetModule')
	.sect	'.debug_info'
.L165:
	.word	335
	.half	3
	.word	.L166
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Asclin/Std/IfxAsclin.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L168,.L167
	.byte	2
	.word	.L131
	.byte	3
	.byte	'IfxAsclin_resetModule',0,1,229,1,6,1,1,1
	.word	.L118,.L271,.L117
	.byte	4
	.byte	'asclin',0,1,229,1,40
	.word	.L245,.L272
	.byte	5
	.word	.L118,.L271
	.byte	6
	.byte	'passwd',0,1,231,1,12
	.word	.L273,.L274
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin_resetModule')
	.sect	'.debug_abbrev'
.L166:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxAsclin_resetModule')
	.sect	'.debug_line'
.L167:
	.word	.L621-.L620
.L620:
	.half	3
	.word	.L623-.L622
.L622:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Asclin/Std/IfxAsclin.c',0,0,0,0,0
.L623:
	.byte	5,6,7,0,5,2
	.word	.L118
	.byte	3,228,1,1,5,53,9
	.half	.L466-.L118
	.byte	3,2,1,5,19,9
	.half	.L465-.L466
	.byte	1,5,31,9
	.half	.L468-.L465
	.byte	3,1,1,5,20,9
	.half	.L469-.L468
	.byte	3,2,1,5,25,9
	.half	.L624-.L469
	.byte	1,5,20,9
	.half	.L625-.L624
	.byte	3,1,1,5,25,9
	.half	.L626-.L625
	.byte	1,5,29,9
	.half	.L627-.L626
	.byte	3,1,1,5,40,9
	.half	.L471-.L627
	.byte	3,2,1,5,32,9
	.half	.L45-.L471
	.byte	1,5,40,9
	.half	.L628-.L45
	.byte	1,5,31,7,9
	.half	.L629-.L628
	.byte	3,3,1,5,22,9
	.half	.L473-.L629
	.byte	3,1,1,5,27,9
	.half	.L630-.L473
	.byte	1,5,29,9
	.half	.L631-.L630
	.byte	3,2,1,5,1,9
	.half	.L475-.L631
	.byte	3,1,1,7,9
	.half	.L169-.L475
	.byte	0,1,1
.L621:
	.sdecl	'.debug_ranges',debug,cluster('IfxAsclin_resetModule')
	.sect	'.debug_ranges'
.L168:
	.word	-1,.L118,0,.L169-.L118,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin_setBitTiming')
	.sect	'.debug_info'
.L170:
	.word	790
	.half	3
	.word	.L171
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Asclin/Std/IfxAsclin.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L173,.L172
	.byte	2
	.word	.L131
	.byte	3
	.byte	'IfxAsclin_setBitTiming',0,1,132,2,9
	.word	.L275
	.byte	1,1,1
	.word	.L122,.L276,.L121
	.byte	4
	.byte	'asclin',0,1,132,2,44
	.word	.L245,.L277
	.byte	4
	.byte	'baudrate',0,1,132,2,60
	.word	.L250,.L278
	.byte	4
	.byte	'oversampling',0,1,132,2,99
	.word	.L279,.L280
	.byte	4
	.byte	'samplepoint',0,1,132,2,143,1
	.word	.L281,.L282
	.byte	4
	.byte	'medianFilter',0,1,132,2,180,1
	.word	.L283,.L284
	.byte	5
	.word	.L122,.L276
	.byte	6
	.byte	'source',0,1,134,2,27
	.word	.L254,.L285
	.byte	6
	.byte	'fOvs',0,1,135,2,27
	.word	.L250,.L286
	.byte	6
	.byte	'd',0,1,136,2,27
	.word	.L262,.L287
	.byte	6
	.byte	'n',0,1,136,2,39
	.word	.L262,.L288
	.byte	6
	.byte	'dBest',0,1,136,2,42
	.word	.L262,.L289
	.byte	6
	.byte	'nBest',0,1,136,2,53
	.word	.L262,.L290
	.byte	6
	.byte	'f',0,1,137,2,27
	.word	.L250,.L291
	.byte	6
	.byte	'fpd',0,1,140,2,27
	.word	.L250,.L292
	.byte	7
	.word	.L256,.L293,.L48
	.byte	8
	.word	.L257,.L294
	.byte	9
	.word	.L259,.L293,.L48
	.byte	0,5
	.word	.L295,.L276
	.byte	6
	.byte	'relError',0,1,144,2,27
	.word	.L250,.L296
	.byte	6
	.byte	'limit',0,1,145,2,27
	.word	.L250,.L297
	.byte	6
	.byte	'terminated',0,1,147,2,27
	.word	.L275,.L298
	.byte	6
	.byte	'newRelError',0,1,148,2,27
	.word	.L250,.L299
	.byte	6
	.byte	'adder_facL',0,1,149,2,27
	.word	.L262,.L300
	.byte	6
	.byte	'adder_facH',0,1,149,2,39
	.word	.L262,.L301
	.byte	6
	.byte	'adder_facL_min',0,1,149,2,51
	.word	.L262,.L302
	.byte	6
	.byte	'count',0,1,149,2,67
	.word	.L262,.L303
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin_setBitTiming')
	.sect	'.debug_abbrev'
.L171:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,5,0,49,16,2,6,0,0,9,11,0,49,16,17
	.byte	1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxAsclin_setBitTiming')
	.sect	'.debug_line'
.L172:
	.word	.L633-.L632
.L632:
	.half	3
	.word	.L635-.L634
.L634:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Asclin/Std/IfxAsclin.c',0,0,0,0
	.byte	'..\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Asclin\\Std\\IfxAsclin.h',0,0,0,0,0
.L635:
	.byte	5,9,7,0,5,2
	.word	.L122
	.byte	3,131,2,1,4,2,5,25,9
	.half	.L293-.L122
	.byte	3,138,13,1,5,5,9
	.half	.L636-.L293
	.byte	1,4,1,5,58,9
	.half	.L48-.L636
	.byte	3,254,114,1,5,31,9
	.half	.L487-.L48
	.byte	1,5,71,9
	.half	.L491-.L487
	.byte	3,1,1,5,56,9
	.half	.L637-.L491
	.byte	1,5,20,9
	.half	.L638-.L637
	.byte	1,5,57,9
	.half	.L494-.L638
	.byte	3,1,1,5,20,9
	.half	.L493-.L494
	.byte	1,5,31,9
	.half	.L497-.L493
	.byte	3,1,1,5,29,9
	.half	.L498-.L497
	.byte	1,5,48,9
	.half	.L295-.L498
	.byte	3,2,1,5,40,9
	.half	.L639-.L295
	.byte	1,5,46,9
	.half	.L640-.L639
	.byte	1,5,38,9
	.half	.L500-.L640
	.byte	1,9
	.half	.L501-.L500
	.byte	3,2,1,5,22,9
	.half	.L504-.L501
	.byte	3,4,1,5,9,9
	.half	.L505-.L504
	.byte	1,5,7,9
	.half	.L508-.L505
	.byte	3,1,1,5,11,9
	.half	.L509-.L508
	.byte	3,2,1,5,5,9
	.half	.L507-.L509
	.byte	1,5,41,7,9
	.half	.L641-.L507
	.byte	3,2,1,5,39,9
	.half	.L642-.L641
	.byte	1,5,27,9
	.half	.L643-.L642
	.byte	1,5,14,9
	.half	.L512-.L643
	.byte	1,5,47,9
	.half	.L644-.L512
	.byte	1,5,9,9
	.half	.L645-.L644
	.byte	1,5,57,7,9
	.half	.L646-.L645
	.byte	3,3,1,5,20,9
	.half	.L49-.L646
	.byte	3,9,1,9
	.half	.L515-.L49
	.byte	3,1,1,9
	.half	.L517-.L515
	.byte	3,1,1,5,29,9
	.half	.L519-.L517
	.byte	3,1,1,5,27,9
	.half	.L510-.L519
	.byte	1,5,34,9
	.half	.L520-.L510
	.byte	1,5,32,9
	.half	.L522-.L520
	.byte	1,5,22,9
	.half	.L523-.L522
	.byte	3,1,1,5,18,9
	.half	.L53-.L523
	.byte	3,2,1,5,5,9
	.half	.L647-.L53
	.byte	1,5,20,7,9
	.half	.L648-.L647
	.byte	3,2,1,5,12,9
	.half	.L54-.L648
	.byte	3,3,1,5,54,9
	.half	.L529-.L54
	.byte	1,5,9,9
	.half	.L56-.L529
	.byte	3,2,1,5,24,7,9
	.half	.L649-.L56
	.byte	3,2,1,9
	.half	.L530-.L649
	.byte	3,1,1,5,27,9
	.half	.L531-.L530
	.byte	3,127,1,5,42,9
	.half	.L57-.L531
	.byte	3,5,1,5,47,9
	.half	.L650-.L57
	.byte	1,5,37,9
	.half	.L534-.L650
	.byte	3,1,1,5,53,9
	.half	.L58-.L534
	.byte	3,3,1,5,34,9
	.half	.L60-.L58
	.byte	3,2,1,5,32,9
	.half	.L651-.L60
	.byte	1,5,46,9
	.half	.L536-.L651
	.byte	1,5,37,9
	.half	.L652-.L536
	.byte	1,5,27,9
	.half	.L539-.L652
	.byte	3,1,1,5,26,9
	.half	.L62-.L539
	.byte	3,2,1,5,13,9
	.half	.L653-.L62
	.byte	1,5,32,7,9
	.half	.L654-.L653
	.byte	3,2,1,9
	.half	.L655-.L654
	.byte	3,1,1,5,41,9
	.half	.L656-.L655
	.byte	3,1,1,5,32,9
	.half	.L657-.L656
	.byte	3,1,1,5,60,9
	.half	.L63-.L657
	.byte	3,118,1,5,53,9
	.half	.L59-.L63
	.byte	1,5,22,7,9
	.half	.L658-.L59
	.byte	3,14,1,5,9,9
	.half	.L659-.L658
	.byte	1,5,13,7,9
	.half	.L660-.L659
	.byte	3,2,1,5,57,9
	.half	.L64-.L660
	.byte	3,99,1,5,20,9
	.half	.L55-.L64
	.byte	1,5,39,9
	.half	.L661-.L55
	.byte	1,5,47,9
	.half	.L546-.L661
	.byte	1,5,44,9
	.half	.L662-.L546
	.byte	1,5,38,7,9
	.half	.L65-.L662
	.byte	3,33,1,5,18,9
	.half	.L548-.L65
	.byte	3,1,1,5,33,9
	.half	.L663-.L548
	.byte	1,5,31,9
	.half	.L664-.L663
	.byte	1,5,18,9
	.half	.L665-.L664
	.byte	3,1,1,5,33,9
	.half	.L666-.L665
	.byte	1,5,31,9
	.half	.L667-.L666
	.byte	1,5,21,9
	.half	.L668-.L667
	.byte	3,3,1,5,50,9
	.half	.L669-.L668
	.byte	1,5,35,9
	.half	.L670-.L669
	.byte	1,5,21,9
	.half	.L671-.L670
	.byte	3,3,1,5,34,9
	.half	.L672-.L671
	.byte	1,5,27,9
	.half	.L673-.L672
	.byte	3,3,1,5,40,9
	.half	.L674-.L673
	.byte	1,5,44,9
	.half	.L554-.L674
	.byte	1,5,40,9
	.half	.L67-.L554
	.byte	1,5,21,9
	.half	.L68-.L67
	.byte	1,5,25,9
	.half	.L675-.L68
	.byte	1,5,38,9
	.half	.L676-.L675
	.byte	3,2,1,5,12,9
	.half	.L556-.L676
	.byte	3,2,1,5,5,9
	.half	.L677-.L556
	.byte	1,5,1,9
	.half	.L69-.L677
	.byte	3,1,1,7,9
	.half	.L174-.L69
	.byte	0,1,1
.L633:
	.sdecl	'.debug_ranges',debug,cluster('IfxAsclin_setBitTiming')
	.sect	'.debug_ranges'
.L173:
	.word	-1,.L122,0,.L174-.L122,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin_disableModule')
	.sect	'.debug_info'
.L175:
	.word	367
	.half	3
	.word	.L176
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Asclin/Std/IfxAsclin.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L178,.L177
	.byte	2
	.word	.L131
	.byte	3
	.byte	'IfxAsclin_disableModule',0,1,55,6,1,1,1
	.word	.L88,.L304,.L87
	.byte	4
	.byte	'asclin',0,1,55,42
	.word	.L245,.L305
	.byte	5
	.word	.L88,.L304
	.byte	6
	.byte	'psw',0,1,57,12
	.word	.L273,.L306
	.byte	7
	.word	.L307,.L308,.L309
	.byte	8
	.word	.L310,.L311
	.byte	9
	.word	.L312,.L308,.L309
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin_disableModule')
	.sect	'.debug_abbrev'
.L176:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,5,0,49,16,2,6,0,0,9,11,0,49,16,17,1,18
	.byte	1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxAsclin_disableModule')
	.sect	'.debug_line'
.L177:
	.word	.L679-.L678
.L678:
	.half	3
	.word	.L681-.L680
.L680:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Asclin/Std/IfxAsclin.c',0,0,0,0
	.byte	'..\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Asclin\\Std\\IfxAsclin.h',0,0,0,0,0
.L681:
	.byte	5,6,7,0,5,2
	.word	.L88
	.byte	3,54,1,5,50,9
	.half	.L429-.L88
	.byte	3,2,1,5,16,9
	.half	.L428-.L429
	.byte	1,5,31,9
	.half	.L431-.L428
	.byte	3,1,1,4,2,5,18,9
	.half	.L308-.L431
	.byte	3,131,17,1,5,24,9
	.half	.L682-.L308
	.byte	1,4,1,5,29,9
	.half	.L309-.L682
	.byte	3,255,110,1,5,1,9
	.half	.L433-.L309
	.byte	3,1,1,7,9
	.half	.L179-.L433
	.byte	0,1,1
.L679:
	.sdecl	'.debug_ranges',debug,cluster('IfxAsclin_disableModule')
	.sect	'.debug_ranges'
.L178:
	.word	-1,.L88,0,.L179-.L88,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin_enableAscErrorFlags')
	.sect	'.debug_info'
.L180:
	.word	455
	.half	3
	.word	.L181
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Asclin/Std/IfxAsclin.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L183,.L182
	.byte	2
	.word	.L131
	.byte	3
	.byte	'IfxAsclin_enableAscErrorFlags',0,1,64,6,1,1,1
	.word	.L90,.L313,.L89
	.byte	4
	.byte	'asclin',0,1,64,48
	.word	.L245,.L314
	.byte	4
	.byte	'parEnable',0,1,64,64
	.word	.L275,.L315
	.byte	4
	.byte	'rfoEnable',0,1,64,83
	.word	.L275,.L316
	.byte	5
	.word	.L90,.L313
	.byte	6
	.word	.L317,.L90,.L318
	.byte	7
	.word	.L319,.L320
	.byte	7
	.word	.L321,.L322
	.byte	8
	.word	.L323,.L90,.L318
	.byte	0,6
	.word	.L324,.L318,.L325
	.byte	7
	.word	.L326,.L327
	.byte	7
	.word	.L328,.L329
	.byte	8
	.word	.L330,.L318,.L325
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin_enableAscErrorFlags')
	.sect	'.debug_abbrev'
.L181:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,29,1,49
	.byte	16,17,1,18,1,0,0,7,5,0,49,16,2,6,0,0,8,11,0,49,16,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxAsclin_enableAscErrorFlags')
	.sect	'.debug_line'
.L182:
	.word	.L684-.L683
.L683:
	.half	3
	.word	.L686-.L685
.L685:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Asclin/Std/IfxAsclin.c',0,0,0,0
	.byte	'..\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Asclin\\Std\\IfxAsclin.h',0,0,0,0,0
.L686:
	.byte	4,2,5,33,7,0,5,2
	.word	.L90
	.byte	3,161,14,1,5,40,7,9
	.half	.L687-.L90
	.byte	1,5,44,9
	.half	.L688-.L687
	.byte	1,5,40,9
	.half	.L2-.L688
	.byte	1,5,26,9
	.half	.L3-.L2
	.byte	1,5,31,9
	.half	.L689-.L3
	.byte	1,5,34,9
	.half	.L318-.L689
	.byte	3,30,1,5,41,7,9
	.half	.L690-.L318
	.byte	1,5,45,9
	.half	.L691-.L690
	.byte	1,5,41,9
	.half	.L4-.L691
	.byte	1,5,26,9
	.half	.L5-.L4
	.byte	1,5,32,9
	.half	.L692-.L5
	.byte	1,4,1,5,1,9
	.half	.L325-.L692
	.byte	3,132,114,1,7,9
	.half	.L184-.L325
	.byte	0,1,1
.L684:
	.sdecl	'.debug_ranges',debug,cluster('IfxAsclin_enableAscErrorFlags')
	.sect	'.debug_ranges'
.L183:
	.word	-1,.L90,0,.L184-.L90,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin_enableModule')
	.sect	'.debug_info'
.L185:
	.word	366
	.half	3
	.word	.L186
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Asclin/Std/IfxAsclin.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L188,.L187
	.byte	2
	.word	.L131
	.byte	3
	.byte	'IfxAsclin_enableModule',0,1,71,6,1,1,1
	.word	.L92,.L331,.L91
	.byte	4
	.byte	'asclin',0,1,71,41
	.word	.L245,.L332
	.byte	5
	.word	.L92,.L331
	.byte	6
	.byte	'psw',0,1,73,12
	.word	.L273,.L333
	.byte	7
	.word	.L334,.L335,.L336
	.byte	8
	.word	.L337,.L338
	.byte	9
	.word	.L339,.L335,.L336
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin_enableModule')
	.sect	'.debug_abbrev'
.L186:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,5,0,49,16,2,6,0,0,9,11,0,49,16,17,1,18
	.byte	1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxAsclin_enableModule')
	.sect	'.debug_line'
.L187:
	.word	.L694-.L693
.L693:
	.half	3
	.word	.L696-.L695
.L695:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Asclin/Std/IfxAsclin.c',0,0,0,0
	.byte	'..\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Asclin\\Std\\IfxAsclin.h',0,0,0,0,0
.L696:
	.byte	5,6,7,0,5,2
	.word	.L92
	.byte	3,198,0,1,5,50,9
	.half	.L435-.L92
	.byte	3,2,1,5,16,9
	.half	.L434-.L435
	.byte	1,5,31,9
	.half	.L437-.L434
	.byte	3,1,1,4,2,5,18,9
	.half	.L335-.L437
	.byte	3,249,16,1,5,24,9
	.half	.L697-.L335
	.byte	1,4,1,5,29,9
	.half	.L336-.L697
	.byte	3,137,111,1,5,1,9
	.half	.L439-.L336
	.byte	3,1,1,7,9
	.half	.L189-.L439
	.byte	0,1,1
.L694:
	.sdecl	'.debug_ranges',debug,cluster('IfxAsclin_enableModule')
	.sect	'.debug_ranges'
.L188:
	.word	-1,.L92,0,.L189-.L92,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin_getSrcPointerEr')
	.sect	'.debug_info'
.L190:
	.word	322
	.half	3
	.word	.L191
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Asclin/Std/IfxAsclin.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L193,.L192
	.byte	2
	.word	.L131
	.byte	3
	.byte	'IfxAsclin_getSrcPointerEr',0,1,169,1,24
	.word	.L340
	.byte	1,1,1
	.word	.L106,.L341,.L105
	.byte	4
	.byte	'asclin',0,1,169,1,62
	.word	.L245,.L342
	.byte	5
	.word	.L106,.L341
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin_getSrcPointerEr')
	.sect	'.debug_abbrev'
.L191:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxAsclin_getSrcPointerEr')
	.sect	'.debug_line'
.L192:
	.word	.L699-.L698
.L698:
	.half	3
	.word	.L701-.L700
.L700:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Asclin/Std/IfxAsclin.c',0,0,0,0,0
.L701:
	.byte	5,83,7,0,5,2
	.word	.L106
	.byte	3,170,1,1,5,63,9
	.half	.L456-.L106
	.byte	1,5,91,9
	.half	.L702-.L456
	.byte	1,5,5,9
	.half	.L703-.L702
	.byte	1,5,1,9
	.half	.L33-.L703
	.byte	3,1,1,7,9
	.half	.L194-.L33
	.byte	0,1,1
.L699:
	.sdecl	'.debug_ranges',debug,cluster('IfxAsclin_getSrcPointerEr')
	.sect	'.debug_ranges'
.L193:
	.word	-1,.L106,0,.L194-.L106,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin_getSrcPointerRx')
	.sect	'.debug_info'
.L195:
	.word	322
	.half	3
	.word	.L196
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Asclin/Std/IfxAsclin.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L198,.L197
	.byte	2
	.word	.L131
	.byte	3
	.byte	'IfxAsclin_getSrcPointerRx',0,1,175,1,24
	.word	.L340
	.byte	1,1,1
	.word	.L108,.L343,.L107
	.byte	4
	.byte	'asclin',0,1,175,1,62
	.word	.L245,.L344
	.byte	5
	.word	.L108,.L343
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin_getSrcPointerRx')
	.sect	'.debug_abbrev'
.L196:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxAsclin_getSrcPointerRx')
	.sect	'.debug_line'
.L197:
	.word	.L705-.L704
.L704:
	.half	3
	.word	.L707-.L706
.L706:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Asclin/Std/IfxAsclin.c',0,0,0,0,0
.L707:
	.byte	5,83,7,0,5,2
	.word	.L108
	.byte	3,176,1,1,5,63,9
	.half	.L457-.L108
	.byte	1,5,91,9
	.half	.L708-.L457
	.byte	1,5,5,9
	.half	.L709-.L708
	.byte	1,5,1,9
	.half	.L34-.L709
	.byte	3,1,1,7,9
	.half	.L199-.L34
	.byte	0,1,1
.L705:
	.sdecl	'.debug_ranges',debug,cluster('IfxAsclin_getSrcPointerRx')
	.sect	'.debug_ranges'
.L198:
	.word	-1,.L108,0,.L199-.L108,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin_getSrcPointerTx')
	.sect	'.debug_info'
.L200:
	.word	322
	.half	3
	.word	.L201
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Asclin/Std/IfxAsclin.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L203,.L202
	.byte	2
	.word	.L131
	.byte	3
	.byte	'IfxAsclin_getSrcPointerTx',0,1,181,1,24
	.word	.L340
	.byte	1,1,1
	.word	.L110,.L345,.L109
	.byte	4
	.byte	'asclin',0,1,181,1,62
	.word	.L245,.L346
	.byte	5
	.word	.L110,.L345
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin_getSrcPointerTx')
	.sect	'.debug_abbrev'
.L201:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxAsclin_getSrcPointerTx')
	.sect	'.debug_line'
.L202:
	.word	.L711-.L710
.L710:
	.half	3
	.word	.L713-.L712
.L712:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Asclin/Std/IfxAsclin.c',0,0,0,0,0
.L713:
	.byte	5,83,7,0,5,2
	.word	.L110
	.byte	3,182,1,1,5,63,9
	.half	.L458-.L110
	.byte	1,5,5,9
	.half	.L714-.L458
	.byte	1,5,1,9
	.half	.L35-.L714
	.byte	3,1,1,7,9
	.half	.L204-.L35
	.byte	0,1,1
.L711:
	.sdecl	'.debug_ranges',debug,cluster('IfxAsclin_getSrcPointerTx')
	.sect	'.debug_ranges'
.L203:
	.word	-1,.L110,0,.L204-.L110,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin_read16')
	.sect	'.debug_info'
.L205:
	.word	371
	.half	3
	.word	.L206
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Asclin/Std/IfxAsclin.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L208,.L207
	.byte	2
	.word	.L131
	.byte	3
	.byte	'IfxAsclin_read16',0,1,187,1,8
	.word	.L262
	.byte	1,1,1
	.word	.L112,.L347,.L111
	.byte	4
	.byte	'asclin',0,1,187,1,37
	.word	.L245,.L348
	.byte	4
	.byte	'data',0,1,187,1,53
	.word	.L349,.L350
	.byte	4
	.byte	'count',0,1,187,1,66
	.word	.L262,.L351
	.byte	5
	.word	.L112,.L347
	.byte	6
	.byte	'rxData',0,1,189,1,33
	.word	.L352,.L353
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin_read16')
	.sect	'.debug_abbrev'
.L206:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxAsclin_read16')
	.sect	'.debug_line'
.L207:
	.word	.L716-.L715
.L715:
	.half	3
	.word	.L718-.L717
.L717:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Asclin/Std/IfxAsclin.c',0,0,0,0,0
.L718:
	.byte	5,79,7,0,5,2
	.word	.L112
	.byte	3,188,1,1,5,21,9
	.half	.L459-.L112
	.byte	3,2,1,5,33,9
	.half	.L37-.L459
	.byte	3,2,1,5,17,9
	.half	.L719-.L37
	.byte	1,5,14,9
	.half	.L720-.L719
	.byte	1,9
	.half	.L721-.L720
	.byte	3,1,1,5,21,9
	.half	.L36-.L721
	.byte	3,125,1,5,5,7,9
	.half	.L722-.L36
	.byte	3,6,1,5,1,9
	.half	.L38-.L722
	.byte	3,1,1,7,9
	.half	.L209-.L38
	.byte	0,1,1
.L716:
	.sdecl	'.debug_ranges',debug,cluster('IfxAsclin_read16')
	.sect	'.debug_ranges'
.L208:
	.word	-1,.L112,0,.L209-.L112,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin_read32')
	.sect	'.debug_info'
.L210:
	.word	371
	.half	3
	.word	.L211
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Asclin/Std/IfxAsclin.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L213,.L212
	.byte	2
	.word	.L131
	.byte	3
	.byte	'IfxAsclin_read32',0,1,201,1,8
	.word	.L262
	.byte	1,1,1
	.word	.L114,.L354,.L113
	.byte	4
	.byte	'asclin',0,1,201,1,37
	.word	.L245,.L355
	.byte	4
	.byte	'data',0,1,201,1,53
	.word	.L356,.L357
	.byte	4
	.byte	'count',0,1,201,1,66
	.word	.L262,.L358
	.byte	5
	.word	.L114,.L354
	.byte	6
	.byte	'rxData',0,1,203,1,33
	.word	.L352,.L359
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin_read32')
	.sect	'.debug_abbrev'
.L211:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxAsclin_read32')
	.sect	'.debug_line'
.L212:
	.word	.L724-.L723
.L723:
	.half	3
	.word	.L726-.L725
.L725:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Asclin/Std/IfxAsclin.c',0,0,0,0,0
.L726:
	.byte	5,79,7,0,5,2
	.word	.L114
	.byte	3,202,1,1,5,21,9
	.half	.L461-.L114
	.byte	3,2,1,5,25,9
	.half	.L40-.L461
	.byte	3,2,1,5,17,9
	.half	.L727-.L40
	.byte	1,5,14,9
	.half	.L728-.L727
	.byte	1,9
	.half	.L729-.L728
	.byte	3,1,1,5,21,9
	.half	.L39-.L729
	.byte	3,125,1,5,5,7,9
	.half	.L730-.L39
	.byte	3,6,1,5,1,9
	.half	.L41-.L730
	.byte	3,1,1,7,9
	.half	.L214-.L41
	.byte	0,1,1
.L724:
	.sdecl	'.debug_ranges',debug,cluster('IfxAsclin_read32')
	.sect	'.debug_ranges'
.L213:
	.word	-1,.L114,0,.L214-.L114,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin_read8')
	.sect	'.debug_info'
.L215:
	.word	370
	.half	3
	.word	.L216
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Asclin/Std/IfxAsclin.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L218,.L217
	.byte	2
	.word	.L131
	.byte	3
	.byte	'IfxAsclin_read8',0,1,215,1,8
	.word	.L262
	.byte	1,1,1
	.word	.L116,.L360,.L115
	.byte	4
	.byte	'asclin',0,1,215,1,36
	.word	.L245,.L361
	.byte	4
	.byte	'data',0,1,215,1,51
	.word	.L362,.L363
	.byte	4
	.byte	'count',0,1,215,1,64
	.word	.L262,.L364
	.byte	5
	.word	.L116,.L360
	.byte	6
	.byte	'rxData',0,1,217,1,33
	.word	.L352,.L365
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin_read8')
	.sect	'.debug_abbrev'
.L216:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxAsclin_read8')
	.sect	'.debug_line'
.L217:
	.word	.L732-.L731
.L731:
	.half	3
	.word	.L734-.L733
.L733:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Asclin/Std/IfxAsclin.c',0,0,0,0,0
.L734:
	.byte	5,79,7,0,5,2
	.word	.L116
	.byte	3,216,1,1,5,21,9
	.half	.L463-.L116
	.byte	3,2,1,5,32,9
	.half	.L43-.L463
	.byte	3,2,1,5,17,9
	.half	.L735-.L43
	.byte	1,5,14,9
	.half	.L736-.L735
	.byte	1,9
	.half	.L737-.L736
	.byte	3,1,1,5,21,9
	.half	.L42-.L737
	.byte	3,125,1,5,5,7,9
	.half	.L738-.L42
	.byte	3,6,1,5,1,9
	.half	.L44-.L738
	.byte	3,1,1,7,9
	.half	.L219-.L44
	.byte	0,1,1
.L732:
	.sdecl	'.debug_ranges',debug,cluster('IfxAsclin_read8')
	.sect	'.debug_ranges'
.L218:
	.word	-1,.L116,0,.L219-.L116,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin_setBaudrateBitFields')
	.sect	'.debug_info'
.L220:
	.word	663
	.half	3
	.word	.L221
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Asclin/Std/IfxAsclin.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L223,.L222
	.byte	2
	.word	.L131
	.byte	3
	.byte	'IfxAsclin_setBaudrateBitFields',0,1,248,1,6,1,1,1
	.word	.L120,.L366,.L119
	.byte	4
	.byte	'asclin',0,1,248,1,49
	.word	.L245,.L367
	.byte	4
	.byte	'prescaler',0,1,248,1,64
	.word	.L273,.L368
	.byte	4
	.byte	'numerator',0,1,248,1,82
	.word	.L273,.L369
	.byte	4
	.byte	'denominator',0,1,248,1,100
	.word	.L273,.L370
	.byte	4
	.byte	'oversampling',0,1,248,1,142,1
	.word	.L279,.L371
	.byte	5
	.word	.L120,.L366
	.byte	6
	.byte	'clockSource',0,1,250,1,27
	.word	.L254,.L372
	.byte	7
	.word	.L256,.L373,.L47
	.byte	8
	.word	.L257,.L374
	.byte	9
	.word	.L259,.L373,.L47
	.byte	0,7
	.word	.L375,.L376,.L377
	.byte	8
	.word	.L378,.L379
	.byte	8
	.word	.L380,.L381
	.byte	9
	.word	.L382,.L376,.L377
	.byte	0,7
	.word	.L383,.L377,.L384
	.byte	8
	.word	.L385,.L386
	.byte	8
	.word	.L387,.L388
	.byte	9
	.word	.L389,.L377,.L384
	.byte	0,7
	.word	.L390,.L384,.L391
	.byte	8
	.word	.L392,.L393
	.byte	8
	.word	.L394,.L395
	.byte	9
	.word	.L396,.L384,.L391
	.byte	0,7
	.word	.L397,.L391,.L398
	.byte	8
	.word	.L399,.L400
	.byte	8
	.word	.L401,.L402
	.byte	9
	.word	.L403,.L391,.L398
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin_setBaudrateBitFields')
	.sect	'.debug_abbrev'
.L221:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,5,0,49,16,2,6,0,0,9,11,0,49,16,17,1,18
	.byte	1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxAsclin_setBaudrateBitFields')
	.sect	'.debug_line'
.L222:
	.word	.L740-.L739
.L739:
	.half	3
	.word	.L742-.L741
.L741:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Asclin/Std/IfxAsclin.c',0,0,0,0
	.byte	'..\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Asclin\\Std\\IfxAsclin.h',0,0,0,0,0
.L742:
	.byte	5,6,7,0,5,2
	.word	.L120
	.byte	3,247,1,1,4,2,5,25,9
	.half	.L373-.L120
	.byte	3,150,13,1,5,5,9
	.half	.L743-.L373
	.byte	1,4,1,5,38,9
	.half	.L47-.L743
	.byte	3,237,114,1,4,2,5,21,9
	.half	.L376-.L47
	.byte	3,168,16,1,5,44,9
	.half	.L744-.L376
	.byte	1,5,32,9
	.half	.L745-.L744
	.byte	1,5,18,9
	.half	.L377-.L745
	.byte	3,110,1,5,29,9
	.half	.L478-.L377
	.byte	1,5,18,9
	.half	.L384-.L478
	.byte	3,166,127,1,5,31,9
	.half	.L480-.L384
	.byte	1,5,21,9
	.half	.L391-.L480
	.byte	3,224,0,1,5,35,9
	.half	.L482-.L391
	.byte	1,4,1,5,38,9
	.half	.L398-.L482
	.byte	3,233,111,1,5,1,9
	.half	.L485-.L398
	.byte	3,1,1,7,9
	.half	.L224-.L485
	.byte	0,1,1
.L740:
	.sdecl	'.debug_ranges',debug,cluster('IfxAsclin_setBaudrateBitFields')
	.sect	'.debug_ranges'
.L223:
	.word	-1,.L120,0,.L224-.L120,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin_setClockSource')
	.sect	'.debug_info'
.L225:
	.word	415
	.half	3
	.word	.L226
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Asclin/Std/IfxAsclin.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L228,.L227
	.byte	2
	.word	.L131
	.byte	3
	.byte	'IfxAsclin_setClockSource',0,1,231,2,6,1,1,1
	.word	.L124,.L404,.L123
	.byte	4
	.byte	'asclin',0,1,231,2,43
	.word	.L245,.L405
	.byte	4
	.byte	'clockSource',0,1,231,2,73
	.word	.L254,.L406
	.byte	5
	.word	.L124,.L404
	.byte	6
	.word	.L407,.L71,.L73
	.byte	7
	.word	.L408,.L409
	.byte	8
	.word	.L410,.L71,.L73
	.byte	0,6
	.word	.L407,.L75,.L77
	.byte	7
	.word	.L408,.L409
	.byte	8
	.word	.L410,.L75,.L77
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin_setClockSource')
	.sect	'.debug_abbrev'
.L226:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,29,1,49
	.byte	16,17,1,18,1,0,0,7,5,0,49,16,2,6,0,0,8,11,0,49,16,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxAsclin_setClockSource')
	.sect	'.debug_line'
.L227:
	.word	.L747-.L746
.L746:
	.half	3
	.word	.L749-.L748
.L748:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Asclin/Std/IfxAsclin.c',0,0,0,0
	.byte	'..\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Asclin\\Std\\IfxAsclin.h',0,0,0,0,0
.L749:
	.byte	5,18,7,0,5,2
	.word	.L124
	.byte	3,232,2,1,5,26,9
	.half	.L750-.L124
	.byte	1,5,5,9
	.half	.L751-.L750
	.byte	3,3,1,5,54,7,9
	.half	.L752-.L751
	.byte	3,2,1,4,2,5,25,9
	.half	.L71-.L752
	.byte	3,166,12,1,5,5,9
	.half	.L753-.L71
	.byte	1,4,1,5,54,9
	.half	.L73-.L753
	.byte	3,218,115,1,7,9
	.half	.L754-.L73
	.byte	1,9
	.half	.L70-.L754
	.byte	3,5,1,4,2,5,25,9
	.half	.L75-.L70
	.byte	3,161,12,1,5,5,9
	.half	.L755-.L75
	.byte	1,4,1,5,54,9
	.half	.L77-.L755
	.byte	3,223,115,1,5,1,7,9
	.half	.L74-.L77
	.byte	3,3,1,7,9
	.half	.L229-.L74
	.byte	0,1,1
.L747:
	.sdecl	'.debug_ranges',debug,cluster('IfxAsclin_setClockSource')
	.sect	'.debug_ranges'
.L228:
	.word	-1,.L124,0,.L229-.L124,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin_write16')
	.sect	'.debug_info'
.L230:
	.word	372
	.half	3
	.word	.L231
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Asclin/Std/IfxAsclin.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L233,.L232
	.byte	2
	.word	.L131
	.byte	3
	.byte	'IfxAsclin_write16',0,1,249,2,8
	.word	.L262
	.byte	1,1,1
	.word	.L126,.L411,.L125
	.byte	4
	.byte	'asclin',0,1,249,2,38
	.word	.L245,.L412
	.byte	4
	.byte	'data',0,1,249,2,54
	.word	.L349,.L413
	.byte	4
	.byte	'count',0,1,249,2,67
	.word	.L262,.L414
	.byte	5
	.word	.L126,.L411
	.byte	6
	.byte	'txData',0,1,251,2,33
	.word	.L415,.L416
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin_write16')
	.sect	'.debug_abbrev'
.L231:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxAsclin_write16')
	.sect	'.debug_line'
.L232:
	.word	.L757-.L756
.L756:
	.half	3
	.word	.L759-.L758
.L758:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Asclin/Std/IfxAsclin.c',0,0,0,0,0
.L759:
	.byte	5,79,7,0,5,2
	.word	.L126
	.byte	3,250,2,1,5,23,9
	.half	.L557-.L126
	.byte	3,2,1,5,21,9
	.half	.L79-.L557
	.byte	3,2,1,5,19,9
	.half	.L760-.L79
	.byte	1,5,26,9
	.half	.L761-.L760
	.byte	1,5,14,9
	.half	.L762-.L761
	.byte	3,1,1,5,23,9
	.half	.L78-.L762
	.byte	3,125,1,5,5,7,9
	.half	.L763-.L78
	.byte	3,6,1,5,1,9
	.half	.L80-.L763
	.byte	3,1,1,7,9
	.half	.L234-.L80
	.byte	0,1,1
.L757:
	.sdecl	'.debug_ranges',debug,cluster('IfxAsclin_write16')
	.sect	'.debug_ranges'
.L233:
	.word	-1,.L126,0,.L234-.L126,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin_write32')
	.sect	'.debug_info'
.L235:
	.word	372
	.half	3
	.word	.L236
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Asclin/Std/IfxAsclin.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L238,.L237
	.byte	2
	.word	.L131
	.byte	3
	.byte	'IfxAsclin_write32',0,1,135,3,8
	.word	.L262
	.byte	1,1,1
	.word	.L128,.L417,.L127
	.byte	4
	.byte	'asclin',0,1,135,3,38
	.word	.L245,.L418
	.byte	4
	.byte	'data',0,1,135,3,54
	.word	.L356,.L419
	.byte	4
	.byte	'count',0,1,135,3,67
	.word	.L262,.L420
	.byte	5
	.word	.L128,.L417
	.byte	6
	.byte	'txData',0,1,137,3,33
	.word	.L415,.L421
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin_write32')
	.sect	'.debug_abbrev'
.L236:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxAsclin_write32')
	.sect	'.debug_line'
.L237:
	.word	.L765-.L764
.L764:
	.half	3
	.word	.L767-.L766
.L766:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Asclin/Std/IfxAsclin.c',0,0,0,0,0
.L767:
	.byte	5,79,7,0,5,2
	.word	.L128
	.byte	3,136,3,1,5,23,9
	.half	.L559-.L128
	.byte	3,2,1,5,21,9
	.half	.L82-.L559
	.byte	3,2,1,5,19,9
	.half	.L768-.L82
	.byte	1,5,26,9
	.half	.L769-.L768
	.byte	1,5,14,9
	.half	.L770-.L769
	.byte	3,1,1,5,23,9
	.half	.L81-.L770
	.byte	3,125,1,5,5,7,9
	.half	.L771-.L81
	.byte	3,6,1,5,1,9
	.half	.L83-.L771
	.byte	3,1,1,7,9
	.half	.L239-.L83
	.byte	0,1,1
.L765:
	.sdecl	'.debug_ranges',debug,cluster('IfxAsclin_write32')
	.sect	'.debug_ranges'
.L238:
	.word	-1,.L128,0,.L239-.L128,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin_write8')
	.sect	'.debug_info'
.L240:
	.word	371
	.half	3
	.word	.L241
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Asclin/Std/IfxAsclin.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L243,.L242
	.byte	2
	.word	.L131
	.byte	3
	.byte	'IfxAsclin_write8',0,1,149,3,8
	.word	.L262
	.byte	1,1,1
	.word	.L130,.L422,.L129
	.byte	4
	.byte	'asclin',0,1,149,3,37
	.word	.L245,.L423
	.byte	4
	.byte	'data',0,1,149,3,57
	.word	.L424,.L425
	.byte	4
	.byte	'count',0,1,149,3,70
	.word	.L262,.L426
	.byte	5
	.word	.L130,.L422
	.byte	6
	.byte	'txData',0,1,151,3,33
	.word	.L415,.L427
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin_write8')
	.sect	'.debug_abbrev'
.L241:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxAsclin_write8')
	.sect	'.debug_line'
.L242:
	.word	.L773-.L772
.L772:
	.half	3
	.word	.L775-.L774
.L774:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Asclin/Std/IfxAsclin.c',0,0,0,0,0
.L775:
	.byte	5,79,7,0,5,2
	.word	.L130
	.byte	3,150,3,1,5,23,9
	.half	.L561-.L130
	.byte	3,2,1,5,21,9
	.half	.L85-.L561
	.byte	3,2,1,5,19,9
	.half	.L776-.L85
	.byte	1,5,26,9
	.half	.L777-.L776
	.byte	1,5,14,9
	.half	.L778-.L777
	.byte	3,1,1,5,23,9
	.half	.L84-.L778
	.byte	3,125,1,5,5,7,9
	.half	.L779-.L84
	.byte	3,6,1,5,1,9
	.half	.L86-.L779
	.byte	3,1,1,7,9
	.half	.L244-.L86
	.byte	0,1,1
.L773:
	.sdecl	'.debug_ranges',debug,cluster('IfxAsclin_write8')
	.sect	'.debug_ranges'
.L243:
	.word	-1,.L130,0,.L244-.L130,0,0
	.sdecl	'.debug_loc',debug,cluster('IfxAsclin_disableModule')
	.sect	'.debug_loc'
.L87:
	.word	-1,.L88,0,.L304-.L88
	.half	2
	.byte	138,0
	.word	0,0
.L305:
	.word	-1,.L88,0,.L428-.L88
	.half	1
	.byte	100
	.word	.L429-.L88,.L304-.L88
	.half	1
	.byte	111
	.word	0,0
.L311:
	.word	0,0
.L306:
	.word	-1,.L88,.L428-.L88,.L430-.L88
	.half	1
	.byte	82
	.word	.L431-.L88,.L304-.L88
	.half	1
	.byte	88
	.word	.L430-.L88,.L308-.L88
	.half	1
	.byte	84
	.word	.L432-.L88,.L433-.L88
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxAsclin_enableAscErrorFlags')
	.sect	'.debug_loc'
.L89:
	.word	-1,.L90,0,.L313-.L90
	.half	2
	.byte	138,0
	.word	0,0
.L314:
	.word	-1,.L90,0,.L313-.L90
	.half	1
	.byte	100
	.word	0,0
.L320:
	.word	0,0
.L327:
	.word	0,0
.L322:
	.word	0,0
.L329:
	.word	0,0
.L315:
	.word	-1,.L90,0,.L313-.L90
	.half	1
	.byte	84
	.word	0,0
.L316:
	.word	-1,.L90,0,.L313-.L90
	.half	1
	.byte	85
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxAsclin_enableModule')
	.sect	'.debug_loc'
.L91:
	.word	-1,.L92,0,.L331-.L92
	.half	2
	.byte	138,0
	.word	0,0
.L332:
	.word	-1,.L92,0,.L434-.L92
	.half	1
	.byte	100
	.word	.L435-.L92,.L331-.L92
	.half	1
	.byte	111
	.word	0,0
.L338:
	.word	0,0
.L333:
	.word	-1,.L92,.L434-.L92,.L436-.L92
	.half	1
	.byte	82
	.word	.L437-.L92,.L331-.L92
	.half	1
	.byte	95
	.word	.L436-.L92,.L335-.L92
	.half	1
	.byte	84
	.word	.L438-.L92,.L439-.L92
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxAsclin_getAddress')
	.sect	'.debug_loc'
.L93:
	.word	-1,.L94,0,.L246-.L94
	.half	2
	.byte	138,0
	.word	0,0
.L248:
	.word	-1,.L94,0,.L246-.L94
	.half	1
	.byte	84
	.word	0,0
.L249:
	.word	-1,.L94,.L440-.L94,.L6-.L94
	.half	1
	.byte	98
	.word	.L7-.L94,.L246-.L94
	.half	1
	.byte	98
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxAsclin_getFaFrequency')
	.sect	'.debug_loc'
.L95:
	.word	-1,.L96,0,.L251-.L96
	.half	2
	.byte	138,0
	.word	0,0
.L252:
	.word	-1,.L96,0,.L441-.L96
	.half	1
	.byte	100
	.word	.L12-.L96,.L443-.L96
	.half	1
	.byte	100
	.word	.L13-.L96,.L444-.L96
	.half	1
	.byte	100
	.word	.L14-.L96,.L445-.L96
	.half	1
	.byte	100
	.word	.L15-.L96,.L446-.L96
	.half	1
	.byte	100
	.word	.L16-.L96,.L17-.L96
	.half	1
	.byte	100
	.word	0,0
.L258:
	.word	0,0
.L255:
	.word	0,0
.L253:
	.word	-1,.L96,.L442-.L96,.L11-.L96
	.half	1
	.byte	82
	.word	.L441-.L96,.L12-.L96
	.half	1
	.byte	82
	.word	.L443-.L96,.L13-.L96
	.half	1
	.byte	82
	.word	.L444-.L96,.L14-.L96
	.half	1
	.byte	82
	.word	.L445-.L96,.L15-.L96
	.half	1
	.byte	82
	.word	.L446-.L96,.L16-.L96
	.half	1
	.byte	82
	.word	.L447-.L96,.L251-.L96
	.half	1
	.byte	82
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxAsclin_getIndex')
	.sect	'.debug_loc'
.L97:
	.word	-1,.L98,0,.L260-.L98
	.half	2
	.byte	138,0
	.word	0,0
.L261:
	.word	-1,.L98,0,.L260-.L98
	.half	1
	.byte	100
	.word	0,0
.L263:
	.word	-1,.L98,.L449-.L98,.L260-.L98
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L264:
	.word	-1,.L98,.L448-.L98,.L260-.L98
	.half	1
	.byte	82
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxAsclin_getOvsFrequency')
	.sect	'.debug_loc'
.L99:
	.word	-1,.L100,0,.L265-.L100
	.half	2
	.byte	138,0
	.word	0,0
.L266:
	.word	-1,.L100,0,.L450-.L100
	.half	1
	.byte	100
	.word	.L451-.L100,.L265-.L100
	.half	1
	.byte	111
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxAsclin_getPdFrequency')
	.sect	'.debug_loc'
.L101:
	.word	-1,.L102,0,.L267-.L102
	.half	2
	.byte	138,0
	.word	0,0
.L268:
	.word	-1,.L102,0,.L452-.L102
	.half	1
	.byte	100
	.word	.L453-.L102,.L267-.L102
	.half	1
	.byte	111
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxAsclin_getShiftFrequency')
	.sect	'.debug_loc'
.L103:
	.word	-1,.L104,0,.L269-.L104
	.half	2
	.byte	138,0
	.word	0,0
.L270:
	.word	-1,.L104,0,.L454-.L104
	.half	1
	.byte	100
	.word	.L455-.L104,.L269-.L104
	.half	1
	.byte	111
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxAsclin_getSrcPointerEr')
	.sect	'.debug_loc'
.L105:
	.word	-1,.L106,0,.L341-.L106
	.half	2
	.byte	138,0
	.word	0,0
.L342:
	.word	-1,.L106,0,.L456-.L106
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxAsclin_getSrcPointerRx')
	.sect	'.debug_loc'
.L107:
	.word	-1,.L108,0,.L343-.L108
	.half	2
	.byte	138,0
	.word	0,0
.L344:
	.word	-1,.L108,0,.L457-.L108
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxAsclin_getSrcPointerTx')
	.sect	'.debug_loc'
.L109:
	.word	-1,.L110,0,.L345-.L110
	.half	2
	.byte	138,0
	.word	0,0
.L346:
	.word	-1,.L110,0,.L458-.L110
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxAsclin_read16')
	.sect	'.debug_loc'
.L111:
	.word	-1,.L112,0,.L347-.L112
	.half	2
	.byte	138,0
	.word	0,0
.L348:
	.word	-1,.L112,0,.L347-.L112
	.half	1
	.byte	100
	.word	0,0
.L351:
	.word	-1,.L112,0,.L347-.L112
	.half	1
	.byte	84
	.word	.L460-.L112,.L347-.L112
	.half	1
	.byte	82
	.word	0,0
.L350:
	.word	-1,.L112,0,.L347-.L112
	.half	1
	.byte	101
	.word	0,0
.L353:
	.word	-1,.L112,.L459-.L112,.L347-.L112
	.half	1
	.byte	111
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxAsclin_read32')
	.sect	'.debug_loc'
.L113:
	.word	-1,.L114,0,.L354-.L114
	.half	2
	.byte	138,0
	.word	0,0
.L355:
	.word	-1,.L114,0,.L354-.L114
	.half	1
	.byte	100
	.word	0,0
.L358:
	.word	-1,.L114,0,.L354-.L114
	.half	1
	.byte	84
	.word	.L462-.L114,.L354-.L114
	.half	1
	.byte	82
	.word	0,0
.L357:
	.word	-1,.L114,0,.L354-.L114
	.half	1
	.byte	101
	.word	0,0
.L359:
	.word	-1,.L114,.L461-.L114,.L354-.L114
	.half	1
	.byte	111
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxAsclin_read8')
	.sect	'.debug_loc'
.L115:
	.word	-1,.L116,0,.L360-.L116
	.half	2
	.byte	138,0
	.word	0,0
.L361:
	.word	-1,.L116,0,.L360-.L116
	.half	1
	.byte	100
	.word	0,0
.L364:
	.word	-1,.L116,0,.L360-.L116
	.half	1
	.byte	84
	.word	.L464-.L116,.L360-.L116
	.half	1
	.byte	82
	.word	0,0
.L363:
	.word	-1,.L116,0,.L360-.L116
	.half	1
	.byte	101
	.word	0,0
.L365:
	.word	-1,.L116,.L463-.L116,.L360-.L116
	.half	1
	.byte	111
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxAsclin_resetModule')
	.sect	'.debug_loc'
.L117:
	.word	-1,.L118,0,.L271-.L118
	.half	2
	.byte	138,0
	.word	0,0
.L272:
	.word	-1,.L118,0,.L465-.L118
	.half	1
	.byte	100
	.word	.L466-.L118,.L271-.L118
	.half	1
	.byte	111
	.word	0,0
.L274:
	.word	-1,.L118,.L465-.L118,.L467-.L118
	.half	1
	.byte	82
	.word	.L468-.L118,.L271-.L118
	.half	1
	.byte	88
	.word	.L467-.L118,.L469-.L118
	.half	1
	.byte	84
	.word	.L470-.L118,.L471-.L118
	.half	1
	.byte	84
	.word	.L472-.L118,.L473-.L118
	.half	1
	.byte	84
	.word	.L474-.L118,.L475-.L118
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxAsclin_setBaudrateBitFields')
	.sect	'.debug_loc'
.L119:
	.word	-1,.L120,0,.L366-.L120
	.half	2
	.byte	138,0
	.word	0,0
.L367:
	.word	-1,.L120,0,.L376-.L120
	.half	1
	.byte	100
	.word	.L477-.L120,.L366-.L120
	.half	1
	.byte	111
	.word	.L484-.L120,.L485-.L120
	.half	1
	.byte	100
	.word	0,0
.L374:
	.word	0,0
.L393:
	.word	0,0
.L386:
	.word	0,0
.L400:
	.word	0,0
.L379:
	.word	0,0
.L372:
	.word	0,0
.L370:
	.word	-1,.L120,0,.L376-.L120
	.half	1
	.byte	86
	.word	.L480-.L120,.L481-.L120
	.half	1
	.byte	90
	.word	0,0
.L395:
	.word	0,0
.L369:
	.word	-1,.L120,0,.L376-.L120
	.half	1
	.byte	85
	.word	.L478-.L120,.L479-.L120
	.half	1
	.byte	89
	.word	0,0
.L388:
	.word	0,0
.L371:
	.word	-1,.L120,0,.L376-.L120
	.half	1
	.byte	87
	.word	.L482-.L120,.L483-.L120
	.half	1
	.byte	91
	.word	0,0
.L402:
	.word	0,0
.L368:
	.word	-1,.L120,0,.L476-.L120
	.half	1
	.byte	84
	.word	0,0
.L381:
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxAsclin_setBitTiming')
	.sect	'.debug_loc'
.L121:
	.word	-1,.L122,0,.L486-.L122
	.half	2
	.byte	138,0
	.word	.L486-.L122,.L276-.L122
	.half	2
	.byte	138,48
	.word	.L276-.L122,.L276-.L122
	.half	2
	.byte	138,0
	.word	0,0
.L301:
	.word	-1,.L122,.L531-.L122,.L57-.L122
	.half	1
	.byte	90
	.word	.L58-.L122,.L55-.L122
	.half	1
	.byte	90
	.word	0,0
.L300:
	.word	-1,.L122,.L530-.L122,.L57-.L122
	.half	1
	.byte	92
	.word	.L534-.L122,.L55-.L122
	.half	1
	.byte	92
	.word	0,0
.L302:
	.word	-1,.L122,.L516-.L122,.L518-.L122
	.half	1
	.byte	95
	.word	.L519-.L122,.L276-.L122
	.half	2
	.byte	145,84
	.word	.L532-.L122,.L533-.L122
	.half	1
	.byte	95
	.word	0,0
.L277:
	.word	-1,.L122,0,.L487-.L122
	.half	1
	.byte	100
	.word	.L488-.L122,.L276-.L122
	.half	1
	.byte	111
	.word	.L547-.L122,.L548-.L122
	.half	1
	.byte	100
	.word	.L555-.L122,.L556-.L122
	.half	1
	.byte	100
	.word	0,0
.L294:
	.word	0,0
.L278:
	.word	-1,.L122,0,.L487-.L122
	.half	1
	.byte	84
	.word	.L489-.L122,.L56-.L122
	.half	1
	.byte	90
	.word	0,0
.L303:
	.word	0,0
.L287:
	.word	-1,.L122,.L506-.L122,.L507-.L122
	.half	1
	.byte	95
	.word	.L508-.L122,.L276-.L122
	.half	2
	.byte	145,80
	.word	.L513-.L122,.L514-.L122
	.half	1
	.byte	95
	.word	.L521-.L122,.L522-.L122
	.half	5
	.byte	144,32,157,32,0
	.word	.L537-.L122,.L538-.L122
	.half	5
	.byte	144,32,157,32,0
	.word	.L540-.L122,.L541-.L122
	.half	1
	.byte	95
	.word	.L545-.L122,.L546-.L122
	.half	1
	.byte	95
	.word	0,0
.L289:
	.word	-1,.L122,.L515-.L122,.L276-.L122
	.half	2
	.byte	145,92
	.word	.L541-.L122,.L63-.L122
	.half	1
	.byte	95
	.word	0,0
.L291:
	.word	-1,.L122,.L523-.L122,.L524-.L122
	.half	1
	.byte	95
	.word	.L539-.L122,.L59-.L122
	.half	1
	.byte	91
	.word	0,0
.L286:
	.word	-1,.L122,.L295-.L122,.L276-.L122
	.half	1
	.byte	94
	.word	.L499-.L122,.L490-.L122
	.half	1
	.byte	84
	.word	0,0
.L292:
	.word	-1,.L122,.L487-.L122,.L490-.L122
	.half	1
	.byte	82
	.word	.L491-.L122,.L276-.L122
	.half	2
	.byte	145,96
	.word	.L503-.L122,.L505-.L122
	.half	1
	.byte	95
	.word	.L511-.L122,.L512-.L122
	.half	1
	.byte	95
	.word	.L518-.L122,.L520-.L122
	.half	1
	.byte	95
	.word	.L535-.L122,.L536-.L122
	.half	1
	.byte	95
	.word	0,0
.L297:
	.word	-1,.L122,.L500-.L122,.L501-.L122
	.half	1
	.byte	82
	.word	.L501-.L122,.L276-.L122
	.half	2
	.byte	145,100
	.word	.L526-.L122,.L527-.L122
	.half	1
	.byte	95
	.word	.L542-.L122,.L543-.L122
	.half	1
	.byte	95
	.word	0,0
.L284:
	.word	-1,.L122,0,.L487-.L122
	.half	1
	.byte	87
	.word	.L293-.L122,.L276-.L122
	.half	2
	.byte	145,116
	.word	.L553-.L122,.L554-.L122
	.half	1
	.byte	95
	.word	.L67-.L122,.L68-.L122
	.half	1
	.byte	95
	.word	0,0
.L288:
	.word	-1,.L122,.L509-.L122,.L510-.L122
	.half	5
	.byte	144,32,157,32,0
	.word	.L529-.L122,.L276-.L122
	.half	1
	.byte	89
	.word	0,0
.L290:
	.word	-1,.L122,.L514-.L122,.L516-.L122
	.half	1
	.byte	95
	.word	.L517-.L122,.L276-.L122
	.half	2
	.byte	145,88
	.word	.L533-.L122,.L58-.L122
	.half	1
	.byte	95
	.word	0,0
.L299:
	.word	0,0
.L280:
	.word	-1,.L122,0,.L487-.L122
	.half	1
	.byte	85
	.word	.L492-.L122,.L493-.L122
	.half	1
	.byte	95
	.word	.L494-.L122,.L276-.L122
	.half	2
	.byte	145,108
	.word	.L496-.L122,.L498-.L122
	.half	1
	.byte	95
	.word	.L549-.L122,.L550-.L122
	.half	1
	.byte	95
	.word	0,0
.L296:
	.word	-1,.L122,.L525-.L122,.L52-.L122
	.half	1
	.byte	88
	.word	.L53-.L122,.L276-.L122
	.half	1
	.byte	88
	.word	0,0
.L282:
	.word	-1,.L122,0,.L487-.L122
	.half	1
	.byte	86
	.word	.L494-.L122,.L493-.L122
	.half	1
	.byte	89
	.word	.L495-.L122,.L496-.L122
	.half	1
	.byte	95
	.word	.L497-.L122,.L276-.L122
	.half	2
	.byte	145,112
	.word	.L551-.L122,.L552-.L122
	.half	1
	.byte	95
	.word	0,0
.L285:
	.word	0,0
.L298:
	.word	-1,.L122,.L502-.L122,.L503-.L122
	.half	1
	.byte	95
	.word	.L504-.L122,.L276-.L122
	.half	2
	.byte	145,104
	.word	.L528-.L122,.L54-.L122
	.half	1
	.byte	95
	.word	.L544-.L122,.L545-.L122
	.half	1
	.byte	95
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxAsclin_setClockSource')
	.sect	'.debug_loc'
.L123:
	.word	-1,.L124,0,.L404-.L124
	.half	2
	.byte	138,0
	.word	0,0
.L405:
	.word	-1,.L124,0,.L404-.L124
	.half	1
	.byte	100
	.word	0,0
.L409:
	.word	0,0
.L406:
	.word	-1,.L124,0,.L404-.L124
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxAsclin_write16')
	.sect	'.debug_loc'
.L125:
	.word	-1,.L126,0,.L411-.L126
	.half	2
	.byte	138,0
	.word	0,0
.L412:
	.word	-1,.L126,0,.L411-.L126
	.half	1
	.byte	100
	.word	0,0
.L414:
	.word	-1,.L126,0,.L411-.L126
	.half	1
	.byte	84
	.word	.L558-.L126,.L411-.L126
	.half	1
	.byte	82
	.word	0,0
.L413:
	.word	-1,.L126,0,.L411-.L126
	.half	1
	.byte	101
	.word	0,0
.L416:
	.word	-1,.L126,.L557-.L126,.L411-.L126
	.half	1
	.byte	111
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxAsclin_write32')
	.sect	'.debug_loc'
.L127:
	.word	-1,.L128,0,.L417-.L128
	.half	2
	.byte	138,0
	.word	0,0
.L418:
	.word	-1,.L128,0,.L417-.L128
	.half	1
	.byte	100
	.word	0,0
.L420:
	.word	-1,.L128,0,.L417-.L128
	.half	1
	.byte	84
	.word	.L560-.L128,.L417-.L128
	.half	1
	.byte	82
	.word	0,0
.L419:
	.word	-1,.L128,0,.L417-.L128
	.half	1
	.byte	101
	.word	0,0
.L421:
	.word	-1,.L128,.L559-.L128,.L417-.L128
	.half	1
	.byte	111
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxAsclin_write8')
	.sect	'.debug_loc'
.L129:
	.word	-1,.L130,0,.L422-.L130
	.half	2
	.byte	138,0
	.word	0,0
.L423:
	.word	-1,.L130,0,.L422-.L130
	.half	1
	.byte	100
	.word	0,0
.L426:
	.word	-1,.L130,0,.L422-.L130
	.half	1
	.byte	84
	.word	.L562-.L130,.L422-.L130
	.half	1
	.byte	82
	.word	0,0
.L425:
	.word	-1,.L130,0,.L422-.L130
	.half	1
	.byte	101
	.word	0,0
.L427:
	.word	-1,.L130,.L561-.L130,.L422-.L130
	.half	1
	.byte	111
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L780:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('IfxAsclin_disableModule')
	.sect	'.debug_frame'
	.word	12
	.word	.L780,.L88,.L304-.L88
	.sdecl	'.debug_frame',debug,cluster('IfxAsclin_enableAscErrorFlags')
	.sect	'.debug_frame'
	.word	24
	.word	.L780,.L90,.L313-.L90
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxAsclin_enableModule')
	.sect	'.debug_frame'
	.word	12
	.word	.L780,.L92,.L331-.L92
	.sdecl	'.debug_frame',debug,cluster('IfxAsclin_getAddress')
	.sect	'.debug_frame'
	.word	24
	.word	.L780,.L94,.L246-.L94
	.byte	8,19,8,20,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxAsclin_getFaFrequency')
	.sect	'.debug_frame'
	.word	12
	.word	.L780,.L96,.L251-.L96
	.sdecl	'.debug_frame',debug,cluster('IfxAsclin_getIndex')
	.sect	'.debug_frame'
	.word	24
	.word	.L780,.L98,.L260-.L98
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxAsclin_getOvsFrequency')
	.sect	'.debug_frame'
	.word	12
	.word	.L780,.L100,.L265-.L100
	.sdecl	'.debug_frame',debug,cluster('IfxAsclin_getPdFrequency')
	.sect	'.debug_frame'
	.word	12
	.word	.L780,.L102,.L267-.L102
	.sdecl	'.debug_frame',debug,cluster('IfxAsclin_getShiftFrequency')
	.sect	'.debug_frame'
	.word	12
	.word	.L780,.L104,.L269-.L104
	.sdecl	'.debug_frame',debug,cluster('IfxAsclin_getSrcPointerEr')
	.sect	'.debug_frame'
	.word	12
	.word	.L780,.L106,.L341-.L106
	.sdecl	'.debug_frame',debug,cluster('IfxAsclin_getSrcPointerRx')
	.sect	'.debug_frame'
	.word	12
	.word	.L780,.L108,.L343-.L108
	.sdecl	'.debug_frame',debug,cluster('IfxAsclin_getSrcPointerTx')
	.sect	'.debug_frame'
	.word	12
	.word	.L780,.L110,.L345-.L110
	.sdecl	'.debug_frame',debug,cluster('IfxAsclin_read16')
	.sect	'.debug_frame'
	.word	20
	.word	.L780,.L112,.L347-.L112
	.byte	8,18,8,19,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('IfxAsclin_read32')
	.sect	'.debug_frame'
	.word	20
	.word	.L780,.L114,.L354-.L114
	.byte	8,18,8,19,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('IfxAsclin_read8')
	.sect	'.debug_frame'
	.word	20
	.word	.L780,.L116,.L360-.L116
	.byte	8,18,8,19,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('IfxAsclin_resetModule')
	.sect	'.debug_frame'
	.word	12
	.word	.L780,.L118,.L271-.L118
	.sdecl	'.debug_frame',debug,cluster('IfxAsclin_setBaudrateBitFields')
	.sect	'.debug_frame'
	.word	12
	.word	.L780,.L120,.L366-.L120
	.sdecl	'.debug_frame',debug,cluster('IfxAsclin_setBitTiming')
	.sect	'.debug_frame'
	.word	36
	.word	.L780,.L122,.L276-.L122
	.byte	4
	.word	(.L486-.L122)/2
	.byte	19,48,22,26,3,19,138,48,4
	.word	(.L276-.L486)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxAsclin_setClockSource')
	.sect	'.debug_frame'
	.word	24
	.word	.L780,.L124,.L404-.L124
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxAsclin_write16')
	.sect	'.debug_frame'
	.word	20
	.word	.L780,.L126,.L411-.L126
	.byte	8,18,8,19,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('IfxAsclin_write32')
	.sect	'.debug_frame'
	.word	20
	.word	.L780,.L128,.L417-.L128
	.byte	8,18,8,19,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('IfxAsclin_write8')
	.sect	'.debug_frame'
	.word	20
	.word	.L780,.L130,.L422-.L130
	.byte	8,18,8,19,8,22,8,23
	; Module end
