	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc20640a --dep-file=Ifx_WndF32_BlackmanHarrisTable.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2\\\\xf_asr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_WndF32_BlackmanHarrisTable.src ../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_WndF32_BlackmanHarrisTable.c"
	.compiler_name		"ctc"
	;source	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_WndF32_BlackmanHarrisTable.c'

	
$TC16X
	
	.sdecl	'.rodata.Ifx_WndF32_BlackmanHarrisTable.Ifx_g_WndF32_blackmanHarrisTable',data,rom,cluster('Ifx_g_WndF32_blackmanHarrisTable')
	.sect	'.rodata.Ifx_WndF32_BlackmanHarrisTable.Ifx_g_WndF32_blackmanHarrisTable'
	.global	Ifx_g_WndF32_blackmanHarrisTable
	.align	2
Ifx_g_WndF32_blackmanHarrisTable:	.type	object
	.size	Ifx_g_WndF32_blackmanHarrisTable,2048
	.word	947628162,947774849,948063888,948431009,948945544,949608058,950419279,951380094
	.word	952491556,953754874,955171422,956522024,957385912,958328960,959352177,960456654
	.word	961643563,962914155,964269764,965200861,965965842,966775573,967630881,968532635
	.word	969481743,970479154,971525857,972622883,973424914,974025373,974652659,975307367
	.word	975990111,976701528,977442274,978213024,979014474,979847341,980712360,981538712
	.word	982004519,982487566,982988261,983507024,984044281,984600472,985176044,985771455
	.word	986387174,987023678,987681455,988361002,989062829,989787451,990195571,990581474
	.word	990979582,991390171,991813527,992249937,992699694,993163096,993640446,994132051
	.word	994638225,995159285,995695551,996247353,996815020,997398891,997999304,998430480
	.word	998747752,999073821,999408868,999753075,1000106627,1000469710,1000842513,1001225229
	.word	1001618050,1002021173,1002434795,1002859118,1003294344,1003740678,1004198326,1004667498
	.word	1005148405,1005641261,1006146282,1006648322,1006913324,1007184737,1007462675,1007747249
	.word	1008038573,1008336763,1008641934,1008954204,1009273690,1009600511,1009934786,1010276637
	.word	1010626185,1010983552,1011348862,1011722239,1012103808,1012493696,1012892028,1013298933
	.word	1013714538,1014138974,1014572370,1015014857,1015244067,1015474599,1015709875,1015949961
	.word	1016194926,1016444835,1016699758,1016959761,1017224915,1017495286,1017770945,1018051961
	.word	1018338403,1018630341,1018927845,1019230985,1019539832,1019854457,1020174931,1020501324
	.word	1020833708,1021172154,1021516735,1021867521,1022224585,1022587999,1022957835,1023334166
	.word	1023563619,1023758387,1023956511,1024158027,1024362971,1024571380,1024783289,1024998736
	.word	1025217756,1025440386,1025666662,1025896621,1026130297,1026367728,1026608950,1026853999
	.word	1027102910,1027355720,1027612464,1027873178,1028137898,1028406659,1028679497,1028956447
	.word	1029237544,1029522823,1029812319,1030106066,1030404100,1030706455,1031013165,1031324264
	.word	1031639785,1031879274,1032041508,1032206003,1032372777,1032541845,1032713224,1032886930
	.word	1033062979,1033241387,1033422168,1033605340,1033790918,1033978916,1034169351,1034362236
	.word	1034557586,1034755418,1034955743,1035158578,1035363936,1035571832,1035782277,1035995288
	.word	1036210875,1036429054,1036649836,1036873234,1037099261,1037327929,1037559250,1037793235
	.word	1038029897,1038269247,1038511295,1038756052,1039003530,1039253737,1039506685,1039762382
	.word	1040020840,1040234729,1040366730,1040500125,1040634917,1040771110,1040908708,1041047715
	.word	1041188134,1041329969,1041473224,1041617900,1041764001,1041911530,1042060489,1042210881
	.word	1042362708,1042515972,1042670676,1042826819,1042984405,1043143435,1043303909,1043465829
	.word	1043629196,1043794010,1043960271,1044127981,1044297138,1044467743,1044639795,1044813294
	.word	1044988239,1045164629,1045342463,1045521739,1045702457,1045884614,1046068208,1046253237
	.word	1046439698,1046627590,1046816908,1047007651,1047199814,1047393395,1047588389,1047784792
	.word	1047982601,1048181811,1048382417,1048580208,1048681900,1048784282,1048887353,1048991108
	.word	1049095545,1049200661,1049306452,1049412916,1049520048,1049627846,1049736305,1049845421
	.word	1049955191,1050065611,1050176676,1050288382,1050400725,1050513700,1050627303,1050741528
	.word	1050856371,1050971827,1051087890,1051204556,1051321819,1051439673,1051558113,1051677133
	.word	1051796728,1051916891,1052037616,1052158897,1052280727,1052403101,1052526011,1052649451
	.word	1052773415,1052897894,1053022882,1053148373,1053274358,1053400830,1053527782,1053655206
	.word	1053783094,1053911438,1054040231,1054169465,1054299131,1054429220,1054559725,1054690637
	.word	1054821947,1054953646,1055085726,1055218178,1055350992,1055484159,1055617670,1055751517
	.word	1055885688,1056020174,1056154967,1056290055,1056425430,1056561081,1056696997,1056833170
	.word	1056967098,1057035424,1057103863,1057172408,1057241056,1057309799,1057378634,1057447553
	.word	1057516553,1057585627,1057654770,1057723975,1057793238,1057862553,1057931914,1058001314
	.word	1058070750,1058140213,1058209699,1058279201,1058348714,1058418232,1058487748,1058557257
	.word	1058626752,1058696227,1058765676,1058835093,1058904472,1058973806,1059043089,1059112315
	.word	1059181478,1059250570,1059319586,1059388520,1059457365,1059526114,1059594761,1059663299
	.word	1059731723,1059800025,1059868200,1059936239,1060004138,1060071889,1060139486,1060206923
	.word	1060274191,1060341286,1060408201,1060474928,1060541461,1060607794,1060673920,1060739833
	.word	1060805524,1060870989,1060936221,1061001212,1061065957,1061130447,1061194678,1061258643
	.word	1061322333,1061385744,1061448869,1061511701,1061574233,1061636458,1061698371,1061759965
	.word	1061821234,1061882170,1061942767,1062003019,1062062920,1062122462,1062181640,1062240448
	.word	1062298878,1062356925,1062414582,1062471844,1062528703,1062585154,1062641190,1062696805
	.word	1062751994,1062806749,1062861066,1062914938,1062968359,1063021323,1063073824,1063125856
	.word	1063177415,1063228493,1063279085,1063329185,1063378788,1063427889,1063476481,1063524559
	.word	1063572117,1063619151,1063665655,1063711624,1063757052,1063801934,1063846265,1063890040
	.word	1063933254,1063975902,1064017979,1064059481,1064100401,1064140737,1064180482,1064219633
	.word	1064258184,1064296132,1064333472,1064370200,1064406311,1064441801,1064476665,1064510901
	.word	1064544504,1064577469,1064609794,1064641474,1064672505,1064702884,1064732608,1064761672
	.word	1064790073,1064817809,1064844875,1064871269,1064896987,1064922026,1064946384,1064970057
	.word	1064993043,1065015339,1065036941,1065057849,1065078059,1065097568,1065116375,1065134477
	.word	1065151872,1065168558,1065184533,1065199795,1065214342,1065228173,1065241285,1065253678
	.word	1065265350,1065276298,1065286523,1065296023,1065304796,1065312842,1065320159,1065326748
	.word	1065332606,1065337733,1065342129,1065345794,1065348726,1065350925,1065352391,1065353124
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L3:
	.word	1176
	.half	3
	.word	.L4
	.byte	4
.L2:
	.byte	1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_WndF32_BlackmanHarrisTable.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L5
	.byte	2,1,1,3
	.word	257
	.byte	4
	.byte	'Ifx__jump_and_link',0,3,1,61,17,1,1,5
	.byte	'fun',0,1,61,43
	.word	260
	.byte	6,0,7
	.byte	'__fract',0,4,128,1,7
	.byte	'float',0,4,4,8
	.byte	'Ifx__float_to_fract',0,3,1,152,2,18
	.word	305
	.byte	1,1,5
	.byte	'a',0,1,152,2,44
	.word	317
	.byte	6,0,7
	.byte	'unsigned long long int',0,8,7,9
	.byte	'void',0,3
	.word	397
	.byte	8
	.byte	'__ld64',0,3,2,135,1,19
	.word	371
	.byte	1,1,5
	.byte	'addr',0,2,135,1,32
	.word	403
	.byte	6,0,4
	.byte	'__st64',0,3,2,143,1,17,1,1,5
	.byte	'addr',0,2,143,1,30
	.word	403
	.byte	5
	.byte	'value',0,2,143,1,43
	.word	371
	.byte	6,0,10,4,61,9,8,11
	.byte	'real',0
	.word	317
	.byte	4,2,35,0,11
	.byte	'imag',0
	.word	317
	.byte	4,2,35,4,0,12
	.word	489
	.byte	3
	.word	523
	.byte	8
	.byte	'IFX_Cf32_dot',0,3,3,85,20
	.word	317
	.byte	1,1,5
	.byte	'b',0,3,85,49
	.word	528
	.byte	6,0,8
	.byte	'IFX_Cf32_mag',0,3,3,91,20
	.word	317
	.byte	1,1,5
	.byte	'c',0,3,91,49
	.word	528
	.byte	13,6,0,0,3
	.word	489
	.byte	4
	.byte	'IFX_Cf32_set',0,3,3,125,17,1,1,5
	.byte	'a',0,3,125,40
	.word	607
	.byte	5
	.byte	're',0,3,125,51
	.word	317
	.byte	5
	.byte	'im',0,3,125,63
	.word	317
	.byte	6,0,14
	.word	265
	.byte	15
	.word	291
	.byte	6,0,14
	.word	326
	.byte	15
	.word	358
	.byte	6,0,14
	.word	408
	.byte	15
	.word	427
	.byte	6,0,14
	.word	443
	.byte	15
	.word	458
	.byte	15
	.word	472
	.byte	6,0,14
	.word	533
	.byte	15
	.word	557
	.byte	6,0,14
	.word	569
	.byte	15
	.word	593
	.byte	13,16
	.word	533
	.byte	15
	.word	557
	.byte	17
	.word	567
	.byte	0,6,0,0,14
	.word	612
	.byte	15
	.word	632
	.byte	15
	.word	642
	.byte	15
	.word	653
	.byte	6,0,7
	.byte	'short int',0,2,5,18
	.byte	'__wchar_t',0,5,1,1
	.word	783
	.byte	7
	.byte	'unsigned int',0,4,7,18
	.byte	'__size_t',0,5,1,1
	.word	814
	.byte	7
	.byte	'int',0,4,5,18
	.byte	'__ptrdiff_t',0,5,1,1
	.word	847
	.byte	19,1,3
	.word	874
	.byte	18
	.byte	'__codeptr',0,5,1,1
	.word	876
	.byte	7
	.byte	'unsigned char',0,1,8,18
	.byte	'uint8',0,6,105,29
	.word	899
	.byte	7
	.byte	'unsigned short int',0,2,7,18
	.byte	'uint16',0,6,109,29
	.word	930
	.byte	7
	.byte	'unsigned long int',0,4,7,18
	.byte	'uint32',0,6,113,29
	.word	967
	.byte	18
	.byte	'uint64',0,6,118,29
	.word	371
	.byte	18
	.byte	'sint16',0,6,126,29
	.word	783
	.byte	7
	.byte	'long int',0,4,5,18
	.byte	'sint32',0,6,131,1,29
	.word	1033
	.byte	7
	.byte	'long long int',0,8,5,18
	.byte	'sint64',0,6,138,1,29
	.word	1061
	.byte	18
	.byte	'float32',0,6,167,1,29
	.word	317
	.byte	18
	.byte	'pvoid',0,4,57,28
	.word	403
	.byte	18
	.byte	'cfloat32',0,4,65,3
	.word	489
	.byte	18
	.byte	'Ifx_TickTime',0,4,79,28
	.word	1061
	.byte	20,128,16
	.word	317
	.byte	21,255,3,0
.L8:
	.byte	12
	.word	1163
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L4:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,21,0,54,15,39,12,0,0,3,15,0,73,19,0,0,4,46,1,3,8,32,13
	.byte	58,15,59,15,57,15,54,15,39,12,0,0,5,5,0,3,8,58,15,59,15,57,15,73,19,0,0,6,11,0,0,0,7,36,0,3,8,11,15,62
	.byte	15,0,0,8,46,1,3,8,32,13,58,15,59,15,57,15,73,19,54,15,39,12,0,0,9,59,0,3,8,0,0,10,19,1,58,15,59,15,57
	.byte	15,11,15,0,0,11,13,0,3,8,73,19,11,15,56,9,0,0,12,38,0,73,19,0,0,13,11,1,0,0,14,46,1,49,19,0,0,15,5,0,49
	.byte	19,0,0,16,29,1,49,19,0,0,17,11,0,49,19,0,0,18,22,0,3,8,58,15,59,15,57,15,73,19,0,0,19,21,0,54,15,0,0,20
	.byte	1,1,11,15,73,19,0,0,21,33,0,47,15,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L5:
	.word	.L10-.L9
.L9:
	.half	3
	.word	.L12-.L11
.L11:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_Intrinsics.h',0
	.byte	0,0,0
	.byte	'..\\libraries\\infineon_libraries\\Service\\CpuGeneric\\SysSe\\Math\\Ifx_Cf32.h',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Ifx_Types.h',0
	.byte	0,0,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_WndF32_BlackmanHarrisTable.c',0,0,0
	.byte	0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Platform_Types.h',0
	.byte	0,0,0,0
.L12:
.L10:
	.sdecl	'.debug_info',debug,cluster('Ifx_g_WndF32_blackmanHarrisTable')
	.sect	'.debug_info'
.L6:
	.word	303
	.half	3
	.word	.L7
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_WndF32_BlackmanHarrisTable.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'Ifx_g_WndF32_blackmanHarrisTable',0,5,52,19
	.word	.L8
	.byte	1,5,3
	.word	Ifx_g_WndF32_blackmanHarrisTable
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('Ifx_g_WndF32_blackmanHarrisTable')
	.sect	'.debug_abbrev'
.L7:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	; Module end
