  /*
 * Device.c
 *
 *  Created on: 2025��3��30��
 *      Author: lenovo
 */
#include "zf_common_headfile.h"
#include "Device.h"

//总初始化
void ALL_Init(void)
{
    mykey_Init();
    imu660ra_init();
    tft180_init();
    tft180_set_font(TFT180_6X8_FONT);
    gnss_init(TAU1201);//gps初始化
    Steer_Init();
    Motor_Init();
    pit_ms_init(CCU60_CH0,1);//状态检测，扫描电机
    //Speed_PID_Init(C, D);
    quaternion_init();
    encoder_init();
    uart_receiver_init();
//    mt9v03x_init();
    gpio_init(BUZZER_PIN, GPO, GPIO_LOW, GPO_PUSH_PULL);
}

//*************************************** 状态LED *******************************************************************
uint8 key1_flag=0;
uint8 key2_flag=0;
uint8 key3_flag=0;
uint8 key4_flag=0;
uint8 switch1_flag = 0;
uint8 switch2_flag = 0;

uint8 key1_state_last = 0;            //第一次按下状态
uint8 key2_state_last = 0;
uint8 key3_state_last = 0;
uint8 key4_state_last = 0;
uint8 switch1_state_last = 0;         //第一次松开状态
uint8 switch2_state_last = 0;

uint8 key1_state = 1;
uint8 key2_state = 1;
uint8 key3_state = 1;
uint8 key4_state = 1;
uint8 switch1_state = 0;
uint8 switch2_state = 0;
void mykey_Init(void)      //LED初始化
{
    gpio_init(LED1, GPO, GPIO_LOW, GPO_PUSH_PULL);          // 初始化 LED1 为高电平 推挽模式
    gpio_init(LED2, GPO, GPIO_HIGH, GPO_PUSH_PULL);         // 初始化 LED2 为高电平 推挽模式
    gpio_init(LED3, GPO, GPIO_LOW, GPO_PUSH_PULL);          // 初始化 LED3 为高电平 推挽模式
    gpio_init(LED4, GPO, GPIO_HIGH, GPO_PUSH_PULL);         // 初始化 LED4 为高电平 推挽模式

    gpio_init(KEY1, GPI, GPIO_HIGH, GPI_PULL_UP);           // 初始化 KEY1 为高电平 上拉
    gpio_init(KEY2, GPI, GPIO_HIGH, GPI_PULL_UP);           // 初始化 KEY2 为高电平 上拉
    gpio_init(KEY3, GPI, GPIO_HIGH, GPI_PULL_UP);           // 初始化 KEY3 为高电平 上拉
    gpio_init(KEY4, GPI, GPIO_HIGH, GPI_PULL_UP);           // 初始化 KEY4 为高电平 上拉

    gpio_init(SWITCH1, GPI, GPIO_HIGH, GPI_PULL_UP);        // 初始化 SWITCH1 为高电平 上拉
    gpio_init(SWITCH2, GPI, GPIO_HIGH, GPI_PULL_UP);        // 初始化 SWITCH2 为高电平 上拉
}
void key_scan(void)        //扫描
{
    //根据状态
    key1_state_last = key1_state;
    key2_state_last = key2_state;
    key3_state_last = key3_state;
    key4_state_last = key4_state;
    //获取当前状态，高电平为低电平，低电平为高电平
    key1_state = gpio_get_level(KEY1);
    key2_state = gpio_get_level(KEY2);
    key3_state = gpio_get_level(KEY3);
    key4_state = gpio_get_level(KEY4);
    //判断按键状态，按下时为高电平，松开时为低电平
    //按下时当前状态key1_state为1，第一次按下状态为0，key1_state_last == 0，取反为1
    if(key1_state && !key1_state_last) {key1_flag = 1;}
    if(key2_state && !key2_state_last) {key2_flag = 1;}
    if(key3_state && !key3_state_last) {key3_flag = 1;}
    if(key4_state && !key4_state_last) {key4_flag = 1;}
  //system_delay_ms(10);
}
//*************************************** 状态LED *******************************************************************

//******************************************** 转向 **************************************************************
uint32 Overall_Steer = 0;
void Steer_Init(void)           //转向初始化
{
    pwm_init(SERVO_MOTOR_PWM,SERVO_MOTOR_FREQ,(uint32)SERVO_MOTOR_DUTY(SERVO_MOTOR_MID));             //频率50~300
}
void Steer_Set(uint32 duty)       //设置转向
{
    if(duty > SERVO_MOTOR_LMAX){duty=SERVO_MOTOR_LMAX;}
    if(duty < SERVO_MOTOR_RMAX){duty=SERVO_MOTOR_RMAX;}
    Overall_Steer = duty;
    pwm_set_duty(SERVO_MOTOR_PWM,(uint32)SERVO_MOTOR_DUTY(duty));
}
//******************************************** 转向 **************************************************************
//******************************************** 转向 **************************************************************
int32 Overall_Motor = 0;
int32 a=1500;
//void Motor_Init(void)//电机初始化
//{
//    gpio_init(DIR1, GPO, GPIO_HIGH, GPO_PUSH_PULL);                             // GPIO 初始化为高电平 推挽模式
//    pwm_init(PWM1, 17000, 0);                                                   // PWM 初始化频率 17KHz 占空比初始为0
//}
//void Speed_Set(int32 duty)
//{
//    if(duty >6000)
//    {
//        duty = 6000;
//    }
//    if(duty < -6000)
//    {
//        duty = -6000;
//    }
//    pwm_set_duty(PWM1,duty);
//}
void Motor_Init(void)//电机初始化
{
    gpio_init(DIR2, GPO, GPIO_HIGH, GPO_PUSH_PULL);                             // GPIO 初始化为高电平 推挽模式
    pwm_init(PWM2, 17000, 0);                                                   // PWM 初始化频率 17KHz 占空比初始为0
}
void Speed_Set(int32 duty)
{
    if(duty >6000)
    {
        duty = 6000;
    }
    if(duty < -6000)
    {
        duty = -6000;
    }
    pwm_set_duty(PWM2,duty);
}
//******************************************** 转向 **************************************************************


