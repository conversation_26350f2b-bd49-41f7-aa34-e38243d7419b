	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc21904a --dep-file=IfxHssl_cfg.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxHssl_cfg.src ../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxHssl_cfg.c"
	.compiler_name		"ctc"
	;source	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxHssl_cfg.c'

	
$TC16X
	
	.sdecl	'.rodata.IfxHssl_cfg.IfxHssl_cfg_hsctIndexMap',data,rom,cluster('IfxHssl_cfg_hsctIndexMap')
	.sect	'.rodata.IfxHssl_cfg.IfxHssl_cfg_hsctIndexMap'
	.global	IfxHssl_cfg_hsctIndexMap
	.align	4
IfxHssl_cfg_hsctIndexMap:	.type	object
	.size	IfxHssl_cfg_hsctIndexMap,8
	.word	-267845632
	.space	4
	.sdecl	'.rodata.IfxHssl_cfg.IfxHssl_cfg_hsslIndexMap',data,rom,cluster('IfxHssl_cfg_hsslIndexMap')
	.sect	'.rodata.IfxHssl_cfg.IfxHssl_cfg_hsslIndexMap'
	.global	IfxHssl_cfg_hsslIndexMap
	.align	4
IfxHssl_cfg_hsslIndexMap:	.type	object
	.size	IfxHssl_cfg_hsslIndexMap,8
	.word	-267911168
	.space	4
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L3:
	.word	14176
	.half	3
	.word	.L4
	.byte	4
.L2:
	.byte	1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxHssl_cfg.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L5
	.byte	2
	.byte	'short int',0,2,5,3
	.byte	'__wchar_t',0,1,1,1
	.word	233
	.byte	2
	.byte	'unsigned int',0,4,7,3
	.byte	'__size_t',0,1,1,1
	.word	264
	.byte	2
	.byte	'int',0,4,5,3
	.byte	'__ptrdiff_t',0,1,1,1
	.word	297
	.byte	4,1,5
	.word	324
	.byte	3
	.byte	'__codeptr',0,1,1,1
	.word	326
	.byte	2
	.byte	'unsigned char',0,1,8,3
	.byte	'uint8',0,2,105,29
	.word	349
	.byte	2
	.byte	'unsigned short int',0,2,7,3
	.byte	'uint16',0,2,109,29
	.word	380
	.byte	2
	.byte	'unsigned long int',0,4,7,3
	.byte	'uint32',0,2,113,29
	.word	417
	.byte	3
	.byte	'sint16',0,2,126,29
	.word	233
	.byte	2
	.byte	'long int',0,4,5,3
	.byte	'sint32',0,2,131,1,29
	.word	468
	.byte	2
	.byte	'long long int',0,8,5,3
	.byte	'sint64',0,2,138,1,29
	.word	496
	.byte	2
	.byte	'float',0,4,4,3
	.byte	'float32',0,2,167,1,29
	.word	529
	.byte	6
	.byte	'void',0,5
	.word	555
	.byte	3
	.byte	'pvoid',0,3,57,28
	.word	561
	.byte	3
	.byte	'Ifx_TickTime',0,3,79,28
	.word	496
	.byte	7
	.word	555
	.byte	5
	.word	601
	.byte	8,3,143,1,9,8,9
	.byte	'module',0
	.word	606
	.byte	4,2,35,0,9
	.byte	'index',0
	.word	468
	.byte	4,2,35,4,0,3
	.byte	'IfxModule_IndexMap',0,3,147,1,3
	.word	611
	.byte	10
	.byte	'_Ifx_HSSL_ACCEN0_Bits',0,4,45,16,4,11
	.byte	'EN0',0,1
	.word	349
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	349
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	349
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	349
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	349
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	349
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	349
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	349
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	349
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	349
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	349
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	349
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	349
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	349
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	349
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	349
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	349
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	349
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	349
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	349
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	349
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	349
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	349
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	349
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	349
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	349
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	349
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	349
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	349
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	349
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	349
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	349
	.byte	1,0,2,35,3,0,3
	.byte	'Ifx_HSSL_ACCEN0_Bits',0,4,79,3
	.word	677
	.byte	10
	.byte	'_Ifx_HSSL_ACCEN1_Bits',0,4,82,16,4,11
	.byte	'reserved_0',0,4
	.word	264
	.byte	32,0,2,35,0,0,3
	.byte	'Ifx_HSSL_ACCEN1_Bits',0,4,85,3
	.word	1236
	.byte	10
	.byte	'_Ifx_HSSL_AR_Bits',0,4,88,16,4,11
	.byte	'ARW0',0,1
	.word	349
	.byte	2,6,2,35,0,11
	.byte	'ARW1',0,1
	.word	349
	.byte	2,4,2,35,0,11
	.byte	'ARW2',0,1
	.word	349
	.byte	2,2,2,35,0,11
	.byte	'ARW3',0,1
	.word	349
	.byte	2,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	349
	.byte	8,0,2,35,1,11
	.byte	'MAVCH',0,1
	.word	349
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	380
	.byte	14,0,2,35,2,0,3
	.byte	'Ifx_HSSL_AR_Bits',0,4,97,3
	.word	1315
	.byte	10
	.byte	'_Ifx_HSSL_AW_AWEND_Bits',0,4,100,16,4,11
	.byte	'reserved_0',0,1
	.word	349
	.byte	8,0,2,35,0,11
	.byte	'AWE',0,4
	.word	264
	.byte	24,0,2,35,0,0,3
	.byte	'Ifx_HSSL_AW_AWEND_Bits',0,4,104,3
	.word	1490
	.byte	10
	.byte	'_Ifx_HSSL_AW_AWSTART_Bits',0,4,107,16,4,11
	.byte	'reserved_0',0,1
	.word	349
	.byte	8,0,2,35,0,11
	.byte	'AWS',0,4
	.word	264
	.byte	24,0,2,35,0,0,3
	.byte	'Ifx_HSSL_AW_AWSTART_Bits',0,4,111,3
	.word	1588
	.byte	10
	.byte	'_Ifx_HSSL_CFG_Bits',0,4,114,16,4,11
	.byte	'PREDIV',0,2
	.word	380
	.byte	14,2,2,35,0,11
	.byte	'reserved_14',0,1
	.word	349
	.byte	2,0,2,35,1,11
	.byte	'SMT',0,1
	.word	349
	.byte	1,7,2,35,2,11
	.byte	'SMR',0,1
	.word	349
	.byte	1,6,2,35,2,11
	.byte	'SCM',0,1
	.word	349
	.byte	1,5,2,35,2,11
	.byte	'CCC',0,1
	.word	349
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	380
	.byte	12,0,2,35,2,0,3
	.byte	'Ifx_HSSL_CFG_Bits',0,4,123,3
	.word	1690
	.byte	10
	.byte	'_Ifx_HSSL_CLC_Bits',0,4,126,16,4,11
	.byte	'DISR',0,1
	.word	349
	.byte	1,7,2,35,0,11
	.byte	'DISS',0,1
	.word	349
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	349
	.byte	1,5,2,35,0,11
	.byte	'EDIS',0,1
	.word	349
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	264
	.byte	28,0,2,35,0,0,3
	.byte	'Ifx_HSSL_CLC_Bits',0,4,133,1,3
	.word	1865
	.byte	10
	.byte	'_Ifx_HSSL_CRC_Bits',0,4,136,1,16,4,11
	.byte	'XORMASK',0,2
	.word	380
	.byte	16,0,2,35,0,11
	.byte	'XEN',0,1
	.word	349
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,2
	.word	380
	.byte	15,0,2,35,2,0,3
	.byte	'Ifx_HSSL_CRC_Bits',0,4,141,1,3
	.word	2009
	.byte	10
	.byte	'_Ifx_HSSL_I_ICON_Bits',0,4,144,1,16,4,11
	.byte	'IDQ',0,1
	.word	349
	.byte	1,7,2,35,0,11
	.byte	'TQ',0,1
	.word	349
	.byte	1,6,2,35,0,11
	.byte	'LETT',0,1
	.word	349
	.byte	3,3,2,35,0,11
	.byte	'CETT',0,1
	.word	349
	.byte	3,0,2,35,0,11
	.byte	'TOCV',0,1
	.word	349
	.byte	8,0,2,35,1,11
	.byte	'DATLEN',0,1
	.word	349
	.byte	2,6,2,35,2,11
	.byte	'RWT',0,1
	.word	349
	.byte	2,4,2,35,2,11
	.byte	'BSY',0,1
	.word	349
	.byte	1,3,2,35,2,11
	.byte	'ITTAG',0,1
	.word	349
	.byte	3,0,2,35,2,11
	.byte	'TOREL',0,1
	.word	349
	.byte	8,0,2,35,3,0,3
	.byte	'Ifx_HSSL_I_ICON_Bits',0,4,156,1,3
	.word	2119
	.byte	10
	.byte	'_Ifx_HSSL_I_IRD_Bits',0,4,159,1,16,4,11
	.byte	'DATA',0,4
	.word	264
	.byte	32,0,2,35,0,0,3
	.byte	'Ifx_HSSL_I_IRD_Bits',0,4,162,1,3
	.word	2337
	.byte	10
	.byte	'_Ifx_HSSL_I_IRWA_Bits',0,4,165,1,16,4,11
	.byte	'ADDRESS',0,4
	.word	264
	.byte	32,0,2,35,0,0,3
	.byte	'Ifx_HSSL_I_IRWA_Bits',0,4,168,1,3
	.word	2410
	.byte	10
	.byte	'_Ifx_HSSL_I_IWD_Bits',0,4,171,1,16,4,11
	.byte	'DATA',0,4
	.word	264
	.byte	32,0,2,35,0,0,3
	.byte	'Ifx_HSSL_I_IWD_Bits',0,4,174,1,3
	.word	2488
	.byte	10
	.byte	'_Ifx_HSSL_ID_Bits',0,4,177,1,16,4,11
	.byte	'MODREV',0,1
	.word	349
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	349
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	380
	.byte	16,0,2,35,2,0,3
	.byte	'Ifx_HSSL_ID_Bits',0,4,182,1,3
	.word	2561
	.byte	10
	.byte	'_Ifx_HSSL_IS_CA_Bits',0,4,185,1,16,4,11
	.byte	'reserved_0',0,1
	.word	349
	.byte	5,3,2,35,0,11
	.byte	'CURR',0,4
	.word	264
	.byte	27,0,2,35,0,0,3
	.byte	'Ifx_HSSL_IS_CA_Bits',0,4,189,1,3
	.word	2670
	.byte	10
	.byte	'_Ifx_HSSL_IS_FC_Bits',0,4,192,1,16,4,11
	.byte	'RELCOUNT',0,2
	.word	380
	.byte	16,0,2,35,0,11
	.byte	'CURCOUNT',0,2
	.word	380
	.byte	16,0,2,35,2,0,3
	.byte	'Ifx_HSSL_IS_FC_Bits',0,4,196,1,3
	.word	2765
	.byte	10
	.byte	'_Ifx_HSSL_ISSA_Bits',0,4,199,1,16,4,11
	.byte	'reserved_0',0,1
	.word	349
	.byte	5,3,2,35,0,11
	.byte	'START',0,4
	.word	264
	.byte	27,0,2,35,0,0,3
	.byte	'Ifx_HSSL_ISSA_Bits',0,4,203,1,3
	.word	2862
	.byte	10
	.byte	'_Ifx_HSSL_KRST0_Bits',0,4,206,1,16,4,11
	.byte	'RST',0,1
	.word	349
	.byte	1,7,2,35,0,11
	.byte	'RSTSTAT',0,1
	.word	349
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	264
	.byte	30,0,2,35,0,0,3
	.byte	'Ifx_HSSL_KRST0_Bits',0,4,211,1,3
	.word	2956
	.byte	10
	.byte	'_Ifx_HSSL_KRST1_Bits',0,4,214,1,16,4,11
	.byte	'RST',0,1
	.word	349
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	264
	.byte	31,0,2,35,0,0,3
	.byte	'Ifx_HSSL_KRST1_Bits',0,4,218,1,3
	.word	3069
	.byte	10
	.byte	'_Ifx_HSSL_KRSTCLR_Bits',0,4,221,1,16,4,11
	.byte	'CLR',0,1
	.word	349
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	264
	.byte	31,0,2,35,0,0,3
	.byte	'Ifx_HSSL_KRSTCLR_Bits',0,4,225,1,3
	.word	3163
	.byte	10
	.byte	'_Ifx_HSSL_MFLAGS_Bits',0,4,228,1,16,4,11
	.byte	'NACK',0,1
	.word	349
	.byte	4,4,2,35,0,11
	.byte	'TTE',0,1
	.word	349
	.byte	4,0,2,35,0,11
	.byte	'TIMEOUT',0,1
	.word	349
	.byte	4,4,2,35,1,11
	.byte	'UNEXPECTED',0,1
	.word	349
	.byte	4,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	349
	.byte	2,6,2,35,2,11
	.byte	'TMB',0,1
	.word	349
	.byte	1,5,2,35,2,11
	.byte	'IMB',0,1
	.word	349
	.byte	1,4,2,35,2,11
	.byte	'ISB',0,1
	.word	349
	.byte	1,3,2,35,2,11
	.byte	'MAV',0,1
	.word	349
	.byte	1,2,2,35,2,11
	.byte	'SRIE',0,1
	.word	349
	.byte	1,1,2,35,2,11
	.byte	'PIE1',0,1
	.word	349
	.byte	1,0,2,35,2,11
	.byte	'PIE2',0,1
	.word	349
	.byte	1,7,2,35,3,11
	.byte	'CRCE',0,1
	.word	349
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	349
	.byte	2,4,2,35,3,11
	.byte	'TSE',0,1
	.word	349
	.byte	1,3,2,35,3,11
	.byte	'TEI',0,1
	.word	349
	.byte	1,2,2,35,3,11
	.byte	'TEO',0,1
	.word	349
	.byte	1,1,2,35,3,11
	.byte	'INI',0,1
	.word	349
	.byte	1,0,2,35,3,0,3
	.byte	'Ifx_HSSL_MFLAGS_Bits',0,4,248,1,3
	.word	3261
	.byte	10
	.byte	'_Ifx_HSSL_MFLAGSCL_Bits',0,4,251,1,16,4,11
	.byte	'NACKC',0,1
	.word	349
	.byte	4,4,2,35,0,11
	.byte	'TTEC',0,1
	.word	349
	.byte	4,0,2,35,0,11
	.byte	'TIMEOUTC',0,1
	.word	349
	.byte	4,4,2,35,1,11
	.byte	'UNEXPECTEDC',0,1
	.word	349
	.byte	4,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	349
	.byte	2,6,2,35,2,11
	.byte	'TMBC',0,1
	.word	349
	.byte	1,5,2,35,2,11
	.byte	'IMBC',0,1
	.word	349
	.byte	1,4,2,35,2,11
	.byte	'ISBC',0,1
	.word	349
	.byte	1,3,2,35,2,11
	.byte	'MAVC',0,1
	.word	349
	.byte	1,2,2,35,2,11
	.byte	'SRIEC',0,1
	.word	349
	.byte	1,1,2,35,2,11
	.byte	'PIE1C',0,1
	.word	349
	.byte	1,0,2,35,2,11
	.byte	'PIE2C',0,1
	.word	349
	.byte	1,7,2,35,3,11
	.byte	'CRCEC',0,1
	.word	349
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	349
	.byte	2,4,2,35,3,11
	.byte	'TSEC',0,1
	.word	349
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	349
	.byte	1,2,2,35,3,11
	.byte	'TEOC',0,1
	.word	349
	.byte	1,1,2,35,3,11
	.byte	'INIC',0,1
	.word	349
	.byte	1,0,2,35,3,0,3
	.byte	'Ifx_HSSL_MFLAGSCL_Bits',0,4,143,2,3
	.word	3622
	.byte	10
	.byte	'_Ifx_HSSL_MFLAGSEN_Bits',0,4,146,2,16,4,11
	.byte	'NACKEN',0,1
	.word	349
	.byte	4,4,2,35,0,11
	.byte	'TTEEN',0,1
	.word	349
	.byte	4,0,2,35,0,11
	.byte	'TIMEOUTEN',0,1
	.word	349
	.byte	4,4,2,35,1,11
	.byte	'UNEXPECTEDEN',0,1
	.word	349
	.byte	4,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	349
	.byte	5,3,2,35,2,11
	.byte	'MAVEN',0,1
	.word	349
	.byte	1,2,2,35,2,11
	.byte	'SRIEEN',0,1
	.word	349
	.byte	1,1,2,35,2,11
	.byte	'PIE1EN',0,1
	.word	349
	.byte	1,0,2,35,2,11
	.byte	'PIE2EN',0,1
	.word	349
	.byte	1,7,2,35,3,11
	.byte	'CRCEEN',0,1
	.word	349
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	349
	.byte	3,3,2,35,3,11
	.byte	'TEIEN',0,1
	.word	349
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	349
	.byte	2,0,2,35,3,0,3
	.byte	'Ifx_HSSL_MFLAGSEN_Bits',0,4,161,2,3
	.word	4010
	.byte	10
	.byte	'_Ifx_HSSL_MFLAGSSET_Bits',0,4,164,2,16,4,11
	.byte	'NACKS',0,1
	.word	349
	.byte	4,4,2,35,0,11
	.byte	'TTES',0,1
	.word	349
	.byte	4,0,2,35,0,11
	.byte	'TIMEOUTS',0,1
	.word	349
	.byte	4,4,2,35,1,11
	.byte	'UNEXPECTEDS',0,1
	.word	349
	.byte	4,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	349
	.byte	2,6,2,35,2,11
	.byte	'TMBS',0,1
	.word	349
	.byte	1,5,2,35,2,11
	.byte	'IMBS',0,1
	.word	349
	.byte	1,4,2,35,2,11
	.byte	'ISBS',0,1
	.word	349
	.byte	1,3,2,35,2,11
	.byte	'MAVS',0,1
	.word	349
	.byte	1,2,2,35,2,11
	.byte	'SRIES',0,1
	.word	349
	.byte	1,1,2,35,2,11
	.byte	'PIE1S',0,1
	.word	349
	.byte	1,0,2,35,2,11
	.byte	'PIE2S',0,1
	.word	349
	.byte	1,7,2,35,3,11
	.byte	'CRCES',0,1
	.word	349
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	349
	.byte	2,4,2,35,3,11
	.byte	'TSES',0,1
	.word	349
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	349
	.byte	1,2,2,35,3,11
	.byte	'TEOS',0,1
	.word	349
	.byte	1,1,2,35,3,11
	.byte	'INIS',0,1
	.word	349
	.byte	1,0,2,35,3,0,3
	.byte	'Ifx_HSSL_MFLAGSSET_Bits',0,4,184,2,3
	.word	4328
	.byte	10
	.byte	'_Ifx_HSSL_OCS_Bits',0,4,187,2,16,4,11
	.byte	'TGS',0,1
	.word	349
	.byte	2,6,2,35,0,11
	.byte	'TGB',0,1
	.word	349
	.byte	1,5,2,35,0,11
	.byte	'TG_P',0,1
	.word	349
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	264
	.byte	20,8,2,35,0,11
	.byte	'SUS',0,1
	.word	349
	.byte	4,4,2,35,3,11
	.byte	'SUS_P',0,1
	.word	349
	.byte	1,3,2,35,3,11
	.byte	'SUSSTA',0,1
	.word	349
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	349
	.byte	2,0,2,35,3,0,3
	.byte	'Ifx_HSSL_OCS_Bits',0,4,197,2,3
	.word	4718
	.byte	10
	.byte	'_Ifx_HSSL_QFLAGS_Bits',0,4,200,2,16,4,11
	.byte	'I',0,1
	.word	349
	.byte	4,4,2,35,0,11
	.byte	'T',0,1
	.word	349
	.byte	4,0,2,35,0,11
	.byte	'R',0,1
	.word	349
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	349
	.byte	4,0,2,35,1,11
	.byte	'E0',0,1
	.word	349
	.byte	2,6,2,35,2,11
	.byte	'E1',0,1
	.word	349
	.byte	2,4,2,35,2,11
	.byte	'E2',0,1
	.word	349
	.byte	2,2,2,35,2,11
	.byte	'E3',0,1
	.word	349
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	349
	.byte	4,4,2,35,3,11
	.byte	'IS',0,1
	.word	349
	.byte	1,3,2,35,3,11
	.byte	'RS',0,1
	.word	349
	.byte	1,2,2,35,3,11
	.byte	'TS',0,1
	.word	349
	.byte	1,1,2,35,3,11
	.byte	'ES',0,1
	.word	349
	.byte	1,0,2,35,3,0,3
	.byte	'Ifx_HSSL_QFLAGS_Bits',0,4,215,2,3
	.word	4912
	.byte	10
	.byte	'_Ifx_HSSL_SFSFLAGS_Bits',0,4,218,2,16,4,11
	.byte	'RXFL',0,1
	.word	349
	.byte	2,6,2,35,0,11
	.byte	'TXFL',0,1
	.word	349
	.byte	2,4,2,35,0,11
	.byte	'EXFL',0,1
	.word	349
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,2
	.word	380
	.byte	9,1,2,35,0,11
	.byte	'ISF',0,1
	.word	349
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	380
	.byte	16,0,2,35,2,0,3
	.byte	'Ifx_HSSL_SFSFLAGS_Bits',0,4,226,2,3
	.word	5168
	.byte	10
	.byte	'_Ifx_HSSL_T_TCA_Bits',0,4,229,2,16,4,11
	.byte	'A',0,4
	.word	264
	.byte	32,0,2,35,0,0,3
	.byte	'Ifx_HSSL_T_TCA_Bits',0,4,232,2,3
	.word	5339
	.byte	10
	.byte	'_Ifx_HSSL_T_TCD_Bits',0,4,235,2,16,4,11
	.byte	'D',0,4
	.word	264
	.byte	32,0,2,35,0,0,3
	.byte	'Ifx_HSSL_T_TCD_Bits',0,4,238,2,3
	.word	5409
	.byte	10
	.byte	'_Ifx_HSSL_TIDADD_Bits',0,4,241,2,16,4,11
	.byte	'A',0,4
	.word	264
	.byte	32,0,2,35,0,0,3
	.byte	'Ifx_HSSL_TIDADD_Bits',0,4,244,2,3
	.word	5479
	.byte	10
	.byte	'_Ifx_HSSL_TS_CA_Bits',0,4,247,2,16,4,11
	.byte	'reserved_0',0,1
	.word	349
	.byte	5,3,2,35,0,11
	.byte	'CURR',0,4
	.word	264
	.byte	27,0,2,35,0,0,3
	.byte	'Ifx_HSSL_TS_CA_Bits',0,4,251,2,3
	.word	5551
	.byte	10
	.byte	'_Ifx_HSSL_TS_FC_Bits',0,4,254,2,16,4,11
	.byte	'RELCOUNT',0,2
	.word	380
	.byte	16,0,2,35,0,11
	.byte	'CURCOUNT',0,2
	.word	380
	.byte	16,0,2,35,2,0,3
	.byte	'Ifx_HSSL_TS_FC_Bits',0,4,130,3,3
	.word	5646
	.byte	10
	.byte	'_Ifx_HSSL_TSSA_Bits',0,4,133,3,16,4,11
	.byte	'reserved_0',0,1
	.word	349
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	264
	.byte	27,0,2,35,0,0,3
	.byte	'Ifx_HSSL_TSSA_Bits',0,4,137,3,3
	.word	5743
	.byte	10
	.byte	'_Ifx_HSSL_TSTAT_Bits',0,4,140,3,16,4,11
	.byte	'LASTCC0',0,1
	.word	349
	.byte	5,3,2,35,0,11
	.byte	'LASTTT0',0,1
	.word	349
	.byte	3,0,2,35,0,11
	.byte	'LASTCC1',0,1
	.word	349
	.byte	5,3,2,35,1,11
	.byte	'LASTTT1',0,1
	.word	349
	.byte	3,0,2,35,1,11
	.byte	'LASTCC2',0,1
	.word	349
	.byte	5,3,2,35,2,11
	.byte	'LASTTT2',0,1
	.word	349
	.byte	3,0,2,35,2,11
	.byte	'LASTCC3',0,1
	.word	349
	.byte	5,3,2,35,3,11
	.byte	'LASTTT3',0,1
	.word	349
	.byte	3,0,2,35,3,0,3
	.byte	'Ifx_HSSL_TSTAT_Bits',0,4,150,3,3
	.word	5836
	.byte	12,4,158,3,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	677
	.byte	4,2,35,0,0,3
	.byte	'Ifx_HSSL_ACCEN0',0,4,163,3,3
	.word	6045
	.byte	12,4,166,3,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	1236
	.byte	4,2,35,0,0,3
	.byte	'Ifx_HSSL_ACCEN1',0,4,171,3,3
	.word	6110
	.byte	12,4,174,3,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	1315
	.byte	4,2,35,0,0,3
	.byte	'Ifx_HSSL_AR',0,4,179,3,3
	.word	6175
	.byte	12,4,182,3,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	1490
	.byte	4,2,35,0,0,3
	.byte	'Ifx_HSSL_AW_AWEND',0,4,187,3,3
	.word	6236
	.byte	12,4,190,3,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	1588
	.byte	4,2,35,0,0,3
	.byte	'Ifx_HSSL_AW_AWSTART',0,4,195,3,3
	.word	6303
	.byte	12,4,198,3,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	1690
	.byte	4,2,35,0,0,3
	.byte	'Ifx_HSSL_CFG',0,4,203,3,3
	.word	6372
	.byte	12,4,206,3,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	1865
	.byte	4,2,35,0,0,3
	.byte	'Ifx_HSSL_CLC',0,4,211,3,3
	.word	6434
	.byte	12,4,214,3,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	2009
	.byte	4,2,35,0,0,3
	.byte	'Ifx_HSSL_CRC',0,4,219,3,3
	.word	6496
	.byte	12,4,222,3,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	2119
	.byte	4,2,35,0,0,3
	.byte	'Ifx_HSSL_I_ICON',0,4,227,3,3
	.word	6558
	.byte	12,4,230,3,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	2337
	.byte	4,2,35,0,0,3
	.byte	'Ifx_HSSL_I_IRD',0,4,235,3,3
	.word	6623
	.byte	12,4,238,3,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	2410
	.byte	4,2,35,0,0,3
	.byte	'Ifx_HSSL_I_IRWA',0,4,243,3,3
	.word	6687
	.byte	12,4,246,3,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	2488
	.byte	4,2,35,0,0,3
	.byte	'Ifx_HSSL_I_IWD',0,4,251,3,3
	.word	6752
	.byte	12,4,254,3,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	2561
	.byte	4,2,35,0,0,3
	.byte	'Ifx_HSSL_ID',0,4,131,4,3
	.word	6816
	.byte	12,4,134,4,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	2670
	.byte	4,2,35,0,0,3
	.byte	'Ifx_HSSL_IS_CA',0,4,139,4,3
	.word	6877
	.byte	12,4,142,4,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	2765
	.byte	4,2,35,0,0,3
	.byte	'Ifx_HSSL_IS_FC',0,4,147,4,3
	.word	6941
	.byte	12,4,150,4,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	2862
	.byte	4,2,35,0,0,3
	.byte	'Ifx_HSSL_ISSA',0,4,155,4,3
	.word	7005
	.byte	12,4,158,4,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	2956
	.byte	4,2,35,0,0,3
	.byte	'Ifx_HSSL_KRST0',0,4,163,4,3
	.word	7068
	.byte	12,4,166,4,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	3069
	.byte	4,2,35,0,0,3
	.byte	'Ifx_HSSL_KRST1',0,4,171,4,3
	.word	7132
	.byte	12,4,174,4,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	3163
	.byte	4,2,35,0,0,3
	.byte	'Ifx_HSSL_KRSTCLR',0,4,179,4,3
	.word	7196
	.byte	12,4,182,4,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	3261
	.byte	4,2,35,0,0,3
	.byte	'Ifx_HSSL_MFLAGS',0,4,187,4,3
	.word	7262
	.byte	12,4,190,4,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	3622
	.byte	4,2,35,0,0,3
	.byte	'Ifx_HSSL_MFLAGSCL',0,4,195,4,3
	.word	7327
	.byte	12,4,198,4,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	4010
	.byte	4,2,35,0,0,3
	.byte	'Ifx_HSSL_MFLAGSEN',0,4,203,4,3
	.word	7394
	.byte	12,4,206,4,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	4328
	.byte	4,2,35,0,0,3
	.byte	'Ifx_HSSL_MFLAGSSET',0,4,211,4,3
	.word	7461
	.byte	12,4,214,4,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	4718
	.byte	4,2,35,0,0,3
	.byte	'Ifx_HSSL_OCS',0,4,219,4,3
	.word	7529
	.byte	12,4,222,4,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	4912
	.byte	4,2,35,0,0,3
	.byte	'Ifx_HSSL_QFLAGS',0,4,227,4,3
	.word	7591
	.byte	12,4,230,4,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	5168
	.byte	4,2,35,0,0,3
	.byte	'Ifx_HSSL_SFSFLAGS',0,4,235,4,3
	.word	7656
	.byte	12,4,238,4,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	5339
	.byte	4,2,35,0,0,3
	.byte	'Ifx_HSSL_T_TCA',0,4,243,4,3
	.word	7723
	.byte	12,4,246,4,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	5409
	.byte	4,2,35,0,0,3
	.byte	'Ifx_HSSL_T_TCD',0,4,251,4,3
	.word	7787
	.byte	12,4,254,4,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	5479
	.byte	4,2,35,0,0,3
	.byte	'Ifx_HSSL_TIDADD',0,4,131,5,3
	.word	7851
	.byte	12,4,134,5,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	5551
	.byte	4,2,35,0,0,3
	.byte	'Ifx_HSSL_TS_CA',0,4,139,5,3
	.word	7916
	.byte	12,4,142,5,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	5646
	.byte	4,2,35,0,0,3
	.byte	'Ifx_HSSL_TS_FC',0,4,147,5,3
	.word	7980
	.byte	12,4,150,5,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	5743
	.byte	4,2,35,0,0,3
	.byte	'Ifx_HSSL_TSSA',0,4,155,5,3
	.word	8044
	.byte	12,4,158,5,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	5836
	.byte	4,2,35,0,0,3
	.byte	'Ifx_HSSL_TSTAT',0,4,163,5,3
	.word	8107
	.byte	10
	.byte	'_Ifx_HSSL_AW',0,4,174,5,25,8,9
	.byte	'AWSTART',0
	.word	6303
	.byte	4,2,35,0,9
	.byte	'AWEND',0
	.word	6236
	.byte	4,2,35,4,0,7
	.word	8171
	.byte	3
	.byte	'Ifx_HSSL_AW',0,4,178,5,3
	.word	8223
	.byte	10
	.byte	'_Ifx_HSSL_I',0,4,181,5,25,16,9
	.byte	'IWD',0
	.word	6752
	.byte	4,2,35,0,9
	.byte	'ICON',0
	.word	6558
	.byte	4,2,35,4,9
	.byte	'IRWA',0
	.word	6687
	.byte	4,2,35,8,9
	.byte	'IRD',0
	.word	6623
	.byte	4,2,35,12,0,7
	.word	8249
	.byte	3
	.byte	'Ifx_HSSL_I',0,4,187,5,3
	.word	8322
	.byte	13,8
	.word	7005
	.byte	14,1,0,10
	.byte	'_Ifx_HSSL_IS',0,4,190,5,25,16,9
	.byte	'SA',0
	.word	8347
	.byte	8,2,35,0,9
	.byte	'CA',0
	.word	6877
	.byte	4,2,35,8,9
	.byte	'FC',0
	.word	6941
	.byte	4,2,35,12,0,7
	.word	8356
	.byte	3
	.byte	'Ifx_HSSL_IS',0,4,195,5,3
	.word	8412
	.byte	10
	.byte	'_Ifx_HSSL_T',0,4,198,5,25,8,9
	.byte	'TCD',0
	.word	7787
	.byte	4,2,35,0,9
	.byte	'TCA',0
	.word	7723
	.byte	4,2,35,4,0,7
	.word	8438
	.byte	3
	.byte	'Ifx_HSSL_T',0,4,202,5,3
	.word	8483
	.byte	13,8
	.word	8044
	.byte	14,1,0,10
	.byte	'_Ifx_HSSL_TS',0,4,205,5,25,16,9
	.byte	'SA',0
	.word	8508
	.byte	8,2,35,0,9
	.byte	'CA',0
	.word	7916
	.byte	4,2,35,8,9
	.byte	'FC',0
	.word	7980
	.byte	4,2,35,12,0,7
	.word	8517
	.byte	3
	.byte	'Ifx_HSSL_TS',0,4,210,5,3
	.word	8573
	.byte	10
	.byte	'_Ifx_HSCT_ACCEN0_Bits',0,5,45,16,4,11
	.byte	'EN0',0,1
	.word	349
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	349
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	349
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	349
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	349
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	349
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	349
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	349
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	349
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	349
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	349
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	349
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	349
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	349
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	349
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	349
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	349
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	349
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	349
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	349
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	349
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	349
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	349
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	349
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	349
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	349
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	349
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	349
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	349
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	349
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	349
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	349
	.byte	1,0,2,35,3,0,3
	.byte	'Ifx_HSCT_ACCEN0_Bits',0,5,79,3
	.word	8599
	.byte	10
	.byte	'_Ifx_HSCT_ACCEN1_Bits',0,5,82,16,4,11
	.byte	'reserved_0',0,4
	.word	264
	.byte	32,0,2,35,0,0,3
	.byte	'Ifx_HSCT_ACCEN1_Bits',0,5,85,3
	.word	9158
	.byte	10
	.byte	'_Ifx_HSCT_CLC_Bits',0,5,88,16,4,11
	.byte	'DISR',0,1
	.word	349
	.byte	1,7,2,35,0,11
	.byte	'DISS',0,1
	.word	349
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	349
	.byte	1,5,2,35,0,11
	.byte	'EDIS',0,1
	.word	349
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	264
	.byte	28,0,2,35,0,0,3
	.byte	'Ifx_HSCT_CLC_Bits',0,5,95,3
	.word	9237
	.byte	10
	.byte	'_Ifx_HSCT_CONFIGPHY_Bits',0,5,98,16,4,11
	.byte	'reserved_0',0,1
	.word	349
	.byte	1,7,2,35,0,11
	.byte	'PLLPON',0,1
	.word	349
	.byte	1,6,2,35,0,11
	.byte	'PLLPE',0,1
	.word	349
	.byte	6,0,2,35,0,11
	.byte	'PLLWMF',0,1
	.word	349
	.byte	6,2,2,35,1,11
	.byte	'PLLKPKI',0,1
	.word	349
	.byte	1,1,2,35,1,11
	.byte	'PHYRST',0,1
	.word	349
	.byte	1,0,2,35,1,11
	.byte	'PLLKP',0,1
	.word	349
	.byte	3,5,2,35,2,11
	.byte	'PLLKI',0,1
	.word	349
	.byte	3,2,2,35,2,11
	.byte	'PLLIVR',0,2
	.word	380
	.byte	4,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	349
	.byte	2,4,2,35,3,11
	.byte	'OSCCLKEN',0,1
	.word	349
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	349
	.byte	3,0,2,35,3,0,3
	.byte	'Ifx_HSCT_CONFIGPHY_Bits',0,5,112,3
	.word	9380
	.byte	10
	.byte	'_Ifx_HSCT_CTSCTRL_Bits',0,5,115,16,4,11
	.byte	'CTS_FRAME',0,1
	.word	349
	.byte	1,7,2,35,0,11
	.byte	'CTS_TXD',0,1
	.word	349
	.byte	1,6,2,35,0,11
	.byte	'CTS_RXD',0,1
	.word	349
	.byte	1,5,2,35,0,11
	.byte	'HSSL_CTS_FBD',0,1
	.word	349
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	264
	.byte	28,0,2,35,0,0,3
	.byte	'Ifx_HSCT_CTSCTRL_Bits',0,5,122,3
	.word	9673
	.byte	10
	.byte	'_Ifx_HSCT_DISABLE_Bits',0,5,125,16,4,11
	.byte	'TX_DIS',0,1
	.word	349
	.byte	1,7,2,35,0,11
	.byte	'RX_DIS',0,1
	.word	349
	.byte	1,6,2,35,0,11
	.byte	'RX_HEPD',0,1
	.word	349
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	264
	.byte	29,0,2,35,0,0,3
	.byte	'Ifx_HSCT_DISABLE_Bits',0,5,131,1,3
	.word	9837
	.byte	10
	.byte	'_Ifx_HSCT_ID_Bits',0,5,134,1,16,4,11
	.byte	'MODREV',0,1
	.word	349
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	349
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	380
	.byte	16,0,2,35,2,0,3
	.byte	'Ifx_HSCT_ID_Bits',0,5,139,1,3
	.word	9974
	.byte	10
	.byte	'_Ifx_HSCT_IFCTRL_Bits',0,5,142,1,16,4,11
	.byte	'IFCVS',0,1
	.word	349
	.byte	8,0,2,35,0,11
	.byte	'SIFCV',0,1
	.word	349
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	349
	.byte	7,0,2,35,1,11
	.byte	'MRXSPEED',0,1
	.word	349
	.byte	2,6,2,35,2,11
	.byte	'MTXSPEED',0,1
	.word	349
	.byte	2,4,2,35,2,11
	.byte	'IFTESTMD',0,1
	.word	349
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,2
	.word	380
	.byte	11,0,2,35,2,0,3
	.byte	'Ifx_HSCT_IFCTRL_Bits',0,5,151,1,3
	.word	10083
	.byte	10
	.byte	'_Ifx_HSCT_IFSTAT_Bits',0,5,154,1,16,4,11
	.byte	'RX_STAT',0,1
	.word	349
	.byte	3,5,2,35,0,11
	.byte	'TX_STAT',0,1
	.word	349
	.byte	2,3,2,35,0,11
	.byte	'reserved_5',0,4
	.word	264
	.byte	27,0,2,35,0,0,3
	.byte	'Ifx_HSCT_IFSTAT_Bits',0,5,159,1,3
	.word	10281
	.byte	10
	.byte	'_Ifx_HSCT_INIT_Bits',0,5,162,1,16,4,11
	.byte	'reserved_0',0,1
	.word	349
	.byte	1,7,2,35,0,11
	.byte	'SYS_CLK_EN',0,1
	.word	349
	.byte	1,6,2,35,0,11
	.byte	'SRCF',0,1
	.word	349
	.byte	1,5,2,35,0,11
	.byte	'IFM',0,1
	.word	349
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	380
	.byte	6,6,2,35,0,11
	.byte	'LHLR',0,1
	.word	349
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	349
	.byte	5,0,2,35,1,11
	.byte	'TXHD',0,1
	.word	349
	.byte	3,5,2,35,2,11
	.byte	'RXHD',0,1
	.word	349
	.byte	3,2,2,35,2,11
	.byte	'reserved_22',0,2
	.word	380
	.byte	10,0,2,35,2,0,3
	.byte	'Ifx_HSCT_INIT_Bits',0,5,174,1,3
	.word	10400
	.byte	10
	.byte	'_Ifx_HSCT_IRQ_Bits',0,5,177,1,16,4,11
	.byte	'reserved_0',0,1
	.word	349
	.byte	1,7,2,35,0,11
	.byte	'HER',0,1
	.word	349
	.byte	1,6,2,35,0,11
	.byte	'PYER',0,1
	.word	349
	.byte	1,5,2,35,0,11
	.byte	'CER',0,1
	.word	349
	.byte	1,4,2,35,0,11
	.byte	'IFCFS',0,1
	.word	349
	.byte	1,3,2,35,0,11
	.byte	'SMER',0,1
	.word	349
	.byte	1,2,2,35,0,11
	.byte	'USMSF',0,1
	.word	349
	.byte	1,1,2,35,0,11
	.byte	'PLER',0,1
	.word	349
	.byte	1,0,2,35,0,11
	.byte	'USM',0,1
	.word	349
	.byte	1,7,2,35,1,11
	.byte	'PAR',0,1
	.word	349
	.byte	1,6,2,35,1,11
	.byte	'TXTE',0,1
	.word	349
	.byte	1,5,2,35,1,11
	.byte	'SFO',0,1
	.word	349
	.byte	1,4,2,35,1,11
	.byte	'SFU',0,1
	.word	349
	.byte	1,3,2,35,1,11
	.byte	'reserved_13',0,4
	.word	264
	.byte	19,0,2,35,0,0,3
	.byte	'Ifx_HSCT_IRQ_Bits',0,5,193,1,3
	.word	10646
	.byte	10
	.byte	'_Ifx_HSCT_IRQCLR_Bits',0,5,196,1,16,4,11
	.byte	'reserved_0',0,1
	.word	349
	.byte	1,7,2,35,0,11
	.byte	'HERCLR',0,1
	.word	349
	.byte	1,6,2,35,0,11
	.byte	'PYERCLR',0,1
	.word	349
	.byte	1,5,2,35,0,11
	.byte	'CERCLR',0,1
	.word	349
	.byte	1,4,2,35,0,11
	.byte	'IFCFSCLR',0,1
	.word	349
	.byte	1,3,2,35,0,11
	.byte	'SMERCLR',0,1
	.word	349
	.byte	1,2,2,35,0,11
	.byte	'USMSFCLR',0,1
	.word	349
	.byte	1,1,2,35,0,11
	.byte	'PLERCLR',0,1
	.word	349
	.byte	1,0,2,35,0,11
	.byte	'USMCLR',0,1
	.word	349
	.byte	1,7,2,35,1,11
	.byte	'PARCLR',0,1
	.word	349
	.byte	1,6,2,35,1,11
	.byte	'TXTECLR',0,1
	.word	349
	.byte	1,5,2,35,1,11
	.byte	'SFOCLR',0,1
	.word	349
	.byte	1,4,2,35,1,11
	.byte	'SFUCLR',0,1
	.word	349
	.byte	1,3,2,35,1,11
	.byte	'reserved_13',0,4
	.word	264
	.byte	19,0,2,35,0,0,3
	.byte	'Ifx_HSCT_IRQCLR_Bits',0,5,212,1,3
	.word	10932
	.byte	10
	.byte	'_Ifx_HSCT_IRQEN_Bits',0,5,215,1,16,4,11
	.byte	'reserved_0',0,1
	.word	349
	.byte	1,7,2,35,0,11
	.byte	'HEREN',0,1
	.word	349
	.byte	1,6,2,35,0,11
	.byte	'PYEREN',0,1
	.word	349
	.byte	1,5,2,35,0,11
	.byte	'CEREN',0,1
	.word	349
	.byte	1,4,2,35,0,11
	.byte	'IFCFSEN',0,1
	.word	349
	.byte	1,3,2,35,0,11
	.byte	'SMEREN',0,1
	.word	349
	.byte	1,2,2,35,0,11
	.byte	'USMSFEN',0,1
	.word	349
	.byte	1,1,2,35,0,11
	.byte	'PLEREN',0,1
	.word	349
	.byte	1,0,2,35,0,11
	.byte	'USMEN',0,1
	.word	349
	.byte	1,7,2,35,1,11
	.byte	'PAREN',0,1
	.word	349
	.byte	1,6,2,35,1,11
	.byte	'TXTEEN',0,1
	.word	349
	.byte	1,5,2,35,1,11
	.byte	'SFOEN',0,1
	.word	349
	.byte	1,4,2,35,1,11
	.byte	'SFUEN',0,1
	.word	349
	.byte	1,3,2,35,1,11
	.byte	'reserved_13',0,4
	.word	264
	.byte	19,0,2,35,0,0,3
	.byte	'Ifx_HSCT_IRQEN_Bits',0,5,231,1,3
	.word	11260
	.byte	10
	.byte	'_Ifx_HSCT_KRST0_Bits',0,5,234,1,16,4,11
	.byte	'RST',0,1
	.word	349
	.byte	1,7,2,35,0,11
	.byte	'RSTSTAT',0,1
	.word	349
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	264
	.byte	30,0,2,35,0,0,3
	.byte	'Ifx_HSCT_KRST0_Bits',0,5,239,1,3
	.word	11574
	.byte	10
	.byte	'_Ifx_HSCT_KRST1_Bits',0,5,242,1,16,4,11
	.byte	'RST',0,1
	.word	349
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	264
	.byte	31,0,2,35,0,0,3
	.byte	'Ifx_HSCT_KRST1_Bits',0,5,246,1,3
	.word	11687
	.byte	10
	.byte	'_Ifx_HSCT_KRSTCLR_Bits',0,5,249,1,16,4,11
	.byte	'CLR',0,1
	.word	349
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	264
	.byte	31,0,2,35,0,0,3
	.byte	'Ifx_HSCT_KRSTCLR_Bits',0,5,253,1,3
	.word	11781
	.byte	10
	.byte	'_Ifx_HSCT_OCS_Bits',0,5,128,2,16,4,11
	.byte	'TGS',0,1
	.word	349
	.byte	2,6,2,35,0,11
	.byte	'TGB',0,1
	.word	349
	.byte	1,5,2,35,0,11
	.byte	'TG_P',0,1
	.word	349
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	264
	.byte	20,8,2,35,0,11
	.byte	'SUS',0,1
	.word	349
	.byte	4,4,2,35,3,11
	.byte	'SUS_P',0,1
	.word	349
	.byte	1,3,2,35,3,11
	.byte	'SUSSTA',0,1
	.word	349
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	349
	.byte	2,0,2,35,3,0,3
	.byte	'Ifx_HSCT_OCS_Bits',0,5,138,2,3
	.word	11879
	.byte	10
	.byte	'_Ifx_HSCT_SLEEPCTRL_Bits',0,5,141,2,16,4,11
	.byte	'SLPEN',0,1
	.word	349
	.byte	1,7,2,35,0,11
	.byte	'SLPCLKG',0,1
	.word	349
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	264
	.byte	30,0,2,35,0,0,3
	.byte	'Ifx_HSCT_SLEEPCTRL_Bits',0,5,146,2,3
	.word	12073
	.byte	10
	.byte	'_Ifx_HSCT_STAT_Bits',0,5,149,2,16,4,11
	.byte	'RX_PSIZE',0,1
	.word	349
	.byte	3,5,2,35,0,11
	.byte	'RX_CHANNEL',0,1
	.word	349
	.byte	4,1,2,35,0,11
	.byte	'RX_SLEEP',0,1
	.word	349
	.byte	1,0,2,35,0,11
	.byte	'TX_SLEEP',0,1
	.word	349
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	349
	.byte	3,4,2,35,1,11
	.byte	'TX_PSIZE',0,1
	.word	349
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	349
	.byte	1,0,2,35,1,11
	.byte	'TX_CHANNEL_TYPE',0,1
	.word	349
	.byte	4,4,2,35,2,11
	.byte	'reserved_20',0,1
	.word	349
	.byte	4,0,2,35,2,11
	.byte	'LIFCCMDR',0,1
	.word	349
	.byte	8,0,2,35,3,0,3
	.byte	'Ifx_HSCT_STAT_Bits',0,5,161,2,3
	.word	12196
	.byte	10
	.byte	'_Ifx_HSCT_STATPHY_Bits',0,5,164,2,16,4,11
	.byte	'PLOCK',0,1
	.word	349
	.byte	1,7,2,35,0,11
	.byte	'RXLSA',0,1
	.word	349
	.byte	1,6,2,35,0,11
	.byte	'TXLSA',0,1
	.word	349
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	264
	.byte	29,0,2,35,0,0,3
	.byte	'Ifx_HSCT_STATPHY_Bits',0,5,170,2,3
	.word	12468
	.byte	10
	.byte	'_Ifx_HSCT_USMR_Bits',0,5,173,2,16,4,11
	.byte	'USMR',0,4
	.word	264
	.byte	32,0,2,35,0,0,3
	.byte	'Ifx_HSCT_USMR_Bits',0,5,176,2,3
	.word	12602
	.byte	10
	.byte	'_Ifx_HSCT_USMS_Bits',0,5,179,2,16,4,11
	.byte	'USMS',0,4
	.word	264
	.byte	32,0,2,35,0,0,3
	.byte	'Ifx_HSCT_USMS_Bits',0,5,182,2,3
	.word	12673
	.byte	12,5,190,2,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	8599
	.byte	4,2,35,0,0,3
	.byte	'Ifx_HSCT_ACCEN0',0,5,195,2,3
	.word	12744
	.byte	12,5,198,2,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	9158
	.byte	4,2,35,0,0,3
	.byte	'Ifx_HSCT_ACCEN1',0,5,203,2,3
	.word	12809
	.byte	12,5,206,2,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	9237
	.byte	4,2,35,0,0,3
	.byte	'Ifx_HSCT_CLC',0,5,211,2,3
	.word	12874
	.byte	12,5,214,2,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	9380
	.byte	4,2,35,0,0,3
	.byte	'Ifx_HSCT_CONFIGPHY',0,5,219,2,3
	.word	12936
	.byte	12,5,222,2,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	9673
	.byte	4,2,35,0,0,3
	.byte	'Ifx_HSCT_CTSCTRL',0,5,227,2,3
	.word	13004
	.byte	12,5,230,2,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	9837
	.byte	4,2,35,0,0,3
	.byte	'Ifx_HSCT_DISABLE',0,5,235,2,3
	.word	13070
	.byte	12,5,238,2,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	9974
	.byte	4,2,35,0,0,3
	.byte	'Ifx_HSCT_ID',0,5,243,2,3
	.word	13136
	.byte	12,5,246,2,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	10083
	.byte	4,2,35,0,0,3
	.byte	'Ifx_HSCT_IFCTRL',0,5,251,2,3
	.word	13197
	.byte	12,5,254,2,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	10281
	.byte	4,2,35,0,0,3
	.byte	'Ifx_HSCT_IFSTAT',0,5,131,3,3
	.word	13262
	.byte	12,5,134,3,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	10400
	.byte	4,2,35,0,0,3
	.byte	'Ifx_HSCT_INIT',0,5,139,3,3
	.word	13327
	.byte	12,5,142,3,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	10646
	.byte	4,2,35,0,0,3
	.byte	'Ifx_HSCT_IRQ',0,5,147,3,3
	.word	13390
	.byte	12,5,150,3,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	10932
	.byte	4,2,35,0,0,3
	.byte	'Ifx_HSCT_IRQCLR',0,5,155,3,3
	.word	13452
	.byte	12,5,158,3,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	11260
	.byte	4,2,35,0,0,3
	.byte	'Ifx_HSCT_IRQEN',0,5,163,3,3
	.word	13517
	.byte	12,5,166,3,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	11574
	.byte	4,2,35,0,0,3
	.byte	'Ifx_HSCT_KRST0',0,5,171,3,3
	.word	13581
	.byte	12,5,174,3,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	11687
	.byte	4,2,35,0,0,3
	.byte	'Ifx_HSCT_KRST1',0,5,179,3,3
	.word	13645
	.byte	12,5,182,3,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	11781
	.byte	4,2,35,0,0,3
	.byte	'Ifx_HSCT_KRSTCLR',0,5,187,3,3
	.word	13709
	.byte	12,5,190,3,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	11879
	.byte	4,2,35,0,0,3
	.byte	'Ifx_HSCT_OCS',0,5,195,3,3
	.word	13775
	.byte	12,5,198,3,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	12073
	.byte	4,2,35,0,0,3
	.byte	'Ifx_HSCT_SLEEPCTRL',0,5,203,3,3
	.word	13837
	.byte	12,5,206,3,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	12196
	.byte	4,2,35,0,0,3
	.byte	'Ifx_HSCT_STAT',0,5,211,3,3
	.word	13905
	.byte	12,5,214,3,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	12468
	.byte	4,2,35,0,0,3
	.byte	'Ifx_HSCT_STATPHY',0,5,219,3,3
	.word	13968
	.byte	12,5,222,3,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	12602
	.byte	4,2,35,0,0,3
	.byte	'Ifx_HSCT_USMR',0,5,227,3,3
	.word	14034
	.byte	12,5,230,3,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	12673
	.byte	4,2,35,0,0,3
	.byte	'Ifx_HSCT_USMS',0,5,235,3,3
	.word	14097
	.byte	13,8
	.word	611
	.byte	14,0,0
.L10:
	.byte	15
	.word	14160
.L11:
	.byte	15
	.word	14160
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L4:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,36,0,3,8,11,15,62,15,0,0,3,22,0,3,8,58,15,59,15,57,15
	.byte	73,19,0,0,4,21,0,54,15,0,0,5,15,0,73,19,0,0,6,59,0,3,8,0,0,7,53,0,73,19,0,0,8,19,1,58,15,59,15,57,15,11
	.byte	15,0,0,9,13,0,3,8,73,19,11,15,56,9,0,0,10,19,1,3,8,58,15,59,15,57,15,11,15,0,0,11,13,0,3,8,11,15,73,19
	.byte	13,15,12,15,56,9,0,0,12,23,1,58,15,59,15,57,15,11,15,0,0,13,1,1,11,15,73,19,0,0,14,33,0,47,15,0,0,15,38
	.byte	0,73,19,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L5:
	.word	.L13-.L12
.L12:
	.half	3
	.word	.L15-.L14
.L14:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg',0
	.byte	0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxHssl_cfg.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Platform_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Ifx_Types.h',0
	.byte	0,0,0
	.byte	'IfxHssl_regdef.h',0,1,0,0
	.byte	'IfxHsct_regdef.h',0,1,0,0,0
.L15:
.L13:
	.sdecl	'.debug_info',debug,cluster('IfxHssl_cfg_hsctIndexMap')
	.sect	'.debug_info'
.L6:
	.word	271
	.half	3
	.word	.L7
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxHssl_cfg.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxHssl_cfg_hsctIndexMap',0,1,55,30
	.word	.L10
	.byte	1,5,3
	.word	IfxHssl_cfg_hsctIndexMap
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxHssl_cfg_hsctIndexMap')
	.sect	'.debug_abbrev'
.L7:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxHssl_cfg_hsslIndexMap')
	.sect	'.debug_info'
.L8:
	.word	271
	.half	3
	.word	.L9
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxHssl_cfg.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxHssl_cfg_hsslIndexMap',0,1,59,30
	.word	.L11
	.byte	1,5,3
	.word	IfxHssl_cfg_hsslIndexMap
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxHssl_cfg_hsslIndexMap')
	.sect	'.debug_abbrev'
.L9:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	; Module end
