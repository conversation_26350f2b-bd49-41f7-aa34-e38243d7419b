gsm_4g_user.o :	../code/user1/gsm_4g_user.c
../code/user1/gsm_4g_user.c :
gsm_4g_user.o :	..\code\user1\gsm_4g_user.h
..\code\user1\gsm_4g_user.h :
gsm_4g_user.o :	..\code\user1\gsm_4g_api.h
..\code\user1\gsm_4g_api.h :
gsm_4g_user.o :	"F:\ADS\AURIX-Studio-1.10.2\tools\Compilers\Tasking_1.1r8\ctc\include\stdint.h"
"F:\ADS\AURIX-Studio-1.10.2\tools\Compilers\Tasking_1.1r8\ctc\include\stdint.h" :
gsm_4g_user.o :	"F:\ADS\AURIX-Studio-1.10.2\tools\Compilers\Tasking_1.1r8\ctc\include\typeinfo.h"
"F:\ADS\AURIX-Studio-1.10.2\tools\Compilers\Tasking_1.1r8\ctc\include\typeinfo.h" :
gsm_4g_user.o :	"F:\ADS\AURIX-Studio-1.10.2\tools\Compilers\Tasking_1.1r8\ctc\include\stdbool.h"
"F:\ADS\AURIX-Studio-1.10.2\tools\Compilers\Tasking_1.1r8\ctc\include\stdbool.h" :
gsm_4g_user.o :	"F:\ADS\AURIX-Studio-1.10.2\tools\Compilers\Tasking_1.1r8\ctc\include\string.h"
"F:\ADS\AURIX-Studio-1.10.2\tools\Compilers\Tasking_1.1r8\ctc\include\string.h" :
gsm_4g_user.o :	"F:\ADS\AURIX-Studio-1.10.2\tools\Compilers\Tasking_1.1r8\ctc\include\stdio.h"
"F:\ADS\AURIX-Studio-1.10.2\tools\Compilers\Tasking_1.1r8\ctc\include\stdio.h" :
gsm_4g_user.o :	"F:\ADS\AURIX-Studio-1.10.2\tools\Compilers\Tasking_1.1r8\ctc\include\stdarg.h"
"F:\ADS\AURIX-Studio-1.10.2\tools\Compilers\Tasking_1.1r8\ctc\include\stdarg.h" :
gsm_4g_user.o :	"F:\ADS\AURIX-Studio-1.10.2\tools\Compilers\Tasking_1.1r8\ctc\include\stdlib.h"
"F:\ADS\AURIX-Studio-1.10.2\tools\Compilers\Tasking_1.1r8\ctc\include\stdlib.h" :
gsm_4g_user.o :	"F:\ADS\AURIX-Studio-1.10.2\tools\Compilers\Tasking_1.1r8\ctc\include\time.h"
"F:\ADS\AURIX-Studio-1.10.2\tools\Compilers\Tasking_1.1r8\ctc\include\time.h" :
gsm_4g_user.o :	..\code\user1\gsm_4g_error.h
..\code\user1\gsm_4g_error.h :
gsm_4g_user.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_driver\zf_driver_timer.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_driver\zf_driver_timer.h" :
gsm_4g_user.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_common\zf_common_typedef.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_common\zf_common_typedef.h" :
gsm_4g_user.o :	"F:\ADS\AURIX-Studio-1.10.2\tools\Compilers\Tasking_1.1r8\ctc\include\math.h"
"F:\ADS\AURIX-Studio-1.10.2\tools\Compilers\Tasking_1.1r8\ctc\include\math.h" :
gsm_4g_user.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Cpu\\Std\ifx_types.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Cpu\\Std\ifx_types.h" :
gsm_4g_user.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Platform\Tricore\Compilers\Compilers.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Platform\Tricore\Compilers\Compilers.h" :
gsm_4g_user.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Configurations\Ifx_Cfg.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Configurations\Ifx_Cfg.h" :
gsm_4g_user.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Platform\Tricore\Compilers\CompilerTasking.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Platform\Tricore\Compilers\CompilerTasking.h" :
gsm_4g_user.o :	"F:\ADS\AURIX-Studio-1.10.2\tools\Compilers\Tasking_1.1r8\ctc\include\stddef.h"
"F:\ADS\AURIX-Studio-1.10.2\tools\Compilers\Tasking_1.1r8\ctc\include\stddef.h" :
gsm_4g_user.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Cpu\\Std\Platform_Types.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Cpu\\Std\Platform_Types.h" :
gsm_4g_user.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Cpu\\Std\Ifx_TypesTasking.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Cpu\\Std\Ifx_TypesTasking.h" :
gsm_4g_user.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Cpu\\Std\PLATFORM_TYPES.H"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Cpu\\Std\PLATFORM_TYPES.H" :
