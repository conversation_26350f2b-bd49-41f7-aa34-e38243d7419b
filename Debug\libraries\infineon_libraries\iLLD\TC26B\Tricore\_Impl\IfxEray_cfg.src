	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc26692a --dep-file=IfxEray_cfg.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2\\\\xf_asr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxEray_cfg.src ../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxEray_cfg.c"
	.compiler_name		"ctc"
	;source	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxEray_cfg.c'

	
$TC16X
	
	.sdecl	'.rodata.IfxEray_cfg.IfxEray_cfg_indexMap',data,rom,cluster('IfxEray_cfg_indexMap')
	.sect	'.rodata.IfxEray_cfg.IfxEray_cfg_indexMap'
	.global	IfxEray_cfg_indexMap
	.align	4
IfxEray_cfg_indexMap:	.type	object
	.size	IfxEray_cfg_indexMap,8
	.word	-268320768
	.space	4
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L3:
	.word	34909
	.half	3
	.word	.L4
	.byte	4
.L2:
	.byte	1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxEray_cfg.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L5
	.byte	2
	.byte	'short int',0,2,5,3
	.byte	'__wchar_t',0,1,1,1
	.word	233
	.byte	2
	.byte	'unsigned int',0,4,7,3
	.byte	'__size_t',0,1,1,1
	.word	264
	.byte	2
	.byte	'int',0,4,5,3
	.byte	'__ptrdiff_t',0,1,1,1
	.word	297
	.byte	4,1,5
	.word	324
	.byte	3
	.byte	'__codeptr',0,1,1,1
	.word	326
	.byte	2
	.byte	'unsigned char',0,1,8,3
	.byte	'uint8',0,2,105,29
	.word	349
	.byte	2
	.byte	'unsigned short int',0,2,7,3
	.byte	'uint16',0,2,109,29
	.word	380
	.byte	2
	.byte	'unsigned long int',0,4,7,3
	.byte	'uint32',0,2,113,29
	.word	417
	.byte	3
	.byte	'sint16',0,2,126,29
	.word	233
	.byte	2
	.byte	'long int',0,4,5,3
	.byte	'sint32',0,2,131,1,29
	.word	468
	.byte	2
	.byte	'long long int',0,8,5,3
	.byte	'sint64',0,2,138,1,29
	.word	496
	.byte	2
	.byte	'float',0,4,4,3
	.byte	'float32',0,2,167,1,29
	.word	529
	.byte	6
	.byte	'void',0,5
	.word	555
	.byte	3
	.byte	'pvoid',0,3,57,28
	.word	561
	.byte	3
	.byte	'Ifx_TickTime',0,3,79,28
	.word	496
	.byte	7
	.word	555
	.byte	5
	.word	601
	.byte	8,3,143,1,9,8,9
	.byte	'module',0
	.word	606
	.byte	4,2,35,0,9
	.byte	'index',0
	.word	468
	.byte	4,2,35,4,0,3
	.byte	'IfxModule_IndexMap',0,3,147,1,3
	.word	611
	.byte	10
	.byte	'_Ifx_ERAY_ACCEN0_Bits',0,4,45,16,4,11
	.byte	'EN0',0,1
	.word	349
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	349
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	349
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	349
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	349
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	349
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	349
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	349
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	349
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	349
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	349
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	349
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	349
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	349
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	349
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	349
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	349
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	349
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	349
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	349
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	349
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	349
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	349
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	349
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	349
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	349
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	349
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	349
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	349
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	349
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	349
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	349
	.byte	1,0,2,35,3,0,3
	.byte	'Ifx_ERAY_ACCEN0_Bits',0,4,79,3
	.word	677
	.byte	10
	.byte	'_Ifx_ERAY_ACCEN1_Bits',0,4,82,16,4,11
	.byte	'reserved_0',0,4
	.word	264
	.byte	32,0,2,35,0,0,3
	.byte	'Ifx_ERAY_ACCEN1_Bits',0,4,85,3
	.word	1236
	.byte	10
	.byte	'_Ifx_ERAY_ACS_Bits',0,4,88,16,4,11
	.byte	'VFRA',0,1
	.word	349
	.byte	1,7,2,35,0,11
	.byte	'SEDA',0,1
	.word	349
	.byte	1,6,2,35,0,11
	.byte	'CEDA',0,1
	.word	349
	.byte	1,5,2,35,0,11
	.byte	'CIA',0,1
	.word	349
	.byte	1,4,2,35,0,11
	.byte	'SBVA',0,1
	.word	349
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	349
	.byte	3,0,2,35,0,11
	.byte	'VFRB',0,1
	.word	349
	.byte	1,7,2,35,1,11
	.byte	'SEDB',0,1
	.word	349
	.byte	1,6,2,35,1,11
	.byte	'CEDB',0,1
	.word	349
	.byte	1,5,2,35,1,11
	.byte	'CIB',0,1
	.word	349
	.byte	1,4,2,35,1,11
	.byte	'SBVB',0,1
	.word	349
	.byte	1,3,2,35,1,11
	.byte	'reserved_13',0,4
	.word	264
	.byte	19,0,2,35,0,0,3
	.byte	'Ifx_ERAY_ACS_Bits',0,4,102,3
	.word	1315
	.byte	10
	.byte	'_Ifx_ERAY_CCEV_Bits',0,4,105,16,4,11
	.byte	'CCFC',0,1
	.word	349
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	349
	.byte	2,2,2,35,0,11
	.byte	'ERRM',0,1
	.word	349
	.byte	2,0,2,35,0,11
	.byte	'PTAC',0,1
	.word	349
	.byte	5,3,2,35,1,11
	.byte	'reserved_13',0,4
	.word	264
	.byte	19,0,2,35,0,0,3
	.byte	'Ifx_ERAY_CCEV_Bits',0,4,112,3
	.word	1569
	.byte	10
	.byte	'_Ifx_ERAY_CCSV_Bits',0,4,115,16,4,11
	.byte	'POCS',0,1
	.word	349
	.byte	6,2,2,35,0,11
	.byte	'FSI',0,1
	.word	349
	.byte	1,1,2,35,0,11
	.byte	'HRQ',0,1
	.word	349
	.byte	1,0,2,35,0,11
	.byte	'SLM',0,1
	.word	349
	.byte	2,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	349
	.byte	2,4,2,35,1,11
	.byte	'CSNI',0,1
	.word	349
	.byte	1,3,2,35,1,11
	.byte	'CSAI',0,1
	.word	349
	.byte	1,2,2,35,1,11
	.byte	'CSI',0,1
	.word	349
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	349
	.byte	1,0,2,35,1,11
	.byte	'WSV',0,1
	.word	349
	.byte	3,5,2,35,2,11
	.byte	'RCA',0,1
	.word	349
	.byte	5,0,2,35,2,11
	.byte	'PSL',0,1
	.word	349
	.byte	6,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	349
	.byte	2,0,2,35,3,0,3
	.byte	'Ifx_ERAY_CCSV_Bits',0,4,130,1,3
	.word	1715
	.byte	10
	.byte	'_Ifx_ERAY_CLC_Bits',0,4,133,1,16,4,11
	.byte	'DISR',0,1
	.word	349
	.byte	1,7,2,35,0,11
	.byte	'DISS',0,1
	.word	349
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	349
	.byte	1,5,2,35,0,11
	.byte	'EDIS',0,1
	.word	349
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	349
	.byte	4,0,2,35,0,11
	.byte	'RMC',0,1
	.word	349
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	264
	.byte	21,0,2,35,0,0,3
	.byte	'Ifx_ERAY_CLC_Bits',0,4,142,1,3
	.word	1991
	.byte	10
	.byte	'_Ifx_ERAY_CREL_Bits',0,4,145,1,16,4,11
	.byte	'DAY',0,1
	.word	349
	.byte	8,0,2,35,0,11
	.byte	'MON',0,1
	.word	349
	.byte	8,0,2,35,1,11
	.byte	'YEAR',0,1
	.word	349
	.byte	4,4,2,35,2,11
	.byte	'SUBSTEP',0,1
	.word	349
	.byte	4,0,2,35,2,11
	.byte	'STEP',0,1
	.word	349
	.byte	4,4,2,35,3,11
	.byte	'REL',0,1
	.word	349
	.byte	4,0,2,35,3,0,3
	.byte	'Ifx_ERAY_CREL_Bits',0,4,153,1,3
	.word	2174
	.byte	10
	.byte	'_Ifx_ERAY_CUST1_Bits',0,4,156,1,16,4,11
	.byte	'INT0',0,1
	.word	349
	.byte	1,7,2,35,0,11
	.byte	'OEN',0,1
	.word	349
	.byte	1,6,2,35,0,11
	.byte	'IEN',0,1
	.word	349
	.byte	1,5,2,35,0,11
	.byte	'IBFS',0,1
	.word	349
	.byte	1,4,2,35,0,11
	.byte	'IBF1PAG',0,1
	.word	349
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	349
	.byte	2,1,2,35,0,11
	.byte	'IBF2PAG',0,1
	.word	349
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	349
	.byte	2,6,2,35,1,11
	.byte	'RISA',0,1
	.word	349
	.byte	2,4,2,35,1,11
	.byte	'RISB',0,1
	.word	349
	.byte	2,2,2,35,1,11
	.byte	'STPWTS',0,1
	.word	349
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	380
	.byte	16,0,2,35,2,0,3
	.byte	'Ifx_ERAY_CUST1_Bits',0,4,170,1,3
	.word	2325
	.byte	10
	.byte	'_Ifx_ERAY_CUST3_Bits',0,4,173,1,16,4,11
	.byte	'TO',0,4
	.word	264
	.byte	32,0,2,35,0,0,3
	.byte	'Ifx_ERAY_CUST3_Bits',0,4,176,1,3
	.word	2599
	.byte	10
	.byte	'_Ifx_ERAY_EIER_Bits',0,4,179,1,16,4,11
	.byte	'PEMCE',0,1
	.word	349
	.byte	1,7,2,35,0,11
	.byte	'CNAE',0,1
	.word	349
	.byte	1,6,2,35,0,11
	.byte	'SFBME',0,1
	.word	349
	.byte	1,5,2,35,0,11
	.byte	'SFOE',0,1
	.word	349
	.byte	1,4,2,35,0,11
	.byte	'CCFE',0,1
	.word	349
	.byte	1,3,2,35,0,11
	.byte	'CCLE',0,1
	.word	349
	.byte	1,2,2,35,0,11
	.byte	'EERRE',0,1
	.word	349
	.byte	1,1,2,35,0,11
	.byte	'RFOE',0,1
	.word	349
	.byte	1,0,2,35,0,11
	.byte	'EFAE',0,1
	.word	349
	.byte	1,7,2,35,1,11
	.byte	'IIBAE',0,1
	.word	349
	.byte	1,6,2,35,1,11
	.byte	'IOBAE',0,1
	.word	349
	.byte	1,5,2,35,1,11
	.byte	'MHFE',0,1
	.word	349
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	349
	.byte	4,0,2,35,1,11
	.byte	'EDAE',0,1
	.word	349
	.byte	1,7,2,35,2,11
	.byte	'LTVAE',0,1
	.word	349
	.byte	1,6,2,35,2,11
	.byte	'TABAE',0,1
	.word	349
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	349
	.byte	5,0,2,35,2,11
	.byte	'EDBE',0,1
	.word	349
	.byte	1,7,2,35,3,11
	.byte	'LTVBE',0,1
	.word	349
	.byte	1,6,2,35,3,11
	.byte	'TABBE',0,1
	.word	349
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	349
	.byte	5,0,2,35,3,0,3
	.byte	'Ifx_ERAY_EIER_Bits',0,4,202,1,3
	.word	2670
	.byte	10
	.byte	'_Ifx_ERAY_EIES_Bits',0,4,205,1,16,4,11
	.byte	'PEMCE',0,1
	.word	349
	.byte	1,7,2,35,0,11
	.byte	'CNAE',0,1
	.word	349
	.byte	1,6,2,35,0,11
	.byte	'SFBME',0,1
	.word	349
	.byte	1,5,2,35,0,11
	.byte	'SFOE',0,1
	.word	349
	.byte	1,4,2,35,0,11
	.byte	'CCFE',0,1
	.word	349
	.byte	1,3,2,35,0,11
	.byte	'CCLE',0,1
	.word	349
	.byte	1,2,2,35,0,11
	.byte	'EERRE',0,1
	.word	349
	.byte	1,1,2,35,0,11
	.byte	'RFOE',0,1
	.word	349
	.byte	1,0,2,35,0,11
	.byte	'EFAE',0,1
	.word	349
	.byte	1,7,2,35,1,11
	.byte	'IIBAE',0,1
	.word	349
	.byte	1,6,2,35,1,11
	.byte	'IOBAE',0,1
	.word	349
	.byte	1,5,2,35,1,11
	.byte	'MHFE',0,1
	.word	349
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	349
	.byte	4,0,2,35,1,11
	.byte	'EDAE',0,1
	.word	349
	.byte	1,7,2,35,2,11
	.byte	'LTVAE',0,1
	.word	349
	.byte	1,6,2,35,2,11
	.byte	'TABAE',0,1
	.word	349
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	349
	.byte	5,0,2,35,2,11
	.byte	'EDBE',0,1
	.word	349
	.byte	1,7,2,35,3,11
	.byte	'LTVBE',0,1
	.word	349
	.byte	1,6,2,35,3,11
	.byte	'TABBE',0,1
	.word	349
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	349
	.byte	5,0,2,35,3,0,3
	.byte	'Ifx_ERAY_EIES_Bits',0,4,228,1,3
	.word	3091
	.byte	10
	.byte	'_Ifx_ERAY_EILS_Bits',0,4,231,1,16,4,11
	.byte	'PEMCL',0,1
	.word	349
	.byte	1,7,2,35,0,11
	.byte	'CNAL',0,1
	.word	349
	.byte	1,6,2,35,0,11
	.byte	'SFBML',0,1
	.word	349
	.byte	1,5,2,35,0,11
	.byte	'SFOL',0,1
	.word	349
	.byte	1,4,2,35,0,11
	.byte	'CCFL',0,1
	.word	349
	.byte	1,3,2,35,0,11
	.byte	'CCLL',0,1
	.word	349
	.byte	1,2,2,35,0,11
	.byte	'EERRL',0,1
	.word	349
	.byte	1,1,2,35,0,11
	.byte	'RFOL',0,1
	.word	349
	.byte	1,0,2,35,0,11
	.byte	'EFAL',0,1
	.word	349
	.byte	1,7,2,35,1,11
	.byte	'IIBAL',0,1
	.word	349
	.byte	1,6,2,35,1,11
	.byte	'IOBAL',0,1
	.word	349
	.byte	1,5,2,35,1,11
	.byte	'MHFL',0,1
	.word	349
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	349
	.byte	4,0,2,35,1,11
	.byte	'EDAL',0,1
	.word	349
	.byte	1,7,2,35,2,11
	.byte	'LTVAL',0,1
	.word	349
	.byte	1,6,2,35,2,11
	.byte	'TABAL',0,1
	.word	349
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	349
	.byte	5,0,2,35,2,11
	.byte	'EDBL',0,1
	.word	349
	.byte	1,7,2,35,3,11
	.byte	'LTVBL',0,1
	.word	349
	.byte	1,6,2,35,3,11
	.byte	'TABBL',0,1
	.word	349
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	349
	.byte	5,0,2,35,3,0,3
	.byte	'Ifx_ERAY_EILS_Bits',0,4,254,1,3
	.word	3512
	.byte	10
	.byte	'_Ifx_ERAY_EIR_Bits',0,4,129,2,16,4,11
	.byte	'PEMC',0,1
	.word	349
	.byte	1,7,2,35,0,11
	.byte	'CNA',0,1
	.word	349
	.byte	1,6,2,35,0,11
	.byte	'SFBM',0,1
	.word	349
	.byte	1,5,2,35,0,11
	.byte	'SFO',0,1
	.word	349
	.byte	1,4,2,35,0,11
	.byte	'CCF',0,1
	.word	349
	.byte	1,3,2,35,0,11
	.byte	'CCL',0,1
	.word	349
	.byte	1,2,2,35,0,11
	.byte	'EERR',0,1
	.word	349
	.byte	1,1,2,35,0,11
	.byte	'RFO',0,1
	.word	349
	.byte	1,0,2,35,0,11
	.byte	'EFA',0,1
	.word	349
	.byte	1,7,2,35,1,11
	.byte	'IIBA',0,1
	.word	349
	.byte	1,6,2,35,1,11
	.byte	'IOBA',0,1
	.word	349
	.byte	1,5,2,35,1,11
	.byte	'MHF',0,1
	.word	349
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	349
	.byte	4,0,2,35,1,11
	.byte	'EDA',0,1
	.word	349
	.byte	1,7,2,35,2,11
	.byte	'LTVA',0,1
	.word	349
	.byte	1,6,2,35,2,11
	.byte	'TABA',0,1
	.word	349
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	349
	.byte	5,0,2,35,2,11
	.byte	'EDB',0,1
	.word	349
	.byte	1,7,2,35,3,11
	.byte	'LTVB',0,1
	.word	349
	.byte	1,6,2,35,3,11
	.byte	'TABB',0,1
	.word	349
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	349
	.byte	5,0,2,35,3,0,3
	.byte	'Ifx_ERAY_EIR_Bits',0,4,152,2,3
	.word	3933
	.byte	10
	.byte	'_Ifx_ERAY_ENDN_Bits',0,4,155,2,16,4,11
	.byte	'ETV',0,4
	.word	264
	.byte	32,0,2,35,0,0,3
	.byte	'Ifx_ERAY_ENDN_Bits',0,4,158,2,3
	.word	4334
	.byte	10
	.byte	'_Ifx_ERAY_ESID_Bits',0,4,161,2,16,4,11
	.byte	'EID',0,2
	.word	380
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	349
	.byte	4,2,2,35,1,11
	.byte	'RXEA',0,1
	.word	349
	.byte	1,1,2,35,1,11
	.byte	'RXEB',0,1
	.word	349
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	380
	.byte	16,0,2,35,2,0,3
	.byte	'Ifx_ERAY_ESID_Bits',0,4,168,2,3
	.word	4404
	.byte	10
	.byte	'_Ifx_ERAY_FCL_Bits',0,4,171,2,16,4,11
	.byte	'CL',0,1
	.word	349
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	264
	.byte	24,0,2,35,0,0,3
	.byte	'Ifx_ERAY_FCL_Bits',0,4,175,2,3
	.word	4552
	.byte	10
	.byte	'_Ifx_ERAY_FRF_Bits',0,4,178,2,16,4,11
	.byte	'CH',0,1
	.word	349
	.byte	2,6,2,35,0,11
	.byte	'FID',0,2
	.word	380
	.byte	11,3,2,35,0,11
	.byte	'reserved_13',0,1
	.word	349
	.byte	3,0,2,35,1,11
	.byte	'CYF',0,1
	.word	349
	.byte	7,1,2,35,2,11
	.byte	'RSS',0,1
	.word	349
	.byte	1,0,2,35,2,11
	.byte	'RNF',0,1
	.word	349
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	349
	.byte	7,0,2,35,3,0,3
	.byte	'Ifx_ERAY_FRF_Bits',0,4,187,2,3
	.word	4641
	.byte	10
	.byte	'_Ifx_ERAY_FRFM_Bits',0,4,190,2,16,4,11
	.byte	'reserved_0',0,1
	.word	349
	.byte	2,6,2,35,0,11
	.byte	'MFID',0,2
	.word	380
	.byte	11,3,2,35,0,11
	.byte	'reserved_13',0,4
	.word	264
	.byte	19,0,2,35,0,0,3
	.byte	'Ifx_ERAY_FRFM_Bits',0,4,195,2,3
	.word	4814
	.byte	10
	.byte	'_Ifx_ERAY_FSR_Bits',0,4,198,2,16,4,11
	.byte	'RFNE',0,1
	.word	349
	.byte	1,7,2,35,0,11
	.byte	'RFCL',0,1
	.word	349
	.byte	1,6,2,35,0,11
	.byte	'RFO',0,1
	.word	349
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	349
	.byte	5,0,2,35,0,11
	.byte	'RFFL',0,1
	.word	349
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	380
	.byte	16,0,2,35,2,0,3
	.byte	'Ifx_ERAY_FSR_Bits',0,4,206,2,3
	.word	4930
	.byte	10
	.byte	'_Ifx_ERAY_GTUC01_Bits',0,4,209,2,16,4,11
	.byte	'UT',0,4
	.word	264
	.byte	20,12,2,35,0,11
	.byte	'reserved_20',0,2
	.word	380
	.byte	12,0,2,35,2,0,3
	.byte	'Ifx_ERAY_GTUC01_Bits',0,4,213,2,3
	.word	5091
	.byte	10
	.byte	'_Ifx_ERAY_GTUC02_Bits',0,4,216,2,16,4,11
	.byte	'MPC',0,2
	.word	380
	.byte	14,2,2,35,0,11
	.byte	'reserved_14',0,1
	.word	349
	.byte	2,0,2,35,1,11
	.byte	'SNM',0,1
	.word	349
	.byte	4,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	380
	.byte	12,0,2,35,2,0,3
	.byte	'Ifx_ERAY_GTUC02_Bits',0,4,222,2,3
	.word	5187
	.byte	10
	.byte	'_Ifx_ERAY_GTUC03_Bits',0,4,225,2,16,4,11
	.byte	'UIOA',0,1
	.word	349
	.byte	8,0,2,35,0,11
	.byte	'UIOB',0,1
	.word	349
	.byte	8,0,2,35,1,11
	.byte	'MIOA',0,1
	.word	349
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	349
	.byte	1,0,2,35,2,11
	.byte	'MIOB',0,1
	.word	349
	.byte	7,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	349
	.byte	1,0,2,35,3,0,3
	.byte	'Ifx_ERAY_GTUC03_Bits',0,4,233,2,3
	.word	5322
	.byte	10
	.byte	'_Ifx_ERAY_GTUC04_Bits',0,4,236,2,16,4,11
	.byte	'NIT',0,2
	.word	380
	.byte	14,2,2,35,0,11
	.byte	'reserved_14',0,1
	.word	349
	.byte	2,0,2,35,1,11
	.byte	'OCS',0,2
	.word	380
	.byte	14,2,2,35,2,11
	.byte	'reserved_30',0,1
	.word	349
	.byte	2,0,2,35,3,0,3
	.byte	'Ifx_ERAY_GTUC04_Bits',0,4,242,2,3
	.word	5491
	.byte	10
	.byte	'_Ifx_ERAY_GTUC05_Bits',0,4,245,2,16,4,11
	.byte	'DCA',0,1
	.word	349
	.byte	8,0,2,35,0,11
	.byte	'DCB',0,1
	.word	349
	.byte	8,0,2,35,1,11
	.byte	'CDD',0,1
	.word	349
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	349
	.byte	3,0,2,35,2,11
	.byte	'DEC',0,1
	.word	349
	.byte	8,0,2,35,3,0,3
	.byte	'Ifx_ERAY_GTUC05_Bits',0,4,252,2,3
	.word	5626
	.byte	10
	.byte	'_Ifx_ERAY_GTUC06_Bits',0,4,255,2,16,4,11
	.byte	'ASR',0,2
	.word	380
	.byte	11,5,2,35,0,11
	.byte	'reserved_11',0,1
	.word	349
	.byte	5,0,2,35,1,11
	.byte	'MOD',0,2
	.word	380
	.byte	11,5,2,35,2,11
	.byte	'reserved_27',0,1
	.word	349
	.byte	5,0,2,35,3,0,3
	.byte	'Ifx_ERAY_GTUC06_Bits',0,4,133,3,3
	.word	5768
	.byte	10
	.byte	'_Ifx_ERAY_GTUC07_Bits',0,4,136,3,16,4,11
	.byte	'SSL',0,2
	.word	380
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	349
	.byte	6,0,2,35,1,11
	.byte	'NSS',0,2
	.word	380
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	349
	.byte	6,0,2,35,3,0,3
	.byte	'Ifx_ERAY_GTUC07_Bits',0,4,142,3,3
	.word	5903
	.byte	10
	.byte	'_Ifx_ERAY_GTUC08_Bits',0,4,145,3,16,4,11
	.byte	'MSL',0,1
	.word	349
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,2
	.word	380
	.byte	10,0,2,35,0,11
	.byte	'NMS',0,2
	.word	380
	.byte	13,3,2,35,2,11
	.byte	'reserved_29',0,1
	.word	349
	.byte	3,0,2,35,3,0,3
	.byte	'Ifx_ERAY_GTUC08_Bits',0,4,151,3,3
	.word	6038
	.byte	10
	.byte	'_Ifx_ERAY_GTUC09_Bits',0,4,154,3,16,4,11
	.byte	'APO',0,1
	.word	349
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	349
	.byte	2,0,2,35,0,11
	.byte	'MAPO',0,1
	.word	349
	.byte	5,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	349
	.byte	3,0,2,35,1,11
	.byte	'DSI',0,1
	.word	349
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	380
	.byte	14,0,2,35,2,0,3
	.byte	'Ifx_ERAY_GTUC09_Bits',0,4,162,3,3
	.word	6172
	.byte	10
	.byte	'_Ifx_ERAY_GTUC10_Bits',0,4,165,3,16,4,11
	.byte	'MOC',0,2
	.word	380
	.byte	14,2,2,35,0,11
	.byte	'reserved_14',0,1
	.word	349
	.byte	2,0,2,35,1,11
	.byte	'MRC',0,2
	.word	380
	.byte	11,5,2,35,2,11
	.byte	'reserved_27',0,1
	.word	349
	.byte	5,0,2,35,3,0,3
	.byte	'Ifx_ERAY_GTUC10_Bits',0,4,171,3,3
	.word	6345
	.byte	10
	.byte	'_Ifx_ERAY_GTUC11_Bits',0,4,174,3,16,4,11
	.byte	'EOCC',0,1
	.word	349
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	349
	.byte	6,0,2,35,0,11
	.byte	'ERCC',0,1
	.word	349
	.byte	2,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	349
	.byte	6,0,2,35,1,11
	.byte	'EOC',0,1
	.word	349
	.byte	3,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	349
	.byte	5,0,2,35,2,11
	.byte	'ERC',0,1
	.word	349
	.byte	3,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	349
	.byte	5,0,2,35,3,0,3
	.byte	'Ifx_ERAY_GTUC11_Bits',0,4,184,3,3
	.word	6480
	.byte	10
	.byte	'_Ifx_ERAY_IBCM_Bits',0,4,187,3,16,4,11
	.byte	'LHSH',0,1
	.word	349
	.byte	1,7,2,35,0,11
	.byte	'LDSH',0,1
	.word	349
	.byte	1,6,2,35,0,11
	.byte	'STXRH',0,1
	.word	349
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,2
	.word	380
	.byte	13,0,2,35,0,11
	.byte	'LHSS',0,1
	.word	349
	.byte	1,7,2,35,2,11
	.byte	'LDSS',0,1
	.word	349
	.byte	1,6,2,35,2,11
	.byte	'STXRS',0,1
	.word	349
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,2
	.word	380
	.byte	13,0,2,35,2,0,3
	.byte	'Ifx_ERAY_IBCM_Bits',0,4,197,3,3
	.word	6692
	.byte	10
	.byte	'_Ifx_ERAY_IBCR_Bits',0,4,200,3,16,4,11
	.byte	'IBRH',0,1
	.word	349
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	380
	.byte	8,1,2,35,0,11
	.byte	'IBSYH',0,1
	.word	349
	.byte	1,0,2,35,1,11
	.byte	'IBRS',0,1
	.word	349
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	380
	.byte	8,1,2,35,2,11
	.byte	'IBSYS',0,1
	.word	349
	.byte	1,0,2,35,3,0,3
	.byte	'Ifx_ERAY_IBCR_Bits',0,4,208,3,3
	.word	6890
	.byte	10
	.byte	'_Ifx_ERAY_ID_Bits',0,4,211,3,16,4,11
	.byte	'MODREV',0,1
	.word	349
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	349
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	380
	.byte	16,0,2,35,2,0,3
	.byte	'Ifx_ERAY_ID_Bits',0,4,216,3,3
	.word	7056
	.byte	10
	.byte	'_Ifx_ERAY_ILE_Bits',0,4,219,3,16,4,11
	.byte	'EINT0',0,1
	.word	349
	.byte	1,7,2,35,0,11
	.byte	'EINT1',0,1
	.word	349
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	264
	.byte	30,0,2,35,0,0,3
	.byte	'Ifx_ERAY_ILE_Bits',0,4,224,3,3
	.word	7165
	.byte	10
	.byte	'_Ifx_ERAY_KRST0_Bits',0,4,227,3,16,4,11
	.byte	'RST',0,1
	.word	349
	.byte	1,7,2,35,0,11
	.byte	'RSTSTAT',0,1
	.word	349
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	264
	.byte	30,0,2,35,0,0,3
	.byte	'Ifx_ERAY_KRST0_Bits',0,4,232,3,3
	.word	7274
	.byte	10
	.byte	'_Ifx_ERAY_KRST1_Bits',0,4,235,3,16,4,11
	.byte	'RST',0,1
	.word	349
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	264
	.byte	31,0,2,35,0,0,3
	.byte	'Ifx_ERAY_KRST1_Bits',0,4,239,3,3
	.word	7387
	.byte	10
	.byte	'_Ifx_ERAY_KRSTCLR_Bits',0,4,242,3,16,4,11
	.byte	'CLR',0,1
	.word	349
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	264
	.byte	31,0,2,35,0,0,3
	.byte	'Ifx_ERAY_KRSTCLR_Bits',0,4,246,3,3
	.word	7481
	.byte	10
	.byte	'_Ifx_ERAY_LCK_Bits',0,4,249,3,16,4,11
	.byte	'CLK',0,1
	.word	349
	.byte	8,0,2,35,0,11
	.byte	'TMK',0,1
	.word	349
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	380
	.byte	16,0,2,35,2,0,3
	.byte	'Ifx_ERAY_LCK_Bits',0,4,254,3,3
	.word	7579
	.byte	10
	.byte	'_Ifx_ERAY_LDTS_Bits',0,4,129,4,16,4,11
	.byte	'LDTA',0,2
	.word	380
	.byte	11,5,2,35,0,11
	.byte	'reserved_11',0,1
	.word	349
	.byte	5,0,2,35,1,11
	.byte	'LDTB',0,2
	.word	380
	.byte	11,5,2,35,2,11
	.byte	'reserved_27',0,1
	.word	349
	.byte	5,0,2,35,3,0,3
	.byte	'Ifx_ERAY_LDTS_Bits',0,4,135,4,3
	.word	7685
	.byte	10
	.byte	'_Ifx_ERAY_MBS_Bits',0,4,138,4,16,4,11
	.byte	'VFRA',0,1
	.word	349
	.byte	1,7,2,35,0,11
	.byte	'VFRB',0,1
	.word	349
	.byte	1,6,2,35,0,11
	.byte	'SEOA',0,1
	.word	349
	.byte	1,5,2,35,0,11
	.byte	'SEOB',0,1
	.word	349
	.byte	1,4,2,35,0,11
	.byte	'CEOA',0,1
	.word	349
	.byte	1,3,2,35,0,11
	.byte	'CEOB',0,1
	.word	349
	.byte	1,2,2,35,0,11
	.byte	'SVOA',0,1
	.word	349
	.byte	1,1,2,35,0,11
	.byte	'SVOB',0,1
	.word	349
	.byte	1,0,2,35,0,11
	.byte	'TCIA',0,1
	.word	349
	.byte	1,7,2,35,1,11
	.byte	'TCIB',0,1
	.word	349
	.byte	1,6,2,35,1,11
	.byte	'ESA',0,1
	.word	349
	.byte	1,5,2,35,1,11
	.byte	'ESB',0,1
	.word	349
	.byte	1,4,2,35,1,11
	.byte	'MLST',0,1
	.word	349
	.byte	1,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	349
	.byte	1,2,2,35,1,11
	.byte	'FTA',0,1
	.word	349
	.byte	1,1,2,35,1,11
	.byte	'FTB',0,1
	.word	349
	.byte	1,0,2,35,1,11
	.byte	'CCS',0,1
	.word	349
	.byte	6,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	349
	.byte	2,0,2,35,2,11
	.byte	'RCIS',0,1
	.word	349
	.byte	1,7,2,35,3,11
	.byte	'SFIS',0,1
	.word	349
	.byte	1,6,2,35,3,11
	.byte	'SYNS',0,1
	.word	349
	.byte	1,5,2,35,3,11
	.byte	'NFIS',0,1
	.word	349
	.byte	1,4,2,35,3,11
	.byte	'PPIS',0,1
	.word	349
	.byte	1,3,2,35,3,11
	.byte	'RESS',0,1
	.word	349
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	349
	.byte	2,0,2,35,3,0,3
	.byte	'Ifx_ERAY_MBS_Bits',0,4,165,4,3
	.word	7818
	.byte	10
	.byte	'_Ifx_ERAY_MBSC1_Bits',0,4,168,4,16,4,11
	.byte	'MBC0',0,1
	.word	349
	.byte	1,7,2,35,0,11
	.byte	'MBC1',0,1
	.word	349
	.byte	1,6,2,35,0,11
	.byte	'MBC2',0,1
	.word	349
	.byte	1,5,2,35,0,11
	.byte	'MBC3',0,1
	.word	349
	.byte	1,4,2,35,0,11
	.byte	'MBC4',0,1
	.word	349
	.byte	1,3,2,35,0,11
	.byte	'MBC5',0,1
	.word	349
	.byte	1,2,2,35,0,11
	.byte	'MBC6',0,1
	.word	349
	.byte	1,1,2,35,0,11
	.byte	'MBC7',0,1
	.word	349
	.byte	1,0,2,35,0,11
	.byte	'MBC8',0,1
	.word	349
	.byte	1,7,2,35,1,11
	.byte	'MBC9',0,1
	.word	349
	.byte	1,6,2,35,1,11
	.byte	'MBC10',0,1
	.word	349
	.byte	1,5,2,35,1,11
	.byte	'MBC11',0,1
	.word	349
	.byte	1,4,2,35,1,11
	.byte	'MBC12',0,1
	.word	349
	.byte	1,3,2,35,1,11
	.byte	'MBC13',0,1
	.word	349
	.byte	1,2,2,35,1,11
	.byte	'MBC14',0,1
	.word	349
	.byte	1,1,2,35,1,11
	.byte	'MBC15',0,1
	.word	349
	.byte	1,0,2,35,1,11
	.byte	'MBC16',0,1
	.word	349
	.byte	1,7,2,35,2,11
	.byte	'MBC17',0,1
	.word	349
	.byte	1,6,2,35,2,11
	.byte	'MBC18',0,1
	.word	349
	.byte	1,5,2,35,2,11
	.byte	'MBC19',0,1
	.word	349
	.byte	1,4,2,35,2,11
	.byte	'MBC20',0,1
	.word	349
	.byte	1,3,2,35,2,11
	.byte	'MBC21',0,1
	.word	349
	.byte	1,2,2,35,2,11
	.byte	'MBC22',0,1
	.word	349
	.byte	1,1,2,35,2,11
	.byte	'MBC23',0,1
	.word	349
	.byte	1,0,2,35,2,11
	.byte	'MBC24',0,1
	.word	349
	.byte	1,7,2,35,3,11
	.byte	'MBC25',0,1
	.word	349
	.byte	1,6,2,35,3,11
	.byte	'MBC26',0,1
	.word	349
	.byte	1,5,2,35,3,11
	.byte	'MBC27',0,1
	.word	349
	.byte	1,4,2,35,3,11
	.byte	'MBC28',0,1
	.word	349
	.byte	1,3,2,35,3,11
	.byte	'MBC29',0,1
	.word	349
	.byte	1,2,2,35,3,11
	.byte	'MBC30',0,1
	.word	349
	.byte	1,1,2,35,3,11
	.byte	'MBC31',0,1
	.word	349
	.byte	1,0,2,35,3,0,3
	.byte	'Ifx_ERAY_MBSC1_Bits',0,4,202,4,3
	.word	8287
	.byte	10
	.byte	'_Ifx_ERAY_MBSC2_Bits',0,4,205,4,16,4,11
	.byte	'MBC32',0,1
	.word	349
	.byte	1,7,2,35,0,11
	.byte	'MBC33',0,1
	.word	349
	.byte	1,6,2,35,0,11
	.byte	'MBC34',0,1
	.word	349
	.byte	1,5,2,35,0,11
	.byte	'MBC35',0,1
	.word	349
	.byte	1,4,2,35,0,11
	.byte	'MBC36',0,1
	.word	349
	.byte	1,3,2,35,0,11
	.byte	'MBC37',0,1
	.word	349
	.byte	1,2,2,35,0,11
	.byte	'MBC38',0,1
	.word	349
	.byte	1,1,2,35,0,11
	.byte	'MBC39',0,1
	.word	349
	.byte	1,0,2,35,0,11
	.byte	'MBC40',0,1
	.word	349
	.byte	1,7,2,35,1,11
	.byte	'MBC41',0,1
	.word	349
	.byte	1,6,2,35,1,11
	.byte	'MBC42',0,1
	.word	349
	.byte	1,5,2,35,1,11
	.byte	'MBC43',0,1
	.word	349
	.byte	1,4,2,35,1,11
	.byte	'MBC44',0,1
	.word	349
	.byte	1,3,2,35,1,11
	.byte	'MBC45',0,1
	.word	349
	.byte	1,2,2,35,1,11
	.byte	'MBC46',0,1
	.word	349
	.byte	1,1,2,35,1,11
	.byte	'MBC47',0,1
	.word	349
	.byte	1,0,2,35,1,11
	.byte	'MBC48',0,1
	.word	349
	.byte	1,7,2,35,2,11
	.byte	'MBC49',0,1
	.word	349
	.byte	1,6,2,35,2,11
	.byte	'MBC50',0,1
	.word	349
	.byte	1,5,2,35,2,11
	.byte	'MBC51',0,1
	.word	349
	.byte	1,4,2,35,2,11
	.byte	'MBC52',0,1
	.word	349
	.byte	1,3,2,35,2,11
	.byte	'MBC53',0,1
	.word	349
	.byte	1,2,2,35,2,11
	.byte	'MBC54',0,1
	.word	349
	.byte	1,1,2,35,2,11
	.byte	'MBC55',0,1
	.word	349
	.byte	1,0,2,35,2,11
	.byte	'MBC56',0,1
	.word	349
	.byte	1,7,2,35,3,11
	.byte	'MBC57',0,1
	.word	349
	.byte	1,6,2,35,3,11
	.byte	'MBC58',0,1
	.word	349
	.byte	1,5,2,35,3,11
	.byte	'MBC59',0,1
	.word	349
	.byte	1,4,2,35,3,11
	.byte	'MBC60',0,1
	.word	349
	.byte	1,3,2,35,3,11
	.byte	'MBC61',0,1
	.word	349
	.byte	1,2,2,35,3,11
	.byte	'MBC62',0,1
	.word	349
	.byte	1,1,2,35,3,11
	.byte	'MBC63',0,1
	.word	349
	.byte	1,0,2,35,3,0,3
	.byte	'Ifx_ERAY_MBSC2_Bits',0,4,239,4,3
	.word	8878
	.byte	10
	.byte	'_Ifx_ERAY_MBSC3_Bits',0,4,242,4,16,4,11
	.byte	'MBC64',0,1
	.word	349
	.byte	1,7,2,35,0,11
	.byte	'MBC65',0,1
	.word	349
	.byte	1,6,2,35,0,11
	.byte	'MBC66',0,1
	.word	349
	.byte	1,5,2,35,0,11
	.byte	'MBC67',0,1
	.word	349
	.byte	1,4,2,35,0,11
	.byte	'MBC68',0,1
	.word	349
	.byte	1,3,2,35,0,11
	.byte	'MBC69',0,1
	.word	349
	.byte	1,2,2,35,0,11
	.byte	'MBC70',0,1
	.word	349
	.byte	1,1,2,35,0,11
	.byte	'MBC71',0,1
	.word	349
	.byte	1,0,2,35,0,11
	.byte	'MBC72',0,1
	.word	349
	.byte	1,7,2,35,1,11
	.byte	'MBC73',0,1
	.word	349
	.byte	1,6,2,35,1,11
	.byte	'MBC74',0,1
	.word	349
	.byte	1,5,2,35,1,11
	.byte	'MBC75',0,1
	.word	349
	.byte	1,4,2,35,1,11
	.byte	'MBC76',0,1
	.word	349
	.byte	1,3,2,35,1,11
	.byte	'MBC77',0,1
	.word	349
	.byte	1,2,2,35,1,11
	.byte	'MBC78',0,1
	.word	349
	.byte	1,1,2,35,1,11
	.byte	'MBC79',0,1
	.word	349
	.byte	1,0,2,35,1,11
	.byte	'MBC80',0,1
	.word	349
	.byte	1,7,2,35,2,11
	.byte	'MBC81',0,1
	.word	349
	.byte	1,6,2,35,2,11
	.byte	'MBC82',0,1
	.word	349
	.byte	1,5,2,35,2,11
	.byte	'MBC83',0,1
	.word	349
	.byte	1,4,2,35,2,11
	.byte	'MBC84',0,1
	.word	349
	.byte	1,3,2,35,2,11
	.byte	'MBC85',0,1
	.word	349
	.byte	1,2,2,35,2,11
	.byte	'MBC86',0,1
	.word	349
	.byte	1,1,2,35,2,11
	.byte	'MBC87',0,1
	.word	349
	.byte	1,0,2,35,2,11
	.byte	'MBC88',0,1
	.word	349
	.byte	1,7,2,35,3,11
	.byte	'MBC89',0,1
	.word	349
	.byte	1,6,2,35,3,11
	.byte	'MBC90',0,1
	.word	349
	.byte	1,5,2,35,3,11
	.byte	'MBC91',0,1
	.word	349
	.byte	1,4,2,35,3,11
	.byte	'MBC92',0,1
	.word	349
	.byte	1,3,2,35,3,11
	.byte	'MBC93',0,1
	.word	349
	.byte	1,2,2,35,3,11
	.byte	'MBC94',0,1
	.word	349
	.byte	1,1,2,35,3,11
	.byte	'MBC95',0,1
	.word	349
	.byte	1,0,2,35,3,0,3
	.byte	'Ifx_ERAY_MBSC3_Bits',0,4,148,5,3
	.word	9479
	.byte	10
	.byte	'_Ifx_ERAY_MBSC4_Bits',0,4,151,5,16,4,11
	.byte	'MBC96',0,1
	.word	349
	.byte	1,7,2,35,0,11
	.byte	'MBC97',0,1
	.word	349
	.byte	1,6,2,35,0,11
	.byte	'MBC98',0,1
	.word	349
	.byte	1,5,2,35,0,11
	.byte	'MBC99',0,1
	.word	349
	.byte	1,4,2,35,0,11
	.byte	'MBC100',0,1
	.word	349
	.byte	1,3,2,35,0,11
	.byte	'MBC101',0,1
	.word	349
	.byte	1,2,2,35,0,11
	.byte	'MBC102',0,1
	.word	349
	.byte	1,1,2,35,0,11
	.byte	'MBC103',0,1
	.word	349
	.byte	1,0,2,35,0,11
	.byte	'MBC104',0,1
	.word	349
	.byte	1,7,2,35,1,11
	.byte	'MBC105',0,1
	.word	349
	.byte	1,6,2,35,1,11
	.byte	'MBC106',0,1
	.word	349
	.byte	1,5,2,35,1,11
	.byte	'MBC107',0,1
	.word	349
	.byte	1,4,2,35,1,11
	.byte	'MBC108',0,1
	.word	349
	.byte	1,3,2,35,1,11
	.byte	'MBC109',0,1
	.word	349
	.byte	1,2,2,35,1,11
	.byte	'MBC110',0,1
	.word	349
	.byte	1,1,2,35,1,11
	.byte	'MBC111',0,1
	.word	349
	.byte	1,0,2,35,1,11
	.byte	'MBC112',0,1
	.word	349
	.byte	1,7,2,35,2,11
	.byte	'MBC113',0,1
	.word	349
	.byte	1,6,2,35,2,11
	.byte	'MBC114',0,1
	.word	349
	.byte	1,5,2,35,2,11
	.byte	'MBC115',0,1
	.word	349
	.byte	1,4,2,35,2,11
	.byte	'MBC116',0,1
	.word	349
	.byte	1,3,2,35,2,11
	.byte	'MBC117',0,1
	.word	349
	.byte	1,2,2,35,2,11
	.byte	'MBC118',0,1
	.word	349
	.byte	1,1,2,35,2,11
	.byte	'MBC119',0,1
	.word	349
	.byte	1,0,2,35,2,11
	.byte	'MBC120',0,1
	.word	349
	.byte	1,7,2,35,3,11
	.byte	'MBC121',0,1
	.word	349
	.byte	1,6,2,35,3,11
	.byte	'MBC122',0,1
	.word	349
	.byte	1,5,2,35,3,11
	.byte	'MBC123',0,1
	.word	349
	.byte	1,4,2,35,3,11
	.byte	'MBC124',0,1
	.word	349
	.byte	1,3,2,35,3,11
	.byte	'MBC125',0,1
	.word	349
	.byte	1,2,2,35,3,11
	.byte	'MBC126',0,1
	.word	349
	.byte	1,1,2,35,3,11
	.byte	'MBC127',0,1
	.word	349
	.byte	1,0,2,35,3,0,3
	.byte	'Ifx_ERAY_MBSC4_Bits',0,4,185,5,3
	.word	10080
	.byte	10
	.byte	'_Ifx_ERAY_MHDC_Bits',0,4,188,5,16,4,11
	.byte	'SFDL',0,1
	.word	349
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	380
	.byte	9,0,2,35,0,11
	.byte	'SLT',0,2
	.word	380
	.byte	13,3,2,35,2,11
	.byte	'reserved_29',0,1
	.word	349
	.byte	3,0,2,35,3,0,3
	.byte	'Ifx_ERAY_MHDC_Bits',0,4,194,5,3
	.word	10709
	.byte	10
	.byte	'_Ifx_ERAY_MHDF_Bits',0,4,197,5,16,4,11
	.byte	'SNUA',0,1
	.word	349
	.byte	1,7,2,35,0,11
	.byte	'SNUB',0,1
	.word	349
	.byte	1,6,2,35,0,11
	.byte	'FNFA',0,1
	.word	349
	.byte	1,5,2,35,0,11
	.byte	'FNFB',0,1
	.word	349
	.byte	1,4,2,35,0,11
	.byte	'TBFA',0,1
	.word	349
	.byte	1,3,2,35,0,11
	.byte	'TBFB',0,1
	.word	349
	.byte	1,2,2,35,0,11
	.byte	'TNSA',0,1
	.word	349
	.byte	1,1,2,35,0,11
	.byte	'TNSB',0,1
	.word	349
	.byte	1,0,2,35,0,11
	.byte	'WAHP',0,1
	.word	349
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,4
	.word	264
	.byte	23,0,2,35,0,0,3
	.byte	'Ifx_ERAY_MHDF_Bits',0,4,209,5,3
	.word	10840
	.byte	10
	.byte	'_Ifx_ERAY_MHDS_Bits',0,4,212,5,16,4,11
	.byte	'EIBF',0,1
	.word	349
	.byte	1,7,2,35,0,11
	.byte	'EOBF',0,1
	.word	349
	.byte	1,6,2,35,0,11
	.byte	'EMR',0,1
	.word	349
	.byte	1,5,2,35,0,11
	.byte	'ETBF1',0,1
	.word	349
	.byte	1,4,2,35,0,11
	.byte	'ETBF2',0,1
	.word	349
	.byte	1,3,2,35,0,11
	.byte	'FMBD',0,1
	.word	349
	.byte	1,2,2,35,0,11
	.byte	'MFMB',0,1
	.word	349
	.byte	1,1,2,35,0,11
	.byte	'CRAM',0,1
	.word	349
	.byte	1,0,2,35,0,11
	.byte	'FMB',0,1
	.word	349
	.byte	7,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	349
	.byte	1,0,2,35,1,11
	.byte	'MBT',0,1
	.word	349
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	349
	.byte	1,0,2,35,2,11
	.byte	'MBU',0,1
	.word	349
	.byte	7,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	349
	.byte	1,0,2,35,3,0,3
	.byte	'Ifx_ERAY_MHDS_Bits',0,4,228,5,3
	.word	11061
	.byte	10
	.byte	'_Ifx_ERAY_MRC_Bits',0,4,231,5,16,4,11
	.byte	'FDB',0,1
	.word	349
	.byte	8,0,2,35,0,11
	.byte	'FFB',0,1
	.word	349
	.byte	8,0,2,35,1,11
	.byte	'LCB',0,1
	.word	349
	.byte	8,0,2,35,2,11
	.byte	'SEC',0,1
	.word	349
	.byte	2,6,2,35,3,11
	.byte	'SPLM',0,1
	.word	349
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	349
	.byte	5,0,2,35,3,0,3
	.byte	'Ifx_ERAY_MRC_Bits',0,4,239,5,3
	.word	11359
	.byte	10
	.byte	'_Ifx_ERAY_MSIC1_Bits',0,4,242,5,16,4,11
	.byte	'MSIP0',0,1
	.word	349
	.byte	1,7,2,35,0,11
	.byte	'MSIP1',0,1
	.word	349
	.byte	1,6,2,35,0,11
	.byte	'MSIP2',0,1
	.word	349
	.byte	1,5,2,35,0,11
	.byte	'MSIP3',0,1
	.word	349
	.byte	1,4,2,35,0,11
	.byte	'MSIP4',0,1
	.word	349
	.byte	1,3,2,35,0,11
	.byte	'MSIP5',0,1
	.word	349
	.byte	1,2,2,35,0,11
	.byte	'MSIP6',0,1
	.word	349
	.byte	1,1,2,35,0,11
	.byte	'MSIP7',0,1
	.word	349
	.byte	1,0,2,35,0,11
	.byte	'MSIP8',0,1
	.word	349
	.byte	1,7,2,35,1,11
	.byte	'MSIP9',0,1
	.word	349
	.byte	1,6,2,35,1,11
	.byte	'MSIP10',0,1
	.word	349
	.byte	1,5,2,35,1,11
	.byte	'MSIP11',0,1
	.word	349
	.byte	1,4,2,35,1,11
	.byte	'MSIP12',0,1
	.word	349
	.byte	1,3,2,35,1,11
	.byte	'MSIP13',0,1
	.word	349
	.byte	1,2,2,35,1,11
	.byte	'MSIP14',0,1
	.word	349
	.byte	1,1,2,35,1,11
	.byte	'MSIP15',0,1
	.word	349
	.byte	1,0,2,35,1,11
	.byte	'MSIP16',0,1
	.word	349
	.byte	1,7,2,35,2,11
	.byte	'MSIP17',0,1
	.word	349
	.byte	1,6,2,35,2,11
	.byte	'MSIP18',0,1
	.word	349
	.byte	1,5,2,35,2,11
	.byte	'MSIP19',0,1
	.word	349
	.byte	1,4,2,35,2,11
	.byte	'MSIP20',0,1
	.word	349
	.byte	1,3,2,35,2,11
	.byte	'MSIP21',0,1
	.word	349
	.byte	1,2,2,35,2,11
	.byte	'MSIP22',0,1
	.word	349
	.byte	1,1,2,35,2,11
	.byte	'MSIP23',0,1
	.word	349
	.byte	1,0,2,35,2,11
	.byte	'MSIP24',0,1
	.word	349
	.byte	1,7,2,35,3,11
	.byte	'MSIP25',0,1
	.word	349
	.byte	1,6,2,35,3,11
	.byte	'MSIP26',0,1
	.word	349
	.byte	1,5,2,35,3,11
	.byte	'MSIP27',0,1
	.word	349
	.byte	1,4,2,35,3,11
	.byte	'MSIP28',0,1
	.word	349
	.byte	1,3,2,35,3,11
	.byte	'MSIP29',0,1
	.word	349
	.byte	1,2,2,35,3,11
	.byte	'MSIP30',0,1
	.word	349
	.byte	1,1,2,35,3,11
	.byte	'MSIP31',0,1
	.word	349
	.byte	1,0,2,35,3,0,3
	.byte	'Ifx_ERAY_MSIC1_Bits',0,4,148,6,3
	.word	11511
	.byte	10
	.byte	'_Ifx_ERAY_MSIC2_Bits',0,4,151,6,16,4,11
	.byte	'MSIP32',0,1
	.word	349
	.byte	1,7,2,35,0,11
	.byte	'MSIP33',0,1
	.word	349
	.byte	1,6,2,35,0,11
	.byte	'MSIP34',0,1
	.word	349
	.byte	1,5,2,35,0,11
	.byte	'MSIP35',0,1
	.word	349
	.byte	1,4,2,35,0,11
	.byte	'MSIP36',0,1
	.word	349
	.byte	1,3,2,35,0,11
	.byte	'MSIP37',0,1
	.word	349
	.byte	1,2,2,35,0,11
	.byte	'MSIP38',0,1
	.word	349
	.byte	1,1,2,35,0,11
	.byte	'MSIP39',0,1
	.word	349
	.byte	1,0,2,35,0,11
	.byte	'MSIP40',0,1
	.word	349
	.byte	1,7,2,35,1,11
	.byte	'MSIP41',0,1
	.word	349
	.byte	1,6,2,35,1,11
	.byte	'MSIP42',0,1
	.word	349
	.byte	1,5,2,35,1,11
	.byte	'MSIP43',0,1
	.word	349
	.byte	1,4,2,35,1,11
	.byte	'MSIP44',0,1
	.word	349
	.byte	1,3,2,35,1,11
	.byte	'MSIP45',0,1
	.word	349
	.byte	1,2,2,35,1,11
	.byte	'MSIP46',0,1
	.word	349
	.byte	1,1,2,35,1,11
	.byte	'MSIP47',0,1
	.word	349
	.byte	1,0,2,35,1,11
	.byte	'MSIP48',0,1
	.word	349
	.byte	1,7,2,35,2,11
	.byte	'MSIP49',0,1
	.word	349
	.byte	1,6,2,35,2,11
	.byte	'MSIP50',0,1
	.word	349
	.byte	1,5,2,35,2,11
	.byte	'MSIP51',0,1
	.word	349
	.byte	1,4,2,35,2,11
	.byte	'MSIP52',0,1
	.word	349
	.byte	1,3,2,35,2,11
	.byte	'MSIP53',0,1
	.word	349
	.byte	1,2,2,35,2,11
	.byte	'MSIP54',0,1
	.word	349
	.byte	1,1,2,35,2,11
	.byte	'MSIP55',0,1
	.word	349
	.byte	1,0,2,35,2,11
	.byte	'MSIP56',0,1
	.word	349
	.byte	1,7,2,35,3,11
	.byte	'MSIP57',0,1
	.word	349
	.byte	1,6,2,35,3,11
	.byte	'MSIP58',0,1
	.word	349
	.byte	1,5,2,35,3,11
	.byte	'MSIP59',0,1
	.word	349
	.byte	1,4,2,35,3,11
	.byte	'MSIP60',0,1
	.word	349
	.byte	1,3,2,35,3,11
	.byte	'MSIP61',0,1
	.word	349
	.byte	1,2,2,35,3,11
	.byte	'MSIP62',0,1
	.word	349
	.byte	1,1,2,35,3,11
	.byte	'MSIP63',0,1
	.word	349
	.byte	1,0,2,35,3,0,3
	.byte	'Ifx_ERAY_MSIC2_Bits',0,4,185,6,3
	.word	12134
	.byte	10
	.byte	'_Ifx_ERAY_MSIC3_Bits',0,4,188,6,16,4,11
	.byte	'MSIP64',0,1
	.word	349
	.byte	1,7,2,35,0,11
	.byte	'MSIP65',0,1
	.word	349
	.byte	1,6,2,35,0,11
	.byte	'MSIP66',0,1
	.word	349
	.byte	1,5,2,35,0,11
	.byte	'MSIP67',0,1
	.word	349
	.byte	1,4,2,35,0,11
	.byte	'MSIP68',0,1
	.word	349
	.byte	1,3,2,35,0,11
	.byte	'MSIP69',0,1
	.word	349
	.byte	1,2,2,35,0,11
	.byte	'MSIP70',0,1
	.word	349
	.byte	1,1,2,35,0,11
	.byte	'MSIP71',0,1
	.word	349
	.byte	1,0,2,35,0,11
	.byte	'MSIP72',0,1
	.word	349
	.byte	1,7,2,35,1,11
	.byte	'MSIP73',0,1
	.word	349
	.byte	1,6,2,35,1,11
	.byte	'MSIP74',0,1
	.word	349
	.byte	1,5,2,35,1,11
	.byte	'MSIP75',0,1
	.word	349
	.byte	1,4,2,35,1,11
	.byte	'MSIP76',0,1
	.word	349
	.byte	1,3,2,35,1,11
	.byte	'MSIP77',0,1
	.word	349
	.byte	1,2,2,35,1,11
	.byte	'MSIP78',0,1
	.word	349
	.byte	1,1,2,35,1,11
	.byte	'MSIP79',0,1
	.word	349
	.byte	1,0,2,35,1,11
	.byte	'MSIP80',0,1
	.word	349
	.byte	1,7,2,35,2,11
	.byte	'MSIP81',0,1
	.word	349
	.byte	1,6,2,35,2,11
	.byte	'MSIP82',0,1
	.word	349
	.byte	1,5,2,35,2,11
	.byte	'MSIP83',0,1
	.word	349
	.byte	1,4,2,35,2,11
	.byte	'MSIP84',0,1
	.word	349
	.byte	1,3,2,35,2,11
	.byte	'MSIP85',0,1
	.word	349
	.byte	1,2,2,35,2,11
	.byte	'MSIP86',0,1
	.word	349
	.byte	1,1,2,35,2,11
	.byte	'MSIP87',0,1
	.word	349
	.byte	1,0,2,35,2,11
	.byte	'MSIP88',0,1
	.word	349
	.byte	1,7,2,35,3,11
	.byte	'MSIP89',0,1
	.word	349
	.byte	1,6,2,35,3,11
	.byte	'MSIP90',0,1
	.word	349
	.byte	1,5,2,35,3,11
	.byte	'MSIP91',0,1
	.word	349
	.byte	1,4,2,35,3,11
	.byte	'MSIP92',0,1
	.word	349
	.byte	1,3,2,35,3,11
	.byte	'MSIP93',0,1
	.word	349
	.byte	1,2,2,35,3,11
	.byte	'MSIP94',0,1
	.word	349
	.byte	1,1,2,35,3,11
	.byte	'MSIP95',0,1
	.word	349
	.byte	1,0,2,35,3,0,3
	.byte	'Ifx_ERAY_MSIC3_Bits',0,4,222,6,3
	.word	12767
	.byte	10
	.byte	'_Ifx_ERAY_MSIC4_Bits',0,4,225,6,16,4,11
	.byte	'MSIP96',0,1
	.word	349
	.byte	1,7,2,35,0,11
	.byte	'MSIP97',0,1
	.word	349
	.byte	1,6,2,35,0,11
	.byte	'MSIP98',0,1
	.word	349
	.byte	1,5,2,35,0,11
	.byte	'MSIP99',0,1
	.word	349
	.byte	1,4,2,35,0,11
	.byte	'MSIP100',0,1
	.word	349
	.byte	1,3,2,35,0,11
	.byte	'MSIP101',0,1
	.word	349
	.byte	1,2,2,35,0,11
	.byte	'MSIP102',0,1
	.word	349
	.byte	1,1,2,35,0,11
	.byte	'MSIP103',0,1
	.word	349
	.byte	1,0,2,35,0,11
	.byte	'MSIP104',0,1
	.word	349
	.byte	1,7,2,35,1,11
	.byte	'MSIP105',0,1
	.word	349
	.byte	1,6,2,35,1,11
	.byte	'MSIP106',0,1
	.word	349
	.byte	1,5,2,35,1,11
	.byte	'MSIP107',0,1
	.word	349
	.byte	1,4,2,35,1,11
	.byte	'MSIP108',0,1
	.word	349
	.byte	1,3,2,35,1,11
	.byte	'MSIP109',0,1
	.word	349
	.byte	1,2,2,35,1,11
	.byte	'MSIP110',0,1
	.word	349
	.byte	1,1,2,35,1,11
	.byte	'MSIP111',0,1
	.word	349
	.byte	1,0,2,35,1,11
	.byte	'MSIP112',0,1
	.word	349
	.byte	1,7,2,35,2,11
	.byte	'MSIP113',0,1
	.word	349
	.byte	1,6,2,35,2,11
	.byte	'MSIP114',0,1
	.word	349
	.byte	1,5,2,35,2,11
	.byte	'MSIP115',0,1
	.word	349
	.byte	1,4,2,35,2,11
	.byte	'MSIP116',0,1
	.word	349
	.byte	1,3,2,35,2,11
	.byte	'MSIP117',0,1
	.word	349
	.byte	1,2,2,35,2,11
	.byte	'MSIP118',0,1
	.word	349
	.byte	1,1,2,35,2,11
	.byte	'MSIP119',0,1
	.word	349
	.byte	1,0,2,35,2,11
	.byte	'MSIP120',0,1
	.word	349
	.byte	1,7,2,35,3,11
	.byte	'MSIP121',0,1
	.word	349
	.byte	1,6,2,35,3,11
	.byte	'MSIP122',0,1
	.word	349
	.byte	1,5,2,35,3,11
	.byte	'MSIP123',0,1
	.word	349
	.byte	1,4,2,35,3,11
	.byte	'MSIP124',0,1
	.word	349
	.byte	1,3,2,35,3,11
	.byte	'MSIP125',0,1
	.word	349
	.byte	1,2,2,35,3,11
	.byte	'MSIP126',0,1
	.word	349
	.byte	1,1,2,35,3,11
	.byte	'MSIP127',0,1
	.word	349
	.byte	1,0,2,35,3,0,3
	.byte	'Ifx_ERAY_MSIC4_Bits',0,4,131,7,3
	.word	13400
	.byte	10
	.byte	'_Ifx_ERAY_MTCCV_Bits',0,4,134,7,16,4,11
	.byte	'MTV',0,2
	.word	380
	.byte	14,2,2,35,0,11
	.byte	'reserved_14',0,1
	.word	349
	.byte	2,0,2,35,1,11
	.byte	'CCV',0,1
	.word	349
	.byte	6,2,2,35,2,11
	.byte	'reserved_22',0,2
	.word	380
	.byte	10,0,2,35,2,0,3
	.byte	'Ifx_ERAY_MTCCV_Bits',0,4,140,7,3
	.word	14061
	.byte	10
	.byte	'_Ifx_ERAY_NDAT1_Bits',0,4,143,7,16,4,11
	.byte	'ND0',0,1
	.word	349
	.byte	1,7,2,35,0,11
	.byte	'ND1',0,1
	.word	349
	.byte	1,6,2,35,0,11
	.byte	'ND2',0,1
	.word	349
	.byte	1,5,2,35,0,11
	.byte	'ND3',0,1
	.word	349
	.byte	1,4,2,35,0,11
	.byte	'ND4',0,1
	.word	349
	.byte	1,3,2,35,0,11
	.byte	'ND5',0,1
	.word	349
	.byte	1,2,2,35,0,11
	.byte	'ND6',0,1
	.word	349
	.byte	1,1,2,35,0,11
	.byte	'ND7',0,1
	.word	349
	.byte	1,0,2,35,0,11
	.byte	'ND8',0,1
	.word	349
	.byte	1,7,2,35,1,11
	.byte	'ND9',0,1
	.word	349
	.byte	1,6,2,35,1,11
	.byte	'ND10',0,1
	.word	349
	.byte	1,5,2,35,1,11
	.byte	'ND11',0,1
	.word	349
	.byte	1,4,2,35,1,11
	.byte	'ND12',0,1
	.word	349
	.byte	1,3,2,35,1,11
	.byte	'ND13',0,1
	.word	349
	.byte	1,2,2,35,1,11
	.byte	'ND14',0,1
	.word	349
	.byte	1,1,2,35,1,11
	.byte	'ND15',0,1
	.word	349
	.byte	1,0,2,35,1,11
	.byte	'ND16',0,1
	.word	349
	.byte	1,7,2,35,2,11
	.byte	'ND17',0,1
	.word	349
	.byte	1,6,2,35,2,11
	.byte	'ND18',0,1
	.word	349
	.byte	1,5,2,35,2,11
	.byte	'ND19',0,1
	.word	349
	.byte	1,4,2,35,2,11
	.byte	'ND20',0,1
	.word	349
	.byte	1,3,2,35,2,11
	.byte	'ND21',0,1
	.word	349
	.byte	1,2,2,35,2,11
	.byte	'ND22',0,1
	.word	349
	.byte	1,1,2,35,2,11
	.byte	'ND23',0,1
	.word	349
	.byte	1,0,2,35,2,11
	.byte	'ND24',0,1
	.word	349
	.byte	1,7,2,35,3,11
	.byte	'ND25',0,1
	.word	349
	.byte	1,6,2,35,3,11
	.byte	'ND26',0,1
	.word	349
	.byte	1,5,2,35,3,11
	.byte	'ND27',0,1
	.word	349
	.byte	1,4,2,35,3,11
	.byte	'ND28',0,1
	.word	349
	.byte	1,3,2,35,3,11
	.byte	'ND29',0,1
	.word	349
	.byte	1,2,2,35,3,11
	.byte	'ND30',0,1
	.word	349
	.byte	1,1,2,35,3,11
	.byte	'ND31',0,1
	.word	349
	.byte	1,0,2,35,3,0,3
	.byte	'Ifx_ERAY_NDAT1_Bits',0,4,177,7,3
	.word	14194
	.byte	10
	.byte	'_Ifx_ERAY_NDAT2_Bits',0,4,180,7,16,4,11
	.byte	'ND32',0,1
	.word	349
	.byte	1,7,2,35,0,11
	.byte	'ND33',0,1
	.word	349
	.byte	1,6,2,35,0,11
	.byte	'ND34',0,1
	.word	349
	.byte	1,5,2,35,0,11
	.byte	'ND35',0,1
	.word	349
	.byte	1,4,2,35,0,11
	.byte	'ND36',0,1
	.word	349
	.byte	1,3,2,35,0,11
	.byte	'ND37',0,1
	.word	349
	.byte	1,2,2,35,0,11
	.byte	'ND38',0,1
	.word	349
	.byte	1,1,2,35,0,11
	.byte	'ND39',0,1
	.word	349
	.byte	1,0,2,35,0,11
	.byte	'ND40',0,1
	.word	349
	.byte	1,7,2,35,1,11
	.byte	'ND41',0,1
	.word	349
	.byte	1,6,2,35,1,11
	.byte	'ND42',0,1
	.word	349
	.byte	1,5,2,35,1,11
	.byte	'ND43',0,1
	.word	349
	.byte	1,4,2,35,1,11
	.byte	'ND44',0,1
	.word	349
	.byte	1,3,2,35,1,11
	.byte	'ND45',0,1
	.word	349
	.byte	1,2,2,35,1,11
	.byte	'ND46',0,1
	.word	349
	.byte	1,1,2,35,1,11
	.byte	'ND47',0,1
	.word	349
	.byte	1,0,2,35,1,11
	.byte	'ND48',0,1
	.word	349
	.byte	1,7,2,35,2,11
	.byte	'ND49',0,1
	.word	349
	.byte	1,6,2,35,2,11
	.byte	'ND50',0,1
	.word	349
	.byte	1,5,2,35,2,11
	.byte	'ND51',0,1
	.word	349
	.byte	1,4,2,35,2,11
	.byte	'ND52',0,1
	.word	349
	.byte	1,3,2,35,2,11
	.byte	'ND53',0,1
	.word	349
	.byte	1,2,2,35,2,11
	.byte	'ND54',0,1
	.word	349
	.byte	1,1,2,35,2,11
	.byte	'ND55',0,1
	.word	349
	.byte	1,0,2,35,2,11
	.byte	'ND56',0,1
	.word	349
	.byte	1,7,2,35,3,11
	.byte	'ND57',0,1
	.word	349
	.byte	1,6,2,35,3,11
	.byte	'ND58',0,1
	.word	349
	.byte	1,5,2,35,3,11
	.byte	'ND59',0,1
	.word	349
	.byte	1,4,2,35,3,11
	.byte	'ND60',0,1
	.word	349
	.byte	1,3,2,35,3,11
	.byte	'ND61',0,1
	.word	349
	.byte	1,2,2,35,3,11
	.byte	'ND62',0,1
	.word	349
	.byte	1,1,2,35,3,11
	.byte	'ND63',0,1
	.word	349
	.byte	1,0,2,35,3,0,3
	.byte	'Ifx_ERAY_NDAT2_Bits',0,4,214,7,3
	.word	14753
	.byte	10
	.byte	'_Ifx_ERAY_NDAT3_Bits',0,4,217,7,16,4,11
	.byte	'ND64',0,1
	.word	349
	.byte	1,7,2,35,0,11
	.byte	'ND65',0,1
	.word	349
	.byte	1,6,2,35,0,11
	.byte	'ND66',0,1
	.word	349
	.byte	1,5,2,35,0,11
	.byte	'ND67',0,1
	.word	349
	.byte	1,4,2,35,0,11
	.byte	'ND68',0,1
	.word	349
	.byte	1,3,2,35,0,11
	.byte	'ND69',0,1
	.word	349
	.byte	1,2,2,35,0,11
	.byte	'ND70',0,1
	.word	349
	.byte	1,1,2,35,0,11
	.byte	'ND71',0,1
	.word	349
	.byte	1,0,2,35,0,11
	.byte	'ND72',0,1
	.word	349
	.byte	1,7,2,35,1,11
	.byte	'ND73',0,1
	.word	349
	.byte	1,6,2,35,1,11
	.byte	'ND74',0,1
	.word	349
	.byte	1,5,2,35,1,11
	.byte	'ND75',0,1
	.word	349
	.byte	1,4,2,35,1,11
	.byte	'ND76',0,1
	.word	349
	.byte	1,3,2,35,1,11
	.byte	'ND77',0,1
	.word	349
	.byte	1,2,2,35,1,11
	.byte	'ND78',0,1
	.word	349
	.byte	1,1,2,35,1,11
	.byte	'ND79',0,1
	.word	349
	.byte	1,0,2,35,1,11
	.byte	'ND80',0,1
	.word	349
	.byte	1,7,2,35,2,11
	.byte	'ND81',0,1
	.word	349
	.byte	1,6,2,35,2,11
	.byte	'ND82',0,1
	.word	349
	.byte	1,5,2,35,2,11
	.byte	'ND83',0,1
	.word	349
	.byte	1,4,2,35,2,11
	.byte	'ND84',0,1
	.word	349
	.byte	1,3,2,35,2,11
	.byte	'ND85',0,1
	.word	349
	.byte	1,2,2,35,2,11
	.byte	'ND86',0,1
	.word	349
	.byte	1,1,2,35,2,11
	.byte	'ND87',0,1
	.word	349
	.byte	1,0,2,35,2,11
	.byte	'ND88',0,1
	.word	349
	.byte	1,7,2,35,3,11
	.byte	'ND89',0,1
	.word	349
	.byte	1,6,2,35,3,11
	.byte	'ND90',0,1
	.word	349
	.byte	1,5,2,35,3,11
	.byte	'ND91',0,1
	.word	349
	.byte	1,4,2,35,3,11
	.byte	'ND92',0,1
	.word	349
	.byte	1,3,2,35,3,11
	.byte	'ND93',0,1
	.word	349
	.byte	1,2,2,35,3,11
	.byte	'ND94',0,1
	.word	349
	.byte	1,1,2,35,3,11
	.byte	'ND95',0,1
	.word	349
	.byte	1,0,2,35,3,0,3
	.byte	'Ifx_ERAY_NDAT3_Bits',0,4,251,7,3
	.word	15322
	.byte	10
	.byte	'_Ifx_ERAY_NDAT4_Bits',0,4,254,7,16,4,11
	.byte	'ND96',0,1
	.word	349
	.byte	1,7,2,35,0,11
	.byte	'ND97',0,1
	.word	349
	.byte	1,6,2,35,0,11
	.byte	'ND98',0,1
	.word	349
	.byte	1,5,2,35,0,11
	.byte	'ND99',0,1
	.word	349
	.byte	1,4,2,35,0,11
	.byte	'ND100',0,1
	.word	349
	.byte	1,3,2,35,0,11
	.byte	'ND101',0,1
	.word	349
	.byte	1,2,2,35,0,11
	.byte	'ND102',0,1
	.word	349
	.byte	1,1,2,35,0,11
	.byte	'ND103',0,1
	.word	349
	.byte	1,0,2,35,0,11
	.byte	'ND104',0,1
	.word	349
	.byte	1,7,2,35,1,11
	.byte	'ND105',0,1
	.word	349
	.byte	1,6,2,35,1,11
	.byte	'ND106',0,1
	.word	349
	.byte	1,5,2,35,1,11
	.byte	'ND107',0,1
	.word	349
	.byte	1,4,2,35,1,11
	.byte	'ND108',0,1
	.word	349
	.byte	1,3,2,35,1,11
	.byte	'ND109',0,1
	.word	349
	.byte	1,2,2,35,1,11
	.byte	'ND110',0,1
	.word	349
	.byte	1,1,2,35,1,11
	.byte	'ND111',0,1
	.word	349
	.byte	1,0,2,35,1,11
	.byte	'ND112',0,1
	.word	349
	.byte	1,7,2,35,2,11
	.byte	'ND113',0,1
	.word	349
	.byte	1,6,2,35,2,11
	.byte	'ND114',0,1
	.word	349
	.byte	1,5,2,35,2,11
	.byte	'ND115',0,1
	.word	349
	.byte	1,4,2,35,2,11
	.byte	'ND116',0,1
	.word	349
	.byte	1,3,2,35,2,11
	.byte	'ND117',0,1
	.word	349
	.byte	1,2,2,35,2,11
	.byte	'ND118',0,1
	.word	349
	.byte	1,1,2,35,2,11
	.byte	'ND119',0,1
	.word	349
	.byte	1,0,2,35,2,11
	.byte	'ND120',0,1
	.word	349
	.byte	1,7,2,35,3,11
	.byte	'ND121',0,1
	.word	349
	.byte	1,6,2,35,3,11
	.byte	'ND122',0,1
	.word	349
	.byte	1,5,2,35,3,11
	.byte	'ND123',0,1
	.word	349
	.byte	1,4,2,35,3,11
	.byte	'ND124',0,1
	.word	349
	.byte	1,3,2,35,3,11
	.byte	'ND125',0,1
	.word	349
	.byte	1,2,2,35,3,11
	.byte	'ND126',0,1
	.word	349
	.byte	1,1,2,35,3,11
	.byte	'ND127',0,1
	.word	349
	.byte	1,0,2,35,3,0,3
	.byte	'Ifx_ERAY_NDAT4_Bits',0,4,160,8,3
	.word	15891
	.byte	10
	.byte	'_Ifx_ERAY_NDIC1_Bits',0,4,163,8,16,4,11
	.byte	'NDIP0',0,1
	.word	349
	.byte	1,7,2,35,0,11
	.byte	'NDIP1',0,1
	.word	349
	.byte	1,6,2,35,0,11
	.byte	'NDIP2',0,1
	.word	349
	.byte	1,5,2,35,0,11
	.byte	'NDIP3',0,1
	.word	349
	.byte	1,4,2,35,0,11
	.byte	'NDIP4',0,1
	.word	349
	.byte	1,3,2,35,0,11
	.byte	'NDIP5',0,1
	.word	349
	.byte	1,2,2,35,0,11
	.byte	'NDIP6',0,1
	.word	349
	.byte	1,1,2,35,0,11
	.byte	'NDIP7',0,1
	.word	349
	.byte	1,0,2,35,0,11
	.byte	'NDIP8',0,1
	.word	349
	.byte	1,7,2,35,1,11
	.byte	'NDIP9',0,1
	.word	349
	.byte	1,6,2,35,1,11
	.byte	'NDIP10',0,1
	.word	349
	.byte	1,5,2,35,1,11
	.byte	'NDIP11',0,1
	.word	349
	.byte	1,4,2,35,1,11
	.byte	'NDIP12',0,1
	.word	349
	.byte	1,3,2,35,1,11
	.byte	'NDIP13',0,1
	.word	349
	.byte	1,2,2,35,1,11
	.byte	'NDIP14',0,1
	.word	349
	.byte	1,1,2,35,1,11
	.byte	'NDIP15',0,1
	.word	349
	.byte	1,0,2,35,1,11
	.byte	'NDIP16',0,1
	.word	349
	.byte	1,7,2,35,2,11
	.byte	'NDIP17',0,1
	.word	349
	.byte	1,6,2,35,2,11
	.byte	'NDIP18',0,1
	.word	349
	.byte	1,5,2,35,2,11
	.byte	'NDIP19',0,1
	.word	349
	.byte	1,4,2,35,2,11
	.byte	'NDIP20',0,1
	.word	349
	.byte	1,3,2,35,2,11
	.byte	'NDIP21',0,1
	.word	349
	.byte	1,2,2,35,2,11
	.byte	'NDIP22',0,1
	.word	349
	.byte	1,1,2,35,2,11
	.byte	'NDIP23',0,1
	.word	349
	.byte	1,0,2,35,2,11
	.byte	'NDIP24',0,1
	.word	349
	.byte	1,7,2,35,3,11
	.byte	'NDIP25',0,1
	.word	349
	.byte	1,6,2,35,3,11
	.byte	'NDIP26',0,1
	.word	349
	.byte	1,5,2,35,3,11
	.byte	'NDIP27',0,1
	.word	349
	.byte	1,4,2,35,3,11
	.byte	'NDIP28',0,1
	.word	349
	.byte	1,3,2,35,3,11
	.byte	'NDIP29',0,1
	.word	349
	.byte	1,2,2,35,3,11
	.byte	'NDIP30',0,1
	.word	349
	.byte	1,1,2,35,3,11
	.byte	'NDIP31',0,1
	.word	349
	.byte	1,0,2,35,3,0,3
	.byte	'Ifx_ERAY_NDIC1_Bits',0,4,197,8,3
	.word	16488
	.byte	10
	.byte	'_Ifx_ERAY_NDIC2_Bits',0,4,200,8,16,4,11
	.byte	'NDIP32',0,1
	.word	349
	.byte	1,7,2,35,0,11
	.byte	'NDIP33',0,1
	.word	349
	.byte	1,6,2,35,0,11
	.byte	'NDIP34',0,1
	.word	349
	.byte	1,5,2,35,0,11
	.byte	'NDIP35',0,1
	.word	349
	.byte	1,4,2,35,0,11
	.byte	'NDIP36',0,1
	.word	349
	.byte	1,3,2,35,0,11
	.byte	'NDIP37',0,1
	.word	349
	.byte	1,2,2,35,0,11
	.byte	'NDIP38',0,1
	.word	349
	.byte	1,1,2,35,0,11
	.byte	'NDIP39',0,1
	.word	349
	.byte	1,0,2,35,0,11
	.byte	'NDIP40',0,1
	.word	349
	.byte	1,7,2,35,1,11
	.byte	'NDIP41',0,1
	.word	349
	.byte	1,6,2,35,1,11
	.byte	'NDIP42',0,1
	.word	349
	.byte	1,5,2,35,1,11
	.byte	'NDIP43',0,1
	.word	349
	.byte	1,4,2,35,1,11
	.byte	'NDIP44',0,1
	.word	349
	.byte	1,3,2,35,1,11
	.byte	'NDIP45',0,1
	.word	349
	.byte	1,2,2,35,1,11
	.byte	'NDIP46',0,1
	.word	349
	.byte	1,1,2,35,1,11
	.byte	'NDIP47',0,1
	.word	349
	.byte	1,0,2,35,1,11
	.byte	'NDIP48',0,1
	.word	349
	.byte	1,7,2,35,2,11
	.byte	'NDIP49',0,1
	.word	349
	.byte	1,6,2,35,2,11
	.byte	'NDIP50',0,1
	.word	349
	.byte	1,5,2,35,2,11
	.byte	'NDIP51',0,1
	.word	349
	.byte	1,4,2,35,2,11
	.byte	'NDIP52',0,1
	.word	349
	.byte	1,3,2,35,2,11
	.byte	'NDIP53',0,1
	.word	349
	.byte	1,2,2,35,2,11
	.byte	'NDIP54',0,1
	.word	349
	.byte	1,1,2,35,2,11
	.byte	'NDIP55',0,1
	.word	349
	.byte	1,0,2,35,2,11
	.byte	'NDIP56',0,1
	.word	349
	.byte	1,7,2,35,3,11
	.byte	'NDIP57',0,1
	.word	349
	.byte	1,6,2,35,3,11
	.byte	'NDIP58',0,1
	.word	349
	.byte	1,5,2,35,3,11
	.byte	'NDIP59',0,1
	.word	349
	.byte	1,4,2,35,3,11
	.byte	'NDIP60',0,1
	.word	349
	.byte	1,3,2,35,3,11
	.byte	'NDIP61',0,1
	.word	349
	.byte	1,2,2,35,3,11
	.byte	'NDIP62',0,1
	.word	349
	.byte	1,1,2,35,3,11
	.byte	'NDIP63',0,1
	.word	349
	.byte	1,0,2,35,3,0,3
	.byte	'Ifx_ERAY_NDIC2_Bits',0,4,234,8,3
	.word	17111
	.byte	10
	.byte	'_Ifx_ERAY_NDIC3_Bits',0,4,237,8,16,4,11
	.byte	'NDIP64',0,1
	.word	349
	.byte	1,7,2,35,0,11
	.byte	'NDIP65',0,1
	.word	349
	.byte	1,6,2,35,0,11
	.byte	'NDIP66',0,1
	.word	349
	.byte	1,5,2,35,0,11
	.byte	'NDIP67',0,1
	.word	349
	.byte	1,4,2,35,0,11
	.byte	'NDIP68',0,1
	.word	349
	.byte	1,3,2,35,0,11
	.byte	'NDIP69',0,1
	.word	349
	.byte	1,2,2,35,0,11
	.byte	'NDIP70',0,1
	.word	349
	.byte	1,1,2,35,0,11
	.byte	'NDIP71',0,1
	.word	349
	.byte	1,0,2,35,0,11
	.byte	'NDIP72',0,1
	.word	349
	.byte	1,7,2,35,1,11
	.byte	'NDIP73',0,1
	.word	349
	.byte	1,6,2,35,1,11
	.byte	'NDIP74',0,1
	.word	349
	.byte	1,5,2,35,1,11
	.byte	'NDIP75',0,1
	.word	349
	.byte	1,4,2,35,1,11
	.byte	'NDIP76',0,1
	.word	349
	.byte	1,3,2,35,1,11
	.byte	'NDIP77',0,1
	.word	349
	.byte	1,2,2,35,1,11
	.byte	'NDIP78',0,1
	.word	349
	.byte	1,1,2,35,1,11
	.byte	'NDIP79',0,1
	.word	349
	.byte	1,0,2,35,1,11
	.byte	'NDIP80',0,1
	.word	349
	.byte	1,7,2,35,2,11
	.byte	'NDIP81',0,1
	.word	349
	.byte	1,6,2,35,2,11
	.byte	'NDIP82',0,1
	.word	349
	.byte	1,5,2,35,2,11
	.byte	'NDIP83',0,1
	.word	349
	.byte	1,4,2,35,2,11
	.byte	'NDIP84',0,1
	.word	349
	.byte	1,3,2,35,2,11
	.byte	'NDIP85',0,1
	.word	349
	.byte	1,2,2,35,2,11
	.byte	'NDIP86',0,1
	.word	349
	.byte	1,1,2,35,2,11
	.byte	'NDIP87',0,1
	.word	349
	.byte	1,0,2,35,2,11
	.byte	'NDIP88',0,1
	.word	349
	.byte	1,7,2,35,3,11
	.byte	'NDIP89',0,1
	.word	349
	.byte	1,6,2,35,3,11
	.byte	'NDIP90',0,1
	.word	349
	.byte	1,5,2,35,3,11
	.byte	'NDIP91',0,1
	.word	349
	.byte	1,4,2,35,3,11
	.byte	'NDIP92',0,1
	.word	349
	.byte	1,3,2,35,3,11
	.byte	'NDIP93',0,1
	.word	349
	.byte	1,2,2,35,3,11
	.byte	'NDIP94',0,1
	.word	349
	.byte	1,1,2,35,3,11
	.byte	'NDIP95',0,1
	.word	349
	.byte	1,0,2,35,3,0,3
	.byte	'Ifx_ERAY_NDIC3_Bits',0,4,143,9,3
	.word	17744
	.byte	10
	.byte	'_Ifx_ERAY_NDIC4_Bits',0,4,146,9,16,4,11
	.byte	'NDIP96',0,1
	.word	349
	.byte	1,7,2,35,0,11
	.byte	'NDIP97',0,1
	.word	349
	.byte	1,6,2,35,0,11
	.byte	'NDIP98',0,1
	.word	349
	.byte	1,5,2,35,0,11
	.byte	'NDIP99',0,1
	.word	349
	.byte	1,4,2,35,0,11
	.byte	'NDIP100',0,1
	.word	349
	.byte	1,3,2,35,0,11
	.byte	'NDIP101',0,1
	.word	349
	.byte	1,2,2,35,0,11
	.byte	'NDIP102',0,1
	.word	349
	.byte	1,1,2,35,0,11
	.byte	'NDIP103',0,1
	.word	349
	.byte	1,0,2,35,0,11
	.byte	'NDIP104',0,1
	.word	349
	.byte	1,7,2,35,1,11
	.byte	'NDIP105',0,1
	.word	349
	.byte	1,6,2,35,1,11
	.byte	'NDIP106',0,1
	.word	349
	.byte	1,5,2,35,1,11
	.byte	'NDIP107',0,1
	.word	349
	.byte	1,4,2,35,1,11
	.byte	'NDIP108',0,1
	.word	349
	.byte	1,3,2,35,1,11
	.byte	'NDIP109',0,1
	.word	349
	.byte	1,2,2,35,1,11
	.byte	'NDIP110',0,1
	.word	349
	.byte	1,1,2,35,1,11
	.byte	'NDIP111',0,1
	.word	349
	.byte	1,0,2,35,1,11
	.byte	'NDIP112',0,1
	.word	349
	.byte	1,7,2,35,2,11
	.byte	'NDIP113',0,1
	.word	349
	.byte	1,6,2,35,2,11
	.byte	'NDIP114',0,1
	.word	349
	.byte	1,5,2,35,2,11
	.byte	'NDIP115',0,1
	.word	349
	.byte	1,4,2,35,2,11
	.byte	'NDIP116',0,1
	.word	349
	.byte	1,3,2,35,2,11
	.byte	'NDIP117',0,1
	.word	349
	.byte	1,2,2,35,2,11
	.byte	'NDIP118',0,1
	.word	349
	.byte	1,1,2,35,2,11
	.byte	'NDIP119',0,1
	.word	349
	.byte	1,0,2,35,2,11
	.byte	'NDIP120',0,1
	.word	349
	.byte	1,7,2,35,3,11
	.byte	'NDIP121',0,1
	.word	349
	.byte	1,6,2,35,3,11
	.byte	'NDIP122',0,1
	.word	349
	.byte	1,5,2,35,3,11
	.byte	'NDIP123',0,1
	.word	349
	.byte	1,4,2,35,3,11
	.byte	'NDIP124',0,1
	.word	349
	.byte	1,3,2,35,3,11
	.byte	'NDIP125',0,1
	.word	349
	.byte	1,2,2,35,3,11
	.byte	'NDIP126',0,1
	.word	349
	.byte	1,1,2,35,3,11
	.byte	'NDIP127',0,1
	.word	349
	.byte	1,0,2,35,3,0,3
	.byte	'Ifx_ERAY_NDIC4_Bits',0,4,180,9,3
	.word	18377
	.byte	10
	.byte	'_Ifx_ERAY_NEMC_Bits',0,4,183,9,16,4,11
	.byte	'NML',0,1
	.word	349
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	264
	.byte	28,0,2,35,0,0,3
	.byte	'Ifx_ERAY_NEMC_Bits',0,4,187,9,3
	.word	19038
	.byte	10
	.byte	'_Ifx_ERAY_NMV_Bits',0,4,190,9,16,4,11
	.byte	'NM',0,4
	.word	264
	.byte	32,0,2,35,0,0,3
	.byte	'Ifx_ERAY_NMV_Bits',0,4,193,9,3
	.word	19130
	.byte	10
	.byte	'_Ifx_ERAY_OBCM_Bits',0,4,196,9,16,4,11
	.byte	'RHSS',0,1
	.word	349
	.byte	1,7,2,35,0,11
	.byte	'RDSS',0,1
	.word	349
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	380
	.byte	14,0,2,35,0,11
	.byte	'RHSH',0,1
	.word	349
	.byte	1,7,2,35,2,11
	.byte	'RDSH',0,1
	.word	349
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	380
	.byte	14,0,2,35,2,0,3
	.byte	'Ifx_ERAY_OBCM_Bits',0,4,204,9,3
	.word	19197
	.byte	10
	.byte	'_Ifx_ERAY_OBCR_Bits',0,4,207,9,16,4,11
	.byte	'OBRS',0,1
	.word	349
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	349
	.byte	1,0,2,35,0,11
	.byte	'VIEW',0,1
	.word	349
	.byte	1,7,2,35,1,11
	.byte	'REQ',0,1
	.word	349
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	349
	.byte	5,1,2,35,1,11
	.byte	'OBSYS',0,1
	.word	349
	.byte	1,0,2,35,1,11
	.byte	'OBRH',0,1
	.word	349
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	380
	.byte	9,0,2,35,2,0,3
	.byte	'Ifx_ERAY_OBCR_Bits',0,4,217,9,3
	.word	19361
	.byte	10
	.byte	'_Ifx_ERAY_OCS_Bits',0,4,220,9,16,4,11
	.byte	'reserved_0',0,4
	.word	264
	.byte	24,8,2,35,0,11
	.byte	'SUS',0,1
	.word	349
	.byte	4,4,2,35,3,11
	.byte	'SUS_P',0,1
	.word	349
	.byte	1,3,2,35,3,11
	.byte	'SUSSTA',0,1
	.word	349
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	349
	.byte	2,0,2,35,3,0,3
	.byte	'Ifx_ERAY_OCS_Bits',0,4,227,9,3
	.word	19564
	.byte	10
	.byte	'_Ifx_ERAY_OCV_Bits',0,4,230,9,16,4,11
	.byte	'OCV',0,4
	.word	264
	.byte	19,13,2,35,0,11
	.byte	'reserved_19',0,2
	.word	380
	.byte	13,0,2,35,2,0,3
	.byte	'Ifx_ERAY_OCV_Bits',0,4,234,9,3
	.word	19712
	.byte	10
	.byte	'_Ifx_ERAY_OSID_Bits',0,4,237,9,16,4,11
	.byte	'OID',0,2
	.word	380
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	349
	.byte	4,2,2,35,1,11
	.byte	'RXOA',0,1
	.word	349
	.byte	1,1,2,35,1,11
	.byte	'RXOB',0,1
	.word	349
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	380
	.byte	16,0,2,35,2,0,3
	.byte	'Ifx_ERAY_OSID_Bits',0,4,244,9,3
	.word	19803
	.byte	10
	.byte	'_Ifx_ERAY_OTSS_Bits',0,4,247,9,16,4,11
	.byte	'OTGB0',0,1
	.word	349
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	349
	.byte	6,0,2,35,0,11
	.byte	'OTGB1',0,1
	.word	349
	.byte	2,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	349
	.byte	6,0,2,35,1,11
	.byte	'OTGB2',0,1
	.word	349
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,2
	.word	380
	.byte	15,0,2,35,2,0,3
	.byte	'Ifx_ERAY_OTSS_Bits',0,4,255,9,3
	.word	19951
	.byte	10
	.byte	'_Ifx_ERAY_PRTC1_Bits',0,4,130,10,16,4,11
	.byte	'TSST',0,1
	.word	349
	.byte	4,4,2,35,0,11
	.byte	'CASM',0,2
	.word	380
	.byte	7,5,2,35,0,11
	.byte	'reserved_11',0,1
	.word	349
	.byte	1,4,2,35,1,11
	.byte	'SPP',0,1
	.word	349
	.byte	2,2,2,35,1,11
	.byte	'BRP',0,1
	.word	349
	.byte	2,0,2,35,1,11
	.byte	'RXW',0,2
	.word	380
	.byte	9,7,2,35,2,11
	.byte	'reserved_25',0,1
	.word	349
	.byte	1,6,2,35,3,11
	.byte	'RWP',0,1
	.word	349
	.byte	6,0,2,35,3,0,3
	.byte	'Ifx_ERAY_PRTC1_Bits',0,4,140,10,3
	.word	20125
	.byte	10
	.byte	'_Ifx_ERAY_PRTC2_Bits',0,4,143,10,16,4,11
	.byte	'RXI',0,1
	.word	349
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	349
	.byte	2,0,2,35,0,11
	.byte	'RXL',0,1
	.word	349
	.byte	6,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	349
	.byte	2,0,2,35,1,11
	.byte	'TXI',0,1
	.word	349
	.byte	8,0,2,35,2,11
	.byte	'TXL',0,1
	.word	349
	.byte	6,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	349
	.byte	2,0,2,35,3,0,3
	.byte	'Ifx_ERAY_PRTC2_Bits',0,4,152,10,3
	.word	20320
	.byte	10
	.byte	'_Ifx_ERAY_RCV_Bits',0,4,155,10,16,4,11
	.byte	'RCV',0,2
	.word	380
	.byte	12,4,2,35,0,11
	.byte	'reserved_12',0,4
	.word	264
	.byte	20,0,2,35,0,0,3
	.byte	'Ifx_ERAY_RCV_Bits',0,4,159,10,3
	.word	20505
	.byte	10
	.byte	'_Ifx_ERAY_RDDS_Bits',0,4,162,10,16,4,11
	.byte	'MDRB0',0,1
	.word	349
	.byte	8,0,2,35,0,11
	.byte	'MDRB1',0,1
	.word	349
	.byte	8,0,2,35,1,11
	.byte	'MDRB2',0,1
	.word	349
	.byte	8,0,2,35,2,11
	.byte	'MDRB3',0,1
	.word	349
	.byte	8,0,2,35,3,0,3
	.byte	'Ifx_ERAY_RDDS_Bits',0,4,168,10,3
	.word	20596
	.byte	10
	.byte	'_Ifx_ERAY_RDHS1_Bits',0,4,171,10,16,4,11
	.byte	'FID',0,2
	.word	380
	.byte	11,5,2,35,0,11
	.byte	'reserved_11',0,1
	.word	349
	.byte	5,0,2,35,1,11
	.byte	'CYC',0,1
	.word	349
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	349
	.byte	1,0,2,35,2,11
	.byte	'CHA',0,1
	.word	349
	.byte	1,7,2,35,3,11
	.byte	'CHB',0,1
	.word	349
	.byte	1,6,2,35,3,11
	.byte	'CFG',0,1
	.word	349
	.byte	1,5,2,35,3,11
	.byte	'PPIT',0,1
	.word	349
	.byte	1,4,2,35,3,11
	.byte	'TXM',0,1
	.word	349
	.byte	1,3,2,35,3,11
	.byte	'MBI',0,1
	.word	349
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	349
	.byte	2,0,2,35,3,0,3
	.byte	'Ifx_ERAY_RDHS1_Bits',0,4,184,10,3
	.word	20719
	.byte	10
	.byte	'_Ifx_ERAY_RDHS2_Bits',0,4,187,10,16,4,11
	.byte	'CRC',0,2
	.word	380
	.byte	11,5,2,35,0,11
	.byte	'reserved_11',0,1
	.word	349
	.byte	5,0,2,35,1,11
	.byte	'PLC',0,1
	.word	349
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	349
	.byte	1,0,2,35,2,11
	.byte	'PLR',0,1
	.word	349
	.byte	7,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	349
	.byte	1,0,2,35,3,0,3
	.byte	'Ifx_ERAY_RDHS2_Bits',0,4,195,10,3
	.word	20966
	.byte	10
	.byte	'_Ifx_ERAY_RDHS3_Bits',0,4,198,10,16,4,11
	.byte	'DP',0,2
	.word	380
	.byte	11,5,2,35,0,11
	.byte	'reserved_11',0,1
	.word	349
	.byte	5,0,2,35,1,11
	.byte	'RCC',0,1
	.word	349
	.byte	6,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	349
	.byte	2,0,2,35,2,11
	.byte	'RCI',0,1
	.word	349
	.byte	1,7,2,35,3,11
	.byte	'SFI',0,1
	.word	349
	.byte	1,6,2,35,3,11
	.byte	'SYN',0,1
	.word	349
	.byte	1,5,2,35,3,11
	.byte	'NFI',0,1
	.word	349
	.byte	1,4,2,35,3,11
	.byte	'PPI',0,1
	.word	349
	.byte	1,3,2,35,3,11
	.byte	'RES',0,1
	.word	349
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	349
	.byte	2,0,2,35,3,0,3
	.byte	'Ifx_ERAY_RDHS3_Bits',0,4,211,10,3
	.word	21137
	.byte	10
	.byte	'_Ifx_ERAY_SCV_Bits',0,4,214,10,16,4,11
	.byte	'SCCA',0,2
	.word	380
	.byte	11,5,2,35,0,11
	.byte	'reserved_11',0,1
	.word	349
	.byte	5,0,2,35,1,11
	.byte	'SCCB',0,2
	.word	380
	.byte	11,5,2,35,2,11
	.byte	'reserved_27',0,1
	.word	349
	.byte	5,0,2,35,3,0,3
	.byte	'Ifx_ERAY_SCV_Bits',0,4,220,10,3
	.word	21382
	.byte	10
	.byte	'_Ifx_ERAY_SFS_Bits',0,4,223,10,16,4,11
	.byte	'VSAE',0,1
	.word	349
	.byte	4,4,2,35,0,11
	.byte	'VSAO',0,1
	.word	349
	.byte	4,0,2,35,0,11
	.byte	'VSBE',0,1
	.word	349
	.byte	4,4,2,35,1,11
	.byte	'VSBO',0,1
	.word	349
	.byte	4,0,2,35,1,11
	.byte	'MOCS',0,1
	.word	349
	.byte	1,7,2,35,2,11
	.byte	'OCLR',0,1
	.word	349
	.byte	1,6,2,35,2,11
	.byte	'MRCS',0,1
	.word	349
	.byte	1,5,2,35,2,11
	.byte	'RCLR',0,1
	.word	349
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	380
	.byte	12,0,2,35,2,0,3
	.byte	'Ifx_ERAY_SFS_Bits',0,4,234,10,3
	.word	21513
	.byte	10
	.byte	'_Ifx_ERAY_SIER_Bits',0,4,237,10,16,4,11
	.byte	'WSTE',0,1
	.word	349
	.byte	1,7,2,35,0,11
	.byte	'CASE',0,1
	.word	349
	.byte	1,6,2,35,0,11
	.byte	'CYCSE',0,1
	.word	349
	.byte	1,5,2,35,0,11
	.byte	'TXIE',0,1
	.word	349
	.byte	1,4,2,35,0,11
	.byte	'RXIE',0,1
	.word	349
	.byte	1,3,2,35,0,11
	.byte	'RFNEE',0,1
	.word	349
	.byte	1,2,2,35,0,11
	.byte	'RFCLE',0,1
	.word	349
	.byte	1,1,2,35,0,11
	.byte	'NMVCE',0,1
	.word	349
	.byte	1,0,2,35,0,11
	.byte	'TI0E',0,1
	.word	349
	.byte	1,7,2,35,1,11
	.byte	'TI1E',0,1
	.word	349
	.byte	1,6,2,35,1,11
	.byte	'TIBCE',0,1
	.word	349
	.byte	1,5,2,35,1,11
	.byte	'TOBCE',0,1
	.word	349
	.byte	1,4,2,35,1,11
	.byte	'SWEE',0,1
	.word	349
	.byte	1,3,2,35,1,11
	.byte	'SUCSE',0,1
	.word	349
	.byte	1,2,2,35,1,11
	.byte	'MBSIE',0,1
	.word	349
	.byte	1,1,2,35,1,11
	.byte	'SDSE',0,1
	.word	349
	.byte	1,0,2,35,1,11
	.byte	'WUPAE',0,1
	.word	349
	.byte	1,7,2,35,2,11
	.byte	'MTSAE',0,1
	.word	349
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	349
	.byte	6,0,2,35,2,11
	.byte	'WUPBE',0,1
	.word	349
	.byte	1,7,2,35,3,11
	.byte	'MTSBE',0,1
	.word	349
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	349
	.byte	6,0,2,35,3,0,3
	.byte	'Ifx_ERAY_SIER_Bits',0,4,133,11,3
	.word	21717
	.byte	10
	.byte	'_Ifx_ERAY_SIES_Bits',0,4,136,11,16,4,11
	.byte	'WSTE',0,1
	.word	349
	.byte	1,7,2,35,0,11
	.byte	'CASE',0,1
	.word	349
	.byte	1,6,2,35,0,11
	.byte	'CYCSE',0,1
	.word	349
	.byte	1,5,2,35,0,11
	.byte	'TXIE',0,1
	.word	349
	.byte	1,4,2,35,0,11
	.byte	'RXIE',0,1
	.word	349
	.byte	1,3,2,35,0,11
	.byte	'RFNEE',0,1
	.word	349
	.byte	1,2,2,35,0,11
	.byte	'RFCLE',0,1
	.word	349
	.byte	1,1,2,35,0,11
	.byte	'NMVCE',0,1
	.word	349
	.byte	1,0,2,35,0,11
	.byte	'TI0E',0,1
	.word	349
	.byte	1,7,2,35,1,11
	.byte	'TI1E',0,1
	.word	349
	.byte	1,6,2,35,1,11
	.byte	'TIBCE',0,1
	.word	349
	.byte	1,5,2,35,1,11
	.byte	'TOBCE',0,1
	.word	349
	.byte	1,4,2,35,1,11
	.byte	'SWEE',0,1
	.word	349
	.byte	1,3,2,35,1,11
	.byte	'SUCSE',0,1
	.word	349
	.byte	1,2,2,35,1,11
	.byte	'MBSIE',0,1
	.word	349
	.byte	1,1,2,35,1,11
	.byte	'SDSE',0,1
	.word	349
	.byte	1,0,2,35,1,11
	.byte	'WUPAE',0,1
	.word	349
	.byte	1,7,2,35,2,11
	.byte	'MTSAE',0,1
	.word	349
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	349
	.byte	6,0,2,35,2,11
	.byte	'WUPBE',0,1
	.word	349
	.byte	1,7,2,35,3,11
	.byte	'MTSBE',0,1
	.word	349
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	349
	.byte	6,0,2,35,3,0,3
	.byte	'Ifx_ERAY_SIES_Bits',0,4,160,11,3
	.word	22150
	.byte	10
	.byte	'_Ifx_ERAY_SILS_Bits',0,4,163,11,16,4,11
	.byte	'WSTL',0,1
	.word	349
	.byte	1,7,2,35,0,11
	.byte	'CASL',0,1
	.word	349
	.byte	1,6,2,35,0,11
	.byte	'CYCSL',0,1
	.word	349
	.byte	1,5,2,35,0,11
	.byte	'TXIL',0,1
	.word	349
	.byte	1,4,2,35,0,11
	.byte	'RXIL',0,1
	.word	349
	.byte	1,3,2,35,0,11
	.byte	'RFNEL',0,1
	.word	349
	.byte	1,2,2,35,0,11
	.byte	'RFCLL',0,1
	.word	349
	.byte	1,1,2,35,0,11
	.byte	'NMVCL',0,1
	.word	349
	.byte	1,0,2,35,0,11
	.byte	'TI0L',0,1
	.word	349
	.byte	1,7,2,35,1,11
	.byte	'TI1L',0,1
	.word	349
	.byte	1,6,2,35,1,11
	.byte	'TIBCL',0,1
	.word	349
	.byte	1,5,2,35,1,11
	.byte	'TOBCL',0,1
	.word	349
	.byte	1,4,2,35,1,11
	.byte	'SWEL',0,1
	.word	349
	.byte	1,3,2,35,1,11
	.byte	'SUCSL',0,1
	.word	349
	.byte	1,2,2,35,1,11
	.byte	'MBSIL',0,1
	.word	349
	.byte	1,1,2,35,1,11
	.byte	'SDSL',0,1
	.word	349
	.byte	1,0,2,35,1,11
	.byte	'WUPAL',0,1
	.word	349
	.byte	1,7,2,35,2,11
	.byte	'MTSAL',0,1
	.word	349
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	349
	.byte	6,0,2,35,2,11
	.byte	'WUPBL',0,1
	.word	349
	.byte	1,7,2,35,3,11
	.byte	'MTSBL',0,1
	.word	349
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	349
	.byte	6,0,2,35,3,0,3
	.byte	'Ifx_ERAY_SILS_Bits',0,4,187,11,3
	.word	22583
	.byte	10
	.byte	'_Ifx_ERAY_SIR_Bits',0,4,190,11,16,4,11
	.byte	'WST',0,1
	.word	349
	.byte	1,7,2,35,0,11
	.byte	'CAS',0,1
	.word	349
	.byte	1,6,2,35,0,11
	.byte	'CYCS',0,1
	.word	349
	.byte	1,5,2,35,0,11
	.byte	'TXI',0,1
	.word	349
	.byte	1,4,2,35,0,11
	.byte	'RXI',0,1
	.word	349
	.byte	1,3,2,35,0,11
	.byte	'RFNE',0,1
	.word	349
	.byte	1,2,2,35,0,11
	.byte	'RFCL',0,1
	.word	349
	.byte	1,1,2,35,0,11
	.byte	'NMVC',0,1
	.word	349
	.byte	1,0,2,35,0,11
	.byte	'TI0',0,1
	.word	349
	.byte	1,7,2,35,1,11
	.byte	'TI1',0,1
	.word	349
	.byte	1,6,2,35,1,11
	.byte	'TIBC',0,1
	.word	349
	.byte	1,5,2,35,1,11
	.byte	'TOBC',0,1
	.word	349
	.byte	1,4,2,35,1,11
	.byte	'SWE',0,1
	.word	349
	.byte	1,3,2,35,1,11
	.byte	'SUCS',0,1
	.word	349
	.byte	1,2,2,35,1,11
	.byte	'MBSI',0,1
	.word	349
	.byte	1,1,2,35,1,11
	.byte	'SDS',0,1
	.word	349
	.byte	1,0,2,35,1,11
	.byte	'WUPA',0,1
	.word	349
	.byte	1,7,2,35,2,11
	.byte	'MTSA',0,1
	.word	349
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	349
	.byte	6,0,2,35,2,11
	.byte	'WUPB',0,1
	.word	349
	.byte	1,7,2,35,3,11
	.byte	'MTSB',0,1
	.word	349
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	349
	.byte	6,0,2,35,3,0,3
	.byte	'Ifx_ERAY_SIR_Bits',0,4,214,11,3
	.word	23016
	.byte	10
	.byte	'_Ifx_ERAY_STPW1_Bits',0,4,217,11,16,4,11
	.byte	'ESWT',0,1
	.word	349
	.byte	1,7,2,35,0,11
	.byte	'SWMS',0,1
	.word	349
	.byte	1,6,2,35,0,11
	.byte	'EDGE',0,1
	.word	349
	.byte	1,5,2,35,0,11
	.byte	'SSWT',0,1
	.word	349
	.byte	1,4,2,35,0,11
	.byte	'EETP',0,1
	.word	349
	.byte	1,3,2,35,0,11
	.byte	'EINT0',0,1
	.word	349
	.byte	1,2,2,35,0,11
	.byte	'EINT1',0,1
	.word	349
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	349
	.byte	1,0,2,35,0,11
	.byte	'SCCV',0,1
	.word	349
	.byte	6,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	349
	.byte	2,0,2,35,1,11
	.byte	'SMTV',0,2
	.word	380
	.byte	14,2,2,35,2,11
	.byte	'reserved_30',0,1
	.word	349
	.byte	2,0,2,35,3,0,3
	.byte	'Ifx_ERAY_STPW1_Bits',0,4,231,11,3
	.word	23427
	.byte	10
	.byte	'_Ifx_ERAY_STPW2_Bits',0,4,234,11,16,4,11
	.byte	'SSCVA',0,2
	.word	380
	.byte	11,5,2,35,0,11
	.byte	'reserved_11',0,1
	.word	349
	.byte	5,0,2,35,1,11
	.byte	'SSCVB',0,2
	.word	380
	.byte	11,5,2,35,2,11
	.byte	'reserved_27',0,1
	.word	349
	.byte	5,0,2,35,3,0,3
	.byte	'Ifx_ERAY_STPW2_Bits',0,4,240,11,3
	.word	23698
	.byte	10
	.byte	'_Ifx_ERAY_SUCC1_Bits',0,4,243,11,16,4,11
	.byte	'CMD',0,1
	.word	349
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	349
	.byte	3,1,2,35,0,11
	.byte	'PBSY',0,1
	.word	349
	.byte	1,0,2,35,0,11
	.byte	'TXST',0,1
	.word	349
	.byte	1,7,2,35,1,11
	.byte	'TXSY',0,1
	.word	349
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	349
	.byte	1,5,2,35,1,11
	.byte	'CSA',0,1
	.word	349
	.byte	5,0,2,35,1,11
	.byte	'PTA',0,1
	.word	349
	.byte	5,3,2,35,2,11
	.byte	'WUCS',0,1
	.word	349
	.byte	1,2,2,35,2,11
	.byte	'TSM',0,1
	.word	349
	.byte	1,1,2,35,2,11
	.byte	'HCSE',0,1
	.word	349
	.byte	1,0,2,35,2,11
	.byte	'MTSA',0,1
	.word	349
	.byte	1,7,2,35,3,11
	.byte	'MTSB',0,1
	.word	349
	.byte	1,6,2,35,3,11
	.byte	'CCHA',0,1
	.word	349
	.byte	1,5,2,35,3,11
	.byte	'CCHB',0,1
	.word	349
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	349
	.byte	4,0,2,35,3,0,3
	.byte	'Ifx_ERAY_SUCC1_Bits',0,4,133,12,3
	.word	23835
	.byte	10
	.byte	'_Ifx_ERAY_SUCC2_Bits',0,4,136,12,16,4,11
	.byte	'LT',0,4
	.word	264
	.byte	21,11,2,35,0,11
	.byte	'reserved_21',0,1
	.word	349
	.byte	3,0,2,35,2,11
	.byte	'LTN',0,1
	.word	349
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	349
	.byte	4,0,2,35,3,0,3
	.byte	'Ifx_ERAY_SUCC2_Bits',0,4,142,12,3
	.word	24164
	.byte	10
	.byte	'_Ifx_ERAY_SUCC3_Bits',0,4,145,12,16,4,11
	.byte	'WCP',0,1
	.word	349
	.byte	4,4,2,35,0,11
	.byte	'WCF',0,1
	.word	349
	.byte	4,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	264
	.byte	24,0,2,35,0,0,3
	.byte	'Ifx_ERAY_SUCC3_Bits',0,4,150,12,3
	.word	24296
	.byte	10
	.byte	'_Ifx_ERAY_SWNIT_Bits',0,4,153,12,16,4,11
	.byte	'SESA',0,1
	.word	349
	.byte	1,7,2,35,0,11
	.byte	'SBSA',0,1
	.word	349
	.byte	1,6,2,35,0,11
	.byte	'TCSA',0,1
	.word	349
	.byte	1,5,2,35,0,11
	.byte	'SESB',0,1
	.word	349
	.byte	1,4,2,35,0,11
	.byte	'SBSB',0,1
	.word	349
	.byte	1,3,2,35,0,11
	.byte	'TCSB',0,1
	.word	349
	.byte	1,2,2,35,0,11
	.byte	'MTSA',0,1
	.word	349
	.byte	1,1,2,35,0,11
	.byte	'MTSB',0,1
	.word	349
	.byte	1,0,2,35,0,11
	.byte	'SENA',0,1
	.word	349
	.byte	1,7,2,35,1,11
	.byte	'SBNA',0,1
	.word	349
	.byte	1,6,2,35,1,11
	.byte	'SENB',0,1
	.word	349
	.byte	1,5,2,35,1,11
	.byte	'SBNB',0,1
	.word	349
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	264
	.byte	20,0,2,35,0,0,3
	.byte	'Ifx_ERAY_SWNIT_Bits',0,4,168,12,3
	.word	24405
	.byte	10
	.byte	'_Ifx_ERAY_T0C_Bits',0,4,171,12,16,4,11
	.byte	'T0RC',0,1
	.word	349
	.byte	1,7,2,35,0,11
	.byte	'T0MS',0,1
	.word	349
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	349
	.byte	6,0,2,35,0,11
	.byte	'T0CC',0,1
	.word	349
	.byte	7,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	349
	.byte	1,0,2,35,1,11
	.byte	'T0MO',0,2
	.word	380
	.byte	14,2,2,35,2,11
	.byte	'reserved_30',0,1
	.word	349
	.byte	2,0,2,35,3,0,3
	.byte	'Ifx_ERAY_T0C_Bits',0,4,180,12,3
	.word	24677
	.byte	10
	.byte	'_Ifx_ERAY_T1C_Bits',0,4,183,12,16,4,11
	.byte	'T1RC',0,1
	.word	349
	.byte	1,7,2,35,0,11
	.byte	'T1MS',0,1
	.word	349
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	380
	.byte	14,0,2,35,0,11
	.byte	'T1MC',0,2
	.word	380
	.byte	14,2,2,35,2,11
	.byte	'reserved_30',0,1
	.word	349
	.byte	2,0,2,35,3,0,3
	.byte	'Ifx_ERAY_T1C_Bits',0,4,190,12,3
	.word	24862
	.byte	10
	.byte	'_Ifx_ERAY_TEST1_Bits',0,4,193,12,16,4,11
	.byte	'WRTEN',0,1
	.word	349
	.byte	1,7,2,35,0,11
	.byte	'ELBE',0,1
	.word	349
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	349
	.byte	2,4,2,35,0,11
	.byte	'TMC',0,1
	.word	349
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	349
	.byte	2,0,2,35,0,11
	.byte	'AOA',0,1
	.word	349
	.byte	1,7,2,35,1,11
	.byte	'AOB',0,1
	.word	349
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	349
	.byte	6,0,2,35,1,11
	.byte	'RXA',0,1
	.word	349
	.byte	1,7,2,35,2,11
	.byte	'RXB',0,1
	.word	349
	.byte	1,6,2,35,2,11
	.byte	'TXA',0,1
	.word	349
	.byte	1,5,2,35,2,11
	.byte	'TXB',0,1
	.word	349
	.byte	1,4,2,35,2,11
	.byte	'TXENA',0,1
	.word	349
	.byte	1,3,2,35,2,11
	.byte	'TXENB',0,1
	.word	349
	.byte	1,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	349
	.byte	2,0,2,35,2,11
	.byte	'CERA',0,1
	.word	349
	.byte	4,4,2,35,3,11
	.byte	'CERB',0,1
	.word	349
	.byte	4,0,2,35,3,0,3
	.byte	'Ifx_ERAY_TEST1_Bits',0,4,212,12,3
	.word	25008
	.byte	10
	.byte	'_Ifx_ERAY_TEST2_Bits',0,4,215,12,16,4,11
	.byte	'RS',0,1
	.word	349
	.byte	3,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	349
	.byte	1,4,2,35,0,11
	.byte	'SSEL',0,1
	.word	349
	.byte	3,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	380
	.byte	7,2,2,35,0,11
	.byte	'WRECC',0,1
	.word	349
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	264
	.byte	17,0,2,35,0,0,3
	.byte	'Ifx_ERAY_TEST2_Bits',0,4,223,12,3
	.word	25359
	.byte	10
	.byte	'_Ifx_ERAY_TXRQ1_Bits',0,4,226,12,16,4,11
	.byte	'TXR0',0,1
	.word	349
	.byte	1,7,2,35,0,11
	.byte	'TXR1',0,1
	.word	349
	.byte	1,6,2,35,0,11
	.byte	'TXR2',0,1
	.word	349
	.byte	1,5,2,35,0,11
	.byte	'TXR3',0,1
	.word	349
	.byte	1,4,2,35,0,11
	.byte	'TXR4',0,1
	.word	349
	.byte	1,3,2,35,0,11
	.byte	'TXR5',0,1
	.word	349
	.byte	1,2,2,35,0,11
	.byte	'TXR6',0,1
	.word	349
	.byte	1,1,2,35,0,11
	.byte	'TXR7',0,1
	.word	349
	.byte	1,0,2,35,0,11
	.byte	'TXR8',0,1
	.word	349
	.byte	1,7,2,35,1,11
	.byte	'TXR9',0,1
	.word	349
	.byte	1,6,2,35,1,11
	.byte	'TXR10',0,1
	.word	349
	.byte	1,5,2,35,1,11
	.byte	'TXR11',0,1
	.word	349
	.byte	1,4,2,35,1,11
	.byte	'TXR12',0,1
	.word	349
	.byte	1,3,2,35,1,11
	.byte	'TXR13',0,1
	.word	349
	.byte	1,2,2,35,1,11
	.byte	'TXR14',0,1
	.word	349
	.byte	1,1,2,35,1,11
	.byte	'TXR15',0,1
	.word	349
	.byte	1,0,2,35,1,11
	.byte	'TXR16',0,1
	.word	349
	.byte	1,7,2,35,2,11
	.byte	'TXR17',0,1
	.word	349
	.byte	1,6,2,35,2,11
	.byte	'TXR18',0,1
	.word	349
	.byte	1,5,2,35,2,11
	.byte	'TXR19',0,1
	.word	349
	.byte	1,4,2,35,2,11
	.byte	'TXR20',0,1
	.word	349
	.byte	1,3,2,35,2,11
	.byte	'TXR21',0,1
	.word	349
	.byte	1,2,2,35,2,11
	.byte	'TXR22',0,1
	.word	349
	.byte	1,1,2,35,2,11
	.byte	'TXR23',0,1
	.word	349
	.byte	1,0,2,35,2,11
	.byte	'TXR24',0,1
	.word	349
	.byte	1,7,2,35,3,11
	.byte	'TXR25',0,1
	.word	349
	.byte	1,6,2,35,3,11
	.byte	'TXR26',0,1
	.word	349
	.byte	1,5,2,35,3,11
	.byte	'TXR27',0,1
	.word	349
	.byte	1,4,2,35,3,11
	.byte	'TXR28',0,1
	.word	349
	.byte	1,3,2,35,3,11
	.byte	'TXR29',0,1
	.word	349
	.byte	1,2,2,35,3,11
	.byte	'TXR30',0,1
	.word	349
	.byte	1,1,2,35,3,11
	.byte	'TXR31',0,1
	.word	349
	.byte	1,0,2,35,3,0,3
	.byte	'Ifx_ERAY_TXRQ1_Bits',0,4,132,13,3
	.word	25530
	.byte	10
	.byte	'_Ifx_ERAY_TXRQ2_Bits',0,4,135,13,16,4,11
	.byte	'TXR32',0,1
	.word	349
	.byte	1,7,2,35,0,11
	.byte	'TXR33',0,1
	.word	349
	.byte	1,6,2,35,0,11
	.byte	'TXR34',0,1
	.word	349
	.byte	1,5,2,35,0,11
	.byte	'TXR35',0,1
	.word	349
	.byte	1,4,2,35,0,11
	.byte	'TXR36',0,1
	.word	349
	.byte	1,3,2,35,0,11
	.byte	'TXR37',0,1
	.word	349
	.byte	1,2,2,35,0,11
	.byte	'TXR38',0,1
	.word	349
	.byte	1,1,2,35,0,11
	.byte	'TXR39',0,1
	.word	349
	.byte	1,0,2,35,0,11
	.byte	'TXR40',0,1
	.word	349
	.byte	1,7,2,35,1,11
	.byte	'TXR41',0,1
	.word	349
	.byte	1,6,2,35,1,11
	.byte	'TXR42',0,1
	.word	349
	.byte	1,5,2,35,1,11
	.byte	'TXR43',0,1
	.word	349
	.byte	1,4,2,35,1,11
	.byte	'TXR44',0,1
	.word	349
	.byte	1,3,2,35,1,11
	.byte	'TXR45',0,1
	.word	349
	.byte	1,2,2,35,1,11
	.byte	'TXR46',0,1
	.word	349
	.byte	1,1,2,35,1,11
	.byte	'TXR47',0,1
	.word	349
	.byte	1,0,2,35,1,11
	.byte	'TXR48',0,1
	.word	349
	.byte	1,7,2,35,2,11
	.byte	'TXR49',0,1
	.word	349
	.byte	1,6,2,35,2,11
	.byte	'TXR50',0,1
	.word	349
	.byte	1,5,2,35,2,11
	.byte	'TXR51',0,1
	.word	349
	.byte	1,4,2,35,2,11
	.byte	'TXR52',0,1
	.word	349
	.byte	1,3,2,35,2,11
	.byte	'TXR53',0,1
	.word	349
	.byte	1,2,2,35,2,11
	.byte	'TXR54',0,1
	.word	349
	.byte	1,1,2,35,2,11
	.byte	'TXR55',0,1
	.word	349
	.byte	1,0,2,35,2,11
	.byte	'TXR56',0,1
	.word	349
	.byte	1,7,2,35,3,11
	.byte	'TXR57',0,1
	.word	349
	.byte	1,6,2,35,3,11
	.byte	'TXR58',0,1
	.word	349
	.byte	1,5,2,35,3,11
	.byte	'TXR59',0,1
	.word	349
	.byte	1,4,2,35,3,11
	.byte	'TXR60',0,1
	.word	349
	.byte	1,3,2,35,3,11
	.byte	'TXR61',0,1
	.word	349
	.byte	1,2,2,35,3,11
	.byte	'TXR62',0,1
	.word	349
	.byte	1,1,2,35,3,11
	.byte	'TXR63',0,1
	.word	349
	.byte	1,0,2,35,3,0,3
	.byte	'Ifx_ERAY_TXRQ2_Bits',0,4,169,13,3
	.word	26121
	.byte	10
	.byte	'_Ifx_ERAY_TXRQ3_Bits',0,4,172,13,16,4,11
	.byte	'TXR64',0,1
	.word	349
	.byte	1,7,2,35,0,11
	.byte	'TXR65',0,1
	.word	349
	.byte	1,6,2,35,0,11
	.byte	'TXR66',0,1
	.word	349
	.byte	1,5,2,35,0,11
	.byte	'TXR67',0,1
	.word	349
	.byte	1,4,2,35,0,11
	.byte	'TXR68',0,1
	.word	349
	.byte	1,3,2,35,0,11
	.byte	'TXR69',0,1
	.word	349
	.byte	1,2,2,35,0,11
	.byte	'TXR70',0,1
	.word	349
	.byte	1,1,2,35,0,11
	.byte	'TXR71',0,1
	.word	349
	.byte	1,0,2,35,0,11
	.byte	'TXR72',0,1
	.word	349
	.byte	1,7,2,35,1,11
	.byte	'TXR73',0,1
	.word	349
	.byte	1,6,2,35,1,11
	.byte	'TXR74',0,1
	.word	349
	.byte	1,5,2,35,1,11
	.byte	'TXR75',0,1
	.word	349
	.byte	1,4,2,35,1,11
	.byte	'TXR76',0,1
	.word	349
	.byte	1,3,2,35,1,11
	.byte	'TXR77',0,1
	.word	349
	.byte	1,2,2,35,1,11
	.byte	'TXR78',0,1
	.word	349
	.byte	1,1,2,35,1,11
	.byte	'TXR79',0,1
	.word	349
	.byte	1,0,2,35,1,11
	.byte	'TXR80',0,1
	.word	349
	.byte	1,7,2,35,2,11
	.byte	'TXR81',0,1
	.word	349
	.byte	1,6,2,35,2,11
	.byte	'TXR82',0,1
	.word	349
	.byte	1,5,2,35,2,11
	.byte	'TXR83',0,1
	.word	349
	.byte	1,4,2,35,2,11
	.byte	'TXR84',0,1
	.word	349
	.byte	1,3,2,35,2,11
	.byte	'TXR85',0,1
	.word	349
	.byte	1,2,2,35,2,11
	.byte	'TXR86',0,1
	.word	349
	.byte	1,1,2,35,2,11
	.byte	'TXR87',0,1
	.word	349
	.byte	1,0,2,35,2,11
	.byte	'TXR88',0,1
	.word	349
	.byte	1,7,2,35,3,11
	.byte	'TXR89',0,1
	.word	349
	.byte	1,6,2,35,3,11
	.byte	'TXR90',0,1
	.word	349
	.byte	1,5,2,35,3,11
	.byte	'TXR91',0,1
	.word	349
	.byte	1,4,2,35,3,11
	.byte	'TXR92',0,1
	.word	349
	.byte	1,3,2,35,3,11
	.byte	'TXR93',0,1
	.word	349
	.byte	1,2,2,35,3,11
	.byte	'TXR94',0,1
	.word	349
	.byte	1,1,2,35,3,11
	.byte	'TXR95',0,1
	.word	349
	.byte	1,0,2,35,3,0,3
	.byte	'Ifx_ERAY_TXRQ3_Bits',0,4,206,13,3
	.word	26722
	.byte	10
	.byte	'_Ifx_ERAY_TXRQ4_Bits',0,4,209,13,16,4,11
	.byte	'TXR96',0,1
	.word	349
	.byte	1,7,2,35,0,11
	.byte	'TXR97',0,1
	.word	349
	.byte	1,6,2,35,0,11
	.byte	'TXR98',0,1
	.word	349
	.byte	1,5,2,35,0,11
	.byte	'TXR99',0,1
	.word	349
	.byte	1,4,2,35,0,11
	.byte	'TXR100',0,1
	.word	349
	.byte	1,3,2,35,0,11
	.byte	'TXR101',0,1
	.word	349
	.byte	1,2,2,35,0,11
	.byte	'TXR102',0,1
	.word	349
	.byte	1,1,2,35,0,11
	.byte	'TXR103',0,1
	.word	349
	.byte	1,0,2,35,0,11
	.byte	'TXR104',0,1
	.word	349
	.byte	1,7,2,35,1,11
	.byte	'TXR105',0,1
	.word	349
	.byte	1,6,2,35,1,11
	.byte	'TXR106',0,1
	.word	349
	.byte	1,5,2,35,1,11
	.byte	'TXR107',0,1
	.word	349
	.byte	1,4,2,35,1,11
	.byte	'TXR108',0,1
	.word	349
	.byte	1,3,2,35,1,11
	.byte	'TXR109',0,1
	.word	349
	.byte	1,2,2,35,1,11
	.byte	'TXR110',0,1
	.word	349
	.byte	1,1,2,35,1,11
	.byte	'TXR111',0,1
	.word	349
	.byte	1,0,2,35,1,11
	.byte	'TXR112',0,1
	.word	349
	.byte	1,7,2,35,2,11
	.byte	'TXR113',0,1
	.word	349
	.byte	1,6,2,35,2,11
	.byte	'TXR114',0,1
	.word	349
	.byte	1,5,2,35,2,11
	.byte	'TXR115',0,1
	.word	349
	.byte	1,4,2,35,2,11
	.byte	'TXR116',0,1
	.word	349
	.byte	1,3,2,35,2,11
	.byte	'TXR117',0,1
	.word	349
	.byte	1,2,2,35,2,11
	.byte	'TXR118',0,1
	.word	349
	.byte	1,1,2,35,2,11
	.byte	'TXR119',0,1
	.word	349
	.byte	1,0,2,35,2,11
	.byte	'TXR120',0,1
	.word	349
	.byte	1,7,2,35,3,11
	.byte	'TXR121',0,1
	.word	349
	.byte	1,6,2,35,3,11
	.byte	'TXR122',0,1
	.word	349
	.byte	1,5,2,35,3,11
	.byte	'TXR123',0,1
	.word	349
	.byte	1,4,2,35,3,11
	.byte	'TXR124',0,1
	.word	349
	.byte	1,3,2,35,3,11
	.byte	'TXR125',0,1
	.word	349
	.byte	1,2,2,35,3,11
	.byte	'TXR126',0,1
	.word	349
	.byte	1,1,2,35,3,11
	.byte	'TXR127',0,1
	.word	349
	.byte	1,0,2,35,3,0,3
	.byte	'Ifx_ERAY_TXRQ4_Bits',0,4,243,13,3
	.word	27323
	.byte	10
	.byte	'_Ifx_ERAY_WRDS_Bits',0,4,246,13,16,4,11
	.byte	'MDWB0',0,1
	.word	349
	.byte	8,0,2,35,0,11
	.byte	'MDWB1',0,1
	.word	349
	.byte	8,0,2,35,1,11
	.byte	'MDWB2',0,1
	.word	349
	.byte	8,0,2,35,2,11
	.byte	'MDWB3',0,1
	.word	349
	.byte	8,0,2,35,3,0,3
	.byte	'Ifx_ERAY_WRDS_Bits',0,4,252,13,3
	.word	27952
	.byte	10
	.byte	'_Ifx_ERAY_WRHS1_Bits',0,4,255,13,16,4,11
	.byte	'FID',0,2
	.word	380
	.byte	11,5,2,35,0,11
	.byte	'reserved_11',0,1
	.word	349
	.byte	5,0,2,35,1,11
	.byte	'CYC',0,1
	.word	349
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	349
	.byte	1,0,2,35,2,11
	.byte	'CHA',0,1
	.word	349
	.byte	1,7,2,35,3,11
	.byte	'CHB',0,1
	.word	349
	.byte	1,6,2,35,3,11
	.byte	'CFG',0,1
	.word	349
	.byte	1,5,2,35,3,11
	.byte	'PPIT',0,1
	.word	349
	.byte	1,4,2,35,3,11
	.byte	'TXM',0,1
	.word	349
	.byte	1,3,2,35,3,11
	.byte	'MBI',0,1
	.word	349
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	349
	.byte	2,0,2,35,3,0,3
	.byte	'Ifx_ERAY_WRHS1_Bits',0,4,140,14,3
	.word	28075
	.byte	10
	.byte	'_Ifx_ERAY_WRHS2_Bits',0,4,143,14,16,4,11
	.byte	'CRC',0,2
	.word	380
	.byte	11,5,2,35,0,11
	.byte	'reserved_11',0,1
	.word	349
	.byte	5,0,2,35,1,11
	.byte	'PLC',0,1
	.word	349
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	380
	.byte	9,0,2,35,2,0,3
	.byte	'Ifx_ERAY_WRHS2_Bits',0,4,149,14,3
	.word	28322
	.byte	10
	.byte	'_Ifx_ERAY_WRHS3_Bits',0,4,152,14,16,4,11
	.byte	'DP',0,2
	.word	380
	.byte	11,5,2,35,0,11
	.byte	'reserved_11',0,4
	.word	264
	.byte	21,0,2,35,0,0,3
	.byte	'Ifx_ERAY_WRHS3_Bits',0,4,156,14,3
	.word	28455
	.byte	12,4,164,14,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	677
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_ACCEN0',0,4,169,14,3
	.word	28549
	.byte	12,4,172,14,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	1236
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_ACCEN1',0,4,177,14,3
	.word	28614
	.byte	12,4,180,14,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	1315
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_ACS',0,4,185,14,3
	.word	28679
	.byte	12,4,188,14,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	1569
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_CCEV',0,4,193,14,3
	.word	28741
	.byte	12,4,196,14,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	1715
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_CCSV',0,4,201,14,3
	.word	28804
	.byte	12,4,204,14,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	1991
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_CLC',0,4,209,14,3
	.word	28867
	.byte	12,4,212,14,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	2174
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_CREL',0,4,217,14,3
	.word	28929
	.byte	12,4,220,14,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	2325
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_CUST1',0,4,225,14,3
	.word	28992
	.byte	12,4,228,14,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	2599
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_CUST3',0,4,233,14,3
	.word	29056
	.byte	12,4,236,14,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	2670
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_EIER',0,4,241,14,3
	.word	29120
	.byte	12,4,244,14,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	3091
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_EIES',0,4,249,14,3
	.word	29183
	.byte	12,4,252,14,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	3512
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_EILS',0,4,129,15,3
	.word	29246
	.byte	12,4,132,15,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	3933
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_EIR',0,4,137,15,3
	.word	29309
	.byte	12,4,140,15,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	4334
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_ENDN',0,4,145,15,3
	.word	29371
	.byte	12,4,148,15,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	4404
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_ESID',0,4,153,15,3
	.word	29434
	.byte	12,4,156,15,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	4552
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_FCL',0,4,161,15,3
	.word	29497
	.byte	12,4,164,15,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	4641
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_FRF',0,4,169,15,3
	.word	29559
	.byte	12,4,172,15,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	4814
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_FRFM',0,4,177,15,3
	.word	29621
	.byte	12,4,180,15,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	4930
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_FSR',0,4,185,15,3
	.word	29684
	.byte	12,4,188,15,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	5091
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_GTUC01',0,4,193,15,3
	.word	29746
	.byte	12,4,196,15,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	5187
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_GTUC02',0,4,201,15,3
	.word	29811
	.byte	12,4,204,15,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	5322
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_GTUC03',0,4,209,15,3
	.word	29876
	.byte	12,4,212,15,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	5491
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_GTUC04',0,4,217,15,3
	.word	29941
	.byte	12,4,220,15,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	5626
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_GTUC05',0,4,225,15,3
	.word	30006
	.byte	12,4,228,15,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	5768
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_GTUC06',0,4,233,15,3
	.word	30071
	.byte	12,4,236,15,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	5903
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_GTUC07',0,4,241,15,3
	.word	30136
	.byte	12,4,244,15,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	6038
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_GTUC08',0,4,249,15,3
	.word	30201
	.byte	12,4,252,15,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	6172
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_GTUC09',0,4,129,16,3
	.word	30266
	.byte	12,4,132,16,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	6345
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_GTUC10',0,4,137,16,3
	.word	30331
	.byte	12,4,140,16,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	6480
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_GTUC11',0,4,145,16,3
	.word	30396
	.byte	12,4,148,16,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	6692
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_IBCM',0,4,153,16,3
	.word	30461
	.byte	12,4,156,16,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	6890
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_IBCR',0,4,161,16,3
	.word	30524
	.byte	12,4,164,16,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	7056
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_ID',0,4,169,16,3
	.word	30587
	.byte	12,4,172,16,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	7165
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_ILE',0,4,177,16,3
	.word	30648
	.byte	12,4,180,16,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	7274
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_KRST0',0,4,185,16,3
	.word	30710
	.byte	12,4,188,16,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	7387
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_KRST1',0,4,193,16,3
	.word	30774
	.byte	12,4,196,16,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	7481
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_KRSTCLR',0,4,201,16,3
	.word	30838
	.byte	12,4,204,16,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	7579
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_LCK',0,4,209,16,3
	.word	30904
	.byte	12,4,212,16,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	7685
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_LDTS',0,4,217,16,3
	.word	30966
	.byte	12,4,220,16,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	7818
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_MBS',0,4,225,16,3
	.word	31029
	.byte	12,4,228,16,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	8287
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_MBSC1',0,4,233,16,3
	.word	31091
	.byte	12,4,236,16,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	8878
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_MBSC2',0,4,241,16,3
	.word	31155
	.byte	12,4,244,16,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	9479
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_MBSC3',0,4,249,16,3
	.word	31219
	.byte	12,4,252,16,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	10080
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_MBSC4',0,4,129,17,3
	.word	31283
	.byte	12,4,132,17,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	10709
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_MHDC',0,4,137,17,3
	.word	31347
	.byte	12,4,140,17,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	10840
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_MHDF',0,4,145,17,3
	.word	31410
	.byte	12,4,148,17,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	11061
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_MHDS',0,4,153,17,3
	.word	31473
	.byte	12,4,156,17,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	11359
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_MRC',0,4,161,17,3
	.word	31536
	.byte	12,4,164,17,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	11511
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_MSIC1',0,4,169,17,3
	.word	31598
	.byte	12,4,172,17,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	12134
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_MSIC2',0,4,177,17,3
	.word	31662
	.byte	12,4,180,17,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	12767
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_MSIC3',0,4,185,17,3
	.word	31726
	.byte	12,4,188,17,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	13400
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_MSIC4',0,4,193,17,3
	.word	31790
	.byte	12,4,196,17,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	14061
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_MTCCV',0,4,201,17,3
	.word	31854
	.byte	12,4,204,17,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	14194
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_NDAT1',0,4,209,17,3
	.word	31918
	.byte	12,4,212,17,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	14753
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_NDAT2',0,4,217,17,3
	.word	31982
	.byte	12,4,220,17,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	15322
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_NDAT3',0,4,225,17,3
	.word	32046
	.byte	12,4,228,17,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	15891
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_NDAT4',0,4,233,17,3
	.word	32110
	.byte	12,4,236,17,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	16488
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_NDIC1',0,4,241,17,3
	.word	32174
	.byte	12,4,244,17,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	17111
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_NDIC2',0,4,249,17,3
	.word	32238
	.byte	12,4,252,17,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	17744
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_NDIC3',0,4,129,18,3
	.word	32302
	.byte	12,4,132,18,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	18377
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_NDIC4',0,4,137,18,3
	.word	32366
	.byte	12,4,140,18,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	19038
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_NEMC',0,4,145,18,3
	.word	32430
	.byte	12,4,148,18,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	19130
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_NMV',0,4,153,18,3
	.word	32493
	.byte	12,4,156,18,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	19197
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_OBCM',0,4,161,18,3
	.word	32555
	.byte	12,4,164,18,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	19361
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_OBCR',0,4,169,18,3
	.word	32618
	.byte	12,4,172,18,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	19564
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_OCS',0,4,177,18,3
	.word	32681
	.byte	12,4,180,18,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	19712
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_OCV',0,4,185,18,3
	.word	32743
	.byte	12,4,188,18,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	19803
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_OSID',0,4,193,18,3
	.word	32805
	.byte	12,4,196,18,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	19951
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_OTSS',0,4,201,18,3
	.word	32868
	.byte	12,4,204,18,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	20125
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_PRTC1',0,4,209,18,3
	.word	32931
	.byte	12,4,212,18,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	20320
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_PRTC2',0,4,217,18,3
	.word	32995
	.byte	12,4,220,18,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	20505
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_RCV',0,4,225,18,3
	.word	33059
	.byte	12,4,228,18,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	20596
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_RDDS',0,4,233,18,3
	.word	33121
	.byte	12,4,236,18,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	20719
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_RDHS1',0,4,241,18,3
	.word	33184
	.byte	12,4,244,18,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	20966
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_RDHS2',0,4,249,18,3
	.word	33248
	.byte	12,4,252,18,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	21137
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_RDHS3',0,4,129,19,3
	.word	33312
	.byte	12,4,132,19,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	21382
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_SCV',0,4,137,19,3
	.word	33376
	.byte	12,4,140,19,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	21513
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_SFS',0,4,145,19,3
	.word	33438
	.byte	12,4,148,19,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	21717
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_SIER',0,4,153,19,3
	.word	33500
	.byte	12,4,156,19,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	22150
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_SIES',0,4,161,19,3
	.word	33563
	.byte	12,4,164,19,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	22583
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_SILS',0,4,169,19,3
	.word	33626
	.byte	12,4,172,19,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	23016
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_SIR',0,4,177,19,3
	.word	33689
	.byte	12,4,180,19,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	23427
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_STPW1',0,4,185,19,3
	.word	33751
	.byte	12,4,188,19,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	23698
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_STPW2',0,4,193,19,3
	.word	33815
	.byte	12,4,196,19,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	23835
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_SUCC1',0,4,201,19,3
	.word	33879
	.byte	12,4,204,19,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	24164
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_SUCC2',0,4,209,19,3
	.word	33943
	.byte	12,4,212,19,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	24296
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_SUCC3',0,4,217,19,3
	.word	34007
	.byte	12,4,220,19,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	24405
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_SWNIT',0,4,225,19,3
	.word	34071
	.byte	12,4,228,19,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	24677
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_T0C',0,4,233,19,3
	.word	34135
	.byte	12,4,236,19,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	24862
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_T1C',0,4,241,19,3
	.word	34197
	.byte	12,4,244,19,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	25008
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_TEST1',0,4,249,19,3
	.word	34259
	.byte	12,4,252,19,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	25359
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_TEST2',0,4,129,20,3
	.word	34323
	.byte	12,4,132,20,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	25530
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_TXRQ1',0,4,137,20,3
	.word	34387
	.byte	12,4,140,20,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	26121
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_TXRQ2',0,4,145,20,3
	.word	34451
	.byte	12,4,148,20,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	26722
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_TXRQ3',0,4,153,20,3
	.word	34515
	.byte	12,4,156,20,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	27323
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_TXRQ4',0,4,161,20,3
	.word	34579
	.byte	12,4,164,20,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	27952
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_WRDS',0,4,169,20,3
	.word	34643
	.byte	12,4,172,20,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	28075
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_WRHS1',0,4,177,20,3
	.word	34706
	.byte	12,4,180,20,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	28322
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_WRHS2',0,4,185,20,3
	.word	34770
	.byte	12,4,188,20,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	28455
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ERAY_WRHS3',0,4,193,20,3
	.word	34834
	.byte	13,8
	.word	611
	.byte	14,0,0
.L8:
	.byte	15
	.word	34898
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L4:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,36,0,3,8,11,15,62,15,0,0,3,22,0,3,8,58,15,59,15,57,15
	.byte	73,19,0,0,4,21,0,54,15,0,0,5,15,0,73,19,0,0,6,59,0,3,8,0,0,7,53,0,73,19,0,0,8,19,1,58,15,59,15,57,15,11
	.byte	15,0,0,9,13,0,3,8,73,19,11,15,56,9,0,0,10,19,1,3,8,58,15,59,15,57,15,11,15,0,0,11,13,0,3,8,11,15,73,19
	.byte	13,15,12,15,56,9,0,0,12,23,1,58,15,59,15,57,15,11,15,0,0,13,1,1,11,15,73,19,0,0,14,33,0,47,15,0,0,15,38
	.byte	0,73,19,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L5:
	.word	.L10-.L9
.L9:
	.half	3
	.word	.L12-.L11
.L11:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg',0
	.byte	0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxEray_cfg.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Platform_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Ifx_Types.h',0
	.byte	0,0,0
	.byte	'IfxEray_regdef.h',0,1,0,0,0
.L12:
.L10:
	.sdecl	'.debug_info',debug,cluster('IfxEray_cfg_indexMap')
	.sect	'.debug_info'
.L6:
	.word	267
	.half	3
	.word	.L7
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxEray_cfg.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxEray_cfg_indexMap',0,1,55,30
	.word	.L8
	.byte	1,5,3
	.word	IfxEray_cfg_indexMap
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxEray_cfg_indexMap')
	.sect	'.debug_abbrev'
.L7:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	; Module end
