	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc42196a --dep-file=IfxDsadc_PinMap.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxDsadc_PinMap.src ../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxDsadc_PinMap.c"
	.compiler_name		"ctc"
	;source	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxDsadc_PinMap.c'

	
$TC16X
	
	.sdecl	'.rodata.IfxDsadc_PinMap.IfxDsadc_CGPWMN_P00_5_OUT',data,rom,cluster('IfxDsadc_CGPWMN_P00_5_OUT')
	.sect	'.rodata.IfxDsadc_PinMap.IfxDsadc_CGPWMN_P00_5_OUT'
	.global	IfxDsadc_CGPWMN_P00_5_OUT
	.align	4
IfxDsadc_CGPWMN_P00_5_OUT:	.type	object
	.size	IfxDsadc_CGPWMN_P00_5_OUT,16
	.word	-268288000,-268197888
	.byte	5
	.space	3
	.byte	144
	.space	3
	.sdecl	'.rodata.IfxDsadc_PinMap.IfxDsadc_CGPWMN_P02_0_OUT',data,rom,cluster('IfxDsadc_CGPWMN_P02_0_OUT')
	.sect	'.rodata.IfxDsadc_PinMap.IfxDsadc_CGPWMN_P02_0_OUT'
	.global	IfxDsadc_CGPWMN_P02_0_OUT
	.align	4
IfxDsadc_CGPWMN_P02_0_OUT:	.type	object
	.size	IfxDsadc_CGPWMN_P02_0_OUT,16
	.word	-268288000,-268197376
	.space	4
	.byte	160
	.space	3
	.sdecl	'.rodata.IfxDsadc_PinMap.IfxDsadc_CGPWMN_P33_11_OUT',data,rom,cluster('IfxDsadc_CGPWMN_P33_11_OUT')
	.sect	'.rodata.IfxDsadc_PinMap.IfxDsadc_CGPWMN_P33_11_OUT'
	.global	IfxDsadc_CGPWMN_P33_11_OUT
	.align	4
IfxDsadc_CGPWMN_P33_11_OUT:	.type	object
	.size	IfxDsadc_CGPWMN_P33_11_OUT,16
	.word	-268288000,-268184832
	.byte	11
	.space	3
	.byte	176
	.space	3
	.sdecl	'.rodata.IfxDsadc_PinMap.IfxDsadc_CGPWMP_P00_6_OUT',data,rom,cluster('IfxDsadc_CGPWMP_P00_6_OUT')
	.sect	'.rodata.IfxDsadc_PinMap.IfxDsadc_CGPWMP_P00_6_OUT'
	.global	IfxDsadc_CGPWMP_P00_6_OUT
	.align	4
IfxDsadc_CGPWMP_P00_6_OUT:	.type	object
	.size	IfxDsadc_CGPWMP_P00_6_OUT,16
	.word	-268288000,-268197888
	.byte	6
	.space	3
	.byte	144
	.space	3
	.sdecl	'.rodata.IfxDsadc_PinMap.IfxDsadc_CGPWMP_P02_1_OUT',data,rom,cluster('IfxDsadc_CGPWMP_P02_1_OUT')
	.sect	'.rodata.IfxDsadc_PinMap.IfxDsadc_CGPWMP_P02_1_OUT'
	.global	IfxDsadc_CGPWMP_P02_1_OUT
	.align	4
IfxDsadc_CGPWMP_P02_1_OUT:	.type	object
	.size	IfxDsadc_CGPWMP_P02_1_OUT,16
	.word	-268288000,-268197376
	.byte	1
	.space	3
	.byte	160
	.space	3
	.sdecl	'.rodata.IfxDsadc_PinMap.IfxDsadc_CGPWMP_P33_12_OUT',data,rom,cluster('IfxDsadc_CGPWMP_P33_12_OUT')
	.sect	'.rodata.IfxDsadc_PinMap.IfxDsadc_CGPWMP_P33_12_OUT'
	.global	IfxDsadc_CGPWMP_P33_12_OUT
	.align	4
IfxDsadc_CGPWMP_P33_12_OUT:	.type	object
	.size	IfxDsadc_CGPWMP_P33_12_OUT,16
	.word	-268288000,-268184832
	.byte	12
	.space	3
	.byte	176
	.space	3
	.sdecl	'.rodata.IfxDsadc_PinMap.IfxDsadc_CIN0A_P00_1_IN',data,rom,cluster('IfxDsadc_CIN0A_P00_1_IN')
	.sect	'.rodata.IfxDsadc_PinMap.IfxDsadc_CIN0A_P00_1_IN'
	.global	IfxDsadc_CIN0A_P00_1_IN
	.align	4
IfxDsadc_CIN0A_P00_1_IN:	.type	object
	.size	IfxDsadc_CIN0A_P00_1_IN,20
	.word	-268288000
	.space	4
	.word	-268197888
	.byte	1
	.space	7
	.sdecl	'.rodata.IfxDsadc_PinMap.IfxDsadc_CIN0B_P33_5_IN',data,rom,cluster('IfxDsadc_CIN0B_P33_5_IN')
	.sect	'.rodata.IfxDsadc_PinMap.IfxDsadc_CIN0B_P33_5_IN'
	.global	IfxDsadc_CIN0B_P33_5_IN
	.align	4
IfxDsadc_CIN0B_P33_5_IN:	.type	object
	.size	IfxDsadc_CIN0B_P33_5_IN,20
	.word	-268288000
	.space	4
	.word	-268184832
	.byte	5
	.space	3
	.byte	1
	.space	3
	.sdecl	'.rodata.IfxDsadc_PinMap.IfxDsadc_CIN2A_P00_5_IN',data,rom,cluster('IfxDsadc_CIN2A_P00_5_IN')
	.sect	'.rodata.IfxDsadc_PinMap.IfxDsadc_CIN2A_P00_5_IN'
	.global	IfxDsadc_CIN2A_P00_5_IN
	.align	4
IfxDsadc_CIN2A_P00_5_IN:	.type	object
	.size	IfxDsadc_CIN2A_P00_5_IN,20
	.word	-268288000
	.byte	2
	.space	3
	.word	-268197888
	.byte	5
	.space	7
	.sdecl	'.rodata.IfxDsadc_PinMap.IfxDsadc_CIN2B_P33_1_IN',data,rom,cluster('IfxDsadc_CIN2B_P33_1_IN')
	.sect	'.rodata.IfxDsadc_PinMap.IfxDsadc_CIN2B_P33_1_IN'
	.global	IfxDsadc_CIN2B_P33_1_IN
	.align	4
IfxDsadc_CIN2B_P33_1_IN:	.type	object
	.size	IfxDsadc_CIN2B_P33_1_IN,20
	.word	-268288000
	.byte	2
	.space	3
	.word	-268184832
	.byte	1
	.space	3
	.byte	1
	.space	3
	.sdecl	'.rodata.IfxDsadc_PinMap.IfxDsadc_CIN3A_P00_3_IN',data,rom,cluster('IfxDsadc_CIN3A_P00_3_IN')
	.sect	'.rodata.IfxDsadc_PinMap.IfxDsadc_CIN3A_P00_3_IN'
	.global	IfxDsadc_CIN3A_P00_3_IN
	.align	4
IfxDsadc_CIN3A_P00_3_IN:	.type	object
	.size	IfxDsadc_CIN3A_P00_3_IN,20
	.word	-268288000
	.byte	3
	.space	3
	.word	-268197888
	.byte	3
	.space	7
	.sdecl	'.rodata.IfxDsadc_PinMap.IfxDsadc_CIN3B_P02_7_IN',data,rom,cluster('IfxDsadc_CIN3B_P02_7_IN')
	.sect	'.rodata.IfxDsadc_PinMap.IfxDsadc_CIN3B_P02_7_IN'
	.global	IfxDsadc_CIN3B_P02_7_IN
	.align	4
IfxDsadc_CIN3B_P02_7_IN:	.type	object
	.size	IfxDsadc_CIN3B_P02_7_IN,20
	.word	-268288000
	.byte	3
	.space	3
	.word	-268197376
	.byte	7
	.space	3
	.byte	1
	.space	3
	.sdecl	'.rodata.IfxDsadc_PinMap.IfxDsadc_COUT0_P00_11_OUT',data,rom,cluster('IfxDsadc_COUT0_P00_11_OUT')
	.sect	'.rodata.IfxDsadc_PinMap.IfxDsadc_COUT0_P00_11_OUT'
	.global	IfxDsadc_COUT0_P00_11_OUT
	.align	4
IfxDsadc_COUT0_P00_11_OUT:	.type	object
	.size	IfxDsadc_COUT0_P00_11_OUT,20
	.word	-268288000
	.space	4
	.word	-268197888
	.byte	11
	.space	3
	.byte	160
	.space	3
	.sdecl	'.rodata.IfxDsadc_PinMap.IfxDsadc_COUT0_P00_1_OUT',data,rom,cluster('IfxDsadc_COUT0_P00_1_OUT')
	.sect	'.rodata.IfxDsadc_PinMap.IfxDsadc_COUT0_P00_1_OUT'
	.global	IfxDsadc_COUT0_P00_1_OUT
	.align	4
IfxDsadc_COUT0_P00_1_OUT:	.type	object
	.size	IfxDsadc_COUT0_P00_1_OUT,20
	.word	-268288000
	.space	4
	.word	-268197888
	.byte	1
	.space	3
	.byte	160
	.space	3
	.sdecl	'.rodata.IfxDsadc_PinMap.IfxDsadc_COUT0_P33_5_OUT',data,rom,cluster('IfxDsadc_COUT0_P33_5_OUT')
	.sect	'.rodata.IfxDsadc_PinMap.IfxDsadc_COUT0_P33_5_OUT'
	.global	IfxDsadc_COUT0_P33_5_OUT
	.align	4
IfxDsadc_COUT0_P33_5_OUT:	.type	object
	.size	IfxDsadc_COUT0_P33_5_OUT,20
	.word	-268288000
	.space	4
	.word	-268184832
	.byte	5
	.space	3
	.byte	160
	.space	3
	.sdecl	'.rodata.IfxDsadc_PinMap.IfxDsadc_COUT2_P00_5_OUT',data,rom,cluster('IfxDsadc_COUT2_P00_5_OUT')
	.sect	'.rodata.IfxDsadc_PinMap.IfxDsadc_COUT2_P00_5_OUT'
	.global	IfxDsadc_COUT2_P00_5_OUT
	.align	4
IfxDsadc_COUT2_P00_5_OUT:	.type	object
	.size	IfxDsadc_COUT2_P00_5_OUT,20
	.word	-268288000
	.byte	2
	.space	3
	.word	-268197888
	.byte	5
	.space	3
	.byte	160
	.space	3
	.sdecl	'.rodata.IfxDsadc_PinMap.IfxDsadc_COUT2_P33_1_OUT',data,rom,cluster('IfxDsadc_COUT2_P33_1_OUT')
	.sect	'.rodata.IfxDsadc_PinMap.IfxDsadc_COUT2_P33_1_OUT'
	.global	IfxDsadc_COUT2_P33_1_OUT
	.align	4
IfxDsadc_COUT2_P33_1_OUT:	.type	object
	.size	IfxDsadc_COUT2_P33_1_OUT,20
	.word	-268288000
	.byte	2
	.space	3
	.word	-268184832
	.byte	1
	.space	3
	.byte	160
	.space	3
	.sdecl	'.rodata.IfxDsadc_PinMap.IfxDsadc_COUT3_P00_3_OUT',data,rom,cluster('IfxDsadc_COUT3_P00_3_OUT')
	.sect	'.rodata.IfxDsadc_PinMap.IfxDsadc_COUT3_P00_3_OUT'
	.global	IfxDsadc_COUT3_P00_3_OUT
	.align	4
IfxDsadc_COUT3_P00_3_OUT:	.type	object
	.size	IfxDsadc_COUT3_P00_3_OUT,20
	.word	-268288000
	.byte	3
	.space	3
	.word	-268197888
	.byte	3
	.space	3
	.byte	160
	.space	3
	.sdecl	'.rodata.IfxDsadc_PinMap.IfxDsadc_COUT3_P02_7_OUT',data,rom,cluster('IfxDsadc_COUT3_P02_7_OUT')
	.sect	'.rodata.IfxDsadc_PinMap.IfxDsadc_COUT3_P02_7_OUT'
	.global	IfxDsadc_COUT3_P02_7_OUT
	.align	4
IfxDsadc_COUT3_P02_7_OUT:	.type	object
	.size	IfxDsadc_COUT3_P02_7_OUT,20
	.word	-268288000
	.byte	3
	.space	3
	.word	-268197376
	.byte	7
	.space	3
	.byte	160
	.space	3
	.sdecl	'.rodata.IfxDsadc_PinMap.IfxDsadc_DIN0A_P00_2_IN',data,rom,cluster('IfxDsadc_DIN0A_P00_2_IN')
	.sect	'.rodata.IfxDsadc_PinMap.IfxDsadc_DIN0A_P00_2_IN'
	.global	IfxDsadc_DIN0A_P00_2_IN
	.align	4
IfxDsadc_DIN0A_P00_2_IN:	.type	object
	.size	IfxDsadc_DIN0A_P00_2_IN,20
	.word	-268288000
	.space	4
	.word	-268197888
	.byte	2
	.space	7
	.sdecl	'.rodata.IfxDsadc_PinMap.IfxDsadc_DIN0B_P33_6_IN',data,rom,cluster('IfxDsadc_DIN0B_P33_6_IN')
	.sect	'.rodata.IfxDsadc_PinMap.IfxDsadc_DIN0B_P33_6_IN'
	.global	IfxDsadc_DIN0B_P33_6_IN
	.align	4
IfxDsadc_DIN0B_P33_6_IN:	.type	object
	.size	IfxDsadc_DIN0B_P33_6_IN,20
	.word	-268288000
	.space	4
	.word	-268184832
	.byte	6
	.space	3
	.byte	1
	.space	3
	.sdecl	'.rodata.IfxDsadc_PinMap.IfxDsadc_DIN2A_P00_6_IN',data,rom,cluster('IfxDsadc_DIN2A_P00_6_IN')
	.sect	'.rodata.IfxDsadc_PinMap.IfxDsadc_DIN2A_P00_6_IN'
	.global	IfxDsadc_DIN2A_P00_6_IN
	.align	4
IfxDsadc_DIN2A_P00_6_IN:	.type	object
	.size	IfxDsadc_DIN2A_P00_6_IN,20
	.word	-268288000
	.byte	2
	.space	3
	.word	-268197888
	.byte	6
	.space	7
	.sdecl	'.rodata.IfxDsadc_PinMap.IfxDsadc_DIN2B_P33_2_IN',data,rom,cluster('IfxDsadc_DIN2B_P33_2_IN')
	.sect	'.rodata.IfxDsadc_PinMap.IfxDsadc_DIN2B_P33_2_IN'
	.global	IfxDsadc_DIN2B_P33_2_IN
	.align	4
IfxDsadc_DIN2B_P33_2_IN:	.type	object
	.size	IfxDsadc_DIN2B_P33_2_IN,20
	.word	-268288000
	.byte	2
	.space	3
	.word	-268184832
	.byte	2
	.space	3
	.byte	1
	.space	3
	.sdecl	'.rodata.IfxDsadc_PinMap.IfxDsadc_DIN3A_P00_4_IN',data,rom,cluster('IfxDsadc_DIN3A_P00_4_IN')
	.sect	'.rodata.IfxDsadc_PinMap.IfxDsadc_DIN3A_P00_4_IN'
	.global	IfxDsadc_DIN3A_P00_4_IN
	.align	4
IfxDsadc_DIN3A_P00_4_IN:	.type	object
	.size	IfxDsadc_DIN3A_P00_4_IN,20
	.word	-268288000
	.byte	3
	.space	3
	.word	-268197888
	.byte	4
	.space	7
	.sdecl	'.rodata.IfxDsadc_PinMap.IfxDsadc_DIN3B_P02_8_IN',data,rom,cluster('IfxDsadc_DIN3B_P02_8_IN')
	.sect	'.rodata.IfxDsadc_PinMap.IfxDsadc_DIN3B_P02_8_IN'
	.global	IfxDsadc_DIN3B_P02_8_IN
	.align	4
IfxDsadc_DIN3B_P02_8_IN:	.type	object
	.size	IfxDsadc_DIN3B_P02_8_IN,20
	.word	-268288000
	.byte	3
	.space	3
	.word	-268197376
	.byte	8
	.space	3
	.byte	1
	.space	3
	.sdecl	'.rodata.IfxDsadc_PinMap.IfxDsadc_DS0NA_AN3_IN',data,rom,cluster('IfxDsadc_DS0NA_AN3_IN')
	.sect	'.rodata.IfxDsadc_PinMap.IfxDsadc_DS0NA_AN3_IN'
	.global	IfxDsadc_DS0NA_AN3_IN
	.align	4
IfxDsadc_DS0NA_AN3_IN:	.type	object
	.size	IfxDsadc_DS0NA_AN3_IN,20
	.word	-268288000
	.space	8
	.byte	3
	.space	7
	.sdecl	'.rodata.IfxDsadc_PinMap.IfxDsadc_DS0NB_AN1_IN',data,rom,cluster('IfxDsadc_DS0NB_AN1_IN')
	.sect	'.rodata.IfxDsadc_PinMap.IfxDsadc_DS0NB_AN1_IN'
	.global	IfxDsadc_DS0NB_AN1_IN
	.align	4
IfxDsadc_DS0NB_AN1_IN:	.type	object
	.size	IfxDsadc_DS0NB_AN1_IN,20
	.word	-268288000
	.space	8
	.byte	1
	.space	3
	.byte	1
	.space	3
	.sdecl	'.rodata.IfxDsadc_PinMap.IfxDsadc_DS2NA_AN21_IN',data,rom,cluster('IfxDsadc_DS2NA_AN21_IN')
	.sect	'.rodata.IfxDsadc_PinMap.IfxDsadc_DS2NA_AN21_IN'
	.global	IfxDsadc_DS2NA_AN21_IN
	.align	4
IfxDsadc_DS2NA_AN21_IN:	.type	object
	.size	IfxDsadc_DS2NA_AN21_IN,20
	.word	-268288000
	.byte	2
	.space	7
	.byte	21
	.space	7
	.sdecl	'.rodata.IfxDsadc_PinMap.IfxDsadc_DS3NA_AN37_IN',data,rom,cluster('IfxDsadc_DS3NA_AN37_IN')
	.sect	'.rodata.IfxDsadc_PinMap.IfxDsadc_DS3NA_AN37_IN'
	.global	IfxDsadc_DS3NA_AN37_IN
	.align	4
IfxDsadc_DS3NA_AN37_IN:	.type	object
	.size	IfxDsadc_DS3NA_AN37_IN,20
	.word	-268288000
	.byte	3
	.space	7
	.byte	37
	.space	7
	.sdecl	'.rodata.IfxDsadc_PinMap.IfxDsadc_DS3NA_P40_7_IN',data,rom,cluster('IfxDsadc_DS3NA_P40_7_IN')
	.sect	'.rodata.IfxDsadc_PinMap.IfxDsadc_DS3NA_P40_7_IN'
	.global	IfxDsadc_DS3NA_P40_7_IN
	.align	4
IfxDsadc_DS3NA_P40_7_IN:	.type	object
	.size	IfxDsadc_DS3NA_P40_7_IN,20
	.word	-268288000
	.byte	3
	.space	3
	.word	-268181504
	.byte	7
	.space	7
	.sdecl	'.rodata.IfxDsadc_PinMap.IfxDsadc_DS3NB_AN39_IN',data,rom,cluster('IfxDsadc_DS3NB_AN39_IN')
	.sect	'.rodata.IfxDsadc_PinMap.IfxDsadc_DS3NB_AN39_IN'
	.global	IfxDsadc_DS3NB_AN39_IN
	.align	4
IfxDsadc_DS3NB_AN39_IN:	.type	object
	.size	IfxDsadc_DS3NB_AN39_IN,20
	.word	-268288000
	.byte	3
	.space	7
	.byte	39
	.space	3
	.byte	1
	.space	3
	.sdecl	'.rodata.IfxDsadc_PinMap.IfxDsadc_DS3NB_P40_9_IN',data,rom,cluster('IfxDsadc_DS3NB_P40_9_IN')
	.sect	'.rodata.IfxDsadc_PinMap.IfxDsadc_DS3NB_P40_9_IN'
	.global	IfxDsadc_DS3NB_P40_9_IN
	.align	4
IfxDsadc_DS3NB_P40_9_IN:	.type	object
	.size	IfxDsadc_DS3NB_P40_9_IN,20
	.word	-268288000
	.byte	3
	.space	3
	.word	-268181504
	.byte	9
	.space	3
	.byte	1
	.space	3
	.sdecl	'.rodata.IfxDsadc_PinMap.IfxDsadc_DS3NC_AN45_IN',data,rom,cluster('IfxDsadc_DS3NC_AN45_IN')
	.sect	'.rodata.IfxDsadc_PinMap.IfxDsadc_DS3NC_AN45_IN'
	.global	IfxDsadc_DS3NC_AN45_IN
	.align	4
IfxDsadc_DS3NC_AN45_IN:	.type	object
	.size	IfxDsadc_DS3NC_AN45_IN,20
	.word	-268288000
	.byte	3
	.space	7
	.byte	45
	.space	3
	.byte	2
	.space	3
	.sdecl	'.rodata.IfxDsadc_PinMap.IfxDsadc_DS3ND_AN47_IN',data,rom,cluster('IfxDsadc_DS3ND_AN47_IN')
	.sect	'.rodata.IfxDsadc_PinMap.IfxDsadc_DS3ND_AN47_IN'
	.global	IfxDsadc_DS3ND_AN47_IN
	.align	4
IfxDsadc_DS3ND_AN47_IN:	.type	object
	.size	IfxDsadc_DS3ND_AN47_IN,20
	.word	-268288000
	.byte	3
	.space	7
	.byte	47
	.space	3
	.byte	3
	.space	3
	.sdecl	'.rodata.IfxDsadc_PinMap.IfxDsadc_DS0PA_AN2_IN',data,rom,cluster('IfxDsadc_DS0PA_AN2_IN')
	.sect	'.rodata.IfxDsadc_PinMap.IfxDsadc_DS0PA_AN2_IN'
	.global	IfxDsadc_DS0PA_AN2_IN
	.align	4
IfxDsadc_DS0PA_AN2_IN:	.type	object
	.size	IfxDsadc_DS0PA_AN2_IN,20
	.word	-268288000
	.space	8
	.byte	2
	.space	7
	.sdecl	'.rodata.IfxDsadc_PinMap.IfxDsadc_DS0PB_AN0_IN',data,rom,cluster('IfxDsadc_DS0PB_AN0_IN')
	.sect	'.rodata.IfxDsadc_PinMap.IfxDsadc_DS0PB_AN0_IN'
	.global	IfxDsadc_DS0PB_AN0_IN
	.align	4
IfxDsadc_DS0PB_AN0_IN:	.type	object
	.size	IfxDsadc_DS0PB_AN0_IN,20
	.word	-268288000
	.space	12
	.byte	1
	.space	3
	.sdecl	'.rodata.IfxDsadc_PinMap.IfxDsadc_DS2PA_AN20_IN',data,rom,cluster('IfxDsadc_DS2PA_AN20_IN')
	.sect	'.rodata.IfxDsadc_PinMap.IfxDsadc_DS2PA_AN20_IN'
	.global	IfxDsadc_DS2PA_AN20_IN
	.align	4
IfxDsadc_DS2PA_AN20_IN:	.type	object
	.size	IfxDsadc_DS2PA_AN20_IN,20
	.word	-268288000
	.byte	2
	.space	7
	.byte	20
	.space	7
	.sdecl	'.rodata.IfxDsadc_PinMap.IfxDsadc_DS3PA_AN36_IN',data,rom,cluster('IfxDsadc_DS3PA_AN36_IN')
	.sect	'.rodata.IfxDsadc_PinMap.IfxDsadc_DS3PA_AN36_IN'
	.global	IfxDsadc_DS3PA_AN36_IN
	.align	4
IfxDsadc_DS3PA_AN36_IN:	.type	object
	.size	IfxDsadc_DS3PA_AN36_IN,20
	.word	-268288000
	.byte	3
	.space	7
	.byte	36
	.space	7
	.sdecl	'.rodata.IfxDsadc_PinMap.IfxDsadc_DS3PA_P40_6_IN',data,rom,cluster('IfxDsadc_DS3PA_P40_6_IN')
	.sect	'.rodata.IfxDsadc_PinMap.IfxDsadc_DS3PA_P40_6_IN'
	.global	IfxDsadc_DS3PA_P40_6_IN
	.align	4
IfxDsadc_DS3PA_P40_6_IN:	.type	object
	.size	IfxDsadc_DS3PA_P40_6_IN,20
	.word	-268288000
	.byte	3
	.space	3
	.word	-268181504
	.byte	6
	.space	7
	.sdecl	'.rodata.IfxDsadc_PinMap.IfxDsadc_DS3PB_AN38_IN',data,rom,cluster('IfxDsadc_DS3PB_AN38_IN')
	.sect	'.rodata.IfxDsadc_PinMap.IfxDsadc_DS3PB_AN38_IN'
	.global	IfxDsadc_DS3PB_AN38_IN
	.align	4
IfxDsadc_DS3PB_AN38_IN:	.type	object
	.size	IfxDsadc_DS3PB_AN38_IN,20
	.word	-268288000
	.byte	3
	.space	7
	.byte	38
	.space	3
	.byte	1
	.space	3
	.sdecl	'.rodata.IfxDsadc_PinMap.IfxDsadc_DS3PB_P40_8_IN',data,rom,cluster('IfxDsadc_DS3PB_P40_8_IN')
	.sect	'.rodata.IfxDsadc_PinMap.IfxDsadc_DS3PB_P40_8_IN'
	.global	IfxDsadc_DS3PB_P40_8_IN
	.align	4
IfxDsadc_DS3PB_P40_8_IN:	.type	object
	.size	IfxDsadc_DS3PB_P40_8_IN,20
	.word	-268288000
	.byte	3
	.space	3
	.word	-268181504
	.byte	8
	.space	3
	.byte	1
	.space	3
	.sdecl	'.rodata.IfxDsadc_PinMap.IfxDsadc_DS3PC_AN44_IN',data,rom,cluster('IfxDsadc_DS3PC_AN44_IN')
	.sect	'.rodata.IfxDsadc_PinMap.IfxDsadc_DS3PC_AN44_IN'
	.global	IfxDsadc_DS3PC_AN44_IN
	.align	4
IfxDsadc_DS3PC_AN44_IN:	.type	object
	.size	IfxDsadc_DS3PC_AN44_IN,20
	.word	-268288000
	.byte	3
	.space	7
	.byte	44
	.space	3
	.byte	2
	.space	3
	.sdecl	'.rodata.IfxDsadc_PinMap.IfxDsadc_DS3PD_AN46_IN',data,rom,cluster('IfxDsadc_DS3PD_AN46_IN')
	.sect	'.rodata.IfxDsadc_PinMap.IfxDsadc_DS3PD_AN46_IN'
	.global	IfxDsadc_DS3PD_AN46_IN
	.align	4
IfxDsadc_DS3PD_AN46_IN:	.type	object
	.size	IfxDsadc_DS3PD_AN46_IN,20
	.word	-268288000
	.byte	3
	.space	7
	.byte	46
	.space	3
	.byte	3
	.space	3
	.sdecl	'.rodata.IfxDsadc_PinMap.IfxDsadc_ITR0E_P33_0_IN',data,rom,cluster('IfxDsadc_ITR0E_P33_0_IN')
	.sect	'.rodata.IfxDsadc_PinMap.IfxDsadc_ITR0E_P33_0_IN'
	.global	IfxDsadc_ITR0E_P33_0_IN
	.align	4
IfxDsadc_ITR0E_P33_0_IN:	.type	object
	.size	IfxDsadc_ITR0E_P33_0_IN,20
	.word	-268288000
	.space	4
	.word	-268184832
	.space	4
	.byte	4
	.space	3
	.sdecl	'.rodata.IfxDsadc_PinMap.IfxDsadc_ITR0F_P33_4_IN',data,rom,cluster('IfxDsadc_ITR0F_P33_4_IN')
	.sect	'.rodata.IfxDsadc_PinMap.IfxDsadc_ITR0F_P33_4_IN'
	.global	IfxDsadc_ITR0F_P33_4_IN
	.align	4
IfxDsadc_ITR0F_P33_4_IN:	.type	object
	.size	IfxDsadc_ITR0F_P33_4_IN,20
	.word	-268288000
	.space	4
	.word	-268184832
	.byte	4
	.space	3
	.byte	5
	.space	3
	.sdecl	'.rodata.IfxDsadc_PinMap.IfxDsadc_ITR2E_P33_2_IN',data,rom,cluster('IfxDsadc_ITR2E_P33_2_IN')
	.sect	'.rodata.IfxDsadc_PinMap.IfxDsadc_ITR2E_P33_2_IN'
	.global	IfxDsadc_ITR2E_P33_2_IN
	.align	4
IfxDsadc_ITR2E_P33_2_IN:	.type	object
	.size	IfxDsadc_ITR2E_P33_2_IN,20
	.word	-268288000
	.byte	2
	.space	3
	.word	-268184832
	.byte	2
	.space	3
	.byte	4
	.space	3
	.sdecl	'.rodata.IfxDsadc_PinMap.IfxDsadc_ITR2F_P33_6_IN',data,rom,cluster('IfxDsadc_ITR2F_P33_6_IN')
	.sect	'.rodata.IfxDsadc_PinMap.IfxDsadc_ITR2F_P33_6_IN'
	.global	IfxDsadc_ITR2F_P33_6_IN
	.align	4
IfxDsadc_ITR2F_P33_6_IN:	.type	object
	.size	IfxDsadc_ITR2F_P33_6_IN,20
	.word	-268288000
	.byte	2
	.space	3
	.word	-268184832
	.byte	6
	.space	3
	.byte	5
	.space	3
	.sdecl	'.rodata.IfxDsadc_PinMap.IfxDsadc_ITR3E_P02_8_IN',data,rom,cluster('IfxDsadc_ITR3E_P02_8_IN')
	.sect	'.rodata.IfxDsadc_PinMap.IfxDsadc_ITR3E_P02_8_IN'
	.global	IfxDsadc_ITR3E_P02_8_IN
	.align	4
IfxDsadc_ITR3E_P02_8_IN:	.type	object
	.size	IfxDsadc_ITR3E_P02_8_IN,20
	.word	-268288000
	.byte	3
	.space	3
	.word	-268197376
	.byte	8
	.space	3
	.byte	4
	.space	3
	.sdecl	'.rodata.IfxDsadc_PinMap.IfxDsadc_ITR3F_P00_9_IN',data,rom,cluster('IfxDsadc_ITR3F_P00_9_IN')
	.sect	'.rodata.IfxDsadc_PinMap.IfxDsadc_ITR3F_P00_9_IN'
	.global	IfxDsadc_ITR3F_P00_9_IN
	.align	4
IfxDsadc_ITR3F_P00_9_IN:	.type	object
	.size	IfxDsadc_ITR3F_P00_9_IN,20
	.word	-268288000
	.byte	3
	.space	3
	.word	-268197888
	.byte	9
	.space	3
	.byte	5
	.space	3
	.sdecl	'.rodata.IfxDsadc_PinMap.IfxDsadc_SGNA_P00_4_IN',data,rom,cluster('IfxDsadc_SGNA_P00_4_IN')
	.sect	'.rodata.IfxDsadc_PinMap.IfxDsadc_SGNA_P00_4_IN'
	.global	IfxDsadc_SGNA_P00_4_IN
	.align	4
IfxDsadc_SGNA_P00_4_IN:	.type	object
	.size	IfxDsadc_SGNA_P00_4_IN,16
	.word	-268288000,-268197888
	.byte	4
	.space	7
	.sdecl	'.rodata.IfxDsadc_PinMap.IfxDsadc_SGNB_P33_13_IN',data,rom,cluster('IfxDsadc_SGNB_P33_13_IN')
	.sect	'.rodata.IfxDsadc_PinMap.IfxDsadc_SGNB_P33_13_IN'
	.global	IfxDsadc_SGNB_P33_13_IN
	.align	4
IfxDsadc_SGNB_P33_13_IN:	.type	object
	.size	IfxDsadc_SGNB_P33_13_IN,16
	.word	-268288000,-268184832
	.byte	13
	.space	3
	.byte	1
	.space	3
	.sdecl	'.data.IfxDsadc_PinMap.IfxDsadc_Cgpwm_Out_pinTable',data,cluster('IfxDsadc_Cgpwm_Out_pinTable')
	.sect	'.data.IfxDsadc_PinMap.IfxDsadc_Cgpwm_Out_pinTable'
	.global	IfxDsadc_Cgpwm_Out_pinTable
	.align	4
IfxDsadc_Cgpwm_Out_pinTable:	.type	object
	.size	IfxDsadc_Cgpwm_Out_pinTable,24
	.word	IfxDsadc_CGPWMN_P00_5_OUT,IfxDsadc_CGPWMP_P00_6_OUT,IfxDsadc_CGPWMN_P02_0_OUT,IfxDsadc_CGPWMP_P02_1_OUT
	.word	IfxDsadc_CGPWMN_P33_11_OUT,IfxDsadc_CGPWMP_P33_12_OUT
	.sdecl	'.data.IfxDsadc_PinMap.IfxDsadc_Cin_In_pinTable',data,cluster('IfxDsadc_Cin_In_pinTable')
	.sect	'.data.IfxDsadc_PinMap.IfxDsadc_Cin_In_pinTable'
	.global	IfxDsadc_Cin_In_pinTable
	.align	4
IfxDsadc_Cin_In_pinTable:	.type	object
	.size	IfxDsadc_Cin_In_pinTable,32
	.word	IfxDsadc_CIN0A_P00_1_IN,IfxDsadc_CIN0B_P33_5_IN
	.space	8
	.word	IfxDsadc_CIN2A_P00_5_IN,IfxDsadc_CIN2B_P33_1_IN,IfxDsadc_CIN3A_P00_3_IN,IfxDsadc_CIN3B_P02_7_IN
	.sdecl	'.data.IfxDsadc_PinMap.IfxDsadc_Cout_Out_pinTable',data,cluster('IfxDsadc_Cout_Out_pinTable')
	.sect	'.data.IfxDsadc_PinMap.IfxDsadc_Cout_Out_pinTable'
	.global	IfxDsadc_Cout_Out_pinTable
	.align	4
IfxDsadc_Cout_Out_pinTable:	.type	object
	.size	IfxDsadc_Cout_Out_pinTable,48
	.word	IfxDsadc_COUT0_P00_1_OUT,IfxDsadc_COUT0_P00_11_OUT,IfxDsadc_COUT0_P33_5_OUT
	.space	12
	.word	IfxDsadc_COUT2_P00_5_OUT,IfxDsadc_COUT2_P33_1_OUT
	.space	4
	.word	IfxDsadc_COUT3_P00_3_OUT,IfxDsadc_COUT3_P02_7_OUT
	.space	4
	.sdecl	'.data.IfxDsadc_PinMap.IfxDsadc_Din_In_pinTable',data,cluster('IfxDsadc_Din_In_pinTable')
	.sect	'.data.IfxDsadc_PinMap.IfxDsadc_Din_In_pinTable'
	.global	IfxDsadc_Din_In_pinTable
	.align	4
IfxDsadc_Din_In_pinTable:	.type	object
	.size	IfxDsadc_Din_In_pinTable,32
	.word	IfxDsadc_DIN0A_P00_2_IN,IfxDsadc_DIN0B_P33_6_IN
	.space	8
	.word	IfxDsadc_DIN2A_P00_6_IN,IfxDsadc_DIN2B_P33_2_IN,IfxDsadc_DIN3A_P00_4_IN,IfxDsadc_DIN3B_P02_8_IN
	.sdecl	'.data.IfxDsadc_PinMap.IfxDsadc_Dsn_In_pinTable',data,cluster('IfxDsadc_Dsn_In_pinTable')
	.sect	'.data.IfxDsadc_PinMap.IfxDsadc_Dsn_In_pinTable'
	.global	IfxDsadc_Dsn_In_pinTable
	.align	4
IfxDsadc_Dsn_In_pinTable:	.type	object
	.size	IfxDsadc_Dsn_In_pinTable,64
	.word	IfxDsadc_DS0NA_AN3_IN,IfxDsadc_DS0NB_AN1_IN
	.space	24
	.word	IfxDsadc_DS2NA_AN21_IN
	.space	12
	.word	IfxDsadc_DS3NA_P40_7_IN,IfxDsadc_DS3NB_P40_9_IN,IfxDsadc_DS3NC_AN45_IN,IfxDsadc_DS3ND_AN47_IN
	.sdecl	'.data.IfxDsadc_PinMap.IfxDsadc_Dsp_In_pinTable',data,cluster('IfxDsadc_Dsp_In_pinTable')
	.sect	'.data.IfxDsadc_PinMap.IfxDsadc_Dsp_In_pinTable'
	.global	IfxDsadc_Dsp_In_pinTable
	.align	4
IfxDsadc_Dsp_In_pinTable:	.type	object
	.size	IfxDsadc_Dsp_In_pinTable,64
	.word	IfxDsadc_DS0PA_AN2_IN,IfxDsadc_DS0PB_AN0_IN
	.space	24
	.word	IfxDsadc_DS2PA_AN20_IN
	.space	12
	.word	IfxDsadc_DS3PA_P40_6_IN,IfxDsadc_DS3PB_P40_8_IN,IfxDsadc_DS3PC_AN44_IN,IfxDsadc_DS3PD_AN46_IN
	.sdecl	'.data.IfxDsadc_PinMap.IfxDsadc_Itr_In_pinTable',data,cluster('IfxDsadc_Itr_In_pinTable')
	.sect	'.data.IfxDsadc_PinMap.IfxDsadc_Itr_In_pinTable'
	.global	IfxDsadc_Itr_In_pinTable
	.align	4
IfxDsadc_Itr_In_pinTable:	.type	object
	.size	IfxDsadc_Itr_In_pinTable,96
	.space	16
	.word	IfxDsadc_ITR0E_P33_0_IN,IfxDsadc_ITR0F_P33_4_IN
	.space	40
	.word	IfxDsadc_ITR2E_P33_2_IN,IfxDsadc_ITR2F_P33_6_IN
	.space	16
	.word	IfxDsadc_ITR3E_P02_8_IN,IfxDsadc_ITR3F_P00_9_IN
	.sdecl	'.data.IfxDsadc_PinMap.IfxDsadc_Sg_In_pinTable',data,cluster('IfxDsadc_Sg_In_pinTable')
	.sect	'.data.IfxDsadc_PinMap.IfxDsadc_Sg_In_pinTable'
	.global	IfxDsadc_Sg_In_pinTable
	.align	4
IfxDsadc_Sg_In_pinTable:	.type	object
	.size	IfxDsadc_Sg_In_pinTable,8
	.word	IfxDsadc_SGNA_P00_4_IN,IfxDsadc_SGNB_P33_13_IN
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L3:
	.word	47005
	.half	3
	.word	.L4
	.byte	4
.L2:
	.byte	1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxDsadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L5
	.byte	2,1,1,3
	.word	239
	.byte	4
	.byte	'Ifx__jump_and_link',0,3,1,61,17,1,1,5
	.byte	'fun',0,1,61,43
	.word	242
	.byte	6,0,7
	.byte	'__fract',0,4,128,1,7
	.byte	'float',0,4,4,8
	.byte	'Ifx__float_to_fract',0,3,1,152,2,18
	.word	287
	.byte	1,1,5
	.byte	'a',0,1,152,2,44
	.word	299
	.byte	6,0,7
	.byte	'unsigned long long int',0,8,7,9
	.byte	'void',0,3
	.word	379
	.byte	8
	.byte	'__ld64',0,3,2,135,1,19
	.word	353
	.byte	1,1,5
	.byte	'addr',0,2,135,1,32
	.word	385
	.byte	6,0,4
	.byte	'__st64',0,3,2,143,1,17,1,1,5
	.byte	'addr',0,2,143,1,30
	.word	385
	.byte	5
	.byte	'value',0,2,143,1,43
	.word	353
	.byte	6,0,7
	.byte	'unsigned int',0,4,7,7
	.byte	'int',0,4,5,7
	.byte	'unsigned char',0,1,8,7
	.byte	'unsigned short int',0,2,7,10
	.byte	'_Ifx_P_OUT_Bits',0,4,143,3,16,4,11
	.byte	'P0',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	494
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	494
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	494
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	494
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	494
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	494
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	494
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	494
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	494
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	494
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	494
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	494
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	511
	.byte	16,0,2,35,2,0,12,4,181,5,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	533
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMR_Bits',0,4,169,2,16,4,11
	.byte	'PS0',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	494
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	494
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	494
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	494
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	494
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	494
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	494
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	494
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	494
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	494
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	494
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	494
	.byte	1,0,2,35,1,11
	.byte	'PCL0',0,1
	.word	494
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	494
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	494
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	494
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	494
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	494
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	494
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	494
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	494
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	494
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	494
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	494
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	494
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	494
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	494
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	494
	.byte	1,0,2,35,3,0,12,4,133,5,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	849
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ID_Bits',0,4,110,16,4,11
	.byte	'MODREV',0,1
	.word	494
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	494
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	511
	.byte	16,0,2,35,2,0,12,4,148,4,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1420
	.byte	4,2,35,0,0,14,4
	.word	494
	.byte	15,3,0,10
	.byte	'_Ifx_P_IOCR0_Bits',0,4,140,1,16,4,11
	.byte	'reserved_0',0,1
	.word	494
	.byte	3,5,2,35,0,11
	.byte	'PC0',0,1
	.word	494
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	494
	.byte	3,5,2,35,1,11
	.byte	'PC1',0,1
	.word	494
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	494
	.byte	3,5,2,35,2,11
	.byte	'PC2',0,1
	.word	494
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	494
	.byte	3,5,2,35,3,11
	.byte	'PC3',0,1
	.word	494
	.byte	5,0,2,35,3,0,12,4,164,4,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1548
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR4_Bits',0,4,166,1,16,4,11
	.byte	'reserved_0',0,1
	.word	494
	.byte	3,5,2,35,0,11
	.byte	'PC4',0,1
	.word	494
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	494
	.byte	3,5,2,35,1,11
	.byte	'PC5',0,1
	.word	494
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	494
	.byte	3,5,2,35,2,11
	.byte	'PC6',0,1
	.word	494
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	494
	.byte	3,5,2,35,3,11
	.byte	'PC7',0,1
	.word	494
	.byte	5,0,2,35,3,0,12,4,180,4,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1763
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR8_Bits',0,4,179,1,16,4,11
	.byte	'reserved_0',0,1
	.word	494
	.byte	3,5,2,35,0,11
	.byte	'PC8',0,1
	.word	494
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	494
	.byte	3,5,2,35,1,11
	.byte	'PC9',0,1
	.word	494
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	494
	.byte	3,5,2,35,2,11
	.byte	'PC10',0,1
	.word	494
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	494
	.byte	3,5,2,35,3,11
	.byte	'PC11',0,1
	.word	494
	.byte	5,0,2,35,3,0,12,4,188,4,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1978
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR12_Bits',0,4,153,1,16,4,11
	.byte	'reserved_0',0,1
	.word	494
	.byte	3,5,2,35,0,11
	.byte	'PC12',0,1
	.word	494
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	494
	.byte	3,5,2,35,1,11
	.byte	'PC13',0,1
	.word	494
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	494
	.byte	3,5,2,35,2,11
	.byte	'PC14',0,1
	.word	494
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	494
	.byte	3,5,2,35,3,11
	.byte	'PC15',0,1
	.word	494
	.byte	5,0,2,35,3,0,12,4,172,4,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2195
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IN_Bits',0,4,118,16,4,11
	.byte	'P0',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	494
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	494
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	494
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	494
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	494
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	494
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	494
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	494
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	494
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	494
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	494
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	494
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	511
	.byte	16,0,2,35,2,0,12,4,156,4,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2415
	.byte	4,2,35,0,0,14,24
	.word	494
	.byte	15,23,0,10
	.byte	'_Ifx_P_PDR0_Bits',0,4,205,3,16,4,11
	.byte	'PD0',0,1
	.word	494
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	494
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	494
	.byte	1,0,2,35,0,11
	.byte	'PD2',0,1
	.word	494
	.byte	3,5,2,35,1,11
	.byte	'PL2',0,1
	.word	494
	.byte	1,4,2,35,1,11
	.byte	'PD3',0,1
	.word	494
	.byte	3,1,2,35,1,11
	.byte	'PL3',0,1
	.word	494
	.byte	1,0,2,35,1,11
	.byte	'PD4',0,1
	.word	494
	.byte	3,5,2,35,2,11
	.byte	'PL4',0,1
	.word	494
	.byte	1,4,2,35,2,11
	.byte	'PD5',0,1
	.word	494
	.byte	3,1,2,35,2,11
	.byte	'PL5',0,1
	.word	494
	.byte	1,0,2,35,2,11
	.byte	'PD6',0,1
	.word	494
	.byte	3,5,2,35,3,11
	.byte	'PL6',0,1
	.word	494
	.byte	1,4,2,35,3,11
	.byte	'PD7',0,1
	.word	494
	.byte	3,1,2,35,3,11
	.byte	'PL7',0,1
	.word	494
	.byte	1,0,2,35,3,0,12,4,205,5,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2738
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PDR1_Bits',0,4,226,3,16,4,11
	.byte	'PD8',0,1
	.word	494
	.byte	3,5,2,35,0,11
	.byte	'PL8',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'PD9',0,1
	.word	494
	.byte	3,1,2,35,0,11
	.byte	'PL9',0,1
	.word	494
	.byte	1,0,2,35,0,11
	.byte	'PD10',0,1
	.word	494
	.byte	3,5,2,35,1,11
	.byte	'PL10',0,1
	.word	494
	.byte	1,4,2,35,1,11
	.byte	'PD11',0,1
	.word	494
	.byte	3,1,2,35,1,11
	.byte	'PL11',0,1
	.word	494
	.byte	1,0,2,35,1,11
	.byte	'PD12',0,1
	.word	494
	.byte	3,5,2,35,2,11
	.byte	'PL12',0,1
	.word	494
	.byte	1,4,2,35,2,11
	.byte	'PD13',0,1
	.word	494
	.byte	3,1,2,35,2,11
	.byte	'PL13',0,1
	.word	494
	.byte	1,0,2,35,2,11
	.byte	'PD14',0,1
	.word	494
	.byte	3,5,2,35,3,11
	.byte	'PL14',0,1
	.word	494
	.byte	1,4,2,35,3,11
	.byte	'PD15',0,1
	.word	494
	.byte	3,1,2,35,3,11
	.byte	'PL15',0,1
	.word	494
	.byte	1,0,2,35,3,0,12,4,213,5,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3042
	.byte	4,2,35,0,0,14,8
	.word	494
	.byte	15,7,0,10
	.byte	'_Ifx_P_ESR_Bits',0,4,88,16,4,11
	.byte	'EN0',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	494
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	494
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	494
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	494
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	494
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	494
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	494
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	494
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	494
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	494
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	494
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	494
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	511
	.byte	16,0,2,35,2,0,12,4,140,4,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3367
	.byte	4,2,35,0,0,14,12
	.word	494
	.byte	15,11,0,10
	.byte	'_Ifx_P_PDISC_Bits',0,4,183,3,16,4,11
	.byte	'PDIS0',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'PDIS2',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'PDIS3',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'PDIS4',0,1
	.word	494
	.byte	1,3,2,35,0,11
	.byte	'PDIS5',0,1
	.word	494
	.byte	1,2,2,35,0,11
	.byte	'PDIS6',0,1
	.word	494
	.byte	1,1,2,35,0,11
	.byte	'PDIS7',0,1
	.word	494
	.byte	1,0,2,35,0,11
	.byte	'PDIS8',0,1
	.word	494
	.byte	1,7,2,35,1,11
	.byte	'PDIS9',0,1
	.word	494
	.byte	1,6,2,35,1,11
	.byte	'PDIS10',0,1
	.word	494
	.byte	1,5,2,35,1,11
	.byte	'PDIS11',0,1
	.word	494
	.byte	1,4,2,35,1,11
	.byte	'PDIS12',0,1
	.word	494
	.byte	1,3,2,35,1,11
	.byte	'PDIS13',0,1
	.word	494
	.byte	1,2,2,35,1,11
	.byte	'PDIS14',0,1
	.word	494
	.byte	1,1,2,35,1,11
	.byte	'PDIS15',0,1
	.word	494
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	511
	.byte	16,0,2,35,2,0,12,4,197,5,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3707
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PCSR_Bits',0,4,165,3,16,4,11
	.byte	'SEL0',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'SEL1',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'SEL2',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'SEL3',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'SEL4',0,1
	.word	494
	.byte	1,3,2,35,0,11
	.byte	'SEL5',0,1
	.word	494
	.byte	1,2,2,35,0,11
	.byte	'SEL6',0,1
	.word	494
	.byte	1,1,2,35,0,11
	.byte	'SEL7',0,1
	.word	494
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	494
	.byte	2,6,2,35,1,11
	.byte	'SEL10',0,1
	.word	494
	.byte	1,5,2,35,1,11
	.byte	'SEL11',0,1
	.word	494
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	471
	.byte	19,1,2,35,0,11
	.byte	'LCK',0,1
	.word	494
	.byte	1,0,2,35,3,0,12,4,189,5,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4073
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR0_Bits',0,4,206,2,16,4,11
	.byte	'PS0',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	471
	.byte	28,0,2,35,0,0,12,4,149,5,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4359
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR4_Bits',0,4,227,2,16,4,11
	.byte	'reserved_0',0,1
	.word	494
	.byte	4,4,2,35,0,11
	.byte	'PS4',0,1
	.word	494
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	494
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	494
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	494
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	471
	.byte	24,0,2,35,0,0,12,4,165,5,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4506
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR8_Bits',0,4,238,2,16,4,11
	.byte	'reserved_0',0,1
	.word	494
	.byte	8,0,2,35,0,11
	.byte	'PS8',0,1
	.word	494
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	494
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	494
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	494
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	471
	.byte	20,0,2,35,0,0,12,4,173,5,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4675
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR12_Bits',0,4,216,2,16,4,11
	.byte	'reserved_0',0,2
	.word	511
	.byte	12,4,2,35,0,11
	.byte	'PS12',0,1
	.word	494
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	494
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	494
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	494
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	511
	.byte	16,0,2,35,2,0,12,4,157,5,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4847
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR0_Bits',0,4,232,1,16,4,11
	.byte	'reserved_0',0,2
	.word	511
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	494
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	494
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	494
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	494
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	511
	.byte	12,0,2,35,2,0,12,4,229,4,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5022
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR4_Bits',0,4,253,1,16,4,11
	.byte	'reserved_0',0,4
	.word	471
	.byte	20,12,2,35,0,11
	.byte	'PCL4',0,1
	.word	494
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	494
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	494
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	494
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	494
	.byte	8,0,2,35,3,0,12,4,245,4,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5196
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR8_Bits',0,4,136,2,16,4,11
	.byte	'reserved_0',0,4
	.word	471
	.byte	24,8,2,35,0,11
	.byte	'PCL8',0,1
	.word	494
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	494
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	494
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	494
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	494
	.byte	4,0,2,35,3,0,12,4,253,4,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5370
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR12_Bits',0,4,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	471
	.byte	28,4,2,35,0,11
	.byte	'PCL12',0,1
	.word	494
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	494
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	494
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	494
	.byte	1,0,2,35,3,0,12,4,237,4,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5546
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR_Bits',0,4,249,2,16,4,11
	.byte	'PS0',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	494
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	494
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	494
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	494
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	494
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	494
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	494
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	494
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	494
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	494
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	494
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	494
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	511
	.byte	16,0,2,35,2,0,12,4,141,5,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5702
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR_Bits',0,4,147,2,16,4,11
	.byte	'reserved_0',0,2
	.word	511
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	494
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	494
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	494
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	494
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	494
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	494
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	494
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	494
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	494
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	494
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	494
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	494
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	494
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	494
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	494
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	494
	.byte	1,0,2,35,3,0,12,4,221,4,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6035
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR0_Bits',0,4,192,1,16,4,11
	.byte	'reserved_0',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	471
	.byte	30,0,2,35,0,0,12,4,196,4,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6383
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_Bits',0,4,200,1,16,4,11
	.byte	'reserved_0',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	471
	.byte	30,0,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_P21_Bits',0,4,208,1,16,4,11
	.byte	'RDIS_CTRL',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'RX_DIS',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'TERM',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'LRXTERM',0,1
	.word	494
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	471
	.byte	24,0,2,35,0,0,12,4,204,4,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6507
	.byte	4,2,35,0,13
	.byte	'B_P21',0
	.word	6591
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR2_Bits',0,4,218,1,16,4,11
	.byte	'reserved_0',0,1
	.word	494
	.byte	8,0,2,35,0,11
	.byte	'LVDSR',0,1
	.word	494
	.byte	1,7,2,35,1,11
	.byte	'LVDSRL',0,1
	.word	494
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	494
	.byte	2,4,2,35,1,11
	.byte	'TDIS_CTRL',0,1
	.word	494
	.byte	1,3,2,35,1,11
	.byte	'TX_DIS',0,1
	.word	494
	.byte	1,2,2,35,1,11
	.byte	'TX_PD',0,1
	.word	494
	.byte	1,1,2,35,1,11
	.byte	'TX_PWDPD',0,1
	.word	494
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	511
	.byte	16,0,2,35,2,0,12,4,213,4,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6771
	.byte	4,2,35,0,0,14,76
	.word	494
	.byte	15,75,0,10
	.byte	'_Ifx_P_ACCEN1_Bits',0,4,82,16,4,11
	.byte	'reserved_0',0,4
	.word	471
	.byte	32,0,2,35,0,0,12,4,132,4,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7024
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ACCEN0_Bits',0,4,45,16,4,11
	.byte	'EN0',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	494
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	494
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	494
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	494
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	494
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	494
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	494
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	494
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	494
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	494
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	494
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	494
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	494
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	494
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	494
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	494
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	494
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	494
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	494
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	494
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	494
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	494
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	494
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	494
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	494
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	494
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	494
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	494
	.byte	1,0,2,35,3,0,12,4,252,3,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7111
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P',0,4,229,5,25,128,2,13
	.byte	'OUT',0
	.word	809
	.byte	4,2,35,0,13
	.byte	'OMR',0
	.word	1380
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	1499
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	1539
	.byte	4,2,35,12,13
	.byte	'IOCR0',0
	.word	1723
	.byte	4,2,35,16,13
	.byte	'IOCR4',0
	.word	1938
	.byte	4,2,35,20,13
	.byte	'IOCR8',0
	.word	2155
	.byte	4,2,35,24,13
	.byte	'IOCR12',0
	.word	2375
	.byte	4,2,35,28,13
	.byte	'reserved_20',0
	.word	1539
	.byte	4,2,35,32,13
	.byte	'IN',0
	.word	2689
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	2729
	.byte	24,2,35,40,13
	.byte	'PDR0',0
	.word	3002
	.byte	4,2,35,64,13
	.byte	'PDR1',0
	.word	3318
	.byte	4,2,35,68,13
	.byte	'reserved_48',0
	.word	3358
	.byte	8,2,35,72,13
	.byte	'ESR',0
	.word	3658
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	3698
	.byte	12,2,35,84,13
	.byte	'PDISC',0
	.word	4033
	.byte	4,2,35,96,13
	.byte	'PCSR',0
	.word	4319
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	3358
	.byte	8,2,35,104,13
	.byte	'OMSR0',0
	.word	4466
	.byte	4,2,35,112,13
	.byte	'OMSR4',0
	.word	4635
	.byte	4,2,35,116,13
	.byte	'OMSR8',0
	.word	4807
	.byte	4,2,35,120,13
	.byte	'OMSR12',0
	.word	4982
	.byte	4,2,35,124,13
	.byte	'OMCR0',0
	.word	5156
	.byte	4,3,35,128,1,13
	.byte	'OMCR4',0
	.word	5330
	.byte	4,3,35,132,1,13
	.byte	'OMCR8',0
	.word	5506
	.byte	4,3,35,136,1,13
	.byte	'OMCR12',0
	.word	5662
	.byte	4,3,35,140,1,13
	.byte	'OMSR',0
	.word	5995
	.byte	4,3,35,144,1,13
	.byte	'OMCR',0
	.word	6343
	.byte	4,3,35,148,1,13
	.byte	'reserved_98',0
	.word	3358
	.byte	8,3,35,152,1,13
	.byte	'LPCR0',0
	.word	6467
	.byte	4,3,35,160,1,13
	.byte	'LPCR1',0
	.word	6716
	.byte	4,3,35,164,1,13
	.byte	'LPCR2',0
	.word	6975
	.byte	4,3,35,168,1,13
	.byte	'reserved_A4',0
	.word	7015
	.byte	76,3,35,172,1,13
	.byte	'ACCEN1',0
	.word	7071
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	7638
	.byte	4,3,35,252,1,0,16
	.word	7678
	.byte	3
	.word	8281
	.byte	17,3,172,1,9,4,18
	.byte	'IfxPort_State_notChanged',0,0,18
	.byte	'IfxPort_State_high',0,1,18
	.byte	'IfxPort_State_low',0,128,128,4,18
	.byte	'IfxPort_State_toggled',0,129,128,4,0,4
	.byte	'IfxPort_setPinState',0,3,3,208,4,17,1,1,5
	.byte	'port',0,3,208,4,44
	.word	8286
	.byte	5
	.byte	'pinIndex',0,3,208,4,56
	.word	494
	.byte	5
	.byte	'action',0,3,208,4,80
	.word	8291
	.byte	6,0,19
	.word	247
	.byte	20
	.word	273
	.byte	6,0,19
	.word	308
	.byte	20
	.word	340
	.byte	6,0,19
	.word	390
	.byte	20
	.word	409
	.byte	6,0,19
	.word	425
	.byte	20
	.word	440
	.byte	20
	.word	454
	.byte	6,0,19
	.word	8394
	.byte	20
	.word	8422
	.byte	20
	.word	8436
	.byte	20
	.word	8454
	.byte	6,0,7
	.byte	'short int',0,2,5,21
	.byte	'__wchar_t',0,5,1,1
	.word	8547
	.byte	21
	.byte	'__size_t',0,5,1,1
	.word	471
	.byte	21
	.byte	'__ptrdiff_t',0,5,1,1
	.word	487
	.byte	22,1,3
	.word	8615
	.byte	21
	.byte	'__codeptr',0,5,1,1
	.word	8617
	.byte	10
	.byte	'_Ifx_DSADC_ACCEN0_Bits',0,6,45,16,4,11
	.byte	'EN0',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	494
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	494
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	494
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	494
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	494
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	494
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	494
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	494
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	494
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	494
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	494
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	494
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	494
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	494
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	494
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	494
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	494
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	494
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	494
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	494
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	494
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	494
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	494
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	494
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	494
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	494
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	494
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	494
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_DSADC_ACCEN0_Bits',0,6,79,3
	.word	8640
	.byte	10
	.byte	'_Ifx_DSADC_ACCPROT_Bits',0,6,82,16,4,11
	.byte	'RG00',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'RG01',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'RG02',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'RG03',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'RG04',0,1
	.word	494
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,2
	.word	511
	.byte	9,2,2,35,0,11
	.byte	'RG10',0,1
	.word	494
	.byte	1,1,2,35,1,11
	.byte	'RG11',0,1
	.word	494
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	511
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_DSADC_ACCPROT_Bits',0,6,93,3
	.word	9201
	.byte	10
	.byte	'_Ifx_DSADC_CGCFG_Bits',0,6,96,16,4,11
	.byte	'CGMOD',0,1
	.word	494
	.byte	2,6,2,35,0,11
	.byte	'BREV',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'SIGPOL',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'DIVCG',0,1
	.word	494
	.byte	4,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	494
	.byte	7,1,2,35,1,11
	.byte	'RUN',0,1
	.word	494
	.byte	1,0,2,35,1,11
	.byte	'BITCOUNT',0,1
	.word	494
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	494
	.byte	3,0,2,35,2,11
	.byte	'STEPCOUNT',0,1
	.word	494
	.byte	4,4,2,35,3,11
	.byte	'STEPS',0,1
	.word	494
	.byte	1,3,2,35,3,11
	.byte	'STEPD',0,1
	.word	494
	.byte	1,2,2,35,3,11
	.byte	'SGNCG',0,1
	.word	494
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	494
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_DSADC_CGCFG_Bits',0,6,111,3
	.word	9419
	.byte	10
	.byte	'_Ifx_DSADC_CH_BOUNDSEL_Bits',0,6,114,16,4,11
	.byte	'BOUNDARYL',0,2
	.word	511
	.byte	16,0,2,35,0,11
	.byte	'BOUNDARYU',0,2
	.word	511
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_DSADC_CH_BOUNDSEL_Bits',0,6,118,3
	.word	9719
	.byte	10
	.byte	'_Ifx_DSADC_CH_CGSYNC_Bits',0,6,121,16,4,11
	.byte	'SDCOUNT',0,1
	.word	494
	.byte	8,0,2,35,0,11
	.byte	'SDCAP',0,1
	.word	494
	.byte	8,0,2,35,1,11
	.byte	'SDPOS',0,1
	.word	494
	.byte	8,0,2,35,2,11
	.byte	'SDNEG',0,1
	.word	494
	.byte	8,0,2,35,3,0,21
	.byte	'Ifx_DSADC_CH_CGSYNC_Bits',0,6,127,3
	.word	9830
	.byte	10
	.byte	'_Ifx_DSADC_CH_DICFG_Bits',0,6,130,1,16,4,11
	.byte	'DSRC',0,1
	.word	494
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	494
	.byte	3,1,2,35,0,11
	.byte	'DSWC',0,1
	.word	494
	.byte	1,0,2,35,0,11
	.byte	'ITRMODE',0,1
	.word	494
	.byte	2,6,2,35,1,11
	.byte	'TSTRMODE',0,1
	.word	494
	.byte	2,4,2,35,1,11
	.byte	'TRSEL',0,1
	.word	494
	.byte	3,1,2,35,1,11
	.byte	'TRWC',0,1
	.word	494
	.byte	1,0,2,35,1,11
	.byte	'CSRC',0,1
	.word	494
	.byte	4,4,2,35,2,11
	.byte	'STROBE',0,1
	.word	494
	.byte	4,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	494
	.byte	7,1,2,35,3,11
	.byte	'SCWC',0,1
	.word	494
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_DSADC_CH_DICFG_Bits',0,6,143,1,3
	.word	9965
	.byte	10
	.byte	'_Ifx_DSADC_CH_FCFGA_Bits',0,6,146,1,16,4,11
	.byte	'CFADF',0,1
	.word	494
	.byte	8,0,2,35,0,11
	.byte	'CFAC',0,1
	.word	494
	.byte	2,6,2,35,1,11
	.byte	'SRGA',0,1
	.word	494
	.byte	2,4,2,35,1,11
	.byte	'ESEL',0,1
	.word	494
	.byte	2,2,2,35,1,11
	.byte	'EGT',0,1
	.word	494
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	494
	.byte	1,0,2,35,1,11
	.byte	'AFSC',0,1
	.word	494
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	494
	.byte	6,0,2,35,2,11
	.byte	'CFADCNT',0,1
	.word	494
	.byte	8,0,2,35,3,0,21
	.byte	'Ifx_DSADC_CH_FCFGA_Bits',0,6,157,1,3
	.word	10229
	.byte	10
	.byte	'_Ifx_DSADC_CH_FCFGC_Bits',0,6,160,1,16,4,11
	.byte	'CFMDF',0,1
	.word	494
	.byte	8,0,2,35,0,11
	.byte	'CFMC',0,1
	.word	494
	.byte	2,6,2,35,1,11
	.byte	'CFEN',0,1
	.word	494
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	494
	.byte	1,4,2,35,1,11
	.byte	'MFSC',0,1
	.word	494
	.byte	2,2,2,35,1,11
	.byte	'SRGM',0,1
	.word	494
	.byte	2,0,2,35,1,11
	.byte	'CFMSV',0,1
	.word	494
	.byte	8,0,2,35,2,11
	.byte	'CFMDCNT',0,1
	.word	494
	.byte	8,0,2,35,3,0,21
	.byte	'Ifx_DSADC_CH_FCFGC_Bits',0,6,170,1,3
	.word	10455
	.byte	10
	.byte	'_Ifx_DSADC_CH_FCFGM_Bits',0,6,173,1,16,4,11
	.byte	'FIR0EN',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'FIR1EN',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'OCEN',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'DSH',0,1
	.word	494
	.byte	2,3,2,35,0,11
	.byte	'FSH',0,1
	.word	494
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	471
	.byte	26,0,2,35,0,0,21
	.byte	'Ifx_DSADC_CH_FCFGM_Bits',0,6,181,1,3
	.word	10660
	.byte	10
	.byte	'_Ifx_DSADC_CH_ICCFG_Bits',0,6,184,1,16,4,11
	.byte	'DI0',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'DI1',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	494
	.byte	2,4,2,35,0,11
	.byte	'IREN',0,1
	.word	494
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	494
	.byte	3,0,2,35,0,11
	.byte	'TWINSP',0,1
	.word	494
	.byte	6,2,2,35,1,11
	.byte	'reserved_14',0,4
	.word	471
	.byte	17,1,2,35,0,11
	.byte	'WREN',0,1
	.word	494
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_DSADC_CH_ICCFG_Bits',0,6,194,1,3
	.word	10829
	.byte	10
	.byte	'_Ifx_DSADC_CH_IWCTR_Bits',0,6,197,1,16,4,11
	.byte	'NVALCNT',0,1
	.word	494
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	494
	.byte	1,1,2,35,0,11
	.byte	'INTEN',0,1
	.word	494
	.byte	1,0,2,35,0,11
	.byte	'REPCNT',0,1
	.word	494
	.byte	4,4,2,35,1,11
	.byte	'REPVAL',0,1
	.word	494
	.byte	4,0,2,35,1,11
	.byte	'NVALDIS',0,1
	.word	494
	.byte	6,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	494
	.byte	1,1,2,35,2,11
	.byte	'IWS',0,1
	.word	494
	.byte	1,0,2,35,2,11
	.byte	'NVALINT',0,1
	.word	494
	.byte	6,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	494
	.byte	2,0,2,35,3,0,21
	.byte	'Ifx_DSADC_CH_IWCTR_Bits',0,6,209,1,3
	.word	11041
	.byte	10
	.byte	'_Ifx_DSADC_CH_MODCFG_Bits',0,6,212,1,16,4,11
	.byte	'INCFGP',0,1
	.word	494
	.byte	2,6,2,35,0,11
	.byte	'INCFGN',0,1
	.word	494
	.byte	2,4,2,35,0,11
	.byte	'GAINSEL',0,1
	.word	494
	.byte	4,0,2,35,0,11
	.byte	'INSEL',0,1
	.word	494
	.byte	2,6,2,35,1,11
	.byte	'INMUX',0,1
	.word	494
	.byte	2,4,2,35,1,11
	.byte	'INMODE',0,1
	.word	494
	.byte	2,2,2,35,1,11
	.byte	'INMAC',0,1
	.word	494
	.byte	1,1,2,35,1,11
	.byte	'INCWC',0,1
	.word	494
	.byte	1,0,2,35,1,11
	.byte	'DIVM',0,1
	.word	494
	.byte	4,4,2,35,2,11
	.byte	'reserved_20',0,1
	.word	494
	.byte	3,1,2,35,2,11
	.byte	'DWC',0,1
	.word	494
	.byte	1,0,2,35,2,11
	.byte	'CMVS',0,1
	.word	494
	.byte	2,6,2,35,3,11
	.byte	'MCFG',0,1
	.word	494
	.byte	2,4,2,35,3,11
	.byte	'GCEN',0,1
	.word	494
	.byte	1,3,2,35,3,11
	.byte	'APC',0,1
	.word	494
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	494
	.byte	1,1,2,35,3,11
	.byte	'MWC',0,1
	.word	494
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_DSADC_CH_MODCFG_Bits',0,6,231,1,3
	.word	11299
	.byte	10
	.byte	'_Ifx_DSADC_CH_OFFM_Bits',0,6,234,1,16,4,11
	.byte	'OFFSET',0,2
	.word	511
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	511
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_DSADC_CH_OFFM_Bits',0,6,238,1,3
	.word	11662
	.byte	10
	.byte	'_Ifx_DSADC_CH_RECTCFG_Bits',0,6,241,1,16,4,11
	.byte	'RFEN',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	494
	.byte	3,4,2,35,0,11
	.byte	'SSRC',0,1
	.word	494
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,2
	.word	511
	.byte	9,1,2,35,0,11
	.byte	'SDCVAL',0,1
	.word	494
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	511
	.byte	14,2,2,35,2,11
	.byte	'SGNCS',0,1
	.word	494
	.byte	1,1,2,35,3,11
	.byte	'SGND',0,1
	.word	494
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_DSADC_CH_RECTCFG_Bits',0,6,251,1,3
	.word	11766
	.byte	10
	.byte	'_Ifx_DSADC_CH_RESA_Bits',0,6,254,1,16,4,11
	.byte	'RESULT',0,2
	.word	511
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	511
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_DSADC_CH_RESA_Bits',0,6,130,2,3
	.word	11985
	.byte	10
	.byte	'_Ifx_DSADC_CH_RESM_Bits',0,6,133,2,16,4,11
	.byte	'RESULT',0,2
	.word	511
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	511
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_DSADC_CH_RESM_Bits',0,6,137,2,3
	.word	12089
	.byte	10
	.byte	'_Ifx_DSADC_CH_TSTMP_Bits',0,6,140,2,16,4,11
	.byte	'RESULT',0,2
	.word	511
	.byte	16,0,2,35,0,11
	.byte	'CFMDCNT',0,1
	.word	494
	.byte	8,0,2,35,2,11
	.byte	'NVALCNT',0,1
	.word	494
	.byte	6,2,2,35,3,11
	.byte	'TSVAL',0,1
	.word	494
	.byte	1,1,2,35,3,11
	.byte	'TSSR',0,1
	.word	494
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_DSADC_CH_TSTMP_Bits',0,6,147,2,3
	.word	12193
	.byte	10
	.byte	'_Ifx_DSADC_CLC_Bits',0,6,150,2,16,4,11
	.byte	'DISR',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'DISS',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'EDIS',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	471
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_DSADC_CLC_Bits',0,6,157,2,3
	.word	12347
	.byte	10
	.byte	'_Ifx_DSADC_EVFLAG_Bits',0,6,160,2,16,4,11
	.byte	'RESEV0',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'RESEV2',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'RESEV3',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	511
	.byte	12,0,2,35,0,11
	.byte	'ALEV0',0,1
	.word	494
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	494
	.byte	1,6,2,35,2,11
	.byte	'ALEV2',0,1
	.word	494
	.byte	1,5,2,35,2,11
	.byte	'ALEV3',0,1
	.word	494
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	511
	.byte	12,0,2,35,2,0,21
	.byte	'Ifx_DSADC_EVFLAG_Bits',0,6,172,2,3
	.word	12494
	.byte	10
	.byte	'_Ifx_DSADC_EVFLAGCLR_Bits',0,6,175,2,16,4,11
	.byte	'RESEC0',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'RESEC2',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'RESEC3',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	511
	.byte	12,0,2,35,0,11
	.byte	'ALEC0',0,1
	.word	494
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	494
	.byte	1,6,2,35,2,11
	.byte	'ALEC2',0,1
	.word	494
	.byte	1,5,2,35,2,11
	.byte	'ALEC3',0,1
	.word	494
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	511
	.byte	12,0,2,35,2,0,21
	.byte	'Ifx_DSADC_EVFLAGCLR_Bits',0,6,187,2,3
	.word	12750
	.byte	10
	.byte	'_Ifx_DSADC_GLOBCFG_Bits',0,6,190,2,16,4,11
	.byte	'MCSEL',0,1
	.word	494
	.byte	3,5,2,35,0,11
	.byte	'reserved_3',0,2
	.word	511
	.byte	8,5,2,35,0,11
	.byte	'IRM0',0,1
	.word	494
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	494
	.byte	4,0,2,35,1,11
	.byte	'IBSEL',0,1
	.word	494
	.byte	4,4,2,35,2,11
	.byte	'LOSUP',0,1
	.word	494
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	494
	.byte	1,2,2,35,2,11
	.byte	'ICT',0,1
	.word	494
	.byte	1,1,2,35,2,11
	.byte	'PSWC',0,1
	.word	494
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	494
	.byte	8,0,2,35,3,0,21
	.byte	'Ifx_DSADC_GLOBCFG_Bits',0,6,202,2,3
	.word	13012
	.byte	10
	.byte	'_Ifx_DSADC_GLOBRC_Bits',0,6,205,2,16,4,11
	.byte	'CH0RUN',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'CH2RUN',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'CH3RUN',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	511
	.byte	12,0,2,35,0,11
	.byte	'M0RUN',0,1
	.word	494
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	494
	.byte	1,6,2,35,2,11
	.byte	'M2RUN',0,1
	.word	494
	.byte	1,5,2,35,2,11
	.byte	'M3RUN',0,1
	.word	494
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	511
	.byte	12,0,2,35,2,0,21
	.byte	'Ifx_DSADC_GLOBRC_Bits',0,6,217,2,3
	.word	13264
	.byte	10
	.byte	'_Ifx_DSADC_GLOBVCMH0_Bits',0,6,220,2,16,4,11
	.byte	'IN0PVC0',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'IN0PVC1',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	494
	.byte	2,4,2,35,0,11
	.byte	'IN0NVC0',0,1
	.word	494
	.byte	1,3,2,35,0,11
	.byte	'IN0NVC1',0,1
	.word	494
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,2
	.word	511
	.byte	10,0,2,35,0,11
	.byte	'IN2PVC0',0,1
	.word	494
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	494
	.byte	3,4,2,35,2,11
	.byte	'IN2NVC0',0,1
	.word	494
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	494
	.byte	3,0,2,35,2,11
	.byte	'IN3PVC0',0,1
	.word	494
	.byte	1,7,2,35,3,11
	.byte	'IN3PVC1',0,1
	.word	494
	.byte	1,6,2,35,3,11
	.byte	'IN3PVC2',0,1
	.word	494
	.byte	1,5,2,35,3,11
	.byte	'IN3PVC3',0,1
	.word	494
	.byte	1,4,2,35,3,11
	.byte	'IN3NVC0',0,1
	.word	494
	.byte	1,3,2,35,3,11
	.byte	'IN3NVC1',0,1
	.word	494
	.byte	1,2,2,35,3,11
	.byte	'IN3NVC2',0,1
	.word	494
	.byte	1,1,2,35,3,11
	.byte	'IN3NVC3',0,1
	.word	494
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_DSADC_GLOBVCMH0_Bits',0,6,240,2,3
	.word	13520
	.byte	10
	.byte	'_Ifx_DSADC_GLOBVCMH2_Bits',0,6,243,2,16,4,11
	.byte	'reserved_0',0,4
	.word	471
	.byte	29,3,2,35,0,11
	.byte	'VHON',0,1
	.word	494
	.byte	1,2,2,35,3,11
	.byte	'VCMHS',0,1
	.word	494
	.byte	2,0,2,35,3,0,21
	.byte	'Ifx_DSADC_GLOBVCMH2_Bits',0,6,248,2,3
	.word	13943
	.byte	10
	.byte	'_Ifx_DSADC_ID_Bits',0,6,251,2,16,4,11
	.byte	'MODREV',0,1
	.word	494
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	494
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	511
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_DSADC_ID_Bits',0,6,128,3,3
	.word	14065
	.byte	10
	.byte	'_Ifx_DSADC_IGCFG_Bits',0,6,131,3,16,4,11
	.byte	'DITRIM',0,1
	.word	494
	.byte	3,5,2,35,0,11
	.byte	'reserved_3',0,2
	.word	511
	.byte	13,0,2,35,0,11
	.byte	'GLOBSP',0,2
	.word	511
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	494
	.byte	5,1,2,35,3,11
	.byte	'WREN',0,1
	.word	494
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_DSADC_IGCFG_Bits',0,6,138,3,3
	.word	14176
	.byte	10
	.byte	'_Ifx_DSADC_KRST0_Bits',0,6,141,3,16,4,11
	.byte	'RST',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'RSTSTAT',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	471
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_DSADC_KRST0_Bits',0,6,146,3,3
	.word	14332
	.byte	10
	.byte	'_Ifx_DSADC_KRST1_Bits',0,6,149,3,16,4,11
	.byte	'RST',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	471
	.byte	31,0,2,35,0,0,21
	.byte	'Ifx_DSADC_KRST1_Bits',0,6,153,3,3
	.word	14447
	.byte	10
	.byte	'_Ifx_DSADC_KRSTCLR_Bits',0,6,156,3,16,4,11
	.byte	'CLR',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	471
	.byte	31,0,2,35,0,0,21
	.byte	'Ifx_DSADC_KRSTCLR_Bits',0,6,160,3,3
	.word	14543
	.byte	10
	.byte	'_Ifx_DSADC_OCS_Bits',0,6,163,3,16,4,11
	.byte	'reserved_0',0,4
	.word	471
	.byte	24,8,2,35,0,11
	.byte	'SUS',0,1
	.word	494
	.byte	4,4,2,35,3,11
	.byte	'SUS_P',0,1
	.word	494
	.byte	1,3,2,35,3,11
	.byte	'SUSSTA',0,1
	.word	494
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	494
	.byte	2,0,2,35,3,0,21
	.byte	'Ifx_DSADC_OCS_Bits',0,6,170,3,3
	.word	14643
	.byte	12,6,178,3,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8640
	.byte	4,2,35,0,0,21
	.byte	'Ifx_DSADC_ACCEN0',0,6,183,3,3
	.word	14793
	.byte	12,6,186,3,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9201
	.byte	4,2,35,0,0,21
	.byte	'Ifx_DSADC_ACCPROT',0,6,191,3,3
	.word	14859
	.byte	12,6,194,3,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9419
	.byte	4,2,35,0,0,21
	.byte	'Ifx_DSADC_CGCFG',0,6,199,3,3
	.word	14926
	.byte	12,6,202,3,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9719
	.byte	4,2,35,0,0,21
	.byte	'Ifx_DSADC_CH_BOUNDSEL',0,6,207,3,3
	.word	14991
	.byte	12,6,210,3,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9830
	.byte	4,2,35,0,0,21
	.byte	'Ifx_DSADC_CH_CGSYNC',0,6,215,3,3
	.word	15062
	.byte	12,6,218,3,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9965
	.byte	4,2,35,0,0,21
	.byte	'Ifx_DSADC_CH_DICFG',0,6,223,3,3
	.word	15131
	.byte	12,6,226,3,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10229
	.byte	4,2,35,0,0,21
	.byte	'Ifx_DSADC_CH_FCFGA',0,6,231,3,3
	.word	15199
	.byte	12,6,234,3,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10455
	.byte	4,2,35,0,0,21
	.byte	'Ifx_DSADC_CH_FCFGC',0,6,239,3,3
	.word	15267
	.byte	12,6,242,3,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10660
	.byte	4,2,35,0,0,21
	.byte	'Ifx_DSADC_CH_FCFGM',0,6,247,3,3
	.word	15335
	.byte	12,6,250,3,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10829
	.byte	4,2,35,0,0,21
	.byte	'Ifx_DSADC_CH_ICCFG',0,6,255,3,3
	.word	15403
	.byte	12,6,130,4,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11041
	.byte	4,2,35,0,0,21
	.byte	'Ifx_DSADC_CH_IWCTR',0,6,135,4,3
	.word	15471
	.byte	12,6,138,4,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11299
	.byte	4,2,35,0,0,21
	.byte	'Ifx_DSADC_CH_MODCFG',0,6,143,4,3
	.word	15539
	.byte	12,6,146,4,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11662
	.byte	4,2,35,0,0,21
	.byte	'Ifx_DSADC_CH_OFFM',0,6,151,4,3
	.word	15608
	.byte	12,6,154,4,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11766
	.byte	4,2,35,0,0,21
	.byte	'Ifx_DSADC_CH_RECTCFG',0,6,159,4,3
	.word	15675
	.byte	12,6,162,4,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11985
	.byte	4,2,35,0,0,21
	.byte	'Ifx_DSADC_CH_RESA',0,6,167,4,3
	.word	15745
	.byte	12,6,170,4,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12089
	.byte	4,2,35,0,0,21
	.byte	'Ifx_DSADC_CH_RESM',0,6,175,4,3
	.word	15812
	.byte	12,6,178,4,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12193
	.byte	4,2,35,0,0,21
	.byte	'Ifx_DSADC_CH_TSTMP',0,6,183,4,3
	.word	15879
	.byte	12,6,186,4,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12347
	.byte	4,2,35,0,0,21
	.byte	'Ifx_DSADC_CLC',0,6,191,4,3
	.word	15947
	.byte	12,6,194,4,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12494
	.byte	4,2,35,0,0,21
	.byte	'Ifx_DSADC_EVFLAG',0,6,199,4,3
	.word	16010
	.byte	12,6,202,4,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12750
	.byte	4,2,35,0,0,21
	.byte	'Ifx_DSADC_EVFLAGCLR',0,6,207,4,3
	.word	16076
	.byte	12,6,210,4,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13012
	.byte	4,2,35,0,0,21
	.byte	'Ifx_DSADC_GLOBCFG',0,6,215,4,3
	.word	16145
	.byte	12,6,218,4,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13264
	.byte	4,2,35,0,0,21
	.byte	'Ifx_DSADC_GLOBRC',0,6,223,4,3
	.word	16212
	.byte	12,6,226,4,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13520
	.byte	4,2,35,0,0,21
	.byte	'Ifx_DSADC_GLOBVCMH0',0,6,231,4,3
	.word	16278
	.byte	12,6,234,4,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13943
	.byte	4,2,35,0,0,21
	.byte	'Ifx_DSADC_GLOBVCMH2',0,6,239,4,3
	.word	16347
	.byte	12,6,242,4,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14065
	.byte	4,2,35,0,0,21
	.byte	'Ifx_DSADC_ID',0,6,247,4,3
	.word	16416
	.byte	12,6,250,4,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14176
	.byte	4,2,35,0,0,21
	.byte	'Ifx_DSADC_IGCFG',0,6,255,4,3
	.word	16478
	.byte	12,6,130,5,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14332
	.byte	4,2,35,0,0,21
	.byte	'Ifx_DSADC_KRST0',0,6,135,5,3
	.word	16543
	.byte	12,6,138,5,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14447
	.byte	4,2,35,0,0,21
	.byte	'Ifx_DSADC_KRST1',0,6,143,5,3
	.word	16608
	.byte	12,6,146,5,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14543
	.byte	4,2,35,0,0,21
	.byte	'Ifx_DSADC_KRSTCLR',0,6,151,5,3
	.word	16673
	.byte	12,6,154,5,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14643
	.byte	4,2,35,0,0,21
	.byte	'Ifx_DSADC_OCS',0,6,159,5,3
	.word	16740
	.byte	14,36
	.word	494
	.byte	15,35,0,14,44
	.word	494
	.byte	15,43,0,10
	.byte	'_Ifx_DSADC_CH',0,6,170,5,25,128,2,13
	.byte	'MODCFG',0
	.word	15539
	.byte	4,2,35,0,13
	.byte	'reserved_4',0
	.word	1539
	.byte	4,2,35,4,13
	.byte	'DICFG',0
	.word	15131
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	1539
	.byte	4,2,35,12,13
	.byte	'FCFGM',0
	.word	15335
	.byte	4,2,35,16,13
	.byte	'FCFGC',0
	.word	15267
	.byte	4,2,35,20,13
	.byte	'FCFGA',0
	.word	15199
	.byte	4,2,35,24,13
	.byte	'reserved_1C',0
	.word	1539
	.byte	4,2,35,28,13
	.byte	'IWCTR',0
	.word	15471
	.byte	4,2,35,32,13
	.byte	'reserved_24',0
	.word	1539
	.byte	4,2,35,36,13
	.byte	'BOUNDSEL',0
	.word	14991
	.byte	4,2,35,40,13
	.byte	'reserved_2C',0
	.word	1539
	.byte	4,2,35,44,13
	.byte	'RESM',0
	.word	15812
	.byte	4,2,35,48,13
	.byte	'reserved_34',0
	.word	1539
	.byte	4,2,35,52,13
	.byte	'OFFM',0
	.word	15608
	.byte	4,2,35,56,13
	.byte	'reserved_3C',0
	.word	1539
	.byte	4,2,35,60,13
	.byte	'RESA',0
	.word	15745
	.byte	4,2,35,64,13
	.byte	'reserved_44',0
	.word	3698
	.byte	12,2,35,68,13
	.byte	'TSTMP',0
	.word	15879
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	7015
	.byte	76,2,35,84,13
	.byte	'CGSYNC',0
	.word	15062
	.byte	4,3,35,160,1,13
	.byte	'reserved_A4',0
	.word	1539
	.byte	4,3,35,164,1,13
	.byte	'RECTCFG',0
	.word	15675
	.byte	4,3,35,168,1,13
	.byte	'reserved_AC',0
	.word	16803
	.byte	36,3,35,172,1,13
	.byte	'ICCFG',0
	.word	15403
	.byte	4,3,35,208,1,13
	.byte	'reserved_D4',0
	.word	16812
	.byte	44,3,35,212,1,0,16
	.word	16821
	.byte	21
	.byte	'Ifx_DSADC_CH',0,6,198,5,3
	.word	17313
	.byte	14,28
	.word	494
	.byte	15,27,0,14,64
	.word	494
	.byte	15,63,0,14,20
	.word	494
	.byte	15,19,0,14,128,8
	.word	16821
	.byte	15,3,0,16
	.word	17367
	.byte	14,128,22
	.word	494
	.byte	15,255,21,0,10
	.byte	'_Ifx_DSADC',0,6,211,5,25,128,32,13
	.byte	'CLC',0
	.word	15947
	.byte	4,2,35,0,13
	.byte	'reserved_4',0
	.word	1539
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	16416
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	17340
	.byte	28,2,35,12,13
	.byte	'OCS',0
	.word	16740
	.byte	4,2,35,40,13
	.byte	'KRSTCLR',0
	.word	16673
	.byte	4,2,35,44,13
	.byte	'KRST1',0
	.word	16608
	.byte	4,2,35,48,13
	.byte	'KRST0',0
	.word	16543
	.byte	4,2,35,52,13
	.byte	'reserved_38',0
	.word	1539
	.byte	4,2,35,56,13
	.byte	'ACCEN0',0
	.word	14793
	.byte	4,2,35,60,13
	.byte	'reserved_40',0
	.word	17349
	.byte	64,2,35,64,13
	.byte	'GLOBCFG',0
	.word	16145
	.byte	4,3,35,128,1,13
	.byte	'reserved_84',0
	.word	1539
	.byte	4,3,35,132,1,13
	.byte	'GLOBRC',0
	.word	16212
	.byte	4,3,35,136,1,13
	.byte	'reserved_8C',0
	.word	1539
	.byte	4,3,35,140,1,13
	.byte	'ACCPROT',0
	.word	14859
	.byte	4,3,35,144,1,13
	.byte	'reserved_94',0
	.word	3698
	.byte	12,3,35,148,1,13
	.byte	'CGCFG',0
	.word	14926
	.byte	4,3,35,160,1,13
	.byte	'reserved_A4',0
	.word	3698
	.byte	12,3,35,164,1,13
	.byte	'GLOBVCMH0',0
	.word	16278
	.byte	4,3,35,176,1,13
	.byte	'reserved_B4',0
	.word	1539
	.byte	4,3,35,180,1,13
	.byte	'GLOBVCMH2',0
	.word	16347
	.byte	4,3,35,184,1,13
	.byte	'reserved_BC',0
	.word	17358
	.byte	20,3,35,188,1,13
	.byte	'IGCFG',0
	.word	16478
	.byte	4,3,35,208,1,13
	.byte	'reserved_D4',0
	.word	3698
	.byte	12,3,35,212,1,13
	.byte	'EVFLAG',0
	.word	16010
	.byte	4,3,35,224,1,13
	.byte	'EVFLAGCLR',0
	.word	16076
	.byte	4,3,35,228,1,13
	.byte	'reserved_E8',0
	.word	2729
	.byte	24,3,35,232,1,13
	.byte	'CH',0
	.word	17377
	.byte	128,8,3,35,128,2,13
	.byte	'reserved_500',0
	.word	17382
	.byte	128,22,3,35,128,10,0,16
	.word	17393
	.byte	21
	.byte	'Ifx_DSADC',0,6,243,5,3
	.word	17971
	.byte	21
	.byte	'boolean',0,7,101,29
	.word	494
	.byte	21
	.byte	'uint8',0,7,105,29
	.word	494
	.byte	21
	.byte	'uint16',0,7,109,29
	.word	511
	.byte	7
	.byte	'unsigned long int',0,4,7,21
	.byte	'uint32',0,7,113,29
	.word	18040
	.byte	21
	.byte	'uint64',0,7,118,29
	.word	353
	.byte	21
	.byte	'sint16',0,7,126,29
	.word	8547
	.byte	7
	.byte	'long int',0,4,5,21
	.byte	'sint32',0,7,131,1,29
	.word	18106
	.byte	7
	.byte	'long long int',0,8,5,21
	.byte	'sint64',0,7,138,1,29
	.word	18134
	.byte	21
	.byte	'float32',0,7,167,1,29
	.word	299
	.byte	21
	.byte	'pvoid',0,8,57,28
	.word	385
	.byte	21
	.byte	'Ifx_TickTime',0,8,79,28
	.word	18134
	.byte	17,8,130,1,9,1,18
	.byte	'Ifx_RxSel_a',0,0,18
	.byte	'Ifx_RxSel_b',0,1,18
	.byte	'Ifx_RxSel_c',0,2,18
	.byte	'Ifx_RxSel_d',0,3,18
	.byte	'Ifx_RxSel_e',0,4,18
	.byte	'Ifx_RxSel_f',0,5,18
	.byte	'Ifx_RxSel_g',0,6,18
	.byte	'Ifx_RxSel_h',0,7,0,21
	.byte	'Ifx_RxSel',0,8,140,1,3
	.word	18219
	.byte	21
	.byte	'Ifx_P_ACCEN0_Bits',0,4,79,3
	.word	7111
	.byte	21
	.byte	'Ifx_P_ACCEN1_Bits',0,4,85,3
	.word	7024
	.byte	21
	.byte	'Ifx_P_ESR_Bits',0,4,107,3
	.word	3367
	.byte	21
	.byte	'Ifx_P_ID_Bits',0,4,115,3
	.word	1420
	.byte	21
	.byte	'Ifx_P_IN_Bits',0,4,137,1,3
	.word	2415
	.byte	21
	.byte	'Ifx_P_IOCR0_Bits',0,4,150,1,3
	.word	1548
	.byte	21
	.byte	'Ifx_P_IOCR12_Bits',0,4,163,1,3
	.word	2195
	.byte	21
	.byte	'Ifx_P_IOCR4_Bits',0,4,176,1,3
	.word	1763
	.byte	21
	.byte	'Ifx_P_IOCR8_Bits',0,4,189,1,3
	.word	1978
	.byte	21
	.byte	'Ifx_P_LPCR0_Bits',0,4,197,1,3
	.word	6383
	.byte	21
	.byte	'Ifx_P_LPCR1_Bits',0,4,205,1,3
	.word	6507
	.byte	21
	.byte	'Ifx_P_LPCR1_P21_Bits',0,4,215,1,3
	.word	6591
	.byte	21
	.byte	'Ifx_P_LPCR2_Bits',0,4,229,1,3
	.word	6771
	.byte	21
	.byte	'Ifx_P_OMCR0_Bits',0,4,240,1,3
	.word	5022
	.byte	21
	.byte	'Ifx_P_OMCR12_Bits',0,4,250,1,3
	.word	5546
	.byte	21
	.byte	'Ifx_P_OMCR4_Bits',0,4,133,2,3
	.word	5196
	.byte	21
	.byte	'Ifx_P_OMCR8_Bits',0,4,144,2,3
	.word	5370
	.byte	21
	.byte	'Ifx_P_OMCR_Bits',0,4,166,2,3
	.word	6035
	.byte	21
	.byte	'Ifx_P_OMR_Bits',0,4,203,2,3
	.word	849
	.byte	21
	.byte	'Ifx_P_OMSR0_Bits',0,4,213,2,3
	.word	4359
	.byte	21
	.byte	'Ifx_P_OMSR12_Bits',0,4,224,2,3
	.word	4847
	.byte	21
	.byte	'Ifx_P_OMSR4_Bits',0,4,235,2,3
	.word	4506
	.byte	21
	.byte	'Ifx_P_OMSR8_Bits',0,4,246,2,3
	.word	4675
	.byte	21
	.byte	'Ifx_P_OMSR_Bits',0,4,140,3,3
	.word	5702
	.byte	21
	.byte	'Ifx_P_OUT_Bits',0,4,162,3,3
	.word	533
	.byte	21
	.byte	'Ifx_P_PCSR_Bits',0,4,180,3,3
	.word	4073
	.byte	21
	.byte	'Ifx_P_PDISC_Bits',0,4,202,3,3
	.word	3707
	.byte	21
	.byte	'Ifx_P_PDR0_Bits',0,4,223,3,3
	.word	2738
	.byte	21
	.byte	'Ifx_P_PDR1_Bits',0,4,244,3,3
	.word	3042
	.byte	21
	.byte	'Ifx_P_ACCEN0',0,4,129,4,3
	.word	7638
	.byte	21
	.byte	'Ifx_P_ACCEN1',0,4,137,4,3
	.word	7071
	.byte	21
	.byte	'Ifx_P_ESR',0,4,145,4,3
	.word	3658
	.byte	21
	.byte	'Ifx_P_ID',0,4,153,4,3
	.word	1499
	.byte	21
	.byte	'Ifx_P_IN',0,4,161,4,3
	.word	2689
	.byte	21
	.byte	'Ifx_P_IOCR0',0,4,169,4,3
	.word	1723
	.byte	21
	.byte	'Ifx_P_IOCR12',0,4,177,4,3
	.word	2375
	.byte	21
	.byte	'Ifx_P_IOCR4',0,4,185,4,3
	.word	1938
	.byte	21
	.byte	'Ifx_P_IOCR8',0,4,193,4,3
	.word	2155
	.byte	21
	.byte	'Ifx_P_LPCR0',0,4,201,4,3
	.word	6467
	.byte	21
	.byte	'Ifx_P_LPCR1',0,4,210,4,3
	.word	6716
	.byte	21
	.byte	'Ifx_P_LPCR2',0,4,218,4,3
	.word	6975
	.byte	21
	.byte	'Ifx_P_OMCR',0,4,226,4,3
	.word	6343
	.byte	21
	.byte	'Ifx_P_OMCR0',0,4,234,4,3
	.word	5156
	.byte	21
	.byte	'Ifx_P_OMCR12',0,4,242,4,3
	.word	5662
	.byte	21
	.byte	'Ifx_P_OMCR4',0,4,250,4,3
	.word	5330
	.byte	21
	.byte	'Ifx_P_OMCR8',0,4,130,5,3
	.word	5506
	.byte	21
	.byte	'Ifx_P_OMR',0,4,138,5,3
	.word	1380
	.byte	21
	.byte	'Ifx_P_OMSR',0,4,146,5,3
	.word	5995
	.byte	21
	.byte	'Ifx_P_OMSR0',0,4,154,5,3
	.word	4466
	.byte	21
	.byte	'Ifx_P_OMSR12',0,4,162,5,3
	.word	4982
	.byte	21
	.byte	'Ifx_P_OMSR4',0,4,170,5,3
	.word	4635
	.byte	21
	.byte	'Ifx_P_OMSR8',0,4,178,5,3
	.word	4807
	.byte	21
	.byte	'Ifx_P_OUT',0,4,186,5,3
	.word	809
	.byte	21
	.byte	'Ifx_P_PCSR',0,4,194,5,3
	.word	4319
	.byte	21
	.byte	'Ifx_P_PDISC',0,4,202,5,3
	.word	4033
	.byte	21
	.byte	'Ifx_P_PDR0',0,4,210,5,3
	.word	3002
	.byte	21
	.byte	'Ifx_P_PDR1',0,4,218,5,3
	.word	3318
	.byte	16
	.word	7678
	.byte	21
	.byte	'Ifx_P',0,4,139,6,3
	.word	19675
	.byte	17,9,250,10,9,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy16384',0,0,18
	.byte	'IfxScu_WDTCON1_IR_divBy256',0,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy64',0,2,0,21
	.byte	'IfxScu_WDTCON1_IR',0,9,255,10,3
	.word	19695
	.byte	10
	.byte	'_Ifx_SCU_ACCEN0_Bits',0,10,45,16,4,11
	.byte	'EN0',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	494
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	494
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	494
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	494
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	494
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	494
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	494
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	494
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	494
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	494
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	494
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	494
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	494
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	494
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	494
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	494
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	494
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	494
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	494
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	494
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	494
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	494
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	494
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	494
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	494
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	494
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	494
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	494
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_ACCEN0_Bits',0,10,79,3
	.word	19817
	.byte	10
	.byte	'_Ifx_SCU_ACCEN1_Bits',0,10,82,16,4,11
	.byte	'reserved_0',0,4
	.word	471
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_SCU_ACCEN1_Bits',0,10,85,3
	.word	20374
	.byte	10
	.byte	'_Ifx_SCU_ARSTDIS_Bits',0,10,88,16,4,11
	.byte	'STM0DIS',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'STM1DIS',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'STM2DIS',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	471
	.byte	29,0,2,35,0,0,21
	.byte	'Ifx_SCU_ARSTDIS_Bits',0,10,94,3
	.word	20451
	.byte	10
	.byte	'_Ifx_SCU_CCUCON0_Bits',0,10,97,16,4,11
	.byte	'BAUD1DIV',0,1
	.word	494
	.byte	4,4,2,35,0,11
	.byte	'BAUD2DIV',0,1
	.word	494
	.byte	4,0,2,35,0,11
	.byte	'SRIDIV',0,1
	.word	494
	.byte	4,4,2,35,1,11
	.byte	'LPDIV',0,1
	.word	494
	.byte	4,0,2,35,1,11
	.byte	'SPBDIV',0,1
	.word	494
	.byte	4,4,2,35,2,11
	.byte	'FSI2DIV',0,1
	.word	494
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	494
	.byte	2,0,2,35,2,11
	.byte	'FSIDIV',0,1
	.word	494
	.byte	2,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	494
	.byte	2,4,2,35,3,11
	.byte	'CLKSEL',0,1
	.word	494
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	494
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	494
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON0_Bits',0,10,111,3
	.word	20587
	.byte	10
	.byte	'_Ifx_SCU_CCUCON1_Bits',0,10,114,16,4,11
	.byte	'CANDIV',0,1
	.word	494
	.byte	4,4,2,35,0,11
	.byte	'ERAYDIV',0,1
	.word	494
	.byte	4,0,2,35,0,11
	.byte	'STMDIV',0,1
	.word	494
	.byte	4,4,2,35,1,11
	.byte	'GTMDIV',0,1
	.word	494
	.byte	4,0,2,35,1,11
	.byte	'ETHDIV',0,1
	.word	494
	.byte	4,4,2,35,2,11
	.byte	'ASCLINFDIV',0,1
	.word	494
	.byte	4,0,2,35,2,11
	.byte	'ASCLINSDIV',0,1
	.word	494
	.byte	4,4,2,35,3,11
	.byte	'INSEL',0,1
	.word	494
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	494
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	494
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON1_Bits',0,10,126,3
	.word	20867
	.byte	10
	.byte	'_Ifx_SCU_CCUCON2_Bits',0,10,129,1,16,4,11
	.byte	'BBBDIV',0,1
	.word	494
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	471
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	494
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	494
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON2_Bits',0,10,135,1,3
	.word	21105
	.byte	10
	.byte	'_Ifx_SCU_CCUCON3_Bits',0,10,138,1,16,4,11
	.byte	'PLLDIV',0,1
	.word	494
	.byte	6,2,2,35,0,11
	.byte	'PLLSEL',0,1
	.word	494
	.byte	2,0,2,35,0,11
	.byte	'PLLERAYDIV',0,1
	.word	494
	.byte	6,2,2,35,1,11
	.byte	'PLLERAYSEL',0,1
	.word	494
	.byte	2,0,2,35,1,11
	.byte	'SRIDIV',0,1
	.word	494
	.byte	6,2,2,35,2,11
	.byte	'SRISEL',0,1
	.word	494
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	494
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	494
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	494
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	494
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON3_Bits',0,10,150,1,3
	.word	21233
	.byte	10
	.byte	'_Ifx_SCU_CCUCON4_Bits',0,10,153,1,16,4,11
	.byte	'SPBDIV',0,1
	.word	494
	.byte	6,2,2,35,0,11
	.byte	'SPBSEL',0,1
	.word	494
	.byte	2,0,2,35,0,11
	.byte	'GTMDIV',0,1
	.word	494
	.byte	6,2,2,35,1,11
	.byte	'GTMSEL',0,1
	.word	494
	.byte	2,0,2,35,1,11
	.byte	'STMDIV',0,1
	.word	494
	.byte	6,2,2,35,2,11
	.byte	'STMSEL',0,1
	.word	494
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	494
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	494
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	494
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	494
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON4_Bits',0,10,165,1,3
	.word	21476
	.byte	10
	.byte	'_Ifx_SCU_CCUCON5_Bits',0,10,168,1,16,4,11
	.byte	'MAXDIV',0,1
	.word	494
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	471
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	494
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	494
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON5_Bits',0,10,174,1,3
	.word	21711
	.byte	10
	.byte	'_Ifx_SCU_CCUCON6_Bits',0,10,177,1,16,4,11
	.byte	'CPU0DIV',0,1
	.word	494
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	471
	.byte	26,0,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON6_Bits',0,10,181,1,3
	.word	21839
	.byte	10
	.byte	'_Ifx_SCU_CCUCON7_Bits',0,10,184,1,16,4,11
	.byte	'CPU1DIV',0,1
	.word	494
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	471
	.byte	26,0,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON7_Bits',0,10,188,1,3
	.word	21939
	.byte	10
	.byte	'_Ifx_SCU_CHIPID_Bits',0,10,191,1,16,4,11
	.byte	'CHREV',0,1
	.word	494
	.byte	6,2,2,35,0,11
	.byte	'CHTEC',0,1
	.word	494
	.byte	2,0,2,35,0,11
	.byte	'CHID',0,1
	.word	494
	.byte	8,0,2,35,1,11
	.byte	'EEA',0,1
	.word	494
	.byte	1,7,2,35,2,11
	.byte	'UCODE',0,1
	.word	494
	.byte	7,0,2,35,2,11
	.byte	'FSIZE',0,1
	.word	494
	.byte	4,4,2,35,3,11
	.byte	'SP',0,1
	.word	494
	.byte	2,2,2,35,3,11
	.byte	'SEC',0,1
	.word	494
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	494
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CHIPID_Bits',0,10,202,1,3
	.word	22039
	.byte	10
	.byte	'_Ifx_SCU_DTSCON_Bits',0,10,205,1,16,4,11
	.byte	'PWD',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'START',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	494
	.byte	2,4,2,35,0,11
	.byte	'CAL',0,4
	.word	471
	.byte	20,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	494
	.byte	7,1,2,35,3,11
	.byte	'SLCK',0,1
	.word	494
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_DTSCON_Bits',0,10,213,1,3
	.word	22247
	.byte	10
	.byte	'_Ifx_SCU_DTSLIM_Bits',0,10,216,1,16,4,11
	.byte	'LOWER',0,2
	.word	511
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	494
	.byte	5,1,2,35,1,11
	.byte	'LLU',0,1
	.word	494
	.byte	1,0,2,35,1,11
	.byte	'UPPER',0,2
	.word	511
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	494
	.byte	4,2,2,35,3,11
	.byte	'SLCK',0,1
	.word	494
	.byte	1,1,2,35,3,11
	.byte	'UOF',0,1
	.word	494
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_DTSLIM_Bits',0,10,225,1,3
	.word	22412
	.byte	10
	.byte	'_Ifx_SCU_DTSSTAT_Bits',0,10,228,1,16,4,11
	.byte	'RESULT',0,2
	.word	511
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	494
	.byte	4,2,2,35,1,11
	.byte	'RDY',0,1
	.word	494
	.byte	1,1,2,35,1,11
	.byte	'BUSY',0,1
	.word	494
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	511
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_DTSSTAT_Bits',0,10,235,1,3
	.word	22595
	.byte	10
	.byte	'_Ifx_SCU_EICR_Bits',0,10,238,1,16,4,11
	.byte	'reserved_0',0,1
	.word	494
	.byte	4,4,2,35,0,11
	.byte	'EXIS0',0,1
	.word	494
	.byte	3,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	494
	.byte	1,0,2,35,0,11
	.byte	'FEN0',0,1
	.word	494
	.byte	1,7,2,35,1,11
	.byte	'REN0',0,1
	.word	494
	.byte	1,6,2,35,1,11
	.byte	'LDEN0',0,1
	.word	494
	.byte	1,5,2,35,1,11
	.byte	'EIEN0',0,1
	.word	494
	.byte	1,4,2,35,1,11
	.byte	'INP0',0,1
	.word	494
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	471
	.byte	5,12,2,35,0,11
	.byte	'EXIS1',0,1
	.word	494
	.byte	3,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	494
	.byte	1,0,2,35,2,11
	.byte	'FEN1',0,1
	.word	494
	.byte	1,7,2,35,3,11
	.byte	'REN1',0,1
	.word	494
	.byte	1,6,2,35,3,11
	.byte	'LDEN1',0,1
	.word	494
	.byte	1,5,2,35,3,11
	.byte	'EIEN1',0,1
	.word	494
	.byte	1,4,2,35,3,11
	.byte	'INP1',0,1
	.word	494
	.byte	3,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	494
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EICR_Bits',0,10,129,2,3
	.word	22749
	.byte	10
	.byte	'_Ifx_SCU_EIFR_Bits',0,10,132,2,16,4,11
	.byte	'INTF0',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'INTF1',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'INTF2',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'INTF3',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'INTF4',0,1
	.word	494
	.byte	1,3,2,35,0,11
	.byte	'INTF5',0,1
	.word	494
	.byte	1,2,2,35,0,11
	.byte	'INTF6',0,1
	.word	494
	.byte	1,1,2,35,0,11
	.byte	'INTF7',0,1
	.word	494
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	471
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_SCU_EIFR_Bits',0,10,143,2,3
	.word	23113
	.byte	10
	.byte	'_Ifx_SCU_EMSR_Bits',0,10,146,2,16,4,11
	.byte	'POL',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'MODE',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'ENON',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'PSEL',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	511
	.byte	12,0,2,35,0,11
	.byte	'EMSF',0,1
	.word	494
	.byte	1,7,2,35,2,11
	.byte	'SEMSF',0,1
	.word	494
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	494
	.byte	6,0,2,35,2,11
	.byte	'EMSFM',0,1
	.word	494
	.byte	2,6,2,35,3,11
	.byte	'SEMSFM',0,1
	.word	494
	.byte	2,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	494
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_EMSR_Bits',0,10,159,2,3
	.word	23324
	.byte	10
	.byte	'_Ifx_SCU_ESRCFG_Bits',0,10,162,2,16,4,11
	.byte	'reserved_0',0,1
	.word	494
	.byte	7,1,2,35,0,11
	.byte	'EDCON',0,2
	.word	511
	.byte	2,7,2,35,0,11
	.byte	'reserved_9',0,4
	.word	471
	.byte	23,0,2,35,0,0,21
	.byte	'Ifx_SCU_ESRCFG_Bits',0,10,167,2,3
	.word	23576
	.byte	10
	.byte	'_Ifx_SCU_ESROCFG_Bits',0,10,170,2,16,4,11
	.byte	'ARI',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'ARC',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	471
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_ESROCFG_Bits',0,10,175,2,3
	.word	23694
	.byte	10
	.byte	'_Ifx_SCU_EVR13CON_Bits',0,10,178,2,16,4,11
	.byte	'reserved_0',0,4
	.word	471
	.byte	28,4,2,35,0,11
	.byte	'EVR13OFF',0,1
	.word	494
	.byte	1,3,2,35,3,11
	.byte	'BPEVR13OFF',0,1
	.word	494
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	494
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	494
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVR13CON_Bits',0,10,185,2,3
	.word	23805
	.byte	10
	.byte	'_Ifx_SCU_EVR33CON_Bits',0,10,188,2,16,4,11
	.byte	'reserved_0',0,4
	.word	471
	.byte	28,4,2,35,0,11
	.byte	'EVR33OFF',0,1
	.word	494
	.byte	1,3,2,35,3,11
	.byte	'BPEVR33OFF',0,1
	.word	494
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	494
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	494
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVR33CON_Bits',0,10,195,2,3
	.word	23968
	.byte	10
	.byte	'_Ifx_SCU_EVRADCSTAT_Bits',0,10,198,2,16,4,11
	.byte	'ADC13V',0,1
	.word	494
	.byte	8,0,2,35,0,11
	.byte	'ADC33V',0,1
	.word	494
	.byte	8,0,2,35,1,11
	.byte	'ADCSWDV',0,1
	.word	494
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	494
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	494
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRADCSTAT_Bits',0,10,205,2,3
	.word	24131
	.byte	10
	.byte	'_Ifx_SCU_EVRDVSTAT_Bits',0,10,208,2,16,4,11
	.byte	'DVS13TRIM',0,1
	.word	494
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	494
	.byte	8,0,2,35,1,11
	.byte	'DVS33TRIM',0,1
	.word	494
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	494
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	494
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRDVSTAT_Bits',0,10,215,2,3
	.word	24289
	.byte	10
	.byte	'_Ifx_SCU_EVRMONCTRL_Bits',0,10,218,2,16,4,11
	.byte	'EVR13OVMOD',0,1
	.word	494
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	494
	.byte	2,4,2,35,0,11
	.byte	'EVR13UVMOD',0,1
	.word	494
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	494
	.byte	2,0,2,35,0,11
	.byte	'EVR33OVMOD',0,1
	.word	494
	.byte	2,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	494
	.byte	2,4,2,35,1,11
	.byte	'EVR33UVMOD',0,1
	.word	494
	.byte	2,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	494
	.byte	2,0,2,35,1,11
	.byte	'SWDOVMOD',0,1
	.word	494
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	494
	.byte	2,4,2,35,2,11
	.byte	'SWDUVMOD',0,1
	.word	494
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,2
	.word	511
	.byte	10,0,2,35,2,0,21
	.byte	'Ifx_SCU_EVRMONCTRL_Bits',0,10,232,2,3
	.word	24454
	.byte	10
	.byte	'_Ifx_SCU_EVROSCCTRL_Bits',0,10,235,2,16,4,11
	.byte	'OSCTRIM',0,2
	.word	511
	.byte	10,6,2,35,0,11
	.byte	'OSCPTAT',0,1
	.word	494
	.byte	6,0,2,35,1,11
	.byte	'OSCANASEL',0,1
	.word	494
	.byte	4,4,2,35,2,11
	.byte	'HPBGTRIM',0,2
	.word	511
	.byte	7,5,2,35,2,11
	.byte	'HPBGCLKEN',0,1
	.word	494
	.byte	1,4,2,35,3,11
	.byte	'OSC3V3',0,1
	.word	494
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	494
	.byte	2,1,2,35,3,11
	.byte	'LCK',0,1
	.word	494
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVROSCCTRL_Bits',0,10,245,2,3
	.word	24783
	.byte	10
	.byte	'_Ifx_SCU_EVROVMON_Bits',0,10,248,2,16,4,11
	.byte	'EVR13OVVAL',0,1
	.word	494
	.byte	8,0,2,35,0,11
	.byte	'EVR33OVVAL',0,1
	.word	494
	.byte	8,0,2,35,1,11
	.byte	'SWDOVVAL',0,1
	.word	494
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	494
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	494
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVROVMON_Bits',0,10,255,2,3
	.word	25004
	.byte	10
	.byte	'_Ifx_SCU_EVRRSTCON_Bits',0,10,130,3,16,4,11
	.byte	'RST13TRIM',0,1
	.word	494
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	471
	.byte	16,8,2,35,0,11
	.byte	'RST13OFF',0,1
	.word	494
	.byte	1,7,2,35,3,11
	.byte	'BPRST13OFF',0,1
	.word	494
	.byte	1,6,2,35,3,11
	.byte	'RST33OFF',0,1
	.word	494
	.byte	1,5,2,35,3,11
	.byte	'BPRST33OFF',0,1
	.word	494
	.byte	1,4,2,35,3,11
	.byte	'RSTSWDOFF',0,1
	.word	494
	.byte	1,3,2,35,3,11
	.byte	'BPRSTSWDOFF',0,1
	.word	494
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	494
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	494
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRRSTCON_Bits',0,10,142,3,3
	.word	25167
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF1_Bits',0,10,145,3,16,4,11
	.byte	'SD5P',0,1
	.word	494
	.byte	8,0,2,35,0,11
	.byte	'SD5I',0,1
	.word	494
	.byte	8,0,2,35,1,11
	.byte	'SD5D',0,1
	.word	494
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	494
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	494
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF1_Bits',0,10,152,3,3
	.word	25439
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF2_Bits',0,10,155,3,16,4,11
	.byte	'SD33P',0,1
	.word	494
	.byte	8,0,2,35,0,11
	.byte	'SD33I',0,1
	.word	494
	.byte	8,0,2,35,1,11
	.byte	'SD33D',0,1
	.word	494
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	494
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	494
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF2_Bits',0,10,162,3,3
	.word	25592
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF3_Bits',0,10,165,3,16,4,11
	.byte	'CT5REG0',0,1
	.word	494
	.byte	8,0,2,35,0,11
	.byte	'CT5REG1',0,1
	.word	494
	.byte	8,0,2,35,1,11
	.byte	'CT5REG2',0,1
	.word	494
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	494
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	494
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF3_Bits',0,10,172,3,3
	.word	25748
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF4_Bits',0,10,175,3,16,4,11
	.byte	'CT5REG3',0,1
	.word	494
	.byte	8,0,2,35,0,11
	.byte	'CT5REG4',0,1
	.word	494
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	511
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	494
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF4_Bits',0,10,181,3,3
	.word	25910
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF5_Bits',0,10,184,3,16,4,11
	.byte	'CT33REG0',0,1
	.word	494
	.byte	8,0,2,35,0,11
	.byte	'CT33REG1',0,1
	.word	494
	.byte	8,0,2,35,1,11
	.byte	'CT33REG2',0,1
	.word	494
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	494
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	494
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF5_Bits',0,10,191,3,3
	.word	26053
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF6_Bits',0,10,194,3,16,4,11
	.byte	'CT33REG3',0,1
	.word	494
	.byte	8,0,2,35,0,11
	.byte	'CT33REG4',0,1
	.word	494
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	511
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	494
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF6_Bits',0,10,200,3,3
	.word	26218
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL1_Bits',0,10,203,3,16,4,11
	.byte	'SDFREQSPRD',0,2
	.word	511
	.byte	16,0,2,35,0,11
	.byte	'SDFREQ',0,1
	.word	494
	.byte	8,0,2,35,2,11
	.byte	'SDSTEP',0,1
	.word	494
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	494
	.byte	2,2,2,35,3,11
	.byte	'SDSAMPLE',0,1
	.word	494
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	494
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCTRL1_Bits',0,10,211,3,3
	.word	26363
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL2_Bits',0,10,214,3,16,4,11
	.byte	'DRVP',0,1
	.word	494
	.byte	8,0,2,35,0,11
	.byte	'SDMINMAXDC',0,1
	.word	494
	.byte	8,0,2,35,1,11
	.byte	'DRVN',0,1
	.word	494
	.byte	8,0,2,35,2,11
	.byte	'SDLUT',0,1
	.word	494
	.byte	6,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	494
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	494
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCTRL2_Bits',0,10,222,3,3
	.word	26544
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL3_Bits',0,10,225,3,16,4,11
	.byte	'SDPWMPRE',0,1
	.word	494
	.byte	8,0,2,35,0,11
	.byte	'SDPID',0,1
	.word	494
	.byte	8,0,2,35,1,11
	.byte	'SDVOKLVL',0,1
	.word	494
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	494
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	494
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCTRL3_Bits',0,10,232,3,3
	.word	26718
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL4_Bits',0,10,235,3,16,4,11
	.byte	'reserved_0',0,1
	.word	494
	.byte	8,0,2,35,0,11
	.byte	'SYNCDIV',0,1
	.word	494
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	471
	.byte	20,1,2,35,0,11
	.byte	'LCK',0,1
	.word	494
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCTRL4_Bits',0,10,241,3,3
	.word	26878
	.byte	10
	.byte	'_Ifx_SCU_EVRSTAT_Bits',0,10,244,3,16,4,11
	.byte	'EVR13',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'OV13',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'EVR33',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'OV33',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'OVSWD',0,1
	.word	494
	.byte	1,3,2,35,0,11
	.byte	'UV13',0,1
	.word	494
	.byte	1,2,2,35,0,11
	.byte	'UV33',0,1
	.word	494
	.byte	1,1,2,35,0,11
	.byte	'UVSWD',0,1
	.word	494
	.byte	1,0,2,35,0,11
	.byte	'EXTPASS13',0,1
	.word	494
	.byte	1,7,2,35,1,11
	.byte	'EXTPASS33',0,1
	.word	494
	.byte	1,6,2,35,1,11
	.byte	'BGPROK',0,1
	.word	494
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	471
	.byte	21,0,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSTAT_Bits',0,10,130,4,3
	.word	27022
	.byte	10
	.byte	'_Ifx_SCU_EVRTRIM_Bits',0,10,133,4,16,4,11
	.byte	'EVR13TRIM',0,1
	.word	494
	.byte	8,0,2,35,0,11
	.byte	'SDVOUTSEL',0,1
	.word	494
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	511
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	494
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRTRIM_Bits',0,10,139,4,3
	.word	27296
	.byte	10
	.byte	'_Ifx_SCU_EVRUVMON_Bits',0,10,142,4,16,4,11
	.byte	'EVR13UVVAL',0,1
	.word	494
	.byte	8,0,2,35,0,11
	.byte	'EVR33UVVAL',0,1
	.word	494
	.byte	8,0,2,35,1,11
	.byte	'SWDUVVAL',0,1
	.word	494
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	494
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	494
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRUVMON_Bits',0,10,149,4,3
	.word	27435
	.byte	10
	.byte	'_Ifx_SCU_EXTCON_Bits',0,10,152,4,16,4,11
	.byte	'EN0',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'SEL0',0,1
	.word	494
	.byte	4,2,2,35,0,11
	.byte	'reserved_6',0,2
	.word	511
	.byte	10,0,2,35,0,11
	.byte	'EN1',0,1
	.word	494
	.byte	1,7,2,35,2,11
	.byte	'NSEL',0,1
	.word	494
	.byte	1,6,2,35,2,11
	.byte	'SEL1',0,1
	.word	494
	.byte	4,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	494
	.byte	2,0,2,35,2,11
	.byte	'DIV1',0,1
	.word	494
	.byte	8,0,2,35,3,0,21
	.byte	'Ifx_SCU_EXTCON_Bits',0,10,163,4,3
	.word	27598
	.byte	10
	.byte	'_Ifx_SCU_FDR_Bits',0,10,166,4,16,4,11
	.byte	'STEP',0,2
	.word	511
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	494
	.byte	4,2,2,35,1,11
	.byte	'DM',0,1
	.word	494
	.byte	2,0,2,35,1,11
	.byte	'RESULT',0,2
	.word	511
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	494
	.byte	5,1,2,35,3,11
	.byte	'DISCLK',0,1
	.word	494
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_FDR_Bits',0,10,174,4,3
	.word	27816
	.byte	10
	.byte	'_Ifx_SCU_FMR_Bits',0,10,177,4,16,4,11
	.byte	'FS0',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'FS1',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'FS2',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'FS3',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'FS4',0,1
	.word	494
	.byte	1,3,2,35,0,11
	.byte	'FS5',0,1
	.word	494
	.byte	1,2,2,35,0,11
	.byte	'FS6',0,1
	.word	494
	.byte	1,1,2,35,0,11
	.byte	'FS7',0,1
	.word	494
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	494
	.byte	8,0,2,35,1,11
	.byte	'FC0',0,1
	.word	494
	.byte	1,7,2,35,2,11
	.byte	'FC1',0,1
	.word	494
	.byte	1,6,2,35,2,11
	.byte	'FC2',0,1
	.word	494
	.byte	1,5,2,35,2,11
	.byte	'FC3',0,1
	.word	494
	.byte	1,4,2,35,2,11
	.byte	'FC4',0,1
	.word	494
	.byte	1,3,2,35,2,11
	.byte	'FC5',0,1
	.word	494
	.byte	1,2,2,35,2,11
	.byte	'FC6',0,1
	.word	494
	.byte	1,1,2,35,2,11
	.byte	'FC7',0,1
	.word	494
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	494
	.byte	8,0,2,35,3,0,21
	.byte	'Ifx_SCU_FMR_Bits',0,10,197,4,3
	.word	27979
	.byte	10
	.byte	'_Ifx_SCU_ID_Bits',0,10,200,4,16,4,11
	.byte	'MODREV',0,1
	.word	494
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	494
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	511
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_ID_Bits',0,10,205,4,3
	.word	28315
	.byte	10
	.byte	'_Ifx_SCU_IGCR_Bits',0,10,208,4,16,4,11
	.byte	'IPEN00',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'IPEN01',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'IPEN02',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'IPEN03',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'IPEN04',0,1
	.word	494
	.byte	1,3,2,35,0,11
	.byte	'IPEN05',0,1
	.word	494
	.byte	1,2,2,35,0,11
	.byte	'IPEN06',0,1
	.word	494
	.byte	1,1,2,35,0,11
	.byte	'IPEN07',0,1
	.word	494
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	494
	.byte	5,3,2,35,1,11
	.byte	'GEEN0',0,1
	.word	494
	.byte	1,2,2,35,1,11
	.byte	'IGP0',0,1
	.word	494
	.byte	2,0,2,35,1,11
	.byte	'IPEN10',0,1
	.word	494
	.byte	1,7,2,35,2,11
	.byte	'IPEN11',0,1
	.word	494
	.byte	1,6,2,35,2,11
	.byte	'IPEN12',0,1
	.word	494
	.byte	1,5,2,35,2,11
	.byte	'IPEN13',0,1
	.word	494
	.byte	1,4,2,35,2,11
	.byte	'IPEN14',0,1
	.word	494
	.byte	1,3,2,35,2,11
	.byte	'IPEN15',0,1
	.word	494
	.byte	1,2,2,35,2,11
	.byte	'IPEN16',0,1
	.word	494
	.byte	1,1,2,35,2,11
	.byte	'IPEN17',0,1
	.word	494
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	494
	.byte	5,3,2,35,3,11
	.byte	'GEEN1',0,1
	.word	494
	.byte	1,2,2,35,3,11
	.byte	'IGP1',0,1
	.word	494
	.byte	2,0,2,35,3,0,21
	.byte	'Ifx_SCU_IGCR_Bits',0,10,232,4,3
	.word	28422
	.byte	10
	.byte	'_Ifx_SCU_IN_Bits',0,10,235,4,16,4,11
	.byte	'P0',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	471
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_IN_Bits',0,10,240,4,3
	.word	28874
	.byte	10
	.byte	'_Ifx_SCU_IOCR_Bits',0,10,243,4,16,4,11
	.byte	'reserved_0',0,1
	.word	494
	.byte	4,4,2,35,0,11
	.byte	'PC0',0,1
	.word	494
	.byte	4,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	494
	.byte	4,4,2,35,1,11
	.byte	'PC1',0,1
	.word	494
	.byte	4,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	511
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_IOCR_Bits',0,10,250,4,3
	.word	28973
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL0_Bits',0,10,253,4,16,4,11
	.byte	'LBISTREQ',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'LBISTREQP',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'PATTERNS',0,2
	.word	511
	.byte	14,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	511
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_LBISTCTRL0_Bits',0,10,131,5,3
	.word	29123
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL1_Bits',0,10,134,5,16,4,11
	.byte	'SEED',0,4
	.word	471
	.byte	23,9,2,35,0,11
	.byte	'reserved_23',0,1
	.word	494
	.byte	1,0,2,35,2,11
	.byte	'SPLITSH',0,1
	.word	494
	.byte	3,5,2,35,3,11
	.byte	'BODY',0,1
	.word	494
	.byte	1,4,2,35,3,11
	.byte	'LBISTFREQU',0,1
	.word	494
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_LBISTCTRL1_Bits',0,10,141,5,3
	.word	29272
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL2_Bits',0,10,144,5,16,4,11
	.byte	'SIGNATURE',0,4
	.word	471
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	494
	.byte	7,1,2,35,3,11
	.byte	'LBISTDONE',0,1
	.word	494
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_LBISTCTRL2_Bits',0,10,149,5,3
	.word	29433
	.byte	10
	.byte	'_Ifx_SCU_LCLCON_Bits',0,10,152,5,16,4,11
	.byte	'reserved_0',0,2
	.word	511
	.byte	16,0,2,35,0,11
	.byte	'LS',0,1
	.word	494
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,2
	.word	511
	.byte	14,1,2,35,2,11
	.byte	'LSEN',0,1
	.word	494
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_LCLCON_Bits',0,10,158,5,3
	.word	29563
	.byte	10
	.byte	'_Ifx_SCU_LCLTEST_Bits',0,10,161,5,16,4,11
	.byte	'LCLT0',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'LCLT1',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	471
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_LCLTEST_Bits',0,10,166,5,3
	.word	29695
	.byte	10
	.byte	'_Ifx_SCU_MANID_Bits',0,10,169,5,16,4,11
	.byte	'DEPT',0,1
	.word	494
	.byte	5,3,2,35,0,11
	.byte	'MANUF',0,2
	.word	511
	.byte	11,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	511
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_MANID_Bits',0,10,174,5,3
	.word	29810
	.byte	10
	.byte	'_Ifx_SCU_OMR_Bits',0,10,177,5,16,4,11
	.byte	'PS0',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	511
	.byte	14,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	494
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	494
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	511
	.byte	14,0,2,35,2,0,21
	.byte	'Ifx_SCU_OMR_Bits',0,10,185,5,3
	.word	29921
	.byte	10
	.byte	'_Ifx_SCU_OSCCON_Bits',0,10,188,5,16,4,11
	.byte	'reserved_0',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'PLLLV',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'OSCRES',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'GAINSEL',0,1
	.word	494
	.byte	2,3,2,35,0,11
	.byte	'MODE',0,1
	.word	494
	.byte	2,1,2,35,0,11
	.byte	'SHBY',0,1
	.word	494
	.byte	1,0,2,35,0,11
	.byte	'PLLHV',0,1
	.word	494
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	494
	.byte	1,6,2,35,1,11
	.byte	'X1D',0,1
	.word	494
	.byte	1,5,2,35,1,11
	.byte	'X1DEN',0,1
	.word	494
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	494
	.byte	4,0,2,35,1,11
	.byte	'OSCVAL',0,1
	.word	494
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	494
	.byte	2,1,2,35,2,11
	.byte	'APREN',0,1
	.word	494
	.byte	1,0,2,35,2,11
	.byte	'CAP0EN',0,1
	.word	494
	.byte	1,7,2,35,3,11
	.byte	'CAP1EN',0,1
	.word	494
	.byte	1,6,2,35,3,11
	.byte	'CAP2EN',0,1
	.word	494
	.byte	1,5,2,35,3,11
	.byte	'CAP3EN',0,1
	.word	494
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	494
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_OSCCON_Bits',0,10,209,5,3
	.word	30079
	.byte	10
	.byte	'_Ifx_SCU_OUT_Bits',0,10,212,5,16,4,11
	.byte	'P0',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	471
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_OUT_Bits',0,10,217,5,3
	.word	30491
	.byte	10
	.byte	'_Ifx_SCU_OVCCON_Bits',0,10,220,5,16,4,11
	.byte	'CSEL0',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'CSEL1',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'CSEL2',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,2
	.word	511
	.byte	13,0,2,35,0,11
	.byte	'OVSTRT',0,1
	.word	494
	.byte	1,7,2,35,2,11
	.byte	'OVSTP',0,1
	.word	494
	.byte	1,6,2,35,2,11
	.byte	'DCINVAL',0,1
	.word	494
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	494
	.byte	5,0,2,35,2,11
	.byte	'OVCONF',0,1
	.word	494
	.byte	1,7,2,35,3,11
	.byte	'POVCONF',0,1
	.word	494
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	494
	.byte	6,0,2,35,3,0,21
	.byte	'Ifx_SCU_OVCCON_Bits',0,10,233,5,3
	.word	30592
	.byte	10
	.byte	'_Ifx_SCU_OVCENABLE_Bits',0,10,236,5,16,4,11
	.byte	'OVEN0',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'OVEN1',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'OVEN2',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	471
	.byte	29,0,2,35,0,0,21
	.byte	'Ifx_SCU_OVCENABLE_Bits',0,10,242,5,3
	.word	30859
	.byte	10
	.byte	'_Ifx_SCU_PDISC_Bits',0,10,245,5,16,4,11
	.byte	'PDIS0',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	471
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_PDISC_Bits',0,10,250,5,3
	.word	30995
	.byte	10
	.byte	'_Ifx_SCU_PDR_Bits',0,10,253,5,16,4,11
	.byte	'PD0',0,1
	.word	494
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	494
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	494
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	471
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_SCU_PDR_Bits',0,10,132,6,3
	.word	31106
	.byte	10
	.byte	'_Ifx_SCU_PDRR_Bits',0,10,135,6,16,4,11
	.byte	'PDR0',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'PDR1',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'PDR2',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'PDR3',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'PDR4',0,1
	.word	494
	.byte	1,3,2,35,0,11
	.byte	'PDR5',0,1
	.word	494
	.byte	1,2,2,35,0,11
	.byte	'PDR6',0,1
	.word	494
	.byte	1,1,2,35,0,11
	.byte	'PDR7',0,1
	.word	494
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	471
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_SCU_PDRR_Bits',0,10,146,6,3
	.word	31239
	.byte	10
	.byte	'_Ifx_SCU_PLLCON0_Bits',0,10,149,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'MODEN',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	494
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	494
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	494
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	511
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	494
	.byte	7,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	494
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	494
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	494
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	494
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	494
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	494
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_PLLCON0_Bits',0,10,166,6,3
	.word	31442
	.byte	10
	.byte	'_Ifx_SCU_PLLCON1_Bits',0,10,169,6,16,4,11
	.byte	'K2DIV',0,1
	.word	494
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	494
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	494
	.byte	7,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	494
	.byte	1,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	494
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	511
	.byte	9,0,2,35,2,0,21
	.byte	'Ifx_SCU_PLLCON1_Bits',0,10,177,6,3
	.word	31798
	.byte	10
	.byte	'_Ifx_SCU_PLLCON2_Bits',0,10,180,6,16,4,11
	.byte	'MODCFG',0,2
	.word	511
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	511
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_PLLCON2_Bits',0,10,184,6,3
	.word	31976
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON0_Bits',0,10,187,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	494
	.byte	2,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	494
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	494
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	494
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	511
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	494
	.byte	5,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	494
	.byte	2,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	494
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	494
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	494
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	494
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	494
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	494
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_PLLERAYCON0_Bits',0,10,204,6,3
	.word	32076
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON1_Bits',0,10,207,6,16,4,11
	.byte	'K2DIV',0,1
	.word	494
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	494
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	494
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	494
	.byte	4,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	494
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	511
	.byte	9,0,2,35,2,0,21
	.byte	'Ifx_SCU_PLLERAYCON1_Bits',0,10,215,6,3
	.word	32446
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYSTAT_Bits',0,10,218,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'PWDSTAT',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	494
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	494
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	471
	.byte	26,0,2,35,0,0,21
	.byte	'Ifx_SCU_PLLERAYSTAT_Bits',0,10,227,6,3
	.word	32632
	.byte	10
	.byte	'_Ifx_SCU_PLLSTAT_Bits',0,10,230,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	494
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	494
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	494
	.byte	1,1,2,35,0,11
	.byte	'MODRUN',0,1
	.word	494
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	471
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_SCU_PLLSTAT_Bits',0,10,241,6,3
	.word	32830
	.byte	10
	.byte	'_Ifx_SCU_PMCSR_Bits',0,10,244,6,16,4,11
	.byte	'REQSLP',0,1
	.word	494
	.byte	2,6,2,35,0,11
	.byte	'SMUSLP',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	494
	.byte	5,0,2,35,0,11
	.byte	'PMST',0,1
	.word	494
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	471
	.byte	21,0,2,35,0,0,21
	.byte	'Ifx_SCU_PMCSR_Bits',0,10,251,6,3
	.word	33063
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR0_Bits',0,10,254,6,16,4,11
	.byte	'reserved_0',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'ESR1WKEN',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'PINAWKEN',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'PINBWKEN',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'ESR0DFEN',0,1
	.word	494
	.byte	1,3,2,35,0,11
	.byte	'ESR0EDCON',0,1
	.word	494
	.byte	2,1,2,35,0,11
	.byte	'ESR1DFEN',0,1
	.word	494
	.byte	1,0,2,35,0,11
	.byte	'ESR1EDCON',0,1
	.word	494
	.byte	2,6,2,35,1,11
	.byte	'PINADFEN',0,1
	.word	494
	.byte	1,5,2,35,1,11
	.byte	'PINAEDCON',0,1
	.word	494
	.byte	2,3,2,35,1,11
	.byte	'PINBDFEN',0,1
	.word	494
	.byte	1,2,2,35,1,11
	.byte	'PINBEDCON',0,1
	.word	494
	.byte	2,0,2,35,1,11
	.byte	'SCREN',0,1
	.word	494
	.byte	1,7,2,35,2,11
	.byte	'STBYRAMSEL',0,1
	.word	494
	.byte	2,5,2,35,2,11
	.byte	'SCRCLKSEL',0,1
	.word	494
	.byte	1,4,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	494
	.byte	1,3,2,35,2,11
	.byte	'TRISTEN',0,1
	.word	494
	.byte	1,2,2,35,2,11
	.byte	'TRISTREQ',0,1
	.word	494
	.byte	1,1,2,35,2,11
	.byte	'PORSTDF',0,1
	.word	494
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	494
	.byte	1,7,2,35,3,11
	.byte	'DCDCSYNC',0,1
	.word	494
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	494
	.byte	3,3,2,35,3,11
	.byte	'ESR0TRIST',0,1
	.word	494
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	494
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	494
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_PMSWCR0_Bits',0,10,153,7,3
	.word	33215
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR1_Bits',0,10,156,7,16,4,11
	.byte	'SCRSTEN',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'SCRSTREQ',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	494
	.byte	6,0,2,35,0,11
	.byte	'CPUIDLSEL',0,1
	.word	494
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	494
	.byte	1,4,2,35,1,11
	.byte	'IRADIS',0,1
	.word	494
	.byte	1,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	494
	.byte	3,0,2,35,1,11
	.byte	'SCRCFG',0,1
	.word	494
	.byte	8,0,2,35,2,11
	.byte	'CPUSEL',0,1
	.word	494
	.byte	3,5,2,35,3,11
	.byte	'STBYEVEN',0,1
	.word	494
	.byte	1,4,2,35,3,11
	.byte	'STBYEV',0,1
	.word	494
	.byte	3,1,2,35,3,11
	.byte	'LCK',0,1
	.word	494
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_PMSWCR1_Bits',0,10,170,7,3
	.word	33782
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR2_Bits',0,10,173,7,16,4,11
	.byte	'SCRINT',0,1
	.word	494
	.byte	8,0,2,35,0,11
	.byte	'BUSY',0,1
	.word	494
	.byte	1,7,2,35,1,11
	.byte	'SCRECC',0,1
	.word	494
	.byte	1,6,2,35,1,11
	.byte	'SCRWDT',0,1
	.word	494
	.byte	1,5,2,35,1,11
	.byte	'SCRRST',0,1
	.word	494
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	494
	.byte	4,0,2,35,1,11
	.byte	'TCINT',0,1
	.word	494
	.byte	8,0,2,35,2,11
	.byte	'TCINTREQ',0,1
	.word	494
	.byte	1,7,2,35,3,11
	.byte	'SMURST',0,1
	.word	494
	.byte	1,6,2,35,3,11
	.byte	'RST',0,1
	.word	494
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	494
	.byte	4,1,2,35,3,11
	.byte	'LCK',0,1
	.word	494
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_PMSWCR2_Bits',0,10,187,7,3
	.word	34076
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTAT_Bits',0,10,190,7,16,4,11
	.byte	'reserved_0',0,1
	.word	494
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKP',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUN',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'PINAWKP',0,1
	.word	494
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUN',0,1
	.word	494
	.byte	1,2,2,35,0,11
	.byte	'PINBWKP',0,1
	.word	494
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUN',0,1
	.word	494
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	494
	.byte	1,7,2,35,1,11
	.byte	'PORSTDF',0,1
	.word	494
	.byte	1,6,2,35,1,11
	.byte	'HWCFGEVR',0,1
	.word	494
	.byte	3,3,2,35,1,11
	.byte	'STBYRAM',0,1
	.word	494
	.byte	2,1,2,35,1,11
	.byte	'TRIST',0,1
	.word	494
	.byte	1,0,2,35,1,11
	.byte	'SCRST',0,1
	.word	494
	.byte	1,7,2,35,2,11
	.byte	'SCRWKP',0,1
	.word	494
	.byte	1,6,2,35,2,11
	.byte	'SCR',0,1
	.word	494
	.byte	1,5,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	494
	.byte	1,4,2,35,2,11
	.byte	'ESR1WKEN',0,1
	.word	494
	.byte	1,3,2,35,2,11
	.byte	'PINAWKEN',0,1
	.word	494
	.byte	1,2,2,35,2,11
	.byte	'PINBWKEN',0,1
	.word	494
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	511
	.byte	4,5,2,35,2,11
	.byte	'ESR0TRIST',0,1
	.word	494
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	494
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_PMSWSTAT_Bits',0,10,214,7,3
	.word	34354
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTATCLR_Bits',0,10,217,7,16,4,11
	.byte	'reserved_0',0,1
	.word	494
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKPCLR',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUNCLR',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'PINAWKPCLR',0,1
	.word	494
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUNCLR',0,1
	.word	494
	.byte	1,2,2,35,0,11
	.byte	'PINBWKPCLR',0,1
	.word	494
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUNCLR',0,1
	.word	494
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	494
	.byte	8,0,2,35,1,11
	.byte	'SCRSTCLR',0,1
	.word	494
	.byte	1,7,2,35,2,11
	.byte	'SCRWKPCLR',0,1
	.word	494
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	511
	.byte	14,0,2,35,2,0,21
	.byte	'Ifx_SCU_PMSWSTATCLR_Bits',0,10,230,7,3
	.word	34850
	.byte	10
	.byte	'_Ifx_SCU_RSTCON2_Bits',0,10,233,7,16,4,11
	.byte	'reserved_0',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'CLRC',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	511
	.byte	10,4,2,35,0,11
	.byte	'CSS0',0,1
	.word	494
	.byte	1,3,2,35,1,11
	.byte	'CSS1',0,1
	.word	494
	.byte	1,2,2,35,1,11
	.byte	'CSS2',0,1
	.word	494
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	494
	.byte	1,0,2,35,1,11
	.byte	'USRINFO',0,2
	.word	511
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_RSTCON2_Bits',0,10,243,7,3
	.word	35163
	.byte	10
	.byte	'_Ifx_SCU_RSTCON_Bits',0,10,246,7,16,4,11
	.byte	'ESR0',0,1
	.word	494
	.byte	2,6,2,35,0,11
	.byte	'ESR1',0,1
	.word	494
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	494
	.byte	2,2,2,35,0,11
	.byte	'SMU',0,1
	.word	494
	.byte	2,0,2,35,0,11
	.byte	'SW',0,1
	.word	494
	.byte	2,6,2,35,1,11
	.byte	'STM0',0,1
	.word	494
	.byte	2,4,2,35,1,11
	.byte	'STM1',0,1
	.word	494
	.byte	2,2,2,35,1,11
	.byte	'STM2',0,1
	.word	494
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	511
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_RSTCON_Bits',0,10,129,8,3
	.word	35372
	.byte	10
	.byte	'_Ifx_SCU_RSTSTAT_Bits',0,10,132,8,16,4,11
	.byte	'ESR0',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'ESR1',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'SMU',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'SW',0,1
	.word	494
	.byte	1,3,2,35,0,11
	.byte	'STM0',0,1
	.word	494
	.byte	1,2,2,35,0,11
	.byte	'STM1',0,1
	.word	494
	.byte	1,1,2,35,0,11
	.byte	'STM2',0,1
	.word	494
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	494
	.byte	8,0,2,35,1,11
	.byte	'PORST',0,1
	.word	494
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	494
	.byte	1,6,2,35,2,11
	.byte	'CB0',0,1
	.word	494
	.byte	1,5,2,35,2,11
	.byte	'CB1',0,1
	.word	494
	.byte	1,4,2,35,2,11
	.byte	'CB3',0,1
	.word	494
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	494
	.byte	2,1,2,35,2,11
	.byte	'EVR13',0,1
	.word	494
	.byte	1,0,2,35,2,11
	.byte	'EVR33',0,1
	.word	494
	.byte	1,7,2,35,3,11
	.byte	'SWD',0,1
	.word	494
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	494
	.byte	2,4,2,35,3,11
	.byte	'STBYR',0,1
	.word	494
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	494
	.byte	3,0,2,35,3,0,21
	.byte	'Ifx_SCU_RSTSTAT_Bits',0,10,155,8,3
	.word	35583
	.byte	10
	.byte	'_Ifx_SCU_SAFECON_Bits',0,10,158,8,16,4,11
	.byte	'HBT',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	471
	.byte	31,0,2,35,0,0,21
	.byte	'Ifx_SCU_SAFECON_Bits',0,10,162,8,3
	.word	36015
	.byte	10
	.byte	'_Ifx_SCU_STSTAT_Bits',0,10,165,8,16,4,11
	.byte	'HWCFG',0,1
	.word	494
	.byte	8,0,2,35,0,11
	.byte	'FTM',0,1
	.word	494
	.byte	7,1,2,35,1,11
	.byte	'MODE',0,1
	.word	494
	.byte	1,0,2,35,1,11
	.byte	'FCBAE',0,1
	.word	494
	.byte	1,7,2,35,2,11
	.byte	'LUDIS',0,1
	.word	494
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	494
	.byte	1,5,2,35,2,11
	.byte	'TRSTL',0,1
	.word	494
	.byte	1,4,2,35,2,11
	.byte	'SPDEN',0,1
	.word	494
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	494
	.byte	3,0,2,35,2,11
	.byte	'RAMINT',0,1
	.word	494
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	494
	.byte	7,0,2,35,3,0,21
	.byte	'Ifx_SCU_STSTAT_Bits',0,10,178,8,3
	.word	36111
	.byte	10
	.byte	'_Ifx_SCU_SWRSTCON_Bits',0,10,181,8,16,4,11
	.byte	'reserved_0',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'SWRSTREQ',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	471
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_SWRSTCON_Bits',0,10,186,8,3
	.word	36371
	.byte	10
	.byte	'_Ifx_SCU_SYSCON_Bits',0,10,189,8,16,4,11
	.byte	'CCTRIG0',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'RAMINTM',0,1
	.word	494
	.byte	2,4,2,35,0,11
	.byte	'SETLUDIS',0,1
	.word	494
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	494
	.byte	3,0,2,35,0,11
	.byte	'DATM',0,1
	.word	494
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,4
	.word	471
	.byte	23,0,2,35,0,0,21
	.byte	'Ifx_SCU_SYSCON_Bits',0,10,198,8,3
	.word	36496
	.byte	10
	.byte	'_Ifx_SCU_TRAPCLR_Bits',0,10,201,8,16,4,11
	.byte	'ESR0T',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	471
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPCLR_Bits',0,10,208,8,3
	.word	36693
	.byte	10
	.byte	'_Ifx_SCU_TRAPDIS_Bits',0,10,211,8,16,4,11
	.byte	'ESR0T',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	471
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPDIS_Bits',0,10,218,8,3
	.word	36846
	.byte	10
	.byte	'_Ifx_SCU_TRAPSET_Bits',0,10,221,8,16,4,11
	.byte	'ESR0T',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	471
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPSET_Bits',0,10,228,8,3
	.word	36999
	.byte	10
	.byte	'_Ifx_SCU_TRAPSTAT_Bits',0,10,231,8,16,4,11
	.byte	'ESR0T',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	471
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPSTAT_Bits',0,10,238,8,3
	.word	37152
	.byte	7
	.byte	'unsigned int',0,4,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON0_Bits',0,10,241,8,16,4,11
	.byte	'ENDINIT',0,4
	.word	37307
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	37307
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	37307
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	37307
	.byte	16,0,2,35,0,0,21
	.byte	'Ifx_SCU_WDTCPU_CON0_Bits',0,10,247,8,3
	.word	37323
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU_CON1_Bits',0,10,250,8,16,4,11
	.byte	'reserved_0',0,1
	.word	494
	.byte	2,6,2,35,0,11
	.byte	'IR0',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	494
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	494
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	494
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	494
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	494
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	494
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	511
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_WDTCPU_CON1_Bits',0,10,134,9,3
	.word	37453
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU_SR_Bits',0,10,137,9,16,4,11
	.byte	'AE',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	494
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	494
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	494
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	494
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	494
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	494
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	511
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_WDTCPU_SR_Bits',0,10,150,9,3
	.word	37691
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON0_Bits',0,10,153,9,16,4,11
	.byte	'ENDINIT',0,4
	.word	37307
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	37307
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	37307
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	37307
	.byte	16,0,2,35,0,0,21
	.byte	'Ifx_SCU_WDTS_CON0_Bits',0,10,159,9,3
	.word	37914
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON1_Bits',0,10,162,9,16,4,11
	.byte	'CLRIRF',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'IR0',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	494
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	494
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	494
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	494
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	494
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	494
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	511
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_WDTS_CON1_Bits',0,10,175,9,3
	.word	38040
	.byte	10
	.byte	'_Ifx_SCU_WDTS_SR_Bits',0,10,178,9,16,4,11
	.byte	'AE',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	494
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	494
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	494
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	494
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	494
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	494
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	511
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_WDTS_SR_Bits',0,10,191,9,3
	.word	38292
	.byte	12,10,199,9,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19817
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ACCEN0',0,10,204,9,3
	.word	38511
	.byte	12,10,207,9,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20374
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ACCEN1',0,10,212,9,3
	.word	38575
	.byte	12,10,215,9,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20451
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ARSTDIS',0,10,220,9,3
	.word	38639
	.byte	12,10,223,9,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20587
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON0',0,10,228,9,3
	.word	38704
	.byte	12,10,231,9,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20867
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON1',0,10,236,9,3
	.word	38769
	.byte	12,10,239,9,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21105
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON2',0,10,244,9,3
	.word	38834
	.byte	12,10,247,9,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21233
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON3',0,10,252,9,3
	.word	38899
	.byte	12,10,255,9,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21476
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON4',0,10,132,10,3
	.word	38964
	.byte	12,10,135,10,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21711
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON5',0,10,140,10,3
	.word	39029
	.byte	12,10,143,10,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21839
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON6',0,10,148,10,3
	.word	39094
	.byte	12,10,151,10,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21939
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON7',0,10,156,10,3
	.word	39159
	.byte	12,10,159,10,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22039
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CHIPID',0,10,164,10,3
	.word	39224
	.byte	12,10,167,10,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22247
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_DTSCON',0,10,172,10,3
	.word	39288
	.byte	12,10,175,10,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22412
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_DTSLIM',0,10,180,10,3
	.word	39352
	.byte	12,10,183,10,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22595
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_DTSSTAT',0,10,188,10,3
	.word	39416
	.byte	12,10,191,10,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22749
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EICR',0,10,196,10,3
	.word	39481
	.byte	12,10,199,10,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23113
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EIFR',0,10,204,10,3
	.word	39543
	.byte	12,10,207,10,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23324
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EMSR',0,10,212,10,3
	.word	39605
	.byte	12,10,215,10,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23576
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ESRCFG',0,10,220,10,3
	.word	39667
	.byte	12,10,223,10,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23694
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ESROCFG',0,10,228,10,3
	.word	39731
	.byte	12,10,231,10,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23805
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVR13CON',0,10,236,10,3
	.word	39796
	.byte	12,10,239,10,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23968
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVR33CON',0,10,244,10,3
	.word	39862
	.byte	12,10,247,10,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24131
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRADCSTAT',0,10,252,10,3
	.word	39928
	.byte	12,10,255,10,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24289
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRDVSTAT',0,10,132,11,3
	.word	39996
	.byte	12,10,135,11,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24454
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRMONCTRL',0,10,140,11,3
	.word	40063
	.byte	12,10,143,11,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24783
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVROSCCTRL',0,10,148,11,3
	.word	40131
	.byte	12,10,151,11,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25004
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVROVMON',0,10,156,11,3
	.word	40199
	.byte	12,10,159,11,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25167
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRRSTCON',0,10,164,11,3
	.word	40265
	.byte	12,10,167,11,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25439
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF1',0,10,172,11,3
	.word	40332
	.byte	12,10,175,11,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25592
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF2',0,10,180,11,3
	.word	40401
	.byte	12,10,183,11,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25748
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF3',0,10,188,11,3
	.word	40470
	.byte	12,10,191,11,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25910
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF4',0,10,196,11,3
	.word	40539
	.byte	12,10,199,11,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26053
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF5',0,10,204,11,3
	.word	40608
	.byte	12,10,207,11,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26218
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF6',0,10,212,11,3
	.word	40677
	.byte	12,10,215,11,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26363
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCTRL1',0,10,220,11,3
	.word	40746
	.byte	12,10,223,11,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26544
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCTRL2',0,10,228,11,3
	.word	40814
	.byte	12,10,231,11,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26718
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCTRL3',0,10,236,11,3
	.word	40882
	.byte	12,10,239,11,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26878
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCTRL4',0,10,244,11,3
	.word	40950
	.byte	12,10,247,11,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27022
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSTAT',0,10,252,11,3
	.word	41018
	.byte	12,10,255,11,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27296
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRTRIM',0,10,132,12,3
	.word	41083
	.byte	12,10,135,12,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27435
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRUVMON',0,10,140,12,3
	.word	41148
	.byte	12,10,143,12,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27598
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EXTCON',0,10,148,12,3
	.word	41214
	.byte	12,10,151,12,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27816
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_FDR',0,10,156,12,3
	.word	41278
	.byte	12,10,159,12,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27979
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_FMR',0,10,164,12,3
	.word	41339
	.byte	12,10,167,12,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28315
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ID',0,10,172,12,3
	.word	41400
	.byte	12,10,175,12,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28422
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_IGCR',0,10,180,12,3
	.word	41460
	.byte	12,10,183,12,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28874
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_IN',0,10,188,12,3
	.word	41522
	.byte	12,10,191,12,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28973
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_IOCR',0,10,196,12,3
	.word	41582
	.byte	12,10,199,12,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29123
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_LBISTCTRL0',0,10,204,12,3
	.word	41644
	.byte	12,10,207,12,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29272
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_LBISTCTRL1',0,10,212,12,3
	.word	41712
	.byte	12,10,215,12,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29433
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_LBISTCTRL2',0,10,220,12,3
	.word	41780
	.byte	12,10,223,12,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29563
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_LCLCON',0,10,228,12,3
	.word	41848
	.byte	12,10,231,12,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29695
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_LCLTEST',0,10,236,12,3
	.word	41912
	.byte	12,10,239,12,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29810
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_MANID',0,10,244,12,3
	.word	41977
	.byte	12,10,247,12,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29921
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_OMR',0,10,252,12,3
	.word	42040
	.byte	12,10,255,12,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30079
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_OSCCON',0,10,132,13,3
	.word	42101
	.byte	12,10,135,13,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30491
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_OUT',0,10,140,13,3
	.word	42165
	.byte	12,10,143,13,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30592
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_OVCCON',0,10,148,13,3
	.word	42226
	.byte	12,10,151,13,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30859
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_OVCENABLE',0,10,156,13,3
	.word	42290
	.byte	12,10,159,13,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30995
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PDISC',0,10,164,13,3
	.word	42357
	.byte	12,10,167,13,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31106
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PDR',0,10,172,13,3
	.word	42420
	.byte	12,10,175,13,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31239
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PDRR',0,10,180,13,3
	.word	42481
	.byte	12,10,183,13,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31442
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLCON0',0,10,188,13,3
	.word	42543
	.byte	12,10,191,13,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31798
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLCON1',0,10,196,13,3
	.word	42608
	.byte	12,10,199,13,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31976
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLCON2',0,10,204,13,3
	.word	42673
	.byte	12,10,207,13,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32076
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLERAYCON0',0,10,212,13,3
	.word	42738
	.byte	12,10,215,13,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32446
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLERAYCON1',0,10,220,13,3
	.word	42807
	.byte	12,10,223,13,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32632
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLERAYSTAT',0,10,228,13,3
	.word	42876
	.byte	12,10,231,13,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32830
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLSTAT',0,10,236,13,3
	.word	42945
	.byte	12,10,239,13,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33063
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMCSR',0,10,244,13,3
	.word	43010
	.byte	12,10,247,13,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33215
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMSWCR0',0,10,252,13,3
	.word	43073
	.byte	12,10,255,13,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33782
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMSWCR1',0,10,132,14,3
	.word	43138
	.byte	12,10,135,14,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34076
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMSWCR2',0,10,140,14,3
	.word	43203
	.byte	12,10,143,14,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34354
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMSWSTAT',0,10,148,14,3
	.word	43268
	.byte	12,10,151,14,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34850
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMSWSTATCLR',0,10,156,14,3
	.word	43334
	.byte	12,10,159,14,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35372
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_RSTCON',0,10,164,14,3
	.word	43403
	.byte	12,10,167,14,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35163
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_RSTCON2',0,10,172,14,3
	.word	43467
	.byte	12,10,175,14,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35583
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_RSTSTAT',0,10,180,14,3
	.word	43532
	.byte	12,10,183,14,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36015
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_SAFECON',0,10,188,14,3
	.word	43597
	.byte	12,10,191,14,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36111
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_STSTAT',0,10,196,14,3
	.word	43662
	.byte	12,10,199,14,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36371
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_SWRSTCON',0,10,204,14,3
	.word	43726
	.byte	12,10,207,14,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36496
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_SYSCON',0,10,212,14,3
	.word	43792
	.byte	12,10,215,14,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36693
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPCLR',0,10,220,14,3
	.word	43856
	.byte	12,10,223,14,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36846
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPDIS',0,10,228,14,3
	.word	43921
	.byte	12,10,231,14,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36999
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPSET',0,10,236,14,3
	.word	43986
	.byte	12,10,239,14,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37152
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPSTAT',0,10,244,14,3
	.word	44051
	.byte	12,10,247,14,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37323
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTCPU_CON0',0,10,252,14,3
	.word	44117
	.byte	12,10,255,14,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37453
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTCPU_CON1',0,10,132,15,3
	.word	44186
	.byte	12,10,135,15,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37691
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTCPU_SR',0,10,140,15,3
	.word	44255
	.byte	12,10,143,15,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37914
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTS_CON0',0,10,148,15,3
	.word	44322
	.byte	12,10,151,15,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38040
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTS_CON1',0,10,156,15,3
	.word	44389
	.byte	12,10,159,15,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38292
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTS_SR',0,10,164,15,3
	.word	44456
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU',0,10,175,15,25,12,13
	.byte	'CON0',0
	.word	44117
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	44186
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	44255
	.byte	4,2,35,8,0,16
	.word	44521
	.byte	21
	.byte	'Ifx_SCU_WDTCPU',0,10,180,15,3
	.word	44584
	.byte	10
	.byte	'_Ifx_SCU_WDTS',0,10,183,15,25,12,13
	.byte	'CON0',0
	.word	44322
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	44389
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	44456
	.byte	4,2,35,8,0,16
	.word	44613
	.byte	21
	.byte	'Ifx_SCU_WDTS',0,10,188,15,3
	.word	44674
	.byte	17,3,83,9,1,18
	.byte	'IfxPort_InputMode_undefined',0,127,18
	.byte	'IfxPort_InputMode_noPullDevice',0,0,18
	.byte	'IfxPort_InputMode_pullDown',0,8,18
	.byte	'IfxPort_InputMode_pullUp',0,16,0,21
	.byte	'IfxPort_InputMode',0,3,89,3
	.word	44701
	.byte	17,3,120,9,1,18
	.byte	'IfxPort_OutputIdx_general',0,128,1,18
	.byte	'IfxPort_OutputIdx_alt1',0,136,1,18
	.byte	'IfxPort_OutputIdx_alt2',0,144,1,18
	.byte	'IfxPort_OutputIdx_alt3',0,152,1,18
	.byte	'IfxPort_OutputIdx_alt4',0,160,1,18
	.byte	'IfxPort_OutputIdx_alt5',0,168,1,18
	.byte	'IfxPort_OutputIdx_alt6',0,176,1,18
	.byte	'IfxPort_OutputIdx_alt7',0,184,1,0,21
	.byte	'IfxPort_OutputIdx',0,3,130,1,3
	.word	44852
	.byte	17,3,134,1,9,1,18
	.byte	'IfxPort_OutputMode_pushPull',0,128,1,18
	.byte	'IfxPort_OutputMode_openDrain',0,192,1,0,21
	.byte	'IfxPort_OutputMode',0,3,138,1,3
	.word	45096
	.byte	17,3,144,1,9,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed1',0,0,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed2',0,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed3',0,2,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed4',0,3,18
	.byte	'IfxPort_PadDriver_lvdsSpeed1',0,4,18
	.byte	'IfxPort_PadDriver_lvdsSpeed2',0,5,18
	.byte	'IfxPort_PadDriver_lvdsSpeed3',0,6,18
	.byte	'IfxPort_PadDriver_lvdsSpeed4',0,7,18
	.byte	'IfxPort_PadDriver_ttlSpeed1',0,8,18
	.byte	'IfxPort_PadDriver_ttlSpeed2',0,9,18
	.byte	'IfxPort_PadDriver_ttlSpeed3',0,10,18
	.byte	'IfxPort_PadDriver_ttlSpeed4',0,11,0,21
	.byte	'IfxPort_PadDriver',0,3,158,1,3
	.word	45194
	.byte	21
	.byte	'IfxPort_State',0,3,178,1,3
	.word	8291
	.byte	23,3,190,1,9,8,13
	.byte	'port',0
	.word	8286
	.byte	4,2,35,0,13
	.byte	'pinIndex',0
	.word	494
	.byte	1,2,35,4,0,21
	.byte	'IfxPort_Pin',0,3,194,1,3
	.word	45659
	.byte	16
	.word	17393
	.byte	3
	.word	45719
	.byte	23,11,59,15,20,13
	.byte	'module',0
	.word	45724
	.byte	4,2,35,0,13
	.byte	'channel',0
	.word	494
	.byte	1,2,35,4,13
	.byte	'pin',0
	.word	45659
	.byte	8,2,35,8,13
	.byte	'select',0
	.word	18219
	.byte	1,2,35,16,0,24
	.word	45729
	.byte	21
	.byte	'IfxDsadc_Dsn_In',0,11,65,3
	.word	45797
	.byte	23,11,68,15,20,13
	.byte	'module',0
	.word	45724
	.byte	4,2,35,0,13
	.byte	'channel',0
	.word	494
	.byte	1,2,35,4,13
	.byte	'pin',0
	.word	45659
	.byte	8,2,35,8,13
	.byte	'select',0
	.word	18219
	.byte	1,2,35,16,0,24
	.word	45826
	.byte	21
	.byte	'IfxDsadc_Dsp_In',0,11,74,3
	.word	45894
	.byte	23,11,77,15,20,13
	.byte	'module',0
	.word	45724
	.byte	4,2,35,0,13
	.byte	'channel',0
	.word	494
	.byte	1,2,35,4,13
	.byte	'pin',0
	.word	45659
	.byte	8,2,35,8,13
	.byte	'select',0
	.word	18219
	.byte	1,2,35,16,0,24
	.word	45923
	.byte	21
	.byte	'IfxDsadc_Cin_In',0,11,83,3
	.word	45991
	.byte	23,11,86,15,20,13
	.byte	'module',0
	.word	45724
	.byte	4,2,35,0,13
	.byte	'channel',0
	.word	494
	.byte	1,2,35,4,13
	.byte	'pin',0
	.word	45659
	.byte	8,2,35,8,13
	.byte	'select',0
	.word	18219
	.byte	1,2,35,16,0,24
	.word	46020
	.byte	21
	.byte	'IfxDsadc_Din_In',0,11,92,3
	.word	46088
	.byte	23,11,95,15,20,13
	.byte	'module',0
	.word	45724
	.byte	4,2,35,0,13
	.byte	'channel',0
	.word	494
	.byte	1,2,35,4,13
	.byte	'pin',0
	.word	45659
	.byte	8,2,35,8,13
	.byte	'select',0
	.word	18219
	.byte	1,2,35,16,0,24
	.word	46117
	.byte	21
	.byte	'IfxDsadc_Itr_In',0,11,101,3
	.word	46185
	.byte	23,11,104,15,16,13
	.byte	'module',0
	.word	45724
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	45659
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	44852
	.byte	1,2,35,12,0,24
	.word	46214
	.byte	21
	.byte	'IfxDsadc_Cgpwm_Out',0,11,109,3
	.word	46265
	.byte	23,11,112,15,16,13
	.byte	'module',0
	.word	45724
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	45659
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	18219
	.byte	1,2,35,12,0,24
	.word	46297
	.byte	21
	.byte	'IfxDsadc_Sg_In',0,11,117,3
	.word	46348
	.byte	23,11,120,15,20,13
	.byte	'module',0
	.word	45724
	.byte	4,2,35,0,13
	.byte	'channel',0
	.word	494
	.byte	1,2,35,4,13
	.byte	'pin',0
	.word	45659
	.byte	8,2,35,8,13
	.byte	'select',0
	.word	44852
	.byte	1,2,35,16,0,24
	.word	46376
	.byte	21
	.byte	'IfxDsadc_Cout_Out',0,11,126,3
	.word	46444
.L124:
	.byte	24
	.word	46214
.L125:
	.byte	24
	.word	46214
.L126:
	.byte	24
	.word	46214
.L127:
	.byte	24
	.word	46214
.L128:
	.byte	24
	.word	46214
.L129:
	.byte	24
	.word	46214
.L130:
	.byte	24
	.word	45923
.L131:
	.byte	24
	.word	45923
.L132:
	.byte	24
	.word	45923
.L133:
	.byte	24
	.word	45923
.L134:
	.byte	24
	.word	45923
.L135:
	.byte	24
	.word	45923
.L136:
	.byte	24
	.word	46376
.L137:
	.byte	24
	.word	46376
.L138:
	.byte	24
	.word	46376
.L139:
	.byte	24
	.word	46376
.L140:
	.byte	24
	.word	46376
.L141:
	.byte	24
	.word	46376
.L142:
	.byte	24
	.word	46376
.L143:
	.byte	24
	.word	46020
.L144:
	.byte	24
	.word	46020
.L145:
	.byte	24
	.word	46020
.L146:
	.byte	24
	.word	46020
.L147:
	.byte	24
	.word	46020
.L148:
	.byte	24
	.word	46020
.L149:
	.byte	24
	.word	45729
.L150:
	.byte	24
	.word	45729
.L151:
	.byte	24
	.word	45729
.L152:
	.byte	24
	.word	45729
.L153:
	.byte	24
	.word	45729
.L154:
	.byte	24
	.word	45729
.L155:
	.byte	24
	.word	45729
.L156:
	.byte	24
	.word	45729
.L157:
	.byte	24
	.word	45729
.L158:
	.byte	24
	.word	45826
.L159:
	.byte	24
	.word	45826
.L160:
	.byte	24
	.word	45826
.L161:
	.byte	24
	.word	45826
.L162:
	.byte	24
	.word	45826
.L163:
	.byte	24
	.word	45826
.L164:
	.byte	24
	.word	45826
.L165:
	.byte	24
	.word	45826
.L166:
	.byte	24
	.word	45826
.L167:
	.byte	24
	.word	46117
.L168:
	.byte	24
	.word	46117
.L169:
	.byte	24
	.word	46117
.L170:
	.byte	24
	.word	46117
.L171:
	.byte	24
	.word	46117
.L172:
	.byte	24
	.word	46117
.L173:
	.byte	24
	.word	46297
.L174:
	.byte	24
	.word	46297
	.byte	24
	.word	46214
	.byte	3
	.word	46730
	.byte	14,24
	.word	46735
	.byte	15,5,0
.L175:
	.byte	14,24
	.word	46740
	.byte	15,0,0,24
	.word	45923
	.byte	3
	.word	46758
	.byte	14,8
	.word	46763
	.byte	15,1,0,14,32
	.word	46768
	.byte	15,3,0
.L176:
	.byte	14,32
	.word	46777
	.byte	15,0,0,24
	.word	46376
	.byte	3
	.word	46795
	.byte	14,12
	.word	46800
	.byte	15,2,0,14,48
	.word	46805
	.byte	15,3,0
.L177:
	.byte	14,48
	.word	46814
	.byte	15,0,0,24
	.word	46020
	.byte	3
	.word	46832
	.byte	14,8
	.word	46837
	.byte	15,1,0,14,32
	.word	46842
	.byte	15,3,0
.L178:
	.byte	14,32
	.word	46851
	.byte	15,0,0,24
	.word	45729
	.byte	3
	.word	46869
	.byte	14,16
	.word	46874
	.byte	15,3,0,14,64
	.word	46879
	.byte	15,3,0
.L179:
	.byte	14,64
	.word	46888
	.byte	15,0,0,24
	.word	45826
	.byte	3
	.word	46906
	.byte	14,16
	.word	46911
	.byte	15,3,0,14,64
	.word	46916
	.byte	15,3,0
.L180:
	.byte	14,64
	.word	46925
	.byte	15,0,0,24
	.word	46117
	.byte	3
	.word	46943
	.byte	14,24
	.word	46948
	.byte	15,5,0,14,96
	.word	46953
	.byte	15,3,0
.L181:
	.byte	14,96
	.word	46962
	.byte	15,0,0,24
	.word	46297
	.byte	3
	.word	46980
	.byte	14,8
	.word	46985
	.byte	15,1,0
.L182:
	.byte	14,8
	.word	46990
	.byte	15,0,0,0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L4:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,21,0,54,15,39,12,0,0,3,15,0,73,19,0,0,4,46,1,3,8,32,13
	.byte	58,15,59,15,57,15,54,15,39,12,0,0,5,5,0,3,8,58,15,59,15,57,15,73,19,0,0,6,11,0,0,0,7,36,0,3,8,11,15,62
	.byte	15,0,0,8,46,1,3,8,32,13,58,15,59,15,57,15,73,19,54,15,39,12,0,0,9,59,0,3,8,0,0,10,19,1,3,8,58,15,59,15
	.byte	57,15,11,15,0,0,11,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,12,23,1,58,15,59,15,57,15,11,15,0,0,13,13
	.byte	0,3,8,73,19,11,15,56,9,0,0,14,1,1,11,15,73,19,0,0,15,33,0,47,15,0,0,16,53,0,73,19,0,0,17,4,1,58,15,59
	.byte	15,57,15,11,15,0,0,18,40,0,3,8,28,13,0,0,19,46,1,49,19,0,0,20,5,0,49,19,0,0,21,22,0,3,8,58,15,59,15,57
	.byte	15,73,19,0,0,22,21,0,54,15,0,0,23,19,1,58,15,59,15,57,15,11,15,0,0,24,38,0,73,19,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L5:
	.word	.L184-.L183
.L183:
	.half	3
	.word	.L186-.L185
.L185:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg',0
	.byte	0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_Intrinsics.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Port\\Std\\IfxPort.h',0
	.byte	0,0,0
	.byte	'IfxPort_regdef.h',0,1,0,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxDsadc_PinMap.c',0,0,0,0
	.byte	'IfxDsadc_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Platform_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Ifx_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxScu_cfg.h',0
	.byte	0,0,0
	.byte	'IfxScu_regdef.h',0,1,0,0
	.byte	'..\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\_PinMap\\IfxDsadc_PinMap.h',0,0,0,0,0
.L186:
.L184:
	.sdecl	'.debug_info',debug,cluster('IfxDsadc_CGPWMN_P00_5_OUT')
	.sect	'.debug_info'
.L6:
	.word	278
	.half	3
	.word	.L7
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxDsadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxDsadc_CGPWMN_P00_5_OUT',0,5,48,20
	.word	.L124
	.byte	1,5,3
	.word	IfxDsadc_CGPWMN_P00_5_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxDsadc_CGPWMN_P00_5_OUT')
	.sect	'.debug_abbrev'
.L7:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxDsadc_CGPWMN_P02_0_OUT')
	.sect	'.debug_info'
.L8:
	.word	278
	.half	3
	.word	.L9
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxDsadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxDsadc_CGPWMN_P02_0_OUT',0,5,49,20
	.word	.L125
	.byte	1,5,3
	.word	IfxDsadc_CGPWMN_P02_0_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxDsadc_CGPWMN_P02_0_OUT')
	.sect	'.debug_abbrev'
.L9:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxDsadc_CGPWMN_P33_11_OUT')
	.sect	'.debug_info'
.L10:
	.word	279
	.half	3
	.word	.L11
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxDsadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxDsadc_CGPWMN_P33_11_OUT',0,5,50,20
	.word	.L126
	.byte	1,5,3
	.word	IfxDsadc_CGPWMN_P33_11_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxDsadc_CGPWMN_P33_11_OUT')
	.sect	'.debug_abbrev'
.L11:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxDsadc_CGPWMP_P00_6_OUT')
	.sect	'.debug_info'
.L12:
	.word	278
	.half	3
	.word	.L13
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxDsadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxDsadc_CGPWMP_P00_6_OUT',0,5,51,20
	.word	.L127
	.byte	1,5,3
	.word	IfxDsadc_CGPWMP_P00_6_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxDsadc_CGPWMP_P00_6_OUT')
	.sect	'.debug_abbrev'
.L13:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxDsadc_CGPWMP_P02_1_OUT')
	.sect	'.debug_info'
.L14:
	.word	278
	.half	3
	.word	.L15
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxDsadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxDsadc_CGPWMP_P02_1_OUT',0,5,52,20
	.word	.L128
	.byte	1,5,3
	.word	IfxDsadc_CGPWMP_P02_1_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxDsadc_CGPWMP_P02_1_OUT')
	.sect	'.debug_abbrev'
.L15:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxDsadc_CGPWMP_P33_12_OUT')
	.sect	'.debug_info'
.L16:
	.word	279
	.half	3
	.word	.L17
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxDsadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxDsadc_CGPWMP_P33_12_OUT',0,5,53,20
	.word	.L129
	.byte	1,5,3
	.word	IfxDsadc_CGPWMP_P33_12_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxDsadc_CGPWMP_P33_12_OUT')
	.sect	'.debug_abbrev'
.L17:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxDsadc_CIN0A_P00_1_IN')
	.sect	'.debug_info'
.L18:
	.word	276
	.half	3
	.word	.L19
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxDsadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxDsadc_CIN0A_P00_1_IN',0,5,54,17
	.word	.L130
	.byte	1,5,3
	.word	IfxDsadc_CIN0A_P00_1_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxDsadc_CIN0A_P00_1_IN')
	.sect	'.debug_abbrev'
.L19:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxDsadc_CIN0B_P33_5_IN')
	.sect	'.debug_info'
.L20:
	.word	276
	.half	3
	.word	.L21
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxDsadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxDsadc_CIN0B_P33_5_IN',0,5,55,17
	.word	.L131
	.byte	1,5,3
	.word	IfxDsadc_CIN0B_P33_5_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxDsadc_CIN0B_P33_5_IN')
	.sect	'.debug_abbrev'
.L21:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxDsadc_CIN2A_P00_5_IN')
	.sect	'.debug_info'
.L22:
	.word	276
	.half	3
	.word	.L23
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxDsadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxDsadc_CIN2A_P00_5_IN',0,5,56,17
	.word	.L132
	.byte	1,5,3
	.word	IfxDsadc_CIN2A_P00_5_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxDsadc_CIN2A_P00_5_IN')
	.sect	'.debug_abbrev'
.L23:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxDsadc_CIN2B_P33_1_IN')
	.sect	'.debug_info'
.L24:
	.word	276
	.half	3
	.word	.L25
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxDsadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxDsadc_CIN2B_P33_1_IN',0,5,57,17
	.word	.L133
	.byte	1,5,3
	.word	IfxDsadc_CIN2B_P33_1_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxDsadc_CIN2B_P33_1_IN')
	.sect	'.debug_abbrev'
.L25:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxDsadc_CIN3A_P00_3_IN')
	.sect	'.debug_info'
.L26:
	.word	276
	.half	3
	.word	.L27
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxDsadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxDsadc_CIN3A_P00_3_IN',0,5,58,17
	.word	.L134
	.byte	1,5,3
	.word	IfxDsadc_CIN3A_P00_3_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxDsadc_CIN3A_P00_3_IN')
	.sect	'.debug_abbrev'
.L27:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxDsadc_CIN3B_P02_7_IN')
	.sect	'.debug_info'
.L28:
	.word	276
	.half	3
	.word	.L29
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxDsadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxDsadc_CIN3B_P02_7_IN',0,5,59,17
	.word	.L135
	.byte	1,5,3
	.word	IfxDsadc_CIN3B_P02_7_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxDsadc_CIN3B_P02_7_IN')
	.sect	'.debug_abbrev'
.L29:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxDsadc_COUT0_P00_11_OUT')
	.sect	'.debug_info'
.L30:
	.word	278
	.half	3
	.word	.L31
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxDsadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxDsadc_COUT0_P00_11_OUT',0,5,60,19
	.word	.L136
	.byte	1,5,3
	.word	IfxDsadc_COUT0_P00_11_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxDsadc_COUT0_P00_11_OUT')
	.sect	'.debug_abbrev'
.L31:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxDsadc_COUT0_P00_1_OUT')
	.sect	'.debug_info'
.L32:
	.word	277
	.half	3
	.word	.L33
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxDsadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxDsadc_COUT0_P00_1_OUT',0,5,61,19
	.word	.L137
	.byte	1,5,3
	.word	IfxDsadc_COUT0_P00_1_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxDsadc_COUT0_P00_1_OUT')
	.sect	'.debug_abbrev'
.L33:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxDsadc_COUT0_P33_5_OUT')
	.sect	'.debug_info'
.L34:
	.word	277
	.half	3
	.word	.L35
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxDsadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxDsadc_COUT0_P33_5_OUT',0,5,62,19
	.word	.L138
	.byte	1,5,3
	.word	IfxDsadc_COUT0_P33_5_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxDsadc_COUT0_P33_5_OUT')
	.sect	'.debug_abbrev'
.L35:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxDsadc_COUT2_P00_5_OUT')
	.sect	'.debug_info'
.L36:
	.word	277
	.half	3
	.word	.L37
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxDsadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxDsadc_COUT2_P00_5_OUT',0,5,63,19
	.word	.L139
	.byte	1,5,3
	.word	IfxDsadc_COUT2_P00_5_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxDsadc_COUT2_P00_5_OUT')
	.sect	'.debug_abbrev'
.L37:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxDsadc_COUT2_P33_1_OUT')
	.sect	'.debug_info'
.L38:
	.word	277
	.half	3
	.word	.L39
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxDsadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxDsadc_COUT2_P33_1_OUT',0,5,64,19
	.word	.L140
	.byte	1,5,3
	.word	IfxDsadc_COUT2_P33_1_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxDsadc_COUT2_P33_1_OUT')
	.sect	'.debug_abbrev'
.L39:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxDsadc_COUT3_P00_3_OUT')
	.sect	'.debug_info'
.L40:
	.word	277
	.half	3
	.word	.L41
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxDsadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxDsadc_COUT3_P00_3_OUT',0,5,65,19
	.word	.L141
	.byte	1,5,3
	.word	IfxDsadc_COUT3_P00_3_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxDsadc_COUT3_P00_3_OUT')
	.sect	'.debug_abbrev'
.L41:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxDsadc_COUT3_P02_7_OUT')
	.sect	'.debug_info'
.L42:
	.word	277
	.half	3
	.word	.L43
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxDsadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxDsadc_COUT3_P02_7_OUT',0,5,66,19
	.word	.L142
	.byte	1,5,3
	.word	IfxDsadc_COUT3_P02_7_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxDsadc_COUT3_P02_7_OUT')
	.sect	'.debug_abbrev'
.L43:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxDsadc_DIN0A_P00_2_IN')
	.sect	'.debug_info'
.L44:
	.word	276
	.half	3
	.word	.L45
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxDsadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxDsadc_DIN0A_P00_2_IN',0,5,67,17
	.word	.L143
	.byte	1,5,3
	.word	IfxDsadc_DIN0A_P00_2_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxDsadc_DIN0A_P00_2_IN')
	.sect	'.debug_abbrev'
.L45:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxDsadc_DIN0B_P33_6_IN')
	.sect	'.debug_info'
.L46:
	.word	276
	.half	3
	.word	.L47
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxDsadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxDsadc_DIN0B_P33_6_IN',0,5,68,17
	.word	.L144
	.byte	1,5,3
	.word	IfxDsadc_DIN0B_P33_6_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxDsadc_DIN0B_P33_6_IN')
	.sect	'.debug_abbrev'
.L47:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxDsadc_DIN2A_P00_6_IN')
	.sect	'.debug_info'
.L48:
	.word	276
	.half	3
	.word	.L49
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxDsadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxDsadc_DIN2A_P00_6_IN',0,5,69,17
	.word	.L145
	.byte	1,5,3
	.word	IfxDsadc_DIN2A_P00_6_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxDsadc_DIN2A_P00_6_IN')
	.sect	'.debug_abbrev'
.L49:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxDsadc_DIN2B_P33_2_IN')
	.sect	'.debug_info'
.L50:
	.word	276
	.half	3
	.word	.L51
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxDsadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxDsadc_DIN2B_P33_2_IN',0,5,70,17
	.word	.L146
	.byte	1,5,3
	.word	IfxDsadc_DIN2B_P33_2_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxDsadc_DIN2B_P33_2_IN')
	.sect	'.debug_abbrev'
.L51:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxDsadc_DIN3A_P00_4_IN')
	.sect	'.debug_info'
.L52:
	.word	276
	.half	3
	.word	.L53
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxDsadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxDsadc_DIN3A_P00_4_IN',0,5,71,17
	.word	.L147
	.byte	1,5,3
	.word	IfxDsadc_DIN3A_P00_4_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxDsadc_DIN3A_P00_4_IN')
	.sect	'.debug_abbrev'
.L53:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxDsadc_DIN3B_P02_8_IN')
	.sect	'.debug_info'
.L54:
	.word	276
	.half	3
	.word	.L55
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxDsadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxDsadc_DIN3B_P02_8_IN',0,5,72,17
	.word	.L148
	.byte	1,5,3
	.word	IfxDsadc_DIN3B_P02_8_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxDsadc_DIN3B_P02_8_IN')
	.sect	'.debug_abbrev'
.L55:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxDsadc_DS0NA_AN3_IN')
	.sect	'.debug_info'
.L56:
	.word	274
	.half	3
	.word	.L57
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxDsadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxDsadc_DS0NA_AN3_IN',0,5,73,17
	.word	.L149
	.byte	1,5,3
	.word	IfxDsadc_DS0NA_AN3_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxDsadc_DS0NA_AN3_IN')
	.sect	'.debug_abbrev'
.L57:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxDsadc_DS0NB_AN1_IN')
	.sect	'.debug_info'
.L58:
	.word	274
	.half	3
	.word	.L59
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxDsadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxDsadc_DS0NB_AN1_IN',0,5,74,17
	.word	.L150
	.byte	1,5,3
	.word	IfxDsadc_DS0NB_AN1_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxDsadc_DS0NB_AN1_IN')
	.sect	'.debug_abbrev'
.L59:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxDsadc_DS2NA_AN21_IN')
	.sect	'.debug_info'
.L60:
	.word	275
	.half	3
	.word	.L61
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxDsadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxDsadc_DS2NA_AN21_IN',0,5,75,17
	.word	.L151
	.byte	1,5,3
	.word	IfxDsadc_DS2NA_AN21_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxDsadc_DS2NA_AN21_IN')
	.sect	'.debug_abbrev'
.L61:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxDsadc_DS3NA_AN37_IN')
	.sect	'.debug_info'
.L62:
	.word	275
	.half	3
	.word	.L63
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxDsadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxDsadc_DS3NA_AN37_IN',0,5,76,17
	.word	.L152
	.byte	1,5,3
	.word	IfxDsadc_DS3NA_AN37_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxDsadc_DS3NA_AN37_IN')
	.sect	'.debug_abbrev'
.L63:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxDsadc_DS3NA_P40_7_IN')
	.sect	'.debug_info'
.L64:
	.word	276
	.half	3
	.word	.L65
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxDsadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxDsadc_DS3NA_P40_7_IN',0,5,77,17
	.word	.L153
	.byte	1,5,3
	.word	IfxDsadc_DS3NA_P40_7_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxDsadc_DS3NA_P40_7_IN')
	.sect	'.debug_abbrev'
.L65:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxDsadc_DS3NB_AN39_IN')
	.sect	'.debug_info'
.L66:
	.word	275
	.half	3
	.word	.L67
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxDsadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxDsadc_DS3NB_AN39_IN',0,5,78,17
	.word	.L154
	.byte	1,5,3
	.word	IfxDsadc_DS3NB_AN39_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxDsadc_DS3NB_AN39_IN')
	.sect	'.debug_abbrev'
.L67:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxDsadc_DS3NB_P40_9_IN')
	.sect	'.debug_info'
.L68:
	.word	276
	.half	3
	.word	.L69
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxDsadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxDsadc_DS3NB_P40_9_IN',0,5,79,17
	.word	.L155
	.byte	1,5,3
	.word	IfxDsadc_DS3NB_P40_9_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxDsadc_DS3NB_P40_9_IN')
	.sect	'.debug_abbrev'
.L69:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxDsadc_DS3NC_AN45_IN')
	.sect	'.debug_info'
.L70:
	.word	275
	.half	3
	.word	.L71
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxDsadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxDsadc_DS3NC_AN45_IN',0,5,80,17
	.word	.L156
	.byte	1,5,3
	.word	IfxDsadc_DS3NC_AN45_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxDsadc_DS3NC_AN45_IN')
	.sect	'.debug_abbrev'
.L71:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxDsadc_DS3ND_AN47_IN')
	.sect	'.debug_info'
.L72:
	.word	275
	.half	3
	.word	.L73
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxDsadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxDsadc_DS3ND_AN47_IN',0,5,81,17
	.word	.L157
	.byte	1,5,3
	.word	IfxDsadc_DS3ND_AN47_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxDsadc_DS3ND_AN47_IN')
	.sect	'.debug_abbrev'
.L73:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxDsadc_DS0PA_AN2_IN')
	.sect	'.debug_info'
.L74:
	.word	274
	.half	3
	.word	.L75
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxDsadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxDsadc_DS0PA_AN2_IN',0,5,82,17
	.word	.L158
	.byte	1,5,3
	.word	IfxDsadc_DS0PA_AN2_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxDsadc_DS0PA_AN2_IN')
	.sect	'.debug_abbrev'
.L75:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxDsadc_DS0PB_AN0_IN')
	.sect	'.debug_info'
.L76:
	.word	274
	.half	3
	.word	.L77
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxDsadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxDsadc_DS0PB_AN0_IN',0,5,83,17
	.word	.L159
	.byte	1,5,3
	.word	IfxDsadc_DS0PB_AN0_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxDsadc_DS0PB_AN0_IN')
	.sect	'.debug_abbrev'
.L77:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxDsadc_DS2PA_AN20_IN')
	.sect	'.debug_info'
.L78:
	.word	275
	.half	3
	.word	.L79
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxDsadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxDsadc_DS2PA_AN20_IN',0,5,84,17
	.word	.L160
	.byte	1,5,3
	.word	IfxDsadc_DS2PA_AN20_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxDsadc_DS2PA_AN20_IN')
	.sect	'.debug_abbrev'
.L79:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxDsadc_DS3PA_AN36_IN')
	.sect	'.debug_info'
.L80:
	.word	275
	.half	3
	.word	.L81
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxDsadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxDsadc_DS3PA_AN36_IN',0,5,85,17
	.word	.L161
	.byte	1,5,3
	.word	IfxDsadc_DS3PA_AN36_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxDsadc_DS3PA_AN36_IN')
	.sect	'.debug_abbrev'
.L81:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxDsadc_DS3PA_P40_6_IN')
	.sect	'.debug_info'
.L82:
	.word	276
	.half	3
	.word	.L83
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxDsadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxDsadc_DS3PA_P40_6_IN',0,5,86,17
	.word	.L162
	.byte	1,5,3
	.word	IfxDsadc_DS3PA_P40_6_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxDsadc_DS3PA_P40_6_IN')
	.sect	'.debug_abbrev'
.L83:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxDsadc_DS3PB_AN38_IN')
	.sect	'.debug_info'
.L84:
	.word	275
	.half	3
	.word	.L85
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxDsadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxDsadc_DS3PB_AN38_IN',0,5,87,17
	.word	.L163
	.byte	1,5,3
	.word	IfxDsadc_DS3PB_AN38_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxDsadc_DS3PB_AN38_IN')
	.sect	'.debug_abbrev'
.L85:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxDsadc_DS3PB_P40_8_IN')
	.sect	'.debug_info'
.L86:
	.word	276
	.half	3
	.word	.L87
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxDsadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxDsadc_DS3PB_P40_8_IN',0,5,88,17
	.word	.L164
	.byte	1,5,3
	.word	IfxDsadc_DS3PB_P40_8_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxDsadc_DS3PB_P40_8_IN')
	.sect	'.debug_abbrev'
.L87:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxDsadc_DS3PC_AN44_IN')
	.sect	'.debug_info'
.L88:
	.word	275
	.half	3
	.word	.L89
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxDsadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxDsadc_DS3PC_AN44_IN',0,5,89,17
	.word	.L165
	.byte	1,5,3
	.word	IfxDsadc_DS3PC_AN44_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxDsadc_DS3PC_AN44_IN')
	.sect	'.debug_abbrev'
.L89:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxDsadc_DS3PD_AN46_IN')
	.sect	'.debug_info'
.L90:
	.word	275
	.half	3
	.word	.L91
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxDsadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxDsadc_DS3PD_AN46_IN',0,5,90,17
	.word	.L166
	.byte	1,5,3
	.word	IfxDsadc_DS3PD_AN46_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxDsadc_DS3PD_AN46_IN')
	.sect	'.debug_abbrev'
.L91:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxDsadc_ITR0E_P33_0_IN')
	.sect	'.debug_info'
.L92:
	.word	276
	.half	3
	.word	.L93
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxDsadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxDsadc_ITR0E_P33_0_IN',0,5,91,17
	.word	.L167
	.byte	1,5,3
	.word	IfxDsadc_ITR0E_P33_0_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxDsadc_ITR0E_P33_0_IN')
	.sect	'.debug_abbrev'
.L93:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxDsadc_ITR0F_P33_4_IN')
	.sect	'.debug_info'
.L94:
	.word	276
	.half	3
	.word	.L95
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxDsadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxDsadc_ITR0F_P33_4_IN',0,5,92,17
	.word	.L168
	.byte	1,5,3
	.word	IfxDsadc_ITR0F_P33_4_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxDsadc_ITR0F_P33_4_IN')
	.sect	'.debug_abbrev'
.L95:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxDsadc_ITR2E_P33_2_IN')
	.sect	'.debug_info'
.L96:
	.word	276
	.half	3
	.word	.L97
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxDsadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxDsadc_ITR2E_P33_2_IN',0,5,93,17
	.word	.L169
	.byte	1,5,3
	.word	IfxDsadc_ITR2E_P33_2_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxDsadc_ITR2E_P33_2_IN')
	.sect	'.debug_abbrev'
.L97:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxDsadc_ITR2F_P33_6_IN')
	.sect	'.debug_info'
.L98:
	.word	276
	.half	3
	.word	.L99
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxDsadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxDsadc_ITR2F_P33_6_IN',0,5,94,17
	.word	.L170
	.byte	1,5,3
	.word	IfxDsadc_ITR2F_P33_6_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxDsadc_ITR2F_P33_6_IN')
	.sect	'.debug_abbrev'
.L99:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxDsadc_ITR3E_P02_8_IN')
	.sect	'.debug_info'
.L100:
	.word	276
	.half	3
	.word	.L101
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxDsadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxDsadc_ITR3E_P02_8_IN',0,5,95,17
	.word	.L171
	.byte	1,5,3
	.word	IfxDsadc_ITR3E_P02_8_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxDsadc_ITR3E_P02_8_IN')
	.sect	'.debug_abbrev'
.L101:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxDsadc_ITR3F_P00_9_IN')
	.sect	'.debug_info'
.L102:
	.word	276
	.half	3
	.word	.L103
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxDsadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxDsadc_ITR3F_P00_9_IN',0,5,96,17
	.word	.L172
	.byte	1,5,3
	.word	IfxDsadc_ITR3F_P00_9_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxDsadc_ITR3F_P00_9_IN')
	.sect	'.debug_abbrev'
.L103:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxDsadc_SGNA_P00_4_IN')
	.sect	'.debug_info'
.L104:
	.word	275
	.half	3
	.word	.L105
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxDsadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxDsadc_SGNA_P00_4_IN',0,5,97,16
	.word	.L173
	.byte	1,5,3
	.word	IfxDsadc_SGNA_P00_4_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxDsadc_SGNA_P00_4_IN')
	.sect	'.debug_abbrev'
.L105:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxDsadc_SGNB_P33_13_IN')
	.sect	'.debug_info'
.L106:
	.word	276
	.half	3
	.word	.L107
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxDsadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxDsadc_SGNB_P33_13_IN',0,5,98,16
	.word	.L174
	.byte	1,5,3
	.word	IfxDsadc_SGNB_P33_13_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxDsadc_SGNB_P33_13_IN')
	.sect	'.debug_abbrev'
.L107:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxDsadc_Cgpwm_Out_pinTable')
	.sect	'.debug_info'
.L108:
	.word	280
	.half	3
	.word	.L109
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxDsadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxDsadc_Cgpwm_Out_pinTable',0,5,101,27
	.word	.L175
	.byte	1,5,3
	.word	IfxDsadc_Cgpwm_Out_pinTable
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxDsadc_Cgpwm_Out_pinTable')
	.sect	'.debug_abbrev'
.L109:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxDsadc_Cin_In_pinTable')
	.sect	'.debug_info'
.L110:
	.word	277
	.half	3
	.word	.L111
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxDsadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxDsadc_Cin_In_pinTable',0,5,112,24
	.word	.L176
	.byte	1,5,3
	.word	IfxDsadc_Cin_In_pinTable
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxDsadc_Cin_In_pinTable')
	.sect	'.debug_abbrev'
.L111:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxDsadc_Cout_Out_pinTable')
	.sect	'.debug_info'
.L112:
	.word	280
	.half	3
	.word	.L113
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxDsadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxDsadc_Cout_Out_pinTable',0,5,133,1,26
	.word	.L177
	.byte	1,5,3
	.word	IfxDsadc_Cout_Out_pinTable
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxDsadc_Cout_Out_pinTable')
	.sect	'.debug_abbrev'
.L113:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxDsadc_Din_In_pinTable')
	.sect	'.debug_info'
.L114:
	.word	278
	.half	3
	.word	.L115
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxDsadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxDsadc_Din_In_pinTable',0,5,158,1,24
	.word	.L178
	.byte	1,5,3
	.word	IfxDsadc_Din_In_pinTable
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxDsadc_Din_In_pinTable')
	.sect	'.debug_abbrev'
.L115:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxDsadc_Dsn_In_pinTable')
	.sect	'.debug_info'
.L116:
	.word	278
	.half	3
	.word	.L117
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxDsadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxDsadc_Dsn_In_pinTable',0,5,179,1,24
	.word	.L179
	.byte	1,5,3
	.word	IfxDsadc_Dsn_In_pinTable
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxDsadc_Dsn_In_pinTable')
	.sect	'.debug_abbrev'
.L117:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxDsadc_Dsp_In_pinTable')
	.sect	'.debug_info'
.L118:
	.word	278
	.half	3
	.word	.L119
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxDsadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxDsadc_Dsp_In_pinTable',0,5,208,1,24
	.word	.L180
	.byte	1,5,3
	.word	IfxDsadc_Dsp_In_pinTable
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxDsadc_Dsp_In_pinTable')
	.sect	'.debug_abbrev'
.L119:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxDsadc_Itr_In_pinTable')
	.sect	'.debug_info'
.L120:
	.word	278
	.half	3
	.word	.L121
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxDsadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxDsadc_Itr_In_pinTable',0,5,237,1,24
	.word	.L181
	.byte	1,5,3
	.word	IfxDsadc_Itr_In_pinTable
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxDsadc_Itr_In_pinTable')
	.sect	'.debug_abbrev'
.L121:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxDsadc_Sg_In_pinTable')
	.sect	'.debug_info'
.L122:
	.word	277
	.half	3
	.word	.L123
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxDsadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxDsadc_Sg_In_pinTable',0,5,146,2,23
	.word	.L182
	.byte	1,5,3
	.word	IfxDsadc_Sg_In_pinTable
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxDsadc_Sg_In_pinTable')
	.sect	'.debug_abbrev'
.L123:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	; Module end
