	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc24644a --dep-file=IfxPsi5s_PinMap.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPsi5s_PinMap.src ../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPsi5s_PinMap.c"
	.compiler_name		"ctc"
	;source	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPsi5s_PinMap.c'

	
$TC16X
	
	.sdecl	'.rodata.IfxPsi5s_PinMap.IfxPsi5s_CLK_P02_4_OUT',data,rom,cluster('IfxPsi5s_CLK_P02_4_OUT')
	.sect	'.rodata.IfxPsi5s_PinMap.IfxPsi5s_CLK_P02_4_OUT'
	.global	IfxPsi5s_CLK_P02_4_OUT
	.align	4
IfxPsi5s_CLK_P02_4_OUT:	.type	object
	.size	IfxPsi5s_CLK_P02_4_OUT,16
	.word	-268406784,-268197376
	.byte	4
	.space	3
	.byte	160
	.space	3
	.sdecl	'.rodata.IfxPsi5s_PinMap.IfxPsi5s_CLK_P33_10_OUT',data,rom,cluster('IfxPsi5s_CLK_P33_10_OUT')
	.sect	'.rodata.IfxPsi5s_PinMap.IfxPsi5s_CLK_P33_10_OUT'
	.global	IfxPsi5s_CLK_P33_10_OUT
	.align	4
IfxPsi5s_CLK_P33_10_OUT:	.type	object
	.size	IfxPsi5s_CLK_P33_10_OUT,16
	.word	-268406784,-268184832
	.byte	10
	.space	3
	.byte	168
	.space	3
	.sdecl	'.rodata.IfxPsi5s_PinMap.IfxPsi5s_RXA_P00_3_IN',data,rom,cluster('IfxPsi5s_RXA_P00_3_IN')
	.sect	'.rodata.IfxPsi5s_PinMap.IfxPsi5s_RXA_P00_3_IN'
	.global	IfxPsi5s_RXA_P00_3_IN
	.align	4
IfxPsi5s_RXA_P00_3_IN:	.type	object
	.size	IfxPsi5s_RXA_P00_3_IN,16
	.word	-268406784,-268197888
	.byte	3
	.space	7
	.sdecl	'.rodata.IfxPsi5s_PinMap.IfxPsi5s_RXB_P02_5_IN',data,rom,cluster('IfxPsi5s_RXB_P02_5_IN')
	.sect	'.rodata.IfxPsi5s_PinMap.IfxPsi5s_RXB_P02_5_IN'
	.global	IfxPsi5s_RXB_P02_5_IN
	.align	4
IfxPsi5s_RXB_P02_5_IN:	.type	object
	.size	IfxPsi5s_RXB_P02_5_IN,16
	.word	-268406784,-268197376
	.byte	5
	.space	3
	.byte	1
	.space	3
	.sdecl	'.rodata.IfxPsi5s_PinMap.IfxPsi5s_RXC_P33_5_IN',data,rom,cluster('IfxPsi5s_RXC_P33_5_IN')
	.sect	'.rodata.IfxPsi5s_PinMap.IfxPsi5s_RXC_P33_5_IN'
	.global	IfxPsi5s_RXC_P33_5_IN
	.align	4
IfxPsi5s_RXC_P33_5_IN:	.type	object
	.size	IfxPsi5s_RXC_P33_5_IN,16
	.word	-268406784,-268184832
	.byte	5
	.space	3
	.byte	2
	.space	3
	.sdecl	'.rodata.IfxPsi5s_PinMap.IfxPsi5s_TX_P00_4_OUT',data,rom,cluster('IfxPsi5s_TX_P00_4_OUT')
	.sect	'.rodata.IfxPsi5s_PinMap.IfxPsi5s_TX_P00_4_OUT'
	.global	IfxPsi5s_TX_P00_4_OUT
	.align	4
IfxPsi5s_TX_P00_4_OUT:	.type	object
	.size	IfxPsi5s_TX_P00_4_OUT,16
	.word	-268406784,-268197888
	.byte	4
	.space	3
	.byte	144
	.space	3
	.sdecl	'.rodata.IfxPsi5s_PinMap.IfxPsi5s_TX_P02_6_OUT',data,rom,cluster('IfxPsi5s_TX_P02_6_OUT')
	.sect	'.rodata.IfxPsi5s_PinMap.IfxPsi5s_TX_P02_6_OUT'
	.global	IfxPsi5s_TX_P02_6_OUT
	.align	4
IfxPsi5s_TX_P02_6_OUT:	.type	object
	.size	IfxPsi5s_TX_P02_6_OUT,16
	.word	-268406784,-268197376
	.byte	6
	.space	3
	.byte	144
	.space	3
	.sdecl	'.rodata.IfxPsi5s_PinMap.IfxPsi5s_TX_P33_6_OUT',data,rom,cluster('IfxPsi5s_TX_P33_6_OUT')
	.sect	'.rodata.IfxPsi5s_PinMap.IfxPsi5s_TX_P33_6_OUT'
	.global	IfxPsi5s_TX_P33_6_OUT
	.align	4
IfxPsi5s_TX_P33_6_OUT:	.type	object
	.size	IfxPsi5s_TX_P33_6_OUT,16
	.word	-268406784,-268184832
	.byte	6
	.space	3
	.byte	184
	.space	3
	.sdecl	'.data.IfxPsi5s_PinMap.IfxPsi5s_Clk_Out_pinTable',data,cluster('IfxPsi5s_Clk_Out_pinTable')
	.sect	'.data.IfxPsi5s_PinMap.IfxPsi5s_Clk_Out_pinTable'
	.global	IfxPsi5s_Clk_Out_pinTable
	.align	4
IfxPsi5s_Clk_Out_pinTable:	.type	object
	.size	IfxPsi5s_Clk_Out_pinTable,8
	.word	IfxPsi5s_CLK_P02_4_OUT,IfxPsi5s_CLK_P33_10_OUT
	.sdecl	'.data.IfxPsi5s_PinMap.IfxPsi5s_Rx_In_pinTable',data,cluster('IfxPsi5s_Rx_In_pinTable')
	.sect	'.data.IfxPsi5s_PinMap.IfxPsi5s_Rx_In_pinTable'
	.global	IfxPsi5s_Rx_In_pinTable
	.align	4
IfxPsi5s_Rx_In_pinTable:	.type	object
	.size	IfxPsi5s_Rx_In_pinTable,12
	.word	IfxPsi5s_RXA_P00_3_IN,IfxPsi5s_RXB_P02_5_IN,IfxPsi5s_RXC_P33_5_IN
	.sdecl	'.data.IfxPsi5s_PinMap.IfxPsi5s_Tx_Out_pinTable',data,cluster('IfxPsi5s_Tx_Out_pinTable')
	.sect	'.data.IfxPsi5s_PinMap.IfxPsi5s_Tx_Out_pinTable'
	.global	IfxPsi5s_Tx_Out_pinTable
	.align	4
IfxPsi5s_Tx_Out_pinTable:	.type	object
	.size	IfxPsi5s_Tx_Out_pinTable,12
	.word	IfxPsi5s_TX_P00_4_OUT,IfxPsi5s_TX_P02_6_OUT,IfxPsi5s_TX_P33_6_OUT
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L3:
	.word	50677
	.half	3
	.word	.L4
	.byte	4
.L2:
	.byte	1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPsi5s_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L5
	.byte	2,1,1,3
	.word	239
	.byte	4
	.byte	'Ifx__jump_and_link',0,3,1,61,17,1,1,5
	.byte	'fun',0,1,61,43
	.word	242
	.byte	6,0,7
	.byte	'__fract',0,4,128,1,7
	.byte	'float',0,4,4,8
	.byte	'Ifx__float_to_fract',0,3,1,152,2,18
	.word	287
	.byte	1,1,5
	.byte	'a',0,1,152,2,44
	.word	299
	.byte	6,0,7
	.byte	'unsigned long long int',0,8,7,9
	.byte	'void',0,3
	.word	379
	.byte	8
	.byte	'__ld64',0,3,2,135,1,19
	.word	353
	.byte	1,1,5
	.byte	'addr',0,2,135,1,32
	.word	385
	.byte	6,0,4
	.byte	'__st64',0,3,2,143,1,17,1,1,5
	.byte	'addr',0,2,143,1,30
	.word	385
	.byte	5
	.byte	'value',0,2,143,1,43
	.word	353
	.byte	6,0,7
	.byte	'unsigned int',0,4,7,7
	.byte	'int',0,4,5,7
	.byte	'unsigned char',0,1,8,7
	.byte	'unsigned short int',0,2,7,10
	.byte	'_Ifx_P_OUT_Bits',0,4,143,3,16,4,11
	.byte	'P0',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	494
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	494
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	494
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	494
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	494
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	494
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	494
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	494
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	494
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	494
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	494
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	494
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	511
	.byte	16,0,2,35,2,0,12,4,181,5,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	533
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMR_Bits',0,4,169,2,16,4,11
	.byte	'PS0',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	494
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	494
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	494
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	494
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	494
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	494
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	494
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	494
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	494
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	494
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	494
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	494
	.byte	1,0,2,35,1,11
	.byte	'PCL0',0,1
	.word	494
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	494
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	494
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	494
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	494
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	494
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	494
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	494
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	494
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	494
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	494
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	494
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	494
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	494
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	494
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	494
	.byte	1,0,2,35,3,0,12,4,133,5,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	849
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ID_Bits',0,4,110,16,4,11
	.byte	'MODREV',0,1
	.word	494
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	494
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	511
	.byte	16,0,2,35,2,0,12,4,148,4,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1420
	.byte	4,2,35,0,0,14,4
	.word	494
	.byte	15,3,0,10
	.byte	'_Ifx_P_IOCR0_Bits',0,4,140,1,16,4,11
	.byte	'reserved_0',0,1
	.word	494
	.byte	3,5,2,35,0,11
	.byte	'PC0',0,1
	.word	494
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	494
	.byte	3,5,2,35,1,11
	.byte	'PC1',0,1
	.word	494
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	494
	.byte	3,5,2,35,2,11
	.byte	'PC2',0,1
	.word	494
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	494
	.byte	3,5,2,35,3,11
	.byte	'PC3',0,1
	.word	494
	.byte	5,0,2,35,3,0,12,4,164,4,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1548
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR4_Bits',0,4,166,1,16,4,11
	.byte	'reserved_0',0,1
	.word	494
	.byte	3,5,2,35,0,11
	.byte	'PC4',0,1
	.word	494
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	494
	.byte	3,5,2,35,1,11
	.byte	'PC5',0,1
	.word	494
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	494
	.byte	3,5,2,35,2,11
	.byte	'PC6',0,1
	.word	494
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	494
	.byte	3,5,2,35,3,11
	.byte	'PC7',0,1
	.word	494
	.byte	5,0,2,35,3,0,12,4,180,4,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1763
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR8_Bits',0,4,179,1,16,4,11
	.byte	'reserved_0',0,1
	.word	494
	.byte	3,5,2,35,0,11
	.byte	'PC8',0,1
	.word	494
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	494
	.byte	3,5,2,35,1,11
	.byte	'PC9',0,1
	.word	494
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	494
	.byte	3,5,2,35,2,11
	.byte	'PC10',0,1
	.word	494
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	494
	.byte	3,5,2,35,3,11
	.byte	'PC11',0,1
	.word	494
	.byte	5,0,2,35,3,0,12,4,188,4,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1978
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR12_Bits',0,4,153,1,16,4,11
	.byte	'reserved_0',0,1
	.word	494
	.byte	3,5,2,35,0,11
	.byte	'PC12',0,1
	.word	494
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	494
	.byte	3,5,2,35,1,11
	.byte	'PC13',0,1
	.word	494
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	494
	.byte	3,5,2,35,2,11
	.byte	'PC14',0,1
	.word	494
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	494
	.byte	3,5,2,35,3,11
	.byte	'PC15',0,1
	.word	494
	.byte	5,0,2,35,3,0,12,4,172,4,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2195
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IN_Bits',0,4,118,16,4,11
	.byte	'P0',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	494
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	494
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	494
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	494
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	494
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	494
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	494
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	494
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	494
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	494
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	494
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	494
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	511
	.byte	16,0,2,35,2,0,12,4,156,4,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2415
	.byte	4,2,35,0,0,14,24
	.word	494
	.byte	15,23,0,10
	.byte	'_Ifx_P_PDR0_Bits',0,4,205,3,16,4,11
	.byte	'PD0',0,1
	.word	494
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	494
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	494
	.byte	1,0,2,35,0,11
	.byte	'PD2',0,1
	.word	494
	.byte	3,5,2,35,1,11
	.byte	'PL2',0,1
	.word	494
	.byte	1,4,2,35,1,11
	.byte	'PD3',0,1
	.word	494
	.byte	3,1,2,35,1,11
	.byte	'PL3',0,1
	.word	494
	.byte	1,0,2,35,1,11
	.byte	'PD4',0,1
	.word	494
	.byte	3,5,2,35,2,11
	.byte	'PL4',0,1
	.word	494
	.byte	1,4,2,35,2,11
	.byte	'PD5',0,1
	.word	494
	.byte	3,1,2,35,2,11
	.byte	'PL5',0,1
	.word	494
	.byte	1,0,2,35,2,11
	.byte	'PD6',0,1
	.word	494
	.byte	3,5,2,35,3,11
	.byte	'PL6',0,1
	.word	494
	.byte	1,4,2,35,3,11
	.byte	'PD7',0,1
	.word	494
	.byte	3,1,2,35,3,11
	.byte	'PL7',0,1
	.word	494
	.byte	1,0,2,35,3,0,12,4,205,5,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2738
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PDR1_Bits',0,4,226,3,16,4,11
	.byte	'PD8',0,1
	.word	494
	.byte	3,5,2,35,0,11
	.byte	'PL8',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'PD9',0,1
	.word	494
	.byte	3,1,2,35,0,11
	.byte	'PL9',0,1
	.word	494
	.byte	1,0,2,35,0,11
	.byte	'PD10',0,1
	.word	494
	.byte	3,5,2,35,1,11
	.byte	'PL10',0,1
	.word	494
	.byte	1,4,2,35,1,11
	.byte	'PD11',0,1
	.word	494
	.byte	3,1,2,35,1,11
	.byte	'PL11',0,1
	.word	494
	.byte	1,0,2,35,1,11
	.byte	'PD12',0,1
	.word	494
	.byte	3,5,2,35,2,11
	.byte	'PL12',0,1
	.word	494
	.byte	1,4,2,35,2,11
	.byte	'PD13',0,1
	.word	494
	.byte	3,1,2,35,2,11
	.byte	'PL13',0,1
	.word	494
	.byte	1,0,2,35,2,11
	.byte	'PD14',0,1
	.word	494
	.byte	3,5,2,35,3,11
	.byte	'PL14',0,1
	.word	494
	.byte	1,4,2,35,3,11
	.byte	'PD15',0,1
	.word	494
	.byte	3,1,2,35,3,11
	.byte	'PL15',0,1
	.word	494
	.byte	1,0,2,35,3,0,12,4,213,5,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3042
	.byte	4,2,35,0,0,14,8
	.word	494
	.byte	15,7,0,10
	.byte	'_Ifx_P_ESR_Bits',0,4,88,16,4,11
	.byte	'EN0',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	494
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	494
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	494
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	494
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	494
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	494
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	494
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	494
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	494
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	494
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	494
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	494
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	511
	.byte	16,0,2,35,2,0,12,4,140,4,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3367
	.byte	4,2,35,0,0,14,12
	.word	494
	.byte	15,11,0,10
	.byte	'_Ifx_P_PDISC_Bits',0,4,183,3,16,4,11
	.byte	'PDIS0',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'PDIS2',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'PDIS3',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'PDIS4',0,1
	.word	494
	.byte	1,3,2,35,0,11
	.byte	'PDIS5',0,1
	.word	494
	.byte	1,2,2,35,0,11
	.byte	'PDIS6',0,1
	.word	494
	.byte	1,1,2,35,0,11
	.byte	'PDIS7',0,1
	.word	494
	.byte	1,0,2,35,0,11
	.byte	'PDIS8',0,1
	.word	494
	.byte	1,7,2,35,1,11
	.byte	'PDIS9',0,1
	.word	494
	.byte	1,6,2,35,1,11
	.byte	'PDIS10',0,1
	.word	494
	.byte	1,5,2,35,1,11
	.byte	'PDIS11',0,1
	.word	494
	.byte	1,4,2,35,1,11
	.byte	'PDIS12',0,1
	.word	494
	.byte	1,3,2,35,1,11
	.byte	'PDIS13',0,1
	.word	494
	.byte	1,2,2,35,1,11
	.byte	'PDIS14',0,1
	.word	494
	.byte	1,1,2,35,1,11
	.byte	'PDIS15',0,1
	.word	494
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	511
	.byte	16,0,2,35,2,0,12,4,197,5,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3707
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PCSR_Bits',0,4,165,3,16,4,11
	.byte	'SEL0',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'SEL1',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'SEL2',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'SEL3',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'SEL4',0,1
	.word	494
	.byte	1,3,2,35,0,11
	.byte	'SEL5',0,1
	.word	494
	.byte	1,2,2,35,0,11
	.byte	'SEL6',0,1
	.word	494
	.byte	1,1,2,35,0,11
	.byte	'SEL7',0,1
	.word	494
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	494
	.byte	2,6,2,35,1,11
	.byte	'SEL10',0,1
	.word	494
	.byte	1,5,2,35,1,11
	.byte	'SEL11',0,1
	.word	494
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	471
	.byte	19,1,2,35,0,11
	.byte	'LCK',0,1
	.word	494
	.byte	1,0,2,35,3,0,12,4,189,5,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4073
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR0_Bits',0,4,206,2,16,4,11
	.byte	'PS0',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	471
	.byte	28,0,2,35,0,0,12,4,149,5,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4359
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR4_Bits',0,4,227,2,16,4,11
	.byte	'reserved_0',0,1
	.word	494
	.byte	4,4,2,35,0,11
	.byte	'PS4',0,1
	.word	494
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	494
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	494
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	494
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	471
	.byte	24,0,2,35,0,0,12,4,165,5,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4506
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR8_Bits',0,4,238,2,16,4,11
	.byte	'reserved_0',0,1
	.word	494
	.byte	8,0,2,35,0,11
	.byte	'PS8',0,1
	.word	494
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	494
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	494
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	494
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	471
	.byte	20,0,2,35,0,0,12,4,173,5,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4675
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR12_Bits',0,4,216,2,16,4,11
	.byte	'reserved_0',0,2
	.word	511
	.byte	12,4,2,35,0,11
	.byte	'PS12',0,1
	.word	494
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	494
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	494
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	494
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	511
	.byte	16,0,2,35,2,0,12,4,157,5,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4847
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR0_Bits',0,4,232,1,16,4,11
	.byte	'reserved_0',0,2
	.word	511
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	494
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	494
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	494
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	494
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	511
	.byte	12,0,2,35,2,0,12,4,229,4,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5022
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR4_Bits',0,4,253,1,16,4,11
	.byte	'reserved_0',0,4
	.word	471
	.byte	20,12,2,35,0,11
	.byte	'PCL4',0,1
	.word	494
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	494
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	494
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	494
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	494
	.byte	8,0,2,35,3,0,12,4,245,4,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5196
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR8_Bits',0,4,136,2,16,4,11
	.byte	'reserved_0',0,4
	.word	471
	.byte	24,8,2,35,0,11
	.byte	'PCL8',0,1
	.word	494
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	494
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	494
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	494
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	494
	.byte	4,0,2,35,3,0,12,4,253,4,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5370
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR12_Bits',0,4,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	471
	.byte	28,4,2,35,0,11
	.byte	'PCL12',0,1
	.word	494
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	494
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	494
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	494
	.byte	1,0,2,35,3,0,12,4,237,4,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5546
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR_Bits',0,4,249,2,16,4,11
	.byte	'PS0',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	494
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	494
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	494
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	494
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	494
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	494
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	494
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	494
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	494
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	494
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	494
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	494
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	511
	.byte	16,0,2,35,2,0,12,4,141,5,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5702
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR_Bits',0,4,147,2,16,4,11
	.byte	'reserved_0',0,2
	.word	511
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	494
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	494
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	494
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	494
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	494
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	494
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	494
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	494
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	494
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	494
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	494
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	494
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	494
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	494
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	494
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	494
	.byte	1,0,2,35,3,0,12,4,221,4,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6035
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR0_Bits',0,4,192,1,16,4,11
	.byte	'reserved_0',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	471
	.byte	30,0,2,35,0,0,12,4,196,4,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6383
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_Bits',0,4,200,1,16,4,11
	.byte	'reserved_0',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	471
	.byte	30,0,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_P21_Bits',0,4,208,1,16,4,11
	.byte	'RDIS_CTRL',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'RX_DIS',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'TERM',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'LRXTERM',0,1
	.word	494
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	471
	.byte	24,0,2,35,0,0,12,4,204,4,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6507
	.byte	4,2,35,0,13
	.byte	'B_P21',0
	.word	6591
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR2_Bits',0,4,218,1,16,4,11
	.byte	'reserved_0',0,1
	.word	494
	.byte	8,0,2,35,0,11
	.byte	'LVDSR',0,1
	.word	494
	.byte	1,7,2,35,1,11
	.byte	'LVDSRL',0,1
	.word	494
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	494
	.byte	2,4,2,35,1,11
	.byte	'TDIS_CTRL',0,1
	.word	494
	.byte	1,3,2,35,1,11
	.byte	'TX_DIS',0,1
	.word	494
	.byte	1,2,2,35,1,11
	.byte	'TX_PD',0,1
	.word	494
	.byte	1,1,2,35,1,11
	.byte	'TX_PWDPD',0,1
	.word	494
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	511
	.byte	16,0,2,35,2,0,12,4,213,4,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6771
	.byte	4,2,35,0,0,14,76
	.word	494
	.byte	15,75,0,10
	.byte	'_Ifx_P_ACCEN1_Bits',0,4,82,16,4,11
	.byte	'reserved_0',0,4
	.word	471
	.byte	32,0,2,35,0,0,12,4,132,4,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7024
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ACCEN0_Bits',0,4,45,16,4,11
	.byte	'EN0',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	494
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	494
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	494
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	494
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	494
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	494
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	494
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	494
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	494
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	494
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	494
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	494
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	494
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	494
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	494
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	494
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	494
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	494
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	494
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	494
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	494
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	494
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	494
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	494
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	494
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	494
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	494
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	494
	.byte	1,0,2,35,3,0,12,4,252,3,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7111
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P',0,4,229,5,25,128,2,13
	.byte	'OUT',0
	.word	809
	.byte	4,2,35,0,13
	.byte	'OMR',0
	.word	1380
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	1499
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	1539
	.byte	4,2,35,12,13
	.byte	'IOCR0',0
	.word	1723
	.byte	4,2,35,16,13
	.byte	'IOCR4',0
	.word	1938
	.byte	4,2,35,20,13
	.byte	'IOCR8',0
	.word	2155
	.byte	4,2,35,24,13
	.byte	'IOCR12',0
	.word	2375
	.byte	4,2,35,28,13
	.byte	'reserved_20',0
	.word	1539
	.byte	4,2,35,32,13
	.byte	'IN',0
	.word	2689
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	2729
	.byte	24,2,35,40,13
	.byte	'PDR0',0
	.word	3002
	.byte	4,2,35,64,13
	.byte	'PDR1',0
	.word	3318
	.byte	4,2,35,68,13
	.byte	'reserved_48',0
	.word	3358
	.byte	8,2,35,72,13
	.byte	'ESR',0
	.word	3658
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	3698
	.byte	12,2,35,84,13
	.byte	'PDISC',0
	.word	4033
	.byte	4,2,35,96,13
	.byte	'PCSR',0
	.word	4319
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	3358
	.byte	8,2,35,104,13
	.byte	'OMSR0',0
	.word	4466
	.byte	4,2,35,112,13
	.byte	'OMSR4',0
	.word	4635
	.byte	4,2,35,116,13
	.byte	'OMSR8',0
	.word	4807
	.byte	4,2,35,120,13
	.byte	'OMSR12',0
	.word	4982
	.byte	4,2,35,124,13
	.byte	'OMCR0',0
	.word	5156
	.byte	4,3,35,128,1,13
	.byte	'OMCR4',0
	.word	5330
	.byte	4,3,35,132,1,13
	.byte	'OMCR8',0
	.word	5506
	.byte	4,3,35,136,1,13
	.byte	'OMCR12',0
	.word	5662
	.byte	4,3,35,140,1,13
	.byte	'OMSR',0
	.word	5995
	.byte	4,3,35,144,1,13
	.byte	'OMCR',0
	.word	6343
	.byte	4,3,35,148,1,13
	.byte	'reserved_98',0
	.word	3358
	.byte	8,3,35,152,1,13
	.byte	'LPCR0',0
	.word	6467
	.byte	4,3,35,160,1,13
	.byte	'LPCR1',0
	.word	6716
	.byte	4,3,35,164,1,13
	.byte	'LPCR2',0
	.word	6975
	.byte	4,3,35,168,1,13
	.byte	'reserved_A4',0
	.word	7015
	.byte	76,3,35,172,1,13
	.byte	'ACCEN1',0
	.word	7071
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	7638
	.byte	4,3,35,252,1,0,16
	.word	7678
	.byte	3
	.word	8281
	.byte	17,3,172,1,9,4,18
	.byte	'IfxPort_State_notChanged',0,0,18
	.byte	'IfxPort_State_high',0,1,18
	.byte	'IfxPort_State_low',0,128,128,4,18
	.byte	'IfxPort_State_toggled',0,129,128,4,0,4
	.byte	'IfxPort_setPinState',0,3,3,208,4,17,1,1,5
	.byte	'port',0,3,208,4,44
	.word	8286
	.byte	5
	.byte	'pinIndex',0,3,208,4,56
	.word	494
	.byte	5
	.byte	'action',0,3,208,4,80
	.word	8291
	.byte	6,0,19
	.word	247
	.byte	20
	.word	273
	.byte	6,0,19
	.word	308
	.byte	20
	.word	340
	.byte	6,0,19
	.word	390
	.byte	20
	.word	409
	.byte	6,0,19
	.word	425
	.byte	20
	.word	440
	.byte	20
	.word	454
	.byte	6,0,19
	.word	8394
	.byte	20
	.word	8422
	.byte	20
	.word	8436
	.byte	20
	.word	8454
	.byte	6,0,7
	.byte	'short int',0,2,5,21
	.byte	'__wchar_t',0,5,1,1
	.word	8547
	.byte	21
	.byte	'__size_t',0,5,1,1
	.word	471
	.byte	21
	.byte	'__ptrdiff_t',0,5,1,1
	.word	487
	.byte	22,1,3
	.word	8615
	.byte	21
	.byte	'__codeptr',0,5,1,1
	.word	8617
	.byte	10
	.byte	'_Ifx_PSI5S_ACCEN0_Bits',0,6,45,16,4,11
	.byte	'EN0',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	494
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	494
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	494
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	494
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	494
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	494
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	494
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	494
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	494
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	494
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	494
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	494
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	494
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	494
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	494
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	494
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	494
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	494
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	494
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	494
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	494
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	494
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	494
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	494
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	494
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	494
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	494
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	494
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_PSI5S_ACCEN0_Bits',0,6,79,3
	.word	8640
	.byte	10
	.byte	'_Ifx_PSI5S_ACCEN1_Bits',0,6,82,16,4,11
	.byte	'reserved_0',0,4
	.word	471
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_PSI5S_ACCEN1_Bits',0,6,85,3
	.word	9201
	.byte	10
	.byte	'_Ifx_PSI5S_BAR_Bits',0,6,88,16,4,11
	.byte	'reserved_0',0,1
	.word	494
	.byte	2,6,2,35,0,11
	.byte	'BA',0,4
	.word	471
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_PSI5S_BAR_Bits',0,6,92,3
	.word	9282
	.byte	10
	.byte	'_Ifx_PSI5S_BG_Bits',0,6,95,16,4,11
	.byte	'BR_VALUE',0,2
	.word	511
	.byte	13,3,2,35,0,11
	.byte	'reserved_13',0,4
	.word	471
	.byte	19,0,2,35,0,0,21
	.byte	'Ifx_PSI5S_BG_Bits',0,6,99,3
	.word	9371
	.byte	10
	.byte	'_Ifx_PSI5S_CDW_Bits',0,6,102,16,4,11
	.byte	'SD0',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'SD1',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'SD2',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'SD3',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'SD4',0,1
	.word	494
	.byte	1,3,2,35,0,11
	.byte	'SD5',0,1
	.word	494
	.byte	1,2,2,35,0,11
	.byte	'SD6',0,1
	.word	494
	.byte	1,1,2,35,0,11
	.byte	'SD7',0,1
	.word	494
	.byte	1,0,2,35,0,11
	.byte	'TSI',0,1
	.word	494
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,4
	.word	471
	.byte	23,0,2,35,0,0,21
	.byte	'Ifx_PSI5S_CDW_Bits',0,6,114,3
	.word	9465
	.byte	10
	.byte	'_Ifx_PSI5S_CLC_Bits',0,6,117,16,4,11
	.byte	'DISR',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'DISS',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'EDIS',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	471
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_PSI5S_CLC_Bits',0,6,124,3
	.word	9675
	.byte	10
	.byte	'_Ifx_PSI5S_CON_Bits',0,6,127,16,4,11
	.byte	'M',0,1
	.word	494
	.byte	3,5,2,35,0,11
	.byte	'STP',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'REN',0,1
	.word	494
	.byte	1,3,2,35,0,11
	.byte	'PEN',0,1
	.word	494
	.byte	1,2,2,35,0,11
	.byte	'FEN',0,1
	.word	494
	.byte	1,1,2,35,0,11
	.byte	'OEN',0,1
	.word	494
	.byte	1,0,2,35,0,11
	.byte	'PE',0,1
	.word	494
	.byte	1,7,2,35,1,11
	.byte	'FE',0,1
	.word	494
	.byte	1,6,2,35,1,11
	.byte	'OE',0,1
	.word	494
	.byte	1,5,2,35,1,11
	.byte	'FDE',0,1
	.word	494
	.byte	1,4,2,35,1,11
	.byte	'ODD',0,1
	.word	494
	.byte	1,3,2,35,1,11
	.byte	'BRS',0,1
	.word	494
	.byte	1,2,2,35,1,11
	.byte	'LB',0,1
	.word	494
	.byte	1,1,2,35,1,11
	.byte	'R',0,1
	.word	494
	.byte	1,0,2,35,1,11
	.byte	'MTX',0,1
	.word	494
	.byte	3,5,2,35,2,11
	.byte	'reserved_19',0,2
	.word	511
	.byte	9,4,2,35,2,11
	.byte	'ODDTX',0,1
	.word	494
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	494
	.byte	3,0,2,35,3,0,21
	.byte	'Ifx_PSI5S_CON_Bits',0,6,147,1,3
	.word	9820
	.byte	10
	.byte	'_Ifx_PSI5S_CTV_Bits',0,6,150,1,16,4,11
	.byte	'CTV',0,2
	.word	511
	.byte	16,0,2,35,0,11
	.byte	'CTC',0,2
	.word	511
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_PSI5S_CTV_Bits',0,6,154,1,3
	.word	10154
	.byte	10
	.byte	'_Ifx_PSI5S_FCNT_Bits',0,6,157,1,16,4,11
	.byte	'FC0',0,1
	.word	494
	.byte	3,5,2,35,0,11
	.byte	'FC1',0,1
	.word	494
	.byte	3,2,2,35,0,11
	.byte	'FC2',0,2
	.word	511
	.byte	3,7,2,35,0,11
	.byte	'FC3',0,1
	.word	494
	.byte	3,4,2,35,1,11
	.byte	'FC4',0,1
	.word	494
	.byte	3,1,2,35,1,11
	.byte	'FC5',0,4
	.word	471
	.byte	3,14,2,35,0,11
	.byte	'FC6',0,1
	.word	494
	.byte	3,3,2,35,2,11
	.byte	'FC7',0,1
	.word	494
	.byte	3,0,2,35,2,11
	.byte	'NFCLR0',0,1
	.word	494
	.byte	1,7,2,35,3,11
	.byte	'NFCLR1',0,1
	.word	494
	.byte	1,6,2,35,3,11
	.byte	'NFCLR2',0,1
	.word	494
	.byte	1,5,2,35,3,11
	.byte	'NFCLR3',0,1
	.word	494
	.byte	1,4,2,35,3,11
	.byte	'NFCLR4',0,1
	.word	494
	.byte	1,3,2,35,3,11
	.byte	'NFCLR5',0,1
	.word	494
	.byte	1,2,2,35,3,11
	.byte	'NFCLR6',0,1
	.word	494
	.byte	1,1,2,35,3,11
	.byte	'NFCLR7',0,1
	.word	494
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_PSI5S_FCNT_Bits',0,6,175,1,3
	.word	10239
	.byte	10
	.byte	'_Ifx_PSI5S_FDO_Bits',0,6,178,1,16,4,11
	.byte	'STEP',0,2
	.word	511
	.byte	11,5,2,35,0,11
	.byte	'reserved_11',0,1
	.word	494
	.byte	3,2,2,35,1,11
	.byte	'DM',0,1
	.word	494
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	511
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_PSI5S_FDO_Bits',0,6,184,1,3
	.word	10560
	.byte	10
	.byte	'_Ifx_PSI5S_FDR_Bits',0,6,187,1,16,4,11
	.byte	'STEP',0,2
	.word	511
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	494
	.byte	4,2,2,35,1,11
	.byte	'DM',0,1
	.word	494
	.byte	2,0,2,35,1,11
	.byte	'RESULT',0,2
	.word	511
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	494
	.byte	6,0,2,35,3,0,21
	.byte	'Ifx_PSI5S_FDR_Bits',0,6,194,1,3
	.word	10691
	.byte	10
	.byte	'_Ifx_PSI5S_FDRT_Bits',0,6,197,1,16,4,11
	.byte	'STEP',0,2
	.word	511
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	494
	.byte	4,2,2,35,1,11
	.byte	'DM',0,1
	.word	494
	.byte	2,0,2,35,1,11
	.byte	'RESULT',0,2
	.word	511
	.byte	10,6,2,35,2,11
	.byte	'ECS',0,1
	.word	494
	.byte	3,3,2,35,3,11
	.byte	'ECEA',0,1
	.word	494
	.byte	1,2,2,35,3,11
	.byte	'ECEB',0,1
	.word	494
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	494
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_PSI5S_FDRT_Bits',0,6,207,1,3
	.word	10840
	.byte	10
	.byte	'_Ifx_PSI5S_FDV_Bits',0,6,210,1,16,4,11
	.byte	'FD_VALUE',0,2
	.word	511
	.byte	11,5,2,35,0,11
	.byte	'reserved_11',0,4
	.word	471
	.byte	21,0,2,35,0,0,21
	.byte	'Ifx_PSI5S_FDV_Bits',0,6,214,1,3
	.word	11038
	.byte	10
	.byte	'_Ifx_PSI5S_GCR_Bits',0,6,217,1,16,4,11
	.byte	'CRCI',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'XCRCI',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'TEI',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'PE',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'FE',0,1
	.word	494
	.byte	1,3,2,35,0,11
	.byte	'OE',0,1
	.word	494
	.byte	1,2,2,35,0,11
	.byte	'RBI',0,1
	.word	494
	.byte	1,1,2,35,0,11
	.byte	'HDI',0,1
	.word	494
	.byte	1,0,2,35,0,11
	.byte	'ETC0',0,1
	.word	494
	.byte	1,7,2,35,1,11
	.byte	'ETC1',0,1
	.word	494
	.byte	1,6,2,35,1,11
	.byte	'ETC2',0,1
	.word	494
	.byte	1,5,2,35,1,11
	.byte	'ETC3',0,1
	.word	494
	.byte	1,4,2,35,1,11
	.byte	'ETC4',0,1
	.word	494
	.byte	1,3,2,35,1,11
	.byte	'ETC5',0,1
	.word	494
	.byte	1,2,2,35,1,11
	.byte	'ETC6',0,1
	.word	494
	.byte	1,1,2,35,1,11
	.byte	'ETC7',0,1
	.word	494
	.byte	1,0,2,35,1,11
	.byte	'CEN0',0,1
	.word	494
	.byte	1,7,2,35,2,11
	.byte	'CEN1',0,1
	.word	494
	.byte	1,6,2,35,2,11
	.byte	'CEN2',0,1
	.word	494
	.byte	1,5,2,35,2,11
	.byte	'CEN3',0,1
	.word	494
	.byte	1,4,2,35,2,11
	.byte	'CEN4',0,1
	.word	494
	.byte	1,3,2,35,2,11
	.byte	'CEN5',0,1
	.word	494
	.byte	1,2,2,35,2,11
	.byte	'CEN6',0,1
	.word	494
	.byte	1,1,2,35,2,11
	.byte	'CEN7',0,1
	.word	494
	.byte	1,0,2,35,2,11
	.byte	'IDT',0,1
	.word	494
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	494
	.byte	3,1,2,35,3,11
	.byte	'ASC',0,1
	.word	494
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_PSI5S_GCR_Bits',0,6,246,1,3
	.word	11136
	.byte	10
	.byte	'_Ifx_PSI5S_ID_Bits',0,6,249,1,16,4,11
	.byte	'MODREV',0,1
	.word	494
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	494
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	511
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_PSI5S_ID_Bits',0,6,254,1,3
	.word	11620
	.byte	10
	.byte	'_Ifx_PSI5S_INP_Bits',0,6,129,2,16,4,11
	.byte	'RSI',0,1
	.word	494
	.byte	3,5,2,35,0,11
	.byte	'RDI',0,1
	.word	494
	.byte	3,2,2,35,0,11
	.byte	'RBI',0,2
	.word	511
	.byte	3,7,2,35,0,11
	.byte	'TEI',0,1
	.word	494
	.byte	3,4,2,35,1,11
	.byte	'CHCI',0,1
	.word	494
	.byte	3,1,2,35,1,11
	.byte	'CRCI',0,4
	.word	471
	.byte	3,14,2,35,0,11
	.byte	'TPI',0,1
	.word	494
	.byte	3,3,2,35,2,11
	.byte	'TPOI',0,1
	.word	494
	.byte	3,0,2,35,2,11
	.byte	'HDI',0,1
	.word	494
	.byte	3,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	494
	.byte	5,0,2,35,3,0,21
	.byte	'Ifx_PSI5S_INP_Bits',0,6,141,2,3
	.word	11731
	.byte	10
	.byte	'_Ifx_PSI5S_INPG_Bits',0,6,144,2,16,4,11
	.byte	'TIR',0,1
	.word	494
	.byte	3,5,2,35,0,11
	.byte	'RIR',0,1
	.word	494
	.byte	3,2,2,35,0,11
	.byte	'EIR',0,2
	.word	511
	.byte	3,7,2,35,0,11
	.byte	'TBIR',0,1
	.word	494
	.byte	3,4,2,35,1,11
	.byte	'XCRCI',0,1
	.word	494
	.byte	3,1,2,35,1,11
	.byte	'FOI',0,4
	.word	471
	.byte	3,14,2,35,0,11
	.byte	'reserved_18',0,2
	.word	511
	.byte	14,0,2,35,2,0,21
	.byte	'Ifx_PSI5S_INPG_Bits',0,6,153,2,3
	.word	11947
	.byte	10
	.byte	'_Ifx_PSI5S_INTCLR_Bits',0,6,156,2,16,4,11
	.byte	'RSI',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'RDI',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'RBI',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'TEI',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'CHCI',0,1
	.word	494
	.byte	1,3,2,35,0,11
	.byte	'CRCI',0,1
	.word	494
	.byte	1,2,2,35,0,11
	.byte	'TPI',0,1
	.word	494
	.byte	1,1,2,35,0,11
	.byte	'TPOI',0,1
	.word	494
	.byte	1,0,2,35,0,11
	.byte	'HDI',0,1
	.word	494
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,4
	.word	471
	.byte	23,0,2,35,0,0,21
	.byte	'Ifx_PSI5S_INTCLR_Bits',0,6,168,2,3
	.word	12120
	.byte	10
	.byte	'_Ifx_PSI5S_INTCLRG_Bits',0,6,171,2,16,4,11
	.byte	'TIR',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'RIR',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'EIR',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'TBIR',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'XCRCI',0,1
	.word	494
	.byte	1,3,2,35,0,11
	.byte	'FOI',0,1
	.word	494
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	471
	.byte	26,0,2,35,0,0,21
	.byte	'Ifx_PSI5S_INTCLRG_Bits',0,6,180,2,3
	.word	12341
	.byte	10
	.byte	'_Ifx_PSI5S_INTEN_Bits',0,6,183,2,16,4,11
	.byte	'RSI',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'RDI',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'RBI',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'TEI',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'CHCI',0,1
	.word	494
	.byte	1,3,2,35,0,11
	.byte	'CRCI',0,1
	.word	494
	.byte	1,2,2,35,0,11
	.byte	'TPI',0,1
	.word	494
	.byte	1,1,2,35,0,11
	.byte	'TPOI',0,1
	.word	494
	.byte	1,0,2,35,0,11
	.byte	'HDI',0,1
	.word	494
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,4
	.word	471
	.byte	23,0,2,35,0,0,21
	.byte	'Ifx_PSI5S_INTEN_Bits',0,6,195,2,3
	.word	12519
	.byte	10
	.byte	'_Ifx_PSI5S_INTENG_Bits',0,6,198,2,16,4,11
	.byte	'TIR',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'RIR',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'EIR',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'TBIR',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'XCRCI',0,1
	.word	494
	.byte	1,3,2,35,0,11
	.byte	'FOI',0,1
	.word	494
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	471
	.byte	26,0,2,35,0,0,21
	.byte	'Ifx_PSI5S_INTENG_Bits',0,6,207,2,3
	.word	12738
	.byte	10
	.byte	'_Ifx_PSI5S_INTOV_Bits',0,6,210,2,16,4,11
	.byte	'RSI',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'RDI',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'RBI',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'TEI',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'CHCI',0,1
	.word	494
	.byte	1,3,2,35,0,11
	.byte	'CRCI',0,1
	.word	494
	.byte	1,2,2,35,0,11
	.byte	'TPI',0,1
	.word	494
	.byte	1,1,2,35,0,11
	.byte	'TPOI',0,1
	.word	494
	.byte	1,0,2,35,0,11
	.byte	'HDI',0,1
	.word	494
	.byte	1,7,2,35,1,11
	.byte	'TIR',0,1
	.word	494
	.byte	1,6,2,35,1,11
	.byte	'RIR',0,1
	.word	494
	.byte	1,5,2,35,1,11
	.byte	'EIR',0,1
	.word	494
	.byte	1,4,2,35,1,11
	.byte	'TBIR',0,1
	.word	494
	.byte	1,3,2,35,1,11
	.byte	'XCRCI',0,1
	.word	494
	.byte	1,2,2,35,1,11
	.byte	'FOI',0,1
	.word	494
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	471
	.byte	17,0,2,35,0,0,21
	.byte	'Ifx_PSI5S_INTOV_Bits',0,6,228,2,3
	.word	12914
	.byte	10
	.byte	'_Ifx_PSI5S_INTSET_Bits',0,6,231,2,16,4,11
	.byte	'RSI',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'RDI',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'RBI',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'TEI',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'CHCI',0,1
	.word	494
	.byte	1,3,2,35,0,11
	.byte	'CRCI',0,1
	.word	494
	.byte	1,2,2,35,0,11
	.byte	'TPI',0,1
	.word	494
	.byte	1,1,2,35,0,11
	.byte	'TPOI',0,1
	.word	494
	.byte	1,0,2,35,0,11
	.byte	'HDI',0,1
	.word	494
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,4
	.word	471
	.byte	23,0,2,35,0,0,21
	.byte	'Ifx_PSI5S_INTSET_Bits',0,6,243,2,3
	.word	13227
	.byte	10
	.byte	'_Ifx_PSI5S_INTSETG_Bits',0,6,246,2,16,4,11
	.byte	'TIR',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'RIR',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'EIR',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'TBIR',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'XCRCI',0,1
	.word	494
	.byte	1,3,2,35,0,11
	.byte	'FOI',0,1
	.word	494
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	471
	.byte	26,0,2,35,0,0,21
	.byte	'Ifx_PSI5S_INTSETG_Bits',0,6,255,2,3
	.word	13448
	.byte	10
	.byte	'_Ifx_PSI5S_INTSTAT_Bits',0,6,130,3,16,4,11
	.byte	'RSI',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'RDI',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'RBI',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'TEI',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'CHCI',0,1
	.word	494
	.byte	1,3,2,35,0,11
	.byte	'CRCI',0,1
	.word	494
	.byte	1,2,2,35,0,11
	.byte	'TPI',0,1
	.word	494
	.byte	1,1,2,35,0,11
	.byte	'TPOI',0,1
	.word	494
	.byte	1,0,2,35,0,11
	.byte	'HDI',0,1
	.word	494
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,4
	.word	471
	.byte	23,0,2,35,0,0,21
	.byte	'Ifx_PSI5S_INTSTAT_Bits',0,6,142,3,3
	.word	13626
	.byte	10
	.byte	'_Ifx_PSI5S_INTSTATG_Bits',0,6,145,3,16,4,11
	.byte	'TIR',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'RIR',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'EIR',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'TBIR',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'XCRCI',0,1
	.word	494
	.byte	1,3,2,35,0,11
	.byte	'FOI',0,1
	.word	494
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	471
	.byte	26,0,2,35,0,0,21
	.byte	'Ifx_PSI5S_INTSTATG_Bits',0,6,154,3,3
	.word	13849
	.byte	10
	.byte	'_Ifx_PSI5S_IOCR_Bits',0,6,157,3,16,4,11
	.byte	'ALTI',0,1
	.word	494
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	471
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_PSI5S_IOCR_Bits',0,6,161,3,3
	.word	14029
	.byte	10
	.byte	'_Ifx_PSI5S_KRST0_Bits',0,6,164,3,16,4,11
	.byte	'RST',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'RSTSTAT',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	471
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_PSI5S_KRST0_Bits',0,6,169,3,3
	.word	14124
	.byte	10
	.byte	'_Ifx_PSI5S_KRST1_Bits',0,6,172,3,16,4,11
	.byte	'RST',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	471
	.byte	31,0,2,35,0,0,21
	.byte	'Ifx_PSI5S_KRST1_Bits',0,6,176,3,3
	.word	14239
	.byte	10
	.byte	'_Ifx_PSI5S_KRSTCLR_Bits',0,6,179,3,16,4,11
	.byte	'CLR',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	471
	.byte	31,0,2,35,0,0,21
	.byte	'Ifx_PSI5S_KRSTCLR_Bits',0,6,183,3,3
	.word	14335
	.byte	10
	.byte	'_Ifx_PSI5S_NFC_Bits',0,6,186,3,16,4,11
	.byte	'NF0',0,1
	.word	494
	.byte	3,5,2,35,0,11
	.byte	'NF1',0,1
	.word	494
	.byte	3,2,2,35,0,11
	.byte	'NF2',0,2
	.word	511
	.byte	3,7,2,35,0,11
	.byte	'NF3',0,1
	.word	494
	.byte	3,4,2,35,1,11
	.byte	'NF4',0,1
	.word	494
	.byte	3,1,2,35,1,11
	.byte	'NF5',0,4
	.word	471
	.byte	3,14,2,35,0,11
	.byte	'NF6',0,1
	.word	494
	.byte	3,3,2,35,2,11
	.byte	'NF7',0,1
	.word	494
	.byte	3,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	494
	.byte	8,0,2,35,3,0,21
	.byte	'Ifx_PSI5S_NFC_Bits',0,6,197,3,3
	.word	14435
	.byte	10
	.byte	'_Ifx_PSI5S_OCS_Bits',0,6,200,3,16,4,11
	.byte	'reserved_0',0,4
	.word	471
	.byte	24,8,2,35,0,11
	.byte	'SUS',0,1
	.word	494
	.byte	4,4,2,35,3,11
	.byte	'SUS_P',0,1
	.word	494
	.byte	1,3,2,35,3,11
	.byte	'SUSSTA',0,1
	.word	494
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	494
	.byte	2,0,2,35,3,0,21
	.byte	'Ifx_PSI5S_OCS_Bits',0,6,207,3,3
	.word	14633
	.byte	10
	.byte	'_Ifx_PSI5S_PGC_Bits',0,6,210,3,16,4,11
	.byte	'TXCMD',0,1
	.word	494
	.byte	5,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	494
	.byte	3,0,2,35,0,11
	.byte	'ATXCMD',0,1
	.word	494
	.byte	5,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	494
	.byte	2,1,2,35,1,11
	.byte	'TBS',0,1
	.word	494
	.byte	1,0,2,35,1,11
	.byte	'ETB',0,1
	.word	494
	.byte	3,5,2,35,2,11
	.byte	'PTE',0,1
	.word	494
	.byte	1,4,2,35,2,11
	.byte	'ETS',0,1
	.word	494
	.byte	3,1,2,35,2,11
	.byte	'ETE',0,1
	.word	494
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	494
	.byte	8,0,2,35,3,0,21
	.byte	'Ifx_PSI5S_PGC_Bits',0,6,222,3,3
	.word	14783
	.byte	10
	.byte	'_Ifx_PSI5S_RBUF_Bits',0,6,225,3,16,4,11
	.byte	'RD_VALUE',0,2
	.word	511
	.byte	9,7,2,35,0,11
	.byte	'reserved_9',0,4
	.word	471
	.byte	23,0,2,35,0,0,21
	.byte	'Ifx_PSI5S_RBUF_Bits',0,6,229,3,3
	.word	15016
	.byte	10
	.byte	'_Ifx_PSI5S_RCRA_Bits',0,6,232,3,16,4,11
	.byte	'CRC0',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'CRC1',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'CRC2',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'CRC3',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'CRC4',0,1
	.word	494
	.byte	1,3,2,35,0,11
	.byte	'CRC5',0,1
	.word	494
	.byte	1,2,2,35,0,11
	.byte	'TSEN',0,1
	.word	494
	.byte	1,1,2,35,0,11
	.byte	'TSP',0,1
	.word	494
	.byte	1,0,2,35,0,11
	.byte	'TSTS',0,1
	.word	494
	.byte	1,7,2,35,1,11
	.byte	'FIDS',0,1
	.word	494
	.byte	1,6,2,35,1,11
	.byte	'WDMS',0,1
	.word	494
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	494
	.byte	5,0,2,35,1,11
	.byte	'UFC0',0,1
	.word	494
	.byte	2,6,2,35,2,11
	.byte	'UFC1',0,1
	.word	494
	.byte	2,4,2,35,2,11
	.byte	'UFC2',0,1
	.word	494
	.byte	2,2,2,35,2,11
	.byte	'UFC3',0,1
	.word	494
	.byte	2,0,2,35,2,11
	.byte	'UFC4',0,1
	.word	494
	.byte	2,6,2,35,3,11
	.byte	'UFC5',0,1
	.word	494
	.byte	2,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	494
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_PSI5S_RCRA_Bits',0,6,253,3,3
	.word	15115
	.byte	10
	.byte	'_Ifx_PSI5S_RCRB_Bits',0,6,128,4,16,4,11
	.byte	'PDL0',0,1
	.word	494
	.byte	5,3,2,35,0,11
	.byte	'PDL1',0,2
	.word	511
	.byte	5,6,2,35,0,11
	.byte	'PDL2',0,1
	.word	494
	.byte	5,1,2,35,1,11
	.byte	'PDL3',0,4
	.word	471
	.byte	5,12,2,35,0,11
	.byte	'PDL4',0,2
	.word	511
	.byte	5,7,2,35,2,11
	.byte	'PDL5',0,1
	.word	494
	.byte	5,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	494
	.byte	2,0,2,35,3,0,21
	.byte	'Ifx_PSI5S_RCRB_Bits',0,6,137,4,3
	.word	15489
	.byte	10
	.byte	'_Ifx_PSI5S_RDR_Bits',0,6,140,4,16,4,11
	.byte	'RD0',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'RD1',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'RD2',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'RD3',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'RD4',0,1
	.word	494
	.byte	1,3,2,35,0,11
	.byte	'RD5',0,1
	.word	494
	.byte	1,2,2,35,0,11
	.byte	'RD6',0,1
	.word	494
	.byte	1,1,2,35,0,11
	.byte	'RD7',0,1
	.word	494
	.byte	1,0,2,35,0,11
	.byte	'RD8',0,1
	.word	494
	.byte	1,7,2,35,1,11
	.byte	'RD9',0,1
	.word	494
	.byte	1,6,2,35,1,11
	.byte	'RD10',0,1
	.word	494
	.byte	1,5,2,35,1,11
	.byte	'RD11',0,1
	.word	494
	.byte	1,4,2,35,1,11
	.byte	'RD12',0,1
	.word	494
	.byte	1,3,2,35,1,11
	.byte	'RD13',0,1
	.word	494
	.byte	1,2,2,35,1,11
	.byte	'RD14',0,1
	.word	494
	.byte	1,1,2,35,1,11
	.byte	'RD15',0,1
	.word	494
	.byte	1,0,2,35,1,11
	.byte	'RD16',0,1
	.word	494
	.byte	1,7,2,35,2,11
	.byte	'RD17',0,1
	.word	494
	.byte	1,6,2,35,2,11
	.byte	'RD18',0,1
	.word	494
	.byte	1,5,2,35,2,11
	.byte	'RD19',0,1
	.word	494
	.byte	1,4,2,35,2,11
	.byte	'RD20',0,1
	.word	494
	.byte	1,3,2,35,2,11
	.byte	'RD21',0,1
	.word	494
	.byte	1,2,2,35,2,11
	.byte	'RD22',0,1
	.word	494
	.byte	1,1,2,35,2,11
	.byte	'RD23',0,1
	.word	494
	.byte	1,0,2,35,2,11
	.byte	'RD24',0,1
	.word	494
	.byte	1,7,2,35,3,11
	.byte	'RD25',0,1
	.word	494
	.byte	1,6,2,35,3,11
	.byte	'RD26',0,1
	.word	494
	.byte	1,5,2,35,3,11
	.byte	'RD27',0,1
	.word	494
	.byte	1,4,2,35,3,11
	.byte	'PFC',0,1
	.word	494
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_PSI5S_RDR_Bits',0,6,171,4,3
	.word	15665
	.byte	10
	.byte	'_Ifx_PSI5S_RDS_Bits',0,6,174,4,16,4,11
	.byte	'XCRC0',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'XCRC1',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'XCRC2',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'XCRC3',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'XCRC4',0,1
	.word	494
	.byte	1,3,2,35,0,11
	.byte	'XCRC5',0,1
	.word	494
	.byte	1,2,2,35,0,11
	.byte	'XCRCI',0,1
	.word	494
	.byte	1,1,2,35,0,11
	.byte	'CRC0',0,1
	.word	494
	.byte	1,0,2,35,0,11
	.byte	'CRC1',0,1
	.word	494
	.byte	1,7,2,35,1,11
	.byte	'CRC2',0,1
	.word	494
	.byte	1,6,2,35,1,11
	.byte	'CRCI',0,1
	.word	494
	.byte	1,5,2,35,1,11
	.byte	'ERR0',0,1
	.word	494
	.byte	1,4,2,35,1,11
	.byte	'ERR1',0,1
	.word	494
	.byte	1,3,2,35,1,11
	.byte	'HDI',0,1
	.word	494
	.byte	1,2,2,35,1,11
	.byte	'PE',0,1
	.word	494
	.byte	1,1,2,35,1,11
	.byte	'FE',0,1
	.word	494
	.byte	1,0,2,35,1,11
	.byte	'OE',0,1
	.word	494
	.byte	1,7,2,35,2,11
	.byte	'TEI',0,1
	.word	494
	.byte	1,6,2,35,2,11
	.byte	'RBI',0,1
	.word	494
	.byte	1,5,2,35,2,11
	.byte	'FID',0,1
	.word	494
	.byte	3,2,2,35,2,11
	.byte	'CID',0,2
	.word	511
	.byte	3,7,2,35,2,11
	.byte	'AFC',0,1
	.word	494
	.byte	3,4,2,35,3,11
	.byte	'PFC',0,1
	.word	494
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_PSI5S_RDS_Bits',0,6,199,4,3
	.word	16173
	.byte	10
	.byte	'_Ifx_PSI5S_SCR_Bits',0,6,202,4,16,4,11
	.byte	'PLL',0,1
	.word	494
	.byte	5,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	494
	.byte	1,2,2,35,0,11
	.byte	'EPS',0,1
	.word	494
	.byte	2,0,2,35,0,11
	.byte	'BSC',0,1
	.word	494
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	494
	.byte	5,2,2,35,1,11
	.byte	'FLUS',0,1
	.word	494
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	471
	.byte	7,10,2,35,0,11
	.byte	'CRC',0,1
	.word	494
	.byte	1,1,2,35,2,11
	.byte	'STA',0,1
	.word	494
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	494
	.byte	2,6,2,35,3,11
	.byte	'TPF',0,1
	.word	494
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	494
	.byte	5,0,2,35,3,0,21
	.byte	'Ifx_PSI5S_SCR_Bits',0,6,216,4,3
	.word	16590
	.byte	10
	.byte	'_Ifx_PSI5S_SDR_Bits',0,6,219,4,16,4,11
	.byte	'SD0',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'SD1',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'SD2',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'SD3',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'SD4',0,1
	.word	494
	.byte	1,3,2,35,0,11
	.byte	'SD5',0,1
	.word	494
	.byte	1,2,2,35,0,11
	.byte	'SD6',0,1
	.word	494
	.byte	1,1,2,35,0,11
	.byte	'SD7',0,1
	.word	494
	.byte	1,0,2,35,0,11
	.byte	'SD8',0,1
	.word	494
	.byte	1,7,2,35,1,11
	.byte	'SD9',0,1
	.word	494
	.byte	1,6,2,35,1,11
	.byte	'SD10',0,1
	.word	494
	.byte	1,5,2,35,1,11
	.byte	'SD11',0,1
	.word	494
	.byte	1,4,2,35,1,11
	.byte	'SD12',0,1
	.word	494
	.byte	1,3,2,35,1,11
	.byte	'SD13',0,1
	.word	494
	.byte	1,2,2,35,1,11
	.byte	'SD14',0,1
	.word	494
	.byte	1,1,2,35,1,11
	.byte	'SD15',0,1
	.word	494
	.byte	1,0,2,35,1,11
	.byte	'SD16',0,1
	.word	494
	.byte	1,7,2,35,2,11
	.byte	'SD17',0,1
	.word	494
	.byte	1,6,2,35,2,11
	.byte	'SD18',0,1
	.word	494
	.byte	1,5,2,35,2,11
	.byte	'SD19',0,1
	.word	494
	.byte	1,4,2,35,2,11
	.byte	'SD20',0,1
	.word	494
	.byte	1,3,2,35,2,11
	.byte	'SD21',0,1
	.word	494
	.byte	1,2,2,35,2,11
	.byte	'SD22',0,1
	.word	494
	.byte	1,1,2,35,2,11
	.byte	'SD23',0,1
	.word	494
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	494
	.byte	8,0,2,35,3,0,21
	.byte	'Ifx_PSI5S_SDR_Bits',0,6,246,4,3
	.word	16864
	.byte	10
	.byte	'_Ifx_PSI5S_TAR_Bits',0,6,249,4,16,4,11
	.byte	'reserved_0',0,1
	.word	494
	.byte	2,6,2,35,0,11
	.byte	'TA',0,4
	.word	471
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_PSI5S_TAR_Bits',0,6,253,4,3
	.word	17316
	.byte	10
	.byte	'_Ifx_PSI5S_TBUF_Bits',0,6,128,5,16,4,11
	.byte	'TD_VALUE',0,2
	.word	511
	.byte	9,7,2,35,0,11
	.byte	'reserved_9',0,4
	.word	471
	.byte	23,0,2,35,0,0,21
	.byte	'Ifx_PSI5S_TBUF_Bits',0,6,132,5,3
	.word	17407
	.byte	10
	.byte	'_Ifx_PSI5S_TSCNTA_Bits',0,6,135,5,16,4,11
	.byte	'CTS',0,4
	.word	471
	.byte	24,8,2,35,0,11
	.byte	'ETB',0,1
	.word	494
	.byte	3,5,2,35,3,11
	.byte	'TBS',0,1
	.word	494
	.byte	1,4,2,35,3,11
	.byte	'TBEA',0,1
	.word	494
	.byte	1,3,2,35,3,11
	.byte	'TBEB',0,1
	.word	494
	.byte	1,2,2,35,3,11
	.byte	'CLRA',0,1
	.word	494
	.byte	1,1,2,35,3,11
	.byte	'CLRB',0,1
	.word	494
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_PSI5S_TSCNTA_Bits',0,6,144,5,3
	.word	17506
	.byte	10
	.byte	'_Ifx_PSI5S_TSCNTB_Bits',0,6,147,5,16,4,11
	.byte	'CTS',0,4
	.word	471
	.byte	24,8,2,35,0,11
	.byte	'ETB',0,1
	.word	494
	.byte	3,5,2,35,3,11
	.byte	'TBS',0,1
	.word	494
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	494
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_PSI5S_TSCNTB_Bits',0,6,153,5,3
	.word	17676
	.byte	10
	.byte	'_Ifx_PSI5S_TSCR_Bits',0,6,156,5,16,4,11
	.byte	'TS',0,4
	.word	471
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	494
	.byte	8,0,2,35,3,0,21
	.byte	'Ifx_PSI5S_TSCR_Bits',0,6,160,5,3
	.word	17805
	.byte	10
	.byte	'_Ifx_PSI5S_TSM_Bits',0,6,163,5,16,4,11
	.byte	'TS',0,4
	.word	471
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	494
	.byte	4,4,2,35,3,11
	.byte	'PFC',0,1
	.word	494
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_PSI5S_TSM_Bits',0,6,168,5,3
	.word	17899
	.byte	10
	.byte	'_Ifx_PSI5S_WDT_Bits',0,6,171,5,16,4,11
	.byte	'WDL',0,4
	.word	471
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	494
	.byte	8,0,2,35,3,0,21
	.byte	'Ifx_PSI5S_WDT_Bits',0,6,175,5,3
	.word	18006
	.byte	10
	.byte	'_Ifx_PSI5S_WHBCON_Bits',0,6,178,5,16,4,11
	.byte	'reserved_0',0,1
	.word	494
	.byte	4,4,2,35,0,11
	.byte	'CLRREN',0,1
	.word	494
	.byte	1,3,2,35,0,11
	.byte	'SETREN',0,1
	.word	494
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	494
	.byte	2,0,2,35,0,11
	.byte	'CLRPE',0,1
	.word	494
	.byte	1,7,2,35,1,11
	.byte	'CLRFE',0,1
	.word	494
	.byte	1,6,2,35,1,11
	.byte	'CLROE',0,1
	.word	494
	.byte	1,5,2,35,1,11
	.byte	'SETPE',0,1
	.word	494
	.byte	1,4,2,35,1,11
	.byte	'SETFE',0,1
	.word	494
	.byte	1,3,2,35,1,11
	.byte	'SETOE',0,1
	.word	494
	.byte	1,2,2,35,1,11
	.byte	'reserved_14',0,4
	.word	471
	.byte	18,0,2,35,0,0,21
	.byte	'Ifx_PSI5S_WHBCON_Bits',0,6,191,5,3
	.word	18099
	.byte	12,6,199,5,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8640
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5S_ACCEN0',0,6,204,5,3
	.word	18365
	.byte	12,6,207,5,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9201
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5S_ACCEN1',0,6,212,5,3
	.word	18431
	.byte	12,6,215,5,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9282
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5S_BAR',0,6,220,5,3
	.word	18497
	.byte	12,6,223,5,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9371
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5S_BG',0,6,228,5,3
	.word	18560
	.byte	12,6,231,5,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9465
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5S_CDW',0,6,236,5,3
	.word	18622
	.byte	12,6,239,5,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9675
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5S_CLC',0,6,244,5,3
	.word	18685
	.byte	12,6,247,5,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9820
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5S_CON',0,6,252,5,3
	.word	18748
	.byte	12,6,255,5,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10154
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5S_CTV',0,6,132,6,3
	.word	18811
	.byte	12,6,135,6,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10239
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5S_FCNT',0,6,140,6,3
	.word	18874
	.byte	12,6,143,6,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10560
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5S_FDO',0,6,148,6,3
	.word	18938
	.byte	12,6,151,6,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10691
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5S_FDR',0,6,156,6,3
	.word	19001
	.byte	12,6,159,6,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10840
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5S_FDRT',0,6,164,6,3
	.word	19064
	.byte	12,6,167,6,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11038
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5S_FDV',0,6,172,6,3
	.word	19128
	.byte	12,6,175,6,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11136
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5S_GCR',0,6,180,6,3
	.word	19191
	.byte	12,6,183,6,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11620
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5S_ID',0,6,188,6,3
	.word	19254
	.byte	12,6,191,6,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11731
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5S_INP',0,6,196,6,3
	.word	19316
	.byte	12,6,199,6,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11947
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5S_INPG',0,6,204,6,3
	.word	19379
	.byte	12,6,207,6,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12120
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5S_INTCLR',0,6,212,6,3
	.word	19443
	.byte	12,6,215,6,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12341
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5S_INTCLRG',0,6,220,6,3
	.word	19509
	.byte	12,6,223,6,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12519
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5S_INTEN',0,6,228,6,3
	.word	19576
	.byte	12,6,231,6,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12738
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5S_INTENG',0,6,236,6,3
	.word	19641
	.byte	12,6,239,6,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12914
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5S_INTOV',0,6,244,6,3
	.word	19707
	.byte	12,6,247,6,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13227
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5S_INTSET',0,6,252,6,3
	.word	19772
	.byte	12,6,255,6,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13448
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5S_INTSETG',0,6,132,7,3
	.word	19838
	.byte	12,6,135,7,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13626
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5S_INTSTAT',0,6,140,7,3
	.word	19905
	.byte	12,6,143,7,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13849
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5S_INTSTATG',0,6,148,7,3
	.word	19972
	.byte	12,6,151,7,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14029
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5S_IOCR',0,6,156,7,3
	.word	20040
	.byte	12,6,159,7,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14124
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5S_KRST0',0,6,164,7,3
	.word	20104
	.byte	12,6,167,7,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14239
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5S_KRST1',0,6,172,7,3
	.word	20169
	.byte	12,6,175,7,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14335
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5S_KRSTCLR',0,6,180,7,3
	.word	20234
	.byte	12,6,183,7,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14435
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5S_NFC',0,6,188,7,3
	.word	20301
	.byte	12,6,191,7,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14633
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5S_OCS',0,6,196,7,3
	.word	20364
	.byte	12,6,199,7,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14783
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5S_PGC',0,6,204,7,3
	.word	20427
	.byte	12,6,207,7,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15016
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5S_RBUF',0,6,212,7,3
	.word	20490
	.byte	12,6,215,7,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15115
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5S_RCRA',0,6,220,7,3
	.word	20554
	.byte	12,6,223,7,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15489
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5S_RCRB',0,6,228,7,3
	.word	20618
	.byte	12,6,231,7,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15665
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5S_RDR',0,6,236,7,3
	.word	20682
	.byte	12,6,239,7,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16173
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5S_RDS',0,6,244,7,3
	.word	20745
	.byte	12,6,247,7,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16590
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5S_SCR',0,6,252,7,3
	.word	20808
	.byte	12,6,255,7,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16864
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5S_SDR',0,6,132,8,3
	.word	20871
	.byte	12,6,135,8,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17316
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5S_TAR',0,6,140,8,3
	.word	20934
	.byte	12,6,143,8,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17407
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5S_TBUF',0,6,148,8,3
	.word	20997
	.byte	12,6,151,8,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17506
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5S_TSCNTA',0,6,156,8,3
	.word	21061
	.byte	12,6,159,8,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17676
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5S_TSCNTB',0,6,164,8,3
	.word	21127
	.byte	12,6,167,8,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17805
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5S_TSCR',0,6,172,8,3
	.word	21193
	.byte	12,6,175,8,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17899
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5S_TSM',0,6,180,8,3
	.word	21257
	.byte	12,6,183,8,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18006
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5S_WDT',0,6,188,8,3
	.word	21320
	.byte	12,6,191,8,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18099
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5S_WHBCON',0,6,196,8,3
	.word	21383
	.byte	14,32
	.word	20554
	.byte	15,7,0,14,32
	.word	20618
	.byte	15,7,0,14,32
	.word	21320
	.byte	15,7,0,14,32
	.word	21193
	.byte	15,7,0,14,20
	.word	494
	.byte	15,19,0,14,32
	.word	20427
	.byte	15,7,0,14,32
	.word	18811
	.byte	15,7,0,14,32
	.word	20808
	.byte	15,7,0,14,32
	.word	20871
	.byte	15,7,0,14,156,1
	.word	494
	.byte	15,155,1,0,14,40
	.word	494
	.byte	15,39,0,14,32
	.word	19905
	.byte	15,7,0,14,32
	.word	19772
	.byte	15,7,0,14,32
	.word	19443
	.byte	15,7,0,14,32
	.word	19576
	.byte	15,7,0,14,32
	.word	19316
	.byte	15,7,0,14,180,1
	.word	494
	.byte	15,179,1,0,14,156,24
	.word	494
	.byte	15,155,24,0,10
	.byte	'_Ifx_PSI5S',0,6,207,8,25,128,32,13
	.byte	'CLC',0
	.word	18685
	.byte	4,2,35,0,13
	.byte	'reserved_4',0
	.word	1539
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	19254
	.byte	4,2,35,8,13
	.byte	'FDR',0
	.word	19001
	.byte	4,2,35,12,13
	.byte	'FDRT',0
	.word	19064
	.byte	4,2,35,16,13
	.byte	'TSCNTA',0
	.word	21061
	.byte	4,2,35,20,13
	.byte	'TSCNTB',0
	.word	21127
	.byte	4,2,35,24,13
	.byte	'GCR',0
	.word	19191
	.byte	4,2,35,28,13
	.byte	'NFC',0
	.word	20301
	.byte	4,2,35,32,13
	.byte	'FCNT',0
	.word	18874
	.byte	4,2,35,36,13
	.byte	'IOCR',0
	.word	20040
	.byte	4,2,35,40,13
	.byte	'reserved_2C',0
	.word	1539
	.byte	4,2,35,44,13
	.byte	'RCRA',0
	.word	21449
	.byte	32,2,35,48,13
	.byte	'RCRB',0
	.word	21458
	.byte	32,2,35,80,13
	.byte	'WDT',0
	.word	21467
	.byte	32,2,35,112,13
	.byte	'TSCR',0
	.word	21476
	.byte	32,3,35,144,1,13
	.byte	'RDS',0
	.word	20745
	.byte	4,3,35,176,1,13
	.byte	'RDR',0
	.word	20682
	.byte	4,3,35,180,1,13
	.byte	'TSM',0
	.word	21257
	.byte	4,3,35,184,1,13
	.byte	'reserved_BC',0
	.word	21485
	.byte	20,3,35,188,1,13
	.byte	'TAR',0
	.word	20934
	.byte	4,3,35,208,1,13
	.byte	'BAR',0
	.word	18497
	.byte	4,3,35,212,1,13
	.byte	'reserved_D8',0
	.word	2729
	.byte	24,3,35,216,1,13
	.byte	'PGC',0
	.word	21494
	.byte	32,3,35,240,1,13
	.byte	'CTV',0
	.word	21503
	.byte	32,3,35,144,2,13
	.byte	'SCR',0
	.word	21512
	.byte	32,3,35,176,2,13
	.byte	'SDR',0
	.word	21521
	.byte	32,3,35,208,2,13
	.byte	'CDW',0
	.word	18622
	.byte	4,3,35,240,2,13
	.byte	'reserved_174',0
	.word	21530
	.byte	156,1,3,35,244,2,13
	.byte	'CON',0
	.word	18748
	.byte	4,3,35,144,4,13
	.byte	'BG',0
	.word	18560
	.byte	4,3,35,148,4,13
	.byte	'FDV',0
	.word	19128
	.byte	4,3,35,152,4,13
	.byte	'FDO',0
	.word	18938
	.byte	4,3,35,156,4,13
	.byte	'TBUF',0
	.word	20997
	.byte	4,3,35,160,4,13
	.byte	'RBUF',0
	.word	20490
	.byte	4,3,35,164,4,13
	.byte	'reserved_228',0
	.word	21541
	.byte	40,3,35,168,4,13
	.byte	'WHBCON',0
	.word	21383
	.byte	4,3,35,208,4,13
	.byte	'reserved_254',0
	.word	3698
	.byte	12,3,35,212,4,13
	.byte	'INTSTAT',0
	.word	21550
	.byte	32,3,35,224,4,13
	.byte	'INTSET',0
	.word	21559
	.byte	32,3,35,128,5,13
	.byte	'INTCLR',0
	.word	21568
	.byte	32,3,35,160,5,13
	.byte	'INTEN',0
	.word	21577
	.byte	32,3,35,192,5,13
	.byte	'INP',0
	.word	21586
	.byte	32,3,35,224,5,13
	.byte	'INTOV',0
	.word	19707
	.byte	4,3,35,128,6,13
	.byte	'INTSTATG',0
	.word	19972
	.byte	4,3,35,132,6,13
	.byte	'INTSETG',0
	.word	19838
	.byte	4,3,35,136,6,13
	.byte	'INTCLRG',0
	.word	19509
	.byte	4,3,35,140,6,13
	.byte	'INTENG',0
	.word	19641
	.byte	4,3,35,144,6,13
	.byte	'INPG',0
	.word	19379
	.byte	4,3,35,148,6,13
	.byte	'reserved_318',0
	.word	21595
	.byte	180,1,3,35,152,6,13
	.byte	'OCS',0
	.word	20364
	.byte	4,3,35,204,7,13
	.byte	'ACCEN0',0
	.word	18365
	.byte	4,3,35,208,7,13
	.byte	'ACCEN1',0
	.word	18431
	.byte	4,3,35,212,7,13
	.byte	'KRST0',0
	.word	20104
	.byte	4,3,35,216,7,13
	.byte	'KRST1',0
	.word	20169
	.byte	4,3,35,220,7,13
	.byte	'KRSTCLR',0
	.word	20234
	.byte	4,3,35,224,7,13
	.byte	'reserved_3E4',0
	.word	21606
	.byte	156,24,3,35,228,7,0,16
	.word	21617
	.byte	21
	.byte	'Ifx_PSI5S',0,6,138,9,3
	.word	22558
	.byte	21
	.byte	'boolean',0,7,101,29
	.word	494
	.byte	21
	.byte	'uint8',0,7,105,29
	.word	494
	.byte	21
	.byte	'uint16',0,7,109,29
	.word	511
	.byte	7
	.byte	'unsigned long int',0,4,7,21
	.byte	'uint32',0,7,113,29
	.word	22627
	.byte	21
	.byte	'uint64',0,7,118,29
	.word	353
	.byte	21
	.byte	'sint16',0,7,126,29
	.word	8547
	.byte	7
	.byte	'long int',0,4,5,21
	.byte	'sint32',0,7,131,1,29
	.word	22693
	.byte	7
	.byte	'long long int',0,8,5,21
	.byte	'sint64',0,7,138,1,29
	.word	22721
	.byte	21
	.byte	'float32',0,7,167,1,29
	.word	299
	.byte	21
	.byte	'pvoid',0,8,57,28
	.word	385
	.byte	21
	.byte	'Ifx_TickTime',0,8,79,28
	.word	22721
	.byte	17,8,130,1,9,1,18
	.byte	'Ifx_RxSel_a',0,0,18
	.byte	'Ifx_RxSel_b',0,1,18
	.byte	'Ifx_RxSel_c',0,2,18
	.byte	'Ifx_RxSel_d',0,3,18
	.byte	'Ifx_RxSel_e',0,4,18
	.byte	'Ifx_RxSel_f',0,5,18
	.byte	'Ifx_RxSel_g',0,6,18
	.byte	'Ifx_RxSel_h',0,7,0,21
	.byte	'Ifx_RxSel',0,8,140,1,3
	.word	22806
	.byte	21
	.byte	'Ifx_P_ACCEN0_Bits',0,4,79,3
	.word	7111
	.byte	21
	.byte	'Ifx_P_ACCEN1_Bits',0,4,85,3
	.word	7024
	.byte	21
	.byte	'Ifx_P_ESR_Bits',0,4,107,3
	.word	3367
	.byte	21
	.byte	'Ifx_P_ID_Bits',0,4,115,3
	.word	1420
	.byte	21
	.byte	'Ifx_P_IN_Bits',0,4,137,1,3
	.word	2415
	.byte	21
	.byte	'Ifx_P_IOCR0_Bits',0,4,150,1,3
	.word	1548
	.byte	21
	.byte	'Ifx_P_IOCR12_Bits',0,4,163,1,3
	.word	2195
	.byte	21
	.byte	'Ifx_P_IOCR4_Bits',0,4,176,1,3
	.word	1763
	.byte	21
	.byte	'Ifx_P_IOCR8_Bits',0,4,189,1,3
	.word	1978
	.byte	21
	.byte	'Ifx_P_LPCR0_Bits',0,4,197,1,3
	.word	6383
	.byte	21
	.byte	'Ifx_P_LPCR1_Bits',0,4,205,1,3
	.word	6507
	.byte	21
	.byte	'Ifx_P_LPCR1_P21_Bits',0,4,215,1,3
	.word	6591
	.byte	21
	.byte	'Ifx_P_LPCR2_Bits',0,4,229,1,3
	.word	6771
	.byte	21
	.byte	'Ifx_P_OMCR0_Bits',0,4,240,1,3
	.word	5022
	.byte	21
	.byte	'Ifx_P_OMCR12_Bits',0,4,250,1,3
	.word	5546
	.byte	21
	.byte	'Ifx_P_OMCR4_Bits',0,4,133,2,3
	.word	5196
	.byte	21
	.byte	'Ifx_P_OMCR8_Bits',0,4,144,2,3
	.word	5370
	.byte	21
	.byte	'Ifx_P_OMCR_Bits',0,4,166,2,3
	.word	6035
	.byte	21
	.byte	'Ifx_P_OMR_Bits',0,4,203,2,3
	.word	849
	.byte	21
	.byte	'Ifx_P_OMSR0_Bits',0,4,213,2,3
	.word	4359
	.byte	21
	.byte	'Ifx_P_OMSR12_Bits',0,4,224,2,3
	.word	4847
	.byte	21
	.byte	'Ifx_P_OMSR4_Bits',0,4,235,2,3
	.word	4506
	.byte	21
	.byte	'Ifx_P_OMSR8_Bits',0,4,246,2,3
	.word	4675
	.byte	21
	.byte	'Ifx_P_OMSR_Bits',0,4,140,3,3
	.word	5702
	.byte	21
	.byte	'Ifx_P_OUT_Bits',0,4,162,3,3
	.word	533
	.byte	21
	.byte	'Ifx_P_PCSR_Bits',0,4,180,3,3
	.word	4073
	.byte	21
	.byte	'Ifx_P_PDISC_Bits',0,4,202,3,3
	.word	3707
	.byte	21
	.byte	'Ifx_P_PDR0_Bits',0,4,223,3,3
	.word	2738
	.byte	21
	.byte	'Ifx_P_PDR1_Bits',0,4,244,3,3
	.word	3042
	.byte	21
	.byte	'Ifx_P_ACCEN0',0,4,129,4,3
	.word	7638
	.byte	21
	.byte	'Ifx_P_ACCEN1',0,4,137,4,3
	.word	7071
	.byte	21
	.byte	'Ifx_P_ESR',0,4,145,4,3
	.word	3658
	.byte	21
	.byte	'Ifx_P_ID',0,4,153,4,3
	.word	1499
	.byte	21
	.byte	'Ifx_P_IN',0,4,161,4,3
	.word	2689
	.byte	21
	.byte	'Ifx_P_IOCR0',0,4,169,4,3
	.word	1723
	.byte	21
	.byte	'Ifx_P_IOCR12',0,4,177,4,3
	.word	2375
	.byte	21
	.byte	'Ifx_P_IOCR4',0,4,185,4,3
	.word	1938
	.byte	21
	.byte	'Ifx_P_IOCR8',0,4,193,4,3
	.word	2155
	.byte	21
	.byte	'Ifx_P_LPCR0',0,4,201,4,3
	.word	6467
	.byte	21
	.byte	'Ifx_P_LPCR1',0,4,210,4,3
	.word	6716
	.byte	21
	.byte	'Ifx_P_LPCR2',0,4,218,4,3
	.word	6975
	.byte	21
	.byte	'Ifx_P_OMCR',0,4,226,4,3
	.word	6343
	.byte	21
	.byte	'Ifx_P_OMCR0',0,4,234,4,3
	.word	5156
	.byte	21
	.byte	'Ifx_P_OMCR12',0,4,242,4,3
	.word	5662
	.byte	21
	.byte	'Ifx_P_OMCR4',0,4,250,4,3
	.word	5330
	.byte	21
	.byte	'Ifx_P_OMCR8',0,4,130,5,3
	.word	5506
	.byte	21
	.byte	'Ifx_P_OMR',0,4,138,5,3
	.word	1380
	.byte	21
	.byte	'Ifx_P_OMSR',0,4,146,5,3
	.word	5995
	.byte	21
	.byte	'Ifx_P_OMSR0',0,4,154,5,3
	.word	4466
	.byte	21
	.byte	'Ifx_P_OMSR12',0,4,162,5,3
	.word	4982
	.byte	21
	.byte	'Ifx_P_OMSR4',0,4,170,5,3
	.word	4635
	.byte	21
	.byte	'Ifx_P_OMSR8',0,4,178,5,3
	.word	4807
	.byte	21
	.byte	'Ifx_P_OUT',0,4,186,5,3
	.word	809
	.byte	21
	.byte	'Ifx_P_PCSR',0,4,194,5,3
	.word	4319
	.byte	21
	.byte	'Ifx_P_PDISC',0,4,202,5,3
	.word	4033
	.byte	21
	.byte	'Ifx_P_PDR0',0,4,210,5,3
	.word	3002
	.byte	21
	.byte	'Ifx_P_PDR1',0,4,218,5,3
	.word	3318
	.byte	16
	.word	7678
	.byte	21
	.byte	'Ifx_P',0,4,139,6,3
	.word	24262
	.byte	17,9,250,10,9,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy16384',0,0,18
	.byte	'IfxScu_WDTCON1_IR_divBy256',0,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy64',0,2,0,21
	.byte	'IfxScu_WDTCON1_IR',0,9,255,10,3
	.word	24282
	.byte	10
	.byte	'_Ifx_SCU_ACCEN0_Bits',0,10,45,16,4,11
	.byte	'EN0',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	494
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	494
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	494
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	494
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	494
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	494
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	494
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	494
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	494
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	494
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	494
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	494
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	494
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	494
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	494
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	494
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	494
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	494
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	494
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	494
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	494
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	494
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	494
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	494
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	494
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	494
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	494
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	494
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_ACCEN0_Bits',0,10,79,3
	.word	24404
	.byte	10
	.byte	'_Ifx_SCU_ACCEN1_Bits',0,10,82,16,4,11
	.byte	'reserved_0',0,4
	.word	471
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_SCU_ACCEN1_Bits',0,10,85,3
	.word	24961
	.byte	10
	.byte	'_Ifx_SCU_ARSTDIS_Bits',0,10,88,16,4,11
	.byte	'STM0DIS',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'STM1DIS',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'STM2DIS',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	471
	.byte	29,0,2,35,0,0,21
	.byte	'Ifx_SCU_ARSTDIS_Bits',0,10,94,3
	.word	25038
	.byte	10
	.byte	'_Ifx_SCU_CCUCON0_Bits',0,10,97,16,4,11
	.byte	'BAUD1DIV',0,1
	.word	494
	.byte	4,4,2,35,0,11
	.byte	'BAUD2DIV',0,1
	.word	494
	.byte	4,0,2,35,0,11
	.byte	'SRIDIV',0,1
	.word	494
	.byte	4,4,2,35,1,11
	.byte	'LPDIV',0,1
	.word	494
	.byte	4,0,2,35,1,11
	.byte	'SPBDIV',0,1
	.word	494
	.byte	4,4,2,35,2,11
	.byte	'FSI2DIV',0,1
	.word	494
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	494
	.byte	2,0,2,35,2,11
	.byte	'FSIDIV',0,1
	.word	494
	.byte	2,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	494
	.byte	2,4,2,35,3,11
	.byte	'CLKSEL',0,1
	.word	494
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	494
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	494
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON0_Bits',0,10,111,3
	.word	25174
	.byte	10
	.byte	'_Ifx_SCU_CCUCON1_Bits',0,10,114,16,4,11
	.byte	'CANDIV',0,1
	.word	494
	.byte	4,4,2,35,0,11
	.byte	'ERAYDIV',0,1
	.word	494
	.byte	4,0,2,35,0,11
	.byte	'STMDIV',0,1
	.word	494
	.byte	4,4,2,35,1,11
	.byte	'GTMDIV',0,1
	.word	494
	.byte	4,0,2,35,1,11
	.byte	'ETHDIV',0,1
	.word	494
	.byte	4,4,2,35,2,11
	.byte	'ASCLINFDIV',0,1
	.word	494
	.byte	4,0,2,35,2,11
	.byte	'ASCLINSDIV',0,1
	.word	494
	.byte	4,4,2,35,3,11
	.byte	'INSEL',0,1
	.word	494
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	494
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	494
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON1_Bits',0,10,126,3
	.word	25454
	.byte	10
	.byte	'_Ifx_SCU_CCUCON2_Bits',0,10,129,1,16,4,11
	.byte	'BBBDIV',0,1
	.word	494
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	471
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	494
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	494
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON2_Bits',0,10,135,1,3
	.word	25692
	.byte	10
	.byte	'_Ifx_SCU_CCUCON3_Bits',0,10,138,1,16,4,11
	.byte	'PLLDIV',0,1
	.word	494
	.byte	6,2,2,35,0,11
	.byte	'PLLSEL',0,1
	.word	494
	.byte	2,0,2,35,0,11
	.byte	'PLLERAYDIV',0,1
	.word	494
	.byte	6,2,2,35,1,11
	.byte	'PLLERAYSEL',0,1
	.word	494
	.byte	2,0,2,35,1,11
	.byte	'SRIDIV',0,1
	.word	494
	.byte	6,2,2,35,2,11
	.byte	'SRISEL',0,1
	.word	494
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	494
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	494
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	494
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	494
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON3_Bits',0,10,150,1,3
	.word	25820
	.byte	10
	.byte	'_Ifx_SCU_CCUCON4_Bits',0,10,153,1,16,4,11
	.byte	'SPBDIV',0,1
	.word	494
	.byte	6,2,2,35,0,11
	.byte	'SPBSEL',0,1
	.word	494
	.byte	2,0,2,35,0,11
	.byte	'GTMDIV',0,1
	.word	494
	.byte	6,2,2,35,1,11
	.byte	'GTMSEL',0,1
	.word	494
	.byte	2,0,2,35,1,11
	.byte	'STMDIV',0,1
	.word	494
	.byte	6,2,2,35,2,11
	.byte	'STMSEL',0,1
	.word	494
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	494
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	494
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	494
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	494
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON4_Bits',0,10,165,1,3
	.word	26063
	.byte	10
	.byte	'_Ifx_SCU_CCUCON5_Bits',0,10,168,1,16,4,11
	.byte	'MAXDIV',0,1
	.word	494
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	471
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	494
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	494
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON5_Bits',0,10,174,1,3
	.word	26298
	.byte	10
	.byte	'_Ifx_SCU_CCUCON6_Bits',0,10,177,1,16,4,11
	.byte	'CPU0DIV',0,1
	.word	494
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	471
	.byte	26,0,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON6_Bits',0,10,181,1,3
	.word	26426
	.byte	10
	.byte	'_Ifx_SCU_CCUCON7_Bits',0,10,184,1,16,4,11
	.byte	'CPU1DIV',0,1
	.word	494
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	471
	.byte	26,0,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON7_Bits',0,10,188,1,3
	.word	26526
	.byte	10
	.byte	'_Ifx_SCU_CHIPID_Bits',0,10,191,1,16,4,11
	.byte	'CHREV',0,1
	.word	494
	.byte	6,2,2,35,0,11
	.byte	'CHTEC',0,1
	.word	494
	.byte	2,0,2,35,0,11
	.byte	'CHID',0,1
	.word	494
	.byte	8,0,2,35,1,11
	.byte	'EEA',0,1
	.word	494
	.byte	1,7,2,35,2,11
	.byte	'UCODE',0,1
	.word	494
	.byte	7,0,2,35,2,11
	.byte	'FSIZE',0,1
	.word	494
	.byte	4,4,2,35,3,11
	.byte	'SP',0,1
	.word	494
	.byte	2,2,2,35,3,11
	.byte	'SEC',0,1
	.word	494
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	494
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CHIPID_Bits',0,10,202,1,3
	.word	26626
	.byte	10
	.byte	'_Ifx_SCU_DTSCON_Bits',0,10,205,1,16,4,11
	.byte	'PWD',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'START',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	494
	.byte	2,4,2,35,0,11
	.byte	'CAL',0,4
	.word	471
	.byte	20,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	494
	.byte	7,1,2,35,3,11
	.byte	'SLCK',0,1
	.word	494
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_DTSCON_Bits',0,10,213,1,3
	.word	26834
	.byte	10
	.byte	'_Ifx_SCU_DTSLIM_Bits',0,10,216,1,16,4,11
	.byte	'LOWER',0,2
	.word	511
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	494
	.byte	5,1,2,35,1,11
	.byte	'LLU',0,1
	.word	494
	.byte	1,0,2,35,1,11
	.byte	'UPPER',0,2
	.word	511
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	494
	.byte	4,2,2,35,3,11
	.byte	'SLCK',0,1
	.word	494
	.byte	1,1,2,35,3,11
	.byte	'UOF',0,1
	.word	494
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_DTSLIM_Bits',0,10,225,1,3
	.word	26999
	.byte	10
	.byte	'_Ifx_SCU_DTSSTAT_Bits',0,10,228,1,16,4,11
	.byte	'RESULT',0,2
	.word	511
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	494
	.byte	4,2,2,35,1,11
	.byte	'RDY',0,1
	.word	494
	.byte	1,1,2,35,1,11
	.byte	'BUSY',0,1
	.word	494
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	511
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_DTSSTAT_Bits',0,10,235,1,3
	.word	27182
	.byte	10
	.byte	'_Ifx_SCU_EICR_Bits',0,10,238,1,16,4,11
	.byte	'reserved_0',0,1
	.word	494
	.byte	4,4,2,35,0,11
	.byte	'EXIS0',0,1
	.word	494
	.byte	3,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	494
	.byte	1,0,2,35,0,11
	.byte	'FEN0',0,1
	.word	494
	.byte	1,7,2,35,1,11
	.byte	'REN0',0,1
	.word	494
	.byte	1,6,2,35,1,11
	.byte	'LDEN0',0,1
	.word	494
	.byte	1,5,2,35,1,11
	.byte	'EIEN0',0,1
	.word	494
	.byte	1,4,2,35,1,11
	.byte	'INP0',0,1
	.word	494
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	471
	.byte	5,12,2,35,0,11
	.byte	'EXIS1',0,1
	.word	494
	.byte	3,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	494
	.byte	1,0,2,35,2,11
	.byte	'FEN1',0,1
	.word	494
	.byte	1,7,2,35,3,11
	.byte	'REN1',0,1
	.word	494
	.byte	1,6,2,35,3,11
	.byte	'LDEN1',0,1
	.word	494
	.byte	1,5,2,35,3,11
	.byte	'EIEN1',0,1
	.word	494
	.byte	1,4,2,35,3,11
	.byte	'INP1',0,1
	.word	494
	.byte	3,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	494
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EICR_Bits',0,10,129,2,3
	.word	27336
	.byte	10
	.byte	'_Ifx_SCU_EIFR_Bits',0,10,132,2,16,4,11
	.byte	'INTF0',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'INTF1',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'INTF2',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'INTF3',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'INTF4',0,1
	.word	494
	.byte	1,3,2,35,0,11
	.byte	'INTF5',0,1
	.word	494
	.byte	1,2,2,35,0,11
	.byte	'INTF6',0,1
	.word	494
	.byte	1,1,2,35,0,11
	.byte	'INTF7',0,1
	.word	494
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	471
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_SCU_EIFR_Bits',0,10,143,2,3
	.word	27700
	.byte	10
	.byte	'_Ifx_SCU_EMSR_Bits',0,10,146,2,16,4,11
	.byte	'POL',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'MODE',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'ENON',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'PSEL',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	511
	.byte	12,0,2,35,0,11
	.byte	'EMSF',0,1
	.word	494
	.byte	1,7,2,35,2,11
	.byte	'SEMSF',0,1
	.word	494
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	494
	.byte	6,0,2,35,2,11
	.byte	'EMSFM',0,1
	.word	494
	.byte	2,6,2,35,3,11
	.byte	'SEMSFM',0,1
	.word	494
	.byte	2,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	494
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_EMSR_Bits',0,10,159,2,3
	.word	27911
	.byte	10
	.byte	'_Ifx_SCU_ESRCFG_Bits',0,10,162,2,16,4,11
	.byte	'reserved_0',0,1
	.word	494
	.byte	7,1,2,35,0,11
	.byte	'EDCON',0,2
	.word	511
	.byte	2,7,2,35,0,11
	.byte	'reserved_9',0,4
	.word	471
	.byte	23,0,2,35,0,0,21
	.byte	'Ifx_SCU_ESRCFG_Bits',0,10,167,2,3
	.word	28163
	.byte	10
	.byte	'_Ifx_SCU_ESROCFG_Bits',0,10,170,2,16,4,11
	.byte	'ARI',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'ARC',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	471
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_ESROCFG_Bits',0,10,175,2,3
	.word	28281
	.byte	10
	.byte	'_Ifx_SCU_EVR13CON_Bits',0,10,178,2,16,4,11
	.byte	'reserved_0',0,4
	.word	471
	.byte	28,4,2,35,0,11
	.byte	'EVR13OFF',0,1
	.word	494
	.byte	1,3,2,35,3,11
	.byte	'BPEVR13OFF',0,1
	.word	494
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	494
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	494
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVR13CON_Bits',0,10,185,2,3
	.word	28392
	.byte	10
	.byte	'_Ifx_SCU_EVR33CON_Bits',0,10,188,2,16,4,11
	.byte	'reserved_0',0,4
	.word	471
	.byte	28,4,2,35,0,11
	.byte	'EVR33OFF',0,1
	.word	494
	.byte	1,3,2,35,3,11
	.byte	'BPEVR33OFF',0,1
	.word	494
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	494
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	494
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVR33CON_Bits',0,10,195,2,3
	.word	28555
	.byte	10
	.byte	'_Ifx_SCU_EVRADCSTAT_Bits',0,10,198,2,16,4,11
	.byte	'ADC13V',0,1
	.word	494
	.byte	8,0,2,35,0,11
	.byte	'ADC33V',0,1
	.word	494
	.byte	8,0,2,35,1,11
	.byte	'ADCSWDV',0,1
	.word	494
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	494
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	494
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRADCSTAT_Bits',0,10,205,2,3
	.word	28718
	.byte	10
	.byte	'_Ifx_SCU_EVRDVSTAT_Bits',0,10,208,2,16,4,11
	.byte	'DVS13TRIM',0,1
	.word	494
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	494
	.byte	8,0,2,35,1,11
	.byte	'DVS33TRIM',0,1
	.word	494
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	494
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	494
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRDVSTAT_Bits',0,10,215,2,3
	.word	28876
	.byte	10
	.byte	'_Ifx_SCU_EVRMONCTRL_Bits',0,10,218,2,16,4,11
	.byte	'EVR13OVMOD',0,1
	.word	494
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	494
	.byte	2,4,2,35,0,11
	.byte	'EVR13UVMOD',0,1
	.word	494
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	494
	.byte	2,0,2,35,0,11
	.byte	'EVR33OVMOD',0,1
	.word	494
	.byte	2,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	494
	.byte	2,4,2,35,1,11
	.byte	'EVR33UVMOD',0,1
	.word	494
	.byte	2,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	494
	.byte	2,0,2,35,1,11
	.byte	'SWDOVMOD',0,1
	.word	494
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	494
	.byte	2,4,2,35,2,11
	.byte	'SWDUVMOD',0,1
	.word	494
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,2
	.word	511
	.byte	10,0,2,35,2,0,21
	.byte	'Ifx_SCU_EVRMONCTRL_Bits',0,10,232,2,3
	.word	29041
	.byte	10
	.byte	'_Ifx_SCU_EVROSCCTRL_Bits',0,10,235,2,16,4,11
	.byte	'OSCTRIM',0,2
	.word	511
	.byte	10,6,2,35,0,11
	.byte	'OSCPTAT',0,1
	.word	494
	.byte	6,0,2,35,1,11
	.byte	'OSCANASEL',0,1
	.word	494
	.byte	4,4,2,35,2,11
	.byte	'HPBGTRIM',0,2
	.word	511
	.byte	7,5,2,35,2,11
	.byte	'HPBGCLKEN',0,1
	.word	494
	.byte	1,4,2,35,3,11
	.byte	'OSC3V3',0,1
	.word	494
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	494
	.byte	2,1,2,35,3,11
	.byte	'LCK',0,1
	.word	494
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVROSCCTRL_Bits',0,10,245,2,3
	.word	29370
	.byte	10
	.byte	'_Ifx_SCU_EVROVMON_Bits',0,10,248,2,16,4,11
	.byte	'EVR13OVVAL',0,1
	.word	494
	.byte	8,0,2,35,0,11
	.byte	'EVR33OVVAL',0,1
	.word	494
	.byte	8,0,2,35,1,11
	.byte	'SWDOVVAL',0,1
	.word	494
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	494
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	494
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVROVMON_Bits',0,10,255,2,3
	.word	29591
	.byte	10
	.byte	'_Ifx_SCU_EVRRSTCON_Bits',0,10,130,3,16,4,11
	.byte	'RST13TRIM',0,1
	.word	494
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	471
	.byte	16,8,2,35,0,11
	.byte	'RST13OFF',0,1
	.word	494
	.byte	1,7,2,35,3,11
	.byte	'BPRST13OFF',0,1
	.word	494
	.byte	1,6,2,35,3,11
	.byte	'RST33OFF',0,1
	.word	494
	.byte	1,5,2,35,3,11
	.byte	'BPRST33OFF',0,1
	.word	494
	.byte	1,4,2,35,3,11
	.byte	'RSTSWDOFF',0,1
	.word	494
	.byte	1,3,2,35,3,11
	.byte	'BPRSTSWDOFF',0,1
	.word	494
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	494
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	494
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRRSTCON_Bits',0,10,142,3,3
	.word	29754
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF1_Bits',0,10,145,3,16,4,11
	.byte	'SD5P',0,1
	.word	494
	.byte	8,0,2,35,0,11
	.byte	'SD5I',0,1
	.word	494
	.byte	8,0,2,35,1,11
	.byte	'SD5D',0,1
	.word	494
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	494
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	494
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF1_Bits',0,10,152,3,3
	.word	30026
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF2_Bits',0,10,155,3,16,4,11
	.byte	'SD33P',0,1
	.word	494
	.byte	8,0,2,35,0,11
	.byte	'SD33I',0,1
	.word	494
	.byte	8,0,2,35,1,11
	.byte	'SD33D',0,1
	.word	494
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	494
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	494
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF2_Bits',0,10,162,3,3
	.word	30179
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF3_Bits',0,10,165,3,16,4,11
	.byte	'CT5REG0',0,1
	.word	494
	.byte	8,0,2,35,0,11
	.byte	'CT5REG1',0,1
	.word	494
	.byte	8,0,2,35,1,11
	.byte	'CT5REG2',0,1
	.word	494
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	494
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	494
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF3_Bits',0,10,172,3,3
	.word	30335
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF4_Bits',0,10,175,3,16,4,11
	.byte	'CT5REG3',0,1
	.word	494
	.byte	8,0,2,35,0,11
	.byte	'CT5REG4',0,1
	.word	494
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	511
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	494
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF4_Bits',0,10,181,3,3
	.word	30497
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF5_Bits',0,10,184,3,16,4,11
	.byte	'CT33REG0',0,1
	.word	494
	.byte	8,0,2,35,0,11
	.byte	'CT33REG1',0,1
	.word	494
	.byte	8,0,2,35,1,11
	.byte	'CT33REG2',0,1
	.word	494
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	494
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	494
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF5_Bits',0,10,191,3,3
	.word	30640
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF6_Bits',0,10,194,3,16,4,11
	.byte	'CT33REG3',0,1
	.word	494
	.byte	8,0,2,35,0,11
	.byte	'CT33REG4',0,1
	.word	494
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	511
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	494
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF6_Bits',0,10,200,3,3
	.word	30805
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL1_Bits',0,10,203,3,16,4,11
	.byte	'SDFREQSPRD',0,2
	.word	511
	.byte	16,0,2,35,0,11
	.byte	'SDFREQ',0,1
	.word	494
	.byte	8,0,2,35,2,11
	.byte	'SDSTEP',0,1
	.word	494
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	494
	.byte	2,2,2,35,3,11
	.byte	'SDSAMPLE',0,1
	.word	494
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	494
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCTRL1_Bits',0,10,211,3,3
	.word	30950
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL2_Bits',0,10,214,3,16,4,11
	.byte	'DRVP',0,1
	.word	494
	.byte	8,0,2,35,0,11
	.byte	'SDMINMAXDC',0,1
	.word	494
	.byte	8,0,2,35,1,11
	.byte	'DRVN',0,1
	.word	494
	.byte	8,0,2,35,2,11
	.byte	'SDLUT',0,1
	.word	494
	.byte	6,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	494
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	494
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCTRL2_Bits',0,10,222,3,3
	.word	31131
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL3_Bits',0,10,225,3,16,4,11
	.byte	'SDPWMPRE',0,1
	.word	494
	.byte	8,0,2,35,0,11
	.byte	'SDPID',0,1
	.word	494
	.byte	8,0,2,35,1,11
	.byte	'SDVOKLVL',0,1
	.word	494
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	494
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	494
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCTRL3_Bits',0,10,232,3,3
	.word	31305
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL4_Bits',0,10,235,3,16,4,11
	.byte	'reserved_0',0,1
	.word	494
	.byte	8,0,2,35,0,11
	.byte	'SYNCDIV',0,1
	.word	494
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	471
	.byte	20,1,2,35,0,11
	.byte	'LCK',0,1
	.word	494
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCTRL4_Bits',0,10,241,3,3
	.word	31465
	.byte	10
	.byte	'_Ifx_SCU_EVRSTAT_Bits',0,10,244,3,16,4,11
	.byte	'EVR13',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'OV13',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'EVR33',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'OV33',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'OVSWD',0,1
	.word	494
	.byte	1,3,2,35,0,11
	.byte	'UV13',0,1
	.word	494
	.byte	1,2,2,35,0,11
	.byte	'UV33',0,1
	.word	494
	.byte	1,1,2,35,0,11
	.byte	'UVSWD',0,1
	.word	494
	.byte	1,0,2,35,0,11
	.byte	'EXTPASS13',0,1
	.word	494
	.byte	1,7,2,35,1,11
	.byte	'EXTPASS33',0,1
	.word	494
	.byte	1,6,2,35,1,11
	.byte	'BGPROK',0,1
	.word	494
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	471
	.byte	21,0,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSTAT_Bits',0,10,130,4,3
	.word	31609
	.byte	10
	.byte	'_Ifx_SCU_EVRTRIM_Bits',0,10,133,4,16,4,11
	.byte	'EVR13TRIM',0,1
	.word	494
	.byte	8,0,2,35,0,11
	.byte	'SDVOUTSEL',0,1
	.word	494
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	511
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	494
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRTRIM_Bits',0,10,139,4,3
	.word	31883
	.byte	10
	.byte	'_Ifx_SCU_EVRUVMON_Bits',0,10,142,4,16,4,11
	.byte	'EVR13UVVAL',0,1
	.word	494
	.byte	8,0,2,35,0,11
	.byte	'EVR33UVVAL',0,1
	.word	494
	.byte	8,0,2,35,1,11
	.byte	'SWDUVVAL',0,1
	.word	494
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	494
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	494
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRUVMON_Bits',0,10,149,4,3
	.word	32022
	.byte	10
	.byte	'_Ifx_SCU_EXTCON_Bits',0,10,152,4,16,4,11
	.byte	'EN0',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'SEL0',0,1
	.word	494
	.byte	4,2,2,35,0,11
	.byte	'reserved_6',0,2
	.word	511
	.byte	10,0,2,35,0,11
	.byte	'EN1',0,1
	.word	494
	.byte	1,7,2,35,2,11
	.byte	'NSEL',0,1
	.word	494
	.byte	1,6,2,35,2,11
	.byte	'SEL1',0,1
	.word	494
	.byte	4,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	494
	.byte	2,0,2,35,2,11
	.byte	'DIV1',0,1
	.word	494
	.byte	8,0,2,35,3,0,21
	.byte	'Ifx_SCU_EXTCON_Bits',0,10,163,4,3
	.word	32185
	.byte	10
	.byte	'_Ifx_SCU_FDR_Bits',0,10,166,4,16,4,11
	.byte	'STEP',0,2
	.word	511
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	494
	.byte	4,2,2,35,1,11
	.byte	'DM',0,1
	.word	494
	.byte	2,0,2,35,1,11
	.byte	'RESULT',0,2
	.word	511
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	494
	.byte	5,1,2,35,3,11
	.byte	'DISCLK',0,1
	.word	494
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_FDR_Bits',0,10,174,4,3
	.word	32403
	.byte	10
	.byte	'_Ifx_SCU_FMR_Bits',0,10,177,4,16,4,11
	.byte	'FS0',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'FS1',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'FS2',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'FS3',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'FS4',0,1
	.word	494
	.byte	1,3,2,35,0,11
	.byte	'FS5',0,1
	.word	494
	.byte	1,2,2,35,0,11
	.byte	'FS6',0,1
	.word	494
	.byte	1,1,2,35,0,11
	.byte	'FS7',0,1
	.word	494
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	494
	.byte	8,0,2,35,1,11
	.byte	'FC0',0,1
	.word	494
	.byte	1,7,2,35,2,11
	.byte	'FC1',0,1
	.word	494
	.byte	1,6,2,35,2,11
	.byte	'FC2',0,1
	.word	494
	.byte	1,5,2,35,2,11
	.byte	'FC3',0,1
	.word	494
	.byte	1,4,2,35,2,11
	.byte	'FC4',0,1
	.word	494
	.byte	1,3,2,35,2,11
	.byte	'FC5',0,1
	.word	494
	.byte	1,2,2,35,2,11
	.byte	'FC6',0,1
	.word	494
	.byte	1,1,2,35,2,11
	.byte	'FC7',0,1
	.word	494
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	494
	.byte	8,0,2,35,3,0,21
	.byte	'Ifx_SCU_FMR_Bits',0,10,197,4,3
	.word	32566
	.byte	10
	.byte	'_Ifx_SCU_ID_Bits',0,10,200,4,16,4,11
	.byte	'MODREV',0,1
	.word	494
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	494
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	511
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_ID_Bits',0,10,205,4,3
	.word	32902
	.byte	10
	.byte	'_Ifx_SCU_IGCR_Bits',0,10,208,4,16,4,11
	.byte	'IPEN00',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'IPEN01',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'IPEN02',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'IPEN03',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'IPEN04',0,1
	.word	494
	.byte	1,3,2,35,0,11
	.byte	'IPEN05',0,1
	.word	494
	.byte	1,2,2,35,0,11
	.byte	'IPEN06',0,1
	.word	494
	.byte	1,1,2,35,0,11
	.byte	'IPEN07',0,1
	.word	494
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	494
	.byte	5,3,2,35,1,11
	.byte	'GEEN0',0,1
	.word	494
	.byte	1,2,2,35,1,11
	.byte	'IGP0',0,1
	.word	494
	.byte	2,0,2,35,1,11
	.byte	'IPEN10',0,1
	.word	494
	.byte	1,7,2,35,2,11
	.byte	'IPEN11',0,1
	.word	494
	.byte	1,6,2,35,2,11
	.byte	'IPEN12',0,1
	.word	494
	.byte	1,5,2,35,2,11
	.byte	'IPEN13',0,1
	.word	494
	.byte	1,4,2,35,2,11
	.byte	'IPEN14',0,1
	.word	494
	.byte	1,3,2,35,2,11
	.byte	'IPEN15',0,1
	.word	494
	.byte	1,2,2,35,2,11
	.byte	'IPEN16',0,1
	.word	494
	.byte	1,1,2,35,2,11
	.byte	'IPEN17',0,1
	.word	494
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	494
	.byte	5,3,2,35,3,11
	.byte	'GEEN1',0,1
	.word	494
	.byte	1,2,2,35,3,11
	.byte	'IGP1',0,1
	.word	494
	.byte	2,0,2,35,3,0,21
	.byte	'Ifx_SCU_IGCR_Bits',0,10,232,4,3
	.word	33009
	.byte	10
	.byte	'_Ifx_SCU_IN_Bits',0,10,235,4,16,4,11
	.byte	'P0',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	471
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_IN_Bits',0,10,240,4,3
	.word	33461
	.byte	10
	.byte	'_Ifx_SCU_IOCR_Bits',0,10,243,4,16,4,11
	.byte	'reserved_0',0,1
	.word	494
	.byte	4,4,2,35,0,11
	.byte	'PC0',0,1
	.word	494
	.byte	4,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	494
	.byte	4,4,2,35,1,11
	.byte	'PC1',0,1
	.word	494
	.byte	4,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	511
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_IOCR_Bits',0,10,250,4,3
	.word	33560
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL0_Bits',0,10,253,4,16,4,11
	.byte	'LBISTREQ',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'LBISTREQP',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'PATTERNS',0,2
	.word	511
	.byte	14,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	511
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_LBISTCTRL0_Bits',0,10,131,5,3
	.word	33710
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL1_Bits',0,10,134,5,16,4,11
	.byte	'SEED',0,4
	.word	471
	.byte	23,9,2,35,0,11
	.byte	'reserved_23',0,1
	.word	494
	.byte	1,0,2,35,2,11
	.byte	'SPLITSH',0,1
	.word	494
	.byte	3,5,2,35,3,11
	.byte	'BODY',0,1
	.word	494
	.byte	1,4,2,35,3,11
	.byte	'LBISTFREQU',0,1
	.word	494
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_LBISTCTRL1_Bits',0,10,141,5,3
	.word	33859
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL2_Bits',0,10,144,5,16,4,11
	.byte	'SIGNATURE',0,4
	.word	471
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	494
	.byte	7,1,2,35,3,11
	.byte	'LBISTDONE',0,1
	.word	494
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_LBISTCTRL2_Bits',0,10,149,5,3
	.word	34020
	.byte	10
	.byte	'_Ifx_SCU_LCLCON_Bits',0,10,152,5,16,4,11
	.byte	'reserved_0',0,2
	.word	511
	.byte	16,0,2,35,0,11
	.byte	'LS',0,1
	.word	494
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,2
	.word	511
	.byte	14,1,2,35,2,11
	.byte	'LSEN',0,1
	.word	494
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_LCLCON_Bits',0,10,158,5,3
	.word	34150
	.byte	10
	.byte	'_Ifx_SCU_LCLTEST_Bits',0,10,161,5,16,4,11
	.byte	'LCLT0',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'LCLT1',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	471
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_LCLTEST_Bits',0,10,166,5,3
	.word	34282
	.byte	10
	.byte	'_Ifx_SCU_MANID_Bits',0,10,169,5,16,4,11
	.byte	'DEPT',0,1
	.word	494
	.byte	5,3,2,35,0,11
	.byte	'MANUF',0,2
	.word	511
	.byte	11,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	511
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_MANID_Bits',0,10,174,5,3
	.word	34397
	.byte	10
	.byte	'_Ifx_SCU_OMR_Bits',0,10,177,5,16,4,11
	.byte	'PS0',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	511
	.byte	14,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	494
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	494
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	511
	.byte	14,0,2,35,2,0,21
	.byte	'Ifx_SCU_OMR_Bits',0,10,185,5,3
	.word	34508
	.byte	10
	.byte	'_Ifx_SCU_OSCCON_Bits',0,10,188,5,16,4,11
	.byte	'reserved_0',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'PLLLV',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'OSCRES',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'GAINSEL',0,1
	.word	494
	.byte	2,3,2,35,0,11
	.byte	'MODE',0,1
	.word	494
	.byte	2,1,2,35,0,11
	.byte	'SHBY',0,1
	.word	494
	.byte	1,0,2,35,0,11
	.byte	'PLLHV',0,1
	.word	494
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	494
	.byte	1,6,2,35,1,11
	.byte	'X1D',0,1
	.word	494
	.byte	1,5,2,35,1,11
	.byte	'X1DEN',0,1
	.word	494
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	494
	.byte	4,0,2,35,1,11
	.byte	'OSCVAL',0,1
	.word	494
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	494
	.byte	2,1,2,35,2,11
	.byte	'APREN',0,1
	.word	494
	.byte	1,0,2,35,2,11
	.byte	'CAP0EN',0,1
	.word	494
	.byte	1,7,2,35,3,11
	.byte	'CAP1EN',0,1
	.word	494
	.byte	1,6,2,35,3,11
	.byte	'CAP2EN',0,1
	.word	494
	.byte	1,5,2,35,3,11
	.byte	'CAP3EN',0,1
	.word	494
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	494
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_OSCCON_Bits',0,10,209,5,3
	.word	34666
	.byte	10
	.byte	'_Ifx_SCU_OUT_Bits',0,10,212,5,16,4,11
	.byte	'P0',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	471
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_OUT_Bits',0,10,217,5,3
	.word	35078
	.byte	10
	.byte	'_Ifx_SCU_OVCCON_Bits',0,10,220,5,16,4,11
	.byte	'CSEL0',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'CSEL1',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'CSEL2',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,2
	.word	511
	.byte	13,0,2,35,0,11
	.byte	'OVSTRT',0,1
	.word	494
	.byte	1,7,2,35,2,11
	.byte	'OVSTP',0,1
	.word	494
	.byte	1,6,2,35,2,11
	.byte	'DCINVAL',0,1
	.word	494
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	494
	.byte	5,0,2,35,2,11
	.byte	'OVCONF',0,1
	.word	494
	.byte	1,7,2,35,3,11
	.byte	'POVCONF',0,1
	.word	494
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	494
	.byte	6,0,2,35,3,0,21
	.byte	'Ifx_SCU_OVCCON_Bits',0,10,233,5,3
	.word	35179
	.byte	10
	.byte	'_Ifx_SCU_OVCENABLE_Bits',0,10,236,5,16,4,11
	.byte	'OVEN0',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'OVEN1',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'OVEN2',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	471
	.byte	29,0,2,35,0,0,21
	.byte	'Ifx_SCU_OVCENABLE_Bits',0,10,242,5,3
	.word	35446
	.byte	10
	.byte	'_Ifx_SCU_PDISC_Bits',0,10,245,5,16,4,11
	.byte	'PDIS0',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	471
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_PDISC_Bits',0,10,250,5,3
	.word	35582
	.byte	10
	.byte	'_Ifx_SCU_PDR_Bits',0,10,253,5,16,4,11
	.byte	'PD0',0,1
	.word	494
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	494
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	494
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	471
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_SCU_PDR_Bits',0,10,132,6,3
	.word	35693
	.byte	10
	.byte	'_Ifx_SCU_PDRR_Bits',0,10,135,6,16,4,11
	.byte	'PDR0',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'PDR1',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'PDR2',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'PDR3',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'PDR4',0,1
	.word	494
	.byte	1,3,2,35,0,11
	.byte	'PDR5',0,1
	.word	494
	.byte	1,2,2,35,0,11
	.byte	'PDR6',0,1
	.word	494
	.byte	1,1,2,35,0,11
	.byte	'PDR7',0,1
	.word	494
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	471
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_SCU_PDRR_Bits',0,10,146,6,3
	.word	35826
	.byte	10
	.byte	'_Ifx_SCU_PLLCON0_Bits',0,10,149,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'MODEN',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	494
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	494
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	494
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	511
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	494
	.byte	7,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	494
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	494
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	494
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	494
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	494
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	494
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_PLLCON0_Bits',0,10,166,6,3
	.word	36029
	.byte	10
	.byte	'_Ifx_SCU_PLLCON1_Bits',0,10,169,6,16,4,11
	.byte	'K2DIV',0,1
	.word	494
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	494
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	494
	.byte	7,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	494
	.byte	1,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	494
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	511
	.byte	9,0,2,35,2,0,21
	.byte	'Ifx_SCU_PLLCON1_Bits',0,10,177,6,3
	.word	36385
	.byte	10
	.byte	'_Ifx_SCU_PLLCON2_Bits',0,10,180,6,16,4,11
	.byte	'MODCFG',0,2
	.word	511
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	511
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_PLLCON2_Bits',0,10,184,6,3
	.word	36563
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON0_Bits',0,10,187,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	494
	.byte	2,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	494
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	494
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	494
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	511
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	494
	.byte	5,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	494
	.byte	2,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	494
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	494
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	494
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	494
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	494
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	494
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_PLLERAYCON0_Bits',0,10,204,6,3
	.word	36663
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON1_Bits',0,10,207,6,16,4,11
	.byte	'K2DIV',0,1
	.word	494
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	494
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	494
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	494
	.byte	4,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	494
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	511
	.byte	9,0,2,35,2,0,21
	.byte	'Ifx_SCU_PLLERAYCON1_Bits',0,10,215,6,3
	.word	37033
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYSTAT_Bits',0,10,218,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'PWDSTAT',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	494
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	494
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	471
	.byte	26,0,2,35,0,0,21
	.byte	'Ifx_SCU_PLLERAYSTAT_Bits',0,10,227,6,3
	.word	37219
	.byte	10
	.byte	'_Ifx_SCU_PLLSTAT_Bits',0,10,230,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	494
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	494
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	494
	.byte	1,1,2,35,0,11
	.byte	'MODRUN',0,1
	.word	494
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	471
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_SCU_PLLSTAT_Bits',0,10,241,6,3
	.word	37417
	.byte	10
	.byte	'_Ifx_SCU_PMCSR_Bits',0,10,244,6,16,4,11
	.byte	'REQSLP',0,1
	.word	494
	.byte	2,6,2,35,0,11
	.byte	'SMUSLP',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	494
	.byte	5,0,2,35,0,11
	.byte	'PMST',0,1
	.word	494
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	471
	.byte	21,0,2,35,0,0,21
	.byte	'Ifx_SCU_PMCSR_Bits',0,10,251,6,3
	.word	37650
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR0_Bits',0,10,254,6,16,4,11
	.byte	'reserved_0',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'ESR1WKEN',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'PINAWKEN',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'PINBWKEN',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'ESR0DFEN',0,1
	.word	494
	.byte	1,3,2,35,0,11
	.byte	'ESR0EDCON',0,1
	.word	494
	.byte	2,1,2,35,0,11
	.byte	'ESR1DFEN',0,1
	.word	494
	.byte	1,0,2,35,0,11
	.byte	'ESR1EDCON',0,1
	.word	494
	.byte	2,6,2,35,1,11
	.byte	'PINADFEN',0,1
	.word	494
	.byte	1,5,2,35,1,11
	.byte	'PINAEDCON',0,1
	.word	494
	.byte	2,3,2,35,1,11
	.byte	'PINBDFEN',0,1
	.word	494
	.byte	1,2,2,35,1,11
	.byte	'PINBEDCON',0,1
	.word	494
	.byte	2,0,2,35,1,11
	.byte	'SCREN',0,1
	.word	494
	.byte	1,7,2,35,2,11
	.byte	'STBYRAMSEL',0,1
	.word	494
	.byte	2,5,2,35,2,11
	.byte	'SCRCLKSEL',0,1
	.word	494
	.byte	1,4,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	494
	.byte	1,3,2,35,2,11
	.byte	'TRISTEN',0,1
	.word	494
	.byte	1,2,2,35,2,11
	.byte	'TRISTREQ',0,1
	.word	494
	.byte	1,1,2,35,2,11
	.byte	'PORSTDF',0,1
	.word	494
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	494
	.byte	1,7,2,35,3,11
	.byte	'DCDCSYNC',0,1
	.word	494
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	494
	.byte	3,3,2,35,3,11
	.byte	'ESR0TRIST',0,1
	.word	494
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	494
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	494
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_PMSWCR0_Bits',0,10,153,7,3
	.word	37802
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR1_Bits',0,10,156,7,16,4,11
	.byte	'SCRSTEN',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'SCRSTREQ',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	494
	.byte	6,0,2,35,0,11
	.byte	'CPUIDLSEL',0,1
	.word	494
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	494
	.byte	1,4,2,35,1,11
	.byte	'IRADIS',0,1
	.word	494
	.byte	1,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	494
	.byte	3,0,2,35,1,11
	.byte	'SCRCFG',0,1
	.word	494
	.byte	8,0,2,35,2,11
	.byte	'CPUSEL',0,1
	.word	494
	.byte	3,5,2,35,3,11
	.byte	'STBYEVEN',0,1
	.word	494
	.byte	1,4,2,35,3,11
	.byte	'STBYEV',0,1
	.word	494
	.byte	3,1,2,35,3,11
	.byte	'LCK',0,1
	.word	494
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_PMSWCR1_Bits',0,10,170,7,3
	.word	38369
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR2_Bits',0,10,173,7,16,4,11
	.byte	'SCRINT',0,1
	.word	494
	.byte	8,0,2,35,0,11
	.byte	'BUSY',0,1
	.word	494
	.byte	1,7,2,35,1,11
	.byte	'SCRECC',0,1
	.word	494
	.byte	1,6,2,35,1,11
	.byte	'SCRWDT',0,1
	.word	494
	.byte	1,5,2,35,1,11
	.byte	'SCRRST',0,1
	.word	494
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	494
	.byte	4,0,2,35,1,11
	.byte	'TCINT',0,1
	.word	494
	.byte	8,0,2,35,2,11
	.byte	'TCINTREQ',0,1
	.word	494
	.byte	1,7,2,35,3,11
	.byte	'SMURST',0,1
	.word	494
	.byte	1,6,2,35,3,11
	.byte	'RST',0,1
	.word	494
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	494
	.byte	4,1,2,35,3,11
	.byte	'LCK',0,1
	.word	494
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_PMSWCR2_Bits',0,10,187,7,3
	.word	38663
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTAT_Bits',0,10,190,7,16,4,11
	.byte	'reserved_0',0,1
	.word	494
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKP',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUN',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'PINAWKP',0,1
	.word	494
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUN',0,1
	.word	494
	.byte	1,2,2,35,0,11
	.byte	'PINBWKP',0,1
	.word	494
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUN',0,1
	.word	494
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	494
	.byte	1,7,2,35,1,11
	.byte	'PORSTDF',0,1
	.word	494
	.byte	1,6,2,35,1,11
	.byte	'HWCFGEVR',0,1
	.word	494
	.byte	3,3,2,35,1,11
	.byte	'STBYRAM',0,1
	.word	494
	.byte	2,1,2,35,1,11
	.byte	'TRIST',0,1
	.word	494
	.byte	1,0,2,35,1,11
	.byte	'SCRST',0,1
	.word	494
	.byte	1,7,2,35,2,11
	.byte	'SCRWKP',0,1
	.word	494
	.byte	1,6,2,35,2,11
	.byte	'SCR',0,1
	.word	494
	.byte	1,5,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	494
	.byte	1,4,2,35,2,11
	.byte	'ESR1WKEN',0,1
	.word	494
	.byte	1,3,2,35,2,11
	.byte	'PINAWKEN',0,1
	.word	494
	.byte	1,2,2,35,2,11
	.byte	'PINBWKEN',0,1
	.word	494
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	511
	.byte	4,5,2,35,2,11
	.byte	'ESR0TRIST',0,1
	.word	494
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	494
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_PMSWSTAT_Bits',0,10,214,7,3
	.word	38941
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTATCLR_Bits',0,10,217,7,16,4,11
	.byte	'reserved_0',0,1
	.word	494
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKPCLR',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUNCLR',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'PINAWKPCLR',0,1
	.word	494
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUNCLR',0,1
	.word	494
	.byte	1,2,2,35,0,11
	.byte	'PINBWKPCLR',0,1
	.word	494
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUNCLR',0,1
	.word	494
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	494
	.byte	8,0,2,35,1,11
	.byte	'SCRSTCLR',0,1
	.word	494
	.byte	1,7,2,35,2,11
	.byte	'SCRWKPCLR',0,1
	.word	494
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	511
	.byte	14,0,2,35,2,0,21
	.byte	'Ifx_SCU_PMSWSTATCLR_Bits',0,10,230,7,3
	.word	39437
	.byte	10
	.byte	'_Ifx_SCU_RSTCON2_Bits',0,10,233,7,16,4,11
	.byte	'reserved_0',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'CLRC',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	511
	.byte	10,4,2,35,0,11
	.byte	'CSS0',0,1
	.word	494
	.byte	1,3,2,35,1,11
	.byte	'CSS1',0,1
	.word	494
	.byte	1,2,2,35,1,11
	.byte	'CSS2',0,1
	.word	494
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	494
	.byte	1,0,2,35,1,11
	.byte	'USRINFO',0,2
	.word	511
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_RSTCON2_Bits',0,10,243,7,3
	.word	39750
	.byte	10
	.byte	'_Ifx_SCU_RSTCON_Bits',0,10,246,7,16,4,11
	.byte	'ESR0',0,1
	.word	494
	.byte	2,6,2,35,0,11
	.byte	'ESR1',0,1
	.word	494
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	494
	.byte	2,2,2,35,0,11
	.byte	'SMU',0,1
	.word	494
	.byte	2,0,2,35,0,11
	.byte	'SW',0,1
	.word	494
	.byte	2,6,2,35,1,11
	.byte	'STM0',0,1
	.word	494
	.byte	2,4,2,35,1,11
	.byte	'STM1',0,1
	.word	494
	.byte	2,2,2,35,1,11
	.byte	'STM2',0,1
	.word	494
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	511
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_RSTCON_Bits',0,10,129,8,3
	.word	39959
	.byte	10
	.byte	'_Ifx_SCU_RSTSTAT_Bits',0,10,132,8,16,4,11
	.byte	'ESR0',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'ESR1',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'SMU',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'SW',0,1
	.word	494
	.byte	1,3,2,35,0,11
	.byte	'STM0',0,1
	.word	494
	.byte	1,2,2,35,0,11
	.byte	'STM1',0,1
	.word	494
	.byte	1,1,2,35,0,11
	.byte	'STM2',0,1
	.word	494
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	494
	.byte	8,0,2,35,1,11
	.byte	'PORST',0,1
	.word	494
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	494
	.byte	1,6,2,35,2,11
	.byte	'CB0',0,1
	.word	494
	.byte	1,5,2,35,2,11
	.byte	'CB1',0,1
	.word	494
	.byte	1,4,2,35,2,11
	.byte	'CB3',0,1
	.word	494
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	494
	.byte	2,1,2,35,2,11
	.byte	'EVR13',0,1
	.word	494
	.byte	1,0,2,35,2,11
	.byte	'EVR33',0,1
	.word	494
	.byte	1,7,2,35,3,11
	.byte	'SWD',0,1
	.word	494
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	494
	.byte	2,4,2,35,3,11
	.byte	'STBYR',0,1
	.word	494
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	494
	.byte	3,0,2,35,3,0,21
	.byte	'Ifx_SCU_RSTSTAT_Bits',0,10,155,8,3
	.word	40170
	.byte	10
	.byte	'_Ifx_SCU_SAFECON_Bits',0,10,158,8,16,4,11
	.byte	'HBT',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	471
	.byte	31,0,2,35,0,0,21
	.byte	'Ifx_SCU_SAFECON_Bits',0,10,162,8,3
	.word	40602
	.byte	10
	.byte	'_Ifx_SCU_STSTAT_Bits',0,10,165,8,16,4,11
	.byte	'HWCFG',0,1
	.word	494
	.byte	8,0,2,35,0,11
	.byte	'FTM',0,1
	.word	494
	.byte	7,1,2,35,1,11
	.byte	'MODE',0,1
	.word	494
	.byte	1,0,2,35,1,11
	.byte	'FCBAE',0,1
	.word	494
	.byte	1,7,2,35,2,11
	.byte	'LUDIS',0,1
	.word	494
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	494
	.byte	1,5,2,35,2,11
	.byte	'TRSTL',0,1
	.word	494
	.byte	1,4,2,35,2,11
	.byte	'SPDEN',0,1
	.word	494
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	494
	.byte	3,0,2,35,2,11
	.byte	'RAMINT',0,1
	.word	494
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	494
	.byte	7,0,2,35,3,0,21
	.byte	'Ifx_SCU_STSTAT_Bits',0,10,178,8,3
	.word	40698
	.byte	10
	.byte	'_Ifx_SCU_SWRSTCON_Bits',0,10,181,8,16,4,11
	.byte	'reserved_0',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'SWRSTREQ',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	471
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_SWRSTCON_Bits',0,10,186,8,3
	.word	40958
	.byte	10
	.byte	'_Ifx_SCU_SYSCON_Bits',0,10,189,8,16,4,11
	.byte	'CCTRIG0',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'RAMINTM',0,1
	.word	494
	.byte	2,4,2,35,0,11
	.byte	'SETLUDIS',0,1
	.word	494
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	494
	.byte	3,0,2,35,0,11
	.byte	'DATM',0,1
	.word	494
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,4
	.word	471
	.byte	23,0,2,35,0,0,21
	.byte	'Ifx_SCU_SYSCON_Bits',0,10,198,8,3
	.word	41083
	.byte	10
	.byte	'_Ifx_SCU_TRAPCLR_Bits',0,10,201,8,16,4,11
	.byte	'ESR0T',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	471
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPCLR_Bits',0,10,208,8,3
	.word	41280
	.byte	10
	.byte	'_Ifx_SCU_TRAPDIS_Bits',0,10,211,8,16,4,11
	.byte	'ESR0T',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	471
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPDIS_Bits',0,10,218,8,3
	.word	41433
	.byte	10
	.byte	'_Ifx_SCU_TRAPSET_Bits',0,10,221,8,16,4,11
	.byte	'ESR0T',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	471
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPSET_Bits',0,10,228,8,3
	.word	41586
	.byte	10
	.byte	'_Ifx_SCU_TRAPSTAT_Bits',0,10,231,8,16,4,11
	.byte	'ESR0T',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	471
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPSTAT_Bits',0,10,238,8,3
	.word	41739
	.byte	7
	.byte	'unsigned int',0,4,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON0_Bits',0,10,241,8,16,4,11
	.byte	'ENDINIT',0,4
	.word	41894
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	41894
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	41894
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	41894
	.byte	16,0,2,35,0,0,21
	.byte	'Ifx_SCU_WDTCPU_CON0_Bits',0,10,247,8,3
	.word	41910
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU_CON1_Bits',0,10,250,8,16,4,11
	.byte	'reserved_0',0,1
	.word	494
	.byte	2,6,2,35,0,11
	.byte	'IR0',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	494
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	494
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	494
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	494
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	494
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	494
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	511
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_WDTCPU_CON1_Bits',0,10,134,9,3
	.word	42040
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU_SR_Bits',0,10,137,9,16,4,11
	.byte	'AE',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	494
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	494
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	494
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	494
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	494
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	494
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	511
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_WDTCPU_SR_Bits',0,10,150,9,3
	.word	42278
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON0_Bits',0,10,153,9,16,4,11
	.byte	'ENDINIT',0,4
	.word	41894
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	41894
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	41894
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	41894
	.byte	16,0,2,35,0,0,21
	.byte	'Ifx_SCU_WDTS_CON0_Bits',0,10,159,9,3
	.word	42501
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON1_Bits',0,10,162,9,16,4,11
	.byte	'CLRIRF',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'IR0',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	494
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	494
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	494
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	494
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	494
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	494
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	511
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_WDTS_CON1_Bits',0,10,175,9,3
	.word	42627
	.byte	10
	.byte	'_Ifx_SCU_WDTS_SR_Bits',0,10,178,9,16,4,11
	.byte	'AE',0,1
	.word	494
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	494
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	494
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	494
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	494
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	494
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	494
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	494
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	494
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	494
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	511
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_WDTS_SR_Bits',0,10,191,9,3
	.word	42879
	.byte	12,10,199,9,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24404
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ACCEN0',0,10,204,9,3
	.word	43098
	.byte	12,10,207,9,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24961
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ACCEN1',0,10,212,9,3
	.word	43162
	.byte	12,10,215,9,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25038
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ARSTDIS',0,10,220,9,3
	.word	43226
	.byte	12,10,223,9,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25174
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON0',0,10,228,9,3
	.word	43291
	.byte	12,10,231,9,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25454
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON1',0,10,236,9,3
	.word	43356
	.byte	12,10,239,9,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25692
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON2',0,10,244,9,3
	.word	43421
	.byte	12,10,247,9,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25820
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON3',0,10,252,9,3
	.word	43486
	.byte	12,10,255,9,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26063
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON4',0,10,132,10,3
	.word	43551
	.byte	12,10,135,10,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26298
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON5',0,10,140,10,3
	.word	43616
	.byte	12,10,143,10,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26426
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON6',0,10,148,10,3
	.word	43681
	.byte	12,10,151,10,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26526
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON7',0,10,156,10,3
	.word	43746
	.byte	12,10,159,10,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26626
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CHIPID',0,10,164,10,3
	.word	43811
	.byte	12,10,167,10,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26834
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_DTSCON',0,10,172,10,3
	.word	43875
	.byte	12,10,175,10,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26999
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_DTSLIM',0,10,180,10,3
	.word	43939
	.byte	12,10,183,10,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27182
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_DTSSTAT',0,10,188,10,3
	.word	44003
	.byte	12,10,191,10,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27336
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EICR',0,10,196,10,3
	.word	44068
	.byte	12,10,199,10,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27700
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EIFR',0,10,204,10,3
	.word	44130
	.byte	12,10,207,10,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27911
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EMSR',0,10,212,10,3
	.word	44192
	.byte	12,10,215,10,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28163
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ESRCFG',0,10,220,10,3
	.word	44254
	.byte	12,10,223,10,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28281
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ESROCFG',0,10,228,10,3
	.word	44318
	.byte	12,10,231,10,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28392
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVR13CON',0,10,236,10,3
	.word	44383
	.byte	12,10,239,10,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28555
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVR33CON',0,10,244,10,3
	.word	44449
	.byte	12,10,247,10,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28718
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRADCSTAT',0,10,252,10,3
	.word	44515
	.byte	12,10,255,10,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28876
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRDVSTAT',0,10,132,11,3
	.word	44583
	.byte	12,10,135,11,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29041
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRMONCTRL',0,10,140,11,3
	.word	44650
	.byte	12,10,143,11,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29370
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVROSCCTRL',0,10,148,11,3
	.word	44718
	.byte	12,10,151,11,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29591
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVROVMON',0,10,156,11,3
	.word	44786
	.byte	12,10,159,11,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29754
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRRSTCON',0,10,164,11,3
	.word	44852
	.byte	12,10,167,11,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30026
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF1',0,10,172,11,3
	.word	44919
	.byte	12,10,175,11,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30179
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF2',0,10,180,11,3
	.word	44988
	.byte	12,10,183,11,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30335
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF3',0,10,188,11,3
	.word	45057
	.byte	12,10,191,11,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30497
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF4',0,10,196,11,3
	.word	45126
	.byte	12,10,199,11,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30640
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF5',0,10,204,11,3
	.word	45195
	.byte	12,10,207,11,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30805
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF6',0,10,212,11,3
	.word	45264
	.byte	12,10,215,11,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30950
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCTRL1',0,10,220,11,3
	.word	45333
	.byte	12,10,223,11,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31131
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCTRL2',0,10,228,11,3
	.word	45401
	.byte	12,10,231,11,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31305
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCTRL3',0,10,236,11,3
	.word	45469
	.byte	12,10,239,11,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31465
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCTRL4',0,10,244,11,3
	.word	45537
	.byte	12,10,247,11,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31609
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSTAT',0,10,252,11,3
	.word	45605
	.byte	12,10,255,11,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31883
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRTRIM',0,10,132,12,3
	.word	45670
	.byte	12,10,135,12,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32022
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRUVMON',0,10,140,12,3
	.word	45735
	.byte	12,10,143,12,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32185
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EXTCON',0,10,148,12,3
	.word	45801
	.byte	12,10,151,12,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32403
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_FDR',0,10,156,12,3
	.word	45865
	.byte	12,10,159,12,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32566
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_FMR',0,10,164,12,3
	.word	45926
	.byte	12,10,167,12,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32902
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ID',0,10,172,12,3
	.word	45987
	.byte	12,10,175,12,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33009
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_IGCR',0,10,180,12,3
	.word	46047
	.byte	12,10,183,12,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33461
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_IN',0,10,188,12,3
	.word	46109
	.byte	12,10,191,12,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33560
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_IOCR',0,10,196,12,3
	.word	46169
	.byte	12,10,199,12,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33710
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_LBISTCTRL0',0,10,204,12,3
	.word	46231
	.byte	12,10,207,12,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33859
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_LBISTCTRL1',0,10,212,12,3
	.word	46299
	.byte	12,10,215,12,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34020
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_LBISTCTRL2',0,10,220,12,3
	.word	46367
	.byte	12,10,223,12,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34150
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_LCLCON',0,10,228,12,3
	.word	46435
	.byte	12,10,231,12,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34282
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_LCLTEST',0,10,236,12,3
	.word	46499
	.byte	12,10,239,12,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34397
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_MANID',0,10,244,12,3
	.word	46564
	.byte	12,10,247,12,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34508
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_OMR',0,10,252,12,3
	.word	46627
	.byte	12,10,255,12,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34666
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_OSCCON',0,10,132,13,3
	.word	46688
	.byte	12,10,135,13,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35078
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_OUT',0,10,140,13,3
	.word	46752
	.byte	12,10,143,13,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35179
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_OVCCON',0,10,148,13,3
	.word	46813
	.byte	12,10,151,13,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35446
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_OVCENABLE',0,10,156,13,3
	.word	46877
	.byte	12,10,159,13,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35582
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PDISC',0,10,164,13,3
	.word	46944
	.byte	12,10,167,13,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35693
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PDR',0,10,172,13,3
	.word	47007
	.byte	12,10,175,13,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35826
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PDRR',0,10,180,13,3
	.word	47068
	.byte	12,10,183,13,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36029
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLCON0',0,10,188,13,3
	.word	47130
	.byte	12,10,191,13,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36385
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLCON1',0,10,196,13,3
	.word	47195
	.byte	12,10,199,13,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36563
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLCON2',0,10,204,13,3
	.word	47260
	.byte	12,10,207,13,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36663
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLERAYCON0',0,10,212,13,3
	.word	47325
	.byte	12,10,215,13,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37033
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLERAYCON1',0,10,220,13,3
	.word	47394
	.byte	12,10,223,13,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37219
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLERAYSTAT',0,10,228,13,3
	.word	47463
	.byte	12,10,231,13,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37417
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLSTAT',0,10,236,13,3
	.word	47532
	.byte	12,10,239,13,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37650
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMCSR',0,10,244,13,3
	.word	47597
	.byte	12,10,247,13,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37802
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMSWCR0',0,10,252,13,3
	.word	47660
	.byte	12,10,255,13,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38369
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMSWCR1',0,10,132,14,3
	.word	47725
	.byte	12,10,135,14,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38663
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMSWCR2',0,10,140,14,3
	.word	47790
	.byte	12,10,143,14,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38941
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMSWSTAT',0,10,148,14,3
	.word	47855
	.byte	12,10,151,14,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39437
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMSWSTATCLR',0,10,156,14,3
	.word	47921
	.byte	12,10,159,14,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39959
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_RSTCON',0,10,164,14,3
	.word	47990
	.byte	12,10,167,14,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39750
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_RSTCON2',0,10,172,14,3
	.word	48054
	.byte	12,10,175,14,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40170
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_RSTSTAT',0,10,180,14,3
	.word	48119
	.byte	12,10,183,14,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40602
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_SAFECON',0,10,188,14,3
	.word	48184
	.byte	12,10,191,14,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40698
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_STSTAT',0,10,196,14,3
	.word	48249
	.byte	12,10,199,14,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40958
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_SWRSTCON',0,10,204,14,3
	.word	48313
	.byte	12,10,207,14,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41083
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_SYSCON',0,10,212,14,3
	.word	48379
	.byte	12,10,215,14,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41280
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPCLR',0,10,220,14,3
	.word	48443
	.byte	12,10,223,14,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41433
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPDIS',0,10,228,14,3
	.word	48508
	.byte	12,10,231,14,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41586
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPSET',0,10,236,14,3
	.word	48573
	.byte	12,10,239,14,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41739
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPSTAT',0,10,244,14,3
	.word	48638
	.byte	12,10,247,14,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41910
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTCPU_CON0',0,10,252,14,3
	.word	48704
	.byte	12,10,255,14,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42040
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTCPU_CON1',0,10,132,15,3
	.word	48773
	.byte	12,10,135,15,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42278
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTCPU_SR',0,10,140,15,3
	.word	48842
	.byte	12,10,143,15,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42501
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTS_CON0',0,10,148,15,3
	.word	48909
	.byte	12,10,151,15,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42627
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTS_CON1',0,10,156,15,3
	.word	48976
	.byte	12,10,159,15,9,4,13
	.byte	'U',0
	.word	471
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	487
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42879
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTS_SR',0,10,164,15,3
	.word	49043
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU',0,10,175,15,25,12,13
	.byte	'CON0',0
	.word	48704
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	48773
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	48842
	.byte	4,2,35,8,0,16
	.word	49108
	.byte	21
	.byte	'Ifx_SCU_WDTCPU',0,10,180,15,3
	.word	49171
	.byte	10
	.byte	'_Ifx_SCU_WDTS',0,10,183,15,25,12,13
	.byte	'CON0',0
	.word	48909
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	48976
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	49043
	.byte	4,2,35,8,0,16
	.word	49200
	.byte	21
	.byte	'Ifx_SCU_WDTS',0,10,188,15,3
	.word	49261
	.byte	17,3,83,9,1,18
	.byte	'IfxPort_InputMode_undefined',0,127,18
	.byte	'IfxPort_InputMode_noPullDevice',0,0,18
	.byte	'IfxPort_InputMode_pullDown',0,8,18
	.byte	'IfxPort_InputMode_pullUp',0,16,0,21
	.byte	'IfxPort_InputMode',0,3,89,3
	.word	49288
	.byte	17,3,120,9,1,18
	.byte	'IfxPort_OutputIdx_general',0,128,1,18
	.byte	'IfxPort_OutputIdx_alt1',0,136,1,18
	.byte	'IfxPort_OutputIdx_alt2',0,144,1,18
	.byte	'IfxPort_OutputIdx_alt3',0,152,1,18
	.byte	'IfxPort_OutputIdx_alt4',0,160,1,18
	.byte	'IfxPort_OutputIdx_alt5',0,168,1,18
	.byte	'IfxPort_OutputIdx_alt6',0,176,1,18
	.byte	'IfxPort_OutputIdx_alt7',0,184,1,0,21
	.byte	'IfxPort_OutputIdx',0,3,130,1,3
	.word	49439
	.byte	17,3,134,1,9,1,18
	.byte	'IfxPort_OutputMode_pushPull',0,128,1,18
	.byte	'IfxPort_OutputMode_openDrain',0,192,1,0,21
	.byte	'IfxPort_OutputMode',0,3,138,1,3
	.word	49683
	.byte	17,3,144,1,9,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed1',0,0,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed2',0,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed3',0,2,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed4',0,3,18
	.byte	'IfxPort_PadDriver_lvdsSpeed1',0,4,18
	.byte	'IfxPort_PadDriver_lvdsSpeed2',0,5,18
	.byte	'IfxPort_PadDriver_lvdsSpeed3',0,6,18
	.byte	'IfxPort_PadDriver_lvdsSpeed4',0,7,18
	.byte	'IfxPort_PadDriver_ttlSpeed1',0,8,18
	.byte	'IfxPort_PadDriver_ttlSpeed2',0,9,18
	.byte	'IfxPort_PadDriver_ttlSpeed3',0,10,18
	.byte	'IfxPort_PadDriver_ttlSpeed4',0,11,0,21
	.byte	'IfxPort_PadDriver',0,3,158,1,3
	.word	49781
	.byte	21
	.byte	'IfxPort_State',0,3,178,1,3
	.word	8291
	.byte	23,3,190,1,9,8,13
	.byte	'port',0
	.word	8286
	.byte	4,2,35,0,13
	.byte	'pinIndex',0
	.word	494
	.byte	1,2,35,4,0,21
	.byte	'IfxPort_Pin',0,3,194,1,3
	.word	50246
	.byte	16
	.word	21617
	.byte	3
	.word	50306
	.byte	23,11,59,15,16,13
	.byte	'module',0
	.word	50311
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	50246
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	22806
	.byte	1,2,35,12,0,24
	.word	50316
	.byte	21
	.byte	'IfxPsi5s_Rx_In',0,11,64,3
	.word	50367
	.byte	23,11,67,15,16,13
	.byte	'module',0
	.word	50311
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	50246
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	49439
	.byte	1,2,35,12,0,24
	.word	50395
	.byte	21
	.byte	'IfxPsi5s_Tx_Out',0,11,72,3
	.word	50446
	.byte	23,11,75,15,16,13
	.byte	'module',0
	.word	50311
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	50246
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	49439
	.byte	1,2,35,12,0,24
	.word	50475
	.byte	21
	.byte	'IfxPsi5s_Clk_Out',0,11,80,3
	.word	50526
.L28:
	.byte	24
	.word	50475
.L29:
	.byte	24
	.word	50475
.L30:
	.byte	24
	.word	50316
.L31:
	.byte	24
	.word	50316
.L32:
	.byte	24
	.word	50316
.L33:
	.byte	24
	.word	50395
.L34:
	.byte	24
	.word	50395
.L35:
	.byte	24
	.word	50395
	.byte	24
	.word	50475
	.byte	3
	.word	50596
	.byte	14,8
	.word	50601
	.byte	15,1,0
.L36:
	.byte	14,8
	.word	50606
	.byte	15,0,0,24
	.word	50316
	.byte	3
	.word	50624
	.byte	14,12
	.word	50629
	.byte	15,2,0
.L37:
	.byte	14,12
	.word	50634
	.byte	15,0,0,24
	.word	50395
	.byte	3
	.word	50652
	.byte	14,12
	.word	50657
	.byte	15,2,0
.L38:
	.byte	14,12
	.word	50662
	.byte	15,0,0,0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L4:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,21,0,54,15,39,12,0,0,3,15,0,73,19,0,0,4,46,1,3,8,32,13
	.byte	58,15,59,15,57,15,54,15,39,12,0,0,5,5,0,3,8,58,15,59,15,57,15,73,19,0,0,6,11,0,0,0,7,36,0,3,8,11,15,62
	.byte	15,0,0,8,46,1,3,8,32,13,58,15,59,15,57,15,73,19,54,15,39,12,0,0,9,59,0,3,8,0,0,10,19,1,3,8,58,15,59,15
	.byte	57,15,11,15,0,0,11,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,12,23,1,58,15,59,15,57,15,11,15,0,0,13,13
	.byte	0,3,8,73,19,11,15,56,9,0,0,14,1,1,11,15,73,19,0,0,15,33,0,47,15,0,0,16,53,0,73,19,0,0,17,4,1,58,15,59
	.byte	15,57,15,11,15,0,0,18,40,0,3,8,28,13,0,0,19,46,1,49,19,0,0,20,5,0,49,19,0,0,21,22,0,3,8,58,15,59,15,57
	.byte	15,73,19,0,0,22,21,0,54,15,0,0,23,19,1,58,15,59,15,57,15,11,15,0,0,24,38,0,73,19,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L5:
	.word	.L40-.L39
.L39:
	.half	3
	.word	.L42-.L41
.L41:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg',0
	.byte	0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_Intrinsics.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Port\\Std\\IfxPort.h',0
	.byte	0,0,0
	.byte	'IfxPort_regdef.h',0,1,0,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPsi5s_PinMap.c',0,0,0,0
	.byte	'IfxPsi5s_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Platform_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Ifx_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxScu_cfg.h',0
	.byte	0,0,0
	.byte	'IfxScu_regdef.h',0,1,0,0
	.byte	'..\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\_PinMap\\IfxPsi5s_PinMap.h',0,0,0,0,0
.L42:
.L40:
	.sdecl	'.debug_info',debug,cluster('IfxPsi5s_CLK_P02_4_OUT')
	.sect	'.debug_info'
.L6:
	.word	275
	.half	3
	.word	.L7
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPsi5s_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPsi5s_CLK_P02_4_OUT',0,5,48,18
	.word	.L28
	.byte	1,5,3
	.word	IfxPsi5s_CLK_P02_4_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPsi5s_CLK_P02_4_OUT')
	.sect	'.debug_abbrev'
.L7:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPsi5s_CLK_P33_10_OUT')
	.sect	'.debug_info'
.L8:
	.word	276
	.half	3
	.word	.L9
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPsi5s_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPsi5s_CLK_P33_10_OUT',0,5,49,18
	.word	.L29
	.byte	1,5,3
	.word	IfxPsi5s_CLK_P33_10_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPsi5s_CLK_P33_10_OUT')
	.sect	'.debug_abbrev'
.L9:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPsi5s_RXA_P00_3_IN')
	.sect	'.debug_info'
.L10:
	.word	274
	.half	3
	.word	.L11
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPsi5s_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPsi5s_RXA_P00_3_IN',0,5,50,16
	.word	.L30
	.byte	1,5,3
	.word	IfxPsi5s_RXA_P00_3_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPsi5s_RXA_P00_3_IN')
	.sect	'.debug_abbrev'
.L11:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPsi5s_RXB_P02_5_IN')
	.sect	'.debug_info'
.L12:
	.word	274
	.half	3
	.word	.L13
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPsi5s_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPsi5s_RXB_P02_5_IN',0,5,51,16
	.word	.L31
	.byte	1,5,3
	.word	IfxPsi5s_RXB_P02_5_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPsi5s_RXB_P02_5_IN')
	.sect	'.debug_abbrev'
.L13:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPsi5s_RXC_P33_5_IN')
	.sect	'.debug_info'
.L14:
	.word	274
	.half	3
	.word	.L15
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPsi5s_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPsi5s_RXC_P33_5_IN',0,5,52,16
	.word	.L32
	.byte	1,5,3
	.word	IfxPsi5s_RXC_P33_5_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPsi5s_RXC_P33_5_IN')
	.sect	'.debug_abbrev'
.L15:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPsi5s_TX_P00_4_OUT')
	.sect	'.debug_info'
.L16:
	.word	274
	.half	3
	.word	.L17
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPsi5s_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPsi5s_TX_P00_4_OUT',0,5,53,17
	.word	.L33
	.byte	1,5,3
	.word	IfxPsi5s_TX_P00_4_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPsi5s_TX_P00_4_OUT')
	.sect	'.debug_abbrev'
.L17:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPsi5s_TX_P02_6_OUT')
	.sect	'.debug_info'
.L18:
	.word	274
	.half	3
	.word	.L19
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPsi5s_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPsi5s_TX_P02_6_OUT',0,5,54,17
	.word	.L34
	.byte	1,5,3
	.word	IfxPsi5s_TX_P02_6_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPsi5s_TX_P02_6_OUT')
	.sect	'.debug_abbrev'
.L19:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPsi5s_TX_P33_6_OUT')
	.sect	'.debug_info'
.L20:
	.word	274
	.half	3
	.word	.L21
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPsi5s_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPsi5s_TX_P33_6_OUT',0,5,55,17
	.word	.L35
	.byte	1,5,3
	.word	IfxPsi5s_TX_P33_6_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPsi5s_TX_P33_6_OUT')
	.sect	'.debug_abbrev'
.L21:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPsi5s_Clk_Out_pinTable')
	.sect	'.debug_info'
.L22:
	.word	278
	.half	3
	.word	.L23
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPsi5s_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPsi5s_Clk_Out_pinTable',0,5,58,25
	.word	.L36
	.byte	1,5,3
	.word	IfxPsi5s_Clk_Out_pinTable
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPsi5s_Clk_Out_pinTable')
	.sect	'.debug_abbrev'
.L23:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPsi5s_Rx_In_pinTable')
	.sect	'.debug_info'
.L24:
	.word	276
	.half	3
	.word	.L25
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPsi5s_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPsi5s_Rx_In_pinTable',0,5,65,23
	.word	.L37
	.byte	1,5,3
	.word	IfxPsi5s_Rx_In_pinTable
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPsi5s_Rx_In_pinTable')
	.sect	'.debug_abbrev'
.L25:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPsi5s_Tx_Out_pinTable')
	.sect	'.debug_info'
.L26:
	.word	277
	.half	3
	.word	.L27
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPsi5s_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPsi5s_Tx_Out_pinTable',0,5,73,24
	.word	.L38
	.byte	1,5,3
	.word	IfxPsi5s_Tx_Out_pinTable
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPsi5s_Tx_Out_pinTable')
	.sect	'.debug_abbrev'
.L27:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	; Module end
