	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc15960a --dep-file=IfxPsi5_PinMap.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2\\\\xf_asr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPsi5_PinMap.src ../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPsi5_PinMap.c"
	.compiler_name		"ctc"
	;source	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPsi5_PinMap.c'

	
$TC16X
	
	.sdecl	'.rodata.IfxPsi5_PinMap.IfxPsi5_RX0A_P00_1_IN',data,rom,cluster('IfxPsi5_RX0A_P00_1_IN')
	.sect	'.rodata.IfxPsi5_PinMap.IfxPsi5_RX0A_P00_1_IN'
	.global	IfxPsi5_RX0A_P00_1_IN
	.align	4
IfxPsi5_RX0A_P00_1_IN:	.type	object
	.size	IfxPsi5_RX0A_P00_1_IN,20
	.word	-268414976
	.space	4
	.word	-268197888
	.byte	1
	.space	7
	.sdecl	'.rodata.IfxPsi5_PinMap.IfxPsi5_RX0B_P02_3_IN',data,rom,cluster('IfxPsi5_RX0B_P02_3_IN')
	.sect	'.rodata.IfxPsi5_PinMap.IfxPsi5_RX0B_P02_3_IN'
	.global	IfxPsi5_RX0B_P02_3_IN
	.align	4
IfxPsi5_RX0B_P02_3_IN:	.type	object
	.size	IfxPsi5_RX0B_P02_3_IN,20
	.word	-268414976
	.space	4
	.word	-268197376
	.byte	3
	.space	3
	.byte	1
	.space	3
	.sdecl	'.rodata.IfxPsi5_PinMap.IfxPsi5_RX0C_P33_1_IN',data,rom,cluster('IfxPsi5_RX0C_P33_1_IN')
	.sect	'.rodata.IfxPsi5_PinMap.IfxPsi5_RX0C_P33_1_IN'
	.global	IfxPsi5_RX0C_P33_1_IN
	.align	4
IfxPsi5_RX0C_P33_1_IN:	.type	object
	.size	IfxPsi5_RX0C_P33_1_IN,20
	.word	-268414976
	.space	4
	.word	-268184832
	.byte	1
	.space	3
	.byte	2
	.space	3
	.sdecl	'.rodata.IfxPsi5_PinMap.IfxPsi5_RX1A_P00_3_IN',data,rom,cluster('IfxPsi5_RX1A_P00_3_IN')
	.sect	'.rodata.IfxPsi5_PinMap.IfxPsi5_RX1A_P00_3_IN'
	.global	IfxPsi5_RX1A_P00_3_IN
	.align	4
IfxPsi5_RX1A_P00_3_IN:	.type	object
	.size	IfxPsi5_RX1A_P00_3_IN,20
	.word	-268414976
	.byte	1
	.space	3
	.word	-268197888
	.byte	3
	.space	7
	.sdecl	'.rodata.IfxPsi5_PinMap.IfxPsi5_RX1B_P02_5_IN',data,rom,cluster('IfxPsi5_RX1B_P02_5_IN')
	.sect	'.rodata.IfxPsi5_PinMap.IfxPsi5_RX1B_P02_5_IN'
	.global	IfxPsi5_RX1B_P02_5_IN
	.align	4
IfxPsi5_RX1B_P02_5_IN:	.type	object
	.size	IfxPsi5_RX1B_P02_5_IN,20
	.word	-268414976
	.byte	1
	.space	3
	.word	-268197376
	.byte	5
	.space	3
	.byte	1
	.space	3
	.sdecl	'.rodata.IfxPsi5_PinMap.IfxPsi5_RX1C_P33_3_IN',data,rom,cluster('IfxPsi5_RX1C_P33_3_IN')
	.sect	'.rodata.IfxPsi5_PinMap.IfxPsi5_RX1C_P33_3_IN'
	.global	IfxPsi5_RX1C_P33_3_IN
	.align	4
IfxPsi5_RX1C_P33_3_IN:	.type	object
	.size	IfxPsi5_RX1C_P33_3_IN,20
	.word	-268414976
	.byte	1
	.space	3
	.word	-268184832
	.byte	3
	.space	3
	.byte	2
	.space	3
	.sdecl	'.rodata.IfxPsi5_PinMap.IfxPsi5_RX2A_P00_5_IN',data,rom,cluster('IfxPsi5_RX2A_P00_5_IN')
	.sect	'.rodata.IfxPsi5_PinMap.IfxPsi5_RX2A_P00_5_IN'
	.global	IfxPsi5_RX2A_P00_5_IN
	.align	4
IfxPsi5_RX2A_P00_5_IN:	.type	object
	.size	IfxPsi5_RX2A_P00_5_IN,20
	.word	-268414976
	.byte	2
	.space	3
	.word	-268197888
	.byte	5
	.space	7
	.sdecl	'.rodata.IfxPsi5_PinMap.IfxPsi5_RX2B_P02_7_IN',data,rom,cluster('IfxPsi5_RX2B_P02_7_IN')
	.sect	'.rodata.IfxPsi5_PinMap.IfxPsi5_RX2B_P02_7_IN'
	.global	IfxPsi5_RX2B_P02_7_IN
	.align	4
IfxPsi5_RX2B_P02_7_IN:	.type	object
	.size	IfxPsi5_RX2B_P02_7_IN,20
	.word	-268414976
	.byte	2
	.space	3
	.word	-268197376
	.byte	7
	.space	3
	.byte	1
	.space	3
	.sdecl	'.rodata.IfxPsi5_PinMap.IfxPsi5_RX2C_P33_5_IN',data,rom,cluster('IfxPsi5_RX2C_P33_5_IN')
	.sect	'.rodata.IfxPsi5_PinMap.IfxPsi5_RX2C_P33_5_IN'
	.global	IfxPsi5_RX2C_P33_5_IN
	.align	4
IfxPsi5_RX2C_P33_5_IN:	.type	object
	.size	IfxPsi5_RX2C_P33_5_IN,20
	.word	-268414976
	.byte	2
	.space	3
	.word	-268184832
	.byte	5
	.space	3
	.byte	2
	.space	3
	.sdecl	'.rodata.IfxPsi5_PinMap.IfxPsi5_TX0_P00_2_OUT',data,rom,cluster('IfxPsi5_TX0_P00_2_OUT')
	.sect	'.rodata.IfxPsi5_PinMap.IfxPsi5_TX0_P00_2_OUT'
	.global	IfxPsi5_TX0_P00_2_OUT
	.align	4
IfxPsi5_TX0_P00_2_OUT:	.type	object
	.size	IfxPsi5_TX0_P00_2_OUT,20
	.word	-268414976
	.space	4
	.word	-268197888
	.byte	2
	.space	3
	.byte	160
	.space	3
	.sdecl	'.rodata.IfxPsi5_PinMap.IfxPsi5_TX0_P02_2_OUT',data,rom,cluster('IfxPsi5_TX0_P02_2_OUT')
	.sect	'.rodata.IfxPsi5_PinMap.IfxPsi5_TX0_P02_2_OUT'
	.global	IfxPsi5_TX0_P02_2_OUT
	.align	4
IfxPsi5_TX0_P02_2_OUT:	.type	object
	.size	IfxPsi5_TX0_P02_2_OUT,20
	.word	-268414976
	.space	4
	.word	-268197376
	.byte	2
	.space	3
	.byte	160
	.space	3
	.sdecl	'.rodata.IfxPsi5_PinMap.IfxPsi5_TX0_P33_2_OUT',data,rom,cluster('IfxPsi5_TX0_P33_2_OUT')
	.sect	'.rodata.IfxPsi5_PinMap.IfxPsi5_TX0_P33_2_OUT'
	.global	IfxPsi5_TX0_P33_2_OUT
	.align	4
IfxPsi5_TX0_P33_2_OUT:	.type	object
	.size	IfxPsi5_TX0_P33_2_OUT,20
	.word	-268414976
	.space	4
	.word	-268184832
	.byte	2
	.space	3
	.byte	160
	.space	3
	.sdecl	'.rodata.IfxPsi5_PinMap.IfxPsi5_TX1_P00_4_OUT',data,rom,cluster('IfxPsi5_TX1_P00_4_OUT')
	.sect	'.rodata.IfxPsi5_PinMap.IfxPsi5_TX1_P00_4_OUT'
	.global	IfxPsi5_TX1_P00_4_OUT
	.align	4
IfxPsi5_TX1_P00_4_OUT:	.type	object
	.size	IfxPsi5_TX1_P00_4_OUT,20
	.word	-268414976
	.byte	1
	.space	3
	.word	-268197888
	.byte	4
	.space	3
	.byte	160
	.space	3
	.sdecl	'.rodata.IfxPsi5_PinMap.IfxPsi5_TX1_P02_6_OUT',data,rom,cluster('IfxPsi5_TX1_P02_6_OUT')
	.sect	'.rodata.IfxPsi5_PinMap.IfxPsi5_TX1_P02_6_OUT'
	.global	IfxPsi5_TX1_P02_6_OUT
	.align	4
IfxPsi5_TX1_P02_6_OUT:	.type	object
	.size	IfxPsi5_TX1_P02_6_OUT,20
	.word	-268414976
	.byte	1
	.space	3
	.word	-268197376
	.byte	6
	.space	3
	.byte	160
	.space	3
	.sdecl	'.rodata.IfxPsi5_PinMap.IfxPsi5_TX1_P33_4_OUT',data,rom,cluster('IfxPsi5_TX1_P33_4_OUT')
	.sect	'.rodata.IfxPsi5_PinMap.IfxPsi5_TX1_P33_4_OUT'
	.global	IfxPsi5_TX1_P33_4_OUT
	.align	4
IfxPsi5_TX1_P33_4_OUT:	.type	object
	.size	IfxPsi5_TX1_P33_4_OUT,20
	.word	-268414976
	.byte	1
	.space	3
	.word	-268184832
	.byte	4
	.space	3
	.byte	160
	.space	3
	.sdecl	'.rodata.IfxPsi5_PinMap.IfxPsi5_TX2_P00_6_OUT',data,rom,cluster('IfxPsi5_TX2_P00_6_OUT')
	.sect	'.rodata.IfxPsi5_PinMap.IfxPsi5_TX2_P00_6_OUT'
	.global	IfxPsi5_TX2_P00_6_OUT
	.align	4
IfxPsi5_TX2_P00_6_OUT:	.type	object
	.size	IfxPsi5_TX2_P00_6_OUT,20
	.word	-268414976
	.byte	2
	.space	3
	.word	-268197888
	.byte	6
	.space	3
	.byte	160
	.space	3
	.sdecl	'.rodata.IfxPsi5_PinMap.IfxPsi5_TX2_P02_8_OUT',data,rom,cluster('IfxPsi5_TX2_P02_8_OUT')
	.sect	'.rodata.IfxPsi5_PinMap.IfxPsi5_TX2_P02_8_OUT'
	.global	IfxPsi5_TX2_P02_8_OUT
	.align	4
IfxPsi5_TX2_P02_8_OUT:	.type	object
	.size	IfxPsi5_TX2_P02_8_OUT,20
	.word	-268414976
	.byte	2
	.space	3
	.word	-268197376
	.byte	8
	.space	3
	.byte	160
	.space	3
	.sdecl	'.rodata.IfxPsi5_PinMap.IfxPsi5_TX2_P33_6_OUT',data,rom,cluster('IfxPsi5_TX2_P33_6_OUT')
	.sect	'.rodata.IfxPsi5_PinMap.IfxPsi5_TX2_P33_6_OUT'
	.global	IfxPsi5_TX2_P33_6_OUT
	.align	4
IfxPsi5_TX2_P33_6_OUT:	.type	object
	.size	IfxPsi5_TX2_P33_6_OUT,20
	.word	-268414976
	.byte	2
	.space	3
	.word	-268184832
	.byte	6
	.space	3
	.byte	160
	.space	3
	.sdecl	'.data.IfxPsi5_PinMap.IfxPsi5_Rx_In_pinTable',data,cluster('IfxPsi5_Rx_In_pinTable')
	.sect	'.data.IfxPsi5_PinMap.IfxPsi5_Rx_In_pinTable'
	.global	IfxPsi5_Rx_In_pinTable
	.align	4
IfxPsi5_Rx_In_pinTable:	.type	object
	.size	IfxPsi5_Rx_In_pinTable,36
	.word	IfxPsi5_RX0A_P00_1_IN,IfxPsi5_RX0B_P02_3_IN,IfxPsi5_RX0C_P33_1_IN,IfxPsi5_RX1A_P00_3_IN,IfxPsi5_RX1B_P02_5_IN,IfxPsi5_RX1C_P33_3_IN,IfxPsi5_RX2A_P00_5_IN,IfxPsi5_RX2B_P02_7_IN
	.word	IfxPsi5_RX2C_P33_5_IN
	.sdecl	'.data.IfxPsi5_PinMap.IfxPsi5_Tx_Out_pinTable',data,cluster('IfxPsi5_Tx_Out_pinTable')
	.sect	'.data.IfxPsi5_PinMap.IfxPsi5_Tx_Out_pinTable'
	.global	IfxPsi5_Tx_Out_pinTable
	.align	4
IfxPsi5_Tx_Out_pinTable:	.type	object
	.size	IfxPsi5_Tx_Out_pinTable,36
	.word	IfxPsi5_TX0_P00_2_OUT,IfxPsi5_TX0_P02_2_OUT,IfxPsi5_TX0_P33_2_OUT,IfxPsi5_TX1_P00_4_OUT,IfxPsi5_TX1_P02_6_OUT,IfxPsi5_TX1_P33_4_OUT,IfxPsi5_TX2_P00_6_OUT,IfxPsi5_TX2_P02_8_OUT
	.word	IfxPsi5_TX2_P33_6_OUT
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L3:
	.word	71062
	.half	3
	.word	.L4
	.byte	4
.L2:
	.byte	1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPsi5_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L5
	.byte	2,1,1,3
	.word	238
	.byte	4
	.byte	'Ifx__jump_and_link',0,3,1,61,17,1,1,5
	.byte	'fun',0,1,61,43
	.word	241
	.byte	6,0,7
	.byte	'__fract',0,4,128,1,7
	.byte	'float',0,4,4,8
	.byte	'Ifx__float_to_fract',0,3,1,152,2,18
	.word	286
	.byte	1,1,5
	.byte	'a',0,1,152,2,44
	.word	298
	.byte	6,0,7
	.byte	'unsigned long long int',0,8,7,9
	.byte	'void',0,3
	.word	378
	.byte	8
	.byte	'__ld64',0,3,2,135,1,19
	.word	352
	.byte	1,1,5
	.byte	'addr',0,2,135,1,32
	.word	384
	.byte	6,0,4
	.byte	'__st64',0,3,2,143,1,17,1,1,5
	.byte	'addr',0,2,143,1,30
	.word	384
	.byte	5
	.byte	'value',0,2,143,1,43
	.word	352
	.byte	6,0,7
	.byte	'unsigned int',0,4,7,7
	.byte	'int',0,4,5,7
	.byte	'unsigned char',0,1,8,7
	.byte	'unsigned short int',0,2,7,10
	.byte	'_Ifx_P_OUT_Bits',0,4,143,3,16,4,11
	.byte	'P0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,12,4,181,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	532
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMR_Bits',0,4,169,2,16,4,11
	.byte	'PS0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'PCL0',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	493
	.byte	1,0,2,35,3,0,12,4,133,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	848
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ID_Bits',0,4,110,16,4,11
	.byte	'MODREV',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	510
	.byte	16,0,2,35,2,0,12,4,148,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1419
	.byte	4,2,35,0,0,14,4
	.word	493
	.byte	15,3,0,10
	.byte	'_Ifx_P_IOCR0_Bits',0,4,140,1,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	3,5,2,35,0,11
	.byte	'PC0',0,1
	.word	493
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	493
	.byte	3,5,2,35,1,11
	.byte	'PC1',0,1
	.word	493
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	493
	.byte	3,5,2,35,2,11
	.byte	'PC2',0,1
	.word	493
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	3,5,2,35,3,11
	.byte	'PC3',0,1
	.word	493
	.byte	5,0,2,35,3,0,12,4,164,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1547
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR4_Bits',0,4,166,1,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	3,5,2,35,0,11
	.byte	'PC4',0,1
	.word	493
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	493
	.byte	3,5,2,35,1,11
	.byte	'PC5',0,1
	.word	493
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	493
	.byte	3,5,2,35,2,11
	.byte	'PC6',0,1
	.word	493
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	3,5,2,35,3,11
	.byte	'PC7',0,1
	.word	493
	.byte	5,0,2,35,3,0,12,4,180,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1762
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR8_Bits',0,4,179,1,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	3,5,2,35,0,11
	.byte	'PC8',0,1
	.word	493
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	493
	.byte	3,5,2,35,1,11
	.byte	'PC9',0,1
	.word	493
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	493
	.byte	3,5,2,35,2,11
	.byte	'PC10',0,1
	.word	493
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	3,5,2,35,3,11
	.byte	'PC11',0,1
	.word	493
	.byte	5,0,2,35,3,0,12,4,188,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1977
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR12_Bits',0,4,153,1,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	3,5,2,35,0,11
	.byte	'PC12',0,1
	.word	493
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	493
	.byte	3,5,2,35,1,11
	.byte	'PC13',0,1
	.word	493
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	493
	.byte	3,5,2,35,2,11
	.byte	'PC14',0,1
	.word	493
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	3,5,2,35,3,11
	.byte	'PC15',0,1
	.word	493
	.byte	5,0,2,35,3,0,12,4,172,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2194
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IN_Bits',0,4,118,16,4,11
	.byte	'P0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,12,4,156,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2414
	.byte	4,2,35,0,0,14,24
	.word	493
	.byte	15,23,0,10
	.byte	'_Ifx_P_PDR0_Bits',0,4,205,3,16,4,11
	.byte	'PD0',0,1
	.word	493
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	493
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'PD2',0,1
	.word	493
	.byte	3,5,2,35,1,11
	.byte	'PL2',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'PD3',0,1
	.word	493
	.byte	3,1,2,35,1,11
	.byte	'PL3',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'PD4',0,1
	.word	493
	.byte	3,5,2,35,2,11
	.byte	'PL4',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'PD5',0,1
	.word	493
	.byte	3,1,2,35,2,11
	.byte	'PL5',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'PD6',0,1
	.word	493
	.byte	3,5,2,35,3,11
	.byte	'PL6',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'PD7',0,1
	.word	493
	.byte	3,1,2,35,3,11
	.byte	'PL7',0,1
	.word	493
	.byte	1,0,2,35,3,0,12,4,205,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2737
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PDR1_Bits',0,4,226,3,16,4,11
	.byte	'PD8',0,1
	.word	493
	.byte	3,5,2,35,0,11
	.byte	'PL8',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'PD9',0,1
	.word	493
	.byte	3,1,2,35,0,11
	.byte	'PL9',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'PD10',0,1
	.word	493
	.byte	3,5,2,35,1,11
	.byte	'PL10',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'PD11',0,1
	.word	493
	.byte	3,1,2,35,1,11
	.byte	'PL11',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'PD12',0,1
	.word	493
	.byte	3,5,2,35,2,11
	.byte	'PL12',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'PD13',0,1
	.word	493
	.byte	3,1,2,35,2,11
	.byte	'PL13',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'PD14',0,1
	.word	493
	.byte	3,5,2,35,3,11
	.byte	'PL14',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'PD15',0,1
	.word	493
	.byte	3,1,2,35,3,11
	.byte	'PL15',0,1
	.word	493
	.byte	1,0,2,35,3,0,12,4,213,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3041
	.byte	4,2,35,0,0,14,8
	.word	493
	.byte	15,7,0,10
	.byte	'_Ifx_P_ESR_Bits',0,4,88,16,4,11
	.byte	'EN0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,12,4,140,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3366
	.byte	4,2,35,0,0,14,12
	.word	493
	.byte	15,11,0,10
	.byte	'_Ifx_P_PDISC_Bits',0,4,183,3,16,4,11
	.byte	'PDIS0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'PDIS2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'PDIS3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'PDIS4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'PDIS5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'PDIS6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'PDIS7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'PDIS8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'PDIS9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'PDIS10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'PDIS11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'PDIS12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'PDIS13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'PDIS14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'PDIS15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,12,4,197,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3706
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PCSR_Bits',0,4,165,3,16,4,11
	.byte	'SEL0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'SEL1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'SEL2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'SEL3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'SEL4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'SEL5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'SEL6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'SEL7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	493
	.byte	2,6,2,35,1,11
	.byte	'SEL10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'SEL11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	470
	.byte	19,1,2,35,0,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,12,4,189,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4072
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR0_Bits',0,4,206,2,16,4,11
	.byte	'PS0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	470
	.byte	28,0,2,35,0,0,12,4,149,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4358
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR4_Bits',0,4,227,2,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	4,4,2,35,0,11
	.byte	'PS4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	470
	.byte	24,0,2,35,0,0,12,4,165,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4505
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR8_Bits',0,4,238,2,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'PS8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	470
	.byte	20,0,2,35,0,0,12,4,173,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4674
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR12_Bits',0,4,216,2,16,4,11
	.byte	'reserved_0',0,2
	.word	510
	.byte	12,4,2,35,0,11
	.byte	'PS12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,12,4,157,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4846
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR0_Bits',0,4,232,1,16,4,11
	.byte	'reserved_0',0,2
	.word	510
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	510
	.byte	12,0,2,35,2,0,12,4,229,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5021
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR4_Bits',0,4,253,1,16,4,11
	.byte	'reserved_0',0,4
	.word	470
	.byte	20,12,2,35,0,11
	.byte	'PCL4',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	8,0,2,35,3,0,12,4,245,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5195
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR8_Bits',0,4,136,2,16,4,11
	.byte	'reserved_0',0,4
	.word	470
	.byte	24,8,2,35,0,11
	.byte	'PCL8',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	493
	.byte	4,0,2,35,3,0,12,4,253,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5369
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR12_Bits',0,4,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	470
	.byte	28,4,2,35,0,11
	.byte	'PCL12',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	493
	.byte	1,0,2,35,3,0,12,4,237,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5545
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR_Bits',0,4,249,2,16,4,11
	.byte	'PS0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,12,4,141,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5701
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR_Bits',0,4,147,2,16,4,11
	.byte	'reserved_0',0,2
	.word	510
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	493
	.byte	1,0,2,35,3,0,12,4,221,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6034
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR0_Bits',0,4,192,1,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	470
	.byte	30,0,2,35,0,0,12,4,196,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6382
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_Bits',0,4,200,1,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	470
	.byte	30,0,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_P21_Bits',0,4,208,1,16,4,11
	.byte	'RDIS_CTRL',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'RX_DIS',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'TERM',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'LRXTERM',0,1
	.word	493
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	470
	.byte	24,0,2,35,0,0,12,4,204,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6506
	.byte	4,2,35,0,13
	.byte	'B_P21',0
	.word	6590
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR2_Bits',0,4,218,1,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'LVDSR',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'LVDSRL',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	493
	.byte	2,4,2,35,1,11
	.byte	'TDIS_CTRL',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'TX_DIS',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'TX_PD',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'TX_PWDPD',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,12,4,213,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6770
	.byte	4,2,35,0,0,14,76
	.word	493
	.byte	15,75,0,10
	.byte	'_Ifx_P_ACCEN1_Bits',0,4,82,16,4,11
	.byte	'reserved_0',0,4
	.word	470
	.byte	32,0,2,35,0,0,12,4,132,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7023
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ACCEN0_Bits',0,4,45,16,4,11
	.byte	'EN0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	493
	.byte	1,0,2,35,3,0,12,4,252,3,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7110
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P',0,4,229,5,25,128,2,13
	.byte	'OUT',0
	.word	808
	.byte	4,2,35,0,13
	.byte	'OMR',0
	.word	1379
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	1498
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	1538
	.byte	4,2,35,12,13
	.byte	'IOCR0',0
	.word	1722
	.byte	4,2,35,16,13
	.byte	'IOCR4',0
	.word	1937
	.byte	4,2,35,20,13
	.byte	'IOCR8',0
	.word	2154
	.byte	4,2,35,24,13
	.byte	'IOCR12',0
	.word	2374
	.byte	4,2,35,28,13
	.byte	'reserved_20',0
	.word	1538
	.byte	4,2,35,32,13
	.byte	'IN',0
	.word	2688
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	2728
	.byte	24,2,35,40,13
	.byte	'PDR0',0
	.word	3001
	.byte	4,2,35,64,13
	.byte	'PDR1',0
	.word	3317
	.byte	4,2,35,68,13
	.byte	'reserved_48',0
	.word	3357
	.byte	8,2,35,72,13
	.byte	'ESR',0
	.word	3657
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	3697
	.byte	12,2,35,84,13
	.byte	'PDISC',0
	.word	4032
	.byte	4,2,35,96,13
	.byte	'PCSR',0
	.word	4318
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	3357
	.byte	8,2,35,104,13
	.byte	'OMSR0',0
	.word	4465
	.byte	4,2,35,112,13
	.byte	'OMSR4',0
	.word	4634
	.byte	4,2,35,116,13
	.byte	'OMSR8',0
	.word	4806
	.byte	4,2,35,120,13
	.byte	'OMSR12',0
	.word	4981
	.byte	4,2,35,124,13
	.byte	'OMCR0',0
	.word	5155
	.byte	4,3,35,128,1,13
	.byte	'OMCR4',0
	.word	5329
	.byte	4,3,35,132,1,13
	.byte	'OMCR8',0
	.word	5505
	.byte	4,3,35,136,1,13
	.byte	'OMCR12',0
	.word	5661
	.byte	4,3,35,140,1,13
	.byte	'OMSR',0
	.word	5994
	.byte	4,3,35,144,1,13
	.byte	'OMCR',0
	.word	6342
	.byte	4,3,35,148,1,13
	.byte	'reserved_98',0
	.word	3357
	.byte	8,3,35,152,1,13
	.byte	'LPCR0',0
	.word	6466
	.byte	4,3,35,160,1,13
	.byte	'LPCR1',0
	.word	6715
	.byte	4,3,35,164,1,13
	.byte	'LPCR2',0
	.word	6974
	.byte	4,3,35,168,1,13
	.byte	'reserved_A4',0
	.word	7014
	.byte	76,3,35,172,1,13
	.byte	'ACCEN1',0
	.word	7070
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	7637
	.byte	4,3,35,252,1,0,16
	.word	7677
	.byte	3
	.word	8280
	.byte	17,3,172,1,9,4,18
	.byte	'IfxPort_State_notChanged',0,0,18
	.byte	'IfxPort_State_high',0,1,18
	.byte	'IfxPort_State_low',0,128,128,4,18
	.byte	'IfxPort_State_toggled',0,129,128,4,0,4
	.byte	'IfxPort_setPinState',0,3,3,208,4,17,1,1,5
	.byte	'port',0,3,208,4,44
	.word	8285
	.byte	5
	.byte	'pinIndex',0,3,208,4,56
	.word	493
	.byte	5
	.byte	'action',0,3,208,4,80
	.word	8290
	.byte	6,0,19
	.word	246
	.byte	20
	.word	272
	.byte	6,0,19
	.word	307
	.byte	20
	.word	339
	.byte	6,0,19
	.word	389
	.byte	20
	.word	408
	.byte	6,0,19
	.word	424
	.byte	20
	.word	439
	.byte	20
	.word	453
	.byte	6,0,19
	.word	8393
	.byte	20
	.word	8421
	.byte	20
	.word	8435
	.byte	20
	.word	8453
	.byte	6,0,7
	.byte	'short int',0,2,5,21
	.byte	'__wchar_t',0,5,1,1
	.word	8546
	.byte	21
	.byte	'__size_t',0,5,1,1
	.word	470
	.byte	21
	.byte	'__ptrdiff_t',0,5,1,1
	.word	486
	.byte	22,1,3
	.word	8614
	.byte	21
	.byte	'__codeptr',0,5,1,1
	.word	8616
	.byte	10
	.byte	'_Ifx_PSI5_ACCEN0_Bits',0,6,45,16,4,11
	.byte	'EN0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_PSI5_ACCEN0_Bits',0,6,79,3
	.word	8639
	.byte	10
	.byte	'_Ifx_PSI5_ACCEN1_Bits',0,6,82,16,4,11
	.byte	'reserved_0',0,4
	.word	470
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_PSI5_ACCEN1_Bits',0,6,85,3
	.word	9198
	.byte	10
	.byte	'_Ifx_PSI5_CH_CTV_Bits',0,6,88,16,4,11
	.byte	'CTV',0,2
	.word	510
	.byte	16,0,2,35,0,11
	.byte	'CTC',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_PSI5_CH_CTV_Bits',0,6,92,3
	.word	9277
	.byte	10
	.byte	'_Ifx_PSI5_CH_IOCR_Bits',0,6,95,16,4,11
	.byte	'ALTI',0,1
	.word	493
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	493
	.byte	2,4,2,35,0,11
	.byte	'DEPTH',0,1
	.word	493
	.byte	4,0,2,35,0,11
	.byte	'OIE',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'IIE',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	493
	.byte	2,4,2,35,1,11
	.byte	'REG',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'FEG',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'CREG',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'CFEG',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	14,2,2,35,2,11
	.byte	'RXM',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'TXM',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_PSI5_CH_IOCR_Bits',0,6,110,3
	.word	9364
	.byte	10
	.byte	'_Ifx_PSI5_CH_PGC_Bits',0,6,113,16,4,11
	.byte	'PLEN',0,1
	.word	493
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	493
	.byte	2,0,2,35,0,11
	.byte	'DEL',0,1
	.word	493
	.byte	6,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'TBS',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'ETB',0,1
	.word	493
	.byte	3,5,2,35,2,11
	.byte	'PTE',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'ETS',0,1
	.word	493
	.byte	3,1,2,35,2,11
	.byte	'ETE',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'BYP',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'BOT',0,1
	.word	493
	.byte	7,0,2,35,3,0,21
	.byte	'Ifx_PSI5_CH_PGC_Bits',0,6,126,3
	.word	9646
	.byte	10
	.byte	'_Ifx_PSI5_CH_RCRA_Bits',0,6,129,1,16,4,11
	.byte	'PDL0',0,1
	.word	493
	.byte	5,3,2,35,0,11
	.byte	'PDL1',0,2
	.word	510
	.byte	5,6,2,35,0,11
	.byte	'PDL2',0,1
	.word	493
	.byte	5,1,2,35,1,11
	.byte	'PDL3',0,4
	.word	470
	.byte	5,12,2,35,0,11
	.byte	'PDL4',0,2
	.word	510
	.byte	5,7,2,35,2,11
	.byte	'PDL5',0,1
	.word	493
	.byte	5,2,2,35,3,11
	.byte	'ASYN',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'AVBS',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_PSI5_CH_RCRA_Bits',0,6,139,1,3
	.word	9884
	.byte	10
	.byte	'_Ifx_PSI5_CH_RCRB_Bits',0,6,142,1,16,4,11
	.byte	'MSG0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'CRC0',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'FEC0',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'VBS0',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'MSG1',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'CRC1',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'FEC1',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'VBS1',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'MSG2',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'CRC2',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'FEC2',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'VBS2',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'MSG3',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'CRC3',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'FEC3',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'VBS3',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'MSG4',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'CRC4',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'FEC4',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'VBS4',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'MSG5',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'CRC5',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'FEC5',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'VBS5',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	8,0,2,35,3,0,21
	.byte	'Ifx_PSI5_CH_RCRB_Bits',0,6,169,1,3
	.word	10073
	.byte	10
	.byte	'_Ifx_PSI5_CH_RCRC_Bits',0,6,172,1,16,4,11
	.byte	'BRS',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'TSP',0,1
	.word	493
	.byte	2,5,2,35,0,11
	.byte	'TSF',0,1
	.word	493
	.byte	2,3,2,35,0,11
	.byte	'TSR',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	470
	.byte	26,0,2,35,0,0,21
	.byte	'Ifx_PSI5_CH_RCRC_Bits',0,6,179,1,3
	.word	10541
	.byte	10
	.byte	'_Ifx_PSI5_CH_RDRH_Bits',0,6,182,1,16,4,11
	.byte	'TS',0,4
	.word	470
	.byte	24,8,2,35,0,11
	.byte	'SC',0,1
	.word	493
	.byte	3,5,2,35,3,11
	.byte	'TEI',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'NBI',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'MEI',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'NFI',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'RBI',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_PSI5_CH_RDRH_Bits',0,6,191,1,3
	.word	10684
	.byte	10
	.byte	'_Ifx_PSI5_CH_RDRL_Bits',0,6,194,1,16,4,11
	.byte	'CRCI',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'CRC',0,1
	.word	493
	.byte	3,4,2,35,0,11
	.byte	'RD',0,4
	.word	470
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_PSI5_CH_RDRL_Bits',0,6,199,1,3
	.word	10848
	.byte	10
	.byte	'_Ifx_PSI5_CH_RSR_Bits',0,6,202,1,16,4,11
	.byte	'CRC',0,1
	.word	493
	.byte	3,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	493
	.byte	5,0,2,35,0,11
	.byte	'MSG',0,1
	.word	493
	.byte	2,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	470
	.byte	22,0,2,35,0,0,21
	.byte	'Ifx_PSI5_CH_RSR_Bits',0,6,208,1,3
	.word	10954
	.byte	10
	.byte	'_Ifx_PSI5_CH_SCR_Bits',0,6,211,1,16,4,11
	.byte	'PLL',0,1
	.word	493
	.byte	6,2,2,35,0,11
	.byte	'EPS',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'BSC',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'SSL',0,1
	.word	493
	.byte	6,2,2,35,1,11
	.byte	'FLUS',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'FLUO',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'SOL',0,1
	.word	493
	.byte	6,2,2,35,2,11
	.byte	'CRC',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'STA',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'INH',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'GO',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'TPF',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'TSF',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'TOF',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	493
	.byte	2,1,2,35,3,11
	.byte	'TRQ',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_PSI5_CH_SCR_Bits',0,6,229,1,3
	.word	11088
	.byte	10
	.byte	'_Ifx_PSI5_CH_SDRH_Bits',0,6,232,1,16,4,11
	.byte	'SD32',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'SD33',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'SD34',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'SD35',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'SD36',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'SD37',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'SD38',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'SD39',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'SD40',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'SD41',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'SD42',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'SD43',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'SD44',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'SD45',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'SD46',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'SD47',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'SD48',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'SD49',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'SD50',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'SD51',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'SD52',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'SD53',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'SD54',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'SD55',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'SD56',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'SD57',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'SD58',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'SD59',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'SD60',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'SD61',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'SD62',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'SD63',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_PSI5_CH_SDRH_Bits',0,6,138,2,3
	.word	11396
	.byte	10
	.byte	'_Ifx_PSI5_CH_SDRL_Bits',0,6,141,2,16,4,11
	.byte	'SD0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'SD1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'SD2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'SD3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'SD4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'SD5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'SD6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'SD7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'SD8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'SD9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'SD10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'SD11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'SD12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'SD13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'SD14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'SD15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'SD16',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'SD17',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'SD18',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'SD19',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'SD20',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'SD21',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'SD22',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'SD23',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'SD24',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'SD25',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'SD26',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'SD27',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'SD28',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'SD29',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'SD30',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'SD31',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_PSI5_CH_SDRL_Bits',0,6,175,2,3
	.word	11969
	.byte	10
	.byte	'_Ifx_PSI5_CH_SDS_Bits',0,6,178,2,16,4,11
	.byte	'SD',0,2
	.word	510
	.byte	16,0,2,35,0,11
	.byte	'MID',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'SCRC',0,1
	.word	493
	.byte	6,2,2,35,3,11
	.byte	'SCRI',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'CON',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_PSI5_CH_SDS_Bits',0,6,185,2,3
	.word	12532
	.byte	10
	.byte	'_Ifx_PSI5_CH_SFTSC_Bits',0,6,188,2,16,4,11
	.byte	'TS',0,4
	.word	470
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	8,0,2,35,3,0,21
	.byte	'Ifx_PSI5_CH_SFTSC_Bits',0,6,192,2,3
	.word	12667
	.byte	10
	.byte	'_Ifx_PSI5_CH_SORH_Bits',0,6,195,2,16,4,11
	.byte	'SD32',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'SD33',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'SD34',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'SD35',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'SD36',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'SD37',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'SD38',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'SD39',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'SD40',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'SD41',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'SD42',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'SD43',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'SD44',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'SD45',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'SD46',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'SD47',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'SD48',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'SD49',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'SD50',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'SD51',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'SD52',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'SD53',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'SD54',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'SD55',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'SD56',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'SD57',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'SD58',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'SD59',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'SD60',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'SD61',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'SD62',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'SD63',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_PSI5_CH_SORH_Bits',0,6,229,2,3
	.word	12767
	.byte	10
	.byte	'_Ifx_PSI5_CH_SORL_Bits',0,6,232,2,16,4,11
	.byte	'SD0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'SD1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'SD2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'SD3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'SD4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'SD5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'SD6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'SD7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'SD8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'SD9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'SD10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'SD11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'SD12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'SD13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'SD14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'SD15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'SD16',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'SD17',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'SD18',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'SD19',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'SD20',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'SD21',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'SD22',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'SD23',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'SD24',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'SD25',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'SD26',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'SD27',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'SD28',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'SD29',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'SD30',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'SD31',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_PSI5_CH_SORL_Bits',0,6,138,3,3
	.word	13340
	.byte	10
	.byte	'_Ifx_PSI5_CH_SPTSC_Bits',0,6,141,3,16,4,11
	.byte	'TS',0,4
	.word	470
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	8,0,2,35,3,0,21
	.byte	'Ifx_PSI5_CH_SPTSC_Bits',0,6,145,3,3
	.word	13903
	.byte	10
	.byte	'_Ifx_PSI5_CH_SSRH_Bits',0,6,148,3,16,4,11
	.byte	'SD32',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'SD33',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'SD34',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'SD35',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'SD36',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'SD37',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'SD38',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'SD39',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'SD40',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'SD41',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'SD42',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'SD43',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'SD44',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'SD45',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'SD46',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'SD47',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'SD48',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'SD49',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'SD50',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'SD51',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'SD52',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'SD53',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'SD54',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'SD55',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'SD56',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'SD57',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'SD58',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'SD59',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'SD60',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'SD61',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'SD62',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'SD63',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_PSI5_CH_SSRH_Bits',0,6,182,3,3
	.word	14003
	.byte	10
	.byte	'_Ifx_PSI5_CH_SSRL_Bits',0,6,185,3,16,4,11
	.byte	'SD0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'SD1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'SD2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'SD3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'SD4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'SD5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'SD6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'SD7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'SD8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'SD9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'SD10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'SD11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'SD12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'SD13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'SD14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'SD15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'SD16',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'SD17',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'SD18',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'SD19',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'SD20',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'SD21',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'SD22',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'SD23',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'SD24',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'SD25',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'SD26',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'SD27',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'SD28',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'SD29',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'SD30',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'SD31',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_PSI5_CH_SSRL_Bits',0,6,219,3,3
	.word	14576
	.byte	10
	.byte	'_Ifx_PSI5_CH_WDT_Bits',0,6,222,3,16,4,11
	.byte	'WDLxw',0,2
	.word	510
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_PSI5_CH_WDT_Bits',0,6,226,3,3
	.word	15139
	.byte	10
	.byte	'_Ifx_PSI5_CLC_Bits',0,6,229,3,16,4,11
	.byte	'DISR',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'DISS',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	470
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_PSI5_CLC_Bits',0,6,234,3,3
	.word	15238
	.byte	10
	.byte	'_Ifx_PSI5_CRCICLR_Bits',0,6,237,3,16,4,11
	.byte	'CRCI0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'CRCI1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'CRCI2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'CRCI3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'CRCI4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'CRCI5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'CRCI6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'CRCI7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'CRCI8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'CRCI9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'CRCI10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'CRCI11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'CRCI12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'CRCI13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'CRCI14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'CRCI15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'CRCI16',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'CRCI17',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'CRCI18',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'CRCI19',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'CRCI20',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'CRCI21',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'CRCI22',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'CRCI23',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'CRCI24',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'CRCI25',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'CRCI26',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'CRCI27',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'CRCI28',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'CRCI29',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'CRCI30',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'CRCI31',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_PSI5_CRCICLR_Bits',0,6,143,4,3
	.word	15345
	.byte	10
	.byte	'_Ifx_PSI5_CRCIOV_Bits',0,6,146,4,16,4,11
	.byte	'CRCI0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'CRCI1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'CRCI2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'CRCI3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'CRCI4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'CRCI5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'CRCI6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'CRCI7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'CRCI8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'CRCI9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'CRCI10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'CRCI11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'CRCI12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'CRCI13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'CRCI14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'CRCI15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'CRCI16',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'CRCI17',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'CRCI18',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'CRCI19',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'CRCI20',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'CRCI21',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'CRCI22',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'CRCI23',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'CRCI24',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'CRCI25',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'CRCI26',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'CRCI27',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'CRCI28',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'CRCI29',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'CRCI30',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'CRCI31',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_PSI5_CRCIOV_Bits',0,6,180,4,3
	.word	15972
	.byte	10
	.byte	'_Ifx_PSI5_CRCISET_Bits',0,6,183,4,16,4,11
	.byte	'CRCI0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'CRCI1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'CRCI2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'CRCI3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'CRCI4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'CRCI5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'CRCI6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'CRCI7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'CRCI8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'CRCI9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'CRCI10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'CRCI11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'CRCI12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'CRCI13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'CRCI14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'CRCI15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'CRCI16',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'CRCI17',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'CRCI18',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'CRCI19',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'CRCI20',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'CRCI21',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'CRCI22',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'CRCI23',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'CRCI24',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'CRCI25',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'CRCI26',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'CRCI27',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'CRCI28',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'CRCI29',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'CRCI30',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'CRCI31',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_PSI5_CRCISET_Bits',0,6,217,4,3
	.word	16597
	.byte	10
	.byte	'_Ifx_PSI5_FDR_Bits',0,6,220,4,16,4,11
	.byte	'STEP',0,2
	.word	510
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	493
	.byte	4,2,2,35,1,11
	.byte	'DM',0,1
	.word	493
	.byte	2,0,2,35,1,11
	.byte	'RESULT',0,2
	.word	510
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	493
	.byte	6,0,2,35,3,0,21
	.byte	'Ifx_PSI5_FDR_Bits',0,6,227,4,3
	.word	17224
	.byte	10
	.byte	'_Ifx_PSI5_FDRH_Bits',0,6,230,4,16,4,11
	.byte	'STEP',0,2
	.word	510
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	493
	.byte	4,2,2,35,1,11
	.byte	'DM',0,1
	.word	493
	.byte	2,0,2,35,1,11
	.byte	'RESULT',0,2
	.word	510
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	493
	.byte	6,0,2,35,3,0,21
	.byte	'Ifx_PSI5_FDRH_Bits',0,6,237,4,3
	.word	17371
	.byte	10
	.byte	'_Ifx_PSI5_FDRL_Bits',0,6,240,4,16,4,11
	.byte	'STEP',0,2
	.word	510
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	493
	.byte	4,2,2,35,1,11
	.byte	'DM',0,1
	.word	493
	.byte	2,0,2,35,1,11
	.byte	'RESULT',0,2
	.word	510
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	493
	.byte	6,0,2,35,3,0,21
	.byte	'Ifx_PSI5_FDRL_Bits',0,6,247,4,3
	.word	17520
	.byte	10
	.byte	'_Ifx_PSI5_FDRT_Bits',0,6,250,4,16,4,11
	.byte	'STEP',0,2
	.word	510
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	493
	.byte	4,2,2,35,1,11
	.byte	'DM',0,1
	.word	493
	.byte	2,0,2,35,1,11
	.byte	'RESULT',0,2
	.word	510
	.byte	10,6,2,35,2,11
	.byte	'ECS',0,1
	.word	493
	.byte	3,3,2,35,3,11
	.byte	'ECEA',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'ECEB',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'ECEC',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_PSI5_FDRT_Bits',0,6,132,5,3
	.word	17669
	.byte	10
	.byte	'_Ifx_PSI5_GCR_Bits',0,6,135,5,16,4,11
	.byte	'CRCI',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'NBI',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'MEI',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'NFI',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'TEI',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	493
	.byte	3,0,2,35,0,11
	.byte	'ETC0',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'ETC1',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'ETC2',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'ETC3',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'ETC4',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	493
	.byte	3,0,2,35,1,11
	.byte	'CEN0',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'CEN1',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'CEN2',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'CEN3',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'CEN4',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,2
	.word	510
	.byte	11,0,2,35,2,0,21
	.byte	'Ifx_PSI5_GCR_Bits',0,6,155,5,3
	.word	17858
	.byte	10
	.byte	'_Ifx_PSI5_ID_Bits',0,6,158,5,16,4,11
	.byte	'MODREV',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_PSI5_ID_Bits',0,6,163,5,3
	.word	18215
	.byte	10
	.byte	'_Ifx_PSI5_INP_Bits',0,6,166,5,16,4,11
	.byte	'RSI',0,1
	.word	493
	.byte	4,4,2,35,0,11
	.byte	'RDI',0,1
	.word	493
	.byte	4,0,2,35,0,11
	.byte	'RBI',0,1
	.word	493
	.byte	4,4,2,35,1,11
	.byte	'TDI',0,1
	.word	493
	.byte	4,0,2,35,1,11
	.byte	'TBI',0,1
	.word	493
	.byte	4,4,2,35,2,11
	.byte	'ERRI',0,1
	.word	493
	.byte	4,0,2,35,2,11
	.byte	'SDI',0,1
	.word	493
	.byte	4,4,2,35,3,11
	.byte	'FWI',0,1
	.word	493
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_PSI5_INP_Bits',0,6,176,5,3
	.word	18324
	.byte	10
	.byte	'_Ifx_PSI5_INTCLRA_Bits',0,6,179,5,16,4,11
	.byte	'RSI',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'RDI',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'RBI',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'TEI',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'NBI',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'MEI',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'CRCI',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'FWI',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'RUI',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'RMI',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'TPI',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'TPOI',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'TSI',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'TSOI',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'TOI',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'TOOI',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'NFI',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,2
	.word	510
	.byte	15,0,2,35,2,0,21
	.byte	'Ifx_PSI5_INTCLRA_Bits',0,6,199,5,3
	.word	18498
	.byte	10
	.byte	'_Ifx_PSI5_INTCLRB_Bits',0,6,202,5,16,4,11
	.byte	'WSI0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'WSI1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'WSI2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'WSI3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'WSI4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'WSI5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'SDI0',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'SDI1',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'SDI2',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'SDI3',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'SDI4',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'SDI5',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'SOI0',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'SOI1',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'SOI2',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'SOI3',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'SOI4',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'SOI5',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'SCRI0',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'SCRI1',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'SCRI2',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'SCRI3',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'SCRI4',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'SCRI5',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	8,0,2,35,3,0,21
	.byte	'Ifx_PSI5_INTCLRB_Bits',0,6,229,5,3
	.word	18841
	.byte	10
	.byte	'_Ifx_PSI5_INTENA_Bits',0,6,232,5,16,4,11
	.byte	'RSI',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'RDI',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'RBI',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'TEI',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'NBI',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'MEI',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'CRCI',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'FWI',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'RUI',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'RMI',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'TPI',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'TPOI',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'TSI',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'TSOI',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'TOI',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'TOOI',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'NFI',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,2
	.word	510
	.byte	15,0,2,35,2,0,21
	.byte	'Ifx_PSI5_INTENA_Bits',0,6,252,5,3
	.word	19315
	.byte	10
	.byte	'_Ifx_PSI5_INTENB_Bits',0,6,255,5,16,4,11
	.byte	'WSI0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'WSI1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'WSI2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'WSI3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'WSI4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'WSI5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'SDI0',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'SDI1',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'SDI2',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'SDI3',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'SDI4',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'SDI5',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'SOI0',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'SOI1',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'SOI2',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'SOI3',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'SOI4',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'SOI5',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'SCRI0',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'SCRI1',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'SCRI2',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'SCRI3',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'SCRI4',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'SCRI5',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	8,0,2,35,3,0,21
	.byte	'Ifx_PSI5_INTENB_Bits',0,6,154,6,3
	.word	19656
	.byte	10
	.byte	'_Ifx_PSI5_INTOV_Bits',0,6,157,6,16,4,11
	.byte	'RSI',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'RDI',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'RBI',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'TDI',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'TBI',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'ERRI',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'SDI',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'FWI',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	470
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_PSI5_INTOV_Bits',0,6,168,6,3
	.word	20128
	.byte	10
	.byte	'_Ifx_PSI5_INTSETA_Bits',0,6,171,6,16,4,11
	.byte	'RSI',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'RDI',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'RBI',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'TEI',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'NBI',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'MEI',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'CRCI',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'FWI',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'RUI',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'RMI',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'TPI',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'TPOI',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'TSI',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'TSOI',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'TOI',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'TOOI',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'NFI',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,2
	.word	510
	.byte	15,0,2,35,2,0,21
	.byte	'Ifx_PSI5_INTSETA_Bits',0,6,191,6,3
	.word	20328
	.byte	10
	.byte	'_Ifx_PSI5_INTSETB_Bits',0,6,194,6,16,4,11
	.byte	'WSI0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'WSI1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'WSI2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'WSI3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'WSI4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'WSI5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'SDI0',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'SDI1',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'SDI2',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'SDI3',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'SDI4',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'SDI5',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'SOI0',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'SOI1',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'SOI2',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'SOI3',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'SOI4',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'SOI5',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'SCRI0',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'SCRI1',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'SCRI2',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'SCRI3',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'SCRI4',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'SCRI5',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	8,0,2,35,3,0,21
	.byte	'Ifx_PSI5_INTSETB_Bits',0,6,221,6,3
	.word	20671
	.byte	10
	.byte	'_Ifx_PSI5_INTSTATA_Bits',0,6,224,6,16,4,11
	.byte	'RSI',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'RDI',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'RBI',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'TEI',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'NBI',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'MEI',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'CRCI',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'FWI',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'RUI',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'RMI',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'TPI',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'TPOI',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'TSI',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'TSOI',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'TOI',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'TOOI',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'NFI',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,2
	.word	510
	.byte	15,0,2,35,2,0,21
	.byte	'Ifx_PSI5_INTSTATA_Bits',0,6,244,6,3
	.word	21145
	.byte	10
	.byte	'_Ifx_PSI5_INTSTATB_Bits',0,6,247,6,16,4,11
	.byte	'WSI0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'WSI1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'WSI2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'WSI3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'WSI4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'WSI5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'SDI0',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'SDI1',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'SDI2',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'SDI3',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'SDI4',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'SDI5',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'SOI0',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'SOI1',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'SOI2',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'SOI3',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'SOI4',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'SOI5',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'SCRI0',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'SCRI1',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'SCRI2',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'SCRI3',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'SCRI4',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'SCRI5',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	8,0,2,35,3,0,21
	.byte	'Ifx_PSI5_INTSTATB_Bits',0,6,146,7,3
	.word	21490
	.byte	10
	.byte	'_Ifx_PSI5_KRST0_Bits',0,6,149,7,16,4,11
	.byte	'RST',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'RSTSTAT',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	470
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_PSI5_KRST0_Bits',0,6,154,7,3
	.word	21966
	.byte	10
	.byte	'_Ifx_PSI5_KRST1_Bits',0,6,157,7,16,4,11
	.byte	'RST',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	470
	.byte	31,0,2,35,0,0,21
	.byte	'Ifx_PSI5_KRST1_Bits',0,6,161,7,3
	.word	22079
	.byte	10
	.byte	'_Ifx_PSI5_KRSTCLR_Bits',0,6,164,7,16,4,11
	.byte	'CLR',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	470
	.byte	31,0,2,35,0,0,21
	.byte	'Ifx_PSI5_KRSTCLR_Bits',0,6,168,7,3
	.word	22173
	.byte	10
	.byte	'_Ifx_PSI5_MEICLR_Bits',0,6,171,7,16,4,11
	.byte	'MEI0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'MEI1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'MEI2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'MEI3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'MEI4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'MEI5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'MEI6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'MEI7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'MEI8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'MEI9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'MEI10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'MEI11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'MEI12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'MEI13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'MEI14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'MEI15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'MEI16',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'MEI17',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'MEI18',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'MEI19',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'MEI20',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'MEI21',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'MEI22',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'MEI23',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'MEI24',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'MEI25',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'MEI26',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'MEI27',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'MEI28',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'MEI29',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'MEI30',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'MEI31',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_PSI5_MEICLR_Bits',0,6,205,7,3
	.word	22271
	.byte	10
	.byte	'_Ifx_PSI5_MEIOV_Bits',0,6,208,7,16,4,11
	.byte	'MEI0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'MEI1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'MEI2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'MEI3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'MEI4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'MEI5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'MEI6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'MEI7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'MEI8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'MEI9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'MEI10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'MEI11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'MEI12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'MEI13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'MEI14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'MEI15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'MEI16',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'MEI17',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'MEI18',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'MEI19',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'MEI20',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'MEI21',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'MEI22',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'MEI23',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'MEI24',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'MEI25',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'MEI26',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'MEI27',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'MEI28',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'MEI29',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'MEI30',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'MEI31',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_PSI5_MEIOV_Bits',0,6,242,7,3
	.word	22864
	.byte	10
	.byte	'_Ifx_PSI5_MEISET_Bits',0,6,245,7,16,4,11
	.byte	'MEI0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'MEI1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'MEI2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'MEI3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'MEI4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'MEI5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'MEI6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'MEI7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'MEI8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'MEI9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'MEI10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'MEI11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'MEI12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'MEI13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'MEI14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'MEI15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'MEI16',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'MEI17',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'MEI18',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'MEI19',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'MEI20',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'MEI21',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'MEI22',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'MEI23',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'MEI24',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'MEI25',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'MEI26',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'MEI27',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'MEI28',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'MEI29',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'MEI30',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'MEI31',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_PSI5_MEISET_Bits',0,6,151,8,3
	.word	23455
	.byte	10
	.byte	'_Ifx_PSI5_NBICLR_Bits',0,6,154,8,16,4,11
	.byte	'NBI0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'NBI1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'NBI2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'NBI3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'NBI4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'NBI5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'NBI6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'NBI7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'NBI8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'NBI9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'NBI10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'NBI11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'NBI12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'NBI13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'NBI14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'NBI15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'NBI16',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'NBI17',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'NBI18',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'NBI19',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'NBI20',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'NBI21',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'NBI22',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'NBI23',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'NBI24',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'NBI25',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'NBI26',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'NBI27',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'NBI28',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'NBI29',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'NBI30',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'NBI31',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_PSI5_NBICLR_Bits',0,6,188,8,3
	.word	24048
	.byte	10
	.byte	'_Ifx_PSI5_NBIOV_Bits',0,6,191,8,16,4,11
	.byte	'NBI0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'NBI1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'NBI2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'NBI3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'NBI4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'NBI5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'NBI6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'NBI7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'NBI8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'NBI9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'NBI10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'NBI11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'NBI12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'NBI13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'NBI14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'NBI15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'NBI16',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'NBI17',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'NBI18',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'NBI19',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'NBI20',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'NBI21',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'NBI22',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'NBI23',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'NBI24',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'NBI25',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'NBI26',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'NBI27',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'NBI28',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'NBI29',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'NBI30',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'NBI31',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_PSI5_NBIOV_Bits',0,6,225,8,3
	.word	24641
	.byte	10
	.byte	'_Ifx_PSI5_NBISET_Bits',0,6,228,8,16,4,11
	.byte	'NBI0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'NBI1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'NBI2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'NBI3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'NBI4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'NBI5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'NBI6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'NBI7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'NBI8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'NBI9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'NBI10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'NBI11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'NBI12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'NBI13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'NBI14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'NBI15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'NBI16',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'NBI17',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'NBI18',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'NBI19',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'NBI20',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'NBI21',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'NBI22',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'NBI23',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'NBI24',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'NBI25',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'NBI26',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'NBI27',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'NBI28',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'NBI29',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'NBI30',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'NBI31',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_PSI5_NBISET_Bits',0,6,134,9,3
	.word	25232
	.byte	10
	.byte	'_Ifx_PSI5_NFICLR_Bits',0,6,137,9,16,4,11
	.byte	'NFI0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'NFI1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'NFI2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'NFI3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'NFI4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'NFI5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'NFI6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'NFI7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'NFI8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'NFI9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'NFI10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'NFI11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'NFI12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'NFI13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'NFI14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'NFI15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'NFI16',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'NFI17',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'NFI18',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'NFI19',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'NFI20',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'NFI21',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'NFI22',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'NFI23',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'NFI24',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'NFI25',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'NFI26',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'NFI27',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'NFI28',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'NFI29',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'NFI30',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'NFI31',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_PSI5_NFICLR_Bits',0,6,171,9,3
	.word	25825
	.byte	10
	.byte	'_Ifx_PSI5_NFIOV_Bits',0,6,174,9,16,4,11
	.byte	'NFI0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'NFI1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'NFI2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'NFI3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'NFI4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'NFI5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'NFI6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'NFI7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'NFI8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'NFI9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'NFI10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'NFI11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'NFI12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'NFI13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'NFI14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'NFI15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'NFI16',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'NFI17',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'NFI18',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'NFI19',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'NFI20',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'NFI21',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'NFI22',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'NFI23',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'NFI24',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'NFI25',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'NFI26',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'NFI27',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'NFI28',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'NFI29',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'NFI30',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'NFI31',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_PSI5_NFIOV_Bits',0,6,208,9,3
	.word	26418
	.byte	10
	.byte	'_Ifx_PSI5_NFISET_Bits',0,6,211,9,16,4,11
	.byte	'NFI0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'NFI1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'NFI2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'NFI3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'NFI4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'NFI5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'NFI6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'NFI7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'NFI8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'NFI9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'NFI10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'NFI11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'NFI12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'NFI13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'NFI14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'NFI15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'NFI16',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'NFI17',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'NFI18',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'NFI19',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'NFI20',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'NFI21',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'NFI22',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'NFI23',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'NFI24',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'NFI25',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'NFI26',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'NFI27',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'NFI28',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'NFI29',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'NFI30',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'NFI31',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_PSI5_NFISET_Bits',0,6,245,9,3
	.word	27009
	.byte	10
	.byte	'_Ifx_PSI5_OCS_Bits',0,6,248,9,16,4,11
	.byte	'reserved_0',0,4
	.word	470
	.byte	24,8,2,35,0,11
	.byte	'SUS',0,1
	.word	493
	.byte	4,4,2,35,3,11
	.byte	'SUS_P',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'SUSSTA',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	493
	.byte	2,0,2,35,3,0,21
	.byte	'Ifx_PSI5_OCS_Bits',0,6,255,9,3
	.word	27602
	.byte	10
	.byte	'_Ifx_PSI5_RDF_Bits',0,6,130,10,16,4,11
	.byte	'RD',0,4
	.word	470
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_PSI5_RDF_Bits',0,6,133,10,3
	.word	27750
	.byte	10
	.byte	'_Ifx_PSI5_RDICLR_Bits',0,6,136,10,16,4,11
	.byte	'RDI0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'RDI1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'RDI2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'RDI3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'RDI4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'RDI5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'RDI6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'RDI7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'RDI8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'RDI9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'RDI10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'RDI11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'RDI12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'RDI13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'RDI14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'RDI15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'RDI16',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'RDI17',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'RDI18',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'RDI19',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'RDI20',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'RDI21',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'RDI22',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'RDI23',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'RDI24',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'RDI25',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'RDI26',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'RDI27',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'RDI28',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'RDI29',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'RDI30',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'RDI31',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_PSI5_RDICLR_Bits',0,6,170,10,3
	.word	27817
	.byte	10
	.byte	'_Ifx_PSI5_RDIOV_Bits',0,6,173,10,16,4,11
	.byte	'RDI0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'RDI1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'RDI2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'RDI3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'RDI4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'RDI5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'RDI6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'RDI7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'RDI8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'RDI9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'RDI10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'RDI11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'RDI12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'RDI13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'RDI14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'RDI15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'RDI16',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'RDI17',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'RDI18',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'RDI19',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'RDI20',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'RDI21',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'RDI22',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'RDI23',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'RDI24',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'RDI25',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'RDI26',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'RDI27',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'RDI28',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'RDI29',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'RDI30',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'RDI31',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_PSI5_RDIOV_Bits',0,6,207,10,3
	.word	28410
	.byte	10
	.byte	'_Ifx_PSI5_RDISET_Bits',0,6,210,10,16,4,11
	.byte	'RDI0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'RDI1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'RDI2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'RDI3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'RDI4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'RDI5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'RDI6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'RDI7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'RDI8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'RDI9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'RDI10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'RDI11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'RDI12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'RDI13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'RDI14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'RDI15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'RDI16',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'RDI17',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'RDI18',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'RDI19',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'RDI20',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'RDI21',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'RDI22',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'RDI23',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'RDI24',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'RDI25',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'RDI26',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'RDI27',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'RDI28',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'RDI29',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'RDI30',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'RDI31',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_PSI5_RDISET_Bits',0,6,244,10,3
	.word	29001
	.byte	10
	.byte	'_Ifx_PSI5_RDM_H_Bits',0,6,247,10,16,4,11
	.byte	'TS',0,4
	.word	470
	.byte	24,8,2,35,0,11
	.byte	'SC',0,1
	.word	493
	.byte	3,5,2,35,3,11
	.byte	'TEI',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'NBI',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'MEI',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'NFI',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'RMI',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_PSI5_RDM_H_Bits',0,6,128,11,3
	.word	29594
	.byte	10
	.byte	'_Ifx_PSI5_RDM_L_Bits',0,6,131,11,16,4,11
	.byte	'CRCI',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'CRC',0,1
	.word	493
	.byte	3,4,2,35,0,11
	.byte	'RD',0,4
	.word	470
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_PSI5_RDM_L_Bits',0,6,136,11,3
	.word	29754
	.byte	10
	.byte	'_Ifx_PSI5_RFC_Bits',0,6,139,11,16,4,11
	.byte	'REP',0,1
	.word	493
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	493
	.byte	2,0,2,35,0,11
	.byte	'WRP',0,1
	.word	493
	.byte	6,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	493
	.byte	2,0,2,35,1,11
	.byte	'FWL',0,1
	.word	493
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,2
	.word	510
	.byte	8,3,2,35,2,11
	.byte	'WRAP',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'FRQ',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'FLU',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_PSI5_RFC_Bits',0,6,150,11,3
	.word	29856
	.byte	10
	.byte	'_Ifx_PSI5_RMICLR_Bits',0,6,153,11,16,4,11
	.byte	'RMI0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'RMI1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'RMI2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'RMI3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'RMI4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'RMI5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'RMI6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'RMI7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'RMI8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'RMI9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'RMI10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'RMI11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'RMI12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'RMI13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'RMI14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'RMI15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'RMI16',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'RMI17',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'RMI18',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'RMI19',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'RMI20',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'RMI21',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'RMI22',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'RMI23',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'RMI24',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'RMI25',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'RMI26',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'RMI27',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'RMI28',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'RMI29',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'RMI30',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'RMI31',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_PSI5_RMICLR_Bits',0,6,187,11,3
	.word	30068
	.byte	10
	.byte	'_Ifx_PSI5_RMIOV_Bits',0,6,190,11,16,4,11
	.byte	'RMI0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'RMI1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'RMI2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'RMI3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'RMI4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'RMI5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'RMI6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'RMI7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'RMI8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'RMI9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'RMI10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'RMI11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'RMI12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'RMI13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'RMI14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'RMI15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'RMI16',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'RMI17',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'RMI18',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'RMI19',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'RMI20',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'RMI21',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'RMI22',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'RMI23',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'RMI24',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'RMI25',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'RMI26',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'RMI27',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'RMI28',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'RMI29',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'RMI30',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'RMI31',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_PSI5_RMIOV_Bits',0,6,224,11,3
	.word	30661
	.byte	10
	.byte	'_Ifx_PSI5_RMISET_Bits',0,6,227,11,16,4,11
	.byte	'RMI0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'RMI1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'RMI2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'RMI3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'RMI4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'RMI5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'RMI6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'RMI7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'RMI8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'RMI9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'RMI10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'RMI11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'RMI12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'RMI13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'RMI14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'RMI15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'RMI16',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'RMI17',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'RMI18',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'RMI19',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'RMI20',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'RMI21',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'RMI22',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'RMI23',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'RMI24',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'RMI25',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'RMI26',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'RMI27',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'RMI28',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'RMI29',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'RMI30',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'RMI31',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_PSI5_RMISET_Bits',0,6,133,12,3
	.word	31252
	.byte	10
	.byte	'_Ifx_PSI5_RSICLR_Bits',0,6,136,12,16,4,11
	.byte	'RSI0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'RSI1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'RSI2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'RSI3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'RSI4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'RSI5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'RSI6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'RSI7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'RSI8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'RSI9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'RSI10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'RSI11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'RSI12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'RSI13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'RSI14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'RSI15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'RSI16',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'RSI17',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'RSI18',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'RSI19',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'RSI20',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'RSI21',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'RSI22',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'RSI23',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'RSI24',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'RSI25',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'RSI26',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'RSI27',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'RSI28',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'RSI29',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'RSI30',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'RSI31',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_PSI5_RSICLR_Bits',0,6,170,12,3
	.word	31845
	.byte	10
	.byte	'_Ifx_PSI5_RSIOV_Bits',0,6,173,12,16,4,11
	.byte	'RSI0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'RSI1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'RSI2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'RSI3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'RSI4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'RSI5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'RSI6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'RSI7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'RSI8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'RSI9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'RSI10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'RSI11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'RSI12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'RSI13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'RSI14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'RSI15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'RSI16',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'RSI17',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'RSI18',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'RSI19',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'RSI20',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'RSI21',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'RSI22',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'RSI23',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'RSI24',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'RSI25',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'RSI26',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'RSI27',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'RSI28',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'RSI29',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'RSI30',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'RSI31',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_PSI5_RSIOV_Bits',0,6,207,12,3
	.word	32438
	.byte	10
	.byte	'_Ifx_PSI5_RSISET_Bits',0,6,210,12,16,4,11
	.byte	'RSI0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'RSI1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'RSI2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'RSI3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'RSI4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'RSI5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'RSI6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'RSI7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'RSI8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'RSI9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'RSI10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'RSI11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'RSI12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'RSI13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'RSI14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'RSI15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'RSI16',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'RSI17',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'RSI18',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'RSI19',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'RSI20',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'RSI21',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'RSI22',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'RSI23',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'RSI24',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'RSI25',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'RSI26',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'RSI27',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'RSI28',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'RSI29',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'RSI30',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'RSI31',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_PSI5_RSISET_Bits',0,6,244,12,3
	.word	33029
	.byte	10
	.byte	'_Ifx_PSI5_TEICLR_Bits',0,6,247,12,16,4,11
	.byte	'TEI0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'TEI1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'TEI2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'TEI3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'TEI4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'TEI5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'TEI6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'TEI7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'TEI8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'TEI9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'TEI10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'TEI11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'TEI12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'TEI13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'TEI14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'TEI15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'TEI16',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'TEI17',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'TEI18',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'TEI19',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'TEI20',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'TEI21',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'TEI22',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'TEI23',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'TEI24',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'TEI25',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'TEI26',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'TEI27',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'TEI28',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'TEI29',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'TEI30',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'TEI31',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_PSI5_TEICLR_Bits',0,6,153,13,3
	.word	33622
	.byte	10
	.byte	'_Ifx_PSI5_TEIOV_Bits',0,6,156,13,16,4,11
	.byte	'TEI0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'TEI1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'TEI2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'TEI3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'TEI4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'TEI5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'TEI6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'TEI7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'TEI8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'TEI9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'TEI10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'TEI11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'TEI12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'TEI13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'TEI14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'TEI15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'TEI16',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'TEI17',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'TEI18',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'TEI19',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'TEI20',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'TEI21',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'TEI22',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'TEI23',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'TEI24',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'TEI25',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'TEI26',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'TEI27',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'TEI28',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'TEI29',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'TEI30',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'TEI31',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_PSI5_TEIOV_Bits',0,6,190,13,3
	.word	34215
	.byte	10
	.byte	'_Ifx_PSI5_TEISET_Bits',0,6,193,13,16,4,11
	.byte	'TEI0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'TEI1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'TEI2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'TEI3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'TEI4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'TEI5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'TEI6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'TEI7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'TEI8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'TEI9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'TEI10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'TEI11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'TEI12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'TEI13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'TEI14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'TEI15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'TEI16',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'TEI17',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'TEI18',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'TEI19',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'TEI20',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'TEI21',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'TEI22',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'TEI23',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'TEI24',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'TEI25',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'TEI26',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'TEI27',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'TEI28',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'TEI29',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'TEI30',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'TEI31',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_PSI5_TEISET_Bits',0,6,227,13,3
	.word	34806
	.byte	10
	.byte	'_Ifx_PSI5_TSR_Bits',0,6,230,13,16,4,11
	.byte	'CTS',0,4
	.word	470
	.byte	24,8,2,35,0,11
	.byte	'ETB',0,1
	.word	493
	.byte	3,5,2,35,3,11
	.byte	'TBS',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	493
	.byte	2,2,2,35,3,11
	.byte	'ACLR',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'CLR',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_PSI5_TSR_Bits',0,6,238,13,3
	.word	35399
	.byte	12,6,246,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8639
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5_ACCEN0',0,6,251,13,3
	.word	35551
	.byte	12,6,254,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9198
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5_ACCEN1',0,6,131,14,3
	.word	35616
	.byte	12,6,134,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9277
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5_CH_CTV',0,6,139,14,3
	.word	35681
	.byte	12,6,142,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9364
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5_CH_IOCR',0,6,147,14,3
	.word	35746
	.byte	12,6,150,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9646
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5_CH_PGC',0,6,155,14,3
	.word	35812
	.byte	12,6,158,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9884
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5_CH_RCRA',0,6,163,14,3
	.word	35877
	.byte	12,6,166,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10073
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5_CH_RCRB',0,6,171,14,3
	.word	35943
	.byte	12,6,174,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10541
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5_CH_RCRC',0,6,179,14,3
	.word	36009
	.byte	12,6,182,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10684
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5_CH_RDRH',0,6,187,14,3
	.word	36075
	.byte	12,6,190,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10848
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5_CH_RDRL',0,6,195,14,3
	.word	36141
	.byte	12,6,198,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10954
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5_CH_RSR',0,6,203,14,3
	.word	36207
	.byte	12,6,206,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11088
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5_CH_SCR',0,6,211,14,3
	.word	36272
	.byte	12,6,214,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11396
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5_CH_SDRH',0,6,219,14,3
	.word	36337
	.byte	12,6,222,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11969
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5_CH_SDRL',0,6,227,14,3
	.word	36403
	.byte	12,6,230,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12532
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5_CH_SDS',0,6,235,14,3
	.word	36469
	.byte	12,6,238,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12667
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5_CH_SFTSC',0,6,243,14,3
	.word	36534
	.byte	12,6,246,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12767
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5_CH_SORH',0,6,251,14,3
	.word	36601
	.byte	12,6,254,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13340
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5_CH_SORL',0,6,131,15,3
	.word	36667
	.byte	12,6,134,15,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13903
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5_CH_SPTSC',0,6,139,15,3
	.word	36733
	.byte	12,6,142,15,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14003
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5_CH_SSRH',0,6,147,15,3
	.word	36800
	.byte	12,6,150,15,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14576
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5_CH_SSRL',0,6,155,15,3
	.word	36866
	.byte	12,6,158,15,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15139
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5_CH_WDT',0,6,163,15,3
	.word	36932
	.byte	12,6,166,15,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15238
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5_CLC',0,6,171,15,3
	.word	36997
	.byte	12,6,174,15,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15345
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5_CRCICLR',0,6,179,15,3
	.word	37059
	.byte	12,6,182,15,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15972
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5_CRCIOV',0,6,187,15,3
	.word	37125
	.byte	12,6,190,15,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16597
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5_CRCISET',0,6,195,15,3
	.word	37190
	.byte	12,6,198,15,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17224
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5_FDR',0,6,203,15,3
	.word	37256
	.byte	12,6,206,15,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17371
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5_FDRH',0,6,211,15,3
	.word	37318
	.byte	12,6,214,15,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17520
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5_FDRL',0,6,219,15,3
	.word	37381
	.byte	12,6,222,15,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17669
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5_FDRT',0,6,227,15,3
	.word	37444
	.byte	12,6,230,15,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17858
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5_GCR',0,6,235,15,3
	.word	37507
	.byte	12,6,238,15,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18215
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5_ID',0,6,243,15,3
	.word	37569
	.byte	12,6,246,15,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18324
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5_INP',0,6,251,15,3
	.word	37630
	.byte	12,6,254,15,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18498
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5_INTCLRA',0,6,131,16,3
	.word	37692
	.byte	12,6,134,16,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18841
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5_INTCLRB',0,6,139,16,3
	.word	37758
	.byte	12,6,142,16,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19315
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5_INTENA',0,6,147,16,3
	.word	37824
	.byte	12,6,150,16,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19656
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5_INTENB',0,6,155,16,3
	.word	37889
	.byte	12,6,158,16,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20128
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5_INTOV',0,6,163,16,3
	.word	37954
	.byte	12,6,166,16,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20328
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5_INTSETA',0,6,171,16,3
	.word	38018
	.byte	12,6,174,16,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20671
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5_INTSETB',0,6,179,16,3
	.word	38084
	.byte	12,6,182,16,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21145
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5_INTSTATA',0,6,187,16,3
	.word	38150
	.byte	12,6,190,16,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21490
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5_INTSTATB',0,6,195,16,3
	.word	38217
	.byte	12,6,198,16,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21966
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5_KRST0',0,6,203,16,3
	.word	38284
	.byte	12,6,206,16,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22079
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5_KRST1',0,6,211,16,3
	.word	38348
	.byte	12,6,214,16,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22173
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5_KRSTCLR',0,6,219,16,3
	.word	38412
	.byte	12,6,222,16,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22271
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5_MEICLR',0,6,227,16,3
	.word	38478
	.byte	12,6,230,16,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22864
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5_MEIOV',0,6,235,16,3
	.word	38543
	.byte	12,6,238,16,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23455
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5_MEISET',0,6,243,16,3
	.word	38607
	.byte	12,6,246,16,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24048
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5_NBICLR',0,6,251,16,3
	.word	38672
	.byte	12,6,254,16,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24641
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5_NBIOV',0,6,131,17,3
	.word	38737
	.byte	12,6,134,17,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25232
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5_NBISET',0,6,139,17,3
	.word	38801
	.byte	12,6,142,17,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25825
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5_NFICLR',0,6,147,17,3
	.word	38866
	.byte	12,6,150,17,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26418
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5_NFIOV',0,6,155,17,3
	.word	38931
	.byte	12,6,158,17,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27009
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5_NFISET',0,6,163,17,3
	.word	38995
	.byte	12,6,166,17,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27602
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5_OCS',0,6,171,17,3
	.word	39060
	.byte	12,6,174,17,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27750
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5_RDF',0,6,179,17,3
	.word	39122
	.byte	12,6,182,17,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27817
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5_RDICLR',0,6,187,17,3
	.word	39184
	.byte	12,6,190,17,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28410
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5_RDIOV',0,6,195,17,3
	.word	39249
	.byte	12,6,198,17,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29001
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5_RDISET',0,6,203,17,3
	.word	39313
	.byte	12,6,206,17,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29594
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5_RDM_H',0,6,211,17,3
	.word	39378
	.byte	12,6,214,17,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29754
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5_RDM_L',0,6,219,17,3
	.word	39442
	.byte	12,6,222,17,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29856
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5_RFC',0,6,227,17,3
	.word	39506
	.byte	12,6,230,17,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30068
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5_RMICLR',0,6,235,17,3
	.word	39568
	.byte	12,6,238,17,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30661
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5_RMIOV',0,6,243,17,3
	.word	39633
	.byte	12,6,246,17,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31252
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5_RMISET',0,6,251,17,3
	.word	39697
	.byte	12,6,254,17,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31845
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5_RSICLR',0,6,131,18,3
	.word	39762
	.byte	12,6,134,18,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32438
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5_RSIOV',0,6,139,18,3
	.word	39827
	.byte	12,6,142,18,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33029
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5_RSISET',0,6,147,18,3
	.word	39891
	.byte	12,6,150,18,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33622
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5_TEICLR',0,6,155,18,3
	.word	39956
	.byte	12,6,158,18,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34215
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5_TEIOV',0,6,163,18,3
	.word	40021
	.byte	12,6,166,18,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34806
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5_TEISET',0,6,171,18,3
	.word	40085
	.byte	12,6,174,18,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35399
	.byte	4,2,35,0,0,21
	.byte	'Ifx_PSI5_TSR',0,6,179,18,3
	.word	40150
	.byte	14,28
	.word	36932
	.byte	15,6,0,14,24
	.word	36469
	.byte	15,5,0,14,20
	.word	493
	.byte	15,19,0,10
	.byte	'_Ifx_PSI5_CH',0,6,190,18,25,144,1,13
	.byte	'IOCR',0
	.word	35746
	.byte	4,2,35,0,13
	.byte	'RCRA',0
	.word	35877
	.byte	4,2,35,4,13
	.byte	'RCRB',0
	.word	35943
	.byte	4,2,35,8,13
	.byte	'RCRC',0
	.word	36009
	.byte	4,2,35,12,13
	.byte	'WDT',0
	.word	40212
	.byte	28,2,35,16,13
	.byte	'RSR',0
	.word	36207
	.byte	4,2,35,44,13
	.byte	'SDS',0
	.word	40221
	.byte	24,2,35,48,13
	.byte	'SPTSC',0
	.word	36733
	.byte	4,2,35,72,13
	.byte	'SFTSC',0
	.word	36534
	.byte	4,2,35,76,13
	.byte	'RDRL',0
	.word	36141
	.byte	4,2,35,80,13
	.byte	'RDRH',0
	.word	36075
	.byte	4,2,35,84,13
	.byte	'PGC',0
	.word	35812
	.byte	4,2,35,88,13
	.byte	'CTV',0
	.word	35681
	.byte	4,2,35,92,13
	.byte	'SCR',0
	.word	36272
	.byte	4,2,35,96,13
	.byte	'SDRL',0
	.word	36403
	.byte	4,2,35,100,13
	.byte	'SDRH',0
	.word	36337
	.byte	4,2,35,104,13
	.byte	'SSRL',0
	.word	36866
	.byte	4,2,35,108,13
	.byte	'SSRH',0
	.word	36800
	.byte	4,2,35,112,13
	.byte	'SORL',0
	.word	36667
	.byte	4,2,35,116,13
	.byte	'SORH',0
	.word	36601
	.byte	4,2,35,120,13
	.byte	'reserved_7C',0
	.word	40230
	.byte	20,2,35,124,0,16
	.word	40239
	.byte	21
	.byte	'Ifx_PSI5_CH',0,6,213,18,3
	.word	40557
	.byte	10
	.byte	'_Ifx_PSI5_RDM',0,6,216,18,25,8,13
	.byte	'L',0
	.word	39442
	.byte	4,2,35,0,13
	.byte	'H',0
	.word	39378
	.byte	4,2,35,4,0,16
	.word	40583
	.byte	21
	.byte	'Ifx_PSI5_RDM',0,6,220,18,3
	.word	40626
	.byte	14,160,2
	.word	40239
	.byte	15,1,0,16
	.word	40653
	.byte	14,168,3
	.word	493
	.byte	15,167,3,0,14,8
	.word	37630
	.byte	15,1,0,14,8
	.word	38150
	.byte	15,1,0,14,8
	.word	38217
	.byte	15,1,0,14,8
	.word	38018
	.byte	15,1,0,14,8
	.word	38084
	.byte	15,1,0,14,8
	.word	37692
	.byte	15,1,0,14,8
	.word	37758
	.byte	15,1,0,14,8
	.word	37824
	.byte	15,1,0,14,8
	.word	37889
	.byte	15,1,0,14,40
	.word	493
	.byte	15,39,0,14,8
	.word	39506
	.byte	15,1,0,14,8
	.word	39122
	.byte	15,1,0,14,8
	.word	39827
	.byte	15,1,0,14,8
	.word	39633
	.byte	15,1,0,14,8
	.word	38737
	.byte	15,1,0,14,8
	.word	40021
	.byte	15,1,0,14,8
	.word	37125
	.byte	15,1,0,14,8
	.word	39249
	.byte	15,1,0,14,8
	.word	38931
	.byte	15,1,0,14,8
	.word	38543
	.byte	15,1,0,14,8
	.word	39891
	.byte	15,1,0,14,8
	.word	39697
	.byte	15,1,0,14,8
	.word	38801
	.byte	15,1,0,14,8
	.word	40085
	.byte	15,1,0,14,8
	.word	37190
	.byte	15,1,0,14,8
	.word	39313
	.byte	15,1,0,14,8
	.word	38995
	.byte	15,1,0,14,8
	.word	38607
	.byte	15,1,0,14,8
	.word	39762
	.byte	15,1,0,14,8
	.word	39568
	.byte	15,1,0,14,8
	.word	38672
	.byte	15,1,0,14,8
	.word	39956
	.byte	15,1,0,14,8
	.word	37059
	.byte	15,1,0,14,8
	.word	39184
	.byte	15,1,0,14,8
	.word	38866
	.byte	15,1,0,14,8
	.word	38478
	.byte	15,1,0,14,32
	.word	493
	.byte	15,31,0,14,128,2
	.word	40583
	.byte	15,31,0,14,128,4
	.word	41012
	.byte	15,1,0,16
	.word	41022
	.byte	14,128,6
	.word	493
	.byte	15,255,5,0,10
	.byte	'_Ifx_PSI5',0,6,233,18,25,128,22,13
	.byte	'CLC',0
	.word	36997
	.byte	4,2,35,0,13
	.byte	'reserved_4',0
	.word	1538
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	37569
	.byte	4,2,35,8,13
	.byte	'FDR',0
	.word	37256
	.byte	4,2,35,12,13
	.byte	'FDRL',0
	.word	37381
	.byte	4,2,35,16,13
	.byte	'FDRH',0
	.word	37318
	.byte	4,2,35,20,13
	.byte	'FDRT',0
	.word	37444
	.byte	4,2,35,24,13
	.byte	'TSRA',0
	.word	40150
	.byte	4,2,35,28,13
	.byte	'TSRB',0
	.word	40150
	.byte	4,2,35,32,13
	.byte	'TSRC',0
	.word	40150
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	1538
	.byte	4,2,35,40,13
	.byte	'GCR',0
	.word	37507
	.byte	4,2,35,44,13
	.byte	'CH',0
	.word	40663
	.byte	160,2,2,35,48,13
	.byte	'reserved_150',0
	.word	40668
	.byte	168,3,3,35,208,2,13
	.byte	'INTOV',0
	.word	37954
	.byte	4,3,35,248,5,13
	.byte	'INP',0
	.word	40679
	.byte	8,3,35,252,5,13
	.byte	'reserved_304',0
	.word	3697
	.byte	12,3,35,132,6,13
	.byte	'INTSTATA',0
	.word	40688
	.byte	8,3,35,144,6,13
	.byte	'reserved_318',0
	.word	3697
	.byte	12,3,35,152,6,13
	.byte	'INTSTATB',0
	.word	40697
	.byte	8,3,35,164,6,13
	.byte	'reserved_32C',0
	.word	3697
	.byte	12,3,35,172,6,13
	.byte	'INTSETA',0
	.word	40706
	.byte	8,3,35,184,6,13
	.byte	'reserved_340',0
	.word	3697
	.byte	12,3,35,192,6,13
	.byte	'INTSETB',0
	.word	40715
	.byte	8,3,35,204,6,13
	.byte	'reserved_354',0
	.word	3697
	.byte	12,3,35,212,6,13
	.byte	'INTCLRA',0
	.word	40724
	.byte	8,3,35,224,6,13
	.byte	'reserved_368',0
	.word	3697
	.byte	12,3,35,232,6,13
	.byte	'INTCLRB',0
	.word	40733
	.byte	8,3,35,244,6,13
	.byte	'reserved_37C',0
	.word	3697
	.byte	12,3,35,252,6,13
	.byte	'INTENA',0
	.word	40742
	.byte	8,3,35,136,7,13
	.byte	'reserved_390',0
	.word	3697
	.byte	12,3,35,144,7,13
	.byte	'INTENB',0
	.word	40751
	.byte	8,3,35,156,7,13
	.byte	'reserved_3A4',0
	.word	40760
	.byte	40,3,35,164,7,13
	.byte	'OCS',0
	.word	39060
	.byte	4,3,35,204,7,13
	.byte	'ACCEN0',0
	.word	35551
	.byte	4,3,35,208,7,13
	.byte	'ACCEN1',0
	.word	35616
	.byte	4,3,35,212,7,13
	.byte	'KRST0',0
	.word	38284
	.byte	4,3,35,216,7,13
	.byte	'KRST1',0
	.word	38348
	.byte	4,3,35,220,7,13
	.byte	'KRSTCLR',0
	.word	38412
	.byte	4,3,35,224,7,13
	.byte	'RFC',0
	.word	40769
	.byte	8,3,35,228,7,13
	.byte	'reserved_3EC',0
	.word	3697
	.byte	12,3,35,236,7,13
	.byte	'RDF',0
	.word	40778
	.byte	8,3,35,248,7,13
	.byte	'reserved_400',0
	.word	3697
	.byte	12,3,35,128,8,13
	.byte	'RSIOV',0
	.word	40787
	.byte	8,3,35,140,8,13
	.byte	'reserved_414',0
	.word	3697
	.byte	12,3,35,148,8,13
	.byte	'RMIOV',0
	.word	40796
	.byte	8,3,35,160,8,13
	.byte	'reserved_428',0
	.word	3697
	.byte	12,3,35,168,8,13
	.byte	'NBIOV',0
	.word	40805
	.byte	8,3,35,180,8,13
	.byte	'reserved_43C',0
	.word	3697
	.byte	12,3,35,188,8,13
	.byte	'TEIOV',0
	.word	40814
	.byte	8,3,35,200,8,13
	.byte	'reserved_450',0
	.word	3697
	.byte	12,3,35,208,8,13
	.byte	'CRCIOV',0
	.word	40823
	.byte	8,3,35,220,8,13
	.byte	'reserved_464',0
	.word	3697
	.byte	12,3,35,228,8,13
	.byte	'RDIOV',0
	.word	40832
	.byte	8,3,35,240,8,13
	.byte	'reserved_478',0
	.word	3697
	.byte	12,3,35,248,8,13
	.byte	'NFIOV',0
	.word	40841
	.byte	8,3,35,132,9,13
	.byte	'reserved_48C',0
	.word	3697
	.byte	12,3,35,140,9,13
	.byte	'MEIOV',0
	.word	40850
	.byte	8,3,35,152,9,13
	.byte	'reserved_4A0',0
	.word	3697
	.byte	12,3,35,160,9,13
	.byte	'RSISET',0
	.word	40859
	.byte	8,3,35,172,9,13
	.byte	'reserved_4B4',0
	.word	3697
	.byte	12,3,35,180,9,13
	.byte	'RMISET',0
	.word	40868
	.byte	8,3,35,192,9,13
	.byte	'reserved_4C8',0
	.word	3697
	.byte	12,3,35,200,9,13
	.byte	'NBISET',0
	.word	40877
	.byte	8,3,35,212,9,13
	.byte	'reserved_4DC',0
	.word	3697
	.byte	12,3,35,220,9,13
	.byte	'TEISET',0
	.word	40886
	.byte	8,3,35,232,9,13
	.byte	'reserved_4F0',0
	.word	3697
	.byte	12,3,35,240,9,13
	.byte	'CRCISET',0
	.word	40895
	.byte	8,3,35,252,9,13
	.byte	'reserved_504',0
	.word	3697
	.byte	12,3,35,132,10,13
	.byte	'RDISET',0
	.word	40904
	.byte	8,3,35,144,10,13
	.byte	'reserved_518',0
	.word	3697
	.byte	12,3,35,152,10,13
	.byte	'NFISET',0
	.word	40913
	.byte	8,3,35,164,10,13
	.byte	'reserved_52C',0
	.word	3697
	.byte	12,3,35,172,10,13
	.byte	'MEISET',0
	.word	40922
	.byte	8,3,35,184,10,13
	.byte	'reserved_540',0
	.word	3697
	.byte	12,3,35,192,10,13
	.byte	'RSICLR',0
	.word	40931
	.byte	8,3,35,204,10,13
	.byte	'reserved_554',0
	.word	3697
	.byte	12,3,35,212,10,13
	.byte	'RMICLR',0
	.word	40940
	.byte	8,3,35,224,10,13
	.byte	'reserved_568',0
	.word	3697
	.byte	12,3,35,232,10,13
	.byte	'NBICLR',0
	.word	40949
	.byte	8,3,35,244,10,13
	.byte	'reserved_57C',0
	.word	3697
	.byte	12,3,35,252,10,13
	.byte	'TEICLR',0
	.word	40958
	.byte	8,3,35,136,11,13
	.byte	'reserved_590',0
	.word	3697
	.byte	12,3,35,144,11,13
	.byte	'CRCICLR',0
	.word	40967
	.byte	8,3,35,156,11,13
	.byte	'reserved_5A4',0
	.word	3697
	.byte	12,3,35,164,11,13
	.byte	'RDICLR',0
	.word	40976
	.byte	8,3,35,176,11,13
	.byte	'reserved_5B8',0
	.word	3697
	.byte	12,3,35,184,11,13
	.byte	'NFICLR',0
	.word	40985
	.byte	8,3,35,196,11,13
	.byte	'reserved_5CC',0
	.word	3697
	.byte	12,3,35,204,11,13
	.byte	'MEICLR',0
	.word	40994
	.byte	8,3,35,216,11,13
	.byte	'reserved_5E0',0
	.word	41003
	.byte	32,3,35,224,11,13
	.byte	'RDM',0
	.word	41032
	.byte	128,4,3,35,128,12,13
	.byte	'reserved_800',0
	.word	41037
	.byte	128,6,3,35,128,16,0,16
	.word	41048
	.byte	21
	.byte	'Ifx_PSI5',0,6,200,19,3
	.word	42826
	.byte	17,7,91,9,1,18
	.byte	'IfxPsi5_ChannelId_none',0,127,18
	.byte	'IfxPsi5_ChannelId_0',0,0,18
	.byte	'IfxPsi5_ChannelId_1',0,1,18
	.byte	'IfxPsi5_ChannelId_2',0,2,0,21
	.byte	'IfxPsi5_ChannelId',0,7,97,3
	.word	42849
	.byte	21
	.byte	'boolean',0,8,101,29
	.word	493
	.byte	21
	.byte	'uint8',0,8,105,29
	.word	493
	.byte	21
	.byte	'uint16',0,8,109,29
	.word	510
	.byte	7
	.byte	'unsigned long int',0,4,7,21
	.byte	'uint32',0,8,113,29
	.word	43017
	.byte	21
	.byte	'uint64',0,8,118,29
	.word	352
	.byte	21
	.byte	'sint16',0,8,126,29
	.word	8546
	.byte	7
	.byte	'long int',0,4,5,21
	.byte	'sint32',0,8,131,1,29
	.word	43083
	.byte	7
	.byte	'long long int',0,8,5,21
	.byte	'sint64',0,8,138,1,29
	.word	43111
	.byte	21
	.byte	'float32',0,8,167,1,29
	.word	298
	.byte	21
	.byte	'pvoid',0,9,57,28
	.word	384
	.byte	21
	.byte	'Ifx_TickTime',0,9,79,28
	.word	43111
	.byte	17,9,130,1,9,1,18
	.byte	'Ifx_RxSel_a',0,0,18
	.byte	'Ifx_RxSel_b',0,1,18
	.byte	'Ifx_RxSel_c',0,2,18
	.byte	'Ifx_RxSel_d',0,3,18
	.byte	'Ifx_RxSel_e',0,4,18
	.byte	'Ifx_RxSel_f',0,5,18
	.byte	'Ifx_RxSel_g',0,6,18
	.byte	'Ifx_RxSel_h',0,7,0,21
	.byte	'Ifx_RxSel',0,9,140,1,3
	.word	43196
	.byte	21
	.byte	'Ifx_P_ACCEN0_Bits',0,4,79,3
	.word	7110
	.byte	21
	.byte	'Ifx_P_ACCEN1_Bits',0,4,85,3
	.word	7023
	.byte	21
	.byte	'Ifx_P_ESR_Bits',0,4,107,3
	.word	3366
	.byte	21
	.byte	'Ifx_P_ID_Bits',0,4,115,3
	.word	1419
	.byte	21
	.byte	'Ifx_P_IN_Bits',0,4,137,1,3
	.word	2414
	.byte	21
	.byte	'Ifx_P_IOCR0_Bits',0,4,150,1,3
	.word	1547
	.byte	21
	.byte	'Ifx_P_IOCR12_Bits',0,4,163,1,3
	.word	2194
	.byte	21
	.byte	'Ifx_P_IOCR4_Bits',0,4,176,1,3
	.word	1762
	.byte	21
	.byte	'Ifx_P_IOCR8_Bits',0,4,189,1,3
	.word	1977
	.byte	21
	.byte	'Ifx_P_LPCR0_Bits',0,4,197,1,3
	.word	6382
	.byte	21
	.byte	'Ifx_P_LPCR1_Bits',0,4,205,1,3
	.word	6506
	.byte	21
	.byte	'Ifx_P_LPCR1_P21_Bits',0,4,215,1,3
	.word	6590
	.byte	21
	.byte	'Ifx_P_LPCR2_Bits',0,4,229,1,3
	.word	6770
	.byte	21
	.byte	'Ifx_P_OMCR0_Bits',0,4,240,1,3
	.word	5021
	.byte	21
	.byte	'Ifx_P_OMCR12_Bits',0,4,250,1,3
	.word	5545
	.byte	21
	.byte	'Ifx_P_OMCR4_Bits',0,4,133,2,3
	.word	5195
	.byte	21
	.byte	'Ifx_P_OMCR8_Bits',0,4,144,2,3
	.word	5369
	.byte	21
	.byte	'Ifx_P_OMCR_Bits',0,4,166,2,3
	.word	6034
	.byte	21
	.byte	'Ifx_P_OMR_Bits',0,4,203,2,3
	.word	848
	.byte	21
	.byte	'Ifx_P_OMSR0_Bits',0,4,213,2,3
	.word	4358
	.byte	21
	.byte	'Ifx_P_OMSR12_Bits',0,4,224,2,3
	.word	4846
	.byte	21
	.byte	'Ifx_P_OMSR4_Bits',0,4,235,2,3
	.word	4505
	.byte	21
	.byte	'Ifx_P_OMSR8_Bits',0,4,246,2,3
	.word	4674
	.byte	21
	.byte	'Ifx_P_OMSR_Bits',0,4,140,3,3
	.word	5701
	.byte	21
	.byte	'Ifx_P_OUT_Bits',0,4,162,3,3
	.word	532
	.byte	21
	.byte	'Ifx_P_PCSR_Bits',0,4,180,3,3
	.word	4072
	.byte	21
	.byte	'Ifx_P_PDISC_Bits',0,4,202,3,3
	.word	3706
	.byte	21
	.byte	'Ifx_P_PDR0_Bits',0,4,223,3,3
	.word	2737
	.byte	21
	.byte	'Ifx_P_PDR1_Bits',0,4,244,3,3
	.word	3041
	.byte	21
	.byte	'Ifx_P_ACCEN0',0,4,129,4,3
	.word	7637
	.byte	21
	.byte	'Ifx_P_ACCEN1',0,4,137,4,3
	.word	7070
	.byte	21
	.byte	'Ifx_P_ESR',0,4,145,4,3
	.word	3657
	.byte	21
	.byte	'Ifx_P_ID',0,4,153,4,3
	.word	1498
	.byte	21
	.byte	'Ifx_P_IN',0,4,161,4,3
	.word	2688
	.byte	21
	.byte	'Ifx_P_IOCR0',0,4,169,4,3
	.word	1722
	.byte	21
	.byte	'Ifx_P_IOCR12',0,4,177,4,3
	.word	2374
	.byte	21
	.byte	'Ifx_P_IOCR4',0,4,185,4,3
	.word	1937
	.byte	21
	.byte	'Ifx_P_IOCR8',0,4,193,4,3
	.word	2154
	.byte	21
	.byte	'Ifx_P_LPCR0',0,4,201,4,3
	.word	6466
	.byte	21
	.byte	'Ifx_P_LPCR1',0,4,210,4,3
	.word	6715
	.byte	21
	.byte	'Ifx_P_LPCR2',0,4,218,4,3
	.word	6974
	.byte	21
	.byte	'Ifx_P_OMCR',0,4,226,4,3
	.word	6342
	.byte	21
	.byte	'Ifx_P_OMCR0',0,4,234,4,3
	.word	5155
	.byte	21
	.byte	'Ifx_P_OMCR12',0,4,242,4,3
	.word	5661
	.byte	21
	.byte	'Ifx_P_OMCR4',0,4,250,4,3
	.word	5329
	.byte	21
	.byte	'Ifx_P_OMCR8',0,4,130,5,3
	.word	5505
	.byte	21
	.byte	'Ifx_P_OMR',0,4,138,5,3
	.word	1379
	.byte	21
	.byte	'Ifx_P_OMSR',0,4,146,5,3
	.word	5994
	.byte	21
	.byte	'Ifx_P_OMSR0',0,4,154,5,3
	.word	4465
	.byte	21
	.byte	'Ifx_P_OMSR12',0,4,162,5,3
	.word	4981
	.byte	21
	.byte	'Ifx_P_OMSR4',0,4,170,5,3
	.word	4634
	.byte	21
	.byte	'Ifx_P_OMSR8',0,4,178,5,3
	.word	4806
	.byte	21
	.byte	'Ifx_P_OUT',0,4,186,5,3
	.word	808
	.byte	21
	.byte	'Ifx_P_PCSR',0,4,194,5,3
	.word	4318
	.byte	21
	.byte	'Ifx_P_PDISC',0,4,202,5,3
	.word	4032
	.byte	21
	.byte	'Ifx_P_PDR0',0,4,210,5,3
	.word	3001
	.byte	21
	.byte	'Ifx_P_PDR1',0,4,218,5,3
	.word	3317
	.byte	16
	.word	7677
	.byte	21
	.byte	'Ifx_P',0,4,139,6,3
	.word	44652
	.byte	17,10,250,10,9,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy16384',0,0,18
	.byte	'IfxScu_WDTCON1_IR_divBy256',0,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy64',0,2,0,21
	.byte	'IfxScu_WDTCON1_IR',0,10,255,10,3
	.word	44672
	.byte	10
	.byte	'_Ifx_SCU_ACCEN0_Bits',0,11,45,16,4,11
	.byte	'EN0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_ACCEN0_Bits',0,11,79,3
	.word	44794
	.byte	10
	.byte	'_Ifx_SCU_ACCEN1_Bits',0,11,82,16,4,11
	.byte	'reserved_0',0,4
	.word	470
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_SCU_ACCEN1_Bits',0,11,85,3
	.word	45351
	.byte	10
	.byte	'_Ifx_SCU_ARSTDIS_Bits',0,11,88,16,4,11
	.byte	'STM0DIS',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'STM1DIS',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'STM2DIS',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	470
	.byte	29,0,2,35,0,0,21
	.byte	'Ifx_SCU_ARSTDIS_Bits',0,11,94,3
	.word	45428
	.byte	10
	.byte	'_Ifx_SCU_CCUCON0_Bits',0,11,97,16,4,11
	.byte	'BAUD1DIV',0,1
	.word	493
	.byte	4,4,2,35,0,11
	.byte	'BAUD2DIV',0,1
	.word	493
	.byte	4,0,2,35,0,11
	.byte	'SRIDIV',0,1
	.word	493
	.byte	4,4,2,35,1,11
	.byte	'LPDIV',0,1
	.word	493
	.byte	4,0,2,35,1,11
	.byte	'SPBDIV',0,1
	.word	493
	.byte	4,4,2,35,2,11
	.byte	'FSI2DIV',0,1
	.word	493
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	493
	.byte	2,0,2,35,2,11
	.byte	'FSIDIV',0,1
	.word	493
	.byte	2,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	493
	.byte	2,4,2,35,3,11
	.byte	'CLKSEL',0,1
	.word	493
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON0_Bits',0,11,111,3
	.word	45564
	.byte	10
	.byte	'_Ifx_SCU_CCUCON1_Bits',0,11,114,16,4,11
	.byte	'CANDIV',0,1
	.word	493
	.byte	4,4,2,35,0,11
	.byte	'ERAYDIV',0,1
	.word	493
	.byte	4,0,2,35,0,11
	.byte	'STMDIV',0,1
	.word	493
	.byte	4,4,2,35,1,11
	.byte	'GTMDIV',0,1
	.word	493
	.byte	4,0,2,35,1,11
	.byte	'ETHDIV',0,1
	.word	493
	.byte	4,4,2,35,2,11
	.byte	'ASCLINFDIV',0,1
	.word	493
	.byte	4,0,2,35,2,11
	.byte	'ASCLINSDIV',0,1
	.word	493
	.byte	4,4,2,35,3,11
	.byte	'INSEL',0,1
	.word	493
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON1_Bits',0,11,126,3
	.word	45844
	.byte	10
	.byte	'_Ifx_SCU_CCUCON2_Bits',0,11,129,1,16,4,11
	.byte	'BBBDIV',0,1
	.word	493
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	470
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON2_Bits',0,11,135,1,3
	.word	46082
	.byte	10
	.byte	'_Ifx_SCU_CCUCON3_Bits',0,11,138,1,16,4,11
	.byte	'PLLDIV',0,1
	.word	493
	.byte	6,2,2,35,0,11
	.byte	'PLLSEL',0,1
	.word	493
	.byte	2,0,2,35,0,11
	.byte	'PLLERAYDIV',0,1
	.word	493
	.byte	6,2,2,35,1,11
	.byte	'PLLERAYSEL',0,1
	.word	493
	.byte	2,0,2,35,1,11
	.byte	'SRIDIV',0,1
	.word	493
	.byte	6,2,2,35,2,11
	.byte	'SRISEL',0,1
	.word	493
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON3_Bits',0,11,150,1,3
	.word	46210
	.byte	10
	.byte	'_Ifx_SCU_CCUCON4_Bits',0,11,153,1,16,4,11
	.byte	'SPBDIV',0,1
	.word	493
	.byte	6,2,2,35,0,11
	.byte	'SPBSEL',0,1
	.word	493
	.byte	2,0,2,35,0,11
	.byte	'GTMDIV',0,1
	.word	493
	.byte	6,2,2,35,1,11
	.byte	'GTMSEL',0,1
	.word	493
	.byte	2,0,2,35,1,11
	.byte	'STMDIV',0,1
	.word	493
	.byte	6,2,2,35,2,11
	.byte	'STMSEL',0,1
	.word	493
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON4_Bits',0,11,165,1,3
	.word	46453
	.byte	10
	.byte	'_Ifx_SCU_CCUCON5_Bits',0,11,168,1,16,4,11
	.byte	'MAXDIV',0,1
	.word	493
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	470
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON5_Bits',0,11,174,1,3
	.word	46688
	.byte	10
	.byte	'_Ifx_SCU_CCUCON6_Bits',0,11,177,1,16,4,11
	.byte	'CPU0DIV',0,1
	.word	493
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	470
	.byte	26,0,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON6_Bits',0,11,181,1,3
	.word	46816
	.byte	10
	.byte	'_Ifx_SCU_CCUCON7_Bits',0,11,184,1,16,4,11
	.byte	'CPU1DIV',0,1
	.word	493
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	470
	.byte	26,0,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON7_Bits',0,11,188,1,3
	.word	46916
	.byte	10
	.byte	'_Ifx_SCU_CHIPID_Bits',0,11,191,1,16,4,11
	.byte	'CHREV',0,1
	.word	493
	.byte	6,2,2,35,0,11
	.byte	'CHTEC',0,1
	.word	493
	.byte	2,0,2,35,0,11
	.byte	'CHID',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'EEA',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'UCODE',0,1
	.word	493
	.byte	7,0,2,35,2,11
	.byte	'FSIZE',0,1
	.word	493
	.byte	4,4,2,35,3,11
	.byte	'SP',0,1
	.word	493
	.byte	2,2,2,35,3,11
	.byte	'SEC',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CHIPID_Bits',0,11,202,1,3
	.word	47016
	.byte	10
	.byte	'_Ifx_SCU_DTSCON_Bits',0,11,205,1,16,4,11
	.byte	'PWD',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'START',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	493
	.byte	2,4,2,35,0,11
	.byte	'CAL',0,4
	.word	470
	.byte	20,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	7,1,2,35,3,11
	.byte	'SLCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_DTSCON_Bits',0,11,213,1,3
	.word	47224
	.byte	10
	.byte	'_Ifx_SCU_DTSLIM_Bits',0,11,216,1,16,4,11
	.byte	'LOWER',0,2
	.word	510
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	493
	.byte	5,1,2,35,1,11
	.byte	'LLU',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'UPPER',0,2
	.word	510
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	493
	.byte	4,2,2,35,3,11
	.byte	'SLCK',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'UOF',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_DTSLIM_Bits',0,11,225,1,3
	.word	47389
	.byte	10
	.byte	'_Ifx_SCU_DTSSTAT_Bits',0,11,228,1,16,4,11
	.byte	'RESULT',0,2
	.word	510
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	493
	.byte	4,2,2,35,1,11
	.byte	'RDY',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'BUSY',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_DTSSTAT_Bits',0,11,235,1,3
	.word	47572
	.byte	10
	.byte	'_Ifx_SCU_EICR_Bits',0,11,238,1,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	4,4,2,35,0,11
	.byte	'EXIS0',0,1
	.word	493
	.byte	3,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'FEN0',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'REN0',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'LDEN0',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'EIEN0',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'INP0',0,1
	.word	493
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	470
	.byte	5,12,2,35,0,11
	.byte	'EXIS1',0,1
	.word	493
	.byte	3,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'FEN1',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'REN1',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'LDEN1',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'EIEN1',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'INP1',0,1
	.word	493
	.byte	3,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EICR_Bits',0,11,129,2,3
	.word	47726
	.byte	10
	.byte	'_Ifx_SCU_EIFR_Bits',0,11,132,2,16,4,11
	.byte	'INTF0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'INTF1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'INTF2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'INTF3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'INTF4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'INTF5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'INTF6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'INTF7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	470
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_SCU_EIFR_Bits',0,11,143,2,3
	.word	48090
	.byte	10
	.byte	'_Ifx_SCU_EMSR_Bits',0,11,146,2,16,4,11
	.byte	'POL',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'MODE',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'ENON',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'PSEL',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	510
	.byte	12,0,2,35,0,11
	.byte	'EMSF',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'SEMSF',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	493
	.byte	6,0,2,35,2,11
	.byte	'EMSFM',0,1
	.word	493
	.byte	2,6,2,35,3,11
	.byte	'SEMSFM',0,1
	.word	493
	.byte	2,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	493
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_EMSR_Bits',0,11,159,2,3
	.word	48301
	.byte	10
	.byte	'_Ifx_SCU_ESRCFG_Bits',0,11,162,2,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	7,1,2,35,0,11
	.byte	'EDCON',0,2
	.word	510
	.byte	2,7,2,35,0,11
	.byte	'reserved_9',0,4
	.word	470
	.byte	23,0,2,35,0,0,21
	.byte	'Ifx_SCU_ESRCFG_Bits',0,11,167,2,3
	.word	48553
	.byte	10
	.byte	'_Ifx_SCU_ESROCFG_Bits',0,11,170,2,16,4,11
	.byte	'ARI',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'ARC',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	470
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_ESROCFG_Bits',0,11,175,2,3
	.word	48671
	.byte	10
	.byte	'_Ifx_SCU_EVR13CON_Bits',0,11,178,2,16,4,11
	.byte	'reserved_0',0,4
	.word	470
	.byte	28,4,2,35,0,11
	.byte	'EVR13OFF',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'BPEVR13OFF',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVR13CON_Bits',0,11,185,2,3
	.word	48782
	.byte	10
	.byte	'_Ifx_SCU_EVR33CON_Bits',0,11,188,2,16,4,11
	.byte	'reserved_0',0,4
	.word	470
	.byte	28,4,2,35,0,11
	.byte	'EVR33OFF',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'BPEVR33OFF',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVR33CON_Bits',0,11,195,2,3
	.word	48945
	.byte	10
	.byte	'_Ifx_SCU_EVRADCSTAT_Bits',0,11,198,2,16,4,11
	.byte	'ADC13V',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'ADC33V',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'ADCSWDV',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRADCSTAT_Bits',0,11,205,2,3
	.word	49108
	.byte	10
	.byte	'_Ifx_SCU_EVRDVSTAT_Bits',0,11,208,2,16,4,11
	.byte	'DVS13TRIM',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'DVS33TRIM',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRDVSTAT_Bits',0,11,215,2,3
	.word	49266
	.byte	10
	.byte	'_Ifx_SCU_EVRMONCTRL_Bits',0,11,218,2,16,4,11
	.byte	'EVR13OVMOD',0,1
	.word	493
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	493
	.byte	2,4,2,35,0,11
	.byte	'EVR13UVMOD',0,1
	.word	493
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	493
	.byte	2,0,2,35,0,11
	.byte	'EVR33OVMOD',0,1
	.word	493
	.byte	2,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	493
	.byte	2,4,2,35,1,11
	.byte	'EVR33UVMOD',0,1
	.word	493
	.byte	2,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	493
	.byte	2,0,2,35,1,11
	.byte	'SWDOVMOD',0,1
	.word	493
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	493
	.byte	2,4,2,35,2,11
	.byte	'SWDUVMOD',0,1
	.word	493
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,2
	.word	510
	.byte	10,0,2,35,2,0,21
	.byte	'Ifx_SCU_EVRMONCTRL_Bits',0,11,232,2,3
	.word	49431
	.byte	10
	.byte	'_Ifx_SCU_EVROSCCTRL_Bits',0,11,235,2,16,4,11
	.byte	'OSCTRIM',0,2
	.word	510
	.byte	10,6,2,35,0,11
	.byte	'OSCPTAT',0,1
	.word	493
	.byte	6,0,2,35,1,11
	.byte	'OSCANASEL',0,1
	.word	493
	.byte	4,4,2,35,2,11
	.byte	'HPBGTRIM',0,2
	.word	510
	.byte	7,5,2,35,2,11
	.byte	'HPBGCLKEN',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'OSC3V3',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	493
	.byte	2,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVROSCCTRL_Bits',0,11,245,2,3
	.word	49760
	.byte	10
	.byte	'_Ifx_SCU_EVROVMON_Bits',0,11,248,2,16,4,11
	.byte	'EVR13OVVAL',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'EVR33OVVAL',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'SWDOVVAL',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVROVMON_Bits',0,11,255,2,3
	.word	49981
	.byte	10
	.byte	'_Ifx_SCU_EVRRSTCON_Bits',0,11,130,3,16,4,11
	.byte	'RST13TRIM',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	470
	.byte	16,8,2,35,0,11
	.byte	'RST13OFF',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'BPRST13OFF',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'RST33OFF',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'BPRST33OFF',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'RSTSWDOFF',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'BPRSTSWDOFF',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRRSTCON_Bits',0,11,142,3,3
	.word	50144
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF1_Bits',0,11,145,3,16,4,11
	.byte	'SD5P',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'SD5I',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'SD5D',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF1_Bits',0,11,152,3,3
	.word	50416
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF2_Bits',0,11,155,3,16,4,11
	.byte	'SD33P',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'SD33I',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'SD33D',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF2_Bits',0,11,162,3,3
	.word	50569
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF3_Bits',0,11,165,3,16,4,11
	.byte	'CT5REG0',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'CT5REG1',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'CT5REG2',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF3_Bits',0,11,172,3,3
	.word	50725
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF4_Bits',0,11,175,3,16,4,11
	.byte	'CT5REG3',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'CT5REG4',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF4_Bits',0,11,181,3,3
	.word	50887
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF5_Bits',0,11,184,3,16,4,11
	.byte	'CT33REG0',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'CT33REG1',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'CT33REG2',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF5_Bits',0,11,191,3,3
	.word	51030
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF6_Bits',0,11,194,3,16,4,11
	.byte	'CT33REG3',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'CT33REG4',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF6_Bits',0,11,200,3,3
	.word	51195
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL1_Bits',0,11,203,3,16,4,11
	.byte	'SDFREQSPRD',0,2
	.word	510
	.byte	16,0,2,35,0,11
	.byte	'SDFREQ',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'SDSTEP',0,1
	.word	493
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	493
	.byte	2,2,2,35,3,11
	.byte	'SDSAMPLE',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCTRL1_Bits',0,11,211,3,3
	.word	51340
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL2_Bits',0,11,214,3,16,4,11
	.byte	'DRVP',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'SDMINMAXDC',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'DRVN',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'SDLUT',0,1
	.word	493
	.byte	6,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCTRL2_Bits',0,11,222,3,3
	.word	51521
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL3_Bits',0,11,225,3,16,4,11
	.byte	'SDPWMPRE',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'SDPID',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'SDVOKLVL',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCTRL3_Bits',0,11,232,3,3
	.word	51695
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL4_Bits',0,11,235,3,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'SYNCDIV',0,1
	.word	493
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	470
	.byte	20,1,2,35,0,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCTRL4_Bits',0,11,241,3,3
	.word	51855
	.byte	10
	.byte	'_Ifx_SCU_EVRSTAT_Bits',0,11,244,3,16,4,11
	.byte	'EVR13',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'OV13',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'EVR33',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'OV33',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'OVSWD',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'UV13',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'UV33',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'UVSWD',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'EXTPASS13',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'EXTPASS33',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'BGPROK',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	470
	.byte	21,0,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSTAT_Bits',0,11,130,4,3
	.word	51999
	.byte	10
	.byte	'_Ifx_SCU_EVRTRIM_Bits',0,11,133,4,16,4,11
	.byte	'EVR13TRIM',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'SDVOUTSEL',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRTRIM_Bits',0,11,139,4,3
	.word	52273
	.byte	10
	.byte	'_Ifx_SCU_EVRUVMON_Bits',0,11,142,4,16,4,11
	.byte	'EVR13UVVAL',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'EVR33UVVAL',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'SWDUVVAL',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRUVMON_Bits',0,11,149,4,3
	.word	52412
	.byte	10
	.byte	'_Ifx_SCU_EXTCON_Bits',0,11,152,4,16,4,11
	.byte	'EN0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'SEL0',0,1
	.word	493
	.byte	4,2,2,35,0,11
	.byte	'reserved_6',0,2
	.word	510
	.byte	10,0,2,35,0,11
	.byte	'EN1',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'NSEL',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'SEL1',0,1
	.word	493
	.byte	4,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	493
	.byte	2,0,2,35,2,11
	.byte	'DIV1',0,1
	.word	493
	.byte	8,0,2,35,3,0,21
	.byte	'Ifx_SCU_EXTCON_Bits',0,11,163,4,3
	.word	52575
	.byte	10
	.byte	'_Ifx_SCU_FDR_Bits',0,11,166,4,16,4,11
	.byte	'STEP',0,2
	.word	510
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	493
	.byte	4,2,2,35,1,11
	.byte	'DM',0,1
	.word	493
	.byte	2,0,2,35,1,11
	.byte	'RESULT',0,2
	.word	510
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	493
	.byte	5,1,2,35,3,11
	.byte	'DISCLK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_FDR_Bits',0,11,174,4,3
	.word	52793
	.byte	10
	.byte	'_Ifx_SCU_FMR_Bits',0,11,177,4,16,4,11
	.byte	'FS0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'FS1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'FS2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'FS3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'FS4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'FS5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'FS6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'FS7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'FC0',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'FC1',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'FC2',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'FC3',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'FC4',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'FC5',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'FC6',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'FC7',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	8,0,2,35,3,0,21
	.byte	'Ifx_SCU_FMR_Bits',0,11,197,4,3
	.word	52956
	.byte	10
	.byte	'_Ifx_SCU_ID_Bits',0,11,200,4,16,4,11
	.byte	'MODREV',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_ID_Bits',0,11,205,4,3
	.word	53292
	.byte	10
	.byte	'_Ifx_SCU_IGCR_Bits',0,11,208,4,16,4,11
	.byte	'IPEN00',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'IPEN01',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'IPEN02',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'IPEN03',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'IPEN04',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'IPEN05',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'IPEN06',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'IPEN07',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	493
	.byte	5,3,2,35,1,11
	.byte	'GEEN0',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'IGP0',0,1
	.word	493
	.byte	2,0,2,35,1,11
	.byte	'IPEN10',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'IPEN11',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'IPEN12',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'IPEN13',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'IPEN14',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'IPEN15',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'IPEN16',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'IPEN17',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	5,3,2,35,3,11
	.byte	'GEEN1',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'IGP1',0,1
	.word	493
	.byte	2,0,2,35,3,0,21
	.byte	'Ifx_SCU_IGCR_Bits',0,11,232,4,3
	.word	53399
	.byte	10
	.byte	'_Ifx_SCU_IN_Bits',0,11,235,4,16,4,11
	.byte	'P0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	470
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_IN_Bits',0,11,240,4,3
	.word	53851
	.byte	10
	.byte	'_Ifx_SCU_IOCR_Bits',0,11,243,4,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	4,4,2,35,0,11
	.byte	'PC0',0,1
	.word	493
	.byte	4,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	493
	.byte	4,4,2,35,1,11
	.byte	'PC1',0,1
	.word	493
	.byte	4,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_IOCR_Bits',0,11,250,4,3
	.word	53950
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL0_Bits',0,11,253,4,16,4,11
	.byte	'LBISTREQ',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'LBISTREQP',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'PATTERNS',0,2
	.word	510
	.byte	14,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_LBISTCTRL0_Bits',0,11,131,5,3
	.word	54100
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL1_Bits',0,11,134,5,16,4,11
	.byte	'SEED',0,4
	.word	470
	.byte	23,9,2,35,0,11
	.byte	'reserved_23',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'SPLITSH',0,1
	.word	493
	.byte	3,5,2,35,3,11
	.byte	'BODY',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'LBISTFREQU',0,1
	.word	493
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_LBISTCTRL1_Bits',0,11,141,5,3
	.word	54249
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL2_Bits',0,11,144,5,16,4,11
	.byte	'SIGNATURE',0,4
	.word	470
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	7,1,2,35,3,11
	.byte	'LBISTDONE',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_LBISTCTRL2_Bits',0,11,149,5,3
	.word	54410
	.byte	10
	.byte	'_Ifx_SCU_LCLCON_Bits',0,11,152,5,16,4,11
	.byte	'reserved_0',0,2
	.word	510
	.byte	16,0,2,35,0,11
	.byte	'LS',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,2
	.word	510
	.byte	14,1,2,35,2,11
	.byte	'LSEN',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_LCLCON_Bits',0,11,158,5,3
	.word	54540
	.byte	10
	.byte	'_Ifx_SCU_LCLTEST_Bits',0,11,161,5,16,4,11
	.byte	'LCLT0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'LCLT1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	470
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_LCLTEST_Bits',0,11,166,5,3
	.word	54672
	.byte	10
	.byte	'_Ifx_SCU_MANID_Bits',0,11,169,5,16,4,11
	.byte	'DEPT',0,1
	.word	493
	.byte	5,3,2,35,0,11
	.byte	'MANUF',0,2
	.word	510
	.byte	11,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_MANID_Bits',0,11,174,5,3
	.word	54787
	.byte	10
	.byte	'_Ifx_SCU_OMR_Bits',0,11,177,5,16,4,11
	.byte	'PS0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	510
	.byte	14,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	510
	.byte	14,0,2,35,2,0,21
	.byte	'Ifx_SCU_OMR_Bits',0,11,185,5,3
	.word	54898
	.byte	10
	.byte	'_Ifx_SCU_OSCCON_Bits',0,11,188,5,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'PLLLV',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'OSCRES',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'GAINSEL',0,1
	.word	493
	.byte	2,3,2,35,0,11
	.byte	'MODE',0,1
	.word	493
	.byte	2,1,2,35,0,11
	.byte	'SHBY',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'PLLHV',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'X1D',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'X1DEN',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	493
	.byte	4,0,2,35,1,11
	.byte	'OSCVAL',0,1
	.word	493
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	493
	.byte	2,1,2,35,2,11
	.byte	'APREN',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'CAP0EN',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'CAP1EN',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'CAP2EN',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'CAP3EN',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	493
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_OSCCON_Bits',0,11,209,5,3
	.word	55056
	.byte	10
	.byte	'_Ifx_SCU_OUT_Bits',0,11,212,5,16,4,11
	.byte	'P0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	470
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_OUT_Bits',0,11,217,5,3
	.word	55468
	.byte	10
	.byte	'_Ifx_SCU_OVCCON_Bits',0,11,220,5,16,4,11
	.byte	'CSEL0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'CSEL1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'CSEL2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,2
	.word	510
	.byte	13,0,2,35,0,11
	.byte	'OVSTRT',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'OVSTP',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'DCINVAL',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	493
	.byte	5,0,2,35,2,11
	.byte	'OVCONF',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'POVCONF',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	493
	.byte	6,0,2,35,3,0,21
	.byte	'Ifx_SCU_OVCCON_Bits',0,11,233,5,3
	.word	55569
	.byte	10
	.byte	'_Ifx_SCU_OVCENABLE_Bits',0,11,236,5,16,4,11
	.byte	'OVEN0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'OVEN1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'OVEN2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	470
	.byte	29,0,2,35,0,0,21
	.byte	'Ifx_SCU_OVCENABLE_Bits',0,11,242,5,3
	.word	55836
	.byte	10
	.byte	'_Ifx_SCU_PDISC_Bits',0,11,245,5,16,4,11
	.byte	'PDIS0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	470
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_PDISC_Bits',0,11,250,5,3
	.word	55972
	.byte	10
	.byte	'_Ifx_SCU_PDR_Bits',0,11,253,5,16,4,11
	.byte	'PD0',0,1
	.word	493
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	493
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	470
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_SCU_PDR_Bits',0,11,132,6,3
	.word	56083
	.byte	10
	.byte	'_Ifx_SCU_PDRR_Bits',0,11,135,6,16,4,11
	.byte	'PDR0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'PDR1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'PDR2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'PDR3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'PDR4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'PDR5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'PDR6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'PDR7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	470
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_SCU_PDRR_Bits',0,11,146,6,3
	.word	56216
	.byte	10
	.byte	'_Ifx_SCU_PLLCON0_Bits',0,11,149,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'MODEN',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	510
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	493
	.byte	7,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	493
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	493
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	493
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_PLLCON0_Bits',0,11,166,6,3
	.word	56419
	.byte	10
	.byte	'_Ifx_SCU_PLLCON1_Bits',0,11,169,6,16,4,11
	.byte	'K2DIV',0,1
	.word	493
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	493
	.byte	7,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	493
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	510
	.byte	9,0,2,35,2,0,21
	.byte	'Ifx_SCU_PLLCON1_Bits',0,11,177,6,3
	.word	56775
	.byte	10
	.byte	'_Ifx_SCU_PLLCON2_Bits',0,11,180,6,16,4,11
	.byte	'MODCFG',0,2
	.word	510
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_PLLCON2_Bits',0,11,184,6,3
	.word	56953
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON0_Bits',0,11,187,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	493
	.byte	2,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	510
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	493
	.byte	5,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	493
	.byte	2,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	493
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	493
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	493
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_PLLERAYCON0_Bits',0,11,204,6,3
	.word	57053
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON1_Bits',0,11,207,6,16,4,11
	.byte	'K2DIV',0,1
	.word	493
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	493
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	493
	.byte	4,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	493
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	510
	.byte	9,0,2,35,2,0,21
	.byte	'Ifx_SCU_PLLERAYCON1_Bits',0,11,215,6,3
	.word	57423
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYSTAT_Bits',0,11,218,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'PWDSTAT',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	470
	.byte	26,0,2,35,0,0,21
	.byte	'Ifx_SCU_PLLERAYSTAT_Bits',0,11,227,6,3
	.word	57609
	.byte	10
	.byte	'_Ifx_SCU_PLLSTAT_Bits',0,11,230,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'MODRUN',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	470
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_SCU_PLLSTAT_Bits',0,11,241,6,3
	.word	57807
	.byte	10
	.byte	'_Ifx_SCU_PMCSR_Bits',0,11,244,6,16,4,11
	.byte	'REQSLP',0,1
	.word	493
	.byte	2,6,2,35,0,11
	.byte	'SMUSLP',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	493
	.byte	5,0,2,35,0,11
	.byte	'PMST',0,1
	.word	493
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	470
	.byte	21,0,2,35,0,0,21
	.byte	'Ifx_SCU_PMCSR_Bits',0,11,251,6,3
	.word	58040
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR0_Bits',0,11,254,6,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'ESR1WKEN',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'PINAWKEN',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'PINBWKEN',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'ESR0DFEN',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'ESR0EDCON',0,1
	.word	493
	.byte	2,1,2,35,0,11
	.byte	'ESR1DFEN',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'ESR1EDCON',0,1
	.word	493
	.byte	2,6,2,35,1,11
	.byte	'PINADFEN',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'PINAEDCON',0,1
	.word	493
	.byte	2,3,2,35,1,11
	.byte	'PINBDFEN',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'PINBEDCON',0,1
	.word	493
	.byte	2,0,2,35,1,11
	.byte	'SCREN',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'STBYRAMSEL',0,1
	.word	493
	.byte	2,5,2,35,2,11
	.byte	'SCRCLKSEL',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'TRISTEN',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'TRISTREQ',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'PORSTDF',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'DCDCSYNC',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	493
	.byte	3,3,2,35,3,11
	.byte	'ESR0TRIST',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_PMSWCR0_Bits',0,11,153,7,3
	.word	58192
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR1_Bits',0,11,156,7,16,4,11
	.byte	'SCRSTEN',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'SCRSTREQ',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	493
	.byte	6,0,2,35,0,11
	.byte	'CPUIDLSEL',0,1
	.word	493
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'IRADIS',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	493
	.byte	3,0,2,35,1,11
	.byte	'SCRCFG',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'CPUSEL',0,1
	.word	493
	.byte	3,5,2,35,3,11
	.byte	'STBYEVEN',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'STBYEV',0,1
	.word	493
	.byte	3,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_PMSWCR1_Bits',0,11,170,7,3
	.word	58759
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR2_Bits',0,11,173,7,16,4,11
	.byte	'SCRINT',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'BUSY',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'SCRECC',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'SCRWDT',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'SCRRST',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	493
	.byte	4,0,2,35,1,11
	.byte	'TCINT',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'TCINTREQ',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'SMURST',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'RST',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	493
	.byte	4,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_PMSWCR2_Bits',0,11,187,7,3
	.word	59053
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTAT_Bits',0,11,190,7,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKP',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUN',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'PINAWKP',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUN',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'PINBWKP',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUN',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'PORSTDF',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'HWCFGEVR',0,1
	.word	493
	.byte	3,3,2,35,1,11
	.byte	'STBYRAM',0,1
	.word	493
	.byte	2,1,2,35,1,11
	.byte	'TRIST',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'SCRST',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'SCRWKP',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'SCR',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'ESR1WKEN',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'PINAWKEN',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'PINBWKEN',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	510
	.byte	4,5,2,35,2,11
	.byte	'ESR0TRIST',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	493
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_PMSWSTAT_Bits',0,11,214,7,3
	.word	59331
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTATCLR_Bits',0,11,217,7,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKPCLR',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUNCLR',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'PINAWKPCLR',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUNCLR',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'PINBWKPCLR',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUNCLR',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'SCRSTCLR',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'SCRWKPCLR',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	510
	.byte	14,0,2,35,2,0,21
	.byte	'Ifx_SCU_PMSWSTATCLR_Bits',0,11,230,7,3
	.word	59827
	.byte	10
	.byte	'_Ifx_SCU_RSTCON2_Bits',0,11,233,7,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'CLRC',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	510
	.byte	10,4,2,35,0,11
	.byte	'CSS0',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'CSS1',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'CSS2',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'USRINFO',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_RSTCON2_Bits',0,11,243,7,3
	.word	60140
	.byte	10
	.byte	'_Ifx_SCU_RSTCON_Bits',0,11,246,7,16,4,11
	.byte	'ESR0',0,1
	.word	493
	.byte	2,6,2,35,0,11
	.byte	'ESR1',0,1
	.word	493
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	493
	.byte	2,2,2,35,0,11
	.byte	'SMU',0,1
	.word	493
	.byte	2,0,2,35,0,11
	.byte	'SW',0,1
	.word	493
	.byte	2,6,2,35,1,11
	.byte	'STM0',0,1
	.word	493
	.byte	2,4,2,35,1,11
	.byte	'STM1',0,1
	.word	493
	.byte	2,2,2,35,1,11
	.byte	'STM2',0,1
	.word	493
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_RSTCON_Bits',0,11,129,8,3
	.word	60349
	.byte	10
	.byte	'_Ifx_SCU_RSTSTAT_Bits',0,11,132,8,16,4,11
	.byte	'ESR0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'ESR1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'SMU',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'SW',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'STM0',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'STM1',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'STM2',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'PORST',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'CB0',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'CB1',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'CB3',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	493
	.byte	2,1,2,35,2,11
	.byte	'EVR13',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'EVR33',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'SWD',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	493
	.byte	2,4,2,35,3,11
	.byte	'STBYR',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	493
	.byte	3,0,2,35,3,0,21
	.byte	'Ifx_SCU_RSTSTAT_Bits',0,11,155,8,3
	.word	60560
	.byte	10
	.byte	'_Ifx_SCU_SAFECON_Bits',0,11,158,8,16,4,11
	.byte	'HBT',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	470
	.byte	31,0,2,35,0,0,21
	.byte	'Ifx_SCU_SAFECON_Bits',0,11,162,8,3
	.word	60992
	.byte	10
	.byte	'_Ifx_SCU_STSTAT_Bits',0,11,165,8,16,4,11
	.byte	'HWCFG',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'FTM',0,1
	.word	493
	.byte	7,1,2,35,1,11
	.byte	'MODE',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'FCBAE',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'LUDIS',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'TRSTL',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'SPDEN',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	493
	.byte	3,0,2,35,2,11
	.byte	'RAMINT',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	493
	.byte	7,0,2,35,3,0,21
	.byte	'Ifx_SCU_STSTAT_Bits',0,11,178,8,3
	.word	61088
	.byte	10
	.byte	'_Ifx_SCU_SWRSTCON_Bits',0,11,181,8,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'SWRSTREQ',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	470
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_SWRSTCON_Bits',0,11,186,8,3
	.word	61348
	.byte	10
	.byte	'_Ifx_SCU_SYSCON_Bits',0,11,189,8,16,4,11
	.byte	'CCTRIG0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'RAMINTM',0,1
	.word	493
	.byte	2,4,2,35,0,11
	.byte	'SETLUDIS',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	493
	.byte	3,0,2,35,0,11
	.byte	'DATM',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,4
	.word	470
	.byte	23,0,2,35,0,0,21
	.byte	'Ifx_SCU_SYSCON_Bits',0,11,198,8,3
	.word	61473
	.byte	10
	.byte	'_Ifx_SCU_TRAPCLR_Bits',0,11,201,8,16,4,11
	.byte	'ESR0T',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	470
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPCLR_Bits',0,11,208,8,3
	.word	61670
	.byte	10
	.byte	'_Ifx_SCU_TRAPDIS_Bits',0,11,211,8,16,4,11
	.byte	'ESR0T',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	470
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPDIS_Bits',0,11,218,8,3
	.word	61823
	.byte	10
	.byte	'_Ifx_SCU_TRAPSET_Bits',0,11,221,8,16,4,11
	.byte	'ESR0T',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	470
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPSET_Bits',0,11,228,8,3
	.word	61976
	.byte	10
	.byte	'_Ifx_SCU_TRAPSTAT_Bits',0,11,231,8,16,4,11
	.byte	'ESR0T',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	470
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPSTAT_Bits',0,11,238,8,3
	.word	62129
	.byte	7
	.byte	'unsigned int',0,4,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON0_Bits',0,11,241,8,16,4,11
	.byte	'ENDINIT',0,4
	.word	62284
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	62284
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	62284
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	62284
	.byte	16,0,2,35,0,0,21
	.byte	'Ifx_SCU_WDTCPU_CON0_Bits',0,11,247,8,3
	.word	62300
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU_CON1_Bits',0,11,250,8,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	2,6,2,35,0,11
	.byte	'IR0',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	493
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_WDTCPU_CON1_Bits',0,11,134,9,3
	.word	62430
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU_SR_Bits',0,11,137,9,16,4,11
	.byte	'AE',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	493
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_WDTCPU_SR_Bits',0,11,150,9,3
	.word	62668
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON0_Bits',0,11,153,9,16,4,11
	.byte	'ENDINIT',0,4
	.word	62284
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	62284
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	62284
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	62284
	.byte	16,0,2,35,0,0,21
	.byte	'Ifx_SCU_WDTS_CON0_Bits',0,11,159,9,3
	.word	62891
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON1_Bits',0,11,162,9,16,4,11
	.byte	'CLRIRF',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'IR0',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	493
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_WDTS_CON1_Bits',0,11,175,9,3
	.word	63017
	.byte	10
	.byte	'_Ifx_SCU_WDTS_SR_Bits',0,11,178,9,16,4,11
	.byte	'AE',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	493
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_WDTS_SR_Bits',0,11,191,9,3
	.word	63269
	.byte	12,11,199,9,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44794
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ACCEN0',0,11,204,9,3
	.word	63488
	.byte	12,11,207,9,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45351
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ACCEN1',0,11,212,9,3
	.word	63552
	.byte	12,11,215,9,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45428
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ARSTDIS',0,11,220,9,3
	.word	63616
	.byte	12,11,223,9,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45564
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON0',0,11,228,9,3
	.word	63681
	.byte	12,11,231,9,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45844
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON1',0,11,236,9,3
	.word	63746
	.byte	12,11,239,9,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46082
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON2',0,11,244,9,3
	.word	63811
	.byte	12,11,247,9,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46210
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON3',0,11,252,9,3
	.word	63876
	.byte	12,11,255,9,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46453
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON4',0,11,132,10,3
	.word	63941
	.byte	12,11,135,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46688
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON5',0,11,140,10,3
	.word	64006
	.byte	12,11,143,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46816
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON6',0,11,148,10,3
	.word	64071
	.byte	12,11,151,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46916
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON7',0,11,156,10,3
	.word	64136
	.byte	12,11,159,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47016
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CHIPID',0,11,164,10,3
	.word	64201
	.byte	12,11,167,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47224
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_DTSCON',0,11,172,10,3
	.word	64265
	.byte	12,11,175,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47389
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_DTSLIM',0,11,180,10,3
	.word	64329
	.byte	12,11,183,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47572
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_DTSSTAT',0,11,188,10,3
	.word	64393
	.byte	12,11,191,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47726
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EICR',0,11,196,10,3
	.word	64458
	.byte	12,11,199,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48090
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EIFR',0,11,204,10,3
	.word	64520
	.byte	12,11,207,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48301
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EMSR',0,11,212,10,3
	.word	64582
	.byte	12,11,215,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48553
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ESRCFG',0,11,220,10,3
	.word	64644
	.byte	12,11,223,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48671
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ESROCFG',0,11,228,10,3
	.word	64708
	.byte	12,11,231,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48782
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVR13CON',0,11,236,10,3
	.word	64773
	.byte	12,11,239,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48945
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVR33CON',0,11,244,10,3
	.word	64839
	.byte	12,11,247,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49108
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRADCSTAT',0,11,252,10,3
	.word	64905
	.byte	12,11,255,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49266
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRDVSTAT',0,11,132,11,3
	.word	64973
	.byte	12,11,135,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49431
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRMONCTRL',0,11,140,11,3
	.word	65040
	.byte	12,11,143,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49760
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVROSCCTRL',0,11,148,11,3
	.word	65108
	.byte	12,11,151,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49981
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVROVMON',0,11,156,11,3
	.word	65176
	.byte	12,11,159,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50144
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRRSTCON',0,11,164,11,3
	.word	65242
	.byte	12,11,167,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50416
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF1',0,11,172,11,3
	.word	65309
	.byte	12,11,175,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50569
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF2',0,11,180,11,3
	.word	65378
	.byte	12,11,183,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50725
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF3',0,11,188,11,3
	.word	65447
	.byte	12,11,191,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50887
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF4',0,11,196,11,3
	.word	65516
	.byte	12,11,199,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51030
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF5',0,11,204,11,3
	.word	65585
	.byte	12,11,207,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51195
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF6',0,11,212,11,3
	.word	65654
	.byte	12,11,215,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51340
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCTRL1',0,11,220,11,3
	.word	65723
	.byte	12,11,223,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51521
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCTRL2',0,11,228,11,3
	.word	65791
	.byte	12,11,231,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51695
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCTRL3',0,11,236,11,3
	.word	65859
	.byte	12,11,239,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51855
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCTRL4',0,11,244,11,3
	.word	65927
	.byte	12,11,247,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51999
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSTAT',0,11,252,11,3
	.word	65995
	.byte	12,11,255,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52273
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRTRIM',0,11,132,12,3
	.word	66060
	.byte	12,11,135,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52412
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRUVMON',0,11,140,12,3
	.word	66125
	.byte	12,11,143,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52575
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EXTCON',0,11,148,12,3
	.word	66191
	.byte	12,11,151,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52793
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_FDR',0,11,156,12,3
	.word	66255
	.byte	12,11,159,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52956
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_FMR',0,11,164,12,3
	.word	66316
	.byte	12,11,167,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53292
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ID',0,11,172,12,3
	.word	66377
	.byte	12,11,175,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53399
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_IGCR',0,11,180,12,3
	.word	66437
	.byte	12,11,183,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53851
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_IN',0,11,188,12,3
	.word	66499
	.byte	12,11,191,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53950
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_IOCR',0,11,196,12,3
	.word	66559
	.byte	12,11,199,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54100
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_LBISTCTRL0',0,11,204,12,3
	.word	66621
	.byte	12,11,207,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54249
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_LBISTCTRL1',0,11,212,12,3
	.word	66689
	.byte	12,11,215,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54410
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_LBISTCTRL2',0,11,220,12,3
	.word	66757
	.byte	12,11,223,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54540
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_LCLCON',0,11,228,12,3
	.word	66825
	.byte	12,11,231,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54672
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_LCLTEST',0,11,236,12,3
	.word	66889
	.byte	12,11,239,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54787
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_MANID',0,11,244,12,3
	.word	66954
	.byte	12,11,247,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54898
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_OMR',0,11,252,12,3
	.word	67017
	.byte	12,11,255,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55056
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_OSCCON',0,11,132,13,3
	.word	67078
	.byte	12,11,135,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55468
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_OUT',0,11,140,13,3
	.word	67142
	.byte	12,11,143,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55569
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_OVCCON',0,11,148,13,3
	.word	67203
	.byte	12,11,151,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55836
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_OVCENABLE',0,11,156,13,3
	.word	67267
	.byte	12,11,159,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55972
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PDISC',0,11,164,13,3
	.word	67334
	.byte	12,11,167,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56083
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PDR',0,11,172,13,3
	.word	67397
	.byte	12,11,175,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56216
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PDRR',0,11,180,13,3
	.word	67458
	.byte	12,11,183,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56419
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLCON0',0,11,188,13,3
	.word	67520
	.byte	12,11,191,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56775
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLCON1',0,11,196,13,3
	.word	67585
	.byte	12,11,199,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56953
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLCON2',0,11,204,13,3
	.word	67650
	.byte	12,11,207,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57053
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLERAYCON0',0,11,212,13,3
	.word	67715
	.byte	12,11,215,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57423
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLERAYCON1',0,11,220,13,3
	.word	67784
	.byte	12,11,223,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57609
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLERAYSTAT',0,11,228,13,3
	.word	67853
	.byte	12,11,231,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57807
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLSTAT',0,11,236,13,3
	.word	67922
	.byte	12,11,239,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58040
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMCSR',0,11,244,13,3
	.word	67987
	.byte	12,11,247,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58192
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMSWCR0',0,11,252,13,3
	.word	68050
	.byte	12,11,255,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58759
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMSWCR1',0,11,132,14,3
	.word	68115
	.byte	12,11,135,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59053
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMSWCR2',0,11,140,14,3
	.word	68180
	.byte	12,11,143,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59331
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMSWSTAT',0,11,148,14,3
	.word	68245
	.byte	12,11,151,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59827
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMSWSTATCLR',0,11,156,14,3
	.word	68311
	.byte	12,11,159,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60349
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_RSTCON',0,11,164,14,3
	.word	68380
	.byte	12,11,167,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60140
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_RSTCON2',0,11,172,14,3
	.word	68444
	.byte	12,11,175,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60560
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_RSTSTAT',0,11,180,14,3
	.word	68509
	.byte	12,11,183,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60992
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_SAFECON',0,11,188,14,3
	.word	68574
	.byte	12,11,191,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61088
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_STSTAT',0,11,196,14,3
	.word	68639
	.byte	12,11,199,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61348
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_SWRSTCON',0,11,204,14,3
	.word	68703
	.byte	12,11,207,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61473
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_SYSCON',0,11,212,14,3
	.word	68769
	.byte	12,11,215,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61670
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPCLR',0,11,220,14,3
	.word	68833
	.byte	12,11,223,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61823
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPDIS',0,11,228,14,3
	.word	68898
	.byte	12,11,231,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61976
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPSET',0,11,236,14,3
	.word	68963
	.byte	12,11,239,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62129
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPSTAT',0,11,244,14,3
	.word	69028
	.byte	12,11,247,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62300
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTCPU_CON0',0,11,252,14,3
	.word	69094
	.byte	12,11,255,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62430
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTCPU_CON1',0,11,132,15,3
	.word	69163
	.byte	12,11,135,15,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62668
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTCPU_SR',0,11,140,15,3
	.word	69232
	.byte	12,11,143,15,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62891
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTS_CON0',0,11,148,15,3
	.word	69299
	.byte	12,11,151,15,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63017
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTS_CON1',0,11,156,15,3
	.word	69366
	.byte	12,11,159,15,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63269
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTS_SR',0,11,164,15,3
	.word	69433
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU',0,11,175,15,25,12,13
	.byte	'CON0',0
	.word	69094
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	69163
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	69232
	.byte	4,2,35,8,0,16
	.word	69498
	.byte	21
	.byte	'Ifx_SCU_WDTCPU',0,11,180,15,3
	.word	69561
	.byte	10
	.byte	'_Ifx_SCU_WDTS',0,11,183,15,25,12,13
	.byte	'CON0',0
	.word	69299
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	69366
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	69433
	.byte	4,2,35,8,0,16
	.word	69590
	.byte	21
	.byte	'Ifx_SCU_WDTS',0,11,188,15,3
	.word	69651
	.byte	17,3,83,9,1,18
	.byte	'IfxPort_InputMode_undefined',0,127,18
	.byte	'IfxPort_InputMode_noPullDevice',0,0,18
	.byte	'IfxPort_InputMode_pullDown',0,8,18
	.byte	'IfxPort_InputMode_pullUp',0,16,0,21
	.byte	'IfxPort_InputMode',0,3,89,3
	.word	69678
	.byte	17,3,120,9,1,18
	.byte	'IfxPort_OutputIdx_general',0,128,1,18
	.byte	'IfxPort_OutputIdx_alt1',0,136,1,18
	.byte	'IfxPort_OutputIdx_alt2',0,144,1,18
	.byte	'IfxPort_OutputIdx_alt3',0,152,1,18
	.byte	'IfxPort_OutputIdx_alt4',0,160,1,18
	.byte	'IfxPort_OutputIdx_alt5',0,168,1,18
	.byte	'IfxPort_OutputIdx_alt6',0,176,1,18
	.byte	'IfxPort_OutputIdx_alt7',0,184,1,0,21
	.byte	'IfxPort_OutputIdx',0,3,130,1,3
	.word	69829
	.byte	17,3,134,1,9,1,18
	.byte	'IfxPort_OutputMode_pushPull',0,128,1,18
	.byte	'IfxPort_OutputMode_openDrain',0,192,1,0,21
	.byte	'IfxPort_OutputMode',0,3,138,1,3
	.word	70073
	.byte	17,3,144,1,9,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed1',0,0,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed2',0,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed3',0,2,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed4',0,3,18
	.byte	'IfxPort_PadDriver_lvdsSpeed1',0,4,18
	.byte	'IfxPort_PadDriver_lvdsSpeed2',0,5,18
	.byte	'IfxPort_PadDriver_lvdsSpeed3',0,6,18
	.byte	'IfxPort_PadDriver_lvdsSpeed4',0,7,18
	.byte	'IfxPort_PadDriver_ttlSpeed1',0,8,18
	.byte	'IfxPort_PadDriver_ttlSpeed2',0,9,18
	.byte	'IfxPort_PadDriver_ttlSpeed3',0,10,18
	.byte	'IfxPort_PadDriver_ttlSpeed4',0,11,0,21
	.byte	'IfxPort_PadDriver',0,3,158,1,3
	.word	70171
	.byte	21
	.byte	'IfxPort_State',0,3,178,1,3
	.word	8290
	.byte	23,3,190,1,9,8,13
	.byte	'port',0
	.word	8285
	.byte	4,2,35,0,13
	.byte	'pinIndex',0
	.word	493
	.byte	1,2,35,4,0,21
	.byte	'IfxPort_Pin',0,3,194,1,3
	.word	70636
	.byte	16
	.word	41048
	.byte	3
	.word	70696
	.byte	23,12,59,15,20,13
	.byte	'module',0
	.word	70701
	.byte	4,2,35,0,13
	.byte	'channelId',0
	.word	42849
	.byte	1,2,35,4,13
	.byte	'pin',0
	.word	70636
	.byte	8,2,35,8,13
	.byte	'select',0
	.word	43196
	.byte	1,2,35,16,0,24
	.word	70706
	.byte	21
	.byte	'IfxPsi5_Rx_In',0,12,65,3
	.word	70776
	.byte	23,12,68,15,20,13
	.byte	'module',0
	.word	70701
	.byte	4,2,35,0,13
	.byte	'channelId',0
	.word	42849
	.byte	1,2,35,4,13
	.byte	'pin',0
	.word	70636
	.byte	8,2,35,8,13
	.byte	'select',0
	.word	69829
	.byte	1,2,35,16,0,24
	.word	70803
	.byte	21
	.byte	'IfxPsi5_Tx_Out',0,12,74,3
	.word	70873
.L46:
	.byte	24
	.word	70706
.L47:
	.byte	24
	.word	70706
.L48:
	.byte	24
	.word	70706
.L49:
	.byte	24
	.word	70706
.L50:
	.byte	24
	.word	70706
.L51:
	.byte	24
	.word	70706
.L52:
	.byte	24
	.word	70706
.L53:
	.byte	24
	.word	70706
.L54:
	.byte	24
	.word	70706
.L55:
	.byte	24
	.word	70803
.L56:
	.byte	24
	.word	70803
.L57:
	.byte	24
	.word	70803
.L58:
	.byte	24
	.word	70803
.L59:
	.byte	24
	.word	70803
.L60:
	.byte	24
	.word	70803
.L61:
	.byte	24
	.word	70803
.L62:
	.byte	24
	.word	70803
.L63:
	.byte	24
	.word	70803
	.byte	24
	.word	70706
	.byte	3
	.word	70991
	.byte	14,12
	.word	70996
	.byte	15,2,0,14,36
	.word	71001
	.byte	15,2,0
.L64:
	.byte	14,36
	.word	71010
	.byte	15,0,0,24
	.word	70803
	.byte	3
	.word	71028
	.byte	14,12
	.word	71033
	.byte	15,2,0,14,36
	.word	71038
	.byte	15,2,0
.L65:
	.byte	14,36
	.word	71047
	.byte	15,0,0,0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L4:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,21,0,54,15,39,12,0,0,3,15,0,73,19,0,0,4,46,1,3,8,32,13
	.byte	58,15,59,15,57,15,54,15,39,12,0,0,5,5,0,3,8,58,15,59,15,57,15,73,19,0,0,6,11,0,0,0,7,36,0,3,8,11,15,62
	.byte	15,0,0,8,46,1,3,8,32,13,58,15,59,15,57,15,73,19,54,15,39,12,0,0,9,59,0,3,8,0,0,10,19,1,3,8,58,15,59,15
	.byte	57,15,11,15,0,0,11,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,12,23,1,58,15,59,15,57,15,11,15,0,0,13,13
	.byte	0,3,8,73,19,11,15,56,9,0,0,14,1,1,11,15,73,19,0,0,15,33,0,47,15,0,0,16,53,0,73,19,0,0,17,4,1,58,15,59
	.byte	15,57,15,11,15,0,0,18,40,0,3,8,28,13,0,0,19,46,1,49,19,0,0,20,5,0,49,19,0,0,21,22,0,3,8,58,15,59,15,57
	.byte	15,73,19,0,0,22,21,0,54,15,0,0,23,19,1,58,15,59,15,57,15,11,15,0,0,24,38,0,73,19,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L5:
	.word	.L67-.L66
.L66:
	.half	3
	.word	.L69-.L68
.L68:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg',0
	.byte	0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_Intrinsics.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Port\\Std\\IfxPort.h',0
	.byte	0,0,0
	.byte	'IfxPort_regdef.h',0,1,0,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPsi5_PinMap.c',0,0,0,0
	.byte	'IfxPsi5_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxPsi5_cfg.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Platform_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Ifx_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxScu_cfg.h',0
	.byte	0,0,0
	.byte	'IfxScu_regdef.h',0,1,0,0
	.byte	'..\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\_PinMap\\IfxPsi5_PinMap.h',0,0,0,0,0
.L69:
.L67:
	.sdecl	'.debug_info',debug,cluster('IfxPsi5_RX0A_P00_1_IN')
	.sect	'.debug_info'
.L6:
	.word	273
	.half	3
	.word	.L7
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPsi5_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPsi5_RX0A_P00_1_IN',0,5,48,15
	.word	.L46
	.byte	1,5,3
	.word	IfxPsi5_RX0A_P00_1_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPsi5_RX0A_P00_1_IN')
	.sect	'.debug_abbrev'
.L7:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPsi5_RX0B_P02_3_IN')
	.sect	'.debug_info'
.L8:
	.word	273
	.half	3
	.word	.L9
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPsi5_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPsi5_RX0B_P02_3_IN',0,5,49,15
	.word	.L47
	.byte	1,5,3
	.word	IfxPsi5_RX0B_P02_3_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPsi5_RX0B_P02_3_IN')
	.sect	'.debug_abbrev'
.L9:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPsi5_RX0C_P33_1_IN')
	.sect	'.debug_info'
.L10:
	.word	273
	.half	3
	.word	.L11
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPsi5_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPsi5_RX0C_P33_1_IN',0,5,50,15
	.word	.L48
	.byte	1,5,3
	.word	IfxPsi5_RX0C_P33_1_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPsi5_RX0C_P33_1_IN')
	.sect	'.debug_abbrev'
.L11:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPsi5_RX1A_P00_3_IN')
	.sect	'.debug_info'
.L12:
	.word	273
	.half	3
	.word	.L13
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPsi5_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPsi5_RX1A_P00_3_IN',0,5,51,15
	.word	.L49
	.byte	1,5,3
	.word	IfxPsi5_RX1A_P00_3_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPsi5_RX1A_P00_3_IN')
	.sect	'.debug_abbrev'
.L13:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPsi5_RX1B_P02_5_IN')
	.sect	'.debug_info'
.L14:
	.word	273
	.half	3
	.word	.L15
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPsi5_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPsi5_RX1B_P02_5_IN',0,5,52,15
	.word	.L50
	.byte	1,5,3
	.word	IfxPsi5_RX1B_P02_5_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPsi5_RX1B_P02_5_IN')
	.sect	'.debug_abbrev'
.L15:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPsi5_RX1C_P33_3_IN')
	.sect	'.debug_info'
.L16:
	.word	273
	.half	3
	.word	.L17
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPsi5_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPsi5_RX1C_P33_3_IN',0,5,53,15
	.word	.L51
	.byte	1,5,3
	.word	IfxPsi5_RX1C_P33_3_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPsi5_RX1C_P33_3_IN')
	.sect	'.debug_abbrev'
.L17:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPsi5_RX2A_P00_5_IN')
	.sect	'.debug_info'
.L18:
	.word	273
	.half	3
	.word	.L19
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPsi5_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPsi5_RX2A_P00_5_IN',0,5,54,15
	.word	.L52
	.byte	1,5,3
	.word	IfxPsi5_RX2A_P00_5_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPsi5_RX2A_P00_5_IN')
	.sect	'.debug_abbrev'
.L19:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPsi5_RX2B_P02_7_IN')
	.sect	'.debug_info'
.L20:
	.word	273
	.half	3
	.word	.L21
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPsi5_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPsi5_RX2B_P02_7_IN',0,5,55,15
	.word	.L53
	.byte	1,5,3
	.word	IfxPsi5_RX2B_P02_7_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPsi5_RX2B_P02_7_IN')
	.sect	'.debug_abbrev'
.L21:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPsi5_RX2C_P33_5_IN')
	.sect	'.debug_info'
.L22:
	.word	273
	.half	3
	.word	.L23
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPsi5_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPsi5_RX2C_P33_5_IN',0,5,56,15
	.word	.L54
	.byte	1,5,3
	.word	IfxPsi5_RX2C_P33_5_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPsi5_RX2C_P33_5_IN')
	.sect	'.debug_abbrev'
.L23:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPsi5_TX0_P00_2_OUT')
	.sect	'.debug_info'
.L24:
	.word	273
	.half	3
	.word	.L25
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPsi5_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPsi5_TX0_P00_2_OUT',0,5,57,16
	.word	.L55
	.byte	1,5,3
	.word	IfxPsi5_TX0_P00_2_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPsi5_TX0_P00_2_OUT')
	.sect	'.debug_abbrev'
.L25:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPsi5_TX0_P02_2_OUT')
	.sect	'.debug_info'
.L26:
	.word	273
	.half	3
	.word	.L27
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPsi5_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPsi5_TX0_P02_2_OUT',0,5,58,16
	.word	.L56
	.byte	1,5,3
	.word	IfxPsi5_TX0_P02_2_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPsi5_TX0_P02_2_OUT')
	.sect	'.debug_abbrev'
.L27:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPsi5_TX0_P33_2_OUT')
	.sect	'.debug_info'
.L28:
	.word	273
	.half	3
	.word	.L29
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPsi5_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPsi5_TX0_P33_2_OUT',0,5,59,16
	.word	.L57
	.byte	1,5,3
	.word	IfxPsi5_TX0_P33_2_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPsi5_TX0_P33_2_OUT')
	.sect	'.debug_abbrev'
.L29:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPsi5_TX1_P00_4_OUT')
	.sect	'.debug_info'
.L30:
	.word	273
	.half	3
	.word	.L31
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPsi5_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPsi5_TX1_P00_4_OUT',0,5,60,16
	.word	.L58
	.byte	1,5,3
	.word	IfxPsi5_TX1_P00_4_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPsi5_TX1_P00_4_OUT')
	.sect	'.debug_abbrev'
.L31:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPsi5_TX1_P02_6_OUT')
	.sect	'.debug_info'
.L32:
	.word	273
	.half	3
	.word	.L33
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPsi5_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPsi5_TX1_P02_6_OUT',0,5,61,16
	.word	.L59
	.byte	1,5,3
	.word	IfxPsi5_TX1_P02_6_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPsi5_TX1_P02_6_OUT')
	.sect	'.debug_abbrev'
.L33:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPsi5_TX1_P33_4_OUT')
	.sect	'.debug_info'
.L34:
	.word	273
	.half	3
	.word	.L35
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPsi5_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPsi5_TX1_P33_4_OUT',0,5,62,16
	.word	.L60
	.byte	1,5,3
	.word	IfxPsi5_TX1_P33_4_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPsi5_TX1_P33_4_OUT')
	.sect	'.debug_abbrev'
.L35:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPsi5_TX2_P00_6_OUT')
	.sect	'.debug_info'
.L36:
	.word	273
	.half	3
	.word	.L37
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPsi5_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPsi5_TX2_P00_6_OUT',0,5,63,16
	.word	.L61
	.byte	1,5,3
	.word	IfxPsi5_TX2_P00_6_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPsi5_TX2_P00_6_OUT')
	.sect	'.debug_abbrev'
.L37:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPsi5_TX2_P02_8_OUT')
	.sect	'.debug_info'
.L38:
	.word	273
	.half	3
	.word	.L39
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPsi5_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPsi5_TX2_P02_8_OUT',0,5,64,16
	.word	.L62
	.byte	1,5,3
	.word	IfxPsi5_TX2_P02_8_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPsi5_TX2_P02_8_OUT')
	.sect	'.debug_abbrev'
.L39:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPsi5_TX2_P33_6_OUT')
	.sect	'.debug_info'
.L40:
	.word	273
	.half	3
	.word	.L41
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPsi5_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPsi5_TX2_P33_6_OUT',0,5,65,16
	.word	.L63
	.byte	1,5,3
	.word	IfxPsi5_TX2_P33_6_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPsi5_TX2_P33_6_OUT')
	.sect	'.debug_abbrev'
.L41:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPsi5_Rx_In_pinTable')
	.sect	'.debug_info'
.L42:
	.word	274
	.half	3
	.word	.L43
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPsi5_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPsi5_Rx_In_pinTable',0,5,68,22
	.word	.L64
	.byte	1,5,3
	.word	IfxPsi5_Rx_In_pinTable
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPsi5_Rx_In_pinTable')
	.sect	'.debug_abbrev'
.L43:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPsi5_Tx_Out_pinTable')
	.sect	'.debug_info'
.L44:
	.word	275
	.half	3
	.word	.L45
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPsi5_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPsi5_Tx_Out_pinTable',0,5,88,23
	.word	.L65
	.byte	1,5,3
	.word	IfxPsi5_Tx_Out_pinTable
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPsi5_Tx_Out_pinTable')
	.sect	'.debug_abbrev'
.L45:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	; Module end
