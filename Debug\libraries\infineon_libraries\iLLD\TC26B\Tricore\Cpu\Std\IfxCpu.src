	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc33780a --dep-file=IfxCpu.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/infineon_libraries/iLLD/TC26B/Tricore/Cpu/Std/IfxCpu.src ../libraries/infineon_libraries/iLLD/TC26B/Tricore/Cpu/Std/IfxCpu.c"
	.compiler_name		"ctc"
	;source	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Cpu/Std/IfxCpu.c'

	
$TC16X
	
	.sdecl	'.text.IfxCpu.IfxCpu_acquireMutex',code,cluster('IfxCpu_acquireMutex')
	.sect	'.text.IfxCpu.IfxCpu_acquireMutex'
	.align	2
	
	.global	IfxCpu_acquireMutex
; Function IfxCpu_acquireMutex
.L62:
IfxCpu_acquireMutex:	.type	func
	sub.a	a10,#8
.L265:
	mov	d2,#0
.L266:
	mov	d15,#1
.L400:
	st.w	[a10],d15
.L401:
	ld.w	d0,[a10]
	mov	d15,#0
	mov	d1,d15
	cmpswap.w	[a4]0,e0
.L402:
	st.w	[a10],d0
.L403:
	ld.w	d15,[a10]
.L404:
	jne	d15,#0,.L2
.L405:
	mov	d2,#1
.L2:
	j	.L3
.L3:
	ret
.L208:
	
__IfxCpu_acquireMutex_function_end:
	.size	IfxCpu_acquireMutex,__IfxCpu_acquireMutex_function_end-IfxCpu_acquireMutex
.L120:
	; End of function
	
	.sdecl	'.text.IfxCpu.IfxCpu_getCoreMode',code,cluster('IfxCpu_getCoreMode')
	.sect	'.text.IfxCpu.IfxCpu_getCoreMode'
	.align	2
	
	.global	IfxCpu_getCoreMode
; Function IfxCpu_getCoreMode
.L64:
IfxCpu_getCoreMode:	.type	func
	mov.aa	a15,a4
.L268:
	mov.aa	a4,a15
	call	IfxCpu_getIndex
.L267:
	mov	d0,#5
.L166:
	mfcr	d15,#65052
.L271:
	and	d15,#7
.L272:
	j	.L4
.L4:
	jeq	d15,d2,.L5
.L339:
	movh.a	a3,#1
	add.a	a3,a15
	lea	a3,[a3]-768
	ld.w	d1,[a3]
.L273:
	j	.L6
.L5:
	mfcr	d1,#64768
.L6:
	extr.u	d15,d1,#1,#2
.L340:
	jne	d15,#1,.L7
.L341:
	mov	d0,#0
.L342:
	j	.L8
.L7:
	extr.u	d15,d1,#1,#2
.L343:
	jne	d15,#0,.L9
.L170:
	mul	d15,d2,#4
	mov.a	a15,d15
.L269:
	movh.a	a3,#61443
	add.a	a3,a15
	lea	a15,[a3]24788
.L274:
	ld.bu	d15,[a15]1
	and	d15,#7
.L344:
	jne	d15,#1,.L10
.L345:
	mov	d0,#1
.L346:
	j	.L11
.L10:
	ld.bu	d15,[a15]1
	and	d15,#7
.L347:
	jne	d15,#3,.L12
.L348:
	mov	d0,#2
.L12:
.L11:
	j	.L13
.L9:
	mov	d0,#5
.L13:
.L8:
	mov	d2,d0
.L270:
	j	.L14
.L14:
	ret
.L157:
	
__IfxCpu_getCoreMode_function_end:
	.size	IfxCpu_getCoreMode,__IfxCpu_getCoreMode_function_end-IfxCpu_getCoreMode
.L95:
	; End of function
	
	.sdecl	'.text.IfxCpu.IfxCpu_getIndex',code,cluster('IfxCpu_getIndex')
	.sect	'.text.IfxCpu.IfxCpu_getIndex'
	.align	2
	
	.global	IfxCpu_getIndex
; Function IfxCpu_getIndex
.L66:
IfxCpu_getIndex:	.type	func
	mov	d2,#2
.L275:
	mov	d0,#0
.L276:
	j	.L15
.L16:
	mul	d15,d0,#8
.L353:
	movh.a	a15,#@his(IfxCpu_cfg_indexMap)
	lea	a15,[a15]@los(IfxCpu_cfg_indexMap)
.L354:
	addsc.a	a15,a15,d15,#0
.L355:
	ld.a	a15,[a15]
.L356:
	jne.a	a15,a4,.L17
.L357:
	mul	d15,d0,#8
.L358:
	movh.a	a15,#@his(IfxCpu_cfg_indexMap)
	lea	a15,[a15]@los(IfxCpu_cfg_indexMap)
.L359:
	addsc.a	a15,a15,d15,#0
.L360:
	ld.w	d15,[a15]4
.L361:
	extr.u	d2,d15,#0,#8
.L362:
	j	.L18
.L17:
	add	d0,#1
.L15:
	jlt.u	d0,#2,.L16
.L18:
	j	.L19
.L19:
	ret
.L173:
	
__IfxCpu_getIndex_function_end:
	.size	IfxCpu_getIndex,__IfxCpu_getIndex_function_end-IfxCpu_getIndex
.L100:
	; End of function
	
	.sdecl	'.text.IfxCpu.IfxCpu_getRandomValue',code,cluster('IfxCpu_getRandomValue')
	.sect	'.text.IfxCpu.IfxCpu_getRandomValue'
	.align	2
	
	.global	IfxCpu_getRandomValue
; Function IfxCpu_getRandomValue
.L68:
IfxCpu_getRandomValue:	.type	func
	ld.w	d0,[a4]
.L277:
	jne	d0,#0,.L20
.L434:
	mov	d0,#42
.L20:
	mov	d1,#24769
.L278:
	addih	d1,d1,#4264
.L435:
	mov	d2,#-5
.L233:
	
	      mul.u     e14,d1,d0      ; d15 = Eh; d14 = El    
        mov       d12,d14        ;   e12 = El            
        mov       d13,#0         ;                       
        madd.u    e14,e12,d15,#5 ; e14 = El + 5 * d15    
 1:     jge.u     d14,d2,2n      ;                       
        jz        d15,3n         ;                       
 2:     subx      d14,d14,d2     ;  e12=e12-m            
        subc      d15,d15,d13    ; d13=d13-0             
        loopu     1p             ;                       
 3:     mov       d2,d14         ;                       

.L279:
	
.L436:
	j	.L21
.L21:
	st.w	[a4],d2
.L234:
	j	.L22
.L22:
	ret
.L225:
	
__IfxCpu_getRandomValue_function_end:
	.size	IfxCpu_getRandomValue,__IfxCpu_getRandomValue_function_end-IfxCpu_getRandomValue
.L140:
	; End of function
	
	.sdecl	'.text.IfxCpu.IfxCpu_getRandomValueWithinRange',code,cluster('IfxCpu_getRandomValueWithinRange')
	.sect	'.text.IfxCpu.IfxCpu_getRandomValueWithinRange'
	.align	2
	
	.global	IfxCpu_getRandomValueWithinRange
; Function IfxCpu_getRandomValueWithinRange
.L70:
IfxCpu_getRandomValueWithinRange:	.type	func
	mov	d8,d4
.L281:
	mov	d15,d5
.L282:
	call	IfxCpu_getRandomValue
.L280:
	jge.u	d15,d8,.L23
.L248:
	mov	d0,d15
.L285:
	mov	d15,d8
.L441:
	mov	d8,d0
.L23:
	jne	d8,#0,.L24
.L442:
	jne	d15,#-1,.L25
.L443:
	j	.L26
.L25:
.L24:
	sub	d15,d8
.L283:
	add	d15,#1
.L444:
	div.u	e0,d2,d15
.L445:
	add	d2,d1,d8
.L284:
	j	.L27
.L27:
.L26:
	ret
.L243:
	
__IfxCpu_getRandomValueWithinRange_function_end:
	.size	IfxCpu_getRandomValueWithinRange,__IfxCpu_getRandomValueWithinRange_function_end-IfxCpu_getRandomValueWithinRange
.L145:
	; End of function
	
	.sdecl	'.text.IfxCpu.IfxCpu_releaseMutex',code,cluster('IfxCpu_releaseMutex')
	.sect	'.text.IfxCpu.IfxCpu_releaseMutex'
	.align	2
	
	.global	IfxCpu_releaseMutex
; Function IfxCpu_releaseMutex
.L72:
IfxCpu_releaseMutex:	.type	func
	mov	d15,#0
.L410:
	st.w	[a4],d15
.L411:
	ret
.L214:
	
__IfxCpu_releaseMutex_function_end:
	.size	IfxCpu_releaseMutex,__IfxCpu_releaseMutex_function_end-IfxCpu_releaseMutex
.L125:
	; End of function
	
	.sdecl	'.text.IfxCpu.IfxCpu_resetSpinLock',code,cluster('IfxCpu_resetSpinLock')
	.sect	'.text.IfxCpu.IfxCpu_resetSpinLock'
	.align	2
	
	.global	IfxCpu_resetSpinLock
; Function IfxCpu_resetSpinLock
.L74:
IfxCpu_resetSpinLock:	.type	func
	mov	d15,#0
.L416:
	st.w	[a4],d15
.L417:
	ret
.L216:
	
__IfxCpu_resetSpinLock_function_end:
	.size	IfxCpu_resetSpinLock,__IfxCpu_resetSpinLock_function_end-IfxCpu_resetSpinLock
.L130:
	; End of function
	
	.sdecl	'.text.IfxCpu.IfxCpu_setCoreMode',code,cluster('IfxCpu_setCoreMode')
	.sect	'.text.IfxCpu.IfxCpu_setCoreMode'
	.align	2
	
	.global	IfxCpu_setCoreMode
; Function IfxCpu_setCoreMode
.L76:
IfxCpu_setCoreMode:	.type	func
	mov.aa	a15,a4
.L287:
	mov	d15,d4
.L289:
	mov.aa	a4,a15
	call	IfxCpu_getIndex
.L286:
	mov	d8,d2
.L292:
	jeq	d15,#0,.L28
.L367:
	jeq	d15,#3,.L29
.L368:
	jne	d15,#4,.L30
.L29:
.L28:
	mov	d9,#0
.L294:
	j	.L31
.L30:
	mov	d9,#1
.L31:
	jne	d15,#2,.L32
.L369:
	mov	d10,#1
.L295:
	j	.L33
.L32:
	mov	d10,#0
.L33:
	jne	d9,#1,.L34
.L185:
	mfcr	d15,#65052
.L290:
	and	d15,#7
.L296:
	j	.L35
.L35:
	jeq	d15,d8,.L36
.L187:
	call	IfxScuWdt_getSafetyWatchdogPassword
.L291:
	mov	d8,d2
.L293:
	mov	d4,d8
.L297:
	call	IfxScuWdt_clearSafetyEndinit
.L298:
	movh.a	a12,#61443
	lea	a12,[a12]@los(0xf00360d4)
.L370:
	mov.aa	a4,a15
.L299:
	call	IfxCpu_getIndex
.L300:
	mul	d15,d2,#4
	addsc.a	a2,a12,d15,#0
.L371:
	ld.bu	d15,[a2]
	insert	d15,d15,d10,#0,#2
	st.b	[a2],d15
.L372:
	mov	d4,d8
.L301:
	call	IfxScuWdt_setSafetyEndinit
.L302:
	movh.a	a3,#1
	add.a	a3,a15
	lea	a3,[a3]-768
	ld.w	d15,[a3]
.L373:
	insert	d15,d15,#2,#1,#2
	movh.a	a3,#1
	add.a	a3,a15
	lea	a3,[a3]-768
	st.w	[a3],d15
.L188:
	j	.L37
.L36:
	call	IfxScuWdt_getCpuWatchdogPassword
.L303:
	mov	d11,d2
.L305:
	mov	d4,d11
.L304:
	call	IfxScuWdt_clearCpuEndinit
.L306:
	movh.a	a15,#61443
.L288:
	lea	a15,[a15]@los(0xf00360d4)
.L374:
	mul	d15,d8,#4
	addsc.a	a15,a15,d15,#0
.L375:
	movh.a	a2,#61443
	lea	a2,[a2]@los(0xf00360d4)
.L376:
	mul	d15,d8,#4
	addsc.a	a2,a2,d15,#0
.L377:
	ld.bu	d15,[a2]
.L378:
	insert	d15,d15,d10,#0,#2
	st.b	[a15],d15
.L379:
	mov	d4,d11
.L307:
	call	IfxScuWdt_setCpuEndinit
.L37:
.L34:
	mov	d2,d9
	j	.L38
.L38:
	ret
.L179:
	
__IfxCpu_setCoreMode_function_end:
	.size	IfxCpu_setCoreMode,__IfxCpu_setCoreMode_function_end-IfxCpu_setCoreMode
.L105:
	; End of function
	
	.sdecl	'.text.IfxCpu.IfxCpu_setProgramCounter',code,cluster('IfxCpu_setProgramCounter')
	.sect	'.text.IfxCpu.IfxCpu_setProgramCounter'
	.align	2
	
	.global	IfxCpu_setProgramCounter
; Function IfxCpu_setProgramCounter
.L78:
IfxCpu_setProgramCounter:	.type	func
	mov	d2,#1
.L196:
	mfcr	d15,#65052
.L309:
	and	d15,#7
.L310:
	j	.L39
.L39:
	jge.u	d15,#2,.L40
.L384:
	mul	d15,d15,#8
.L385:
	movh.a	a15,#@his(IfxCpu_cfg_indexMap)
	lea	a15,[a15]@los(IfxCpu_cfg_indexMap)
.L386:
	addsc.a	a15,a15,d15,#0
.L387:
	ld.a	a15,[a15]
.L311:
	j	.L41
.L40:
	mov.a	a15,#0
.L41:
	j	.L42
.L42:
	jne.a	a4,a15,.L43
.L199:
	mov	d2,#0
.L388:
	j	.L44
.L43:
	movh.a	a3,#1
	add.a	a3,a4
	lea	a3,[a3]-504
	ld.w	d15,[a3]
.L389:
	sh	d4,#-1
.L308:
	insert	d15,d15,d4,#1,#31
	movh.a	a3,#1
	add.a	a3,a4
	lea	a3,[a3]-504
	st.w	[a3],d15
.L44:
	j	.L45
.L45:
	ret
.L192:
	
__IfxCpu_setProgramCounter_function_end:
	.size	IfxCpu_setProgramCounter,__IfxCpu_setProgramCounter_function_end-IfxCpu_setProgramCounter
.L110:
	; End of function
	
	.sdecl	'.text.IfxCpu.IfxCpu_setSpinLock',code,cluster('IfxCpu_setSpinLock')
	.sect	'.text.IfxCpu.IfxCpu_setSpinLock'
	.align	2
	
	.global	IfxCpu_setSpinLock
; Function IfxCpu_setSpinLock
.L80:
IfxCpu_setSpinLock:	.type	func
	sub.a	a10,#8
.L312:
	mov	d2,#0
.L46:
	mov	d15,#1
.L422:
	st.w	[a10],d15
.L423:
	ld.w	d0,[a10]
	mov	d15,#0
	mov	d1,d15
	cmpswap.w	[a4]0,e0
.L424:
	st.w	[a10],d0
.L425:
	ld.w	d15,[a10]
.L426:
	jne	d15,#0,.L47
.L427:
	mov	d2,#1
.L428:
	j	.L48
.L47:
	add	d4,#-1
.L48:
	jne	d2,#0,.L49
.L429:
	jge.u	d4,#1,.L46
.L49:
	j	.L50
.L50:
	ret
.L219:
	
__IfxCpu_setSpinLock_function_end:
	.size	IfxCpu_setSpinLock,__IfxCpu_setSpinLock_function_end-IfxCpu_setSpinLock
.L135:
	; End of function
	
	.sdecl	'.text.IfxCpu.IfxCpu_startCore',code,cluster('IfxCpu_startCore')
	.sect	'.text.IfxCpu.IfxCpu_startCore'
	.align	2
	
	.global	IfxCpu_startCore
; Function IfxCpu_startCore
.L82:
IfxCpu_startCore:	.type	func
	mov.aa	a15,a4
.L314:
	mov	d15,#1
.L315:
	mov.aa	a4,a15
	call	IfxCpu_setProgramCounter
.L313:
	and	d15,d2
.L394:
	mov.aa	a4,a15
.L316:
	call	IfxCpu_getCoreMode
.L317:
	jne	d2,#0,.L51
.L395:
	mov	d4,#1
	mov.aa	a4,a15
.L318:
	call	IfxCpu_setCoreMode
.L319:
	and	d15,d2
.L51:
	mov	d2,d15
.L320:
	j	.L52
.L52:
	ret
.L204:
	
__IfxCpu_startCore_function_end:
	.size	IfxCpu_startCore,__IfxCpu_startCore_function_end-IfxCpu_startCore
.L115:
	; End of function
	
	.sdecl	'.text.IfxCpu.IfxCpu_waitEvent',code,cluster('IfxCpu_waitEvent')
	.sect	'.text.IfxCpu.IfxCpu_waitEvent'
	.align	2
	
	.global	IfxCpu_waitEvent
; Function IfxCpu_waitEvent
.L84:
IfxCpu_waitEvent:	.type	func
	mov	d8,d4
.L322:
	mov.d	d15,a4
.L323:
	insert	d15,d15,#0,#0,#28
.L324:
	movh	d0,#53248
	jne	d15,d0,.L53
	mov.d	d15,a4
.L325:
	insert	d15,d15,#0,#20,#12
.L326:
	insert	d15,d15,#7,#28,#3
	mfcr	d0,#65052
	movh	d1,#4096
	mul	d0,d1
	sub	d15,d0
	j	.L54
.L53:
	mov.d	d15,a4
.L54:
	mov.a	a15,d15
.L327:
	mov	d9,#0
.L261:
	call	IfxScuCcu_getSourceFrequency
.L321:
	movh.a	a2,#61443
	ld.bu	d15,[a2]@los(0xf0036035)
	and	d15,#15
	itof	d15,d15
.L450:
	div.f	d15,d2,d15
.L451:
	j	.L55
.L55:
	movh	d0,#17530
.L452:
	div.f	d15,d15,d0
.L453:
	utof	d0,d8
.L454:
	mul.f	d15,d15,d0
.L455:
	ftouz	d0,d15
.L328:
	ld.w	d1,0xf0000010
.L329:
	j	.L56
.L57:
	nop
.L456:
	ld.w	d15,0xf0000010
.L457:
	sub	d15,d1
.L458:
	jlt.u	d15,d0,.L58
.L459:
	mov	d9,#1
.L460:
	j	.L59
.L58:
.L56:
	ld.w	d15,[a15]
.L461:
	and	d15,#3
.L462:
	jne	d15,#3,.L57
.L59:
	mov	d2,d9
.L330:
	j	.L60
.L60:
	ret
.L251:
	
__IfxCpu_waitEvent_function_end:
	.size	IfxCpu_waitEvent,__IfxCpu_waitEvent_function_end-IfxCpu_waitEvent
.L150:
	; End of function
	
	.sdecl	'.text.IfxCpu.IfxCpu_emitEvent',code,cluster('IfxCpu_emitEvent')
	.sect	'.text.IfxCpu.IfxCpu_emitEvent'
	.align	2
	
	.global	IfxCpu_emitEvent
; Function IfxCpu_emitEvent
.L86:
IfxCpu_emitEvent:	.type	func
	mfcr	d15,#65052
	imask	e0,#1,d15,#1
	ldmst	[a4]0,e0
.L467:
	ret
.L263:
	
__IfxCpu_emitEvent_function_end:
	.size	IfxCpu_emitEvent,__IfxCpu_emitEvent_function_end-IfxCpu_emitEvent
.L155:
	; End of function
	
	.calls	'IfxCpu_getCoreMode','IfxCpu_getIndex'
	.calls	'IfxCpu_getRandomValueWithinRange','IfxCpu_getRandomValue'
	.calls	'IfxCpu_setCoreMode','IfxCpu_getIndex'
	.calls	'IfxCpu_setCoreMode','IfxScuWdt_getSafetyWatchdogPassword'
	.calls	'IfxCpu_setCoreMode','IfxScuWdt_clearSafetyEndinit'
	.calls	'IfxCpu_setCoreMode','IfxScuWdt_setSafetyEndinit'
	.calls	'IfxCpu_setCoreMode','IfxScuWdt_getCpuWatchdogPassword'
	.calls	'IfxCpu_setCoreMode','IfxScuWdt_clearCpuEndinit'
	.calls	'IfxCpu_setCoreMode','IfxScuWdt_setCpuEndinit'
	.calls	'IfxCpu_startCore','IfxCpu_setProgramCounter'
	.calls	'IfxCpu_startCore','IfxCpu_getCoreMode'
	.calls	'IfxCpu_startCore','IfxCpu_setCoreMode'
	.calls	'IfxCpu_waitEvent','IfxScuCcu_getSourceFrequency'
	.calls	'IfxCpu_acquireMutex','',8
	.calls	'IfxCpu_getCoreMode','',0
	.calls	'IfxCpu_getIndex','',0
	.calls	'IfxCpu_getRandomValue','',0
	.calls	'IfxCpu_getRandomValueWithinRange','',0
	.calls	'IfxCpu_releaseMutex','',0
	.calls	'IfxCpu_resetSpinLock','',0
	.calls	'IfxCpu_setCoreMode','',0
	.calls	'IfxCpu_setProgramCounter','',0
	.calls	'IfxCpu_setSpinLock','',8
	.calls	'IfxCpu_startCore','',0
	.calls	'IfxCpu_waitEvent','',0
	.extern	IfxCpu_cfg_indexMap
	.extern	IfxScuWdt_clearCpuEndinit
	.extern	IfxScuWdt_clearSafetyEndinit
	.extern	IfxScuWdt_setCpuEndinit
	.extern	IfxScuWdt_setSafetyEndinit
	.extern	IfxScuWdt_getCpuWatchdogPassword
	.extern	IfxScuWdt_getSafetyWatchdogPassword
	.extern	IfxScuCcu_getSourceFrequency
	.calls	'IfxCpu_emitEvent','',0
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L88:
	.word	78472
	.half	3
	.word	.L89
	.byte	4
.L87:
	.byte	1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Cpu/Std/IfxCpu.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L90
	.byte	2,1,1,3
	.word	230
	.byte	4
	.byte	'Ifx__jump_and_link',0,3,1,61,17,1,1,5
	.byte	'fun',0,1,61,43
	.word	233
	.byte	6,0,7
	.byte	'__fract',0,4,128,1,7
	.byte	'float',0,4,4,8
	.byte	'Ifx__float_to_fract',0,3,1,152,2,18
	.word	278
	.byte	1,1,5
	.byte	'a',0,1,152,2,44
	.word	290
	.byte	6,0,4
	.byte	'Ifx__stopPerfCounters',0,3,1,172,2,17,1,1,6,0
.L176:
	.byte	7
	.byte	'unsigned long int',0,4,7
.L232:
	.byte	8
	.byte	'IfxCpu_getRandomVal',0,3,1,247,2,19
	.word	376
	.byte	1,1
.L235:
	.byte	5
	.byte	'a',0,1,247,2,46
	.word	376
.L237:
	.byte	5
	.byte	'x',0,1,247,2,56
	.word	376
.L239:
	.byte	5
	.byte	'm',0,1,247,2,66
	.word	376
.L241:
	.byte	6,0,7
	.byte	'unsigned long long int',0,8,7,9
	.byte	'void',0,3
	.word	490
	.byte	8
	.byte	'__ld64',0,3,2,135,1,19
	.word	464
	.byte	1,1,5
	.byte	'addr',0,2,135,1,32
	.word	496
	.byte	6,0,4
	.byte	'__st64',0,3,2,143,1,17,1,1,5
	.byte	'addr',0,2,143,1,30
	.word	496
	.byte	5
	.byte	'value',0,2,143,1,43
	.word	464
	.byte	6,0
.L249:
	.byte	7
	.byte	'unsigned int',0,4,7,7
	.byte	'int',0,4,5,7
	.byte	'unsigned int',0,4,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON0_Bits',0,4,241,8,16,4,11
	.byte	'ENDINIT',0,4
	.word	605
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	605
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	605
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	605
	.byte	16,0,2,35,0,0,12,4,247,14,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	621
	.byte	4,2,35,0,0
.L178:
	.byte	7
	.byte	'unsigned char',0,1,8
.L189:
	.byte	7
	.byte	'unsigned short int',0,2,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON1_Bits',0,4,250,8,16,4,11
	.byte	'reserved_0',0,1
	.word	757
	.byte	2,6,2,35,0,11
	.byte	'IR0',0,1
	.word	757
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	757
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	757
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	757
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	757
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	757
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	757
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	757
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	774
	.byte	16,0,2,35,2,0,12,4,255,14,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	796
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_WDTCPU_SR_Bits',0,4,137,9,16,4,11
	.byte	'AE',0,1
	.word	757
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	757
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	757
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	757
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	757
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	757
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	757
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	757
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	757
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	757
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	774
	.byte	16,0,2,35,2,0,12,4,135,15,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1040
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_WDTCPU',0,4,175,15,25,12,13
	.byte	'CON0',0
	.word	717
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	1000
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	1231
	.byte	4,2,35,8,0,14
	.word	1271
	.byte	3
	.word	1334
	.byte	4
	.byte	'IfxScuWdt_clearCpuEndinitInline',0,3,3,181,3,17,1,1,5
	.byte	'watchdog',0,3,181,3,65
	.word	1339
	.byte	5
	.byte	'password',0,3,181,3,82
	.word	774
	.byte	6,0,4
	.byte	'IfxScuWdt_setCpuEndinitInline',0,3,3,140,4,17,1,1,5
	.byte	'watchdog',0,3,140,4,63
	.word	1339
	.byte	5
	.byte	'password',0,3,140,4,80
	.word	774
	.byte	6,0,8
	.byte	'IfxScuWdt_getCpuWatchdogPasswordInline',0,3,3,227,3,19
	.word	774
	.byte	1,1,5
	.byte	'watchdog',0,3,227,3,74
	.word	1339
	.byte	6,0,10
	.byte	'_Ifx_P_OUT_Bits',0,6,143,3,16,4,11
	.byte	'P0',0,1
	.word	757
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	757
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	757
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	757
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	757
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	757
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	757
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	757
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	757
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	757
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	757
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	757
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	757
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	757
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	757
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	757
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	774
	.byte	16,0,2,35,2,0,12,6,181,5,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1569
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMR_Bits',0,6,169,2,16,4,11
	.byte	'PS0',0,1
	.word	757
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	757
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	757
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	757
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	757
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	757
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	757
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	757
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	757
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	757
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	757
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	757
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	757
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	757
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	757
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	757
	.byte	1,0,2,35,1,11
	.byte	'PCL0',0,1
	.word	757
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	757
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	757
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	757
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	757
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	757
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	757
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	757
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	757
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	757
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	757
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	757
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	757
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	757
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	757
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	757
	.byte	1,0,2,35,3,0,12,6,133,5,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1885
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ID_Bits',0,6,110,16,4,11
	.byte	'MODREV',0,1
	.word	757
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	757
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	774
	.byte	16,0,2,35,2,0,12,6,148,4,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2456
	.byte	4,2,35,0,0,15,4
	.word	757
	.byte	16,3,0,10
	.byte	'_Ifx_P_IOCR0_Bits',0,6,140,1,16,4,11
	.byte	'reserved_0',0,1
	.word	757
	.byte	3,5,2,35,0,11
	.byte	'PC0',0,1
	.word	757
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	757
	.byte	3,5,2,35,1,11
	.byte	'PC1',0,1
	.word	757
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	757
	.byte	3,5,2,35,2,11
	.byte	'PC2',0,1
	.word	757
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	757
	.byte	3,5,2,35,3,11
	.byte	'PC3',0,1
	.word	757
	.byte	5,0,2,35,3,0,12,6,164,4,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2584
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR4_Bits',0,6,166,1,16,4,11
	.byte	'reserved_0',0,1
	.word	757
	.byte	3,5,2,35,0,11
	.byte	'PC4',0,1
	.word	757
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	757
	.byte	3,5,2,35,1,11
	.byte	'PC5',0,1
	.word	757
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	757
	.byte	3,5,2,35,2,11
	.byte	'PC6',0,1
	.word	757
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	757
	.byte	3,5,2,35,3,11
	.byte	'PC7',0,1
	.word	757
	.byte	5,0,2,35,3,0,12,6,180,4,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2799
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR8_Bits',0,6,179,1,16,4,11
	.byte	'reserved_0',0,1
	.word	757
	.byte	3,5,2,35,0,11
	.byte	'PC8',0,1
	.word	757
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	757
	.byte	3,5,2,35,1,11
	.byte	'PC9',0,1
	.word	757
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	757
	.byte	3,5,2,35,2,11
	.byte	'PC10',0,1
	.word	757
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	757
	.byte	3,5,2,35,3,11
	.byte	'PC11',0,1
	.word	757
	.byte	5,0,2,35,3,0,12,6,188,4,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3014
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR12_Bits',0,6,153,1,16,4,11
	.byte	'reserved_0',0,1
	.word	757
	.byte	3,5,2,35,0,11
	.byte	'PC12',0,1
	.word	757
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	757
	.byte	3,5,2,35,1,11
	.byte	'PC13',0,1
	.word	757
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	757
	.byte	3,5,2,35,2,11
	.byte	'PC14',0,1
	.word	757
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	757
	.byte	3,5,2,35,3,11
	.byte	'PC15',0,1
	.word	757
	.byte	5,0,2,35,3,0,12,6,172,4,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3231
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IN_Bits',0,6,118,16,4,11
	.byte	'P0',0,1
	.word	757
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	757
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	757
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	757
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	757
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	757
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	757
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	757
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	757
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	757
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	757
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	757
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	757
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	757
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	757
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	757
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	774
	.byte	16,0,2,35,2,0,12,6,156,4,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3451
	.byte	4,2,35,0,0,15,24
	.word	757
	.byte	16,23,0,10
	.byte	'_Ifx_P_PDR0_Bits',0,6,205,3,16,4,11
	.byte	'PD0',0,1
	.word	757
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	757
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	757
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	757
	.byte	1,0,2,35,0,11
	.byte	'PD2',0,1
	.word	757
	.byte	3,5,2,35,1,11
	.byte	'PL2',0,1
	.word	757
	.byte	1,4,2,35,1,11
	.byte	'PD3',0,1
	.word	757
	.byte	3,1,2,35,1,11
	.byte	'PL3',0,1
	.word	757
	.byte	1,0,2,35,1,11
	.byte	'PD4',0,1
	.word	757
	.byte	3,5,2,35,2,11
	.byte	'PL4',0,1
	.word	757
	.byte	1,4,2,35,2,11
	.byte	'PD5',0,1
	.word	757
	.byte	3,1,2,35,2,11
	.byte	'PL5',0,1
	.word	757
	.byte	1,0,2,35,2,11
	.byte	'PD6',0,1
	.word	757
	.byte	3,5,2,35,3,11
	.byte	'PL6',0,1
	.word	757
	.byte	1,4,2,35,3,11
	.byte	'PD7',0,1
	.word	757
	.byte	3,1,2,35,3,11
	.byte	'PL7',0,1
	.word	757
	.byte	1,0,2,35,3,0,12,6,205,5,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3774
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PDR1_Bits',0,6,226,3,16,4,11
	.byte	'PD8',0,1
	.word	757
	.byte	3,5,2,35,0,11
	.byte	'PL8',0,1
	.word	757
	.byte	1,4,2,35,0,11
	.byte	'PD9',0,1
	.word	757
	.byte	3,1,2,35,0,11
	.byte	'PL9',0,1
	.word	757
	.byte	1,0,2,35,0,11
	.byte	'PD10',0,1
	.word	757
	.byte	3,5,2,35,1,11
	.byte	'PL10',0,1
	.word	757
	.byte	1,4,2,35,1,11
	.byte	'PD11',0,1
	.word	757
	.byte	3,1,2,35,1,11
	.byte	'PL11',0,1
	.word	757
	.byte	1,0,2,35,1,11
	.byte	'PD12',0,1
	.word	757
	.byte	3,5,2,35,2,11
	.byte	'PL12',0,1
	.word	757
	.byte	1,4,2,35,2,11
	.byte	'PD13',0,1
	.word	757
	.byte	3,1,2,35,2,11
	.byte	'PL13',0,1
	.word	757
	.byte	1,0,2,35,2,11
	.byte	'PD14',0,1
	.word	757
	.byte	3,5,2,35,3,11
	.byte	'PL14',0,1
	.word	757
	.byte	1,4,2,35,3,11
	.byte	'PD15',0,1
	.word	757
	.byte	3,1,2,35,3,11
	.byte	'PL15',0,1
	.word	757
	.byte	1,0,2,35,3,0,12,6,213,5,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4078
	.byte	4,2,35,0,0,15,8
	.word	757
	.byte	16,7,0,10
	.byte	'_Ifx_P_ESR_Bits',0,6,88,16,4,11
	.byte	'EN0',0,1
	.word	757
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	757
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	757
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	757
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	757
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	757
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	757
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	757
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	757
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	757
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	757
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	757
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	757
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	757
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	757
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	757
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	774
	.byte	16,0,2,35,2,0,12,6,140,4,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4403
	.byte	4,2,35,0,0,15,12
	.word	757
	.byte	16,11,0,10
	.byte	'_Ifx_P_PDISC_Bits',0,6,183,3,16,4,11
	.byte	'PDIS0',0,1
	.word	757
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	757
	.byte	1,6,2,35,0,11
	.byte	'PDIS2',0,1
	.word	757
	.byte	1,5,2,35,0,11
	.byte	'PDIS3',0,1
	.word	757
	.byte	1,4,2,35,0,11
	.byte	'PDIS4',0,1
	.word	757
	.byte	1,3,2,35,0,11
	.byte	'PDIS5',0,1
	.word	757
	.byte	1,2,2,35,0,11
	.byte	'PDIS6',0,1
	.word	757
	.byte	1,1,2,35,0,11
	.byte	'PDIS7',0,1
	.word	757
	.byte	1,0,2,35,0,11
	.byte	'PDIS8',0,1
	.word	757
	.byte	1,7,2,35,1,11
	.byte	'PDIS9',0,1
	.word	757
	.byte	1,6,2,35,1,11
	.byte	'PDIS10',0,1
	.word	757
	.byte	1,5,2,35,1,11
	.byte	'PDIS11',0,1
	.word	757
	.byte	1,4,2,35,1,11
	.byte	'PDIS12',0,1
	.word	757
	.byte	1,3,2,35,1,11
	.byte	'PDIS13',0,1
	.word	757
	.byte	1,2,2,35,1,11
	.byte	'PDIS14',0,1
	.word	757
	.byte	1,1,2,35,1,11
	.byte	'PDIS15',0,1
	.word	757
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	774
	.byte	16,0,2,35,2,0,12,6,197,5,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4743
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PCSR_Bits',0,6,165,3,16,4,11
	.byte	'SEL0',0,1
	.word	757
	.byte	1,7,2,35,0,11
	.byte	'SEL1',0,1
	.word	757
	.byte	1,6,2,35,0,11
	.byte	'SEL2',0,1
	.word	757
	.byte	1,5,2,35,0,11
	.byte	'SEL3',0,1
	.word	757
	.byte	1,4,2,35,0,11
	.byte	'SEL4',0,1
	.word	757
	.byte	1,3,2,35,0,11
	.byte	'SEL5',0,1
	.word	757
	.byte	1,2,2,35,0,11
	.byte	'SEL6',0,1
	.word	757
	.byte	1,1,2,35,0,11
	.byte	'SEL7',0,1
	.word	757
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	757
	.byte	2,6,2,35,1,11
	.byte	'SEL10',0,1
	.word	757
	.byte	1,5,2,35,1,11
	.byte	'SEL11',0,1
	.word	757
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	582
	.byte	19,1,2,35,0,11
	.byte	'LCK',0,1
	.word	757
	.byte	1,0,2,35,3,0,12,6,189,5,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5109
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR0_Bits',0,6,206,2,16,4,11
	.byte	'PS0',0,1
	.word	757
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	757
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	757
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	757
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	582
	.byte	28,0,2,35,0,0,12,6,149,5,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5395
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR4_Bits',0,6,227,2,16,4,11
	.byte	'reserved_0',0,1
	.word	757
	.byte	4,4,2,35,0,11
	.byte	'PS4',0,1
	.word	757
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	757
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	757
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	757
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	582
	.byte	24,0,2,35,0,0,12,6,165,5,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5542
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR8_Bits',0,6,238,2,16,4,11
	.byte	'reserved_0',0,1
	.word	757
	.byte	8,0,2,35,0,11
	.byte	'PS8',0,1
	.word	757
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	757
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	757
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	757
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	582
	.byte	20,0,2,35,0,0,12,6,173,5,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5711
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR12_Bits',0,6,216,2,16,4,11
	.byte	'reserved_0',0,2
	.word	774
	.byte	12,4,2,35,0,11
	.byte	'PS12',0,1
	.word	757
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	757
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	757
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	757
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	774
	.byte	16,0,2,35,2,0,12,6,157,5,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5883
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR0_Bits',0,6,232,1,16,4,11
	.byte	'reserved_0',0,2
	.word	774
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	757
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	757
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	757
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	757
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	774
	.byte	12,0,2,35,2,0,12,6,229,4,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6058
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR4_Bits',0,6,253,1,16,4,11
	.byte	'reserved_0',0,4
	.word	582
	.byte	20,12,2,35,0,11
	.byte	'PCL4',0,1
	.word	757
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	757
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	757
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	757
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	757
	.byte	8,0,2,35,3,0,12,6,245,4,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6232
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR8_Bits',0,6,136,2,16,4,11
	.byte	'reserved_0',0,4
	.word	582
	.byte	24,8,2,35,0,11
	.byte	'PCL8',0,1
	.word	757
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	757
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	757
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	757
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	757
	.byte	4,0,2,35,3,0,12,6,253,4,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6406
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR12_Bits',0,6,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	582
	.byte	28,4,2,35,0,11
	.byte	'PCL12',0,1
	.word	757
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	757
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	757
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	757
	.byte	1,0,2,35,3,0,12,6,237,4,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6582
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR_Bits',0,6,249,2,16,4,11
	.byte	'PS0',0,1
	.word	757
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	757
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	757
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	757
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	757
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	757
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	757
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	757
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	757
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	757
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	757
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	757
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	757
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	757
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	757
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	757
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	774
	.byte	16,0,2,35,2,0,12,6,141,5,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6738
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR_Bits',0,6,147,2,16,4,11
	.byte	'reserved_0',0,2
	.word	774
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	757
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	757
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	757
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	757
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	757
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	757
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	757
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	757
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	757
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	757
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	757
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	757
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	757
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	757
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	757
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	757
	.byte	1,0,2,35,3,0,12,6,221,4,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7071
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR0_Bits',0,6,192,1,16,4,11
	.byte	'reserved_0',0,1
	.word	757
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	757
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	582
	.byte	30,0,2,35,0,0,12,6,196,4,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7419
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_Bits',0,6,200,1,16,4,11
	.byte	'reserved_0',0,1
	.word	757
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	757
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	582
	.byte	30,0,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_P21_Bits',0,6,208,1,16,4,11
	.byte	'RDIS_CTRL',0,1
	.word	757
	.byte	1,7,2,35,0,11
	.byte	'RX_DIS',0,1
	.word	757
	.byte	1,6,2,35,0,11
	.byte	'TERM',0,1
	.word	757
	.byte	1,5,2,35,0,11
	.byte	'LRXTERM',0,1
	.word	757
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	582
	.byte	24,0,2,35,0,0,12,6,204,4,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7543
	.byte	4,2,35,0,13
	.byte	'B_P21',0
	.word	7627
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR2_Bits',0,6,218,1,16,4,11
	.byte	'reserved_0',0,1
	.word	757
	.byte	8,0,2,35,0,11
	.byte	'LVDSR',0,1
	.word	757
	.byte	1,7,2,35,1,11
	.byte	'LVDSRL',0,1
	.word	757
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	757
	.byte	2,4,2,35,1,11
	.byte	'TDIS_CTRL',0,1
	.word	757
	.byte	1,3,2,35,1,11
	.byte	'TX_DIS',0,1
	.word	757
	.byte	1,2,2,35,1,11
	.byte	'TX_PD',0,1
	.word	757
	.byte	1,1,2,35,1,11
	.byte	'TX_PWDPD',0,1
	.word	757
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	774
	.byte	16,0,2,35,2,0,12,6,213,4,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7807
	.byte	4,2,35,0,0,15,76
	.word	757
	.byte	16,75,0,10
	.byte	'_Ifx_P_ACCEN1_Bits',0,6,82,16,4,11
	.byte	'reserved_0',0,4
	.word	582
	.byte	32,0,2,35,0,0,12,6,132,4,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8060
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ACCEN0_Bits',0,6,45,16,4,11
	.byte	'EN0',0,1
	.word	757
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	757
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	757
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	757
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	757
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	757
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	757
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	757
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	757
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	757
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	757
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	757
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	757
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	757
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	757
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	757
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	757
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	757
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	757
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	757
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	757
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	757
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	757
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	757
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	757
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	757
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	757
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	757
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	757
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	757
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	757
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	757
	.byte	1,0,2,35,3,0,12,6,252,3,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8147
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P',0,6,229,5,25,128,2,13
	.byte	'OUT',0
	.word	1845
	.byte	4,2,35,0,13
	.byte	'OMR',0
	.word	2416
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	2535
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	2575
	.byte	4,2,35,12,13
	.byte	'IOCR0',0
	.word	2759
	.byte	4,2,35,16,13
	.byte	'IOCR4',0
	.word	2974
	.byte	4,2,35,20,13
	.byte	'IOCR8',0
	.word	3191
	.byte	4,2,35,24,13
	.byte	'IOCR12',0
	.word	3411
	.byte	4,2,35,28,13
	.byte	'reserved_20',0
	.word	2575
	.byte	4,2,35,32,13
	.byte	'IN',0
	.word	3725
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	3765
	.byte	24,2,35,40,13
	.byte	'PDR0',0
	.word	4038
	.byte	4,2,35,64,13
	.byte	'PDR1',0
	.word	4354
	.byte	4,2,35,68,13
	.byte	'reserved_48',0
	.word	4394
	.byte	8,2,35,72,13
	.byte	'ESR',0
	.word	4694
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	4734
	.byte	12,2,35,84,13
	.byte	'PDISC',0
	.word	5069
	.byte	4,2,35,96,13
	.byte	'PCSR',0
	.word	5355
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	4394
	.byte	8,2,35,104,13
	.byte	'OMSR0',0
	.word	5502
	.byte	4,2,35,112,13
	.byte	'OMSR4',0
	.word	5671
	.byte	4,2,35,116,13
	.byte	'OMSR8',0
	.word	5843
	.byte	4,2,35,120,13
	.byte	'OMSR12',0
	.word	6018
	.byte	4,2,35,124,13
	.byte	'OMCR0',0
	.word	6192
	.byte	4,3,35,128,1,13
	.byte	'OMCR4',0
	.word	6366
	.byte	4,3,35,132,1,13
	.byte	'OMCR8',0
	.word	6542
	.byte	4,3,35,136,1,13
	.byte	'OMCR12',0
	.word	6698
	.byte	4,3,35,140,1,13
	.byte	'OMSR',0
	.word	7031
	.byte	4,3,35,144,1,13
	.byte	'OMCR',0
	.word	7379
	.byte	4,3,35,148,1,13
	.byte	'reserved_98',0
	.word	4394
	.byte	8,3,35,152,1,13
	.byte	'LPCR0',0
	.word	7503
	.byte	4,3,35,160,1,13
	.byte	'LPCR1',0
	.word	7752
	.byte	4,3,35,164,1,13
	.byte	'LPCR2',0
	.word	8011
	.byte	4,3,35,168,1,13
	.byte	'reserved_A4',0
	.word	8051
	.byte	76,3,35,172,1,13
	.byte	'ACCEN1',0
	.word	8107
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	8674
	.byte	4,3,35,252,1,0,14
	.word	8714
	.byte	3
	.word	9317
	.byte	17,5,172,1,9,4,18
	.byte	'IfxPort_State_notChanged',0,0,18
	.byte	'IfxPort_State_high',0,1,18
	.byte	'IfxPort_State_low',0,128,128,4,18
	.byte	'IfxPort_State_toggled',0,129,128,4,0,4
	.byte	'IfxPort_setPinState',0,3,5,208,4,17,1,1,5
	.byte	'port',0,5,208,4,44
	.word	9322
	.byte	5
	.byte	'pinIndex',0,5,208,4,56
	.word	757
	.byte	5
	.byte	'action',0,5,208,4,80
	.word	9327
	.byte	6,0
.L260:
	.byte	8
	.byte	'IfxScuCcu_getStmFrequency',0,3,7,226,8,20
	.word	290
	.byte	1,1
.L262:
	.byte	6,0,15,176,32
	.word	757
	.byte	16,175,32,0,10
	.byte	'_Ifx_CPU_SEGEN_Bits',0,9,150,4,16,4,11
	.byte	'ADFLIP',0,4
	.word	605
	.byte	8,24,2,35,0,11
	.byte	'ADTYPE',0,4
	.word	605
	.byte	2,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	605
	.byte	21,1,2,35,0,11
	.byte	'AE',0,4
	.word	605
	.byte	1,0,2,35,0,0,12,9,239,8,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9559
	.byte	4,2,35,0,0,15,208,223,1
	.word	757
	.byte	16,207,223,1,0,10
	.byte	'_Ifx_CPU_TASK_ASI_Bits',0,9,234,4,16,4,11
	.byte	'ASI',0,4
	.word	605
	.byte	5,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	605
	.byte	27,0,2,35,0,0,12,9,191,9,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9712
	.byte	4,2,35,0,0,15,248,1
	.word	757
	.byte	16,247,1,0,10
	.byte	'_Ifx_CPU_PMA0_Bits',0,9,224,3,16,4,11
	.byte	'reserved_0',0,4
	.word	605
	.byte	13,19,2,35,0,11
	.byte	'DAC',0,4
	.word	605
	.byte	3,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	605
	.byte	16,0,2,35,0,0,12,9,199,8,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9830
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_PMA1_Bits',0,9,232,3,16,4,11
	.byte	'reserved_0',0,4
	.word	605
	.byte	14,18,2,35,0,11
	.byte	'CAC',0,4
	.word	605
	.byte	2,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	605
	.byte	16,0,2,35,0,0,12,9,207,8,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9956
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_PMA2_Bits',0,9,240,3,16,4,11
	.byte	'PSI',0,4
	.word	605
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	605
	.byte	16,0,2,35,0,0,12,9,215,8,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10082
	.byte	4,2,35,0,0,15,244,29
	.word	757
	.byte	16,243,29,0,10
	.byte	'_Ifx_CPU_DCON2_Bits',0,9,195,1,16,4,11
	.byte	'DCACHE_SZE',0,4
	.word	605
	.byte	16,16,2,35,0,11
	.byte	'DSCRATCH_SZE',0,4
	.word	605
	.byte	16,0,2,35,0,0,12,9,191,6,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10197
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_SMACON_Bits',0,9,159,4,16,4,11
	.byte	'PC',0,4
	.word	605
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	605
	.byte	1,30,2,35,0,11
	.byte	'PT',0,4
	.word	605
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	605
	.byte	5,24,2,35,0,11
	.byte	'DC',0,4
	.word	605
	.byte	1,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	605
	.byte	1,22,2,35,0,11
	.byte	'DT',0,4
	.word	605
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	605
	.byte	13,8,2,35,0,11
	.byte	'IODT',0,4
	.word	605
	.byte	1,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	605
	.byte	7,0,2,35,0,0,12,9,247,8,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10310
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_DSTR_Bits',0,9,143,2,16,4,11
	.byte	'SRE',0,4
	.word	605
	.byte	1,31,2,35,0,11
	.byte	'GAE',0,4
	.word	605
	.byte	1,30,2,35,0,11
	.byte	'LBE',0,4
	.word	605
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	605
	.byte	3,26,2,35,0,11
	.byte	'CRE',0,4
	.word	605
	.byte	1,25,2,35,0,11
	.byte	'reserved_7',0,4
	.word	605
	.byte	7,18,2,35,0,11
	.byte	'DTME',0,4
	.word	605
	.byte	1,17,2,35,0,11
	.byte	'LOE',0,4
	.word	605
	.byte	1,16,2,35,0,11
	.byte	'SDE',0,4
	.word	605
	.byte	1,15,2,35,0,11
	.byte	'SCE',0,4
	.word	605
	.byte	1,14,2,35,0,11
	.byte	'CAC',0,4
	.word	605
	.byte	1,13,2,35,0,11
	.byte	'MPE',0,4
	.word	605
	.byte	1,12,2,35,0,11
	.byte	'CLE',0,4
	.word	605
	.byte	1,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	605
	.byte	3,8,2,35,0,11
	.byte	'ALN',0,4
	.word	605
	.byte	1,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	605
	.byte	7,0,2,35,0,0,12,9,143,7,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10562
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_DATR_Bits',0,9,152,1,16,4,11
	.byte	'reserved_0',0,4
	.word	605
	.byte	3,29,2,35,0,11
	.byte	'SBE',0,4
	.word	605
	.byte	1,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	605
	.byte	5,23,2,35,0,11
	.byte	'CWE',0,4
	.word	605
	.byte	1,22,2,35,0,11
	.byte	'CFE',0,4
	.word	605
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	605
	.byte	3,18,2,35,0,11
	.byte	'SOE',0,4
	.word	605
	.byte	1,17,2,35,0,11
	.byte	'SME',0,4
	.word	605
	.byte	1,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	605
	.byte	16,0,2,35,0,0,12,9,159,6,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10899
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_DEADD_Bits',0,9,209,1,16,4,11
	.byte	'ERROR_ADDRESS',0,4
	.word	605
	.byte	32,0,2,35,0,0,12,9,207,6,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11130
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_DIEAR_Bits',0,9,215,1,16,4,11
	.byte	'TA',0,4
	.word	605
	.byte	32,0,2,35,0,0,12,9,215,6,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11222
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_DIETR_Bits',0,9,221,1,16,4,11
	.byte	'IED',0,4
	.word	605
	.byte	1,31,2,35,0,11
	.byte	'IE_T',0,4
	.word	605
	.byte	1,30,2,35,0,11
	.byte	'IE_C',0,4
	.word	605
	.byte	1,29,2,35,0,11
	.byte	'IE_S',0,4
	.word	605
	.byte	1,28,2,35,0,11
	.byte	'IE_BI',0,4
	.word	605
	.byte	1,27,2,35,0,11
	.byte	'E_INFO',0,4
	.word	605
	.byte	6,21,2,35,0,11
	.byte	'IE_DUAL',0,4
	.word	605
	.byte	1,20,2,35,0,11
	.byte	'IE_SP',0,4
	.word	605
	.byte	1,19,2,35,0,11
	.byte	'IE_BS',0,4
	.word	605
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	605
	.byte	18,0,2,35,0,0,12,9,223,6,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11303
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_DCON0_Bits',0,9,187,1,16,4,11
	.byte	'reserved_0',0,4
	.word	605
	.byte	1,31,2,35,0,11
	.byte	'DCBYP',0,4
	.word	605
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	605
	.byte	30,0,2,35,0,0,12,9,183,6,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11544
	.byte	4,2,35,0,0,15,188,3
	.word	757
	.byte	16,187,3,0,10
	.byte	'_Ifx_CPU_PSTR_Bits',0,9,247,3,16,4,11
	.byte	'FRE',0,4
	.word	605
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	605
	.byte	1,30,2,35,0,11
	.byte	'FBE',0,4
	.word	605
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	605
	.byte	9,20,2,35,0,11
	.byte	'FPE',0,4
	.word	605
	.byte	1,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	605
	.byte	1,18,2,35,0,11
	.byte	'FME',0,4
	.word	605
	.byte	1,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	605
	.byte	17,0,2,35,0,0,12,9,223,8,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11683
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_PCON1_Bits',0,9,178,3,16,4,11
	.byte	'PCINV',0,4
	.word	605
	.byte	1,31,2,35,0,11
	.byte	'PBINV',0,4
	.word	605
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	605
	.byte	30,0,2,35,0,0,12,9,159,8,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11899
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_PCON2_Bits',0,9,186,3,16,4,11
	.byte	'PCACHE_SZE',0,4
	.word	605
	.byte	16,16,2,35,0,11
	.byte	'PSCRATCH_SZE',0,4
	.word	605
	.byte	16,0,2,35,0,0,12,9,167,8,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12022
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_PCON0_Bits',0,9,170,3,16,4,11
	.byte	'reserved_0',0,4
	.word	605
	.byte	1,31,2,35,0,11
	.byte	'PCBYP',0,4
	.word	605
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	605
	.byte	30,0,2,35,0,0,12,9,151,8,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12135
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_PIEAR_Bits',0,9,203,3,16,4,11
	.byte	'TA',0,4
	.word	605
	.byte	32,0,2,35,0,0,12,9,183,8,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12263
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_PIETR_Bits',0,9,209,3,16,4,11
	.byte	'IED',0,4
	.word	605
	.byte	1,31,2,35,0,11
	.byte	'IE_T',0,4
	.word	605
	.byte	1,30,2,35,0,11
	.byte	'IE_C',0,4
	.word	605
	.byte	1,29,2,35,0,11
	.byte	'IE_S',0,4
	.word	605
	.byte	1,28,2,35,0,11
	.byte	'IE_BI',0,4
	.word	605
	.byte	1,27,2,35,0,11
	.byte	'E_INFO',0,4
	.word	605
	.byte	6,21,2,35,0,11
	.byte	'IE_DUAL',0,4
	.word	605
	.byte	1,20,2,35,0,11
	.byte	'IE_SP',0,4
	.word	605
	.byte	1,19,2,35,0,11
	.byte	'IE_BS',0,4
	.word	605
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	605
	.byte	18,0,2,35,0,0,12,9,191,8,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12344
	.byte	4,2,35,0,0,15,232,3
	.word	757
	.byte	16,231,3,0,10
	.byte	'_Ifx_CPU_COMPAT_Bits',0,9,83,16,4,11
	.byte	'reserved_0',0,4
	.word	605
	.byte	3,29,2,35,0,11
	.byte	'RM',0,4
	.word	605
	.byte	1,28,2,35,0,11
	.byte	'SP',0,4
	.word	605
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	605
	.byte	27,0,2,35,0,0,12,9,215,5,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12596
	.byte	4,2,35,0,0,15,252,23
	.word	757
	.byte	16,251,23,0,10
	.byte	'_Ifx_CPU_FPU_TRAP_CON_Bits',0,9,183,2,16,4,11
	.byte	'TST',0,4
	.word	605
	.byte	1,31,2,35,0,11
	.byte	'TCL',0,4
	.word	605
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	605
	.byte	6,24,2,35,0,11
	.byte	'RM',0,4
	.word	605
	.byte	2,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	605
	.byte	8,14,2,35,0,11
	.byte	'FXE',0,4
	.word	605
	.byte	1,13,2,35,0,11
	.byte	'FUE',0,4
	.word	605
	.byte	1,12,2,35,0,11
	.byte	'FZE',0,4
	.word	605
	.byte	1,11,2,35,0,11
	.byte	'FVE',0,4
	.word	605
	.byte	1,10,2,35,0,11
	.byte	'FIE',0,4
	.word	605
	.byte	1,9,2,35,0,11
	.byte	'reserved_23',0,4
	.word	605
	.byte	3,6,2,35,0,11
	.byte	'FX',0,4
	.word	605
	.byte	1,5,2,35,0,11
	.byte	'FU',0,4
	.word	605
	.byte	1,4,2,35,0,11
	.byte	'FZ',0,4
	.word	605
	.byte	1,3,2,35,0,11
	.byte	'FV',0,4
	.word	605
	.byte	1,2,2,35,0,11
	.byte	'FI',0,4
	.word	605
	.byte	1,1,2,35,0,11
	.byte	'reserved_31',0,4
	.word	605
	.byte	1,0,2,35,0,0,12,9,167,7,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12746
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_FPU_TRAP_PC_Bits',0,9,215,2,16,4,11
	.byte	'PC',0,4
	.word	605
	.byte	32,0,2,35,0,0,12,9,183,7,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13100
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_FPU_TRAP_OPC_Bits',0,9,205,2,16,4,11
	.byte	'OPC',0,4
	.word	605
	.byte	8,24,2,35,0,11
	.byte	'FMT',0,4
	.word	605
	.byte	1,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	605
	.byte	7,16,2,35,0,11
	.byte	'DREG',0,4
	.word	605
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	605
	.byte	12,0,2,35,0,0,12,9,175,7,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13187
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC1_Bits',0,9,221,2,16,4,11
	.byte	'SRC1',0,4
	.word	605
	.byte	32,0,2,35,0,0,12,9,191,7,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13352
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC2_Bits',0,9,227,2,16,4,11
	.byte	'SRC2',0,4
	.word	605
	.byte	32,0,2,35,0,0,12,9,199,7,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13443
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC3_Bits',0,9,233,2,16,4,11
	.byte	'SRC3',0,4
	.word	605
	.byte	32,0,2,35,0,0,12,9,207,7,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13534
	.byte	4,2,35,0,0,15,228,63
	.word	757
	.byte	16,227,63,0,10
	.byte	'_Ifx_CPU_DPR_L_Bits',0,9,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	605
	.byte	3,29,2,35,0,11
	.byte	'LOWBND',0,4
	.word	605
	.byte	29,0,2,35,0,0,12,9,239,6,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13636
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_DPR_U_Bits',0,9,250,1,16,4,11
	.byte	'reserved_0',0,4
	.word	605
	.byte	3,29,2,35,0,11
	.byte	'UPPBND',0,4
	.word	605
	.byte	29,0,2,35,0,0,12,9,247,6,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13743
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_DPR',0,9,254,9,25,8,13
	.byte	'L',0
	.word	13703
	.byte	4,2,35,0,13
	.byte	'U',0
	.word	13810
	.byte	4,2,35,4,0,15,128,1
	.word	13850
	.byte	16,15,0,14
	.word	13892
	.byte	15,128,31
	.word	757
	.byte	16,255,30,0,10
	.byte	'_Ifx_CPU_CPR_L_Bits',0,9,99,16,4,11
	.byte	'reserved_0',0,4
	.word	605
	.byte	3,29,2,35,0,11
	.byte	'LOWBND',0,4
	.word	605
	.byte	29,0,2,35,0,0,12,9,231,5,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13918
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_CPR_U_Bits',0,9,106,16,4,11
	.byte	'reserved_0',0,4
	.word	605
	.byte	3,29,2,35,0,11
	.byte	'UPPBND',0,4
	.word	605
	.byte	29,0,2,35,0,0,12,9,239,5,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14024
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_CPR',0,9,247,9,25,8,13
	.byte	'L',0
	.word	13984
	.byte	4,2,35,0,13
	.byte	'U',0
	.word	14090
	.byte	4,2,35,4,0,15,64
	.word	14130
	.byte	16,7,0,14
	.word	14172
	.byte	15,192,31
	.word	757
	.byte	16,191,31,0,10
	.byte	'_Ifx_CPU_CPXE_Bits',0,9,121,16,4,11
	.byte	'XE',0,4
	.word	605
	.byte	8,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	605
	.byte	24,0,2,35,0,0,12,9,255,5,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14197
	.byte	4,2,35,0,0,15,16
	.word	14258
	.byte	16,3,0,10
	.byte	'_Ifx_CPU_DPRE_Bits',0,9,129,2,16,4,11
	.byte	'RE',0,4
	.word	605
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	605
	.byte	16,0,2,35,0,0,12,9,255,6,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14307
	.byte	4,2,35,0,0,15,16
	.word	14370
	.byte	16,3,0,10
	.byte	'_Ifx_CPU_DPWE_Bits',0,9,136,2,16,4,11
	.byte	'WE',0,4
	.word	605
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	605
	.byte	16,0,2,35,0,0,12,9,135,7,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14419
	.byte	4,2,35,0,0,15,16
	.word	14482
	.byte	16,3,0,15,208,7
	.word	757
	.byte	16,207,7,0,10
	.byte	'_Ifx_CPU_TPS_CON_Bits',0,9,241,4,16,4,11
	.byte	'TEXP0',0,4
	.word	605
	.byte	1,31,2,35,0,11
	.byte	'TEXP1',0,4
	.word	605
	.byte	1,30,2,35,0,11
	.byte	'TEXP2',0,4
	.word	605
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	605
	.byte	13,16,2,35,0,11
	.byte	'TTRAP',0,4
	.word	605
	.byte	1,15,2,35,0,11
	.byte	'reserved_17',0,4
	.word	605
	.byte	15,0,2,35,0,0,12,9,199,9,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14542
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_TPS_TIMER_Bits',0,9,252,4,16,4,11
	.byte	'Timer',0,4
	.word	605
	.byte	32,0,2,35,0,0,12,9,207,9,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14724
	.byte	4,2,35,0,0,15,12
	.word	14772
	.byte	16,2,0,10
	.byte	'_Ifx_CPU_TPS',0,9,142,10,25,16,13
	.byte	'CON',0
	.word	14684
	.byte	4,2,35,0,13
	.byte	'TIMER',0
	.word	14812
	.byte	12,2,35,4,0,14
	.word	14821
	.byte	15,240,23
	.word	757
	.byte	16,239,23,0,10
	.byte	'_Ifx_CPU_TR_EVT_Bits',0,9,136,5,16,4,11
	.byte	'EVTA',0,4
	.word	605
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	605
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	605
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	605
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	605
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	605
	.byte	4,20,2,35,0,11
	.byte	'TYP',0,4
	.word	605
	.byte	1,19,2,35,0,11
	.byte	'RNG',0,4
	.word	605
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	605
	.byte	1,17,2,35,0,11
	.byte	'ASI_EN',0,4
	.word	605
	.byte	1,16,2,35,0,11
	.byte	'ASI',0,4
	.word	605
	.byte	5,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	605
	.byte	6,5,2,35,0,11
	.byte	'AST',0,4
	.word	605
	.byte	1,4,2,35,0,11
	.byte	'ALD',0,4
	.word	605
	.byte	1,3,2,35,0,11
	.byte	'reserved_29',0,4
	.word	605
	.byte	3,0,2,35,0,0,12,9,223,9,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14885
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_TR_ADR_Bits',0,9,130,5,16,4,11
	.byte	'ADDR',0,4
	.word	605
	.byte	32,0,2,35,0,0,12,9,215,9,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15214
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_TR',0,9,149,10,25,8,13
	.byte	'EVT',0
	.word	15174
	.byte	4,2,35,0,13
	.byte	'ADR',0
	.word	15258
	.byte	4,2,35,4,0,15,64
	.word	15298
	.byte	16,7,0,14
	.word	15343
	.byte	15,192,23
	.word	757
	.byte	16,191,23,0,10
	.byte	'_Ifx_CPU_CCTRL_Bits',0,9,72,16,4,11
	.byte	'CM',0,4
	.word	605
	.byte	1,31,2,35,0,11
	.byte	'CE',0,4
	.word	605
	.byte	1,30,2,35,0,11
	.byte	'M1',0,4
	.word	605
	.byte	3,27,2,35,0,11
	.byte	'M2',0,4
	.word	605
	.byte	3,24,2,35,0,11
	.byte	'M3',0,4
	.word	605
	.byte	3,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	605
	.byte	21,0,2,35,0,0,12,9,207,5,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15368
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_CCNT_Bits',0,9,65,16,4,11
	.byte	'CountValue',0,4
	.word	605
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	605
	.byte	1,0,2,35,0,0,12,9,199,5,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15527
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_ICNT_Bits',0,9,239,2,16,4,11
	.byte	'CountValue',0,4
	.word	605
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	605
	.byte	1,0,2,35,0,0,12,9,215,7,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15630
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_M1CNT_Bits',0,9,142,3,16,4,11
	.byte	'CountValue',0,4
	.word	605
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	605
	.byte	1,0,2,35,0,0,12,9,247,7,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15734
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_M2CNT_Bits',0,9,149,3,16,4,11
	.byte	'CountValue',0,4
	.word	605
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	605
	.byte	1,0,2,35,0,0,12,9,255,7,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15839
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_M3CNT_Bits',0,9,156,3,16,4,11
	.byte	'CountValue',0,4
	.word	605
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	605
	.byte	1,0,2,35,0,0,12,9,135,8,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15944
	.byte	4,2,35,0,0,15,232,1
	.word	757
	.byte	16,231,1,0,10
	.byte	'_Ifx_CPU_DBGSR_Bits',0,9,166,1,16,4,11
	.byte	'DE',0,4
	.word	605
	.byte	1,31,2,35,0,11
	.byte	'HALT',0,4
	.word	605
	.byte	2,29,2,35,0,11
	.byte	'SIH',0,4
	.word	605
	.byte	1,28,2,35,0,11
	.byte	'SUSP',0,4
	.word	605
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	605
	.byte	1,26,2,35,0,11
	.byte	'PREVSUSP',0,4
	.word	605
	.byte	1,25,2,35,0,11
	.byte	'PEVT',0,4
	.word	605
	.byte	1,24,2,35,0,11
	.byte	'EVTSRC',0,4
	.word	605
	.byte	5,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	605
	.byte	19,0,2,35,0,0
.L161:
	.byte	12,9,167,6,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16060
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_EXEVT_Bits',0,9,164,2,16,4,11
	.byte	'EVTA',0,4
	.word	605
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	605
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	605
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	605
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	605
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	605
	.byte	24,0,2,35,0,0,12,9,151,7,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16287
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_CREVT_Bits',0,9,128,1,16,4,11
	.byte	'EVTA',0,4
	.word	605
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	605
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	605
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	605
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	605
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	605
	.byte	24,0,2,35,0,0,12,9,135,6,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16453
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_SWEVT_Bits',0,9,212,4,16,4,11
	.byte	'EVTA',0,4
	.word	605
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	605
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	605
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	605
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	605
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	605
	.byte	24,0,2,35,0,0,12,9,175,9,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16619
	.byte	4,2,35,0,0,15,28
	.word	757
	.byte	16,27,0,10
	.byte	'_Ifx_CPU_TRIG_ACC_Bits',0,9,156,5,16,4,11
	.byte	'T0',0,4
	.word	605
	.byte	1,31,2,35,0,11
	.byte	'T1',0,4
	.word	605
	.byte	1,30,2,35,0,11
	.byte	'T2',0,4
	.word	605
	.byte	1,29,2,35,0,11
	.byte	'T3',0,4
	.word	605
	.byte	1,28,2,35,0,11
	.byte	'T4',0,4
	.word	605
	.byte	1,27,2,35,0,11
	.byte	'T5',0,4
	.word	605
	.byte	1,26,2,35,0,11
	.byte	'T6',0,4
	.word	605
	.byte	1,25,2,35,0,11
	.byte	'T7',0,4
	.word	605
	.byte	1,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	605
	.byte	24,0,2,35,0,0,12,9,231,9,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16794
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_DMS_Bits',0,9,236,1,16,4,11
	.byte	'reserved_0',0,4
	.word	605
	.byte	1,31,2,35,0,11
	.byte	'DMSValue',0,4
	.word	605
	.byte	31,0,2,35,0,0,12,9,231,6,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16998
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_DCX_Bits',0,9,202,1,16,4,11
	.byte	'reserved_0',0,4
	.word	605
	.byte	6,26,2,35,0,11
	.byte	'DCXValue',0,4
	.word	605
	.byte	26,0,2,35,0,0,12,9,199,6,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17105
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_DBGTCR_Bits',0,9,180,1,16,4,11
	.byte	'DTA',0,4
	.word	605
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	605
	.byte	31,0,2,35,0,0,12,9,175,6,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17212
	.byte	4,2,35,0,0,15,180,1
	.word	757
	.byte	16,179,1,0,10
	.byte	'_Ifx_CPU_PCXI_Bits',0,9,193,3,16,4,11
	.byte	'PCXO',0,4
	.word	605
	.byte	16,16,2,35,0,11
	.byte	'PCXS',0,4
	.word	605
	.byte	4,12,2,35,0,11
	.byte	'UL',0,4
	.word	605
	.byte	1,11,2,35,0,11
	.byte	'PIE',0,4
	.word	605
	.byte	1,10,2,35,0,11
	.byte	'PCPN',0,4
	.word	605
	.byte	10,0,2,35,0,0,12,9,175,8,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17328
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_PSW_Bits',0,9,132,4,16,4,11
	.byte	'CDC',0,4
	.word	605
	.byte	7,25,2,35,0,11
	.byte	'CDE',0,4
	.word	605
	.byte	1,24,2,35,0,11
	.byte	'GW',0,4
	.word	605
	.byte	1,23,2,35,0,11
	.byte	'IS',0,4
	.word	605
	.byte	1,22,2,35,0,11
	.byte	'IO',0,4
	.word	605
	.byte	2,20,2,35,0,11
	.byte	'PRS',0,4
	.word	605
	.byte	2,18,2,35,0,11
	.byte	'S',0,4
	.word	605
	.byte	1,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	605
	.byte	12,5,2,35,0,11
	.byte	'SAV',0,4
	.word	605
	.byte	1,4,2,35,0,11
	.byte	'AV',0,4
	.word	605
	.byte	1,3,2,35,0,11
	.byte	'SV',0,4
	.word	605
	.byte	1,2,2,35,0,11
	.byte	'V',0,4
	.word	605
	.byte	1,1,2,35,0,11
	.byte	'C',0,4
	.word	605
	.byte	1,0,2,35,0,0,12,9,231,8,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17471
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_PC_Bits',0,9,163,3,16,4,11
	.byte	'reserved_0',0,4
	.word	605
	.byte	1,31,2,35,0,11
	.byte	'PC',0,4
	.word	605
	.byte	31,0,2,35,0,0,12,9,143,8,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17728
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_SYSCON_Bits',0,9,223,4,16,4,11
	.byte	'FCDSF',0,4
	.word	605
	.byte	1,31,2,35,0,11
	.byte	'PROTEN',0,4
	.word	605
	.byte	1,30,2,35,0,11
	.byte	'TPROTEN',0,4
	.word	605
	.byte	1,29,2,35,0,11
	.byte	'IS',0,4
	.word	605
	.byte	1,28,2,35,0,11
	.byte	'IT',0,4
	.word	605
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	605
	.byte	27,0,2,35,0,0,12,9,183,9,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17828
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_CPU_ID_Bits',0,9,113,16,4,11
	.byte	'MODREV',0,4
	.word	605
	.byte	8,24,2,35,0,11
	.byte	'MOD_32B',0,4
	.word	605
	.byte	8,16,2,35,0,11
	.byte	'MOD',0,4
	.word	605
	.byte	16,0,2,35,0,0,12,9,247,5,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18000
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_CORE_ID_Bits',0,9,92,16,4,11
	.byte	'CORE_ID',0,4
	.word	605
	.byte	3,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	605
	.byte	29,0,2,35,0,0
.L168:
	.byte	12,9,223,5,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18119
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_BIV_Bits',0,9,51,16,4,11
	.byte	'VSS',0,4
	.word	605
	.byte	1,31,2,35,0,11
	.byte	'BIV',0,4
	.word	605
	.byte	31,0,2,35,0,0,12,9,183,5,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18228
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_BTV_Bits',0,9,58,16,4,11
	.byte	'reserved_0',0,4
	.word	605
	.byte	1,31,2,35,0,11
	.byte	'BTV',0,4
	.word	605
	.byte	31,0,2,35,0,0,12,9,191,5,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18322
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_ISP_Bits',0,9,128,3,16,4,11
	.byte	'ISP',0,4
	.word	605
	.byte	32,0,2,35,0,0,12,9,231,7,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18423
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_ICR_Bits',0,9,246,2,16,4,11
	.byte	'CCPN',0,4
	.word	605
	.byte	10,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	605
	.byte	5,17,2,35,0,11
	.byte	'IE',0,4
	.word	605
	.byte	1,16,2,35,0,11
	.byte	'PIPN',0,4
	.word	605
	.byte	10,6,2,35,0,11
	.byte	'reserved_26',0,4
	.word	605
	.byte	6,0,2,35,0,0,12,9,223,7,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18503
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_FCX_Bits',0,9,175,2,16,4,11
	.byte	'FCXO',0,4
	.word	605
	.byte	16,16,2,35,0,11
	.byte	'FCXS',0,4
	.word	605
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	605
	.byte	12,0,2,35,0,0,12,9,159,7,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18660
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_LCX_Bits',0,9,134,3,16,4,11
	.byte	'LCXO',0,4
	.word	605
	.byte	16,16,2,35,0,11
	.byte	'LCXS',0,4
	.word	605
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	605
	.byte	12,0,2,35,0,0,12,9,239,7,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18780
	.byte	4,2,35,0,0,15,16
	.word	757
	.byte	16,15,0,10
	.byte	'_Ifx_CPU_CUS_ID_Bits',0,9,139,1,16,4,11
	.byte	'CID',0,4
	.word	605
	.byte	3,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	605
	.byte	29,0,2,35,0,0,12,9,143,6,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18909
	.byte	4,2,35,0,0,15,172,1
	.word	757
	.byte	16,171,1,0,10
	.byte	'_Ifx_CPU_D_Bits',0,9,146,1,16,4,11
	.byte	'DATA',0,4
	.word	605
	.byte	32,0,2,35,0,0,12,9,151,6,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19025
	.byte	4,2,35,0,0,15,64
	.word	19064
	.byte	16,15,0,15,64
	.word	757
	.byte	16,63,0,10
	.byte	'_Ifx_CPU_A_Bits',0,9,45,16,4,11
	.byte	'ADDR',0,4
	.word	605
	.byte	32,0,2,35,0,0,12,9,175,5,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19122
	.byte	4,2,35,0,0,15,64
	.word	19160
	.byte	16,15,0,10
	.byte	'_Ifx_CPU',0,9,166,10,25,128,128,4,13
	.byte	'reserved_0',0
	.word	9548
	.byte	176,32,2,35,0,13
	.byte	'SEGEN',0
	.word	9659
	.byte	4,3,35,176,32,13
	.byte	'reserved_1034',0
	.word	9699
	.byte	208,223,1,3,35,180,32,13
	.byte	'TASK_ASI',0
	.word	9779
	.byte	4,4,35,132,128,2,13
	.byte	'reserved_8008',0
	.word	9819
	.byte	248,1,4,35,136,128,2,13
	.byte	'PMA0',0
	.word	9916
	.byte	4,4,35,128,130,2,13
	.byte	'PMA1',0
	.word	10042
	.byte	4,4,35,132,130,2,13
	.byte	'PMA2',0
	.word	10146
	.byte	4,4,35,136,130,2,13
	.byte	'reserved_810C',0
	.word	10186
	.byte	244,29,4,35,140,130,2,13
	.byte	'DCON2',0
	.word	10270
	.byte	4,4,35,128,160,2,13
	.byte	'reserved_9004',0
	.word	4394
	.byte	8,4,35,132,160,2,13
	.byte	'SMACON',0
	.word	10522
	.byte	4,4,35,140,160,2,13
	.byte	'DSTR',0
	.word	10859
	.byte	4,4,35,144,160,2,13
	.byte	'reserved_9014',0
	.word	2575
	.byte	4,4,35,148,160,2,13
	.byte	'DATR',0
	.word	11090
	.byte	4,4,35,152,160,2,13
	.byte	'DEADD',0
	.word	11182
	.byte	4,4,35,156,160,2,13
	.byte	'DIEAR',0
	.word	11263
	.byte	4,4,35,160,160,2,13
	.byte	'DIETR',0
	.word	11504
	.byte	4,4,35,164,160,2,13
	.byte	'reserved_9028',0
	.word	3765
	.byte	24,4,35,168,160,2,13
	.byte	'DCON0',0
	.word	11632
	.byte	4,4,35,192,160,2,13
	.byte	'reserved_9044',0
	.word	11672
	.byte	188,3,4,35,196,160,2,13
	.byte	'PSTR',0
	.word	11859
	.byte	4,4,35,128,164,2,13
	.byte	'PCON1',0
	.word	11982
	.byte	4,4,35,132,164,2,13
	.byte	'PCON2',0
	.word	12095
	.byte	4,4,35,136,164,2,13
	.byte	'PCON0',0
	.word	12223
	.byte	4,4,35,140,164,2,13
	.byte	'PIEAR',0
	.word	12304
	.byte	4,4,35,144,164,2,13
	.byte	'PIETR',0
	.word	12545
	.byte	4,4,35,148,164,2,13
	.byte	'reserved_9218',0
	.word	12585
	.byte	232,3,4,35,152,164,2,13
	.byte	'COMPAT',0
	.word	12695
	.byte	4,4,35,128,168,2,13
	.byte	'reserved_9404',0
	.word	12735
	.byte	252,23,4,35,132,168,2,13
	.byte	'FPU_TRAP_CON',0
	.word	13060
	.byte	4,4,35,128,192,2,13
	.byte	'FPU_TRAP_PC',0
	.word	13147
	.byte	4,4,35,132,192,2,13
	.byte	'FPU_TRAP_OPC',0
	.word	13312
	.byte	4,4,35,136,192,2,13
	.byte	'reserved_A00C',0
	.word	2575
	.byte	4,4,35,140,192,2,13
	.byte	'FPU_TRAP_SRC1',0
	.word	13403
	.byte	4,4,35,144,192,2,13
	.byte	'FPU_TRAP_SRC2',0
	.word	13494
	.byte	4,4,35,148,192,2,13
	.byte	'FPU_TRAP_SRC3',0
	.word	13585
	.byte	4,4,35,152,192,2,13
	.byte	'reserved_A01C',0
	.word	13625
	.byte	228,63,4,35,156,192,2,13
	.byte	'DPR',0
	.word	13902
	.byte	128,1,4,35,128,128,3,13
	.byte	'reserved_C080',0
	.word	13907
	.byte	128,31,4,35,128,129,3,13
	.byte	'CPR',0
	.word	14181
	.byte	64,4,35,128,160,3,13
	.byte	'reserved_D040',0
	.word	14186
	.byte	192,31,4,35,192,160,3,13
	.byte	'CPXE',0
	.word	14298
	.byte	16,4,35,128,192,3,13
	.byte	'DPRE',0
	.word	14410
	.byte	16,4,35,144,192,3,13
	.byte	'DPWE',0
	.word	14522
	.byte	16,4,35,160,192,3,13
	.byte	'reserved_E030',0
	.word	14531
	.byte	208,7,4,35,176,192,3,13
	.byte	'TPS',0
	.word	14869
	.byte	16,4,35,128,200,3,13
	.byte	'reserved_E410',0
	.word	14874
	.byte	240,23,4,35,144,200,3,13
	.byte	'TR',0
	.word	15352
	.byte	64,4,35,128,224,3,13
	.byte	'reserved_F040',0
	.word	15357
	.byte	192,23,4,35,192,224,3,13
	.byte	'CCTRL',0
	.word	15487
	.byte	4,4,35,128,248,3,13
	.byte	'CCNT',0
	.word	15590
	.byte	4,4,35,132,248,3,13
	.byte	'ICNT',0
	.word	15694
	.byte	4,4,35,136,248,3,13
	.byte	'M1CNT',0
	.word	15799
	.byte	4,4,35,140,248,3,13
	.byte	'M2CNT',0
	.word	15904
	.byte	4,4,35,144,248,3,13
	.byte	'M3CNT',0
	.word	16009
	.byte	4,4,35,148,248,3,13
	.byte	'reserved_FC18',0
	.word	16049
	.byte	232,1,4,35,152,248,3,13
	.byte	'DBGSR',0
	.word	16247
	.byte	4,4,35,128,250,3,13
	.byte	'reserved_FD04',0
	.word	2575
	.byte	4,4,35,132,250,3,13
	.byte	'EXEVT',0
	.word	16413
	.byte	4,4,35,136,250,3,13
	.byte	'CREVT',0
	.word	16579
	.byte	4,4,35,140,250,3,13
	.byte	'SWEVT',0
	.word	16745
	.byte	4,4,35,144,250,3,13
	.byte	'reserved_FD14',0
	.word	16785
	.byte	28,4,35,148,250,3,13
	.byte	'TRIG_ACC',0
	.word	16958
	.byte	4,4,35,176,250,3,13
	.byte	'reserved_FD34',0
	.word	4734
	.byte	12,4,35,180,250,3,13
	.byte	'DMS',0
	.word	17065
	.byte	4,4,35,192,250,3,13
	.byte	'DCX',0
	.word	17172
	.byte	4,4,35,196,250,3,13
	.byte	'DBGTCR',0
	.word	17277
	.byte	4,4,35,200,250,3,13
	.byte	'reserved_FD4C',0
	.word	17317
	.byte	180,1,4,35,204,250,3,13
	.byte	'PCXI',0
	.word	17431
	.byte	4,4,35,128,252,3,13
	.byte	'PSW',0
	.word	17688
	.byte	4,4,35,132,252,3,13
	.byte	'PC',0
	.word	17788
	.byte	4,4,35,136,252,3,13
	.byte	'reserved_FE0C',0
	.word	4394
	.byte	8,4,35,140,252,3,13
	.byte	'SYSCON',0
	.word	17960
	.byte	4,4,35,148,252,3,13
	.byte	'CPU_ID',0
	.word	18079
	.byte	4,4,35,152,252,3,13
	.byte	'CORE_ID',0
	.word	18188
	.byte	4,4,35,156,252,3,13
	.byte	'BIV',0
	.word	18282
	.byte	4,4,35,160,252,3,13
	.byte	'BTV',0
	.word	18383
	.byte	4,4,35,164,252,3,13
	.byte	'ISP',0
	.word	18463
	.byte	4,4,35,168,252,3,13
	.byte	'ICR',0
	.word	18620
	.byte	4,4,35,172,252,3,13
	.byte	'reserved_FE30',0
	.word	4394
	.byte	8,4,35,176,252,3,13
	.byte	'FCX',0
	.word	18740
	.byte	4,4,35,184,252,3,13
	.byte	'LCX',0
	.word	18860
	.byte	4,4,35,188,252,3,13
	.byte	'reserved_FE40',0
	.word	18900
	.byte	16,4,35,192,252,3,13
	.byte	'CUS_ID',0
	.word	18974
	.byte	4,4,35,208,252,3,13
	.byte	'reserved_FE54',0
	.word	19014
	.byte	172,1,4,35,212,252,3,13
	.byte	'D',0
	.word	19104
	.byte	64,4,35,128,254,3,13
	.byte	'reserved_FF40',0
	.word	19113
	.byte	64,4,35,192,254,3,13
	.byte	'A',0
	.word	19200
	.byte	64,4,35,128,255,3,13
	.byte	'reserved_FFC0',0
	.word	19113
	.byte	64,4,35,192,255,3,0,14
	.word	19209
.L158:
	.byte	3
	.word	21000
.L163:
	.byte	17,10,156,1,9,1,18
	.byte	'IfxCpu_ResourceCpu_0',0,0,18
	.byte	'IfxCpu_ResourceCpu_1',0,1,18
	.byte	'IfxCpu_ResourceCpu_none',0,2,0
.L198:
	.byte	8
	.byte	'IfxCpu_getAddress',0,3,8,232,5,21
	.word	21005
	.byte	1,1
.L200:
	.byte	5
	.byte	'cpu',0,8,232,5,58
	.word	21010
.L202:
	.byte	6,0
.L165:
	.byte	8
	.byte	'IfxCpu_getCoreIndex',0,3,8,141,6,31
	.word	21010
	.byte	1,1
.L167:
	.byte	6,0,8
	.byte	'IfxCpu_areInterruptsEnabled',0,3,8,139,5,20
	.word	757
	.byte	1,1,6,0,8
	.byte	'IfxCpu_getPerformanceCounter',0,3,8,161,6,19
	.word	376
	.byte	1,1,5
	.byte	'address',0,8,161,6,55
	.word	774
	.byte	6,0,8
	.byte	'IfxCpu_getPerformanceCounterStickyOverflow',0,3,8,190,6,20
	.word	757
	.byte	1,1,5
	.byte	'address',0,8,190,6,70
	.word	774
	.byte	6,0,4
	.byte	'IfxCpu_updatePerformanceCounter',0,3,8,172,8,17,1,1,5
	.byte	'address',0,8,172,8,56
	.word	376
	.byte	5
	.byte	'count',0,8,172,8,72
	.word	376
	.byte	19,6,0,0,14
	.word	582
	.byte	3
	.word	582
	.byte	20
	.byte	'__cmpswapw',0
	.word	21420
	.byte	1,1,1,1,21
	.byte	'p',0
	.word	21425
	.byte	21
	.byte	'value',0
	.word	582
	.byte	21
	.byte	'compare',0
	.word	582
	.byte	0,3
	.word	598
	.byte	22
	.byte	'__imaskldmst',0,1,1,1,1,23
	.word	21482
	.byte	23
	.word	598
	.byte	23
	.word	598
	.byte	23
	.word	598
	.byte	0,14
	.word	598
	.byte	20
	.byte	'__mfcr',0
	.word	21526
	.byte	1,1,1,1,23
	.word	598
	.byte	0,24
	.byte	'__nop',0,1,1,1,1,25
	.word	238
	.byte	26
	.word	264
	.byte	6,0,25
	.word	299
	.byte	26
	.word	331
	.byte	6,0,25
	.word	344
	.byte	6,0,25
	.word	397
	.byte	26
	.word	429
	.byte	26
	.word	440
	.byte	26
	.word	451
	.byte	6,0,25
	.word	501
	.byte	26
	.word	520
	.byte	6,0,25
	.word	536
	.byte	26
	.word	551
	.byte	26
	.word	565
	.byte	6,0,25
	.word	1344
	.byte	26
	.word	1384
	.byte	26
	.word	1402
	.byte	6,0,25
	.word	1422
	.byte	26
	.word	1460
	.byte	26
	.word	1478
	.byte	6,0,27
	.byte	'IfxScuWdt_clearCpuEndinit',0,3,217,1,17,1,1,1,1,5
	.byte	'password',0,3,217,1,50
	.word	774
	.byte	0,27
	.byte	'IfxScuWdt_clearSafetyEndinit',0,3,229,1,17,1,1,1,1,5
	.byte	'password',0,3,229,1,53
	.word	774
	.byte	0,27
	.byte	'IfxScuWdt_setCpuEndinit',0,3,239,1,17,1,1,1,1,5
	.byte	'password',0,3,239,1,48
	.word	774
	.byte	0,27
	.byte	'IfxScuWdt_setSafetyEndinit',0,3,249,1,17,1,1,1,1,5
	.byte	'password',0,3,249,1,51
	.word	774
	.byte	0,25
	.word	1498
	.byte	26
	.word	1549
	.byte	6,0,28
	.byte	'IfxScuWdt_getCpuWatchdogPassword',0,3,129,3,19
	.word	774
	.byte	1,1,1,1,28
	.byte	'IfxScuWdt_getSafetyWatchdogPassword',0,3,143,3,19
	.word	774
	.byte	1,1,1,1,25
	.word	9430
	.byte	26
	.word	9458
	.byte	26
	.word	9472
	.byte	26
	.word	9490
	.byte	6,0,25
	.word	9508
	.byte	6,0,28
	.byte	'IfxScuCcu_getSourceFrequency',0,7,173,7,20
	.word	290
	.byte	1,1,1,1,25
	.word	21089
	.byte	26
	.word	21119
	.byte	6,0,25
	.word	21134
	.byte	6,0
.L156:
	.byte	17,8,132,1,9,1,18
	.byte	'IfxCpu_CoreMode_halt',0,0,18
	.byte	'IfxCpu_CoreMode_run',0,1,18
	.byte	'IfxCpu_CoreMode_idle',0,2,18
	.byte	'IfxCpu_CoreMode_sleep',0,3,18
	.byte	'IfxCpu_CoreMode_stby',0,4,18
	.byte	'IfxCpu_CoreMode_unknown',0,5,0,10
	.byte	'_Ifx_SCU_PMCSR_Bits',0,4,244,6,16,4,11
	.byte	'REQSLP',0,1
	.word	757
	.byte	2,6,2,35,0,11
	.byte	'SMUSLP',0,1
	.word	757
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	757
	.byte	5,0,2,35,0,11
	.byte	'PMST',0,1
	.word	757
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	582
	.byte	21,0,2,35,0,0,12,4,239,13,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22243
	.byte	4,2,35,0,0,14
	.word	22367
.L171:
	.byte	3
	.word	22407
	.byte	25
	.word	21168
	.byte	6,0,25
	.word	21210
	.byte	26
	.word	21251
	.byte	6,0,25
	.word	21270
	.byte	26
	.word	21325
	.byte	6,0,25
	.word	21344
	.byte	26
	.word	21384
	.byte	26
	.word	21401
	.byte	19,6,0,0
.L209:
	.byte	3
	.word	582
.L212:
	.byte	14
	.word	376
.L217:
	.byte	3
	.word	582
.L223:
	.byte	14
	.word	376
.L226:
	.byte	3
	.word	376
.L252:
	.byte	3
	.word	582
	.byte	14
	.word	376
.L255:
	.byte	3
	.word	22497
	.byte	7
	.byte	'short int',0,2,5,29
	.byte	'__wchar_t',0,11,1,1
	.word	22507
	.byte	29
	.byte	'__size_t',0,11,1,1
	.word	582
	.byte	29
	.byte	'__ptrdiff_t',0,11,1,1
	.word	598
	.byte	30,1,3
	.word	22575
	.byte	29
	.byte	'__codeptr',0,11,1,1
	.word	22577
	.byte	29
	.byte	'boolean',0,12,101,29
	.word	757
	.byte	29
	.byte	'uint8',0,12,105,29
	.word	757
	.byte	29
	.byte	'uint16',0,12,109,29
	.word	774
	.byte	29
	.byte	'uint32',0,12,113,29
	.word	376
	.byte	29
	.byte	'uint64',0,12,118,29
	.word	464
	.byte	29
	.byte	'sint16',0,12,126,29
	.word	22507
	.byte	7
	.byte	'long int',0,4,5,29
	.byte	'sint32',0,12,131,1,29
	.word	22690
	.byte	7
	.byte	'long long int',0,8,5,29
	.byte	'sint64',0,12,138,1,29
	.word	22718
	.byte	29
	.byte	'float32',0,12,167,1,29
	.word	290
	.byte	29
	.byte	'pvoid',0,13,57,28
	.word	496
	.byte	29
	.byte	'Ifx_TickTime',0,13,79,28
	.word	22718
	.byte	17,13,130,1,9,1,18
	.byte	'Ifx_RxSel_a',0,0,18
	.byte	'Ifx_RxSel_b',0,1,18
	.byte	'Ifx_RxSel_c',0,2,18
	.byte	'Ifx_RxSel_d',0,3,18
	.byte	'Ifx_RxSel_e',0,4,18
	.byte	'Ifx_RxSel_f',0,5,18
	.byte	'Ifx_RxSel_g',0,6,18
	.byte	'Ifx_RxSel_h',0,7,0,29
	.byte	'Ifx_RxSel',0,13,140,1,3
	.word	22803
	.byte	14
	.word	490
	.byte	3
	.word	22941
	.byte	31,13,143,1,9,8,13
	.byte	'module',0
	.word	22946
	.byte	4,2,35,0,13
	.byte	'index',0
	.word	22690
	.byte	4,2,35,4,0,29
	.byte	'IfxModule_IndexMap',0,13,147,1,3
	.word	22951
	.byte	29
	.byte	'Ifx_CPU_A_Bits',0,9,48,3
	.word	19122
	.byte	29
	.byte	'Ifx_CPU_BIV_Bits',0,9,55,3
	.word	18228
	.byte	29
	.byte	'Ifx_CPU_BTV_Bits',0,9,62,3
	.word	18322
	.byte	29
	.byte	'Ifx_CPU_CCNT_Bits',0,9,69,3
	.word	15527
	.byte	29
	.byte	'Ifx_CPU_CCTRL_Bits',0,9,80,3
	.word	15368
	.byte	29
	.byte	'Ifx_CPU_COMPAT_Bits',0,9,89,3
	.word	12596
	.byte	29
	.byte	'Ifx_CPU_CORE_ID_Bits',0,9,96,3
	.word	18119
	.byte	29
	.byte	'Ifx_CPU_CPR_L_Bits',0,9,103,3
	.word	13918
	.byte	29
	.byte	'Ifx_CPU_CPR_U_Bits',0,9,110,3
	.word	14024
	.byte	29
	.byte	'Ifx_CPU_CPU_ID_Bits',0,9,118,3
	.word	18000
	.byte	29
	.byte	'Ifx_CPU_CPXE_Bits',0,9,125,3
	.word	14197
	.byte	29
	.byte	'Ifx_CPU_CREVT_Bits',0,9,136,1,3
	.word	16453
	.byte	29
	.byte	'Ifx_CPU_CUS_ID_Bits',0,9,143,1,3
	.word	18909
	.byte	29
	.byte	'Ifx_CPU_D_Bits',0,9,149,1,3
	.word	19025
	.byte	29
	.byte	'Ifx_CPU_DATR_Bits',0,9,163,1,3
	.word	10899
	.byte	29
	.byte	'Ifx_CPU_DBGSR_Bits',0,9,177,1,3
	.word	16060
	.byte	29
	.byte	'Ifx_CPU_DBGTCR_Bits',0,9,184,1,3
	.word	17212
	.byte	29
	.byte	'Ifx_CPU_DCON0_Bits',0,9,192,1,3
	.word	11544
	.byte	29
	.byte	'Ifx_CPU_DCON2_Bits',0,9,199,1,3
	.word	10197
	.byte	29
	.byte	'Ifx_CPU_DCX_Bits',0,9,206,1,3
	.word	17105
	.byte	29
	.byte	'Ifx_CPU_DEADD_Bits',0,9,212,1,3
	.word	11130
	.byte	29
	.byte	'Ifx_CPU_DIEAR_Bits',0,9,218,1,3
	.word	11222
	.byte	29
	.byte	'Ifx_CPU_DIETR_Bits',0,9,233,1,3
	.word	11303
	.byte	29
	.byte	'Ifx_CPU_DMS_Bits',0,9,240,1,3
	.word	16998
	.byte	29
	.byte	'Ifx_CPU_DPR_L_Bits',0,9,247,1,3
	.word	13636
	.byte	29
	.byte	'Ifx_CPU_DPR_U_Bits',0,9,254,1,3
	.word	13743
	.byte	29
	.byte	'Ifx_CPU_DPRE_Bits',0,9,133,2,3
	.word	14307
	.byte	29
	.byte	'Ifx_CPU_DPWE_Bits',0,9,140,2,3
	.word	14419
	.byte	29
	.byte	'Ifx_CPU_DSTR_Bits',0,9,161,2,3
	.word	10562
	.byte	29
	.byte	'Ifx_CPU_EXEVT_Bits',0,9,172,2,3
	.word	16287
	.byte	29
	.byte	'Ifx_CPU_FCX_Bits',0,9,180,2,3
	.word	18660
	.byte	29
	.byte	'Ifx_CPU_FPU_TRAP_CON_Bits',0,9,202,2,3
	.word	12746
	.byte	29
	.byte	'Ifx_CPU_FPU_TRAP_OPC_Bits',0,9,212,2,3
	.word	13187
	.byte	29
	.byte	'Ifx_CPU_FPU_TRAP_PC_Bits',0,9,218,2,3
	.word	13100
	.byte	29
	.byte	'Ifx_CPU_FPU_TRAP_SRC1_Bits',0,9,224,2,3
	.word	13352
	.byte	29
	.byte	'Ifx_CPU_FPU_TRAP_SRC2_Bits',0,9,230,2,3
	.word	13443
	.byte	29
	.byte	'Ifx_CPU_FPU_TRAP_SRC3_Bits',0,9,236,2,3
	.word	13534
	.byte	29
	.byte	'Ifx_CPU_ICNT_Bits',0,9,243,2,3
	.word	15630
	.byte	29
	.byte	'Ifx_CPU_ICR_Bits',0,9,253,2,3
	.word	18503
	.byte	29
	.byte	'Ifx_CPU_ISP_Bits',0,9,131,3,3
	.word	18423
	.byte	29
	.byte	'Ifx_CPU_LCX_Bits',0,9,139,3,3
	.word	18780
	.byte	29
	.byte	'Ifx_CPU_M1CNT_Bits',0,9,146,3,3
	.word	15734
	.byte	29
	.byte	'Ifx_CPU_M2CNT_Bits',0,9,153,3,3
	.word	15839
	.byte	29
	.byte	'Ifx_CPU_M3CNT_Bits',0,9,160,3,3
	.word	15944
	.byte	29
	.byte	'Ifx_CPU_PC_Bits',0,9,167,3,3
	.word	17728
	.byte	29
	.byte	'Ifx_CPU_PCON0_Bits',0,9,175,3,3
	.word	12135
	.byte	29
	.byte	'Ifx_CPU_PCON1_Bits',0,9,183,3,3
	.word	11899
	.byte	29
	.byte	'Ifx_CPU_PCON2_Bits',0,9,190,3,3
	.word	12022
	.byte	29
	.byte	'Ifx_CPU_PCXI_Bits',0,9,200,3,3
	.word	17328
	.byte	29
	.byte	'Ifx_CPU_PIEAR_Bits',0,9,206,3,3
	.word	12263
	.byte	29
	.byte	'Ifx_CPU_PIETR_Bits',0,9,221,3,3
	.word	12344
	.byte	29
	.byte	'Ifx_CPU_PMA0_Bits',0,9,229,3,3
	.word	9830
	.byte	29
	.byte	'Ifx_CPU_PMA1_Bits',0,9,237,3,3
	.word	9956
	.byte	29
	.byte	'Ifx_CPU_PMA2_Bits',0,9,244,3,3
	.word	10082
	.byte	29
	.byte	'Ifx_CPU_PSTR_Bits',0,9,129,4,3
	.word	11683
	.byte	29
	.byte	'Ifx_CPU_PSW_Bits',0,9,147,4,3
	.word	17471
	.byte	29
	.byte	'Ifx_CPU_SEGEN_Bits',0,9,156,4,3
	.word	9559
	.byte	29
	.byte	'Ifx_CPU_SMACON_Bits',0,9,171,4,3
	.word	10310
	.byte	10
	.byte	'_Ifx_CPU_SPROT_ACCENA_Bits',0,9,174,4,16,4,11
	.byte	'EN',0,4
	.word	582
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_SPROT_ACCENA_Bits',0,9,177,4,3
	.word	24640
	.byte	10
	.byte	'_Ifx_CPU_SPROT_ACCENB_Bits',0,9,180,4,16,4,11
	.byte	'reserved_0',0,4
	.word	582
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_SPROT_ACCENB_Bits',0,9,183,4,3
	.word	24723
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,9,186,4,16,4,11
	.byte	'EN',0,4
	.word	582
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,9,189,4,3
	.word	24814
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,9,192,4,16,4,11
	.byte	'reserved_0',0,4
	.word	582
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,9,195,4,3
	.word	24905
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_LA_Bits',0,9,198,4,16,4,11
	.byte	'reserved_0',0,1
	.word	757
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	582
	.byte	27,0,2,35,0,0,29
	.byte	'Ifx_CPU_SPROT_RGN_LA_Bits',0,9,202,4,3
	.word	25004
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_UA_Bits',0,9,205,4,16,4,11
	.byte	'reserved_0',0,1
	.word	757
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	582
	.byte	27,0,2,35,0,0,29
	.byte	'Ifx_CPU_SPROT_RGN_UA_Bits',0,9,209,4,3
	.word	25111
	.byte	29
	.byte	'Ifx_CPU_SWEVT_Bits',0,9,220,4,3
	.word	16619
	.byte	29
	.byte	'Ifx_CPU_SYSCON_Bits',0,9,231,4,3
	.word	17828
	.byte	29
	.byte	'Ifx_CPU_TASK_ASI_Bits',0,9,238,4,3
	.word	9712
	.byte	29
	.byte	'Ifx_CPU_TPS_CON_Bits',0,9,249,4,3
	.word	14542
	.byte	29
	.byte	'Ifx_CPU_TPS_TIMER_Bits',0,9,255,4,3
	.word	14724
	.byte	29
	.byte	'Ifx_CPU_TR_ADR_Bits',0,9,133,5,3
	.word	15214
	.byte	29
	.byte	'Ifx_CPU_TR_EVT_Bits',0,9,153,5,3
	.word	14885
	.byte	29
	.byte	'Ifx_CPU_TRIG_ACC_Bits',0,9,167,5,3
	.word	16794
	.byte	29
	.byte	'Ifx_CPU_A',0,9,180,5,3
	.word	19160
	.byte	29
	.byte	'Ifx_CPU_BIV',0,9,188,5,3
	.word	18282
	.byte	29
	.byte	'Ifx_CPU_BTV',0,9,196,5,3
	.word	18383
	.byte	29
	.byte	'Ifx_CPU_CCNT',0,9,204,5,3
	.word	15590
	.byte	29
	.byte	'Ifx_CPU_CCTRL',0,9,212,5,3
	.word	15487
	.byte	29
	.byte	'Ifx_CPU_COMPAT',0,9,220,5,3
	.word	12695
	.byte	29
	.byte	'Ifx_CPU_CORE_ID',0,9,228,5,3
	.word	18188
	.byte	29
	.byte	'Ifx_CPU_CPR_L',0,9,236,5,3
	.word	13984
	.byte	29
	.byte	'Ifx_CPU_CPR_U',0,9,244,5,3
	.word	14090
	.byte	29
	.byte	'Ifx_CPU_CPU_ID',0,9,252,5,3
	.word	18079
	.byte	29
	.byte	'Ifx_CPU_CPXE',0,9,132,6,3
	.word	14258
	.byte	29
	.byte	'Ifx_CPU_CREVT',0,9,140,6,3
	.word	16579
	.byte	29
	.byte	'Ifx_CPU_CUS_ID',0,9,148,6,3
	.word	18974
	.byte	29
	.byte	'Ifx_CPU_D',0,9,156,6,3
	.word	19064
	.byte	29
	.byte	'Ifx_CPU_DATR',0,9,164,6,3
	.word	11090
	.byte	29
	.byte	'Ifx_CPU_DBGSR',0,9,172,6,3
	.word	16247
	.byte	29
	.byte	'Ifx_CPU_DBGTCR',0,9,180,6,3
	.word	17277
	.byte	29
	.byte	'Ifx_CPU_DCON0',0,9,188,6,3
	.word	11632
	.byte	29
	.byte	'Ifx_CPU_DCON2',0,9,196,6,3
	.word	10270
	.byte	29
	.byte	'Ifx_CPU_DCX',0,9,204,6,3
	.word	17172
	.byte	29
	.byte	'Ifx_CPU_DEADD',0,9,212,6,3
	.word	11182
	.byte	29
	.byte	'Ifx_CPU_DIEAR',0,9,220,6,3
	.word	11263
	.byte	29
	.byte	'Ifx_CPU_DIETR',0,9,228,6,3
	.word	11504
	.byte	29
	.byte	'Ifx_CPU_DMS',0,9,236,6,3
	.word	17065
	.byte	29
	.byte	'Ifx_CPU_DPR_L',0,9,244,6,3
	.word	13703
	.byte	29
	.byte	'Ifx_CPU_DPR_U',0,9,252,6,3
	.word	13810
	.byte	29
	.byte	'Ifx_CPU_DPRE',0,9,132,7,3
	.word	14370
	.byte	29
	.byte	'Ifx_CPU_DPWE',0,9,140,7,3
	.word	14482
	.byte	29
	.byte	'Ifx_CPU_DSTR',0,9,148,7,3
	.word	10859
	.byte	29
	.byte	'Ifx_CPU_EXEVT',0,9,156,7,3
	.word	16413
	.byte	29
	.byte	'Ifx_CPU_FCX',0,9,164,7,3
	.word	18740
	.byte	29
	.byte	'Ifx_CPU_FPU_TRAP_CON',0,9,172,7,3
	.word	13060
	.byte	29
	.byte	'Ifx_CPU_FPU_TRAP_OPC',0,9,180,7,3
	.word	13312
	.byte	29
	.byte	'Ifx_CPU_FPU_TRAP_PC',0,9,188,7,3
	.word	13147
	.byte	29
	.byte	'Ifx_CPU_FPU_TRAP_SRC1',0,9,196,7,3
	.word	13403
	.byte	29
	.byte	'Ifx_CPU_FPU_TRAP_SRC2',0,9,204,7,3
	.word	13494
	.byte	29
	.byte	'Ifx_CPU_FPU_TRAP_SRC3',0,9,212,7,3
	.word	13585
	.byte	29
	.byte	'Ifx_CPU_ICNT',0,9,220,7,3
	.word	15694
	.byte	29
	.byte	'Ifx_CPU_ICR',0,9,228,7,3
	.word	18620
	.byte	29
	.byte	'Ifx_CPU_ISP',0,9,236,7,3
	.word	18463
	.byte	29
	.byte	'Ifx_CPU_LCX',0,9,244,7,3
	.word	18860
	.byte	29
	.byte	'Ifx_CPU_M1CNT',0,9,252,7,3
	.word	15799
	.byte	29
	.byte	'Ifx_CPU_M2CNT',0,9,132,8,3
	.word	15904
	.byte	29
	.byte	'Ifx_CPU_M3CNT',0,9,140,8,3
	.word	16009
	.byte	29
	.byte	'Ifx_CPU_PC',0,9,148,8,3
	.word	17788
	.byte	29
	.byte	'Ifx_CPU_PCON0',0,9,156,8,3
	.word	12223
	.byte	29
	.byte	'Ifx_CPU_PCON1',0,9,164,8,3
	.word	11982
	.byte	29
	.byte	'Ifx_CPU_PCON2',0,9,172,8,3
	.word	12095
	.byte	29
	.byte	'Ifx_CPU_PCXI',0,9,180,8,3
	.word	17431
	.byte	29
	.byte	'Ifx_CPU_PIEAR',0,9,188,8,3
	.word	12304
	.byte	29
	.byte	'Ifx_CPU_PIETR',0,9,196,8,3
	.word	12545
	.byte	29
	.byte	'Ifx_CPU_PMA0',0,9,204,8,3
	.word	9916
	.byte	29
	.byte	'Ifx_CPU_PMA1',0,9,212,8,3
	.word	10042
	.byte	29
	.byte	'Ifx_CPU_PMA2',0,9,220,8,3
	.word	10146
	.byte	29
	.byte	'Ifx_CPU_PSTR',0,9,228,8,3
	.word	11859
	.byte	29
	.byte	'Ifx_CPU_PSW',0,9,236,8,3
	.word	17688
	.byte	29
	.byte	'Ifx_CPU_SEGEN',0,9,244,8,3
	.word	9659
	.byte	29
	.byte	'Ifx_CPU_SMACON',0,9,252,8,3
	.word	10522
	.byte	12,9,255,8,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24640
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_SPROT_ACCENA',0,9,132,9,3
	.word	26801
	.byte	12,9,135,9,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24723
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_SPROT_ACCENB',0,9,140,9,3
	.word	26871
	.byte	12,9,143,9,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24814
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA',0,9,148,9,3
	.word	26941
	.byte	12,9,151,9,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24905
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB',0,9,156,9,3
	.word	27015
	.byte	12,9,159,9,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25004
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_SPROT_RGN_LA',0,9,164,9,3
	.word	27089
	.byte	12,9,167,9,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25111
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_SPROT_RGN_UA',0,9,172,9,3
	.word	27159
	.byte	29
	.byte	'Ifx_CPU_SWEVT',0,9,180,9,3
	.word	16745
	.byte	29
	.byte	'Ifx_CPU_SYSCON',0,9,188,9,3
	.word	17960
	.byte	29
	.byte	'Ifx_CPU_TASK_ASI',0,9,196,9,3
	.word	9779
	.byte	29
	.byte	'Ifx_CPU_TPS_CON',0,9,204,9,3
	.word	14684
	.byte	29
	.byte	'Ifx_CPU_TPS_TIMER',0,9,212,9,3
	.word	14772
	.byte	29
	.byte	'Ifx_CPU_TR_ADR',0,9,220,9,3
	.word	15258
	.byte	29
	.byte	'Ifx_CPU_TR_EVT',0,9,228,9,3
	.word	15174
	.byte	29
	.byte	'Ifx_CPU_TRIG_ACC',0,9,236,9,3
	.word	16958
	.byte	14
	.word	14130
	.byte	29
	.byte	'Ifx_CPU_CPR',0,9,251,9,3
	.word	27428
	.byte	14
	.word	13850
	.byte	29
	.byte	'Ifx_CPU_DPR',0,9,130,10,3
	.word	27454
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN',0,9,133,10,25,16,13
	.byte	'LA',0
	.word	27089
	.byte	4,2,35,0,13
	.byte	'UA',0
	.word	27159
	.byte	4,2,35,4,13
	.byte	'ACCENA',0
	.word	26941
	.byte	4,2,35,8,13
	.byte	'ACCENB',0
	.word	27015
	.byte	4,2,35,12,0,14
	.word	27480
	.byte	29
	.byte	'Ifx_CPU_SPROT_RGN',0,9,139,10,3
	.word	27562
	.byte	14
	.word	14821
	.byte	29
	.byte	'Ifx_CPU_TPS',0,9,146,10,3
	.word	27594
	.byte	14
	.word	15298
	.byte	29
	.byte	'Ifx_CPU_TR',0,9,153,10,3
	.word	27620
	.byte	14
	.word	19209
	.byte	29
	.byte	'Ifx_CPU',0,9,130,11,3
	.word	27645
	.byte	17,10,127,9,1,18
	.byte	'IfxCpu_Id_0',0,0,18
	.byte	'IfxCpu_Id_1',0,1,18
	.byte	'IfxCpu_Id_none',0,2,0,29
	.byte	'IfxCpu_Id',0,10,132,1,3
	.word	27667
	.byte	29
	.byte	'IfxCpu_ResourceCpu',0,10,161,1,3
	.word	21010
	.byte	15,16
	.word	22951
	.byte	16,1,0,32
	.word	27765
	.byte	33
	.byte	'IfxCpu_cfg_indexMap',0,10,167,1,41
	.word	27774
	.byte	1,1,10
	.byte	'_Ifx_SRC_SRCR_Bits',0,14,45,16,4,11
	.byte	'SRPN',0,1
	.word	757
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	757
	.byte	2,6,2,35,1,11
	.byte	'SRE',0,1
	.word	757
	.byte	1,5,2,35,1,11
	.byte	'TOS',0,1
	.word	757
	.byte	2,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	757
	.byte	3,0,2,35,1,11
	.byte	'ECC',0,1
	.word	757
	.byte	6,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	757
	.byte	2,0,2,35,2,11
	.byte	'SRR',0,1
	.word	757
	.byte	1,7,2,35,3,11
	.byte	'CLRR',0,1
	.word	757
	.byte	1,6,2,35,3,11
	.byte	'SETR',0,1
	.word	757
	.byte	1,5,2,35,3,11
	.byte	'IOV',0,1
	.word	757
	.byte	1,4,2,35,3,11
	.byte	'IOVCLR',0,1
	.word	757
	.byte	1,3,2,35,3,11
	.byte	'SWS',0,1
	.word	757
	.byte	1,2,2,35,3,11
	.byte	'SWSCLR',0,1
	.word	757
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	757
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SRC_SRCR_Bits',0,14,62,3
	.word	27810
	.byte	12,14,70,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27810
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SRC_SRCR',0,14,75,3
	.word	28126
	.byte	10
	.byte	'_Ifx_SRC_AGBT',0,14,86,25,4,13
	.byte	'SR',0
	.word	28126
	.byte	4,2,35,0,0,14
	.word	28186
	.byte	29
	.byte	'Ifx_SRC_AGBT',0,14,89,3
	.word	28218
	.byte	10
	.byte	'_Ifx_SRC_ASCLIN',0,14,92,25,12,13
	.byte	'TX',0
	.word	28126
	.byte	4,2,35,0,13
	.byte	'RX',0
	.word	28126
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	28126
	.byte	4,2,35,8,0,14
	.word	28244
	.byte	29
	.byte	'Ifx_SRC_ASCLIN',0,14,97,3
	.word	28303
	.byte	10
	.byte	'_Ifx_SRC_BCUSPB',0,14,100,25,4,13
	.byte	'SBSRC',0
	.word	28126
	.byte	4,2,35,0,0,14
	.word	28331
	.byte	29
	.byte	'Ifx_SRC_BCUSPB',0,14,103,3
	.word	28368
	.byte	15,64
	.word	28126
	.byte	16,15,0,10
	.byte	'_Ifx_SRC_CAN',0,14,106,25,64,13
	.byte	'INT',0
	.word	28396
	.byte	64,2,35,0,0,14
	.word	28405
	.byte	29
	.byte	'Ifx_SRC_CAN',0,14,109,3
	.word	28437
	.byte	10
	.byte	'_Ifx_SRC_CCU6',0,14,112,25,16,13
	.byte	'SR0',0
	.word	28126
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	28126
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	28126
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	28126
	.byte	4,2,35,12,0,14
	.word	28462
	.byte	29
	.byte	'Ifx_SRC_CCU6',0,14,118,3
	.word	28534
	.byte	15,8
	.word	28126
	.byte	16,1,0,10
	.byte	'_Ifx_SRC_CERBERUS',0,14,121,25,8,13
	.byte	'SR',0
	.word	28560
	.byte	8,2,35,0,0,14
	.word	28569
	.byte	29
	.byte	'Ifx_SRC_CERBERUS',0,14,124,3
	.word	28605
	.byte	10
	.byte	'_Ifx_SRC_CIF',0,14,127,25,16,13
	.byte	'MI',0
	.word	28126
	.byte	4,2,35,0,13
	.byte	'MIEP',0
	.word	28126
	.byte	4,2,35,4,13
	.byte	'ISP',0
	.word	28126
	.byte	4,2,35,8,13
	.byte	'MJPEG',0
	.word	28126
	.byte	4,2,35,12,0,14
	.word	28635
	.byte	29
	.byte	'Ifx_SRC_CIF',0,14,133,1,3
	.word	28708
	.byte	10
	.byte	'_Ifx_SRC_CPU',0,14,136,1,25,4,13
	.byte	'SBSRC',0
	.word	28126
	.byte	4,2,35,0,0,14
	.word	28734
	.byte	29
	.byte	'Ifx_SRC_CPU',0,14,139,1,3
	.word	28769
	.byte	15,192,1
	.word	28126
	.byte	16,47,0,10
	.byte	'_Ifx_SRC_DMA',0,14,142,1,25,208,1,13
	.byte	'ERR',0
	.word	28126
	.byte	4,2,35,0,13
	.byte	'reserved_4',0
	.word	4734
	.byte	12,2,35,4,13
	.byte	'CH',0
	.word	28795
	.byte	192,1,2,35,16,0,14
	.word	28805
	.byte	29
	.byte	'Ifx_SRC_DMA',0,14,147,1,3
	.word	28872
	.byte	10
	.byte	'_Ifx_SRC_DSADC',0,14,150,1,25,8,13
	.byte	'SRM',0
	.word	28126
	.byte	4,2,35,0,13
	.byte	'SRA',0
	.word	28126
	.byte	4,2,35,4,0,14
	.word	28898
	.byte	29
	.byte	'Ifx_SRC_DSADC',0,14,154,1,3
	.word	28946
	.byte	10
	.byte	'_Ifx_SRC_EMEM',0,14,157,1,25,4,13
	.byte	'SR',0
	.word	28126
	.byte	4,2,35,0,0,14
	.word	28974
	.byte	29
	.byte	'Ifx_SRC_EMEM',0,14,160,1,3
	.word	29007
	.byte	15,40
	.word	757
	.byte	16,39,0,10
	.byte	'_Ifx_SRC_ERAY',0,14,163,1,25,80,13
	.byte	'INT',0
	.word	28560
	.byte	8,2,35,0,13
	.byte	'TINT',0
	.word	28560
	.byte	8,2,35,8,13
	.byte	'NDAT',0
	.word	28560
	.byte	8,2,35,16,13
	.byte	'MBSC',0
	.word	28560
	.byte	8,2,35,24,13
	.byte	'OBUSY',0
	.word	28126
	.byte	4,2,35,32,13
	.byte	'IBUSY',0
	.word	28126
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	29034
	.byte	40,2,35,40,0,14
	.word	29043
	.byte	29
	.byte	'Ifx_SRC_ERAY',0,14,172,1,3
	.word	29170
	.byte	10
	.byte	'_Ifx_SRC_ETH',0,14,175,1,25,4,13
	.byte	'SR',0
	.word	28126
	.byte	4,2,35,0,0,14
	.word	29197
	.byte	29
	.byte	'Ifx_SRC_ETH',0,14,178,1,3
	.word	29229
	.byte	10
	.byte	'_Ifx_SRC_FCE',0,14,181,1,25,4,13
	.byte	'SR',0
	.word	28126
	.byte	4,2,35,0,0,14
	.word	29255
	.byte	29
	.byte	'Ifx_SRC_FCE',0,14,184,1,3
	.word	29287
	.byte	10
	.byte	'_Ifx_SRC_FFT',0,14,187,1,25,12,13
	.byte	'DONE',0
	.word	28126
	.byte	4,2,35,0,13
	.byte	'ERR',0
	.word	28126
	.byte	4,2,35,4,13
	.byte	'RFS',0
	.word	28126
	.byte	4,2,35,8,0,14
	.word	29313
	.byte	29
	.byte	'Ifx_SRC_FFT',0,14,192,1,3
	.word	29373
	.byte	10
	.byte	'_Ifx_SRC_GPSR',0,14,195,1,25,32,13
	.byte	'SR0',0
	.word	28126
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	28126
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	28126
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	28126
	.byte	4,2,35,12,13
	.byte	'reserved_10',0
	.word	18900
	.byte	16,2,35,16,0,14
	.word	29399
	.byte	29
	.byte	'Ifx_SRC_GPSR',0,14,202,1,3
	.word	29493
	.byte	10
	.byte	'_Ifx_SRC_GPT12',0,14,205,1,25,48,13
	.byte	'CIRQ',0
	.word	28126
	.byte	4,2,35,0,13
	.byte	'T2',0
	.word	28126
	.byte	4,2,35,4,13
	.byte	'T3',0
	.word	28126
	.byte	4,2,35,8,13
	.byte	'T4',0
	.word	28126
	.byte	4,2,35,12,13
	.byte	'T5',0
	.word	28126
	.byte	4,2,35,16,13
	.byte	'T6',0
	.word	28126
	.byte	4,2,35,20,13
	.byte	'reserved_18',0
	.word	3765
	.byte	24,2,35,24,0,14
	.word	29520
	.byte	29
	.byte	'Ifx_SRC_GPT12',0,14,214,1,3
	.word	29637
	.byte	15,12
	.word	28126
	.byte	16,2,0,15,32
	.word	28126
	.byte	16,7,0,15,32
	.word	29674
	.byte	16,0,0,15,88
	.word	757
	.byte	16,87,0,15,108
	.word	28126
	.byte	16,26,0,15,96
	.word	757
	.byte	16,95,0,15,96
	.word	29674
	.byte	16,2,0,15,160,3
	.word	757
	.byte	16,159,3,0,15,64
	.word	29674
	.byte	16,1,0,15,192,3
	.word	757
	.byte	16,191,3,0,15,16
	.word	28126
	.byte	16,3,0,15,64
	.word	29759
	.byte	16,3,0,15,192,2
	.word	757
	.byte	16,191,2,0,15,52
	.word	757
	.byte	16,51,0,10
	.byte	'_Ifx_SRC_GTM',0,14,217,1,25,204,18,13
	.byte	'AEIIRQ',0
	.word	28126
	.byte	4,2,35,0,13
	.byte	'ARUIRQ',0
	.word	29665
	.byte	12,2,35,4,13
	.byte	'reserved_10',0
	.word	2575
	.byte	4,2,35,16,13
	.byte	'BRCIRQ',0
	.word	28126
	.byte	4,2,35,20,13
	.byte	'CMPIRQ',0
	.word	28126
	.byte	4,2,35,24,13
	.byte	'SPEIRQ',0
	.word	28560
	.byte	8,2,35,28,13
	.byte	'reserved_24',0
	.word	4394
	.byte	8,2,35,36,13
	.byte	'PSM',0
	.word	29683
	.byte	32,2,35,44,13
	.byte	'reserved_4C',0
	.word	29692
	.byte	88,2,35,76,13
	.byte	'DPLL',0
	.word	29701
	.byte	108,3,35,164,1,13
	.byte	'reserved_110',0
	.word	29710
	.byte	96,3,35,144,2,13
	.byte	'ERR',0
	.word	28126
	.byte	4,3,35,240,2,13
	.byte	'reserved_174',0
	.word	4734
	.byte	12,3,35,244,2,13
	.byte	'TIM',0
	.word	29719
	.byte	96,3,35,128,3,13
	.byte	'reserved_1E0',0
	.word	29728
	.byte	160,3,3,35,224,3,13
	.byte	'MCS',0
	.word	29719
	.byte	96,3,35,128,7,13
	.byte	'reserved_3E0',0
	.word	29728
	.byte	160,3,3,35,224,7,13
	.byte	'TOM',0
	.word	29739
	.byte	64,3,35,128,11,13
	.byte	'reserved_5C0',0
	.word	29748
	.byte	192,3,3,35,192,11,13
	.byte	'ATOM',0
	.word	29768
	.byte	64,3,35,128,15,13
	.byte	'reserved_7C0',0
	.word	29777
	.byte	192,2,3,35,192,15,13
	.byte	'MCSW0',0
	.word	29665
	.byte	12,3,35,128,18,13
	.byte	'reserved_90C',0
	.word	29788
	.byte	52,3,35,140,18,13
	.byte	'MCSW1',0
	.word	29665
	.byte	12,3,35,192,18,0,14
	.word	29797
	.byte	29
	.byte	'Ifx_SRC_GTM',0,14,243,1,3
	.word	30257
	.byte	10
	.byte	'_Ifx_SRC_HSCT',0,14,246,1,25,4,13
	.byte	'SR',0
	.word	28126
	.byte	4,2,35,0,0,14
	.word	30283
	.byte	29
	.byte	'Ifx_SRC_HSCT',0,14,249,1,3
	.word	30316
	.byte	10
	.byte	'_Ifx_SRC_HSSL',0,14,252,1,25,16,13
	.byte	'COK',0
	.word	28126
	.byte	4,2,35,0,13
	.byte	'RDI',0
	.word	28126
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	28126
	.byte	4,2,35,8,13
	.byte	'TRG',0
	.word	28126
	.byte	4,2,35,12,0,14
	.word	30343
	.byte	29
	.byte	'Ifx_SRC_HSSL',0,14,130,2,3
	.word	30416
	.byte	15,56
	.word	757
	.byte	16,55,0,10
	.byte	'_Ifx_SRC_I2C',0,14,133,2,25,80,13
	.byte	'BREQ',0
	.word	28126
	.byte	4,2,35,0,13
	.byte	'LBREQ',0
	.word	28126
	.byte	4,2,35,4,13
	.byte	'SREQ',0
	.word	28126
	.byte	4,2,35,8,13
	.byte	'LSREQ',0
	.word	28126
	.byte	4,2,35,12,13
	.byte	'ERR',0
	.word	28126
	.byte	4,2,35,16,13
	.byte	'P',0
	.word	28126
	.byte	4,2,35,20,13
	.byte	'reserved_18',0
	.word	30443
	.byte	56,2,35,24,0,14
	.word	30452
	.byte	29
	.byte	'Ifx_SRC_I2C',0,14,142,2,3
	.word	30575
	.byte	10
	.byte	'_Ifx_SRC_LMU',0,14,145,2,25,4,13
	.byte	'SR',0
	.word	28126
	.byte	4,2,35,0,0,14
	.word	30601
	.byte	29
	.byte	'Ifx_SRC_LMU',0,14,148,2,3
	.word	30633
	.byte	10
	.byte	'_Ifx_SRC_MSC',0,14,151,2,25,20,13
	.byte	'SR0',0
	.word	28126
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	28126
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	28126
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	28126
	.byte	4,2,35,12,13
	.byte	'SR4',0
	.word	28126
	.byte	4,2,35,16,0,14
	.word	30659
	.byte	29
	.byte	'Ifx_SRC_MSC',0,14,158,2,3
	.word	30744
	.byte	10
	.byte	'_Ifx_SRC_PMU',0,14,161,2,25,4,13
	.byte	'SR',0
	.word	28126
	.byte	4,2,35,0,0,14
	.word	30770
	.byte	29
	.byte	'Ifx_SRC_PMU',0,14,164,2,3
	.word	30802
	.byte	10
	.byte	'_Ifx_SRC_PSI5',0,14,167,2,25,32,13
	.byte	'SR',0
	.word	29674
	.byte	32,2,35,0,0,14
	.word	30828
	.byte	29
	.byte	'Ifx_SRC_PSI5',0,14,170,2,3
	.word	30861
	.byte	10
	.byte	'_Ifx_SRC_PSI5S',0,14,173,2,25,32,13
	.byte	'SR',0
	.word	29674
	.byte	32,2,35,0,0,14
	.word	30888
	.byte	29
	.byte	'Ifx_SRC_PSI5S',0,14,176,2,3
	.word	30922
	.byte	10
	.byte	'_Ifx_SRC_QSPI',0,14,179,2,25,24,13
	.byte	'TX',0
	.word	28126
	.byte	4,2,35,0,13
	.byte	'RX',0
	.word	28126
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	28126
	.byte	4,2,35,8,13
	.byte	'PT',0
	.word	28126
	.byte	4,2,35,12,13
	.byte	'HC',0
	.word	28126
	.byte	4,2,35,16,13
	.byte	'U',0
	.word	28126
	.byte	4,2,35,20,0,14
	.word	30950
	.byte	29
	.byte	'Ifx_SRC_QSPI',0,14,187,2,3
	.word	31043
	.byte	10
	.byte	'_Ifx_SRC_SCR',0,14,190,2,25,4,13
	.byte	'SR',0
	.word	28126
	.byte	4,2,35,0,0,14
	.word	31070
	.byte	29
	.byte	'Ifx_SRC_SCR',0,14,193,2,3
	.word	31102
	.byte	10
	.byte	'_Ifx_SRC_SCU',0,14,196,2,25,20,13
	.byte	'DTS',0
	.word	28126
	.byte	4,2,35,0,13
	.byte	'ERU',0
	.word	29759
	.byte	16,2,35,4,0,14
	.word	31128
	.byte	29
	.byte	'Ifx_SRC_SCU',0,14,200,2,3
	.word	31174
	.byte	15,24
	.word	28126
	.byte	16,5,0,10
	.byte	'_Ifx_SRC_SENT',0,14,203,2,25,24,13
	.byte	'SR',0
	.word	31200
	.byte	24,2,35,0,0,14
	.word	31209
	.byte	29
	.byte	'Ifx_SRC_SENT',0,14,206,2,3
	.word	31242
	.byte	10
	.byte	'_Ifx_SRC_SMU',0,14,209,2,25,12,13
	.byte	'SR',0
	.word	29665
	.byte	12,2,35,0,0,14
	.word	31269
	.byte	29
	.byte	'Ifx_SRC_SMU',0,14,212,2,3
	.word	31301
	.byte	10
	.byte	'_Ifx_SRC_STM',0,14,215,2,25,8,13
	.byte	'SR0',0
	.word	28126
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	28126
	.byte	4,2,35,4,0,14
	.word	31327
	.byte	29
	.byte	'Ifx_SRC_STM',0,14,219,2,3
	.word	31373
	.byte	10
	.byte	'_Ifx_SRC_VADCCG',0,14,222,2,25,16,13
	.byte	'SR0',0
	.word	28126
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	28126
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	28126
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	28126
	.byte	4,2,35,12,0,14
	.word	31399
	.byte	29
	.byte	'Ifx_SRC_VADCCG',0,14,228,2,3
	.word	31474
	.byte	10
	.byte	'_Ifx_SRC_VADCG',0,14,231,2,25,16,13
	.byte	'SR0',0
	.word	28126
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	28126
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	28126
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	28126
	.byte	4,2,35,12,0,14
	.word	31503
	.byte	29
	.byte	'Ifx_SRC_VADCG',0,14,237,2,3
	.word	31577
	.byte	10
	.byte	'_Ifx_SRC_XBAR',0,14,240,2,25,4,13
	.byte	'SRC',0
	.word	28126
	.byte	4,2,35,0,0,14
	.word	31605
	.byte	29
	.byte	'Ifx_SRC_XBAR',0,14,243,2,3
	.word	31639
	.byte	15,4
	.word	28186
	.byte	16,0,0,14
	.word	31666
	.byte	10
	.byte	'_Ifx_SRC_GAGBT',0,14,128,3,25,4,13
	.byte	'AGBT',0
	.word	31675
	.byte	4,2,35,0,0,14
	.word	31680
	.byte	29
	.byte	'Ifx_SRC_GAGBT',0,14,131,3,3
	.word	31716
	.byte	15,48
	.word	28244
	.byte	16,3,0,14
	.word	31744
	.byte	10
	.byte	'_Ifx_SRC_GASCLIN',0,14,134,3,25,48,13
	.byte	'ASCLIN',0
	.word	31753
	.byte	48,2,35,0,0,14
	.word	31758
	.byte	29
	.byte	'Ifx_SRC_GASCLIN',0,14,137,3,3
	.word	31798
	.byte	14
	.word	28331
	.byte	10
	.byte	'_Ifx_SRC_GBCU',0,14,140,3,25,4,13
	.byte	'SPB',0
	.word	31828
	.byte	4,2,35,0,0,14
	.word	31833
	.byte	29
	.byte	'Ifx_SRC_GBCU',0,14,143,3,3
	.word	31867
	.byte	15,64
	.word	28405
	.byte	16,0,0,14
	.word	31894
	.byte	10
	.byte	'_Ifx_SRC_GCAN',0,14,146,3,25,64,13
	.byte	'CAN',0
	.word	31903
	.byte	64,2,35,0,0,14
	.word	31908
	.byte	29
	.byte	'Ifx_SRC_GCAN',0,14,149,3,3
	.word	31942
	.byte	15,32
	.word	28462
	.byte	16,1,0,14
	.word	31969
	.byte	10
	.byte	'_Ifx_SRC_GCCU6',0,14,152,3,25,32,13
	.byte	'CCU6',0
	.word	31978
	.byte	32,2,35,0,0,14
	.word	31983
	.byte	29
	.byte	'Ifx_SRC_GCCU6',0,14,155,3,3
	.word	32019
	.byte	14
	.word	28569
	.byte	10
	.byte	'_Ifx_SRC_GCERBERUS',0,14,158,3,25,8,13
	.byte	'CERBERUS',0
	.word	32047
	.byte	8,2,35,0,0,14
	.word	32052
	.byte	29
	.byte	'Ifx_SRC_GCERBERUS',0,14,161,3,3
	.word	32096
	.byte	15,16
	.word	28635
	.byte	16,0,0,14
	.word	32128
	.byte	10
	.byte	'_Ifx_SRC_GCIF',0,14,164,3,25,16,13
	.byte	'CIF',0
	.word	32137
	.byte	16,2,35,0,0,14
	.word	32142
	.byte	29
	.byte	'Ifx_SRC_GCIF',0,14,167,3,3
	.word	32176
	.byte	15,8
	.word	28734
	.byte	16,1,0,14
	.word	32203
	.byte	10
	.byte	'_Ifx_SRC_GCPU',0,14,170,3,25,8,13
	.byte	'CPU',0
	.word	32212
	.byte	8,2,35,0,0,14
	.word	32217
	.byte	29
	.byte	'Ifx_SRC_GCPU',0,14,173,3,3
	.word	32251
	.byte	15,208,1
	.word	28805
	.byte	16,0,0,14
	.word	32278
	.byte	10
	.byte	'_Ifx_SRC_GDMA',0,14,176,3,25,208,1,13
	.byte	'DMA',0
	.word	32288
	.byte	208,1,2,35,0,0,14
	.word	32293
	.byte	29
	.byte	'Ifx_SRC_GDMA',0,14,179,3,3
	.word	32329
	.byte	14
	.word	28898
	.byte	14
	.word	28898
	.byte	14
	.word	28898
	.byte	10
	.byte	'_Ifx_SRC_GDSADC',0,14,182,3,25,32,13
	.byte	'DSADC0',0
	.word	32356
	.byte	8,2,35,0,13
	.byte	'reserved_8',0
	.word	4394
	.byte	8,2,35,8,13
	.byte	'DSADC2',0
	.word	32361
	.byte	8,2,35,16,13
	.byte	'DSADC3',0
	.word	32366
	.byte	8,2,35,24,0,14
	.word	32371
	.byte	29
	.byte	'Ifx_SRC_GDSADC',0,14,188,3,3
	.word	32462
	.byte	15,4
	.word	28974
	.byte	16,0,0,14
	.word	32491
	.byte	10
	.byte	'_Ifx_SRC_GEMEM',0,14,191,3,25,4,13
	.byte	'EMEM',0
	.word	32500
	.byte	4,2,35,0,0,14
	.word	32505
	.byte	29
	.byte	'Ifx_SRC_GEMEM',0,14,194,3,3
	.word	32541
	.byte	15,80
	.word	29043
	.byte	16,0,0,14
	.word	32569
	.byte	10
	.byte	'_Ifx_SRC_GERAY',0,14,197,3,25,80,13
	.byte	'ERAY',0
	.word	32578
	.byte	80,2,35,0,0,14
	.word	32583
	.byte	29
	.byte	'Ifx_SRC_GERAY',0,14,200,3,3
	.word	32619
	.byte	15,4
	.word	29197
	.byte	16,0,0,14
	.word	32647
	.byte	10
	.byte	'_Ifx_SRC_GETH',0,14,203,3,25,4,13
	.byte	'ETH',0
	.word	32656
	.byte	4,2,35,0,0,14
	.word	32661
	.byte	29
	.byte	'Ifx_SRC_GETH',0,14,206,3,3
	.word	32695
	.byte	15,4
	.word	29255
	.byte	16,0,0,14
	.word	32722
	.byte	10
	.byte	'_Ifx_SRC_GFCE',0,14,209,3,25,4,13
	.byte	'FCE',0
	.word	32731
	.byte	4,2,35,0,0,14
	.word	32736
	.byte	29
	.byte	'Ifx_SRC_GFCE',0,14,212,3,3
	.word	32770
	.byte	15,12
	.word	29313
	.byte	16,0,0,14
	.word	32797
	.byte	10
	.byte	'_Ifx_SRC_GFFT',0,14,215,3,25,12,13
	.byte	'FFT',0
	.word	32806
	.byte	12,2,35,0,0,14
	.word	32811
	.byte	29
	.byte	'Ifx_SRC_GFFT',0,14,218,3,3
	.word	32845
	.byte	15,64
	.word	29399
	.byte	16,1,0,14
	.word	32872
	.byte	10
	.byte	'_Ifx_SRC_GGPSR',0,14,221,3,25,64,13
	.byte	'GPSR',0
	.word	32881
	.byte	64,2,35,0,0,14
	.word	32886
	.byte	29
	.byte	'Ifx_SRC_GGPSR',0,14,224,3,3
	.word	32922
	.byte	15,48
	.word	29520
	.byte	16,0,0,14
	.word	32950
	.byte	10
	.byte	'_Ifx_SRC_GGPT12',0,14,227,3,25,48,13
	.byte	'GPT12',0
	.word	32959
	.byte	48,2,35,0,0,14
	.word	32964
	.byte	29
	.byte	'Ifx_SRC_GGPT12',0,14,230,3,3
	.word	33002
	.byte	15,204,18
	.word	29797
	.byte	16,0,0,14
	.word	33031
	.byte	10
	.byte	'_Ifx_SRC_GGTM',0,14,233,3,25,204,18,13
	.byte	'GTM',0
	.word	33041
	.byte	204,18,2,35,0,0,14
	.word	33046
	.byte	29
	.byte	'Ifx_SRC_GGTM',0,14,236,3,3
	.word	33082
	.byte	15,4
	.word	30283
	.byte	16,0,0,14
	.word	33109
	.byte	10
	.byte	'_Ifx_SRC_GHSCT',0,14,239,3,25,4,13
	.byte	'HSCT',0
	.word	33118
	.byte	4,2,35,0,0,14
	.word	33123
	.byte	29
	.byte	'Ifx_SRC_GHSCT',0,14,242,3,3
	.word	33159
	.byte	15,64
	.word	30343
	.byte	16,3,0,14
	.word	33187
	.byte	10
	.byte	'_Ifx_SRC_GHSSL',0,14,245,3,25,68,13
	.byte	'HSSL',0
	.word	33196
	.byte	64,2,35,0,13
	.byte	'EXI',0
	.word	28126
	.byte	4,2,35,64,0,14
	.word	33201
	.byte	29
	.byte	'Ifx_SRC_GHSSL',0,14,249,3,3
	.word	33250
	.byte	15,80
	.word	30452
	.byte	16,0,0,14
	.word	33278
	.byte	10
	.byte	'_Ifx_SRC_GI2C',0,14,252,3,25,80,13
	.byte	'I2C',0
	.word	33287
	.byte	80,2,35,0,0,14
	.word	33292
	.byte	29
	.byte	'Ifx_SRC_GI2C',0,14,255,3,3
	.word	33326
	.byte	15,4
	.word	30601
	.byte	16,0,0,14
	.word	33353
	.byte	10
	.byte	'_Ifx_SRC_GLMU',0,14,130,4,25,4,13
	.byte	'LMU',0
	.word	33362
	.byte	4,2,35,0,0,14
	.word	33367
	.byte	29
	.byte	'Ifx_SRC_GLMU',0,14,133,4,3
	.word	33401
	.byte	15,40
	.word	30659
	.byte	16,1,0,14
	.word	33428
	.byte	10
	.byte	'_Ifx_SRC_GMSC',0,14,136,4,25,40,13
	.byte	'MSC',0
	.word	33437
	.byte	40,2,35,0,0,14
	.word	33442
	.byte	29
	.byte	'Ifx_SRC_GMSC',0,14,139,4,3
	.word	33476
	.byte	15,8
	.word	30770
	.byte	16,1,0,14
	.word	33503
	.byte	10
	.byte	'_Ifx_SRC_GPMU',0,14,142,4,25,8,13
	.byte	'PMU',0
	.word	33512
	.byte	8,2,35,0,0,14
	.word	33517
	.byte	29
	.byte	'Ifx_SRC_GPMU',0,14,145,4,3
	.word	33551
	.byte	15,32
	.word	30828
	.byte	16,0,0,14
	.word	33578
	.byte	10
	.byte	'_Ifx_SRC_GPSI5',0,14,148,4,25,32,13
	.byte	'PSI5',0
	.word	33587
	.byte	32,2,35,0,0,14
	.word	33592
	.byte	29
	.byte	'Ifx_SRC_GPSI5',0,14,151,4,3
	.word	33628
	.byte	15,32
	.word	30888
	.byte	16,0,0,14
	.word	33656
	.byte	10
	.byte	'_Ifx_SRC_GPSI5S',0,14,154,4,25,32,13
	.byte	'PSI5S',0
	.word	33665
	.byte	32,2,35,0,0,14
	.word	33670
	.byte	29
	.byte	'Ifx_SRC_GPSI5S',0,14,157,4,3
	.word	33708
	.byte	15,96
	.word	30950
	.byte	16,3,0,14
	.word	33737
	.byte	10
	.byte	'_Ifx_SRC_GQSPI',0,14,160,4,25,96,13
	.byte	'QSPI',0
	.word	33746
	.byte	96,2,35,0,0,14
	.word	33751
	.byte	29
	.byte	'Ifx_SRC_GQSPI',0,14,163,4,3
	.word	33787
	.byte	15,4
	.word	31070
	.byte	16,0,0,14
	.word	33815
	.byte	10
	.byte	'_Ifx_SRC_GSCR',0,14,166,4,25,4,13
	.byte	'SCR',0
	.word	33824
	.byte	4,2,35,0,0,14
	.word	33829
	.byte	29
	.byte	'Ifx_SRC_GSCR',0,14,169,4,3
	.word	33863
	.byte	14
	.word	31128
	.byte	10
	.byte	'_Ifx_SRC_GSCU',0,14,172,4,25,20,13
	.byte	'SCU',0
	.word	33890
	.byte	20,2,35,0,0,14
	.word	33895
	.byte	29
	.byte	'Ifx_SRC_GSCU',0,14,175,4,3
	.word	33929
	.byte	15,24
	.word	31209
	.byte	16,0,0,14
	.word	33956
	.byte	10
	.byte	'_Ifx_SRC_GSENT',0,14,178,4,25,24,13
	.byte	'SENT',0
	.word	33965
	.byte	24,2,35,0,0,14
	.word	33970
	.byte	29
	.byte	'Ifx_SRC_GSENT',0,14,181,4,3
	.word	34006
	.byte	15,12
	.word	31269
	.byte	16,0,0,14
	.word	34034
	.byte	10
	.byte	'_Ifx_SRC_GSMU',0,14,184,4,25,12,13
	.byte	'SMU',0
	.word	34043
	.byte	12,2,35,0,0,14
	.word	34048
	.byte	29
	.byte	'Ifx_SRC_GSMU',0,14,187,4,3
	.word	34082
	.byte	15,16
	.word	31327
	.byte	16,1,0,14
	.word	34109
	.byte	10
	.byte	'_Ifx_SRC_GSTM',0,14,190,4,25,16,13
	.byte	'STM',0
	.word	34118
	.byte	16,2,35,0,0,14
	.word	34123
	.byte	29
	.byte	'Ifx_SRC_GSTM',0,14,193,4,3
	.word	34157
	.byte	15,64
	.word	31503
	.byte	16,3,0,14
	.word	34184
	.byte	15,224,1
	.word	757
	.byte	16,223,1,0,15,32
	.word	31399
	.byte	16,1,0,14
	.word	34209
	.byte	10
	.byte	'_Ifx_SRC_GVADC',0,14,196,4,25,192,2,13
	.byte	'G',0
	.word	34193
	.byte	64,2,35,0,13
	.byte	'reserved_40',0
	.word	34198
	.byte	224,1,2,35,64,13
	.byte	'CG',0
	.word	34218
	.byte	32,3,35,160,2,0,14
	.word	34223
	.byte	29
	.byte	'Ifx_SRC_GVADC',0,14,201,4,3
	.word	34292
	.byte	14
	.word	31605
	.byte	10
	.byte	'_Ifx_SRC_GXBAR',0,14,204,4,25,4,13
	.byte	'XBAR',0
	.word	34320
	.byte	4,2,35,0,0,14
	.word	34325
	.byte	29
	.byte	'Ifx_SRC_GXBAR',0,14,207,4,3
	.word	34361
	.byte	10
	.byte	'_Ifx_SCU_ACCEN0_Bits',0,4,45,16,4,11
	.byte	'EN0',0,1
	.word	757
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	757
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	757
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	757
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	757
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	757
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	757
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	757
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	757
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	757
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	757
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	757
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	757
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	757
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	757
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	757
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	757
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	757
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	757
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	757
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	757
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	757
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	757
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	757
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	757
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	757
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	757
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	757
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	757
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	757
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	757
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	757
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_ACCEN0_Bits',0,4,79,3
	.word	34389
	.byte	10
	.byte	'_Ifx_SCU_ACCEN1_Bits',0,4,82,16,4,11
	.byte	'reserved_0',0,4
	.word	582
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_SCU_ACCEN1_Bits',0,4,85,3
	.word	34946
	.byte	10
	.byte	'_Ifx_SCU_ARSTDIS_Bits',0,4,88,16,4,11
	.byte	'STM0DIS',0,1
	.word	757
	.byte	1,7,2,35,0,11
	.byte	'STM1DIS',0,1
	.word	757
	.byte	1,6,2,35,0,11
	.byte	'STM2DIS',0,1
	.word	757
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	582
	.byte	29,0,2,35,0,0,29
	.byte	'Ifx_SCU_ARSTDIS_Bits',0,4,94,3
	.word	35023
	.byte	10
	.byte	'_Ifx_SCU_CCUCON0_Bits',0,4,97,16,4,11
	.byte	'BAUD1DIV',0,1
	.word	757
	.byte	4,4,2,35,0,11
	.byte	'BAUD2DIV',0,1
	.word	757
	.byte	4,0,2,35,0,11
	.byte	'SRIDIV',0,1
	.word	757
	.byte	4,4,2,35,1,11
	.byte	'LPDIV',0,1
	.word	757
	.byte	4,0,2,35,1,11
	.byte	'SPBDIV',0,1
	.word	757
	.byte	4,4,2,35,2,11
	.byte	'FSI2DIV',0,1
	.word	757
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	757
	.byte	2,0,2,35,2,11
	.byte	'FSIDIV',0,1
	.word	757
	.byte	2,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	757
	.byte	2,4,2,35,3,11
	.byte	'CLKSEL',0,1
	.word	757
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	757
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	757
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_CCUCON0_Bits',0,4,111,3
	.word	35159
	.byte	10
	.byte	'_Ifx_SCU_CCUCON1_Bits',0,4,114,16,4,11
	.byte	'CANDIV',0,1
	.word	757
	.byte	4,4,2,35,0,11
	.byte	'ERAYDIV',0,1
	.word	757
	.byte	4,0,2,35,0,11
	.byte	'STMDIV',0,1
	.word	757
	.byte	4,4,2,35,1,11
	.byte	'GTMDIV',0,1
	.word	757
	.byte	4,0,2,35,1,11
	.byte	'ETHDIV',0,1
	.word	757
	.byte	4,4,2,35,2,11
	.byte	'ASCLINFDIV',0,1
	.word	757
	.byte	4,0,2,35,2,11
	.byte	'ASCLINSDIV',0,1
	.word	757
	.byte	4,4,2,35,3,11
	.byte	'INSEL',0,1
	.word	757
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	757
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	757
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_CCUCON1_Bits',0,4,126,3
	.word	35439
	.byte	10
	.byte	'_Ifx_SCU_CCUCON2_Bits',0,4,129,1,16,4,11
	.byte	'BBBDIV',0,1
	.word	757
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	582
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	757
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	757
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_CCUCON2_Bits',0,4,135,1,3
	.word	35677
	.byte	10
	.byte	'_Ifx_SCU_CCUCON3_Bits',0,4,138,1,16,4,11
	.byte	'PLLDIV',0,1
	.word	757
	.byte	6,2,2,35,0,11
	.byte	'PLLSEL',0,1
	.word	757
	.byte	2,0,2,35,0,11
	.byte	'PLLERAYDIV',0,1
	.word	757
	.byte	6,2,2,35,1,11
	.byte	'PLLERAYSEL',0,1
	.word	757
	.byte	2,0,2,35,1,11
	.byte	'SRIDIV',0,1
	.word	757
	.byte	6,2,2,35,2,11
	.byte	'SRISEL',0,1
	.word	757
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	757
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	757
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	757
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	757
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_CCUCON3_Bits',0,4,150,1,3
	.word	35805
	.byte	10
	.byte	'_Ifx_SCU_CCUCON4_Bits',0,4,153,1,16,4,11
	.byte	'SPBDIV',0,1
	.word	757
	.byte	6,2,2,35,0,11
	.byte	'SPBSEL',0,1
	.word	757
	.byte	2,0,2,35,0,11
	.byte	'GTMDIV',0,1
	.word	757
	.byte	6,2,2,35,1,11
	.byte	'GTMSEL',0,1
	.word	757
	.byte	2,0,2,35,1,11
	.byte	'STMDIV',0,1
	.word	757
	.byte	6,2,2,35,2,11
	.byte	'STMSEL',0,1
	.word	757
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	757
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	757
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	757
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	757
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_CCUCON4_Bits',0,4,165,1,3
	.word	36048
	.byte	10
	.byte	'_Ifx_SCU_CCUCON5_Bits',0,4,168,1,16,4,11
	.byte	'MAXDIV',0,1
	.word	757
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	582
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	757
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	757
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_CCUCON5_Bits',0,4,174,1,3
	.word	36283
	.byte	10
	.byte	'_Ifx_SCU_CCUCON6_Bits',0,4,177,1,16,4,11
	.byte	'CPU0DIV',0,1
	.word	757
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	582
	.byte	26,0,2,35,0,0,29
	.byte	'Ifx_SCU_CCUCON6_Bits',0,4,181,1,3
	.word	36411
	.byte	10
	.byte	'_Ifx_SCU_CCUCON7_Bits',0,4,184,1,16,4,11
	.byte	'CPU1DIV',0,1
	.word	757
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	582
	.byte	26,0,2,35,0,0,29
	.byte	'Ifx_SCU_CCUCON7_Bits',0,4,188,1,3
	.word	36511
	.byte	10
	.byte	'_Ifx_SCU_CHIPID_Bits',0,4,191,1,16,4,11
	.byte	'CHREV',0,1
	.word	757
	.byte	6,2,2,35,0,11
	.byte	'CHTEC',0,1
	.word	757
	.byte	2,0,2,35,0,11
	.byte	'CHID',0,1
	.word	757
	.byte	8,0,2,35,1,11
	.byte	'EEA',0,1
	.word	757
	.byte	1,7,2,35,2,11
	.byte	'UCODE',0,1
	.word	757
	.byte	7,0,2,35,2,11
	.byte	'FSIZE',0,1
	.word	757
	.byte	4,4,2,35,3,11
	.byte	'SP',0,1
	.word	757
	.byte	2,2,2,35,3,11
	.byte	'SEC',0,1
	.word	757
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	757
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_CHIPID_Bits',0,4,202,1,3
	.word	36611
	.byte	10
	.byte	'_Ifx_SCU_DTSCON_Bits',0,4,205,1,16,4,11
	.byte	'PWD',0,1
	.word	757
	.byte	1,7,2,35,0,11
	.byte	'START',0,1
	.word	757
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	757
	.byte	2,4,2,35,0,11
	.byte	'CAL',0,4
	.word	582
	.byte	20,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	757
	.byte	7,1,2,35,3,11
	.byte	'SLCK',0,1
	.word	757
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_DTSCON_Bits',0,4,213,1,3
	.word	36819
	.byte	10
	.byte	'_Ifx_SCU_DTSLIM_Bits',0,4,216,1,16,4,11
	.byte	'LOWER',0,2
	.word	774
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	757
	.byte	5,1,2,35,1,11
	.byte	'LLU',0,1
	.word	757
	.byte	1,0,2,35,1,11
	.byte	'UPPER',0,2
	.word	774
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	757
	.byte	4,2,2,35,3,11
	.byte	'SLCK',0,1
	.word	757
	.byte	1,1,2,35,3,11
	.byte	'UOF',0,1
	.word	757
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_DTSLIM_Bits',0,4,225,1,3
	.word	36984
	.byte	10
	.byte	'_Ifx_SCU_DTSSTAT_Bits',0,4,228,1,16,4,11
	.byte	'RESULT',0,2
	.word	774
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	757
	.byte	4,2,2,35,1,11
	.byte	'RDY',0,1
	.word	757
	.byte	1,1,2,35,1,11
	.byte	'BUSY',0,1
	.word	757
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	774
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_SCU_DTSSTAT_Bits',0,4,235,1,3
	.word	37167
	.byte	10
	.byte	'_Ifx_SCU_EICR_Bits',0,4,238,1,16,4,11
	.byte	'reserved_0',0,1
	.word	757
	.byte	4,4,2,35,0,11
	.byte	'EXIS0',0,1
	.word	757
	.byte	3,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	757
	.byte	1,0,2,35,0,11
	.byte	'FEN0',0,1
	.word	757
	.byte	1,7,2,35,1,11
	.byte	'REN0',0,1
	.word	757
	.byte	1,6,2,35,1,11
	.byte	'LDEN0',0,1
	.word	757
	.byte	1,5,2,35,1,11
	.byte	'EIEN0',0,1
	.word	757
	.byte	1,4,2,35,1,11
	.byte	'INP0',0,1
	.word	757
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	582
	.byte	5,12,2,35,0,11
	.byte	'EXIS1',0,1
	.word	757
	.byte	3,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	757
	.byte	1,0,2,35,2,11
	.byte	'FEN1',0,1
	.word	757
	.byte	1,7,2,35,3,11
	.byte	'REN1',0,1
	.word	757
	.byte	1,6,2,35,3,11
	.byte	'LDEN1',0,1
	.word	757
	.byte	1,5,2,35,3,11
	.byte	'EIEN1',0,1
	.word	757
	.byte	1,4,2,35,3,11
	.byte	'INP1',0,1
	.word	757
	.byte	3,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	757
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EICR_Bits',0,4,129,2,3
	.word	37321
	.byte	10
	.byte	'_Ifx_SCU_EIFR_Bits',0,4,132,2,16,4,11
	.byte	'INTF0',0,1
	.word	757
	.byte	1,7,2,35,0,11
	.byte	'INTF1',0,1
	.word	757
	.byte	1,6,2,35,0,11
	.byte	'INTF2',0,1
	.word	757
	.byte	1,5,2,35,0,11
	.byte	'INTF3',0,1
	.word	757
	.byte	1,4,2,35,0,11
	.byte	'INTF4',0,1
	.word	757
	.byte	1,3,2,35,0,11
	.byte	'INTF5',0,1
	.word	757
	.byte	1,2,2,35,0,11
	.byte	'INTF6',0,1
	.word	757
	.byte	1,1,2,35,0,11
	.byte	'INTF7',0,1
	.word	757
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	582
	.byte	24,0,2,35,0,0,29
	.byte	'Ifx_SCU_EIFR_Bits',0,4,143,2,3
	.word	37685
	.byte	10
	.byte	'_Ifx_SCU_EMSR_Bits',0,4,146,2,16,4,11
	.byte	'POL',0,1
	.word	757
	.byte	1,7,2,35,0,11
	.byte	'MODE',0,1
	.word	757
	.byte	1,6,2,35,0,11
	.byte	'ENON',0,1
	.word	757
	.byte	1,5,2,35,0,11
	.byte	'PSEL',0,1
	.word	757
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	774
	.byte	12,0,2,35,0,11
	.byte	'EMSF',0,1
	.word	757
	.byte	1,7,2,35,2,11
	.byte	'SEMSF',0,1
	.word	757
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	757
	.byte	6,0,2,35,2,11
	.byte	'EMSFM',0,1
	.word	757
	.byte	2,6,2,35,3,11
	.byte	'SEMSFM',0,1
	.word	757
	.byte	2,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	757
	.byte	4,0,2,35,3,0,29
	.byte	'Ifx_SCU_EMSR_Bits',0,4,159,2,3
	.word	37896
	.byte	10
	.byte	'_Ifx_SCU_ESRCFG_Bits',0,4,162,2,16,4,11
	.byte	'reserved_0',0,1
	.word	757
	.byte	7,1,2,35,0,11
	.byte	'EDCON',0,2
	.word	774
	.byte	2,7,2,35,0,11
	.byte	'reserved_9',0,4
	.word	582
	.byte	23,0,2,35,0,0,29
	.byte	'Ifx_SCU_ESRCFG_Bits',0,4,167,2,3
	.word	38148
	.byte	10
	.byte	'_Ifx_SCU_ESROCFG_Bits',0,4,170,2,16,4,11
	.byte	'ARI',0,1
	.word	757
	.byte	1,7,2,35,0,11
	.byte	'ARC',0,1
	.word	757
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	582
	.byte	30,0,2,35,0,0,29
	.byte	'Ifx_SCU_ESROCFG_Bits',0,4,175,2,3
	.word	38266
	.byte	10
	.byte	'_Ifx_SCU_EVR13CON_Bits',0,4,178,2,16,4,11
	.byte	'reserved_0',0,4
	.word	582
	.byte	28,4,2,35,0,11
	.byte	'EVR13OFF',0,1
	.word	757
	.byte	1,3,2,35,3,11
	.byte	'BPEVR13OFF',0,1
	.word	757
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	757
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	757
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVR13CON_Bits',0,4,185,2,3
	.word	38377
	.byte	10
	.byte	'_Ifx_SCU_EVR33CON_Bits',0,4,188,2,16,4,11
	.byte	'reserved_0',0,4
	.word	582
	.byte	28,4,2,35,0,11
	.byte	'EVR33OFF',0,1
	.word	757
	.byte	1,3,2,35,3,11
	.byte	'BPEVR33OFF',0,1
	.word	757
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	757
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	757
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVR33CON_Bits',0,4,195,2,3
	.word	38540
	.byte	10
	.byte	'_Ifx_SCU_EVRADCSTAT_Bits',0,4,198,2,16,4,11
	.byte	'ADC13V',0,1
	.word	757
	.byte	8,0,2,35,0,11
	.byte	'ADC33V',0,1
	.word	757
	.byte	8,0,2,35,1,11
	.byte	'ADCSWDV',0,1
	.word	757
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	757
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	757
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRADCSTAT_Bits',0,4,205,2,3
	.word	38703
	.byte	10
	.byte	'_Ifx_SCU_EVRDVSTAT_Bits',0,4,208,2,16,4,11
	.byte	'DVS13TRIM',0,1
	.word	757
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	757
	.byte	8,0,2,35,1,11
	.byte	'DVS33TRIM',0,1
	.word	757
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	757
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	757
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRDVSTAT_Bits',0,4,215,2,3
	.word	38861
	.byte	10
	.byte	'_Ifx_SCU_EVRMONCTRL_Bits',0,4,218,2,16,4,11
	.byte	'EVR13OVMOD',0,1
	.word	757
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	757
	.byte	2,4,2,35,0,11
	.byte	'EVR13UVMOD',0,1
	.word	757
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	757
	.byte	2,0,2,35,0,11
	.byte	'EVR33OVMOD',0,1
	.word	757
	.byte	2,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	757
	.byte	2,4,2,35,1,11
	.byte	'EVR33UVMOD',0,1
	.word	757
	.byte	2,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	757
	.byte	2,0,2,35,1,11
	.byte	'SWDOVMOD',0,1
	.word	757
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	757
	.byte	2,4,2,35,2,11
	.byte	'SWDUVMOD',0,1
	.word	757
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,2
	.word	774
	.byte	10,0,2,35,2,0,29
	.byte	'Ifx_SCU_EVRMONCTRL_Bits',0,4,232,2,3
	.word	39026
	.byte	10
	.byte	'_Ifx_SCU_EVROSCCTRL_Bits',0,4,235,2,16,4,11
	.byte	'OSCTRIM',0,2
	.word	774
	.byte	10,6,2,35,0,11
	.byte	'OSCPTAT',0,1
	.word	757
	.byte	6,0,2,35,1,11
	.byte	'OSCANASEL',0,1
	.word	757
	.byte	4,4,2,35,2,11
	.byte	'HPBGTRIM',0,2
	.word	774
	.byte	7,5,2,35,2,11
	.byte	'HPBGCLKEN',0,1
	.word	757
	.byte	1,4,2,35,3,11
	.byte	'OSC3V3',0,1
	.word	757
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	757
	.byte	2,1,2,35,3,11
	.byte	'LCK',0,1
	.word	757
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVROSCCTRL_Bits',0,4,245,2,3
	.word	39355
	.byte	10
	.byte	'_Ifx_SCU_EVROVMON_Bits',0,4,248,2,16,4,11
	.byte	'EVR13OVVAL',0,1
	.word	757
	.byte	8,0,2,35,0,11
	.byte	'EVR33OVVAL',0,1
	.word	757
	.byte	8,0,2,35,1,11
	.byte	'SWDOVVAL',0,1
	.word	757
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	757
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	757
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVROVMON_Bits',0,4,255,2,3
	.word	39576
	.byte	10
	.byte	'_Ifx_SCU_EVRRSTCON_Bits',0,4,130,3,16,4,11
	.byte	'RST13TRIM',0,1
	.word	757
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	582
	.byte	16,8,2,35,0,11
	.byte	'RST13OFF',0,1
	.word	757
	.byte	1,7,2,35,3,11
	.byte	'BPRST13OFF',0,1
	.word	757
	.byte	1,6,2,35,3,11
	.byte	'RST33OFF',0,1
	.word	757
	.byte	1,5,2,35,3,11
	.byte	'BPRST33OFF',0,1
	.word	757
	.byte	1,4,2,35,3,11
	.byte	'RSTSWDOFF',0,1
	.word	757
	.byte	1,3,2,35,3,11
	.byte	'BPRSTSWDOFF',0,1
	.word	757
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	757
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	757
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRRSTCON_Bits',0,4,142,3,3
	.word	39739
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF1_Bits',0,4,145,3,16,4,11
	.byte	'SD5P',0,1
	.word	757
	.byte	8,0,2,35,0,11
	.byte	'SD5I',0,1
	.word	757
	.byte	8,0,2,35,1,11
	.byte	'SD5D',0,1
	.word	757
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	757
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	757
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF1_Bits',0,4,152,3,3
	.word	40011
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF2_Bits',0,4,155,3,16,4,11
	.byte	'SD33P',0,1
	.word	757
	.byte	8,0,2,35,0,11
	.byte	'SD33I',0,1
	.word	757
	.byte	8,0,2,35,1,11
	.byte	'SD33D',0,1
	.word	757
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	757
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	757
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF2_Bits',0,4,162,3,3
	.word	40164
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF3_Bits',0,4,165,3,16,4,11
	.byte	'CT5REG0',0,1
	.word	757
	.byte	8,0,2,35,0,11
	.byte	'CT5REG1',0,1
	.word	757
	.byte	8,0,2,35,1,11
	.byte	'CT5REG2',0,1
	.word	757
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	757
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	757
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF3_Bits',0,4,172,3,3
	.word	40320
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF4_Bits',0,4,175,3,16,4,11
	.byte	'CT5REG3',0,1
	.word	757
	.byte	8,0,2,35,0,11
	.byte	'CT5REG4',0,1
	.word	757
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	774
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	757
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF4_Bits',0,4,181,3,3
	.word	40482
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF5_Bits',0,4,184,3,16,4,11
	.byte	'CT33REG0',0,1
	.word	757
	.byte	8,0,2,35,0,11
	.byte	'CT33REG1',0,1
	.word	757
	.byte	8,0,2,35,1,11
	.byte	'CT33REG2',0,1
	.word	757
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	757
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	757
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF5_Bits',0,4,191,3,3
	.word	40625
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF6_Bits',0,4,194,3,16,4,11
	.byte	'CT33REG3',0,1
	.word	757
	.byte	8,0,2,35,0,11
	.byte	'CT33REG4',0,1
	.word	757
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	774
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	757
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF6_Bits',0,4,200,3,3
	.word	40790
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL1_Bits',0,4,203,3,16,4,11
	.byte	'SDFREQSPRD',0,2
	.word	774
	.byte	16,0,2,35,0,11
	.byte	'SDFREQ',0,1
	.word	757
	.byte	8,0,2,35,2,11
	.byte	'SDSTEP',0,1
	.word	757
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	757
	.byte	2,2,2,35,3,11
	.byte	'SDSAMPLE',0,1
	.word	757
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	757
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRSDCTRL1_Bits',0,4,211,3,3
	.word	40935
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL2_Bits',0,4,214,3,16,4,11
	.byte	'DRVP',0,1
	.word	757
	.byte	8,0,2,35,0,11
	.byte	'SDMINMAXDC',0,1
	.word	757
	.byte	8,0,2,35,1,11
	.byte	'DRVN',0,1
	.word	757
	.byte	8,0,2,35,2,11
	.byte	'SDLUT',0,1
	.word	757
	.byte	6,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	757
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	757
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRSDCTRL2_Bits',0,4,222,3,3
	.word	41116
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL3_Bits',0,4,225,3,16,4,11
	.byte	'SDPWMPRE',0,1
	.word	757
	.byte	8,0,2,35,0,11
	.byte	'SDPID',0,1
	.word	757
	.byte	8,0,2,35,1,11
	.byte	'SDVOKLVL',0,1
	.word	757
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	757
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	757
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRSDCTRL3_Bits',0,4,232,3,3
	.word	41290
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL4_Bits',0,4,235,3,16,4,11
	.byte	'reserved_0',0,1
	.word	757
	.byte	8,0,2,35,0,11
	.byte	'SYNCDIV',0,1
	.word	757
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	582
	.byte	20,1,2,35,0,11
	.byte	'LCK',0,1
	.word	757
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRSDCTRL4_Bits',0,4,241,3,3
	.word	41450
	.byte	10
	.byte	'_Ifx_SCU_EVRSTAT_Bits',0,4,244,3,16,4,11
	.byte	'EVR13',0,1
	.word	757
	.byte	1,7,2,35,0,11
	.byte	'OV13',0,1
	.word	757
	.byte	1,6,2,35,0,11
	.byte	'EVR33',0,1
	.word	757
	.byte	1,5,2,35,0,11
	.byte	'OV33',0,1
	.word	757
	.byte	1,4,2,35,0,11
	.byte	'OVSWD',0,1
	.word	757
	.byte	1,3,2,35,0,11
	.byte	'UV13',0,1
	.word	757
	.byte	1,2,2,35,0,11
	.byte	'UV33',0,1
	.word	757
	.byte	1,1,2,35,0,11
	.byte	'UVSWD',0,1
	.word	757
	.byte	1,0,2,35,0,11
	.byte	'EXTPASS13',0,1
	.word	757
	.byte	1,7,2,35,1,11
	.byte	'EXTPASS33',0,1
	.word	757
	.byte	1,6,2,35,1,11
	.byte	'BGPROK',0,1
	.word	757
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	582
	.byte	21,0,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSTAT_Bits',0,4,130,4,3
	.word	41594
	.byte	10
	.byte	'_Ifx_SCU_EVRTRIM_Bits',0,4,133,4,16,4,11
	.byte	'EVR13TRIM',0,1
	.word	757
	.byte	8,0,2,35,0,11
	.byte	'SDVOUTSEL',0,1
	.word	757
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	774
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	757
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRTRIM_Bits',0,4,139,4,3
	.word	41868
	.byte	10
	.byte	'_Ifx_SCU_EVRUVMON_Bits',0,4,142,4,16,4,11
	.byte	'EVR13UVVAL',0,1
	.word	757
	.byte	8,0,2,35,0,11
	.byte	'EVR33UVVAL',0,1
	.word	757
	.byte	8,0,2,35,1,11
	.byte	'SWDUVVAL',0,1
	.word	757
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	757
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	757
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRUVMON_Bits',0,4,149,4,3
	.word	42007
	.byte	10
	.byte	'_Ifx_SCU_EXTCON_Bits',0,4,152,4,16,4,11
	.byte	'EN0',0,1
	.word	757
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	757
	.byte	1,6,2,35,0,11
	.byte	'SEL0',0,1
	.word	757
	.byte	4,2,2,35,0,11
	.byte	'reserved_6',0,2
	.word	774
	.byte	10,0,2,35,0,11
	.byte	'EN1',0,1
	.word	757
	.byte	1,7,2,35,2,11
	.byte	'NSEL',0,1
	.word	757
	.byte	1,6,2,35,2,11
	.byte	'SEL1',0,1
	.word	757
	.byte	4,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	757
	.byte	2,0,2,35,2,11
	.byte	'DIV1',0,1
	.word	757
	.byte	8,0,2,35,3,0,29
	.byte	'Ifx_SCU_EXTCON_Bits',0,4,163,4,3
	.word	42170
	.byte	10
	.byte	'_Ifx_SCU_FDR_Bits',0,4,166,4,16,4,11
	.byte	'STEP',0,2
	.word	774
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	757
	.byte	4,2,2,35,1,11
	.byte	'DM',0,1
	.word	757
	.byte	2,0,2,35,1,11
	.byte	'RESULT',0,2
	.word	774
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	757
	.byte	5,1,2,35,3,11
	.byte	'DISCLK',0,1
	.word	757
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_FDR_Bits',0,4,174,4,3
	.word	42388
	.byte	10
	.byte	'_Ifx_SCU_FMR_Bits',0,4,177,4,16,4,11
	.byte	'FS0',0,1
	.word	757
	.byte	1,7,2,35,0,11
	.byte	'FS1',0,1
	.word	757
	.byte	1,6,2,35,0,11
	.byte	'FS2',0,1
	.word	757
	.byte	1,5,2,35,0,11
	.byte	'FS3',0,1
	.word	757
	.byte	1,4,2,35,0,11
	.byte	'FS4',0,1
	.word	757
	.byte	1,3,2,35,0,11
	.byte	'FS5',0,1
	.word	757
	.byte	1,2,2,35,0,11
	.byte	'FS6',0,1
	.word	757
	.byte	1,1,2,35,0,11
	.byte	'FS7',0,1
	.word	757
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	757
	.byte	8,0,2,35,1,11
	.byte	'FC0',0,1
	.word	757
	.byte	1,7,2,35,2,11
	.byte	'FC1',0,1
	.word	757
	.byte	1,6,2,35,2,11
	.byte	'FC2',0,1
	.word	757
	.byte	1,5,2,35,2,11
	.byte	'FC3',0,1
	.word	757
	.byte	1,4,2,35,2,11
	.byte	'FC4',0,1
	.word	757
	.byte	1,3,2,35,2,11
	.byte	'FC5',0,1
	.word	757
	.byte	1,2,2,35,2,11
	.byte	'FC6',0,1
	.word	757
	.byte	1,1,2,35,2,11
	.byte	'FC7',0,1
	.word	757
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	757
	.byte	8,0,2,35,3,0,29
	.byte	'Ifx_SCU_FMR_Bits',0,4,197,4,3
	.word	42551
	.byte	10
	.byte	'_Ifx_SCU_ID_Bits',0,4,200,4,16,4,11
	.byte	'MODREV',0,1
	.word	757
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	757
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	774
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_SCU_ID_Bits',0,4,205,4,3
	.word	42887
	.byte	10
	.byte	'_Ifx_SCU_IGCR_Bits',0,4,208,4,16,4,11
	.byte	'IPEN00',0,1
	.word	757
	.byte	1,7,2,35,0,11
	.byte	'IPEN01',0,1
	.word	757
	.byte	1,6,2,35,0,11
	.byte	'IPEN02',0,1
	.word	757
	.byte	1,5,2,35,0,11
	.byte	'IPEN03',0,1
	.word	757
	.byte	1,4,2,35,0,11
	.byte	'IPEN04',0,1
	.word	757
	.byte	1,3,2,35,0,11
	.byte	'IPEN05',0,1
	.word	757
	.byte	1,2,2,35,0,11
	.byte	'IPEN06',0,1
	.word	757
	.byte	1,1,2,35,0,11
	.byte	'IPEN07',0,1
	.word	757
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	757
	.byte	5,3,2,35,1,11
	.byte	'GEEN0',0,1
	.word	757
	.byte	1,2,2,35,1,11
	.byte	'IGP0',0,1
	.word	757
	.byte	2,0,2,35,1,11
	.byte	'IPEN10',0,1
	.word	757
	.byte	1,7,2,35,2,11
	.byte	'IPEN11',0,1
	.word	757
	.byte	1,6,2,35,2,11
	.byte	'IPEN12',0,1
	.word	757
	.byte	1,5,2,35,2,11
	.byte	'IPEN13',0,1
	.word	757
	.byte	1,4,2,35,2,11
	.byte	'IPEN14',0,1
	.word	757
	.byte	1,3,2,35,2,11
	.byte	'IPEN15',0,1
	.word	757
	.byte	1,2,2,35,2,11
	.byte	'IPEN16',0,1
	.word	757
	.byte	1,1,2,35,2,11
	.byte	'IPEN17',0,1
	.word	757
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	757
	.byte	5,3,2,35,3,11
	.byte	'GEEN1',0,1
	.word	757
	.byte	1,2,2,35,3,11
	.byte	'IGP1',0,1
	.word	757
	.byte	2,0,2,35,3,0,29
	.byte	'Ifx_SCU_IGCR_Bits',0,4,232,4,3
	.word	42994
	.byte	10
	.byte	'_Ifx_SCU_IN_Bits',0,4,235,4,16,4,11
	.byte	'P0',0,1
	.word	757
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	757
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	582
	.byte	30,0,2,35,0,0,29
	.byte	'Ifx_SCU_IN_Bits',0,4,240,4,3
	.word	43446
	.byte	10
	.byte	'_Ifx_SCU_IOCR_Bits',0,4,243,4,16,4,11
	.byte	'reserved_0',0,1
	.word	757
	.byte	4,4,2,35,0,11
	.byte	'PC0',0,1
	.word	757
	.byte	4,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	757
	.byte	4,4,2,35,1,11
	.byte	'PC1',0,1
	.word	757
	.byte	4,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	774
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_SCU_IOCR_Bits',0,4,250,4,3
	.word	43545
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL0_Bits',0,4,253,4,16,4,11
	.byte	'LBISTREQ',0,1
	.word	757
	.byte	1,7,2,35,0,11
	.byte	'LBISTREQP',0,1
	.word	757
	.byte	1,6,2,35,0,11
	.byte	'PATTERNS',0,2
	.word	774
	.byte	14,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	774
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_SCU_LBISTCTRL0_Bits',0,4,131,5,3
	.word	43695
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL1_Bits',0,4,134,5,16,4,11
	.byte	'SEED',0,4
	.word	582
	.byte	23,9,2,35,0,11
	.byte	'reserved_23',0,1
	.word	757
	.byte	1,0,2,35,2,11
	.byte	'SPLITSH',0,1
	.word	757
	.byte	3,5,2,35,3,11
	.byte	'BODY',0,1
	.word	757
	.byte	1,4,2,35,3,11
	.byte	'LBISTFREQU',0,1
	.word	757
	.byte	4,0,2,35,3,0,29
	.byte	'Ifx_SCU_LBISTCTRL1_Bits',0,4,141,5,3
	.word	43844
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL2_Bits',0,4,144,5,16,4,11
	.byte	'SIGNATURE',0,4
	.word	582
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	757
	.byte	7,1,2,35,3,11
	.byte	'LBISTDONE',0,1
	.word	757
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_LBISTCTRL2_Bits',0,4,149,5,3
	.word	44005
	.byte	10
	.byte	'_Ifx_SCU_LCLCON_Bits',0,4,152,5,16,4,11
	.byte	'reserved_0',0,2
	.word	774
	.byte	16,0,2,35,0,11
	.byte	'LS',0,1
	.word	757
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,2
	.word	774
	.byte	14,1,2,35,2,11
	.byte	'LSEN',0,1
	.word	757
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_LCLCON_Bits',0,4,158,5,3
	.word	44135
	.byte	10
	.byte	'_Ifx_SCU_LCLTEST_Bits',0,4,161,5,16,4,11
	.byte	'LCLT0',0,1
	.word	757
	.byte	1,7,2,35,0,11
	.byte	'LCLT1',0,1
	.word	757
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	582
	.byte	30,0,2,35,0,0,29
	.byte	'Ifx_SCU_LCLTEST_Bits',0,4,166,5,3
	.word	44267
	.byte	10
	.byte	'_Ifx_SCU_MANID_Bits',0,4,169,5,16,4,11
	.byte	'DEPT',0,1
	.word	757
	.byte	5,3,2,35,0,11
	.byte	'MANUF',0,2
	.word	774
	.byte	11,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	774
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_SCU_MANID_Bits',0,4,174,5,3
	.word	44382
	.byte	10
	.byte	'_Ifx_SCU_OMR_Bits',0,4,177,5,16,4,11
	.byte	'PS0',0,1
	.word	757
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	757
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	774
	.byte	14,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	757
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	757
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	774
	.byte	14,0,2,35,2,0,29
	.byte	'Ifx_SCU_OMR_Bits',0,4,185,5,3
	.word	44493
	.byte	10
	.byte	'_Ifx_SCU_OSCCON_Bits',0,4,188,5,16,4,11
	.byte	'reserved_0',0,1
	.word	757
	.byte	1,7,2,35,0,11
	.byte	'PLLLV',0,1
	.word	757
	.byte	1,6,2,35,0,11
	.byte	'OSCRES',0,1
	.word	757
	.byte	1,5,2,35,0,11
	.byte	'GAINSEL',0,1
	.word	757
	.byte	2,3,2,35,0,11
	.byte	'MODE',0,1
	.word	757
	.byte	2,1,2,35,0,11
	.byte	'SHBY',0,1
	.word	757
	.byte	1,0,2,35,0,11
	.byte	'PLLHV',0,1
	.word	757
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	757
	.byte	1,6,2,35,1,11
	.byte	'X1D',0,1
	.word	757
	.byte	1,5,2,35,1,11
	.byte	'X1DEN',0,1
	.word	757
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	757
	.byte	4,0,2,35,1,11
	.byte	'OSCVAL',0,1
	.word	757
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	757
	.byte	2,1,2,35,2,11
	.byte	'APREN',0,1
	.word	757
	.byte	1,0,2,35,2,11
	.byte	'CAP0EN',0,1
	.word	757
	.byte	1,7,2,35,3,11
	.byte	'CAP1EN',0,1
	.word	757
	.byte	1,6,2,35,3,11
	.byte	'CAP2EN',0,1
	.word	757
	.byte	1,5,2,35,3,11
	.byte	'CAP3EN',0,1
	.word	757
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	757
	.byte	4,0,2,35,3,0,29
	.byte	'Ifx_SCU_OSCCON_Bits',0,4,209,5,3
	.word	44651
	.byte	10
	.byte	'_Ifx_SCU_OUT_Bits',0,4,212,5,16,4,11
	.byte	'P0',0,1
	.word	757
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	757
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	582
	.byte	30,0,2,35,0,0,29
	.byte	'Ifx_SCU_OUT_Bits',0,4,217,5,3
	.word	45063
	.byte	10
	.byte	'_Ifx_SCU_OVCCON_Bits',0,4,220,5,16,4,11
	.byte	'CSEL0',0,1
	.word	757
	.byte	1,7,2,35,0,11
	.byte	'CSEL1',0,1
	.word	757
	.byte	1,6,2,35,0,11
	.byte	'CSEL2',0,1
	.word	757
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,2
	.word	774
	.byte	13,0,2,35,0,11
	.byte	'OVSTRT',0,1
	.word	757
	.byte	1,7,2,35,2,11
	.byte	'OVSTP',0,1
	.word	757
	.byte	1,6,2,35,2,11
	.byte	'DCINVAL',0,1
	.word	757
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	757
	.byte	5,0,2,35,2,11
	.byte	'OVCONF',0,1
	.word	757
	.byte	1,7,2,35,3,11
	.byte	'POVCONF',0,1
	.word	757
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	757
	.byte	6,0,2,35,3,0,29
	.byte	'Ifx_SCU_OVCCON_Bits',0,4,233,5,3
	.word	45164
	.byte	10
	.byte	'_Ifx_SCU_OVCENABLE_Bits',0,4,236,5,16,4,11
	.byte	'OVEN0',0,1
	.word	757
	.byte	1,7,2,35,0,11
	.byte	'OVEN1',0,1
	.word	757
	.byte	1,6,2,35,0,11
	.byte	'OVEN2',0,1
	.word	757
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	582
	.byte	29,0,2,35,0,0,29
	.byte	'Ifx_SCU_OVCENABLE_Bits',0,4,242,5,3
	.word	45431
	.byte	10
	.byte	'_Ifx_SCU_PDISC_Bits',0,4,245,5,16,4,11
	.byte	'PDIS0',0,1
	.word	757
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	757
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	582
	.byte	30,0,2,35,0,0,29
	.byte	'Ifx_SCU_PDISC_Bits',0,4,250,5,3
	.word	45567
	.byte	10
	.byte	'_Ifx_SCU_PDR_Bits',0,4,253,5,16,4,11
	.byte	'PD0',0,1
	.word	757
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	757
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	757
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	757
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	582
	.byte	24,0,2,35,0,0,29
	.byte	'Ifx_SCU_PDR_Bits',0,4,132,6,3
	.word	45678
	.byte	10
	.byte	'_Ifx_SCU_PDRR_Bits',0,4,135,6,16,4,11
	.byte	'PDR0',0,1
	.word	757
	.byte	1,7,2,35,0,11
	.byte	'PDR1',0,1
	.word	757
	.byte	1,6,2,35,0,11
	.byte	'PDR2',0,1
	.word	757
	.byte	1,5,2,35,0,11
	.byte	'PDR3',0,1
	.word	757
	.byte	1,4,2,35,0,11
	.byte	'PDR4',0,1
	.word	757
	.byte	1,3,2,35,0,11
	.byte	'PDR5',0,1
	.word	757
	.byte	1,2,2,35,0,11
	.byte	'PDR6',0,1
	.word	757
	.byte	1,1,2,35,0,11
	.byte	'PDR7',0,1
	.word	757
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	582
	.byte	24,0,2,35,0,0,29
	.byte	'Ifx_SCU_PDRR_Bits',0,4,146,6,3
	.word	45811
	.byte	10
	.byte	'_Ifx_SCU_PLLCON0_Bits',0,4,149,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	757
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	757
	.byte	1,6,2,35,0,11
	.byte	'MODEN',0,1
	.word	757
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	757
	.byte	1,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	757
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	757
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	757
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	774
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	757
	.byte	7,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	757
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	757
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	757
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	757
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	757
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	757
	.byte	4,0,2,35,3,0,29
	.byte	'Ifx_SCU_PLLCON0_Bits',0,4,166,6,3
	.word	46014
	.byte	10
	.byte	'_Ifx_SCU_PLLCON1_Bits',0,4,169,6,16,4,11
	.byte	'K2DIV',0,1
	.word	757
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	757
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	757
	.byte	7,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	757
	.byte	1,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	757
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	774
	.byte	9,0,2,35,2,0,29
	.byte	'Ifx_SCU_PLLCON1_Bits',0,4,177,6,3
	.word	46370
	.byte	10
	.byte	'_Ifx_SCU_PLLCON2_Bits',0,4,180,6,16,4,11
	.byte	'MODCFG',0,2
	.word	774
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	774
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_SCU_PLLCON2_Bits',0,4,184,6,3
	.word	46548
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON0_Bits',0,4,187,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	757
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	757
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	757
	.byte	2,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	757
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	757
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	757
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	774
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	757
	.byte	5,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	757
	.byte	2,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	757
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	757
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	757
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	757
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	757
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	757
	.byte	4,0,2,35,3,0,29
	.byte	'Ifx_SCU_PLLERAYCON0_Bits',0,4,204,6,3
	.word	46648
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON1_Bits',0,4,207,6,16,4,11
	.byte	'K2DIV',0,1
	.word	757
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	757
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	757
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	757
	.byte	4,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	757
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	774
	.byte	9,0,2,35,2,0,29
	.byte	'Ifx_SCU_PLLERAYCON1_Bits',0,4,215,6,3
	.word	47018
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYSTAT_Bits',0,4,218,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	757
	.byte	1,7,2,35,0,11
	.byte	'PWDSTAT',0,1
	.word	757
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	757
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	757
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	757
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	757
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	582
	.byte	26,0,2,35,0,0,29
	.byte	'Ifx_SCU_PLLERAYSTAT_Bits',0,4,227,6,3
	.word	47204
	.byte	10
	.byte	'_Ifx_SCU_PLLSTAT_Bits',0,4,230,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	757
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	757
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	757
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	757
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	757
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	757
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	757
	.byte	1,1,2,35,0,11
	.byte	'MODRUN',0,1
	.word	757
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	582
	.byte	24,0,2,35,0,0,29
	.byte	'Ifx_SCU_PLLSTAT_Bits',0,4,241,6,3
	.word	47402
	.byte	29
	.byte	'Ifx_SCU_PMCSR_Bits',0,4,251,6,3
	.word	22243
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR0_Bits',0,4,254,6,16,4,11
	.byte	'reserved_0',0,1
	.word	757
	.byte	1,7,2,35,0,11
	.byte	'ESR1WKEN',0,1
	.word	757
	.byte	1,6,2,35,0,11
	.byte	'PINAWKEN',0,1
	.word	757
	.byte	1,5,2,35,0,11
	.byte	'PINBWKEN',0,1
	.word	757
	.byte	1,4,2,35,0,11
	.byte	'ESR0DFEN',0,1
	.word	757
	.byte	1,3,2,35,0,11
	.byte	'ESR0EDCON',0,1
	.word	757
	.byte	2,1,2,35,0,11
	.byte	'ESR1DFEN',0,1
	.word	757
	.byte	1,0,2,35,0,11
	.byte	'ESR1EDCON',0,1
	.word	757
	.byte	2,6,2,35,1,11
	.byte	'PINADFEN',0,1
	.word	757
	.byte	1,5,2,35,1,11
	.byte	'PINAEDCON',0,1
	.word	757
	.byte	2,3,2,35,1,11
	.byte	'PINBDFEN',0,1
	.word	757
	.byte	1,2,2,35,1,11
	.byte	'PINBEDCON',0,1
	.word	757
	.byte	2,0,2,35,1,11
	.byte	'SCREN',0,1
	.word	757
	.byte	1,7,2,35,2,11
	.byte	'STBYRAMSEL',0,1
	.word	757
	.byte	2,5,2,35,2,11
	.byte	'SCRCLKSEL',0,1
	.word	757
	.byte	1,4,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	757
	.byte	1,3,2,35,2,11
	.byte	'TRISTEN',0,1
	.word	757
	.byte	1,2,2,35,2,11
	.byte	'TRISTREQ',0,1
	.word	757
	.byte	1,1,2,35,2,11
	.byte	'PORSTDF',0,1
	.word	757
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	757
	.byte	1,7,2,35,3,11
	.byte	'DCDCSYNC',0,1
	.word	757
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	757
	.byte	3,3,2,35,3,11
	.byte	'ESR0TRIST',0,1
	.word	757
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	757
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	757
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_PMSWCR0_Bits',0,4,153,7,3
	.word	47663
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR1_Bits',0,4,156,7,16,4,11
	.byte	'SCRSTEN',0,1
	.word	757
	.byte	1,7,2,35,0,11
	.byte	'SCRSTREQ',0,1
	.word	757
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	757
	.byte	6,0,2,35,0,11
	.byte	'CPUIDLSEL',0,1
	.word	757
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	757
	.byte	1,4,2,35,1,11
	.byte	'IRADIS',0,1
	.word	757
	.byte	1,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	757
	.byte	3,0,2,35,1,11
	.byte	'SCRCFG',0,1
	.word	757
	.byte	8,0,2,35,2,11
	.byte	'CPUSEL',0,1
	.word	757
	.byte	3,5,2,35,3,11
	.byte	'STBYEVEN',0,1
	.word	757
	.byte	1,4,2,35,3,11
	.byte	'STBYEV',0,1
	.word	757
	.byte	3,1,2,35,3,11
	.byte	'LCK',0,1
	.word	757
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_PMSWCR1_Bits',0,4,170,7,3
	.word	48230
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR2_Bits',0,4,173,7,16,4,11
	.byte	'SCRINT',0,1
	.word	757
	.byte	8,0,2,35,0,11
	.byte	'BUSY',0,1
	.word	757
	.byte	1,7,2,35,1,11
	.byte	'SCRECC',0,1
	.word	757
	.byte	1,6,2,35,1,11
	.byte	'SCRWDT',0,1
	.word	757
	.byte	1,5,2,35,1,11
	.byte	'SCRRST',0,1
	.word	757
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	757
	.byte	4,0,2,35,1,11
	.byte	'TCINT',0,1
	.word	757
	.byte	8,0,2,35,2,11
	.byte	'TCINTREQ',0,1
	.word	757
	.byte	1,7,2,35,3,11
	.byte	'SMURST',0,1
	.word	757
	.byte	1,6,2,35,3,11
	.byte	'RST',0,1
	.word	757
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	757
	.byte	4,1,2,35,3,11
	.byte	'LCK',0,1
	.word	757
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_PMSWCR2_Bits',0,4,187,7,3
	.word	48524
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTAT_Bits',0,4,190,7,16,4,11
	.byte	'reserved_0',0,1
	.word	757
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKP',0,1
	.word	757
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUN',0,1
	.word	757
	.byte	1,4,2,35,0,11
	.byte	'PINAWKP',0,1
	.word	757
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUN',0,1
	.word	757
	.byte	1,2,2,35,0,11
	.byte	'PINBWKP',0,1
	.word	757
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUN',0,1
	.word	757
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	757
	.byte	1,7,2,35,1,11
	.byte	'PORSTDF',0,1
	.word	757
	.byte	1,6,2,35,1,11
	.byte	'HWCFGEVR',0,1
	.word	757
	.byte	3,3,2,35,1,11
	.byte	'STBYRAM',0,1
	.word	757
	.byte	2,1,2,35,1,11
	.byte	'TRIST',0,1
	.word	757
	.byte	1,0,2,35,1,11
	.byte	'SCRST',0,1
	.word	757
	.byte	1,7,2,35,2,11
	.byte	'SCRWKP',0,1
	.word	757
	.byte	1,6,2,35,2,11
	.byte	'SCR',0,1
	.word	757
	.byte	1,5,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	757
	.byte	1,4,2,35,2,11
	.byte	'ESR1WKEN',0,1
	.word	757
	.byte	1,3,2,35,2,11
	.byte	'PINAWKEN',0,1
	.word	757
	.byte	1,2,2,35,2,11
	.byte	'PINBWKEN',0,1
	.word	757
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	774
	.byte	4,5,2,35,2,11
	.byte	'ESR0TRIST',0,1
	.word	757
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	757
	.byte	4,0,2,35,3,0,29
	.byte	'Ifx_SCU_PMSWSTAT_Bits',0,4,214,7,3
	.word	48802
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTATCLR_Bits',0,4,217,7,16,4,11
	.byte	'reserved_0',0,1
	.word	757
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKPCLR',0,1
	.word	757
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUNCLR',0,1
	.word	757
	.byte	1,4,2,35,0,11
	.byte	'PINAWKPCLR',0,1
	.word	757
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUNCLR',0,1
	.word	757
	.byte	1,2,2,35,0,11
	.byte	'PINBWKPCLR',0,1
	.word	757
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUNCLR',0,1
	.word	757
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	757
	.byte	8,0,2,35,1,11
	.byte	'SCRSTCLR',0,1
	.word	757
	.byte	1,7,2,35,2,11
	.byte	'SCRWKPCLR',0,1
	.word	757
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	774
	.byte	14,0,2,35,2,0,29
	.byte	'Ifx_SCU_PMSWSTATCLR_Bits',0,4,230,7,3
	.word	49298
	.byte	10
	.byte	'_Ifx_SCU_RSTCON2_Bits',0,4,233,7,16,4,11
	.byte	'reserved_0',0,1
	.word	757
	.byte	1,7,2,35,0,11
	.byte	'CLRC',0,1
	.word	757
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	774
	.byte	10,4,2,35,0,11
	.byte	'CSS0',0,1
	.word	757
	.byte	1,3,2,35,1,11
	.byte	'CSS1',0,1
	.word	757
	.byte	1,2,2,35,1,11
	.byte	'CSS2',0,1
	.word	757
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	757
	.byte	1,0,2,35,1,11
	.byte	'USRINFO',0,2
	.word	774
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_SCU_RSTCON2_Bits',0,4,243,7,3
	.word	49611
	.byte	10
	.byte	'_Ifx_SCU_RSTCON_Bits',0,4,246,7,16,4,11
	.byte	'ESR0',0,1
	.word	757
	.byte	2,6,2,35,0,11
	.byte	'ESR1',0,1
	.word	757
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	757
	.byte	2,2,2,35,0,11
	.byte	'SMU',0,1
	.word	757
	.byte	2,0,2,35,0,11
	.byte	'SW',0,1
	.word	757
	.byte	2,6,2,35,1,11
	.byte	'STM0',0,1
	.word	757
	.byte	2,4,2,35,1,11
	.byte	'STM1',0,1
	.word	757
	.byte	2,2,2,35,1,11
	.byte	'STM2',0,1
	.word	757
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	774
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_SCU_RSTCON_Bits',0,4,129,8,3
	.word	49820
	.byte	10
	.byte	'_Ifx_SCU_RSTSTAT_Bits',0,4,132,8,16,4,11
	.byte	'ESR0',0,1
	.word	757
	.byte	1,7,2,35,0,11
	.byte	'ESR1',0,1
	.word	757
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	757
	.byte	1,5,2,35,0,11
	.byte	'SMU',0,1
	.word	757
	.byte	1,4,2,35,0,11
	.byte	'SW',0,1
	.word	757
	.byte	1,3,2,35,0,11
	.byte	'STM0',0,1
	.word	757
	.byte	1,2,2,35,0,11
	.byte	'STM1',0,1
	.word	757
	.byte	1,1,2,35,0,11
	.byte	'STM2',0,1
	.word	757
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	757
	.byte	8,0,2,35,1,11
	.byte	'PORST',0,1
	.word	757
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	757
	.byte	1,6,2,35,2,11
	.byte	'CB0',0,1
	.word	757
	.byte	1,5,2,35,2,11
	.byte	'CB1',0,1
	.word	757
	.byte	1,4,2,35,2,11
	.byte	'CB3',0,1
	.word	757
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	757
	.byte	2,1,2,35,2,11
	.byte	'EVR13',0,1
	.word	757
	.byte	1,0,2,35,2,11
	.byte	'EVR33',0,1
	.word	757
	.byte	1,7,2,35,3,11
	.byte	'SWD',0,1
	.word	757
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	757
	.byte	2,4,2,35,3,11
	.byte	'STBYR',0,1
	.word	757
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	757
	.byte	3,0,2,35,3,0,29
	.byte	'Ifx_SCU_RSTSTAT_Bits',0,4,155,8,3
	.word	50031
	.byte	10
	.byte	'_Ifx_SCU_SAFECON_Bits',0,4,158,8,16,4,11
	.byte	'HBT',0,1
	.word	757
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	582
	.byte	31,0,2,35,0,0,29
	.byte	'Ifx_SCU_SAFECON_Bits',0,4,162,8,3
	.word	50463
	.byte	10
	.byte	'_Ifx_SCU_STSTAT_Bits',0,4,165,8,16,4,11
	.byte	'HWCFG',0,1
	.word	757
	.byte	8,0,2,35,0,11
	.byte	'FTM',0,1
	.word	757
	.byte	7,1,2,35,1,11
	.byte	'MODE',0,1
	.word	757
	.byte	1,0,2,35,1,11
	.byte	'FCBAE',0,1
	.word	757
	.byte	1,7,2,35,2,11
	.byte	'LUDIS',0,1
	.word	757
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	757
	.byte	1,5,2,35,2,11
	.byte	'TRSTL',0,1
	.word	757
	.byte	1,4,2,35,2,11
	.byte	'SPDEN',0,1
	.word	757
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	757
	.byte	3,0,2,35,2,11
	.byte	'RAMINT',0,1
	.word	757
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	757
	.byte	7,0,2,35,3,0,29
	.byte	'Ifx_SCU_STSTAT_Bits',0,4,178,8,3
	.word	50559
	.byte	10
	.byte	'_Ifx_SCU_SWRSTCON_Bits',0,4,181,8,16,4,11
	.byte	'reserved_0',0,1
	.word	757
	.byte	1,7,2,35,0,11
	.byte	'SWRSTREQ',0,1
	.word	757
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	582
	.byte	30,0,2,35,0,0,29
	.byte	'Ifx_SCU_SWRSTCON_Bits',0,4,186,8,3
	.word	50819
	.byte	10
	.byte	'_Ifx_SCU_SYSCON_Bits',0,4,189,8,16,4,11
	.byte	'CCTRIG0',0,1
	.word	757
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	757
	.byte	1,6,2,35,0,11
	.byte	'RAMINTM',0,1
	.word	757
	.byte	2,4,2,35,0,11
	.byte	'SETLUDIS',0,1
	.word	757
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	757
	.byte	3,0,2,35,0,11
	.byte	'DATM',0,1
	.word	757
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,4
	.word	582
	.byte	23,0,2,35,0,0,29
	.byte	'Ifx_SCU_SYSCON_Bits',0,4,198,8,3
	.word	50944
	.byte	10
	.byte	'_Ifx_SCU_TRAPCLR_Bits',0,4,201,8,16,4,11
	.byte	'ESR0T',0,1
	.word	757
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	757
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	757
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	757
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	582
	.byte	28,0,2,35,0,0,29
	.byte	'Ifx_SCU_TRAPCLR_Bits',0,4,208,8,3
	.word	51141
	.byte	10
	.byte	'_Ifx_SCU_TRAPDIS_Bits',0,4,211,8,16,4,11
	.byte	'ESR0T',0,1
	.word	757
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	757
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	757
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	757
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	582
	.byte	28,0,2,35,0,0,29
	.byte	'Ifx_SCU_TRAPDIS_Bits',0,4,218,8,3
	.word	51294
	.byte	10
	.byte	'_Ifx_SCU_TRAPSET_Bits',0,4,221,8,16,4,11
	.byte	'ESR0T',0,1
	.word	757
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	757
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	757
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	757
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	582
	.byte	28,0,2,35,0,0,29
	.byte	'Ifx_SCU_TRAPSET_Bits',0,4,228,8,3
	.word	51447
	.byte	10
	.byte	'_Ifx_SCU_TRAPSTAT_Bits',0,4,231,8,16,4,11
	.byte	'ESR0T',0,1
	.word	757
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	757
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	757
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	757
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	582
	.byte	28,0,2,35,0,0,29
	.byte	'Ifx_SCU_TRAPSTAT_Bits',0,4,238,8,3
	.word	51600
	.byte	29
	.byte	'Ifx_SCU_WDTCPU_CON0_Bits',0,4,247,8,3
	.word	621
	.byte	29
	.byte	'Ifx_SCU_WDTCPU_CON1_Bits',0,4,134,9,3
	.word	796
	.byte	29
	.byte	'Ifx_SCU_WDTCPU_SR_Bits',0,4,150,9,3
	.word	1040
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON0_Bits',0,4,153,9,16,4,11
	.byte	'ENDINIT',0,4
	.word	605
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	605
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	605
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	605
	.byte	16,0,2,35,0,0,29
	.byte	'Ifx_SCU_WDTS_CON0_Bits',0,4,159,9,3
	.word	51855
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON1_Bits',0,4,162,9,16,4,11
	.byte	'CLRIRF',0,1
	.word	757
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	757
	.byte	1,6,2,35,0,11
	.byte	'IR0',0,1
	.word	757
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	757
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	757
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	757
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	757
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	757
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	757
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	757
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	774
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_SCU_WDTS_CON1_Bits',0,4,175,9,3
	.word	51981
	.byte	10
	.byte	'_Ifx_SCU_WDTS_SR_Bits',0,4,178,9,16,4,11
	.byte	'AE',0,1
	.word	757
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	757
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	757
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	757
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	757
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	757
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	757
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	757
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	757
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	757
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	774
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_SCU_WDTS_SR_Bits',0,4,191,9,3
	.word	52233
	.byte	12,4,199,9,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34389
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_ACCEN0',0,4,204,9,3
	.word	52452
	.byte	12,4,207,9,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34946
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_ACCEN1',0,4,212,9,3
	.word	52516
	.byte	12,4,215,9,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35023
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_ARSTDIS',0,4,220,9,3
	.word	52580
	.byte	12,4,223,9,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35159
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_CCUCON0',0,4,228,9,3
	.word	52645
	.byte	12,4,231,9,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35439
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_CCUCON1',0,4,236,9,3
	.word	52710
	.byte	12,4,239,9,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35677
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_CCUCON2',0,4,244,9,3
	.word	52775
	.byte	12,4,247,9,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35805
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_CCUCON3',0,4,252,9,3
	.word	52840
	.byte	12,4,255,9,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36048
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_CCUCON4',0,4,132,10,3
	.word	52905
	.byte	12,4,135,10,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36283
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_CCUCON5',0,4,140,10,3
	.word	52970
	.byte	12,4,143,10,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36411
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_CCUCON6',0,4,148,10,3
	.word	53035
	.byte	12,4,151,10,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36511
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_CCUCON7',0,4,156,10,3
	.word	53100
	.byte	12,4,159,10,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36611
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_CHIPID',0,4,164,10,3
	.word	53165
	.byte	12,4,167,10,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36819
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_DTSCON',0,4,172,10,3
	.word	53229
	.byte	12,4,175,10,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36984
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_DTSLIM',0,4,180,10,3
	.word	53293
	.byte	12,4,183,10,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37167
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_DTSSTAT',0,4,188,10,3
	.word	53357
	.byte	12,4,191,10,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37321
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EICR',0,4,196,10,3
	.word	53422
	.byte	12,4,199,10,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37685
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EIFR',0,4,204,10,3
	.word	53484
	.byte	12,4,207,10,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37896
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EMSR',0,4,212,10,3
	.word	53546
	.byte	12,4,215,10,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38148
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_ESRCFG',0,4,220,10,3
	.word	53608
	.byte	12,4,223,10,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38266
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_ESROCFG',0,4,228,10,3
	.word	53672
	.byte	12,4,231,10,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38377
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVR13CON',0,4,236,10,3
	.word	53737
	.byte	12,4,239,10,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38540
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVR33CON',0,4,244,10,3
	.word	53803
	.byte	12,4,247,10,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38703
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRADCSTAT',0,4,252,10,3
	.word	53869
	.byte	12,4,255,10,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38861
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRDVSTAT',0,4,132,11,3
	.word	53937
	.byte	12,4,135,11,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39026
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRMONCTRL',0,4,140,11,3
	.word	54004
	.byte	12,4,143,11,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39355
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVROSCCTRL',0,4,148,11,3
	.word	54072
	.byte	12,4,151,11,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39576
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVROVMON',0,4,156,11,3
	.word	54140
	.byte	12,4,159,11,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39739
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRRSTCON',0,4,164,11,3
	.word	54206
	.byte	12,4,167,11,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40011
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF1',0,4,172,11,3
	.word	54273
	.byte	12,4,175,11,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40164
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF2',0,4,180,11,3
	.word	54342
	.byte	12,4,183,11,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40320
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF3',0,4,188,11,3
	.word	54411
	.byte	12,4,191,11,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40482
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF4',0,4,196,11,3
	.word	54480
	.byte	12,4,199,11,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40625
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF5',0,4,204,11,3
	.word	54549
	.byte	12,4,207,11,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40790
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF6',0,4,212,11,3
	.word	54618
	.byte	12,4,215,11,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40935
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSDCTRL1',0,4,220,11,3
	.word	54687
	.byte	12,4,223,11,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41116
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSDCTRL2',0,4,228,11,3
	.word	54755
	.byte	12,4,231,11,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41290
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSDCTRL3',0,4,236,11,3
	.word	54823
	.byte	12,4,239,11,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41450
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSDCTRL4',0,4,244,11,3
	.word	54891
	.byte	12,4,247,11,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41594
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSTAT',0,4,252,11,3
	.word	54959
	.byte	12,4,255,11,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41868
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRTRIM',0,4,132,12,3
	.word	55024
	.byte	12,4,135,12,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42007
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRUVMON',0,4,140,12,3
	.word	55089
	.byte	12,4,143,12,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42170
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EXTCON',0,4,148,12,3
	.word	55155
	.byte	12,4,151,12,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42388
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_FDR',0,4,156,12,3
	.word	55219
	.byte	12,4,159,12,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42551
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_FMR',0,4,164,12,3
	.word	55280
	.byte	12,4,167,12,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42887
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_ID',0,4,172,12,3
	.word	55341
	.byte	12,4,175,12,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42994
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_IGCR',0,4,180,12,3
	.word	55401
	.byte	12,4,183,12,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43446
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_IN',0,4,188,12,3
	.word	55463
	.byte	12,4,191,12,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43545
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_IOCR',0,4,196,12,3
	.word	55523
	.byte	12,4,199,12,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43695
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_LBISTCTRL0',0,4,204,12,3
	.word	55585
	.byte	12,4,207,12,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43844
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_LBISTCTRL1',0,4,212,12,3
	.word	55653
	.byte	12,4,215,12,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44005
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_LBISTCTRL2',0,4,220,12,3
	.word	55721
	.byte	12,4,223,12,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44135
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_LCLCON',0,4,228,12,3
	.word	55789
	.byte	12,4,231,12,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44267
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_LCLTEST',0,4,236,12,3
	.word	55853
	.byte	12,4,239,12,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44382
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_MANID',0,4,244,12,3
	.word	55918
	.byte	12,4,247,12,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44493
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_OMR',0,4,252,12,3
	.word	55981
	.byte	12,4,255,12,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44651
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_OSCCON',0,4,132,13,3
	.word	56042
	.byte	12,4,135,13,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45063
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_OUT',0,4,140,13,3
	.word	56106
	.byte	12,4,143,13,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45164
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_OVCCON',0,4,148,13,3
	.word	56167
	.byte	12,4,151,13,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45431
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_OVCENABLE',0,4,156,13,3
	.word	56231
	.byte	12,4,159,13,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45567
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PDISC',0,4,164,13,3
	.word	56298
	.byte	12,4,167,13,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45678
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PDR',0,4,172,13,3
	.word	56361
	.byte	12,4,175,13,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45811
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PDRR',0,4,180,13,3
	.word	56422
	.byte	12,4,183,13,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46014
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PLLCON0',0,4,188,13,3
	.word	56484
	.byte	12,4,191,13,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46370
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PLLCON1',0,4,196,13,3
	.word	56549
	.byte	12,4,199,13,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46548
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PLLCON2',0,4,204,13,3
	.word	56614
	.byte	12,4,207,13,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46648
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PLLERAYCON0',0,4,212,13,3
	.word	56679
	.byte	12,4,215,13,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47018
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PLLERAYCON1',0,4,220,13,3
	.word	56748
	.byte	12,4,223,13,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47204
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PLLERAYSTAT',0,4,228,13,3
	.word	56817
	.byte	12,4,231,13,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47402
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PLLSTAT',0,4,236,13,3
	.word	56886
	.byte	29
	.byte	'Ifx_SCU_PMCSR',0,4,244,13,3
	.word	22367
	.byte	12,4,247,13,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47663
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PMSWCR0',0,4,252,13,3
	.word	56974
	.byte	12,4,255,13,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48230
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PMSWCR1',0,4,132,14,3
	.word	57039
	.byte	12,4,135,14,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48524
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PMSWCR2',0,4,140,14,3
	.word	57104
	.byte	12,4,143,14,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48802
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PMSWSTAT',0,4,148,14,3
	.word	57169
	.byte	12,4,151,14,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49298
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PMSWSTATCLR',0,4,156,14,3
	.word	57235
	.byte	12,4,159,14,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49820
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_RSTCON',0,4,164,14,3
	.word	57304
	.byte	12,4,167,14,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49611
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_RSTCON2',0,4,172,14,3
	.word	57368
	.byte	12,4,175,14,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50031
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_RSTSTAT',0,4,180,14,3
	.word	57433
	.byte	12,4,183,14,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50463
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_SAFECON',0,4,188,14,3
	.word	57498
	.byte	12,4,191,14,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50559
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_STSTAT',0,4,196,14,3
	.word	57563
	.byte	12,4,199,14,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50819
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_SWRSTCON',0,4,204,14,3
	.word	57627
	.byte	12,4,207,14,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50944
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_SYSCON',0,4,212,14,3
	.word	57693
	.byte	12,4,215,14,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51141
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_TRAPCLR',0,4,220,14,3
	.word	57757
	.byte	12,4,223,14,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51294
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_TRAPDIS',0,4,228,14,3
	.word	57822
	.byte	12,4,231,14,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51447
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_TRAPSET',0,4,236,14,3
	.word	57887
	.byte	12,4,239,14,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51600
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_TRAPSTAT',0,4,244,14,3
	.word	57952
	.byte	29
	.byte	'Ifx_SCU_WDTCPU_CON0',0,4,252,14,3
	.word	717
	.byte	29
	.byte	'Ifx_SCU_WDTCPU_CON1',0,4,132,15,3
	.word	1000
	.byte	29
	.byte	'Ifx_SCU_WDTCPU_SR',0,4,140,15,3
	.word	1231
	.byte	12,4,143,15,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51855
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_WDTS_CON0',0,4,148,15,3
	.word	58103
	.byte	12,4,151,15,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51981
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_WDTS_CON1',0,4,156,15,3
	.word	58170
	.byte	12,4,159,15,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52233
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_WDTS_SR',0,4,164,15,3
	.word	58237
	.byte	14
	.word	1271
	.byte	29
	.byte	'Ifx_SCU_WDTCPU',0,4,180,15,3
	.word	58302
	.byte	10
	.byte	'_Ifx_SCU_WDTS',0,4,183,15,25,12,13
	.byte	'CON0',0
	.word	58103
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	58170
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	58237
	.byte	4,2,35,8,0,14
	.word	58331
	.byte	29
	.byte	'Ifx_SCU_WDTS',0,4,188,15,3
	.word	58392
	.byte	15,8
	.word	53608
	.byte	16,1,0,15,20
	.word	757
	.byte	16,19,0,15,8
	.word	22367
	.byte	16,1,0,14
	.word	58331
	.byte	15,24
	.word	1271
	.byte	16,1,0,14
	.word	58451
	.byte	15,16
	.word	53422
	.byte	16,3,0,15,16
	.word	55401
	.byte	16,3,0,15,180,3
	.word	757
	.byte	16,179,3,0,10
	.byte	'_Ifx_SCU',0,4,201,15,25,128,8,13
	.byte	'reserved_0',0
	.word	4394
	.byte	8,2,35,0,13
	.byte	'ID',0
	.word	55341
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	2575
	.byte	4,2,35,12,13
	.byte	'OSCCON',0
	.word	56042
	.byte	4,2,35,16,13
	.byte	'PLLSTAT',0
	.word	56886
	.byte	4,2,35,20,13
	.byte	'PLLCON0',0
	.word	56484
	.byte	4,2,35,24,13
	.byte	'PLLCON1',0
	.word	56549
	.byte	4,2,35,28,13
	.byte	'PLLCON2',0
	.word	56614
	.byte	4,2,35,32,13
	.byte	'PLLERAYSTAT',0
	.word	56817
	.byte	4,2,35,36,13
	.byte	'PLLERAYCON0',0
	.word	56679
	.byte	4,2,35,40,13
	.byte	'PLLERAYCON1',0
	.word	56748
	.byte	4,2,35,44,13
	.byte	'CCUCON0',0
	.word	52645
	.byte	4,2,35,48,13
	.byte	'CCUCON1',0
	.word	52710
	.byte	4,2,35,52,13
	.byte	'FDR',0
	.word	55219
	.byte	4,2,35,56,13
	.byte	'EXTCON',0
	.word	55155
	.byte	4,2,35,60,13
	.byte	'CCUCON2',0
	.word	52775
	.byte	4,2,35,64,13
	.byte	'CCUCON3',0
	.word	52840
	.byte	4,2,35,68,13
	.byte	'CCUCON4',0
	.word	52905
	.byte	4,2,35,72,13
	.byte	'CCUCON5',0
	.word	52970
	.byte	4,2,35,76,13
	.byte	'RSTSTAT',0
	.word	57433
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	2575
	.byte	4,2,35,84,13
	.byte	'RSTCON',0
	.word	57304
	.byte	4,2,35,88,13
	.byte	'ARSTDIS',0
	.word	52580
	.byte	4,2,35,92,13
	.byte	'SWRSTCON',0
	.word	57627
	.byte	4,2,35,96,13
	.byte	'RSTCON2',0
	.word	57368
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	2575
	.byte	4,2,35,104,13
	.byte	'EVRRSTCON',0
	.word	54206
	.byte	4,2,35,108,13
	.byte	'ESRCFG',0
	.word	58419
	.byte	8,2,35,112,13
	.byte	'ESROCFG',0
	.word	53672
	.byte	4,2,35,120,13
	.byte	'SYSCON',0
	.word	57693
	.byte	4,2,35,124,13
	.byte	'CCUCON6',0
	.word	53035
	.byte	4,3,35,128,1,13
	.byte	'CCUCON7',0
	.word	53100
	.byte	4,3,35,132,1,13
	.byte	'reserved_88',0
	.word	58428
	.byte	20,3,35,136,1,13
	.byte	'PDR',0
	.word	56361
	.byte	4,3,35,156,1,13
	.byte	'IOCR',0
	.word	55523
	.byte	4,3,35,160,1,13
	.byte	'OUT',0
	.word	56106
	.byte	4,3,35,164,1,13
	.byte	'OMR',0
	.word	55981
	.byte	4,3,35,168,1,13
	.byte	'IN',0
	.word	55463
	.byte	4,3,35,172,1,13
	.byte	'EVRSTAT',0
	.word	54959
	.byte	4,3,35,176,1,13
	.byte	'EVRDVSTAT',0
	.word	53937
	.byte	4,3,35,180,1,13
	.byte	'EVR13CON',0
	.word	53737
	.byte	4,3,35,184,1,13
	.byte	'EVR33CON',0
	.word	53803
	.byte	4,3,35,188,1,13
	.byte	'STSTAT',0
	.word	57563
	.byte	4,3,35,192,1,13
	.byte	'reserved_C4',0
	.word	2575
	.byte	4,3,35,196,1,13
	.byte	'PMSWCR0',0
	.word	56974
	.byte	4,3,35,200,1,13
	.byte	'PMSWSTAT',0
	.word	57169
	.byte	4,3,35,204,1,13
	.byte	'PMSWSTATCLR',0
	.word	57235
	.byte	4,3,35,208,1,13
	.byte	'PMCSR',0
	.word	58437
	.byte	8,3,35,212,1,13
	.byte	'reserved_DC',0
	.word	2575
	.byte	4,3,35,220,1,13
	.byte	'DTSSTAT',0
	.word	53357
	.byte	4,3,35,224,1,13
	.byte	'DTSCON',0
	.word	53229
	.byte	4,3,35,228,1,13
	.byte	'PMSWCR1',0
	.word	57039
	.byte	4,3,35,232,1,13
	.byte	'PMSWCR2',0
	.word	57104
	.byte	4,3,35,236,1,13
	.byte	'WDTS',0
	.word	58446
	.byte	12,3,35,240,1,13
	.byte	'EMSR',0
	.word	53546
	.byte	4,3,35,252,1,13
	.byte	'WDTCPU',0
	.word	58460
	.byte	24,3,35,128,2,13
	.byte	'reserved_118',0
	.word	4734
	.byte	12,3,35,152,2,13
	.byte	'TRAPSTAT',0
	.word	57952
	.byte	4,3,35,164,2,13
	.byte	'TRAPSET',0
	.word	57887
	.byte	4,3,35,168,2,13
	.byte	'TRAPCLR',0
	.word	57757
	.byte	4,3,35,172,2,13
	.byte	'TRAPDIS',0
	.word	57822
	.byte	4,3,35,176,2,13
	.byte	'reserved_134',0
	.word	2575
	.byte	4,3,35,180,2,13
	.byte	'LCLCON1',0
	.word	55789
	.byte	4,3,35,184,2,13
	.byte	'LCLTEST',0
	.word	55853
	.byte	4,3,35,188,2,13
	.byte	'CHIPID',0
	.word	53165
	.byte	4,3,35,192,2,13
	.byte	'MANID',0
	.word	55918
	.byte	4,3,35,196,2,13
	.byte	'reserved_148',0
	.word	4394
	.byte	8,3,35,200,2,13
	.byte	'SAFECON',0
	.word	57498
	.byte	4,3,35,208,2,13
	.byte	'reserved_154',0
	.word	18900
	.byte	16,3,35,212,2,13
	.byte	'LBISTCTRL0',0
	.word	55585
	.byte	4,3,35,228,2,13
	.byte	'LBISTCTRL1',0
	.word	55653
	.byte	4,3,35,232,2,13
	.byte	'LBISTCTRL2',0
	.word	55721
	.byte	4,3,35,236,2,13
	.byte	'reserved_170',0
	.word	16785
	.byte	28,3,35,240,2,13
	.byte	'PDISC',0
	.word	56298
	.byte	4,3,35,140,3,13
	.byte	'reserved_190',0
	.word	4394
	.byte	8,3,35,144,3,13
	.byte	'EVRTRIM',0
	.word	55024
	.byte	4,3,35,152,3,13
	.byte	'EVRADCSTAT',0
	.word	53869
	.byte	4,3,35,156,3,13
	.byte	'EVRUVMON',0
	.word	55089
	.byte	4,3,35,160,3,13
	.byte	'EVROVMON',0
	.word	54140
	.byte	4,3,35,164,3,13
	.byte	'EVRMONCTRL',0
	.word	54004
	.byte	4,3,35,168,3,13
	.byte	'reserved_1AC',0
	.word	2575
	.byte	4,3,35,172,3,13
	.byte	'EVRSDCTRL1',0
	.word	54687
	.byte	4,3,35,176,3,13
	.byte	'EVRSDCTRL2',0
	.word	54755
	.byte	4,3,35,180,3,13
	.byte	'EVRSDCTRL3',0
	.word	54823
	.byte	4,3,35,184,3,13
	.byte	'EVRSDCTRL4',0
	.word	54891
	.byte	4,3,35,188,3,13
	.byte	'EVRSDCOEFF1',0
	.word	54273
	.byte	4,3,35,192,3,13
	.byte	'EVRSDCOEFF2',0
	.word	54342
	.byte	4,3,35,196,3,13
	.byte	'EVRSDCOEFF3',0
	.word	54411
	.byte	4,3,35,200,3,13
	.byte	'EVRSDCOEFF4',0
	.word	54480
	.byte	4,3,35,204,3,13
	.byte	'EVRSDCOEFF5',0
	.word	54549
	.byte	4,3,35,208,3,13
	.byte	'EVRSDCOEFF6',0
	.word	54618
	.byte	4,3,35,212,3,13
	.byte	'EVROSCCTRL',0
	.word	54072
	.byte	4,3,35,216,3,13
	.byte	'reserved_1DC',0
	.word	2575
	.byte	4,3,35,220,3,13
	.byte	'OVCENABLE',0
	.word	56231
	.byte	4,3,35,224,3,13
	.byte	'OVCCON',0
	.word	56167
	.byte	4,3,35,228,3,13
	.byte	'reserved_1E8',0
	.word	29034
	.byte	40,3,35,232,3,13
	.byte	'EICR',0
	.word	58465
	.byte	16,3,35,144,4,13
	.byte	'EIFR',0
	.word	53484
	.byte	4,3,35,160,4,13
	.byte	'FMR',0
	.word	55280
	.byte	4,3,35,164,4,13
	.byte	'PDRR',0
	.word	56422
	.byte	4,3,35,168,4,13
	.byte	'IGCR',0
	.word	58474
	.byte	16,3,35,172,4,13
	.byte	'reserved_23C',0
	.word	2575
	.byte	4,3,35,188,4,13
	.byte	'DTSLIM',0
	.word	53293
	.byte	4,3,35,192,4,13
	.byte	'reserved_244',0
	.word	58483
	.byte	180,3,3,35,196,4,13
	.byte	'ACCEN1',0
	.word	52516
	.byte	4,3,35,248,7,13
	.byte	'ACCEN0',0
	.word	52452
	.byte	4,3,35,252,7,0,14
	.word	58494
	.byte	29
	.byte	'Ifx_SCU',0,4,181,16,3
	.word	60484
	.byte	10
	.byte	'_Ifx_STM_ACCEN0_Bits',0,15,45,16,4,11
	.byte	'EN0',0,1
	.word	757
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	757
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	757
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	757
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	757
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	757
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	757
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	757
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	757
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	757
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	757
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	757
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	757
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	757
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	757
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	757
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	757
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	757
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	757
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	757
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	757
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	757
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	757
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	757
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	757
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	757
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	757
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	757
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	757
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	757
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	757
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	757
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_STM_ACCEN0_Bits',0,15,79,3
	.word	60506
	.byte	10
	.byte	'_Ifx_STM_ACCEN1_Bits',0,15,82,16,4,11
	.byte	'reserved_0',0,4
	.word	582
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_STM_ACCEN1_Bits',0,15,85,3
	.word	61063
	.byte	10
	.byte	'_Ifx_STM_CAP_Bits',0,15,88,16,4,11
	.byte	'STMCAP63_32',0,4
	.word	582
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_STM_CAP_Bits',0,15,91,3
	.word	61140
	.byte	10
	.byte	'_Ifx_STM_CAPSV_Bits',0,15,94,16,4,11
	.byte	'STMCAP63_32',0,4
	.word	582
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_STM_CAPSV_Bits',0,15,97,3
	.word	61212
	.byte	10
	.byte	'_Ifx_STM_CLC_Bits',0,15,100,16,4,11
	.byte	'DISR',0,1
	.word	757
	.byte	1,7,2,35,0,11
	.byte	'DISS',0,1
	.word	757
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	757
	.byte	1,5,2,35,0,11
	.byte	'EDIS',0,1
	.word	757
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	582
	.byte	28,0,2,35,0,0,29
	.byte	'Ifx_STM_CLC_Bits',0,15,107,3
	.word	61288
	.byte	10
	.byte	'_Ifx_STM_CMCON_Bits',0,15,110,16,4,11
	.byte	'MSIZE0',0,1
	.word	757
	.byte	5,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	757
	.byte	3,0,2,35,0,11
	.byte	'MSTART0',0,1
	.word	757
	.byte	5,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	757
	.byte	3,0,2,35,1,11
	.byte	'MSIZE1',0,1
	.word	757
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	757
	.byte	3,0,2,35,2,11
	.byte	'MSTART1',0,1
	.word	757
	.byte	5,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	757
	.byte	3,0,2,35,3,0,29
	.byte	'Ifx_STM_CMCON_Bits',0,15,120,3
	.word	61429
	.byte	10
	.byte	'_Ifx_STM_CMP_Bits',0,15,123,16,4,11
	.byte	'CMPVAL',0,4
	.word	582
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_STM_CMP_Bits',0,15,126,3
	.word	61647
	.byte	10
	.byte	'_Ifx_STM_ICR_Bits',0,15,129,1,16,4,11
	.byte	'CMP0EN',0,1
	.word	757
	.byte	1,7,2,35,0,11
	.byte	'CMP0IR',0,1
	.word	757
	.byte	1,6,2,35,0,11
	.byte	'CMP0OS',0,1
	.word	757
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	757
	.byte	1,4,2,35,0,11
	.byte	'CMP1EN',0,1
	.word	757
	.byte	1,3,2,35,0,11
	.byte	'CMP1IR',0,1
	.word	757
	.byte	1,2,2,35,0,11
	.byte	'CMP1OS',0,1
	.word	757
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,4
	.word	582
	.byte	25,0,2,35,0,0,29
	.byte	'Ifx_STM_ICR_Bits',0,15,139,1,3
	.word	61714
	.byte	10
	.byte	'_Ifx_STM_ID_Bits',0,15,142,1,16,4,11
	.byte	'MODREV',0,1
	.word	757
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	757
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	774
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_STM_ID_Bits',0,15,147,1,3
	.word	61917
	.byte	10
	.byte	'_Ifx_STM_ISCR_Bits',0,15,150,1,16,4,11
	.byte	'CMP0IRR',0,1
	.word	757
	.byte	1,7,2,35,0,11
	.byte	'CMP0IRS',0,1
	.word	757
	.byte	1,6,2,35,0,11
	.byte	'CMP1IRR',0,1
	.word	757
	.byte	1,5,2,35,0,11
	.byte	'CMP1IRS',0,1
	.word	757
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	582
	.byte	28,0,2,35,0,0,29
	.byte	'Ifx_STM_ISCR_Bits',0,15,157,1,3
	.word	62024
	.byte	10
	.byte	'_Ifx_STM_KRST0_Bits',0,15,160,1,16,4,11
	.byte	'RST',0,1
	.word	757
	.byte	1,7,2,35,0,11
	.byte	'RSTSTAT',0,1
	.word	757
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	582
	.byte	30,0,2,35,0,0,29
	.byte	'Ifx_STM_KRST0_Bits',0,15,165,1,3
	.word	62175
	.byte	10
	.byte	'_Ifx_STM_KRST1_Bits',0,15,168,1,16,4,11
	.byte	'RST',0,1
	.word	757
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	582
	.byte	31,0,2,35,0,0,29
	.byte	'Ifx_STM_KRST1_Bits',0,15,172,1,3
	.word	62286
	.byte	10
	.byte	'_Ifx_STM_KRSTCLR_Bits',0,15,175,1,16,4,11
	.byte	'CLR',0,1
	.word	757
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	582
	.byte	31,0,2,35,0,0,29
	.byte	'Ifx_STM_KRSTCLR_Bits',0,15,179,1,3
	.word	62378
	.byte	10
	.byte	'_Ifx_STM_OCS_Bits',0,15,182,1,16,4,11
	.byte	'reserved_0',0,4
	.word	582
	.byte	24,8,2,35,0,11
	.byte	'SUS',0,1
	.word	757
	.byte	4,4,2,35,3,11
	.byte	'SUS_P',0,1
	.word	757
	.byte	1,3,2,35,3,11
	.byte	'SUSSTA',0,1
	.word	757
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	757
	.byte	2,0,2,35,3,0,29
	.byte	'Ifx_STM_OCS_Bits',0,15,189,1,3
	.word	62474
	.byte	10
	.byte	'_Ifx_STM_TIM0_Bits',0,15,192,1,16,4,11
	.byte	'STM31_0',0,4
	.word	582
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_STM_TIM0_Bits',0,15,195,1,3
	.word	62620
	.byte	10
	.byte	'_Ifx_STM_TIM0SV_Bits',0,15,198,1,16,4,11
	.byte	'STM31_0',0,4
	.word	582
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_STM_TIM0SV_Bits',0,15,201,1,3
	.word	62692
	.byte	10
	.byte	'_Ifx_STM_TIM1_Bits',0,15,204,1,16,4,11
	.byte	'STM35_4',0,4
	.word	582
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_STM_TIM1_Bits',0,15,207,1,3
	.word	62768
	.byte	10
	.byte	'_Ifx_STM_TIM2_Bits',0,15,210,1,16,4,11
	.byte	'STM39_8',0,4
	.word	582
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_STM_TIM2_Bits',0,15,213,1,3
	.word	62840
	.byte	10
	.byte	'_Ifx_STM_TIM3_Bits',0,15,216,1,16,4,11
	.byte	'STM43_12',0,4
	.word	582
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_STM_TIM3_Bits',0,15,219,1,3
	.word	62912
	.byte	10
	.byte	'_Ifx_STM_TIM4_Bits',0,15,222,1,16,4,11
	.byte	'STM47_16',0,4
	.word	582
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_STM_TIM4_Bits',0,15,225,1,3
	.word	62985
	.byte	10
	.byte	'_Ifx_STM_TIM5_Bits',0,15,228,1,16,4,11
	.byte	'STM51_20',0,4
	.word	582
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_STM_TIM5_Bits',0,15,231,1,3
	.word	63058
	.byte	10
	.byte	'_Ifx_STM_TIM6_Bits',0,15,234,1,16,4,11
	.byte	'STM63_32',0,4
	.word	582
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_STM_TIM6_Bits',0,15,237,1,3
	.word	63131
	.byte	12,15,245,1,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60506
	.byte	4,2,35,0,0,29
	.byte	'Ifx_STM_ACCEN0',0,15,250,1,3
	.word	63204
	.byte	12,15,253,1,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61063
	.byte	4,2,35,0,0,29
	.byte	'Ifx_STM_ACCEN1',0,15,130,2,3
	.word	63268
	.byte	12,15,133,2,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61140
	.byte	4,2,35,0,0,29
	.byte	'Ifx_STM_CAP',0,15,138,2,3
	.word	63332
	.byte	12,15,141,2,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61212
	.byte	4,2,35,0,0,29
	.byte	'Ifx_STM_CAPSV',0,15,146,2,3
	.word	63393
	.byte	12,15,149,2,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61288
	.byte	4,2,35,0,0,29
	.byte	'Ifx_STM_CLC',0,15,154,2,3
	.word	63456
	.byte	12,15,157,2,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61429
	.byte	4,2,35,0,0,29
	.byte	'Ifx_STM_CMCON',0,15,162,2,3
	.word	63517
	.byte	12,15,165,2,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61647
	.byte	4,2,35,0,0,29
	.byte	'Ifx_STM_CMP',0,15,170,2,3
	.word	63580
	.byte	12,15,173,2,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61714
	.byte	4,2,35,0,0,29
	.byte	'Ifx_STM_ICR',0,15,178,2,3
	.word	63641
	.byte	12,15,181,2,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61917
	.byte	4,2,35,0,0,29
	.byte	'Ifx_STM_ID',0,15,186,2,3
	.word	63702
	.byte	12,15,189,2,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62024
	.byte	4,2,35,0,0,29
	.byte	'Ifx_STM_ISCR',0,15,194,2,3
	.word	63762
	.byte	12,15,197,2,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62175
	.byte	4,2,35,0,0,29
	.byte	'Ifx_STM_KRST0',0,15,202,2,3
	.word	63824
	.byte	12,15,205,2,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62286
	.byte	4,2,35,0,0,29
	.byte	'Ifx_STM_KRST1',0,15,210,2,3
	.word	63887
	.byte	12,15,213,2,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62378
	.byte	4,2,35,0,0,29
	.byte	'Ifx_STM_KRSTCLR',0,15,218,2,3
	.word	63950
	.byte	12,15,221,2,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62474
	.byte	4,2,35,0,0,29
	.byte	'Ifx_STM_OCS',0,15,226,2,3
	.word	64015
	.byte	12,15,229,2,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62620
	.byte	4,2,35,0,0,29
	.byte	'Ifx_STM_TIM0',0,15,234,2,3
	.word	64076
	.byte	12,15,237,2,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62692
	.byte	4,2,35,0,0,29
	.byte	'Ifx_STM_TIM0SV',0,15,242,2,3
	.word	64138
	.byte	12,15,245,2,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62768
	.byte	4,2,35,0,0,29
	.byte	'Ifx_STM_TIM1',0,15,250,2,3
	.word	64202
	.byte	12,15,253,2,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62840
	.byte	4,2,35,0,0,29
	.byte	'Ifx_STM_TIM2',0,15,130,3,3
	.word	64264
	.byte	12,15,133,3,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62912
	.byte	4,2,35,0,0,29
	.byte	'Ifx_STM_TIM3',0,15,138,3,3
	.word	64326
	.byte	12,15,141,3,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62985
	.byte	4,2,35,0,0,29
	.byte	'Ifx_STM_TIM4',0,15,146,3,3
	.word	64388
	.byte	12,15,149,3,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63058
	.byte	4,2,35,0,0,29
	.byte	'Ifx_STM_TIM5',0,15,154,3,3
	.word	64450
	.byte	12,15,157,3,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63131
	.byte	4,2,35,0,0,29
	.byte	'Ifx_STM_TIM6',0,15,162,3,3
	.word	64512
	.byte	17,16,236,10,9,1,18
	.byte	'IfxScu_CCUCON0_CLKSEL_fBack',0,0,18
	.byte	'IfxScu_CCUCON0_CLKSEL_fPll',0,1,0,29
	.byte	'IfxScu_CCUCON0_CLKSEL',0,16,240,10,3
	.word	64574
	.byte	17,16,250,10,9,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy16384',0,0,18
	.byte	'IfxScu_WDTCON1_IR_divBy256',0,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy64',0,2,0,29
	.byte	'IfxScu_WDTCON1_IR',0,16,255,10,3
	.word	64671
	.byte	10
	.byte	'_Ifx_FLASH_ACCEN0_Bits',0,17,45,16,4,11
	.byte	'EN0',0,1
	.word	757
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	757
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	757
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	757
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	757
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	757
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	757
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	757
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	757
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	757
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	757
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	757
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	757
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	757
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	757
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	757
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	757
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	757
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	757
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	757
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	757
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	757
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	757
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	757
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	757
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	757
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	757
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	757
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	757
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	757
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	757
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	757
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_FLASH_ACCEN0_Bits',0,17,79,3
	.word	64793
	.byte	10
	.byte	'_Ifx_FLASH_ACCEN1_Bits',0,17,82,16,4,11
	.byte	'reserved_0',0,4
	.word	582
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_FLASH_ACCEN1_Bits',0,17,85,3
	.word	65354
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_CFG_Bits',0,17,88,16,4,11
	.byte	'SEL',0,1
	.word	757
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	757
	.byte	2,0,2,35,0,11
	.byte	'CLR',0,1
	.word	757
	.byte	1,7,2,35,1,11
	.byte	'DIS',0,1
	.word	757
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	582
	.byte	22,0,2,35,0,0,29
	.byte	'Ifx_FLASH_CBAB_CFG_Bits',0,17,95,3
	.word	65435
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_STAT_Bits',0,17,98,16,4,11
	.byte	'VLD0',0,1
	.word	757
	.byte	1,7,2,35,0,11
	.byte	'VLD1',0,1
	.word	757
	.byte	1,6,2,35,0,11
	.byte	'VLD2',0,1
	.word	757
	.byte	1,5,2,35,0,11
	.byte	'VLD3',0,1
	.word	757
	.byte	1,4,2,35,0,11
	.byte	'VLD4',0,1
	.word	757
	.byte	1,3,2,35,0,11
	.byte	'VLD5',0,1
	.word	757
	.byte	1,2,2,35,0,11
	.byte	'VLD6',0,1
	.word	757
	.byte	1,1,2,35,0,11
	.byte	'VLD7',0,1
	.word	757
	.byte	1,0,2,35,0,11
	.byte	'VLD8',0,1
	.word	757
	.byte	1,7,2,35,1,11
	.byte	'VLD9',0,1
	.word	757
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	582
	.byte	22,0,2,35,0,0,29
	.byte	'Ifx_FLASH_CBAB_STAT_Bits',0,17,111,3
	.word	65588
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_TOP_Bits',0,17,114,16,4,11
	.byte	'reserved_0',0,1
	.word	757
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	582
	.byte	19,8,2,35,0,11
	.byte	'ERR',0,1
	.word	757
	.byte	6,2,2,35,3,11
	.byte	'VLD',0,1
	.word	757
	.byte	1,1,2,35,3,11
	.byte	'CLR',0,1
	.word	757
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_FLASH_CBAB_TOP_Bits',0,17,121,3
	.word	65836
	.byte	10
	.byte	'_Ifx_FLASH_COMM0_Bits',0,17,124,16,4,11
	.byte	'STATUS',0,1
	.word	757
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	582
	.byte	24,0,2,35,0,0,29
	.byte	'Ifx_FLASH_COMM0_Bits',0,17,128,1,3
	.word	65982
	.byte	10
	.byte	'_Ifx_FLASH_COMM1_Bits',0,17,131,1,16,4,11
	.byte	'STATUS',0,1
	.word	757
	.byte	8,0,2,35,0,11
	.byte	'DATA',0,1
	.word	757
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	774
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_FLASH_COMM1_Bits',0,17,136,1,3
	.word	66080
	.byte	10
	.byte	'_Ifx_FLASH_COMM2_Bits',0,17,139,1,16,4,11
	.byte	'STATUS',0,1
	.word	757
	.byte	8,0,2,35,0,11
	.byte	'DATA',0,1
	.word	757
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	774
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_FLASH_COMM2_Bits',0,17,144,1,3
	.word	66196
	.byte	10
	.byte	'_Ifx_FLASH_ECCRD_Bits',0,17,147,1,16,4,11
	.byte	'RCODE',0,4
	.word	582
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	774
	.byte	8,2,2,35,2,11
	.byte	'EDCERRINJ',0,1
	.word	757
	.byte	1,1,2,35,3,11
	.byte	'ECCORDIS',0,1
	.word	757
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_FLASH_ECCRD_Bits',0,17,153,1,3
	.word	66312
	.byte	10
	.byte	'_Ifx_FLASH_ECCRP_Bits',0,17,156,1,16,4,11
	.byte	'RCODE',0,4
	.word	582
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	774
	.byte	8,2,2,35,2,11
	.byte	'EDCERRINJ',0,1
	.word	757
	.byte	1,1,2,35,3,11
	.byte	'ECCORDIS',0,1
	.word	757
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_FLASH_ECCRP_Bits',0,17,162,1,3
	.word	66452
	.byte	10
	.byte	'_Ifx_FLASH_ECCW_Bits',0,17,165,1,16,4,11
	.byte	'WCODE',0,4
	.word	582
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	774
	.byte	8,2,2,35,2,11
	.byte	'DECENCDIS',0,1
	.word	757
	.byte	1,1,2,35,3,11
	.byte	'PECENCDIS',0,1
	.word	757
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_FLASH_ECCW_Bits',0,17,171,1,3
	.word	66592
	.byte	10
	.byte	'_Ifx_FLASH_FCON_Bits',0,17,174,1,16,4,11
	.byte	'WSPFLASH',0,1
	.word	757
	.byte	4,4,2,35,0,11
	.byte	'WSECPF',0,1
	.word	757
	.byte	2,2,2,35,0,11
	.byte	'WSDFLASH',0,2
	.word	774
	.byte	6,4,2,35,0,11
	.byte	'WSECDF',0,1
	.word	757
	.byte	3,1,2,35,1,11
	.byte	'IDLE',0,1
	.word	757
	.byte	1,0,2,35,1,11
	.byte	'ESLDIS',0,1
	.word	757
	.byte	1,7,2,35,2,11
	.byte	'SLEEP',0,1
	.word	757
	.byte	1,6,2,35,2,11
	.byte	'NSAFECC',0,1
	.word	757
	.byte	1,5,2,35,2,11
	.byte	'STALL',0,1
	.word	757
	.byte	1,4,2,35,2,11
	.byte	'RES21',0,1
	.word	757
	.byte	2,2,2,35,2,11
	.byte	'RES23',0,1
	.word	757
	.byte	2,0,2,35,2,11
	.byte	'VOPERM',0,1
	.word	757
	.byte	1,7,2,35,3,11
	.byte	'SQERM',0,1
	.word	757
	.byte	1,6,2,35,3,11
	.byte	'PROERM',0,1
	.word	757
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	757
	.byte	3,2,2,35,3,11
	.byte	'PR5V',0,1
	.word	757
	.byte	1,1,2,35,3,11
	.byte	'EOBM',0,1
	.word	757
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_FLASH_FCON_Bits',0,17,193,1,3
	.word	66731
	.byte	10
	.byte	'_Ifx_FLASH_FPRO_Bits',0,17,196,1,16,4,11
	.byte	'PROINP',0,1
	.word	757
	.byte	1,7,2,35,0,11
	.byte	'PRODISP',0,1
	.word	757
	.byte	1,6,2,35,0,11
	.byte	'PROIND',0,1
	.word	757
	.byte	1,5,2,35,0,11
	.byte	'PRODISD',0,1
	.word	757
	.byte	1,4,2,35,0,11
	.byte	'PROINHSMCOTP',0,1
	.word	757
	.byte	1,3,2,35,0,11
	.byte	'RES5',0,1
	.word	757
	.byte	1,2,2,35,0,11
	.byte	'PROINOTP',0,1
	.word	757
	.byte	1,1,2,35,0,11
	.byte	'RES7',0,1
	.word	757
	.byte	1,0,2,35,0,11
	.byte	'PROINDBG',0,1
	.word	757
	.byte	1,7,2,35,1,11
	.byte	'PRODISDBG',0,1
	.word	757
	.byte	1,6,2,35,1,11
	.byte	'PROINHSM',0,1
	.word	757
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	757
	.byte	5,0,2,35,1,11
	.byte	'DCFP',0,1
	.word	757
	.byte	1,7,2,35,2,11
	.byte	'DDFP',0,1
	.word	757
	.byte	1,6,2,35,2,11
	.byte	'DDFPX',0,1
	.word	757
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	757
	.byte	1,4,2,35,2,11
	.byte	'DDFD',0,1
	.word	757
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	757
	.byte	1,2,2,35,2,11
	.byte	'ENPE',0,1
	.word	757
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	757
	.byte	8,0,2,35,3,0,29
	.byte	'Ifx_FLASH_FPRO_Bits',0,17,218,1,3
	.word	67093
	.byte	10
	.byte	'_Ifx_FLASH_FSR_Bits',0,17,221,1,16,4,11
	.byte	'FABUSY',0,1
	.word	757
	.byte	1,7,2,35,0,11
	.byte	'D0BUSY',0,1
	.word	757
	.byte	1,6,2,35,0,11
	.byte	'RES1',0,1
	.word	757
	.byte	1,5,2,35,0,11
	.byte	'P0BUSY',0,1
	.word	757
	.byte	1,4,2,35,0,11
	.byte	'P1BUSY',0,1
	.word	757
	.byte	1,3,2,35,0,11
	.byte	'RES5',0,1
	.word	757
	.byte	1,2,2,35,0,11
	.byte	'RES6',0,1
	.word	757
	.byte	1,1,2,35,0,11
	.byte	'PROG',0,1
	.word	757
	.byte	1,0,2,35,0,11
	.byte	'ERASE',0,1
	.word	757
	.byte	1,7,2,35,1,11
	.byte	'PFPAGE',0,1
	.word	757
	.byte	1,6,2,35,1,11
	.byte	'DFPAGE',0,1
	.word	757
	.byte	1,5,2,35,1,11
	.byte	'OPER',0,1
	.word	757
	.byte	1,4,2,35,1,11
	.byte	'SQER',0,1
	.word	757
	.byte	1,3,2,35,1,11
	.byte	'PROER',0,1
	.word	757
	.byte	1,2,2,35,1,11
	.byte	'PFSBER',0,1
	.word	757
	.byte	1,1,2,35,1,11
	.byte	'PFDBER',0,1
	.word	757
	.byte	1,0,2,35,1,11
	.byte	'PFMBER',0,1
	.word	757
	.byte	1,7,2,35,2,11
	.byte	'RES17',0,1
	.word	757
	.byte	1,6,2,35,2,11
	.byte	'DFSBER',0,1
	.word	757
	.byte	1,5,2,35,2,11
	.byte	'DFDBER',0,1
	.word	757
	.byte	1,4,2,35,2,11
	.byte	'DFTBER',0,1
	.word	757
	.byte	1,3,2,35,2,11
	.byte	'DFMBER',0,1
	.word	757
	.byte	1,2,2,35,2,11
	.byte	'SRIADDERR',0,1
	.word	757
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	774
	.byte	2,7,2,35,2,11
	.byte	'PVER',0,1
	.word	757
	.byte	1,6,2,35,3,11
	.byte	'EVER',0,1
	.word	757
	.byte	1,5,2,35,3,11
	.byte	'SPND',0,1
	.word	757
	.byte	1,4,2,35,3,11
	.byte	'SLM',0,1
	.word	757
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	757
	.byte	1,2,2,35,3,11
	.byte	'ORIER',0,1
	.word	757
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	757
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_FLASH_FSR_Bits',0,17,254,1,3
	.word	67534
	.byte	10
	.byte	'_Ifx_FLASH_ID_Bits',0,17,129,2,16,4,11
	.byte	'MODREV',0,1
	.word	757
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	757
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	774
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_FLASH_ID_Bits',0,17,134,2,3
	.word	68140
	.byte	10
	.byte	'_Ifx_FLASH_MARD_Bits',0,17,137,2,16,4,11
	.byte	'HMARGIN',0,1
	.word	757
	.byte	1,7,2,35,0,11
	.byte	'SELD0',0,1
	.word	757
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	757
	.byte	1,5,2,35,0,11
	.byte	'SPND',0,1
	.word	757
	.byte	1,4,2,35,0,11
	.byte	'SPNDERR',0,1
	.word	757
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,2
	.word	774
	.byte	10,1,2,35,0,11
	.byte	'TRAPDIS',0,1
	.word	757
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	774
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_FLASH_MARD_Bits',0,17,147,2,3
	.word	68251
	.byte	10
	.byte	'_Ifx_FLASH_MARP_Bits',0,17,150,2,16,4,11
	.byte	'SELP0',0,1
	.word	757
	.byte	1,7,2,35,0,11
	.byte	'SELP1',0,1
	.word	757
	.byte	1,6,2,35,0,11
	.byte	'RES2',0,1
	.word	757
	.byte	1,5,2,35,0,11
	.byte	'RES3',0,1
	.word	757
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	774
	.byte	11,1,2,35,0,11
	.byte	'TRAPDIS',0,1
	.word	757
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	774
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_FLASH_MARP_Bits',0,17,159,2,3
	.word	68465
	.byte	10
	.byte	'_Ifx_FLASH_PROCOND_Bits',0,17,162,2,16,4,11
	.byte	'L',0,1
	.word	757
	.byte	1,7,2,35,0,11
	.byte	'NSAFECC',0,1
	.word	757
	.byte	1,6,2,35,0,11
	.byte	'RAMIN',0,1
	.word	757
	.byte	2,4,2,35,0,11
	.byte	'RAMINSEL',0,1
	.word	757
	.byte	4,0,2,35,0,11
	.byte	'OSCCFG',0,1
	.word	757
	.byte	1,7,2,35,1,11
	.byte	'MODE',0,1
	.word	757
	.byte	2,5,2,35,1,11
	.byte	'APREN',0,1
	.word	757
	.byte	1,4,2,35,1,11
	.byte	'CAP0EN',0,1
	.word	757
	.byte	1,3,2,35,1,11
	.byte	'CAP1EN',0,1
	.word	757
	.byte	1,2,2,35,1,11
	.byte	'CAP2EN',0,1
	.word	757
	.byte	1,1,2,35,1,11
	.byte	'CAP3EN',0,1
	.word	757
	.byte	1,0,2,35,1,11
	.byte	'ESR0CNT',0,2
	.word	774
	.byte	12,4,2,35,2,11
	.byte	'RES29',0,1
	.word	757
	.byte	2,2,2,35,3,11
	.byte	'RES30',0,1
	.word	757
	.byte	1,1,2,35,3,11
	.byte	'RPRO',0,1
	.word	757
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_FLASH_PROCOND_Bits',0,17,179,2,3
	.word	68652
	.byte	10
	.byte	'_Ifx_FLASH_PROCONDBG_Bits',0,17,182,2,16,4,11
	.byte	'OCDSDIS',0,1
	.word	757
	.byte	1,7,2,35,0,11
	.byte	'DBGIFLCK',0,1
	.word	757
	.byte	1,6,2,35,0,11
	.byte	'EDM',0,1
	.word	757
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	582
	.byte	28,0,2,35,0,0,29
	.byte	'Ifx_FLASH_PROCONDBG_Bits',0,17,188,2,3
	.word	68976
	.byte	10
	.byte	'_Ifx_FLASH_PROCONHSM_Bits',0,17,191,2,16,4,11
	.byte	'HSMDBGDIS',0,1
	.word	757
	.byte	1,7,2,35,0,11
	.byte	'DBGIFLCK',0,1
	.word	757
	.byte	1,6,2,35,0,11
	.byte	'TSTIFLCK',0,1
	.word	757
	.byte	1,5,2,35,0,11
	.byte	'HSMTSTDIS',0,1
	.word	757
	.byte	1,4,2,35,0,11
	.byte	'RES15',0,2
	.word	774
	.byte	12,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	774
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_FLASH_PROCONHSM_Bits',0,17,199,2,3
	.word	69119
	.byte	10
	.byte	'_Ifx_FLASH_PROCONHSMCOTP_Bits',0,17,202,2,16,4,11
	.byte	'HSMBOOTEN',0,1
	.word	757
	.byte	1,7,2,35,0,11
	.byte	'SSWWAIT',0,1
	.word	757
	.byte	1,6,2,35,0,11
	.byte	'HSMDX',0,1
	.word	757
	.byte	1,5,2,35,0,11
	.byte	'HSM6X',0,1
	.word	757
	.byte	1,4,2,35,0,11
	.byte	'HSM16X',0,1
	.word	757
	.byte	1,3,2,35,0,11
	.byte	'HSM17X',0,1
	.word	757
	.byte	1,2,2,35,0,11
	.byte	'S6ROM',0,1
	.word	757
	.byte	1,1,2,35,0,11
	.byte	'HSMENPINS',0,2
	.word	774
	.byte	2,7,2,35,0,11
	.byte	'HSMENRES',0,1
	.word	757
	.byte	2,5,2,35,1,11
	.byte	'DESTDBG',0,1
	.word	757
	.byte	2,3,2,35,1,11
	.byte	'BLKFLAN',0,1
	.word	757
	.byte	1,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	757
	.byte	2,0,2,35,1,11
	.byte	'S16ROM',0,1
	.word	757
	.byte	1,7,2,35,2,11
	.byte	'S17ROM',0,1
	.word	757
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	774
	.byte	14,0,2,35,2,0,29
	.byte	'Ifx_FLASH_PROCONHSMCOTP_Bits',0,17,219,2,3
	.word	69308
	.byte	10
	.byte	'_Ifx_FLASH_PROCONOTP_Bits',0,17,222,2,16,4,11
	.byte	'S0ROM',0,1
	.word	757
	.byte	1,7,2,35,0,11
	.byte	'S1ROM',0,1
	.word	757
	.byte	1,6,2,35,0,11
	.byte	'S2ROM',0,1
	.word	757
	.byte	1,5,2,35,0,11
	.byte	'S3ROM',0,1
	.word	757
	.byte	1,4,2,35,0,11
	.byte	'S4ROM',0,1
	.word	757
	.byte	1,3,2,35,0,11
	.byte	'S5ROM',0,1
	.word	757
	.byte	1,2,2,35,0,11
	.byte	'S6ROM',0,1
	.word	757
	.byte	1,1,2,35,0,11
	.byte	'S7ROM',0,1
	.word	757
	.byte	1,0,2,35,0,11
	.byte	'S8ROM',0,1
	.word	757
	.byte	1,7,2,35,1,11
	.byte	'S9ROM',0,1
	.word	757
	.byte	1,6,2,35,1,11
	.byte	'S10ROM',0,1
	.word	757
	.byte	1,5,2,35,1,11
	.byte	'S11ROM',0,1
	.word	757
	.byte	1,4,2,35,1,11
	.byte	'S12ROM',0,1
	.word	757
	.byte	1,3,2,35,1,11
	.byte	'S13ROM',0,1
	.word	757
	.byte	1,2,2,35,1,11
	.byte	'S14ROM',0,1
	.word	757
	.byte	1,1,2,35,1,11
	.byte	'S15ROM',0,1
	.word	757
	.byte	1,0,2,35,1,11
	.byte	'S16ROM',0,1
	.word	757
	.byte	1,7,2,35,2,11
	.byte	'S17ROM',0,1
	.word	757
	.byte	1,6,2,35,2,11
	.byte	'S18ROM',0,1
	.word	757
	.byte	1,5,2,35,2,11
	.byte	'S19ROM',0,1
	.word	757
	.byte	1,4,2,35,2,11
	.byte	'S20ROM',0,1
	.word	757
	.byte	1,3,2,35,2,11
	.byte	'S21ROM',0,1
	.word	757
	.byte	1,2,2,35,2,11
	.byte	'S22ROM',0,1
	.word	757
	.byte	1,1,2,35,2,11
	.byte	'S23ROM',0,1
	.word	757
	.byte	1,0,2,35,2,11
	.byte	'S24ROM',0,1
	.word	757
	.byte	1,7,2,35,3,11
	.byte	'S25ROM',0,1
	.word	757
	.byte	1,6,2,35,3,11
	.byte	'S26ROM',0,1
	.word	757
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	757
	.byte	2,3,2,35,3,11
	.byte	'BML',0,1
	.word	757
	.byte	2,1,2,35,3,11
	.byte	'TP',0,1
	.word	757
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_FLASH_PROCONOTP_Bits',0,17,254,2,3
	.word	69671
	.byte	10
	.byte	'_Ifx_FLASH_PROCONP_Bits',0,17,129,3,16,4,11
	.byte	'S0L',0,1
	.word	757
	.byte	1,7,2,35,0,11
	.byte	'S1L',0,1
	.word	757
	.byte	1,6,2,35,0,11
	.byte	'S2L',0,1
	.word	757
	.byte	1,5,2,35,0,11
	.byte	'S3L',0,1
	.word	757
	.byte	1,4,2,35,0,11
	.byte	'S4L',0,1
	.word	757
	.byte	1,3,2,35,0,11
	.byte	'S5L',0,1
	.word	757
	.byte	1,2,2,35,0,11
	.byte	'S6L',0,1
	.word	757
	.byte	1,1,2,35,0,11
	.byte	'S7L',0,1
	.word	757
	.byte	1,0,2,35,0,11
	.byte	'S8L',0,1
	.word	757
	.byte	1,7,2,35,1,11
	.byte	'S9L',0,1
	.word	757
	.byte	1,6,2,35,1,11
	.byte	'S10L',0,1
	.word	757
	.byte	1,5,2,35,1,11
	.byte	'S11L',0,1
	.word	757
	.byte	1,4,2,35,1,11
	.byte	'S12L',0,1
	.word	757
	.byte	1,3,2,35,1,11
	.byte	'S13L',0,1
	.word	757
	.byte	1,2,2,35,1,11
	.byte	'S14L',0,1
	.word	757
	.byte	1,1,2,35,1,11
	.byte	'S15L',0,1
	.word	757
	.byte	1,0,2,35,1,11
	.byte	'S16L',0,1
	.word	757
	.byte	1,7,2,35,2,11
	.byte	'S17L',0,1
	.word	757
	.byte	1,6,2,35,2,11
	.byte	'S18L',0,1
	.word	757
	.byte	1,5,2,35,2,11
	.byte	'S19L',0,1
	.word	757
	.byte	1,4,2,35,2,11
	.byte	'S20L',0,1
	.word	757
	.byte	1,3,2,35,2,11
	.byte	'S21L',0,1
	.word	757
	.byte	1,2,2,35,2,11
	.byte	'S22L',0,1
	.word	757
	.byte	1,1,2,35,2,11
	.byte	'S23L',0,1
	.word	757
	.byte	1,0,2,35,2,11
	.byte	'S24L',0,1
	.word	757
	.byte	1,7,2,35,3,11
	.byte	'S25L',0,1
	.word	757
	.byte	1,6,2,35,3,11
	.byte	'S26L',0,1
	.word	757
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	757
	.byte	4,1,2,35,3,11
	.byte	'RPRO',0,1
	.word	757
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_FLASH_PROCONP_Bits',0,17,160,3,3
	.word	70266
	.byte	10
	.byte	'_Ifx_FLASH_PROCONWOP_Bits',0,17,163,3,16,4,11
	.byte	'S0WOP',0,1
	.word	757
	.byte	1,7,2,35,0,11
	.byte	'S1WOP',0,1
	.word	757
	.byte	1,6,2,35,0,11
	.byte	'S2WOP',0,1
	.word	757
	.byte	1,5,2,35,0,11
	.byte	'S3WOP',0,1
	.word	757
	.byte	1,4,2,35,0,11
	.byte	'S4WOP',0,1
	.word	757
	.byte	1,3,2,35,0,11
	.byte	'S5WOP',0,1
	.word	757
	.byte	1,2,2,35,0,11
	.byte	'S6WOP',0,1
	.word	757
	.byte	1,1,2,35,0,11
	.byte	'S7WOP',0,1
	.word	757
	.byte	1,0,2,35,0,11
	.byte	'S8WOP',0,1
	.word	757
	.byte	1,7,2,35,1,11
	.byte	'S9WOP',0,1
	.word	757
	.byte	1,6,2,35,1,11
	.byte	'S10WOP',0,1
	.word	757
	.byte	1,5,2,35,1,11
	.byte	'S11WOP',0,1
	.word	757
	.byte	1,4,2,35,1,11
	.byte	'S12WOP',0,1
	.word	757
	.byte	1,3,2,35,1,11
	.byte	'S13WOP',0,1
	.word	757
	.byte	1,2,2,35,1,11
	.byte	'S14WOP',0,1
	.word	757
	.byte	1,1,2,35,1,11
	.byte	'S15WOP',0,1
	.word	757
	.byte	1,0,2,35,1,11
	.byte	'S16WOP',0,1
	.word	757
	.byte	1,7,2,35,2,11
	.byte	'S17WOP',0,1
	.word	757
	.byte	1,6,2,35,2,11
	.byte	'S18WOP',0,1
	.word	757
	.byte	1,5,2,35,2,11
	.byte	'S19WOP',0,1
	.word	757
	.byte	1,4,2,35,2,11
	.byte	'S20WOP',0,1
	.word	757
	.byte	1,3,2,35,2,11
	.byte	'S21WOP',0,1
	.word	757
	.byte	1,2,2,35,2,11
	.byte	'S22WOP',0,1
	.word	757
	.byte	1,1,2,35,2,11
	.byte	'S23WOP',0,1
	.word	757
	.byte	1,0,2,35,2,11
	.byte	'S24WOP',0,1
	.word	757
	.byte	1,7,2,35,3,11
	.byte	'S25WOP',0,1
	.word	757
	.byte	1,6,2,35,3,11
	.byte	'S26WOP',0,1
	.word	757
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	757
	.byte	4,1,2,35,3,11
	.byte	'DATM',0,1
	.word	757
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_FLASH_PROCONWOP_Bits',0,17,194,3,3
	.word	70790
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG0_Bits',0,17,197,3,16,4,11
	.byte	'TAG',0,1
	.word	757
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	582
	.byte	26,0,2,35,0,0,29
	.byte	'Ifx_FLASH_RDB_CFG0_Bits',0,17,201,3,3
	.word	71372
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG1_Bits',0,17,204,3,16,4,11
	.byte	'TAG',0,1
	.word	757
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	582
	.byte	26,0,2,35,0,0,29
	.byte	'Ifx_FLASH_RDB_CFG1_Bits',0,17,208,3,3
	.word	71474
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG2_Bits',0,17,211,3,16,4,11
	.byte	'TAG',0,1
	.word	757
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	582
	.byte	26,0,2,35,0,0,29
	.byte	'Ifx_FLASH_RDB_CFG2_Bits',0,17,215,3,3
	.word	71576
	.byte	10
	.byte	'_Ifx_FLASH_RRAD_Bits',0,17,218,3,16,4,11
	.byte	'reserved_0',0,1
	.word	757
	.byte	3,5,2,35,0,11
	.byte	'ADD',0,4
	.word	582
	.byte	29,0,2,35,0,0,29
	.byte	'Ifx_FLASH_RRAD_Bits',0,17,222,3,3
	.word	71678
	.byte	10
	.byte	'_Ifx_FLASH_RRCT_Bits',0,17,225,3,16,4,11
	.byte	'STRT',0,1
	.word	757
	.byte	1,7,2,35,0,11
	.byte	'STP',0,1
	.word	757
	.byte	1,6,2,35,0,11
	.byte	'BUSY',0,1
	.word	757
	.byte	1,5,2,35,0,11
	.byte	'DONE',0,1
	.word	757
	.byte	1,4,2,35,0,11
	.byte	'ERR',0,1
	.word	757
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	757
	.byte	3,0,2,35,0,11
	.byte	'EOBM',0,1
	.word	757
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	757
	.byte	7,0,2,35,1,11
	.byte	'CNT',0,2
	.word	774
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_FLASH_RRCT_Bits',0,17,236,3,3
	.word	71772
	.byte	10
	.byte	'_Ifx_FLASH_RRD0_Bits',0,17,239,3,16,4,11
	.byte	'DATA',0,4
	.word	582
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_FLASH_RRD0_Bits',0,17,242,3,3
	.word	71982
	.byte	10
	.byte	'_Ifx_FLASH_RRD1_Bits',0,17,245,3,16,4,11
	.byte	'DATA',0,4
	.word	582
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_FLASH_RRD1_Bits',0,17,248,3,3
	.word	72055
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_CFG_Bits',0,17,251,3,16,4,11
	.byte	'SEL',0,1
	.word	757
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	757
	.byte	2,0,2,35,0,11
	.byte	'CLR',0,1
	.word	757
	.byte	1,7,2,35,1,11
	.byte	'DIS',0,1
	.word	757
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	582
	.byte	22,0,2,35,0,0,29
	.byte	'Ifx_FLASH_UBAB_CFG_Bits',0,17,130,4,3
	.word	72128
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_STAT_Bits',0,17,133,4,16,4,11
	.byte	'VLD0',0,1
	.word	757
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	582
	.byte	31,0,2,35,0,0,29
	.byte	'Ifx_FLASH_UBAB_STAT_Bits',0,17,137,4,3
	.word	72283
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_TOP_Bits',0,17,140,4,16,4,11
	.byte	'reserved_0',0,1
	.word	757
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	582
	.byte	19,8,2,35,0,11
	.byte	'ERR',0,1
	.word	757
	.byte	6,2,2,35,3,11
	.byte	'VLD',0,1
	.word	757
	.byte	1,1,2,35,3,11
	.byte	'CLR',0,1
	.word	757
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_FLASH_UBAB_TOP_Bits',0,17,147,4,3
	.word	72388
	.byte	12,17,155,4,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64793
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_ACCEN0',0,17,160,4,3
	.word	72536
	.byte	12,17,163,4,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65354
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_ACCEN1',0,17,168,4,3
	.word	72602
	.byte	12,17,171,4,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65435
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_CBAB_CFG',0,17,176,4,3
	.word	72668
	.byte	12,17,179,4,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65588
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_CBAB_STAT',0,17,184,4,3
	.word	72736
	.byte	12,17,187,4,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65836
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_CBAB_TOP',0,17,192,4,3
	.word	72805
	.byte	12,17,195,4,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65982
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_COMM0',0,17,200,4,3
	.word	72873
	.byte	12,17,203,4,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66080
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_COMM1',0,17,208,4,3
	.word	72938
	.byte	12,17,211,4,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66196
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_COMM2',0,17,216,4,3
	.word	73003
	.byte	12,17,219,4,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66312
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_ECCRD',0,17,224,4,3
	.word	73068
	.byte	12,17,227,4,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66452
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_ECCRP',0,17,232,4,3
	.word	73133
	.byte	12,17,235,4,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66592
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_ECCW',0,17,240,4,3
	.word	73198
	.byte	12,17,243,4,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66731
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_FCON',0,17,248,4,3
	.word	73262
	.byte	12,17,251,4,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67093
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_FPRO',0,17,128,5,3
	.word	73326
	.byte	12,17,131,5,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67534
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_FSR',0,17,136,5,3
	.word	73390
	.byte	12,17,139,5,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68140
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_ID',0,17,144,5,3
	.word	73453
	.byte	12,17,147,5,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68251
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_MARD',0,17,152,5,3
	.word	73515
	.byte	12,17,155,5,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68465
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_MARP',0,17,160,5,3
	.word	73579
	.byte	12,17,163,5,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68652
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_PROCOND',0,17,168,5,3
	.word	73643
	.byte	12,17,171,5,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68976
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_PROCONDBG',0,17,176,5,3
	.word	73710
	.byte	12,17,179,5,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69119
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_PROCONHSM',0,17,184,5,3
	.word	73779
	.byte	12,17,187,5,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69308
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_PROCONHSMCOTP',0,17,192,5,3
	.word	73848
	.byte	12,17,195,5,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69671
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_PROCONOTP',0,17,200,5,3
	.word	73921
	.byte	12,17,203,5,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70266
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_PROCONP',0,17,208,5,3
	.word	73990
	.byte	12,17,211,5,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70790
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_PROCONWOP',0,17,216,5,3
	.word	74057
	.byte	12,17,219,5,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71372
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_RDB_CFG0',0,17,224,5,3
	.word	74126
	.byte	12,17,227,5,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71474
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_RDB_CFG1',0,17,232,5,3
	.word	74194
	.byte	12,17,235,5,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71576
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_RDB_CFG2',0,17,240,5,3
	.word	74262
	.byte	12,17,243,5,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71678
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_RRAD',0,17,248,5,3
	.word	74330
	.byte	12,17,251,5,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71772
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_RRCT',0,17,128,6,3
	.word	74394
	.byte	12,17,131,6,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71982
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_RRD0',0,17,136,6,3
	.word	74458
	.byte	12,17,139,6,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72055
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_RRD1',0,17,144,6,3
	.word	74522
	.byte	12,17,147,6,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72128
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_UBAB_CFG',0,17,152,6,3
	.word	74586
	.byte	12,17,155,6,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72283
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_UBAB_STAT',0,17,160,6,3
	.word	74654
	.byte	12,17,163,6,9,4,13
	.byte	'U',0
	.word	582
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	598
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72388
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_UBAB_TOP',0,17,168,6,3
	.word	74723
	.byte	10
	.byte	'_Ifx_FLASH_CBAB',0,17,179,6,25,12,13
	.byte	'CFG',0
	.word	72668
	.byte	4,2,35,0,13
	.byte	'STAT',0
	.word	72736
	.byte	4,2,35,4,13
	.byte	'TOP',0
	.word	72805
	.byte	4,2,35,8,0,14
	.word	74791
	.byte	29
	.byte	'Ifx_FLASH_CBAB',0,17,184,6,3
	.word	74854
	.byte	10
	.byte	'_Ifx_FLASH_RDB',0,17,187,6,25,12,13
	.byte	'CFG0',0
	.word	74126
	.byte	4,2,35,0,13
	.byte	'CFG1',0
	.word	74194
	.byte	4,2,35,4,13
	.byte	'CFG2',0
	.word	74262
	.byte	4,2,35,8,0,14
	.word	74883
	.byte	29
	.byte	'Ifx_FLASH_RDB',0,17,192,6,3
	.word	74947
	.byte	10
	.byte	'_Ifx_FLASH_UBAB',0,17,195,6,25,12,13
	.byte	'CFG',0
	.word	74586
	.byte	4,2,35,0,13
	.byte	'STAT',0
	.word	74654
	.byte	4,2,35,4,13
	.byte	'TOP',0
	.word	74723
	.byte	4,2,35,8,0,14
	.word	74975
	.byte	29
	.byte	'Ifx_FLASH_UBAB',0,17,200,6,3
	.word	75038
	.byte	29
	.byte	'Ifx_P_ACCEN0_Bits',0,6,79,3
	.word	8147
	.byte	29
	.byte	'Ifx_P_ACCEN1_Bits',0,6,85,3
	.word	8060
	.byte	29
	.byte	'Ifx_P_ESR_Bits',0,6,107,3
	.word	4403
	.byte	29
	.byte	'Ifx_P_ID_Bits',0,6,115,3
	.word	2456
	.byte	29
	.byte	'Ifx_P_IN_Bits',0,6,137,1,3
	.word	3451
	.byte	29
	.byte	'Ifx_P_IOCR0_Bits',0,6,150,1,3
	.word	2584
	.byte	29
	.byte	'Ifx_P_IOCR12_Bits',0,6,163,1,3
	.word	3231
	.byte	29
	.byte	'Ifx_P_IOCR4_Bits',0,6,176,1,3
	.word	2799
	.byte	29
	.byte	'Ifx_P_IOCR8_Bits',0,6,189,1,3
	.word	3014
	.byte	29
	.byte	'Ifx_P_LPCR0_Bits',0,6,197,1,3
	.word	7419
	.byte	29
	.byte	'Ifx_P_LPCR1_Bits',0,6,205,1,3
	.word	7543
	.byte	29
	.byte	'Ifx_P_LPCR1_P21_Bits',0,6,215,1,3
	.word	7627
	.byte	29
	.byte	'Ifx_P_LPCR2_Bits',0,6,229,1,3
	.word	7807
	.byte	29
	.byte	'Ifx_P_OMCR0_Bits',0,6,240,1,3
	.word	6058
	.byte	29
	.byte	'Ifx_P_OMCR12_Bits',0,6,250,1,3
	.word	6582
	.byte	29
	.byte	'Ifx_P_OMCR4_Bits',0,6,133,2,3
	.word	6232
	.byte	29
	.byte	'Ifx_P_OMCR8_Bits',0,6,144,2,3
	.word	6406
	.byte	29
	.byte	'Ifx_P_OMCR_Bits',0,6,166,2,3
	.word	7071
	.byte	29
	.byte	'Ifx_P_OMR_Bits',0,6,203,2,3
	.word	1885
	.byte	29
	.byte	'Ifx_P_OMSR0_Bits',0,6,213,2,3
	.word	5395
	.byte	29
	.byte	'Ifx_P_OMSR12_Bits',0,6,224,2,3
	.word	5883
	.byte	29
	.byte	'Ifx_P_OMSR4_Bits',0,6,235,2,3
	.word	5542
	.byte	29
	.byte	'Ifx_P_OMSR8_Bits',0,6,246,2,3
	.word	5711
	.byte	29
	.byte	'Ifx_P_OMSR_Bits',0,6,140,3,3
	.word	6738
	.byte	29
	.byte	'Ifx_P_OUT_Bits',0,6,162,3,3
	.word	1569
	.byte	29
	.byte	'Ifx_P_PCSR_Bits',0,6,180,3,3
	.word	5109
	.byte	29
	.byte	'Ifx_P_PDISC_Bits',0,6,202,3,3
	.word	4743
	.byte	29
	.byte	'Ifx_P_PDR0_Bits',0,6,223,3,3
	.word	3774
	.byte	29
	.byte	'Ifx_P_PDR1_Bits',0,6,244,3,3
	.word	4078
	.byte	29
	.byte	'Ifx_P_ACCEN0',0,6,129,4,3
	.word	8674
	.byte	29
	.byte	'Ifx_P_ACCEN1',0,6,137,4,3
	.word	8107
	.byte	29
	.byte	'Ifx_P_ESR',0,6,145,4,3
	.word	4694
	.byte	29
	.byte	'Ifx_P_ID',0,6,153,4,3
	.word	2535
	.byte	29
	.byte	'Ifx_P_IN',0,6,161,4,3
	.word	3725
	.byte	29
	.byte	'Ifx_P_IOCR0',0,6,169,4,3
	.word	2759
	.byte	29
	.byte	'Ifx_P_IOCR12',0,6,177,4,3
	.word	3411
	.byte	29
	.byte	'Ifx_P_IOCR4',0,6,185,4,3
	.word	2974
	.byte	29
	.byte	'Ifx_P_IOCR8',0,6,193,4,3
	.word	3191
	.byte	29
	.byte	'Ifx_P_LPCR0',0,6,201,4,3
	.word	7503
	.byte	29
	.byte	'Ifx_P_LPCR1',0,6,210,4,3
	.word	7752
	.byte	29
	.byte	'Ifx_P_LPCR2',0,6,218,4,3
	.word	8011
	.byte	29
	.byte	'Ifx_P_OMCR',0,6,226,4,3
	.word	7379
	.byte	29
	.byte	'Ifx_P_OMCR0',0,6,234,4,3
	.word	6192
	.byte	29
	.byte	'Ifx_P_OMCR12',0,6,242,4,3
	.word	6698
	.byte	29
	.byte	'Ifx_P_OMCR4',0,6,250,4,3
	.word	6366
	.byte	29
	.byte	'Ifx_P_OMCR8',0,6,130,5,3
	.word	6542
	.byte	29
	.byte	'Ifx_P_OMR',0,6,138,5,3
	.word	2416
	.byte	29
	.byte	'Ifx_P_OMSR',0,6,146,5,3
	.word	7031
	.byte	29
	.byte	'Ifx_P_OMSR0',0,6,154,5,3
	.word	5502
	.byte	29
	.byte	'Ifx_P_OMSR12',0,6,162,5,3
	.word	6018
	.byte	29
	.byte	'Ifx_P_OMSR4',0,6,170,5,3
	.word	5671
	.byte	29
	.byte	'Ifx_P_OMSR8',0,6,178,5,3
	.word	5843
	.byte	29
	.byte	'Ifx_P_OUT',0,6,186,5,3
	.word	1845
	.byte	29
	.byte	'Ifx_P_PCSR',0,6,194,5,3
	.word	5355
	.byte	29
	.byte	'Ifx_P_PDISC',0,6,202,5,3
	.word	5069
	.byte	29
	.byte	'Ifx_P_PDR0',0,6,210,5,3
	.word	4038
	.byte	29
	.byte	'Ifx_P_PDR1',0,6,218,5,3
	.word	4354
	.byte	14
	.word	8714
	.byte	29
	.byte	'Ifx_P',0,6,139,6,3
	.word	76385
	.byte	17,5,83,9,1,18
	.byte	'IfxPort_InputMode_undefined',0,127,18
	.byte	'IfxPort_InputMode_noPullDevice',0,0,18
	.byte	'IfxPort_InputMode_pullDown',0,8,18
	.byte	'IfxPort_InputMode_pullUp',0,16,0,29
	.byte	'IfxPort_InputMode',0,5,89,3
	.word	76405
	.byte	17,5,120,9,1,18
	.byte	'IfxPort_OutputIdx_general',0,128,1,18
	.byte	'IfxPort_OutputIdx_alt1',0,136,1,18
	.byte	'IfxPort_OutputIdx_alt2',0,144,1,18
	.byte	'IfxPort_OutputIdx_alt3',0,152,1,18
	.byte	'IfxPort_OutputIdx_alt4',0,160,1,18
	.byte	'IfxPort_OutputIdx_alt5',0,168,1,18
	.byte	'IfxPort_OutputIdx_alt6',0,176,1,18
	.byte	'IfxPort_OutputIdx_alt7',0,184,1,0,29
	.byte	'IfxPort_OutputIdx',0,5,130,1,3
	.word	76556
	.byte	17,5,134,1,9,1,18
	.byte	'IfxPort_OutputMode_pushPull',0,128,1,18
	.byte	'IfxPort_OutputMode_openDrain',0,192,1,0,29
	.byte	'IfxPort_OutputMode',0,5,138,1,3
	.word	76800
	.byte	17,5,144,1,9,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed1',0,0,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed2',0,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed3',0,2,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed4',0,3,18
	.byte	'IfxPort_PadDriver_lvdsSpeed1',0,4,18
	.byte	'IfxPort_PadDriver_lvdsSpeed2',0,5,18
	.byte	'IfxPort_PadDriver_lvdsSpeed3',0,6,18
	.byte	'IfxPort_PadDriver_lvdsSpeed4',0,7,18
	.byte	'IfxPort_PadDriver_ttlSpeed1',0,8,18
	.byte	'IfxPort_PadDriver_ttlSpeed2',0,9,18
	.byte	'IfxPort_PadDriver_ttlSpeed3',0,10,18
	.byte	'IfxPort_PadDriver_ttlSpeed4',0,11,0,29
	.byte	'IfxPort_PadDriver',0,5,158,1,3
	.word	76898
	.byte	29
	.byte	'IfxPort_State',0,5,178,1,3
	.word	9327
	.byte	31,5,190,1,9,8,13
	.byte	'port',0
	.word	9322
	.byte	4,2,35,0,13
	.byte	'pinIndex',0
	.word	757
	.byte	1,2,35,4,0,29
	.byte	'IfxPort_Pin',0,5,194,1,3
	.word	77363
	.byte	29
	.byte	'IfxScuCcu_PllStepsFunctionHook',0,7,148,1,16
	.word	233
	.byte	31,7,212,5,9,8,13
	.byte	'value',0
	.word	376
	.byte	4,2,35,0,13
	.byte	'mask',0
	.word	376
	.byte	4,2,35,4,0,29
	.byte	'IfxScuCcu_CcuconRegConfig',0,7,216,5,3
	.word	77463
	.byte	31,7,221,5,9,8,13
	.byte	'pDivider',0
	.word	757
	.byte	1,2,35,0,13
	.byte	'nDivider',0
	.word	757
	.byte	1,2,35,1,13
	.byte	'k2Initial',0
	.word	757
	.byte	1,2,35,2,13
	.byte	'waitTime',0
	.word	290
	.byte	4,2,35,4,0,29
	.byte	'IfxScuCcu_InitialStepConfig',0,7,227,5,3
	.word	77534
	.byte	31,7,231,5,9,12,13
	.byte	'k2Step',0
	.word	757
	.byte	1,2,35,0,13
	.byte	'waitTime',0
	.word	290
	.byte	4,2,35,2,13
	.byte	'hookFunction',0
	.word	77423
	.byte	4,2,35,8,0,29
	.byte	'IfxScuCcu_PllStepsConfig',0,7,236,5,3
	.word	77651
	.byte	3
	.word	230
	.byte	31,7,244,5,9,48,13
	.byte	'ccucon0',0
	.word	77463
	.byte	8,2,35,0,13
	.byte	'ccucon1',0
	.word	77463
	.byte	8,2,35,8,13
	.byte	'ccucon2',0
	.word	77463
	.byte	8,2,35,16,13
	.byte	'ccucon5',0
	.word	77463
	.byte	8,2,35,24,13
	.byte	'ccucon6',0
	.word	77463
	.byte	8,2,35,32,13
	.byte	'ccucon7',0
	.word	77463
	.byte	8,2,35,40,0,29
	.byte	'IfxScuCcu_ClockDistributionConfig',0,7,252,5,3
	.word	77753
	.byte	31,7,128,6,9,8,13
	.byte	'value',0
	.word	376
	.byte	4,2,35,0,13
	.byte	'mask',0
	.word	376
	.byte	4,2,35,4,0,29
	.byte	'IfxScuCcu_FlashWaitstateConfig',0,7,132,6,3
	.word	77905
	.byte	3
	.word	77651
	.byte	31,7,137,6,9,16,13
	.byte	'numOfPllDividerSteps',0
	.word	757
	.byte	1,2,35,0,13
	.byte	'pllDividerStep',0
	.word	77981
	.byte	4,2,35,4,13
	.byte	'pllInitialStep',0
	.word	77534
	.byte	8,2,35,8,0,29
	.byte	'IfxScuCcu_SysPllConfig',0,7,142,6,3
	.word	77986
	.byte	29
	.byte	'IfxCpu_spinLock',0,8,114,22
	.word	582
	.byte	29
	.byte	'IfxCpu_mutexLock',0,8,118,22
	.word	582
	.byte	29
	.byte	'IfxCpu_syncEvent',0,8,122,22
	.word	582
	.byte	29
	.byte	'IfxCpu_CoreMode',0,8,140,1,3
	.word	22095
	.byte	17,8,144,1,9,1,18
	.byte	'IfxCpu_CounterMode_normal',0,0,18
	.byte	'IfxCpu_CounterMode_task',0,1,0,29
	.byte	'IfxCpu_CounterMode',0,8,148,1,3
	.word	78202
	.byte	31,8,160,1,9,6,13
	.byte	'counter',0
	.word	376
	.byte	4,2,35,0,13
	.byte	'overlfow',0
	.word	757
	.byte	1,2,35,4,0,29
	.byte	'IfxCpu_Counter',0,8,164,1,3
	.word	78291
	.byte	31,8,172,1,9,32,13
	.byte	'instruction',0
	.word	78291
	.byte	6,2,35,0,13
	.byte	'clock',0
	.word	78291
	.byte	6,2,35,6,13
	.byte	'counter1',0
	.word	78291
	.byte	6,2,35,12,13
	.byte	'counter2',0
	.word	78291
	.byte	6,2,35,18,13
	.byte	'counter3',0
	.word	78291
	.byte	6,2,35,24,0,29
	.byte	'IfxCpu_Perf',0,8,179,1,3
	.word	78357
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L89:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,21,0,54,15,39,12,0,0,3,15,0,73,19,0,0,4,46,1,3,8,32,13
	.byte	58,15,59,15,57,15,54,15,39,12,0,0,5,5,0,3,8,58,15,59,15,57,15,73,19,0,0,6,11,0,0,0,7,36,0,3,8,11,15,62
	.byte	15,0,0,8,46,1,3,8,32,13,58,15,59,15,57,15,73,19,54,15,39,12,0,0,9,59,0,3,8,0,0,10,19,1,3,8,58,15,59,15
	.byte	57,15,11,15,0,0,11,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,12,23,1,58,15,59,15,57,15,11,15,0,0,13,13
	.byte	0,3,8,73,19,11,15,56,9,0,0,14,53,0,73,19,0,0,15,1,1,11,15,73,19,0,0,16,33,0,47,15,0,0,17,4,1,58,15,59
	.byte	15,57,15,11,15,0,0,18,40,0,3,8,28,13,0,0,19,11,1,0,0,20,46,1,3,8,73,19,54,15,39,12,63,12,60,12,0,0,21
	.byte	5,0,3,8,73,19,0,0,22,46,1,3,8,54,15,39,12,63,12,60,12,0,0,23,5,0,73,19,0,0,24,46,0,3,8,54,15,39,12,63
	.byte	12,60,12,0,0,25,46,1,49,19,0,0,26,5,0,49,19,0,0,27,46,1,3,8,58,15,59,15,57,15,54,15,39,12,63,12,60,12
	.byte	0,0,28,46,0,3,8,58,15,59,15,57,15,73,19,54,15,39,12,63,12,60,12,0,0,29,22,0,3,8,58,15,59,15,57,15,73,19
	.byte	0,0,30,21,0,54,15,0,0,31,19,1,58,15,59,15,57,15,11,15,0,0,32,38,0,73,19,0,0,33,52,0,3,8,58,15,59,15,57
	.byte	15,73,19,63,12,60,12,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L90:
	.word	.L332-.L331
.L331:
	.half	3
	.word	.L334-.L333
.L333:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg',0
	.byte	0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_Intrinsics.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Scu\\Std\\IfxScuWdt.h',0
	.byte	0,0,0
	.byte	'IfxScu_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Port\\Std\\IfxPort.h',0
	.byte	0,0,0
	.byte	'IfxPort_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Scu\\Std\\IfxScuCcu.h',0
	.byte	0,0,0
	.byte	'..\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Cpu\\Std\\IfxCpu.h',0,0,0,0
	.byte	'IfxCpu_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxCpu_cfg.h',0
	.byte	0,0,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Cpu/Std/IfxCpu.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Platform_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Ifx_Types.h',0
	.byte	0,0,0
	.byte	'IfxSrc_regdef.h',0,1,0,0
	.byte	'IfxStm_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxScu_cfg.h',0
	.byte	0,0,0
	.byte	'IfxFlash_regdef.h',0,1,0,0,0
.L334:
.L332:
	.sdecl	'.debug_info',debug,cluster('IfxCpu_getCoreMode')
	.sect	'.debug_info'
.L91:
	.word	438
	.half	3
	.word	.L92
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Cpu/Std/IfxCpu.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L94,.L93
	.byte	2
	.word	.L87
	.byte	3
	.byte	'IfxCpu_getCoreMode',0,1,77,17
	.word	.L156
	.byte	1,1,1
	.word	.L64,.L157,.L63
	.byte	4
	.byte	'cpu',0,1,77,45
	.word	.L158,.L159
	.byte	5
	.word	.L64,.L157
	.byte	6
	.byte	'cpuMode',0,1,79,24
	.word	.L156,.L160
	.byte	6
	.byte	'dbgsr',0,1,80,24
	.word	.L161,.L162
	.byte	6
	.byte	'index',0,1,81,24
	.word	.L163,.L164
	.byte	7
	.word	.L165,.L166,.L4
	.byte	8
	.word	.L167,.L166,.L4
	.byte	6
	.byte	'reg',0,2,143,6,21
	.word	.L168,.L169
	.byte	0,0,5
	.word	.L170,.L11
	.byte	6
	.byte	'pmcsr_val',0,1,105,37
	.word	.L171,.L172
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCpu_getCoreMode')
	.sect	'.debug_abbrev'
.L92:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,11,1,49,16,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxCpu_getCoreMode')
	.sect	'.debug_line'
.L93:
	.word	.L336-.L335
.L335:
	.half	3
	.word	.L338-.L337
.L337:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Cpu/Std/IfxCpu.c',0,0,0,0
	.byte	'..\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Cpu\\Std\\IfxCpu.h',0,0,0,0,0
.L338:
	.byte	5,17,7,0,5,2
	.word	.L64
	.byte	3,204,0,1,5,48,9
	.half	.L268-.L64
	.byte	3,4,1,5,13,9
	.half	.L267-.L268
	.byte	3,2,1,4,2,5,19,9
	.half	.L166-.L267
	.byte	3,189,5,1,5,37,9
	.half	.L271-.L166
	.byte	3,1,1,5,5,9
	.half	.L272-.L271
	.byte	1,4,1,9
	.half	.L4-.L272
	.byte	3,198,122,1,5,20,7,9
	.half	.L339-.L4
	.byte	3,2,1,5,27,9
	.half	.L273-.L339
	.byte	1,5,25,9
	.half	.L5-.L273
	.byte	3,4,1,5,16,9
	.half	.L6-.L5
	.byte	3,4,1,5,5,9
	.half	.L340-.L6
	.byte	1,5,17,7,9
	.half	.L341-.L340
	.byte	3,2,1,5,39,9
	.half	.L342-.L341
	.byte	1,5,20,9
	.half	.L7-.L342
	.byte	3,4,1,5,9,9
	.half	.L343-.L7
	.byte	1,5,42,7,9
	.half	.L170-.L343
	.byte	3,4,1,5,29,9
	.half	.L274-.L170
	.byte	3,2,1,5,13,9
	.half	.L344-.L274
	.byte	1,5,25,7,9
	.half	.L345-.L344
	.byte	3,2,1,5,46,9
	.half	.L346-.L345
	.byte	1,5,33,9
	.half	.L10-.L346
	.byte	3,4,1,5,17,9
	.half	.L347-.L10
	.byte	1,5,29,7,9
	.half	.L348-.L347
	.byte	3,2,1,5,13,9
	.half	.L11-.L348
	.byte	3,124,1,5,21,9
	.half	.L9-.L11
	.byte	3,10,1,5,5,9
	.half	.L8-.L9
	.byte	3,4,1,5,1,9
	.half	.L14-.L8
	.byte	3,1,1,7,9
	.half	.L95-.L14
	.byte	0,1,1
.L336:
	.sdecl	'.debug_ranges',debug,cluster('IfxCpu_getCoreMode')
	.sect	'.debug_ranges'
.L94:
	.word	-1,.L64,0,.L95-.L64,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCpu_getIndex')
	.sect	'.debug_info'
.L96:
	.word	343
	.half	3
	.word	.L97
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Cpu/Std/IfxCpu.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L99,.L98
	.byte	2
	.word	.L87
	.byte	3
	.byte	'IfxCpu_getIndex',0,1,131,1,20
	.word	.L163
	.byte	1,1,1
	.word	.L66,.L173,.L65
	.byte	4
	.byte	'cpu',0,1,131,1,45
	.word	.L158,.L174
	.byte	5
	.word	.L66,.L173
	.byte	6
	.byte	'result',0,1,133,1,24
	.word	.L163,.L175
	.byte	6
	.byte	'index',0,1,134,1,24
	.word	.L176,.L177
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCpu_getIndex')
	.sect	'.debug_abbrev'
.L97:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxCpu_getIndex')
	.sect	'.debug_line'
.L98:
	.word	.L350-.L349
.L349:
	.half	3
	.word	.L352-.L351
.L351:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Cpu/Std/IfxCpu.c',0,0,0,0,0
.L352:
	.byte	5,12,7,0,5,2
	.word	.L66
	.byte	3,134,1,1,5,16,9
	.half	.L275-.L66
	.byte	3,2,1,5,47,9
	.half	.L276-.L275
	.byte	1,5,32,9
	.half	.L16-.L276
	.byte	3,2,1,5,13,9
	.half	.L353-.L16
	.byte	1,5,32,9
	.half	.L354-.L353
	.byte	1,5,39,9
	.half	.L355-.L354
	.byte	1,5,9,9
	.half	.L356-.L355
	.byte	1,5,61,7,9
	.half	.L357-.L356
	.byte	3,2,1,5,42,9
	.half	.L358-.L357
	.byte	1,5,61,9
	.half	.L359-.L358
	.byte	1,5,68,9
	.half	.L360-.L359
	.byte	1,5,22,9
	.half	.L361-.L360
	.byte	1,5,13,9
	.half	.L362-.L361
	.byte	3,1,1,5,54,9
	.half	.L17-.L362
	.byte	3,123,1,5,47,9
	.half	.L15-.L17
	.byte	1,5,5,7,9
	.half	.L18-.L15
	.byte	3,9,1,5,1,9
	.half	.L19-.L18
	.byte	3,1,1,7,9
	.half	.L100-.L19
	.byte	0,1,1
.L350:
	.sdecl	'.debug_ranges',debug,cluster('IfxCpu_getIndex')
	.sect	'.debug_ranges'
.L99:
	.word	-1,.L66,0,.L100-.L66,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCpu_setCoreMode')
	.sect	'.debug_info'
.L101:
	.word	498
	.half	3
	.word	.L102
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Cpu/Std/IfxCpu.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L104,.L103
	.byte	2
	.word	.L87
	.byte	3
	.byte	'IfxCpu_setCoreMode',0,1,226,1,9
	.word	.L178
	.byte	1,1,1
	.word	.L76,.L179,.L75
	.byte	4
	.byte	'cpu',0,1,226,1,37
	.word	.L158,.L180
	.byte	4
	.byte	'mode',0,1,226,1,58
	.word	.L156,.L181
	.byte	5
	.word	.L76,.L179
	.byte	6
	.byte	'reqslp',0,1,228,1,24
	.word	.L178,.L182
	.byte	6
	.byte	'retValue',0,1,229,1,24
	.word	.L178,.L183
	.byte	6
	.byte	'index',0,1,230,1,24
	.word	.L163,.L184
	.byte	7
	.word	.L165,.L185,.L35
	.byte	8
	.word	.L167,.L185,.L35
	.byte	6
	.byte	'reg',0,2,143,6,21
	.word	.L168,.L186
	.byte	0,0,5
	.word	.L187,.L188
	.byte	6
	.byte	'safetyWdtPw',0,1,244,1,20
	.word	.L189,.L190
	.byte	0,5
	.word	.L36,.L34
	.byte	6
	.byte	'cpuWdtPw',0,1,254,1,20
	.word	.L189,.L191
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCpu_setCoreMode')
	.sect	'.debug_abbrev'
.L102:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,11,1,49,16,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxCpu_setCoreMode')
	.sect	'.debug_line'
.L103:
	.word	.L364-.L363
.L363:
	.half	3
	.word	.L366-.L365
.L365:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Cpu/Std/IfxCpu.c',0,0,0,0
	.byte	'..\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Cpu\\Std\\IfxCpu.h',0,0,0,0,0
.L366:
	.byte	5,9,7,0,5,2
	.word	.L76
	.byte	3,225,1,1,5,48,9
	.half	.L289-.L76
	.byte	3,4,1,5,30,9
	.half	.L286-.L289
	.byte	1,5,17,9
	.half	.L292-.L286
	.byte	3,3,1,5,57,7,9
	.half	.L367-.L292
	.byte	1,5,26,7,9
	.half	.L368-.L367
	.byte	3,1,1,5,52,7,9
	.half	.L28-.L368
	.byte	1,5,60,9
	.half	.L294-.L28
	.byte	1,5,52,9
	.half	.L30-.L294
	.byte	1,5,14,9
	.half	.L31-.L30
	.byte	3,2,1,5,45,7,9
	.half	.L369-.L31
	.byte	1,5,72,9
	.half	.L295-.L369
	.byte	1,5,45,9
	.half	.L32-.L295
	.byte	1,5,5,9
	.half	.L33-.L32
	.byte	3,2,1,4,2,5,19,7,9
	.half	.L185-.L33
	.byte	3,162,4,1,5,37,9
	.half	.L290-.L185
	.byte	3,1,1,5,5,9
	.half	.L296-.L290
	.byte	1,4,1,5,9,9
	.half	.L35-.L296
	.byte	3,224,123,1,5,69,7,9
	.half	.L187-.L35
	.byte	3,3,1,5,32,9
	.half	.L291-.L187
	.byte	1,5,42,9
	.half	.L293-.L291
	.byte	3,1,1,5,23,9
	.half	.L298-.L293
	.byte	3,1,1,5,54,9
	.half	.L370-.L298
	.byte	1,5,29,9
	.half	.L300-.L370
	.byte	1,5,69,9
	.half	.L371-.L300
	.byte	1,5,40,9
	.half	.L372-.L371
	.byte	3,1,1,5,25,9
	.half	.L302-.L372
	.byte	3,2,1,5,31,9
	.half	.L373-.L302
	.byte	1,5,32,9
	.half	.L188-.L373
	.byte	3,123,1,5,63,9
	.half	.L36-.L188
	.byte	3,10,1,5,29,9
	.half	.L303-.L36
	.byte	1,5,39,9
	.half	.L305-.L303
	.byte	3,1,1,5,23,9
	.half	.L306-.L305
	.byte	3,1,1,5,29,9
	.half	.L374-.L306
	.byte	1,5,23,9
	.half	.L375-.L374
	.byte	1,5,29,9
	.half	.L376-.L375
	.byte	1,5,46,9
	.half	.L377-.L376
	.byte	1,5,54,9
	.half	.L378-.L377
	.byte	1,5,37,9
	.half	.L379-.L378
	.byte	3,1,1,5,5,9
	.half	.L34-.L379
	.byte	3,4,1,5,1,9
	.half	.L38-.L34
	.byte	3,1,1,7,9
	.half	.L105-.L38
	.byte	0,1,1
.L364:
	.sdecl	'.debug_ranges',debug,cluster('IfxCpu_setCoreMode')
	.sect	'.debug_ranges'
.L104:
	.word	-1,.L76,0,.L105-.L76,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCpu_setProgramCounter')
	.sect	'.debug_info'
.L106:
	.word	463
	.half	3
	.word	.L107
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Cpu/Std/IfxCpu.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L109,.L108
	.byte	2
	.word	.L87
	.byte	3
	.byte	'IfxCpu_setProgramCounter',0,1,137,2,9
	.word	.L178
	.byte	1,1,1
	.word	.L78,.L192,.L77
	.byte	4
	.byte	'cpu',0,1,137,2,43
	.word	.L158,.L193
	.byte	4
	.byte	'programCounter',0,1,137,2,55
	.word	.L176,.L194
	.byte	5
	.word	.L78,.L192
	.byte	6
	.byte	'retVal',0,1,139,2,13
	.word	.L178,.L195
	.byte	7
	.word	.L165,.L196,.L39
	.byte	8
	.word	.L167,.L196,.L39
	.byte	6
	.byte	'reg',0,2,143,6,21
	.word	.L168,.L197
	.byte	0,0,7
	.word	.L198,.L39,.L199
	.byte	9
	.word	.L200,.L201
	.byte	8
	.word	.L202,.L39,.L199
	.byte	6
	.byte	'module',0,2,234,5,14
	.word	.L158,.L203
	.byte	0,0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCpu_setProgramCounter')
	.sect	'.debug_abbrev'
.L107:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,11,1,49,16,17,1,18,1,0,0,9,5,0,49
	.byte	16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxCpu_setProgramCounter')
	.sect	'.debug_line'
.L108:
	.word	.L381-.L380
.L380:
	.half	3
	.word	.L383-.L382
.L382:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Cpu/Std/IfxCpu.c',0,0,0,0
	.byte	'..\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Cpu\\Std\\IfxCpu.h',0,0,0,0,0
.L383:
	.byte	5,20,7,0,5,2
	.word	.L78
	.byte	3,138,2,1,4,2,5,19,9
	.half	.L196-.L78
	.byte	3,133,4,1,5,37,9
	.half	.L309-.L196
	.byte	3,1,1,5,5,9
	.half	.L310-.L309
	.byte	1,9
	.half	.L39-.L310
	.byte	3,91,1,5,48,7,9
	.half	.L384-.L39
	.byte	3,2,1,5,29,9
	.half	.L385-.L384
	.byte	1,5,48,9
	.half	.L386-.L385
	.byte	1,5,53,9
	.half	.L387-.L386
	.byte	1,5,60,9
	.half	.L311-.L387
	.byte	1,5,16,9
	.half	.L40-.L311
	.byte	3,4,1,5,5,9
	.half	.L41-.L40
	.byte	3,3,1,4,1,9
	.half	.L42-.L41
	.byte	3,152,124,1,5,16,7,9
	.half	.L199-.L42
	.byte	3,2,1,5,23,9
	.half	.L388-.L199
	.byte	1,5,18,9
	.half	.L43-.L388
	.byte	3,4,1,5,39,9
	.half	.L389-.L43
	.byte	1,5,22,9
	.half	.L308-.L389
	.byte	1,5,5,9
	.half	.L44-.L308
	.byte	3,3,1,5,1,9
	.half	.L45-.L44
	.byte	3,1,1,7,9
	.half	.L110-.L45
	.byte	0,1,1
.L381:
	.sdecl	'.debug_ranges',debug,cluster('IfxCpu_setProgramCounter')
	.sect	'.debug_ranges'
.L109:
	.word	-1,.L78,0,.L110-.L78,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCpu_startCore')
	.sect	'.debug_info'
.L111:
	.word	353
	.half	3
	.word	.L112
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Cpu/Std/IfxCpu.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L114,.L113
	.byte	2
	.word	.L87
	.byte	3
	.byte	'IfxCpu_startCore',0,1,182,2,9
	.word	.L178
	.byte	1,1,1
	.word	.L82,.L204,.L81
	.byte	4
	.byte	'cpu',0,1,182,2,35
	.word	.L158,.L205
	.byte	4
	.byte	'programCounter',0,1,182,2,47
	.word	.L176,.L206
	.byte	5
	.word	.L82,.L204
	.byte	6
	.byte	'retVal',0,1,184,2,13
	.word	.L178,.L207
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCpu_startCore')
	.sect	'.debug_abbrev'
.L112:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxCpu_startCore')
	.sect	'.debug_line'
.L113:
	.word	.L391-.L390
.L390:
	.half	3
	.word	.L393-.L392
.L392:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Cpu/Std/IfxCpu.c',0,0,0,0,0
.L393:
	.byte	5,9,7,0,5,2
	.word	.L82
	.byte	3,181,2,1,5,20,9
	.half	.L314-.L82
	.byte	3,2,1,5,45,9
	.half	.L315-.L314
	.byte	3,3,1,5,12,9
	.half	.L313-.L315
	.byte	1,5,28,9
	.half	.L394-.L313
	.byte	3,4,1,5,5,9
	.half	.L317-.L394
	.byte	1,5,43,7,9
	.half	.L395-.L317
	.byte	3,2,1,5,16,9
	.half	.L319-.L395
	.byte	1,5,5,9
	.half	.L51-.L319
	.byte	3,3,1,5,1,9
	.half	.L52-.L51
	.byte	3,1,1,7,9
	.half	.L115-.L52
	.byte	0,1,1
.L391:
	.sdecl	'.debug_ranges',debug,cluster('IfxCpu_startCore')
	.sect	'.debug_ranges'
.L114:
	.word	-1,.L82,0,.L115-.L82,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCpu_acquireMutex')
	.sect	'.debug_info'
.L116:
	.word	350
	.half	3
	.word	.L117
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Cpu/Std/IfxCpu.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L119,.L118
	.byte	2
	.word	.L87
	.byte	3
	.byte	'IfxCpu_acquireMutex',0,1,56,9
	.word	.L178
	.byte	1,1,1
	.word	.L62,.L208,.L61
	.byte	4
	.byte	'lock',0,1,56,47
	.word	.L209,.L210
	.byte	5
	.word	.L62,.L208
	.byte	6
	.byte	'retVal',0,1,58,21
	.word	.L178,.L211
	.byte	6
	.byte	'spinLockVal',0,1,59,21
	.word	.L212,.L213
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCpu_acquireMutex')
	.sect	'.debug_abbrev'
.L117:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxCpu_acquireMutex')
	.sect	'.debug_line'
.L118:
	.word	.L397-.L396
.L396:
	.half	3
	.word	.L399-.L398
.L398:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Cpu/Std/IfxCpu.c',0,0,0,0,0
.L399:
	.byte	5,9,7,0,5,2
	.word	.L62
	.byte	3,55,1,5,17,9
	.half	.L265-.L62
	.byte	3,5,1,5,19,9
	.half	.L266-.L265
	.byte	3,2,1,5,17,9
	.half	.L400-.L266
	.byte	1,9
	.half	.L401-.L400
	.byte	3,2,1,9
	.half	.L402-.L401
	.byte	3,127,1,5,9,9
	.half	.L403-.L402
	.byte	3,4,1,5,5,9
	.half	.L404-.L403
	.byte	1,5,16,7,9
	.half	.L405-.L404
	.byte	3,2,1,5,5,9
	.half	.L2-.L405
	.byte	3,3,1,5,1,9
	.half	.L3-.L2
	.byte	3,1,1,7,9
	.half	.L120-.L3
	.byte	0,1,1
.L397:
	.sdecl	'.debug_ranges',debug,cluster('IfxCpu_acquireMutex')
	.sect	'.debug_ranges'
.L119:
	.word	-1,.L62,0,.L120-.L62,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCpu_releaseMutex')
	.sect	'.debug_info'
.L121:
	.word	304
	.half	3
	.word	.L122
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Cpu/Std/IfxCpu.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L124,.L123
	.byte	2
	.word	.L87
	.byte	3
	.byte	'IfxCpu_releaseMutex',0,1,212,1,6,1,1,1
	.word	.L72,.L214,.L71
	.byte	4
	.byte	'lock',0,1,212,1,44
	.word	.L209,.L215
	.byte	5
	.word	.L72,.L214
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCpu_releaseMutex')
	.sect	'.debug_abbrev'
.L122:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxCpu_releaseMutex')
	.sect	'.debug_line'
.L123:
	.word	.L407-.L406
.L406:
	.half	3
	.word	.L409-.L408
.L408:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Cpu/Std/IfxCpu.c',0,0,0,0,0
.L409:
	.byte	5,13,7,0,5,2
	.word	.L72
	.byte	3,214,1,1,5,11,9
	.half	.L410-.L72
	.byte	1,5,1,9
	.half	.L411-.L410
	.byte	3,1,1,7,9
	.half	.L125-.L411
	.byte	0,1,1
.L407:
	.sdecl	'.debug_ranges',debug,cluster('IfxCpu_releaseMutex')
	.sect	'.debug_ranges'
.L124:
	.word	-1,.L72,0,.L125-.L72,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCpu_resetSpinLock')
	.sect	'.debug_info'
.L126:
	.word	305
	.half	3
	.word	.L127
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Cpu/Std/IfxCpu.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L129,.L128
	.byte	2
	.word	.L87
	.byte	3
	.byte	'IfxCpu_resetSpinLock',0,1,219,1,6,1,1,1
	.word	.L74,.L216,.L73
	.byte	4
	.byte	'lock',0,1,219,1,44
	.word	.L217,.L218
	.byte	5
	.word	.L74,.L216
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCpu_resetSpinLock')
	.sect	'.debug_abbrev'
.L127:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxCpu_resetSpinLock')
	.sect	'.debug_line'
.L128:
	.word	.L413-.L412
.L412:
	.half	3
	.word	.L415-.L414
.L414:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Cpu/Std/IfxCpu.c',0,0,0,0,0
.L415:
	.byte	5,13,7,0,5,2
	.word	.L74
	.byte	3,221,1,1,5,11,9
	.half	.L416-.L74
	.byte	1,5,1,9
	.half	.L417-.L416
	.byte	3,1,1,7,9
	.half	.L130-.L417
	.byte	0,1,1
.L413:
	.sdecl	'.debug_ranges',debug,cluster('IfxCpu_resetSpinLock')
	.sect	'.debug_ranges'
.L129:
	.word	-1,.L74,0,.L130-.L74,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCpu_setSpinLock')
	.sect	'.debug_info'
.L131:
	.word	379
	.half	3
	.word	.L132
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Cpu/Std/IfxCpu.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L134,.L133
	.byte	2
	.word	.L87
	.byte	3
	.byte	'IfxCpu_setSpinLock',0,1,154,2,9
	.word	.L178
	.byte	1,1,1
	.word	.L80,.L219,.L79
	.byte	4
	.byte	'lock',0,1,154,2,45
	.word	.L217,.L220
	.byte	4
	.byte	'timeoutCount',0,1,154,2,58
	.word	.L176,.L221
	.byte	5
	.word	.L80,.L219
	.byte	6
	.byte	'retVal',0,1,156,2,21
	.word	.L178,.L222
	.byte	6
	.byte	'spinLockVal',0,1,157,2,21
	.word	.L223,.L224
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCpu_setSpinLock')
	.sect	'.debug_abbrev'
.L132:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxCpu_setSpinLock')
	.sect	'.debug_line'
.L133:
	.word	.L419-.L418
.L418:
	.half	3
	.word	.L421-.L420
.L420:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Cpu/Std/IfxCpu.c',0,0,0,0,0
.L421:
	.byte	5,9,7,0,5,2
	.word	.L80
	.byte	3,153,2,1,5,12,9
	.half	.L312-.L80
	.byte	3,5,1,5,23,9
	.half	.L46-.L312
	.byte	3,4,1,5,21,9
	.half	.L422-.L46
	.byte	1,9
	.half	.L423-.L422
	.byte	3,2,1,9
	.half	.L424-.L423
	.byte	3,127,1,5,13,9
	.half	.L425-.L424
	.byte	3,4,1,5,9,9
	.half	.L426-.L425
	.byte	1,5,20,7,9
	.half	.L427-.L426
	.byte	3,2,1,5,26,9
	.half	.L428-.L427
	.byte	1,5,25,9
	.half	.L47-.L428
	.byte	3,4,1,5,14,9
	.half	.L48-.L47
	.byte	3,2,1,5,49,7,9
	.half	.L429-.L48
	.byte	1,5,5,7,9
	.half	.L49-.L429
	.byte	3,2,1,5,1,9
	.half	.L50-.L49
	.byte	3,1,1,7,9
	.half	.L135-.L50
	.byte	0,1,1
.L419:
	.sdecl	'.debug_ranges',debug,cluster('IfxCpu_setSpinLock')
	.sect	'.debug_ranges'
.L134:
	.word	-1,.L80,0,.L135-.L80,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCpu_getRandomValue')
	.sect	'.debug_info'
.L136:
	.word	461
	.half	3
	.word	.L137
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Cpu/Std/IfxCpu.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L139,.L138
	.byte	2
	.word	.L87
	.byte	3
	.byte	'IfxCpu_getRandomValue',0,1,150,1,8
	.word	.L176
	.byte	1,1,1
	.word	.L68,.L225,.L67
	.byte	4
	.byte	'seed',0,1,150,1,38
	.word	.L226,.L227
	.byte	5
	.word	.L68,.L225
	.byte	6
	.byte	'x',0,1,162,1,12
	.word	.L176,.L228
	.byte	5
	.word	.L20,.L225
	.byte	6
	.byte	'a',0,1,170,1,12
	.word	.L176,.L229
	.byte	6
	.byte	'm',0,1,171,1,12
	.word	.L176,.L230
	.byte	6
	.byte	'result',0,1,172,1,12
	.word	.L176,.L231
	.byte	7
	.word	.L232,.L233,.L234
	.byte	8
	.word	.L235,.L236
	.byte	8
	.word	.L237,.L238
	.byte	8
	.word	.L239,.L240
	.byte	9
	.word	.L241,.L233,.L234
	.byte	6
	.byte	'result',0,2,249,2,9
	.word	.L176,.L242
	.byte	0,0,0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCpu_getRandomValue')
	.sect	'.debug_abbrev'
.L137:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,5,0,49,16,2,6,0,0,9,11,1,49,16,17
	.byte	1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxCpu_getRandomValue')
	.sect	'.debug_line'
.L138:
	.word	.L431-.L430
.L430:
	.half	3
	.word	.L433-.L432
.L432:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Cpu/Std/IfxCpu.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0,0
.L433:
	.byte	5,16,7,0,5,2
	.word	.L68
	.byte	3,161,1,1,5,5,9
	.half	.L277-.L68
	.byte	3,3,1,5,11,7,9
	.half	.L434-.L277
	.byte	3,2,1,5,16,9
	.half	.L20-.L434
	.byte	3,3,1,5,14,9
	.half	.L435-.L20
	.byte	3,1,1,4,2,5,5,9
	.half	.L233-.L435
	.byte	3,207,1,1,9
	.half	.L436-.L233
	.byte	3,11,1,4,1,5,12,9
	.half	.L21-.L436
	.byte	3,178,126,1,5,5,9
	.half	.L234-.L21
	.byte	3,2,1,5,1,9
	.half	.L22-.L234
	.byte	3,1,1,7,9
	.half	.L140-.L22
	.byte	0,1,1
.L431:
	.sdecl	'.debug_ranges',debug,cluster('IfxCpu_getRandomValue')
	.sect	'.debug_ranges'
.L139:
	.word	-1,.L68,0,.L140-.L68,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCpu_getRandomValueWithinRange')
	.sect	'.debug_info'
.L141:
	.word	407
	.half	3
	.word	.L142
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Cpu/Std/IfxCpu.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L144,.L143
	.byte	2
	.word	.L87
	.byte	3
	.byte	'IfxCpu_getRandomValueWithinRange',0,1,189,1,8
	.word	.L176
	.byte	1,1,1
	.word	.L70,.L243,.L69
	.byte	4
	.byte	'seed',0,1,189,1,49
	.word	.L226,.L244
	.byte	4
	.byte	'min',0,1,189,1,62
	.word	.L176,.L245
	.byte	4
	.byte	'max',0,1,189,1,74
	.word	.L176,.L246
	.byte	5
	.word	.L70,.L243
	.byte	6
	.byte	'new_value',0,1,191,1,12
	.word	.L176,.L247
	.byte	5
	.word	.L248,.L23
	.byte	6
	.byte	'swap',0,1,196,1,18
	.word	.L249,.L250
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCpu_getRandomValueWithinRange')
	.sect	'.debug_abbrev'
.L142:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxCpu_getRandomValueWithinRange')
	.sect	'.debug_line'
.L143:
	.word	.L438-.L437
.L437:
	.half	3
	.word	.L440-.L439
.L439:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Cpu/Std/IfxCpu.c',0,0,0,0,0
.L440:
	.byte	5,8,7,0,5,2
	.word	.L70
	.byte	3,188,1,1,5,46,9
	.half	.L282-.L70
	.byte	3,2,1,5,5,9
	.half	.L280-.L282
	.byte	3,3,1,5,23,7,9
	.half	.L248-.L280
	.byte	3,2,1,5,13,9
	.half	.L285-.L248
	.byte	3,1,1,9
	.half	.L441-.L285
	.byte	3,1,1,5,9,9
	.half	.L23-.L441
	.byte	3,4,1,5,28,7,9
	.half	.L442-.L23
	.byte	1,5,9,7,9
	.half	.L443-.L442
	.byte	3,2,1,5,30,9
	.half	.L24-.L443
	.byte	3,4,1,5,36,9
	.half	.L283-.L24
	.byte	1,5,23,9
	.half	.L444-.L283
	.byte	1,5,42,9
	.half	.L445-.L444
	.byte	1,5,5,9
	.half	.L284-.L445
	.byte	1,5,1,9
	.half	.L26-.L284
	.byte	3,1,1,7,9
	.half	.L145-.L26
	.byte	0,1,1
.L438:
	.sdecl	'.debug_ranges',debug,cluster('IfxCpu_getRandomValueWithinRange')
	.sect	'.debug_ranges'
.L144:
	.word	-1,.L70,0,.L145-.L70,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCpu_waitEvent')
	.sect	'.debug_info'
.L146:
	.word	452
	.half	3
	.word	.L147
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Cpu/Std/IfxCpu.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L149,.L148
	.byte	2
	.word	.L87
	.byte	3
	.byte	'IfxCpu_waitEvent',0,1,200,2,9
	.word	.L178
	.byte	1,1,1
	.word	.L84,.L251,.L83
	.byte	4
	.byte	'event',0,1,200,2,44
	.word	.L252,.L253
	.byte	4
	.byte	'timeoutMilliSec',0,1,200,2,58
	.word	.L176,.L254
	.byte	5
	.word	.L84,.L251
	.byte	6
	.byte	'sync',0,1,202,2,22
	.word	.L255,.L256
	.byte	6
	.byte	'errorcnt',0,1,204,2,22
	.word	.L178,.L257
	.byte	6
	.byte	'stmCount',0,1,206,2,22
	.word	.L176,.L258
	.byte	6
	.byte	'stmCountBegin',0,1,207,2,22
	.word	.L176,.L259
	.byte	7
	.word	.L260,.L261,.L55
	.byte	8
	.word	.L262,.L261,.L55
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCpu_waitEvent')
	.sect	'.debug_abbrev'
.L147:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,11,0,49,16,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxCpu_waitEvent')
	.sect	'.debug_line'
.L148:
	.word	.L447-.L446
.L446:
	.half	3
	.word	.L449-.L448
.L448:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Cpu/Std/IfxCpu.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Scu\\Std\\IfxScuCcu.h',0
	.byte	0,0,0,0
.L449:
	.byte	5,9,7,0,5,2
	.word	.L84
	.byte	3,199,2,1,5,57,9
	.half	.L322-.L84
	.byte	3,2,1,5,36,9
	.half	.L54-.L322
	.byte	1,9
	.half	.L327-.L54
	.byte	3,2,1,4,2,5,40,9
	.half	.L261-.L327
	.byte	3,152,6,1,5,58,9
	.half	.L321-.L261
	.byte	1,5,43,9
	.half	.L450-.L321
	.byte	1,5,5,9
	.half	.L451-.L450
	.byte	1,4,1,5,78,9
	.half	.L55-.L451
	.byte	3,234,121,1,5,76,9
	.half	.L452-.L55
	.byte	1,5,86,9
	.half	.L453-.L452
	.byte	1,5,84,9
	.half	.L454-.L453
	.byte	1,5,38,9
	.half	.L455-.L454
	.byte	1,5,47,9
	.half	.L328-.L455
	.byte	3,1,1,5,72,9
	.half	.L329-.L328
	.byte	3,2,1,5,14,9
	.half	.L57-.L329
	.byte	3,2,1,5,31,9
	.half	.L456-.L57
	.byte	3,2,1,5,34,9
	.half	.L457-.L456
	.byte	1,5,9,9
	.half	.L458-.L457
	.byte	1,5,22,7,9
	.half	.L459-.L458
	.byte	3,2,1,5,13,9
	.half	.L460-.L459
	.byte	3,1,1,9
	.half	.L56-.L460
	.byte	3,121,1,5,19,9
	.half	.L461-.L56
	.byte	1,5,72,9
	.half	.L462-.L461
	.byte	1,5,5,7,9
	.half	.L59-.L462
	.byte	3,18,1,5,1,9
	.half	.L60-.L59
	.byte	3,1,1,7,9
	.half	.L150-.L60
	.byte	0,1,1
.L447:
	.sdecl	'.debug_ranges',debug,cluster('IfxCpu_waitEvent')
	.sect	'.debug_ranges'
.L149:
	.word	-1,.L84,0,.L150-.L84,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCpu_emitEvent')
	.sect	'.debug_info'
.L151:
	.word	302
	.half	3
	.word	.L152
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Cpu/Std/IfxCpu.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L154,.L153
	.byte	2
	.word	.L87
	.byte	3
	.byte	'IfxCpu_emitEvent',0,1,231,2,6,1,1,1
	.word	.L86,.L263,.L85
	.byte	4
	.byte	'event',0,1,231,2,41
	.word	.L252,.L264
	.byte	5
	.word	.L86,.L263
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCpu_emitEvent')
	.sect	'.debug_abbrev'
.L152:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxCpu_emitEvent')
	.sect	'.debug_line'
.L153:
	.word	.L464-.L463
.L463:
	.half	3
	.word	.L466-.L465
.L465:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Cpu/Std/IfxCpu.c',0,0,0,0,0
.L466:
	.byte	5,5,7,0,5,2
	.word	.L86
	.byte	3,232,2,1,5,1,9
	.half	.L467-.L86
	.byte	3,1,1,7,9
	.half	.L155-.L467
	.byte	0,1,1
.L464:
	.sdecl	'.debug_ranges',debug,cluster('IfxCpu_emitEvent')
	.sect	'.debug_ranges'
.L154:
	.word	-1,.L86,0,.L155-.L86,0,0
	.sdecl	'.debug_loc',debug,cluster('IfxCpu_acquireMutex')
	.sect	'.debug_loc'
.L61:
	.word	-1,.L62,0,.L265-.L62
	.half	2
	.byte	138,0
	.word	.L265-.L62,.L208-.L62
	.half	2
	.byte	138,8
	.word	.L208-.L62,.L208-.L62
	.half	2
	.byte	138,0
	.word	0,0
.L210:
	.word	-1,.L62,0,.L208-.L62
	.half	1
	.byte	100
	.word	0,0
.L211:
	.word	-1,.L62,.L266-.L62,.L208-.L62
	.half	1
	.byte	82
	.word	0,0
.L213:
	.word	-1,.L62,0,.L208-.L62
	.half	2
	.byte	145,120
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxCpu_emitEvent')
	.sect	'.debug_loc'
.L85:
	.word	-1,.L86,0,.L263-.L86
	.half	2
	.byte	138,0
	.word	0,0
.L264:
	.word	-1,.L86,0,.L263-.L86
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxCpu_getCoreMode')
	.sect	'.debug_loc'
.L63:
	.word	-1,.L64,0,.L157-.L64
	.half	2
	.byte	138,0
	.word	0,0
.L159:
	.word	-1,.L64,0,.L267-.L64
	.half	1
	.byte	100
	.word	.L268-.L64,.L269-.L64
	.half	1
	.byte	111
	.word	.L9-.L64,.L8-.L64
	.half	1
	.byte	111
	.word	0,0
.L160:
	.word	-1,.L64,.L166-.L64,.L157-.L64
	.half	5
	.byte	144,32,157,32,0
	.word	.L270-.L64,.L157-.L64
	.half	1
	.byte	82
	.word	0,0
.L162:
	.word	-1,.L64,.L273-.L64,.L5-.L64
	.half	1
	.byte	81
	.word	.L6-.L64,.L157-.L64
	.half	1
	.byte	81
	.word	0,0
.L164:
	.word	-1,.L64,.L267-.L64,.L270-.L64
	.half	1
	.byte	82
	.word	0,0
.L172:
	.word	-1,.L64,.L274-.L64,.L9-.L64
	.half	1
	.byte	111
	.word	0,0
.L169:
	.word	-1,.L64,.L271-.L64,.L272-.L64
	.half	1
	.byte	95
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxCpu_getIndex')
	.sect	'.debug_loc'
.L65:
	.word	-1,.L66,0,.L173-.L66
	.half	2
	.byte	138,0
	.word	0,0
.L174:
	.word	-1,.L66,0,.L173-.L66
	.half	1
	.byte	100
	.word	0,0
.L177:
	.word	-1,.L66,.L276-.L66,.L173-.L66
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L175:
	.word	-1,.L66,.L275-.L66,.L173-.L66
	.half	1
	.byte	82
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxCpu_getRandomValue')
	.sect	'.debug_loc'
.L67:
	.word	-1,.L68,0,.L225-.L68
	.half	2
	.byte	138,0
	.word	0,0
.L229:
	.word	-1,.L68,.L278-.L68,.L225-.L68
	.half	1
	.byte	81
	.word	0,0
.L236:
	.word	0,0
.L230:
	.word	-1,.L68,.L233-.L68,.L279-.L68
	.half	1
	.byte	82
	.word	0,0
.L240:
	.word	0,0
.L231:
	.word	0,0
.L242:
	.word	-1,.L68,.L279-.L68,.L225-.L68
	.half	1
	.byte	82
	.word	0,0
.L227:
	.word	-1,.L68,0,.L225-.L68
	.half	1
	.byte	100
	.word	0,0
.L228:
	.word	-1,.L68,.L277-.L68,.L225-.L68
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L238:
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxCpu_getRandomValueWithinRange')
	.sect	'.debug_loc'
.L69:
	.word	-1,.L70,0,.L243-.L70
	.half	2
	.byte	138,0
	.word	0,0
.L246:
	.word	-1,.L70,0,.L280-.L70
	.half	1
	.byte	85
	.word	.L282-.L70,.L283-.L70
	.half	1
	.byte	95
	.word	0,0
.L245:
	.word	-1,.L70,0,.L280-.L70
	.half	1
	.byte	84
	.word	.L281-.L70,.L243-.L70
	.half	1
	.byte	88
	.word	0,0
.L247:
	.word	-1,.L70,.L280-.L70,.L284-.L70
	.half	1
	.byte	82
	.word	0,0
.L244:
	.word	-1,.L70,0,.L280-.L70
	.half	1
	.byte	100
	.word	0,0
.L250:
	.word	-1,.L70,.L285-.L70,.L23-.L70
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxCpu_releaseMutex')
	.sect	'.debug_loc'
.L71:
	.word	-1,.L72,0,.L214-.L72
	.half	2
	.byte	138,0
	.word	0,0
.L215:
	.word	-1,.L72,0,.L214-.L72
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxCpu_resetSpinLock')
	.sect	'.debug_loc'
.L73:
	.word	-1,.L74,0,.L216-.L74
	.half	2
	.byte	138,0
	.word	0,0
.L218:
	.word	-1,.L74,0,.L216-.L74
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxCpu_setCoreMode')
	.sect	'.debug_loc'
.L75:
	.word	-1,.L76,0,.L179-.L76
	.half	2
	.byte	138,0
	.word	0,0
.L180:
	.word	-1,.L76,0,.L286-.L76
	.half	1
	.byte	100
	.word	.L287-.L76,.L288-.L76
	.half	1
	.byte	111
	.word	.L299-.L76,.L300-.L76
	.half	1
	.byte	100
	.word	0,0
.L191:
	.word	-1,.L76,.L303-.L76,.L304-.L76
	.half	1
	.byte	82
	.word	.L305-.L76,.L34-.L76
	.half	1
	.byte	91
	.word	.L304-.L76,.L306-.L76
	.half	1
	.byte	84
	.word	.L307-.L76,.L34-.L76
	.half	1
	.byte	84
	.word	0,0
.L184:
	.word	-1,.L76,.L286-.L76,.L291-.L76
	.half	1
	.byte	82
	.word	.L292-.L76,.L293-.L76
	.half	1
	.byte	88
	.word	.L36-.L76,.L34-.L76
	.half	1
	.byte	88
	.word	.L36-.L76,.L303-.L76
	.half	1
	.byte	82
	.word	0,0
.L181:
	.word	-1,.L76,0,.L286-.L76
	.half	1
	.byte	84
	.word	.L289-.L76,.L290-.L76
	.half	1
	.byte	95
	.word	0,0
.L186:
	.word	-1,.L76,.L290-.L76,.L296-.L76
	.half	1
	.byte	95
	.word	0,0
.L182:
	.word	-1,.L76,.L295-.L76,.L32-.L76
	.half	1
	.byte	90
	.word	.L33-.L76,.L179-.L76
	.half	1
	.byte	90
	.word	0,0
.L183:
	.word	-1,.L76,.L294-.L76,.L30-.L76
	.half	1
	.byte	89
	.word	.L31-.L76,.L179-.L76
	.half	1
	.byte	89
	.word	0,0
.L190:
	.word	-1,.L76,.L291-.L76,.L297-.L76
	.half	1
	.byte	82
	.word	.L293-.L76,.L36-.L76
	.half	1
	.byte	88
	.word	.L297-.L76,.L298-.L76
	.half	1
	.byte	84
	.word	.L301-.L76,.L302-.L76
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxCpu_setProgramCounter')
	.sect	'.debug_loc'
.L77:
	.word	-1,.L78,0,.L192-.L78
	.half	2
	.byte	138,0
	.word	0,0
.L193:
	.word	-1,.L78,0,.L192-.L78
	.half	1
	.byte	100
	.word	0,0
.L201:
	.word	0,0
.L203:
	.word	-1,.L78,.L311-.L78,.L40-.L78
	.half	1
	.byte	111
	.word	.L41-.L78,.L192-.L78
	.half	1
	.byte	111
	.word	0,0
.L194:
	.word	-1,.L78,0,.L308-.L78
	.half	1
	.byte	84
	.word	0,0
.L197:
	.word	-1,.L78,.L309-.L78,.L310-.L78
	.half	1
	.byte	95
	.word	0,0
.L195:
	.word	-1,.L78,.L196-.L78,.L192-.L78
	.half	1
	.byte	82
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxCpu_setSpinLock')
	.sect	'.debug_loc'
.L79:
	.word	-1,.L80,0,.L312-.L80
	.half	2
	.byte	138,0
	.word	.L312-.L80,.L219-.L80
	.half	2
	.byte	138,8
	.word	.L219-.L80,.L219-.L80
	.half	2
	.byte	138,0
	.word	0,0
.L220:
	.word	-1,.L80,0,.L219-.L80
	.half	1
	.byte	100
	.word	0,0
.L222:
	.word	-1,.L80,.L46-.L80,.L219-.L80
	.half	1
	.byte	82
	.word	0,0
.L224:
	.word	-1,.L80,0,.L219-.L80
	.half	2
	.byte	145,120
	.word	0,0
.L221:
	.word	-1,.L80,0,.L219-.L80
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxCpu_startCore')
	.sect	'.debug_loc'
.L81:
	.word	-1,.L82,0,.L204-.L82
	.half	2
	.byte	138,0
	.word	0,0
.L205:
	.word	-1,.L82,0,.L313-.L82
	.half	1
	.byte	100
	.word	.L314-.L82,.L204-.L82
	.half	1
	.byte	111
	.word	.L316-.L82,.L317-.L82
	.half	1
	.byte	100
	.word	.L318-.L82,.L319-.L82
	.half	1
	.byte	100
	.word	0,0
.L206:
	.word	-1,.L82,0,.L313-.L82
	.half	1
	.byte	84
	.word	0,0
.L207:
	.word	-1,.L82,.L315-.L82,.L204-.L82
	.half	1
	.byte	95
	.word	.L320-.L82,.L204-.L82
	.half	1
	.byte	82
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxCpu_waitEvent')
	.sect	'.debug_loc'
.L83:
	.word	-1,.L84,0,.L251-.L84
	.half	2
	.byte	138,0
	.word	0,0
.L257:
	.word	-1,.L84,.L261-.L84,.L251-.L84
	.half	1
	.byte	89
	.word	.L330-.L84,.L251-.L84
	.half	1
	.byte	82
	.word	0,0
.L253:
	.word	-1,.L84,0,.L321-.L84
	.half	1
	.byte	100
	.word	.L323-.L84,.L324-.L84
	.half	1
	.byte	95
	.word	.L325-.L84,.L326-.L84
	.half	1
	.byte	95
	.word	0,0
.L258:
	.word	-1,.L84,.L328-.L84,.L251-.L84
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L259:
	.word	-1,.L84,.L329-.L84,.L251-.L84
	.half	1
	.byte	81
	.word	0,0
.L256:
	.word	-1,.L84,.L327-.L84,.L251-.L84
	.half	1
	.byte	111
	.word	0,0
.L254:
	.word	-1,.L84,0,.L321-.L84
	.half	1
	.byte	84
	.word	.L322-.L84,.L251-.L84
	.half	1
	.byte	88
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L468:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('IfxCpu_acquireMutex')
	.sect	'.debug_frame'
	.word	44
	.word	.L468,.L62,.L208-.L62
	.byte	8,18,8,19,8,21,8,22,8,23,4
	.word	(.L265-.L62)/2
	.byte	19,8,22,26,3,19,138,8,4
	.word	(.L208-.L265)/2
	.byte	19,0,8,26
	.sdecl	'.debug_frame',debug,cluster('IfxCpu_getCoreMode')
	.sect	'.debug_frame'
	.word	12
	.word	.L468,.L64,.L157-.L64
	.sdecl	'.debug_frame',debug,cluster('IfxCpu_getIndex')
	.sect	'.debug_frame'
	.word	24
	.word	.L468,.L66,.L173-.L66
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxCpu_getRandomValue')
	.sect	'.debug_frame'
	.word	24
	.word	.L468,.L68,.L225-.L68
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxCpu_getRandomValueWithinRange')
	.sect	'.debug_frame'
	.word	12
	.word	.L468,.L70,.L243-.L70
	.sdecl	'.debug_frame',debug,cluster('IfxCpu_releaseMutex')
	.sect	'.debug_frame'
	.word	24
	.word	.L468,.L72,.L214-.L72
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxCpu_resetSpinLock')
	.sect	'.debug_frame'
	.word	24
	.word	.L468,.L74,.L216-.L74
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxCpu_setCoreMode')
	.sect	'.debug_frame'
	.word	12
	.word	.L468,.L76,.L179-.L76
	.sdecl	'.debug_frame',debug,cluster('IfxCpu_setProgramCounter')
	.sect	'.debug_frame'
	.word	24
	.word	.L468,.L78,.L192-.L78
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxCpu_setSpinLock')
	.sect	'.debug_frame'
	.word	44
	.word	.L468,.L80,.L219-.L80
	.byte	8,18,8,19,8,21,8,22,8,23,4
	.word	(.L312-.L80)/2
	.byte	19,8,22,26,3,19,138,8,4
	.word	(.L219-.L312)/2
	.byte	19,0,8,26
	.sdecl	'.debug_frame',debug,cluster('IfxCpu_startCore')
	.sect	'.debug_frame'
	.word	12
	.word	.L468,.L82,.L204-.L82
	.sdecl	'.debug_frame',debug,cluster('IfxCpu_waitEvent')
	.sect	'.debug_frame'
	.word	12
	.word	.L468,.L84,.L251-.L84
	.sdecl	'.debug_frame',debug,cluster('IfxCpu_emitEvent')
	.sect	'.debug_frame'
	.word	24
	.word	.L468,.L86,.L263-.L86
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	; Module end
