/**
 * \file IfxIom_bf.h
 * \brief
 * \copyright Copyright (c) 2015 Infineon Technologies AG. All rights reserved.
 *
 * Version: TC26XB_UM_V1.2.R0
 * Specification: tc26xB_um_v1.2_MCSFR.xml (Revision: UM_V1.2)
 * MAY BE CHANGED BY USER [yes/no]: No
 *
 *                                 IMPORTANT NOTICE
 *
 * Infineon Technologies AG (Infineon) is supplying this file for use
 * exclusively with Infineon's microcontroller products. This file can be freely
 * distributed within development tools that are supporting such microcontroller
 * products.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS".  NO WARRANTIES, WHETHER EXPRESS, IMPLIED
 * OR STATUTORY, INCLUDING, BUT NOT LIMITED TO, IMPLIED WARRANTIES OF
 * MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE APPLY TO THIS SOFTWARE.
 * INFINEON SHALL NOT, IN ANY CIRCUMSTANCES, BE LIABLE FOR SPECIAL, INCIDENTAL,
 * OR CONSEQUENTIAL DAMAGES, FOR ANY REASON WHATSOEVER.
 *
 * \defgroup IfxLld_Iom_BitfieldsMask Bitfields mask and offset
 * \ingroup IfxLld_Iom
 * 
 */
#ifndef IFXIOM_BF_H
#define IFXIOM_BF_H 1
/******************************************************************************/
/******************************************************************************/
/** \addtogroup IfxLld_Iom_BitfieldsMask
 * \{  */

/** \brief  Length for Ifx_IOM_ACCEN0_Bits.EN0 */
#define IFX_IOM_ACCEN0_EN0_LEN (1u)

/** \brief  Mask for Ifx_IOM_ACCEN0_Bits.EN0 */
#define IFX_IOM_ACCEN0_EN0_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ACCEN0_Bits.EN0 */
#define IFX_IOM_ACCEN0_EN0_OFF (0u)

/** \brief  Length for Ifx_IOM_ACCEN0_Bits.EN10 */
#define IFX_IOM_ACCEN0_EN10_LEN (1u)

/** \brief  Mask for Ifx_IOM_ACCEN0_Bits.EN10 */
#define IFX_IOM_ACCEN0_EN10_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ACCEN0_Bits.EN10 */
#define IFX_IOM_ACCEN0_EN10_OFF (10u)

/** \brief  Length for Ifx_IOM_ACCEN0_Bits.EN11 */
#define IFX_IOM_ACCEN0_EN11_LEN (1u)

/** \brief  Mask for Ifx_IOM_ACCEN0_Bits.EN11 */
#define IFX_IOM_ACCEN0_EN11_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ACCEN0_Bits.EN11 */
#define IFX_IOM_ACCEN0_EN11_OFF (11u)

/** \brief  Length for Ifx_IOM_ACCEN0_Bits.EN12 */
#define IFX_IOM_ACCEN0_EN12_LEN (1u)

/** \brief  Mask for Ifx_IOM_ACCEN0_Bits.EN12 */
#define IFX_IOM_ACCEN0_EN12_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ACCEN0_Bits.EN12 */
#define IFX_IOM_ACCEN0_EN12_OFF (12u)

/** \brief  Length for Ifx_IOM_ACCEN0_Bits.EN13 */
#define IFX_IOM_ACCEN0_EN13_LEN (1u)

/** \brief  Mask for Ifx_IOM_ACCEN0_Bits.EN13 */
#define IFX_IOM_ACCEN0_EN13_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ACCEN0_Bits.EN13 */
#define IFX_IOM_ACCEN0_EN13_OFF (13u)

/** \brief  Length for Ifx_IOM_ACCEN0_Bits.EN14 */
#define IFX_IOM_ACCEN0_EN14_LEN (1u)

/** \brief  Mask for Ifx_IOM_ACCEN0_Bits.EN14 */
#define IFX_IOM_ACCEN0_EN14_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ACCEN0_Bits.EN14 */
#define IFX_IOM_ACCEN0_EN14_OFF (14u)

/** \brief  Length for Ifx_IOM_ACCEN0_Bits.EN15 */
#define IFX_IOM_ACCEN0_EN15_LEN (1u)

/** \brief  Mask for Ifx_IOM_ACCEN0_Bits.EN15 */
#define IFX_IOM_ACCEN0_EN15_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ACCEN0_Bits.EN15 */
#define IFX_IOM_ACCEN0_EN15_OFF (15u)

/** \brief  Length for Ifx_IOM_ACCEN0_Bits.EN16 */
#define IFX_IOM_ACCEN0_EN16_LEN (1u)

/** \brief  Mask for Ifx_IOM_ACCEN0_Bits.EN16 */
#define IFX_IOM_ACCEN0_EN16_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ACCEN0_Bits.EN16 */
#define IFX_IOM_ACCEN0_EN16_OFF (16u)

/** \brief  Length for Ifx_IOM_ACCEN0_Bits.EN17 */
#define IFX_IOM_ACCEN0_EN17_LEN (1u)

/** \brief  Mask for Ifx_IOM_ACCEN0_Bits.EN17 */
#define IFX_IOM_ACCEN0_EN17_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ACCEN0_Bits.EN17 */
#define IFX_IOM_ACCEN0_EN17_OFF (17u)

/** \brief  Length for Ifx_IOM_ACCEN0_Bits.EN18 */
#define IFX_IOM_ACCEN0_EN18_LEN (1u)

/** \brief  Mask for Ifx_IOM_ACCEN0_Bits.EN18 */
#define IFX_IOM_ACCEN0_EN18_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ACCEN0_Bits.EN18 */
#define IFX_IOM_ACCEN0_EN18_OFF (18u)

/** \brief  Length for Ifx_IOM_ACCEN0_Bits.EN19 */
#define IFX_IOM_ACCEN0_EN19_LEN (1u)

/** \brief  Mask for Ifx_IOM_ACCEN0_Bits.EN19 */
#define IFX_IOM_ACCEN0_EN19_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ACCEN0_Bits.EN19 */
#define IFX_IOM_ACCEN0_EN19_OFF (19u)

/** \brief  Length for Ifx_IOM_ACCEN0_Bits.EN1 */
#define IFX_IOM_ACCEN0_EN1_LEN (1u)

/** \brief  Mask for Ifx_IOM_ACCEN0_Bits.EN1 */
#define IFX_IOM_ACCEN0_EN1_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ACCEN0_Bits.EN1 */
#define IFX_IOM_ACCEN0_EN1_OFF (1u)

/** \brief  Length for Ifx_IOM_ACCEN0_Bits.EN20 */
#define IFX_IOM_ACCEN0_EN20_LEN (1u)

/** \brief  Mask for Ifx_IOM_ACCEN0_Bits.EN20 */
#define IFX_IOM_ACCEN0_EN20_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ACCEN0_Bits.EN20 */
#define IFX_IOM_ACCEN0_EN20_OFF (20u)

/** \brief  Length for Ifx_IOM_ACCEN0_Bits.EN21 */
#define IFX_IOM_ACCEN0_EN21_LEN (1u)

/** \brief  Mask for Ifx_IOM_ACCEN0_Bits.EN21 */
#define IFX_IOM_ACCEN0_EN21_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ACCEN0_Bits.EN21 */
#define IFX_IOM_ACCEN0_EN21_OFF (21u)

/** \brief  Length for Ifx_IOM_ACCEN0_Bits.EN22 */
#define IFX_IOM_ACCEN0_EN22_LEN (1u)

/** \brief  Mask for Ifx_IOM_ACCEN0_Bits.EN22 */
#define IFX_IOM_ACCEN0_EN22_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ACCEN0_Bits.EN22 */
#define IFX_IOM_ACCEN0_EN22_OFF (22u)

/** \brief  Length for Ifx_IOM_ACCEN0_Bits.EN23 */
#define IFX_IOM_ACCEN0_EN23_LEN (1u)

/** \brief  Mask for Ifx_IOM_ACCEN0_Bits.EN23 */
#define IFX_IOM_ACCEN0_EN23_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ACCEN0_Bits.EN23 */
#define IFX_IOM_ACCEN0_EN23_OFF (23u)

/** \brief  Length for Ifx_IOM_ACCEN0_Bits.EN24 */
#define IFX_IOM_ACCEN0_EN24_LEN (1u)

/** \brief  Mask for Ifx_IOM_ACCEN0_Bits.EN24 */
#define IFX_IOM_ACCEN0_EN24_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ACCEN0_Bits.EN24 */
#define IFX_IOM_ACCEN0_EN24_OFF (24u)

/** \brief  Length for Ifx_IOM_ACCEN0_Bits.EN25 */
#define IFX_IOM_ACCEN0_EN25_LEN (1u)

/** \brief  Mask for Ifx_IOM_ACCEN0_Bits.EN25 */
#define IFX_IOM_ACCEN0_EN25_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ACCEN0_Bits.EN25 */
#define IFX_IOM_ACCEN0_EN25_OFF (25u)

/** \brief  Length for Ifx_IOM_ACCEN0_Bits.EN26 */
#define IFX_IOM_ACCEN0_EN26_LEN (1u)

/** \brief  Mask for Ifx_IOM_ACCEN0_Bits.EN26 */
#define IFX_IOM_ACCEN0_EN26_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ACCEN0_Bits.EN26 */
#define IFX_IOM_ACCEN0_EN26_OFF (26u)

/** \brief  Length for Ifx_IOM_ACCEN0_Bits.EN27 */
#define IFX_IOM_ACCEN0_EN27_LEN (1u)

/** \brief  Mask for Ifx_IOM_ACCEN0_Bits.EN27 */
#define IFX_IOM_ACCEN0_EN27_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ACCEN0_Bits.EN27 */
#define IFX_IOM_ACCEN0_EN27_OFF (27u)

/** \brief  Length for Ifx_IOM_ACCEN0_Bits.EN28 */
#define IFX_IOM_ACCEN0_EN28_LEN (1u)

/** \brief  Mask for Ifx_IOM_ACCEN0_Bits.EN28 */
#define IFX_IOM_ACCEN0_EN28_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ACCEN0_Bits.EN28 */
#define IFX_IOM_ACCEN0_EN28_OFF (28u)

/** \brief  Length for Ifx_IOM_ACCEN0_Bits.EN29 */
#define IFX_IOM_ACCEN0_EN29_LEN (1u)

/** \brief  Mask for Ifx_IOM_ACCEN0_Bits.EN29 */
#define IFX_IOM_ACCEN0_EN29_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ACCEN0_Bits.EN29 */
#define IFX_IOM_ACCEN0_EN29_OFF (29u)

/** \brief  Length for Ifx_IOM_ACCEN0_Bits.EN2 */
#define IFX_IOM_ACCEN0_EN2_LEN (1u)

/** \brief  Mask for Ifx_IOM_ACCEN0_Bits.EN2 */
#define IFX_IOM_ACCEN0_EN2_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ACCEN0_Bits.EN2 */
#define IFX_IOM_ACCEN0_EN2_OFF (2u)

/** \brief  Length for Ifx_IOM_ACCEN0_Bits.EN30 */
#define IFX_IOM_ACCEN0_EN30_LEN (1u)

/** \brief  Mask for Ifx_IOM_ACCEN0_Bits.EN30 */
#define IFX_IOM_ACCEN0_EN30_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ACCEN0_Bits.EN30 */
#define IFX_IOM_ACCEN0_EN30_OFF (30u)

/** \brief  Length for Ifx_IOM_ACCEN0_Bits.EN31 */
#define IFX_IOM_ACCEN0_EN31_LEN (1u)

/** \brief  Mask for Ifx_IOM_ACCEN0_Bits.EN31 */
#define IFX_IOM_ACCEN0_EN31_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ACCEN0_Bits.EN31 */
#define IFX_IOM_ACCEN0_EN31_OFF (31u)

/** \brief  Length for Ifx_IOM_ACCEN0_Bits.EN3 */
#define IFX_IOM_ACCEN0_EN3_LEN (1u)

/** \brief  Mask for Ifx_IOM_ACCEN0_Bits.EN3 */
#define IFX_IOM_ACCEN0_EN3_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ACCEN0_Bits.EN3 */
#define IFX_IOM_ACCEN0_EN3_OFF (3u)

/** \brief  Length for Ifx_IOM_ACCEN0_Bits.EN4 */
#define IFX_IOM_ACCEN0_EN4_LEN (1u)

/** \brief  Mask for Ifx_IOM_ACCEN0_Bits.EN4 */
#define IFX_IOM_ACCEN0_EN4_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ACCEN0_Bits.EN4 */
#define IFX_IOM_ACCEN0_EN4_OFF (4u)

/** \brief  Length for Ifx_IOM_ACCEN0_Bits.EN5 */
#define IFX_IOM_ACCEN0_EN5_LEN (1u)

/** \brief  Mask for Ifx_IOM_ACCEN0_Bits.EN5 */
#define IFX_IOM_ACCEN0_EN5_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ACCEN0_Bits.EN5 */
#define IFX_IOM_ACCEN0_EN5_OFF (5u)

/** \brief  Length for Ifx_IOM_ACCEN0_Bits.EN6 */
#define IFX_IOM_ACCEN0_EN6_LEN (1u)

/** \brief  Mask for Ifx_IOM_ACCEN0_Bits.EN6 */
#define IFX_IOM_ACCEN0_EN6_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ACCEN0_Bits.EN6 */
#define IFX_IOM_ACCEN0_EN6_OFF (6u)

/** \brief  Length for Ifx_IOM_ACCEN0_Bits.EN7 */
#define IFX_IOM_ACCEN0_EN7_LEN (1u)

/** \brief  Mask for Ifx_IOM_ACCEN0_Bits.EN7 */
#define IFX_IOM_ACCEN0_EN7_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ACCEN0_Bits.EN7 */
#define IFX_IOM_ACCEN0_EN7_OFF (7u)

/** \brief  Length for Ifx_IOM_ACCEN0_Bits.EN8 */
#define IFX_IOM_ACCEN0_EN8_LEN (1u)

/** \brief  Mask for Ifx_IOM_ACCEN0_Bits.EN8 */
#define IFX_IOM_ACCEN0_EN8_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ACCEN0_Bits.EN8 */
#define IFX_IOM_ACCEN0_EN8_OFF (8u)

/** \brief  Length for Ifx_IOM_ACCEN0_Bits.EN9 */
#define IFX_IOM_ACCEN0_EN9_LEN (1u)

/** \brief  Mask for Ifx_IOM_ACCEN0_Bits.EN9 */
#define IFX_IOM_ACCEN0_EN9_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ACCEN0_Bits.EN9 */
#define IFX_IOM_ACCEN0_EN9_OFF (9u)

/** \brief  Length for Ifx_IOM_CLC_Bits.DISR */
#define IFX_IOM_CLC_DISR_LEN (1u)

/** \brief  Mask for Ifx_IOM_CLC_Bits.DISR */
#define IFX_IOM_CLC_DISR_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_CLC_Bits.DISR */
#define IFX_IOM_CLC_DISR_OFF (0u)

/** \brief  Length for Ifx_IOM_CLC_Bits.DISS */
#define IFX_IOM_CLC_DISS_LEN (1u)

/** \brief  Mask for Ifx_IOM_CLC_Bits.DISS */
#define IFX_IOM_CLC_DISS_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_CLC_Bits.DISS */
#define IFX_IOM_CLC_DISS_OFF (1u)

/** \brief  Length for Ifx_IOM_CLC_Bits.EDIS */
#define IFX_IOM_CLC_EDIS_LEN (1u)

/** \brief  Mask for Ifx_IOM_CLC_Bits.EDIS */
#define IFX_IOM_CLC_EDIS_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_CLC_Bits.EDIS */
#define IFX_IOM_CLC_EDIS_OFF (3u)

/** \brief  Length for Ifx_IOM_CLC_Bits.RMC */
#define IFX_IOM_CLC_RMC_LEN (8u)

/** \brief  Mask for Ifx_IOM_CLC_Bits.RMC */
#define IFX_IOM_CLC_RMC_MSK (0xffu)

/** \brief  Offset for Ifx_IOM_CLC_Bits.RMC */
#define IFX_IOM_CLC_RMC_OFF (8u)

/** \brief  Length for Ifx_IOM_ECMCCFG_Bits.SELC0 */
#define IFX_IOM_ECMCCFG_SELC0_LEN (4u)

/** \brief  Mask for Ifx_IOM_ECMCCFG_Bits.SELC0 */
#define IFX_IOM_ECMCCFG_SELC0_MSK (0xfu)

/** \brief  Offset for Ifx_IOM_ECMCCFG_Bits.SELC0 */
#define IFX_IOM_ECMCCFG_SELC0_OFF (0u)

/** \brief  Length for Ifx_IOM_ECMCCFG_Bits.SELC1 */
#define IFX_IOM_ECMCCFG_SELC1_LEN (4u)

/** \brief  Mask for Ifx_IOM_ECMCCFG_Bits.SELC1 */
#define IFX_IOM_ECMCCFG_SELC1_MSK (0xfu)

/** \brief  Offset for Ifx_IOM_ECMCCFG_Bits.SELC1 */
#define IFX_IOM_ECMCCFG_SELC1_OFF (8u)

/** \brief  Length for Ifx_IOM_ECMCCFG_Bits.SELC2 */
#define IFX_IOM_ECMCCFG_SELC2_LEN (4u)

/** \brief  Mask for Ifx_IOM_ECMCCFG_Bits.SELC2 */
#define IFX_IOM_ECMCCFG_SELC2_MSK (0xfu)

/** \brief  Offset for Ifx_IOM_ECMCCFG_Bits.SELC2 */
#define IFX_IOM_ECMCCFG_SELC2_OFF (16u)

/** \brief  Length for Ifx_IOM_ECMCCFG_Bits.SELC3 */
#define IFX_IOM_ECMCCFG_SELC3_LEN (4u)

/** \brief  Mask for Ifx_IOM_ECMCCFG_Bits.SELC3 */
#define IFX_IOM_ECMCCFG_SELC3_MSK (0xfu)

/** \brief  Offset for Ifx_IOM_ECMCCFG_Bits.SELC3 */
#define IFX_IOM_ECMCCFG_SELC3_OFF (24u)

/** \brief  Length for Ifx_IOM_ECMCCFG_Bits.THCR1 */
#define IFX_IOM_ECMCCFG_THCR1_LEN (4u)

/** \brief  Mask for Ifx_IOM_ECMCCFG_Bits.THCR1 */
#define IFX_IOM_ECMCCFG_THCR1_MSK (0xfu)

/** \brief  Offset for Ifx_IOM_ECMCCFG_Bits.THCR1 */
#define IFX_IOM_ECMCCFG_THCR1_OFF (12u)

/** \brief  Length for Ifx_IOM_ECMCCFG_Bits.THCR2 */
#define IFX_IOM_ECMCCFG_THCR2_LEN (4u)

/** \brief  Mask for Ifx_IOM_ECMCCFG_Bits.THCR2 */
#define IFX_IOM_ECMCCFG_THCR2_MSK (0xfu)

/** \brief  Offset for Ifx_IOM_ECMCCFG_Bits.THCR2 */
#define IFX_IOM_ECMCCFG_THCR2_OFF (20u)

/** \brief  Length for Ifx_IOM_ECMCCFG_Bits.THCR3 */
#define IFX_IOM_ECMCCFG_THCR3_LEN (4u)

/** \brief  Mask for Ifx_IOM_ECMCCFG_Bits.THCR3 */
#define IFX_IOM_ECMCCFG_THCR3_MSK (0xfu)

/** \brief  Offset for Ifx_IOM_ECMCCFG_Bits.THCR3 */
#define IFX_IOM_ECMCCFG_THCR3_OFF (28u)

/** \brief  Length for Ifx_IOM_ECMCCFG_Bits.THRC0 */
#define IFX_IOM_ECMCCFG_THRC0_LEN (4u)

/** \brief  Mask for Ifx_IOM_ECMCCFG_Bits.THRC0 */
#define IFX_IOM_ECMCCFG_THRC0_MSK (0xfu)

/** \brief  Offset for Ifx_IOM_ECMCCFG_Bits.THRC0 */
#define IFX_IOM_ECMCCFG_THRC0_OFF (4u)

/** \brief  Length for Ifx_IOM_ECMETH0_Bits.ETA0 */
#define IFX_IOM_ECMETH0_ETA0_LEN (1u)

/** \brief  Mask for Ifx_IOM_ECMETH0_Bits.ETA0 */
#define IFX_IOM_ECMETH0_ETA0_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ECMETH0_Bits.ETA0 */
#define IFX_IOM_ECMETH0_ETA0_OFF (0u)

/** \brief  Length for Ifx_IOM_ECMETH0_Bits.ETA10 */
#define IFX_IOM_ECMETH0_ETA10_LEN (1u)

/** \brief  Mask for Ifx_IOM_ECMETH0_Bits.ETA10 */
#define IFX_IOM_ECMETH0_ETA10_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ECMETH0_Bits.ETA10 */
#define IFX_IOM_ECMETH0_ETA10_OFF (10u)

/** \brief  Length for Ifx_IOM_ECMETH0_Bits.ETA11 */
#define IFX_IOM_ECMETH0_ETA11_LEN (1u)

/** \brief  Mask for Ifx_IOM_ECMETH0_Bits.ETA11 */
#define IFX_IOM_ECMETH0_ETA11_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ECMETH0_Bits.ETA11 */
#define IFX_IOM_ECMETH0_ETA11_OFF (11u)

/** \brief  Length for Ifx_IOM_ECMETH0_Bits.ETA12 */
#define IFX_IOM_ECMETH0_ETA12_LEN (1u)

/** \brief  Mask for Ifx_IOM_ECMETH0_Bits.ETA12 */
#define IFX_IOM_ECMETH0_ETA12_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ECMETH0_Bits.ETA12 */
#define IFX_IOM_ECMETH0_ETA12_OFF (12u)

/** \brief  Length for Ifx_IOM_ECMETH0_Bits.ETA13 */
#define IFX_IOM_ECMETH0_ETA13_LEN (1u)

/** \brief  Mask for Ifx_IOM_ECMETH0_Bits.ETA13 */
#define IFX_IOM_ECMETH0_ETA13_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ECMETH0_Bits.ETA13 */
#define IFX_IOM_ECMETH0_ETA13_OFF (13u)

/** \brief  Length for Ifx_IOM_ECMETH0_Bits.ETA14 */
#define IFX_IOM_ECMETH0_ETA14_LEN (1u)

/** \brief  Mask for Ifx_IOM_ECMETH0_Bits.ETA14 */
#define IFX_IOM_ECMETH0_ETA14_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ECMETH0_Bits.ETA14 */
#define IFX_IOM_ECMETH0_ETA14_OFF (14u)

/** \brief  Length for Ifx_IOM_ECMETH0_Bits.ETA15 */
#define IFX_IOM_ECMETH0_ETA15_LEN (1u)

/** \brief  Mask for Ifx_IOM_ECMETH0_Bits.ETA15 */
#define IFX_IOM_ECMETH0_ETA15_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ECMETH0_Bits.ETA15 */
#define IFX_IOM_ECMETH0_ETA15_OFF (15u)

/** \brief  Length for Ifx_IOM_ECMETH0_Bits.ETA1 */
#define IFX_IOM_ECMETH0_ETA1_LEN (1u)

/** \brief  Mask for Ifx_IOM_ECMETH0_Bits.ETA1 */
#define IFX_IOM_ECMETH0_ETA1_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ECMETH0_Bits.ETA1 */
#define IFX_IOM_ECMETH0_ETA1_OFF (1u)

/** \brief  Length for Ifx_IOM_ECMETH0_Bits.ETA2 */
#define IFX_IOM_ECMETH0_ETA2_LEN (1u)

/** \brief  Mask for Ifx_IOM_ECMETH0_Bits.ETA2 */
#define IFX_IOM_ECMETH0_ETA2_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ECMETH0_Bits.ETA2 */
#define IFX_IOM_ECMETH0_ETA2_OFF (2u)

/** \brief  Length for Ifx_IOM_ECMETH0_Bits.ETA3 */
#define IFX_IOM_ECMETH0_ETA3_LEN (1u)

/** \brief  Mask for Ifx_IOM_ECMETH0_Bits.ETA3 */
#define IFX_IOM_ECMETH0_ETA3_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ECMETH0_Bits.ETA3 */
#define IFX_IOM_ECMETH0_ETA3_OFF (3u)

/** \brief  Length for Ifx_IOM_ECMETH0_Bits.ETA4 */
#define IFX_IOM_ECMETH0_ETA4_LEN (1u)

/** \brief  Mask for Ifx_IOM_ECMETH0_Bits.ETA4 */
#define IFX_IOM_ECMETH0_ETA4_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ECMETH0_Bits.ETA4 */
#define IFX_IOM_ECMETH0_ETA4_OFF (4u)

/** \brief  Length for Ifx_IOM_ECMETH0_Bits.ETA5 */
#define IFX_IOM_ECMETH0_ETA5_LEN (1u)

/** \brief  Mask for Ifx_IOM_ECMETH0_Bits.ETA5 */
#define IFX_IOM_ECMETH0_ETA5_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ECMETH0_Bits.ETA5 */
#define IFX_IOM_ECMETH0_ETA5_OFF (5u)

/** \brief  Length for Ifx_IOM_ECMETH0_Bits.ETA6 */
#define IFX_IOM_ECMETH0_ETA6_LEN (1u)

/** \brief  Mask for Ifx_IOM_ECMETH0_Bits.ETA6 */
#define IFX_IOM_ECMETH0_ETA6_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ECMETH0_Bits.ETA6 */
#define IFX_IOM_ECMETH0_ETA6_OFF (6u)

/** \brief  Length for Ifx_IOM_ECMETH0_Bits.ETA7 */
#define IFX_IOM_ECMETH0_ETA7_LEN (1u)

/** \brief  Mask for Ifx_IOM_ECMETH0_Bits.ETA7 */
#define IFX_IOM_ECMETH0_ETA7_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ECMETH0_Bits.ETA7 */
#define IFX_IOM_ECMETH0_ETA7_OFF (7u)

/** \brief  Length for Ifx_IOM_ECMETH0_Bits.ETA8 */
#define IFX_IOM_ECMETH0_ETA8_LEN (1u)

/** \brief  Mask for Ifx_IOM_ECMETH0_Bits.ETA8 */
#define IFX_IOM_ECMETH0_ETA8_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ECMETH0_Bits.ETA8 */
#define IFX_IOM_ECMETH0_ETA8_OFF (8u)

/** \brief  Length for Ifx_IOM_ECMETH0_Bits.ETA9 */
#define IFX_IOM_ECMETH0_ETA9_LEN (1u)

/** \brief  Mask for Ifx_IOM_ECMETH0_Bits.ETA9 */
#define IFX_IOM_ECMETH0_ETA9_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ECMETH0_Bits.ETA9 */
#define IFX_IOM_ECMETH0_ETA9_OFF (9u)

/** \brief  Length for Ifx_IOM_ECMETH0_Bits.ETB0 */
#define IFX_IOM_ECMETH0_ETB0_LEN (1u)

/** \brief  Mask for Ifx_IOM_ECMETH0_Bits.ETB0 */
#define IFX_IOM_ECMETH0_ETB0_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ECMETH0_Bits.ETB0 */
#define IFX_IOM_ECMETH0_ETB0_OFF (16u)

/** \brief  Length for Ifx_IOM_ECMETH0_Bits.ETB10 */
#define IFX_IOM_ECMETH0_ETB10_LEN (1u)

/** \brief  Mask for Ifx_IOM_ECMETH0_Bits.ETB10 */
#define IFX_IOM_ECMETH0_ETB10_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ECMETH0_Bits.ETB10 */
#define IFX_IOM_ECMETH0_ETB10_OFF (26u)

/** \brief  Length for Ifx_IOM_ECMETH0_Bits.ETB11 */
#define IFX_IOM_ECMETH0_ETB11_LEN (1u)

/** \brief  Mask for Ifx_IOM_ECMETH0_Bits.ETB11 */
#define IFX_IOM_ECMETH0_ETB11_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ECMETH0_Bits.ETB11 */
#define IFX_IOM_ECMETH0_ETB11_OFF (27u)

/** \brief  Length for Ifx_IOM_ECMETH0_Bits.ETB12 */
#define IFX_IOM_ECMETH0_ETB12_LEN (1u)

/** \brief  Mask for Ifx_IOM_ECMETH0_Bits.ETB12 */
#define IFX_IOM_ECMETH0_ETB12_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ECMETH0_Bits.ETB12 */
#define IFX_IOM_ECMETH0_ETB12_OFF (28u)

/** \brief  Length for Ifx_IOM_ECMETH0_Bits.ETB13 */
#define IFX_IOM_ECMETH0_ETB13_LEN (1u)

/** \brief  Mask for Ifx_IOM_ECMETH0_Bits.ETB13 */
#define IFX_IOM_ECMETH0_ETB13_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ECMETH0_Bits.ETB13 */
#define IFX_IOM_ECMETH0_ETB13_OFF (29u)

/** \brief  Length for Ifx_IOM_ECMETH0_Bits.ETB14 */
#define IFX_IOM_ECMETH0_ETB14_LEN (1u)

/** \brief  Mask for Ifx_IOM_ECMETH0_Bits.ETB14 */
#define IFX_IOM_ECMETH0_ETB14_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ECMETH0_Bits.ETB14 */
#define IFX_IOM_ECMETH0_ETB14_OFF (30u)

/** \brief  Length for Ifx_IOM_ECMETH0_Bits.ETB15 */
#define IFX_IOM_ECMETH0_ETB15_LEN (1u)

/** \brief  Mask for Ifx_IOM_ECMETH0_Bits.ETB15 */
#define IFX_IOM_ECMETH0_ETB15_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ECMETH0_Bits.ETB15 */
#define IFX_IOM_ECMETH0_ETB15_OFF (31u)

/** \brief  Length for Ifx_IOM_ECMETH0_Bits.ETB1 */
#define IFX_IOM_ECMETH0_ETB1_LEN (1u)

/** \brief  Mask for Ifx_IOM_ECMETH0_Bits.ETB1 */
#define IFX_IOM_ECMETH0_ETB1_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ECMETH0_Bits.ETB1 */
#define IFX_IOM_ECMETH0_ETB1_OFF (17u)

/** \brief  Length for Ifx_IOM_ECMETH0_Bits.ETB2 */
#define IFX_IOM_ECMETH0_ETB2_LEN (1u)

/** \brief  Mask for Ifx_IOM_ECMETH0_Bits.ETB2 */
#define IFX_IOM_ECMETH0_ETB2_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ECMETH0_Bits.ETB2 */
#define IFX_IOM_ECMETH0_ETB2_OFF (18u)

/** \brief  Length for Ifx_IOM_ECMETH0_Bits.ETB3 */
#define IFX_IOM_ECMETH0_ETB3_LEN (1u)

/** \brief  Mask for Ifx_IOM_ECMETH0_Bits.ETB3 */
#define IFX_IOM_ECMETH0_ETB3_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ECMETH0_Bits.ETB3 */
#define IFX_IOM_ECMETH0_ETB3_OFF (19u)

/** \brief  Length for Ifx_IOM_ECMETH0_Bits.ETB4 */
#define IFX_IOM_ECMETH0_ETB4_LEN (1u)

/** \brief  Mask for Ifx_IOM_ECMETH0_Bits.ETB4 */
#define IFX_IOM_ECMETH0_ETB4_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ECMETH0_Bits.ETB4 */
#define IFX_IOM_ECMETH0_ETB4_OFF (20u)

/** \brief  Length for Ifx_IOM_ECMETH0_Bits.ETB5 */
#define IFX_IOM_ECMETH0_ETB5_LEN (1u)

/** \brief  Mask for Ifx_IOM_ECMETH0_Bits.ETB5 */
#define IFX_IOM_ECMETH0_ETB5_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ECMETH0_Bits.ETB5 */
#define IFX_IOM_ECMETH0_ETB5_OFF (21u)

/** \brief  Length for Ifx_IOM_ECMETH0_Bits.ETB6 */
#define IFX_IOM_ECMETH0_ETB6_LEN (1u)

/** \brief  Mask for Ifx_IOM_ECMETH0_Bits.ETB6 */
#define IFX_IOM_ECMETH0_ETB6_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ECMETH0_Bits.ETB6 */
#define IFX_IOM_ECMETH0_ETB6_OFF (22u)

/** \brief  Length for Ifx_IOM_ECMETH0_Bits.ETB7 */
#define IFX_IOM_ECMETH0_ETB7_LEN (1u)

/** \brief  Mask for Ifx_IOM_ECMETH0_Bits.ETB7 */
#define IFX_IOM_ECMETH0_ETB7_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ECMETH0_Bits.ETB7 */
#define IFX_IOM_ECMETH0_ETB7_OFF (23u)

/** \brief  Length for Ifx_IOM_ECMETH0_Bits.ETB8 */
#define IFX_IOM_ECMETH0_ETB8_LEN (1u)

/** \brief  Mask for Ifx_IOM_ECMETH0_Bits.ETB8 */
#define IFX_IOM_ECMETH0_ETB8_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ECMETH0_Bits.ETB8 */
#define IFX_IOM_ECMETH0_ETB8_OFF (24u)

/** \brief  Length for Ifx_IOM_ECMETH0_Bits.ETB9 */
#define IFX_IOM_ECMETH0_ETB9_LEN (1u)

/** \brief  Mask for Ifx_IOM_ECMETH0_Bits.ETB9 */
#define IFX_IOM_ECMETH0_ETB9_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ECMETH0_Bits.ETB9 */
#define IFX_IOM_ECMETH0_ETB9_OFF (25u)

/** \brief  Length for Ifx_IOM_ECMETH1_Bits.ETC0 */
#define IFX_IOM_ECMETH1_ETC0_LEN (1u)

/** \brief  Mask for Ifx_IOM_ECMETH1_Bits.ETC0 */
#define IFX_IOM_ECMETH1_ETC0_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ECMETH1_Bits.ETC0 */
#define IFX_IOM_ECMETH1_ETC0_OFF (0u)

/** \brief  Length for Ifx_IOM_ECMETH1_Bits.ETC10 */
#define IFX_IOM_ECMETH1_ETC10_LEN (1u)

/** \brief  Mask for Ifx_IOM_ECMETH1_Bits.ETC10 */
#define IFX_IOM_ECMETH1_ETC10_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ECMETH1_Bits.ETC10 */
#define IFX_IOM_ECMETH1_ETC10_OFF (10u)

/** \brief  Length for Ifx_IOM_ECMETH1_Bits.ETC11 */
#define IFX_IOM_ECMETH1_ETC11_LEN (1u)

/** \brief  Mask for Ifx_IOM_ECMETH1_Bits.ETC11 */
#define IFX_IOM_ECMETH1_ETC11_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ECMETH1_Bits.ETC11 */
#define IFX_IOM_ECMETH1_ETC11_OFF (11u)

/** \brief  Length for Ifx_IOM_ECMETH1_Bits.ETC12 */
#define IFX_IOM_ECMETH1_ETC12_LEN (1u)

/** \brief  Mask for Ifx_IOM_ECMETH1_Bits.ETC12 */
#define IFX_IOM_ECMETH1_ETC12_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ECMETH1_Bits.ETC12 */
#define IFX_IOM_ECMETH1_ETC12_OFF (12u)

/** \brief  Length for Ifx_IOM_ECMETH1_Bits.ETC13 */
#define IFX_IOM_ECMETH1_ETC13_LEN (1u)

/** \brief  Mask for Ifx_IOM_ECMETH1_Bits.ETC13 */
#define IFX_IOM_ECMETH1_ETC13_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ECMETH1_Bits.ETC13 */
#define IFX_IOM_ECMETH1_ETC13_OFF (13u)

/** \brief  Length for Ifx_IOM_ECMETH1_Bits.ETC14 */
#define IFX_IOM_ECMETH1_ETC14_LEN (1u)

/** \brief  Mask for Ifx_IOM_ECMETH1_Bits.ETC14 */
#define IFX_IOM_ECMETH1_ETC14_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ECMETH1_Bits.ETC14 */
#define IFX_IOM_ECMETH1_ETC14_OFF (14u)

/** \brief  Length for Ifx_IOM_ECMETH1_Bits.ETC15 */
#define IFX_IOM_ECMETH1_ETC15_LEN (1u)

/** \brief  Mask for Ifx_IOM_ECMETH1_Bits.ETC15 */
#define IFX_IOM_ECMETH1_ETC15_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ECMETH1_Bits.ETC15 */
#define IFX_IOM_ECMETH1_ETC15_OFF (15u)

/** \brief  Length for Ifx_IOM_ECMETH1_Bits.ETC1 */
#define IFX_IOM_ECMETH1_ETC1_LEN (1u)

/** \brief  Mask for Ifx_IOM_ECMETH1_Bits.ETC1 */
#define IFX_IOM_ECMETH1_ETC1_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ECMETH1_Bits.ETC1 */
#define IFX_IOM_ECMETH1_ETC1_OFF (1u)

/** \brief  Length for Ifx_IOM_ECMETH1_Bits.ETC2 */
#define IFX_IOM_ECMETH1_ETC2_LEN (1u)

/** \brief  Mask for Ifx_IOM_ECMETH1_Bits.ETC2 */
#define IFX_IOM_ECMETH1_ETC2_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ECMETH1_Bits.ETC2 */
#define IFX_IOM_ECMETH1_ETC2_OFF (2u)

/** \brief  Length for Ifx_IOM_ECMETH1_Bits.ETC3 */
#define IFX_IOM_ECMETH1_ETC3_LEN (1u)

/** \brief  Mask for Ifx_IOM_ECMETH1_Bits.ETC3 */
#define IFX_IOM_ECMETH1_ETC3_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ECMETH1_Bits.ETC3 */
#define IFX_IOM_ECMETH1_ETC3_OFF (3u)

/** \brief  Length for Ifx_IOM_ECMETH1_Bits.ETC4 */
#define IFX_IOM_ECMETH1_ETC4_LEN (1u)

/** \brief  Mask for Ifx_IOM_ECMETH1_Bits.ETC4 */
#define IFX_IOM_ECMETH1_ETC4_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ECMETH1_Bits.ETC4 */
#define IFX_IOM_ECMETH1_ETC4_OFF (4u)

/** \brief  Length for Ifx_IOM_ECMETH1_Bits.ETC5 */
#define IFX_IOM_ECMETH1_ETC5_LEN (1u)

/** \brief  Mask for Ifx_IOM_ECMETH1_Bits.ETC5 */
#define IFX_IOM_ECMETH1_ETC5_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ECMETH1_Bits.ETC5 */
#define IFX_IOM_ECMETH1_ETC5_OFF (5u)

/** \brief  Length for Ifx_IOM_ECMETH1_Bits.ETC6 */
#define IFX_IOM_ECMETH1_ETC6_LEN (1u)

/** \brief  Mask for Ifx_IOM_ECMETH1_Bits.ETC6 */
#define IFX_IOM_ECMETH1_ETC6_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ECMETH1_Bits.ETC6 */
#define IFX_IOM_ECMETH1_ETC6_OFF (6u)

/** \brief  Length for Ifx_IOM_ECMETH1_Bits.ETC7 */
#define IFX_IOM_ECMETH1_ETC7_LEN (1u)

/** \brief  Mask for Ifx_IOM_ECMETH1_Bits.ETC7 */
#define IFX_IOM_ECMETH1_ETC7_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ECMETH1_Bits.ETC7 */
#define IFX_IOM_ECMETH1_ETC7_OFF (7u)

/** \brief  Length for Ifx_IOM_ECMETH1_Bits.ETC8 */
#define IFX_IOM_ECMETH1_ETC8_LEN (1u)

/** \brief  Mask for Ifx_IOM_ECMETH1_Bits.ETC8 */
#define IFX_IOM_ECMETH1_ETC8_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ECMETH1_Bits.ETC8 */
#define IFX_IOM_ECMETH1_ETC8_OFF (8u)

/** \brief  Length for Ifx_IOM_ECMETH1_Bits.ETC9 */
#define IFX_IOM_ECMETH1_ETC9_LEN (1u)

/** \brief  Mask for Ifx_IOM_ECMETH1_Bits.ETC9 */
#define IFX_IOM_ECMETH1_ETC9_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ECMETH1_Bits.ETC9 */
#define IFX_IOM_ECMETH1_ETC9_OFF (9u)

/** \brief  Length for Ifx_IOM_ECMETH1_Bits.ETD0 */
#define IFX_IOM_ECMETH1_ETD0_LEN (1u)

/** \brief  Mask for Ifx_IOM_ECMETH1_Bits.ETD0 */
#define IFX_IOM_ECMETH1_ETD0_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ECMETH1_Bits.ETD0 */
#define IFX_IOM_ECMETH1_ETD0_OFF (16u)

/** \brief  Length for Ifx_IOM_ECMETH1_Bits.ETD10 */
#define IFX_IOM_ECMETH1_ETD10_LEN (1u)

/** \brief  Mask for Ifx_IOM_ECMETH1_Bits.ETD10 */
#define IFX_IOM_ECMETH1_ETD10_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ECMETH1_Bits.ETD10 */
#define IFX_IOM_ECMETH1_ETD10_OFF (26u)

/** \brief  Length for Ifx_IOM_ECMETH1_Bits.ETD11 */
#define IFX_IOM_ECMETH1_ETD11_LEN (1u)

/** \brief  Mask for Ifx_IOM_ECMETH1_Bits.ETD11 */
#define IFX_IOM_ECMETH1_ETD11_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ECMETH1_Bits.ETD11 */
#define IFX_IOM_ECMETH1_ETD11_OFF (27u)

/** \brief  Length for Ifx_IOM_ECMETH1_Bits.ETD12 */
#define IFX_IOM_ECMETH1_ETD12_LEN (1u)

/** \brief  Mask for Ifx_IOM_ECMETH1_Bits.ETD12 */
#define IFX_IOM_ECMETH1_ETD12_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ECMETH1_Bits.ETD12 */
#define IFX_IOM_ECMETH1_ETD12_OFF (28u)

/** \brief  Length for Ifx_IOM_ECMETH1_Bits.ETD13 */
#define IFX_IOM_ECMETH1_ETD13_LEN (1u)

/** \brief  Mask for Ifx_IOM_ECMETH1_Bits.ETD13 */
#define IFX_IOM_ECMETH1_ETD13_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ECMETH1_Bits.ETD13 */
#define IFX_IOM_ECMETH1_ETD13_OFF (29u)

/** \brief  Length for Ifx_IOM_ECMETH1_Bits.ETD14 */
#define IFX_IOM_ECMETH1_ETD14_LEN (1u)

/** \brief  Mask for Ifx_IOM_ECMETH1_Bits.ETD14 */
#define IFX_IOM_ECMETH1_ETD14_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ECMETH1_Bits.ETD14 */
#define IFX_IOM_ECMETH1_ETD14_OFF (30u)

/** \brief  Length for Ifx_IOM_ECMETH1_Bits.ETD15 */
#define IFX_IOM_ECMETH1_ETD15_LEN (1u)

/** \brief  Mask for Ifx_IOM_ECMETH1_Bits.ETD15 */
#define IFX_IOM_ECMETH1_ETD15_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ECMETH1_Bits.ETD15 */
#define IFX_IOM_ECMETH1_ETD15_OFF (31u)

/** \brief  Length for Ifx_IOM_ECMETH1_Bits.ETD1 */
#define IFX_IOM_ECMETH1_ETD1_LEN (1u)

/** \brief  Mask for Ifx_IOM_ECMETH1_Bits.ETD1 */
#define IFX_IOM_ECMETH1_ETD1_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ECMETH1_Bits.ETD1 */
#define IFX_IOM_ECMETH1_ETD1_OFF (17u)

/** \brief  Length for Ifx_IOM_ECMETH1_Bits.ETD2 */
#define IFX_IOM_ECMETH1_ETD2_LEN (1u)

/** \brief  Mask for Ifx_IOM_ECMETH1_Bits.ETD2 */
#define IFX_IOM_ECMETH1_ETD2_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ECMETH1_Bits.ETD2 */
#define IFX_IOM_ECMETH1_ETD2_OFF (18u)

/** \brief  Length for Ifx_IOM_ECMETH1_Bits.ETD3 */
#define IFX_IOM_ECMETH1_ETD3_LEN (1u)

/** \brief  Mask for Ifx_IOM_ECMETH1_Bits.ETD3 */
#define IFX_IOM_ECMETH1_ETD3_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ECMETH1_Bits.ETD3 */
#define IFX_IOM_ECMETH1_ETD3_OFF (19u)

/** \brief  Length for Ifx_IOM_ECMETH1_Bits.ETD4 */
#define IFX_IOM_ECMETH1_ETD4_LEN (1u)

/** \brief  Mask for Ifx_IOM_ECMETH1_Bits.ETD4 */
#define IFX_IOM_ECMETH1_ETD4_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ECMETH1_Bits.ETD4 */
#define IFX_IOM_ECMETH1_ETD4_OFF (20u)

/** \brief  Length for Ifx_IOM_ECMETH1_Bits.ETD5 */
#define IFX_IOM_ECMETH1_ETD5_LEN (1u)

/** \brief  Mask for Ifx_IOM_ECMETH1_Bits.ETD5 */
#define IFX_IOM_ECMETH1_ETD5_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ECMETH1_Bits.ETD5 */
#define IFX_IOM_ECMETH1_ETD5_OFF (21u)

/** \brief  Length for Ifx_IOM_ECMETH1_Bits.ETD6 */
#define IFX_IOM_ECMETH1_ETD6_LEN (1u)

/** \brief  Mask for Ifx_IOM_ECMETH1_Bits.ETD6 */
#define IFX_IOM_ECMETH1_ETD6_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ECMETH1_Bits.ETD6 */
#define IFX_IOM_ECMETH1_ETD6_OFF (22u)

/** \brief  Length for Ifx_IOM_ECMETH1_Bits.ETD7 */
#define IFX_IOM_ECMETH1_ETD7_LEN (1u)

/** \brief  Mask for Ifx_IOM_ECMETH1_Bits.ETD7 */
#define IFX_IOM_ECMETH1_ETD7_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ECMETH1_Bits.ETD7 */
#define IFX_IOM_ECMETH1_ETD7_OFF (23u)

/** \brief  Length for Ifx_IOM_ECMETH1_Bits.ETD8 */
#define IFX_IOM_ECMETH1_ETD8_LEN (1u)

/** \brief  Mask for Ifx_IOM_ECMETH1_Bits.ETD8 */
#define IFX_IOM_ECMETH1_ETD8_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ECMETH1_Bits.ETD8 */
#define IFX_IOM_ECMETH1_ETD8_OFF (24u)

/** \brief  Length for Ifx_IOM_ECMETH1_Bits.ETD9 */
#define IFX_IOM_ECMETH1_ETD9_LEN (1u)

/** \brief  Mask for Ifx_IOM_ECMETH1_Bits.ETD9 */
#define IFX_IOM_ECMETH1_ETD9_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ECMETH1_Bits.ETD9 */
#define IFX_IOM_ECMETH1_ETD9_OFF (25u)

/** \brief  Length for Ifx_IOM_ECMSELR_Bits.CES0 */
#define IFX_IOM_ECMSELR_CES0_LEN (1u)

/** \brief  Mask for Ifx_IOM_ECMSELR_Bits.CES0 */
#define IFX_IOM_ECMSELR_CES0_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ECMSELR_Bits.CES0 */
#define IFX_IOM_ECMSELR_CES0_OFF (0u)

/** \brief  Length for Ifx_IOM_ECMSELR_Bits.CES10 */
#define IFX_IOM_ECMSELR_CES10_LEN (1u)

/** \brief  Mask for Ifx_IOM_ECMSELR_Bits.CES10 */
#define IFX_IOM_ECMSELR_CES10_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ECMSELR_Bits.CES10 */
#define IFX_IOM_ECMSELR_CES10_OFF (10u)

/** \brief  Length for Ifx_IOM_ECMSELR_Bits.CES11 */
#define IFX_IOM_ECMSELR_CES11_LEN (1u)

/** \brief  Mask for Ifx_IOM_ECMSELR_Bits.CES11 */
#define IFX_IOM_ECMSELR_CES11_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ECMSELR_Bits.CES11 */
#define IFX_IOM_ECMSELR_CES11_OFF (11u)

/** \brief  Length for Ifx_IOM_ECMSELR_Bits.CES12 */
#define IFX_IOM_ECMSELR_CES12_LEN (1u)

/** \brief  Mask for Ifx_IOM_ECMSELR_Bits.CES12 */
#define IFX_IOM_ECMSELR_CES12_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ECMSELR_Bits.CES12 */
#define IFX_IOM_ECMSELR_CES12_OFF (12u)

/** \brief  Length for Ifx_IOM_ECMSELR_Bits.CES13 */
#define IFX_IOM_ECMSELR_CES13_LEN (1u)

/** \brief  Mask for Ifx_IOM_ECMSELR_Bits.CES13 */
#define IFX_IOM_ECMSELR_CES13_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ECMSELR_Bits.CES13 */
#define IFX_IOM_ECMSELR_CES13_OFF (13u)

/** \brief  Length for Ifx_IOM_ECMSELR_Bits.CES14 */
#define IFX_IOM_ECMSELR_CES14_LEN (1u)

/** \brief  Mask for Ifx_IOM_ECMSELR_Bits.CES14 */
#define IFX_IOM_ECMSELR_CES14_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ECMSELR_Bits.CES14 */
#define IFX_IOM_ECMSELR_CES14_OFF (14u)

/** \brief  Length for Ifx_IOM_ECMSELR_Bits.CES15 */
#define IFX_IOM_ECMSELR_CES15_LEN (1u)

/** \brief  Mask for Ifx_IOM_ECMSELR_Bits.CES15 */
#define IFX_IOM_ECMSELR_CES15_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ECMSELR_Bits.CES15 */
#define IFX_IOM_ECMSELR_CES15_OFF (15u)

/** \brief  Length for Ifx_IOM_ECMSELR_Bits.CES1 */
#define IFX_IOM_ECMSELR_CES1_LEN (1u)

/** \brief  Mask for Ifx_IOM_ECMSELR_Bits.CES1 */
#define IFX_IOM_ECMSELR_CES1_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ECMSELR_Bits.CES1 */
#define IFX_IOM_ECMSELR_CES1_OFF (1u)

/** \brief  Length for Ifx_IOM_ECMSELR_Bits.CES2 */
#define IFX_IOM_ECMSELR_CES2_LEN (1u)

/** \brief  Mask for Ifx_IOM_ECMSELR_Bits.CES2 */
#define IFX_IOM_ECMSELR_CES2_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ECMSELR_Bits.CES2 */
#define IFX_IOM_ECMSELR_CES2_OFF (2u)

/** \brief  Length for Ifx_IOM_ECMSELR_Bits.CES3 */
#define IFX_IOM_ECMSELR_CES3_LEN (1u)

/** \brief  Mask for Ifx_IOM_ECMSELR_Bits.CES3 */
#define IFX_IOM_ECMSELR_CES3_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ECMSELR_Bits.CES3 */
#define IFX_IOM_ECMSELR_CES3_OFF (3u)

/** \brief  Length for Ifx_IOM_ECMSELR_Bits.CES4 */
#define IFX_IOM_ECMSELR_CES4_LEN (1u)

/** \brief  Mask for Ifx_IOM_ECMSELR_Bits.CES4 */
#define IFX_IOM_ECMSELR_CES4_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ECMSELR_Bits.CES4 */
#define IFX_IOM_ECMSELR_CES4_OFF (4u)

/** \brief  Length for Ifx_IOM_ECMSELR_Bits.CES5 */
#define IFX_IOM_ECMSELR_CES5_LEN (1u)

/** \brief  Mask for Ifx_IOM_ECMSELR_Bits.CES5 */
#define IFX_IOM_ECMSELR_CES5_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ECMSELR_Bits.CES5 */
#define IFX_IOM_ECMSELR_CES5_OFF (5u)

/** \brief  Length for Ifx_IOM_ECMSELR_Bits.CES6 */
#define IFX_IOM_ECMSELR_CES6_LEN (1u)

/** \brief  Mask for Ifx_IOM_ECMSELR_Bits.CES6 */
#define IFX_IOM_ECMSELR_CES6_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ECMSELR_Bits.CES6 */
#define IFX_IOM_ECMSELR_CES6_OFF (6u)

/** \brief  Length for Ifx_IOM_ECMSELR_Bits.CES7 */
#define IFX_IOM_ECMSELR_CES7_LEN (1u)

/** \brief  Mask for Ifx_IOM_ECMSELR_Bits.CES7 */
#define IFX_IOM_ECMSELR_CES7_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ECMSELR_Bits.CES7 */
#define IFX_IOM_ECMSELR_CES7_OFF (7u)

/** \brief  Length for Ifx_IOM_ECMSELR_Bits.CES8 */
#define IFX_IOM_ECMSELR_CES8_LEN (1u)

/** \brief  Mask for Ifx_IOM_ECMSELR_Bits.CES8 */
#define IFX_IOM_ECMSELR_CES8_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ECMSELR_Bits.CES8 */
#define IFX_IOM_ECMSELR_CES8_OFF (8u)

/** \brief  Length for Ifx_IOM_ECMSELR_Bits.CES9 */
#define IFX_IOM_ECMSELR_CES9_LEN (1u)

/** \brief  Mask for Ifx_IOM_ECMSELR_Bits.CES9 */
#define IFX_IOM_ECMSELR_CES9_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ECMSELR_Bits.CES9 */
#define IFX_IOM_ECMSELR_CES9_OFF (9u)

/** \brief  Length for Ifx_IOM_ECMSELR_Bits.CTS0 */
#define IFX_IOM_ECMSELR_CTS0_LEN (1u)

/** \brief  Mask for Ifx_IOM_ECMSELR_Bits.CTS0 */
#define IFX_IOM_ECMSELR_CTS0_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ECMSELR_Bits.CTS0 */
#define IFX_IOM_ECMSELR_CTS0_OFF (16u)

/** \brief  Length for Ifx_IOM_ECMSELR_Bits.CTS1 */
#define IFX_IOM_ECMSELR_CTS1_LEN (1u)

/** \brief  Mask for Ifx_IOM_ECMSELR_Bits.CTS1 */
#define IFX_IOM_ECMSELR_CTS1_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ECMSELR_Bits.CTS1 */
#define IFX_IOM_ECMSELR_CTS1_OFF (17u)

/** \brief  Length for Ifx_IOM_ECMSELR_Bits.CTS2 */
#define IFX_IOM_ECMSELR_CTS2_LEN (1u)

/** \brief  Mask for Ifx_IOM_ECMSELR_Bits.CTS2 */
#define IFX_IOM_ECMSELR_CTS2_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ECMSELR_Bits.CTS2 */
#define IFX_IOM_ECMSELR_CTS2_OFF (18u)

/** \brief  Length for Ifx_IOM_ECMSELR_Bits.CTS3 */
#define IFX_IOM_ECMSELR_CTS3_LEN (1u)

/** \brief  Mask for Ifx_IOM_ECMSELR_Bits.CTS3 */
#define IFX_IOM_ECMSELR_CTS3_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_ECMSELR_Bits.CTS3 */
#define IFX_IOM_ECMSELR_CTS3_OFF (19u)

/** \brief  Length for Ifx_IOM_FPCCTR_Bits.CMP */
#define IFX_IOM_FPCCTR_CMP_LEN (16u)

/** \brief  Mask for Ifx_IOM_FPCCTR_Bits.CMP */
#define IFX_IOM_FPCCTR_CMP_MSK (0xffffu)

/** \brief  Offset for Ifx_IOM_FPCCTR_Bits.CMP */
#define IFX_IOM_FPCCTR_CMP_OFF (0u)

/** \brief  Length for Ifx_IOM_FPCCTR_Bits.ISM */
#define IFX_IOM_FPCCTR_ISM_LEN (2u)

/** \brief  Mask for Ifx_IOM_FPCCTR_Bits.ISM */
#define IFX_IOM_FPCCTR_ISM_MSK (0x3u)

/** \brief  Offset for Ifx_IOM_FPCCTR_Bits.ISM */
#define IFX_IOM_FPCCTR_ISM_OFF (19u)

/** \brief  Length for Ifx_IOM_FPCCTR_Bits.ISR */
#define IFX_IOM_FPCCTR_ISR_LEN (3u)

/** \brief  Mask for Ifx_IOM_FPCCTR_Bits.ISR */
#define IFX_IOM_FPCCTR_ISR_MSK (0x7u)

/** \brief  Offset for Ifx_IOM_FPCCTR_Bits.ISR */
#define IFX_IOM_FPCCTR_ISR_OFF (24u)

/** \brief  Length for Ifx_IOM_FPCCTR_Bits.MOD */
#define IFX_IOM_FPCCTR_MOD_LEN (3u)

/** \brief  Mask for Ifx_IOM_FPCCTR_Bits.MOD */
#define IFX_IOM_FPCCTR_MOD_MSK (0x7u)

/** \brief  Offset for Ifx_IOM_FPCCTR_Bits.MOD */
#define IFX_IOM_FPCCTR_MOD_OFF (16u)

/** \brief  Length for Ifx_IOM_FPCCTR_Bits.RTG */
#define IFX_IOM_FPCCTR_RTG_LEN (1u)

/** \brief  Mask for Ifx_IOM_FPCCTR_Bits.RTG */
#define IFX_IOM_FPCCTR_RTG_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_FPCCTR_Bits.RTG */
#define IFX_IOM_FPCCTR_RTG_OFF (22u)

/** \brief  Length for Ifx_IOM_FPCESR_Bits.FEG0 */
#define IFX_IOM_FPCESR_FEG0_LEN (1u)

/** \brief  Mask for Ifx_IOM_FPCESR_Bits.FEG0 */
#define IFX_IOM_FPCESR_FEG0_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_FPCESR_Bits.FEG0 */
#define IFX_IOM_FPCESR_FEG0_OFF (0u)

/** \brief  Length for Ifx_IOM_FPCESR_Bits.FEG10 */
#define IFX_IOM_FPCESR_FEG10_LEN (1u)

/** \brief  Mask for Ifx_IOM_FPCESR_Bits.FEG10 */
#define IFX_IOM_FPCESR_FEG10_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_FPCESR_Bits.FEG10 */
#define IFX_IOM_FPCESR_FEG10_OFF (10u)

/** \brief  Length for Ifx_IOM_FPCESR_Bits.FEG11 */
#define IFX_IOM_FPCESR_FEG11_LEN (1u)

/** \brief  Mask for Ifx_IOM_FPCESR_Bits.FEG11 */
#define IFX_IOM_FPCESR_FEG11_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_FPCESR_Bits.FEG11 */
#define IFX_IOM_FPCESR_FEG11_OFF (11u)

/** \brief  Length for Ifx_IOM_FPCESR_Bits.FEG12 */
#define IFX_IOM_FPCESR_FEG12_LEN (1u)

/** \brief  Mask for Ifx_IOM_FPCESR_Bits.FEG12 */
#define IFX_IOM_FPCESR_FEG12_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_FPCESR_Bits.FEG12 */
#define IFX_IOM_FPCESR_FEG12_OFF (12u)

/** \brief  Length for Ifx_IOM_FPCESR_Bits.FEG13 */
#define IFX_IOM_FPCESR_FEG13_LEN (1u)

/** \brief  Mask for Ifx_IOM_FPCESR_Bits.FEG13 */
#define IFX_IOM_FPCESR_FEG13_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_FPCESR_Bits.FEG13 */
#define IFX_IOM_FPCESR_FEG13_OFF (13u)

/** \brief  Length for Ifx_IOM_FPCESR_Bits.FEG14 */
#define IFX_IOM_FPCESR_FEG14_LEN (1u)

/** \brief  Mask for Ifx_IOM_FPCESR_Bits.FEG14 */
#define IFX_IOM_FPCESR_FEG14_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_FPCESR_Bits.FEG14 */
#define IFX_IOM_FPCESR_FEG14_OFF (14u)

/** \brief  Length for Ifx_IOM_FPCESR_Bits.FEG15 */
#define IFX_IOM_FPCESR_FEG15_LEN (1u)

/** \brief  Mask for Ifx_IOM_FPCESR_Bits.FEG15 */
#define IFX_IOM_FPCESR_FEG15_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_FPCESR_Bits.FEG15 */
#define IFX_IOM_FPCESR_FEG15_OFF (15u)

/** \brief  Length for Ifx_IOM_FPCESR_Bits.FEG1 */
#define IFX_IOM_FPCESR_FEG1_LEN (1u)

/** \brief  Mask for Ifx_IOM_FPCESR_Bits.FEG1 */
#define IFX_IOM_FPCESR_FEG1_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_FPCESR_Bits.FEG1 */
#define IFX_IOM_FPCESR_FEG1_OFF (1u)

/** \brief  Length for Ifx_IOM_FPCESR_Bits.FEG2 */
#define IFX_IOM_FPCESR_FEG2_LEN (1u)

/** \brief  Mask for Ifx_IOM_FPCESR_Bits.FEG2 */
#define IFX_IOM_FPCESR_FEG2_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_FPCESR_Bits.FEG2 */
#define IFX_IOM_FPCESR_FEG2_OFF (2u)

/** \brief  Length for Ifx_IOM_FPCESR_Bits.FEG3 */
#define IFX_IOM_FPCESR_FEG3_LEN (1u)

/** \brief  Mask for Ifx_IOM_FPCESR_Bits.FEG3 */
#define IFX_IOM_FPCESR_FEG3_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_FPCESR_Bits.FEG3 */
#define IFX_IOM_FPCESR_FEG3_OFF (3u)

/** \brief  Length for Ifx_IOM_FPCESR_Bits.FEG4 */
#define IFX_IOM_FPCESR_FEG4_LEN (1u)

/** \brief  Mask for Ifx_IOM_FPCESR_Bits.FEG4 */
#define IFX_IOM_FPCESR_FEG4_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_FPCESR_Bits.FEG4 */
#define IFX_IOM_FPCESR_FEG4_OFF (4u)

/** \brief  Length for Ifx_IOM_FPCESR_Bits.FEG5 */
#define IFX_IOM_FPCESR_FEG5_LEN (1u)

/** \brief  Mask for Ifx_IOM_FPCESR_Bits.FEG5 */
#define IFX_IOM_FPCESR_FEG5_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_FPCESR_Bits.FEG5 */
#define IFX_IOM_FPCESR_FEG5_OFF (5u)

/** \brief  Length for Ifx_IOM_FPCESR_Bits.FEG6 */
#define IFX_IOM_FPCESR_FEG6_LEN (1u)

/** \brief  Mask for Ifx_IOM_FPCESR_Bits.FEG6 */
#define IFX_IOM_FPCESR_FEG6_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_FPCESR_Bits.FEG6 */
#define IFX_IOM_FPCESR_FEG6_OFF (6u)

/** \brief  Length for Ifx_IOM_FPCESR_Bits.FEG7 */
#define IFX_IOM_FPCESR_FEG7_LEN (1u)

/** \brief  Mask for Ifx_IOM_FPCESR_Bits.FEG7 */
#define IFX_IOM_FPCESR_FEG7_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_FPCESR_Bits.FEG7 */
#define IFX_IOM_FPCESR_FEG7_OFF (7u)

/** \brief  Length for Ifx_IOM_FPCESR_Bits.FEG8 */
#define IFX_IOM_FPCESR_FEG8_LEN (1u)

/** \brief  Mask for Ifx_IOM_FPCESR_Bits.FEG8 */
#define IFX_IOM_FPCESR_FEG8_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_FPCESR_Bits.FEG8 */
#define IFX_IOM_FPCESR_FEG8_OFF (8u)

/** \brief  Length for Ifx_IOM_FPCESR_Bits.FEG9 */
#define IFX_IOM_FPCESR_FEG9_LEN (1u)

/** \brief  Mask for Ifx_IOM_FPCESR_Bits.FEG9 */
#define IFX_IOM_FPCESR_FEG9_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_FPCESR_Bits.FEG9 */
#define IFX_IOM_FPCESR_FEG9_OFF (9u)

/** \brief  Length for Ifx_IOM_FPCESR_Bits.REG0 */
#define IFX_IOM_FPCESR_REG0_LEN (1u)

/** \brief  Mask for Ifx_IOM_FPCESR_Bits.REG0 */
#define IFX_IOM_FPCESR_REG0_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_FPCESR_Bits.REG0 */
#define IFX_IOM_FPCESR_REG0_OFF (16u)

/** \brief  Length for Ifx_IOM_FPCESR_Bits.REG10 */
#define IFX_IOM_FPCESR_REG10_LEN (1u)

/** \brief  Mask for Ifx_IOM_FPCESR_Bits.REG10 */
#define IFX_IOM_FPCESR_REG10_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_FPCESR_Bits.REG10 */
#define IFX_IOM_FPCESR_REG10_OFF (26u)

/** \brief  Length for Ifx_IOM_FPCESR_Bits.REG11 */
#define IFX_IOM_FPCESR_REG11_LEN (1u)

/** \brief  Mask for Ifx_IOM_FPCESR_Bits.REG11 */
#define IFX_IOM_FPCESR_REG11_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_FPCESR_Bits.REG11 */
#define IFX_IOM_FPCESR_REG11_OFF (27u)

/** \brief  Length for Ifx_IOM_FPCESR_Bits.REG12 */
#define IFX_IOM_FPCESR_REG12_LEN (1u)

/** \brief  Mask for Ifx_IOM_FPCESR_Bits.REG12 */
#define IFX_IOM_FPCESR_REG12_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_FPCESR_Bits.REG12 */
#define IFX_IOM_FPCESR_REG12_OFF (28u)

/** \brief  Length for Ifx_IOM_FPCESR_Bits.REG13 */
#define IFX_IOM_FPCESR_REG13_LEN (1u)

/** \brief  Mask for Ifx_IOM_FPCESR_Bits.REG13 */
#define IFX_IOM_FPCESR_REG13_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_FPCESR_Bits.REG13 */
#define IFX_IOM_FPCESR_REG13_OFF (29u)

/** \brief  Length for Ifx_IOM_FPCESR_Bits.REG14 */
#define IFX_IOM_FPCESR_REG14_LEN (1u)

/** \brief  Mask for Ifx_IOM_FPCESR_Bits.REG14 */
#define IFX_IOM_FPCESR_REG14_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_FPCESR_Bits.REG14 */
#define IFX_IOM_FPCESR_REG14_OFF (30u)

/** \brief  Length for Ifx_IOM_FPCESR_Bits.REG15 */
#define IFX_IOM_FPCESR_REG15_LEN (1u)

/** \brief  Mask for Ifx_IOM_FPCESR_Bits.REG15 */
#define IFX_IOM_FPCESR_REG15_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_FPCESR_Bits.REG15 */
#define IFX_IOM_FPCESR_REG15_OFF (31u)

/** \brief  Length for Ifx_IOM_FPCESR_Bits.REG1 */
#define IFX_IOM_FPCESR_REG1_LEN (1u)

/** \brief  Mask for Ifx_IOM_FPCESR_Bits.REG1 */
#define IFX_IOM_FPCESR_REG1_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_FPCESR_Bits.REG1 */
#define IFX_IOM_FPCESR_REG1_OFF (17u)

/** \brief  Length for Ifx_IOM_FPCESR_Bits.REG2 */
#define IFX_IOM_FPCESR_REG2_LEN (1u)

/** \brief  Mask for Ifx_IOM_FPCESR_Bits.REG2 */
#define IFX_IOM_FPCESR_REG2_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_FPCESR_Bits.REG2 */
#define IFX_IOM_FPCESR_REG2_OFF (18u)

/** \brief  Length for Ifx_IOM_FPCESR_Bits.REG3 */
#define IFX_IOM_FPCESR_REG3_LEN (1u)

/** \brief  Mask for Ifx_IOM_FPCESR_Bits.REG3 */
#define IFX_IOM_FPCESR_REG3_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_FPCESR_Bits.REG3 */
#define IFX_IOM_FPCESR_REG3_OFF (19u)

/** \brief  Length for Ifx_IOM_FPCESR_Bits.REG4 */
#define IFX_IOM_FPCESR_REG4_LEN (1u)

/** \brief  Mask for Ifx_IOM_FPCESR_Bits.REG4 */
#define IFX_IOM_FPCESR_REG4_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_FPCESR_Bits.REG4 */
#define IFX_IOM_FPCESR_REG4_OFF (20u)

/** \brief  Length for Ifx_IOM_FPCESR_Bits.REG5 */
#define IFX_IOM_FPCESR_REG5_LEN (1u)

/** \brief  Mask for Ifx_IOM_FPCESR_Bits.REG5 */
#define IFX_IOM_FPCESR_REG5_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_FPCESR_Bits.REG5 */
#define IFX_IOM_FPCESR_REG5_OFF (21u)

/** \brief  Length for Ifx_IOM_FPCESR_Bits.REG6 */
#define IFX_IOM_FPCESR_REG6_LEN (1u)

/** \brief  Mask for Ifx_IOM_FPCESR_Bits.REG6 */
#define IFX_IOM_FPCESR_REG6_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_FPCESR_Bits.REG6 */
#define IFX_IOM_FPCESR_REG6_OFF (22u)

/** \brief  Length for Ifx_IOM_FPCESR_Bits.REG7 */
#define IFX_IOM_FPCESR_REG7_LEN (1u)

/** \brief  Mask for Ifx_IOM_FPCESR_Bits.REG7 */
#define IFX_IOM_FPCESR_REG7_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_FPCESR_Bits.REG7 */
#define IFX_IOM_FPCESR_REG7_OFF (23u)

/** \brief  Length for Ifx_IOM_FPCESR_Bits.REG8 */
#define IFX_IOM_FPCESR_REG8_LEN (1u)

/** \brief  Mask for Ifx_IOM_FPCESR_Bits.REG8 */
#define IFX_IOM_FPCESR_REG8_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_FPCESR_Bits.REG8 */
#define IFX_IOM_FPCESR_REG8_OFF (24u)

/** \brief  Length for Ifx_IOM_FPCESR_Bits.REG9 */
#define IFX_IOM_FPCESR_REG9_LEN (1u)

/** \brief  Mask for Ifx_IOM_FPCESR_Bits.REG9 */
#define IFX_IOM_FPCESR_REG9_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_FPCESR_Bits.REG9 */
#define IFX_IOM_FPCESR_REG9_OFF (25u)

/** \brief  Length for Ifx_IOM_FPCTIM_Bits.TIM */
#define IFX_IOM_FPCTIM_TIM_LEN (16u)

/** \brief  Mask for Ifx_IOM_FPCTIM_Bits.TIM */
#define IFX_IOM_FPCTIM_TIM_MSK (0xffffu)

/** \brief  Offset for Ifx_IOM_FPCTIM_Bits.TIM */
#define IFX_IOM_FPCTIM_TIM_OFF (0u)

/** \brief  Length for Ifx_IOM_GTMEXR_Bits.EN0 */
#define IFX_IOM_GTMEXR_EN0_LEN (1u)

/** \brief  Mask for Ifx_IOM_GTMEXR_Bits.EN0 */
#define IFX_IOM_GTMEXR_EN0_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_GTMEXR_Bits.EN0 */
#define IFX_IOM_GTMEXR_EN0_OFF (0u)

/** \brief  Length for Ifx_IOM_GTMEXR_Bits.EN1 */
#define IFX_IOM_GTMEXR_EN1_LEN (1u)

/** \brief  Mask for Ifx_IOM_GTMEXR_Bits.EN1 */
#define IFX_IOM_GTMEXR_EN1_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_GTMEXR_Bits.EN1 */
#define IFX_IOM_GTMEXR_EN1_OFF (1u)

/** \brief  Length for Ifx_IOM_GTMEXR_Bits.EN2 */
#define IFX_IOM_GTMEXR_EN2_LEN (1u)

/** \brief  Mask for Ifx_IOM_GTMEXR_Bits.EN2 */
#define IFX_IOM_GTMEXR_EN2_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_GTMEXR_Bits.EN2 */
#define IFX_IOM_GTMEXR_EN2_OFF (2u)

/** \brief  Length for Ifx_IOM_GTMEXR_Bits.EN3 */
#define IFX_IOM_GTMEXR_EN3_LEN (1u)

/** \brief  Mask for Ifx_IOM_GTMEXR_Bits.EN3 */
#define IFX_IOM_GTMEXR_EN3_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_GTMEXR_Bits.EN3 */
#define IFX_IOM_GTMEXR_EN3_OFF (3u)

/** \brief  Length for Ifx_IOM_GTMEXR_Bits.EN4 */
#define IFX_IOM_GTMEXR_EN4_LEN (1u)

/** \brief  Mask for Ifx_IOM_GTMEXR_Bits.EN4 */
#define IFX_IOM_GTMEXR_EN4_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_GTMEXR_Bits.EN4 */
#define IFX_IOM_GTMEXR_EN4_OFF (4u)

/** \brief  Length for Ifx_IOM_GTMEXR_Bits.EN5 */
#define IFX_IOM_GTMEXR_EN5_LEN (1u)

/** \brief  Mask for Ifx_IOM_GTMEXR_Bits.EN5 */
#define IFX_IOM_GTMEXR_EN5_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_GTMEXR_Bits.EN5 */
#define IFX_IOM_GTMEXR_EN5_OFF (5u)

/** \brief  Length for Ifx_IOM_GTMEXR_Bits.EN6 */
#define IFX_IOM_GTMEXR_EN6_LEN (1u)

/** \brief  Mask for Ifx_IOM_GTMEXR_Bits.EN6 */
#define IFX_IOM_GTMEXR_EN6_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_GTMEXR_Bits.EN6 */
#define IFX_IOM_GTMEXR_EN6_OFF (6u)

/** \brief  Length for Ifx_IOM_GTMEXR_Bits.EN7 */
#define IFX_IOM_GTMEXR_EN7_LEN (1u)

/** \brief  Mask for Ifx_IOM_GTMEXR_Bits.EN7 */
#define IFX_IOM_GTMEXR_EN7_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_GTMEXR_Bits.EN7 */
#define IFX_IOM_GTMEXR_EN7_OFF (7u)

/** \brief  Length for Ifx_IOM_ID_Bits.MODNUMBER */
#define IFX_IOM_ID_MODNUMBER_LEN (16u)

/** \brief  Mask for Ifx_IOM_ID_Bits.MODNUMBER */
#define IFX_IOM_ID_MODNUMBER_MSK (0xffffu)

/** \brief  Offset for Ifx_IOM_ID_Bits.MODNUMBER */
#define IFX_IOM_ID_MODNUMBER_OFF (16u)

/** \brief  Length for Ifx_IOM_ID_Bits.MODREV */
#define IFX_IOM_ID_MODREV_LEN (8u)

/** \brief  Mask for Ifx_IOM_ID_Bits.MODREV */
#define IFX_IOM_ID_MODREV_MSK (0xffu)

/** \brief  Offset for Ifx_IOM_ID_Bits.MODREV */
#define IFX_IOM_ID_MODREV_OFF (0u)

/** \brief  Length for Ifx_IOM_ID_Bits.MODTYPE */
#define IFX_IOM_ID_MODTYPE_LEN (8u)

/** \brief  Mask for Ifx_IOM_ID_Bits.MODTYPE */
#define IFX_IOM_ID_MODTYPE_MSK (0xffu)

/** \brief  Offset for Ifx_IOM_ID_Bits.MODTYPE */
#define IFX_IOM_ID_MODTYPE_OFF (8u)

/** \brief  Length for Ifx_IOM_KRST0_Bits.RST */
#define IFX_IOM_KRST0_RST_LEN (1u)

/** \brief  Mask for Ifx_IOM_KRST0_Bits.RST */
#define IFX_IOM_KRST0_RST_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_KRST0_Bits.RST */
#define IFX_IOM_KRST0_RST_OFF (0u)

/** \brief  Length for Ifx_IOM_KRST0_Bits.RSTSTAT */
#define IFX_IOM_KRST0_RSTSTAT_LEN (1u)

/** \brief  Mask for Ifx_IOM_KRST0_Bits.RSTSTAT */
#define IFX_IOM_KRST0_RSTSTAT_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_KRST0_Bits.RSTSTAT */
#define IFX_IOM_KRST0_RSTSTAT_OFF (1u)

/** \brief  Length for Ifx_IOM_KRST1_Bits.RST */
#define IFX_IOM_KRST1_RST_LEN (1u)

/** \brief  Mask for Ifx_IOM_KRST1_Bits.RST */
#define IFX_IOM_KRST1_RST_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_KRST1_Bits.RST */
#define IFX_IOM_KRST1_RST_OFF (0u)

/** \brief  Length for Ifx_IOM_KRSTCLR_Bits.CLR */
#define IFX_IOM_KRSTCLR_CLR_LEN (1u)

/** \brief  Mask for Ifx_IOM_KRSTCLR_Bits.CLR */
#define IFX_IOM_KRSTCLR_CLR_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_KRSTCLR_Bits.CLR */
#define IFX_IOM_KRSTCLR_CLR_OFF (0u)

/** \brief  Length for Ifx_IOM_LAMCFG_Bits.EDS */
#define IFX_IOM_LAMCFG_EDS_LEN (4u)

/** \brief  Mask for Ifx_IOM_LAMCFG_Bits.EDS */
#define IFX_IOM_LAMCFG_EDS_MSK (0xfu)

/** \brief  Offset for Ifx_IOM_LAMCFG_Bits.EDS */
#define IFX_IOM_LAMCFG_EDS_OFF (8u)

/** \brief  Length for Ifx_IOM_LAMCFG_Bits.EWS */
#define IFX_IOM_LAMCFG_EWS_LEN (1u)

/** \brief  Mask for Ifx_IOM_LAMCFG_Bits.EWS */
#define IFX_IOM_LAMCFG_EWS_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_LAMCFG_Bits.EWS */
#define IFX_IOM_LAMCFG_EWS_OFF (4u)

/** \brief  Length for Ifx_IOM_LAMCFG_Bits.IVM */
#define IFX_IOM_LAMCFG_IVM_LEN (1u)

/** \brief  Mask for Ifx_IOM_LAMCFG_Bits.IVM */
#define IFX_IOM_LAMCFG_IVM_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_LAMCFG_Bits.IVM */
#define IFX_IOM_LAMCFG_IVM_OFF (1u)

/** \brief  Length for Ifx_IOM_LAMCFG_Bits.IVR */
#define IFX_IOM_LAMCFG_IVR_LEN (1u)

/** \brief  Mask for Ifx_IOM_LAMCFG_Bits.IVR */
#define IFX_IOM_LAMCFG_IVR_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_LAMCFG_Bits.IVR */
#define IFX_IOM_LAMCFG_IVR_OFF (0u)

/** \brief  Length for Ifx_IOM_LAMCFG_Bits.IVW */
#define IFX_IOM_LAMCFG_IVW_LEN (1u)

/** \brief  Mask for Ifx_IOM_LAMCFG_Bits.IVW */
#define IFX_IOM_LAMCFG_IVW_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_LAMCFG_Bits.IVW */
#define IFX_IOM_LAMCFG_IVW_OFF (12u)

/** \brief  Length for Ifx_IOM_LAMCFG_Bits.MCS */
#define IFX_IOM_LAMCFG_MCS_LEN (4u)

/** \brief  Mask for Ifx_IOM_LAMCFG_Bits.MCS */
#define IFX_IOM_LAMCFG_MCS_MSK (0xfu)

/** \brief  Offset for Ifx_IOM_LAMCFG_Bits.MCS */
#define IFX_IOM_LAMCFG_MCS_OFF (16u)

/** \brief  Length for Ifx_IOM_LAMCFG_Bits.MOS */
#define IFX_IOM_LAMCFG_MOS_LEN (1u)

/** \brief  Mask for Ifx_IOM_LAMCFG_Bits.MOS */
#define IFX_IOM_LAMCFG_MOS_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_LAMCFG_Bits.MOS */
#define IFX_IOM_LAMCFG_MOS_OFF (2u)

/** \brief  Length for Ifx_IOM_LAMCFG_Bits.RCS */
#define IFX_IOM_LAMCFG_RCS_LEN (4u)

/** \brief  Mask for Ifx_IOM_LAMCFG_Bits.RCS */
#define IFX_IOM_LAMCFG_RCS_MSK (0xfu)

/** \brief  Offset for Ifx_IOM_LAMCFG_Bits.RCS */
#define IFX_IOM_LAMCFG_RCS_OFF (20u)

/** \brief  Length for Ifx_IOM_LAMCFG_Bits.RMS */
#define IFX_IOM_LAMCFG_RMS_LEN (1u)

/** \brief  Mask for Ifx_IOM_LAMCFG_Bits.RMS */
#define IFX_IOM_LAMCFG_RMS_MSK (0x1u)

/** \brief  Offset for Ifx_IOM_LAMCFG_Bits.RMS */
#define IFX_IOM_LAMCFG_RMS_OFF (3u)

/** \brief  Length for Ifx_IOM_LAMEWC_Bits.CNT */
#define IFX_IOM_LAMEWC_CNT_LEN (24u)

/** \brief  Mask for Ifx_IOM_LAMEWC_Bits.CNT */
#define IFX_IOM_LAMEWC_CNT_MSK (0xffffffu)

/** \brief  Offset for Ifx_IOM_LAMEWC_Bits.CNT */
#define IFX_IOM_LAMEWC_CNT_OFF (0u)

/** \brief  Length for Ifx_IOM_LAMEWS_Bits.THR */
#define IFX_IOM_LAMEWS_THR_LEN (24u)

/** \brief  Mask for Ifx_IOM_LAMEWS_Bits.THR */
#define IFX_IOM_LAMEWS_THR_MSK (0xffffffu)

/** \brief  Offset for Ifx_IOM_LAMEWS_Bits.THR */
#define IFX_IOM_LAMEWS_THR_OFF (0u)
/** \}  */
/******************************************************************************/
/******************************************************************************/
#endif /* IFXIOM_BF_H */
