	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc20120a --dep-file=zf_device_wireless_uart.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2\\\\xf_asr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/zf_device/zf_device_wireless_uart.src ../libraries/zf_device/zf_device_wireless_uart.c"
	.compiler_name		"ctc"
	;source	'../libraries/zf_device/zf_device_wireless_uart.c'

	
$TC16X
	
	.sdecl	'.text.zf_device_wireless_uart.wireless_uart_send_byte',code,cluster('wireless_uart_send_byte')
	.sect	'.text.zf_device_wireless_uart.wireless_uart_send_byte'
	.align	2
	
	.global	wireless_uart_send_byte
; Function wireless_uart_send_byte
.L39:
wireless_uart_send_byte:	.type	func
	mov	d8,d4
.L142:
	mov	d15,#100
.L143:
	j	.L2
.L3:
	mov	d4,#322
	call	gpio_get_level
.L199:
	jne	d2,#0,.L4
.L200:
	mov	d4,#2
.L201:
	mov	d5,d8
.L144:
	call	uart_write_byte
.L145:
	j	.L5
.L4:
	add	d15,#-1
.L202:
	mov	d4,#1
	call	system_delay_ms
.L2:
	jne	d15,#0,.L3
.L5:
	eq	d2,d15,#0
.L203:
	j	.L6
.L6:
	ret
.L102:
	
__wireless_uart_send_byte_function_end:
	.size	wireless_uart_send_byte,__wireless_uart_send_byte_function_end-wireless_uart_send_byte
.L60:
	; End of function
	
	.sdecl	'.text.zf_device_wireless_uart.wireless_uart_send_buffer',code,cluster('wireless_uart_send_buffer')
	.sect	'.text.zf_device_wireless_uart.wireless_uart_send_buffer'
	.align	2
	
	.global	wireless_uart_send_buffer
; Function wireless_uart_send_buffer
.L41:
wireless_uart_send_buffer:	.type	func
	mov.aa	a15,a4
.L148:
	mov	d8,d4
.L149:
	mov.a	a2,#0
	ne.a	d4,a2,a15
.L147:
	movh.a	a4,#@his(.1.str)
.L146:
	lea	a4,[a4]@los(.1.str)
	mov	d5,#98
	call	debug_assert_handler
.L111:
	mov	d9,#0
.L150:
	j	.L7
.L8:
	mov	d4,#322
	call	gpio_get_level
.L208:
	jne	d2,#0,.L9
.L209:
	mov	d15,#30
.L210:
	jlt.u	d8,d15,.L10
.L211:
	mov	d4,#2
.L212:
	mov	d5,#30
	mov.aa	a4,a15
.L151:
	call	uart_write_buffer
.L152:
	lea	a15,[a15]30
.L213:
	add	d8,d8,#-30
.L214:
	mov	d9,#0
.L215:
	j	.L11
.L10:
	mov	d4,#2
.L216:
	mov.aa	a4,a15
.L153:
	mov	d5,d8
.L155:
	call	uart_write_buffer
.L154:
	mov	d8,#0
.L217:
	j	.L12
.L11:
	j	.L13
.L9:
	add	d9,#1
.L218:
	mov	d15,#100
.L219:
	jlt.u	d9,d15,.L14
.L220:
	j	.L15
.L14:
	mov	d4,#1
	call	system_delay_ms
.L13:
.L7:
	jne	d8,#0,.L8
.L15:
.L12:
	mov	d2,d8
.L156:
	j	.L16
.L16:
	ret
.L107:
	
__wireless_uart_send_buffer_function_end:
	.size	wireless_uart_send_buffer,__wireless_uart_send_buffer_function_end-wireless_uart_send_buffer
.L65:
	; End of function
	
	.sdecl	'.text.zf_device_wireless_uart.wireless_uart_send_string',code,cluster('wireless_uart_send_string')
	.sect	'.text.zf_device_wireless_uart.wireless_uart_send_string'
	.align	2
	
	.global	wireless_uart_send_string
; Function wireless_uart_send_string
.L43:
wireless_uart_send_string:	.type	func
	mov.aa	a15,a4
.L158:
	mov.a	a2,#0
	ne.a	d4,a2,a15
	movh.a	a4,#@his(.1.str)
.L157:
	lea	a4,[a4]@los(.1.str)
	mov	d5,#139
	call	debug_assert_handler
.L116:
	mov	d8,#0
.L159:
	mov.aa	a4,a15
.L160:
	call	strlen
.L161:
	mov	d9,d2
.L162:
	j	.L17
.L18:
	mov	d4,#322
	call	gpio_get_level
.L225:
	jne	d2,#0,.L19
.L226:
	mov	d15,#30
.L227:
	jlt.u	d9,d15,.L20
.L228:
	mov	d4,#2
.L229:
	mov	d5,#30
	mov.aa	a4,a15
.L163:
	call	uart_write_buffer
.L164:
	lea	a15,[a15]30
.L230:
	add	d9,d9,#-30
.L231:
	mov	d8,#0
.L232:
	j	.L21
.L20:
	mov	d4,#2
.L233:
	mov.aa	a4,a15
.L165:
	mov	d5,d9
.L167:
	call	uart_write_buffer
.L166:
	mov	d9,#0
.L234:
	j	.L22
.L21:
	j	.L23
.L19:
	add	d8,#1
.L235:
	mov	d15,#100
.L236:
	jlt.u	d8,d15,.L24
.L237:
	j	.L25
.L24:
	mov	d4,#1
	call	system_delay_ms
.L23:
.L17:
	jne	d9,#0,.L18
.L25:
.L22:
	mov	d2,d9
.L168:
	j	.L26
.L26:
	ret
.L113:
	
__wireless_uart_send_string_function_end:
	.size	wireless_uart_send_string,__wireless_uart_send_string_function_end-wireless_uart_send_string
.L70:
	; End of function
	
	.sdecl	'.text.zf_device_wireless_uart.wireless_uart_send_image',code,cluster('wireless_uart_send_image')
	.sect	'.text.zf_device_wireless_uart.wireless_uart_send_image'
	.align	2
	
	.global	wireless_uart_send_image
; Function wireless_uart_send_image
.L45:
wireless_uart_send_image:	.type	func
	mov.aa	a15,a4
.L171:
	mov	d15,d4
.L172:
	mov.a	a2,#0
	ne.a	d4,a2,a15
.L170:
	movh.a	a4,#@his(.1.str)
.L169:
	lea	a4,[a4]@los(.1.str)
	mov	d5,#182
	call	debug_assert_handler
.L122:
	movh.a	a4,#@his(camera_send_image_frame_header)
	lea	a4,[a4]@los(camera_send_image_frame_header)
.L242:
	mov	d4,#4
	call	wireless_uart_send_buffer
.L243:
	mov.aa	a4,a15
.L173:
	mov	d4,d15
.L175:
	call	wireless_uart_send_buffer
.L174:
	ret
.L119:
	
__wireless_uart_send_image_function_end:
	.size	wireless_uart_send_image,__wireless_uart_send_image_function_end-wireless_uart_send_image
.L75:
	; End of function
	
	.sdecl	'.text.zf_device_wireless_uart.wireless_uart_read_buffer',code,cluster('wireless_uart_read_buffer')
	.sect	'.text.zf_device_wireless_uart.wireless_uart_read_buffer'
	.align	2
	
	.global	wireless_uart_read_buffer
; Function wireless_uart_read_buffer
.L47:
wireless_uart_read_buffer:	.type	func
	sub.a	a10,#8
.L176:
	mov.aa	a15,a4
.L179:
	mov	d15,d4
.L180:
	mov.a	a2,#0
	ne.a	d4,a2,a15
.L178:
	movh.a	a4,#@his(.1.str)
.L177:
	lea	a4,[a4]@los(.1.str)
	mov	d5,#198
	call	debug_assert_handler
.L127:
	st.w	[a10],d15
.L248:
	movh.a	a4,#@his(wireless_uart_fifo)
	lea	a4,[a4]@los(wireless_uart_fifo)
.L249:
	lea	a6,[a10]0
.L250:
	mov	d4,#0
	mov.aa	a5,a15
.L181:
	call	fifo_read_buffer
.L182:
	ld.w	d2,[a10]
.L251:
	j	.L27
.L27:
	ret
.L123:
	
__wireless_uart_read_buffer_function_end:
	.size	wireless_uart_read_buffer,__wireless_uart_read_buffer_function_end-wireless_uart_read_buffer
.L80:
	; End of function
	
	.sdecl	'.text.zf_device_wireless_uart.wireless_uart_callback',code,cluster('wireless_uart_callback')
	.sect	'.text.zf_device_wireless_uart.wireless_uart_callback'
	.align	2
	
	.global	wireless_uart_callback
; Function wireless_uart_callback
.L49:
wireless_uart_callback:	.type	func
	sub.a	a10,#8
.L183:
	mov	d4,#2
.L256:
	movh.a	a4,#@his(wireless_uart_data)
	lea	a4,[a4]@los(wireless_uart_data)
	call	uart_query_byte
.L257:
	movh.a	a4,#@his(wireless_uart_fifo)
	lea	a4,[a4]@los(wireless_uart_fifo)
.L258:
	movh.a	a5,#@his(wireless_uart_data)
	lea	a5,[a5]@los(wireless_uart_data)
.L259:
	mov	d4,#1
	call	fifo_write_buffer
.L260:
	movh.a	a15,#@his(wireless_auto_baud_flag)
	lea	a15,[a15]@los(wireless_auto_baud_flag)
	ld.bu	d15,[a15]
.L261:
	jne	d15,#2,.L28
.L262:
	movh.a	a4,#@his(wireless_uart_fifo)
	lea	a4,[a4]@los(wireless_uart_fifo)
	call	fifo_used
.L263:
	jne	d2,#3,.L29
.L130:
	mov	d15,#3
.L264:
	st.w	[a10],d15
.L265:
	movh.a	a15,#@his(wireless_auto_baud_flag)
	lea	a15,[a15]@los(wireless_auto_baud_flag)
.L266:
	mov	d15,#3
.L267:
	st.b	[a15],d15
.L268:
	movh.a	a4,#@his(wireless_uart_fifo)
	lea	a4,[a4]@los(wireless_uart_fifo)
.L269:
	movh.a	a5,#@his(wireless_auto_baud_data)
	lea	a5,[a5]@los(wireless_auto_baud_data)
.L270:
	lea	a6,[a10]0
.L271:
	mov	d4,#0
	call	fifo_read_buffer
.L29:
.L28:
	ret
.L129:
	
__wireless_uart_callback_function_end:
	.size	wireless_uart_callback,__wireless_uart_callback_function_end-wireless_uart_callback
.L85:
	; End of function
	
	.sdecl	'.text.zf_device_wireless_uart.wireless_uart_init',code,cluster('wireless_uart_init')
	.sect	'.text.zf_device_wireless_uart.wireless_uart_init'
	.align	2
	
	.global	wireless_uart_init
; Function wireless_uart_init
.L51:
wireless_uart_init:	.type	func
	mov	d8,#0
.L184:
	mov	d4,#1
.L276:
	movh.a	a4,#@his(wireless_uart_callback)
	lea	a4,[a4]@los(wireless_uart_callback)
	call	set_wireless_type
.L277:
	movh.a	a4,#@his(wireless_uart_fifo)
	lea	a4,[a4]@los(wireless_uart_fifo)
.L278:
	mov	d4,#0
.L279:
	movh.a	a5,#@his(wireless_uart_buffer)
	lea	a5,[a5]@los(wireless_uart_buffer)
.L280:
	mov	d5,#64
	call	fifo_init
.L281:
	mov	d4,#322
.L282:
	mov	d5,#0
.L283:
	mov	d6,#1
.L284:
	mov	d7,#1
	call	gpio_init
.L135:
	movh.a	a15,#@his(wireless_auto_baud_flag)
	lea	a15,[a15]@los(wireless_auto_baud_flag)
.L285:
	mov	d15,#1
.L286:
	st.b	[a15],d15
.L287:
	movh.a	a15,#@his(wireless_auto_baud_data)
	lea	a15,[a15]@los(wireless_auto_baud_data)
.L288:
	mov	d15,#0
.L289:
	st.b	[a15],d15
.L290:
	movh.a	a15,#@his(wireless_auto_baud_data)
	lea	a15,[a15]@los(wireless_auto_baud_data)
.L291:
	mov	d15,#1
.L292:
	st.b	[a15]1,d15
.L293:
	movh.a	a15,#@his(wireless_auto_baud_data)
	lea	a15,[a15]@los(wireless_auto_baud_data)
.L294:
	mov	d15,#3
.L295:
	st.b	[a15]2,d15
.L296:
	mov	d4,#322
	call	gpio_get_level
.L185:
	mov	d15,d2
.L187:
	mov	d4,#322
.L297:
	mov	d5,#1
.L298:
	mov	d7,#3
	mov	d6,d15
.L186:
	call	gpio_init
.L189:
	mov	d4,#2
.L299:
	mov.u	d5,#49664
	addih	d5,d5,#1
.L300:
	mov	d6,#14
.L301:
	mov	d7,#10
	call	uart_init
.L302:
	mov	d4,#2
.L303:
	mov	d5,#1
	call	uart_rx_interrupt
.L304:
	mov	d4,#5
	call	system_delay_ms
.L305:
	mov	d4,#322
.L306:
	eq	d5,d15,#0
	call	gpio_set_level
.L307:
	mov	d4,#100
	call	system_delay_ms
.L308:
	mov	d4,#322
	call	gpio_toggle_level
.L30:
	movh.a	a15,#@his(wireless_auto_baud_flag)
	lea	a15,[a15]@los(wireless_auto_baud_flag)
.L309:
	mov	d15,#2
.L188:
	st.b	[a15],d15
.L310:
	movh.a	a15,#@his(wireless_auto_baud_data)
	lea	a15,[a15]@los(wireless_auto_baud_data)
.L311:
	ld.bu	d5,[a15]
.L312:
	mov	d4,#2
.L313:
	call	uart_write_byte
.L314:
	movh.a	a15,#@his(wireless_auto_baud_data)
	lea	a15,[a15]@los(wireless_auto_baud_data)
.L315:
	ld.bu	d5,[a15]1
.L316:
	mov	d4,#2
.L317:
	call	uart_write_byte
.L318:
	movh.a	a15,#@his(wireless_auto_baud_data)
	lea	a15,[a15]@los(wireless_auto_baud_data)
.L319:
	ld.bu	d5,[a15]2
.L320:
	mov	d4,#2
.L321:
	call	uart_write_byte
.L322:
	mov	d4,#20
	call	system_delay_ms
.L323:
	movh.a	a15,#@his(wireless_auto_baud_flag)
	lea	a15,[a15]@los(wireless_auto_baud_flag)
	ld.bu	d15,[a15]
.L324:
	jeq	d15,#3,.L31
.L325:
	mov	d8,#1
.L326:
	j	.L32
.L31:
	movh.a	a15,#@his(wireless_auto_baud_data)
	lea	a15,[a15]@los(wireless_auto_baud_data)
.L327:
	ld.bu	d0,[a15]
.L328:
	mov	d15,#165
	jeq	d15,d0,.L33
.L329:
	movh.a	a15,#@his(wireless_auto_baud_data)
	lea	a15,[a15]@los(wireless_auto_baud_data)
.L330:
	ld.bu	d15,[a15]1
.L331:
	mov	d0,#255
.L332:
	jeq	d15,d0,.L34
.L333:
	movh.a	a15,#@his(wireless_auto_baud_data)
	lea	a15,[a15]@los(wireless_auto_baud_data)
.L334:
	ld.bu	d15,[a15]2
.L335:
	mov	d0,#255
.L336:
	jeq	d15,d0,.L35
.L337:
	mov	d8,#1
.L338:
	j	.L36
.L35:
.L34:
.L33:
	movh.a	a15,#@his(wireless_auto_baud_flag)
	lea	a15,[a15]@los(wireless_auto_baud_flag)
.L339:
	mov	d15,#0
.L340:
	st.b	[a15],d15
.L341:
	mov	d4,#322
.L342:
	mov	d5,#0
.L343:
	mov	d6,#0
.L344:
	mov	d7,#1
	call	gpio_init
.L345:
	mov	d4,#10
	call	system_delay_ms
.L36:
.L32:
	mov	d2,d8
.L190:
	j	.L37
.L37:
	ret
.L133:
	
__wireless_uart_init_function_end:
	.size	wireless_uart_init,__wireless_uart_init_function_end-wireless_uart_init
.L90:
	; End of function
	
	.sdecl	'.bss.zf_device_wireless_uart.wireless_uart_fifo',data,cluster('wireless_uart_fifo')
	.sect	'.bss.zf_device_wireless_uart.wireless_uart_fifo'
	.align	4
wireless_uart_fifo:	.type	object
	.size	wireless_uart_fifo,24
	.space	24
	.sdecl	'.bss.zf_device_wireless_uart.wireless_uart_buffer',data,cluster('wireless_uart_buffer')
	.sect	'.bss.zf_device_wireless_uart.wireless_uart_buffer'
wireless_uart_buffer:	.type	object
	.size	wireless_uart_buffer,64
	.space	64
	.sdecl	'.data.zf_device_wireless_uart.wireless_uart_data',data,cluster('wireless_uart_data')
	.sect	'.data.zf_device_wireless_uart.wireless_uart_data'
wireless_uart_data:	.type	object
	.size	wireless_uart_data,1
	.space	1
	.sdecl	'.data.zf_device_wireless_uart.wireless_auto_baud_flag',data,cluster('wireless_auto_baud_flag')
	.sect	'.data.zf_device_wireless_uart.wireless_auto_baud_flag'
wireless_auto_baud_flag:	.type	object
	.size	wireless_auto_baud_flag,1
	.byte	1
	.sdecl	'.data.zf_device_wireless_uart.wireless_auto_baud_data',data,cluster('wireless_auto_baud_data')
	.sect	'.data.zf_device_wireless_uart.wireless_auto_baud_data'
wireless_auto_baud_data:	.type	object
	.size	wireless_auto_baud_data,3
	.space	1
	.byte	1,3
	.sdecl	'.rodata.zf_device_wireless_uart..1.str',data,rom
	.sect	'.rodata.zf_device_wireless_uart..1.str'
.1.str:	.type	object
	.size	.1.str,49
	.byte	46,46,47,108,105,98,114,97
	.byte	114,105,101,115,47,122,102,95
	.byte	100,101,118,105,99,101,47,122
	.byte	102,95,100,101,118,105,99,101
	.byte	95,119,105,114,101,108,101,115
	.byte	115,95,117,97,114,116,46,99
	.space	1
	.calls	'__INDIRECT__','wireless_uart_callback'
	.calls	'wireless_uart_send_byte','gpio_get_level'
	.calls	'wireless_uart_send_byte','uart_write_byte'
	.calls	'wireless_uart_send_byte','system_delay_ms'
	.calls	'wireless_uart_send_buffer','debug_assert_handler'
	.calls	'wireless_uart_send_buffer','gpio_get_level'
	.calls	'wireless_uart_send_buffer','uart_write_buffer'
	.calls	'wireless_uart_send_buffer','system_delay_ms'
	.calls	'wireless_uart_send_string','debug_assert_handler'
	.calls	'wireless_uart_send_string','strlen'
	.calls	'wireless_uart_send_string','gpio_get_level'
	.calls	'wireless_uart_send_string','uart_write_buffer'
	.calls	'wireless_uart_send_string','system_delay_ms'
	.calls	'wireless_uart_send_image','debug_assert_handler'
	.calls	'wireless_uart_send_image','wireless_uart_send_buffer'
	.calls	'wireless_uart_read_buffer','debug_assert_handler'
	.calls	'wireless_uart_read_buffer','fifo_read_buffer'
	.calls	'wireless_uart_callback','uart_query_byte'
	.calls	'wireless_uart_callback','fifo_write_buffer'
	.calls	'wireless_uart_callback','fifo_used'
	.calls	'wireless_uart_callback','fifo_read_buffer'
	.calls	'wireless_uart_init','set_wireless_type'
	.calls	'wireless_uart_init','fifo_init'
	.calls	'wireless_uart_init','gpio_init'
	.calls	'wireless_uart_init','gpio_get_level'
	.calls	'wireless_uart_init','uart_init'
	.calls	'wireless_uart_init','uart_rx_interrupt'
	.calls	'wireless_uart_init','system_delay_ms'
	.calls	'wireless_uart_init','gpio_set_level'
	.calls	'wireless_uart_init','gpio_toggle_level'
	.calls	'wireless_uart_init','uart_write_byte'
	.calls	'wireless_uart_send_byte','',0
	.calls	'wireless_uart_send_buffer','',0
	.calls	'wireless_uart_send_string','',0
	.calls	'wireless_uart_send_image','',0
	.calls	'wireless_uart_read_buffer','',8
	.calls	'wireless_uart_callback','',8
	.extern	strlen
	.extern	debug_assert_handler
	.extern	fifo_used
	.extern	fifo_write_buffer
	.extern	fifo_read_buffer
	.extern	fifo_init
	.extern	system_delay_ms
	.extern	gpio_set_level
	.extern	gpio_get_level
	.extern	gpio_toggle_level
	.extern	gpio_init
	.extern	uart_write_byte
	.extern	uart_write_buffer
	.extern	uart_query_byte
	.extern	uart_rx_interrupt
	.extern	uart_init
	.extern	set_wireless_type
	.extern	camera_send_image_frame_header
	.extern	__INDIRECT__
	.calls	'wireless_uart_init','',0
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L53:
	.word	104815
	.half	3
	.word	.L54
	.byte	4
.L52:
	.byte	1
	.byte	'../libraries/zf_device/zf_device_wireless_uart.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L55
	.byte	2,1,1,3
	.word	211
	.byte	4
	.byte	'Ifx__jump_and_link',0,3,1,61,17,1,1,5
	.byte	'fun',0,1,61,43
	.word	214
	.byte	6,0,7
	.byte	'__fract',0,4,128,1,7
	.byte	'float',0,4,4,8
	.byte	'Ifx__float_to_fract',0,3,1,152,2,18
	.word	259
	.byte	1,1,5
	.byte	'a',0,1,152,2,44
	.word	271
	.byte	6,0,4
	.byte	'Ifx__stopPerfCounters',0,3,1,172,2,17,1,1,6,0,7
	.byte	'unsigned long long int',0,8,7,9
	.byte	'void',0,3
	.word	383
	.byte	8
	.byte	'__ld64',0,3,2,135,1,19
	.word	357
	.byte	1,1,5
	.byte	'addr',0,2,135,1,32
	.word	389
	.byte	6,0,4
	.byte	'__st64',0,3,2,143,1,17,1,1,5
	.byte	'addr',0,2,143,1,30
	.word	389
	.byte	5
	.byte	'value',0,2,143,1,43
	.word	357
	.byte	6,0,7
	.byte	'unsigned int',0,4,7,7
	.byte	'int',0,4,5,7
	.byte	'unsigned int',0,4,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON0_Bits',0,4,241,8,16,4,11
	.byte	'ENDINIT',0,4
	.word	498
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	498
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	498
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	498
	.byte	16,0,2,35,0,0,12,4,247,14,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	514
	.byte	4,2,35,0,0
.L132:
	.byte	7
	.byte	'unsigned char',0,1,8
.L105:
	.byte	7
	.byte	'unsigned short int',0,2,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON1_Bits',0,4,250,8,16,4,11
	.byte	'reserved_0',0,1
	.word	650
	.byte	2,6,2,35,0,11
	.byte	'IR0',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	650
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	650
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	650
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	650
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	650
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	650
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	667
	.byte	16,0,2,35,2,0,12,4,255,14,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	689
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_WDTCPU_SR_Bits',0,4,137,9,16,4,11
	.byte	'AE',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	650
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	650
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	650
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	650
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	650
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	650
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	667
	.byte	16,0,2,35,2,0,12,4,135,15,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	933
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_WDTCPU',0,4,175,15,25,12,13
	.byte	'CON0',0
	.word	610
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	893
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	1124
	.byte	4,2,35,8,0,14
	.word	1164
	.byte	3
	.word	1227
	.byte	4
	.byte	'IfxScuWdt_clearCpuEndinitInline',0,3,3,181,3,17,1,1,5
	.byte	'watchdog',0,3,181,3,65
	.word	1232
	.byte	5
	.byte	'password',0,3,181,3,82
	.word	667
	.byte	6,0,4
	.byte	'IfxScuWdt_setCpuEndinitInline',0,3,3,140,4,17,1,1,5
	.byte	'watchdog',0,3,140,4,63
	.word	1232
	.byte	5
	.byte	'password',0,3,140,4,80
	.word	667
	.byte	6,0,8
	.byte	'IfxScuWdt_getCpuWatchdogPasswordInline',0,3,3,227,3,19
	.word	667
	.byte	1,1,5
	.byte	'watchdog',0,3,227,3,74
	.word	1232
	.byte	6,0,10
	.byte	'_Ifx_P_OUT_Bits',0,6,143,3,16,4,11
	.byte	'P0',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	650
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	650
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	650
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	650
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	650
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	650
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	650
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	650
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	650
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	650
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	650
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	650
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	667
	.byte	16,0,2,35,2,0,12,6,181,5,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1462
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMR_Bits',0,6,169,2,16,4,11
	.byte	'PS0',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	650
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	650
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	650
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	650
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	650
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	650
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	650
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	650
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	650
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	650
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	650
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	650
	.byte	1,0,2,35,1,11
	.byte	'PCL0',0,1
	.word	650
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	650
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	650
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	650
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	650
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	650
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	650
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	650
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	650
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	650
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	650
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	650
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	650
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	650
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	650
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	650
	.byte	1,0,2,35,3,0,12,6,133,5,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1778
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ID_Bits',0,6,110,16,4,11
	.byte	'MODREV',0,1
	.word	650
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	650
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	667
	.byte	16,0,2,35,2,0,12,6,148,4,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2349
	.byte	4,2,35,0,0,15,4
	.word	650
	.byte	16,3,0,10
	.byte	'_Ifx_P_IOCR0_Bits',0,6,140,1,16,4,11
	.byte	'reserved_0',0,1
	.word	650
	.byte	3,5,2,35,0,11
	.byte	'PC0',0,1
	.word	650
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	650
	.byte	3,5,2,35,1,11
	.byte	'PC1',0,1
	.word	650
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	650
	.byte	3,5,2,35,2,11
	.byte	'PC2',0,1
	.word	650
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	650
	.byte	3,5,2,35,3,11
	.byte	'PC3',0,1
	.word	650
	.byte	5,0,2,35,3,0,12,6,164,4,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2477
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR4_Bits',0,6,166,1,16,4,11
	.byte	'reserved_0',0,1
	.word	650
	.byte	3,5,2,35,0,11
	.byte	'PC4',0,1
	.word	650
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	650
	.byte	3,5,2,35,1,11
	.byte	'PC5',0,1
	.word	650
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	650
	.byte	3,5,2,35,2,11
	.byte	'PC6',0,1
	.word	650
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	650
	.byte	3,5,2,35,3,11
	.byte	'PC7',0,1
	.word	650
	.byte	5,0,2,35,3,0,12,6,180,4,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2692
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR8_Bits',0,6,179,1,16,4,11
	.byte	'reserved_0',0,1
	.word	650
	.byte	3,5,2,35,0,11
	.byte	'PC8',0,1
	.word	650
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	650
	.byte	3,5,2,35,1,11
	.byte	'PC9',0,1
	.word	650
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	650
	.byte	3,5,2,35,2,11
	.byte	'PC10',0,1
	.word	650
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	650
	.byte	3,5,2,35,3,11
	.byte	'PC11',0,1
	.word	650
	.byte	5,0,2,35,3,0,12,6,188,4,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2907
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR12_Bits',0,6,153,1,16,4,11
	.byte	'reserved_0',0,1
	.word	650
	.byte	3,5,2,35,0,11
	.byte	'PC12',0,1
	.word	650
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	650
	.byte	3,5,2,35,1,11
	.byte	'PC13',0,1
	.word	650
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	650
	.byte	3,5,2,35,2,11
	.byte	'PC14',0,1
	.word	650
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	650
	.byte	3,5,2,35,3,11
	.byte	'PC15',0,1
	.word	650
	.byte	5,0,2,35,3,0,12,6,172,4,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3124
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IN_Bits',0,6,118,16,4,11
	.byte	'P0',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	650
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	650
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	650
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	650
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	650
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	650
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	650
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	650
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	650
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	650
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	650
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	650
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	667
	.byte	16,0,2,35,2,0,12,6,156,4,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3344
	.byte	4,2,35,0,0,15,24
	.word	650
	.byte	16,23,0,10
	.byte	'_Ifx_P_PDR0_Bits',0,6,205,3,16,4,11
	.byte	'PD0',0,1
	.word	650
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	650
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	650
	.byte	1,0,2,35,0,11
	.byte	'PD2',0,1
	.word	650
	.byte	3,5,2,35,1,11
	.byte	'PL2',0,1
	.word	650
	.byte	1,4,2,35,1,11
	.byte	'PD3',0,1
	.word	650
	.byte	3,1,2,35,1,11
	.byte	'PL3',0,1
	.word	650
	.byte	1,0,2,35,1,11
	.byte	'PD4',0,1
	.word	650
	.byte	3,5,2,35,2,11
	.byte	'PL4',0,1
	.word	650
	.byte	1,4,2,35,2,11
	.byte	'PD5',0,1
	.word	650
	.byte	3,1,2,35,2,11
	.byte	'PL5',0,1
	.word	650
	.byte	1,0,2,35,2,11
	.byte	'PD6',0,1
	.word	650
	.byte	3,5,2,35,3,11
	.byte	'PL6',0,1
	.word	650
	.byte	1,4,2,35,3,11
	.byte	'PD7',0,1
	.word	650
	.byte	3,1,2,35,3,11
	.byte	'PL7',0,1
	.word	650
	.byte	1,0,2,35,3,0,12,6,205,5,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3667
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PDR1_Bits',0,6,226,3,16,4,11
	.byte	'PD8',0,1
	.word	650
	.byte	3,5,2,35,0,11
	.byte	'PL8',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'PD9',0,1
	.word	650
	.byte	3,1,2,35,0,11
	.byte	'PL9',0,1
	.word	650
	.byte	1,0,2,35,0,11
	.byte	'PD10',0,1
	.word	650
	.byte	3,5,2,35,1,11
	.byte	'PL10',0,1
	.word	650
	.byte	1,4,2,35,1,11
	.byte	'PD11',0,1
	.word	650
	.byte	3,1,2,35,1,11
	.byte	'PL11',0,1
	.word	650
	.byte	1,0,2,35,1,11
	.byte	'PD12',0,1
	.word	650
	.byte	3,5,2,35,2,11
	.byte	'PL12',0,1
	.word	650
	.byte	1,4,2,35,2,11
	.byte	'PD13',0,1
	.word	650
	.byte	3,1,2,35,2,11
	.byte	'PL13',0,1
	.word	650
	.byte	1,0,2,35,2,11
	.byte	'PD14',0,1
	.word	650
	.byte	3,5,2,35,3,11
	.byte	'PL14',0,1
	.word	650
	.byte	1,4,2,35,3,11
	.byte	'PD15',0,1
	.word	650
	.byte	3,1,2,35,3,11
	.byte	'PL15',0,1
	.word	650
	.byte	1,0,2,35,3,0,12,6,213,5,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3971
	.byte	4,2,35,0,0,15,8
	.word	650
	.byte	16,7,0,10
	.byte	'_Ifx_P_ESR_Bits',0,6,88,16,4,11
	.byte	'EN0',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	650
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	650
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	650
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	650
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	650
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	650
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	650
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	650
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	650
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	650
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	650
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	650
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	667
	.byte	16,0,2,35,2,0,12,6,140,4,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4296
	.byte	4,2,35,0,0,15,12
	.word	650
	.byte	16,11,0,10
	.byte	'_Ifx_P_PDISC_Bits',0,6,183,3,16,4,11
	.byte	'PDIS0',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'PDIS2',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'PDIS3',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'PDIS4',0,1
	.word	650
	.byte	1,3,2,35,0,11
	.byte	'PDIS5',0,1
	.word	650
	.byte	1,2,2,35,0,11
	.byte	'PDIS6',0,1
	.word	650
	.byte	1,1,2,35,0,11
	.byte	'PDIS7',0,1
	.word	650
	.byte	1,0,2,35,0,11
	.byte	'PDIS8',0,1
	.word	650
	.byte	1,7,2,35,1,11
	.byte	'PDIS9',0,1
	.word	650
	.byte	1,6,2,35,1,11
	.byte	'PDIS10',0,1
	.word	650
	.byte	1,5,2,35,1,11
	.byte	'PDIS11',0,1
	.word	650
	.byte	1,4,2,35,1,11
	.byte	'PDIS12',0,1
	.word	650
	.byte	1,3,2,35,1,11
	.byte	'PDIS13',0,1
	.word	650
	.byte	1,2,2,35,1,11
	.byte	'PDIS14',0,1
	.word	650
	.byte	1,1,2,35,1,11
	.byte	'PDIS15',0,1
	.word	650
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	667
	.byte	16,0,2,35,2,0,12,6,197,5,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4636
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PCSR_Bits',0,6,165,3,16,4,11
	.byte	'SEL0',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'SEL1',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'SEL2',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'SEL3',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'SEL4',0,1
	.word	650
	.byte	1,3,2,35,0,11
	.byte	'SEL5',0,1
	.word	650
	.byte	1,2,2,35,0,11
	.byte	'SEL6',0,1
	.word	650
	.byte	1,1,2,35,0,11
	.byte	'SEL7',0,1
	.word	650
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	650
	.byte	2,6,2,35,1,11
	.byte	'SEL10',0,1
	.word	650
	.byte	1,5,2,35,1,11
	.byte	'SEL11',0,1
	.word	650
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	475
	.byte	19,1,2,35,0,11
	.byte	'LCK',0,1
	.word	650
	.byte	1,0,2,35,3,0,12,6,189,5,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5002
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR0_Bits',0,6,206,2,16,4,11
	.byte	'PS0',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	475
	.byte	28,0,2,35,0,0,12,6,149,5,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5288
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR4_Bits',0,6,227,2,16,4,11
	.byte	'reserved_0',0,1
	.word	650
	.byte	4,4,2,35,0,11
	.byte	'PS4',0,1
	.word	650
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	650
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	650
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	650
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	475
	.byte	24,0,2,35,0,0,12,6,165,5,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5435
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR8_Bits',0,6,238,2,16,4,11
	.byte	'reserved_0',0,1
	.word	650
	.byte	8,0,2,35,0,11
	.byte	'PS8',0,1
	.word	650
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	650
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	650
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	650
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	475
	.byte	20,0,2,35,0,0,12,6,173,5,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5604
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR12_Bits',0,6,216,2,16,4,11
	.byte	'reserved_0',0,2
	.word	667
	.byte	12,4,2,35,0,11
	.byte	'PS12',0,1
	.word	650
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	650
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	650
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	650
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	667
	.byte	16,0,2,35,2,0,12,6,157,5,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5776
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR0_Bits',0,6,232,1,16,4,11
	.byte	'reserved_0',0,2
	.word	667
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	650
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	650
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	650
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	650
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	667
	.byte	12,0,2,35,2,0,12,6,229,4,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5951
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR4_Bits',0,6,253,1,16,4,11
	.byte	'reserved_0',0,4
	.word	475
	.byte	20,12,2,35,0,11
	.byte	'PCL4',0,1
	.word	650
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	650
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	650
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	650
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	650
	.byte	8,0,2,35,3,0,12,6,245,4,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6125
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR8_Bits',0,6,136,2,16,4,11
	.byte	'reserved_0',0,4
	.word	475
	.byte	24,8,2,35,0,11
	.byte	'PCL8',0,1
	.word	650
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	650
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	650
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	650
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	650
	.byte	4,0,2,35,3,0,12,6,253,4,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6299
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR12_Bits',0,6,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	475
	.byte	28,4,2,35,0,11
	.byte	'PCL12',0,1
	.word	650
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	650
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	650
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	650
	.byte	1,0,2,35,3,0,12,6,237,4,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6475
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR_Bits',0,6,249,2,16,4,11
	.byte	'PS0',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	650
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	650
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	650
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	650
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	650
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	650
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	650
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	650
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	650
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	650
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	650
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	650
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	667
	.byte	16,0,2,35,2,0,12,6,141,5,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6631
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR_Bits',0,6,147,2,16,4,11
	.byte	'reserved_0',0,2
	.word	667
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	650
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	650
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	650
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	650
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	650
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	650
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	650
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	650
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	650
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	650
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	650
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	650
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	650
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	650
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	650
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	650
	.byte	1,0,2,35,3,0,12,6,221,4,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6964
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR0_Bits',0,6,192,1,16,4,11
	.byte	'reserved_0',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	475
	.byte	30,0,2,35,0,0,12,6,196,4,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7312
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_Bits',0,6,200,1,16,4,11
	.byte	'reserved_0',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	475
	.byte	30,0,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_P21_Bits',0,6,208,1,16,4,11
	.byte	'RDIS_CTRL',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'RX_DIS',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'TERM',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'LRXTERM',0,1
	.word	650
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	475
	.byte	24,0,2,35,0,0,12,6,204,4,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7436
	.byte	4,2,35,0,13
	.byte	'B_P21',0
	.word	7520
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR2_Bits',0,6,218,1,16,4,11
	.byte	'reserved_0',0,1
	.word	650
	.byte	8,0,2,35,0,11
	.byte	'LVDSR',0,1
	.word	650
	.byte	1,7,2,35,1,11
	.byte	'LVDSRL',0,1
	.word	650
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	650
	.byte	2,4,2,35,1,11
	.byte	'TDIS_CTRL',0,1
	.word	650
	.byte	1,3,2,35,1,11
	.byte	'TX_DIS',0,1
	.word	650
	.byte	1,2,2,35,1,11
	.byte	'TX_PD',0,1
	.word	650
	.byte	1,1,2,35,1,11
	.byte	'TX_PWDPD',0,1
	.word	650
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	667
	.byte	16,0,2,35,2,0,12,6,213,4,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7700
	.byte	4,2,35,0,0,15,76
	.word	650
	.byte	16,75,0,10
	.byte	'_Ifx_P_ACCEN1_Bits',0,6,82,16,4,11
	.byte	'reserved_0',0,4
	.word	475
	.byte	32,0,2,35,0,0,12,6,132,4,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7953
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ACCEN0_Bits',0,6,45,16,4,11
	.byte	'EN0',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	650
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	650
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	650
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	650
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	650
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	650
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	650
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	650
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	650
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	650
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	650
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	650
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	650
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	650
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	650
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	650
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	650
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	650
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	650
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	650
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	650
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	650
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	650
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	650
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	650
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	650
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	650
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	650
	.byte	1,0,2,35,3,0,12,6,252,3,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8040
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P',0,6,229,5,25,128,2,13
	.byte	'OUT',0
	.word	1738
	.byte	4,2,35,0,13
	.byte	'OMR',0
	.word	2309
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	2428
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	2468
	.byte	4,2,35,12,13
	.byte	'IOCR0',0
	.word	2652
	.byte	4,2,35,16,13
	.byte	'IOCR4',0
	.word	2867
	.byte	4,2,35,20,13
	.byte	'IOCR8',0
	.word	3084
	.byte	4,2,35,24,13
	.byte	'IOCR12',0
	.word	3304
	.byte	4,2,35,28,13
	.byte	'reserved_20',0
	.word	2468
	.byte	4,2,35,32,13
	.byte	'IN',0
	.word	3618
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	3658
	.byte	24,2,35,40,13
	.byte	'PDR0',0
	.word	3931
	.byte	4,2,35,64,13
	.byte	'PDR1',0
	.word	4247
	.byte	4,2,35,68,13
	.byte	'reserved_48',0
	.word	4287
	.byte	8,2,35,72,13
	.byte	'ESR',0
	.word	4587
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	4627
	.byte	12,2,35,84,13
	.byte	'PDISC',0
	.word	4962
	.byte	4,2,35,96,13
	.byte	'PCSR',0
	.word	5248
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	4287
	.byte	8,2,35,104,13
	.byte	'OMSR0',0
	.word	5395
	.byte	4,2,35,112,13
	.byte	'OMSR4',0
	.word	5564
	.byte	4,2,35,116,13
	.byte	'OMSR8',0
	.word	5736
	.byte	4,2,35,120,13
	.byte	'OMSR12',0
	.word	5911
	.byte	4,2,35,124,13
	.byte	'OMCR0',0
	.word	6085
	.byte	4,3,35,128,1,13
	.byte	'OMCR4',0
	.word	6259
	.byte	4,3,35,132,1,13
	.byte	'OMCR8',0
	.word	6435
	.byte	4,3,35,136,1,13
	.byte	'OMCR12',0
	.word	6591
	.byte	4,3,35,140,1,13
	.byte	'OMSR',0
	.word	6924
	.byte	4,3,35,144,1,13
	.byte	'OMCR',0
	.word	7272
	.byte	4,3,35,148,1,13
	.byte	'reserved_98',0
	.word	4287
	.byte	8,3,35,152,1,13
	.byte	'LPCR0',0
	.word	7396
	.byte	4,3,35,160,1,13
	.byte	'LPCR1',0
	.word	7645
	.byte	4,3,35,164,1,13
	.byte	'LPCR2',0
	.word	7904
	.byte	4,3,35,168,1,13
	.byte	'reserved_A4',0
	.word	7944
	.byte	76,3,35,172,1,13
	.byte	'ACCEN1',0
	.word	8000
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	8567
	.byte	4,3,35,252,1,0,14
	.word	8607
	.byte	3
	.word	9210
	.byte	17,5,83,9,1,18
	.byte	'IfxPort_InputMode_undefined',0,127,18
	.byte	'IfxPort_InputMode_noPullDevice',0,0,18
	.byte	'IfxPort_InputMode_pullDown',0,8,18
	.byte	'IfxPort_InputMode_pullUp',0,16,0,4
	.byte	'IfxPort_setPinModeInput',0,3,5,196,4,17,1,1,5
	.byte	'port',0,5,196,4,48
	.word	9215
	.byte	5
	.byte	'pinIndex',0,5,196,4,60
	.word	650
	.byte	5
	.byte	'mode',0,5,196,4,88
	.word	9220
	.byte	6,0,17,5,134,1,9,1,18
	.byte	'IfxPort_OutputMode_pushPull',0,128,1,18
	.byte	'IfxPort_OutputMode_openDrain',0,192,1,0,17,5,120,9,1,18
	.byte	'IfxPort_OutputIdx_general',0,128,1,18
	.byte	'IfxPort_OutputIdx_alt1',0,136,1,18
	.byte	'IfxPort_OutputIdx_alt2',0,144,1,18
	.byte	'IfxPort_OutputIdx_alt3',0,152,1,18
	.byte	'IfxPort_OutputIdx_alt4',0,160,1,18
	.byte	'IfxPort_OutputIdx_alt5',0,168,1,18
	.byte	'IfxPort_OutputIdx_alt6',0,176,1,18
	.byte	'IfxPort_OutputIdx_alt7',0,184,1,0,4
	.byte	'IfxPort_setPinModeOutput',0,3,5,202,4,17,1,1,5
	.byte	'port',0,5,202,4,49
	.word	9215
	.byte	5
	.byte	'pinIndex',0,5,202,4,61
	.word	650
	.byte	5
	.byte	'mode',0,5,202,4,90
	.word	9425
	.byte	5
	.byte	'index',0,5,202,4,114
	.word	9495
	.byte	6,0,17,5,172,1,9,4,18
	.byte	'IfxPort_State_notChanged',0,0,18
	.byte	'IfxPort_State_high',0,1,18
	.byte	'IfxPort_State_low',0,128,128,4,18
	.byte	'IfxPort_State_toggled',0,129,128,4,0,4
	.byte	'IfxPort_setPinState',0,3,5,208,4,17,1,1,5
	.byte	'port',0,5,208,4,44
	.word	9215
	.byte	5
	.byte	'pinIndex',0,5,208,4,56
	.word	650
	.byte	5
	.byte	'action',0,5,208,4,80
	.word	9808
	.byte	6,0,17,8,156,1,9,1,18
	.byte	'IfxCpu_ResourceCpu_0',0,0,18
	.byte	'IfxCpu_ResourceCpu_1',0,1,18
	.byte	'IfxCpu_ResourceCpu_none',0,2,0,8
	.byte	'IfxCpu_getCoreIndex',0,3,7,141,6,31
	.word	9989
	.byte	1,1,6,0,8
	.byte	'IfxCpu_areInterruptsEnabled',0,3,7,139,5,20
	.word	650
	.byte	1,1,6,0,8
	.byte	'IfxCpu_disableInterrupts',0,3,7,147,5,20
	.word	650
	.byte	1,1,19,6,0,0,4
	.byte	'IfxCpu_enableInterrupts',0,3,7,157,5,17,1,1,6,0,4
	.byte	'IfxCpu_forceDisableInterrupts',0,3,7,225,5,17,1,1,6,0,4
	.byte	'IfxCpu_restoreInterrupts',0,3,7,168,7,17,1,1,5
	.byte	'enabled',0,7,168,7,50
	.word	650
	.byte	6,0
.L101:
	.byte	7
	.byte	'unsigned long int',0,4,7,8
	.byte	'IfxCpu_getPerformanceCounter',0,3,7,161,6,19
	.word	10311
	.byte	1,1,5
	.byte	'address',0,7,161,6,55
	.word	667
	.byte	6,0,8
	.byte	'IfxCpu_getPerformanceCounterStickyOverflow',0,3,7,190,6,20
	.word	650
	.byte	1,1,5
	.byte	'address',0,7,190,6,70
	.word	667
	.byte	6,0,4
	.byte	'IfxCpu_updatePerformanceCounter',0,3,7,172,8,17,1,1,5
	.byte	'address',0,7,172,8,56
	.word	10311
	.byte	5
	.byte	'count',0,7,172,8,72
	.word	10311
	.byte	19,6,0,0,8
	.byte	'IfxScuCcu_getStmFrequency',0,3,9,226,8,20
	.word	271
	.byte	1,1,6,0,10
	.byte	'_Ifx_SRC_SRCR_Bits',0,11,45,16,4,11
	.byte	'SRPN',0,1
	.word	650
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	650
	.byte	2,6,2,35,1,11
	.byte	'SRE',0,1
	.word	650
	.byte	1,5,2,35,1,11
	.byte	'TOS',0,1
	.word	650
	.byte	2,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	650
	.byte	3,0,2,35,1,11
	.byte	'ECC',0,1
	.word	650
	.byte	6,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	650
	.byte	2,0,2,35,2,11
	.byte	'SRR',0,1
	.word	650
	.byte	1,7,2,35,3,11
	.byte	'CLRR',0,1
	.word	650
	.byte	1,6,2,35,3,11
	.byte	'SETR',0,1
	.word	650
	.byte	1,5,2,35,3,11
	.byte	'IOV',0,1
	.word	650
	.byte	1,4,2,35,3,11
	.byte	'IOVCLR',0,1
	.word	650
	.byte	1,3,2,35,3,11
	.byte	'SWS',0,1
	.word	650
	.byte	1,2,2,35,3,11
	.byte	'SWSCLR',0,1
	.word	650
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	650
	.byte	1,0,2,35,3,0,12,11,70,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10582
	.byte	4,2,35,0,0,14
	.word	10872
	.byte	3
	.word	10911
	.byte	4
	.byte	'IfxSrc_clearRequest',0,3,10,250,1,17,1,1,5
	.byte	'src',0,10,250,1,60
	.word	10916
	.byte	6,0,10
	.byte	'_Ifx_ASCLIN_CLC_Bits',0,13,118,16,4,11
	.byte	'DISR',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'DISS',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'EDIS',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	475
	.byte	28,0,2,35,0,0,12,13,207,3,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10964
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_IOCR_Bits',0,13,169,2,16,4,11
	.byte	'ALTI',0,1
	.word	650
	.byte	3,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'DEPTH',0,2
	.word	667
	.byte	6,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	650
	.byte	6,0,2,35,1,11
	.byte	'CTS',0,1
	.word	650
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	667
	.byte	7,7,2,35,2,11
	.byte	'RCPOL',0,1
	.word	650
	.byte	1,6,2,35,3,11
	.byte	'CPOL',0,1
	.word	650
	.byte	1,5,2,35,3,11
	.byte	'SPOL',0,1
	.word	650
	.byte	1,4,2,35,3,11
	.byte	'LB',0,1
	.word	650
	.byte	1,3,2,35,3,11
	.byte	'CTSEN',0,1
	.word	650
	.byte	1,2,2,35,3,11
	.byte	'RXM',0,1
	.word	650
	.byte	1,1,2,35,3,11
	.byte	'TXM',0,1
	.word	650
	.byte	1,0,2,35,3,0,12,13,151,4,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11123
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_ID_Bits',0,13,161,2,16,4,11
	.byte	'MODREV',0,1
	.word	650
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	650
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	667
	.byte	16,0,2,35,2,0,12,13,143,4,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11418
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_TXFIFOCON_Bits',0,13,149,3,16,4,11
	.byte	'FLUSH',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'ENO',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	650
	.byte	4,2,2,35,0,11
	.byte	'INW',0,1
	.word	650
	.byte	2,0,2,35,0,11
	.byte	'INTLEVEL',0,1
	.word	650
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	650
	.byte	4,0,2,35,1,11
	.byte	'FILL',0,1
	.word	650
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,2
	.word	667
	.byte	11,0,2,35,2,0,12,13,247,4,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11543
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_RXFIFOCON_Bits',0,13,129,3,16,4,11
	.byte	'FLUSH',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'ENI',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	650
	.byte	4,2,2,35,0,11
	.byte	'OUTW',0,1
	.word	650
	.byte	2,0,2,35,0,11
	.byte	'INTLEVEL',0,1
	.word	650
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	650
	.byte	4,0,2,35,1,11
	.byte	'FILL',0,1
	.word	650
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,2
	.word	667
	.byte	10,1,2,35,2,11
	.byte	'BUF',0,1
	.word	650
	.byte	1,0,2,35,3,0,12,13,231,4,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11768
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_BITCON_Bits',0,13,88,16,4,11
	.byte	'PRESCALER',0,2
	.word	667
	.byte	12,4,2,35,0,11
	.byte	'reserved_12',0,1
	.word	650
	.byte	4,0,2,35,1,11
	.byte	'OVERSAMPLING',0,1
	.word	650
	.byte	4,4,2,35,2,11
	.byte	'reserved_20',0,1
	.word	650
	.byte	4,0,2,35,2,11
	.byte	'SAMPLEPOINT',0,1
	.word	650
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	650
	.byte	3,1,2,35,3,11
	.byte	'SM',0,1
	.word	650
	.byte	1,0,2,35,3,0,12,13,183,3,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12009
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_FRAMECON_Bits',0,13,145,2,16,4,11
	.byte	'reserved_0',0,1
	.word	650
	.byte	6,2,2,35,0,11
	.byte	'IDLE',0,2
	.word	667
	.byte	3,7,2,35,0,11
	.byte	'STOP',0,1
	.word	650
	.byte	3,4,2,35,1,11
	.byte	'LEAD',0,1
	.word	650
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	650
	.byte	1,0,2,35,1,11
	.byte	'MODE',0,1
	.word	650
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	667
	.byte	10,4,2,35,2,11
	.byte	'MSB',0,1
	.word	650
	.byte	1,3,2,35,3,11
	.byte	'CEN',0,1
	.word	650
	.byte	1,2,2,35,3,11
	.byte	'PEN',0,1
	.word	650
	.byte	1,1,2,35,3,11
	.byte	'ODD',0,1
	.word	650
	.byte	1,0,2,35,3,0,12,13,135,4,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12230
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_DATCON_Bits',0,13,136,1,16,4,11
	.byte	'DATLEN',0,1
	.word	650
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	667
	.byte	9,3,2,35,0,11
	.byte	'HO',0,1
	.word	650
	.byte	1,2,2,35,1,11
	.byte	'RM',0,1
	.word	650
	.byte	1,1,2,35,1,11
	.byte	'CSM',0,1
	.word	650
	.byte	1,0,2,35,1,11
	.byte	'RESPONSE',0,1
	.word	650
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	650
	.byte	8,0,2,35,3,0,12,13,223,3,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12495
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_BRG_Bits',0,13,109,16,4,11
	.byte	'DENOMINATOR',0,2
	.word	667
	.byte	12,4,2,35,0,11
	.byte	'reserved_12',0,1
	.word	650
	.byte	4,0,2,35,1,11
	.byte	'NUMERATOR',0,2
	.word	667
	.byte	12,4,2,35,2,11
	.byte	'reserved_28',0,1
	.word	650
	.byte	4,0,2,35,3,0,12,13,199,3,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12692
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_BRD_Bits',0,13,100,16,4,11
	.byte	'LOWERLIMIT',0,1
	.word	650
	.byte	8,0,2,35,0,11
	.byte	'UPPERLIMIT',0,1
	.word	650
	.byte	8,0,2,35,1,11
	.byte	'MEASURED',0,2
	.word	667
	.byte	12,4,2,35,2,11
	.byte	'reserved_28',0,1
	.word	650
	.byte	4,0,2,35,3,0,12,13,191,3,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12849
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_LIN_CON_Bits',0,13,216,2,16,4,11
	.byte	'reserved_0',0,4
	.word	475
	.byte	23,9,2,35,0,11
	.byte	'CSI',0,1
	.word	650
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	650
	.byte	1,7,2,35,3,11
	.byte	'CSEN',0,1
	.word	650
	.byte	1,6,2,35,3,11
	.byte	'MS',0,1
	.word	650
	.byte	1,5,2,35,3,11
	.byte	'ABD',0,1
	.word	650
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	650
	.byte	4,0,2,35,3,0,12,13,191,4,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13003
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_LIN_BTIMER_Bits',0,13,209,2,16,4,11
	.byte	'BREAK',0,1
	.word	650
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	475
	.byte	26,0,2,35,0,0,12,13,183,4,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13203
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_LIN_HTIMER_Bits',0,13,228,2,16,4,11
	.byte	'HEADER',0,1
	.word	650
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	475
	.byte	24,0,2,35,0,0,12,13,199,4,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13317
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_LIN',0,13,135,5,25,12,13
	.byte	'CON',0
	.word	13163
	.byte	4,2,35,0,13
	.byte	'BTIMER',0
	.word	13277
	.byte	4,2,35,4,13
	.byte	'HTIMER',0
	.word	13392
	.byte	4,2,35,8,0,14
	.word	13432
	.byte	10
	.byte	'_Ifx_ASCLIN_FLAGS_Bits',0,13,148,1,16,4,11
	.byte	'TH',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'TR',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'RH',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'RR',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	650
	.byte	1,3,2,35,0,11
	.byte	'FED',0,1
	.word	650
	.byte	1,2,2,35,0,11
	.byte	'RED',0,1
	.word	650
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	667
	.byte	6,3,2,35,0,11
	.byte	'TWRQ',0,1
	.word	650
	.byte	1,2,2,35,1,11
	.byte	'THRQ',0,1
	.word	650
	.byte	1,1,2,35,1,11
	.byte	'TRRQ',0,1
	.word	650
	.byte	1,0,2,35,1,11
	.byte	'PE',0,1
	.word	650
	.byte	1,7,2,35,2,11
	.byte	'TC',0,1
	.word	650
	.byte	1,6,2,35,2,11
	.byte	'FE',0,1
	.word	650
	.byte	1,5,2,35,2,11
	.byte	'HT',0,1
	.word	650
	.byte	1,4,2,35,2,11
	.byte	'RT',0,1
	.word	650
	.byte	1,3,2,35,2,11
	.byte	'BD',0,1
	.word	650
	.byte	1,2,2,35,2,11
	.byte	'LP',0,1
	.word	650
	.byte	1,1,2,35,2,11
	.byte	'LA',0,1
	.word	650
	.byte	1,0,2,35,2,11
	.byte	'LC',0,1
	.word	650
	.byte	1,7,2,35,3,11
	.byte	'CE',0,1
	.word	650
	.byte	1,6,2,35,3,11
	.byte	'RFO',0,1
	.word	650
	.byte	1,5,2,35,3,11
	.byte	'RFU',0,1
	.word	650
	.byte	1,4,2,35,3,11
	.byte	'RFL',0,1
	.word	650
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	650
	.byte	1,2,2,35,3,11
	.byte	'TFO',0,1
	.word	650
	.byte	1,1,2,35,3,11
	.byte	'TFL',0,1
	.word	650
	.byte	1,0,2,35,3,0,12,13,231,3,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13505
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_FLAGSSET_Bits',0,13,241,1,16,4,11
	.byte	'THS',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'TRS',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'RHS',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'RRS',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	650
	.byte	1,3,2,35,0,11
	.byte	'FEDS',0,1
	.word	650
	.byte	1,2,2,35,0,11
	.byte	'REDS',0,1
	.word	650
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	667
	.byte	6,3,2,35,0,11
	.byte	'TWRQS',0,1
	.word	650
	.byte	1,2,2,35,1,11
	.byte	'THRQS',0,1
	.word	650
	.byte	1,1,2,35,1,11
	.byte	'TRRQS',0,1
	.word	650
	.byte	1,0,2,35,1,11
	.byte	'PES',0,1
	.word	650
	.byte	1,7,2,35,2,11
	.byte	'TCS',0,1
	.word	650
	.byte	1,6,2,35,2,11
	.byte	'FES',0,1
	.word	650
	.byte	1,5,2,35,2,11
	.byte	'HTS',0,1
	.word	650
	.byte	1,4,2,35,2,11
	.byte	'RTS',0,1
	.word	650
	.byte	1,3,2,35,2,11
	.byte	'BDS',0,1
	.word	650
	.byte	1,2,2,35,2,11
	.byte	'LPS',0,1
	.word	650
	.byte	1,1,2,35,2,11
	.byte	'LAS',0,1
	.word	650
	.byte	1,0,2,35,2,11
	.byte	'LCS',0,1
	.word	650
	.byte	1,7,2,35,3,11
	.byte	'CES',0,1
	.word	650
	.byte	1,6,2,35,3,11
	.byte	'RFOS',0,1
	.word	650
	.byte	1,5,2,35,3,11
	.byte	'RFUS',0,1
	.word	650
	.byte	1,4,2,35,3,11
	.byte	'RFLS',0,1
	.word	650
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	650
	.byte	1,2,2,35,3,11
	.byte	'TFOS',0,1
	.word	650
	.byte	1,1,2,35,3,11
	.byte	'TFLS',0,1
	.word	650
	.byte	1,0,2,35,3,0,12,13,255,3,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13991
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_FLAGSCLEAR_Bits',0,13,180,1,16,4,11
	.byte	'THC',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'TRC',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'RHC',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'RRC',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	650
	.byte	1,3,2,35,0,11
	.byte	'FEDC',0,1
	.word	650
	.byte	1,2,2,35,0,11
	.byte	'REDC',0,1
	.word	650
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	667
	.byte	6,3,2,35,0,11
	.byte	'TWRQC',0,1
	.word	650
	.byte	1,2,2,35,1,11
	.byte	'THRQC',0,1
	.word	650
	.byte	1,1,2,35,1,11
	.byte	'TRRQC',0,1
	.word	650
	.byte	1,0,2,35,1,11
	.byte	'PEC',0,1
	.word	650
	.byte	1,7,2,35,2,11
	.byte	'TCC',0,1
	.word	650
	.byte	1,6,2,35,2,11
	.byte	'FEC',0,1
	.word	650
	.byte	1,5,2,35,2,11
	.byte	'HTC',0,1
	.word	650
	.byte	1,4,2,35,2,11
	.byte	'RTC',0,1
	.word	650
	.byte	1,3,2,35,2,11
	.byte	'BDC',0,1
	.word	650
	.byte	1,2,2,35,2,11
	.byte	'LPC',0,1
	.word	650
	.byte	1,1,2,35,2,11
	.byte	'LAC',0,1
	.word	650
	.byte	1,0,2,35,2,11
	.byte	'LCC',0,1
	.word	650
	.byte	1,7,2,35,3,11
	.byte	'CEC',0,1
	.word	650
	.byte	1,6,2,35,3,11
	.byte	'RFOC',0,1
	.word	650
	.byte	1,5,2,35,3,11
	.byte	'RFUC',0,1
	.word	650
	.byte	1,4,2,35,3,11
	.byte	'RFLC',0,1
	.word	650
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	650
	.byte	1,2,2,35,3,11
	.byte	'TFOC',0,1
	.word	650
	.byte	1,1,2,35,3,11
	.byte	'TFLC',0,1
	.word	650
	.byte	1,0,2,35,3,0,12,13,239,3,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14504
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_FLAGSENABLE_Bits',0,13,212,1,16,4,11
	.byte	'THE',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'TRE',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'RHE',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'RRE',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	650
	.byte	1,3,2,35,0,11
	.byte	'FEDE',0,1
	.word	650
	.byte	1,2,2,35,0,11
	.byte	'REDE',0,1
	.word	650
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	667
	.byte	9,0,2,35,0,11
	.byte	'PEE',0,1
	.word	650
	.byte	1,7,2,35,2,11
	.byte	'TCE',0,1
	.word	650
	.byte	1,6,2,35,2,11
	.byte	'FEE',0,1
	.word	650
	.byte	1,5,2,35,2,11
	.byte	'HTE',0,1
	.word	650
	.byte	1,4,2,35,2,11
	.byte	'RTE',0,1
	.word	650
	.byte	1,3,2,35,2,11
	.byte	'BDE',0,1
	.word	650
	.byte	1,2,2,35,2,11
	.byte	'LPE',0,1
	.word	650
	.byte	1,1,2,35,2,11
	.byte	'ABE',0,1
	.word	650
	.byte	1,0,2,35,2,11
	.byte	'LCE',0,1
	.word	650
	.byte	1,7,2,35,3,11
	.byte	'CEE',0,1
	.word	650
	.byte	1,6,2,35,3,11
	.byte	'RFOE',0,1
	.word	650
	.byte	1,5,2,35,3,11
	.byte	'RFUE',0,1
	.word	650
	.byte	1,4,2,35,3,11
	.byte	'RFLE',0,1
	.word	650
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	650
	.byte	1,2,2,35,3,11
	.byte	'TFOE',0,1
	.word	650
	.byte	1,1,2,35,3,11
	.byte	'TFLE',0,1
	.word	650
	.byte	1,0,2,35,3,0,12,13,247,3,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15019
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_TXDATA_Bits',0,13,143,3,16,4,11
	.byte	'DATA',0,4
	.word	475
	.byte	32,0,2,35,0,0,12,13,239,4,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15484
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_RXDATA_Bits',0,13,245,2,16,4,11
	.byte	'DATA',0,4
	.word	475
	.byte	32,0,2,35,0,0,12,13,215,4,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15571
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_CSR_Bits',0,13,128,1,16,4,11
	.byte	'CLKSEL',0,1
	.word	650
	.byte	5,3,2,35,0,11
	.byte	'reserved_5',0,4
	.word	475
	.byte	26,1,2,35,0,11
	.byte	'CON',0,1
	.word	650
	.byte	1,0,2,35,3,0,12,13,215,3,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15658
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_RXDATAD_Bits',0,13,251,2,16,4,11
	.byte	'DATA',0,4
	.word	475
	.byte	32,0,2,35,0,0,12,13,223,4,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15781
	.byte	4,2,35,0,0,15,148,1
	.word	650
	.byte	16,147,1,0,10
	.byte	'_Ifx_ASCLIN_OCS_Bits',0,13,235,2,16,4,11
	.byte	'reserved_0',0,4
	.word	475
	.byte	24,8,2,35,0,11
	.byte	'SUS',0,1
	.word	650
	.byte	4,4,2,35,3,11
	.byte	'SUS_P',0,1
	.word	650
	.byte	1,3,2,35,3,11
	.byte	'SUSSTA',0,1
	.word	650
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	650
	.byte	2,0,2,35,3,0,12,13,207,4,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15880
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_KRSTCLR_Bits',0,13,202,2,16,4,11
	.byte	'CLR',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	475
	.byte	31,0,2,35,0,0,12,13,175,4,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16043
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_KRST1_Bits',0,13,195,2,16,4,11
	.byte	'RST',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	475
	.byte	31,0,2,35,0,0,12,13,167,4,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16152
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_KRST0_Bits',0,13,187,2,16,4,11
	.byte	'RST',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'RSTSTAT',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	475
	.byte	30,0,2,35,0,0,12,13,159,4,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16259
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_ACCEN1_Bits',0,13,82,16,4,11
	.byte	'reserved_0',0,4
	.word	475
	.byte	32,0,2,35,0,0,12,13,175,3,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16385
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_ACCEN0_Bits',0,13,45,16,4,11
	.byte	'EN0',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	650
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	650
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	650
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	650
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	650
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	650
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	650
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	650
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	650
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	650
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	650
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	650
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	650
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	650
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	650
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	650
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	650
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	650
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	650
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	650
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	650
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	650
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	650
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	650
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	650
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	650
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	650
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	650
	.byte	1,0,2,35,3,0,12,13,167,3,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16477
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN',0,13,153,5,25,128,2,13
	.byte	'CLC',0
	.word	11083
	.byte	4,2,35,0,13
	.byte	'IOCR',0
	.word	11378
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	11503
	.byte	4,2,35,8,13
	.byte	'TXFIFOCON',0
	.word	11728
	.byte	4,2,35,12,13
	.byte	'RXFIFOCON',0
	.word	11969
	.byte	4,2,35,16,13
	.byte	'BITCON',0
	.word	12190
	.byte	4,2,35,20,13
	.byte	'FRAMECON',0
	.word	12455
	.byte	4,2,35,24,13
	.byte	'DATCON',0
	.word	12652
	.byte	4,2,35,28,13
	.byte	'BRG',0
	.word	12809
	.byte	4,2,35,32,13
	.byte	'BRD',0
	.word	12963
	.byte	4,2,35,36,13
	.byte	'LIN',0
	.word	13500
	.byte	12,2,35,40,13
	.byte	'FLAGS',0
	.word	13951
	.byte	4,2,35,52,13
	.byte	'FLAGSSET',0
	.word	14464
	.byte	4,2,35,56,13
	.byte	'FLAGSCLEAR',0
	.word	14979
	.byte	4,2,35,60,13
	.byte	'FLAGSENABLE',0
	.word	15444
	.byte	4,2,35,64,13
	.byte	'TXDATA',0
	.word	15531
	.byte	4,2,35,68,13
	.byte	'RXDATA',0
	.word	15618
	.byte	4,2,35,72,13
	.byte	'CSR',0
	.word	15741
	.byte	4,2,35,76,13
	.byte	'RXDATAD',0
	.word	15829
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	15869
	.byte	148,1,2,35,84,13
	.byte	'OCS',0
	.word	16003
	.byte	4,3,35,232,1,13
	.byte	'KRSTCLR',0
	.word	16112
	.byte	4,3,35,236,1,13
	.byte	'KRST1',0
	.word	16219
	.byte	4,3,35,240,1,13
	.byte	'KRST0',0
	.word	16345
	.byte	4,3,35,244,1,13
	.byte	'ACCEN1',0
	.word	16437
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	17009
	.byte	4,3,35,252,1,0,14
	.word	17049
	.byte	3
	.word	17491
	.byte	4
	.byte	'IfxAsclin_enableCts',0,3,12,228,13,17,1,1,5
	.byte	'asclin',0,12,228,13,49
	.word	17496
	.byte	5
	.byte	'enable',0,12,228,13,65
	.word	650
	.byte	6,0,17,12,123,9,1,18
	.byte	'IfxAsclin_CtsInputSelect_0',0,0,18
	.byte	'IfxAsclin_CtsInputSelect_1',0,1,18
	.byte	'IfxAsclin_CtsInputSelect_2',0,2,18
	.byte	'IfxAsclin_CtsInputSelect_3',0,3,0,4
	.byte	'IfxAsclin_setCtsInput',0,3,12,169,17,17,1,1,5
	.byte	'asclin',0,12,169,17,51
	.word	17496
	.byte	5
	.byte	'ctsi',0,12,169,17,84
	.word	17563
	.byte	6,0,17,12,181,2,9,1,18
	.byte	'IfxAsclin_RxInputSelect_0',0,0,18
	.byte	'IfxAsclin_RxInputSelect_1',0,1,18
	.byte	'IfxAsclin_RxInputSelect_2',0,2,18
	.byte	'IfxAsclin_RxInputSelect_3',0,3,18
	.byte	'IfxAsclin_RxInputSelect_4',0,4,18
	.byte	'IfxAsclin_RxInputSelect_5',0,5,18
	.byte	'IfxAsclin_RxInputSelect_6',0,6,18
	.byte	'IfxAsclin_RxInputSelect_7',0,7,0,4
	.byte	'IfxAsclin_setRxInput',0,3,12,191,18,17,1,1,5
	.byte	'asclin',0,12,191,18,50
	.word	17496
	.byte	5
	.byte	'alti',0,12,191,18,82
	.word	17747
	.byte	6,0,7
	.byte	'short int',0,2,5,7
	.byte	'long int',0,4,5,20,14,60,9,12,13
	.byte	'count',0
	.word	18039
	.byte	2,2,35,0,13
	.byte	'readerWaitx',0
	.word	18052
	.byte	4,2,35,2,13
	.byte	'writerWaitx',0
	.word	18052
	.byte	4,2,35,6,13
	.byte	'maxcount',0
	.word	18039
	.byte	2,2,35,10,0,14
	.word	650
	.byte	14
	.word	650
	.byte	10
	.byte	'_Fifo',0,14,73,16,28,13
	.byte	'buffer',0
	.word	389
	.byte	4,2,35,0,13
	.byte	'shared',0
	.word	18064
	.byte	12,2,35,4,13
	.byte	'startIndex',0
	.word	18039
	.byte	2,2,35,16,13
	.byte	'endIndex',0
	.word	18039
	.byte	2,2,35,18,13
	.byte	'size',0
	.word	18039
	.byte	2,2,35,20,13
	.byte	'elementSize',0
	.word	18039
	.byte	2,2,35,22,13
	.byte	'eventReader',0
	.word	18145
	.byte	1,2,35,24,13
	.byte	'eventWriter',0
	.word	18150
	.byte	1,2,35,25,0,3
	.word	18155
	.byte	8
	.byte	'Ifx_Fifo_readCount',0,3,14,206,1,22
	.word	18039
	.byte	1,1,5
	.byte	'fifo',0,14,206,1,51
	.word	18314
	.byte	6,0,10
	.byte	'_Ifx_STM_CLC_Bits',0,16,100,16,4,11
	.byte	'DISR',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'DISS',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'EDIS',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	475
	.byte	28,0,2,35,0,0,12,16,149,2,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18366
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_ID_Bits',0,16,142,1,16,4,11
	.byte	'MODREV',0,1
	.word	650
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	650
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	667
	.byte	16,0,2,35,2,0,12,16,181,2,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18522
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM0_Bits',0,16,192,1,16,4,11
	.byte	'STM31_0',0,4
	.word	475
	.byte	32,0,2,35,0,0,12,16,229,2,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18644
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM1_Bits',0,16,204,1,16,4,11
	.byte	'STM35_4',0,4
	.word	475
	.byte	32,0,2,35,0,0,12,16,245,2,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18729
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM2_Bits',0,16,210,1,16,4,11
	.byte	'STM39_8',0,4
	.word	475
	.byte	32,0,2,35,0,0,12,16,253,2,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18814
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM3_Bits',0,16,216,1,16,4,11
	.byte	'STM43_12',0,4
	.word	475
	.byte	32,0,2,35,0,0,12,16,133,3,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18899
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM4_Bits',0,16,222,1,16,4,11
	.byte	'STM47_16',0,4
	.word	475
	.byte	32,0,2,35,0,0,12,16,141,3,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18985
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM5_Bits',0,16,228,1,16,4,11
	.byte	'STM51_20',0,4
	.word	475
	.byte	32,0,2,35,0,0,12,16,149,3,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19071
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM6_Bits',0,16,234,1,16,4,11
	.byte	'STM63_32',0,4
	.word	475
	.byte	32,0,2,35,0,0,12,16,157,3,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19157
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_CAP_Bits',0,16,88,16,4,11
	.byte	'STMCAP63_32',0,4
	.word	475
	.byte	32,0,2,35,0,0,12,16,133,2,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19243
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_CMP_Bits',0,16,123,16,4,11
	.byte	'CMPVAL',0,4
	.word	475
	.byte	32,0,2,35,0,0,12,16,165,2,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19330
	.byte	4,2,35,0,0,15,8
	.word	19372
	.byte	16,1,0,10
	.byte	'_Ifx_STM_CMCON_Bits',0,16,110,16,4,11
	.byte	'MSIZE0',0,1
	.word	650
	.byte	5,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	650
	.byte	3,0,2,35,0,11
	.byte	'MSTART0',0,1
	.word	650
	.byte	5,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	650
	.byte	3,0,2,35,1,11
	.byte	'MSIZE1',0,1
	.word	650
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	650
	.byte	3,0,2,35,2,11
	.byte	'MSTART1',0,1
	.word	650
	.byte	5,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	650
	.byte	3,0,2,35,3,0,12,16,157,2,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19421
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_ICR_Bits',0,16,129,1,16,4,11
	.byte	'CMP0EN',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'CMP0IR',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'CMP0OS',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'CMP1EN',0,1
	.word	650
	.byte	1,3,2,35,0,11
	.byte	'CMP1IR',0,1
	.word	650
	.byte	1,2,2,35,0,11
	.byte	'CMP1OS',0,1
	.word	650
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,4
	.word	475
	.byte	25,0,2,35,0,0,12,16,173,2,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19652
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_ISCR_Bits',0,16,150,1,16,4,11
	.byte	'CMP0IRR',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'CMP0IRS',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'CMP1IRR',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'CMP1IRS',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	475
	.byte	28,0,2,35,0,0,12,16,189,2,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19869
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM0SV_Bits',0,16,198,1,16,4,11
	.byte	'STM31_0',0,4
	.word	475
	.byte	32,0,2,35,0,0,12,16,237,2,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20033
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_CAPSV_Bits',0,16,94,16,4,11
	.byte	'STMCAP63_32',0,4
	.word	475
	.byte	32,0,2,35,0,0,12,16,141,2,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20120
	.byte	4,2,35,0,0,15,144,1
	.word	650
	.byte	16,143,1,0,10
	.byte	'_Ifx_STM_OCS_Bits',0,16,182,1,16,4,11
	.byte	'reserved_0',0,4
	.word	475
	.byte	24,8,2,35,0,11
	.byte	'SUS',0,1
	.word	650
	.byte	4,4,2,35,3,11
	.byte	'SUS_P',0,1
	.word	650
	.byte	1,3,2,35,3,11
	.byte	'SUSSTA',0,1
	.word	650
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	650
	.byte	2,0,2,35,3,0,12,16,221,2,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20220
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_KRSTCLR_Bits',0,16,175,1,16,4,11
	.byte	'CLR',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	475
	.byte	31,0,2,35,0,0,12,16,213,2,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20380
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_KRST1_Bits',0,16,168,1,16,4,11
	.byte	'RST',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	475
	.byte	31,0,2,35,0,0,12,16,205,2,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20486
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_KRST0_Bits',0,16,160,1,16,4,11
	.byte	'RST',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'RSTSTAT',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	475
	.byte	30,0,2,35,0,0,12,16,197,2,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20590
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_ACCEN1_Bits',0,16,82,16,4,11
	.byte	'reserved_0',0,4
	.word	475
	.byte	32,0,2,35,0,0,12,16,253,1,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20713
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_ACCEN0_Bits',0,16,45,16,4,11
	.byte	'EN0',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	650
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	650
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	650
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	650
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	650
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	650
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	650
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	650
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	650
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	650
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	650
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	650
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	650
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	650
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	650
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	650
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	650
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	650
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	650
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	650
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	650
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	650
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	650
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	650
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	650
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	650
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	650
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	650
	.byte	1,0,2,35,3,0,12,16,245,1,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20802
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM',0,16,173,3,25,128,2,13
	.byte	'CLC',0
	.word	18482
	.byte	4,2,35,0,13
	.byte	'reserved_4',0
	.word	2468
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	18604
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	2468
	.byte	4,2,35,12,13
	.byte	'TIM0',0
	.word	18689
	.byte	4,2,35,16,13
	.byte	'TIM1',0
	.word	18774
	.byte	4,2,35,20,13
	.byte	'TIM2',0
	.word	18859
	.byte	4,2,35,24,13
	.byte	'TIM3',0
	.word	18945
	.byte	4,2,35,28,13
	.byte	'TIM4',0
	.word	19031
	.byte	4,2,35,32,13
	.byte	'TIM5',0
	.word	19117
	.byte	4,2,35,36,13
	.byte	'TIM6',0
	.word	19203
	.byte	4,2,35,40,13
	.byte	'CAP',0
	.word	19290
	.byte	4,2,35,44,13
	.byte	'CMP',0
	.word	19412
	.byte	8,2,35,48,13
	.byte	'CMCON',0
	.word	19612
	.byte	4,2,35,56,13
	.byte	'ICR',0
	.word	19829
	.byte	4,2,35,60,13
	.byte	'ISCR',0
	.word	19993
	.byte	4,2,35,64,13
	.byte	'reserved_44',0
	.word	4627
	.byte	12,2,35,68,13
	.byte	'TIM0SV',0
	.word	20080
	.byte	4,2,35,80,13
	.byte	'CAPSV',0
	.word	20169
	.byte	4,2,35,84,13
	.byte	'reserved_58',0
	.word	20209
	.byte	144,1,2,35,88,13
	.byte	'OCS',0
	.word	20340
	.byte	4,3,35,232,1,13
	.byte	'KRSTCLR',0
	.word	20446
	.byte	4,3,35,236,1,13
	.byte	'KRST1',0
	.word	20550
	.byte	4,3,35,240,1,13
	.byte	'KRST0',0
	.word	20673
	.byte	4,3,35,244,1,13
	.byte	'ACCEN1',0
	.word	20762
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	21331
	.byte	4,3,35,252,1,0,14
	.word	21371
	.byte	3
	.word	21791
	.byte	8
	.byte	'IfxStm_get',0,3,15,162,4,19
	.word	357
	.byte	1,1,5
	.byte	'stm',0,15,162,4,39
	.word	21796
	.byte	6,0,8
	.byte	'IfxStm_getFrequency',0,3,15,179,4,20
	.word	271
	.byte	1,1,5
	.byte	'stm',0,15,179,4,49
	.word	21796
	.byte	19,6,6,0,0,8
	.byte	'IfxStm_getLower',0,3,15,190,4,19
	.word	10311
	.byte	1,1,5
	.byte	'stm',0,15,190,4,44
	.word	21796
	.byte	6,0,8
	.byte	'disableInterrupts',0,3,17,108,20
	.word	650
	.byte	1,1,19,6,0,0,4
	.byte	'restoreInterrupts',0,3,17,142,1,17,1,1,5
	.byte	'enabled',0,17,142,1,43
	.word	650
	.byte	19,6,0,0,7
	.byte	'long long int',0,8,5,8
	.byte	'getDeadLine',0,3,17,164,2,25
	.word	22012
	.byte	1,1,5
	.byte	'timeout',0,17,164,2,50
	.word	22012
	.byte	19,6,0,0,8
	.byte	'isDeadLine',0,3,17,211,2,20
	.word	650
	.byte	1,1,5
	.byte	'deadLine',0,17,211,2,44
	.word	22012
	.byte	19,6,0,0,8
	.byte	'now',0,3,17,221,1,25
	.word	22012
	.byte	1,1,19,6,6,6,0,0,8
	.byte	'nowWithoutCriticalSection',0,3,17,240,1,25
	.word	22012
	.byte	1,1,19,6,0,0,7
	.byte	'char',0,1,6,21
	.word	22183
.L114:
	.byte	3
	.word	22191
	.byte	22
	.byte	'strlen',0,18,52,17
	.word	475
	.byte	1,1,1,1,23,18,52,39
	.word	22196
	.byte	0,3
	.word	22183
	.byte	24
	.byte	'debug_assert_handler',0,19,112,9,1,1,1,1,5
	.byte	'pass',0,19,112,47
	.word	650
	.byte	5
	.byte	'file',0,19,112,59
	.word	22229
	.byte	5
	.byte	'line',0,19,112,69
	.word	491
	.byte	0,17,20,78,9,1,18
	.byte	'FIFO_DATA_8BIT',0,0,18
	.byte	'FIFO_DATA_16BIT',0,1,18
	.byte	'FIFO_DATA_32BIT',0,2,0
.L138:
	.byte	20,20,85,9,24,13
	.byte	'execution',0
	.word	650
	.byte	1,2,35,0,13
	.byte	'type',0
	.word	22303
	.byte	1,2,35,1,13
	.byte	'buffer',0
	.word	389
	.byte	4,2,35,4,13
	.byte	'head',0
	.word	10311
	.byte	4,2,35,8,13
	.byte	'end',0
	.word	10311
	.byte	4,2,35,12,13
	.byte	'size',0
	.word	10311
	.byte	4,2,35,16,13
	.byte	'max',0
	.word	10311
	.byte	4,2,35,20,0,3
	.word	22362
	.byte	22
	.byte	'fifo_used',0,20,97,17
	.word	10311
	.byte	1,1,1,1,5
	.byte	'fifo',0,20,97,55
	.word	22471
	.byte	0,17,20,42,9,1,18
	.byte	'FIFO_SUCCESS',0,0,18
	.byte	'FIFO_RESET_UNDO',0,1,18
	.byte	'FIFO_CLEAR_UNDO',0,2,18
	.byte	'FIFO_BUFFER_NULL',0,3,18
	.byte	'FIFO_WRITE_UNDO',0,4,18
	.byte	'FIFO_SPACE_NO_ENOUGH',0,5,18
	.byte	'FIFO_READ_UNDO',0,6,18
	.byte	'FIFO_DATA_NO_ENOUGH',0,7,0,22
	.byte	'fifo_write_buffer',0,20,100,17
	.word	22512
	.byte	1,1,1,1,5
	.byte	'fifo',0,20,100,55
	.word	22471
	.byte	5
	.byte	'dat',0,20,100,67
	.word	389
	.byte	5
	.byte	'length',0,20,100,79
	.word	10311
	.byte	0,3
	.word	10311
	.byte	17,20,72,9,1,18
	.byte	'FIFO_READ_AND_CLEAN',0,0,18
	.byte	'FIFO_READ_ONLY',0,1,0,22
	.byte	'fifo_read_buffer',0,20,102,17
	.word	22512
	.byte	1,1,1,1,5
	.byte	'fifo',0,20,102,55
	.word	22471
	.byte	5
	.byte	'dat',0,20,102,67
	.word	389
	.byte	5
	.byte	'length',0,20,102,80
	.word	22739
	.byte	5
	.byte	'flag',0,20,102,108
	.word	22744
	.byte	0,22
	.byte	'fifo_init',0,20,105,17
	.word	22512
	.byte	1,1,1,1,5
	.byte	'fifo',0,20,105,55
	.word	22471
	.byte	5
	.byte	'type',0,20,105,81
	.word	22303
	.byte	5
	.byte	'buffer_addr',0,20,105,93
	.word	389
	.byte	5
	.byte	'size',0,20,105,113
	.word	10311
	.byte	0,24
	.byte	'system_delay_ms',0,21,46,9,1,1,1,1,5
	.byte	'time',0,21,46,45
	.word	10311
	.byte	0,25
	.word	219
	.byte	26
	.word	245
	.byte	6,0,25
	.word	280
	.byte	26
	.word	312
	.byte	6,0,25
	.word	325
	.byte	6,0,25
	.word	394
	.byte	26
	.word	413
	.byte	6,0,25
	.word	429
	.byte	26
	.word	444
	.byte	26
	.word	458
	.byte	6,0,25
	.word	1237
	.byte	26
	.word	1277
	.byte	26
	.word	1295
	.byte	6,0,25
	.word	1315
	.byte	26
	.word	1353
	.byte	26
	.word	1371
	.byte	6,0,25
	.word	1391
	.byte	26
	.word	1442
	.byte	6,0,25
	.word	9345
	.byte	26
	.word	9377
	.byte	26
	.word	9391
	.byte	26
	.word	9409
	.byte	6,0,25
	.word	9712
	.byte	26
	.word	9745
	.byte	26
	.word	9759
	.byte	26
	.word	9777
	.byte	26
	.word	9791
	.byte	6,0,25
	.word	9911
	.byte	26
	.word	9939
	.byte	26
	.word	9953
	.byte	26
	.word	9971
	.byte	6,0,17,22,42,9,2,18
	.byte	'P00_0',0,0,18
	.byte	'P00_1',0,1,18
	.byte	'P00_2',0,2,18
	.byte	'P00_3',0,3,18
	.byte	'P00_4',0,4,18
	.byte	'P00_5',0,5,18
	.byte	'P00_6',0,6,18
	.byte	'P00_7',0,7,18
	.byte	'P00_8',0,8,18
	.byte	'P00_9',0,9,18
	.byte	'P00_10',0,10,18
	.byte	'P00_11',0,11,18
	.byte	'P00_12',0,12,18
	.byte	'P00_13',0,13,18
	.byte	'P00_14',0,14,18
	.byte	'P00_15',0,15,18
	.byte	'P02_0',0,192,0,18
	.byte	'P02_1',0,193,0,18
	.byte	'P02_2',0,194,0,18
	.byte	'P02_3',0,195,0,18
	.byte	'P02_4',0,196,0,18
	.byte	'P02_5',0,197,0,18
	.byte	'P02_6',0,198,0,18
	.byte	'P02_7',0,199,0,18
	.byte	'P02_8',0,200,0,18
	.byte	'P02_9',0,201,0,18
	.byte	'P02_10',0,202,0,18
	.byte	'P02_11',0,203,0,18
	.byte	'P02_12',0,204,0,18
	.byte	'P02_13',0,205,0,18
	.byte	'P02_14',0,206,0,18
	.byte	'P02_15',0,207,0,18
	.byte	'P10_0',0,192,2,18
	.byte	'P10_1',0,193,2,18
	.byte	'P10_2',0,194,2,18
	.byte	'P10_3',0,195,2,18
	.byte	'P10_4',0,196,2,18
	.byte	'P10_5',0,197,2,18
	.byte	'P10_6',0,198,2,18
	.byte	'P10_7',0,199,2,18
	.byte	'P10_8',0,200,2,18
	.byte	'P10_9',0,201,2,18
	.byte	'P10_10',0,202,2,18
	.byte	'P10_11',0,203,2,18
	.byte	'P10_12',0,204,2,18
	.byte	'P10_13',0,205,2,18
	.byte	'P10_14',0,206,2,18
	.byte	'P10_15',0,207,2,18
	.byte	'P11_0',0,224,2,18
	.byte	'P11_1',0,225,2,18
	.byte	'P11_2',0,226,2,18
	.byte	'P11_3',0,227,2,18
	.byte	'P11_4',0,228,2,18
	.byte	'P11_5',0,229,2,18
	.byte	'P11_6',0,230,2,18
	.byte	'P11_7',0,231,2,18
	.byte	'P11_8',0,232,2,18
	.byte	'P11_9',0,233,2,18
	.byte	'P11_10',0,234,2,18
	.byte	'P11_11',0,235,2,18
	.byte	'P11_12',0,236,2,18
	.byte	'P11_13',0,237,2,18
	.byte	'P11_14',0,238,2,18
	.byte	'P11_15',0,239,2,18
	.byte	'P13_0',0,160,3,18
	.byte	'P13_1',0,161,3,18
	.byte	'P13_2',0,162,3,18
	.byte	'P13_3',0,163,3,18
	.byte	'P13_4',0,164,3,18
	.byte	'P13_5',0,165,3,18
	.byte	'P13_6',0,166,3,18
	.byte	'P13_7',0,167,3,18
	.byte	'P13_8',0,168,3,18
	.byte	'P13_9',0,169,3,18
	.byte	'P13_10',0,170,3,18
	.byte	'P13_11',0,171,3,18
	.byte	'P13_12',0,172,3,18
	.byte	'P13_13',0,173,3,18
	.byte	'P13_14',0,174,3,18
	.byte	'P13_15',0,175,3,18
	.byte	'P14_0',0,192,3,18
	.byte	'P14_1',0,193,3,18
	.byte	'P14_2',0,194,3,18
	.byte	'P14_3',0,195,3,18
	.byte	'P14_4',0,196,3,18
	.byte	'P14_5',0,197,3,18
	.byte	'P14_6',0,198,3,18
	.byte	'P14_7',0,199,3,18
	.byte	'P14_8',0,200,3,18
	.byte	'P14_9',0,201,3,18
	.byte	'P14_10',0,202,3,18
	.byte	'P14_11',0,203,3,18
	.byte	'P14_12',0,204,3,18
	.byte	'P14_13',0,205,3,18
	.byte	'P14_14',0,206,3,18
	.byte	'P14_15',0,207,3,18
	.byte	'P15_0',0,224,3,18
	.byte	'P15_1',0,225,3,18
	.byte	'P15_2',0,226,3,18
	.byte	'P15_3',0,227,3,18
	.byte	'P15_4',0,228,3,18
	.byte	'P15_5',0,229,3,18
	.byte	'P15_6',0,230,3,18
	.byte	'P15_7',0,231,3,18
	.byte	'P15_8',0,232,3,18
	.byte	'P15_9',0,233,3,18
	.byte	'P15_10',0,234,3,18
	.byte	'P15_11',0,235,3,18
	.byte	'P15_12',0,236,3,18
	.byte	'P15_13',0,237,3,18
	.byte	'P15_14',0,238,3,18
	.byte	'P15_15',0,239,3,18
	.byte	'P20_0',0,128,5,18
	.byte	'P20_1',0,129,5,18
	.byte	'P20_2',0,130,5,18
	.byte	'P20_3',0,131,5,18
	.byte	'P20_4',0,132,5,18
	.byte	'P20_5',0,133,5,18
	.byte	'P20_6',0,134,5,18
	.byte	'P20_7',0,135,5,18
	.byte	'P20_8',0,136,5,18
	.byte	'P20_9',0,137,5,18
	.byte	'P20_10',0,138,5,18
	.byte	'P20_11',0,139,5,18
	.byte	'P20_12',0,140,5,18
	.byte	'P20_13',0,141,5,18
	.byte	'P20_14',0,142,5,18
	.byte	'P20_15',0,143,5,18
	.byte	'P21_0',0,160,5,18
	.byte	'P21_1',0,161,5,18
	.byte	'P21_2',0,162,5,18
	.byte	'P21_3',0,163,5,18
	.byte	'P21_4',0,164,5,18
	.byte	'P21_5',0,165,5,18
	.byte	'P21_6',0,166,5,18
	.byte	'P21_7',0,167,5,18
	.byte	'P21_8',0,168,5,18
	.byte	'P21_9',0,169,5,18
	.byte	'P21_10',0,170,5,18
	.byte	'P21_11',0,171,5,18
	.byte	'P21_12',0,172,5,18
	.byte	'P21_13',0,173,5,18
	.byte	'P21_14',0,174,5,18
	.byte	'P21_15',0,175,5,18
	.byte	'P22_0',0,192,5,18
	.byte	'P22_1',0,193,5,18
	.byte	'P22_2',0,194,5,18
	.byte	'P22_3',0,195,5,18
	.byte	'P22_4',0,196,5,18
	.byte	'P22_5',0,197,5,18
	.byte	'P22_6',0,198,5,18
	.byte	'P22_7',0,199,5,18
	.byte	'P22_8',0,200,5,18
	.byte	'P22_9',0,201,5,18
	.byte	'P22_10',0,202,5,18
	.byte	'P22_11',0,203,5,18
	.byte	'P22_12',0,204,5,18
	.byte	'P22_13',0,205,5,18
	.byte	'P22_14',0,206,5,18
	.byte	'P22_15',0,207,5,18
	.byte	'P23_0',0,224,5,18
	.byte	'P23_1',0,225,5,18
	.byte	'P23_2',0,226,5,18
	.byte	'P23_3',0,227,5,18
	.byte	'P23_4',0,228,5,18
	.byte	'P23_5',0,229,5,18
	.byte	'P23_6',0,230,5,18
	.byte	'P23_7',0,231,5,18
	.byte	'P23_8',0,232,5,18
	.byte	'P23_9',0,233,5,18
	.byte	'P23_10',0,234,5,18
	.byte	'P23_11',0,235,5,18
	.byte	'P23_12',0,236,5,18
	.byte	'P23_13',0,237,5,18
	.byte	'P23_14',0,238,5,18
	.byte	'P23_15',0,239,5,18
	.byte	'P32_0',0,128,8,18
	.byte	'P32_1',0,129,8,18
	.byte	'P32_2',0,130,8,18
	.byte	'P32_3',0,131,8,18
	.byte	'P32_4',0,132,8,18
	.byte	'P32_5',0,133,8,18
	.byte	'P32_6',0,134,8,18
	.byte	'P32_7',0,135,8,18
	.byte	'P32_8',0,136,8,18
	.byte	'P32_9',0,137,8,18
	.byte	'P32_10',0,138,8,18
	.byte	'P32_11',0,139,8,18
	.byte	'P32_12',0,140,8,18
	.byte	'P32_13',0,141,8,18
	.byte	'P32_14',0,142,8,18
	.byte	'P32_15',0,143,8,18
	.byte	'P33_0',0,160,8,18
	.byte	'P33_1',0,161,8,18
	.byte	'P33_2',0,162,8,18
	.byte	'P33_3',0,163,8,18
	.byte	'P33_4',0,164,8,18
	.byte	'P33_5',0,165,8,18
	.byte	'P33_6',0,166,8,18
	.byte	'P33_7',0,167,8,18
	.byte	'P33_8',0,168,8,18
	.byte	'P33_9',0,169,8,18
	.byte	'P33_10',0,170,8,18
	.byte	'P33_11',0,171,8,18
	.byte	'P33_12',0,172,8,18
	.byte	'P33_13',0,173,8,18
	.byte	'P33_14',0,174,8,18
	.byte	'P33_15',0,175,8,0,24
	.byte	'gpio_set_level',0,22,139,1,7,1,1,1,1,5
	.byte	'pin',0,22,139,1,40
	.word	23169
	.byte	5
	.byte	'dat',0,22,139,1,51
	.word	650
	.byte	0,22
	.byte	'gpio_get_level',0,22,140,1,7
	.word	650
	.byte	1,1,1,1,5
	.byte	'pin',0,22,140,1,40
	.word	23169
	.byte	0,24
	.byte	'gpio_toggle_level',0,22,141,1,7,1,1,1,1,5
	.byte	'pin',0,22,141,1,40
	.word	23169
	.byte	0,17,22,91,9,1,18
	.byte	'GPI',0,0,18
	.byte	'GPO',0,1,0,17,22,103,9,1,18
	.byte	'GPI_FLOATING_IN',0,0,18
	.byte	'GPI_PULL_UP',0,1,18
	.byte	'GPI_PULL_DOWN',0,2,18
	.byte	'GPO_PUSH_PULL',0,3,18
	.byte	'GPO_OPEN_DTAIN',0,4,0,24
	.byte	'gpio_init',0,22,143,1,7,1,1,1,1,5
	.byte	'pin',0,22,143,1,40
	.word	23169
	.byte	5
	.byte	'dir',0,22,143,1,59
	.word	25243
	.byte	5
	.byte	'dat',0,22,143,1,70
	.word	650
	.byte	5
	.byte	'pinconf',0,22,143,1,90
	.word	25261
	.byte	0,25
	.word	10068
	.byte	6,0,25
	.word	10102
	.byte	6,0,25
	.word	10144
	.byte	19,27
	.word	10102
	.byte	28
	.word	10142
	.byte	0,6,0,0,25
	.word	10185
	.byte	6,0,25
	.word	10219
	.byte	6,0,25
	.word	10259
	.byte	26
	.word	10292
	.byte	6,0,25
	.word	10332
	.byte	26
	.word	10373
	.byte	6,0,25
	.word	10392
	.byte	26
	.word	10447
	.byte	6,0,25
	.word	10466
	.byte	26
	.word	10506
	.byte	26
	.word	10523
	.byte	19,6,0,0,25
	.word	10542
	.byte	6,0,25
	.word	10921
	.byte	26
	.word	10949
	.byte	6,0,25
	.word	17501
	.byte	26
	.word	17529
	.byte	26
	.word	17545
	.byte	6,0,25
	.word	17685
	.byte	26
	.word	17715
	.byte	26
	.word	17731
	.byte	6,0,25
	.word	17978
	.byte	26
	.word	18007
	.byte	26
	.word	18023
	.byte	6,0,25
	.word	18319
	.byte	26
	.word	18350
	.byte	6,0,25
	.word	21801
	.byte	26
	.word	21824
	.byte	6,0,25
	.word	21839
	.byte	26
	.word	21871
	.byte	19,19,27
	.word	10542
	.byte	28
	.word	10580
	.byte	0,0,6,0,0,25
	.word	21889
	.byte	26
	.word	21917
	.byte	6,0,25
	.word	21932
	.byte	19,27
	.word	10144
	.byte	29
	.word	10181
	.byte	27
	.word	10102
	.byte	28
	.word	10142
	.byte	0,28
	.word	10182
	.byte	0,0,6,0,0,25
	.word	21965
	.byte	26
	.word	21991
	.byte	19,27
	.word	10259
	.byte	26
	.word	10292
	.byte	28
	.word	10309
	.byte	0,6,0,0,25
	.word	22029
	.byte	26
	.word	22053
	.byte	19,27
	.word	22119
	.byte	29
	.word	22135
	.byte	27
	.word	21932
	.byte	29
	.word	21961
	.byte	27
	.word	10144
	.byte	29
	.word	10181
	.byte	27
	.word	10102
	.byte	28
	.word	10142
	.byte	0,28
	.word	10182
	.byte	0,0,28
	.word	21962
	.byte	0,0,28
	.word	22136
	.byte	27
	.word	21965
	.byte	26
	.word	21991
	.byte	29
	.word	22008
	.byte	27
	.word	10259
	.byte	26
	.word	10292
	.byte	28
	.word	10309
	.byte	0,28
	.word	22009
	.byte	0,0,28
	.word	22137
	.byte	27
	.word	21801
	.byte	26
	.word	21824
	.byte	28
	.word	21837
	.byte	0,28
	.word	22138
	.byte	0,0,6,0,0,25
	.word	22074
	.byte	26
	.word	22097
	.byte	19,27
	.word	22119
	.byte	29
	.word	22135
	.byte	27
	.word	21932
	.byte	29
	.word	21961
	.byte	27
	.word	10144
	.byte	29
	.word	10181
	.byte	27
	.word	10102
	.byte	28
	.word	10142
	.byte	0,28
	.word	10182
	.byte	0,0,28
	.word	21962
	.byte	0,0,28
	.word	22136
	.byte	27
	.word	21965
	.byte	26
	.word	21991
	.byte	29
	.word	22008
	.byte	27
	.word	10259
	.byte	26
	.word	10292
	.byte	28
	.word	10309
	.byte	0,28
	.word	22009
	.byte	0,0,28
	.word	22137
	.byte	27
	.word	21801
	.byte	26
	.word	21824
	.byte	28
	.word	21837
	.byte	0,28
	.word	22138
	.byte	0,0,6,0,0,25
	.word	22119
	.byte	19,27
	.word	21932
	.byte	29
	.word	21961
	.byte	27
	.word	10144
	.byte	29
	.word	10181
	.byte	27
	.word	10102
	.byte	28
	.word	10142
	.byte	0,28
	.word	10182
	.byte	0,0,28
	.word	21962
	.byte	0,0,6,27
	.word	21965
	.byte	26
	.word	21991
	.byte	29
	.word	22008
	.byte	27
	.word	10259
	.byte	26
	.word	10292
	.byte	28
	.word	10309
	.byte	0,28
	.word	22009
	.byte	0,0,6,27
	.word	21801
	.byte	26
	.word	21824
	.byte	28
	.word	21837
	.byte	0,6,0,0,25
	.word	22141
	.byte	19,27
	.word	21801
	.byte	26
	.word	21824
	.byte	28
	.word	21837
	.byte	0,6,0,0,17,23,103,9,1,18
	.byte	'UART_0',0,0,18
	.byte	'UART_1',0,1,18
	.byte	'UART_2',0,2,18
	.byte	'UART_3',0,3,0,21
	.word	650
	.byte	24
	.byte	'uart_write_byte',0,23,118,9,1,1,1,1,5
	.byte	'uartn',0,23,118,62
	.word	26142
	.byte	5
	.byte	'dat',0,23,118,81
	.word	26184
	.byte	0,21
	.word	650
.L108:
	.byte	3
	.word	26240
	.byte	24
	.byte	'uart_write_buffer',0,23,119,9,1,1,1,1,5
	.byte	'uartn',0,23,119,62
	.word	26142
	.byte	5
	.byte	'buff',0,23,119,82
	.word	26245
	.byte	5
	.byte	'len',0,23,119,95
	.word	10311
	.byte	0
.L124:
	.byte	3
	.word	650
	.byte	22
	.byte	'uart_query_byte',0,23,123,9
	.word	650
	.byte	1,1,1,1,5
	.byte	'uartn',0,23,123,62
	.word	26142
	.byte	5
	.byte	'dat',0,23,123,76
	.word	26316
	.byte	0,24
	.byte	'uart_rx_interrupt',0,23,126,9,1,1,1,1,5
	.byte	'uartn',0,23,126,62
	.word	26142
	.byte	5
	.byte	'status',0,23,126,76
	.word	10311
	.byte	0,17,23,43,9,1,18
	.byte	'UART0_TX_P14_0',0,0,18
	.byte	'UART0_TX_P14_1',0,1,18
	.byte	'UART0_TX_P15_2',0,2,18
	.byte	'UART0_TX_P15_3',0,3,18
	.byte	'UART1_TX_P02_2',0,4,18
	.byte	'UART1_TX_P11_12',0,5,18
	.byte	'UART1_TX_P15_0',0,6,18
	.byte	'UART1_TX_P15_1',0,7,18
	.byte	'UART1_TX_P15_4',0,8,18
	.byte	'UART1_TX_P15_5',0,9,18
	.byte	'UART1_TX_P20_10',0,10,18
	.byte	'UART1_TX_P33_12',0,11,18
	.byte	'UART1_TX_P33_13',0,12,18
	.byte	'UART2_TX_P02_0',0,13,18
	.byte	'UART2_TX_P10_5',0,14,18
	.byte	'UART2_TX_P14_2',0,15,18
	.byte	'UART2_TX_P14_3',0,16,18
	.byte	'UART2_TX_P33_8',0,17,18
	.byte	'UART2_TX_P33_9',0,18,18
	.byte	'UART3_TX_P00_0',0,19,18
	.byte	'UART3_TX_P00_1',0,20,18
	.byte	'UART3_TX_P15_6',0,21,18
	.byte	'UART3_TX_P15_7',0,22,18
	.byte	'UART3_TX_P20_0',0,23,18
	.byte	'UART3_TX_P20_3',0,24,18
	.byte	'UART3_TX_P21_7',0,25,0,17,23,77,9,1,18
	.byte	'UART0_RX_P14_1',0,0,18
	.byte	'UART0_RX_P15_3',0,1,18
	.byte	'UART1_RX_P02_3',0,2,18
	.byte	'UART1_RX_P11_10',0,3,18
	.byte	'UART1_RX_P15_1',0,4,18
	.byte	'UART1_RX_P15_5',0,5,18
	.byte	'UART1_RX_P20_9',0,6,18
	.byte	'UART1_RX_P33_13',0,7,18
	.byte	'UART2_RX_P02_0',0,8,18
	.byte	'UART2_RX_P02_1',0,9,18
	.byte	'UART2_RX_P10_6',0,10,18
	.byte	'UART2_RX_P14_3',0,11,18
	.byte	'UART2_RX_P33_8',0,12,18
	.byte	'UART3_RX_P00_1',0,13,18
	.byte	'UART3_RX_P15_7',0,14,18
	.byte	'UART3_RX_P20_3',0,15,18
	.byte	'UART3_RX_P21_6',0,16,0,24
	.byte	'uart_init',0,23,129,1,9,1,1,1,1,5
	.byte	'uartn',0,23,129,1,62
	.word	26142
	.byte	5
	.byte	'baud',0,23,129,1,76
	.word	10311
	.byte	5
	.byte	'tx_pin',0,23,129,1,99
	.word	26432
	.byte	5
	.byte	'rx_pin',0,23,129,1,124
	.word	26884
	.byte	0,17,24,53,9,1,18
	.byte	'NO_WIRELESS',0,0,18
	.byte	'WIRELESS_UART',0,1,18
	.byte	'BLE6A20',0,2,18
	.byte	'BLUETOOTH_CH9141',0,3,18
	.byte	'WIFI_UART',0,4,18
	.byte	'RECEIVER_UART',0,5,0,30
	.byte	'callback_function',0,24,73,16
	.word	214
	.byte	24
	.byte	'set_wireless_type',0,24,91,8,1,1,1,1,5
	.byte	'type_set',0,24,91,53
	.word	27262
	.byte	5
	.byte	'wireless_callback',0,24,91,81
	.word	27355
	.byte	0,3
	.word	211
.L103:
	.byte	21
	.word	650
	.byte	31
	.byte	'__INDIRECT__',0,25,1,1,1,1,1,30
	.byte	'__wchar_t',0,25,1,1
	.word	18039
	.byte	30
	.byte	'__size_t',0,25,1,1
	.word	475
	.byte	30
	.byte	'__ptrdiff_t',0,25,1,1
	.word	491
	.byte	32,1,3
	.word	27536
	.byte	30
	.byte	'__codeptr',0,25,1,1
	.word	27538
	.byte	30
	.byte	'__intptr_t',0,25,1,1
	.word	491
	.byte	30
	.byte	'__uintptr_t',0,25,1,1
	.word	475
	.byte	30
	.byte	'size_t',0,26,31,25
	.word	475
	.byte	30
	.byte	'_iob_flag_t',0,26,82,25
	.word	667
	.byte	30
	.byte	'boolean',0,27,101,29
	.word	650
	.byte	30
	.byte	'uint8',0,27,105,29
	.word	650
	.byte	30
	.byte	'uint16',0,27,109,29
	.word	667
	.byte	30
	.byte	'uint32',0,27,113,29
	.word	10311
	.byte	30
	.byte	'uint64',0,27,118,29
	.word	357
	.byte	30
	.byte	'sint16',0,27,126,29
	.word	18039
	.byte	30
	.byte	'sint32',0,27,131,1,29
	.word	18052
	.byte	30
	.byte	'sint64',0,27,138,1,29
	.word	22012
	.byte	30
	.byte	'float32',0,27,167,1,29
	.word	271
	.byte	30
	.byte	'pvoid',0,28,57,28
	.word	389
	.byte	30
	.byte	'Ifx_TickTime',0,28,79,28
	.word	22012
	.byte	30
	.byte	'Ifx_SizeT',0,28,92,16
	.word	18039
	.byte	30
	.byte	'Ifx_Priority',0,28,103,16
	.word	667
	.byte	17,28,130,1,9,1,18
	.byte	'Ifx_RxSel_a',0,0,18
	.byte	'Ifx_RxSel_b',0,1,18
	.byte	'Ifx_RxSel_c',0,2,18
	.byte	'Ifx_RxSel_d',0,3,18
	.byte	'Ifx_RxSel_e',0,4,18
	.byte	'Ifx_RxSel_f',0,5,18
	.byte	'Ifx_RxSel_g',0,6,18
	.byte	'Ifx_RxSel_h',0,7,0,30
	.byte	'Ifx_RxSel',0,28,140,1,3
	.word	27848
	.byte	17,28,164,1,9,1,18
	.byte	'Ifx_DataBufferMode_normal',0,0,18
	.byte	'Ifx_DataBufferMode_timeStampSingle',0,1,0,30
	.byte	'Ifx_DataBufferMode',0,28,169,1,2
	.word	27986
	.byte	7
	.byte	'char',0,1,6,30
	.byte	'int8',0,29,54,29
	.word	28086
	.byte	30
	.byte	'int16',0,29,55,29
	.word	18039
	.byte	30
	.byte	'int32',0,29,56,29
	.word	491
	.byte	30
	.byte	'int64',0,29,57,29
	.word	22012
	.byte	30
	.byte	'fifo_state_enum',0,20,53,2
	.word	22512
	.byte	30
	.byte	'fifo_operation_enum',0,20,76,2
	.word	22744
	.byte	30
	.byte	'fifo_data_type_enum',0,20,83,2
	.word	22303
	.byte	30
	.byte	'fifo_struct',0,20,94,2
	.word	22362
	.byte	30
	.byte	'Ifx_P_ACCEN0_Bits',0,6,79,3
	.word	8040
	.byte	30
	.byte	'Ifx_P_ACCEN1_Bits',0,6,85,3
	.word	7953
	.byte	30
	.byte	'Ifx_P_ESR_Bits',0,6,107,3
	.word	4296
	.byte	30
	.byte	'Ifx_P_ID_Bits',0,6,115,3
	.word	2349
	.byte	30
	.byte	'Ifx_P_IN_Bits',0,6,137,1,3
	.word	3344
	.byte	30
	.byte	'Ifx_P_IOCR0_Bits',0,6,150,1,3
	.word	2477
	.byte	30
	.byte	'Ifx_P_IOCR12_Bits',0,6,163,1,3
	.word	3124
	.byte	30
	.byte	'Ifx_P_IOCR4_Bits',0,6,176,1,3
	.word	2692
	.byte	30
	.byte	'Ifx_P_IOCR8_Bits',0,6,189,1,3
	.word	2907
	.byte	30
	.byte	'Ifx_P_LPCR0_Bits',0,6,197,1,3
	.word	7312
	.byte	30
	.byte	'Ifx_P_LPCR1_Bits',0,6,205,1,3
	.word	7436
	.byte	30
	.byte	'Ifx_P_LPCR1_P21_Bits',0,6,215,1,3
	.word	7520
	.byte	30
	.byte	'Ifx_P_LPCR2_Bits',0,6,229,1,3
	.word	7700
	.byte	30
	.byte	'Ifx_P_OMCR0_Bits',0,6,240,1,3
	.word	5951
	.byte	30
	.byte	'Ifx_P_OMCR12_Bits',0,6,250,1,3
	.word	6475
	.byte	30
	.byte	'Ifx_P_OMCR4_Bits',0,6,133,2,3
	.word	6125
	.byte	30
	.byte	'Ifx_P_OMCR8_Bits',0,6,144,2,3
	.word	6299
	.byte	30
	.byte	'Ifx_P_OMCR_Bits',0,6,166,2,3
	.word	6964
	.byte	30
	.byte	'Ifx_P_OMR_Bits',0,6,203,2,3
	.word	1778
	.byte	30
	.byte	'Ifx_P_OMSR0_Bits',0,6,213,2,3
	.word	5288
	.byte	30
	.byte	'Ifx_P_OMSR12_Bits',0,6,224,2,3
	.word	5776
	.byte	30
	.byte	'Ifx_P_OMSR4_Bits',0,6,235,2,3
	.word	5435
	.byte	30
	.byte	'Ifx_P_OMSR8_Bits',0,6,246,2,3
	.word	5604
	.byte	30
	.byte	'Ifx_P_OMSR_Bits',0,6,140,3,3
	.word	6631
	.byte	30
	.byte	'Ifx_P_OUT_Bits',0,6,162,3,3
	.word	1462
	.byte	30
	.byte	'Ifx_P_PCSR_Bits',0,6,180,3,3
	.word	5002
	.byte	30
	.byte	'Ifx_P_PDISC_Bits',0,6,202,3,3
	.word	4636
	.byte	30
	.byte	'Ifx_P_PDR0_Bits',0,6,223,3,3
	.word	3667
	.byte	30
	.byte	'Ifx_P_PDR1_Bits',0,6,244,3,3
	.word	3971
	.byte	30
	.byte	'Ifx_P_ACCEN0',0,6,129,4,3
	.word	8567
	.byte	30
	.byte	'Ifx_P_ACCEN1',0,6,137,4,3
	.word	8000
	.byte	30
	.byte	'Ifx_P_ESR',0,6,145,4,3
	.word	4587
	.byte	30
	.byte	'Ifx_P_ID',0,6,153,4,3
	.word	2428
	.byte	30
	.byte	'Ifx_P_IN',0,6,161,4,3
	.word	3618
	.byte	30
	.byte	'Ifx_P_IOCR0',0,6,169,4,3
	.word	2652
	.byte	30
	.byte	'Ifx_P_IOCR12',0,6,177,4,3
	.word	3304
	.byte	30
	.byte	'Ifx_P_IOCR4',0,6,185,4,3
	.word	2867
	.byte	30
	.byte	'Ifx_P_IOCR8',0,6,193,4,3
	.word	3084
	.byte	30
	.byte	'Ifx_P_LPCR0',0,6,201,4,3
	.word	7396
	.byte	30
	.byte	'Ifx_P_LPCR1',0,6,210,4,3
	.word	7645
	.byte	30
	.byte	'Ifx_P_LPCR2',0,6,218,4,3
	.word	7904
	.byte	30
	.byte	'Ifx_P_OMCR',0,6,226,4,3
	.word	7272
	.byte	30
	.byte	'Ifx_P_OMCR0',0,6,234,4,3
	.word	6085
	.byte	30
	.byte	'Ifx_P_OMCR12',0,6,242,4,3
	.word	6591
	.byte	30
	.byte	'Ifx_P_OMCR4',0,6,250,4,3
	.word	6259
	.byte	30
	.byte	'Ifx_P_OMCR8',0,6,130,5,3
	.word	6435
	.byte	30
	.byte	'Ifx_P_OMR',0,6,138,5,3
	.word	2309
	.byte	30
	.byte	'Ifx_P_OMSR',0,6,146,5,3
	.word	6924
	.byte	30
	.byte	'Ifx_P_OMSR0',0,6,154,5,3
	.word	5395
	.byte	30
	.byte	'Ifx_P_OMSR12',0,6,162,5,3
	.word	5911
	.byte	30
	.byte	'Ifx_P_OMSR4',0,6,170,5,3
	.word	5564
	.byte	30
	.byte	'Ifx_P_OMSR8',0,6,178,5,3
	.word	5736
	.byte	30
	.byte	'Ifx_P_OUT',0,6,186,5,3
	.word	1738
	.byte	30
	.byte	'Ifx_P_PCSR',0,6,194,5,3
	.word	5248
	.byte	30
	.byte	'Ifx_P_PDISC',0,6,202,5,3
	.word	4962
	.byte	30
	.byte	'Ifx_P_PDR0',0,6,210,5,3
	.word	3931
	.byte	30
	.byte	'Ifx_P_PDR1',0,6,218,5,3
	.word	4247
	.byte	14
	.word	8607
	.byte	30
	.byte	'Ifx_P',0,6,139,6,3
	.word	29567
	.byte	17,30,236,10,9,1,18
	.byte	'IfxScu_CCUCON0_CLKSEL_fBack',0,0,18
	.byte	'IfxScu_CCUCON0_CLKSEL_fPll',0,1,0,30
	.byte	'IfxScu_CCUCON0_CLKSEL',0,30,240,10,3
	.word	29587
	.byte	17,30,250,10,9,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy16384',0,0,18
	.byte	'IfxScu_WDTCON1_IR_divBy256',0,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy64',0,2,0,30
	.byte	'IfxScu_WDTCON1_IR',0,30,255,10,3
	.word	29684
	.byte	10
	.byte	'_Ifx_SCU_ACCEN0_Bits',0,4,45,16,4,11
	.byte	'EN0',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	650
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	650
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	650
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	650
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	650
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	650
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	650
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	650
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	650
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	650
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	650
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	650
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	650
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	650
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	650
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	650
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	650
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	650
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	650
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	650
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	650
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	650
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	650
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	650
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	650
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	650
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	650
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	650
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_ACCEN0_Bits',0,4,79,3
	.word	29806
	.byte	10
	.byte	'_Ifx_SCU_ACCEN1_Bits',0,4,82,16,4,11
	.byte	'reserved_0',0,4
	.word	475
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_SCU_ACCEN1_Bits',0,4,85,3
	.word	30363
	.byte	10
	.byte	'_Ifx_SCU_ARSTDIS_Bits',0,4,88,16,4,11
	.byte	'STM0DIS',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'STM1DIS',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'STM2DIS',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	475
	.byte	29,0,2,35,0,0,30
	.byte	'Ifx_SCU_ARSTDIS_Bits',0,4,94,3
	.word	30440
	.byte	10
	.byte	'_Ifx_SCU_CCUCON0_Bits',0,4,97,16,4,11
	.byte	'BAUD1DIV',0,1
	.word	650
	.byte	4,4,2,35,0,11
	.byte	'BAUD2DIV',0,1
	.word	650
	.byte	4,0,2,35,0,11
	.byte	'SRIDIV',0,1
	.word	650
	.byte	4,4,2,35,1,11
	.byte	'LPDIV',0,1
	.word	650
	.byte	4,0,2,35,1,11
	.byte	'SPBDIV',0,1
	.word	650
	.byte	4,4,2,35,2,11
	.byte	'FSI2DIV',0,1
	.word	650
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	650
	.byte	2,0,2,35,2,11
	.byte	'FSIDIV',0,1
	.word	650
	.byte	2,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	650
	.byte	2,4,2,35,3,11
	.byte	'CLKSEL',0,1
	.word	650
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	650
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	650
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_CCUCON0_Bits',0,4,111,3
	.word	30576
	.byte	10
	.byte	'_Ifx_SCU_CCUCON1_Bits',0,4,114,16,4,11
	.byte	'CANDIV',0,1
	.word	650
	.byte	4,4,2,35,0,11
	.byte	'ERAYDIV',0,1
	.word	650
	.byte	4,0,2,35,0,11
	.byte	'STMDIV',0,1
	.word	650
	.byte	4,4,2,35,1,11
	.byte	'GTMDIV',0,1
	.word	650
	.byte	4,0,2,35,1,11
	.byte	'ETHDIV',0,1
	.word	650
	.byte	4,4,2,35,2,11
	.byte	'ASCLINFDIV',0,1
	.word	650
	.byte	4,0,2,35,2,11
	.byte	'ASCLINSDIV',0,1
	.word	650
	.byte	4,4,2,35,3,11
	.byte	'INSEL',0,1
	.word	650
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	650
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	650
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_CCUCON1_Bits',0,4,126,3
	.word	30856
	.byte	10
	.byte	'_Ifx_SCU_CCUCON2_Bits',0,4,129,1,16,4,11
	.byte	'BBBDIV',0,1
	.word	650
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	475
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	650
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	650
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_CCUCON2_Bits',0,4,135,1,3
	.word	31094
	.byte	10
	.byte	'_Ifx_SCU_CCUCON3_Bits',0,4,138,1,16,4,11
	.byte	'PLLDIV',0,1
	.word	650
	.byte	6,2,2,35,0,11
	.byte	'PLLSEL',0,1
	.word	650
	.byte	2,0,2,35,0,11
	.byte	'PLLERAYDIV',0,1
	.word	650
	.byte	6,2,2,35,1,11
	.byte	'PLLERAYSEL',0,1
	.word	650
	.byte	2,0,2,35,1,11
	.byte	'SRIDIV',0,1
	.word	650
	.byte	6,2,2,35,2,11
	.byte	'SRISEL',0,1
	.word	650
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	650
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	650
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	650
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	650
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_CCUCON3_Bits',0,4,150,1,3
	.word	31222
	.byte	10
	.byte	'_Ifx_SCU_CCUCON4_Bits',0,4,153,1,16,4,11
	.byte	'SPBDIV',0,1
	.word	650
	.byte	6,2,2,35,0,11
	.byte	'SPBSEL',0,1
	.word	650
	.byte	2,0,2,35,0,11
	.byte	'GTMDIV',0,1
	.word	650
	.byte	6,2,2,35,1,11
	.byte	'GTMSEL',0,1
	.word	650
	.byte	2,0,2,35,1,11
	.byte	'STMDIV',0,1
	.word	650
	.byte	6,2,2,35,2,11
	.byte	'STMSEL',0,1
	.word	650
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	650
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	650
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	650
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	650
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_CCUCON4_Bits',0,4,165,1,3
	.word	31465
	.byte	10
	.byte	'_Ifx_SCU_CCUCON5_Bits',0,4,168,1,16,4,11
	.byte	'MAXDIV',0,1
	.word	650
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	475
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	650
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	650
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_CCUCON5_Bits',0,4,174,1,3
	.word	31700
	.byte	10
	.byte	'_Ifx_SCU_CCUCON6_Bits',0,4,177,1,16,4,11
	.byte	'CPU0DIV',0,1
	.word	650
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	475
	.byte	26,0,2,35,0,0,30
	.byte	'Ifx_SCU_CCUCON6_Bits',0,4,181,1,3
	.word	31828
	.byte	10
	.byte	'_Ifx_SCU_CCUCON7_Bits',0,4,184,1,16,4,11
	.byte	'CPU1DIV',0,1
	.word	650
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	475
	.byte	26,0,2,35,0,0,30
	.byte	'Ifx_SCU_CCUCON7_Bits',0,4,188,1,3
	.word	31928
	.byte	10
	.byte	'_Ifx_SCU_CHIPID_Bits',0,4,191,1,16,4,11
	.byte	'CHREV',0,1
	.word	650
	.byte	6,2,2,35,0,11
	.byte	'CHTEC',0,1
	.word	650
	.byte	2,0,2,35,0,11
	.byte	'CHID',0,1
	.word	650
	.byte	8,0,2,35,1,11
	.byte	'EEA',0,1
	.word	650
	.byte	1,7,2,35,2,11
	.byte	'UCODE',0,1
	.word	650
	.byte	7,0,2,35,2,11
	.byte	'FSIZE',0,1
	.word	650
	.byte	4,4,2,35,3,11
	.byte	'SP',0,1
	.word	650
	.byte	2,2,2,35,3,11
	.byte	'SEC',0,1
	.word	650
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	650
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_CHIPID_Bits',0,4,202,1,3
	.word	32028
	.byte	10
	.byte	'_Ifx_SCU_DTSCON_Bits',0,4,205,1,16,4,11
	.byte	'PWD',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'START',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	650
	.byte	2,4,2,35,0,11
	.byte	'CAL',0,4
	.word	475
	.byte	20,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	650
	.byte	7,1,2,35,3,11
	.byte	'SLCK',0,1
	.word	650
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_DTSCON_Bits',0,4,213,1,3
	.word	32236
	.byte	10
	.byte	'_Ifx_SCU_DTSLIM_Bits',0,4,216,1,16,4,11
	.byte	'LOWER',0,2
	.word	667
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	650
	.byte	5,1,2,35,1,11
	.byte	'LLU',0,1
	.word	650
	.byte	1,0,2,35,1,11
	.byte	'UPPER',0,2
	.word	667
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	650
	.byte	4,2,2,35,3,11
	.byte	'SLCK',0,1
	.word	650
	.byte	1,1,2,35,3,11
	.byte	'UOF',0,1
	.word	650
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_DTSLIM_Bits',0,4,225,1,3
	.word	32401
	.byte	10
	.byte	'_Ifx_SCU_DTSSTAT_Bits',0,4,228,1,16,4,11
	.byte	'RESULT',0,2
	.word	667
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	650
	.byte	4,2,2,35,1,11
	.byte	'RDY',0,1
	.word	650
	.byte	1,1,2,35,1,11
	.byte	'BUSY',0,1
	.word	650
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	667
	.byte	16,0,2,35,2,0,30
	.byte	'Ifx_SCU_DTSSTAT_Bits',0,4,235,1,3
	.word	32584
	.byte	10
	.byte	'_Ifx_SCU_EICR_Bits',0,4,238,1,16,4,11
	.byte	'reserved_0',0,1
	.word	650
	.byte	4,4,2,35,0,11
	.byte	'EXIS0',0,1
	.word	650
	.byte	3,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	650
	.byte	1,0,2,35,0,11
	.byte	'FEN0',0,1
	.word	650
	.byte	1,7,2,35,1,11
	.byte	'REN0',0,1
	.word	650
	.byte	1,6,2,35,1,11
	.byte	'LDEN0',0,1
	.word	650
	.byte	1,5,2,35,1,11
	.byte	'EIEN0',0,1
	.word	650
	.byte	1,4,2,35,1,11
	.byte	'INP0',0,1
	.word	650
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	475
	.byte	5,12,2,35,0,11
	.byte	'EXIS1',0,1
	.word	650
	.byte	3,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	650
	.byte	1,0,2,35,2,11
	.byte	'FEN1',0,1
	.word	650
	.byte	1,7,2,35,3,11
	.byte	'REN1',0,1
	.word	650
	.byte	1,6,2,35,3,11
	.byte	'LDEN1',0,1
	.word	650
	.byte	1,5,2,35,3,11
	.byte	'EIEN1',0,1
	.word	650
	.byte	1,4,2,35,3,11
	.byte	'INP1',0,1
	.word	650
	.byte	3,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	650
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_EICR_Bits',0,4,129,2,3
	.word	32738
	.byte	10
	.byte	'_Ifx_SCU_EIFR_Bits',0,4,132,2,16,4,11
	.byte	'INTF0',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'INTF1',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'INTF2',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'INTF3',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'INTF4',0,1
	.word	650
	.byte	1,3,2,35,0,11
	.byte	'INTF5',0,1
	.word	650
	.byte	1,2,2,35,0,11
	.byte	'INTF6',0,1
	.word	650
	.byte	1,1,2,35,0,11
	.byte	'INTF7',0,1
	.word	650
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	475
	.byte	24,0,2,35,0,0,30
	.byte	'Ifx_SCU_EIFR_Bits',0,4,143,2,3
	.word	33102
	.byte	10
	.byte	'_Ifx_SCU_EMSR_Bits',0,4,146,2,16,4,11
	.byte	'POL',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'MODE',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'ENON',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'PSEL',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	667
	.byte	12,0,2,35,0,11
	.byte	'EMSF',0,1
	.word	650
	.byte	1,7,2,35,2,11
	.byte	'SEMSF',0,1
	.word	650
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	650
	.byte	6,0,2,35,2,11
	.byte	'EMSFM',0,1
	.word	650
	.byte	2,6,2,35,3,11
	.byte	'SEMSFM',0,1
	.word	650
	.byte	2,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	650
	.byte	4,0,2,35,3,0,30
	.byte	'Ifx_SCU_EMSR_Bits',0,4,159,2,3
	.word	33313
	.byte	10
	.byte	'_Ifx_SCU_ESRCFG_Bits',0,4,162,2,16,4,11
	.byte	'reserved_0',0,1
	.word	650
	.byte	7,1,2,35,0,11
	.byte	'EDCON',0,2
	.word	667
	.byte	2,7,2,35,0,11
	.byte	'reserved_9',0,4
	.word	475
	.byte	23,0,2,35,0,0,30
	.byte	'Ifx_SCU_ESRCFG_Bits',0,4,167,2,3
	.word	33565
	.byte	10
	.byte	'_Ifx_SCU_ESROCFG_Bits',0,4,170,2,16,4,11
	.byte	'ARI',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'ARC',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	475
	.byte	30,0,2,35,0,0,30
	.byte	'Ifx_SCU_ESROCFG_Bits',0,4,175,2,3
	.word	33683
	.byte	10
	.byte	'_Ifx_SCU_EVR13CON_Bits',0,4,178,2,16,4,11
	.byte	'reserved_0',0,4
	.word	475
	.byte	28,4,2,35,0,11
	.byte	'EVR13OFF',0,1
	.word	650
	.byte	1,3,2,35,3,11
	.byte	'BPEVR13OFF',0,1
	.word	650
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	650
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	650
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_EVR13CON_Bits',0,4,185,2,3
	.word	33794
	.byte	10
	.byte	'_Ifx_SCU_EVR33CON_Bits',0,4,188,2,16,4,11
	.byte	'reserved_0',0,4
	.word	475
	.byte	28,4,2,35,0,11
	.byte	'EVR33OFF',0,1
	.word	650
	.byte	1,3,2,35,3,11
	.byte	'BPEVR33OFF',0,1
	.word	650
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	650
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	650
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_EVR33CON_Bits',0,4,195,2,3
	.word	33957
	.byte	10
	.byte	'_Ifx_SCU_EVRADCSTAT_Bits',0,4,198,2,16,4,11
	.byte	'ADC13V',0,1
	.word	650
	.byte	8,0,2,35,0,11
	.byte	'ADC33V',0,1
	.word	650
	.byte	8,0,2,35,1,11
	.byte	'ADCSWDV',0,1
	.word	650
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	650
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	650
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_EVRADCSTAT_Bits',0,4,205,2,3
	.word	34120
	.byte	10
	.byte	'_Ifx_SCU_EVRDVSTAT_Bits',0,4,208,2,16,4,11
	.byte	'DVS13TRIM',0,1
	.word	650
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	650
	.byte	8,0,2,35,1,11
	.byte	'DVS33TRIM',0,1
	.word	650
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	650
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	650
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_EVRDVSTAT_Bits',0,4,215,2,3
	.word	34278
	.byte	10
	.byte	'_Ifx_SCU_EVRMONCTRL_Bits',0,4,218,2,16,4,11
	.byte	'EVR13OVMOD',0,1
	.word	650
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	650
	.byte	2,4,2,35,0,11
	.byte	'EVR13UVMOD',0,1
	.word	650
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	650
	.byte	2,0,2,35,0,11
	.byte	'EVR33OVMOD',0,1
	.word	650
	.byte	2,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	650
	.byte	2,4,2,35,1,11
	.byte	'EVR33UVMOD',0,1
	.word	650
	.byte	2,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	650
	.byte	2,0,2,35,1,11
	.byte	'SWDOVMOD',0,1
	.word	650
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	650
	.byte	2,4,2,35,2,11
	.byte	'SWDUVMOD',0,1
	.word	650
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,2
	.word	667
	.byte	10,0,2,35,2,0,30
	.byte	'Ifx_SCU_EVRMONCTRL_Bits',0,4,232,2,3
	.word	34443
	.byte	10
	.byte	'_Ifx_SCU_EVROSCCTRL_Bits',0,4,235,2,16,4,11
	.byte	'OSCTRIM',0,2
	.word	667
	.byte	10,6,2,35,0,11
	.byte	'OSCPTAT',0,1
	.word	650
	.byte	6,0,2,35,1,11
	.byte	'OSCANASEL',0,1
	.word	650
	.byte	4,4,2,35,2,11
	.byte	'HPBGTRIM',0,2
	.word	667
	.byte	7,5,2,35,2,11
	.byte	'HPBGCLKEN',0,1
	.word	650
	.byte	1,4,2,35,3,11
	.byte	'OSC3V3',0,1
	.word	650
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	650
	.byte	2,1,2,35,3,11
	.byte	'LCK',0,1
	.word	650
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_EVROSCCTRL_Bits',0,4,245,2,3
	.word	34772
	.byte	10
	.byte	'_Ifx_SCU_EVROVMON_Bits',0,4,248,2,16,4,11
	.byte	'EVR13OVVAL',0,1
	.word	650
	.byte	8,0,2,35,0,11
	.byte	'EVR33OVVAL',0,1
	.word	650
	.byte	8,0,2,35,1,11
	.byte	'SWDOVVAL',0,1
	.word	650
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	650
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	650
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_EVROVMON_Bits',0,4,255,2,3
	.word	34993
	.byte	10
	.byte	'_Ifx_SCU_EVRRSTCON_Bits',0,4,130,3,16,4,11
	.byte	'RST13TRIM',0,1
	.word	650
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	475
	.byte	16,8,2,35,0,11
	.byte	'RST13OFF',0,1
	.word	650
	.byte	1,7,2,35,3,11
	.byte	'BPRST13OFF',0,1
	.word	650
	.byte	1,6,2,35,3,11
	.byte	'RST33OFF',0,1
	.word	650
	.byte	1,5,2,35,3,11
	.byte	'BPRST33OFF',0,1
	.word	650
	.byte	1,4,2,35,3,11
	.byte	'RSTSWDOFF',0,1
	.word	650
	.byte	1,3,2,35,3,11
	.byte	'BPRSTSWDOFF',0,1
	.word	650
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	650
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	650
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_EVRRSTCON_Bits',0,4,142,3,3
	.word	35156
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF1_Bits',0,4,145,3,16,4,11
	.byte	'SD5P',0,1
	.word	650
	.byte	8,0,2,35,0,11
	.byte	'SD5I',0,1
	.word	650
	.byte	8,0,2,35,1,11
	.byte	'SD5D',0,1
	.word	650
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	650
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	650
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_EVRSDCOEFF1_Bits',0,4,152,3,3
	.word	35428
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF2_Bits',0,4,155,3,16,4,11
	.byte	'SD33P',0,1
	.word	650
	.byte	8,0,2,35,0,11
	.byte	'SD33I',0,1
	.word	650
	.byte	8,0,2,35,1,11
	.byte	'SD33D',0,1
	.word	650
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	650
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	650
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_EVRSDCOEFF2_Bits',0,4,162,3,3
	.word	35581
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF3_Bits',0,4,165,3,16,4,11
	.byte	'CT5REG0',0,1
	.word	650
	.byte	8,0,2,35,0,11
	.byte	'CT5REG1',0,1
	.word	650
	.byte	8,0,2,35,1,11
	.byte	'CT5REG2',0,1
	.word	650
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	650
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	650
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_EVRSDCOEFF3_Bits',0,4,172,3,3
	.word	35737
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF4_Bits',0,4,175,3,16,4,11
	.byte	'CT5REG3',0,1
	.word	650
	.byte	8,0,2,35,0,11
	.byte	'CT5REG4',0,1
	.word	650
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	667
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	650
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_EVRSDCOEFF4_Bits',0,4,181,3,3
	.word	35899
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF5_Bits',0,4,184,3,16,4,11
	.byte	'CT33REG0',0,1
	.word	650
	.byte	8,0,2,35,0,11
	.byte	'CT33REG1',0,1
	.word	650
	.byte	8,0,2,35,1,11
	.byte	'CT33REG2',0,1
	.word	650
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	650
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	650
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_EVRSDCOEFF5_Bits',0,4,191,3,3
	.word	36042
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF6_Bits',0,4,194,3,16,4,11
	.byte	'CT33REG3',0,1
	.word	650
	.byte	8,0,2,35,0,11
	.byte	'CT33REG4',0,1
	.word	650
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	667
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	650
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_EVRSDCOEFF6_Bits',0,4,200,3,3
	.word	36207
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL1_Bits',0,4,203,3,16,4,11
	.byte	'SDFREQSPRD',0,2
	.word	667
	.byte	16,0,2,35,0,11
	.byte	'SDFREQ',0,1
	.word	650
	.byte	8,0,2,35,2,11
	.byte	'SDSTEP',0,1
	.word	650
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	650
	.byte	2,2,2,35,3,11
	.byte	'SDSAMPLE',0,1
	.word	650
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	650
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_EVRSDCTRL1_Bits',0,4,211,3,3
	.word	36352
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL2_Bits',0,4,214,3,16,4,11
	.byte	'DRVP',0,1
	.word	650
	.byte	8,0,2,35,0,11
	.byte	'SDMINMAXDC',0,1
	.word	650
	.byte	8,0,2,35,1,11
	.byte	'DRVN',0,1
	.word	650
	.byte	8,0,2,35,2,11
	.byte	'SDLUT',0,1
	.word	650
	.byte	6,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	650
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	650
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_EVRSDCTRL2_Bits',0,4,222,3,3
	.word	36533
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL3_Bits',0,4,225,3,16,4,11
	.byte	'SDPWMPRE',0,1
	.word	650
	.byte	8,0,2,35,0,11
	.byte	'SDPID',0,1
	.word	650
	.byte	8,0,2,35,1,11
	.byte	'SDVOKLVL',0,1
	.word	650
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	650
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	650
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_EVRSDCTRL3_Bits',0,4,232,3,3
	.word	36707
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL4_Bits',0,4,235,3,16,4,11
	.byte	'reserved_0',0,1
	.word	650
	.byte	8,0,2,35,0,11
	.byte	'SYNCDIV',0,1
	.word	650
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	475
	.byte	20,1,2,35,0,11
	.byte	'LCK',0,1
	.word	650
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_EVRSDCTRL4_Bits',0,4,241,3,3
	.word	36867
	.byte	10
	.byte	'_Ifx_SCU_EVRSTAT_Bits',0,4,244,3,16,4,11
	.byte	'EVR13',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'OV13',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'EVR33',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'OV33',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'OVSWD',0,1
	.word	650
	.byte	1,3,2,35,0,11
	.byte	'UV13',0,1
	.word	650
	.byte	1,2,2,35,0,11
	.byte	'UV33',0,1
	.word	650
	.byte	1,1,2,35,0,11
	.byte	'UVSWD',0,1
	.word	650
	.byte	1,0,2,35,0,11
	.byte	'EXTPASS13',0,1
	.word	650
	.byte	1,7,2,35,1,11
	.byte	'EXTPASS33',0,1
	.word	650
	.byte	1,6,2,35,1,11
	.byte	'BGPROK',0,1
	.word	650
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	475
	.byte	21,0,2,35,0,0,30
	.byte	'Ifx_SCU_EVRSTAT_Bits',0,4,130,4,3
	.word	37011
	.byte	10
	.byte	'_Ifx_SCU_EVRTRIM_Bits',0,4,133,4,16,4,11
	.byte	'EVR13TRIM',0,1
	.word	650
	.byte	8,0,2,35,0,11
	.byte	'SDVOUTSEL',0,1
	.word	650
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	667
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	650
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_EVRTRIM_Bits',0,4,139,4,3
	.word	37285
	.byte	10
	.byte	'_Ifx_SCU_EVRUVMON_Bits',0,4,142,4,16,4,11
	.byte	'EVR13UVVAL',0,1
	.word	650
	.byte	8,0,2,35,0,11
	.byte	'EVR33UVVAL',0,1
	.word	650
	.byte	8,0,2,35,1,11
	.byte	'SWDUVVAL',0,1
	.word	650
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	650
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	650
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_EVRUVMON_Bits',0,4,149,4,3
	.word	37424
	.byte	10
	.byte	'_Ifx_SCU_EXTCON_Bits',0,4,152,4,16,4,11
	.byte	'EN0',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'SEL0',0,1
	.word	650
	.byte	4,2,2,35,0,11
	.byte	'reserved_6',0,2
	.word	667
	.byte	10,0,2,35,0,11
	.byte	'EN1',0,1
	.word	650
	.byte	1,7,2,35,2,11
	.byte	'NSEL',0,1
	.word	650
	.byte	1,6,2,35,2,11
	.byte	'SEL1',0,1
	.word	650
	.byte	4,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	650
	.byte	2,0,2,35,2,11
	.byte	'DIV1',0,1
	.word	650
	.byte	8,0,2,35,3,0,30
	.byte	'Ifx_SCU_EXTCON_Bits',0,4,163,4,3
	.word	37587
	.byte	10
	.byte	'_Ifx_SCU_FDR_Bits',0,4,166,4,16,4,11
	.byte	'STEP',0,2
	.word	667
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	650
	.byte	4,2,2,35,1,11
	.byte	'DM',0,1
	.word	650
	.byte	2,0,2,35,1,11
	.byte	'RESULT',0,2
	.word	667
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	650
	.byte	5,1,2,35,3,11
	.byte	'DISCLK',0,1
	.word	650
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_FDR_Bits',0,4,174,4,3
	.word	37805
	.byte	10
	.byte	'_Ifx_SCU_FMR_Bits',0,4,177,4,16,4,11
	.byte	'FS0',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'FS1',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'FS2',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'FS3',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'FS4',0,1
	.word	650
	.byte	1,3,2,35,0,11
	.byte	'FS5',0,1
	.word	650
	.byte	1,2,2,35,0,11
	.byte	'FS6',0,1
	.word	650
	.byte	1,1,2,35,0,11
	.byte	'FS7',0,1
	.word	650
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	650
	.byte	8,0,2,35,1,11
	.byte	'FC0',0,1
	.word	650
	.byte	1,7,2,35,2,11
	.byte	'FC1',0,1
	.word	650
	.byte	1,6,2,35,2,11
	.byte	'FC2',0,1
	.word	650
	.byte	1,5,2,35,2,11
	.byte	'FC3',0,1
	.word	650
	.byte	1,4,2,35,2,11
	.byte	'FC4',0,1
	.word	650
	.byte	1,3,2,35,2,11
	.byte	'FC5',0,1
	.word	650
	.byte	1,2,2,35,2,11
	.byte	'FC6',0,1
	.word	650
	.byte	1,1,2,35,2,11
	.byte	'FC7',0,1
	.word	650
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	650
	.byte	8,0,2,35,3,0,30
	.byte	'Ifx_SCU_FMR_Bits',0,4,197,4,3
	.word	37968
	.byte	10
	.byte	'_Ifx_SCU_ID_Bits',0,4,200,4,16,4,11
	.byte	'MODREV',0,1
	.word	650
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	650
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	667
	.byte	16,0,2,35,2,0,30
	.byte	'Ifx_SCU_ID_Bits',0,4,205,4,3
	.word	38304
	.byte	10
	.byte	'_Ifx_SCU_IGCR_Bits',0,4,208,4,16,4,11
	.byte	'IPEN00',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'IPEN01',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'IPEN02',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'IPEN03',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'IPEN04',0,1
	.word	650
	.byte	1,3,2,35,0,11
	.byte	'IPEN05',0,1
	.word	650
	.byte	1,2,2,35,0,11
	.byte	'IPEN06',0,1
	.word	650
	.byte	1,1,2,35,0,11
	.byte	'IPEN07',0,1
	.word	650
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	650
	.byte	5,3,2,35,1,11
	.byte	'GEEN0',0,1
	.word	650
	.byte	1,2,2,35,1,11
	.byte	'IGP0',0,1
	.word	650
	.byte	2,0,2,35,1,11
	.byte	'IPEN10',0,1
	.word	650
	.byte	1,7,2,35,2,11
	.byte	'IPEN11',0,1
	.word	650
	.byte	1,6,2,35,2,11
	.byte	'IPEN12',0,1
	.word	650
	.byte	1,5,2,35,2,11
	.byte	'IPEN13',0,1
	.word	650
	.byte	1,4,2,35,2,11
	.byte	'IPEN14',0,1
	.word	650
	.byte	1,3,2,35,2,11
	.byte	'IPEN15',0,1
	.word	650
	.byte	1,2,2,35,2,11
	.byte	'IPEN16',0,1
	.word	650
	.byte	1,1,2,35,2,11
	.byte	'IPEN17',0,1
	.word	650
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	650
	.byte	5,3,2,35,3,11
	.byte	'GEEN1',0,1
	.word	650
	.byte	1,2,2,35,3,11
	.byte	'IGP1',0,1
	.word	650
	.byte	2,0,2,35,3,0,30
	.byte	'Ifx_SCU_IGCR_Bits',0,4,232,4,3
	.word	38411
	.byte	10
	.byte	'_Ifx_SCU_IN_Bits',0,4,235,4,16,4,11
	.byte	'P0',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	475
	.byte	30,0,2,35,0,0,30
	.byte	'Ifx_SCU_IN_Bits',0,4,240,4,3
	.word	38863
	.byte	10
	.byte	'_Ifx_SCU_IOCR_Bits',0,4,243,4,16,4,11
	.byte	'reserved_0',0,1
	.word	650
	.byte	4,4,2,35,0,11
	.byte	'PC0',0,1
	.word	650
	.byte	4,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	650
	.byte	4,4,2,35,1,11
	.byte	'PC1',0,1
	.word	650
	.byte	4,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	667
	.byte	16,0,2,35,2,0,30
	.byte	'Ifx_SCU_IOCR_Bits',0,4,250,4,3
	.word	38962
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL0_Bits',0,4,253,4,16,4,11
	.byte	'LBISTREQ',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'LBISTREQP',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'PATTERNS',0,2
	.word	667
	.byte	14,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	667
	.byte	16,0,2,35,2,0,30
	.byte	'Ifx_SCU_LBISTCTRL0_Bits',0,4,131,5,3
	.word	39112
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL1_Bits',0,4,134,5,16,4,11
	.byte	'SEED',0,4
	.word	475
	.byte	23,9,2,35,0,11
	.byte	'reserved_23',0,1
	.word	650
	.byte	1,0,2,35,2,11
	.byte	'SPLITSH',0,1
	.word	650
	.byte	3,5,2,35,3,11
	.byte	'BODY',0,1
	.word	650
	.byte	1,4,2,35,3,11
	.byte	'LBISTFREQU',0,1
	.word	650
	.byte	4,0,2,35,3,0,30
	.byte	'Ifx_SCU_LBISTCTRL1_Bits',0,4,141,5,3
	.word	39261
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL2_Bits',0,4,144,5,16,4,11
	.byte	'SIGNATURE',0,4
	.word	475
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	650
	.byte	7,1,2,35,3,11
	.byte	'LBISTDONE',0,1
	.word	650
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_LBISTCTRL2_Bits',0,4,149,5,3
	.word	39422
	.byte	10
	.byte	'_Ifx_SCU_LCLCON_Bits',0,4,152,5,16,4,11
	.byte	'reserved_0',0,2
	.word	667
	.byte	16,0,2,35,0,11
	.byte	'LS',0,1
	.word	650
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,2
	.word	667
	.byte	14,1,2,35,2,11
	.byte	'LSEN',0,1
	.word	650
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_LCLCON_Bits',0,4,158,5,3
	.word	39552
	.byte	10
	.byte	'_Ifx_SCU_LCLTEST_Bits',0,4,161,5,16,4,11
	.byte	'LCLT0',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'LCLT1',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	475
	.byte	30,0,2,35,0,0,30
	.byte	'Ifx_SCU_LCLTEST_Bits',0,4,166,5,3
	.word	39684
	.byte	10
	.byte	'_Ifx_SCU_MANID_Bits',0,4,169,5,16,4,11
	.byte	'DEPT',0,1
	.word	650
	.byte	5,3,2,35,0,11
	.byte	'MANUF',0,2
	.word	667
	.byte	11,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	667
	.byte	16,0,2,35,2,0,30
	.byte	'Ifx_SCU_MANID_Bits',0,4,174,5,3
	.word	39799
	.byte	10
	.byte	'_Ifx_SCU_OMR_Bits',0,4,177,5,16,4,11
	.byte	'PS0',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	667
	.byte	14,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	650
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	650
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	667
	.byte	14,0,2,35,2,0,30
	.byte	'Ifx_SCU_OMR_Bits',0,4,185,5,3
	.word	39910
	.byte	10
	.byte	'_Ifx_SCU_OSCCON_Bits',0,4,188,5,16,4,11
	.byte	'reserved_0',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'PLLLV',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'OSCRES',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'GAINSEL',0,1
	.word	650
	.byte	2,3,2,35,0,11
	.byte	'MODE',0,1
	.word	650
	.byte	2,1,2,35,0,11
	.byte	'SHBY',0,1
	.word	650
	.byte	1,0,2,35,0,11
	.byte	'PLLHV',0,1
	.word	650
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	650
	.byte	1,6,2,35,1,11
	.byte	'X1D',0,1
	.word	650
	.byte	1,5,2,35,1,11
	.byte	'X1DEN',0,1
	.word	650
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	650
	.byte	4,0,2,35,1,11
	.byte	'OSCVAL',0,1
	.word	650
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	650
	.byte	2,1,2,35,2,11
	.byte	'APREN',0,1
	.word	650
	.byte	1,0,2,35,2,11
	.byte	'CAP0EN',0,1
	.word	650
	.byte	1,7,2,35,3,11
	.byte	'CAP1EN',0,1
	.word	650
	.byte	1,6,2,35,3,11
	.byte	'CAP2EN',0,1
	.word	650
	.byte	1,5,2,35,3,11
	.byte	'CAP3EN',0,1
	.word	650
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	650
	.byte	4,0,2,35,3,0,30
	.byte	'Ifx_SCU_OSCCON_Bits',0,4,209,5,3
	.word	40068
	.byte	10
	.byte	'_Ifx_SCU_OUT_Bits',0,4,212,5,16,4,11
	.byte	'P0',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	475
	.byte	30,0,2,35,0,0,30
	.byte	'Ifx_SCU_OUT_Bits',0,4,217,5,3
	.word	40480
	.byte	10
	.byte	'_Ifx_SCU_OVCCON_Bits',0,4,220,5,16,4,11
	.byte	'CSEL0',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'CSEL1',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'CSEL2',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,2
	.word	667
	.byte	13,0,2,35,0,11
	.byte	'OVSTRT',0,1
	.word	650
	.byte	1,7,2,35,2,11
	.byte	'OVSTP',0,1
	.word	650
	.byte	1,6,2,35,2,11
	.byte	'DCINVAL',0,1
	.word	650
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	650
	.byte	5,0,2,35,2,11
	.byte	'OVCONF',0,1
	.word	650
	.byte	1,7,2,35,3,11
	.byte	'POVCONF',0,1
	.word	650
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	650
	.byte	6,0,2,35,3,0,30
	.byte	'Ifx_SCU_OVCCON_Bits',0,4,233,5,3
	.word	40581
	.byte	10
	.byte	'_Ifx_SCU_OVCENABLE_Bits',0,4,236,5,16,4,11
	.byte	'OVEN0',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'OVEN1',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'OVEN2',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	475
	.byte	29,0,2,35,0,0,30
	.byte	'Ifx_SCU_OVCENABLE_Bits',0,4,242,5,3
	.word	40848
	.byte	10
	.byte	'_Ifx_SCU_PDISC_Bits',0,4,245,5,16,4,11
	.byte	'PDIS0',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	475
	.byte	30,0,2,35,0,0,30
	.byte	'Ifx_SCU_PDISC_Bits',0,4,250,5,3
	.word	40984
	.byte	10
	.byte	'_Ifx_SCU_PDR_Bits',0,4,253,5,16,4,11
	.byte	'PD0',0,1
	.word	650
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	650
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	650
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	475
	.byte	24,0,2,35,0,0,30
	.byte	'Ifx_SCU_PDR_Bits',0,4,132,6,3
	.word	41095
	.byte	10
	.byte	'_Ifx_SCU_PDRR_Bits',0,4,135,6,16,4,11
	.byte	'PDR0',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'PDR1',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'PDR2',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'PDR3',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'PDR4',0,1
	.word	650
	.byte	1,3,2,35,0,11
	.byte	'PDR5',0,1
	.word	650
	.byte	1,2,2,35,0,11
	.byte	'PDR6',0,1
	.word	650
	.byte	1,1,2,35,0,11
	.byte	'PDR7',0,1
	.word	650
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	475
	.byte	24,0,2,35,0,0,30
	.byte	'Ifx_SCU_PDRR_Bits',0,4,146,6,3
	.word	41228
	.byte	10
	.byte	'_Ifx_SCU_PLLCON0_Bits',0,4,149,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'MODEN',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	650
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	650
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	650
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	667
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	650
	.byte	7,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	650
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	650
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	650
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	650
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	650
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	650
	.byte	4,0,2,35,3,0,30
	.byte	'Ifx_SCU_PLLCON0_Bits',0,4,166,6,3
	.word	41431
	.byte	10
	.byte	'_Ifx_SCU_PLLCON1_Bits',0,4,169,6,16,4,11
	.byte	'K2DIV',0,1
	.word	650
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	650
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	650
	.byte	7,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	650
	.byte	1,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	650
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	667
	.byte	9,0,2,35,2,0,30
	.byte	'Ifx_SCU_PLLCON1_Bits',0,4,177,6,3
	.word	41787
	.byte	10
	.byte	'_Ifx_SCU_PLLCON2_Bits',0,4,180,6,16,4,11
	.byte	'MODCFG',0,2
	.word	667
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	667
	.byte	16,0,2,35,2,0,30
	.byte	'Ifx_SCU_PLLCON2_Bits',0,4,184,6,3
	.word	41965
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON0_Bits',0,4,187,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	650
	.byte	2,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	650
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	650
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	650
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	667
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	650
	.byte	5,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	650
	.byte	2,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	650
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	650
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	650
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	650
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	650
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	650
	.byte	4,0,2,35,3,0,30
	.byte	'Ifx_SCU_PLLERAYCON0_Bits',0,4,204,6,3
	.word	42065
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON1_Bits',0,4,207,6,16,4,11
	.byte	'K2DIV',0,1
	.word	650
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	650
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	650
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	650
	.byte	4,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	650
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	667
	.byte	9,0,2,35,2,0,30
	.byte	'Ifx_SCU_PLLERAYCON1_Bits',0,4,215,6,3
	.word	42435
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYSTAT_Bits',0,4,218,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'PWDSTAT',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	650
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	650
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	475
	.byte	26,0,2,35,0,0,30
	.byte	'Ifx_SCU_PLLERAYSTAT_Bits',0,4,227,6,3
	.word	42621
	.byte	10
	.byte	'_Ifx_SCU_PLLSTAT_Bits',0,4,230,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	650
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	650
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	650
	.byte	1,1,2,35,0,11
	.byte	'MODRUN',0,1
	.word	650
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	475
	.byte	24,0,2,35,0,0,30
	.byte	'Ifx_SCU_PLLSTAT_Bits',0,4,241,6,3
	.word	42819
	.byte	10
	.byte	'_Ifx_SCU_PMCSR_Bits',0,4,244,6,16,4,11
	.byte	'REQSLP',0,1
	.word	650
	.byte	2,6,2,35,0,11
	.byte	'SMUSLP',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	650
	.byte	5,0,2,35,0,11
	.byte	'PMST',0,1
	.word	650
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	475
	.byte	21,0,2,35,0,0,30
	.byte	'Ifx_SCU_PMCSR_Bits',0,4,251,6,3
	.word	43052
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR0_Bits',0,4,254,6,16,4,11
	.byte	'reserved_0',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'ESR1WKEN',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'PINAWKEN',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'PINBWKEN',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'ESR0DFEN',0,1
	.word	650
	.byte	1,3,2,35,0,11
	.byte	'ESR0EDCON',0,1
	.word	650
	.byte	2,1,2,35,0,11
	.byte	'ESR1DFEN',0,1
	.word	650
	.byte	1,0,2,35,0,11
	.byte	'ESR1EDCON',0,1
	.word	650
	.byte	2,6,2,35,1,11
	.byte	'PINADFEN',0,1
	.word	650
	.byte	1,5,2,35,1,11
	.byte	'PINAEDCON',0,1
	.word	650
	.byte	2,3,2,35,1,11
	.byte	'PINBDFEN',0,1
	.word	650
	.byte	1,2,2,35,1,11
	.byte	'PINBEDCON',0,1
	.word	650
	.byte	2,0,2,35,1,11
	.byte	'SCREN',0,1
	.word	650
	.byte	1,7,2,35,2,11
	.byte	'STBYRAMSEL',0,1
	.word	650
	.byte	2,5,2,35,2,11
	.byte	'SCRCLKSEL',0,1
	.word	650
	.byte	1,4,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	650
	.byte	1,3,2,35,2,11
	.byte	'TRISTEN',0,1
	.word	650
	.byte	1,2,2,35,2,11
	.byte	'TRISTREQ',0,1
	.word	650
	.byte	1,1,2,35,2,11
	.byte	'PORSTDF',0,1
	.word	650
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	650
	.byte	1,7,2,35,3,11
	.byte	'DCDCSYNC',0,1
	.word	650
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	650
	.byte	3,3,2,35,3,11
	.byte	'ESR0TRIST',0,1
	.word	650
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	650
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	650
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_PMSWCR0_Bits',0,4,153,7,3
	.word	43204
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR1_Bits',0,4,156,7,16,4,11
	.byte	'SCRSTEN',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'SCRSTREQ',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	650
	.byte	6,0,2,35,0,11
	.byte	'CPUIDLSEL',0,1
	.word	650
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	650
	.byte	1,4,2,35,1,11
	.byte	'IRADIS',0,1
	.word	650
	.byte	1,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	650
	.byte	3,0,2,35,1,11
	.byte	'SCRCFG',0,1
	.word	650
	.byte	8,0,2,35,2,11
	.byte	'CPUSEL',0,1
	.word	650
	.byte	3,5,2,35,3,11
	.byte	'STBYEVEN',0,1
	.word	650
	.byte	1,4,2,35,3,11
	.byte	'STBYEV',0,1
	.word	650
	.byte	3,1,2,35,3,11
	.byte	'LCK',0,1
	.word	650
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_PMSWCR1_Bits',0,4,170,7,3
	.word	43771
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR2_Bits',0,4,173,7,16,4,11
	.byte	'SCRINT',0,1
	.word	650
	.byte	8,0,2,35,0,11
	.byte	'BUSY',0,1
	.word	650
	.byte	1,7,2,35,1,11
	.byte	'SCRECC',0,1
	.word	650
	.byte	1,6,2,35,1,11
	.byte	'SCRWDT',0,1
	.word	650
	.byte	1,5,2,35,1,11
	.byte	'SCRRST',0,1
	.word	650
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	650
	.byte	4,0,2,35,1,11
	.byte	'TCINT',0,1
	.word	650
	.byte	8,0,2,35,2,11
	.byte	'TCINTREQ',0,1
	.word	650
	.byte	1,7,2,35,3,11
	.byte	'SMURST',0,1
	.word	650
	.byte	1,6,2,35,3,11
	.byte	'RST',0,1
	.word	650
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	650
	.byte	4,1,2,35,3,11
	.byte	'LCK',0,1
	.word	650
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_PMSWCR2_Bits',0,4,187,7,3
	.word	44065
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTAT_Bits',0,4,190,7,16,4,11
	.byte	'reserved_0',0,1
	.word	650
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKP',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUN',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'PINAWKP',0,1
	.word	650
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUN',0,1
	.word	650
	.byte	1,2,2,35,0,11
	.byte	'PINBWKP',0,1
	.word	650
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUN',0,1
	.word	650
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	650
	.byte	1,7,2,35,1,11
	.byte	'PORSTDF',0,1
	.word	650
	.byte	1,6,2,35,1,11
	.byte	'HWCFGEVR',0,1
	.word	650
	.byte	3,3,2,35,1,11
	.byte	'STBYRAM',0,1
	.word	650
	.byte	2,1,2,35,1,11
	.byte	'TRIST',0,1
	.word	650
	.byte	1,0,2,35,1,11
	.byte	'SCRST',0,1
	.word	650
	.byte	1,7,2,35,2,11
	.byte	'SCRWKP',0,1
	.word	650
	.byte	1,6,2,35,2,11
	.byte	'SCR',0,1
	.word	650
	.byte	1,5,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	650
	.byte	1,4,2,35,2,11
	.byte	'ESR1WKEN',0,1
	.word	650
	.byte	1,3,2,35,2,11
	.byte	'PINAWKEN',0,1
	.word	650
	.byte	1,2,2,35,2,11
	.byte	'PINBWKEN',0,1
	.word	650
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	667
	.byte	4,5,2,35,2,11
	.byte	'ESR0TRIST',0,1
	.word	650
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	650
	.byte	4,0,2,35,3,0,30
	.byte	'Ifx_SCU_PMSWSTAT_Bits',0,4,214,7,3
	.word	44343
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTATCLR_Bits',0,4,217,7,16,4,11
	.byte	'reserved_0',0,1
	.word	650
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKPCLR',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUNCLR',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'PINAWKPCLR',0,1
	.word	650
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUNCLR',0,1
	.word	650
	.byte	1,2,2,35,0,11
	.byte	'PINBWKPCLR',0,1
	.word	650
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUNCLR',0,1
	.word	650
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	650
	.byte	8,0,2,35,1,11
	.byte	'SCRSTCLR',0,1
	.word	650
	.byte	1,7,2,35,2,11
	.byte	'SCRWKPCLR',0,1
	.word	650
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	667
	.byte	14,0,2,35,2,0,30
	.byte	'Ifx_SCU_PMSWSTATCLR_Bits',0,4,230,7,3
	.word	44839
	.byte	10
	.byte	'_Ifx_SCU_RSTCON2_Bits',0,4,233,7,16,4,11
	.byte	'reserved_0',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'CLRC',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	667
	.byte	10,4,2,35,0,11
	.byte	'CSS0',0,1
	.word	650
	.byte	1,3,2,35,1,11
	.byte	'CSS1',0,1
	.word	650
	.byte	1,2,2,35,1,11
	.byte	'CSS2',0,1
	.word	650
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	650
	.byte	1,0,2,35,1,11
	.byte	'USRINFO',0,2
	.word	667
	.byte	16,0,2,35,2,0,30
	.byte	'Ifx_SCU_RSTCON2_Bits',0,4,243,7,3
	.word	45152
	.byte	10
	.byte	'_Ifx_SCU_RSTCON_Bits',0,4,246,7,16,4,11
	.byte	'ESR0',0,1
	.word	650
	.byte	2,6,2,35,0,11
	.byte	'ESR1',0,1
	.word	650
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	650
	.byte	2,2,2,35,0,11
	.byte	'SMU',0,1
	.word	650
	.byte	2,0,2,35,0,11
	.byte	'SW',0,1
	.word	650
	.byte	2,6,2,35,1,11
	.byte	'STM0',0,1
	.word	650
	.byte	2,4,2,35,1,11
	.byte	'STM1',0,1
	.word	650
	.byte	2,2,2,35,1,11
	.byte	'STM2',0,1
	.word	650
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	667
	.byte	16,0,2,35,2,0,30
	.byte	'Ifx_SCU_RSTCON_Bits',0,4,129,8,3
	.word	45361
	.byte	10
	.byte	'_Ifx_SCU_RSTSTAT_Bits',0,4,132,8,16,4,11
	.byte	'ESR0',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'ESR1',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'SMU',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'SW',0,1
	.word	650
	.byte	1,3,2,35,0,11
	.byte	'STM0',0,1
	.word	650
	.byte	1,2,2,35,0,11
	.byte	'STM1',0,1
	.word	650
	.byte	1,1,2,35,0,11
	.byte	'STM2',0,1
	.word	650
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	650
	.byte	8,0,2,35,1,11
	.byte	'PORST',0,1
	.word	650
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	650
	.byte	1,6,2,35,2,11
	.byte	'CB0',0,1
	.word	650
	.byte	1,5,2,35,2,11
	.byte	'CB1',0,1
	.word	650
	.byte	1,4,2,35,2,11
	.byte	'CB3',0,1
	.word	650
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	650
	.byte	2,1,2,35,2,11
	.byte	'EVR13',0,1
	.word	650
	.byte	1,0,2,35,2,11
	.byte	'EVR33',0,1
	.word	650
	.byte	1,7,2,35,3,11
	.byte	'SWD',0,1
	.word	650
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	650
	.byte	2,4,2,35,3,11
	.byte	'STBYR',0,1
	.word	650
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	650
	.byte	3,0,2,35,3,0,30
	.byte	'Ifx_SCU_RSTSTAT_Bits',0,4,155,8,3
	.word	45572
	.byte	10
	.byte	'_Ifx_SCU_SAFECON_Bits',0,4,158,8,16,4,11
	.byte	'HBT',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	475
	.byte	31,0,2,35,0,0,30
	.byte	'Ifx_SCU_SAFECON_Bits',0,4,162,8,3
	.word	46004
	.byte	10
	.byte	'_Ifx_SCU_STSTAT_Bits',0,4,165,8,16,4,11
	.byte	'HWCFG',0,1
	.word	650
	.byte	8,0,2,35,0,11
	.byte	'FTM',0,1
	.word	650
	.byte	7,1,2,35,1,11
	.byte	'MODE',0,1
	.word	650
	.byte	1,0,2,35,1,11
	.byte	'FCBAE',0,1
	.word	650
	.byte	1,7,2,35,2,11
	.byte	'LUDIS',0,1
	.word	650
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	650
	.byte	1,5,2,35,2,11
	.byte	'TRSTL',0,1
	.word	650
	.byte	1,4,2,35,2,11
	.byte	'SPDEN',0,1
	.word	650
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	650
	.byte	3,0,2,35,2,11
	.byte	'RAMINT',0,1
	.word	650
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	650
	.byte	7,0,2,35,3,0,30
	.byte	'Ifx_SCU_STSTAT_Bits',0,4,178,8,3
	.word	46100
	.byte	10
	.byte	'_Ifx_SCU_SWRSTCON_Bits',0,4,181,8,16,4,11
	.byte	'reserved_0',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'SWRSTREQ',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	475
	.byte	30,0,2,35,0,0,30
	.byte	'Ifx_SCU_SWRSTCON_Bits',0,4,186,8,3
	.word	46360
	.byte	10
	.byte	'_Ifx_SCU_SYSCON_Bits',0,4,189,8,16,4,11
	.byte	'CCTRIG0',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'RAMINTM',0,1
	.word	650
	.byte	2,4,2,35,0,11
	.byte	'SETLUDIS',0,1
	.word	650
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	650
	.byte	3,0,2,35,0,11
	.byte	'DATM',0,1
	.word	650
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,4
	.word	475
	.byte	23,0,2,35,0,0,30
	.byte	'Ifx_SCU_SYSCON_Bits',0,4,198,8,3
	.word	46485
	.byte	10
	.byte	'_Ifx_SCU_TRAPCLR_Bits',0,4,201,8,16,4,11
	.byte	'ESR0T',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	475
	.byte	28,0,2,35,0,0,30
	.byte	'Ifx_SCU_TRAPCLR_Bits',0,4,208,8,3
	.word	46682
	.byte	10
	.byte	'_Ifx_SCU_TRAPDIS_Bits',0,4,211,8,16,4,11
	.byte	'ESR0T',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	475
	.byte	28,0,2,35,0,0,30
	.byte	'Ifx_SCU_TRAPDIS_Bits',0,4,218,8,3
	.word	46835
	.byte	10
	.byte	'_Ifx_SCU_TRAPSET_Bits',0,4,221,8,16,4,11
	.byte	'ESR0T',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	475
	.byte	28,0,2,35,0,0,30
	.byte	'Ifx_SCU_TRAPSET_Bits',0,4,228,8,3
	.word	46988
	.byte	10
	.byte	'_Ifx_SCU_TRAPSTAT_Bits',0,4,231,8,16,4,11
	.byte	'ESR0T',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	475
	.byte	28,0,2,35,0,0,30
	.byte	'Ifx_SCU_TRAPSTAT_Bits',0,4,238,8,3
	.word	47141
	.byte	30
	.byte	'Ifx_SCU_WDTCPU_CON0_Bits',0,4,247,8,3
	.word	514
	.byte	30
	.byte	'Ifx_SCU_WDTCPU_CON1_Bits',0,4,134,9,3
	.word	689
	.byte	30
	.byte	'Ifx_SCU_WDTCPU_SR_Bits',0,4,150,9,3
	.word	933
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON0_Bits',0,4,153,9,16,4,11
	.byte	'ENDINIT',0,4
	.word	498
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	498
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	498
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	498
	.byte	16,0,2,35,0,0,30
	.byte	'Ifx_SCU_WDTS_CON0_Bits',0,4,159,9,3
	.word	47396
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON1_Bits',0,4,162,9,16,4,11
	.byte	'CLRIRF',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'IR0',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	650
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	650
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	650
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	650
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	650
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	650
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	667
	.byte	16,0,2,35,2,0,30
	.byte	'Ifx_SCU_WDTS_CON1_Bits',0,4,175,9,3
	.word	47522
	.byte	10
	.byte	'_Ifx_SCU_WDTS_SR_Bits',0,4,178,9,16,4,11
	.byte	'AE',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	650
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	650
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	650
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	650
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	650
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	650
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	667
	.byte	16,0,2,35,2,0,30
	.byte	'Ifx_SCU_WDTS_SR_Bits',0,4,191,9,3
	.word	47774
	.byte	12,4,199,9,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29806
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_ACCEN0',0,4,204,9,3
	.word	47993
	.byte	12,4,207,9,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30363
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_ACCEN1',0,4,212,9,3
	.word	48057
	.byte	12,4,215,9,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30440
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_ARSTDIS',0,4,220,9,3
	.word	48121
	.byte	12,4,223,9,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30576
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_CCUCON0',0,4,228,9,3
	.word	48186
	.byte	12,4,231,9,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30856
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_CCUCON1',0,4,236,9,3
	.word	48251
	.byte	12,4,239,9,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31094
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_CCUCON2',0,4,244,9,3
	.word	48316
	.byte	12,4,247,9,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31222
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_CCUCON3',0,4,252,9,3
	.word	48381
	.byte	12,4,255,9,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31465
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_CCUCON4',0,4,132,10,3
	.word	48446
	.byte	12,4,135,10,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31700
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_CCUCON5',0,4,140,10,3
	.word	48511
	.byte	12,4,143,10,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31828
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_CCUCON6',0,4,148,10,3
	.word	48576
	.byte	12,4,151,10,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31928
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_CCUCON7',0,4,156,10,3
	.word	48641
	.byte	12,4,159,10,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32028
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_CHIPID',0,4,164,10,3
	.word	48706
	.byte	12,4,167,10,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32236
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_DTSCON',0,4,172,10,3
	.word	48770
	.byte	12,4,175,10,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32401
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_DTSLIM',0,4,180,10,3
	.word	48834
	.byte	12,4,183,10,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32584
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_DTSSTAT',0,4,188,10,3
	.word	48898
	.byte	12,4,191,10,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32738
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EICR',0,4,196,10,3
	.word	48963
	.byte	12,4,199,10,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33102
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EIFR',0,4,204,10,3
	.word	49025
	.byte	12,4,207,10,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33313
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EMSR',0,4,212,10,3
	.word	49087
	.byte	12,4,215,10,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33565
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_ESRCFG',0,4,220,10,3
	.word	49149
	.byte	12,4,223,10,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33683
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_ESROCFG',0,4,228,10,3
	.word	49213
	.byte	12,4,231,10,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33794
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EVR13CON',0,4,236,10,3
	.word	49278
	.byte	12,4,239,10,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33957
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EVR33CON',0,4,244,10,3
	.word	49344
	.byte	12,4,247,10,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34120
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EVRADCSTAT',0,4,252,10,3
	.word	49410
	.byte	12,4,255,10,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34278
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EVRDVSTAT',0,4,132,11,3
	.word	49478
	.byte	12,4,135,11,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34443
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EVRMONCTRL',0,4,140,11,3
	.word	49545
	.byte	12,4,143,11,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34772
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EVROSCCTRL',0,4,148,11,3
	.word	49613
	.byte	12,4,151,11,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34993
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EVROVMON',0,4,156,11,3
	.word	49681
	.byte	12,4,159,11,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35156
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EVRRSTCON',0,4,164,11,3
	.word	49747
	.byte	12,4,167,11,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35428
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EVRSDCOEFF1',0,4,172,11,3
	.word	49814
	.byte	12,4,175,11,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35581
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EVRSDCOEFF2',0,4,180,11,3
	.word	49883
	.byte	12,4,183,11,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35737
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EVRSDCOEFF3',0,4,188,11,3
	.word	49952
	.byte	12,4,191,11,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35899
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EVRSDCOEFF4',0,4,196,11,3
	.word	50021
	.byte	12,4,199,11,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36042
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EVRSDCOEFF5',0,4,204,11,3
	.word	50090
	.byte	12,4,207,11,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36207
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EVRSDCOEFF6',0,4,212,11,3
	.word	50159
	.byte	12,4,215,11,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36352
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EVRSDCTRL1',0,4,220,11,3
	.word	50228
	.byte	12,4,223,11,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36533
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EVRSDCTRL2',0,4,228,11,3
	.word	50296
	.byte	12,4,231,11,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36707
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EVRSDCTRL3',0,4,236,11,3
	.word	50364
	.byte	12,4,239,11,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36867
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EVRSDCTRL4',0,4,244,11,3
	.word	50432
	.byte	12,4,247,11,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37011
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EVRSTAT',0,4,252,11,3
	.word	50500
	.byte	12,4,255,11,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37285
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EVRTRIM',0,4,132,12,3
	.word	50565
	.byte	12,4,135,12,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37424
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EVRUVMON',0,4,140,12,3
	.word	50630
	.byte	12,4,143,12,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37587
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EXTCON',0,4,148,12,3
	.word	50696
	.byte	12,4,151,12,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37805
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_FDR',0,4,156,12,3
	.word	50760
	.byte	12,4,159,12,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37968
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_FMR',0,4,164,12,3
	.word	50821
	.byte	12,4,167,12,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38304
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_ID',0,4,172,12,3
	.word	50882
	.byte	12,4,175,12,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38411
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_IGCR',0,4,180,12,3
	.word	50942
	.byte	12,4,183,12,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38863
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_IN',0,4,188,12,3
	.word	51004
	.byte	12,4,191,12,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38962
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_IOCR',0,4,196,12,3
	.word	51064
	.byte	12,4,199,12,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39112
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_LBISTCTRL0',0,4,204,12,3
	.word	51126
	.byte	12,4,207,12,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39261
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_LBISTCTRL1',0,4,212,12,3
	.word	51194
	.byte	12,4,215,12,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39422
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_LBISTCTRL2',0,4,220,12,3
	.word	51262
	.byte	12,4,223,12,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39552
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_LCLCON',0,4,228,12,3
	.word	51330
	.byte	12,4,231,12,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39684
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_LCLTEST',0,4,236,12,3
	.word	51394
	.byte	12,4,239,12,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39799
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_MANID',0,4,244,12,3
	.word	51459
	.byte	12,4,247,12,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39910
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_OMR',0,4,252,12,3
	.word	51522
	.byte	12,4,255,12,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40068
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_OSCCON',0,4,132,13,3
	.word	51583
	.byte	12,4,135,13,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40480
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_OUT',0,4,140,13,3
	.word	51647
	.byte	12,4,143,13,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40581
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_OVCCON',0,4,148,13,3
	.word	51708
	.byte	12,4,151,13,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40848
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_OVCENABLE',0,4,156,13,3
	.word	51772
	.byte	12,4,159,13,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40984
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_PDISC',0,4,164,13,3
	.word	51839
	.byte	12,4,167,13,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41095
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_PDR',0,4,172,13,3
	.word	51902
	.byte	12,4,175,13,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41228
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_PDRR',0,4,180,13,3
	.word	51963
	.byte	12,4,183,13,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41431
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_PLLCON0',0,4,188,13,3
	.word	52025
	.byte	12,4,191,13,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41787
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_PLLCON1',0,4,196,13,3
	.word	52090
	.byte	12,4,199,13,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41965
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_PLLCON2',0,4,204,13,3
	.word	52155
	.byte	12,4,207,13,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42065
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_PLLERAYCON0',0,4,212,13,3
	.word	52220
	.byte	12,4,215,13,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42435
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_PLLERAYCON1',0,4,220,13,3
	.word	52289
	.byte	12,4,223,13,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42621
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_PLLERAYSTAT',0,4,228,13,3
	.word	52358
	.byte	12,4,231,13,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42819
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_PLLSTAT',0,4,236,13,3
	.word	52427
	.byte	12,4,239,13,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43052
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_PMCSR',0,4,244,13,3
	.word	52492
	.byte	12,4,247,13,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43204
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_PMSWCR0',0,4,252,13,3
	.word	52555
	.byte	12,4,255,13,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43771
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_PMSWCR1',0,4,132,14,3
	.word	52620
	.byte	12,4,135,14,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44065
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_PMSWCR2',0,4,140,14,3
	.word	52685
	.byte	12,4,143,14,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44343
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_PMSWSTAT',0,4,148,14,3
	.word	52750
	.byte	12,4,151,14,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44839
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_PMSWSTATCLR',0,4,156,14,3
	.word	52816
	.byte	12,4,159,14,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45361
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_RSTCON',0,4,164,14,3
	.word	52885
	.byte	12,4,167,14,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45152
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_RSTCON2',0,4,172,14,3
	.word	52949
	.byte	12,4,175,14,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45572
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_RSTSTAT',0,4,180,14,3
	.word	53014
	.byte	12,4,183,14,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46004
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_SAFECON',0,4,188,14,3
	.word	53079
	.byte	12,4,191,14,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46100
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_STSTAT',0,4,196,14,3
	.word	53144
	.byte	12,4,199,14,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46360
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_SWRSTCON',0,4,204,14,3
	.word	53208
	.byte	12,4,207,14,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46485
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_SYSCON',0,4,212,14,3
	.word	53274
	.byte	12,4,215,14,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46682
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_TRAPCLR',0,4,220,14,3
	.word	53338
	.byte	12,4,223,14,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46835
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_TRAPDIS',0,4,228,14,3
	.word	53403
	.byte	12,4,231,14,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46988
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_TRAPSET',0,4,236,14,3
	.word	53468
	.byte	12,4,239,14,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47141
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_TRAPSTAT',0,4,244,14,3
	.word	53533
	.byte	30
	.byte	'Ifx_SCU_WDTCPU_CON0',0,4,252,14,3
	.word	610
	.byte	30
	.byte	'Ifx_SCU_WDTCPU_CON1',0,4,132,15,3
	.word	893
	.byte	30
	.byte	'Ifx_SCU_WDTCPU_SR',0,4,140,15,3
	.word	1124
	.byte	12,4,143,15,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47396
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_WDTS_CON0',0,4,148,15,3
	.word	53684
	.byte	12,4,151,15,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47522
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_WDTS_CON1',0,4,156,15,3
	.word	53751
	.byte	12,4,159,15,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47774
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_WDTS_SR',0,4,164,15,3
	.word	53818
	.byte	14
	.word	1164
	.byte	30
	.byte	'Ifx_SCU_WDTCPU',0,4,180,15,3
	.word	53883
	.byte	10
	.byte	'_Ifx_SCU_WDTS',0,4,183,15,25,12,13
	.byte	'CON0',0
	.word	53684
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	53751
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	53818
	.byte	4,2,35,8,0,14
	.word	53912
	.byte	30
	.byte	'Ifx_SCU_WDTS',0,4,188,15,3
	.word	53973
	.byte	15,8
	.word	49149
	.byte	16,1,0,15,20
	.word	650
	.byte	16,19,0,15,8
	.word	52492
	.byte	16,1,0,14
	.word	53912
	.byte	15,24
	.word	1164
	.byte	16,1,0,14
	.word	54032
	.byte	15,16
	.word	650
	.byte	16,15,0,15,28
	.word	650
	.byte	16,27,0,15,40
	.word	650
	.byte	16,39,0,15,16
	.word	48963
	.byte	16,3,0,15,16
	.word	50942
	.byte	16,3,0,15,180,3
	.word	650
	.byte	16,179,3,0,10
	.byte	'_Ifx_SCU',0,4,201,15,25,128,8,13
	.byte	'reserved_0',0
	.word	4287
	.byte	8,2,35,0,13
	.byte	'ID',0
	.word	50882
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	2468
	.byte	4,2,35,12,13
	.byte	'OSCCON',0
	.word	51583
	.byte	4,2,35,16,13
	.byte	'PLLSTAT',0
	.word	52427
	.byte	4,2,35,20,13
	.byte	'PLLCON0',0
	.word	52025
	.byte	4,2,35,24,13
	.byte	'PLLCON1',0
	.word	52090
	.byte	4,2,35,28,13
	.byte	'PLLCON2',0
	.word	52155
	.byte	4,2,35,32,13
	.byte	'PLLERAYSTAT',0
	.word	52358
	.byte	4,2,35,36,13
	.byte	'PLLERAYCON0',0
	.word	52220
	.byte	4,2,35,40,13
	.byte	'PLLERAYCON1',0
	.word	52289
	.byte	4,2,35,44,13
	.byte	'CCUCON0',0
	.word	48186
	.byte	4,2,35,48,13
	.byte	'CCUCON1',0
	.word	48251
	.byte	4,2,35,52,13
	.byte	'FDR',0
	.word	50760
	.byte	4,2,35,56,13
	.byte	'EXTCON',0
	.word	50696
	.byte	4,2,35,60,13
	.byte	'CCUCON2',0
	.word	48316
	.byte	4,2,35,64,13
	.byte	'CCUCON3',0
	.word	48381
	.byte	4,2,35,68,13
	.byte	'CCUCON4',0
	.word	48446
	.byte	4,2,35,72,13
	.byte	'CCUCON5',0
	.word	48511
	.byte	4,2,35,76,13
	.byte	'RSTSTAT',0
	.word	53014
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	2468
	.byte	4,2,35,84,13
	.byte	'RSTCON',0
	.word	52885
	.byte	4,2,35,88,13
	.byte	'ARSTDIS',0
	.word	48121
	.byte	4,2,35,92,13
	.byte	'SWRSTCON',0
	.word	53208
	.byte	4,2,35,96,13
	.byte	'RSTCON2',0
	.word	52949
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	2468
	.byte	4,2,35,104,13
	.byte	'EVRRSTCON',0
	.word	49747
	.byte	4,2,35,108,13
	.byte	'ESRCFG',0
	.word	54000
	.byte	8,2,35,112,13
	.byte	'ESROCFG',0
	.word	49213
	.byte	4,2,35,120,13
	.byte	'SYSCON',0
	.word	53274
	.byte	4,2,35,124,13
	.byte	'CCUCON6',0
	.word	48576
	.byte	4,3,35,128,1,13
	.byte	'CCUCON7',0
	.word	48641
	.byte	4,3,35,132,1,13
	.byte	'reserved_88',0
	.word	54009
	.byte	20,3,35,136,1,13
	.byte	'PDR',0
	.word	51902
	.byte	4,3,35,156,1,13
	.byte	'IOCR',0
	.word	51064
	.byte	4,3,35,160,1,13
	.byte	'OUT',0
	.word	51647
	.byte	4,3,35,164,1,13
	.byte	'OMR',0
	.word	51522
	.byte	4,3,35,168,1,13
	.byte	'IN',0
	.word	51004
	.byte	4,3,35,172,1,13
	.byte	'EVRSTAT',0
	.word	50500
	.byte	4,3,35,176,1,13
	.byte	'EVRDVSTAT',0
	.word	49478
	.byte	4,3,35,180,1,13
	.byte	'EVR13CON',0
	.word	49278
	.byte	4,3,35,184,1,13
	.byte	'EVR33CON',0
	.word	49344
	.byte	4,3,35,188,1,13
	.byte	'STSTAT',0
	.word	53144
	.byte	4,3,35,192,1,13
	.byte	'reserved_C4',0
	.word	2468
	.byte	4,3,35,196,1,13
	.byte	'PMSWCR0',0
	.word	52555
	.byte	4,3,35,200,1,13
	.byte	'PMSWSTAT',0
	.word	52750
	.byte	4,3,35,204,1,13
	.byte	'PMSWSTATCLR',0
	.word	52816
	.byte	4,3,35,208,1,13
	.byte	'PMCSR',0
	.word	54018
	.byte	8,3,35,212,1,13
	.byte	'reserved_DC',0
	.word	2468
	.byte	4,3,35,220,1,13
	.byte	'DTSSTAT',0
	.word	48898
	.byte	4,3,35,224,1,13
	.byte	'DTSCON',0
	.word	48770
	.byte	4,3,35,228,1,13
	.byte	'PMSWCR1',0
	.word	52620
	.byte	4,3,35,232,1,13
	.byte	'PMSWCR2',0
	.word	52685
	.byte	4,3,35,236,1,13
	.byte	'WDTS',0
	.word	54027
	.byte	12,3,35,240,1,13
	.byte	'EMSR',0
	.word	49087
	.byte	4,3,35,252,1,13
	.byte	'WDTCPU',0
	.word	54041
	.byte	24,3,35,128,2,13
	.byte	'reserved_118',0
	.word	4627
	.byte	12,3,35,152,2,13
	.byte	'TRAPSTAT',0
	.word	53533
	.byte	4,3,35,164,2,13
	.byte	'TRAPSET',0
	.word	53468
	.byte	4,3,35,168,2,13
	.byte	'TRAPCLR',0
	.word	53338
	.byte	4,3,35,172,2,13
	.byte	'TRAPDIS',0
	.word	53403
	.byte	4,3,35,176,2,13
	.byte	'reserved_134',0
	.word	2468
	.byte	4,3,35,180,2,13
	.byte	'LCLCON1',0
	.word	51330
	.byte	4,3,35,184,2,13
	.byte	'LCLTEST',0
	.word	51394
	.byte	4,3,35,188,2,13
	.byte	'CHIPID',0
	.word	48706
	.byte	4,3,35,192,2,13
	.byte	'MANID',0
	.word	51459
	.byte	4,3,35,196,2,13
	.byte	'reserved_148',0
	.word	4287
	.byte	8,3,35,200,2,13
	.byte	'SAFECON',0
	.word	53079
	.byte	4,3,35,208,2,13
	.byte	'reserved_154',0
	.word	54046
	.byte	16,3,35,212,2,13
	.byte	'LBISTCTRL0',0
	.word	51126
	.byte	4,3,35,228,2,13
	.byte	'LBISTCTRL1',0
	.word	51194
	.byte	4,3,35,232,2,13
	.byte	'LBISTCTRL2',0
	.word	51262
	.byte	4,3,35,236,2,13
	.byte	'reserved_170',0
	.word	54055
	.byte	28,3,35,240,2,13
	.byte	'PDISC',0
	.word	51839
	.byte	4,3,35,140,3,13
	.byte	'reserved_190',0
	.word	4287
	.byte	8,3,35,144,3,13
	.byte	'EVRTRIM',0
	.word	50565
	.byte	4,3,35,152,3,13
	.byte	'EVRADCSTAT',0
	.word	49410
	.byte	4,3,35,156,3,13
	.byte	'EVRUVMON',0
	.word	50630
	.byte	4,3,35,160,3,13
	.byte	'EVROVMON',0
	.word	49681
	.byte	4,3,35,164,3,13
	.byte	'EVRMONCTRL',0
	.word	49545
	.byte	4,3,35,168,3,13
	.byte	'reserved_1AC',0
	.word	2468
	.byte	4,3,35,172,3,13
	.byte	'EVRSDCTRL1',0
	.word	50228
	.byte	4,3,35,176,3,13
	.byte	'EVRSDCTRL2',0
	.word	50296
	.byte	4,3,35,180,3,13
	.byte	'EVRSDCTRL3',0
	.word	50364
	.byte	4,3,35,184,3,13
	.byte	'EVRSDCTRL4',0
	.word	50432
	.byte	4,3,35,188,3,13
	.byte	'EVRSDCOEFF1',0
	.word	49814
	.byte	4,3,35,192,3,13
	.byte	'EVRSDCOEFF2',0
	.word	49883
	.byte	4,3,35,196,3,13
	.byte	'EVRSDCOEFF3',0
	.word	49952
	.byte	4,3,35,200,3,13
	.byte	'EVRSDCOEFF4',0
	.word	50021
	.byte	4,3,35,204,3,13
	.byte	'EVRSDCOEFF5',0
	.word	50090
	.byte	4,3,35,208,3,13
	.byte	'EVRSDCOEFF6',0
	.word	50159
	.byte	4,3,35,212,3,13
	.byte	'EVROSCCTRL',0
	.word	49613
	.byte	4,3,35,216,3,13
	.byte	'reserved_1DC',0
	.word	2468
	.byte	4,3,35,220,3,13
	.byte	'OVCENABLE',0
	.word	51772
	.byte	4,3,35,224,3,13
	.byte	'OVCCON',0
	.word	51708
	.byte	4,3,35,228,3,13
	.byte	'reserved_1E8',0
	.word	54064
	.byte	40,3,35,232,3,13
	.byte	'EICR',0
	.word	54073
	.byte	16,3,35,144,4,13
	.byte	'EIFR',0
	.word	49025
	.byte	4,3,35,160,4,13
	.byte	'FMR',0
	.word	50821
	.byte	4,3,35,164,4,13
	.byte	'PDRR',0
	.word	51963
	.byte	4,3,35,168,4,13
	.byte	'IGCR',0
	.word	54082
	.byte	16,3,35,172,4,13
	.byte	'reserved_23C',0
	.word	2468
	.byte	4,3,35,188,4,13
	.byte	'DTSLIM',0
	.word	48834
	.byte	4,3,35,192,4,13
	.byte	'reserved_244',0
	.word	54091
	.byte	180,3,3,35,196,4,13
	.byte	'ACCEN1',0
	.word	48057
	.byte	4,3,35,248,7,13
	.byte	'ACCEN0',0
	.word	47993
	.byte	4,3,35,252,7,0,14
	.word	54102
	.byte	30
	.byte	'Ifx_SCU',0,4,181,16,3
	.word	56092
	.byte	30
	.byte	'IfxPort_InputMode',0,5,89,3
	.word	9220
	.byte	30
	.byte	'IfxPort_OutputIdx',0,5,130,1,3
	.word	9495
	.byte	30
	.byte	'IfxPort_OutputMode',0,5,138,1,3
	.word	9425
	.byte	17,5,144,1,9,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed1',0,0,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed2',0,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed3',0,2,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed4',0,3,18
	.byte	'IfxPort_PadDriver_lvdsSpeed1',0,4,18
	.byte	'IfxPort_PadDriver_lvdsSpeed2',0,5,18
	.byte	'IfxPort_PadDriver_lvdsSpeed3',0,6,18
	.byte	'IfxPort_PadDriver_lvdsSpeed4',0,7,18
	.byte	'IfxPort_PadDriver_ttlSpeed1',0,8,18
	.byte	'IfxPort_PadDriver_ttlSpeed2',0,9,18
	.byte	'IfxPort_PadDriver_ttlSpeed3',0,10,18
	.byte	'IfxPort_PadDriver_ttlSpeed4',0,11,0,30
	.byte	'IfxPort_PadDriver',0,5,158,1,3
	.word	56195
	.byte	30
	.byte	'IfxPort_State',0,5,178,1,3
	.word	9808
	.byte	20,5,190,1,9,8,13
	.byte	'port',0
	.word	9215
	.byte	4,2,35,0,13
	.byte	'pinIndex',0
	.word	650
	.byte	1,2,35,4,0,30
	.byte	'IfxPort_Pin',0,5,194,1,3
	.word	56660
	.byte	30
	.byte	'gpio_pin_enum',0,22,89,2
	.word	23169
	.byte	30
	.byte	'gpio_dir_enum',0,22,95,2
	.word	25243
	.byte	30
	.byte	'gpio_mode_enum',0,22,111,2
	.word	25261
	.byte	30
	.byte	'Ifx_ASCLIN_ACCEN0_Bits',0,13,79,3
	.word	16477
	.byte	30
	.byte	'Ifx_ASCLIN_ACCEN1_Bits',0,13,85,3
	.word	16385
	.byte	30
	.byte	'Ifx_ASCLIN_BITCON_Bits',0,13,97,3
	.word	12009
	.byte	30
	.byte	'Ifx_ASCLIN_BRD_Bits',0,13,106,3
	.word	12849
	.byte	30
	.byte	'Ifx_ASCLIN_BRG_Bits',0,13,115,3
	.word	12692
	.byte	30
	.byte	'Ifx_ASCLIN_CLC_Bits',0,13,125,3
	.word	10964
	.byte	30
	.byte	'Ifx_ASCLIN_CSR_Bits',0,13,133,1,3
	.word	15658
	.byte	30
	.byte	'Ifx_ASCLIN_DATCON_Bits',0,13,145,1,3
	.word	12495
	.byte	30
	.byte	'Ifx_ASCLIN_FLAGS_Bits',0,13,177,1,3
	.word	13505
	.byte	30
	.byte	'Ifx_ASCLIN_FLAGSCLEAR_Bits',0,13,209,1,3
	.word	14504
	.byte	30
	.byte	'Ifx_ASCLIN_FLAGSENABLE_Bits',0,13,238,1,3
	.word	15019
	.byte	30
	.byte	'Ifx_ASCLIN_FLAGSSET_Bits',0,13,142,2,3
	.word	13991
	.byte	30
	.byte	'Ifx_ASCLIN_FRAMECON_Bits',0,13,158,2,3
	.word	12230
	.byte	30
	.byte	'Ifx_ASCLIN_ID_Bits',0,13,166,2,3
	.word	11418
	.byte	30
	.byte	'Ifx_ASCLIN_IOCR_Bits',0,13,184,2,3
	.word	11123
	.byte	30
	.byte	'Ifx_ASCLIN_KRST0_Bits',0,13,192,2,3
	.word	16259
	.byte	30
	.byte	'Ifx_ASCLIN_KRST1_Bits',0,13,199,2,3
	.word	16152
	.byte	30
	.byte	'Ifx_ASCLIN_KRSTCLR_Bits',0,13,206,2,3
	.word	16043
	.byte	30
	.byte	'Ifx_ASCLIN_LIN_BTIMER_Bits',0,13,213,2,3
	.word	13203
	.byte	30
	.byte	'Ifx_ASCLIN_LIN_CON_Bits',0,13,225,2,3
	.word	13003
	.byte	30
	.byte	'Ifx_ASCLIN_LIN_HTIMER_Bits',0,13,232,2,3
	.word	13317
	.byte	30
	.byte	'Ifx_ASCLIN_OCS_Bits',0,13,242,2,3
	.word	15880
	.byte	30
	.byte	'Ifx_ASCLIN_RXDATA_Bits',0,13,248,2,3
	.word	15571
	.byte	30
	.byte	'Ifx_ASCLIN_RXDATAD_Bits',0,13,254,2,3
	.word	15781
	.byte	30
	.byte	'Ifx_ASCLIN_RXFIFOCON_Bits',0,13,140,3,3
	.word	11768
	.byte	30
	.byte	'Ifx_ASCLIN_TXDATA_Bits',0,13,146,3,3
	.word	15484
	.byte	30
	.byte	'Ifx_ASCLIN_TXFIFOCON_Bits',0,13,159,3,3
	.word	11543
	.byte	30
	.byte	'Ifx_ASCLIN_ACCEN0',0,13,172,3,3
	.word	17009
	.byte	30
	.byte	'Ifx_ASCLIN_ACCEN1',0,13,180,3,3
	.word	16437
	.byte	30
	.byte	'Ifx_ASCLIN_BITCON',0,13,188,3,3
	.word	12190
	.byte	30
	.byte	'Ifx_ASCLIN_BRD',0,13,196,3,3
	.word	12963
	.byte	30
	.byte	'Ifx_ASCLIN_BRG',0,13,204,3,3
	.word	12809
	.byte	30
	.byte	'Ifx_ASCLIN_CLC',0,13,212,3,3
	.word	11083
	.byte	30
	.byte	'Ifx_ASCLIN_CSR',0,13,220,3,3
	.word	15741
	.byte	30
	.byte	'Ifx_ASCLIN_DATCON',0,13,228,3,3
	.word	12652
	.byte	30
	.byte	'Ifx_ASCLIN_FLAGS',0,13,236,3,3
	.word	13951
	.byte	30
	.byte	'Ifx_ASCLIN_FLAGSCLEAR',0,13,244,3,3
	.word	14979
	.byte	30
	.byte	'Ifx_ASCLIN_FLAGSENABLE',0,13,252,3,3
	.word	15444
	.byte	30
	.byte	'Ifx_ASCLIN_FLAGSSET',0,13,132,4,3
	.word	14464
	.byte	30
	.byte	'Ifx_ASCLIN_FRAMECON',0,13,140,4,3
	.word	12455
	.byte	30
	.byte	'Ifx_ASCLIN_ID',0,13,148,4,3
	.word	11503
	.byte	30
	.byte	'Ifx_ASCLIN_IOCR',0,13,156,4,3
	.word	11378
	.byte	30
	.byte	'Ifx_ASCLIN_KRST0',0,13,164,4,3
	.word	16345
	.byte	30
	.byte	'Ifx_ASCLIN_KRST1',0,13,172,4,3
	.word	16219
	.byte	30
	.byte	'Ifx_ASCLIN_KRSTCLR',0,13,180,4,3
	.word	16112
	.byte	30
	.byte	'Ifx_ASCLIN_LIN_BTIMER',0,13,188,4,3
	.word	13277
	.byte	30
	.byte	'Ifx_ASCLIN_LIN_CON',0,13,196,4,3
	.word	13163
	.byte	30
	.byte	'Ifx_ASCLIN_LIN_HTIMER',0,13,204,4,3
	.word	13392
	.byte	30
	.byte	'Ifx_ASCLIN_OCS',0,13,212,4,3
	.word	16003
	.byte	30
	.byte	'Ifx_ASCLIN_RXDATA',0,13,220,4,3
	.word	15618
	.byte	30
	.byte	'Ifx_ASCLIN_RXDATAD',0,13,228,4,3
	.word	15829
	.byte	30
	.byte	'Ifx_ASCLIN_RXFIFOCON',0,13,236,4,3
	.word	11969
	.byte	30
	.byte	'Ifx_ASCLIN_TXDATA',0,13,244,4,3
	.word	15531
	.byte	30
	.byte	'Ifx_ASCLIN_TXFIFOCON',0,13,252,4,3
	.word	11728
	.byte	14
	.word	13432
	.byte	30
	.byte	'Ifx_ASCLIN_LIN',0,13,140,5,3
	.word	58386
	.byte	14
	.word	17049
	.byte	30
	.byte	'Ifx_ASCLIN',0,13,181,5,3
	.word	58415
	.byte	10
	.byte	'_Ifx_CPU_A_Bits',0,31,45,16,4,11
	.byte	'ADDR',0,4
	.word	498
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_CPU_A_Bits',0,31,48,3
	.word	58440
	.byte	10
	.byte	'_Ifx_CPU_BIV_Bits',0,31,51,16,4,11
	.byte	'VSS',0,4
	.word	498
	.byte	1,31,2,35,0,11
	.byte	'BIV',0,4
	.word	498
	.byte	31,0,2,35,0,0,30
	.byte	'Ifx_CPU_BIV_Bits',0,31,55,3
	.word	58501
	.byte	10
	.byte	'_Ifx_CPU_BTV_Bits',0,31,58,16,4,11
	.byte	'reserved_0',0,4
	.word	498
	.byte	1,31,2,35,0,11
	.byte	'BTV',0,4
	.word	498
	.byte	31,0,2,35,0,0,30
	.byte	'Ifx_CPU_BTV_Bits',0,31,62,3
	.word	58580
	.byte	10
	.byte	'_Ifx_CPU_CCNT_Bits',0,31,65,16,4,11
	.byte	'CountValue',0,4
	.word	498
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	498
	.byte	1,0,2,35,0,0,30
	.byte	'Ifx_CPU_CCNT_Bits',0,31,69,3
	.word	58666
	.byte	10
	.byte	'_Ifx_CPU_CCTRL_Bits',0,31,72,16,4,11
	.byte	'CM',0,4
	.word	498
	.byte	1,31,2,35,0,11
	.byte	'CE',0,4
	.word	498
	.byte	1,30,2,35,0,11
	.byte	'M1',0,4
	.word	498
	.byte	3,27,2,35,0,11
	.byte	'M2',0,4
	.word	498
	.byte	3,24,2,35,0,11
	.byte	'M3',0,4
	.word	498
	.byte	3,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	498
	.byte	21,0,2,35,0,0,30
	.byte	'Ifx_CPU_CCTRL_Bits',0,31,80,3
	.word	58755
	.byte	10
	.byte	'_Ifx_CPU_COMPAT_Bits',0,31,83,16,4,11
	.byte	'reserved_0',0,4
	.word	498
	.byte	3,29,2,35,0,11
	.byte	'RM',0,4
	.word	498
	.byte	1,28,2,35,0,11
	.byte	'SP',0,4
	.word	498
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	498
	.byte	27,0,2,35,0,0,30
	.byte	'Ifx_CPU_COMPAT_Bits',0,31,89,3
	.word	58901
	.byte	10
	.byte	'_Ifx_CPU_CORE_ID_Bits',0,31,92,16,4,11
	.byte	'CORE_ID',0,4
	.word	498
	.byte	3,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	498
	.byte	29,0,2,35,0,0,30
	.byte	'Ifx_CPU_CORE_ID_Bits',0,31,96,3
	.word	59028
	.byte	10
	.byte	'_Ifx_CPU_CPR_L_Bits',0,31,99,16,4,11
	.byte	'reserved_0',0,4
	.word	498
	.byte	3,29,2,35,0,11
	.byte	'LOWBND',0,4
	.word	498
	.byte	29,0,2,35,0,0,30
	.byte	'Ifx_CPU_CPR_L_Bits',0,31,103,3
	.word	59126
	.byte	10
	.byte	'_Ifx_CPU_CPR_U_Bits',0,31,106,16,4,11
	.byte	'reserved_0',0,4
	.word	498
	.byte	3,29,2,35,0,11
	.byte	'UPPBND',0,4
	.word	498
	.byte	29,0,2,35,0,0,30
	.byte	'Ifx_CPU_CPR_U_Bits',0,31,110,3
	.word	59219
	.byte	10
	.byte	'_Ifx_CPU_CPU_ID_Bits',0,31,113,16,4,11
	.byte	'MODREV',0,4
	.word	498
	.byte	8,24,2,35,0,11
	.byte	'MOD_32B',0,4
	.word	498
	.byte	8,16,2,35,0,11
	.byte	'MOD',0,4
	.word	498
	.byte	16,0,2,35,0,0,30
	.byte	'Ifx_CPU_CPU_ID_Bits',0,31,118,3
	.word	59312
	.byte	10
	.byte	'_Ifx_CPU_CPXE_Bits',0,31,121,16,4,11
	.byte	'XE',0,4
	.word	498
	.byte	8,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	498
	.byte	24,0,2,35,0,0,30
	.byte	'Ifx_CPU_CPXE_Bits',0,31,125,3
	.word	59419
	.byte	10
	.byte	'_Ifx_CPU_CREVT_Bits',0,31,128,1,16,4,11
	.byte	'EVTA',0,4
	.word	498
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	498
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	498
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	498
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	498
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	498
	.byte	24,0,2,35,0,0,30
	.byte	'Ifx_CPU_CREVT_Bits',0,31,136,1,3
	.word	59506
	.byte	10
	.byte	'_Ifx_CPU_CUS_ID_Bits',0,31,139,1,16,4,11
	.byte	'CID',0,4
	.word	498
	.byte	3,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	498
	.byte	29,0,2,35,0,0,30
	.byte	'Ifx_CPU_CUS_ID_Bits',0,31,143,1,3
	.word	59660
	.byte	10
	.byte	'_Ifx_CPU_D_Bits',0,31,146,1,16,4,11
	.byte	'DATA',0,4
	.word	498
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_CPU_D_Bits',0,31,149,1,3
	.word	59754
	.byte	10
	.byte	'_Ifx_CPU_DATR_Bits',0,31,152,1,16,4,11
	.byte	'reserved_0',0,4
	.word	498
	.byte	3,29,2,35,0,11
	.byte	'SBE',0,4
	.word	498
	.byte	1,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	498
	.byte	5,23,2,35,0,11
	.byte	'CWE',0,4
	.word	498
	.byte	1,22,2,35,0,11
	.byte	'CFE',0,4
	.word	498
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	498
	.byte	3,18,2,35,0,11
	.byte	'SOE',0,4
	.word	498
	.byte	1,17,2,35,0,11
	.byte	'SME',0,4
	.word	498
	.byte	1,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	498
	.byte	16,0,2,35,0,0,30
	.byte	'Ifx_CPU_DATR_Bits',0,31,163,1,3
	.word	59817
	.byte	10
	.byte	'_Ifx_CPU_DBGSR_Bits',0,31,166,1,16,4,11
	.byte	'DE',0,4
	.word	498
	.byte	1,31,2,35,0,11
	.byte	'HALT',0,4
	.word	498
	.byte	2,29,2,35,0,11
	.byte	'SIH',0,4
	.word	498
	.byte	1,28,2,35,0,11
	.byte	'SUSP',0,4
	.word	498
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	498
	.byte	1,26,2,35,0,11
	.byte	'PREVSUSP',0,4
	.word	498
	.byte	1,25,2,35,0,11
	.byte	'PEVT',0,4
	.word	498
	.byte	1,24,2,35,0,11
	.byte	'EVTSRC',0,4
	.word	498
	.byte	5,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	498
	.byte	19,0,2,35,0,0,30
	.byte	'Ifx_CPU_DBGSR_Bits',0,31,177,1,3
	.word	60035
	.byte	10
	.byte	'_Ifx_CPU_DBGTCR_Bits',0,31,180,1,16,4,11
	.byte	'DTA',0,4
	.word	498
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	498
	.byte	31,0,2,35,0,0,30
	.byte	'Ifx_CPU_DBGTCR_Bits',0,31,184,1,3
	.word	60250
	.byte	10
	.byte	'_Ifx_CPU_DCON0_Bits',0,31,187,1,16,4,11
	.byte	'reserved_0',0,4
	.word	498
	.byte	1,31,2,35,0,11
	.byte	'DCBYP',0,4
	.word	498
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	498
	.byte	30,0,2,35,0,0,30
	.byte	'Ifx_CPU_DCON0_Bits',0,31,192,1,3
	.word	60344
	.byte	10
	.byte	'_Ifx_CPU_DCON2_Bits',0,31,195,1,16,4,11
	.byte	'DCACHE_SZE',0,4
	.word	498
	.byte	16,16,2,35,0,11
	.byte	'DSCRATCH_SZE',0,4
	.word	498
	.byte	16,0,2,35,0,0,30
	.byte	'Ifx_CPU_DCON2_Bits',0,31,199,1,3
	.word	60460
	.byte	10
	.byte	'_Ifx_CPU_DCX_Bits',0,31,202,1,16,4,11
	.byte	'reserved_0',0,4
	.word	498
	.byte	6,26,2,35,0,11
	.byte	'DCXValue',0,4
	.word	498
	.byte	26,0,2,35,0,0,30
	.byte	'Ifx_CPU_DCX_Bits',0,31,206,1,3
	.word	60561
	.byte	10
	.byte	'_Ifx_CPU_DEADD_Bits',0,31,209,1,16,4,11
	.byte	'ERROR_ADDRESS',0,4
	.word	498
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_CPU_DEADD_Bits',0,31,212,1,3
	.word	60654
	.byte	10
	.byte	'_Ifx_CPU_DIEAR_Bits',0,31,215,1,16,4,11
	.byte	'TA',0,4
	.word	498
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_CPU_DIEAR_Bits',0,31,218,1,3
	.word	60734
	.byte	10
	.byte	'_Ifx_CPU_DIETR_Bits',0,31,221,1,16,4,11
	.byte	'IED',0,4
	.word	498
	.byte	1,31,2,35,0,11
	.byte	'IE_T',0,4
	.word	498
	.byte	1,30,2,35,0,11
	.byte	'IE_C',0,4
	.word	498
	.byte	1,29,2,35,0,11
	.byte	'IE_S',0,4
	.word	498
	.byte	1,28,2,35,0,11
	.byte	'IE_BI',0,4
	.word	498
	.byte	1,27,2,35,0,11
	.byte	'E_INFO',0,4
	.word	498
	.byte	6,21,2,35,0,11
	.byte	'IE_DUAL',0,4
	.word	498
	.byte	1,20,2,35,0,11
	.byte	'IE_SP',0,4
	.word	498
	.byte	1,19,2,35,0,11
	.byte	'IE_BS',0,4
	.word	498
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	498
	.byte	18,0,2,35,0,0,30
	.byte	'Ifx_CPU_DIETR_Bits',0,31,233,1,3
	.word	60803
	.byte	10
	.byte	'_Ifx_CPU_DMS_Bits',0,31,236,1,16,4,11
	.byte	'reserved_0',0,4
	.word	498
	.byte	1,31,2,35,0,11
	.byte	'DMSValue',0,4
	.word	498
	.byte	31,0,2,35,0,0,30
	.byte	'Ifx_CPU_DMS_Bits',0,31,240,1,3
	.word	61032
	.byte	10
	.byte	'_Ifx_CPU_DPR_L_Bits',0,31,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	498
	.byte	3,29,2,35,0,11
	.byte	'LOWBND',0,4
	.word	498
	.byte	29,0,2,35,0,0,30
	.byte	'Ifx_CPU_DPR_L_Bits',0,31,247,1,3
	.word	61125
	.byte	10
	.byte	'_Ifx_CPU_DPR_U_Bits',0,31,250,1,16,4,11
	.byte	'reserved_0',0,4
	.word	498
	.byte	3,29,2,35,0,11
	.byte	'UPPBND',0,4
	.word	498
	.byte	29,0,2,35,0,0,30
	.byte	'Ifx_CPU_DPR_U_Bits',0,31,254,1,3
	.word	61220
	.byte	10
	.byte	'_Ifx_CPU_DPRE_Bits',0,31,129,2,16,4,11
	.byte	'RE',0,4
	.word	498
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	498
	.byte	16,0,2,35,0,0,30
	.byte	'Ifx_CPU_DPRE_Bits',0,31,133,2,3
	.word	61315
	.byte	10
	.byte	'_Ifx_CPU_DPWE_Bits',0,31,136,2,16,4,11
	.byte	'WE',0,4
	.word	498
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	498
	.byte	16,0,2,35,0,0,30
	.byte	'Ifx_CPU_DPWE_Bits',0,31,140,2,3
	.word	61405
	.byte	10
	.byte	'_Ifx_CPU_DSTR_Bits',0,31,143,2,16,4,11
	.byte	'SRE',0,4
	.word	498
	.byte	1,31,2,35,0,11
	.byte	'GAE',0,4
	.word	498
	.byte	1,30,2,35,0,11
	.byte	'LBE',0,4
	.word	498
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	498
	.byte	3,26,2,35,0,11
	.byte	'CRE',0,4
	.word	498
	.byte	1,25,2,35,0,11
	.byte	'reserved_7',0,4
	.word	498
	.byte	7,18,2,35,0,11
	.byte	'DTME',0,4
	.word	498
	.byte	1,17,2,35,0,11
	.byte	'LOE',0,4
	.word	498
	.byte	1,16,2,35,0,11
	.byte	'SDE',0,4
	.word	498
	.byte	1,15,2,35,0,11
	.byte	'SCE',0,4
	.word	498
	.byte	1,14,2,35,0,11
	.byte	'CAC',0,4
	.word	498
	.byte	1,13,2,35,0,11
	.byte	'MPE',0,4
	.word	498
	.byte	1,12,2,35,0,11
	.byte	'CLE',0,4
	.word	498
	.byte	1,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	498
	.byte	3,8,2,35,0,11
	.byte	'ALN',0,4
	.word	498
	.byte	1,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	498
	.byte	7,0,2,35,0,0,30
	.byte	'Ifx_CPU_DSTR_Bits',0,31,161,2,3
	.word	61495
	.byte	10
	.byte	'_Ifx_CPU_EXEVT_Bits',0,31,164,2,16,4,11
	.byte	'EVTA',0,4
	.word	498
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	498
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	498
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	498
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	498
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	498
	.byte	24,0,2,35,0,0,30
	.byte	'Ifx_CPU_EXEVT_Bits',0,31,172,2,3
	.word	61819
	.byte	10
	.byte	'_Ifx_CPU_FCX_Bits',0,31,175,2,16,4,11
	.byte	'FCXO',0,4
	.word	498
	.byte	16,16,2,35,0,11
	.byte	'FCXS',0,4
	.word	498
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	498
	.byte	12,0,2,35,0,0,30
	.byte	'Ifx_CPU_FCX_Bits',0,31,180,2,3
	.word	61973
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_CON_Bits',0,31,183,2,16,4,11
	.byte	'TST',0,4
	.word	498
	.byte	1,31,2,35,0,11
	.byte	'TCL',0,4
	.word	498
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	498
	.byte	6,24,2,35,0,11
	.byte	'RM',0,4
	.word	498
	.byte	2,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	498
	.byte	8,14,2,35,0,11
	.byte	'FXE',0,4
	.word	498
	.byte	1,13,2,35,0,11
	.byte	'FUE',0,4
	.word	498
	.byte	1,12,2,35,0,11
	.byte	'FZE',0,4
	.word	498
	.byte	1,11,2,35,0,11
	.byte	'FVE',0,4
	.word	498
	.byte	1,10,2,35,0,11
	.byte	'FIE',0,4
	.word	498
	.byte	1,9,2,35,0,11
	.byte	'reserved_23',0,4
	.word	498
	.byte	3,6,2,35,0,11
	.byte	'FX',0,4
	.word	498
	.byte	1,5,2,35,0,11
	.byte	'FU',0,4
	.word	498
	.byte	1,4,2,35,0,11
	.byte	'FZ',0,4
	.word	498
	.byte	1,3,2,35,0,11
	.byte	'FV',0,4
	.word	498
	.byte	1,2,2,35,0,11
	.byte	'FI',0,4
	.word	498
	.byte	1,1,2,35,0,11
	.byte	'reserved_31',0,4
	.word	498
	.byte	1,0,2,35,0,0,30
	.byte	'Ifx_CPU_FPU_TRAP_CON_Bits',0,31,202,2,3
	.word	62079
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_OPC_Bits',0,31,205,2,16,4,11
	.byte	'OPC',0,4
	.word	498
	.byte	8,24,2,35,0,11
	.byte	'FMT',0,4
	.word	498
	.byte	1,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	498
	.byte	7,16,2,35,0,11
	.byte	'DREG',0,4
	.word	498
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	498
	.byte	12,0,2,35,0,0,30
	.byte	'Ifx_CPU_FPU_TRAP_OPC_Bits',0,31,212,2,3
	.word	62428
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_PC_Bits',0,31,215,2,16,4,11
	.byte	'PC',0,4
	.word	498
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_CPU_FPU_TRAP_PC_Bits',0,31,218,2,3
	.word	62588
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC1_Bits',0,31,221,2,16,4,11
	.byte	'SRC1',0,4
	.word	498
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_CPU_FPU_TRAP_SRC1_Bits',0,31,224,2,3
	.word	62669
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC2_Bits',0,31,227,2,16,4,11
	.byte	'SRC2',0,4
	.word	498
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_CPU_FPU_TRAP_SRC2_Bits',0,31,230,2,3
	.word	62756
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC3_Bits',0,31,233,2,16,4,11
	.byte	'SRC3',0,4
	.word	498
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_CPU_FPU_TRAP_SRC3_Bits',0,31,236,2,3
	.word	62843
	.byte	10
	.byte	'_Ifx_CPU_ICNT_Bits',0,31,239,2,16,4,11
	.byte	'CountValue',0,4
	.word	498
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	498
	.byte	1,0,2,35,0,0,30
	.byte	'Ifx_CPU_ICNT_Bits',0,31,243,2,3
	.word	62930
	.byte	10
	.byte	'_Ifx_CPU_ICR_Bits',0,31,246,2,16,4,11
	.byte	'CCPN',0,4
	.word	498
	.byte	10,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	498
	.byte	5,17,2,35,0,11
	.byte	'IE',0,4
	.word	498
	.byte	1,16,2,35,0,11
	.byte	'PIPN',0,4
	.word	498
	.byte	10,6,2,35,0,11
	.byte	'reserved_26',0,4
	.word	498
	.byte	6,0,2,35,0,0,30
	.byte	'Ifx_CPU_ICR_Bits',0,31,253,2,3
	.word	63021
	.byte	10
	.byte	'_Ifx_CPU_ISP_Bits',0,31,128,3,16,4,11
	.byte	'ISP',0,4
	.word	498
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_CPU_ISP_Bits',0,31,131,3,3
	.word	63164
	.byte	10
	.byte	'_Ifx_CPU_LCX_Bits',0,31,134,3,16,4,11
	.byte	'LCXO',0,4
	.word	498
	.byte	16,16,2,35,0,11
	.byte	'LCXS',0,4
	.word	498
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	498
	.byte	12,0,2,35,0,0,30
	.byte	'Ifx_CPU_LCX_Bits',0,31,139,3,3
	.word	63230
	.byte	10
	.byte	'_Ifx_CPU_M1CNT_Bits',0,31,142,3,16,4,11
	.byte	'CountValue',0,4
	.word	498
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	498
	.byte	1,0,2,35,0,0,30
	.byte	'Ifx_CPU_M1CNT_Bits',0,31,146,3,3
	.word	63336
	.byte	10
	.byte	'_Ifx_CPU_M2CNT_Bits',0,31,149,3,16,4,11
	.byte	'CountValue',0,4
	.word	498
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	498
	.byte	1,0,2,35,0,0,30
	.byte	'Ifx_CPU_M2CNT_Bits',0,31,153,3,3
	.word	63429
	.byte	10
	.byte	'_Ifx_CPU_M3CNT_Bits',0,31,156,3,16,4,11
	.byte	'CountValue',0,4
	.word	498
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	498
	.byte	1,0,2,35,0,0,30
	.byte	'Ifx_CPU_M3CNT_Bits',0,31,160,3,3
	.word	63522
	.byte	10
	.byte	'_Ifx_CPU_PC_Bits',0,31,163,3,16,4,11
	.byte	'reserved_0',0,4
	.word	498
	.byte	1,31,2,35,0,11
	.byte	'PC',0,4
	.word	498
	.byte	31,0,2,35,0,0,30
	.byte	'Ifx_CPU_PC_Bits',0,31,167,3,3
	.word	63615
	.byte	10
	.byte	'_Ifx_CPU_PCON0_Bits',0,31,170,3,16,4,11
	.byte	'reserved_0',0,4
	.word	498
	.byte	1,31,2,35,0,11
	.byte	'PCBYP',0,4
	.word	498
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	498
	.byte	30,0,2,35,0,0,30
	.byte	'Ifx_CPU_PCON0_Bits',0,31,175,3,3
	.word	63700
	.byte	10
	.byte	'_Ifx_CPU_PCON1_Bits',0,31,178,3,16,4,11
	.byte	'PCINV',0,4
	.word	498
	.byte	1,31,2,35,0,11
	.byte	'PBINV',0,4
	.word	498
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	498
	.byte	30,0,2,35,0,0,30
	.byte	'Ifx_CPU_PCON1_Bits',0,31,183,3,3
	.word	63816
	.byte	10
	.byte	'_Ifx_CPU_PCON2_Bits',0,31,186,3,16,4,11
	.byte	'PCACHE_SZE',0,4
	.word	498
	.byte	16,16,2,35,0,11
	.byte	'PSCRATCH_SZE',0,4
	.word	498
	.byte	16,0,2,35,0,0,30
	.byte	'Ifx_CPU_PCON2_Bits',0,31,190,3,3
	.word	63927
	.byte	10
	.byte	'_Ifx_CPU_PCXI_Bits',0,31,193,3,16,4,11
	.byte	'PCXO',0,4
	.word	498
	.byte	16,16,2,35,0,11
	.byte	'PCXS',0,4
	.word	498
	.byte	4,12,2,35,0,11
	.byte	'UL',0,4
	.word	498
	.byte	1,11,2,35,0,11
	.byte	'PIE',0,4
	.word	498
	.byte	1,10,2,35,0,11
	.byte	'PCPN',0,4
	.word	498
	.byte	10,0,2,35,0,0,30
	.byte	'Ifx_CPU_PCXI_Bits',0,31,200,3,3
	.word	64028
	.byte	10
	.byte	'_Ifx_CPU_PIEAR_Bits',0,31,203,3,16,4,11
	.byte	'TA',0,4
	.word	498
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_CPU_PIEAR_Bits',0,31,206,3,3
	.word	64158
	.byte	10
	.byte	'_Ifx_CPU_PIETR_Bits',0,31,209,3,16,4,11
	.byte	'IED',0,4
	.word	498
	.byte	1,31,2,35,0,11
	.byte	'IE_T',0,4
	.word	498
	.byte	1,30,2,35,0,11
	.byte	'IE_C',0,4
	.word	498
	.byte	1,29,2,35,0,11
	.byte	'IE_S',0,4
	.word	498
	.byte	1,28,2,35,0,11
	.byte	'IE_BI',0,4
	.word	498
	.byte	1,27,2,35,0,11
	.byte	'E_INFO',0,4
	.word	498
	.byte	6,21,2,35,0,11
	.byte	'IE_DUAL',0,4
	.word	498
	.byte	1,20,2,35,0,11
	.byte	'IE_SP',0,4
	.word	498
	.byte	1,19,2,35,0,11
	.byte	'IE_BS',0,4
	.word	498
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	498
	.byte	18,0,2,35,0,0,30
	.byte	'Ifx_CPU_PIETR_Bits',0,31,221,3,3
	.word	64227
	.byte	10
	.byte	'_Ifx_CPU_PMA0_Bits',0,31,224,3,16,4,11
	.byte	'reserved_0',0,4
	.word	498
	.byte	13,19,2,35,0,11
	.byte	'DAC',0,4
	.word	498
	.byte	3,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	498
	.byte	16,0,2,35,0,0,30
	.byte	'Ifx_CPU_PMA0_Bits',0,31,229,3,3
	.word	64456
	.byte	10
	.byte	'_Ifx_CPU_PMA1_Bits',0,31,232,3,16,4,11
	.byte	'reserved_0',0,4
	.word	498
	.byte	14,18,2,35,0,11
	.byte	'CAC',0,4
	.word	498
	.byte	2,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	498
	.byte	16,0,2,35,0,0,30
	.byte	'Ifx_CPU_PMA1_Bits',0,31,237,3,3
	.word	64569
	.byte	10
	.byte	'_Ifx_CPU_PMA2_Bits',0,31,240,3,16,4,11
	.byte	'PSI',0,4
	.word	498
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	498
	.byte	16,0,2,35,0,0,30
	.byte	'Ifx_CPU_PMA2_Bits',0,31,244,3,3
	.word	64682
	.byte	10
	.byte	'_Ifx_CPU_PSTR_Bits',0,31,247,3,16,4,11
	.byte	'FRE',0,4
	.word	498
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	498
	.byte	1,30,2,35,0,11
	.byte	'FBE',0,4
	.word	498
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	498
	.byte	9,20,2,35,0,11
	.byte	'FPE',0,4
	.word	498
	.byte	1,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	498
	.byte	1,18,2,35,0,11
	.byte	'FME',0,4
	.word	498
	.byte	1,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	498
	.byte	17,0,2,35,0,0,30
	.byte	'Ifx_CPU_PSTR_Bits',0,31,129,4,3
	.word	64773
	.byte	10
	.byte	'_Ifx_CPU_PSW_Bits',0,31,132,4,16,4,11
	.byte	'CDC',0,4
	.word	498
	.byte	7,25,2,35,0,11
	.byte	'CDE',0,4
	.word	498
	.byte	1,24,2,35,0,11
	.byte	'GW',0,4
	.word	498
	.byte	1,23,2,35,0,11
	.byte	'IS',0,4
	.word	498
	.byte	1,22,2,35,0,11
	.byte	'IO',0,4
	.word	498
	.byte	2,20,2,35,0,11
	.byte	'PRS',0,4
	.word	498
	.byte	2,18,2,35,0,11
	.byte	'S',0,4
	.word	498
	.byte	1,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	498
	.byte	12,5,2,35,0,11
	.byte	'SAV',0,4
	.word	498
	.byte	1,4,2,35,0,11
	.byte	'AV',0,4
	.word	498
	.byte	1,3,2,35,0,11
	.byte	'SV',0,4
	.word	498
	.byte	1,2,2,35,0,11
	.byte	'V',0,4
	.word	498
	.byte	1,1,2,35,0,11
	.byte	'C',0,4
	.word	498
	.byte	1,0,2,35,0,0,30
	.byte	'Ifx_CPU_PSW_Bits',0,31,147,4,3
	.word	64976
	.byte	10
	.byte	'_Ifx_CPU_SEGEN_Bits',0,31,150,4,16,4,11
	.byte	'ADFLIP',0,4
	.word	498
	.byte	8,24,2,35,0,11
	.byte	'ADTYPE',0,4
	.word	498
	.byte	2,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	498
	.byte	21,1,2,35,0,11
	.byte	'AE',0,4
	.word	498
	.byte	1,0,2,35,0,0,30
	.byte	'Ifx_CPU_SEGEN_Bits',0,31,156,4,3
	.word	65219
	.byte	10
	.byte	'_Ifx_CPU_SMACON_Bits',0,31,159,4,16,4,11
	.byte	'PC',0,4
	.word	498
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	498
	.byte	1,30,2,35,0,11
	.byte	'PT',0,4
	.word	498
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	498
	.byte	5,24,2,35,0,11
	.byte	'DC',0,4
	.word	498
	.byte	1,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	498
	.byte	1,22,2,35,0,11
	.byte	'DT',0,4
	.word	498
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	498
	.byte	13,8,2,35,0,11
	.byte	'IODT',0,4
	.word	498
	.byte	1,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	498
	.byte	7,0,2,35,0,0,30
	.byte	'Ifx_CPU_SMACON_Bits',0,31,171,4,3
	.word	65347
	.byte	10
	.byte	'_Ifx_CPU_SPROT_ACCENA_Bits',0,31,174,4,16,4,11
	.byte	'EN',0,4
	.word	475
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_CPU_SPROT_ACCENA_Bits',0,31,177,4,3
	.word	65588
	.byte	10
	.byte	'_Ifx_CPU_SPROT_ACCENB_Bits',0,31,180,4,16,4,11
	.byte	'reserved_0',0,4
	.word	475
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_CPU_SPROT_ACCENB_Bits',0,31,183,4,3
	.word	65671
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,31,186,4,16,4,11
	.byte	'EN',0,4
	.word	475
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,31,189,4,3
	.word	65762
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,31,192,4,16,4,11
	.byte	'reserved_0',0,4
	.word	475
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,31,195,4,3
	.word	65853
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_LA_Bits',0,31,198,4,16,4,11
	.byte	'reserved_0',0,1
	.word	650
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	475
	.byte	27,0,2,35,0,0,30
	.byte	'Ifx_CPU_SPROT_RGN_LA_Bits',0,31,202,4,3
	.word	65952
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_UA_Bits',0,31,205,4,16,4,11
	.byte	'reserved_0',0,1
	.word	650
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	475
	.byte	27,0,2,35,0,0,30
	.byte	'Ifx_CPU_SPROT_RGN_UA_Bits',0,31,209,4,3
	.word	66059
	.byte	10
	.byte	'_Ifx_CPU_SWEVT_Bits',0,31,212,4,16,4,11
	.byte	'EVTA',0,4
	.word	498
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	498
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	498
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	498
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	498
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	498
	.byte	24,0,2,35,0,0,30
	.byte	'Ifx_CPU_SWEVT_Bits',0,31,220,4,3
	.word	66166
	.byte	10
	.byte	'_Ifx_CPU_SYSCON_Bits',0,31,223,4,16,4,11
	.byte	'FCDSF',0,4
	.word	498
	.byte	1,31,2,35,0,11
	.byte	'PROTEN',0,4
	.word	498
	.byte	1,30,2,35,0,11
	.byte	'TPROTEN',0,4
	.word	498
	.byte	1,29,2,35,0,11
	.byte	'IS',0,4
	.word	498
	.byte	1,28,2,35,0,11
	.byte	'IT',0,4
	.word	498
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	498
	.byte	27,0,2,35,0,0,30
	.byte	'Ifx_CPU_SYSCON_Bits',0,31,231,4,3
	.word	66320
	.byte	10
	.byte	'_Ifx_CPU_TASK_ASI_Bits',0,31,234,4,16,4,11
	.byte	'ASI',0,4
	.word	498
	.byte	5,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	498
	.byte	27,0,2,35,0,0,30
	.byte	'Ifx_CPU_TASK_ASI_Bits',0,31,238,4,3
	.word	66481
	.byte	10
	.byte	'_Ifx_CPU_TPS_CON_Bits',0,31,241,4,16,4,11
	.byte	'TEXP0',0,4
	.word	498
	.byte	1,31,2,35,0,11
	.byte	'TEXP1',0,4
	.word	498
	.byte	1,30,2,35,0,11
	.byte	'TEXP2',0,4
	.word	498
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	498
	.byte	13,16,2,35,0,11
	.byte	'TTRAP',0,4
	.word	498
	.byte	1,15,2,35,0,11
	.byte	'reserved_17',0,4
	.word	498
	.byte	15,0,2,35,0,0,30
	.byte	'Ifx_CPU_TPS_CON_Bits',0,31,249,4,3
	.word	66579
	.byte	10
	.byte	'_Ifx_CPU_TPS_TIMER_Bits',0,31,252,4,16,4,11
	.byte	'Timer',0,4
	.word	498
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_CPU_TPS_TIMER_Bits',0,31,255,4,3
	.word	66751
	.byte	10
	.byte	'_Ifx_CPU_TR_ADR_Bits',0,31,130,5,16,4,11
	.byte	'ADDR',0,4
	.word	498
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_CPU_TR_ADR_Bits',0,31,133,5,3
	.word	66831
	.byte	10
	.byte	'_Ifx_CPU_TR_EVT_Bits',0,31,136,5,16,4,11
	.byte	'EVTA',0,4
	.word	498
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	498
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	498
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	498
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	498
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	498
	.byte	4,20,2,35,0,11
	.byte	'TYP',0,4
	.word	498
	.byte	1,19,2,35,0,11
	.byte	'RNG',0,4
	.word	498
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	498
	.byte	1,17,2,35,0,11
	.byte	'ASI_EN',0,4
	.word	498
	.byte	1,16,2,35,0,11
	.byte	'ASI',0,4
	.word	498
	.byte	5,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	498
	.byte	6,5,2,35,0,11
	.byte	'AST',0,4
	.word	498
	.byte	1,4,2,35,0,11
	.byte	'ALD',0,4
	.word	498
	.byte	1,3,2,35,0,11
	.byte	'reserved_29',0,4
	.word	498
	.byte	3,0,2,35,0,0,30
	.byte	'Ifx_CPU_TR_EVT_Bits',0,31,153,5,3
	.word	66904
	.byte	10
	.byte	'_Ifx_CPU_TRIG_ACC_Bits',0,31,156,5,16,4,11
	.byte	'T0',0,4
	.word	498
	.byte	1,31,2,35,0,11
	.byte	'T1',0,4
	.word	498
	.byte	1,30,2,35,0,11
	.byte	'T2',0,4
	.word	498
	.byte	1,29,2,35,0,11
	.byte	'T3',0,4
	.word	498
	.byte	1,28,2,35,0,11
	.byte	'T4',0,4
	.word	498
	.byte	1,27,2,35,0,11
	.byte	'T5',0,4
	.word	498
	.byte	1,26,2,35,0,11
	.byte	'T6',0,4
	.word	498
	.byte	1,25,2,35,0,11
	.byte	'T7',0,4
	.word	498
	.byte	1,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	498
	.byte	24,0,2,35,0,0,30
	.byte	'Ifx_CPU_TRIG_ACC_Bits',0,31,167,5,3
	.word	67222
	.byte	12,31,175,5,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58440
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_A',0,31,180,5,3
	.word	67417
	.byte	12,31,183,5,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58501
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_BIV',0,31,188,5,3
	.word	67476
	.byte	12,31,191,5,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58580
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_BTV',0,31,196,5,3
	.word	67537
	.byte	12,31,199,5,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58666
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_CCNT',0,31,204,5,3
	.word	67598
	.byte	12,31,207,5,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58755
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_CCTRL',0,31,212,5,3
	.word	67660
	.byte	12,31,215,5,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58901
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_COMPAT',0,31,220,5,3
	.word	67723
	.byte	12,31,223,5,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59028
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_CORE_ID',0,31,228,5,3
	.word	67787
	.byte	12,31,231,5,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59126
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_CPR_L',0,31,236,5,3
	.word	67852
	.byte	12,31,239,5,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59219
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_CPR_U',0,31,244,5,3
	.word	67915
	.byte	12,31,247,5,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59312
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_CPU_ID',0,31,252,5,3
	.word	67978
	.byte	12,31,255,5,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59419
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_CPXE',0,31,132,6,3
	.word	68042
	.byte	12,31,135,6,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59506
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_CREVT',0,31,140,6,3
	.word	68104
	.byte	12,31,143,6,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59660
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_CUS_ID',0,31,148,6,3
	.word	68167
	.byte	12,31,151,6,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59754
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_D',0,31,156,6,3
	.word	68231
	.byte	12,31,159,6,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59817
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_DATR',0,31,164,6,3
	.word	68290
	.byte	12,31,167,6,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60035
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_DBGSR',0,31,172,6,3
	.word	68352
	.byte	12,31,175,6,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60250
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_DBGTCR',0,31,180,6,3
	.word	68415
	.byte	12,31,183,6,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60344
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_DCON0',0,31,188,6,3
	.word	68479
	.byte	12,31,191,6,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60460
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_DCON2',0,31,196,6,3
	.word	68542
	.byte	12,31,199,6,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60561
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_DCX',0,31,204,6,3
	.word	68605
	.byte	12,31,207,6,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60654
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_DEADD',0,31,212,6,3
	.word	68666
	.byte	12,31,215,6,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60734
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_DIEAR',0,31,220,6,3
	.word	68729
	.byte	12,31,223,6,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60803
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_DIETR',0,31,228,6,3
	.word	68792
	.byte	12,31,231,6,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61032
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_DMS',0,31,236,6,3
	.word	68855
	.byte	12,31,239,6,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61125
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_DPR_L',0,31,244,6,3
	.word	68916
	.byte	12,31,247,6,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61220
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_DPR_U',0,31,252,6,3
	.word	68979
	.byte	12,31,255,6,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61315
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_DPRE',0,31,132,7,3
	.word	69042
	.byte	12,31,135,7,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61405
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_DPWE',0,31,140,7,3
	.word	69104
	.byte	12,31,143,7,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61495
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_DSTR',0,31,148,7,3
	.word	69166
	.byte	12,31,151,7,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61819
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_EXEVT',0,31,156,7,3
	.word	69228
	.byte	12,31,159,7,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61973
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_FCX',0,31,164,7,3
	.word	69291
	.byte	12,31,167,7,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62079
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_FPU_TRAP_CON',0,31,172,7,3
	.word	69352
	.byte	12,31,175,7,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62428
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_FPU_TRAP_OPC',0,31,180,7,3
	.word	69422
	.byte	12,31,183,7,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62588
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_FPU_TRAP_PC',0,31,188,7,3
	.word	69492
	.byte	12,31,191,7,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62669
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_FPU_TRAP_SRC1',0,31,196,7,3
	.word	69561
	.byte	12,31,199,7,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62756
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_FPU_TRAP_SRC2',0,31,204,7,3
	.word	69632
	.byte	12,31,207,7,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62843
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_FPU_TRAP_SRC3',0,31,212,7,3
	.word	69703
	.byte	12,31,215,7,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62930
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_ICNT',0,31,220,7,3
	.word	69774
	.byte	12,31,223,7,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63021
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_ICR',0,31,228,7,3
	.word	69836
	.byte	12,31,231,7,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63164
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_ISP',0,31,236,7,3
	.word	69897
	.byte	12,31,239,7,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63230
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_LCX',0,31,244,7,3
	.word	69958
	.byte	12,31,247,7,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63336
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_M1CNT',0,31,252,7,3
	.word	70019
	.byte	12,31,255,7,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63429
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_M2CNT',0,31,132,8,3
	.word	70082
	.byte	12,31,135,8,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63522
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_M3CNT',0,31,140,8,3
	.word	70145
	.byte	12,31,143,8,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63615
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_PC',0,31,148,8,3
	.word	70208
	.byte	12,31,151,8,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63700
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_PCON0',0,31,156,8,3
	.word	70268
	.byte	12,31,159,8,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63816
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_PCON1',0,31,164,8,3
	.word	70331
	.byte	12,31,167,8,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63927
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_PCON2',0,31,172,8,3
	.word	70394
	.byte	12,31,175,8,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64028
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_PCXI',0,31,180,8,3
	.word	70457
	.byte	12,31,183,8,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64158
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_PIEAR',0,31,188,8,3
	.word	70519
	.byte	12,31,191,8,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64227
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_PIETR',0,31,196,8,3
	.word	70582
	.byte	12,31,199,8,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64456
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_PMA0',0,31,204,8,3
	.word	70645
	.byte	12,31,207,8,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64569
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_PMA1',0,31,212,8,3
	.word	70707
	.byte	12,31,215,8,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64682
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_PMA2',0,31,220,8,3
	.word	70769
	.byte	12,31,223,8,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64773
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_PSTR',0,31,228,8,3
	.word	70831
	.byte	12,31,231,8,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64976
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_PSW',0,31,236,8,3
	.word	70893
	.byte	12,31,239,8,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65219
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_SEGEN',0,31,244,8,3
	.word	70954
	.byte	12,31,247,8,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65347
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_SMACON',0,31,252,8,3
	.word	71017
	.byte	12,31,255,8,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65588
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_SPROT_ACCENA',0,31,132,9,3
	.word	71081
	.byte	12,31,135,9,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65671
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_SPROT_ACCENB',0,31,140,9,3
	.word	71151
	.byte	12,31,143,9,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65762
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA',0,31,148,9,3
	.word	71221
	.byte	12,31,151,9,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65853
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB',0,31,156,9,3
	.word	71295
	.byte	12,31,159,9,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65952
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_SPROT_RGN_LA',0,31,164,9,3
	.word	71369
	.byte	12,31,167,9,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66059
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_SPROT_RGN_UA',0,31,172,9,3
	.word	71439
	.byte	12,31,175,9,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66166
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_SWEVT',0,31,180,9,3
	.word	71509
	.byte	12,31,183,9,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66320
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_SYSCON',0,31,188,9,3
	.word	71572
	.byte	12,31,191,9,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66481
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_TASK_ASI',0,31,196,9,3
	.word	71636
	.byte	12,31,199,9,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66579
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_TPS_CON',0,31,204,9,3
	.word	71702
	.byte	12,31,207,9,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66751
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_TPS_TIMER',0,31,212,9,3
	.word	71767
	.byte	12,31,215,9,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66831
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_TR_ADR',0,31,220,9,3
	.word	71834
	.byte	12,31,223,9,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66904
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_TR_EVT',0,31,228,9,3
	.word	71898
	.byte	12,31,231,9,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67222
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_TRIG_ACC',0,31,236,9,3
	.word	71962
	.byte	10
	.byte	'_Ifx_CPU_CPR',0,31,247,9,25,8,13
	.byte	'L',0
	.word	67852
	.byte	4,2,35,0,13
	.byte	'U',0
	.word	67915
	.byte	4,2,35,4,0,14
	.word	72028
	.byte	30
	.byte	'Ifx_CPU_CPR',0,31,251,9,3
	.word	72070
	.byte	10
	.byte	'_Ifx_CPU_DPR',0,31,254,9,25,8,13
	.byte	'L',0
	.word	68916
	.byte	4,2,35,0,13
	.byte	'U',0
	.word	68979
	.byte	4,2,35,4,0,14
	.word	72096
	.byte	30
	.byte	'Ifx_CPU_DPR',0,31,130,10,3
	.word	72138
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN',0,31,133,10,25,16,13
	.byte	'LA',0
	.word	71369
	.byte	4,2,35,0,13
	.byte	'UA',0
	.word	71439
	.byte	4,2,35,4,13
	.byte	'ACCENA',0
	.word	71221
	.byte	4,2,35,8,13
	.byte	'ACCENB',0
	.word	71295
	.byte	4,2,35,12,0,14
	.word	72164
	.byte	30
	.byte	'Ifx_CPU_SPROT_RGN',0,31,139,10,3
	.word	72246
	.byte	15,12
	.word	71767
	.byte	16,2,0,10
	.byte	'_Ifx_CPU_TPS',0,31,142,10,25,16,13
	.byte	'CON',0
	.word	71702
	.byte	4,2,35,0,13
	.byte	'TIMER',0
	.word	72278
	.byte	12,2,35,4,0,14
	.word	72287
	.byte	30
	.byte	'Ifx_CPU_TPS',0,31,146,10,3
	.word	72335
	.byte	10
	.byte	'_Ifx_CPU_TR',0,31,149,10,25,8,13
	.byte	'EVT',0
	.word	71898
	.byte	4,2,35,0,13
	.byte	'ADR',0
	.word	71834
	.byte	4,2,35,4,0,14
	.word	72361
	.byte	30
	.byte	'Ifx_CPU_TR',0,31,153,10,3
	.word	72406
	.byte	15,176,32
	.word	650
	.byte	16,175,32,0,15,208,223,1
	.word	650
	.byte	16,207,223,1,0,15,248,1
	.word	650
	.byte	16,247,1,0,15,244,29
	.word	650
	.byte	16,243,29,0,15,188,3
	.word	650
	.byte	16,187,3,0,15,232,3
	.word	650
	.byte	16,231,3,0,15,252,23
	.word	650
	.byte	16,251,23,0,15,228,63
	.word	650
	.byte	16,227,63,0,15,128,1
	.word	72096
	.byte	16,15,0,14
	.word	72521
	.byte	15,128,31
	.word	650
	.byte	16,255,30,0,15,64
	.word	72028
	.byte	16,7,0,14
	.word	72547
	.byte	15,192,31
	.word	650
	.byte	16,191,31,0,15,16
	.word	68042
	.byte	16,3,0,15,16
	.word	69042
	.byte	16,3,0,15,16
	.word	69104
	.byte	16,3,0,15,208,7
	.word	650
	.byte	16,207,7,0,14
	.word	72287
	.byte	15,240,23
	.word	650
	.byte	16,239,23,0,15,64
	.word	72361
	.byte	16,7,0,14
	.word	72626
	.byte	15,192,23
	.word	650
	.byte	16,191,23,0,15,232,1
	.word	650
	.byte	16,231,1,0,15,180,1
	.word	650
	.byte	16,179,1,0,15,172,1
	.word	650
	.byte	16,171,1,0,15,64
	.word	68231
	.byte	16,15,0,15,64
	.word	650
	.byte	16,63,0,15,64
	.word	67417
	.byte	16,15,0,10
	.byte	'_Ifx_CPU',0,31,166,10,25,128,128,4,13
	.byte	'reserved_0',0
	.word	72431
	.byte	176,32,2,35,0,13
	.byte	'SEGEN',0
	.word	70954
	.byte	4,3,35,176,32,13
	.byte	'reserved_1034',0
	.word	72442
	.byte	208,223,1,3,35,180,32,13
	.byte	'TASK_ASI',0
	.word	71636
	.byte	4,4,35,132,128,2,13
	.byte	'reserved_8008',0
	.word	72455
	.byte	248,1,4,35,136,128,2,13
	.byte	'PMA0',0
	.word	70645
	.byte	4,4,35,128,130,2,13
	.byte	'PMA1',0
	.word	70707
	.byte	4,4,35,132,130,2,13
	.byte	'PMA2',0
	.word	70769
	.byte	4,4,35,136,130,2,13
	.byte	'reserved_810C',0
	.word	72466
	.byte	244,29,4,35,140,130,2,13
	.byte	'DCON2',0
	.word	68542
	.byte	4,4,35,128,160,2,13
	.byte	'reserved_9004',0
	.word	4287
	.byte	8,4,35,132,160,2,13
	.byte	'SMACON',0
	.word	71017
	.byte	4,4,35,140,160,2,13
	.byte	'DSTR',0
	.word	69166
	.byte	4,4,35,144,160,2,13
	.byte	'reserved_9014',0
	.word	2468
	.byte	4,4,35,148,160,2,13
	.byte	'DATR',0
	.word	68290
	.byte	4,4,35,152,160,2,13
	.byte	'DEADD',0
	.word	68666
	.byte	4,4,35,156,160,2,13
	.byte	'DIEAR',0
	.word	68729
	.byte	4,4,35,160,160,2,13
	.byte	'DIETR',0
	.word	68792
	.byte	4,4,35,164,160,2,13
	.byte	'reserved_9028',0
	.word	3658
	.byte	24,4,35,168,160,2,13
	.byte	'DCON0',0
	.word	68479
	.byte	4,4,35,192,160,2,13
	.byte	'reserved_9044',0
	.word	72477
	.byte	188,3,4,35,196,160,2,13
	.byte	'PSTR',0
	.word	70831
	.byte	4,4,35,128,164,2,13
	.byte	'PCON1',0
	.word	70331
	.byte	4,4,35,132,164,2,13
	.byte	'PCON2',0
	.word	70394
	.byte	4,4,35,136,164,2,13
	.byte	'PCON0',0
	.word	70268
	.byte	4,4,35,140,164,2,13
	.byte	'PIEAR',0
	.word	70519
	.byte	4,4,35,144,164,2,13
	.byte	'PIETR',0
	.word	70582
	.byte	4,4,35,148,164,2,13
	.byte	'reserved_9218',0
	.word	72488
	.byte	232,3,4,35,152,164,2,13
	.byte	'COMPAT',0
	.word	67723
	.byte	4,4,35,128,168,2,13
	.byte	'reserved_9404',0
	.word	72499
	.byte	252,23,4,35,132,168,2,13
	.byte	'FPU_TRAP_CON',0
	.word	69352
	.byte	4,4,35,128,192,2,13
	.byte	'FPU_TRAP_PC',0
	.word	69492
	.byte	4,4,35,132,192,2,13
	.byte	'FPU_TRAP_OPC',0
	.word	69422
	.byte	4,4,35,136,192,2,13
	.byte	'reserved_A00C',0
	.word	2468
	.byte	4,4,35,140,192,2,13
	.byte	'FPU_TRAP_SRC1',0
	.word	69561
	.byte	4,4,35,144,192,2,13
	.byte	'FPU_TRAP_SRC2',0
	.word	69632
	.byte	4,4,35,148,192,2,13
	.byte	'FPU_TRAP_SRC3',0
	.word	69703
	.byte	4,4,35,152,192,2,13
	.byte	'reserved_A01C',0
	.word	72510
	.byte	228,63,4,35,156,192,2,13
	.byte	'DPR',0
	.word	72531
	.byte	128,1,4,35,128,128,3,13
	.byte	'reserved_C080',0
	.word	72536
	.byte	128,31,4,35,128,129,3,13
	.byte	'CPR',0
	.word	72556
	.byte	64,4,35,128,160,3,13
	.byte	'reserved_D040',0
	.word	72561
	.byte	192,31,4,35,192,160,3,13
	.byte	'CPXE',0
	.word	72572
	.byte	16,4,35,128,192,3,13
	.byte	'DPRE',0
	.word	72581
	.byte	16,4,35,144,192,3,13
	.byte	'DPWE',0
	.word	72590
	.byte	16,4,35,160,192,3,13
	.byte	'reserved_E030',0
	.word	72599
	.byte	208,7,4,35,176,192,3,13
	.byte	'TPS',0
	.word	72610
	.byte	16,4,35,128,200,3,13
	.byte	'reserved_E410',0
	.word	72615
	.byte	240,23,4,35,144,200,3,13
	.byte	'TR',0
	.word	72635
	.byte	64,4,35,128,224,3,13
	.byte	'reserved_F040',0
	.word	72640
	.byte	192,23,4,35,192,224,3,13
	.byte	'CCTRL',0
	.word	67660
	.byte	4,4,35,128,248,3,13
	.byte	'CCNT',0
	.word	67598
	.byte	4,4,35,132,248,3,13
	.byte	'ICNT',0
	.word	69774
	.byte	4,4,35,136,248,3,13
	.byte	'M1CNT',0
	.word	70019
	.byte	4,4,35,140,248,3,13
	.byte	'M2CNT',0
	.word	70082
	.byte	4,4,35,144,248,3,13
	.byte	'M3CNT',0
	.word	70145
	.byte	4,4,35,148,248,3,13
	.byte	'reserved_FC18',0
	.word	72651
	.byte	232,1,4,35,152,248,3,13
	.byte	'DBGSR',0
	.word	68352
	.byte	4,4,35,128,250,3,13
	.byte	'reserved_FD04',0
	.word	2468
	.byte	4,4,35,132,250,3,13
	.byte	'EXEVT',0
	.word	69228
	.byte	4,4,35,136,250,3,13
	.byte	'CREVT',0
	.word	68104
	.byte	4,4,35,140,250,3,13
	.byte	'SWEVT',0
	.word	71509
	.byte	4,4,35,144,250,3,13
	.byte	'reserved_FD14',0
	.word	54055
	.byte	28,4,35,148,250,3,13
	.byte	'TRIG_ACC',0
	.word	71962
	.byte	4,4,35,176,250,3,13
	.byte	'reserved_FD34',0
	.word	4627
	.byte	12,4,35,180,250,3,13
	.byte	'DMS',0
	.word	68855
	.byte	4,4,35,192,250,3,13
	.byte	'DCX',0
	.word	68605
	.byte	4,4,35,196,250,3,13
	.byte	'DBGTCR',0
	.word	68415
	.byte	4,4,35,200,250,3,13
	.byte	'reserved_FD4C',0
	.word	72662
	.byte	180,1,4,35,204,250,3,13
	.byte	'PCXI',0
	.word	70457
	.byte	4,4,35,128,252,3,13
	.byte	'PSW',0
	.word	70893
	.byte	4,4,35,132,252,3,13
	.byte	'PC',0
	.word	70208
	.byte	4,4,35,136,252,3,13
	.byte	'reserved_FE0C',0
	.word	4287
	.byte	8,4,35,140,252,3,13
	.byte	'SYSCON',0
	.word	71572
	.byte	4,4,35,148,252,3,13
	.byte	'CPU_ID',0
	.word	67978
	.byte	4,4,35,152,252,3,13
	.byte	'CORE_ID',0
	.word	67787
	.byte	4,4,35,156,252,3,13
	.byte	'BIV',0
	.word	67476
	.byte	4,4,35,160,252,3,13
	.byte	'BTV',0
	.word	67537
	.byte	4,4,35,164,252,3,13
	.byte	'ISP',0
	.word	69897
	.byte	4,4,35,168,252,3,13
	.byte	'ICR',0
	.word	69836
	.byte	4,4,35,172,252,3,13
	.byte	'reserved_FE30',0
	.word	4287
	.byte	8,4,35,176,252,3,13
	.byte	'FCX',0
	.word	69291
	.byte	4,4,35,184,252,3,13
	.byte	'LCX',0
	.word	69958
	.byte	4,4,35,188,252,3,13
	.byte	'reserved_FE40',0
	.word	54046
	.byte	16,4,35,192,252,3,13
	.byte	'CUS_ID',0
	.word	68167
	.byte	4,4,35,208,252,3,13
	.byte	'reserved_FE54',0
	.word	72673
	.byte	172,1,4,35,212,252,3,13
	.byte	'D',0
	.word	72684
	.byte	64,4,35,128,254,3,13
	.byte	'reserved_FF40',0
	.word	72693
	.byte	64,4,35,192,254,3,13
	.byte	'A',0
	.word	72702
	.byte	64,4,35,128,255,3,13
	.byte	'reserved_FFC0',0
	.word	72693
	.byte	64,4,35,192,255,3,0,14
	.word	72711
	.byte	30
	.byte	'Ifx_CPU',0,31,130,11,3
	.word	74502
	.byte	17,8,127,9,1,18
	.byte	'IfxCpu_Id_0',0,0,18
	.byte	'IfxCpu_Id_1',0,1,18
	.byte	'IfxCpu_Id_none',0,2,0,30
	.byte	'IfxCpu_Id',0,8,132,1,3
	.word	74524
	.byte	30
	.byte	'IfxCpu_ResourceCpu',0,8,161,1,3
	.word	9989
	.byte	30
	.byte	'Ifx_SRC_SRCR_Bits',0,11,62,3
	.word	10582
	.byte	30
	.byte	'Ifx_SRC_SRCR',0,11,75,3
	.word	10872
	.byte	10
	.byte	'_Ifx_SRC_AGBT',0,11,86,25,4,13
	.byte	'SR',0
	.word	10872
	.byte	4,2,35,0,0,14
	.word	74669
	.byte	30
	.byte	'Ifx_SRC_AGBT',0,11,89,3
	.word	74701
	.byte	10
	.byte	'_Ifx_SRC_ASCLIN',0,11,92,25,12,13
	.byte	'TX',0
	.word	10872
	.byte	4,2,35,0,13
	.byte	'RX',0
	.word	10872
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	10872
	.byte	4,2,35,8,0,14
	.word	74727
	.byte	30
	.byte	'Ifx_SRC_ASCLIN',0,11,97,3
	.word	74786
	.byte	10
	.byte	'_Ifx_SRC_BCUSPB',0,11,100,25,4,13
	.byte	'SBSRC',0
	.word	10872
	.byte	4,2,35,0,0,14
	.word	74814
	.byte	30
	.byte	'Ifx_SRC_BCUSPB',0,11,103,3
	.word	74851
	.byte	15,64
	.word	10872
	.byte	16,15,0,10
	.byte	'_Ifx_SRC_CAN',0,11,106,25,64,13
	.byte	'INT',0
	.word	74879
	.byte	64,2,35,0,0,14
	.word	74888
	.byte	30
	.byte	'Ifx_SRC_CAN',0,11,109,3
	.word	74920
	.byte	10
	.byte	'_Ifx_SRC_CCU6',0,11,112,25,16,13
	.byte	'SR0',0
	.word	10872
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10872
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	10872
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	10872
	.byte	4,2,35,12,0,14
	.word	74945
	.byte	30
	.byte	'Ifx_SRC_CCU6',0,11,118,3
	.word	75017
	.byte	15,8
	.word	10872
	.byte	16,1,0,10
	.byte	'_Ifx_SRC_CERBERUS',0,11,121,25,8,13
	.byte	'SR',0
	.word	75043
	.byte	8,2,35,0,0,14
	.word	75052
	.byte	30
	.byte	'Ifx_SRC_CERBERUS',0,11,124,3
	.word	75088
	.byte	10
	.byte	'_Ifx_SRC_CIF',0,11,127,25,16,13
	.byte	'MI',0
	.word	10872
	.byte	4,2,35,0,13
	.byte	'MIEP',0
	.word	10872
	.byte	4,2,35,4,13
	.byte	'ISP',0
	.word	10872
	.byte	4,2,35,8,13
	.byte	'MJPEG',0
	.word	10872
	.byte	4,2,35,12,0,14
	.word	75118
	.byte	30
	.byte	'Ifx_SRC_CIF',0,11,133,1,3
	.word	75191
	.byte	10
	.byte	'_Ifx_SRC_CPU',0,11,136,1,25,4,13
	.byte	'SBSRC',0
	.word	10872
	.byte	4,2,35,0,0,14
	.word	75217
	.byte	30
	.byte	'Ifx_SRC_CPU',0,11,139,1,3
	.word	75252
	.byte	15,192,1
	.word	10872
	.byte	16,47,0,10
	.byte	'_Ifx_SRC_DMA',0,11,142,1,25,208,1,13
	.byte	'ERR',0
	.word	10872
	.byte	4,2,35,0,13
	.byte	'reserved_4',0
	.word	4627
	.byte	12,2,35,4,13
	.byte	'CH',0
	.word	75278
	.byte	192,1,2,35,16,0,14
	.word	75288
	.byte	30
	.byte	'Ifx_SRC_DMA',0,11,147,1,3
	.word	75355
	.byte	10
	.byte	'_Ifx_SRC_DSADC',0,11,150,1,25,8,13
	.byte	'SRM',0
	.word	10872
	.byte	4,2,35,0,13
	.byte	'SRA',0
	.word	10872
	.byte	4,2,35,4,0,14
	.word	75381
	.byte	30
	.byte	'Ifx_SRC_DSADC',0,11,154,1,3
	.word	75429
	.byte	10
	.byte	'_Ifx_SRC_EMEM',0,11,157,1,25,4,13
	.byte	'SR',0
	.word	10872
	.byte	4,2,35,0,0,14
	.word	75457
	.byte	30
	.byte	'Ifx_SRC_EMEM',0,11,160,1,3
	.word	75490
	.byte	10
	.byte	'_Ifx_SRC_ERAY',0,11,163,1,25,80,13
	.byte	'INT',0
	.word	75043
	.byte	8,2,35,0,13
	.byte	'TINT',0
	.word	75043
	.byte	8,2,35,8,13
	.byte	'NDAT',0
	.word	75043
	.byte	8,2,35,16,13
	.byte	'MBSC',0
	.word	75043
	.byte	8,2,35,24,13
	.byte	'OBUSY',0
	.word	10872
	.byte	4,2,35,32,13
	.byte	'IBUSY',0
	.word	10872
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	54064
	.byte	40,2,35,40,0,14
	.word	75517
	.byte	30
	.byte	'Ifx_SRC_ERAY',0,11,172,1,3
	.word	75644
	.byte	10
	.byte	'_Ifx_SRC_ETH',0,11,175,1,25,4,13
	.byte	'SR',0
	.word	10872
	.byte	4,2,35,0,0,14
	.word	75671
	.byte	30
	.byte	'Ifx_SRC_ETH',0,11,178,1,3
	.word	75703
	.byte	10
	.byte	'_Ifx_SRC_FCE',0,11,181,1,25,4,13
	.byte	'SR',0
	.word	10872
	.byte	4,2,35,0,0,14
	.word	75729
	.byte	30
	.byte	'Ifx_SRC_FCE',0,11,184,1,3
	.word	75761
	.byte	10
	.byte	'_Ifx_SRC_FFT',0,11,187,1,25,12,13
	.byte	'DONE',0
	.word	10872
	.byte	4,2,35,0,13
	.byte	'ERR',0
	.word	10872
	.byte	4,2,35,4,13
	.byte	'RFS',0
	.word	10872
	.byte	4,2,35,8,0,14
	.word	75787
	.byte	30
	.byte	'Ifx_SRC_FFT',0,11,192,1,3
	.word	75847
	.byte	10
	.byte	'_Ifx_SRC_GPSR',0,11,195,1,25,32,13
	.byte	'SR0',0
	.word	10872
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10872
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	10872
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	10872
	.byte	4,2,35,12,13
	.byte	'reserved_10',0
	.word	54046
	.byte	16,2,35,16,0,14
	.word	75873
	.byte	30
	.byte	'Ifx_SRC_GPSR',0,11,202,1,3
	.word	75967
	.byte	10
	.byte	'_Ifx_SRC_GPT12',0,11,205,1,25,48,13
	.byte	'CIRQ',0
	.word	10872
	.byte	4,2,35,0,13
	.byte	'T2',0
	.word	10872
	.byte	4,2,35,4,13
	.byte	'T3',0
	.word	10872
	.byte	4,2,35,8,13
	.byte	'T4',0
	.word	10872
	.byte	4,2,35,12,13
	.byte	'T5',0
	.word	10872
	.byte	4,2,35,16,13
	.byte	'T6',0
	.word	10872
	.byte	4,2,35,20,13
	.byte	'reserved_18',0
	.word	3658
	.byte	24,2,35,24,0,14
	.word	75994
	.byte	30
	.byte	'Ifx_SRC_GPT12',0,11,214,1,3
	.word	76111
	.byte	15,12
	.word	10872
	.byte	16,2,0,15,32
	.word	10872
	.byte	16,7,0,15,32
	.word	76148
	.byte	16,0,0,15,88
	.word	650
	.byte	16,87,0,15,108
	.word	10872
	.byte	16,26,0,15,96
	.word	650
	.byte	16,95,0,15,96
	.word	76148
	.byte	16,2,0,15,160,3
	.word	650
	.byte	16,159,3,0,15,64
	.word	76148
	.byte	16,1,0,15,192,3
	.word	650
	.byte	16,191,3,0,15,16
	.word	10872
	.byte	16,3,0,15,64
	.word	76233
	.byte	16,3,0,15,192,2
	.word	650
	.byte	16,191,2,0,15,52
	.word	650
	.byte	16,51,0,10
	.byte	'_Ifx_SRC_GTM',0,11,217,1,25,204,18,13
	.byte	'AEIIRQ',0
	.word	10872
	.byte	4,2,35,0,13
	.byte	'ARUIRQ',0
	.word	76139
	.byte	12,2,35,4,13
	.byte	'reserved_10',0
	.word	2468
	.byte	4,2,35,16,13
	.byte	'BRCIRQ',0
	.word	10872
	.byte	4,2,35,20,13
	.byte	'CMPIRQ',0
	.word	10872
	.byte	4,2,35,24,13
	.byte	'SPEIRQ',0
	.word	75043
	.byte	8,2,35,28,13
	.byte	'reserved_24',0
	.word	4287
	.byte	8,2,35,36,13
	.byte	'PSM',0
	.word	76157
	.byte	32,2,35,44,13
	.byte	'reserved_4C',0
	.word	76166
	.byte	88,2,35,76,13
	.byte	'DPLL',0
	.word	76175
	.byte	108,3,35,164,1,13
	.byte	'reserved_110',0
	.word	76184
	.byte	96,3,35,144,2,13
	.byte	'ERR',0
	.word	10872
	.byte	4,3,35,240,2,13
	.byte	'reserved_174',0
	.word	4627
	.byte	12,3,35,244,2,13
	.byte	'TIM',0
	.word	76193
	.byte	96,3,35,128,3,13
	.byte	'reserved_1E0',0
	.word	76202
	.byte	160,3,3,35,224,3,13
	.byte	'MCS',0
	.word	76193
	.byte	96,3,35,128,7,13
	.byte	'reserved_3E0',0
	.word	76202
	.byte	160,3,3,35,224,7,13
	.byte	'TOM',0
	.word	76213
	.byte	64,3,35,128,11,13
	.byte	'reserved_5C0',0
	.word	76222
	.byte	192,3,3,35,192,11,13
	.byte	'ATOM',0
	.word	76242
	.byte	64,3,35,128,15,13
	.byte	'reserved_7C0',0
	.word	76251
	.byte	192,2,3,35,192,15,13
	.byte	'MCSW0',0
	.word	76139
	.byte	12,3,35,128,18,13
	.byte	'reserved_90C',0
	.word	76262
	.byte	52,3,35,140,18,13
	.byte	'MCSW1',0
	.word	76139
	.byte	12,3,35,192,18,0,14
	.word	76271
	.byte	30
	.byte	'Ifx_SRC_GTM',0,11,243,1,3
	.word	76731
	.byte	10
	.byte	'_Ifx_SRC_HSCT',0,11,246,1,25,4,13
	.byte	'SR',0
	.word	10872
	.byte	4,2,35,0,0,14
	.word	76757
	.byte	30
	.byte	'Ifx_SRC_HSCT',0,11,249,1,3
	.word	76790
	.byte	10
	.byte	'_Ifx_SRC_HSSL',0,11,252,1,25,16,13
	.byte	'COK',0
	.word	10872
	.byte	4,2,35,0,13
	.byte	'RDI',0
	.word	10872
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	10872
	.byte	4,2,35,8,13
	.byte	'TRG',0
	.word	10872
	.byte	4,2,35,12,0,14
	.word	76817
	.byte	30
	.byte	'Ifx_SRC_HSSL',0,11,130,2,3
	.word	76890
	.byte	15,56
	.word	650
	.byte	16,55,0,10
	.byte	'_Ifx_SRC_I2C',0,11,133,2,25,80,13
	.byte	'BREQ',0
	.word	10872
	.byte	4,2,35,0,13
	.byte	'LBREQ',0
	.word	10872
	.byte	4,2,35,4,13
	.byte	'SREQ',0
	.word	10872
	.byte	4,2,35,8,13
	.byte	'LSREQ',0
	.word	10872
	.byte	4,2,35,12,13
	.byte	'ERR',0
	.word	10872
	.byte	4,2,35,16,13
	.byte	'P',0
	.word	10872
	.byte	4,2,35,20,13
	.byte	'reserved_18',0
	.word	76917
	.byte	56,2,35,24,0,14
	.word	76926
	.byte	30
	.byte	'Ifx_SRC_I2C',0,11,142,2,3
	.word	77049
	.byte	10
	.byte	'_Ifx_SRC_LMU',0,11,145,2,25,4,13
	.byte	'SR',0
	.word	10872
	.byte	4,2,35,0,0,14
	.word	77075
	.byte	30
	.byte	'Ifx_SRC_LMU',0,11,148,2,3
	.word	77107
	.byte	10
	.byte	'_Ifx_SRC_MSC',0,11,151,2,25,20,13
	.byte	'SR0',0
	.word	10872
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10872
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	10872
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	10872
	.byte	4,2,35,12,13
	.byte	'SR4',0
	.word	10872
	.byte	4,2,35,16,0,14
	.word	77133
	.byte	30
	.byte	'Ifx_SRC_MSC',0,11,158,2,3
	.word	77218
	.byte	10
	.byte	'_Ifx_SRC_PMU',0,11,161,2,25,4,13
	.byte	'SR',0
	.word	10872
	.byte	4,2,35,0,0,14
	.word	77244
	.byte	30
	.byte	'Ifx_SRC_PMU',0,11,164,2,3
	.word	77276
	.byte	10
	.byte	'_Ifx_SRC_PSI5',0,11,167,2,25,32,13
	.byte	'SR',0
	.word	76148
	.byte	32,2,35,0,0,14
	.word	77302
	.byte	30
	.byte	'Ifx_SRC_PSI5',0,11,170,2,3
	.word	77335
	.byte	10
	.byte	'_Ifx_SRC_PSI5S',0,11,173,2,25,32,13
	.byte	'SR',0
	.word	76148
	.byte	32,2,35,0,0,14
	.word	77362
	.byte	30
	.byte	'Ifx_SRC_PSI5S',0,11,176,2,3
	.word	77396
	.byte	10
	.byte	'_Ifx_SRC_QSPI',0,11,179,2,25,24,13
	.byte	'TX',0
	.word	10872
	.byte	4,2,35,0,13
	.byte	'RX',0
	.word	10872
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	10872
	.byte	4,2,35,8,13
	.byte	'PT',0
	.word	10872
	.byte	4,2,35,12,13
	.byte	'HC',0
	.word	10872
	.byte	4,2,35,16,13
	.byte	'U',0
	.word	10872
	.byte	4,2,35,20,0,14
	.word	77424
	.byte	30
	.byte	'Ifx_SRC_QSPI',0,11,187,2,3
	.word	77517
	.byte	10
	.byte	'_Ifx_SRC_SCR',0,11,190,2,25,4,13
	.byte	'SR',0
	.word	10872
	.byte	4,2,35,0,0,14
	.word	77544
	.byte	30
	.byte	'Ifx_SRC_SCR',0,11,193,2,3
	.word	77576
	.byte	10
	.byte	'_Ifx_SRC_SCU',0,11,196,2,25,20,13
	.byte	'DTS',0
	.word	10872
	.byte	4,2,35,0,13
	.byte	'ERU',0
	.word	76233
	.byte	16,2,35,4,0,14
	.word	77602
	.byte	30
	.byte	'Ifx_SRC_SCU',0,11,200,2,3
	.word	77648
	.byte	15,24
	.word	10872
	.byte	16,5,0,10
	.byte	'_Ifx_SRC_SENT',0,11,203,2,25,24,13
	.byte	'SR',0
	.word	77674
	.byte	24,2,35,0,0,14
	.word	77683
	.byte	30
	.byte	'Ifx_SRC_SENT',0,11,206,2,3
	.word	77716
	.byte	10
	.byte	'_Ifx_SRC_SMU',0,11,209,2,25,12,13
	.byte	'SR',0
	.word	76139
	.byte	12,2,35,0,0,14
	.word	77743
	.byte	30
	.byte	'Ifx_SRC_SMU',0,11,212,2,3
	.word	77775
	.byte	10
	.byte	'_Ifx_SRC_STM',0,11,215,2,25,8,13
	.byte	'SR0',0
	.word	10872
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10872
	.byte	4,2,35,4,0,14
	.word	77801
	.byte	30
	.byte	'Ifx_SRC_STM',0,11,219,2,3
	.word	77847
	.byte	10
	.byte	'_Ifx_SRC_VADCCG',0,11,222,2,25,16,13
	.byte	'SR0',0
	.word	10872
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10872
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	10872
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	10872
	.byte	4,2,35,12,0,14
	.word	77873
	.byte	30
	.byte	'Ifx_SRC_VADCCG',0,11,228,2,3
	.word	77948
	.byte	10
	.byte	'_Ifx_SRC_VADCG',0,11,231,2,25,16,13
	.byte	'SR0',0
	.word	10872
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10872
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	10872
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	10872
	.byte	4,2,35,12,0,14
	.word	77977
	.byte	30
	.byte	'Ifx_SRC_VADCG',0,11,237,2,3
	.word	78051
	.byte	10
	.byte	'_Ifx_SRC_XBAR',0,11,240,2,25,4,13
	.byte	'SRC',0
	.word	10872
	.byte	4,2,35,0,0,14
	.word	78079
	.byte	30
	.byte	'Ifx_SRC_XBAR',0,11,243,2,3
	.word	78113
	.byte	15,4
	.word	74669
	.byte	16,0,0,14
	.word	78140
	.byte	10
	.byte	'_Ifx_SRC_GAGBT',0,11,128,3,25,4,13
	.byte	'AGBT',0
	.word	78149
	.byte	4,2,35,0,0,14
	.word	78154
	.byte	30
	.byte	'Ifx_SRC_GAGBT',0,11,131,3,3
	.word	78190
	.byte	15,48
	.word	74727
	.byte	16,3,0,14
	.word	78218
	.byte	10
	.byte	'_Ifx_SRC_GASCLIN',0,11,134,3,25,48,13
	.byte	'ASCLIN',0
	.word	78227
	.byte	48,2,35,0,0,14
	.word	78232
	.byte	30
	.byte	'Ifx_SRC_GASCLIN',0,11,137,3,3
	.word	78272
	.byte	14
	.word	74814
	.byte	10
	.byte	'_Ifx_SRC_GBCU',0,11,140,3,25,4,13
	.byte	'SPB',0
	.word	78302
	.byte	4,2,35,0,0,14
	.word	78307
	.byte	30
	.byte	'Ifx_SRC_GBCU',0,11,143,3,3
	.word	78341
	.byte	15,64
	.word	74888
	.byte	16,0,0,14
	.word	78368
	.byte	10
	.byte	'_Ifx_SRC_GCAN',0,11,146,3,25,64,13
	.byte	'CAN',0
	.word	78377
	.byte	64,2,35,0,0,14
	.word	78382
	.byte	30
	.byte	'Ifx_SRC_GCAN',0,11,149,3,3
	.word	78416
	.byte	15,32
	.word	74945
	.byte	16,1,0,14
	.word	78443
	.byte	10
	.byte	'_Ifx_SRC_GCCU6',0,11,152,3,25,32,13
	.byte	'CCU6',0
	.word	78452
	.byte	32,2,35,0,0,14
	.word	78457
	.byte	30
	.byte	'Ifx_SRC_GCCU6',0,11,155,3,3
	.word	78493
	.byte	14
	.word	75052
	.byte	10
	.byte	'_Ifx_SRC_GCERBERUS',0,11,158,3,25,8,13
	.byte	'CERBERUS',0
	.word	78521
	.byte	8,2,35,0,0,14
	.word	78526
	.byte	30
	.byte	'Ifx_SRC_GCERBERUS',0,11,161,3,3
	.word	78570
	.byte	15,16
	.word	75118
	.byte	16,0,0,14
	.word	78602
	.byte	10
	.byte	'_Ifx_SRC_GCIF',0,11,164,3,25,16,13
	.byte	'CIF',0
	.word	78611
	.byte	16,2,35,0,0,14
	.word	78616
	.byte	30
	.byte	'Ifx_SRC_GCIF',0,11,167,3,3
	.word	78650
	.byte	15,8
	.word	75217
	.byte	16,1,0,14
	.word	78677
	.byte	10
	.byte	'_Ifx_SRC_GCPU',0,11,170,3,25,8,13
	.byte	'CPU',0
	.word	78686
	.byte	8,2,35,0,0,14
	.word	78691
	.byte	30
	.byte	'Ifx_SRC_GCPU',0,11,173,3,3
	.word	78725
	.byte	15,208,1
	.word	75288
	.byte	16,0,0,14
	.word	78752
	.byte	10
	.byte	'_Ifx_SRC_GDMA',0,11,176,3,25,208,1,13
	.byte	'DMA',0
	.word	78762
	.byte	208,1,2,35,0,0,14
	.word	78767
	.byte	30
	.byte	'Ifx_SRC_GDMA',0,11,179,3,3
	.word	78803
	.byte	14
	.word	75381
	.byte	14
	.word	75381
	.byte	14
	.word	75381
	.byte	10
	.byte	'_Ifx_SRC_GDSADC',0,11,182,3,25,32,13
	.byte	'DSADC0',0
	.word	78830
	.byte	8,2,35,0,13
	.byte	'reserved_8',0
	.word	4287
	.byte	8,2,35,8,13
	.byte	'DSADC2',0
	.word	78835
	.byte	8,2,35,16,13
	.byte	'DSADC3',0
	.word	78840
	.byte	8,2,35,24,0,14
	.word	78845
	.byte	30
	.byte	'Ifx_SRC_GDSADC',0,11,188,3,3
	.word	78936
	.byte	15,4
	.word	75457
	.byte	16,0,0,14
	.word	78965
	.byte	10
	.byte	'_Ifx_SRC_GEMEM',0,11,191,3,25,4,13
	.byte	'EMEM',0
	.word	78974
	.byte	4,2,35,0,0,14
	.word	78979
	.byte	30
	.byte	'Ifx_SRC_GEMEM',0,11,194,3,3
	.word	79015
	.byte	15,80
	.word	75517
	.byte	16,0,0,14
	.word	79043
	.byte	10
	.byte	'_Ifx_SRC_GERAY',0,11,197,3,25,80,13
	.byte	'ERAY',0
	.word	79052
	.byte	80,2,35,0,0,14
	.word	79057
	.byte	30
	.byte	'Ifx_SRC_GERAY',0,11,200,3,3
	.word	79093
	.byte	15,4
	.word	75671
	.byte	16,0,0,14
	.word	79121
	.byte	10
	.byte	'_Ifx_SRC_GETH',0,11,203,3,25,4,13
	.byte	'ETH',0
	.word	79130
	.byte	4,2,35,0,0,14
	.word	79135
	.byte	30
	.byte	'Ifx_SRC_GETH',0,11,206,3,3
	.word	79169
	.byte	15,4
	.word	75729
	.byte	16,0,0,14
	.word	79196
	.byte	10
	.byte	'_Ifx_SRC_GFCE',0,11,209,3,25,4,13
	.byte	'FCE',0
	.word	79205
	.byte	4,2,35,0,0,14
	.word	79210
	.byte	30
	.byte	'Ifx_SRC_GFCE',0,11,212,3,3
	.word	79244
	.byte	15,12
	.word	75787
	.byte	16,0,0,14
	.word	79271
	.byte	10
	.byte	'_Ifx_SRC_GFFT',0,11,215,3,25,12,13
	.byte	'FFT',0
	.word	79280
	.byte	12,2,35,0,0,14
	.word	79285
	.byte	30
	.byte	'Ifx_SRC_GFFT',0,11,218,3,3
	.word	79319
	.byte	15,64
	.word	75873
	.byte	16,1,0,14
	.word	79346
	.byte	10
	.byte	'_Ifx_SRC_GGPSR',0,11,221,3,25,64,13
	.byte	'GPSR',0
	.word	79355
	.byte	64,2,35,0,0,14
	.word	79360
	.byte	30
	.byte	'Ifx_SRC_GGPSR',0,11,224,3,3
	.word	79396
	.byte	15,48
	.word	75994
	.byte	16,0,0,14
	.word	79424
	.byte	10
	.byte	'_Ifx_SRC_GGPT12',0,11,227,3,25,48,13
	.byte	'GPT12',0
	.word	79433
	.byte	48,2,35,0,0,14
	.word	79438
	.byte	30
	.byte	'Ifx_SRC_GGPT12',0,11,230,3,3
	.word	79476
	.byte	15,204,18
	.word	76271
	.byte	16,0,0,14
	.word	79505
	.byte	10
	.byte	'_Ifx_SRC_GGTM',0,11,233,3,25,204,18,13
	.byte	'GTM',0
	.word	79515
	.byte	204,18,2,35,0,0,14
	.word	79520
	.byte	30
	.byte	'Ifx_SRC_GGTM',0,11,236,3,3
	.word	79556
	.byte	15,4
	.word	76757
	.byte	16,0,0,14
	.word	79583
	.byte	10
	.byte	'_Ifx_SRC_GHSCT',0,11,239,3,25,4,13
	.byte	'HSCT',0
	.word	79592
	.byte	4,2,35,0,0,14
	.word	79597
	.byte	30
	.byte	'Ifx_SRC_GHSCT',0,11,242,3,3
	.word	79633
	.byte	15,64
	.word	76817
	.byte	16,3,0,14
	.word	79661
	.byte	10
	.byte	'_Ifx_SRC_GHSSL',0,11,245,3,25,68,13
	.byte	'HSSL',0
	.word	79670
	.byte	64,2,35,0,13
	.byte	'EXI',0
	.word	10872
	.byte	4,2,35,64,0,14
	.word	79675
	.byte	30
	.byte	'Ifx_SRC_GHSSL',0,11,249,3,3
	.word	79724
	.byte	15,80
	.word	76926
	.byte	16,0,0,14
	.word	79752
	.byte	10
	.byte	'_Ifx_SRC_GI2C',0,11,252,3,25,80,13
	.byte	'I2C',0
	.word	79761
	.byte	80,2,35,0,0,14
	.word	79766
	.byte	30
	.byte	'Ifx_SRC_GI2C',0,11,255,3,3
	.word	79800
	.byte	15,4
	.word	77075
	.byte	16,0,0,14
	.word	79827
	.byte	10
	.byte	'_Ifx_SRC_GLMU',0,11,130,4,25,4,13
	.byte	'LMU',0
	.word	79836
	.byte	4,2,35,0,0,14
	.word	79841
	.byte	30
	.byte	'Ifx_SRC_GLMU',0,11,133,4,3
	.word	79875
	.byte	15,40
	.word	77133
	.byte	16,1,0,14
	.word	79902
	.byte	10
	.byte	'_Ifx_SRC_GMSC',0,11,136,4,25,40,13
	.byte	'MSC',0
	.word	79911
	.byte	40,2,35,0,0,14
	.word	79916
	.byte	30
	.byte	'Ifx_SRC_GMSC',0,11,139,4,3
	.word	79950
	.byte	15,8
	.word	77244
	.byte	16,1,0,14
	.word	79977
	.byte	10
	.byte	'_Ifx_SRC_GPMU',0,11,142,4,25,8,13
	.byte	'PMU',0
	.word	79986
	.byte	8,2,35,0,0,14
	.word	79991
	.byte	30
	.byte	'Ifx_SRC_GPMU',0,11,145,4,3
	.word	80025
	.byte	15,32
	.word	77302
	.byte	16,0,0,14
	.word	80052
	.byte	10
	.byte	'_Ifx_SRC_GPSI5',0,11,148,4,25,32,13
	.byte	'PSI5',0
	.word	80061
	.byte	32,2,35,0,0,14
	.word	80066
	.byte	30
	.byte	'Ifx_SRC_GPSI5',0,11,151,4,3
	.word	80102
	.byte	15,32
	.word	77362
	.byte	16,0,0,14
	.word	80130
	.byte	10
	.byte	'_Ifx_SRC_GPSI5S',0,11,154,4,25,32,13
	.byte	'PSI5S',0
	.word	80139
	.byte	32,2,35,0,0,14
	.word	80144
	.byte	30
	.byte	'Ifx_SRC_GPSI5S',0,11,157,4,3
	.word	80182
	.byte	15,96
	.word	77424
	.byte	16,3,0,14
	.word	80211
	.byte	10
	.byte	'_Ifx_SRC_GQSPI',0,11,160,4,25,96,13
	.byte	'QSPI',0
	.word	80220
	.byte	96,2,35,0,0,14
	.word	80225
	.byte	30
	.byte	'Ifx_SRC_GQSPI',0,11,163,4,3
	.word	80261
	.byte	15,4
	.word	77544
	.byte	16,0,0,14
	.word	80289
	.byte	10
	.byte	'_Ifx_SRC_GSCR',0,11,166,4,25,4,13
	.byte	'SCR',0
	.word	80298
	.byte	4,2,35,0,0,14
	.word	80303
	.byte	30
	.byte	'Ifx_SRC_GSCR',0,11,169,4,3
	.word	80337
	.byte	14
	.word	77602
	.byte	10
	.byte	'_Ifx_SRC_GSCU',0,11,172,4,25,20,13
	.byte	'SCU',0
	.word	80364
	.byte	20,2,35,0,0,14
	.word	80369
	.byte	30
	.byte	'Ifx_SRC_GSCU',0,11,175,4,3
	.word	80403
	.byte	15,24
	.word	77683
	.byte	16,0,0,14
	.word	80430
	.byte	10
	.byte	'_Ifx_SRC_GSENT',0,11,178,4,25,24,13
	.byte	'SENT',0
	.word	80439
	.byte	24,2,35,0,0,14
	.word	80444
	.byte	30
	.byte	'Ifx_SRC_GSENT',0,11,181,4,3
	.word	80480
	.byte	15,12
	.word	77743
	.byte	16,0,0,14
	.word	80508
	.byte	10
	.byte	'_Ifx_SRC_GSMU',0,11,184,4,25,12,13
	.byte	'SMU',0
	.word	80517
	.byte	12,2,35,0,0,14
	.word	80522
	.byte	30
	.byte	'Ifx_SRC_GSMU',0,11,187,4,3
	.word	80556
	.byte	15,16
	.word	77801
	.byte	16,1,0,14
	.word	80583
	.byte	10
	.byte	'_Ifx_SRC_GSTM',0,11,190,4,25,16,13
	.byte	'STM',0
	.word	80592
	.byte	16,2,35,0,0,14
	.word	80597
	.byte	30
	.byte	'Ifx_SRC_GSTM',0,11,193,4,3
	.word	80631
	.byte	15,64
	.word	77977
	.byte	16,3,0,14
	.word	80658
	.byte	15,224,1
	.word	650
	.byte	16,223,1,0,15,32
	.word	77873
	.byte	16,1,0,14
	.word	80683
	.byte	10
	.byte	'_Ifx_SRC_GVADC',0,11,196,4,25,192,2,13
	.byte	'G',0
	.word	80667
	.byte	64,2,35,0,13
	.byte	'reserved_40',0
	.word	80672
	.byte	224,1,2,35,64,13
	.byte	'CG',0
	.word	80692
	.byte	32,3,35,160,2,0,14
	.word	80697
	.byte	30
	.byte	'Ifx_SRC_GVADC',0,11,201,4,3
	.word	80766
	.byte	14
	.word	78079
	.byte	10
	.byte	'_Ifx_SRC_GXBAR',0,11,204,4,25,4,13
	.byte	'XBAR',0
	.word	80794
	.byte	4,2,35,0,0,14
	.word	80799
	.byte	30
	.byte	'Ifx_SRC_GXBAR',0,11,207,4,3
	.word	80835
	.byte	30
	.byte	'Ifx_STM_ACCEN0_Bits',0,16,79,3
	.word	20802
	.byte	30
	.byte	'Ifx_STM_ACCEN1_Bits',0,16,85,3
	.word	20713
	.byte	30
	.byte	'Ifx_STM_CAP_Bits',0,16,91,3
	.word	19243
	.byte	30
	.byte	'Ifx_STM_CAPSV_Bits',0,16,97,3
	.word	20120
	.byte	30
	.byte	'Ifx_STM_CLC_Bits',0,16,107,3
	.word	18366
	.byte	30
	.byte	'Ifx_STM_CMCON_Bits',0,16,120,3
	.word	19421
	.byte	30
	.byte	'Ifx_STM_CMP_Bits',0,16,126,3
	.word	19330
	.byte	30
	.byte	'Ifx_STM_ICR_Bits',0,16,139,1,3
	.word	19652
	.byte	30
	.byte	'Ifx_STM_ID_Bits',0,16,147,1,3
	.word	18522
	.byte	30
	.byte	'Ifx_STM_ISCR_Bits',0,16,157,1,3
	.word	19869
	.byte	30
	.byte	'Ifx_STM_KRST0_Bits',0,16,165,1,3
	.word	20590
	.byte	30
	.byte	'Ifx_STM_KRST1_Bits',0,16,172,1,3
	.word	20486
	.byte	30
	.byte	'Ifx_STM_KRSTCLR_Bits',0,16,179,1,3
	.word	20380
	.byte	30
	.byte	'Ifx_STM_OCS_Bits',0,16,189,1,3
	.word	20220
	.byte	30
	.byte	'Ifx_STM_TIM0_Bits',0,16,195,1,3
	.word	18644
	.byte	30
	.byte	'Ifx_STM_TIM0SV_Bits',0,16,201,1,3
	.word	20033
	.byte	30
	.byte	'Ifx_STM_TIM1_Bits',0,16,207,1,3
	.word	18729
	.byte	30
	.byte	'Ifx_STM_TIM2_Bits',0,16,213,1,3
	.word	18814
	.byte	30
	.byte	'Ifx_STM_TIM3_Bits',0,16,219,1,3
	.word	18899
	.byte	30
	.byte	'Ifx_STM_TIM4_Bits',0,16,225,1,3
	.word	18985
	.byte	30
	.byte	'Ifx_STM_TIM5_Bits',0,16,231,1,3
	.word	19071
	.byte	30
	.byte	'Ifx_STM_TIM6_Bits',0,16,237,1,3
	.word	19157
	.byte	30
	.byte	'Ifx_STM_ACCEN0',0,16,250,1,3
	.word	21331
	.byte	30
	.byte	'Ifx_STM_ACCEN1',0,16,130,2,3
	.word	20762
	.byte	30
	.byte	'Ifx_STM_CAP',0,16,138,2,3
	.word	19290
	.byte	30
	.byte	'Ifx_STM_CAPSV',0,16,146,2,3
	.word	20169
	.byte	30
	.byte	'Ifx_STM_CLC',0,16,154,2,3
	.word	18482
	.byte	30
	.byte	'Ifx_STM_CMCON',0,16,162,2,3
	.word	19612
	.byte	30
	.byte	'Ifx_STM_CMP',0,16,170,2,3
	.word	19372
	.byte	30
	.byte	'Ifx_STM_ICR',0,16,178,2,3
	.word	19829
	.byte	30
	.byte	'Ifx_STM_ID',0,16,186,2,3
	.word	18604
	.byte	30
	.byte	'Ifx_STM_ISCR',0,16,194,2,3
	.word	19993
	.byte	30
	.byte	'Ifx_STM_KRST0',0,16,202,2,3
	.word	20673
	.byte	30
	.byte	'Ifx_STM_KRST1',0,16,210,2,3
	.word	20550
	.byte	30
	.byte	'Ifx_STM_KRSTCLR',0,16,218,2,3
	.word	20446
	.byte	30
	.byte	'Ifx_STM_OCS',0,16,226,2,3
	.word	20340
	.byte	30
	.byte	'Ifx_STM_TIM0',0,16,234,2,3
	.word	18689
	.byte	30
	.byte	'Ifx_STM_TIM0SV',0,16,242,2,3
	.word	20080
	.byte	30
	.byte	'Ifx_STM_TIM1',0,16,250,2,3
	.word	18774
	.byte	30
	.byte	'Ifx_STM_TIM2',0,16,130,3,3
	.word	18859
	.byte	30
	.byte	'Ifx_STM_TIM3',0,16,138,3,3
	.word	18945
	.byte	30
	.byte	'Ifx_STM_TIM4',0,16,146,3,3
	.word	19031
	.byte	30
	.byte	'Ifx_STM_TIM5',0,16,154,3,3
	.word	19117
	.byte	30
	.byte	'Ifx_STM_TIM6',0,16,162,3,3
	.word	19203
	.byte	14
	.word	21371
	.byte	30
	.byte	'Ifx_STM',0,16,201,3,3
	.word	81946
	.byte	17,7,144,1,9,1,18
	.byte	'IfxCpu_CounterMode_normal',0,0,18
	.byte	'IfxCpu_CounterMode_task',0,1,0,30
	.byte	'IfxCpu_CounterMode',0,7,148,1,3
	.word	81968
	.byte	20,7,160,1,9,6,13
	.byte	'counter',0
	.word	10311
	.byte	4,2,35,0,13
	.byte	'overlfow',0
	.word	650
	.byte	1,2,35,4,0,30
	.byte	'IfxCpu_Counter',0,7,164,1,3
	.word	82057
	.byte	20,7,172,1,9,32,13
	.byte	'instruction',0
	.word	82057
	.byte	6,2,35,0,13
	.byte	'clock',0
	.word	82057
	.byte	6,2,35,6,13
	.byte	'counter1',0
	.word	82057
	.byte	6,2,35,12,13
	.byte	'counter2',0
	.word	82057
	.byte	6,2,35,18,13
	.byte	'counter3',0
	.word	82057
	.byte	6,2,35,24,0,30
	.byte	'IfxCpu_Perf',0,7,179,1,3
	.word	82123
	.byte	10
	.byte	'_Ifx_FLASH_ACCEN0_Bits',0,32,45,16,4,11
	.byte	'EN0',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	650
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	650
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	650
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	650
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	650
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	650
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	650
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	650
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	650
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	650
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	650
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	650
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	650
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	650
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	650
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	650
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	650
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	650
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	650
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	650
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	650
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	650
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	650
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	650
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	650
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	650
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	650
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	650
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_FLASH_ACCEN0_Bits',0,32,79,3
	.word	82241
	.byte	10
	.byte	'_Ifx_FLASH_ACCEN1_Bits',0,32,82,16,4,11
	.byte	'reserved_0',0,4
	.word	475
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_FLASH_ACCEN1_Bits',0,32,85,3
	.word	82802
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_CFG_Bits',0,32,88,16,4,11
	.byte	'SEL',0,1
	.word	650
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	650
	.byte	2,0,2,35,0,11
	.byte	'CLR',0,1
	.word	650
	.byte	1,7,2,35,1,11
	.byte	'DIS',0,1
	.word	650
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	475
	.byte	22,0,2,35,0,0,30
	.byte	'Ifx_FLASH_CBAB_CFG_Bits',0,32,95,3
	.word	82883
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_STAT_Bits',0,32,98,16,4,11
	.byte	'VLD0',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'VLD1',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'VLD2',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'VLD3',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'VLD4',0,1
	.word	650
	.byte	1,3,2,35,0,11
	.byte	'VLD5',0,1
	.word	650
	.byte	1,2,2,35,0,11
	.byte	'VLD6',0,1
	.word	650
	.byte	1,1,2,35,0,11
	.byte	'VLD7',0,1
	.word	650
	.byte	1,0,2,35,0,11
	.byte	'VLD8',0,1
	.word	650
	.byte	1,7,2,35,1,11
	.byte	'VLD9',0,1
	.word	650
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	475
	.byte	22,0,2,35,0,0,30
	.byte	'Ifx_FLASH_CBAB_STAT_Bits',0,32,111,3
	.word	83036
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_TOP_Bits',0,32,114,16,4,11
	.byte	'reserved_0',0,1
	.word	650
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	475
	.byte	19,8,2,35,0,11
	.byte	'ERR',0,1
	.word	650
	.byte	6,2,2,35,3,11
	.byte	'VLD',0,1
	.word	650
	.byte	1,1,2,35,3,11
	.byte	'CLR',0,1
	.word	650
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_FLASH_CBAB_TOP_Bits',0,32,121,3
	.word	83284
	.byte	10
	.byte	'_Ifx_FLASH_COMM0_Bits',0,32,124,16,4,11
	.byte	'STATUS',0,1
	.word	650
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	475
	.byte	24,0,2,35,0,0,30
	.byte	'Ifx_FLASH_COMM0_Bits',0,32,128,1,3
	.word	83430
	.byte	10
	.byte	'_Ifx_FLASH_COMM1_Bits',0,32,131,1,16,4,11
	.byte	'STATUS',0,1
	.word	650
	.byte	8,0,2,35,0,11
	.byte	'DATA',0,1
	.word	650
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	667
	.byte	16,0,2,35,2,0,30
	.byte	'Ifx_FLASH_COMM1_Bits',0,32,136,1,3
	.word	83528
	.byte	10
	.byte	'_Ifx_FLASH_COMM2_Bits',0,32,139,1,16,4,11
	.byte	'STATUS',0,1
	.word	650
	.byte	8,0,2,35,0,11
	.byte	'DATA',0,1
	.word	650
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	667
	.byte	16,0,2,35,2,0,30
	.byte	'Ifx_FLASH_COMM2_Bits',0,32,144,1,3
	.word	83644
	.byte	10
	.byte	'_Ifx_FLASH_ECCRD_Bits',0,32,147,1,16,4,11
	.byte	'RCODE',0,4
	.word	475
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	667
	.byte	8,2,2,35,2,11
	.byte	'EDCERRINJ',0,1
	.word	650
	.byte	1,1,2,35,3,11
	.byte	'ECCORDIS',0,1
	.word	650
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_FLASH_ECCRD_Bits',0,32,153,1,3
	.word	83760
	.byte	10
	.byte	'_Ifx_FLASH_ECCRP_Bits',0,32,156,1,16,4,11
	.byte	'RCODE',0,4
	.word	475
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	667
	.byte	8,2,2,35,2,11
	.byte	'EDCERRINJ',0,1
	.word	650
	.byte	1,1,2,35,3,11
	.byte	'ECCORDIS',0,1
	.word	650
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_FLASH_ECCRP_Bits',0,32,162,1,3
	.word	83900
	.byte	10
	.byte	'_Ifx_FLASH_ECCW_Bits',0,32,165,1,16,4,11
	.byte	'WCODE',0,4
	.word	475
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	667
	.byte	8,2,2,35,2,11
	.byte	'DECENCDIS',0,1
	.word	650
	.byte	1,1,2,35,3,11
	.byte	'PECENCDIS',0,1
	.word	650
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_FLASH_ECCW_Bits',0,32,171,1,3
	.word	84040
	.byte	10
	.byte	'_Ifx_FLASH_FCON_Bits',0,32,174,1,16,4,11
	.byte	'WSPFLASH',0,1
	.word	650
	.byte	4,4,2,35,0,11
	.byte	'WSECPF',0,1
	.word	650
	.byte	2,2,2,35,0,11
	.byte	'WSDFLASH',0,2
	.word	667
	.byte	6,4,2,35,0,11
	.byte	'WSECDF',0,1
	.word	650
	.byte	3,1,2,35,1,11
	.byte	'IDLE',0,1
	.word	650
	.byte	1,0,2,35,1,11
	.byte	'ESLDIS',0,1
	.word	650
	.byte	1,7,2,35,2,11
	.byte	'SLEEP',0,1
	.word	650
	.byte	1,6,2,35,2,11
	.byte	'NSAFECC',0,1
	.word	650
	.byte	1,5,2,35,2,11
	.byte	'STALL',0,1
	.word	650
	.byte	1,4,2,35,2,11
	.byte	'RES21',0,1
	.word	650
	.byte	2,2,2,35,2,11
	.byte	'RES23',0,1
	.word	650
	.byte	2,0,2,35,2,11
	.byte	'VOPERM',0,1
	.word	650
	.byte	1,7,2,35,3,11
	.byte	'SQERM',0,1
	.word	650
	.byte	1,6,2,35,3,11
	.byte	'PROERM',0,1
	.word	650
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	650
	.byte	3,2,2,35,3,11
	.byte	'PR5V',0,1
	.word	650
	.byte	1,1,2,35,3,11
	.byte	'EOBM',0,1
	.word	650
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_FLASH_FCON_Bits',0,32,193,1,3
	.word	84179
	.byte	10
	.byte	'_Ifx_FLASH_FPRO_Bits',0,32,196,1,16,4,11
	.byte	'PROINP',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'PRODISP',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'PROIND',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'PRODISD',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'PROINHSMCOTP',0,1
	.word	650
	.byte	1,3,2,35,0,11
	.byte	'RES5',0,1
	.word	650
	.byte	1,2,2,35,0,11
	.byte	'PROINOTP',0,1
	.word	650
	.byte	1,1,2,35,0,11
	.byte	'RES7',0,1
	.word	650
	.byte	1,0,2,35,0,11
	.byte	'PROINDBG',0,1
	.word	650
	.byte	1,7,2,35,1,11
	.byte	'PRODISDBG',0,1
	.word	650
	.byte	1,6,2,35,1,11
	.byte	'PROINHSM',0,1
	.word	650
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	650
	.byte	5,0,2,35,1,11
	.byte	'DCFP',0,1
	.word	650
	.byte	1,7,2,35,2,11
	.byte	'DDFP',0,1
	.word	650
	.byte	1,6,2,35,2,11
	.byte	'DDFPX',0,1
	.word	650
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	650
	.byte	1,4,2,35,2,11
	.byte	'DDFD',0,1
	.word	650
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	650
	.byte	1,2,2,35,2,11
	.byte	'ENPE',0,1
	.word	650
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	650
	.byte	8,0,2,35,3,0,30
	.byte	'Ifx_FLASH_FPRO_Bits',0,32,218,1,3
	.word	84541
	.byte	10
	.byte	'_Ifx_FLASH_FSR_Bits',0,32,221,1,16,4,11
	.byte	'FABUSY',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'D0BUSY',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'RES1',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'P0BUSY',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'P1BUSY',0,1
	.word	650
	.byte	1,3,2,35,0,11
	.byte	'RES5',0,1
	.word	650
	.byte	1,2,2,35,0,11
	.byte	'RES6',0,1
	.word	650
	.byte	1,1,2,35,0,11
	.byte	'PROG',0,1
	.word	650
	.byte	1,0,2,35,0,11
	.byte	'ERASE',0,1
	.word	650
	.byte	1,7,2,35,1,11
	.byte	'PFPAGE',0,1
	.word	650
	.byte	1,6,2,35,1,11
	.byte	'DFPAGE',0,1
	.word	650
	.byte	1,5,2,35,1,11
	.byte	'OPER',0,1
	.word	650
	.byte	1,4,2,35,1,11
	.byte	'SQER',0,1
	.word	650
	.byte	1,3,2,35,1,11
	.byte	'PROER',0,1
	.word	650
	.byte	1,2,2,35,1,11
	.byte	'PFSBER',0,1
	.word	650
	.byte	1,1,2,35,1,11
	.byte	'PFDBER',0,1
	.word	650
	.byte	1,0,2,35,1,11
	.byte	'PFMBER',0,1
	.word	650
	.byte	1,7,2,35,2,11
	.byte	'RES17',0,1
	.word	650
	.byte	1,6,2,35,2,11
	.byte	'DFSBER',0,1
	.word	650
	.byte	1,5,2,35,2,11
	.byte	'DFDBER',0,1
	.word	650
	.byte	1,4,2,35,2,11
	.byte	'DFTBER',0,1
	.word	650
	.byte	1,3,2,35,2,11
	.byte	'DFMBER',0,1
	.word	650
	.byte	1,2,2,35,2,11
	.byte	'SRIADDERR',0,1
	.word	650
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	667
	.byte	2,7,2,35,2,11
	.byte	'PVER',0,1
	.word	650
	.byte	1,6,2,35,3,11
	.byte	'EVER',0,1
	.word	650
	.byte	1,5,2,35,3,11
	.byte	'SPND',0,1
	.word	650
	.byte	1,4,2,35,3,11
	.byte	'SLM',0,1
	.word	650
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	650
	.byte	1,2,2,35,3,11
	.byte	'ORIER',0,1
	.word	650
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	650
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_FLASH_FSR_Bits',0,32,254,1,3
	.word	84982
	.byte	10
	.byte	'_Ifx_FLASH_ID_Bits',0,32,129,2,16,4,11
	.byte	'MODREV',0,1
	.word	650
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	650
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	667
	.byte	16,0,2,35,2,0,30
	.byte	'Ifx_FLASH_ID_Bits',0,32,134,2,3
	.word	85588
	.byte	10
	.byte	'_Ifx_FLASH_MARD_Bits',0,32,137,2,16,4,11
	.byte	'HMARGIN',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'SELD0',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'SPND',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'SPNDERR',0,1
	.word	650
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,2
	.word	667
	.byte	10,1,2,35,0,11
	.byte	'TRAPDIS',0,1
	.word	650
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	667
	.byte	16,0,2,35,2,0,30
	.byte	'Ifx_FLASH_MARD_Bits',0,32,147,2,3
	.word	85699
	.byte	10
	.byte	'_Ifx_FLASH_MARP_Bits',0,32,150,2,16,4,11
	.byte	'SELP0',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'SELP1',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'RES2',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'RES3',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	667
	.byte	11,1,2,35,0,11
	.byte	'TRAPDIS',0,1
	.word	650
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	667
	.byte	16,0,2,35,2,0,30
	.byte	'Ifx_FLASH_MARP_Bits',0,32,159,2,3
	.word	85913
	.byte	10
	.byte	'_Ifx_FLASH_PROCOND_Bits',0,32,162,2,16,4,11
	.byte	'L',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'NSAFECC',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'RAMIN',0,1
	.word	650
	.byte	2,4,2,35,0,11
	.byte	'RAMINSEL',0,1
	.word	650
	.byte	4,0,2,35,0,11
	.byte	'OSCCFG',0,1
	.word	650
	.byte	1,7,2,35,1,11
	.byte	'MODE',0,1
	.word	650
	.byte	2,5,2,35,1,11
	.byte	'APREN',0,1
	.word	650
	.byte	1,4,2,35,1,11
	.byte	'CAP0EN',0,1
	.word	650
	.byte	1,3,2,35,1,11
	.byte	'CAP1EN',0,1
	.word	650
	.byte	1,2,2,35,1,11
	.byte	'CAP2EN',0,1
	.word	650
	.byte	1,1,2,35,1,11
	.byte	'CAP3EN',0,1
	.word	650
	.byte	1,0,2,35,1,11
	.byte	'ESR0CNT',0,2
	.word	667
	.byte	12,4,2,35,2,11
	.byte	'RES29',0,1
	.word	650
	.byte	2,2,2,35,3,11
	.byte	'RES30',0,1
	.word	650
	.byte	1,1,2,35,3,11
	.byte	'RPRO',0,1
	.word	650
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_FLASH_PROCOND_Bits',0,32,179,2,3
	.word	86100
	.byte	10
	.byte	'_Ifx_FLASH_PROCONDBG_Bits',0,32,182,2,16,4,11
	.byte	'OCDSDIS',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'DBGIFLCK',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'EDM',0,1
	.word	650
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	475
	.byte	28,0,2,35,0,0,30
	.byte	'Ifx_FLASH_PROCONDBG_Bits',0,32,188,2,3
	.word	86424
	.byte	10
	.byte	'_Ifx_FLASH_PROCONHSM_Bits',0,32,191,2,16,4,11
	.byte	'HSMDBGDIS',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'DBGIFLCK',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'TSTIFLCK',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'HSMTSTDIS',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'RES15',0,2
	.word	667
	.byte	12,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	667
	.byte	16,0,2,35,2,0,30
	.byte	'Ifx_FLASH_PROCONHSM_Bits',0,32,199,2,3
	.word	86567
	.byte	10
	.byte	'_Ifx_FLASH_PROCONHSMCOTP_Bits',0,32,202,2,16,4,11
	.byte	'HSMBOOTEN',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'SSWWAIT',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'HSMDX',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'HSM6X',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'HSM16X',0,1
	.word	650
	.byte	1,3,2,35,0,11
	.byte	'HSM17X',0,1
	.word	650
	.byte	1,2,2,35,0,11
	.byte	'S6ROM',0,1
	.word	650
	.byte	1,1,2,35,0,11
	.byte	'HSMENPINS',0,2
	.word	667
	.byte	2,7,2,35,0,11
	.byte	'HSMENRES',0,1
	.word	650
	.byte	2,5,2,35,1,11
	.byte	'DESTDBG',0,1
	.word	650
	.byte	2,3,2,35,1,11
	.byte	'BLKFLAN',0,1
	.word	650
	.byte	1,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	650
	.byte	2,0,2,35,1,11
	.byte	'S16ROM',0,1
	.word	650
	.byte	1,7,2,35,2,11
	.byte	'S17ROM',0,1
	.word	650
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	667
	.byte	14,0,2,35,2,0,30
	.byte	'Ifx_FLASH_PROCONHSMCOTP_Bits',0,32,219,2,3
	.word	86756
	.byte	10
	.byte	'_Ifx_FLASH_PROCONOTP_Bits',0,32,222,2,16,4,11
	.byte	'S0ROM',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'S1ROM',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'S2ROM',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'S3ROM',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'S4ROM',0,1
	.word	650
	.byte	1,3,2,35,0,11
	.byte	'S5ROM',0,1
	.word	650
	.byte	1,2,2,35,0,11
	.byte	'S6ROM',0,1
	.word	650
	.byte	1,1,2,35,0,11
	.byte	'S7ROM',0,1
	.word	650
	.byte	1,0,2,35,0,11
	.byte	'S8ROM',0,1
	.word	650
	.byte	1,7,2,35,1,11
	.byte	'S9ROM',0,1
	.word	650
	.byte	1,6,2,35,1,11
	.byte	'S10ROM',0,1
	.word	650
	.byte	1,5,2,35,1,11
	.byte	'S11ROM',0,1
	.word	650
	.byte	1,4,2,35,1,11
	.byte	'S12ROM',0,1
	.word	650
	.byte	1,3,2,35,1,11
	.byte	'S13ROM',0,1
	.word	650
	.byte	1,2,2,35,1,11
	.byte	'S14ROM',0,1
	.word	650
	.byte	1,1,2,35,1,11
	.byte	'S15ROM',0,1
	.word	650
	.byte	1,0,2,35,1,11
	.byte	'S16ROM',0,1
	.word	650
	.byte	1,7,2,35,2,11
	.byte	'S17ROM',0,1
	.word	650
	.byte	1,6,2,35,2,11
	.byte	'S18ROM',0,1
	.word	650
	.byte	1,5,2,35,2,11
	.byte	'S19ROM',0,1
	.word	650
	.byte	1,4,2,35,2,11
	.byte	'S20ROM',0,1
	.word	650
	.byte	1,3,2,35,2,11
	.byte	'S21ROM',0,1
	.word	650
	.byte	1,2,2,35,2,11
	.byte	'S22ROM',0,1
	.word	650
	.byte	1,1,2,35,2,11
	.byte	'S23ROM',0,1
	.word	650
	.byte	1,0,2,35,2,11
	.byte	'S24ROM',0,1
	.word	650
	.byte	1,7,2,35,3,11
	.byte	'S25ROM',0,1
	.word	650
	.byte	1,6,2,35,3,11
	.byte	'S26ROM',0,1
	.word	650
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	650
	.byte	2,3,2,35,3,11
	.byte	'BML',0,1
	.word	650
	.byte	2,1,2,35,3,11
	.byte	'TP',0,1
	.word	650
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_FLASH_PROCONOTP_Bits',0,32,254,2,3
	.word	87119
	.byte	10
	.byte	'_Ifx_FLASH_PROCONP_Bits',0,32,129,3,16,4,11
	.byte	'S0L',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'S1L',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'S2L',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'S3L',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'S4L',0,1
	.word	650
	.byte	1,3,2,35,0,11
	.byte	'S5L',0,1
	.word	650
	.byte	1,2,2,35,0,11
	.byte	'S6L',0,1
	.word	650
	.byte	1,1,2,35,0,11
	.byte	'S7L',0,1
	.word	650
	.byte	1,0,2,35,0,11
	.byte	'S8L',0,1
	.word	650
	.byte	1,7,2,35,1,11
	.byte	'S9L',0,1
	.word	650
	.byte	1,6,2,35,1,11
	.byte	'S10L',0,1
	.word	650
	.byte	1,5,2,35,1,11
	.byte	'S11L',0,1
	.word	650
	.byte	1,4,2,35,1,11
	.byte	'S12L',0,1
	.word	650
	.byte	1,3,2,35,1,11
	.byte	'S13L',0,1
	.word	650
	.byte	1,2,2,35,1,11
	.byte	'S14L',0,1
	.word	650
	.byte	1,1,2,35,1,11
	.byte	'S15L',0,1
	.word	650
	.byte	1,0,2,35,1,11
	.byte	'S16L',0,1
	.word	650
	.byte	1,7,2,35,2,11
	.byte	'S17L',0,1
	.word	650
	.byte	1,6,2,35,2,11
	.byte	'S18L',0,1
	.word	650
	.byte	1,5,2,35,2,11
	.byte	'S19L',0,1
	.word	650
	.byte	1,4,2,35,2,11
	.byte	'S20L',0,1
	.word	650
	.byte	1,3,2,35,2,11
	.byte	'S21L',0,1
	.word	650
	.byte	1,2,2,35,2,11
	.byte	'S22L',0,1
	.word	650
	.byte	1,1,2,35,2,11
	.byte	'S23L',0,1
	.word	650
	.byte	1,0,2,35,2,11
	.byte	'S24L',0,1
	.word	650
	.byte	1,7,2,35,3,11
	.byte	'S25L',0,1
	.word	650
	.byte	1,6,2,35,3,11
	.byte	'S26L',0,1
	.word	650
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	650
	.byte	4,1,2,35,3,11
	.byte	'RPRO',0,1
	.word	650
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_FLASH_PROCONP_Bits',0,32,160,3,3
	.word	87714
	.byte	10
	.byte	'_Ifx_FLASH_PROCONWOP_Bits',0,32,163,3,16,4,11
	.byte	'S0WOP',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'S1WOP',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'S2WOP',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'S3WOP',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'S4WOP',0,1
	.word	650
	.byte	1,3,2,35,0,11
	.byte	'S5WOP',0,1
	.word	650
	.byte	1,2,2,35,0,11
	.byte	'S6WOP',0,1
	.word	650
	.byte	1,1,2,35,0,11
	.byte	'S7WOP',0,1
	.word	650
	.byte	1,0,2,35,0,11
	.byte	'S8WOP',0,1
	.word	650
	.byte	1,7,2,35,1,11
	.byte	'S9WOP',0,1
	.word	650
	.byte	1,6,2,35,1,11
	.byte	'S10WOP',0,1
	.word	650
	.byte	1,5,2,35,1,11
	.byte	'S11WOP',0,1
	.word	650
	.byte	1,4,2,35,1,11
	.byte	'S12WOP',0,1
	.word	650
	.byte	1,3,2,35,1,11
	.byte	'S13WOP',0,1
	.word	650
	.byte	1,2,2,35,1,11
	.byte	'S14WOP',0,1
	.word	650
	.byte	1,1,2,35,1,11
	.byte	'S15WOP',0,1
	.word	650
	.byte	1,0,2,35,1,11
	.byte	'S16WOP',0,1
	.word	650
	.byte	1,7,2,35,2,11
	.byte	'S17WOP',0,1
	.word	650
	.byte	1,6,2,35,2,11
	.byte	'S18WOP',0,1
	.word	650
	.byte	1,5,2,35,2,11
	.byte	'S19WOP',0,1
	.word	650
	.byte	1,4,2,35,2,11
	.byte	'S20WOP',0,1
	.word	650
	.byte	1,3,2,35,2,11
	.byte	'S21WOP',0,1
	.word	650
	.byte	1,2,2,35,2,11
	.byte	'S22WOP',0,1
	.word	650
	.byte	1,1,2,35,2,11
	.byte	'S23WOP',0,1
	.word	650
	.byte	1,0,2,35,2,11
	.byte	'S24WOP',0,1
	.word	650
	.byte	1,7,2,35,3,11
	.byte	'S25WOP',0,1
	.word	650
	.byte	1,6,2,35,3,11
	.byte	'S26WOP',0,1
	.word	650
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	650
	.byte	4,1,2,35,3,11
	.byte	'DATM',0,1
	.word	650
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_FLASH_PROCONWOP_Bits',0,32,194,3,3
	.word	88238
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG0_Bits',0,32,197,3,16,4,11
	.byte	'TAG',0,1
	.word	650
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	475
	.byte	26,0,2,35,0,0,30
	.byte	'Ifx_FLASH_RDB_CFG0_Bits',0,32,201,3,3
	.word	88820
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG1_Bits',0,32,204,3,16,4,11
	.byte	'TAG',0,1
	.word	650
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	475
	.byte	26,0,2,35,0,0,30
	.byte	'Ifx_FLASH_RDB_CFG1_Bits',0,32,208,3,3
	.word	88922
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG2_Bits',0,32,211,3,16,4,11
	.byte	'TAG',0,1
	.word	650
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	475
	.byte	26,0,2,35,0,0,30
	.byte	'Ifx_FLASH_RDB_CFG2_Bits',0,32,215,3,3
	.word	89024
	.byte	10
	.byte	'_Ifx_FLASH_RRAD_Bits',0,32,218,3,16,4,11
	.byte	'reserved_0',0,1
	.word	650
	.byte	3,5,2,35,0,11
	.byte	'ADD',0,4
	.word	475
	.byte	29,0,2,35,0,0,30
	.byte	'Ifx_FLASH_RRAD_Bits',0,32,222,3,3
	.word	89126
	.byte	10
	.byte	'_Ifx_FLASH_RRCT_Bits',0,32,225,3,16,4,11
	.byte	'STRT',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'STP',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'BUSY',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'DONE',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'ERR',0,1
	.word	650
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	650
	.byte	3,0,2,35,0,11
	.byte	'EOBM',0,1
	.word	650
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	650
	.byte	7,0,2,35,1,11
	.byte	'CNT',0,2
	.word	667
	.byte	16,0,2,35,2,0,30
	.byte	'Ifx_FLASH_RRCT_Bits',0,32,236,3,3
	.word	89220
	.byte	10
	.byte	'_Ifx_FLASH_RRD0_Bits',0,32,239,3,16,4,11
	.byte	'DATA',0,4
	.word	475
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_FLASH_RRD0_Bits',0,32,242,3,3
	.word	89430
	.byte	10
	.byte	'_Ifx_FLASH_RRD1_Bits',0,32,245,3,16,4,11
	.byte	'DATA',0,4
	.word	475
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_FLASH_RRD1_Bits',0,32,248,3,3
	.word	89503
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_CFG_Bits',0,32,251,3,16,4,11
	.byte	'SEL',0,1
	.word	650
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	650
	.byte	2,0,2,35,0,11
	.byte	'CLR',0,1
	.word	650
	.byte	1,7,2,35,1,11
	.byte	'DIS',0,1
	.word	650
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	475
	.byte	22,0,2,35,0,0,30
	.byte	'Ifx_FLASH_UBAB_CFG_Bits',0,32,130,4,3
	.word	89576
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_STAT_Bits',0,32,133,4,16,4,11
	.byte	'VLD0',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	475
	.byte	31,0,2,35,0,0,30
	.byte	'Ifx_FLASH_UBAB_STAT_Bits',0,32,137,4,3
	.word	89731
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_TOP_Bits',0,32,140,4,16,4,11
	.byte	'reserved_0',0,1
	.word	650
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	475
	.byte	19,8,2,35,0,11
	.byte	'ERR',0,1
	.word	650
	.byte	6,2,2,35,3,11
	.byte	'VLD',0,1
	.word	650
	.byte	1,1,2,35,3,11
	.byte	'CLR',0,1
	.word	650
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_FLASH_UBAB_TOP_Bits',0,32,147,4,3
	.word	89836
	.byte	12,32,155,4,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	82241
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_ACCEN0',0,32,160,4,3
	.word	89984
	.byte	12,32,163,4,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	82802
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_ACCEN1',0,32,168,4,3
	.word	90050
	.byte	12,32,171,4,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	82883
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_CBAB_CFG',0,32,176,4,3
	.word	90116
	.byte	12,32,179,4,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	83036
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_CBAB_STAT',0,32,184,4,3
	.word	90184
	.byte	12,32,187,4,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	83284
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_CBAB_TOP',0,32,192,4,3
	.word	90253
	.byte	12,32,195,4,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	83430
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_COMM0',0,32,200,4,3
	.word	90321
	.byte	12,32,203,4,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	83528
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_COMM1',0,32,208,4,3
	.word	90386
	.byte	12,32,211,4,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	83644
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_COMM2',0,32,216,4,3
	.word	90451
	.byte	12,32,219,4,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	83760
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_ECCRD',0,32,224,4,3
	.word	90516
	.byte	12,32,227,4,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	83900
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_ECCRP',0,32,232,4,3
	.word	90581
	.byte	12,32,235,4,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	84040
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_ECCW',0,32,240,4,3
	.word	90646
	.byte	12,32,243,4,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	84179
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_FCON',0,32,248,4,3
	.word	90710
	.byte	12,32,251,4,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	84541
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_FPRO',0,32,128,5,3
	.word	90774
	.byte	12,32,131,5,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	84982
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_FSR',0,32,136,5,3
	.word	90838
	.byte	12,32,139,5,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	85588
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_ID',0,32,144,5,3
	.word	90901
	.byte	12,32,147,5,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	85699
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_MARD',0,32,152,5,3
	.word	90963
	.byte	12,32,155,5,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	85913
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_MARP',0,32,160,5,3
	.word	91027
	.byte	12,32,163,5,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	86100
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_PROCOND',0,32,168,5,3
	.word	91091
	.byte	12,32,171,5,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	86424
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_PROCONDBG',0,32,176,5,3
	.word	91158
	.byte	12,32,179,5,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	86567
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_PROCONHSM',0,32,184,5,3
	.word	91227
	.byte	12,32,187,5,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	86756
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_PROCONHSMCOTP',0,32,192,5,3
	.word	91296
	.byte	12,32,195,5,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	87119
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_PROCONOTP',0,32,200,5,3
	.word	91369
	.byte	12,32,203,5,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	87714
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_PROCONP',0,32,208,5,3
	.word	91438
	.byte	12,32,211,5,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	88238
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_PROCONWOP',0,32,216,5,3
	.word	91505
	.byte	12,32,219,5,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	88820
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_RDB_CFG0',0,32,224,5,3
	.word	91574
	.byte	12,32,227,5,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	88922
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_RDB_CFG1',0,32,232,5,3
	.word	91642
	.byte	12,32,235,5,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	89024
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_RDB_CFG2',0,32,240,5,3
	.word	91710
	.byte	12,32,243,5,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	89126
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_RRAD',0,32,248,5,3
	.word	91778
	.byte	12,32,251,5,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	89220
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_RRCT',0,32,128,6,3
	.word	91842
	.byte	12,32,131,6,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	89430
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_RRD0',0,32,136,6,3
	.word	91906
	.byte	12,32,139,6,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	89503
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_RRD1',0,32,144,6,3
	.word	91970
	.byte	12,32,147,6,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	89576
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_UBAB_CFG',0,32,152,6,3
	.word	92034
	.byte	12,32,155,6,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	89731
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_UBAB_STAT',0,32,160,6,3
	.word	92102
	.byte	12,32,163,6,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	89836
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_UBAB_TOP',0,32,168,6,3
	.word	92171
	.byte	10
	.byte	'_Ifx_FLASH_CBAB',0,32,179,6,25,12,13
	.byte	'CFG',0
	.word	90116
	.byte	4,2,35,0,13
	.byte	'STAT',0
	.word	90184
	.byte	4,2,35,4,13
	.byte	'TOP',0
	.word	90253
	.byte	4,2,35,8,0,14
	.word	92239
	.byte	30
	.byte	'Ifx_FLASH_CBAB',0,32,184,6,3
	.word	92302
	.byte	10
	.byte	'_Ifx_FLASH_RDB',0,32,187,6,25,12,13
	.byte	'CFG0',0
	.word	91574
	.byte	4,2,35,0,13
	.byte	'CFG1',0
	.word	91642
	.byte	4,2,35,4,13
	.byte	'CFG2',0
	.word	91710
	.byte	4,2,35,8,0,14
	.word	92331
	.byte	30
	.byte	'Ifx_FLASH_RDB',0,32,192,6,3
	.word	92395
	.byte	10
	.byte	'_Ifx_FLASH_UBAB',0,32,195,6,25,12,13
	.byte	'CFG',0
	.word	92034
	.byte	4,2,35,0,13
	.byte	'STAT',0
	.word	92102
	.byte	4,2,35,4,13
	.byte	'TOP',0
	.word	92171
	.byte	4,2,35,8,0,14
	.word	92423
	.byte	30
	.byte	'Ifx_FLASH_UBAB',0,32,200,6,3
	.word	92486
	.byte	30
	.byte	'IfxScuCcu_PllStepsFunctionHook',0,9,148,1,16
	.word	214
	.byte	20,9,212,5,9,8,13
	.byte	'value',0
	.word	10311
	.byte	4,2,35,0,13
	.byte	'mask',0
	.word	10311
	.byte	4,2,35,4,0,30
	.byte	'IfxScuCcu_CcuconRegConfig',0,9,216,5,3
	.word	92555
	.byte	20,9,221,5,9,8,13
	.byte	'pDivider',0
	.word	650
	.byte	1,2,35,0,13
	.byte	'nDivider',0
	.word	650
	.byte	1,2,35,1,13
	.byte	'k2Initial',0
	.word	650
	.byte	1,2,35,2,13
	.byte	'waitTime',0
	.word	271
	.byte	4,2,35,4,0,30
	.byte	'IfxScuCcu_InitialStepConfig',0,9,227,5,3
	.word	92626
	.byte	20,9,231,5,9,12,13
	.byte	'k2Step',0
	.word	650
	.byte	1,2,35,0,13
	.byte	'waitTime',0
	.word	271
	.byte	4,2,35,2,13
	.byte	'hookFunction',0
	.word	92515
	.byte	4,2,35,8,0,30
	.byte	'IfxScuCcu_PllStepsConfig',0,9,236,5,3
	.word	92743
	.byte	3
	.word	211
	.byte	20,9,244,5,9,48,13
	.byte	'ccucon0',0
	.word	92555
	.byte	8,2,35,0,13
	.byte	'ccucon1',0
	.word	92555
	.byte	8,2,35,8,13
	.byte	'ccucon2',0
	.word	92555
	.byte	8,2,35,16,13
	.byte	'ccucon5',0
	.word	92555
	.byte	8,2,35,24,13
	.byte	'ccucon6',0
	.word	92555
	.byte	8,2,35,32,13
	.byte	'ccucon7',0
	.word	92555
	.byte	8,2,35,40,0,30
	.byte	'IfxScuCcu_ClockDistributionConfig',0,9,252,5,3
	.word	92845
	.byte	20,9,128,6,9,8,13
	.byte	'value',0
	.word	10311
	.byte	4,2,35,0,13
	.byte	'mask',0
	.word	10311
	.byte	4,2,35,4,0,30
	.byte	'IfxScuCcu_FlashWaitstateConfig',0,9,132,6,3
	.word	92997
	.byte	3
	.word	92743
	.byte	20,9,137,6,9,16,13
	.byte	'numOfPllDividerSteps',0
	.word	650
	.byte	1,2,35,0,13
	.byte	'pllDividerStep',0
	.word	93073
	.byte	4,2,35,4,13
	.byte	'pllInitialStep',0
	.word	92626
	.byte	8,2,35,8,0,30
	.byte	'IfxScuCcu_SysPllConfig',0,9,142,6,3
	.word	93078
	.byte	17,33,69,9,1,18
	.byte	'IfxSrc_Tos_cpu0',0,0,18
	.byte	'IfxSrc_Tos_cpu1',0,1,18
	.byte	'IfxSrc_Tos_dma',0,3,0,30
	.byte	'IfxSrc_Tos',0,33,74,3
	.word	93195
	.byte	20,34,59,15,16,13
	.byte	'module',0
	.word	17496
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	56660
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	27848
	.byte	1,2,35,12,0,21
	.word	93273
	.byte	30
	.byte	'IfxAsclin_Cts_In',0,34,64,3
	.word	93324
	.byte	20,34,67,15,16,13
	.byte	'module',0
	.word	17496
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	56660
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	27848
	.byte	1,2,35,12,0,21
	.word	93354
	.byte	30
	.byte	'IfxAsclin_Rx_In',0,34,72,3
	.word	93405
	.byte	20,34,75,15,16,13
	.byte	'module',0
	.word	17496
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	56660
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	9495
	.byte	1,2,35,12,0,21
	.word	93434
	.byte	30
	.byte	'IfxAsclin_Rts_Out',0,34,80,3
	.word	93485
	.byte	20,34,83,15,16,13
	.byte	'module',0
	.word	17496
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	56660
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	9495
	.byte	1,2,35,12,0,21
	.word	93516
	.byte	30
	.byte	'IfxAsclin_Sclk_Out',0,34,88,3
	.word	93567
	.byte	20,34,91,15,16,13
	.byte	'module',0
	.word	17496
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	56660
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	9495
	.byte	1,2,35,12,0,21
	.word	93599
	.byte	30
	.byte	'IfxAsclin_Slso_Out',0,34,96,3
	.word	93650
	.byte	20,34,99,15,16,13
	.byte	'module',0
	.word	17496
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	56660
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	9495
	.byte	1,2,35,12,0,21
	.word	93682
	.byte	30
	.byte	'IfxAsclin_Tx_Out',0,34,104,3
	.word	93733
	.byte	17,12,82,9,1,18
	.byte	'IfxAsclin_Checksum_classic',0,0,18
	.byte	'IfxAsclin_Checksum_enhanced',0,1,0,30
	.byte	'IfxAsclin_Checksum',0,12,86,3
	.word	93763
	.byte	17,12,91,9,1,18
	.byte	'IfxAsclin_ChecksumInjection_notWritten',0,0,18
	.byte	'IfxAsclin_ChecksumInjection_written',0,1,0,30
	.byte	'IfxAsclin_ChecksumInjection',0,12,95,3
	.word	93855
	.byte	17,12,101,9,1,18
	.byte	'IfxAsclin_ClockPolarity_idleLow',0,0,18
	.byte	'IfxAsclin_ClockPolarity_idleHigh',0,1,0,30
	.byte	'IfxAsclin_ClockPolarity',0,12,105,3
	.word	93976
	.byte	17,12,110,9,1,18
	.byte	'IfxAsclin_ClockSource_noClock',0,0,18
	.byte	'IfxAsclin_ClockSource_kernelClock',0,1,18
	.byte	'IfxAsclin_ClockSource_oscillatorClock',0,2,18
	.byte	'IfxAsclin_ClockSource_flexRayClock',0,4,18
	.byte	'IfxAsclin_ClockSource_ascFastClock',0,8,18
	.byte	'IfxAsclin_ClockSource_ascSlowClock',0,16,0,30
	.byte	'IfxAsclin_ClockSource',0,12,118,3
	.word	94083
	.byte	30
	.byte	'IfxAsclin_CtsInputSelect',0,12,129,1,3
	.word	17563
	.byte	17,12,134,1,9,1,18
	.byte	'IfxAsclin_DataLength_1',0,0,18
	.byte	'IfxAsclin_DataLength_2',0,1,18
	.byte	'IfxAsclin_DataLength_3',0,2,18
	.byte	'IfxAsclin_DataLength_4',0,3,18
	.byte	'IfxAsclin_DataLength_5',0,4,18
	.byte	'IfxAsclin_DataLength_6',0,5,18
	.byte	'IfxAsclin_DataLength_7',0,6,18
	.byte	'IfxAsclin_DataLength_8',0,7,18
	.byte	'IfxAsclin_DataLength_9',0,8,18
	.byte	'IfxAsclin_DataLength_10',0,9,18
	.byte	'IfxAsclin_DataLength_11',0,10,18
	.byte	'IfxAsclin_DataLength_12',0,11,18
	.byte	'IfxAsclin_DataLength_13',0,12,18
	.byte	'IfxAsclin_DataLength_14',0,13,18
	.byte	'IfxAsclin_DataLength_15',0,14,18
	.byte	'IfxAsclin_DataLength_16',0,15,0,30
	.byte	'IfxAsclin_DataLength',0,12,152,1,3
	.word	94372
	.byte	17,12,157,1,9,1,18
	.byte	'IfxAsclin_FrameMode_initialise',0,0,18
	.byte	'IfxAsclin_FrameMode_asc',0,1,18
	.byte	'IfxAsclin_FrameMode_spi',0,2,18
	.byte	'IfxAsclin_FrameMode_lin',0,3,0,30
	.byte	'IfxAsclin_FrameMode',0,12,163,1,3
	.word	94816
	.byte	17,12,168,1,9,1,18
	.byte	'IfxAsclin_HeaderResponseSelect_headerAndResponse',0,0,18
	.byte	'IfxAsclin_HeaderResponseSelect_headerOnly',0,1,0,30
	.byte	'IfxAsclin_HeaderResponseSelect',0,12,172,1,3
	.word	94963
	.byte	17,12,179,1,9,1,18
	.byte	'IfxAsclin_IdleDelay_0',0,0,18
	.byte	'IfxAsclin_IdleDelay_1',0,1,18
	.byte	'IfxAsclin_IdleDelay_2',0,2,18
	.byte	'IfxAsclin_IdleDelay_3',0,3,18
	.byte	'IfxAsclin_IdleDelay_4',0,4,18
	.byte	'IfxAsclin_IdleDelay_5',0,5,18
	.byte	'IfxAsclin_IdleDelay_6',0,6,18
	.byte	'IfxAsclin_IdleDelay_7',0,7,0,30
	.byte	'IfxAsclin_IdleDelay',0,12,189,1,3
	.word	95105
	.byte	17,12,195,1,9,1,18
	.byte	'IfxAsclin_LeadDelay_0',0,0,18
	.byte	'IfxAsclin_LeadDelay_1',0,1,18
	.byte	'IfxAsclin_LeadDelay_2',0,2,18
	.byte	'IfxAsclin_LeadDelay_3',0,3,18
	.byte	'IfxAsclin_LeadDelay_4',0,4,18
	.byte	'IfxAsclin_LeadDelay_5',0,5,18
	.byte	'IfxAsclin_LeadDelay_6',0,6,18
	.byte	'IfxAsclin_LeadDelay_7',0,7,0,30
	.byte	'IfxAsclin_LeadDelay',0,12,205,1,3
	.word	95333
	.byte	17,12,210,1,9,1,18
	.byte	'IfxAsclin_LinMode_slave',0,0,18
	.byte	'IfxAsclin_LinMode_master',0,1,0,30
	.byte	'IfxAsclin_LinMode',0,12,214,1,3
	.word	95561
	.byte	17,12,219,1,9,1,18
	.byte	'IfxAsclin_LinResponseTimeoutMode_frameTimeout',0,0,18
	.byte	'IfxAsclin_LinResponseTimeoutMode_responseTimeout',0,1,0,30
	.byte	'IfxAsclin_LinResponseTimeoutMode',0,12,223,1,3
	.word	95648
	.byte	17,12,228,1,9,1,18
	.byte	'IfxAsclin_OversamplingFactor_4',0,3,18
	.byte	'IfxAsclin_OversamplingFactor_5',0,4,18
	.byte	'IfxAsclin_OversamplingFactor_6',0,5,18
	.byte	'IfxAsclin_OversamplingFactor_7',0,6,18
	.byte	'IfxAsclin_OversamplingFactor_8',0,7,18
	.byte	'IfxAsclin_OversamplingFactor_9',0,8,18
	.byte	'IfxAsclin_OversamplingFactor_10',0,9,18
	.byte	'IfxAsclin_OversamplingFactor_11',0,10,18
	.byte	'IfxAsclin_OversamplingFactor_12',0,11,18
	.byte	'IfxAsclin_OversamplingFactor_13',0,12,18
	.byte	'IfxAsclin_OversamplingFactor_14',0,13,18
	.byte	'IfxAsclin_OversamplingFactor_15',0,14,18
	.byte	'IfxAsclin_OversamplingFactor_16',0,15,0,30
	.byte	'IfxAsclin_OversamplingFactor',0,12,243,1,3
	.word	95796
	.byte	17,12,248,1,9,1,18
	.byte	'IfxAsclin_ParityType_even',0,0,18
	.byte	'IfxAsclin_ParityType_odd',0,1,0,30
	.byte	'IfxAsclin_ParityType',0,12,252,1,3
	.word	96277
	.byte	17,12,129,2,9,1,18
	.byte	'IfxAsclin_ReceiveBufferMode_rxFifo',0,0,18
	.byte	'IfxAsclin_ReceiveBufferMode_rxBuffer',0,1,0,30
	.byte	'IfxAsclin_ReceiveBufferMode',0,12,133,2,3
	.word	96369
	.byte	17,12,138,2,9,1,18
	.byte	'IfxAsclin_RtsCtsPolarity_activeHigh',0,0,18
	.byte	'IfxAsclin_RtsCtsPolarity_activeLow',0,1,0,30
	.byte	'IfxAsclin_RtsCtsPolarity',0,12,142,2,3
	.word	96489
	.byte	17,12,147,2,9,1,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_1',0,0,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_2',0,1,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_3',0,2,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_4',0,3,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_5',0,4,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_6',0,5,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_7',0,6,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_8',0,7,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_9',0,8,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_10',0,9,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_11',0,10,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_12',0,11,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_13',0,12,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_14',0,13,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_15',0,14,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_16',0,15,0,30
	.byte	'IfxAsclin_RxFifoInterruptLevel',0,12,165,2,3
	.word	96605
	.byte	17,12,170,2,9,1,18
	.byte	'IfxAsclin_RxFifoOutletWidth_0',0,0,18
	.byte	'IfxAsclin_RxFifoOutletWidth_1',0,1,18
	.byte	'IfxAsclin_RxFifoOutletWidth_2',0,2,18
	.byte	'IfxAsclin_RxFifoOutletWidth_3',0,3,0,30
	.byte	'IfxAsclin_RxFifoOutletWidth',0,12,176,2,3
	.word	97219
	.byte	30
	.byte	'IfxAsclin_RxInputSelect',0,12,191,2,3
	.word	17747
	.byte	17,12,196,2,9,1,18
	.byte	'IfxAsclin_SamplePointPosition_1',0,1,18
	.byte	'IfxAsclin_SamplePointPosition_2',0,2,18
	.byte	'IfxAsclin_SamplePointPosition_3',0,3,18
	.byte	'IfxAsclin_SamplePointPosition_4',0,4,18
	.byte	'IfxAsclin_SamplePointPosition_5',0,5,18
	.byte	'IfxAsclin_SamplePointPosition_6',0,6,18
	.byte	'IfxAsclin_SamplePointPosition_7',0,7,18
	.byte	'IfxAsclin_SamplePointPosition_8',0,8,18
	.byte	'IfxAsclin_SamplePointPosition_9',0,9,18
	.byte	'IfxAsclin_SamplePointPosition_10',0,10,18
	.byte	'IfxAsclin_SamplePointPosition_11',0,11,18
	.byte	'IfxAsclin_SamplePointPosition_12',0,12,18
	.byte	'IfxAsclin_SamplePointPosition_13',0,13,18
	.byte	'IfxAsclin_SamplePointPosition_14',0,14,18
	.byte	'IfxAsclin_SamplePointPosition_15',0,15,0,30
	.byte	'IfxAsclin_SamplePointPosition',0,12,213,2,3
	.word	97424
	.byte	17,12,218,2,9,1,18
	.byte	'IfxAsclin_SamplesPerBit_one',0,0,18
	.byte	'IfxAsclin_SamplesPerBit_three',0,1,0,30
	.byte	'IfxAsclin_SamplesPerBit',0,12,222,2,3
	.word	97986
	.byte	17,12,228,2,9,1,18
	.byte	'IfxAsclin_ShiftDirection_lsbFirst',0,0,18
	.byte	'IfxAsclin_ShiftDirection_msbFirst',0,1,0,30
	.byte	'IfxAsclin_ShiftDirection',0,12,232,2,3
	.word	98088
	.byte	17,12,238,2,9,1,18
	.byte	'IfxAsclin_SlavePolarity_idleLow',0,0,18
	.byte	'IfxAsclin_SlavePolarity_idlehigh',0,1,0,30
	.byte	'IfxAsclin_SlavePolarity',0,12,242,2,3
	.word	98201
	.byte	17,12,247,2,9,1,18
	.byte	'IfxAsclin_SleepMode_enable',0,0,18
	.byte	'IfxAsclin_SleepMode_disable',0,1,0,30
	.byte	'IfxAsclin_SleepMode',0,12,251,2,3
	.word	98310
	.byte	17,12,136,3,9,1,18
	.byte	'IfxAsclin_StopBit_0',0,0,18
	.byte	'IfxAsclin_StopBit_1',0,1,18
	.byte	'IfxAsclin_StopBit_2',0,2,18
	.byte	'IfxAsclin_StopBit_3',0,3,18
	.byte	'IfxAsclin_StopBit_4',0,4,18
	.byte	'IfxAsclin_StopBit_5',0,5,18
	.byte	'IfxAsclin_StopBit_6',0,6,18
	.byte	'IfxAsclin_StopBit_7',0,7,0,30
	.byte	'IfxAsclin_StopBit',0,12,146,3,3
	.word	98405
	.byte	17,12,150,3,9,1,18
	.byte	'IfxAsclin_SuspendMode_none',0,0,18
	.byte	'IfxAsclin_SuspendMode_hard',0,1,18
	.byte	'IfxAsclin_SuspendMode_soft',0,2,0,30
	.byte	'IfxAsclin_SuspendMode',0,12,155,3,3
	.word	98615
	.byte	17,12,160,3,9,1,18
	.byte	'IfxAsclin_TxFifoInletWidth_0',0,0,18
	.byte	'IfxAsclin_TxFifoInletWidth_1',0,1,18
	.byte	'IfxAsclin_TxFifoInletWidth_2',0,2,18
	.byte	'IfxAsclin_TxFifoInletWidth_3',0,3,0,30
	.byte	'IfxAsclin_TxFifoInletWidth',0,12,166,3,3
	.word	98740
	.byte	17,12,171,3,9,1,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_0',0,0,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_1',0,1,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_2',0,2,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_3',0,3,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_4',0,4,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_5',0,5,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_6',0,6,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_7',0,7,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_8',0,8,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_9',0,9,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_10',0,10,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_11',0,11,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_12',0,12,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_13',0,13,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_14',0,14,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_15',0,15,0,30
	.byte	'IfxAsclin_TxFifoInterruptLevel',0,12,189,3,3
	.word	98907
	.byte	30
	.byte	'Ifx_Fifo_Shared',0,14,66,3
	.word	18064
	.byte	30
	.byte	'Ifx_Fifo',0,14,83,3
	.word	18155
	.byte	17,15,151,1,9,1,18
	.byte	'IfxStm_Comparator_0',0,0,18
	.byte	'IfxStm_Comparator_1',0,1,0,30
	.byte	'IfxStm_Comparator',0,15,155,1,3
	.word	99561
	.byte	17,15,159,1,9,1,18
	.byte	'IfxStm_ComparatorInterrupt_ir0',0,0,18
	.byte	'IfxStm_ComparatorInterrupt_ir1',0,1,0,30
	.byte	'IfxStm_ComparatorInterrupt',0,15,163,1,3
	.word	99639
	.byte	17,15,167,1,9,1,18
	.byte	'IfxStm_ComparatorOffset_0',0,0,18
	.byte	'IfxStm_ComparatorOffset_1',0,1,18
	.byte	'IfxStm_ComparatorOffset_2',0,2,18
	.byte	'IfxStm_ComparatorOffset_3',0,3,18
	.byte	'IfxStm_ComparatorOffset_4',0,4,18
	.byte	'IfxStm_ComparatorOffset_5',0,5,18
	.byte	'IfxStm_ComparatorOffset_6',0,6,18
	.byte	'IfxStm_ComparatorOffset_7',0,7,18
	.byte	'IfxStm_ComparatorOffset_8',0,8,18
	.byte	'IfxStm_ComparatorOffset_9',0,9,18
	.byte	'IfxStm_ComparatorOffset_10',0,10,18
	.byte	'IfxStm_ComparatorOffset_11',0,11,18
	.byte	'IfxStm_ComparatorOffset_12',0,12,18
	.byte	'IfxStm_ComparatorOffset_13',0,13,18
	.byte	'IfxStm_ComparatorOffset_14',0,14,18
	.byte	'IfxStm_ComparatorOffset_15',0,15,18
	.byte	'IfxStm_ComparatorOffset_16',0,16,18
	.byte	'IfxStm_ComparatorOffset_17',0,17,18
	.byte	'IfxStm_ComparatorOffset_18',0,18,18
	.byte	'IfxStm_ComparatorOffset_19',0,19,18
	.byte	'IfxStm_ComparatorOffset_20',0,20,18
	.byte	'IfxStm_ComparatorOffset_21',0,21,18
	.byte	'IfxStm_ComparatorOffset_22',0,22,18
	.byte	'IfxStm_ComparatorOffset_23',0,23,18
	.byte	'IfxStm_ComparatorOffset_24',0,24,18
	.byte	'IfxStm_ComparatorOffset_25',0,25,18
	.byte	'IfxStm_ComparatorOffset_26',0,26,18
	.byte	'IfxStm_ComparatorOffset_27',0,27,18
	.byte	'IfxStm_ComparatorOffset_28',0,28,18
	.byte	'IfxStm_ComparatorOffset_29',0,29,18
	.byte	'IfxStm_ComparatorOffset_30',0,30,18
	.byte	'IfxStm_ComparatorOffset_31',0,31,0,30
	.byte	'IfxStm_ComparatorOffset',0,15,201,1,3
	.word	99748
	.byte	17,15,205,1,9,1,18
	.byte	'IfxStm_ComparatorSize_1Bit',0,0,18
	.byte	'IfxStm_ComparatorSize_2Bits',0,1,18
	.byte	'IfxStm_ComparatorSize_3Bits',0,2,18
	.byte	'IfxStm_ComparatorSize_4Bits',0,3,18
	.byte	'IfxStm_ComparatorSize_5Bits',0,4,18
	.byte	'IfxStm_ComparatorSize_6Bits',0,5,18
	.byte	'IfxStm_ComparatorSize_7Bits',0,6,18
	.byte	'IfxStm_ComparatorSize_8Bits',0,7,18
	.byte	'IfxStm_ComparatorSize_9Bits',0,8,18
	.byte	'IfxStm_ComparatorSize_10Bits',0,9,18
	.byte	'IfxStm_ComparatorSize_11Bits',0,10,18
	.byte	'IfxStm_ComparatorSize_12Bits',0,11,18
	.byte	'IfxStm_ComparatorSize_13Bits',0,12,18
	.byte	'IfxStm_ComparatorSize_14Bits',0,13,18
	.byte	'IfxStm_ComparatorSize_15Bits',0,14,18
	.byte	'IfxStm_ComparatorSize_16Bits',0,15,18
	.byte	'IfxStm_ComparatorSize_17Bits',0,16,18
	.byte	'IfxStm_ComparatorSize_18Bits',0,17,18
	.byte	'IfxStm_ComparatorSize_19Bits',0,18,18
	.byte	'IfxStm_ComparatorSize_20Bits',0,19,18
	.byte	'IfxStm_ComparatorSize_21Bits',0,20,18
	.byte	'IfxStm_ComparatorSize_22Bits',0,21,18
	.byte	'IfxStm_ComparatorSize_23Bits',0,22,18
	.byte	'IfxStm_ComparatorSize_24Bits',0,23,18
	.byte	'IfxStm_ComparatorSize_25Bits',0,24,18
	.byte	'IfxStm_ComparatorSize_26Bits',0,25,18
	.byte	'IfxStm_ComparatorSize_27Bits',0,26,18
	.byte	'IfxStm_ComparatorSize_28Bits',0,27,18
	.byte	'IfxStm_ComparatorSize_29Bits',0,28,18
	.byte	'IfxStm_ComparatorSize_30Bits',0,29,18
	.byte	'IfxStm_ComparatorSize_31Bits',0,30,18
	.byte	'IfxStm_ComparatorSize_32Bits',0,31,0,30
	.byte	'IfxStm_ComparatorSize',0,15,239,1,3
	.word	100706
	.byte	17,15,244,1,9,1,18
	.byte	'IfxStm_SleepMode_enable',0,0,18
	.byte	'IfxStm_SleepMode_disable',0,1,0,30
	.byte	'IfxStm_SleepMode',0,15,248,1,3
	.word	101726
	.byte	17,15,252,1,9,1,18
	.byte	'IfxStm_SuspendMode_none',0,0,18
	.byte	'IfxStm_SuspendMode_hard',0,1,18
	.byte	'IfxStm_SuspendMode_soft',0,2,0,30
	.byte	'IfxStm_SuspendMode',0,15,129,2,3
	.word	101812
	.byte	30
	.byte	'IfxStdIf_InterfaceDriver',0,35,118,15
	.word	389
	.byte	3
	.word	18039
	.byte	33
	.word	650
	.byte	1,1,34
	.word	389
	.byte	34
	.word	389
	.byte	34
	.word	101958
	.byte	34
	.word	22012
	.byte	0,3
	.word	101963
	.byte	30
	.byte	'IfxStdIf_DPipe_Write',0,36,92,19
	.word	101991
	.byte	30
	.byte	'IfxStdIf_DPipe_Read',0,36,107,19
	.word	101991
	.byte	33
	.word	18052
	.byte	1,1,34
	.word	389
	.byte	0,3
	.word	102053
	.byte	30
	.byte	'IfxStdIf_DPipe_GetReadCount',0,36,115,18
	.word	102066
	.byte	14
	.word	650
	.byte	3
	.word	102107
	.byte	33
	.word	102112
	.byte	1,1,34
	.word	389
	.byte	0,3
	.word	102117
	.byte	30
	.byte	'IfxStdIf_DPipe_GetReadEvent',0,36,123,36
	.word	102130
	.byte	30
	.byte	'IfxStdIf_DPipe_GetWriteCount',0,36,147,1,18
	.word	102066
	.byte	3
	.word	102117
	.byte	30
	.byte	'IfxStdIf_DPipe_GetWriteEvent',0,36,155,1,37
	.word	102209
	.byte	33
	.word	650
	.byte	1,1,34
	.word	389
	.byte	34
	.word	18039
	.byte	34
	.word	22012
	.byte	0,3
	.word	102252
	.byte	30
	.byte	'IfxStdIf_DPipe_CanReadCount',0,36,166,1,19
	.word	102275
	.byte	30
	.byte	'IfxStdIf_DPipe_CanWriteCount',0,36,177,1,19
	.word	102275
	.byte	33
	.word	650
	.byte	1,1,34
	.word	389
	.byte	34
	.word	22012
	.byte	0,3
	.word	102355
	.byte	30
	.byte	'IfxStdIf_DPipe_FlushTx',0,36,186,1,19
	.word	102373
	.byte	35,1,1,34
	.word	389
	.byte	0,3
	.word	102410
	.byte	30
	.byte	'IfxStdIf_DPipe_ClearTx',0,36,200,1,16
	.word	102419
	.byte	30
	.byte	'IfxStdIf_DPipe_ClearRx',0,36,193,1,16
	.word	102419
	.byte	30
	.byte	'IfxStdIf_DPipe_OnReceive',0,36,208,1,16
	.word	102419
	.byte	30
	.byte	'IfxStdIf_DPipe_OnTransmit',0,36,215,1,16
	.word	102419
	.byte	30
	.byte	'IfxStdIf_DPipe_OnError',0,36,222,1,16
	.word	102419
	.byte	33
	.word	10311
	.byte	1,1,34
	.word	389
	.byte	0,3
	.word	102589
	.byte	30
	.byte	'IfxStdIf_DPipe_GetSendCount',0,36,131,1,18
	.word	102602
	.byte	33
	.word	22012
	.byte	1,1,34
	.word	389
	.byte	0,3
	.word	102644
	.byte	30
	.byte	'IfxStdIf_DPipe_GetTxTimeStamp',0,36,139,1,24
	.word	102657
	.byte	30
	.byte	'IfxStdIf_DPipe_ResetSendCount',0,36,229,1,16
	.word	102419
	.byte	10
	.byte	'IfxStdIf_DPipe_',0,36,233,1,8,76,13
	.byte	'driver',0
	.word	101925
	.byte	4,2,35,0,13
	.byte	'txDisabled',0
	.word	650
	.byte	1,2,35,4,13
	.byte	'write',0
	.word	101996
	.byte	4,2,35,8,13
	.byte	'read',0
	.word	102025
	.byte	4,2,35,12,13
	.byte	'getReadCount',0
	.word	102071
	.byte	4,2,35,16,13
	.byte	'getReadEvent',0
	.word	102135
	.byte	4,2,35,20,13
	.byte	'getWriteCount',0
	.word	102171
	.byte	4,2,35,24,13
	.byte	'getWriteEvent',0
	.word	102214
	.byte	4,2,35,28,13
	.byte	'canReadCount',0
	.word	102280
	.byte	4,2,35,32,13
	.byte	'canWriteCount',0
	.word	102317
	.byte	4,2,35,36,13
	.byte	'flushTx',0
	.word	102378
	.byte	4,2,35,40,13
	.byte	'clearTx',0
	.word	102424
	.byte	4,2,35,44,13
	.byte	'clearRx',0
	.word	102456
	.byte	4,2,35,48,13
	.byte	'onReceive',0
	.word	102488
	.byte	4,2,35,52,13
	.byte	'onTransmit',0
	.word	102522
	.byte	4,2,35,56,13
	.byte	'onError',0
	.word	102557
	.byte	4,2,35,60,13
	.byte	'getSendCount',0
	.word	102607
	.byte	4,2,35,64,13
	.byte	'getTxTimeStamp',0
	.word	102662
	.byte	4,2,35,68,13
	.byte	'resetSendCount',0
	.word	102701
	.byte	4,2,35,72,0,30
	.byte	'IfxStdIf_DPipe',0,36,71,32
	.word	102740
	.byte	3
	.word	383
	.byte	3
	.word	101963
	.byte	3
	.word	101963
	.byte	3
	.word	102053
	.byte	3
	.word	102117
	.byte	3
	.word	102053
	.byte	3
	.word	102117
	.byte	3
	.word	102252
	.byte	3
	.word	102252
	.byte	3
	.word	102355
	.byte	3
	.word	102410
	.byte	3
	.word	102410
	.byte	3
	.word	102410
	.byte	3
	.word	102410
	.byte	3
	.word	102410
	.byte	3
	.word	102589
	.byte	3
	.word	102644
	.byte	3
	.word	102410
	.byte	14
	.word	650
	.byte	3
	.word	103253
	.byte	30
	.byte	'IfxStdIf_DPipe_WriteEvent',0,36,73,32
	.word	103258
	.byte	30
	.byte	'IfxStdIf_DPipe_ReadEvent',0,36,74,32
	.word	103258
	.byte	20,37,252,1,9,1,11
	.byte	'parityError',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'frameError',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'rxFifoOverflow',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'rxFifoUnderflow',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'txFifoOverflow',0,1
	.word	650
	.byte	1,3,2,35,0,0,30
	.byte	'IfxAsclin_Asc_ErrorFlags',0,37,131,2,3
	.word	103330
	.byte	20,37,137,2,9,8,13
	.byte	'baudrate',0
	.word	271
	.byte	4,2,35,0,13
	.byte	'prescaler',0
	.word	667
	.byte	2,2,35,4,13
	.byte	'oversampling',0
	.word	95796
	.byte	1,2,35,6,0,30
	.byte	'IfxAsclin_Asc_BaudRate',0,37,142,2,3
	.word	103495
	.byte	20,37,146,2,9,2,13
	.byte	'medianFilter',0
	.word	97986
	.byte	1,2,35,0,13
	.byte	'samplePointPosition',0
	.word	97424
	.byte	1,2,35,1,0,30
	.byte	'IfxAsclin_Asc_BitTimingControl',0,37,150,2,3
	.word	103593
	.byte	20,37,154,2,9,6,13
	.byte	'inWidth',0
	.word	98740
	.byte	1,2,35,0,13
	.byte	'outWidth',0
	.word	97219
	.byte	1,2,35,1,13
	.byte	'txFifoInterruptLevel',0
	.word	98907
	.byte	1,2,35,2,13
	.byte	'rxFifoInterruptLevel',0
	.word	96605
	.byte	1,2,35,3,13
	.byte	'buffMode',0
	.word	96369
	.byte	1,2,35,4,0,30
	.byte	'IfxAsclin_Asc_FifoControl',0,37,161,2,3
	.word	103691
	.byte	20,37,165,2,9,8,13
	.byte	'idleDelay',0
	.word	95105
	.byte	1,2,35,0,13
	.byte	'stopBit',0
	.word	98405
	.byte	1,2,35,1,13
	.byte	'frameMode',0
	.word	94816
	.byte	1,2,35,2,13
	.byte	'shiftDir',0
	.word	98088
	.byte	1,2,35,3,13
	.byte	'parityType',0
	.word	96277
	.byte	1,2,35,4,13
	.byte	'dataLength',0
	.word	94372
	.byte	1,2,35,5,13
	.byte	'parityBit',0
	.word	650
	.byte	1,2,35,6,0,30
	.byte	'IfxAsclin_Asc_FrameControl',0,37,174,2,3
	.word	103846
	.byte	20,37,178,2,9,8,13
	.byte	'txPriority',0
	.word	667
	.byte	2,2,35,0,13
	.byte	'rxPriority',0
	.word	667
	.byte	2,2,35,2,13
	.byte	'erPriority',0
	.word	667
	.byte	2,2,35,4,13
	.byte	'typeOfService',0
	.word	93195
	.byte	1,2,35,6,0,30
	.byte	'IfxAsclin_Asc_InterruptConfig',0,37,184,2,3
	.word	104021
	.byte	21
	.word	93273
	.byte	3
	.word	104150
	.byte	21
	.word	93354
	.byte	3
	.word	104160
	.byte	21
	.word	93434
	.byte	3
	.word	104170
	.byte	21
	.word	93682
	.byte	3
	.word	104180
	.byte	20,37,188,2,9,32,13
	.byte	'cts',0
	.word	104155
	.byte	4,2,35,0,13
	.byte	'ctsMode',0
	.word	9220
	.byte	1,2,35,4,13
	.byte	'rx',0
	.word	104165
	.byte	4,2,35,8,13
	.byte	'rxMode',0
	.word	9220
	.byte	1,2,35,12,13
	.byte	'rts',0
	.word	104175
	.byte	4,2,35,16,13
	.byte	'rtsMode',0
	.word	9425
	.byte	1,2,35,20,13
	.byte	'tx',0
	.word	104185
	.byte	4,2,35,24,13
	.byte	'txMode',0
	.word	9425
	.byte	1,2,35,28,13
	.byte	'pinDriver',0
	.word	56195
	.byte	1,2,35,29,0,30
	.byte	'IfxAsclin_Asc_Pins',0,37,199,2,3
	.word	104190
	.byte	12,37,205,2,9,1,13
	.byte	'ALL',0
	.word	650
	.byte	1,2,35,0,13
	.byte	'flags',0
	.word	103330
	.byte	1,2,35,0,0,30
	.byte	'IfxAsclin_Asc_ErrorFlagsUnion',0,37,209,2,3
	.word	104360
	.byte	30
	.byte	'uart_tx_pin_enum',0,23,74,2
	.word	26432
	.byte	30
	.byte	'uart_rx_pin_enum',0,23,100,2
	.word	26884
	.byte	30
	.byte	'uart_index_enum',0,23,109,2
	.word	26142
	.byte	30
	.byte	'wireless_type_enum',0,24,61,2
	.word	27262
	.byte	17,38,74,9,1,18
	.byte	'WIRELESS_UART_AUTO_BAUD_RATE_SUCCESS',0,0,18
	.byte	'WIRELESS_UART_AUTO_BAUD_RATE_INIT',0,1,18
	.byte	'WIRELESS_UART_AUTO_BAUD_RATE_START',0,2,18
	.byte	'WIRELESS_UART_AUTO_BAUD_RATE_GET_ACK',0,3,0,30
	.byte	'wireless_uart_auto_baudrate_state_enum',0,38,80,2
	.word	104535
.L139:
	.byte	15,64
	.word	650
	.byte	16,63,0
.L140:
	.byte	14
	.word	104535
	.byte	15,3
	.word	650
	.byte	16,2,0
.L141:
	.byte	14
	.word	104753
	.byte	15,4
	.word	650
	.byte	16,3,0,36
	.byte	'camera_send_image_frame_header',0,25,183,1,18
	.word	104767
	.byte	1,1,0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L54:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,21,0,54,15,39,12,0,0,3,15,0,73,19,0,0,4,46,1,3,8,32,13
	.byte	58,15,59,15,57,15,54,15,39,12,0,0,5,5,0,3,8,58,15,59,15,57,15,73,19,0,0,6,11,0,0,0,7,36,0,3,8,11,15,62
	.byte	15,0,0,8,46,1,3,8,32,13,58,15,59,15,57,15,73,19,54,15,39,12,0,0,9,59,0,3,8,0,0,10,19,1,3,8,58,15,59,15
	.byte	57,15,11,15,0,0,11,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,12,23,1,58,15,59,15,57,15,11,15,0,0,13,13
	.byte	0,3,8,73,19,11,15,56,9,0,0,14,53,0,73,19,0,0,15,1,1,11,15,73,19,0,0,16,33,0,47,15,0,0,17,4,1,58,15,59
	.byte	15,57,15,11,15,0,0,18,40,0,3,8,28,13,0,0,19,11,1,0,0,20,19,1,58,15,59,15,57,15,11,15,0,0,21,38,0,73,19
	.byte	0,0,22,46,1,3,8,58,15,59,15,57,15,73,19,54,15,39,12,63,12,60,12,0,0,23,5,0,58,15,59,15,57,15,73,19,0,0
	.byte	24,46,1,3,8,58,15,59,15,57,15,54,15,39,12,63,12,60,12,0,0,25,46,1,49,19,0,0,26,5,0,49,19,0,0,27,29,1,49
	.byte	19,0,0,28,11,0,49,19,0,0,29,11,1,49,19,0,0,30,22,0,3,8,58,15,59,15,57,15,73,19,0,0,31,46,0,3,8,58,15,59
	.byte	15,57,15,54,15,63,12,60,12,0,0,32,21,0,54,15,0,0,33,21,1,73,19,54,15,39,12,0,0,34,5,0,73,19,0,0,35,21
	.byte	1,54,15,39,12,0,0,36,52,0,3,8,58,15,59,15,57,15,73,19,63,12,60,12,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L55:
	.word	.L192-.L191
.L191:
	.half	3
	.word	.L194-.L193
.L193:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std',0
	.byte	'F:\\ADS\\AURIX-Studio-1.10.2\\tools\\Compilers\\Tasking_1.1r8\\ctc\\include\\',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc',0
	.byte	0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_Intrinsics.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Scu\\Std\\IfxScuWdt.h',0
	.byte	0,0,0
	.byte	'IfxScu_regdef.h',0,1,0,0
	.byte	'IFXPORT.h',0,2,0,0
	.byte	'IfxPort_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxCpu_cfg.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Scu\\Std\\IfxScuCcu.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Src\\Std\\IfxSrc.h',0
	.byte	0,0,0
	.byte	'IfxSrc_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Asclin\\Std\\IfxAsclin.h',0
	.byte	0,0,0
	.byte	'IfxAsclin_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Lib\\DataHandling\\Ifx_Fifo.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Stm\\Std\\IfxStm.h',0
	.byte	0,0,0
	.byte	'IfxStm_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\SysSe\\Bsp\\Bsp.h',0
	.byte	0,0,0
	.byte	'string.h',0,3,0,0
	.byte	'zf_common_debug.h',0,4,0,0
	.byte	'zf_common_fifo.h',0,4,0,0
	.byte	'zf_driver_delay.h',0,5,0,0
	.byte	'zf_driver_gpio.h',0,5,0,0
	.byte	'zf_driver_uart.h',0,5,0,0
	.byte	'..\\libraries\\zf_device\\zf_device_type.h',0,0,0,0
	.byte	'../libraries/zf_device/zf_device_wireless_uart.c',0,0,0,0
	.byte	'stdio.h',0,3,0,0
	.byte	'Platform_Types.h',0,6,0,0
	.byte	'ifx_types.h',0,6,0,0
	.byte	'zf_common_typedef.h',0,4,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxScu_cfg.h',0
	.byte	0,0,0
	.byte	'IfxCpu_regdef.h',0,1,0,0
	.byte	'IfxFlash_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxSrc_cfg.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_PinMap\\IfxAsclin_PinMap.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\StdIf\\IfxStdIf.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\StdIf\\IfxStdIf_DPipe.h',0
	.byte	0,0,0
	.byte	'ifxAsclin_Asc.h',0,7,0,0
	.byte	'..\\libraries\\zf_device\\zf_device_wireless_uart.h',0,0,0,0,0
.L194:
.L192:
	.sdecl	'.debug_info',debug,cluster('wireless_uart_send_byte')
	.sect	'.debug_info'
.L56:
	.word	315
	.half	3
	.word	.L57
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_wireless_uart.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L59,.L58
	.byte	2
	.word	.L52
	.byte	3
	.byte	'wireless_uart_send_byte',0,1,72,8
	.word	.L101
	.byte	1,1,1
	.word	.L39,.L102,.L38
	.byte	4
	.byte	'data',0,1,72,45
	.word	.L103,.L104
	.byte	5
	.word	.L39,.L102
	.byte	6
	.byte	'time_count',0,1,74,12
	.word	.L105,.L106
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('wireless_uart_send_byte')
	.sect	'.debug_abbrev'
.L57:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('wireless_uart_send_byte')
	.sect	'.debug_line'
.L58:
	.word	.L196-.L195
.L195:
	.half	3
	.word	.L198-.L197
.L197:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_wireless_uart.c',0,0,0,0,0
.L198:
	.byte	5,8,7,0,5,2
	.word	.L39
	.byte	3,199,0,1,5,23,9
	.half	.L142-.L39
	.byte	3,2,1,5,21,9
	.half	.L143-.L142
	.byte	3,1,1,5,28,9
	.half	.L3-.L143
	.byte	3,2,1,5,13,9
	.half	.L199-.L3
	.byte	1,5,29,7,9
	.half	.L200-.L199
	.byte	3,2,1,5,50,9
	.half	.L201-.L200
	.byte	1,5,13,9
	.half	.L145-.L201
	.byte	3,1,1,5,20,9
	.half	.L4-.L145
	.byte	3,2,1,5,25,9
	.half	.L202-.L4
	.byte	3,1,1,5,21,9
	.half	.L2-.L202
	.byte	3,120,1,5,15,7,9
	.half	.L5-.L2
	.byte	3,10,1,5,5,9
	.half	.L203-.L5
	.byte	1,5,1,9
	.half	.L6-.L203
	.byte	3,1,1,7,9
	.half	.L60-.L6
	.byte	0,1,1
.L196:
	.sdecl	'.debug_ranges',debug,cluster('wireless_uart_send_byte')
	.sect	'.debug_ranges'
.L59:
	.word	-1,.L39,0,.L60-.L39,0,0
	.sdecl	'.debug_info',debug,cluster('wireless_uart_send_buffer')
	.sect	'.debug_info'
.L61:
	.word	343
	.half	3
	.word	.L62
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_wireless_uart.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L64,.L63
	.byte	2
	.word	.L52
	.byte	3
	.byte	'wireless_uart_send_buffer',0,1,96,8
	.word	.L101
	.byte	1,1,1
	.word	.L41,.L107,.L40
	.byte	4
	.byte	'buff',0,1,96,48
	.word	.L108,.L109
	.byte	4
	.byte	'len',0,1,96,61
	.word	.L101,.L110
	.byte	5
	.word	.L41,.L107
	.byte	5
	.word	.L111,.L107
	.byte	6
	.byte	'time_count',0,1,99,12
	.word	.L105,.L112
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('wireless_uart_send_buffer')
	.sect	'.debug_abbrev'
.L62:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('wireless_uart_send_buffer')
	.sect	'.debug_line'
.L63:
	.word	.L205-.L204
.L204:
	.half	3
	.word	.L207-.L206
.L206:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_wireless_uart.c',0,0,0,0,0
.L207:
	.byte	5,8,7,0,5,2
	.word	.L41
	.byte	3,223,0,1,5,5,9
	.half	.L149-.L41
	.byte	3,2,1,5,23,9
	.half	.L111-.L149
	.byte	3,1,1,5,19,9
	.half	.L150-.L111
	.byte	3,1,1,5,28,9
	.half	.L8-.L150
	.byte	3,2,1,5,13,9
	.half	.L208-.L8
	.byte	1,5,16,7,9
	.half	.L209-.L208
	.byte	3,2,1,5,13,9
	.half	.L210-.L209
	.byte	1,5,35,7,9
	.half	.L211-.L210
	.byte	3,2,1,5,62,9
	.half	.L212-.L211
	.byte	1,5,22,9
	.half	.L152-.L212
	.byte	3,1,1,5,21,9
	.half	.L213-.L152
	.byte	3,1,1,5,28,9
	.half	.L214-.L213
	.byte	3,1,1,5,65,9
	.half	.L215-.L214
	.byte	3,125,1,5,35,9
	.half	.L10-.L215
	.byte	3,7,1,5,62,9
	.half	.L216-.L10
	.byte	1,5,21,9
	.half	.L154-.L216
	.byte	3,1,1,5,17,9
	.half	.L217-.L154
	.byte	3,1,1,5,13,9
	.half	.L11-.L217
	.byte	3,124,1,5,48,9
	.half	.L9-.L11
	.byte	3,9,1,5,16,9
	.half	.L218-.L9
	.byte	1,5,13,9
	.half	.L219-.L218
	.byte	1,5,17,7,9
	.half	.L220-.L219
	.byte	3,2,1,5,29,9
	.half	.L14-.L220
	.byte	3,2,1,5,19,9
	.half	.L7-.L14
	.byte	3,104,1,5,5,7,9
	.half	.L12-.L7
	.byte	3,27,1,5,1,9
	.half	.L16-.L12
	.byte	3,1,1,7,9
	.half	.L65-.L16
	.byte	0,1,1
.L205:
	.sdecl	'.debug_ranges',debug,cluster('wireless_uart_send_buffer')
	.sect	'.debug_ranges'
.L64:
	.word	-1,.L41,0,.L65-.L41,0,0
	.sdecl	'.debug_info',debug,cluster('wireless_uart_send_string')
	.sect	'.debug_info'
.L66:
	.word	346
	.half	3
	.word	.L67
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_wireless_uart.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L69,.L68
	.byte	2
	.word	.L52
	.byte	3
	.byte	'wireless_uart_send_string',0,1,137,1,8
	.word	.L101
	.byte	1,1,1
	.word	.L43,.L113,.L42
	.byte	4
	.byte	'str',0,1,137,1,47
	.word	.L114,.L115
	.byte	5
	.word	.L43,.L113
	.byte	5
	.word	.L116,.L113
	.byte	6
	.byte	'time_count',0,1,140,1,12
	.word	.L105,.L117
	.byte	6
	.byte	'len',0,1,141,1,12
	.word	.L101,.L118
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('wireless_uart_send_string')
	.sect	'.debug_abbrev'
.L67:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('wireless_uart_send_string')
	.sect	'.debug_line'
.L68:
	.word	.L222-.L221
.L221:
	.half	3
	.word	.L224-.L223
.L223:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_wireless_uart.c',0,0,0,0,0
.L224:
	.byte	5,8,7,0,5,2
	.word	.L43
	.byte	3,136,1,1,5,5,9
	.half	.L158-.L43
	.byte	3,2,1,5,23,9
	.half	.L116-.L158
	.byte	3,1,1,5,25,9
	.half	.L159-.L116
	.byte	3,1,1,5,16,9
	.half	.L161-.L159
	.byte	1,5,19,9
	.half	.L162-.L161
	.byte	3,1,1,5,28,9
	.half	.L18-.L162
	.byte	3,2,1,5,13,9
	.half	.L225-.L18
	.byte	1,5,16,7,9
	.half	.L226-.L225
	.byte	3,2,1,5,13,9
	.half	.L227-.L226
	.byte	1,5,35,7,9
	.half	.L228-.L227
	.byte	3,2,1,5,76,9
	.half	.L229-.L228
	.byte	1,5,21,9
	.half	.L164-.L229
	.byte	3,1,1,9
	.half	.L230-.L164
	.byte	3,1,1,5,28,9
	.half	.L231-.L230
	.byte	3,1,1,5,79,9
	.half	.L232-.L231
	.byte	3,125,1,5,35,9
	.half	.L20-.L232
	.byte	3,7,1,5,76,9
	.half	.L233-.L20
	.byte	1,5,21,9
	.half	.L166-.L233
	.byte	3,1,1,5,17,9
	.half	.L234-.L166
	.byte	3,1,1,5,13,9
	.half	.L21-.L234
	.byte	3,124,1,5,48,9
	.half	.L19-.L21
	.byte	3,9,1,5,16,9
	.half	.L235-.L19
	.byte	1,5,13,9
	.half	.L236-.L235
	.byte	1,5,17,7,9
	.half	.L237-.L236
	.byte	3,2,1,5,29,9
	.half	.L24-.L237
	.byte	3,2,1,5,19,9
	.half	.L17-.L24
	.byte	3,104,1,5,5,7,9
	.half	.L22-.L17
	.byte	3,27,1,5,1,9
	.half	.L26-.L22
	.byte	3,1,1,7,9
	.half	.L70-.L26
	.byte	0,1,1
.L222:
	.sdecl	'.debug_ranges',debug,cluster('wireless_uart_send_string')
	.sect	'.debug_ranges'
.L69:
	.word	-1,.L43,0,.L70-.L43,0,0
	.sdecl	'.debug_info',debug,cluster('wireless_uart_send_image')
	.sect	'.debug_info'
.L71:
	.word	330
	.half	3
	.word	.L72
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_wireless_uart.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L74,.L73
	.byte	2
	.word	.L52
	.byte	3
	.byte	'wireless_uart_send_image',0,1,180,1,6,1,1,1
	.word	.L45,.L119,.L44
	.byte	4
	.byte	'image_addr',0,1,180,1,45
	.word	.L108,.L120
	.byte	4
	.byte	'image_size',0,1,180,1,64
	.word	.L101,.L121
	.byte	5
	.word	.L45,.L119
	.byte	6
	.word	.L122,.L119
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('wireless_uart_send_image')
	.sect	'.debug_abbrev'
.L72:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,11,0,17
	.byte	1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('wireless_uart_send_image')
	.sect	'.debug_line'
.L73:
	.word	.L239-.L238
.L238:
	.half	3
	.word	.L241-.L240
.L240:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_wireless_uart.c',0,0,0,0,0
.L241:
	.byte	5,6,7,0,5,2
	.word	.L45
	.byte	3,179,1,1,5,5,9
	.half	.L172-.L45
	.byte	3,2,1,5,31,9
	.half	.L122-.L172
	.byte	3,2,1,5,63,9
	.half	.L242-.L122
	.byte	1,5,52,9
	.half	.L243-.L242
	.byte	3,1,1,5,1,9
	.half	.L174-.L243
	.byte	3,1,1,7,9
	.half	.L75-.L174
	.byte	0,1,1
.L239:
	.sdecl	'.debug_ranges',debug,cluster('wireless_uart_send_image')
	.sect	'.debug_ranges'
.L74:
	.word	-1,.L45,0,.L75-.L45,0,0
	.sdecl	'.debug_info',debug,cluster('wireless_uart_read_buffer')
	.sect	'.debug_info'
.L76:
	.word	345
	.half	3
	.word	.L77
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_wireless_uart.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L79,.L78
	.byte	2
	.word	.L52
	.byte	3
	.byte	'wireless_uart_read_buffer',0,1,196,1,8
	.word	.L101
	.byte	1,1,1
	.word	.L47,.L123,.L46
	.byte	4
	.byte	'buff',0,1,196,1,42
	.word	.L124,.L125
	.byte	4
	.byte	'len',0,1,196,1,55
	.word	.L101,.L126
	.byte	5
	.word	.L47,.L123
	.byte	5
	.word	.L127,.L123
	.byte	6
	.byte	'data_len',0,1,199,1,12
	.word	.L101,.L128
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('wireless_uart_read_buffer')
	.sect	'.debug_abbrev'
.L77:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('wireless_uart_read_buffer')
	.sect	'.debug_line'
.L78:
	.word	.L245-.L244
.L244:
	.half	3
	.word	.L247-.L246
.L246:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_wireless_uart.c',0,0,0,0,0
.L247:
	.byte	5,8,7,0,5,2
	.word	.L47
	.byte	3,195,1,1,5,5,9
	.half	.L180-.L47
	.byte	3,2,1,5,21,9
	.half	.L127-.L180
	.byte	3,1,1,5,23,9
	.half	.L248-.L127
	.byte	3,1,1,5,50,9
	.half	.L249-.L248
	.byte	1,5,60,9
	.half	.L250-.L249
	.byte	1,5,12,9
	.half	.L182-.L250
	.byte	3,1,1,5,5,9
	.half	.L251-.L182
	.byte	1,5,1,9
	.half	.L27-.L251
	.byte	3,1,1,7,9
	.half	.L80-.L27
	.byte	0,1,1
.L245:
	.sdecl	'.debug_ranges',debug,cluster('wireless_uart_read_buffer')
	.sect	'.debug_ranges'
.L79:
	.word	-1,.L47,0,.L80-.L47,0,0
	.sdecl	'.debug_info',debug,cluster('wireless_uart_callback')
	.sect	'.debug_info'
.L81:
	.word	319
	.half	3
	.word	.L82
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_wireless_uart.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L84,.L83
	.byte	2
	.word	.L52
	.byte	3
	.byte	'wireless_uart_callback',0,1,213,1,6,1,1,1
	.word	.L49,.L129,.L48
	.byte	4
	.word	.L49,.L129
	.byte	4
	.word	.L130,.L28
	.byte	5
	.byte	'wireless_auto_baud_count',0,1,220,1,16
	.word	.L101,.L131
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('wireless_uart_callback')
	.sect	'.debug_abbrev'
.L82:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('wireless_uart_callback')
	.sect	'.debug_line'
.L83:
	.word	.L253-.L252
.L252:
	.half	3
	.word	.L255-.L254
.L254:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_wireless_uart.c',0,0,0,0,0
.L255:
	.byte	5,6,7,0,5,2
	.word	.L49
	.byte	3,212,1,1,5,21,9
	.half	.L183-.L49
	.byte	3,2,1,5,43,9
	.half	.L256-.L183
	.byte	1,5,24,9
	.half	.L257-.L256
	.byte	3,1,1,5,45,9
	.half	.L258-.L257
	.byte	1,5,65,9
	.half	.L259-.L258
	.byte	1,5,46,9
	.half	.L260-.L259
	.byte	3,2,1,5,8,9
	.half	.L261-.L260
	.byte	1,5,89,7,9
	.half	.L262-.L261
	.byte	1,5,75,9
	.half	.L263-.L262
	.byte	1,5,43,7,9
	.half	.L130-.L263
	.byte	3,2,1,5,41,9
	.half	.L264-.L130
	.byte	1,5,9,9
	.half	.L265-.L264
	.byte	3,1,1,5,35,9
	.half	.L266-.L265
	.byte	1,5,33,9
	.half	.L267-.L266
	.byte	1,5,27,9
	.half	.L268-.L267
	.byte	3,1,1,5,56,9
	.half	.L269-.L268
	.byte	1,5,92,9
	.half	.L270-.L269
	.byte	1,5,118,9
	.half	.L271-.L270
	.byte	1,5,1,9
	.half	.L28-.L271
	.byte	3,3,1,7,9
	.half	.L85-.L28
	.byte	0,1,1
.L253:
	.sdecl	'.debug_ranges',debug,cluster('wireless_uart_callback')
	.sect	'.debug_ranges'
.L84:
	.word	-1,.L49,0,.L85-.L49,0,0
	.sdecl	'.debug_info',debug,cluster('wireless_uart_init')
	.sect	'.debug_info'
.L86:
	.word	360
	.half	3
	.word	.L87
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_wireless_uart.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L89,.L88
	.byte	2
	.word	.L52
	.byte	3
	.byte	'wireless_uart_init',0,1,234,1,7
	.word	.L132
	.byte	1,1,1
	.word	.L51,.L133,.L50
	.byte	4
	.word	.L51,.L133
	.byte	5
	.byte	'return_state',0,1,236,1,11
	.word	.L132,.L134
	.byte	4
	.word	.L135,.L133
	.byte	5
	.byte	'rts_init_status',0,1,246,1,11
	.word	.L132,.L136
	.byte	5
	.byte	'time_count',0,1,247,1,12
	.word	.L105,.L137
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('wireless_uart_init')
	.sect	'.debug_abbrev'
.L87:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('wireless_uart_init')
	.sect	'.debug_line'
.L88:
	.word	.L273-.L272
.L272:
	.half	3
	.word	.L275-.L274
.L274:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_wireless_uart.c',0,0,0,0,0
.L275:
	.byte	5,24,7,0,5,2
	.word	.L51
	.byte	3,235,1,1,5,23,9
	.half	.L184-.L51
	.byte	3,1,1,5,38,9
	.half	.L276-.L184
	.byte	1,5,16,9
	.half	.L277-.L276
	.byte	3,2,1,5,36,9
	.half	.L278-.L277
	.byte	1,5,52,9
	.half	.L279-.L278
	.byte	1,5,74,9
	.half	.L280-.L279
	.byte	1,5,15,9
	.half	.L281-.L280
	.byte	3,1,1,5,38,9
	.half	.L282-.L281
	.byte	1,5,43,9
	.half	.L283-.L282
	.byte	1,5,54,9
	.half	.L284-.L283
	.byte	1,5,5,9
	.half	.L135-.L284
	.byte	3,9,1,5,31,9
	.half	.L285-.L135
	.byte	1,5,29,9
	.half	.L286-.L285
	.byte	1,5,5,9
	.half	.L287-.L286
	.byte	3,1,1,5,34,9
	.half	.L288-.L287
	.byte	1,5,32,9
	.half	.L289-.L288
	.byte	1,5,5,9
	.half	.L290-.L289
	.byte	3,1,1,5,34,9
	.half	.L291-.L290
	.byte	1,5,32,9
	.half	.L292-.L291
	.byte	1,5,5,9
	.half	.L293-.L292
	.byte	3,1,1,5,34,9
	.half	.L294-.L293
	.byte	1,5,32,9
	.half	.L295-.L294
	.byte	1,5,38,9
	.half	.L296-.L295
	.byte	3,2,1,5,21,9
	.half	.L185-.L296
	.byte	1,5,15,9
	.half	.L187-.L185
	.byte	3,1,1,5,38,9
	.half	.L297-.L187
	.byte	1,5,60,9
	.half	.L298-.L297
	.byte	1,5,16,9
	.half	.L189-.L298
	.byte	3,2,1,5,37,9
	.half	.L299-.L189
	.byte	1,5,62,9
	.half	.L300-.L299
	.byte	1,5,84,9
	.half	.L301-.L300
	.byte	1,5,23,9
	.half	.L302-.L301
	.byte	3,1,1,5,44,9
	.half	.L303-.L302
	.byte	1,5,21,9
	.half	.L304-.L303
	.byte	3,2,1,5,20,9
	.half	.L305-.L304
	.byte	3,1,1,5,43,9
	.half	.L306-.L305
	.byte	1,5,21,9
	.half	.L307-.L306
	.byte	3,1,1,5,23,9
	.half	.L308-.L307
	.byte	3,1,1,5,9,9
	.half	.L30-.L308
	.byte	3,4,1,5,35,9
	.half	.L309-.L30
	.byte	1,5,33,9
	.half	.L188-.L309
	.byte	1,5,46,9
	.half	.L310-.L188
	.byte	3,1,1,5,69,9
	.half	.L311-.L310
	.byte	1,5,25,9
	.half	.L312-.L311
	.byte	1,5,69,9
	.half	.L313-.L312
	.byte	1,5,46,9
	.half	.L314-.L313
	.byte	3,1,1,5,69,9
	.half	.L315-.L314
	.byte	1,5,25,9
	.half	.L316-.L315
	.byte	1,5,69,9
	.half	.L317-.L316
	.byte	1,5,46,9
	.half	.L318-.L317
	.byte	3,1,1,5,69,9
	.half	.L319-.L318
	.byte	1,5,25,9
	.half	.L320-.L319
	.byte	1,5,69,9
	.half	.L321-.L320
	.byte	1,5,25,9
	.half	.L322-.L321
	.byte	3,1,1,5,52,9
	.half	.L323-.L322
	.byte	3,2,1,5,9,9
	.half	.L324-.L323
	.byte	1,5,26,7,9
	.half	.L325-.L324
	.byte	3,2,1,5,13,9
	.half	.L326-.L325
	.byte	3,1,1,5,21,9
	.half	.L31-.L326
	.byte	3,4,1,5,44,9
	.half	.L327-.L31
	.byte	1,5,13,9
	.half	.L328-.L327
	.byte	1,5,21,9
	.half	.L329-.L328
	.byte	3,1,1,5,44,9
	.half	.L330-.L329
	.byte	1,5,13,9
	.half	.L331-.L330
	.byte	1,5,18,9
	.half	.L332-.L331
	.byte	1,5,21,7,9
	.half	.L333-.L332
	.byte	3,1,1,5,44,9
	.half	.L334-.L333
	.byte	1,5,13,9
	.half	.L335-.L334
	.byte	1,5,18,9
	.half	.L336-.L335
	.byte	1,5,26,7,9
	.half	.L337-.L336
	.byte	3,2,1,5,13,9
	.half	.L338-.L337
	.byte	3,1,1,5,9,9
	.half	.L33-.L338
	.byte	3,2,1,5,35,9
	.half	.L339-.L33
	.byte	1,5,33,9
	.half	.L340-.L339
	.byte	1,5,19,9
	.half	.L341-.L340
	.byte	3,2,1,5,42,9
	.half	.L342-.L341
	.byte	1,5,47,9
	.half	.L343-.L342
	.byte	1,5,50,9
	.half	.L344-.L343
	.byte	1,5,25,9
	.half	.L345-.L344
	.byte	3,1,1,5,5,9
	.half	.L32-.L345
	.byte	3,3,1,5,1,9
	.half	.L37-.L32
	.byte	3,1,1,7,9
	.half	.L90-.L37
	.byte	0,1,1
.L273:
	.sdecl	'.debug_ranges',debug,cluster('wireless_uart_init')
	.sect	'.debug_ranges'
.L89:
	.word	-1,.L51,0,.L90-.L51,0,0
	.sdecl	'.debug_info',debug,cluster('wireless_uart_fifo')
	.sect	'.debug_info'
.L91:
	.word	242
	.half	3
	.word	.L92
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_wireless_uart.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L52
	.byte	3
	.byte	'wireless_uart_fifo',0,25,56,57
	.word	.L138
	.byte	5,3
	.word	wireless_uart_fifo
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('wireless_uart_fifo')
	.sect	'.debug_abbrev'
.L92:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_info',debug,cluster('wireless_uart_buffer')
	.sect	'.debug_info'
.L93:
	.word	244
	.half	3
	.word	.L94
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_wireless_uart.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L52
	.byte	3
	.byte	'wireless_uart_buffer',0,25,57,57
	.word	.L139
	.byte	5,3
	.word	wireless_uart_buffer
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('wireless_uart_buffer')
	.sect	'.debug_abbrev'
.L94:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_info',debug,cluster('wireless_uart_data')
	.sect	'.debug_info'
.L95:
	.word	242
	.half	3
	.word	.L96
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_wireless_uart.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L52
	.byte	3
	.byte	'wireless_uart_data',0,25,59,57
	.word	.L132
	.byte	5,3
	.word	wireless_uart_data
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('wireless_uart_data')
	.sect	'.debug_abbrev'
.L96:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_info',debug,cluster('wireless_auto_baud_flag')
	.sect	'.debug_info'
.L97:
	.word	247
	.half	3
	.word	.L98
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_wireless_uart.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L52
	.byte	3
	.byte	'wireless_auto_baud_flag',0,25,61,57
	.word	.L140
	.byte	5,3
	.word	wireless_auto_baud_flag
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('wireless_auto_baud_flag')
	.sect	'.debug_abbrev'
.L98:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_info',debug,cluster('wireless_auto_baud_data')
	.sect	'.debug_info'
.L99:
	.word	247
	.half	3
	.word	.L100
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_wireless_uart.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L52
	.byte	3
	.byte	'wireless_auto_baud_data',0,25,62,57
	.word	.L141
	.byte	5,3
	.word	wireless_auto_baud_data
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('wireless_auto_baud_data')
	.sect	'.debug_abbrev'
.L100:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_loc',debug,cluster('wireless_uart_callback')
	.sect	'.debug_loc'
.L131:
	.word	-1,.L49,0,.L129-.L49
	.half	2
	.byte	145,120
	.word	0,0
.L48:
	.word	-1,.L49,0,.L183-.L49
	.half	2
	.byte	138,0
	.word	.L183-.L49,.L129-.L49
	.half	2
	.byte	138,8
	.word	.L129-.L49,.L129-.L49
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('wireless_uart_init')
	.sect	'.debug_loc'
.L134:
	.word	-1,.L51,.L184-.L51,.L133-.L51
	.half	1
	.byte	88
	.word	.L190-.L51,.L133-.L51
	.half	1
	.byte	82
	.word	0,0
.L136:
	.word	-1,.L51,.L185-.L51,.L186-.L51
	.half	1
	.byte	82
	.word	.L187-.L51,.L188-.L51
	.half	1
	.byte	95
	.word	.L186-.L51,.L189-.L51
	.half	1
	.byte	86
	.word	0,0
.L137:
	.word	0,0
.L50:
	.word	-1,.L51,0,.L133-.L51
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('wireless_uart_read_buffer')
	.sect	'.debug_loc'
.L125:
	.word	-1,.L47,0,.L177-.L47
	.half	1
	.byte	100
	.word	.L179-.L47,.L123-.L47
	.half	1
	.byte	111
	.word	.L181-.L47,.L182-.L47
	.half	1
	.byte	101
	.word	0,0
.L128:
	.word	-1,.L47,0,.L123-.L47
	.half	2
	.byte	145,120
	.word	0,0
.L126:
	.word	-1,.L47,0,.L178-.L47
	.half	1
	.byte	84
	.word	.L180-.L47,.L123-.L47
	.half	1
	.byte	95
	.word	0,0
.L46:
	.word	-1,.L47,0,.L176-.L47
	.half	2
	.byte	138,0
	.word	.L176-.L47,.L123-.L47
	.half	2
	.byte	138,8
	.word	.L123-.L47,.L123-.L47
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('wireless_uart_send_buffer')
	.sect	'.debug_loc'
.L109:
	.word	-1,.L41,0,.L146-.L41
	.half	1
	.byte	100
	.word	.L148-.L41,.L107-.L41
	.half	1
	.byte	111
	.word	.L151-.L41,.L152-.L41
	.half	1
	.byte	100
	.word	.L153-.L41,.L154-.L41
	.half	1
	.byte	100
	.word	0,0
.L110:
	.word	-1,.L41,0,.L147-.L41
	.half	1
	.byte	84
	.word	.L149-.L41,.L107-.L41
	.half	1
	.byte	88
	.word	.L155-.L41,.L154-.L41
	.half	1
	.byte	85
	.word	.L156-.L41,.L107-.L41
	.half	1
	.byte	82
	.word	0,0
.L112:
	.word	-1,.L41,.L150-.L41,.L107-.L41
	.half	1
	.byte	89
	.word	0,0
.L40:
	.word	-1,.L41,0,.L107-.L41
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('wireless_uart_send_byte')
	.sect	'.debug_loc'
.L104:
	.word	-1,.L39,0,.L3-.L39
	.half	1
	.byte	84
	.word	.L142-.L39,.L102-.L39
	.half	1
	.byte	88
	.word	.L144-.L39,.L145-.L39
	.half	1
	.byte	85
	.word	0,0
.L106:
	.word	-1,.L39,.L143-.L39,.L102-.L39
	.half	1
	.byte	95
	.word	0,0
.L38:
	.word	-1,.L39,0,.L102-.L39
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('wireless_uart_send_image')
	.sect	'.debug_loc'
.L120:
	.word	-1,.L45,0,.L169-.L45
	.half	1
	.byte	100
	.word	.L171-.L45,.L119-.L45
	.half	1
	.byte	111
	.word	.L173-.L45,.L174-.L45
	.half	1
	.byte	100
	.word	0,0
.L121:
	.word	-1,.L45,0,.L170-.L45
	.half	1
	.byte	84
	.word	.L172-.L45,.L119-.L45
	.half	1
	.byte	95
	.word	.L175-.L45,.L174-.L45
	.half	1
	.byte	84
	.word	0,0
.L44:
	.word	-1,.L45,0,.L119-.L45
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('wireless_uart_send_string')
	.sect	'.debug_loc'
.L118:
	.word	-1,.L43,.L161-.L43,.L18-.L43
	.half	1
	.byte	82
	.word	.L162-.L43,.L113-.L43
	.half	1
	.byte	89
	.word	.L167-.L43,.L166-.L43
	.half	1
	.byte	85
	.word	.L168-.L43,.L113-.L43
	.half	1
	.byte	82
	.word	0,0
.L115:
	.word	-1,.L43,0,.L157-.L43
	.half	1
	.byte	100
	.word	.L158-.L43,.L113-.L43
	.half	1
	.byte	111
	.word	.L160-.L43,.L161-.L43
	.half	1
	.byte	100
	.word	.L163-.L43,.L164-.L43
	.half	1
	.byte	100
	.word	.L165-.L43,.L166-.L43
	.half	1
	.byte	100
	.word	0,0
.L117:
	.word	-1,.L43,.L159-.L43,.L113-.L43
	.half	1
	.byte	88
	.word	0,0
.L42:
	.word	-1,.L43,0,.L113-.L43
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L346:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('wireless_uart_send_byte')
	.sect	'.debug_frame'
	.word	12
	.word	.L346,.L39,.L102-.L39
	.sdecl	'.debug_frame',debug,cluster('wireless_uart_send_buffer')
	.sect	'.debug_frame'
	.word	12
	.word	.L346,.L41,.L107-.L41
	.sdecl	'.debug_frame',debug,cluster('wireless_uart_send_string')
	.sect	'.debug_frame'
	.word	12
	.word	.L346,.L43,.L113-.L43
	.sdecl	'.debug_frame',debug,cluster('wireless_uart_send_image')
	.sect	'.debug_frame'
	.word	12
	.word	.L346,.L45,.L119-.L45
	.sdecl	'.debug_frame',debug,cluster('wireless_uart_read_buffer')
	.sect	'.debug_frame'
	.word	36
	.word	.L346,.L47,.L123-.L47
	.byte	4
	.word	(.L176-.L47)/2
	.byte	19,8,22,26,3,19,138,8,4
	.word	(.L123-.L176)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('wireless_uart_callback')
	.sect	'.debug_frame'
	.word	36
	.word	.L346,.L49,.L129-.L49
	.byte	4
	.word	(.L183-.L49)/2
	.byte	19,8,22,26,3,19,138,8,4
	.word	(.L129-.L183)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('wireless_uart_init')
	.sect	'.debug_frame'
	.word	12
	.word	.L346,.L51,.L133-.L51
	; Module end
