/**
 * \file Ifx_IntegralF32.h
 * \brief Discrete integral approximation
 * \ingroup library_srvsw_sysse_math_f32_integral
 *
 *
 *
 * \version disabled
 * \copyright Copyright (c) 2013 Infineon Technologies AG. All rights reserved.
 *
 *
 *                                 IMPORTANT NOTICE
 *
 *
 * Use of this file is subject to the terms of use agreed between (i) you or 
 * the company in which ordinary course of business you are acting and (ii) 
 * Infineon Technologies AG or its licensees. If and as long as no such 
 * terms of use are agreed, use of this file is subject to following:


 * Boost Software License - Version 1.0 - August 17th, 2003

 * Permission is hereby granted, free of charge, to any person or 
 * organization obtaining a copy of the software and accompanying 
 * documentation covered by this license (the "Software") to use, reproduce,
 * display, distribute, execute, and transmit the Software, and to prepare
 * derivative works of the Software, and to permit third-parties to whom the 
 * Software is furnished to do so, all subject to the following:

 * The copyright notices in the Software and this entire statement, including
 * the above license grant, this restriction and the following disclaimer, must
 * be included in all copies of the Software, in whole or in part, and all
 * derivative works of the Software, unless such copies or derivative works are
 * solely in the form of machine-executable object code generated by a source
 * language processor.

 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR 
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE, TITLE AND NON-INFRINGEMENT. IN NO EVENT
 * SHALL THE COPYRIGHT HOLDERS OR ANYONE DISTRIBUTING THE SOFTWARE BE LIABLE 
 * FOR ANY DAMAGES OR OTHER LIABILITY, WHETHER IN CONTRACT, TORT OR OTHERWISE,
 * ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
 * DEALINGS IN THE SOFTWARE.

 *
 * \defgroup library_srvsw_sysse_math_f32_integral Discrete Integral Approximation
 * \ingroup library_srvsw_sysse_math_f32
 *
 */

#ifndef INTEGRAL_H
#define INTEGRAL_H

#include "Ifx_Cf32.h"

/** \brief Integrator object for float32 data type */
typedef struct
{
    float32 uk;
    float32 ik;
    float32 delta;
} Ifx_IntegralF32;

/** \brief Integrator object for cfloat32 data type */
typedef struct
{
    cfloat32 uk;
    cfloat32 ik;
    float32  delta;
} Ifx_ClpxFloat32_Integral;

/** \addtogroup library_srvsw_sysse_math_f32_integral
 * \{ */

/** \brief Initialize the integrator object
 * \param ci Pointer to the integrator object
 * \param gain Integrator gain
 * \param Ts Sampling period */
void Ifx_IntegralF32_init(Ifx_IntegralF32 *ci, float32 gain, float32 Ts);

/** \brief Step function of the integrator object
 * \param ci Pointer to the integrator object
 * \param ik input value
 * \return most actual integrator value */
float32 Ifx_IntegralF32_step(Ifx_IntegralF32 *ci, float32 ik);

/** \brief Reset the integrator object
 * \param ci Pointer to the integrator object */
void Ifx_IntegralF32_reset(Ifx_IntegralF32 *ci);

/** \brief Initialize the integrator object
 * \param ci Pointer to the integrator object
 * \param gain Integrator gain
 * \param Ts Sampling period */
void Ifx_ClpxFloat32_Integral_init(Ifx_ClpxFloat32_Integral *ci, float32 gain, float32 Ts);

/** \brief Step function of the integrator object
 * \param ci Pointer to the integrator object
 * \param ik input value
 * \return most actual integrator value */
cfloat32 Ifx_ClpxFloat32_Integral_step(Ifx_ClpxFloat32_Integral *ci, cfloat32 ik);

/** \brief Reset the integrator object
 * \param ci Pointer to the integrator object */
void Ifx_ClpxFloat32_Integral_reset(Ifx_ClpxFloat32_Integral *ci);

/**\}*/

#endif /* INTEGRAL_H */
