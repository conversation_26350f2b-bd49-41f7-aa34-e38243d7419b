	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc26168a --dep-file=seekfree_assistant_interface.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/zf_components/seekfree_assistant_interface.src ../libraries/zf_components/seekfree_assistant_interface.c"
	.compiler_name		"ctc"
	;source	'../libraries/zf_components/seekfree_assistant_interface.c'

	
$TC16X
	
	.sdecl	'.text.seekfree_assistant_interface.seekfree_assistant_transfer',code,cluster('seekfree_assistant_transfer')
	.sect	'.text.seekfree_assistant_interface.seekfree_assistant_transfer'
	.align	2
	
	.global	seekfree_assistant_transfer
; Function seekfree_assistant_transfer
.L20:
seekfree_assistant_transfer:	.type	func
	mov	d2,d4
.L56:
	j	.L2
.L2:
	ret
.L48:
	
__seekfree_assistant_transfer_function_end:
	.size	seekfree_assistant_transfer,__seekfree_assistant_transfer_function_end-seekfree_assistant_transfer
.L38:
	; End of function
	
	.sdecl	'.text.seekfree_assistant_interface.seekfree_assistant_receive',code,cluster('seekfree_assistant_receive')
	.sect	'.text.seekfree_assistant_interface.seekfree_assistant_receive'
	.align	2
	
	.global	seekfree_assistant_receive
; Function seekfree_assistant_receive
.L22:
seekfree_assistant_receive:	.type	func
	mov	d2,#0
.L115:
	j	.L3
.L3:
	ret
.L52:
	
__seekfree_assistant_receive_function_end:
	.size	seekfree_assistant_receive,__seekfree_assistant_receive_function_end-seekfree_assistant_receive
.L43:
	; End of function
	
	.sdecl	'.text.seekfree_assistant_interface.seekfree_assistant_interface_init',code,cluster('seekfree_assistant_interface_init')
	.sect	'.text.seekfree_assistant_interface.seekfree_assistant_interface_init'
	.align	2
	
	.global	seekfree_assistant_interface_init
; Function seekfree_assistant_interface_init
.L24:
seekfree_assistant_interface_init:	.type	func
	mov	d15,#0
	jeq	d15,d4,.L4
.L65:
	mov	d15,#1
	jeq	d15,d4,.L5
.L66:
	mov	d15,#2
	jeq	d15,d4,.L6
.L67:
	mov	d15,#3
	jeq	d15,d4,.L7
.L68:
	mov	d15,#4
	jeq	d15,d4,.L8
.L69:
	mov	d15,#5
	jeq	d15,d4,.L9
.L70:
	mov	d15,#6
	jeq	d15,d4,.L10
	j	.L11
.L4:
	movh.a	a15,#@his(seekfree_assistant_transfer_callback)
	lea	a15,[a15]@los(seekfree_assistant_transfer_callback)
.L71:
	movh.a	a2,#@his(debug_send_buffer)
	lea	a2,[a2]@los(debug_send_buffer)
.L72:
	st.a	[a15],a2
.L73:
	movh.a	a15,#@his(seekfree_assistant_receive_callback)
	lea	a15,[a15]@los(seekfree_assistant_receive_callback)
.L74:
	movh.a	a2,#@his(debug_read_ring_buffer)
	lea	a2,[a2]@los(debug_read_ring_buffer)
.L75:
	st.a	[a15],a2
.L76:
	j	.L12
.L5:
	movh.a	a15,#@his(seekfree_assistant_transfer_callback)
	lea	a15,[a15]@los(seekfree_assistant_transfer_callback)
.L77:
	movh.a	a2,#@his(wireless_uart_send_buffer)
	lea	a2,[a2]@los(wireless_uart_send_buffer)
.L78:
	st.a	[a15],a2
.L79:
	movh.a	a15,#@his(seekfree_assistant_receive_callback)
	lea	a15,[a15]@los(seekfree_assistant_receive_callback)
.L80:
	movh.a	a2,#@his(wireless_uart_read_buffer)
	lea	a2,[a2]@los(wireless_uart_read_buffer)
.L81:
	st.a	[a15],a2
.L82:
	j	.L13
.L6:
	movh.a	a15,#@his(seekfree_assistant_transfer_callback)
	lea	a15,[a15]@los(seekfree_assistant_transfer_callback)
.L83:
	movh.a	a2,#@his(ble6a20_send_buffer)
	lea	a2,[a2]@los(ble6a20_send_buffer)
.L84:
	st.a	[a15],a2
.L85:
	movh.a	a15,#@his(seekfree_assistant_receive_callback)
	lea	a15,[a15]@los(seekfree_assistant_receive_callback)
.L86:
	movh.a	a2,#@his(ble6a20_read_buffer)
	lea	a2,[a2]@los(ble6a20_read_buffer)
.L87:
	st.a	[a15],a2
.L88:
	j	.L14
.L7:
	movh.a	a15,#@his(seekfree_assistant_transfer_callback)
	lea	a15,[a15]@los(seekfree_assistant_transfer_callback)
.L89:
	movh.a	a2,#@his(bluetooth_ch9141_send_buffer)
	lea	a2,[a2]@los(bluetooth_ch9141_send_buffer)
.L90:
	st.a	[a15],a2
.L91:
	movh.a	a15,#@his(seekfree_assistant_receive_callback)
	lea	a15,[a15]@los(seekfree_assistant_receive_callback)
.L92:
	movh.a	a2,#@his(bluetooth_ch9141_read_buffer)
	lea	a2,[a2]@los(bluetooth_ch9141_read_buffer)
.L93:
	st.a	[a15],a2
.L94:
	j	.L15
.L8:
	movh.a	a15,#@his(seekfree_assistant_transfer_callback)
	lea	a15,[a15]@los(seekfree_assistant_transfer_callback)
.L95:
	movh.a	a2,#@his(wifi_uart_send_buffer)
	lea	a2,[a2]@los(wifi_uart_send_buffer)
.L96:
	st.a	[a15],a2
.L97:
	movh.a	a15,#@his(seekfree_assistant_receive_callback)
	lea	a15,[a15]@los(seekfree_assistant_receive_callback)
.L98:
	movh.a	a2,#@his(wifi_uart_read_buffer)
	lea	a2,[a2]@los(wifi_uart_read_buffer)
.L99:
	st.a	[a15],a2
.L100:
	j	.L16
.L9:
	movh.a	a15,#@his(seekfree_assistant_transfer_callback)
	lea	a15,[a15]@los(seekfree_assistant_transfer_callback)
.L101:
	movh.a	a2,#@his(wifi_spi_send_buffer)
	lea	a2,[a2]@los(wifi_spi_send_buffer)
.L102:
	st.a	[a15],a2
.L103:
	movh.a	a15,#@his(seekfree_assistant_receive_callback)
	lea	a15,[a15]@los(seekfree_assistant_receive_callback)
.L104:
	movh.a	a2,#@his(wifi_spi_read_buffer)
	lea	a2,[a2]@los(wifi_spi_read_buffer)
.L105:
	st.a	[a15],a2
.L106:
	j	.L17
.L10:
	j	.L18
.L11:
.L18:
.L17:
.L16:
.L15:
.L14:
.L13:
.L12:
	ret
.L44:
	
__seekfree_assistant_interface_init_function_end:
	.size	seekfree_assistant_interface_init,__seekfree_assistant_interface_init_function_end-seekfree_assistant_interface_init
.L33:
	; End of function
	
	.calls	'__INDIRECT__','debug_read_ring_buffer'
	.calls	'__INDIRECT__','debug_send_buffer'
	.calls	'__INDIRECT__','wireless_uart_send_buffer'
	.calls	'__INDIRECT__','wireless_uart_read_buffer'
	.calls	'__INDIRECT__','ble6a20_send_buffer'
	.calls	'__INDIRECT__','ble6a20_read_buffer'
	.calls	'__INDIRECT__','bluetooth_ch9141_send_buffer'
	.calls	'__INDIRECT__','bluetooth_ch9141_read_buffer'
	.calls	'__INDIRECT__','wifi_uart_send_buffer'
	.calls	'__INDIRECT__','wifi_uart_read_buffer'
	.calls	'__INDIRECT__','wifi_spi_send_buffer'
	.calls	'__INDIRECT__','wifi_spi_read_buffer'
	.calls	'seekfree_assistant_transfer','',0
	.calls	'seekfree_assistant_receive','',0
	.extern	debug_read_ring_buffer
	.extern	debug_send_buffer
	.extern	wireless_uart_send_buffer
	.extern	wireless_uart_read_buffer
	.extern	ble6a20_send_buffer
	.extern	ble6a20_read_buffer
	.extern	bluetooth_ch9141_send_buffer
	.extern	bluetooth_ch9141_read_buffer
	.extern	wifi_uart_send_buffer
	.extern	wifi_uart_read_buffer
	.extern	wifi_spi_send_buffer
	.extern	wifi_spi_read_buffer
	.weak	seekfree_assistant_interface_init
	.extern	seekfree_assistant_transfer_callback
	.extern	seekfree_assistant_receive_callback
	.weak	seekfree_assistant_transfer
	.weak	seekfree_assistant_receive
	.extern	__INDIRECT__
	.calls	'seekfree_assistant_interface_init','',0
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L26:
	.word	101933
	.half	3
	.word	.L27
	.byte	4
.L25:
	.byte	1
	.byte	'../libraries/zf_components/seekfree_assistant_interface.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L28
	.byte	2,1,1,3
	.word	220
	.byte	4
	.byte	'Ifx__jump_and_link',0,3,1,61,17,1,1,5
	.byte	'fun',0,1,61,43
	.word	223
	.byte	6,0,7
	.byte	'__fract',0,4,128,1,7
	.byte	'float',0,4,4,8
	.byte	'Ifx__float_to_fract',0,3,1,152,2,18
	.word	268
	.byte	1,1,5
	.byte	'a',0,1,152,2,44
	.word	280
	.byte	6,0,4
	.byte	'Ifx__stopPerfCounters',0,3,1,172,2,17,1,1,6,0,7
	.byte	'unsigned long long int',0,8,7,9
	.byte	'void',0,3
	.word	392
	.byte	8
	.byte	'__ld64',0,3,2,135,1,19
	.word	366
	.byte	1,1,5
	.byte	'addr',0,2,135,1,32
	.word	398
	.byte	6,0,4
	.byte	'__st64',0,3,2,143,1,17,1,1,5
	.byte	'addr',0,2,143,1,30
	.word	398
	.byte	5
	.byte	'value',0,2,143,1,43
	.word	366
	.byte	6,0,7
	.byte	'unsigned int',0,4,7,7
	.byte	'int',0,4,5,7
	.byte	'unsigned int',0,4,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON0_Bits',0,4,241,8,16,4,11
	.byte	'ENDINIT',0,4
	.word	507
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	507
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	507
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	507
	.byte	16,0,2,35,0,0,12,4,247,14,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	523
	.byte	4,2,35,0,0,7
	.byte	'unsigned char',0,1,8,7
	.byte	'unsigned short int',0,2,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON1_Bits',0,4,250,8,16,4,11
	.byte	'reserved_0',0,1
	.word	659
	.byte	2,6,2,35,0,11
	.byte	'IR0',0,1
	.word	659
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	659
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	659
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	659
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	659
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	659
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	659
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	659
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	676
	.byte	16,0,2,35,2,0,12,4,255,14,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	698
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_WDTCPU_SR_Bits',0,4,137,9,16,4,11
	.byte	'AE',0,1
	.word	659
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	659
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	659
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	659
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	659
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	659
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	659
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	659
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	659
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	659
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	676
	.byte	16,0,2,35,2,0,12,4,135,15,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	942
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_WDTCPU',0,4,175,15,25,12,13
	.byte	'CON0',0
	.word	619
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	902
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	1133
	.byte	4,2,35,8,0,14
	.word	1173
	.byte	3
	.word	1236
	.byte	4
	.byte	'IfxScuWdt_clearCpuEndinitInline',0,3,3,181,3,17,1,1,5
	.byte	'watchdog',0,3,181,3,65
	.word	1241
	.byte	5
	.byte	'password',0,3,181,3,82
	.word	676
	.byte	6,0,4
	.byte	'IfxScuWdt_setCpuEndinitInline',0,3,3,140,4,17,1,1,5
	.byte	'watchdog',0,3,140,4,63
	.word	1241
	.byte	5
	.byte	'password',0,3,140,4,80
	.word	676
	.byte	6,0,8
	.byte	'IfxScuWdt_getCpuWatchdogPasswordInline',0,3,3,227,3,19
	.word	676
	.byte	1,1,5
	.byte	'watchdog',0,3,227,3,74
	.word	1241
	.byte	6,0,15,6,156,1,9,1,16
	.byte	'IfxCpu_ResourceCpu_0',0,0,16
	.byte	'IfxCpu_ResourceCpu_1',0,1,16
	.byte	'IfxCpu_ResourceCpu_none',0,2,0,8
	.byte	'IfxCpu_getCoreIndex',0,3,5,141,6,31
	.word	1471
	.byte	1,1,6,0,8
	.byte	'IfxCpu_areInterruptsEnabled',0,3,5,139,5,20
	.word	659
	.byte	1,1,6,0,8
	.byte	'IfxCpu_disableInterrupts',0,3,5,147,5,20
	.word	659
	.byte	1,1,17,6,0,0,4
	.byte	'IfxCpu_enableInterrupts',0,3,5,157,5,17,1,1,6,0,4
	.byte	'IfxCpu_forceDisableInterrupts',0,3,5,225,5,17,1,1,6,0,4
	.byte	'IfxCpu_restoreInterrupts',0,3,5,168,7,17,1,1,5
	.byte	'enabled',0,5,168,7,50
	.word	659
	.byte	6,0
.L47:
	.byte	7
	.byte	'unsigned long int',0,4,7,8
	.byte	'IfxCpu_getPerformanceCounter',0,3,5,161,6,19
	.word	1793
	.byte	1,1,5
	.byte	'address',0,5,161,6,55
	.word	676
	.byte	6,0,8
	.byte	'IfxCpu_getPerformanceCounterStickyOverflow',0,3,5,190,6,20
	.word	659
	.byte	1,1,5
	.byte	'address',0,5,190,6,70
	.word	676
	.byte	6,0,4
	.byte	'IfxCpu_updatePerformanceCounter',0,3,5,172,8,17,1,1,5
	.byte	'address',0,5,172,8,56
	.word	1793
	.byte	5
	.byte	'count',0,5,172,8,72
	.word	1793
	.byte	17,6,0,0,10
	.byte	'_Ifx_P_OUT_Bits',0,8,143,3,16,4,11
	.byte	'P0',0,1
	.word	659
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	659
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	659
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	659
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	659
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	659
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	659
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	659
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	659
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	659
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	659
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	659
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	659
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	659
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	659
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	659
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	676
	.byte	16,0,2,35,2,0,12,8,181,5,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2024
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMR_Bits',0,8,169,2,16,4,11
	.byte	'PS0',0,1
	.word	659
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	659
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	659
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	659
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	659
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	659
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	659
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	659
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	659
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	659
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	659
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	659
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	659
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	659
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	659
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	659
	.byte	1,0,2,35,1,11
	.byte	'PCL0',0,1
	.word	659
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	659
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	659
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	659
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	659
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	659
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	659
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	659
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	659
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	659
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	659
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	659
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	659
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	659
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	659
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	659
	.byte	1,0,2,35,3,0,12,8,133,5,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2340
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ID_Bits',0,8,110,16,4,11
	.byte	'MODREV',0,1
	.word	659
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	659
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	676
	.byte	16,0,2,35,2,0,12,8,148,4,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2911
	.byte	4,2,35,0,0,18,4
	.word	659
	.byte	19,3,0,10
	.byte	'_Ifx_P_IOCR0_Bits',0,8,140,1,16,4,11
	.byte	'reserved_0',0,1
	.word	659
	.byte	3,5,2,35,0,11
	.byte	'PC0',0,1
	.word	659
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	659
	.byte	3,5,2,35,1,11
	.byte	'PC1',0,1
	.word	659
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	659
	.byte	3,5,2,35,2,11
	.byte	'PC2',0,1
	.word	659
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	659
	.byte	3,5,2,35,3,11
	.byte	'PC3',0,1
	.word	659
	.byte	5,0,2,35,3,0,12,8,164,4,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3039
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR4_Bits',0,8,166,1,16,4,11
	.byte	'reserved_0',0,1
	.word	659
	.byte	3,5,2,35,0,11
	.byte	'PC4',0,1
	.word	659
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	659
	.byte	3,5,2,35,1,11
	.byte	'PC5',0,1
	.word	659
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	659
	.byte	3,5,2,35,2,11
	.byte	'PC6',0,1
	.word	659
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	659
	.byte	3,5,2,35,3,11
	.byte	'PC7',0,1
	.word	659
	.byte	5,0,2,35,3,0,12,8,180,4,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3254
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR8_Bits',0,8,179,1,16,4,11
	.byte	'reserved_0',0,1
	.word	659
	.byte	3,5,2,35,0,11
	.byte	'PC8',0,1
	.word	659
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	659
	.byte	3,5,2,35,1,11
	.byte	'PC9',0,1
	.word	659
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	659
	.byte	3,5,2,35,2,11
	.byte	'PC10',0,1
	.word	659
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	659
	.byte	3,5,2,35,3,11
	.byte	'PC11',0,1
	.word	659
	.byte	5,0,2,35,3,0,12,8,188,4,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3469
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR12_Bits',0,8,153,1,16,4,11
	.byte	'reserved_0',0,1
	.word	659
	.byte	3,5,2,35,0,11
	.byte	'PC12',0,1
	.word	659
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	659
	.byte	3,5,2,35,1,11
	.byte	'PC13',0,1
	.word	659
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	659
	.byte	3,5,2,35,2,11
	.byte	'PC14',0,1
	.word	659
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	659
	.byte	3,5,2,35,3,11
	.byte	'PC15',0,1
	.word	659
	.byte	5,0,2,35,3,0,12,8,172,4,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3686
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IN_Bits',0,8,118,16,4,11
	.byte	'P0',0,1
	.word	659
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	659
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	659
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	659
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	659
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	659
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	659
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	659
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	659
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	659
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	659
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	659
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	659
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	659
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	659
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	659
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	676
	.byte	16,0,2,35,2,0,12,8,156,4,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3906
	.byte	4,2,35,0,0,18,24
	.word	659
	.byte	19,23,0,10
	.byte	'_Ifx_P_PDR0_Bits',0,8,205,3,16,4,11
	.byte	'PD0',0,1
	.word	659
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	659
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	659
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	659
	.byte	1,0,2,35,0,11
	.byte	'PD2',0,1
	.word	659
	.byte	3,5,2,35,1,11
	.byte	'PL2',0,1
	.word	659
	.byte	1,4,2,35,1,11
	.byte	'PD3',0,1
	.word	659
	.byte	3,1,2,35,1,11
	.byte	'PL3',0,1
	.word	659
	.byte	1,0,2,35,1,11
	.byte	'PD4',0,1
	.word	659
	.byte	3,5,2,35,2,11
	.byte	'PL4',0,1
	.word	659
	.byte	1,4,2,35,2,11
	.byte	'PD5',0,1
	.word	659
	.byte	3,1,2,35,2,11
	.byte	'PL5',0,1
	.word	659
	.byte	1,0,2,35,2,11
	.byte	'PD6',0,1
	.word	659
	.byte	3,5,2,35,3,11
	.byte	'PL6',0,1
	.word	659
	.byte	1,4,2,35,3,11
	.byte	'PD7',0,1
	.word	659
	.byte	3,1,2,35,3,11
	.byte	'PL7',0,1
	.word	659
	.byte	1,0,2,35,3,0,12,8,205,5,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4229
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PDR1_Bits',0,8,226,3,16,4,11
	.byte	'PD8',0,1
	.word	659
	.byte	3,5,2,35,0,11
	.byte	'PL8',0,1
	.word	659
	.byte	1,4,2,35,0,11
	.byte	'PD9',0,1
	.word	659
	.byte	3,1,2,35,0,11
	.byte	'PL9',0,1
	.word	659
	.byte	1,0,2,35,0,11
	.byte	'PD10',0,1
	.word	659
	.byte	3,5,2,35,1,11
	.byte	'PL10',0,1
	.word	659
	.byte	1,4,2,35,1,11
	.byte	'PD11',0,1
	.word	659
	.byte	3,1,2,35,1,11
	.byte	'PL11',0,1
	.word	659
	.byte	1,0,2,35,1,11
	.byte	'PD12',0,1
	.word	659
	.byte	3,5,2,35,2,11
	.byte	'PL12',0,1
	.word	659
	.byte	1,4,2,35,2,11
	.byte	'PD13',0,1
	.word	659
	.byte	3,1,2,35,2,11
	.byte	'PL13',0,1
	.word	659
	.byte	1,0,2,35,2,11
	.byte	'PD14',0,1
	.word	659
	.byte	3,5,2,35,3,11
	.byte	'PL14',0,1
	.word	659
	.byte	1,4,2,35,3,11
	.byte	'PD15',0,1
	.word	659
	.byte	3,1,2,35,3,11
	.byte	'PL15',0,1
	.word	659
	.byte	1,0,2,35,3,0,12,8,213,5,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4533
	.byte	4,2,35,0,0,18,8
	.word	659
	.byte	19,7,0,10
	.byte	'_Ifx_P_ESR_Bits',0,8,88,16,4,11
	.byte	'EN0',0,1
	.word	659
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	659
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	659
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	659
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	659
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	659
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	659
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	659
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	659
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	659
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	659
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	659
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	659
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	659
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	659
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	659
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	676
	.byte	16,0,2,35,2,0,12,8,140,4,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4858
	.byte	4,2,35,0,0,18,12
	.word	659
	.byte	19,11,0,10
	.byte	'_Ifx_P_PDISC_Bits',0,8,183,3,16,4,11
	.byte	'PDIS0',0,1
	.word	659
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	659
	.byte	1,6,2,35,0,11
	.byte	'PDIS2',0,1
	.word	659
	.byte	1,5,2,35,0,11
	.byte	'PDIS3',0,1
	.word	659
	.byte	1,4,2,35,0,11
	.byte	'PDIS4',0,1
	.word	659
	.byte	1,3,2,35,0,11
	.byte	'PDIS5',0,1
	.word	659
	.byte	1,2,2,35,0,11
	.byte	'PDIS6',0,1
	.word	659
	.byte	1,1,2,35,0,11
	.byte	'PDIS7',0,1
	.word	659
	.byte	1,0,2,35,0,11
	.byte	'PDIS8',0,1
	.word	659
	.byte	1,7,2,35,1,11
	.byte	'PDIS9',0,1
	.word	659
	.byte	1,6,2,35,1,11
	.byte	'PDIS10',0,1
	.word	659
	.byte	1,5,2,35,1,11
	.byte	'PDIS11',0,1
	.word	659
	.byte	1,4,2,35,1,11
	.byte	'PDIS12',0,1
	.word	659
	.byte	1,3,2,35,1,11
	.byte	'PDIS13',0,1
	.word	659
	.byte	1,2,2,35,1,11
	.byte	'PDIS14',0,1
	.word	659
	.byte	1,1,2,35,1,11
	.byte	'PDIS15',0,1
	.word	659
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	676
	.byte	16,0,2,35,2,0,12,8,197,5,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5198
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PCSR_Bits',0,8,165,3,16,4,11
	.byte	'SEL0',0,1
	.word	659
	.byte	1,7,2,35,0,11
	.byte	'SEL1',0,1
	.word	659
	.byte	1,6,2,35,0,11
	.byte	'SEL2',0,1
	.word	659
	.byte	1,5,2,35,0,11
	.byte	'SEL3',0,1
	.word	659
	.byte	1,4,2,35,0,11
	.byte	'SEL4',0,1
	.word	659
	.byte	1,3,2,35,0,11
	.byte	'SEL5',0,1
	.word	659
	.byte	1,2,2,35,0,11
	.byte	'SEL6',0,1
	.word	659
	.byte	1,1,2,35,0,11
	.byte	'SEL7',0,1
	.word	659
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	659
	.byte	2,6,2,35,1,11
	.byte	'SEL10',0,1
	.word	659
	.byte	1,5,2,35,1,11
	.byte	'SEL11',0,1
	.word	659
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	484
	.byte	19,1,2,35,0,11
	.byte	'LCK',0,1
	.word	659
	.byte	1,0,2,35,3,0,12,8,189,5,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5564
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR0_Bits',0,8,206,2,16,4,11
	.byte	'PS0',0,1
	.word	659
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	659
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	659
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	659
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	484
	.byte	28,0,2,35,0,0,12,8,149,5,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5850
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR4_Bits',0,8,227,2,16,4,11
	.byte	'reserved_0',0,1
	.word	659
	.byte	4,4,2,35,0,11
	.byte	'PS4',0,1
	.word	659
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	659
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	659
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	659
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	484
	.byte	24,0,2,35,0,0,12,8,165,5,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5997
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR8_Bits',0,8,238,2,16,4,11
	.byte	'reserved_0',0,1
	.word	659
	.byte	8,0,2,35,0,11
	.byte	'PS8',0,1
	.word	659
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	659
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	659
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	659
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	484
	.byte	20,0,2,35,0,0,12,8,173,5,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6166
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR12_Bits',0,8,216,2,16,4,11
	.byte	'reserved_0',0,2
	.word	676
	.byte	12,4,2,35,0,11
	.byte	'PS12',0,1
	.word	659
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	659
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	659
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	659
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	676
	.byte	16,0,2,35,2,0,12,8,157,5,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6338
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR0_Bits',0,8,232,1,16,4,11
	.byte	'reserved_0',0,2
	.word	676
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	659
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	659
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	659
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	659
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	676
	.byte	12,0,2,35,2,0,12,8,229,4,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6513
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR4_Bits',0,8,253,1,16,4,11
	.byte	'reserved_0',0,4
	.word	484
	.byte	20,12,2,35,0,11
	.byte	'PCL4',0,1
	.word	659
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	659
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	659
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	659
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	659
	.byte	8,0,2,35,3,0,12,8,245,4,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6687
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR8_Bits',0,8,136,2,16,4,11
	.byte	'reserved_0',0,4
	.word	484
	.byte	24,8,2,35,0,11
	.byte	'PCL8',0,1
	.word	659
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	659
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	659
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	659
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	659
	.byte	4,0,2,35,3,0,12,8,253,4,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6861
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR12_Bits',0,8,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	484
	.byte	28,4,2,35,0,11
	.byte	'PCL12',0,1
	.word	659
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	659
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	659
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	659
	.byte	1,0,2,35,3,0,12,8,237,4,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7037
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR_Bits',0,8,249,2,16,4,11
	.byte	'PS0',0,1
	.word	659
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	659
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	659
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	659
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	659
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	659
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	659
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	659
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	659
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	659
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	659
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	659
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	659
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	659
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	659
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	659
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	676
	.byte	16,0,2,35,2,0,12,8,141,5,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7193
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR_Bits',0,8,147,2,16,4,11
	.byte	'reserved_0',0,2
	.word	676
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	659
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	659
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	659
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	659
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	659
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	659
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	659
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	659
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	659
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	659
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	659
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	659
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	659
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	659
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	659
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	659
	.byte	1,0,2,35,3,0,12,8,221,4,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7526
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR0_Bits',0,8,192,1,16,4,11
	.byte	'reserved_0',0,1
	.word	659
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	659
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	484
	.byte	30,0,2,35,0,0,12,8,196,4,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7874
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_Bits',0,8,200,1,16,4,11
	.byte	'reserved_0',0,1
	.word	659
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	659
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	484
	.byte	30,0,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_P21_Bits',0,8,208,1,16,4,11
	.byte	'RDIS_CTRL',0,1
	.word	659
	.byte	1,7,2,35,0,11
	.byte	'RX_DIS',0,1
	.word	659
	.byte	1,6,2,35,0,11
	.byte	'TERM',0,1
	.word	659
	.byte	1,5,2,35,0,11
	.byte	'LRXTERM',0,1
	.word	659
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	484
	.byte	24,0,2,35,0,0,12,8,204,4,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7998
	.byte	4,2,35,0,13
	.byte	'B_P21',0
	.word	8082
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR2_Bits',0,8,218,1,16,4,11
	.byte	'reserved_0',0,1
	.word	659
	.byte	8,0,2,35,0,11
	.byte	'LVDSR',0,1
	.word	659
	.byte	1,7,2,35,1,11
	.byte	'LVDSRL',0,1
	.word	659
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	659
	.byte	2,4,2,35,1,11
	.byte	'TDIS_CTRL',0,1
	.word	659
	.byte	1,3,2,35,1,11
	.byte	'TX_DIS',0,1
	.word	659
	.byte	1,2,2,35,1,11
	.byte	'TX_PD',0,1
	.word	659
	.byte	1,1,2,35,1,11
	.byte	'TX_PWDPD',0,1
	.word	659
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	676
	.byte	16,0,2,35,2,0,12,8,213,4,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8262
	.byte	4,2,35,0,0,18,76
	.word	659
	.byte	19,75,0,10
	.byte	'_Ifx_P_ACCEN1_Bits',0,8,82,16,4,11
	.byte	'reserved_0',0,4
	.word	484
	.byte	32,0,2,35,0,0,12,8,132,4,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8515
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ACCEN0_Bits',0,8,45,16,4,11
	.byte	'EN0',0,1
	.word	659
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	659
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	659
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	659
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	659
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	659
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	659
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	659
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	659
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	659
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	659
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	659
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	659
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	659
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	659
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	659
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	659
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	659
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	659
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	659
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	659
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	659
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	659
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	659
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	659
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	659
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	659
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	659
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	659
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	659
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	659
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	659
	.byte	1,0,2,35,3,0,12,8,252,3,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8602
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P',0,8,229,5,25,128,2,13
	.byte	'OUT',0
	.word	2300
	.byte	4,2,35,0,13
	.byte	'OMR',0
	.word	2871
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	2990
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	3030
	.byte	4,2,35,12,13
	.byte	'IOCR0',0
	.word	3214
	.byte	4,2,35,16,13
	.byte	'IOCR4',0
	.word	3429
	.byte	4,2,35,20,13
	.byte	'IOCR8',0
	.word	3646
	.byte	4,2,35,24,13
	.byte	'IOCR12',0
	.word	3866
	.byte	4,2,35,28,13
	.byte	'reserved_20',0
	.word	3030
	.byte	4,2,35,32,13
	.byte	'IN',0
	.word	4180
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	4220
	.byte	24,2,35,40,13
	.byte	'PDR0',0
	.word	4493
	.byte	4,2,35,64,13
	.byte	'PDR1',0
	.word	4809
	.byte	4,2,35,68,13
	.byte	'reserved_48',0
	.word	4849
	.byte	8,2,35,72,13
	.byte	'ESR',0
	.word	5149
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	5189
	.byte	12,2,35,84,13
	.byte	'PDISC',0
	.word	5524
	.byte	4,2,35,96,13
	.byte	'PCSR',0
	.word	5810
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	4849
	.byte	8,2,35,104,13
	.byte	'OMSR0',0
	.word	5957
	.byte	4,2,35,112,13
	.byte	'OMSR4',0
	.word	6126
	.byte	4,2,35,116,13
	.byte	'OMSR8',0
	.word	6298
	.byte	4,2,35,120,13
	.byte	'OMSR12',0
	.word	6473
	.byte	4,2,35,124,13
	.byte	'OMCR0',0
	.word	6647
	.byte	4,3,35,128,1,13
	.byte	'OMCR4',0
	.word	6821
	.byte	4,3,35,132,1,13
	.byte	'OMCR8',0
	.word	6997
	.byte	4,3,35,136,1,13
	.byte	'OMCR12',0
	.word	7153
	.byte	4,3,35,140,1,13
	.byte	'OMSR',0
	.word	7486
	.byte	4,3,35,144,1,13
	.byte	'OMCR',0
	.word	7834
	.byte	4,3,35,148,1,13
	.byte	'reserved_98',0
	.word	4849
	.byte	8,3,35,152,1,13
	.byte	'LPCR0',0
	.word	7958
	.byte	4,3,35,160,1,13
	.byte	'LPCR1',0
	.word	8207
	.byte	4,3,35,164,1,13
	.byte	'LPCR2',0
	.word	8466
	.byte	4,3,35,168,1,13
	.byte	'reserved_A4',0
	.word	8506
	.byte	76,3,35,172,1,13
	.byte	'ACCEN1',0
	.word	8562
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	9129
	.byte	4,3,35,252,1,0,14
	.word	9169
	.byte	3
	.word	9772
	.byte	15,7,83,9,1,16
	.byte	'IfxPort_InputMode_undefined',0,127,16
	.byte	'IfxPort_InputMode_noPullDevice',0,0,16
	.byte	'IfxPort_InputMode_pullDown',0,8,16
	.byte	'IfxPort_InputMode_pullUp',0,16,0,4
	.byte	'IfxPort_setPinModeInput',0,3,7,196,4,17,1,1,5
	.byte	'port',0,7,196,4,48
	.word	9777
	.byte	5
	.byte	'pinIndex',0,7,196,4,60
	.word	659
	.byte	5
	.byte	'mode',0,7,196,4,88
	.word	9782
	.byte	6,0,15,7,134,1,9,1,16
	.byte	'IfxPort_OutputMode_pushPull',0,128,1,16
	.byte	'IfxPort_OutputMode_openDrain',0,192,1,0,15,7,120,9,1,16
	.byte	'IfxPort_OutputIdx_general',0,128,1,16
	.byte	'IfxPort_OutputIdx_alt1',0,136,1,16
	.byte	'IfxPort_OutputIdx_alt2',0,144,1,16
	.byte	'IfxPort_OutputIdx_alt3',0,152,1,16
	.byte	'IfxPort_OutputIdx_alt4',0,160,1,16
	.byte	'IfxPort_OutputIdx_alt5',0,168,1,16
	.byte	'IfxPort_OutputIdx_alt6',0,176,1,16
	.byte	'IfxPort_OutputIdx_alt7',0,184,1,0,4
	.byte	'IfxPort_setPinModeOutput',0,3,7,202,4,17,1,1,5
	.byte	'port',0,7,202,4,49
	.word	9777
	.byte	5
	.byte	'pinIndex',0,7,202,4,61
	.word	659
	.byte	5
	.byte	'mode',0,7,202,4,90
	.word	9987
	.byte	5
	.byte	'index',0,7,202,4,114
	.word	10057
	.byte	6,0,15,7,172,1,9,4,16
	.byte	'IfxPort_State_notChanged',0,0,16
	.byte	'IfxPort_State_high',0,1,16
	.byte	'IfxPort_State_low',0,128,128,4,16
	.byte	'IfxPort_State_toggled',0,129,128,4,0,4
	.byte	'IfxPort_setPinState',0,3,7,208,4,17,1,1,5
	.byte	'port',0,7,208,4,44
	.word	9777
	.byte	5
	.byte	'pinIndex',0,7,208,4,56
	.word	659
	.byte	5
	.byte	'action',0,7,208,4,80
	.word	10370
	.byte	6,0,8
	.byte	'IfxScuCcu_getStmFrequency',0,3,9,226,8,20
	.word	280
	.byte	1,1,6,0,10
	.byte	'_Ifx_SRC_SRCR_Bits',0,11,45,16,4,11
	.byte	'SRPN',0,1
	.word	659
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	659
	.byte	2,6,2,35,1,11
	.byte	'SRE',0,1
	.word	659
	.byte	1,5,2,35,1,11
	.byte	'TOS',0,1
	.word	659
	.byte	2,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	659
	.byte	3,0,2,35,1,11
	.byte	'ECC',0,1
	.word	659
	.byte	6,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	659
	.byte	2,0,2,35,2,11
	.byte	'SRR',0,1
	.word	659
	.byte	1,7,2,35,3,11
	.byte	'CLRR',0,1
	.word	659
	.byte	1,6,2,35,3,11
	.byte	'SETR',0,1
	.word	659
	.byte	1,5,2,35,3,11
	.byte	'IOV',0,1
	.word	659
	.byte	1,4,2,35,3,11
	.byte	'IOVCLR',0,1
	.word	659
	.byte	1,3,2,35,3,11
	.byte	'SWS',0,1
	.word	659
	.byte	1,2,2,35,3,11
	.byte	'SWSCLR',0,1
	.word	659
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	659
	.byte	1,0,2,35,3,0,12,11,70,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10591
	.byte	4,2,35,0,0,14
	.word	10881
	.byte	3
	.word	10920
	.byte	4
	.byte	'IfxSrc_clearRequest',0,3,10,250,1,17,1,1,5
	.byte	'src',0,10,250,1,60
	.word	10925
	.byte	6,0,10
	.byte	'_Ifx_ASCLIN_CLC_Bits',0,13,118,16,4,11
	.byte	'DISR',0,1
	.word	659
	.byte	1,7,2,35,0,11
	.byte	'DISS',0,1
	.word	659
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	659
	.byte	1,5,2,35,0,11
	.byte	'EDIS',0,1
	.word	659
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	484
	.byte	28,0,2,35,0,0,12,13,207,3,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10973
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_IOCR_Bits',0,13,169,2,16,4,11
	.byte	'ALTI',0,1
	.word	659
	.byte	3,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	659
	.byte	1,4,2,35,0,11
	.byte	'DEPTH',0,2
	.word	676
	.byte	6,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	659
	.byte	6,0,2,35,1,11
	.byte	'CTS',0,1
	.word	659
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	676
	.byte	7,7,2,35,2,11
	.byte	'RCPOL',0,1
	.word	659
	.byte	1,6,2,35,3,11
	.byte	'CPOL',0,1
	.word	659
	.byte	1,5,2,35,3,11
	.byte	'SPOL',0,1
	.word	659
	.byte	1,4,2,35,3,11
	.byte	'LB',0,1
	.word	659
	.byte	1,3,2,35,3,11
	.byte	'CTSEN',0,1
	.word	659
	.byte	1,2,2,35,3,11
	.byte	'RXM',0,1
	.word	659
	.byte	1,1,2,35,3,11
	.byte	'TXM',0,1
	.word	659
	.byte	1,0,2,35,3,0,12,13,151,4,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11132
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_ID_Bits',0,13,161,2,16,4,11
	.byte	'MODREV',0,1
	.word	659
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	659
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	676
	.byte	16,0,2,35,2,0,12,13,143,4,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11427
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_TXFIFOCON_Bits',0,13,149,3,16,4,11
	.byte	'FLUSH',0,1
	.word	659
	.byte	1,7,2,35,0,11
	.byte	'ENO',0,1
	.word	659
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	659
	.byte	4,2,2,35,0,11
	.byte	'INW',0,1
	.word	659
	.byte	2,0,2,35,0,11
	.byte	'INTLEVEL',0,1
	.word	659
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	659
	.byte	4,0,2,35,1,11
	.byte	'FILL',0,1
	.word	659
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,2
	.word	676
	.byte	11,0,2,35,2,0,12,13,247,4,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11552
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_RXFIFOCON_Bits',0,13,129,3,16,4,11
	.byte	'FLUSH',0,1
	.word	659
	.byte	1,7,2,35,0,11
	.byte	'ENI',0,1
	.word	659
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	659
	.byte	4,2,2,35,0,11
	.byte	'OUTW',0,1
	.word	659
	.byte	2,0,2,35,0,11
	.byte	'INTLEVEL',0,1
	.word	659
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	659
	.byte	4,0,2,35,1,11
	.byte	'FILL',0,1
	.word	659
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,2
	.word	676
	.byte	10,1,2,35,2,11
	.byte	'BUF',0,1
	.word	659
	.byte	1,0,2,35,3,0,12,13,231,4,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11777
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_BITCON_Bits',0,13,88,16,4,11
	.byte	'PRESCALER',0,2
	.word	676
	.byte	12,4,2,35,0,11
	.byte	'reserved_12',0,1
	.word	659
	.byte	4,0,2,35,1,11
	.byte	'OVERSAMPLING',0,1
	.word	659
	.byte	4,4,2,35,2,11
	.byte	'reserved_20',0,1
	.word	659
	.byte	4,0,2,35,2,11
	.byte	'SAMPLEPOINT',0,1
	.word	659
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	659
	.byte	3,1,2,35,3,11
	.byte	'SM',0,1
	.word	659
	.byte	1,0,2,35,3,0,12,13,183,3,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12018
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_FRAMECON_Bits',0,13,145,2,16,4,11
	.byte	'reserved_0',0,1
	.word	659
	.byte	6,2,2,35,0,11
	.byte	'IDLE',0,2
	.word	676
	.byte	3,7,2,35,0,11
	.byte	'STOP',0,1
	.word	659
	.byte	3,4,2,35,1,11
	.byte	'LEAD',0,1
	.word	659
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	659
	.byte	1,0,2,35,1,11
	.byte	'MODE',0,1
	.word	659
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	676
	.byte	10,4,2,35,2,11
	.byte	'MSB',0,1
	.word	659
	.byte	1,3,2,35,3,11
	.byte	'CEN',0,1
	.word	659
	.byte	1,2,2,35,3,11
	.byte	'PEN',0,1
	.word	659
	.byte	1,1,2,35,3,11
	.byte	'ODD',0,1
	.word	659
	.byte	1,0,2,35,3,0,12,13,135,4,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12239
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_DATCON_Bits',0,13,136,1,16,4,11
	.byte	'DATLEN',0,1
	.word	659
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	676
	.byte	9,3,2,35,0,11
	.byte	'HO',0,1
	.word	659
	.byte	1,2,2,35,1,11
	.byte	'RM',0,1
	.word	659
	.byte	1,1,2,35,1,11
	.byte	'CSM',0,1
	.word	659
	.byte	1,0,2,35,1,11
	.byte	'RESPONSE',0,1
	.word	659
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	659
	.byte	8,0,2,35,3,0,12,13,223,3,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12504
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_BRG_Bits',0,13,109,16,4,11
	.byte	'DENOMINATOR',0,2
	.word	676
	.byte	12,4,2,35,0,11
	.byte	'reserved_12',0,1
	.word	659
	.byte	4,0,2,35,1,11
	.byte	'NUMERATOR',0,2
	.word	676
	.byte	12,4,2,35,2,11
	.byte	'reserved_28',0,1
	.word	659
	.byte	4,0,2,35,3,0,12,13,199,3,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12701
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_BRD_Bits',0,13,100,16,4,11
	.byte	'LOWERLIMIT',0,1
	.word	659
	.byte	8,0,2,35,0,11
	.byte	'UPPERLIMIT',0,1
	.word	659
	.byte	8,0,2,35,1,11
	.byte	'MEASURED',0,2
	.word	676
	.byte	12,4,2,35,2,11
	.byte	'reserved_28',0,1
	.word	659
	.byte	4,0,2,35,3,0,12,13,191,3,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12858
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_LIN_CON_Bits',0,13,216,2,16,4,11
	.byte	'reserved_0',0,4
	.word	484
	.byte	23,9,2,35,0,11
	.byte	'CSI',0,1
	.word	659
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	659
	.byte	1,7,2,35,3,11
	.byte	'CSEN',0,1
	.word	659
	.byte	1,6,2,35,3,11
	.byte	'MS',0,1
	.word	659
	.byte	1,5,2,35,3,11
	.byte	'ABD',0,1
	.word	659
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	659
	.byte	4,0,2,35,3,0,12,13,191,4,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13012
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_LIN_BTIMER_Bits',0,13,209,2,16,4,11
	.byte	'BREAK',0,1
	.word	659
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	484
	.byte	26,0,2,35,0,0,12,13,183,4,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13212
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_LIN_HTIMER_Bits',0,13,228,2,16,4,11
	.byte	'HEADER',0,1
	.word	659
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	484
	.byte	24,0,2,35,0,0,12,13,199,4,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13326
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_LIN',0,13,135,5,25,12,13
	.byte	'CON',0
	.word	13172
	.byte	4,2,35,0,13
	.byte	'BTIMER',0
	.word	13286
	.byte	4,2,35,4,13
	.byte	'HTIMER',0
	.word	13401
	.byte	4,2,35,8,0,14
	.word	13441
	.byte	10
	.byte	'_Ifx_ASCLIN_FLAGS_Bits',0,13,148,1,16,4,11
	.byte	'TH',0,1
	.word	659
	.byte	1,7,2,35,0,11
	.byte	'TR',0,1
	.word	659
	.byte	1,6,2,35,0,11
	.byte	'RH',0,1
	.word	659
	.byte	1,5,2,35,0,11
	.byte	'RR',0,1
	.word	659
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	659
	.byte	1,3,2,35,0,11
	.byte	'FED',0,1
	.word	659
	.byte	1,2,2,35,0,11
	.byte	'RED',0,1
	.word	659
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	676
	.byte	6,3,2,35,0,11
	.byte	'TWRQ',0,1
	.word	659
	.byte	1,2,2,35,1,11
	.byte	'THRQ',0,1
	.word	659
	.byte	1,1,2,35,1,11
	.byte	'TRRQ',0,1
	.word	659
	.byte	1,0,2,35,1,11
	.byte	'PE',0,1
	.word	659
	.byte	1,7,2,35,2,11
	.byte	'TC',0,1
	.word	659
	.byte	1,6,2,35,2,11
	.byte	'FE',0,1
	.word	659
	.byte	1,5,2,35,2,11
	.byte	'HT',0,1
	.word	659
	.byte	1,4,2,35,2,11
	.byte	'RT',0,1
	.word	659
	.byte	1,3,2,35,2,11
	.byte	'BD',0,1
	.word	659
	.byte	1,2,2,35,2,11
	.byte	'LP',0,1
	.word	659
	.byte	1,1,2,35,2,11
	.byte	'LA',0,1
	.word	659
	.byte	1,0,2,35,2,11
	.byte	'LC',0,1
	.word	659
	.byte	1,7,2,35,3,11
	.byte	'CE',0,1
	.word	659
	.byte	1,6,2,35,3,11
	.byte	'RFO',0,1
	.word	659
	.byte	1,5,2,35,3,11
	.byte	'RFU',0,1
	.word	659
	.byte	1,4,2,35,3,11
	.byte	'RFL',0,1
	.word	659
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	659
	.byte	1,2,2,35,3,11
	.byte	'TFO',0,1
	.word	659
	.byte	1,1,2,35,3,11
	.byte	'TFL',0,1
	.word	659
	.byte	1,0,2,35,3,0,12,13,231,3,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13514
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_FLAGSSET_Bits',0,13,241,1,16,4,11
	.byte	'THS',0,1
	.word	659
	.byte	1,7,2,35,0,11
	.byte	'TRS',0,1
	.word	659
	.byte	1,6,2,35,0,11
	.byte	'RHS',0,1
	.word	659
	.byte	1,5,2,35,0,11
	.byte	'RRS',0,1
	.word	659
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	659
	.byte	1,3,2,35,0,11
	.byte	'FEDS',0,1
	.word	659
	.byte	1,2,2,35,0,11
	.byte	'REDS',0,1
	.word	659
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	676
	.byte	6,3,2,35,0,11
	.byte	'TWRQS',0,1
	.word	659
	.byte	1,2,2,35,1,11
	.byte	'THRQS',0,1
	.word	659
	.byte	1,1,2,35,1,11
	.byte	'TRRQS',0,1
	.word	659
	.byte	1,0,2,35,1,11
	.byte	'PES',0,1
	.word	659
	.byte	1,7,2,35,2,11
	.byte	'TCS',0,1
	.word	659
	.byte	1,6,2,35,2,11
	.byte	'FES',0,1
	.word	659
	.byte	1,5,2,35,2,11
	.byte	'HTS',0,1
	.word	659
	.byte	1,4,2,35,2,11
	.byte	'RTS',0,1
	.word	659
	.byte	1,3,2,35,2,11
	.byte	'BDS',0,1
	.word	659
	.byte	1,2,2,35,2,11
	.byte	'LPS',0,1
	.word	659
	.byte	1,1,2,35,2,11
	.byte	'LAS',0,1
	.word	659
	.byte	1,0,2,35,2,11
	.byte	'LCS',0,1
	.word	659
	.byte	1,7,2,35,3,11
	.byte	'CES',0,1
	.word	659
	.byte	1,6,2,35,3,11
	.byte	'RFOS',0,1
	.word	659
	.byte	1,5,2,35,3,11
	.byte	'RFUS',0,1
	.word	659
	.byte	1,4,2,35,3,11
	.byte	'RFLS',0,1
	.word	659
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	659
	.byte	1,2,2,35,3,11
	.byte	'TFOS',0,1
	.word	659
	.byte	1,1,2,35,3,11
	.byte	'TFLS',0,1
	.word	659
	.byte	1,0,2,35,3,0,12,13,255,3,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14000
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_FLAGSCLEAR_Bits',0,13,180,1,16,4,11
	.byte	'THC',0,1
	.word	659
	.byte	1,7,2,35,0,11
	.byte	'TRC',0,1
	.word	659
	.byte	1,6,2,35,0,11
	.byte	'RHC',0,1
	.word	659
	.byte	1,5,2,35,0,11
	.byte	'RRC',0,1
	.word	659
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	659
	.byte	1,3,2,35,0,11
	.byte	'FEDC',0,1
	.word	659
	.byte	1,2,2,35,0,11
	.byte	'REDC',0,1
	.word	659
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	676
	.byte	6,3,2,35,0,11
	.byte	'TWRQC',0,1
	.word	659
	.byte	1,2,2,35,1,11
	.byte	'THRQC',0,1
	.word	659
	.byte	1,1,2,35,1,11
	.byte	'TRRQC',0,1
	.word	659
	.byte	1,0,2,35,1,11
	.byte	'PEC',0,1
	.word	659
	.byte	1,7,2,35,2,11
	.byte	'TCC',0,1
	.word	659
	.byte	1,6,2,35,2,11
	.byte	'FEC',0,1
	.word	659
	.byte	1,5,2,35,2,11
	.byte	'HTC',0,1
	.word	659
	.byte	1,4,2,35,2,11
	.byte	'RTC',0,1
	.word	659
	.byte	1,3,2,35,2,11
	.byte	'BDC',0,1
	.word	659
	.byte	1,2,2,35,2,11
	.byte	'LPC',0,1
	.word	659
	.byte	1,1,2,35,2,11
	.byte	'LAC',0,1
	.word	659
	.byte	1,0,2,35,2,11
	.byte	'LCC',0,1
	.word	659
	.byte	1,7,2,35,3,11
	.byte	'CEC',0,1
	.word	659
	.byte	1,6,2,35,3,11
	.byte	'RFOC',0,1
	.word	659
	.byte	1,5,2,35,3,11
	.byte	'RFUC',0,1
	.word	659
	.byte	1,4,2,35,3,11
	.byte	'RFLC',0,1
	.word	659
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	659
	.byte	1,2,2,35,3,11
	.byte	'TFOC',0,1
	.word	659
	.byte	1,1,2,35,3,11
	.byte	'TFLC',0,1
	.word	659
	.byte	1,0,2,35,3,0,12,13,239,3,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14513
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_FLAGSENABLE_Bits',0,13,212,1,16,4,11
	.byte	'THE',0,1
	.word	659
	.byte	1,7,2,35,0,11
	.byte	'TRE',0,1
	.word	659
	.byte	1,6,2,35,0,11
	.byte	'RHE',0,1
	.word	659
	.byte	1,5,2,35,0,11
	.byte	'RRE',0,1
	.word	659
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	659
	.byte	1,3,2,35,0,11
	.byte	'FEDE',0,1
	.word	659
	.byte	1,2,2,35,0,11
	.byte	'REDE',0,1
	.word	659
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	676
	.byte	9,0,2,35,0,11
	.byte	'PEE',0,1
	.word	659
	.byte	1,7,2,35,2,11
	.byte	'TCE',0,1
	.word	659
	.byte	1,6,2,35,2,11
	.byte	'FEE',0,1
	.word	659
	.byte	1,5,2,35,2,11
	.byte	'HTE',0,1
	.word	659
	.byte	1,4,2,35,2,11
	.byte	'RTE',0,1
	.word	659
	.byte	1,3,2,35,2,11
	.byte	'BDE',0,1
	.word	659
	.byte	1,2,2,35,2,11
	.byte	'LPE',0,1
	.word	659
	.byte	1,1,2,35,2,11
	.byte	'ABE',0,1
	.word	659
	.byte	1,0,2,35,2,11
	.byte	'LCE',0,1
	.word	659
	.byte	1,7,2,35,3,11
	.byte	'CEE',0,1
	.word	659
	.byte	1,6,2,35,3,11
	.byte	'RFOE',0,1
	.word	659
	.byte	1,5,2,35,3,11
	.byte	'RFUE',0,1
	.word	659
	.byte	1,4,2,35,3,11
	.byte	'RFLE',0,1
	.word	659
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	659
	.byte	1,2,2,35,3,11
	.byte	'TFOE',0,1
	.word	659
	.byte	1,1,2,35,3,11
	.byte	'TFLE',0,1
	.word	659
	.byte	1,0,2,35,3,0,12,13,247,3,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15028
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_TXDATA_Bits',0,13,143,3,16,4,11
	.byte	'DATA',0,4
	.word	484
	.byte	32,0,2,35,0,0,12,13,239,4,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15493
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_RXDATA_Bits',0,13,245,2,16,4,11
	.byte	'DATA',0,4
	.word	484
	.byte	32,0,2,35,0,0,12,13,215,4,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15580
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_CSR_Bits',0,13,128,1,16,4,11
	.byte	'CLKSEL',0,1
	.word	659
	.byte	5,3,2,35,0,11
	.byte	'reserved_5',0,4
	.word	484
	.byte	26,1,2,35,0,11
	.byte	'CON',0,1
	.word	659
	.byte	1,0,2,35,3,0,12,13,215,3,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15667
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_RXDATAD_Bits',0,13,251,2,16,4,11
	.byte	'DATA',0,4
	.word	484
	.byte	32,0,2,35,0,0,12,13,223,4,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15790
	.byte	4,2,35,0,0,18,148,1
	.word	659
	.byte	19,147,1,0,10
	.byte	'_Ifx_ASCLIN_OCS_Bits',0,13,235,2,16,4,11
	.byte	'reserved_0',0,4
	.word	484
	.byte	24,8,2,35,0,11
	.byte	'SUS',0,1
	.word	659
	.byte	4,4,2,35,3,11
	.byte	'SUS_P',0,1
	.word	659
	.byte	1,3,2,35,3,11
	.byte	'SUSSTA',0,1
	.word	659
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	659
	.byte	2,0,2,35,3,0,12,13,207,4,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15889
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_KRSTCLR_Bits',0,13,202,2,16,4,11
	.byte	'CLR',0,1
	.word	659
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	484
	.byte	31,0,2,35,0,0,12,13,175,4,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16052
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_KRST1_Bits',0,13,195,2,16,4,11
	.byte	'RST',0,1
	.word	659
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	484
	.byte	31,0,2,35,0,0,12,13,167,4,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16161
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_KRST0_Bits',0,13,187,2,16,4,11
	.byte	'RST',0,1
	.word	659
	.byte	1,7,2,35,0,11
	.byte	'RSTSTAT',0,1
	.word	659
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	484
	.byte	30,0,2,35,0,0,12,13,159,4,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16268
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_ACCEN1_Bits',0,13,82,16,4,11
	.byte	'reserved_0',0,4
	.word	484
	.byte	32,0,2,35,0,0,12,13,175,3,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16394
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_ACCEN0_Bits',0,13,45,16,4,11
	.byte	'EN0',0,1
	.word	659
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	659
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	659
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	659
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	659
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	659
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	659
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	659
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	659
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	659
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	659
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	659
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	659
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	659
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	659
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	659
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	659
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	659
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	659
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	659
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	659
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	659
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	659
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	659
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	659
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	659
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	659
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	659
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	659
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	659
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	659
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	659
	.byte	1,0,2,35,3,0,12,13,167,3,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16486
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN',0,13,153,5,25,128,2,13
	.byte	'CLC',0
	.word	11092
	.byte	4,2,35,0,13
	.byte	'IOCR',0
	.word	11387
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	11512
	.byte	4,2,35,8,13
	.byte	'TXFIFOCON',0
	.word	11737
	.byte	4,2,35,12,13
	.byte	'RXFIFOCON',0
	.word	11978
	.byte	4,2,35,16,13
	.byte	'BITCON',0
	.word	12199
	.byte	4,2,35,20,13
	.byte	'FRAMECON',0
	.word	12464
	.byte	4,2,35,24,13
	.byte	'DATCON',0
	.word	12661
	.byte	4,2,35,28,13
	.byte	'BRG',0
	.word	12818
	.byte	4,2,35,32,13
	.byte	'BRD',0
	.word	12972
	.byte	4,2,35,36,13
	.byte	'LIN',0
	.word	13509
	.byte	12,2,35,40,13
	.byte	'FLAGS',0
	.word	13960
	.byte	4,2,35,52,13
	.byte	'FLAGSSET',0
	.word	14473
	.byte	4,2,35,56,13
	.byte	'FLAGSCLEAR',0
	.word	14988
	.byte	4,2,35,60,13
	.byte	'FLAGSENABLE',0
	.word	15453
	.byte	4,2,35,64,13
	.byte	'TXDATA',0
	.word	15540
	.byte	4,2,35,68,13
	.byte	'RXDATA',0
	.word	15627
	.byte	4,2,35,72,13
	.byte	'CSR',0
	.word	15750
	.byte	4,2,35,76,13
	.byte	'RXDATAD',0
	.word	15838
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	15878
	.byte	148,1,2,35,84,13
	.byte	'OCS',0
	.word	16012
	.byte	4,3,35,232,1,13
	.byte	'KRSTCLR',0
	.word	16121
	.byte	4,3,35,236,1,13
	.byte	'KRST1',0
	.word	16228
	.byte	4,3,35,240,1,13
	.byte	'KRST0',0
	.word	16354
	.byte	4,3,35,244,1,13
	.byte	'ACCEN1',0
	.word	16446
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	17018
	.byte	4,3,35,252,1,0,14
	.word	17058
	.byte	3
	.word	17500
	.byte	4
	.byte	'IfxAsclin_enableCts',0,3,12,228,13,17,1,1,5
	.byte	'asclin',0,12,228,13,49
	.word	17505
	.byte	5
	.byte	'enable',0,12,228,13,65
	.word	659
	.byte	6,0,15,12,123,9,1,16
	.byte	'IfxAsclin_CtsInputSelect_0',0,0,16
	.byte	'IfxAsclin_CtsInputSelect_1',0,1,16
	.byte	'IfxAsclin_CtsInputSelect_2',0,2,16
	.byte	'IfxAsclin_CtsInputSelect_3',0,3,0,4
	.byte	'IfxAsclin_setCtsInput',0,3,12,169,17,17,1,1,5
	.byte	'asclin',0,12,169,17,51
	.word	17505
	.byte	5
	.byte	'ctsi',0,12,169,17,84
	.word	17572
	.byte	6,0,15,12,181,2,9,1,16
	.byte	'IfxAsclin_RxInputSelect_0',0,0,16
	.byte	'IfxAsclin_RxInputSelect_1',0,1,16
	.byte	'IfxAsclin_RxInputSelect_2',0,2,16
	.byte	'IfxAsclin_RxInputSelect_3',0,3,16
	.byte	'IfxAsclin_RxInputSelect_4',0,4,16
	.byte	'IfxAsclin_RxInputSelect_5',0,5,16
	.byte	'IfxAsclin_RxInputSelect_6',0,6,16
	.byte	'IfxAsclin_RxInputSelect_7',0,7,0,4
	.byte	'IfxAsclin_setRxInput',0,3,12,191,18,17,1,1,5
	.byte	'asclin',0,12,191,18,50
	.word	17505
	.byte	5
	.byte	'alti',0,12,191,18,82
	.word	17756
	.byte	6,0,7
	.byte	'short int',0,2,5,7
	.byte	'long int',0,4,5,20,14,60,9,12,13
	.byte	'count',0
	.word	18048
	.byte	2,2,35,0,13
	.byte	'readerWaitx',0
	.word	18061
	.byte	4,2,35,2,13
	.byte	'writerWaitx',0
	.word	18061
	.byte	4,2,35,6,13
	.byte	'maxcount',0
	.word	18048
	.byte	2,2,35,10,0,14
	.word	659
	.byte	14
	.word	659
	.byte	10
	.byte	'_Fifo',0,14,73,16,28,13
	.byte	'buffer',0
	.word	398
	.byte	4,2,35,0,13
	.byte	'shared',0
	.word	18073
	.byte	12,2,35,4,13
	.byte	'startIndex',0
	.word	18048
	.byte	2,2,35,16,13
	.byte	'endIndex',0
	.word	18048
	.byte	2,2,35,18,13
	.byte	'size',0
	.word	18048
	.byte	2,2,35,20,13
	.byte	'elementSize',0
	.word	18048
	.byte	2,2,35,22,13
	.byte	'eventReader',0
	.word	18154
	.byte	1,2,35,24,13
	.byte	'eventWriter',0
	.word	18159
	.byte	1,2,35,25,0,3
	.word	18164
	.byte	8
	.byte	'Ifx_Fifo_readCount',0,3,14,206,1,22
	.word	18048
	.byte	1,1,5
	.byte	'fifo',0,14,206,1,51
	.word	18323
	.byte	6,0,10
	.byte	'_Ifx_STM_CLC_Bits',0,16,100,16,4,11
	.byte	'DISR',0,1
	.word	659
	.byte	1,7,2,35,0,11
	.byte	'DISS',0,1
	.word	659
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	659
	.byte	1,5,2,35,0,11
	.byte	'EDIS',0,1
	.word	659
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	484
	.byte	28,0,2,35,0,0,12,16,149,2,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18375
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_ID_Bits',0,16,142,1,16,4,11
	.byte	'MODREV',0,1
	.word	659
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	659
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	676
	.byte	16,0,2,35,2,0,12,16,181,2,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18531
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM0_Bits',0,16,192,1,16,4,11
	.byte	'STM31_0',0,4
	.word	484
	.byte	32,0,2,35,0,0,12,16,229,2,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18653
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM1_Bits',0,16,204,1,16,4,11
	.byte	'STM35_4',0,4
	.word	484
	.byte	32,0,2,35,0,0,12,16,245,2,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18738
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM2_Bits',0,16,210,1,16,4,11
	.byte	'STM39_8',0,4
	.word	484
	.byte	32,0,2,35,0,0,12,16,253,2,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18823
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM3_Bits',0,16,216,1,16,4,11
	.byte	'STM43_12',0,4
	.word	484
	.byte	32,0,2,35,0,0,12,16,133,3,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18908
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM4_Bits',0,16,222,1,16,4,11
	.byte	'STM47_16',0,4
	.word	484
	.byte	32,0,2,35,0,0,12,16,141,3,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18994
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM5_Bits',0,16,228,1,16,4,11
	.byte	'STM51_20',0,4
	.word	484
	.byte	32,0,2,35,0,0,12,16,149,3,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19080
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM6_Bits',0,16,234,1,16,4,11
	.byte	'STM63_32',0,4
	.word	484
	.byte	32,0,2,35,0,0,12,16,157,3,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19166
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_CAP_Bits',0,16,88,16,4,11
	.byte	'STMCAP63_32',0,4
	.word	484
	.byte	32,0,2,35,0,0,12,16,133,2,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19252
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_CMP_Bits',0,16,123,16,4,11
	.byte	'CMPVAL',0,4
	.word	484
	.byte	32,0,2,35,0,0,12,16,165,2,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19339
	.byte	4,2,35,0,0,18,8
	.word	19381
	.byte	19,1,0,10
	.byte	'_Ifx_STM_CMCON_Bits',0,16,110,16,4,11
	.byte	'MSIZE0',0,1
	.word	659
	.byte	5,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	659
	.byte	3,0,2,35,0,11
	.byte	'MSTART0',0,1
	.word	659
	.byte	5,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	659
	.byte	3,0,2,35,1,11
	.byte	'MSIZE1',0,1
	.word	659
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	659
	.byte	3,0,2,35,2,11
	.byte	'MSTART1',0,1
	.word	659
	.byte	5,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	659
	.byte	3,0,2,35,3,0,12,16,157,2,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19430
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_ICR_Bits',0,16,129,1,16,4,11
	.byte	'CMP0EN',0,1
	.word	659
	.byte	1,7,2,35,0,11
	.byte	'CMP0IR',0,1
	.word	659
	.byte	1,6,2,35,0,11
	.byte	'CMP0OS',0,1
	.word	659
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	659
	.byte	1,4,2,35,0,11
	.byte	'CMP1EN',0,1
	.word	659
	.byte	1,3,2,35,0,11
	.byte	'CMP1IR',0,1
	.word	659
	.byte	1,2,2,35,0,11
	.byte	'CMP1OS',0,1
	.word	659
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,4
	.word	484
	.byte	25,0,2,35,0,0,12,16,173,2,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19661
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_ISCR_Bits',0,16,150,1,16,4,11
	.byte	'CMP0IRR',0,1
	.word	659
	.byte	1,7,2,35,0,11
	.byte	'CMP0IRS',0,1
	.word	659
	.byte	1,6,2,35,0,11
	.byte	'CMP1IRR',0,1
	.word	659
	.byte	1,5,2,35,0,11
	.byte	'CMP1IRS',0,1
	.word	659
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	484
	.byte	28,0,2,35,0,0,12,16,189,2,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19878
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM0SV_Bits',0,16,198,1,16,4,11
	.byte	'STM31_0',0,4
	.word	484
	.byte	32,0,2,35,0,0,12,16,237,2,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20042
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_CAPSV_Bits',0,16,94,16,4,11
	.byte	'STMCAP63_32',0,4
	.word	484
	.byte	32,0,2,35,0,0,12,16,141,2,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20129
	.byte	4,2,35,0,0,18,144,1
	.word	659
	.byte	19,143,1,0,10
	.byte	'_Ifx_STM_OCS_Bits',0,16,182,1,16,4,11
	.byte	'reserved_0',0,4
	.word	484
	.byte	24,8,2,35,0,11
	.byte	'SUS',0,1
	.word	659
	.byte	4,4,2,35,3,11
	.byte	'SUS_P',0,1
	.word	659
	.byte	1,3,2,35,3,11
	.byte	'SUSSTA',0,1
	.word	659
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	659
	.byte	2,0,2,35,3,0,12,16,221,2,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20229
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_KRSTCLR_Bits',0,16,175,1,16,4,11
	.byte	'CLR',0,1
	.word	659
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	484
	.byte	31,0,2,35,0,0,12,16,213,2,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20389
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_KRST1_Bits',0,16,168,1,16,4,11
	.byte	'RST',0,1
	.word	659
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	484
	.byte	31,0,2,35,0,0,12,16,205,2,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20495
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_KRST0_Bits',0,16,160,1,16,4,11
	.byte	'RST',0,1
	.word	659
	.byte	1,7,2,35,0,11
	.byte	'RSTSTAT',0,1
	.word	659
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	484
	.byte	30,0,2,35,0,0,12,16,197,2,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20599
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_ACCEN1_Bits',0,16,82,16,4,11
	.byte	'reserved_0',0,4
	.word	484
	.byte	32,0,2,35,0,0,12,16,253,1,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20722
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_ACCEN0_Bits',0,16,45,16,4,11
	.byte	'EN0',0,1
	.word	659
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	659
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	659
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	659
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	659
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	659
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	659
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	659
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	659
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	659
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	659
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	659
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	659
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	659
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	659
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	659
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	659
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	659
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	659
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	659
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	659
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	659
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	659
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	659
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	659
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	659
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	659
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	659
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	659
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	659
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	659
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	659
	.byte	1,0,2,35,3,0,12,16,245,1,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20811
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM',0,16,173,3,25,128,2,13
	.byte	'CLC',0
	.word	18491
	.byte	4,2,35,0,13
	.byte	'reserved_4',0
	.word	3030
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	18613
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	3030
	.byte	4,2,35,12,13
	.byte	'TIM0',0
	.word	18698
	.byte	4,2,35,16,13
	.byte	'TIM1',0
	.word	18783
	.byte	4,2,35,20,13
	.byte	'TIM2',0
	.word	18868
	.byte	4,2,35,24,13
	.byte	'TIM3',0
	.word	18954
	.byte	4,2,35,28,13
	.byte	'TIM4',0
	.word	19040
	.byte	4,2,35,32,13
	.byte	'TIM5',0
	.word	19126
	.byte	4,2,35,36,13
	.byte	'TIM6',0
	.word	19212
	.byte	4,2,35,40,13
	.byte	'CAP',0
	.word	19299
	.byte	4,2,35,44,13
	.byte	'CMP',0
	.word	19421
	.byte	8,2,35,48,13
	.byte	'CMCON',0
	.word	19621
	.byte	4,2,35,56,13
	.byte	'ICR',0
	.word	19838
	.byte	4,2,35,60,13
	.byte	'ISCR',0
	.word	20002
	.byte	4,2,35,64,13
	.byte	'reserved_44',0
	.word	5189
	.byte	12,2,35,68,13
	.byte	'TIM0SV',0
	.word	20089
	.byte	4,2,35,80,13
	.byte	'CAPSV',0
	.word	20178
	.byte	4,2,35,84,13
	.byte	'reserved_58',0
	.word	20218
	.byte	144,1,2,35,88,13
	.byte	'OCS',0
	.word	20349
	.byte	4,3,35,232,1,13
	.byte	'KRSTCLR',0
	.word	20455
	.byte	4,3,35,236,1,13
	.byte	'KRST1',0
	.word	20559
	.byte	4,3,35,240,1,13
	.byte	'KRST0',0
	.word	20682
	.byte	4,3,35,244,1,13
	.byte	'ACCEN1',0
	.word	20771
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	21340
	.byte	4,3,35,252,1,0,14
	.word	21380
	.byte	3
	.word	21800
	.byte	8
	.byte	'IfxStm_get',0,3,15,162,4,19
	.word	366
	.byte	1,1,5
	.byte	'stm',0,15,162,4,39
	.word	21805
	.byte	6,0,8
	.byte	'IfxStm_getFrequency',0,3,15,179,4,20
	.word	280
	.byte	1,1,5
	.byte	'stm',0,15,179,4,49
	.word	21805
	.byte	17,6,6,0,0,8
	.byte	'IfxStm_getLower',0,3,15,190,4,19
	.word	1793
	.byte	1,1,5
	.byte	'stm',0,15,190,4,44
	.word	21805
	.byte	6,0,8
	.byte	'disableInterrupts',0,3,17,108,20
	.word	659
	.byte	1,1,17,6,0,0,4
	.byte	'restoreInterrupts',0,3,17,142,1,17,1,1,5
	.byte	'enabled',0,17,142,1,43
	.word	659
	.byte	17,6,0,0,7
	.byte	'long long int',0,8,5,8
	.byte	'getDeadLine',0,3,17,164,2,25
	.word	22021
	.byte	1,1,5
	.byte	'timeout',0,17,164,2,50
	.word	22021
	.byte	17,6,0,0,8
	.byte	'isDeadLine',0,3,17,211,2,20
	.word	659
	.byte	1,1,5
	.byte	'deadLine',0,17,211,2,44
	.word	22021
	.byte	17,6,0,0,8
	.byte	'now',0,3,17,221,1,25
	.word	22021
	.byte	1,1,17,6,6,6,0,0,8
	.byte	'nowWithoutCriticalSection',0,3,17,240,1,25
	.word	22021
	.byte	1,1,17,6,0,0
.L53:
	.byte	3
	.word	659
	.byte	21
	.byte	'debug_read_ring_buffer',0,18,105,9
	.word	1793
	.byte	1,1,1,1,5
	.byte	'buff',0,18,105,48
	.word	22192
	.byte	5
	.byte	'len',0,18,105,61
	.word	1793
	.byte	0,22
	.word	659
.L49:
	.byte	3
	.word	22258
	.byte	21
	.byte	'debug_send_buffer',0,18,109,9
	.word	1793
	.byte	1,1,1,1,5
	.byte	'buff',0,18,109,54
	.word	22263
	.byte	5
	.byte	'len',0,18,109,67
	.word	1793
	.byte	0,23
	.word	228
	.byte	24
	.word	254
	.byte	6,0,23
	.word	289
	.byte	24
	.word	321
	.byte	6,0,23
	.word	334
	.byte	6,0,23
	.word	403
	.byte	24
	.word	422
	.byte	6,0,23
	.word	438
	.byte	24
	.word	453
	.byte	24
	.word	467
	.byte	6,0,23
	.word	1246
	.byte	24
	.word	1286
	.byte	24
	.word	1304
	.byte	6,0,23
	.word	1324
	.byte	24
	.word	1362
	.byte	24
	.word	1380
	.byte	6,0,23
	.word	1400
	.byte	24
	.word	1451
	.byte	6,0,23
	.word	1550
	.byte	6,0,23
	.word	1584
	.byte	6,0,23
	.word	1626
	.byte	17,25
	.word	1584
	.byte	26
	.word	1624
	.byte	0,6,0,0,23
	.word	1667
	.byte	6,0,23
	.word	1701
	.byte	6,0,23
	.word	1741
	.byte	24
	.word	1774
	.byte	6,0,23
	.word	1814
	.byte	24
	.word	1855
	.byte	6,0,23
	.word	1874
	.byte	24
	.word	1929
	.byte	6,0,23
	.word	1948
	.byte	24
	.word	1988
	.byte	24
	.word	2005
	.byte	17,6,0,0,23
	.word	9907
	.byte	24
	.word	9939
	.byte	24
	.word	9953
	.byte	24
	.word	9971
	.byte	6,0,23
	.word	10274
	.byte	24
	.word	10307
	.byte	24
	.word	10321
	.byte	24
	.word	10339
	.byte	24
	.word	10353
	.byte	6,0,23
	.word	10473
	.byte	24
	.word	10501
	.byte	24
	.word	10515
	.byte	24
	.word	10533
	.byte	6,0,23
	.word	10551
	.byte	6,0,23
	.word	10930
	.byte	24
	.word	10958
	.byte	6,0,23
	.word	17510
	.byte	24
	.word	17538
	.byte	24
	.word	17554
	.byte	6,0,23
	.word	17694
	.byte	24
	.word	17724
	.byte	24
	.word	17740
	.byte	6,0,23
	.word	17987
	.byte	24
	.word	18016
	.byte	24
	.word	18032
	.byte	6,0,23
	.word	18328
	.byte	24
	.word	18359
	.byte	6,0,23
	.word	21810
	.byte	24
	.word	21833
	.byte	6,0,23
	.word	21848
	.byte	24
	.word	21880
	.byte	17,17,25
	.word	10551
	.byte	26
	.word	10589
	.byte	0,0,6,0,0,23
	.word	21898
	.byte	24
	.word	21926
	.byte	6,0,23
	.word	21941
	.byte	17,25
	.word	1626
	.byte	27
	.word	1663
	.byte	25
	.word	1584
	.byte	26
	.word	1624
	.byte	0,26
	.word	1664
	.byte	0,0,6,0,0,23
	.word	21974
	.byte	24
	.word	22000
	.byte	17,25
	.word	1741
	.byte	24
	.word	1774
	.byte	26
	.word	1791
	.byte	0,6,0,0,23
	.word	22038
	.byte	24
	.word	22062
	.byte	17,25
	.word	22128
	.byte	27
	.word	22144
	.byte	25
	.word	21941
	.byte	27
	.word	21970
	.byte	25
	.word	1626
	.byte	27
	.word	1663
	.byte	25
	.word	1584
	.byte	26
	.word	1624
	.byte	0,26
	.word	1664
	.byte	0,0,26
	.word	21971
	.byte	0,0,26
	.word	22145
	.byte	25
	.word	21974
	.byte	24
	.word	22000
	.byte	27
	.word	22017
	.byte	25
	.word	1741
	.byte	24
	.word	1774
	.byte	26
	.word	1791
	.byte	0,26
	.word	22018
	.byte	0,0,26
	.word	22146
	.byte	25
	.word	21810
	.byte	24
	.word	21833
	.byte	26
	.word	21846
	.byte	0,26
	.word	22147
	.byte	0,0,6,0,0,23
	.word	22083
	.byte	24
	.word	22106
	.byte	17,25
	.word	22128
	.byte	27
	.word	22144
	.byte	25
	.word	21941
	.byte	27
	.word	21970
	.byte	25
	.word	1626
	.byte	27
	.word	1663
	.byte	25
	.word	1584
	.byte	26
	.word	1624
	.byte	0,26
	.word	1664
	.byte	0,0,26
	.word	21971
	.byte	0,0,26
	.word	22145
	.byte	25
	.word	21974
	.byte	24
	.word	22000
	.byte	27
	.word	22017
	.byte	25
	.word	1741
	.byte	24
	.word	1774
	.byte	26
	.word	1791
	.byte	0,26
	.word	22018
	.byte	0,0,26
	.word	22146
	.byte	25
	.word	21810
	.byte	24
	.word	21833
	.byte	26
	.word	21846
	.byte	0,26
	.word	22147
	.byte	0,0,6,0,0,23
	.word	22128
	.byte	17,25
	.word	21941
	.byte	27
	.word	21970
	.byte	25
	.word	1626
	.byte	27
	.word	1663
	.byte	25
	.word	1584
	.byte	26
	.word	1624
	.byte	0,26
	.word	1664
	.byte	0,0,26
	.word	21971
	.byte	0,0,6,25
	.word	21974
	.byte	24
	.word	22000
	.byte	27
	.word	22017
	.byte	25
	.word	1741
	.byte	24
	.word	1774
	.byte	26
	.word	1791
	.byte	0,26
	.word	22018
	.byte	0,0,6,25
	.word	21810
	.byte	24
	.word	21833
	.byte	26
	.word	21846
	.byte	0,6,0,0,23
	.word	22150
	.byte	17,25
	.word	21810
	.byte	24
	.word	21833
	.byte	26
	.word	21846
	.byte	0,6,0,0,21
	.byte	'wireless_uart_send_buffer',0,19,90,13
	.word	1793
	.byte	1,1,1,1,5
	.byte	'buff',0,19,90,58
	.word	22263
	.byte	5
	.byte	'len',0,19,90,71
	.word	1793
	.byte	0,21
	.byte	'wireless_uart_read_buffer',0,19,94,13
	.word	1793
	.byte	1,1,1,1,5
	.byte	'buff',0,19,94,53
	.word	22192
	.byte	5
	.byte	'len',0,19,94,66
	.word	1793
	.byte	0,21
	.byte	'ble6a20_send_buffer',0,20,97,13
	.word	1793
	.byte	1,1,1,1,5
	.byte	'buff',0,20,97,53
	.word	22263
	.byte	5
	.byte	'len',0,20,97,66
	.word	1793
	.byte	0,21
	.byte	'ble6a20_read_buffer',0,20,101,13
	.word	1793
	.byte	1,1,1,1,5
	.byte	'buff',0,20,101,47
	.word	22192
	.byte	5
	.byte	'len',0,20,101,60
	.word	1793
	.byte	0,21
	.byte	'bluetooth_ch9141_send_buffer',0,21,67,9
	.word	1793
	.byte	1,1,1,1,5
	.byte	'buff',0,21,67,59
	.word	22263
	.byte	5
	.byte	'len',0,21,67,72
	.word	1793
	.byte	0,21
	.byte	'bluetooth_ch9141_read_buffer',0,21,70,9
	.word	1793
	.byte	1,1,1,1,5
	.byte	'buff',0,21,70,53
	.word	22192
	.byte	5
	.byte	'len',0,21,70,66
	.word	1793
	.byte	0,21
	.byte	'wifi_uart_send_buffer',0,22,151,1,9
	.word	1793
	.byte	1,1,1,1,5
	.byte	'buff',0,22,151,1,59
	.word	22263
	.byte	5
	.byte	'len',0,22,151,1,72
	.word	1793
	.byte	0,21
	.byte	'wifi_uart_read_buffer',0,22,153,1,9
	.word	1793
	.byte	1,1,1,1,5
	.byte	'buff',0,22,153,1,53
	.word	22192
	.byte	5
	.byte	'len',0,22,153,1,66
	.word	1793
	.byte	0,21
	.byte	'wifi_spi_send_buffer',0,23,146,1,8
	.word	1793
	.byte	1,1,1,1,5
	.byte	'buff',0,23,146,1,51
	.word	22263
	.byte	5
	.byte	'length',0,23,146,1,64
	.word	1793
	.byte	0,21
	.byte	'wifi_spi_read_buffer',0,23,147,1,8
	.word	1793
	.byte	1,1,1,1,5
	.byte	'buffer',0,23,147,1,45
	.word	22192
	.byte	5
	.byte	'length',0,23,147,1,60
	.word	1793
	.byte	0
.L45:
	.byte	15,24,42,9,1,16
	.byte	'SEEKFREE_ASSISTANT_DEBUG_UART',0,0,16
	.byte	'SEEKFREE_ASSISTANT_WIRELESS_UART',0,1,16
	.byte	'SEEKFREE_ASSISTANT_BLE6A20',0,2,16
	.byte	'SEEKFREE_ASSISTANT_CH9141',0,3,16
	.byte	'SEEKFREE_ASSISTANT_WIFI_UART',0,4,16
	.byte	'SEEKFREE_ASSISTANT_WIFI_SPI',0,5,16
	.byte	'SEEKFREE_ASSISTANT_CUSTOM',0,6,0,28
	.byte	'__INDIRECT__',0,25,1,1,1,1,1,29
	.byte	'__wchar_t',0,25,1,1
	.word	18048
	.byte	29
	.byte	'__size_t',0,25,1,1
	.word	484
	.byte	29
	.byte	'__ptrdiff_t',0,25,1,1
	.word	500
	.byte	30,1,3
	.word	24149
	.byte	29
	.byte	'__codeptr',0,25,1,1
	.word	24151
	.byte	29
	.byte	'__intptr_t',0,25,1,1
	.word	500
	.byte	29
	.byte	'__uintptr_t',0,25,1,1
	.word	484
	.byte	29
	.byte	'_iob_flag_t',0,26,82,25
	.word	676
	.byte	29
	.byte	'boolean',0,27,101,29
	.word	659
	.byte	29
	.byte	'uint8',0,27,105,29
	.word	659
	.byte	29
	.byte	'uint16',0,27,109,29
	.word	676
	.byte	29
	.byte	'uint32',0,27,113,29
	.word	1793
	.byte	29
	.byte	'uint64',0,27,118,29
	.word	366
	.byte	29
	.byte	'sint16',0,27,126,29
	.word	18048
	.byte	29
	.byte	'sint32',0,27,131,1,29
	.word	18061
	.byte	29
	.byte	'sint64',0,27,138,1,29
	.word	22021
	.byte	29
	.byte	'float32',0,27,167,1,29
	.word	280
	.byte	29
	.byte	'pvoid',0,28,57,28
	.word	398
	.byte	29
	.byte	'Ifx_TickTime',0,28,79,28
	.word	22021
	.byte	29
	.byte	'Ifx_SizeT',0,28,92,16
	.word	18048
	.byte	29
	.byte	'Ifx_Priority',0,28,103,16
	.word	676
	.byte	15,28,130,1,9,1,16
	.byte	'Ifx_RxSel_a',0,0,16
	.byte	'Ifx_RxSel_b',0,1,16
	.byte	'Ifx_RxSel_c',0,2,16
	.byte	'Ifx_RxSel_d',0,3,16
	.byte	'Ifx_RxSel_e',0,4,16
	.byte	'Ifx_RxSel_f',0,5,16
	.byte	'Ifx_RxSel_g',0,6,16
	.byte	'Ifx_RxSel_h',0,7,0,29
	.byte	'Ifx_RxSel',0,28,140,1,3
	.word	24446
	.byte	15,28,164,1,9,1,16
	.byte	'Ifx_DataBufferMode_normal',0,0,16
	.byte	'Ifx_DataBufferMode_timeStampSingle',0,1,0,29
	.byte	'Ifx_DataBufferMode',0,28,169,1,2
	.word	24584
	.byte	7
	.byte	'char',0,1,6,29
	.byte	'int8',0,29,54,29
	.word	24684
	.byte	29
	.byte	'int16',0,29,55,29
	.word	18048
	.byte	29
	.byte	'int32',0,29,56,29
	.word	500
	.byte	29
	.byte	'int64',0,29,57,29
	.word	22021
	.byte	15,30,78,9,1,16
	.byte	'FIFO_DATA_8BIT',0,0,16
	.byte	'FIFO_DATA_16BIT',0,1,16
	.byte	'FIFO_DATA_32BIT',0,2,0,29
	.byte	'fifo_data_type_enum',0,30,83,2
	.word	24747
	.byte	29
	.byte	'Ifx_ASCLIN_ACCEN0_Bits',0,13,79,3
	.word	16486
	.byte	29
	.byte	'Ifx_ASCLIN_ACCEN1_Bits',0,13,85,3
	.word	16394
	.byte	29
	.byte	'Ifx_ASCLIN_BITCON_Bits',0,13,97,3
	.word	12018
	.byte	29
	.byte	'Ifx_ASCLIN_BRD_Bits',0,13,106,3
	.word	12858
	.byte	29
	.byte	'Ifx_ASCLIN_BRG_Bits',0,13,115,3
	.word	12701
	.byte	29
	.byte	'Ifx_ASCLIN_CLC_Bits',0,13,125,3
	.word	10973
	.byte	29
	.byte	'Ifx_ASCLIN_CSR_Bits',0,13,133,1,3
	.word	15667
	.byte	29
	.byte	'Ifx_ASCLIN_DATCON_Bits',0,13,145,1,3
	.word	12504
	.byte	29
	.byte	'Ifx_ASCLIN_FLAGS_Bits',0,13,177,1,3
	.word	13514
	.byte	29
	.byte	'Ifx_ASCLIN_FLAGSCLEAR_Bits',0,13,209,1,3
	.word	14513
	.byte	29
	.byte	'Ifx_ASCLIN_FLAGSENABLE_Bits',0,13,238,1,3
	.word	15028
	.byte	29
	.byte	'Ifx_ASCLIN_FLAGSSET_Bits',0,13,142,2,3
	.word	14000
	.byte	29
	.byte	'Ifx_ASCLIN_FRAMECON_Bits',0,13,158,2,3
	.word	12239
	.byte	29
	.byte	'Ifx_ASCLIN_ID_Bits',0,13,166,2,3
	.word	11427
	.byte	29
	.byte	'Ifx_ASCLIN_IOCR_Bits',0,13,184,2,3
	.word	11132
	.byte	29
	.byte	'Ifx_ASCLIN_KRST0_Bits',0,13,192,2,3
	.word	16268
	.byte	29
	.byte	'Ifx_ASCLIN_KRST1_Bits',0,13,199,2,3
	.word	16161
	.byte	29
	.byte	'Ifx_ASCLIN_KRSTCLR_Bits',0,13,206,2,3
	.word	16052
	.byte	29
	.byte	'Ifx_ASCLIN_LIN_BTIMER_Bits',0,13,213,2,3
	.word	13212
	.byte	29
	.byte	'Ifx_ASCLIN_LIN_CON_Bits',0,13,225,2,3
	.word	13012
	.byte	29
	.byte	'Ifx_ASCLIN_LIN_HTIMER_Bits',0,13,232,2,3
	.word	13326
	.byte	29
	.byte	'Ifx_ASCLIN_OCS_Bits',0,13,242,2,3
	.word	15889
	.byte	29
	.byte	'Ifx_ASCLIN_RXDATA_Bits',0,13,248,2,3
	.word	15580
	.byte	29
	.byte	'Ifx_ASCLIN_RXDATAD_Bits',0,13,254,2,3
	.word	15790
	.byte	29
	.byte	'Ifx_ASCLIN_RXFIFOCON_Bits',0,13,140,3,3
	.word	11777
	.byte	29
	.byte	'Ifx_ASCLIN_TXDATA_Bits',0,13,146,3,3
	.word	15493
	.byte	29
	.byte	'Ifx_ASCLIN_TXFIFOCON_Bits',0,13,159,3,3
	.word	11552
	.byte	29
	.byte	'Ifx_ASCLIN_ACCEN0',0,13,172,3,3
	.word	17018
	.byte	29
	.byte	'Ifx_ASCLIN_ACCEN1',0,13,180,3,3
	.word	16446
	.byte	29
	.byte	'Ifx_ASCLIN_BITCON',0,13,188,3,3
	.word	12199
	.byte	29
	.byte	'Ifx_ASCLIN_BRD',0,13,196,3,3
	.word	12972
	.byte	29
	.byte	'Ifx_ASCLIN_BRG',0,13,204,3,3
	.word	12818
	.byte	29
	.byte	'Ifx_ASCLIN_CLC',0,13,212,3,3
	.word	11092
	.byte	29
	.byte	'Ifx_ASCLIN_CSR',0,13,220,3,3
	.word	15750
	.byte	29
	.byte	'Ifx_ASCLIN_DATCON',0,13,228,3,3
	.word	12661
	.byte	29
	.byte	'Ifx_ASCLIN_FLAGS',0,13,236,3,3
	.word	13960
	.byte	29
	.byte	'Ifx_ASCLIN_FLAGSCLEAR',0,13,244,3,3
	.word	14988
	.byte	29
	.byte	'Ifx_ASCLIN_FLAGSENABLE',0,13,252,3,3
	.word	15453
	.byte	29
	.byte	'Ifx_ASCLIN_FLAGSSET',0,13,132,4,3
	.word	14473
	.byte	29
	.byte	'Ifx_ASCLIN_FRAMECON',0,13,140,4,3
	.word	12464
	.byte	29
	.byte	'Ifx_ASCLIN_ID',0,13,148,4,3
	.word	11512
	.byte	29
	.byte	'Ifx_ASCLIN_IOCR',0,13,156,4,3
	.word	11387
	.byte	29
	.byte	'Ifx_ASCLIN_KRST0',0,13,164,4,3
	.word	16354
	.byte	29
	.byte	'Ifx_ASCLIN_KRST1',0,13,172,4,3
	.word	16228
	.byte	29
	.byte	'Ifx_ASCLIN_KRSTCLR',0,13,180,4,3
	.word	16121
	.byte	29
	.byte	'Ifx_ASCLIN_LIN_BTIMER',0,13,188,4,3
	.word	13286
	.byte	29
	.byte	'Ifx_ASCLIN_LIN_CON',0,13,196,4,3
	.word	13172
	.byte	29
	.byte	'Ifx_ASCLIN_LIN_HTIMER',0,13,204,4,3
	.word	13401
	.byte	29
	.byte	'Ifx_ASCLIN_OCS',0,13,212,4,3
	.word	16012
	.byte	29
	.byte	'Ifx_ASCLIN_RXDATA',0,13,220,4,3
	.word	15627
	.byte	29
	.byte	'Ifx_ASCLIN_RXDATAD',0,13,228,4,3
	.word	15838
	.byte	29
	.byte	'Ifx_ASCLIN_RXFIFOCON',0,13,236,4,3
	.word	11978
	.byte	29
	.byte	'Ifx_ASCLIN_TXDATA',0,13,244,4,3
	.word	15540
	.byte	29
	.byte	'Ifx_ASCLIN_TXFIFOCON',0,13,252,4,3
	.word	11737
	.byte	14
	.word	13441
	.byte	29
	.byte	'Ifx_ASCLIN_LIN',0,13,140,5,3
	.word	26433
	.byte	14
	.word	17058
	.byte	29
	.byte	'Ifx_ASCLIN',0,13,181,5,3
	.word	26462
	.byte	15,31,236,10,9,1,16
	.byte	'IfxScu_CCUCON0_CLKSEL_fBack',0,0,16
	.byte	'IfxScu_CCUCON0_CLKSEL_fPll',0,1,0,29
	.byte	'IfxScu_CCUCON0_CLKSEL',0,31,240,10,3
	.word	26487
	.byte	15,31,250,10,9,1,16
	.byte	'IfxScu_WDTCON1_IR_divBy16384',0,0,16
	.byte	'IfxScu_WDTCON1_IR_divBy256',0,1,16
	.byte	'IfxScu_WDTCON1_IR_divBy64',0,2,0,29
	.byte	'IfxScu_WDTCON1_IR',0,31,255,10,3
	.word	26584
	.byte	10
	.byte	'_Ifx_SCU_ACCEN0_Bits',0,4,45,16,4,11
	.byte	'EN0',0,1
	.word	659
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	659
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	659
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	659
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	659
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	659
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	659
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	659
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	659
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	659
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	659
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	659
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	659
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	659
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	659
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	659
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	659
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	659
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	659
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	659
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	659
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	659
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	659
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	659
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	659
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	659
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	659
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	659
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	659
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	659
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	659
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	659
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_ACCEN0_Bits',0,4,79,3
	.word	26706
	.byte	10
	.byte	'_Ifx_SCU_ACCEN1_Bits',0,4,82,16,4,11
	.byte	'reserved_0',0,4
	.word	484
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_SCU_ACCEN1_Bits',0,4,85,3
	.word	27263
	.byte	10
	.byte	'_Ifx_SCU_ARSTDIS_Bits',0,4,88,16,4,11
	.byte	'STM0DIS',0,1
	.word	659
	.byte	1,7,2,35,0,11
	.byte	'STM1DIS',0,1
	.word	659
	.byte	1,6,2,35,0,11
	.byte	'STM2DIS',0,1
	.word	659
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	484
	.byte	29,0,2,35,0,0,29
	.byte	'Ifx_SCU_ARSTDIS_Bits',0,4,94,3
	.word	27340
	.byte	10
	.byte	'_Ifx_SCU_CCUCON0_Bits',0,4,97,16,4,11
	.byte	'BAUD1DIV',0,1
	.word	659
	.byte	4,4,2,35,0,11
	.byte	'BAUD2DIV',0,1
	.word	659
	.byte	4,0,2,35,0,11
	.byte	'SRIDIV',0,1
	.word	659
	.byte	4,4,2,35,1,11
	.byte	'LPDIV',0,1
	.word	659
	.byte	4,0,2,35,1,11
	.byte	'SPBDIV',0,1
	.word	659
	.byte	4,4,2,35,2,11
	.byte	'FSI2DIV',0,1
	.word	659
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	659
	.byte	2,0,2,35,2,11
	.byte	'FSIDIV',0,1
	.word	659
	.byte	2,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	659
	.byte	2,4,2,35,3,11
	.byte	'CLKSEL',0,1
	.word	659
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	659
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	659
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_CCUCON0_Bits',0,4,111,3
	.word	27476
	.byte	10
	.byte	'_Ifx_SCU_CCUCON1_Bits',0,4,114,16,4,11
	.byte	'CANDIV',0,1
	.word	659
	.byte	4,4,2,35,0,11
	.byte	'ERAYDIV',0,1
	.word	659
	.byte	4,0,2,35,0,11
	.byte	'STMDIV',0,1
	.word	659
	.byte	4,4,2,35,1,11
	.byte	'GTMDIV',0,1
	.word	659
	.byte	4,0,2,35,1,11
	.byte	'ETHDIV',0,1
	.word	659
	.byte	4,4,2,35,2,11
	.byte	'ASCLINFDIV',0,1
	.word	659
	.byte	4,0,2,35,2,11
	.byte	'ASCLINSDIV',0,1
	.word	659
	.byte	4,4,2,35,3,11
	.byte	'INSEL',0,1
	.word	659
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	659
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	659
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_CCUCON1_Bits',0,4,126,3
	.word	27756
	.byte	10
	.byte	'_Ifx_SCU_CCUCON2_Bits',0,4,129,1,16,4,11
	.byte	'BBBDIV',0,1
	.word	659
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	484
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	659
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	659
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_CCUCON2_Bits',0,4,135,1,3
	.word	27994
	.byte	10
	.byte	'_Ifx_SCU_CCUCON3_Bits',0,4,138,1,16,4,11
	.byte	'PLLDIV',0,1
	.word	659
	.byte	6,2,2,35,0,11
	.byte	'PLLSEL',0,1
	.word	659
	.byte	2,0,2,35,0,11
	.byte	'PLLERAYDIV',0,1
	.word	659
	.byte	6,2,2,35,1,11
	.byte	'PLLERAYSEL',0,1
	.word	659
	.byte	2,0,2,35,1,11
	.byte	'SRIDIV',0,1
	.word	659
	.byte	6,2,2,35,2,11
	.byte	'SRISEL',0,1
	.word	659
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	659
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	659
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	659
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	659
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_CCUCON3_Bits',0,4,150,1,3
	.word	28122
	.byte	10
	.byte	'_Ifx_SCU_CCUCON4_Bits',0,4,153,1,16,4,11
	.byte	'SPBDIV',0,1
	.word	659
	.byte	6,2,2,35,0,11
	.byte	'SPBSEL',0,1
	.word	659
	.byte	2,0,2,35,0,11
	.byte	'GTMDIV',0,1
	.word	659
	.byte	6,2,2,35,1,11
	.byte	'GTMSEL',0,1
	.word	659
	.byte	2,0,2,35,1,11
	.byte	'STMDIV',0,1
	.word	659
	.byte	6,2,2,35,2,11
	.byte	'STMSEL',0,1
	.word	659
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	659
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	659
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	659
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	659
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_CCUCON4_Bits',0,4,165,1,3
	.word	28365
	.byte	10
	.byte	'_Ifx_SCU_CCUCON5_Bits',0,4,168,1,16,4,11
	.byte	'MAXDIV',0,1
	.word	659
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	484
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	659
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	659
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_CCUCON5_Bits',0,4,174,1,3
	.word	28600
	.byte	10
	.byte	'_Ifx_SCU_CCUCON6_Bits',0,4,177,1,16,4,11
	.byte	'CPU0DIV',0,1
	.word	659
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	484
	.byte	26,0,2,35,0,0,29
	.byte	'Ifx_SCU_CCUCON6_Bits',0,4,181,1,3
	.word	28728
	.byte	10
	.byte	'_Ifx_SCU_CCUCON7_Bits',0,4,184,1,16,4,11
	.byte	'CPU1DIV',0,1
	.word	659
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	484
	.byte	26,0,2,35,0,0,29
	.byte	'Ifx_SCU_CCUCON7_Bits',0,4,188,1,3
	.word	28828
	.byte	10
	.byte	'_Ifx_SCU_CHIPID_Bits',0,4,191,1,16,4,11
	.byte	'CHREV',0,1
	.word	659
	.byte	6,2,2,35,0,11
	.byte	'CHTEC',0,1
	.word	659
	.byte	2,0,2,35,0,11
	.byte	'CHID',0,1
	.word	659
	.byte	8,0,2,35,1,11
	.byte	'EEA',0,1
	.word	659
	.byte	1,7,2,35,2,11
	.byte	'UCODE',0,1
	.word	659
	.byte	7,0,2,35,2,11
	.byte	'FSIZE',0,1
	.word	659
	.byte	4,4,2,35,3,11
	.byte	'SP',0,1
	.word	659
	.byte	2,2,2,35,3,11
	.byte	'SEC',0,1
	.word	659
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	659
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_CHIPID_Bits',0,4,202,1,3
	.word	28928
	.byte	10
	.byte	'_Ifx_SCU_DTSCON_Bits',0,4,205,1,16,4,11
	.byte	'PWD',0,1
	.word	659
	.byte	1,7,2,35,0,11
	.byte	'START',0,1
	.word	659
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	659
	.byte	2,4,2,35,0,11
	.byte	'CAL',0,4
	.word	484
	.byte	20,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	659
	.byte	7,1,2,35,3,11
	.byte	'SLCK',0,1
	.word	659
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_DTSCON_Bits',0,4,213,1,3
	.word	29136
	.byte	10
	.byte	'_Ifx_SCU_DTSLIM_Bits',0,4,216,1,16,4,11
	.byte	'LOWER',0,2
	.word	676
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	659
	.byte	5,1,2,35,1,11
	.byte	'LLU',0,1
	.word	659
	.byte	1,0,2,35,1,11
	.byte	'UPPER',0,2
	.word	676
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	659
	.byte	4,2,2,35,3,11
	.byte	'SLCK',0,1
	.word	659
	.byte	1,1,2,35,3,11
	.byte	'UOF',0,1
	.word	659
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_DTSLIM_Bits',0,4,225,1,3
	.word	29301
	.byte	10
	.byte	'_Ifx_SCU_DTSSTAT_Bits',0,4,228,1,16,4,11
	.byte	'RESULT',0,2
	.word	676
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	659
	.byte	4,2,2,35,1,11
	.byte	'RDY',0,1
	.word	659
	.byte	1,1,2,35,1,11
	.byte	'BUSY',0,1
	.word	659
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	676
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_SCU_DTSSTAT_Bits',0,4,235,1,3
	.word	29484
	.byte	10
	.byte	'_Ifx_SCU_EICR_Bits',0,4,238,1,16,4,11
	.byte	'reserved_0',0,1
	.word	659
	.byte	4,4,2,35,0,11
	.byte	'EXIS0',0,1
	.word	659
	.byte	3,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	659
	.byte	1,0,2,35,0,11
	.byte	'FEN0',0,1
	.word	659
	.byte	1,7,2,35,1,11
	.byte	'REN0',0,1
	.word	659
	.byte	1,6,2,35,1,11
	.byte	'LDEN0',0,1
	.word	659
	.byte	1,5,2,35,1,11
	.byte	'EIEN0',0,1
	.word	659
	.byte	1,4,2,35,1,11
	.byte	'INP0',0,1
	.word	659
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	484
	.byte	5,12,2,35,0,11
	.byte	'EXIS1',0,1
	.word	659
	.byte	3,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	659
	.byte	1,0,2,35,2,11
	.byte	'FEN1',0,1
	.word	659
	.byte	1,7,2,35,3,11
	.byte	'REN1',0,1
	.word	659
	.byte	1,6,2,35,3,11
	.byte	'LDEN1',0,1
	.word	659
	.byte	1,5,2,35,3,11
	.byte	'EIEN1',0,1
	.word	659
	.byte	1,4,2,35,3,11
	.byte	'INP1',0,1
	.word	659
	.byte	3,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	659
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EICR_Bits',0,4,129,2,3
	.word	29638
	.byte	10
	.byte	'_Ifx_SCU_EIFR_Bits',0,4,132,2,16,4,11
	.byte	'INTF0',0,1
	.word	659
	.byte	1,7,2,35,0,11
	.byte	'INTF1',0,1
	.word	659
	.byte	1,6,2,35,0,11
	.byte	'INTF2',0,1
	.word	659
	.byte	1,5,2,35,0,11
	.byte	'INTF3',0,1
	.word	659
	.byte	1,4,2,35,0,11
	.byte	'INTF4',0,1
	.word	659
	.byte	1,3,2,35,0,11
	.byte	'INTF5',0,1
	.word	659
	.byte	1,2,2,35,0,11
	.byte	'INTF6',0,1
	.word	659
	.byte	1,1,2,35,0,11
	.byte	'INTF7',0,1
	.word	659
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	484
	.byte	24,0,2,35,0,0,29
	.byte	'Ifx_SCU_EIFR_Bits',0,4,143,2,3
	.word	30002
	.byte	10
	.byte	'_Ifx_SCU_EMSR_Bits',0,4,146,2,16,4,11
	.byte	'POL',0,1
	.word	659
	.byte	1,7,2,35,0,11
	.byte	'MODE',0,1
	.word	659
	.byte	1,6,2,35,0,11
	.byte	'ENON',0,1
	.word	659
	.byte	1,5,2,35,0,11
	.byte	'PSEL',0,1
	.word	659
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	676
	.byte	12,0,2,35,0,11
	.byte	'EMSF',0,1
	.word	659
	.byte	1,7,2,35,2,11
	.byte	'SEMSF',0,1
	.word	659
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	659
	.byte	6,0,2,35,2,11
	.byte	'EMSFM',0,1
	.word	659
	.byte	2,6,2,35,3,11
	.byte	'SEMSFM',0,1
	.word	659
	.byte	2,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	659
	.byte	4,0,2,35,3,0,29
	.byte	'Ifx_SCU_EMSR_Bits',0,4,159,2,3
	.word	30213
	.byte	10
	.byte	'_Ifx_SCU_ESRCFG_Bits',0,4,162,2,16,4,11
	.byte	'reserved_0',0,1
	.word	659
	.byte	7,1,2,35,0,11
	.byte	'EDCON',0,2
	.word	676
	.byte	2,7,2,35,0,11
	.byte	'reserved_9',0,4
	.word	484
	.byte	23,0,2,35,0,0,29
	.byte	'Ifx_SCU_ESRCFG_Bits',0,4,167,2,3
	.word	30465
	.byte	10
	.byte	'_Ifx_SCU_ESROCFG_Bits',0,4,170,2,16,4,11
	.byte	'ARI',0,1
	.word	659
	.byte	1,7,2,35,0,11
	.byte	'ARC',0,1
	.word	659
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	484
	.byte	30,0,2,35,0,0,29
	.byte	'Ifx_SCU_ESROCFG_Bits',0,4,175,2,3
	.word	30583
	.byte	10
	.byte	'_Ifx_SCU_EVR13CON_Bits',0,4,178,2,16,4,11
	.byte	'reserved_0',0,4
	.word	484
	.byte	28,4,2,35,0,11
	.byte	'EVR13OFF',0,1
	.word	659
	.byte	1,3,2,35,3,11
	.byte	'BPEVR13OFF',0,1
	.word	659
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	659
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	659
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVR13CON_Bits',0,4,185,2,3
	.word	30694
	.byte	10
	.byte	'_Ifx_SCU_EVR33CON_Bits',0,4,188,2,16,4,11
	.byte	'reserved_0',0,4
	.word	484
	.byte	28,4,2,35,0,11
	.byte	'EVR33OFF',0,1
	.word	659
	.byte	1,3,2,35,3,11
	.byte	'BPEVR33OFF',0,1
	.word	659
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	659
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	659
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVR33CON_Bits',0,4,195,2,3
	.word	30857
	.byte	10
	.byte	'_Ifx_SCU_EVRADCSTAT_Bits',0,4,198,2,16,4,11
	.byte	'ADC13V',0,1
	.word	659
	.byte	8,0,2,35,0,11
	.byte	'ADC33V',0,1
	.word	659
	.byte	8,0,2,35,1,11
	.byte	'ADCSWDV',0,1
	.word	659
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	659
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	659
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRADCSTAT_Bits',0,4,205,2,3
	.word	31020
	.byte	10
	.byte	'_Ifx_SCU_EVRDVSTAT_Bits',0,4,208,2,16,4,11
	.byte	'DVS13TRIM',0,1
	.word	659
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	659
	.byte	8,0,2,35,1,11
	.byte	'DVS33TRIM',0,1
	.word	659
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	659
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	659
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRDVSTAT_Bits',0,4,215,2,3
	.word	31178
	.byte	10
	.byte	'_Ifx_SCU_EVRMONCTRL_Bits',0,4,218,2,16,4,11
	.byte	'EVR13OVMOD',0,1
	.word	659
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	659
	.byte	2,4,2,35,0,11
	.byte	'EVR13UVMOD',0,1
	.word	659
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	659
	.byte	2,0,2,35,0,11
	.byte	'EVR33OVMOD',0,1
	.word	659
	.byte	2,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	659
	.byte	2,4,2,35,1,11
	.byte	'EVR33UVMOD',0,1
	.word	659
	.byte	2,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	659
	.byte	2,0,2,35,1,11
	.byte	'SWDOVMOD',0,1
	.word	659
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	659
	.byte	2,4,2,35,2,11
	.byte	'SWDUVMOD',0,1
	.word	659
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,2
	.word	676
	.byte	10,0,2,35,2,0,29
	.byte	'Ifx_SCU_EVRMONCTRL_Bits',0,4,232,2,3
	.word	31343
	.byte	10
	.byte	'_Ifx_SCU_EVROSCCTRL_Bits',0,4,235,2,16,4,11
	.byte	'OSCTRIM',0,2
	.word	676
	.byte	10,6,2,35,0,11
	.byte	'OSCPTAT',0,1
	.word	659
	.byte	6,0,2,35,1,11
	.byte	'OSCANASEL',0,1
	.word	659
	.byte	4,4,2,35,2,11
	.byte	'HPBGTRIM',0,2
	.word	676
	.byte	7,5,2,35,2,11
	.byte	'HPBGCLKEN',0,1
	.word	659
	.byte	1,4,2,35,3,11
	.byte	'OSC3V3',0,1
	.word	659
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	659
	.byte	2,1,2,35,3,11
	.byte	'LCK',0,1
	.word	659
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVROSCCTRL_Bits',0,4,245,2,3
	.word	31672
	.byte	10
	.byte	'_Ifx_SCU_EVROVMON_Bits',0,4,248,2,16,4,11
	.byte	'EVR13OVVAL',0,1
	.word	659
	.byte	8,0,2,35,0,11
	.byte	'EVR33OVVAL',0,1
	.word	659
	.byte	8,0,2,35,1,11
	.byte	'SWDOVVAL',0,1
	.word	659
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	659
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	659
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVROVMON_Bits',0,4,255,2,3
	.word	31893
	.byte	10
	.byte	'_Ifx_SCU_EVRRSTCON_Bits',0,4,130,3,16,4,11
	.byte	'RST13TRIM',0,1
	.word	659
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	484
	.byte	16,8,2,35,0,11
	.byte	'RST13OFF',0,1
	.word	659
	.byte	1,7,2,35,3,11
	.byte	'BPRST13OFF',0,1
	.word	659
	.byte	1,6,2,35,3,11
	.byte	'RST33OFF',0,1
	.word	659
	.byte	1,5,2,35,3,11
	.byte	'BPRST33OFF',0,1
	.word	659
	.byte	1,4,2,35,3,11
	.byte	'RSTSWDOFF',0,1
	.word	659
	.byte	1,3,2,35,3,11
	.byte	'BPRSTSWDOFF',0,1
	.word	659
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	659
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	659
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRRSTCON_Bits',0,4,142,3,3
	.word	32056
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF1_Bits',0,4,145,3,16,4,11
	.byte	'SD5P',0,1
	.word	659
	.byte	8,0,2,35,0,11
	.byte	'SD5I',0,1
	.word	659
	.byte	8,0,2,35,1,11
	.byte	'SD5D',0,1
	.word	659
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	659
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	659
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF1_Bits',0,4,152,3,3
	.word	32328
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF2_Bits',0,4,155,3,16,4,11
	.byte	'SD33P',0,1
	.word	659
	.byte	8,0,2,35,0,11
	.byte	'SD33I',0,1
	.word	659
	.byte	8,0,2,35,1,11
	.byte	'SD33D',0,1
	.word	659
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	659
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	659
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF2_Bits',0,4,162,3,3
	.word	32481
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF3_Bits',0,4,165,3,16,4,11
	.byte	'CT5REG0',0,1
	.word	659
	.byte	8,0,2,35,0,11
	.byte	'CT5REG1',0,1
	.word	659
	.byte	8,0,2,35,1,11
	.byte	'CT5REG2',0,1
	.word	659
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	659
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	659
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF3_Bits',0,4,172,3,3
	.word	32637
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF4_Bits',0,4,175,3,16,4,11
	.byte	'CT5REG3',0,1
	.word	659
	.byte	8,0,2,35,0,11
	.byte	'CT5REG4',0,1
	.word	659
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	676
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	659
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF4_Bits',0,4,181,3,3
	.word	32799
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF5_Bits',0,4,184,3,16,4,11
	.byte	'CT33REG0',0,1
	.word	659
	.byte	8,0,2,35,0,11
	.byte	'CT33REG1',0,1
	.word	659
	.byte	8,0,2,35,1,11
	.byte	'CT33REG2',0,1
	.word	659
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	659
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	659
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF5_Bits',0,4,191,3,3
	.word	32942
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF6_Bits',0,4,194,3,16,4,11
	.byte	'CT33REG3',0,1
	.word	659
	.byte	8,0,2,35,0,11
	.byte	'CT33REG4',0,1
	.word	659
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	676
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	659
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF6_Bits',0,4,200,3,3
	.word	33107
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL1_Bits',0,4,203,3,16,4,11
	.byte	'SDFREQSPRD',0,2
	.word	676
	.byte	16,0,2,35,0,11
	.byte	'SDFREQ',0,1
	.word	659
	.byte	8,0,2,35,2,11
	.byte	'SDSTEP',0,1
	.word	659
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	659
	.byte	2,2,2,35,3,11
	.byte	'SDSAMPLE',0,1
	.word	659
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	659
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRSDCTRL1_Bits',0,4,211,3,3
	.word	33252
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL2_Bits',0,4,214,3,16,4,11
	.byte	'DRVP',0,1
	.word	659
	.byte	8,0,2,35,0,11
	.byte	'SDMINMAXDC',0,1
	.word	659
	.byte	8,0,2,35,1,11
	.byte	'DRVN',0,1
	.word	659
	.byte	8,0,2,35,2,11
	.byte	'SDLUT',0,1
	.word	659
	.byte	6,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	659
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	659
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRSDCTRL2_Bits',0,4,222,3,3
	.word	33433
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL3_Bits',0,4,225,3,16,4,11
	.byte	'SDPWMPRE',0,1
	.word	659
	.byte	8,0,2,35,0,11
	.byte	'SDPID',0,1
	.word	659
	.byte	8,0,2,35,1,11
	.byte	'SDVOKLVL',0,1
	.word	659
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	659
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	659
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRSDCTRL3_Bits',0,4,232,3,3
	.word	33607
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL4_Bits',0,4,235,3,16,4,11
	.byte	'reserved_0',0,1
	.word	659
	.byte	8,0,2,35,0,11
	.byte	'SYNCDIV',0,1
	.word	659
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	484
	.byte	20,1,2,35,0,11
	.byte	'LCK',0,1
	.word	659
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRSDCTRL4_Bits',0,4,241,3,3
	.word	33767
	.byte	10
	.byte	'_Ifx_SCU_EVRSTAT_Bits',0,4,244,3,16,4,11
	.byte	'EVR13',0,1
	.word	659
	.byte	1,7,2,35,0,11
	.byte	'OV13',0,1
	.word	659
	.byte	1,6,2,35,0,11
	.byte	'EVR33',0,1
	.word	659
	.byte	1,5,2,35,0,11
	.byte	'OV33',0,1
	.word	659
	.byte	1,4,2,35,0,11
	.byte	'OVSWD',0,1
	.word	659
	.byte	1,3,2,35,0,11
	.byte	'UV13',0,1
	.word	659
	.byte	1,2,2,35,0,11
	.byte	'UV33',0,1
	.word	659
	.byte	1,1,2,35,0,11
	.byte	'UVSWD',0,1
	.word	659
	.byte	1,0,2,35,0,11
	.byte	'EXTPASS13',0,1
	.word	659
	.byte	1,7,2,35,1,11
	.byte	'EXTPASS33',0,1
	.word	659
	.byte	1,6,2,35,1,11
	.byte	'BGPROK',0,1
	.word	659
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	484
	.byte	21,0,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSTAT_Bits',0,4,130,4,3
	.word	33911
	.byte	10
	.byte	'_Ifx_SCU_EVRTRIM_Bits',0,4,133,4,16,4,11
	.byte	'EVR13TRIM',0,1
	.word	659
	.byte	8,0,2,35,0,11
	.byte	'SDVOUTSEL',0,1
	.word	659
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	676
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	659
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRTRIM_Bits',0,4,139,4,3
	.word	34185
	.byte	10
	.byte	'_Ifx_SCU_EVRUVMON_Bits',0,4,142,4,16,4,11
	.byte	'EVR13UVVAL',0,1
	.word	659
	.byte	8,0,2,35,0,11
	.byte	'EVR33UVVAL',0,1
	.word	659
	.byte	8,0,2,35,1,11
	.byte	'SWDUVVAL',0,1
	.word	659
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	659
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	659
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRUVMON_Bits',0,4,149,4,3
	.word	34324
	.byte	10
	.byte	'_Ifx_SCU_EXTCON_Bits',0,4,152,4,16,4,11
	.byte	'EN0',0,1
	.word	659
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	659
	.byte	1,6,2,35,0,11
	.byte	'SEL0',0,1
	.word	659
	.byte	4,2,2,35,0,11
	.byte	'reserved_6',0,2
	.word	676
	.byte	10,0,2,35,0,11
	.byte	'EN1',0,1
	.word	659
	.byte	1,7,2,35,2,11
	.byte	'NSEL',0,1
	.word	659
	.byte	1,6,2,35,2,11
	.byte	'SEL1',0,1
	.word	659
	.byte	4,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	659
	.byte	2,0,2,35,2,11
	.byte	'DIV1',0,1
	.word	659
	.byte	8,0,2,35,3,0,29
	.byte	'Ifx_SCU_EXTCON_Bits',0,4,163,4,3
	.word	34487
	.byte	10
	.byte	'_Ifx_SCU_FDR_Bits',0,4,166,4,16,4,11
	.byte	'STEP',0,2
	.word	676
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	659
	.byte	4,2,2,35,1,11
	.byte	'DM',0,1
	.word	659
	.byte	2,0,2,35,1,11
	.byte	'RESULT',0,2
	.word	676
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	659
	.byte	5,1,2,35,3,11
	.byte	'DISCLK',0,1
	.word	659
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_FDR_Bits',0,4,174,4,3
	.word	34705
	.byte	10
	.byte	'_Ifx_SCU_FMR_Bits',0,4,177,4,16,4,11
	.byte	'FS0',0,1
	.word	659
	.byte	1,7,2,35,0,11
	.byte	'FS1',0,1
	.word	659
	.byte	1,6,2,35,0,11
	.byte	'FS2',0,1
	.word	659
	.byte	1,5,2,35,0,11
	.byte	'FS3',0,1
	.word	659
	.byte	1,4,2,35,0,11
	.byte	'FS4',0,1
	.word	659
	.byte	1,3,2,35,0,11
	.byte	'FS5',0,1
	.word	659
	.byte	1,2,2,35,0,11
	.byte	'FS6',0,1
	.word	659
	.byte	1,1,2,35,0,11
	.byte	'FS7',0,1
	.word	659
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	659
	.byte	8,0,2,35,1,11
	.byte	'FC0',0,1
	.word	659
	.byte	1,7,2,35,2,11
	.byte	'FC1',0,1
	.word	659
	.byte	1,6,2,35,2,11
	.byte	'FC2',0,1
	.word	659
	.byte	1,5,2,35,2,11
	.byte	'FC3',0,1
	.word	659
	.byte	1,4,2,35,2,11
	.byte	'FC4',0,1
	.word	659
	.byte	1,3,2,35,2,11
	.byte	'FC5',0,1
	.word	659
	.byte	1,2,2,35,2,11
	.byte	'FC6',0,1
	.word	659
	.byte	1,1,2,35,2,11
	.byte	'FC7',0,1
	.word	659
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	659
	.byte	8,0,2,35,3,0,29
	.byte	'Ifx_SCU_FMR_Bits',0,4,197,4,3
	.word	34868
	.byte	10
	.byte	'_Ifx_SCU_ID_Bits',0,4,200,4,16,4,11
	.byte	'MODREV',0,1
	.word	659
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	659
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	676
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_SCU_ID_Bits',0,4,205,4,3
	.word	35204
	.byte	10
	.byte	'_Ifx_SCU_IGCR_Bits',0,4,208,4,16,4,11
	.byte	'IPEN00',0,1
	.word	659
	.byte	1,7,2,35,0,11
	.byte	'IPEN01',0,1
	.word	659
	.byte	1,6,2,35,0,11
	.byte	'IPEN02',0,1
	.word	659
	.byte	1,5,2,35,0,11
	.byte	'IPEN03',0,1
	.word	659
	.byte	1,4,2,35,0,11
	.byte	'IPEN04',0,1
	.word	659
	.byte	1,3,2,35,0,11
	.byte	'IPEN05',0,1
	.word	659
	.byte	1,2,2,35,0,11
	.byte	'IPEN06',0,1
	.word	659
	.byte	1,1,2,35,0,11
	.byte	'IPEN07',0,1
	.word	659
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	659
	.byte	5,3,2,35,1,11
	.byte	'GEEN0',0,1
	.word	659
	.byte	1,2,2,35,1,11
	.byte	'IGP0',0,1
	.word	659
	.byte	2,0,2,35,1,11
	.byte	'IPEN10',0,1
	.word	659
	.byte	1,7,2,35,2,11
	.byte	'IPEN11',0,1
	.word	659
	.byte	1,6,2,35,2,11
	.byte	'IPEN12',0,1
	.word	659
	.byte	1,5,2,35,2,11
	.byte	'IPEN13',0,1
	.word	659
	.byte	1,4,2,35,2,11
	.byte	'IPEN14',0,1
	.word	659
	.byte	1,3,2,35,2,11
	.byte	'IPEN15',0,1
	.word	659
	.byte	1,2,2,35,2,11
	.byte	'IPEN16',0,1
	.word	659
	.byte	1,1,2,35,2,11
	.byte	'IPEN17',0,1
	.word	659
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	659
	.byte	5,3,2,35,3,11
	.byte	'GEEN1',0,1
	.word	659
	.byte	1,2,2,35,3,11
	.byte	'IGP1',0,1
	.word	659
	.byte	2,0,2,35,3,0,29
	.byte	'Ifx_SCU_IGCR_Bits',0,4,232,4,3
	.word	35311
	.byte	10
	.byte	'_Ifx_SCU_IN_Bits',0,4,235,4,16,4,11
	.byte	'P0',0,1
	.word	659
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	659
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	484
	.byte	30,0,2,35,0,0,29
	.byte	'Ifx_SCU_IN_Bits',0,4,240,4,3
	.word	35763
	.byte	10
	.byte	'_Ifx_SCU_IOCR_Bits',0,4,243,4,16,4,11
	.byte	'reserved_0',0,1
	.word	659
	.byte	4,4,2,35,0,11
	.byte	'PC0',0,1
	.word	659
	.byte	4,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	659
	.byte	4,4,2,35,1,11
	.byte	'PC1',0,1
	.word	659
	.byte	4,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	676
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_SCU_IOCR_Bits',0,4,250,4,3
	.word	35862
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL0_Bits',0,4,253,4,16,4,11
	.byte	'LBISTREQ',0,1
	.word	659
	.byte	1,7,2,35,0,11
	.byte	'LBISTREQP',0,1
	.word	659
	.byte	1,6,2,35,0,11
	.byte	'PATTERNS',0,2
	.word	676
	.byte	14,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	676
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_SCU_LBISTCTRL0_Bits',0,4,131,5,3
	.word	36012
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL1_Bits',0,4,134,5,16,4,11
	.byte	'SEED',0,4
	.word	484
	.byte	23,9,2,35,0,11
	.byte	'reserved_23',0,1
	.word	659
	.byte	1,0,2,35,2,11
	.byte	'SPLITSH',0,1
	.word	659
	.byte	3,5,2,35,3,11
	.byte	'BODY',0,1
	.word	659
	.byte	1,4,2,35,3,11
	.byte	'LBISTFREQU',0,1
	.word	659
	.byte	4,0,2,35,3,0,29
	.byte	'Ifx_SCU_LBISTCTRL1_Bits',0,4,141,5,3
	.word	36161
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL2_Bits',0,4,144,5,16,4,11
	.byte	'SIGNATURE',0,4
	.word	484
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	659
	.byte	7,1,2,35,3,11
	.byte	'LBISTDONE',0,1
	.word	659
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_LBISTCTRL2_Bits',0,4,149,5,3
	.word	36322
	.byte	10
	.byte	'_Ifx_SCU_LCLCON_Bits',0,4,152,5,16,4,11
	.byte	'reserved_0',0,2
	.word	676
	.byte	16,0,2,35,0,11
	.byte	'LS',0,1
	.word	659
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,2
	.word	676
	.byte	14,1,2,35,2,11
	.byte	'LSEN',0,1
	.word	659
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_LCLCON_Bits',0,4,158,5,3
	.word	36452
	.byte	10
	.byte	'_Ifx_SCU_LCLTEST_Bits',0,4,161,5,16,4,11
	.byte	'LCLT0',0,1
	.word	659
	.byte	1,7,2,35,0,11
	.byte	'LCLT1',0,1
	.word	659
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	484
	.byte	30,0,2,35,0,0,29
	.byte	'Ifx_SCU_LCLTEST_Bits',0,4,166,5,3
	.word	36584
	.byte	10
	.byte	'_Ifx_SCU_MANID_Bits',0,4,169,5,16,4,11
	.byte	'DEPT',0,1
	.word	659
	.byte	5,3,2,35,0,11
	.byte	'MANUF',0,2
	.word	676
	.byte	11,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	676
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_SCU_MANID_Bits',0,4,174,5,3
	.word	36699
	.byte	10
	.byte	'_Ifx_SCU_OMR_Bits',0,4,177,5,16,4,11
	.byte	'PS0',0,1
	.word	659
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	659
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	676
	.byte	14,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	659
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	659
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	676
	.byte	14,0,2,35,2,0,29
	.byte	'Ifx_SCU_OMR_Bits',0,4,185,5,3
	.word	36810
	.byte	10
	.byte	'_Ifx_SCU_OSCCON_Bits',0,4,188,5,16,4,11
	.byte	'reserved_0',0,1
	.word	659
	.byte	1,7,2,35,0,11
	.byte	'PLLLV',0,1
	.word	659
	.byte	1,6,2,35,0,11
	.byte	'OSCRES',0,1
	.word	659
	.byte	1,5,2,35,0,11
	.byte	'GAINSEL',0,1
	.word	659
	.byte	2,3,2,35,0,11
	.byte	'MODE',0,1
	.word	659
	.byte	2,1,2,35,0,11
	.byte	'SHBY',0,1
	.word	659
	.byte	1,0,2,35,0,11
	.byte	'PLLHV',0,1
	.word	659
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	659
	.byte	1,6,2,35,1,11
	.byte	'X1D',0,1
	.word	659
	.byte	1,5,2,35,1,11
	.byte	'X1DEN',0,1
	.word	659
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	659
	.byte	4,0,2,35,1,11
	.byte	'OSCVAL',0,1
	.word	659
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	659
	.byte	2,1,2,35,2,11
	.byte	'APREN',0,1
	.word	659
	.byte	1,0,2,35,2,11
	.byte	'CAP0EN',0,1
	.word	659
	.byte	1,7,2,35,3,11
	.byte	'CAP1EN',0,1
	.word	659
	.byte	1,6,2,35,3,11
	.byte	'CAP2EN',0,1
	.word	659
	.byte	1,5,2,35,3,11
	.byte	'CAP3EN',0,1
	.word	659
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	659
	.byte	4,0,2,35,3,0,29
	.byte	'Ifx_SCU_OSCCON_Bits',0,4,209,5,3
	.word	36968
	.byte	10
	.byte	'_Ifx_SCU_OUT_Bits',0,4,212,5,16,4,11
	.byte	'P0',0,1
	.word	659
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	659
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	484
	.byte	30,0,2,35,0,0,29
	.byte	'Ifx_SCU_OUT_Bits',0,4,217,5,3
	.word	37380
	.byte	10
	.byte	'_Ifx_SCU_OVCCON_Bits',0,4,220,5,16,4,11
	.byte	'CSEL0',0,1
	.word	659
	.byte	1,7,2,35,0,11
	.byte	'CSEL1',0,1
	.word	659
	.byte	1,6,2,35,0,11
	.byte	'CSEL2',0,1
	.word	659
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,2
	.word	676
	.byte	13,0,2,35,0,11
	.byte	'OVSTRT',0,1
	.word	659
	.byte	1,7,2,35,2,11
	.byte	'OVSTP',0,1
	.word	659
	.byte	1,6,2,35,2,11
	.byte	'DCINVAL',0,1
	.word	659
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	659
	.byte	5,0,2,35,2,11
	.byte	'OVCONF',0,1
	.word	659
	.byte	1,7,2,35,3,11
	.byte	'POVCONF',0,1
	.word	659
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	659
	.byte	6,0,2,35,3,0,29
	.byte	'Ifx_SCU_OVCCON_Bits',0,4,233,5,3
	.word	37481
	.byte	10
	.byte	'_Ifx_SCU_OVCENABLE_Bits',0,4,236,5,16,4,11
	.byte	'OVEN0',0,1
	.word	659
	.byte	1,7,2,35,0,11
	.byte	'OVEN1',0,1
	.word	659
	.byte	1,6,2,35,0,11
	.byte	'OVEN2',0,1
	.word	659
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	484
	.byte	29,0,2,35,0,0,29
	.byte	'Ifx_SCU_OVCENABLE_Bits',0,4,242,5,3
	.word	37748
	.byte	10
	.byte	'_Ifx_SCU_PDISC_Bits',0,4,245,5,16,4,11
	.byte	'PDIS0',0,1
	.word	659
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	659
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	484
	.byte	30,0,2,35,0,0,29
	.byte	'Ifx_SCU_PDISC_Bits',0,4,250,5,3
	.word	37884
	.byte	10
	.byte	'_Ifx_SCU_PDR_Bits',0,4,253,5,16,4,11
	.byte	'PD0',0,1
	.word	659
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	659
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	659
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	659
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	484
	.byte	24,0,2,35,0,0,29
	.byte	'Ifx_SCU_PDR_Bits',0,4,132,6,3
	.word	37995
	.byte	10
	.byte	'_Ifx_SCU_PDRR_Bits',0,4,135,6,16,4,11
	.byte	'PDR0',0,1
	.word	659
	.byte	1,7,2,35,0,11
	.byte	'PDR1',0,1
	.word	659
	.byte	1,6,2,35,0,11
	.byte	'PDR2',0,1
	.word	659
	.byte	1,5,2,35,0,11
	.byte	'PDR3',0,1
	.word	659
	.byte	1,4,2,35,0,11
	.byte	'PDR4',0,1
	.word	659
	.byte	1,3,2,35,0,11
	.byte	'PDR5',0,1
	.word	659
	.byte	1,2,2,35,0,11
	.byte	'PDR6',0,1
	.word	659
	.byte	1,1,2,35,0,11
	.byte	'PDR7',0,1
	.word	659
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	484
	.byte	24,0,2,35,0,0,29
	.byte	'Ifx_SCU_PDRR_Bits',0,4,146,6,3
	.word	38128
	.byte	10
	.byte	'_Ifx_SCU_PLLCON0_Bits',0,4,149,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	659
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	659
	.byte	1,6,2,35,0,11
	.byte	'MODEN',0,1
	.word	659
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	659
	.byte	1,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	659
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	659
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	659
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	676
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	659
	.byte	7,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	659
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	659
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	659
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	659
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	659
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	659
	.byte	4,0,2,35,3,0,29
	.byte	'Ifx_SCU_PLLCON0_Bits',0,4,166,6,3
	.word	38331
	.byte	10
	.byte	'_Ifx_SCU_PLLCON1_Bits',0,4,169,6,16,4,11
	.byte	'K2DIV',0,1
	.word	659
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	659
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	659
	.byte	7,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	659
	.byte	1,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	659
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	676
	.byte	9,0,2,35,2,0,29
	.byte	'Ifx_SCU_PLLCON1_Bits',0,4,177,6,3
	.word	38687
	.byte	10
	.byte	'_Ifx_SCU_PLLCON2_Bits',0,4,180,6,16,4,11
	.byte	'MODCFG',0,2
	.word	676
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	676
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_SCU_PLLCON2_Bits',0,4,184,6,3
	.word	38865
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON0_Bits',0,4,187,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	659
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	659
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	659
	.byte	2,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	659
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	659
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	659
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	676
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	659
	.byte	5,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	659
	.byte	2,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	659
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	659
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	659
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	659
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	659
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	659
	.byte	4,0,2,35,3,0,29
	.byte	'Ifx_SCU_PLLERAYCON0_Bits',0,4,204,6,3
	.word	38965
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON1_Bits',0,4,207,6,16,4,11
	.byte	'K2DIV',0,1
	.word	659
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	659
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	659
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	659
	.byte	4,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	659
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	676
	.byte	9,0,2,35,2,0,29
	.byte	'Ifx_SCU_PLLERAYCON1_Bits',0,4,215,6,3
	.word	39335
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYSTAT_Bits',0,4,218,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	659
	.byte	1,7,2,35,0,11
	.byte	'PWDSTAT',0,1
	.word	659
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	659
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	659
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	659
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	659
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	484
	.byte	26,0,2,35,0,0,29
	.byte	'Ifx_SCU_PLLERAYSTAT_Bits',0,4,227,6,3
	.word	39521
	.byte	10
	.byte	'_Ifx_SCU_PLLSTAT_Bits',0,4,230,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	659
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	659
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	659
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	659
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	659
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	659
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	659
	.byte	1,1,2,35,0,11
	.byte	'MODRUN',0,1
	.word	659
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	484
	.byte	24,0,2,35,0,0,29
	.byte	'Ifx_SCU_PLLSTAT_Bits',0,4,241,6,3
	.word	39719
	.byte	10
	.byte	'_Ifx_SCU_PMCSR_Bits',0,4,244,6,16,4,11
	.byte	'REQSLP',0,1
	.word	659
	.byte	2,6,2,35,0,11
	.byte	'SMUSLP',0,1
	.word	659
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	659
	.byte	5,0,2,35,0,11
	.byte	'PMST',0,1
	.word	659
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	484
	.byte	21,0,2,35,0,0,29
	.byte	'Ifx_SCU_PMCSR_Bits',0,4,251,6,3
	.word	39952
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR0_Bits',0,4,254,6,16,4,11
	.byte	'reserved_0',0,1
	.word	659
	.byte	1,7,2,35,0,11
	.byte	'ESR1WKEN',0,1
	.word	659
	.byte	1,6,2,35,0,11
	.byte	'PINAWKEN',0,1
	.word	659
	.byte	1,5,2,35,0,11
	.byte	'PINBWKEN',0,1
	.word	659
	.byte	1,4,2,35,0,11
	.byte	'ESR0DFEN',0,1
	.word	659
	.byte	1,3,2,35,0,11
	.byte	'ESR0EDCON',0,1
	.word	659
	.byte	2,1,2,35,0,11
	.byte	'ESR1DFEN',0,1
	.word	659
	.byte	1,0,2,35,0,11
	.byte	'ESR1EDCON',0,1
	.word	659
	.byte	2,6,2,35,1,11
	.byte	'PINADFEN',0,1
	.word	659
	.byte	1,5,2,35,1,11
	.byte	'PINAEDCON',0,1
	.word	659
	.byte	2,3,2,35,1,11
	.byte	'PINBDFEN',0,1
	.word	659
	.byte	1,2,2,35,1,11
	.byte	'PINBEDCON',0,1
	.word	659
	.byte	2,0,2,35,1,11
	.byte	'SCREN',0,1
	.word	659
	.byte	1,7,2,35,2,11
	.byte	'STBYRAMSEL',0,1
	.word	659
	.byte	2,5,2,35,2,11
	.byte	'SCRCLKSEL',0,1
	.word	659
	.byte	1,4,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	659
	.byte	1,3,2,35,2,11
	.byte	'TRISTEN',0,1
	.word	659
	.byte	1,2,2,35,2,11
	.byte	'TRISTREQ',0,1
	.word	659
	.byte	1,1,2,35,2,11
	.byte	'PORSTDF',0,1
	.word	659
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	659
	.byte	1,7,2,35,3,11
	.byte	'DCDCSYNC',0,1
	.word	659
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	659
	.byte	3,3,2,35,3,11
	.byte	'ESR0TRIST',0,1
	.word	659
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	659
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	659
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_PMSWCR0_Bits',0,4,153,7,3
	.word	40104
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR1_Bits',0,4,156,7,16,4,11
	.byte	'SCRSTEN',0,1
	.word	659
	.byte	1,7,2,35,0,11
	.byte	'SCRSTREQ',0,1
	.word	659
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	659
	.byte	6,0,2,35,0,11
	.byte	'CPUIDLSEL',0,1
	.word	659
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	659
	.byte	1,4,2,35,1,11
	.byte	'IRADIS',0,1
	.word	659
	.byte	1,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	659
	.byte	3,0,2,35,1,11
	.byte	'SCRCFG',0,1
	.word	659
	.byte	8,0,2,35,2,11
	.byte	'CPUSEL',0,1
	.word	659
	.byte	3,5,2,35,3,11
	.byte	'STBYEVEN',0,1
	.word	659
	.byte	1,4,2,35,3,11
	.byte	'STBYEV',0,1
	.word	659
	.byte	3,1,2,35,3,11
	.byte	'LCK',0,1
	.word	659
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_PMSWCR1_Bits',0,4,170,7,3
	.word	40671
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR2_Bits',0,4,173,7,16,4,11
	.byte	'SCRINT',0,1
	.word	659
	.byte	8,0,2,35,0,11
	.byte	'BUSY',0,1
	.word	659
	.byte	1,7,2,35,1,11
	.byte	'SCRECC',0,1
	.word	659
	.byte	1,6,2,35,1,11
	.byte	'SCRWDT',0,1
	.word	659
	.byte	1,5,2,35,1,11
	.byte	'SCRRST',0,1
	.word	659
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	659
	.byte	4,0,2,35,1,11
	.byte	'TCINT',0,1
	.word	659
	.byte	8,0,2,35,2,11
	.byte	'TCINTREQ',0,1
	.word	659
	.byte	1,7,2,35,3,11
	.byte	'SMURST',0,1
	.word	659
	.byte	1,6,2,35,3,11
	.byte	'RST',0,1
	.word	659
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	659
	.byte	4,1,2,35,3,11
	.byte	'LCK',0,1
	.word	659
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_PMSWCR2_Bits',0,4,187,7,3
	.word	40965
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTAT_Bits',0,4,190,7,16,4,11
	.byte	'reserved_0',0,1
	.word	659
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKP',0,1
	.word	659
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUN',0,1
	.word	659
	.byte	1,4,2,35,0,11
	.byte	'PINAWKP',0,1
	.word	659
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUN',0,1
	.word	659
	.byte	1,2,2,35,0,11
	.byte	'PINBWKP',0,1
	.word	659
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUN',0,1
	.word	659
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	659
	.byte	1,7,2,35,1,11
	.byte	'PORSTDF',0,1
	.word	659
	.byte	1,6,2,35,1,11
	.byte	'HWCFGEVR',0,1
	.word	659
	.byte	3,3,2,35,1,11
	.byte	'STBYRAM',0,1
	.word	659
	.byte	2,1,2,35,1,11
	.byte	'TRIST',0,1
	.word	659
	.byte	1,0,2,35,1,11
	.byte	'SCRST',0,1
	.word	659
	.byte	1,7,2,35,2,11
	.byte	'SCRWKP',0,1
	.word	659
	.byte	1,6,2,35,2,11
	.byte	'SCR',0,1
	.word	659
	.byte	1,5,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	659
	.byte	1,4,2,35,2,11
	.byte	'ESR1WKEN',0,1
	.word	659
	.byte	1,3,2,35,2,11
	.byte	'PINAWKEN',0,1
	.word	659
	.byte	1,2,2,35,2,11
	.byte	'PINBWKEN',0,1
	.word	659
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	676
	.byte	4,5,2,35,2,11
	.byte	'ESR0TRIST',0,1
	.word	659
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	659
	.byte	4,0,2,35,3,0,29
	.byte	'Ifx_SCU_PMSWSTAT_Bits',0,4,214,7,3
	.word	41243
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTATCLR_Bits',0,4,217,7,16,4,11
	.byte	'reserved_0',0,1
	.word	659
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKPCLR',0,1
	.word	659
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUNCLR',0,1
	.word	659
	.byte	1,4,2,35,0,11
	.byte	'PINAWKPCLR',0,1
	.word	659
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUNCLR',0,1
	.word	659
	.byte	1,2,2,35,0,11
	.byte	'PINBWKPCLR',0,1
	.word	659
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUNCLR',0,1
	.word	659
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	659
	.byte	8,0,2,35,1,11
	.byte	'SCRSTCLR',0,1
	.word	659
	.byte	1,7,2,35,2,11
	.byte	'SCRWKPCLR',0,1
	.word	659
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	676
	.byte	14,0,2,35,2,0,29
	.byte	'Ifx_SCU_PMSWSTATCLR_Bits',0,4,230,7,3
	.word	41739
	.byte	10
	.byte	'_Ifx_SCU_RSTCON2_Bits',0,4,233,7,16,4,11
	.byte	'reserved_0',0,1
	.word	659
	.byte	1,7,2,35,0,11
	.byte	'CLRC',0,1
	.word	659
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	676
	.byte	10,4,2,35,0,11
	.byte	'CSS0',0,1
	.word	659
	.byte	1,3,2,35,1,11
	.byte	'CSS1',0,1
	.word	659
	.byte	1,2,2,35,1,11
	.byte	'CSS2',0,1
	.word	659
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	659
	.byte	1,0,2,35,1,11
	.byte	'USRINFO',0,2
	.word	676
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_SCU_RSTCON2_Bits',0,4,243,7,3
	.word	42052
	.byte	10
	.byte	'_Ifx_SCU_RSTCON_Bits',0,4,246,7,16,4,11
	.byte	'ESR0',0,1
	.word	659
	.byte	2,6,2,35,0,11
	.byte	'ESR1',0,1
	.word	659
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	659
	.byte	2,2,2,35,0,11
	.byte	'SMU',0,1
	.word	659
	.byte	2,0,2,35,0,11
	.byte	'SW',0,1
	.word	659
	.byte	2,6,2,35,1,11
	.byte	'STM0',0,1
	.word	659
	.byte	2,4,2,35,1,11
	.byte	'STM1',0,1
	.word	659
	.byte	2,2,2,35,1,11
	.byte	'STM2',0,1
	.word	659
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	676
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_SCU_RSTCON_Bits',0,4,129,8,3
	.word	42261
	.byte	10
	.byte	'_Ifx_SCU_RSTSTAT_Bits',0,4,132,8,16,4,11
	.byte	'ESR0',0,1
	.word	659
	.byte	1,7,2,35,0,11
	.byte	'ESR1',0,1
	.word	659
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	659
	.byte	1,5,2,35,0,11
	.byte	'SMU',0,1
	.word	659
	.byte	1,4,2,35,0,11
	.byte	'SW',0,1
	.word	659
	.byte	1,3,2,35,0,11
	.byte	'STM0',0,1
	.word	659
	.byte	1,2,2,35,0,11
	.byte	'STM1',0,1
	.word	659
	.byte	1,1,2,35,0,11
	.byte	'STM2',0,1
	.word	659
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	659
	.byte	8,0,2,35,1,11
	.byte	'PORST',0,1
	.word	659
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	659
	.byte	1,6,2,35,2,11
	.byte	'CB0',0,1
	.word	659
	.byte	1,5,2,35,2,11
	.byte	'CB1',0,1
	.word	659
	.byte	1,4,2,35,2,11
	.byte	'CB3',0,1
	.word	659
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	659
	.byte	2,1,2,35,2,11
	.byte	'EVR13',0,1
	.word	659
	.byte	1,0,2,35,2,11
	.byte	'EVR33',0,1
	.word	659
	.byte	1,7,2,35,3,11
	.byte	'SWD',0,1
	.word	659
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	659
	.byte	2,4,2,35,3,11
	.byte	'STBYR',0,1
	.word	659
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	659
	.byte	3,0,2,35,3,0,29
	.byte	'Ifx_SCU_RSTSTAT_Bits',0,4,155,8,3
	.word	42472
	.byte	10
	.byte	'_Ifx_SCU_SAFECON_Bits',0,4,158,8,16,4,11
	.byte	'HBT',0,1
	.word	659
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	484
	.byte	31,0,2,35,0,0,29
	.byte	'Ifx_SCU_SAFECON_Bits',0,4,162,8,3
	.word	42904
	.byte	10
	.byte	'_Ifx_SCU_STSTAT_Bits',0,4,165,8,16,4,11
	.byte	'HWCFG',0,1
	.word	659
	.byte	8,0,2,35,0,11
	.byte	'FTM',0,1
	.word	659
	.byte	7,1,2,35,1,11
	.byte	'MODE',0,1
	.word	659
	.byte	1,0,2,35,1,11
	.byte	'FCBAE',0,1
	.word	659
	.byte	1,7,2,35,2,11
	.byte	'LUDIS',0,1
	.word	659
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	659
	.byte	1,5,2,35,2,11
	.byte	'TRSTL',0,1
	.word	659
	.byte	1,4,2,35,2,11
	.byte	'SPDEN',0,1
	.word	659
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	659
	.byte	3,0,2,35,2,11
	.byte	'RAMINT',0,1
	.word	659
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	659
	.byte	7,0,2,35,3,0,29
	.byte	'Ifx_SCU_STSTAT_Bits',0,4,178,8,3
	.word	43000
	.byte	10
	.byte	'_Ifx_SCU_SWRSTCON_Bits',0,4,181,8,16,4,11
	.byte	'reserved_0',0,1
	.word	659
	.byte	1,7,2,35,0,11
	.byte	'SWRSTREQ',0,1
	.word	659
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	484
	.byte	30,0,2,35,0,0,29
	.byte	'Ifx_SCU_SWRSTCON_Bits',0,4,186,8,3
	.word	43260
	.byte	10
	.byte	'_Ifx_SCU_SYSCON_Bits',0,4,189,8,16,4,11
	.byte	'CCTRIG0',0,1
	.word	659
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	659
	.byte	1,6,2,35,0,11
	.byte	'RAMINTM',0,1
	.word	659
	.byte	2,4,2,35,0,11
	.byte	'SETLUDIS',0,1
	.word	659
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	659
	.byte	3,0,2,35,0,11
	.byte	'DATM',0,1
	.word	659
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,4
	.word	484
	.byte	23,0,2,35,0,0,29
	.byte	'Ifx_SCU_SYSCON_Bits',0,4,198,8,3
	.word	43385
	.byte	10
	.byte	'_Ifx_SCU_TRAPCLR_Bits',0,4,201,8,16,4,11
	.byte	'ESR0T',0,1
	.word	659
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	659
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	659
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	659
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	484
	.byte	28,0,2,35,0,0,29
	.byte	'Ifx_SCU_TRAPCLR_Bits',0,4,208,8,3
	.word	43582
	.byte	10
	.byte	'_Ifx_SCU_TRAPDIS_Bits',0,4,211,8,16,4,11
	.byte	'ESR0T',0,1
	.word	659
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	659
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	659
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	659
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	484
	.byte	28,0,2,35,0,0,29
	.byte	'Ifx_SCU_TRAPDIS_Bits',0,4,218,8,3
	.word	43735
	.byte	10
	.byte	'_Ifx_SCU_TRAPSET_Bits',0,4,221,8,16,4,11
	.byte	'ESR0T',0,1
	.word	659
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	659
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	659
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	659
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	484
	.byte	28,0,2,35,0,0,29
	.byte	'Ifx_SCU_TRAPSET_Bits',0,4,228,8,3
	.word	43888
	.byte	10
	.byte	'_Ifx_SCU_TRAPSTAT_Bits',0,4,231,8,16,4,11
	.byte	'ESR0T',0,1
	.word	659
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	659
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	659
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	659
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	484
	.byte	28,0,2,35,0,0,29
	.byte	'Ifx_SCU_TRAPSTAT_Bits',0,4,238,8,3
	.word	44041
	.byte	29
	.byte	'Ifx_SCU_WDTCPU_CON0_Bits',0,4,247,8,3
	.word	523
	.byte	29
	.byte	'Ifx_SCU_WDTCPU_CON1_Bits',0,4,134,9,3
	.word	698
	.byte	29
	.byte	'Ifx_SCU_WDTCPU_SR_Bits',0,4,150,9,3
	.word	942
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON0_Bits',0,4,153,9,16,4,11
	.byte	'ENDINIT',0,4
	.word	507
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	507
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	507
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	507
	.byte	16,0,2,35,0,0,29
	.byte	'Ifx_SCU_WDTS_CON0_Bits',0,4,159,9,3
	.word	44296
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON1_Bits',0,4,162,9,16,4,11
	.byte	'CLRIRF',0,1
	.word	659
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	659
	.byte	1,6,2,35,0,11
	.byte	'IR0',0,1
	.word	659
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	659
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	659
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	659
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	659
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	659
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	659
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	659
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	676
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_SCU_WDTS_CON1_Bits',0,4,175,9,3
	.word	44422
	.byte	10
	.byte	'_Ifx_SCU_WDTS_SR_Bits',0,4,178,9,16,4,11
	.byte	'AE',0,1
	.word	659
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	659
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	659
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	659
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	659
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	659
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	659
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	659
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	659
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	659
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	676
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_SCU_WDTS_SR_Bits',0,4,191,9,3
	.word	44674
	.byte	12,4,199,9,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26706
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_ACCEN0',0,4,204,9,3
	.word	44893
	.byte	12,4,207,9,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27263
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_ACCEN1',0,4,212,9,3
	.word	44957
	.byte	12,4,215,9,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27340
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_ARSTDIS',0,4,220,9,3
	.word	45021
	.byte	12,4,223,9,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27476
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_CCUCON0',0,4,228,9,3
	.word	45086
	.byte	12,4,231,9,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27756
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_CCUCON1',0,4,236,9,3
	.word	45151
	.byte	12,4,239,9,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27994
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_CCUCON2',0,4,244,9,3
	.word	45216
	.byte	12,4,247,9,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28122
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_CCUCON3',0,4,252,9,3
	.word	45281
	.byte	12,4,255,9,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28365
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_CCUCON4',0,4,132,10,3
	.word	45346
	.byte	12,4,135,10,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28600
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_CCUCON5',0,4,140,10,3
	.word	45411
	.byte	12,4,143,10,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28728
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_CCUCON6',0,4,148,10,3
	.word	45476
	.byte	12,4,151,10,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28828
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_CCUCON7',0,4,156,10,3
	.word	45541
	.byte	12,4,159,10,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28928
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_CHIPID',0,4,164,10,3
	.word	45606
	.byte	12,4,167,10,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29136
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_DTSCON',0,4,172,10,3
	.word	45670
	.byte	12,4,175,10,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29301
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_DTSLIM',0,4,180,10,3
	.word	45734
	.byte	12,4,183,10,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29484
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_DTSSTAT',0,4,188,10,3
	.word	45798
	.byte	12,4,191,10,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29638
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EICR',0,4,196,10,3
	.word	45863
	.byte	12,4,199,10,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30002
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EIFR',0,4,204,10,3
	.word	45925
	.byte	12,4,207,10,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30213
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EMSR',0,4,212,10,3
	.word	45987
	.byte	12,4,215,10,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30465
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_ESRCFG',0,4,220,10,3
	.word	46049
	.byte	12,4,223,10,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30583
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_ESROCFG',0,4,228,10,3
	.word	46113
	.byte	12,4,231,10,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30694
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVR13CON',0,4,236,10,3
	.word	46178
	.byte	12,4,239,10,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30857
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVR33CON',0,4,244,10,3
	.word	46244
	.byte	12,4,247,10,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31020
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRADCSTAT',0,4,252,10,3
	.word	46310
	.byte	12,4,255,10,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31178
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRDVSTAT',0,4,132,11,3
	.word	46378
	.byte	12,4,135,11,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31343
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRMONCTRL',0,4,140,11,3
	.word	46445
	.byte	12,4,143,11,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31672
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVROSCCTRL',0,4,148,11,3
	.word	46513
	.byte	12,4,151,11,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31893
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVROVMON',0,4,156,11,3
	.word	46581
	.byte	12,4,159,11,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32056
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRRSTCON',0,4,164,11,3
	.word	46647
	.byte	12,4,167,11,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32328
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF1',0,4,172,11,3
	.word	46714
	.byte	12,4,175,11,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32481
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF2',0,4,180,11,3
	.word	46783
	.byte	12,4,183,11,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32637
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF3',0,4,188,11,3
	.word	46852
	.byte	12,4,191,11,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32799
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF4',0,4,196,11,3
	.word	46921
	.byte	12,4,199,11,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32942
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF5',0,4,204,11,3
	.word	46990
	.byte	12,4,207,11,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33107
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF6',0,4,212,11,3
	.word	47059
	.byte	12,4,215,11,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33252
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSDCTRL1',0,4,220,11,3
	.word	47128
	.byte	12,4,223,11,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33433
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSDCTRL2',0,4,228,11,3
	.word	47196
	.byte	12,4,231,11,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33607
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSDCTRL3',0,4,236,11,3
	.word	47264
	.byte	12,4,239,11,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33767
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSDCTRL4',0,4,244,11,3
	.word	47332
	.byte	12,4,247,11,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33911
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSTAT',0,4,252,11,3
	.word	47400
	.byte	12,4,255,11,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34185
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRTRIM',0,4,132,12,3
	.word	47465
	.byte	12,4,135,12,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34324
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRUVMON',0,4,140,12,3
	.word	47530
	.byte	12,4,143,12,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34487
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EXTCON',0,4,148,12,3
	.word	47596
	.byte	12,4,151,12,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34705
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_FDR',0,4,156,12,3
	.word	47660
	.byte	12,4,159,12,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34868
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_FMR',0,4,164,12,3
	.word	47721
	.byte	12,4,167,12,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35204
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_ID',0,4,172,12,3
	.word	47782
	.byte	12,4,175,12,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35311
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_IGCR',0,4,180,12,3
	.word	47842
	.byte	12,4,183,12,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35763
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_IN',0,4,188,12,3
	.word	47904
	.byte	12,4,191,12,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35862
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_IOCR',0,4,196,12,3
	.word	47964
	.byte	12,4,199,12,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36012
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_LBISTCTRL0',0,4,204,12,3
	.word	48026
	.byte	12,4,207,12,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36161
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_LBISTCTRL1',0,4,212,12,3
	.word	48094
	.byte	12,4,215,12,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36322
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_LBISTCTRL2',0,4,220,12,3
	.word	48162
	.byte	12,4,223,12,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36452
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_LCLCON',0,4,228,12,3
	.word	48230
	.byte	12,4,231,12,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36584
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_LCLTEST',0,4,236,12,3
	.word	48294
	.byte	12,4,239,12,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36699
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_MANID',0,4,244,12,3
	.word	48359
	.byte	12,4,247,12,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36810
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_OMR',0,4,252,12,3
	.word	48422
	.byte	12,4,255,12,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36968
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_OSCCON',0,4,132,13,3
	.word	48483
	.byte	12,4,135,13,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37380
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_OUT',0,4,140,13,3
	.word	48547
	.byte	12,4,143,13,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37481
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_OVCCON',0,4,148,13,3
	.word	48608
	.byte	12,4,151,13,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37748
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_OVCENABLE',0,4,156,13,3
	.word	48672
	.byte	12,4,159,13,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37884
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PDISC',0,4,164,13,3
	.word	48739
	.byte	12,4,167,13,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37995
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PDR',0,4,172,13,3
	.word	48802
	.byte	12,4,175,13,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38128
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PDRR',0,4,180,13,3
	.word	48863
	.byte	12,4,183,13,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38331
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PLLCON0',0,4,188,13,3
	.word	48925
	.byte	12,4,191,13,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38687
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PLLCON1',0,4,196,13,3
	.word	48990
	.byte	12,4,199,13,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38865
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PLLCON2',0,4,204,13,3
	.word	49055
	.byte	12,4,207,13,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38965
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PLLERAYCON0',0,4,212,13,3
	.word	49120
	.byte	12,4,215,13,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39335
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PLLERAYCON1',0,4,220,13,3
	.word	49189
	.byte	12,4,223,13,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39521
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PLLERAYSTAT',0,4,228,13,3
	.word	49258
	.byte	12,4,231,13,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39719
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PLLSTAT',0,4,236,13,3
	.word	49327
	.byte	12,4,239,13,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39952
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PMCSR',0,4,244,13,3
	.word	49392
	.byte	12,4,247,13,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40104
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PMSWCR0',0,4,252,13,3
	.word	49455
	.byte	12,4,255,13,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40671
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PMSWCR1',0,4,132,14,3
	.word	49520
	.byte	12,4,135,14,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40965
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PMSWCR2',0,4,140,14,3
	.word	49585
	.byte	12,4,143,14,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41243
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PMSWSTAT',0,4,148,14,3
	.word	49650
	.byte	12,4,151,14,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41739
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PMSWSTATCLR',0,4,156,14,3
	.word	49716
	.byte	12,4,159,14,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42261
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_RSTCON',0,4,164,14,3
	.word	49785
	.byte	12,4,167,14,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42052
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_RSTCON2',0,4,172,14,3
	.word	49849
	.byte	12,4,175,14,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42472
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_RSTSTAT',0,4,180,14,3
	.word	49914
	.byte	12,4,183,14,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42904
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_SAFECON',0,4,188,14,3
	.word	49979
	.byte	12,4,191,14,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43000
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_STSTAT',0,4,196,14,3
	.word	50044
	.byte	12,4,199,14,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43260
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_SWRSTCON',0,4,204,14,3
	.word	50108
	.byte	12,4,207,14,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43385
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_SYSCON',0,4,212,14,3
	.word	50174
	.byte	12,4,215,14,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43582
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_TRAPCLR',0,4,220,14,3
	.word	50238
	.byte	12,4,223,14,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43735
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_TRAPDIS',0,4,228,14,3
	.word	50303
	.byte	12,4,231,14,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43888
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_TRAPSET',0,4,236,14,3
	.word	50368
	.byte	12,4,239,14,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44041
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_TRAPSTAT',0,4,244,14,3
	.word	50433
	.byte	29
	.byte	'Ifx_SCU_WDTCPU_CON0',0,4,252,14,3
	.word	619
	.byte	29
	.byte	'Ifx_SCU_WDTCPU_CON1',0,4,132,15,3
	.word	902
	.byte	29
	.byte	'Ifx_SCU_WDTCPU_SR',0,4,140,15,3
	.word	1133
	.byte	12,4,143,15,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44296
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_WDTS_CON0',0,4,148,15,3
	.word	50584
	.byte	12,4,151,15,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44422
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_WDTS_CON1',0,4,156,15,3
	.word	50651
	.byte	12,4,159,15,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44674
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_WDTS_SR',0,4,164,15,3
	.word	50718
	.byte	14
	.word	1173
	.byte	29
	.byte	'Ifx_SCU_WDTCPU',0,4,180,15,3
	.word	50783
	.byte	10
	.byte	'_Ifx_SCU_WDTS',0,4,183,15,25,12,13
	.byte	'CON0',0
	.word	50584
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	50651
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	50718
	.byte	4,2,35,8,0,14
	.word	50812
	.byte	29
	.byte	'Ifx_SCU_WDTS',0,4,188,15,3
	.word	50873
	.byte	18,8
	.word	46049
	.byte	19,1,0,18,20
	.word	659
	.byte	19,19,0,18,8
	.word	49392
	.byte	19,1,0,14
	.word	50812
	.byte	18,24
	.word	1173
	.byte	19,1,0,14
	.word	50932
	.byte	18,16
	.word	659
	.byte	19,15,0,18,28
	.word	659
	.byte	19,27,0,18,40
	.word	659
	.byte	19,39,0,18,16
	.word	45863
	.byte	19,3,0,18,16
	.word	47842
	.byte	19,3,0,18,180,3
	.word	659
	.byte	19,179,3,0,10
	.byte	'_Ifx_SCU',0,4,201,15,25,128,8,13
	.byte	'reserved_0',0
	.word	4849
	.byte	8,2,35,0,13
	.byte	'ID',0
	.word	47782
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	3030
	.byte	4,2,35,12,13
	.byte	'OSCCON',0
	.word	48483
	.byte	4,2,35,16,13
	.byte	'PLLSTAT',0
	.word	49327
	.byte	4,2,35,20,13
	.byte	'PLLCON0',0
	.word	48925
	.byte	4,2,35,24,13
	.byte	'PLLCON1',0
	.word	48990
	.byte	4,2,35,28,13
	.byte	'PLLCON2',0
	.word	49055
	.byte	4,2,35,32,13
	.byte	'PLLERAYSTAT',0
	.word	49258
	.byte	4,2,35,36,13
	.byte	'PLLERAYCON0',0
	.word	49120
	.byte	4,2,35,40,13
	.byte	'PLLERAYCON1',0
	.word	49189
	.byte	4,2,35,44,13
	.byte	'CCUCON0',0
	.word	45086
	.byte	4,2,35,48,13
	.byte	'CCUCON1',0
	.word	45151
	.byte	4,2,35,52,13
	.byte	'FDR',0
	.word	47660
	.byte	4,2,35,56,13
	.byte	'EXTCON',0
	.word	47596
	.byte	4,2,35,60,13
	.byte	'CCUCON2',0
	.word	45216
	.byte	4,2,35,64,13
	.byte	'CCUCON3',0
	.word	45281
	.byte	4,2,35,68,13
	.byte	'CCUCON4',0
	.word	45346
	.byte	4,2,35,72,13
	.byte	'CCUCON5',0
	.word	45411
	.byte	4,2,35,76,13
	.byte	'RSTSTAT',0
	.word	49914
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	3030
	.byte	4,2,35,84,13
	.byte	'RSTCON',0
	.word	49785
	.byte	4,2,35,88,13
	.byte	'ARSTDIS',0
	.word	45021
	.byte	4,2,35,92,13
	.byte	'SWRSTCON',0
	.word	50108
	.byte	4,2,35,96,13
	.byte	'RSTCON2',0
	.word	49849
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	3030
	.byte	4,2,35,104,13
	.byte	'EVRRSTCON',0
	.word	46647
	.byte	4,2,35,108,13
	.byte	'ESRCFG',0
	.word	50900
	.byte	8,2,35,112,13
	.byte	'ESROCFG',0
	.word	46113
	.byte	4,2,35,120,13
	.byte	'SYSCON',0
	.word	50174
	.byte	4,2,35,124,13
	.byte	'CCUCON6',0
	.word	45476
	.byte	4,3,35,128,1,13
	.byte	'CCUCON7',0
	.word	45541
	.byte	4,3,35,132,1,13
	.byte	'reserved_88',0
	.word	50909
	.byte	20,3,35,136,1,13
	.byte	'PDR',0
	.word	48802
	.byte	4,3,35,156,1,13
	.byte	'IOCR',0
	.word	47964
	.byte	4,3,35,160,1,13
	.byte	'OUT',0
	.word	48547
	.byte	4,3,35,164,1,13
	.byte	'OMR',0
	.word	48422
	.byte	4,3,35,168,1,13
	.byte	'IN',0
	.word	47904
	.byte	4,3,35,172,1,13
	.byte	'EVRSTAT',0
	.word	47400
	.byte	4,3,35,176,1,13
	.byte	'EVRDVSTAT',0
	.word	46378
	.byte	4,3,35,180,1,13
	.byte	'EVR13CON',0
	.word	46178
	.byte	4,3,35,184,1,13
	.byte	'EVR33CON',0
	.word	46244
	.byte	4,3,35,188,1,13
	.byte	'STSTAT',0
	.word	50044
	.byte	4,3,35,192,1,13
	.byte	'reserved_C4',0
	.word	3030
	.byte	4,3,35,196,1,13
	.byte	'PMSWCR0',0
	.word	49455
	.byte	4,3,35,200,1,13
	.byte	'PMSWSTAT',0
	.word	49650
	.byte	4,3,35,204,1,13
	.byte	'PMSWSTATCLR',0
	.word	49716
	.byte	4,3,35,208,1,13
	.byte	'PMCSR',0
	.word	50918
	.byte	8,3,35,212,1,13
	.byte	'reserved_DC',0
	.word	3030
	.byte	4,3,35,220,1,13
	.byte	'DTSSTAT',0
	.word	45798
	.byte	4,3,35,224,1,13
	.byte	'DTSCON',0
	.word	45670
	.byte	4,3,35,228,1,13
	.byte	'PMSWCR1',0
	.word	49520
	.byte	4,3,35,232,1,13
	.byte	'PMSWCR2',0
	.word	49585
	.byte	4,3,35,236,1,13
	.byte	'WDTS',0
	.word	50927
	.byte	12,3,35,240,1,13
	.byte	'EMSR',0
	.word	45987
	.byte	4,3,35,252,1,13
	.byte	'WDTCPU',0
	.word	50941
	.byte	24,3,35,128,2,13
	.byte	'reserved_118',0
	.word	5189
	.byte	12,3,35,152,2,13
	.byte	'TRAPSTAT',0
	.word	50433
	.byte	4,3,35,164,2,13
	.byte	'TRAPSET',0
	.word	50368
	.byte	4,3,35,168,2,13
	.byte	'TRAPCLR',0
	.word	50238
	.byte	4,3,35,172,2,13
	.byte	'TRAPDIS',0
	.word	50303
	.byte	4,3,35,176,2,13
	.byte	'reserved_134',0
	.word	3030
	.byte	4,3,35,180,2,13
	.byte	'LCLCON1',0
	.word	48230
	.byte	4,3,35,184,2,13
	.byte	'LCLTEST',0
	.word	48294
	.byte	4,3,35,188,2,13
	.byte	'CHIPID',0
	.word	45606
	.byte	4,3,35,192,2,13
	.byte	'MANID',0
	.word	48359
	.byte	4,3,35,196,2,13
	.byte	'reserved_148',0
	.word	4849
	.byte	8,3,35,200,2,13
	.byte	'SAFECON',0
	.word	49979
	.byte	4,3,35,208,2,13
	.byte	'reserved_154',0
	.word	50946
	.byte	16,3,35,212,2,13
	.byte	'LBISTCTRL0',0
	.word	48026
	.byte	4,3,35,228,2,13
	.byte	'LBISTCTRL1',0
	.word	48094
	.byte	4,3,35,232,2,13
	.byte	'LBISTCTRL2',0
	.word	48162
	.byte	4,3,35,236,2,13
	.byte	'reserved_170',0
	.word	50955
	.byte	28,3,35,240,2,13
	.byte	'PDISC',0
	.word	48739
	.byte	4,3,35,140,3,13
	.byte	'reserved_190',0
	.word	4849
	.byte	8,3,35,144,3,13
	.byte	'EVRTRIM',0
	.word	47465
	.byte	4,3,35,152,3,13
	.byte	'EVRADCSTAT',0
	.word	46310
	.byte	4,3,35,156,3,13
	.byte	'EVRUVMON',0
	.word	47530
	.byte	4,3,35,160,3,13
	.byte	'EVROVMON',0
	.word	46581
	.byte	4,3,35,164,3,13
	.byte	'EVRMONCTRL',0
	.word	46445
	.byte	4,3,35,168,3,13
	.byte	'reserved_1AC',0
	.word	3030
	.byte	4,3,35,172,3,13
	.byte	'EVRSDCTRL1',0
	.word	47128
	.byte	4,3,35,176,3,13
	.byte	'EVRSDCTRL2',0
	.word	47196
	.byte	4,3,35,180,3,13
	.byte	'EVRSDCTRL3',0
	.word	47264
	.byte	4,3,35,184,3,13
	.byte	'EVRSDCTRL4',0
	.word	47332
	.byte	4,3,35,188,3,13
	.byte	'EVRSDCOEFF1',0
	.word	46714
	.byte	4,3,35,192,3,13
	.byte	'EVRSDCOEFF2',0
	.word	46783
	.byte	4,3,35,196,3,13
	.byte	'EVRSDCOEFF3',0
	.word	46852
	.byte	4,3,35,200,3,13
	.byte	'EVRSDCOEFF4',0
	.word	46921
	.byte	4,3,35,204,3,13
	.byte	'EVRSDCOEFF5',0
	.word	46990
	.byte	4,3,35,208,3,13
	.byte	'EVRSDCOEFF6',0
	.word	47059
	.byte	4,3,35,212,3,13
	.byte	'EVROSCCTRL',0
	.word	46513
	.byte	4,3,35,216,3,13
	.byte	'reserved_1DC',0
	.word	3030
	.byte	4,3,35,220,3,13
	.byte	'OVCENABLE',0
	.word	48672
	.byte	4,3,35,224,3,13
	.byte	'OVCCON',0
	.word	48608
	.byte	4,3,35,228,3,13
	.byte	'reserved_1E8',0
	.word	50964
	.byte	40,3,35,232,3,13
	.byte	'EICR',0
	.word	50973
	.byte	16,3,35,144,4,13
	.byte	'EIFR',0
	.word	45925
	.byte	4,3,35,160,4,13
	.byte	'FMR',0
	.word	47721
	.byte	4,3,35,164,4,13
	.byte	'PDRR',0
	.word	48863
	.byte	4,3,35,168,4,13
	.byte	'IGCR',0
	.word	50982
	.byte	16,3,35,172,4,13
	.byte	'reserved_23C',0
	.word	3030
	.byte	4,3,35,188,4,13
	.byte	'DTSLIM',0
	.word	45734
	.byte	4,3,35,192,4,13
	.byte	'reserved_244',0
	.word	50991
	.byte	180,3,3,35,196,4,13
	.byte	'ACCEN1',0
	.word	44957
	.byte	4,3,35,248,7,13
	.byte	'ACCEN0',0
	.word	44893
	.byte	4,3,35,252,7,0,14
	.word	51002
	.byte	29
	.byte	'Ifx_SCU',0,4,181,16,3
	.word	52992
	.byte	10
	.byte	'_Ifx_CPU_A_Bits',0,32,45,16,4,11
	.byte	'ADDR',0,4
	.word	507
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_A_Bits',0,32,48,3
	.word	53014
	.byte	10
	.byte	'_Ifx_CPU_BIV_Bits',0,32,51,16,4,11
	.byte	'VSS',0,4
	.word	507
	.byte	1,31,2,35,0,11
	.byte	'BIV',0,4
	.word	507
	.byte	31,0,2,35,0,0,29
	.byte	'Ifx_CPU_BIV_Bits',0,32,55,3
	.word	53075
	.byte	10
	.byte	'_Ifx_CPU_BTV_Bits',0,32,58,16,4,11
	.byte	'reserved_0',0,4
	.word	507
	.byte	1,31,2,35,0,11
	.byte	'BTV',0,4
	.word	507
	.byte	31,0,2,35,0,0,29
	.byte	'Ifx_CPU_BTV_Bits',0,32,62,3
	.word	53154
	.byte	10
	.byte	'_Ifx_CPU_CCNT_Bits',0,32,65,16,4,11
	.byte	'CountValue',0,4
	.word	507
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	507
	.byte	1,0,2,35,0,0,29
	.byte	'Ifx_CPU_CCNT_Bits',0,32,69,3
	.word	53240
	.byte	10
	.byte	'_Ifx_CPU_CCTRL_Bits',0,32,72,16,4,11
	.byte	'CM',0,4
	.word	507
	.byte	1,31,2,35,0,11
	.byte	'CE',0,4
	.word	507
	.byte	1,30,2,35,0,11
	.byte	'M1',0,4
	.word	507
	.byte	3,27,2,35,0,11
	.byte	'M2',0,4
	.word	507
	.byte	3,24,2,35,0,11
	.byte	'M3',0,4
	.word	507
	.byte	3,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	507
	.byte	21,0,2,35,0,0,29
	.byte	'Ifx_CPU_CCTRL_Bits',0,32,80,3
	.word	53329
	.byte	10
	.byte	'_Ifx_CPU_COMPAT_Bits',0,32,83,16,4,11
	.byte	'reserved_0',0,4
	.word	507
	.byte	3,29,2,35,0,11
	.byte	'RM',0,4
	.word	507
	.byte	1,28,2,35,0,11
	.byte	'SP',0,4
	.word	507
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	507
	.byte	27,0,2,35,0,0,29
	.byte	'Ifx_CPU_COMPAT_Bits',0,32,89,3
	.word	53475
	.byte	10
	.byte	'_Ifx_CPU_CORE_ID_Bits',0,32,92,16,4,11
	.byte	'CORE_ID',0,4
	.word	507
	.byte	3,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	507
	.byte	29,0,2,35,0,0,29
	.byte	'Ifx_CPU_CORE_ID_Bits',0,32,96,3
	.word	53602
	.byte	10
	.byte	'_Ifx_CPU_CPR_L_Bits',0,32,99,16,4,11
	.byte	'reserved_0',0,4
	.word	507
	.byte	3,29,2,35,0,11
	.byte	'LOWBND',0,4
	.word	507
	.byte	29,0,2,35,0,0,29
	.byte	'Ifx_CPU_CPR_L_Bits',0,32,103,3
	.word	53700
	.byte	10
	.byte	'_Ifx_CPU_CPR_U_Bits',0,32,106,16,4,11
	.byte	'reserved_0',0,4
	.word	507
	.byte	3,29,2,35,0,11
	.byte	'UPPBND',0,4
	.word	507
	.byte	29,0,2,35,0,0,29
	.byte	'Ifx_CPU_CPR_U_Bits',0,32,110,3
	.word	53793
	.byte	10
	.byte	'_Ifx_CPU_CPU_ID_Bits',0,32,113,16,4,11
	.byte	'MODREV',0,4
	.word	507
	.byte	8,24,2,35,0,11
	.byte	'MOD_32B',0,4
	.word	507
	.byte	8,16,2,35,0,11
	.byte	'MOD',0,4
	.word	507
	.byte	16,0,2,35,0,0,29
	.byte	'Ifx_CPU_CPU_ID_Bits',0,32,118,3
	.word	53886
	.byte	10
	.byte	'_Ifx_CPU_CPXE_Bits',0,32,121,16,4,11
	.byte	'XE',0,4
	.word	507
	.byte	8,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	507
	.byte	24,0,2,35,0,0,29
	.byte	'Ifx_CPU_CPXE_Bits',0,32,125,3
	.word	53993
	.byte	10
	.byte	'_Ifx_CPU_CREVT_Bits',0,32,128,1,16,4,11
	.byte	'EVTA',0,4
	.word	507
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	507
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	507
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	507
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	507
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	507
	.byte	24,0,2,35,0,0,29
	.byte	'Ifx_CPU_CREVT_Bits',0,32,136,1,3
	.word	54080
	.byte	10
	.byte	'_Ifx_CPU_CUS_ID_Bits',0,32,139,1,16,4,11
	.byte	'CID',0,4
	.word	507
	.byte	3,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	507
	.byte	29,0,2,35,0,0,29
	.byte	'Ifx_CPU_CUS_ID_Bits',0,32,143,1,3
	.word	54234
	.byte	10
	.byte	'_Ifx_CPU_D_Bits',0,32,146,1,16,4,11
	.byte	'DATA',0,4
	.word	507
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_D_Bits',0,32,149,1,3
	.word	54328
	.byte	10
	.byte	'_Ifx_CPU_DATR_Bits',0,32,152,1,16,4,11
	.byte	'reserved_0',0,4
	.word	507
	.byte	3,29,2,35,0,11
	.byte	'SBE',0,4
	.word	507
	.byte	1,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	507
	.byte	5,23,2,35,0,11
	.byte	'CWE',0,4
	.word	507
	.byte	1,22,2,35,0,11
	.byte	'CFE',0,4
	.word	507
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	507
	.byte	3,18,2,35,0,11
	.byte	'SOE',0,4
	.word	507
	.byte	1,17,2,35,0,11
	.byte	'SME',0,4
	.word	507
	.byte	1,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	507
	.byte	16,0,2,35,0,0,29
	.byte	'Ifx_CPU_DATR_Bits',0,32,163,1,3
	.word	54391
	.byte	10
	.byte	'_Ifx_CPU_DBGSR_Bits',0,32,166,1,16,4,11
	.byte	'DE',0,4
	.word	507
	.byte	1,31,2,35,0,11
	.byte	'HALT',0,4
	.word	507
	.byte	2,29,2,35,0,11
	.byte	'SIH',0,4
	.word	507
	.byte	1,28,2,35,0,11
	.byte	'SUSP',0,4
	.word	507
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	507
	.byte	1,26,2,35,0,11
	.byte	'PREVSUSP',0,4
	.word	507
	.byte	1,25,2,35,0,11
	.byte	'PEVT',0,4
	.word	507
	.byte	1,24,2,35,0,11
	.byte	'EVTSRC',0,4
	.word	507
	.byte	5,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	507
	.byte	19,0,2,35,0,0,29
	.byte	'Ifx_CPU_DBGSR_Bits',0,32,177,1,3
	.word	54609
	.byte	10
	.byte	'_Ifx_CPU_DBGTCR_Bits',0,32,180,1,16,4,11
	.byte	'DTA',0,4
	.word	507
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	507
	.byte	31,0,2,35,0,0,29
	.byte	'Ifx_CPU_DBGTCR_Bits',0,32,184,1,3
	.word	54824
	.byte	10
	.byte	'_Ifx_CPU_DCON0_Bits',0,32,187,1,16,4,11
	.byte	'reserved_0',0,4
	.word	507
	.byte	1,31,2,35,0,11
	.byte	'DCBYP',0,4
	.word	507
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	507
	.byte	30,0,2,35,0,0,29
	.byte	'Ifx_CPU_DCON0_Bits',0,32,192,1,3
	.word	54918
	.byte	10
	.byte	'_Ifx_CPU_DCON2_Bits',0,32,195,1,16,4,11
	.byte	'DCACHE_SZE',0,4
	.word	507
	.byte	16,16,2,35,0,11
	.byte	'DSCRATCH_SZE',0,4
	.word	507
	.byte	16,0,2,35,0,0,29
	.byte	'Ifx_CPU_DCON2_Bits',0,32,199,1,3
	.word	55034
	.byte	10
	.byte	'_Ifx_CPU_DCX_Bits',0,32,202,1,16,4,11
	.byte	'reserved_0',0,4
	.word	507
	.byte	6,26,2,35,0,11
	.byte	'DCXValue',0,4
	.word	507
	.byte	26,0,2,35,0,0,29
	.byte	'Ifx_CPU_DCX_Bits',0,32,206,1,3
	.word	55135
	.byte	10
	.byte	'_Ifx_CPU_DEADD_Bits',0,32,209,1,16,4,11
	.byte	'ERROR_ADDRESS',0,4
	.word	507
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_DEADD_Bits',0,32,212,1,3
	.word	55228
	.byte	10
	.byte	'_Ifx_CPU_DIEAR_Bits',0,32,215,1,16,4,11
	.byte	'TA',0,4
	.word	507
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_DIEAR_Bits',0,32,218,1,3
	.word	55308
	.byte	10
	.byte	'_Ifx_CPU_DIETR_Bits',0,32,221,1,16,4,11
	.byte	'IED',0,4
	.word	507
	.byte	1,31,2,35,0,11
	.byte	'IE_T',0,4
	.word	507
	.byte	1,30,2,35,0,11
	.byte	'IE_C',0,4
	.word	507
	.byte	1,29,2,35,0,11
	.byte	'IE_S',0,4
	.word	507
	.byte	1,28,2,35,0,11
	.byte	'IE_BI',0,4
	.word	507
	.byte	1,27,2,35,0,11
	.byte	'E_INFO',0,4
	.word	507
	.byte	6,21,2,35,0,11
	.byte	'IE_DUAL',0,4
	.word	507
	.byte	1,20,2,35,0,11
	.byte	'IE_SP',0,4
	.word	507
	.byte	1,19,2,35,0,11
	.byte	'IE_BS',0,4
	.word	507
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	507
	.byte	18,0,2,35,0,0,29
	.byte	'Ifx_CPU_DIETR_Bits',0,32,233,1,3
	.word	55377
	.byte	10
	.byte	'_Ifx_CPU_DMS_Bits',0,32,236,1,16,4,11
	.byte	'reserved_0',0,4
	.word	507
	.byte	1,31,2,35,0,11
	.byte	'DMSValue',0,4
	.word	507
	.byte	31,0,2,35,0,0,29
	.byte	'Ifx_CPU_DMS_Bits',0,32,240,1,3
	.word	55606
	.byte	10
	.byte	'_Ifx_CPU_DPR_L_Bits',0,32,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	507
	.byte	3,29,2,35,0,11
	.byte	'LOWBND',0,4
	.word	507
	.byte	29,0,2,35,0,0,29
	.byte	'Ifx_CPU_DPR_L_Bits',0,32,247,1,3
	.word	55699
	.byte	10
	.byte	'_Ifx_CPU_DPR_U_Bits',0,32,250,1,16,4,11
	.byte	'reserved_0',0,4
	.word	507
	.byte	3,29,2,35,0,11
	.byte	'UPPBND',0,4
	.word	507
	.byte	29,0,2,35,0,0,29
	.byte	'Ifx_CPU_DPR_U_Bits',0,32,254,1,3
	.word	55794
	.byte	10
	.byte	'_Ifx_CPU_DPRE_Bits',0,32,129,2,16,4,11
	.byte	'RE',0,4
	.word	507
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	507
	.byte	16,0,2,35,0,0,29
	.byte	'Ifx_CPU_DPRE_Bits',0,32,133,2,3
	.word	55889
	.byte	10
	.byte	'_Ifx_CPU_DPWE_Bits',0,32,136,2,16,4,11
	.byte	'WE',0,4
	.word	507
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	507
	.byte	16,0,2,35,0,0,29
	.byte	'Ifx_CPU_DPWE_Bits',0,32,140,2,3
	.word	55979
	.byte	10
	.byte	'_Ifx_CPU_DSTR_Bits',0,32,143,2,16,4,11
	.byte	'SRE',0,4
	.word	507
	.byte	1,31,2,35,0,11
	.byte	'GAE',0,4
	.word	507
	.byte	1,30,2,35,0,11
	.byte	'LBE',0,4
	.word	507
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	507
	.byte	3,26,2,35,0,11
	.byte	'CRE',0,4
	.word	507
	.byte	1,25,2,35,0,11
	.byte	'reserved_7',0,4
	.word	507
	.byte	7,18,2,35,0,11
	.byte	'DTME',0,4
	.word	507
	.byte	1,17,2,35,0,11
	.byte	'LOE',0,4
	.word	507
	.byte	1,16,2,35,0,11
	.byte	'SDE',0,4
	.word	507
	.byte	1,15,2,35,0,11
	.byte	'SCE',0,4
	.word	507
	.byte	1,14,2,35,0,11
	.byte	'CAC',0,4
	.word	507
	.byte	1,13,2,35,0,11
	.byte	'MPE',0,4
	.word	507
	.byte	1,12,2,35,0,11
	.byte	'CLE',0,4
	.word	507
	.byte	1,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	507
	.byte	3,8,2,35,0,11
	.byte	'ALN',0,4
	.word	507
	.byte	1,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	507
	.byte	7,0,2,35,0,0,29
	.byte	'Ifx_CPU_DSTR_Bits',0,32,161,2,3
	.word	56069
	.byte	10
	.byte	'_Ifx_CPU_EXEVT_Bits',0,32,164,2,16,4,11
	.byte	'EVTA',0,4
	.word	507
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	507
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	507
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	507
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	507
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	507
	.byte	24,0,2,35,0,0,29
	.byte	'Ifx_CPU_EXEVT_Bits',0,32,172,2,3
	.word	56393
	.byte	10
	.byte	'_Ifx_CPU_FCX_Bits',0,32,175,2,16,4,11
	.byte	'FCXO',0,4
	.word	507
	.byte	16,16,2,35,0,11
	.byte	'FCXS',0,4
	.word	507
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	507
	.byte	12,0,2,35,0,0,29
	.byte	'Ifx_CPU_FCX_Bits',0,32,180,2,3
	.word	56547
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_CON_Bits',0,32,183,2,16,4,11
	.byte	'TST',0,4
	.word	507
	.byte	1,31,2,35,0,11
	.byte	'TCL',0,4
	.word	507
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	507
	.byte	6,24,2,35,0,11
	.byte	'RM',0,4
	.word	507
	.byte	2,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	507
	.byte	8,14,2,35,0,11
	.byte	'FXE',0,4
	.word	507
	.byte	1,13,2,35,0,11
	.byte	'FUE',0,4
	.word	507
	.byte	1,12,2,35,0,11
	.byte	'FZE',0,4
	.word	507
	.byte	1,11,2,35,0,11
	.byte	'FVE',0,4
	.word	507
	.byte	1,10,2,35,0,11
	.byte	'FIE',0,4
	.word	507
	.byte	1,9,2,35,0,11
	.byte	'reserved_23',0,4
	.word	507
	.byte	3,6,2,35,0,11
	.byte	'FX',0,4
	.word	507
	.byte	1,5,2,35,0,11
	.byte	'FU',0,4
	.word	507
	.byte	1,4,2,35,0,11
	.byte	'FZ',0,4
	.word	507
	.byte	1,3,2,35,0,11
	.byte	'FV',0,4
	.word	507
	.byte	1,2,2,35,0,11
	.byte	'FI',0,4
	.word	507
	.byte	1,1,2,35,0,11
	.byte	'reserved_31',0,4
	.word	507
	.byte	1,0,2,35,0,0,29
	.byte	'Ifx_CPU_FPU_TRAP_CON_Bits',0,32,202,2,3
	.word	56653
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_OPC_Bits',0,32,205,2,16,4,11
	.byte	'OPC',0,4
	.word	507
	.byte	8,24,2,35,0,11
	.byte	'FMT',0,4
	.word	507
	.byte	1,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	507
	.byte	7,16,2,35,0,11
	.byte	'DREG',0,4
	.word	507
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	507
	.byte	12,0,2,35,0,0,29
	.byte	'Ifx_CPU_FPU_TRAP_OPC_Bits',0,32,212,2,3
	.word	57002
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_PC_Bits',0,32,215,2,16,4,11
	.byte	'PC',0,4
	.word	507
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_FPU_TRAP_PC_Bits',0,32,218,2,3
	.word	57162
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC1_Bits',0,32,221,2,16,4,11
	.byte	'SRC1',0,4
	.word	507
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_FPU_TRAP_SRC1_Bits',0,32,224,2,3
	.word	57243
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC2_Bits',0,32,227,2,16,4,11
	.byte	'SRC2',0,4
	.word	507
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_FPU_TRAP_SRC2_Bits',0,32,230,2,3
	.word	57330
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC3_Bits',0,32,233,2,16,4,11
	.byte	'SRC3',0,4
	.word	507
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_FPU_TRAP_SRC3_Bits',0,32,236,2,3
	.word	57417
	.byte	10
	.byte	'_Ifx_CPU_ICNT_Bits',0,32,239,2,16,4,11
	.byte	'CountValue',0,4
	.word	507
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	507
	.byte	1,0,2,35,0,0,29
	.byte	'Ifx_CPU_ICNT_Bits',0,32,243,2,3
	.word	57504
	.byte	10
	.byte	'_Ifx_CPU_ICR_Bits',0,32,246,2,16,4,11
	.byte	'CCPN',0,4
	.word	507
	.byte	10,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	507
	.byte	5,17,2,35,0,11
	.byte	'IE',0,4
	.word	507
	.byte	1,16,2,35,0,11
	.byte	'PIPN',0,4
	.word	507
	.byte	10,6,2,35,0,11
	.byte	'reserved_26',0,4
	.word	507
	.byte	6,0,2,35,0,0,29
	.byte	'Ifx_CPU_ICR_Bits',0,32,253,2,3
	.word	57595
	.byte	10
	.byte	'_Ifx_CPU_ISP_Bits',0,32,128,3,16,4,11
	.byte	'ISP',0,4
	.word	507
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_ISP_Bits',0,32,131,3,3
	.word	57738
	.byte	10
	.byte	'_Ifx_CPU_LCX_Bits',0,32,134,3,16,4,11
	.byte	'LCXO',0,4
	.word	507
	.byte	16,16,2,35,0,11
	.byte	'LCXS',0,4
	.word	507
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	507
	.byte	12,0,2,35,0,0,29
	.byte	'Ifx_CPU_LCX_Bits',0,32,139,3,3
	.word	57804
	.byte	10
	.byte	'_Ifx_CPU_M1CNT_Bits',0,32,142,3,16,4,11
	.byte	'CountValue',0,4
	.word	507
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	507
	.byte	1,0,2,35,0,0,29
	.byte	'Ifx_CPU_M1CNT_Bits',0,32,146,3,3
	.word	57910
	.byte	10
	.byte	'_Ifx_CPU_M2CNT_Bits',0,32,149,3,16,4,11
	.byte	'CountValue',0,4
	.word	507
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	507
	.byte	1,0,2,35,0,0,29
	.byte	'Ifx_CPU_M2CNT_Bits',0,32,153,3,3
	.word	58003
	.byte	10
	.byte	'_Ifx_CPU_M3CNT_Bits',0,32,156,3,16,4,11
	.byte	'CountValue',0,4
	.word	507
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	507
	.byte	1,0,2,35,0,0,29
	.byte	'Ifx_CPU_M3CNT_Bits',0,32,160,3,3
	.word	58096
	.byte	10
	.byte	'_Ifx_CPU_PC_Bits',0,32,163,3,16,4,11
	.byte	'reserved_0',0,4
	.word	507
	.byte	1,31,2,35,0,11
	.byte	'PC',0,4
	.word	507
	.byte	31,0,2,35,0,0,29
	.byte	'Ifx_CPU_PC_Bits',0,32,167,3,3
	.word	58189
	.byte	10
	.byte	'_Ifx_CPU_PCON0_Bits',0,32,170,3,16,4,11
	.byte	'reserved_0',0,4
	.word	507
	.byte	1,31,2,35,0,11
	.byte	'PCBYP',0,4
	.word	507
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	507
	.byte	30,0,2,35,0,0,29
	.byte	'Ifx_CPU_PCON0_Bits',0,32,175,3,3
	.word	58274
	.byte	10
	.byte	'_Ifx_CPU_PCON1_Bits',0,32,178,3,16,4,11
	.byte	'PCINV',0,4
	.word	507
	.byte	1,31,2,35,0,11
	.byte	'PBINV',0,4
	.word	507
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	507
	.byte	30,0,2,35,0,0,29
	.byte	'Ifx_CPU_PCON1_Bits',0,32,183,3,3
	.word	58390
	.byte	10
	.byte	'_Ifx_CPU_PCON2_Bits',0,32,186,3,16,4,11
	.byte	'PCACHE_SZE',0,4
	.word	507
	.byte	16,16,2,35,0,11
	.byte	'PSCRATCH_SZE',0,4
	.word	507
	.byte	16,0,2,35,0,0,29
	.byte	'Ifx_CPU_PCON2_Bits',0,32,190,3,3
	.word	58501
	.byte	10
	.byte	'_Ifx_CPU_PCXI_Bits',0,32,193,3,16,4,11
	.byte	'PCXO',0,4
	.word	507
	.byte	16,16,2,35,0,11
	.byte	'PCXS',0,4
	.word	507
	.byte	4,12,2,35,0,11
	.byte	'UL',0,4
	.word	507
	.byte	1,11,2,35,0,11
	.byte	'PIE',0,4
	.word	507
	.byte	1,10,2,35,0,11
	.byte	'PCPN',0,4
	.word	507
	.byte	10,0,2,35,0,0,29
	.byte	'Ifx_CPU_PCXI_Bits',0,32,200,3,3
	.word	58602
	.byte	10
	.byte	'_Ifx_CPU_PIEAR_Bits',0,32,203,3,16,4,11
	.byte	'TA',0,4
	.word	507
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_PIEAR_Bits',0,32,206,3,3
	.word	58732
	.byte	10
	.byte	'_Ifx_CPU_PIETR_Bits',0,32,209,3,16,4,11
	.byte	'IED',0,4
	.word	507
	.byte	1,31,2,35,0,11
	.byte	'IE_T',0,4
	.word	507
	.byte	1,30,2,35,0,11
	.byte	'IE_C',0,4
	.word	507
	.byte	1,29,2,35,0,11
	.byte	'IE_S',0,4
	.word	507
	.byte	1,28,2,35,0,11
	.byte	'IE_BI',0,4
	.word	507
	.byte	1,27,2,35,0,11
	.byte	'E_INFO',0,4
	.word	507
	.byte	6,21,2,35,0,11
	.byte	'IE_DUAL',0,4
	.word	507
	.byte	1,20,2,35,0,11
	.byte	'IE_SP',0,4
	.word	507
	.byte	1,19,2,35,0,11
	.byte	'IE_BS',0,4
	.word	507
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	507
	.byte	18,0,2,35,0,0,29
	.byte	'Ifx_CPU_PIETR_Bits',0,32,221,3,3
	.word	58801
	.byte	10
	.byte	'_Ifx_CPU_PMA0_Bits',0,32,224,3,16,4,11
	.byte	'reserved_0',0,4
	.word	507
	.byte	13,19,2,35,0,11
	.byte	'DAC',0,4
	.word	507
	.byte	3,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	507
	.byte	16,0,2,35,0,0,29
	.byte	'Ifx_CPU_PMA0_Bits',0,32,229,3,3
	.word	59030
	.byte	10
	.byte	'_Ifx_CPU_PMA1_Bits',0,32,232,3,16,4,11
	.byte	'reserved_0',0,4
	.word	507
	.byte	14,18,2,35,0,11
	.byte	'CAC',0,4
	.word	507
	.byte	2,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	507
	.byte	16,0,2,35,0,0,29
	.byte	'Ifx_CPU_PMA1_Bits',0,32,237,3,3
	.word	59143
	.byte	10
	.byte	'_Ifx_CPU_PMA2_Bits',0,32,240,3,16,4,11
	.byte	'PSI',0,4
	.word	507
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	507
	.byte	16,0,2,35,0,0,29
	.byte	'Ifx_CPU_PMA2_Bits',0,32,244,3,3
	.word	59256
	.byte	10
	.byte	'_Ifx_CPU_PSTR_Bits',0,32,247,3,16,4,11
	.byte	'FRE',0,4
	.word	507
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	507
	.byte	1,30,2,35,0,11
	.byte	'FBE',0,4
	.word	507
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	507
	.byte	9,20,2,35,0,11
	.byte	'FPE',0,4
	.word	507
	.byte	1,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	507
	.byte	1,18,2,35,0,11
	.byte	'FME',0,4
	.word	507
	.byte	1,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	507
	.byte	17,0,2,35,0,0,29
	.byte	'Ifx_CPU_PSTR_Bits',0,32,129,4,3
	.word	59347
	.byte	10
	.byte	'_Ifx_CPU_PSW_Bits',0,32,132,4,16,4,11
	.byte	'CDC',0,4
	.word	507
	.byte	7,25,2,35,0,11
	.byte	'CDE',0,4
	.word	507
	.byte	1,24,2,35,0,11
	.byte	'GW',0,4
	.word	507
	.byte	1,23,2,35,0,11
	.byte	'IS',0,4
	.word	507
	.byte	1,22,2,35,0,11
	.byte	'IO',0,4
	.word	507
	.byte	2,20,2,35,0,11
	.byte	'PRS',0,4
	.word	507
	.byte	2,18,2,35,0,11
	.byte	'S',0,4
	.word	507
	.byte	1,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	507
	.byte	12,5,2,35,0,11
	.byte	'SAV',0,4
	.word	507
	.byte	1,4,2,35,0,11
	.byte	'AV',0,4
	.word	507
	.byte	1,3,2,35,0,11
	.byte	'SV',0,4
	.word	507
	.byte	1,2,2,35,0,11
	.byte	'V',0,4
	.word	507
	.byte	1,1,2,35,0,11
	.byte	'C',0,4
	.word	507
	.byte	1,0,2,35,0,0,29
	.byte	'Ifx_CPU_PSW_Bits',0,32,147,4,3
	.word	59550
	.byte	10
	.byte	'_Ifx_CPU_SEGEN_Bits',0,32,150,4,16,4,11
	.byte	'ADFLIP',0,4
	.word	507
	.byte	8,24,2,35,0,11
	.byte	'ADTYPE',0,4
	.word	507
	.byte	2,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	507
	.byte	21,1,2,35,0,11
	.byte	'AE',0,4
	.word	507
	.byte	1,0,2,35,0,0,29
	.byte	'Ifx_CPU_SEGEN_Bits',0,32,156,4,3
	.word	59793
	.byte	10
	.byte	'_Ifx_CPU_SMACON_Bits',0,32,159,4,16,4,11
	.byte	'PC',0,4
	.word	507
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	507
	.byte	1,30,2,35,0,11
	.byte	'PT',0,4
	.word	507
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	507
	.byte	5,24,2,35,0,11
	.byte	'DC',0,4
	.word	507
	.byte	1,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	507
	.byte	1,22,2,35,0,11
	.byte	'DT',0,4
	.word	507
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	507
	.byte	13,8,2,35,0,11
	.byte	'IODT',0,4
	.word	507
	.byte	1,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	507
	.byte	7,0,2,35,0,0,29
	.byte	'Ifx_CPU_SMACON_Bits',0,32,171,4,3
	.word	59921
	.byte	10
	.byte	'_Ifx_CPU_SPROT_ACCENA_Bits',0,32,174,4,16,4,11
	.byte	'EN',0,4
	.word	484
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_SPROT_ACCENA_Bits',0,32,177,4,3
	.word	60162
	.byte	10
	.byte	'_Ifx_CPU_SPROT_ACCENB_Bits',0,32,180,4,16,4,11
	.byte	'reserved_0',0,4
	.word	484
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_SPROT_ACCENB_Bits',0,32,183,4,3
	.word	60245
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,32,186,4,16,4,11
	.byte	'EN',0,4
	.word	484
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,32,189,4,3
	.word	60336
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,32,192,4,16,4,11
	.byte	'reserved_0',0,4
	.word	484
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,32,195,4,3
	.word	60427
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_LA_Bits',0,32,198,4,16,4,11
	.byte	'reserved_0',0,1
	.word	659
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	484
	.byte	27,0,2,35,0,0,29
	.byte	'Ifx_CPU_SPROT_RGN_LA_Bits',0,32,202,4,3
	.word	60526
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_UA_Bits',0,32,205,4,16,4,11
	.byte	'reserved_0',0,1
	.word	659
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	484
	.byte	27,0,2,35,0,0,29
	.byte	'Ifx_CPU_SPROT_RGN_UA_Bits',0,32,209,4,3
	.word	60633
	.byte	10
	.byte	'_Ifx_CPU_SWEVT_Bits',0,32,212,4,16,4,11
	.byte	'EVTA',0,4
	.word	507
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	507
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	507
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	507
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	507
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	507
	.byte	24,0,2,35,0,0,29
	.byte	'Ifx_CPU_SWEVT_Bits',0,32,220,4,3
	.word	60740
	.byte	10
	.byte	'_Ifx_CPU_SYSCON_Bits',0,32,223,4,16,4,11
	.byte	'FCDSF',0,4
	.word	507
	.byte	1,31,2,35,0,11
	.byte	'PROTEN',0,4
	.word	507
	.byte	1,30,2,35,0,11
	.byte	'TPROTEN',0,4
	.word	507
	.byte	1,29,2,35,0,11
	.byte	'IS',0,4
	.word	507
	.byte	1,28,2,35,0,11
	.byte	'IT',0,4
	.word	507
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	507
	.byte	27,0,2,35,0,0,29
	.byte	'Ifx_CPU_SYSCON_Bits',0,32,231,4,3
	.word	60894
	.byte	10
	.byte	'_Ifx_CPU_TASK_ASI_Bits',0,32,234,4,16,4,11
	.byte	'ASI',0,4
	.word	507
	.byte	5,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	507
	.byte	27,0,2,35,0,0,29
	.byte	'Ifx_CPU_TASK_ASI_Bits',0,32,238,4,3
	.word	61055
	.byte	10
	.byte	'_Ifx_CPU_TPS_CON_Bits',0,32,241,4,16,4,11
	.byte	'TEXP0',0,4
	.word	507
	.byte	1,31,2,35,0,11
	.byte	'TEXP1',0,4
	.word	507
	.byte	1,30,2,35,0,11
	.byte	'TEXP2',0,4
	.word	507
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	507
	.byte	13,16,2,35,0,11
	.byte	'TTRAP',0,4
	.word	507
	.byte	1,15,2,35,0,11
	.byte	'reserved_17',0,4
	.word	507
	.byte	15,0,2,35,0,0,29
	.byte	'Ifx_CPU_TPS_CON_Bits',0,32,249,4,3
	.word	61153
	.byte	10
	.byte	'_Ifx_CPU_TPS_TIMER_Bits',0,32,252,4,16,4,11
	.byte	'Timer',0,4
	.word	507
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_TPS_TIMER_Bits',0,32,255,4,3
	.word	61325
	.byte	10
	.byte	'_Ifx_CPU_TR_ADR_Bits',0,32,130,5,16,4,11
	.byte	'ADDR',0,4
	.word	507
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_TR_ADR_Bits',0,32,133,5,3
	.word	61405
	.byte	10
	.byte	'_Ifx_CPU_TR_EVT_Bits',0,32,136,5,16,4,11
	.byte	'EVTA',0,4
	.word	507
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	507
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	507
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	507
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	507
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	507
	.byte	4,20,2,35,0,11
	.byte	'TYP',0,4
	.word	507
	.byte	1,19,2,35,0,11
	.byte	'RNG',0,4
	.word	507
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	507
	.byte	1,17,2,35,0,11
	.byte	'ASI_EN',0,4
	.word	507
	.byte	1,16,2,35,0,11
	.byte	'ASI',0,4
	.word	507
	.byte	5,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	507
	.byte	6,5,2,35,0,11
	.byte	'AST',0,4
	.word	507
	.byte	1,4,2,35,0,11
	.byte	'ALD',0,4
	.word	507
	.byte	1,3,2,35,0,11
	.byte	'reserved_29',0,4
	.word	507
	.byte	3,0,2,35,0,0,29
	.byte	'Ifx_CPU_TR_EVT_Bits',0,32,153,5,3
	.word	61478
	.byte	10
	.byte	'_Ifx_CPU_TRIG_ACC_Bits',0,32,156,5,16,4,11
	.byte	'T0',0,4
	.word	507
	.byte	1,31,2,35,0,11
	.byte	'T1',0,4
	.word	507
	.byte	1,30,2,35,0,11
	.byte	'T2',0,4
	.word	507
	.byte	1,29,2,35,0,11
	.byte	'T3',0,4
	.word	507
	.byte	1,28,2,35,0,11
	.byte	'T4',0,4
	.word	507
	.byte	1,27,2,35,0,11
	.byte	'T5',0,4
	.word	507
	.byte	1,26,2,35,0,11
	.byte	'T6',0,4
	.word	507
	.byte	1,25,2,35,0,11
	.byte	'T7',0,4
	.word	507
	.byte	1,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	507
	.byte	24,0,2,35,0,0,29
	.byte	'Ifx_CPU_TRIG_ACC_Bits',0,32,167,5,3
	.word	61796
	.byte	12,32,175,5,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53014
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_A',0,32,180,5,3
	.word	61991
	.byte	12,32,183,5,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53075
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_BIV',0,32,188,5,3
	.word	62050
	.byte	12,32,191,5,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53154
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_BTV',0,32,196,5,3
	.word	62111
	.byte	12,32,199,5,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53240
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_CCNT',0,32,204,5,3
	.word	62172
	.byte	12,32,207,5,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53329
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_CCTRL',0,32,212,5,3
	.word	62234
	.byte	12,32,215,5,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53475
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_COMPAT',0,32,220,5,3
	.word	62297
	.byte	12,32,223,5,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53602
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_CORE_ID',0,32,228,5,3
	.word	62361
	.byte	12,32,231,5,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53700
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_CPR_L',0,32,236,5,3
	.word	62426
	.byte	12,32,239,5,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53793
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_CPR_U',0,32,244,5,3
	.word	62489
	.byte	12,32,247,5,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53886
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_CPU_ID',0,32,252,5,3
	.word	62552
	.byte	12,32,255,5,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53993
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_CPXE',0,32,132,6,3
	.word	62616
	.byte	12,32,135,6,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54080
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_CREVT',0,32,140,6,3
	.word	62678
	.byte	12,32,143,6,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54234
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_CUS_ID',0,32,148,6,3
	.word	62741
	.byte	12,32,151,6,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54328
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_D',0,32,156,6,3
	.word	62805
	.byte	12,32,159,6,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54391
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_DATR',0,32,164,6,3
	.word	62864
	.byte	12,32,167,6,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54609
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_DBGSR',0,32,172,6,3
	.word	62926
	.byte	12,32,175,6,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54824
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_DBGTCR',0,32,180,6,3
	.word	62989
	.byte	12,32,183,6,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54918
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_DCON0',0,32,188,6,3
	.word	63053
	.byte	12,32,191,6,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55034
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_DCON2',0,32,196,6,3
	.word	63116
	.byte	12,32,199,6,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55135
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_DCX',0,32,204,6,3
	.word	63179
	.byte	12,32,207,6,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55228
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_DEADD',0,32,212,6,3
	.word	63240
	.byte	12,32,215,6,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55308
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_DIEAR',0,32,220,6,3
	.word	63303
	.byte	12,32,223,6,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55377
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_DIETR',0,32,228,6,3
	.word	63366
	.byte	12,32,231,6,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55606
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_DMS',0,32,236,6,3
	.word	63429
	.byte	12,32,239,6,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55699
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_DPR_L',0,32,244,6,3
	.word	63490
	.byte	12,32,247,6,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55794
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_DPR_U',0,32,252,6,3
	.word	63553
	.byte	12,32,255,6,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55889
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_DPRE',0,32,132,7,3
	.word	63616
	.byte	12,32,135,7,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55979
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_DPWE',0,32,140,7,3
	.word	63678
	.byte	12,32,143,7,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56069
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_DSTR',0,32,148,7,3
	.word	63740
	.byte	12,32,151,7,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56393
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_EXEVT',0,32,156,7,3
	.word	63802
	.byte	12,32,159,7,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56547
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_FCX',0,32,164,7,3
	.word	63865
	.byte	12,32,167,7,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56653
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_FPU_TRAP_CON',0,32,172,7,3
	.word	63926
	.byte	12,32,175,7,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57002
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_FPU_TRAP_OPC',0,32,180,7,3
	.word	63996
	.byte	12,32,183,7,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57162
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_FPU_TRAP_PC',0,32,188,7,3
	.word	64066
	.byte	12,32,191,7,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57243
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_FPU_TRAP_SRC1',0,32,196,7,3
	.word	64135
	.byte	12,32,199,7,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57330
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_FPU_TRAP_SRC2',0,32,204,7,3
	.word	64206
	.byte	12,32,207,7,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57417
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_FPU_TRAP_SRC3',0,32,212,7,3
	.word	64277
	.byte	12,32,215,7,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57504
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_ICNT',0,32,220,7,3
	.word	64348
	.byte	12,32,223,7,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57595
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_ICR',0,32,228,7,3
	.word	64410
	.byte	12,32,231,7,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57738
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_ISP',0,32,236,7,3
	.word	64471
	.byte	12,32,239,7,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57804
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_LCX',0,32,244,7,3
	.word	64532
	.byte	12,32,247,7,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57910
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_M1CNT',0,32,252,7,3
	.word	64593
	.byte	12,32,255,7,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58003
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_M2CNT',0,32,132,8,3
	.word	64656
	.byte	12,32,135,8,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58096
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_M3CNT',0,32,140,8,3
	.word	64719
	.byte	12,32,143,8,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58189
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_PC',0,32,148,8,3
	.word	64782
	.byte	12,32,151,8,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58274
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_PCON0',0,32,156,8,3
	.word	64842
	.byte	12,32,159,8,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58390
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_PCON1',0,32,164,8,3
	.word	64905
	.byte	12,32,167,8,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58501
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_PCON2',0,32,172,8,3
	.word	64968
	.byte	12,32,175,8,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58602
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_PCXI',0,32,180,8,3
	.word	65031
	.byte	12,32,183,8,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58732
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_PIEAR',0,32,188,8,3
	.word	65093
	.byte	12,32,191,8,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58801
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_PIETR',0,32,196,8,3
	.word	65156
	.byte	12,32,199,8,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59030
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_PMA0',0,32,204,8,3
	.word	65219
	.byte	12,32,207,8,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59143
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_PMA1',0,32,212,8,3
	.word	65281
	.byte	12,32,215,8,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59256
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_PMA2',0,32,220,8,3
	.word	65343
	.byte	12,32,223,8,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59347
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_PSTR',0,32,228,8,3
	.word	65405
	.byte	12,32,231,8,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59550
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_PSW',0,32,236,8,3
	.word	65467
	.byte	12,32,239,8,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59793
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_SEGEN',0,32,244,8,3
	.word	65528
	.byte	12,32,247,8,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59921
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_SMACON',0,32,252,8,3
	.word	65591
	.byte	12,32,255,8,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60162
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_SPROT_ACCENA',0,32,132,9,3
	.word	65655
	.byte	12,32,135,9,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60245
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_SPROT_ACCENB',0,32,140,9,3
	.word	65725
	.byte	12,32,143,9,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60336
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA',0,32,148,9,3
	.word	65795
	.byte	12,32,151,9,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60427
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB',0,32,156,9,3
	.word	65869
	.byte	12,32,159,9,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60526
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_SPROT_RGN_LA',0,32,164,9,3
	.word	65943
	.byte	12,32,167,9,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60633
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_SPROT_RGN_UA',0,32,172,9,3
	.word	66013
	.byte	12,32,175,9,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60740
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_SWEVT',0,32,180,9,3
	.word	66083
	.byte	12,32,183,9,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60894
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_SYSCON',0,32,188,9,3
	.word	66146
	.byte	12,32,191,9,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61055
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_TASK_ASI',0,32,196,9,3
	.word	66210
	.byte	12,32,199,9,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61153
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_TPS_CON',0,32,204,9,3
	.word	66276
	.byte	12,32,207,9,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61325
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_TPS_TIMER',0,32,212,9,3
	.word	66341
	.byte	12,32,215,9,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61405
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_TR_ADR',0,32,220,9,3
	.word	66408
	.byte	12,32,223,9,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61478
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_TR_EVT',0,32,228,9,3
	.word	66472
	.byte	12,32,231,9,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61796
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_TRIG_ACC',0,32,236,9,3
	.word	66536
	.byte	10
	.byte	'_Ifx_CPU_CPR',0,32,247,9,25,8,13
	.byte	'L',0
	.word	62426
	.byte	4,2,35,0,13
	.byte	'U',0
	.word	62489
	.byte	4,2,35,4,0,14
	.word	66602
	.byte	29
	.byte	'Ifx_CPU_CPR',0,32,251,9,3
	.word	66644
	.byte	10
	.byte	'_Ifx_CPU_DPR',0,32,254,9,25,8,13
	.byte	'L',0
	.word	63490
	.byte	4,2,35,0,13
	.byte	'U',0
	.word	63553
	.byte	4,2,35,4,0,14
	.word	66670
	.byte	29
	.byte	'Ifx_CPU_DPR',0,32,130,10,3
	.word	66712
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN',0,32,133,10,25,16,13
	.byte	'LA',0
	.word	65943
	.byte	4,2,35,0,13
	.byte	'UA',0
	.word	66013
	.byte	4,2,35,4,13
	.byte	'ACCENA',0
	.word	65795
	.byte	4,2,35,8,13
	.byte	'ACCENB',0
	.word	65869
	.byte	4,2,35,12,0,14
	.word	66738
	.byte	29
	.byte	'Ifx_CPU_SPROT_RGN',0,32,139,10,3
	.word	66820
	.byte	18,12
	.word	66341
	.byte	19,2,0,10
	.byte	'_Ifx_CPU_TPS',0,32,142,10,25,16,13
	.byte	'CON',0
	.word	66276
	.byte	4,2,35,0,13
	.byte	'TIMER',0
	.word	66852
	.byte	12,2,35,4,0,14
	.word	66861
	.byte	29
	.byte	'Ifx_CPU_TPS',0,32,146,10,3
	.word	66909
	.byte	10
	.byte	'_Ifx_CPU_TR',0,32,149,10,25,8,13
	.byte	'EVT',0
	.word	66472
	.byte	4,2,35,0,13
	.byte	'ADR',0
	.word	66408
	.byte	4,2,35,4,0,14
	.word	66935
	.byte	29
	.byte	'Ifx_CPU_TR',0,32,153,10,3
	.word	66980
	.byte	18,176,32
	.word	659
	.byte	19,175,32,0,18,208,223,1
	.word	659
	.byte	19,207,223,1,0,18,248,1
	.word	659
	.byte	19,247,1,0,18,244,29
	.word	659
	.byte	19,243,29,0,18,188,3
	.word	659
	.byte	19,187,3,0,18,232,3
	.word	659
	.byte	19,231,3,0,18,252,23
	.word	659
	.byte	19,251,23,0,18,228,63
	.word	659
	.byte	19,227,63,0,18,128,1
	.word	66670
	.byte	19,15,0,14
	.word	67095
	.byte	18,128,31
	.word	659
	.byte	19,255,30,0,18,64
	.word	66602
	.byte	19,7,0,14
	.word	67121
	.byte	18,192,31
	.word	659
	.byte	19,191,31,0,18,16
	.word	62616
	.byte	19,3,0,18,16
	.word	63616
	.byte	19,3,0,18,16
	.word	63678
	.byte	19,3,0,18,208,7
	.word	659
	.byte	19,207,7,0,14
	.word	66861
	.byte	18,240,23
	.word	659
	.byte	19,239,23,0,18,64
	.word	66935
	.byte	19,7,0,14
	.word	67200
	.byte	18,192,23
	.word	659
	.byte	19,191,23,0,18,232,1
	.word	659
	.byte	19,231,1,0,18,180,1
	.word	659
	.byte	19,179,1,0,18,172,1
	.word	659
	.byte	19,171,1,0,18,64
	.word	62805
	.byte	19,15,0,18,64
	.word	659
	.byte	19,63,0,18,64
	.word	61991
	.byte	19,15,0,10
	.byte	'_Ifx_CPU',0,32,166,10,25,128,128,4,13
	.byte	'reserved_0',0
	.word	67005
	.byte	176,32,2,35,0,13
	.byte	'SEGEN',0
	.word	65528
	.byte	4,3,35,176,32,13
	.byte	'reserved_1034',0
	.word	67016
	.byte	208,223,1,3,35,180,32,13
	.byte	'TASK_ASI',0
	.word	66210
	.byte	4,4,35,132,128,2,13
	.byte	'reserved_8008',0
	.word	67029
	.byte	248,1,4,35,136,128,2,13
	.byte	'PMA0',0
	.word	65219
	.byte	4,4,35,128,130,2,13
	.byte	'PMA1',0
	.word	65281
	.byte	4,4,35,132,130,2,13
	.byte	'PMA2',0
	.word	65343
	.byte	4,4,35,136,130,2,13
	.byte	'reserved_810C',0
	.word	67040
	.byte	244,29,4,35,140,130,2,13
	.byte	'DCON2',0
	.word	63116
	.byte	4,4,35,128,160,2,13
	.byte	'reserved_9004',0
	.word	4849
	.byte	8,4,35,132,160,2,13
	.byte	'SMACON',0
	.word	65591
	.byte	4,4,35,140,160,2,13
	.byte	'DSTR',0
	.word	63740
	.byte	4,4,35,144,160,2,13
	.byte	'reserved_9014',0
	.word	3030
	.byte	4,4,35,148,160,2,13
	.byte	'DATR',0
	.word	62864
	.byte	4,4,35,152,160,2,13
	.byte	'DEADD',0
	.word	63240
	.byte	4,4,35,156,160,2,13
	.byte	'DIEAR',0
	.word	63303
	.byte	4,4,35,160,160,2,13
	.byte	'DIETR',0
	.word	63366
	.byte	4,4,35,164,160,2,13
	.byte	'reserved_9028',0
	.word	4220
	.byte	24,4,35,168,160,2,13
	.byte	'DCON0',0
	.word	63053
	.byte	4,4,35,192,160,2,13
	.byte	'reserved_9044',0
	.word	67051
	.byte	188,3,4,35,196,160,2,13
	.byte	'PSTR',0
	.word	65405
	.byte	4,4,35,128,164,2,13
	.byte	'PCON1',0
	.word	64905
	.byte	4,4,35,132,164,2,13
	.byte	'PCON2',0
	.word	64968
	.byte	4,4,35,136,164,2,13
	.byte	'PCON0',0
	.word	64842
	.byte	4,4,35,140,164,2,13
	.byte	'PIEAR',0
	.word	65093
	.byte	4,4,35,144,164,2,13
	.byte	'PIETR',0
	.word	65156
	.byte	4,4,35,148,164,2,13
	.byte	'reserved_9218',0
	.word	67062
	.byte	232,3,4,35,152,164,2,13
	.byte	'COMPAT',0
	.word	62297
	.byte	4,4,35,128,168,2,13
	.byte	'reserved_9404',0
	.word	67073
	.byte	252,23,4,35,132,168,2,13
	.byte	'FPU_TRAP_CON',0
	.word	63926
	.byte	4,4,35,128,192,2,13
	.byte	'FPU_TRAP_PC',0
	.word	64066
	.byte	4,4,35,132,192,2,13
	.byte	'FPU_TRAP_OPC',0
	.word	63996
	.byte	4,4,35,136,192,2,13
	.byte	'reserved_A00C',0
	.word	3030
	.byte	4,4,35,140,192,2,13
	.byte	'FPU_TRAP_SRC1',0
	.word	64135
	.byte	4,4,35,144,192,2,13
	.byte	'FPU_TRAP_SRC2',0
	.word	64206
	.byte	4,4,35,148,192,2,13
	.byte	'FPU_TRAP_SRC3',0
	.word	64277
	.byte	4,4,35,152,192,2,13
	.byte	'reserved_A01C',0
	.word	67084
	.byte	228,63,4,35,156,192,2,13
	.byte	'DPR',0
	.word	67105
	.byte	128,1,4,35,128,128,3,13
	.byte	'reserved_C080',0
	.word	67110
	.byte	128,31,4,35,128,129,3,13
	.byte	'CPR',0
	.word	67130
	.byte	64,4,35,128,160,3,13
	.byte	'reserved_D040',0
	.word	67135
	.byte	192,31,4,35,192,160,3,13
	.byte	'CPXE',0
	.word	67146
	.byte	16,4,35,128,192,3,13
	.byte	'DPRE',0
	.word	67155
	.byte	16,4,35,144,192,3,13
	.byte	'DPWE',0
	.word	67164
	.byte	16,4,35,160,192,3,13
	.byte	'reserved_E030',0
	.word	67173
	.byte	208,7,4,35,176,192,3,13
	.byte	'TPS',0
	.word	67184
	.byte	16,4,35,128,200,3,13
	.byte	'reserved_E410',0
	.word	67189
	.byte	240,23,4,35,144,200,3,13
	.byte	'TR',0
	.word	67209
	.byte	64,4,35,128,224,3,13
	.byte	'reserved_F040',0
	.word	67214
	.byte	192,23,4,35,192,224,3,13
	.byte	'CCTRL',0
	.word	62234
	.byte	4,4,35,128,248,3,13
	.byte	'CCNT',0
	.word	62172
	.byte	4,4,35,132,248,3,13
	.byte	'ICNT',0
	.word	64348
	.byte	4,4,35,136,248,3,13
	.byte	'M1CNT',0
	.word	64593
	.byte	4,4,35,140,248,3,13
	.byte	'M2CNT',0
	.word	64656
	.byte	4,4,35,144,248,3,13
	.byte	'M3CNT',0
	.word	64719
	.byte	4,4,35,148,248,3,13
	.byte	'reserved_FC18',0
	.word	67225
	.byte	232,1,4,35,152,248,3,13
	.byte	'DBGSR',0
	.word	62926
	.byte	4,4,35,128,250,3,13
	.byte	'reserved_FD04',0
	.word	3030
	.byte	4,4,35,132,250,3,13
	.byte	'EXEVT',0
	.word	63802
	.byte	4,4,35,136,250,3,13
	.byte	'CREVT',0
	.word	62678
	.byte	4,4,35,140,250,3,13
	.byte	'SWEVT',0
	.word	66083
	.byte	4,4,35,144,250,3,13
	.byte	'reserved_FD14',0
	.word	50955
	.byte	28,4,35,148,250,3,13
	.byte	'TRIG_ACC',0
	.word	66536
	.byte	4,4,35,176,250,3,13
	.byte	'reserved_FD34',0
	.word	5189
	.byte	12,4,35,180,250,3,13
	.byte	'DMS',0
	.word	63429
	.byte	4,4,35,192,250,3,13
	.byte	'DCX',0
	.word	63179
	.byte	4,4,35,196,250,3,13
	.byte	'DBGTCR',0
	.word	62989
	.byte	4,4,35,200,250,3,13
	.byte	'reserved_FD4C',0
	.word	67236
	.byte	180,1,4,35,204,250,3,13
	.byte	'PCXI',0
	.word	65031
	.byte	4,4,35,128,252,3,13
	.byte	'PSW',0
	.word	65467
	.byte	4,4,35,132,252,3,13
	.byte	'PC',0
	.word	64782
	.byte	4,4,35,136,252,3,13
	.byte	'reserved_FE0C',0
	.word	4849
	.byte	8,4,35,140,252,3,13
	.byte	'SYSCON',0
	.word	66146
	.byte	4,4,35,148,252,3,13
	.byte	'CPU_ID',0
	.word	62552
	.byte	4,4,35,152,252,3,13
	.byte	'CORE_ID',0
	.word	62361
	.byte	4,4,35,156,252,3,13
	.byte	'BIV',0
	.word	62050
	.byte	4,4,35,160,252,3,13
	.byte	'BTV',0
	.word	62111
	.byte	4,4,35,164,252,3,13
	.byte	'ISP',0
	.word	64471
	.byte	4,4,35,168,252,3,13
	.byte	'ICR',0
	.word	64410
	.byte	4,4,35,172,252,3,13
	.byte	'reserved_FE30',0
	.word	4849
	.byte	8,4,35,176,252,3,13
	.byte	'FCX',0
	.word	63865
	.byte	4,4,35,184,252,3,13
	.byte	'LCX',0
	.word	64532
	.byte	4,4,35,188,252,3,13
	.byte	'reserved_FE40',0
	.word	50946
	.byte	16,4,35,192,252,3,13
	.byte	'CUS_ID',0
	.word	62741
	.byte	4,4,35,208,252,3,13
	.byte	'reserved_FE54',0
	.word	67247
	.byte	172,1,4,35,212,252,3,13
	.byte	'D',0
	.word	67258
	.byte	64,4,35,128,254,3,13
	.byte	'reserved_FF40',0
	.word	67267
	.byte	64,4,35,192,254,3,13
	.byte	'A',0
	.word	67276
	.byte	64,4,35,128,255,3,13
	.byte	'reserved_FFC0',0
	.word	67267
	.byte	64,4,35,192,255,3,0,14
	.word	67285
	.byte	29
	.byte	'Ifx_CPU',0,32,130,11,3
	.word	69076
	.byte	15,6,127,9,1,16
	.byte	'IfxCpu_Id_0',0,0,16
	.byte	'IfxCpu_Id_1',0,1,16
	.byte	'IfxCpu_Id_none',0,2,0,29
	.byte	'IfxCpu_Id',0,6,132,1,3
	.word	69098
	.byte	29
	.byte	'IfxCpu_ResourceCpu',0,6,161,1,3
	.word	1471
	.byte	29
	.byte	'Ifx_SRC_SRCR_Bits',0,11,62,3
	.word	10591
	.byte	29
	.byte	'Ifx_SRC_SRCR',0,11,75,3
	.word	10881
	.byte	10
	.byte	'_Ifx_SRC_AGBT',0,11,86,25,4,13
	.byte	'SR',0
	.word	10881
	.byte	4,2,35,0,0,14
	.word	69243
	.byte	29
	.byte	'Ifx_SRC_AGBT',0,11,89,3
	.word	69275
	.byte	10
	.byte	'_Ifx_SRC_ASCLIN',0,11,92,25,12,13
	.byte	'TX',0
	.word	10881
	.byte	4,2,35,0,13
	.byte	'RX',0
	.word	10881
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	10881
	.byte	4,2,35,8,0,14
	.word	69301
	.byte	29
	.byte	'Ifx_SRC_ASCLIN',0,11,97,3
	.word	69360
	.byte	10
	.byte	'_Ifx_SRC_BCUSPB',0,11,100,25,4,13
	.byte	'SBSRC',0
	.word	10881
	.byte	4,2,35,0,0,14
	.word	69388
	.byte	29
	.byte	'Ifx_SRC_BCUSPB',0,11,103,3
	.word	69425
	.byte	18,64
	.word	10881
	.byte	19,15,0,10
	.byte	'_Ifx_SRC_CAN',0,11,106,25,64,13
	.byte	'INT',0
	.word	69453
	.byte	64,2,35,0,0,14
	.word	69462
	.byte	29
	.byte	'Ifx_SRC_CAN',0,11,109,3
	.word	69494
	.byte	10
	.byte	'_Ifx_SRC_CCU6',0,11,112,25,16,13
	.byte	'SR0',0
	.word	10881
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10881
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	10881
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	10881
	.byte	4,2,35,12,0,14
	.word	69519
	.byte	29
	.byte	'Ifx_SRC_CCU6',0,11,118,3
	.word	69591
	.byte	18,8
	.word	10881
	.byte	19,1,0,10
	.byte	'_Ifx_SRC_CERBERUS',0,11,121,25,8,13
	.byte	'SR',0
	.word	69617
	.byte	8,2,35,0,0,14
	.word	69626
	.byte	29
	.byte	'Ifx_SRC_CERBERUS',0,11,124,3
	.word	69662
	.byte	10
	.byte	'_Ifx_SRC_CIF',0,11,127,25,16,13
	.byte	'MI',0
	.word	10881
	.byte	4,2,35,0,13
	.byte	'MIEP',0
	.word	10881
	.byte	4,2,35,4,13
	.byte	'ISP',0
	.word	10881
	.byte	4,2,35,8,13
	.byte	'MJPEG',0
	.word	10881
	.byte	4,2,35,12,0,14
	.word	69692
	.byte	29
	.byte	'Ifx_SRC_CIF',0,11,133,1,3
	.word	69765
	.byte	10
	.byte	'_Ifx_SRC_CPU',0,11,136,1,25,4,13
	.byte	'SBSRC',0
	.word	10881
	.byte	4,2,35,0,0,14
	.word	69791
	.byte	29
	.byte	'Ifx_SRC_CPU',0,11,139,1,3
	.word	69826
	.byte	18,192,1
	.word	10881
	.byte	19,47,0,10
	.byte	'_Ifx_SRC_DMA',0,11,142,1,25,208,1,13
	.byte	'ERR',0
	.word	10881
	.byte	4,2,35,0,13
	.byte	'reserved_4',0
	.word	5189
	.byte	12,2,35,4,13
	.byte	'CH',0
	.word	69852
	.byte	192,1,2,35,16,0,14
	.word	69862
	.byte	29
	.byte	'Ifx_SRC_DMA',0,11,147,1,3
	.word	69929
	.byte	10
	.byte	'_Ifx_SRC_DSADC',0,11,150,1,25,8,13
	.byte	'SRM',0
	.word	10881
	.byte	4,2,35,0,13
	.byte	'SRA',0
	.word	10881
	.byte	4,2,35,4,0,14
	.word	69955
	.byte	29
	.byte	'Ifx_SRC_DSADC',0,11,154,1,3
	.word	70003
	.byte	10
	.byte	'_Ifx_SRC_EMEM',0,11,157,1,25,4,13
	.byte	'SR',0
	.word	10881
	.byte	4,2,35,0,0,14
	.word	70031
	.byte	29
	.byte	'Ifx_SRC_EMEM',0,11,160,1,3
	.word	70064
	.byte	10
	.byte	'_Ifx_SRC_ERAY',0,11,163,1,25,80,13
	.byte	'INT',0
	.word	69617
	.byte	8,2,35,0,13
	.byte	'TINT',0
	.word	69617
	.byte	8,2,35,8,13
	.byte	'NDAT',0
	.word	69617
	.byte	8,2,35,16,13
	.byte	'MBSC',0
	.word	69617
	.byte	8,2,35,24,13
	.byte	'OBUSY',0
	.word	10881
	.byte	4,2,35,32,13
	.byte	'IBUSY',0
	.word	10881
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	50964
	.byte	40,2,35,40,0,14
	.word	70091
	.byte	29
	.byte	'Ifx_SRC_ERAY',0,11,172,1,3
	.word	70218
	.byte	10
	.byte	'_Ifx_SRC_ETH',0,11,175,1,25,4,13
	.byte	'SR',0
	.word	10881
	.byte	4,2,35,0,0,14
	.word	70245
	.byte	29
	.byte	'Ifx_SRC_ETH',0,11,178,1,3
	.word	70277
	.byte	10
	.byte	'_Ifx_SRC_FCE',0,11,181,1,25,4,13
	.byte	'SR',0
	.word	10881
	.byte	4,2,35,0,0,14
	.word	70303
	.byte	29
	.byte	'Ifx_SRC_FCE',0,11,184,1,3
	.word	70335
	.byte	10
	.byte	'_Ifx_SRC_FFT',0,11,187,1,25,12,13
	.byte	'DONE',0
	.word	10881
	.byte	4,2,35,0,13
	.byte	'ERR',0
	.word	10881
	.byte	4,2,35,4,13
	.byte	'RFS',0
	.word	10881
	.byte	4,2,35,8,0,14
	.word	70361
	.byte	29
	.byte	'Ifx_SRC_FFT',0,11,192,1,3
	.word	70421
	.byte	10
	.byte	'_Ifx_SRC_GPSR',0,11,195,1,25,32,13
	.byte	'SR0',0
	.word	10881
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10881
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	10881
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	10881
	.byte	4,2,35,12,13
	.byte	'reserved_10',0
	.word	50946
	.byte	16,2,35,16,0,14
	.word	70447
	.byte	29
	.byte	'Ifx_SRC_GPSR',0,11,202,1,3
	.word	70541
	.byte	10
	.byte	'_Ifx_SRC_GPT12',0,11,205,1,25,48,13
	.byte	'CIRQ',0
	.word	10881
	.byte	4,2,35,0,13
	.byte	'T2',0
	.word	10881
	.byte	4,2,35,4,13
	.byte	'T3',0
	.word	10881
	.byte	4,2,35,8,13
	.byte	'T4',0
	.word	10881
	.byte	4,2,35,12,13
	.byte	'T5',0
	.word	10881
	.byte	4,2,35,16,13
	.byte	'T6',0
	.word	10881
	.byte	4,2,35,20,13
	.byte	'reserved_18',0
	.word	4220
	.byte	24,2,35,24,0,14
	.word	70568
	.byte	29
	.byte	'Ifx_SRC_GPT12',0,11,214,1,3
	.word	70685
	.byte	18,12
	.word	10881
	.byte	19,2,0,18,32
	.word	10881
	.byte	19,7,0,18,32
	.word	70722
	.byte	19,0,0,18,88
	.word	659
	.byte	19,87,0,18,108
	.word	10881
	.byte	19,26,0,18,96
	.word	659
	.byte	19,95,0,18,96
	.word	70722
	.byte	19,2,0,18,160,3
	.word	659
	.byte	19,159,3,0,18,64
	.word	70722
	.byte	19,1,0,18,192,3
	.word	659
	.byte	19,191,3,0,18,16
	.word	10881
	.byte	19,3,0,18,64
	.word	70807
	.byte	19,3,0,18,192,2
	.word	659
	.byte	19,191,2,0,18,52
	.word	659
	.byte	19,51,0,10
	.byte	'_Ifx_SRC_GTM',0,11,217,1,25,204,18,13
	.byte	'AEIIRQ',0
	.word	10881
	.byte	4,2,35,0,13
	.byte	'ARUIRQ',0
	.word	70713
	.byte	12,2,35,4,13
	.byte	'reserved_10',0
	.word	3030
	.byte	4,2,35,16,13
	.byte	'BRCIRQ',0
	.word	10881
	.byte	4,2,35,20,13
	.byte	'CMPIRQ',0
	.word	10881
	.byte	4,2,35,24,13
	.byte	'SPEIRQ',0
	.word	69617
	.byte	8,2,35,28,13
	.byte	'reserved_24',0
	.word	4849
	.byte	8,2,35,36,13
	.byte	'PSM',0
	.word	70731
	.byte	32,2,35,44,13
	.byte	'reserved_4C',0
	.word	70740
	.byte	88,2,35,76,13
	.byte	'DPLL',0
	.word	70749
	.byte	108,3,35,164,1,13
	.byte	'reserved_110',0
	.word	70758
	.byte	96,3,35,144,2,13
	.byte	'ERR',0
	.word	10881
	.byte	4,3,35,240,2,13
	.byte	'reserved_174',0
	.word	5189
	.byte	12,3,35,244,2,13
	.byte	'TIM',0
	.word	70767
	.byte	96,3,35,128,3,13
	.byte	'reserved_1E0',0
	.word	70776
	.byte	160,3,3,35,224,3,13
	.byte	'MCS',0
	.word	70767
	.byte	96,3,35,128,7,13
	.byte	'reserved_3E0',0
	.word	70776
	.byte	160,3,3,35,224,7,13
	.byte	'TOM',0
	.word	70787
	.byte	64,3,35,128,11,13
	.byte	'reserved_5C0',0
	.word	70796
	.byte	192,3,3,35,192,11,13
	.byte	'ATOM',0
	.word	70816
	.byte	64,3,35,128,15,13
	.byte	'reserved_7C0',0
	.word	70825
	.byte	192,2,3,35,192,15,13
	.byte	'MCSW0',0
	.word	70713
	.byte	12,3,35,128,18,13
	.byte	'reserved_90C',0
	.word	70836
	.byte	52,3,35,140,18,13
	.byte	'MCSW1',0
	.word	70713
	.byte	12,3,35,192,18,0,14
	.word	70845
	.byte	29
	.byte	'Ifx_SRC_GTM',0,11,243,1,3
	.word	71305
	.byte	10
	.byte	'_Ifx_SRC_HSCT',0,11,246,1,25,4,13
	.byte	'SR',0
	.word	10881
	.byte	4,2,35,0,0,14
	.word	71331
	.byte	29
	.byte	'Ifx_SRC_HSCT',0,11,249,1,3
	.word	71364
	.byte	10
	.byte	'_Ifx_SRC_HSSL',0,11,252,1,25,16,13
	.byte	'COK',0
	.word	10881
	.byte	4,2,35,0,13
	.byte	'RDI',0
	.word	10881
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	10881
	.byte	4,2,35,8,13
	.byte	'TRG',0
	.word	10881
	.byte	4,2,35,12,0,14
	.word	71391
	.byte	29
	.byte	'Ifx_SRC_HSSL',0,11,130,2,3
	.word	71464
	.byte	18,56
	.word	659
	.byte	19,55,0,10
	.byte	'_Ifx_SRC_I2C',0,11,133,2,25,80,13
	.byte	'BREQ',0
	.word	10881
	.byte	4,2,35,0,13
	.byte	'LBREQ',0
	.word	10881
	.byte	4,2,35,4,13
	.byte	'SREQ',0
	.word	10881
	.byte	4,2,35,8,13
	.byte	'LSREQ',0
	.word	10881
	.byte	4,2,35,12,13
	.byte	'ERR',0
	.word	10881
	.byte	4,2,35,16,13
	.byte	'P',0
	.word	10881
	.byte	4,2,35,20,13
	.byte	'reserved_18',0
	.word	71491
	.byte	56,2,35,24,0,14
	.word	71500
	.byte	29
	.byte	'Ifx_SRC_I2C',0,11,142,2,3
	.word	71623
	.byte	10
	.byte	'_Ifx_SRC_LMU',0,11,145,2,25,4,13
	.byte	'SR',0
	.word	10881
	.byte	4,2,35,0,0,14
	.word	71649
	.byte	29
	.byte	'Ifx_SRC_LMU',0,11,148,2,3
	.word	71681
	.byte	10
	.byte	'_Ifx_SRC_MSC',0,11,151,2,25,20,13
	.byte	'SR0',0
	.word	10881
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10881
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	10881
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	10881
	.byte	4,2,35,12,13
	.byte	'SR4',0
	.word	10881
	.byte	4,2,35,16,0,14
	.word	71707
	.byte	29
	.byte	'Ifx_SRC_MSC',0,11,158,2,3
	.word	71792
	.byte	10
	.byte	'_Ifx_SRC_PMU',0,11,161,2,25,4,13
	.byte	'SR',0
	.word	10881
	.byte	4,2,35,0,0,14
	.word	71818
	.byte	29
	.byte	'Ifx_SRC_PMU',0,11,164,2,3
	.word	71850
	.byte	10
	.byte	'_Ifx_SRC_PSI5',0,11,167,2,25,32,13
	.byte	'SR',0
	.word	70722
	.byte	32,2,35,0,0,14
	.word	71876
	.byte	29
	.byte	'Ifx_SRC_PSI5',0,11,170,2,3
	.word	71909
	.byte	10
	.byte	'_Ifx_SRC_PSI5S',0,11,173,2,25,32,13
	.byte	'SR',0
	.word	70722
	.byte	32,2,35,0,0,14
	.word	71936
	.byte	29
	.byte	'Ifx_SRC_PSI5S',0,11,176,2,3
	.word	71970
	.byte	10
	.byte	'_Ifx_SRC_QSPI',0,11,179,2,25,24,13
	.byte	'TX',0
	.word	10881
	.byte	4,2,35,0,13
	.byte	'RX',0
	.word	10881
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	10881
	.byte	4,2,35,8,13
	.byte	'PT',0
	.word	10881
	.byte	4,2,35,12,13
	.byte	'HC',0
	.word	10881
	.byte	4,2,35,16,13
	.byte	'U',0
	.word	10881
	.byte	4,2,35,20,0,14
	.word	71998
	.byte	29
	.byte	'Ifx_SRC_QSPI',0,11,187,2,3
	.word	72091
	.byte	10
	.byte	'_Ifx_SRC_SCR',0,11,190,2,25,4,13
	.byte	'SR',0
	.word	10881
	.byte	4,2,35,0,0,14
	.word	72118
	.byte	29
	.byte	'Ifx_SRC_SCR',0,11,193,2,3
	.word	72150
	.byte	10
	.byte	'_Ifx_SRC_SCU',0,11,196,2,25,20,13
	.byte	'DTS',0
	.word	10881
	.byte	4,2,35,0,13
	.byte	'ERU',0
	.word	70807
	.byte	16,2,35,4,0,14
	.word	72176
	.byte	29
	.byte	'Ifx_SRC_SCU',0,11,200,2,3
	.word	72222
	.byte	18,24
	.word	10881
	.byte	19,5,0,10
	.byte	'_Ifx_SRC_SENT',0,11,203,2,25,24,13
	.byte	'SR',0
	.word	72248
	.byte	24,2,35,0,0,14
	.word	72257
	.byte	29
	.byte	'Ifx_SRC_SENT',0,11,206,2,3
	.word	72290
	.byte	10
	.byte	'_Ifx_SRC_SMU',0,11,209,2,25,12,13
	.byte	'SR',0
	.word	70713
	.byte	12,2,35,0,0,14
	.word	72317
	.byte	29
	.byte	'Ifx_SRC_SMU',0,11,212,2,3
	.word	72349
	.byte	10
	.byte	'_Ifx_SRC_STM',0,11,215,2,25,8,13
	.byte	'SR0',0
	.word	10881
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10881
	.byte	4,2,35,4,0,14
	.word	72375
	.byte	29
	.byte	'Ifx_SRC_STM',0,11,219,2,3
	.word	72421
	.byte	10
	.byte	'_Ifx_SRC_VADCCG',0,11,222,2,25,16,13
	.byte	'SR0',0
	.word	10881
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10881
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	10881
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	10881
	.byte	4,2,35,12,0,14
	.word	72447
	.byte	29
	.byte	'Ifx_SRC_VADCCG',0,11,228,2,3
	.word	72522
	.byte	10
	.byte	'_Ifx_SRC_VADCG',0,11,231,2,25,16,13
	.byte	'SR0',0
	.word	10881
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10881
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	10881
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	10881
	.byte	4,2,35,12,0,14
	.word	72551
	.byte	29
	.byte	'Ifx_SRC_VADCG',0,11,237,2,3
	.word	72625
	.byte	10
	.byte	'_Ifx_SRC_XBAR',0,11,240,2,25,4,13
	.byte	'SRC',0
	.word	10881
	.byte	4,2,35,0,0,14
	.word	72653
	.byte	29
	.byte	'Ifx_SRC_XBAR',0,11,243,2,3
	.word	72687
	.byte	18,4
	.word	69243
	.byte	19,0,0,14
	.word	72714
	.byte	10
	.byte	'_Ifx_SRC_GAGBT',0,11,128,3,25,4,13
	.byte	'AGBT',0
	.word	72723
	.byte	4,2,35,0,0,14
	.word	72728
	.byte	29
	.byte	'Ifx_SRC_GAGBT',0,11,131,3,3
	.word	72764
	.byte	18,48
	.word	69301
	.byte	19,3,0,14
	.word	72792
	.byte	10
	.byte	'_Ifx_SRC_GASCLIN',0,11,134,3,25,48,13
	.byte	'ASCLIN',0
	.word	72801
	.byte	48,2,35,0,0,14
	.word	72806
	.byte	29
	.byte	'Ifx_SRC_GASCLIN',0,11,137,3,3
	.word	72846
	.byte	14
	.word	69388
	.byte	10
	.byte	'_Ifx_SRC_GBCU',0,11,140,3,25,4,13
	.byte	'SPB',0
	.word	72876
	.byte	4,2,35,0,0,14
	.word	72881
	.byte	29
	.byte	'Ifx_SRC_GBCU',0,11,143,3,3
	.word	72915
	.byte	18,64
	.word	69462
	.byte	19,0,0,14
	.word	72942
	.byte	10
	.byte	'_Ifx_SRC_GCAN',0,11,146,3,25,64,13
	.byte	'CAN',0
	.word	72951
	.byte	64,2,35,0,0,14
	.word	72956
	.byte	29
	.byte	'Ifx_SRC_GCAN',0,11,149,3,3
	.word	72990
	.byte	18,32
	.word	69519
	.byte	19,1,0,14
	.word	73017
	.byte	10
	.byte	'_Ifx_SRC_GCCU6',0,11,152,3,25,32,13
	.byte	'CCU6',0
	.word	73026
	.byte	32,2,35,0,0,14
	.word	73031
	.byte	29
	.byte	'Ifx_SRC_GCCU6',0,11,155,3,3
	.word	73067
	.byte	14
	.word	69626
	.byte	10
	.byte	'_Ifx_SRC_GCERBERUS',0,11,158,3,25,8,13
	.byte	'CERBERUS',0
	.word	73095
	.byte	8,2,35,0,0,14
	.word	73100
	.byte	29
	.byte	'Ifx_SRC_GCERBERUS',0,11,161,3,3
	.word	73144
	.byte	18,16
	.word	69692
	.byte	19,0,0,14
	.word	73176
	.byte	10
	.byte	'_Ifx_SRC_GCIF',0,11,164,3,25,16,13
	.byte	'CIF',0
	.word	73185
	.byte	16,2,35,0,0,14
	.word	73190
	.byte	29
	.byte	'Ifx_SRC_GCIF',0,11,167,3,3
	.word	73224
	.byte	18,8
	.word	69791
	.byte	19,1,0,14
	.word	73251
	.byte	10
	.byte	'_Ifx_SRC_GCPU',0,11,170,3,25,8,13
	.byte	'CPU',0
	.word	73260
	.byte	8,2,35,0,0,14
	.word	73265
	.byte	29
	.byte	'Ifx_SRC_GCPU',0,11,173,3,3
	.word	73299
	.byte	18,208,1
	.word	69862
	.byte	19,0,0,14
	.word	73326
	.byte	10
	.byte	'_Ifx_SRC_GDMA',0,11,176,3,25,208,1,13
	.byte	'DMA',0
	.word	73336
	.byte	208,1,2,35,0,0,14
	.word	73341
	.byte	29
	.byte	'Ifx_SRC_GDMA',0,11,179,3,3
	.word	73377
	.byte	14
	.word	69955
	.byte	14
	.word	69955
	.byte	14
	.word	69955
	.byte	10
	.byte	'_Ifx_SRC_GDSADC',0,11,182,3,25,32,13
	.byte	'DSADC0',0
	.word	73404
	.byte	8,2,35,0,13
	.byte	'reserved_8',0
	.word	4849
	.byte	8,2,35,8,13
	.byte	'DSADC2',0
	.word	73409
	.byte	8,2,35,16,13
	.byte	'DSADC3',0
	.word	73414
	.byte	8,2,35,24,0,14
	.word	73419
	.byte	29
	.byte	'Ifx_SRC_GDSADC',0,11,188,3,3
	.word	73510
	.byte	18,4
	.word	70031
	.byte	19,0,0,14
	.word	73539
	.byte	10
	.byte	'_Ifx_SRC_GEMEM',0,11,191,3,25,4,13
	.byte	'EMEM',0
	.word	73548
	.byte	4,2,35,0,0,14
	.word	73553
	.byte	29
	.byte	'Ifx_SRC_GEMEM',0,11,194,3,3
	.word	73589
	.byte	18,80
	.word	70091
	.byte	19,0,0,14
	.word	73617
	.byte	10
	.byte	'_Ifx_SRC_GERAY',0,11,197,3,25,80,13
	.byte	'ERAY',0
	.word	73626
	.byte	80,2,35,0,0,14
	.word	73631
	.byte	29
	.byte	'Ifx_SRC_GERAY',0,11,200,3,3
	.word	73667
	.byte	18,4
	.word	70245
	.byte	19,0,0,14
	.word	73695
	.byte	10
	.byte	'_Ifx_SRC_GETH',0,11,203,3,25,4,13
	.byte	'ETH',0
	.word	73704
	.byte	4,2,35,0,0,14
	.word	73709
	.byte	29
	.byte	'Ifx_SRC_GETH',0,11,206,3,3
	.word	73743
	.byte	18,4
	.word	70303
	.byte	19,0,0,14
	.word	73770
	.byte	10
	.byte	'_Ifx_SRC_GFCE',0,11,209,3,25,4,13
	.byte	'FCE',0
	.word	73779
	.byte	4,2,35,0,0,14
	.word	73784
	.byte	29
	.byte	'Ifx_SRC_GFCE',0,11,212,3,3
	.word	73818
	.byte	18,12
	.word	70361
	.byte	19,0,0,14
	.word	73845
	.byte	10
	.byte	'_Ifx_SRC_GFFT',0,11,215,3,25,12,13
	.byte	'FFT',0
	.word	73854
	.byte	12,2,35,0,0,14
	.word	73859
	.byte	29
	.byte	'Ifx_SRC_GFFT',0,11,218,3,3
	.word	73893
	.byte	18,64
	.word	70447
	.byte	19,1,0,14
	.word	73920
	.byte	10
	.byte	'_Ifx_SRC_GGPSR',0,11,221,3,25,64,13
	.byte	'GPSR',0
	.word	73929
	.byte	64,2,35,0,0,14
	.word	73934
	.byte	29
	.byte	'Ifx_SRC_GGPSR',0,11,224,3,3
	.word	73970
	.byte	18,48
	.word	70568
	.byte	19,0,0,14
	.word	73998
	.byte	10
	.byte	'_Ifx_SRC_GGPT12',0,11,227,3,25,48,13
	.byte	'GPT12',0
	.word	74007
	.byte	48,2,35,0,0,14
	.word	74012
	.byte	29
	.byte	'Ifx_SRC_GGPT12',0,11,230,3,3
	.word	74050
	.byte	18,204,18
	.word	70845
	.byte	19,0,0,14
	.word	74079
	.byte	10
	.byte	'_Ifx_SRC_GGTM',0,11,233,3,25,204,18,13
	.byte	'GTM',0
	.word	74089
	.byte	204,18,2,35,0,0,14
	.word	74094
	.byte	29
	.byte	'Ifx_SRC_GGTM',0,11,236,3,3
	.word	74130
	.byte	18,4
	.word	71331
	.byte	19,0,0,14
	.word	74157
	.byte	10
	.byte	'_Ifx_SRC_GHSCT',0,11,239,3,25,4,13
	.byte	'HSCT',0
	.word	74166
	.byte	4,2,35,0,0,14
	.word	74171
	.byte	29
	.byte	'Ifx_SRC_GHSCT',0,11,242,3,3
	.word	74207
	.byte	18,64
	.word	71391
	.byte	19,3,0,14
	.word	74235
	.byte	10
	.byte	'_Ifx_SRC_GHSSL',0,11,245,3,25,68,13
	.byte	'HSSL',0
	.word	74244
	.byte	64,2,35,0,13
	.byte	'EXI',0
	.word	10881
	.byte	4,2,35,64,0,14
	.word	74249
	.byte	29
	.byte	'Ifx_SRC_GHSSL',0,11,249,3,3
	.word	74298
	.byte	18,80
	.word	71500
	.byte	19,0,0,14
	.word	74326
	.byte	10
	.byte	'_Ifx_SRC_GI2C',0,11,252,3,25,80,13
	.byte	'I2C',0
	.word	74335
	.byte	80,2,35,0,0,14
	.word	74340
	.byte	29
	.byte	'Ifx_SRC_GI2C',0,11,255,3,3
	.word	74374
	.byte	18,4
	.word	71649
	.byte	19,0,0,14
	.word	74401
	.byte	10
	.byte	'_Ifx_SRC_GLMU',0,11,130,4,25,4,13
	.byte	'LMU',0
	.word	74410
	.byte	4,2,35,0,0,14
	.word	74415
	.byte	29
	.byte	'Ifx_SRC_GLMU',0,11,133,4,3
	.word	74449
	.byte	18,40
	.word	71707
	.byte	19,1,0,14
	.word	74476
	.byte	10
	.byte	'_Ifx_SRC_GMSC',0,11,136,4,25,40,13
	.byte	'MSC',0
	.word	74485
	.byte	40,2,35,0,0,14
	.word	74490
	.byte	29
	.byte	'Ifx_SRC_GMSC',0,11,139,4,3
	.word	74524
	.byte	18,8
	.word	71818
	.byte	19,1,0,14
	.word	74551
	.byte	10
	.byte	'_Ifx_SRC_GPMU',0,11,142,4,25,8,13
	.byte	'PMU',0
	.word	74560
	.byte	8,2,35,0,0,14
	.word	74565
	.byte	29
	.byte	'Ifx_SRC_GPMU',0,11,145,4,3
	.word	74599
	.byte	18,32
	.word	71876
	.byte	19,0,0,14
	.word	74626
	.byte	10
	.byte	'_Ifx_SRC_GPSI5',0,11,148,4,25,32,13
	.byte	'PSI5',0
	.word	74635
	.byte	32,2,35,0,0,14
	.word	74640
	.byte	29
	.byte	'Ifx_SRC_GPSI5',0,11,151,4,3
	.word	74676
	.byte	18,32
	.word	71936
	.byte	19,0,0,14
	.word	74704
	.byte	10
	.byte	'_Ifx_SRC_GPSI5S',0,11,154,4,25,32,13
	.byte	'PSI5S',0
	.word	74713
	.byte	32,2,35,0,0,14
	.word	74718
	.byte	29
	.byte	'Ifx_SRC_GPSI5S',0,11,157,4,3
	.word	74756
	.byte	18,96
	.word	71998
	.byte	19,3,0,14
	.word	74785
	.byte	10
	.byte	'_Ifx_SRC_GQSPI',0,11,160,4,25,96,13
	.byte	'QSPI',0
	.word	74794
	.byte	96,2,35,0,0,14
	.word	74799
	.byte	29
	.byte	'Ifx_SRC_GQSPI',0,11,163,4,3
	.word	74835
	.byte	18,4
	.word	72118
	.byte	19,0,0,14
	.word	74863
	.byte	10
	.byte	'_Ifx_SRC_GSCR',0,11,166,4,25,4,13
	.byte	'SCR',0
	.word	74872
	.byte	4,2,35,0,0,14
	.word	74877
	.byte	29
	.byte	'Ifx_SRC_GSCR',0,11,169,4,3
	.word	74911
	.byte	14
	.word	72176
	.byte	10
	.byte	'_Ifx_SRC_GSCU',0,11,172,4,25,20,13
	.byte	'SCU',0
	.word	74938
	.byte	20,2,35,0,0,14
	.word	74943
	.byte	29
	.byte	'Ifx_SRC_GSCU',0,11,175,4,3
	.word	74977
	.byte	18,24
	.word	72257
	.byte	19,0,0,14
	.word	75004
	.byte	10
	.byte	'_Ifx_SRC_GSENT',0,11,178,4,25,24,13
	.byte	'SENT',0
	.word	75013
	.byte	24,2,35,0,0,14
	.word	75018
	.byte	29
	.byte	'Ifx_SRC_GSENT',0,11,181,4,3
	.word	75054
	.byte	18,12
	.word	72317
	.byte	19,0,0,14
	.word	75082
	.byte	10
	.byte	'_Ifx_SRC_GSMU',0,11,184,4,25,12,13
	.byte	'SMU',0
	.word	75091
	.byte	12,2,35,0,0,14
	.word	75096
	.byte	29
	.byte	'Ifx_SRC_GSMU',0,11,187,4,3
	.word	75130
	.byte	18,16
	.word	72375
	.byte	19,1,0,14
	.word	75157
	.byte	10
	.byte	'_Ifx_SRC_GSTM',0,11,190,4,25,16,13
	.byte	'STM',0
	.word	75166
	.byte	16,2,35,0,0,14
	.word	75171
	.byte	29
	.byte	'Ifx_SRC_GSTM',0,11,193,4,3
	.word	75205
	.byte	18,64
	.word	72551
	.byte	19,3,0,14
	.word	75232
	.byte	18,224,1
	.word	659
	.byte	19,223,1,0,18,32
	.word	72447
	.byte	19,1,0,14
	.word	75257
	.byte	10
	.byte	'_Ifx_SRC_GVADC',0,11,196,4,25,192,2,13
	.byte	'G',0
	.word	75241
	.byte	64,2,35,0,13
	.byte	'reserved_40',0
	.word	75246
	.byte	224,1,2,35,64,13
	.byte	'CG',0
	.word	75266
	.byte	32,3,35,160,2,0,14
	.word	75271
	.byte	29
	.byte	'Ifx_SRC_GVADC',0,11,201,4,3
	.word	75340
	.byte	14
	.word	72653
	.byte	10
	.byte	'_Ifx_SRC_GXBAR',0,11,204,4,25,4,13
	.byte	'XBAR',0
	.word	75368
	.byte	4,2,35,0,0,14
	.word	75373
	.byte	29
	.byte	'Ifx_SRC_GXBAR',0,11,207,4,3
	.word	75409
	.byte	29
	.byte	'Ifx_STM_ACCEN0_Bits',0,16,79,3
	.word	20811
	.byte	29
	.byte	'Ifx_STM_ACCEN1_Bits',0,16,85,3
	.word	20722
	.byte	29
	.byte	'Ifx_STM_CAP_Bits',0,16,91,3
	.word	19252
	.byte	29
	.byte	'Ifx_STM_CAPSV_Bits',0,16,97,3
	.word	20129
	.byte	29
	.byte	'Ifx_STM_CLC_Bits',0,16,107,3
	.word	18375
	.byte	29
	.byte	'Ifx_STM_CMCON_Bits',0,16,120,3
	.word	19430
	.byte	29
	.byte	'Ifx_STM_CMP_Bits',0,16,126,3
	.word	19339
	.byte	29
	.byte	'Ifx_STM_ICR_Bits',0,16,139,1,3
	.word	19661
	.byte	29
	.byte	'Ifx_STM_ID_Bits',0,16,147,1,3
	.word	18531
	.byte	29
	.byte	'Ifx_STM_ISCR_Bits',0,16,157,1,3
	.word	19878
	.byte	29
	.byte	'Ifx_STM_KRST0_Bits',0,16,165,1,3
	.word	20599
	.byte	29
	.byte	'Ifx_STM_KRST1_Bits',0,16,172,1,3
	.word	20495
	.byte	29
	.byte	'Ifx_STM_KRSTCLR_Bits',0,16,179,1,3
	.word	20389
	.byte	29
	.byte	'Ifx_STM_OCS_Bits',0,16,189,1,3
	.word	20229
	.byte	29
	.byte	'Ifx_STM_TIM0_Bits',0,16,195,1,3
	.word	18653
	.byte	29
	.byte	'Ifx_STM_TIM0SV_Bits',0,16,201,1,3
	.word	20042
	.byte	29
	.byte	'Ifx_STM_TIM1_Bits',0,16,207,1,3
	.word	18738
	.byte	29
	.byte	'Ifx_STM_TIM2_Bits',0,16,213,1,3
	.word	18823
	.byte	29
	.byte	'Ifx_STM_TIM3_Bits',0,16,219,1,3
	.word	18908
	.byte	29
	.byte	'Ifx_STM_TIM4_Bits',0,16,225,1,3
	.word	18994
	.byte	29
	.byte	'Ifx_STM_TIM5_Bits',0,16,231,1,3
	.word	19080
	.byte	29
	.byte	'Ifx_STM_TIM6_Bits',0,16,237,1,3
	.word	19166
	.byte	29
	.byte	'Ifx_STM_ACCEN0',0,16,250,1,3
	.word	21340
	.byte	29
	.byte	'Ifx_STM_ACCEN1',0,16,130,2,3
	.word	20771
	.byte	29
	.byte	'Ifx_STM_CAP',0,16,138,2,3
	.word	19299
	.byte	29
	.byte	'Ifx_STM_CAPSV',0,16,146,2,3
	.word	20178
	.byte	29
	.byte	'Ifx_STM_CLC',0,16,154,2,3
	.word	18491
	.byte	29
	.byte	'Ifx_STM_CMCON',0,16,162,2,3
	.word	19621
	.byte	29
	.byte	'Ifx_STM_CMP',0,16,170,2,3
	.word	19381
	.byte	29
	.byte	'Ifx_STM_ICR',0,16,178,2,3
	.word	19838
	.byte	29
	.byte	'Ifx_STM_ID',0,16,186,2,3
	.word	18613
	.byte	29
	.byte	'Ifx_STM_ISCR',0,16,194,2,3
	.word	20002
	.byte	29
	.byte	'Ifx_STM_KRST0',0,16,202,2,3
	.word	20682
	.byte	29
	.byte	'Ifx_STM_KRST1',0,16,210,2,3
	.word	20559
	.byte	29
	.byte	'Ifx_STM_KRSTCLR',0,16,218,2,3
	.word	20455
	.byte	29
	.byte	'Ifx_STM_OCS',0,16,226,2,3
	.word	20349
	.byte	29
	.byte	'Ifx_STM_TIM0',0,16,234,2,3
	.word	18698
	.byte	29
	.byte	'Ifx_STM_TIM0SV',0,16,242,2,3
	.word	20089
	.byte	29
	.byte	'Ifx_STM_TIM1',0,16,250,2,3
	.word	18783
	.byte	29
	.byte	'Ifx_STM_TIM2',0,16,130,3,3
	.word	18868
	.byte	29
	.byte	'Ifx_STM_TIM3',0,16,138,3,3
	.word	18954
	.byte	29
	.byte	'Ifx_STM_TIM4',0,16,146,3,3
	.word	19040
	.byte	29
	.byte	'Ifx_STM_TIM5',0,16,154,3,3
	.word	19126
	.byte	29
	.byte	'Ifx_STM_TIM6',0,16,162,3,3
	.word	19212
	.byte	14
	.word	21380
	.byte	29
	.byte	'Ifx_STM',0,16,201,3,3
	.word	76520
	.byte	15,5,144,1,9,1,16
	.byte	'IfxCpu_CounterMode_normal',0,0,16
	.byte	'IfxCpu_CounterMode_task',0,1,0,29
	.byte	'IfxCpu_CounterMode',0,5,148,1,3
	.word	76542
	.byte	20,5,160,1,9,6,13
	.byte	'counter',0
	.word	1793
	.byte	4,2,35,0,13
	.byte	'overlfow',0
	.word	659
	.byte	1,2,35,4,0,29
	.byte	'IfxCpu_Counter',0,5,164,1,3
	.word	76631
	.byte	20,5,172,1,9,32,13
	.byte	'instruction',0
	.word	76631
	.byte	6,2,35,0,13
	.byte	'clock',0
	.word	76631
	.byte	6,2,35,6,13
	.byte	'counter1',0
	.word	76631
	.byte	6,2,35,12,13
	.byte	'counter2',0
	.word	76631
	.byte	6,2,35,18,13
	.byte	'counter3',0
	.word	76631
	.byte	6,2,35,24,0,29
	.byte	'IfxCpu_Perf',0,5,179,1,3
	.word	76697
	.byte	10
	.byte	'_Ifx_FLASH_ACCEN0_Bits',0,33,45,16,4,11
	.byte	'EN0',0,1
	.word	659
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	659
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	659
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	659
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	659
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	659
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	659
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	659
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	659
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	659
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	659
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	659
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	659
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	659
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	659
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	659
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	659
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	659
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	659
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	659
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	659
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	659
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	659
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	659
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	659
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	659
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	659
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	659
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	659
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	659
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	659
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	659
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_FLASH_ACCEN0_Bits',0,33,79,3
	.word	76815
	.byte	10
	.byte	'_Ifx_FLASH_ACCEN1_Bits',0,33,82,16,4,11
	.byte	'reserved_0',0,4
	.word	484
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_FLASH_ACCEN1_Bits',0,33,85,3
	.word	77376
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_CFG_Bits',0,33,88,16,4,11
	.byte	'SEL',0,1
	.word	659
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	659
	.byte	2,0,2,35,0,11
	.byte	'CLR',0,1
	.word	659
	.byte	1,7,2,35,1,11
	.byte	'DIS',0,1
	.word	659
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	484
	.byte	22,0,2,35,0,0,29
	.byte	'Ifx_FLASH_CBAB_CFG_Bits',0,33,95,3
	.word	77457
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_STAT_Bits',0,33,98,16,4,11
	.byte	'VLD0',0,1
	.word	659
	.byte	1,7,2,35,0,11
	.byte	'VLD1',0,1
	.word	659
	.byte	1,6,2,35,0,11
	.byte	'VLD2',0,1
	.word	659
	.byte	1,5,2,35,0,11
	.byte	'VLD3',0,1
	.word	659
	.byte	1,4,2,35,0,11
	.byte	'VLD4',0,1
	.word	659
	.byte	1,3,2,35,0,11
	.byte	'VLD5',0,1
	.word	659
	.byte	1,2,2,35,0,11
	.byte	'VLD6',0,1
	.word	659
	.byte	1,1,2,35,0,11
	.byte	'VLD7',0,1
	.word	659
	.byte	1,0,2,35,0,11
	.byte	'VLD8',0,1
	.word	659
	.byte	1,7,2,35,1,11
	.byte	'VLD9',0,1
	.word	659
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	484
	.byte	22,0,2,35,0,0,29
	.byte	'Ifx_FLASH_CBAB_STAT_Bits',0,33,111,3
	.word	77610
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_TOP_Bits',0,33,114,16,4,11
	.byte	'reserved_0',0,1
	.word	659
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	484
	.byte	19,8,2,35,0,11
	.byte	'ERR',0,1
	.word	659
	.byte	6,2,2,35,3,11
	.byte	'VLD',0,1
	.word	659
	.byte	1,1,2,35,3,11
	.byte	'CLR',0,1
	.word	659
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_FLASH_CBAB_TOP_Bits',0,33,121,3
	.word	77858
	.byte	10
	.byte	'_Ifx_FLASH_COMM0_Bits',0,33,124,16,4,11
	.byte	'STATUS',0,1
	.word	659
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	484
	.byte	24,0,2,35,0,0,29
	.byte	'Ifx_FLASH_COMM0_Bits',0,33,128,1,3
	.word	78004
	.byte	10
	.byte	'_Ifx_FLASH_COMM1_Bits',0,33,131,1,16,4,11
	.byte	'STATUS',0,1
	.word	659
	.byte	8,0,2,35,0,11
	.byte	'DATA',0,1
	.word	659
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	676
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_FLASH_COMM1_Bits',0,33,136,1,3
	.word	78102
	.byte	10
	.byte	'_Ifx_FLASH_COMM2_Bits',0,33,139,1,16,4,11
	.byte	'STATUS',0,1
	.word	659
	.byte	8,0,2,35,0,11
	.byte	'DATA',0,1
	.word	659
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	676
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_FLASH_COMM2_Bits',0,33,144,1,3
	.word	78218
	.byte	10
	.byte	'_Ifx_FLASH_ECCRD_Bits',0,33,147,1,16,4,11
	.byte	'RCODE',0,4
	.word	484
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	676
	.byte	8,2,2,35,2,11
	.byte	'EDCERRINJ',0,1
	.word	659
	.byte	1,1,2,35,3,11
	.byte	'ECCORDIS',0,1
	.word	659
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_FLASH_ECCRD_Bits',0,33,153,1,3
	.word	78334
	.byte	10
	.byte	'_Ifx_FLASH_ECCRP_Bits',0,33,156,1,16,4,11
	.byte	'RCODE',0,4
	.word	484
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	676
	.byte	8,2,2,35,2,11
	.byte	'EDCERRINJ',0,1
	.word	659
	.byte	1,1,2,35,3,11
	.byte	'ECCORDIS',0,1
	.word	659
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_FLASH_ECCRP_Bits',0,33,162,1,3
	.word	78474
	.byte	10
	.byte	'_Ifx_FLASH_ECCW_Bits',0,33,165,1,16,4,11
	.byte	'WCODE',0,4
	.word	484
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	676
	.byte	8,2,2,35,2,11
	.byte	'DECENCDIS',0,1
	.word	659
	.byte	1,1,2,35,3,11
	.byte	'PECENCDIS',0,1
	.word	659
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_FLASH_ECCW_Bits',0,33,171,1,3
	.word	78614
	.byte	10
	.byte	'_Ifx_FLASH_FCON_Bits',0,33,174,1,16,4,11
	.byte	'WSPFLASH',0,1
	.word	659
	.byte	4,4,2,35,0,11
	.byte	'WSECPF',0,1
	.word	659
	.byte	2,2,2,35,0,11
	.byte	'WSDFLASH',0,2
	.word	676
	.byte	6,4,2,35,0,11
	.byte	'WSECDF',0,1
	.word	659
	.byte	3,1,2,35,1,11
	.byte	'IDLE',0,1
	.word	659
	.byte	1,0,2,35,1,11
	.byte	'ESLDIS',0,1
	.word	659
	.byte	1,7,2,35,2,11
	.byte	'SLEEP',0,1
	.word	659
	.byte	1,6,2,35,2,11
	.byte	'NSAFECC',0,1
	.word	659
	.byte	1,5,2,35,2,11
	.byte	'STALL',0,1
	.word	659
	.byte	1,4,2,35,2,11
	.byte	'RES21',0,1
	.word	659
	.byte	2,2,2,35,2,11
	.byte	'RES23',0,1
	.word	659
	.byte	2,0,2,35,2,11
	.byte	'VOPERM',0,1
	.word	659
	.byte	1,7,2,35,3,11
	.byte	'SQERM',0,1
	.word	659
	.byte	1,6,2,35,3,11
	.byte	'PROERM',0,1
	.word	659
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	659
	.byte	3,2,2,35,3,11
	.byte	'PR5V',0,1
	.word	659
	.byte	1,1,2,35,3,11
	.byte	'EOBM',0,1
	.word	659
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_FLASH_FCON_Bits',0,33,193,1,3
	.word	78753
	.byte	10
	.byte	'_Ifx_FLASH_FPRO_Bits',0,33,196,1,16,4,11
	.byte	'PROINP',0,1
	.word	659
	.byte	1,7,2,35,0,11
	.byte	'PRODISP',0,1
	.word	659
	.byte	1,6,2,35,0,11
	.byte	'PROIND',0,1
	.word	659
	.byte	1,5,2,35,0,11
	.byte	'PRODISD',0,1
	.word	659
	.byte	1,4,2,35,0,11
	.byte	'PROINHSMCOTP',0,1
	.word	659
	.byte	1,3,2,35,0,11
	.byte	'RES5',0,1
	.word	659
	.byte	1,2,2,35,0,11
	.byte	'PROINOTP',0,1
	.word	659
	.byte	1,1,2,35,0,11
	.byte	'RES7',0,1
	.word	659
	.byte	1,0,2,35,0,11
	.byte	'PROINDBG',0,1
	.word	659
	.byte	1,7,2,35,1,11
	.byte	'PRODISDBG',0,1
	.word	659
	.byte	1,6,2,35,1,11
	.byte	'PROINHSM',0,1
	.word	659
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	659
	.byte	5,0,2,35,1,11
	.byte	'DCFP',0,1
	.word	659
	.byte	1,7,2,35,2,11
	.byte	'DDFP',0,1
	.word	659
	.byte	1,6,2,35,2,11
	.byte	'DDFPX',0,1
	.word	659
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	659
	.byte	1,4,2,35,2,11
	.byte	'DDFD',0,1
	.word	659
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	659
	.byte	1,2,2,35,2,11
	.byte	'ENPE',0,1
	.word	659
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	659
	.byte	8,0,2,35,3,0,29
	.byte	'Ifx_FLASH_FPRO_Bits',0,33,218,1,3
	.word	79115
	.byte	10
	.byte	'_Ifx_FLASH_FSR_Bits',0,33,221,1,16,4,11
	.byte	'FABUSY',0,1
	.word	659
	.byte	1,7,2,35,0,11
	.byte	'D0BUSY',0,1
	.word	659
	.byte	1,6,2,35,0,11
	.byte	'RES1',0,1
	.word	659
	.byte	1,5,2,35,0,11
	.byte	'P0BUSY',0,1
	.word	659
	.byte	1,4,2,35,0,11
	.byte	'P1BUSY',0,1
	.word	659
	.byte	1,3,2,35,0,11
	.byte	'RES5',0,1
	.word	659
	.byte	1,2,2,35,0,11
	.byte	'RES6',0,1
	.word	659
	.byte	1,1,2,35,0,11
	.byte	'PROG',0,1
	.word	659
	.byte	1,0,2,35,0,11
	.byte	'ERASE',0,1
	.word	659
	.byte	1,7,2,35,1,11
	.byte	'PFPAGE',0,1
	.word	659
	.byte	1,6,2,35,1,11
	.byte	'DFPAGE',0,1
	.word	659
	.byte	1,5,2,35,1,11
	.byte	'OPER',0,1
	.word	659
	.byte	1,4,2,35,1,11
	.byte	'SQER',0,1
	.word	659
	.byte	1,3,2,35,1,11
	.byte	'PROER',0,1
	.word	659
	.byte	1,2,2,35,1,11
	.byte	'PFSBER',0,1
	.word	659
	.byte	1,1,2,35,1,11
	.byte	'PFDBER',0,1
	.word	659
	.byte	1,0,2,35,1,11
	.byte	'PFMBER',0,1
	.word	659
	.byte	1,7,2,35,2,11
	.byte	'RES17',0,1
	.word	659
	.byte	1,6,2,35,2,11
	.byte	'DFSBER',0,1
	.word	659
	.byte	1,5,2,35,2,11
	.byte	'DFDBER',0,1
	.word	659
	.byte	1,4,2,35,2,11
	.byte	'DFTBER',0,1
	.word	659
	.byte	1,3,2,35,2,11
	.byte	'DFMBER',0,1
	.word	659
	.byte	1,2,2,35,2,11
	.byte	'SRIADDERR',0,1
	.word	659
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	676
	.byte	2,7,2,35,2,11
	.byte	'PVER',0,1
	.word	659
	.byte	1,6,2,35,3,11
	.byte	'EVER',0,1
	.word	659
	.byte	1,5,2,35,3,11
	.byte	'SPND',0,1
	.word	659
	.byte	1,4,2,35,3,11
	.byte	'SLM',0,1
	.word	659
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	659
	.byte	1,2,2,35,3,11
	.byte	'ORIER',0,1
	.word	659
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	659
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_FLASH_FSR_Bits',0,33,254,1,3
	.word	79556
	.byte	10
	.byte	'_Ifx_FLASH_ID_Bits',0,33,129,2,16,4,11
	.byte	'MODREV',0,1
	.word	659
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	659
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	676
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_FLASH_ID_Bits',0,33,134,2,3
	.word	80162
	.byte	10
	.byte	'_Ifx_FLASH_MARD_Bits',0,33,137,2,16,4,11
	.byte	'HMARGIN',0,1
	.word	659
	.byte	1,7,2,35,0,11
	.byte	'SELD0',0,1
	.word	659
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	659
	.byte	1,5,2,35,0,11
	.byte	'SPND',0,1
	.word	659
	.byte	1,4,2,35,0,11
	.byte	'SPNDERR',0,1
	.word	659
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,2
	.word	676
	.byte	10,1,2,35,0,11
	.byte	'TRAPDIS',0,1
	.word	659
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	676
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_FLASH_MARD_Bits',0,33,147,2,3
	.word	80273
	.byte	10
	.byte	'_Ifx_FLASH_MARP_Bits',0,33,150,2,16,4,11
	.byte	'SELP0',0,1
	.word	659
	.byte	1,7,2,35,0,11
	.byte	'SELP1',0,1
	.word	659
	.byte	1,6,2,35,0,11
	.byte	'RES2',0,1
	.word	659
	.byte	1,5,2,35,0,11
	.byte	'RES3',0,1
	.word	659
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	676
	.byte	11,1,2,35,0,11
	.byte	'TRAPDIS',0,1
	.word	659
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	676
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_FLASH_MARP_Bits',0,33,159,2,3
	.word	80487
	.byte	10
	.byte	'_Ifx_FLASH_PROCOND_Bits',0,33,162,2,16,4,11
	.byte	'L',0,1
	.word	659
	.byte	1,7,2,35,0,11
	.byte	'NSAFECC',0,1
	.word	659
	.byte	1,6,2,35,0,11
	.byte	'RAMIN',0,1
	.word	659
	.byte	2,4,2,35,0,11
	.byte	'RAMINSEL',0,1
	.word	659
	.byte	4,0,2,35,0,11
	.byte	'OSCCFG',0,1
	.word	659
	.byte	1,7,2,35,1,11
	.byte	'MODE',0,1
	.word	659
	.byte	2,5,2,35,1,11
	.byte	'APREN',0,1
	.word	659
	.byte	1,4,2,35,1,11
	.byte	'CAP0EN',0,1
	.word	659
	.byte	1,3,2,35,1,11
	.byte	'CAP1EN',0,1
	.word	659
	.byte	1,2,2,35,1,11
	.byte	'CAP2EN',0,1
	.word	659
	.byte	1,1,2,35,1,11
	.byte	'CAP3EN',0,1
	.word	659
	.byte	1,0,2,35,1,11
	.byte	'ESR0CNT',0,2
	.word	676
	.byte	12,4,2,35,2,11
	.byte	'RES29',0,1
	.word	659
	.byte	2,2,2,35,3,11
	.byte	'RES30',0,1
	.word	659
	.byte	1,1,2,35,3,11
	.byte	'RPRO',0,1
	.word	659
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_FLASH_PROCOND_Bits',0,33,179,2,3
	.word	80674
	.byte	10
	.byte	'_Ifx_FLASH_PROCONDBG_Bits',0,33,182,2,16,4,11
	.byte	'OCDSDIS',0,1
	.word	659
	.byte	1,7,2,35,0,11
	.byte	'DBGIFLCK',0,1
	.word	659
	.byte	1,6,2,35,0,11
	.byte	'EDM',0,1
	.word	659
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	484
	.byte	28,0,2,35,0,0,29
	.byte	'Ifx_FLASH_PROCONDBG_Bits',0,33,188,2,3
	.word	80998
	.byte	10
	.byte	'_Ifx_FLASH_PROCONHSM_Bits',0,33,191,2,16,4,11
	.byte	'HSMDBGDIS',0,1
	.word	659
	.byte	1,7,2,35,0,11
	.byte	'DBGIFLCK',0,1
	.word	659
	.byte	1,6,2,35,0,11
	.byte	'TSTIFLCK',0,1
	.word	659
	.byte	1,5,2,35,0,11
	.byte	'HSMTSTDIS',0,1
	.word	659
	.byte	1,4,2,35,0,11
	.byte	'RES15',0,2
	.word	676
	.byte	12,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	676
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_FLASH_PROCONHSM_Bits',0,33,199,2,3
	.word	81141
	.byte	10
	.byte	'_Ifx_FLASH_PROCONHSMCOTP_Bits',0,33,202,2,16,4,11
	.byte	'HSMBOOTEN',0,1
	.word	659
	.byte	1,7,2,35,0,11
	.byte	'SSWWAIT',0,1
	.word	659
	.byte	1,6,2,35,0,11
	.byte	'HSMDX',0,1
	.word	659
	.byte	1,5,2,35,0,11
	.byte	'HSM6X',0,1
	.word	659
	.byte	1,4,2,35,0,11
	.byte	'HSM16X',0,1
	.word	659
	.byte	1,3,2,35,0,11
	.byte	'HSM17X',0,1
	.word	659
	.byte	1,2,2,35,0,11
	.byte	'S6ROM',0,1
	.word	659
	.byte	1,1,2,35,0,11
	.byte	'HSMENPINS',0,2
	.word	676
	.byte	2,7,2,35,0,11
	.byte	'HSMENRES',0,1
	.word	659
	.byte	2,5,2,35,1,11
	.byte	'DESTDBG',0,1
	.word	659
	.byte	2,3,2,35,1,11
	.byte	'BLKFLAN',0,1
	.word	659
	.byte	1,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	659
	.byte	2,0,2,35,1,11
	.byte	'S16ROM',0,1
	.word	659
	.byte	1,7,2,35,2,11
	.byte	'S17ROM',0,1
	.word	659
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	676
	.byte	14,0,2,35,2,0,29
	.byte	'Ifx_FLASH_PROCONHSMCOTP_Bits',0,33,219,2,3
	.word	81330
	.byte	10
	.byte	'_Ifx_FLASH_PROCONOTP_Bits',0,33,222,2,16,4,11
	.byte	'S0ROM',0,1
	.word	659
	.byte	1,7,2,35,0,11
	.byte	'S1ROM',0,1
	.word	659
	.byte	1,6,2,35,0,11
	.byte	'S2ROM',0,1
	.word	659
	.byte	1,5,2,35,0,11
	.byte	'S3ROM',0,1
	.word	659
	.byte	1,4,2,35,0,11
	.byte	'S4ROM',0,1
	.word	659
	.byte	1,3,2,35,0,11
	.byte	'S5ROM',0,1
	.word	659
	.byte	1,2,2,35,0,11
	.byte	'S6ROM',0,1
	.word	659
	.byte	1,1,2,35,0,11
	.byte	'S7ROM',0,1
	.word	659
	.byte	1,0,2,35,0,11
	.byte	'S8ROM',0,1
	.word	659
	.byte	1,7,2,35,1,11
	.byte	'S9ROM',0,1
	.word	659
	.byte	1,6,2,35,1,11
	.byte	'S10ROM',0,1
	.word	659
	.byte	1,5,2,35,1,11
	.byte	'S11ROM',0,1
	.word	659
	.byte	1,4,2,35,1,11
	.byte	'S12ROM',0,1
	.word	659
	.byte	1,3,2,35,1,11
	.byte	'S13ROM',0,1
	.word	659
	.byte	1,2,2,35,1,11
	.byte	'S14ROM',0,1
	.word	659
	.byte	1,1,2,35,1,11
	.byte	'S15ROM',0,1
	.word	659
	.byte	1,0,2,35,1,11
	.byte	'S16ROM',0,1
	.word	659
	.byte	1,7,2,35,2,11
	.byte	'S17ROM',0,1
	.word	659
	.byte	1,6,2,35,2,11
	.byte	'S18ROM',0,1
	.word	659
	.byte	1,5,2,35,2,11
	.byte	'S19ROM',0,1
	.word	659
	.byte	1,4,2,35,2,11
	.byte	'S20ROM',0,1
	.word	659
	.byte	1,3,2,35,2,11
	.byte	'S21ROM',0,1
	.word	659
	.byte	1,2,2,35,2,11
	.byte	'S22ROM',0,1
	.word	659
	.byte	1,1,2,35,2,11
	.byte	'S23ROM',0,1
	.word	659
	.byte	1,0,2,35,2,11
	.byte	'S24ROM',0,1
	.word	659
	.byte	1,7,2,35,3,11
	.byte	'S25ROM',0,1
	.word	659
	.byte	1,6,2,35,3,11
	.byte	'S26ROM',0,1
	.word	659
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	659
	.byte	2,3,2,35,3,11
	.byte	'BML',0,1
	.word	659
	.byte	2,1,2,35,3,11
	.byte	'TP',0,1
	.word	659
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_FLASH_PROCONOTP_Bits',0,33,254,2,3
	.word	81693
	.byte	10
	.byte	'_Ifx_FLASH_PROCONP_Bits',0,33,129,3,16,4,11
	.byte	'S0L',0,1
	.word	659
	.byte	1,7,2,35,0,11
	.byte	'S1L',0,1
	.word	659
	.byte	1,6,2,35,0,11
	.byte	'S2L',0,1
	.word	659
	.byte	1,5,2,35,0,11
	.byte	'S3L',0,1
	.word	659
	.byte	1,4,2,35,0,11
	.byte	'S4L',0,1
	.word	659
	.byte	1,3,2,35,0,11
	.byte	'S5L',0,1
	.word	659
	.byte	1,2,2,35,0,11
	.byte	'S6L',0,1
	.word	659
	.byte	1,1,2,35,0,11
	.byte	'S7L',0,1
	.word	659
	.byte	1,0,2,35,0,11
	.byte	'S8L',0,1
	.word	659
	.byte	1,7,2,35,1,11
	.byte	'S9L',0,1
	.word	659
	.byte	1,6,2,35,1,11
	.byte	'S10L',0,1
	.word	659
	.byte	1,5,2,35,1,11
	.byte	'S11L',0,1
	.word	659
	.byte	1,4,2,35,1,11
	.byte	'S12L',0,1
	.word	659
	.byte	1,3,2,35,1,11
	.byte	'S13L',0,1
	.word	659
	.byte	1,2,2,35,1,11
	.byte	'S14L',0,1
	.word	659
	.byte	1,1,2,35,1,11
	.byte	'S15L',0,1
	.word	659
	.byte	1,0,2,35,1,11
	.byte	'S16L',0,1
	.word	659
	.byte	1,7,2,35,2,11
	.byte	'S17L',0,1
	.word	659
	.byte	1,6,2,35,2,11
	.byte	'S18L',0,1
	.word	659
	.byte	1,5,2,35,2,11
	.byte	'S19L',0,1
	.word	659
	.byte	1,4,2,35,2,11
	.byte	'S20L',0,1
	.word	659
	.byte	1,3,2,35,2,11
	.byte	'S21L',0,1
	.word	659
	.byte	1,2,2,35,2,11
	.byte	'S22L',0,1
	.word	659
	.byte	1,1,2,35,2,11
	.byte	'S23L',0,1
	.word	659
	.byte	1,0,2,35,2,11
	.byte	'S24L',0,1
	.word	659
	.byte	1,7,2,35,3,11
	.byte	'S25L',0,1
	.word	659
	.byte	1,6,2,35,3,11
	.byte	'S26L',0,1
	.word	659
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	659
	.byte	4,1,2,35,3,11
	.byte	'RPRO',0,1
	.word	659
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_FLASH_PROCONP_Bits',0,33,160,3,3
	.word	82288
	.byte	10
	.byte	'_Ifx_FLASH_PROCONWOP_Bits',0,33,163,3,16,4,11
	.byte	'S0WOP',0,1
	.word	659
	.byte	1,7,2,35,0,11
	.byte	'S1WOP',0,1
	.word	659
	.byte	1,6,2,35,0,11
	.byte	'S2WOP',0,1
	.word	659
	.byte	1,5,2,35,0,11
	.byte	'S3WOP',0,1
	.word	659
	.byte	1,4,2,35,0,11
	.byte	'S4WOP',0,1
	.word	659
	.byte	1,3,2,35,0,11
	.byte	'S5WOP',0,1
	.word	659
	.byte	1,2,2,35,0,11
	.byte	'S6WOP',0,1
	.word	659
	.byte	1,1,2,35,0,11
	.byte	'S7WOP',0,1
	.word	659
	.byte	1,0,2,35,0,11
	.byte	'S8WOP',0,1
	.word	659
	.byte	1,7,2,35,1,11
	.byte	'S9WOP',0,1
	.word	659
	.byte	1,6,2,35,1,11
	.byte	'S10WOP',0,1
	.word	659
	.byte	1,5,2,35,1,11
	.byte	'S11WOP',0,1
	.word	659
	.byte	1,4,2,35,1,11
	.byte	'S12WOP',0,1
	.word	659
	.byte	1,3,2,35,1,11
	.byte	'S13WOP',0,1
	.word	659
	.byte	1,2,2,35,1,11
	.byte	'S14WOP',0,1
	.word	659
	.byte	1,1,2,35,1,11
	.byte	'S15WOP',0,1
	.word	659
	.byte	1,0,2,35,1,11
	.byte	'S16WOP',0,1
	.word	659
	.byte	1,7,2,35,2,11
	.byte	'S17WOP',0,1
	.word	659
	.byte	1,6,2,35,2,11
	.byte	'S18WOP',0,1
	.word	659
	.byte	1,5,2,35,2,11
	.byte	'S19WOP',0,1
	.word	659
	.byte	1,4,2,35,2,11
	.byte	'S20WOP',0,1
	.word	659
	.byte	1,3,2,35,2,11
	.byte	'S21WOP',0,1
	.word	659
	.byte	1,2,2,35,2,11
	.byte	'S22WOP',0,1
	.word	659
	.byte	1,1,2,35,2,11
	.byte	'S23WOP',0,1
	.word	659
	.byte	1,0,2,35,2,11
	.byte	'S24WOP',0,1
	.word	659
	.byte	1,7,2,35,3,11
	.byte	'S25WOP',0,1
	.word	659
	.byte	1,6,2,35,3,11
	.byte	'S26WOP',0,1
	.word	659
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	659
	.byte	4,1,2,35,3,11
	.byte	'DATM',0,1
	.word	659
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_FLASH_PROCONWOP_Bits',0,33,194,3,3
	.word	82812
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG0_Bits',0,33,197,3,16,4,11
	.byte	'TAG',0,1
	.word	659
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	484
	.byte	26,0,2,35,0,0,29
	.byte	'Ifx_FLASH_RDB_CFG0_Bits',0,33,201,3,3
	.word	83394
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG1_Bits',0,33,204,3,16,4,11
	.byte	'TAG',0,1
	.word	659
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	484
	.byte	26,0,2,35,0,0,29
	.byte	'Ifx_FLASH_RDB_CFG1_Bits',0,33,208,3,3
	.word	83496
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG2_Bits',0,33,211,3,16,4,11
	.byte	'TAG',0,1
	.word	659
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	484
	.byte	26,0,2,35,0,0,29
	.byte	'Ifx_FLASH_RDB_CFG2_Bits',0,33,215,3,3
	.word	83598
	.byte	10
	.byte	'_Ifx_FLASH_RRAD_Bits',0,33,218,3,16,4,11
	.byte	'reserved_0',0,1
	.word	659
	.byte	3,5,2,35,0,11
	.byte	'ADD',0,4
	.word	484
	.byte	29,0,2,35,0,0,29
	.byte	'Ifx_FLASH_RRAD_Bits',0,33,222,3,3
	.word	83700
	.byte	10
	.byte	'_Ifx_FLASH_RRCT_Bits',0,33,225,3,16,4,11
	.byte	'STRT',0,1
	.word	659
	.byte	1,7,2,35,0,11
	.byte	'STP',0,1
	.word	659
	.byte	1,6,2,35,0,11
	.byte	'BUSY',0,1
	.word	659
	.byte	1,5,2,35,0,11
	.byte	'DONE',0,1
	.word	659
	.byte	1,4,2,35,0,11
	.byte	'ERR',0,1
	.word	659
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	659
	.byte	3,0,2,35,0,11
	.byte	'EOBM',0,1
	.word	659
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	659
	.byte	7,0,2,35,1,11
	.byte	'CNT',0,2
	.word	676
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_FLASH_RRCT_Bits',0,33,236,3,3
	.word	83794
	.byte	10
	.byte	'_Ifx_FLASH_RRD0_Bits',0,33,239,3,16,4,11
	.byte	'DATA',0,4
	.word	484
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_FLASH_RRD0_Bits',0,33,242,3,3
	.word	84004
	.byte	10
	.byte	'_Ifx_FLASH_RRD1_Bits',0,33,245,3,16,4,11
	.byte	'DATA',0,4
	.word	484
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_FLASH_RRD1_Bits',0,33,248,3,3
	.word	84077
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_CFG_Bits',0,33,251,3,16,4,11
	.byte	'SEL',0,1
	.word	659
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	659
	.byte	2,0,2,35,0,11
	.byte	'CLR',0,1
	.word	659
	.byte	1,7,2,35,1,11
	.byte	'DIS',0,1
	.word	659
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	484
	.byte	22,0,2,35,0,0,29
	.byte	'Ifx_FLASH_UBAB_CFG_Bits',0,33,130,4,3
	.word	84150
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_STAT_Bits',0,33,133,4,16,4,11
	.byte	'VLD0',0,1
	.word	659
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	484
	.byte	31,0,2,35,0,0,29
	.byte	'Ifx_FLASH_UBAB_STAT_Bits',0,33,137,4,3
	.word	84305
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_TOP_Bits',0,33,140,4,16,4,11
	.byte	'reserved_0',0,1
	.word	659
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	484
	.byte	19,8,2,35,0,11
	.byte	'ERR',0,1
	.word	659
	.byte	6,2,2,35,3,11
	.byte	'VLD',0,1
	.word	659
	.byte	1,1,2,35,3,11
	.byte	'CLR',0,1
	.word	659
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_FLASH_UBAB_TOP_Bits',0,33,147,4,3
	.word	84410
	.byte	12,33,155,4,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	76815
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_ACCEN0',0,33,160,4,3
	.word	84558
	.byte	12,33,163,4,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	77376
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_ACCEN1',0,33,168,4,3
	.word	84624
	.byte	12,33,171,4,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	77457
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_CBAB_CFG',0,33,176,4,3
	.word	84690
	.byte	12,33,179,4,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	77610
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_CBAB_STAT',0,33,184,4,3
	.word	84758
	.byte	12,33,187,4,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	77858
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_CBAB_TOP',0,33,192,4,3
	.word	84827
	.byte	12,33,195,4,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	78004
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_COMM0',0,33,200,4,3
	.word	84895
	.byte	12,33,203,4,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	78102
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_COMM1',0,33,208,4,3
	.word	84960
	.byte	12,33,211,4,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	78218
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_COMM2',0,33,216,4,3
	.word	85025
	.byte	12,33,219,4,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	78334
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_ECCRD',0,33,224,4,3
	.word	85090
	.byte	12,33,227,4,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	78474
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_ECCRP',0,33,232,4,3
	.word	85155
	.byte	12,33,235,4,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	78614
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_ECCW',0,33,240,4,3
	.word	85220
	.byte	12,33,243,4,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	78753
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_FCON',0,33,248,4,3
	.word	85284
	.byte	12,33,251,4,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	79115
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_FPRO',0,33,128,5,3
	.word	85348
	.byte	12,33,131,5,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	79556
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_FSR',0,33,136,5,3
	.word	85412
	.byte	12,33,139,5,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	80162
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_ID',0,33,144,5,3
	.word	85475
	.byte	12,33,147,5,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	80273
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_MARD',0,33,152,5,3
	.word	85537
	.byte	12,33,155,5,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	80487
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_MARP',0,33,160,5,3
	.word	85601
	.byte	12,33,163,5,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	80674
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_PROCOND',0,33,168,5,3
	.word	85665
	.byte	12,33,171,5,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	80998
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_PROCONDBG',0,33,176,5,3
	.word	85732
	.byte	12,33,179,5,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81141
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_PROCONHSM',0,33,184,5,3
	.word	85801
	.byte	12,33,187,5,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81330
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_PROCONHSMCOTP',0,33,192,5,3
	.word	85870
	.byte	12,33,195,5,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81693
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_PROCONOTP',0,33,200,5,3
	.word	85943
	.byte	12,33,203,5,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	82288
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_PROCONP',0,33,208,5,3
	.word	86012
	.byte	12,33,211,5,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	82812
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_PROCONWOP',0,33,216,5,3
	.word	86079
	.byte	12,33,219,5,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	83394
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_RDB_CFG0',0,33,224,5,3
	.word	86148
	.byte	12,33,227,5,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	83496
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_RDB_CFG1',0,33,232,5,3
	.word	86216
	.byte	12,33,235,5,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	83598
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_RDB_CFG2',0,33,240,5,3
	.word	86284
	.byte	12,33,243,5,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	83700
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_RRAD',0,33,248,5,3
	.word	86352
	.byte	12,33,251,5,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	83794
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_RRCT',0,33,128,6,3
	.word	86416
	.byte	12,33,131,6,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	84004
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_RRD0',0,33,136,6,3
	.word	86480
	.byte	12,33,139,6,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	84077
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_RRD1',0,33,144,6,3
	.word	86544
	.byte	12,33,147,6,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	84150
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_UBAB_CFG',0,33,152,6,3
	.word	86608
	.byte	12,33,155,6,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	84305
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_UBAB_STAT',0,33,160,6,3
	.word	86676
	.byte	12,33,163,6,9,4,13
	.byte	'U',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	500
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	84410
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_UBAB_TOP',0,33,168,6,3
	.word	86745
	.byte	10
	.byte	'_Ifx_FLASH_CBAB',0,33,179,6,25,12,13
	.byte	'CFG',0
	.word	84690
	.byte	4,2,35,0,13
	.byte	'STAT',0
	.word	84758
	.byte	4,2,35,4,13
	.byte	'TOP',0
	.word	84827
	.byte	4,2,35,8,0,14
	.word	86813
	.byte	29
	.byte	'Ifx_FLASH_CBAB',0,33,184,6,3
	.word	86876
	.byte	10
	.byte	'_Ifx_FLASH_RDB',0,33,187,6,25,12,13
	.byte	'CFG0',0
	.word	86148
	.byte	4,2,35,0,13
	.byte	'CFG1',0
	.word	86216
	.byte	4,2,35,4,13
	.byte	'CFG2',0
	.word	86284
	.byte	4,2,35,8,0,14
	.word	86905
	.byte	29
	.byte	'Ifx_FLASH_RDB',0,33,192,6,3
	.word	86969
	.byte	10
	.byte	'_Ifx_FLASH_UBAB',0,33,195,6,25,12,13
	.byte	'CFG',0
	.word	86608
	.byte	4,2,35,0,13
	.byte	'STAT',0
	.word	86676
	.byte	4,2,35,4,13
	.byte	'TOP',0
	.word	86745
	.byte	4,2,35,8,0,14
	.word	86997
	.byte	29
	.byte	'Ifx_FLASH_UBAB',0,33,200,6,3
	.word	87060
	.byte	29
	.byte	'Ifx_P_ACCEN0_Bits',0,8,79,3
	.word	8602
	.byte	29
	.byte	'Ifx_P_ACCEN1_Bits',0,8,85,3
	.word	8515
	.byte	29
	.byte	'Ifx_P_ESR_Bits',0,8,107,3
	.word	4858
	.byte	29
	.byte	'Ifx_P_ID_Bits',0,8,115,3
	.word	2911
	.byte	29
	.byte	'Ifx_P_IN_Bits',0,8,137,1,3
	.word	3906
	.byte	29
	.byte	'Ifx_P_IOCR0_Bits',0,8,150,1,3
	.word	3039
	.byte	29
	.byte	'Ifx_P_IOCR12_Bits',0,8,163,1,3
	.word	3686
	.byte	29
	.byte	'Ifx_P_IOCR4_Bits',0,8,176,1,3
	.word	3254
	.byte	29
	.byte	'Ifx_P_IOCR8_Bits',0,8,189,1,3
	.word	3469
	.byte	29
	.byte	'Ifx_P_LPCR0_Bits',0,8,197,1,3
	.word	7874
	.byte	29
	.byte	'Ifx_P_LPCR1_Bits',0,8,205,1,3
	.word	7998
	.byte	29
	.byte	'Ifx_P_LPCR1_P21_Bits',0,8,215,1,3
	.word	8082
	.byte	29
	.byte	'Ifx_P_LPCR2_Bits',0,8,229,1,3
	.word	8262
	.byte	29
	.byte	'Ifx_P_OMCR0_Bits',0,8,240,1,3
	.word	6513
	.byte	29
	.byte	'Ifx_P_OMCR12_Bits',0,8,250,1,3
	.word	7037
	.byte	29
	.byte	'Ifx_P_OMCR4_Bits',0,8,133,2,3
	.word	6687
	.byte	29
	.byte	'Ifx_P_OMCR8_Bits',0,8,144,2,3
	.word	6861
	.byte	29
	.byte	'Ifx_P_OMCR_Bits',0,8,166,2,3
	.word	7526
	.byte	29
	.byte	'Ifx_P_OMR_Bits',0,8,203,2,3
	.word	2340
	.byte	29
	.byte	'Ifx_P_OMSR0_Bits',0,8,213,2,3
	.word	5850
	.byte	29
	.byte	'Ifx_P_OMSR12_Bits',0,8,224,2,3
	.word	6338
	.byte	29
	.byte	'Ifx_P_OMSR4_Bits',0,8,235,2,3
	.word	5997
	.byte	29
	.byte	'Ifx_P_OMSR8_Bits',0,8,246,2,3
	.word	6166
	.byte	29
	.byte	'Ifx_P_OMSR_Bits',0,8,140,3,3
	.word	7193
	.byte	29
	.byte	'Ifx_P_OUT_Bits',0,8,162,3,3
	.word	2024
	.byte	29
	.byte	'Ifx_P_PCSR_Bits',0,8,180,3,3
	.word	5564
	.byte	29
	.byte	'Ifx_P_PDISC_Bits',0,8,202,3,3
	.word	5198
	.byte	29
	.byte	'Ifx_P_PDR0_Bits',0,8,223,3,3
	.word	4229
	.byte	29
	.byte	'Ifx_P_PDR1_Bits',0,8,244,3,3
	.word	4533
	.byte	29
	.byte	'Ifx_P_ACCEN0',0,8,129,4,3
	.word	9129
	.byte	29
	.byte	'Ifx_P_ACCEN1',0,8,137,4,3
	.word	8562
	.byte	29
	.byte	'Ifx_P_ESR',0,8,145,4,3
	.word	5149
	.byte	29
	.byte	'Ifx_P_ID',0,8,153,4,3
	.word	2990
	.byte	29
	.byte	'Ifx_P_IN',0,8,161,4,3
	.word	4180
	.byte	29
	.byte	'Ifx_P_IOCR0',0,8,169,4,3
	.word	3214
	.byte	29
	.byte	'Ifx_P_IOCR12',0,8,177,4,3
	.word	3866
	.byte	29
	.byte	'Ifx_P_IOCR4',0,8,185,4,3
	.word	3429
	.byte	29
	.byte	'Ifx_P_IOCR8',0,8,193,4,3
	.word	3646
	.byte	29
	.byte	'Ifx_P_LPCR0',0,8,201,4,3
	.word	7958
	.byte	29
	.byte	'Ifx_P_LPCR1',0,8,210,4,3
	.word	8207
	.byte	29
	.byte	'Ifx_P_LPCR2',0,8,218,4,3
	.word	8466
	.byte	29
	.byte	'Ifx_P_OMCR',0,8,226,4,3
	.word	7834
	.byte	29
	.byte	'Ifx_P_OMCR0',0,8,234,4,3
	.word	6647
	.byte	29
	.byte	'Ifx_P_OMCR12',0,8,242,4,3
	.word	7153
	.byte	29
	.byte	'Ifx_P_OMCR4',0,8,250,4,3
	.word	6821
	.byte	29
	.byte	'Ifx_P_OMCR8',0,8,130,5,3
	.word	6997
	.byte	29
	.byte	'Ifx_P_OMR',0,8,138,5,3
	.word	2871
	.byte	29
	.byte	'Ifx_P_OMSR',0,8,146,5,3
	.word	7486
	.byte	29
	.byte	'Ifx_P_OMSR0',0,8,154,5,3
	.word	5957
	.byte	29
	.byte	'Ifx_P_OMSR12',0,8,162,5,3
	.word	6473
	.byte	29
	.byte	'Ifx_P_OMSR4',0,8,170,5,3
	.word	6126
	.byte	29
	.byte	'Ifx_P_OMSR8',0,8,178,5,3
	.word	6298
	.byte	29
	.byte	'Ifx_P_OUT',0,8,186,5,3
	.word	2300
	.byte	29
	.byte	'Ifx_P_PCSR',0,8,194,5,3
	.word	5810
	.byte	29
	.byte	'Ifx_P_PDISC',0,8,202,5,3
	.word	5524
	.byte	29
	.byte	'Ifx_P_PDR0',0,8,210,5,3
	.word	4493
	.byte	29
	.byte	'Ifx_P_PDR1',0,8,218,5,3
	.word	4809
	.byte	14
	.word	9169
	.byte	29
	.byte	'Ifx_P',0,8,139,6,3
	.word	88407
	.byte	29
	.byte	'IfxPort_InputMode',0,7,89,3
	.word	9782
	.byte	29
	.byte	'IfxPort_OutputIdx',0,7,130,1,3
	.word	10057
	.byte	29
	.byte	'IfxPort_OutputMode',0,7,138,1,3
	.word	9987
	.byte	15,7,144,1,9,1,16
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed1',0,0,16
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed2',0,1,16
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed3',0,2,16
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed4',0,3,16
	.byte	'IfxPort_PadDriver_lvdsSpeed1',0,4,16
	.byte	'IfxPort_PadDriver_lvdsSpeed2',0,5,16
	.byte	'IfxPort_PadDriver_lvdsSpeed3',0,6,16
	.byte	'IfxPort_PadDriver_lvdsSpeed4',0,7,16
	.byte	'IfxPort_PadDriver_ttlSpeed1',0,8,16
	.byte	'IfxPort_PadDriver_ttlSpeed2',0,9,16
	.byte	'IfxPort_PadDriver_ttlSpeed3',0,10,16
	.byte	'IfxPort_PadDriver_ttlSpeed4',0,11,0,29
	.byte	'IfxPort_PadDriver',0,7,158,1,3
	.word	88508
	.byte	29
	.byte	'IfxPort_State',0,7,178,1,3
	.word	10370
	.byte	20,7,190,1,9,8,13
	.byte	'port',0
	.word	9777
	.byte	4,2,35,0,13
	.byte	'pinIndex',0
	.word	659
	.byte	1,2,35,4,0,29
	.byte	'IfxPort_Pin',0,7,194,1,3
	.word	88973
	.byte	29
	.byte	'IfxScuCcu_PllStepsFunctionHook',0,9,148,1,16
	.word	223
	.byte	20,9,212,5,9,8,13
	.byte	'value',0
	.word	1793
	.byte	4,2,35,0,13
	.byte	'mask',0
	.word	1793
	.byte	4,2,35,4,0,29
	.byte	'IfxScuCcu_CcuconRegConfig',0,9,216,5,3
	.word	89073
	.byte	20,9,221,5,9,8,13
	.byte	'pDivider',0
	.word	659
	.byte	1,2,35,0,13
	.byte	'nDivider',0
	.word	659
	.byte	1,2,35,1,13
	.byte	'k2Initial',0
	.word	659
	.byte	1,2,35,2,13
	.byte	'waitTime',0
	.word	280
	.byte	4,2,35,4,0,29
	.byte	'IfxScuCcu_InitialStepConfig',0,9,227,5,3
	.word	89144
	.byte	20,9,231,5,9,12,13
	.byte	'k2Step',0
	.word	659
	.byte	1,2,35,0,13
	.byte	'waitTime',0
	.word	280
	.byte	4,2,35,2,13
	.byte	'hookFunction',0
	.word	89033
	.byte	4,2,35,8,0,29
	.byte	'IfxScuCcu_PllStepsConfig',0,9,236,5,3
	.word	89261
	.byte	3
	.word	220
	.byte	20,9,244,5,9,48,13
	.byte	'ccucon0',0
	.word	89073
	.byte	8,2,35,0,13
	.byte	'ccucon1',0
	.word	89073
	.byte	8,2,35,8,13
	.byte	'ccucon2',0
	.word	89073
	.byte	8,2,35,16,13
	.byte	'ccucon5',0
	.word	89073
	.byte	8,2,35,24,13
	.byte	'ccucon6',0
	.word	89073
	.byte	8,2,35,32,13
	.byte	'ccucon7',0
	.word	89073
	.byte	8,2,35,40,0,29
	.byte	'IfxScuCcu_ClockDistributionConfig',0,9,252,5,3
	.word	89363
	.byte	20,9,128,6,9,8,13
	.byte	'value',0
	.word	1793
	.byte	4,2,35,0,13
	.byte	'mask',0
	.word	1793
	.byte	4,2,35,4,0,29
	.byte	'IfxScuCcu_FlashWaitstateConfig',0,9,132,6,3
	.word	89515
	.byte	3
	.word	89261
	.byte	20,9,137,6,9,16,13
	.byte	'numOfPllDividerSteps',0
	.word	659
	.byte	1,2,35,0,13
	.byte	'pllDividerStep',0
	.word	89591
	.byte	4,2,35,4,13
	.byte	'pllInitialStep',0
	.word	89144
	.byte	8,2,35,8,0,29
	.byte	'IfxScuCcu_SysPllConfig',0,9,142,6,3
	.word	89596
	.byte	15,34,69,9,1,16
	.byte	'IfxSrc_Tos_cpu0',0,0,16
	.byte	'IfxSrc_Tos_cpu1',0,1,16
	.byte	'IfxSrc_Tos_dma',0,3,0,29
	.byte	'IfxSrc_Tos',0,34,74,3
	.word	89713
	.byte	20,35,59,15,16,13
	.byte	'module',0
	.word	17505
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	88973
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	24446
	.byte	1,2,35,12,0,22
	.word	89791
	.byte	29
	.byte	'IfxAsclin_Cts_In',0,35,64,3
	.word	89842
	.byte	20,35,67,15,16,13
	.byte	'module',0
	.word	17505
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	88973
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	24446
	.byte	1,2,35,12,0,22
	.word	89872
	.byte	29
	.byte	'IfxAsclin_Rx_In',0,35,72,3
	.word	89923
	.byte	20,35,75,15,16,13
	.byte	'module',0
	.word	17505
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	88973
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	10057
	.byte	1,2,35,12,0,22
	.word	89952
	.byte	29
	.byte	'IfxAsclin_Rts_Out',0,35,80,3
	.word	90003
	.byte	20,35,83,15,16,13
	.byte	'module',0
	.word	17505
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	88973
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	10057
	.byte	1,2,35,12,0,22
	.word	90034
	.byte	29
	.byte	'IfxAsclin_Sclk_Out',0,35,88,3
	.word	90085
	.byte	20,35,91,15,16,13
	.byte	'module',0
	.word	17505
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	88973
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	10057
	.byte	1,2,35,12,0,22
	.word	90117
	.byte	29
	.byte	'IfxAsclin_Slso_Out',0,35,96,3
	.word	90168
	.byte	20,35,99,15,16,13
	.byte	'module',0
	.word	17505
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	88973
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	10057
	.byte	1,2,35,12,0,22
	.word	90200
	.byte	29
	.byte	'IfxAsclin_Tx_Out',0,35,104,3
	.word	90251
	.byte	15,12,82,9,1,16
	.byte	'IfxAsclin_Checksum_classic',0,0,16
	.byte	'IfxAsclin_Checksum_enhanced',0,1,0,29
	.byte	'IfxAsclin_Checksum',0,12,86,3
	.word	90281
	.byte	15,12,91,9,1,16
	.byte	'IfxAsclin_ChecksumInjection_notWritten',0,0,16
	.byte	'IfxAsclin_ChecksumInjection_written',0,1,0,29
	.byte	'IfxAsclin_ChecksumInjection',0,12,95,3
	.word	90373
	.byte	15,12,101,9,1,16
	.byte	'IfxAsclin_ClockPolarity_idleLow',0,0,16
	.byte	'IfxAsclin_ClockPolarity_idleHigh',0,1,0,29
	.byte	'IfxAsclin_ClockPolarity',0,12,105,3
	.word	90494
	.byte	15,12,110,9,1,16
	.byte	'IfxAsclin_ClockSource_noClock',0,0,16
	.byte	'IfxAsclin_ClockSource_kernelClock',0,1,16
	.byte	'IfxAsclin_ClockSource_oscillatorClock',0,2,16
	.byte	'IfxAsclin_ClockSource_flexRayClock',0,4,16
	.byte	'IfxAsclin_ClockSource_ascFastClock',0,8,16
	.byte	'IfxAsclin_ClockSource_ascSlowClock',0,16,0,29
	.byte	'IfxAsclin_ClockSource',0,12,118,3
	.word	90601
	.byte	29
	.byte	'IfxAsclin_CtsInputSelect',0,12,129,1,3
	.word	17572
	.byte	15,12,134,1,9,1,16
	.byte	'IfxAsclin_DataLength_1',0,0,16
	.byte	'IfxAsclin_DataLength_2',0,1,16
	.byte	'IfxAsclin_DataLength_3',0,2,16
	.byte	'IfxAsclin_DataLength_4',0,3,16
	.byte	'IfxAsclin_DataLength_5',0,4,16
	.byte	'IfxAsclin_DataLength_6',0,5,16
	.byte	'IfxAsclin_DataLength_7',0,6,16
	.byte	'IfxAsclin_DataLength_8',0,7,16
	.byte	'IfxAsclin_DataLength_9',0,8,16
	.byte	'IfxAsclin_DataLength_10',0,9,16
	.byte	'IfxAsclin_DataLength_11',0,10,16
	.byte	'IfxAsclin_DataLength_12',0,11,16
	.byte	'IfxAsclin_DataLength_13',0,12,16
	.byte	'IfxAsclin_DataLength_14',0,13,16
	.byte	'IfxAsclin_DataLength_15',0,14,16
	.byte	'IfxAsclin_DataLength_16',0,15,0,29
	.byte	'IfxAsclin_DataLength',0,12,152,1,3
	.word	90890
	.byte	15,12,157,1,9,1,16
	.byte	'IfxAsclin_FrameMode_initialise',0,0,16
	.byte	'IfxAsclin_FrameMode_asc',0,1,16
	.byte	'IfxAsclin_FrameMode_spi',0,2,16
	.byte	'IfxAsclin_FrameMode_lin',0,3,0,29
	.byte	'IfxAsclin_FrameMode',0,12,163,1,3
	.word	91334
	.byte	15,12,168,1,9,1,16
	.byte	'IfxAsclin_HeaderResponseSelect_headerAndResponse',0,0,16
	.byte	'IfxAsclin_HeaderResponseSelect_headerOnly',0,1,0,29
	.byte	'IfxAsclin_HeaderResponseSelect',0,12,172,1,3
	.word	91481
	.byte	15,12,179,1,9,1,16
	.byte	'IfxAsclin_IdleDelay_0',0,0,16
	.byte	'IfxAsclin_IdleDelay_1',0,1,16
	.byte	'IfxAsclin_IdleDelay_2',0,2,16
	.byte	'IfxAsclin_IdleDelay_3',0,3,16
	.byte	'IfxAsclin_IdleDelay_4',0,4,16
	.byte	'IfxAsclin_IdleDelay_5',0,5,16
	.byte	'IfxAsclin_IdleDelay_6',0,6,16
	.byte	'IfxAsclin_IdleDelay_7',0,7,0,29
	.byte	'IfxAsclin_IdleDelay',0,12,189,1,3
	.word	91623
	.byte	15,12,195,1,9,1,16
	.byte	'IfxAsclin_LeadDelay_0',0,0,16
	.byte	'IfxAsclin_LeadDelay_1',0,1,16
	.byte	'IfxAsclin_LeadDelay_2',0,2,16
	.byte	'IfxAsclin_LeadDelay_3',0,3,16
	.byte	'IfxAsclin_LeadDelay_4',0,4,16
	.byte	'IfxAsclin_LeadDelay_5',0,5,16
	.byte	'IfxAsclin_LeadDelay_6',0,6,16
	.byte	'IfxAsclin_LeadDelay_7',0,7,0,29
	.byte	'IfxAsclin_LeadDelay',0,12,205,1,3
	.word	91851
	.byte	15,12,210,1,9,1,16
	.byte	'IfxAsclin_LinMode_slave',0,0,16
	.byte	'IfxAsclin_LinMode_master',0,1,0,29
	.byte	'IfxAsclin_LinMode',0,12,214,1,3
	.word	92079
	.byte	15,12,219,1,9,1,16
	.byte	'IfxAsclin_LinResponseTimeoutMode_frameTimeout',0,0,16
	.byte	'IfxAsclin_LinResponseTimeoutMode_responseTimeout',0,1,0,29
	.byte	'IfxAsclin_LinResponseTimeoutMode',0,12,223,1,3
	.word	92166
	.byte	15,12,228,1,9,1,16
	.byte	'IfxAsclin_OversamplingFactor_4',0,3,16
	.byte	'IfxAsclin_OversamplingFactor_5',0,4,16
	.byte	'IfxAsclin_OversamplingFactor_6',0,5,16
	.byte	'IfxAsclin_OversamplingFactor_7',0,6,16
	.byte	'IfxAsclin_OversamplingFactor_8',0,7,16
	.byte	'IfxAsclin_OversamplingFactor_9',0,8,16
	.byte	'IfxAsclin_OversamplingFactor_10',0,9,16
	.byte	'IfxAsclin_OversamplingFactor_11',0,10,16
	.byte	'IfxAsclin_OversamplingFactor_12',0,11,16
	.byte	'IfxAsclin_OversamplingFactor_13',0,12,16
	.byte	'IfxAsclin_OversamplingFactor_14',0,13,16
	.byte	'IfxAsclin_OversamplingFactor_15',0,14,16
	.byte	'IfxAsclin_OversamplingFactor_16',0,15,0,29
	.byte	'IfxAsclin_OversamplingFactor',0,12,243,1,3
	.word	92314
	.byte	15,12,248,1,9,1,16
	.byte	'IfxAsclin_ParityType_even',0,0,16
	.byte	'IfxAsclin_ParityType_odd',0,1,0,29
	.byte	'IfxAsclin_ParityType',0,12,252,1,3
	.word	92795
	.byte	15,12,129,2,9,1,16
	.byte	'IfxAsclin_ReceiveBufferMode_rxFifo',0,0,16
	.byte	'IfxAsclin_ReceiveBufferMode_rxBuffer',0,1,0,29
	.byte	'IfxAsclin_ReceiveBufferMode',0,12,133,2,3
	.word	92887
	.byte	15,12,138,2,9,1,16
	.byte	'IfxAsclin_RtsCtsPolarity_activeHigh',0,0,16
	.byte	'IfxAsclin_RtsCtsPolarity_activeLow',0,1,0,29
	.byte	'IfxAsclin_RtsCtsPolarity',0,12,142,2,3
	.word	93007
	.byte	15,12,147,2,9,1,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_1',0,0,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_2',0,1,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_3',0,2,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_4',0,3,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_5',0,4,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_6',0,5,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_7',0,6,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_8',0,7,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_9',0,8,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_10',0,9,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_11',0,10,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_12',0,11,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_13',0,12,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_14',0,13,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_15',0,14,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_16',0,15,0,29
	.byte	'IfxAsclin_RxFifoInterruptLevel',0,12,165,2,3
	.word	93123
	.byte	15,12,170,2,9,1,16
	.byte	'IfxAsclin_RxFifoOutletWidth_0',0,0,16
	.byte	'IfxAsclin_RxFifoOutletWidth_1',0,1,16
	.byte	'IfxAsclin_RxFifoOutletWidth_2',0,2,16
	.byte	'IfxAsclin_RxFifoOutletWidth_3',0,3,0,29
	.byte	'IfxAsclin_RxFifoOutletWidth',0,12,176,2,3
	.word	93737
	.byte	29
	.byte	'IfxAsclin_RxInputSelect',0,12,191,2,3
	.word	17756
	.byte	15,12,196,2,9,1,16
	.byte	'IfxAsclin_SamplePointPosition_1',0,1,16
	.byte	'IfxAsclin_SamplePointPosition_2',0,2,16
	.byte	'IfxAsclin_SamplePointPosition_3',0,3,16
	.byte	'IfxAsclin_SamplePointPosition_4',0,4,16
	.byte	'IfxAsclin_SamplePointPosition_5',0,5,16
	.byte	'IfxAsclin_SamplePointPosition_6',0,6,16
	.byte	'IfxAsclin_SamplePointPosition_7',0,7,16
	.byte	'IfxAsclin_SamplePointPosition_8',0,8,16
	.byte	'IfxAsclin_SamplePointPosition_9',0,9,16
	.byte	'IfxAsclin_SamplePointPosition_10',0,10,16
	.byte	'IfxAsclin_SamplePointPosition_11',0,11,16
	.byte	'IfxAsclin_SamplePointPosition_12',0,12,16
	.byte	'IfxAsclin_SamplePointPosition_13',0,13,16
	.byte	'IfxAsclin_SamplePointPosition_14',0,14,16
	.byte	'IfxAsclin_SamplePointPosition_15',0,15,0,29
	.byte	'IfxAsclin_SamplePointPosition',0,12,213,2,3
	.word	93942
	.byte	15,12,218,2,9,1,16
	.byte	'IfxAsclin_SamplesPerBit_one',0,0,16
	.byte	'IfxAsclin_SamplesPerBit_three',0,1,0,29
	.byte	'IfxAsclin_SamplesPerBit',0,12,222,2,3
	.word	94504
	.byte	15,12,228,2,9,1,16
	.byte	'IfxAsclin_ShiftDirection_lsbFirst',0,0,16
	.byte	'IfxAsclin_ShiftDirection_msbFirst',0,1,0,29
	.byte	'IfxAsclin_ShiftDirection',0,12,232,2,3
	.word	94606
	.byte	15,12,238,2,9,1,16
	.byte	'IfxAsclin_SlavePolarity_idleLow',0,0,16
	.byte	'IfxAsclin_SlavePolarity_idlehigh',0,1,0,29
	.byte	'IfxAsclin_SlavePolarity',0,12,242,2,3
	.word	94719
	.byte	15,12,247,2,9,1,16
	.byte	'IfxAsclin_SleepMode_enable',0,0,16
	.byte	'IfxAsclin_SleepMode_disable',0,1,0,29
	.byte	'IfxAsclin_SleepMode',0,12,251,2,3
	.word	94828
	.byte	15,12,136,3,9,1,16
	.byte	'IfxAsclin_StopBit_0',0,0,16
	.byte	'IfxAsclin_StopBit_1',0,1,16
	.byte	'IfxAsclin_StopBit_2',0,2,16
	.byte	'IfxAsclin_StopBit_3',0,3,16
	.byte	'IfxAsclin_StopBit_4',0,4,16
	.byte	'IfxAsclin_StopBit_5',0,5,16
	.byte	'IfxAsclin_StopBit_6',0,6,16
	.byte	'IfxAsclin_StopBit_7',0,7,0,29
	.byte	'IfxAsclin_StopBit',0,12,146,3,3
	.word	94923
	.byte	15,12,150,3,9,1,16
	.byte	'IfxAsclin_SuspendMode_none',0,0,16
	.byte	'IfxAsclin_SuspendMode_hard',0,1,16
	.byte	'IfxAsclin_SuspendMode_soft',0,2,0,29
	.byte	'IfxAsclin_SuspendMode',0,12,155,3,3
	.word	95133
	.byte	15,12,160,3,9,1,16
	.byte	'IfxAsclin_TxFifoInletWidth_0',0,0,16
	.byte	'IfxAsclin_TxFifoInletWidth_1',0,1,16
	.byte	'IfxAsclin_TxFifoInletWidth_2',0,2,16
	.byte	'IfxAsclin_TxFifoInletWidth_3',0,3,0,29
	.byte	'IfxAsclin_TxFifoInletWidth',0,12,166,3,3
	.word	95258
	.byte	15,12,171,3,9,1,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_0',0,0,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_1',0,1,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_2',0,2,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_3',0,3,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_4',0,4,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_5',0,5,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_6',0,6,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_7',0,7,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_8',0,8,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_9',0,9,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_10',0,10,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_11',0,11,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_12',0,12,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_13',0,13,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_14',0,14,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_15',0,15,0,29
	.byte	'IfxAsclin_TxFifoInterruptLevel',0,12,189,3,3
	.word	95425
	.byte	29
	.byte	'Ifx_Fifo_Shared',0,14,66,3
	.word	18073
	.byte	29
	.byte	'Ifx_Fifo',0,14,83,3
	.word	18164
	.byte	15,15,151,1,9,1,16
	.byte	'IfxStm_Comparator_0',0,0,16
	.byte	'IfxStm_Comparator_1',0,1,0,29
	.byte	'IfxStm_Comparator',0,15,155,1,3
	.word	96079
	.byte	15,15,159,1,9,1,16
	.byte	'IfxStm_ComparatorInterrupt_ir0',0,0,16
	.byte	'IfxStm_ComparatorInterrupt_ir1',0,1,0,29
	.byte	'IfxStm_ComparatorInterrupt',0,15,163,1,3
	.word	96157
	.byte	15,15,167,1,9,1,16
	.byte	'IfxStm_ComparatorOffset_0',0,0,16
	.byte	'IfxStm_ComparatorOffset_1',0,1,16
	.byte	'IfxStm_ComparatorOffset_2',0,2,16
	.byte	'IfxStm_ComparatorOffset_3',0,3,16
	.byte	'IfxStm_ComparatorOffset_4',0,4,16
	.byte	'IfxStm_ComparatorOffset_5',0,5,16
	.byte	'IfxStm_ComparatorOffset_6',0,6,16
	.byte	'IfxStm_ComparatorOffset_7',0,7,16
	.byte	'IfxStm_ComparatorOffset_8',0,8,16
	.byte	'IfxStm_ComparatorOffset_9',0,9,16
	.byte	'IfxStm_ComparatorOffset_10',0,10,16
	.byte	'IfxStm_ComparatorOffset_11',0,11,16
	.byte	'IfxStm_ComparatorOffset_12',0,12,16
	.byte	'IfxStm_ComparatorOffset_13',0,13,16
	.byte	'IfxStm_ComparatorOffset_14',0,14,16
	.byte	'IfxStm_ComparatorOffset_15',0,15,16
	.byte	'IfxStm_ComparatorOffset_16',0,16,16
	.byte	'IfxStm_ComparatorOffset_17',0,17,16
	.byte	'IfxStm_ComparatorOffset_18',0,18,16
	.byte	'IfxStm_ComparatorOffset_19',0,19,16
	.byte	'IfxStm_ComparatorOffset_20',0,20,16
	.byte	'IfxStm_ComparatorOffset_21',0,21,16
	.byte	'IfxStm_ComparatorOffset_22',0,22,16
	.byte	'IfxStm_ComparatorOffset_23',0,23,16
	.byte	'IfxStm_ComparatorOffset_24',0,24,16
	.byte	'IfxStm_ComparatorOffset_25',0,25,16
	.byte	'IfxStm_ComparatorOffset_26',0,26,16
	.byte	'IfxStm_ComparatorOffset_27',0,27,16
	.byte	'IfxStm_ComparatorOffset_28',0,28,16
	.byte	'IfxStm_ComparatorOffset_29',0,29,16
	.byte	'IfxStm_ComparatorOffset_30',0,30,16
	.byte	'IfxStm_ComparatorOffset_31',0,31,0,29
	.byte	'IfxStm_ComparatorOffset',0,15,201,1,3
	.word	96266
	.byte	15,15,205,1,9,1,16
	.byte	'IfxStm_ComparatorSize_1Bit',0,0,16
	.byte	'IfxStm_ComparatorSize_2Bits',0,1,16
	.byte	'IfxStm_ComparatorSize_3Bits',0,2,16
	.byte	'IfxStm_ComparatorSize_4Bits',0,3,16
	.byte	'IfxStm_ComparatorSize_5Bits',0,4,16
	.byte	'IfxStm_ComparatorSize_6Bits',0,5,16
	.byte	'IfxStm_ComparatorSize_7Bits',0,6,16
	.byte	'IfxStm_ComparatorSize_8Bits',0,7,16
	.byte	'IfxStm_ComparatorSize_9Bits',0,8,16
	.byte	'IfxStm_ComparatorSize_10Bits',0,9,16
	.byte	'IfxStm_ComparatorSize_11Bits',0,10,16
	.byte	'IfxStm_ComparatorSize_12Bits',0,11,16
	.byte	'IfxStm_ComparatorSize_13Bits',0,12,16
	.byte	'IfxStm_ComparatorSize_14Bits',0,13,16
	.byte	'IfxStm_ComparatorSize_15Bits',0,14,16
	.byte	'IfxStm_ComparatorSize_16Bits',0,15,16
	.byte	'IfxStm_ComparatorSize_17Bits',0,16,16
	.byte	'IfxStm_ComparatorSize_18Bits',0,17,16
	.byte	'IfxStm_ComparatorSize_19Bits',0,18,16
	.byte	'IfxStm_ComparatorSize_20Bits',0,19,16
	.byte	'IfxStm_ComparatorSize_21Bits',0,20,16
	.byte	'IfxStm_ComparatorSize_22Bits',0,21,16
	.byte	'IfxStm_ComparatorSize_23Bits',0,22,16
	.byte	'IfxStm_ComparatorSize_24Bits',0,23,16
	.byte	'IfxStm_ComparatorSize_25Bits',0,24,16
	.byte	'IfxStm_ComparatorSize_26Bits',0,25,16
	.byte	'IfxStm_ComparatorSize_27Bits',0,26,16
	.byte	'IfxStm_ComparatorSize_28Bits',0,27,16
	.byte	'IfxStm_ComparatorSize_29Bits',0,28,16
	.byte	'IfxStm_ComparatorSize_30Bits',0,29,16
	.byte	'IfxStm_ComparatorSize_31Bits',0,30,16
	.byte	'IfxStm_ComparatorSize_32Bits',0,31,0,29
	.byte	'IfxStm_ComparatorSize',0,15,239,1,3
	.word	97224
	.byte	15,15,244,1,9,1,16
	.byte	'IfxStm_SleepMode_enable',0,0,16
	.byte	'IfxStm_SleepMode_disable',0,1,0,29
	.byte	'IfxStm_SleepMode',0,15,248,1,3
	.word	98244
	.byte	15,15,252,1,9,1,16
	.byte	'IfxStm_SuspendMode_none',0,0,16
	.byte	'IfxStm_SuspendMode_hard',0,1,16
	.byte	'IfxStm_SuspendMode_soft',0,2,0,29
	.byte	'IfxStm_SuspendMode',0,15,129,2,3
	.word	98330
	.byte	29
	.byte	'IfxStdIf_InterfaceDriver',0,36,118,15
	.word	398
	.byte	3
	.word	18048
	.byte	31
	.word	659
	.byte	1,1,32
	.word	398
	.byte	32
	.word	398
	.byte	32
	.word	98476
	.byte	32
	.word	22021
	.byte	0,3
	.word	98481
	.byte	29
	.byte	'IfxStdIf_DPipe_Write',0,37,92,19
	.word	98509
	.byte	29
	.byte	'IfxStdIf_DPipe_Read',0,37,107,19
	.word	98509
	.byte	31
	.word	18061
	.byte	1,1,32
	.word	398
	.byte	0,3
	.word	98571
	.byte	29
	.byte	'IfxStdIf_DPipe_GetReadCount',0,37,115,18
	.word	98584
	.byte	14
	.word	659
	.byte	3
	.word	98625
	.byte	31
	.word	98630
	.byte	1,1,32
	.word	398
	.byte	0,3
	.word	98635
	.byte	29
	.byte	'IfxStdIf_DPipe_GetReadEvent',0,37,123,36
	.word	98648
	.byte	29
	.byte	'IfxStdIf_DPipe_GetWriteCount',0,37,147,1,18
	.word	98584
	.byte	3
	.word	98635
	.byte	29
	.byte	'IfxStdIf_DPipe_GetWriteEvent',0,37,155,1,37
	.word	98727
	.byte	31
	.word	659
	.byte	1,1,32
	.word	398
	.byte	32
	.word	18048
	.byte	32
	.word	22021
	.byte	0,3
	.word	98770
	.byte	29
	.byte	'IfxStdIf_DPipe_CanReadCount',0,37,166,1,19
	.word	98793
	.byte	29
	.byte	'IfxStdIf_DPipe_CanWriteCount',0,37,177,1,19
	.word	98793
	.byte	31
	.word	659
	.byte	1,1,32
	.word	398
	.byte	32
	.word	22021
	.byte	0,3
	.word	98873
	.byte	29
	.byte	'IfxStdIf_DPipe_FlushTx',0,37,186,1,19
	.word	98891
	.byte	33,1,1,32
	.word	398
	.byte	0,3
	.word	98928
	.byte	29
	.byte	'IfxStdIf_DPipe_ClearTx',0,37,200,1,16
	.word	98937
	.byte	29
	.byte	'IfxStdIf_DPipe_ClearRx',0,37,193,1,16
	.word	98937
	.byte	29
	.byte	'IfxStdIf_DPipe_OnReceive',0,37,208,1,16
	.word	98937
	.byte	29
	.byte	'IfxStdIf_DPipe_OnTransmit',0,37,215,1,16
	.word	98937
	.byte	29
	.byte	'IfxStdIf_DPipe_OnError',0,37,222,1,16
	.word	98937
	.byte	31
	.word	1793
	.byte	1,1,32
	.word	398
	.byte	0,3
	.word	99107
	.byte	29
	.byte	'IfxStdIf_DPipe_GetSendCount',0,37,131,1,18
	.word	99120
	.byte	31
	.word	22021
	.byte	1,1,32
	.word	398
	.byte	0,3
	.word	99162
	.byte	29
	.byte	'IfxStdIf_DPipe_GetTxTimeStamp',0,37,139,1,24
	.word	99175
	.byte	29
	.byte	'IfxStdIf_DPipe_ResetSendCount',0,37,229,1,16
	.word	98937
	.byte	10
	.byte	'IfxStdIf_DPipe_',0,37,233,1,8,76,13
	.byte	'driver',0
	.word	98443
	.byte	4,2,35,0,13
	.byte	'txDisabled',0
	.word	659
	.byte	1,2,35,4,13
	.byte	'write',0
	.word	98514
	.byte	4,2,35,8,13
	.byte	'read',0
	.word	98543
	.byte	4,2,35,12,13
	.byte	'getReadCount',0
	.word	98589
	.byte	4,2,35,16,13
	.byte	'getReadEvent',0
	.word	98653
	.byte	4,2,35,20,13
	.byte	'getWriteCount',0
	.word	98689
	.byte	4,2,35,24,13
	.byte	'getWriteEvent',0
	.word	98732
	.byte	4,2,35,28,13
	.byte	'canReadCount',0
	.word	98798
	.byte	4,2,35,32,13
	.byte	'canWriteCount',0
	.word	98835
	.byte	4,2,35,36,13
	.byte	'flushTx',0
	.word	98896
	.byte	4,2,35,40,13
	.byte	'clearTx',0
	.word	98942
	.byte	4,2,35,44,13
	.byte	'clearRx',0
	.word	98974
	.byte	4,2,35,48,13
	.byte	'onReceive',0
	.word	99006
	.byte	4,2,35,52,13
	.byte	'onTransmit',0
	.word	99040
	.byte	4,2,35,56,13
	.byte	'onError',0
	.word	99075
	.byte	4,2,35,60,13
	.byte	'getSendCount',0
	.word	99125
	.byte	4,2,35,64,13
	.byte	'getTxTimeStamp',0
	.word	99180
	.byte	4,2,35,68,13
	.byte	'resetSendCount',0
	.word	99219
	.byte	4,2,35,72,0,29
	.byte	'IfxStdIf_DPipe',0,37,71,32
	.word	99258
	.byte	3
	.word	392
	.byte	3
	.word	98481
	.byte	3
	.word	98481
	.byte	3
	.word	98571
	.byte	3
	.word	98635
	.byte	3
	.word	98571
	.byte	3
	.word	98635
	.byte	3
	.word	98770
	.byte	3
	.word	98770
	.byte	3
	.word	98873
	.byte	3
	.word	98928
	.byte	3
	.word	98928
	.byte	3
	.word	98928
	.byte	3
	.word	98928
	.byte	3
	.word	98928
	.byte	3
	.word	99107
	.byte	3
	.word	99162
	.byte	3
	.word	98928
	.byte	14
	.word	659
	.byte	3
	.word	99771
	.byte	29
	.byte	'IfxStdIf_DPipe_WriteEvent',0,37,73,32
	.word	99776
	.byte	29
	.byte	'IfxStdIf_DPipe_ReadEvent',0,37,74,32
	.word	99776
	.byte	20,38,252,1,9,1,11
	.byte	'parityError',0,1
	.word	659
	.byte	1,7,2,35,0,11
	.byte	'frameError',0,1
	.word	659
	.byte	1,6,2,35,0,11
	.byte	'rxFifoOverflow',0,1
	.word	659
	.byte	1,5,2,35,0,11
	.byte	'rxFifoUnderflow',0,1
	.word	659
	.byte	1,4,2,35,0,11
	.byte	'txFifoOverflow',0,1
	.word	659
	.byte	1,3,2,35,0,0,29
	.byte	'IfxAsclin_Asc_ErrorFlags',0,38,131,2,3
	.word	99848
	.byte	20,38,137,2,9,8,13
	.byte	'baudrate',0
	.word	280
	.byte	4,2,35,0,13
	.byte	'prescaler',0
	.word	676
	.byte	2,2,35,4,13
	.byte	'oversampling',0
	.word	92314
	.byte	1,2,35,6,0,29
	.byte	'IfxAsclin_Asc_BaudRate',0,38,142,2,3
	.word	100013
	.byte	20,38,146,2,9,2,13
	.byte	'medianFilter',0
	.word	94504
	.byte	1,2,35,0,13
	.byte	'samplePointPosition',0
	.word	93942
	.byte	1,2,35,1,0,29
	.byte	'IfxAsclin_Asc_BitTimingControl',0,38,150,2,3
	.word	100111
	.byte	20,38,154,2,9,6,13
	.byte	'inWidth',0
	.word	95258
	.byte	1,2,35,0,13
	.byte	'outWidth',0
	.word	93737
	.byte	1,2,35,1,13
	.byte	'txFifoInterruptLevel',0
	.word	95425
	.byte	1,2,35,2,13
	.byte	'rxFifoInterruptLevel',0
	.word	93123
	.byte	1,2,35,3,13
	.byte	'buffMode',0
	.word	92887
	.byte	1,2,35,4,0,29
	.byte	'IfxAsclin_Asc_FifoControl',0,38,161,2,3
	.word	100209
	.byte	20,38,165,2,9,8,13
	.byte	'idleDelay',0
	.word	91623
	.byte	1,2,35,0,13
	.byte	'stopBit',0
	.word	94923
	.byte	1,2,35,1,13
	.byte	'frameMode',0
	.word	91334
	.byte	1,2,35,2,13
	.byte	'shiftDir',0
	.word	94606
	.byte	1,2,35,3,13
	.byte	'parityType',0
	.word	92795
	.byte	1,2,35,4,13
	.byte	'dataLength',0
	.word	90890
	.byte	1,2,35,5,13
	.byte	'parityBit',0
	.word	659
	.byte	1,2,35,6,0,29
	.byte	'IfxAsclin_Asc_FrameControl',0,38,174,2,3
	.word	100364
	.byte	20,38,178,2,9,8,13
	.byte	'txPriority',0
	.word	676
	.byte	2,2,35,0,13
	.byte	'rxPriority',0
	.word	676
	.byte	2,2,35,2,13
	.byte	'erPriority',0
	.word	676
	.byte	2,2,35,4,13
	.byte	'typeOfService',0
	.word	89713
	.byte	1,2,35,6,0,29
	.byte	'IfxAsclin_Asc_InterruptConfig',0,38,184,2,3
	.word	100539
	.byte	22
	.word	89791
	.byte	3
	.word	100668
	.byte	22
	.word	89872
	.byte	3
	.word	100678
	.byte	22
	.word	89952
	.byte	3
	.word	100688
	.byte	22
	.word	90200
	.byte	3
	.word	100698
	.byte	20,38,188,2,9,32,13
	.byte	'cts',0
	.word	100673
	.byte	4,2,35,0,13
	.byte	'ctsMode',0
	.word	9782
	.byte	1,2,35,4,13
	.byte	'rx',0
	.word	100683
	.byte	4,2,35,8,13
	.byte	'rxMode',0
	.word	9782
	.byte	1,2,35,12,13
	.byte	'rts',0
	.word	100693
	.byte	4,2,35,16,13
	.byte	'rtsMode',0
	.word	9987
	.byte	1,2,35,20,13
	.byte	'tx',0
	.word	100703
	.byte	4,2,35,24,13
	.byte	'txMode',0
	.word	9987
	.byte	1,2,35,28,13
	.byte	'pinDriver',0
	.word	88508
	.byte	1,2,35,29,0,29
	.byte	'IfxAsclin_Asc_Pins',0,38,199,2,3
	.word	100708
	.byte	12,38,205,2,9,1,13
	.byte	'ALL',0
	.word	659
	.byte	1,2,35,0,13
	.byte	'flags',0
	.word	99848
	.byte	1,2,35,0,0,29
	.byte	'IfxAsclin_Asc_ErrorFlagsUnion',0,38,209,2,3
	.word	100878
	.byte	15,22,83,9,1,16
	.byte	'WIFI_UART_STATION',0,0,16
	.byte	'WIFI_UART_SOFTAP',0,1,0,29
	.byte	'wifi_uart_mode_enum',0,22,87,2
	.word	100952
	.byte	15,22,89,9,1,16
	.byte	'WIFI_UART_COMMAND',0,0,16
	.byte	'WIFI_UART_SERIANET',0,1,0,29
	.byte	'wifi_uart_transfer_mode_enum',0,22,93,2
	.word	101025
	.byte	15,22,95,9,1,16
	.byte	'WIFI_UART_TCP_CLIENT',0,0,16
	.byte	'WIFI_UART_TCP_SERVER',0,1,16
	.byte	'WIFI_UART_UDP_CLIENT',0,2,0,29
	.byte	'wifi_uart_connect_mode_enum',0,22,100,2
	.word	101109
	.byte	15,22,102,9,1,16
	.byte	'WIFI_UART_SERVER_OFF',0,0,16
	.byte	'WIFI_UART_SERVER_ON',0,1,0,29
	.byte	'wifi_uart_connect_state_enum',0,22,106,2
	.word	101220
	.byte	20,23,123,9,4,13
	.byte	'command',0
	.word	659
	.byte	1,2,35,0,13
	.byte	'reserve',0
	.word	659
	.byte	1,2,35,1,13
	.byte	'length',0
	.word	676
	.byte	2,2,35,2,0,29
	.byte	'wifi_spi_head_struct',0,23,128,1,2
	.word	101308
	.byte	15,39,75,9,1,16
	.byte	'SEEKFREE_ASSISTANT_BINARY',0,1,16
	.byte	'SEEKFREE_ASSISTANT_OV7725_BIN',0,1,16
	.byte	'SEEKFREE_ASSISTANT_GRAY',0,2,16
	.byte	'SEEKFREE_ASSISTANT_MT9V03X',0,2,16
	.byte	'SEEKFREE_ASSISTANT_RGB565',0,3,16
	.byte	'SEEKFREE_ASSISTANT_SCC8660',0,3,0,29
	.byte	'seekfree_assistant_image_type_enum',0,39,86,2
	.word	101394
	.byte	22
	.word	659
	.byte	3
	.word	101615
	.byte	31
	.word	1793
	.byte	1,1,32
	.word	101620
	.byte	32
	.word	1793
	.byte	0,3
	.word	101625
	.byte	29
	.byte	'seekfree_assistant_transfer_callback_function',0,39,149,1,18
	.word	101643
	.byte	3
	.word	659
	.byte	31
	.word	1793
	.byte	1,1,32
	.word	101703
	.byte	32
	.word	1793
	.byte	0,3
	.word	101708
	.byte	29
	.byte	'seekfree_assistant_receive_callback_function',0,39,150,1,18
	.word	101726
	.byte	29
	.byte	'seekfree_assistant_transfer_device_enum',0,24,51,2
	.word	23855
	.byte	34
	.byte	'seekfree_assistant_transfer_callback',0,25,50,56
	.word	101648
	.byte	1,1,3
	.word	101625
	.byte	34
	.byte	'seekfree_assistant_receive_callback',0,25,51,56
	.word	101731
	.byte	1,1,3
	.word	101708
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L27:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,21,0,54,15,39,12,0,0,3,15,0,73,19,0,0,4,46,1,3,8,32,13
	.byte	58,15,59,15,57,15,54,15,39,12,0,0,5,5,0,3,8,58,15,59,15,57,15,73,19,0,0,6,11,0,0,0,7,36,0,3,8,11,15,62
	.byte	15,0,0,8,46,1,3,8,32,13,58,15,59,15,57,15,73,19,54,15,39,12,0,0,9,59,0,3,8,0,0,10,19,1,3,8,58,15,59,15
	.byte	57,15,11,15,0,0,11,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,12,23,1,58,15,59,15,57,15,11,15,0,0,13,13
	.byte	0,3,8,73,19,11,15,56,9,0,0,14,53,0,73,19,0,0,15,4,1,58,15,59,15,57,15,11,15,0,0,16,40,0,3,8,28,13,0,0
	.byte	17,11,1,0,0,18,1,1,11,15,73,19,0,0,19,33,0,47,15,0,0,20,19,1,58,15,59,15,57,15,11,15,0,0,21,46,1,3,8,58
	.byte	15,59,15,57,15,73,19,54,15,39,12,63,12,60,12,0,0,22,38,0,73,19,0,0,23,46,1,49,19,0,0,24,5,0,49,19,0,0
	.byte	25,29,1,49,19,0,0,26,11,0,49,19,0,0,27,11,1,49,19,0,0,28,46,0,3,8,58,15,59,15,57,15,54,15,63,12,60,12
	.byte	0,0,29,22,0,3,8,58,15,59,15,57,15,73,19,0,0,30,21,0,54,15,0,0,31,21,1,73,19,54,15,39,12,0,0,32,5,0,73
	.byte	19,0,0,33,21,1,54,15,39,12,0,0,34,52,0,3,8,58,15,59,15,57,15,73,19,63,12,60,12,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L28:
	.word	.L58-.L57
.L57:
	.half	3
	.word	.L60-.L59
.L59:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device',0
	.byte	'F:\\ADS\\AURIX-Studio-1.10.2\\tools\\Compilers\\Tasking_1.1r8\\ctc\\include\\',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc',0
	.byte	0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_Intrinsics.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Scu\\Std\\IfxScuWdt.h',0
	.byte	0,0,0
	.byte	'IfxScu_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxCpu_cfg.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Port\\Std\\IfxPort.h',0
	.byte	0,0,0
	.byte	'IfxPort_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Scu\\Std\\IfxScuCcu.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Src\\Std\\IfxSrc.h',0
	.byte	0,0,0
	.byte	'IfxSrc_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Asclin\\Std\\IfxAsclin.h',0
	.byte	0,0,0
	.byte	'IfxAsclin_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Lib\\DataHandling\\Ifx_Fifo.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Stm\\Std\\IfxStm.h',0
	.byte	0,0,0
	.byte	'IfxStm_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\SysSe\\Bsp\\Bsp.h',0
	.byte	0,0,0
	.byte	'zf_common_debug.h',0,2,0,0
	.byte	'zf_device_wireless_uart.h',0,3,0,0
	.byte	'zf_device_ble6a20.h',0,3,0,0
	.byte	'zf_device_bluetooth_ch9141.h',0,3,0,0
	.byte	'zf_device_wifi_uart.h',0,3,0,0
	.byte	'zf_device_wifi_spi.h',0,3,0,0
	.byte	'..\\libraries\\zf_components\\seekfree_assistant_interface.h',0,0,0,0
	.byte	'../libraries/zf_components/seekfree_assistant_interface.c',0,0,0,0
	.byte	'stdio.h',0,4,0,0
	.byte	'Platform_Types.h',0,5,0,0
	.byte	'ifx_types.h',0,5,0,0
	.byte	'zf_common_typedef.h',0,2,0,0
	.byte	'zf_common_fifo.h',0,2,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxScu_cfg.h',0
	.byte	0,0,0
	.byte	'IfxCpu_regdef.h',0,1,0,0
	.byte	'IfxFlash_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxSrc_cfg.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_PinMap\\IfxAsclin_PinMap.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\StdIf\\IfxStdIf.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\StdIf\\IfxStdIf_DPipe.h',0
	.byte	0,0,0
	.byte	'ifxAsclin_Asc.h',0,6,0,0
	.byte	'..\\libraries\\zf_components\\seekfree_assistant.h',0,0,0,0,0
.L60:
.L58:
	.sdecl	'.debug_info',debug,cluster('seekfree_assistant_interface_init')
	.sect	'.debug_info'
.L29:
	.word	317
	.half	3
	.word	.L30
	.byte	4,1
	.byte	'../libraries/zf_components/seekfree_assistant_interface.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L32,.L31
	.byte	2
	.word	.L25
	.byte	3
	.byte	'seekfree_assistant_interface_init',0,1,88,14,1,1,1
	.word	.L24,.L44,.L23
	.byte	4
	.byte	'transfer_device',0,1,88,89
	.word	.L45,.L46
	.byte	5
	.word	.L24,.L44
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('seekfree_assistant_interface_init')
	.sect	'.debug_abbrev'
.L30:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('seekfree_assistant_interface_init')
	.sect	'.debug_line'
.L31:
	.word	.L62-.L61
.L61:
	.half	3
	.word	.L64-.L63
.L63:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_components/seekfree_assistant_interface.c',0,0,0,0,0
.L64:
	.byte	5,14,7,0,5,2
	.word	.L24
	.byte	3,219,0,1,9
	.half	.L65-.L24
	.byte	3,6,1,9
	.half	.L66-.L65
	.byte	3,6,1,9
	.half	.L67-.L66
	.byte	3,6,1,9
	.half	.L68-.L67
	.byte	3,6,1,9
	.half	.L69-.L68
	.byte	3,6,1,9
	.half	.L70-.L69
	.byte	3,6,1,5,13,9
	.half	.L4-.L70
	.byte	3,94,1,5,52,9
	.half	.L71-.L4
	.byte	1,5,50,9
	.half	.L72-.L71
	.byte	1,5,13,9
	.half	.L73-.L72
	.byte	3,1,1,5,51,9
	.half	.L74-.L73
	.byte	1,5,49,9
	.half	.L75-.L74
	.byte	1,5,10,9
	.half	.L76-.L75
	.byte	3,1,1,5,13,9
	.half	.L5-.L76
	.byte	3,4,1,5,52,9
	.half	.L77-.L5
	.byte	1,5,50,9
	.half	.L78-.L77
	.byte	1,5,13,9
	.half	.L79-.L78
	.byte	3,1,1,5,51,9
	.half	.L80-.L79
	.byte	1,5,49,9
	.half	.L81-.L80
	.byte	1,5,10,9
	.half	.L82-.L81
	.byte	3,1,1,5,13,9
	.half	.L6-.L82
	.byte	3,4,1,5,52,9
	.half	.L83-.L6
	.byte	1,5,50,9
	.half	.L84-.L83
	.byte	1,5,13,9
	.half	.L85-.L84
	.byte	3,1,1,5,51,9
	.half	.L86-.L85
	.byte	1,5,49,9
	.half	.L87-.L86
	.byte	1,5,10,9
	.half	.L88-.L87
	.byte	3,1,1,5,13,9
	.half	.L7-.L88
	.byte	3,4,1,5,52,9
	.half	.L89-.L7
	.byte	1,5,50,9
	.half	.L90-.L89
	.byte	1,5,13,9
	.half	.L91-.L90
	.byte	3,1,1,5,51,9
	.half	.L92-.L91
	.byte	1,5,49,9
	.half	.L93-.L92
	.byte	1,5,10,9
	.half	.L94-.L93
	.byte	3,1,1,5,13,9
	.half	.L8-.L94
	.byte	3,4,1,5,52,9
	.half	.L95-.L8
	.byte	1,5,50,9
	.half	.L96-.L95
	.byte	1,5,13,9
	.half	.L97-.L96
	.byte	3,1,1,5,51,9
	.half	.L98-.L97
	.byte	1,5,49,9
	.half	.L99-.L98
	.byte	1,5,10,9
	.half	.L100-.L99
	.byte	3,1,1,5,13,9
	.half	.L9-.L100
	.byte	3,4,1,5,52,9
	.half	.L101-.L9
	.byte	1,5,50,9
	.half	.L102-.L101
	.byte	1,5,13,9
	.half	.L103-.L102
	.byte	3,1,1,5,51,9
	.half	.L104-.L103
	.byte	1,5,49,9
	.half	.L105-.L104
	.byte	1,5,10,9
	.half	.L106-.L105
	.byte	3,1,1,9
	.half	.L10-.L106
	.byte	3,6,1,5,1,9
	.half	.L12-.L10
	.byte	3,2,1,7,9
	.half	.L33-.L12
	.byte	0,1,1
.L62:
	.sdecl	'.debug_ranges',debug,cluster('seekfree_assistant_interface_init')
	.sect	'.debug_ranges'
.L32:
	.word	-1,.L24,0,.L33-.L24,0,0
	.sdecl	'.debug_info',debug,cluster('seekfree_assistant_transfer')
	.sect	'.debug_info'
.L34:
	.word	323
	.half	3
	.word	.L35
	.byte	4,1
	.byte	'../libraries/zf_components/seekfree_assistant_interface.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L37,.L36
	.byte	2
	.word	.L25
	.byte	3
	.byte	'seekfree_assistant_transfer',0,1,61,16
	.word	.L47
	.byte	1,1,1
	.word	.L20,.L48,.L19
	.byte	4
	.byte	'buff',0,1,61,58
	.word	.L49,.L50
	.byte	4
	.byte	'length',0,1,61,71
	.word	.L47,.L51
	.byte	5
	.word	.L20,.L48
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('seekfree_assistant_transfer')
	.sect	'.debug_abbrev'
.L35:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('seekfree_assistant_transfer')
	.sect	'.debug_line'
.L36:
	.word	.L108-.L107
.L107:
	.half	3
	.word	.L110-.L109
.L109:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_components/seekfree_assistant_interface.c',0,0,0,0,0
.L110:
	.byte	5,5,7,0,5,2
	.word	.L20
	.byte	3,192,0,1,5,1,9
	.half	.L2-.L20
	.byte	3,1,1,7,9
	.half	.L38-.L2
	.byte	0,1,1
.L108:
	.sdecl	'.debug_ranges',debug,cluster('seekfree_assistant_transfer')
	.sect	'.debug_ranges'
.L37:
	.word	-1,.L20,0,.L38-.L20,0,0
	.sdecl	'.debug_info',debug,cluster('seekfree_assistant_receive')
	.sect	'.debug_info'
.L39:
	.word	322
	.half	3
	.word	.L40
	.byte	4,1
	.byte	'../libraries/zf_components/seekfree_assistant_interface.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L42,.L41
	.byte	2
	.word	.L25
	.byte	3
	.byte	'seekfree_assistant_receive',0,1,75,16
	.word	.L47
	.byte	1,1,1
	.word	.L22,.L52,.L21
	.byte	4
	.byte	'buff',0,1,75,51
	.word	.L53,.L54
	.byte	4
	.byte	'length',0,1,75,64
	.word	.L47,.L55
	.byte	5
	.word	.L22,.L52
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('seekfree_assistant_receive')
	.sect	'.debug_abbrev'
.L40:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('seekfree_assistant_receive')
	.sect	'.debug_line'
.L41:
	.word	.L112-.L111
.L111:
	.half	3
	.word	.L114-.L113
.L113:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_components/seekfree_assistant_interface.c',0,0,0,0,0
.L114:
	.byte	5,12,7,0,5,2
	.word	.L22
	.byte	3,205,0,1,5,5,9
	.half	.L115-.L22
	.byte	1,5,1,9
	.half	.L3-.L115
	.byte	3,1,1,7,9
	.half	.L43-.L3
	.byte	0,1,1
.L112:
	.sdecl	'.debug_ranges',debug,cluster('seekfree_assistant_receive')
	.sect	'.debug_ranges'
.L42:
	.word	-1,.L22,0,.L43-.L22,0,0
	.sdecl	'.debug_loc',debug,cluster('seekfree_assistant_interface_init')
	.sect	'.debug_loc'
.L23:
	.word	-1,.L24,0,.L44-.L24
	.half	2
	.byte	138,0
	.word	0,0
.L46:
	.word	-1,.L24,0,.L44-.L24
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('seekfree_assistant_receive')
	.sect	'.debug_loc'
.L54:
	.word	-1,.L22,0,.L52-.L22
	.half	1
	.byte	100
	.word	0,0
.L55:
	.word	-1,.L22,0,.L52-.L22
	.half	1
	.byte	84
	.word	0,0
.L21:
	.word	-1,.L22,0,.L52-.L22
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('seekfree_assistant_transfer')
	.sect	'.debug_loc'
.L50:
	.word	-1,.L20,0,.L48-.L20
	.half	1
	.byte	100
	.word	0,0
.L51:
	.word	-1,.L20,0,.L48-.L20
	.half	1
	.byte	84
	.word	.L56-.L20,.L48-.L20
	.half	1
	.byte	82
	.word	0,0
.L19:
	.word	-1,.L20,0,.L48-.L20
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L116:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('seekfree_assistant_transfer')
	.sect	'.debug_frame'
	.word	24
	.word	.L116,.L20,.L48-.L20
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('seekfree_assistant_receive')
	.sect	'.debug_frame'
	.word	24
	.word	.L116,.L22,.L52-.L22
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('seekfree_assistant_interface_init')
	.sect	'.debug_frame'
	.word	24
	.word	.L116,.L24,.L44-.L24
	.byte	8,19,8,20,8,21,8,22,8,23,0,0
	; Module end
