<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<launchConfiguration type="si.isystem.eclipse.debug.launch.debugLaunchType">
    <stringAttribute key="com.infineon.auriy.buildsystem.debug.launch.BUILD_CONFIGURATION_NAME" value="Debug"/>
    <stringAttribute key="communicationType" value="USB"/>
    <booleanAttribute key="createSMPConfig" value="true"/>
    <stringAttribute key="deviceName" value="TC264D"/>
    <stringAttribute key="hardwareType" value="IFX_DAS"/>
    <stringAttribute key="imageCheck" value="Default"/>
    <booleanAttribute key="isAnalyzeSourceFiles" value="false"/>
    <booleanAttribute key="isBringWinIdeaToTop" value="true"/>
    <booleanAttribute key="isLaunchInvisibleWinIdeaInstance" value="true"/>
    <booleanAttribute key="isSaveWinIdeaWorkspace" value="true"/>
    <booleanAttribute key="isShutDownWinIdeaAfterTerminate" value="true"/>
    <booleanAttribute key="isVerboseMode" value="false"/>
    <booleanAttribute key="isVerifyAfterDownload" value="true"/>
    <intAttribute key="org.eclipse.cdt.launch.ATTR_BUILD_BEFORE_LAUNCH_ATTR" value="2"/>
    <stringAttribute key="org.eclipse.cdt.launch.DEBUGGER_ID" value="IConnectDebugger.winIdea"/>
    <booleanAttribute key="org.eclipse.cdt.launch.DEBUGGER_STOP_AT_MAIN" value="true"/>
    <stringAttribute key="org.eclipse.cdt.launch.DEBUGGER_STOP_AT_MAIN_SYMBOL" value="core0_main"/>
    <stringAttribute key="org.eclipse.cdt.launch.PROGRAM_NAME" value="Debug/Seekfree_TC264_Opensource_Library.elf"/>
    <stringAttribute key="org.eclipse.cdt.launch.PROJECT_ATTR" value="Seekfree_TC264_Opensource_Library"/>
    <booleanAttribute key="org.eclipse.cdt.launch.PROJECT_BUILD_CONFIG_AUTO_ATTR" value="false"/>
    <stringAttribute key="org.eclipse.cdt.launch.PROJECT_BUILD_CONFIG_ID_ATTR" value=""/>
    <booleanAttribute key="org.eclipse.debug.core.ATTR_FORCE_SYSTEM_CONSOLE_ENCODING" value="false"/>
    <listAttribute key="org.eclipse.debug.core.MAPPED_RESOURCE_PATHS">
        <listEntry value="/Seekfree_TC264_Opensource_Library"/>
    </listAttribute>
    <listAttribute key="org.eclipse.debug.core.MAPPED_RESOURCE_TYPES">
        <listEntry value="4"/>
    </listAttribute>
    <stringAttribute key="selectedDevice" value=""/>
    <stringAttribute key="workspaceFileName" value=".ads/winIDEAWorkspaces/Debug/ADS.xjrf"/>
</launchConfiguration>
