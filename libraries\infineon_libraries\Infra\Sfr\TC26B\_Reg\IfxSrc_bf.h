/**
 * \file IfxSrc_bf.h
 * \brief
 * \copyright Copyright (c) 2015 Infineon Technologies AG. All rights reserved.
 *
 * Version: TC26XB_UM_V1.2.R0
 * Specification: tc26xB_um_v1.2_MCSFR.xml (Revision: UM_V1.2)
 * MAY BE CHANGED BY USER [yes/no]: No
 *
 *                                 IMPORTANT NOTICE
 *
 * Infineon Technologies AG (Infineon) is supplying this file for use
 * exclusively with Infineon's microcontroller products. This file can be freely
 * distributed within development tools that are supporting such microcontroller
 * products.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS".  NO WARRANTIES, WHETHER EXPRESS, IMPLIED
 * OR STATUTORY, INCLUDING, BUT NOT LIMITED TO, IMPLIED WARRANTIES OF
 * MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE APPLY TO THIS SOFTWARE.
 * INFINEON SHALL NOT, IN ANY CIRCUMSTANCES, BE LIABLE FOR SPECIAL, INCIDENTAL,
 * OR CONSEQUENTIAL DAMAGES, FOR ANY REASON WHATSOEVER.
 *
 * \defgroup IfxLld_Src_BitfieldsMask Bitfields mask and offset
 * \ingroup IfxLld_Src
 * 
 */
#ifndef IFXSRC_BF_H
#define IFXSRC_BF_H 1
/******************************************************************************/
/******************************************************************************/
/** \addtogroup IfxLld_Src_BitfieldsMask
 * \{  */

/** \brief  Length for Ifx_SRC_SRCR_Bits.CLRR */
#define IFX_SRC_SRCR_CLRR_LEN (1u)

/** \brief  Mask for Ifx_SRC_SRCR_Bits.CLRR */
#define IFX_SRC_SRCR_CLRR_MSK (0x1u)

/** \brief  Offset for Ifx_SRC_SRCR_Bits.CLRR */
#define IFX_SRC_SRCR_CLRR_OFF (25u)

/** \brief  Length for Ifx_SRC_SRCR_Bits.ECC */
#define IFX_SRC_SRCR_ECC_LEN (6u)

/** \brief  Mask for Ifx_SRC_SRCR_Bits.ECC */
#define IFX_SRC_SRCR_ECC_MSK (0x3fu)

/** \brief  Offset for Ifx_SRC_SRCR_Bits.ECC */
#define IFX_SRC_SRCR_ECC_OFF (16u)

/** \brief  Length for Ifx_SRC_SRCR_Bits.IOV */
#define IFX_SRC_SRCR_IOV_LEN (1u)

/** \brief  Mask for Ifx_SRC_SRCR_Bits.IOV */
#define IFX_SRC_SRCR_IOV_MSK (0x1u)

/** \brief  Offset for Ifx_SRC_SRCR_Bits.IOV */
#define IFX_SRC_SRCR_IOV_OFF (27u)

/** \brief  Length for Ifx_SRC_SRCR_Bits.IOVCLR */
#define IFX_SRC_SRCR_IOVCLR_LEN (1u)

/** \brief  Mask for Ifx_SRC_SRCR_Bits.IOVCLR */
#define IFX_SRC_SRCR_IOVCLR_MSK (0x1u)

/** \brief  Offset for Ifx_SRC_SRCR_Bits.IOVCLR */
#define IFX_SRC_SRCR_IOVCLR_OFF (28u)

/** \brief  Length for Ifx_SRC_SRCR_Bits.SETR */
#define IFX_SRC_SRCR_SETR_LEN (1u)

/** \brief  Mask for Ifx_SRC_SRCR_Bits.SETR */
#define IFX_SRC_SRCR_SETR_MSK (0x1u)

/** \brief  Offset for Ifx_SRC_SRCR_Bits.SETR */
#define IFX_SRC_SRCR_SETR_OFF (26u)

/** \brief  Length for Ifx_SRC_SRCR_Bits.SRE */
#define IFX_SRC_SRCR_SRE_LEN (1u)

/** \brief  Mask for Ifx_SRC_SRCR_Bits.SRE */
#define IFX_SRC_SRCR_SRE_MSK (0x1u)

/** \brief  Offset for Ifx_SRC_SRCR_Bits.SRE */
#define IFX_SRC_SRCR_SRE_OFF (10u)

/** \brief  Length for Ifx_SRC_SRCR_Bits.SRPN */
#define IFX_SRC_SRCR_SRPN_LEN (8u)

/** \brief  Mask for Ifx_SRC_SRCR_Bits.SRPN */
#define IFX_SRC_SRCR_SRPN_MSK (0xffu)

/** \brief  Offset for Ifx_SRC_SRCR_Bits.SRPN */
#define IFX_SRC_SRCR_SRPN_OFF (0u)

/** \brief  Length for Ifx_SRC_SRCR_Bits.SRR */
#define IFX_SRC_SRCR_SRR_LEN (1u)

/** \brief  Mask for Ifx_SRC_SRCR_Bits.SRR */
#define IFX_SRC_SRCR_SRR_MSK (0x1u)

/** \brief  Offset for Ifx_SRC_SRCR_Bits.SRR */
#define IFX_SRC_SRCR_SRR_OFF (24u)

/** \brief  Length for Ifx_SRC_SRCR_Bits.SWS */
#define IFX_SRC_SRCR_SWS_LEN (1u)

/** \brief  Mask for Ifx_SRC_SRCR_Bits.SWS */
#define IFX_SRC_SRCR_SWS_MSK (0x1u)

/** \brief  Offset for Ifx_SRC_SRCR_Bits.SWS */
#define IFX_SRC_SRCR_SWS_OFF (29u)

/** \brief  Length for Ifx_SRC_SRCR_Bits.SWSCLR */
#define IFX_SRC_SRCR_SWSCLR_LEN (1u)

/** \brief  Mask for Ifx_SRC_SRCR_Bits.SWSCLR */
#define IFX_SRC_SRCR_SWSCLR_MSK (0x1u)

/** \brief  Offset for Ifx_SRC_SRCR_Bits.SWSCLR */
#define IFX_SRC_SRCR_SWSCLR_OFF (30u)

/** \brief  Length for Ifx_SRC_SRCR_Bits.TOS */
#define IFX_SRC_SRCR_TOS_LEN (2u)

/** \brief  Mask for Ifx_SRC_SRCR_Bits.TOS */
#define IFX_SRC_SRCR_TOS_MSK (0x3u)

/** \brief  Offset for Ifx_SRC_SRCR_Bits.TOS */
#define IFX_SRC_SRCR_TOS_OFF (11u)
/** \}  */
/******************************************************************************/
/******************************************************************************/
#endif /* IFXSRC_BF_H */
