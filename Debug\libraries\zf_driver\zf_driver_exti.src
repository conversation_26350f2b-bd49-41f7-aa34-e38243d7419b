	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc34924a --dep-file=zf_driver_exti.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/zf_driver/zf_driver_exti.src ../libraries/zf_driver/zf_driver_exti.c"
	.compiler_name		"ctc"
	;source	'../libraries/zf_driver/zf_driver_exti.c'

	
$TC16X
	
	.sdecl	'.text.zf_driver_exti.get_exit_pin',code,cluster('get_exit_pin')
	.sect	'.text.zf_driver_exti.get_exit_pin'
	.align	2
	
; Function get_exit_pin
.L57:
get_exit_pin:	.type	func
	mov	d15,#1
	jeq	d15,d4,.L2
.L329:
	mov	d15,#4
	jeq	d15,d4,.L3
.L330:
	mov	d15,#6
	jeq	d15,d4,.L4
.L331:
	mov	d15,#7
	jeq	d15,d4,.L5
.L332:
	mov	d15,#8
	jeq	d15,d4,.L6
.L333:
	mov	d15,#9
	jeq	d15,d4,.L7
.L334:
	mov	d15,#10
	jeq	d15,d4,.L8
.L335:
	mov	d15,#11
	jeq	d15,d4,.L9
.L336:
	mov	d15,#12
	jeq	d15,d4,.L10
.L337:
	mov	d15,#13
	jeq	d15,d4,.L11
.L338:
	mov	d15,#15
	jeq	d15,d4,.L12
.L339:
	mov	d15,#18
	jeq	d15,d4,.L13
.L340:
	mov	d15,#19
	jeq	d15,d4,.L14
.L341:
	mov	d15,#21
	jeq	d15,d4,.L15
.L342:
	mov	d15,#22
	jeq	d15,d4,.L16
	j	.L17
.L2:
	movh.a	a2,#@his(IfxScu_REQ0_P15_4_IN)
.L203:
	lea	a2,[a2]@los(IfxScu_REQ0_P15_4_IN)
.L343:
	j	.L18
.L3:
	movh.a	a2,#@his(IfxScu_REQ10_P14_3_IN)
.L204:
	lea	a2,[a2]@los(IfxScu_REQ10_P14_3_IN)
.L344:
	j	.L19
.L4:
	movh.a	a2,#@his(IfxScu_REQ7_P00_4_IN)
.L205:
	lea	a2,[a2]@los(IfxScu_REQ7_P00_4_IN)
.L345:
	j	.L20
.L5:
	movh.a	a2,#@his(IfxScu_REQ14_P02_1_IN)
.L206:
	lea	a2,[a2]@los(IfxScu_REQ14_P02_1_IN)
.L346:
	j	.L21
.L6:
	movh.a	a2,#@his(IfxScu_REQ2_P10_2_IN)
.L207:
	lea	a2,[a2]@los(IfxScu_REQ2_P10_2_IN)
.L347:
	j	.L22
.L7:
	movh.a	a2,#@his(IfxScu_REQ6_P02_0_IN)
.L208:
	lea	a2,[a2]@los(IfxScu_REQ6_P02_0_IN)
.L348:
	j	.L23
.L8:
	movh.a	a2,#@his(IfxScu_REQ3_P10_3_IN)
.L209:
	lea	a2,[a2]@los(IfxScu_REQ3_P10_3_IN)
.L349:
	j	.L24
.L9:
	movh.a	a2,#@his(IfxScu_REQ15_P14_1_IN)
.L210:
	lea	a2,[a2]@los(IfxScu_REQ15_P14_1_IN)
.L350:
	j	.L25
.L10:
	movh.a	a2,#@his(IfxScu_REQ13_P15_5_IN)
.L211:
	lea	a2,[a2]@los(IfxScu_REQ13_P15_5_IN)
.L351:
	j	.L26
.L11:
	movh.a	a2,#@his(IfxScu_REQ8_P33_7_IN)
.L212:
	lea	a2,[a2]@los(IfxScu_REQ8_P33_7_IN)
.L352:
	j	.L27
.L12:
	movh.a	a2,#@his(IfxScu_REQ1_P15_8_IN)
.L213:
	lea	a2,[a2]@los(IfxScu_REQ1_P15_8_IN)
.L353:
	j	.L28
.L13:
	movh.a	a2,#@his(IfxScu_REQ12_P11_10_IN)
.L214:
	lea	a2,[a2]@los(IfxScu_REQ12_P11_10_IN)
.L354:
	j	.L29
.L14:
	movh.a	a2,#@his(IfxScu_REQ9_P20_0_IN)
.L215:
	lea	a2,[a2]@los(IfxScu_REQ9_P20_0_IN)
.L355:
	j	.L30
.L15:
	movh.a	a2,#@his(IfxScu_REQ16_P15_1_IN)
.L216:
	lea	a2,[a2]@los(IfxScu_REQ16_P15_1_IN)
.L356:
	j	.L31
.L16:
	movh.a	a2,#@his(IfxScu_REQ11_P20_9_IN)
.L217:
	lea	a2,[a2]@los(IfxScu_REQ11_P20_9_IN)
.L357:
	j	.L32
.L17:
	mov	d4,#0
.L202:
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#69
	call	debug_assert_handler
.L358:
	mov.a	a2,#0
.L32:
.L31:
.L30:
.L29:
.L28:
.L27:
.L26:
.L25:
.L24:
.L23:
.L22:
.L21:
.L20:
.L19:
.L18:
	j	.L33
.L33:
	ret
.L199:
	
__get_exit_pin_function_end:
	.size	get_exit_pin,__get_exit_pin_function_end-get_exit_pin
.L94:
	; End of function
	
	.sdecl	'.text.zf_driver_exti.exti_enable',code,cluster('exti_enable')
	.sect	'.text.zf_driver_exti.exti_enable'
	.align	2
	
	.global	exti_enable
; Function exti_enable
.L59:
exti_enable:	.type	func
	mov	d15,#3
.L295:
	div	e0,d4,d15
.L218:
	mov	d15,#4
.L296:
	div	e0,d0,d15
.L219:
	mul	d15,d1,#4
	mov.a	a15,d15
	movh.a	a3,#61444
	add.a	a3,a15
	lea	a15,[a3]-29484
.L113:
	ld.bu	d15,[a15]1
.L297:
	or	d15,#4
	st.b	[a15]1,d15
.L114:
	ret
.L106:
	
__exti_enable_function_end:
	.size	exti_enable,__exti_enable_function_end-exti_enable
.L79:
	; End of function
	
	.sdecl	'.text.zf_driver_exti.exti_disable',code,cluster('exti_disable')
	.sect	'.text.zf_driver_exti.exti_disable'
	.align	2
	
	.global	exti_disable
; Function exti_disable
.L61:
exti_disable:	.type	func
	mov	d15,#3
.L302:
	div	e0,d4,d15
.L220:
	mov	d15,#4
.L303:
	div	e0,d0,d15
.L221:
	mul	d15,d1,#4
	mov.a	a15,d15
	movh.a	a3,#61444
	add.a	a3,a15
	lea	a15,[a3]-29484
.L123:
	ld.bu	d15,[a15]1
.L304:
	insert	d15,d15,#0,#2,#1
	st.b	[a15]1,d15
.L124:
	ret
.L118:
	
__exti_disable_function_end:
	.size	exti_disable,__exti_disable_function_end-exti_disable
.L84:
	; End of function
	
	.sdecl	'.text.zf_driver_exti.exti_all_close',code,cluster('exti_all_close')
	.sect	'.text.zf_driver_exti.exti_all_close'
	.align	2
	
	.global	exti_all_close
; Function exti_all_close
.L63:
exti_all_close:	.type	func
	mov	d15,#0
.L222:
	j	.L34
.L35:
	mul	d0,d15,#4
	mov.a	a15,d0
	movh.a	a3,#61444
	add.a	a3,a15
	lea	a15,[a3]-29484
.L101:
	mov	d0,#0
.L289:
	st.w	[a15],d0
.L102:
	add	d15,#1
.L34:
	jlt	d15,#4,.L35
.L290:
	ret
.L95:
	
__exti_all_close_function_end:
	.size	exti_all_close,__exti_all_close_function_end-exti_all_close
.L74:
	; End of function
	
	.sdecl	'.text.zf_driver_exti.exti_init',code,cluster('exti_init')
	.sect	'.text.zf_driver_exti.exti_init'
	.align	2
	
	.global	exti_init
; Function exti_init
.L65:
exti_init:	.type	func
	sub.a	a10,#8
.L223:
	mov	e10,d5,d4
.L137:
	mfcr	d0,#65068
.L225:
	extr.u	d0,d0,#15,#1
.L226:
	ne	d14,d0,#0
.L227:
	j	.L36
.L36:
	disable
.L309:
	nop
.L310:
	j	.L37
.L37:
	j	.L38
.L38:
	mov	d4,d10
.L228:
	call	get_exit_pin
.L224:
	mov.aa	a15,a2
.L229:
	mov	d5,#16
.L147:
	ld.a	a4,[a15]8
.L311:
	ld.bu	d4,[a15]12
.L155:
	call	IfxPort_setPinMode
.L156:
	ld.bu	d4,[a15]4
.L312:
	ld.bu	d5,[a15]16
.L313:
	call	IfxScuEru_selectExternalInput
.L148:
	ld.bu	d15,[a15]4
.L231:
	st.w	[a10],d15
.L233:
	mov	d15,#3
.L232:
	div	e12,d10,d15
.L234:
	mov	d15,#3
.L235:
	div	e8,d10,d15
.L236:
	mov	d15,#0
.L237:
	jeq	d15,d11,.L39
.L238:
	mov	d15,#1
.L239:
	jeq	d15,d11,.L40
.L240:
	mov	d15,#2
.L241:
	jeq	d15,d11,.L41
.L242:
	j	.L42
.L39:
	ld.w	d4,[a10]
.L243:
	call	IfxScuEru_disableFallingEdgeDetection
.L244:
	ld.w	d4,[a10]
.L245:
	call	IfxScuEru_enableRisingEdgeDetection
.L246:
	j	.L43
.L40:
	ld.w	d4,[a10]
.L247:
	call	IfxScuEru_enableFallingEdgeDetection
.L248:
	ld.w	d4,[a10]
.L249:
	call	IfxScuEru_disableRisingEdgeDetection
.L250:
	j	.L44
.L41:
	ld.w	d4,[a10]
.L251:
	call	IfxScuEru_enableFallingEdgeDetection
.L252:
	ld.w	d4,[a10]
.L253:
	call	IfxScuEru_enableRisingEdgeDetection
.L254:
	j	.L45
.L42:
	mov	d4,#0
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#165
	call	debug_assert_handler
.L45:
.L44:
.L43:
	ld.w	d4,[a10]
.L255:
	call	IfxScuEru_enableTriggerPulse
.L256:
	ld.w	d4,[a10]
.L257:
	mov	d5,d12
.L259:
	call	IfxScuEru_connectTrigger
.L258:
	mov	d6,#1
	mov	d4,d8
.L260:
	ld.w	d5,[a10]
.L262:
	call	IfxScuEru_setFlagPatternDetection
.L261:
	mov	d4,d8
.L263:
	call	IfxScuEru_enablePatternDetectionTrigger
.L264:
	mov	d5,#1
	mov	d4,d8
.L265:
	call	IfxScuEru_setInterruptGatingPattern
.L169:
	mov	d15,#4
.L314:
	div	e0,d8,d15
.L315:
	mul	d15,d1,#4
	mov.a	a15,d15
.L230:
	movh.a	a3,#61444
	add.a	a3,a15
	lea	a15,[a3]-29484
.L266:
	mov	d15,#3
.L267:
	div	e0,d10,d15
.L268:
	mov	d15,#4
.L316:
	div	e0,d0,d15
.L317:
	mov	d15,#0
	jeq	d15,d1,.L46
.L318:
	mov	d15,#1
	jeq	d15,d1,.L47
.L319:
	mov	d15,#2
	jeq	d15,d1,.L48
.L320:
	mov	d15,#3
	jeq	d15,d1,.L49
	j	.L50
.L46:
	mov	d0,#0
.L269:
	mov	d15,#40
.L270:
	j	.L51
.L47:
	mov	d0,#0
.L271:
	mov	d15,#41
.L272:
	j	.L52
.L48:
	mov	d0,#3
.L273:
	mov	d15,#5
.L274:
	j	.L53
.L49:
	mov	d0,#0
.L275:
	mov	d15,#43
.L276:
	j	.L54
.L50:
.L54:
.L53:
.L52:
.L51:
	ld.bu	d1,[a15]
.L277:
	insert	d15,d1,d15,#0,#8
.L278:
	st.b	[a15],d15
.L321:
	ld.bu	d15,[a15]1
.L279:
	insert	d15,d15,d0,#3,#2
.L280:
	st.b	[a15]1,d15
.L184:
	ld.bu	d15,[a15]3
.L322:
	or	d15,#2
	st.b	[a15]3,d15
.L175:
	ld.bu	d15,[a15]1
.L323:
	or	d15,#4
	st.b	[a15]1,d15
.L188:
	mov	d15,d14
.L191:
	jeq	d15,#0,.L55
.L324:
	enable
.L55:
	ret
.L128:
	
__exti_init_function_end:
	.size	exti_init,__exti_init_function_end-exti_init
.L89:
	; End of function
	
	.sdecl	'.rodata.zf_driver_exti..1.str',data,rom
	.sect	'.rodata.zf_driver_exti..1.str'
.1.str:	.type	object
	.size	.1.str,40
	.byte	46,46,47,108,105,98,114,97
	.byte	114,105,101,115,47,122,102,95
	.byte	100,114,105,118,101,114,47,122
	.byte	102,95,100,114,105,118,101,114
	.byte	95,101,120,116
	.byte	105,46,99
	.space	1
	.calls	'get_exit_pin','debug_assert_handler'
	.calls	'exti_init','get_exit_pin'
	.calls	'exti_init','IfxPort_setPinMode'
	.calls	'exti_init','IfxScuEru_selectExternalInput'
	.calls	'exti_init','IfxScuEru_disableFallingEdgeDetection'
	.calls	'exti_init','IfxScuEru_enableRisingEdgeDetection'
	.calls	'exti_init','IfxScuEru_enableFallingEdgeDetection'
	.calls	'exti_init','IfxScuEru_disableRisingEdgeDetection'
	.calls	'exti_init','debug_assert_handler'
	.calls	'exti_init','IfxScuEru_enableTriggerPulse'
	.calls	'exti_init','IfxScuEru_connectTrigger'
	.calls	'exti_init','IfxScuEru_setFlagPatternDetection'
	.calls	'exti_init','IfxScuEru_enablePatternDetectionTrigger'
	.calls	'exti_init','IfxScuEru_setInterruptGatingPattern'
	.calls	'get_exit_pin','',0
	.calls	'exti_enable','',0
	.calls	'exti_disable','',0
	.calls	'exti_all_close','',0
	.extern	IfxPort_setPinMode
	.extern	IfxScu_REQ0_P15_4_IN
	.extern	IfxScu_REQ10_P14_3_IN
	.extern	IfxScu_REQ11_P20_9_IN
	.extern	IfxScu_REQ12_P11_10_IN
	.extern	IfxScu_REQ13_P15_5_IN
	.extern	IfxScu_REQ14_P02_1_IN
	.extern	IfxScu_REQ15_P14_1_IN
	.extern	IfxScu_REQ16_P15_1_IN
	.extern	IfxScu_REQ1_P15_8_IN
	.extern	IfxScu_REQ2_P10_2_IN
	.extern	IfxScu_REQ3_P10_3_IN
	.extern	IfxScu_REQ6_P02_0_IN
	.extern	IfxScu_REQ7_P00_4_IN
	.extern	IfxScu_REQ8_P33_7_IN
	.extern	IfxScu_REQ9_P20_0_IN
	.extern	debug_assert_handler
	.extern	IfxScuEru_selectExternalInput
	.extern	IfxScuEru_disableFallingEdgeDetection
	.extern	IfxScuEru_disableRisingEdgeDetection
	.extern	IfxScuEru_enableFallingEdgeDetection
	.extern	IfxScuEru_enableRisingEdgeDetection
	.extern	IfxScuEru_connectTrigger
	.extern	IfxScuEru_enableTriggerPulse
	.extern	IfxScuEru_setFlagPatternDetection
	.extern	IfxScuEru_enablePatternDetectionTrigger
	.extern	IfxScuEru_setInterruptGatingPattern
	.calls	'exti_init','',8
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L67:
	.word	86125
	.half	3
	.word	.L68
	.byte	4
.L66:
	.byte	1
	.byte	'../libraries/zf_driver/zf_driver_exti.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L69
	.byte	2,1,1,3
	.word	202
	.byte	4
	.byte	'Ifx__jump_and_link',0,3,1,61,17,1,1,5
	.byte	'fun',0,1,61,43
	.word	205
	.byte	6,0,7
	.byte	'__fract',0,4,128,1,7
	.byte	'float',0,4,4,8
	.byte	'Ifx__float_to_fract',0,3,1,152,2,18
	.word	250
	.byte	1,1,5
	.byte	'a',0,1,152,2,44
	.word	262
	.byte	6,0,4
	.byte	'Ifx__stopPerfCounters',0,3,1,172,2,17,1,1,6,0,7
	.byte	'unsigned long long int',0,8,7,9
	.byte	'void',0,3
	.word	374
	.byte	8
	.byte	'__ld64',0,3,2,135,1,19
	.word	348
	.byte	1,1,5
	.byte	'addr',0,2,135,1,32
	.word	380
	.byte	6,0,4
	.byte	'__st64',0,3,2,143,1,17,1,1,5
	.byte	'addr',0,2,143,1,30
	.word	380
	.byte	5
	.byte	'value',0,2,143,1,43
	.word	348
	.byte	6,0,7
	.byte	'unsigned int',0,4,7,7
	.byte	'int',0,4,5
.L132:
	.byte	7
	.byte	'unsigned char',0,1,8,10
	.byte	'_Ifx_SRC_SRCR_Bits',0,4,45,16,4,11
	.byte	'SRPN',0,1
	.word	489
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	489
	.byte	2,6,2,35,1,11
	.byte	'SRE',0,1
	.word	489
	.byte	1,5,2,35,1,11
	.byte	'TOS',0,1
	.word	489
	.byte	2,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	489
	.byte	3,0,2,35,1,11
	.byte	'ECC',0,1
	.word	489
	.byte	6,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	489
	.byte	2,0,2,35,2,11
	.byte	'SRR',0,1
	.word	489
	.byte	1,7,2,35,3,11
	.byte	'CLRR',0,1
	.word	489
	.byte	1,6,2,35,3,11
	.byte	'SETR',0,1
	.word	489
	.byte	1,5,2,35,3,11
	.byte	'IOV',0,1
	.word	489
	.byte	1,4,2,35,3,11
	.byte	'IOVCLR',0,1
	.word	489
	.byte	1,3,2,35,3,11
	.byte	'SWS',0,1
	.word	489
	.byte	1,2,2,35,3,11
	.byte	'SWSCLR',0,1
	.word	489
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	489
	.byte	1,0,2,35,3,0,12,4,70,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	506
	.byte	4,2,35,0,0,14
	.word	796
.L96:
	.byte	3
	.word	835
.L183:
	.byte	4
	.byte	'IfxSrc_clearRequest',0,3,3,250,1,17,1,1
.L185:
	.byte	5
	.byte	'src',0,3,250,1,60
	.word	840
.L187:
	.byte	6,0
.L100:
	.byte	4
	.byte	'IfxSrc_deinit',0,3,3,128,2,17,1,1
.L103:
	.byte	5
	.byte	'src',0,3,128,2,54
	.word	840
.L105:
	.byte	6,0
.L122:
	.byte	4
	.byte	'IfxSrc_disable',0,3,3,134,2,17,1,1
.L125:
	.byte	5
	.byte	'src',0,3,134,2,55
	.word	840
.L127:
	.byte	6,0
.L112:
	.byte	4
	.byte	'IfxSrc_enable',0,3,3,140,2,17,1,1
.L115:
	.byte	5
	.byte	'src',0,3,140,2,54
	.word	840
.L117:
	.byte	6,0
.L171:
	.byte	15,5,69,9,1,16
	.byte	'IfxSrc_Tos_cpu0',0,0,16
	.byte	'IfxSrc_Tos_cpu1',0,1,16
	.byte	'IfxSrc_Tos_dma',0,3,0,7
	.byte	'unsigned short int',0,2,7
.L174:
	.byte	4
	.byte	'IfxSrc_init',0,3,3,146,2,17,1,1
.L176:
	.byte	5
	.byte	'src',0,3,146,2,52
	.word	840
.L178:
	.byte	5
	.byte	'typOfService',0,3,146,2,68
	.word	1000
.L180:
	.byte	5
	.byte	'priority',0,3,146,2,95
	.word	1059
.L182:
	.byte	17,6,0,0,7
	.byte	'unsigned int',0,4,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON0_Bits',0,7,241,8,16,4,11
	.byte	'ENDINIT',0,4
	.word	1158
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	1158
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	1158
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	1158
	.byte	16,0,2,35,0,0,12,7,247,14,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1174
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_WDTCPU_CON1_Bits',0,7,250,8,16,4,11
	.byte	'reserved_0',0,1
	.word	489
	.byte	2,6,2,35,0,11
	.byte	'IR0',0,1
	.word	489
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	489
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	489
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	489
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	489
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	489
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	489
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	489
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1059
	.byte	16,0,2,35,2,0,12,7,255,14,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1310
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_WDTCPU_SR_Bits',0,7,137,9,16,4,11
	.byte	'AE',0,1
	.word	489
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	489
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	489
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	489
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	489
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	489
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	489
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	489
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	489
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	489
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	1059
	.byte	16,0,2,35,2,0,12,7,135,15,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1554
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_WDTCPU',0,7,175,15,25,12,13
	.byte	'CON0',0
	.word	1270
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	1514
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	1745
	.byte	4,2,35,8,0,14
	.word	1785
	.byte	3
	.word	1848
	.byte	4
	.byte	'IfxScuWdt_clearCpuEndinitInline',0,3,6,181,3,17,1,1,5
	.byte	'watchdog',0,6,181,3,65
	.word	1853
	.byte	5
	.byte	'password',0,6,181,3,82
	.word	1059
	.byte	6,0,4
	.byte	'IfxScuWdt_setCpuEndinitInline',0,3,6,140,4,17,1,1,5
	.byte	'watchdog',0,6,140,4,63
	.word	1853
	.byte	5
	.byte	'password',0,6,140,4,80
	.word	1059
	.byte	6,0,8
	.byte	'IfxScuWdt_getCpuWatchdogPasswordInline',0,3,6,227,3,19
	.word	1059
	.byte	1,1,5
	.byte	'watchdog',0,6,227,3,74
	.word	1853
	.byte	6,0,10
	.byte	'_Ifx_P_OUT_Bits',0,9,143,3,16,4,11
	.byte	'P0',0,1
	.word	489
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	489
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	489
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	489
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	489
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	489
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	489
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	489
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	489
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	489
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	489
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	489
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	489
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	489
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	489
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	489
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1059
	.byte	16,0,2,35,2,0,12,9,181,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2083
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMR_Bits',0,9,169,2,16,4,11
	.byte	'PS0',0,1
	.word	489
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	489
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	489
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	489
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	489
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	489
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	489
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	489
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	489
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	489
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	489
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	489
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	489
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	489
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	489
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	489
	.byte	1,0,2,35,1,11
	.byte	'PCL0',0,1
	.word	489
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	489
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	489
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	489
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	489
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	489
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	489
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	489
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	489
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	489
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	489
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	489
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	489
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	489
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	489
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	489
	.byte	1,0,2,35,3,0,12,9,133,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2399
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ID_Bits',0,9,110,16,4,11
	.byte	'MODREV',0,1
	.word	489
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	489
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	1059
	.byte	16,0,2,35,2,0,12,9,148,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2970
	.byte	4,2,35,0,0,18,4
	.word	489
	.byte	19,3,0,10
	.byte	'_Ifx_P_IOCR0_Bits',0,9,140,1,16,4,11
	.byte	'reserved_0',0,1
	.word	489
	.byte	3,5,2,35,0,11
	.byte	'PC0',0,1
	.word	489
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	489
	.byte	3,5,2,35,1,11
	.byte	'PC1',0,1
	.word	489
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	489
	.byte	3,5,2,35,2,11
	.byte	'PC2',0,1
	.word	489
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	489
	.byte	3,5,2,35,3,11
	.byte	'PC3',0,1
	.word	489
	.byte	5,0,2,35,3,0,12,9,164,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3098
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR4_Bits',0,9,166,1,16,4,11
	.byte	'reserved_0',0,1
	.word	489
	.byte	3,5,2,35,0,11
	.byte	'PC4',0,1
	.word	489
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	489
	.byte	3,5,2,35,1,11
	.byte	'PC5',0,1
	.word	489
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	489
	.byte	3,5,2,35,2,11
	.byte	'PC6',0,1
	.word	489
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	489
	.byte	3,5,2,35,3,11
	.byte	'PC7',0,1
	.word	489
	.byte	5,0,2,35,3,0,12,9,180,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3313
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR8_Bits',0,9,179,1,16,4,11
	.byte	'reserved_0',0,1
	.word	489
	.byte	3,5,2,35,0,11
	.byte	'PC8',0,1
	.word	489
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	489
	.byte	3,5,2,35,1,11
	.byte	'PC9',0,1
	.word	489
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	489
	.byte	3,5,2,35,2,11
	.byte	'PC10',0,1
	.word	489
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	489
	.byte	3,5,2,35,3,11
	.byte	'PC11',0,1
	.word	489
	.byte	5,0,2,35,3,0,12,9,188,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3528
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR12_Bits',0,9,153,1,16,4,11
	.byte	'reserved_0',0,1
	.word	489
	.byte	3,5,2,35,0,11
	.byte	'PC12',0,1
	.word	489
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	489
	.byte	3,5,2,35,1,11
	.byte	'PC13',0,1
	.word	489
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	489
	.byte	3,5,2,35,2,11
	.byte	'PC14',0,1
	.word	489
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	489
	.byte	3,5,2,35,3,11
	.byte	'PC15',0,1
	.word	489
	.byte	5,0,2,35,3,0,12,9,172,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3745
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IN_Bits',0,9,118,16,4,11
	.byte	'P0',0,1
	.word	489
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	489
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	489
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	489
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	489
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	489
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	489
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	489
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	489
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	489
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	489
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	489
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	489
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	489
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	489
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	489
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1059
	.byte	16,0,2,35,2,0,12,9,156,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3965
	.byte	4,2,35,0,0,18,24
	.word	489
	.byte	19,23,0,10
	.byte	'_Ifx_P_PDR0_Bits',0,9,205,3,16,4,11
	.byte	'PD0',0,1
	.word	489
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	489
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	489
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	489
	.byte	1,0,2,35,0,11
	.byte	'PD2',0,1
	.word	489
	.byte	3,5,2,35,1,11
	.byte	'PL2',0,1
	.word	489
	.byte	1,4,2,35,1,11
	.byte	'PD3',0,1
	.word	489
	.byte	3,1,2,35,1,11
	.byte	'PL3',0,1
	.word	489
	.byte	1,0,2,35,1,11
	.byte	'PD4',0,1
	.word	489
	.byte	3,5,2,35,2,11
	.byte	'PL4',0,1
	.word	489
	.byte	1,4,2,35,2,11
	.byte	'PD5',0,1
	.word	489
	.byte	3,1,2,35,2,11
	.byte	'PL5',0,1
	.word	489
	.byte	1,0,2,35,2,11
	.byte	'PD6',0,1
	.word	489
	.byte	3,5,2,35,3,11
	.byte	'PL6',0,1
	.word	489
	.byte	1,4,2,35,3,11
	.byte	'PD7',0,1
	.word	489
	.byte	3,1,2,35,3,11
	.byte	'PL7',0,1
	.word	489
	.byte	1,0,2,35,3,0,12,9,205,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4288
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PDR1_Bits',0,9,226,3,16,4,11
	.byte	'PD8',0,1
	.word	489
	.byte	3,5,2,35,0,11
	.byte	'PL8',0,1
	.word	489
	.byte	1,4,2,35,0,11
	.byte	'PD9',0,1
	.word	489
	.byte	3,1,2,35,0,11
	.byte	'PL9',0,1
	.word	489
	.byte	1,0,2,35,0,11
	.byte	'PD10',0,1
	.word	489
	.byte	3,5,2,35,1,11
	.byte	'PL10',0,1
	.word	489
	.byte	1,4,2,35,1,11
	.byte	'PD11',0,1
	.word	489
	.byte	3,1,2,35,1,11
	.byte	'PL11',0,1
	.word	489
	.byte	1,0,2,35,1,11
	.byte	'PD12',0,1
	.word	489
	.byte	3,5,2,35,2,11
	.byte	'PL12',0,1
	.word	489
	.byte	1,4,2,35,2,11
	.byte	'PD13',0,1
	.word	489
	.byte	3,1,2,35,2,11
	.byte	'PL13',0,1
	.word	489
	.byte	1,0,2,35,2,11
	.byte	'PD14',0,1
	.word	489
	.byte	3,5,2,35,3,11
	.byte	'PL14',0,1
	.word	489
	.byte	1,4,2,35,3,11
	.byte	'PD15',0,1
	.word	489
	.byte	3,1,2,35,3,11
	.byte	'PL15',0,1
	.word	489
	.byte	1,0,2,35,3,0,12,9,213,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4592
	.byte	4,2,35,0,0,18,8
	.word	489
	.byte	19,7,0,10
	.byte	'_Ifx_P_ESR_Bits',0,9,88,16,4,11
	.byte	'EN0',0,1
	.word	489
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	489
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	489
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	489
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	489
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	489
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	489
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	489
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	489
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	489
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	489
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	489
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	489
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	489
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	489
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	489
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1059
	.byte	16,0,2,35,2,0,12,9,140,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4917
	.byte	4,2,35,0,0,18,12
	.word	489
	.byte	19,11,0,10
	.byte	'_Ifx_P_PDISC_Bits',0,9,183,3,16,4,11
	.byte	'PDIS0',0,1
	.word	489
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	489
	.byte	1,6,2,35,0,11
	.byte	'PDIS2',0,1
	.word	489
	.byte	1,5,2,35,0,11
	.byte	'PDIS3',0,1
	.word	489
	.byte	1,4,2,35,0,11
	.byte	'PDIS4',0,1
	.word	489
	.byte	1,3,2,35,0,11
	.byte	'PDIS5',0,1
	.word	489
	.byte	1,2,2,35,0,11
	.byte	'PDIS6',0,1
	.word	489
	.byte	1,1,2,35,0,11
	.byte	'PDIS7',0,1
	.word	489
	.byte	1,0,2,35,0,11
	.byte	'PDIS8',0,1
	.word	489
	.byte	1,7,2,35,1,11
	.byte	'PDIS9',0,1
	.word	489
	.byte	1,6,2,35,1,11
	.byte	'PDIS10',0,1
	.word	489
	.byte	1,5,2,35,1,11
	.byte	'PDIS11',0,1
	.word	489
	.byte	1,4,2,35,1,11
	.byte	'PDIS12',0,1
	.word	489
	.byte	1,3,2,35,1,11
	.byte	'PDIS13',0,1
	.word	489
	.byte	1,2,2,35,1,11
	.byte	'PDIS14',0,1
	.word	489
	.byte	1,1,2,35,1,11
	.byte	'PDIS15',0,1
	.word	489
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1059
	.byte	16,0,2,35,2,0,12,9,197,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5257
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PCSR_Bits',0,9,165,3,16,4,11
	.byte	'SEL0',0,1
	.word	489
	.byte	1,7,2,35,0,11
	.byte	'SEL1',0,1
	.word	489
	.byte	1,6,2,35,0,11
	.byte	'SEL2',0,1
	.word	489
	.byte	1,5,2,35,0,11
	.byte	'SEL3',0,1
	.word	489
	.byte	1,4,2,35,0,11
	.byte	'SEL4',0,1
	.word	489
	.byte	1,3,2,35,0,11
	.byte	'SEL5',0,1
	.word	489
	.byte	1,2,2,35,0,11
	.byte	'SEL6',0,1
	.word	489
	.byte	1,1,2,35,0,11
	.byte	'SEL7',0,1
	.word	489
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	489
	.byte	2,6,2,35,1,11
	.byte	'SEL10',0,1
	.word	489
	.byte	1,5,2,35,1,11
	.byte	'SEL11',0,1
	.word	489
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	466
	.byte	19,1,2,35,0,11
	.byte	'LCK',0,1
	.word	489
	.byte	1,0,2,35,3,0,12,9,189,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5623
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR0_Bits',0,9,206,2,16,4,11
	.byte	'PS0',0,1
	.word	489
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	489
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	489
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	489
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	466
	.byte	28,0,2,35,0,0,12,9,149,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5909
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR4_Bits',0,9,227,2,16,4,11
	.byte	'reserved_0',0,1
	.word	489
	.byte	4,4,2,35,0,11
	.byte	'PS4',0,1
	.word	489
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	489
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	489
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	489
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	466
	.byte	24,0,2,35,0,0,12,9,165,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6056
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR8_Bits',0,9,238,2,16,4,11
	.byte	'reserved_0',0,1
	.word	489
	.byte	8,0,2,35,0,11
	.byte	'PS8',0,1
	.word	489
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	489
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	489
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	489
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	466
	.byte	20,0,2,35,0,0,12,9,173,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6225
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR12_Bits',0,9,216,2,16,4,11
	.byte	'reserved_0',0,2
	.word	1059
	.byte	12,4,2,35,0,11
	.byte	'PS12',0,1
	.word	489
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	489
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	489
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	489
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1059
	.byte	16,0,2,35,2,0,12,9,157,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6397
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR0_Bits',0,9,232,1,16,4,11
	.byte	'reserved_0',0,2
	.word	1059
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	489
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	489
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	489
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	489
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	1059
	.byte	12,0,2,35,2,0,12,9,229,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6572
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR4_Bits',0,9,253,1,16,4,11
	.byte	'reserved_0',0,4
	.word	466
	.byte	20,12,2,35,0,11
	.byte	'PCL4',0,1
	.word	489
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	489
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	489
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	489
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	489
	.byte	8,0,2,35,3,0,12,9,245,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6746
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR8_Bits',0,9,136,2,16,4,11
	.byte	'reserved_0',0,4
	.word	466
	.byte	24,8,2,35,0,11
	.byte	'PCL8',0,1
	.word	489
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	489
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	489
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	489
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	489
	.byte	4,0,2,35,3,0,12,9,253,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6920
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR12_Bits',0,9,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	466
	.byte	28,4,2,35,0,11
	.byte	'PCL12',0,1
	.word	489
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	489
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	489
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	489
	.byte	1,0,2,35,3,0,12,9,237,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7096
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR_Bits',0,9,249,2,16,4,11
	.byte	'PS0',0,1
	.word	489
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	489
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	489
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	489
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	489
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	489
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	489
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	489
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	489
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	489
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	489
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	489
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	489
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	489
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	489
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	489
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1059
	.byte	16,0,2,35,2,0,12,9,141,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7252
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR_Bits',0,9,147,2,16,4,11
	.byte	'reserved_0',0,2
	.word	1059
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	489
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	489
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	489
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	489
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	489
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	489
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	489
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	489
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	489
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	489
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	489
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	489
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	489
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	489
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	489
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	489
	.byte	1,0,2,35,3,0,12,9,221,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7585
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR0_Bits',0,9,192,1,16,4,11
	.byte	'reserved_0',0,1
	.word	489
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	489
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	466
	.byte	30,0,2,35,0,0,12,9,196,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7933
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_Bits',0,9,200,1,16,4,11
	.byte	'reserved_0',0,1
	.word	489
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	489
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	466
	.byte	30,0,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_P21_Bits',0,9,208,1,16,4,11
	.byte	'RDIS_CTRL',0,1
	.word	489
	.byte	1,7,2,35,0,11
	.byte	'RX_DIS',0,1
	.word	489
	.byte	1,6,2,35,0,11
	.byte	'TERM',0,1
	.word	489
	.byte	1,5,2,35,0,11
	.byte	'LRXTERM',0,1
	.word	489
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	466
	.byte	24,0,2,35,0,0,12,9,204,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8057
	.byte	4,2,35,0,13
	.byte	'B_P21',0
	.word	8141
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR2_Bits',0,9,218,1,16,4,11
	.byte	'reserved_0',0,1
	.word	489
	.byte	8,0,2,35,0,11
	.byte	'LVDSR',0,1
	.word	489
	.byte	1,7,2,35,1,11
	.byte	'LVDSRL',0,1
	.word	489
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	489
	.byte	2,4,2,35,1,11
	.byte	'TDIS_CTRL',0,1
	.word	489
	.byte	1,3,2,35,1,11
	.byte	'TX_DIS',0,1
	.word	489
	.byte	1,2,2,35,1,11
	.byte	'TX_PD',0,1
	.word	489
	.byte	1,1,2,35,1,11
	.byte	'TX_PWDPD',0,1
	.word	489
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1059
	.byte	16,0,2,35,2,0,12,9,213,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8321
	.byte	4,2,35,0,0,18,76
	.word	489
	.byte	19,75,0,10
	.byte	'_Ifx_P_ACCEN1_Bits',0,9,82,16,4,11
	.byte	'reserved_0',0,4
	.word	466
	.byte	32,0,2,35,0,0,12,9,132,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8574
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ACCEN0_Bits',0,9,45,16,4,11
	.byte	'EN0',0,1
	.word	489
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	489
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	489
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	489
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	489
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	489
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	489
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	489
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	489
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	489
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	489
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	489
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	489
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	489
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	489
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	489
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	489
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	489
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	489
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	489
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	489
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	489
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	489
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	489
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	489
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	489
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	489
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	489
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	489
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	489
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	489
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	489
	.byte	1,0,2,35,3,0,12,9,252,3,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8661
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P',0,9,229,5,25,128,2,13
	.byte	'OUT',0
	.word	2359
	.byte	4,2,35,0,13
	.byte	'OMR',0
	.word	2930
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	3049
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	3089
	.byte	4,2,35,12,13
	.byte	'IOCR0',0
	.word	3273
	.byte	4,2,35,16,13
	.byte	'IOCR4',0
	.word	3488
	.byte	4,2,35,20,13
	.byte	'IOCR8',0
	.word	3705
	.byte	4,2,35,24,13
	.byte	'IOCR12',0
	.word	3925
	.byte	4,2,35,28,13
	.byte	'reserved_20',0
	.word	3089
	.byte	4,2,35,32,13
	.byte	'IN',0
	.word	4239
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	4279
	.byte	24,2,35,40,13
	.byte	'PDR0',0
	.word	4552
	.byte	4,2,35,64,13
	.byte	'PDR1',0
	.word	4868
	.byte	4,2,35,68,13
	.byte	'reserved_48',0
	.word	4908
	.byte	8,2,35,72,13
	.byte	'ESR',0
	.word	5208
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	5248
	.byte	12,2,35,84,13
	.byte	'PDISC',0
	.word	5583
	.byte	4,2,35,96,13
	.byte	'PCSR',0
	.word	5869
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	4908
	.byte	8,2,35,104,13
	.byte	'OMSR0',0
	.word	6016
	.byte	4,2,35,112,13
	.byte	'OMSR4',0
	.word	6185
	.byte	4,2,35,116,13
	.byte	'OMSR8',0
	.word	6357
	.byte	4,2,35,120,13
	.byte	'OMSR12',0
	.word	6532
	.byte	4,2,35,124,13
	.byte	'OMCR0',0
	.word	6706
	.byte	4,3,35,128,1,13
	.byte	'OMCR4',0
	.word	6880
	.byte	4,3,35,132,1,13
	.byte	'OMCR8',0
	.word	7056
	.byte	4,3,35,136,1,13
	.byte	'OMCR12',0
	.word	7212
	.byte	4,3,35,140,1,13
	.byte	'OMSR',0
	.word	7545
	.byte	4,3,35,144,1,13
	.byte	'OMCR',0
	.word	7893
	.byte	4,3,35,148,1,13
	.byte	'reserved_98',0
	.word	4908
	.byte	8,3,35,152,1,13
	.byte	'LPCR0',0
	.word	8017
	.byte	4,3,35,160,1,13
	.byte	'LPCR1',0
	.word	8266
	.byte	4,3,35,164,1,13
	.byte	'LPCR2',0
	.word	8525
	.byte	4,3,35,168,1,13
	.byte	'reserved_A4',0
	.word	8565
	.byte	76,3,35,172,1,13
	.byte	'ACCEN1',0
	.word	8621
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	9188
	.byte	4,3,35,252,1,0,14
	.word	9228
	.byte	3
	.word	9831
	.byte	15,8,83,9,1,16
	.byte	'IfxPort_InputMode_undefined',0,127,16
	.byte	'IfxPort_InputMode_noPullDevice',0,0,16
	.byte	'IfxPort_InputMode_pullDown',0,8,16
	.byte	'IfxPort_InputMode_pullUp',0,16,0
.L154:
	.byte	4
	.byte	'IfxPort_setPinModeInput',0,3,8,196,4,17,1,1
.L157:
	.byte	5
	.byte	'port',0,8,196,4,48
	.word	9836
.L159:
	.byte	5
	.byte	'pinIndex',0,8,196,4,60
	.word	489
.L161:
	.byte	5
	.byte	'mode',0,8,196,4,88
	.word	9841
.L163:
	.byte	6,0,15,8,172,1,9,4,16
	.byte	'IfxPort_State_notChanged',0,0,16
	.byte	'IfxPort_State_high',0,1,16
	.byte	'IfxPort_State_low',0,128,128,4,16
	.byte	'IfxPort_State_toggled',0,129,128,4,0,4
	.byte	'IfxPort_setPinState',0,3,8,208,4,17,1,1,5
	.byte	'port',0,8,208,4,44
	.word	9836
	.byte	5
	.byte	'pinIndex',0,8,208,4,56
	.word	489
	.byte	5
	.byte	'action',0,8,208,4,80
	.word	10046
	.byte	6,0,8
	.byte	'IfxScuCcu_getStmFrequency',0,3,10,226,8,20
	.word	262
	.byte	1,1,6,0,15,12,156,1,9,1,16
	.byte	'IfxCpu_ResourceCpu_0',0,0,16
	.byte	'IfxCpu_ResourceCpu_1',0,1,16
	.byte	'IfxCpu_ResourceCpu_none',0,2,0,8
	.byte	'IfxCpu_getCoreIndex',0,3,11,141,6,31
	.word	10267
	.byte	1,1,6,0
.L142:
	.byte	8
	.byte	'IfxCpu_areInterruptsEnabled',0,3,11,139,5,20
	.word	489
	.byte	1,1
.L143:
	.byte	6,0
.L139:
	.byte	8
	.byte	'IfxCpu_disableInterrupts',0,3,11,147,5,20
	.word	489
	.byte	1,1
.L140:
	.byte	17,6,0,0,4
	.byte	'IfxCpu_enableInterrupts',0,3,11,157,5,17,1,1,6,0,4
	.byte	'IfxCpu_forceDisableInterrupts',0,3,11,225,5,17,1,1,6,0
.L195:
	.byte	4
	.byte	'IfxCpu_restoreInterrupts',0,3,11,168,7,17,1,1
.L196:
	.byte	5
	.byte	'enabled',0,11,168,7,50
	.word	489
.L198:
	.byte	6,0,7
	.byte	'unsigned long int',0,4,7,8
	.byte	'IfxCpu_getPerformanceCounter',0,3,11,161,6,19
	.word	10589
	.byte	1,1,5
	.byte	'address',0,11,161,6,55
	.word	1059
	.byte	6,0,8
	.byte	'IfxCpu_getPerformanceCounterStickyOverflow',0,3,11,190,6,20
	.word	489
	.byte	1,1,5
	.byte	'address',0,11,190,6,70
	.word	1059
	.byte	6,0,4
	.byte	'IfxCpu_updatePerformanceCounter',0,3,11,172,8,17,1,1,5
	.byte	'address',0,11,172,8,56
	.word	10589
	.byte	5
	.byte	'count',0,11,172,8,72
	.word	10589
	.byte	17,6,0,0,10
	.byte	'_Ifx_STM_CLC_Bits',0,14,100,16,4,11
	.byte	'DISR',0,1
	.word	489
	.byte	1,7,2,35,0,11
	.byte	'DISS',0,1
	.word	489
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	489
	.byte	1,5,2,35,0,11
	.byte	'EDIS',0,1
	.word	489
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	466
	.byte	28,0,2,35,0,0,12,14,149,2,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10820
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_ID_Bits',0,14,142,1,16,4,11
	.byte	'MODREV',0,1
	.word	489
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	489
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	1059
	.byte	16,0,2,35,2,0,12,14,181,2,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10976
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM0_Bits',0,14,192,1,16,4,11
	.byte	'STM31_0',0,4
	.word	466
	.byte	32,0,2,35,0,0,12,14,229,2,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11098
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM1_Bits',0,14,204,1,16,4,11
	.byte	'STM35_4',0,4
	.word	466
	.byte	32,0,2,35,0,0,12,14,245,2,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11183
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM2_Bits',0,14,210,1,16,4,11
	.byte	'STM39_8',0,4
	.word	466
	.byte	32,0,2,35,0,0,12,14,253,2,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11268
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM3_Bits',0,14,216,1,16,4,11
	.byte	'STM43_12',0,4
	.word	466
	.byte	32,0,2,35,0,0,12,14,133,3,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11353
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM4_Bits',0,14,222,1,16,4,11
	.byte	'STM47_16',0,4
	.word	466
	.byte	32,0,2,35,0,0,12,14,141,3,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11439
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM5_Bits',0,14,228,1,16,4,11
	.byte	'STM51_20',0,4
	.word	466
	.byte	32,0,2,35,0,0,12,14,149,3,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11525
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM6_Bits',0,14,234,1,16,4,11
	.byte	'STM63_32',0,4
	.word	466
	.byte	32,0,2,35,0,0,12,14,157,3,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11611
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_CAP_Bits',0,14,88,16,4,11
	.byte	'STMCAP63_32',0,4
	.word	466
	.byte	32,0,2,35,0,0,12,14,133,2,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11697
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_CMP_Bits',0,14,123,16,4,11
	.byte	'CMPVAL',0,4
	.word	466
	.byte	32,0,2,35,0,0,12,14,165,2,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11784
	.byte	4,2,35,0,0,18,8
	.word	11826
	.byte	19,1,0,10
	.byte	'_Ifx_STM_CMCON_Bits',0,14,110,16,4,11
	.byte	'MSIZE0',0,1
	.word	489
	.byte	5,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	489
	.byte	3,0,2,35,0,11
	.byte	'MSTART0',0,1
	.word	489
	.byte	5,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	489
	.byte	3,0,2,35,1,11
	.byte	'MSIZE1',0,1
	.word	489
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	489
	.byte	3,0,2,35,2,11
	.byte	'MSTART1',0,1
	.word	489
	.byte	5,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	489
	.byte	3,0,2,35,3,0,12,14,157,2,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11875
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_ICR_Bits',0,14,129,1,16,4,11
	.byte	'CMP0EN',0,1
	.word	489
	.byte	1,7,2,35,0,11
	.byte	'CMP0IR',0,1
	.word	489
	.byte	1,6,2,35,0,11
	.byte	'CMP0OS',0,1
	.word	489
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	489
	.byte	1,4,2,35,0,11
	.byte	'CMP1EN',0,1
	.word	489
	.byte	1,3,2,35,0,11
	.byte	'CMP1IR',0,1
	.word	489
	.byte	1,2,2,35,0,11
	.byte	'CMP1OS',0,1
	.word	489
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,4
	.word	466
	.byte	25,0,2,35,0,0,12,14,173,2,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12106
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_ISCR_Bits',0,14,150,1,16,4,11
	.byte	'CMP0IRR',0,1
	.word	489
	.byte	1,7,2,35,0,11
	.byte	'CMP0IRS',0,1
	.word	489
	.byte	1,6,2,35,0,11
	.byte	'CMP1IRR',0,1
	.word	489
	.byte	1,5,2,35,0,11
	.byte	'CMP1IRS',0,1
	.word	489
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	466
	.byte	28,0,2,35,0,0,12,14,189,2,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12323
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM0SV_Bits',0,14,198,1,16,4,11
	.byte	'STM31_0',0,4
	.word	466
	.byte	32,0,2,35,0,0,12,14,237,2,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12487
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_CAPSV_Bits',0,14,94,16,4,11
	.byte	'STMCAP63_32',0,4
	.word	466
	.byte	32,0,2,35,0,0,12,14,141,2,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12574
	.byte	4,2,35,0,0,18,144,1
	.word	489
	.byte	19,143,1,0,10
	.byte	'_Ifx_STM_OCS_Bits',0,14,182,1,16,4,11
	.byte	'reserved_0',0,4
	.word	466
	.byte	24,8,2,35,0,11
	.byte	'SUS',0,1
	.word	489
	.byte	4,4,2,35,3,11
	.byte	'SUS_P',0,1
	.word	489
	.byte	1,3,2,35,3,11
	.byte	'SUSSTA',0,1
	.word	489
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	489
	.byte	2,0,2,35,3,0,12,14,221,2,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12674
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_KRSTCLR_Bits',0,14,175,1,16,4,11
	.byte	'CLR',0,1
	.word	489
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	466
	.byte	31,0,2,35,0,0,12,14,213,2,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12834
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_KRST1_Bits',0,14,168,1,16,4,11
	.byte	'RST',0,1
	.word	489
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	466
	.byte	31,0,2,35,0,0,12,14,205,2,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12940
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_KRST0_Bits',0,14,160,1,16,4,11
	.byte	'RST',0,1
	.word	489
	.byte	1,7,2,35,0,11
	.byte	'RSTSTAT',0,1
	.word	489
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	466
	.byte	30,0,2,35,0,0,12,14,197,2,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13044
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_ACCEN1_Bits',0,14,82,16,4,11
	.byte	'reserved_0',0,4
	.word	466
	.byte	32,0,2,35,0,0,12,14,253,1,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13167
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_ACCEN0_Bits',0,14,45,16,4,11
	.byte	'EN0',0,1
	.word	489
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	489
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	489
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	489
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	489
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	489
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	489
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	489
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	489
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	489
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	489
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	489
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	489
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	489
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	489
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	489
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	489
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	489
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	489
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	489
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	489
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	489
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	489
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	489
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	489
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	489
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	489
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	489
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	489
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	489
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	489
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	489
	.byte	1,0,2,35,3,0,12,14,245,1,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13256
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM',0,14,173,3,25,128,2,13
	.byte	'CLC',0
	.word	10936
	.byte	4,2,35,0,13
	.byte	'reserved_4',0
	.word	3089
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	11058
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	3089
	.byte	4,2,35,12,13
	.byte	'TIM0',0
	.word	11143
	.byte	4,2,35,16,13
	.byte	'TIM1',0
	.word	11228
	.byte	4,2,35,20,13
	.byte	'TIM2',0
	.word	11313
	.byte	4,2,35,24,13
	.byte	'TIM3',0
	.word	11399
	.byte	4,2,35,28,13
	.byte	'TIM4',0
	.word	11485
	.byte	4,2,35,32,13
	.byte	'TIM5',0
	.word	11571
	.byte	4,2,35,36,13
	.byte	'TIM6',0
	.word	11657
	.byte	4,2,35,40,13
	.byte	'CAP',0
	.word	11744
	.byte	4,2,35,44,13
	.byte	'CMP',0
	.word	11866
	.byte	8,2,35,48,13
	.byte	'CMCON',0
	.word	12066
	.byte	4,2,35,56,13
	.byte	'ICR',0
	.word	12283
	.byte	4,2,35,60,13
	.byte	'ISCR',0
	.word	12447
	.byte	4,2,35,64,13
	.byte	'reserved_44',0
	.word	5248
	.byte	12,2,35,68,13
	.byte	'TIM0SV',0
	.word	12534
	.byte	4,2,35,80,13
	.byte	'CAPSV',0
	.word	12623
	.byte	4,2,35,84,13
	.byte	'reserved_58',0
	.word	12663
	.byte	144,1,2,35,88,13
	.byte	'OCS',0
	.word	12794
	.byte	4,3,35,232,1,13
	.byte	'KRSTCLR',0
	.word	12900
	.byte	4,3,35,236,1,13
	.byte	'KRST1',0
	.word	13004
	.byte	4,3,35,240,1,13
	.byte	'KRST0',0
	.word	13127
	.byte	4,3,35,244,1,13
	.byte	'ACCEN1',0
	.word	13216
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	13785
	.byte	4,3,35,252,1,0,14
	.word	13825
	.byte	3
	.word	14245
	.byte	8
	.byte	'IfxStm_get',0,3,13,162,4,19
	.word	348
	.byte	1,1,5
	.byte	'stm',0,13,162,4,39
	.word	14250
	.byte	6,0,8
	.byte	'IfxStm_getFrequency',0,3,13,179,4,20
	.word	262
	.byte	1,1,5
	.byte	'stm',0,13,179,4,49
	.word	14250
	.byte	17,6,6,0,0,8
	.byte	'IfxStm_getLower',0,3,13,190,4,19
	.word	10589
	.byte	1,1,5
	.byte	'stm',0,13,190,4,44
	.word	14250
	.byte	6,0
.L136:
	.byte	8
	.byte	'disableInterrupts',0,3,15,108,20
	.word	489
	.byte	1,1
.L138:
	.byte	17,6,0,0
.L190:
	.byte	4
	.byte	'restoreInterrupts',0,3,15,142,1,17,1,1
.L192:
	.byte	5
	.byte	'enabled',0,15,142,1,43
	.word	489
.L194:
	.byte	17,6,0,0,7
	.byte	'long long int',0,8,5,8
	.byte	'getDeadLine',0,3,15,164,2,25
	.word	14466
	.byte	1,1,5
	.byte	'timeout',0,15,164,2,50
	.word	14466
	.byte	17,6,0,0,8
	.byte	'isDeadLine',0,3,15,211,2,20
	.word	489
	.byte	1,1,5
	.byte	'deadLine',0,15,211,2,44
	.word	14466
	.byte	17,6,0,0,8
	.byte	'now',0,3,15,221,1,25
	.word	14466
	.byte	1,1,17,6,6,6,0,0,8
	.byte	'nowWithoutCriticalSection',0,3,15,240,1,25
	.word	14466
	.byte	1,1,17,6,0,0,10
	.byte	'_Ifx_SCU_ID_Bits',0,7,200,4,16,4,11
	.byte	'MODREV',0,1
	.word	489
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	489
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	1059
	.byte	16,0,2,35,2,0,12,7,167,12,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14637
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_OSCCON_Bits',0,7,188,5,16,4,11
	.byte	'reserved_0',0,1
	.word	489
	.byte	1,7,2,35,0,11
	.byte	'PLLLV',0,1
	.word	489
	.byte	1,6,2,35,0,11
	.byte	'OSCRES',0,1
	.word	489
	.byte	1,5,2,35,0,11
	.byte	'GAINSEL',0,1
	.word	489
	.byte	2,3,2,35,0,11
	.byte	'MODE',0,1
	.word	489
	.byte	2,1,2,35,0,11
	.byte	'SHBY',0,1
	.word	489
	.byte	1,0,2,35,0,11
	.byte	'PLLHV',0,1
	.word	489
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	489
	.byte	1,6,2,35,1,11
	.byte	'X1D',0,1
	.word	489
	.byte	1,5,2,35,1,11
	.byte	'X1DEN',0,1
	.word	489
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	489
	.byte	4,0,2,35,1,11
	.byte	'OSCVAL',0,1
	.word	489
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	489
	.byte	2,1,2,35,2,11
	.byte	'APREN',0,1
	.word	489
	.byte	1,0,2,35,2,11
	.byte	'CAP0EN',0,1
	.word	489
	.byte	1,7,2,35,3,11
	.byte	'CAP1EN',0,1
	.word	489
	.byte	1,6,2,35,3,11
	.byte	'CAP2EN',0,1
	.word	489
	.byte	1,5,2,35,3,11
	.byte	'CAP3EN',0,1
	.word	489
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	489
	.byte	4,0,2,35,3,0,12,7,255,12,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14759
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_PLLSTAT_Bits',0,7,230,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	489
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	489
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	489
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	489
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	489
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	489
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	489
	.byte	1,1,2,35,0,11
	.byte	'MODRUN',0,1
	.word	489
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	466
	.byte	24,0,2,35,0,0,12,7,231,13,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15182
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_PLLCON0_Bits',0,7,149,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	489
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	489
	.byte	1,6,2,35,0,11
	.byte	'MODEN',0,1
	.word	489
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	489
	.byte	1,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	489
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	489
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	489
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	1059
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	489
	.byte	7,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	489
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	489
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	489
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	489
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	489
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	489
	.byte	4,0,2,35,3,0,12,7,183,13,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15425
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_PLLCON1_Bits',0,7,169,6,16,4,11
	.byte	'K2DIV',0,1
	.word	489
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	489
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	489
	.byte	7,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	489
	.byte	1,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	489
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	1059
	.byte	9,0,2,35,2,0,12,7,191,13,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15791
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_PLLCON2_Bits',0,7,180,6,16,4,11
	.byte	'MODCFG',0,2
	.word	1059
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	1059
	.byte	16,0,2,35,2,0,12,7,199,13,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15979
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_PLLERAYSTAT_Bits',0,7,218,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	489
	.byte	1,7,2,35,0,11
	.byte	'PWDSTAT',0,1
	.word	489
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	489
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	489
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	489
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	489
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	466
	.byte	26,0,2,35,0,0,12,7,223,13,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16089
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_PLLERAYCON0_Bits',0,7,187,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	489
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	489
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	489
	.byte	2,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	489
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	489
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	489
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	1059
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	489
	.byte	5,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	489
	.byte	2,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	489
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	489
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	489
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	489
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	489
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	489
	.byte	4,0,2,35,3,0,12,7,207,13,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16293
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_PLLERAYCON1_Bits',0,7,207,6,16,4,11
	.byte	'K2DIV',0,1
	.word	489
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	489
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	489
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	489
	.byte	4,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	489
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	1059
	.byte	9,0,2,35,2,0,12,7,215,13,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16669
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_CCUCON0_Bits',0,7,97,16,4,11
	.byte	'BAUD1DIV',0,1
	.word	489
	.byte	4,4,2,35,0,11
	.byte	'BAUD2DIV',0,1
	.word	489
	.byte	4,0,2,35,0,11
	.byte	'SRIDIV',0,1
	.word	489
	.byte	4,4,2,35,1,11
	.byte	'LPDIV',0,1
	.word	489
	.byte	4,0,2,35,1,11
	.byte	'SPBDIV',0,1
	.word	489
	.byte	4,4,2,35,2,11
	.byte	'FSI2DIV',0,1
	.word	489
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	489
	.byte	2,0,2,35,2,11
	.byte	'FSIDIV',0,1
	.word	489
	.byte	2,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	489
	.byte	2,4,2,35,3,11
	.byte	'CLKSEL',0,1
	.word	489
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	489
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	489
	.byte	1,0,2,35,3,0,12,7,223,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16861
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_CCUCON1_Bits',0,7,114,16,4,11
	.byte	'CANDIV',0,1
	.word	489
	.byte	4,4,2,35,0,11
	.byte	'ERAYDIV',0,1
	.word	489
	.byte	4,0,2,35,0,11
	.byte	'STMDIV',0,1
	.word	489
	.byte	4,4,2,35,1,11
	.byte	'GTMDIV',0,1
	.word	489
	.byte	4,0,2,35,1,11
	.byte	'ETHDIV',0,1
	.word	489
	.byte	4,4,2,35,2,11
	.byte	'ASCLINFDIV',0,1
	.word	489
	.byte	4,0,2,35,2,11
	.byte	'ASCLINSDIV',0,1
	.word	489
	.byte	4,4,2,35,3,11
	.byte	'INSEL',0,1
	.word	489
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	489
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	489
	.byte	1,0,2,35,3,0,12,7,231,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17152
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_FDR_Bits',0,7,166,4,16,4,11
	.byte	'STEP',0,2
	.word	1059
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	489
	.byte	4,2,2,35,1,11
	.byte	'DM',0,1
	.word	489
	.byte	2,0,2,35,1,11
	.byte	'RESULT',0,2
	.word	1059
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	489
	.byte	5,1,2,35,3,11
	.byte	'DISCLK',0,1
	.word	489
	.byte	1,0,2,35,3,0,12,7,151,12,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17401
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_EXTCON_Bits',0,7,152,4,16,4,11
	.byte	'EN0',0,1
	.word	489
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	489
	.byte	1,6,2,35,0,11
	.byte	'SEL0',0,1
	.word	489
	.byte	4,2,2,35,0,11
	.byte	'reserved_6',0,2
	.word	1059
	.byte	10,0,2,35,0,11
	.byte	'EN1',0,1
	.word	489
	.byte	1,7,2,35,2,11
	.byte	'NSEL',0,1
	.word	489
	.byte	1,6,2,35,2,11
	.byte	'SEL1',0,1
	.word	489
	.byte	4,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	489
	.byte	2,0,2,35,2,11
	.byte	'DIV1',0,1
	.word	489
	.byte	8,0,2,35,3,0,12,7,143,12,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17578
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_CCUCON2_Bits',0,7,129,1,16,4,11
	.byte	'BBBDIV',0,1
	.word	489
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	466
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	489
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	489
	.byte	1,0,2,35,3,0,12,7,239,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17807
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_CCUCON3_Bits',0,7,138,1,16,4,11
	.byte	'PLLDIV',0,1
	.word	489
	.byte	6,2,2,35,0,11
	.byte	'PLLSEL',0,1
	.word	489
	.byte	2,0,2,35,0,11
	.byte	'PLLERAYDIV',0,1
	.word	489
	.byte	6,2,2,35,1,11
	.byte	'PLLERAYSEL',0,1
	.word	489
	.byte	2,0,2,35,1,11
	.byte	'SRIDIV',0,1
	.word	489
	.byte	6,2,2,35,2,11
	.byte	'SRISEL',0,1
	.word	489
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	489
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	489
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	489
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	489
	.byte	1,0,2,35,3,0,12,7,247,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17945
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_CCUCON4_Bits',0,7,153,1,16,4,11
	.byte	'SPBDIV',0,1
	.word	489
	.byte	6,2,2,35,0,11
	.byte	'SPBSEL',0,1
	.word	489
	.byte	2,0,2,35,0,11
	.byte	'GTMDIV',0,1
	.word	489
	.byte	6,2,2,35,1,11
	.byte	'GTMSEL',0,1
	.word	489
	.byte	2,0,2,35,1,11
	.byte	'STMDIV',0,1
	.word	489
	.byte	6,2,2,35,2,11
	.byte	'STMSEL',0,1
	.word	489
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	489
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	489
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	489
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	489
	.byte	1,0,2,35,3,0,12,7,255,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18198
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_CCUCON5_Bits',0,7,168,1,16,4,11
	.byte	'MAXDIV',0,1
	.word	489
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	466
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	489
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	489
	.byte	1,0,2,35,3,0,12,7,135,10,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18443
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_RSTSTAT_Bits',0,7,132,8,16,4,11
	.byte	'ESR0',0,1
	.word	489
	.byte	1,7,2,35,0,11
	.byte	'ESR1',0,1
	.word	489
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	489
	.byte	1,5,2,35,0,11
	.byte	'SMU',0,1
	.word	489
	.byte	1,4,2,35,0,11
	.byte	'SW',0,1
	.word	489
	.byte	1,3,2,35,0,11
	.byte	'STM0',0,1
	.word	489
	.byte	1,2,2,35,0,11
	.byte	'STM1',0,1
	.word	489
	.byte	1,1,2,35,0,11
	.byte	'STM2',0,1
	.word	489
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	489
	.byte	8,0,2,35,1,11
	.byte	'PORST',0,1
	.word	489
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	489
	.byte	1,6,2,35,2,11
	.byte	'CB0',0,1
	.word	489
	.byte	1,5,2,35,2,11
	.byte	'CB1',0,1
	.word	489
	.byte	1,4,2,35,2,11
	.byte	'CB3',0,1
	.word	489
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	489
	.byte	2,1,2,35,2,11
	.byte	'EVR13',0,1
	.word	489
	.byte	1,0,2,35,2,11
	.byte	'EVR33',0,1
	.word	489
	.byte	1,7,2,35,3,11
	.byte	'SWD',0,1
	.word	489
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	489
	.byte	2,4,2,35,3,11
	.byte	'STBYR',0,1
	.word	489
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	489
	.byte	3,0,2,35,3,0,12,7,175,14,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18581
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_RSTCON_Bits',0,7,246,7,16,4,11
	.byte	'ESR0',0,1
	.word	489
	.byte	2,6,2,35,0,11
	.byte	'ESR1',0,1
	.word	489
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	489
	.byte	2,2,2,35,0,11
	.byte	'SMU',0,1
	.word	489
	.byte	2,0,2,35,0,11
	.byte	'SW',0,1
	.word	489
	.byte	2,6,2,35,1,11
	.byte	'STM0',0,1
	.word	489
	.byte	2,4,2,35,1,11
	.byte	'STM1',0,1
	.word	489
	.byte	2,2,2,35,1,11
	.byte	'STM2',0,1
	.word	489
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1059
	.byte	16,0,2,35,2,0,12,7,159,14,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19023
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_ARSTDIS_Bits',0,7,88,16,4,11
	.byte	'STM0DIS',0,1
	.word	489
	.byte	1,7,2,35,0,11
	.byte	'STM1DIS',0,1
	.word	489
	.byte	1,6,2,35,0,11
	.byte	'STM2DIS',0,1
	.word	489
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	466
	.byte	29,0,2,35,0,0,12,7,215,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19245
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_SWRSTCON_Bits',0,7,181,8,16,4,11
	.byte	'reserved_0',0,1
	.word	489
	.byte	1,7,2,35,0,11
	.byte	'SWRSTREQ',0,1
	.word	489
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	466
	.byte	30,0,2,35,0,0,12,7,199,14,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19392
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_RSTCON2_Bits',0,7,233,7,16,4,11
	.byte	'reserved_0',0,1
	.word	489
	.byte	1,7,2,35,0,11
	.byte	'CLRC',0,1
	.word	489
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	1059
	.byte	10,4,2,35,0,11
	.byte	'CSS0',0,1
	.word	489
	.byte	1,3,2,35,1,11
	.byte	'CSS1',0,1
	.word	489
	.byte	1,2,2,35,1,11
	.byte	'CSS2',0,1
	.word	489
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	489
	.byte	1,0,2,35,1,11
	.byte	'USRINFO',0,2
	.word	1059
	.byte	16,0,2,35,2,0,12,7,167,14,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19526
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_EVRRSTCON_Bits',0,7,130,3,16,4,11
	.byte	'RST13TRIM',0,1
	.word	489
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	466
	.byte	16,8,2,35,0,11
	.byte	'RST13OFF',0,1
	.word	489
	.byte	1,7,2,35,3,11
	.byte	'BPRST13OFF',0,1
	.word	489
	.byte	1,6,2,35,3,11
	.byte	'RST33OFF',0,1
	.word	489
	.byte	1,5,2,35,3,11
	.byte	'BPRST33OFF',0,1
	.word	489
	.byte	1,4,2,35,3,11
	.byte	'RSTSWDOFF',0,1
	.word	489
	.byte	1,3,2,35,3,11
	.byte	'BPRSTSWDOFF',0,1
	.word	489
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	489
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	489
	.byte	1,0,2,35,3,0,12,7,159,11,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19745
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_ESRCFG_Bits',0,7,162,2,16,4,11
	.byte	'reserved_0',0,1
	.word	489
	.byte	7,1,2,35,0,11
	.byte	'EDCON',0,2
	.word	1059
	.byte	2,7,2,35,0,11
	.byte	'reserved_9',0,4
	.word	466
	.byte	23,0,2,35,0,0,12,7,215,10,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20025
	.byte	4,2,35,0,0,18,8
	.word	20114
	.byte	19,1,0,10
	.byte	'_Ifx_SCU_ESROCFG_Bits',0,7,170,2,16,4,11
	.byte	'ARI',0,1
	.word	489
	.byte	1,7,2,35,0,11
	.byte	'ARC',0,1
	.word	489
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	466
	.byte	30,0,2,35,0,0,12,7,223,10,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20163
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_SYSCON_Bits',0,7,189,8,16,4,11
	.byte	'CCTRIG0',0,1
	.word	489
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	489
	.byte	1,6,2,35,0,11
	.byte	'RAMINTM',0,1
	.word	489
	.byte	2,4,2,35,0,11
	.byte	'SETLUDIS',0,1
	.word	489
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	489
	.byte	3,0,2,35,0,11
	.byte	'DATM',0,1
	.word	489
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,4
	.word	466
	.byte	23,0,2,35,0,0,12,7,207,14,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20284
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_CCUCON6_Bits',0,7,177,1,16,4,11
	.byte	'CPU0DIV',0,1
	.word	489
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	466
	.byte	26,0,2,35,0,0,12,7,143,10,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20492
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_CCUCON7_Bits',0,7,184,1,16,4,11
	.byte	'CPU1DIV',0,1
	.word	489
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	466
	.byte	26,0,2,35,0,0,12,7,151,10,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20602
	.byte	4,2,35,0,0,18,20
	.word	489
	.byte	19,19,0,10
	.byte	'_Ifx_SCU_PDR_Bits',0,7,253,5,16,4,11
	.byte	'PD0',0,1
	.word	489
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	489
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	489
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	489
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	466
	.byte	24,0,2,35,0,0,12,7,167,13,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20721
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_IOCR_Bits',0,7,243,4,16,4,11
	.byte	'reserved_0',0,1
	.word	489
	.byte	4,4,2,35,0,11
	.byte	'PC0',0,1
	.word	489
	.byte	4,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	489
	.byte	4,4,2,35,1,11
	.byte	'PC1',0,1
	.word	489
	.byte	4,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1059
	.byte	16,0,2,35,2,0,12,7,191,12,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20868
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_OUT_Bits',0,7,212,5,16,4,11
	.byte	'P0',0,1
	.word	489
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	489
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	466
	.byte	30,0,2,35,0,0,12,7,135,13,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21031
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_OMR_Bits',0,7,177,5,16,4,11
	.byte	'PS0',0,1
	.word	489
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	489
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	1059
	.byte	14,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	489
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	489
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	1059
	.byte	14,0,2,35,2,0,12,7,247,12,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21146
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_IN_Bits',0,7,235,4,16,4,11
	.byte	'P0',0,1
	.word	489
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	489
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	466
	.byte	30,0,2,35,0,0,12,7,183,12,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21318
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_EVRSTAT_Bits',0,7,244,3,16,4,11
	.byte	'EVR13',0,1
	.word	489
	.byte	1,7,2,35,0,11
	.byte	'OV13',0,1
	.word	489
	.byte	1,6,2,35,0,11
	.byte	'EVR33',0,1
	.word	489
	.byte	1,5,2,35,0,11
	.byte	'OV33',0,1
	.word	489
	.byte	1,4,2,35,0,11
	.byte	'OVSWD',0,1
	.word	489
	.byte	1,3,2,35,0,11
	.byte	'UV13',0,1
	.word	489
	.byte	1,2,2,35,0,11
	.byte	'UV33',0,1
	.word	489
	.byte	1,1,2,35,0,11
	.byte	'UVSWD',0,1
	.word	489
	.byte	1,0,2,35,0,11
	.byte	'EXTPASS13',0,1
	.word	489
	.byte	1,7,2,35,1,11
	.byte	'EXTPASS33',0,1
	.word	489
	.byte	1,6,2,35,1,11
	.byte	'BGPROK',0,1
	.word	489
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	466
	.byte	21,0,2,35,0,0,12,7,247,11,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21432
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_EVRDVSTAT_Bits',0,7,208,2,16,4,11
	.byte	'DVS13TRIM',0,1
	.word	489
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	489
	.byte	8,0,2,35,1,11
	.byte	'DVS33TRIM',0,1
	.word	489
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	489
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	489
	.byte	1,0,2,35,3,0,12,7,255,10,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21716
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_EVR13CON_Bits',0,7,178,2,16,4,11
	.byte	'reserved_0',0,4
	.word	466
	.byte	28,4,2,35,0,11
	.byte	'EVR13OFF',0,1
	.word	489
	.byte	1,3,2,35,3,11
	.byte	'BPEVR13OFF',0,1
	.word	489
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	489
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	489
	.byte	1,0,2,35,3,0,12,7,231,10,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21889
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_EVR33CON_Bits',0,7,188,2,16,4,11
	.byte	'reserved_0',0,4
	.word	466
	.byte	28,4,2,35,0,11
	.byte	'EVR33OFF',0,1
	.word	489
	.byte	1,3,2,35,3,11
	.byte	'BPEVR33OFF',0,1
	.word	489
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	489
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	489
	.byte	1,0,2,35,3,0,12,7,239,10,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22061
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_STSTAT_Bits',0,7,165,8,16,4,11
	.byte	'HWCFG',0,1
	.word	489
	.byte	8,0,2,35,0,11
	.byte	'FTM',0,1
	.word	489
	.byte	7,1,2,35,1,11
	.byte	'MODE',0,1
	.word	489
	.byte	1,0,2,35,1,11
	.byte	'FCBAE',0,1
	.word	489
	.byte	1,7,2,35,2,11
	.byte	'LUDIS',0,1
	.word	489
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	489
	.byte	1,5,2,35,2,11
	.byte	'TRSTL',0,1
	.word	489
	.byte	1,4,2,35,2,11
	.byte	'SPDEN',0,1
	.word	489
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	489
	.byte	3,0,2,35,2,11
	.byte	'RAMINT',0,1
	.word	489
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	489
	.byte	7,0,2,35,3,0,12,7,191,14,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22233
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_PMSWCR0_Bits',0,7,254,6,16,4,11
	.byte	'reserved_0',0,1
	.word	489
	.byte	1,7,2,35,0,11
	.byte	'ESR1WKEN',0,1
	.word	489
	.byte	1,6,2,35,0,11
	.byte	'PINAWKEN',0,1
	.word	489
	.byte	1,5,2,35,0,11
	.byte	'PINBWKEN',0,1
	.word	489
	.byte	1,4,2,35,0,11
	.byte	'ESR0DFEN',0,1
	.word	489
	.byte	1,3,2,35,0,11
	.byte	'ESR0EDCON',0,1
	.word	489
	.byte	2,1,2,35,0,11
	.byte	'ESR1DFEN',0,1
	.word	489
	.byte	1,0,2,35,0,11
	.byte	'ESR1EDCON',0,1
	.word	489
	.byte	2,6,2,35,1,11
	.byte	'PINADFEN',0,1
	.word	489
	.byte	1,5,2,35,1,11
	.byte	'PINAEDCON',0,1
	.word	489
	.byte	2,3,2,35,1,11
	.byte	'PINBDFEN',0,1
	.word	489
	.byte	1,2,2,35,1,11
	.byte	'PINBEDCON',0,1
	.word	489
	.byte	2,0,2,35,1,11
	.byte	'SCREN',0,1
	.word	489
	.byte	1,7,2,35,2,11
	.byte	'STBYRAMSEL',0,1
	.word	489
	.byte	2,5,2,35,2,11
	.byte	'SCRCLKSEL',0,1
	.word	489
	.byte	1,4,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	489
	.byte	1,3,2,35,2,11
	.byte	'TRISTEN',0,1
	.word	489
	.byte	1,2,2,35,2,11
	.byte	'TRISTREQ',0,1
	.word	489
	.byte	1,1,2,35,2,11
	.byte	'PORSTDF',0,1
	.word	489
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	489
	.byte	1,7,2,35,3,11
	.byte	'DCDCSYNC',0,1
	.word	489
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	489
	.byte	3,3,2,35,3,11
	.byte	'ESR0TRIST',0,1
	.word	489
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	489
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	489
	.byte	1,0,2,35,3,0,12,7,247,13,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22504
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_PMSWSTAT_Bits',0,7,190,7,16,4,11
	.byte	'reserved_0',0,1
	.word	489
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKP',0,1
	.word	489
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUN',0,1
	.word	489
	.byte	1,4,2,35,0,11
	.byte	'PINAWKP',0,1
	.word	489
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUN',0,1
	.word	489
	.byte	1,2,2,35,0,11
	.byte	'PINBWKP',0,1
	.word	489
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUN',0,1
	.word	489
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	489
	.byte	1,7,2,35,1,11
	.byte	'PORSTDF',0,1
	.word	489
	.byte	1,6,2,35,1,11
	.byte	'HWCFGEVR',0,1
	.word	489
	.byte	3,3,2,35,1,11
	.byte	'STBYRAM',0,1
	.word	489
	.byte	2,1,2,35,1,11
	.byte	'TRIST',0,1
	.word	489
	.byte	1,0,2,35,1,11
	.byte	'SCRST',0,1
	.word	489
	.byte	1,7,2,35,2,11
	.byte	'SCRWKP',0,1
	.word	489
	.byte	1,6,2,35,2,11
	.byte	'SCR',0,1
	.word	489
	.byte	1,5,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	489
	.byte	1,4,2,35,2,11
	.byte	'ESR1WKEN',0,1
	.word	489
	.byte	1,3,2,35,2,11
	.byte	'PINAWKEN',0,1
	.word	489
	.byte	1,2,2,35,2,11
	.byte	'PINBWKEN',0,1
	.word	489
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	1059
	.byte	4,5,2,35,2,11
	.byte	'ESR0TRIST',0,1
	.word	489
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	489
	.byte	4,0,2,35,3,0,12,7,143,14,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23081
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_PMSWSTATCLR_Bits',0,7,217,7,16,4,11
	.byte	'reserved_0',0,1
	.word	489
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKPCLR',0,1
	.word	489
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUNCLR',0,1
	.word	489
	.byte	1,4,2,35,0,11
	.byte	'PINAWKPCLR',0,1
	.word	489
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUNCLR',0,1
	.word	489
	.byte	1,2,2,35,0,11
	.byte	'PINBWKPCLR',0,1
	.word	489
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUNCLR',0,1
	.word	489
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	489
	.byte	8,0,2,35,1,11
	.byte	'SCRSTCLR',0,1
	.word	489
	.byte	1,7,2,35,2,11
	.byte	'SCRWKPCLR',0,1
	.word	489
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	1059
	.byte	14,0,2,35,2,0,12,7,151,14,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23586
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_PMCSR_Bits',0,7,244,6,16,4,11
	.byte	'REQSLP',0,1
	.word	489
	.byte	2,6,2,35,0,11
	.byte	'SMUSLP',0,1
	.word	489
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	489
	.byte	5,0,2,35,0,11
	.byte	'PMST',0,1
	.word	489
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	466
	.byte	21,0,2,35,0,0,12,7,239,13,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23905
	.byte	4,2,35,0,0,18,8
	.word	24029
	.byte	19,1,0,10
	.byte	'_Ifx_SCU_DTSSTAT_Bits',0,7,228,1,16,4,11
	.byte	'RESULT',0,2
	.word	1059
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	489
	.byte	4,2,2,35,1,11
	.byte	'RDY',0,1
	.word	489
	.byte	1,1,2,35,1,11
	.byte	'BUSY',0,1
	.word	489
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1059
	.byte	16,0,2,35,2,0,12,7,183,10,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24078
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_DTSCON_Bits',0,7,205,1,16,4,11
	.byte	'PWD',0,1
	.word	489
	.byte	1,7,2,35,0,11
	.byte	'START',0,1
	.word	489
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	489
	.byte	2,4,2,35,0,11
	.byte	'CAL',0,4
	.word	466
	.byte	20,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	489
	.byte	7,1,2,35,3,11
	.byte	'SLCK',0,1
	.word	489
	.byte	1,0,2,35,3,0,12,7,167,10,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24242
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_PMSWCR1_Bits',0,7,156,7,16,4,11
	.byte	'SCRSTEN',0,1
	.word	489
	.byte	1,7,2,35,0,11
	.byte	'SCRSTREQ',0,1
	.word	489
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	489
	.byte	6,0,2,35,0,11
	.byte	'CPUIDLSEL',0,1
	.word	489
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	489
	.byte	1,4,2,35,1,11
	.byte	'IRADIS',0,1
	.word	489
	.byte	1,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	489
	.byte	3,0,2,35,1,11
	.byte	'SCRCFG',0,1
	.word	489
	.byte	8,0,2,35,2,11
	.byte	'CPUSEL',0,1
	.word	489
	.byte	3,5,2,35,3,11
	.byte	'STBYEVEN',0,1
	.word	489
	.byte	1,4,2,35,3,11
	.byte	'STBYEV',0,1
	.word	489
	.byte	3,1,2,35,3,11
	.byte	'LCK',0,1
	.word	489
	.byte	1,0,2,35,3,0,12,7,255,13,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24418
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_PMSWCR2_Bits',0,7,173,7,16,4,11
	.byte	'SCRINT',0,1
	.word	489
	.byte	8,0,2,35,0,11
	.byte	'BUSY',0,1
	.word	489
	.byte	1,7,2,35,1,11
	.byte	'SCRECC',0,1
	.word	489
	.byte	1,6,2,35,1,11
	.byte	'SCRWDT',0,1
	.word	489
	.byte	1,5,2,35,1,11
	.byte	'SCRRST',0,1
	.word	489
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	489
	.byte	4,0,2,35,1,11
	.byte	'TCINT',0,1
	.word	489
	.byte	8,0,2,35,2,11
	.byte	'TCINTREQ',0,1
	.word	489
	.byte	1,7,2,35,3,11
	.byte	'SMURST',0,1
	.word	489
	.byte	1,6,2,35,3,11
	.byte	'RST',0,1
	.word	489
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	489
	.byte	4,1,2,35,3,11
	.byte	'LCK',0,1
	.word	489
	.byte	1,0,2,35,3,0,12,7,135,14,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24722
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_WDTS_CON0_Bits',0,7,153,9,16,4,11
	.byte	'ENDINIT',0,4
	.word	1158
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	1158
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	1158
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	1158
	.byte	16,0,2,35,0,0,12,7,143,15,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25010
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_WDTS_CON1_Bits',0,7,162,9,16,4,11
	.byte	'CLRIRF',0,1
	.word	489
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	489
	.byte	1,6,2,35,0,11
	.byte	'IR0',0,1
	.word	489
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	489
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	489
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	489
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	489
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	489
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	489
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	489
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1059
	.byte	16,0,2,35,2,0,12,7,151,15,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25144
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_WDTS_SR_Bits',0,7,178,9,16,4,11
	.byte	'AE',0,1
	.word	489
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	489
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	489
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	489
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	489
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	489
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	489
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	489
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	489
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	489
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	1059
	.byte	16,0,2,35,2,0,12,7,159,15,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25404
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_WDTS',0,7,183,15,25,12,13
	.byte	'CON0',0
	.word	25104
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	25364
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	25593
	.byte	4,2,35,8,0,14
	.word	25633
	.byte	10
	.byte	'_Ifx_SCU_EMSR_Bits',0,7,146,2,16,4,11
	.byte	'POL',0,1
	.word	489
	.byte	1,7,2,35,0,11
	.byte	'MODE',0,1
	.word	489
	.byte	1,6,2,35,0,11
	.byte	'ENON',0,1
	.word	489
	.byte	1,5,2,35,0,11
	.byte	'PSEL',0,1
	.word	489
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	1059
	.byte	12,0,2,35,0,11
	.byte	'EMSF',0,1
	.word	489
	.byte	1,7,2,35,2,11
	.byte	'SEMSF',0,1
	.word	489
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	489
	.byte	6,0,2,35,2,11
	.byte	'EMSFM',0,1
	.word	489
	.byte	2,6,2,35,3,11
	.byte	'SEMSFM',0,1
	.word	489
	.byte	2,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	489
	.byte	4,0,2,35,3,0,12,7,207,10,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25699
	.byte	4,2,35,0,0,18,24
	.word	1785
	.byte	19,1,0,14
	.word	25964
	.byte	10
	.byte	'_Ifx_SCU_TRAPSTAT_Bits',0,7,231,8,16,4,11
	.byte	'ESR0T',0,1
	.word	489
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	489
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	489
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	489
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	466
	.byte	28,0,2,35,0,0,12,7,239,14,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25978
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_TRAPSET_Bits',0,7,221,8,16,4,11
	.byte	'ESR0T',0,1
	.word	489
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	489
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	489
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	489
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	466
	.byte	28,0,2,35,0,0,12,7,231,14,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26142
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_TRAPCLR_Bits',0,7,201,8,16,4,11
	.byte	'ESR0T',0,1
	.word	489
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	489
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	489
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	489
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	466
	.byte	28,0,2,35,0,0,12,7,215,14,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26305
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_TRAPDIS_Bits',0,7,211,8,16,4,11
	.byte	'ESR0T',0,1
	.word	489
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	489
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	489
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	489
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	466
	.byte	28,0,2,35,0,0,12,7,223,14,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26468
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_LCLCON_Bits',0,7,152,5,16,4,11
	.byte	'reserved_0',0,2
	.word	1059
	.byte	16,0,2,35,0,11
	.byte	'LS',0,1
	.word	489
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,2
	.word	1059
	.byte	14,1,2,35,2,11
	.byte	'LSEN',0,1
	.word	489
	.byte	1,0,2,35,3,0,12,7,223,12,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26631
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_LCLTEST_Bits',0,7,161,5,16,4,11
	.byte	'LCLT0',0,1
	.word	489
	.byte	1,7,2,35,0,11
	.byte	'LCLT1',0,1
	.word	489
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	466
	.byte	30,0,2,35,0,0,12,7,231,12,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26774
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_CHIPID_Bits',0,7,191,1,16,4,11
	.byte	'CHREV',0,1
	.word	489
	.byte	6,2,2,35,0,11
	.byte	'CHTEC',0,1
	.word	489
	.byte	2,0,2,35,0,11
	.byte	'CHID',0,1
	.word	489
	.byte	8,0,2,35,1,11
	.byte	'EEA',0,1
	.word	489
	.byte	1,7,2,35,2,11
	.byte	'UCODE',0,1
	.word	489
	.byte	7,0,2,35,2,11
	.byte	'FSIZE',0,1
	.word	489
	.byte	4,4,2,35,3,11
	.byte	'SP',0,1
	.word	489
	.byte	2,2,2,35,3,11
	.byte	'SEC',0,1
	.word	489
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	489
	.byte	1,0,2,35,3,0,12,7,159,10,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26899
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_MANID_Bits',0,7,169,5,16,4,11
	.byte	'DEPT',0,1
	.word	489
	.byte	5,3,2,35,0,11
	.byte	'MANUF',0,2
	.word	1059
	.byte	11,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	1059
	.byte	16,0,2,35,2,0,12,7,239,12,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27118
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_SAFECON_Bits',0,7,158,8,16,4,11
	.byte	'HBT',0,1
	.word	489
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	466
	.byte	31,0,2,35,0,0,12,7,183,14,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27241
	.byte	4,2,35,0,0,18,16
	.word	489
	.byte	19,15,0,10
	.byte	'_Ifx_SCU_LBISTCTRL0_Bits',0,7,253,4,16,4,11
	.byte	'LBISTREQ',0,1
	.word	489
	.byte	1,7,2,35,0,11
	.byte	'LBISTREQP',0,1
	.word	489
	.byte	1,6,2,35,0,11
	.byte	'PATTERNS',0,2
	.word	1059
	.byte	14,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	1059
	.byte	16,0,2,35,2,0,12,7,199,12,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27356
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_LBISTCTRL1_Bits',0,7,134,5,16,4,11
	.byte	'SEED',0,4
	.word	466
	.byte	23,9,2,35,0,11
	.byte	'reserved_23',0,1
	.word	489
	.byte	1,0,2,35,2,11
	.byte	'SPLITSH',0,1
	.word	489
	.byte	3,5,2,35,3,11
	.byte	'BODY',0,1
	.word	489
	.byte	1,4,2,35,3,11
	.byte	'LBISTFREQU',0,1
	.word	489
	.byte	4,0,2,35,3,0,12,7,207,12,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27512
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_LBISTCTRL2_Bits',0,7,144,5,16,4,11
	.byte	'SIGNATURE',0,4
	.word	466
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	489
	.byte	7,1,2,35,3,11
	.byte	'LBISTDONE',0,1
	.word	489
	.byte	1,0,2,35,3,0,12,7,215,12,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27680
	.byte	4,2,35,0,0,18,28
	.word	489
	.byte	19,27,0,10
	.byte	'_Ifx_SCU_PDISC_Bits',0,7,245,5,16,4,11
	.byte	'PDIS0',0,1
	.word	489
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	489
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	466
	.byte	30,0,2,35,0,0,12,7,159,13,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27826
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_EVRTRIM_Bits',0,7,133,4,16,4,11
	.byte	'EVR13TRIM',0,1
	.word	489
	.byte	8,0,2,35,0,11
	.byte	'SDVOUTSEL',0,1
	.word	489
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1059
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	489
	.byte	1,0,2,35,3,0,12,7,255,11,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27949
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_EVRADCSTAT_Bits',0,7,198,2,16,4,11
	.byte	'ADC13V',0,1
	.word	489
	.byte	8,0,2,35,0,11
	.byte	'ADC33V',0,1
	.word	489
	.byte	8,0,2,35,1,11
	.byte	'ADCSWDV',0,1
	.word	489
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	489
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	489
	.byte	1,0,2,35,3,0,12,7,247,10,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28098
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_EVRUVMON_Bits',0,7,142,4,16,4,11
	.byte	'EVR13UVVAL',0,1
	.word	489
	.byte	8,0,2,35,0,11
	.byte	'EVR33UVVAL',0,1
	.word	489
	.byte	8,0,2,35,1,11
	.byte	'SWDUVVAL',0,1
	.word	489
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	489
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	489
	.byte	1,0,2,35,3,0,12,7,135,12,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28263
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_EVROVMON_Bits',0,7,248,2,16,4,11
	.byte	'EVR13OVVAL',0,1
	.word	489
	.byte	8,0,2,35,0,11
	.byte	'EVR33OVVAL',0,1
	.word	489
	.byte	8,0,2,35,1,11
	.byte	'SWDOVVAL',0,1
	.word	489
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	489
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	489
	.byte	1,0,2,35,3,0,12,7,151,11,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28435
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_EVRMONCTRL_Bits',0,7,218,2,16,4,11
	.byte	'EVR13OVMOD',0,1
	.word	489
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	489
	.byte	2,4,2,35,0,11
	.byte	'EVR13UVMOD',0,1
	.word	489
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	489
	.byte	2,0,2,35,0,11
	.byte	'EVR33OVMOD',0,1
	.word	489
	.byte	2,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	489
	.byte	2,4,2,35,1,11
	.byte	'EVR33UVMOD',0,1
	.word	489
	.byte	2,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	489
	.byte	2,0,2,35,1,11
	.byte	'SWDOVMOD',0,1
	.word	489
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	489
	.byte	2,4,2,35,2,11
	.byte	'SWDUVMOD',0,1
	.word	489
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,2
	.word	1059
	.byte	10,0,2,35,2,0,12,7,135,11,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28607
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_EVRSDCTRL1_Bits',0,7,203,3,16,4,11
	.byte	'SDFREQSPRD',0,2
	.word	1059
	.byte	16,0,2,35,0,11
	.byte	'SDFREQ',0,1
	.word	489
	.byte	8,0,2,35,2,11
	.byte	'SDSTEP',0,1
	.word	489
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	489
	.byte	2,2,2,35,3,11
	.byte	'SDSAMPLE',0,1
	.word	489
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	489
	.byte	1,0,2,35,3,0,12,7,215,11,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28943
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_EVRSDCTRL2_Bits',0,7,214,3,16,4,11
	.byte	'DRVP',0,1
	.word	489
	.byte	8,0,2,35,0,11
	.byte	'SDMINMAXDC',0,1
	.word	489
	.byte	8,0,2,35,1,11
	.byte	'DRVN',0,1
	.word	489
	.byte	8,0,2,35,2,11
	.byte	'SDLUT',0,1
	.word	489
	.byte	6,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	489
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	489
	.byte	1,0,2,35,3,0,12,7,223,11,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29131
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_EVRSDCTRL3_Bits',0,7,225,3,16,4,11
	.byte	'SDPWMPRE',0,1
	.word	489
	.byte	8,0,2,35,0,11
	.byte	'SDPID',0,1
	.word	489
	.byte	8,0,2,35,1,11
	.byte	'SDVOKLVL',0,1
	.word	489
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	489
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	489
	.byte	1,0,2,35,3,0,12,7,231,11,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29312
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_EVRSDCTRL4_Bits',0,7,235,3,16,4,11
	.byte	'reserved_0',0,1
	.word	489
	.byte	8,0,2,35,0,11
	.byte	'SYNCDIV',0,1
	.word	489
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	466
	.byte	20,1,2,35,0,11
	.byte	'LCK',0,1
	.word	489
	.byte	1,0,2,35,3,0,12,7,239,11,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29479
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_EVRSDCOEFF1_Bits',0,7,145,3,16,4,11
	.byte	'SD5P',0,1
	.word	489
	.byte	8,0,2,35,0,11
	.byte	'SD5I',0,1
	.word	489
	.byte	8,0,2,35,1,11
	.byte	'SD5D',0,1
	.word	489
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	489
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	489
	.byte	1,0,2,35,3,0,12,7,167,11,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29630
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_EVRSDCOEFF2_Bits',0,7,155,3,16,4,11
	.byte	'SD33P',0,1
	.word	489
	.byte	8,0,2,35,0,11
	.byte	'SD33I',0,1
	.word	489
	.byte	8,0,2,35,1,11
	.byte	'SD33D',0,1
	.word	489
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	489
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	489
	.byte	1,0,2,35,3,0,12,7,175,11,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29789
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_EVRSDCOEFF3_Bits',0,7,165,3,16,4,11
	.byte	'CT5REG0',0,1
	.word	489
	.byte	8,0,2,35,0,11
	.byte	'CT5REG1',0,1
	.word	489
	.byte	8,0,2,35,1,11
	.byte	'CT5REG2',0,1
	.word	489
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	489
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	489
	.byte	1,0,2,35,3,0,12,7,183,11,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29951
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_EVRSDCOEFF4_Bits',0,7,175,3,16,4,11
	.byte	'CT5REG3',0,1
	.word	489
	.byte	8,0,2,35,0,11
	.byte	'CT5REG4',0,1
	.word	489
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1059
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	489
	.byte	1,0,2,35,3,0,12,7,191,11,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30119
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_EVRSDCOEFF5_Bits',0,7,184,3,16,4,11
	.byte	'CT33REG0',0,1
	.word	489
	.byte	8,0,2,35,0,11
	.byte	'CT33REG1',0,1
	.word	489
	.byte	8,0,2,35,1,11
	.byte	'CT33REG2',0,1
	.word	489
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	489
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	489
	.byte	1,0,2,35,3,0,12,7,199,11,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30268
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_EVRSDCOEFF6_Bits',0,7,194,3,16,4,11
	.byte	'CT33REG3',0,1
	.word	489
	.byte	8,0,2,35,0,11
	.byte	'CT33REG4',0,1
	.word	489
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1059
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	489
	.byte	1,0,2,35,3,0,12,7,207,11,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30439
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_EVROSCCTRL_Bits',0,7,235,2,16,4,11
	.byte	'OSCTRIM',0,2
	.word	1059
	.byte	10,6,2,35,0,11
	.byte	'OSCPTAT',0,1
	.word	489
	.byte	6,0,2,35,1,11
	.byte	'OSCANASEL',0,1
	.word	489
	.byte	4,4,2,35,2,11
	.byte	'HPBGTRIM',0,2
	.word	1059
	.byte	7,5,2,35,2,11
	.byte	'HPBGCLKEN',0,1
	.word	489
	.byte	1,4,2,35,3,11
	.byte	'OSC3V3',0,1
	.word	489
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	489
	.byte	2,1,2,35,3,11
	.byte	'LCK',0,1
	.word	489
	.byte	1,0,2,35,3,0,12,7,143,11,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30590
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_OVCENABLE_Bits',0,7,236,5,16,4,11
	.byte	'OVEN0',0,1
	.word	489
	.byte	1,7,2,35,0,11
	.byte	'OVEN1',0,1
	.word	489
	.byte	1,6,2,35,0,11
	.byte	'OVEN2',0,1
	.word	489
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	466
	.byte	29,0,2,35,0,0,12,7,151,13,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30818
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_OVCCON_Bits',0,7,220,5,16,4,11
	.byte	'CSEL0',0,1
	.word	489
	.byte	1,7,2,35,0,11
	.byte	'CSEL1',0,1
	.word	489
	.byte	1,6,2,35,0,11
	.byte	'CSEL2',0,1
	.word	489
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,2
	.word	1059
	.byte	13,0,2,35,0,11
	.byte	'OVSTRT',0,1
	.word	489
	.byte	1,7,2,35,2,11
	.byte	'OVSTP',0,1
	.word	489
	.byte	1,6,2,35,2,11
	.byte	'DCINVAL',0,1
	.word	489
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	489
	.byte	5,0,2,35,2,11
	.byte	'OVCONF',0,1
	.word	489
	.byte	1,7,2,35,3,11
	.byte	'POVCONF',0,1
	.word	489
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	489
	.byte	6,0,2,35,3,0,12,7,143,13,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30962
	.byte	4,2,35,0,0,18,40
	.word	489
	.byte	19,39,0,10
	.byte	'_Ifx_SCU_EICR_Bits',0,7,238,1,16,4,11
	.byte	'reserved_0',0,1
	.word	489
	.byte	4,4,2,35,0,11
	.byte	'EXIS0',0,1
	.word	489
	.byte	3,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	489
	.byte	1,0,2,35,0,11
	.byte	'FEN0',0,1
	.word	489
	.byte	1,7,2,35,1,11
	.byte	'REN0',0,1
	.word	489
	.byte	1,6,2,35,1,11
	.byte	'LDEN0',0,1
	.word	489
	.byte	1,5,2,35,1,11
	.byte	'EIEN0',0,1
	.word	489
	.byte	1,4,2,35,1,11
	.byte	'INP0',0,1
	.word	489
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	466
	.byte	5,12,2,35,0,11
	.byte	'EXIS1',0,1
	.word	489
	.byte	3,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	489
	.byte	1,0,2,35,2,11
	.byte	'FEN1',0,1
	.word	489
	.byte	1,7,2,35,3,11
	.byte	'REN1',0,1
	.word	489
	.byte	1,6,2,35,3,11
	.byte	'LDEN1',0,1
	.word	489
	.byte	1,5,2,35,3,11
	.byte	'EIEN1',0,1
	.word	489
	.byte	1,4,2,35,3,11
	.byte	'INP1',0,1
	.word	489
	.byte	3,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	489
	.byte	1,0,2,35,3,0,12,7,191,10,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31249
	.byte	4,2,35,0,0,18,16
	.word	31586
	.byte	19,3,0,10
	.byte	'_Ifx_SCU_EIFR_Bits',0,7,132,2,16,4,11
	.byte	'INTF0',0,1
	.word	489
	.byte	1,7,2,35,0,11
	.byte	'INTF1',0,1
	.word	489
	.byte	1,6,2,35,0,11
	.byte	'INTF2',0,1
	.word	489
	.byte	1,5,2,35,0,11
	.byte	'INTF3',0,1
	.word	489
	.byte	1,4,2,35,0,11
	.byte	'INTF4',0,1
	.word	489
	.byte	1,3,2,35,0,11
	.byte	'INTF5',0,1
	.word	489
	.byte	1,2,2,35,0,11
	.byte	'INTF6',0,1
	.word	489
	.byte	1,1,2,35,0,11
	.byte	'INTF7',0,1
	.word	489
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	466
	.byte	24,0,2,35,0,0,12,7,199,10,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31635
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_FMR_Bits',0,7,177,4,16,4,11
	.byte	'FS0',0,1
	.word	489
	.byte	1,7,2,35,0,11
	.byte	'FS1',0,1
	.word	489
	.byte	1,6,2,35,0,11
	.byte	'FS2',0,1
	.word	489
	.byte	1,5,2,35,0,11
	.byte	'FS3',0,1
	.word	489
	.byte	1,4,2,35,0,11
	.byte	'FS4',0,1
	.word	489
	.byte	1,3,2,35,0,11
	.byte	'FS5',0,1
	.word	489
	.byte	1,2,2,35,0,11
	.byte	'FS6',0,1
	.word	489
	.byte	1,1,2,35,0,11
	.byte	'FS7',0,1
	.word	489
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	489
	.byte	8,0,2,35,1,11
	.byte	'FC0',0,1
	.word	489
	.byte	1,7,2,35,2,11
	.byte	'FC1',0,1
	.word	489
	.byte	1,6,2,35,2,11
	.byte	'FC2',0,1
	.word	489
	.byte	1,5,2,35,2,11
	.byte	'FC3',0,1
	.word	489
	.byte	1,4,2,35,2,11
	.byte	'FC4',0,1
	.word	489
	.byte	1,3,2,35,2,11
	.byte	'FC5',0,1
	.word	489
	.byte	1,2,2,35,2,11
	.byte	'FC6',0,1
	.word	489
	.byte	1,1,2,35,2,11
	.byte	'FC7',0,1
	.word	489
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	489
	.byte	8,0,2,35,3,0,12,7,159,12,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31859
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_PDRR_Bits',0,7,135,6,16,4,11
	.byte	'PDR0',0,1
	.word	489
	.byte	1,7,2,35,0,11
	.byte	'PDR1',0,1
	.word	489
	.byte	1,6,2,35,0,11
	.byte	'PDR2',0,1
	.word	489
	.byte	1,5,2,35,0,11
	.byte	'PDR3',0,1
	.word	489
	.byte	1,4,2,35,0,11
	.byte	'PDR4',0,1
	.word	489
	.byte	1,3,2,35,0,11
	.byte	'PDR5',0,1
	.word	489
	.byte	1,2,2,35,0,11
	.byte	'PDR6',0,1
	.word	489
	.byte	1,1,2,35,0,11
	.byte	'PDR7',0,1
	.word	489
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	466
	.byte	24,0,2,35,0,0,12,7,175,13,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32209
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_IGCR_Bits',0,7,208,4,16,4,11
	.byte	'IPEN00',0,1
	.word	489
	.byte	1,7,2,35,0,11
	.byte	'IPEN01',0,1
	.word	489
	.byte	1,6,2,35,0,11
	.byte	'IPEN02',0,1
	.word	489
	.byte	1,5,2,35,0,11
	.byte	'IPEN03',0,1
	.word	489
	.byte	1,4,2,35,0,11
	.byte	'IPEN04',0,1
	.word	489
	.byte	1,3,2,35,0,11
	.byte	'IPEN05',0,1
	.word	489
	.byte	1,2,2,35,0,11
	.byte	'IPEN06',0,1
	.word	489
	.byte	1,1,2,35,0,11
	.byte	'IPEN07',0,1
	.word	489
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	489
	.byte	5,3,2,35,1,11
	.byte	'GEEN0',0,1
	.word	489
	.byte	1,2,2,35,1,11
	.byte	'IGP0',0,1
	.word	489
	.byte	2,0,2,35,1,11
	.byte	'IPEN10',0,1
	.word	489
	.byte	1,7,2,35,2,11
	.byte	'IPEN11',0,1
	.word	489
	.byte	1,6,2,35,2,11
	.byte	'IPEN12',0,1
	.word	489
	.byte	1,5,2,35,2,11
	.byte	'IPEN13',0,1
	.word	489
	.byte	1,4,2,35,2,11
	.byte	'IPEN14',0,1
	.word	489
	.byte	1,3,2,35,2,11
	.byte	'IPEN15',0,1
	.word	489
	.byte	1,2,2,35,2,11
	.byte	'IPEN16',0,1
	.word	489
	.byte	1,1,2,35,2,11
	.byte	'IPEN17',0,1
	.word	489
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	489
	.byte	5,3,2,35,3,11
	.byte	'GEEN1',0,1
	.word	489
	.byte	1,2,2,35,3,11
	.byte	'IGP1',0,1
	.word	489
	.byte	2,0,2,35,3,0,12,7,175,12,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32425
	.byte	4,2,35,0,0,18,16
	.word	32850
	.byte	19,3,0,10
	.byte	'_Ifx_SCU_DTSLIM_Bits',0,7,216,1,16,4,11
	.byte	'LOWER',0,2
	.word	1059
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	489
	.byte	5,1,2,35,1,11
	.byte	'LLU',0,1
	.word	489
	.byte	1,0,2,35,1,11
	.byte	'UPPER',0,2
	.word	1059
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	489
	.byte	4,2,2,35,3,11
	.byte	'SLCK',0,1
	.word	489
	.byte	1,1,2,35,3,11
	.byte	'UOF',0,1
	.word	489
	.byte	1,0,2,35,3,0,12,7,175,10,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32899
	.byte	4,2,35,0,0,18,180,3
	.word	489
	.byte	19,179,3,0,10
	.byte	'_Ifx_SCU_ACCEN1_Bits',0,7,82,16,4,11
	.byte	'reserved_0',0,4
	.word	466
	.byte	32,0,2,35,0,0,12,7,207,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33104
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_ACCEN0_Bits',0,7,45,16,4,11
	.byte	'EN0',0,1
	.word	489
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	489
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	489
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	489
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	489
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	489
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	489
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	489
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	489
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	489
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	489
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	489
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	489
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	489
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	489
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	489
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	489
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	489
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	489
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	489
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	489
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	489
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	489
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	489
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	489
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	489
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	489
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	489
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	489
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	489
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	489
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	489
	.byte	1,0,2,35,3,0,12,7,199,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33193
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU',0,7,201,15,25,128,8,13
	.byte	'reserved_0',0
	.word	4908
	.byte	8,2,35,0,13
	.byte	'ID',0
	.word	14719
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	3089
	.byte	4,2,35,12,13
	.byte	'OSCCON',0
	.word	15142
	.byte	4,2,35,16,13
	.byte	'PLLSTAT',0
	.word	15385
	.byte	4,2,35,20,13
	.byte	'PLLCON0',0
	.word	15751
	.byte	4,2,35,24,13
	.byte	'PLLCON1',0
	.word	15939
	.byte	4,2,35,28,13
	.byte	'PLLCON2',0
	.word	16049
	.byte	4,2,35,32,13
	.byte	'PLLERAYSTAT',0
	.word	16253
	.byte	4,2,35,36,13
	.byte	'PLLERAYCON0',0
	.word	16629
	.byte	4,2,35,40,13
	.byte	'PLLERAYCON1',0
	.word	16821
	.byte	4,2,35,44,13
	.byte	'CCUCON0',0
	.word	17112
	.byte	4,2,35,48,13
	.byte	'CCUCON1',0
	.word	17361
	.byte	4,2,35,52,13
	.byte	'FDR',0
	.word	17538
	.byte	4,2,35,56,13
	.byte	'EXTCON',0
	.word	17767
	.byte	4,2,35,60,13
	.byte	'CCUCON2',0
	.word	17905
	.byte	4,2,35,64,13
	.byte	'CCUCON3',0
	.word	18158
	.byte	4,2,35,68,13
	.byte	'CCUCON4',0
	.word	18403
	.byte	4,2,35,72,13
	.byte	'CCUCON5',0
	.word	18541
	.byte	4,2,35,76,13
	.byte	'RSTSTAT',0
	.word	18983
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	3089
	.byte	4,2,35,84,13
	.byte	'RSTCON',0
	.word	19205
	.byte	4,2,35,88,13
	.byte	'ARSTDIS',0
	.word	19352
	.byte	4,2,35,92,13
	.byte	'SWRSTCON',0
	.word	19486
	.byte	4,2,35,96,13
	.byte	'RSTCON2',0
	.word	19705
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	3089
	.byte	4,2,35,104,13
	.byte	'EVRRSTCON',0
	.word	19985
	.byte	4,2,35,108,13
	.byte	'ESRCFG',0
	.word	20154
	.byte	8,2,35,112,13
	.byte	'ESROCFG',0
	.word	20244
	.byte	4,2,35,120,13
	.byte	'SYSCON',0
	.word	20452
	.byte	4,2,35,124,13
	.byte	'CCUCON6',0
	.word	20562
	.byte	4,3,35,128,1,13
	.byte	'CCUCON7',0
	.word	20672
	.byte	4,3,35,132,1,13
	.byte	'reserved_88',0
	.word	20712
	.byte	20,3,35,136,1,13
	.byte	'PDR',0
	.word	20828
	.byte	4,3,35,156,1,13
	.byte	'IOCR',0
	.word	20991
	.byte	4,3,35,160,1,13
	.byte	'OUT',0
	.word	21106
	.byte	4,3,35,164,1,13
	.byte	'OMR',0
	.word	21278
	.byte	4,3,35,168,1,13
	.byte	'IN',0
	.word	21392
	.byte	4,3,35,172,1,13
	.byte	'EVRSTAT',0
	.word	21676
	.byte	4,3,35,176,1,13
	.byte	'EVRDVSTAT',0
	.word	21849
	.byte	4,3,35,180,1,13
	.byte	'EVR13CON',0
	.word	22021
	.byte	4,3,35,184,1,13
	.byte	'EVR33CON',0
	.word	22193
	.byte	4,3,35,188,1,13
	.byte	'STSTAT',0
	.word	22464
	.byte	4,3,35,192,1,13
	.byte	'reserved_C4',0
	.word	3089
	.byte	4,3,35,196,1,13
	.byte	'PMSWCR0',0
	.word	23041
	.byte	4,3,35,200,1,13
	.byte	'PMSWSTAT',0
	.word	23546
	.byte	4,3,35,204,1,13
	.byte	'PMSWSTATCLR',0
	.word	23865
	.byte	4,3,35,208,1,13
	.byte	'PMCSR',0
	.word	24069
	.byte	8,3,35,212,1,13
	.byte	'reserved_DC',0
	.word	3089
	.byte	4,3,35,220,1,13
	.byte	'DTSSTAT',0
	.word	24202
	.byte	4,3,35,224,1,13
	.byte	'DTSCON',0
	.word	24378
	.byte	4,3,35,228,1,13
	.byte	'PMSWCR1',0
	.word	24682
	.byte	4,3,35,232,1,13
	.byte	'PMSWCR2',0
	.word	24970
	.byte	4,3,35,236,1,13
	.byte	'WDTS',0
	.word	25694
	.byte	12,3,35,240,1,13
	.byte	'EMSR',0
	.word	25924
	.byte	4,3,35,252,1,13
	.byte	'WDTCPU',0
	.word	25973
	.byte	24,3,35,128,2,13
	.byte	'reserved_118',0
	.word	5248
	.byte	12,3,35,152,2,13
	.byte	'TRAPSTAT',0
	.word	26102
	.byte	4,3,35,164,2,13
	.byte	'TRAPSET',0
	.word	26265
	.byte	4,3,35,168,2,13
	.byte	'TRAPCLR',0
	.word	26428
	.byte	4,3,35,172,2,13
	.byte	'TRAPDIS',0
	.word	26591
	.byte	4,3,35,176,2,13
	.byte	'reserved_134',0
	.word	3089
	.byte	4,3,35,180,2,13
	.byte	'LCLCON1',0
	.word	26734
	.byte	4,3,35,184,2,13
	.byte	'LCLTEST',0
	.word	26859
	.byte	4,3,35,188,2,13
	.byte	'CHIPID',0
	.word	27078
	.byte	4,3,35,192,2,13
	.byte	'MANID',0
	.word	27201
	.byte	4,3,35,196,2,13
	.byte	'reserved_148',0
	.word	4908
	.byte	8,3,35,200,2,13
	.byte	'SAFECON',0
	.word	27307
	.byte	4,3,35,208,2,13
	.byte	'reserved_154',0
	.word	27347
	.byte	16,3,35,212,2,13
	.byte	'LBISTCTRL0',0
	.word	27472
	.byte	4,3,35,228,2,13
	.byte	'LBISTCTRL1',0
	.word	27640
	.byte	4,3,35,232,2,13
	.byte	'LBISTCTRL2',0
	.word	27777
	.byte	4,3,35,236,2,13
	.byte	'reserved_170',0
	.word	27817
	.byte	28,3,35,240,2,13
	.byte	'PDISC',0
	.word	27909
	.byte	4,3,35,140,3,13
	.byte	'reserved_190',0
	.word	4908
	.byte	8,3,35,144,3,13
	.byte	'EVRTRIM',0
	.word	28058
	.byte	4,3,35,152,3,13
	.byte	'EVRADCSTAT',0
	.word	28223
	.byte	4,3,35,156,3,13
	.byte	'EVRUVMON',0
	.word	28395
	.byte	4,3,35,160,3,13
	.byte	'EVROVMON',0
	.word	28567
	.byte	4,3,35,164,3,13
	.byte	'EVRMONCTRL',0
	.word	28903
	.byte	4,3,35,168,3,13
	.byte	'reserved_1AC',0
	.word	3089
	.byte	4,3,35,172,3,13
	.byte	'EVRSDCTRL1',0
	.word	29091
	.byte	4,3,35,176,3,13
	.byte	'EVRSDCTRL2',0
	.word	29272
	.byte	4,3,35,180,3,13
	.byte	'EVRSDCTRL3',0
	.word	29439
	.byte	4,3,35,184,3,13
	.byte	'EVRSDCTRL4',0
	.word	29590
	.byte	4,3,35,188,3,13
	.byte	'EVRSDCOEFF1',0
	.word	29749
	.byte	4,3,35,192,3,13
	.byte	'EVRSDCOEFF2',0
	.word	29911
	.byte	4,3,35,196,3,13
	.byte	'EVRSDCOEFF3',0
	.word	30079
	.byte	4,3,35,200,3,13
	.byte	'EVRSDCOEFF4',0
	.word	30228
	.byte	4,3,35,204,3,13
	.byte	'EVRSDCOEFF5',0
	.word	30399
	.byte	4,3,35,208,3,13
	.byte	'EVRSDCOEFF6',0
	.word	30550
	.byte	4,3,35,212,3,13
	.byte	'EVROSCCTRL',0
	.word	30778
	.byte	4,3,35,216,3,13
	.byte	'reserved_1DC',0
	.word	3089
	.byte	4,3,35,220,3,13
	.byte	'OVCENABLE',0
	.word	30922
	.byte	4,3,35,224,3,13
	.byte	'OVCCON',0
	.word	31200
	.byte	4,3,35,228,3,13
	.byte	'reserved_1E8',0
	.word	31240
	.byte	40,3,35,232,3,13
	.byte	'EICR',0
	.word	31626
	.byte	16,3,35,144,4,13
	.byte	'EIFR',0
	.word	31819
	.byte	4,3,35,160,4,13
	.byte	'FMR',0
	.word	32169
	.byte	4,3,35,164,4,13
	.byte	'PDRR',0
	.word	32385
	.byte	4,3,35,168,4,13
	.byte	'IGCR',0
	.word	32890
	.byte	16,3,35,172,4,13
	.byte	'reserved_23C',0
	.word	3089
	.byte	4,3,35,188,4,13
	.byte	'DTSLIM',0
	.word	33053
	.byte	4,3,35,192,4,13
	.byte	'reserved_244',0
	.word	33093
	.byte	180,3,3,35,196,4,13
	.byte	'ACCEN1',0
	.word	33153
	.byte	4,3,35,248,7,13
	.byte	'ACCEN0',0
	.word	33722
	.byte	4,3,35,252,7,0,14
	.word	33762
	.byte	3
	.word	35752
	.byte	20,8,190,1,9,8,13
	.byte	'port',0
	.word	9836
	.byte	4,2,35,0,13
	.byte	'pinIndex',0
	.word	489
	.byte	1,2,35,4,0,15,18,130,1,9,1,16
	.byte	'Ifx_RxSel_a',0,0,16
	.byte	'Ifx_RxSel_b',0,1,16
	.byte	'Ifx_RxSel_c',0,2,16
	.byte	'Ifx_RxSel_d',0,3,16
	.byte	'Ifx_RxSel_e',0,4,16
	.byte	'Ifx_RxSel_f',0,5,16
	.byte	'Ifx_RxSel_g',0,6,16
	.byte	'Ifx_RxSel_h',0,7,0,20,17,74,15,20,13
	.byte	'module',0
	.word	35757
	.byte	4,2,35,0,13
	.byte	'channelId',0
	.word	489
	.byte	1,2,35,4,13
	.byte	'pin',0
	.word	35762
	.byte	8,2,35,8,13
	.byte	'select',0
	.word	35801
	.byte	1,2,35,16,0,21
	.word	35920
.L134:
	.byte	3
	.word	35990
.L146:
	.byte	4
	.byte	'IfxScuEru_initReqPin',0,3,16,222,2,17,1,1
.L149:
	.byte	5
	.byte	'req',0,16,222,2,53
	.word	35995
.L151:
	.byte	5
	.byte	'inputMode',0,16,222,2,76
	.word	9841
.L153:
	.byte	17,6,0,0,14
	.word	482
	.byte	22
	.byte	'__mfcr',0
	.word	36065
	.byte	1,1,1,1,23
	.word	482
	.byte	0,24
	.byte	'__nop',0,1,1,1,1,24
	.byte	'__disable',0,1,1,1,1,24
	.byte	'__enable',0,1,1,1,1,25
	.word	210
	.byte	26
	.word	236
	.byte	6,0,25
	.word	271
	.byte	26
	.word	303
	.byte	6,0,25
	.word	316
	.byte	6,0,25
	.word	385
	.byte	26
	.word	404
	.byte	6,0,25
	.word	420
	.byte	26
	.word	435
	.byte	26
	.word	449
	.byte	6,0,25
	.word	845
	.byte	26
	.word	873
	.byte	6,0,25
	.word	888
	.byte	26
	.word	910
	.byte	6,0,25
	.word	925
	.byte	26
	.word	948
	.byte	6,0,25
	.word	963
	.byte	26
	.word	985
	.byte	6,0,25
	.word	1081
	.byte	26
	.word	1101
	.byte	26
	.word	1114
	.byte	26
	.word	1136
	.byte	17,27
	.word	845
	.byte	26
	.word	873
	.byte	28
	.word	886
	.byte	0,6,0,0,25
	.word	1858
	.byte	26
	.word	1898
	.byte	26
	.word	1916
	.byte	6,0,25
	.word	1936
	.byte	26
	.word	1974
	.byte	26
	.word	1992
	.byte	6,0,25
	.word	2012
	.byte	26
	.word	2063
	.byte	6,0,25
	.word	9966
	.byte	26
	.word	9998
	.byte	26
	.word	10012
	.byte	26
	.word	10030
	.byte	6,0,25
	.word	10149
	.byte	26
	.word	10177
	.byte	26
	.word	10191
	.byte	26
	.word	10209
	.byte	6,0,15,8,95,9,1,16
	.byte	'IfxPort_Mode_inputNoPullDevice',0,0,16
	.byte	'IfxPort_Mode_inputPullDown',0,8,16
	.byte	'IfxPort_Mode_inputPullUp',0,16,16
	.byte	'IfxPort_Mode_outputPushPullGeneral',0,128,1,16
	.byte	'IfxPort_Mode_outputPushPullAlt1',0,136,1,16
	.byte	'IfxPort_Mode_outputPushPullAlt2',0,144,1,16
	.byte	'IfxPort_Mode_outputPushPullAlt3',0,152,1,16
	.byte	'IfxPort_Mode_outputPushPullAlt4',0,160,1,16
	.byte	'IfxPort_Mode_outputPushPullAlt5',0,168,1,16
	.byte	'IfxPort_Mode_outputPushPullAlt6',0,176,1,16
	.byte	'IfxPort_Mode_outputPushPullAlt7',0,184,1,16
	.byte	'IfxPort_Mode_outputOpenDrainGeneral',0,192,1,16
	.byte	'IfxPort_Mode_outputOpenDrainAlt1',0,200,1,16
	.byte	'IfxPort_Mode_outputOpenDrainAlt2',0,208,1,16
	.byte	'IfxPort_Mode_outputOpenDrainAlt3',0,216,1,16
	.byte	'IfxPort_Mode_outputOpenDrainAlt4',0,224,1,16
	.byte	'IfxPort_Mode_outputOpenDrainAlt5',0,232,1,16
	.byte	'IfxPort_Mode_outputOpenDrainAlt6',0,240,1,16
	.byte	'IfxPort_Mode_outputOpenDrainAlt7',0,248,1,0,29
	.byte	'IfxPort_setPinMode',0,8,247,2,17,1,1,1,1,5
	.byte	'port',0,8,247,2,43
	.word	9836
	.byte	5
	.byte	'pinIndex',0,8,247,2,55
	.word	489
	.byte	5
	.byte	'mode',0,8,247,2,78
	.word	36370
	.byte	0,25
	.word	10227
	.byte	6,0,25
	.word	10346
	.byte	6,0,25
	.word	10380
	.byte	6,0,25
	.word	10422
	.byte	17,27
	.word	10380
	.byte	28
	.word	10420
	.byte	0,6,0,0,25
	.word	10463
	.byte	6,0,25
	.word	10497
	.byte	6,0,25
	.word	10537
	.byte	26
	.word	10570
	.byte	6,0,25
	.word	10610
	.byte	26
	.word	10651
	.byte	6,0,25
	.word	10670
	.byte	26
	.word	10725
	.byte	6,0,25
	.word	10744
	.byte	26
	.word	10784
	.byte	26
	.word	10801
	.byte	17,6,0,0,25
	.word	14255
	.byte	26
	.word	14278
	.byte	6,0,25
	.word	14293
	.byte	26
	.word	14325
	.byte	17,17,27
	.word	10227
	.byte	28
	.word	10265
	.byte	0,0,6,0,0,25
	.word	14343
	.byte	26
	.word	14371
	.byte	6,0,25
	.word	14386
	.byte	17,27
	.word	10422
	.byte	30
	.word	10459
	.byte	27
	.word	10380
	.byte	28
	.word	10420
	.byte	0,28
	.word	10460
	.byte	0,0,6,0,0,25
	.word	14419
	.byte	26
	.word	14445
	.byte	17,27
	.word	10537
	.byte	26
	.word	10570
	.byte	28
	.word	10587
	.byte	0,6,0,0,25
	.word	14483
	.byte	26
	.word	14507
	.byte	17,27
	.word	14573
	.byte	30
	.word	14589
	.byte	27
	.word	14386
	.byte	30
	.word	14415
	.byte	27
	.word	10422
	.byte	30
	.word	10459
	.byte	27
	.word	10380
	.byte	28
	.word	10420
	.byte	0,28
	.word	10460
	.byte	0,0,28
	.word	14416
	.byte	0,0,28
	.word	14590
	.byte	27
	.word	14419
	.byte	26
	.word	14445
	.byte	30
	.word	14462
	.byte	27
	.word	10537
	.byte	26
	.word	10570
	.byte	28
	.word	10587
	.byte	0,28
	.word	14463
	.byte	0,0,28
	.word	14591
	.byte	27
	.word	14255
	.byte	26
	.word	14278
	.byte	28
	.word	14291
	.byte	0,28
	.word	14592
	.byte	0,0,6,0,0,25
	.word	14528
	.byte	26
	.word	14551
	.byte	17,27
	.word	14573
	.byte	30
	.word	14589
	.byte	27
	.word	14386
	.byte	30
	.word	14415
	.byte	27
	.word	10422
	.byte	30
	.word	10459
	.byte	27
	.word	10380
	.byte	28
	.word	10420
	.byte	0,28
	.word	10460
	.byte	0,0,28
	.word	14416
	.byte	0,0,28
	.word	14590
	.byte	27
	.word	14419
	.byte	26
	.word	14445
	.byte	30
	.word	14462
	.byte	27
	.word	10537
	.byte	26
	.word	10570
	.byte	28
	.word	10587
	.byte	0,28
	.word	14463
	.byte	0,0,28
	.word	14591
	.byte	27
	.word	14255
	.byte	26
	.word	14278
	.byte	28
	.word	14291
	.byte	0,28
	.word	14592
	.byte	0,0,6,0,0,25
	.word	14573
	.byte	17,27
	.word	14386
	.byte	30
	.word	14415
	.byte	27
	.word	10422
	.byte	30
	.word	10459
	.byte	27
	.word	10380
	.byte	28
	.word	10420
	.byte	0,28
	.word	10460
	.byte	0,0,28
	.word	14416
	.byte	0,0,6,27
	.word	14419
	.byte	26
	.word	14445
	.byte	30
	.word	14462
	.byte	27
	.word	10537
	.byte	26
	.word	10570
	.byte	28
	.word	10587
	.byte	0,28
	.word	14463
	.byte	0,0,6,27
	.word	14255
	.byte	26
	.word	14278
	.byte	28
	.word	14291
	.byte	0,6,0,0,25
	.word	14595
	.byte	17,27
	.word	14255
	.byte	26
	.word	14278
	.byte	28
	.word	14291
	.byte	0,6,0,0,7
	.byte	'char',0,1,6,3
	.word	37757
	.byte	29
	.byte	'debug_assert_handler',0,19,112,9,1,1,1,1,5
	.byte	'pass',0,19,112,47
	.word	489
	.byte	5
	.byte	'file',0,19,112,59
	.word	37765
	.byte	5
	.byte	'line',0,19,112,69
	.word	482
	.byte	0,25
	.word	36000
	.byte	26
	.word	36029
	.byte	26
	.word	36042
	.byte	17,27
	.word	9966
	.byte	26
	.word	9998
	.byte	26
	.word	10012
	.byte	26
	.word	10030
	.byte	28
	.word	10044
	.byte	0,6,0,0
.L164:
	.byte	15,16,92,9,1,16
	.byte	'IfxScuEru_InputChannel_0',0,0,16
	.byte	'IfxScuEru_InputChannel_1',0,1,16
	.byte	'IfxScuEru_InputChannel_2',0,2,16
	.byte	'IfxScuEru_InputChannel_3',0,3,16
	.byte	'IfxScuEru_InputChannel_4',0,4,16
	.byte	'IfxScuEru_InputChannel_5',0,5,16
	.byte	'IfxScuEru_InputChannel_6',0,6,16
	.byte	'IfxScuEru_InputChannel_7',0,7,0,15,16,82,9,1,16
	.byte	'IfxScuEru_ExternalInputSelection_0',0,0,16
	.byte	'IfxScuEru_ExternalInputSelection_1',0,1,16
	.byte	'IfxScuEru_ExternalInputSelection_2',0,2,16
	.byte	'IfxScuEru_ExternalInputSelection_3',0,3,0,29
	.byte	'IfxScuEru_selectExternalInput',0,16,169,1,17,1,1,1,1,5
	.byte	'inputChannel',0,16,169,1,70
	.word	37884
	.byte	5
	.byte	'inputSignal',0,16,169,1,117
	.word	38106
	.byte	0,29
	.byte	'IfxScuEru_disableFallingEdgeDetection',0,16,207,1,17,1,1,1,1,5
	.byte	'inputChannel',0,16,207,1,78
	.word	37884
	.byte	0,29
	.byte	'IfxScuEru_disableRisingEdgeDetection',0,16,213,1,17,1,1,1,1,5
	.byte	'inputChannel',0,16,213,1,77
	.word	37884
	.byte	0,29
	.byte	'IfxScuEru_enableFallingEdgeDetection',0,16,225,1,17,1,1,1,1,5
	.byte	'inputChannel',0,16,225,1,77
	.word	37884
	.byte	0,29
	.byte	'IfxScuEru_enableRisingEdgeDetection',0,16,231,1,17,1,1,1,1,5
	.byte	'inputChannel',0,16,231,1,76
	.word	37884
	.byte	0
.L166:
	.byte	15,16,107,9,1,16
	.byte	'IfxScuEru_InputNodePointer_0',0,0,16
	.byte	'IfxScuEru_InputNodePointer_1',0,1,16
	.byte	'IfxScuEru_InputNodePointer_2',0,2,16
	.byte	'IfxScuEru_InputNodePointer_3',0,3,16
	.byte	'IfxScuEru_InputNodePointer_4',0,4,16
	.byte	'IfxScuEru_InputNodePointer_5',0,5,16
	.byte	'IfxScuEru_InputNodePointer_6',0,6,16
	.byte	'IfxScuEru_InputNodePointer_7',0,7,0,29
	.byte	'IfxScuEru_connectTrigger',0,16,142,2,17,1,1,1,1,5
	.byte	'inputChannel',0,16,142,2,65
	.word	37884
	.byte	5
	.byte	'triggerSelect',0,16,142,2,106
	.word	38619
	.byte	0,29
	.byte	'IfxScuEru_enableTriggerPulse',0,16,154,2,17,1,1,1,1,5
	.byte	'inputChannel',0,16,154,2,69
	.word	37884
	.byte	0
.L109:
	.byte	15,16,132,1,9,1,16
	.byte	'IfxScuEru_OutputChannel_0',0,0,16
	.byte	'IfxScuEru_OutputChannel_1',0,1,16
	.byte	'IfxScuEru_OutputChannel_2',0,2,16
	.byte	'IfxScuEru_OutputChannel_3',0,3,16
	.byte	'IfxScuEru_OutputChannel_4',0,4,16
	.byte	'IfxScuEru_OutputChannel_5',0,5,16
	.byte	'IfxScuEru_OutputChannel_6',0,6,16
	.byte	'IfxScuEru_OutputChannel_7',0,7,0,29
	.byte	'IfxScuEru_setFlagPatternDetection',0,16,163,2,17,1,1,1,1,5
	.byte	'outputChannel',0,16,163,2,75
	.word	39014
	.byte	5
	.byte	'inputChannel',0,16,163,2,113
	.word	37884
	.byte	5
	.byte	'state',0,16,163,2,135,1
	.word	489
	.byte	0,29
	.byte	'IfxScuEru_enablePatternDetectionTrigger',0,16,190,2,17,1,1,1,1,5
	.byte	'outputChannel',0,16,190,2,81
	.word	39014
	.byte	0,15,16,122,9,1,16
	.byte	'IfxScuEru_InterruptGatingPattern_none',0,0,16
	.byte	'IfxScuEru_InterruptGatingPattern_alwaysActive',0,1,16
	.byte	'IfxScuEru_InterruptGatingPattern_patternMatch',0,2,16
	.byte	'IfxScuEru_InterruptGatingPattern_patternMiss',0,3,0,29
	.byte	'IfxScuEru_setInterruptGatingPattern',0,16,214,2,17,1,1,1,1,5
	.byte	'outputChannel',0,16,214,2,77
	.word	39014
	.byte	5
	.byte	'gatingPattern',0,16,214,2,125
	.word	39423
	.byte	0
.L98:
	.byte	7
	.byte	'char',0,1,6
.L107:
	.byte	15,20,42,9,1,16
	.byte	'ERU_CH0_REQ0_P15_4',0,1,16
	.byte	'ERU_CH1_REQ10_P14_3',0,4,16
	.byte	'ERU_CH2_REQ7_P00_4',0,6,16
	.byte	'ERU_CH2_REQ14_P02_1',0,7,16
	.byte	'ERU_CH2_REQ2_P10_2',0,8,16
	.byte	'ERU_CH3_REQ6_P02_0',0,9,16
	.byte	'ERU_CH3_REQ3_P10_3',0,10,16
	.byte	'ERU_CH3_REQ15_P14_1',0,11,16
	.byte	'ERU_CH4_REQ13_P15_5',0,12,16
	.byte	'ERU_CH4_REQ8_P33_7',0,13,16
	.byte	'ERU_CH5_REQ1_P15_8',0,15,16
	.byte	'ERU_CH6_REQ12_P11_10',0,18,16
	.byte	'ERU_CH6_REQ9_P20_0',0,19,16
	.byte	'ERU_CH7_REQ16_P15_1',0,21,16
	.byte	'ERU_CH7_REQ11_P20_9',0,22,0
.L130:
	.byte	15,20,65,9,1,16
	.byte	'EXTI_TRIGGER_RISING',0,0,16
	.byte	'EXTI_TRIGGER_FALLING',0,1,16
	.byte	'EXTI_TRIGGER_BOTH',0,2,0,10
	.byte	'_Ifx_CPU_ICR_Bits',0,21,246,2,16,4,11
	.byte	'CCPN',0,4
	.word	1158
	.byte	10,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	1158
	.byte	5,17,2,35,0,11
	.byte	'IE',0,4
	.word	1158
	.byte	1,16,2,35,0,11
	.byte	'PIPN',0,4
	.word	1158
	.byte	10,6,2,35,0,11
	.byte	'reserved_26',0,4
	.word	1158
	.byte	6,0,2,35,0,0
.L144:
	.byte	12,21,223,7,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40112
	.byte	4,2,35,0,0,7
	.byte	'short int',0,2,5,31
	.byte	'__wchar_t',0,22,1,1
	.word	40269
	.byte	31
	.byte	'__size_t',0,22,1,1
	.word	466
	.byte	31
	.byte	'__ptrdiff_t',0,22,1,1
	.word	482
	.byte	32,1,3
	.word	40337
	.byte	31
	.byte	'__codeptr',0,22,1,1
	.word	40339
	.byte	31
	.byte	'__intptr_t',0,22,1,1
	.word	482
	.byte	31
	.byte	'__uintptr_t',0,22,1,1
	.word	466
	.byte	31
	.byte	'IfxSrc_Tos',0,5,74,3
	.word	1000
	.byte	31
	.byte	'boolean',0,23,101,29
	.word	489
	.byte	31
	.byte	'uint8',0,23,105,29
	.word	489
	.byte	31
	.byte	'uint16',0,23,109,29
	.word	1059
	.byte	31
	.byte	'uint32',0,23,113,29
	.word	10589
	.byte	31
	.byte	'uint64',0,23,118,29
	.word	348
	.byte	31
	.byte	'sint16',0,23,126,29
	.word	40269
	.byte	7
	.byte	'long int',0,4,5,31
	.byte	'sint32',0,23,131,1,29
	.word	40510
	.byte	31
	.byte	'sint64',0,23,138,1,29
	.word	14466
	.byte	31
	.byte	'float32',0,23,167,1,29
	.word	262
	.byte	31
	.byte	'pvoid',0,18,57,28
	.word	380
	.byte	31
	.byte	'Ifx_TickTime',0,18,79,28
	.word	14466
	.byte	31
	.byte	'Ifx_Priority',0,18,103,16
	.word	1059
	.byte	31
	.byte	'Ifx_RxSel',0,18,140,1,3
	.word	35801
	.byte	31
	.byte	'Ifx_SRC_SRCR_Bits',0,4,62,3
	.word	506
	.byte	31
	.byte	'Ifx_SRC_SRCR',0,4,75,3
	.word	796
	.byte	10
	.byte	'_Ifx_SRC_AGBT',0,4,86,25,4,13
	.byte	'SR',0
	.word	796
	.byte	4,2,35,0,0,14
	.word	40693
	.byte	31
	.byte	'Ifx_SRC_AGBT',0,4,89,3
	.word	40725
	.byte	10
	.byte	'_Ifx_SRC_ASCLIN',0,4,92,25,12,13
	.byte	'TX',0
	.word	796
	.byte	4,2,35,0,13
	.byte	'RX',0
	.word	796
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	796
	.byte	4,2,35,8,0,14
	.word	40751
	.byte	31
	.byte	'Ifx_SRC_ASCLIN',0,4,97,3
	.word	40810
	.byte	10
	.byte	'_Ifx_SRC_BCUSPB',0,4,100,25,4,13
	.byte	'SBSRC',0
	.word	796
	.byte	4,2,35,0,0,14
	.word	40838
	.byte	31
	.byte	'Ifx_SRC_BCUSPB',0,4,103,3
	.word	40875
	.byte	18,64
	.word	796
	.byte	19,15,0,10
	.byte	'_Ifx_SRC_CAN',0,4,106,25,64,13
	.byte	'INT',0
	.word	40903
	.byte	64,2,35,0,0,14
	.word	40912
	.byte	31
	.byte	'Ifx_SRC_CAN',0,4,109,3
	.word	40944
	.byte	10
	.byte	'_Ifx_SRC_CCU6',0,4,112,25,16,13
	.byte	'SR0',0
	.word	796
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	796
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	796
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	796
	.byte	4,2,35,12,0,14
	.word	40969
	.byte	31
	.byte	'Ifx_SRC_CCU6',0,4,118,3
	.word	41041
	.byte	18,8
	.word	796
	.byte	19,1,0,10
	.byte	'_Ifx_SRC_CERBERUS',0,4,121,25,8,13
	.byte	'SR',0
	.word	41067
	.byte	8,2,35,0,0,14
	.word	41076
	.byte	31
	.byte	'Ifx_SRC_CERBERUS',0,4,124,3
	.word	41112
	.byte	10
	.byte	'_Ifx_SRC_CIF',0,4,127,25,16,13
	.byte	'MI',0
	.word	796
	.byte	4,2,35,0,13
	.byte	'MIEP',0
	.word	796
	.byte	4,2,35,4,13
	.byte	'ISP',0
	.word	796
	.byte	4,2,35,8,13
	.byte	'MJPEG',0
	.word	796
	.byte	4,2,35,12,0,14
	.word	41142
	.byte	31
	.byte	'Ifx_SRC_CIF',0,4,133,1,3
	.word	41215
	.byte	10
	.byte	'_Ifx_SRC_CPU',0,4,136,1,25,4,13
	.byte	'SBSRC',0
	.word	796
	.byte	4,2,35,0,0,14
	.word	41241
	.byte	31
	.byte	'Ifx_SRC_CPU',0,4,139,1,3
	.word	41276
	.byte	18,192,1
	.word	796
	.byte	19,47,0,10
	.byte	'_Ifx_SRC_DMA',0,4,142,1,25,208,1,13
	.byte	'ERR',0
	.word	796
	.byte	4,2,35,0,13
	.byte	'reserved_4',0
	.word	5248
	.byte	12,2,35,4,13
	.byte	'CH',0
	.word	41302
	.byte	192,1,2,35,16,0,14
	.word	41312
	.byte	31
	.byte	'Ifx_SRC_DMA',0,4,147,1,3
	.word	41379
	.byte	10
	.byte	'_Ifx_SRC_DSADC',0,4,150,1,25,8,13
	.byte	'SRM',0
	.word	796
	.byte	4,2,35,0,13
	.byte	'SRA',0
	.word	796
	.byte	4,2,35,4,0,14
	.word	41405
	.byte	31
	.byte	'Ifx_SRC_DSADC',0,4,154,1,3
	.word	41453
	.byte	10
	.byte	'_Ifx_SRC_EMEM',0,4,157,1,25,4,13
	.byte	'SR',0
	.word	796
	.byte	4,2,35,0,0,14
	.word	41481
	.byte	31
	.byte	'Ifx_SRC_EMEM',0,4,160,1,3
	.word	41514
	.byte	10
	.byte	'_Ifx_SRC_ERAY',0,4,163,1,25,80,13
	.byte	'INT',0
	.word	41067
	.byte	8,2,35,0,13
	.byte	'TINT',0
	.word	41067
	.byte	8,2,35,8,13
	.byte	'NDAT',0
	.word	41067
	.byte	8,2,35,16,13
	.byte	'MBSC',0
	.word	41067
	.byte	8,2,35,24,13
	.byte	'OBUSY',0
	.word	796
	.byte	4,2,35,32,13
	.byte	'IBUSY',0
	.word	796
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	31240
	.byte	40,2,35,40,0,14
	.word	41541
	.byte	31
	.byte	'Ifx_SRC_ERAY',0,4,172,1,3
	.word	41668
	.byte	10
	.byte	'_Ifx_SRC_ETH',0,4,175,1,25,4,13
	.byte	'SR',0
	.word	796
	.byte	4,2,35,0,0,14
	.word	41695
	.byte	31
	.byte	'Ifx_SRC_ETH',0,4,178,1,3
	.word	41727
	.byte	10
	.byte	'_Ifx_SRC_FCE',0,4,181,1,25,4,13
	.byte	'SR',0
	.word	796
	.byte	4,2,35,0,0,14
	.word	41753
	.byte	31
	.byte	'Ifx_SRC_FCE',0,4,184,1,3
	.word	41785
	.byte	10
	.byte	'_Ifx_SRC_FFT',0,4,187,1,25,12,13
	.byte	'DONE',0
	.word	796
	.byte	4,2,35,0,13
	.byte	'ERR',0
	.word	796
	.byte	4,2,35,4,13
	.byte	'RFS',0
	.word	796
	.byte	4,2,35,8,0,14
	.word	41811
	.byte	31
	.byte	'Ifx_SRC_FFT',0,4,192,1,3
	.word	41871
	.byte	10
	.byte	'_Ifx_SRC_GPSR',0,4,195,1,25,32,13
	.byte	'SR0',0
	.word	796
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	796
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	796
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	796
	.byte	4,2,35,12,13
	.byte	'reserved_10',0
	.word	27347
	.byte	16,2,35,16,0,14
	.word	41897
	.byte	31
	.byte	'Ifx_SRC_GPSR',0,4,202,1,3
	.word	41991
	.byte	10
	.byte	'_Ifx_SRC_GPT12',0,4,205,1,25,48,13
	.byte	'CIRQ',0
	.word	796
	.byte	4,2,35,0,13
	.byte	'T2',0
	.word	796
	.byte	4,2,35,4,13
	.byte	'T3',0
	.word	796
	.byte	4,2,35,8,13
	.byte	'T4',0
	.word	796
	.byte	4,2,35,12,13
	.byte	'T5',0
	.word	796
	.byte	4,2,35,16,13
	.byte	'T6',0
	.word	796
	.byte	4,2,35,20,13
	.byte	'reserved_18',0
	.word	4279
	.byte	24,2,35,24,0,14
	.word	42018
	.byte	31
	.byte	'Ifx_SRC_GPT12',0,4,214,1,3
	.word	42135
	.byte	18,12
	.word	796
	.byte	19,2,0,18,32
	.word	796
	.byte	19,7,0,18,32
	.word	42172
	.byte	19,0,0,18,88
	.word	489
	.byte	19,87,0,18,108
	.word	796
	.byte	19,26,0,18,96
	.word	489
	.byte	19,95,0,18,96
	.word	42172
	.byte	19,2,0,18,160,3
	.word	489
	.byte	19,159,3,0,18,64
	.word	42172
	.byte	19,1,0,18,192,3
	.word	489
	.byte	19,191,3,0,18,16
	.word	796
	.byte	19,3,0,18,64
	.word	42257
	.byte	19,3,0,18,192,2
	.word	489
	.byte	19,191,2,0,18,52
	.word	489
	.byte	19,51,0,10
	.byte	'_Ifx_SRC_GTM',0,4,217,1,25,204,18,13
	.byte	'AEIIRQ',0
	.word	796
	.byte	4,2,35,0,13
	.byte	'ARUIRQ',0
	.word	42163
	.byte	12,2,35,4,13
	.byte	'reserved_10',0
	.word	3089
	.byte	4,2,35,16,13
	.byte	'BRCIRQ',0
	.word	796
	.byte	4,2,35,20,13
	.byte	'CMPIRQ',0
	.word	796
	.byte	4,2,35,24,13
	.byte	'SPEIRQ',0
	.word	41067
	.byte	8,2,35,28,13
	.byte	'reserved_24',0
	.word	4908
	.byte	8,2,35,36,13
	.byte	'PSM',0
	.word	42181
	.byte	32,2,35,44,13
	.byte	'reserved_4C',0
	.word	42190
	.byte	88,2,35,76,13
	.byte	'DPLL',0
	.word	42199
	.byte	108,3,35,164,1,13
	.byte	'reserved_110',0
	.word	42208
	.byte	96,3,35,144,2,13
	.byte	'ERR',0
	.word	796
	.byte	4,3,35,240,2,13
	.byte	'reserved_174',0
	.word	5248
	.byte	12,3,35,244,2,13
	.byte	'TIM',0
	.word	42217
	.byte	96,3,35,128,3,13
	.byte	'reserved_1E0',0
	.word	42226
	.byte	160,3,3,35,224,3,13
	.byte	'MCS',0
	.word	42217
	.byte	96,3,35,128,7,13
	.byte	'reserved_3E0',0
	.word	42226
	.byte	160,3,3,35,224,7,13
	.byte	'TOM',0
	.word	42237
	.byte	64,3,35,128,11,13
	.byte	'reserved_5C0',0
	.word	42246
	.byte	192,3,3,35,192,11,13
	.byte	'ATOM',0
	.word	42266
	.byte	64,3,35,128,15,13
	.byte	'reserved_7C0',0
	.word	42275
	.byte	192,2,3,35,192,15,13
	.byte	'MCSW0',0
	.word	42163
	.byte	12,3,35,128,18,13
	.byte	'reserved_90C',0
	.word	42286
	.byte	52,3,35,140,18,13
	.byte	'MCSW1',0
	.word	42163
	.byte	12,3,35,192,18,0,14
	.word	42295
	.byte	31
	.byte	'Ifx_SRC_GTM',0,4,243,1,3
	.word	42755
	.byte	10
	.byte	'_Ifx_SRC_HSCT',0,4,246,1,25,4,13
	.byte	'SR',0
	.word	796
	.byte	4,2,35,0,0,14
	.word	42781
	.byte	31
	.byte	'Ifx_SRC_HSCT',0,4,249,1,3
	.word	42814
	.byte	10
	.byte	'_Ifx_SRC_HSSL',0,4,252,1,25,16,13
	.byte	'COK',0
	.word	796
	.byte	4,2,35,0,13
	.byte	'RDI',0
	.word	796
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	796
	.byte	4,2,35,8,13
	.byte	'TRG',0
	.word	796
	.byte	4,2,35,12,0,14
	.word	42841
	.byte	31
	.byte	'Ifx_SRC_HSSL',0,4,130,2,3
	.word	42914
	.byte	18,56
	.word	489
	.byte	19,55,0,10
	.byte	'_Ifx_SRC_I2C',0,4,133,2,25,80,13
	.byte	'BREQ',0
	.word	796
	.byte	4,2,35,0,13
	.byte	'LBREQ',0
	.word	796
	.byte	4,2,35,4,13
	.byte	'SREQ',0
	.word	796
	.byte	4,2,35,8,13
	.byte	'LSREQ',0
	.word	796
	.byte	4,2,35,12,13
	.byte	'ERR',0
	.word	796
	.byte	4,2,35,16,13
	.byte	'P',0
	.word	796
	.byte	4,2,35,20,13
	.byte	'reserved_18',0
	.word	42941
	.byte	56,2,35,24,0,14
	.word	42950
	.byte	31
	.byte	'Ifx_SRC_I2C',0,4,142,2,3
	.word	43073
	.byte	10
	.byte	'_Ifx_SRC_LMU',0,4,145,2,25,4,13
	.byte	'SR',0
	.word	796
	.byte	4,2,35,0,0,14
	.word	43099
	.byte	31
	.byte	'Ifx_SRC_LMU',0,4,148,2,3
	.word	43131
	.byte	10
	.byte	'_Ifx_SRC_MSC',0,4,151,2,25,20,13
	.byte	'SR0',0
	.word	796
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	796
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	796
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	796
	.byte	4,2,35,12,13
	.byte	'SR4',0
	.word	796
	.byte	4,2,35,16,0,14
	.word	43157
	.byte	31
	.byte	'Ifx_SRC_MSC',0,4,158,2,3
	.word	43242
	.byte	10
	.byte	'_Ifx_SRC_PMU',0,4,161,2,25,4,13
	.byte	'SR',0
	.word	796
	.byte	4,2,35,0,0,14
	.word	43268
	.byte	31
	.byte	'Ifx_SRC_PMU',0,4,164,2,3
	.word	43300
	.byte	10
	.byte	'_Ifx_SRC_PSI5',0,4,167,2,25,32,13
	.byte	'SR',0
	.word	42172
	.byte	32,2,35,0,0,14
	.word	43326
	.byte	31
	.byte	'Ifx_SRC_PSI5',0,4,170,2,3
	.word	43359
	.byte	10
	.byte	'_Ifx_SRC_PSI5S',0,4,173,2,25,32,13
	.byte	'SR',0
	.word	42172
	.byte	32,2,35,0,0,14
	.word	43386
	.byte	31
	.byte	'Ifx_SRC_PSI5S',0,4,176,2,3
	.word	43420
	.byte	10
	.byte	'_Ifx_SRC_QSPI',0,4,179,2,25,24,13
	.byte	'TX',0
	.word	796
	.byte	4,2,35,0,13
	.byte	'RX',0
	.word	796
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	796
	.byte	4,2,35,8,13
	.byte	'PT',0
	.word	796
	.byte	4,2,35,12,13
	.byte	'HC',0
	.word	796
	.byte	4,2,35,16,13
	.byte	'U',0
	.word	796
	.byte	4,2,35,20,0,14
	.word	43448
	.byte	31
	.byte	'Ifx_SRC_QSPI',0,4,187,2,3
	.word	43541
	.byte	10
	.byte	'_Ifx_SRC_SCR',0,4,190,2,25,4,13
	.byte	'SR',0
	.word	796
	.byte	4,2,35,0,0,14
	.word	43568
	.byte	31
	.byte	'Ifx_SRC_SCR',0,4,193,2,3
	.word	43600
	.byte	10
	.byte	'_Ifx_SRC_SCU',0,4,196,2,25,20,13
	.byte	'DTS',0
	.word	796
	.byte	4,2,35,0,13
	.byte	'ERU',0
	.word	42257
	.byte	16,2,35,4,0,14
	.word	43626
	.byte	31
	.byte	'Ifx_SRC_SCU',0,4,200,2,3
	.word	43672
	.byte	18,24
	.word	796
	.byte	19,5,0,10
	.byte	'_Ifx_SRC_SENT',0,4,203,2,25,24,13
	.byte	'SR',0
	.word	43698
	.byte	24,2,35,0,0,14
	.word	43707
	.byte	31
	.byte	'Ifx_SRC_SENT',0,4,206,2,3
	.word	43740
	.byte	10
	.byte	'_Ifx_SRC_SMU',0,4,209,2,25,12,13
	.byte	'SR',0
	.word	42163
	.byte	12,2,35,0,0,14
	.word	43767
	.byte	31
	.byte	'Ifx_SRC_SMU',0,4,212,2,3
	.word	43799
	.byte	10
	.byte	'_Ifx_SRC_STM',0,4,215,2,25,8,13
	.byte	'SR0',0
	.word	796
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	796
	.byte	4,2,35,4,0,14
	.word	43825
	.byte	31
	.byte	'Ifx_SRC_STM',0,4,219,2,3
	.word	43871
	.byte	10
	.byte	'_Ifx_SRC_VADCCG',0,4,222,2,25,16,13
	.byte	'SR0',0
	.word	796
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	796
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	796
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	796
	.byte	4,2,35,12,0,14
	.word	43897
	.byte	31
	.byte	'Ifx_SRC_VADCCG',0,4,228,2,3
	.word	43972
	.byte	10
	.byte	'_Ifx_SRC_VADCG',0,4,231,2,25,16,13
	.byte	'SR0',0
	.word	796
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	796
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	796
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	796
	.byte	4,2,35,12,0,14
	.word	44001
	.byte	31
	.byte	'Ifx_SRC_VADCG',0,4,237,2,3
	.word	44075
	.byte	10
	.byte	'_Ifx_SRC_XBAR',0,4,240,2,25,4,13
	.byte	'SRC',0
	.word	796
	.byte	4,2,35,0,0,14
	.word	44103
	.byte	31
	.byte	'Ifx_SRC_XBAR',0,4,243,2,3
	.word	44137
	.byte	18,4
	.word	40693
	.byte	19,0,0,14
	.word	44164
	.byte	10
	.byte	'_Ifx_SRC_GAGBT',0,4,128,3,25,4,13
	.byte	'AGBT',0
	.word	44173
	.byte	4,2,35,0,0,14
	.word	44178
	.byte	31
	.byte	'Ifx_SRC_GAGBT',0,4,131,3,3
	.word	44214
	.byte	18,48
	.word	40751
	.byte	19,3,0,14
	.word	44242
	.byte	10
	.byte	'_Ifx_SRC_GASCLIN',0,4,134,3,25,48,13
	.byte	'ASCLIN',0
	.word	44251
	.byte	48,2,35,0,0,14
	.word	44256
	.byte	31
	.byte	'Ifx_SRC_GASCLIN',0,4,137,3,3
	.word	44296
	.byte	14
	.word	40838
	.byte	10
	.byte	'_Ifx_SRC_GBCU',0,4,140,3,25,4,13
	.byte	'SPB',0
	.word	44326
	.byte	4,2,35,0,0,14
	.word	44331
	.byte	31
	.byte	'Ifx_SRC_GBCU',0,4,143,3,3
	.word	44365
	.byte	18,64
	.word	40912
	.byte	19,0,0,14
	.word	44392
	.byte	10
	.byte	'_Ifx_SRC_GCAN',0,4,146,3,25,64,13
	.byte	'CAN',0
	.word	44401
	.byte	64,2,35,0,0,14
	.word	44406
	.byte	31
	.byte	'Ifx_SRC_GCAN',0,4,149,3,3
	.word	44440
	.byte	18,32
	.word	40969
	.byte	19,1,0,14
	.word	44467
	.byte	10
	.byte	'_Ifx_SRC_GCCU6',0,4,152,3,25,32,13
	.byte	'CCU6',0
	.word	44476
	.byte	32,2,35,0,0,14
	.word	44481
	.byte	31
	.byte	'Ifx_SRC_GCCU6',0,4,155,3,3
	.word	44517
	.byte	14
	.word	41076
	.byte	10
	.byte	'_Ifx_SRC_GCERBERUS',0,4,158,3,25,8,13
	.byte	'CERBERUS',0
	.word	44545
	.byte	8,2,35,0,0,14
	.word	44550
	.byte	31
	.byte	'Ifx_SRC_GCERBERUS',0,4,161,3,3
	.word	44594
	.byte	18,16
	.word	41142
	.byte	19,0,0,14
	.word	44626
	.byte	10
	.byte	'_Ifx_SRC_GCIF',0,4,164,3,25,16,13
	.byte	'CIF',0
	.word	44635
	.byte	16,2,35,0,0,14
	.word	44640
	.byte	31
	.byte	'Ifx_SRC_GCIF',0,4,167,3,3
	.word	44674
	.byte	18,8
	.word	41241
	.byte	19,1,0,14
	.word	44701
	.byte	10
	.byte	'_Ifx_SRC_GCPU',0,4,170,3,25,8,13
	.byte	'CPU',0
	.word	44710
	.byte	8,2,35,0,0,14
	.word	44715
	.byte	31
	.byte	'Ifx_SRC_GCPU',0,4,173,3,3
	.word	44749
	.byte	18,208,1
	.word	41312
	.byte	19,0,0,14
	.word	44776
	.byte	10
	.byte	'_Ifx_SRC_GDMA',0,4,176,3,25,208,1,13
	.byte	'DMA',0
	.word	44786
	.byte	208,1,2,35,0,0,14
	.word	44791
	.byte	31
	.byte	'Ifx_SRC_GDMA',0,4,179,3,3
	.word	44827
	.byte	14
	.word	41405
	.byte	14
	.word	41405
	.byte	14
	.word	41405
	.byte	10
	.byte	'_Ifx_SRC_GDSADC',0,4,182,3,25,32,13
	.byte	'DSADC0',0
	.word	44854
	.byte	8,2,35,0,13
	.byte	'reserved_8',0
	.word	4908
	.byte	8,2,35,8,13
	.byte	'DSADC2',0
	.word	44859
	.byte	8,2,35,16,13
	.byte	'DSADC3',0
	.word	44864
	.byte	8,2,35,24,0,14
	.word	44869
	.byte	31
	.byte	'Ifx_SRC_GDSADC',0,4,188,3,3
	.word	44960
	.byte	18,4
	.word	41481
	.byte	19,0,0,14
	.word	44989
	.byte	10
	.byte	'_Ifx_SRC_GEMEM',0,4,191,3,25,4,13
	.byte	'EMEM',0
	.word	44998
	.byte	4,2,35,0,0,14
	.word	45003
	.byte	31
	.byte	'Ifx_SRC_GEMEM',0,4,194,3,3
	.word	45039
	.byte	18,80
	.word	41541
	.byte	19,0,0,14
	.word	45067
	.byte	10
	.byte	'_Ifx_SRC_GERAY',0,4,197,3,25,80,13
	.byte	'ERAY',0
	.word	45076
	.byte	80,2,35,0,0,14
	.word	45081
	.byte	31
	.byte	'Ifx_SRC_GERAY',0,4,200,3,3
	.word	45117
	.byte	18,4
	.word	41695
	.byte	19,0,0,14
	.word	45145
	.byte	10
	.byte	'_Ifx_SRC_GETH',0,4,203,3,25,4,13
	.byte	'ETH',0
	.word	45154
	.byte	4,2,35,0,0,14
	.word	45159
	.byte	31
	.byte	'Ifx_SRC_GETH',0,4,206,3,3
	.word	45193
	.byte	18,4
	.word	41753
	.byte	19,0,0,14
	.word	45220
	.byte	10
	.byte	'_Ifx_SRC_GFCE',0,4,209,3,25,4,13
	.byte	'FCE',0
	.word	45229
	.byte	4,2,35,0,0,14
	.word	45234
	.byte	31
	.byte	'Ifx_SRC_GFCE',0,4,212,3,3
	.word	45268
	.byte	18,12
	.word	41811
	.byte	19,0,0,14
	.word	45295
	.byte	10
	.byte	'_Ifx_SRC_GFFT',0,4,215,3,25,12,13
	.byte	'FFT',0
	.word	45304
	.byte	12,2,35,0,0,14
	.word	45309
	.byte	31
	.byte	'Ifx_SRC_GFFT',0,4,218,3,3
	.word	45343
	.byte	18,64
	.word	41897
	.byte	19,1,0,14
	.word	45370
	.byte	10
	.byte	'_Ifx_SRC_GGPSR',0,4,221,3,25,64,13
	.byte	'GPSR',0
	.word	45379
	.byte	64,2,35,0,0,14
	.word	45384
	.byte	31
	.byte	'Ifx_SRC_GGPSR',0,4,224,3,3
	.word	45420
	.byte	18,48
	.word	42018
	.byte	19,0,0,14
	.word	45448
	.byte	10
	.byte	'_Ifx_SRC_GGPT12',0,4,227,3,25,48,13
	.byte	'GPT12',0
	.word	45457
	.byte	48,2,35,0,0,14
	.word	45462
	.byte	31
	.byte	'Ifx_SRC_GGPT12',0,4,230,3,3
	.word	45500
	.byte	18,204,18
	.word	42295
	.byte	19,0,0,14
	.word	45529
	.byte	10
	.byte	'_Ifx_SRC_GGTM',0,4,233,3,25,204,18,13
	.byte	'GTM',0
	.word	45539
	.byte	204,18,2,35,0,0,14
	.word	45544
	.byte	31
	.byte	'Ifx_SRC_GGTM',0,4,236,3,3
	.word	45580
	.byte	18,4
	.word	42781
	.byte	19,0,0,14
	.word	45607
	.byte	10
	.byte	'_Ifx_SRC_GHSCT',0,4,239,3,25,4,13
	.byte	'HSCT',0
	.word	45616
	.byte	4,2,35,0,0,14
	.word	45621
	.byte	31
	.byte	'Ifx_SRC_GHSCT',0,4,242,3,3
	.word	45657
	.byte	18,64
	.word	42841
	.byte	19,3,0,14
	.word	45685
	.byte	10
	.byte	'_Ifx_SRC_GHSSL',0,4,245,3,25,68,13
	.byte	'HSSL',0
	.word	45694
	.byte	64,2,35,0,13
	.byte	'EXI',0
	.word	796
	.byte	4,2,35,64,0,14
	.word	45699
	.byte	31
	.byte	'Ifx_SRC_GHSSL',0,4,249,3,3
	.word	45748
	.byte	18,80
	.word	42950
	.byte	19,0,0,14
	.word	45776
	.byte	10
	.byte	'_Ifx_SRC_GI2C',0,4,252,3,25,80,13
	.byte	'I2C',0
	.word	45785
	.byte	80,2,35,0,0,14
	.word	45790
	.byte	31
	.byte	'Ifx_SRC_GI2C',0,4,255,3,3
	.word	45824
	.byte	18,4
	.word	43099
	.byte	19,0,0,14
	.word	45851
	.byte	10
	.byte	'_Ifx_SRC_GLMU',0,4,130,4,25,4,13
	.byte	'LMU',0
	.word	45860
	.byte	4,2,35,0,0,14
	.word	45865
	.byte	31
	.byte	'Ifx_SRC_GLMU',0,4,133,4,3
	.word	45899
	.byte	18,40
	.word	43157
	.byte	19,1,0,14
	.word	45926
	.byte	10
	.byte	'_Ifx_SRC_GMSC',0,4,136,4,25,40,13
	.byte	'MSC',0
	.word	45935
	.byte	40,2,35,0,0,14
	.word	45940
	.byte	31
	.byte	'Ifx_SRC_GMSC',0,4,139,4,3
	.word	45974
	.byte	18,8
	.word	43268
	.byte	19,1,0,14
	.word	46001
	.byte	10
	.byte	'_Ifx_SRC_GPMU',0,4,142,4,25,8,13
	.byte	'PMU',0
	.word	46010
	.byte	8,2,35,0,0,14
	.word	46015
	.byte	31
	.byte	'Ifx_SRC_GPMU',0,4,145,4,3
	.word	46049
	.byte	18,32
	.word	43326
	.byte	19,0,0,14
	.word	46076
	.byte	10
	.byte	'_Ifx_SRC_GPSI5',0,4,148,4,25,32,13
	.byte	'PSI5',0
	.word	46085
	.byte	32,2,35,0,0,14
	.word	46090
	.byte	31
	.byte	'Ifx_SRC_GPSI5',0,4,151,4,3
	.word	46126
	.byte	18,32
	.word	43386
	.byte	19,0,0,14
	.word	46154
	.byte	10
	.byte	'_Ifx_SRC_GPSI5S',0,4,154,4,25,32,13
	.byte	'PSI5S',0
	.word	46163
	.byte	32,2,35,0,0,14
	.word	46168
	.byte	31
	.byte	'Ifx_SRC_GPSI5S',0,4,157,4,3
	.word	46206
	.byte	18,96
	.word	43448
	.byte	19,3,0,14
	.word	46235
	.byte	10
	.byte	'_Ifx_SRC_GQSPI',0,4,160,4,25,96,13
	.byte	'QSPI',0
	.word	46244
	.byte	96,2,35,0,0,14
	.word	46249
	.byte	31
	.byte	'Ifx_SRC_GQSPI',0,4,163,4,3
	.word	46285
	.byte	18,4
	.word	43568
	.byte	19,0,0,14
	.word	46313
	.byte	10
	.byte	'_Ifx_SRC_GSCR',0,4,166,4,25,4,13
	.byte	'SCR',0
	.word	46322
	.byte	4,2,35,0,0,14
	.word	46327
	.byte	31
	.byte	'Ifx_SRC_GSCR',0,4,169,4,3
	.word	46361
	.byte	14
	.word	43626
	.byte	10
	.byte	'_Ifx_SRC_GSCU',0,4,172,4,25,20,13
	.byte	'SCU',0
	.word	46388
	.byte	20,2,35,0,0,14
	.word	46393
	.byte	31
	.byte	'Ifx_SRC_GSCU',0,4,175,4,3
	.word	46427
	.byte	18,24
	.word	43707
	.byte	19,0,0,14
	.word	46454
	.byte	10
	.byte	'_Ifx_SRC_GSENT',0,4,178,4,25,24,13
	.byte	'SENT',0
	.word	46463
	.byte	24,2,35,0,0,14
	.word	46468
	.byte	31
	.byte	'Ifx_SRC_GSENT',0,4,181,4,3
	.word	46504
	.byte	18,12
	.word	43767
	.byte	19,0,0,14
	.word	46532
	.byte	10
	.byte	'_Ifx_SRC_GSMU',0,4,184,4,25,12,13
	.byte	'SMU',0
	.word	46541
	.byte	12,2,35,0,0,14
	.word	46546
	.byte	31
	.byte	'Ifx_SRC_GSMU',0,4,187,4,3
	.word	46580
	.byte	18,16
	.word	43825
	.byte	19,1,0,14
	.word	46607
	.byte	10
	.byte	'_Ifx_SRC_GSTM',0,4,190,4,25,16,13
	.byte	'STM',0
	.word	46616
	.byte	16,2,35,0,0,14
	.word	46621
	.byte	31
	.byte	'Ifx_SRC_GSTM',0,4,193,4,3
	.word	46655
	.byte	18,64
	.word	44001
	.byte	19,3,0,14
	.word	46682
	.byte	18,224,1
	.word	489
	.byte	19,223,1,0,18,32
	.word	43897
	.byte	19,1,0,14
	.word	46707
	.byte	10
	.byte	'_Ifx_SRC_GVADC',0,4,196,4,25,192,2,13
	.byte	'G',0
	.word	46691
	.byte	64,2,35,0,13
	.byte	'reserved_40',0
	.word	46696
	.byte	224,1,2,35,64,13
	.byte	'CG',0
	.word	46716
	.byte	32,3,35,160,2,0,14
	.word	46721
	.byte	31
	.byte	'Ifx_SRC_GVADC',0,4,201,4,3
	.word	46790
	.byte	14
	.word	44103
	.byte	10
	.byte	'_Ifx_SRC_GXBAR',0,4,204,4,25,4,13
	.byte	'XBAR',0
	.word	46818
	.byte	4,2,35,0,0,14
	.word	46823
	.byte	31
	.byte	'Ifx_SRC_GXBAR',0,4,207,4,3
	.word	46859
	.byte	10
	.byte	'_Ifx_CPU_A_Bits',0,21,45,16,4,11
	.byte	'ADDR',0,4
	.word	1158
	.byte	32,0,2,35,0,0,31
	.byte	'Ifx_CPU_A_Bits',0,21,48,3
	.word	46887
	.byte	10
	.byte	'_Ifx_CPU_BIV_Bits',0,21,51,16,4,11
	.byte	'VSS',0,4
	.word	1158
	.byte	1,31,2,35,0,11
	.byte	'BIV',0,4
	.word	1158
	.byte	31,0,2,35,0,0,31
	.byte	'Ifx_CPU_BIV_Bits',0,21,55,3
	.word	46948
	.byte	10
	.byte	'_Ifx_CPU_BTV_Bits',0,21,58,16,4,11
	.byte	'reserved_0',0,4
	.word	1158
	.byte	1,31,2,35,0,11
	.byte	'BTV',0,4
	.word	1158
	.byte	31,0,2,35,0,0,31
	.byte	'Ifx_CPU_BTV_Bits',0,21,62,3
	.word	47027
	.byte	10
	.byte	'_Ifx_CPU_CCNT_Bits',0,21,65,16,4,11
	.byte	'CountValue',0,4
	.word	1158
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	1158
	.byte	1,0,2,35,0,0,31
	.byte	'Ifx_CPU_CCNT_Bits',0,21,69,3
	.word	47113
	.byte	10
	.byte	'_Ifx_CPU_CCTRL_Bits',0,21,72,16,4,11
	.byte	'CM',0,4
	.word	1158
	.byte	1,31,2,35,0,11
	.byte	'CE',0,4
	.word	1158
	.byte	1,30,2,35,0,11
	.byte	'M1',0,4
	.word	1158
	.byte	3,27,2,35,0,11
	.byte	'M2',0,4
	.word	1158
	.byte	3,24,2,35,0,11
	.byte	'M3',0,4
	.word	1158
	.byte	3,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	1158
	.byte	21,0,2,35,0,0,31
	.byte	'Ifx_CPU_CCTRL_Bits',0,21,80,3
	.word	47202
	.byte	10
	.byte	'_Ifx_CPU_COMPAT_Bits',0,21,83,16,4,11
	.byte	'reserved_0',0,4
	.word	1158
	.byte	3,29,2,35,0,11
	.byte	'RM',0,4
	.word	1158
	.byte	1,28,2,35,0,11
	.byte	'SP',0,4
	.word	1158
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	1158
	.byte	27,0,2,35,0,0,31
	.byte	'Ifx_CPU_COMPAT_Bits',0,21,89,3
	.word	47348
	.byte	10
	.byte	'_Ifx_CPU_CORE_ID_Bits',0,21,92,16,4,11
	.byte	'CORE_ID',0,4
	.word	1158
	.byte	3,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	1158
	.byte	29,0,2,35,0,0,31
	.byte	'Ifx_CPU_CORE_ID_Bits',0,21,96,3
	.word	47475
	.byte	10
	.byte	'_Ifx_CPU_CPR_L_Bits',0,21,99,16,4,11
	.byte	'reserved_0',0,4
	.word	1158
	.byte	3,29,2,35,0,11
	.byte	'LOWBND',0,4
	.word	1158
	.byte	29,0,2,35,0,0,31
	.byte	'Ifx_CPU_CPR_L_Bits',0,21,103,3
	.word	47573
	.byte	10
	.byte	'_Ifx_CPU_CPR_U_Bits',0,21,106,16,4,11
	.byte	'reserved_0',0,4
	.word	1158
	.byte	3,29,2,35,0,11
	.byte	'UPPBND',0,4
	.word	1158
	.byte	29,0,2,35,0,0,31
	.byte	'Ifx_CPU_CPR_U_Bits',0,21,110,3
	.word	47666
	.byte	10
	.byte	'_Ifx_CPU_CPU_ID_Bits',0,21,113,16,4,11
	.byte	'MODREV',0,4
	.word	1158
	.byte	8,24,2,35,0,11
	.byte	'MOD_32B',0,4
	.word	1158
	.byte	8,16,2,35,0,11
	.byte	'MOD',0,4
	.word	1158
	.byte	16,0,2,35,0,0,31
	.byte	'Ifx_CPU_CPU_ID_Bits',0,21,118,3
	.word	47759
	.byte	10
	.byte	'_Ifx_CPU_CPXE_Bits',0,21,121,16,4,11
	.byte	'XE',0,4
	.word	1158
	.byte	8,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	1158
	.byte	24,0,2,35,0,0,31
	.byte	'Ifx_CPU_CPXE_Bits',0,21,125,3
	.word	47866
	.byte	10
	.byte	'_Ifx_CPU_CREVT_Bits',0,21,128,1,16,4,11
	.byte	'EVTA',0,4
	.word	1158
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	1158
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	1158
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	1158
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	1158
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	1158
	.byte	24,0,2,35,0,0,31
	.byte	'Ifx_CPU_CREVT_Bits',0,21,136,1,3
	.word	47953
	.byte	10
	.byte	'_Ifx_CPU_CUS_ID_Bits',0,21,139,1,16,4,11
	.byte	'CID',0,4
	.word	1158
	.byte	3,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	1158
	.byte	29,0,2,35,0,0,31
	.byte	'Ifx_CPU_CUS_ID_Bits',0,21,143,1,3
	.word	48107
	.byte	10
	.byte	'_Ifx_CPU_D_Bits',0,21,146,1,16,4,11
	.byte	'DATA',0,4
	.word	1158
	.byte	32,0,2,35,0,0,31
	.byte	'Ifx_CPU_D_Bits',0,21,149,1,3
	.word	48201
	.byte	10
	.byte	'_Ifx_CPU_DATR_Bits',0,21,152,1,16,4,11
	.byte	'reserved_0',0,4
	.word	1158
	.byte	3,29,2,35,0,11
	.byte	'SBE',0,4
	.word	1158
	.byte	1,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	1158
	.byte	5,23,2,35,0,11
	.byte	'CWE',0,4
	.word	1158
	.byte	1,22,2,35,0,11
	.byte	'CFE',0,4
	.word	1158
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	1158
	.byte	3,18,2,35,0,11
	.byte	'SOE',0,4
	.word	1158
	.byte	1,17,2,35,0,11
	.byte	'SME',0,4
	.word	1158
	.byte	1,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	1158
	.byte	16,0,2,35,0,0,31
	.byte	'Ifx_CPU_DATR_Bits',0,21,163,1,3
	.word	48264
	.byte	10
	.byte	'_Ifx_CPU_DBGSR_Bits',0,21,166,1,16,4,11
	.byte	'DE',0,4
	.word	1158
	.byte	1,31,2,35,0,11
	.byte	'HALT',0,4
	.word	1158
	.byte	2,29,2,35,0,11
	.byte	'SIH',0,4
	.word	1158
	.byte	1,28,2,35,0,11
	.byte	'SUSP',0,4
	.word	1158
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	1158
	.byte	1,26,2,35,0,11
	.byte	'PREVSUSP',0,4
	.word	1158
	.byte	1,25,2,35,0,11
	.byte	'PEVT',0,4
	.word	1158
	.byte	1,24,2,35,0,11
	.byte	'EVTSRC',0,4
	.word	1158
	.byte	5,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	1158
	.byte	19,0,2,35,0,0,31
	.byte	'Ifx_CPU_DBGSR_Bits',0,21,177,1,3
	.word	48482
	.byte	10
	.byte	'_Ifx_CPU_DBGTCR_Bits',0,21,180,1,16,4,11
	.byte	'DTA',0,4
	.word	1158
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	1158
	.byte	31,0,2,35,0,0,31
	.byte	'Ifx_CPU_DBGTCR_Bits',0,21,184,1,3
	.word	48697
	.byte	10
	.byte	'_Ifx_CPU_DCON0_Bits',0,21,187,1,16,4,11
	.byte	'reserved_0',0,4
	.word	1158
	.byte	1,31,2,35,0,11
	.byte	'DCBYP',0,4
	.word	1158
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	1158
	.byte	30,0,2,35,0,0,31
	.byte	'Ifx_CPU_DCON0_Bits',0,21,192,1,3
	.word	48791
	.byte	10
	.byte	'_Ifx_CPU_DCON2_Bits',0,21,195,1,16,4,11
	.byte	'DCACHE_SZE',0,4
	.word	1158
	.byte	16,16,2,35,0,11
	.byte	'DSCRATCH_SZE',0,4
	.word	1158
	.byte	16,0,2,35,0,0,31
	.byte	'Ifx_CPU_DCON2_Bits',0,21,199,1,3
	.word	48907
	.byte	10
	.byte	'_Ifx_CPU_DCX_Bits',0,21,202,1,16,4,11
	.byte	'reserved_0',0,4
	.word	1158
	.byte	6,26,2,35,0,11
	.byte	'DCXValue',0,4
	.word	1158
	.byte	26,0,2,35,0,0,31
	.byte	'Ifx_CPU_DCX_Bits',0,21,206,1,3
	.word	49008
	.byte	10
	.byte	'_Ifx_CPU_DEADD_Bits',0,21,209,1,16,4,11
	.byte	'ERROR_ADDRESS',0,4
	.word	1158
	.byte	32,0,2,35,0,0,31
	.byte	'Ifx_CPU_DEADD_Bits',0,21,212,1,3
	.word	49101
	.byte	10
	.byte	'_Ifx_CPU_DIEAR_Bits',0,21,215,1,16,4,11
	.byte	'TA',0,4
	.word	1158
	.byte	32,0,2,35,0,0,31
	.byte	'Ifx_CPU_DIEAR_Bits',0,21,218,1,3
	.word	49181
	.byte	10
	.byte	'_Ifx_CPU_DIETR_Bits',0,21,221,1,16,4,11
	.byte	'IED',0,4
	.word	1158
	.byte	1,31,2,35,0,11
	.byte	'IE_T',0,4
	.word	1158
	.byte	1,30,2,35,0,11
	.byte	'IE_C',0,4
	.word	1158
	.byte	1,29,2,35,0,11
	.byte	'IE_S',0,4
	.word	1158
	.byte	1,28,2,35,0,11
	.byte	'IE_BI',0,4
	.word	1158
	.byte	1,27,2,35,0,11
	.byte	'E_INFO',0,4
	.word	1158
	.byte	6,21,2,35,0,11
	.byte	'IE_DUAL',0,4
	.word	1158
	.byte	1,20,2,35,0,11
	.byte	'IE_SP',0,4
	.word	1158
	.byte	1,19,2,35,0,11
	.byte	'IE_BS',0,4
	.word	1158
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	1158
	.byte	18,0,2,35,0,0,31
	.byte	'Ifx_CPU_DIETR_Bits',0,21,233,1,3
	.word	49250
	.byte	10
	.byte	'_Ifx_CPU_DMS_Bits',0,21,236,1,16,4,11
	.byte	'reserved_0',0,4
	.word	1158
	.byte	1,31,2,35,0,11
	.byte	'DMSValue',0,4
	.word	1158
	.byte	31,0,2,35,0,0,31
	.byte	'Ifx_CPU_DMS_Bits',0,21,240,1,3
	.word	49479
	.byte	10
	.byte	'_Ifx_CPU_DPR_L_Bits',0,21,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	1158
	.byte	3,29,2,35,0,11
	.byte	'LOWBND',0,4
	.word	1158
	.byte	29,0,2,35,0,0,31
	.byte	'Ifx_CPU_DPR_L_Bits',0,21,247,1,3
	.word	49572
	.byte	10
	.byte	'_Ifx_CPU_DPR_U_Bits',0,21,250,1,16,4,11
	.byte	'reserved_0',0,4
	.word	1158
	.byte	3,29,2,35,0,11
	.byte	'UPPBND',0,4
	.word	1158
	.byte	29,0,2,35,0,0,31
	.byte	'Ifx_CPU_DPR_U_Bits',0,21,254,1,3
	.word	49667
	.byte	10
	.byte	'_Ifx_CPU_DPRE_Bits',0,21,129,2,16,4,11
	.byte	'RE',0,4
	.word	1158
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	1158
	.byte	16,0,2,35,0,0,31
	.byte	'Ifx_CPU_DPRE_Bits',0,21,133,2,3
	.word	49762
	.byte	10
	.byte	'_Ifx_CPU_DPWE_Bits',0,21,136,2,16,4,11
	.byte	'WE',0,4
	.word	1158
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	1158
	.byte	16,0,2,35,0,0,31
	.byte	'Ifx_CPU_DPWE_Bits',0,21,140,2,3
	.word	49852
	.byte	10
	.byte	'_Ifx_CPU_DSTR_Bits',0,21,143,2,16,4,11
	.byte	'SRE',0,4
	.word	1158
	.byte	1,31,2,35,0,11
	.byte	'GAE',0,4
	.word	1158
	.byte	1,30,2,35,0,11
	.byte	'LBE',0,4
	.word	1158
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	1158
	.byte	3,26,2,35,0,11
	.byte	'CRE',0,4
	.word	1158
	.byte	1,25,2,35,0,11
	.byte	'reserved_7',0,4
	.word	1158
	.byte	7,18,2,35,0,11
	.byte	'DTME',0,4
	.word	1158
	.byte	1,17,2,35,0,11
	.byte	'LOE',0,4
	.word	1158
	.byte	1,16,2,35,0,11
	.byte	'SDE',0,4
	.word	1158
	.byte	1,15,2,35,0,11
	.byte	'SCE',0,4
	.word	1158
	.byte	1,14,2,35,0,11
	.byte	'CAC',0,4
	.word	1158
	.byte	1,13,2,35,0,11
	.byte	'MPE',0,4
	.word	1158
	.byte	1,12,2,35,0,11
	.byte	'CLE',0,4
	.word	1158
	.byte	1,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	1158
	.byte	3,8,2,35,0,11
	.byte	'ALN',0,4
	.word	1158
	.byte	1,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	1158
	.byte	7,0,2,35,0,0,31
	.byte	'Ifx_CPU_DSTR_Bits',0,21,161,2,3
	.word	49942
	.byte	10
	.byte	'_Ifx_CPU_EXEVT_Bits',0,21,164,2,16,4,11
	.byte	'EVTA',0,4
	.word	1158
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	1158
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	1158
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	1158
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	1158
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	1158
	.byte	24,0,2,35,0,0,31
	.byte	'Ifx_CPU_EXEVT_Bits',0,21,172,2,3
	.word	50266
	.byte	10
	.byte	'_Ifx_CPU_FCX_Bits',0,21,175,2,16,4,11
	.byte	'FCXO',0,4
	.word	1158
	.byte	16,16,2,35,0,11
	.byte	'FCXS',0,4
	.word	1158
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	1158
	.byte	12,0,2,35,0,0,31
	.byte	'Ifx_CPU_FCX_Bits',0,21,180,2,3
	.word	50420
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_CON_Bits',0,21,183,2,16,4,11
	.byte	'TST',0,4
	.word	1158
	.byte	1,31,2,35,0,11
	.byte	'TCL',0,4
	.word	1158
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	1158
	.byte	6,24,2,35,0,11
	.byte	'RM',0,4
	.word	1158
	.byte	2,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	1158
	.byte	8,14,2,35,0,11
	.byte	'FXE',0,4
	.word	1158
	.byte	1,13,2,35,0,11
	.byte	'FUE',0,4
	.word	1158
	.byte	1,12,2,35,0,11
	.byte	'FZE',0,4
	.word	1158
	.byte	1,11,2,35,0,11
	.byte	'FVE',0,4
	.word	1158
	.byte	1,10,2,35,0,11
	.byte	'FIE',0,4
	.word	1158
	.byte	1,9,2,35,0,11
	.byte	'reserved_23',0,4
	.word	1158
	.byte	3,6,2,35,0,11
	.byte	'FX',0,4
	.word	1158
	.byte	1,5,2,35,0,11
	.byte	'FU',0,4
	.word	1158
	.byte	1,4,2,35,0,11
	.byte	'FZ',0,4
	.word	1158
	.byte	1,3,2,35,0,11
	.byte	'FV',0,4
	.word	1158
	.byte	1,2,2,35,0,11
	.byte	'FI',0,4
	.word	1158
	.byte	1,1,2,35,0,11
	.byte	'reserved_31',0,4
	.word	1158
	.byte	1,0,2,35,0,0,31
	.byte	'Ifx_CPU_FPU_TRAP_CON_Bits',0,21,202,2,3
	.word	50526
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_OPC_Bits',0,21,205,2,16,4,11
	.byte	'OPC',0,4
	.word	1158
	.byte	8,24,2,35,0,11
	.byte	'FMT',0,4
	.word	1158
	.byte	1,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	1158
	.byte	7,16,2,35,0,11
	.byte	'DREG',0,4
	.word	1158
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	1158
	.byte	12,0,2,35,0,0,31
	.byte	'Ifx_CPU_FPU_TRAP_OPC_Bits',0,21,212,2,3
	.word	50875
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_PC_Bits',0,21,215,2,16,4,11
	.byte	'PC',0,4
	.word	1158
	.byte	32,0,2,35,0,0,31
	.byte	'Ifx_CPU_FPU_TRAP_PC_Bits',0,21,218,2,3
	.word	51035
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC1_Bits',0,21,221,2,16,4,11
	.byte	'SRC1',0,4
	.word	1158
	.byte	32,0,2,35,0,0,31
	.byte	'Ifx_CPU_FPU_TRAP_SRC1_Bits',0,21,224,2,3
	.word	51116
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC2_Bits',0,21,227,2,16,4,11
	.byte	'SRC2',0,4
	.word	1158
	.byte	32,0,2,35,0,0,31
	.byte	'Ifx_CPU_FPU_TRAP_SRC2_Bits',0,21,230,2,3
	.word	51203
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC3_Bits',0,21,233,2,16,4,11
	.byte	'SRC3',0,4
	.word	1158
	.byte	32,0,2,35,0,0,31
	.byte	'Ifx_CPU_FPU_TRAP_SRC3_Bits',0,21,236,2,3
	.word	51290
	.byte	10
	.byte	'_Ifx_CPU_ICNT_Bits',0,21,239,2,16,4,11
	.byte	'CountValue',0,4
	.word	1158
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	1158
	.byte	1,0,2,35,0,0,31
	.byte	'Ifx_CPU_ICNT_Bits',0,21,243,2,3
	.word	51377
	.byte	31
	.byte	'Ifx_CPU_ICR_Bits',0,21,253,2,3
	.word	40112
	.byte	10
	.byte	'_Ifx_CPU_ISP_Bits',0,21,128,3,16,4,11
	.byte	'ISP',0,4
	.word	1158
	.byte	32,0,2,35,0,0,31
	.byte	'Ifx_CPU_ISP_Bits',0,21,131,3,3
	.word	51494
	.byte	10
	.byte	'_Ifx_CPU_LCX_Bits',0,21,134,3,16,4,11
	.byte	'LCXO',0,4
	.word	1158
	.byte	16,16,2,35,0,11
	.byte	'LCXS',0,4
	.word	1158
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	1158
	.byte	12,0,2,35,0,0,31
	.byte	'Ifx_CPU_LCX_Bits',0,21,139,3,3
	.word	51560
	.byte	10
	.byte	'_Ifx_CPU_M1CNT_Bits',0,21,142,3,16,4,11
	.byte	'CountValue',0,4
	.word	1158
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	1158
	.byte	1,0,2,35,0,0,31
	.byte	'Ifx_CPU_M1CNT_Bits',0,21,146,3,3
	.word	51666
	.byte	10
	.byte	'_Ifx_CPU_M2CNT_Bits',0,21,149,3,16,4,11
	.byte	'CountValue',0,4
	.word	1158
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	1158
	.byte	1,0,2,35,0,0,31
	.byte	'Ifx_CPU_M2CNT_Bits',0,21,153,3,3
	.word	51759
	.byte	10
	.byte	'_Ifx_CPU_M3CNT_Bits',0,21,156,3,16,4,11
	.byte	'CountValue',0,4
	.word	1158
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	1158
	.byte	1,0,2,35,0,0,31
	.byte	'Ifx_CPU_M3CNT_Bits',0,21,160,3,3
	.word	51852
	.byte	10
	.byte	'_Ifx_CPU_PC_Bits',0,21,163,3,16,4,11
	.byte	'reserved_0',0,4
	.word	1158
	.byte	1,31,2,35,0,11
	.byte	'PC',0,4
	.word	1158
	.byte	31,0,2,35,0,0,31
	.byte	'Ifx_CPU_PC_Bits',0,21,167,3,3
	.word	51945
	.byte	10
	.byte	'_Ifx_CPU_PCON0_Bits',0,21,170,3,16,4,11
	.byte	'reserved_0',0,4
	.word	1158
	.byte	1,31,2,35,0,11
	.byte	'PCBYP',0,4
	.word	1158
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	1158
	.byte	30,0,2,35,0,0,31
	.byte	'Ifx_CPU_PCON0_Bits',0,21,175,3,3
	.word	52030
	.byte	10
	.byte	'_Ifx_CPU_PCON1_Bits',0,21,178,3,16,4,11
	.byte	'PCINV',0,4
	.word	1158
	.byte	1,31,2,35,0,11
	.byte	'PBINV',0,4
	.word	1158
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	1158
	.byte	30,0,2,35,0,0,31
	.byte	'Ifx_CPU_PCON1_Bits',0,21,183,3,3
	.word	52146
	.byte	10
	.byte	'_Ifx_CPU_PCON2_Bits',0,21,186,3,16,4,11
	.byte	'PCACHE_SZE',0,4
	.word	1158
	.byte	16,16,2,35,0,11
	.byte	'PSCRATCH_SZE',0,4
	.word	1158
	.byte	16,0,2,35,0,0,31
	.byte	'Ifx_CPU_PCON2_Bits',0,21,190,3,3
	.word	52257
	.byte	10
	.byte	'_Ifx_CPU_PCXI_Bits',0,21,193,3,16,4,11
	.byte	'PCXO',0,4
	.word	1158
	.byte	16,16,2,35,0,11
	.byte	'PCXS',0,4
	.word	1158
	.byte	4,12,2,35,0,11
	.byte	'UL',0,4
	.word	1158
	.byte	1,11,2,35,0,11
	.byte	'PIE',0,4
	.word	1158
	.byte	1,10,2,35,0,11
	.byte	'PCPN',0,4
	.word	1158
	.byte	10,0,2,35,0,0,31
	.byte	'Ifx_CPU_PCXI_Bits',0,21,200,3,3
	.word	52358
	.byte	10
	.byte	'_Ifx_CPU_PIEAR_Bits',0,21,203,3,16,4,11
	.byte	'TA',0,4
	.word	1158
	.byte	32,0,2,35,0,0,31
	.byte	'Ifx_CPU_PIEAR_Bits',0,21,206,3,3
	.word	52488
	.byte	10
	.byte	'_Ifx_CPU_PIETR_Bits',0,21,209,3,16,4,11
	.byte	'IED',0,4
	.word	1158
	.byte	1,31,2,35,0,11
	.byte	'IE_T',0,4
	.word	1158
	.byte	1,30,2,35,0,11
	.byte	'IE_C',0,4
	.word	1158
	.byte	1,29,2,35,0,11
	.byte	'IE_S',0,4
	.word	1158
	.byte	1,28,2,35,0,11
	.byte	'IE_BI',0,4
	.word	1158
	.byte	1,27,2,35,0,11
	.byte	'E_INFO',0,4
	.word	1158
	.byte	6,21,2,35,0,11
	.byte	'IE_DUAL',0,4
	.word	1158
	.byte	1,20,2,35,0,11
	.byte	'IE_SP',0,4
	.word	1158
	.byte	1,19,2,35,0,11
	.byte	'IE_BS',0,4
	.word	1158
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	1158
	.byte	18,0,2,35,0,0,31
	.byte	'Ifx_CPU_PIETR_Bits',0,21,221,3,3
	.word	52557
	.byte	10
	.byte	'_Ifx_CPU_PMA0_Bits',0,21,224,3,16,4,11
	.byte	'reserved_0',0,4
	.word	1158
	.byte	13,19,2,35,0,11
	.byte	'DAC',0,4
	.word	1158
	.byte	3,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	1158
	.byte	16,0,2,35,0,0,31
	.byte	'Ifx_CPU_PMA0_Bits',0,21,229,3,3
	.word	52786
	.byte	10
	.byte	'_Ifx_CPU_PMA1_Bits',0,21,232,3,16,4,11
	.byte	'reserved_0',0,4
	.word	1158
	.byte	14,18,2,35,0,11
	.byte	'CAC',0,4
	.word	1158
	.byte	2,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	1158
	.byte	16,0,2,35,0,0,31
	.byte	'Ifx_CPU_PMA1_Bits',0,21,237,3,3
	.word	52899
	.byte	10
	.byte	'_Ifx_CPU_PMA2_Bits',0,21,240,3,16,4,11
	.byte	'PSI',0,4
	.word	1158
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	1158
	.byte	16,0,2,35,0,0,31
	.byte	'Ifx_CPU_PMA2_Bits',0,21,244,3,3
	.word	53012
	.byte	10
	.byte	'_Ifx_CPU_PSTR_Bits',0,21,247,3,16,4,11
	.byte	'FRE',0,4
	.word	1158
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	1158
	.byte	1,30,2,35,0,11
	.byte	'FBE',0,4
	.word	1158
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	1158
	.byte	9,20,2,35,0,11
	.byte	'FPE',0,4
	.word	1158
	.byte	1,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	1158
	.byte	1,18,2,35,0,11
	.byte	'FME',0,4
	.word	1158
	.byte	1,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	1158
	.byte	17,0,2,35,0,0,31
	.byte	'Ifx_CPU_PSTR_Bits',0,21,129,4,3
	.word	53103
	.byte	10
	.byte	'_Ifx_CPU_PSW_Bits',0,21,132,4,16,4,11
	.byte	'CDC',0,4
	.word	1158
	.byte	7,25,2,35,0,11
	.byte	'CDE',0,4
	.word	1158
	.byte	1,24,2,35,0,11
	.byte	'GW',0,4
	.word	1158
	.byte	1,23,2,35,0,11
	.byte	'IS',0,4
	.word	1158
	.byte	1,22,2,35,0,11
	.byte	'IO',0,4
	.word	1158
	.byte	2,20,2,35,0,11
	.byte	'PRS',0,4
	.word	1158
	.byte	2,18,2,35,0,11
	.byte	'S',0,4
	.word	1158
	.byte	1,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	1158
	.byte	12,5,2,35,0,11
	.byte	'SAV',0,4
	.word	1158
	.byte	1,4,2,35,0,11
	.byte	'AV',0,4
	.word	1158
	.byte	1,3,2,35,0,11
	.byte	'SV',0,4
	.word	1158
	.byte	1,2,2,35,0,11
	.byte	'V',0,4
	.word	1158
	.byte	1,1,2,35,0,11
	.byte	'C',0,4
	.word	1158
	.byte	1,0,2,35,0,0,31
	.byte	'Ifx_CPU_PSW_Bits',0,21,147,4,3
	.word	53306
	.byte	10
	.byte	'_Ifx_CPU_SEGEN_Bits',0,21,150,4,16,4,11
	.byte	'ADFLIP',0,4
	.word	1158
	.byte	8,24,2,35,0,11
	.byte	'ADTYPE',0,4
	.word	1158
	.byte	2,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	1158
	.byte	21,1,2,35,0,11
	.byte	'AE',0,4
	.word	1158
	.byte	1,0,2,35,0,0,31
	.byte	'Ifx_CPU_SEGEN_Bits',0,21,156,4,3
	.word	53549
	.byte	10
	.byte	'_Ifx_CPU_SMACON_Bits',0,21,159,4,16,4,11
	.byte	'PC',0,4
	.word	1158
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	1158
	.byte	1,30,2,35,0,11
	.byte	'PT',0,4
	.word	1158
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	1158
	.byte	5,24,2,35,0,11
	.byte	'DC',0,4
	.word	1158
	.byte	1,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	1158
	.byte	1,22,2,35,0,11
	.byte	'DT',0,4
	.word	1158
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	1158
	.byte	13,8,2,35,0,11
	.byte	'IODT',0,4
	.word	1158
	.byte	1,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	1158
	.byte	7,0,2,35,0,0,31
	.byte	'Ifx_CPU_SMACON_Bits',0,21,171,4,3
	.word	53677
	.byte	10
	.byte	'_Ifx_CPU_SPROT_ACCENA_Bits',0,21,174,4,16,4,11
	.byte	'EN',0,4
	.word	466
	.byte	32,0,2,35,0,0,31
	.byte	'Ifx_CPU_SPROT_ACCENA_Bits',0,21,177,4,3
	.word	53918
	.byte	10
	.byte	'_Ifx_CPU_SPROT_ACCENB_Bits',0,21,180,4,16,4,11
	.byte	'reserved_0',0,4
	.word	466
	.byte	32,0,2,35,0,0,31
	.byte	'Ifx_CPU_SPROT_ACCENB_Bits',0,21,183,4,3
	.word	54001
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,21,186,4,16,4,11
	.byte	'EN',0,4
	.word	466
	.byte	32,0,2,35,0,0,31
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,21,189,4,3
	.word	54092
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,21,192,4,16,4,11
	.byte	'reserved_0',0,4
	.word	466
	.byte	32,0,2,35,0,0,31
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,21,195,4,3
	.word	54183
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_LA_Bits',0,21,198,4,16,4,11
	.byte	'reserved_0',0,1
	.word	489
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	466
	.byte	27,0,2,35,0,0,31
	.byte	'Ifx_CPU_SPROT_RGN_LA_Bits',0,21,202,4,3
	.word	54282
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_UA_Bits',0,21,205,4,16,4,11
	.byte	'reserved_0',0,1
	.word	489
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	466
	.byte	27,0,2,35,0,0,31
	.byte	'Ifx_CPU_SPROT_RGN_UA_Bits',0,21,209,4,3
	.word	54389
	.byte	10
	.byte	'_Ifx_CPU_SWEVT_Bits',0,21,212,4,16,4,11
	.byte	'EVTA',0,4
	.word	1158
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	1158
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	1158
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	1158
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	1158
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	1158
	.byte	24,0,2,35,0,0,31
	.byte	'Ifx_CPU_SWEVT_Bits',0,21,220,4,3
	.word	54496
	.byte	10
	.byte	'_Ifx_CPU_SYSCON_Bits',0,21,223,4,16,4,11
	.byte	'FCDSF',0,4
	.word	1158
	.byte	1,31,2,35,0,11
	.byte	'PROTEN',0,4
	.word	1158
	.byte	1,30,2,35,0,11
	.byte	'TPROTEN',0,4
	.word	1158
	.byte	1,29,2,35,0,11
	.byte	'IS',0,4
	.word	1158
	.byte	1,28,2,35,0,11
	.byte	'IT',0,4
	.word	1158
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	1158
	.byte	27,0,2,35,0,0,31
	.byte	'Ifx_CPU_SYSCON_Bits',0,21,231,4,3
	.word	54650
	.byte	10
	.byte	'_Ifx_CPU_TASK_ASI_Bits',0,21,234,4,16,4,11
	.byte	'ASI',0,4
	.word	1158
	.byte	5,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	1158
	.byte	27,0,2,35,0,0,31
	.byte	'Ifx_CPU_TASK_ASI_Bits',0,21,238,4,3
	.word	54811
	.byte	10
	.byte	'_Ifx_CPU_TPS_CON_Bits',0,21,241,4,16,4,11
	.byte	'TEXP0',0,4
	.word	1158
	.byte	1,31,2,35,0,11
	.byte	'TEXP1',0,4
	.word	1158
	.byte	1,30,2,35,0,11
	.byte	'TEXP2',0,4
	.word	1158
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	1158
	.byte	13,16,2,35,0,11
	.byte	'TTRAP',0,4
	.word	1158
	.byte	1,15,2,35,0,11
	.byte	'reserved_17',0,4
	.word	1158
	.byte	15,0,2,35,0,0,31
	.byte	'Ifx_CPU_TPS_CON_Bits',0,21,249,4,3
	.word	54909
	.byte	10
	.byte	'_Ifx_CPU_TPS_TIMER_Bits',0,21,252,4,16,4,11
	.byte	'Timer',0,4
	.word	1158
	.byte	32,0,2,35,0,0,31
	.byte	'Ifx_CPU_TPS_TIMER_Bits',0,21,255,4,3
	.word	55081
	.byte	10
	.byte	'_Ifx_CPU_TR_ADR_Bits',0,21,130,5,16,4,11
	.byte	'ADDR',0,4
	.word	1158
	.byte	32,0,2,35,0,0,31
	.byte	'Ifx_CPU_TR_ADR_Bits',0,21,133,5,3
	.word	55161
	.byte	10
	.byte	'_Ifx_CPU_TR_EVT_Bits',0,21,136,5,16,4,11
	.byte	'EVTA',0,4
	.word	1158
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	1158
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	1158
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	1158
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	1158
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	1158
	.byte	4,20,2,35,0,11
	.byte	'TYP',0,4
	.word	1158
	.byte	1,19,2,35,0,11
	.byte	'RNG',0,4
	.word	1158
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	1158
	.byte	1,17,2,35,0,11
	.byte	'ASI_EN',0,4
	.word	1158
	.byte	1,16,2,35,0,11
	.byte	'ASI',0,4
	.word	1158
	.byte	5,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	1158
	.byte	6,5,2,35,0,11
	.byte	'AST',0,4
	.word	1158
	.byte	1,4,2,35,0,11
	.byte	'ALD',0,4
	.word	1158
	.byte	1,3,2,35,0,11
	.byte	'reserved_29',0,4
	.word	1158
	.byte	3,0,2,35,0,0,31
	.byte	'Ifx_CPU_TR_EVT_Bits',0,21,153,5,3
	.word	55234
	.byte	10
	.byte	'_Ifx_CPU_TRIG_ACC_Bits',0,21,156,5,16,4,11
	.byte	'T0',0,4
	.word	1158
	.byte	1,31,2,35,0,11
	.byte	'T1',0,4
	.word	1158
	.byte	1,30,2,35,0,11
	.byte	'T2',0,4
	.word	1158
	.byte	1,29,2,35,0,11
	.byte	'T3',0,4
	.word	1158
	.byte	1,28,2,35,0,11
	.byte	'T4',0,4
	.word	1158
	.byte	1,27,2,35,0,11
	.byte	'T5',0,4
	.word	1158
	.byte	1,26,2,35,0,11
	.byte	'T6',0,4
	.word	1158
	.byte	1,25,2,35,0,11
	.byte	'T7',0,4
	.word	1158
	.byte	1,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	1158
	.byte	24,0,2,35,0,0,31
	.byte	'Ifx_CPU_TRIG_ACC_Bits',0,21,167,5,3
	.word	55552
	.byte	12,21,175,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46887
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_A',0,21,180,5,3
	.word	55747
	.byte	12,21,183,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46948
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_BIV',0,21,188,5,3
	.word	55806
	.byte	12,21,191,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47027
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_BTV',0,21,196,5,3
	.word	55867
	.byte	12,21,199,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47113
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_CCNT',0,21,204,5,3
	.word	55928
	.byte	12,21,207,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47202
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_CCTRL',0,21,212,5,3
	.word	55990
	.byte	12,21,215,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47348
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_COMPAT',0,21,220,5,3
	.word	56053
	.byte	12,21,223,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47475
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_CORE_ID',0,21,228,5,3
	.word	56117
	.byte	12,21,231,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47573
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_CPR_L',0,21,236,5,3
	.word	56182
	.byte	12,21,239,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47666
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_CPR_U',0,21,244,5,3
	.word	56245
	.byte	12,21,247,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47759
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_CPU_ID',0,21,252,5,3
	.word	56308
	.byte	12,21,255,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47866
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_CPXE',0,21,132,6,3
	.word	56372
	.byte	12,21,135,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47953
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_CREVT',0,21,140,6,3
	.word	56434
	.byte	12,21,143,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48107
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_CUS_ID',0,21,148,6,3
	.word	56497
	.byte	12,21,151,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48201
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_D',0,21,156,6,3
	.word	56561
	.byte	12,21,159,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48264
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_DATR',0,21,164,6,3
	.word	56620
	.byte	12,21,167,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48482
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_DBGSR',0,21,172,6,3
	.word	56682
	.byte	12,21,175,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48697
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_DBGTCR',0,21,180,6,3
	.word	56745
	.byte	12,21,183,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48791
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_DCON0',0,21,188,6,3
	.word	56809
	.byte	12,21,191,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48907
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_DCON2',0,21,196,6,3
	.word	56872
	.byte	12,21,199,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49008
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_DCX',0,21,204,6,3
	.word	56935
	.byte	12,21,207,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49101
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_DEADD',0,21,212,6,3
	.word	56996
	.byte	12,21,215,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49181
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_DIEAR',0,21,220,6,3
	.word	57059
	.byte	12,21,223,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49250
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_DIETR',0,21,228,6,3
	.word	57122
	.byte	12,21,231,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49479
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_DMS',0,21,236,6,3
	.word	57185
	.byte	12,21,239,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49572
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_DPR_L',0,21,244,6,3
	.word	57246
	.byte	12,21,247,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49667
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_DPR_U',0,21,252,6,3
	.word	57309
	.byte	12,21,255,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49762
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_DPRE',0,21,132,7,3
	.word	57372
	.byte	12,21,135,7,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49852
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_DPWE',0,21,140,7,3
	.word	57434
	.byte	12,21,143,7,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49942
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_DSTR',0,21,148,7,3
	.word	57496
	.byte	12,21,151,7,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50266
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_EXEVT',0,21,156,7,3
	.word	57558
	.byte	12,21,159,7,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50420
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_FCX',0,21,164,7,3
	.word	57621
	.byte	12,21,167,7,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50526
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_FPU_TRAP_CON',0,21,172,7,3
	.word	57682
	.byte	12,21,175,7,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50875
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_FPU_TRAP_OPC',0,21,180,7,3
	.word	57752
	.byte	12,21,183,7,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51035
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_FPU_TRAP_PC',0,21,188,7,3
	.word	57822
	.byte	12,21,191,7,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51116
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_FPU_TRAP_SRC1',0,21,196,7,3
	.word	57891
	.byte	12,21,199,7,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51203
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_FPU_TRAP_SRC2',0,21,204,7,3
	.word	57962
	.byte	12,21,207,7,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51290
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_FPU_TRAP_SRC3',0,21,212,7,3
	.word	58033
	.byte	12,21,215,7,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51377
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_ICNT',0,21,220,7,3
	.word	58104
	.byte	31
	.byte	'Ifx_CPU_ICR',0,21,228,7,3
	.word	40229
	.byte	12,21,231,7,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51494
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_ISP',0,21,236,7,3
	.word	58187
	.byte	12,21,239,7,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51560
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_LCX',0,21,244,7,3
	.word	58248
	.byte	12,21,247,7,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51666
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_M1CNT',0,21,252,7,3
	.word	58309
	.byte	12,21,255,7,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51759
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_M2CNT',0,21,132,8,3
	.word	58372
	.byte	12,21,135,8,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51852
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_M3CNT',0,21,140,8,3
	.word	58435
	.byte	12,21,143,8,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51945
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_PC',0,21,148,8,3
	.word	58498
	.byte	12,21,151,8,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52030
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_PCON0',0,21,156,8,3
	.word	58558
	.byte	12,21,159,8,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52146
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_PCON1',0,21,164,8,3
	.word	58621
	.byte	12,21,167,8,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52257
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_PCON2',0,21,172,8,3
	.word	58684
	.byte	12,21,175,8,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52358
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_PCXI',0,21,180,8,3
	.word	58747
	.byte	12,21,183,8,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52488
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_PIEAR',0,21,188,8,3
	.word	58809
	.byte	12,21,191,8,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52557
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_PIETR',0,21,196,8,3
	.word	58872
	.byte	12,21,199,8,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52786
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_PMA0',0,21,204,8,3
	.word	58935
	.byte	12,21,207,8,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52899
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_PMA1',0,21,212,8,3
	.word	58997
	.byte	12,21,215,8,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53012
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_PMA2',0,21,220,8,3
	.word	59059
	.byte	12,21,223,8,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53103
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_PSTR',0,21,228,8,3
	.word	59121
	.byte	12,21,231,8,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53306
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_PSW',0,21,236,8,3
	.word	59183
	.byte	12,21,239,8,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53549
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_SEGEN',0,21,244,8,3
	.word	59244
	.byte	12,21,247,8,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53677
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_SMACON',0,21,252,8,3
	.word	59307
	.byte	12,21,255,8,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53918
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_SPROT_ACCENA',0,21,132,9,3
	.word	59371
	.byte	12,21,135,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54001
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_SPROT_ACCENB',0,21,140,9,3
	.word	59441
	.byte	12,21,143,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54092
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA',0,21,148,9,3
	.word	59511
	.byte	12,21,151,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54183
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB',0,21,156,9,3
	.word	59585
	.byte	12,21,159,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54282
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_SPROT_RGN_LA',0,21,164,9,3
	.word	59659
	.byte	12,21,167,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54389
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_SPROT_RGN_UA',0,21,172,9,3
	.word	59729
	.byte	12,21,175,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54496
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_SWEVT',0,21,180,9,3
	.word	59799
	.byte	12,21,183,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54650
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_SYSCON',0,21,188,9,3
	.word	59862
	.byte	12,21,191,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54811
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_TASK_ASI',0,21,196,9,3
	.word	59926
	.byte	12,21,199,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54909
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_TPS_CON',0,21,204,9,3
	.word	59992
	.byte	12,21,207,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55081
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_TPS_TIMER',0,21,212,9,3
	.word	60057
	.byte	12,21,215,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55161
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_TR_ADR',0,21,220,9,3
	.word	60124
	.byte	12,21,223,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55234
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_TR_EVT',0,21,228,9,3
	.word	60188
	.byte	12,21,231,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55552
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_TRIG_ACC',0,21,236,9,3
	.word	60252
	.byte	10
	.byte	'_Ifx_CPU_CPR',0,21,247,9,25,8,13
	.byte	'L',0
	.word	56182
	.byte	4,2,35,0,13
	.byte	'U',0
	.word	56245
	.byte	4,2,35,4,0,14
	.word	60318
	.byte	31
	.byte	'Ifx_CPU_CPR',0,21,251,9,3
	.word	60360
	.byte	10
	.byte	'_Ifx_CPU_DPR',0,21,254,9,25,8,13
	.byte	'L',0
	.word	57246
	.byte	4,2,35,0,13
	.byte	'U',0
	.word	57309
	.byte	4,2,35,4,0,14
	.word	60386
	.byte	31
	.byte	'Ifx_CPU_DPR',0,21,130,10,3
	.word	60428
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN',0,21,133,10,25,16,13
	.byte	'LA',0
	.word	59659
	.byte	4,2,35,0,13
	.byte	'UA',0
	.word	59729
	.byte	4,2,35,4,13
	.byte	'ACCENA',0
	.word	59511
	.byte	4,2,35,8,13
	.byte	'ACCENB',0
	.word	59585
	.byte	4,2,35,12,0,14
	.word	60454
	.byte	31
	.byte	'Ifx_CPU_SPROT_RGN',0,21,139,10,3
	.word	60536
	.byte	18,12
	.word	60057
	.byte	19,2,0,10
	.byte	'_Ifx_CPU_TPS',0,21,142,10,25,16,13
	.byte	'CON',0
	.word	59992
	.byte	4,2,35,0,13
	.byte	'TIMER',0
	.word	60568
	.byte	12,2,35,4,0,14
	.word	60577
	.byte	31
	.byte	'Ifx_CPU_TPS',0,21,146,10,3
	.word	60625
	.byte	10
	.byte	'_Ifx_CPU_TR',0,21,149,10,25,8,13
	.byte	'EVT',0
	.word	60188
	.byte	4,2,35,0,13
	.byte	'ADR',0
	.word	60124
	.byte	4,2,35,4,0,14
	.word	60651
	.byte	31
	.byte	'Ifx_CPU_TR',0,21,153,10,3
	.word	60696
	.byte	18,176,32
	.word	489
	.byte	19,175,32,0,18,208,223,1
	.word	489
	.byte	19,207,223,1,0,18,248,1
	.word	489
	.byte	19,247,1,0,18,244,29
	.word	489
	.byte	19,243,29,0,18,188,3
	.word	489
	.byte	19,187,3,0,18,232,3
	.word	489
	.byte	19,231,3,0,18,252,23
	.word	489
	.byte	19,251,23,0,18,228,63
	.word	489
	.byte	19,227,63,0,18,128,1
	.word	60386
	.byte	19,15,0,14
	.word	60811
	.byte	18,128,31
	.word	489
	.byte	19,255,30,0,18,64
	.word	60318
	.byte	19,7,0,14
	.word	60837
	.byte	18,192,31
	.word	489
	.byte	19,191,31,0,18,16
	.word	56372
	.byte	19,3,0,18,16
	.word	57372
	.byte	19,3,0,18,16
	.word	57434
	.byte	19,3,0,18,208,7
	.word	489
	.byte	19,207,7,0,14
	.word	60577
	.byte	18,240,23
	.word	489
	.byte	19,239,23,0,18,64
	.word	60651
	.byte	19,7,0,14
	.word	60916
	.byte	18,192,23
	.word	489
	.byte	19,191,23,0,18,232,1
	.word	489
	.byte	19,231,1,0,18,180,1
	.word	489
	.byte	19,179,1,0,18,172,1
	.word	489
	.byte	19,171,1,0,18,64
	.word	56561
	.byte	19,15,0,18,64
	.word	489
	.byte	19,63,0,18,64
	.word	55747
	.byte	19,15,0,10
	.byte	'_Ifx_CPU',0,21,166,10,25,128,128,4,13
	.byte	'reserved_0',0
	.word	60721
	.byte	176,32,2,35,0,13
	.byte	'SEGEN',0
	.word	59244
	.byte	4,3,35,176,32,13
	.byte	'reserved_1034',0
	.word	60732
	.byte	208,223,1,3,35,180,32,13
	.byte	'TASK_ASI',0
	.word	59926
	.byte	4,4,35,132,128,2,13
	.byte	'reserved_8008',0
	.word	60745
	.byte	248,1,4,35,136,128,2,13
	.byte	'PMA0',0
	.word	58935
	.byte	4,4,35,128,130,2,13
	.byte	'PMA1',0
	.word	58997
	.byte	4,4,35,132,130,2,13
	.byte	'PMA2',0
	.word	59059
	.byte	4,4,35,136,130,2,13
	.byte	'reserved_810C',0
	.word	60756
	.byte	244,29,4,35,140,130,2,13
	.byte	'DCON2',0
	.word	56872
	.byte	4,4,35,128,160,2,13
	.byte	'reserved_9004',0
	.word	4908
	.byte	8,4,35,132,160,2,13
	.byte	'SMACON',0
	.word	59307
	.byte	4,4,35,140,160,2,13
	.byte	'DSTR',0
	.word	57496
	.byte	4,4,35,144,160,2,13
	.byte	'reserved_9014',0
	.word	3089
	.byte	4,4,35,148,160,2,13
	.byte	'DATR',0
	.word	56620
	.byte	4,4,35,152,160,2,13
	.byte	'DEADD',0
	.word	56996
	.byte	4,4,35,156,160,2,13
	.byte	'DIEAR',0
	.word	57059
	.byte	4,4,35,160,160,2,13
	.byte	'DIETR',0
	.word	57122
	.byte	4,4,35,164,160,2,13
	.byte	'reserved_9028',0
	.word	4279
	.byte	24,4,35,168,160,2,13
	.byte	'DCON0',0
	.word	56809
	.byte	4,4,35,192,160,2,13
	.byte	'reserved_9044',0
	.word	60767
	.byte	188,3,4,35,196,160,2,13
	.byte	'PSTR',0
	.word	59121
	.byte	4,4,35,128,164,2,13
	.byte	'PCON1',0
	.word	58621
	.byte	4,4,35,132,164,2,13
	.byte	'PCON2',0
	.word	58684
	.byte	4,4,35,136,164,2,13
	.byte	'PCON0',0
	.word	58558
	.byte	4,4,35,140,164,2,13
	.byte	'PIEAR',0
	.word	58809
	.byte	4,4,35,144,164,2,13
	.byte	'PIETR',0
	.word	58872
	.byte	4,4,35,148,164,2,13
	.byte	'reserved_9218',0
	.word	60778
	.byte	232,3,4,35,152,164,2,13
	.byte	'COMPAT',0
	.word	56053
	.byte	4,4,35,128,168,2,13
	.byte	'reserved_9404',0
	.word	60789
	.byte	252,23,4,35,132,168,2,13
	.byte	'FPU_TRAP_CON',0
	.word	57682
	.byte	4,4,35,128,192,2,13
	.byte	'FPU_TRAP_PC',0
	.word	57822
	.byte	4,4,35,132,192,2,13
	.byte	'FPU_TRAP_OPC',0
	.word	57752
	.byte	4,4,35,136,192,2,13
	.byte	'reserved_A00C',0
	.word	3089
	.byte	4,4,35,140,192,2,13
	.byte	'FPU_TRAP_SRC1',0
	.word	57891
	.byte	4,4,35,144,192,2,13
	.byte	'FPU_TRAP_SRC2',0
	.word	57962
	.byte	4,4,35,148,192,2,13
	.byte	'FPU_TRAP_SRC3',0
	.word	58033
	.byte	4,4,35,152,192,2,13
	.byte	'reserved_A01C',0
	.word	60800
	.byte	228,63,4,35,156,192,2,13
	.byte	'DPR',0
	.word	60821
	.byte	128,1,4,35,128,128,3,13
	.byte	'reserved_C080',0
	.word	60826
	.byte	128,31,4,35,128,129,3,13
	.byte	'CPR',0
	.word	60846
	.byte	64,4,35,128,160,3,13
	.byte	'reserved_D040',0
	.word	60851
	.byte	192,31,4,35,192,160,3,13
	.byte	'CPXE',0
	.word	60862
	.byte	16,4,35,128,192,3,13
	.byte	'DPRE',0
	.word	60871
	.byte	16,4,35,144,192,3,13
	.byte	'DPWE',0
	.word	60880
	.byte	16,4,35,160,192,3,13
	.byte	'reserved_E030',0
	.word	60889
	.byte	208,7,4,35,176,192,3,13
	.byte	'TPS',0
	.word	60900
	.byte	16,4,35,128,200,3,13
	.byte	'reserved_E410',0
	.word	60905
	.byte	240,23,4,35,144,200,3,13
	.byte	'TR',0
	.word	60925
	.byte	64,4,35,128,224,3,13
	.byte	'reserved_F040',0
	.word	60930
	.byte	192,23,4,35,192,224,3,13
	.byte	'CCTRL',0
	.word	55990
	.byte	4,4,35,128,248,3,13
	.byte	'CCNT',0
	.word	55928
	.byte	4,4,35,132,248,3,13
	.byte	'ICNT',0
	.word	58104
	.byte	4,4,35,136,248,3,13
	.byte	'M1CNT',0
	.word	58309
	.byte	4,4,35,140,248,3,13
	.byte	'M2CNT',0
	.word	58372
	.byte	4,4,35,144,248,3,13
	.byte	'M3CNT',0
	.word	58435
	.byte	4,4,35,148,248,3,13
	.byte	'reserved_FC18',0
	.word	60941
	.byte	232,1,4,35,152,248,3,13
	.byte	'DBGSR',0
	.word	56682
	.byte	4,4,35,128,250,3,13
	.byte	'reserved_FD04',0
	.word	3089
	.byte	4,4,35,132,250,3,13
	.byte	'EXEVT',0
	.word	57558
	.byte	4,4,35,136,250,3,13
	.byte	'CREVT',0
	.word	56434
	.byte	4,4,35,140,250,3,13
	.byte	'SWEVT',0
	.word	59799
	.byte	4,4,35,144,250,3,13
	.byte	'reserved_FD14',0
	.word	27817
	.byte	28,4,35,148,250,3,13
	.byte	'TRIG_ACC',0
	.word	60252
	.byte	4,4,35,176,250,3,13
	.byte	'reserved_FD34',0
	.word	5248
	.byte	12,4,35,180,250,3,13
	.byte	'DMS',0
	.word	57185
	.byte	4,4,35,192,250,3,13
	.byte	'DCX',0
	.word	56935
	.byte	4,4,35,196,250,3,13
	.byte	'DBGTCR',0
	.word	56745
	.byte	4,4,35,200,250,3,13
	.byte	'reserved_FD4C',0
	.word	60952
	.byte	180,1,4,35,204,250,3,13
	.byte	'PCXI',0
	.word	58747
	.byte	4,4,35,128,252,3,13
	.byte	'PSW',0
	.word	59183
	.byte	4,4,35,132,252,3,13
	.byte	'PC',0
	.word	58498
	.byte	4,4,35,136,252,3,13
	.byte	'reserved_FE0C',0
	.word	4908
	.byte	8,4,35,140,252,3,13
	.byte	'SYSCON',0
	.word	59862
	.byte	4,4,35,148,252,3,13
	.byte	'CPU_ID',0
	.word	56308
	.byte	4,4,35,152,252,3,13
	.byte	'CORE_ID',0
	.word	56117
	.byte	4,4,35,156,252,3,13
	.byte	'BIV',0
	.word	55806
	.byte	4,4,35,160,252,3,13
	.byte	'BTV',0
	.word	55867
	.byte	4,4,35,164,252,3,13
	.byte	'ISP',0
	.word	58187
	.byte	4,4,35,168,252,3,13
	.byte	'ICR',0
	.word	40229
	.byte	4,4,35,172,252,3,13
	.byte	'reserved_FE30',0
	.word	4908
	.byte	8,4,35,176,252,3,13
	.byte	'FCX',0
	.word	57621
	.byte	4,4,35,184,252,3,13
	.byte	'LCX',0
	.word	58248
	.byte	4,4,35,188,252,3,13
	.byte	'reserved_FE40',0
	.word	27347
	.byte	16,4,35,192,252,3,13
	.byte	'CUS_ID',0
	.word	56497
	.byte	4,4,35,208,252,3,13
	.byte	'reserved_FE54',0
	.word	60963
	.byte	172,1,4,35,212,252,3,13
	.byte	'D',0
	.word	60974
	.byte	64,4,35,128,254,3,13
	.byte	'reserved_FF40',0
	.word	60983
	.byte	64,4,35,192,254,3,13
	.byte	'A',0
	.word	60992
	.byte	64,4,35,128,255,3,13
	.byte	'reserved_FFC0',0
	.word	60983
	.byte	64,4,35,192,255,3,0,14
	.word	61001
	.byte	31
	.byte	'Ifx_CPU',0,21,130,11,3
	.word	62792
	.byte	15,12,127,9,1,16
	.byte	'IfxCpu_Id_0',0,0,16
	.byte	'IfxCpu_Id_1',0,1,16
	.byte	'IfxCpu_Id_none',0,2,0,31
	.byte	'IfxCpu_Id',0,12,132,1,3
	.word	62814
	.byte	31
	.byte	'IfxCpu_ResourceCpu',0,12,161,1,3
	.word	10267
	.byte	31
	.byte	'Ifx_SCU_ACCEN0_Bits',0,7,79,3
	.word	33193
	.byte	31
	.byte	'Ifx_SCU_ACCEN1_Bits',0,7,85,3
	.word	33104
	.byte	31
	.byte	'Ifx_SCU_ARSTDIS_Bits',0,7,94,3
	.word	19245
	.byte	31
	.byte	'Ifx_SCU_CCUCON0_Bits',0,7,111,3
	.word	16861
	.byte	31
	.byte	'Ifx_SCU_CCUCON1_Bits',0,7,126,3
	.word	17152
	.byte	31
	.byte	'Ifx_SCU_CCUCON2_Bits',0,7,135,1,3
	.word	17807
	.byte	31
	.byte	'Ifx_SCU_CCUCON3_Bits',0,7,150,1,3
	.word	17945
	.byte	31
	.byte	'Ifx_SCU_CCUCON4_Bits',0,7,165,1,3
	.word	18198
	.byte	31
	.byte	'Ifx_SCU_CCUCON5_Bits',0,7,174,1,3
	.word	18443
	.byte	31
	.byte	'Ifx_SCU_CCUCON6_Bits',0,7,181,1,3
	.word	20492
	.byte	31
	.byte	'Ifx_SCU_CCUCON7_Bits',0,7,188,1,3
	.word	20602
	.byte	31
	.byte	'Ifx_SCU_CHIPID_Bits',0,7,202,1,3
	.word	26899
	.byte	31
	.byte	'Ifx_SCU_DTSCON_Bits',0,7,213,1,3
	.word	24242
	.byte	31
	.byte	'Ifx_SCU_DTSLIM_Bits',0,7,225,1,3
	.word	32899
	.byte	31
	.byte	'Ifx_SCU_DTSSTAT_Bits',0,7,235,1,3
	.word	24078
	.byte	31
	.byte	'Ifx_SCU_EICR_Bits',0,7,129,2,3
	.word	31249
	.byte	31
	.byte	'Ifx_SCU_EIFR_Bits',0,7,143,2,3
	.word	31635
	.byte	31
	.byte	'Ifx_SCU_EMSR_Bits',0,7,159,2,3
	.word	25699
	.byte	31
	.byte	'Ifx_SCU_ESRCFG_Bits',0,7,167,2,3
	.word	20025
	.byte	31
	.byte	'Ifx_SCU_ESROCFG_Bits',0,7,175,2,3
	.word	20163
	.byte	31
	.byte	'Ifx_SCU_EVR13CON_Bits',0,7,185,2,3
	.word	21889
	.byte	31
	.byte	'Ifx_SCU_EVR33CON_Bits',0,7,195,2,3
	.word	22061
	.byte	31
	.byte	'Ifx_SCU_EVRADCSTAT_Bits',0,7,205,2,3
	.word	28098
	.byte	31
	.byte	'Ifx_SCU_EVRDVSTAT_Bits',0,7,215,2,3
	.word	21716
	.byte	31
	.byte	'Ifx_SCU_EVRMONCTRL_Bits',0,7,232,2,3
	.word	28607
	.byte	31
	.byte	'Ifx_SCU_EVROSCCTRL_Bits',0,7,245,2,3
	.word	30590
	.byte	31
	.byte	'Ifx_SCU_EVROVMON_Bits',0,7,255,2,3
	.word	28435
	.byte	31
	.byte	'Ifx_SCU_EVRRSTCON_Bits',0,7,142,3,3
	.word	19745
	.byte	31
	.byte	'Ifx_SCU_EVRSDCOEFF1_Bits',0,7,152,3,3
	.word	29630
	.byte	31
	.byte	'Ifx_SCU_EVRSDCOEFF2_Bits',0,7,162,3,3
	.word	29789
	.byte	31
	.byte	'Ifx_SCU_EVRSDCOEFF3_Bits',0,7,172,3,3
	.word	29951
	.byte	31
	.byte	'Ifx_SCU_EVRSDCOEFF4_Bits',0,7,181,3,3
	.word	30119
	.byte	31
	.byte	'Ifx_SCU_EVRSDCOEFF5_Bits',0,7,191,3,3
	.word	30268
	.byte	31
	.byte	'Ifx_SCU_EVRSDCOEFF6_Bits',0,7,200,3,3
	.word	30439
	.byte	31
	.byte	'Ifx_SCU_EVRSDCTRL1_Bits',0,7,211,3,3
	.word	28943
	.byte	31
	.byte	'Ifx_SCU_EVRSDCTRL2_Bits',0,7,222,3,3
	.word	29131
	.byte	31
	.byte	'Ifx_SCU_EVRSDCTRL3_Bits',0,7,232,3,3
	.word	29312
	.byte	31
	.byte	'Ifx_SCU_EVRSDCTRL4_Bits',0,7,241,3,3
	.word	29479
	.byte	31
	.byte	'Ifx_SCU_EVRSTAT_Bits',0,7,130,4,3
	.word	21432
	.byte	31
	.byte	'Ifx_SCU_EVRTRIM_Bits',0,7,139,4,3
	.word	27949
	.byte	31
	.byte	'Ifx_SCU_EVRUVMON_Bits',0,7,149,4,3
	.word	28263
	.byte	31
	.byte	'Ifx_SCU_EXTCON_Bits',0,7,163,4,3
	.word	17578
	.byte	31
	.byte	'Ifx_SCU_FDR_Bits',0,7,174,4,3
	.word	17401
	.byte	31
	.byte	'Ifx_SCU_FMR_Bits',0,7,197,4,3
	.word	31859
	.byte	31
	.byte	'Ifx_SCU_ID_Bits',0,7,205,4,3
	.word	14637
	.byte	31
	.byte	'Ifx_SCU_IGCR_Bits',0,7,232,4,3
	.word	32425
	.byte	31
	.byte	'Ifx_SCU_IN_Bits',0,7,240,4,3
	.word	21318
	.byte	31
	.byte	'Ifx_SCU_IOCR_Bits',0,7,250,4,3
	.word	20868
	.byte	31
	.byte	'Ifx_SCU_LBISTCTRL0_Bits',0,7,131,5,3
	.word	27356
	.byte	31
	.byte	'Ifx_SCU_LBISTCTRL1_Bits',0,7,141,5,3
	.word	27512
	.byte	31
	.byte	'Ifx_SCU_LBISTCTRL2_Bits',0,7,149,5,3
	.word	27680
	.byte	31
	.byte	'Ifx_SCU_LCLCON_Bits',0,7,158,5,3
	.word	26631
	.byte	31
	.byte	'Ifx_SCU_LCLTEST_Bits',0,7,166,5,3
	.word	26774
	.byte	31
	.byte	'Ifx_SCU_MANID_Bits',0,7,174,5,3
	.word	27118
	.byte	31
	.byte	'Ifx_SCU_OMR_Bits',0,7,185,5,3
	.word	21146
	.byte	31
	.byte	'Ifx_SCU_OSCCON_Bits',0,7,209,5,3
	.word	14759
	.byte	31
	.byte	'Ifx_SCU_OUT_Bits',0,7,217,5,3
	.word	21031
	.byte	31
	.byte	'Ifx_SCU_OVCCON_Bits',0,7,233,5,3
	.word	30962
	.byte	31
	.byte	'Ifx_SCU_OVCENABLE_Bits',0,7,242,5,3
	.word	30818
	.byte	31
	.byte	'Ifx_SCU_PDISC_Bits',0,7,250,5,3
	.word	27826
	.byte	31
	.byte	'Ifx_SCU_PDR_Bits',0,7,132,6,3
	.word	20721
	.byte	31
	.byte	'Ifx_SCU_PDRR_Bits',0,7,146,6,3
	.word	32209
	.byte	31
	.byte	'Ifx_SCU_PLLCON0_Bits',0,7,166,6,3
	.word	15425
	.byte	31
	.byte	'Ifx_SCU_PLLCON1_Bits',0,7,177,6,3
	.word	15791
	.byte	31
	.byte	'Ifx_SCU_PLLCON2_Bits',0,7,184,6,3
	.word	15979
	.byte	31
	.byte	'Ifx_SCU_PLLERAYCON0_Bits',0,7,204,6,3
	.word	16293
	.byte	31
	.byte	'Ifx_SCU_PLLERAYCON1_Bits',0,7,215,6,3
	.word	16669
	.byte	31
	.byte	'Ifx_SCU_PLLERAYSTAT_Bits',0,7,227,6,3
	.word	16089
	.byte	31
	.byte	'Ifx_SCU_PLLSTAT_Bits',0,7,241,6,3
	.word	15182
	.byte	31
	.byte	'Ifx_SCU_PMCSR_Bits',0,7,251,6,3
	.word	23905
	.byte	31
	.byte	'Ifx_SCU_PMSWCR0_Bits',0,7,153,7,3
	.word	22504
	.byte	31
	.byte	'Ifx_SCU_PMSWCR1_Bits',0,7,170,7,3
	.word	24418
	.byte	31
	.byte	'Ifx_SCU_PMSWCR2_Bits',0,7,187,7,3
	.word	24722
	.byte	31
	.byte	'Ifx_SCU_PMSWSTAT_Bits',0,7,214,7,3
	.word	23081
	.byte	31
	.byte	'Ifx_SCU_PMSWSTATCLR_Bits',0,7,230,7,3
	.word	23586
	.byte	31
	.byte	'Ifx_SCU_RSTCON2_Bits',0,7,243,7,3
	.word	19526
	.byte	31
	.byte	'Ifx_SCU_RSTCON_Bits',0,7,129,8,3
	.word	19023
	.byte	31
	.byte	'Ifx_SCU_RSTSTAT_Bits',0,7,155,8,3
	.word	18581
	.byte	31
	.byte	'Ifx_SCU_SAFECON_Bits',0,7,162,8,3
	.word	27241
	.byte	31
	.byte	'Ifx_SCU_STSTAT_Bits',0,7,178,8,3
	.word	22233
	.byte	31
	.byte	'Ifx_SCU_SWRSTCON_Bits',0,7,186,8,3
	.word	19392
	.byte	31
	.byte	'Ifx_SCU_SYSCON_Bits',0,7,198,8,3
	.word	20284
	.byte	31
	.byte	'Ifx_SCU_TRAPCLR_Bits',0,7,208,8,3
	.word	26305
	.byte	31
	.byte	'Ifx_SCU_TRAPDIS_Bits',0,7,218,8,3
	.word	26468
	.byte	31
	.byte	'Ifx_SCU_TRAPSET_Bits',0,7,228,8,3
	.word	26142
	.byte	31
	.byte	'Ifx_SCU_TRAPSTAT_Bits',0,7,238,8,3
	.word	25978
	.byte	31
	.byte	'Ifx_SCU_WDTCPU_CON0_Bits',0,7,247,8,3
	.word	1174
	.byte	31
	.byte	'Ifx_SCU_WDTCPU_CON1_Bits',0,7,134,9,3
	.word	1310
	.byte	31
	.byte	'Ifx_SCU_WDTCPU_SR_Bits',0,7,150,9,3
	.word	1554
	.byte	31
	.byte	'Ifx_SCU_WDTS_CON0_Bits',0,7,159,9,3
	.word	25010
	.byte	31
	.byte	'Ifx_SCU_WDTS_CON1_Bits',0,7,175,9,3
	.word	25144
	.byte	31
	.byte	'Ifx_SCU_WDTS_SR_Bits',0,7,191,9,3
	.word	25404
	.byte	31
	.byte	'Ifx_SCU_ACCEN0',0,7,204,9,3
	.word	33722
	.byte	31
	.byte	'Ifx_SCU_ACCEN1',0,7,212,9,3
	.word	33153
	.byte	31
	.byte	'Ifx_SCU_ARSTDIS',0,7,220,9,3
	.word	19352
	.byte	31
	.byte	'Ifx_SCU_CCUCON0',0,7,228,9,3
	.word	17112
	.byte	31
	.byte	'Ifx_SCU_CCUCON1',0,7,236,9,3
	.word	17361
	.byte	31
	.byte	'Ifx_SCU_CCUCON2',0,7,244,9,3
	.word	17905
	.byte	31
	.byte	'Ifx_SCU_CCUCON3',0,7,252,9,3
	.word	18158
	.byte	31
	.byte	'Ifx_SCU_CCUCON4',0,7,132,10,3
	.word	18403
	.byte	31
	.byte	'Ifx_SCU_CCUCON5',0,7,140,10,3
	.word	18541
	.byte	31
	.byte	'Ifx_SCU_CCUCON6',0,7,148,10,3
	.word	20562
	.byte	31
	.byte	'Ifx_SCU_CCUCON7',0,7,156,10,3
	.word	20672
	.byte	31
	.byte	'Ifx_SCU_CHIPID',0,7,164,10,3
	.word	27078
	.byte	31
	.byte	'Ifx_SCU_DTSCON',0,7,172,10,3
	.word	24378
	.byte	31
	.byte	'Ifx_SCU_DTSLIM',0,7,180,10,3
	.word	33053
	.byte	31
	.byte	'Ifx_SCU_DTSSTAT',0,7,188,10,3
	.word	24202
	.byte	31
	.byte	'Ifx_SCU_EICR',0,7,196,10,3
	.word	31586
	.byte	31
	.byte	'Ifx_SCU_EIFR',0,7,204,10,3
	.word	31819
	.byte	31
	.byte	'Ifx_SCU_EMSR',0,7,212,10,3
	.word	25924
	.byte	31
	.byte	'Ifx_SCU_ESRCFG',0,7,220,10,3
	.word	20114
	.byte	31
	.byte	'Ifx_SCU_ESROCFG',0,7,228,10,3
	.word	20244
	.byte	31
	.byte	'Ifx_SCU_EVR13CON',0,7,236,10,3
	.word	22021
	.byte	31
	.byte	'Ifx_SCU_EVR33CON',0,7,244,10,3
	.word	22193
	.byte	31
	.byte	'Ifx_SCU_EVRADCSTAT',0,7,252,10,3
	.word	28223
	.byte	31
	.byte	'Ifx_SCU_EVRDVSTAT',0,7,132,11,3
	.word	21849
	.byte	31
	.byte	'Ifx_SCU_EVRMONCTRL',0,7,140,11,3
	.word	28903
	.byte	31
	.byte	'Ifx_SCU_EVROSCCTRL',0,7,148,11,3
	.word	30778
	.byte	31
	.byte	'Ifx_SCU_EVROVMON',0,7,156,11,3
	.word	28567
	.byte	31
	.byte	'Ifx_SCU_EVRRSTCON',0,7,164,11,3
	.word	19985
	.byte	31
	.byte	'Ifx_SCU_EVRSDCOEFF1',0,7,172,11,3
	.word	29749
	.byte	31
	.byte	'Ifx_SCU_EVRSDCOEFF2',0,7,180,11,3
	.word	29911
	.byte	31
	.byte	'Ifx_SCU_EVRSDCOEFF3',0,7,188,11,3
	.word	30079
	.byte	31
	.byte	'Ifx_SCU_EVRSDCOEFF4',0,7,196,11,3
	.word	30228
	.byte	31
	.byte	'Ifx_SCU_EVRSDCOEFF5',0,7,204,11,3
	.word	30399
	.byte	31
	.byte	'Ifx_SCU_EVRSDCOEFF6',0,7,212,11,3
	.word	30550
	.byte	31
	.byte	'Ifx_SCU_EVRSDCTRL1',0,7,220,11,3
	.word	29091
	.byte	31
	.byte	'Ifx_SCU_EVRSDCTRL2',0,7,228,11,3
	.word	29272
	.byte	31
	.byte	'Ifx_SCU_EVRSDCTRL3',0,7,236,11,3
	.word	29439
	.byte	31
	.byte	'Ifx_SCU_EVRSDCTRL4',0,7,244,11,3
	.word	29590
	.byte	31
	.byte	'Ifx_SCU_EVRSTAT',0,7,252,11,3
	.word	21676
	.byte	31
	.byte	'Ifx_SCU_EVRTRIM',0,7,132,12,3
	.word	28058
	.byte	31
	.byte	'Ifx_SCU_EVRUVMON',0,7,140,12,3
	.word	28395
	.byte	31
	.byte	'Ifx_SCU_EXTCON',0,7,148,12,3
	.word	17767
	.byte	31
	.byte	'Ifx_SCU_FDR',0,7,156,12,3
	.word	17538
	.byte	31
	.byte	'Ifx_SCU_FMR',0,7,164,12,3
	.word	32169
	.byte	31
	.byte	'Ifx_SCU_ID',0,7,172,12,3
	.word	14719
	.byte	31
	.byte	'Ifx_SCU_IGCR',0,7,180,12,3
	.word	32850
	.byte	31
	.byte	'Ifx_SCU_IN',0,7,188,12,3
	.word	21392
	.byte	31
	.byte	'Ifx_SCU_IOCR',0,7,196,12,3
	.word	20991
	.byte	31
	.byte	'Ifx_SCU_LBISTCTRL0',0,7,204,12,3
	.word	27472
	.byte	31
	.byte	'Ifx_SCU_LBISTCTRL1',0,7,212,12,3
	.word	27640
	.byte	31
	.byte	'Ifx_SCU_LBISTCTRL2',0,7,220,12,3
	.word	27777
	.byte	31
	.byte	'Ifx_SCU_LCLCON',0,7,228,12,3
	.word	26734
	.byte	31
	.byte	'Ifx_SCU_LCLTEST',0,7,236,12,3
	.word	26859
	.byte	31
	.byte	'Ifx_SCU_MANID',0,7,244,12,3
	.word	27201
	.byte	31
	.byte	'Ifx_SCU_OMR',0,7,252,12,3
	.word	21278
	.byte	31
	.byte	'Ifx_SCU_OSCCON',0,7,132,13,3
	.word	15142
	.byte	31
	.byte	'Ifx_SCU_OUT',0,7,140,13,3
	.word	21106
	.byte	31
	.byte	'Ifx_SCU_OVCCON',0,7,148,13,3
	.word	31200
	.byte	31
	.byte	'Ifx_SCU_OVCENABLE',0,7,156,13,3
	.word	30922
	.byte	31
	.byte	'Ifx_SCU_PDISC',0,7,164,13,3
	.word	27909
	.byte	31
	.byte	'Ifx_SCU_PDR',0,7,172,13,3
	.word	20828
	.byte	31
	.byte	'Ifx_SCU_PDRR',0,7,180,13,3
	.word	32385
	.byte	31
	.byte	'Ifx_SCU_PLLCON0',0,7,188,13,3
	.word	15751
	.byte	31
	.byte	'Ifx_SCU_PLLCON1',0,7,196,13,3
	.word	15939
	.byte	31
	.byte	'Ifx_SCU_PLLCON2',0,7,204,13,3
	.word	16049
	.byte	31
	.byte	'Ifx_SCU_PLLERAYCON0',0,7,212,13,3
	.word	16629
	.byte	31
	.byte	'Ifx_SCU_PLLERAYCON1',0,7,220,13,3
	.word	16821
	.byte	31
	.byte	'Ifx_SCU_PLLERAYSTAT',0,7,228,13,3
	.word	16253
	.byte	31
	.byte	'Ifx_SCU_PLLSTAT',0,7,236,13,3
	.word	15385
	.byte	31
	.byte	'Ifx_SCU_PMCSR',0,7,244,13,3
	.word	24029
	.byte	31
	.byte	'Ifx_SCU_PMSWCR0',0,7,252,13,3
	.word	23041
	.byte	31
	.byte	'Ifx_SCU_PMSWCR1',0,7,132,14,3
	.word	24682
	.byte	31
	.byte	'Ifx_SCU_PMSWCR2',0,7,140,14,3
	.word	24970
	.byte	31
	.byte	'Ifx_SCU_PMSWSTAT',0,7,148,14,3
	.word	23546
	.byte	31
	.byte	'Ifx_SCU_PMSWSTATCLR',0,7,156,14,3
	.word	23865
	.byte	31
	.byte	'Ifx_SCU_RSTCON',0,7,164,14,3
	.word	19205
	.byte	31
	.byte	'Ifx_SCU_RSTCON2',0,7,172,14,3
	.word	19705
	.byte	31
	.byte	'Ifx_SCU_RSTSTAT',0,7,180,14,3
	.word	18983
	.byte	31
	.byte	'Ifx_SCU_SAFECON',0,7,188,14,3
	.word	27307
	.byte	31
	.byte	'Ifx_SCU_STSTAT',0,7,196,14,3
	.word	22464
	.byte	31
	.byte	'Ifx_SCU_SWRSTCON',0,7,204,14,3
	.word	19486
	.byte	31
	.byte	'Ifx_SCU_SYSCON',0,7,212,14,3
	.word	20452
	.byte	31
	.byte	'Ifx_SCU_TRAPCLR',0,7,220,14,3
	.word	26428
	.byte	31
	.byte	'Ifx_SCU_TRAPDIS',0,7,228,14,3
	.word	26591
	.byte	31
	.byte	'Ifx_SCU_TRAPSET',0,7,236,14,3
	.word	26265
	.byte	31
	.byte	'Ifx_SCU_TRAPSTAT',0,7,244,14,3
	.word	26102
	.byte	31
	.byte	'Ifx_SCU_WDTCPU_CON0',0,7,252,14,3
	.word	1270
	.byte	31
	.byte	'Ifx_SCU_WDTCPU_CON1',0,7,132,15,3
	.word	1514
	.byte	31
	.byte	'Ifx_SCU_WDTCPU_SR',0,7,140,15,3
	.word	1745
	.byte	31
	.byte	'Ifx_SCU_WDTS_CON0',0,7,148,15,3
	.word	25104
	.byte	31
	.byte	'Ifx_SCU_WDTS_CON1',0,7,156,15,3
	.word	25364
	.byte	31
	.byte	'Ifx_SCU_WDTS_SR',0,7,164,15,3
	.word	25593
	.byte	14
	.word	1785
	.byte	31
	.byte	'Ifx_SCU_WDTCPU',0,7,180,15,3
	.word	68027
	.byte	14
	.word	25633
	.byte	31
	.byte	'Ifx_SCU_WDTS',0,7,188,15,3
	.word	68056
	.byte	14
	.word	33762
	.byte	31
	.byte	'Ifx_SCU',0,7,181,16,3
	.word	68083
	.byte	31
	.byte	'Ifx_STM_ACCEN0_Bits',0,14,79,3
	.word	13256
	.byte	31
	.byte	'Ifx_STM_ACCEN1_Bits',0,14,85,3
	.word	13167
	.byte	31
	.byte	'Ifx_STM_CAP_Bits',0,14,91,3
	.word	11697
	.byte	31
	.byte	'Ifx_STM_CAPSV_Bits',0,14,97,3
	.word	12574
	.byte	31
	.byte	'Ifx_STM_CLC_Bits',0,14,107,3
	.word	10820
	.byte	31
	.byte	'Ifx_STM_CMCON_Bits',0,14,120,3
	.word	11875
	.byte	31
	.byte	'Ifx_STM_CMP_Bits',0,14,126,3
	.word	11784
	.byte	31
	.byte	'Ifx_STM_ICR_Bits',0,14,139,1,3
	.word	12106
	.byte	31
	.byte	'Ifx_STM_ID_Bits',0,14,147,1,3
	.word	10976
	.byte	31
	.byte	'Ifx_STM_ISCR_Bits',0,14,157,1,3
	.word	12323
	.byte	31
	.byte	'Ifx_STM_KRST0_Bits',0,14,165,1,3
	.word	13044
	.byte	31
	.byte	'Ifx_STM_KRST1_Bits',0,14,172,1,3
	.word	12940
	.byte	31
	.byte	'Ifx_STM_KRSTCLR_Bits',0,14,179,1,3
	.word	12834
	.byte	31
	.byte	'Ifx_STM_OCS_Bits',0,14,189,1,3
	.word	12674
	.byte	31
	.byte	'Ifx_STM_TIM0_Bits',0,14,195,1,3
	.word	11098
	.byte	31
	.byte	'Ifx_STM_TIM0SV_Bits',0,14,201,1,3
	.word	12487
	.byte	31
	.byte	'Ifx_STM_TIM1_Bits',0,14,207,1,3
	.word	11183
	.byte	31
	.byte	'Ifx_STM_TIM2_Bits',0,14,213,1,3
	.word	11268
	.byte	31
	.byte	'Ifx_STM_TIM3_Bits',0,14,219,1,3
	.word	11353
	.byte	31
	.byte	'Ifx_STM_TIM4_Bits',0,14,225,1,3
	.word	11439
	.byte	31
	.byte	'Ifx_STM_TIM5_Bits',0,14,231,1,3
	.word	11525
	.byte	31
	.byte	'Ifx_STM_TIM6_Bits',0,14,237,1,3
	.word	11611
	.byte	31
	.byte	'Ifx_STM_ACCEN0',0,14,250,1,3
	.word	13785
	.byte	31
	.byte	'Ifx_STM_ACCEN1',0,14,130,2,3
	.word	13216
	.byte	31
	.byte	'Ifx_STM_CAP',0,14,138,2,3
	.word	11744
	.byte	31
	.byte	'Ifx_STM_CAPSV',0,14,146,2,3
	.word	12623
	.byte	31
	.byte	'Ifx_STM_CLC',0,14,154,2,3
	.word	10936
	.byte	31
	.byte	'Ifx_STM_CMCON',0,14,162,2,3
	.word	12066
	.byte	31
	.byte	'Ifx_STM_CMP',0,14,170,2,3
	.word	11826
	.byte	31
	.byte	'Ifx_STM_ICR',0,14,178,2,3
	.word	12283
	.byte	31
	.byte	'Ifx_STM_ID',0,14,186,2,3
	.word	11058
	.byte	31
	.byte	'Ifx_STM_ISCR',0,14,194,2,3
	.word	12447
	.byte	31
	.byte	'Ifx_STM_KRST0',0,14,202,2,3
	.word	13127
	.byte	31
	.byte	'Ifx_STM_KRST1',0,14,210,2,3
	.word	13004
	.byte	31
	.byte	'Ifx_STM_KRSTCLR',0,14,218,2,3
	.word	12900
	.byte	31
	.byte	'Ifx_STM_OCS',0,14,226,2,3
	.word	12794
	.byte	31
	.byte	'Ifx_STM_TIM0',0,14,234,2,3
	.word	11143
	.byte	31
	.byte	'Ifx_STM_TIM0SV',0,14,242,2,3
	.word	12534
	.byte	31
	.byte	'Ifx_STM_TIM1',0,14,250,2,3
	.word	11228
	.byte	31
	.byte	'Ifx_STM_TIM2',0,14,130,3,3
	.word	11313
	.byte	31
	.byte	'Ifx_STM_TIM3',0,14,138,3,3
	.word	11399
	.byte	31
	.byte	'Ifx_STM_TIM4',0,14,146,3,3
	.word	11485
	.byte	31
	.byte	'Ifx_STM_TIM5',0,14,154,3,3
	.word	11571
	.byte	31
	.byte	'Ifx_STM_TIM6',0,14,162,3,3
	.word	11657
	.byte	14
	.word	13825
	.byte	31
	.byte	'Ifx_STM',0,14,201,3,3
	.word	69188
	.byte	15,24,236,10,9,1,16
	.byte	'IfxScu_CCUCON0_CLKSEL_fBack',0,0,16
	.byte	'IfxScu_CCUCON0_CLKSEL_fPll',0,1,0,31
	.byte	'IfxScu_CCUCON0_CLKSEL',0,24,240,10,3
	.word	69210
	.byte	15,24,250,10,9,1,16
	.byte	'IfxScu_WDTCON1_IR_divBy16384',0,0,16
	.byte	'IfxScu_WDTCON1_IR_divBy256',0,1,16
	.byte	'IfxScu_WDTCON1_IR_divBy64',0,2,0,31
	.byte	'IfxScu_WDTCON1_IR',0,24,255,10,3
	.word	69307
	.byte	10
	.byte	'_Ifx_FLASH_ACCEN0_Bits',0,25,45,16,4,11
	.byte	'EN0',0,1
	.word	489
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	489
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	489
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	489
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	489
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	489
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	489
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	489
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	489
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	489
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	489
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	489
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	489
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	489
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	489
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	489
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	489
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	489
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	489
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	489
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	489
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	489
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	489
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	489
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	489
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	489
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	489
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	489
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	489
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	489
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	489
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	489
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_FLASH_ACCEN0_Bits',0,25,79,3
	.word	69429
	.byte	10
	.byte	'_Ifx_FLASH_ACCEN1_Bits',0,25,82,16,4,11
	.byte	'reserved_0',0,4
	.word	466
	.byte	32,0,2,35,0,0,31
	.byte	'Ifx_FLASH_ACCEN1_Bits',0,25,85,3
	.word	69990
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_CFG_Bits',0,25,88,16,4,11
	.byte	'SEL',0,1
	.word	489
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	489
	.byte	2,0,2,35,0,11
	.byte	'CLR',0,1
	.word	489
	.byte	1,7,2,35,1,11
	.byte	'DIS',0,1
	.word	489
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	466
	.byte	22,0,2,35,0,0,31
	.byte	'Ifx_FLASH_CBAB_CFG_Bits',0,25,95,3
	.word	70071
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_STAT_Bits',0,25,98,16,4,11
	.byte	'VLD0',0,1
	.word	489
	.byte	1,7,2,35,0,11
	.byte	'VLD1',0,1
	.word	489
	.byte	1,6,2,35,0,11
	.byte	'VLD2',0,1
	.word	489
	.byte	1,5,2,35,0,11
	.byte	'VLD3',0,1
	.word	489
	.byte	1,4,2,35,0,11
	.byte	'VLD4',0,1
	.word	489
	.byte	1,3,2,35,0,11
	.byte	'VLD5',0,1
	.word	489
	.byte	1,2,2,35,0,11
	.byte	'VLD6',0,1
	.word	489
	.byte	1,1,2,35,0,11
	.byte	'VLD7',0,1
	.word	489
	.byte	1,0,2,35,0,11
	.byte	'VLD8',0,1
	.word	489
	.byte	1,7,2,35,1,11
	.byte	'VLD9',0,1
	.word	489
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	466
	.byte	22,0,2,35,0,0,31
	.byte	'Ifx_FLASH_CBAB_STAT_Bits',0,25,111,3
	.word	70224
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_TOP_Bits',0,25,114,16,4,11
	.byte	'reserved_0',0,1
	.word	489
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	466
	.byte	19,8,2,35,0,11
	.byte	'ERR',0,1
	.word	489
	.byte	6,2,2,35,3,11
	.byte	'VLD',0,1
	.word	489
	.byte	1,1,2,35,3,11
	.byte	'CLR',0,1
	.word	489
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_FLASH_CBAB_TOP_Bits',0,25,121,3
	.word	70472
	.byte	10
	.byte	'_Ifx_FLASH_COMM0_Bits',0,25,124,16,4,11
	.byte	'STATUS',0,1
	.word	489
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	466
	.byte	24,0,2,35,0,0,31
	.byte	'Ifx_FLASH_COMM0_Bits',0,25,128,1,3
	.word	70618
	.byte	10
	.byte	'_Ifx_FLASH_COMM1_Bits',0,25,131,1,16,4,11
	.byte	'STATUS',0,1
	.word	489
	.byte	8,0,2,35,0,11
	.byte	'DATA',0,1
	.word	489
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1059
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_FLASH_COMM1_Bits',0,25,136,1,3
	.word	70716
	.byte	10
	.byte	'_Ifx_FLASH_COMM2_Bits',0,25,139,1,16,4,11
	.byte	'STATUS',0,1
	.word	489
	.byte	8,0,2,35,0,11
	.byte	'DATA',0,1
	.word	489
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1059
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_FLASH_COMM2_Bits',0,25,144,1,3
	.word	70832
	.byte	10
	.byte	'_Ifx_FLASH_ECCRD_Bits',0,25,147,1,16,4,11
	.byte	'RCODE',0,4
	.word	466
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	1059
	.byte	8,2,2,35,2,11
	.byte	'EDCERRINJ',0,1
	.word	489
	.byte	1,1,2,35,3,11
	.byte	'ECCORDIS',0,1
	.word	489
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_FLASH_ECCRD_Bits',0,25,153,1,3
	.word	70948
	.byte	10
	.byte	'_Ifx_FLASH_ECCRP_Bits',0,25,156,1,16,4,11
	.byte	'RCODE',0,4
	.word	466
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	1059
	.byte	8,2,2,35,2,11
	.byte	'EDCERRINJ',0,1
	.word	489
	.byte	1,1,2,35,3,11
	.byte	'ECCORDIS',0,1
	.word	489
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_FLASH_ECCRP_Bits',0,25,162,1,3
	.word	71088
	.byte	10
	.byte	'_Ifx_FLASH_ECCW_Bits',0,25,165,1,16,4,11
	.byte	'WCODE',0,4
	.word	466
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	1059
	.byte	8,2,2,35,2,11
	.byte	'DECENCDIS',0,1
	.word	489
	.byte	1,1,2,35,3,11
	.byte	'PECENCDIS',0,1
	.word	489
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_FLASH_ECCW_Bits',0,25,171,1,3
	.word	71228
	.byte	10
	.byte	'_Ifx_FLASH_FCON_Bits',0,25,174,1,16,4,11
	.byte	'WSPFLASH',0,1
	.word	489
	.byte	4,4,2,35,0,11
	.byte	'WSECPF',0,1
	.word	489
	.byte	2,2,2,35,0,11
	.byte	'WSDFLASH',0,2
	.word	1059
	.byte	6,4,2,35,0,11
	.byte	'WSECDF',0,1
	.word	489
	.byte	3,1,2,35,1,11
	.byte	'IDLE',0,1
	.word	489
	.byte	1,0,2,35,1,11
	.byte	'ESLDIS',0,1
	.word	489
	.byte	1,7,2,35,2,11
	.byte	'SLEEP',0,1
	.word	489
	.byte	1,6,2,35,2,11
	.byte	'NSAFECC',0,1
	.word	489
	.byte	1,5,2,35,2,11
	.byte	'STALL',0,1
	.word	489
	.byte	1,4,2,35,2,11
	.byte	'RES21',0,1
	.word	489
	.byte	2,2,2,35,2,11
	.byte	'RES23',0,1
	.word	489
	.byte	2,0,2,35,2,11
	.byte	'VOPERM',0,1
	.word	489
	.byte	1,7,2,35,3,11
	.byte	'SQERM',0,1
	.word	489
	.byte	1,6,2,35,3,11
	.byte	'PROERM',0,1
	.word	489
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	489
	.byte	3,2,2,35,3,11
	.byte	'PR5V',0,1
	.word	489
	.byte	1,1,2,35,3,11
	.byte	'EOBM',0,1
	.word	489
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_FLASH_FCON_Bits',0,25,193,1,3
	.word	71367
	.byte	10
	.byte	'_Ifx_FLASH_FPRO_Bits',0,25,196,1,16,4,11
	.byte	'PROINP',0,1
	.word	489
	.byte	1,7,2,35,0,11
	.byte	'PRODISP',0,1
	.word	489
	.byte	1,6,2,35,0,11
	.byte	'PROIND',0,1
	.word	489
	.byte	1,5,2,35,0,11
	.byte	'PRODISD',0,1
	.word	489
	.byte	1,4,2,35,0,11
	.byte	'PROINHSMCOTP',0,1
	.word	489
	.byte	1,3,2,35,0,11
	.byte	'RES5',0,1
	.word	489
	.byte	1,2,2,35,0,11
	.byte	'PROINOTP',0,1
	.word	489
	.byte	1,1,2,35,0,11
	.byte	'RES7',0,1
	.word	489
	.byte	1,0,2,35,0,11
	.byte	'PROINDBG',0,1
	.word	489
	.byte	1,7,2,35,1,11
	.byte	'PRODISDBG',0,1
	.word	489
	.byte	1,6,2,35,1,11
	.byte	'PROINHSM',0,1
	.word	489
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	489
	.byte	5,0,2,35,1,11
	.byte	'DCFP',0,1
	.word	489
	.byte	1,7,2,35,2,11
	.byte	'DDFP',0,1
	.word	489
	.byte	1,6,2,35,2,11
	.byte	'DDFPX',0,1
	.word	489
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	489
	.byte	1,4,2,35,2,11
	.byte	'DDFD',0,1
	.word	489
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	489
	.byte	1,2,2,35,2,11
	.byte	'ENPE',0,1
	.word	489
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	489
	.byte	8,0,2,35,3,0,31
	.byte	'Ifx_FLASH_FPRO_Bits',0,25,218,1,3
	.word	71729
	.byte	10
	.byte	'_Ifx_FLASH_FSR_Bits',0,25,221,1,16,4,11
	.byte	'FABUSY',0,1
	.word	489
	.byte	1,7,2,35,0,11
	.byte	'D0BUSY',0,1
	.word	489
	.byte	1,6,2,35,0,11
	.byte	'RES1',0,1
	.word	489
	.byte	1,5,2,35,0,11
	.byte	'P0BUSY',0,1
	.word	489
	.byte	1,4,2,35,0,11
	.byte	'P1BUSY',0,1
	.word	489
	.byte	1,3,2,35,0,11
	.byte	'RES5',0,1
	.word	489
	.byte	1,2,2,35,0,11
	.byte	'RES6',0,1
	.word	489
	.byte	1,1,2,35,0,11
	.byte	'PROG',0,1
	.word	489
	.byte	1,0,2,35,0,11
	.byte	'ERASE',0,1
	.word	489
	.byte	1,7,2,35,1,11
	.byte	'PFPAGE',0,1
	.word	489
	.byte	1,6,2,35,1,11
	.byte	'DFPAGE',0,1
	.word	489
	.byte	1,5,2,35,1,11
	.byte	'OPER',0,1
	.word	489
	.byte	1,4,2,35,1,11
	.byte	'SQER',0,1
	.word	489
	.byte	1,3,2,35,1,11
	.byte	'PROER',0,1
	.word	489
	.byte	1,2,2,35,1,11
	.byte	'PFSBER',0,1
	.word	489
	.byte	1,1,2,35,1,11
	.byte	'PFDBER',0,1
	.word	489
	.byte	1,0,2,35,1,11
	.byte	'PFMBER',0,1
	.word	489
	.byte	1,7,2,35,2,11
	.byte	'RES17',0,1
	.word	489
	.byte	1,6,2,35,2,11
	.byte	'DFSBER',0,1
	.word	489
	.byte	1,5,2,35,2,11
	.byte	'DFDBER',0,1
	.word	489
	.byte	1,4,2,35,2,11
	.byte	'DFTBER',0,1
	.word	489
	.byte	1,3,2,35,2,11
	.byte	'DFMBER',0,1
	.word	489
	.byte	1,2,2,35,2,11
	.byte	'SRIADDERR',0,1
	.word	489
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	1059
	.byte	2,7,2,35,2,11
	.byte	'PVER',0,1
	.word	489
	.byte	1,6,2,35,3,11
	.byte	'EVER',0,1
	.word	489
	.byte	1,5,2,35,3,11
	.byte	'SPND',0,1
	.word	489
	.byte	1,4,2,35,3,11
	.byte	'SLM',0,1
	.word	489
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	489
	.byte	1,2,2,35,3,11
	.byte	'ORIER',0,1
	.word	489
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	489
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_FLASH_FSR_Bits',0,25,254,1,3
	.word	72170
	.byte	10
	.byte	'_Ifx_FLASH_ID_Bits',0,25,129,2,16,4,11
	.byte	'MODREV',0,1
	.word	489
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	489
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	1059
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_FLASH_ID_Bits',0,25,134,2,3
	.word	72776
	.byte	10
	.byte	'_Ifx_FLASH_MARD_Bits',0,25,137,2,16,4,11
	.byte	'HMARGIN',0,1
	.word	489
	.byte	1,7,2,35,0,11
	.byte	'SELD0',0,1
	.word	489
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	489
	.byte	1,5,2,35,0,11
	.byte	'SPND',0,1
	.word	489
	.byte	1,4,2,35,0,11
	.byte	'SPNDERR',0,1
	.word	489
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,2
	.word	1059
	.byte	10,1,2,35,0,11
	.byte	'TRAPDIS',0,1
	.word	489
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1059
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_FLASH_MARD_Bits',0,25,147,2,3
	.word	72887
	.byte	10
	.byte	'_Ifx_FLASH_MARP_Bits',0,25,150,2,16,4,11
	.byte	'SELP0',0,1
	.word	489
	.byte	1,7,2,35,0,11
	.byte	'SELP1',0,1
	.word	489
	.byte	1,6,2,35,0,11
	.byte	'RES2',0,1
	.word	489
	.byte	1,5,2,35,0,11
	.byte	'RES3',0,1
	.word	489
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	1059
	.byte	11,1,2,35,0,11
	.byte	'TRAPDIS',0,1
	.word	489
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1059
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_FLASH_MARP_Bits',0,25,159,2,3
	.word	73101
	.byte	10
	.byte	'_Ifx_FLASH_PROCOND_Bits',0,25,162,2,16,4,11
	.byte	'L',0,1
	.word	489
	.byte	1,7,2,35,0,11
	.byte	'NSAFECC',0,1
	.word	489
	.byte	1,6,2,35,0,11
	.byte	'RAMIN',0,1
	.word	489
	.byte	2,4,2,35,0,11
	.byte	'RAMINSEL',0,1
	.word	489
	.byte	4,0,2,35,0,11
	.byte	'OSCCFG',0,1
	.word	489
	.byte	1,7,2,35,1,11
	.byte	'MODE',0,1
	.word	489
	.byte	2,5,2,35,1,11
	.byte	'APREN',0,1
	.word	489
	.byte	1,4,2,35,1,11
	.byte	'CAP0EN',0,1
	.word	489
	.byte	1,3,2,35,1,11
	.byte	'CAP1EN',0,1
	.word	489
	.byte	1,2,2,35,1,11
	.byte	'CAP2EN',0,1
	.word	489
	.byte	1,1,2,35,1,11
	.byte	'CAP3EN',0,1
	.word	489
	.byte	1,0,2,35,1,11
	.byte	'ESR0CNT',0,2
	.word	1059
	.byte	12,4,2,35,2,11
	.byte	'RES29',0,1
	.word	489
	.byte	2,2,2,35,3,11
	.byte	'RES30',0,1
	.word	489
	.byte	1,1,2,35,3,11
	.byte	'RPRO',0,1
	.word	489
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_FLASH_PROCOND_Bits',0,25,179,2,3
	.word	73288
	.byte	10
	.byte	'_Ifx_FLASH_PROCONDBG_Bits',0,25,182,2,16,4,11
	.byte	'OCDSDIS',0,1
	.word	489
	.byte	1,7,2,35,0,11
	.byte	'DBGIFLCK',0,1
	.word	489
	.byte	1,6,2,35,0,11
	.byte	'EDM',0,1
	.word	489
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	466
	.byte	28,0,2,35,0,0,31
	.byte	'Ifx_FLASH_PROCONDBG_Bits',0,25,188,2,3
	.word	73612
	.byte	10
	.byte	'_Ifx_FLASH_PROCONHSM_Bits',0,25,191,2,16,4,11
	.byte	'HSMDBGDIS',0,1
	.word	489
	.byte	1,7,2,35,0,11
	.byte	'DBGIFLCK',0,1
	.word	489
	.byte	1,6,2,35,0,11
	.byte	'TSTIFLCK',0,1
	.word	489
	.byte	1,5,2,35,0,11
	.byte	'HSMTSTDIS',0,1
	.word	489
	.byte	1,4,2,35,0,11
	.byte	'RES15',0,2
	.word	1059
	.byte	12,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	1059
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_FLASH_PROCONHSM_Bits',0,25,199,2,3
	.word	73755
	.byte	10
	.byte	'_Ifx_FLASH_PROCONHSMCOTP_Bits',0,25,202,2,16,4,11
	.byte	'HSMBOOTEN',0,1
	.word	489
	.byte	1,7,2,35,0,11
	.byte	'SSWWAIT',0,1
	.word	489
	.byte	1,6,2,35,0,11
	.byte	'HSMDX',0,1
	.word	489
	.byte	1,5,2,35,0,11
	.byte	'HSM6X',0,1
	.word	489
	.byte	1,4,2,35,0,11
	.byte	'HSM16X',0,1
	.word	489
	.byte	1,3,2,35,0,11
	.byte	'HSM17X',0,1
	.word	489
	.byte	1,2,2,35,0,11
	.byte	'S6ROM',0,1
	.word	489
	.byte	1,1,2,35,0,11
	.byte	'HSMENPINS',0,2
	.word	1059
	.byte	2,7,2,35,0,11
	.byte	'HSMENRES',0,1
	.word	489
	.byte	2,5,2,35,1,11
	.byte	'DESTDBG',0,1
	.word	489
	.byte	2,3,2,35,1,11
	.byte	'BLKFLAN',0,1
	.word	489
	.byte	1,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	489
	.byte	2,0,2,35,1,11
	.byte	'S16ROM',0,1
	.word	489
	.byte	1,7,2,35,2,11
	.byte	'S17ROM',0,1
	.word	489
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	1059
	.byte	14,0,2,35,2,0,31
	.byte	'Ifx_FLASH_PROCONHSMCOTP_Bits',0,25,219,2,3
	.word	73944
	.byte	10
	.byte	'_Ifx_FLASH_PROCONOTP_Bits',0,25,222,2,16,4,11
	.byte	'S0ROM',0,1
	.word	489
	.byte	1,7,2,35,0,11
	.byte	'S1ROM',0,1
	.word	489
	.byte	1,6,2,35,0,11
	.byte	'S2ROM',0,1
	.word	489
	.byte	1,5,2,35,0,11
	.byte	'S3ROM',0,1
	.word	489
	.byte	1,4,2,35,0,11
	.byte	'S4ROM',0,1
	.word	489
	.byte	1,3,2,35,0,11
	.byte	'S5ROM',0,1
	.word	489
	.byte	1,2,2,35,0,11
	.byte	'S6ROM',0,1
	.word	489
	.byte	1,1,2,35,0,11
	.byte	'S7ROM',0,1
	.word	489
	.byte	1,0,2,35,0,11
	.byte	'S8ROM',0,1
	.word	489
	.byte	1,7,2,35,1,11
	.byte	'S9ROM',0,1
	.word	489
	.byte	1,6,2,35,1,11
	.byte	'S10ROM',0,1
	.word	489
	.byte	1,5,2,35,1,11
	.byte	'S11ROM',0,1
	.word	489
	.byte	1,4,2,35,1,11
	.byte	'S12ROM',0,1
	.word	489
	.byte	1,3,2,35,1,11
	.byte	'S13ROM',0,1
	.word	489
	.byte	1,2,2,35,1,11
	.byte	'S14ROM',0,1
	.word	489
	.byte	1,1,2,35,1,11
	.byte	'S15ROM',0,1
	.word	489
	.byte	1,0,2,35,1,11
	.byte	'S16ROM',0,1
	.word	489
	.byte	1,7,2,35,2,11
	.byte	'S17ROM',0,1
	.word	489
	.byte	1,6,2,35,2,11
	.byte	'S18ROM',0,1
	.word	489
	.byte	1,5,2,35,2,11
	.byte	'S19ROM',0,1
	.word	489
	.byte	1,4,2,35,2,11
	.byte	'S20ROM',0,1
	.word	489
	.byte	1,3,2,35,2,11
	.byte	'S21ROM',0,1
	.word	489
	.byte	1,2,2,35,2,11
	.byte	'S22ROM',0,1
	.word	489
	.byte	1,1,2,35,2,11
	.byte	'S23ROM',0,1
	.word	489
	.byte	1,0,2,35,2,11
	.byte	'S24ROM',0,1
	.word	489
	.byte	1,7,2,35,3,11
	.byte	'S25ROM',0,1
	.word	489
	.byte	1,6,2,35,3,11
	.byte	'S26ROM',0,1
	.word	489
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	489
	.byte	2,3,2,35,3,11
	.byte	'BML',0,1
	.word	489
	.byte	2,1,2,35,3,11
	.byte	'TP',0,1
	.word	489
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_FLASH_PROCONOTP_Bits',0,25,254,2,3
	.word	74307
	.byte	10
	.byte	'_Ifx_FLASH_PROCONP_Bits',0,25,129,3,16,4,11
	.byte	'S0L',0,1
	.word	489
	.byte	1,7,2,35,0,11
	.byte	'S1L',0,1
	.word	489
	.byte	1,6,2,35,0,11
	.byte	'S2L',0,1
	.word	489
	.byte	1,5,2,35,0,11
	.byte	'S3L',0,1
	.word	489
	.byte	1,4,2,35,0,11
	.byte	'S4L',0,1
	.word	489
	.byte	1,3,2,35,0,11
	.byte	'S5L',0,1
	.word	489
	.byte	1,2,2,35,0,11
	.byte	'S6L',0,1
	.word	489
	.byte	1,1,2,35,0,11
	.byte	'S7L',0,1
	.word	489
	.byte	1,0,2,35,0,11
	.byte	'S8L',0,1
	.word	489
	.byte	1,7,2,35,1,11
	.byte	'S9L',0,1
	.word	489
	.byte	1,6,2,35,1,11
	.byte	'S10L',0,1
	.word	489
	.byte	1,5,2,35,1,11
	.byte	'S11L',0,1
	.word	489
	.byte	1,4,2,35,1,11
	.byte	'S12L',0,1
	.word	489
	.byte	1,3,2,35,1,11
	.byte	'S13L',0,1
	.word	489
	.byte	1,2,2,35,1,11
	.byte	'S14L',0,1
	.word	489
	.byte	1,1,2,35,1,11
	.byte	'S15L',0,1
	.word	489
	.byte	1,0,2,35,1,11
	.byte	'S16L',0,1
	.word	489
	.byte	1,7,2,35,2,11
	.byte	'S17L',0,1
	.word	489
	.byte	1,6,2,35,2,11
	.byte	'S18L',0,1
	.word	489
	.byte	1,5,2,35,2,11
	.byte	'S19L',0,1
	.word	489
	.byte	1,4,2,35,2,11
	.byte	'S20L',0,1
	.word	489
	.byte	1,3,2,35,2,11
	.byte	'S21L',0,1
	.word	489
	.byte	1,2,2,35,2,11
	.byte	'S22L',0,1
	.word	489
	.byte	1,1,2,35,2,11
	.byte	'S23L',0,1
	.word	489
	.byte	1,0,2,35,2,11
	.byte	'S24L',0,1
	.word	489
	.byte	1,7,2,35,3,11
	.byte	'S25L',0,1
	.word	489
	.byte	1,6,2,35,3,11
	.byte	'S26L',0,1
	.word	489
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	489
	.byte	4,1,2,35,3,11
	.byte	'RPRO',0,1
	.word	489
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_FLASH_PROCONP_Bits',0,25,160,3,3
	.word	74902
	.byte	10
	.byte	'_Ifx_FLASH_PROCONWOP_Bits',0,25,163,3,16,4,11
	.byte	'S0WOP',0,1
	.word	489
	.byte	1,7,2,35,0,11
	.byte	'S1WOP',0,1
	.word	489
	.byte	1,6,2,35,0,11
	.byte	'S2WOP',0,1
	.word	489
	.byte	1,5,2,35,0,11
	.byte	'S3WOP',0,1
	.word	489
	.byte	1,4,2,35,0,11
	.byte	'S4WOP',0,1
	.word	489
	.byte	1,3,2,35,0,11
	.byte	'S5WOP',0,1
	.word	489
	.byte	1,2,2,35,0,11
	.byte	'S6WOP',0,1
	.word	489
	.byte	1,1,2,35,0,11
	.byte	'S7WOP',0,1
	.word	489
	.byte	1,0,2,35,0,11
	.byte	'S8WOP',0,1
	.word	489
	.byte	1,7,2,35,1,11
	.byte	'S9WOP',0,1
	.word	489
	.byte	1,6,2,35,1,11
	.byte	'S10WOP',0,1
	.word	489
	.byte	1,5,2,35,1,11
	.byte	'S11WOP',0,1
	.word	489
	.byte	1,4,2,35,1,11
	.byte	'S12WOP',0,1
	.word	489
	.byte	1,3,2,35,1,11
	.byte	'S13WOP',0,1
	.word	489
	.byte	1,2,2,35,1,11
	.byte	'S14WOP',0,1
	.word	489
	.byte	1,1,2,35,1,11
	.byte	'S15WOP',0,1
	.word	489
	.byte	1,0,2,35,1,11
	.byte	'S16WOP',0,1
	.word	489
	.byte	1,7,2,35,2,11
	.byte	'S17WOP',0,1
	.word	489
	.byte	1,6,2,35,2,11
	.byte	'S18WOP',0,1
	.word	489
	.byte	1,5,2,35,2,11
	.byte	'S19WOP',0,1
	.word	489
	.byte	1,4,2,35,2,11
	.byte	'S20WOP',0,1
	.word	489
	.byte	1,3,2,35,2,11
	.byte	'S21WOP',0,1
	.word	489
	.byte	1,2,2,35,2,11
	.byte	'S22WOP',0,1
	.word	489
	.byte	1,1,2,35,2,11
	.byte	'S23WOP',0,1
	.word	489
	.byte	1,0,2,35,2,11
	.byte	'S24WOP',0,1
	.word	489
	.byte	1,7,2,35,3,11
	.byte	'S25WOP',0,1
	.word	489
	.byte	1,6,2,35,3,11
	.byte	'S26WOP',0,1
	.word	489
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	489
	.byte	4,1,2,35,3,11
	.byte	'DATM',0,1
	.word	489
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_FLASH_PROCONWOP_Bits',0,25,194,3,3
	.word	75426
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG0_Bits',0,25,197,3,16,4,11
	.byte	'TAG',0,1
	.word	489
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	466
	.byte	26,0,2,35,0,0,31
	.byte	'Ifx_FLASH_RDB_CFG0_Bits',0,25,201,3,3
	.word	76008
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG1_Bits',0,25,204,3,16,4,11
	.byte	'TAG',0,1
	.word	489
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	466
	.byte	26,0,2,35,0,0,31
	.byte	'Ifx_FLASH_RDB_CFG1_Bits',0,25,208,3,3
	.word	76110
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG2_Bits',0,25,211,3,16,4,11
	.byte	'TAG',0,1
	.word	489
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	466
	.byte	26,0,2,35,0,0,31
	.byte	'Ifx_FLASH_RDB_CFG2_Bits',0,25,215,3,3
	.word	76212
	.byte	10
	.byte	'_Ifx_FLASH_RRAD_Bits',0,25,218,3,16,4,11
	.byte	'reserved_0',0,1
	.word	489
	.byte	3,5,2,35,0,11
	.byte	'ADD',0,4
	.word	466
	.byte	29,0,2,35,0,0,31
	.byte	'Ifx_FLASH_RRAD_Bits',0,25,222,3,3
	.word	76314
	.byte	10
	.byte	'_Ifx_FLASH_RRCT_Bits',0,25,225,3,16,4,11
	.byte	'STRT',0,1
	.word	489
	.byte	1,7,2,35,0,11
	.byte	'STP',0,1
	.word	489
	.byte	1,6,2,35,0,11
	.byte	'BUSY',0,1
	.word	489
	.byte	1,5,2,35,0,11
	.byte	'DONE',0,1
	.word	489
	.byte	1,4,2,35,0,11
	.byte	'ERR',0,1
	.word	489
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	489
	.byte	3,0,2,35,0,11
	.byte	'EOBM',0,1
	.word	489
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	489
	.byte	7,0,2,35,1,11
	.byte	'CNT',0,2
	.word	1059
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_FLASH_RRCT_Bits',0,25,236,3,3
	.word	76408
	.byte	10
	.byte	'_Ifx_FLASH_RRD0_Bits',0,25,239,3,16,4,11
	.byte	'DATA',0,4
	.word	466
	.byte	32,0,2,35,0,0,31
	.byte	'Ifx_FLASH_RRD0_Bits',0,25,242,3,3
	.word	76618
	.byte	10
	.byte	'_Ifx_FLASH_RRD1_Bits',0,25,245,3,16,4,11
	.byte	'DATA',0,4
	.word	466
	.byte	32,0,2,35,0,0,31
	.byte	'Ifx_FLASH_RRD1_Bits',0,25,248,3,3
	.word	76691
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_CFG_Bits',0,25,251,3,16,4,11
	.byte	'SEL',0,1
	.word	489
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	489
	.byte	2,0,2,35,0,11
	.byte	'CLR',0,1
	.word	489
	.byte	1,7,2,35,1,11
	.byte	'DIS',0,1
	.word	489
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	466
	.byte	22,0,2,35,0,0,31
	.byte	'Ifx_FLASH_UBAB_CFG_Bits',0,25,130,4,3
	.word	76764
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_STAT_Bits',0,25,133,4,16,4,11
	.byte	'VLD0',0,1
	.word	489
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	466
	.byte	31,0,2,35,0,0,31
	.byte	'Ifx_FLASH_UBAB_STAT_Bits',0,25,137,4,3
	.word	76919
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_TOP_Bits',0,25,140,4,16,4,11
	.byte	'reserved_0',0,1
	.word	489
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	466
	.byte	19,8,2,35,0,11
	.byte	'ERR',0,1
	.word	489
	.byte	6,2,2,35,3,11
	.byte	'VLD',0,1
	.word	489
	.byte	1,1,2,35,3,11
	.byte	'CLR',0,1
	.word	489
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_FLASH_UBAB_TOP_Bits',0,25,147,4,3
	.word	77024
	.byte	12,25,155,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69429
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_ACCEN0',0,25,160,4,3
	.word	77172
	.byte	12,25,163,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69990
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_ACCEN1',0,25,168,4,3
	.word	77238
	.byte	12,25,171,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70071
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_CBAB_CFG',0,25,176,4,3
	.word	77304
	.byte	12,25,179,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70224
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_CBAB_STAT',0,25,184,4,3
	.word	77372
	.byte	12,25,187,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70472
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_CBAB_TOP',0,25,192,4,3
	.word	77441
	.byte	12,25,195,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70618
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_COMM0',0,25,200,4,3
	.word	77509
	.byte	12,25,203,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70716
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_COMM1',0,25,208,4,3
	.word	77574
	.byte	12,25,211,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70832
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_COMM2',0,25,216,4,3
	.word	77639
	.byte	12,25,219,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70948
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_ECCRD',0,25,224,4,3
	.word	77704
	.byte	12,25,227,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71088
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_ECCRP',0,25,232,4,3
	.word	77769
	.byte	12,25,235,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71228
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_ECCW',0,25,240,4,3
	.word	77834
	.byte	12,25,243,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71367
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_FCON',0,25,248,4,3
	.word	77898
	.byte	12,25,251,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71729
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_FPRO',0,25,128,5,3
	.word	77962
	.byte	12,25,131,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72170
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_FSR',0,25,136,5,3
	.word	78026
	.byte	12,25,139,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72776
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_ID',0,25,144,5,3
	.word	78089
	.byte	12,25,147,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72887
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_MARD',0,25,152,5,3
	.word	78151
	.byte	12,25,155,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	73101
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_MARP',0,25,160,5,3
	.word	78215
	.byte	12,25,163,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	73288
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_PROCOND',0,25,168,5,3
	.word	78279
	.byte	12,25,171,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	73612
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_PROCONDBG',0,25,176,5,3
	.word	78346
	.byte	12,25,179,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	73755
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_PROCONHSM',0,25,184,5,3
	.word	78415
	.byte	12,25,187,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	73944
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_PROCONHSMCOTP',0,25,192,5,3
	.word	78484
	.byte	12,25,195,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	74307
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_PROCONOTP',0,25,200,5,3
	.word	78557
	.byte	12,25,203,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	74902
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_PROCONP',0,25,208,5,3
	.word	78626
	.byte	12,25,211,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	75426
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_PROCONWOP',0,25,216,5,3
	.word	78693
	.byte	12,25,219,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	76008
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_RDB_CFG0',0,25,224,5,3
	.word	78762
	.byte	12,25,227,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	76110
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_RDB_CFG1',0,25,232,5,3
	.word	78830
	.byte	12,25,235,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	76212
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_RDB_CFG2',0,25,240,5,3
	.word	78898
	.byte	12,25,243,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	76314
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_RRAD',0,25,248,5,3
	.word	78966
	.byte	12,25,251,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	76408
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_RRCT',0,25,128,6,3
	.word	79030
	.byte	12,25,131,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	76618
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_RRD0',0,25,136,6,3
	.word	79094
	.byte	12,25,139,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	76691
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_RRD1',0,25,144,6,3
	.word	79158
	.byte	12,25,147,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	76764
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_UBAB_CFG',0,25,152,6,3
	.word	79222
	.byte	12,25,155,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	76919
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_UBAB_STAT',0,25,160,6,3
	.word	79290
	.byte	12,25,163,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	77024
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_UBAB_TOP',0,25,168,6,3
	.word	79359
	.byte	10
	.byte	'_Ifx_FLASH_CBAB',0,25,179,6,25,12,13
	.byte	'CFG',0
	.word	77304
	.byte	4,2,35,0,13
	.byte	'STAT',0
	.word	77372
	.byte	4,2,35,4,13
	.byte	'TOP',0
	.word	77441
	.byte	4,2,35,8,0,14
	.word	79427
	.byte	31
	.byte	'Ifx_FLASH_CBAB',0,25,184,6,3
	.word	79490
	.byte	10
	.byte	'_Ifx_FLASH_RDB',0,25,187,6,25,12,13
	.byte	'CFG0',0
	.word	78762
	.byte	4,2,35,0,13
	.byte	'CFG1',0
	.word	78830
	.byte	4,2,35,4,13
	.byte	'CFG2',0
	.word	78898
	.byte	4,2,35,8,0,14
	.word	79519
	.byte	31
	.byte	'Ifx_FLASH_RDB',0,25,192,6,3
	.word	79583
	.byte	10
	.byte	'_Ifx_FLASH_UBAB',0,25,195,6,25,12,13
	.byte	'CFG',0
	.word	79222
	.byte	4,2,35,0,13
	.byte	'STAT',0
	.word	79290
	.byte	4,2,35,4,13
	.byte	'TOP',0
	.word	79359
	.byte	4,2,35,8,0,14
	.word	79611
	.byte	31
	.byte	'Ifx_FLASH_UBAB',0,25,200,6,3
	.word	79674
	.byte	31
	.byte	'Ifx_P_ACCEN0_Bits',0,9,79,3
	.word	8661
	.byte	31
	.byte	'Ifx_P_ACCEN1_Bits',0,9,85,3
	.word	8574
	.byte	31
	.byte	'Ifx_P_ESR_Bits',0,9,107,3
	.word	4917
	.byte	31
	.byte	'Ifx_P_ID_Bits',0,9,115,3
	.word	2970
	.byte	31
	.byte	'Ifx_P_IN_Bits',0,9,137,1,3
	.word	3965
	.byte	31
	.byte	'Ifx_P_IOCR0_Bits',0,9,150,1,3
	.word	3098
	.byte	31
	.byte	'Ifx_P_IOCR12_Bits',0,9,163,1,3
	.word	3745
	.byte	31
	.byte	'Ifx_P_IOCR4_Bits',0,9,176,1,3
	.word	3313
	.byte	31
	.byte	'Ifx_P_IOCR8_Bits',0,9,189,1,3
	.word	3528
	.byte	31
	.byte	'Ifx_P_LPCR0_Bits',0,9,197,1,3
	.word	7933
	.byte	31
	.byte	'Ifx_P_LPCR1_Bits',0,9,205,1,3
	.word	8057
	.byte	31
	.byte	'Ifx_P_LPCR1_P21_Bits',0,9,215,1,3
	.word	8141
	.byte	31
	.byte	'Ifx_P_LPCR2_Bits',0,9,229,1,3
	.word	8321
	.byte	31
	.byte	'Ifx_P_OMCR0_Bits',0,9,240,1,3
	.word	6572
	.byte	31
	.byte	'Ifx_P_OMCR12_Bits',0,9,250,1,3
	.word	7096
	.byte	31
	.byte	'Ifx_P_OMCR4_Bits',0,9,133,2,3
	.word	6746
	.byte	31
	.byte	'Ifx_P_OMCR8_Bits',0,9,144,2,3
	.word	6920
	.byte	31
	.byte	'Ifx_P_OMCR_Bits',0,9,166,2,3
	.word	7585
	.byte	31
	.byte	'Ifx_P_OMR_Bits',0,9,203,2,3
	.word	2399
	.byte	31
	.byte	'Ifx_P_OMSR0_Bits',0,9,213,2,3
	.word	5909
	.byte	31
	.byte	'Ifx_P_OMSR12_Bits',0,9,224,2,3
	.word	6397
	.byte	31
	.byte	'Ifx_P_OMSR4_Bits',0,9,235,2,3
	.word	6056
	.byte	31
	.byte	'Ifx_P_OMSR8_Bits',0,9,246,2,3
	.word	6225
	.byte	31
	.byte	'Ifx_P_OMSR_Bits',0,9,140,3,3
	.word	7252
	.byte	31
	.byte	'Ifx_P_OUT_Bits',0,9,162,3,3
	.word	2083
	.byte	31
	.byte	'Ifx_P_PCSR_Bits',0,9,180,3,3
	.word	5623
	.byte	31
	.byte	'Ifx_P_PDISC_Bits',0,9,202,3,3
	.word	5257
	.byte	31
	.byte	'Ifx_P_PDR0_Bits',0,9,223,3,3
	.word	4288
	.byte	31
	.byte	'Ifx_P_PDR1_Bits',0,9,244,3,3
	.word	4592
	.byte	31
	.byte	'Ifx_P_ACCEN0',0,9,129,4,3
	.word	9188
	.byte	31
	.byte	'Ifx_P_ACCEN1',0,9,137,4,3
	.word	8621
	.byte	31
	.byte	'Ifx_P_ESR',0,9,145,4,3
	.word	5208
	.byte	31
	.byte	'Ifx_P_ID',0,9,153,4,3
	.word	3049
	.byte	31
	.byte	'Ifx_P_IN',0,9,161,4,3
	.word	4239
	.byte	31
	.byte	'Ifx_P_IOCR0',0,9,169,4,3
	.word	3273
	.byte	31
	.byte	'Ifx_P_IOCR12',0,9,177,4,3
	.word	3925
	.byte	31
	.byte	'Ifx_P_IOCR4',0,9,185,4,3
	.word	3488
	.byte	31
	.byte	'Ifx_P_IOCR8',0,9,193,4,3
	.word	3705
	.byte	31
	.byte	'Ifx_P_LPCR0',0,9,201,4,3
	.word	8017
	.byte	31
	.byte	'Ifx_P_LPCR1',0,9,210,4,3
	.word	8266
	.byte	31
	.byte	'Ifx_P_LPCR2',0,9,218,4,3
	.word	8525
	.byte	31
	.byte	'Ifx_P_OMCR',0,9,226,4,3
	.word	7893
	.byte	31
	.byte	'Ifx_P_OMCR0',0,9,234,4,3
	.word	6706
	.byte	31
	.byte	'Ifx_P_OMCR12',0,9,242,4,3
	.word	7212
	.byte	31
	.byte	'Ifx_P_OMCR4',0,9,250,4,3
	.word	6880
	.byte	31
	.byte	'Ifx_P_OMCR8',0,9,130,5,3
	.word	7056
	.byte	31
	.byte	'Ifx_P_OMR',0,9,138,5,3
	.word	2930
	.byte	31
	.byte	'Ifx_P_OMSR',0,9,146,5,3
	.word	7545
	.byte	31
	.byte	'Ifx_P_OMSR0',0,9,154,5,3
	.word	6016
	.byte	31
	.byte	'Ifx_P_OMSR12',0,9,162,5,3
	.word	6532
	.byte	31
	.byte	'Ifx_P_OMSR4',0,9,170,5,3
	.word	6185
	.byte	31
	.byte	'Ifx_P_OMSR8',0,9,178,5,3
	.word	6357
	.byte	31
	.byte	'Ifx_P_OUT',0,9,186,5,3
	.word	2359
	.byte	31
	.byte	'Ifx_P_PCSR',0,9,194,5,3
	.word	5869
	.byte	31
	.byte	'Ifx_P_PDISC',0,9,202,5,3
	.word	5583
	.byte	31
	.byte	'Ifx_P_PDR0',0,9,210,5,3
	.word	4552
	.byte	31
	.byte	'Ifx_P_PDR1',0,9,218,5,3
	.word	4868
	.byte	14
	.word	9228
	.byte	31
	.byte	'Ifx_P',0,9,139,6,3
	.word	81021
	.byte	31
	.byte	'IfxPort_InputMode',0,8,89,3
	.word	9841
	.byte	31
	.byte	'IfxPort_Mode',0,8,116,3
	.word	36370
	.byte	15,8,120,9,1,16
	.byte	'IfxPort_OutputIdx_general',0,128,1,16
	.byte	'IfxPort_OutputIdx_alt1',0,136,1,16
	.byte	'IfxPort_OutputIdx_alt2',0,144,1,16
	.byte	'IfxPort_OutputIdx_alt3',0,152,1,16
	.byte	'IfxPort_OutputIdx_alt4',0,160,1,16
	.byte	'IfxPort_OutputIdx_alt5',0,168,1,16
	.byte	'IfxPort_OutputIdx_alt6',0,176,1,16
	.byte	'IfxPort_OutputIdx_alt7',0,184,1,0,31
	.byte	'IfxPort_OutputIdx',0,8,130,1,3
	.word	81088
	.byte	15,8,134,1,9,1,16
	.byte	'IfxPort_OutputMode_pushPull',0,128,1,16
	.byte	'IfxPort_OutputMode_openDrain',0,192,1,0,31
	.byte	'IfxPort_OutputMode',0,8,138,1,3
	.word	81332
	.byte	15,8,144,1,9,1,16
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed1',0,0,16
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed2',0,1,16
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed3',0,2,16
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed4',0,3,16
	.byte	'IfxPort_PadDriver_lvdsSpeed1',0,4,16
	.byte	'IfxPort_PadDriver_lvdsSpeed2',0,5,16
	.byte	'IfxPort_PadDriver_lvdsSpeed3',0,6,16
	.byte	'IfxPort_PadDriver_lvdsSpeed4',0,7,16
	.byte	'IfxPort_PadDriver_ttlSpeed1',0,8,16
	.byte	'IfxPort_PadDriver_ttlSpeed2',0,9,16
	.byte	'IfxPort_PadDriver_ttlSpeed3',0,10,16
	.byte	'IfxPort_PadDriver_ttlSpeed4',0,11,0,31
	.byte	'IfxPort_PadDriver',0,8,158,1,3
	.word	81430
	.byte	31
	.byte	'IfxPort_State',0,8,178,1,3
	.word	10046
	.byte	31
	.byte	'IfxPort_Pin',0,8,194,1,3
	.word	35762
	.byte	21
	.word	35920
	.byte	31
	.byte	'IfxScu_Req_In',0,17,80,3
	.word	81916
	.byte	21
	.word	35920
	.byte	33
	.byte	'IfxScu_REQ0_P15_4_IN',0,17,130,1,26
	.word	81943
	.byte	1,1,21
	.word	35920
	.byte	33
	.byte	'IfxScu_REQ10_P14_3_IN',0,17,131,1,26
	.word	81980
	.byte	1,1,21
	.word	35920
	.byte	33
	.byte	'IfxScu_REQ11_P20_9_IN',0,17,132,1,26
	.word	82018
	.byte	1,1,21
	.word	35920
	.byte	33
	.byte	'IfxScu_REQ12_P11_10_IN',0,17,133,1,26
	.word	82056
	.byte	1,1,21
	.word	35920
	.byte	33
	.byte	'IfxScu_REQ13_P15_5_IN',0,17,134,1,26
	.word	82095
	.byte	1,1,21
	.word	35920
	.byte	33
	.byte	'IfxScu_REQ14_P02_1_IN',0,17,135,1,26
	.word	82133
	.byte	1,1,21
	.word	35920
	.byte	33
	.byte	'IfxScu_REQ15_P14_1_IN',0,17,136,1,26
	.word	82171
	.byte	1,1,21
	.word	35920
	.byte	33
	.byte	'IfxScu_REQ16_P15_1_IN',0,17,137,1,26
	.word	82209
	.byte	1,1,21
	.word	35920
	.byte	33
	.byte	'IfxScu_REQ1_P15_8_IN',0,17,138,1,26
	.word	82247
	.byte	1,1,21
	.word	35920
	.byte	33
	.byte	'IfxScu_REQ2_P10_2_IN',0,17,139,1,26
	.word	82284
	.byte	1,1,21
	.word	35920
	.byte	33
	.byte	'IfxScu_REQ3_P10_3_IN',0,17,140,1,26
	.word	82321
	.byte	1,1,21
	.word	35920
	.byte	33
	.byte	'IfxScu_REQ6_P02_0_IN',0,17,143,1,26
	.word	82358
	.byte	1,1,21
	.word	35920
	.byte	33
	.byte	'IfxScu_REQ7_P00_4_IN',0,17,144,1,26
	.word	82395
	.byte	1,1,21
	.word	35920
	.byte	33
	.byte	'IfxScu_REQ8_P33_7_IN',0,17,145,1,26
	.word	82432
	.byte	1,1,21
	.word	35920
	.byte	33
	.byte	'IfxScu_REQ9_P20_0_IN',0,17,146,1,26
	.word	82469
	.byte	1,1,31
	.byte	'IfxScuCcu_PllStepsFunctionHook',0,10,148,1,16
	.word	205
	.byte	20,10,212,5,9,8,13
	.byte	'value',0
	.word	10589
	.byte	4,2,35,0,13
	.byte	'mask',0
	.word	10589
	.byte	4,2,35,4,0,31
	.byte	'IfxScuCcu_CcuconRegConfig',0,10,216,5,3
	.word	82546
	.byte	20,10,221,5,9,8,13
	.byte	'pDivider',0
	.word	489
	.byte	1,2,35,0,13
	.byte	'nDivider',0
	.word	489
	.byte	1,2,35,1,13
	.byte	'k2Initial',0
	.word	489
	.byte	1,2,35,2,13
	.byte	'waitTime',0
	.word	262
	.byte	4,2,35,4,0,31
	.byte	'IfxScuCcu_InitialStepConfig',0,10,227,5,3
	.word	82617
	.byte	20,10,231,5,9,12,13
	.byte	'k2Step',0
	.word	489
	.byte	1,2,35,0,13
	.byte	'waitTime',0
	.word	262
	.byte	4,2,35,2,13
	.byte	'hookFunction',0
	.word	82506
	.byte	4,2,35,8,0,31
	.byte	'IfxScuCcu_PllStepsConfig',0,10,236,5,3
	.word	82734
	.byte	3
	.word	202
	.byte	20,10,244,5,9,48,13
	.byte	'ccucon0',0
	.word	82546
	.byte	8,2,35,0,13
	.byte	'ccucon1',0
	.word	82546
	.byte	8,2,35,8,13
	.byte	'ccucon2',0
	.word	82546
	.byte	8,2,35,16,13
	.byte	'ccucon5',0
	.word	82546
	.byte	8,2,35,24,13
	.byte	'ccucon6',0
	.word	82546
	.byte	8,2,35,32,13
	.byte	'ccucon7',0
	.word	82546
	.byte	8,2,35,40,0,31
	.byte	'IfxScuCcu_ClockDistributionConfig',0,10,252,5,3
	.word	82836
	.byte	20,10,128,6,9,8,13
	.byte	'value',0
	.word	10589
	.byte	4,2,35,0,13
	.byte	'mask',0
	.word	10589
	.byte	4,2,35,4,0,31
	.byte	'IfxScuCcu_FlashWaitstateConfig',0,10,132,6,3
	.word	82988
	.byte	3
	.word	82734
	.byte	20,10,137,6,9,16,13
	.byte	'numOfPllDividerSteps',0
	.word	489
	.byte	1,2,35,0,13
	.byte	'pllDividerStep',0
	.word	83064
	.byte	4,2,35,4,13
	.byte	'pllInitialStep',0
	.word	82617
	.byte	8,2,35,8,0,31
	.byte	'IfxScuCcu_SysPllConfig',0,10,142,6,3
	.word	83069
	.byte	15,11,144,1,9,1,16
	.byte	'IfxCpu_CounterMode_normal',0,0,16
	.byte	'IfxCpu_CounterMode_task',0,1,0,31
	.byte	'IfxCpu_CounterMode',0,11,148,1,3
	.word	83186
	.byte	20,11,160,1,9,6,13
	.byte	'counter',0
	.word	10589
	.byte	4,2,35,0,13
	.byte	'overlfow',0
	.word	489
	.byte	1,2,35,4,0,31
	.byte	'IfxCpu_Counter',0,11,164,1,3
	.word	83275
	.byte	20,11,172,1,9,32,13
	.byte	'instruction',0
	.word	83275
	.byte	6,2,35,0,13
	.byte	'clock',0
	.word	83275
	.byte	6,2,35,6,13
	.byte	'counter1',0
	.word	83275
	.byte	6,2,35,12,13
	.byte	'counter2',0
	.word	83275
	.byte	6,2,35,18,13
	.byte	'counter3',0
	.word	83275
	.byte	6,2,35,24,0,31
	.byte	'IfxCpu_Perf',0,11,179,1,3
	.word	83341
	.byte	15,13,151,1,9,1,16
	.byte	'IfxStm_Comparator_0',0,0,16
	.byte	'IfxStm_Comparator_1',0,1,0,31
	.byte	'IfxStm_Comparator',0,13,155,1,3
	.word	83459
	.byte	15,13,159,1,9,1,16
	.byte	'IfxStm_ComparatorInterrupt_ir0',0,0,16
	.byte	'IfxStm_ComparatorInterrupt_ir1',0,1,0,31
	.byte	'IfxStm_ComparatorInterrupt',0,13,163,1,3
	.word	83537
	.byte	15,13,167,1,9,1,16
	.byte	'IfxStm_ComparatorOffset_0',0,0,16
	.byte	'IfxStm_ComparatorOffset_1',0,1,16
	.byte	'IfxStm_ComparatorOffset_2',0,2,16
	.byte	'IfxStm_ComparatorOffset_3',0,3,16
	.byte	'IfxStm_ComparatorOffset_4',0,4,16
	.byte	'IfxStm_ComparatorOffset_5',0,5,16
	.byte	'IfxStm_ComparatorOffset_6',0,6,16
	.byte	'IfxStm_ComparatorOffset_7',0,7,16
	.byte	'IfxStm_ComparatorOffset_8',0,8,16
	.byte	'IfxStm_ComparatorOffset_9',0,9,16
	.byte	'IfxStm_ComparatorOffset_10',0,10,16
	.byte	'IfxStm_ComparatorOffset_11',0,11,16
	.byte	'IfxStm_ComparatorOffset_12',0,12,16
	.byte	'IfxStm_ComparatorOffset_13',0,13,16
	.byte	'IfxStm_ComparatorOffset_14',0,14,16
	.byte	'IfxStm_ComparatorOffset_15',0,15,16
	.byte	'IfxStm_ComparatorOffset_16',0,16,16
	.byte	'IfxStm_ComparatorOffset_17',0,17,16
	.byte	'IfxStm_ComparatorOffset_18',0,18,16
	.byte	'IfxStm_ComparatorOffset_19',0,19,16
	.byte	'IfxStm_ComparatorOffset_20',0,20,16
	.byte	'IfxStm_ComparatorOffset_21',0,21,16
	.byte	'IfxStm_ComparatorOffset_22',0,22,16
	.byte	'IfxStm_ComparatorOffset_23',0,23,16
	.byte	'IfxStm_ComparatorOffset_24',0,24,16
	.byte	'IfxStm_ComparatorOffset_25',0,25,16
	.byte	'IfxStm_ComparatorOffset_26',0,26,16
	.byte	'IfxStm_ComparatorOffset_27',0,27,16
	.byte	'IfxStm_ComparatorOffset_28',0,28,16
	.byte	'IfxStm_ComparatorOffset_29',0,29,16
	.byte	'IfxStm_ComparatorOffset_30',0,30,16
	.byte	'IfxStm_ComparatorOffset_31',0,31,0,31
	.byte	'IfxStm_ComparatorOffset',0,13,201,1,3
	.word	83646
	.byte	15,13,205,1,9,1,16
	.byte	'IfxStm_ComparatorSize_1Bit',0,0,16
	.byte	'IfxStm_ComparatorSize_2Bits',0,1,16
	.byte	'IfxStm_ComparatorSize_3Bits',0,2,16
	.byte	'IfxStm_ComparatorSize_4Bits',0,3,16
	.byte	'IfxStm_ComparatorSize_5Bits',0,4,16
	.byte	'IfxStm_ComparatorSize_6Bits',0,5,16
	.byte	'IfxStm_ComparatorSize_7Bits',0,6,16
	.byte	'IfxStm_ComparatorSize_8Bits',0,7,16
	.byte	'IfxStm_ComparatorSize_9Bits',0,8,16
	.byte	'IfxStm_ComparatorSize_10Bits',0,9,16
	.byte	'IfxStm_ComparatorSize_11Bits',0,10,16
	.byte	'IfxStm_ComparatorSize_12Bits',0,11,16
	.byte	'IfxStm_ComparatorSize_13Bits',0,12,16
	.byte	'IfxStm_ComparatorSize_14Bits',0,13,16
	.byte	'IfxStm_ComparatorSize_15Bits',0,14,16
	.byte	'IfxStm_ComparatorSize_16Bits',0,15,16
	.byte	'IfxStm_ComparatorSize_17Bits',0,16,16
	.byte	'IfxStm_ComparatorSize_18Bits',0,17,16
	.byte	'IfxStm_ComparatorSize_19Bits',0,18,16
	.byte	'IfxStm_ComparatorSize_20Bits',0,19,16
	.byte	'IfxStm_ComparatorSize_21Bits',0,20,16
	.byte	'IfxStm_ComparatorSize_22Bits',0,21,16
	.byte	'IfxStm_ComparatorSize_23Bits',0,22,16
	.byte	'IfxStm_ComparatorSize_24Bits',0,23,16
	.byte	'IfxStm_ComparatorSize_25Bits',0,24,16
	.byte	'IfxStm_ComparatorSize_26Bits',0,25,16
	.byte	'IfxStm_ComparatorSize_27Bits',0,26,16
	.byte	'IfxStm_ComparatorSize_28Bits',0,27,16
	.byte	'IfxStm_ComparatorSize_29Bits',0,28,16
	.byte	'IfxStm_ComparatorSize_30Bits',0,29,16
	.byte	'IfxStm_ComparatorSize_31Bits',0,30,16
	.byte	'IfxStm_ComparatorSize_32Bits',0,31,0,31
	.byte	'IfxStm_ComparatorSize',0,13,239,1,3
	.word	84604
	.byte	15,13,244,1,9,1,16
	.byte	'IfxStm_SleepMode_enable',0,0,16
	.byte	'IfxStm_SleepMode_disable',0,1,0,31
	.byte	'IfxStm_SleepMode',0,13,248,1,3
	.word	85624
	.byte	15,13,252,1,9,1,16
	.byte	'IfxStm_SuspendMode_none',0,0,16
	.byte	'IfxStm_SuspendMode_hard',0,1,16
	.byte	'IfxStm_SuspendMode_soft',0,2,0,31
	.byte	'IfxStm_SuspendMode',0,13,129,2,3
	.word	85710
	.byte	31
	.byte	'_iob_flag_t',0,26,82,25
	.word	1059
	.byte	31
	.byte	'int8',0,27,54,29
	.word	39704
	.byte	31
	.byte	'int16',0,27,55,29
	.word	40269
	.byte	31
	.byte	'int32',0,27,56,29
	.word	482
	.byte	31
	.byte	'int64',0,27,57,29
	.word	14466
	.byte	31
	.byte	'IfxScuEru_ExternalInputSelection',0,16,88,3
	.word	38106
	.byte	31
	.byte	'IfxScuEru_InputChannel',0,16,102,3
	.word	37884
	.byte	31
	.byte	'IfxScuEru_InputNodePointer',0,16,117,3
	.word	38619
	.byte	31
	.byte	'IfxScuEru_InterruptGatingPattern',0,16,128,1,3
	.word	39423
	.byte	31
	.byte	'IfxScuEru_OutputChannel',0,16,142,1,3
	.word	39014
	.byte	31
	.byte	'exti_pin_enum',0,20,61,2
	.word	39712
	.byte	31
	.byte	'exti_trigger_enum',0,20,70,2
	.word	40041
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L68:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,21,0,54,15,39,12,0,0,3,15,0,73,19,0,0,4,46,1,3,8,32,13
	.byte	58,15,59,15,57,15,54,15,39,12,0,0,5,5,0,3,8,58,15,59,15,57,15,73,19,0,0,6,11,0,0,0,7,36,0,3,8,11,15,62
	.byte	15,0,0,8,46,1,3,8,32,13,58,15,59,15,57,15,73,19,54,15,39,12,0,0,9,59,0,3,8,0,0,10,19,1,3,8,58,15,59,15
	.byte	57,15,11,15,0,0,11,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,12,23,1,58,15,59,15,57,15,11,15,0,0,13,13
	.byte	0,3,8,73,19,11,15,56,9,0,0,14,53,0,73,19,0,0,15,4,1,58,15,59,15,57,15,11,15,0,0,16,40,0,3,8,28,13,0,0
	.byte	17,11,1,0,0,18,1,1,11,15,73,19,0,0,19,33,0,47,15,0,0,20,19,1,58,15,59,15,57,15,11,15,0,0,21,38,0,73,19
	.byte	0,0,22,46,1,3,8,73,19,54,15,39,12,63,12,60,12,0,0,23,5,0,73,19,0,0,24,46,0,3,8,54,15,39,12,63,12,60,12
	.byte	0,0,25,46,1,49,19,0,0,26,5,0,49,19,0,0,27,29,1,49,19,0,0,28,11,0,49,19,0,0,29,46,1,3,8,58,15,59,15,57
	.byte	15,54,15,39,12,63,12,60,12,0,0,30,11,1,49,19,0,0,31,22,0,3,8,58,15,59,15,57,15,73,19,0,0,32,21,0,54,15
	.byte	0,0,33,52,0,3,8,58,15,59,15,57,15,73,19,63,12,60,12,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L69:
	.word	.L282-.L281
.L281:
	.half	3
	.word	.L284-.L283
.L283:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common',0
	.byte	'F:\\ADS\\AURIX-Studio-1.10.2\\tools\\Compilers\\Tasking_1.1r8\\ctc\\include\\',0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_Intrinsics.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Src\\Std\\IfxSrc.h',0
	.byte	0,0,0
	.byte	'IfxSrc_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxSrc_cfg.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Scu\\Std\\IfxScuWdt.h',0
	.byte	0,0,0
	.byte	'IfxScu_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Port\\Std\\IfxPort.h',0
	.byte	0,0,0
	.byte	'IfxPort_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Scu\\Std\\IfxScuCcu.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxCpu_cfg.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Stm\\Std\\IfxStm.h',0
	.byte	0,0,0
	.byte	'IfxStm_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\SysSe\\Bsp\\Bsp.h',0
	.byte	0,0,0
	.byte	'IfxScuEru.h',0,2,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_PinMap\\IfxScu_PinMap.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Ifx_Types.h',0
	.byte	0,0,0
	.byte	'zf_common_debug.h',0,3,0,0
	.byte	'..\\libraries\\zf_driver\\zf_driver_exti.h',0,0,0,0
	.byte	'IfxCpu_regdef.h',0,1,0,0
	.byte	'../libraries/zf_driver/zf_driver_exti.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Platform_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxScu_cfg.h',0
	.byte	0,0,0
	.byte	'IfxFlash_regdef.h',0,1,0,0
	.byte	'stdio.h',0,4,0,0
	.byte	'zf_common_typedef.h',0,3,0,0,0
.L284:
.L282:
	.sdecl	'.debug_info',debug,cluster('exti_all_close')
	.sect	'.debug_info'
.L70:
	.word	325
	.half	3
	.word	.L71
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_exti.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L73,.L72
	.byte	2
	.word	.L66
	.byte	3
	.byte	'exti_all_close',0,1,112,6,1,1,1
	.word	.L63,.L95,.L62
	.byte	4
	.word	.L63,.L95
	.byte	5
	.byte	'src',0,1,114,28
	.word	.L96,.L97
	.byte	5
	.byte	'channel',0,1,115,10
	.word	.L98,.L99
	.byte	6
	.word	.L100,.L101,.L102
	.byte	7
	.word	.L103,.L104
	.byte	8
	.word	.L105,.L101,.L102
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('exti_all_close')
	.sect	'.debug_abbrev'
.L71:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,6,29,1
	.byte	49,16,17,1,18,1,0,0,7,5,0,49,16,2,6,0,0,8,11,0,49,16,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('exti_all_close')
	.sect	'.debug_line'
.L72:
	.word	.L286-.L285
.L285:
	.half	3
	.word	.L288-.L287
.L287:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_exti.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Src\\Std\\IfxSrc.h',0
	.byte	0,0,0,0
.L288:
	.byte	5,17,7,0,5,2
	.word	.L63
	.byte	3,243,0,1,5,33,9
	.half	.L222-.L63
	.byte	1,5,38,9
	.half	.L35-.L222
	.byte	3,2,1,4,2,5,14,9
	.half	.L101-.L35
	.byte	3,140,1,1,5,12,9
	.half	.L289-.L101
	.byte	1,4,1,5,43,9
	.half	.L102-.L289
	.byte	3,242,126,1,5,33,9
	.half	.L34-.L102
	.byte	1,5,1,7,9
	.half	.L290-.L34
	.byte	3,5,1,7,9
	.half	.L74-.L290
	.byte	0,1,1
.L286:
	.sdecl	'.debug_ranges',debug,cluster('exti_all_close')
	.sect	'.debug_ranges'
.L73:
	.word	-1,.L63,0,.L74-.L63,0,0
	.sdecl	'.debug_info',debug,cluster('exti_enable')
	.sect	'.debug_info'
.L75:
	.word	348
	.half	3
	.word	.L76
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_exti.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L78,.L77
	.byte	2
	.word	.L66
	.byte	3
	.byte	'exti_enable',0,1,84,6,1,1,1
	.word	.L59,.L106,.L58
	.byte	4
	.byte	'eru_pin',0,1,84,33
	.word	.L107,.L108
	.byte	5
	.word	.L59,.L106
	.byte	6
	.byte	'outputChannel',0,1,86,32
	.word	.L109,.L110
	.byte	6
	.byte	'src',0,1,88,28
	.word	.L96,.L111
	.byte	7
	.word	.L112,.L113,.L114
	.byte	8
	.word	.L115,.L116
	.byte	9
	.word	.L117,.L113,.L114
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('exti_enable')
	.sect	'.debug_abbrev'
.L76:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,5,0,49,16,2,6,0,0,9,11,0,49,16,17,1,18
	.byte	1,0,0,0
	.sdecl	'.debug_line',debug,cluster('exti_enable')
	.sect	'.debug_line'
.L77:
	.word	.L292-.L291
.L291:
	.half	3
	.word	.L294-.L293
.L293:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_exti.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Src\\Std\\IfxSrc.h',0
	.byte	0,0,0,0
.L294:
	.byte	5,82,7,0,5,2
	.word	.L59
	.byte	3,213,0,1,5,81,9
	.half	.L295-.L59
	.byte	1,5,79,9
	.half	.L218-.L295
	.byte	3,2,1,5,77,9
	.half	.L296-.L218
	.byte	1,5,57,9
	.half	.L219-.L296
	.byte	1,4,2,5,11,9
	.half	.L113-.L219
	.byte	3,182,1,1,5,16,9
	.half	.L297-.L113
	.byte	1,4,1,5,1,9
	.half	.L114-.L297
	.byte	3,204,126,1,7,9
	.half	.L79-.L114
	.byte	0,1,1
.L292:
	.sdecl	'.debug_ranges',debug,cluster('exti_enable')
	.sect	'.debug_ranges'
.L78:
	.word	-1,.L59,0,.L79-.L59,0,0
	.sdecl	'.debug_info',debug,cluster('exti_disable')
	.sect	'.debug_info'
.L80:
	.word	349
	.half	3
	.word	.L81
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_exti.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L83,.L82
	.byte	2
	.word	.L66
	.byte	3
	.byte	'exti_disable',0,1,99,6,1,1,1
	.word	.L61,.L118,.L60
	.byte	4
	.byte	'eru_pin',0,1,99,34
	.word	.L107,.L119
	.byte	5
	.word	.L61,.L118
	.byte	6
	.byte	'outputChannel',0,1,101,32
	.word	.L109,.L120
	.byte	6
	.byte	'src',0,1,103,28
	.word	.L96,.L121
	.byte	7
	.word	.L122,.L123,.L124
	.byte	8
	.word	.L125,.L126
	.byte	9
	.word	.L127,.L123,.L124
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('exti_disable')
	.sect	'.debug_abbrev'
.L81:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,5,0,49,16,2,6,0,0,9,11,0,49,16,17,1,18
	.byte	1,0,0,0
	.sdecl	'.debug_line',debug,cluster('exti_disable')
	.sect	'.debug_line'
.L82:
	.word	.L299-.L298
.L298:
	.half	3
	.word	.L301-.L300
.L300:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_exti.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Src\\Std\\IfxSrc.h',0
	.byte	0,0,0,0
.L301:
	.byte	5,82,7,0,5,2
	.word	.L61
	.byte	3,228,0,1,5,81,9
	.half	.L302-.L61
	.byte	1,5,79,9
	.half	.L220-.L302
	.byte	3,2,1,5,77,9
	.half	.L303-.L220
	.byte	1,5,57,9
	.half	.L221-.L303
	.byte	1,4,2,5,11,9
	.half	.L123-.L221
	.byte	3,161,1,1,5,16,9
	.half	.L304-.L123
	.byte	1,4,1,5,1,9
	.half	.L124-.L304
	.byte	3,225,126,1,7,9
	.half	.L84-.L124
	.byte	0,1,1
.L299:
	.sdecl	'.debug_ranges',debug,cluster('exti_disable')
	.sect	'.debug_ranges'
.L83:
	.word	-1,.L61,0,.L84-.L61,0,0
	.sdecl	'.debug_info',debug,cluster('exti_init')
	.sect	'.debug_info'
.L85:
	.word	933
	.half	3
	.word	.L86
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_exti.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L88,.L87
	.byte	2
	.word	.L66
	.byte	3
	.byte	'exti_init',0,1,130,1,6,1,1,1
	.word	.L65,.L128,.L64
	.byte	4
	.byte	'exti_pin',0,1,130,1,31
	.word	.L107,.L129
	.byte	4
	.byte	'trigger',0,1,130,1,59
	.word	.L130,.L131
	.byte	5
	.word	.L65,.L128
	.byte	6
	.byte	'interrupt_state',0,1,132,1,13
	.word	.L132,.L133
	.byte	6
	.byte	'reqPin',0,1,134,1,20
	.word	.L134,.L135
	.byte	7
	.word	.L136,.L137,.L38
	.byte	8
	.word	.L138,.L137,.L38
	.byte	7
	.word	.L139,.L137,.L37
	.byte	8
	.word	.L140,.L137,.L37
	.byte	6
	.byte	'enabled',0,2,149,5,13
	.word	.L132,.L141
	.byte	7
	.word	.L142,.L137,.L36
	.byte	8
	.word	.L143,.L137,.L36
	.byte	6
	.byte	'reg',0,2,141,5,17
	.word	.L144,.L145
	.byte	0,0,0,0,0,0,7
	.word	.L146,.L147,.L148
	.byte	9
	.word	.L149,.L150
	.byte	9
	.word	.L151,.L152
	.byte	8
	.word	.L153,.L147,.L148
	.byte	7
	.word	.L154,.L155,.L156
	.byte	9
	.word	.L157,.L158
	.byte	9
	.word	.L159,.L160
	.byte	9
	.word	.L161,.L162
	.byte	10
	.word	.L163,.L155,.L156
	.byte	0,0,0,5
	.word	.L148,.L128
	.byte	6
	.byte	'inputChannel',0,1,140,1,28
	.word	.L164,.L165
	.byte	6
	.byte	'triggerSelect',0,1,142,1,32
	.word	.L166,.L167
	.byte	6
	.byte	'outputChannel',0,1,143,1,32
	.word	.L109,.L168
	.byte	5
	.word	.L169,.L128
	.byte	6
	.byte	'src',0,1,176,1,28
	.word	.L96,.L170
	.byte	6
	.byte	'exit_service',0,1,177,1,16
	.word	.L171,.L172
	.byte	6
	.byte	'exit_priority',0,1,178,1,11
	.word	.L132,.L173
	.byte	7
	.word	.L174,.L51,.L175
	.byte	9
	.word	.L176,.L177
	.byte	9
	.word	.L178,.L179
	.byte	9
	.word	.L180,.L181
	.byte	8
	.word	.L182,.L51,.L175
	.byte	7
	.word	.L183,.L184,.L175
	.byte	9
	.word	.L185,.L186
	.byte	10
	.word	.L187,.L184,.L175
	.byte	0,0,0,7
	.word	.L112,.L175,.L188
	.byte	9
	.word	.L115,.L189
	.byte	10
	.word	.L117,.L175,.L188
	.byte	0,7
	.word	.L190,.L191,.L55
	.byte	9
	.word	.L192,.L193
	.byte	8
	.word	.L194,.L191,.L55
	.byte	7
	.word	.L195,.L191,.L55
	.byte	9
	.word	.L196,.L197
	.byte	10
	.word	.L198,.L191,.L55
	.byte	0,0,0,0,0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('exti_init')
	.sect	'.debug_abbrev'
.L86:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,11,1,49,16,17,1,18,1,0,0,9,5,0,49,16,2
	.byte	6,0,0,10,11,0,49,16,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('exti_init')
	.sect	'.debug_line'
.L87:
	.word	.L306-.L305
.L305:
	.half	3
	.word	.L308-.L307
.L307:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std',0
	.byte	0
	.byte	'../libraries/zf_driver/zf_driver_exti.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\SysSe\\Bsp\\Bsp.h',0
	.byte	0,0,0
	.byte	'IfxScuEru.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Port\\Std\\IfxPort.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Src\\Std\\IfxSrc.h',0
	.byte	0,0,0,0
.L308:
	.byte	5,6,7,0,5,2
	.word	.L65
	.byte	3,129,1,1,4,2,5,19,9
	.half	.L137-.L65
	.byte	3,140,4,1,5,17,9
	.half	.L225-.L137
	.byte	3,1,1,5,21,9
	.half	.L226-.L225
	.byte	1,5,5,9
	.half	.L227-.L226
	.byte	1,5,14,9
	.half	.L36-.L227
	.byte	3,8,1,5,10,9
	.half	.L309-.L36
	.byte	3,1,1,5,5,9
	.half	.L310-.L309
	.byte	3,1,1,4,3,9
	.half	.L37-.L310
	.byte	3,213,123,1,4,1,5,27,9
	.half	.L38-.L37
	.byte	3,26,1,5,12,9
	.half	.L224-.L38
	.byte	1,5,34,9
	.half	.L229-.L224
	.byte	3,2,1,4,4,5,37,9
	.half	.L147-.L229
	.byte	3,214,1,1,5,52,9
	.half	.L311-.L147
	.byte	1,4,5,5,40,9
	.half	.L155-.L311
	.byte	3,230,1,1,4,4,5,62,9
	.half	.L156-.L155
	.byte	3,155,126,1,5,112,9
	.half	.L312-.L156
	.byte	1,5,75,9
	.half	.L313-.L312
	.byte	1,4,1,5,73,9
	.half	.L148-.L313
	.byte	3,171,126,1,5,86,9
	.half	.L233-.L148
	.byte	3,2,1,5,85,9
	.half	.L232-.L233
	.byte	1,5,83,9
	.half	.L234-.L232
	.byte	3,1,1,5,82,9
	.half	.L235-.L234
	.byte	1,5,14,9
	.half	.L236-.L235
	.byte	3,4,1,9
	.half	.L238-.L236
	.byte	3,6,1,9
	.half	.L240-.L238
	.byte	3,6,1,5,51,9
	.half	.L39-.L240
	.byte	3,118,1,5,49,9
	.half	.L244-.L39
	.byte	3,1,1,5,10,9
	.half	.L246-.L244
	.byte	3,1,1,5,50,9
	.half	.L40-.L246
	.byte	3,4,1,9
	.half	.L248-.L40
	.byte	3,1,1,5,10,9
	.half	.L250-.L248
	.byte	3,1,1,5,50,9
	.half	.L41-.L250
	.byte	3,4,1,5,49,9
	.half	.L252-.L41
	.byte	3,1,1,5,10,9
	.half	.L254-.L252
	.byte	3,1,1,5,18,9
	.half	.L42-.L254
	.byte	3,2,1,5,34,9
	.half	.L43-.L42
	.byte	3,3,1,5,44,9
	.half	.L256-.L43
	.byte	3,1,1,5,68,9
	.half	.L258-.L256
	.byte	3,2,1,5,45,9
	.half	.L261-.L258
	.byte	3,1,1,5,56,9
	.half	.L264-.L261
	.byte	3,1,1,5,79,9
	.half	.L169-.L264
	.byte	3,3,1,5,77,9
	.half	.L314-.L169
	.byte	1,5,57,9
	.half	.L315-.L314
	.byte	1,5,22,9
	.half	.L266-.L315
	.byte	3,3,1,5,21,9
	.half	.L267-.L266
	.byte	1,5,25,9
	.half	.L268-.L267
	.byte	1,5,24,9
	.half	.L316-.L268
	.byte	1,5,14,9
	.half	.L317-.L316
	.byte	3,2,1,9
	.half	.L318-.L317
	.byte	3,6,1,9
	.half	.L319-.L318
	.byte	3,6,1,9
	.half	.L320-.L319
	.byte	3,6,1,5,27,9
	.half	.L46-.L320
	.byte	3,112,1,9
	.half	.L269-.L46
	.byte	3,1,1,5,10,9
	.half	.L270-.L269
	.byte	3,1,1,5,27,9
	.half	.L47-.L270
	.byte	3,4,1,9
	.half	.L271-.L47
	.byte	3,1,1,5,10,9
	.half	.L272-.L271
	.byte	3,1,1,5,27,9
	.half	.L48-.L272
	.byte	3,4,1,9
	.half	.L273-.L48
	.byte	3,1,1,5,10,9
	.half	.L274-.L273
	.byte	3,1,1,5,27,9
	.half	.L49-.L274
	.byte	3,4,1,9
	.half	.L275-.L49
	.byte	3,1,1,5,10,9
	.half	.L276-.L275
	.byte	3,1,1,4,6,5,11,9
	.half	.L51-.L276
	.byte	3,201,0,1,5,17,9
	.half	.L277-.L51
	.byte	1,5,11,9
	.half	.L321-.L277
	.byte	3,1,1,5,17,9
	.half	.L279-.L321
	.byte	1,5,11,9
	.half	.L184-.L279
	.byte	3,103,1,5,17,9
	.half	.L322-.L184
	.byte	1,5,11,9
	.half	.L175-.L322
	.byte	3,18,1,5,16,9
	.half	.L323-.L175
	.byte	1,4,1,5,23,9
	.half	.L188-.L323
	.byte	3,69,1,4,2,5,5,9
	.half	.L191-.L188
	.byte	3,215,5,1,5,17,7,9
	.half	.L324-.L191
	.byte	3,2,1,4,1,5,1,9
	.half	.L55-.L324
	.byte	3,168,122,1,7,9
	.half	.L89-.L55
	.byte	0,1,1
.L306:
	.sdecl	'.debug_ranges',debug,cluster('exti_init')
	.sect	'.debug_ranges'
.L88:
	.word	-1,.L65,0,.L89-.L65,0,0
	.sdecl	'.debug_info',debug,cluster('get_exit_pin')
	.sect	'.debug_info'
.L90:
	.word	307
	.half	3
	.word	.L91
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_exti.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L93,.L92
	.byte	2
	.word	.L66
	.byte	3
	.byte	'get_exit_pin',0,1,48,23
	.word	.L134
	.byte	1,1
	.word	.L57,.L199,.L56
	.byte	4
	.byte	'exti_pin',0,1,48,51
	.word	.L107,.L200
	.byte	5
	.word	.L57,.L199
	.byte	6
	.byte	'get_exit_pin_config',0,1,50,20
	.word	.L134,.L201
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('get_exit_pin')
	.sect	'.debug_abbrev'
.L91:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('get_exit_pin')
	.sect	'.debug_line'
.L92:
	.word	.L326-.L325
.L325:
	.half	3
	.word	.L328-.L327
.L327:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_exti.c',0,0,0,0,0
.L328:
	.byte	5,14,7,0,5,2
	.word	.L57
	.byte	3,53,1,9
	.half	.L329-.L57
	.byte	3,1,1,9
	.half	.L330-.L329
	.byte	3,1,1,9
	.half	.L331-.L330
	.byte	3,1,1,9
	.half	.L332-.L331
	.byte	3,1,1,9
	.half	.L333-.L332
	.byte	3,1,1,9
	.half	.L334-.L333
	.byte	3,1,1,9
	.half	.L335-.L334
	.byte	3,1,1,9
	.half	.L336-.L335
	.byte	3,1,1,9
	.half	.L337-.L336
	.byte	3,1,1,9
	.half	.L338-.L337
	.byte	3,1,1,9
	.half	.L339-.L338
	.byte	3,1,1,9
	.half	.L340-.L339
	.byte	3,1,1,9
	.half	.L341-.L340
	.byte	3,1,1,9
	.half	.L342-.L341
	.byte	3,1,1,5,60,9
	.half	.L2-.L342
	.byte	3,114,1,5,84,9
	.half	.L343-.L2
	.byte	1,5,60,9
	.half	.L3-.L343
	.byte	3,1,1,5,84,9
	.half	.L344-.L3
	.byte	1,5,60,9
	.half	.L4-.L344
	.byte	3,1,1,5,84,9
	.half	.L345-.L4
	.byte	1,5,60,9
	.half	.L5-.L345
	.byte	3,1,1,5,84,9
	.half	.L346-.L5
	.byte	1,5,60,9
	.half	.L6-.L346
	.byte	3,1,1,5,84,9
	.half	.L347-.L6
	.byte	1,5,60,9
	.half	.L7-.L347
	.byte	3,1,1,5,84,9
	.half	.L348-.L7
	.byte	1,5,60,9
	.half	.L8-.L348
	.byte	3,1,1,5,84,9
	.half	.L349-.L8
	.byte	1,5,60,9
	.half	.L9-.L349
	.byte	3,1,1,5,84,9
	.half	.L350-.L9
	.byte	1,5,60,9
	.half	.L10-.L350
	.byte	3,1,1,5,84,9
	.half	.L351-.L10
	.byte	1,5,60,9
	.half	.L11-.L351
	.byte	3,1,1,5,84,9
	.half	.L352-.L11
	.byte	1,5,60,9
	.half	.L12-.L352
	.byte	3,1,1,5,84,9
	.half	.L353-.L12
	.byte	1,5,60,9
	.half	.L13-.L353
	.byte	3,1,1,5,84,9
	.half	.L354-.L13
	.byte	1,5,60,9
	.half	.L14-.L354
	.byte	3,1,1,5,84,9
	.half	.L355-.L14
	.byte	1,5,60,9
	.half	.L15-.L355
	.byte	3,1,1,5,84,9
	.half	.L356-.L15
	.byte	1,5,60,9
	.half	.L16-.L356
	.byte	3,1,1,5,84,9
	.half	.L357-.L16
	.byte	1,5,18,9
	.half	.L17-.L357
	.byte	3,1,1,5,56,9
	.half	.L358-.L17
	.byte	1,5,5,9
	.half	.L18-.L358
	.byte	3,3,1,5,1,9
	.half	.L33-.L18
	.byte	3,1,1,7,9
	.half	.L94-.L33
	.byte	0,1,1
.L326:
	.sdecl	'.debug_ranges',debug,cluster('get_exit_pin')
	.sect	'.debug_ranges'
.L93:
	.word	-1,.L57,0,.L94-.L57,0,0
	.sdecl	'.debug_loc',debug,cluster('exti_all_close')
	.sect	'.debug_loc'
.L99:
	.word	-1,.L63,.L222-.L63,.L95-.L63
	.half	1
	.byte	95
	.word	0,0
.L62:
	.word	-1,.L63,0,.L95-.L63
	.half	2
	.byte	138,0
	.word	0,0
.L97:
	.word	-1,.L63,.L101-.L63,.L34-.L63
	.half	1
	.byte	111
	.word	0,0
.L104:
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('exti_disable')
	.sect	'.debug_loc'
.L119:
	.word	-1,.L61,0,.L118-.L61
	.half	1
	.byte	84
	.word	0,0
.L60:
	.word	-1,.L61,0,.L118-.L61
	.half	2
	.byte	138,0
	.word	0,0
.L120:
	.word	-1,.L61,.L220-.L61,.L221-.L61
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L121:
	.word	-1,.L61,.L123-.L61,.L118-.L61
	.half	1
	.byte	111
	.word	0,0
.L126:
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('exti_enable')
	.sect	'.debug_loc'
.L108:
	.word	-1,.L59,0,.L106-.L59
	.half	1
	.byte	84
	.word	0,0
.L58:
	.word	-1,.L59,0,.L106-.L59
	.half	2
	.byte	138,0
	.word	0,0
.L110:
	.word	-1,.L59,.L218-.L59,.L219-.L59
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L111:
	.word	-1,.L59,.L113-.L59,.L106-.L59
	.half	1
	.byte	111
	.word	0,0
.L116:
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('exti_init')
	.sect	'.debug_loc'
.L193:
	.word	0,0
.L141:
	.word	0,0
.L197:
	.word	0,0
.L173:
	.word	-1,.L65,.L270-.L65,.L47-.L65
	.half	1
	.byte	95
	.word	.L272-.L65,.L48-.L65
	.half	1
	.byte	95
	.word	.L274-.L65,.L49-.L65
	.half	1
	.byte	95
	.word	.L276-.L65,.L51-.L65
	.half	1
	.byte	95
	.word	.L277-.L65,.L278-.L65
	.half	1
	.byte	95
	.word	0,0
.L172:
	.word	-1,.L65,.L269-.L65,.L47-.L65
	.half	5
	.byte	144,32,157,32,0
	.word	.L271-.L65,.L48-.L65
	.half	5
	.byte	144,32,157,32,0
	.word	.L273-.L65,.L49-.L65
	.half	5
	.byte	144,32,157,32,0
	.word	.L275-.L65,.L51-.L65
	.half	5
	.byte	144,32,157,32,0
	.word	.L279-.L65,.L280-.L65
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L64:
	.word	-1,.L65,0,.L223-.L65
	.half	2
	.byte	138,0
	.word	.L223-.L65,.L128-.L65
	.half	2
	.byte	138,8
	.word	.L128-.L65,.L128-.L65
	.half	2
	.byte	138,0
	.word	0,0
.L129:
	.word	-1,.L65,0,.L224-.L65
	.half	1
	.byte	84
	.word	.L38-.L65,.L228-.L65
	.half	1
	.byte	90
	.word	.L232-.L65,.L234-.L65
	.half	1
	.byte	90
	.word	.L235-.L65,.L236-.L65
	.half	1
	.byte	90
	.word	.L267-.L65,.L268-.L65
	.half	1
	.byte	90
	.word	0,0
.L165:
	.word	-1,.L65,.L231-.L65,.L232-.L65
	.half	1
	.byte	95
	.word	.L233-.L65,.L128-.L65
	.half	2
	.byte	145,120
	.word	.L243-.L65,.L244-.L65
	.half	1
	.byte	84
	.word	.L245-.L65,.L246-.L65
	.half	1
	.byte	84
	.word	.L247-.L65,.L248-.L65
	.half	1
	.byte	84
	.word	.L249-.L65,.L250-.L65
	.half	1
	.byte	84
	.word	.L251-.L65,.L252-.L65
	.half	1
	.byte	84
	.word	.L253-.L65,.L254-.L65
	.half	1
	.byte	84
	.word	.L255-.L65,.L256-.L65
	.half	1
	.byte	84
	.word	.L257-.L65,.L258-.L65
	.half	1
	.byte	84
	.word	.L262-.L65,.L261-.L65
	.half	1
	.byte	85
	.word	0,0
.L152:
	.word	0,0
.L133:
	.word	-1,.L65,.L227-.L65,.L128-.L65
	.half	1
	.byte	94
	.word	0,0
.L162:
	.word	0,0
.L168:
	.word	-1,.L65,.L236-.L65,.L128-.L65
	.half	1
	.byte	88
	.word	.L260-.L65,.L261-.L65
	.half	1
	.byte	84
	.word	.L263-.L65,.L264-.L65
	.half	1
	.byte	84
	.word	.L265-.L65,.L169-.L65
	.half	1
	.byte	84
	.word	0,0
.L160:
	.word	0,0
.L158:
	.word	0,0
.L181:
	.word	0,0
.L145:
	.word	-1,.L65,.L225-.L65,.L226-.L65
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L150:
	.word	0,0
.L135:
	.word	-1,.L65,.L224-.L65,.L156-.L65
	.half	1
	.byte	98
	.word	.L229-.L65,.L230-.L65
	.half	1
	.byte	111
	.word	0,0
.L170:
	.word	-1,.L65,.L266-.L65,.L128-.L65
	.half	1
	.byte	111
	.word	0,0
.L186:
	.word	0,0
.L189:
	.word	0,0
.L177:
	.word	0,0
.L131:
	.word	-1,.L65,0,.L224-.L65
	.half	1
	.byte	85
	.word	.L237-.L65,.L238-.L65
	.half	1
	.byte	91
	.word	.L239-.L65,.L240-.L65
	.half	1
	.byte	91
	.word	.L241-.L65,.L242-.L65
	.half	1
	.byte	91
	.word	0,0
.L167:
	.word	-1,.L65,.L234-.L65,.L128-.L65
	.half	1
	.byte	92
	.word	.L259-.L65,.L258-.L65
	.half	1
	.byte	85
	.word	0,0
.L179:
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('get_exit_pin')
	.sect	'.debug_loc'
.L200:
	.word	-1,.L57,0,.L202-.L57
	.half	1
	.byte	84
	.word	0,0
.L56:
	.word	-1,.L57,0,.L199-.L57
	.half	2
	.byte	138,0
	.word	0,0
.L201:
	.word	-1,.L57,.L203-.L57,.L3-.L57
	.half	1
	.byte	98
	.word	.L204-.L57,.L4-.L57
	.half	1
	.byte	98
	.word	.L205-.L57,.L5-.L57
	.half	1
	.byte	98
	.word	.L206-.L57,.L6-.L57
	.half	1
	.byte	98
	.word	.L207-.L57,.L7-.L57
	.half	1
	.byte	98
	.word	.L208-.L57,.L8-.L57
	.half	1
	.byte	98
	.word	.L209-.L57,.L9-.L57
	.half	1
	.byte	98
	.word	.L210-.L57,.L10-.L57
	.half	1
	.byte	98
	.word	.L211-.L57,.L11-.L57
	.half	1
	.byte	98
	.word	.L212-.L57,.L12-.L57
	.half	1
	.byte	98
	.word	.L213-.L57,.L13-.L57
	.half	1
	.byte	98
	.word	.L214-.L57,.L14-.L57
	.half	1
	.byte	98
	.word	.L215-.L57,.L15-.L57
	.half	1
	.byte	98
	.word	.L216-.L57,.L16-.L57
	.half	1
	.byte	98
	.word	.L217-.L57,.L17-.L57
	.half	1
	.byte	98
	.word	.L18-.L57,.L199-.L57
	.half	1
	.byte	98
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L359:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('get_exit_pin')
	.sect	'.debug_frame'
	.word	12
	.word	.L359,.L57,.L199-.L57
	.sdecl	'.debug_frame',debug,cluster('exti_enable')
	.sect	'.debug_frame'
	.word	24
	.word	.L359,.L59,.L106-.L59
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('exti_disable')
	.sect	'.debug_frame'
	.word	24
	.word	.L359,.L61,.L118-.L61
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('exti_all_close')
	.sect	'.debug_frame'
	.word	24
	.word	.L359,.L63,.L95-.L63
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('exti_init')
	.sect	'.debug_frame'
	.word	36
	.word	.L359,.L65,.L128-.L65
	.byte	4
	.word	(.L223-.L65)/2
	.byte	19,8,22,26,3,19,138,8,4
	.word	(.L128-.L223)/2
	.byte	19,0,8,26,0,0
	; Module end
