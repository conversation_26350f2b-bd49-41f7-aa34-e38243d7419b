 <Root>
  <Options>
   <Editor>
    <EditorOptions>
     <REG_HEADER>
      <Title>BF283F24-70B9-4f97-86E6-0AD5BAAE9E01</Title>
      <VersionHigh>0</VersionHigh>
      <VersionLow>1</VersionLow>
     </REG_HEADER>
     <REG_ROOT>
      <RegVer>**********</RegVer>
     </REG_ROOT>
    </EditorOptions>
   </Editor>
   <LA>
    <LAOptions>
     <REG_HEADER>
      <Title>BF283F24-70B9-4f97-86E6-0AD5BAAE9E01</Title>
      <VersionHigh>0</VersionHigh>
      <VersionLow>1</VersionLow>
     </REG_HEADER>
     <REG_ROOT>
      <RegVer>**********</RegVer>
      <WorkspaceOptions>
       <Debug_Breakpoints>
        <ProfilerAnalysisSample>18446744073709551615</ProfilerAnalysisSample>
        <ProfilerStatTime>-9223372036854775808</ProfilerStatTime>
       </Debug_Breakpoints>
       <Debug_SessionDumpFilter>
        <Enabled>false</Enabled>
        <SampleRangeBegin>0</SampleRangeBegin>
        <SampleRangeEnd>0</SampleRangeEnd>
       </Debug_SessionDumpFilter>
      </WorkspaceOptions>
     </REG_ROOT>
    </LAOptions>
   </LA>
  </Options>
  <winIDEA>
   <REG_HEADER>
    <Title>BF283F24-70B9-4f97-86E6-0AD5BAAE9E01</Title>
    <VersionHigh>0</VersionHigh>
    <VersionLow>1</VersionLow>
   </REG_HEADER>
   <REG_ROOT>
    <HServer>HASYST</HServer>
    <RegVer>**********</RegVer>
    <Sec4>72226310959049B83990C5F7DCDECAC972226310959049B83990C5F7DCDECAC972226310959049B83990C5F7DCDECAC9C60C5F397E632F0A3646E6691DAF3254</Sec4>
    <Cfg_License>
     <Source>HW</Source>
     <Floating>
      <Edition>Standard</Edition>
      <TLM>
       <FloatingServer></FloatingServer>
       <Host>Floating</Host>
       <Key></Key>
       <Location>Auto</Location>
       <NodeLockLicense></NodeLockLicense>
       <Path></Path>
      </TLM>
     </Floating>
    </Cfg_License>
    <Cfg_System>
     <DebugModel>Basic</DebugModel>
     <Debug>
      <Applications>
       <__.0>
        <Name>App</Name>
        <Directories>
         <AltPaths>false</AltPaths>
         <ConvertFromTo></ConvertFromTo>
         <ConvertPath>false</ConvertPath>
         <FindPriority>DLFile</FindPriority>
         <FindSources>true</FindSources>
        </Directories>
        <MemProp_ISA>
         <IgnoreDLInfo>false</IgnoreDLInfo>
        </MemProp_ISA>
        <OS>
         <Type>None</Type>
         <FreeRTOS>
          <ProfilerXMLPath></ProfilerXMLPath>
         </FreeRTOS>
         <L4Re_Config>
          <FileName></FileName>
          <L4Re_._Plugin_._Version>Version 1.1</L4Re_._Plugin_._Version>
          <Thread_._block_._size>8192</Thread_._block_._size>
         </L4Re_Config>
         <Linux_Config>
          <KernelStart>0x48080000</KernelStart>
          <PikeOSParentKernelTaskName></PikeOSParentKernelTaskName>
          <PikeOSParentPluginName></PikeOSParentPluginName>
         </Linux_Config>
         <OpTee_Config>
          <OP-TEE_._Version>3.8.0</OP-TEE_._Version>
         </OpTee_Config>
         <OSEK_Data>
          <Config>
           <FileName></FileName>
           <FileType>ORTI</FileType>
          </Config>
          <ConfigRTOS>
           <UseiTCHi>false</UseiTCHi>
          </ConfigRTOS>
          <Export>
           <FileName></FileName>
           <Type>CSV</Type>
          </Export>
         </OSEK_Data>
         <PikeOS_Config>
          <FileNameExport>PikeOSExport.json</FileNameExport>
          <FileNameTasks></FileNameTasks>
          <FileNameThreads></FileNameThreads>
          <Host_._PikeOS_._SP>0x0</Host_._PikeOS_._SP>
          <OSLinuxChildPluginName></OSLinuxChildPluginName>
          <PikeOS_._Version>PikeOS 3.4</PikeOS_._Version>
          <PosixPartition></PosixPartition>
         </PikeOS_Config>
         <QNX_Config>
          <QNX_._Version>QNX 7.1</QNX_._Version>
         </QNX_Config>
         <rcX_Config>
          <rcX_._version>rcX 2.0.x.x</rcX_._version>
         </rcX_Config>
         <uCConfig>
          <AutoRefresh>false</AutoRefresh>
          <FloatingPoint>false</FloatingPoint>
          <VersionOfuC>uC/OS-II</VersionOfuC>
         </uCConfig>
         <Xen_Config>
          <Xen_._Version>Xen 4.8</Xen_._Version>
         </Xen_Config>
        </OS>
        <StackUsage>
         <End></End>
         <Pattern>0xCC</Pattern>
         <Position>End</Position>
         <Size></Size>
         <Start></Start>
         <Use>false</Use>
        </StackUsage>
        <SymbolFiles>
         <DefaultFile>0</DefaultFile>
         <File>
          <__.0>
           <Area></Area>
           <Load>true</Load>
           <OverrideMemArea>false</OverrideMemArea>
           <Path>..\..\..\Debug\Mark_1.elf</Path>
           <UseDefaultMSID>true</UseDefaultMSID>
           <MSID>
            <App>0x0</App>
            <bAppID>false</bAppID>
            <bVMID>false</bVMID>
            <Level>Sys</Level>
            <Secure>false</Secure>
            <Use>false</Use>
            <VM>0x0</VM>
           </MSID>
           <Options>
            <AnalyzeBeyondCOFInFunc>false</AnalyzeBeyondCOFInFunc>
            <AnalyzeCodeOutsideFunctions>true</AnalyzeCodeOutsideFunctions>
            <CodeOffset>0x0</CodeOffset>
            <InTargetDir>false</InTargetDir>
            <LoadCode>false</LoadCode>
            <LoadSymbols>false</LoadSymbols>
            <MemPropOnLoadedCodeOnly>false</MemPropOnLoadedCodeOnly>
            <MergeLines>true</MergeLines>
            <Offset>0x0</Offset>
            <OptimizeTypeName>true</OptimizeTypeName>
            <SymbolOffset>0x0</SymbolOffset>
            <TrustLineSymbolsToStartOnInstruction>true</TrustLineSymbolsToStartOnInstruction>
            <Type>ELF</Type>
            <Elf>
             <BitfieldsOverrideTypeFromByteSize>false</BitfieldsOverrideTypeFromByteSize>
             <CallStackGeneration>Automatic</CallStackGeneration>
             <DumpELFHeaders>false</DumpELFHeaders>
             <GCCARMDoubleFormat>Straight (VFP)</GCCARMDoubleFormat>
             <InsertInlinedFunctions>false</InsertInlinedFunctions>
             <LoadCodeFrom>Program Headers - Physical</LoadCodeFrom>
             <LoadCodeFromExecutableSections>true</LoadCodeFromExecutableSections>
             <LoadDebugSections>true</LoadDebugSections>
             <LoadOnlyBeginningOfTheStatementLines>true</LoadOnlyBeginningOfTheStatementLines>
             <LoadSymbolTableItems>None</LoadSymbolTableItems>
             <LoadWeakFunctions>true</LoadWeakFunctions>
             <LoadZeroSizedSegments>false</LoadZeroSizedSegments>
             <MergeTypes>false</MergeTypes>
             <OffsetFRAMEAdditionaly>0</OffsetFRAMEAdditionaly>
             <Override64bitVariableLocation>true</Override64bitVariableLocation>
             <RemoveOptimizedLines>true</RemoveOptimizedLines>
             <RemoveOptimizedLinesForCU>true</RemoveOptimizedLinesForCU>
             <ReverseBitFieldsOrder>false</ReverseBitFieldsOrder>
             <UseSymbolTableForVariablesWithNoLocation>true</UseSymbolTableForVariablesWithNoLocation>
             <Cosmic>
              <nowidden>false</nowidden>
              <sprec>false</sprec>
             </Cosmic>
             <IAR>
              <LegacyLocationLists>false</LegacyLocationLists>
             </IAR>
             <Tasking>
              <Demangle>false</Demangle>
             </Tasking>
            </Elf>
            <Hex>
             <IgnoreChecksum>false</IgnoreChecksum>
            </Hex>
            <IEEE>
             <EnumSize>16-bit</EnumSize>
             <Generator>Cosmic</Generator>
             <ReadPublicSection>false</ReadPublicSection>
            </IEEE>
            <PE>
             <AddSymbolsFromSymbolTable>false</AddSymbolsFromSymbolTable>
            </PE>
            <UBROF>
             <ConvertBankAddresses>false</ConvertBankAddresses>
             <Generator>IAR</Generator>
            </UBROF>
           </Options>
           <PreProcessor>
            <Analyze>none</Analyze>
            <CommandLine></CommandLine>
            <UseProjectDefines>false</UseProjectDefines>
            <UseProjectIncludePaths>false</UseProjectIncludePaths>
           </PreProcessor>
          </__.0>
         </File>
        </SymbolFiles>
       </__.0>
      </Applications>
      <SoCs>
       <__.0>
        <Enabled>true</Enabled>
        <ProbeURL></ProbeURL>
        <SFRName>TC264D</SFRName>
        <UserName>MCU</UserName>
        <DLFs_Program>
         <DefaultFile>1</DefaultFile>
         <File>
          <__.0>
           <Area></Area>
           <Load>true</Load>
           <OverrideMemArea>false</OverrideMemArea>
           <Path>..\..\..\Debug\Mark_1.elf</Path>
           <UseDefaultMSID>true</UseDefaultMSID>
           <MSID>
            <App>0x0</App>
            <bAppID>false</bAppID>
            <bVMID>false</bVMID>
            <Level>Sys</Level>
            <Secure>false</Secure>
            <Use>false</Use>
            <VM>0x0</VM>
           </MSID>
           <Options>
            <AnalyzeBeyondCOFInFunc>false</AnalyzeBeyondCOFInFunc>
            <AnalyzeCodeOutsideFunctions>true</AnalyzeCodeOutsideFunctions>
            <CodeOffset>0x0</CodeOffset>
            <InTargetDir>false</InTargetDir>
            <LoadCode>false</LoadCode>
            <LoadSymbols>false</LoadSymbols>
            <MemPropOnLoadedCodeOnly>false</MemPropOnLoadedCodeOnly>
            <MergeLines>true</MergeLines>
            <Offset>0x0</Offset>
            <OptimizeTypeName>true</OptimizeTypeName>
            <SymbolOffset>0x0</SymbolOffset>
            <TrustLineSymbolsToStartOnInstruction>true</TrustLineSymbolsToStartOnInstruction>
            <Type>ELF</Type>
            <Elf>
             <BitfieldsOverrideTypeFromByteSize>false</BitfieldsOverrideTypeFromByteSize>
             <CallStackGeneration>Automatic</CallStackGeneration>
             <DumpELFHeaders>false</DumpELFHeaders>
             <GCCARMDoubleFormat>Straight (VFP)</GCCARMDoubleFormat>
             <InsertInlinedFunctions>false</InsertInlinedFunctions>
             <LoadCodeFrom>Program Headers - Physical</LoadCodeFrom>
             <LoadCodeFromExecutableSections>true</LoadCodeFromExecutableSections>
             <LoadDebugSections>true</LoadDebugSections>
             <LoadOnlyBeginningOfTheStatementLines>true</LoadOnlyBeginningOfTheStatementLines>
             <LoadSymbolTableItems>None</LoadSymbolTableItems>
             <LoadWeakFunctions>true</LoadWeakFunctions>
             <LoadZeroSizedSegments>false</LoadZeroSizedSegments>
             <MergeTypes>false</MergeTypes>
             <OffsetFRAMEAdditionaly>0</OffsetFRAMEAdditionaly>
             <Override64bitVariableLocation>true</Override64bitVariableLocation>
             <RemoveOptimizedLines>true</RemoveOptimizedLines>
             <RemoveOptimizedLinesForCU>true</RemoveOptimizedLinesForCU>
             <ReverseBitFieldsOrder>false</ReverseBitFieldsOrder>
             <UseSymbolTableForVariablesWithNoLocation>true</UseSymbolTableForVariablesWithNoLocation>
             <Cosmic>
              <nowidden>false</nowidden>
              <sprec>false</sprec>
             </Cosmic>
             <IAR>
              <LegacyLocationLists>false</LegacyLocationLists>
             </IAR>
             <Tasking>
              <Demangle>false</Demangle>
             </Tasking>
            </Elf>
            <Hex>
             <IgnoreChecksum>false</IgnoreChecksum>
            </Hex>
            <IEEE>
             <EnumSize>16-bit</EnumSize>
             <Generator>Cosmic</Generator>
             <ReadPublicSection>false</ReadPublicSection>
            </IEEE>
            <PE>
             <AddSymbolsFromSymbolTable>false</AddSymbolsFromSymbolTable>
            </PE>
            <UBROF>
             <ConvertBankAddresses>false</ConvertBankAddresses>
             <Generator>IAR</Generator>
            </UBROF>
           </Options>
           <PreProcessor>
            <Analyze>none</Analyze>
            <CommandLine></CommandLine>
            <UseProjectDefines>false</UseProjectDefines>
            <UseProjectIncludePaths>false</UseProjectIncludePaths>
           </PreProcessor>
          </__.0>
         </File>
        </DLFs_Program>
        <DLFs_Target>
         <DefaultFile>-1</DefaultFile>
        </DLFs_Target>
        <MemorySpaces>
         <__.0>
          <Application>App</Application>
          <Cores>SMP</Cores>
          <Enabled>true</Enabled>
          <UserName>SMP</UserName>
          <MSID>
           <App>0x0</App>
           <bAppID>false</bAppID>
           <bVMID>false</bVMID>
           <Level>Sys</Level>
           <Secure>false</Secure>
           <Use>false</Use>
           <VM>0x0</VM>
          </MSID>
         </__.0>
        </MemorySpaces>
        <SMP>
         <Bindings>
          <__.0>
           <Cores>CPU0,CPU1</Cores>
           <Name>SMP</Name>
          </__.0>
         </Bindings>
        </SMP>
        <Startup>
         <AfterDL1>false</AfterDL1>
         <AfterDL1Action>GotoEntry</AfterDL1Action>
         <AfterDL1Goto></AfterDL1Goto>
         <AfterDL2>false</AfterDL2>
         <AfterDL2Action>Run</AfterDL2Action>
         <AfterDL2RunUntil></AfterDL2RunUntil>
         <DisplayLoadMap>false</DisplayLoadMap>
         <PostDLActionAlsoAfterReset>false</PostDLActionAlsoAfterReset>
         <TargetDownload>None</TargetDownload>
         <VerifyDownload>false</VerifyDownload>
         <LoadReport>
          <FileName>$Download.xml</FileName>
          <Format>none</Format>
          <Loaded>true</Loaded>
          <Overlap>true</Overlap>
          <VerifyErrors>true</VerifyErrors>
         </LoadReport>
        </Startup>
       </__.0>
      </SoCs>
     </Debug>
    </Cfg_System>
    <Environment>
     <Breakpoints>
      <DisplayBPMessage>0</DisplayBPMessage>
      <DisplayHWBPMessage>0</DisplayHWBPMessage>
     </Breakpoints>
     <Debug>
      <Debugging>
       <Ambiguity>Use Lowest</Ambiguity>
       <DisableBPs>None</DisableBPs>
       <DisableIRQ_RunUntil>false</DisableIRQ_RunUntil>
       <DisableIRQ_RunUntilReturn>true</DisableIRQ_RunUntilReturn>
       <DisableIRQ_Step>true</DisableIRQ_Step>
       <DisableIRQ_StepOver>true</DisableIRQ_StepOver>
       <ReserveBP>true</ReserveBP>
       <ResetConditionalBPCountWhenStop>false</ResetConditionalBPCountWhenStop>
       <RunUntilReturn>StepOut</RunUntilReturn>
      </Debugging>
      <Directories1>
       <AltPaths>false</AltPaths>
       <ConvertFromTo></ConvertFromTo>
       <ConvertPath>false</ConvertPath>
       <FindPriority>DLFile</FindPriority>
       <FindSources>true</FindSources>
      </Directories1>
      <Download>
       <DefaultFile>-1</DefaultFile>
      </Download>
      <DownloadOptionsData>
       <AfterDL1>0</AfterDL1>
       <AfterDL2>1</AfterDL2>
       <AfterDownloadGoto></AfterDownloadGoto>
       <AfterDownloadRunUntil></AfterDownloadRunUntil>
       <bAfterDL1>0</bAfterDL1>
       <bAfterDL2>0</bAfterDL2>
       <BeforeDownload>1</BeforeDownload>
       <DisplayLoadMap>False</DisplayLoadMap>
       <PostDLActionAlsoAfterReset>0</PostDLActionAlsoAfterReset>
       <TargetDownload>0</TargetDownload>
       <VerifyDownload>True</VerifyDownload>
       <Download>
        <DefaultFile>-1</DefaultFile>
       </Download>
       <LoadReport>
        <FileName>$Download.xml</FileName>
        <Format>none</Format>
        <Loaded>true</Loaded>
        <Overlap>true</Overlap>
        <VerifyErrors>true</VerifyErrors>
       </LoadReport>
      </DownloadOptionsData>
      <mode0>
       <IgnoreDLInfo>0</IgnoreDLInfo>
      </mode0>
      <mode1>
       <IgnoreDLInfo>0</IgnoreDLInfo>
      </mode1>
      <MSID>
       <App>0x0</App>
       <bAppID>false</bAppID>
       <bVMID>false</bVMID>
       <Level>Sys</Level>
       <Secure>false</Secure>
       <Use>false</Use>
       <VM>0x0</VM>
      </MSID>
      <Parser>
       <ExpressionMemoryAccessOperator>58</ExpressionMemoryAccessOperator>
      </Parser>
      <StackUsage>
       <End></End>
       <Pattern>0xCC</Pattern>
       <Position>End</Position>
       <Size></Size>
       <Start></Start>
       <Use>false</Use>
      </StackUsage>
      <Symbols>
       <LoadSymbols>false</LoadSymbols>
       <MarkersInLastLine>true</MarkersInLastLine>
       <NumChangeGradients>1</NumChangeGradients>
       <StepToOtherLine>false</StepToOtherLine>
       <UsePartitionAppQN>true</UsePartitionAppQN>
       <Format>
        <AddressDisplay>HexNoPrefix</AddressDisplay>
        <ANSI>true</ANSI>
        <BinaryDisplay>0bPrefix</BinaryDisplay>
        <CharArraysAsString>true</CharArraysAsString>
        <CharDisplay>Both</CharDisplay>
        <DereferenceStringPointers>false</DereferenceStringPointers>
        <DisplayCollapsedArrayStruct>true</DisplayCollapsedArrayStruct>
        <DisplayPointerMemArea>false</DisplayPointerMemArea>
        <EnumDisplay>Enum</EnumDisplay>
        <Hex>true</Hex>
        <MaxParseUpload>256</MaxParseUpload>
        <QualifiedNames>Unique</QualifiedNames>
        <UseSpacers>false</UseSpacers>
        <VagueFloatPrecision>1e-05</VagueFloatPrecision>
       </Format>
      </Symbols>
      <Update1>
       <RTUpdatePeriod>200</RTUpdatePeriod>
       <RTUpdateWhenRunning>true</RTUpdateWhenRunning>
       <RTUpdateWhenStopped>false</RTUpdateWhenStopped>
       <UpdateMemory>false</UpdateMemory>
       <UpdateOnBP>false</UpdateOnBP>
       <UpdateOSHw>true</UpdateOSHw>
       <UpdatePeriod>1</UpdatePeriod>
       <UpdateRegisters>false</UpdateRegisters>
       <UpdateWatches>true</UpdateWatches>
       <UpdateWhenRunning>false</UpdateWhenRunning>
       <UpdateWhenStopped>false</UpdateWhenStopped>
       <Access>
        <AllowMonitorAccess>false</AllowMonitorAccess>
        <AllowRealTimeAccess>true</AllowRealTimeAccess>
        <CacheAccesses>true</CacheAccesses>
        <DisableSFRCaching>true</DisableSFRCaching>
       </Access>
      </Update1>
      <WatchExport>
       <Expression>true</Expression>
       <FileName></FileName>
       <Location>false</Location>
       <Type>false</Type>
       <Value>true</Value>
      </WatchExport>
     </Debug>
     <Info>
      <winIDEA>Build 9.21.253.5</winIDEA>
     </Info>
     <Options>
      <Environment>
       <BuildManagerType>None</BuildManagerType>
       <iConnectLimitClients>false</iConnectLimitClients>
       <iConnectLimitNum>1</iConnectLimitNum>
       <IsBuildManagerEnabled>false</IsBuildManagerEnabled>
      </Environment>
      <Search>
       <ExtFolderIgnoreFilter>*.elf,*.bin,*.o*</ExtFolderIgnoreFilter>
      </Search>
     </Options>
     <Test>
      <CatchUncaughtExceptions>false</CatchUncaughtExceptions>
      <CheckPointerAssignType>true</CheckPointerAssignType>
      <CheckUserStubPrototype>true</CheckUserStubPrototype>
      <IgnoreInterruptFunctions>false</IgnoreInterruptFunctions>
      <InhibitIDERefresh>true</InhibitIDERefresh>
      <ZeroInitVar>false</ZeroInitVar>
      <ARM>
       <ExtendReturnValues>true</ExtendReturnValues>
       <UseArmEABI>true</UseArmEABI>
       <UseHardwareVFP>false</UseHardwareVFP>
      </ARM>
      <CallFrame>
       <CallFrame>Stack</CallFrame>
       <CallFrameNone>
        <AutomaticReturn>true</AutomaticReturn>
        <CustomReturn></CustomReturn>
       </CallFrameNone>
       <CallFrameRange>
        <CallFrameLocation>Symbol</CallFrameLocation>
        <CallFrameLocationAddr>
         <ContentAddress></ContentAddress>
         <ContentSize></ContentSize>
        </CallFrameLocationAddr>
        <CallFrameLocationSymbol>
         <ContentSymbol></ContentSymbol>
        </CallFrameLocationSymbol>
       </CallFrameRange>
      </CallFrame>
      <PersistentVars>
       <PersistentVariables>Stack</PersistentVariables>
       <PersistentVariablesRange>
        <PersistentVariablesLocation>Symbol</PersistentVariablesLocation>
        <PersistentVariablesLocationAddr>
         <ContentAddress></ContentAddress>
         <ContentSize></ContentSize>
        </PersistentVariablesLocationAddr>
        <PersistentVariablesLocationSymbol>
         <ContentSymbol></ContentSymbol>
        </PersistentVariablesLocationSymbol>
       </PersistentVariablesRange>
      </PersistentVars>
      <PPC>
       <ExtendReturnValues>false</ExtendReturnValues>
      </PPC>
      <RISCV32>
       <ABI>ILP32D</ABI>
      </RISCV32>
      <RISCV64>
       <ABI>2</ABI>
      </RISCV64>
      <RL78>
       <Calling_._Convention>V2</Calling_._Convention>
      </RL78>
      <TriCore>
       <Compiler>Tasking</Compiler>
      </TriCore>
      <V850>
       <Compiler>GreenHills ABI 2014</Compiler>
      </V850>
     </Test>
    </Environment>
    <Mode>
     <MMSLevel>AllInstances</MMSLevel>
     <MMS_Cfg>true</MMS_Cfg>
    </Mode>
    <Op_System>
     <Debug>
      <Processes>
       <__.0>
        <URL>App/SMP</URL>
       </__.0>
      </Processes>
     </Debug>
    </Op_System>
    <PluginData>
     <PluginInstance.0>
      <InstanceData>
       <Configuration>
        <CCTRLOnReset>
         <AutoOnReset>false</AutoOnReset>
         <InitialConfig>
          <CountEnable>Enable</CountEnable>
          <CounterMode>Task</CounterMode>
          <M1CNT>IP_DISPATCH_STALL</M1CNT>
          <M2CNT>LS_DISPATCH_STALL</M2CNT>
          <M3CNT>0</M3CNT>
         </InitialConfig>
        </CCTRLOnReset>
       </Configuration>
      </InstanceData>
      <InstanceInfo>
       <Instance>{r:{BB:0,SoC:0,Core:0}}</Instance>
       <Type>TriCore</Type>
      </InstanceInfo>
     </PluginInstance.0>
     <PluginInstance.1>
      <InstanceData>
       <Configuration>
        <CCTRLOnReset>
         <AutoOnReset>false</AutoOnReset>
         <InitialConfig>
          <CountEnable>Enable</CountEnable>
          <CounterMode>Task</CounterMode>
          <M1CNT>IP_DISPATCH_STALL</M1CNT>
          <M2CNT>LS_DISPATCH_STALL</M2CNT>
          <M3CNT>LP_DISPATCH_STALL</M3CNT>
         </InitialConfig>
        </CCTRLOnReset>
       </Configuration>
      </InstanceData>
      <InstanceInfo>
       <Instance>{r:{BB:0,SoC:0,Core:1}}</Instance>
       <Type>TriCore</Type>
      </InstanceInfo>
     </PluginInstance.1>
     <PluginInstance.2>
      <InstanceData>
       <Config>
        <AutoStart>false</AutoStart>
        <Port>5555</Port>
        <TimestampUnit>100us</TimestampUnit>
        <TraceToXCP>
         <A2LPath></A2LPath>
         <Enable>false</Enable>
         <SendAlways>true</SendAlways>
         <TrdPath></TrdPath>
        </TraceToXCP>
       </Config>
      </InstanceData>
      <InstanceInfo>
       <Instance></Instance>
       <Type>XCP</Type>
      </InstanceInfo>
     </PluginInstance.2>
    </PluginData>
    <Plugins>
     <_3BB2B9D2-577A-4597-90CE-7032BE77BD1F>
      <Plugin>
       <Configuration>
        <CCTRLOnReset>
         <AutoOnReset>false</AutoOnReset>
         <InitialConfig>
          <CountEnable>Enable</CountEnable>
          <CounterMode>Task</CounterMode>
          <M1CNT>IP_DISPATCH_STALL</M1CNT>
          <M2CNT>LS_DISPATCH_STALL</M2CNT>
          <M3CNT>0</M3CNT>
         </InitialConfig>
        </CCTRLOnReset>
       </Configuration>
      </Plugin>
     </_3BB2B9D2-577A-4597-90CE-7032BE77BD1F>
     <_3BB2B9D2-577A-4597-90CE-7032BE77BD1F1>
      <Plugin>
       <Configuration>
        <CCTRLOnReset>
         <AutoOnReset>false</AutoOnReset>
         <InitialConfig>
          <CountEnable>Enable</CountEnable>
          <CounterMode>Task</CounterMode>
          <M1CNT>IP_DISPATCH_STALL</M1CNT>
          <M2CNT>LS_DISPATCH_STALL</M2CNT>
          <M3CNT>LP_DISPATCH_STALL</M3CNT>
         </InitialConfig>
        </CCTRLOnReset>
       </Configuration>
      </Plugin>
     </_3BB2B9D2-577A-4597-90CE-7032BE77BD1F1>
     <B6DE5FA5-AE7D-4C0B-B4FF-02174A47912C>
      <Plugin>
       <Config>
        <AutoStart>false</AutoStart>
        <Port>5555</Port>
        <TimestampUnit>100us</TimestampUnit>
        <TraceToXCP>
         <A2LPath></A2LPath>
         <Enable>false</Enable>
         <SendAlways>true</SendAlways>
         <TrdPath></TrdPath>
        </TraceToXCP>
       </Config>
      </Plugin>
     </B6DE5FA5-AE7D-4C0B-B4FF-02174A47912C>
    </Plugins>
   </REG_ROOT>
  </winIDEA>
  <Project>
   <REG_HEADER>
    <Title>BF283F24-70B9-4f97-86E6-0AD5BAAE9E01</Title>
    <VersionHigh>0</VersionHigh>
    <VersionLow>1</VersionLow>
   </REG_HEADER>
   <REG_ROOT>
    <RegVer>**********</RegVer>
    <Project_Local>
     <Project.0>
      <DefaultTargetName>ADS - Debug</DefaultTargetName>
     </Project.0>
    </Project_Local>
   </REG_ROOT>
  </Project>
  <HServer>
   <REG_HEADER>
    <Title>BF283F24-70B9-4f97-86E6-0AD5BAAE9E01</Title>
    <VersionHigh>0</VersionHigh>
    <VersionLow>1</VersionLow>
   </REG_HEADER>
   <REG_ROOT>
    <RegVer>**********</RegVer>
    <HServerData>
     <Data>
      <BBSync>
       <Sync>Disabled</Sync>
       <SyncTime>false</SyncTime>
      </BBSync>
      <Debug>
       <BPData>
        <Tricore16>
         <TriCore16>
          <Enabled>0</Enabled>
          <BP.0>
           <Access>0</Access>
           <Address></Address>
           <Enabled>0</Enabled>
           <EntireObject>0</EntireObject>
           <Range>0</Range>
           <Event>
            <BBM>0</BBM>
            <BOD>0</BOD>
            <CNT>0</CNT>
            <CSP>0</CSP>
            <CST>0</CST>
            <Enable>0</Enable>
            <EVTA>0</EVTA>
            <SUSP>0</SUSP>
           </Event>
          </BP.0>
          <BP.1>
           <Access>0</Access>
           <Address></Address>
           <Enabled>0</Enabled>
           <EntireObject>0</EntireObject>
           <Range>0</Range>
           <Event>
            <BBM>0</BBM>
            <BOD>0</BOD>
            <CNT>0</CNT>
            <CSP>0</CSP>
            <CST>0</CST>
            <Enable>0</Enable>
            <EVTA>0</EVTA>
            <SUSP>0</SUSP>
           </Event>
          </BP.1>
          <BP.2>
           <Access>0</Access>
           <Address></Address>
           <Enabled>0</Enabled>
           <EntireObject>0</EntireObject>
           <Range>0</Range>
           <Event>
            <BBM>0</BBM>
            <BOD>0</BOD>
            <CNT>0</CNT>
            <CSP>0</CSP>
            <CST>0</CST>
            <Enable>0</Enable>
            <EVTA>0</EVTA>
            <SUSP>0</SUSP>
           </Event>
          </BP.2>
          <BP.3>
           <Access>0</Access>
           <Address></Address>
           <Enabled>0</Enabled>
           <EntireObject>0</EntireObject>
           <Range>0</Range>
           <Event>
            <BBM>0</BBM>
            <BOD>0</BOD>
            <CNT>0</CNT>
            <CSP>0</CSP>
            <CST>0</CST>
            <Enable>0</Enable>
            <EVTA>0</EVTA>
            <SUSP>0</SUSP>
           </Event>
          </BP.3>
          <BP.4>
           <Access>0</Access>
           <Address></Address>
           <Enabled>0</Enabled>
           <EntireObject>0</EntireObject>
           <Range>0</Range>
           <Event>
            <BBM>0</BBM>
            <BOD>0</BOD>
            <CNT>0</CNT>
            <CSP>0</CSP>
            <CST>0</CST>
            <Enable>0</Enable>
            <EVTA>0</EVTA>
            <SUSP>0</SUSP>
           </Event>
          </BP.4>
          <BP.5>
           <Access>0</Access>
           <Address></Address>
           <Enabled>0</Enabled>
           <EntireObject>0</EntireObject>
           <Range>0</Range>
           <Event>
            <BBM>0</BBM>
            <BOD>0</BOD>
            <CNT>0</CNT>
            <CSP>0</CSP>
            <CST>0</CST>
            <Enable>0</Enable>
            <EVTA>0</EVTA>
            <SUSP>0</SUSP>
           </Event>
          </BP.5>
          <BP.6>
           <Access>0</Access>
           <Address></Address>
           <Enabled>0</Enabled>
           <EntireObject>0</EntireObject>
           <Range>0</Range>
           <Event>
            <BBM>0</BBM>
            <BOD>0</BOD>
            <CNT>0</CNT>
            <CSP>0</CSP>
            <CST>0</CST>
            <Enable>0</Enable>
            <EVTA>0</EVTA>
            <SUSP>0</SUSP>
           </Event>
          </BP.6>
          <BP.7>
           <Access>0</Access>
           <Address></Address>
           <Enabled>0</Enabled>
           <EntireObject>0</EntireObject>
           <Range>0</Range>
           <Event>
            <BBM>0</BBM>
            <BOD>0</BOD>
            <CNT>0</CNT>
            <CSP>0</CSP>
            <CST>0</CST>
            <Enable>0</Enable>
            <EVTA>0</EVTA>
            <SUSP>0</SUSP>
           </Event>
          </BP.7>
         </TriCore16>
        </Tricore16>
       </BPData>
       <TriCore>
        <SoC_Debug>
         <ApplyAfterDL>false</ApplyAfterDL>
         <ApplyAfterDLFileName></ApplyAfterDLFileName>
        </SoC_Debug>
       </TriCore>
      </Debug>
      <FNet>
       <FixedFNode.0>
        <Name>Root</Name>
        <Type>0x0</Type>
        <Cfg>
         <AP.0>
          <Cfg>
           <Group>0</Group>
           <Name>AP1</Name>
           <Connectors.0>
            <Adapter>none</Adapter>
            <PinCfg.0>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.0>
            <PinCfg.10>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.10>
            <PinCfg.11>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.11>
            <PinCfg.12>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.12>
            <PinCfg.13>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.13>
            <PinCfg.14>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.14>
            <PinCfg.15>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.15>
            <PinCfg.16>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.16>
            <PinCfg.17>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.17>
            <PinCfg.18>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.18>
            <PinCfg.19>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.19>
            <PinCfg.1>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.1>
            <PinCfg.20>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.20>
            <PinCfg.21>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.21>
            <PinCfg.22>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.22>
            <PinCfg.23>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.23>
            <PinCfg.24>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.24>
            <PinCfg.25>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.25>
            <PinCfg.26>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.26>
            <PinCfg.27>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.27>
            <PinCfg.28>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.28>
            <PinCfg.29>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.29>
            <PinCfg.2>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.2>
            <PinCfg.30>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.30>
            <PinCfg.31>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.31>
            <PinCfg.32>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.32>
            <PinCfg.33>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.33>
            <PinCfg.34>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.34>
            <PinCfg.35>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.35>
            <PinCfg.36>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.36>
            <PinCfg.37>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.37>
            <PinCfg.38>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.38>
            <PinCfg.39>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.39>
            <PinCfg.3>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.3>
            <PinCfg.4>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.4>
            <PinCfg.5>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.5>
            <PinCfg.6>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.6>
            <PinCfg.7>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.7>
            <PinCfg.8>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.8>
            <PinCfg.9>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.9>
           </Connectors.0>
           <Connectors.1>
            <Adapter>none</Adapter>
            <PinCfg.0>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.0>
            <PinCfg.10>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.10>
            <PinCfg.11>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.11>
            <PinCfg.12>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.12>
            <PinCfg.13>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.13>
            <PinCfg.14>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.14>
            <PinCfg.15>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.15>
            <PinCfg.16>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.16>
            <PinCfg.17>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.17>
            <PinCfg.18>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.18>
            <PinCfg.19>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.19>
            <PinCfg.1>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.1>
            <PinCfg.20>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.20>
            <PinCfg.21>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.21>
            <PinCfg.22>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.22>
            <PinCfg.23>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.23>
            <PinCfg.24>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.24>
            <PinCfg.25>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.25>
            <PinCfg.26>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.26>
            <PinCfg.27>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.27>
            <PinCfg.28>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.28>
            <PinCfg.29>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.29>
            <PinCfg.2>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.2>
            <PinCfg.30>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.30>
            <PinCfg.31>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.31>
            <PinCfg.32>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.32>
            <PinCfg.33>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.33>
            <PinCfg.34>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.34>
            <PinCfg.35>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.35>
            <PinCfg.36>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.36>
            <PinCfg.37>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.37>
            <PinCfg.38>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.38>
            <PinCfg.39>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.39>
            <PinCfg.3>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.3>
            <PinCfg.4>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.4>
            <PinCfg.5>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.5>
            <PinCfg.6>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.6>
            <PinCfg.7>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.7>
            <PinCfg.8>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.8>
            <PinCfg.9>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.9>
           </Connectors.1>
          </Cfg>
          <Op>
           <CheckStatus>false</CheckStatus>
           <RunOnSync>false</RunOnSync>
           <StopOnSync>false</StopOnSync>
           <SyncOnStop>false</SyncOnStop>
           <IPQualifier>
            <Initial>true</Initial>
            <Disable>
             <TC>0</TC>
            </Disable>
            <Enable>
             <TC>0</TC>
            </Enable>
           </IPQualifier>
          </Op>
         </AP.0>
         <AP.1>
          <Cfg>
           <Group>0</Group>
           <Name>AP2</Name>
           <Connectors.0>
            <Adapter>none</Adapter>
            <PinCfg.0>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.0>
            <PinCfg.10>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.10>
            <PinCfg.11>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.11>
            <PinCfg.12>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.12>
            <PinCfg.13>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.13>
            <PinCfg.14>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.14>
            <PinCfg.15>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.15>
            <PinCfg.16>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.16>
            <PinCfg.17>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.17>
            <PinCfg.18>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.18>
            <PinCfg.19>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.19>
            <PinCfg.1>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.1>
            <PinCfg.20>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.20>
            <PinCfg.21>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.21>
            <PinCfg.22>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.22>
            <PinCfg.23>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.23>
            <PinCfg.24>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.24>
            <PinCfg.25>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.25>
            <PinCfg.26>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.26>
            <PinCfg.27>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.27>
            <PinCfg.28>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.28>
            <PinCfg.29>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.29>
            <PinCfg.2>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.2>
            <PinCfg.30>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.30>
            <PinCfg.31>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.31>
            <PinCfg.32>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.32>
            <PinCfg.33>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.33>
            <PinCfg.34>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.34>
            <PinCfg.35>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.35>
            <PinCfg.36>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.36>
            <PinCfg.37>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.37>
            <PinCfg.38>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.38>
            <PinCfg.39>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.39>
            <PinCfg.3>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.3>
            <PinCfg.4>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.4>
            <PinCfg.5>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.5>
            <PinCfg.6>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.6>
            <PinCfg.7>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.7>
            <PinCfg.8>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.8>
            <PinCfg.9>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.9>
           </Connectors.0>
           <Connectors.1>
            <Adapter>none</Adapter>
            <PinCfg.0>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.0>
            <PinCfg.10>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.10>
            <PinCfg.11>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.11>
            <PinCfg.12>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.12>
            <PinCfg.13>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.13>
            <PinCfg.14>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.14>
            <PinCfg.15>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.15>
            <PinCfg.16>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.16>
            <PinCfg.17>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.17>
            <PinCfg.18>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.18>
            <PinCfg.19>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.19>
            <PinCfg.1>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.1>
            <PinCfg.20>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.20>
            <PinCfg.21>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.21>
            <PinCfg.22>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.22>
            <PinCfg.23>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.23>
            <PinCfg.24>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.24>
            <PinCfg.25>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.25>
            <PinCfg.26>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.26>
            <PinCfg.27>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.27>
            <PinCfg.28>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.28>
            <PinCfg.29>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.29>
            <PinCfg.2>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.2>
            <PinCfg.30>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.30>
            <PinCfg.31>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.31>
            <PinCfg.32>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.32>
            <PinCfg.33>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.33>
            <PinCfg.34>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.34>
            <PinCfg.35>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.35>
            <PinCfg.36>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.36>
            <PinCfg.37>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.37>
            <PinCfg.38>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.38>
            <PinCfg.39>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.39>
            <PinCfg.3>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.3>
            <PinCfg.4>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.4>
            <PinCfg.5>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.5>
            <PinCfg.6>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.6>
            <PinCfg.7>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.7>
            <PinCfg.8>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.8>
            <PinCfg.9>
             <CfgIn>I_HZ</CfgIn>
             <CfgOut>O_TS</CfgOut>
             <Initial>HIGH</Initial>
            </PinCfg.9>
           </Connectors.1>
          </Cfg>
          <Op>
           <CheckStatus>false</CheckStatus>
           <RunOnSync>false</RunOnSync>
           <StopOnSync>false</StopOnSync>
           <SyncOnStop>false</SyncOnStop>
           <IPQualifier>
            <Initial>true</Initial>
            <Disable>
             <TC>0</TC>
            </Disable>
            <Enable>
             <TC>0</TC>
            </Enable>
           </IPQualifier>
          </Op>
         </AP.1>
         <COUNTER.0>
          <Cfg>
           <Group>0</Group>
           <Name>COUNTER1</Name>
          </Cfg>
          <Op>
           <Channel.0>
            <Limit>1000</Limit>
            <LimitAutoReset>false</LimitAutoReset>
            <LimitEnable>false</LimitEnable>
            <Record>false</Record>
            <StartActive>false</StartActive>
            <TickCounter>1</TickCounter>
            <TickCounterEnable>false</TickCounterEnable>
            <A_Increment>
             <TC>0</TC>
            </A_Increment>
            <A_Reset>
             <TC>0</TC>
            </A_Reset>
            <A_Resume>
             <TC>0</TC>
            </A_Resume>
            <A_Suspend>
             <TC>0</TC>
            </A_Suspend>
            <E_Increment>
             <TC>0</TC>
            </E_Increment>
            <E_Limit>
             <TC>0</TC>
            </E_Limit>
           </Channel.0>
           <Channel.1>
            <Limit>1000</Limit>
            <LimitAutoReset>false</LimitAutoReset>
            <LimitEnable>false</LimitEnable>
            <Record>false</Record>
            <StartActive>false</StartActive>
            <TickCounter>1</TickCounter>
            <TickCounterEnable>false</TickCounterEnable>
            <A_Increment>
             <TC>0</TC>
            </A_Increment>
            <A_Reset>
             <TC>0</TC>
            </A_Reset>
            <A_Resume>
             <TC>0</TC>
            </A_Resume>
            <A_Suspend>
             <TC>0</TC>
            </A_Suspend>
            <E_Increment>
             <TC>0</TC>
            </E_Increment>
            <E_Limit>
             <TC>0</TC>
            </E_Limit>
           </Channel.1>
           <Channel.2>
            <Limit>1000</Limit>
            <LimitAutoReset>false</LimitAutoReset>
            <LimitEnable>false</LimitEnable>
            <Record>false</Record>
            <StartActive>false</StartActive>
            <TickCounter>1</TickCounter>
            <TickCounterEnable>false</TickCounterEnable>
            <A_Increment>
             <TC>0</TC>
            </A_Increment>
            <A_Reset>
             <TC>0</TC>
            </A_Reset>
            <A_Resume>
             <TC>0</TC>
            </A_Resume>
            <A_Suspend>
             <TC>0</TC>
            </A_Suspend>
            <E_Increment>
             <TC>0</TC>
            </E_Increment>
            <E_Limit>
             <TC>0</TC>
            </E_Limit>
           </Channel.2>
           <Channel.3>
            <Limit>1000</Limit>
            <LimitAutoReset>false</LimitAutoReset>
            <LimitEnable>false</LimitEnable>
            <Record>false</Record>
            <StartActive>false</StartActive>
            <TickCounter>1</TickCounter>
            <TickCounterEnable>false</TickCounterEnable>
            <A_Increment>
             <TC>0</TC>
            </A_Increment>
            <A_Reset>
             <TC>0</TC>
            </A_Reset>
            <A_Resume>
             <TC>0</TC>
            </A_Resume>
            <A_Suspend>
             <TC>0</TC>
            </A_Suspend>
            <E_Increment>
             <TC>0</TC>
            </E_Increment>
            <E_Limit>
             <TC>0</TC>
            </E_Limit>
           </Channel.3>
           <IPQualifier>
            <Initial>false</Initial>
            <Disable>
             <TC>0</TC>
            </Disable>
            <Enable>
             <TC>0</TC>
            </Enable>
           </IPQualifier>
          </Op>
         </COUNTER.0>
         <I2C.0>
          <Cfg>
           <Enabled>false</Enabled>
           <Group>0</Group>
           <Name>I2C1</Name>
          </Cfg>
          <Op>
           <QualifyAll>true</QualifyAll>
           <Comp.0>
            <Access>Any</Access>
            <Address>0</Address>
            <Encoding>7-bit</Encoding>
            <TQ>
             <Qualify>false</Qualify>
             <Trigger>
              <TC>0</TC>
             </Trigger>
            </TQ>
           </Comp.0>
           <Comp.1>
            <Access>Any</Access>
            <Address>0</Address>
            <Encoding>7-bit</Encoding>
            <TQ>
             <Qualify>false</Qualify>
             <Trigger>
              <TC>0</TC>
             </Trigger>
            </TQ>
           </Comp.1>
           <Comp.2>
            <Access>Any</Access>
            <Address>0</Address>
            <Encoding>7-bit</Encoding>
            <TQ>
             <Qualify>false</Qualify>
             <Trigger>
              <TC>0</TC>
             </Trigger>
            </TQ>
           </Comp.2>
           <Comp.3>
            <Access>Any</Access>
            <Address>0</Address>
            <Encoding>7-bit</Encoding>
            <TQ>
             <Qualify>false</Qualify>
             <Trigger>
              <TC>0</TC>
             </Trigger>
            </TQ>
           </Comp.3>
           <IPQualifier>
            <Initial>false</Initial>
            <Disable>
             <TC>0</TC>
            </Disable>
            <Enable>
             <TC>0</TC>
            </Enable>
           </IPQualifier>
          </Op>
         </I2C.0>
         <I2C.1>
          <Cfg>
           <Enabled>false</Enabled>
           <Group>0</Group>
           <Name>I2C2</Name>
          </Cfg>
          <Op>
           <QualifyAll>true</QualifyAll>
           <Comp.0>
            <Access>Any</Access>
            <Address>0</Address>
            <Encoding>7-bit</Encoding>
            <TQ>
             <Qualify>false</Qualify>
             <Trigger>
              <TC>0</TC>
             </Trigger>
            </TQ>
           </Comp.0>
           <Comp.1>
            <Access>Any</Access>
            <Address>0</Address>
            <Encoding>7-bit</Encoding>
            <TQ>
             <Qualify>false</Qualify>
             <Trigger>
              <TC>0</TC>
             </Trigger>
            </TQ>
           </Comp.1>
           <Comp.2>
            <Access>Any</Access>
            <Address>0</Address>
            <Encoding>7-bit</Encoding>
            <TQ>
             <Qualify>false</Qualify>
             <Trigger>
              <TC>0</TC>
             </Trigger>
            </TQ>
           </Comp.2>
           <Comp.3>
            <Access>Any</Access>
            <Address>0</Address>
            <Encoding>7-bit</Encoding>
            <TQ>
             <Qualify>false</Qualify>
             <Trigger>
              <TC>0</TC>
             </Trigger>
            </TQ>
           </Comp.3>
           <IPQualifier>
            <Initial>false</Initial>
            <Disable>
             <TC>0</TC>
            </Disable>
            <Enable>
             <TC>0</TC>
            </Enable>
           </IPQualifier>
          </Op>
         </I2C.1>
         <I2C.2>
          <Cfg>
           <Enabled>false</Enabled>
           <Group>0</Group>
           <Name>I2C3</Name>
          </Cfg>
          <Op>
           <QualifyAll>true</QualifyAll>
           <Comp.0>
            <Access>Any</Access>
            <Address>0</Address>
            <Encoding>7-bit</Encoding>
            <TQ>
             <Qualify>false</Qualify>
             <Trigger>
              <TC>0</TC>
             </Trigger>
            </TQ>
           </Comp.0>
           <Comp.1>
            <Access>Any</Access>
            <Address>0</Address>
            <Encoding>7-bit</Encoding>
            <TQ>
             <Qualify>false</Qualify>
             <Trigger>
              <TC>0</TC>
             </Trigger>
            </TQ>
           </Comp.1>
           <Comp.2>
            <Access>Any</Access>
            <Address>0</Address>
            <Encoding>7-bit</Encoding>
            <TQ>
             <Qualify>false</Qualify>
             <Trigger>
              <TC>0</TC>
             </Trigger>
            </TQ>
           </Comp.2>
           <Comp.3>
            <Access>Any</Access>
            <Address>0</Address>
            <Encoding>7-bit</Encoding>
            <TQ>
             <Qualify>false</Qualify>
             <Trigger>
              <TC>0</TC>
             </Trigger>
            </TQ>
           </Comp.3>
           <IPQualifier>
            <Initial>false</Initial>
            <Disable>
             <TC>0</TC>
            </Disable>
            <Enable>
             <TC>0</TC>
            </Enable>
           </IPQualifier>
          </Op>
         </I2C.2>
         <I2C.3>
          <Cfg>
           <Enabled>false</Enabled>
           <Group>0</Group>
           <Name>I2C4</Name>
          </Cfg>
          <Op>
           <QualifyAll>true</QualifyAll>
           <Comp.0>
            <Access>Any</Access>
            <Address>0</Address>
            <Encoding>7-bit</Encoding>
            <TQ>
             <Qualify>false</Qualify>
             <Trigger>
              <TC>0</TC>
             </Trigger>
            </TQ>
           </Comp.0>
           <Comp.1>
            <Access>Any</Access>
            <Address>0</Address>
            <Encoding>7-bit</Encoding>
            <TQ>
             <Qualify>false</Qualify>
             <Trigger>
              <TC>0</TC>
             </Trigger>
            </TQ>
           </Comp.1>
           <Comp.2>
            <Access>Any</Access>
            <Address>0</Address>
            <Encoding>7-bit</Encoding>
            <TQ>
             <Qualify>false</Qualify>
             <Trigger>
              <TC>0</TC>
             </Trigger>
            </TQ>
           </Comp.2>
           <Comp.3>
            <Access>Any</Access>
            <Address>0</Address>
            <Encoding>7-bit</Encoding>
            <TQ>
             <Qualify>false</Qualify>
             <Trigger>
              <TC>0</TC>
             </Trigger>
            </TQ>
           </Comp.3>
           <IPQualifier>
            <Initial>false</Initial>
            <Disable>
             <TC>0</TC>
            </Disable>
            <Enable>
             <TC>0</TC>
            </Enable>
           </IPQualifier>
          </Op>
         </I2C.3>
         <STORE.0>
          <Cfg>
           <DTM_Partition>0</DTM_Partition>
           <Group>0</Group>
           <Name></Name>
           <TC_Partition>0</TC_Partition>
           <Partitions.0>
            <Enabled>false</Enabled>
            <Size>0</Size>
           </Partitions.0>
           <Partitions.1>
            <Enabled>false</Enabled>
            <Size>0</Size>
           </Partitions.1>
           <Partitions.2>
            <Enabled>false</Enabled>
            <Size>0</Size>
           </Partitions.2>
           <Partitions.3>
            <Enabled>false</Enabled>
            <Size>0</Size>
           </Partitions.3>
          </Cfg>
          <Op>
           <RecordTC>false</RecordTC>
           <IPQualifier>
            <Initial>false</Initial>
            <Disable>
             <TC>0</TC>
            </Disable>
            <Enable>
             <TC>0</TC>
            </Enable>
           </IPQualifier>
           <Partitions.0>
            <PreTrigger>0</PreTrigger>
            <Error>
             <TC>0</TC>
            </Error>
            <Start>
             <TC>0</TC>
            </Start>
           </Partitions.0>
           <Partitions.1>
            <PreTrigger>0</PreTrigger>
            <Error>
             <TC>0</TC>
            </Error>
            <Start>
             <TC>0</TC>
            </Start>
           </Partitions.1>
           <Partitions.2>
            <PreTrigger>0</PreTrigger>
            <Error>
             <TC>0</TC>
            </Error>
            <Start>
             <TC>0</TC>
            </Start>
           </Partitions.2>
           <Partitions.3>
            <PreTrigger>0</PreTrigger>
            <Error>
             <TC>0</TC>
            </Error>
            <Start>
             <TC>0</TC>
            </Start>
           </Partitions.3>
          </Op>
         </STORE.0>
         <SYSTEM>
          <Cfg>
           <Group>0</Group>
           <Name></Name>
          </Cfg>
         </SYSTEM>
        </Cfg>
       </FixedFNode.0>
       <FixedFNode.1>
        <Name>Hub</Name>
        <Type>0x103</Type>
        <Cfg>
         <SYSTEM>
          <Cfg>
           <Group>0</Group>
           <Name></Name>
          </Cfg>
         </SYSTEM>
        </Cfg>
       </FixedFNode.1>
      </FNet>
      <Op>
       <TraceCalibrationPerformed>false</TraceCalibrationPerformed>
      </Op>
      <Options>
       <EVE>
        <Debug>
         <Enabled>Never</Enabled>
         <Port>2022</Port>
         <winIDEAPath></winIDEAPath>
        </Debug>
       </EVE>
      </Options>
      <Op_BPs>
       <BPs>
        <CPU0>
         <BP>
          <BP.0>
           <Address></Address>
           <EntireObject>false</EntireObject>
          </BP.0>
          <BP.1>
           <Address></Address>
           <EntireObject>false</EntireObject>
          </BP.1>
          <BP.2>
           <Address></Address>
           <EntireObject>false</EntireObject>
          </BP.2>
          <BP.3>
           <Address></Address>
           <EntireObject>false</EntireObject>
          </BP.3>
          <BP.4>
           <Address></Address>
           <EntireObject>false</EntireObject>
          </BP.4>
          <BP.5>
           <Address></Address>
           <EntireObject>false</EntireObject>
          </BP.5>
          <BP.6>
           <Address></Address>
           <EntireObject>false</EntireObject>
          </BP.6>
          <BP.7>
           <Address></Address>
           <EntireObject>false</EntireObject>
          </BP.7>
          <HW>
           <Enabled>false</Enabled>
           <BP.0>
            <Access>RD</Access>
            <Enabled>false</Enabled>
            <Range>false</Range>
            <Event>
             <BBM>false</BBM>
             <BOD>false</BOD>
             <CNT>NoChange</CNT>
             <CSP>false</CSP>
             <CST>false</CST>
             <Enable>false</Enable>
             <EVTA>Disabled</EVTA>
             <SUSP>false</SUSP>
            </Event>
           </BP.0>
           <BP.1>
            <Access>RD</Access>
            <Enabled>false</Enabled>
            <Range>false</Range>
            <Event>
             <BBM>false</BBM>
             <BOD>false</BOD>
             <CNT>NoChange</CNT>
             <CSP>false</CSP>
             <CST>false</CST>
             <Enable>false</Enable>
             <EVTA>Disabled</EVTA>
             <SUSP>false</SUSP>
            </Event>
           </BP.1>
           <BP.2>
            <Access>RD</Access>
            <Enabled>false</Enabled>
            <Range>false</Range>
            <Event>
             <BBM>false</BBM>
             <BOD>false</BOD>
             <CNT>NoChange</CNT>
             <CSP>false</CSP>
             <CST>false</CST>
             <Enable>false</Enable>
             <EVTA>Disabled</EVTA>
             <SUSP>false</SUSP>
            </Event>
           </BP.2>
           <BP.3>
            <Access>RD</Access>
            <Enabled>false</Enabled>
            <Range>false</Range>
            <Event>
             <BBM>false</BBM>
             <BOD>false</BOD>
             <CNT>NoChange</CNT>
             <CSP>false</CSP>
             <CST>false</CST>
             <Enable>false</Enable>
             <EVTA>Disabled</EVTA>
             <SUSP>false</SUSP>
            </Event>
           </BP.3>
           <BP.4>
            <Access>RD</Access>
            <Enabled>false</Enabled>
            <Range>false</Range>
            <Event>
             <BBM>false</BBM>
             <BOD>false</BOD>
             <CNT>NoChange</CNT>
             <CSP>false</CSP>
             <CST>false</CST>
             <Enable>false</Enable>
             <EVTA>Disabled</EVTA>
             <SUSP>false</SUSP>
            </Event>
           </BP.4>
           <BP.5>
            <Access>RD</Access>
            <Enabled>false</Enabled>
            <Range>false</Range>
            <Event>
             <BBM>false</BBM>
             <BOD>false</BOD>
             <CNT>NoChange</CNT>
             <CSP>false</CSP>
             <CST>false</CST>
             <Enable>false</Enable>
             <EVTA>Disabled</EVTA>
             <SUSP>false</SUSP>
            </Event>
           </BP.5>
           <BP.6>
            <Access>RD</Access>
            <Enabled>false</Enabled>
            <Range>false</Range>
            <Event>
             <BBM>false</BBM>
             <BOD>false</BOD>
             <CNT>NoChange</CNT>
             <CSP>false</CSP>
             <CST>false</CST>
             <Enable>false</Enable>
             <EVTA>Disabled</EVTA>
             <SUSP>false</SUSP>
            </Event>
           </BP.6>
           <BP.7>
            <Access>RD</Access>
            <Enabled>false</Enabled>
            <Range>false</Range>
            <Event>
             <BBM>false</BBM>
             <BOD>false</BOD>
             <CNT>NoChange</CNT>
             <CSP>false</CSP>
             <CST>false</CST>
             <Enable>false</Enable>
             <EVTA>Disabled</EVTA>
             <SUSP>false</SUSP>
            </Event>
           </BP.7>
          </HW>
         </BP>
        </CPU0>
        <CPU1>
         <BP>
          <BP.0>
           <Address></Address>
           <EntireObject>false</EntireObject>
          </BP.0>
          <BP.1>
           <Address></Address>
           <EntireObject>false</EntireObject>
          </BP.1>
          <BP.2>
           <Address></Address>
           <EntireObject>false</EntireObject>
          </BP.2>
          <BP.3>
           <Address></Address>
           <EntireObject>false</EntireObject>
          </BP.3>
          <BP.4>
           <Address></Address>
           <EntireObject>false</EntireObject>
          </BP.4>
          <BP.5>
           <Address></Address>
           <EntireObject>false</EntireObject>
          </BP.5>
          <BP.6>
           <Address></Address>
           <EntireObject>false</EntireObject>
          </BP.6>
          <BP.7>
           <Address></Address>
           <EntireObject>false</EntireObject>
          </BP.7>
          <HW>
           <Enabled>false</Enabled>
           <BP.0>
            <Access>RD</Access>
            <Enabled>false</Enabled>
            <Range>false</Range>
            <Event>
             <BBM>false</BBM>
             <BOD>false</BOD>
             <CNT>NoChange</CNT>
             <CSP>false</CSP>
             <CST>false</CST>
             <Enable>false</Enable>
             <EVTA>Disabled</EVTA>
             <SUSP>false</SUSP>
            </Event>
           </BP.0>
           <BP.1>
            <Access>RD</Access>
            <Enabled>false</Enabled>
            <Range>false</Range>
            <Event>
             <BBM>false</BBM>
             <BOD>false</BOD>
             <CNT>NoChange</CNT>
             <CSP>false</CSP>
             <CST>false</CST>
             <Enable>false</Enable>
             <EVTA>Disabled</EVTA>
             <SUSP>false</SUSP>
            </Event>
           </BP.1>
           <BP.2>
            <Access>RD</Access>
            <Enabled>false</Enabled>
            <Range>false</Range>
            <Event>
             <BBM>false</BBM>
             <BOD>false</BOD>
             <CNT>NoChange</CNT>
             <CSP>false</CSP>
             <CST>false</CST>
             <Enable>false</Enable>
             <EVTA>Disabled</EVTA>
             <SUSP>false</SUSP>
            </Event>
           </BP.2>
           <BP.3>
            <Access>RD</Access>
            <Enabled>false</Enabled>
            <Range>false</Range>
            <Event>
             <BBM>false</BBM>
             <BOD>false</BOD>
             <CNT>NoChange</CNT>
             <CSP>false</CSP>
             <CST>false</CST>
             <Enable>false</Enable>
             <EVTA>Disabled</EVTA>
             <SUSP>false</SUSP>
            </Event>
           </BP.3>
           <BP.4>
            <Access>RD</Access>
            <Enabled>false</Enabled>
            <Range>false</Range>
            <Event>
             <BBM>false</BBM>
             <BOD>false</BOD>
             <CNT>NoChange</CNT>
             <CSP>false</CSP>
             <CST>false</CST>
             <Enable>false</Enable>
             <EVTA>Disabled</EVTA>
             <SUSP>false</SUSP>
            </Event>
           </BP.4>
           <BP.5>
            <Access>RD</Access>
            <Enabled>false</Enabled>
            <Range>false</Range>
            <Event>
             <BBM>false</BBM>
             <BOD>false</BOD>
             <CNT>NoChange</CNT>
             <CSP>false</CSP>
             <CST>false</CST>
             <Enable>false</Enable>
             <EVTA>Disabled</EVTA>
             <SUSP>false</SUSP>
            </Event>
           </BP.5>
           <BP.6>
            <Access>RD</Access>
            <Enabled>false</Enabled>
            <Range>false</Range>
            <Event>
             <BBM>false</BBM>
             <BOD>false</BOD>
             <CNT>NoChange</CNT>
             <CSP>false</CSP>
             <CST>false</CST>
             <Enable>false</Enable>
             <EVTA>Disabled</EVTA>
             <SUSP>false</SUSP>
            </Event>
           </BP.6>
           <BP.7>
            <Access>RD</Access>
            <Enabled>false</Enabled>
            <Range>false</Range>
            <Event>
             <BBM>false</BBM>
             <BOD>false</BOD>
             <CNT>NoChange</CNT>
             <CSP>false</CSP>
             <CST>false</CST>
             <Enable>false</Enable>
             <EVTA>Disabled</EVTA>
             <SUSP>false</SUSP>
            </Event>
           </BP.7>
          </HW>
         </BP>
        </CPU1>
       </BPs>
      </Op_BPs>
      <Tools>
       <CoreSightDetection>
        <ActiveResetDuringDetection>0</ActiveResetDuringDetection>
        <ActiveTRSTDuringDetection>1</ActiveTRSTDuringDetection>
        <APVersion>APvAuto</APVersion>
        <DeactivateResetDuringDetection>1</DeactivateResetDuringDetection>
        <DebugBaseAddress>0</DebugBaseAddress>
        <DPVersion>DPvAuto</DPVersion>
        <DRPostfix>0</DRPostfix>
        <DRPrefix>0</DRPrefix>
        <EVEScriptPath></EVEScriptPath>
        <ForceDebugBaseAddress>0</ForceDebugBaseAddress>
        <ForceScanAllDPv1APs>false</ForceScanAllDPv1APs>
        <ForceScanEntireRomtables>0</ForceScanEntireRomtables>
        <IDSingleComponentOnly>0</IDSingleComponentOnly>
        <IRLength>4</IRLength>
        <IRPostfix>0</IRPostfix>
        <IRPrefix>0</IRPrefix>
        <PostResetDelay_ms>100</PostResetDelay_ms>
        <Protocol>0</Protocol>
        <ResetDuration_ms>100</ResetDuration_ms>
        <ScanSpecificSWDTargetID>0</ScanSpecificSWDTargetID>
        <SingleComponentAddress>0</SingleComponentAddress>
        <SpecificJTAGTAPOnly>0</SpecificJTAGTAPOnly>
        <SWDTargetID>0</SWDTargetID>
       </CoreSightDetection>
       <Memory>
        <Address>0</Address>
        <Area>0</Area>
        <Continuous>0</Continuous>
        <DataSize>0</DataSize>
        <ECC_Address>0</ECC_Address>
        <ECC_Size>1</ECC_Size>
        <Operation>0</Operation>
        <Ordering>0</Ordering>
        <RefreshAll>0</RefreshAll>
        <WriteData>0</WriteData>
       </Memory>
       <Output>
        <Process></Process>
        <Type>0</Type>
        <Cortex>
         <EnableITMStimulus type="B">AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA</EnableITMStimulus>
         <ITMStimulusSelect>0</ITMStimulusSelect>
        </Cortex>
        <SharedMem>
         <DebugConn>0</DebugConn>
         <DebugConnAddress></DebugConnAddress>
        </SharedMem>
        <UART>
         <BaudRate>9600</BaudRate>
        </UART>
       </Output>
      </Tools>
      <Trace>
       <Common>
        <AnalyzerReadMemoryOnMissingCode>true</AnalyzerReadMemoryOnMissingCode>
        <CustomTimeStamp></CustomTimeStamp>
       </Common>
       <TriCore>
        <ProfilerTimeSource>1</ProfilerTimeSource>
       </TriCore>
      </Trace>
      <UMI1>
       <Devices>
        <Devices>
         <__.0>
          <AllowDownloadOnly>false</AllowDownloadOnly>
          <AllowErase>false</AllowErase>
          <CoreIndex>4294967295</CoreIndex>
          <Device>TC26x_PFLASH_2MB5</Device>
          <DeviceAddress>0x80000000</DeviceAddress>
          <DeviceType>Unknown</DeviceType>
          <Enabled>true</Enabled>
          <EnableECC>1</EnableECC>
          <EraseBeforeDownload>false</EraseBeforeDownload>
          <FillDevice>true</FillDevice>
          <FillFirst>0x0</FillFirst>
          <FillLast>0x0</FillLast>
          <FillValue>0</FillValue>
          <Flags_0>0</Flags_0>
          <Flags_1>0</Flags_1>
          <Flags_2>0</Flags_2>
          <Flags_3>0</Flags_3>
          <Manufacturer>Infineon</Manufacturer>
          <Name>Infineon TC26x_PFLASH_2MB5</Name>
          <OnChip>true</OnChip>
          <OverrideTimeout>0</OverrideTimeout>
          <ParallelNum>1</ParallelNum>
          <ProgramAtDownload>false</ProgramAtDownload>
          <RDOnly>false</RDOnly>
          <SafeMode>Auto</SafeMode>
          <Timeout>100</Timeout>
          <UseBufferCompress>true</UseBufferCompress>
          <UseCompression>true</UseCompression>
          <UseHash>true</UseHash>
          <UseHashing>true</UseHashing>
          <Verify>true</Verify>
          <VerifyOnTheFly>true</VerifyOnTheFly>
          <Version></Version>
          <Virtual>false</Virtual>
          <CP_Post>
           <Execute>Default</Execute>
           <Scripts></Scripts>
          </CP_Post>
          <CP_Pre>
           <Execute>Default</Execute>
           <Scripts></Scripts>
          </CP_Pre>
          <Options>
           <UNIT_Type_AllowProgramming_0>false</UNIT_Type_AllowProgramming_0>
           <UNIT_Type_AllowProgramming_1>false</UNIT_Type_AllowProgramming_1>
           <UNIT_Type_AllowProgramming_2>false</UNIT_Type_AllowProgramming_2>
          </Options>
         </__.0>
         <__.1>
          <AllowDownloadOnly>false</AllowDownloadOnly>
          <AllowErase>false</AllowErase>
          <CoreIndex>4294967295</CoreIndex>
          <Device>TC26x_DFLASH_96kB</Device>
          <DeviceAddress>0x8F000000</DeviceAddress>
          <DeviceType>Unknown</DeviceType>
          <Enabled>true</Enabled>
          <EnableECC>1</EnableECC>
          <EraseBeforeDownload>false</EraseBeforeDownload>
          <FillDevice>true</FillDevice>
          <FillFirst>0x0</FillFirst>
          <FillLast>0x0</FillLast>
          <FillValue>0</FillValue>
          <Flags_0>1</Flags_0>
          <Flags_1>0</Flags_1>
          <Flags_2>0</Flags_2>
          <Flags_3>0</Flags_3>
          <Manufacturer>Infineon</Manufacturer>
          <Name>Infineon TC26x_DFLASH_96kB</Name>
          <OnChip>true</OnChip>
          <OverrideTimeout>0</OverrideTimeout>
          <ParallelNum>1</ParallelNum>
          <ProgramAtDownload>false</ProgramAtDownload>
          <RDOnly>false</RDOnly>
          <SafeMode>Auto</SafeMode>
          <Timeout>100</Timeout>
          <UseBufferCompress>true</UseBufferCompress>
          <UseCompression>true</UseCompression>
          <UseHash>true</UseHash>
          <UseHashing>true</UseHashing>
          <Verify>true</Verify>
          <VerifyOnTheFly>true</VerifyOnTheFly>
          <Version></Version>
          <Virtual>false</Virtual>
          <CP_Post>
           <Execute>Default</Execute>
           <Scripts></Scripts>
          </CP_Post>
          <CP_Pre>
           <Execute>Default</Execute>
           <Scripts></Scripts>
          </CP_Pre>
          <Options>
           <UNIT_Type_AllowProgramming_0>false</UNIT_Type_AllowProgramming_0>
           <UNIT_Type_AllowProgramming_1>false</UNIT_Type_AllowProgramming_1>
           <UNIT_Type_AllowProgramming_2>false</UNIT_Type_AllowProgramming_2>
          </Options>
         </__.1>
         <__.2>
          <AllowDownloadOnly>false</AllowDownloadOnly>
          <AllowErase>false</AllowErase>
          <CoreIndex>4294967295</CoreIndex>
          <Device>TC2xx_UCB_16kB</Device>
          <DeviceAddress>0xAF100000</DeviceAddress>
          <DeviceType>Unknown</DeviceType>
          <Enabled>false</Enabled>
          <EnableECC>1</EnableECC>
          <EraseBeforeDownload>false</EraseBeforeDownload>
          <FillDevice>true</FillDevice>
          <FillFirst>0x0</FillFirst>
          <FillLast>0x0</FillLast>
          <FillValue>0</FillValue>
          <Flags_0>1</Flags_0>
          <Flags_1>0</Flags_1>
          <Flags_2>0</Flags_2>
          <Flags_3>0</Flags_3>
          <Manufacturer>Infineon</Manufacturer>
          <Name>Infineon TC2xx_UCB_16kB</Name>
          <OnChip>true</OnChip>
          <OverrideTimeout>0</OverrideTimeout>
          <ParallelNum>1</ParallelNum>
          <ProgramAtDownload>false</ProgramAtDownload>
          <RDOnly>false</RDOnly>
          <SafeMode>Auto</SafeMode>
          <Timeout>100</Timeout>
          <UseBufferCompress>true</UseBufferCompress>
          <UseCompression>true</UseCompression>
          <UseHash>true</UseHash>
          <UseHashing>true</UseHashing>
          <Verify>true</Verify>
          <VerifyOnTheFly>true</VerifyOnTheFly>
          <Version></Version>
          <Virtual>false</Virtual>
          <CP_Post>
           <Execute>Default</Execute>
           <Scripts></Scripts>
          </CP_Post>
          <CP_Pre>
           <Execute>Default</Execute>
           <Scripts></Scripts>
          </CP_Pre>
          <Options>
           <UNIT_Type_AllowProgramming_0>false</UNIT_Type_AllowProgramming_0>
           <UNIT_Type_AllowProgramming_1>false</UNIT_Type_AllowProgramming_1>
           <UNIT_Type_AllowProgramming_2>false</UNIT_Type_AllowProgramming_2>
          </Options>
         </__.2>
        </Devices>
       </Devices>
      </UMI1>
     </Data>
     <HW>
      <V_BUILD>253</V_BUILD>
      <V_BUILD_SCC>180504</V_BUILD_SCC>
      <V_BUILD_SUB>5</V_BUILD_SUB>
      <V_MAJOR>9</V_MAJOR>
      <V_MINOR>21</V_MINOR>
      <BDM>
       <CallPoint>
        <Attach_ConnectToSoC_Same>false</Attach_ConnectToSoC_Same>
        <Attach_InitSoC_Same>false</Attach_InitSoC_Same>
        <Attach_StopToExecute>true</Attach_StopToExecute>
        <Debug_ConnectToSoC_Same>false</Debug_ConnectToSoC_Same>
        <Debug_InitSoC_Same>false</Debug_InitSoC_Same>
        <Program_ResetAfterProgram>false</Program_ResetAfterProgram>
        <WDTDisable>false</WDTDisable>
        <Attach_ConnectToSoC>
         <Execute>Default</Execute>
         <Scripts></Scripts>
        </Attach_ConnectToSoC>
        <Attach_InitSoC>
         <Execute>Default</Execute>
         <Scripts></Scripts>
        </Attach_InitSoC>
        <Debug_ConnectToSoC>
         <Execute>Default</Execute>
         <Scripts></Scripts>
        </Debug_ConnectToSoC>
        <Debug_InitSoC>
         <Execute>Default</Execute>
         <Scripts></Scripts>
        </Debug_InitSoC>
        <Detach_CleanupSoC>
         <Execute>Default</Execute>
         <Scripts></Scripts>
        </Detach_CleanupSoC>
        <Program_ConnectToSoC>
         <Execute>Default</Execute>
         <Scripts></Scripts>
        </Program_ConnectToSoC>
        <Program_InitSoC>
         <Execute>Default</Execute>
         <Scripts></Scripts>
        </Program_InitSoC>
        <Trace_Done>
         <Execute>Default</Execute>
         <Scripts></Scripts>
        </Trace_Done>
        <Trace_Init>
         <Execute>Default</Execute>
         <Scripts></Scripts>
        </Trace_Init>
        <Trace_Init_BeforeRun>
         <Execute>Default</Execute>
         <Scripts></Scripts>
        </Trace_Init_BeforeRun>
       </CallPoint>
       <CPU>
        <CPU1>0</CPU1>
        <Family>36</Family>
        <POD>0</POD>
        <SubCPU>TC264D</SubCPU>
        <Aurix>
         <AllowCacheMonExec>false</AllowCacheMonExec>
         <ApplicationLBIST>false</ApplicationLBIST>
         <BRKIN>HIGH</BRKIN>
         <ClearICRIEWhenStopped>false</ClearICRIEWhenStopped>
         <DAPClock>5000</DAPClock>
         <DAPE>false</DAPE>
         <DAPE_Clock>10000</DAPE_Clock>
         <DAPE_DebugMode>DAP_W</DAPE_DebugMode>
         <DebugMode>DAP</DebugMode>
         <FirstTraceTile>0</FirstTraceTile>
         <FlushTraceOnAGBTStop>false</FlushTraceOnAGBTStop>
         <NumTraceTiles>8</NumTraceTiles>
         <OnChipRAM_Size>0</OnChipRAM_Size>
         <OnChipRAM_Start>0</OnChipRAM_Start>
         <Password_0>0</Password_0>
         <Password_1>0</Password_1>
         <Password_2>0</Password_2>
         <Password_3>0</Password_3>
         <Password_4>0</Password_4>
         <Password_5>0</Password_5>
         <Password_6>0</Password_6>
         <Password_7>0</Password_7>
         <QuickLBIST>false</QuickLBIST>
         <SingleStepMode>Run until next instruction</SingleStepMode>
         <SoC_Debug_SetOnReset>false</SoC_Debug_SetOnReset>
         <TimeSource_Profiler>tsu_rel</TimeSource_Profiler>
         <TraceBufferMode>ONCHIP_TBUF_TFIF</TraceBufferMode>
         <TraceLMUAuto>true</TraceLMUAuto>
         <TraceMemoryRegion>TCM</TraceMemoryRegion>
         <UseAllEmMemForTrace>true</UseAllEmMemForTrace>
         <UsePassword>false</UsePassword>
         <Events1p8>
          <BreakoutDisable>false</BreakoutDisable>
          <DebugVM_0>true</DebugVM_0>
          <DebugVM_1>true</DebugVM_1>
          <DebugVM_2>true</DebugVM_2>
          <DebugVM_3>true</DebugVM_3>
          <DebugVM_4>true</DebugVM_4>
          <DebugVM_5>true</DebugVM_5>
          <DebugVM_6>true</DebugVM_6>
          <DebugVM_7>true</DebugVM_7>
          <Event>Halt</Event>
          <SuspendOutSignalState>true</SuspendOutSignalState>
         </Events1p8>
         <Event.CREVT>
          <BBM>false</BBM>
          <BOD>false</BOD>
          <CNT>NoChange</CNT>
          <CSP>false</CSP>
          <CST>false</CST>
          <Enable>false</Enable>
          <EVTA>Disabled</EVTA>
          <SUSP>false</SUSP>
         </Event.CREVT>
         <Event.EXECEVT>
          <BBM>true</BBM>
          <BOD>false</BOD>
          <CNT>NoChange</CNT>
          <CSP>false</CSP>
          <CST>false</CST>
          <Enable>false</Enable>
          <EVTA>Halt</EVTA>
          <SUSP>true</SUSP>
         </Event.EXECEVT>
         <Event.EXEVT>
          <BBM>false</BBM>
          <BOD>false</BOD>
          <CNT>NoChange</CNT>
          <CSP>false</CSP>
          <CST>false</CST>
          <Enable>false</Enable>
          <EVTA>Disabled</EVTA>
          <SUSP>false</SUSP>
         </Event.EXEVT>
         <Event.SWEVT>
          <BBM>true</BBM>
          <BOD>false</BOD>
          <CNT>NoChange</CNT>
          <CSP>false</CSP>
          <CST>false</CST>
          <Enable>false</Enable>
          <EVTA>Halt</EVTA>
          <SUSP>true</SUSP>
         </Event.SWEVT>
         <GenericEvent1p8.0>
          <BreakBeforeMake>true</BreakBeforeMake>
          <Counters>NoChange</Counters>
          <Enabled>false</Enabled>
         </GenericEvent1p8.0>
         <GenericEvent1p8.1>
          <BreakBeforeMake>true</BreakBeforeMake>
          <Counters>NoChange</Counters>
          <Enabled>false</Enabled>
         </GenericEvent1p8.1>
         <GenericEvent1p8.2>
          <BreakBeforeMake>true</BreakBeforeMake>
          <Counters>NoChange</Counters>
          <Enabled>false</Enabled>
         </GenericEvent1p8.2>
         <TraceLMU>
          <LMU.0>
           <Base>0</Base>
           <Region>0</Region>
           <Size>0</Size>
           <Use>false</Use>
          </LMU.0>
          <LMU.1>
           <Base>0</Base>
           <Region>0</Region>
           <Size>0</Size>
           <Use>false</Use>
          </LMU.1>
          <LMU.2>
           <Base>0</Base>
           <Region>0</Region>
           <Size>0</Size>
           <Use>false</Use>
          </LMU.2>
          <LMU.3>
           <Base>0</Base>
           <Region>0</Region>
           <Size>0</Size>
           <Use>false</Use>
          </LMU.3>
          <LMU.4>
           <Base>0</Base>
           <Region>0</Region>
           <Size>0</Size>
           <Use>false</Use>
          </LMU.4>
          <LMU.5>
           <Base>0</Base>
           <Region>0</Region>
           <Size>0</Size>
           <Use>false</Use>
          </LMU.5>
          <LMU.6>
           <Base>0</Base>
           <Region>0</Region>
           <Size>0</Size>
           <Use>false</Use>
          </LMU.6>
          <LMU.7>
           <Base>0</Base>
           <Region>0</Region>
           <Size>0</Size>
           <Use>false</Use>
          </LMU.7>
          <LMU.8>
           <Base>0</Base>
           <Region>0</Region>
           <Size>0</Size>
           <Use>false</Use>
          </LMU.8>
          <LMU.9>
           <Base>0</Base>
           <Region>0</Region>
           <Size>0</Size>
           <Use>false</Use>
          </LMU.9>
         </TraceLMU>
         <TriggerEvent1p8>
          <BreakBeforeMake>true</BreakBeforeMake>
          <CompareType>Equality</CompareType>
          <Counters>NoChange</Counters>
          <Enabled>false</Enabled>
          <Load>false</Load>
          <Store>false</Store>
          <Type>PC</Type>
         </TriggerEvent1p8>
        </Aurix>
        <TriCore>
         <AllowCacheMonitorExecution>0</AllowCacheMonitorExecution>
         <ApplicationLBIST>0</ApplicationLBIST>
         <BRKIN>1</BRKIN>
         <ClearICRIEWhenStopped>0</ClearICRIEWhenStopped>
         <DAPClockkHz>5000</DAPClockkHz>
         <DAPE>0</DAPE>
         <DAPEClockkHz>10000</DAPEClockkHz>
         <DAPEDebugMode>2</DAPEDebugMode>
         <DebugMode>1</DebugMode>
         <FirstTraceTile>0</FirstTraceTile>
         <FlushTraceOnAGBTStop>0</FlushTraceOnAGBTStop>
         <NumTraceTiles>8</NumTraceTiles>
         <OnChipRamSize>0</OnChipRamSize>
         <OnChipRamStart>0</OnChipRamStart>
         <Password.0>0</Password.0>
         <Password.1>0</Password.1>
         <Password.2>0</Password.2>
         <Password.3>0</Password.3>
         <Password.4>0</Password.4>
         <Password.5>0</Password.5>
         <Password.6>0</Password.6>
         <Password.7>0</Password.7>
         <QuickLBIST>0</QuickLBIST>
         <ReserveBPPair type="B">AA</ReserveBPPair>
         <SingleStepMode>0</SingleStepMode>
         <TimeSource_Profiler>1</TimeSource_Profiler>
         <TraceBufferMode>1</TraceBufferMode>
         <TraceMemoryRegion>0</TraceMemoryRegion>
         <UseAllEmMemForTrace>1</UseAllEmMemForTrace>
         <UsePassword>0</UsePassword>
         <Event.0>
          <BBM>1</BBM>
          <BOD>0</BOD>
          <CNT>0</CNT>
          <CSP>0</CSP>
          <CST>0</CST>
          <Enable>0</Enable>
          <EVTA>2</EVTA>
          <SUSP>1</SUSP>
         </Event.0>
         <Event.1>
          <BBM>0</BBM>
          <BOD>0</BOD>
          <CNT>0</CNT>
          <CSP>0</CSP>
          <CST>0</CST>
          <Enable>0</Enable>
          <EVTA>0</EVTA>
          <SUSP>0</SUSP>
         </Event.1>
         <Event.2>
          <BBM>0</BBM>
          <BOD>0</BOD>
          <CNT>0</CNT>
          <CSP>0</CSP>
          <CST>0</CST>
          <Enable>0</Enable>
          <EVTA>0</EVTA>
          <SUSP>0</SUSP>
         </Event.2>
         <Event.3>
          <BBM>1</BBM>
          <BOD>0</BOD>
          <CNT>0</CNT>
          <CSP>0</CSP>
          <CST>0</CST>
          <Enable>0</Enable>
          <EVTA>2</EVTA>
          <SUSP>1</SUSP>
         </Event.3>
        </TriCore>
       </CPU>
       <Debug>
        <AllowAccessToUnimplementedRegisters>false</AllowAccessToUnimplementedRegisters>
        <AuroraBaudrate>2500M</AuroraBaudrate>
        <BootCore>0</BootCore>
        <Breakpoints>HW</Breakpoints>
        <CacheSWBPs>false</CacheSWBPs>
        <CalibrateClockVref>32</CalibrateClockVref>
        <CalibratePhase>0</CalibratePhase>
        <CalibrateVref>64</CalibrateVref>
        <ClockkHz>1000</ClockkHz>
        <CycleDuration>1e-09</CycleDuration>
        <DebugChannel>DAP</DebugChannel>
        <DebugClockkHz>4000</DebugClockkHz>
        <DetectWDT>false</DetectWDT>
        <DetectWDT_duration_ms>20</DetectWDT_duration_ms>
        <EndSession>Cleanup</EndSession>
        <ExtOscillatorClk>1000</ExtOscillatorClk>
        <GenerateAuroraClock>false</GenerateAuroraClock>
        <GenerateAuroraCRC>false</GenerateAuroraCRC>
        <IgnoreAccessErrors>false</IgnoreAccessErrors>
        <InitRAM>Auto</InitRAM>
        <LatchRESET>true</LatchRESET>
        <MemorySecurityND_Policy>Allow</MemorySecurityND_Policy>
        <MemorySecurity_CheckEntryPoint>false</MemorySecurity_CheckEntryPoint>
        <MemorySecurity_Policy>Allow</MemorySecurity_Policy>
        <MemorySecurity_Policy_Preset></MemorySecurity_Policy_Preset>
        <MultiCoreSync>true</MultiCoreSync>
        <NexusClockDivider>1</NexusClockDivider>
        <NexusDataRate>Single</NexusDataRate>
        <NexusForcePeriodicSYNC>false</NexusForcePeriodicSYNC>
        <NexusMDOWidth>18</NexusMDOWidth>
        <NexusMSEOWidth>1</NexusMSEOWidth>
        <NexusTermination_MCKO>false</NexusTermination_MCKO>
        <NexusTermination_MDO_MSEO>false</NexusTermination_MDO_MSEO>
        <NumAuroraLanes>1</NumAuroraLanes>
        <OCTInitAtStartup>true</OCTInitAtStartup>
        <PostResetDelay>0</PostResetDelay>
        <ReadMemoryOnMissingCode>true</ReadMemoryOnMissingCode>
        <ReadStatusInterval_ms>0</ReadStatusInterval_ms>
        <RequireDeviceIDMatch>false</RequireDeviceIDMatch>
        <RESETDriveWhenDetached>no</RESETDriveWhenDetached>
        <ResetDuration>20</ResetDuration>
        <ResetMethod>StopAndPreset</ResetMethod>
        <RESETMode>OD</RESETMode>
        <ResetPinConnected>true</ResetPinConnected>
        <ReversedAuroraLanes>false</ReversedAuroraLanes>
        <SamplingEdge>Pos</SamplingEdge>
        <StopAfterReset>false</StopAfterReset>
        <SuspendPeripheralsWhileStopped>false</SuspendPeripheralsWhileStopped>
        <TargetResetTimeout>100</TargetResetTimeout>
        <MemorySecurity_CheckParameters>
         <Entry>false</Entry>
         <Aurix>
          <ActiveDXCPL>false</ActiveDXCPL>
          <Always_._check_._BMHDs>true</Always_._check_._BMHDs>
          <BMHDPassword>false</BMHDPassword>
          <DF0ComplementSensing>false</DF0ComplementSensing>
          <DF1ComplementSensing>false</DF1ComplementSensing>
          <DisabledHWBootSelection>false</DisabledHWBootSelection>
          <DisabledLockstep>false</DisabledLockstep>
          <ESR0>false</ESR0>
          <HSM_._enabled>Don&apos;t care</HSM_._enabled>
          <HSM_._pins>Don&apos;t care</HSM_._pins>
          <HSMExclusivePFLASHSectors>false</HSMExclusivePFLASHSectors>
          <OscillatorSettings>false</OscillatorSettings>
          <OTP>false</OTP>
          <RAMInitialization>false</RAMInitialization>
          <RequirePFLASHReadProtectWithDebugLock>false</RequirePFLASHReadProtectWithDebugLock>
          <SwapAltBanksHSMCode>false</SwapAltBanksHSMCode>
         </Aurix>
        </MemorySecurity_CheckParameters>
       </Debug>
       <Debugging>
        <AllowAccessToUnimplementedRegisters>0</AllowAccessToUnimplementedRegisters>
        <AuroraBaudrate>2</AuroraBaudrate>
        <BootCore>0</BootCore>
        <Breakpoints>0</Breakpoints>
        <CacheDL>0</CacheDL>
        <CacheSWBPs>0</CacheSWBPs>
        <CalibrateClockVref>32</CalibrateClockVref>
        <CalibratePhase>0</CalibratePhase>
        <CalibrateVref>64</CalibrateVref>
        <ClockkHz>1000</ClockkHz>
        <CycleDuration type="D">0.****************</CycleDuration>
        <DebugClockkHz>4000</DebugClockkHz>
        <EndSession>0</EndSession>
        <ExtOscillatorClk>1000</ExtOscillatorClk>
        <GenerateAuroraClock>0</GenerateAuroraClock>
        <GenerateAuroraCRC>0</GenerateAuroraCRC>
        <IgnoreAccessErrors>0</IgnoreAccessErrors>
        <InitRAM>2</InitRAM>
        <LatchRESET>1</LatchRESET>
        <MemorySecurityND_Policy>2</MemorySecurityND_Policy>
        <MemorySecurity_CheckEntryPoint>0</MemorySecurity_CheckEntryPoint>
        <MemorySecurity_Policy>2</MemorySecurity_Policy>
        <MemorySecurity_Policy_Preset></MemorySecurity_Policy_Preset>
        <NexusClockDivider>0</NexusClockDivider>
        <NexusDataRate>0</NexusDataRate>
        <NexusForcePeriodicSYNC>0</NexusForcePeriodicSYNC>
        <NexusMDOWidth>3</NexusMDOWidth>
        <NexusMSEOWidth>0</NexusMSEOWidth>
        <NexusTermination_MCKO>0</NexusTermination_MCKO>
        <NexusTermination_MDO_MSEO>0</NexusTermination_MDO_MSEO>
        <NumAuroraLanes>1</NumAuroraLanes>
        <OCTInitAtStart>1</OCTInitAtStart>
        <PostResetDelay>0</PostResetDelay>
        <ReadMemoryOnMissingCode>1</ReadMemoryOnMissingCode>
        <ReadStatusInterval_ms>0</ReadStatusInterval_ms>
        <RequireDeviceIDMatch>0</RequireDeviceIDMatch>
        <ResetDelay>20</ResetDelay>
        <RESETDriveWhenDetached>0</RESETDriveWhenDetached>
        <ResetMethod>1</ResetMethod>
        <RESETMode>0</RESETMode>
        <ReversedAuroraLanes>0</ReversedAuroraLanes>
        <StopAfterReset>0</StopAfterReset>
        <TargetResetTimeout>100</TargetResetTimeout>
       </Debugging>
       <ExtWDT>
        <DebugEntryExit>
         <Address>0</Address>
         <DataEntry>0</DataEntry>
         <DataExit>0</DataExit>
         <DataMask>0</DataMask>
         <Enable>0</Enable>
         <MinTime>0</MinTime>
         <Size>0</Size>
        </DebugEntryExit>
        <PeriodicService>
         <Initialization>0</Initialization>
         <MinTime>1</MinTime>
         <Service>0</Service>
         <ServiceInRun>0</ServiceInRun>
         <Item.0>
          <Address>0</Address>
          <Data>0</Data>
          <Size>0</Size>
         </Item.0>
        </PeriodicService>
       </ExtWDT>
       <Hardware>
        <CheckVref>true</CheckVref>
        <DebugVccSource>Target</DebugVccSource>
        <DebugVccVoltage>3300</DebugVccVoltage>
        <WDTDrive>HighZ</WDTDrive>
       </Hardware>
       <InitSequence>
        <File></File>
        <InitAfterReset>0</InitAfterReset>
        <ResetCPUAfterDownload>0</ResetCPUAfterDownload>
        <Use>0</Use>
       </InitSequence>
       <InitSequence2nd>
        <File></File>
        <InitAfterReset>1</InitAfterReset>
        <ResetCPUAfterDownload>0</ResetCPUAfterDownload>
        <Use>0</Use>
       </InitSequence2nd>
       <JTAG>
        <DRPostfix>0</DRPostfix>
        <DRPrefix>0</DRPrefix>
        <IdleTCKCount>0</IdleTCKCount>
        <InhibitTRST>false</InhibitTRST>
        <InitScan>false</InitScan>
        <InitScanClock>500</InitScanClock>
        <IRPostfix>0</IRPostfix>
        <IRPrefix>0</IRPrefix>
        <ScanClock>1000</ScanClock>
        <SingleDevice>true</SingleDevice>
       </JTAG>
       <JTAGPos>
        <DRPostfix>0</DRPostfix>
        <DRPrefix>0</DRPrefix>
        <HWBurstClockkHz>1000</HWBurstClockkHz>
        <IdleTCKCount>0</IdleTCKCount>
        <InhibitTRST>0</InhibitTRST>
        <InitScanClockkHz>500</InitScanClockkHz>
        <InitScanSpeed>0</InitScanSpeed>
        <IRPostfix>0</IRPostfix>
        <IRPrefix>0</IRPrefix>
        <ScanSpeed1>0</ScanSpeed1>
        <SingleDevice>1</SingleDevice>
        <SlowScanDuringInit>0</SlowScanDuringInit>
       </JTAGPos>
       <MC>
        <Cores>
         <__.0>
          <ARMv7>
           <Exceptions>
            <MD>Leave</MD>
            <MF>Leave</MF>
            <MI>Leave</MI>
            <MP>Leave</MP>
            <MS>Leave</MS>
            <NSD>Leave</NSD>
            <NSF>Leave</NSF>
            <NSHC>Leave</NSHC>
            <NSHD>Leave</NSHD>
            <NSHE>Leave</NSHE>
            <NSHF>Leave</NSHF>
            <NSHI>Leave</NSHI>
            <NSHP>Leave</NSHP>
            <NSHU>Leave</NSHU>
            <NSI>Leave</NSI>
            <NSP>Leave</NSP>
            <NSS>Leave</NSS>
            <NSU>Leave</NSU>
            <R>Leave</R>
            <SD>Leave</SD>
            <SF>Leave</SF>
            <SI>Leave</SI>
            <SP>Leave</SP>
            <SS>Leave</SS>
            <SU>Leave</SU>
           </Exceptions>
          </ARMv7>
          <ARMv8>
           <CatchEvents>
            <MD>false</MD>
            <MF>false</MF>
            <MI>false</MI>
            <MP>false</MP>
            <MS>false</MS>
            <NSD>false</NSD>
            <NSE1>false</NSE1>
            <NSE2>false</NSE2>
            <NSF>false</NSF>
            <NSI>false</NSI>
            <NSP>false</NSP>
            <NSS>false</NSS>
            <NSU>false</NSU>
            <OSUCE>false</OSUCE>
            <RCE>false</RCE>
            <SD>false</SD>
            <SE1>false</SE1>
            <SE3>false</SE3>
            <SF>false</SF>
            <SI>false</SI>
            <SP>false</SP>
            <SS>false</SS>
            <SU>false</SU>
            <TDA>false</TDA>
           </CatchEvents>
          </ARMv8>
          <Common>
           <ConnectDebug>true</ConnectDebug>
           <DCache_EnableWriteThrough>false</DCache_EnableWriteThrough>
           <DCache_FlushWhenStopped>false</DCache_FlushWhenStopped>
           <Endian>LE</Endian>
           <MCSync>true</MCSync>
           <StopCPUActivities>false</StopCPUActivities>
           <UseSWBPs>false</UseSWBPs>
           <DebugEntry>
            <PresetAddr></PresetAddr>
            <PresetPCAfterStoppedInInit>NoPreset</PresetPCAfterStoppedInInit>
            <Type>NonIntrusive</Type>
           </DebugEntry>
          </Common>
          <Cortex>
           <Exceptions>
            <BUSERR>false</BUSERR>
            <CHKERR>false</CHKERR>
            <CORERESET>false</CORERESET>
            <HARDERR>false</HARDERR>
            <INTERR>false</INTERR>
            <MMERR>false</MMERR>
            <NOCPERR>false</NOCPERR>
            <STATERR>false</STATERR>
           </Exceptions>
          </Cortex>
          <e200>
           <DebugSelect>0x180</DebugSelect>
           <EVTOOnlyOnBP>false</EVTOOnlyOnBP>
           <NPIDRistransmittedwithinOTM>false</NPIDRistransmittedwithinOTM>
           <StopTimerDuringStep>false</StopTimerDuringStep>
           <StopWhenReleasedFromReset>false</StopWhenReleasedFromReset>
           <UseBDMMemAccessWhenStopped>false</UseBDMMemAccessWhenStopped>
           <UseTrapForSWBPsinPowerPCMode>false</UseTrapForSWBPsinPowerPCMode>
          </e200>
          <eTPU>
           <CLKS>false</CLKS>
           <HaltOnPrimaryCoreBP>false</HaltOnPrimaryCoreBP>
           <HTWN>false</HTWN>
           <PINS>false</PINS>
           <RegAccessHelperLocation>0x0</RegAccessHelperLocation>
          </eTPU>
          <Tricore>
           <ReserveBPPair_0>false</ReserveBPPair_0>
           <ReserveBPPair_1>false</ReserveBPPair_1>
           <ReserveBPPair_2>false</ReserveBPPair_2>
           <ReserveBPPair_3>false</ReserveBPPair_3>
          </Tricore>
          <V850>
           <MaskRESET>false</MaskRESET>
          </V850>
          <Xtensa>
           <StopSoCWhenStopped>false</StopSoCWhenStopped>
           <StopWhenSoCIsStopped>false</StopWhenSoCIsStopped>
          </Xtensa>
         </__.0>
         <__.1>
          <ARMv7>
           <Exceptions>
            <MD>Leave</MD>
            <MF>Leave</MF>
            <MI>Leave</MI>
            <MP>Leave</MP>
            <MS>Leave</MS>
            <NSD>Leave</NSD>
            <NSF>Leave</NSF>
            <NSHC>Leave</NSHC>
            <NSHD>Leave</NSHD>
            <NSHE>Leave</NSHE>
            <NSHF>Leave</NSHF>
            <NSHI>Leave</NSHI>
            <NSHP>Leave</NSHP>
            <NSHU>Leave</NSHU>
            <NSI>Leave</NSI>
            <NSP>Leave</NSP>
            <NSS>Leave</NSS>
            <NSU>Leave</NSU>
            <R>Leave</R>
            <SD>Leave</SD>
            <SF>Leave</SF>
            <SI>Leave</SI>
            <SP>Leave</SP>
            <SS>Leave</SS>
            <SU>Leave</SU>
           </Exceptions>
          </ARMv7>
          <ARMv8>
           <CatchEvents>
            <MD>false</MD>
            <MF>false</MF>
            <MI>false</MI>
            <MP>false</MP>
            <MS>false</MS>
            <NSD>false</NSD>
            <NSE1>false</NSE1>
            <NSE2>false</NSE2>
            <NSF>false</NSF>
            <NSI>false</NSI>
            <NSP>false</NSP>
            <NSS>false</NSS>
            <NSU>false</NSU>
            <OSUCE>false</OSUCE>
            <RCE>false</RCE>
            <SD>false</SD>
            <SE1>false</SE1>
            <SE3>false</SE3>
            <SF>false</SF>
            <SI>false</SI>
            <SP>false</SP>
            <SS>false</SS>
            <SU>false</SU>
            <TDA>false</TDA>
           </CatchEvents>
          </ARMv8>
          <Common>
           <ConnectDebug>true</ConnectDebug>
           <DCache_EnableWriteThrough>false</DCache_EnableWriteThrough>
           <DCache_FlushWhenStopped>false</DCache_FlushWhenStopped>
           <Endian>LE</Endian>
           <MCSync>true</MCSync>
           <StopCPUActivities>false</StopCPUActivities>
           <UseSWBPs>false</UseSWBPs>
           <DebugEntry>
            <PresetAddr></PresetAddr>
            <PresetPCAfterStoppedInInit>NoPreset</PresetPCAfterStoppedInInit>
            <Type>NonIntrusive</Type>
           </DebugEntry>
          </Common>
          <Cortex>
           <Exceptions>
            <BUSERR>false</BUSERR>
            <CHKERR>false</CHKERR>
            <CORERESET>false</CORERESET>
            <HARDERR>false</HARDERR>
            <INTERR>false</INTERR>
            <MMERR>false</MMERR>
            <NOCPERR>false</NOCPERR>
            <STATERR>false</STATERR>
           </Exceptions>
          </Cortex>
          <e200>
           <DebugSelect>0x180</DebugSelect>
           <EVTOOnlyOnBP>false</EVTOOnlyOnBP>
           <NPIDRistransmittedwithinOTM>false</NPIDRistransmittedwithinOTM>
           <StopTimerDuringStep>false</StopTimerDuringStep>
           <StopWhenReleasedFromReset>false</StopWhenReleasedFromReset>
           <UseBDMMemAccessWhenStopped>false</UseBDMMemAccessWhenStopped>
           <UseTrapForSWBPsinPowerPCMode>false</UseTrapForSWBPsinPowerPCMode>
          </e200>
          <eTPU>
           <CLKS>false</CLKS>
           <HaltOnPrimaryCoreBP>false</HaltOnPrimaryCoreBP>
           <HTWN>false</HTWN>
           <PINS>false</PINS>
           <RegAccessHelperLocation>0x0</RegAccessHelperLocation>
          </eTPU>
          <Tricore>
           <ReserveBPPair_0>false</ReserveBPPair_0>
           <ReserveBPPair_1>false</ReserveBPPair_1>
           <ReserveBPPair_2>false</ReserveBPPair_2>
           <ReserveBPPair_3>false</ReserveBPPair_3>
          </Tricore>
          <V850>
           <MaskRESET>false</MaskRESET>
          </V850>
          <Xtensa>
           <StopSoCWhenStopped>false</StopSoCWhenStopped>
           <StopWhenSoCIsStopped>false</StopWhenSoCIsStopped>
          </Xtensa>
         </__.1>
        </Cores>
       </MC>
       <Module>
        <CheckVref>1</CheckVref>
        <DebugVccSource>0</DebugVccSource>
        <DebugVccVoltage>3300</DebugVccVoltage>
        <HotAttach>0</HotAttach>
        <PowerSupply>2</PowerSupply>
       </Module>
       <Probe>
        <APName></APName>
        <Type>DTM</Type>
       </Probe>
       <SetupData>
        <ChallengeResponse>
         <Authenticator>
          <__.0>
           <Enabled>false</Enabled>
           <ExtTool>
            <CmdLine>%1</CmdLine>
            <Path></Path>
           </ExtTool>
          </__.0>
          <__.1>
           <Enabled>false</Enabled>
           <ExtTool>
            <CmdLine>%1</CmdLine>
            <Path></Path>
           </ExtTool>
          </__.1>
         </Authenticator>
        </ChallengeResponse>
        <PeripheralsSuspendInStop>
         <SuspendItems>
          <__.0>
           <Enabled>true</Enabled>
           <Name>STM0</Name>
          </__.0>
          <__.100>
           <Enabled>false</Enabled>
           <Name>DSADC</Name>
          </__.100>
          <__.101>
           <Enabled>false</Enabled>
           <Name>PSI5</Name>
          </__.101>
          <__.102>
           <Enabled>false</Enabled>
           <Name>PSI5S</Name>
          </__.102>
          <__.103>
           <Enabled>false</Enabled>
           <Name>HSSL</Name>
          </__.103>
          <__.10>
           <Enabled>false</Enabled>
           <Name>QSPI4</Name>
          </__.10>
          <__.11>
           <Enabled>false</Enabled>
           <Name>QSPI5</Name>
          </__.11>
          <__.12>
           <Enabled>false</Enabled>
           <Name>SMU</Name>
          </__.12>
          <__.13>
           <Enabled>false</Enabled>
           <Name>ASCLIN0</Name>
          </__.13>
          <__.14>
           <Enabled>false</Enabled>
           <Name>ASCLIN1</Name>
          </__.14>
          <__.15>
           <Enabled>false</Enabled>
           <Name>ASCLIN2</Name>
          </__.15>
          <__.16>
           <Enabled>false</Enabled>
           <Name>ASCLIN3</Name>
          </__.16>
          <__.17>
           <Enabled>false</Enabled>
           <Name>ASCLIN4</Name>
          </__.17>
          <__.18>
           <Enabled>false</Enabled>
           <Name>ASCLIN5</Name>
          </__.18>
          <__.19>
           <Enabled>false</Enabled>
           <Name>ASCLIN6</Name>
          </__.19>
          <__.1>
           <Enabled>true</Enabled>
           <Name>STM1</Name>
          </__.1>
          <__.20>
           <Enabled>false</Enabled>
           <Name>ASCLIN7</Name>
          </__.20>
          <__.21>
           <Enabled>false</Enabled>
           <Name>HSCT0</Name>
          </__.21>
          <__.22>
           <Enabled>false</Enabled>
           <Name>MSC0</Name>
          </__.22>
          <__.23>
           <Enabled>false</Enabled>
           <Name>MSC1</Name>
          </__.23>
          <__.24>
           <Enabled>false</Enabled>
           <Name>MSC2</Name>
          </__.24>
          <__.25>
           <Enabled>false</Enabled>
           <Name>MSC3</Name>
          </__.25>
          <__.26>
           <Enabled>false</Enabled>
           <Name>CAN0</Name>
          </__.26>
          <__.27>
           <Enabled>false</Enabled>
           <Name>CAN1</Name>
          </__.27>
          <__.28>
           <Enabled>false</Enabled>
           <Name>CAN2</Name>
          </__.28>
          <__.29>
           <Enabled>false</Enabled>
           <Name>SENT_RCR0</Name>
          </__.29>
          <__.2>
           <Enabled>true</Enabled>
           <Name>STM2</Name>
          </__.2>
          <__.30>
           <Enabled>false</Enabled>
           <Name>SENT_RCR1</Name>
          </__.30>
          <__.31>
           <Enabled>false</Enabled>
           <Name>SENT_RCR2</Name>
          </__.31>
          <__.32>
           <Enabled>false</Enabled>
           <Name>SENT_RCR3</Name>
          </__.32>
          <__.33>
           <Enabled>false</Enabled>
           <Name>SENT_RCR4</Name>
          </__.33>
          <__.34>
           <Enabled>false</Enabled>
           <Name>SENT_RCR5</Name>
          </__.34>
          <__.35>
           <Enabled>false</Enabled>
           <Name>SENT_RCR6</Name>
          </__.35>
          <__.36>
           <Enabled>false</Enabled>
           <Name>SENT_RCR7</Name>
          </__.36>
          <__.37>
           <Enabled>false</Enabled>
           <Name>SENT_RCR8</Name>
          </__.37>
          <__.38>
           <Enabled>false</Enabled>
           <Name>SENT_RCR9</Name>
          </__.38>
          <__.39>
           <Enabled>false</Enabled>
           <Name>SENT_RCR10</Name>
          </__.39>
          <__.3>
           <Enabled>true</Enabled>
           <Name>STM3</Name>
          </__.3>
          <__.40>
           <Enabled>false</Enabled>
           <Name>SENT_RCR11</Name>
          </__.40>
          <__.41>
           <Enabled>false</Enabled>
           <Name>SENT_RCR12</Name>
          </__.41>
          <__.42>
           <Enabled>false</Enabled>
           <Name>SENT_RCR13</Name>
          </__.42>
          <__.43>
           <Enabled>false</Enabled>
           <Name>SENT_RCR14</Name>
          </__.43>
          <__.44>
           <Enabled>false</Enabled>
           <Name>SENT_RCR15</Name>
          </__.44>
          <__.45>
           <Enabled>false</Enabled>
           <Name>SENT_RCR16</Name>
          </__.45>
          <__.46>
           <Enabled>false</Enabled>
           <Name>SENT_RCR17</Name>
          </__.46>
          <__.47>
           <Enabled>false</Enabled>
           <Name>SENT_RCR18</Name>
          </__.47>
          <__.48>
           <Enabled>false</Enabled>
           <Name>SENT_RCR19</Name>
          </__.48>
          <__.49>
           <Enabled>false</Enabled>
           <Name>SENT_RCR20</Name>
          </__.49>
          <__.4>
           <Enabled>true</Enabled>
           <Name>STM4</Name>
          </__.4>
          <__.50>
           <Enabled>false</Enabled>
           <Name>SENT_RCR21</Name>
          </__.50>
          <__.51>
           <Enabled>false</Enabled>
           <Name>SENT_RCR22</Name>
          </__.51>
          <__.52>
           <Enabled>false</Enabled>
           <Name>SENT_RCR23</Name>
          </__.52>
          <__.53>
           <Enabled>false</Enabled>
           <Name>SENT_RCR24</Name>
          </__.53>
          <__.54>
           <Enabled>false</Enabled>
           <Name>ERAY0</Name>
          </__.54>
          <__.55>
           <Enabled>false</Enabled>
           <Name>ERAY1</Name>
          </__.55>
          <__.56>
           <Enabled>false</Enabled>
           <Name>GTM</Name>
          </__.56>
          <__.57>
           <Enabled>false</Enabled>
           <Name>CCU0</Name>
          </__.57>
          <__.58>
           <Enabled>false</Enabled>
           <Name>CCU1</Name>
          </__.58>
          <__.59>
           <Enabled>false</Enabled>
           <Name>GPT</Name>
          </__.59>
          <__.5>
           <Enabled>true</Enabled>
           <Name>STM5</Name>
          </__.5>
          <__.60>
           <Enabled>false</Enabled>
           <Name>VADC</Name>
          </__.60>
          <__.61>
           <Enabled>false</Enabled>
           <Name>DSADC</Name>
          </__.61>
          <__.62>
           <Enabled>false</Enabled>
           <Name>PSI5</Name>
          </__.62>
          <__.63>
           <Enabled>false</Enabled>
           <Name>PSI5S</Name>
          </__.63>
          <__.64>
           <Enabled>false</Enabled>
           <Name>RIF0</Name>
          </__.64>
          <__.65>
           <Enabled>false</Enabled>
           <Name>RIF1</Name>
          </__.65>
          <__.66>
           <Enabled>false</Enabled>
           <Name>SPU0</Name>
          </__.66>
          <__.67>
           <Enabled>false</Enabled>
           <Name>SPU1</Name>
          </__.67>
          <__.68>
           <Enabled>false</Enabled>
           <Name>HSSL0</Name>
          </__.68>
          <__.69>
           <Enabled>false</Enabled>
           <Name>CONVCTRL</Name>
          </__.69>
          <__.6>
           <Enabled>false</Enabled>
           <Name>QSPI0</Name>
          </__.6>
          <__.70>
           <Enabled>true</Enabled>
           <Name>STM0</Name>
          </__.70>
          <__.71>
           <Enabled>true</Enabled>
           <Name>STM1</Name>
          </__.71>
          <__.72>
           <Enabled>false</Enabled>
           <Name>QSPI0</Name>
          </__.72>
          <__.73>
           <Enabled>false</Enabled>
           <Name>QSPI1</Name>
          </__.73>
          <__.74>
           <Enabled>false</Enabled>
           <Name>QSPI2</Name>
          </__.74>
          <__.75>
           <Enabled>false</Enabled>
           <Name>QSPI3</Name>
          </__.75>
          <__.76>
           <Enabled>false</Enabled>
           <Name>SMU</Name>
          </__.76>
          <__.77>
           <Enabled>false</Enabled>
           <Name>ASCLIN0</Name>
          </__.77>
          <__.78>
           <Enabled>false</Enabled>
           <Name>ASCLIN1</Name>
          </__.78>
          <__.79>
           <Enabled>false</Enabled>
           <Name>ASCLIN2</Name>
          </__.79>
          <__.7>
           <Enabled>false</Enabled>
           <Name>QSPI1</Name>
          </__.7>
          <__.80>
           <Enabled>false</Enabled>
           <Name>ASCLIN3</Name>
          </__.80>
          <__.81>
           <Enabled>false</Enabled>
           <Name>HSCT0</Name>
          </__.81>
          <__.82>
           <Enabled>false</Enabled>
           <Name>MSC0</Name>
          </__.82>
          <__.83>
           <Enabled>false</Enabled>
           <Name>MSC1</Name>
          </__.83>
          <__.84>
           <Enabled>false</Enabled>
           <Name>CAN NCR0</Name>
          </__.84>
          <__.85>
           <Enabled>false</Enabled>
           <Name>CAN NCR1</Name>
          </__.85>
          <__.86>
           <Enabled>false</Enabled>
           <Name>CAN NCR2</Name>
          </__.86>
          <__.87>
           <Enabled>false</Enabled>
           <Name>CAN NCR3</Name>
          </__.87>
          <__.88>
           <Enabled>false</Enabled>
           <Name>SENT_RCR0</Name>
          </__.88>
          <__.89>
           <Enabled>false</Enabled>
           <Name>SENT_RCR1</Name>
          </__.89>
          <__.8>
           <Enabled>false</Enabled>
           <Name>QSPI2</Name>
          </__.8>
          <__.90>
           <Enabled>false</Enabled>
           <Name>SENT_RCR2</Name>
          </__.90>
          <__.91>
           <Enabled>false</Enabled>
           <Name>SENT_RCR3</Name>
          </__.91>
          <__.92>
           <Enabled>false</Enabled>
           <Name>SENT_RCR4</Name>
          </__.92>
          <__.93>
           <Enabled>false</Enabled>
           <Name>SENT_RCR5</Name>
          </__.93>
          <__.94>
           <Enabled>false</Enabled>
           <Name>ERAY0</Name>
          </__.94>
          <__.95>
           <Enabled>false</Enabled>
           <Name>GTM</Name>
          </__.95>
          <__.96>
           <Enabled>false</Enabled>
           <Name>CCU0</Name>
          </__.96>
          <__.97>
           <Enabled>false</Enabled>
           <Name>CCU1</Name>
          </__.97>
          <__.98>
           <Enabled>false</Enabled>
           <Name>GPT</Name>
          </__.98>
          <__.99>
           <Enabled>false</Enabled>
           <Name>VADC</Name>
          </__.99>
          <__.9>
           <Enabled>false</Enabled>
           <Name>QSPI3</Name>
          </__.9>
         </SuspendItems>
        </PeripheralsSuspendInStop>
       </SetupData>
       <Synchronization>
        <Enabled>0</Enabled>
        <Core.0>
         <Master>0</Master>
         <Slave>0</Slave>
         <Sync type="B">AAAAAAAAAAAAAAAA</Sync>
        </Core.0>
        <Core.1>
         <Master>0</Master>
         <Slave>0</Slave>
         <Sync type="B">AAAAAAAAAAAAAAAA</Sync>
        </Core.1>
        <Core.2>
         <Master>0</Master>
         <Slave>0</Slave>
         <Sync type="B">AAAAAAAAAAAAAAAA</Sync>
        </Core.2>
        <Core.3>
         <Master>0</Master>
         <Slave>0</Slave>
         <Sync type="B">AAAAAAAAAAAAAAAA</Sync>
        </Core.3>
        <Core.4>
         <Master>0</Master>
         <Slave>0</Slave>
         <Sync type="B">AAAAAAAAAAAAAAAA</Sync>
        </Core.4>
        <Core.5>
         <Master>0</Master>
         <Slave>0</Slave>
         <Sync type="B">AAAAAAAAAAAAAAAA</Sync>
        </Core.5>
        <Core.6>
         <Master>0</Master>
         <Slave>0</Slave>
         <Sync type="B">AAAAAAAAAAAAAAAA</Sync>
        </Core.6>
        <Core.7>
         <Master>0</Master>
         <Slave>0</Slave>
         <Sync type="B">AAAAAAAAAAAAAAAA</Sync>
        </Core.7>
       </Synchronization>
      </BDM>
      <HW>
       <HW>14</HW>
       <Communication>
        <IPAddr></IPAddr>
        <IPUseGlobalDiscoveryPort>0</IPUseGlobalDiscoveryPort>
        <Mode>3</Mode>
        <TCPPortNumber>5313</TCPPortNumber>
        <USBDeviceName></USBDeviceName>
       </Communication>
       <IFX_DAS>
        <Config>
         <Str></Str>
        </Config>
       </IFX_DAS>
       <XCP>
        <Transport>TCPIP</Transport>
        <Common>
         <ConnectMode>0</ConnectMode>
         <CPU>AURIX</CPU>
         <KeyQueryDLL></KeyQueryDLL>
         <UseBulkAccessIfAvaliable>false</UseBulkAccessIfAvaliable>
        </Common>
        <TCPIP>
         <Port>0</Port>
         <Server></Server>
        </TCPIP>
       </XCP>
      </HW>
      <Trace>
       <Type>9</Type>
      </Trace>
     </HW>
    </HServerData>
   </REG_ROOT>
  </HServer>
 </Root>
