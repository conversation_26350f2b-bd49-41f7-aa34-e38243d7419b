	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc30056a --dep-file=Ifx_Crc.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2\\\\xf_asr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_Crc.src ../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_Crc.c"
	.compiler_name		"ctc"
	;source	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_Crc.c'

	
$TC16X
	
	.sdecl	'.text.Ifx_Crc.Ifx_Crc_init',code,cluster('Ifx_Crc_init')
	.sect	'.text.Ifx_Crc.Ifx_Crc_init'
	.align	2
	
	.global	Ifx_Crc_init
; Function Ifx_Crc_init
.L128:
Ifx_Crc_init:	.type	func
	ld.w	d15,[a5]16
.L398:
	and	d15,d6
.L399:
	jeq	d15,d6,.L2
.L400:
	mov	d2,#0
.L401:
	j	.L3
.L2:
	ld.w	d15,[a5]16
.L402:
	and	d15,d7
.L403:
	jeq	d15,d7,.L4
.L404:
	mov	d2,#0
.L405:
	j	.L5
.L4:
	st.a	[a4]16,a5
.L406:
	st.w	[a4],d7
.L407:
	st.w	[a4]4,d5
.L408:
	jne	d4,#0,.L6
.L409:
	st.w	[a4]12,d6
.L410:
	mov	d0,#0
.L266:
	j	.L7
.L8:
	ld.w	d15,[a5]12
.L267:
	and	d15,d6
.L411:
	sh	d6,#1
.L412:
	jeq	d15,#0,.L9
.L413:
	ld.w	d15,[a5]4
.L268:
	xor	d6,d15
.L9:
	add	d0,#1
.L7:
	ld.w	d15,[a5]
.L414:
	jlt	d0,d15,.L8
.L415:
	ld.w	d15,[a5]16
.L416:
	and	d6,d15
.L265:
	st.w	[a4]8,d6
.L417:
	j	.L10
.L6:
	st.w	[a4]8,d6
.L418:
	mov	d0,#0
.L269:
	j	.L11
.L12:
	and	d15,d6,#1
.L270:
	jz.t	d6:0,.L13
.L419:
	ld.w	d1,[a5]4
.L420:
	xor	d6,d1
.L13:
	sh	d6,#-1
.L421:
	jeq	d15,#0,.L14
.L422:
	ld.w	d15,[a5]12
.L271:
	or	d6,d15
.L14:
	add	d0,#1
.L11:
	ld.w	d15,[a5]
.L423:
	jlt	d0,d15,.L12
.L424:
	st.w	[a4]12,d6
.L10:
	mov	d2,#1
.L425:
	j	.L15
.L15:
.L5:
.L3:
	ret
.L181:
	
__Ifx_Crc_init_function_end:
	.size	Ifx_Crc_init,__Ifx_Crc_init_function_end-Ifx_Crc_init
.L149:
	; End of function
	
	.sdecl	'.text.Ifx_Crc.Ifx_Crc_createTable',code,cluster('Ifx_Crc_createTable')
	.sect	'.text.Ifx_Crc.Ifx_Crc_createTable'
	.align	2
	
	.global	Ifx_Crc_createTable
; Function Ifx_Crc_createTable
.L130:
Ifx_Crc_createTable:	.type	func
	mov.aa	a15,a4
.L272:
	mov	e8,d5,d4
	mov	d10,d6
.L273:
	mov	d0,#1
.L274:
	add	d15,d8,#-1
.L275:
	sh	d0,d0,d15
.L430:
	add	d0,#-1
.L431:
	sh	d0,#1
.L432:
	or	d11,d0,#1
.L276:
	jlt	d8,#1,.L16
.L277:
	mov	d15,#32
.L278:
	jge	d15,d8,.L17
.L16:
	mov	d2,#0
.L433:
	j	.L18
.L17:
	and	d15,d9,d11
.L434:
	jeq	d15,d9,.L19
.L279:
	mov	d2,#0
.L435:
	j	.L20
.L19:
	st.w	[a15],d8
.L280:
	st.w	[a15]4,d9
.L281:
	st.w	[a15]8,d10
.L436:
	mov	d0,#1
.L282:
	add	d15,d8,#-1
.L283:
	sh	d0,d0,d15
.L437:
	st.w	[a15]12,d0
.L438:
	st.w	[a15]16,d11
.L202:
	mov	d12,#0
.L284:
	j	.L21
.L22:
	mov	d2,d12
.L285:
	jeq	d10,#0,.L23
.L439:
	mov	d5,#8
	mov	d4,d12
.L287:
	call	Ifx_Crc_reflect
.L23:
	add	d15,d8,#-8
.L288:
	sh	d4,d2,d15
.L286:
	mov	d0,#0
.L290:
	j	.L24
.L25:
	ld.w	d15,[a15]12
.L291:
	and	d15,d4
.L440:
	sh	d4,#1
.L441:
	jeq	d15,#0,.L26
.L292:
	xor	d4,d9
.L26:
	add	d0,#1
.L24:
	mov	d15,#8
.L442:
	jlt	d0,d15,.L25
.L443:
	jeq	d10,#0,.L27
.L293:
	mov	d5,d8
.L294:
	call	Ifx_Crc_reflect
.L289:
	mov	d4,d2
.L27:
	and	d4,d11
.L444:
	mov	d15,#8
.L295:
	jlt	d15,d8,.L28
.L208:
	mov.d	d15,a15
.L296:
	add	d15,d15,#20
.L297:
	mov.a	a2,d15
.L298:
	addsc.a	a2,a2,d12,#0
.L299:
	st.b	[a2],d4
.L209:
	j	.L29
.L28:
	mov	d15,#16
.L300:
	jlt	d15,d8,.L30
.L212:
	mov.d	d15,a15
.L301:
	add	d15,d15,#20
.L302:
	mov.a	a2,d15
.L304:
	mul	d15,d12,#2
.L303:
	addsc.a	a2,a2,d15,#0
.L305:
	st.h	[a2],d4
.L213:
	j	.L31
.L30:
	mov.d	d15,a15
.L306:
	add	d15,d15,#20
.L307:
	mov.a	a2,d15
.L309:
	mul	d15,d12,#4
.L308:
	addsc.a	a2,a2,d15,#0
.L310:
	st.w	[a2],d4
.L31:
.L29:
	add	d12,#1
.L21:
	mov	d15,#256
.L445:
	jlt	d12,d15,.L22
.L203:
	mov	d2,#1
.L446:
	j	.L32
.L32:
.L20:
.L18:
	ret
.L195:
	
__Ifx_Crc_createTable_function_end:
	.size	Ifx_Crc_createTable,__Ifx_Crc_createTable_function_end-Ifx_Crc_createTable
.L154:
	; End of function
	
	.sdecl	'.text.Ifx_Crc.Ifx_Crc_reflect',code,cluster('Ifx_Crc_reflect')
	.sect	'.text.Ifx_Crc.Ifx_Crc_reflect'
	.align	2
	
	.global	Ifx_Crc_reflect
; Function Ifx_Crc_reflect
.L132:
Ifx_Crc_reflect:	.type	func
	mov	d1,#1
.L312:
	mov	d2,#0
.L313:
	mov	d0,#1
.L732:
	add	d5,#-1
.L311:
	sh	d0,d0,d5
.L314:
	j	.L33
.L34:
	and	d15,d4,d0
.L733:
	jeq	d15,#0,.L35
.L734:
	or	d2,d1
.L35:
	sh	d1,#1
.L735:
	sh	d0,#-1
.L33:
	jne	d0,#0,.L34
.L736:
	j	.L36
.L36:
	ret
.L259:
	
__Ifx_Crc_reflect_function_end:
	.size	Ifx_Crc_reflect,__Ifx_Crc_reflect_function_end-Ifx_Crc_reflect
.L179:
	; End of function
	
	.sdecl	'.text.Ifx_Crc.Ifx_Crc_tableFast',code,cluster('Ifx_Crc_tableFast')
	.sect	'.text.Ifx_Crc.Ifx_Crc_tableFast'
	.align	2
	
	.global	Ifx_Crc_tableFast
; Function Ifx_Crc_tableFast
.L134:
Ifx_Crc_tableFast:	.type	func
	mov.aa	a15,a4
.L317:
	mov.aa	a12,a5
.L319:
	mov	d9,d4
.L320:
	ld.w	d4,[a15]8
.L316:
	ld.a	a2,[a15]16
.L451:
	ld.w	d8,[a2]
.L321:
	add	d8,#-8
.L452:
	ld.a	a2,[a15]16
.L453:
	ld.w	d15,[a2]8
.L454:
	jeq	d15,#0,.L37
.L455:
	ld.a	a2,[a15]16
.L456:
	ld.w	d5,[a2]
	call	Ifx_Crc_reflect
.L315:
	mov	d4,d2
.L37:
	ld.a	a2,[a15]16
.L457:
	ld.w	d0,[a2]
.L458:
	mov	d15,#8
.L459:
	jlt	d15,d0,.L38
.L224:
	ld.w	d15,[a15]16
.L460:
	add	d0,d15,#20
.L323:
	mov.a	a2,d0
.L324:
	ld.a	a4,[a15]16
.L461:
	ld.w	d15,[a4]8
.L462:
	jne	d15,#0,.L39
.L463:
	j	.L40
.L41:
	add	d9,#-1
.L464:
	sh	d0,d4,#8
.L465:
	extr.u	d15,d4,d8,#8
.L466:
	ld.bu	d1,[a12]
.L467:
	xor	d15,d1
.L468:
	addsc.a	a4,a2,d15,#0
	ld.bu	d4,[a4]
.L469:
	xor	d4,d0
.L470:
	add.a	a12,#1
.L40:
	jne	d9,#0,.L41
.L471:
	j	.L42
.L39:
	j	.L43
.L44:
	add	d9,#-1
.L472:
	sh	d1,d4,#-8
.L473:
	and	d15,d4,#255
.L474:
	ld.bu	d0,[a12]
.L475:
	xor	d15,d0
.L476:
	addsc.a	a4,a2,d15,#0
	ld.bu	d4,[a4]
.L477:
	xor	d4,d1
.L478:
	add.a	a12,#1
.L43:
	jne	d9,#0,.L44
.L42:
	j	.L45
.L38:
	ld.a	a2,[a15]16
.L479:
	ld.w	d0,[a2]
.L480:
	mov	d15,#16
.L481:
	jlt	d15,d0,.L46
.L226:
	ld.w	d15,[a15]16
.L482:
	add	d15,d15,#20
.L325:
	mov.a	a2,d15
.L327:
	ld.a	a4,[a15]16
.L483:
	ld.w	d15,[a4]8
.L326:
	jne	d15,#0,.L47
.L484:
	j	.L48
.L49:
	add	d9,#-1
.L485:
	sh	d0,d4,#8
.L486:
	extr.u	d15,d4,d8,#8
.L487:
	ld.bu	d1,[a12]
.L488:
	xor	d15,d1
.L489:
	mul	d15,d15,#2
	addsc.a	a4,a2,d15,#0
	ld.hu	d4,[a4]0
.L490:
	xor	d4,d0
.L491:
	add.a	a12,#1
.L48:
	jne	d9,#0,.L49
.L492:
	j	.L50
.L47:
	j	.L51
.L52:
	add	d9,#-1
.L493:
	sh	d1,d4,#-8
.L494:
	and	d15,d4,#255
.L495:
	ld.bu	d0,[a12]
.L496:
	xor	d15,d0
.L497:
	mul	d15,d15,#2
	addsc.a	a4,a2,d15,#0
	ld.hu	d4,[a4]0
.L498:
	xor	d4,d1
.L499:
	add.a	a12,#1
.L51:
	jne	d9,#0,.L52
.L50:
	j	.L53
.L46:
	ld.w	d15,[a15]16
.L500:
	add	d15,d15,#20
.L328:
	mov.a	a2,d15
.L330:
	ld.a	a4,[a15]16
.L501:
	ld.w	d15,[a4]8
.L329:
	jne	d15,#0,.L54
.L502:
	j	.L55
.L56:
	add	d9,#-1
.L503:
	sh	d0,d4,#8
.L504:
	extr.u	d15,d4,d8,#8
.L505:
	ld.bu	d1,[a12]
.L506:
	xor	d15,d1
.L507:
	mul	d15,d15,#4
	addsc.a	a4,a2,d15,#0
	ld.w	d4,[a4]
.L508:
	xor	d4,d0
.L509:
	add.a	a12,#1
.L55:
	jne	d9,#0,.L56
.L510:
	j	.L57
.L54:
	j	.L58
.L59:
	add	d9,#-1
.L511:
	sh	d1,d4,#-8
.L512:
	and	d15,d4,#255
.L513:
	ld.bu	d0,[a12]
.L514:
	xor	d15,d0
.L515:
	mul	d15,d15,#4
	addsc.a	a4,a2,d15,#0
	ld.w	d4,[a4]
.L516:
	xor	d4,d1
.L517:
	add.a	a12,#1
.L58:
	jne	d9,#0,.L59
.L57:
.L53:
.L45:
	ld.w	d15,[a15]4
.L518:
	ld.a	a2,[a15]16
.L519:
	ld.w	d0,[a2]8
.L520:
	xor	d15,d0
.L521:
	jeq	d15,#0,.L60
.L522:
	ld.a	a2,[a15]16
.L523:
	ld.w	d5,[a2]
	call	Ifx_Crc_reflect
.L322:
	mov	d4,d2
.L60:
	ld.w	d15,[a15]
.L524:
	xor	d4,d15
.L525:
	ld.a	a15,[a15]16
.L318:
	ld.w	d15,[a15]16
.L526:
	and	d2,d4,d15
.L331:
	j	.L61
.L61:
	ret
.L218:
	
__Ifx_Crc_tableFast_function_end:
	.size	Ifx_Crc_tableFast,__Ifx_Crc_tableFast_function_end-Ifx_Crc_tableFast
.L159:
	; End of function
	
	.sdecl	'.text.Ifx_Crc.Ifx_Crc_table',code,cluster('Ifx_Crc_table')
	.sect	'.text.Ifx_Crc.Ifx_Crc_table'
	.align	2
	
	.global	Ifx_Crc_table
; Function Ifx_Crc_table
.L136:
Ifx_Crc_table:	.type	func
	mov.aa	a15,a4
.L334:
	mov.aa	a12,a5
.L336:
	mov	d9,d4
.L337:
	ld.w	d4,[a15]12
.L333:
	ld.a	a2,[a15]16
.L531:
	ld.w	d8,[a2]
.L339:
	add	d8,#-8
.L532:
	ld.a	a2,[a15]16
.L533:
	ld.w	d15,[a2]8
.L534:
	jeq	d15,#0,.L62
.L535:
	ld.a	a2,[a15]16
.L536:
	ld.w	d5,[a2]
	call	Ifx_Crc_reflect
.L332:
	mov	d4,d2
.L62:
	ld.a	a2,[a15]16
.L537:
	ld.w	d0,[a2]
.L538:
	mov	d15,#8
.L539:
	jlt	d15,d0,.L63
.L235:
	ld.w	d15,[a15]16
.L540:
	add	d0,d15,#20
.L341:
	mov.a	a4,d0
.L342:
	ld.a	a2,[a15]16
.L541:
	ld.w	d15,[a2]8
.L542:
	jne	d15,#0,.L64
.L543:
	j	.L65
.L66:
	add	d9,#-1
.L544:
	sh	d0,d4,#8
.L545:
	ld.bu	d15,[a12]
.L546:
	or	d0,d15
.L547:
	extr.u	d15,d4,d8,#8
.L548:
	addsc.a	a2,a4,d15,#0
	ld.bu	d4,[a2]
.L549:
	xor	d4,d0
.L550:
	add.a	a12,#1
.L65:
	jne	d9,#0,.L66
.L551:
	mov	d15,#1
.L338:
	rsub	d15,#0
	j	.L67
.L64:
	j	.L68
.L69:
	add	d9,#-1
.L552:
	sh	d0,d4,#-8
.L553:
	ld.bu	d15,[a12]
.L554:
	sha	d15,d15,d8
.L555:
	or	d0,d15
.L556:
	and	d15,d4,#255
.L557:
	addsc.a	a2,a4,d15,#0
	ld.bu	d4,[a2]
.L558:
	xor	d4,d0
.L559:
	add.a	a12,#1
.L68:
	jne	d9,#0,.L69
.L560:
	mov	d15,#1
.L343:
	rsub	d15,#0
.L67:
	ld.a	a2,[a15]16
.L561:
	ld.w	d0,[a2]8
.L562:
	jne	d0,#0,.L70
.L563:
	add	d0,d15,#1
.L344:
	j	.L71
.L72:
	sh	d1,d4,#8
.L564:
	extr.u	d15,d4,d8,#8
.L565:
	addsc.a	a2,a4,d15,#0
	ld.bu	d4,[a2]
.L566:
	xor	d4,d1
.L567:
	add	d0,#1
.L71:
	ld.a	a2,[a15]16
.L568:
	ld.w	d15,[a2]
.L569:
	sha	d15,#-3
.L570:
	jlt.u	d0,d15,.L72
.L571:
	j	.L73
.L70:
	add	d0,d15,#1
.L345:
	j	.L74
.L75:
	sh	d1,d4,#-8
.L572:
	and	d15,d4,#255
.L573:
	addsc.a	a2,a4,d15,#0
	ld.bu	d4,[a2]
.L574:
	xor	d4,d1
.L575:
	add	d0,#1
.L74:
	ld.a	a2,[a15]16
.L576:
	ld.w	d15,[a2]
.L577:
	sha	d15,#-3
.L578:
	jlt.u	d0,d15,.L75
.L73:
	j	.L76
.L63:
	ld.a	a2,[a15]16
.L579:
	ld.w	d0,[a2]
.L580:
	mov	d15,#16
.L581:
	jlt	d15,d0,.L77
.L237:
	ld.w	d15,[a15]16
.L582:
	add	d15,d15,#20
.L347:
	mov.a	a4,d15
.L349:
	ld.a	a2,[a15]16
.L583:
	ld.w	d15,[a2]8
.L348:
	jne	d15,#0,.L78
.L584:
	j	.L79
.L80:
	add	d9,#-1
.L585:
	sh	d0,d4,#8
.L586:
	ld.bu	d15,[a12]
.L587:
	or	d0,d15
.L588:
	extr.u	d15,d4,d8,#8
.L589:
	mul	d15,d15,#2
	addsc.a	a2,a4,d15,#0
	ld.hu	d4,[a2]0
.L590:
	xor	d4,d0
.L591:
	add.a	a12,#1
.L79:
	jne	d9,#0,.L80
.L592:
	mov	d15,#1
.L346:
	rsub	d15,#0
	j	.L81
.L78:
	j	.L82
.L83:
	add	d9,#-1
.L593:
	sh	d0,d4,#-8
.L594:
	ld.bu	d15,[a12]
.L595:
	sha	d15,d15,d8
.L596:
	or	d0,d15
.L597:
	and	d15,d4,#255
.L598:
	mul	d15,d15,#2
	addsc.a	a2,a4,d15,#0
	ld.hu	d4,[a2]0
.L599:
	xor	d4,d0
.L600:
	add.a	a12,#1
.L82:
	jne	d9,#0,.L83
.L601:
	mov	d15,#1
.L350:
	rsub	d15,#0
.L81:
	ld.a	a2,[a15]16
.L602:
	ld.w	d0,[a2]8
.L603:
	jne	d0,#0,.L84
.L604:
	add	d0,d15,#1
.L351:
	j	.L85
.L86:
	sh	d1,d4,#8
.L605:
	extr.u	d15,d4,d8,#8
.L606:
	mul	d15,d15,#2
	addsc.a	a2,a4,d15,#0
	ld.hu	d4,[a2]0
.L607:
	xor	d4,d1
.L608:
	add	d0,#1
.L85:
	ld.a	a2,[a15]16
.L609:
	ld.w	d15,[a2]
.L610:
	sha	d15,#-3
.L611:
	jlt.u	d0,d15,.L86
.L612:
	j	.L87
.L84:
	add	d0,d15,#1
.L352:
	j	.L88
.L89:
	sh	d1,d4,#-8
.L613:
	and	d15,d4,#255
.L614:
	mul	d15,d15,#2
	addsc.a	a2,a4,d15,#0
	ld.hu	d4,[a2]0
.L615:
	xor	d4,d1
.L616:
	add	d0,#1
.L88:
	ld.a	a2,[a15]16
.L617:
	ld.w	d15,[a2]
.L618:
	sha	d15,#-3
.L619:
	jlt.u	d0,d15,.L89
.L87:
	j	.L90
.L77:
	ld.a	a2,[a15]16
.L620:
	ld.w	d0,[a2]
.L621:
	mov	d15,#32
.L622:
	jlt	d15,d0,.L91
.L239:
	ld.w	d15,[a15]16
.L623:
	add	d15,d15,#20
.L354:
	mov.a	a4,d15
.L356:
	ld.a	a2,[a15]16
.L624:
	ld.w	d15,[a2]8
.L355:
	jne	d15,#0,.L92
.L625:
	j	.L93
.L94:
	add	d9,#-1
.L626:
	sh	d0,d4,#8
.L627:
	ld.bu	d15,[a12]
.L628:
	or	d0,d15
.L629:
	extr.u	d15,d4,d8,#8
.L630:
	mul	d15,d15,#4
	addsc.a	a2,a4,d15,#0
	ld.w	d4,[a2]
.L631:
	xor	d4,d0
.L632:
	add.a	a12,#1
.L93:
	jne	d9,#0,.L94
.L633:
	mov	d15,#1
.L353:
	rsub	d15,#0
	j	.L95
.L92:
	j	.L96
.L97:
	add	d9,#-1
.L634:
	sh	d0,d4,#-8
.L635:
	ld.bu	d15,[a12]
.L636:
	sha	d15,d15,d8
.L637:
	or	d0,d15
.L638:
	and	d15,d4,#255
.L639:
	mul	d15,d15,#4
	addsc.a	a2,a4,d15,#0
	ld.w	d4,[a2]
.L640:
	xor	d4,d0
.L641:
	add.a	a12,#1
.L96:
	jne	d9,#0,.L97
.L642:
	mov	d15,#1
.L357:
	rsub	d15,#0
.L95:
	ld.a	a2,[a15]16
.L643:
	ld.w	d0,[a2]8
.L644:
	jne	d0,#0,.L98
.L645:
	add	d0,d15,#1
.L358:
	j	.L99
.L100:
	sh	d1,d4,#8
.L646:
	extr.u	d15,d4,d8,#8
.L647:
	mul	d15,d15,#4
	addsc.a	a2,a4,d15,#0
	ld.w	d4,[a2]
.L648:
	xor	d4,d1
.L649:
	add	d0,#1
.L99:
	ld.a	a2,[a15]16
.L650:
	ld.w	d15,[a2]
.L651:
	sha	d15,#-3
.L652:
	jlt.u	d0,d15,.L100
.L653:
	j	.L101
.L98:
	add	d0,d15,#1
.L359:
	j	.L102
.L103:
	sh	d1,d4,#-8
.L654:
	and	d15,d4,#255
.L655:
	mul	d15,d15,#4
	addsc.a	a2,a4,d15,#0
	ld.w	d4,[a2]
.L656:
	xor	d4,d1
.L657:
	add	d0,#1
.L102:
	ld.a	a2,[a15]16
.L658:
	ld.w	d15,[a2]
.L659:
	sha	d15,#-3
.L660:
	jlt.u	d0,d15,.L103
.L101:
.L91:
.L90:
.L76:
	ld.w	d15,[a15]4
.L661:
	ld.a	a2,[a15]16
.L662:
	ld.w	d0,[a2]8
.L663:
	xor	d15,d0
.L664:
	jeq	d15,#0,.L104
.L665:
	ld.a	a2,[a15]16
.L666:
	ld.w	d5,[a2]
	call	Ifx_Crc_reflect
.L340:
	mov	d4,d2
.L104:
	ld.w	d15,[a15]
.L667:
	xor	d4,d15
.L668:
	ld.a	a15,[a15]16
.L335:
	ld.w	d15,[a15]16
.L669:
	and	d2,d4,d15
.L360:
	j	.L105
.L105:
	ret
.L229:
	
__Ifx_Crc_table_function_end:
	.size	Ifx_Crc_table,__Ifx_Crc_table_function_end-Ifx_Crc_table
.L164:
	; End of function
	
	.sdecl	'.text.Ifx_Crc.Ifx_Crc_bitByBit',code,cluster('Ifx_Crc_bitByBit')
	.sect	'.text.Ifx_Crc.Ifx_Crc_bitByBit'
	.align	2
	
	.global	Ifx_Crc_bitByBit
; Function Ifx_Crc_bitByBit
.L138:
Ifx_Crc_bitByBit:	.type	func
	mov.aa	a15,a4
.L361:
	mov.aa	a12,a5
.L363:
	mov	d8,d4
.L364:
	ld.w	d9,[a15]12
.L365:
	mov	d10,#0
.L367:
	j	.L106
.L107:
	ld.bu	d4,[a12]
.L369:
	add.a	a12,#1
.L674:
	ld.a	a2,[a15]16
.L675:
	ld.w	d15,[a2]8
.L676:
	jeq	d15,#0,.L108
.L677:
	mov	d5,#8
	call	Ifx_Crc_reflect
.L370:
	mov	d4,d2
.L108:
	mov	d0,#128
.L371:
	j	.L109
.L110:
	ld.a	a2,[a15]16
.L678:
	ld.w	d15,[a2]12
.L679:
	and	d1,d9,d15
.L372:
	sh	d9,#1
.L680:
	and	d15,d4,d0
.L681:
	jeq	d15,#0,.L111
.L682:
	or	d9,d9,#1
.L111:
	jeq	d1,#0,.L112
.L683:
	ld.a	a2,[a15]16
.L684:
	ld.w	d15,[a2]4
.L685:
	xor	d9,d15
.L112:
	sh	d0,#-1
.L109:
	jne	d0,#0,.L110
.L686:
	add	d10,#1
.L106:
	jlt.u	d10,d8,.L107
.L687:
	mov	d0,#0
.L368:
	j	.L113
.L114:
	ld.a	a2,[a15]16
.L688:
	ld.w	d15,[a2]12
.L373:
	and	d15,d9
.L689:
	sh	d9,#1
.L690:
	jeq	d15,#0,.L115
.L691:
	ld.a	a2,[a15]16
.L692:
	ld.w	d15,[a2]4
.L374:
	xor	d9,d15
.L115:
	add	d0,#1
.L113:
	ld.a	a2,[a15]16
.L693:
	ld.w	d15,[a2]
.L694:
	jlt.u	d0,d15,.L114
.L695:
	ld.w	d15,[a15]4
.L696:
	jeq	d15,#0,.L116
.L697:
	ld.a	a2,[a15]16
.L698:
	ld.w	d5,[a2]
	mov	d4,d9
.L375:
	call	Ifx_Crc_reflect
.L366:
	mov	d9,d2
.L116:
	ld.w	d15,[a15]
.L699:
	xor	d9,d15
.L700:
	ld.a	a15,[a15]16
.L362:
	ld.w	d15,[a15]16
.L701:
	and	d2,d9,d15
.L376:
	j	.L117
.L117:
	ret
.L241:
	
__Ifx_Crc_bitByBit_function_end:
	.size	Ifx_Crc_bitByBit,__Ifx_Crc_bitByBit_function_end-Ifx_Crc_bitByBit
.L169:
	; End of function
	
	.sdecl	'.text.Ifx_Crc.Ifx_Crc_bitByBitFast',code,cluster('Ifx_Crc_bitByBitFast')
	.sect	'.text.Ifx_Crc.Ifx_Crc_bitByBitFast'
	.align	2
	
	.global	Ifx_Crc_bitByBitFast
; Function Ifx_Crc_bitByBitFast
.L140:
Ifx_Crc_bitByBitFast:	.type	func
	mov.aa	a15,a4
.L377:
	mov.aa	a12,a5
.L379:
	mov	d8,d4
.L380:
	ld.w	d9,[a15]8
.L381:
	mov	d10,#0
.L383:
	j	.L118
.L119:
	ld.bu	d4,[a12]
.L384:
	add.a	a12,#1
.L706:
	ld.a	a2,[a15]16
.L707:
	ld.w	d15,[a2]8
.L708:
	jeq	d15,#0,.L120
.L709:
	mov	d5,#8
	call	Ifx_Crc_reflect
.L385:
	mov	d4,d2
.L120:
	mov	d0,#128
.L386:
	j	.L121
.L122:
	ld.a	a2,[a15]16
.L710:
	ld.w	d15,[a2]12
.L711:
	and	d1,d9,d15
.L387:
	sh	d9,#1
.L712:
	and	d15,d4,d0
.L713:
	jeq	d15,#0,.L123
.L714:
	ld.a	a2,[a15]16
.L715:
	ld.w	d15,[a2]12
.L716:
	xor	d1,d15
.L123:
	jeq	d1,#0,.L124
.L717:
	ld.a	a2,[a15]16
.L718:
	ld.w	d15,[a2]4
.L719:
	xor	d9,d15
.L124:
	sh	d0,#-1
.L121:
	jne	d0,#0,.L122
.L720:
	add	d10,#1
.L118:
	jlt.u	d10,d8,.L119
.L721:
	ld.w	d15,[a15]4
.L722:
	jeq	d15,#0,.L125
.L723:
	ld.a	a2,[a15]16
.L724:
	ld.w	d5,[a2]
	mov	d4,d9
.L388:
	call	Ifx_Crc_reflect
.L382:
	mov	d9,d2
.L125:
	ld.w	d15,[a15]
.L725:
	xor	d9,d15
.L726:
	ld.a	a15,[a15]16
.L378:
	ld.w	d15,[a15]16
.L727:
	and	d2,d9,d15
.L389:
	j	.L126
.L126:
	ret
.L250:
	
__Ifx_Crc_bitByBitFast_function_end:
	.size	Ifx_Crc_bitByBitFast,__Ifx_Crc_bitByBitFast_function_end-Ifx_Crc_bitByBitFast
.L174:
	; End of function
	
	.calls	'Ifx_Crc_createTable','Ifx_Crc_reflect'
	.calls	'Ifx_Crc_tableFast','Ifx_Crc_reflect'
	.calls	'Ifx_Crc_table','Ifx_Crc_reflect'
	.calls	'Ifx_Crc_bitByBit','Ifx_Crc_reflect'
	.calls	'Ifx_Crc_bitByBitFast','Ifx_Crc_reflect'
	.calls	'Ifx_Crc_init','',0
	.calls	'Ifx_Crc_createTable','',0
	.calls	'Ifx_Crc_reflect','',0
	.calls	'Ifx_Crc_tableFast','',0
	.calls	'Ifx_Crc_table','',0
	.calls	'Ifx_Crc_bitByBit','',0
	.calls	'Ifx_Crc_bitByBitFast','',0
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L142:
	.word	882
	.half	3
	.word	.L143
	.byte	4
.L141:
	.byte	1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_Crc.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L144
.L180:
	.byte	2
	.byte	'unsigned char',0,1,8
.L189:
	.byte	2
	.byte	'unsigned long int',0,4,7
.L186:
	.byte	2
	.byte	'long int',0,4,5,3,1,62,9,20,4
	.byte	'order',0
	.word	272
	.byte	4,2,35,0,4
	.byte	'polynom',0
	.word	251
	.byte	4,2,35,4,4
	.byte	'refin',0
	.word	272
	.byte	4,2,35,8,4
	.byte	'crchighbit',0
	.word	251
	.byte	4,2,35,12,4
	.byte	'crcmask',0
	.word	251
	.byte	4,2,35,16,0,5
	.word	284
.L184:
	.byte	6
	.word	374
	.byte	3,1,88,9,20,4
	.byte	'crcxor',0
	.word	251
	.byte	4,2,35,0,4
	.byte	'refout',0
	.word	272
	.byte	4,2,35,4,4
	.byte	'crcinit_direct',0
	.word	251
	.byte	4,2,35,8,4
	.byte	'crcinit_nondirect',0
	.word	251
	.byte	4,2,35,12,4
	.byte	'table',0
	.word	379
	.byte	4,2,35,16,0
.L182:
	.byte	6
	.word	384
.L196:
	.byte	6
	.word	284
.L210:
	.byte	6
	.word	234
	.byte	2
	.byte	'unsigned short int',0,2,7
.L214:
	.byte	6
	.word	503
.L216:
	.byte	6
	.word	251
	.byte	2
	.byte	'short int',0,2,5,7
	.byte	'__wchar_t',0,2,1,1
	.word	535
	.byte	2
	.byte	'unsigned int',0,4,7,7
	.byte	'__size_t',0,2,1,1
	.word	566
	.byte	2
	.byte	'int',0,4,5,7
	.byte	'__ptrdiff_t',0,2,1,1
	.word	599
	.byte	8,1,6
	.word	626
	.byte	7
	.byte	'__codeptr',0,2,1,1
	.word	628
	.byte	7
	.byte	'boolean',0,3,101,29
	.word	234
	.byte	7
	.byte	'uint8',0,3,105,29
	.word	234
	.byte	7
	.byte	'uint16',0,3,109,29
	.word	503
	.byte	7
	.byte	'uint32',0,3,113,29
	.word	251
	.byte	7
	.byte	'sint16',0,3,126,29
	.word	535
	.byte	7
	.byte	'sint32',0,3,131,1,29
	.word	272
	.byte	2
	.byte	'long long int',0,8,5,7
	.byte	'sint64',0,3,138,1,29
	.word	742
	.byte	2
	.byte	'float',0,4,4,7
	.byte	'float32',0,3,167,1,29
	.word	775
	.byte	9
	.byte	'void',0,6
	.word	801
	.byte	7
	.byte	'pvoid',0,4,57,28
	.word	807
	.byte	7
	.byte	'Ifx_TickTime',0,4,79,28
	.word	742
	.byte	7
	.byte	'Ifc_Crc_Table',0,1,69,2
	.word	284
	.byte	7
	.byte	'Ifc_Crc',0,1,96,2
	.word	384
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L143:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,36,0,3,8,11,15,62,15,0,0,3,19,1,58,15,59,15,57,15,11,15
	.byte	0,0,4,13,0,3,8,73,19,11,15,56,9,0,0,5,38,0,73,19,0,0,6,15,0,73,19,0,0,7,22,0,3,8,58,15,59,15,57,15,73
	.byte	19,0,0,8,21,0,54,15,0,0,9,59,0,3,8,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L144:
	.word	.L391-.L390
.L390:
	.half	3
	.word	.L393-.L392
.L392:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\libraries\\infineon_libraries\\Service\\CpuGeneric\\SysSe\\Math\\Ifx_Crc.h',0,0,0,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_Crc.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Platform_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Ifx_Types.h',0
	.byte	0,0,0,0
.L393:
.L391:
	.sdecl	'.debug_info',debug,cluster('Ifx_Crc_init')
	.sect	'.debug_info'
.L145:
	.word	447
	.half	3
	.word	.L146
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_Crc.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L148,.L147
	.byte	2
	.word	.L141
	.byte	3
	.byte	'Ifx_Crc_init',0,1,48,9
	.word	.L180
	.byte	1,1,1
	.word	.L128,.L181,.L127
	.byte	4
	.byte	'driver',0,1,48,31
	.word	.L182,.L183
	.byte	4
	.byte	'table',0,1,48,60
	.word	.L184,.L185
	.byte	4
	.byte	'direct',0,1,48,74
	.word	.L186,.L187
	.byte	4
	.byte	'refout',0,1,48,89
	.word	.L186,.L188
	.byte	4
	.byte	'crcinit',0,1,48,104
	.word	.L189,.L190
	.byte	4
	.byte	'crcxor',0,1,48,120
	.word	.L189,.L191
	.byte	5
	.word	.L128,.L181
	.byte	6
	.byte	'i',0,1,50,12
	.word	.L186,.L192
	.byte	6
	.byte	'bit',0,1,51,12
	.word	.L189,.L193
	.byte	6
	.byte	'crc',0,1,51,17
	.word	.L189,.L194
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('Ifx_Crc_init')
	.sect	'.debug_abbrev'
.L146:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('Ifx_Crc_init')
	.sect	'.debug_line'
.L147:
	.word	.L395-.L394
.L394:
	.half	3
	.word	.L397-.L396
.L396:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_Crc.c',0,0,0,0,0
.L397:
	.byte	5,36,7,0,5,2
	.word	.L128
	.byte	3,52,1,5,29,9
	.half	.L398-.L128
	.byte	1,5,5,9
	.half	.L399-.L398
	.byte	1,5,16,7,9
	.half	.L400-.L399
	.byte	3,3,1,5,9,9
	.half	.L401-.L400
	.byte	1,5,34,9
	.half	.L2-.L401
	.byte	3,3,1,5,27,9
	.half	.L402-.L2
	.byte	1,5,5,9
	.half	.L403-.L402
	.byte	1,5,16,7,9
	.half	.L404-.L403
	.byte	3,3,1,5,9,9
	.half	.L405-.L404
	.byte	1,5,20,9
	.half	.L4-.L405
	.byte	3,3,1,9
	.half	.L406-.L4
	.byte	3,1,1,9
	.half	.L407-.L406
	.byte	3,1,1,5,10,9
	.half	.L408-.L407
	.byte	3,4,1,5,35,7,9
	.half	.L409-.L408
	.byte	3,2,1,5,16,9
	.half	.L410-.L409
	.byte	3,3,1,5,37,9
	.half	.L266-.L410
	.byte	1,5,32,9
	.half	.L8-.L266
	.byte	3,2,1,5,25,9
	.half	.L267-.L8
	.byte	1,5,17,9
	.half	.L411-.L267
	.byte	3,1,1,5,13,9
	.half	.L412-.L411
	.byte	3,2,1,5,29,7,9
	.half	.L413-.L412
	.byte	3,2,1,5,21,9
	.half	.L268-.L413
	.byte	1,5,40,9
	.half	.L9-.L268
	.byte	3,121,1,5,30,9
	.half	.L7-.L9
	.byte	1,5,37,9
	.half	.L414-.L7
	.byte	1,5,39,7,9
	.half	.L415-.L414
	.byte	3,11,1,5,31,9
	.half	.L416-.L415
	.byte	1,5,32,9
	.half	.L265-.L416
	.byte	3,1,1,5,48,9
	.half	.L417-.L265
	.byte	3,127,1,5,32,9
	.half	.L6-.L417
	.byte	3,6,1,5,16,9
	.half	.L418-.L6
	.byte	3,3,1,5,37,9
	.half	.L269-.L418
	.byte	1,5,23,9
	.half	.L12-.L269
	.byte	3,2,1,5,13,9
	.half	.L270-.L12
	.byte	3,2,1,5,29,7,9
	.half	.L419-.L270
	.byte	3,2,1,5,21,9
	.half	.L420-.L419
	.byte	1,5,17,9
	.half	.L13-.L420
	.byte	3,3,1,5,13,9
	.half	.L421-.L13
	.byte	3,2,1,5,29,7,9
	.half	.L422-.L421
	.byte	3,2,1,5,21,9
	.half	.L271-.L422
	.byte	1,5,40,9
	.half	.L14-.L271
	.byte	3,115,1,5,30,9
	.half	.L11-.L14
	.byte	1,5,37,9
	.half	.L423-.L11
	.byte	1,5,35,7,9
	.half	.L424-.L423
	.byte	3,17,1,5,12,9
	.half	.L10-.L424
	.byte	3,3,1,5,5,9
	.half	.L425-.L10
	.byte	1,5,1,9
	.half	.L3-.L425
	.byte	3,1,1,7,9
	.half	.L149-.L3
	.byte	0,1,1
.L395:
	.sdecl	'.debug_ranges',debug,cluster('Ifx_Crc_init')
	.sect	'.debug_ranges'
.L148:
	.word	-1,.L128,0,.L149-.L128,0,0
	.sdecl	'.debug_info',debug,cluster('Ifx_Crc_createTable')
	.sect	'.debug_info'
.L150:
	.word	552
	.half	3
	.word	.L151
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_Crc.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L153,.L152
	.byte	2
	.word	.L141
	.byte	3
	.byte	'Ifx_Crc_createTable',0,1,120,9
	.word	.L180
	.byte	1,1,1
	.word	.L130,.L195,.L129
	.byte	4
	.byte	'table',0,1,120,44
	.word	.L196,.L197
	.byte	4
	.byte	'order',0,1,120,58
	.word	.L186,.L198
	.byte	4
	.byte	'polynom',0,1,120,72
	.word	.L189,.L199
	.byte	4
	.byte	'refin',0,1,120,88
	.word	.L186,.L200
	.byte	5
	.word	.L130,.L195
	.byte	6
	.byte	'crcmask',0,1,122,12
	.word	.L189,.L201
	.byte	5
	.word	.L202,.L203
	.byte	6
	.byte	'i',0,1,149,1,16
	.word	.L186,.L204
	.byte	6
	.byte	'j',0,1,149,1,19
	.word	.L186,.L205
	.byte	6
	.byte	'bit',0,1,150,1,16
	.word	.L189,.L206
	.byte	6
	.byte	'crc',0,1,150,1,21
	.word	.L189,.L207
	.byte	5
	.word	.L208,.L209
	.byte	6
	.byte	'crctab',0,1,183,1,24
	.word	.L210,.L211
	.byte	0,5
	.word	.L212,.L213
	.byte	6
	.byte	'crctab',0,1,188,1,25
	.word	.L214,.L215
	.byte	0,5
	.word	.L30,.L29
	.byte	6
	.byte	'crctab',0,1,193,1,25
	.word	.L216,.L217
	.byte	0,0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('Ifx_Crc_createTable')
	.sect	'.debug_abbrev'
.L151:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('Ifx_Crc_createTable')
	.sect	'.debug_line'
.L152:
	.word	.L427-.L426
.L426:
	.half	3
	.word	.L429-.L428
.L428:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_Crc.c',0,0,0,0,0
.L429:
	.byte	5,9,7,0,5,2
	.word	.L130
	.byte	3,247,0,1,5,18,9
	.half	.L273-.L130
	.byte	3,5,1,5,38,9
	.half	.L274-.L273
	.byte	1,5,28,9
	.half	.L275-.L274
	.byte	1,5,44,9
	.half	.L430-.L275
	.byte	1,5,49,9
	.half	.L431-.L430
	.byte	1,5,55,9
	.half	.L432-.L431
	.byte	1,5,9,9
	.half	.L276-.L432
	.byte	3,4,1,5,33,7,9
	.half	.L277-.L276
	.byte	1,5,31,9
	.half	.L278-.L277
	.byte	1,5,16,7,9
	.half	.L16-.L278
	.byte	3,3,1,5,9,9
	.half	.L433-.L16
	.byte	1,5,29,9
	.half	.L17-.L433
	.byte	3,3,1,5,5,9
	.half	.L434-.L17
	.byte	1,5,16,7,9
	.half	.L279-.L434
	.byte	3,3,1,5,9,9
	.half	.L435-.L279
	.byte	1,5,23,9
	.half	.L19-.L435
	.byte	3,3,1,9
	.half	.L280-.L19
	.byte	3,1,1,9
	.half	.L281-.L280
	.byte	3,1,1,5,25,9
	.half	.L436-.L281
	.byte	3,1,1,5,45,9
	.half	.L282-.L436
	.byte	1,5,35,9
	.half	.L283-.L282
	.byte	1,5,23,9
	.half	.L437-.L283
	.byte	1,9
	.half	.L438-.L437
	.byte	3,1,1,5,16,9
	.half	.L202-.L438
	.byte	3,7,1,5,28,9
	.half	.L284-.L202
	.byte	1,5,17,9
	.half	.L22-.L284
	.byte	3,2,1,5,13,9
	.half	.L285-.L22
	.byte	3,2,1,5,44,7,9
	.half	.L439-.L285
	.byte	3,2,1,5,27,9
	.half	.L23-.L439
	.byte	3,3,1,5,17,9
	.half	.L288-.L23
	.byte	1,5,20,9
	.half	.L286-.L288
	.byte	3,2,1,5,30,9
	.half	.L290-.L286
	.byte	1,5,36,9
	.half	.L25-.L290
	.byte	3,2,1,5,29,9
	.half	.L291-.L25
	.byte	1,5,21,9
	.half	.L440-.L291
	.byte	3,1,1,5,17,9
	.half	.L441-.L440
	.byte	3,2,1,5,25,7,9
	.half	.L292-.L441
	.byte	3,2,1,5,33,9
	.half	.L26-.L292
	.byte	3,121,1,5,29,9
	.half	.L24-.L26
	.byte	1,5,30,9
	.half	.L442-.L24
	.byte	1,5,13,7,9
	.half	.L443-.L442
	.byte	3,11,1,5,44,7,9
	.half	.L293-.L443
	.byte	3,2,1,5,21,9
	.half	.L289-.L293
	.byte	1,5,17,9
	.half	.L27-.L289
	.byte	3,3,1,5,26,9
	.half	.L444-.L27
	.byte	3,2,1,5,13,9
	.half	.L295-.L444
	.byte	1,5,51,7,9
	.half	.L208-.L295
	.byte	3,2,1,5,57,9
	.half	.L296-.L208
	.byte	1,5,31,9
	.half	.L297-.L296
	.byte	1,5,23,9
	.half	.L298-.L297
	.byte	3,1,1,5,27,9
	.half	.L299-.L298
	.byte	1,5,31,9
	.half	.L209-.L299
	.byte	3,127,1,9
	.half	.L28-.L209
	.byte	3,3,1,5,18,9
	.half	.L300-.L28
	.byte	1,5,53,7,9
	.half	.L212-.L300
	.byte	3,2,1,5,59,9
	.half	.L301-.L212
	.byte	1,5,32,9
	.half	.L302-.L301
	.byte	1,5,23,9
	.half	.L304-.L302
	.byte	3,1,1,5,27,9
	.half	.L305-.L304
	.byte	1,5,32,9
	.half	.L213-.L305
	.byte	3,127,1,5,53,9
	.half	.L30-.L213
	.byte	3,5,1,5,59,9
	.half	.L306-.L30
	.byte	1,5,32,9
	.half	.L307-.L306
	.byte	1,5,23,9
	.half	.L309-.L307
	.byte	3,1,1,5,27,9
	.half	.L310-.L309
	.byte	1,5,31,9
	.half	.L29-.L310
	.byte	3,86,1,5,25,9
	.half	.L21-.L29
	.byte	1,5,28,9
	.half	.L445-.L21
	.byte	1,5,12,7,9
	.half	.L203-.L445
	.byte	3,46,1,5,5,9
	.half	.L446-.L203
	.byte	1,5,1,9
	.half	.L18-.L446
	.byte	3,1,1,7,9
	.half	.L154-.L18
	.byte	0,1,1
.L427:
	.sdecl	'.debug_ranges',debug,cluster('Ifx_Crc_createTable')
	.sect	'.debug_ranges'
.L153:
	.word	-1,.L130,0,.L154-.L130,0,0
	.sdecl	'.debug_info',debug,cluster('Ifx_Crc_tableFast')
	.sect	'.debug_info'
.L155:
	.word	482
	.half	3
	.word	.L156
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_Crc.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L158,.L157
	.byte	2
	.word	.L141
	.byte	3
	.byte	'Ifx_Crc_tableFast',0,1,224,1,8
	.word	.L189
	.byte	1,1,1
	.word	.L134,.L218,.L133
	.byte	4
	.byte	'driver',0,1,224,1,35
	.word	.L182,.L219
	.byte	4
	.byte	'p',0,1,224,1,50
	.word	.L210,.L220
	.byte	4
	.byte	'len',0,1,224,1,60
	.word	.L189,.L221
	.byte	5
	.word	.L134,.L218
	.byte	6
	.byte	'crc',0,1,229,1,12
	.word	.L189,.L222
	.byte	6
	.byte	'orderMinusHeight',0,1,230,1,12
	.word	.L186,.L223
	.byte	5
	.word	.L224,.L42
	.byte	6
	.byte	'crctab',0,1,239,1,16
	.word	.L210,.L225
	.byte	0,5
	.word	.L226,.L50
	.byte	6
	.byte	'crctab',0,1,130,2,17
	.word	.L214,.L227
	.byte	0,5
	.word	.L46,.L45
	.byte	6
	.byte	'crctab',0,1,149,2,17
	.word	.L216,.L228
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('Ifx_Crc_tableFast')
	.sect	'.debug_abbrev'
.L156:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('Ifx_Crc_tableFast')
	.sect	'.debug_line'
.L157:
	.word	.L448-.L447
.L447:
	.half	3
	.word	.L450-.L449
.L449:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_Crc.c',0,0,0,0,0
.L450:
	.byte	5,8,7,0,5,2
	.word	.L134
	.byte	3,223,1,1,5,37,9
	.half	.L320-.L134
	.byte	3,5,1,9
	.half	.L316-.L320
	.byte	3,1,1,5,44,9
	.half	.L451-.L316
	.byte	1,5,52,9
	.half	.L321-.L451
	.byte	1,5,15,9
	.half	.L452-.L321
	.byte	3,2,1,5,22,9
	.half	.L453-.L452
	.byte	1,5,5,9
	.half	.L454-.L453
	.byte	1,5,42,7,9
	.half	.L455-.L454
	.byte	3,2,1,5,49,9
	.half	.L456-.L455
	.byte	1,5,13,9
	.half	.L315-.L456
	.byte	1,5,15,9
	.half	.L37-.L315
	.byte	3,3,1,5,22,9
	.half	.L457-.L37
	.byte	1,5,33,9
	.half	.L458-.L457
	.byte	1,5,5,9
	.half	.L459-.L458
	.byte	1,5,49,7,9
	.half	.L224-.L459
	.byte	3,2,1,5,57,9
	.half	.L460-.L224
	.byte	1,5,23,9
	.half	.L323-.L460
	.byte	1,5,20,9
	.half	.L324-.L323
	.byte	3,2,1,5,27,9
	.half	.L461-.L324
	.byte	1,5,14,9
	.half	.L462-.L461
	.byte	1,5,25,7,9
	.half	.L463-.L462
	.byte	3,2,1,5,23,9
	.half	.L41-.L463
	.byte	1,5,28,9
	.half	.L464-.L41
	.byte	3,2,1,5,72,9
	.half	.L465-.L464
	.byte	1,5,82,9
	.half	.L466-.L465
	.byte	1,5,80,9
	.half	.L467-.L466
	.byte	1,5,42,9
	.half	.L468-.L467
	.byte	1,5,34,9
	.half	.L469-.L468
	.byte	1,5,84,9
	.half	.L470-.L469
	.byte	1,5,25,9
	.half	.L40-.L470
	.byte	3,126,1,5,23,7,9
	.half	.L471-.L40
	.byte	1,5,25,9
	.half	.L39-.L471
	.byte	3,7,1,5,23,9
	.half	.L44-.L39
	.byte	1,5,28,9
	.half	.L472-.L44
	.byte	3,2,1,5,48,9
	.half	.L473-.L472
	.byte	1,5,58,9
	.half	.L474-.L473
	.byte	1,5,56,9
	.half	.L475-.L474
	.byte	1,5,42,9
	.half	.L476-.L475
	.byte	1,5,34,9
	.half	.L477-.L476
	.byte	1,5,60,9
	.half	.L478-.L477
	.byte	1,5,25,9
	.half	.L43-.L478
	.byte	3,126,1,5,9,7,9
	.half	.L42-.L43
	.byte	3,126,1,5,20,9
	.half	.L38-.L42
	.byte	3,8,1,5,27,9
	.half	.L479-.L38
	.byte	1,5,38,9
	.half	.L480-.L479
	.byte	1,5,10,9
	.half	.L481-.L480
	.byte	1,5,51,7,9
	.half	.L226-.L481
	.byte	3,2,1,5,59,9
	.half	.L482-.L226
	.byte	1,5,24,9
	.half	.L325-.L482
	.byte	1,5,20,9
	.half	.L327-.L325
	.byte	3,2,1,5,27,9
	.half	.L483-.L327
	.byte	1,5,14,9
	.half	.L326-.L483
	.byte	1,5,25,7,9
	.half	.L484-.L326
	.byte	3,2,1,5,23,9
	.half	.L49-.L484
	.byte	1,5,28,9
	.half	.L485-.L49
	.byte	3,2,1,5,72,9
	.half	.L486-.L485
	.byte	1,5,82,9
	.half	.L487-.L486
	.byte	1,5,80,9
	.half	.L488-.L487
	.byte	1,5,42,9
	.half	.L489-.L488
	.byte	1,5,34,9
	.half	.L490-.L489
	.byte	1,5,84,9
	.half	.L491-.L490
	.byte	1,5,25,9
	.half	.L48-.L491
	.byte	3,126,1,5,23,7,9
	.half	.L492-.L48
	.byte	1,5,25,9
	.half	.L47-.L492
	.byte	3,7,1,5,23,9
	.half	.L52-.L47
	.byte	1,5,28,9
	.half	.L493-.L52
	.byte	3,2,1,5,48,9
	.half	.L494-.L493
	.byte	1,5,58,9
	.half	.L495-.L494
	.byte	1,5,56,9
	.half	.L496-.L495
	.byte	1,5,42,9
	.half	.L497-.L496
	.byte	1,5,34,9
	.half	.L498-.L497
	.byte	1,5,60,9
	.half	.L499-.L498
	.byte	1,5,25,9
	.half	.L51-.L499
	.byte	3,126,1,5,9,7,9
	.half	.L50-.L51
	.byte	3,126,1,5,51,9
	.half	.L46-.L50
	.byte	3,10,1,5,59,9
	.half	.L500-.L46
	.byte	1,5,24,9
	.half	.L328-.L500
	.byte	1,5,20,9
	.half	.L330-.L328
	.byte	3,2,1,5,27,9
	.half	.L501-.L330
	.byte	1,5,14,9
	.half	.L329-.L501
	.byte	1,5,25,7,9
	.half	.L502-.L329
	.byte	3,2,1,5,23,9
	.half	.L56-.L502
	.byte	1,5,28,9
	.half	.L503-.L56
	.byte	3,2,1,5,72,9
	.half	.L504-.L503
	.byte	1,5,82,9
	.half	.L505-.L504
	.byte	1,5,80,9
	.half	.L506-.L505
	.byte	1,5,42,9
	.half	.L507-.L506
	.byte	1,5,34,9
	.half	.L508-.L507
	.byte	1,5,84,9
	.half	.L509-.L508
	.byte	1,5,25,9
	.half	.L55-.L509
	.byte	3,126,1,5,23,7,9
	.half	.L510-.L55
	.byte	1,5,25,9
	.half	.L54-.L510
	.byte	3,7,1,5,23,9
	.half	.L59-.L54
	.byte	1,5,28,9
	.half	.L511-.L59
	.byte	3,2,1,5,48,9
	.half	.L512-.L511
	.byte	1,5,58,9
	.half	.L513-.L512
	.byte	1,5,56,9
	.half	.L514-.L513
	.byte	1,5,42,9
	.half	.L515-.L514
	.byte	1,5,34,9
	.half	.L516-.L515
	.byte	1,5,60,9
	.half	.L517-.L516
	.byte	1,5,25,9
	.half	.L58-.L517
	.byte	3,126,1,5,15,7,9
	.half	.L45-.L58
	.byte	3,7,1,5,32,9
	.half	.L518-.L45
	.byte	1,5,39,9
	.half	.L519-.L518
	.byte	1,5,24,9
	.half	.L520-.L519
	.byte	1,5,5,9
	.half	.L521-.L520
	.byte	1,5,42,7,9
	.half	.L522-.L521
	.byte	3,2,1,5,49,9
	.half	.L523-.L522
	.byte	1,5,13,9
	.half	.L322-.L523
	.byte	1,5,18,9
	.half	.L60-.L322
	.byte	3,3,1,5,9,9
	.half	.L524-.L60
	.byte	1,5,18,9
	.half	.L525-.L524
	.byte	3,1,1,5,25,9
	.half	.L318-.L525
	.byte	1,5,9,9
	.half	.L526-.L318
	.byte	1,5,5,9
	.half	.L331-.L526
	.byte	3,2,1,5,1,9
	.half	.L61-.L331
	.byte	3,1,1,7,9
	.half	.L159-.L61
	.byte	0,1,1
.L448:
	.sdecl	'.debug_ranges',debug,cluster('Ifx_Crc_tableFast')
	.sect	'.debug_ranges'
.L158:
	.word	-1,.L134,0,.L159-.L134,0,0
	.sdecl	'.debug_info',debug,cluster('Ifx_Crc_table')
	.sect	'.debug_info'
.L160:
	.word	478
	.half	3
	.word	.L161
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_Crc.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L163,.L162
	.byte	2
	.word	.L141
	.byte	3
	.byte	'Ifx_Crc_table',0,1,179,2,8
	.word	.L189
	.byte	1,1,1
	.word	.L136,.L229,.L135
	.byte	4
	.byte	'driver',0,1,179,2,31
	.word	.L182,.L230
	.byte	4
	.byte	'p',0,1,179,2,46
	.word	.L210,.L231
	.byte	4
	.byte	'len',0,1,179,2,56
	.word	.L189,.L232
	.byte	5
	.word	.L136,.L229
	.byte	6
	.byte	'crc',0,1,184,2,12
	.word	.L189,.L233
	.byte	6
	.byte	'orderMinusHeight',0,1,185,2,12
	.word	.L186,.L234
	.byte	5
	.word	.L235,.L73
	.byte	6
	.byte	'crctab',0,1,194,2,16
	.word	.L210,.L236
	.byte	0,5
	.word	.L237,.L87
	.byte	6
	.byte	'crctab',0,1,228,2,17
	.word	.L214,.L238
	.byte	0,5
	.word	.L239,.L76
	.byte	6
	.byte	'crctab',0,1,134,3,17
	.word	.L216,.L240
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('Ifx_Crc_table')
	.sect	'.debug_abbrev'
.L161:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('Ifx_Crc_table')
	.sect	'.debug_line'
.L162:
	.word	.L528-.L527
.L527:
	.half	3
	.word	.L530-.L529
.L529:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_Crc.c',0,0,0,0,0
.L530:
	.byte	5,8,7,0,5,2
	.word	.L136
	.byte	3,178,2,1,5,37,9
	.half	.L337-.L136
	.byte	3,5,1,9
	.half	.L333-.L337
	.byte	3,1,1,5,44,9
	.half	.L531-.L333
	.byte	1,5,52,9
	.half	.L339-.L531
	.byte	1,5,15,9
	.half	.L532-.L339
	.byte	3,2,1,5,22,9
	.half	.L533-.L532
	.byte	1,5,5,9
	.half	.L534-.L533
	.byte	1,5,42,7,9
	.half	.L535-.L534
	.byte	3,2,1,5,49,9
	.half	.L536-.L535
	.byte	1,5,13,9
	.half	.L332-.L536
	.byte	1,5,15,9
	.half	.L62-.L332
	.byte	3,3,1,5,22,9
	.half	.L537-.L62
	.byte	1,5,33,9
	.half	.L538-.L537
	.byte	1,5,5,9
	.half	.L539-.L538
	.byte	1,5,49,7,9
	.half	.L235-.L539
	.byte	3,2,1,5,57,9
	.half	.L540-.L235
	.byte	1,5,23,9
	.half	.L341-.L540
	.byte	1,5,20,9
	.half	.L342-.L341
	.byte	3,2,1,5,27,9
	.half	.L541-.L342
	.byte	1,5,14,9
	.half	.L542-.L541
	.byte	1,5,25,7,9
	.half	.L543-.L542
	.byte	3,2,1,5,23,9
	.half	.L66-.L543
	.byte	1,5,29,9
	.half	.L544-.L66
	.byte	3,2,1,5,37,9
	.half	.L545-.L544
	.byte	1,5,35,9
	.half	.L546-.L545
	.byte	1,5,80,9
	.half	.L547-.L546
	.byte	1,5,51,9
	.half	.L548-.L547
	.byte	1,5,43,9
	.half	.L549-.L548
	.byte	1,5,39,9
	.half	.L550-.L549
	.byte	1,5,25,9
	.half	.L65-.L550
	.byte	3,126,1,5,23,7,9
	.half	.L551-.L65
	.byte	1,5,25,9
	.half	.L64-.L551
	.byte	3,7,1,5,23,9
	.half	.L69-.L64
	.byte	1,5,29,9
	.half	.L552-.L69
	.byte	3,2,1,5,38,9
	.half	.L553-.L552
	.byte	1,5,43,9
	.half	.L554-.L553
	.byte	1,5,35,9
	.half	.L555-.L554
	.byte	1,5,80,9
	.half	.L556-.L555
	.byte	1,5,75,9
	.half	.L557-.L556
	.byte	1,5,67,9
	.half	.L558-.L557
	.byte	1,5,40,9
	.half	.L559-.L558
	.byte	1,5,25,9
	.half	.L68-.L559
	.byte	3,126,1,5,23,7,9
	.half	.L560-.L68
	.byte	1,5,20,9
	.half	.L67-.L560
	.byte	3,6,1,5,27,9
	.half	.L561-.L67
	.byte	1,5,14,9
	.half	.L562-.L561
	.byte	1,5,20,7,9
	.half	.L563-.L562
	.byte	3,2,1,5,55,9
	.half	.L344-.L563
	.byte	1,5,28,9
	.half	.L72-.L344
	.byte	3,2,1,5,71,9
	.half	.L564-.L72
	.byte	1,5,42,9
	.half	.L565-.L564
	.byte	1,5,34,9
	.half	.L566-.L565
	.byte	1,5,20,9
	.half	.L567-.L566
	.byte	3,126,1,5,35,9
	.half	.L71-.L567
	.byte	1,5,42,9
	.half	.L568-.L71
	.byte	1,5,50,9
	.half	.L569-.L568
	.byte	1,5,55,9
	.half	.L570-.L569
	.byte	1,5,20,7,9
	.half	.L571-.L570
	.byte	1,9
	.half	.L70-.L571
	.byte	3,7,1,5,55,9
	.half	.L345-.L70
	.byte	1,5,28,9
	.half	.L75-.L345
	.byte	3,2,1,5,47,9
	.half	.L572-.L75
	.byte	1,5,42,9
	.half	.L573-.L572
	.byte	1,5,34,9
	.half	.L574-.L573
	.byte	1,5,20,9
	.half	.L575-.L574
	.byte	3,126,1,5,35,9
	.half	.L74-.L575
	.byte	1,5,42,9
	.half	.L576-.L74
	.byte	1,5,50,9
	.half	.L577-.L576
	.byte	1,5,55,9
	.half	.L578-.L577
	.byte	1,5,9,7,9
	.half	.L73-.L578
	.byte	3,126,1,5,20,9
	.half	.L63-.L73
	.byte	3,8,1,5,27,9
	.half	.L579-.L63
	.byte	1,5,38,9
	.half	.L580-.L579
	.byte	1,5,10,9
	.half	.L581-.L580
	.byte	1,5,51,7,9
	.half	.L237-.L581
	.byte	3,2,1,5,59,9
	.half	.L582-.L237
	.byte	1,5,24,9
	.half	.L347-.L582
	.byte	1,5,20,9
	.half	.L349-.L347
	.byte	3,2,1,5,27,9
	.half	.L583-.L349
	.byte	1,5,14,9
	.half	.L348-.L583
	.byte	1,5,25,7,9
	.half	.L584-.L348
	.byte	3,2,1,5,23,9
	.half	.L80-.L584
	.byte	1,5,29,9
	.half	.L585-.L80
	.byte	3,2,1,5,37,9
	.half	.L586-.L585
	.byte	1,5,35,9
	.half	.L587-.L586
	.byte	1,5,80,9
	.half	.L588-.L587
	.byte	1,5,51,9
	.half	.L589-.L588
	.byte	1,5,43,9
	.half	.L590-.L589
	.byte	1,5,39,9
	.half	.L591-.L590
	.byte	1,5,25,9
	.half	.L79-.L591
	.byte	3,126,1,5,23,7,9
	.half	.L592-.L79
	.byte	1,5,25,9
	.half	.L78-.L592
	.byte	3,7,1,5,23,9
	.half	.L83-.L78
	.byte	1,5,29,9
	.half	.L593-.L83
	.byte	3,2,1,5,38,9
	.half	.L594-.L593
	.byte	1,5,43,9
	.half	.L595-.L594
	.byte	1,5,35,9
	.half	.L596-.L595
	.byte	1,5,80,9
	.half	.L597-.L596
	.byte	1,5,75,9
	.half	.L598-.L597
	.byte	1,5,67,9
	.half	.L599-.L598
	.byte	1,5,40,9
	.half	.L600-.L599
	.byte	1,5,25,9
	.half	.L82-.L600
	.byte	3,126,1,5,23,7,9
	.half	.L601-.L82
	.byte	1,5,20,9
	.half	.L81-.L601
	.byte	3,6,1,5,27,9
	.half	.L602-.L81
	.byte	1,5,14,9
	.half	.L603-.L602
	.byte	1,5,20,7,9
	.half	.L604-.L603
	.byte	3,2,1,5,55,9
	.half	.L351-.L604
	.byte	1,5,28,9
	.half	.L86-.L351
	.byte	3,2,1,5,71,9
	.half	.L605-.L86
	.byte	1,5,42,9
	.half	.L606-.L605
	.byte	1,5,34,9
	.half	.L607-.L606
	.byte	1,5,20,9
	.half	.L608-.L607
	.byte	3,126,1,5,35,9
	.half	.L85-.L608
	.byte	1,5,42,9
	.half	.L609-.L85
	.byte	1,5,50,9
	.half	.L610-.L609
	.byte	1,5,55,9
	.half	.L611-.L610
	.byte	1,5,20,7,9
	.half	.L612-.L611
	.byte	1,9
	.half	.L84-.L612
	.byte	3,7,1,5,55,9
	.half	.L352-.L84
	.byte	1,5,28,9
	.half	.L89-.L352
	.byte	3,2,1,5,47,9
	.half	.L613-.L89
	.byte	1,5,42,9
	.half	.L614-.L613
	.byte	1,5,34,9
	.half	.L615-.L614
	.byte	1,5,20,9
	.half	.L616-.L615
	.byte	3,126,1,5,35,9
	.half	.L88-.L616
	.byte	1,5,42,9
	.half	.L617-.L88
	.byte	1,5,50,9
	.half	.L618-.L617
	.byte	1,5,55,9
	.half	.L619-.L618
	.byte	1,5,9,7,9
	.half	.L87-.L619
	.byte	3,126,1,5,20,9
	.half	.L77-.L87
	.byte	3,8,1,5,27,9
	.half	.L620-.L77
	.byte	1,5,38,9
	.half	.L621-.L620
	.byte	1,5,10,9
	.half	.L622-.L621
	.byte	1,5,51,7,9
	.half	.L239-.L622
	.byte	3,2,1,5,59,9
	.half	.L623-.L239
	.byte	1,5,24,9
	.half	.L354-.L623
	.byte	1,5,20,9
	.half	.L356-.L354
	.byte	3,2,1,5,27,9
	.half	.L624-.L356
	.byte	1,5,14,9
	.half	.L355-.L624
	.byte	1,5,25,7,9
	.half	.L625-.L355
	.byte	3,2,1,5,23,9
	.half	.L94-.L625
	.byte	1,5,29,9
	.half	.L626-.L94
	.byte	3,2,1,5,37,9
	.half	.L627-.L626
	.byte	1,5,35,9
	.half	.L628-.L627
	.byte	1,5,80,9
	.half	.L629-.L628
	.byte	1,5,51,9
	.half	.L630-.L629
	.byte	1,5,43,9
	.half	.L631-.L630
	.byte	1,5,39,9
	.half	.L632-.L631
	.byte	1,5,25,9
	.half	.L93-.L632
	.byte	3,126,1,5,23,7,9
	.half	.L633-.L93
	.byte	1,5,25,9
	.half	.L92-.L633
	.byte	3,7,1,5,23,9
	.half	.L97-.L92
	.byte	1,5,29,9
	.half	.L634-.L97
	.byte	3,2,1,5,38,9
	.half	.L635-.L634
	.byte	1,5,43,9
	.half	.L636-.L635
	.byte	1,5,35,9
	.half	.L637-.L636
	.byte	1,5,80,9
	.half	.L638-.L637
	.byte	1,5,75,9
	.half	.L639-.L638
	.byte	1,5,67,9
	.half	.L640-.L639
	.byte	1,5,40,9
	.half	.L641-.L640
	.byte	1,5,25,9
	.half	.L96-.L641
	.byte	3,126,1,5,23,7,9
	.half	.L642-.L96
	.byte	1,5,20,9
	.half	.L95-.L642
	.byte	3,6,1,5,27,9
	.half	.L643-.L95
	.byte	1,5,14,9
	.half	.L644-.L643
	.byte	1,5,20,7,9
	.half	.L645-.L644
	.byte	3,2,1,5,55,9
	.half	.L358-.L645
	.byte	1,5,28,9
	.half	.L100-.L358
	.byte	3,2,1,5,71,9
	.half	.L646-.L100
	.byte	1,5,42,9
	.half	.L647-.L646
	.byte	1,5,34,9
	.half	.L648-.L647
	.byte	1,5,20,9
	.half	.L649-.L648
	.byte	3,126,1,5,35,9
	.half	.L99-.L649
	.byte	1,5,42,9
	.half	.L650-.L99
	.byte	1,5,50,9
	.half	.L651-.L650
	.byte	1,5,55,9
	.half	.L652-.L651
	.byte	1,5,20,7,9
	.half	.L653-.L652
	.byte	1,9
	.half	.L98-.L653
	.byte	3,7,1,5,55,9
	.half	.L359-.L98
	.byte	1,5,28,9
	.half	.L103-.L359
	.byte	3,2,1,5,47,9
	.half	.L654-.L103
	.byte	1,5,42,9
	.half	.L655-.L654
	.byte	1,5,34,9
	.half	.L656-.L655
	.byte	1,5,20,9
	.half	.L657-.L656
	.byte	3,126,1,5,35,9
	.half	.L102-.L657
	.byte	1,5,42,9
	.half	.L658-.L102
	.byte	1,5,50,9
	.half	.L659-.L658
	.byte	1,5,55,9
	.half	.L660-.L659
	.byte	1,5,15,7,9
	.half	.L76-.L660
	.byte	3,7,1,5,32,9
	.half	.L661-.L76
	.byte	1,5,39,9
	.half	.L662-.L661
	.byte	1,5,24,9
	.half	.L663-.L662
	.byte	1,5,5,9
	.half	.L664-.L663
	.byte	1,5,42,7,9
	.half	.L665-.L664
	.byte	3,2,1,5,49,9
	.half	.L666-.L665
	.byte	1,5,13,9
	.half	.L340-.L666
	.byte	1,5,18,9
	.half	.L104-.L340
	.byte	3,3,1,5,9,9
	.half	.L667-.L104
	.byte	1,5,18,9
	.half	.L668-.L667
	.byte	3,1,1,5,25,9
	.half	.L335-.L668
	.byte	1,5,9,9
	.half	.L669-.L335
	.byte	1,5,5,9
	.half	.L360-.L669
	.byte	3,2,1,5,1,9
	.half	.L105-.L360
	.byte	3,1,1,7,9
	.half	.L164-.L105
	.byte	0,1,1
.L528:
	.sdecl	'.debug_ranges',debug,cluster('Ifx_Crc_table')
	.sect	'.debug_ranges'
.L163:
	.word	-1,.L136,0,.L164-.L136,0,0
	.sdecl	'.debug_info',debug,cluster('Ifx_Crc_bitByBit')
	.sect	'.debug_info'
.L165:
	.word	423
	.half	3
	.word	.L166
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_Crc.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L168,.L167
	.byte	2
	.word	.L141
	.byte	3
	.byte	'Ifx_Crc_bitByBit',0,1,179,3,8
	.word	.L189
	.byte	1,1,1
	.word	.L138,.L241,.L137
	.byte	4
	.byte	'driver',0,1,179,3,34
	.word	.L182,.L242
	.byte	4
	.byte	'p',0,1,179,3,49
	.word	.L210,.L243
	.byte	4
	.byte	'len',0,1,179,3,59
	.word	.L189,.L244
	.byte	5
	.word	.L138,.L241
	.byte	6
	.byte	'i',0,1,184,3,12
	.word	.L189,.L245
	.byte	6
	.byte	'j',0,1,184,3,15
	.word	.L189,.L246
	.byte	6
	.byte	'c',0,1,184,3,18
	.word	.L189,.L247
	.byte	6
	.byte	'bit',0,1,184,3,21
	.word	.L189,.L248
	.byte	6
	.byte	'crc',0,1,185,3,12
	.word	.L189,.L249
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('Ifx_Crc_bitByBit')
	.sect	'.debug_abbrev'
.L166:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('Ifx_Crc_bitByBit')
	.sect	'.debug_line'
.L167:
	.word	.L671-.L670
.L670:
	.half	3
	.word	.L673-.L672
.L672:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_Crc.c',0,0,0,0,0
.L673:
	.byte	5,8,7,0,5,2
	.word	.L138
	.byte	3,178,3,1,5,24,9
	.half	.L364-.L138
	.byte	3,6,1,5,12,9
	.half	.L365-.L364
	.byte	3,2,1,5,24,9
	.half	.L367-.L365
	.byte	1,5,21,9
	.half	.L107-.L367
	.byte	3,2,1,5,23,9
	.half	.L369-.L107
	.byte	1,5,19,9
	.half	.L674-.L369
	.byte	3,2,1,5,26,9
	.half	.L675-.L674
	.byte	1,5,9,9
	.half	.L676-.L675
	.byte	1,5,36,7,9
	.half	.L677-.L676
	.byte	3,2,1,5,15,9
	.half	.L370-.L677
	.byte	1,5,16,9
	.half	.L108-.L370
	.byte	3,3,1,5,25,9
	.half	.L371-.L108
	.byte	1,5,33,9
	.half	.L110-.L371
	.byte	3,2,1,5,40,9
	.half	.L678-.L110
	.byte	1,5,25,9
	.half	.L679-.L678
	.byte	1,5,17,9
	.half	.L372-.L679
	.byte	3,1,1,5,19,9
	.half	.L680-.L372
	.byte	3,2,1,5,13,9
	.half	.L681-.L680
	.byte	1,5,21,7,9
	.half	.L682-.L681
	.byte	3,2,1,5,13,9
	.half	.L111-.L682
	.byte	3,3,1,5,30,7,9
	.half	.L683-.L111
	.byte	3,2,1,5,37,9
	.half	.L684-.L683
	.byte	1,5,21,9
	.half	.L685-.L684
	.byte	1,5,29,9
	.half	.L112-.L685
	.byte	3,116,1,5,25,9
	.half	.L109-.L112
	.byte	1,5,27,7,9
	.half	.L686-.L109
	.byte	3,119,1,5,24,9
	.half	.L106-.L686
	.byte	1,5,12,7,9
	.half	.L687-.L106
	.byte	3,26,1,5,41,9
	.half	.L368-.L687
	.byte	1,5,29,9
	.half	.L114-.L368
	.byte	3,2,1,5,36,9
	.half	.L688-.L114
	.byte	1,5,21,9
	.half	.L373-.L688
	.byte	1,5,13,9
	.half	.L689-.L373
	.byte	3,1,1,5,9,9
	.half	.L690-.L689
	.byte	3,2,1,5,26,7,9
	.half	.L691-.L690
	.byte	3,2,1,5,33,9
	.half	.L692-.L691
	.byte	1,5,17,9
	.half	.L374-.L692
	.byte	1,5,44,9
	.half	.L115-.L374
	.byte	3,121,1,5,27,9
	.half	.L113-.L115
	.byte	1,5,34,9
	.half	.L693-.L113
	.byte	1,5,41,9
	.half	.L694-.L693
	.byte	1,5,15,7,9
	.half	.L695-.L694
	.byte	3,11,1,5,5,9
	.half	.L696-.L695
	.byte	1,5,42,7,9
	.half	.L697-.L696
	.byte	3,2,1,5,49,9
	.half	.L698-.L697
	.byte	1,5,13,9
	.half	.L366-.L698
	.byte	1,5,18,9
	.half	.L116-.L366
	.byte	3,3,1,5,9,9
	.half	.L699-.L116
	.byte	1,5,18,9
	.half	.L700-.L699
	.byte	3,1,1,5,25,9
	.half	.L362-.L700
	.byte	1,5,9,9
	.half	.L701-.L362
	.byte	1,5,5,9
	.half	.L376-.L701
	.byte	3,2,1,5,1,9
	.half	.L117-.L376
	.byte	3,1,1,7,9
	.half	.L169-.L117
	.byte	0,1,1
.L671:
	.sdecl	'.debug_ranges',debug,cluster('Ifx_Crc_bitByBit')
	.sect	'.debug_ranges'
.L168:
	.word	-1,.L138,0,.L169-.L138,0,0
	.sdecl	'.debug_info',debug,cluster('Ifx_Crc_bitByBitFast')
	.sect	'.debug_info'
.L170:
	.word	427
	.half	3
	.word	.L171
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_Crc.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L173,.L172
	.byte	2
	.word	.L141
	.byte	3
	.byte	'Ifx_Crc_bitByBitFast',0,1,236,3,8
	.word	.L189
	.byte	1,1,1
	.word	.L140,.L250,.L139
	.byte	4
	.byte	'driver',0,1,236,3,38
	.word	.L182,.L251
	.byte	4
	.byte	'p',0,1,236,3,53
	.word	.L210,.L252
	.byte	4
	.byte	'len',0,1,236,3,63
	.word	.L189,.L253
	.byte	5
	.word	.L140,.L250
	.byte	6
	.byte	'i',0,1,241,3,12
	.word	.L189,.L254
	.byte	6
	.byte	'j',0,1,241,3,15
	.word	.L189,.L255
	.byte	6
	.byte	'c',0,1,241,3,18
	.word	.L189,.L256
	.byte	6
	.byte	'bit',0,1,241,3,21
	.word	.L189,.L257
	.byte	6
	.byte	'crc',0,1,242,3,12
	.word	.L189,.L258
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('Ifx_Crc_bitByBitFast')
	.sect	'.debug_abbrev'
.L171:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('Ifx_Crc_bitByBitFast')
	.sect	'.debug_line'
.L172:
	.word	.L703-.L702
.L702:
	.half	3
	.word	.L705-.L704
.L704:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_Crc.c',0,0,0,0,0
.L705:
	.byte	5,8,7,0,5,2
	.word	.L140
	.byte	3,235,3,1,5,24,9
	.half	.L380-.L140
	.byte	3,6,1,5,12,9
	.half	.L381-.L380
	.byte	3,2,1,5,24,9
	.half	.L383-.L381
	.byte	1,5,21,9
	.half	.L119-.L383
	.byte	3,2,1,5,23,9
	.half	.L384-.L119
	.byte	1,5,19,9
	.half	.L706-.L384
	.byte	3,2,1,5,26,9
	.half	.L707-.L706
	.byte	1,5,9,9
	.half	.L708-.L707
	.byte	1,5,36,7,9
	.half	.L709-.L708
	.byte	3,2,1,5,15,9
	.half	.L385-.L709
	.byte	1,5,16,9
	.half	.L120-.L385
	.byte	3,3,1,5,25,9
	.half	.L386-.L120
	.byte	1,5,33,9
	.half	.L122-.L386
	.byte	3,2,1,5,40,9
	.half	.L710-.L122
	.byte	1,5,25,9
	.half	.L711-.L710
	.byte	1,5,17,9
	.half	.L387-.L711
	.byte	3,1,1,5,19,9
	.half	.L712-.L387
	.byte	3,2,1,5,13,9
	.half	.L713-.L712
	.byte	1,5,30,7,9
	.half	.L714-.L713
	.byte	3,2,1,5,37,9
	.half	.L715-.L714
	.byte	1,5,21,9
	.half	.L716-.L715
	.byte	1,5,13,9
	.half	.L123-.L716
	.byte	3,3,1,5,30,7,9
	.half	.L717-.L123
	.byte	3,2,1,5,37,9
	.half	.L718-.L717
	.byte	1,5,21,9
	.half	.L719-.L718
	.byte	1,5,29,9
	.half	.L124-.L719
	.byte	3,116,1,5,25,9
	.half	.L121-.L124
	.byte	1,5,27,7,9
	.half	.L720-.L121
	.byte	3,119,1,5,24,9
	.half	.L118-.L720
	.byte	1,5,15,7,9
	.half	.L721-.L118
	.byte	3,26,1,5,5,9
	.half	.L722-.L721
	.byte	1,5,42,7,9
	.half	.L723-.L722
	.byte	3,2,1,5,49,9
	.half	.L724-.L723
	.byte	1,5,13,9
	.half	.L382-.L724
	.byte	1,5,18,9
	.half	.L125-.L382
	.byte	3,3,1,5,9,9
	.half	.L725-.L125
	.byte	1,5,18,9
	.half	.L726-.L725
	.byte	3,1,1,5,25,9
	.half	.L378-.L726
	.byte	1,5,9,9
	.half	.L727-.L378
	.byte	1,5,5,9
	.half	.L389-.L727
	.byte	3,2,1,5,1,9
	.half	.L126-.L389
	.byte	3,1,1,7,9
	.half	.L174-.L126
	.byte	0,1,1
.L703:
	.sdecl	'.debug_ranges',debug,cluster('Ifx_Crc_bitByBitFast')
	.sect	'.debug_ranges'
.L173:
	.word	-1,.L140,0,.L174-.L140,0,0
	.sdecl	'.debug_info',debug,cluster('Ifx_Crc_reflect')
	.sect	'.debug_info'
.L175:
	.word	378
	.half	3
	.word	.L176
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_Crc.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L178,.L177
	.byte	2
	.word	.L141
	.byte	3
	.byte	'Ifx_Crc_reflect',0,1,204,1,8
	.word	.L189
	.byte	1,1,1
	.word	.L132,.L259,.L131
	.byte	4
	.byte	'crc',0,1,204,1,31
	.word	.L189,.L260
	.byte	4
	.byte	'bitnum',0,1,204,1,43
	.word	.L186,.L261
	.byte	5
	.word	.L132,.L259
	.byte	6
	.byte	'i',0,1,208,1,12
	.word	.L189,.L262
	.byte	6
	.byte	'j',0,1,208,1,15
	.word	.L189,.L263
	.byte	6
	.byte	'crcout',0,1,208,1,22
	.word	.L189,.L264
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('Ifx_Crc_reflect')
	.sect	'.debug_abbrev'
.L176:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('Ifx_Crc_reflect')
	.sect	'.debug_line'
.L177:
	.word	.L729-.L728
.L728:
	.half	3
	.word	.L731-.L730
.L730:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_Crc.c',0,0,0,0,0
.L731:
	.byte	5,17,7,0,5,2
	.word	.L132
	.byte	3,207,1,1,5,29,9
	.half	.L312-.L132
	.byte	1,5,14,9
	.half	.L313-.L312
	.byte	3,2,1,5,35,9
	.half	.L732-.L313
	.byte	1,5,24,9
	.half	.L311-.L732
	.byte	1,5,42,9
	.half	.L314-.L311
	.byte	1,5,17,9
	.half	.L34-.L314
	.byte	3,2,1,5,9,9
	.half	.L733-.L34
	.byte	1,5,20,7,9
	.half	.L734-.L733
	.byte	3,2,1,5,11,9
	.half	.L35-.L734
	.byte	3,3,1,5,46,9
	.half	.L735-.L35
	.byte	3,121,1,5,42,9
	.half	.L33-.L735
	.byte	1,5,5,7,9
	.half	.L736-.L33
	.byte	3,10,1,5,1,9
	.half	.L36-.L736
	.byte	3,1,1,7,9
	.half	.L179-.L36
	.byte	0,1,1
.L729:
	.sdecl	'.debug_ranges',debug,cluster('Ifx_Crc_reflect')
	.sect	'.debug_ranges'
.L178:
	.word	-1,.L132,0,.L179-.L132,0,0
	.sdecl	'.debug_loc',debug,cluster('Ifx_Crc_bitByBit')
	.sect	'.debug_loc'
.L137:
	.word	-1,.L138,0,.L241-.L138
	.half	2
	.byte	138,0
	.word	0,0
.L248:
	.word	-1,.L138,.L372-.L138,.L109-.L138
	.half	1
	.byte	81
	.word	.L373-.L138,.L374-.L138
	.half	1
	.byte	95
	.word	0,0
.L247:
	.word	-1,.L138,.L369-.L138,.L370-.L138
	.half	1
	.byte	84
	.word	.L370-.L138,.L108-.L138
	.half	1
	.byte	82
	.word	.L108-.L138,.L106-.L138
	.half	1
	.byte	84
	.word	0,0
.L249:
	.word	-1,.L138,.L365-.L138,.L366-.L138
	.half	1
	.byte	89
	.word	.L375-.L138,.L366-.L138
	.half	1
	.byte	84
	.word	.L366-.L138,.L116-.L138
	.half	1
	.byte	82
	.word	.L116-.L138,.L376-.L138
	.half	1
	.byte	89
	.word	.L376-.L138,.L241-.L138
	.half	1
	.byte	82
	.word	0,0
.L242:
	.word	-1,.L138,0,.L107-.L138
	.half	1
	.byte	100
	.word	.L361-.L138,.L362-.L138
	.half	1
	.byte	111
	.word	0,0
.L245:
	.word	-1,.L138,.L367-.L138,.L368-.L138
	.half	1
	.byte	90
	.word	.L368-.L138,.L366-.L138
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L246:
	.word	-1,.L138,.L371-.L138,.L106-.L138
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L244:
	.word	-1,.L138,0,.L107-.L138
	.half	1
	.byte	84
	.word	.L364-.L138,.L241-.L138
	.half	1
	.byte	88
	.word	0,0
.L243:
	.word	-1,.L138,0,.L107-.L138
	.half	1
	.byte	101
	.word	.L363-.L138,.L241-.L138
	.half	1
	.byte	108
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Ifx_Crc_bitByBitFast')
	.sect	'.debug_loc'
.L139:
	.word	-1,.L140,0,.L250-.L140
	.half	2
	.byte	138,0
	.word	0,0
.L257:
	.word	-1,.L140,.L387-.L140,.L121-.L140
	.half	1
	.byte	81
	.word	0,0
.L256:
	.word	-1,.L140,.L384-.L140,.L385-.L140
	.half	1
	.byte	84
	.word	.L385-.L140,.L120-.L140
	.half	1
	.byte	82
	.word	.L120-.L140,.L118-.L140
	.half	1
	.byte	84
	.word	0,0
.L258:
	.word	-1,.L140,.L381-.L140,.L382-.L140
	.half	1
	.byte	89
	.word	.L388-.L140,.L382-.L140
	.half	1
	.byte	84
	.word	.L382-.L140,.L125-.L140
	.half	1
	.byte	82
	.word	.L125-.L140,.L389-.L140
	.half	1
	.byte	89
	.word	.L389-.L140,.L250-.L140
	.half	1
	.byte	82
	.word	0,0
.L251:
	.word	-1,.L140,0,.L119-.L140
	.half	1
	.byte	100
	.word	.L377-.L140,.L378-.L140
	.half	1
	.byte	111
	.word	0,0
.L254:
	.word	-1,.L140,.L383-.L140,.L250-.L140
	.half	1
	.byte	90
	.word	0,0
.L255:
	.word	-1,.L140,.L386-.L140,.L118-.L140
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L253:
	.word	-1,.L140,0,.L119-.L140
	.half	1
	.byte	84
	.word	.L380-.L140,.L250-.L140
	.half	1
	.byte	88
	.word	0,0
.L252:
	.word	-1,.L140,0,.L119-.L140
	.half	1
	.byte	101
	.word	.L379-.L140,.L250-.L140
	.half	1
	.byte	108
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Ifx_Crc_createTable')
	.sect	'.debug_loc'
.L129:
	.word	-1,.L130,0,.L195-.L130
	.half	2
	.byte	138,0
	.word	0,0
.L206:
	.word	-1,.L130,.L291-.L130,.L24-.L130
	.half	1
	.byte	95
	.word	0,0
.L207:
	.word	-1,.L130,.L285-.L130,.L286-.L130
	.half	1
	.byte	82
	.word	.L286-.L130,.L289-.L130
	.half	1
	.byte	84
	.word	.L289-.L130,.L27-.L130
	.half	1
	.byte	82
	.word	.L27-.L130,.L21-.L130
	.half	1
	.byte	84
	.word	0,0
.L201:
	.word	-1,.L130,.L276-.L130,.L195-.L130
	.half	1
	.byte	91
	.word	0,0
.L211:
	.word	-1,.L130,.L297-.L130,.L28-.L130
	.half	1
	.byte	95
	.word	.L298-.L130,.L299-.L130
	.half	1
	.byte	98
	.word	0,0
.L215:
	.word	-1,.L130,.L302-.L130,.L303-.L130
	.half	1
	.byte	95
	.word	.L304-.L130,.L305-.L130
	.half	1
	.byte	98
	.word	0,0
.L217:
	.word	-1,.L130,.L307-.L130,.L308-.L130
	.half	1
	.byte	95
	.word	.L309-.L130,.L310-.L130
	.half	1
	.byte	98
	.word	0,0
.L204:
	.word	-1,.L130,.L284-.L130,.L18-.L130
	.half	1
	.byte	92
	.word	.L287-.L130,.L23-.L130
	.half	1
	.byte	84
	.word	0,0
.L205:
	.word	-1,.L130,.L290-.L130,.L289-.L130
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L198:
	.word	-1,.L130,0,.L22-.L130
	.half	1
	.byte	84
	.word	.L274-.L130,.L275-.L130
	.half	1
	.byte	88
	.word	.L276-.L130,.L277-.L130
	.half	1
	.byte	88
	.word	.L278-.L130,.L16-.L130
	.half	1
	.byte	88
	.word	.L19-.L130,.L280-.L130
	.half	1
	.byte	88
	.word	.L282-.L130,.L283-.L130
	.half	1
	.byte	88
	.word	.L23-.L130,.L288-.L130
	.half	1
	.byte	88
	.word	.L293-.L130,.L294-.L130
	.half	1
	.byte	88
	.word	.L294-.L130,.L289-.L130
	.half	1
	.byte	85
	.word	.L295-.L130,.L208-.L130
	.half	1
	.byte	88
	.word	.L300-.L130,.L212-.L130
	.half	1
	.byte	88
	.word	0,0
.L199:
	.word	-1,.L130,0,.L22-.L130
	.half	1
	.byte	85
	.word	.L17-.L130,.L279-.L130
	.half	1
	.byte	89
	.word	.L280-.L130,.L281-.L130
	.half	1
	.byte	89
	.word	.L292-.L130,.L26-.L130
	.half	1
	.byte	89
	.word	0,0
.L200:
	.word	-1,.L130,0,.L22-.L130
	.half	1
	.byte	86
	.word	.L273-.L130,.L195-.L130
	.half	1
	.byte	90
	.word	0,0
.L197:
	.word	-1,.L130,0,.L22-.L130
	.half	1
	.byte	100
	.word	.L272-.L130,.L195-.L130
	.half	1
	.byte	111
	.word	.L296-.L130,.L297-.L130
	.half	1
	.byte	95
	.word	.L301-.L130,.L302-.L130
	.half	1
	.byte	95
	.word	.L306-.L130,.L307-.L130
	.half	1
	.byte	95
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Ifx_Crc_init')
	.sect	'.debug_loc'
.L127:
	.word	-1,.L128,0,.L181-.L128
	.half	2
	.byte	138,0
	.word	0,0
.L193:
	.word	-1,.L128,.L267-.L128,.L268-.L128
	.half	1
	.byte	95
	.word	.L270-.L128,.L271-.L128
	.half	1
	.byte	95
	.word	0,0
.L194:
	.word	-1,.L128,0,.L10-.L128
	.half	1
	.byte	86
	.word	0,0
.L190:
	.word	-1,.L128,0,.L265-.L128
	.half	1
	.byte	86
	.word	.L6-.L128,.L10-.L128
	.half	1
	.byte	86
	.word	0,0
.L191:
	.word	-1,.L128,0,.L181-.L128
	.half	1
	.byte	87
	.word	0,0
.L187:
	.word	-1,.L128,0,.L181-.L128
	.half	1
	.byte	84
	.word	0,0
.L183:
	.word	-1,.L128,0,.L181-.L128
	.half	1
	.byte	100
	.word	0,0
.L192:
	.word	-1,.L128,.L266-.L128,.L6-.L128
	.half	5
	.byte	144,32,157,32,0
	.word	.L269-.L128,.L10-.L128
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L188:
	.word	-1,.L128,0,.L181-.L128
	.half	1
	.byte	85
	.word	0,0
.L185:
	.word	-1,.L128,0,.L181-.L128
	.half	1
	.byte	101
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Ifx_Crc_reflect')
	.sect	'.debug_loc'
.L131:
	.word	-1,.L132,0,.L259-.L132
	.half	2
	.byte	138,0
	.word	0,0
.L261:
	.word	-1,.L132,0,.L311-.L132
	.half	1
	.byte	85
	.word	0,0
.L260:
	.word	-1,.L132,0,.L259-.L132
	.half	1
	.byte	84
	.word	0,0
.L264:
	.word	-1,.L132,.L313-.L132,.L259-.L132
	.half	1
	.byte	82
	.word	0,0
.L262:
	.word	-1,.L132,.L314-.L132,.L259-.L132
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L263:
	.word	-1,.L132,.L312-.L132,.L259-.L132
	.half	1
	.byte	81
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Ifx_Crc_table')
	.sect	'.debug_loc'
.L135:
	.word	-1,.L136,0,.L229-.L136
	.half	2
	.byte	138,0
	.word	0,0
.L233:
	.word	-1,.L136,.L333-.L136,.L332-.L136
	.half	1
	.byte	84
	.word	.L332-.L136,.L62-.L136
	.half	1
	.byte	82
	.word	.L62-.L136,.L340-.L136
	.half	1
	.byte	84
	.word	.L340-.L136,.L104-.L136
	.half	1
	.byte	82
	.word	.L104-.L136,.L360-.L136
	.half	1
	.byte	84
	.word	.L360-.L136,.L229-.L136
	.half	1
	.byte	82
	.word	0,0
.L236:
	.word	-1,.L136,.L341-.L136,.L66-.L136
	.half	5
	.byte	144,32,157,32,0
	.word	.L342-.L136,.L63-.L136
	.half	1
	.byte	100
	.word	.L64-.L136,.L69-.L136
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L238:
	.word	-1,.L136,.L347-.L136,.L348-.L136
	.half	1
	.byte	95
	.word	.L349-.L136,.L77-.L136
	.half	1
	.byte	100
	.word	0,0
.L240:
	.word	-1,.L136,.L354-.L136,.L355-.L136
	.half	1
	.byte	95
	.word	.L356-.L136,.L76-.L136
	.half	1
	.byte	100
	.word	0,0
.L230:
	.word	-1,.L136,0,.L332-.L136
	.half	1
	.byte	100
	.word	.L334-.L136,.L335-.L136
	.half	1
	.byte	111
	.word	0,0
.L232:
	.word	-1,.L136,0,.L333-.L136
	.half	1
	.byte	84
	.word	.L337-.L136,.L338-.L136
	.half	1
	.byte	89
	.word	.L338-.L136,.L64-.L136
	.half	1
	.byte	95
	.word	.L64-.L136,.L343-.L136
	.half	1
	.byte	89
	.word	.L343-.L136,.L344-.L136
	.half	1
	.byte	95
	.word	.L344-.L136,.L70-.L136
	.half	5
	.byte	144,32,157,32,0
	.word	.L70-.L136,.L345-.L136
	.half	1
	.byte	95
	.word	.L345-.L136,.L73-.L136
	.half	5
	.byte	144,32,157,32,0
	.word	.L63-.L136,.L346-.L136
	.half	1
	.byte	89
	.word	.L346-.L136,.L78-.L136
	.half	1
	.byte	95
	.word	.L78-.L136,.L350-.L136
	.half	1
	.byte	89
	.word	.L350-.L136,.L351-.L136
	.half	1
	.byte	95
	.word	.L351-.L136,.L84-.L136
	.half	5
	.byte	144,32,157,32,0
	.word	.L84-.L136,.L352-.L136
	.half	1
	.byte	95
	.word	.L352-.L136,.L87-.L136
	.half	5
	.byte	144,32,157,32,0
	.word	.L77-.L136,.L353-.L136
	.half	1
	.byte	89
	.word	.L353-.L136,.L92-.L136
	.half	1
	.byte	95
	.word	.L92-.L136,.L357-.L136
	.half	1
	.byte	89
	.word	.L357-.L136,.L358-.L136
	.half	1
	.byte	95
	.word	.L358-.L136,.L98-.L136
	.half	5
	.byte	144,32,157,32,0
	.word	.L98-.L136,.L359-.L136
	.half	1
	.byte	95
	.word	.L359-.L136,.L76-.L136
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L234:
	.word	-1,.L136,.L339-.L136,.L229-.L136
	.half	1
	.byte	88
	.word	0,0
.L231:
	.word	-1,.L136,0,.L332-.L136
	.half	1
	.byte	101
	.word	.L336-.L136,.L229-.L136
	.half	1
	.byte	108
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Ifx_Crc_tableFast')
	.sect	'.debug_loc'
.L133:
	.word	-1,.L134,0,.L218-.L134
	.half	2
	.byte	138,0
	.word	0,0
.L222:
	.word	-1,.L134,.L316-.L134,.L315-.L134
	.half	1
	.byte	84
	.word	.L315-.L134,.L37-.L134
	.half	1
	.byte	82
	.word	.L37-.L134,.L322-.L134
	.half	1
	.byte	84
	.word	.L322-.L134,.L60-.L134
	.half	1
	.byte	82
	.word	.L60-.L134,.L331-.L134
	.half	1
	.byte	84
	.word	.L331-.L134,.L218-.L134
	.half	1
	.byte	82
	.word	0,0
.L225:
	.word	-1,.L134,.L323-.L134,.L41-.L134
	.half	5
	.byte	144,32,157,32,0
	.word	.L324-.L134,.L38-.L134
	.half	1
	.byte	98
	.word	.L39-.L134,.L44-.L134
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L227:
	.word	-1,.L134,.L325-.L134,.L326-.L134
	.half	1
	.byte	95
	.word	.L327-.L134,.L46-.L134
	.half	1
	.byte	98
	.word	0,0
.L228:
	.word	-1,.L134,.L328-.L134,.L329-.L134
	.half	1
	.byte	95
	.word	.L330-.L134,.L45-.L134
	.half	1
	.byte	98
	.word	0,0
.L219:
	.word	-1,.L134,0,.L315-.L134
	.half	1
	.byte	100
	.word	.L317-.L134,.L318-.L134
	.half	1
	.byte	111
	.word	0,0
.L221:
	.word	-1,.L134,0,.L316-.L134
	.half	1
	.byte	84
	.word	.L320-.L134,.L218-.L134
	.half	1
	.byte	89
	.word	0,0
.L223:
	.word	-1,.L134,.L321-.L134,.L218-.L134
	.half	1
	.byte	88
	.word	0,0
.L220:
	.word	-1,.L134,0,.L315-.L134
	.half	1
	.byte	101
	.word	.L319-.L134,.L218-.L134
	.half	1
	.byte	108
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L737:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('Ifx_Crc_init')
	.sect	'.debug_frame'
	.word	20
	.word	.L737,.L128,.L181-.L128
	.byte	8,18,8,19,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('Ifx_Crc_createTable')
	.sect	'.debug_frame'
	.word	12
	.word	.L737,.L130,.L195-.L130
	.sdecl	'.debug_frame',debug,cluster('Ifx_Crc_reflect')
	.sect	'.debug_frame'
	.word	24
	.word	.L737,.L132,.L259-.L132
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('Ifx_Crc_tableFast')
	.sect	'.debug_frame'
	.word	12
	.word	.L737,.L134,.L218-.L134
	.sdecl	'.debug_frame',debug,cluster('Ifx_Crc_table')
	.sect	'.debug_frame'
	.word	12
	.word	.L737,.L136,.L229-.L136
	.sdecl	'.debug_frame',debug,cluster('Ifx_Crc_bitByBit')
	.sect	'.debug_frame'
	.word	12
	.word	.L737,.L138,.L241-.L138
	.sdecl	'.debug_frame',debug,cluster('Ifx_Crc_bitByBitFast')
	.sect	'.debug_frame'
	.word	12
	.word	.L737,.L140,.L250-.L140
	; Module end
