	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc44468a --dep-file=zf_device_gnss.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2\\\\xf_asr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/zf_device/zf_device_gnss.src ../libraries/zf_device/zf_device_gnss.c"
	.compiler_name		"ctc"
	;source	'../libraries/zf_device/zf_device_gnss.c'

	
$TC16X
	.sdecl	'.rodata.zf_device_gnss..17.cnt',data,rom
	.sect	'.rodata.zf_device_gnss..17.cnt'
	.align	4
.17.cnt:	.type	object
	.size	.17.cnt,8
	.word	1073741824,1096307878
	.sdecl	'.rodata.zf_device_gnss..18.cnt',data,rom
	.sect	'.rodata.zf_device_gnss..18.cnt'
	.align	4
.18.cnt:	.type	object
	.size	.18.cnt,8
	.word	1413754152,1074340347
	
	.sdecl	'.text.zf_device_gnss.get_parameter_index',code,cluster('get_parameter_index')
	.sect	'.text.zf_device_gnss.get_parameter_index'
	.align	2
	
; Function get_parameter_index
.L84:
get_parameter_index:	.type	func
	mov	d8,d4
.L331:
	mov.aa	a15,a4
.L332:
	mov	d9,#0
.L333:
	mov	d4,#10
.L329:
	mov.aa	a4,a15
	call	strchr
.L330:
	mov	d0,#0
.L335:
	mov	d2,#0
.L337:
	mov.a	a4,#0
.L826:
	jeq.a	a4,a2,.L2
.L827:
	mov.d	d0,a2
.L336:
	mov.d	d1,a15
.L339:
	sub	d0,d1
.L338:
	add	d0,#1
.L828:
	extr.u	d0,d0,#0,#8
.L2:
	mov	d15,#0
.L340:
	j	.L3
.L4:
	addsc.a	a2,a15,d15,#0
	ld.b	d1,[a2]0
.L829:
	mov	d3,#44
.L830:
	jne	d1,d3,.L5
.L831:
	add	d9,#1
.L334:
	extr.u	d9,d9,#0,#8
.L5:
	jne	d9,d8,.L6
.L832:
	add	d15,#1
.L341:
	extr.u	d2,d15,#0,#8
.L833:
	j	.L7
.L6:
	add	d15,#1
.L342:
	extr.u	d15,d15,#0,#8
.L3:
	jlt.u	d15,d0,.L4
.L7:
	j	.L8
.L8:
	ret
.L266:
	
__get_parameter_index_function_end:
	.size	get_parameter_index,__get_parameter_index_function_end-get_parameter_index
.L142:
	; End of function
	
	.sdecl	'.text.zf_device_gnss.get_int_number',code,cluster('get_int_number')
	.sect	'.text.zf_device_gnss.get_int_number'
	.align	2
	
; Function get_int_number
.L86:
get_int_number:	.type	func
	sub.a	a10,#16
.L343:
	mov.aa	a15,a4
.L345:
	mov	d4,#1
.L838:
	mov.aa	a4,a15
	call	get_parameter_index
.L344:
	add	d2,#-1
.L347:
	extr.u	d15,d2,#0,#8
.L348:
	lea	a4,[a10]0
.L839:
	mov.aa	a5,a15
.L350:
	mov	d4,d15
.L352:
	call	strncpy
.L351:
	addsc.a	a15,a10,d15,#0
.L346:
	mov	d15,#0
.L349:
	st.b	[a15],d15
.L840:
	lea	a4,[a10]0
	call	func_str_to_int
.L353:
	j	.L9
.L9:
	ret
.L276:
	
__get_int_number_function_end:
	.size	get_int_number,__get_int_number_function_end-get_int_number
.L147:
	; End of function
	
	.sdecl	'.text.zf_device_gnss.get_float_number',code,cluster('get_float_number')
	.sect	'.text.zf_device_gnss.get_float_number'
	.align	2
	
; Function get_float_number
.L88:
get_float_number:	.type	func
	sub.a	a10,#16
.L354:
	mov.aa	a15,a4
.L356:
	mov	d4,#1
.L845:
	mov.aa	a4,a15
	call	get_parameter_index
.L355:
	add	d2,#-1
.L358:
	extr.u	d15,d2,#0,#8
.L359:
	lea	a4,[a10]0
.L846:
	mov.aa	a5,a15
.L361:
	mov	d4,d15
.L363:
	call	strncpy
.L362:
	addsc.a	a15,a10,d15,#0
.L357:
	mov	d15,#0
.L360:
	st.b	[a15],d15
.L847:
	lea	a4,[a10]0
	call	func_str_to_double
.L848:
	mov	e4,d3,d2
	call	__d_dtof
.L849:
	j	.L10
.L10:
	ret
.L283:
	
__get_float_number_function_end:
	.size	get_float_number,__get_float_number_function_end-get_float_number
.L152:
	; End of function
	
	.sdecl	'.text.zf_device_gnss.get_double_number',code,cluster('get_double_number')
	.sect	'.text.zf_device_gnss.get_double_number'
	.align	2
	
; Function get_double_number
.L90:
get_double_number:	.type	func
	sub.a	a10,#16
.L364:
	mov.aa	a15,a4
.L366:
	mov	d4,#1
.L854:
	mov.aa	a4,a15
	call	get_parameter_index
.L365:
	add	d2,#-1
.L368:
	extr.u	d15,d2,#0,#8
.L369:
	lea	a4,[a10]0
.L855:
	mov.aa	a5,a15
.L371:
	mov	d4,d15
.L373:
	call	strncpy
.L372:
	addsc.a	a15,a10,d15,#0
.L367:
	mov	d15,#0
.L370:
	st.b	[a15],d15
.L856:
	lea	a4,[a10]0
	call	func_str_to_double
.L857:
	j	.L11
.L11:
	ret
.L289:
	
__get_double_number_function_end:
	.size	get_double_number,__get_double_number_function_end-get_double_number
.L157:
	; End of function
	
	.sdecl	'.text.zf_device_gnss.utc_to_btc',code,cluster('utc_to_btc')
	.sect	'.text.zf_device_gnss.utc_to_btc'
	.align	2
	
; Function utc_to_btc
.L92:
utc_to_btc:	.type	func
	ld.bu	d15,[a4]4
.L862:
	add	d15,d15,#8
.L863:
	st.b	[a4]4,d15
.L864:
	ld.bu	d15,[a4]4
.L865:
	mov	d0,#23
.L866:
	jge.u	d0,d15,.L12
.L867:
	ld.bu	d15,[a4]4
.L868:
	add	d15,d15,#-24
	st.b	[a4]4,d15
.L869:
	ld.bu	d15,[a4]3
.L870:
	add	d15,#1
	st.b	[a4]3,d15
.L871:
	ld.bu	d15,[a4]2
.L872:
	jne	d15,#2,.L13
.L873:
	mov	d2,#28
.L374:
	ld.hu	d0,[a4]0
.L874:
	mov	d1,#4
.L875:
	div	e0,d0,d1
.L876:
	jne	d1,#0,.L14
.L877:
	ld.hu	d0,[a4]0
.L878:
	mov	d15,#100
.L879:
	div	e0,d0,d15
.L880:
	jne	d1,#0,.L15
.L14:
	ld.hu	d0,[a4]0
.L881:
	mov	d1,#400
.L882:
	div	e0,d0,d1
.L883:
	jne	d1,#0,.L16
.L15:
	add	d2,#1
.L16:
	j	.L17
.L13:
	mov	d2,#31
.L375:
	ld.bu	d15,[a4]2
.L884:
	jeq	d15,#4,.L18
.L885:
	ld.bu	d15,[a4]2
.L886:
	jeq	d15,#6,.L19
.L887:
	ld.bu	d0,[a4]2
.L888:
	mov	d15,#9
.L889:
	jeq	d15,d0,.L20
.L890:
	ld.bu	d0,[a4]2
.L891:
	mov	d15,#11
.L892:
	jne	d15,d0,.L21
.L20:
.L19:
.L18:
	mov	d2,#30
.L21:
.L17:
	ld.bu	d15,[a4]3
.L893:
	jge.u	d2,d15,.L22
.L894:
	mov	d15,#1
.L895:
	st.b	[a4]3,d15
.L896:
	ld.bu	d15,[a4]2
.L897:
	add	d15,#1
	st.b	[a4]2,d15
.L898:
	ld.bu	d15,[a4]2
.L899:
	jlt.u	d15,#13,.L23
.L900:
	ld.bu	d15,[a4]2
.L901:
	add	d15,d15,#-12
	st.b	[a4]2,d15
.L902:
	ld.hu	d15,[a4]0
.L903:
	add	d15,#1
	st.h	[a4],d15
.L23:
.L22:
.L12:
	ret
.L294:
	
__utc_to_btc_function_end:
	.size	utc_to_btc,__utc_to_btc_function_end-utc_to_btc
.L162:
	; End of function
	
	.sdecl	'.text.zf_device_gnss.gps_gnrmc_parse',code,cluster('gps_gnrmc_parse')
	.sect	'.text.zf_device_gnss.gps_gnrmc_parse'
	.align	2
	
; Function gps_gnrmc_parse
.L94:
gps_gnrmc_parse:	.type	func
	mov.aa	a12,a4
.L377:
	mov.aa	a15,a5
.L378:
	mov	d8,#0
.L379:
	mov	d4,#2
.L908:
	mov.aa	a4,a12
	call	get_parameter_index
.L376:
	addsc.a	a2,a12,d2,#0
	ld.b	d15,[a2]0
	extr.u	d0,d15,#0,#8
.L380:
	mov	d15,#65
	jeq	d15,d0,.L24
.L909:
	mov	d15,#68
.L910:
	jne	d15,d0,.L25
.L24:
	mov	d8,#1
.L911:
	mov	d15,#1
.L912:
	st.b	[a15]8,d15
.L913:
	mov	d4,#4
.L914:
	mov.aa	a4,a12
.L382:
	call	get_parameter_index
.L381:
	addsc.a	a2,a12,d2,#0
	ld.b	d15,[a2]0
.L915:
	st.b	[a15]40,d15
.L916:
	mov	d4,#6
.L917:
	mov.aa	a4,a12
.L383:
	call	get_parameter_index
.L384:
	addsc.a	a2,a12,d2,#0
	ld.b	d15,[a2]0
.L918:
	st.b	[a15]41,d15
.L919:
	mov	d4,#3
.L920:
	mov.aa	a4,a12
.L385:
	call	get_parameter_index
.L386:
	addsc.a	a4,a12,d2,#0
	call	get_double_number
	mov	e10,d3,d2
.L921:
	mov	d4,#5
.L922:
	mov.aa	a4,a12
.L387:
	call	get_parameter_index
.L388:
	mov	d15,d2
.L923:
	addsc.a	a4,a12,d15,#0
	call	get_double_number
	mov	e12,d3,d2
.L389:
	mov	e4,d11,d10
.L390:
	call	__d_dtoi
.L924:
	mov	d15,#100
.L925:
	div	e0,d2,d15
.L926:
	st.h	[a15]10,d0
.L927:
	ld.hu	d15,[a15]10
.L928:
	mul	d4,d15,#100
	call	__d_itod
	mov	e6,d3,d2
.L391:
	mov	e4,d11,d10
.L392:
	call	__d_sub
	mov	e10,d3,d2
.L393:
	mov	e4,d11,d10
.L394:
	call	__d_dtoi
	mov	d15,d2
.L929:
	st.h	[a15]12,d15
.L930:
	ld.hu	d4,[a15]12
	call	__d_uitod
	mov	e6,d3,d2
.L395:
	mov	e4,d11,d10
.L396:
	call	__d_sub
	mov	e4,d3,d2
.L931:
	mov	d6,#0
	mov.u	d7,#28672
	addih	d7,d7,#16567
.L932:
	call	__d_mul
	mov	e4,d3,d2
.L933:
	call	__d_dtoi
	mov	d15,d2
.L934:
	st.h	[a15]14,d15
.L397:
	mov	e4,d13,d12
.L398:
	call	__d_dtoi
.L935:
	mov	d15,#100
.L936:
	div	e0,d2,d15
.L937:
	st.h	[a15]16,d0
.L938:
	ld.hu	d15,[a15]16
.L939:
	mul	d4,d15,#100
	call	__d_itod
	mov	e6,d3,d2
.L399:
	mov	e4,d13,d12
.L400:
	call	__d_sub
	mov	e12,d3,d2
.L401:
	mov	e4,d13,d12
.L402:
	call	__d_dtoi
	mov	d15,d2
.L940:
	st.h	[a15]18,d15
.L941:
	ld.hu	d4,[a15]18
	call	__d_uitod
	mov	e6,d3,d2
.L403:
	mov	e4,d13,d12
.L404:
	call	__d_sub
	mov	e4,d3,d2
.L942:
	mov	d6,#0
	mov.u	d7,#28672
	addih	d7,d7,#16567
.L943:
	call	__d_mul
	mov	e4,d3,d2
.L944:
	call	__d_dtoi
	mov	d15,d2
.L945:
	st.h	[a15]20,d15
.L946:
	ld.hu	d4,[a15]10
	call	__d_uitod
	mov	e14,d3,d2
.L947:
	mov	d6,#0
	mov	d7,#0
	addih	d7,d7,#16462
.L405:
	mov	e4,d11,d10
.L406:
	call	__d_div
	mov	e6,d3,d2
.L948:
	mov	e4,d15,d14
	call	__d_add
.L949:
	st.d	[a15]24,e2
.L950:
	ld.hu	d4,[a15]16
	call	__d_uitod
	mov	e10,d3,d2
.L951:
	mov	d6,#0
	mov	d7,#0
	addih	d7,d7,#16462
.L407:
	mov	e4,d13,d12
.L408:
	call	__d_div
	mov	e6,d3,d2
.L952:
	mov	e4,d11,d10
	call	__d_add
.L953:
	st.d	[a15]32,e2
.L954:
	mov	d4,#7
.L955:
	mov.aa	a4,a12
.L409:
	call	get_parameter_index
.L410:
	addsc.a	a4,a12,d2,#0
	call	get_float_number
.L411:
	mov.u	d15,#52429
	addih	d15,d15,#16364
.L956:
	mul.f	d15,d2,d15
.L957:
	st.w	[a15]42,d15
.L958:
	mov	d4,#8
.L959:
	mov.aa	a4,a12
.L413:
	call	get_parameter_index
.L412:
	addsc.a	a4,a12,d2,#0
	call	get_float_number
.L960:
	st.w	[a15]46,d2
.L961:
	j	.L26
.L25:
	mov	d15,#0
.L962:
	st.b	[a15]8,d15
.L26:
	ld.b	d15,[a12]7
.L963:
	add	d0,d15,#-48
.L964:
	mov	d15,#10
.L965:
	ld.b	d1,[a12]8
.L966:
	add	d1,d1,#-48
.L967:
	madd	d15,d1,d0,d15
.L968:
	st.b	[a15]4,d15
.L969:
	ld.b	d15,[a12]9
.L970:
	add	d0,d15,#-48
.L971:
	mov	d15,#10
.L972:
	ld.b	d1,[a12]10
.L973:
	add	d1,d1,#-48
.L974:
	madd	d15,d1,d0,d15
.L975:
	st.b	[a15]5,d15
.L976:
	ld.b	d15,[a12]11
.L977:
	add	d0,d15,#-48
.L978:
	mov	d15,#10
.L979:
	ld.b	d1,[a12]12
.L980:
	add	d1,d1,#-48
.L981:
	madd	d15,d1,d0,d15
.L982:
	st.b	[a15]6,d15
.L983:
	mov	d4,#9
.L984:
	mov.aa	a4,a12
.L414:
	call	get_parameter_index
.L415:
	addsc.a	a2,a12,d2,#0
	ld.b	d15,[a2]0
.L985:
	add	d0,d15,#-48
.L986:
	mov	d15,#10
.L987:
	addsc.a	a2,a12,d2,#0
	ld.b	d1,[a2]1
.L988:
	add	d1,d1,#-48
.L989:
	madd	d15,d1,d0,d15
.L990:
	st.b	[a15]3,d15
.L991:
	addsc.a	a2,a12,d2,#0
	ld.b	d15,[a2]2
.L992:
	add	d0,d15,#-48
.L993:
	mov	d15,#10
.L994:
	addsc.a	a2,a12,d2,#0
	ld.b	d1,[a2]3
.L995:
	add	d1,d1,#-48
.L996:
	madd	d15,d1,d0,d15
.L997:
	st.b	[a15]2,d15
.L998:
	addsc.a	a2,a12,d2,#0
	ld.b	d15,[a2]4
.L999:
	add	d0,d15,#-48
.L1000:
	mov	d15,#10
.L1001:
	addsc.a	a2,a12,d2,#0
	ld.b	d1,[a2]5
.L1002:
	add	d1,d1,#-48
.L1003:
	madd	d15,d1,d0,d15
.L1004:
	addi	d15,d15,#2000
.L1005:
	st.h	[a15],d15
.L1006:
	mov.aa	a4,a15
.L417:
	call	utc_to_btc
.L416:
	mov	d2,d8
.L418:
	j	.L27
.L27:
	ret
.L298:
	
__gps_gnrmc_parse_function_end:
	.size	gps_gnrmc_parse,__gps_gnrmc_parse_function_end-gps_gnrmc_parse
.L167:
	; End of function
	
	.sdecl	'.text.zf_device_gnss.gps_gngga_parse',code,cluster('gps_gngga_parse')
	.sect	'.text.zf_device_gnss.gps_gngga_parse'
	.align	2
	
; Function gps_gngga_parse
.L96:
gps_gngga_parse:	.type	func
	mov.aa	a15,a4
.L420:
	mov.aa	a13,a5
.L422:
	mov	d8,#0
.L423:
	mov	d4,#2
.L1011:
	mov.aa	a4,a15
	call	get_parameter_index
.L419:
	addsc.a	a2,a15,d2,#0
	ld.b	d15,[a2]0
	extr.u	d0,d15,#0,#8
.L424:
	mov	d15,#44
.L1012:
	jeq	d15,d0,.L28
.L1013:
	mov	d4,#7
.L1014:
	mov.aa	a4,a15
.L426:
	call	get_parameter_index
.L425:
	addsc.a	a4,a15,d2,#0
	call	get_int_number
.L1015:
	st.b	[a13]56,d2
.L1016:
	mov	d4,#9
.L1017:
	mov.aa	a4,a15
.L427:
	call	get_parameter_index
.L428:
	addsc.a	a12,a15,d2,#0
.L1018:
	mov	d4,#11
.L1019:
	mov.aa	a4,a15
.L429:
	call	get_parameter_index
.L430:
	addsc.a	a15,a15,d2,#0
.L421:
	mov.aa	a4,a12
	call	get_float_number
.L1020:
	mov	d15,d2
.L1021:
	mov.aa	a4,a15
	call	get_float_number
.L1022:
	add.f	d15,d15,d2
.L1023:
	st.w	[a13]58,d15
.L1024:
	mov	d8,#1
.L28:
	mov	d2,d8
.L431:
	j	.L29
.L29:
	ret
.L313:
	
__gps_gngga_parse_function_end:
	.size	gps_gngga_parse,__gps_gngga_parse_function_end-gps_gngga_parse
.L172:
	; End of function
	
	.sdecl	'.text.zf_device_gnss.gps_gnths_parse',code,cluster('gps_gnths_parse')
	.sect	'.text.zf_device_gnss.gps_gnths_parse'
	.align	2
	
; Function gps_gnths_parse
.L98:
gps_gnths_parse:	.type	func
	mov.aa	a12,a4
.L433:
	mov.aa	a15,a5
.L434:
	mov	d8,#0
.L435:
	mov	d4,#2
.L1029:
	mov.aa	a4,a12
	call	get_parameter_index
.L432:
	addsc.a	a2,a12,d2,#0
	ld.b	d15,[a2]0
	extr.u	d0,d15,#0,#8
.L436:
	mov	d15,#65
.L1030:
	jne	d15,d0,.L30
.L1031:
	mov	d15,#1
.L1032:
	st.b	[a15]50,d15
.L1033:
	mov	d4,#1
.L1034:
	mov.aa	a4,a12
.L438:
	call	get_parameter_index
.L437:
	addsc.a	a4,a12,d2,#0
	call	get_float_number
.L1035:
	st.w	[a15]52,d2
.L1036:
	mov	d8,#1
.L1037:
	j	.L31
.L30:
	mov	d15,#0
.L1038:
	st.b	[a15]50,d15
.L31:
	mov	d2,d8
.L439:
	j	.L32
.L32:
	ret
.L319:
	
__gps_gnths_parse_function_end:
	.size	gps_gnths_parse,__gps_gnths_parse_function_end-gps_gnths_parse
.L177:
	; End of function
	
	.sdecl	'.text.zf_device_gnss.get_two_points_distance',code,cluster('get_two_points_distance')
	.sect	'.text.zf_device_gnss.get_two_points_distance'
	.align	2
	
	.global	get_two_points_distance
; Function get_two_points_distance
.L100:
get_two_points_distance:	.type	func
	sub.a	a10,#16
.L440:
	mov	e8,d7,d6
	ld.d	e10,[a10]16
.L443:
	ld.d	e12,[a10]24
.L445:
	movh.a	a15,#@his(.17.cnt)
	lea	a15,[a15]@los(.17.cnt)
	ld.d	e0,[a15]0
.L447:
	st.d	[a10]8,e0
.L448:
	movh.a	a15,#@his(.18.cnt)
	lea	a15,[a15]@los(.18.cnt)
	ld.d	e6,[a15]0
.L442:
	call	__d_mul
.L441:
	mov	d6,#0
	mov.u	d7,#32768
	addih	d7,d7,#16486
	mov	e4,d3,d2
	call	__d_div
.L527:
	mov	e14,d3,d2
.L528:
	movh.a	a15,#@his(.18.cnt)
	lea	a15,[a15]@los(.18.cnt)
	ld.d	e6,[a15]0
	mov	e4,d11,d10
	call	__d_mul
	mov	d6,#0
	mov.u	d7,#32768
	addih	d7,d7,#16486
	mov	e4,d3,d2
	call	__d_div
.L449:
	st.d	[a10]0,e2
.L450:
	movh.a	a15,#@his(.18.cnt)
	lea	a15,[a15]@los(.18.cnt)
	ld.d	e6,[a15]0
.L451:
	mov	e4,d9,d8
.L452:
	call	__d_mul
	mov	d6,#0
	mov.u	d7,#32768
	addih	d7,d7,#16486
	mov	e4,d3,d2
	call	__d_div
.L529:
	mov	e10,d3,d2
.L444:
	movh.a	a15,#@his(.18.cnt)
	lea	a15,[a15]@los(.18.cnt)
	ld.d	e6,[a15]0
	mov	e4,d13,d12
	call	__d_mul
	mov	d6,#0
	mov.u	d7,#32768
	addih	d7,d7,#16486
	mov	e4,d3,d2
	call	__d_div
.L530:
	mov	e8,d3,d2
.L453:
	mov	e4,d15,d14
.L454:
	ld.d	e6,[a10]0
.L455:
	call	__d_sub
.L456:
	mov	e12,d3,d2
.L446:
	mov	e4,d11,d10
.L457:
	mov	e6,d9,d8
.L458:
	call	__d_sub
.L531:
	mov	e10,d3,d2
.L532:
	mov	d6,#0
	mov	d7,#0
	addih	d7,d7,#16384
.L459:
	mov	e4,d13,d12
.L460:
	call	__d_div
	mov	e4,d3,d2
	call	sin
.L533:
	mov	e12,d3,d2
.L461:
	mov	e4,d15,d14
.L462:
	call	cos
.L534:
	mov	e8,d3,d2
.L535:
	mov	d6,#0
	mov	d7,#0
	addih	d7,d7,#16384
.L463:
	mov	e4,d11,d10
.L464:
	call	__d_div
	mov	e4,d3,d2
	call	sin
.L536:
	mov	e10,d3,d2
.L537:
	ld.d	e4,[a10]0
.L465:
	call	cos
.L466:
	mov	e4,d9,d8
	mov	e6,d3,d2
	call	__d_mul
.L538:
	mov	e8,d3,d2
.L539:
	mov	d6,#0
	mov	d7,#0
	addih	d7,d7,#16384
	mov	e4,d11,d10
	call	pow
.L540:
	mov	e4,d9,d8
	mov	e6,d3,d2
	call	__d_mul
.L541:
	mov	e8,d3,d2
.L542:
	mov	d6,#0
	mov	d7,#0
	addih	d7,d7,#16384
	mov	e4,d13,d12
	call	pow
.L543:
	mov	e4,d3,d2
	mov	e6,d9,d8
	call	__d_add
	mov	e4,d3,d2
	call	sqrt
.L544:
	mov	e4,d3,d2
	call	asin
.L545:
	mov	d6,#0
	mov	d7,#0
	addih	d7,d7,#16384
.L546:
	mov	e4,d3,d2
	call	__d_mul
.L467:
	mov	e4,d3,d2
.L468:
	ld.d	e6,[a10]8
.L469:
	call	__d_mul
.L470:
	j	.L33
.L33:
	ret
.L201:
	
__get_two_points_distance_function_end:
	.size	get_two_points_distance,__get_two_points_distance_function_end-get_two_points_distance
.L117:
	; End of function
	
	.sdecl	'.text.zf_device_gnss.get_two_points_azimuth',code,cluster('get_two_points_azimuth')
	.sect	'.text.zf_device_gnss.get_two_points_azimuth'
	.align	2
	
	.global	get_two_points_azimuth
; Function get_two_points_azimuth
.L102:
get_two_points_azimuth:	.type	func
	sub.a	a10,#8
.L471:
	mov	e8,d7,d6
	ld.d	e10,[a10]8
.L474:
	ld.d	e14,[a10]16
.L476:
	movh.a	a15,#@his(.18.cnt)
	lea	a15,[a15]@los(.18.cnt)
	ld.d	e6,[a15]0
.L473:
	call	__d_mul
.L472:
	mov	d6,#0
	mov.u	d7,#32768
	addih	d7,d7,#16486
	mov	e4,d3,d2
	call	__d_div
.L551:
	mov	e12,d3,d2
.L552:
	movh.a	a15,#@his(.18.cnt)
	lea	a15,[a15]@los(.18.cnt)
	ld.d	e6,[a15]0
	mov	e4,d11,d10
	call	__d_mul
	mov	d6,#0
	mov.u	d7,#32768
	addih	d7,d7,#16486
	mov	e4,d3,d2
	call	__d_div
.L478:
	st.d	[a10]8,e2
.L475:
	movh.a	a15,#@his(.18.cnt)
	lea	a15,[a15]@los(.18.cnt)
	ld.d	e6,[a15]0
.L479:
	mov	e4,d9,d8
.L480:
	call	__d_mul
	mov	d6,#0
	mov.u	d7,#32768
	addih	d7,d7,#16486
	mov	e4,d3,d2
	call	__d_div
.L553:
	mov	e10,d3,d2
.L554:
	movh.a	a15,#@his(.18.cnt)
	lea	a15,[a15]@los(.18.cnt)
	ld.d	e6,[a15]0
	mov	e4,d15,d14
	call	__d_mul
	mov	d6,#0
	mov.u	d7,#32768
	addih	d7,d7,#16486
	mov	e4,d3,d2
	call	__d_div
.L555:
	mov	e14,d3,d2
.L220:
	mov	e4,d15,d14
.L477:
	mov	e6,d11,d10
.L481:
	call	__d_sub
	mov	e4,d3,d2
	call	sin
.L556:
	mov	e8,d3,d2
.L557:
	ld.d	e4,[a10]8
.L482:
	call	cos
.L483:
	mov	e4,d9,d8
	mov	e6,d3,d2
	call	__d_mul
.L484:
	st.d	[a10]0,e2
.L485:
	mov	e4,d13,d12
.L486:
	call	cos
.L558:
	mov	e8,d3,d2
.L487:
	mov	e4,d13,d12
.L488:
	call	sin
.L559:
	mov	e12,d3,d2
.L560:
	ld.d	e4,[a10]8
.L489:
	call	cos
.L490:
	mov	e4,d13,d12
	mov	e6,d3,d2
	call	__d_mul
.L561:
	mov	e12,d3,d2
.L491:
	mov	e4,d15,d14
.L492:
	mov	e6,d11,d10
.L493:
	call	__d_sub
	mov	e4,d3,d2
	call	cos
.L562:
	mov	e4,d13,d12
	mov	e6,d3,d2
	call	__d_mul
.L563:
	mov	e10,d3,d2
.L564:
	ld.d	e4,[a10]8
.L494:
	call	sin
.L495:
	mov	e4,d9,d8
	mov	e6,d3,d2
	call	__d_mul
.L565:
	mov	e4,d3,d2
	mov	e6,d11,d10
	call	__d_sub
.L566:
	ld.d	e4,[a10]0
.L496:
	mov	e6,d3,d2
.L498:
	call	atan2
.L497:
	mov	d6,#0
	mov.u	d7,#32768
	addih	d7,d7,#16486
	mov	e4,d3,d2
	call	__d_mul
	movh.a	a15,#@his(.18.cnt)
	lea	a15,[a15]@los(.18.cnt)
	ld.d	e6,[a15]0
	mov	e4,d3,d2
	call	__d_div
.L567:
	mov	e8,d3,d2
.L568:
	mov	d6,#0
	mov	d7,#0
.L499:
	mov	e4,d9,d8
.L500:
	call	__d_fgt
.L569:
	jne	d2,#0,.L34
.L570:
	mov	d6,#0
	mov.u	d7,#32768
	addih	d7,d7,#16502
.L501:
	mov	e4,d9,d8
.L502:
	call	__d_add
.L571:
	j	.L35
.L34:
	mov	e2,d9,d8
.L35:
	j	.L36
.L36:
	ret
.L215:
	
__get_two_points_azimuth_function_end:
	.size	get_two_points_azimuth,__get_two_points_azimuth_function_end-get_two_points_azimuth
.L122:
	; End of function
	
	.sdecl	'.text.zf_device_gnss.gnss_data_parse',code,cluster('gnss_data_parse')
	.sect	'.text.zf_device_gnss.gnss_data_parse'
	.align	2
	
	.global	gnss_data_parse
; Function gnss_data_parse
.L104:
gnss_data_parse:	.type	func
	sub.a	a10,#8
.L503:
	mov	d8,#0
.L504:
	movh.a	a15,#@his(.1.ini)
	lea	a15,[a15]@los(.1.ini)
	lea	a15,[a15]0
.L576:
	lea	a2,[a10]0
	mov.a	a4,#4
.L37:
	ld.bu	d15,[a15+]
	st.b	[a2+],d15
	loop	a4,.L37
.L38:
	movh.a	a15,#@his(gnss_rmc_state)
	lea	a15,[a15]@los(gnss_rmc_state)
	ld.bu	d15,[a15]
.L577:
	jne	d15,#1,.L39
.L578:
	movh.a	a15,#@his(gnss_rmc_state)
	lea	a15,[a15]@los(gnss_rmc_state)
.L579:
	mov	d15,#2
.L580:
	st.b	[a15],d15
.L581:
	movh.a	a4,#@his(gps_rmc_buffer)
	lea	a4,[a4]@los(gps_rmc_buffer)
.L582:
	mov	d4,#42
	call	strchr
.L583:
	add.a	a2,#1
.L584:
	lea	a4,[a10]2
.L585:
	mov	d4,#2
	mov.aa	a5,a2
	call	strncpy
.L586:
	lea	a4,[a10]0
.L587:
	call	func_str_to_hex
.L588:
	extr.u	d0,d2,#0,#8
.L505:
	movh.a	a15,#@his(gps_rmc_buffer)
	lea	a15,[a15]@los(gps_rmc_buffer)
.L589:
	ld.bu	d1,[a15]1
.L506:
	mov	d2,#2
.L507:
	j	.L40
.L41:
	movh.a	a15,#@his(gps_rmc_buffer)
	lea	a15,[a15]@los(gps_rmc_buffer)
.L590:
	addsc.a	a15,a15,d2,#0
	ld.bu	d15,[a15]
.L591:
	xor	d1,d15
.L592:
	add	d2,#1
.L40:
	movh.a	a15,#@his(gps_rmc_buffer)
	lea	a15,[a15]@los(gps_rmc_buffer)
.L593:
	addsc.a	a15,a15,d2,#0
	ld.bu	d3,[a15]
.L594:
	mov	d15,#42
.L595:
	jne	d15,d3,.L41
.L596:
	jeq	d1,d0,.L42
.L597:
	mov	d8,#1
.L598:
	j	.L43
.L42:
	movh.a	a4,#@his(gps_rmc_buffer)
	lea	a4,[a4]@los(gps_rmc_buffer)
.L599:
	movh.a	a5,#@his(gnss)
	lea	a5,[a5]@los(gnss)
	call	gps_gnrmc_parse
.L39:
	movh.a	a15,#@his(gnss_rmc_state)
	lea	a15,[a15]@los(gnss_rmc_state)
.L600:
	mov	d15,#0
.L601:
	st.b	[a15],d15
.L602:
	movh.a	a15,#@his(gnss_gga_state)
	lea	a15,[a15]@los(gnss_gga_state)
	ld.bu	d15,[a15]
.L603:
	jne	d15,#1,.L44
.L604:
	movh.a	a15,#@his(gnss_gga_state)
	lea	a15,[a15]@los(gnss_gga_state)
.L605:
	mov	d15,#2
.L606:
	st.b	[a15],d15
.L607:
	movh.a	a4,#@his(gps_gga_buffer)
	lea	a4,[a4]@los(gps_gga_buffer)
.L608:
	mov	d4,#42
	call	strchr
.L609:
	add.a	a2,#1
.L610:
	lea	a4,[a10]2
.L611:
	mov	d4,#2
	mov.aa	a5,a2
	call	strncpy
.L612:
	lea	a4,[a10]0
.L613:
	call	func_str_to_hex
.L614:
	extr.u	d0,d2,#0,#8
.L508:
	movh.a	a15,#@his(gps_gga_buffer)
	lea	a15,[a15]@los(gps_gga_buffer)
.L615:
	ld.bu	d1,[a15]1
.L509:
	mov	d2,#2
.L510:
	j	.L45
.L46:
	movh.a	a15,#@his(gps_gga_buffer)
	lea	a15,[a15]@los(gps_gga_buffer)
.L616:
	addsc.a	a15,a15,d2,#0
	ld.bu	d15,[a15]
.L617:
	xor	d1,d15
.L618:
	add	d2,#1
.L45:
	movh.a	a15,#@his(gps_gga_buffer)
	lea	a15,[a15]@los(gps_gga_buffer)
.L619:
	addsc.a	a15,a15,d2,#0
	ld.bu	d3,[a15]
.L620:
	mov	d15,#42
.L621:
	jne	d15,d3,.L46
.L622:
	jeq	d1,d0,.L47
.L623:
	mov	d8,#1
.L624:
	j	.L48
.L47:
	movh.a	a4,#@his(gps_gga_buffer)
	lea	a4,[a4]@los(gps_gga_buffer)
.L625:
	movh.a	a5,#@his(gnss)
	lea	a5,[a5]@los(gnss)
	call	gps_gngga_parse
.L44:
	movh.a	a15,#@his(gnss_gga_state)
	lea	a15,[a15]@los(gnss_gga_state)
.L626:
	mov	d15,#0
.L627:
	st.b	[a15],d15
.L628:
	movh.a	a15,#@his(gnss_ths_state)
	lea	a15,[a15]@los(gnss_ths_state)
	ld.bu	d15,[a15]
.L629:
	jne	d15,#1,.L49
.L630:
	movh.a	a15,#@his(gnss_ths_state)
	lea	a15,[a15]@los(gnss_ths_state)
.L631:
	mov	d15,#2
.L632:
	st.b	[a15],d15
.L633:
	movh.a	a4,#@his(gps_ths_buffer)
	lea	a4,[a4]@los(gps_ths_buffer)
.L634:
	mov	d4,#42
	call	strchr
.L635:
	add.a	a2,#1
.L636:
	lea	a4,[a10]2
.L637:
	mov	d4,#2
	mov.aa	a5,a2
	call	strncpy
.L638:
	lea	a4,[a10]0
.L639:
	call	func_str_to_hex
.L640:
	extr.u	d0,d2,#0,#8
.L511:
	movh.a	a15,#@his(gps_ths_buffer)
	lea	a15,[a15]@los(gps_ths_buffer)
.L641:
	ld.bu	d1,[a15]1
.L512:
	mov	d2,#2
.L513:
	j	.L50
.L51:
	movh.a	a15,#@his(gps_ths_buffer)
	lea	a15,[a15]@los(gps_ths_buffer)
.L642:
	addsc.a	a15,a15,d2,#0
	ld.bu	d15,[a15]
.L643:
	xor	d1,d15
.L644:
	add	d2,#1
.L50:
	movh.a	a15,#@his(gps_ths_buffer)
	lea	a15,[a15]@los(gps_ths_buffer)
.L645:
	addsc.a	a15,a15,d2,#0
	ld.bu	d3,[a15]
.L646:
	mov	d15,#42
.L647:
	jne	d15,d3,.L51
.L648:
	jeq	d1,d0,.L52
.L649:
	mov	d8,#1
.L650:
	j	.L53
.L52:
	movh.a	a4,#@his(gps_ths_buffer)
	lea	a4,[a4]@los(gps_ths_buffer)
.L651:
	movh.a	a5,#@his(gnss)
	lea	a5,[a5]@los(gnss)
	call	gps_gnths_parse
.L49:
	movh.a	a15,#@his(gnss_ths_state)
	lea	a15,[a15]@los(gnss_ths_state)
.L652:
	mov	d15,#0
.L653:
	st.b	[a15],d15
.L53:
.L48:
.L43:
	mov	d2,d8
.L514:
	j	.L54
.L54:
	ret
.L225:
	
__gnss_data_parse_function_end:
	.size	gnss_data_parse,__gnss_data_parse_function_end-gnss_data_parse
.L127:
	; End of function
	
	.sdecl	'.text.zf_device_gnss.gnss_uart_callback',code,cluster('gnss_uart_callback')
	.sect	'.text.zf_device_gnss.gnss_uart_callback'
	.align	2
	
	.global	gnss_uart_callback
; Function gnss_uart_callback
.L106:
gnss_uart_callback:	.type	func
	sub.a	a10,#16
.L515:
	mov	d15,#0
.L658:
	st.w	[a10]6,d15
.L659:
	movh.a	a15,#@his(gnss_state)
	lea	a15,[a15]@los(gnss_state)
	ld.bu	d15,[a15]
.L660:
	jeq	d15,#0,.L55
.L237:
	j	.L56
.L57:
	movh.a	a4,#@his(gnss_receiver_fifo)
	lea	a4,[a4]@los(gnss_receiver_fifo)
.L661:
	lea	a5,[a10]10
.L662:
	mov	d4,#1
	call	fifo_write_buffer
.L56:
	mov	d4,#3
.L663:
	lea	a4,[a10]10
	call	uart_query_byte
.L664:
	jne	d2,#0,.L57
.L665:
	ld.bu	d15,[a10]10
.L666:
	mov	d0,#10
.L667:
	jne	d15,d0,.L58
.L668:
	mov	d15,#6
.L669:
	st.w	[a10]6,d15
.L670:
	movh.a	a4,#@his(gnss_receiver_fifo)
	lea	a4,[a4]@los(gnss_receiver_fifo)
.L671:
	lea	a5,[a10]0
.L672:
	lea	a6,[a10]6
.L673:
	mov	d4,#1
	call	fifo_read_buffer
.L674:
	lea	a4,[a10]3
.L675:
	movh.a	a5,#@his(.2.str)
	lea	a5,[a5]@los(.2.str)
.L676:
	mov	d4,#3
	call	strncmp
.L677:
	jne	d2,#0,.L59
.L678:
	movh.a	a15,#@his(gnss_rmc_state)
	lea	a15,[a15]@los(gnss_rmc_state)
	ld.bu	d15,[a15]
.L679:
	jeq	d15,#2,.L60
.L680:
	movh.a	a15,#@his(gnss_rmc_state)
	lea	a15,[a15]@los(gnss_rmc_state)
.L681:
	mov	d15,#1
.L682:
	st.b	[a15],d15
.L683:
	movh.a	a4,#@his(gnss_receiver_fifo)
	lea	a4,[a4]@los(gnss_receiver_fifo)
	call	fifo_used
.L684:
	st.w	[a10]6,d2
.L685:
	movh.a	a4,#@his(gnss_receiver_fifo)
	lea	a4,[a4]@los(gnss_receiver_fifo)
.L686:
	movh.a	a5,#@his(gps_rmc_buffer)
	lea	a5,[a5]@los(gps_rmc_buffer)
.L687:
	lea	a6,[a10]6
.L688:
	mov	d4,#0
	call	fifo_read_buffer
.L60:
	j	.L61
.L59:
	lea	a4,[a10]3
.L689:
	movh.a	a5,#@his(.3.str)
	lea	a5,[a5]@los(.3.str)
.L690:
	mov	d4,#3
	call	strncmp
.L691:
	jne	d2,#0,.L62
.L692:
	movh.a	a15,#@his(gnss_gga_state)
	lea	a15,[a15]@los(gnss_gga_state)
	ld.bu	d15,[a15]
.L693:
	jeq	d15,#2,.L63
.L694:
	movh.a	a15,#@his(gnss_gga_state)
	lea	a15,[a15]@los(gnss_gga_state)
.L695:
	mov	d15,#1
.L696:
	st.b	[a15],d15
.L697:
	movh.a	a4,#@his(gnss_receiver_fifo)
	lea	a4,[a4]@los(gnss_receiver_fifo)
	call	fifo_used
.L698:
	st.w	[a10]6,d2
.L699:
	movh.a	a4,#@his(gnss_receiver_fifo)
	lea	a4,[a4]@los(gnss_receiver_fifo)
.L700:
	movh.a	a5,#@his(gps_gga_buffer)
	lea	a5,[a5]@los(gps_gga_buffer)
.L701:
	lea	a6,[a10]6
.L702:
	mov	d4,#0
	call	fifo_read_buffer
.L63:
	j	.L64
.L62:
	lea	a4,[a10]3
.L703:
	movh.a	a5,#@his(.4.str)
	lea	a5,[a5]@los(.4.str)
.L704:
	mov	d4,#3
	call	strncmp
.L705:
	jne	d2,#0,.L65
.L706:
	movh.a	a15,#@his(gnss_ths_state)
	lea	a15,[a15]@los(gnss_ths_state)
	ld.bu	d15,[a15]
.L707:
	jeq	d15,#2,.L66
.L708:
	movh.a	a15,#@his(gnss_ths_state)
	lea	a15,[a15]@los(gnss_ths_state)
.L709:
	mov	d15,#1
.L710:
	st.b	[a15],d15
.L711:
	movh.a	a4,#@his(gnss_receiver_fifo)
	lea	a4,[a4]@los(gnss_receiver_fifo)
	call	fifo_used
.L712:
	st.w	[a10]6,d2
.L713:
	movh.a	a4,#@his(gnss_receiver_fifo)
	lea	a4,[a4]@los(gnss_receiver_fifo)
.L714:
	movh.a	a5,#@his(gps_ths_buffer)
	lea	a5,[a5]@los(gps_ths_buffer)
.L715:
	lea	a6,[a10]6
.L716:
	mov	d4,#0
	call	fifo_read_buffer
.L66:
.L65:
.L64:
.L61:
	movh.a	a4,#@his(gnss_receiver_fifo)
	lea	a4,[a4]@los(gnss_receiver_fifo)
	call	fifo_clear
.L717:
	movh.a	a15,#@his(gnss_flag)
	lea	a15,[a15]@los(gnss_flag)
.L718:
	mov	d15,#1
.L719:
	st.b	[a15],d15
.L58:
.L55:
	ret
.L233:
	
__gnss_uart_callback_function_end:
	.size	gnss_uart_callback,__gnss_uart_callback_function_end-gnss_uart_callback
.L132:
	; End of function
	
	.sdecl	'.text.zf_device_gnss.gnss_init',code,cluster('gnss_init')
	.sect	'.text.zf_device_gnss.gnss_init'
	.align	2
	
	.global	gnss_init
; Function gnss_init
.L108:
gnss_init:	.type	func
	sub.a	a10,#152
.L516:
	movh.a	a15,#@his(.5.ini)
	lea	a15,[a15]@los(.5.ini)
	lea	a15,[a15]0
.L724:
	lea	a2,[a10]0
	lea	a4,27
.L67:
	ld.bu	d15,[a15+]
	st.b	[a2+],d15
	loop	a4,.L67
.L725:
	movh.a	a15,#@his(.6.ini)
	lea	a15,[a15]@los(.6.ini)
	lea	a15,[a15]0
.L726:
	lea	a2,[a10]28
	mov.a	a4,#10
.L68:
	ld.bu	d15,[a15+]
	st.b	[a2+],d15
	loop	a4,.L68
.L727:
	movh.a	a15,#@his(.7.ini)
	lea	a15,[a15]@los(.7.ini)
	lea	a15,[a15]0
.L728:
	lea	a2,[a10]39
	mov.a	a4,#10
.L69:
	ld.bu	d15,[a15+]
	st.b	[a2+],d15
	loop	a4,.L69
.L729:
	movh.a	a15,#@his(.8.ini)
	lea	a15,[a15]@los(.8.ini)
	lea	a15,[a15]0
.L730:
	lea	a2,[a10]50
	mov.a	a4,#10
.L70:
	ld.bu	d15,[a15+]
	st.b	[a2+],d15
	loop	a4,.L70
.L731:
	movh.a	a15,#@his(.9.ini)
	lea	a15,[a15]@los(.9.ini)
	lea	a15,[a15]0
.L732:
	lea	a2,[a10]61
	mov.a	a4,#10
.L71:
	ld.bu	d15,[a15+]
	st.b	[a2+],d15
	loop	a4,.L71
.L733:
	movh.a	a15,#@his(.10.ini)
	lea	a15,[a15]@los(.10.ini)
	lea	a15,[a15]0
.L734:
	lea	a2,[a10]72
	mov.a	a4,#10
.L72:
	ld.bu	d15,[a15+]
	st.b	[a2+],d15
	loop	a4,.L72
.L735:
	movh.a	a15,#@his(.11.ini)
	lea	a15,[a15]@los(.11.ini)
	lea	a15,[a15]0
.L736:
	lea	a2,[a10]83
	mov.a	a4,#10
.L73:
	ld.bu	d15,[a15+]
	st.b	[a2+],d15
	loop	a4,.L73
.L737:
	movh.a	a15,#@his(.12.ini)
	lea	a15,[a15]@los(.12.ini)
	lea	a15,[a15]0
.L738:
	lea	a2,[a10]94
	mov.a	a4,#10
.L74:
	ld.bu	d15,[a15+]
	st.b	[a2+],d15
	loop	a4,.L74
.L739:
	movh.a	a15,#@his(.13.ini)
	lea	a15,[a15]@los(.13.ini)
	lea	a15,[a15]0
.L740:
	lea	a2,[a10]105
	mov.a	a4,#10
.L75:
	ld.bu	d15,[a15+]
	st.b	[a2+],d15
	loop	a4,.L75
.L741:
	movh.a	a15,#@his(.14.ini)
	lea	a15,[a15]@los(.14.ini)
	lea	a15,[a15]0
.L742:
	lea	a2,[a10]116
	mov.a	a4,#10
.L76:
	ld.bu	d15,[a15+]
	st.b	[a2+],d15
	loop	a4,.L76
.L743:
	movh.a	a15,#@his(.15.ini)
	lea	a15,[a15]@los(.15.ini)
	lea	a15,[a15]0
.L744:
	lea	a2,[a10]127
	mov.a	a4,#10
.L77:
	ld.bu	d15,[a15+]
	st.b	[a2+],d15
	loop	a4,.L77
.L745:
	movh.a	a15,#@his(.16.ini)
	lea	a15,[a15]@los(.16.ini)
	lea	a15,[a15]0
.L746:
	lea	a2,[a10]138
	mov.a	a4,#10
.L78:
	ld.bu	d15,[a15+]
	st.b	[a2+],d15
	loop	a4,.L78
.L747:
	jeq	d4,#1,.L79
.L748:
	jne	d4,#1,.L80
.L79:
	movh.a	a4,#@his(gnss_receiver_fifo)
	lea	a4,[a4]@los(gnss_receiver_fifo)
.L749:
	mov	d4,#0
.L517:
	movh.a	a5,#@his(gnss_receiver_buffer)
	lea	a5,[a5]@los(gnss_receiver_buffer)
.L750:
	mov	d5,#128
	call	fifo_init
.L751:
	mov	d4,#500
	call	system_delay_ms
.L752:
	mov	d4,#3
.L753:
	mov.u	d5,#49664
	addih	d5,d5,#1
.L754:
	mov	d6,#21
.L755:
	mov	d7,#14
	call	uart_init
.L756:
	mov	d4,#3
.L757:
	lea	a4,[a10]0
.L758:
	mov	d5,#28
	call	uart_write_buffer
.L759:
	mov	d4,#200
	call	system_delay_ms
.L760:
	mov	d4,#3
.L761:
	lea	a4,[a10]39
.L762:
	mov	d5,#11
	call	uart_write_buffer
.L763:
	mov	d4,#50
	call	system_delay_ms
.L764:
	mov	d4,#3
.L765:
	lea	a4,[a10]28
.L766:
	mov	d5,#11
	call	uart_write_buffer
.L767:
	mov	d4,#50
	call	system_delay_ms
.L768:
	mov	d4,#3
.L769:
	lea	a4,[a10]50
.L770:
	mov	d5,#11
	call	uart_write_buffer
.L771:
	mov	d4,#50
	call	system_delay_ms
.L772:
	mov	d4,#3
.L773:
	lea	a4,[a10]61
.L774:
	mov	d5,#11
	call	uart_write_buffer
.L775:
	mov	d4,#50
	call	system_delay_ms
.L776:
	mov	d4,#3
.L777:
	lea	a4,[a10]72
.L778:
	mov	d5,#11
	call	uart_write_buffer
.L779:
	mov	d4,#50
	call	system_delay_ms
.L780:
	mov	d4,#3
.L781:
	lea	a4,[a10]83
.L782:
	mov	d5,#11
	call	uart_write_buffer
.L783:
	mov	d4,#50
	call	system_delay_ms
.L784:
	mov	d4,#3
.L785:
	lea	a4,[a10]94
.L786:
	mov	d5,#11
	call	uart_write_buffer
.L787:
	mov	d4,#50
	call	system_delay_ms
.L788:
	mov	d4,#3
.L789:
	lea	a4,[a10]105
.L790:
	mov	d5,#11
	call	uart_write_buffer
.L791:
	mov	d4,#50
	call	system_delay_ms
.L792:
	mov	d4,#3
.L793:
	lea	a4,[a10]116
.L794:
	mov	d5,#11
	call	uart_write_buffer
.L795:
	mov	d4,#50
	call	system_delay_ms
.L796:
	mov	d4,#3
.L797:
	lea	a4,[a10]127
.L798:
	mov	d5,#11
	call	uart_write_buffer
.L799:
	mov	d4,#50
	call	system_delay_ms
.L800:
	mov	d4,#3
.L801:
	lea	a4,[a10]138
.L802:
	mov	d5,#11
	call	uart_write_buffer
.L803:
	mov	d4,#50
	call	system_delay_ms
.L804:
	movh.a	a15,#@his(gnss_state)
	lea	a15,[a15]@los(gnss_state)
.L805:
	mov	d15,#1
.L806:
	st.b	[a15],d15
.L807:
	mov	d4,#3
.L808:
	mov	d5,#1
	call	uart_rx_interrupt
.L809:
	j	.L81
.L80:
	jne	d4,#2,.L82
.L810:
	movh.a	a4,#@his(gnss_receiver_fifo)
	lea	a4,[a4]@los(gnss_receiver_fifo)
.L811:
	mov	d4,#0
.L518:
	movh.a	a5,#@his(gnss_receiver_buffer)
	lea	a5,[a5]@los(gnss_receiver_buffer)
.L812:
	mov	d5,#128
	call	fifo_init
.L813:
	mov	d4,#3
.L814:
	mov.u	d5,#49664
	addih	d5,d5,#1
.L815:
	mov	d6,#21
.L816:
	mov	d7,#14
	call	uart_init
.L817:
	movh.a	a15,#@his(gnss_state)
	lea	a15,[a15]@los(gnss_state)
.L818:
	mov	d15,#1
.L819:
	st.b	[a15],d15
.L820:
	mov	d4,#3
.L821:
	mov	d5,#1
	call	uart_rx_interrupt
.L82:
.L81:
	ret
.L239:
	
__gnss_init_function_end:
	.size	gnss_init,__gnss_init_function_end-gnss_init
.L137:
	; End of function
	
	.sdecl	'.data.zf_device_gnss.gnss_flag',data,cluster('gnss_flag')
	.sect	'.data.zf_device_gnss.gnss_flag'
	.global	gnss_flag
gnss_flag:	.type	object
	.size	gnss_flag,1
	.space	1
	.sdecl	'.bss.zf_device_gnss.gnss',data,cluster('gnss')
	.sect	'.bss.zf_device_gnss.gnss'
	.global	gnss
	.align	4
gnss:	.type	object
	.size	gnss,64
	.space	64
	.sdecl	'.data.zf_device_gnss.gnss_state',data,cluster('gnss_state')
	.sect	'.data.zf_device_gnss.gnss_state'
gnss_state:	.type	object
	.size	gnss_state,1
	.space	1
	.sdecl	'.bss.zf_device_gnss.gnss_receiver_fifo',data,cluster('gnss_receiver_fifo')
	.sect	'.bss.zf_device_gnss.gnss_receiver_fifo'
	.align	4
gnss_receiver_fifo:	.type	object
	.size	gnss_receiver_fifo,24
	.space	24
	.sdecl	'.bss.zf_device_gnss.gnss_receiver_buffer',data,cluster('gnss_receiver_buffer')
	.sect	'.bss.zf_device_gnss.gnss_receiver_buffer'
gnss_receiver_buffer:	.type	object
	.size	gnss_receiver_buffer,128
	.space	128
	.sdecl	'.data.zf_device_gnss.gnss_gga_state',data,cluster('gnss_gga_state')
	.sect	'.data.zf_device_gnss.gnss_gga_state'
gnss_gga_state:	.type	object
	.size	gnss_gga_state,1
	.space	1
	.sdecl	'.data.zf_device_gnss.gnss_rmc_state',data,cluster('gnss_rmc_state')
	.sect	'.data.zf_device_gnss.gnss_rmc_state'
gnss_rmc_state:	.type	object
	.size	gnss_rmc_state,1
	.space	1
	.sdecl	'.data.zf_device_gnss.gnss_ths_state',data,cluster('gnss_ths_state')
	.sect	'.data.zf_device_gnss.gnss_ths_state'
gnss_ths_state:	.type	object
	.size	gnss_ths_state,1
	.space	1
	.sdecl	'.bss.zf_device_gnss.gps_gga_buffer',data,cluster('gps_gga_buffer')
	.sect	'.bss.zf_device_gnss.gps_gga_buffer'
gps_gga_buffer:	.type	object
	.size	gps_gga_buffer,128
	.space	128
	.sdecl	'.bss.zf_device_gnss.gps_rmc_buffer',data,cluster('gps_rmc_buffer')
	.sect	'.bss.zf_device_gnss.gps_rmc_buffer'
gps_rmc_buffer:	.type	object
	.size	gps_rmc_buffer,128
	.space	128
	.sdecl	'.bss.zf_device_gnss.gps_ths_buffer',data,cluster('gps_ths_buffer')
	.sect	'.bss.zf_device_gnss.gps_ths_buffer'
gps_ths_buffer:	.type	object
	.size	gps_ths_buffer,128
	.space	128
	.sdecl	'.rodata.zf_device_gnss..1.ini',data,rom
	.sect	'.rodata.zf_device_gnss..1.ini'
.1.ini:	.type	object
	.size	.1.ini,5
	.byte	48,120
	.space	3
	.sdecl	'.rodata.zf_device_gnss..2.str',data,rom
	.sect	'.rodata.zf_device_gnss..2.str'
.2.str:	.type	object
	.size	.2.str,4
	.byte	82,77,67
	.space	1
	.sdecl	'.rodata.zf_device_gnss..3.str',data,rom
	.sect	'.rodata.zf_device_gnss..3.str'
.3.str:	.type	object
	.size	.3.str,4
	.byte	71,71,65
	.space	1
	.sdecl	'.rodata.zf_device_gnss..4.str',data,rom
	.sect	'.rodata.zf_device_gnss..4.str'
.4.str:	.type	object
	.size	.4.str,4
	.byte	84,72,83
	.space	1
	.sdecl	'.rodata.zf_device_gnss..5.ini',data,rom
	.sect	'.rodata.zf_device_gnss..5.ini'
.5.ini:	.type	object
	.size	.5.ini,28
	.byte	241,217,6,66
	.byte	20
	.space	2
	.byte	10,5
	.space	1
	.byte	100
	.space	3
	.byte	96,234
	.space	2
	.byte	208,7
	.space	2
	.byte	200
	.space	3
	.byte	184,237
	.sdecl	'.rodata.zf_device_gnss..6.ini',data,rom
	.sect	'.rodata.zf_device_gnss..6.ini'
.6.ini:	.type	object
	.size	.6.ini,11
	.byte	241,217,6,1
	.byte	3
	.space	1
	.byte	240
	.space	1
	.byte	1,251,16
	.sdecl	'.rodata.zf_device_gnss..7.ini',data,rom
	.sect	'.rodata.zf_device_gnss..7.ini'
.7.ini:	.type	object
	.size	.7.ini,11
	.byte	241,217,6,1
	.byte	3
	.space	1
	.byte	240,5,1
	.space	1
	.byte	26
	.sdecl	'.rodata.zf_device_gnss..8.ini',data,rom
	.sect	'.rodata.zf_device_gnss..8.ini'
.8.ini:	.type	object
	.size	.8.ini,11
	.byte	241,217,6,1
	.byte	3
	.space	1
	.byte	240,1
	.space	1
	.byte	251,17
	.sdecl	'.rodata.zf_device_gnss..9.ini',data,rom
	.sect	'.rodata.zf_device_gnss..9.ini'
.9.ini:	.type	object
	.size	.9.ini,11
	.byte	241,217,6,1
	.byte	3
	.space	1
	.byte	240,2
	.space	1
	.byte	252,19
	.sdecl	'.rodata.zf_device_gnss..10.ini',data,rom
	.sect	'.rodata.zf_device_gnss..10.ini'
.10.ini:	.type	object
	.size	.10.ini,11
	.byte	241,217,6,1
	.byte	3
	.space	1
	.byte	240,3
	.space	1
	.byte	253,21
	.sdecl	'.rodata.zf_device_gnss..11.ini',data,rom
	.sect	'.rodata.zf_device_gnss..11.ini'
.11.ini:	.type	object
	.size	.11.ini,11
	.byte	241,217,6,1
	.byte	3
	.space	1
	.byte	240,4
	.space	1
	.byte	254,23
	.sdecl	'.rodata.zf_device_gnss..12.ini',data,rom
	.sect	'.rodata.zf_device_gnss..12.ini'
.12.ini:	.type	object
	.size	.12.ini,11
	.byte	241,217,6,1
	.byte	3
	.space	1
	.byte	240,6
	.space	2
	.byte	27
	.sdecl	'.rodata.zf_device_gnss..13.ini',data,rom
	.sect	'.rodata.zf_device_gnss..13.ini'
.13.ini:	.type	object
	.size	.13.ini,11
	.byte	241,217,6,1
	.byte	3
	.space	1
	.byte	240,7
	.space	1
	.byte	1,29
	.sdecl	'.rodata.zf_device_gnss..14.ini',data,rom
	.sect	'.rodata.zf_device_gnss..14.ini'
.14.ini:	.type	object
	.size	.14.ini,11
	.byte	241,217,6,1
	.byte	3
	.space	1
	.byte	240,8
	.space	1
	.byte	2,31
	.sdecl	'.rodata.zf_device_gnss..15.ini',data,rom
	.sect	'.rodata.zf_device_gnss..15.ini'
.15.ini:	.type	object
	.size	.15.ini,11
	.byte	241,217,6,1
	.byte	3
	.space	1
	.byte	240,64
	.space	1
	.byte	58,143
	.sdecl	'.rodata.zf_device_gnss..16.ini',data,rom
	.sect	'.rodata.zf_device_gnss..16.ini'
.16.ini:	.type	object
	.size	.16.ini,11
	.byte	241,217,6,1
	.byte	3
	.space	1
	.byte	240,32
	.space	1
	.byte	26,79
	.calls	'get_float_number','__d_dtof'
	.calls	'gps_gnrmc_parse','__d_dtoi'
	.calls	'gps_gnrmc_parse','__d_itod'
	.calls	'gps_gnrmc_parse','__d_sub'
	.calls	'gps_gnrmc_parse','__d_uitod'
	.calls	'gps_gnrmc_parse','__d_mul'
	.calls	'gps_gnrmc_parse','__d_div'
	.calls	'gps_gnrmc_parse','__d_add'
	.calls	'get_two_points_distance','__d_mul'
	.calls	'get_two_points_distance','__d_div'
	.calls	'get_two_points_distance','__d_sub'
	.calls	'get_two_points_distance','__d_add'
	.calls	'get_two_points_azimuth','__d_mul'
	.calls	'get_two_points_azimuth','__d_div'
	.calls	'get_two_points_azimuth','__d_sub'
	.calls	'get_two_points_azimuth','__d_fgt'
	.calls	'get_two_points_azimuth','__d_add'
	.calls	'get_parameter_index','strchr'
	.calls	'get_int_number','get_parameter_index'
	.calls	'get_int_number','strncpy'
	.calls	'get_int_number','func_str_to_int'
	.calls	'get_float_number','get_parameter_index'
	.calls	'get_float_number','strncpy'
	.calls	'get_float_number','func_str_to_double'
	.calls	'get_double_number','get_parameter_index'
	.calls	'get_double_number','strncpy'
	.calls	'get_double_number','func_str_to_double'
	.calls	'gps_gnrmc_parse','get_parameter_index'
	.calls	'gps_gnrmc_parse','get_double_number'
	.calls	'gps_gnrmc_parse','get_float_number'
	.calls	'gps_gnrmc_parse','utc_to_btc'
	.calls	'gps_gngga_parse','get_parameter_index'
	.calls	'gps_gngga_parse','get_int_number'
	.calls	'gps_gngga_parse','get_float_number'
	.calls	'gps_gnths_parse','get_parameter_index'
	.calls	'gps_gnths_parse','get_float_number'
	.calls	'get_two_points_distance','sin'
	.calls	'get_two_points_distance','cos'
	.calls	'get_two_points_distance','pow'
	.calls	'get_two_points_distance','sqrt'
	.calls	'get_two_points_distance','asin'
	.calls	'get_two_points_azimuth','sin'
	.calls	'get_two_points_azimuth','cos'
	.calls	'get_two_points_azimuth','atan2'
	.calls	'gnss_data_parse','strchr'
	.calls	'gnss_data_parse','strncpy'
	.calls	'gnss_data_parse','func_str_to_hex'
	.calls	'gnss_data_parse','gps_gnrmc_parse'
	.calls	'gnss_data_parse','gps_gngga_parse'
	.calls	'gnss_data_parse','gps_gnths_parse'
	.calls	'gnss_uart_callback','fifo_write_buffer'
	.calls	'gnss_uart_callback','uart_query_byte'
	.calls	'gnss_uart_callback','fifo_read_buffer'
	.calls	'gnss_uart_callback','strncmp'
	.calls	'gnss_uart_callback','fifo_used'
	.calls	'gnss_uart_callback','fifo_clear'
	.calls	'gnss_init','fifo_init'
	.calls	'gnss_init','system_delay_ms'
	.calls	'gnss_init','uart_init'
	.calls	'gnss_init','uart_write_buffer'
	.calls	'gnss_init','uart_rx_interrupt'
	.calls	'get_parameter_index','',0
	.calls	'get_int_number','',16
	.calls	'get_float_number','',16
	.calls	'get_double_number','',16
	.calls	'utc_to_btc','',0
	.calls	'gps_gnrmc_parse','',0
	.calls	'gps_gngga_parse','',0
	.calls	'gps_gnths_parse','',0
	.calls	'get_two_points_distance','',16
	.calls	'get_two_points_azimuth','',8
	.calls	'gnss_data_parse','',8
	.calls	'gnss_uart_callback','',16
	.extern	sin
	.extern	cos
	.extern	asin
	.extern	atan2
	.extern	pow
	.extern	sqrt
	.extern	strncpy
	.extern	strchr
	.extern	strncmp
	.extern	func_str_to_int
	.extern	func_str_to_double
	.extern	func_str_to_hex
	.extern	fifo_clear
	.extern	fifo_used
	.extern	fifo_write_buffer
	.extern	fifo_read_buffer
	.extern	fifo_init
	.extern	system_delay_ms
	.extern	uart_write_buffer
	.extern	uart_query_byte
	.extern	uart_rx_interrupt
	.extern	uart_init
	.extern	__d_dtof
	.extern	__d_dtoi
	.extern	__d_itod
	.extern	__d_sub
	.extern	__d_uitod
	.extern	__d_mul
	.extern	__d_div
	.extern	__d_add
	.extern	__d_fgt
	.calls	'gnss_init','',152
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L110:
	.word	103097
	.half	3
	.word	.L111
	.byte	4
.L109:
	.byte	1
	.byte	'../libraries/zf_device/zf_device_gnss.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L112
	.byte	2,1,1,3
	.word	202
	.byte	4
	.byte	'Ifx__jump_and_link',0,3,1,61,17,1,1,5
	.byte	'fun',0,1,61,43
	.word	205
	.byte	6,0,7
	.byte	'__fract',0,4,128,1
.L282:
	.byte	7
	.byte	'float',0,4,4,8
	.byte	'Ifx__float_to_fract',0,3,1,152,2,18
	.word	250
	.byte	1,1,5
	.byte	'a',0,1,152,2,44
	.word	262
	.byte	6,0,4
	.byte	'Ifx__stopPerfCounters',0,3,1,172,2,17,1,1,6,0,7
	.byte	'unsigned long long int',0,8,7,9
	.byte	'void',0,3
	.word	374
	.byte	8
	.byte	'__ld64',0,3,2,135,1,19
	.word	348
	.byte	1,1,5
	.byte	'addr',0,2,135,1,32
	.word	380
	.byte	6,0,4
	.byte	'__st64',0,3,2,143,1,17,1,1,5
	.byte	'addr',0,2,143,1,30
	.word	380
	.byte	5
	.byte	'value',0,2,143,1,43
	.word	348
	.byte	6,0,7
	.byte	'unsigned int',0,4,7
.L275:
	.byte	7
	.byte	'int',0,4,5,7
	.byte	'unsigned int',0,4,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON0_Bits',0,4,241,8,16,4,11
	.byte	'ENDINIT',0,4
	.word	489
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	489
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	489
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	489
	.byte	16,0,2,35,0,0,12,4,247,14,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	505
	.byte	4,2,35,0,0
.L224:
	.byte	7
	.byte	'unsigned char',0,1,8,7
	.byte	'unsigned short int',0,2,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON1_Bits',0,4,250,8,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	2,6,2,35,0,11
	.byte	'IR0',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	641
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	658
	.byte	16,0,2,35,2,0,12,4,255,14,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	680
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_WDTCPU_SR_Bits',0,4,137,9,16,4,11
	.byte	'AE',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	641
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	658
	.byte	16,0,2,35,2,0,12,4,135,15,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	924
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_WDTCPU',0,4,175,15,25,12,13
	.byte	'CON0',0
	.word	601
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	884
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	1115
	.byte	4,2,35,8,0,14
	.word	1155
	.byte	3
	.word	1218
	.byte	4
	.byte	'IfxScuWdt_clearCpuEndinitInline',0,3,3,181,3,17,1,1,5
	.byte	'watchdog',0,3,181,3,65
	.word	1223
	.byte	5
	.byte	'password',0,3,181,3,82
	.word	658
	.byte	6,0,4
	.byte	'IfxScuWdt_setCpuEndinitInline',0,3,3,140,4,17,1,1,5
	.byte	'watchdog',0,3,140,4,63
	.word	1223
	.byte	5
	.byte	'password',0,3,140,4,80
	.word	658
	.byte	6,0,8
	.byte	'IfxScuWdt_getCpuWatchdogPasswordInline',0,3,3,227,3,19
	.word	658
	.byte	1,1,5
	.byte	'watchdog',0,3,227,3,74
	.word	1223
	.byte	6,0,15,6,156,1,9,1,16
	.byte	'IfxCpu_ResourceCpu_0',0,0,16
	.byte	'IfxCpu_ResourceCpu_1',0,1,16
	.byte	'IfxCpu_ResourceCpu_none',0,2,0,8
	.byte	'IfxCpu_getCoreIndex',0,3,5,141,6,31
	.word	1453
	.byte	1,1,6,0,8
	.byte	'IfxCpu_areInterruptsEnabled',0,3,5,139,5,20
	.word	641
	.byte	1,1,6,0,8
	.byte	'IfxCpu_disableInterrupts',0,3,5,147,5,20
	.word	641
	.byte	1,1,17,6,0,0,4
	.byte	'IfxCpu_enableInterrupts',0,3,5,157,5,17,1,1,6,0,4
	.byte	'IfxCpu_forceDisableInterrupts',0,3,5,225,5,17,1,1,6,0,4
	.byte	'IfxCpu_restoreInterrupts',0,3,5,168,7,17,1,1,5
	.byte	'enabled',0,5,168,7,50
	.word	641
	.byte	6,0
.L231:
	.byte	7
	.byte	'unsigned long int',0,4,7,8
	.byte	'IfxCpu_getPerformanceCounter',0,3,5,161,6,19
	.word	1775
	.byte	1,1,5
	.byte	'address',0,5,161,6,55
	.word	658
	.byte	6,0,8
	.byte	'IfxCpu_getPerformanceCounterStickyOverflow',0,3,5,190,6,20
	.word	641
	.byte	1,1,5
	.byte	'address',0,5,190,6,70
	.word	658
	.byte	6,0,4
	.byte	'IfxCpu_updatePerformanceCounter',0,3,5,172,8,17,1,1,5
	.byte	'address',0,5,172,8,56
	.word	1775
	.byte	5
	.byte	'count',0,5,172,8,72
	.word	1775
	.byte	17,6,0,0,10
	.byte	'_Ifx_P_OUT_Bits',0,8,143,3,16,4,11
	.byte	'P0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	641
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	641
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	658
	.byte	16,0,2,35,2,0,12,8,181,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2006
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMR_Bits',0,8,169,2,16,4,11
	.byte	'PS0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	641
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	641
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'PCL0',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	641
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	641
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	641
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	641
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	641
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	641
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	641
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	641
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	641
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	641
	.byte	1,0,2,35,3,0,12,8,133,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2322
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ID_Bits',0,8,110,16,4,11
	.byte	'MODREV',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	658
	.byte	16,0,2,35,2,0,12,8,148,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2893
	.byte	4,2,35,0,0,18,4
	.word	641
	.byte	19,3,0,10
	.byte	'_Ifx_P_IOCR0_Bits',0,8,140,1,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	3,5,2,35,0,11
	.byte	'PC0',0,1
	.word	641
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	641
	.byte	3,5,2,35,1,11
	.byte	'PC1',0,1
	.word	641
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	641
	.byte	3,5,2,35,2,11
	.byte	'PC2',0,1
	.word	641
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	641
	.byte	3,5,2,35,3,11
	.byte	'PC3',0,1
	.word	641
	.byte	5,0,2,35,3,0,12,8,164,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3021
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR4_Bits',0,8,166,1,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	3,5,2,35,0,11
	.byte	'PC4',0,1
	.word	641
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	641
	.byte	3,5,2,35,1,11
	.byte	'PC5',0,1
	.word	641
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	641
	.byte	3,5,2,35,2,11
	.byte	'PC6',0,1
	.word	641
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	641
	.byte	3,5,2,35,3,11
	.byte	'PC7',0,1
	.word	641
	.byte	5,0,2,35,3,0,12,8,180,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3236
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR8_Bits',0,8,179,1,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	3,5,2,35,0,11
	.byte	'PC8',0,1
	.word	641
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	641
	.byte	3,5,2,35,1,11
	.byte	'PC9',0,1
	.word	641
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	641
	.byte	3,5,2,35,2,11
	.byte	'PC10',0,1
	.word	641
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	641
	.byte	3,5,2,35,3,11
	.byte	'PC11',0,1
	.word	641
	.byte	5,0,2,35,3,0,12,8,188,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3451
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR12_Bits',0,8,153,1,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	3,5,2,35,0,11
	.byte	'PC12',0,1
	.word	641
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	641
	.byte	3,5,2,35,1,11
	.byte	'PC13',0,1
	.word	641
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	641
	.byte	3,5,2,35,2,11
	.byte	'PC14',0,1
	.word	641
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	641
	.byte	3,5,2,35,3,11
	.byte	'PC15',0,1
	.word	641
	.byte	5,0,2,35,3,0,12,8,172,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3668
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IN_Bits',0,8,118,16,4,11
	.byte	'P0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	641
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	641
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	658
	.byte	16,0,2,35,2,0,12,8,156,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3888
	.byte	4,2,35,0,0,18,24
	.word	641
	.byte	19,23,0,10
	.byte	'_Ifx_P_PDR0_Bits',0,8,205,3,16,4,11
	.byte	'PD0',0,1
	.word	641
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	641
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'PD2',0,1
	.word	641
	.byte	3,5,2,35,1,11
	.byte	'PL2',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'PD3',0,1
	.word	641
	.byte	3,1,2,35,1,11
	.byte	'PL3',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'PD4',0,1
	.word	641
	.byte	3,5,2,35,2,11
	.byte	'PL4',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'PD5',0,1
	.word	641
	.byte	3,1,2,35,2,11
	.byte	'PL5',0,1
	.word	641
	.byte	1,0,2,35,2,11
	.byte	'PD6',0,1
	.word	641
	.byte	3,5,2,35,3,11
	.byte	'PL6',0,1
	.word	641
	.byte	1,4,2,35,3,11
	.byte	'PD7',0,1
	.word	641
	.byte	3,1,2,35,3,11
	.byte	'PL7',0,1
	.word	641
	.byte	1,0,2,35,3,0,12,8,205,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4211
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PDR1_Bits',0,8,226,3,16,4,11
	.byte	'PD8',0,1
	.word	641
	.byte	3,5,2,35,0,11
	.byte	'PL8',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'PD9',0,1
	.word	641
	.byte	3,1,2,35,0,11
	.byte	'PL9',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'PD10',0,1
	.word	641
	.byte	3,5,2,35,1,11
	.byte	'PL10',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'PD11',0,1
	.word	641
	.byte	3,1,2,35,1,11
	.byte	'PL11',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'PD12',0,1
	.word	641
	.byte	3,5,2,35,2,11
	.byte	'PL12',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'PD13',0,1
	.word	641
	.byte	3,1,2,35,2,11
	.byte	'PL13',0,1
	.word	641
	.byte	1,0,2,35,2,11
	.byte	'PD14',0,1
	.word	641
	.byte	3,5,2,35,3,11
	.byte	'PL14',0,1
	.word	641
	.byte	1,4,2,35,3,11
	.byte	'PD15',0,1
	.word	641
	.byte	3,1,2,35,3,11
	.byte	'PL15',0,1
	.word	641
	.byte	1,0,2,35,3,0,12,8,213,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4515
	.byte	4,2,35,0,0,18,8
	.word	641
	.byte	19,7,0,10
	.byte	'_Ifx_P_ESR_Bits',0,8,88,16,4,11
	.byte	'EN0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	641
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	641
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	658
	.byte	16,0,2,35,2,0,12,8,140,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4840
	.byte	4,2,35,0,0,18,12
	.word	641
	.byte	19,11,0,10
	.byte	'_Ifx_P_PDISC_Bits',0,8,183,3,16,4,11
	.byte	'PDIS0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'PDIS2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'PDIS3',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'PDIS4',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'PDIS5',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'PDIS6',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'PDIS7',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'PDIS8',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'PDIS9',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'PDIS10',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'PDIS11',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'PDIS12',0,1
	.word	641
	.byte	1,3,2,35,1,11
	.byte	'PDIS13',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'PDIS14',0,1
	.word	641
	.byte	1,1,2,35,1,11
	.byte	'PDIS15',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	658
	.byte	16,0,2,35,2,0,12,8,197,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5180
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PCSR_Bits',0,8,165,3,16,4,11
	.byte	'SEL0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'SEL1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'SEL2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'SEL3',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'SEL4',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'SEL5',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'SEL6',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'SEL7',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	641
	.byte	2,6,2,35,1,11
	.byte	'SEL10',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'SEL11',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	466
	.byte	19,1,2,35,0,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,12,8,189,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5546
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR0_Bits',0,8,206,2,16,4,11
	.byte	'PS0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	466
	.byte	28,0,2,35,0,0,12,8,149,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5832
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR4_Bits',0,8,227,2,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	4,4,2,35,0,11
	.byte	'PS4',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	466
	.byte	24,0,2,35,0,0,12,8,165,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5979
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR8_Bits',0,8,238,2,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'PS8',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	466
	.byte	20,0,2,35,0,0,12,8,173,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6148
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR12_Bits',0,8,216,2,16,4,11
	.byte	'reserved_0',0,2
	.word	658
	.byte	12,4,2,35,0,11
	.byte	'PS12',0,1
	.word	641
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	641
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	658
	.byte	16,0,2,35,2,0,12,8,157,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6320
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR0_Bits',0,8,232,1,16,4,11
	.byte	'reserved_0',0,2
	.word	658
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	658
	.byte	12,0,2,35,2,0,12,8,229,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6495
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR4_Bits',0,8,253,1,16,4,11
	.byte	'reserved_0',0,4
	.word	466
	.byte	20,12,2,35,0,11
	.byte	'PCL4',0,1
	.word	641
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	641
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	641
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	641
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	641
	.byte	8,0,2,35,3,0,12,8,245,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6669
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR8_Bits',0,8,136,2,16,4,11
	.byte	'reserved_0',0,4
	.word	466
	.byte	24,8,2,35,0,11
	.byte	'PCL8',0,1
	.word	641
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	641
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	641
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	641
	.byte	4,0,2,35,3,0,12,8,253,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6843
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR12_Bits',0,8,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	466
	.byte	28,4,2,35,0,11
	.byte	'PCL12',0,1
	.word	641
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	641
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	641
	.byte	1,0,2,35,3,0,12,8,237,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7019
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR_Bits',0,8,249,2,16,4,11
	.byte	'PS0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	641
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	641
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	658
	.byte	16,0,2,35,2,0,12,8,141,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7175
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR_Bits',0,8,147,2,16,4,11
	.byte	'reserved_0',0,2
	.word	658
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	641
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	641
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	641
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	641
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	641
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	641
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	641
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	641
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	641
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	641
	.byte	1,0,2,35,3,0,12,8,221,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7508
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR0_Bits',0,8,192,1,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	466
	.byte	30,0,2,35,0,0,12,8,196,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7856
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_Bits',0,8,200,1,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	466
	.byte	30,0,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_P21_Bits',0,8,208,1,16,4,11
	.byte	'RDIS_CTRL',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'RX_DIS',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'TERM',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'LRXTERM',0,1
	.word	641
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	466
	.byte	24,0,2,35,0,0,12,8,204,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7980
	.byte	4,2,35,0,13
	.byte	'B_P21',0
	.word	8064
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR2_Bits',0,8,218,1,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'LVDSR',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'LVDSRL',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	641
	.byte	2,4,2,35,1,11
	.byte	'TDIS_CTRL',0,1
	.word	641
	.byte	1,3,2,35,1,11
	.byte	'TX_DIS',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'TX_PD',0,1
	.word	641
	.byte	1,1,2,35,1,11
	.byte	'TX_PWDPD',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	658
	.byte	16,0,2,35,2,0,12,8,213,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8244
	.byte	4,2,35,0,0,18,76
	.word	641
	.byte	19,75,0,10
	.byte	'_Ifx_P_ACCEN1_Bits',0,8,82,16,4,11
	.byte	'reserved_0',0,4
	.word	466
	.byte	32,0,2,35,0,0,12,8,132,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8497
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ACCEN0_Bits',0,8,45,16,4,11
	.byte	'EN0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	641
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	641
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	641
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	641
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	641
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	641
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	641
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	641
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	641
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	641
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	641
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	641
	.byte	1,0,2,35,3,0,12,8,252,3,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8584
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P',0,8,229,5,25,128,2,13
	.byte	'OUT',0
	.word	2282
	.byte	4,2,35,0,13
	.byte	'OMR',0
	.word	2853
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	2972
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	3012
	.byte	4,2,35,12,13
	.byte	'IOCR0',0
	.word	3196
	.byte	4,2,35,16,13
	.byte	'IOCR4',0
	.word	3411
	.byte	4,2,35,20,13
	.byte	'IOCR8',0
	.word	3628
	.byte	4,2,35,24,13
	.byte	'IOCR12',0
	.word	3848
	.byte	4,2,35,28,13
	.byte	'reserved_20',0
	.word	3012
	.byte	4,2,35,32,13
	.byte	'IN',0
	.word	4162
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	4202
	.byte	24,2,35,40,13
	.byte	'PDR0',0
	.word	4475
	.byte	4,2,35,64,13
	.byte	'PDR1',0
	.word	4791
	.byte	4,2,35,68,13
	.byte	'reserved_48',0
	.word	4831
	.byte	8,2,35,72,13
	.byte	'ESR',0
	.word	5131
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	5171
	.byte	12,2,35,84,13
	.byte	'PDISC',0
	.word	5506
	.byte	4,2,35,96,13
	.byte	'PCSR',0
	.word	5792
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	4831
	.byte	8,2,35,104,13
	.byte	'OMSR0',0
	.word	5939
	.byte	4,2,35,112,13
	.byte	'OMSR4',0
	.word	6108
	.byte	4,2,35,116,13
	.byte	'OMSR8',0
	.word	6280
	.byte	4,2,35,120,13
	.byte	'OMSR12',0
	.word	6455
	.byte	4,2,35,124,13
	.byte	'OMCR0',0
	.word	6629
	.byte	4,3,35,128,1,13
	.byte	'OMCR4',0
	.word	6803
	.byte	4,3,35,132,1,13
	.byte	'OMCR8',0
	.word	6979
	.byte	4,3,35,136,1,13
	.byte	'OMCR12',0
	.word	7135
	.byte	4,3,35,140,1,13
	.byte	'OMSR',0
	.word	7468
	.byte	4,3,35,144,1,13
	.byte	'OMCR',0
	.word	7816
	.byte	4,3,35,148,1,13
	.byte	'reserved_98',0
	.word	4831
	.byte	8,3,35,152,1,13
	.byte	'LPCR0',0
	.word	7940
	.byte	4,3,35,160,1,13
	.byte	'LPCR1',0
	.word	8189
	.byte	4,3,35,164,1,13
	.byte	'LPCR2',0
	.word	8448
	.byte	4,3,35,168,1,13
	.byte	'reserved_A4',0
	.word	8488
	.byte	76,3,35,172,1,13
	.byte	'ACCEN1',0
	.word	8544
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	9111
	.byte	4,3,35,252,1,0,14
	.word	9151
	.byte	3
	.word	9754
	.byte	15,7,83,9,1,16
	.byte	'IfxPort_InputMode_undefined',0,127,16
	.byte	'IfxPort_InputMode_noPullDevice',0,0,16
	.byte	'IfxPort_InputMode_pullDown',0,8,16
	.byte	'IfxPort_InputMode_pullUp',0,16,0,4
	.byte	'IfxPort_setPinModeInput',0,3,7,196,4,17,1,1,5
	.byte	'port',0,7,196,4,48
	.word	9759
	.byte	5
	.byte	'pinIndex',0,7,196,4,60
	.word	641
	.byte	5
	.byte	'mode',0,7,196,4,88
	.word	9764
	.byte	6,0,15,7,134,1,9,1,16
	.byte	'IfxPort_OutputMode_pushPull',0,128,1,16
	.byte	'IfxPort_OutputMode_openDrain',0,192,1,0,15,7,120,9,1,16
	.byte	'IfxPort_OutputIdx_general',0,128,1,16
	.byte	'IfxPort_OutputIdx_alt1',0,136,1,16
	.byte	'IfxPort_OutputIdx_alt2',0,144,1,16
	.byte	'IfxPort_OutputIdx_alt3',0,152,1,16
	.byte	'IfxPort_OutputIdx_alt4',0,160,1,16
	.byte	'IfxPort_OutputIdx_alt5',0,168,1,16
	.byte	'IfxPort_OutputIdx_alt6',0,176,1,16
	.byte	'IfxPort_OutputIdx_alt7',0,184,1,0,4
	.byte	'IfxPort_setPinModeOutput',0,3,7,202,4,17,1,1,5
	.byte	'port',0,7,202,4,49
	.word	9759
	.byte	5
	.byte	'pinIndex',0,7,202,4,61
	.word	641
	.byte	5
	.byte	'mode',0,7,202,4,90
	.word	9969
	.byte	5
	.byte	'index',0,7,202,4,114
	.word	10039
	.byte	6,0,15,7,172,1,9,4,16
	.byte	'IfxPort_State_notChanged',0,0,16
	.byte	'IfxPort_State_high',0,1,16
	.byte	'IfxPort_State_low',0,128,128,4,16
	.byte	'IfxPort_State_toggled',0,129,128,4,0,4
	.byte	'IfxPort_setPinState',0,3,7,208,4,17,1,1,5
	.byte	'port',0,7,208,4,44
	.word	9759
	.byte	5
	.byte	'pinIndex',0,7,208,4,56
	.word	641
	.byte	5
	.byte	'action',0,7,208,4,80
	.word	10352
	.byte	6,0,8
	.byte	'IfxScuCcu_getStmFrequency',0,3,9,226,8,20
	.word	262
	.byte	1,1,6,0,10
	.byte	'_Ifx_SRC_SRCR_Bits',0,11,45,16,4,11
	.byte	'SRPN',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	641
	.byte	2,6,2,35,1,11
	.byte	'SRE',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'TOS',0,1
	.word	641
	.byte	2,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	641
	.byte	3,0,2,35,1,11
	.byte	'ECC',0,1
	.word	641
	.byte	6,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	641
	.byte	2,0,2,35,2,11
	.byte	'SRR',0,1
	.word	641
	.byte	1,7,2,35,3,11
	.byte	'CLRR',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'SETR',0,1
	.word	641
	.byte	1,5,2,35,3,11
	.byte	'IOV',0,1
	.word	641
	.byte	1,4,2,35,3,11
	.byte	'IOVCLR',0,1
	.word	641
	.byte	1,3,2,35,3,11
	.byte	'SWS',0,1
	.word	641
	.byte	1,2,2,35,3,11
	.byte	'SWSCLR',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	641
	.byte	1,0,2,35,3,0,12,11,70,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10573
	.byte	4,2,35,0,0,14
	.word	10863
	.byte	3
	.word	10902
	.byte	4
	.byte	'IfxSrc_clearRequest',0,3,10,250,1,17,1,1,5
	.byte	'src',0,10,250,1,60
	.word	10907
	.byte	6,0,10
	.byte	'_Ifx_ASCLIN_CLC_Bits',0,13,118,16,4,11
	.byte	'DISR',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'DISS',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'EDIS',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	466
	.byte	28,0,2,35,0,0,12,13,207,3,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10955
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_IOCR_Bits',0,13,169,2,16,4,11
	.byte	'ALTI',0,1
	.word	641
	.byte	3,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'DEPTH',0,2
	.word	658
	.byte	6,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	641
	.byte	6,0,2,35,1,11
	.byte	'CTS',0,1
	.word	641
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	658
	.byte	7,7,2,35,2,11
	.byte	'RCPOL',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'CPOL',0,1
	.word	641
	.byte	1,5,2,35,3,11
	.byte	'SPOL',0,1
	.word	641
	.byte	1,4,2,35,3,11
	.byte	'LB',0,1
	.word	641
	.byte	1,3,2,35,3,11
	.byte	'CTSEN',0,1
	.word	641
	.byte	1,2,2,35,3,11
	.byte	'RXM',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'TXM',0,1
	.word	641
	.byte	1,0,2,35,3,0,12,13,151,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11114
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_ID_Bits',0,13,161,2,16,4,11
	.byte	'MODREV',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	658
	.byte	16,0,2,35,2,0,12,13,143,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11409
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_TXFIFOCON_Bits',0,13,149,3,16,4,11
	.byte	'FLUSH',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'ENO',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	641
	.byte	4,2,2,35,0,11
	.byte	'INW',0,1
	.word	641
	.byte	2,0,2,35,0,11
	.byte	'INTLEVEL',0,1
	.word	641
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	641
	.byte	4,0,2,35,1,11
	.byte	'FILL',0,1
	.word	641
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,2
	.word	658
	.byte	11,0,2,35,2,0,12,13,247,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11534
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_RXFIFOCON_Bits',0,13,129,3,16,4,11
	.byte	'FLUSH',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'ENI',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	641
	.byte	4,2,2,35,0,11
	.byte	'OUTW',0,1
	.word	641
	.byte	2,0,2,35,0,11
	.byte	'INTLEVEL',0,1
	.word	641
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	641
	.byte	4,0,2,35,1,11
	.byte	'FILL',0,1
	.word	641
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,2
	.word	658
	.byte	10,1,2,35,2,11
	.byte	'BUF',0,1
	.word	641
	.byte	1,0,2,35,3,0,12,13,231,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11759
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_BITCON_Bits',0,13,88,16,4,11
	.byte	'PRESCALER',0,2
	.word	658
	.byte	12,4,2,35,0,11
	.byte	'reserved_12',0,1
	.word	641
	.byte	4,0,2,35,1,11
	.byte	'OVERSAMPLING',0,1
	.word	641
	.byte	4,4,2,35,2,11
	.byte	'reserved_20',0,1
	.word	641
	.byte	4,0,2,35,2,11
	.byte	'SAMPLEPOINT',0,1
	.word	641
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	641
	.byte	3,1,2,35,3,11
	.byte	'SM',0,1
	.word	641
	.byte	1,0,2,35,3,0,12,13,183,3,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12000
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_FRAMECON_Bits',0,13,145,2,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	6,2,2,35,0,11
	.byte	'IDLE',0,2
	.word	658
	.byte	3,7,2,35,0,11
	.byte	'STOP',0,1
	.word	641
	.byte	3,4,2,35,1,11
	.byte	'LEAD',0,1
	.word	641
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'MODE',0,1
	.word	641
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	658
	.byte	10,4,2,35,2,11
	.byte	'MSB',0,1
	.word	641
	.byte	1,3,2,35,3,11
	.byte	'CEN',0,1
	.word	641
	.byte	1,2,2,35,3,11
	.byte	'PEN',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'ODD',0,1
	.word	641
	.byte	1,0,2,35,3,0,12,13,135,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12221
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_DATCON_Bits',0,13,136,1,16,4,11
	.byte	'DATLEN',0,1
	.word	641
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	658
	.byte	9,3,2,35,0,11
	.byte	'HO',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'RM',0,1
	.word	641
	.byte	1,1,2,35,1,11
	.byte	'CSM',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'RESPONSE',0,1
	.word	641
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	641
	.byte	8,0,2,35,3,0,12,13,223,3,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12486
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_BRG_Bits',0,13,109,16,4,11
	.byte	'DENOMINATOR',0,2
	.word	658
	.byte	12,4,2,35,0,11
	.byte	'reserved_12',0,1
	.word	641
	.byte	4,0,2,35,1,11
	.byte	'NUMERATOR',0,2
	.word	658
	.byte	12,4,2,35,2,11
	.byte	'reserved_28',0,1
	.word	641
	.byte	4,0,2,35,3,0,12,13,199,3,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12683
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_BRD_Bits',0,13,100,16,4,11
	.byte	'LOWERLIMIT',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'UPPERLIMIT',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'MEASURED',0,2
	.word	658
	.byte	12,4,2,35,2,11
	.byte	'reserved_28',0,1
	.word	641
	.byte	4,0,2,35,3,0,12,13,191,3,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12840
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_LIN_CON_Bits',0,13,216,2,16,4,11
	.byte	'reserved_0',0,4
	.word	466
	.byte	23,9,2,35,0,11
	.byte	'CSI',0,1
	.word	641
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	641
	.byte	1,7,2,35,3,11
	.byte	'CSEN',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'MS',0,1
	.word	641
	.byte	1,5,2,35,3,11
	.byte	'ABD',0,1
	.word	641
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	641
	.byte	4,0,2,35,3,0,12,13,191,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12994
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_LIN_BTIMER_Bits',0,13,209,2,16,4,11
	.byte	'BREAK',0,1
	.word	641
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	466
	.byte	26,0,2,35,0,0,12,13,183,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13194
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_LIN_HTIMER_Bits',0,13,228,2,16,4,11
	.byte	'HEADER',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	466
	.byte	24,0,2,35,0,0,12,13,199,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13308
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_LIN',0,13,135,5,25,12,13
	.byte	'CON',0
	.word	13154
	.byte	4,2,35,0,13
	.byte	'BTIMER',0
	.word	13268
	.byte	4,2,35,4,13
	.byte	'HTIMER',0
	.word	13383
	.byte	4,2,35,8,0,14
	.word	13423
	.byte	10
	.byte	'_Ifx_ASCLIN_FLAGS_Bits',0,13,148,1,16,4,11
	.byte	'TH',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'TR',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'RH',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'RR',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'FED',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'RED',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	658
	.byte	6,3,2,35,0,11
	.byte	'TWRQ',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'THRQ',0,1
	.word	641
	.byte	1,1,2,35,1,11
	.byte	'TRRQ',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'PE',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'TC',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'FE',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'HT',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'RT',0,1
	.word	641
	.byte	1,3,2,35,2,11
	.byte	'BD',0,1
	.word	641
	.byte	1,2,2,35,2,11
	.byte	'LP',0,1
	.word	641
	.byte	1,1,2,35,2,11
	.byte	'LA',0,1
	.word	641
	.byte	1,0,2,35,2,11
	.byte	'LC',0,1
	.word	641
	.byte	1,7,2,35,3,11
	.byte	'CE',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'RFO',0,1
	.word	641
	.byte	1,5,2,35,3,11
	.byte	'RFU',0,1
	.word	641
	.byte	1,4,2,35,3,11
	.byte	'RFL',0,1
	.word	641
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	641
	.byte	1,2,2,35,3,11
	.byte	'TFO',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'TFL',0,1
	.word	641
	.byte	1,0,2,35,3,0,12,13,231,3,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13496
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_FLAGSSET_Bits',0,13,241,1,16,4,11
	.byte	'THS',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'TRS',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'RHS',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'RRS',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'FEDS',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'REDS',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	658
	.byte	6,3,2,35,0,11
	.byte	'TWRQS',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'THRQS',0,1
	.word	641
	.byte	1,1,2,35,1,11
	.byte	'TRRQS',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'PES',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'TCS',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'FES',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'HTS',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'RTS',0,1
	.word	641
	.byte	1,3,2,35,2,11
	.byte	'BDS',0,1
	.word	641
	.byte	1,2,2,35,2,11
	.byte	'LPS',0,1
	.word	641
	.byte	1,1,2,35,2,11
	.byte	'LAS',0,1
	.word	641
	.byte	1,0,2,35,2,11
	.byte	'LCS',0,1
	.word	641
	.byte	1,7,2,35,3,11
	.byte	'CES',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'RFOS',0,1
	.word	641
	.byte	1,5,2,35,3,11
	.byte	'RFUS',0,1
	.word	641
	.byte	1,4,2,35,3,11
	.byte	'RFLS',0,1
	.word	641
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	641
	.byte	1,2,2,35,3,11
	.byte	'TFOS',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'TFLS',0,1
	.word	641
	.byte	1,0,2,35,3,0,12,13,255,3,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13982
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_FLAGSCLEAR_Bits',0,13,180,1,16,4,11
	.byte	'THC',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'TRC',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'RHC',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'RRC',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'FEDC',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'REDC',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	658
	.byte	6,3,2,35,0,11
	.byte	'TWRQC',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'THRQC',0,1
	.word	641
	.byte	1,1,2,35,1,11
	.byte	'TRRQC',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'PEC',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'TCC',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'FEC',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'HTC',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'RTC',0,1
	.word	641
	.byte	1,3,2,35,2,11
	.byte	'BDC',0,1
	.word	641
	.byte	1,2,2,35,2,11
	.byte	'LPC',0,1
	.word	641
	.byte	1,1,2,35,2,11
	.byte	'LAC',0,1
	.word	641
	.byte	1,0,2,35,2,11
	.byte	'LCC',0,1
	.word	641
	.byte	1,7,2,35,3,11
	.byte	'CEC',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'RFOC',0,1
	.word	641
	.byte	1,5,2,35,3,11
	.byte	'RFUC',0,1
	.word	641
	.byte	1,4,2,35,3,11
	.byte	'RFLC',0,1
	.word	641
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	641
	.byte	1,2,2,35,3,11
	.byte	'TFOC',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'TFLC',0,1
	.word	641
	.byte	1,0,2,35,3,0,12,13,239,3,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14495
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_FLAGSENABLE_Bits',0,13,212,1,16,4,11
	.byte	'THE',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'TRE',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'RHE',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'RRE',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'FEDE',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'REDE',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	658
	.byte	9,0,2,35,0,11
	.byte	'PEE',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'TCE',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'FEE',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'HTE',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'RTE',0,1
	.word	641
	.byte	1,3,2,35,2,11
	.byte	'BDE',0,1
	.word	641
	.byte	1,2,2,35,2,11
	.byte	'LPE',0,1
	.word	641
	.byte	1,1,2,35,2,11
	.byte	'ABE',0,1
	.word	641
	.byte	1,0,2,35,2,11
	.byte	'LCE',0,1
	.word	641
	.byte	1,7,2,35,3,11
	.byte	'CEE',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'RFOE',0,1
	.word	641
	.byte	1,5,2,35,3,11
	.byte	'RFUE',0,1
	.word	641
	.byte	1,4,2,35,3,11
	.byte	'RFLE',0,1
	.word	641
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	641
	.byte	1,2,2,35,3,11
	.byte	'TFOE',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'TFLE',0,1
	.word	641
	.byte	1,0,2,35,3,0,12,13,247,3,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15010
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_TXDATA_Bits',0,13,143,3,16,4,11
	.byte	'DATA',0,4
	.word	466
	.byte	32,0,2,35,0,0,12,13,239,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15475
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_RXDATA_Bits',0,13,245,2,16,4,11
	.byte	'DATA',0,4
	.word	466
	.byte	32,0,2,35,0,0,12,13,215,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15562
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_CSR_Bits',0,13,128,1,16,4,11
	.byte	'CLKSEL',0,1
	.word	641
	.byte	5,3,2,35,0,11
	.byte	'reserved_5',0,4
	.word	466
	.byte	26,1,2,35,0,11
	.byte	'CON',0,1
	.word	641
	.byte	1,0,2,35,3,0,12,13,215,3,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15649
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_RXDATAD_Bits',0,13,251,2,16,4,11
	.byte	'DATA',0,4
	.word	466
	.byte	32,0,2,35,0,0,12,13,223,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15772
	.byte	4,2,35,0,0,18,148,1
	.word	641
	.byte	19,147,1,0,10
	.byte	'_Ifx_ASCLIN_OCS_Bits',0,13,235,2,16,4,11
	.byte	'reserved_0',0,4
	.word	466
	.byte	24,8,2,35,0,11
	.byte	'SUS',0,1
	.word	641
	.byte	4,4,2,35,3,11
	.byte	'SUS_P',0,1
	.word	641
	.byte	1,3,2,35,3,11
	.byte	'SUSSTA',0,1
	.word	641
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	641
	.byte	2,0,2,35,3,0,12,13,207,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15871
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_KRSTCLR_Bits',0,13,202,2,16,4,11
	.byte	'CLR',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	466
	.byte	31,0,2,35,0,0,12,13,175,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16034
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_KRST1_Bits',0,13,195,2,16,4,11
	.byte	'RST',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	466
	.byte	31,0,2,35,0,0,12,13,167,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16143
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_KRST0_Bits',0,13,187,2,16,4,11
	.byte	'RST',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'RSTSTAT',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	466
	.byte	30,0,2,35,0,0,12,13,159,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16250
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_ACCEN1_Bits',0,13,82,16,4,11
	.byte	'reserved_0',0,4
	.word	466
	.byte	32,0,2,35,0,0,12,13,175,3,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16376
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_ACCEN0_Bits',0,13,45,16,4,11
	.byte	'EN0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	641
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	641
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	641
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	641
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	641
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	641
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	641
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	641
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	641
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	641
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	641
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	641
	.byte	1,0,2,35,3,0,12,13,167,3,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16468
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN',0,13,153,5,25,128,2,13
	.byte	'CLC',0
	.word	11074
	.byte	4,2,35,0,13
	.byte	'IOCR',0
	.word	11369
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	11494
	.byte	4,2,35,8,13
	.byte	'TXFIFOCON',0
	.word	11719
	.byte	4,2,35,12,13
	.byte	'RXFIFOCON',0
	.word	11960
	.byte	4,2,35,16,13
	.byte	'BITCON',0
	.word	12181
	.byte	4,2,35,20,13
	.byte	'FRAMECON',0
	.word	12446
	.byte	4,2,35,24,13
	.byte	'DATCON',0
	.word	12643
	.byte	4,2,35,28,13
	.byte	'BRG',0
	.word	12800
	.byte	4,2,35,32,13
	.byte	'BRD',0
	.word	12954
	.byte	4,2,35,36,13
	.byte	'LIN',0
	.word	13491
	.byte	12,2,35,40,13
	.byte	'FLAGS',0
	.word	13942
	.byte	4,2,35,52,13
	.byte	'FLAGSSET',0
	.word	14455
	.byte	4,2,35,56,13
	.byte	'FLAGSCLEAR',0
	.word	14970
	.byte	4,2,35,60,13
	.byte	'FLAGSENABLE',0
	.word	15435
	.byte	4,2,35,64,13
	.byte	'TXDATA',0
	.word	15522
	.byte	4,2,35,68,13
	.byte	'RXDATA',0
	.word	15609
	.byte	4,2,35,72,13
	.byte	'CSR',0
	.word	15732
	.byte	4,2,35,76,13
	.byte	'RXDATAD',0
	.word	15820
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	15860
	.byte	148,1,2,35,84,13
	.byte	'OCS',0
	.word	15994
	.byte	4,3,35,232,1,13
	.byte	'KRSTCLR',0
	.word	16103
	.byte	4,3,35,236,1,13
	.byte	'KRST1',0
	.word	16210
	.byte	4,3,35,240,1,13
	.byte	'KRST0',0
	.word	16336
	.byte	4,3,35,244,1,13
	.byte	'ACCEN1',0
	.word	16428
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	17000
	.byte	4,3,35,252,1,0,14
	.word	17040
	.byte	3
	.word	17482
	.byte	4
	.byte	'IfxAsclin_enableCts',0,3,12,228,13,17,1,1,5
	.byte	'asclin',0,12,228,13,49
	.word	17487
	.byte	5
	.byte	'enable',0,12,228,13,65
	.word	641
	.byte	6,0,15,12,123,9,1,16
	.byte	'IfxAsclin_CtsInputSelect_0',0,0,16
	.byte	'IfxAsclin_CtsInputSelect_1',0,1,16
	.byte	'IfxAsclin_CtsInputSelect_2',0,2,16
	.byte	'IfxAsclin_CtsInputSelect_3',0,3,0,4
	.byte	'IfxAsclin_setCtsInput',0,3,12,169,17,17,1,1,5
	.byte	'asclin',0,12,169,17,51
	.word	17487
	.byte	5
	.byte	'ctsi',0,12,169,17,84
	.word	17554
	.byte	6,0,15,12,181,2,9,1,16
	.byte	'IfxAsclin_RxInputSelect_0',0,0,16
	.byte	'IfxAsclin_RxInputSelect_1',0,1,16
	.byte	'IfxAsclin_RxInputSelect_2',0,2,16
	.byte	'IfxAsclin_RxInputSelect_3',0,3,16
	.byte	'IfxAsclin_RxInputSelect_4',0,4,16
	.byte	'IfxAsclin_RxInputSelect_5',0,5,16
	.byte	'IfxAsclin_RxInputSelect_6',0,6,16
	.byte	'IfxAsclin_RxInputSelect_7',0,7,0,4
	.byte	'IfxAsclin_setRxInput',0,3,12,191,18,17,1,1,5
	.byte	'asclin',0,12,191,18,50
	.word	17487
	.byte	5
	.byte	'alti',0,12,191,18,82
	.word	17738
	.byte	6,0,7
	.byte	'short int',0,2,5,7
	.byte	'long int',0,4,5,20,14,60,9,12,13
	.byte	'count',0
	.word	18030
	.byte	2,2,35,0,13
	.byte	'readerWaitx',0
	.word	18043
	.byte	4,2,35,2,13
	.byte	'writerWaitx',0
	.word	18043
	.byte	4,2,35,6,13
	.byte	'maxcount',0
	.word	18030
	.byte	2,2,35,10,0,14
	.word	641
	.byte	14
	.word	641
	.byte	10
	.byte	'_Fifo',0,14,73,16,28,13
	.byte	'buffer',0
	.word	380
	.byte	4,2,35,0,13
	.byte	'shared',0
	.word	18055
	.byte	12,2,35,4,13
	.byte	'startIndex',0
	.word	18030
	.byte	2,2,35,16,13
	.byte	'endIndex',0
	.word	18030
	.byte	2,2,35,18,13
	.byte	'size',0
	.word	18030
	.byte	2,2,35,20,13
	.byte	'elementSize',0
	.word	18030
	.byte	2,2,35,22,13
	.byte	'eventReader',0
	.word	18136
	.byte	1,2,35,24,13
	.byte	'eventWriter',0
	.word	18141
	.byte	1,2,35,25,0,3
	.word	18146
	.byte	8
	.byte	'Ifx_Fifo_readCount',0,3,14,206,1,22
	.word	18030
	.byte	1,1,5
	.byte	'fifo',0,14,206,1,51
	.word	18305
	.byte	6,0,10
	.byte	'_Ifx_STM_CLC_Bits',0,16,100,16,4,11
	.byte	'DISR',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'DISS',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'EDIS',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	466
	.byte	28,0,2,35,0,0,12,16,149,2,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18357
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_ID_Bits',0,16,142,1,16,4,11
	.byte	'MODREV',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	658
	.byte	16,0,2,35,2,0,12,16,181,2,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18513
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM0_Bits',0,16,192,1,16,4,11
	.byte	'STM31_0',0,4
	.word	466
	.byte	32,0,2,35,0,0,12,16,229,2,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18635
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM1_Bits',0,16,204,1,16,4,11
	.byte	'STM35_4',0,4
	.word	466
	.byte	32,0,2,35,0,0,12,16,245,2,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18720
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM2_Bits',0,16,210,1,16,4,11
	.byte	'STM39_8',0,4
	.word	466
	.byte	32,0,2,35,0,0,12,16,253,2,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18805
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM3_Bits',0,16,216,1,16,4,11
	.byte	'STM43_12',0,4
	.word	466
	.byte	32,0,2,35,0,0,12,16,133,3,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18890
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM4_Bits',0,16,222,1,16,4,11
	.byte	'STM47_16',0,4
	.word	466
	.byte	32,0,2,35,0,0,12,16,141,3,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18976
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM5_Bits',0,16,228,1,16,4,11
	.byte	'STM51_20',0,4
	.word	466
	.byte	32,0,2,35,0,0,12,16,149,3,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19062
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM6_Bits',0,16,234,1,16,4,11
	.byte	'STM63_32',0,4
	.word	466
	.byte	32,0,2,35,0,0,12,16,157,3,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19148
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_CAP_Bits',0,16,88,16,4,11
	.byte	'STMCAP63_32',0,4
	.word	466
	.byte	32,0,2,35,0,0,12,16,133,2,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19234
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_CMP_Bits',0,16,123,16,4,11
	.byte	'CMPVAL',0,4
	.word	466
	.byte	32,0,2,35,0,0,12,16,165,2,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19321
	.byte	4,2,35,0,0,18,8
	.word	19363
	.byte	19,1,0,10
	.byte	'_Ifx_STM_CMCON_Bits',0,16,110,16,4,11
	.byte	'MSIZE0',0,1
	.word	641
	.byte	5,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	641
	.byte	3,0,2,35,0,11
	.byte	'MSTART0',0,1
	.word	641
	.byte	5,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	641
	.byte	3,0,2,35,1,11
	.byte	'MSIZE1',0,1
	.word	641
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	641
	.byte	3,0,2,35,2,11
	.byte	'MSTART1',0,1
	.word	641
	.byte	5,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	641
	.byte	3,0,2,35,3,0,12,16,157,2,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19412
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_ICR_Bits',0,16,129,1,16,4,11
	.byte	'CMP0EN',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'CMP0IR',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'CMP0OS',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'CMP1EN',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'CMP1IR',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'CMP1OS',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,4
	.word	466
	.byte	25,0,2,35,0,0,12,16,173,2,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19643
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_ISCR_Bits',0,16,150,1,16,4,11
	.byte	'CMP0IRR',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'CMP0IRS',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'CMP1IRR',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'CMP1IRS',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	466
	.byte	28,0,2,35,0,0,12,16,189,2,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19860
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM0SV_Bits',0,16,198,1,16,4,11
	.byte	'STM31_0',0,4
	.word	466
	.byte	32,0,2,35,0,0,12,16,237,2,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20024
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_CAPSV_Bits',0,16,94,16,4,11
	.byte	'STMCAP63_32',0,4
	.word	466
	.byte	32,0,2,35,0,0,12,16,141,2,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20111
	.byte	4,2,35,0,0,18,144,1
	.word	641
	.byte	19,143,1,0,10
	.byte	'_Ifx_STM_OCS_Bits',0,16,182,1,16,4,11
	.byte	'reserved_0',0,4
	.word	466
	.byte	24,8,2,35,0,11
	.byte	'SUS',0,1
	.word	641
	.byte	4,4,2,35,3,11
	.byte	'SUS_P',0,1
	.word	641
	.byte	1,3,2,35,3,11
	.byte	'SUSSTA',0,1
	.word	641
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	641
	.byte	2,0,2,35,3,0,12,16,221,2,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20211
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_KRSTCLR_Bits',0,16,175,1,16,4,11
	.byte	'CLR',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	466
	.byte	31,0,2,35,0,0,12,16,213,2,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20371
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_KRST1_Bits',0,16,168,1,16,4,11
	.byte	'RST',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	466
	.byte	31,0,2,35,0,0,12,16,205,2,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20477
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_KRST0_Bits',0,16,160,1,16,4,11
	.byte	'RST',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'RSTSTAT',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	466
	.byte	30,0,2,35,0,0,12,16,197,2,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20581
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_ACCEN1_Bits',0,16,82,16,4,11
	.byte	'reserved_0',0,4
	.word	466
	.byte	32,0,2,35,0,0,12,16,253,1,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20704
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_ACCEN0_Bits',0,16,45,16,4,11
	.byte	'EN0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	641
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	641
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	641
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	641
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	641
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	641
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	641
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	641
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	641
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	641
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	641
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	641
	.byte	1,0,2,35,3,0,12,16,245,1,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20793
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM',0,16,173,3,25,128,2,13
	.byte	'CLC',0
	.word	18473
	.byte	4,2,35,0,13
	.byte	'reserved_4',0
	.word	3012
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	18595
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	3012
	.byte	4,2,35,12,13
	.byte	'TIM0',0
	.word	18680
	.byte	4,2,35,16,13
	.byte	'TIM1',0
	.word	18765
	.byte	4,2,35,20,13
	.byte	'TIM2',0
	.word	18850
	.byte	4,2,35,24,13
	.byte	'TIM3',0
	.word	18936
	.byte	4,2,35,28,13
	.byte	'TIM4',0
	.word	19022
	.byte	4,2,35,32,13
	.byte	'TIM5',0
	.word	19108
	.byte	4,2,35,36,13
	.byte	'TIM6',0
	.word	19194
	.byte	4,2,35,40,13
	.byte	'CAP',0
	.word	19281
	.byte	4,2,35,44,13
	.byte	'CMP',0
	.word	19403
	.byte	8,2,35,48,13
	.byte	'CMCON',0
	.word	19603
	.byte	4,2,35,56,13
	.byte	'ICR',0
	.word	19820
	.byte	4,2,35,60,13
	.byte	'ISCR',0
	.word	19984
	.byte	4,2,35,64,13
	.byte	'reserved_44',0
	.word	5171
	.byte	12,2,35,68,13
	.byte	'TIM0SV',0
	.word	20071
	.byte	4,2,35,80,13
	.byte	'CAPSV',0
	.word	20160
	.byte	4,2,35,84,13
	.byte	'reserved_58',0
	.word	20200
	.byte	144,1,2,35,88,13
	.byte	'OCS',0
	.word	20331
	.byte	4,3,35,232,1,13
	.byte	'KRSTCLR',0
	.word	20437
	.byte	4,3,35,236,1,13
	.byte	'KRST1',0
	.word	20541
	.byte	4,3,35,240,1,13
	.byte	'KRST0',0
	.word	20664
	.byte	4,3,35,244,1,13
	.byte	'ACCEN1',0
	.word	20753
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	21322
	.byte	4,3,35,252,1,0,14
	.word	21362
	.byte	3
	.word	21782
	.byte	8
	.byte	'IfxStm_get',0,3,15,162,4,19
	.word	348
	.byte	1,1,5
	.byte	'stm',0,15,162,4,39
	.word	21787
	.byte	6,0,8
	.byte	'IfxStm_getFrequency',0,3,15,179,4,20
	.word	262
	.byte	1,1,5
	.byte	'stm',0,15,179,4,49
	.word	21787
	.byte	17,6,6,0,0,8
	.byte	'IfxStm_getLower',0,3,15,190,4,19
	.word	1775
	.byte	1,1,5
	.byte	'stm',0,15,190,4,44
	.word	21787
	.byte	6,0,8
	.byte	'disableInterrupts',0,3,17,108,20
	.word	641
	.byte	1,1,17,6,0,0,4
	.byte	'restoreInterrupts',0,3,17,142,1,17,1,1,5
	.byte	'enabled',0,17,142,1,43
	.word	641
	.byte	17,6,0,0,7
	.byte	'long long int',0,8,5,8
	.byte	'getDeadLine',0,3,17,164,2,25
	.word	22003
	.byte	1,1,5
	.byte	'timeout',0,17,164,2,50
	.word	22003
	.byte	17,6,0,0,8
	.byte	'isDeadLine',0,3,17,211,2,20
	.word	641
	.byte	1,1,5
	.byte	'deadLine',0,17,211,2,44
	.word	22003
	.byte	17,6,0,0,8
	.byte	'now',0,3,17,221,1,25
	.word	22003
	.byte	1,1,17,6,6,6,0,0,8
	.byte	'nowWithoutCriticalSection',0,3,17,240,1,25
	.word	22003
	.byte	1,1,17,6,0,0
.L200:
	.byte	7
	.byte	'double',0,8,4,21
	.byte	'sin',0,18,88,25
	.word	22174
	.byte	1,1,1,1,22,18,88,43
	.word	22174
	.byte	0,21
	.byte	'cos',0,18,91,25
	.word	22174
	.byte	1,1,1,1,22,18,91,43
	.word	22174
	.byte	0,21
	.byte	'asin',0,18,97,25
	.word	22174
	.byte	1,1,1,1,22,18,97,43
	.word	22174
	.byte	0,21
	.byte	'atan2',0,18,106,25
	.word	22174
	.byte	1,1,1,1,22,18,106,43
	.word	22174
	.byte	22,18,106,51
	.word	22174
	.byte	0,21
	.byte	'pow',0,18,180,1,25
	.word	22174
	.byte	1,1,1,1,22,18,180,1,43
	.word	22174
	.byte	22,18,180,1,51
	.word	22174
	.byte	0,21
	.byte	'sqrt',0,18,186,1,25
	.word	22174
	.byte	1,1,1,1,22,18,186,1,43
	.word	22174
	.byte	0,7
	.byte	'char',0,1,6
.L268:
	.byte	3
	.word	22359
	.byte	23
	.word	22367
	.byte	24
	.word	22359
	.byte	3
	.word	22377
	.byte	23
	.word	22382
	.byte	21
	.byte	'strncpy',0,19,40,17
	.word	22367
	.byte	1,1,1,1,22,19,40,33
	.word	22372
	.byte	22,19,40,56
	.word	22387
	.byte	22,19,40,68
	.word	466
	.byte	0,21
	.byte	'strchr',0,19,43,17
	.word	22367
	.byte	1,1,1,1,22,19,43,39
	.word	22382
	.byte	22,19,43,42
	.word	482
	.byte	0,21
	.byte	'strncmp',0,19,51,17
	.word	482
	.byte	1,1,1,1,22,19,51,39
	.word	22382
	.byte	22,19,51,53
	.word	22382
	.byte	22,19,51,56
	.word	466
	.byte	0,21
	.byte	'func_str_to_int',0,20,79,13
	.word	482
	.byte	1,1,1,1,5
	.byte	'str',0,20,79,56
	.word	22367
	.byte	0,21
	.byte	'func_str_to_double',0,20,85,13
	.word	22174
	.byte	1,1,1,1,5
	.byte	'str',0,20,85,56
	.word	22367
	.byte	0,21
	.byte	'func_str_to_hex',0,20,87,13
	.word	1775
	.byte	1,1,1,1,5
	.byte	'str',0,20,87,56
	.word	22367
	.byte	0,15,21,42,9,1,16
	.byte	'FIFO_SUCCESS',0,0,16
	.byte	'FIFO_RESET_UNDO',0,1,16
	.byte	'FIFO_CLEAR_UNDO',0,2,16
	.byte	'FIFO_BUFFER_NULL',0,3,16
	.byte	'FIFO_WRITE_UNDO',0,4,16
	.byte	'FIFO_SPACE_NO_ENOUGH',0,5,16
	.byte	'FIFO_READ_UNDO',0,6,16
	.byte	'FIFO_DATA_NO_ENOUGH',0,7,0,15,21,78,9,1,16
	.byte	'FIFO_DATA_8BIT',0,0,16
	.byte	'FIFO_DATA_16BIT',0,1,16
	.byte	'FIFO_DATA_32BIT',0,2,0
.L326:
	.byte	20,21,85,9,24,13
	.byte	'execution',0
	.word	641
	.byte	1,2,35,0,13
	.byte	'type',0
	.word	22800
	.byte	1,2,35,1,13
	.byte	'buffer',0
	.word	380
	.byte	4,2,35,4,13
	.byte	'head',0
	.word	1775
	.byte	4,2,35,8,13
	.byte	'end',0
	.word	1775
	.byte	4,2,35,12,13
	.byte	'size',0
	.word	1775
	.byte	4,2,35,16,13
	.byte	'max',0
	.word	1775
	.byte	4,2,35,20,0,3
	.word	22859
	.byte	21
	.byte	'fifo_clear',0,21,96,17
	.word	22644
	.byte	1,1,1,1,5
	.byte	'fifo',0,21,96,55
	.word	22968
	.byte	0,21
	.byte	'fifo_used',0,21,97,17
	.word	1775
	.byte	1,1,1,1,5
	.byte	'fifo',0,21,97,55
	.word	22968
	.byte	0,21
	.byte	'fifo_write_buffer',0,21,100,17
	.word	22644
	.byte	1,1,1,1,5
	.byte	'fifo',0,21,100,55
	.word	22968
	.byte	5
	.byte	'dat',0,21,100,67
	.word	380
	.byte	5
	.byte	'length',0,21,100,79
	.word	1775
	.byte	0,3
	.word	1775
	.byte	15,21,72,9,1,16
	.byte	'FIFO_READ_AND_CLEAN',0,0,16
	.byte	'FIFO_READ_ONLY',0,1,0,21
	.byte	'fifo_read_buffer',0,21,102,17
	.word	22644
	.byte	1,1,1,1,5
	.byte	'fifo',0,21,102,55
	.word	22968
	.byte	5
	.byte	'dat',0,21,102,67
	.word	380
	.byte	5
	.byte	'length',0,21,102,80
	.word	23117
	.byte	5
	.byte	'flag',0,21,102,108
	.word	23122
	.byte	0,21
	.byte	'fifo_init',0,21,105,17
	.word	22644
	.byte	1,1,1,1,5
	.byte	'fifo',0,21,105,55
	.word	22968
	.byte	5
	.byte	'type',0,21,105,81
	.word	22800
	.byte	5
	.byte	'buffer_addr',0,21,105,93
	.word	380
	.byte	5
	.byte	'size',0,21,105,113
	.word	1775
	.byte	0,25
	.byte	'system_delay_ms',0,22,46,9,1,1,1,1,5
	.byte	'time',0,22,46,45
	.word	1775
	.byte	0,26
	.word	210
	.byte	27
	.word	236
	.byte	6,0,26
	.word	271
	.byte	27
	.word	303
	.byte	6,0,26
	.word	316
	.byte	6,0,26
	.word	385
	.byte	27
	.word	404
	.byte	6,0,26
	.word	420
	.byte	27
	.word	435
	.byte	27
	.word	449
	.byte	6,0,26
	.word	1228
	.byte	27
	.word	1268
	.byte	27
	.word	1286
	.byte	6,0,26
	.word	1306
	.byte	27
	.word	1344
	.byte	27
	.word	1362
	.byte	6,0,26
	.word	1382
	.byte	27
	.word	1433
	.byte	6,0,26
	.word	1532
	.byte	6,0,26
	.word	1566
	.byte	6,0,26
	.word	1608
	.byte	17,28
	.word	1566
	.byte	29
	.word	1606
	.byte	0,6,0,0,26
	.word	1649
	.byte	6,0,26
	.word	1683
	.byte	6,0,26
	.word	1723
	.byte	27
	.word	1756
	.byte	6,0,26
	.word	1796
	.byte	27
	.word	1837
	.byte	6,0,26
	.word	1856
	.byte	27
	.word	1911
	.byte	6,0,26
	.word	1930
	.byte	27
	.word	1970
	.byte	27
	.word	1987
	.byte	17,6,0,0,26
	.word	9889
	.byte	27
	.word	9921
	.byte	27
	.word	9935
	.byte	27
	.word	9953
	.byte	6,0,26
	.word	10256
	.byte	27
	.word	10289
	.byte	27
	.word	10303
	.byte	27
	.word	10321
	.byte	27
	.word	10335
	.byte	6,0,26
	.word	10455
	.byte	27
	.word	10483
	.byte	27
	.word	10497
	.byte	27
	.word	10515
	.byte	6,0,26
	.word	10533
	.byte	6,0,26
	.word	10912
	.byte	27
	.word	10940
	.byte	6,0,26
	.word	17492
	.byte	27
	.word	17520
	.byte	27
	.word	17536
	.byte	6,0,26
	.word	17676
	.byte	27
	.word	17706
	.byte	27
	.word	17722
	.byte	6,0,26
	.word	17969
	.byte	27
	.word	17998
	.byte	27
	.word	18014
	.byte	6,0,26
	.word	18310
	.byte	27
	.word	18341
	.byte	6,0,26
	.word	21792
	.byte	27
	.word	21815
	.byte	6,0,26
	.word	21830
	.byte	27
	.word	21862
	.byte	17,17,28
	.word	10533
	.byte	29
	.word	10571
	.byte	0,0,6,0,0,26
	.word	21880
	.byte	27
	.word	21908
	.byte	6,0,26
	.word	21923
	.byte	17,28
	.word	1608
	.byte	30
	.word	1645
	.byte	28
	.word	1566
	.byte	29
	.word	1606
	.byte	0,29
	.word	1646
	.byte	0,0,6,0,0,26
	.word	21956
	.byte	27
	.word	21982
	.byte	17,28
	.word	1723
	.byte	27
	.word	1756
	.byte	29
	.word	1773
	.byte	0,6,0,0,26
	.word	22020
	.byte	27
	.word	22044
	.byte	17,28
	.word	22110
	.byte	30
	.word	22126
	.byte	28
	.word	21923
	.byte	30
	.word	21952
	.byte	28
	.word	1608
	.byte	30
	.word	1645
	.byte	28
	.word	1566
	.byte	29
	.word	1606
	.byte	0,29
	.word	1646
	.byte	0,0,29
	.word	21953
	.byte	0,0,29
	.word	22127
	.byte	28
	.word	21956
	.byte	27
	.word	21982
	.byte	30
	.word	21999
	.byte	28
	.word	1723
	.byte	27
	.word	1756
	.byte	29
	.word	1773
	.byte	0,29
	.word	22000
	.byte	0,0,29
	.word	22128
	.byte	28
	.word	21792
	.byte	27
	.word	21815
	.byte	29
	.word	21828
	.byte	0,29
	.word	22129
	.byte	0,0,6,0,0,26
	.word	22065
	.byte	27
	.word	22088
	.byte	17,28
	.word	22110
	.byte	30
	.word	22126
	.byte	28
	.word	21923
	.byte	30
	.word	21952
	.byte	28
	.word	1608
	.byte	30
	.word	1645
	.byte	28
	.word	1566
	.byte	29
	.word	1606
	.byte	0,29
	.word	1646
	.byte	0,0,29
	.word	21953
	.byte	0,0,29
	.word	22127
	.byte	28
	.word	21956
	.byte	27
	.word	21982
	.byte	30
	.word	21999
	.byte	28
	.word	1723
	.byte	27
	.word	1756
	.byte	29
	.word	1773
	.byte	0,29
	.word	22000
	.byte	0,0,29
	.word	22128
	.byte	28
	.word	21792
	.byte	27
	.word	21815
	.byte	29
	.word	21828
	.byte	0,29
	.word	22129
	.byte	0,0,6,0,0,26
	.word	22110
	.byte	17,28
	.word	21923
	.byte	30
	.word	21952
	.byte	28
	.word	1608
	.byte	30
	.word	1645
	.byte	28
	.word	1566
	.byte	29
	.word	1606
	.byte	0,29
	.word	1646
	.byte	0,0,29
	.word	21953
	.byte	0,0,6,28
	.word	21956
	.byte	27
	.word	21982
	.byte	30
	.word	21999
	.byte	28
	.word	1723
	.byte	27
	.word	1756
	.byte	29
	.word	1773
	.byte	0,29
	.word	22000
	.byte	0,0,6,28
	.word	21792
	.byte	27
	.word	21815
	.byte	29
	.word	21828
	.byte	0,6,0,0,26
	.word	22132
	.byte	17,28
	.word	21792
	.byte	27
	.word	21815
	.byte	29
	.word	21828
	.byte	0,6,0,0,15,23,103,9,1,16
	.byte	'UART_0',0,0,16
	.byte	'UART_1',0,1,16
	.byte	'UART_2',0,2,16
	.byte	'UART_3',0,3,0,24
	.word	641
	.byte	3
	.word	24307
	.byte	25
	.byte	'uart_write_buffer',0,23,119,9,1,1,1,1,5
	.byte	'uartn',0,23,119,62
	.word	24265
	.byte	5
	.byte	'buff',0,23,119,82
	.word	24312
	.byte	5
	.byte	'len',0,23,119,95
	.word	1775
	.byte	0,3
	.word	641
	.byte	21
	.byte	'uart_query_byte',0,23,123,9
	.word	641
	.byte	1,1,1,1,5
	.byte	'uartn',0,23,123,62
	.word	24265
	.byte	5
	.byte	'dat',0,23,123,76
	.word	24383
	.byte	0,25
	.byte	'uart_rx_interrupt',0,23,126,9,1,1,1,1,5
	.byte	'uartn',0,23,126,62
	.word	24265
	.byte	5
	.byte	'status',0,23,126,76
	.word	1775
	.byte	0,15,23,43,9,1,16
	.byte	'UART0_TX_P14_0',0,0,16
	.byte	'UART0_TX_P14_1',0,1,16
	.byte	'UART0_TX_P15_2',0,2,16
	.byte	'UART0_TX_P15_3',0,3,16
	.byte	'UART1_TX_P02_2',0,4,16
	.byte	'UART1_TX_P11_12',0,5,16
	.byte	'UART1_TX_P15_0',0,6,16
	.byte	'UART1_TX_P15_1',0,7,16
	.byte	'UART1_TX_P15_4',0,8,16
	.byte	'UART1_TX_P15_5',0,9,16
	.byte	'UART1_TX_P20_10',0,10,16
	.byte	'UART1_TX_P33_12',0,11,16
	.byte	'UART1_TX_P33_13',0,12,16
	.byte	'UART2_TX_P02_0',0,13,16
	.byte	'UART2_TX_P10_5',0,14,16
	.byte	'UART2_TX_P14_2',0,15,16
	.byte	'UART2_TX_P14_3',0,16,16
	.byte	'UART2_TX_P33_8',0,17,16
	.byte	'UART2_TX_P33_9',0,18,16
	.byte	'UART3_TX_P00_0',0,19,16
	.byte	'UART3_TX_P00_1',0,20,16
	.byte	'UART3_TX_P15_6',0,21,16
	.byte	'UART3_TX_P15_7',0,22,16
	.byte	'UART3_TX_P20_0',0,23,16
	.byte	'UART3_TX_P20_3',0,24,16
	.byte	'UART3_TX_P21_7',0,25,0,15,23,77,9,1,16
	.byte	'UART0_RX_P14_1',0,0,16
	.byte	'UART0_RX_P15_3',0,1,16
	.byte	'UART1_RX_P02_3',0,2,16
	.byte	'UART1_RX_P11_10',0,3,16
	.byte	'UART1_RX_P15_1',0,4,16
	.byte	'UART1_RX_P15_5',0,5,16
	.byte	'UART1_RX_P20_9',0,6,16
	.byte	'UART1_RX_P33_13',0,7,16
	.byte	'UART2_RX_P02_0',0,8,16
	.byte	'UART2_RX_P02_1',0,9,16
	.byte	'UART2_RX_P10_6',0,10,16
	.byte	'UART2_RX_P14_3',0,11,16
	.byte	'UART2_RX_P33_8',0,12,16
	.byte	'UART3_RX_P00_1',0,13,16
	.byte	'UART3_RX_P15_7',0,14,16
	.byte	'UART3_RX_P20_3',0,15,16
	.byte	'UART3_RX_P21_6',0,16,0,25
	.byte	'uart_init',0,23,129,1,9,1,1,1,1,5
	.byte	'uartn',0,23,129,1,62
	.word	24265
	.byte	5
	.byte	'baud',0,23,129,1,76
	.word	1775
	.byte	5
	.byte	'tx_pin',0,23,129,1,99
	.word	24499
	.byte	5
	.byte	'rx_pin',0,23,129,1,124
	.word	24951
	.byte	0
.L206:
	.byte	24
	.word	22174
.L227:
	.byte	18,5
	.word	641
	.byte	19,4,0
.L234:
	.byte	18,6
	.word	641
	.byte	19,5,0
.L240:
	.byte	15,24,62,9,1,16
	.byte	'GN42A',0,1,16
	.byte	'TAU1201',0,1,16
	.byte	'GN43RFA',0,2,0,18,28
	.word	641
	.byte	19,27,0
.L242:
	.byte	24
	.word	25386
	.byte	18,11
	.word	641
	.byte	19,10,0
.L244:
	.byte	24
	.word	25400
.L246:
	.byte	24
	.word	25400
.L248:
	.byte	24
	.word	25400
.L250:
	.byte	24
	.word	25400
.L252:
	.byte	24
	.word	25400
.L254:
	.byte	24
	.word	25400
.L256:
	.byte	24
	.word	25400
.L258:
	.byte	24
	.word	25400
.L260:
	.byte	24
	.word	25400
.L262:
	.byte	24
	.word	25400
.L264:
	.byte	24
	.word	25400
.L278:
	.byte	18,10
	.word	22359
	.byte	19,9,0
.L286:
	.byte	18,15
	.word	22359
	.byte	19,14,0,20,24,69,9,8,13
	.byte	'year',0
	.word	658
	.byte	2,2,35,0,13
	.byte	'month',0
	.word	641
	.byte	1,2,35,2,13
	.byte	'day',0
	.word	641
	.byte	1,2,35,3,13
	.byte	'hour',0
	.word	641
	.byte	1,2,35,4,13
	.byte	'minute',0
	.word	641
	.byte	1,2,35,5,13
	.byte	'second',0
	.word	641
	.byte	1,2,35,6,0
.L295:
	.byte	3
	.word	25482
	.byte	7
	.byte	'char',0,1,6
.L325:
	.byte	20,24,79,9,64,13
	.byte	'time',0
	.word	25482
	.byte	8,2,35,0,13
	.byte	'state',0
	.word	641
	.byte	1,2,35,8,13
	.byte	'latitude_degree',0
	.word	658
	.byte	2,2,35,10,13
	.byte	'latitude_cent',0
	.word	658
	.byte	2,2,35,12,13
	.byte	'latitude_second',0
	.word	658
	.byte	2,2,35,14,13
	.byte	'longitude_degree',0
	.word	658
	.byte	2,2,35,16,13
	.byte	'longitude_cent',0
	.word	658
	.byte	2,2,35,18,13
	.byte	'longitude_second',0
	.word	658
	.byte	2,2,35,20,13
	.byte	'latitude',0
	.word	22174
	.byte	8,2,35,24,13
	.byte	'longitude',0
	.word	22174
	.byte	8,2,35,32,13
	.byte	'ns',0
	.word	25581
	.byte	1,2,35,40,13
	.byte	'ew',0
	.word	25581
	.byte	1,2,35,41,13
	.byte	'speed',0
	.word	262
	.byte	4,2,35,42,13
	.byte	'direction',0
	.word	262
	.byte	4,2,35,46,13
	.byte	'antenna_direction_state',0
	.word	641
	.byte	1,2,35,50,13
	.byte	'antenna_direction',0
	.word	262
	.byte	4,2,35,52,13
	.byte	'satellite_used',0
	.word	641
	.byte	1,2,35,56,13
	.byte	'height',0
	.word	262
	.byte	4,2,35,58,0
.L300:
	.byte	3
	.word	25589
	.byte	31
	.byte	'__wchar_t',0,25,1,1
	.word	18030
	.byte	31
	.byte	'__size_t',0,25,1,1
	.word	466
	.byte	31
	.byte	'__ptrdiff_t',0,25,1,1
	.word	482
	.byte	32,1,3
	.word	26028
	.byte	31
	.byte	'__codeptr',0,25,1,1
	.word	26030
	.byte	31
	.byte	'__intptr_t',0,25,1,1
	.word	482
	.byte	31
	.byte	'__uintptr_t',0,25,1,1
	.word	466
	.byte	31
	.byte	'size_t',0,26,31,25
	.word	466
	.byte	31
	.byte	'_iob_flag_t',0,26,82,25
	.word	658
	.byte	31
	.byte	'boolean',0,27,101,29
	.word	641
	.byte	31
	.byte	'uint8',0,27,105,29
	.word	641
	.byte	31
	.byte	'uint16',0,27,109,29
	.word	658
	.byte	31
	.byte	'uint32',0,27,113,29
	.word	1775
	.byte	31
	.byte	'uint64',0,27,118,29
	.word	348
	.byte	31
	.byte	'sint16',0,27,126,29
	.word	18030
	.byte	31
	.byte	'sint32',0,27,131,1,29
	.word	18043
	.byte	31
	.byte	'sint64',0,27,138,1,29
	.word	22003
	.byte	31
	.byte	'float32',0,27,167,1,29
	.word	262
	.byte	31
	.byte	'pvoid',0,28,57,28
	.word	380
	.byte	31
	.byte	'Ifx_TickTime',0,28,79,28
	.word	22003
	.byte	31
	.byte	'Ifx_SizeT',0,28,92,16
	.word	18030
	.byte	31
	.byte	'Ifx_Priority',0,28,103,16
	.word	658
	.byte	15,28,130,1,9,1,16
	.byte	'Ifx_RxSel_a',0,0,16
	.byte	'Ifx_RxSel_b',0,1,16
	.byte	'Ifx_RxSel_c',0,2,16
	.byte	'Ifx_RxSel_d',0,3,16
	.byte	'Ifx_RxSel_e',0,4,16
	.byte	'Ifx_RxSel_f',0,5,16
	.byte	'Ifx_RxSel_g',0,6,16
	.byte	'Ifx_RxSel_h',0,7,0,31
	.byte	'Ifx_RxSel',0,28,140,1,3
	.word	26340
	.byte	15,28,164,1,9,1,16
	.byte	'Ifx_DataBufferMode_normal',0,0,16
	.byte	'Ifx_DataBufferMode_timeStampSingle',0,1,0,31
	.byte	'Ifx_DataBufferMode',0,28,169,1,2
	.word	26478
	.byte	31
	.byte	'int8',0,29,54,29
	.word	25581
	.byte	31
	.byte	'int16',0,29,55,29
	.word	18030
	.byte	31
	.byte	'int32',0,29,56,29
	.word	482
	.byte	31
	.byte	'int64',0,29,57,29
	.word	22003
	.byte	31
	.byte	'fifo_state_enum',0,21,53,2
	.word	22644
	.byte	31
	.byte	'fifo_operation_enum',0,21,76,2
	.word	23122
	.byte	31
	.byte	'fifo_data_type_enum',0,21,83,2
	.word	22800
	.byte	31
	.byte	'fifo_struct',0,21,94,2
	.word	22859
	.byte	31
	.byte	'Ifx_ASCLIN_ACCEN0_Bits',0,13,79,3
	.word	16468
	.byte	31
	.byte	'Ifx_ASCLIN_ACCEN1_Bits',0,13,85,3
	.word	16376
	.byte	31
	.byte	'Ifx_ASCLIN_BITCON_Bits',0,13,97,3
	.word	12000
	.byte	31
	.byte	'Ifx_ASCLIN_BRD_Bits',0,13,106,3
	.word	12840
	.byte	31
	.byte	'Ifx_ASCLIN_BRG_Bits',0,13,115,3
	.word	12683
	.byte	31
	.byte	'Ifx_ASCLIN_CLC_Bits',0,13,125,3
	.word	10955
	.byte	31
	.byte	'Ifx_ASCLIN_CSR_Bits',0,13,133,1,3
	.word	15649
	.byte	31
	.byte	'Ifx_ASCLIN_DATCON_Bits',0,13,145,1,3
	.word	12486
	.byte	31
	.byte	'Ifx_ASCLIN_FLAGS_Bits',0,13,177,1,3
	.word	13496
	.byte	31
	.byte	'Ifx_ASCLIN_FLAGSCLEAR_Bits',0,13,209,1,3
	.word	14495
	.byte	31
	.byte	'Ifx_ASCLIN_FLAGSENABLE_Bits',0,13,238,1,3
	.word	15010
	.byte	31
	.byte	'Ifx_ASCLIN_FLAGSSET_Bits',0,13,142,2,3
	.word	13982
	.byte	31
	.byte	'Ifx_ASCLIN_FRAMECON_Bits',0,13,158,2,3
	.word	12221
	.byte	31
	.byte	'Ifx_ASCLIN_ID_Bits',0,13,166,2,3
	.word	11409
	.byte	31
	.byte	'Ifx_ASCLIN_IOCR_Bits',0,13,184,2,3
	.word	11114
	.byte	31
	.byte	'Ifx_ASCLIN_KRST0_Bits',0,13,192,2,3
	.word	16250
	.byte	31
	.byte	'Ifx_ASCLIN_KRST1_Bits',0,13,199,2,3
	.word	16143
	.byte	31
	.byte	'Ifx_ASCLIN_KRSTCLR_Bits',0,13,206,2,3
	.word	16034
	.byte	31
	.byte	'Ifx_ASCLIN_LIN_BTIMER_Bits',0,13,213,2,3
	.word	13194
	.byte	31
	.byte	'Ifx_ASCLIN_LIN_CON_Bits',0,13,225,2,3
	.word	12994
	.byte	31
	.byte	'Ifx_ASCLIN_LIN_HTIMER_Bits',0,13,232,2,3
	.word	13308
	.byte	31
	.byte	'Ifx_ASCLIN_OCS_Bits',0,13,242,2,3
	.word	15871
	.byte	31
	.byte	'Ifx_ASCLIN_RXDATA_Bits',0,13,248,2,3
	.word	15562
	.byte	31
	.byte	'Ifx_ASCLIN_RXDATAD_Bits',0,13,254,2,3
	.word	15772
	.byte	31
	.byte	'Ifx_ASCLIN_RXFIFOCON_Bits',0,13,140,3,3
	.word	11759
	.byte	31
	.byte	'Ifx_ASCLIN_TXDATA_Bits',0,13,146,3,3
	.word	15475
	.byte	31
	.byte	'Ifx_ASCLIN_TXFIFOCON_Bits',0,13,159,3,3
	.word	11534
	.byte	31
	.byte	'Ifx_ASCLIN_ACCEN0',0,13,172,3,3
	.word	17000
	.byte	31
	.byte	'Ifx_ASCLIN_ACCEN1',0,13,180,3,3
	.word	16428
	.byte	31
	.byte	'Ifx_ASCLIN_BITCON',0,13,188,3,3
	.word	12181
	.byte	31
	.byte	'Ifx_ASCLIN_BRD',0,13,196,3,3
	.word	12954
	.byte	31
	.byte	'Ifx_ASCLIN_BRG',0,13,204,3,3
	.word	12800
	.byte	31
	.byte	'Ifx_ASCLIN_CLC',0,13,212,3,3
	.word	11074
	.byte	31
	.byte	'Ifx_ASCLIN_CSR',0,13,220,3,3
	.word	15732
	.byte	31
	.byte	'Ifx_ASCLIN_DATCON',0,13,228,3,3
	.word	12643
	.byte	31
	.byte	'Ifx_ASCLIN_FLAGS',0,13,236,3,3
	.word	13942
	.byte	31
	.byte	'Ifx_ASCLIN_FLAGSCLEAR',0,13,244,3,3
	.word	14970
	.byte	31
	.byte	'Ifx_ASCLIN_FLAGSENABLE',0,13,252,3,3
	.word	15435
	.byte	31
	.byte	'Ifx_ASCLIN_FLAGSSET',0,13,132,4,3
	.word	14455
	.byte	31
	.byte	'Ifx_ASCLIN_FRAMECON',0,13,140,4,3
	.word	12446
	.byte	31
	.byte	'Ifx_ASCLIN_ID',0,13,148,4,3
	.word	11494
	.byte	31
	.byte	'Ifx_ASCLIN_IOCR',0,13,156,4,3
	.word	11369
	.byte	31
	.byte	'Ifx_ASCLIN_KRST0',0,13,164,4,3
	.word	16336
	.byte	31
	.byte	'Ifx_ASCLIN_KRST1',0,13,172,4,3
	.word	16210
	.byte	31
	.byte	'Ifx_ASCLIN_KRSTCLR',0,13,180,4,3
	.word	16103
	.byte	31
	.byte	'Ifx_ASCLIN_LIN_BTIMER',0,13,188,4,3
	.word	13268
	.byte	31
	.byte	'Ifx_ASCLIN_LIN_CON',0,13,196,4,3
	.word	13154
	.byte	31
	.byte	'Ifx_ASCLIN_LIN_HTIMER',0,13,204,4,3
	.word	13383
	.byte	31
	.byte	'Ifx_ASCLIN_OCS',0,13,212,4,3
	.word	15994
	.byte	31
	.byte	'Ifx_ASCLIN_RXDATA',0,13,220,4,3
	.word	15609
	.byte	31
	.byte	'Ifx_ASCLIN_RXDATAD',0,13,228,4,3
	.word	15820
	.byte	31
	.byte	'Ifx_ASCLIN_RXFIFOCON',0,13,236,4,3
	.word	11960
	.byte	31
	.byte	'Ifx_ASCLIN_TXDATA',0,13,244,4,3
	.word	15522
	.byte	31
	.byte	'Ifx_ASCLIN_TXFIFOCON',0,13,252,4,3
	.word	11719
	.byte	14
	.word	13423
	.byte	31
	.byte	'Ifx_ASCLIN_LIN',0,13,140,5,3
	.word	28332
	.byte	14
	.word	17040
	.byte	31
	.byte	'Ifx_ASCLIN',0,13,181,5,3
	.word	28361
	.byte	15,30,236,10,9,1,16
	.byte	'IfxScu_CCUCON0_CLKSEL_fBack',0,0,16
	.byte	'IfxScu_CCUCON0_CLKSEL_fPll',0,1,0,31
	.byte	'IfxScu_CCUCON0_CLKSEL',0,30,240,10,3
	.word	28386
	.byte	15,30,250,10,9,1,16
	.byte	'IfxScu_WDTCON1_IR_divBy16384',0,0,16
	.byte	'IfxScu_WDTCON1_IR_divBy256',0,1,16
	.byte	'IfxScu_WDTCON1_IR_divBy64',0,2,0,31
	.byte	'IfxScu_WDTCON1_IR',0,30,255,10,3
	.word	28483
	.byte	10
	.byte	'_Ifx_SCU_ACCEN0_Bits',0,4,45,16,4,11
	.byte	'EN0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	641
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	641
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	641
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	641
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	641
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	641
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	641
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	641
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	641
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	641
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	641
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	641
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_ACCEN0_Bits',0,4,79,3
	.word	28605
	.byte	10
	.byte	'_Ifx_SCU_ACCEN1_Bits',0,4,82,16,4,11
	.byte	'reserved_0',0,4
	.word	466
	.byte	32,0,2,35,0,0,31
	.byte	'Ifx_SCU_ACCEN1_Bits',0,4,85,3
	.word	29162
	.byte	10
	.byte	'_Ifx_SCU_ARSTDIS_Bits',0,4,88,16,4,11
	.byte	'STM0DIS',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'STM1DIS',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'STM2DIS',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	466
	.byte	29,0,2,35,0,0,31
	.byte	'Ifx_SCU_ARSTDIS_Bits',0,4,94,3
	.word	29239
	.byte	10
	.byte	'_Ifx_SCU_CCUCON0_Bits',0,4,97,16,4,11
	.byte	'BAUD1DIV',0,1
	.word	641
	.byte	4,4,2,35,0,11
	.byte	'BAUD2DIV',0,1
	.word	641
	.byte	4,0,2,35,0,11
	.byte	'SRIDIV',0,1
	.word	641
	.byte	4,4,2,35,1,11
	.byte	'LPDIV',0,1
	.word	641
	.byte	4,0,2,35,1,11
	.byte	'SPBDIV',0,1
	.word	641
	.byte	4,4,2,35,2,11
	.byte	'FSI2DIV',0,1
	.word	641
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	641
	.byte	2,0,2,35,2,11
	.byte	'FSIDIV',0,1
	.word	641
	.byte	2,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	641
	.byte	2,4,2,35,3,11
	.byte	'CLKSEL',0,1
	.word	641
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_CCUCON0_Bits',0,4,111,3
	.word	29375
	.byte	10
	.byte	'_Ifx_SCU_CCUCON1_Bits',0,4,114,16,4,11
	.byte	'CANDIV',0,1
	.word	641
	.byte	4,4,2,35,0,11
	.byte	'ERAYDIV',0,1
	.word	641
	.byte	4,0,2,35,0,11
	.byte	'STMDIV',0,1
	.word	641
	.byte	4,4,2,35,1,11
	.byte	'GTMDIV',0,1
	.word	641
	.byte	4,0,2,35,1,11
	.byte	'ETHDIV',0,1
	.word	641
	.byte	4,4,2,35,2,11
	.byte	'ASCLINFDIV',0,1
	.word	641
	.byte	4,0,2,35,2,11
	.byte	'ASCLINSDIV',0,1
	.word	641
	.byte	4,4,2,35,3,11
	.byte	'INSEL',0,1
	.word	641
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_CCUCON1_Bits',0,4,126,3
	.word	29655
	.byte	10
	.byte	'_Ifx_SCU_CCUCON2_Bits',0,4,129,1,16,4,11
	.byte	'BBBDIV',0,1
	.word	641
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	466
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_CCUCON2_Bits',0,4,135,1,3
	.word	29893
	.byte	10
	.byte	'_Ifx_SCU_CCUCON3_Bits',0,4,138,1,16,4,11
	.byte	'PLLDIV',0,1
	.word	641
	.byte	6,2,2,35,0,11
	.byte	'PLLSEL',0,1
	.word	641
	.byte	2,0,2,35,0,11
	.byte	'PLLERAYDIV',0,1
	.word	641
	.byte	6,2,2,35,1,11
	.byte	'PLLERAYSEL',0,1
	.word	641
	.byte	2,0,2,35,1,11
	.byte	'SRIDIV',0,1
	.word	641
	.byte	6,2,2,35,2,11
	.byte	'SRISEL',0,1
	.word	641
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	641
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	641
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_CCUCON3_Bits',0,4,150,1,3
	.word	30021
	.byte	10
	.byte	'_Ifx_SCU_CCUCON4_Bits',0,4,153,1,16,4,11
	.byte	'SPBDIV',0,1
	.word	641
	.byte	6,2,2,35,0,11
	.byte	'SPBSEL',0,1
	.word	641
	.byte	2,0,2,35,0,11
	.byte	'GTMDIV',0,1
	.word	641
	.byte	6,2,2,35,1,11
	.byte	'GTMSEL',0,1
	.word	641
	.byte	2,0,2,35,1,11
	.byte	'STMDIV',0,1
	.word	641
	.byte	6,2,2,35,2,11
	.byte	'STMSEL',0,1
	.word	641
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	641
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	641
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_CCUCON4_Bits',0,4,165,1,3
	.word	30264
	.byte	10
	.byte	'_Ifx_SCU_CCUCON5_Bits',0,4,168,1,16,4,11
	.byte	'MAXDIV',0,1
	.word	641
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	466
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_CCUCON5_Bits',0,4,174,1,3
	.word	30499
	.byte	10
	.byte	'_Ifx_SCU_CCUCON6_Bits',0,4,177,1,16,4,11
	.byte	'CPU0DIV',0,1
	.word	641
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	466
	.byte	26,0,2,35,0,0,31
	.byte	'Ifx_SCU_CCUCON6_Bits',0,4,181,1,3
	.word	30627
	.byte	10
	.byte	'_Ifx_SCU_CCUCON7_Bits',0,4,184,1,16,4,11
	.byte	'CPU1DIV',0,1
	.word	641
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	466
	.byte	26,0,2,35,0,0,31
	.byte	'Ifx_SCU_CCUCON7_Bits',0,4,188,1,3
	.word	30727
	.byte	10
	.byte	'_Ifx_SCU_CHIPID_Bits',0,4,191,1,16,4,11
	.byte	'CHREV',0,1
	.word	641
	.byte	6,2,2,35,0,11
	.byte	'CHTEC',0,1
	.word	641
	.byte	2,0,2,35,0,11
	.byte	'CHID',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'EEA',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'UCODE',0,1
	.word	641
	.byte	7,0,2,35,2,11
	.byte	'FSIZE',0,1
	.word	641
	.byte	4,4,2,35,3,11
	.byte	'SP',0,1
	.word	641
	.byte	2,2,2,35,3,11
	.byte	'SEC',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	641
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_CHIPID_Bits',0,4,202,1,3
	.word	30827
	.byte	10
	.byte	'_Ifx_SCU_DTSCON_Bits',0,4,205,1,16,4,11
	.byte	'PWD',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'START',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	641
	.byte	2,4,2,35,0,11
	.byte	'CAL',0,4
	.word	466
	.byte	20,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	641
	.byte	7,1,2,35,3,11
	.byte	'SLCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_DTSCON_Bits',0,4,213,1,3
	.word	31035
	.byte	10
	.byte	'_Ifx_SCU_DTSLIM_Bits',0,4,216,1,16,4,11
	.byte	'LOWER',0,2
	.word	658
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	641
	.byte	5,1,2,35,1,11
	.byte	'LLU',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'UPPER',0,2
	.word	658
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	641
	.byte	4,2,2,35,3,11
	.byte	'SLCK',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'UOF',0,1
	.word	641
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_DTSLIM_Bits',0,4,225,1,3
	.word	31200
	.byte	10
	.byte	'_Ifx_SCU_DTSSTAT_Bits',0,4,228,1,16,4,11
	.byte	'RESULT',0,2
	.word	658
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	641
	.byte	4,2,2,35,1,11
	.byte	'RDY',0,1
	.word	641
	.byte	1,1,2,35,1,11
	.byte	'BUSY',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	658
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_SCU_DTSSTAT_Bits',0,4,235,1,3
	.word	31383
	.byte	10
	.byte	'_Ifx_SCU_EICR_Bits',0,4,238,1,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	4,4,2,35,0,11
	.byte	'EXIS0',0,1
	.word	641
	.byte	3,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'FEN0',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'REN0',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'LDEN0',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'EIEN0',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'INP0',0,1
	.word	641
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	466
	.byte	5,12,2,35,0,11
	.byte	'EXIS1',0,1
	.word	641
	.byte	3,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	641
	.byte	1,0,2,35,2,11
	.byte	'FEN1',0,1
	.word	641
	.byte	1,7,2,35,3,11
	.byte	'REN1',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'LDEN1',0,1
	.word	641
	.byte	1,5,2,35,3,11
	.byte	'EIEN1',0,1
	.word	641
	.byte	1,4,2,35,3,11
	.byte	'INP1',0,1
	.word	641
	.byte	3,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	641
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EICR_Bits',0,4,129,2,3
	.word	31537
	.byte	10
	.byte	'_Ifx_SCU_EIFR_Bits',0,4,132,2,16,4,11
	.byte	'INTF0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'INTF1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'INTF2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'INTF3',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'INTF4',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'INTF5',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'INTF6',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'INTF7',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	466
	.byte	24,0,2,35,0,0,31
	.byte	'Ifx_SCU_EIFR_Bits',0,4,143,2,3
	.word	31901
	.byte	10
	.byte	'_Ifx_SCU_EMSR_Bits',0,4,146,2,16,4,11
	.byte	'POL',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'MODE',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'ENON',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'PSEL',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	658
	.byte	12,0,2,35,0,11
	.byte	'EMSF',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'SEMSF',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	641
	.byte	6,0,2,35,2,11
	.byte	'EMSFM',0,1
	.word	641
	.byte	2,6,2,35,3,11
	.byte	'SEMSFM',0,1
	.word	641
	.byte	2,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	641
	.byte	4,0,2,35,3,0,31
	.byte	'Ifx_SCU_EMSR_Bits',0,4,159,2,3
	.word	32112
	.byte	10
	.byte	'_Ifx_SCU_ESRCFG_Bits',0,4,162,2,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	7,1,2,35,0,11
	.byte	'EDCON',0,2
	.word	658
	.byte	2,7,2,35,0,11
	.byte	'reserved_9',0,4
	.word	466
	.byte	23,0,2,35,0,0,31
	.byte	'Ifx_SCU_ESRCFG_Bits',0,4,167,2,3
	.word	32364
	.byte	10
	.byte	'_Ifx_SCU_ESROCFG_Bits',0,4,170,2,16,4,11
	.byte	'ARI',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'ARC',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	466
	.byte	30,0,2,35,0,0,31
	.byte	'Ifx_SCU_ESROCFG_Bits',0,4,175,2,3
	.word	32482
	.byte	10
	.byte	'_Ifx_SCU_EVR13CON_Bits',0,4,178,2,16,4,11
	.byte	'reserved_0',0,4
	.word	466
	.byte	28,4,2,35,0,11
	.byte	'EVR13OFF',0,1
	.word	641
	.byte	1,3,2,35,3,11
	.byte	'BPEVR13OFF',0,1
	.word	641
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVR13CON_Bits',0,4,185,2,3
	.word	32593
	.byte	10
	.byte	'_Ifx_SCU_EVR33CON_Bits',0,4,188,2,16,4,11
	.byte	'reserved_0',0,4
	.word	466
	.byte	28,4,2,35,0,11
	.byte	'EVR33OFF',0,1
	.word	641
	.byte	1,3,2,35,3,11
	.byte	'BPEVR33OFF',0,1
	.word	641
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVR33CON_Bits',0,4,195,2,3
	.word	32756
	.byte	10
	.byte	'_Ifx_SCU_EVRADCSTAT_Bits',0,4,198,2,16,4,11
	.byte	'ADC13V',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'ADC33V',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'ADCSWDV',0,1
	.word	641
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	641
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	641
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVRADCSTAT_Bits',0,4,205,2,3
	.word	32919
	.byte	10
	.byte	'_Ifx_SCU_EVRDVSTAT_Bits',0,4,208,2,16,4,11
	.byte	'DVS13TRIM',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'DVS33TRIM',0,1
	.word	641
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	641
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	641
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVRDVSTAT_Bits',0,4,215,2,3
	.word	33077
	.byte	10
	.byte	'_Ifx_SCU_EVRMONCTRL_Bits',0,4,218,2,16,4,11
	.byte	'EVR13OVMOD',0,1
	.word	641
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	641
	.byte	2,4,2,35,0,11
	.byte	'EVR13UVMOD',0,1
	.word	641
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	641
	.byte	2,0,2,35,0,11
	.byte	'EVR33OVMOD',0,1
	.word	641
	.byte	2,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	641
	.byte	2,4,2,35,1,11
	.byte	'EVR33UVMOD',0,1
	.word	641
	.byte	2,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	641
	.byte	2,0,2,35,1,11
	.byte	'SWDOVMOD',0,1
	.word	641
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	641
	.byte	2,4,2,35,2,11
	.byte	'SWDUVMOD',0,1
	.word	641
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,2
	.word	658
	.byte	10,0,2,35,2,0,31
	.byte	'Ifx_SCU_EVRMONCTRL_Bits',0,4,232,2,3
	.word	33242
	.byte	10
	.byte	'_Ifx_SCU_EVROSCCTRL_Bits',0,4,235,2,16,4,11
	.byte	'OSCTRIM',0,2
	.word	658
	.byte	10,6,2,35,0,11
	.byte	'OSCPTAT',0,1
	.word	641
	.byte	6,0,2,35,1,11
	.byte	'OSCANASEL',0,1
	.word	641
	.byte	4,4,2,35,2,11
	.byte	'HPBGTRIM',0,2
	.word	658
	.byte	7,5,2,35,2,11
	.byte	'HPBGCLKEN',0,1
	.word	641
	.byte	1,4,2,35,3,11
	.byte	'OSC3V3',0,1
	.word	641
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	641
	.byte	2,1,2,35,3,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVROSCCTRL_Bits',0,4,245,2,3
	.word	33571
	.byte	10
	.byte	'_Ifx_SCU_EVROVMON_Bits',0,4,248,2,16,4,11
	.byte	'EVR13OVVAL',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'EVR33OVVAL',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'SWDOVVAL',0,1
	.word	641
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	641
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVROVMON_Bits',0,4,255,2,3
	.word	33792
	.byte	10
	.byte	'_Ifx_SCU_EVRRSTCON_Bits',0,4,130,3,16,4,11
	.byte	'RST13TRIM',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	466
	.byte	16,8,2,35,0,11
	.byte	'RST13OFF',0,1
	.word	641
	.byte	1,7,2,35,3,11
	.byte	'BPRST13OFF',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'RST33OFF',0,1
	.word	641
	.byte	1,5,2,35,3,11
	.byte	'BPRST33OFF',0,1
	.word	641
	.byte	1,4,2,35,3,11
	.byte	'RSTSWDOFF',0,1
	.word	641
	.byte	1,3,2,35,3,11
	.byte	'BPRSTSWDOFF',0,1
	.word	641
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVRRSTCON_Bits',0,4,142,3,3
	.word	33955
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF1_Bits',0,4,145,3,16,4,11
	.byte	'SD5P',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'SD5I',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'SD5D',0,1
	.word	641
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	641
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVRSDCOEFF1_Bits',0,4,152,3,3
	.word	34227
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF2_Bits',0,4,155,3,16,4,11
	.byte	'SD33P',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'SD33I',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'SD33D',0,1
	.word	641
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	641
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVRSDCOEFF2_Bits',0,4,162,3,3
	.word	34380
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF3_Bits',0,4,165,3,16,4,11
	.byte	'CT5REG0',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'CT5REG1',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'CT5REG2',0,1
	.word	641
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	641
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVRSDCOEFF3_Bits',0,4,172,3,3
	.word	34536
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF4_Bits',0,4,175,3,16,4,11
	.byte	'CT5REG3',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'CT5REG4',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	658
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVRSDCOEFF4_Bits',0,4,181,3,3
	.word	34698
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF5_Bits',0,4,184,3,16,4,11
	.byte	'CT33REG0',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'CT33REG1',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'CT33REG2',0,1
	.word	641
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	641
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVRSDCOEFF5_Bits',0,4,191,3,3
	.word	34841
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF6_Bits',0,4,194,3,16,4,11
	.byte	'CT33REG3',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'CT33REG4',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	658
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVRSDCOEFF6_Bits',0,4,200,3,3
	.word	35006
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL1_Bits',0,4,203,3,16,4,11
	.byte	'SDFREQSPRD',0,2
	.word	658
	.byte	16,0,2,35,0,11
	.byte	'SDFREQ',0,1
	.word	641
	.byte	8,0,2,35,2,11
	.byte	'SDSTEP',0,1
	.word	641
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	641
	.byte	2,2,2,35,3,11
	.byte	'SDSAMPLE',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVRSDCTRL1_Bits',0,4,211,3,3
	.word	35151
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL2_Bits',0,4,214,3,16,4,11
	.byte	'DRVP',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'SDMINMAXDC',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'DRVN',0,1
	.word	641
	.byte	8,0,2,35,2,11
	.byte	'SDLUT',0,1
	.word	641
	.byte	6,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVRSDCTRL2_Bits',0,4,222,3,3
	.word	35332
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL3_Bits',0,4,225,3,16,4,11
	.byte	'SDPWMPRE',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'SDPID',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'SDVOKLVL',0,1
	.word	641
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	641
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVRSDCTRL3_Bits',0,4,232,3,3
	.word	35506
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL4_Bits',0,4,235,3,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'SYNCDIV',0,1
	.word	641
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	466
	.byte	20,1,2,35,0,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVRSDCTRL4_Bits',0,4,241,3,3
	.word	35666
	.byte	10
	.byte	'_Ifx_SCU_EVRSTAT_Bits',0,4,244,3,16,4,11
	.byte	'EVR13',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'OV13',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'EVR33',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'OV33',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'OVSWD',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'UV13',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'UV33',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'UVSWD',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'EXTPASS13',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'EXTPASS33',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'BGPROK',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	466
	.byte	21,0,2,35,0,0,31
	.byte	'Ifx_SCU_EVRSTAT_Bits',0,4,130,4,3
	.word	35810
	.byte	10
	.byte	'_Ifx_SCU_EVRTRIM_Bits',0,4,133,4,16,4,11
	.byte	'EVR13TRIM',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'SDVOUTSEL',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	658
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVRTRIM_Bits',0,4,139,4,3
	.word	36084
	.byte	10
	.byte	'_Ifx_SCU_EVRUVMON_Bits',0,4,142,4,16,4,11
	.byte	'EVR13UVVAL',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'EVR33UVVAL',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'SWDUVVAL',0,1
	.word	641
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	641
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVRUVMON_Bits',0,4,149,4,3
	.word	36223
	.byte	10
	.byte	'_Ifx_SCU_EXTCON_Bits',0,4,152,4,16,4,11
	.byte	'EN0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'SEL0',0,1
	.word	641
	.byte	4,2,2,35,0,11
	.byte	'reserved_6',0,2
	.word	658
	.byte	10,0,2,35,0,11
	.byte	'EN1',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'NSEL',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'SEL1',0,1
	.word	641
	.byte	4,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	641
	.byte	2,0,2,35,2,11
	.byte	'DIV1',0,1
	.word	641
	.byte	8,0,2,35,3,0,31
	.byte	'Ifx_SCU_EXTCON_Bits',0,4,163,4,3
	.word	36386
	.byte	10
	.byte	'_Ifx_SCU_FDR_Bits',0,4,166,4,16,4,11
	.byte	'STEP',0,2
	.word	658
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	641
	.byte	4,2,2,35,1,11
	.byte	'DM',0,1
	.word	641
	.byte	2,0,2,35,1,11
	.byte	'RESULT',0,2
	.word	658
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	641
	.byte	5,1,2,35,3,11
	.byte	'DISCLK',0,1
	.word	641
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_FDR_Bits',0,4,174,4,3
	.word	36604
	.byte	10
	.byte	'_Ifx_SCU_FMR_Bits',0,4,177,4,16,4,11
	.byte	'FS0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'FS1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'FS2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'FS3',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'FS4',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'FS5',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'FS6',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'FS7',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'FC0',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'FC1',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'FC2',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'FC3',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'FC4',0,1
	.word	641
	.byte	1,3,2,35,2,11
	.byte	'FC5',0,1
	.word	641
	.byte	1,2,2,35,2,11
	.byte	'FC6',0,1
	.word	641
	.byte	1,1,2,35,2,11
	.byte	'FC7',0,1
	.word	641
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	641
	.byte	8,0,2,35,3,0,31
	.byte	'Ifx_SCU_FMR_Bits',0,4,197,4,3
	.word	36767
	.byte	10
	.byte	'_Ifx_SCU_ID_Bits',0,4,200,4,16,4,11
	.byte	'MODREV',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	658
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_SCU_ID_Bits',0,4,205,4,3
	.word	37103
	.byte	10
	.byte	'_Ifx_SCU_IGCR_Bits',0,4,208,4,16,4,11
	.byte	'IPEN00',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'IPEN01',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'IPEN02',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'IPEN03',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'IPEN04',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'IPEN05',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'IPEN06',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'IPEN07',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	641
	.byte	5,3,2,35,1,11
	.byte	'GEEN0',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'IGP0',0,1
	.word	641
	.byte	2,0,2,35,1,11
	.byte	'IPEN10',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'IPEN11',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'IPEN12',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'IPEN13',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'IPEN14',0,1
	.word	641
	.byte	1,3,2,35,2,11
	.byte	'IPEN15',0,1
	.word	641
	.byte	1,2,2,35,2,11
	.byte	'IPEN16',0,1
	.word	641
	.byte	1,1,2,35,2,11
	.byte	'IPEN17',0,1
	.word	641
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	641
	.byte	5,3,2,35,3,11
	.byte	'GEEN1',0,1
	.word	641
	.byte	1,2,2,35,3,11
	.byte	'IGP1',0,1
	.word	641
	.byte	2,0,2,35,3,0,31
	.byte	'Ifx_SCU_IGCR_Bits',0,4,232,4,3
	.word	37210
	.byte	10
	.byte	'_Ifx_SCU_IN_Bits',0,4,235,4,16,4,11
	.byte	'P0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	466
	.byte	30,0,2,35,0,0,31
	.byte	'Ifx_SCU_IN_Bits',0,4,240,4,3
	.word	37662
	.byte	10
	.byte	'_Ifx_SCU_IOCR_Bits',0,4,243,4,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	4,4,2,35,0,11
	.byte	'PC0',0,1
	.word	641
	.byte	4,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	641
	.byte	4,4,2,35,1,11
	.byte	'PC1',0,1
	.word	641
	.byte	4,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	658
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_SCU_IOCR_Bits',0,4,250,4,3
	.word	37761
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL0_Bits',0,4,253,4,16,4,11
	.byte	'LBISTREQ',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'LBISTREQP',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'PATTERNS',0,2
	.word	658
	.byte	14,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	658
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_SCU_LBISTCTRL0_Bits',0,4,131,5,3
	.word	37911
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL1_Bits',0,4,134,5,16,4,11
	.byte	'SEED',0,4
	.word	466
	.byte	23,9,2,35,0,11
	.byte	'reserved_23',0,1
	.word	641
	.byte	1,0,2,35,2,11
	.byte	'SPLITSH',0,1
	.word	641
	.byte	3,5,2,35,3,11
	.byte	'BODY',0,1
	.word	641
	.byte	1,4,2,35,3,11
	.byte	'LBISTFREQU',0,1
	.word	641
	.byte	4,0,2,35,3,0,31
	.byte	'Ifx_SCU_LBISTCTRL1_Bits',0,4,141,5,3
	.word	38060
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL2_Bits',0,4,144,5,16,4,11
	.byte	'SIGNATURE',0,4
	.word	466
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	641
	.byte	7,1,2,35,3,11
	.byte	'LBISTDONE',0,1
	.word	641
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_LBISTCTRL2_Bits',0,4,149,5,3
	.word	38221
	.byte	10
	.byte	'_Ifx_SCU_LCLCON_Bits',0,4,152,5,16,4,11
	.byte	'reserved_0',0,2
	.word	658
	.byte	16,0,2,35,0,11
	.byte	'LS',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,2
	.word	658
	.byte	14,1,2,35,2,11
	.byte	'LSEN',0,1
	.word	641
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_LCLCON_Bits',0,4,158,5,3
	.word	38351
	.byte	10
	.byte	'_Ifx_SCU_LCLTEST_Bits',0,4,161,5,16,4,11
	.byte	'LCLT0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'LCLT1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	466
	.byte	30,0,2,35,0,0,31
	.byte	'Ifx_SCU_LCLTEST_Bits',0,4,166,5,3
	.word	38483
	.byte	10
	.byte	'_Ifx_SCU_MANID_Bits',0,4,169,5,16,4,11
	.byte	'DEPT',0,1
	.word	641
	.byte	5,3,2,35,0,11
	.byte	'MANUF',0,2
	.word	658
	.byte	11,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	658
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_SCU_MANID_Bits',0,4,174,5,3
	.word	38598
	.byte	10
	.byte	'_Ifx_SCU_OMR_Bits',0,4,177,5,16,4,11
	.byte	'PS0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	658
	.byte	14,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	658
	.byte	14,0,2,35,2,0,31
	.byte	'Ifx_SCU_OMR_Bits',0,4,185,5,3
	.word	38709
	.byte	10
	.byte	'_Ifx_SCU_OSCCON_Bits',0,4,188,5,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'PLLLV',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'OSCRES',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'GAINSEL',0,1
	.word	641
	.byte	2,3,2,35,0,11
	.byte	'MODE',0,1
	.word	641
	.byte	2,1,2,35,0,11
	.byte	'SHBY',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'PLLHV',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'X1D',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'X1DEN',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	641
	.byte	4,0,2,35,1,11
	.byte	'OSCVAL',0,1
	.word	641
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	641
	.byte	2,1,2,35,2,11
	.byte	'APREN',0,1
	.word	641
	.byte	1,0,2,35,2,11
	.byte	'CAP0EN',0,1
	.word	641
	.byte	1,7,2,35,3,11
	.byte	'CAP1EN',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'CAP2EN',0,1
	.word	641
	.byte	1,5,2,35,3,11
	.byte	'CAP3EN',0,1
	.word	641
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	641
	.byte	4,0,2,35,3,0,31
	.byte	'Ifx_SCU_OSCCON_Bits',0,4,209,5,3
	.word	38867
	.byte	10
	.byte	'_Ifx_SCU_OUT_Bits',0,4,212,5,16,4,11
	.byte	'P0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	466
	.byte	30,0,2,35,0,0,31
	.byte	'Ifx_SCU_OUT_Bits',0,4,217,5,3
	.word	39279
	.byte	10
	.byte	'_Ifx_SCU_OVCCON_Bits',0,4,220,5,16,4,11
	.byte	'CSEL0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'CSEL1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'CSEL2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,2
	.word	658
	.byte	13,0,2,35,0,11
	.byte	'OVSTRT',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'OVSTP',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'DCINVAL',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	641
	.byte	5,0,2,35,2,11
	.byte	'OVCONF',0,1
	.word	641
	.byte	1,7,2,35,3,11
	.byte	'POVCONF',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	641
	.byte	6,0,2,35,3,0,31
	.byte	'Ifx_SCU_OVCCON_Bits',0,4,233,5,3
	.word	39380
	.byte	10
	.byte	'_Ifx_SCU_OVCENABLE_Bits',0,4,236,5,16,4,11
	.byte	'OVEN0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'OVEN1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'OVEN2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	466
	.byte	29,0,2,35,0,0,31
	.byte	'Ifx_SCU_OVCENABLE_Bits',0,4,242,5,3
	.word	39647
	.byte	10
	.byte	'_Ifx_SCU_PDISC_Bits',0,4,245,5,16,4,11
	.byte	'PDIS0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	466
	.byte	30,0,2,35,0,0,31
	.byte	'Ifx_SCU_PDISC_Bits',0,4,250,5,3
	.word	39783
	.byte	10
	.byte	'_Ifx_SCU_PDR_Bits',0,4,253,5,16,4,11
	.byte	'PD0',0,1
	.word	641
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	641
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	466
	.byte	24,0,2,35,0,0,31
	.byte	'Ifx_SCU_PDR_Bits',0,4,132,6,3
	.word	39894
	.byte	10
	.byte	'_Ifx_SCU_PDRR_Bits',0,4,135,6,16,4,11
	.byte	'PDR0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'PDR1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'PDR2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'PDR3',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'PDR4',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'PDR5',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'PDR6',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'PDR7',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	466
	.byte	24,0,2,35,0,0,31
	.byte	'Ifx_SCU_PDRR_Bits',0,4,146,6,3
	.word	40027
	.byte	10
	.byte	'_Ifx_SCU_PLLCON0_Bits',0,4,149,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'MODEN',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	658
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	641
	.byte	7,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	641
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	641
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	641
	.byte	4,0,2,35,3,0,31
	.byte	'Ifx_SCU_PLLCON0_Bits',0,4,166,6,3
	.word	40230
	.byte	10
	.byte	'_Ifx_SCU_PLLCON1_Bits',0,4,169,6,16,4,11
	.byte	'K2DIV',0,1
	.word	641
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	641
	.byte	7,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	641
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	658
	.byte	9,0,2,35,2,0,31
	.byte	'Ifx_SCU_PLLCON1_Bits',0,4,177,6,3
	.word	40586
	.byte	10
	.byte	'_Ifx_SCU_PLLCON2_Bits',0,4,180,6,16,4,11
	.byte	'MODCFG',0,2
	.word	658
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	658
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_SCU_PLLCON2_Bits',0,4,184,6,3
	.word	40764
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON0_Bits',0,4,187,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	641
	.byte	2,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	658
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	641
	.byte	5,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	641
	.byte	2,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	641
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	641
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	641
	.byte	4,0,2,35,3,0,31
	.byte	'Ifx_SCU_PLLERAYCON0_Bits',0,4,204,6,3
	.word	40864
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON1_Bits',0,4,207,6,16,4,11
	.byte	'K2DIV',0,1
	.word	641
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	641
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	641
	.byte	4,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	641
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	658
	.byte	9,0,2,35,2,0,31
	.byte	'Ifx_SCU_PLLERAYCON1_Bits',0,4,215,6,3
	.word	41234
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYSTAT_Bits',0,4,218,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'PWDSTAT',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	466
	.byte	26,0,2,35,0,0,31
	.byte	'Ifx_SCU_PLLERAYSTAT_Bits',0,4,227,6,3
	.word	41420
	.byte	10
	.byte	'_Ifx_SCU_PLLSTAT_Bits',0,4,230,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'MODRUN',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	466
	.byte	24,0,2,35,0,0,31
	.byte	'Ifx_SCU_PLLSTAT_Bits',0,4,241,6,3
	.word	41618
	.byte	10
	.byte	'_Ifx_SCU_PMCSR_Bits',0,4,244,6,16,4,11
	.byte	'REQSLP',0,1
	.word	641
	.byte	2,6,2,35,0,11
	.byte	'SMUSLP',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	641
	.byte	5,0,2,35,0,11
	.byte	'PMST',0,1
	.word	641
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	466
	.byte	21,0,2,35,0,0,31
	.byte	'Ifx_SCU_PMCSR_Bits',0,4,251,6,3
	.word	41851
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR0_Bits',0,4,254,6,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'ESR1WKEN',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'PINAWKEN',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'PINBWKEN',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'ESR0DFEN',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'ESR0EDCON',0,1
	.word	641
	.byte	2,1,2,35,0,11
	.byte	'ESR1DFEN',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'ESR1EDCON',0,1
	.word	641
	.byte	2,6,2,35,1,11
	.byte	'PINADFEN',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'PINAEDCON',0,1
	.word	641
	.byte	2,3,2,35,1,11
	.byte	'PINBDFEN',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'PINBEDCON',0,1
	.word	641
	.byte	2,0,2,35,1,11
	.byte	'SCREN',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'STBYRAMSEL',0,1
	.word	641
	.byte	2,5,2,35,2,11
	.byte	'SCRCLKSEL',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	641
	.byte	1,3,2,35,2,11
	.byte	'TRISTEN',0,1
	.word	641
	.byte	1,2,2,35,2,11
	.byte	'TRISTREQ',0,1
	.word	641
	.byte	1,1,2,35,2,11
	.byte	'PORSTDF',0,1
	.word	641
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	641
	.byte	1,7,2,35,3,11
	.byte	'DCDCSYNC',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	641
	.byte	3,3,2,35,3,11
	.byte	'ESR0TRIST',0,1
	.word	641
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_PMSWCR0_Bits',0,4,153,7,3
	.word	42003
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR1_Bits',0,4,156,7,16,4,11
	.byte	'SCRSTEN',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'SCRSTREQ',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	641
	.byte	6,0,2,35,0,11
	.byte	'CPUIDLSEL',0,1
	.word	641
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'IRADIS',0,1
	.word	641
	.byte	1,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	641
	.byte	3,0,2,35,1,11
	.byte	'SCRCFG',0,1
	.word	641
	.byte	8,0,2,35,2,11
	.byte	'CPUSEL',0,1
	.word	641
	.byte	3,5,2,35,3,11
	.byte	'STBYEVEN',0,1
	.word	641
	.byte	1,4,2,35,3,11
	.byte	'STBYEV',0,1
	.word	641
	.byte	3,1,2,35,3,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_PMSWCR1_Bits',0,4,170,7,3
	.word	42570
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR2_Bits',0,4,173,7,16,4,11
	.byte	'SCRINT',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'BUSY',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'SCRECC',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'SCRWDT',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'SCRRST',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	641
	.byte	4,0,2,35,1,11
	.byte	'TCINT',0,1
	.word	641
	.byte	8,0,2,35,2,11
	.byte	'TCINTREQ',0,1
	.word	641
	.byte	1,7,2,35,3,11
	.byte	'SMURST',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'RST',0,1
	.word	641
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	641
	.byte	4,1,2,35,3,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_PMSWCR2_Bits',0,4,187,7,3
	.word	42864
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTAT_Bits',0,4,190,7,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKP',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUN',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'PINAWKP',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUN',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'PINBWKP',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUN',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'PORSTDF',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'HWCFGEVR',0,1
	.word	641
	.byte	3,3,2,35,1,11
	.byte	'STBYRAM',0,1
	.word	641
	.byte	2,1,2,35,1,11
	.byte	'TRIST',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'SCRST',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'SCRWKP',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'SCR',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'ESR1WKEN',0,1
	.word	641
	.byte	1,3,2,35,2,11
	.byte	'PINAWKEN',0,1
	.word	641
	.byte	1,2,2,35,2,11
	.byte	'PINBWKEN',0,1
	.word	641
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	658
	.byte	4,5,2,35,2,11
	.byte	'ESR0TRIST',0,1
	.word	641
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	641
	.byte	4,0,2,35,3,0,31
	.byte	'Ifx_SCU_PMSWSTAT_Bits',0,4,214,7,3
	.word	43142
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTATCLR_Bits',0,4,217,7,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKPCLR',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUNCLR',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'PINAWKPCLR',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUNCLR',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'PINBWKPCLR',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUNCLR',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'SCRSTCLR',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'SCRWKPCLR',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	658
	.byte	14,0,2,35,2,0,31
	.byte	'Ifx_SCU_PMSWSTATCLR_Bits',0,4,230,7,3
	.word	43638
	.byte	10
	.byte	'_Ifx_SCU_RSTCON2_Bits',0,4,233,7,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'CLRC',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	658
	.byte	10,4,2,35,0,11
	.byte	'CSS0',0,1
	.word	641
	.byte	1,3,2,35,1,11
	.byte	'CSS1',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'CSS2',0,1
	.word	641
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'USRINFO',0,2
	.word	658
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_SCU_RSTCON2_Bits',0,4,243,7,3
	.word	43951
	.byte	10
	.byte	'_Ifx_SCU_RSTCON_Bits',0,4,246,7,16,4,11
	.byte	'ESR0',0,1
	.word	641
	.byte	2,6,2,35,0,11
	.byte	'ESR1',0,1
	.word	641
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	641
	.byte	2,2,2,35,0,11
	.byte	'SMU',0,1
	.word	641
	.byte	2,0,2,35,0,11
	.byte	'SW',0,1
	.word	641
	.byte	2,6,2,35,1,11
	.byte	'STM0',0,1
	.word	641
	.byte	2,4,2,35,1,11
	.byte	'STM1',0,1
	.word	641
	.byte	2,2,2,35,1,11
	.byte	'STM2',0,1
	.word	641
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	658
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_SCU_RSTCON_Bits',0,4,129,8,3
	.word	44160
	.byte	10
	.byte	'_Ifx_SCU_RSTSTAT_Bits',0,4,132,8,16,4,11
	.byte	'ESR0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'ESR1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'SMU',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'SW',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'STM0',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'STM1',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'STM2',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'PORST',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'CB0',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'CB1',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'CB3',0,1
	.word	641
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	641
	.byte	2,1,2,35,2,11
	.byte	'EVR13',0,1
	.word	641
	.byte	1,0,2,35,2,11
	.byte	'EVR33',0,1
	.word	641
	.byte	1,7,2,35,3,11
	.byte	'SWD',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	641
	.byte	2,4,2,35,3,11
	.byte	'STBYR',0,1
	.word	641
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	641
	.byte	3,0,2,35,3,0,31
	.byte	'Ifx_SCU_RSTSTAT_Bits',0,4,155,8,3
	.word	44371
	.byte	10
	.byte	'_Ifx_SCU_SAFECON_Bits',0,4,158,8,16,4,11
	.byte	'HBT',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	466
	.byte	31,0,2,35,0,0,31
	.byte	'Ifx_SCU_SAFECON_Bits',0,4,162,8,3
	.word	44803
	.byte	10
	.byte	'_Ifx_SCU_STSTAT_Bits',0,4,165,8,16,4,11
	.byte	'HWCFG',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'FTM',0,1
	.word	641
	.byte	7,1,2,35,1,11
	.byte	'MODE',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'FCBAE',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'LUDIS',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'TRSTL',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'SPDEN',0,1
	.word	641
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	641
	.byte	3,0,2,35,2,11
	.byte	'RAMINT',0,1
	.word	641
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	641
	.byte	7,0,2,35,3,0,31
	.byte	'Ifx_SCU_STSTAT_Bits',0,4,178,8,3
	.word	44899
	.byte	10
	.byte	'_Ifx_SCU_SWRSTCON_Bits',0,4,181,8,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'SWRSTREQ',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	466
	.byte	30,0,2,35,0,0,31
	.byte	'Ifx_SCU_SWRSTCON_Bits',0,4,186,8,3
	.word	45159
	.byte	10
	.byte	'_Ifx_SCU_SYSCON_Bits',0,4,189,8,16,4,11
	.byte	'CCTRIG0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'RAMINTM',0,1
	.word	641
	.byte	2,4,2,35,0,11
	.byte	'SETLUDIS',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	641
	.byte	3,0,2,35,0,11
	.byte	'DATM',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,4
	.word	466
	.byte	23,0,2,35,0,0,31
	.byte	'Ifx_SCU_SYSCON_Bits',0,4,198,8,3
	.word	45284
	.byte	10
	.byte	'_Ifx_SCU_TRAPCLR_Bits',0,4,201,8,16,4,11
	.byte	'ESR0T',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	466
	.byte	28,0,2,35,0,0,31
	.byte	'Ifx_SCU_TRAPCLR_Bits',0,4,208,8,3
	.word	45481
	.byte	10
	.byte	'_Ifx_SCU_TRAPDIS_Bits',0,4,211,8,16,4,11
	.byte	'ESR0T',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	466
	.byte	28,0,2,35,0,0,31
	.byte	'Ifx_SCU_TRAPDIS_Bits',0,4,218,8,3
	.word	45634
	.byte	10
	.byte	'_Ifx_SCU_TRAPSET_Bits',0,4,221,8,16,4,11
	.byte	'ESR0T',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	466
	.byte	28,0,2,35,0,0,31
	.byte	'Ifx_SCU_TRAPSET_Bits',0,4,228,8,3
	.word	45787
	.byte	10
	.byte	'_Ifx_SCU_TRAPSTAT_Bits',0,4,231,8,16,4,11
	.byte	'ESR0T',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	466
	.byte	28,0,2,35,0,0,31
	.byte	'Ifx_SCU_TRAPSTAT_Bits',0,4,238,8,3
	.word	45940
	.byte	31
	.byte	'Ifx_SCU_WDTCPU_CON0_Bits',0,4,247,8,3
	.word	505
	.byte	31
	.byte	'Ifx_SCU_WDTCPU_CON1_Bits',0,4,134,9,3
	.word	680
	.byte	31
	.byte	'Ifx_SCU_WDTCPU_SR_Bits',0,4,150,9,3
	.word	924
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON0_Bits',0,4,153,9,16,4,11
	.byte	'ENDINIT',0,4
	.word	489
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	489
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	489
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	489
	.byte	16,0,2,35,0,0,31
	.byte	'Ifx_SCU_WDTS_CON0_Bits',0,4,159,9,3
	.word	46195
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON1_Bits',0,4,162,9,16,4,11
	.byte	'CLRIRF',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'IR0',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	641
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	658
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_SCU_WDTS_CON1_Bits',0,4,175,9,3
	.word	46321
	.byte	10
	.byte	'_Ifx_SCU_WDTS_SR_Bits',0,4,178,9,16,4,11
	.byte	'AE',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	641
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	658
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_SCU_WDTS_SR_Bits',0,4,191,9,3
	.word	46573
	.byte	12,4,199,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28605
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_ACCEN0',0,4,204,9,3
	.word	46792
	.byte	12,4,207,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29162
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_ACCEN1',0,4,212,9,3
	.word	46856
	.byte	12,4,215,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29239
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_ARSTDIS',0,4,220,9,3
	.word	46920
	.byte	12,4,223,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29375
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_CCUCON0',0,4,228,9,3
	.word	46985
	.byte	12,4,231,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29655
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_CCUCON1',0,4,236,9,3
	.word	47050
	.byte	12,4,239,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29893
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_CCUCON2',0,4,244,9,3
	.word	47115
	.byte	12,4,247,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30021
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_CCUCON3',0,4,252,9,3
	.word	47180
	.byte	12,4,255,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30264
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_CCUCON4',0,4,132,10,3
	.word	47245
	.byte	12,4,135,10,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30499
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_CCUCON5',0,4,140,10,3
	.word	47310
	.byte	12,4,143,10,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30627
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_CCUCON6',0,4,148,10,3
	.word	47375
	.byte	12,4,151,10,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30727
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_CCUCON7',0,4,156,10,3
	.word	47440
	.byte	12,4,159,10,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30827
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_CHIPID',0,4,164,10,3
	.word	47505
	.byte	12,4,167,10,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31035
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_DTSCON',0,4,172,10,3
	.word	47569
	.byte	12,4,175,10,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31200
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_DTSLIM',0,4,180,10,3
	.word	47633
	.byte	12,4,183,10,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31383
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_DTSSTAT',0,4,188,10,3
	.word	47697
	.byte	12,4,191,10,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31537
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EICR',0,4,196,10,3
	.word	47762
	.byte	12,4,199,10,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31901
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EIFR',0,4,204,10,3
	.word	47824
	.byte	12,4,207,10,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32112
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EMSR',0,4,212,10,3
	.word	47886
	.byte	12,4,215,10,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32364
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_ESRCFG',0,4,220,10,3
	.word	47948
	.byte	12,4,223,10,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32482
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_ESROCFG',0,4,228,10,3
	.word	48012
	.byte	12,4,231,10,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32593
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVR13CON',0,4,236,10,3
	.word	48077
	.byte	12,4,239,10,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32756
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVR33CON',0,4,244,10,3
	.word	48143
	.byte	12,4,247,10,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32919
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRADCSTAT',0,4,252,10,3
	.word	48209
	.byte	12,4,255,10,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33077
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRDVSTAT',0,4,132,11,3
	.word	48277
	.byte	12,4,135,11,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33242
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRMONCTRL',0,4,140,11,3
	.word	48344
	.byte	12,4,143,11,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33571
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVROSCCTRL',0,4,148,11,3
	.word	48412
	.byte	12,4,151,11,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33792
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVROVMON',0,4,156,11,3
	.word	48480
	.byte	12,4,159,11,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33955
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRRSTCON',0,4,164,11,3
	.word	48546
	.byte	12,4,167,11,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34227
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRSDCOEFF1',0,4,172,11,3
	.word	48613
	.byte	12,4,175,11,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34380
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRSDCOEFF2',0,4,180,11,3
	.word	48682
	.byte	12,4,183,11,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34536
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRSDCOEFF3',0,4,188,11,3
	.word	48751
	.byte	12,4,191,11,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34698
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRSDCOEFF4',0,4,196,11,3
	.word	48820
	.byte	12,4,199,11,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34841
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRSDCOEFF5',0,4,204,11,3
	.word	48889
	.byte	12,4,207,11,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35006
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRSDCOEFF6',0,4,212,11,3
	.word	48958
	.byte	12,4,215,11,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35151
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRSDCTRL1',0,4,220,11,3
	.word	49027
	.byte	12,4,223,11,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35332
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRSDCTRL2',0,4,228,11,3
	.word	49095
	.byte	12,4,231,11,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35506
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRSDCTRL3',0,4,236,11,3
	.word	49163
	.byte	12,4,239,11,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35666
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRSDCTRL4',0,4,244,11,3
	.word	49231
	.byte	12,4,247,11,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35810
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRSTAT',0,4,252,11,3
	.word	49299
	.byte	12,4,255,11,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36084
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRTRIM',0,4,132,12,3
	.word	49364
	.byte	12,4,135,12,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36223
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRUVMON',0,4,140,12,3
	.word	49429
	.byte	12,4,143,12,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36386
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EXTCON',0,4,148,12,3
	.word	49495
	.byte	12,4,151,12,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36604
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_FDR',0,4,156,12,3
	.word	49559
	.byte	12,4,159,12,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36767
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_FMR',0,4,164,12,3
	.word	49620
	.byte	12,4,167,12,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37103
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_ID',0,4,172,12,3
	.word	49681
	.byte	12,4,175,12,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37210
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_IGCR',0,4,180,12,3
	.word	49741
	.byte	12,4,183,12,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37662
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_IN',0,4,188,12,3
	.word	49803
	.byte	12,4,191,12,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37761
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_IOCR',0,4,196,12,3
	.word	49863
	.byte	12,4,199,12,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37911
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_LBISTCTRL0',0,4,204,12,3
	.word	49925
	.byte	12,4,207,12,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38060
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_LBISTCTRL1',0,4,212,12,3
	.word	49993
	.byte	12,4,215,12,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38221
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_LBISTCTRL2',0,4,220,12,3
	.word	50061
	.byte	12,4,223,12,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38351
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_LCLCON',0,4,228,12,3
	.word	50129
	.byte	12,4,231,12,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38483
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_LCLTEST',0,4,236,12,3
	.word	50193
	.byte	12,4,239,12,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38598
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_MANID',0,4,244,12,3
	.word	50258
	.byte	12,4,247,12,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38709
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_OMR',0,4,252,12,3
	.word	50321
	.byte	12,4,255,12,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38867
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_OSCCON',0,4,132,13,3
	.word	50382
	.byte	12,4,135,13,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39279
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_OUT',0,4,140,13,3
	.word	50446
	.byte	12,4,143,13,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39380
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_OVCCON',0,4,148,13,3
	.word	50507
	.byte	12,4,151,13,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39647
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_OVCENABLE',0,4,156,13,3
	.word	50571
	.byte	12,4,159,13,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39783
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PDISC',0,4,164,13,3
	.word	50638
	.byte	12,4,167,13,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39894
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PDR',0,4,172,13,3
	.word	50701
	.byte	12,4,175,13,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40027
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PDRR',0,4,180,13,3
	.word	50762
	.byte	12,4,183,13,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40230
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PLLCON0',0,4,188,13,3
	.word	50824
	.byte	12,4,191,13,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40586
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PLLCON1',0,4,196,13,3
	.word	50889
	.byte	12,4,199,13,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40764
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PLLCON2',0,4,204,13,3
	.word	50954
	.byte	12,4,207,13,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40864
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PLLERAYCON0',0,4,212,13,3
	.word	51019
	.byte	12,4,215,13,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41234
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PLLERAYCON1',0,4,220,13,3
	.word	51088
	.byte	12,4,223,13,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41420
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PLLERAYSTAT',0,4,228,13,3
	.word	51157
	.byte	12,4,231,13,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41618
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PLLSTAT',0,4,236,13,3
	.word	51226
	.byte	12,4,239,13,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41851
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PMCSR',0,4,244,13,3
	.word	51291
	.byte	12,4,247,13,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42003
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PMSWCR0',0,4,252,13,3
	.word	51354
	.byte	12,4,255,13,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42570
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PMSWCR1',0,4,132,14,3
	.word	51419
	.byte	12,4,135,14,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42864
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PMSWCR2',0,4,140,14,3
	.word	51484
	.byte	12,4,143,14,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43142
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PMSWSTAT',0,4,148,14,3
	.word	51549
	.byte	12,4,151,14,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43638
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PMSWSTATCLR',0,4,156,14,3
	.word	51615
	.byte	12,4,159,14,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44160
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_RSTCON',0,4,164,14,3
	.word	51684
	.byte	12,4,167,14,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43951
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_RSTCON2',0,4,172,14,3
	.word	51748
	.byte	12,4,175,14,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44371
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_RSTSTAT',0,4,180,14,3
	.word	51813
	.byte	12,4,183,14,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44803
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_SAFECON',0,4,188,14,3
	.word	51878
	.byte	12,4,191,14,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44899
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_STSTAT',0,4,196,14,3
	.word	51943
	.byte	12,4,199,14,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45159
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_SWRSTCON',0,4,204,14,3
	.word	52007
	.byte	12,4,207,14,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45284
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_SYSCON',0,4,212,14,3
	.word	52073
	.byte	12,4,215,14,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45481
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_TRAPCLR',0,4,220,14,3
	.word	52137
	.byte	12,4,223,14,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45634
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_TRAPDIS',0,4,228,14,3
	.word	52202
	.byte	12,4,231,14,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45787
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_TRAPSET',0,4,236,14,3
	.word	52267
	.byte	12,4,239,14,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45940
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_TRAPSTAT',0,4,244,14,3
	.word	52332
	.byte	31
	.byte	'Ifx_SCU_WDTCPU_CON0',0,4,252,14,3
	.word	601
	.byte	31
	.byte	'Ifx_SCU_WDTCPU_CON1',0,4,132,15,3
	.word	884
	.byte	31
	.byte	'Ifx_SCU_WDTCPU_SR',0,4,140,15,3
	.word	1115
	.byte	12,4,143,15,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46195
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_WDTS_CON0',0,4,148,15,3
	.word	52483
	.byte	12,4,151,15,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46321
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_WDTS_CON1',0,4,156,15,3
	.word	52550
	.byte	12,4,159,15,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46573
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_WDTS_SR',0,4,164,15,3
	.word	52617
	.byte	14
	.word	1155
	.byte	31
	.byte	'Ifx_SCU_WDTCPU',0,4,180,15,3
	.word	52682
	.byte	10
	.byte	'_Ifx_SCU_WDTS',0,4,183,15,25,12,13
	.byte	'CON0',0
	.word	52483
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	52550
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	52617
	.byte	4,2,35,8,0,14
	.word	52711
	.byte	31
	.byte	'Ifx_SCU_WDTS',0,4,188,15,3
	.word	52772
	.byte	18,8
	.word	47948
	.byte	19,1,0,18,20
	.word	641
	.byte	19,19,0,18,8
	.word	51291
	.byte	19,1,0,14
	.word	52711
	.byte	18,24
	.word	1155
	.byte	19,1,0,14
	.word	52831
	.byte	18,16
	.word	641
	.byte	19,15,0,18,28
	.word	641
	.byte	19,27,0,18,40
	.word	641
	.byte	19,39,0,18,16
	.word	47762
	.byte	19,3,0,18,16
	.word	49741
	.byte	19,3,0,18,180,3
	.word	641
	.byte	19,179,3,0,10
	.byte	'_Ifx_SCU',0,4,201,15,25,128,8,13
	.byte	'reserved_0',0
	.word	4831
	.byte	8,2,35,0,13
	.byte	'ID',0
	.word	49681
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	3012
	.byte	4,2,35,12,13
	.byte	'OSCCON',0
	.word	50382
	.byte	4,2,35,16,13
	.byte	'PLLSTAT',0
	.word	51226
	.byte	4,2,35,20,13
	.byte	'PLLCON0',0
	.word	50824
	.byte	4,2,35,24,13
	.byte	'PLLCON1',0
	.word	50889
	.byte	4,2,35,28,13
	.byte	'PLLCON2',0
	.word	50954
	.byte	4,2,35,32,13
	.byte	'PLLERAYSTAT',0
	.word	51157
	.byte	4,2,35,36,13
	.byte	'PLLERAYCON0',0
	.word	51019
	.byte	4,2,35,40,13
	.byte	'PLLERAYCON1',0
	.word	51088
	.byte	4,2,35,44,13
	.byte	'CCUCON0',0
	.word	46985
	.byte	4,2,35,48,13
	.byte	'CCUCON1',0
	.word	47050
	.byte	4,2,35,52,13
	.byte	'FDR',0
	.word	49559
	.byte	4,2,35,56,13
	.byte	'EXTCON',0
	.word	49495
	.byte	4,2,35,60,13
	.byte	'CCUCON2',0
	.word	47115
	.byte	4,2,35,64,13
	.byte	'CCUCON3',0
	.word	47180
	.byte	4,2,35,68,13
	.byte	'CCUCON4',0
	.word	47245
	.byte	4,2,35,72,13
	.byte	'CCUCON5',0
	.word	47310
	.byte	4,2,35,76,13
	.byte	'RSTSTAT',0
	.word	51813
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	3012
	.byte	4,2,35,84,13
	.byte	'RSTCON',0
	.word	51684
	.byte	4,2,35,88,13
	.byte	'ARSTDIS',0
	.word	46920
	.byte	4,2,35,92,13
	.byte	'SWRSTCON',0
	.word	52007
	.byte	4,2,35,96,13
	.byte	'RSTCON2',0
	.word	51748
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	3012
	.byte	4,2,35,104,13
	.byte	'EVRRSTCON',0
	.word	48546
	.byte	4,2,35,108,13
	.byte	'ESRCFG',0
	.word	52799
	.byte	8,2,35,112,13
	.byte	'ESROCFG',0
	.word	48012
	.byte	4,2,35,120,13
	.byte	'SYSCON',0
	.word	52073
	.byte	4,2,35,124,13
	.byte	'CCUCON6',0
	.word	47375
	.byte	4,3,35,128,1,13
	.byte	'CCUCON7',0
	.word	47440
	.byte	4,3,35,132,1,13
	.byte	'reserved_88',0
	.word	52808
	.byte	20,3,35,136,1,13
	.byte	'PDR',0
	.word	50701
	.byte	4,3,35,156,1,13
	.byte	'IOCR',0
	.word	49863
	.byte	4,3,35,160,1,13
	.byte	'OUT',0
	.word	50446
	.byte	4,3,35,164,1,13
	.byte	'OMR',0
	.word	50321
	.byte	4,3,35,168,1,13
	.byte	'IN',0
	.word	49803
	.byte	4,3,35,172,1,13
	.byte	'EVRSTAT',0
	.word	49299
	.byte	4,3,35,176,1,13
	.byte	'EVRDVSTAT',0
	.word	48277
	.byte	4,3,35,180,1,13
	.byte	'EVR13CON',0
	.word	48077
	.byte	4,3,35,184,1,13
	.byte	'EVR33CON',0
	.word	48143
	.byte	4,3,35,188,1,13
	.byte	'STSTAT',0
	.word	51943
	.byte	4,3,35,192,1,13
	.byte	'reserved_C4',0
	.word	3012
	.byte	4,3,35,196,1,13
	.byte	'PMSWCR0',0
	.word	51354
	.byte	4,3,35,200,1,13
	.byte	'PMSWSTAT',0
	.word	51549
	.byte	4,3,35,204,1,13
	.byte	'PMSWSTATCLR',0
	.word	51615
	.byte	4,3,35,208,1,13
	.byte	'PMCSR',0
	.word	52817
	.byte	8,3,35,212,1,13
	.byte	'reserved_DC',0
	.word	3012
	.byte	4,3,35,220,1,13
	.byte	'DTSSTAT',0
	.word	47697
	.byte	4,3,35,224,1,13
	.byte	'DTSCON',0
	.word	47569
	.byte	4,3,35,228,1,13
	.byte	'PMSWCR1',0
	.word	51419
	.byte	4,3,35,232,1,13
	.byte	'PMSWCR2',0
	.word	51484
	.byte	4,3,35,236,1,13
	.byte	'WDTS',0
	.word	52826
	.byte	12,3,35,240,1,13
	.byte	'EMSR',0
	.word	47886
	.byte	4,3,35,252,1,13
	.byte	'WDTCPU',0
	.word	52840
	.byte	24,3,35,128,2,13
	.byte	'reserved_118',0
	.word	5171
	.byte	12,3,35,152,2,13
	.byte	'TRAPSTAT',0
	.word	52332
	.byte	4,3,35,164,2,13
	.byte	'TRAPSET',0
	.word	52267
	.byte	4,3,35,168,2,13
	.byte	'TRAPCLR',0
	.word	52137
	.byte	4,3,35,172,2,13
	.byte	'TRAPDIS',0
	.word	52202
	.byte	4,3,35,176,2,13
	.byte	'reserved_134',0
	.word	3012
	.byte	4,3,35,180,2,13
	.byte	'LCLCON1',0
	.word	50129
	.byte	4,3,35,184,2,13
	.byte	'LCLTEST',0
	.word	50193
	.byte	4,3,35,188,2,13
	.byte	'CHIPID',0
	.word	47505
	.byte	4,3,35,192,2,13
	.byte	'MANID',0
	.word	50258
	.byte	4,3,35,196,2,13
	.byte	'reserved_148',0
	.word	4831
	.byte	8,3,35,200,2,13
	.byte	'SAFECON',0
	.word	51878
	.byte	4,3,35,208,2,13
	.byte	'reserved_154',0
	.word	52845
	.byte	16,3,35,212,2,13
	.byte	'LBISTCTRL0',0
	.word	49925
	.byte	4,3,35,228,2,13
	.byte	'LBISTCTRL1',0
	.word	49993
	.byte	4,3,35,232,2,13
	.byte	'LBISTCTRL2',0
	.word	50061
	.byte	4,3,35,236,2,13
	.byte	'reserved_170',0
	.word	52854
	.byte	28,3,35,240,2,13
	.byte	'PDISC',0
	.word	50638
	.byte	4,3,35,140,3,13
	.byte	'reserved_190',0
	.word	4831
	.byte	8,3,35,144,3,13
	.byte	'EVRTRIM',0
	.word	49364
	.byte	4,3,35,152,3,13
	.byte	'EVRADCSTAT',0
	.word	48209
	.byte	4,3,35,156,3,13
	.byte	'EVRUVMON',0
	.word	49429
	.byte	4,3,35,160,3,13
	.byte	'EVROVMON',0
	.word	48480
	.byte	4,3,35,164,3,13
	.byte	'EVRMONCTRL',0
	.word	48344
	.byte	4,3,35,168,3,13
	.byte	'reserved_1AC',0
	.word	3012
	.byte	4,3,35,172,3,13
	.byte	'EVRSDCTRL1',0
	.word	49027
	.byte	4,3,35,176,3,13
	.byte	'EVRSDCTRL2',0
	.word	49095
	.byte	4,3,35,180,3,13
	.byte	'EVRSDCTRL3',0
	.word	49163
	.byte	4,3,35,184,3,13
	.byte	'EVRSDCTRL4',0
	.word	49231
	.byte	4,3,35,188,3,13
	.byte	'EVRSDCOEFF1',0
	.word	48613
	.byte	4,3,35,192,3,13
	.byte	'EVRSDCOEFF2',0
	.word	48682
	.byte	4,3,35,196,3,13
	.byte	'EVRSDCOEFF3',0
	.word	48751
	.byte	4,3,35,200,3,13
	.byte	'EVRSDCOEFF4',0
	.word	48820
	.byte	4,3,35,204,3,13
	.byte	'EVRSDCOEFF5',0
	.word	48889
	.byte	4,3,35,208,3,13
	.byte	'EVRSDCOEFF6',0
	.word	48958
	.byte	4,3,35,212,3,13
	.byte	'EVROSCCTRL',0
	.word	48412
	.byte	4,3,35,216,3,13
	.byte	'reserved_1DC',0
	.word	3012
	.byte	4,3,35,220,3,13
	.byte	'OVCENABLE',0
	.word	50571
	.byte	4,3,35,224,3,13
	.byte	'OVCCON',0
	.word	50507
	.byte	4,3,35,228,3,13
	.byte	'reserved_1E8',0
	.word	52863
	.byte	40,3,35,232,3,13
	.byte	'EICR',0
	.word	52872
	.byte	16,3,35,144,4,13
	.byte	'EIFR',0
	.word	47824
	.byte	4,3,35,160,4,13
	.byte	'FMR',0
	.word	49620
	.byte	4,3,35,164,4,13
	.byte	'PDRR',0
	.word	50762
	.byte	4,3,35,168,4,13
	.byte	'IGCR',0
	.word	52881
	.byte	16,3,35,172,4,13
	.byte	'reserved_23C',0
	.word	3012
	.byte	4,3,35,188,4,13
	.byte	'DTSLIM',0
	.word	47633
	.byte	4,3,35,192,4,13
	.byte	'reserved_244',0
	.word	52890
	.byte	180,3,3,35,196,4,13
	.byte	'ACCEN1',0
	.word	46856
	.byte	4,3,35,248,7,13
	.byte	'ACCEN0',0
	.word	46792
	.byte	4,3,35,252,7,0,14
	.word	52901
	.byte	31
	.byte	'Ifx_SCU',0,4,181,16,3
	.word	54891
	.byte	10
	.byte	'_Ifx_CPU_A_Bits',0,31,45,16,4,11
	.byte	'ADDR',0,4
	.word	489
	.byte	32,0,2,35,0,0,31
	.byte	'Ifx_CPU_A_Bits',0,31,48,3
	.word	54913
	.byte	10
	.byte	'_Ifx_CPU_BIV_Bits',0,31,51,16,4,11
	.byte	'VSS',0,4
	.word	489
	.byte	1,31,2,35,0,11
	.byte	'BIV',0,4
	.word	489
	.byte	31,0,2,35,0,0,31
	.byte	'Ifx_CPU_BIV_Bits',0,31,55,3
	.word	54974
	.byte	10
	.byte	'_Ifx_CPU_BTV_Bits',0,31,58,16,4,11
	.byte	'reserved_0',0,4
	.word	489
	.byte	1,31,2,35,0,11
	.byte	'BTV',0,4
	.word	489
	.byte	31,0,2,35,0,0,31
	.byte	'Ifx_CPU_BTV_Bits',0,31,62,3
	.word	55053
	.byte	10
	.byte	'_Ifx_CPU_CCNT_Bits',0,31,65,16,4,11
	.byte	'CountValue',0,4
	.word	489
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	489
	.byte	1,0,2,35,0,0,31
	.byte	'Ifx_CPU_CCNT_Bits',0,31,69,3
	.word	55139
	.byte	10
	.byte	'_Ifx_CPU_CCTRL_Bits',0,31,72,16,4,11
	.byte	'CM',0,4
	.word	489
	.byte	1,31,2,35,0,11
	.byte	'CE',0,4
	.word	489
	.byte	1,30,2,35,0,11
	.byte	'M1',0,4
	.word	489
	.byte	3,27,2,35,0,11
	.byte	'M2',0,4
	.word	489
	.byte	3,24,2,35,0,11
	.byte	'M3',0,4
	.word	489
	.byte	3,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	489
	.byte	21,0,2,35,0,0,31
	.byte	'Ifx_CPU_CCTRL_Bits',0,31,80,3
	.word	55228
	.byte	10
	.byte	'_Ifx_CPU_COMPAT_Bits',0,31,83,16,4,11
	.byte	'reserved_0',0,4
	.word	489
	.byte	3,29,2,35,0,11
	.byte	'RM',0,4
	.word	489
	.byte	1,28,2,35,0,11
	.byte	'SP',0,4
	.word	489
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	489
	.byte	27,0,2,35,0,0,31
	.byte	'Ifx_CPU_COMPAT_Bits',0,31,89,3
	.word	55374
	.byte	10
	.byte	'_Ifx_CPU_CORE_ID_Bits',0,31,92,16,4,11
	.byte	'CORE_ID',0,4
	.word	489
	.byte	3,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	489
	.byte	29,0,2,35,0,0,31
	.byte	'Ifx_CPU_CORE_ID_Bits',0,31,96,3
	.word	55501
	.byte	10
	.byte	'_Ifx_CPU_CPR_L_Bits',0,31,99,16,4,11
	.byte	'reserved_0',0,4
	.word	489
	.byte	3,29,2,35,0,11
	.byte	'LOWBND',0,4
	.word	489
	.byte	29,0,2,35,0,0,31
	.byte	'Ifx_CPU_CPR_L_Bits',0,31,103,3
	.word	55599
	.byte	10
	.byte	'_Ifx_CPU_CPR_U_Bits',0,31,106,16,4,11
	.byte	'reserved_0',0,4
	.word	489
	.byte	3,29,2,35,0,11
	.byte	'UPPBND',0,4
	.word	489
	.byte	29,0,2,35,0,0,31
	.byte	'Ifx_CPU_CPR_U_Bits',0,31,110,3
	.word	55692
	.byte	10
	.byte	'_Ifx_CPU_CPU_ID_Bits',0,31,113,16,4,11
	.byte	'MODREV',0,4
	.word	489
	.byte	8,24,2,35,0,11
	.byte	'MOD_32B',0,4
	.word	489
	.byte	8,16,2,35,0,11
	.byte	'MOD',0,4
	.word	489
	.byte	16,0,2,35,0,0,31
	.byte	'Ifx_CPU_CPU_ID_Bits',0,31,118,3
	.word	55785
	.byte	10
	.byte	'_Ifx_CPU_CPXE_Bits',0,31,121,16,4,11
	.byte	'XE',0,4
	.word	489
	.byte	8,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	489
	.byte	24,0,2,35,0,0,31
	.byte	'Ifx_CPU_CPXE_Bits',0,31,125,3
	.word	55892
	.byte	10
	.byte	'_Ifx_CPU_CREVT_Bits',0,31,128,1,16,4,11
	.byte	'EVTA',0,4
	.word	489
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	489
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	489
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	489
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	489
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	489
	.byte	24,0,2,35,0,0,31
	.byte	'Ifx_CPU_CREVT_Bits',0,31,136,1,3
	.word	55979
	.byte	10
	.byte	'_Ifx_CPU_CUS_ID_Bits',0,31,139,1,16,4,11
	.byte	'CID',0,4
	.word	489
	.byte	3,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	489
	.byte	29,0,2,35,0,0,31
	.byte	'Ifx_CPU_CUS_ID_Bits',0,31,143,1,3
	.word	56133
	.byte	10
	.byte	'_Ifx_CPU_D_Bits',0,31,146,1,16,4,11
	.byte	'DATA',0,4
	.word	489
	.byte	32,0,2,35,0,0,31
	.byte	'Ifx_CPU_D_Bits',0,31,149,1,3
	.word	56227
	.byte	10
	.byte	'_Ifx_CPU_DATR_Bits',0,31,152,1,16,4,11
	.byte	'reserved_0',0,4
	.word	489
	.byte	3,29,2,35,0,11
	.byte	'SBE',0,4
	.word	489
	.byte	1,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	489
	.byte	5,23,2,35,0,11
	.byte	'CWE',0,4
	.word	489
	.byte	1,22,2,35,0,11
	.byte	'CFE',0,4
	.word	489
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	489
	.byte	3,18,2,35,0,11
	.byte	'SOE',0,4
	.word	489
	.byte	1,17,2,35,0,11
	.byte	'SME',0,4
	.word	489
	.byte	1,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	489
	.byte	16,0,2,35,0,0,31
	.byte	'Ifx_CPU_DATR_Bits',0,31,163,1,3
	.word	56290
	.byte	10
	.byte	'_Ifx_CPU_DBGSR_Bits',0,31,166,1,16,4,11
	.byte	'DE',0,4
	.word	489
	.byte	1,31,2,35,0,11
	.byte	'HALT',0,4
	.word	489
	.byte	2,29,2,35,0,11
	.byte	'SIH',0,4
	.word	489
	.byte	1,28,2,35,0,11
	.byte	'SUSP',0,4
	.word	489
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	489
	.byte	1,26,2,35,0,11
	.byte	'PREVSUSP',0,4
	.word	489
	.byte	1,25,2,35,0,11
	.byte	'PEVT',0,4
	.word	489
	.byte	1,24,2,35,0,11
	.byte	'EVTSRC',0,4
	.word	489
	.byte	5,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	489
	.byte	19,0,2,35,0,0,31
	.byte	'Ifx_CPU_DBGSR_Bits',0,31,177,1,3
	.word	56508
	.byte	10
	.byte	'_Ifx_CPU_DBGTCR_Bits',0,31,180,1,16,4,11
	.byte	'DTA',0,4
	.word	489
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	489
	.byte	31,0,2,35,0,0,31
	.byte	'Ifx_CPU_DBGTCR_Bits',0,31,184,1,3
	.word	56723
	.byte	10
	.byte	'_Ifx_CPU_DCON0_Bits',0,31,187,1,16,4,11
	.byte	'reserved_0',0,4
	.word	489
	.byte	1,31,2,35,0,11
	.byte	'DCBYP',0,4
	.word	489
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	489
	.byte	30,0,2,35,0,0,31
	.byte	'Ifx_CPU_DCON0_Bits',0,31,192,1,3
	.word	56817
	.byte	10
	.byte	'_Ifx_CPU_DCON2_Bits',0,31,195,1,16,4,11
	.byte	'DCACHE_SZE',0,4
	.word	489
	.byte	16,16,2,35,0,11
	.byte	'DSCRATCH_SZE',0,4
	.word	489
	.byte	16,0,2,35,0,0,31
	.byte	'Ifx_CPU_DCON2_Bits',0,31,199,1,3
	.word	56933
	.byte	10
	.byte	'_Ifx_CPU_DCX_Bits',0,31,202,1,16,4,11
	.byte	'reserved_0',0,4
	.word	489
	.byte	6,26,2,35,0,11
	.byte	'DCXValue',0,4
	.word	489
	.byte	26,0,2,35,0,0,31
	.byte	'Ifx_CPU_DCX_Bits',0,31,206,1,3
	.word	57034
	.byte	10
	.byte	'_Ifx_CPU_DEADD_Bits',0,31,209,1,16,4,11
	.byte	'ERROR_ADDRESS',0,4
	.word	489
	.byte	32,0,2,35,0,0,31
	.byte	'Ifx_CPU_DEADD_Bits',0,31,212,1,3
	.word	57127
	.byte	10
	.byte	'_Ifx_CPU_DIEAR_Bits',0,31,215,1,16,4,11
	.byte	'TA',0,4
	.word	489
	.byte	32,0,2,35,0,0,31
	.byte	'Ifx_CPU_DIEAR_Bits',0,31,218,1,3
	.word	57207
	.byte	10
	.byte	'_Ifx_CPU_DIETR_Bits',0,31,221,1,16,4,11
	.byte	'IED',0,4
	.word	489
	.byte	1,31,2,35,0,11
	.byte	'IE_T',0,4
	.word	489
	.byte	1,30,2,35,0,11
	.byte	'IE_C',0,4
	.word	489
	.byte	1,29,2,35,0,11
	.byte	'IE_S',0,4
	.word	489
	.byte	1,28,2,35,0,11
	.byte	'IE_BI',0,4
	.word	489
	.byte	1,27,2,35,0,11
	.byte	'E_INFO',0,4
	.word	489
	.byte	6,21,2,35,0,11
	.byte	'IE_DUAL',0,4
	.word	489
	.byte	1,20,2,35,0,11
	.byte	'IE_SP',0,4
	.word	489
	.byte	1,19,2,35,0,11
	.byte	'IE_BS',0,4
	.word	489
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	489
	.byte	18,0,2,35,0,0,31
	.byte	'Ifx_CPU_DIETR_Bits',0,31,233,1,3
	.word	57276
	.byte	10
	.byte	'_Ifx_CPU_DMS_Bits',0,31,236,1,16,4,11
	.byte	'reserved_0',0,4
	.word	489
	.byte	1,31,2,35,0,11
	.byte	'DMSValue',0,4
	.word	489
	.byte	31,0,2,35,0,0,31
	.byte	'Ifx_CPU_DMS_Bits',0,31,240,1,3
	.word	57505
	.byte	10
	.byte	'_Ifx_CPU_DPR_L_Bits',0,31,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	489
	.byte	3,29,2,35,0,11
	.byte	'LOWBND',0,4
	.word	489
	.byte	29,0,2,35,0,0,31
	.byte	'Ifx_CPU_DPR_L_Bits',0,31,247,1,3
	.word	57598
	.byte	10
	.byte	'_Ifx_CPU_DPR_U_Bits',0,31,250,1,16,4,11
	.byte	'reserved_0',0,4
	.word	489
	.byte	3,29,2,35,0,11
	.byte	'UPPBND',0,4
	.word	489
	.byte	29,0,2,35,0,0,31
	.byte	'Ifx_CPU_DPR_U_Bits',0,31,254,1,3
	.word	57693
	.byte	10
	.byte	'_Ifx_CPU_DPRE_Bits',0,31,129,2,16,4,11
	.byte	'RE',0,4
	.word	489
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	489
	.byte	16,0,2,35,0,0,31
	.byte	'Ifx_CPU_DPRE_Bits',0,31,133,2,3
	.word	57788
	.byte	10
	.byte	'_Ifx_CPU_DPWE_Bits',0,31,136,2,16,4,11
	.byte	'WE',0,4
	.word	489
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	489
	.byte	16,0,2,35,0,0,31
	.byte	'Ifx_CPU_DPWE_Bits',0,31,140,2,3
	.word	57878
	.byte	10
	.byte	'_Ifx_CPU_DSTR_Bits',0,31,143,2,16,4,11
	.byte	'SRE',0,4
	.word	489
	.byte	1,31,2,35,0,11
	.byte	'GAE',0,4
	.word	489
	.byte	1,30,2,35,0,11
	.byte	'LBE',0,4
	.word	489
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	489
	.byte	3,26,2,35,0,11
	.byte	'CRE',0,4
	.word	489
	.byte	1,25,2,35,0,11
	.byte	'reserved_7',0,4
	.word	489
	.byte	7,18,2,35,0,11
	.byte	'DTME',0,4
	.word	489
	.byte	1,17,2,35,0,11
	.byte	'LOE',0,4
	.word	489
	.byte	1,16,2,35,0,11
	.byte	'SDE',0,4
	.word	489
	.byte	1,15,2,35,0,11
	.byte	'SCE',0,4
	.word	489
	.byte	1,14,2,35,0,11
	.byte	'CAC',0,4
	.word	489
	.byte	1,13,2,35,0,11
	.byte	'MPE',0,4
	.word	489
	.byte	1,12,2,35,0,11
	.byte	'CLE',0,4
	.word	489
	.byte	1,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	489
	.byte	3,8,2,35,0,11
	.byte	'ALN',0,4
	.word	489
	.byte	1,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	489
	.byte	7,0,2,35,0,0,31
	.byte	'Ifx_CPU_DSTR_Bits',0,31,161,2,3
	.word	57968
	.byte	10
	.byte	'_Ifx_CPU_EXEVT_Bits',0,31,164,2,16,4,11
	.byte	'EVTA',0,4
	.word	489
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	489
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	489
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	489
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	489
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	489
	.byte	24,0,2,35,0,0,31
	.byte	'Ifx_CPU_EXEVT_Bits',0,31,172,2,3
	.word	58292
	.byte	10
	.byte	'_Ifx_CPU_FCX_Bits',0,31,175,2,16,4,11
	.byte	'FCXO',0,4
	.word	489
	.byte	16,16,2,35,0,11
	.byte	'FCXS',0,4
	.word	489
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	489
	.byte	12,0,2,35,0,0,31
	.byte	'Ifx_CPU_FCX_Bits',0,31,180,2,3
	.word	58446
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_CON_Bits',0,31,183,2,16,4,11
	.byte	'TST',0,4
	.word	489
	.byte	1,31,2,35,0,11
	.byte	'TCL',0,4
	.word	489
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	489
	.byte	6,24,2,35,0,11
	.byte	'RM',0,4
	.word	489
	.byte	2,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	489
	.byte	8,14,2,35,0,11
	.byte	'FXE',0,4
	.word	489
	.byte	1,13,2,35,0,11
	.byte	'FUE',0,4
	.word	489
	.byte	1,12,2,35,0,11
	.byte	'FZE',0,4
	.word	489
	.byte	1,11,2,35,0,11
	.byte	'FVE',0,4
	.word	489
	.byte	1,10,2,35,0,11
	.byte	'FIE',0,4
	.word	489
	.byte	1,9,2,35,0,11
	.byte	'reserved_23',0,4
	.word	489
	.byte	3,6,2,35,0,11
	.byte	'FX',0,4
	.word	489
	.byte	1,5,2,35,0,11
	.byte	'FU',0,4
	.word	489
	.byte	1,4,2,35,0,11
	.byte	'FZ',0,4
	.word	489
	.byte	1,3,2,35,0,11
	.byte	'FV',0,4
	.word	489
	.byte	1,2,2,35,0,11
	.byte	'FI',0,4
	.word	489
	.byte	1,1,2,35,0,11
	.byte	'reserved_31',0,4
	.word	489
	.byte	1,0,2,35,0,0,31
	.byte	'Ifx_CPU_FPU_TRAP_CON_Bits',0,31,202,2,3
	.word	58552
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_OPC_Bits',0,31,205,2,16,4,11
	.byte	'OPC',0,4
	.word	489
	.byte	8,24,2,35,0,11
	.byte	'FMT',0,4
	.word	489
	.byte	1,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	489
	.byte	7,16,2,35,0,11
	.byte	'DREG',0,4
	.word	489
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	489
	.byte	12,0,2,35,0,0,31
	.byte	'Ifx_CPU_FPU_TRAP_OPC_Bits',0,31,212,2,3
	.word	58901
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_PC_Bits',0,31,215,2,16,4,11
	.byte	'PC',0,4
	.word	489
	.byte	32,0,2,35,0,0,31
	.byte	'Ifx_CPU_FPU_TRAP_PC_Bits',0,31,218,2,3
	.word	59061
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC1_Bits',0,31,221,2,16,4,11
	.byte	'SRC1',0,4
	.word	489
	.byte	32,0,2,35,0,0,31
	.byte	'Ifx_CPU_FPU_TRAP_SRC1_Bits',0,31,224,2,3
	.word	59142
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC2_Bits',0,31,227,2,16,4,11
	.byte	'SRC2',0,4
	.word	489
	.byte	32,0,2,35,0,0,31
	.byte	'Ifx_CPU_FPU_TRAP_SRC2_Bits',0,31,230,2,3
	.word	59229
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC3_Bits',0,31,233,2,16,4,11
	.byte	'SRC3',0,4
	.word	489
	.byte	32,0,2,35,0,0,31
	.byte	'Ifx_CPU_FPU_TRAP_SRC3_Bits',0,31,236,2,3
	.word	59316
	.byte	10
	.byte	'_Ifx_CPU_ICNT_Bits',0,31,239,2,16,4,11
	.byte	'CountValue',0,4
	.word	489
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	489
	.byte	1,0,2,35,0,0,31
	.byte	'Ifx_CPU_ICNT_Bits',0,31,243,2,3
	.word	59403
	.byte	10
	.byte	'_Ifx_CPU_ICR_Bits',0,31,246,2,16,4,11
	.byte	'CCPN',0,4
	.word	489
	.byte	10,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	489
	.byte	5,17,2,35,0,11
	.byte	'IE',0,4
	.word	489
	.byte	1,16,2,35,0,11
	.byte	'PIPN',0,4
	.word	489
	.byte	10,6,2,35,0,11
	.byte	'reserved_26',0,4
	.word	489
	.byte	6,0,2,35,0,0,31
	.byte	'Ifx_CPU_ICR_Bits',0,31,253,2,3
	.word	59494
	.byte	10
	.byte	'_Ifx_CPU_ISP_Bits',0,31,128,3,16,4,11
	.byte	'ISP',0,4
	.word	489
	.byte	32,0,2,35,0,0,31
	.byte	'Ifx_CPU_ISP_Bits',0,31,131,3,3
	.word	59637
	.byte	10
	.byte	'_Ifx_CPU_LCX_Bits',0,31,134,3,16,4,11
	.byte	'LCXO',0,4
	.word	489
	.byte	16,16,2,35,0,11
	.byte	'LCXS',0,4
	.word	489
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	489
	.byte	12,0,2,35,0,0,31
	.byte	'Ifx_CPU_LCX_Bits',0,31,139,3,3
	.word	59703
	.byte	10
	.byte	'_Ifx_CPU_M1CNT_Bits',0,31,142,3,16,4,11
	.byte	'CountValue',0,4
	.word	489
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	489
	.byte	1,0,2,35,0,0,31
	.byte	'Ifx_CPU_M1CNT_Bits',0,31,146,3,3
	.word	59809
	.byte	10
	.byte	'_Ifx_CPU_M2CNT_Bits',0,31,149,3,16,4,11
	.byte	'CountValue',0,4
	.word	489
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	489
	.byte	1,0,2,35,0,0,31
	.byte	'Ifx_CPU_M2CNT_Bits',0,31,153,3,3
	.word	59902
	.byte	10
	.byte	'_Ifx_CPU_M3CNT_Bits',0,31,156,3,16,4,11
	.byte	'CountValue',0,4
	.word	489
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	489
	.byte	1,0,2,35,0,0,31
	.byte	'Ifx_CPU_M3CNT_Bits',0,31,160,3,3
	.word	59995
	.byte	10
	.byte	'_Ifx_CPU_PC_Bits',0,31,163,3,16,4,11
	.byte	'reserved_0',0,4
	.word	489
	.byte	1,31,2,35,0,11
	.byte	'PC',0,4
	.word	489
	.byte	31,0,2,35,0,0,31
	.byte	'Ifx_CPU_PC_Bits',0,31,167,3,3
	.word	60088
	.byte	10
	.byte	'_Ifx_CPU_PCON0_Bits',0,31,170,3,16,4,11
	.byte	'reserved_0',0,4
	.word	489
	.byte	1,31,2,35,0,11
	.byte	'PCBYP',0,4
	.word	489
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	489
	.byte	30,0,2,35,0,0,31
	.byte	'Ifx_CPU_PCON0_Bits',0,31,175,3,3
	.word	60173
	.byte	10
	.byte	'_Ifx_CPU_PCON1_Bits',0,31,178,3,16,4,11
	.byte	'PCINV',0,4
	.word	489
	.byte	1,31,2,35,0,11
	.byte	'PBINV',0,4
	.word	489
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	489
	.byte	30,0,2,35,0,0,31
	.byte	'Ifx_CPU_PCON1_Bits',0,31,183,3,3
	.word	60289
	.byte	10
	.byte	'_Ifx_CPU_PCON2_Bits',0,31,186,3,16,4,11
	.byte	'PCACHE_SZE',0,4
	.word	489
	.byte	16,16,2,35,0,11
	.byte	'PSCRATCH_SZE',0,4
	.word	489
	.byte	16,0,2,35,0,0,31
	.byte	'Ifx_CPU_PCON2_Bits',0,31,190,3,3
	.word	60400
	.byte	10
	.byte	'_Ifx_CPU_PCXI_Bits',0,31,193,3,16,4,11
	.byte	'PCXO',0,4
	.word	489
	.byte	16,16,2,35,0,11
	.byte	'PCXS',0,4
	.word	489
	.byte	4,12,2,35,0,11
	.byte	'UL',0,4
	.word	489
	.byte	1,11,2,35,0,11
	.byte	'PIE',0,4
	.word	489
	.byte	1,10,2,35,0,11
	.byte	'PCPN',0,4
	.word	489
	.byte	10,0,2,35,0,0,31
	.byte	'Ifx_CPU_PCXI_Bits',0,31,200,3,3
	.word	60501
	.byte	10
	.byte	'_Ifx_CPU_PIEAR_Bits',0,31,203,3,16,4,11
	.byte	'TA',0,4
	.word	489
	.byte	32,0,2,35,0,0,31
	.byte	'Ifx_CPU_PIEAR_Bits',0,31,206,3,3
	.word	60631
	.byte	10
	.byte	'_Ifx_CPU_PIETR_Bits',0,31,209,3,16,4,11
	.byte	'IED',0,4
	.word	489
	.byte	1,31,2,35,0,11
	.byte	'IE_T',0,4
	.word	489
	.byte	1,30,2,35,0,11
	.byte	'IE_C',0,4
	.word	489
	.byte	1,29,2,35,0,11
	.byte	'IE_S',0,4
	.word	489
	.byte	1,28,2,35,0,11
	.byte	'IE_BI',0,4
	.word	489
	.byte	1,27,2,35,0,11
	.byte	'E_INFO',0,4
	.word	489
	.byte	6,21,2,35,0,11
	.byte	'IE_DUAL',0,4
	.word	489
	.byte	1,20,2,35,0,11
	.byte	'IE_SP',0,4
	.word	489
	.byte	1,19,2,35,0,11
	.byte	'IE_BS',0,4
	.word	489
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	489
	.byte	18,0,2,35,0,0,31
	.byte	'Ifx_CPU_PIETR_Bits',0,31,221,3,3
	.word	60700
	.byte	10
	.byte	'_Ifx_CPU_PMA0_Bits',0,31,224,3,16,4,11
	.byte	'reserved_0',0,4
	.word	489
	.byte	13,19,2,35,0,11
	.byte	'DAC',0,4
	.word	489
	.byte	3,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	489
	.byte	16,0,2,35,0,0,31
	.byte	'Ifx_CPU_PMA0_Bits',0,31,229,3,3
	.word	60929
	.byte	10
	.byte	'_Ifx_CPU_PMA1_Bits',0,31,232,3,16,4,11
	.byte	'reserved_0',0,4
	.word	489
	.byte	14,18,2,35,0,11
	.byte	'CAC',0,4
	.word	489
	.byte	2,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	489
	.byte	16,0,2,35,0,0,31
	.byte	'Ifx_CPU_PMA1_Bits',0,31,237,3,3
	.word	61042
	.byte	10
	.byte	'_Ifx_CPU_PMA2_Bits',0,31,240,3,16,4,11
	.byte	'PSI',0,4
	.word	489
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	489
	.byte	16,0,2,35,0,0,31
	.byte	'Ifx_CPU_PMA2_Bits',0,31,244,3,3
	.word	61155
	.byte	10
	.byte	'_Ifx_CPU_PSTR_Bits',0,31,247,3,16,4,11
	.byte	'FRE',0,4
	.word	489
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	489
	.byte	1,30,2,35,0,11
	.byte	'FBE',0,4
	.word	489
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	489
	.byte	9,20,2,35,0,11
	.byte	'FPE',0,4
	.word	489
	.byte	1,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	489
	.byte	1,18,2,35,0,11
	.byte	'FME',0,4
	.word	489
	.byte	1,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	489
	.byte	17,0,2,35,0,0,31
	.byte	'Ifx_CPU_PSTR_Bits',0,31,129,4,3
	.word	61246
	.byte	10
	.byte	'_Ifx_CPU_PSW_Bits',0,31,132,4,16,4,11
	.byte	'CDC',0,4
	.word	489
	.byte	7,25,2,35,0,11
	.byte	'CDE',0,4
	.word	489
	.byte	1,24,2,35,0,11
	.byte	'GW',0,4
	.word	489
	.byte	1,23,2,35,0,11
	.byte	'IS',0,4
	.word	489
	.byte	1,22,2,35,0,11
	.byte	'IO',0,4
	.word	489
	.byte	2,20,2,35,0,11
	.byte	'PRS',0,4
	.word	489
	.byte	2,18,2,35,0,11
	.byte	'S',0,4
	.word	489
	.byte	1,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	489
	.byte	12,5,2,35,0,11
	.byte	'SAV',0,4
	.word	489
	.byte	1,4,2,35,0,11
	.byte	'AV',0,4
	.word	489
	.byte	1,3,2,35,0,11
	.byte	'SV',0,4
	.word	489
	.byte	1,2,2,35,0,11
	.byte	'V',0,4
	.word	489
	.byte	1,1,2,35,0,11
	.byte	'C',0,4
	.word	489
	.byte	1,0,2,35,0,0,31
	.byte	'Ifx_CPU_PSW_Bits',0,31,147,4,3
	.word	61449
	.byte	10
	.byte	'_Ifx_CPU_SEGEN_Bits',0,31,150,4,16,4,11
	.byte	'ADFLIP',0,4
	.word	489
	.byte	8,24,2,35,0,11
	.byte	'ADTYPE',0,4
	.word	489
	.byte	2,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	489
	.byte	21,1,2,35,0,11
	.byte	'AE',0,4
	.word	489
	.byte	1,0,2,35,0,0,31
	.byte	'Ifx_CPU_SEGEN_Bits',0,31,156,4,3
	.word	61692
	.byte	10
	.byte	'_Ifx_CPU_SMACON_Bits',0,31,159,4,16,4,11
	.byte	'PC',0,4
	.word	489
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	489
	.byte	1,30,2,35,0,11
	.byte	'PT',0,4
	.word	489
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	489
	.byte	5,24,2,35,0,11
	.byte	'DC',0,4
	.word	489
	.byte	1,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	489
	.byte	1,22,2,35,0,11
	.byte	'DT',0,4
	.word	489
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	489
	.byte	13,8,2,35,0,11
	.byte	'IODT',0,4
	.word	489
	.byte	1,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	489
	.byte	7,0,2,35,0,0,31
	.byte	'Ifx_CPU_SMACON_Bits',0,31,171,4,3
	.word	61820
	.byte	10
	.byte	'_Ifx_CPU_SPROT_ACCENA_Bits',0,31,174,4,16,4,11
	.byte	'EN',0,4
	.word	466
	.byte	32,0,2,35,0,0,31
	.byte	'Ifx_CPU_SPROT_ACCENA_Bits',0,31,177,4,3
	.word	62061
	.byte	10
	.byte	'_Ifx_CPU_SPROT_ACCENB_Bits',0,31,180,4,16,4,11
	.byte	'reserved_0',0,4
	.word	466
	.byte	32,0,2,35,0,0,31
	.byte	'Ifx_CPU_SPROT_ACCENB_Bits',0,31,183,4,3
	.word	62144
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,31,186,4,16,4,11
	.byte	'EN',0,4
	.word	466
	.byte	32,0,2,35,0,0,31
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,31,189,4,3
	.word	62235
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,31,192,4,16,4,11
	.byte	'reserved_0',0,4
	.word	466
	.byte	32,0,2,35,0,0,31
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,31,195,4,3
	.word	62326
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_LA_Bits',0,31,198,4,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	466
	.byte	27,0,2,35,0,0,31
	.byte	'Ifx_CPU_SPROT_RGN_LA_Bits',0,31,202,4,3
	.word	62425
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_UA_Bits',0,31,205,4,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	466
	.byte	27,0,2,35,0,0,31
	.byte	'Ifx_CPU_SPROT_RGN_UA_Bits',0,31,209,4,3
	.word	62532
	.byte	10
	.byte	'_Ifx_CPU_SWEVT_Bits',0,31,212,4,16,4,11
	.byte	'EVTA',0,4
	.word	489
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	489
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	489
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	489
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	489
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	489
	.byte	24,0,2,35,0,0,31
	.byte	'Ifx_CPU_SWEVT_Bits',0,31,220,4,3
	.word	62639
	.byte	10
	.byte	'_Ifx_CPU_SYSCON_Bits',0,31,223,4,16,4,11
	.byte	'FCDSF',0,4
	.word	489
	.byte	1,31,2,35,0,11
	.byte	'PROTEN',0,4
	.word	489
	.byte	1,30,2,35,0,11
	.byte	'TPROTEN',0,4
	.word	489
	.byte	1,29,2,35,0,11
	.byte	'IS',0,4
	.word	489
	.byte	1,28,2,35,0,11
	.byte	'IT',0,4
	.word	489
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	489
	.byte	27,0,2,35,0,0,31
	.byte	'Ifx_CPU_SYSCON_Bits',0,31,231,4,3
	.word	62793
	.byte	10
	.byte	'_Ifx_CPU_TASK_ASI_Bits',0,31,234,4,16,4,11
	.byte	'ASI',0,4
	.word	489
	.byte	5,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	489
	.byte	27,0,2,35,0,0,31
	.byte	'Ifx_CPU_TASK_ASI_Bits',0,31,238,4,3
	.word	62954
	.byte	10
	.byte	'_Ifx_CPU_TPS_CON_Bits',0,31,241,4,16,4,11
	.byte	'TEXP0',0,4
	.word	489
	.byte	1,31,2,35,0,11
	.byte	'TEXP1',0,4
	.word	489
	.byte	1,30,2,35,0,11
	.byte	'TEXP2',0,4
	.word	489
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	489
	.byte	13,16,2,35,0,11
	.byte	'TTRAP',0,4
	.word	489
	.byte	1,15,2,35,0,11
	.byte	'reserved_17',0,4
	.word	489
	.byte	15,0,2,35,0,0,31
	.byte	'Ifx_CPU_TPS_CON_Bits',0,31,249,4,3
	.word	63052
	.byte	10
	.byte	'_Ifx_CPU_TPS_TIMER_Bits',0,31,252,4,16,4,11
	.byte	'Timer',0,4
	.word	489
	.byte	32,0,2,35,0,0,31
	.byte	'Ifx_CPU_TPS_TIMER_Bits',0,31,255,4,3
	.word	63224
	.byte	10
	.byte	'_Ifx_CPU_TR_ADR_Bits',0,31,130,5,16,4,11
	.byte	'ADDR',0,4
	.word	489
	.byte	32,0,2,35,0,0,31
	.byte	'Ifx_CPU_TR_ADR_Bits',0,31,133,5,3
	.word	63304
	.byte	10
	.byte	'_Ifx_CPU_TR_EVT_Bits',0,31,136,5,16,4,11
	.byte	'EVTA',0,4
	.word	489
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	489
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	489
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	489
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	489
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	489
	.byte	4,20,2,35,0,11
	.byte	'TYP',0,4
	.word	489
	.byte	1,19,2,35,0,11
	.byte	'RNG',0,4
	.word	489
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	489
	.byte	1,17,2,35,0,11
	.byte	'ASI_EN',0,4
	.word	489
	.byte	1,16,2,35,0,11
	.byte	'ASI',0,4
	.word	489
	.byte	5,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	489
	.byte	6,5,2,35,0,11
	.byte	'AST',0,4
	.word	489
	.byte	1,4,2,35,0,11
	.byte	'ALD',0,4
	.word	489
	.byte	1,3,2,35,0,11
	.byte	'reserved_29',0,4
	.word	489
	.byte	3,0,2,35,0,0,31
	.byte	'Ifx_CPU_TR_EVT_Bits',0,31,153,5,3
	.word	63377
	.byte	10
	.byte	'_Ifx_CPU_TRIG_ACC_Bits',0,31,156,5,16,4,11
	.byte	'T0',0,4
	.word	489
	.byte	1,31,2,35,0,11
	.byte	'T1',0,4
	.word	489
	.byte	1,30,2,35,0,11
	.byte	'T2',0,4
	.word	489
	.byte	1,29,2,35,0,11
	.byte	'T3',0,4
	.word	489
	.byte	1,28,2,35,0,11
	.byte	'T4',0,4
	.word	489
	.byte	1,27,2,35,0,11
	.byte	'T5',0,4
	.word	489
	.byte	1,26,2,35,0,11
	.byte	'T6',0,4
	.word	489
	.byte	1,25,2,35,0,11
	.byte	'T7',0,4
	.word	489
	.byte	1,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	489
	.byte	24,0,2,35,0,0,31
	.byte	'Ifx_CPU_TRIG_ACC_Bits',0,31,167,5,3
	.word	63695
	.byte	12,31,175,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54913
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_A',0,31,180,5,3
	.word	63890
	.byte	12,31,183,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54974
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_BIV',0,31,188,5,3
	.word	63949
	.byte	12,31,191,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55053
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_BTV',0,31,196,5,3
	.word	64010
	.byte	12,31,199,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55139
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_CCNT',0,31,204,5,3
	.word	64071
	.byte	12,31,207,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55228
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_CCTRL',0,31,212,5,3
	.word	64133
	.byte	12,31,215,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55374
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_COMPAT',0,31,220,5,3
	.word	64196
	.byte	12,31,223,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55501
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_CORE_ID',0,31,228,5,3
	.word	64260
	.byte	12,31,231,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55599
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_CPR_L',0,31,236,5,3
	.word	64325
	.byte	12,31,239,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55692
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_CPR_U',0,31,244,5,3
	.word	64388
	.byte	12,31,247,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55785
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_CPU_ID',0,31,252,5,3
	.word	64451
	.byte	12,31,255,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55892
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_CPXE',0,31,132,6,3
	.word	64515
	.byte	12,31,135,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55979
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_CREVT',0,31,140,6,3
	.word	64577
	.byte	12,31,143,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56133
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_CUS_ID',0,31,148,6,3
	.word	64640
	.byte	12,31,151,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56227
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_D',0,31,156,6,3
	.word	64704
	.byte	12,31,159,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56290
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_DATR',0,31,164,6,3
	.word	64763
	.byte	12,31,167,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56508
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_DBGSR',0,31,172,6,3
	.word	64825
	.byte	12,31,175,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56723
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_DBGTCR',0,31,180,6,3
	.word	64888
	.byte	12,31,183,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56817
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_DCON0',0,31,188,6,3
	.word	64952
	.byte	12,31,191,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56933
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_DCON2',0,31,196,6,3
	.word	65015
	.byte	12,31,199,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57034
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_DCX',0,31,204,6,3
	.word	65078
	.byte	12,31,207,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57127
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_DEADD',0,31,212,6,3
	.word	65139
	.byte	12,31,215,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57207
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_DIEAR',0,31,220,6,3
	.word	65202
	.byte	12,31,223,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57276
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_DIETR',0,31,228,6,3
	.word	65265
	.byte	12,31,231,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57505
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_DMS',0,31,236,6,3
	.word	65328
	.byte	12,31,239,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57598
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_DPR_L',0,31,244,6,3
	.word	65389
	.byte	12,31,247,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57693
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_DPR_U',0,31,252,6,3
	.word	65452
	.byte	12,31,255,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57788
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_DPRE',0,31,132,7,3
	.word	65515
	.byte	12,31,135,7,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57878
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_DPWE',0,31,140,7,3
	.word	65577
	.byte	12,31,143,7,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57968
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_DSTR',0,31,148,7,3
	.word	65639
	.byte	12,31,151,7,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58292
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_EXEVT',0,31,156,7,3
	.word	65701
	.byte	12,31,159,7,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58446
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_FCX',0,31,164,7,3
	.word	65764
	.byte	12,31,167,7,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58552
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_FPU_TRAP_CON',0,31,172,7,3
	.word	65825
	.byte	12,31,175,7,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58901
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_FPU_TRAP_OPC',0,31,180,7,3
	.word	65895
	.byte	12,31,183,7,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59061
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_FPU_TRAP_PC',0,31,188,7,3
	.word	65965
	.byte	12,31,191,7,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59142
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_FPU_TRAP_SRC1',0,31,196,7,3
	.word	66034
	.byte	12,31,199,7,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59229
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_FPU_TRAP_SRC2',0,31,204,7,3
	.word	66105
	.byte	12,31,207,7,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59316
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_FPU_TRAP_SRC3',0,31,212,7,3
	.word	66176
	.byte	12,31,215,7,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59403
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_ICNT',0,31,220,7,3
	.word	66247
	.byte	12,31,223,7,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59494
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_ICR',0,31,228,7,3
	.word	66309
	.byte	12,31,231,7,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59637
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_ISP',0,31,236,7,3
	.word	66370
	.byte	12,31,239,7,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59703
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_LCX',0,31,244,7,3
	.word	66431
	.byte	12,31,247,7,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59809
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_M1CNT',0,31,252,7,3
	.word	66492
	.byte	12,31,255,7,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59902
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_M2CNT',0,31,132,8,3
	.word	66555
	.byte	12,31,135,8,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59995
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_M3CNT',0,31,140,8,3
	.word	66618
	.byte	12,31,143,8,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60088
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_PC',0,31,148,8,3
	.word	66681
	.byte	12,31,151,8,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60173
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_PCON0',0,31,156,8,3
	.word	66741
	.byte	12,31,159,8,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60289
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_PCON1',0,31,164,8,3
	.word	66804
	.byte	12,31,167,8,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60400
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_PCON2',0,31,172,8,3
	.word	66867
	.byte	12,31,175,8,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60501
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_PCXI',0,31,180,8,3
	.word	66930
	.byte	12,31,183,8,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60631
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_PIEAR',0,31,188,8,3
	.word	66992
	.byte	12,31,191,8,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60700
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_PIETR',0,31,196,8,3
	.word	67055
	.byte	12,31,199,8,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60929
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_PMA0',0,31,204,8,3
	.word	67118
	.byte	12,31,207,8,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61042
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_PMA1',0,31,212,8,3
	.word	67180
	.byte	12,31,215,8,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61155
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_PMA2',0,31,220,8,3
	.word	67242
	.byte	12,31,223,8,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61246
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_PSTR',0,31,228,8,3
	.word	67304
	.byte	12,31,231,8,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61449
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_PSW',0,31,236,8,3
	.word	67366
	.byte	12,31,239,8,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61692
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_SEGEN',0,31,244,8,3
	.word	67427
	.byte	12,31,247,8,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61820
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_SMACON',0,31,252,8,3
	.word	67490
	.byte	12,31,255,8,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62061
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_SPROT_ACCENA',0,31,132,9,3
	.word	67554
	.byte	12,31,135,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62144
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_SPROT_ACCENB',0,31,140,9,3
	.word	67624
	.byte	12,31,143,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62235
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA',0,31,148,9,3
	.word	67694
	.byte	12,31,151,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62326
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB',0,31,156,9,3
	.word	67768
	.byte	12,31,159,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62425
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_SPROT_RGN_LA',0,31,164,9,3
	.word	67842
	.byte	12,31,167,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62532
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_SPROT_RGN_UA',0,31,172,9,3
	.word	67912
	.byte	12,31,175,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62639
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_SWEVT',0,31,180,9,3
	.word	67982
	.byte	12,31,183,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62793
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_SYSCON',0,31,188,9,3
	.word	68045
	.byte	12,31,191,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62954
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_TASK_ASI',0,31,196,9,3
	.word	68109
	.byte	12,31,199,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63052
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_TPS_CON',0,31,204,9,3
	.word	68175
	.byte	12,31,207,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63224
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_TPS_TIMER',0,31,212,9,3
	.word	68240
	.byte	12,31,215,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63304
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_TR_ADR',0,31,220,9,3
	.word	68307
	.byte	12,31,223,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63377
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_TR_EVT',0,31,228,9,3
	.word	68371
	.byte	12,31,231,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63695
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_TRIG_ACC',0,31,236,9,3
	.word	68435
	.byte	10
	.byte	'_Ifx_CPU_CPR',0,31,247,9,25,8,13
	.byte	'L',0
	.word	64325
	.byte	4,2,35,0,13
	.byte	'U',0
	.word	64388
	.byte	4,2,35,4,0,14
	.word	68501
	.byte	31
	.byte	'Ifx_CPU_CPR',0,31,251,9,3
	.word	68543
	.byte	10
	.byte	'_Ifx_CPU_DPR',0,31,254,9,25,8,13
	.byte	'L',0
	.word	65389
	.byte	4,2,35,0,13
	.byte	'U',0
	.word	65452
	.byte	4,2,35,4,0,14
	.word	68569
	.byte	31
	.byte	'Ifx_CPU_DPR',0,31,130,10,3
	.word	68611
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN',0,31,133,10,25,16,13
	.byte	'LA',0
	.word	67842
	.byte	4,2,35,0,13
	.byte	'UA',0
	.word	67912
	.byte	4,2,35,4,13
	.byte	'ACCENA',0
	.word	67694
	.byte	4,2,35,8,13
	.byte	'ACCENB',0
	.word	67768
	.byte	4,2,35,12,0,14
	.word	68637
	.byte	31
	.byte	'Ifx_CPU_SPROT_RGN',0,31,139,10,3
	.word	68719
	.byte	18,12
	.word	68240
	.byte	19,2,0,10
	.byte	'_Ifx_CPU_TPS',0,31,142,10,25,16,13
	.byte	'CON',0
	.word	68175
	.byte	4,2,35,0,13
	.byte	'TIMER',0
	.word	68751
	.byte	12,2,35,4,0,14
	.word	68760
	.byte	31
	.byte	'Ifx_CPU_TPS',0,31,146,10,3
	.word	68808
	.byte	10
	.byte	'_Ifx_CPU_TR',0,31,149,10,25,8,13
	.byte	'EVT',0
	.word	68371
	.byte	4,2,35,0,13
	.byte	'ADR',0
	.word	68307
	.byte	4,2,35,4,0,14
	.word	68834
	.byte	31
	.byte	'Ifx_CPU_TR',0,31,153,10,3
	.word	68879
	.byte	18,176,32
	.word	641
	.byte	19,175,32,0,18,208,223,1
	.word	641
	.byte	19,207,223,1,0,18,248,1
	.word	641
	.byte	19,247,1,0,18,244,29
	.word	641
	.byte	19,243,29,0,18,188,3
	.word	641
	.byte	19,187,3,0,18,232,3
	.word	641
	.byte	19,231,3,0,18,252,23
	.word	641
	.byte	19,251,23,0,18,228,63
	.word	641
	.byte	19,227,63,0,18,128,1
	.word	68569
	.byte	19,15,0,14
	.word	68994
	.byte	18,128,31
	.word	641
	.byte	19,255,30,0,18,64
	.word	68501
	.byte	19,7,0,14
	.word	69020
	.byte	18,192,31
	.word	641
	.byte	19,191,31,0,18,16
	.word	64515
	.byte	19,3,0,18,16
	.word	65515
	.byte	19,3,0,18,16
	.word	65577
	.byte	19,3,0,18,208,7
	.word	641
	.byte	19,207,7,0,14
	.word	68760
	.byte	18,240,23
	.word	641
	.byte	19,239,23,0,18,64
	.word	68834
	.byte	19,7,0,14
	.word	69099
	.byte	18,192,23
	.word	641
	.byte	19,191,23,0,18,232,1
	.word	641
	.byte	19,231,1,0,18,180,1
	.word	641
	.byte	19,179,1,0,18,172,1
	.word	641
	.byte	19,171,1,0,18,64
	.word	64704
	.byte	19,15,0,18,64
	.word	641
	.byte	19,63,0,18,64
	.word	63890
	.byte	19,15,0,10
	.byte	'_Ifx_CPU',0,31,166,10,25,128,128,4,13
	.byte	'reserved_0',0
	.word	68904
	.byte	176,32,2,35,0,13
	.byte	'SEGEN',0
	.word	67427
	.byte	4,3,35,176,32,13
	.byte	'reserved_1034',0
	.word	68915
	.byte	208,223,1,3,35,180,32,13
	.byte	'TASK_ASI',0
	.word	68109
	.byte	4,4,35,132,128,2,13
	.byte	'reserved_8008',0
	.word	68928
	.byte	248,1,4,35,136,128,2,13
	.byte	'PMA0',0
	.word	67118
	.byte	4,4,35,128,130,2,13
	.byte	'PMA1',0
	.word	67180
	.byte	4,4,35,132,130,2,13
	.byte	'PMA2',0
	.word	67242
	.byte	4,4,35,136,130,2,13
	.byte	'reserved_810C',0
	.word	68939
	.byte	244,29,4,35,140,130,2,13
	.byte	'DCON2',0
	.word	65015
	.byte	4,4,35,128,160,2,13
	.byte	'reserved_9004',0
	.word	4831
	.byte	8,4,35,132,160,2,13
	.byte	'SMACON',0
	.word	67490
	.byte	4,4,35,140,160,2,13
	.byte	'DSTR',0
	.word	65639
	.byte	4,4,35,144,160,2,13
	.byte	'reserved_9014',0
	.word	3012
	.byte	4,4,35,148,160,2,13
	.byte	'DATR',0
	.word	64763
	.byte	4,4,35,152,160,2,13
	.byte	'DEADD',0
	.word	65139
	.byte	4,4,35,156,160,2,13
	.byte	'DIEAR',0
	.word	65202
	.byte	4,4,35,160,160,2,13
	.byte	'DIETR',0
	.word	65265
	.byte	4,4,35,164,160,2,13
	.byte	'reserved_9028',0
	.word	4202
	.byte	24,4,35,168,160,2,13
	.byte	'DCON0',0
	.word	64952
	.byte	4,4,35,192,160,2,13
	.byte	'reserved_9044',0
	.word	68950
	.byte	188,3,4,35,196,160,2,13
	.byte	'PSTR',0
	.word	67304
	.byte	4,4,35,128,164,2,13
	.byte	'PCON1',0
	.word	66804
	.byte	4,4,35,132,164,2,13
	.byte	'PCON2',0
	.word	66867
	.byte	4,4,35,136,164,2,13
	.byte	'PCON0',0
	.word	66741
	.byte	4,4,35,140,164,2,13
	.byte	'PIEAR',0
	.word	66992
	.byte	4,4,35,144,164,2,13
	.byte	'PIETR',0
	.word	67055
	.byte	4,4,35,148,164,2,13
	.byte	'reserved_9218',0
	.word	68961
	.byte	232,3,4,35,152,164,2,13
	.byte	'COMPAT',0
	.word	64196
	.byte	4,4,35,128,168,2,13
	.byte	'reserved_9404',0
	.word	68972
	.byte	252,23,4,35,132,168,2,13
	.byte	'FPU_TRAP_CON',0
	.word	65825
	.byte	4,4,35,128,192,2,13
	.byte	'FPU_TRAP_PC',0
	.word	65965
	.byte	4,4,35,132,192,2,13
	.byte	'FPU_TRAP_OPC',0
	.word	65895
	.byte	4,4,35,136,192,2,13
	.byte	'reserved_A00C',0
	.word	3012
	.byte	4,4,35,140,192,2,13
	.byte	'FPU_TRAP_SRC1',0
	.word	66034
	.byte	4,4,35,144,192,2,13
	.byte	'FPU_TRAP_SRC2',0
	.word	66105
	.byte	4,4,35,148,192,2,13
	.byte	'FPU_TRAP_SRC3',0
	.word	66176
	.byte	4,4,35,152,192,2,13
	.byte	'reserved_A01C',0
	.word	68983
	.byte	228,63,4,35,156,192,2,13
	.byte	'DPR',0
	.word	69004
	.byte	128,1,4,35,128,128,3,13
	.byte	'reserved_C080',0
	.word	69009
	.byte	128,31,4,35,128,129,3,13
	.byte	'CPR',0
	.word	69029
	.byte	64,4,35,128,160,3,13
	.byte	'reserved_D040',0
	.word	69034
	.byte	192,31,4,35,192,160,3,13
	.byte	'CPXE',0
	.word	69045
	.byte	16,4,35,128,192,3,13
	.byte	'DPRE',0
	.word	69054
	.byte	16,4,35,144,192,3,13
	.byte	'DPWE',0
	.word	69063
	.byte	16,4,35,160,192,3,13
	.byte	'reserved_E030',0
	.word	69072
	.byte	208,7,4,35,176,192,3,13
	.byte	'TPS',0
	.word	69083
	.byte	16,4,35,128,200,3,13
	.byte	'reserved_E410',0
	.word	69088
	.byte	240,23,4,35,144,200,3,13
	.byte	'TR',0
	.word	69108
	.byte	64,4,35,128,224,3,13
	.byte	'reserved_F040',0
	.word	69113
	.byte	192,23,4,35,192,224,3,13
	.byte	'CCTRL',0
	.word	64133
	.byte	4,4,35,128,248,3,13
	.byte	'CCNT',0
	.word	64071
	.byte	4,4,35,132,248,3,13
	.byte	'ICNT',0
	.word	66247
	.byte	4,4,35,136,248,3,13
	.byte	'M1CNT',0
	.word	66492
	.byte	4,4,35,140,248,3,13
	.byte	'M2CNT',0
	.word	66555
	.byte	4,4,35,144,248,3,13
	.byte	'M3CNT',0
	.word	66618
	.byte	4,4,35,148,248,3,13
	.byte	'reserved_FC18',0
	.word	69124
	.byte	232,1,4,35,152,248,3,13
	.byte	'DBGSR',0
	.word	64825
	.byte	4,4,35,128,250,3,13
	.byte	'reserved_FD04',0
	.word	3012
	.byte	4,4,35,132,250,3,13
	.byte	'EXEVT',0
	.word	65701
	.byte	4,4,35,136,250,3,13
	.byte	'CREVT',0
	.word	64577
	.byte	4,4,35,140,250,3,13
	.byte	'SWEVT',0
	.word	67982
	.byte	4,4,35,144,250,3,13
	.byte	'reserved_FD14',0
	.word	52854
	.byte	28,4,35,148,250,3,13
	.byte	'TRIG_ACC',0
	.word	68435
	.byte	4,4,35,176,250,3,13
	.byte	'reserved_FD34',0
	.word	5171
	.byte	12,4,35,180,250,3,13
	.byte	'DMS',0
	.word	65328
	.byte	4,4,35,192,250,3,13
	.byte	'DCX',0
	.word	65078
	.byte	4,4,35,196,250,3,13
	.byte	'DBGTCR',0
	.word	64888
	.byte	4,4,35,200,250,3,13
	.byte	'reserved_FD4C',0
	.word	69135
	.byte	180,1,4,35,204,250,3,13
	.byte	'PCXI',0
	.word	66930
	.byte	4,4,35,128,252,3,13
	.byte	'PSW',0
	.word	67366
	.byte	4,4,35,132,252,3,13
	.byte	'PC',0
	.word	66681
	.byte	4,4,35,136,252,3,13
	.byte	'reserved_FE0C',0
	.word	4831
	.byte	8,4,35,140,252,3,13
	.byte	'SYSCON',0
	.word	68045
	.byte	4,4,35,148,252,3,13
	.byte	'CPU_ID',0
	.word	64451
	.byte	4,4,35,152,252,3,13
	.byte	'CORE_ID',0
	.word	64260
	.byte	4,4,35,156,252,3,13
	.byte	'BIV',0
	.word	63949
	.byte	4,4,35,160,252,3,13
	.byte	'BTV',0
	.word	64010
	.byte	4,4,35,164,252,3,13
	.byte	'ISP',0
	.word	66370
	.byte	4,4,35,168,252,3,13
	.byte	'ICR',0
	.word	66309
	.byte	4,4,35,172,252,3,13
	.byte	'reserved_FE30',0
	.word	4831
	.byte	8,4,35,176,252,3,13
	.byte	'FCX',0
	.word	65764
	.byte	4,4,35,184,252,3,13
	.byte	'LCX',0
	.word	66431
	.byte	4,4,35,188,252,3,13
	.byte	'reserved_FE40',0
	.word	52845
	.byte	16,4,35,192,252,3,13
	.byte	'CUS_ID',0
	.word	64640
	.byte	4,4,35,208,252,3,13
	.byte	'reserved_FE54',0
	.word	69146
	.byte	172,1,4,35,212,252,3,13
	.byte	'D',0
	.word	69157
	.byte	64,4,35,128,254,3,13
	.byte	'reserved_FF40',0
	.word	69166
	.byte	64,4,35,192,254,3,13
	.byte	'A',0
	.word	69175
	.byte	64,4,35,128,255,3,13
	.byte	'reserved_FFC0',0
	.word	69166
	.byte	64,4,35,192,255,3,0,14
	.word	69184
	.byte	31
	.byte	'Ifx_CPU',0,31,130,11,3
	.word	70975
	.byte	15,6,127,9,1,16
	.byte	'IfxCpu_Id_0',0,0,16
	.byte	'IfxCpu_Id_1',0,1,16
	.byte	'IfxCpu_Id_none',0,2,0,31
	.byte	'IfxCpu_Id',0,6,132,1,3
	.word	70997
	.byte	31
	.byte	'IfxCpu_ResourceCpu',0,6,161,1,3
	.word	1453
	.byte	31
	.byte	'Ifx_SRC_SRCR_Bits',0,11,62,3
	.word	10573
	.byte	31
	.byte	'Ifx_SRC_SRCR',0,11,75,3
	.word	10863
	.byte	10
	.byte	'_Ifx_SRC_AGBT',0,11,86,25,4,13
	.byte	'SR',0
	.word	10863
	.byte	4,2,35,0,0,14
	.word	71142
	.byte	31
	.byte	'Ifx_SRC_AGBT',0,11,89,3
	.word	71174
	.byte	10
	.byte	'_Ifx_SRC_ASCLIN',0,11,92,25,12,13
	.byte	'TX',0
	.word	10863
	.byte	4,2,35,0,13
	.byte	'RX',0
	.word	10863
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	10863
	.byte	4,2,35,8,0,14
	.word	71200
	.byte	31
	.byte	'Ifx_SRC_ASCLIN',0,11,97,3
	.word	71259
	.byte	10
	.byte	'_Ifx_SRC_BCUSPB',0,11,100,25,4,13
	.byte	'SBSRC',0
	.word	10863
	.byte	4,2,35,0,0,14
	.word	71287
	.byte	31
	.byte	'Ifx_SRC_BCUSPB',0,11,103,3
	.word	71324
	.byte	18,64
	.word	10863
	.byte	19,15,0,10
	.byte	'_Ifx_SRC_CAN',0,11,106,25,64,13
	.byte	'INT',0
	.word	71352
	.byte	64,2,35,0,0,14
	.word	71361
	.byte	31
	.byte	'Ifx_SRC_CAN',0,11,109,3
	.word	71393
	.byte	10
	.byte	'_Ifx_SRC_CCU6',0,11,112,25,16,13
	.byte	'SR0',0
	.word	10863
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10863
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	10863
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	10863
	.byte	4,2,35,12,0,14
	.word	71418
	.byte	31
	.byte	'Ifx_SRC_CCU6',0,11,118,3
	.word	71490
	.byte	18,8
	.word	10863
	.byte	19,1,0,10
	.byte	'_Ifx_SRC_CERBERUS',0,11,121,25,8,13
	.byte	'SR',0
	.word	71516
	.byte	8,2,35,0,0,14
	.word	71525
	.byte	31
	.byte	'Ifx_SRC_CERBERUS',0,11,124,3
	.word	71561
	.byte	10
	.byte	'_Ifx_SRC_CIF',0,11,127,25,16,13
	.byte	'MI',0
	.word	10863
	.byte	4,2,35,0,13
	.byte	'MIEP',0
	.word	10863
	.byte	4,2,35,4,13
	.byte	'ISP',0
	.word	10863
	.byte	4,2,35,8,13
	.byte	'MJPEG',0
	.word	10863
	.byte	4,2,35,12,0,14
	.word	71591
	.byte	31
	.byte	'Ifx_SRC_CIF',0,11,133,1,3
	.word	71664
	.byte	10
	.byte	'_Ifx_SRC_CPU',0,11,136,1,25,4,13
	.byte	'SBSRC',0
	.word	10863
	.byte	4,2,35,0,0,14
	.word	71690
	.byte	31
	.byte	'Ifx_SRC_CPU',0,11,139,1,3
	.word	71725
	.byte	18,192,1
	.word	10863
	.byte	19,47,0,10
	.byte	'_Ifx_SRC_DMA',0,11,142,1,25,208,1,13
	.byte	'ERR',0
	.word	10863
	.byte	4,2,35,0,13
	.byte	'reserved_4',0
	.word	5171
	.byte	12,2,35,4,13
	.byte	'CH',0
	.word	71751
	.byte	192,1,2,35,16,0,14
	.word	71761
	.byte	31
	.byte	'Ifx_SRC_DMA',0,11,147,1,3
	.word	71828
	.byte	10
	.byte	'_Ifx_SRC_DSADC',0,11,150,1,25,8,13
	.byte	'SRM',0
	.word	10863
	.byte	4,2,35,0,13
	.byte	'SRA',0
	.word	10863
	.byte	4,2,35,4,0,14
	.word	71854
	.byte	31
	.byte	'Ifx_SRC_DSADC',0,11,154,1,3
	.word	71902
	.byte	10
	.byte	'_Ifx_SRC_EMEM',0,11,157,1,25,4,13
	.byte	'SR',0
	.word	10863
	.byte	4,2,35,0,0,14
	.word	71930
	.byte	31
	.byte	'Ifx_SRC_EMEM',0,11,160,1,3
	.word	71963
	.byte	10
	.byte	'_Ifx_SRC_ERAY',0,11,163,1,25,80,13
	.byte	'INT',0
	.word	71516
	.byte	8,2,35,0,13
	.byte	'TINT',0
	.word	71516
	.byte	8,2,35,8,13
	.byte	'NDAT',0
	.word	71516
	.byte	8,2,35,16,13
	.byte	'MBSC',0
	.word	71516
	.byte	8,2,35,24,13
	.byte	'OBUSY',0
	.word	10863
	.byte	4,2,35,32,13
	.byte	'IBUSY',0
	.word	10863
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	52863
	.byte	40,2,35,40,0,14
	.word	71990
	.byte	31
	.byte	'Ifx_SRC_ERAY',0,11,172,1,3
	.word	72117
	.byte	10
	.byte	'_Ifx_SRC_ETH',0,11,175,1,25,4,13
	.byte	'SR',0
	.word	10863
	.byte	4,2,35,0,0,14
	.word	72144
	.byte	31
	.byte	'Ifx_SRC_ETH',0,11,178,1,3
	.word	72176
	.byte	10
	.byte	'_Ifx_SRC_FCE',0,11,181,1,25,4,13
	.byte	'SR',0
	.word	10863
	.byte	4,2,35,0,0,14
	.word	72202
	.byte	31
	.byte	'Ifx_SRC_FCE',0,11,184,1,3
	.word	72234
	.byte	10
	.byte	'_Ifx_SRC_FFT',0,11,187,1,25,12,13
	.byte	'DONE',0
	.word	10863
	.byte	4,2,35,0,13
	.byte	'ERR',0
	.word	10863
	.byte	4,2,35,4,13
	.byte	'RFS',0
	.word	10863
	.byte	4,2,35,8,0,14
	.word	72260
	.byte	31
	.byte	'Ifx_SRC_FFT',0,11,192,1,3
	.word	72320
	.byte	10
	.byte	'_Ifx_SRC_GPSR',0,11,195,1,25,32,13
	.byte	'SR0',0
	.word	10863
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10863
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	10863
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	10863
	.byte	4,2,35,12,13
	.byte	'reserved_10',0
	.word	52845
	.byte	16,2,35,16,0,14
	.word	72346
	.byte	31
	.byte	'Ifx_SRC_GPSR',0,11,202,1,3
	.word	72440
	.byte	10
	.byte	'_Ifx_SRC_GPT12',0,11,205,1,25,48,13
	.byte	'CIRQ',0
	.word	10863
	.byte	4,2,35,0,13
	.byte	'T2',0
	.word	10863
	.byte	4,2,35,4,13
	.byte	'T3',0
	.word	10863
	.byte	4,2,35,8,13
	.byte	'T4',0
	.word	10863
	.byte	4,2,35,12,13
	.byte	'T5',0
	.word	10863
	.byte	4,2,35,16,13
	.byte	'T6',0
	.word	10863
	.byte	4,2,35,20,13
	.byte	'reserved_18',0
	.word	4202
	.byte	24,2,35,24,0,14
	.word	72467
	.byte	31
	.byte	'Ifx_SRC_GPT12',0,11,214,1,3
	.word	72584
	.byte	18,12
	.word	10863
	.byte	19,2,0,18,32
	.word	10863
	.byte	19,7,0,18,32
	.word	72621
	.byte	19,0,0,18,88
	.word	641
	.byte	19,87,0,18,108
	.word	10863
	.byte	19,26,0,18,96
	.word	641
	.byte	19,95,0,18,96
	.word	72621
	.byte	19,2,0,18,160,3
	.word	641
	.byte	19,159,3,0,18,64
	.word	72621
	.byte	19,1,0,18,192,3
	.word	641
	.byte	19,191,3,0,18,16
	.word	10863
	.byte	19,3,0,18,64
	.word	72706
	.byte	19,3,0,18,192,2
	.word	641
	.byte	19,191,2,0,18,52
	.word	641
	.byte	19,51,0,10
	.byte	'_Ifx_SRC_GTM',0,11,217,1,25,204,18,13
	.byte	'AEIIRQ',0
	.word	10863
	.byte	4,2,35,0,13
	.byte	'ARUIRQ',0
	.word	72612
	.byte	12,2,35,4,13
	.byte	'reserved_10',0
	.word	3012
	.byte	4,2,35,16,13
	.byte	'BRCIRQ',0
	.word	10863
	.byte	4,2,35,20,13
	.byte	'CMPIRQ',0
	.word	10863
	.byte	4,2,35,24,13
	.byte	'SPEIRQ',0
	.word	71516
	.byte	8,2,35,28,13
	.byte	'reserved_24',0
	.word	4831
	.byte	8,2,35,36,13
	.byte	'PSM',0
	.word	72630
	.byte	32,2,35,44,13
	.byte	'reserved_4C',0
	.word	72639
	.byte	88,2,35,76,13
	.byte	'DPLL',0
	.word	72648
	.byte	108,3,35,164,1,13
	.byte	'reserved_110',0
	.word	72657
	.byte	96,3,35,144,2,13
	.byte	'ERR',0
	.word	10863
	.byte	4,3,35,240,2,13
	.byte	'reserved_174',0
	.word	5171
	.byte	12,3,35,244,2,13
	.byte	'TIM',0
	.word	72666
	.byte	96,3,35,128,3,13
	.byte	'reserved_1E0',0
	.word	72675
	.byte	160,3,3,35,224,3,13
	.byte	'MCS',0
	.word	72666
	.byte	96,3,35,128,7,13
	.byte	'reserved_3E0',0
	.word	72675
	.byte	160,3,3,35,224,7,13
	.byte	'TOM',0
	.word	72686
	.byte	64,3,35,128,11,13
	.byte	'reserved_5C0',0
	.word	72695
	.byte	192,3,3,35,192,11,13
	.byte	'ATOM',0
	.word	72715
	.byte	64,3,35,128,15,13
	.byte	'reserved_7C0',0
	.word	72724
	.byte	192,2,3,35,192,15,13
	.byte	'MCSW0',0
	.word	72612
	.byte	12,3,35,128,18,13
	.byte	'reserved_90C',0
	.word	72735
	.byte	52,3,35,140,18,13
	.byte	'MCSW1',0
	.word	72612
	.byte	12,3,35,192,18,0,14
	.word	72744
	.byte	31
	.byte	'Ifx_SRC_GTM',0,11,243,1,3
	.word	73204
	.byte	10
	.byte	'_Ifx_SRC_HSCT',0,11,246,1,25,4,13
	.byte	'SR',0
	.word	10863
	.byte	4,2,35,0,0,14
	.word	73230
	.byte	31
	.byte	'Ifx_SRC_HSCT',0,11,249,1,3
	.word	73263
	.byte	10
	.byte	'_Ifx_SRC_HSSL',0,11,252,1,25,16,13
	.byte	'COK',0
	.word	10863
	.byte	4,2,35,0,13
	.byte	'RDI',0
	.word	10863
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	10863
	.byte	4,2,35,8,13
	.byte	'TRG',0
	.word	10863
	.byte	4,2,35,12,0,14
	.word	73290
	.byte	31
	.byte	'Ifx_SRC_HSSL',0,11,130,2,3
	.word	73363
	.byte	18,56
	.word	641
	.byte	19,55,0,10
	.byte	'_Ifx_SRC_I2C',0,11,133,2,25,80,13
	.byte	'BREQ',0
	.word	10863
	.byte	4,2,35,0,13
	.byte	'LBREQ',0
	.word	10863
	.byte	4,2,35,4,13
	.byte	'SREQ',0
	.word	10863
	.byte	4,2,35,8,13
	.byte	'LSREQ',0
	.word	10863
	.byte	4,2,35,12,13
	.byte	'ERR',0
	.word	10863
	.byte	4,2,35,16,13
	.byte	'P',0
	.word	10863
	.byte	4,2,35,20,13
	.byte	'reserved_18',0
	.word	73390
	.byte	56,2,35,24,0,14
	.word	73399
	.byte	31
	.byte	'Ifx_SRC_I2C',0,11,142,2,3
	.word	73522
	.byte	10
	.byte	'_Ifx_SRC_LMU',0,11,145,2,25,4,13
	.byte	'SR',0
	.word	10863
	.byte	4,2,35,0,0,14
	.word	73548
	.byte	31
	.byte	'Ifx_SRC_LMU',0,11,148,2,3
	.word	73580
	.byte	10
	.byte	'_Ifx_SRC_MSC',0,11,151,2,25,20,13
	.byte	'SR0',0
	.word	10863
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10863
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	10863
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	10863
	.byte	4,2,35,12,13
	.byte	'SR4',0
	.word	10863
	.byte	4,2,35,16,0,14
	.word	73606
	.byte	31
	.byte	'Ifx_SRC_MSC',0,11,158,2,3
	.word	73691
	.byte	10
	.byte	'_Ifx_SRC_PMU',0,11,161,2,25,4,13
	.byte	'SR',0
	.word	10863
	.byte	4,2,35,0,0,14
	.word	73717
	.byte	31
	.byte	'Ifx_SRC_PMU',0,11,164,2,3
	.word	73749
	.byte	10
	.byte	'_Ifx_SRC_PSI5',0,11,167,2,25,32,13
	.byte	'SR',0
	.word	72621
	.byte	32,2,35,0,0,14
	.word	73775
	.byte	31
	.byte	'Ifx_SRC_PSI5',0,11,170,2,3
	.word	73808
	.byte	10
	.byte	'_Ifx_SRC_PSI5S',0,11,173,2,25,32,13
	.byte	'SR',0
	.word	72621
	.byte	32,2,35,0,0,14
	.word	73835
	.byte	31
	.byte	'Ifx_SRC_PSI5S',0,11,176,2,3
	.word	73869
	.byte	10
	.byte	'_Ifx_SRC_QSPI',0,11,179,2,25,24,13
	.byte	'TX',0
	.word	10863
	.byte	4,2,35,0,13
	.byte	'RX',0
	.word	10863
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	10863
	.byte	4,2,35,8,13
	.byte	'PT',0
	.word	10863
	.byte	4,2,35,12,13
	.byte	'HC',0
	.word	10863
	.byte	4,2,35,16,13
	.byte	'U',0
	.word	10863
	.byte	4,2,35,20,0,14
	.word	73897
	.byte	31
	.byte	'Ifx_SRC_QSPI',0,11,187,2,3
	.word	73990
	.byte	10
	.byte	'_Ifx_SRC_SCR',0,11,190,2,25,4,13
	.byte	'SR',0
	.word	10863
	.byte	4,2,35,0,0,14
	.word	74017
	.byte	31
	.byte	'Ifx_SRC_SCR',0,11,193,2,3
	.word	74049
	.byte	10
	.byte	'_Ifx_SRC_SCU',0,11,196,2,25,20,13
	.byte	'DTS',0
	.word	10863
	.byte	4,2,35,0,13
	.byte	'ERU',0
	.word	72706
	.byte	16,2,35,4,0,14
	.word	74075
	.byte	31
	.byte	'Ifx_SRC_SCU',0,11,200,2,3
	.word	74121
	.byte	18,24
	.word	10863
	.byte	19,5,0,10
	.byte	'_Ifx_SRC_SENT',0,11,203,2,25,24,13
	.byte	'SR',0
	.word	74147
	.byte	24,2,35,0,0,14
	.word	74156
	.byte	31
	.byte	'Ifx_SRC_SENT',0,11,206,2,3
	.word	74189
	.byte	10
	.byte	'_Ifx_SRC_SMU',0,11,209,2,25,12,13
	.byte	'SR',0
	.word	72612
	.byte	12,2,35,0,0,14
	.word	74216
	.byte	31
	.byte	'Ifx_SRC_SMU',0,11,212,2,3
	.word	74248
	.byte	10
	.byte	'_Ifx_SRC_STM',0,11,215,2,25,8,13
	.byte	'SR0',0
	.word	10863
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10863
	.byte	4,2,35,4,0,14
	.word	74274
	.byte	31
	.byte	'Ifx_SRC_STM',0,11,219,2,3
	.word	74320
	.byte	10
	.byte	'_Ifx_SRC_VADCCG',0,11,222,2,25,16,13
	.byte	'SR0',0
	.word	10863
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10863
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	10863
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	10863
	.byte	4,2,35,12,0,14
	.word	74346
	.byte	31
	.byte	'Ifx_SRC_VADCCG',0,11,228,2,3
	.word	74421
	.byte	10
	.byte	'_Ifx_SRC_VADCG',0,11,231,2,25,16,13
	.byte	'SR0',0
	.word	10863
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10863
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	10863
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	10863
	.byte	4,2,35,12,0,14
	.word	74450
	.byte	31
	.byte	'Ifx_SRC_VADCG',0,11,237,2,3
	.word	74524
	.byte	10
	.byte	'_Ifx_SRC_XBAR',0,11,240,2,25,4,13
	.byte	'SRC',0
	.word	10863
	.byte	4,2,35,0,0,14
	.word	74552
	.byte	31
	.byte	'Ifx_SRC_XBAR',0,11,243,2,3
	.word	74586
	.byte	18,4
	.word	71142
	.byte	19,0,0,14
	.word	74613
	.byte	10
	.byte	'_Ifx_SRC_GAGBT',0,11,128,3,25,4,13
	.byte	'AGBT',0
	.word	74622
	.byte	4,2,35,0,0,14
	.word	74627
	.byte	31
	.byte	'Ifx_SRC_GAGBT',0,11,131,3,3
	.word	74663
	.byte	18,48
	.word	71200
	.byte	19,3,0,14
	.word	74691
	.byte	10
	.byte	'_Ifx_SRC_GASCLIN',0,11,134,3,25,48,13
	.byte	'ASCLIN',0
	.word	74700
	.byte	48,2,35,0,0,14
	.word	74705
	.byte	31
	.byte	'Ifx_SRC_GASCLIN',0,11,137,3,3
	.word	74745
	.byte	14
	.word	71287
	.byte	10
	.byte	'_Ifx_SRC_GBCU',0,11,140,3,25,4,13
	.byte	'SPB',0
	.word	74775
	.byte	4,2,35,0,0,14
	.word	74780
	.byte	31
	.byte	'Ifx_SRC_GBCU',0,11,143,3,3
	.word	74814
	.byte	18,64
	.word	71361
	.byte	19,0,0,14
	.word	74841
	.byte	10
	.byte	'_Ifx_SRC_GCAN',0,11,146,3,25,64,13
	.byte	'CAN',0
	.word	74850
	.byte	64,2,35,0,0,14
	.word	74855
	.byte	31
	.byte	'Ifx_SRC_GCAN',0,11,149,3,3
	.word	74889
	.byte	18,32
	.word	71418
	.byte	19,1,0,14
	.word	74916
	.byte	10
	.byte	'_Ifx_SRC_GCCU6',0,11,152,3,25,32,13
	.byte	'CCU6',0
	.word	74925
	.byte	32,2,35,0,0,14
	.word	74930
	.byte	31
	.byte	'Ifx_SRC_GCCU6',0,11,155,3,3
	.word	74966
	.byte	14
	.word	71525
	.byte	10
	.byte	'_Ifx_SRC_GCERBERUS',0,11,158,3,25,8,13
	.byte	'CERBERUS',0
	.word	74994
	.byte	8,2,35,0,0,14
	.word	74999
	.byte	31
	.byte	'Ifx_SRC_GCERBERUS',0,11,161,3,3
	.word	75043
	.byte	18,16
	.word	71591
	.byte	19,0,0,14
	.word	75075
	.byte	10
	.byte	'_Ifx_SRC_GCIF',0,11,164,3,25,16,13
	.byte	'CIF',0
	.word	75084
	.byte	16,2,35,0,0,14
	.word	75089
	.byte	31
	.byte	'Ifx_SRC_GCIF',0,11,167,3,3
	.word	75123
	.byte	18,8
	.word	71690
	.byte	19,1,0,14
	.word	75150
	.byte	10
	.byte	'_Ifx_SRC_GCPU',0,11,170,3,25,8,13
	.byte	'CPU',0
	.word	75159
	.byte	8,2,35,0,0,14
	.word	75164
	.byte	31
	.byte	'Ifx_SRC_GCPU',0,11,173,3,3
	.word	75198
	.byte	18,208,1
	.word	71761
	.byte	19,0,0,14
	.word	75225
	.byte	10
	.byte	'_Ifx_SRC_GDMA',0,11,176,3,25,208,1,13
	.byte	'DMA',0
	.word	75235
	.byte	208,1,2,35,0,0,14
	.word	75240
	.byte	31
	.byte	'Ifx_SRC_GDMA',0,11,179,3,3
	.word	75276
	.byte	14
	.word	71854
	.byte	14
	.word	71854
	.byte	14
	.word	71854
	.byte	10
	.byte	'_Ifx_SRC_GDSADC',0,11,182,3,25,32,13
	.byte	'DSADC0',0
	.word	75303
	.byte	8,2,35,0,13
	.byte	'reserved_8',0
	.word	4831
	.byte	8,2,35,8,13
	.byte	'DSADC2',0
	.word	75308
	.byte	8,2,35,16,13
	.byte	'DSADC3',0
	.word	75313
	.byte	8,2,35,24,0,14
	.word	75318
	.byte	31
	.byte	'Ifx_SRC_GDSADC',0,11,188,3,3
	.word	75409
	.byte	18,4
	.word	71930
	.byte	19,0,0,14
	.word	75438
	.byte	10
	.byte	'_Ifx_SRC_GEMEM',0,11,191,3,25,4,13
	.byte	'EMEM',0
	.word	75447
	.byte	4,2,35,0,0,14
	.word	75452
	.byte	31
	.byte	'Ifx_SRC_GEMEM',0,11,194,3,3
	.word	75488
	.byte	18,80
	.word	71990
	.byte	19,0,0,14
	.word	75516
	.byte	10
	.byte	'_Ifx_SRC_GERAY',0,11,197,3,25,80,13
	.byte	'ERAY',0
	.word	75525
	.byte	80,2,35,0,0,14
	.word	75530
	.byte	31
	.byte	'Ifx_SRC_GERAY',0,11,200,3,3
	.word	75566
	.byte	18,4
	.word	72144
	.byte	19,0,0,14
	.word	75594
	.byte	10
	.byte	'_Ifx_SRC_GETH',0,11,203,3,25,4,13
	.byte	'ETH',0
	.word	75603
	.byte	4,2,35,0,0,14
	.word	75608
	.byte	31
	.byte	'Ifx_SRC_GETH',0,11,206,3,3
	.word	75642
	.byte	18,4
	.word	72202
	.byte	19,0,0,14
	.word	75669
	.byte	10
	.byte	'_Ifx_SRC_GFCE',0,11,209,3,25,4,13
	.byte	'FCE',0
	.word	75678
	.byte	4,2,35,0,0,14
	.word	75683
	.byte	31
	.byte	'Ifx_SRC_GFCE',0,11,212,3,3
	.word	75717
	.byte	18,12
	.word	72260
	.byte	19,0,0,14
	.word	75744
	.byte	10
	.byte	'_Ifx_SRC_GFFT',0,11,215,3,25,12,13
	.byte	'FFT',0
	.word	75753
	.byte	12,2,35,0,0,14
	.word	75758
	.byte	31
	.byte	'Ifx_SRC_GFFT',0,11,218,3,3
	.word	75792
	.byte	18,64
	.word	72346
	.byte	19,1,0,14
	.word	75819
	.byte	10
	.byte	'_Ifx_SRC_GGPSR',0,11,221,3,25,64,13
	.byte	'GPSR',0
	.word	75828
	.byte	64,2,35,0,0,14
	.word	75833
	.byte	31
	.byte	'Ifx_SRC_GGPSR',0,11,224,3,3
	.word	75869
	.byte	18,48
	.word	72467
	.byte	19,0,0,14
	.word	75897
	.byte	10
	.byte	'_Ifx_SRC_GGPT12',0,11,227,3,25,48,13
	.byte	'GPT12',0
	.word	75906
	.byte	48,2,35,0,0,14
	.word	75911
	.byte	31
	.byte	'Ifx_SRC_GGPT12',0,11,230,3,3
	.word	75949
	.byte	18,204,18
	.word	72744
	.byte	19,0,0,14
	.word	75978
	.byte	10
	.byte	'_Ifx_SRC_GGTM',0,11,233,3,25,204,18,13
	.byte	'GTM',0
	.word	75988
	.byte	204,18,2,35,0,0,14
	.word	75993
	.byte	31
	.byte	'Ifx_SRC_GGTM',0,11,236,3,3
	.word	76029
	.byte	18,4
	.word	73230
	.byte	19,0,0,14
	.word	76056
	.byte	10
	.byte	'_Ifx_SRC_GHSCT',0,11,239,3,25,4,13
	.byte	'HSCT',0
	.word	76065
	.byte	4,2,35,0,0,14
	.word	76070
	.byte	31
	.byte	'Ifx_SRC_GHSCT',0,11,242,3,3
	.word	76106
	.byte	18,64
	.word	73290
	.byte	19,3,0,14
	.word	76134
	.byte	10
	.byte	'_Ifx_SRC_GHSSL',0,11,245,3,25,68,13
	.byte	'HSSL',0
	.word	76143
	.byte	64,2,35,0,13
	.byte	'EXI',0
	.word	10863
	.byte	4,2,35,64,0,14
	.word	76148
	.byte	31
	.byte	'Ifx_SRC_GHSSL',0,11,249,3,3
	.word	76197
	.byte	18,80
	.word	73399
	.byte	19,0,0,14
	.word	76225
	.byte	10
	.byte	'_Ifx_SRC_GI2C',0,11,252,3,25,80,13
	.byte	'I2C',0
	.word	76234
	.byte	80,2,35,0,0,14
	.word	76239
	.byte	31
	.byte	'Ifx_SRC_GI2C',0,11,255,3,3
	.word	76273
	.byte	18,4
	.word	73548
	.byte	19,0,0,14
	.word	76300
	.byte	10
	.byte	'_Ifx_SRC_GLMU',0,11,130,4,25,4,13
	.byte	'LMU',0
	.word	76309
	.byte	4,2,35,0,0,14
	.word	76314
	.byte	31
	.byte	'Ifx_SRC_GLMU',0,11,133,4,3
	.word	76348
	.byte	18,40
	.word	73606
	.byte	19,1,0,14
	.word	76375
	.byte	10
	.byte	'_Ifx_SRC_GMSC',0,11,136,4,25,40,13
	.byte	'MSC',0
	.word	76384
	.byte	40,2,35,0,0,14
	.word	76389
	.byte	31
	.byte	'Ifx_SRC_GMSC',0,11,139,4,3
	.word	76423
	.byte	18,8
	.word	73717
	.byte	19,1,0,14
	.word	76450
	.byte	10
	.byte	'_Ifx_SRC_GPMU',0,11,142,4,25,8,13
	.byte	'PMU',0
	.word	76459
	.byte	8,2,35,0,0,14
	.word	76464
	.byte	31
	.byte	'Ifx_SRC_GPMU',0,11,145,4,3
	.word	76498
	.byte	18,32
	.word	73775
	.byte	19,0,0,14
	.word	76525
	.byte	10
	.byte	'_Ifx_SRC_GPSI5',0,11,148,4,25,32,13
	.byte	'PSI5',0
	.word	76534
	.byte	32,2,35,0,0,14
	.word	76539
	.byte	31
	.byte	'Ifx_SRC_GPSI5',0,11,151,4,3
	.word	76575
	.byte	18,32
	.word	73835
	.byte	19,0,0,14
	.word	76603
	.byte	10
	.byte	'_Ifx_SRC_GPSI5S',0,11,154,4,25,32,13
	.byte	'PSI5S',0
	.word	76612
	.byte	32,2,35,0,0,14
	.word	76617
	.byte	31
	.byte	'Ifx_SRC_GPSI5S',0,11,157,4,3
	.word	76655
	.byte	18,96
	.word	73897
	.byte	19,3,0,14
	.word	76684
	.byte	10
	.byte	'_Ifx_SRC_GQSPI',0,11,160,4,25,96,13
	.byte	'QSPI',0
	.word	76693
	.byte	96,2,35,0,0,14
	.word	76698
	.byte	31
	.byte	'Ifx_SRC_GQSPI',0,11,163,4,3
	.word	76734
	.byte	18,4
	.word	74017
	.byte	19,0,0,14
	.word	76762
	.byte	10
	.byte	'_Ifx_SRC_GSCR',0,11,166,4,25,4,13
	.byte	'SCR',0
	.word	76771
	.byte	4,2,35,0,0,14
	.word	76776
	.byte	31
	.byte	'Ifx_SRC_GSCR',0,11,169,4,3
	.word	76810
	.byte	14
	.word	74075
	.byte	10
	.byte	'_Ifx_SRC_GSCU',0,11,172,4,25,20,13
	.byte	'SCU',0
	.word	76837
	.byte	20,2,35,0,0,14
	.word	76842
	.byte	31
	.byte	'Ifx_SRC_GSCU',0,11,175,4,3
	.word	76876
	.byte	18,24
	.word	74156
	.byte	19,0,0,14
	.word	76903
	.byte	10
	.byte	'_Ifx_SRC_GSENT',0,11,178,4,25,24,13
	.byte	'SENT',0
	.word	76912
	.byte	24,2,35,0,0,14
	.word	76917
	.byte	31
	.byte	'Ifx_SRC_GSENT',0,11,181,4,3
	.word	76953
	.byte	18,12
	.word	74216
	.byte	19,0,0,14
	.word	76981
	.byte	10
	.byte	'_Ifx_SRC_GSMU',0,11,184,4,25,12,13
	.byte	'SMU',0
	.word	76990
	.byte	12,2,35,0,0,14
	.word	76995
	.byte	31
	.byte	'Ifx_SRC_GSMU',0,11,187,4,3
	.word	77029
	.byte	18,16
	.word	74274
	.byte	19,1,0,14
	.word	77056
	.byte	10
	.byte	'_Ifx_SRC_GSTM',0,11,190,4,25,16,13
	.byte	'STM',0
	.word	77065
	.byte	16,2,35,0,0,14
	.word	77070
	.byte	31
	.byte	'Ifx_SRC_GSTM',0,11,193,4,3
	.word	77104
	.byte	18,64
	.word	74450
	.byte	19,3,0,14
	.word	77131
	.byte	18,224,1
	.word	641
	.byte	19,223,1,0,18,32
	.word	74346
	.byte	19,1,0,14
	.word	77156
	.byte	10
	.byte	'_Ifx_SRC_GVADC',0,11,196,4,25,192,2,13
	.byte	'G',0
	.word	77140
	.byte	64,2,35,0,13
	.byte	'reserved_40',0
	.word	77145
	.byte	224,1,2,35,64,13
	.byte	'CG',0
	.word	77165
	.byte	32,3,35,160,2,0,14
	.word	77170
	.byte	31
	.byte	'Ifx_SRC_GVADC',0,11,201,4,3
	.word	77239
	.byte	14
	.word	74552
	.byte	10
	.byte	'_Ifx_SRC_GXBAR',0,11,204,4,25,4,13
	.byte	'XBAR',0
	.word	77267
	.byte	4,2,35,0,0,14
	.word	77272
	.byte	31
	.byte	'Ifx_SRC_GXBAR',0,11,207,4,3
	.word	77308
	.byte	31
	.byte	'Ifx_STM_ACCEN0_Bits',0,16,79,3
	.word	20793
	.byte	31
	.byte	'Ifx_STM_ACCEN1_Bits',0,16,85,3
	.word	20704
	.byte	31
	.byte	'Ifx_STM_CAP_Bits',0,16,91,3
	.word	19234
	.byte	31
	.byte	'Ifx_STM_CAPSV_Bits',0,16,97,3
	.word	20111
	.byte	31
	.byte	'Ifx_STM_CLC_Bits',0,16,107,3
	.word	18357
	.byte	31
	.byte	'Ifx_STM_CMCON_Bits',0,16,120,3
	.word	19412
	.byte	31
	.byte	'Ifx_STM_CMP_Bits',0,16,126,3
	.word	19321
	.byte	31
	.byte	'Ifx_STM_ICR_Bits',0,16,139,1,3
	.word	19643
	.byte	31
	.byte	'Ifx_STM_ID_Bits',0,16,147,1,3
	.word	18513
	.byte	31
	.byte	'Ifx_STM_ISCR_Bits',0,16,157,1,3
	.word	19860
	.byte	31
	.byte	'Ifx_STM_KRST0_Bits',0,16,165,1,3
	.word	20581
	.byte	31
	.byte	'Ifx_STM_KRST1_Bits',0,16,172,1,3
	.word	20477
	.byte	31
	.byte	'Ifx_STM_KRSTCLR_Bits',0,16,179,1,3
	.word	20371
	.byte	31
	.byte	'Ifx_STM_OCS_Bits',0,16,189,1,3
	.word	20211
	.byte	31
	.byte	'Ifx_STM_TIM0_Bits',0,16,195,1,3
	.word	18635
	.byte	31
	.byte	'Ifx_STM_TIM0SV_Bits',0,16,201,1,3
	.word	20024
	.byte	31
	.byte	'Ifx_STM_TIM1_Bits',0,16,207,1,3
	.word	18720
	.byte	31
	.byte	'Ifx_STM_TIM2_Bits',0,16,213,1,3
	.word	18805
	.byte	31
	.byte	'Ifx_STM_TIM3_Bits',0,16,219,1,3
	.word	18890
	.byte	31
	.byte	'Ifx_STM_TIM4_Bits',0,16,225,1,3
	.word	18976
	.byte	31
	.byte	'Ifx_STM_TIM5_Bits',0,16,231,1,3
	.word	19062
	.byte	31
	.byte	'Ifx_STM_TIM6_Bits',0,16,237,1,3
	.word	19148
	.byte	31
	.byte	'Ifx_STM_ACCEN0',0,16,250,1,3
	.word	21322
	.byte	31
	.byte	'Ifx_STM_ACCEN1',0,16,130,2,3
	.word	20753
	.byte	31
	.byte	'Ifx_STM_CAP',0,16,138,2,3
	.word	19281
	.byte	31
	.byte	'Ifx_STM_CAPSV',0,16,146,2,3
	.word	20160
	.byte	31
	.byte	'Ifx_STM_CLC',0,16,154,2,3
	.word	18473
	.byte	31
	.byte	'Ifx_STM_CMCON',0,16,162,2,3
	.word	19603
	.byte	31
	.byte	'Ifx_STM_CMP',0,16,170,2,3
	.word	19363
	.byte	31
	.byte	'Ifx_STM_ICR',0,16,178,2,3
	.word	19820
	.byte	31
	.byte	'Ifx_STM_ID',0,16,186,2,3
	.word	18595
	.byte	31
	.byte	'Ifx_STM_ISCR',0,16,194,2,3
	.word	19984
	.byte	31
	.byte	'Ifx_STM_KRST0',0,16,202,2,3
	.word	20664
	.byte	31
	.byte	'Ifx_STM_KRST1',0,16,210,2,3
	.word	20541
	.byte	31
	.byte	'Ifx_STM_KRSTCLR',0,16,218,2,3
	.word	20437
	.byte	31
	.byte	'Ifx_STM_OCS',0,16,226,2,3
	.word	20331
	.byte	31
	.byte	'Ifx_STM_TIM0',0,16,234,2,3
	.word	18680
	.byte	31
	.byte	'Ifx_STM_TIM0SV',0,16,242,2,3
	.word	20071
	.byte	31
	.byte	'Ifx_STM_TIM1',0,16,250,2,3
	.word	18765
	.byte	31
	.byte	'Ifx_STM_TIM2',0,16,130,3,3
	.word	18850
	.byte	31
	.byte	'Ifx_STM_TIM3',0,16,138,3,3
	.word	18936
	.byte	31
	.byte	'Ifx_STM_TIM4',0,16,146,3,3
	.word	19022
	.byte	31
	.byte	'Ifx_STM_TIM5',0,16,154,3,3
	.word	19108
	.byte	31
	.byte	'Ifx_STM_TIM6',0,16,162,3,3
	.word	19194
	.byte	14
	.word	21362
	.byte	31
	.byte	'Ifx_STM',0,16,201,3,3
	.word	78419
	.byte	15,5,144,1,9,1,16
	.byte	'IfxCpu_CounterMode_normal',0,0,16
	.byte	'IfxCpu_CounterMode_task',0,1,0,31
	.byte	'IfxCpu_CounterMode',0,5,148,1,3
	.word	78441
	.byte	20,5,160,1,9,6,13
	.byte	'counter',0
	.word	1775
	.byte	4,2,35,0,13
	.byte	'overlfow',0
	.word	641
	.byte	1,2,35,4,0,31
	.byte	'IfxCpu_Counter',0,5,164,1,3
	.word	78530
	.byte	20,5,172,1,9,32,13
	.byte	'instruction',0
	.word	78530
	.byte	6,2,35,0,13
	.byte	'clock',0
	.word	78530
	.byte	6,2,35,6,13
	.byte	'counter1',0
	.word	78530
	.byte	6,2,35,12,13
	.byte	'counter2',0
	.word	78530
	.byte	6,2,35,18,13
	.byte	'counter3',0
	.word	78530
	.byte	6,2,35,24,0,31
	.byte	'IfxCpu_Perf',0,5,179,1,3
	.word	78596
	.byte	10
	.byte	'_Ifx_FLASH_ACCEN0_Bits',0,32,45,16,4,11
	.byte	'EN0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	641
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	641
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	641
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	641
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	641
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	641
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	641
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	641
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	641
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	641
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	641
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	641
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_FLASH_ACCEN0_Bits',0,32,79,3
	.word	78714
	.byte	10
	.byte	'_Ifx_FLASH_ACCEN1_Bits',0,32,82,16,4,11
	.byte	'reserved_0',0,4
	.word	466
	.byte	32,0,2,35,0,0,31
	.byte	'Ifx_FLASH_ACCEN1_Bits',0,32,85,3
	.word	79275
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_CFG_Bits',0,32,88,16,4,11
	.byte	'SEL',0,1
	.word	641
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	641
	.byte	2,0,2,35,0,11
	.byte	'CLR',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'DIS',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	466
	.byte	22,0,2,35,0,0,31
	.byte	'Ifx_FLASH_CBAB_CFG_Bits',0,32,95,3
	.word	79356
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_STAT_Bits',0,32,98,16,4,11
	.byte	'VLD0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'VLD1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'VLD2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'VLD3',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'VLD4',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'VLD5',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'VLD6',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'VLD7',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'VLD8',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'VLD9',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	466
	.byte	22,0,2,35,0,0,31
	.byte	'Ifx_FLASH_CBAB_STAT_Bits',0,32,111,3
	.word	79509
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_TOP_Bits',0,32,114,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	466
	.byte	19,8,2,35,0,11
	.byte	'ERR',0,1
	.word	641
	.byte	6,2,2,35,3,11
	.byte	'VLD',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'CLR',0,1
	.word	641
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_FLASH_CBAB_TOP_Bits',0,32,121,3
	.word	79757
	.byte	10
	.byte	'_Ifx_FLASH_COMM0_Bits',0,32,124,16,4,11
	.byte	'STATUS',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	466
	.byte	24,0,2,35,0,0,31
	.byte	'Ifx_FLASH_COMM0_Bits',0,32,128,1,3
	.word	79903
	.byte	10
	.byte	'_Ifx_FLASH_COMM1_Bits',0,32,131,1,16,4,11
	.byte	'STATUS',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'DATA',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	658
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_FLASH_COMM1_Bits',0,32,136,1,3
	.word	80001
	.byte	10
	.byte	'_Ifx_FLASH_COMM2_Bits',0,32,139,1,16,4,11
	.byte	'STATUS',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'DATA',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	658
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_FLASH_COMM2_Bits',0,32,144,1,3
	.word	80117
	.byte	10
	.byte	'_Ifx_FLASH_ECCRD_Bits',0,32,147,1,16,4,11
	.byte	'RCODE',0,4
	.word	466
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	658
	.byte	8,2,2,35,2,11
	.byte	'EDCERRINJ',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'ECCORDIS',0,1
	.word	641
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_FLASH_ECCRD_Bits',0,32,153,1,3
	.word	80233
	.byte	10
	.byte	'_Ifx_FLASH_ECCRP_Bits',0,32,156,1,16,4,11
	.byte	'RCODE',0,4
	.word	466
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	658
	.byte	8,2,2,35,2,11
	.byte	'EDCERRINJ',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'ECCORDIS',0,1
	.word	641
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_FLASH_ECCRP_Bits',0,32,162,1,3
	.word	80373
	.byte	10
	.byte	'_Ifx_FLASH_ECCW_Bits',0,32,165,1,16,4,11
	.byte	'WCODE',0,4
	.word	466
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	658
	.byte	8,2,2,35,2,11
	.byte	'DECENCDIS',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'PECENCDIS',0,1
	.word	641
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_FLASH_ECCW_Bits',0,32,171,1,3
	.word	80513
	.byte	10
	.byte	'_Ifx_FLASH_FCON_Bits',0,32,174,1,16,4,11
	.byte	'WSPFLASH',0,1
	.word	641
	.byte	4,4,2,35,0,11
	.byte	'WSECPF',0,1
	.word	641
	.byte	2,2,2,35,0,11
	.byte	'WSDFLASH',0,2
	.word	658
	.byte	6,4,2,35,0,11
	.byte	'WSECDF',0,1
	.word	641
	.byte	3,1,2,35,1,11
	.byte	'IDLE',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'ESLDIS',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'SLEEP',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'NSAFECC',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'STALL',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'RES21',0,1
	.word	641
	.byte	2,2,2,35,2,11
	.byte	'RES23',0,1
	.word	641
	.byte	2,0,2,35,2,11
	.byte	'VOPERM',0,1
	.word	641
	.byte	1,7,2,35,3,11
	.byte	'SQERM',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'PROERM',0,1
	.word	641
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	641
	.byte	3,2,2,35,3,11
	.byte	'PR5V',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'EOBM',0,1
	.word	641
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_FLASH_FCON_Bits',0,32,193,1,3
	.word	80652
	.byte	10
	.byte	'_Ifx_FLASH_FPRO_Bits',0,32,196,1,16,4,11
	.byte	'PROINP',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'PRODISP',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'PROIND',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'PRODISD',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'PROINHSMCOTP',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'RES5',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'PROINOTP',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'RES7',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'PROINDBG',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'PRODISDBG',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'PROINHSM',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	641
	.byte	5,0,2,35,1,11
	.byte	'DCFP',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'DDFP',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'DDFPX',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'DDFD',0,1
	.word	641
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	641
	.byte	1,2,2,35,2,11
	.byte	'ENPE',0,1
	.word	641
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	641
	.byte	8,0,2,35,3,0,31
	.byte	'Ifx_FLASH_FPRO_Bits',0,32,218,1,3
	.word	81014
	.byte	10
	.byte	'_Ifx_FLASH_FSR_Bits',0,32,221,1,16,4,11
	.byte	'FABUSY',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'D0BUSY',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'RES1',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'P0BUSY',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'P1BUSY',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'RES5',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'RES6',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'PROG',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'ERASE',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'PFPAGE',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'DFPAGE',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'OPER',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'SQER',0,1
	.word	641
	.byte	1,3,2,35,1,11
	.byte	'PROER',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'PFSBER',0,1
	.word	641
	.byte	1,1,2,35,1,11
	.byte	'PFDBER',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'PFMBER',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'RES17',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'DFSBER',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'DFDBER',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'DFTBER',0,1
	.word	641
	.byte	1,3,2,35,2,11
	.byte	'DFMBER',0,1
	.word	641
	.byte	1,2,2,35,2,11
	.byte	'SRIADDERR',0,1
	.word	641
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	658
	.byte	2,7,2,35,2,11
	.byte	'PVER',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'EVER',0,1
	.word	641
	.byte	1,5,2,35,3,11
	.byte	'SPND',0,1
	.word	641
	.byte	1,4,2,35,3,11
	.byte	'SLM',0,1
	.word	641
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	641
	.byte	1,2,2,35,3,11
	.byte	'ORIER',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	641
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_FLASH_FSR_Bits',0,32,254,1,3
	.word	81455
	.byte	10
	.byte	'_Ifx_FLASH_ID_Bits',0,32,129,2,16,4,11
	.byte	'MODREV',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	658
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_FLASH_ID_Bits',0,32,134,2,3
	.word	82061
	.byte	10
	.byte	'_Ifx_FLASH_MARD_Bits',0,32,137,2,16,4,11
	.byte	'HMARGIN',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'SELD0',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'SPND',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'SPNDERR',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,2
	.word	658
	.byte	10,1,2,35,0,11
	.byte	'TRAPDIS',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	658
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_FLASH_MARD_Bits',0,32,147,2,3
	.word	82172
	.byte	10
	.byte	'_Ifx_FLASH_MARP_Bits',0,32,150,2,16,4,11
	.byte	'SELP0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'SELP1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'RES2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'RES3',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	658
	.byte	11,1,2,35,0,11
	.byte	'TRAPDIS',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	658
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_FLASH_MARP_Bits',0,32,159,2,3
	.word	82386
	.byte	10
	.byte	'_Ifx_FLASH_PROCOND_Bits',0,32,162,2,16,4,11
	.byte	'L',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'NSAFECC',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'RAMIN',0,1
	.word	641
	.byte	2,4,2,35,0,11
	.byte	'RAMINSEL',0,1
	.word	641
	.byte	4,0,2,35,0,11
	.byte	'OSCCFG',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'MODE',0,1
	.word	641
	.byte	2,5,2,35,1,11
	.byte	'APREN',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'CAP0EN',0,1
	.word	641
	.byte	1,3,2,35,1,11
	.byte	'CAP1EN',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'CAP2EN',0,1
	.word	641
	.byte	1,1,2,35,1,11
	.byte	'CAP3EN',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'ESR0CNT',0,2
	.word	658
	.byte	12,4,2,35,2,11
	.byte	'RES29',0,1
	.word	641
	.byte	2,2,2,35,3,11
	.byte	'RES30',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'RPRO',0,1
	.word	641
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_FLASH_PROCOND_Bits',0,32,179,2,3
	.word	82573
	.byte	10
	.byte	'_Ifx_FLASH_PROCONDBG_Bits',0,32,182,2,16,4,11
	.byte	'OCDSDIS',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'DBGIFLCK',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'EDM',0,1
	.word	641
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	466
	.byte	28,0,2,35,0,0,31
	.byte	'Ifx_FLASH_PROCONDBG_Bits',0,32,188,2,3
	.word	82897
	.byte	10
	.byte	'_Ifx_FLASH_PROCONHSM_Bits',0,32,191,2,16,4,11
	.byte	'HSMDBGDIS',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'DBGIFLCK',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'TSTIFLCK',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'HSMTSTDIS',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'RES15',0,2
	.word	658
	.byte	12,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	658
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_FLASH_PROCONHSM_Bits',0,32,199,2,3
	.word	83040
	.byte	10
	.byte	'_Ifx_FLASH_PROCONHSMCOTP_Bits',0,32,202,2,16,4,11
	.byte	'HSMBOOTEN',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'SSWWAIT',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'HSMDX',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'HSM6X',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'HSM16X',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'HSM17X',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'S6ROM',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'HSMENPINS',0,2
	.word	658
	.byte	2,7,2,35,0,11
	.byte	'HSMENRES',0,1
	.word	641
	.byte	2,5,2,35,1,11
	.byte	'DESTDBG',0,1
	.word	641
	.byte	2,3,2,35,1,11
	.byte	'BLKFLAN',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	641
	.byte	2,0,2,35,1,11
	.byte	'S16ROM',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'S17ROM',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	658
	.byte	14,0,2,35,2,0,31
	.byte	'Ifx_FLASH_PROCONHSMCOTP_Bits',0,32,219,2,3
	.word	83229
	.byte	10
	.byte	'_Ifx_FLASH_PROCONOTP_Bits',0,32,222,2,16,4,11
	.byte	'S0ROM',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'S1ROM',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'S2ROM',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'S3ROM',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'S4ROM',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'S5ROM',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'S6ROM',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'S7ROM',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'S8ROM',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'S9ROM',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'S10ROM',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'S11ROM',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'S12ROM',0,1
	.word	641
	.byte	1,3,2,35,1,11
	.byte	'S13ROM',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'S14ROM',0,1
	.word	641
	.byte	1,1,2,35,1,11
	.byte	'S15ROM',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'S16ROM',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'S17ROM',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'S18ROM',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'S19ROM',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'S20ROM',0,1
	.word	641
	.byte	1,3,2,35,2,11
	.byte	'S21ROM',0,1
	.word	641
	.byte	1,2,2,35,2,11
	.byte	'S22ROM',0,1
	.word	641
	.byte	1,1,2,35,2,11
	.byte	'S23ROM',0,1
	.word	641
	.byte	1,0,2,35,2,11
	.byte	'S24ROM',0,1
	.word	641
	.byte	1,7,2,35,3,11
	.byte	'S25ROM',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'S26ROM',0,1
	.word	641
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	641
	.byte	2,3,2,35,3,11
	.byte	'BML',0,1
	.word	641
	.byte	2,1,2,35,3,11
	.byte	'TP',0,1
	.word	641
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_FLASH_PROCONOTP_Bits',0,32,254,2,3
	.word	83592
	.byte	10
	.byte	'_Ifx_FLASH_PROCONP_Bits',0,32,129,3,16,4,11
	.byte	'S0L',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'S1L',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'S2L',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'S3L',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'S4L',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'S5L',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'S6L',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'S7L',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'S8L',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'S9L',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'S10L',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'S11L',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'S12L',0,1
	.word	641
	.byte	1,3,2,35,1,11
	.byte	'S13L',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'S14L',0,1
	.word	641
	.byte	1,1,2,35,1,11
	.byte	'S15L',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'S16L',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'S17L',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'S18L',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'S19L',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'S20L',0,1
	.word	641
	.byte	1,3,2,35,2,11
	.byte	'S21L',0,1
	.word	641
	.byte	1,2,2,35,2,11
	.byte	'S22L',0,1
	.word	641
	.byte	1,1,2,35,2,11
	.byte	'S23L',0,1
	.word	641
	.byte	1,0,2,35,2,11
	.byte	'S24L',0,1
	.word	641
	.byte	1,7,2,35,3,11
	.byte	'S25L',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'S26L',0,1
	.word	641
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	641
	.byte	4,1,2,35,3,11
	.byte	'RPRO',0,1
	.word	641
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_FLASH_PROCONP_Bits',0,32,160,3,3
	.word	84187
	.byte	10
	.byte	'_Ifx_FLASH_PROCONWOP_Bits',0,32,163,3,16,4,11
	.byte	'S0WOP',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'S1WOP',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'S2WOP',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'S3WOP',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'S4WOP',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'S5WOP',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'S6WOP',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'S7WOP',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'S8WOP',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'S9WOP',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'S10WOP',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'S11WOP',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'S12WOP',0,1
	.word	641
	.byte	1,3,2,35,1,11
	.byte	'S13WOP',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'S14WOP',0,1
	.word	641
	.byte	1,1,2,35,1,11
	.byte	'S15WOP',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'S16WOP',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'S17WOP',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'S18WOP',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'S19WOP',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'S20WOP',0,1
	.word	641
	.byte	1,3,2,35,2,11
	.byte	'S21WOP',0,1
	.word	641
	.byte	1,2,2,35,2,11
	.byte	'S22WOP',0,1
	.word	641
	.byte	1,1,2,35,2,11
	.byte	'S23WOP',0,1
	.word	641
	.byte	1,0,2,35,2,11
	.byte	'S24WOP',0,1
	.word	641
	.byte	1,7,2,35,3,11
	.byte	'S25WOP',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'S26WOP',0,1
	.word	641
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	641
	.byte	4,1,2,35,3,11
	.byte	'DATM',0,1
	.word	641
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_FLASH_PROCONWOP_Bits',0,32,194,3,3
	.word	84711
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG0_Bits',0,32,197,3,16,4,11
	.byte	'TAG',0,1
	.word	641
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	466
	.byte	26,0,2,35,0,0,31
	.byte	'Ifx_FLASH_RDB_CFG0_Bits',0,32,201,3,3
	.word	85293
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG1_Bits',0,32,204,3,16,4,11
	.byte	'TAG',0,1
	.word	641
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	466
	.byte	26,0,2,35,0,0,31
	.byte	'Ifx_FLASH_RDB_CFG1_Bits',0,32,208,3,3
	.word	85395
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG2_Bits',0,32,211,3,16,4,11
	.byte	'TAG',0,1
	.word	641
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	466
	.byte	26,0,2,35,0,0,31
	.byte	'Ifx_FLASH_RDB_CFG2_Bits',0,32,215,3,3
	.word	85497
	.byte	10
	.byte	'_Ifx_FLASH_RRAD_Bits',0,32,218,3,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	3,5,2,35,0,11
	.byte	'ADD',0,4
	.word	466
	.byte	29,0,2,35,0,0,31
	.byte	'Ifx_FLASH_RRAD_Bits',0,32,222,3,3
	.word	85599
	.byte	10
	.byte	'_Ifx_FLASH_RRCT_Bits',0,32,225,3,16,4,11
	.byte	'STRT',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'STP',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'BUSY',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'DONE',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'ERR',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	641
	.byte	3,0,2,35,0,11
	.byte	'EOBM',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	641
	.byte	7,0,2,35,1,11
	.byte	'CNT',0,2
	.word	658
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_FLASH_RRCT_Bits',0,32,236,3,3
	.word	85693
	.byte	10
	.byte	'_Ifx_FLASH_RRD0_Bits',0,32,239,3,16,4,11
	.byte	'DATA',0,4
	.word	466
	.byte	32,0,2,35,0,0,31
	.byte	'Ifx_FLASH_RRD0_Bits',0,32,242,3,3
	.word	85903
	.byte	10
	.byte	'_Ifx_FLASH_RRD1_Bits',0,32,245,3,16,4,11
	.byte	'DATA',0,4
	.word	466
	.byte	32,0,2,35,0,0,31
	.byte	'Ifx_FLASH_RRD1_Bits',0,32,248,3,3
	.word	85976
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_CFG_Bits',0,32,251,3,16,4,11
	.byte	'SEL',0,1
	.word	641
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	641
	.byte	2,0,2,35,0,11
	.byte	'CLR',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'DIS',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	466
	.byte	22,0,2,35,0,0,31
	.byte	'Ifx_FLASH_UBAB_CFG_Bits',0,32,130,4,3
	.word	86049
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_STAT_Bits',0,32,133,4,16,4,11
	.byte	'VLD0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	466
	.byte	31,0,2,35,0,0,31
	.byte	'Ifx_FLASH_UBAB_STAT_Bits',0,32,137,4,3
	.word	86204
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_TOP_Bits',0,32,140,4,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	466
	.byte	19,8,2,35,0,11
	.byte	'ERR',0,1
	.word	641
	.byte	6,2,2,35,3,11
	.byte	'VLD',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'CLR',0,1
	.word	641
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_FLASH_UBAB_TOP_Bits',0,32,147,4,3
	.word	86309
	.byte	12,32,155,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	78714
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_ACCEN0',0,32,160,4,3
	.word	86457
	.byte	12,32,163,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	79275
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_ACCEN1',0,32,168,4,3
	.word	86523
	.byte	12,32,171,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	79356
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_CBAB_CFG',0,32,176,4,3
	.word	86589
	.byte	12,32,179,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	79509
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_CBAB_STAT',0,32,184,4,3
	.word	86657
	.byte	12,32,187,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	79757
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_CBAB_TOP',0,32,192,4,3
	.word	86726
	.byte	12,32,195,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	79903
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_COMM0',0,32,200,4,3
	.word	86794
	.byte	12,32,203,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	80001
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_COMM1',0,32,208,4,3
	.word	86859
	.byte	12,32,211,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	80117
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_COMM2',0,32,216,4,3
	.word	86924
	.byte	12,32,219,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	80233
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_ECCRD',0,32,224,4,3
	.word	86989
	.byte	12,32,227,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	80373
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_ECCRP',0,32,232,4,3
	.word	87054
	.byte	12,32,235,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	80513
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_ECCW',0,32,240,4,3
	.word	87119
	.byte	12,32,243,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	80652
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_FCON',0,32,248,4,3
	.word	87183
	.byte	12,32,251,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81014
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_FPRO',0,32,128,5,3
	.word	87247
	.byte	12,32,131,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81455
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_FSR',0,32,136,5,3
	.word	87311
	.byte	12,32,139,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	82061
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_ID',0,32,144,5,3
	.word	87374
	.byte	12,32,147,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	82172
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_MARD',0,32,152,5,3
	.word	87436
	.byte	12,32,155,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	82386
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_MARP',0,32,160,5,3
	.word	87500
	.byte	12,32,163,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	82573
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_PROCOND',0,32,168,5,3
	.word	87564
	.byte	12,32,171,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	82897
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_PROCONDBG',0,32,176,5,3
	.word	87631
	.byte	12,32,179,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	83040
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_PROCONHSM',0,32,184,5,3
	.word	87700
	.byte	12,32,187,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	83229
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_PROCONHSMCOTP',0,32,192,5,3
	.word	87769
	.byte	12,32,195,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	83592
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_PROCONOTP',0,32,200,5,3
	.word	87842
	.byte	12,32,203,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	84187
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_PROCONP',0,32,208,5,3
	.word	87911
	.byte	12,32,211,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	84711
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_PROCONWOP',0,32,216,5,3
	.word	87978
	.byte	12,32,219,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	85293
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_RDB_CFG0',0,32,224,5,3
	.word	88047
	.byte	12,32,227,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	85395
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_RDB_CFG1',0,32,232,5,3
	.word	88115
	.byte	12,32,235,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	85497
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_RDB_CFG2',0,32,240,5,3
	.word	88183
	.byte	12,32,243,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	85599
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_RRAD',0,32,248,5,3
	.word	88251
	.byte	12,32,251,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	85693
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_RRCT',0,32,128,6,3
	.word	88315
	.byte	12,32,131,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	85903
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_RRD0',0,32,136,6,3
	.word	88379
	.byte	12,32,139,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	85976
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_RRD1',0,32,144,6,3
	.word	88443
	.byte	12,32,147,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	86049
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_UBAB_CFG',0,32,152,6,3
	.word	88507
	.byte	12,32,155,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	86204
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_UBAB_STAT',0,32,160,6,3
	.word	88575
	.byte	12,32,163,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	86309
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_UBAB_TOP',0,32,168,6,3
	.word	88644
	.byte	10
	.byte	'_Ifx_FLASH_CBAB',0,32,179,6,25,12,13
	.byte	'CFG',0
	.word	86589
	.byte	4,2,35,0,13
	.byte	'STAT',0
	.word	86657
	.byte	4,2,35,4,13
	.byte	'TOP',0
	.word	86726
	.byte	4,2,35,8,0,14
	.word	88712
	.byte	31
	.byte	'Ifx_FLASH_CBAB',0,32,184,6,3
	.word	88775
	.byte	10
	.byte	'_Ifx_FLASH_RDB',0,32,187,6,25,12,13
	.byte	'CFG0',0
	.word	88047
	.byte	4,2,35,0,13
	.byte	'CFG1',0
	.word	88115
	.byte	4,2,35,4,13
	.byte	'CFG2',0
	.word	88183
	.byte	4,2,35,8,0,14
	.word	88804
	.byte	31
	.byte	'Ifx_FLASH_RDB',0,32,192,6,3
	.word	88868
	.byte	10
	.byte	'_Ifx_FLASH_UBAB',0,32,195,6,25,12,13
	.byte	'CFG',0
	.word	88507
	.byte	4,2,35,0,13
	.byte	'STAT',0
	.word	88575
	.byte	4,2,35,4,13
	.byte	'TOP',0
	.word	88644
	.byte	4,2,35,8,0,14
	.word	88896
	.byte	31
	.byte	'Ifx_FLASH_UBAB',0,32,200,6,3
	.word	88959
	.byte	31
	.byte	'Ifx_P_ACCEN0_Bits',0,8,79,3
	.word	8584
	.byte	31
	.byte	'Ifx_P_ACCEN1_Bits',0,8,85,3
	.word	8497
	.byte	31
	.byte	'Ifx_P_ESR_Bits',0,8,107,3
	.word	4840
	.byte	31
	.byte	'Ifx_P_ID_Bits',0,8,115,3
	.word	2893
	.byte	31
	.byte	'Ifx_P_IN_Bits',0,8,137,1,3
	.word	3888
	.byte	31
	.byte	'Ifx_P_IOCR0_Bits',0,8,150,1,3
	.word	3021
	.byte	31
	.byte	'Ifx_P_IOCR12_Bits',0,8,163,1,3
	.word	3668
	.byte	31
	.byte	'Ifx_P_IOCR4_Bits',0,8,176,1,3
	.word	3236
	.byte	31
	.byte	'Ifx_P_IOCR8_Bits',0,8,189,1,3
	.word	3451
	.byte	31
	.byte	'Ifx_P_LPCR0_Bits',0,8,197,1,3
	.word	7856
	.byte	31
	.byte	'Ifx_P_LPCR1_Bits',0,8,205,1,3
	.word	7980
	.byte	31
	.byte	'Ifx_P_LPCR1_P21_Bits',0,8,215,1,3
	.word	8064
	.byte	31
	.byte	'Ifx_P_LPCR2_Bits',0,8,229,1,3
	.word	8244
	.byte	31
	.byte	'Ifx_P_OMCR0_Bits',0,8,240,1,3
	.word	6495
	.byte	31
	.byte	'Ifx_P_OMCR12_Bits',0,8,250,1,3
	.word	7019
	.byte	31
	.byte	'Ifx_P_OMCR4_Bits',0,8,133,2,3
	.word	6669
	.byte	31
	.byte	'Ifx_P_OMCR8_Bits',0,8,144,2,3
	.word	6843
	.byte	31
	.byte	'Ifx_P_OMCR_Bits',0,8,166,2,3
	.word	7508
	.byte	31
	.byte	'Ifx_P_OMR_Bits',0,8,203,2,3
	.word	2322
	.byte	31
	.byte	'Ifx_P_OMSR0_Bits',0,8,213,2,3
	.word	5832
	.byte	31
	.byte	'Ifx_P_OMSR12_Bits',0,8,224,2,3
	.word	6320
	.byte	31
	.byte	'Ifx_P_OMSR4_Bits',0,8,235,2,3
	.word	5979
	.byte	31
	.byte	'Ifx_P_OMSR8_Bits',0,8,246,2,3
	.word	6148
	.byte	31
	.byte	'Ifx_P_OMSR_Bits',0,8,140,3,3
	.word	7175
	.byte	31
	.byte	'Ifx_P_OUT_Bits',0,8,162,3,3
	.word	2006
	.byte	31
	.byte	'Ifx_P_PCSR_Bits',0,8,180,3,3
	.word	5546
	.byte	31
	.byte	'Ifx_P_PDISC_Bits',0,8,202,3,3
	.word	5180
	.byte	31
	.byte	'Ifx_P_PDR0_Bits',0,8,223,3,3
	.word	4211
	.byte	31
	.byte	'Ifx_P_PDR1_Bits',0,8,244,3,3
	.word	4515
	.byte	31
	.byte	'Ifx_P_ACCEN0',0,8,129,4,3
	.word	9111
	.byte	31
	.byte	'Ifx_P_ACCEN1',0,8,137,4,3
	.word	8544
	.byte	31
	.byte	'Ifx_P_ESR',0,8,145,4,3
	.word	5131
	.byte	31
	.byte	'Ifx_P_ID',0,8,153,4,3
	.word	2972
	.byte	31
	.byte	'Ifx_P_IN',0,8,161,4,3
	.word	4162
	.byte	31
	.byte	'Ifx_P_IOCR0',0,8,169,4,3
	.word	3196
	.byte	31
	.byte	'Ifx_P_IOCR12',0,8,177,4,3
	.word	3848
	.byte	31
	.byte	'Ifx_P_IOCR4',0,8,185,4,3
	.word	3411
	.byte	31
	.byte	'Ifx_P_IOCR8',0,8,193,4,3
	.word	3628
	.byte	31
	.byte	'Ifx_P_LPCR0',0,8,201,4,3
	.word	7940
	.byte	31
	.byte	'Ifx_P_LPCR1',0,8,210,4,3
	.word	8189
	.byte	31
	.byte	'Ifx_P_LPCR2',0,8,218,4,3
	.word	8448
	.byte	31
	.byte	'Ifx_P_OMCR',0,8,226,4,3
	.word	7816
	.byte	31
	.byte	'Ifx_P_OMCR0',0,8,234,4,3
	.word	6629
	.byte	31
	.byte	'Ifx_P_OMCR12',0,8,242,4,3
	.word	7135
	.byte	31
	.byte	'Ifx_P_OMCR4',0,8,250,4,3
	.word	6803
	.byte	31
	.byte	'Ifx_P_OMCR8',0,8,130,5,3
	.word	6979
	.byte	31
	.byte	'Ifx_P_OMR',0,8,138,5,3
	.word	2853
	.byte	31
	.byte	'Ifx_P_OMSR',0,8,146,5,3
	.word	7468
	.byte	31
	.byte	'Ifx_P_OMSR0',0,8,154,5,3
	.word	5939
	.byte	31
	.byte	'Ifx_P_OMSR12',0,8,162,5,3
	.word	6455
	.byte	31
	.byte	'Ifx_P_OMSR4',0,8,170,5,3
	.word	6108
	.byte	31
	.byte	'Ifx_P_OMSR8',0,8,178,5,3
	.word	6280
	.byte	31
	.byte	'Ifx_P_OUT',0,8,186,5,3
	.word	2282
	.byte	31
	.byte	'Ifx_P_PCSR',0,8,194,5,3
	.word	5792
	.byte	31
	.byte	'Ifx_P_PDISC',0,8,202,5,3
	.word	5506
	.byte	31
	.byte	'Ifx_P_PDR0',0,8,210,5,3
	.word	4475
	.byte	31
	.byte	'Ifx_P_PDR1',0,8,218,5,3
	.word	4791
	.byte	14
	.word	9151
	.byte	31
	.byte	'Ifx_P',0,8,139,6,3
	.word	90306
	.byte	31
	.byte	'IfxPort_InputMode',0,7,89,3
	.word	9764
	.byte	31
	.byte	'IfxPort_OutputIdx',0,7,130,1,3
	.word	10039
	.byte	31
	.byte	'IfxPort_OutputMode',0,7,138,1,3
	.word	9969
	.byte	15,7,144,1,9,1,16
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed1',0,0,16
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed2',0,1,16
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed3',0,2,16
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed4',0,3,16
	.byte	'IfxPort_PadDriver_lvdsSpeed1',0,4,16
	.byte	'IfxPort_PadDriver_lvdsSpeed2',0,5,16
	.byte	'IfxPort_PadDriver_lvdsSpeed3',0,6,16
	.byte	'IfxPort_PadDriver_lvdsSpeed4',0,7,16
	.byte	'IfxPort_PadDriver_ttlSpeed1',0,8,16
	.byte	'IfxPort_PadDriver_ttlSpeed2',0,9,16
	.byte	'IfxPort_PadDriver_ttlSpeed3',0,10,16
	.byte	'IfxPort_PadDriver_ttlSpeed4',0,11,0,31
	.byte	'IfxPort_PadDriver',0,7,158,1,3
	.word	90407
	.byte	31
	.byte	'IfxPort_State',0,7,178,1,3
	.word	10352
	.byte	20,7,190,1,9,8,13
	.byte	'port',0
	.word	9759
	.byte	4,2,35,0,13
	.byte	'pinIndex',0
	.word	641
	.byte	1,2,35,4,0,31
	.byte	'IfxPort_Pin',0,7,194,1,3
	.word	90872
	.byte	31
	.byte	'IfxScuCcu_PllStepsFunctionHook',0,9,148,1,16
	.word	205
	.byte	20,9,212,5,9,8,13
	.byte	'value',0
	.word	1775
	.byte	4,2,35,0,13
	.byte	'mask',0
	.word	1775
	.byte	4,2,35,4,0,31
	.byte	'IfxScuCcu_CcuconRegConfig',0,9,216,5,3
	.word	90972
	.byte	20,9,221,5,9,8,13
	.byte	'pDivider',0
	.word	641
	.byte	1,2,35,0,13
	.byte	'nDivider',0
	.word	641
	.byte	1,2,35,1,13
	.byte	'k2Initial',0
	.word	641
	.byte	1,2,35,2,13
	.byte	'waitTime',0
	.word	262
	.byte	4,2,35,4,0,31
	.byte	'IfxScuCcu_InitialStepConfig',0,9,227,5,3
	.word	91043
	.byte	20,9,231,5,9,12,13
	.byte	'k2Step',0
	.word	641
	.byte	1,2,35,0,13
	.byte	'waitTime',0
	.word	262
	.byte	4,2,35,2,13
	.byte	'hookFunction',0
	.word	90932
	.byte	4,2,35,8,0,31
	.byte	'IfxScuCcu_PllStepsConfig',0,9,236,5,3
	.word	91160
	.byte	3
	.word	202
	.byte	20,9,244,5,9,48,13
	.byte	'ccucon0',0
	.word	90972
	.byte	8,2,35,0,13
	.byte	'ccucon1',0
	.word	90972
	.byte	8,2,35,8,13
	.byte	'ccucon2',0
	.word	90972
	.byte	8,2,35,16,13
	.byte	'ccucon5',0
	.word	90972
	.byte	8,2,35,24,13
	.byte	'ccucon6',0
	.word	90972
	.byte	8,2,35,32,13
	.byte	'ccucon7',0
	.word	90972
	.byte	8,2,35,40,0,31
	.byte	'IfxScuCcu_ClockDistributionConfig',0,9,252,5,3
	.word	91262
	.byte	20,9,128,6,9,8,13
	.byte	'value',0
	.word	1775
	.byte	4,2,35,0,13
	.byte	'mask',0
	.word	1775
	.byte	4,2,35,4,0,31
	.byte	'IfxScuCcu_FlashWaitstateConfig',0,9,132,6,3
	.word	91414
	.byte	3
	.word	91160
	.byte	20,9,137,6,9,16,13
	.byte	'numOfPllDividerSteps',0
	.word	641
	.byte	1,2,35,0,13
	.byte	'pllDividerStep',0
	.word	91490
	.byte	4,2,35,4,13
	.byte	'pllInitialStep',0
	.word	91043
	.byte	8,2,35,8,0,31
	.byte	'IfxScuCcu_SysPllConfig',0,9,142,6,3
	.word	91495
	.byte	15,33,69,9,1,16
	.byte	'IfxSrc_Tos_cpu0',0,0,16
	.byte	'IfxSrc_Tos_cpu1',0,1,16
	.byte	'IfxSrc_Tos_dma',0,3,0,31
	.byte	'IfxSrc_Tos',0,33,74,3
	.word	91612
	.byte	20,34,59,15,16,13
	.byte	'module',0
	.word	17487
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	90872
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	26340
	.byte	1,2,35,12,0,24
	.word	91690
	.byte	31
	.byte	'IfxAsclin_Cts_In',0,34,64,3
	.word	91741
	.byte	20,34,67,15,16,13
	.byte	'module',0
	.word	17487
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	90872
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	26340
	.byte	1,2,35,12,0,24
	.word	91771
	.byte	31
	.byte	'IfxAsclin_Rx_In',0,34,72,3
	.word	91822
	.byte	20,34,75,15,16,13
	.byte	'module',0
	.word	17487
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	90872
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	10039
	.byte	1,2,35,12,0,24
	.word	91851
	.byte	31
	.byte	'IfxAsclin_Rts_Out',0,34,80,3
	.word	91902
	.byte	20,34,83,15,16,13
	.byte	'module',0
	.word	17487
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	90872
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	10039
	.byte	1,2,35,12,0,24
	.word	91933
	.byte	31
	.byte	'IfxAsclin_Sclk_Out',0,34,88,3
	.word	91984
	.byte	20,34,91,15,16,13
	.byte	'module',0
	.word	17487
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	90872
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	10039
	.byte	1,2,35,12,0,24
	.word	92016
	.byte	31
	.byte	'IfxAsclin_Slso_Out',0,34,96,3
	.word	92067
	.byte	20,34,99,15,16,13
	.byte	'module',0
	.word	17487
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	90872
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	10039
	.byte	1,2,35,12,0,24
	.word	92099
	.byte	31
	.byte	'IfxAsclin_Tx_Out',0,34,104,3
	.word	92150
	.byte	15,12,82,9,1,16
	.byte	'IfxAsclin_Checksum_classic',0,0,16
	.byte	'IfxAsclin_Checksum_enhanced',0,1,0,31
	.byte	'IfxAsclin_Checksum',0,12,86,3
	.word	92180
	.byte	15,12,91,9,1,16
	.byte	'IfxAsclin_ChecksumInjection_notWritten',0,0,16
	.byte	'IfxAsclin_ChecksumInjection_written',0,1,0,31
	.byte	'IfxAsclin_ChecksumInjection',0,12,95,3
	.word	92272
	.byte	15,12,101,9,1,16
	.byte	'IfxAsclin_ClockPolarity_idleLow',0,0,16
	.byte	'IfxAsclin_ClockPolarity_idleHigh',0,1,0,31
	.byte	'IfxAsclin_ClockPolarity',0,12,105,3
	.word	92393
	.byte	15,12,110,9,1,16
	.byte	'IfxAsclin_ClockSource_noClock',0,0,16
	.byte	'IfxAsclin_ClockSource_kernelClock',0,1,16
	.byte	'IfxAsclin_ClockSource_oscillatorClock',0,2,16
	.byte	'IfxAsclin_ClockSource_flexRayClock',0,4,16
	.byte	'IfxAsclin_ClockSource_ascFastClock',0,8,16
	.byte	'IfxAsclin_ClockSource_ascSlowClock',0,16,0,31
	.byte	'IfxAsclin_ClockSource',0,12,118,3
	.word	92500
	.byte	31
	.byte	'IfxAsclin_CtsInputSelect',0,12,129,1,3
	.word	17554
	.byte	15,12,134,1,9,1,16
	.byte	'IfxAsclin_DataLength_1',0,0,16
	.byte	'IfxAsclin_DataLength_2',0,1,16
	.byte	'IfxAsclin_DataLength_3',0,2,16
	.byte	'IfxAsclin_DataLength_4',0,3,16
	.byte	'IfxAsclin_DataLength_5',0,4,16
	.byte	'IfxAsclin_DataLength_6',0,5,16
	.byte	'IfxAsclin_DataLength_7',0,6,16
	.byte	'IfxAsclin_DataLength_8',0,7,16
	.byte	'IfxAsclin_DataLength_9',0,8,16
	.byte	'IfxAsclin_DataLength_10',0,9,16
	.byte	'IfxAsclin_DataLength_11',0,10,16
	.byte	'IfxAsclin_DataLength_12',0,11,16
	.byte	'IfxAsclin_DataLength_13',0,12,16
	.byte	'IfxAsclin_DataLength_14',0,13,16
	.byte	'IfxAsclin_DataLength_15',0,14,16
	.byte	'IfxAsclin_DataLength_16',0,15,0,31
	.byte	'IfxAsclin_DataLength',0,12,152,1,3
	.word	92789
	.byte	15,12,157,1,9,1,16
	.byte	'IfxAsclin_FrameMode_initialise',0,0,16
	.byte	'IfxAsclin_FrameMode_asc',0,1,16
	.byte	'IfxAsclin_FrameMode_spi',0,2,16
	.byte	'IfxAsclin_FrameMode_lin',0,3,0,31
	.byte	'IfxAsclin_FrameMode',0,12,163,1,3
	.word	93233
	.byte	15,12,168,1,9,1,16
	.byte	'IfxAsclin_HeaderResponseSelect_headerAndResponse',0,0,16
	.byte	'IfxAsclin_HeaderResponseSelect_headerOnly',0,1,0,31
	.byte	'IfxAsclin_HeaderResponseSelect',0,12,172,1,3
	.word	93380
	.byte	15,12,179,1,9,1,16
	.byte	'IfxAsclin_IdleDelay_0',0,0,16
	.byte	'IfxAsclin_IdleDelay_1',0,1,16
	.byte	'IfxAsclin_IdleDelay_2',0,2,16
	.byte	'IfxAsclin_IdleDelay_3',0,3,16
	.byte	'IfxAsclin_IdleDelay_4',0,4,16
	.byte	'IfxAsclin_IdleDelay_5',0,5,16
	.byte	'IfxAsclin_IdleDelay_6',0,6,16
	.byte	'IfxAsclin_IdleDelay_7',0,7,0,31
	.byte	'IfxAsclin_IdleDelay',0,12,189,1,3
	.word	93522
	.byte	15,12,195,1,9,1,16
	.byte	'IfxAsclin_LeadDelay_0',0,0,16
	.byte	'IfxAsclin_LeadDelay_1',0,1,16
	.byte	'IfxAsclin_LeadDelay_2',0,2,16
	.byte	'IfxAsclin_LeadDelay_3',0,3,16
	.byte	'IfxAsclin_LeadDelay_4',0,4,16
	.byte	'IfxAsclin_LeadDelay_5',0,5,16
	.byte	'IfxAsclin_LeadDelay_6',0,6,16
	.byte	'IfxAsclin_LeadDelay_7',0,7,0,31
	.byte	'IfxAsclin_LeadDelay',0,12,205,1,3
	.word	93750
	.byte	15,12,210,1,9,1,16
	.byte	'IfxAsclin_LinMode_slave',0,0,16
	.byte	'IfxAsclin_LinMode_master',0,1,0,31
	.byte	'IfxAsclin_LinMode',0,12,214,1,3
	.word	93978
	.byte	15,12,219,1,9,1,16
	.byte	'IfxAsclin_LinResponseTimeoutMode_frameTimeout',0,0,16
	.byte	'IfxAsclin_LinResponseTimeoutMode_responseTimeout',0,1,0,31
	.byte	'IfxAsclin_LinResponseTimeoutMode',0,12,223,1,3
	.word	94065
	.byte	15,12,228,1,9,1,16
	.byte	'IfxAsclin_OversamplingFactor_4',0,3,16
	.byte	'IfxAsclin_OversamplingFactor_5',0,4,16
	.byte	'IfxAsclin_OversamplingFactor_6',0,5,16
	.byte	'IfxAsclin_OversamplingFactor_7',0,6,16
	.byte	'IfxAsclin_OversamplingFactor_8',0,7,16
	.byte	'IfxAsclin_OversamplingFactor_9',0,8,16
	.byte	'IfxAsclin_OversamplingFactor_10',0,9,16
	.byte	'IfxAsclin_OversamplingFactor_11',0,10,16
	.byte	'IfxAsclin_OversamplingFactor_12',0,11,16
	.byte	'IfxAsclin_OversamplingFactor_13',0,12,16
	.byte	'IfxAsclin_OversamplingFactor_14',0,13,16
	.byte	'IfxAsclin_OversamplingFactor_15',0,14,16
	.byte	'IfxAsclin_OversamplingFactor_16',0,15,0,31
	.byte	'IfxAsclin_OversamplingFactor',0,12,243,1,3
	.word	94213
	.byte	15,12,248,1,9,1,16
	.byte	'IfxAsclin_ParityType_even',0,0,16
	.byte	'IfxAsclin_ParityType_odd',0,1,0,31
	.byte	'IfxAsclin_ParityType',0,12,252,1,3
	.word	94694
	.byte	15,12,129,2,9,1,16
	.byte	'IfxAsclin_ReceiveBufferMode_rxFifo',0,0,16
	.byte	'IfxAsclin_ReceiveBufferMode_rxBuffer',0,1,0,31
	.byte	'IfxAsclin_ReceiveBufferMode',0,12,133,2,3
	.word	94786
	.byte	15,12,138,2,9,1,16
	.byte	'IfxAsclin_RtsCtsPolarity_activeHigh',0,0,16
	.byte	'IfxAsclin_RtsCtsPolarity_activeLow',0,1,0,31
	.byte	'IfxAsclin_RtsCtsPolarity',0,12,142,2,3
	.word	94906
	.byte	15,12,147,2,9,1,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_1',0,0,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_2',0,1,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_3',0,2,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_4',0,3,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_5',0,4,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_6',0,5,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_7',0,6,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_8',0,7,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_9',0,8,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_10',0,9,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_11',0,10,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_12',0,11,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_13',0,12,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_14',0,13,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_15',0,14,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_16',0,15,0,31
	.byte	'IfxAsclin_RxFifoInterruptLevel',0,12,165,2,3
	.word	95022
	.byte	15,12,170,2,9,1,16
	.byte	'IfxAsclin_RxFifoOutletWidth_0',0,0,16
	.byte	'IfxAsclin_RxFifoOutletWidth_1',0,1,16
	.byte	'IfxAsclin_RxFifoOutletWidth_2',0,2,16
	.byte	'IfxAsclin_RxFifoOutletWidth_3',0,3,0,31
	.byte	'IfxAsclin_RxFifoOutletWidth',0,12,176,2,3
	.word	95636
	.byte	31
	.byte	'IfxAsclin_RxInputSelect',0,12,191,2,3
	.word	17738
	.byte	15,12,196,2,9,1,16
	.byte	'IfxAsclin_SamplePointPosition_1',0,1,16
	.byte	'IfxAsclin_SamplePointPosition_2',0,2,16
	.byte	'IfxAsclin_SamplePointPosition_3',0,3,16
	.byte	'IfxAsclin_SamplePointPosition_4',0,4,16
	.byte	'IfxAsclin_SamplePointPosition_5',0,5,16
	.byte	'IfxAsclin_SamplePointPosition_6',0,6,16
	.byte	'IfxAsclin_SamplePointPosition_7',0,7,16
	.byte	'IfxAsclin_SamplePointPosition_8',0,8,16
	.byte	'IfxAsclin_SamplePointPosition_9',0,9,16
	.byte	'IfxAsclin_SamplePointPosition_10',0,10,16
	.byte	'IfxAsclin_SamplePointPosition_11',0,11,16
	.byte	'IfxAsclin_SamplePointPosition_12',0,12,16
	.byte	'IfxAsclin_SamplePointPosition_13',0,13,16
	.byte	'IfxAsclin_SamplePointPosition_14',0,14,16
	.byte	'IfxAsclin_SamplePointPosition_15',0,15,0,31
	.byte	'IfxAsclin_SamplePointPosition',0,12,213,2,3
	.word	95841
	.byte	15,12,218,2,9,1,16
	.byte	'IfxAsclin_SamplesPerBit_one',0,0,16
	.byte	'IfxAsclin_SamplesPerBit_three',0,1,0,31
	.byte	'IfxAsclin_SamplesPerBit',0,12,222,2,3
	.word	96403
	.byte	15,12,228,2,9,1,16
	.byte	'IfxAsclin_ShiftDirection_lsbFirst',0,0,16
	.byte	'IfxAsclin_ShiftDirection_msbFirst',0,1,0,31
	.byte	'IfxAsclin_ShiftDirection',0,12,232,2,3
	.word	96505
	.byte	15,12,238,2,9,1,16
	.byte	'IfxAsclin_SlavePolarity_idleLow',0,0,16
	.byte	'IfxAsclin_SlavePolarity_idlehigh',0,1,0,31
	.byte	'IfxAsclin_SlavePolarity',0,12,242,2,3
	.word	96618
	.byte	15,12,247,2,9,1,16
	.byte	'IfxAsclin_SleepMode_enable',0,0,16
	.byte	'IfxAsclin_SleepMode_disable',0,1,0,31
	.byte	'IfxAsclin_SleepMode',0,12,251,2,3
	.word	96727
	.byte	15,12,136,3,9,1,16
	.byte	'IfxAsclin_StopBit_0',0,0,16
	.byte	'IfxAsclin_StopBit_1',0,1,16
	.byte	'IfxAsclin_StopBit_2',0,2,16
	.byte	'IfxAsclin_StopBit_3',0,3,16
	.byte	'IfxAsclin_StopBit_4',0,4,16
	.byte	'IfxAsclin_StopBit_5',0,5,16
	.byte	'IfxAsclin_StopBit_6',0,6,16
	.byte	'IfxAsclin_StopBit_7',0,7,0,31
	.byte	'IfxAsclin_StopBit',0,12,146,3,3
	.word	96822
	.byte	15,12,150,3,9,1,16
	.byte	'IfxAsclin_SuspendMode_none',0,0,16
	.byte	'IfxAsclin_SuspendMode_hard',0,1,16
	.byte	'IfxAsclin_SuspendMode_soft',0,2,0,31
	.byte	'IfxAsclin_SuspendMode',0,12,155,3,3
	.word	97032
	.byte	15,12,160,3,9,1,16
	.byte	'IfxAsclin_TxFifoInletWidth_0',0,0,16
	.byte	'IfxAsclin_TxFifoInletWidth_1',0,1,16
	.byte	'IfxAsclin_TxFifoInletWidth_2',0,2,16
	.byte	'IfxAsclin_TxFifoInletWidth_3',0,3,0,31
	.byte	'IfxAsclin_TxFifoInletWidth',0,12,166,3,3
	.word	97157
	.byte	15,12,171,3,9,1,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_0',0,0,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_1',0,1,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_2',0,2,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_3',0,3,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_4',0,4,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_5',0,5,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_6',0,6,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_7',0,7,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_8',0,8,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_9',0,9,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_10',0,10,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_11',0,11,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_12',0,12,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_13',0,13,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_14',0,14,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_15',0,15,0,31
	.byte	'IfxAsclin_TxFifoInterruptLevel',0,12,189,3,3
	.word	97324
	.byte	31
	.byte	'Ifx_Fifo_Shared',0,14,66,3
	.word	18055
	.byte	31
	.byte	'Ifx_Fifo',0,14,83,3
	.word	18146
	.byte	15,15,151,1,9,1,16
	.byte	'IfxStm_Comparator_0',0,0,16
	.byte	'IfxStm_Comparator_1',0,1,0,31
	.byte	'IfxStm_Comparator',0,15,155,1,3
	.word	97978
	.byte	15,15,159,1,9,1,16
	.byte	'IfxStm_ComparatorInterrupt_ir0',0,0,16
	.byte	'IfxStm_ComparatorInterrupt_ir1',0,1,0,31
	.byte	'IfxStm_ComparatorInterrupt',0,15,163,1,3
	.word	98056
	.byte	15,15,167,1,9,1,16
	.byte	'IfxStm_ComparatorOffset_0',0,0,16
	.byte	'IfxStm_ComparatorOffset_1',0,1,16
	.byte	'IfxStm_ComparatorOffset_2',0,2,16
	.byte	'IfxStm_ComparatorOffset_3',0,3,16
	.byte	'IfxStm_ComparatorOffset_4',0,4,16
	.byte	'IfxStm_ComparatorOffset_5',0,5,16
	.byte	'IfxStm_ComparatorOffset_6',0,6,16
	.byte	'IfxStm_ComparatorOffset_7',0,7,16
	.byte	'IfxStm_ComparatorOffset_8',0,8,16
	.byte	'IfxStm_ComparatorOffset_9',0,9,16
	.byte	'IfxStm_ComparatorOffset_10',0,10,16
	.byte	'IfxStm_ComparatorOffset_11',0,11,16
	.byte	'IfxStm_ComparatorOffset_12',0,12,16
	.byte	'IfxStm_ComparatorOffset_13',0,13,16
	.byte	'IfxStm_ComparatorOffset_14',0,14,16
	.byte	'IfxStm_ComparatorOffset_15',0,15,16
	.byte	'IfxStm_ComparatorOffset_16',0,16,16
	.byte	'IfxStm_ComparatorOffset_17',0,17,16
	.byte	'IfxStm_ComparatorOffset_18',0,18,16
	.byte	'IfxStm_ComparatorOffset_19',0,19,16
	.byte	'IfxStm_ComparatorOffset_20',0,20,16
	.byte	'IfxStm_ComparatorOffset_21',0,21,16
	.byte	'IfxStm_ComparatorOffset_22',0,22,16
	.byte	'IfxStm_ComparatorOffset_23',0,23,16
	.byte	'IfxStm_ComparatorOffset_24',0,24,16
	.byte	'IfxStm_ComparatorOffset_25',0,25,16
	.byte	'IfxStm_ComparatorOffset_26',0,26,16
	.byte	'IfxStm_ComparatorOffset_27',0,27,16
	.byte	'IfxStm_ComparatorOffset_28',0,28,16
	.byte	'IfxStm_ComparatorOffset_29',0,29,16
	.byte	'IfxStm_ComparatorOffset_30',0,30,16
	.byte	'IfxStm_ComparatorOffset_31',0,31,0,31
	.byte	'IfxStm_ComparatorOffset',0,15,201,1,3
	.word	98165
	.byte	15,15,205,1,9,1,16
	.byte	'IfxStm_ComparatorSize_1Bit',0,0,16
	.byte	'IfxStm_ComparatorSize_2Bits',0,1,16
	.byte	'IfxStm_ComparatorSize_3Bits',0,2,16
	.byte	'IfxStm_ComparatorSize_4Bits',0,3,16
	.byte	'IfxStm_ComparatorSize_5Bits',0,4,16
	.byte	'IfxStm_ComparatorSize_6Bits',0,5,16
	.byte	'IfxStm_ComparatorSize_7Bits',0,6,16
	.byte	'IfxStm_ComparatorSize_8Bits',0,7,16
	.byte	'IfxStm_ComparatorSize_9Bits',0,8,16
	.byte	'IfxStm_ComparatorSize_10Bits',0,9,16
	.byte	'IfxStm_ComparatorSize_11Bits',0,10,16
	.byte	'IfxStm_ComparatorSize_12Bits',0,11,16
	.byte	'IfxStm_ComparatorSize_13Bits',0,12,16
	.byte	'IfxStm_ComparatorSize_14Bits',0,13,16
	.byte	'IfxStm_ComparatorSize_15Bits',0,14,16
	.byte	'IfxStm_ComparatorSize_16Bits',0,15,16
	.byte	'IfxStm_ComparatorSize_17Bits',0,16,16
	.byte	'IfxStm_ComparatorSize_18Bits',0,17,16
	.byte	'IfxStm_ComparatorSize_19Bits',0,18,16
	.byte	'IfxStm_ComparatorSize_20Bits',0,19,16
	.byte	'IfxStm_ComparatorSize_21Bits',0,20,16
	.byte	'IfxStm_ComparatorSize_22Bits',0,21,16
	.byte	'IfxStm_ComparatorSize_23Bits',0,22,16
	.byte	'IfxStm_ComparatorSize_24Bits',0,23,16
	.byte	'IfxStm_ComparatorSize_25Bits',0,24,16
	.byte	'IfxStm_ComparatorSize_26Bits',0,25,16
	.byte	'IfxStm_ComparatorSize_27Bits',0,26,16
	.byte	'IfxStm_ComparatorSize_28Bits',0,27,16
	.byte	'IfxStm_ComparatorSize_29Bits',0,28,16
	.byte	'IfxStm_ComparatorSize_30Bits',0,29,16
	.byte	'IfxStm_ComparatorSize_31Bits',0,30,16
	.byte	'IfxStm_ComparatorSize_32Bits',0,31,0,31
	.byte	'IfxStm_ComparatorSize',0,15,239,1,3
	.word	99123
	.byte	15,15,244,1,9,1,16
	.byte	'IfxStm_SleepMode_enable',0,0,16
	.byte	'IfxStm_SleepMode_disable',0,1,0,31
	.byte	'IfxStm_SleepMode',0,15,248,1,3
	.word	100143
	.byte	15,15,252,1,9,1,16
	.byte	'IfxStm_SuspendMode_none',0,0,16
	.byte	'IfxStm_SuspendMode_hard',0,1,16
	.byte	'IfxStm_SuspendMode_soft',0,2,0,31
	.byte	'IfxStm_SuspendMode',0,15,129,2,3
	.word	100229
	.byte	31
	.byte	'IfxStdIf_InterfaceDriver',0,35,118,15
	.word	380
	.byte	3
	.word	18030
	.byte	33
	.word	641
	.byte	1,1,34
	.word	380
	.byte	34
	.word	380
	.byte	34
	.word	100375
	.byte	34
	.word	22003
	.byte	0,3
	.word	100380
	.byte	31
	.byte	'IfxStdIf_DPipe_Write',0,36,92,19
	.word	100408
	.byte	31
	.byte	'IfxStdIf_DPipe_Read',0,36,107,19
	.word	100408
	.byte	33
	.word	18043
	.byte	1,1,34
	.word	380
	.byte	0,3
	.word	100470
	.byte	31
	.byte	'IfxStdIf_DPipe_GetReadCount',0,36,115,18
	.word	100483
	.byte	14
	.word	641
	.byte	3
	.word	100524
	.byte	33
	.word	100529
	.byte	1,1,34
	.word	380
	.byte	0,3
	.word	100534
	.byte	31
	.byte	'IfxStdIf_DPipe_GetReadEvent',0,36,123,36
	.word	100547
	.byte	31
	.byte	'IfxStdIf_DPipe_GetWriteCount',0,36,147,1,18
	.word	100483
	.byte	3
	.word	100534
	.byte	31
	.byte	'IfxStdIf_DPipe_GetWriteEvent',0,36,155,1,37
	.word	100626
	.byte	33
	.word	641
	.byte	1,1,34
	.word	380
	.byte	34
	.word	18030
	.byte	34
	.word	22003
	.byte	0,3
	.word	100669
	.byte	31
	.byte	'IfxStdIf_DPipe_CanReadCount',0,36,166,1,19
	.word	100692
	.byte	31
	.byte	'IfxStdIf_DPipe_CanWriteCount',0,36,177,1,19
	.word	100692
	.byte	33
	.word	641
	.byte	1,1,34
	.word	380
	.byte	34
	.word	22003
	.byte	0,3
	.word	100772
	.byte	31
	.byte	'IfxStdIf_DPipe_FlushTx',0,36,186,1,19
	.word	100790
	.byte	35,1,1,34
	.word	380
	.byte	0,3
	.word	100827
	.byte	31
	.byte	'IfxStdIf_DPipe_ClearTx',0,36,200,1,16
	.word	100836
	.byte	31
	.byte	'IfxStdIf_DPipe_ClearRx',0,36,193,1,16
	.word	100836
	.byte	31
	.byte	'IfxStdIf_DPipe_OnReceive',0,36,208,1,16
	.word	100836
	.byte	31
	.byte	'IfxStdIf_DPipe_OnTransmit',0,36,215,1,16
	.word	100836
	.byte	31
	.byte	'IfxStdIf_DPipe_OnError',0,36,222,1,16
	.word	100836
	.byte	33
	.word	1775
	.byte	1,1,34
	.word	380
	.byte	0,3
	.word	101006
	.byte	31
	.byte	'IfxStdIf_DPipe_GetSendCount',0,36,131,1,18
	.word	101019
	.byte	33
	.word	22003
	.byte	1,1,34
	.word	380
	.byte	0,3
	.word	101061
	.byte	31
	.byte	'IfxStdIf_DPipe_GetTxTimeStamp',0,36,139,1,24
	.word	101074
	.byte	31
	.byte	'IfxStdIf_DPipe_ResetSendCount',0,36,229,1,16
	.word	100836
	.byte	10
	.byte	'IfxStdIf_DPipe_',0,36,233,1,8,76,13
	.byte	'driver',0
	.word	100342
	.byte	4,2,35,0,13
	.byte	'txDisabled',0
	.word	641
	.byte	1,2,35,4,13
	.byte	'write',0
	.word	100413
	.byte	4,2,35,8,13
	.byte	'read',0
	.word	100442
	.byte	4,2,35,12,13
	.byte	'getReadCount',0
	.word	100488
	.byte	4,2,35,16,13
	.byte	'getReadEvent',0
	.word	100552
	.byte	4,2,35,20,13
	.byte	'getWriteCount',0
	.word	100588
	.byte	4,2,35,24,13
	.byte	'getWriteEvent',0
	.word	100631
	.byte	4,2,35,28,13
	.byte	'canReadCount',0
	.word	100697
	.byte	4,2,35,32,13
	.byte	'canWriteCount',0
	.word	100734
	.byte	4,2,35,36,13
	.byte	'flushTx',0
	.word	100795
	.byte	4,2,35,40,13
	.byte	'clearTx',0
	.word	100841
	.byte	4,2,35,44,13
	.byte	'clearRx',0
	.word	100873
	.byte	4,2,35,48,13
	.byte	'onReceive',0
	.word	100905
	.byte	4,2,35,52,13
	.byte	'onTransmit',0
	.word	100939
	.byte	4,2,35,56,13
	.byte	'onError',0
	.word	100974
	.byte	4,2,35,60,13
	.byte	'getSendCount',0
	.word	101024
	.byte	4,2,35,64,13
	.byte	'getTxTimeStamp',0
	.word	101079
	.byte	4,2,35,68,13
	.byte	'resetSendCount',0
	.word	101118
	.byte	4,2,35,72,0,31
	.byte	'IfxStdIf_DPipe',0,36,71,32
	.word	101157
	.byte	3
	.word	374
	.byte	3
	.word	100380
	.byte	3
	.word	100380
	.byte	3
	.word	100470
	.byte	3
	.word	100534
	.byte	3
	.word	100470
	.byte	3
	.word	100534
	.byte	3
	.word	100669
	.byte	3
	.word	100669
	.byte	3
	.word	100772
	.byte	3
	.word	100827
	.byte	3
	.word	100827
	.byte	3
	.word	100827
	.byte	3
	.word	100827
	.byte	3
	.word	100827
	.byte	3
	.word	101006
	.byte	3
	.word	101061
	.byte	3
	.word	100827
	.byte	14
	.word	641
	.byte	3
	.word	101670
	.byte	31
	.byte	'IfxStdIf_DPipe_WriteEvent',0,36,73,32
	.word	101675
	.byte	31
	.byte	'IfxStdIf_DPipe_ReadEvent',0,36,74,32
	.word	101675
	.byte	20,37,252,1,9,1,11
	.byte	'parityError',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'frameError',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'rxFifoOverflow',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'rxFifoUnderflow',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'txFifoOverflow',0,1
	.word	641
	.byte	1,3,2,35,0,0,31
	.byte	'IfxAsclin_Asc_ErrorFlags',0,37,131,2,3
	.word	101747
	.byte	20,37,137,2,9,8,13
	.byte	'baudrate',0
	.word	262
	.byte	4,2,35,0,13
	.byte	'prescaler',0
	.word	658
	.byte	2,2,35,4,13
	.byte	'oversampling',0
	.word	94213
	.byte	1,2,35,6,0,31
	.byte	'IfxAsclin_Asc_BaudRate',0,37,142,2,3
	.word	101912
	.byte	20,37,146,2,9,2,13
	.byte	'medianFilter',0
	.word	96403
	.byte	1,2,35,0,13
	.byte	'samplePointPosition',0
	.word	95841
	.byte	1,2,35,1,0,31
	.byte	'IfxAsclin_Asc_BitTimingControl',0,37,150,2,3
	.word	102010
	.byte	20,37,154,2,9,6,13
	.byte	'inWidth',0
	.word	97157
	.byte	1,2,35,0,13
	.byte	'outWidth',0
	.word	95636
	.byte	1,2,35,1,13
	.byte	'txFifoInterruptLevel',0
	.word	97324
	.byte	1,2,35,2,13
	.byte	'rxFifoInterruptLevel',0
	.word	95022
	.byte	1,2,35,3,13
	.byte	'buffMode',0
	.word	94786
	.byte	1,2,35,4,0,31
	.byte	'IfxAsclin_Asc_FifoControl',0,37,161,2,3
	.word	102108
	.byte	20,37,165,2,9,8,13
	.byte	'idleDelay',0
	.word	93522
	.byte	1,2,35,0,13
	.byte	'stopBit',0
	.word	96822
	.byte	1,2,35,1,13
	.byte	'frameMode',0
	.word	93233
	.byte	1,2,35,2,13
	.byte	'shiftDir',0
	.word	96505
	.byte	1,2,35,3,13
	.byte	'parityType',0
	.word	94694
	.byte	1,2,35,4,13
	.byte	'dataLength',0
	.word	92789
	.byte	1,2,35,5,13
	.byte	'parityBit',0
	.word	641
	.byte	1,2,35,6,0,31
	.byte	'IfxAsclin_Asc_FrameControl',0,37,174,2,3
	.word	102263
	.byte	20,37,178,2,9,8,13
	.byte	'txPriority',0
	.word	658
	.byte	2,2,35,0,13
	.byte	'rxPriority',0
	.word	658
	.byte	2,2,35,2,13
	.byte	'erPriority',0
	.word	658
	.byte	2,2,35,4,13
	.byte	'typeOfService',0
	.word	91612
	.byte	1,2,35,6,0,31
	.byte	'IfxAsclin_Asc_InterruptConfig',0,37,184,2,3
	.word	102438
	.byte	24
	.word	91690
	.byte	3
	.word	102567
	.byte	24
	.word	91771
	.byte	3
	.word	102577
	.byte	24
	.word	91851
	.byte	3
	.word	102587
	.byte	24
	.word	92099
	.byte	3
	.word	102597
	.byte	20,37,188,2,9,32,13
	.byte	'cts',0
	.word	102572
	.byte	4,2,35,0,13
	.byte	'ctsMode',0
	.word	9764
	.byte	1,2,35,4,13
	.byte	'rx',0
	.word	102582
	.byte	4,2,35,8,13
	.byte	'rxMode',0
	.word	9764
	.byte	1,2,35,12,13
	.byte	'rts',0
	.word	102592
	.byte	4,2,35,16,13
	.byte	'rtsMode',0
	.word	9969
	.byte	1,2,35,20,13
	.byte	'tx',0
	.word	102602
	.byte	4,2,35,24,13
	.byte	'txMode',0
	.word	9969
	.byte	1,2,35,28,13
	.byte	'pinDriver',0
	.word	90407
	.byte	1,2,35,29,0,31
	.byte	'IfxAsclin_Asc_Pins',0,37,199,2,3
	.word	102607
	.byte	12,37,205,2,9,1,13
	.byte	'ALL',0
	.word	641
	.byte	1,2,35,0,13
	.byte	'flags',0
	.word	101747
	.byte	1,2,35,0,0,31
	.byte	'IfxAsclin_Asc_ErrorFlagsUnion',0,37,209,2,3
	.word	102777
	.byte	31
	.byte	'uart_tx_pin_enum',0,23,74,2
	.word	24499
	.byte	31
	.byte	'uart_rx_pin_enum',0,23,100,2
	.word	24951
	.byte	31
	.byte	'uart_index_enum',0,23,109,2
	.word	24265
	.byte	31
	.byte	'gps_device_enum',0,24,67,2
	.word	25352
	.byte	31
	.byte	'gps_time_struct',0,24,77,2
	.word	25482
	.byte	31
	.byte	'gnss_info_struct',0,24,106,2
	.word	25589
.L328:
	.byte	15,24,108,9,1,16
	.byte	'GPS_STATE_RECEIVING',0,0,16
	.byte	'GPS_STATE_RECEIVED',0,1,16
	.byte	'GPS_STATE_PARSING',0,2,0,31
	.byte	'gps_state_enum',0,24,113,2
	.word	102998
.L327:
	.byte	18,128,1
	.word	641
	.byte	19,127,0,0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L111:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,21,0,54,15,39,12,0,0,3,15,0,73,19,0,0,4,46,1,3,8,32,13
	.byte	58,15,59,15,57,15,54,15,39,12,0,0,5,5,0,3,8,58,15,59,15,57,15,73,19,0,0,6,11,0,0,0,7,36,0,3,8,11,15,62
	.byte	15,0,0,8,46,1,3,8,32,13,58,15,59,15,57,15,73,19,54,15,39,12,0,0,9,59,0,3,8,0,0,10,19,1,3,8,58,15,59,15
	.byte	57,15,11,15,0,0,11,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,12,23,1,58,15,59,15,57,15,11,15,0,0,13,13
	.byte	0,3,8,73,19,11,15,56,9,0,0,14,53,0,73,19,0,0,15,4,1,58,15,59,15,57,15,11,15,0,0,16,40,0,3,8,28,13,0,0
	.byte	17,11,1,0,0,18,1,1,11,15,73,19,0,0,19,33,0,47,15,0,0,20,19,1,58,15,59,15,57,15,11,15,0,0,21,46,1,3,8,58
	.byte	15,59,15,57,15,73,19,54,15,39,12,63,12,60,12,0,0,22,5,0,58,15,59,15,57,15,73,19,0,0,23,55,0,73,19,0,0
	.byte	24,38,0,73,19,0,0,25,46,1,3,8,58,15,59,15,57,15,54,15,39,12,63,12,60,12,0,0,26,46,1,49,19,0,0,27,5,0,49
	.byte	19,0,0,28,29,1,49,19,0,0,29,11,0,49,19,0,0,30,11,1,49,19,0,0,31,22,0,3,8,58,15,59,15,57,15,73,19,0,0,32
	.byte	21,0,54,15,0,0,33,21,1,73,19,54,15,39,12,0,0,34,5,0,73,19,0,0,35,21,1,54,15,39,12,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L112:
	.word	.L520-.L519
.L519:
	.half	3
	.word	.L522-.L521
.L521:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg',0
	.byte	'F:\\ADS\\AURIX-Studio-1.10.2\\tools\\Compilers\\Tasking_1.1r8\\ctc\\include\\',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc',0
	.byte	0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_Intrinsics.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Scu\\Std\\IfxScuWdt.h',0
	.byte	0,0,0
	.byte	'IfxScu_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxCpu_cfg.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Port\\Std\\IfxPort.h',0
	.byte	0,0,0
	.byte	'IfxPort_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Scu\\Std\\IfxScuCcu.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Src\\Std\\IfxSrc.h',0
	.byte	0,0,0
	.byte	'IfxSrc_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Asclin\\Std\\IfxAsclin.h',0
	.byte	0,0,0
	.byte	'IfxAsclin_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Lib\\DataHandling\\Ifx_Fifo.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Stm\\Std\\IfxStm.h',0
	.byte	0,0,0
	.byte	'IfxStm_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\SysSe\\Bsp\\Bsp.h',0
	.byte	0,0,0
	.byte	'math.h',0,2,0,0
	.byte	'string.h',0,2,0,0
	.byte	'zf_common_function.h',0,3,0,0
	.byte	'zf_common_fifo.h',0,3,0,0
	.byte	'zf_driver_delay.h',0,4,0,0
	.byte	'zf_driver_uart.h',0,4,0,0
	.byte	'..\\libraries\\zf_device\\zf_device_gnss.h',0,0,0,0
	.byte	'../libraries/zf_device/zf_device_gnss.c',0,0,0,0
	.byte	'stdio.h',0,2,0,0
	.byte	'Platform_Types.h',0,5,0,0
	.byte	'ifx_types.h',0,5,0,0
	.byte	'zf_common_typedef.h',0,3,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxScu_cfg.h',0
	.byte	0,0,0
	.byte	'IfxCpu_regdef.h',0,1,0,0
	.byte	'IfxFlash_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxSrc_cfg.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_PinMap\\IfxAsclin_PinMap.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\StdIf\\IfxStdIf.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\StdIf\\IfxStdIf_DPipe.h',0
	.byte	0,0,0
	.byte	'ifxAsclin_Asc.h',0,6,0,0,0
.L522:
.L520:
	.sdecl	'.debug_info',debug,cluster('get_two_points_distance')
	.sect	'.debug_info'
.L113:
	.word	549
	.half	3
	.word	.L114
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_gnss.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L116,.L115
	.byte	2
	.word	.L109
	.byte	3
	.byte	'get_two_points_distance',0,1,225,2,8
	.word	.L200
	.byte	1,1,1
	.word	.L100,.L201,.L99
	.byte	4
	.byte	'latitude1',0,1,225,2,40
	.word	.L200,.L202
	.byte	4
	.byte	'longitude1',0,1,225,2,58
	.word	.L200,.L203
	.byte	4
	.byte	'latitude2',0,1,225,2,77
	.word	.L200,.L204
	.byte	4
	.byte	'longitude2',0,1,225,2,95
	.word	.L200,.L205
	.byte	5
	.word	.L100,.L201
	.byte	6
	.byte	'EARTH_RADIUS',0,1,227,2,18
	.word	.L206,.L207
	.byte	6
	.byte	'rad_latitude1',0,1,228,2,12
	.word	.L200,.L208
	.byte	6
	.byte	'rad_latitude2',0,1,229,2,12
	.word	.L200,.L209
	.byte	6
	.byte	'rad_longitude1',0,1,230,2,12
	.word	.L200,.L210
	.byte	6
	.byte	'rad_longitude2',0,1,231,2,12
	.word	.L200,.L211
	.byte	6
	.byte	'distance',0,1,232,2,12
	.word	.L200,.L212
	.byte	6
	.byte	'a',0,1,233,2,12
	.word	.L200,.L213
	.byte	6
	.byte	'b',0,1,234,2,12
	.word	.L200,.L214
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('get_two_points_distance')
	.sect	'.debug_abbrev'
.L114:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('get_two_points_distance')
	.sect	'.debug_line'
.L115:
	.word	.L524-.L523
.L523:
	.half	3
	.word	.L526-.L525
.L525:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_gnss.c',0,0,0,0,0
.L526:
	.byte	5,8,7,0,5,2
	.word	.L100
	.byte	3,224,2,1,5,33,9
	.half	.L445-.L100
	.byte	3,2,1,5,21,9
	.half	.L448-.L445
	.byte	3,9,1,5,19,9
	.half	.L527-.L448
	.byte	1,5,21,9
	.half	.L528-.L527
	.byte	3,1,1,5,19,9
	.half	.L449-.L528
	.byte	1,5,22,9
	.half	.L450-.L449
	.byte	3,1,1,5,20,9
	.half	.L529-.L450
	.byte	1,5,22,9
	.half	.L444-.L529
	.byte	3,1,1,5,20,9
	.half	.L530-.L444
	.byte	1,5,23,9
	.half	.L453-.L530
	.byte	3,2,1,5,7,9
	.half	.L456-.L453
	.byte	1,5,24,9
	.half	.L446-.L456
	.byte	3,1,1,5,7,9
	.half	.L531-.L446
	.byte	1,5,42,9
	.half	.L532-.L531
	.byte	3,2,1,5,40,9
	.half	.L459-.L532
	.byte	1,5,37,9
	.half	.L533-.L459
	.byte	1,5,55,9
	.half	.L461-.L533
	.byte	1,5,70,9
	.half	.L534-.L461
	.byte	1,5,105,9
	.half	.L535-.L534
	.byte	1,5,103,9
	.half	.L463-.L535
	.byte	1,5,100,9
	.half	.L536-.L463
	.byte	1,5,76,9
	.half	.L537-.L536
	.byte	1,5,70,9
	.half	.L466-.L537
	.byte	1,5,91,9
	.half	.L538-.L466
	.byte	1,5,109,9
	.half	.L539-.L538
	.byte	1,5,91,9
	.half	.L540-.L539
	.byte	1,5,49,9
	.half	.L541-.L540
	.byte	1,5,46,9
	.half	.L542-.L541
	.byte	1,5,49,9
	.half	.L543-.L542
	.byte	1,5,29,9
	.half	.L544-.L543
	.byte	1,5,16,9
	.half	.L545-.L544
	.byte	1,5,18,9
	.half	.L546-.L545
	.byte	1,5,25,9
	.half	.L467-.L546
	.byte	3,1,1,5,5,9
	.half	.L470-.L467
	.byte	3,2,1,5,1,9
	.half	.L33-.L470
	.byte	3,1,1,7,9
	.half	.L117-.L33
	.byte	0,1,1
.L524:
	.sdecl	'.debug_ranges',debug,cluster('get_two_points_distance')
	.sect	'.debug_ranges'
.L116:
	.word	-1,.L100,0,.L117-.L100,0,0
	.sdecl	'.debug_info',debug,cluster('get_two_points_azimuth')
	.sect	'.debug_info'
.L118:
	.word	419
	.half	3
	.word	.L119
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_gnss.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L121,.L120
	.byte	2
	.word	.L109
	.byte	3
	.byte	'get_two_points_azimuth',0,1,132,3,8
	.word	.L200
	.byte	1,1,1
	.word	.L102,.L215,.L101
	.byte	4
	.byte	'latitude1',0,1,132,3,39
	.word	.L200,.L216
	.byte	4
	.byte	'longitude1',0,1,132,3,57
	.word	.L200,.L217
	.byte	4
	.byte	'latitude2',0,1,132,3,76
	.word	.L200,.L218
	.byte	4
	.byte	'longitude2',0,1,132,3,94
	.word	.L200,.L219
	.byte	5
	.word	.L102,.L215
	.byte	5
	.word	.L220,.L215
	.byte	6
	.byte	'x',0,1,139,3,12
	.word	.L200,.L221
	.byte	6
	.byte	'y',0,1,140,3,12
	.word	.L200,.L222
	.byte	6
	.byte	'angle',0,1,141,3,12
	.word	.L200,.L223
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('get_two_points_azimuth')
	.sect	'.debug_abbrev'
.L119:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('get_two_points_azimuth')
	.sect	'.debug_line'
.L120:
	.word	.L548-.L547
.L547:
	.half	3
	.word	.L550-.L549
.L549:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_gnss.c',0,0,0,0,0
.L550:
	.byte	5,8,7,0,5,2
	.word	.L102
	.byte	3,131,3,1,5,17,9
	.half	.L476-.L102
	.byte	3,2,1,5,15,9
	.half	.L551-.L476
	.byte	1,5,17,9
	.half	.L552-.L551
	.byte	3,1,1,5,15,9
	.half	.L478-.L552
	.byte	1,5,18,9
	.half	.L475-.L478
	.byte	3,1,1,5,16,9
	.half	.L553-.L475
	.byte	1,5,18,9
	.half	.L554-.L553
	.byte	3,1,1,5,16,9
	.half	.L555-.L554
	.byte	1,5,31,9
	.half	.L220-.L555
	.byte	3,2,1,5,45,9
	.half	.L556-.L220
	.byte	1,5,51,9
	.half	.L557-.L556
	.byte	1,5,45,9
	.half	.L483-.L557
	.byte	1,5,14,9
	.half	.L484-.L483
	.byte	1,5,20,9
	.half	.L485-.L484
	.byte	3,1,1,5,31,9
	.half	.L558-.L485
	.byte	1,5,54,9
	.half	.L487-.L558
	.byte	1,5,65,9
	.half	.L559-.L487
	.byte	1,5,71,9
	.half	.L560-.L559
	.byte	1,5,65,9
	.half	.L490-.L560
	.byte	1,5,82,9
	.half	.L561-.L490
	.byte	1,5,99,9
	.half	.L491-.L561
	.byte	1,5,82,9
	.half	.L562-.L491
	.byte	1,5,48,9
	.half	.L563-.L562
	.byte	1,5,37,9
	.half	.L564-.L563
	.byte	1,5,31,9
	.half	.L495-.L564
	.byte	1,5,48,9
	.half	.L565-.L495
	.byte	1,5,20,9
	.half	.L566-.L565
	.byte	3,1,1,5,18,9
	.half	.L567-.L566
	.byte	1,5,14,9
	.half	.L568-.L567
	.byte	3,1,1,5,16,9
	.half	.L499-.L568
	.byte	1,5,13,9
	.half	.L569-.L499
	.byte	1,5,44,7,9
	.half	.L570-.L569
	.byte	1,5,42,9
	.half	.L501-.L570
	.byte	1,5,33,9
	.half	.L571-.L501
	.byte	1,5,25,9
	.half	.L34-.L571
	.byte	1,5,5,9
	.half	.L35-.L34
	.byte	1,5,1,9
	.half	.L36-.L35
	.byte	3,1,1,7,9
	.half	.L122-.L36
	.byte	0,1,1
.L548:
	.sdecl	'.debug_ranges',debug,cluster('get_two_points_azimuth')
	.sect	'.debug_ranges'
.L121:
	.word	-1,.L102,0,.L122-.L102,0,0
	.sdecl	'.debug_info',debug,cluster('gnss_data_parse')
	.sect	'.debug_info'
.L123:
	.word	394
	.half	3
	.word	.L124
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_gnss.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L126,.L125
	.byte	2
	.word	.L109
	.byte	3
	.byte	'gnss_data_parse',0,1,152,3,7
	.word	.L224
	.byte	1,1,1
	.word	.L104,.L225,.L103
	.byte	4
	.word	.L104,.L225
	.byte	5
	.byte	'return_state',0,1,154,3,11
	.word	.L224,.L226
	.byte	5
	.byte	'check_buffer',0,1,155,3,11
	.word	.L227,.L228
	.byte	5
	.byte	'bbc_xor_origin',0,1,156,3,11
	.word	.L224,.L229
	.byte	5
	.byte	'bbc_xor_calculation',0,1,157,3,11
	.word	.L224,.L230
	.byte	5
	.byte	'data_len',0,1,158,3,12
	.word	.L231,.L232
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('gnss_data_parse')
	.sect	'.debug_abbrev'
.L124:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('gnss_data_parse')
	.sect	'.debug_line'
.L125:
	.word	.L573-.L572
.L572:
	.half	3
	.word	.L575-.L574
.L574:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_gnss.c',0,0,0,0,0
.L575:
	.byte	5,7,7,0,5,2
	.word	.L104
	.byte	3,151,3,1,5,24,9
	.half	.L503-.L104
	.byte	3,2,1,5,29,9
	.half	.L504-.L503
	.byte	3,1,1,5,27,9
	.half	.L576-.L504
	.byte	1,5,34,9
	.half	.L38-.L576
	.byte	3,7,1,5,9,9
	.half	.L577-.L38
	.byte	1,5,13,7,9
	.half	.L578-.L577
	.byte	3,2,1,5,30,9
	.half	.L579-.L578
	.byte	1,5,28,9
	.half	.L580-.L579
	.byte	1,5,68,9
	.half	.L581-.L580
	.byte	3,1,1,5,84,9
	.half	.L582-.L581
	.byte	1,5,89,9
	.half	.L583-.L582
	.byte	1,5,42,9
	.half	.L584-.L583
	.byte	1,5,94,9
	.half	.L585-.L584
	.byte	1,5,61,9
	.half	.L586-.L585
	.byte	3,1,1,5,53,9
	.half	.L587-.L586
	.byte	1,5,30,9
	.half	.L588-.L587
	.byte	1,5,39,9
	.half	.L505-.L588
	.byte	3,1,1,5,53,9
	.half	.L589-.L505
	.byte	1,5,67,9
	.half	.L506-.L589
	.byte	1,5,103,9
	.half	.L507-.L506
	.byte	1,5,40,9
	.half	.L41-.L507
	.byte	3,2,1,5,54,9
	.half	.L590-.L41
	.byte	1,5,37,9
	.half	.L591-.L590
	.byte	1,5,114,9
	.half	.L592-.L591
	.byte	3,126,1,5,79,9
	.half	.L40-.L592
	.byte	1,5,93,9
	.half	.L593-.L40
	.byte	1,5,72,9
	.half	.L594-.L593
	.byte	1,5,103,9
	.half	.L595-.L594
	.byte	1,5,13,7,9
	.half	.L596-.L595
	.byte	3,4,1,5,30,7,9
	.half	.L597-.L596
	.byte	3,3,1,5,17,9
	.half	.L598-.L597
	.byte	3,1,1,5,37,9
	.half	.L42-.L598
	.byte	3,3,1,5,54,9
	.half	.L599-.L42
	.byte	1,5,9,9
	.half	.L39-.L599
	.byte	3,2,1,5,26,9
	.half	.L600-.L39
	.byte	1,5,24,9
	.half	.L601-.L600
	.byte	1,5,34,9
	.half	.L602-.L601
	.byte	3,2,1,5,9,9
	.half	.L603-.L602
	.byte	1,5,13,7,9
	.half	.L604-.L603
	.byte	3,2,1,5,30,9
	.half	.L605-.L604
	.byte	1,5,28,9
	.half	.L606-.L605
	.byte	1,5,68,9
	.half	.L607-.L606
	.byte	3,1,1,5,84,9
	.half	.L608-.L607
	.byte	1,5,89,9
	.half	.L609-.L608
	.byte	1,5,42,9
	.half	.L610-.L609
	.byte	1,5,94,9
	.half	.L611-.L610
	.byte	1,5,61,9
	.half	.L612-.L611
	.byte	3,1,1,5,53,9
	.half	.L613-.L612
	.byte	1,5,30,9
	.half	.L614-.L613
	.byte	1,5,39,9
	.half	.L508-.L614
	.byte	3,2,1,5,53,9
	.half	.L615-.L508
	.byte	1,5,67,9
	.half	.L509-.L615
	.byte	1,5,103,9
	.half	.L510-.L509
	.byte	1,5,40,9
	.half	.L46-.L510
	.byte	3,2,1,5,54,9
	.half	.L616-.L46
	.byte	1,5,37,9
	.half	.L617-.L616
	.byte	1,5,114,9
	.half	.L618-.L617
	.byte	3,126,1,5,79,9
	.half	.L45-.L618
	.byte	1,5,93,9
	.half	.L619-.L45
	.byte	1,5,72,9
	.half	.L620-.L619
	.byte	1,5,103,9
	.half	.L621-.L620
	.byte	1,5,13,7,9
	.half	.L622-.L621
	.byte	3,4,1,5,30,7,9
	.half	.L623-.L622
	.byte	3,3,1,5,17,9
	.half	.L624-.L623
	.byte	3,1,1,5,37,9
	.half	.L47-.L624
	.byte	3,3,1,5,54,9
	.half	.L625-.L47
	.byte	1,5,9,9
	.half	.L44-.L625
	.byte	3,2,1,5,26,9
	.half	.L626-.L44
	.byte	1,5,24,9
	.half	.L627-.L626
	.byte	1,5,34,9
	.half	.L628-.L627
	.byte	3,2,1,5,9,9
	.half	.L629-.L628
	.byte	1,5,13,7,9
	.half	.L630-.L629
	.byte	3,2,1,5,30,9
	.half	.L631-.L630
	.byte	1,5,28,9
	.half	.L632-.L631
	.byte	1,5,68,9
	.half	.L633-.L632
	.byte	3,1,1,5,84,9
	.half	.L634-.L633
	.byte	1,5,89,9
	.half	.L635-.L634
	.byte	1,5,42,9
	.half	.L636-.L635
	.byte	1,5,94,9
	.half	.L637-.L636
	.byte	1,5,61,9
	.half	.L638-.L637
	.byte	3,1,1,5,53,9
	.half	.L639-.L638
	.byte	1,5,30,9
	.half	.L640-.L639
	.byte	1,5,39,9
	.half	.L511-.L640
	.byte	3,2,1,5,53,9
	.half	.L641-.L511
	.byte	1,5,67,9
	.half	.L512-.L641
	.byte	1,5,103,9
	.half	.L513-.L512
	.byte	1,5,40,9
	.half	.L51-.L513
	.byte	3,2,1,5,54,9
	.half	.L642-.L51
	.byte	1,5,37,9
	.half	.L643-.L642
	.byte	1,5,114,9
	.half	.L644-.L643
	.byte	3,126,1,5,79,9
	.half	.L50-.L644
	.byte	1,5,93,9
	.half	.L645-.L50
	.byte	1,5,72,9
	.half	.L646-.L645
	.byte	1,5,103,9
	.half	.L647-.L646
	.byte	1,5,13,7,9
	.half	.L648-.L647
	.byte	3,4,1,5,30,7,9
	.half	.L649-.L648
	.byte	3,3,1,5,17,9
	.half	.L650-.L649
	.byte	3,1,1,5,37,9
	.half	.L52-.L650
	.byte	3,3,1,5,54,9
	.half	.L651-.L52
	.byte	1,5,9,9
	.half	.L49-.L651
	.byte	3,2,1,5,26,9
	.half	.L652-.L49
	.byte	1,5,24,9
	.half	.L653-.L652
	.byte	1,5,5,9
	.half	.L43-.L653
	.byte	3,3,1,5,1,9
	.half	.L54-.L43
	.byte	3,1,1,7,9
	.half	.L127-.L54
	.byte	0,1,1
.L573:
	.sdecl	'.debug_ranges',debug,cluster('gnss_data_parse')
	.sect	'.debug_ranges'
.L126:
	.word	-1,.L104,0,.L127-.L104,0,0
	.sdecl	'.debug_info',debug,cluster('gnss_uart_callback')
	.sect	'.debug_info'
.L128:
	.word	332
	.half	3
	.word	.L129
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_gnss.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L131,.L130
	.byte	2
	.word	.L109
	.byte	3
	.byte	'gnss_uart_callback',0,1,236,3,6,1,1,1
	.word	.L106,.L233,.L105
	.byte	4
	.word	.L106,.L233
	.byte	5
	.byte	'temp_gps',0,1,238,3,11
	.word	.L234,.L235
	.byte	5
	.byte	'temp_length',0,1,239,3,12
	.word	.L231,.L236
	.byte	4
	.word	.L237,.L55
	.byte	5
	.byte	'dat',0,1,246,3,15
	.word	.L224,.L238
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('gnss_uart_callback')
	.sect	'.debug_abbrev'
.L129:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('gnss_uart_callback')
	.sect	'.debug_line'
.L130:
	.word	.L655-.L654
.L654:
	.half	3
	.word	.L657-.L656
.L656:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_gnss.c',0,0,0,0,0
.L657:
	.byte	5,6,7,0,5,2
	.word	.L106
	.byte	3,235,3,1,5,26,9
	.half	.L515-.L106
	.byte	3,3,1,5,24,9
	.half	.L658-.L515
	.byte	1,5,8,9
	.half	.L659-.L658
	.byte	3,5,1,5,5,9
	.half	.L660-.L659
	.byte	1,5,47,7,9
	.half	.L237-.L660
	.byte	3,3,1,5,32,9
	.half	.L57-.L237
	.byte	3,2,1,5,53,9
	.half	.L661-.L57
	.byte	1,5,58,9
	.half	.L662-.L661
	.byte	1,5,31,9
	.half	.L56-.L662
	.byte	3,126,1,5,43,9
	.half	.L663-.L56
	.byte	1,5,47,9
	.half	.L664-.L663
	.byte	1,5,20,7,9
	.half	.L665-.L664
	.byte	3,5,1,5,12,9
	.half	.L666-.L665
	.byte	1,5,9,9
	.half	.L667-.L666
	.byte	1,5,27,7,9
	.half	.L668-.L667
	.byte	3,3,1,5,25,9
	.half	.L669-.L668
	.byte	1,5,31,9
	.half	.L670-.L669
	.byte	3,1,1,5,51,9
	.half	.L671-.L670
	.byte	1,5,62,9
	.half	.L672-.L671
	.byte	1,5,75,9
	.half	.L673-.L672
	.byte	1,5,46,9
	.half	.L674-.L673
	.byte	3,3,1,5,51,9
	.half	.L675-.L674
	.byte	1,5,58,9
	.half	.L676-.L675
	.byte	1,5,13,9
	.half	.L677-.L676
	.byte	1,5,41,7,9
	.half	.L678-.L677
	.byte	3,3,1,5,17,9
	.half	.L679-.L678
	.byte	1,5,21,7,9
	.half	.L680-.L679
	.byte	3,2,1,5,38,9
	.half	.L681-.L680
	.byte	1,5,36,9
	.half	.L682-.L681
	.byte	1,5,46,9
	.half	.L683-.L682
	.byte	3,1,1,5,33,9
	.half	.L684-.L683
	.byte	1,5,39,9
	.half	.L685-.L684
	.byte	3,1,1,5,59,9
	.half	.L686-.L685
	.byte	1,5,76,9
	.half	.L687-.L686
	.byte	1,5,89,9
	.half	.L688-.L687
	.byte	1,5,17,9
	.half	.L60-.L688
	.byte	3,124,1,5,51,9
	.half	.L59-.L60
	.byte	3,7,1,5,56,9
	.half	.L689-.L59
	.byte	1,5,63,9
	.half	.L690-.L689
	.byte	1,5,18,9
	.half	.L691-.L690
	.byte	1,5,41,7,9
	.half	.L692-.L691
	.byte	3,3,1,5,17,9
	.half	.L693-.L692
	.byte	1,5,21,7,9
	.half	.L694-.L693
	.byte	3,2,1,5,38,9
	.half	.L695-.L694
	.byte	1,5,36,9
	.half	.L696-.L695
	.byte	1,5,46,9
	.half	.L697-.L696
	.byte	3,1,1,5,33,9
	.half	.L698-.L697
	.byte	1,5,39,9
	.half	.L699-.L698
	.byte	3,1,1,5,59,9
	.half	.L700-.L699
	.byte	1,5,76,9
	.half	.L701-.L700
	.byte	1,5,89,9
	.half	.L702-.L701
	.byte	1,5,17,9
	.half	.L63-.L702
	.byte	3,124,1,5,51,9
	.half	.L62-.L63
	.byte	3,7,1,5,56,9
	.half	.L703-.L62
	.byte	1,5,63,9
	.half	.L704-.L703
	.byte	1,5,18,9
	.half	.L705-.L704
	.byte	1,5,41,7,9
	.half	.L706-.L705
	.byte	3,3,1,5,17,9
	.half	.L707-.L706
	.byte	1,5,21,7,9
	.half	.L708-.L707
	.byte	3,2,1,5,38,9
	.half	.L709-.L708
	.byte	1,5,36,9
	.half	.L710-.L709
	.byte	1,5,46,9
	.half	.L711-.L710
	.byte	3,1,1,5,33,9
	.half	.L712-.L711
	.byte	1,5,39,9
	.half	.L713-.L712
	.byte	3,1,1,5,59,9
	.half	.L714-.L713
	.byte	1,5,76,9
	.half	.L715-.L714
	.byte	1,5,89,9
	.half	.L716-.L715
	.byte	1,5,25,9
	.half	.L61-.L716
	.byte	3,5,1,5,13,9
	.half	.L717-.L61
	.byte	3,2,1,5,25,9
	.half	.L718-.L717
	.byte	1,5,23,9
	.half	.L719-.L718
	.byte	1,5,1,9
	.half	.L55-.L719
	.byte	3,3,1,7,9
	.half	.L132-.L55
	.byte	0,1,1
.L655:
	.sdecl	'.debug_ranges',debug,cluster('gnss_uart_callback')
	.sect	'.debug_ranges'
.L131:
	.word	-1,.L106,0,.L132-.L106,0,0
	.sdecl	'.debug_info',debug,cluster('gnss_init')
	.sect	'.debug_info'
.L133:
	.word	550
	.half	3
	.word	.L134
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_gnss.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L136,.L135
	.byte	2
	.word	.L109
	.byte	3
	.byte	'gnss_init',0,1,177,4,6,1,1,1
	.word	.L108,.L239,.L107
	.byte	4
	.byte	'gps_device',0,1,177,4,33
	.word	.L240,.L241
	.byte	5
	.word	.L108,.L239
	.byte	6
	.byte	'set_rate',0,1,179,4,17
	.word	.L242,.L243
	.byte	6
	.byte	'open_gga',0,1,180,4,17
	.word	.L244,.L245
	.byte	6
	.byte	'open_rmc',0,1,181,4,17
	.word	.L246,.L247
	.byte	6
	.byte	'close_gll',0,1,183,4,17
	.word	.L248,.L249
	.byte	6
	.byte	'close_gsa',0,1,184,4,17
	.word	.L250,.L251
	.byte	6
	.byte	'close_grs',0,1,185,4,17
	.word	.L252,.L253
	.byte	6
	.byte	'close_gsv',0,1,186,4,17
	.word	.L254,.L255
	.byte	6
	.byte	'close_vtg',0,1,187,4,17
	.word	.L256,.L257
	.byte	6
	.byte	'close_zda',0,1,188,4,17
	.word	.L258,.L259
	.byte	6
	.byte	'close_gst',0,1,189,4,17
	.word	.L260,.L261
	.byte	6
	.byte	'close_txt',0,1,190,4,17
	.word	.L262,.L263
	.byte	6
	.byte	'close_txt_ant',0,1,191,4,17
	.word	.L264,.L265
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('gnss_init')
	.sect	'.debug_abbrev'
.L134:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('gnss_init')
	.sect	'.debug_line'
.L135:
	.word	.L721-.L720
.L720:
	.half	3
	.word	.L723-.L722
.L722:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_gnss.c',0,0,0,0,0
.L723:
	.byte	5,6,7,0,5,2
	.word	.L108
	.byte	3,176,4,1,5,35,9
	.half	.L516-.L108
	.byte	3,2,1,5,33,9
	.half	.L724-.L516
	.byte	1,5,35,9
	.half	.L725-.L724
	.byte	3,1,1,5,33,9
	.half	.L726-.L725
	.byte	1,5,35,9
	.half	.L727-.L726
	.byte	3,1,1,5,33,9
	.half	.L728-.L727
	.byte	1,5,35,9
	.half	.L729-.L728
	.byte	3,2,1,5,33,9
	.half	.L730-.L729
	.byte	1,5,35,9
	.half	.L731-.L730
	.byte	3,1,1,5,33,9
	.half	.L732-.L731
	.byte	1,5,35,9
	.half	.L733-.L732
	.byte	3,1,1,5,33,9
	.half	.L734-.L733
	.byte	1,5,35,9
	.half	.L735-.L734
	.byte	3,1,1,5,33,9
	.half	.L736-.L735
	.byte	1,5,35,9
	.half	.L737-.L736
	.byte	3,1,1,5,33,9
	.half	.L738-.L737
	.byte	1,5,35,9
	.half	.L739-.L738
	.byte	3,1,1,5,33,9
	.half	.L740-.L739
	.byte	1,5,35,9
	.half	.L741-.L740
	.byte	3,1,1,5,33,9
	.half	.L742-.L741
	.byte	1,5,35,9
	.half	.L743-.L742
	.byte	3,1,1,5,33,9
	.half	.L744-.L743
	.byte	1,5,35,9
	.half	.L745-.L744
	.byte	3,1,1,5,33,9
	.half	.L746-.L745
	.byte	1,5,8,9
	.half	.L747-.L746
	.byte	3,2,1,5,42,7,9
	.half	.L748-.L747
	.byte	1,5,20,7,9
	.half	.L79-.L748
	.byte	3,2,1,5,40,9
	.half	.L749-.L79
	.byte	1,5,56,9
	.half	.L517-.L749
	.byte	1,5,78,9
	.half	.L750-.L517
	.byte	1,5,25,9
	.half	.L751-.L750
	.byte	3,1,1,5,19,9
	.half	.L752-.L751
	.byte	3,1,1,5,30,9
	.half	.L753-.L752
	.byte	1,5,38,9
	.half	.L754-.L753
	.byte	1,5,47,9
	.half	.L755-.L754
	.byte	1,5,27,9
	.half	.L756-.L755
	.byte	3,2,1,5,47,9
	.half	.L757-.L756
	.byte	1,5,57,9
	.half	.L758-.L757
	.byte	1,5,25,9
	.half	.L759-.L758
	.byte	3,1,1,5,27,9
	.half	.L760-.L759
	.byte	3,2,1,5,47,9
	.half	.L761-.L760
	.byte	1,5,57,9
	.half	.L762-.L761
	.byte	1,5,25,9
	.half	.L763-.L762
	.byte	3,1,1,5,27,9
	.half	.L764-.L763
	.byte	3,1,1,5,47,9
	.half	.L765-.L764
	.byte	1,5,57,9
	.half	.L766-.L765
	.byte	1,5,25,9
	.half	.L767-.L766
	.byte	3,1,1,5,27,9
	.half	.L768-.L767
	.byte	3,1,1,5,47,9
	.half	.L769-.L768
	.byte	1,5,58,9
	.half	.L770-.L769
	.byte	1,5,25,9
	.half	.L771-.L770
	.byte	3,1,1,5,27,9
	.half	.L772-.L771
	.byte	3,1,1,5,47,9
	.half	.L773-.L772
	.byte	1,5,58,9
	.half	.L774-.L773
	.byte	1,5,25,9
	.half	.L775-.L774
	.byte	3,1,1,5,27,9
	.half	.L776-.L775
	.byte	3,1,1,5,47,9
	.half	.L777-.L776
	.byte	1,5,58,9
	.half	.L778-.L777
	.byte	1,5,25,9
	.half	.L779-.L778
	.byte	3,1,1,5,27,9
	.half	.L780-.L779
	.byte	3,1,1,5,47,9
	.half	.L781-.L780
	.byte	1,5,58,9
	.half	.L782-.L781
	.byte	1,5,25,9
	.half	.L783-.L782
	.byte	3,1,1,5,27,9
	.half	.L784-.L783
	.byte	3,1,1,5,47,9
	.half	.L785-.L784
	.byte	1,5,58,9
	.half	.L786-.L785
	.byte	1,5,25,9
	.half	.L787-.L786
	.byte	3,1,1,5,27,9
	.half	.L788-.L787
	.byte	3,1,1,5,47,9
	.half	.L789-.L788
	.byte	1,5,58,9
	.half	.L790-.L789
	.byte	1,5,25,9
	.half	.L791-.L790
	.byte	3,1,1,5,27,9
	.half	.L792-.L791
	.byte	3,1,1,5,47,9
	.half	.L793-.L792
	.byte	1,5,58,9
	.half	.L794-.L793
	.byte	1,5,25,9
	.half	.L795-.L794
	.byte	3,1,1,5,27,9
	.half	.L796-.L795
	.byte	3,1,1,5,47,9
	.half	.L797-.L796
	.byte	1,5,58,9
	.half	.L798-.L797
	.byte	1,5,25,9
	.half	.L799-.L798
	.byte	3,1,1,5,27,9
	.half	.L800-.L799
	.byte	3,1,1,5,47,9
	.half	.L801-.L800
	.byte	1,5,62,9
	.half	.L802-.L801
	.byte	1,5,25,9
	.half	.L803-.L802
	.byte	3,1,1,5,9,9
	.half	.L804-.L803
	.byte	3,2,1,5,22,9
	.half	.L805-.L804
	.byte	1,5,20,9
	.half	.L806-.L805
	.byte	1,5,27,9
	.half	.L807-.L806
	.byte	3,1,1,5,38,9
	.half	.L808-.L807
	.byte	1,5,95,9
	.half	.L809-.L808
	.byte	3,97,1,5,10,9
	.half	.L80-.L809
	.byte	3,33,1,5,20,7,9
	.half	.L810-.L80
	.byte	3,3,1,5,40,9
	.half	.L811-.L810
	.byte	1,5,56,9
	.half	.L518-.L811
	.byte	1,5,78,9
	.half	.L812-.L518
	.byte	1,5,19,9
	.half	.L813-.L812
	.byte	3,1,1,5,30,9
	.half	.L814-.L813
	.byte	1,5,38,9
	.half	.L815-.L814
	.byte	1,5,47,9
	.half	.L816-.L815
	.byte	1,5,9,9
	.half	.L817-.L816
	.byte	3,1,1,5,22,9
	.half	.L818-.L817
	.byte	1,5,20,9
	.half	.L819-.L818
	.byte	1,5,27,9
	.half	.L820-.L819
	.byte	3,1,1,5,38,9
	.half	.L821-.L820
	.byte	1,5,1,9
	.half	.L81-.L821
	.byte	3,3,1,7,9
	.half	.L137-.L81
	.byte	0,1,1
.L721:
	.sdecl	'.debug_ranges',debug,cluster('gnss_init')
	.sect	'.debug_ranges'
.L136:
	.word	-1,.L108,0,.L137-.L108,0,0
	.sdecl	'.debug_info',debug,cluster('get_parameter_index')
	.sect	'.debug_info'
.L138:
	.word	371
	.half	3
	.word	.L139
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_gnss.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L141,.L140
	.byte	2
	.word	.L109
	.byte	3
	.byte	'get_parameter_index',0,1,80,14
	.word	.L224
	.byte	1,1
	.word	.L84,.L266,.L83
	.byte	4
	.byte	'num',0,1,80,41
	.word	.L224,.L267
	.byte	4
	.byte	'str',0,1,80,52
	.word	.L268,.L269
	.byte	5
	.word	.L84,.L266
	.byte	6
	.byte	'i',0,1,82,11
	.word	.L224,.L270
	.byte	6
	.byte	'j',0,1,82,18
	.word	.L224,.L271
	.byte	6
	.byte	'temp',0,1,83,11
	.word	.L268,.L272
	.byte	6
	.byte	'len',0,1,84,11
	.word	.L224,.L273
	.byte	6
	.byte	'len1',0,1,84,20
	.word	.L224,.L274
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('get_parameter_index')
	.sect	'.debug_abbrev'
.L139:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('get_parameter_index')
	.sect	'.debug_line'
.L140:
	.word	.L823-.L822
.L822:
	.half	3
	.word	.L825-.L824
.L824:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_gnss.c',0,0,0,0,0
.L825:
	.byte	5,14,7,0,5,2
	.word	.L84
	.byte	3,207,0,1,5,20,9
	.half	.L332-.L84
	.byte	3,2,1,5,30,9
	.half	.L333-.L332
	.byte	3,1,1,5,15,9
	.half	.L330-.L333
	.byte	3,1,1,5,25,9
	.half	.L335-.L330
	.byte	1,5,8,9
	.half	.L337-.L335
	.byte	3,2,1,5,5,9
	.half	.L826-.L337
	.byte	1,5,31,7,9
	.half	.L827-.L826
	.byte	3,2,1,5,46,9
	.half	.L336-.L827
	.byte	1,5,36,9
	.half	.L339-.L336
	.byte	1,5,50,9
	.half	.L338-.L339
	.byte	1,5,15,9
	.half	.L828-.L338
	.byte	1,5,11,9
	.half	.L2-.L828
	.byte	3,3,1,5,23,9
	.half	.L340-.L2
	.byte	1,5,22,9
	.half	.L4-.L340
	.byte	3,2,1,5,12,9
	.half	.L829-.L4
	.byte	1,5,9,9
	.half	.L830-.L829
	.byte	1,5,15,7,9
	.half	.L831-.L830
	.byte	3,2,1,5,9,9
	.half	.L5-.L831
	.byte	3,2,1,5,23,7,9
	.half	.L832-.L5
	.byte	3,2,1,5,13,9
	.half	.L833-.L832
	.byte	3,1,1,5,27,9
	.half	.L6-.L833
	.byte	3,119,1,5,23,9
	.half	.L3-.L6
	.byte	1,5,5,7,9
	.half	.L7-.L3
	.byte	3,13,1,5,1,9
	.half	.L8-.L7
	.byte	3,1,1,7,9
	.half	.L142-.L8
	.byte	0,1,1
.L823:
	.sdecl	'.debug_ranges',debug,cluster('get_parameter_index')
	.sect	'.debug_ranges'
.L141:
	.word	-1,.L84,0,.L142-.L84,0,0
	.sdecl	'.debug_info',debug,cluster('get_int_number')
	.sect	'.debug_info'
.L143:
	.word	325
	.half	3
	.word	.L144
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_gnss.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L146,.L145
	.byte	2
	.word	.L109
	.byte	3
	.byte	'get_int_number',0,1,114,12
	.word	.L275
	.byte	1,1
	.word	.L86,.L276,.L85
	.byte	4
	.byte	's',0,1,114,34
	.word	.L268,.L277
	.byte	5
	.word	.L86,.L276
	.byte	6
	.byte	'buf',0,1,116,10
	.word	.L278,.L279
	.byte	6
	.byte	'i',0,1,117,11
	.word	.L224,.L280
	.byte	6
	.byte	'return_value',0,1,118,9
	.word	.L275,.L281
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('get_int_number')
	.sect	'.debug_abbrev'
.L144:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('get_int_number')
	.sect	'.debug_line'
.L145:
	.word	.L835-.L834
.L834:
	.half	3
	.word	.L837-.L836
.L836:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_gnss.c',0,0,0,0,0
.L837:
	.byte	5,12,7,0,5,2
	.word	.L86
	.byte	3,241,0,1,5,29,9
	.half	.L345-.L86
	.byte	3,5,1,5,32,9
	.half	.L838-.L345
	.byte	1,5,11,9
	.half	.L344-.L838
	.byte	3,1,1,5,13,9
	.half	.L348-.L344
	.byte	3,1,1,5,21,9
	.half	.L839-.L348
	.byte	1,5,8,9
	.half	.L351-.L839
	.byte	3,1,1,5,14,9
	.half	.L346-.L351
	.byte	1,5,12,9
	.half	.L349-.L346
	.byte	1,5,36,9
	.half	.L840-.L349
	.byte	3,1,1,5,5,9
	.half	.L353-.L840
	.byte	3,1,1,5,1,9
	.half	.L9-.L353
	.byte	3,1,1,7,9
	.half	.L147-.L9
	.byte	0,1,1
.L835:
	.sdecl	'.debug_ranges',debug,cluster('get_int_number')
	.sect	'.debug_ranges'
.L146:
	.word	-1,.L86,0,.L147-.L86,0,0
	.sdecl	'.debug_info',debug,cluster('get_float_number')
	.sect	'.debug_info'
.L148:
	.word	332
	.half	3
	.word	.L149
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_gnss.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L151,.L150
	.byte	2
	.word	.L109
	.byte	3
	.byte	'get_float_number',0,1,134,1,14
	.word	.L282
	.byte	1,1
	.word	.L88,.L283,.L87
	.byte	4
	.byte	's',0,1,134,1,38
	.word	.L268,.L284
	.byte	5
	.word	.L88,.L283
	.byte	6
	.byte	'i',0,1,136,1,11
	.word	.L224,.L285
	.byte	6
	.byte	'buf',0,1,137,1,10
	.word	.L286,.L287
	.byte	6
	.byte	'return_value',0,1,138,1,11
	.word	.L282,.L288
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('get_float_number')
	.sect	'.debug_abbrev'
.L149:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('get_float_number')
	.sect	'.debug_line'
.L150:
	.word	.L842-.L841
.L841:
	.half	3
	.word	.L844-.L843
.L843:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_gnss.c',0,0,0,0,0
.L844:
	.byte	5,14,7,0,5,2
	.word	.L88
	.byte	3,133,1,1,5,29,9
	.half	.L356-.L88
	.byte	3,6,1,5,32,9
	.half	.L845-.L356
	.byte	1,5,11,9
	.half	.L355-.L845
	.byte	3,1,1,5,13,9
	.half	.L359-.L355
	.byte	3,1,1,5,21,9
	.half	.L846-.L359
	.byte	1,5,8,9
	.half	.L362-.L846
	.byte	3,1,1,5,14,9
	.half	.L357-.L362
	.byte	1,5,12,9
	.half	.L360-.L357
	.byte	1,5,46,9
	.half	.L847-.L360
	.byte	3,1,1,5,20,9
	.half	.L848-.L847
	.byte	1,5,5,9
	.half	.L849-.L848
	.byte	3,1,1,5,1,9
	.half	.L10-.L849
	.byte	3,1,1,7,9
	.half	.L152-.L10
	.byte	0,1,1
.L842:
	.sdecl	'.debug_ranges',debug,cluster('get_float_number')
	.sect	'.debug_ranges'
.L151:
	.word	-1,.L88,0,.L152-.L88,0,0
	.sdecl	'.debug_info',debug,cluster('get_double_number')
	.sect	'.debug_info'
.L153:
	.word	333
	.half	3
	.word	.L154
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_gnss.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L156,.L155
	.byte	2
	.word	.L109
	.byte	3
	.byte	'get_double_number',0,1,155,1,15
	.word	.L200
	.byte	1,1
	.word	.L90,.L289,.L89
	.byte	4
	.byte	's',0,1,155,1,40
	.word	.L268,.L290
	.byte	5
	.word	.L90,.L289
	.byte	6
	.byte	'i',0,1,157,1,11
	.word	.L224,.L291
	.byte	6
	.byte	'buf',0,1,158,1,10
	.word	.L286,.L292
	.byte	6
	.byte	'return_value',0,1,159,1,12
	.word	.L200,.L293
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('get_double_number')
	.sect	'.debug_abbrev'
.L154:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('get_double_number')
	.sect	'.debug_line'
.L155:
	.word	.L851-.L850
.L850:
	.half	3
	.word	.L853-.L852
.L852:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_gnss.c',0,0,0,0,0
.L853:
	.byte	5,15,7,0,5,2
	.word	.L90
	.byte	3,154,1,1,5,29,9
	.half	.L366-.L90
	.byte	3,6,1,5,32,9
	.half	.L854-.L366
	.byte	1,5,11,9
	.half	.L365-.L854
	.byte	3,1,1,5,13,9
	.half	.L369-.L365
	.byte	3,1,1,5,21,9
	.half	.L855-.L369
	.byte	1,5,8,9
	.half	.L372-.L855
	.byte	3,1,1,5,14,9
	.half	.L367-.L372
	.byte	1,5,12,9
	.half	.L370-.L367
	.byte	1,5,39,9
	.half	.L856-.L370
	.byte	3,1,1,5,5,9
	.half	.L857-.L856
	.byte	3,1,1,5,1,9
	.half	.L11-.L857
	.byte	3,1,1,7,9
	.half	.L157-.L11
	.byte	0,1,1
.L851:
	.sdecl	'.debug_ranges',debug,cluster('get_double_number')
	.sect	'.debug_ranges'
.L156:
	.word	-1,.L90,0,.L157-.L90,0,0
	.sdecl	'.debug_info',debug,cluster('utc_to_btc')
	.sect	'.debug_info'
.L158:
	.word	288
	.half	3
	.word	.L159
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_gnss.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L161,.L160
	.byte	2
	.word	.L109
	.byte	3
	.byte	'utc_to_btc',0,1,176,1,13,1,1
	.word	.L92,.L294,.L91
	.byte	4
	.byte	'time',0,1,176,1,42
	.word	.L295,.L296
	.byte	5
	.word	.L92,.L294
	.byte	6
	.byte	'day_num',0,1,178,1,11
	.word	.L224,.L297
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('utc_to_btc')
	.sect	'.debug_abbrev'
.L159:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3,8,58
	.byte	15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('utc_to_btc')
	.sect	'.debug_line'
.L160:
	.word	.L859-.L858
.L858:
	.half	3
	.word	.L861-.L860
.L860:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_gnss.c',0,0,0,0,0
.L861:
	.byte	5,22,7,0,5,2
	.word	.L92
	.byte	3,179,1,1,5,29,9
	.half	.L862-.L92
	.byte	1,5,16,9
	.half	.L863-.L862
	.byte	1,5,17,9
	.half	.L864-.L863
	.byte	3,1,1,5,8,9
	.half	.L865-.L864
	.byte	1,5,5,9
	.half	.L866-.L865
	.byte	1,5,13,7,9
	.half	.L867-.L866
	.byte	3,2,1,5,20,9
	.half	.L868-.L867
	.byte	1,5,13,9
	.half	.L869-.L868
	.byte	3,1,1,5,19,9
	.half	.L870-.L869
	.byte	1,5,21,9
	.half	.L871-.L870
	.byte	3,2,1,5,9,9
	.half	.L872-.L871
	.byte	1,5,21,7,9
	.half	.L873-.L872
	.byte	3,2,1,5,26,9
	.half	.L374-.L873
	.byte	3,1,1,5,35,9
	.half	.L874-.L374
	.byte	1,5,33,9
	.half	.L875-.L874
	.byte	1,5,17,9
	.half	.L876-.L875
	.byte	1,5,49,7,9
	.half	.L877-.L876
	.byte	1,5,58,9
	.half	.L878-.L877
	.byte	1,5,56,9
	.half	.L879-.L878
	.byte	1,5,42,9
	.half	.L880-.L879
	.byte	1,5,75,7,9
	.half	.L14-.L880
	.byte	1,5,84,9
	.half	.L881-.L14
	.byte	1,5,82,9
	.half	.L882-.L881
	.byte	1,5,68,9
	.half	.L883-.L882
	.byte	1,5,25,7,9
	.half	.L15-.L883
	.byte	3,2,1,5,13,9
	.half	.L16-.L15
	.byte	3,126,1,5,21,9
	.half	.L13-.L16
	.byte	3,7,1,5,26,9
	.half	.L375-.L13
	.byte	3,1,1,5,16,9
	.half	.L884-.L375
	.byte	1,5,47,7,9
	.half	.L885-.L884
	.byte	1,5,40,9
	.half	.L886-.L885
	.byte	1,5,68,7,9
	.half	.L887-.L886
	.byte	1,5,58,9
	.half	.L888-.L887
	.byte	1,5,61,9
	.half	.L889-.L888
	.byte	1,5,89,7,9
	.half	.L890-.L889
	.byte	1,5,79,9
	.half	.L891-.L890
	.byte	1,5,82,9
	.half	.L892-.L891
	.byte	1,5,25,7,9
	.half	.L18-.L892
	.byte	3,2,1,5,16,9
	.half	.L17-.L18
	.byte	3,4,1,5,9,9
	.half	.L893-.L17
	.byte	1,5,25,7,9
	.half	.L894-.L893
	.byte	3,2,1,5,23,9
	.half	.L895-.L894
	.byte	1,5,17,9
	.half	.L896-.L895
	.byte	3,1,1,5,25,9
	.half	.L897-.L896
	.byte	1,9
	.half	.L898-.L897
	.byte	3,1,1,5,13,9
	.half	.L899-.L898
	.byte	1,5,21,7,9
	.half	.L900-.L899
	.byte	3,2,1,5,29,9
	.half	.L901-.L900
	.byte	1,5,21,9
	.half	.L902-.L901
	.byte	3,1,1,5,28,9
	.half	.L903-.L902
	.byte	1,5,1,9
	.half	.L12-.L903
	.byte	3,4,1,7,9
	.half	.L162-.L12
	.byte	0,1,1
.L859:
	.sdecl	'.debug_ranges',debug,cluster('utc_to_btc')
	.sect	'.debug_ranges'
.L161:
	.word	-1,.L92,0,.L162-.L92,0,0
	.sdecl	'.debug_info',debug,cluster('gps_gnrmc_parse')
	.sect	'.debug_info'
.L163:
	.word	554
	.half	3
	.word	.L164
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_gnss.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L166,.L165
	.byte	2
	.word	.L109
	.byte	3
	.byte	'gps_gnrmc_parse',0,1,224,1,14
	.word	.L224
	.byte	1,1
	.word	.L94,.L298,.L93
	.byte	4
	.byte	'line',0,1,224,1,37
	.word	.L268,.L299
	.byte	4
	.byte	'gnss',0,1,224,1,61
	.word	.L300,.L301
	.byte	5
	.word	.L94,.L298
	.byte	6
	.byte	'state',0,1,226,1,11
	.word	.L224,.L302
	.byte	6
	.byte	'temp',0,1,226,1,22
	.word	.L224,.L303
	.byte	6
	.byte	'latitude',0,1,228,1,13
	.word	.L200,.L304
	.byte	6
	.byte	'longitude',0,1,229,1,13
	.word	.L200,.L305
	.byte	6
	.byte	'lati_cent_tmp',0,1,231,1,12
	.word	.L200,.L306
	.byte	6
	.byte	'lati_second_tmp',0,1,231,1,31
	.word	.L200,.L307
	.byte	6
	.byte	'long_cent_tmp',0,1,232,1,12
	.word	.L200,.L308
	.byte	6
	.byte	'long_second_tmp',0,1,232,1,31
	.word	.L200,.L309
	.byte	6
	.byte	'speed_tmp',0,1,233,1,11
	.word	.L282,.L310
	.byte	6
	.byte	'buf',0,1,234,1,11
	.word	.L268,.L311
	.byte	6
	.byte	'return_state',0,1,235,1,11
	.word	.L224,.L312
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('gps_gnrmc_parse')
	.sect	'.debug_abbrev'
.L164:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('gps_gnrmc_parse')
	.sect	'.debug_line'
.L165:
	.word	.L905-.L904
.L904:
	.half	3
	.word	.L907-.L906
.L906:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_gnss.c',0,0,0,0,0
.L907:
	.byte	5,14,7,0,5,2
	.word	.L94
	.byte	3,223,1,1,5,24,9
	.half	.L378-.L94
	.byte	3,11,1,5,37,9
	.half	.L379-.L378
	.byte	3,2,1,5,40,9
	.half	.L908-.L379
	.byte	1,5,16,9
	.half	.L376-.L908
	.byte	1,5,8,9
	.half	.L380-.L376
	.byte	3,2,1,5,24,9
	.half	.L909-.L380
	.byte	1,5,28,9
	.half	.L910-.L909
	.byte	1,5,22,7,9
	.half	.L24-.L910
	.byte	3,2,1,5,23,9
	.half	.L911-.L24
	.byte	3,1,1,5,21,9
	.half	.L912-.L911
	.byte	1,5,60,9
	.half	.L913-.L912
	.byte	3,1,1,5,63,9
	.half	.L914-.L913
	.byte	1,5,39,9
	.half	.L381-.L914
	.byte	1,5,34,9
	.half	.L915-.L381
	.byte	1,5,60,9
	.half	.L916-.L915
	.byte	3,1,1,5,63,9
	.half	.L917-.L916
	.byte	1,5,39,9
	.half	.L384-.L917
	.byte	1,5,34,9
	.half	.L918-.L384
	.byte	1,5,78,9
	.half	.L919-.L918
	.byte	3,2,1,5,81,9
	.half	.L920-.L919
	.byte	1,5,57,9
	.half	.L386-.L920
	.byte	1,5,78,9
	.half	.L921-.L386
	.byte	3,1,1,5,81,9
	.half	.L922-.L921
	.byte	1,5,57,9
	.half	.L923-.L922
	.byte	1,5,36,9
	.half	.L389-.L923
	.byte	3,2,1,5,52,9
	.half	.L924-.L389
	.byte	1,5,50,9
	.half	.L925-.L924
	.byte	1,5,34,9
	.half	.L926-.L925
	.byte	1,5,51,9
	.half	.L927-.L926
	.byte	3,1,1,5,69,9
	.half	.L928-.L927
	.byte	1,5,45,9
	.half	.L391-.L928
	.byte	1,5,36,9
	.half	.L393-.L391
	.byte	3,1,1,5,34,9
	.half	.L929-.L393
	.byte	1,5,56,9
	.half	.L930-.L929
	.byte	3,1,1,5,50,9
	.half	.L395-.L930
	.byte	1,5,75,9
	.half	.L931-.L395
	.byte	1,5,73,9
	.half	.L932-.L931
	.byte	1,5,36,9
	.half	.L933-.L932
	.byte	3,1,1,5,34,9
	.half	.L934-.L933
	.byte	1,5,36,9
	.half	.L397-.L934
	.byte	3,2,1,5,53,9
	.half	.L935-.L397
	.byte	1,5,51,9
	.half	.L936-.L935
	.byte	1,5,34,9
	.half	.L937-.L936
	.byte	1,5,52,9
	.half	.L938-.L937
	.byte	3,1,1,5,71,9
	.half	.L939-.L938
	.byte	1,5,46,9
	.half	.L399-.L939
	.byte	1,5,36,9
	.half	.L401-.L399
	.byte	3,1,1,5,34,9
	.half	.L940-.L401
	.byte	1,5,56,9
	.half	.L941-.L940
	.byte	3,1,1,5,50,9
	.half	.L403-.L941
	.byte	1,5,76,9
	.half	.L942-.L403
	.byte	1,5,74,9
	.half	.L943-.L942
	.byte	1,5,36,9
	.half	.L944-.L943
	.byte	3,1,1,5,34,9
	.half	.L945-.L944
	.byte	1,5,32,9
	.half	.L946-.L945
	.byte	3,2,1,5,68,9
	.half	.L947-.L946
	.byte	1,5,66,9
	.half	.L405-.L947
	.byte	1,5,50,9
	.half	.L948-.L405
	.byte	1,5,26,9
	.half	.L949-.L948
	.byte	1,5,32,9
	.half	.L950-.L949
	.byte	3,1,1,5,69,9
	.half	.L951-.L950
	.byte	1,5,67,9
	.half	.L407-.L951
	.byte	1,5,51,9
	.half	.L952-.L407
	.byte	1,5,26,9
	.half	.L953-.L952
	.byte	1,5,69,9
	.half	.L954-.L953
	.byte	3,2,1,5,72,9
	.half	.L955-.L954
	.byte	1,5,48,9
	.half	.L410-.L955
	.byte	1,5,40,9
	.half	.L411-.L410
	.byte	3,1,1,5,38,9
	.half	.L956-.L411
	.byte	1,5,26,9
	.half	.L957-.L956
	.byte	1,5,70,9
	.half	.L958-.L957
	.byte	3,1,1,5,73,9
	.half	.L959-.L958
	.byte	1,5,49,9
	.half	.L412-.L959
	.byte	1,5,26,9
	.half	.L960-.L412
	.byte	1,5,25,9
	.half	.L961-.L960
	.byte	3,103,1,5,23,9
	.half	.L25-.L961
	.byte	3,29,1,5,21,9
	.half	.L962-.L25
	.byte	1,5,30,9
	.half	.L26-.L962
	.byte	3,4,1,5,34,9
	.half	.L963-.L26
	.byte	1,5,43,9
	.half	.L964-.L963
	.byte	1,5,52,9
	.half	.L965-.L964
	.byte	1,5,56,9
	.half	.L966-.L965
	.byte	1,5,46,9
	.half	.L967-.L966
	.byte	1,5,24,9
	.half	.L968-.L967
	.byte	1,5,30,9
	.half	.L969-.L968
	.byte	3,1,1,5,34,9
	.half	.L970-.L969
	.byte	1,5,43,9
	.half	.L971-.L970
	.byte	1,5,52,9
	.half	.L972-.L971
	.byte	1,5,57,9
	.half	.L973-.L972
	.byte	1,5,46,9
	.half	.L974-.L973
	.byte	1,5,24,9
	.half	.L975-.L974
	.byte	1,5,30,9
	.half	.L976-.L975
	.byte	3,1,1,5,35,9
	.half	.L977-.L976
	.byte	1,5,44,9
	.half	.L978-.L977
	.byte	1,5,53,9
	.half	.L979-.L978
	.byte	1,5,58,9
	.half	.L980-.L979
	.byte	1,5,47,9
	.half	.L981-.L980
	.byte	1,5,24,9
	.half	.L982-.L981
	.byte	1,5,32,9
	.half	.L983-.L982
	.byte	3,1,1,5,35,9
	.half	.L984-.L983
	.byte	1,5,30,9
	.half	.L415-.L984
	.byte	3,1,1,5,41,9
	.half	.L985-.L415
	.byte	1,5,50,9
	.half	.L986-.L985
	.byte	1,5,59,9
	.half	.L987-.L986
	.byte	1,5,70,9
	.half	.L988-.L987
	.byte	1,5,53,9
	.half	.L989-.L988
	.byte	1,5,24,9
	.half	.L990-.L989
	.byte	1,5,30,9
	.half	.L991-.L990
	.byte	3,1,1,5,41,9
	.half	.L992-.L991
	.byte	1,5,50,9
	.half	.L993-.L992
	.byte	1,5,59,9
	.half	.L994-.L993
	.byte	1,5,70,9
	.half	.L995-.L994
	.byte	1,5,53,9
	.half	.L996-.L995
	.byte	1,5,24,9
	.half	.L997-.L996
	.byte	1,5,30,9
	.half	.L998-.L997
	.byte	3,1,1,5,41,9
	.half	.L999-.L998
	.byte	1,5,50,9
	.half	.L1000-.L999
	.byte	1,5,59,9
	.half	.L1001-.L1000
	.byte	1,5,70,9
	.half	.L1002-.L1001
	.byte	1,5,53,9
	.half	.L1003-.L1002
	.byte	1,5,77,9
	.half	.L1004-.L1003
	.byte	1,5,24,9
	.half	.L1005-.L1004
	.byte	1,5,21,9
	.half	.L1006-.L1005
	.byte	3,2,1,5,5,9
	.half	.L416-.L1006
	.byte	3,2,1,5,1,9
	.half	.L27-.L416
	.byte	3,1,1,7,9
	.half	.L167-.L27
	.byte	0,1,1
.L905:
	.sdecl	'.debug_ranges',debug,cluster('gps_gnrmc_parse')
	.sect	'.debug_ranges'
.L166:
	.word	-1,.L94,0,.L167-.L94,0,0
	.sdecl	'.debug_info',debug,cluster('gps_gngga_parse')
	.sect	'.debug_info'
.L168:
	.word	356
	.half	3
	.word	.L169
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_gnss.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L171,.L170
	.byte	2
	.word	.L109
	.byte	3
	.byte	'gps_gngga_parse',0,1,167,2,14
	.word	.L224
	.byte	1,1
	.word	.L96,.L313,.L95
	.byte	4
	.byte	'line',0,1,167,2,37
	.word	.L268,.L314
	.byte	4
	.byte	'gnss',0,1,167,2,61
	.word	.L300,.L315
	.byte	5
	.word	.L96,.L313
	.byte	6
	.byte	'state',0,1,169,2,11
	.word	.L224,.L316
	.byte	6
	.byte	'buf',0,1,170,2,11
	.word	.L268,.L317
	.byte	6
	.byte	'return_state',0,1,171,2,11
	.word	.L224,.L318
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('gps_gngga_parse')
	.sect	'.debug_abbrev'
.L169:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('gps_gngga_parse')
	.sect	'.debug_line'
.L170:
	.word	.L1008-.L1007
.L1007:
	.half	3
	.word	.L1010-.L1009
.L1009:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_gnss.c',0,0,0,0,0
.L1010:
	.byte	5,14,7,0,5,2
	.word	.L96
	.byte	3,166,2,1,5,24,9
	.half	.L422-.L96
	.byte	3,4,1,5,37,9
	.half	.L423-.L422
	.byte	3,2,1,5,40,9
	.half	.L1011-.L423
	.byte	1,5,16,9
	.half	.L419-.L1011
	.byte	1,5,8,9
	.half	.L424-.L419
	.byte	3,2,1,5,5,9
	.half	.L1012-.L424
	.byte	1,5,79,7,9
	.half	.L1013-.L1012
	.byte	3,2,1,5,82,9
	.half	.L1014-.L1013
	.byte	1,5,58,9
	.half	.L425-.L1014
	.byte	1,5,30,9
	.half	.L1015-.L425
	.byte	1,5,74,9
	.half	.L1016-.L1015
	.byte	3,1,1,5,77,9
	.half	.L1017-.L1016
	.byte	1,5,53,9
	.half	.L428-.L1017
	.byte	1,5,128,1,9
	.half	.L1018-.L428
	.byte	1,5,132,1,9
	.half	.L1019-.L1018
	.byte	1,5,107,9
	.half	.L430-.L1019
	.byte	1,5,53,9
	.half	.L421-.L430
	.byte	1,5,84,9
	.half	.L1020-.L421
	.byte	1,5,107,9
	.half	.L1021-.L1020
	.byte	1,5,84,9
	.half	.L1022-.L1021
	.byte	1,5,30,9
	.half	.L1023-.L1022
	.byte	1,5,22,9
	.half	.L1024-.L1023
	.byte	3,1,1,5,5,9
	.half	.L28-.L1024
	.byte	3,3,1,5,1,9
	.half	.L29-.L28
	.byte	3,1,1,7,9
	.half	.L172-.L29
	.byte	0,1,1
.L1008:
	.sdecl	'.debug_ranges',debug,cluster('gps_gngga_parse')
	.sect	'.debug_ranges'
.L171:
	.word	-1,.L96,0,.L172-.L96,0,0
	.sdecl	'.debug_info',debug,cluster('gps_gnths_parse')
	.sect	'.debug_info'
.L173:
	.word	356
	.half	3
	.word	.L174
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_gnss.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L176,.L175
	.byte	2
	.word	.L109
	.byte	3
	.byte	'gps_gnths_parse',0,1,193,2,14
	.word	.L224
	.byte	1,1
	.word	.L98,.L319,.L97
	.byte	4
	.byte	'line',0,1,193,2,37
	.word	.L268,.L320
	.byte	4
	.byte	'gnss',0,1,193,2,61
	.word	.L300,.L321
	.byte	5
	.word	.L98,.L319
	.byte	6
	.byte	'state',0,1,195,2,11
	.word	.L224,.L322
	.byte	6
	.byte	'buf',0,1,196,2,11
	.word	.L268,.L323
	.byte	6
	.byte	'return_state',0,1,197,2,11
	.word	.L224,.L324
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('gps_gnths_parse')
	.sect	'.debug_abbrev'
.L174:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('gps_gnths_parse')
	.sect	'.debug_line'
.L175:
	.word	.L1026-.L1025
.L1025:
	.half	3
	.word	.L1028-.L1027
.L1027:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_gnss.c',0,0,0,0,0
.L1028:
	.byte	5,14,7,0,5,2
	.word	.L98
	.byte	3,192,2,1,5,24,9
	.half	.L434-.L98
	.byte	3,4,1,5,37,9
	.half	.L435-.L434
	.byte	3,2,1,5,40,9
	.half	.L1029-.L435
	.byte	1,5,16,9
	.half	.L432-.L1029
	.byte	1,5,8,9
	.half	.L436-.L432
	.byte	3,2,1,5,5,9
	.half	.L1030-.L436
	.byte	1,5,41,7,9
	.half	.L1031-.L1030
	.byte	3,2,1,5,39,9
	.half	.L1032-.L1031
	.byte	1,5,77,9
	.half	.L1033-.L1032
	.byte	3,1,1,5,80,9
	.half	.L1034-.L1033
	.byte	1,5,56,9
	.half	.L437-.L1034
	.byte	1,5,33,9
	.half	.L1035-.L437
	.byte	1,5,22,9
	.half	.L1036-.L1035
	.byte	3,1,1,5,42,9
	.half	.L1037-.L1036
	.byte	3,126,1,5,41,9
	.half	.L30-.L1037
	.byte	3,6,1,5,39,9
	.half	.L1038-.L30
	.byte	1,5,5,9
	.half	.L31-.L1038
	.byte	3,3,1,5,1,9
	.half	.L32-.L31
	.byte	3,1,1,7,9
	.half	.L177-.L32
	.byte	0,1,1
.L1026:
	.sdecl	'.debug_ranges',debug,cluster('gps_gnths_parse')
	.sect	'.debug_ranges'
.L176:
	.word	-1,.L98,0,.L177-.L98,0,0
	.sdecl	'.debug_info',debug,cluster('gnss')
	.sect	'.debug_info'
.L178:
	.word	220
	.half	3
	.word	.L179
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_gnss.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L109
	.byte	3
	.byte	'gnss',0,25,58,29
	.word	.L325
	.byte	1,5,3
	.word	gnss
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('gnss')
	.sect	'.debug_abbrev'
.L179:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('gnss_flag')
	.sect	'.debug_info'
.L180:
	.word	225
	.half	3
	.word	.L181
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_gnss.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L109
	.byte	3
	.byte	'gnss_flag',0,25,57,29
	.word	.L224
	.byte	1,5,3
	.word	gnss_flag
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('gnss_flag')
	.sect	'.debug_abbrev'
.L181:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('gnss_state')
	.sect	'.debug_info'
.L182:
	.word	225
	.half	3
	.word	.L183
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_gnss.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L109
	.byte	3
	.byte	'gnss_state',0,25,60,29
	.word	.L224
	.byte	5,3
	.word	gnss_state
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('gnss_state')
	.sect	'.debug_abbrev'
.L183:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_info',debug,cluster('gnss_receiver_fifo')
	.sect	'.debug_info'
.L184:
	.word	233
	.half	3
	.word	.L185
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_gnss.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L109
	.byte	3
	.byte	'gnss_receiver_fifo',0,25,61,25
	.word	.L326
	.byte	5,3
	.word	gnss_receiver_fifo
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('gnss_receiver_fifo')
	.sect	'.debug_abbrev'
.L185:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_info',debug,cluster('gnss_receiver_buffer')
	.sect	'.debug_info'
.L186:
	.word	235
	.half	3
	.word	.L187
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_gnss.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L109
	.byte	3
	.byte	'gnss_receiver_buffer',0,25,62,29
	.word	.L327
	.byte	5,3
	.word	gnss_receiver_buffer
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('gnss_receiver_buffer')
	.sect	'.debug_abbrev'
.L187:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_info',debug,cluster('gnss_gga_state')
	.sect	'.debug_info'
.L188:
	.word	229
	.half	3
	.word	.L189
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_gnss.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L109
	.byte	3
	.byte	'gnss_gga_state',0,25,64,29
	.word	.L328
	.byte	5,3
	.word	gnss_gga_state
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('gnss_gga_state')
	.sect	'.debug_abbrev'
.L189:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_info',debug,cluster('gnss_rmc_state')
	.sect	'.debug_info'
.L190:
	.word	229
	.half	3
	.word	.L191
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_gnss.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L109
	.byte	3
	.byte	'gnss_rmc_state',0,25,65,29
	.word	.L328
	.byte	5,3
	.word	gnss_rmc_state
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('gnss_rmc_state')
	.sect	'.debug_abbrev'
.L191:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_info',debug,cluster('gnss_ths_state')
	.sect	'.debug_info'
.L192:
	.word	229
	.half	3
	.word	.L193
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_gnss.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L109
	.byte	3
	.byte	'gnss_ths_state',0,25,66,29
	.word	.L328
	.byte	5,3
	.word	gnss_ths_state
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('gnss_ths_state')
	.sect	'.debug_abbrev'
.L193:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_info',debug,cluster('gps_gga_buffer')
	.sect	'.debug_info'
.L194:
	.word	229
	.half	3
	.word	.L195
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_gnss.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L109
	.byte	3
	.byte	'gps_gga_buffer',0,25,68,29
	.word	.L327
	.byte	5,3
	.word	gps_gga_buffer
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('gps_gga_buffer')
	.sect	'.debug_abbrev'
.L195:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_info',debug,cluster('gps_rmc_buffer')
	.sect	'.debug_info'
.L196:
	.word	229
	.half	3
	.word	.L197
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_gnss.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L109
	.byte	3
	.byte	'gps_rmc_buffer',0,25,69,29
	.word	.L327
	.byte	5,3
	.word	gps_rmc_buffer
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('gps_rmc_buffer')
	.sect	'.debug_abbrev'
.L197:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_info',debug,cluster('gps_ths_buffer')
	.sect	'.debug_info'
.L198:
	.word	229
	.half	3
	.word	.L199
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_gnss.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L109
	.byte	3
	.byte	'gps_ths_buffer',0,25,70,29
	.word	.L327
	.byte	5,3
	.word	gps_ths_buffer
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('gps_ths_buffer')
	.sect	'.debug_abbrev'
.L199:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_loc',debug,cluster('get_double_number')
	.sect	'.debug_loc'
.L292:
	.word	-1,.L90,0,.L289-.L90
	.half	2
	.byte	145,112
	.word	0,0
.L89:
	.word	-1,.L90,0,.L364-.L90
	.half	2
	.byte	138,0
	.word	.L364-.L90,.L289-.L90
	.half	2
	.byte	138,16
	.word	.L289-.L90,.L289-.L90
	.half	2
	.byte	138,0
	.word	0,0
.L291:
	.word	-1,.L90,.L365-.L90,.L368-.L90
	.half	1
	.byte	82
	.word	.L369-.L90,.L370-.L90
	.half	1
	.byte	95
	.word	.L373-.L90,.L372-.L90
	.half	1
	.byte	84
	.word	0,0
.L293:
	.word	0,0
.L290:
	.word	-1,.L90,0,.L365-.L90
	.half	1
	.byte	100
	.word	.L366-.L90,.L367-.L90
	.half	1
	.byte	111
	.word	.L371-.L90,.L372-.L90
	.half	1
	.byte	101
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('get_float_number')
	.sect	'.debug_loc'
.L287:
	.word	-1,.L88,0,.L283-.L88
	.half	2
	.byte	145,112
	.word	0,0
.L87:
	.word	-1,.L88,0,.L354-.L88
	.half	2
	.byte	138,0
	.word	.L354-.L88,.L283-.L88
	.half	2
	.byte	138,16
	.word	.L283-.L88,.L283-.L88
	.half	2
	.byte	138,0
	.word	0,0
.L285:
	.word	-1,.L88,.L355-.L88,.L358-.L88
	.half	1
	.byte	82
	.word	.L359-.L88,.L360-.L88
	.half	1
	.byte	95
	.word	.L363-.L88,.L362-.L88
	.half	1
	.byte	84
	.word	0,0
.L288:
	.word	0,0
.L284:
	.word	-1,.L88,0,.L355-.L88
	.half	1
	.byte	100
	.word	.L356-.L88,.L357-.L88
	.half	1
	.byte	111
	.word	.L361-.L88,.L362-.L88
	.half	1
	.byte	101
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('get_int_number')
	.sect	'.debug_loc'
.L279:
	.word	-1,.L86,0,.L276-.L86
	.half	2
	.byte	145,112
	.word	0,0
.L85:
	.word	-1,.L86,0,.L343-.L86
	.half	2
	.byte	138,0
	.word	.L343-.L86,.L276-.L86
	.half	2
	.byte	138,16
	.word	.L276-.L86,.L276-.L86
	.half	2
	.byte	138,0
	.word	0,0
.L280:
	.word	-1,.L86,.L344-.L86,.L347-.L86
	.half	1
	.byte	82
	.word	.L348-.L86,.L349-.L86
	.half	1
	.byte	95
	.word	.L352-.L86,.L351-.L86
	.half	1
	.byte	84
	.word	0,0
.L281:
	.word	-1,.L86,.L353-.L86,.L276-.L86
	.half	1
	.byte	82
	.word	0,0
.L277:
	.word	-1,.L86,0,.L344-.L86
	.half	1
	.byte	100
	.word	.L345-.L86,.L346-.L86
	.half	1
	.byte	111
	.word	.L350-.L86,.L351-.L86
	.half	1
	.byte	101
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('get_parameter_index')
	.sect	'.debug_loc'
.L83:
	.word	-1,.L84,0,.L266-.L84
	.half	2
	.byte	138,0
	.word	0,0
.L270:
	.word	-1,.L84,.L340-.L84,.L341-.L84
	.half	1
	.byte	95
	.word	.L6-.L84,.L342-.L84
	.half	1
	.byte	95
	.word	.L3-.L84,.L7-.L84
	.half	1
	.byte	95
	.word	0,0
.L271:
	.word	-1,.L84,.L333-.L84,.L334-.L84
	.half	1
	.byte	89
	.word	.L5-.L84,.L266-.L84
	.half	1
	.byte	89
	.word	0,0
.L273:
	.word	-1,.L84,.L335-.L84,.L336-.L84
	.half	5
	.byte	144,32,157,32,0
	.word	.L2-.L84,.L266-.L84
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L274:
	.word	-1,.L84,.L337-.L84,.L266-.L84
	.half	1
	.byte	82
	.word	0,0
.L267:
	.word	-1,.L84,0,.L329-.L84
	.half	1
	.byte	84
	.word	.L331-.L84,.L266-.L84
	.half	1
	.byte	88
	.word	0,0
.L269:
	.word	-1,.L84,0,.L330-.L84
	.half	1
	.byte	100
	.word	.L332-.L84,.L266-.L84
	.half	1
	.byte	111
	.word	.L339-.L84,.L2-.L84
	.half	1
	.byte	81
	.word	0,0
.L272:
	.word	-1,.L84,.L330-.L84,.L4-.L84
	.half	1
	.byte	98
	.word	.L336-.L84,.L338-.L84
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('get_two_points_azimuth')
	.sect	'.debug_loc'
.L223:
	.word	-1,.L102,.L499-.L102,.L500-.L102
	.half	2
	.byte	144,36
	.word	.L501-.L102,.L502-.L102
	.half	2
	.byte	144,36
	.word	.L34-.L102,.L35-.L102
	.half	2
	.byte	144,36
	.word	0,0
.L101:
	.word	-1,.L102,0,.L471-.L102
	.half	2
	.byte	138,0
	.word	.L471-.L102,.L215-.L102
	.half	2
	.byte	138,8
	.word	.L215-.L102,.L215-.L102
	.half	2
	.byte	138,0
	.word	0,0
.L216:
	.word	-1,.L102,0,.L472-.L102
	.half	2
	.byte	144,34
	.word	.L485-.L102,.L486-.L102
	.half	2
	.byte	144,38
	.word	.L487-.L102,.L488-.L102
	.half	2
	.byte	144,38
	.word	0,0
.L218:
	.word	-1,.L102,0,.L215-.L102
	.half	2
	.byte	145,0
	.word	.L474-.L102,.L475-.L102
	.half	2
	.byte	144,37
	.word	.L478-.L102,.L475-.L102
	.half	2
	.byte	144,33
	.word	.L482-.L102,.L483-.L102
	.half	2
	.byte	144,34
	.word	.L489-.L102,.L490-.L102
	.half	2
	.byte	144,34
	.word	.L494-.L102,.L495-.L102
	.half	2
	.byte	144,34
	.word	0,0
.L217:
	.word	-1,.L102,0,.L473-.L102
	.half	2
	.byte	144,35
	.word	.L479-.L102,.L480-.L102
	.half	2
	.byte	144,36
	.word	.L477-.L102,.L481-.L102
	.half	2
	.byte	144,37
	.word	.L492-.L102,.L493-.L102
	.half	2
	.byte	144,37
	.word	0,0
.L219:
	.word	-1,.L102,0,.L215-.L102
	.half	2
	.byte	145,8
	.word	.L476-.L102,.L477-.L102
	.half	2
	.byte	144,39
	.word	.L491-.L102,.L492-.L102
	.half	2
	.byte	144,39
	.word	0,0
.L221:
	.word	-1,.L102,.L484-.L102,.L485-.L102
	.half	2
	.byte	144,33
	.word	.L485-.L102,.L215-.L102
	.half	2
	.byte	145,120
	.word	.L496-.L102,.L497-.L102
	.half	2
	.byte	144,34
	.word	0,0
.L222:
	.word	-1,.L102,.L496-.L102,.L498-.L102
	.half	2
	.byte	144,33
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('get_two_points_distance')
	.sect	'.debug_loc'
.L207:
	.word	-1,.L100,.L447-.L100,.L441-.L100
	.half	2
	.byte	144,32
	.word	.L448-.L100,.L201-.L100
	.half	2
	.byte	145,120
	.word	.L469-.L100,.L470-.L100
	.half	2
	.byte	144,35
	.word	0,0
.L213:
	.word	-1,.L100,.L459-.L100,.L460-.L100
	.half	2
	.byte	144,38
	.word	0,0
.L214:
	.word	-1,.L100,.L463-.L100,.L464-.L100
	.half	2
	.byte	144,37
	.word	0,0
.L212:
	.word	-1,.L100,.L467-.L100,.L468-.L100
	.half	2
	.byte	144,33
	.word	0,0
.L99:
	.word	-1,.L100,0,.L440-.L100
	.half	2
	.byte	138,0
	.word	.L440-.L100,.L201-.L100
	.half	2
	.byte	138,16
	.word	.L201-.L100,.L201-.L100
	.half	2
	.byte	138,0
	.word	0,0
.L202:
	.word	-1,.L100,0,.L441-.L100
	.half	2
	.byte	144,34
	.word	0,0
.L204:
	.word	-1,.L100,0,.L201-.L100
	.half	2
	.byte	145,0
	.word	.L443-.L100,.L444-.L100
	.half	2
	.byte	144,37
	.word	0,0
.L203:
	.word	-1,.L100,0,.L442-.L100
	.half	2
	.byte	144,35
	.word	.L451-.L100,.L452-.L100
	.half	2
	.byte	144,36
	.word	0,0
.L205:
	.word	-1,.L100,0,.L201-.L100
	.half	2
	.byte	145,8
	.word	.L445-.L100,.L446-.L100
	.half	2
	.byte	144,38
	.word	0,0
.L208:
	.word	-1,.L100,.L453-.L100,.L454-.L100
	.half	2
	.byte	144,39
	.word	.L461-.L100,.L462-.L100
	.half	2
	.byte	144,39
	.word	0,0
.L209:
	.word	-1,.L100,.L449-.L100,.L450-.L100
	.half	2
	.byte	144,33
	.word	.L450-.L100,.L201-.L100
	.half	2
	.byte	145,112
	.word	.L455-.L100,.L456-.L100
	.half	2
	.byte	144,35
	.word	.L465-.L100,.L466-.L100
	.half	2
	.byte	144,34
	.word	0,0
.L210:
	.word	-1,.L100,.L446-.L100,.L457-.L100
	.half	2
	.byte	144,37
	.word	0,0
.L211:
	.word	-1,.L100,.L457-.L100,.L458-.L100
	.half	2
	.byte	144,36
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('gnss_data_parse')
	.sect	'.debug_loc'
.L230:
	.word	-1,.L104,.L506-.L104,.L39-.L104
	.half	1
	.byte	81
	.word	.L509-.L104,.L44-.L104
	.half	1
	.byte	81
	.word	.L512-.L104,.L49-.L104
	.half	1
	.byte	81
	.word	0,0
.L229:
	.word	-1,.L104,.L505-.L104,.L39-.L104
	.half	5
	.byte	144,32,157,32,0
	.word	.L508-.L104,.L44-.L104
	.half	5
	.byte	144,32,157,32,0
	.word	.L511-.L104,.L49-.L104
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L228:
	.word	-1,.L104,0,.L225-.L104
	.half	2
	.byte	145,120
	.word	0,0
.L232:
	.word	-1,.L104,.L507-.L104,.L39-.L104
	.half	1
	.byte	82
	.word	.L510-.L104,.L44-.L104
	.half	1
	.byte	82
	.word	.L513-.L104,.L49-.L104
	.half	1
	.byte	82
	.word	0,0
.L103:
	.word	-1,.L104,0,.L503-.L104
	.half	2
	.byte	138,0
	.word	.L503-.L104,.L225-.L104
	.half	2
	.byte	138,8
	.word	.L225-.L104,.L225-.L104
	.half	2
	.byte	138,0
	.word	0,0
.L226:
	.word	-1,.L104,.L504-.L104,.L225-.L104
	.half	1
	.byte	88
	.word	.L514-.L104,.L225-.L104
	.half	1
	.byte	82
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('gnss_init')
	.sect	'.debug_loc'
.L249:
	.word	-1,.L108,0,.L239-.L108
	.half	3
	.byte	145,154,127
	.word	0,0
.L253:
	.word	-1,.L108,0,.L239-.L108
	.half	3
	.byte	145,176,127
	.word	0,0
.L251:
	.word	-1,.L108,0,.L239-.L108
	.half	3
	.byte	145,165,127
	.word	0,0
.L261:
	.word	-1,.L108,0,.L239-.L108
	.half	2
	.byte	145,92
	.word	0,0
.L255:
	.word	-1,.L108,0,.L239-.L108
	.half	3
	.byte	145,187,127
	.word	0,0
.L263:
	.word	-1,.L108,0,.L239-.L108
	.half	2
	.byte	145,103
	.word	0,0
.L265:
	.word	-1,.L108,0,.L239-.L108
	.half	2
	.byte	145,114
	.word	0,0
.L257:
	.word	-1,.L108,0,.L239-.L108
	.half	2
	.byte	145,70
	.word	0,0
.L259:
	.word	-1,.L108,0,.L239-.L108
	.half	2
	.byte	145,81
	.word	0,0
.L107:
	.word	-1,.L108,0,.L516-.L108
	.half	2
	.byte	138,0
	.word	.L516-.L108,.L239-.L108
	.half	3
	.byte	138,152,1
	.word	.L239-.L108,.L239-.L108
	.half	2
	.byte	138,0
	.word	0,0
.L241:
	.word	-1,.L108,0,.L517-.L108
	.half	1
	.byte	84
	.word	.L80-.L108,.L518-.L108
	.half	1
	.byte	84
	.word	0,0
.L245:
	.word	-1,.L108,0,.L239-.L108
	.half	3
	.byte	145,132,127
	.word	0,0
.L247:
	.word	-1,.L108,0,.L239-.L108
	.half	3
	.byte	145,143,127
	.word	0,0
.L243:
	.word	-1,.L108,0,.L239-.L108
	.half	3
	.byte	145,232,126
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('gnss_uart_callback')
	.sect	'.debug_loc'
.L238:
	.word	-1,.L106,0,.L233-.L106
	.half	2
	.byte	145,122
	.word	0,0
.L105:
	.word	-1,.L106,0,.L515-.L106
	.half	2
	.byte	138,0
	.word	.L515-.L106,.L233-.L106
	.half	2
	.byte	138,16
	.word	.L233-.L106,.L233-.L106
	.half	2
	.byte	138,0
	.word	0,0
.L235:
	.word	-1,.L106,0,.L233-.L106
	.half	2
	.byte	145,112
	.word	0,0
.L236:
	.word	-1,.L106,0,.L233-.L106
	.half	2
	.byte	145,118
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('gps_gngga_parse')
	.sect	'.debug_loc'
.L317:
	.word	0,0
.L315:
	.word	-1,.L96,0,.L419-.L96
	.half	1
	.byte	101
	.word	.L422-.L96,.L313-.L96
	.half	1
	.byte	109
	.word	0,0
.L95:
	.word	-1,.L96,0,.L313-.L96
	.half	2
	.byte	138,0
	.word	0,0
.L314:
	.word	-1,.L96,0,.L419-.L96
	.half	1
	.byte	100
	.word	.L420-.L96,.L421-.L96
	.half	1
	.byte	111
	.word	.L426-.L96,.L425-.L96
	.half	1
	.byte	100
	.word	.L427-.L96,.L428-.L96
	.half	1
	.byte	100
	.word	.L429-.L96,.L430-.L96
	.half	1
	.byte	100
	.word	0,0
.L318:
	.word	-1,.L96,.L423-.L96,.L313-.L96
	.half	1
	.byte	88
	.word	.L431-.L96,.L313-.L96
	.half	1
	.byte	82
	.word	0,0
.L316:
	.word	-1,.L96,.L424-.L96,.L425-.L96
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('gps_gnrmc_parse')
	.sect	'.debug_loc'
.L311:
	.word	0,0
.L301:
	.word	-1,.L94,0,.L376-.L94
	.half	1
	.byte	101
	.word	.L378-.L94,.L298-.L94
	.half	1
	.byte	111
	.word	.L417-.L94,.L416-.L94
	.half	1
	.byte	100
	.word	0,0
.L93:
	.word	-1,.L94,0,.L298-.L94
	.half	2
	.byte	138,0
	.word	0,0
.L306:
	.word	-1,.L94,.L393-.L94,.L394-.L94
	.half	2
	.byte	144,37
	.word	.L395-.L94,.L396-.L94
	.half	2
	.byte	144,37
	.word	.L405-.L94,.L406-.L94
	.half	2
	.byte	144,37
	.word	0,0
.L307:
	.word	0,0
.L304:
	.word	-1,.L94,.L389-.L94,.L390-.L94
	.half	2
	.byte	144,37
	.word	.L391-.L94,.L392-.L94
	.half	2
	.byte	144,37
	.word	0,0
.L299:
	.word	-1,.L94,0,.L376-.L94
	.half	1
	.byte	100
	.word	.L377-.L94,.L298-.L94
	.half	1
	.byte	108
	.word	.L382-.L94,.L381-.L94
	.half	1
	.byte	100
	.word	.L383-.L94,.L384-.L94
	.half	1
	.byte	100
	.word	.L385-.L94,.L386-.L94
	.half	1
	.byte	100
	.word	.L387-.L94,.L388-.L94
	.half	1
	.byte	100
	.word	.L409-.L94,.L410-.L94
	.half	1
	.byte	100
	.word	.L413-.L94,.L412-.L94
	.half	1
	.byte	100
	.word	.L414-.L94,.L415-.L94
	.half	1
	.byte	100
	.word	0,0
.L308:
	.word	-1,.L94,.L401-.L94,.L402-.L94
	.half	2
	.byte	144,38
	.word	.L403-.L94,.L404-.L94
	.half	2
	.byte	144,38
	.word	.L407-.L94,.L408-.L94
	.half	2
	.byte	144,38
	.word	0,0
.L309:
	.word	0,0
.L305:
	.word	-1,.L94,.L397-.L94,.L398-.L94
	.half	2
	.byte	144,38
	.word	.L399-.L94,.L400-.L94
	.half	2
	.byte	144,38
	.word	0,0
.L312:
	.word	-1,.L94,.L379-.L94,.L298-.L94
	.half	1
	.byte	88
	.word	.L418-.L94,.L298-.L94
	.half	1
	.byte	82
	.word	0,0
.L310:
	.word	-1,.L94,.L411-.L94,.L412-.L94
	.half	1
	.byte	82
	.word	0,0
.L302:
	.word	-1,.L94,.L380-.L94,.L381-.L94
	.half	5
	.byte	144,32,157,32,0
	.word	.L25-.L94,.L26-.L94
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L303:
	.word	-1,.L94,.L415-.L94,.L416-.L94
	.half	1
	.byte	82
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('gps_gnths_parse')
	.sect	'.debug_loc'
.L323:
	.word	0,0
.L321:
	.word	-1,.L98,0,.L432-.L98
	.half	1
	.byte	101
	.word	.L434-.L98,.L319-.L98
	.half	1
	.byte	111
	.word	0,0
.L97:
	.word	-1,.L98,0,.L319-.L98
	.half	2
	.byte	138,0
	.word	0,0
.L320:
	.word	-1,.L98,0,.L432-.L98
	.half	1
	.byte	100
	.word	.L433-.L98,.L319-.L98
	.half	1
	.byte	108
	.word	.L438-.L98,.L437-.L98
	.half	1
	.byte	100
	.word	0,0
.L324:
	.word	-1,.L98,.L435-.L98,.L319-.L98
	.half	1
	.byte	88
	.word	.L439-.L98,.L319-.L98
	.half	1
	.byte	82
	.word	0,0
.L322:
	.word	-1,.L98,.L436-.L98,.L437-.L98
	.half	5
	.byte	144,32,157,32,0
	.word	.L30-.L98,.L31-.L98
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('utc_to_btc')
	.sect	'.debug_loc'
.L297:
	.word	-1,.L92,.L374-.L92,.L13-.L92
	.half	1
	.byte	82
	.word	.L375-.L92,.L12-.L92
	.half	1
	.byte	82
	.word	0,0
.L296:
	.word	-1,.L92,0,.L294-.L92
	.half	1
	.byte	100
	.word	0,0
.L91:
	.word	-1,.L92,0,.L294-.L92
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L1039:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('get_parameter_index')
	.sect	'.debug_frame'
	.word	12
	.word	.L1039,.L84,.L266-.L84
	.sdecl	'.debug_frame',debug,cluster('get_int_number')
	.sect	'.debug_frame'
	.word	36
	.word	.L1039,.L86,.L276-.L86
	.byte	4
	.word	(.L343-.L86)/2
	.byte	19,16,22,26,3,19,138,16,4
	.word	(.L276-.L343)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('get_float_number')
	.sect	'.debug_frame'
	.word	36
	.word	.L1039,.L88,.L283-.L88
	.byte	4
	.word	(.L354-.L88)/2
	.byte	19,16,22,26,3,19,138,16,4
	.word	(.L283-.L354)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('get_double_number')
	.sect	'.debug_frame'
	.word	36
	.word	.L1039,.L90,.L289-.L90
	.byte	4
	.word	(.L364-.L90)/2
	.byte	19,16,22,26,3,19,138,16,4
	.word	(.L289-.L364)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('utc_to_btc')
	.sect	'.debug_frame'
	.word	24
	.word	.L1039,.L92,.L294-.L92
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('gps_gnrmc_parse')
	.sect	'.debug_frame'
	.word	12
	.word	.L1039,.L94,.L298-.L94
	.sdecl	'.debug_frame',debug,cluster('gps_gngga_parse')
	.sect	'.debug_frame'
	.word	12
	.word	.L1039,.L96,.L313-.L96
	.sdecl	'.debug_frame',debug,cluster('gps_gnths_parse')
	.sect	'.debug_frame'
	.word	12
	.word	.L1039,.L98,.L319-.L98
	.sdecl	'.debug_frame',debug,cluster('get_two_points_distance')
	.sect	'.debug_frame'
	.word	36
	.word	.L1039,.L100,.L201-.L100
	.byte	4
	.word	(.L440-.L100)/2
	.byte	19,16,22,26,3,19,138,16,4
	.word	(.L201-.L440)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('get_two_points_azimuth')
	.sect	'.debug_frame'
	.word	36
	.word	.L1039,.L102,.L215-.L102
	.byte	4
	.word	(.L471-.L102)/2
	.byte	19,8,22,26,3,19,138,8,4
	.word	(.L215-.L471)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('gnss_data_parse')
	.sect	'.debug_frame'
	.word	36
	.word	.L1039,.L104,.L225-.L104
	.byte	4
	.word	(.L503-.L104)/2
	.byte	19,8,22,26,3,19,138,8,4
	.word	(.L225-.L503)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('gnss_uart_callback')
	.sect	'.debug_frame'
	.word	36
	.word	.L1039,.L106,.L233-.L106
	.byte	4
	.word	(.L515-.L106)/2
	.byte	19,16,22,26,3,19,138,16,4
	.word	(.L233-.L515)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('gnss_init')
	.sect	'.debug_frame'
	.word	36
	.word	.L1039,.L108,.L239-.L108
	.byte	4
	.word	(.L516-.L108)/2
	.byte	19,152,1,22,26,4,19,138,152,1,4
	.word	(.L239-.L516)/2
	.byte	19,0,8,26
	; Module end
