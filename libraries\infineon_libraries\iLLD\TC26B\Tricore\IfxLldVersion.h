#ifndef IFX_LLD_VERSION_H
#define IFX_LLD_VERSION_H

#define IFX_LLD_VERSION_GENERATION        1   /**< \brief Indicates the driver generation */
#define IFX_LLD_VERSION_MAJOR             0   /**< \brief Informs about changes which could lead to incompatibilities */
#define IFX_LLD_VERSION_MAJOR_UPDATE      1   /**< \brief Informs about a release for a new derivative without further API changes */
#define IFX_LLD_VERSION_MINOR            11   /**< \brief Informs about new additions to the library */
#define IFX_LLD_VERSION_REVISION          0   /**< \brief Informs about patches and/or documentation changes */

#endif /* IFX_LLD_VERSION_H */
