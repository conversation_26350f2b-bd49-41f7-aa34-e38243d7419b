	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc21640a --dep-file=IfxCcu6_PinMap.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2\\\\xf_asr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.src ../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c"
	.compiler_name		"ctc"
	;source	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c'

	
$TC16X
	
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu60_CC60_P02_0_OUT',data,rom,cluster('IfxCcu60_CC60_P02_0_OUT')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu60_CC60_P02_0_OUT'
	.global	IfxCcu60_CC60_P02_0_OUT
	.align	4
IfxCcu60_CC60_P02_0_OUT:	.type	object
	.size	IfxCcu60_CC60_P02_0_OUT,16
	.word	-268424704,-268197376
	.space	4
	.byte	184
	.space	3
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu60_CC60_P02_6_OUT',data,rom,cluster('IfxCcu60_CC60_P02_6_OUT')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu60_CC60_P02_6_OUT'
	.global	IfxCcu60_CC60_P02_6_OUT
	.align	4
IfxCcu60_CC60_P02_6_OUT:	.type	object
	.size	IfxCcu60_CC60_P02_6_OUT,16
	.word	-268424704,-268197376
	.byte	6
	.space	3
	.byte	184
	.space	3
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu60_CC60_P11_12_OUT',data,rom,cluster('IfxCcu60_CC60_P11_12_OUT')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu60_CC60_P11_12_OUT'
	.global	IfxCcu60_CC60_P11_12_OUT
	.align	4
IfxCcu60_CC60_P11_12_OUT:	.type	object
	.size	IfxCcu60_CC60_P11_12_OUT,16
	.word	-268424704,-268193536
	.byte	12
	.space	3
	.byte	184
	.space	3
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu60_CC60_P15_6_OUT',data,rom,cluster('IfxCcu60_CC60_P15_6_OUT')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu60_CC60_P15_6_OUT'
	.global	IfxCcu60_CC60_P15_6_OUT
	.align	4
IfxCcu60_CC60_P15_6_OUT:	.type	object
	.size	IfxCcu60_CC60_P15_6_OUT,16
	.word	-268424704,-268192512
	.byte	6
	.space	3
	.byte	184
	.space	3
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu61_CC60_P00_1_OUT',data,rom,cluster('IfxCcu61_CC60_P00_1_OUT')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu61_CC60_P00_1_OUT'
	.global	IfxCcu61_CC60_P00_1_OUT
	.align	4
IfxCcu61_CC60_P00_1_OUT:	.type	object
	.size	IfxCcu61_CC60_P00_1_OUT,16
	.word	-268424448,-268197888
	.byte	1
	.space	3
	.byte	184
	.space	3
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu61_CC60_P00_7_OUT',data,rom,cluster('IfxCcu61_CC60_P00_7_OUT')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu61_CC60_P00_7_OUT'
	.global	IfxCcu61_CC60_P00_7_OUT
	.align	4
IfxCcu61_CC60_P00_7_OUT:	.type	object
	.size	IfxCcu61_CC60_P00_7_OUT,16
	.word	-268424448,-268197888
	.byte	7
	.space	3
	.byte	184
	.space	3
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu61_CC60_P20_8_OUT',data,rom,cluster('IfxCcu61_CC60_P20_8_OUT')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu61_CC60_P20_8_OUT'
	.global	IfxCcu61_CC60_P20_8_OUT
	.align	4
IfxCcu61_CC60_P20_8_OUT:	.type	object
	.size	IfxCcu61_CC60_P20_8_OUT,16
	.word	-268424448,-268189696
	.byte	8
	.space	3
	.byte	184
	.space	3
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu61_CC60_P33_13_OUT',data,rom,cluster('IfxCcu61_CC60_P33_13_OUT')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu61_CC60_P33_13_OUT'
	.global	IfxCcu61_CC60_P33_13_OUT
	.align	4
IfxCcu61_CC60_P33_13_OUT:	.type	object
	.size	IfxCcu61_CC60_P33_13_OUT,16
	.word	-268424448,-268184832
	.byte	13
	.space	3
	.byte	184
	.space	3
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu60_CC60INA_P02_0_IN',data,rom,cluster('IfxCcu60_CC60INA_P02_0_IN')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu60_CC60INA_P02_0_IN'
	.global	IfxCcu60_CC60INA_P02_0_IN
	.align	4
IfxCcu60_CC60INA_P02_0_IN:	.type	object
	.size	IfxCcu60_CC60INA_P02_0_IN,16
	.word	-268424704,-268197376
	.space	8
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu60_CC60INB_P00_1_IN',data,rom,cluster('IfxCcu60_CC60INB_P00_1_IN')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu60_CC60INB_P00_1_IN'
	.global	IfxCcu60_CC60INB_P00_1_IN
	.align	4
IfxCcu60_CC60INB_P00_1_IN:	.type	object
	.size	IfxCcu60_CC60INB_P00_1_IN,16
	.word	-268424704,-268197888
	.byte	1
	.space	3
	.byte	1
	.space	3
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu60_CC60INC_P02_6_IN',data,rom,cluster('IfxCcu60_CC60INC_P02_6_IN')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu60_CC60INC_P02_6_IN'
	.global	IfxCcu60_CC60INC_P02_6_IN
	.align	4
IfxCcu60_CC60INC_P02_6_IN:	.type	object
	.size	IfxCcu60_CC60INC_P02_6_IN,16
	.word	-268424704,-268197376
	.byte	6
	.space	3
	.byte	2
	.space	3
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu61_CC60INA_P00_1_IN',data,rom,cluster('IfxCcu61_CC60INA_P00_1_IN')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu61_CC60INA_P00_1_IN'
	.global	IfxCcu61_CC60INA_P00_1_IN
	.align	4
IfxCcu61_CC60INA_P00_1_IN:	.type	object
	.size	IfxCcu61_CC60INA_P00_1_IN,16
	.word	-268424448,-268197888
	.byte	1
	.space	7
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu61_CC60INB_P02_0_IN',data,rom,cluster('IfxCcu61_CC60INB_P02_0_IN')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu61_CC60INB_P02_0_IN'
	.global	IfxCcu61_CC60INB_P02_0_IN
	.align	4
IfxCcu61_CC60INB_P02_0_IN:	.type	object
	.size	IfxCcu61_CC60INB_P02_0_IN,16
	.word	-268424448,-268197376
	.space	4
	.byte	1
	.space	3
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu61_CC60INC_P00_7_IN',data,rom,cluster('IfxCcu61_CC60INC_P00_7_IN')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu61_CC60INC_P00_7_IN'
	.global	IfxCcu61_CC60INC_P00_7_IN
	.align	4
IfxCcu61_CC60INC_P00_7_IN:	.type	object
	.size	IfxCcu61_CC60INC_P00_7_IN,16
	.word	-268424448,-268197888
	.byte	7
	.space	3
	.byte	2
	.space	3
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu60_CC61_P02_2_OUT',data,rom,cluster('IfxCcu60_CC61_P02_2_OUT')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu60_CC61_P02_2_OUT'
	.global	IfxCcu60_CC61_P02_2_OUT
	.align	4
IfxCcu60_CC61_P02_2_OUT:	.type	object
	.size	IfxCcu60_CC61_P02_2_OUT,16
	.word	-268424704,-268197376
	.byte	2
	.space	3
	.byte	184
	.space	3
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu60_CC61_P02_7_OUT',data,rom,cluster('IfxCcu60_CC61_P02_7_OUT')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu60_CC61_P02_7_OUT'
	.global	IfxCcu60_CC61_P02_7_OUT
	.align	4
IfxCcu60_CC61_P02_7_OUT:	.type	object
	.size	IfxCcu60_CC61_P02_7_OUT,16
	.word	-268424704,-268197376
	.byte	7
	.space	3
	.byte	184
	.space	3
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu60_CC61_P11_11_OUT',data,rom,cluster('IfxCcu60_CC61_P11_11_OUT')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu60_CC61_P11_11_OUT'
	.global	IfxCcu60_CC61_P11_11_OUT
	.align	4
IfxCcu60_CC61_P11_11_OUT:	.type	object
	.size	IfxCcu60_CC61_P11_11_OUT,16
	.word	-268424704,-268193536
	.byte	11
	.space	3
	.byte	184
	.space	3
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu60_CC61_P15_5_OUT',data,rom,cluster('IfxCcu60_CC61_P15_5_OUT')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu60_CC61_P15_5_OUT'
	.global	IfxCcu60_CC61_P15_5_OUT
	.align	4
IfxCcu60_CC61_P15_5_OUT:	.type	object
	.size	IfxCcu60_CC61_P15_5_OUT,16
	.word	-268424704,-268192512
	.byte	5
	.space	3
	.byte	184
	.space	3
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu61_CC61_P00_3_OUT',data,rom,cluster('IfxCcu61_CC61_P00_3_OUT')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu61_CC61_P00_3_OUT'
	.global	IfxCcu61_CC61_P00_3_OUT
	.align	4
IfxCcu61_CC61_P00_3_OUT:	.type	object
	.size	IfxCcu61_CC61_P00_3_OUT,16
	.word	-268424448,-268197888
	.byte	3
	.space	3
	.byte	184
	.space	3
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu61_CC61_P00_8_OUT',data,rom,cluster('IfxCcu61_CC61_P00_8_OUT')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu61_CC61_P00_8_OUT'
	.global	IfxCcu61_CC61_P00_8_OUT
	.align	4
IfxCcu61_CC61_P00_8_OUT:	.type	object
	.size	IfxCcu61_CC61_P00_8_OUT,16
	.word	-268424448,-268197888
	.byte	8
	.space	3
	.byte	184
	.space	3
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu61_CC61_P20_9_OUT',data,rom,cluster('IfxCcu61_CC61_P20_9_OUT')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu61_CC61_P20_9_OUT'
	.global	IfxCcu61_CC61_P20_9_OUT
	.align	4
IfxCcu61_CC61_P20_9_OUT:	.type	object
	.size	IfxCcu61_CC61_P20_9_OUT,16
	.word	-268424448,-268189696
	.byte	9
	.space	3
	.byte	184
	.space	3
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu61_CC61_P33_11_OUT',data,rom,cluster('IfxCcu61_CC61_P33_11_OUT')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu61_CC61_P33_11_OUT'
	.global	IfxCcu61_CC61_P33_11_OUT
	.align	4
IfxCcu61_CC61_P33_11_OUT:	.type	object
	.size	IfxCcu61_CC61_P33_11_OUT,16
	.word	-268424448,-268184832
	.byte	11
	.space	3
	.byte	184
	.space	3
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu60_CC61INA_P02_2_IN',data,rom,cluster('IfxCcu60_CC61INA_P02_2_IN')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu60_CC61INA_P02_2_IN'
	.global	IfxCcu60_CC61INA_P02_2_IN
	.align	4
IfxCcu60_CC61INA_P02_2_IN:	.type	object
	.size	IfxCcu60_CC61INA_P02_2_IN,16
	.word	-268424704,-268197376
	.byte	2
	.space	7
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu60_CC61INB_P00_3_IN',data,rom,cluster('IfxCcu60_CC61INB_P00_3_IN')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu60_CC61INB_P00_3_IN'
	.global	IfxCcu60_CC61INB_P00_3_IN
	.align	4
IfxCcu60_CC61INB_P00_3_IN:	.type	object
	.size	IfxCcu60_CC61INB_P00_3_IN,16
	.word	-268424704,-268197888
	.byte	3
	.space	3
	.byte	1
	.space	3
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu60_CC61INC_P02_7_IN',data,rom,cluster('IfxCcu60_CC61INC_P02_7_IN')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu60_CC61INC_P02_7_IN'
	.global	IfxCcu60_CC61INC_P02_7_IN
	.align	4
IfxCcu60_CC61INC_P02_7_IN:	.type	object
	.size	IfxCcu60_CC61INC_P02_7_IN,16
	.word	-268424704,-268197376
	.byte	7
	.space	3
	.byte	2
	.space	3
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu61_CC61INA_P00_3_IN',data,rom,cluster('IfxCcu61_CC61INA_P00_3_IN')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu61_CC61INA_P00_3_IN'
	.global	IfxCcu61_CC61INA_P00_3_IN
	.align	4
IfxCcu61_CC61INA_P00_3_IN:	.type	object
	.size	IfxCcu61_CC61INA_P00_3_IN,16
	.word	-268424448,-268197888
	.byte	3
	.space	7
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu61_CC61INB_P02_2_IN',data,rom,cluster('IfxCcu61_CC61INB_P02_2_IN')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu61_CC61INB_P02_2_IN'
	.global	IfxCcu61_CC61INB_P02_2_IN
	.align	4
IfxCcu61_CC61INB_P02_2_IN:	.type	object
	.size	IfxCcu61_CC61INB_P02_2_IN,16
	.word	-268424448,-268197376
	.byte	2
	.space	3
	.byte	1
	.space	3
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu61_CC61INC_P00_8_IN',data,rom,cluster('IfxCcu61_CC61INC_P00_8_IN')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu61_CC61INC_P00_8_IN'
	.global	IfxCcu61_CC61INC_P00_8_IN
	.align	4
IfxCcu61_CC61INC_P00_8_IN:	.type	object
	.size	IfxCcu61_CC61INC_P00_8_IN,16
	.word	-268424448,-268197888
	.byte	8
	.space	3
	.byte	2
	.space	3
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu60_CC62_P02_4_OUT',data,rom,cluster('IfxCcu60_CC62_P02_4_OUT')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu60_CC62_P02_4_OUT'
	.global	IfxCcu60_CC62_P02_4_OUT
	.align	4
IfxCcu60_CC62_P02_4_OUT:	.type	object
	.size	IfxCcu60_CC62_P02_4_OUT,16
	.word	-268424704,-268197376
	.byte	4
	.space	3
	.byte	184
	.space	3
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu60_CC62_P02_8_OUT',data,rom,cluster('IfxCcu60_CC62_P02_8_OUT')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu60_CC62_P02_8_OUT'
	.global	IfxCcu60_CC62_P02_8_OUT
	.align	4
IfxCcu60_CC62_P02_8_OUT:	.type	object
	.size	IfxCcu60_CC62_P02_8_OUT,16
	.word	-268424704,-268197376
	.byte	8
	.space	3
	.byte	184
	.space	3
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu60_CC62_P11_10_OUT',data,rom,cluster('IfxCcu60_CC62_P11_10_OUT')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu60_CC62_P11_10_OUT'
	.global	IfxCcu60_CC62_P11_10_OUT
	.align	4
IfxCcu60_CC62_P11_10_OUT:	.type	object
	.size	IfxCcu60_CC62_P11_10_OUT,16
	.word	-268424704,-268193536
	.byte	10
	.space	3
	.byte	184
	.space	3
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu60_CC62_P15_4_OUT',data,rom,cluster('IfxCcu60_CC62_P15_4_OUT')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu60_CC62_P15_4_OUT'
	.global	IfxCcu60_CC62_P15_4_OUT
	.align	4
IfxCcu60_CC62_P15_4_OUT:	.type	object
	.size	IfxCcu60_CC62_P15_4_OUT,16
	.word	-268424704,-268192512
	.byte	4
	.space	3
	.byte	184
	.space	3
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu61_CC62_P00_5_OUT',data,rom,cluster('IfxCcu61_CC62_P00_5_OUT')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu61_CC62_P00_5_OUT'
	.global	IfxCcu61_CC62_P00_5_OUT
	.align	4
IfxCcu61_CC62_P00_5_OUT:	.type	object
	.size	IfxCcu61_CC62_P00_5_OUT,16
	.word	-268424448,-268197888
	.byte	5
	.space	3
	.byte	184
	.space	3
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu61_CC62_P00_9_OUT',data,rom,cluster('IfxCcu61_CC62_P00_9_OUT')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu61_CC62_P00_9_OUT'
	.global	IfxCcu61_CC62_P00_9_OUT
	.align	4
IfxCcu61_CC62_P00_9_OUT:	.type	object
	.size	IfxCcu61_CC62_P00_9_OUT,16
	.word	-268424448,-268197888
	.byte	9
	.space	3
	.byte	184
	.space	3
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu61_CC62_P20_10_OUT',data,rom,cluster('IfxCcu61_CC62_P20_10_OUT')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu61_CC62_P20_10_OUT'
	.global	IfxCcu61_CC62_P20_10_OUT
	.align	4
IfxCcu61_CC62_P20_10_OUT:	.type	object
	.size	IfxCcu61_CC62_P20_10_OUT,16
	.word	-268424448,-268189696
	.byte	10
	.space	3
	.byte	184
	.space	3
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu61_CC62_P33_9_OUT',data,rom,cluster('IfxCcu61_CC62_P33_9_OUT')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu61_CC62_P33_9_OUT'
	.global	IfxCcu61_CC62_P33_9_OUT
	.align	4
IfxCcu61_CC62_P33_9_OUT:	.type	object
	.size	IfxCcu61_CC62_P33_9_OUT,16
	.word	-268424448,-268184832
	.byte	9
	.space	3
	.byte	184
	.space	3
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu60_CC62INA_P02_4_IN',data,rom,cluster('IfxCcu60_CC62INA_P02_4_IN')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu60_CC62INA_P02_4_IN'
	.global	IfxCcu60_CC62INA_P02_4_IN
	.align	4
IfxCcu60_CC62INA_P02_4_IN:	.type	object
	.size	IfxCcu60_CC62INA_P02_4_IN,16
	.word	-268424704,-268197376
	.byte	4
	.space	7
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu60_CC62INB_P00_5_IN',data,rom,cluster('IfxCcu60_CC62INB_P00_5_IN')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu60_CC62INB_P00_5_IN'
	.global	IfxCcu60_CC62INB_P00_5_IN
	.align	4
IfxCcu60_CC62INB_P00_5_IN:	.type	object
	.size	IfxCcu60_CC62INB_P00_5_IN,16
	.word	-268424704,-268197888
	.byte	5
	.space	3
	.byte	1
	.space	3
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu60_CC62INC_P02_8_IN',data,rom,cluster('IfxCcu60_CC62INC_P02_8_IN')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu60_CC62INC_P02_8_IN'
	.global	IfxCcu60_CC62INC_P02_8_IN
	.align	4
IfxCcu60_CC62INC_P02_8_IN:	.type	object
	.size	IfxCcu60_CC62INC_P02_8_IN,16
	.word	-268424704,-268197376
	.byte	8
	.space	3
	.byte	2
	.space	3
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu61_CC62INA_P00_5_IN',data,rom,cluster('IfxCcu61_CC62INA_P00_5_IN')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu61_CC62INA_P00_5_IN'
	.global	IfxCcu61_CC62INA_P00_5_IN
	.align	4
IfxCcu61_CC62INA_P00_5_IN:	.type	object
	.size	IfxCcu61_CC62INA_P00_5_IN,16
	.word	-268424448,-268197888
	.byte	5
	.space	7
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu61_CC62INB_P02_4_IN',data,rom,cluster('IfxCcu61_CC62INB_P02_4_IN')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu61_CC62INB_P02_4_IN'
	.global	IfxCcu61_CC62INB_P02_4_IN
	.align	4
IfxCcu61_CC62INB_P02_4_IN:	.type	object
	.size	IfxCcu61_CC62INB_P02_4_IN,16
	.word	-268424448,-268197376
	.byte	4
	.space	3
	.byte	1
	.space	3
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu61_CC62INC_P00_9_IN',data,rom,cluster('IfxCcu61_CC62INC_P00_9_IN')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu61_CC62INC_P00_9_IN'
	.global	IfxCcu61_CC62INC_P00_9_IN
	.align	4
IfxCcu61_CC62INC_P00_9_IN:	.type	object
	.size	IfxCcu61_CC62INC_P00_9_IN,16
	.word	-268424448,-268197888
	.byte	9
	.space	3
	.byte	2
	.space	3
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu60_CCPOS0A_P02_6_IN',data,rom,cluster('IfxCcu60_CCPOS0A_P02_6_IN')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu60_CCPOS0A_P02_6_IN'
	.global	IfxCcu60_CCPOS0A_P02_6_IN
	.align	4
IfxCcu60_CCPOS0A_P02_6_IN:	.type	object
	.size	IfxCcu60_CCPOS0A_P02_6_IN,16
	.word	-268424704,-268197376
	.byte	6
	.space	7
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu60_CCPOS0C_P10_4_IN',data,rom,cluster('IfxCcu60_CCPOS0C_P10_4_IN')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu60_CCPOS0C_P10_4_IN'
	.global	IfxCcu60_CCPOS0C_P10_4_IN
	.align	4
IfxCcu60_CCPOS0C_P10_4_IN:	.type	object
	.size	IfxCcu60_CCPOS0C_P10_4_IN,16
	.word	-268424704,-268193792
	.byte	4
	.space	3
	.byte	2
	.space	3
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu60_CCPOS0D_P40_0_IN',data,rom,cluster('IfxCcu60_CCPOS0D_P40_0_IN')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu60_CCPOS0D_P40_0_IN'
	.global	IfxCcu60_CCPOS0D_P40_0_IN
	.align	4
IfxCcu60_CCPOS0D_P40_0_IN:	.type	object
	.size	IfxCcu60_CCPOS0D_P40_0_IN,16
	.word	-268424704,-268181504
	.space	4
	.byte	3
	.space	3
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu61_CCPOS0A_P00_7_IN',data,rom,cluster('IfxCcu61_CCPOS0A_P00_7_IN')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu61_CCPOS0A_P00_7_IN'
	.global	IfxCcu61_CCPOS0A_P00_7_IN
	.align	4
IfxCcu61_CCPOS0A_P00_7_IN:	.type	object
	.size	IfxCcu61_CCPOS0A_P00_7_IN,16
	.word	-268424448,-268197888
	.byte	7
	.space	7
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu61_CCPOS0C_P33_7_IN',data,rom,cluster('IfxCcu61_CCPOS0C_P33_7_IN')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu61_CCPOS0C_P33_7_IN'
	.global	IfxCcu61_CCPOS0C_P33_7_IN
	.align	4
IfxCcu61_CCPOS0C_P33_7_IN:	.type	object
	.size	IfxCcu61_CCPOS0C_P33_7_IN,16
	.word	-268424448,-268184832
	.byte	7
	.space	3
	.byte	2
	.space	3
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu60_CCPOS1A_P02_7_IN',data,rom,cluster('IfxCcu60_CCPOS1A_P02_7_IN')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu60_CCPOS1A_P02_7_IN'
	.global	IfxCcu60_CCPOS1A_P02_7_IN
	.align	4
IfxCcu60_CCPOS1A_P02_7_IN:	.type	object
	.size	IfxCcu60_CCPOS1A_P02_7_IN,16
	.word	-268424704,-268197376
	.byte	7
	.space	7
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu60_CCPOS1B_P40_1_IN',data,rom,cluster('IfxCcu60_CCPOS1B_P40_1_IN')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu60_CCPOS1B_P40_1_IN'
	.global	IfxCcu60_CCPOS1B_P40_1_IN
	.align	4
IfxCcu60_CCPOS1B_P40_1_IN:	.type	object
	.size	IfxCcu60_CCPOS1B_P40_1_IN,16
	.word	-268424704,-268181504
	.byte	1
	.space	3
	.byte	1
	.space	3
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu60_CCPOS1C_P10_7_IN',data,rom,cluster('IfxCcu60_CCPOS1C_P10_7_IN')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu60_CCPOS1C_P10_7_IN'
	.global	IfxCcu60_CCPOS1C_P10_7_IN
	.align	4
IfxCcu60_CCPOS1C_P10_7_IN:	.type	object
	.size	IfxCcu60_CCPOS1C_P10_7_IN,16
	.word	-268424704,-268193792
	.byte	7
	.space	3
	.byte	2
	.space	3
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu60_CCPOS1D_P40_2_IN',data,rom,cluster('IfxCcu60_CCPOS1D_P40_2_IN')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu60_CCPOS1D_P40_2_IN'
	.global	IfxCcu60_CCPOS1D_P40_2_IN
	.align	4
IfxCcu60_CCPOS1D_P40_2_IN:	.type	object
	.size	IfxCcu60_CCPOS1D_P40_2_IN,16
	.word	-268424704,-268181504
	.byte	2
	.space	3
	.byte	3
	.space	3
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu61_CCPOS1A_P00_8_IN',data,rom,cluster('IfxCcu61_CCPOS1A_P00_8_IN')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu61_CCPOS1A_P00_8_IN'
	.global	IfxCcu61_CCPOS1A_P00_8_IN
	.align	4
IfxCcu61_CCPOS1A_P00_8_IN:	.type	object
	.size	IfxCcu61_CCPOS1A_P00_8_IN,16
	.word	-268424448,-268197888
	.byte	8
	.space	7
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu61_CCPOS1B_P40_6_IN',data,rom,cluster('IfxCcu61_CCPOS1B_P40_6_IN')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu61_CCPOS1B_P40_6_IN'
	.global	IfxCcu61_CCPOS1B_P40_6_IN
	.align	4
IfxCcu61_CCPOS1B_P40_6_IN:	.type	object
	.size	IfxCcu61_CCPOS1B_P40_6_IN,16
	.word	-268424448,-268181504
	.byte	6
	.space	3
	.byte	1
	.space	3
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu61_CCPOS1C_P33_6_IN',data,rom,cluster('IfxCcu61_CCPOS1C_P33_6_IN')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu61_CCPOS1C_P33_6_IN'
	.global	IfxCcu61_CCPOS1C_P33_6_IN
	.align	4
IfxCcu61_CCPOS1C_P33_6_IN:	.type	object
	.size	IfxCcu61_CCPOS1C_P33_6_IN,16
	.word	-268424448,-268184832
	.byte	6
	.space	3
	.byte	2
	.space	3
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu61_CCPOS1D_P40_7_IN',data,rom,cluster('IfxCcu61_CCPOS1D_P40_7_IN')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu61_CCPOS1D_P40_7_IN'
	.global	IfxCcu61_CCPOS1D_P40_7_IN
	.align	4
IfxCcu61_CCPOS1D_P40_7_IN:	.type	object
	.size	IfxCcu61_CCPOS1D_P40_7_IN,16
	.word	-268424448,-268181504
	.byte	7
	.space	3
	.byte	3
	.space	3
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu60_CCPOS2A_P02_8_IN',data,rom,cluster('IfxCcu60_CCPOS2A_P02_8_IN')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu60_CCPOS2A_P02_8_IN'
	.global	IfxCcu60_CCPOS2A_P02_8_IN
	.align	4
IfxCcu60_CCPOS2A_P02_8_IN:	.type	object
	.size	IfxCcu60_CCPOS2A_P02_8_IN,16
	.word	-268424704,-268197376
	.byte	8
	.space	7
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu60_CCPOS2B_P40_3_IN',data,rom,cluster('IfxCcu60_CCPOS2B_P40_3_IN')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu60_CCPOS2B_P40_3_IN'
	.global	IfxCcu60_CCPOS2B_P40_3_IN
	.align	4
IfxCcu60_CCPOS2B_P40_3_IN:	.type	object
	.size	IfxCcu60_CCPOS2B_P40_3_IN,16
	.word	-268424704,-268181504
	.byte	3
	.space	3
	.byte	1
	.space	3
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu60_CCPOS2C_P10_8_IN',data,rom,cluster('IfxCcu60_CCPOS2C_P10_8_IN')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu60_CCPOS2C_P10_8_IN'
	.global	IfxCcu60_CCPOS2C_P10_8_IN
	.align	4
IfxCcu60_CCPOS2C_P10_8_IN:	.type	object
	.size	IfxCcu60_CCPOS2C_P10_8_IN,16
	.word	-268424704,-268193792
	.byte	8
	.space	3
	.byte	2
	.space	3
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu61_CCPOS2A_P00_9_IN',data,rom,cluster('IfxCcu61_CCPOS2A_P00_9_IN')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu61_CCPOS2A_P00_9_IN'
	.global	IfxCcu61_CCPOS2A_P00_9_IN
	.align	4
IfxCcu61_CCPOS2A_P00_9_IN:	.type	object
	.size	IfxCcu61_CCPOS2A_P00_9_IN,16
	.word	-268424448,-268197888
	.byte	9
	.space	7
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu61_CCPOS2B_P40_8_IN',data,rom,cluster('IfxCcu61_CCPOS2B_P40_8_IN')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu61_CCPOS2B_P40_8_IN'
	.global	IfxCcu61_CCPOS2B_P40_8_IN
	.align	4
IfxCcu61_CCPOS2B_P40_8_IN:	.type	object
	.size	IfxCcu61_CCPOS2B_P40_8_IN,16
	.word	-268424448,-268181504
	.byte	8
	.space	3
	.byte	1
	.space	3
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu61_CCPOS2C_P33_5_IN',data,rom,cluster('IfxCcu61_CCPOS2C_P33_5_IN')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu61_CCPOS2C_P33_5_IN'
	.global	IfxCcu61_CCPOS2C_P33_5_IN
	.align	4
IfxCcu61_CCPOS2C_P33_5_IN:	.type	object
	.size	IfxCcu61_CCPOS2C_P33_5_IN,16
	.word	-268424448,-268184832
	.byte	5
	.space	3
	.byte	2
	.space	3
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu61_CCPOS2D_P40_9_IN',data,rom,cluster('IfxCcu61_CCPOS2D_P40_9_IN')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu61_CCPOS2D_P40_9_IN'
	.global	IfxCcu61_CCPOS2D_P40_9_IN
	.align	4
IfxCcu61_CCPOS2D_P40_9_IN:	.type	object
	.size	IfxCcu61_CCPOS2D_P40_9_IN,16
	.word	-268424448,-268181504
	.byte	9
	.space	3
	.byte	3
	.space	3
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu60_COUT60_P02_1_OUT',data,rom,cluster('IfxCcu60_COUT60_P02_1_OUT')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu60_COUT60_P02_1_OUT'
	.global	IfxCcu60_COUT60_P02_1_OUT
	.align	4
IfxCcu60_COUT60_P02_1_OUT:	.type	object
	.size	IfxCcu60_COUT60_P02_1_OUT,16
	.word	-268424704,-268197376
	.byte	1
	.space	3
	.byte	184
	.space	3
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu60_COUT60_P11_9_OUT',data,rom,cluster('IfxCcu60_COUT60_P11_9_OUT')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu60_COUT60_P11_9_OUT'
	.global	IfxCcu60_COUT60_P11_9_OUT
	.align	4
IfxCcu60_COUT60_P11_9_OUT:	.type	object
	.size	IfxCcu60_COUT60_P11_9_OUT,16
	.word	-268424704,-268193536
	.byte	9
	.space	3
	.byte	184
	.space	3
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu60_COUT60_P15_7_OUT',data,rom,cluster('IfxCcu60_COUT60_P15_7_OUT')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu60_COUT60_P15_7_OUT'
	.global	IfxCcu60_COUT60_P15_7_OUT
	.align	4
IfxCcu60_COUT60_P15_7_OUT:	.type	object
	.size	IfxCcu60_COUT60_P15_7_OUT,16
	.word	-268424704,-268192512
	.byte	7
	.space	3
	.byte	184
	.space	3
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu61_COUT60_P00_2_OUT',data,rom,cluster('IfxCcu61_COUT60_P00_2_OUT')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu61_COUT60_P00_2_OUT'
	.global	IfxCcu61_COUT60_P00_2_OUT
	.align	4
IfxCcu61_COUT60_P00_2_OUT:	.type	object
	.size	IfxCcu61_COUT60_P00_2_OUT,16
	.word	-268424448,-268197888
	.byte	2
	.space	3
	.byte	184
	.space	3
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu61_COUT60_P20_11_OUT',data,rom,cluster('IfxCcu61_COUT60_P20_11_OUT')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu61_COUT60_P20_11_OUT'
	.global	IfxCcu61_COUT60_P20_11_OUT
	.align	4
IfxCcu61_COUT60_P20_11_OUT:	.type	object
	.size	IfxCcu61_COUT60_P20_11_OUT,16
	.word	-268424448,-268189696
	.byte	11
	.space	3
	.byte	184
	.space	3
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu61_COUT60_P33_12_OUT',data,rom,cluster('IfxCcu61_COUT60_P33_12_OUT')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu61_COUT60_P33_12_OUT'
	.global	IfxCcu61_COUT60_P33_12_OUT
	.align	4
IfxCcu61_COUT60_P33_12_OUT:	.type	object
	.size	IfxCcu61_COUT60_P33_12_OUT,16
	.word	-268424448,-268184832
	.byte	12
	.space	3
	.byte	184
	.space	3
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu60_COUT61_P02_3_OUT',data,rom,cluster('IfxCcu60_COUT61_P02_3_OUT')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu60_COUT61_P02_3_OUT'
	.global	IfxCcu60_COUT61_P02_3_OUT
	.align	4
IfxCcu60_COUT61_P02_3_OUT:	.type	object
	.size	IfxCcu60_COUT61_P02_3_OUT,16
	.word	-268424704,-268197376
	.byte	3
	.space	3
	.byte	184
	.space	3
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu60_COUT61_P11_6_OUT',data,rom,cluster('IfxCcu60_COUT61_P11_6_OUT')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu60_COUT61_P11_6_OUT'
	.global	IfxCcu60_COUT61_P11_6_OUT
	.align	4
IfxCcu60_COUT61_P11_6_OUT:	.type	object
	.size	IfxCcu60_COUT61_P11_6_OUT,16
	.word	-268424704,-268193536
	.byte	6
	.space	3
	.byte	184
	.space	3
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu60_COUT61_P15_8_OUT',data,rom,cluster('IfxCcu60_COUT61_P15_8_OUT')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu60_COUT61_P15_8_OUT'
	.global	IfxCcu60_COUT61_P15_8_OUT
	.align	4
IfxCcu60_COUT61_P15_8_OUT:	.type	object
	.size	IfxCcu60_COUT61_P15_8_OUT,16
	.word	-268424704,-268192512
	.byte	8
	.space	3
	.byte	184
	.space	3
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu61_COUT61_P00_4_OUT',data,rom,cluster('IfxCcu61_COUT61_P00_4_OUT')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu61_COUT61_P00_4_OUT'
	.global	IfxCcu61_COUT61_P00_4_OUT
	.align	4
IfxCcu61_COUT61_P00_4_OUT:	.type	object
	.size	IfxCcu61_COUT61_P00_4_OUT,16
	.word	-268424448,-268197888
	.byte	4
	.space	3
	.byte	184
	.space	3
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu61_COUT61_P20_12_OUT',data,rom,cluster('IfxCcu61_COUT61_P20_12_OUT')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu61_COUT61_P20_12_OUT'
	.global	IfxCcu61_COUT61_P20_12_OUT
	.align	4
IfxCcu61_COUT61_P20_12_OUT:	.type	object
	.size	IfxCcu61_COUT61_P20_12_OUT,16
	.word	-268424448,-268189696
	.byte	12
	.space	3
	.byte	184
	.space	3
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu61_COUT61_P33_10_OUT',data,rom,cluster('IfxCcu61_COUT61_P33_10_OUT')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu61_COUT61_P33_10_OUT'
	.global	IfxCcu61_COUT61_P33_10_OUT
	.align	4
IfxCcu61_COUT61_P33_10_OUT:	.type	object
	.size	IfxCcu61_COUT61_P33_10_OUT,16
	.word	-268424448,-268184832
	.byte	10
	.space	3
	.byte	184
	.space	3
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu60_COUT62_P02_5_OUT',data,rom,cluster('IfxCcu60_COUT62_P02_5_OUT')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu60_COUT62_P02_5_OUT'
	.global	IfxCcu60_COUT62_P02_5_OUT
	.align	4
IfxCcu60_COUT62_P02_5_OUT:	.type	object
	.size	IfxCcu60_COUT62_P02_5_OUT,16
	.word	-268424704,-268197376
	.byte	5
	.space	3
	.byte	184
	.space	3
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu60_COUT62_P11_3_OUT',data,rom,cluster('IfxCcu60_COUT62_P11_3_OUT')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu60_COUT62_P11_3_OUT'
	.global	IfxCcu60_COUT62_P11_3_OUT
	.align	4
IfxCcu60_COUT62_P11_3_OUT:	.type	object
	.size	IfxCcu60_COUT62_P11_3_OUT,16
	.word	-268424704,-268193536
	.byte	3
	.space	3
	.byte	184
	.space	3
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu60_COUT62_P14_0_OUT',data,rom,cluster('IfxCcu60_COUT62_P14_0_OUT')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu60_COUT62_P14_0_OUT'
	.global	IfxCcu60_COUT62_P14_0_OUT
	.align	4
IfxCcu60_COUT62_P14_0_OUT:	.type	object
	.size	IfxCcu60_COUT62_P14_0_OUT,16
	.word	-268424704,-268192768
	.space	4
	.byte	184
	.space	3
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu61_COUT62_P00_6_OUT',data,rom,cluster('IfxCcu61_COUT62_P00_6_OUT')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu61_COUT62_P00_6_OUT'
	.global	IfxCcu61_COUT62_P00_6_OUT
	.align	4
IfxCcu61_COUT62_P00_6_OUT:	.type	object
	.size	IfxCcu61_COUT62_P00_6_OUT,16
	.word	-268424448,-268197888
	.byte	6
	.space	3
	.byte	184
	.space	3
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu61_COUT62_P20_13_OUT',data,rom,cluster('IfxCcu61_COUT62_P20_13_OUT')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu61_COUT62_P20_13_OUT'
	.global	IfxCcu61_COUT62_P20_13_OUT
	.align	4
IfxCcu61_COUT62_P20_13_OUT:	.type	object
	.size	IfxCcu61_COUT62_P20_13_OUT,16
	.word	-268424448,-268189696
	.byte	13
	.space	3
	.byte	184
	.space	3
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu61_COUT62_P33_8_OUT',data,rom,cluster('IfxCcu61_COUT62_P33_8_OUT')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu61_COUT62_P33_8_OUT'
	.global	IfxCcu61_COUT62_P33_8_OUT
	.align	4
IfxCcu61_COUT62_P33_8_OUT:	.type	object
	.size	IfxCcu61_COUT62_P33_8_OUT,16
	.word	-268424448,-268184832
	.byte	8
	.space	3
	.byte	184
	.space	3
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu60_COUT63_P00_0_OUT',data,rom,cluster('IfxCcu60_COUT63_P00_0_OUT')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu60_COUT63_P00_0_OUT'
	.global	IfxCcu60_COUT63_P00_0_OUT
	.align	4
IfxCcu60_COUT63_P00_0_OUT:	.type	object
	.size	IfxCcu60_COUT63_P00_0_OUT,16
	.word	-268424704,-268197888
	.space	4
	.byte	184
	.space	3
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu60_COUT63_P11_2_OUT',data,rom,cluster('IfxCcu60_COUT63_P11_2_OUT')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu60_COUT63_P11_2_OUT'
	.global	IfxCcu60_COUT63_P11_2_OUT
	.align	4
IfxCcu60_COUT63_P11_2_OUT:	.type	object
	.size	IfxCcu60_COUT63_P11_2_OUT,16
	.word	-268424704,-268193536
	.byte	2
	.space	3
	.byte	184
	.space	3
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu60_COUT63_P14_1_OUT',data,rom,cluster('IfxCcu60_COUT63_P14_1_OUT')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu60_COUT63_P14_1_OUT'
	.global	IfxCcu60_COUT63_P14_1_OUT
	.align	4
IfxCcu60_COUT63_P14_1_OUT:	.type	object
	.size	IfxCcu60_COUT63_P14_1_OUT,16
	.word	-268424704,-268192768
	.byte	1
	.space	3
	.byte	184
	.space	3
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu60_COUT63_P32_4_OUT',data,rom,cluster('IfxCcu60_COUT63_P32_4_OUT')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu60_COUT63_P32_4_OUT'
	.global	IfxCcu60_COUT63_P32_4_OUT
	.align	4
IfxCcu60_COUT63_P32_4_OUT:	.type	object
	.size	IfxCcu60_COUT63_P32_4_OUT,16
	.word	-268424704,-268185088
	.byte	4
	.space	3
	.byte	184
	.space	3
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu61_COUT63_P00_10_OUT',data,rom,cluster('IfxCcu61_COUT63_P00_10_OUT')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu61_COUT63_P00_10_OUT'
	.global	IfxCcu61_COUT63_P00_10_OUT
	.align	4
IfxCcu61_COUT63_P00_10_OUT:	.type	object
	.size	IfxCcu61_COUT63_P00_10_OUT,16
	.word	-268424448,-268197888
	.byte	10
	.space	3
	.byte	184
	.space	3
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu61_COUT63_P00_12_OUT',data,rom,cluster('IfxCcu61_COUT63_P00_12_OUT')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu61_COUT63_P00_12_OUT'
	.global	IfxCcu61_COUT63_P00_12_OUT
	.align	4
IfxCcu61_COUT63_P00_12_OUT:	.type	object
	.size	IfxCcu61_COUT63_P00_12_OUT,16
	.word	-268424448,-268197888
	.byte	12
	.space	3
	.byte	184
	.space	3
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu61_COUT63_P20_7_OUT',data,rom,cluster('IfxCcu61_COUT63_P20_7_OUT')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu61_COUT63_P20_7_OUT'
	.global	IfxCcu61_COUT63_P20_7_OUT
	.align	4
IfxCcu61_COUT63_P20_7_OUT:	.type	object
	.size	IfxCcu61_COUT63_P20_7_OUT,16
	.word	-268424448,-268189696
	.byte	7
	.space	3
	.byte	184
	.space	3
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu60_CTRAPA_P00_11_IN',data,rom,cluster('IfxCcu60_CTRAPA_P00_11_IN')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu60_CTRAPA_P00_11_IN'
	.global	IfxCcu60_CTRAPA_P00_11_IN
	.align	4
IfxCcu60_CTRAPA_P00_11_IN:	.type	object
	.size	IfxCcu60_CTRAPA_P00_11_IN,16
	.word	-268424704,-268197888
	.byte	11
	.space	7
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu61_CTRAPA_P00_0_IN',data,rom,cluster('IfxCcu61_CTRAPA_P00_0_IN')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu61_CTRAPA_P00_0_IN'
	.global	IfxCcu61_CTRAPA_P00_0_IN
	.align	4
IfxCcu61_CTRAPA_P00_0_IN:	.type	object
	.size	IfxCcu61_CTRAPA_P00_0_IN,16
	.word	-268424448,-268197888
	.space	8
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu61_CTRAPC_P33_4_IN',data,rom,cluster('IfxCcu61_CTRAPC_P33_4_IN')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu61_CTRAPC_P33_4_IN'
	.global	IfxCcu61_CTRAPC_P33_4_IN
	.align	4
IfxCcu61_CTRAPC_P33_4_IN:	.type	object
	.size	IfxCcu61_CTRAPC_P33_4_IN,16
	.word	-268424448,-268184832
	.byte	4
	.space	3
	.byte	2
	.space	3
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu60_T12HRB_P00_7_IN',data,rom,cluster('IfxCcu60_T12HRB_P00_7_IN')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu60_T12HRB_P00_7_IN'
	.global	IfxCcu60_T12HRB_P00_7_IN
	.align	4
IfxCcu60_T12HRB_P00_7_IN:	.type	object
	.size	IfxCcu60_T12HRB_P00_7_IN,16
	.word	-268424704,-268197888
	.byte	7
	.space	3
	.byte	1
	.space	3
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu60_T12HRC_P00_9_IN',data,rom,cluster('IfxCcu60_T12HRC_P00_9_IN')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu60_T12HRC_P00_9_IN'
	.global	IfxCcu60_T12HRC_P00_9_IN
	.align	4
IfxCcu60_T12HRC_P00_9_IN:	.type	object
	.size	IfxCcu60_T12HRC_P00_9_IN,16
	.word	-268424704,-268197888
	.byte	9
	.space	3
	.byte	2
	.space	3
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu60_T12HRE_P00_0_IN',data,rom,cluster('IfxCcu60_T12HRE_P00_0_IN')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu60_T12HRE_P00_0_IN'
	.global	IfxCcu60_T12HRE_P00_0_IN
	.align	4
IfxCcu60_T12HRE_P00_0_IN:	.type	object
	.size	IfxCcu60_T12HRE_P00_0_IN,16
	.word	-268424704,-268197888
	.space	4
	.byte	4
	.space	3
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu61_T12HRB_P02_6_IN',data,rom,cluster('IfxCcu61_T12HRB_P02_6_IN')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu61_T12HRB_P02_6_IN'
	.global	IfxCcu61_T12HRB_P02_6_IN
	.align	4
IfxCcu61_T12HRB_P02_6_IN:	.type	object
	.size	IfxCcu61_T12HRB_P02_6_IN,16
	.word	-268424448,-268197376
	.byte	6
	.space	3
	.byte	1
	.space	3
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu61_T12HRC_P02_8_IN',data,rom,cluster('IfxCcu61_T12HRC_P02_8_IN')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu61_T12HRC_P02_8_IN'
	.global	IfxCcu61_T12HRC_P02_8_IN
	.align	4
IfxCcu61_T12HRC_P02_8_IN:	.type	object
	.size	IfxCcu61_T12HRC_P02_8_IN,16
	.word	-268424448,-268197376
	.byte	8
	.space	3
	.byte	2
	.space	3
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu61_T12HRE_P00_11_IN',data,rom,cluster('IfxCcu61_T12HRE_P00_11_IN')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu61_T12HRE_P00_11_IN'
	.global	IfxCcu61_T12HRE_P00_11_IN
	.align	4
IfxCcu61_T12HRE_P00_11_IN:	.type	object
	.size	IfxCcu61_T12HRE_P00_11_IN,16
	.word	-268424448,-268197888
	.byte	11
	.space	3
	.byte	4
	.space	3
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu60_T13HRB_P00_8_IN',data,rom,cluster('IfxCcu60_T13HRB_P00_8_IN')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu60_T13HRB_P00_8_IN'
	.global	IfxCcu60_T13HRB_P00_8_IN
	.align	4
IfxCcu60_T13HRB_P00_8_IN:	.type	object
	.size	IfxCcu60_T13HRB_P00_8_IN,16
	.word	-268424704,-268197888
	.byte	8
	.space	3
	.byte	1
	.space	3
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu60_T13HRC_P00_9_IN',data,rom,cluster('IfxCcu60_T13HRC_P00_9_IN')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu60_T13HRC_P00_9_IN'
	.global	IfxCcu60_T13HRC_P00_9_IN
	.align	4
IfxCcu60_T13HRC_P00_9_IN:	.type	object
	.size	IfxCcu60_T13HRC_P00_9_IN,16
	.word	-268424704,-268197888
	.byte	9
	.space	3
	.byte	2
	.space	3
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu61_T13HRB_P02_7_IN',data,rom,cluster('IfxCcu61_T13HRB_P02_7_IN')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu61_T13HRB_P02_7_IN'
	.global	IfxCcu61_T13HRB_P02_7_IN
	.align	4
IfxCcu61_T13HRB_P02_7_IN:	.type	object
	.size	IfxCcu61_T13HRB_P02_7_IN,16
	.word	-268424448,-268197376
	.byte	7
	.space	3
	.byte	1
	.space	3
	.sdecl	'.rodata.IfxCcu6_PinMap.IfxCcu61_T13HRC_P02_8_IN',data,rom,cluster('IfxCcu61_T13HRC_P02_8_IN')
	.sect	'.rodata.IfxCcu6_PinMap.IfxCcu61_T13HRC_P02_8_IN'
	.global	IfxCcu61_T13HRC_P02_8_IN
	.align	4
IfxCcu61_T13HRC_P02_8_IN:	.type	object
	.size	IfxCcu61_T13HRC_P02_8_IN,16
	.word	-268424448,-268197376
	.byte	8
	.space	3
	.byte	2
	.space	3
	.sdecl	'.data.IfxCcu6_PinMap.IfxCcu6_Cc60_Out_pinTable',data,cluster('IfxCcu6_Cc60_Out_pinTable')
	.sect	'.data.IfxCcu6_PinMap.IfxCcu6_Cc60_Out_pinTable'
	.global	IfxCcu6_Cc60_Out_pinTable
	.align	4
IfxCcu6_Cc60_Out_pinTable:	.type	object
	.size	IfxCcu6_Cc60_Out_pinTable,32
	.word	IfxCcu60_CC60_P02_0_OUT,IfxCcu60_CC60_P02_6_OUT,IfxCcu60_CC60_P11_12_OUT,IfxCcu60_CC60_P15_6_OUT,IfxCcu61_CC60_P00_1_OUT,IfxCcu61_CC60_P00_7_OUT,IfxCcu61_CC60_P20_8_OUT,IfxCcu61_CC60_P33_13_OUT
	.sdecl	'.data.IfxCcu6_PinMap.IfxCcu6_Cc60in_In_pinTable',data,cluster('IfxCcu6_Cc60in_In_pinTable')
	.sect	'.data.IfxCcu6_PinMap.IfxCcu6_Cc60in_In_pinTable'
	.global	IfxCcu6_Cc60in_In_pinTable
	.align	4
IfxCcu6_Cc60in_In_pinTable:	.type	object
	.size	IfxCcu6_Cc60in_In_pinTable,24
	.word	IfxCcu60_CC60INA_P02_0_IN,IfxCcu60_CC60INB_P00_1_IN,IfxCcu60_CC60INC_P02_6_IN,IfxCcu61_CC60INA_P00_1_IN
	.word	IfxCcu61_CC60INB_P02_0_IN,IfxCcu61_CC60INC_P00_7_IN
	.sdecl	'.data.IfxCcu6_PinMap.IfxCcu6_Cc61_Out_pinTable',data,cluster('IfxCcu6_Cc61_Out_pinTable')
	.sect	'.data.IfxCcu6_PinMap.IfxCcu6_Cc61_Out_pinTable'
	.global	IfxCcu6_Cc61_Out_pinTable
	.align	4
IfxCcu6_Cc61_Out_pinTable:	.type	object
	.size	IfxCcu6_Cc61_Out_pinTable,32
	.word	IfxCcu60_CC61_P02_2_OUT,IfxCcu60_CC61_P02_7_OUT,IfxCcu60_CC61_P11_11_OUT,IfxCcu60_CC61_P15_5_OUT,IfxCcu61_CC61_P00_3_OUT,IfxCcu61_CC61_P00_8_OUT,IfxCcu61_CC61_P20_9_OUT,IfxCcu61_CC61_P33_11_OUT
	.sdecl	'.data.IfxCcu6_PinMap.IfxCcu6_Cc61in_In_pinTable',data,cluster('IfxCcu6_Cc61in_In_pinTable')
	.sect	'.data.IfxCcu6_PinMap.IfxCcu6_Cc61in_In_pinTable'
	.global	IfxCcu6_Cc61in_In_pinTable
	.align	4
IfxCcu6_Cc61in_In_pinTable:	.type	object
	.size	IfxCcu6_Cc61in_In_pinTable,24
	.word	IfxCcu60_CC61INA_P02_2_IN,IfxCcu60_CC61INB_P00_3_IN,IfxCcu60_CC61INC_P02_7_IN,IfxCcu61_CC61INA_P00_3_IN
	.word	IfxCcu61_CC61INB_P02_2_IN,IfxCcu61_CC61INC_P00_8_IN
	.sdecl	'.data.IfxCcu6_PinMap.IfxCcu6_Cc62_Out_pinTable',data,cluster('IfxCcu6_Cc62_Out_pinTable')
	.sect	'.data.IfxCcu6_PinMap.IfxCcu6_Cc62_Out_pinTable'
	.global	IfxCcu6_Cc62_Out_pinTable
	.align	4
IfxCcu6_Cc62_Out_pinTable:	.type	object
	.size	IfxCcu6_Cc62_Out_pinTable,32
	.word	IfxCcu60_CC62_P02_4_OUT,IfxCcu60_CC62_P02_8_OUT,IfxCcu60_CC62_P11_10_OUT,IfxCcu60_CC62_P15_4_OUT,IfxCcu61_CC62_P00_5_OUT,IfxCcu61_CC62_P00_9_OUT,IfxCcu61_CC62_P20_10_OUT,IfxCcu61_CC62_P33_9_OUT
	.sdecl	'.data.IfxCcu6_PinMap.IfxCcu6_Cc62in_In_pinTable',data,cluster('IfxCcu6_Cc62in_In_pinTable')
	.sect	'.data.IfxCcu6_PinMap.IfxCcu6_Cc62in_In_pinTable'
	.global	IfxCcu6_Cc62in_In_pinTable
	.align	4
IfxCcu6_Cc62in_In_pinTable:	.type	object
	.size	IfxCcu6_Cc62in_In_pinTable,24
	.word	IfxCcu60_CC62INA_P02_4_IN,IfxCcu60_CC62INB_P00_5_IN,IfxCcu60_CC62INC_P02_8_IN,IfxCcu61_CC62INA_P00_5_IN
	.word	IfxCcu61_CC62INB_P02_4_IN,IfxCcu61_CC62INC_P00_9_IN
	.sdecl	'.data.IfxCcu6_PinMap.IfxCcu6_Ccpos0_In_pinTable',data,cluster('IfxCcu6_Ccpos0_In_pinTable')
	.sect	'.data.IfxCcu6_PinMap.IfxCcu6_Ccpos0_In_pinTable'
	.global	IfxCcu6_Ccpos0_In_pinTable
	.align	4
IfxCcu6_Ccpos0_In_pinTable:	.type	object
	.size	IfxCcu6_Ccpos0_In_pinTable,32
	.word	IfxCcu60_CCPOS0A_P02_6_IN
	.space	4
	.word	IfxCcu60_CCPOS0C_P10_4_IN,IfxCcu60_CCPOS0D_P40_0_IN,IfxCcu61_CCPOS0A_P00_7_IN
	.space	4
	.word	IfxCcu61_CCPOS0C_P33_7_IN
	.space	4
	.sdecl	'.data.IfxCcu6_PinMap.IfxCcu6_Ccpos1_In_pinTable',data,cluster('IfxCcu6_Ccpos1_In_pinTable')
	.sect	'.data.IfxCcu6_PinMap.IfxCcu6_Ccpos1_In_pinTable'
	.global	IfxCcu6_Ccpos1_In_pinTable
	.align	4
IfxCcu6_Ccpos1_In_pinTable:	.type	object
	.size	IfxCcu6_Ccpos1_In_pinTable,32
	.word	IfxCcu60_CCPOS1A_P02_7_IN,IfxCcu60_CCPOS1B_P40_1_IN,IfxCcu60_CCPOS1C_P10_7_IN,IfxCcu60_CCPOS1D_P40_2_IN,IfxCcu61_CCPOS1A_P00_8_IN,IfxCcu61_CCPOS1B_P40_6_IN,IfxCcu61_CCPOS1C_P33_6_IN,IfxCcu61_CCPOS1D_P40_7_IN
	.sdecl	'.data.IfxCcu6_PinMap.IfxCcu6_Ccpos2_In_pinTable',data,cluster('IfxCcu6_Ccpos2_In_pinTable')
	.sect	'.data.IfxCcu6_PinMap.IfxCcu6_Ccpos2_In_pinTable'
	.global	IfxCcu6_Ccpos2_In_pinTable
	.align	4
IfxCcu6_Ccpos2_In_pinTable:	.type	object
	.size	IfxCcu6_Ccpos2_In_pinTable,32
	.word	IfxCcu60_CCPOS2A_P02_8_IN,IfxCcu60_CCPOS2B_P40_3_IN,IfxCcu60_CCPOS2C_P10_8_IN
	.space	4
	.word	IfxCcu61_CCPOS2A_P00_9_IN,IfxCcu61_CCPOS2B_P40_8_IN,IfxCcu61_CCPOS2C_P33_5_IN,IfxCcu61_CCPOS2D_P40_9_IN
	.sdecl	'.data.IfxCcu6_PinMap.IfxCcu6_Cout60_Out_pinTable',data,cluster('IfxCcu6_Cout60_Out_pinTable')
	.sect	'.data.IfxCcu6_PinMap.IfxCcu6_Cout60_Out_pinTable'
	.global	IfxCcu6_Cout60_Out_pinTable
	.align	4
IfxCcu6_Cout60_Out_pinTable:	.type	object
	.size	IfxCcu6_Cout60_Out_pinTable,24
	.word	IfxCcu60_COUT60_P02_1_OUT,IfxCcu60_COUT60_P11_9_OUT,IfxCcu60_COUT60_P15_7_OUT,IfxCcu61_COUT60_P00_2_OUT
	.word	IfxCcu61_COUT60_P20_11_OUT,IfxCcu61_COUT60_P33_12_OUT
	.sdecl	'.data.IfxCcu6_PinMap.IfxCcu6_Cout61_Out_pinTable',data,cluster('IfxCcu6_Cout61_Out_pinTable')
	.sect	'.data.IfxCcu6_PinMap.IfxCcu6_Cout61_Out_pinTable'
	.global	IfxCcu6_Cout61_Out_pinTable
	.align	4
IfxCcu6_Cout61_Out_pinTable:	.type	object
	.size	IfxCcu6_Cout61_Out_pinTable,24
	.word	IfxCcu60_COUT61_P02_3_OUT,IfxCcu60_COUT61_P11_6_OUT,IfxCcu60_COUT61_P15_8_OUT,IfxCcu61_COUT61_P00_4_OUT
	.word	IfxCcu61_COUT61_P20_12_OUT,IfxCcu61_COUT61_P33_10_OUT
	.sdecl	'.data.IfxCcu6_PinMap.IfxCcu6_Cout62_Out_pinTable',data,cluster('IfxCcu6_Cout62_Out_pinTable')
	.sect	'.data.IfxCcu6_PinMap.IfxCcu6_Cout62_Out_pinTable'
	.global	IfxCcu6_Cout62_Out_pinTable
	.align	4
IfxCcu6_Cout62_Out_pinTable:	.type	object
	.size	IfxCcu6_Cout62_Out_pinTable,24
	.word	IfxCcu60_COUT62_P02_5_OUT,IfxCcu60_COUT62_P11_3_OUT,IfxCcu60_COUT62_P14_0_OUT,IfxCcu61_COUT62_P00_6_OUT
	.word	IfxCcu61_COUT62_P20_13_OUT,IfxCcu61_COUT62_P33_8_OUT
	.sdecl	'.data.IfxCcu6_PinMap.IfxCcu6_Cout63_Out_pinTable',data,cluster('IfxCcu6_Cout63_Out_pinTable')
	.sect	'.data.IfxCcu6_PinMap.IfxCcu6_Cout63_Out_pinTable'
	.global	IfxCcu6_Cout63_Out_pinTable
	.align	4
IfxCcu6_Cout63_Out_pinTable:	.type	object
	.size	IfxCcu6_Cout63_Out_pinTable,32
	.word	IfxCcu60_COUT63_P00_0_OUT,IfxCcu60_COUT63_P11_2_OUT,IfxCcu60_COUT63_P14_1_OUT,IfxCcu60_COUT63_P32_4_OUT
	.word	IfxCcu61_COUT63_P00_10_OUT,IfxCcu61_COUT63_P00_12_OUT,IfxCcu61_COUT63_P20_7_OUT
	.space	4
	.sdecl	'.data.IfxCcu6_PinMap.IfxCcu6_Ctrap_In_pinTable',data,cluster('IfxCcu6_Ctrap_In_pinTable')
	.sect	'.data.IfxCcu6_PinMap.IfxCcu6_Ctrap_In_pinTable'
	.global	IfxCcu6_Ctrap_In_pinTable
	.align	4
IfxCcu6_Ctrap_In_pinTable:	.type	object
	.size	IfxCcu6_Ctrap_In_pinTable,24
	.word	IfxCcu60_CTRAPA_P00_11_IN
	.space	8
	.word	IfxCcu61_CTRAPA_P00_0_IN
	.space	4
	.word	IfxCcu61_CTRAPC_P33_4_IN
	.sdecl	'.data.IfxCcu6_PinMap.IfxCcu6_T12hr_In_pinTable',data,cluster('IfxCcu6_T12hr_In_pinTable')
	.sect	'.data.IfxCcu6_PinMap.IfxCcu6_T12hr_In_pinTable'
	.global	IfxCcu6_T12hr_In_pinTable
	.align	4
IfxCcu6_T12hr_In_pinTable:	.type	object
	.size	IfxCcu6_T12hr_In_pinTable,40
	.space	4
	.word	IfxCcu60_T12HRB_P00_7_IN,IfxCcu60_T12HRC_P00_9_IN
	.space	4
	.word	IfxCcu60_T12HRE_P00_0_IN
	.space	4
	.word	IfxCcu61_T12HRB_P02_6_IN,IfxCcu61_T12HRC_P02_8_IN
	.space	4
	.word	IfxCcu61_T12HRE_P00_11_IN
	.sdecl	'.data.IfxCcu6_PinMap.IfxCcu6_T13hr_In_pinTable',data,cluster('IfxCcu6_T13hr_In_pinTable')
	.sect	'.data.IfxCcu6_PinMap.IfxCcu6_T13hr_In_pinTable'
	.global	IfxCcu6_T13hr_In_pinTable
	.align	4
IfxCcu6_T13hr_In_pinTable:	.type	object
	.size	IfxCcu6_T13hr_In_pinTable,24
	.space	4
	.word	IfxCcu60_T13HRB_P00_8_IN,IfxCcu60_T13HRC_P00_9_IN
	.space	4
	.word	IfxCcu61_T13HRB_P02_7_IN,IfxCcu61_T13HRC_P02_8_IN
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L3:
	.word	50990
	.half	3
	.word	.L4
	.byte	4
.L2:
	.byte	1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L5
	.byte	2,1,1,3
	.word	238
	.byte	4
	.byte	'Ifx__jump_and_link',0,3,1,61,17,1,1,5
	.byte	'fun',0,1,61,43
	.word	241
	.byte	6,0,7
	.byte	'__fract',0,4,128,1,7
	.byte	'float',0,4,4,8
	.byte	'Ifx__float_to_fract',0,3,1,152,2,18
	.word	286
	.byte	1,1,5
	.byte	'a',0,1,152,2,44
	.word	298
	.byte	6,0,7
	.byte	'unsigned long long int',0,8,7,9
	.byte	'void',0,3
	.word	378
	.byte	8
	.byte	'__ld64',0,3,2,135,1,19
	.word	352
	.byte	1,1,5
	.byte	'addr',0,2,135,1,32
	.word	384
	.byte	6,0,4
	.byte	'__st64',0,3,2,143,1,17,1,1,5
	.byte	'addr',0,2,143,1,30
	.word	384
	.byte	5
	.byte	'value',0,2,143,1,43
	.word	352
	.byte	6,0,7
	.byte	'unsigned int',0,4,7,7
	.byte	'int',0,4,5,7
	.byte	'unsigned char',0,1,8,7
	.byte	'unsigned short int',0,2,7,10
	.byte	'_Ifx_P_OUT_Bits',0,4,143,3,16,4,11
	.byte	'P0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,12,4,181,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	532
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMR_Bits',0,4,169,2,16,4,11
	.byte	'PS0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'PCL0',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	493
	.byte	1,0,2,35,3,0,12,4,133,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	848
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ID_Bits',0,4,110,16,4,11
	.byte	'MODREV',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	510
	.byte	16,0,2,35,2,0,12,4,148,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1419
	.byte	4,2,35,0,0,14,4
	.word	493
	.byte	15,3,0,10
	.byte	'_Ifx_P_IOCR0_Bits',0,4,140,1,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	3,5,2,35,0,11
	.byte	'PC0',0,1
	.word	493
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	493
	.byte	3,5,2,35,1,11
	.byte	'PC1',0,1
	.word	493
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	493
	.byte	3,5,2,35,2,11
	.byte	'PC2',0,1
	.word	493
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	3,5,2,35,3,11
	.byte	'PC3',0,1
	.word	493
	.byte	5,0,2,35,3,0,12,4,164,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1547
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR4_Bits',0,4,166,1,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	3,5,2,35,0,11
	.byte	'PC4',0,1
	.word	493
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	493
	.byte	3,5,2,35,1,11
	.byte	'PC5',0,1
	.word	493
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	493
	.byte	3,5,2,35,2,11
	.byte	'PC6',0,1
	.word	493
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	3,5,2,35,3,11
	.byte	'PC7',0,1
	.word	493
	.byte	5,0,2,35,3,0,12,4,180,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1762
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR8_Bits',0,4,179,1,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	3,5,2,35,0,11
	.byte	'PC8',0,1
	.word	493
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	493
	.byte	3,5,2,35,1,11
	.byte	'PC9',0,1
	.word	493
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	493
	.byte	3,5,2,35,2,11
	.byte	'PC10',0,1
	.word	493
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	3,5,2,35,3,11
	.byte	'PC11',0,1
	.word	493
	.byte	5,0,2,35,3,0,12,4,188,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1977
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR12_Bits',0,4,153,1,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	3,5,2,35,0,11
	.byte	'PC12',0,1
	.word	493
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	493
	.byte	3,5,2,35,1,11
	.byte	'PC13',0,1
	.word	493
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	493
	.byte	3,5,2,35,2,11
	.byte	'PC14',0,1
	.word	493
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	3,5,2,35,3,11
	.byte	'PC15',0,1
	.word	493
	.byte	5,0,2,35,3,0,12,4,172,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2194
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IN_Bits',0,4,118,16,4,11
	.byte	'P0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,12,4,156,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2414
	.byte	4,2,35,0,0,14,24
	.word	493
	.byte	15,23,0,10
	.byte	'_Ifx_P_PDR0_Bits',0,4,205,3,16,4,11
	.byte	'PD0',0,1
	.word	493
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	493
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'PD2',0,1
	.word	493
	.byte	3,5,2,35,1,11
	.byte	'PL2',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'PD3',0,1
	.word	493
	.byte	3,1,2,35,1,11
	.byte	'PL3',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'PD4',0,1
	.word	493
	.byte	3,5,2,35,2,11
	.byte	'PL4',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'PD5',0,1
	.word	493
	.byte	3,1,2,35,2,11
	.byte	'PL5',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'PD6',0,1
	.word	493
	.byte	3,5,2,35,3,11
	.byte	'PL6',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'PD7',0,1
	.word	493
	.byte	3,1,2,35,3,11
	.byte	'PL7',0,1
	.word	493
	.byte	1,0,2,35,3,0,12,4,205,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2737
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PDR1_Bits',0,4,226,3,16,4,11
	.byte	'PD8',0,1
	.word	493
	.byte	3,5,2,35,0,11
	.byte	'PL8',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'PD9',0,1
	.word	493
	.byte	3,1,2,35,0,11
	.byte	'PL9',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'PD10',0,1
	.word	493
	.byte	3,5,2,35,1,11
	.byte	'PL10',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'PD11',0,1
	.word	493
	.byte	3,1,2,35,1,11
	.byte	'PL11',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'PD12',0,1
	.word	493
	.byte	3,5,2,35,2,11
	.byte	'PL12',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'PD13',0,1
	.word	493
	.byte	3,1,2,35,2,11
	.byte	'PL13',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'PD14',0,1
	.word	493
	.byte	3,5,2,35,3,11
	.byte	'PL14',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'PD15',0,1
	.word	493
	.byte	3,1,2,35,3,11
	.byte	'PL15',0,1
	.word	493
	.byte	1,0,2,35,3,0,12,4,213,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3041
	.byte	4,2,35,0,0,14,8
	.word	493
	.byte	15,7,0,10
	.byte	'_Ifx_P_ESR_Bits',0,4,88,16,4,11
	.byte	'EN0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,12,4,140,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3366
	.byte	4,2,35,0,0,14,12
	.word	493
	.byte	15,11,0,10
	.byte	'_Ifx_P_PDISC_Bits',0,4,183,3,16,4,11
	.byte	'PDIS0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'PDIS2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'PDIS3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'PDIS4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'PDIS5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'PDIS6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'PDIS7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'PDIS8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'PDIS9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'PDIS10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'PDIS11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'PDIS12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'PDIS13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'PDIS14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'PDIS15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,12,4,197,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3706
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PCSR_Bits',0,4,165,3,16,4,11
	.byte	'SEL0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'SEL1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'SEL2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'SEL3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'SEL4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'SEL5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'SEL6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'SEL7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	493
	.byte	2,6,2,35,1,11
	.byte	'SEL10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'SEL11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	470
	.byte	19,1,2,35,0,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,12,4,189,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4072
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR0_Bits',0,4,206,2,16,4,11
	.byte	'PS0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	470
	.byte	28,0,2,35,0,0,12,4,149,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4358
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR4_Bits',0,4,227,2,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	4,4,2,35,0,11
	.byte	'PS4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	470
	.byte	24,0,2,35,0,0,12,4,165,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4505
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR8_Bits',0,4,238,2,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'PS8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	470
	.byte	20,0,2,35,0,0,12,4,173,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4674
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR12_Bits',0,4,216,2,16,4,11
	.byte	'reserved_0',0,2
	.word	510
	.byte	12,4,2,35,0,11
	.byte	'PS12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,12,4,157,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4846
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR0_Bits',0,4,232,1,16,4,11
	.byte	'reserved_0',0,2
	.word	510
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	510
	.byte	12,0,2,35,2,0,12,4,229,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5021
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR4_Bits',0,4,253,1,16,4,11
	.byte	'reserved_0',0,4
	.word	470
	.byte	20,12,2,35,0,11
	.byte	'PCL4',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	8,0,2,35,3,0,12,4,245,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5195
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR8_Bits',0,4,136,2,16,4,11
	.byte	'reserved_0',0,4
	.word	470
	.byte	24,8,2,35,0,11
	.byte	'PCL8',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	493
	.byte	4,0,2,35,3,0,12,4,253,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5369
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR12_Bits',0,4,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	470
	.byte	28,4,2,35,0,11
	.byte	'PCL12',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	493
	.byte	1,0,2,35,3,0,12,4,237,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5545
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR_Bits',0,4,249,2,16,4,11
	.byte	'PS0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,12,4,141,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5701
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR_Bits',0,4,147,2,16,4,11
	.byte	'reserved_0',0,2
	.word	510
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	493
	.byte	1,0,2,35,3,0,12,4,221,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6034
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR0_Bits',0,4,192,1,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	470
	.byte	30,0,2,35,0,0,12,4,196,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6382
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_Bits',0,4,200,1,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	470
	.byte	30,0,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_P21_Bits',0,4,208,1,16,4,11
	.byte	'RDIS_CTRL',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'RX_DIS',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'TERM',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'LRXTERM',0,1
	.word	493
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	470
	.byte	24,0,2,35,0,0,12,4,204,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6506
	.byte	4,2,35,0,13
	.byte	'B_P21',0
	.word	6590
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR2_Bits',0,4,218,1,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'LVDSR',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'LVDSRL',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	493
	.byte	2,4,2,35,1,11
	.byte	'TDIS_CTRL',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'TX_DIS',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'TX_PD',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'TX_PWDPD',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,12,4,213,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6770
	.byte	4,2,35,0,0,14,76
	.word	493
	.byte	15,75,0,10
	.byte	'_Ifx_P_ACCEN1_Bits',0,4,82,16,4,11
	.byte	'reserved_0',0,4
	.word	470
	.byte	32,0,2,35,0,0,12,4,132,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7023
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ACCEN0_Bits',0,4,45,16,4,11
	.byte	'EN0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	493
	.byte	1,0,2,35,3,0,12,4,252,3,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7110
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P',0,4,229,5,25,128,2,13
	.byte	'OUT',0
	.word	808
	.byte	4,2,35,0,13
	.byte	'OMR',0
	.word	1379
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	1498
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	1538
	.byte	4,2,35,12,13
	.byte	'IOCR0',0
	.word	1722
	.byte	4,2,35,16,13
	.byte	'IOCR4',0
	.word	1937
	.byte	4,2,35,20,13
	.byte	'IOCR8',0
	.word	2154
	.byte	4,2,35,24,13
	.byte	'IOCR12',0
	.word	2374
	.byte	4,2,35,28,13
	.byte	'reserved_20',0
	.word	1538
	.byte	4,2,35,32,13
	.byte	'IN',0
	.word	2688
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	2728
	.byte	24,2,35,40,13
	.byte	'PDR0',0
	.word	3001
	.byte	4,2,35,64,13
	.byte	'PDR1',0
	.word	3317
	.byte	4,2,35,68,13
	.byte	'reserved_48',0
	.word	3357
	.byte	8,2,35,72,13
	.byte	'ESR',0
	.word	3657
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	3697
	.byte	12,2,35,84,13
	.byte	'PDISC',0
	.word	4032
	.byte	4,2,35,96,13
	.byte	'PCSR',0
	.word	4318
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	3357
	.byte	8,2,35,104,13
	.byte	'OMSR0',0
	.word	4465
	.byte	4,2,35,112,13
	.byte	'OMSR4',0
	.word	4634
	.byte	4,2,35,116,13
	.byte	'OMSR8',0
	.word	4806
	.byte	4,2,35,120,13
	.byte	'OMSR12',0
	.word	4981
	.byte	4,2,35,124,13
	.byte	'OMCR0',0
	.word	5155
	.byte	4,3,35,128,1,13
	.byte	'OMCR4',0
	.word	5329
	.byte	4,3,35,132,1,13
	.byte	'OMCR8',0
	.word	5505
	.byte	4,3,35,136,1,13
	.byte	'OMCR12',0
	.word	5661
	.byte	4,3,35,140,1,13
	.byte	'OMSR',0
	.word	5994
	.byte	4,3,35,144,1,13
	.byte	'OMCR',0
	.word	6342
	.byte	4,3,35,148,1,13
	.byte	'reserved_98',0
	.word	3357
	.byte	8,3,35,152,1,13
	.byte	'LPCR0',0
	.word	6466
	.byte	4,3,35,160,1,13
	.byte	'LPCR1',0
	.word	6715
	.byte	4,3,35,164,1,13
	.byte	'LPCR2',0
	.word	6974
	.byte	4,3,35,168,1,13
	.byte	'reserved_A4',0
	.word	7014
	.byte	76,3,35,172,1,13
	.byte	'ACCEN1',0
	.word	7070
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	7637
	.byte	4,3,35,252,1,0,16
	.word	7677
	.byte	3
	.word	8280
	.byte	17,3,172,1,9,4,18
	.byte	'IfxPort_State_notChanged',0,0,18
	.byte	'IfxPort_State_high',0,1,18
	.byte	'IfxPort_State_low',0,128,128,4,18
	.byte	'IfxPort_State_toggled',0,129,128,4,0,4
	.byte	'IfxPort_setPinState',0,3,3,208,4,17,1,1,5
	.byte	'port',0,3,208,4,44
	.word	8285
	.byte	5
	.byte	'pinIndex',0,3,208,4,56
	.word	493
	.byte	5
	.byte	'action',0,3,208,4,80
	.word	8290
	.byte	6,0,19
	.word	246
	.byte	20
	.word	272
	.byte	6,0,19
	.word	307
	.byte	20
	.word	339
	.byte	6,0,19
	.word	389
	.byte	20
	.word	408
	.byte	6,0,19
	.word	424
	.byte	20
	.word	439
	.byte	20
	.word	453
	.byte	6,0,19
	.word	8393
	.byte	20
	.word	8421
	.byte	20
	.word	8435
	.byte	20
	.word	8453
	.byte	6,0,7
	.byte	'short int',0,2,5,21
	.byte	'__wchar_t',0,5,1,1
	.word	8546
	.byte	21
	.byte	'__size_t',0,5,1,1
	.word	470
	.byte	21
	.byte	'__ptrdiff_t',0,5,1,1
	.word	486
	.byte	22,1,3
	.word	8614
	.byte	21
	.byte	'__codeptr',0,5,1,1
	.word	8616
	.byte	10
	.byte	'_Ifx_CCU6_ACCEN0_Bits',0,6,45,16,4,11
	.byte	'EN0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_CCU6_ACCEN0_Bits',0,6,79,3
	.word	8639
	.byte	10
	.byte	'_Ifx_CCU6_ACCEN1_Bits',0,6,82,16,4,11
	.byte	'reserved_0',0,4
	.word	470
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_CCU6_ACCEN1_Bits',0,6,85,3
	.word	9198
	.byte	10
	.byte	'_Ifx_CCU6_CC60R_Bits',0,6,88,16,4,11
	.byte	'CCV',0,2
	.word	510
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_CCU6_CC60R_Bits',0,6,92,3
	.word	9277
	.byte	10
	.byte	'_Ifx_CCU6_CC60SR_Bits',0,6,95,16,4,11
	.byte	'CCS',0,2
	.word	510
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_CCU6_CC60SR_Bits',0,6,99,3
	.word	9370
	.byte	10
	.byte	'_Ifx_CCU6_CC61R_Bits',0,6,102,16,4,11
	.byte	'CCV',0,2
	.word	510
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_CCU6_CC61R_Bits',0,6,106,3
	.word	9465
	.byte	10
	.byte	'_Ifx_CCU6_CC61SR_Bits',0,6,109,16,4,11
	.byte	'CCS',0,2
	.word	510
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_CCU6_CC61SR_Bits',0,6,113,3
	.word	9558
	.byte	10
	.byte	'_Ifx_CCU6_CC62R_Bits',0,6,116,16,4,11
	.byte	'CCV',0,2
	.word	510
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_CCU6_CC62R_Bits',0,6,120,3
	.word	9653
	.byte	10
	.byte	'_Ifx_CCU6_CC62SR_Bits',0,6,123,16,4,11
	.byte	'CCS',0,2
	.word	510
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_CCU6_CC62SR_Bits',0,6,127,3
	.word	9746
	.byte	10
	.byte	'_Ifx_CCU6_CC63R_Bits',0,6,130,1,16,4,11
	.byte	'CCV',0,2
	.word	510
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_CCU6_CC63R_Bits',0,6,134,1,3
	.word	9841
	.byte	10
	.byte	'_Ifx_CCU6_CC63SR_Bits',0,6,137,1,16,4,11
	.byte	'CCS',0,2
	.word	510
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_CCU6_CC63SR_Bits',0,6,141,1,3
	.word	9936
	.byte	10
	.byte	'_Ifx_CCU6_CLC_Bits',0,6,144,1,16,4,11
	.byte	'DISR',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'DISS',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'EDIS',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	470
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_CCU6_CLC_Bits',0,6,151,1,3
	.word	10033
	.byte	10
	.byte	'_Ifx_CCU6_CMPMODIF_Bits',0,6,154,1,16,4,11
	.byte	'MCC60S',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'MCC61S',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'MCC62S',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	493
	.byte	3,2,2,35,0,11
	.byte	'MCC63S',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'MCC60R',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'MCC61R',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'MCC62R',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	493
	.byte	3,2,2,35,1,11
	.byte	'MCC63R',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	470
	.byte	17,0,2,35,0,0,21
	.byte	'Ifx_CCU6_CMPMODIF_Bits',0,6,168,1,3
	.word	10178
	.byte	10
	.byte	'_Ifx_CCU6_CMPSTAT_Bits',0,6,171,1,16,4,11
	.byte	'CC60ST',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'CC61ST',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'CC62ST',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'CCPOS60',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'CCPOS61',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'CCPOS62',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'CC63ST',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'CC60PS',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'COUT60PS',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'CC61PS',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'COUT61PS',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'CC62PS',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'COUT62PS',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'COUT63PS',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'T13IM',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_CCU6_CMPSTAT_Bits',0,6,190,1,3
	.word	10475
	.byte	10
	.byte	'_Ifx_CCU6_ID_Bits',0,6,193,1,16,4,11
	.byte	'MODREV',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'MODNUMBER',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_CCU6_ID_Bits',0,6,198,1,3
	.word	10861
	.byte	10
	.byte	'_Ifx_CCU6_IEN_Bits',0,6,201,1,16,4,11
	.byte	'ENCC60R',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'ENCC60F',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'ENCC61R',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'ENCC61F',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'ENCC62R',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'ENCC62F',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'ENT12OM',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'ENT12PM',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'ENT13CM',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'ENT13PM',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'ENTRPF',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'ENCHE',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'ENWHE',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'ENIDLE',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'ENSTR',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_CCU6_IEN_Bits',0,6,220,1,3
	.word	10974
	.byte	10
	.byte	'_Ifx_CCU6_IMON_Bits',0,6,223,1,16,4,11
	.byte	'LBE',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'CCPOS0I',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'CCPOS1I',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'CCPOS2I',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'CC60INI',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'CC61INI',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'CC62INI',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'CTRAPI',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'T12HRI',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'T13HRI',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	470
	.byte	22,0,2,35,0,0,21
	.byte	'Ifx_CCU6_IMON_Bits',0,6,236,1,3
	.word	11350
	.byte	10
	.byte	'_Ifx_CCU6_INP_Bits',0,6,239,1,16,4,11
	.byte	'INPCC60',0,1
	.word	493
	.byte	2,6,2,35,0,11
	.byte	'INPCC61',0,1
	.word	493
	.byte	2,4,2,35,0,11
	.byte	'INPCC62',0,1
	.word	493
	.byte	2,2,2,35,0,11
	.byte	'INPCHE',0,1
	.word	493
	.byte	2,0,2,35,0,11
	.byte	'INPERR',0,1
	.word	493
	.byte	2,6,2,35,1,11
	.byte	'INPT12',0,1
	.word	493
	.byte	2,4,2,35,1,11
	.byte	'INPT13',0,1
	.word	493
	.byte	2,2,2,35,1,11
	.byte	'reserved_14',0,4
	.word	470
	.byte	18,0,2,35,0,0,21
	.byte	'Ifx_CCU6_INP_Bits',0,6,249,1,3
	.word	11611
	.byte	10
	.byte	'_Ifx_CCU6_IS_Bits',0,6,252,1,16,4,11
	.byte	'ICC60R',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'ICC60F',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'ICC61R',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'ICC61F',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'ICC62R',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'ICC62F',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'T12OM',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'T12PM',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'T13CM',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'T13PM',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'TRPF',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'TRPS',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'CHE',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'WHE',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'IDLE',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'STR',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_CCU6_IS_Bits',0,6,143,2,3
	.word	11816
	.byte	10
	.byte	'_Ifx_CCU6_ISR_Bits',0,6,146,2,16,4,11
	.byte	'RCC60R',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'RCC60F',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'RCC61R',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'RCC61F',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'RCC62R',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'RCC62F',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'RT12OM',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'RT12PM',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'RT13CM',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'RT13PM',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'RTRPF',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'RCHE',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'RWHE',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'RIDLE',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'RSTR',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_CCU6_ISR_Bits',0,6,165,2,3
	.word	12159
	.byte	10
	.byte	'_Ifx_CCU6_ISS_Bits',0,6,168,2,16,4,11
	.byte	'SCC60R',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'SCC60F',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'SCC61R',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'SCC61F',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'SCC62R',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'SCC62F',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'ST12OM',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'ST12PM',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'ST13CM',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'ST13PM',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'STRPF',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'SWHC',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'SCHE',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'SWHE',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'SIDLE',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'SSTR',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_CCU6_ISS_Bits',0,6,187,2,3
	.word	12520
	.byte	10
	.byte	'_Ifx_CCU6_KRST0_Bits',0,6,190,2,16,4,11
	.byte	'RST',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'RSTSTAT',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	470
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_CCU6_KRST0_Bits',0,6,195,2,3
	.word	12874
	.byte	10
	.byte	'_Ifx_CCU6_KRST1_Bits',0,6,198,2,16,4,11
	.byte	'RST',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	470
	.byte	31,0,2,35,0,0,21
	.byte	'Ifx_CCU6_KRST1_Bits',0,6,202,2,3
	.word	12987
	.byte	10
	.byte	'_Ifx_CCU6_KRSTCLR_Bits',0,6,205,2,16,4,11
	.byte	'CLR',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	470
	.byte	31,0,2,35,0,0,21
	.byte	'Ifx_CCU6_KRSTCLR_Bits',0,6,209,2,3
	.word	13081
	.byte	10
	.byte	'_Ifx_CCU6_KSCSR_Bits',0,6,212,2,16,4,11
	.byte	'SB0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'SB1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'SB2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'SB3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	470
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_CCU6_KSCSR_Bits',0,6,219,2,3
	.word	13179
	.byte	10
	.byte	'_Ifx_CCU6_LI_Bits',0,6,222,2,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'CCPOS0EN',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'CCPOS1EN',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'CCPOS2EN',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'CC60INEN',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'CC61INEN',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'CC62INEN',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'CTRAPEN',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'T12HREN',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'T13HREN',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	493
	.byte	3,3,2,35,1,11
	.byte	'LBEEN',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'INPLBE',0,1
	.word	493
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_CCU6_LI_Bits',0,6,238,2,3
	.word	13318
	.byte	10
	.byte	'_Ifx_CCU6_MCFG_Bits',0,6,241,2,16,4,11
	.byte	'T12',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'T13',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'MCM',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	470
	.byte	29,0,2,35,0,0,21
	.byte	'Ifx_CCU6_MCFG_Bits',0,6,247,2,3
	.word	13649
	.byte	10
	.byte	'_Ifx_CCU6_MCMCTR_Bits',0,6,250,2,16,4,11
	.byte	'SWSEL',0,1
	.word	493
	.byte	3,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'SWSYN',0,1
	.word	493
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	493
	.byte	2,0,2,35,0,11
	.byte	'STE12U',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'STE12D',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'STE13U',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	470
	.byte	21,0,2,35,0,0,21
	.byte	'Ifx_CCU6_MCMCTR_Bits',0,6,132,3,3
	.word	13771
	.byte	10
	.byte	'_Ifx_CCU6_MCMOUT_Bits',0,6,135,3,16,4,11
	.byte	'MCMP',0,1
	.word	493
	.byte	6,2,2,35,0,11
	.byte	'R',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'EXPH',0,1
	.word	493
	.byte	3,5,2,35,1,11
	.byte	'CURH',0,1
	.word	493
	.byte	3,2,2,35,1,11
	.byte	'reserved_14',0,4
	.word	470
	.byte	18,0,2,35,0,0,21
	.byte	'Ifx_CCU6_MCMOUT_Bits',0,6,143,3,3
	.word	13985
	.byte	10
	.byte	'_Ifx_CCU6_MCMOUTS_Bits',0,6,146,3,16,4,11
	.byte	'MCMPS',0,1
	.word	493
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'STRMCM',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'EXPHS',0,1
	.word	493
	.byte	3,5,2,35,1,11
	.byte	'CURHS',0,1
	.word	493
	.byte	3,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'STRHP',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_CCU6_MCMOUTS_Bits',0,6,156,3,3
	.word	14150
	.byte	10
	.byte	'_Ifx_CCU6_MODCTR_Bits',0,6,159,3,16,4,11
	.byte	'T12MODEN',0,1
	.word	493
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'MCMEN',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'T13MODEN',0,1
	.word	493
	.byte	6,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'ECT13O',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_CCU6_MODCTR_Bits',0,6,168,3,3
	.word	14365
	.byte	10
	.byte	'_Ifx_CCU6_MOSEL_Bits',0,6,171,3,16,4,11
	.byte	'TRIG0SEL',0,1
	.word	493
	.byte	3,5,2,35,0,11
	.byte	'TRIG1SEL',0,1
	.word	493
	.byte	3,2,2,35,0,11
	.byte	'TRIG2SEL',0,2
	.word	510
	.byte	3,7,2,35,0,11
	.byte	'reserved_9',0,4
	.word	470
	.byte	23,0,2,35,0,0,21
	.byte	'Ifx_CCU6_MOSEL_Bits',0,6,177,3,3
	.word	14567
	.byte	10
	.byte	'_Ifx_CCU6_OCS_Bits',0,6,180,3,16,4,11
	.byte	'TGS',0,1
	.word	493
	.byte	2,6,2,35,0,11
	.byte	'TGB',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'TG_P',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	470
	.byte	20,8,2,35,0,11
	.byte	'SUS',0,1
	.word	493
	.byte	4,4,2,35,3,11
	.byte	'SUS_P',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'SUSSTA',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	493
	.byte	2,0,2,35,3,0,21
	.byte	'Ifx_CCU6_OCS_Bits',0,6,190,3,3
	.word	14706
	.byte	10
	.byte	'_Ifx_CCU6_PISEL0_Bits',0,6,193,3,16,4,11
	.byte	'ISCC60',0,1
	.word	493
	.byte	2,6,2,35,0,11
	.byte	'ISCC61',0,1
	.word	493
	.byte	2,4,2,35,0,11
	.byte	'ISCC62',0,1
	.word	493
	.byte	2,2,2,35,0,11
	.byte	'ISTRP',0,1
	.word	493
	.byte	2,0,2,35,0,11
	.byte	'ISPOS0',0,1
	.word	493
	.byte	2,6,2,35,1,11
	.byte	'ISPOS1',0,1
	.word	493
	.byte	2,4,2,35,1,11
	.byte	'ISPOS2',0,1
	.word	493
	.byte	2,2,2,35,1,11
	.byte	'IST12HR',0,1
	.word	493
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_CCU6_PISEL0_Bits',0,6,204,3,3
	.word	14900
	.byte	10
	.byte	'_Ifx_CCU6_PISEL2_Bits',0,6,207,3,16,4,11
	.byte	'IST13HR',0,1
	.word	493
	.byte	2,6,2,35,0,11
	.byte	'ISCNT12',0,1
	.word	493
	.byte	2,4,2,35,0,11
	.byte	'ISCNT13',0,1
	.word	493
	.byte	2,2,2,35,0,11
	.byte	'T12EXT',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'T13EXT',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	470
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_CCU6_PISEL2_Bits',0,6,215,3,3
	.word	15126
	.byte	10
	.byte	'_Ifx_CCU6_PSLR_Bits',0,6,218,3,16,4,11
	.byte	'PSL',0,1
	.word	493
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'PSL63',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	470
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_CCU6_PSLR_Bits',0,6,224,3,3
	.word	15300
	.byte	10
	.byte	'_Ifx_CCU6_T12_Bits',0,6,227,3,16,4,11
	.byte	'T12CV',0,2
	.word	510
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_CCU6_T12_Bits',0,6,231,3,3
	.word	15431
	.byte	10
	.byte	'_Ifx_CCU6_T12DTC_Bits',0,6,234,3,16,4,11
	.byte	'DTM',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'DTE0',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'DTE1',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'DTE2',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'DTR0',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'DTR1',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'DTR2',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	470
	.byte	17,0,2,35,0,0,21
	.byte	'Ifx_CCU6_T12DTC_Bits',0,6,245,3,3
	.word	15524
	.byte	10
	.byte	'_Ifx_CCU6_T12MSEL_Bits',0,6,248,3,16,4,11
	.byte	'MSEL60',0,1
	.word	493
	.byte	4,4,2,35,0,11
	.byte	'MSEL61',0,1
	.word	493
	.byte	4,0,2,35,0,11
	.byte	'MSEL62',0,1
	.word	493
	.byte	4,4,2,35,1,11
	.byte	'HSYNC',0,1
	.word	493
	.byte	3,1,2,35,1,11
	.byte	'DBYP',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_CCU6_T12MSEL_Bits',0,6,128,4,3
	.word	15740
	.byte	10
	.byte	'_Ifx_CCU6_T12PR_Bits',0,6,131,4,16,4,11
	.byte	'T12PV',0,2
	.word	510
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_CCU6_T12PR_Bits',0,6,135,4,3
	.word	15911
	.byte	10
	.byte	'_Ifx_CCU6_T13_Bits',0,6,138,4,16,4,11
	.byte	'T13CV',0,2
	.word	510
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_CCU6_T13_Bits',0,6,142,4,3
	.word	16008
	.byte	10
	.byte	'_Ifx_CCU6_T13PR_Bits',0,6,145,4,16,4,11
	.byte	'T13PV',0,2
	.word	510
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_CCU6_T13PR_Bits',0,6,149,4,3
	.word	16101
	.byte	10
	.byte	'_Ifx_CCU6_TCTR0_Bits',0,6,152,4,16,4,11
	.byte	'T12CLK',0,1
	.word	493
	.byte	3,5,2,35,0,11
	.byte	'T12PRE',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'T12R',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'STE12',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'CDIR',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'CTM',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'T13CLK',0,1
	.word	493
	.byte	3,5,2,35,1,11
	.byte	'T13PRE',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'T13R',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'STE13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'reserved_14',0,4
	.word	470
	.byte	18,0,2,35,0,0,21
	.byte	'Ifx_CCU6_TCTR0_Bits',0,6,165,4,3
	.word	16198
	.byte	10
	.byte	'_Ifx_CCU6_TCTR2_Bits',0,6,168,4,16,4,11
	.byte	'T12SSC',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'T13SSC',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'T13TEC',0,1
	.word	493
	.byte	3,3,2,35,0,11
	.byte	'T13TED',0,1
	.word	493
	.byte	2,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'T12RSEL',0,1
	.word	493
	.byte	2,6,2,35,1,11
	.byte	'T13RSEL',0,1
	.word	493
	.byte	2,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	470
	.byte	20,0,2,35,0,0,21
	.byte	'Ifx_CCU6_TCTR2_Bits',0,6,178,4,3
	.word	16447
	.byte	10
	.byte	'_Ifx_CCU6_TCTR4_Bits',0,6,181,4,16,4,11
	.byte	'T12RR',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'T12RS',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'T12RES',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'DTRES',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'T12CNT',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'T12STR',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'T12STD',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'T13RR',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'T13RS',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'T13RES',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	493
	.byte	2,3,2,35,1,11
	.byte	'T13CNT',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'T13STR',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'T13STD',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_CCU6_TCTR4_Bits',0,6,199,4,3
	.word	16659
	.byte	10
	.byte	'_Ifx_CCU6_TRPCTR_Bits',0,6,202,4,16,4,11
	.byte	'TRPM0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'TRPM1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'TRPM2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	493
	.byte	5,0,2,35,0,11
	.byte	'TRPEN',0,1
	.word	493
	.byte	6,2,2,35,1,11
	.byte	'TRPEN13',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'TRPPEN',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_CCU6_TRPCTR_Bits',0,6,212,4,3
	.word	17013
	.byte	12,6,220,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8639
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CCU6_ACCEN0',0,6,225,4,3
	.word	17222
	.byte	12,6,228,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9198
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CCU6_ACCEN1',0,6,233,4,3
	.word	17287
	.byte	12,6,236,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9277
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CCU6_CC60R',0,6,241,4,3
	.word	17352
	.byte	12,6,244,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9370
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CCU6_CC60SR',0,6,249,4,3
	.word	17416
	.byte	12,6,252,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9465
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CCU6_CC61R',0,6,129,5,3
	.word	17481
	.byte	12,6,132,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9558
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CCU6_CC61SR',0,6,137,5,3
	.word	17545
	.byte	12,6,140,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9653
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CCU6_CC62R',0,6,145,5,3
	.word	17610
	.byte	12,6,148,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9746
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CCU6_CC62SR',0,6,153,5,3
	.word	17674
	.byte	12,6,156,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9841
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CCU6_CC63R',0,6,161,5,3
	.word	17739
	.byte	12,6,164,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9936
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CCU6_CC63SR',0,6,169,5,3
	.word	17803
	.byte	12,6,172,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10033
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CCU6_CLC',0,6,177,5,3
	.word	17868
	.byte	12,6,180,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10178
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CCU6_CMPMODIF',0,6,185,5,3
	.word	17930
	.byte	12,6,188,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10475
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CCU6_CMPSTAT',0,6,193,5,3
	.word	17997
	.byte	12,6,196,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10861
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CCU6_ID',0,6,201,5,3
	.word	18063
	.byte	12,6,204,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10974
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CCU6_IEN',0,6,209,5,3
	.word	18124
	.byte	12,6,212,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11350
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CCU6_IMON',0,6,217,5,3
	.word	18186
	.byte	12,6,220,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11611
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CCU6_INP',0,6,225,5,3
	.word	18249
	.byte	12,6,228,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11816
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CCU6_IS',0,6,233,5,3
	.word	18311
	.byte	12,6,236,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12159
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CCU6_ISR',0,6,241,5,3
	.word	18372
	.byte	12,6,244,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12520
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CCU6_ISS',0,6,249,5,3
	.word	18434
	.byte	12,6,252,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12874
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CCU6_KRST0',0,6,129,6,3
	.word	18496
	.byte	12,6,132,6,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12987
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CCU6_KRST1',0,6,137,6,3
	.word	18560
	.byte	12,6,140,6,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13081
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CCU6_KRSTCLR',0,6,145,6,3
	.word	18624
	.byte	12,6,148,6,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13179
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CCU6_KSCSR',0,6,153,6,3
	.word	18690
	.byte	12,6,156,6,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13318
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CCU6_LI',0,6,161,6,3
	.word	18754
	.byte	12,6,164,6,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13649
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CCU6_MCFG',0,6,169,6,3
	.word	18815
	.byte	12,6,172,6,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13771
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CCU6_MCMCTR',0,6,177,6,3
	.word	18878
	.byte	12,6,180,6,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13985
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CCU6_MCMOUT',0,6,185,6,3
	.word	18943
	.byte	12,6,188,6,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14150
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CCU6_MCMOUTS',0,6,193,6,3
	.word	19008
	.byte	12,6,196,6,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14365
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CCU6_MODCTR',0,6,201,6,3
	.word	19074
	.byte	12,6,204,6,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14567
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CCU6_MOSEL',0,6,209,6,3
	.word	19139
	.byte	12,6,212,6,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14706
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CCU6_OCS',0,6,217,6,3
	.word	19203
	.byte	12,6,220,6,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14900
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CCU6_PISEL0',0,6,225,6,3
	.word	19265
	.byte	12,6,228,6,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15126
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CCU6_PISEL2',0,6,233,6,3
	.word	19330
	.byte	12,6,236,6,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15300
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CCU6_PSLR',0,6,241,6,3
	.word	19395
	.byte	12,6,244,6,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15431
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CCU6_T12',0,6,249,6,3
	.word	19458
	.byte	12,6,252,6,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15524
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CCU6_T12DTC',0,6,129,7,3
	.word	19520
	.byte	12,6,132,7,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15740
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CCU6_T12MSEL',0,6,137,7,3
	.word	19585
	.byte	12,6,140,7,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15911
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CCU6_T12PR',0,6,145,7,3
	.word	19651
	.byte	12,6,148,7,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16008
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CCU6_T13',0,6,153,7,3
	.word	19715
	.byte	12,6,156,7,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16101
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CCU6_T13PR',0,6,161,7,3
	.word	19777
	.byte	12,6,164,7,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16198
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CCU6_TCTR0',0,6,169,7,3
	.word	19841
	.byte	12,6,172,7,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16447
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CCU6_TCTR2',0,6,177,7,3
	.word	19905
	.byte	12,6,180,7,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16659
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CCU6_TCTR4',0,6,185,7,3
	.word	19969
	.byte	12,6,188,7,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17013
	.byte	4,2,35,0,0,21
	.byte	'Ifx_CCU6_TRPCTR',0,6,193,7,3
	.word	20033
	.byte	14,52
	.word	493
	.byte	15,51,0,10
	.byte	'_Ifx_CCU6',0,6,204,7,25,128,2,13
	.byte	'CLC',0
	.word	17868
	.byte	4,2,35,0,13
	.byte	'MCFG',0
	.word	18815
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	18063
	.byte	4,2,35,8,13
	.byte	'MOSEL',0
	.word	19139
	.byte	4,2,35,12,13
	.byte	'PISEL0',0
	.word	19265
	.byte	4,2,35,16,13
	.byte	'PISEL2',0
	.word	19330
	.byte	4,2,35,20,13
	.byte	'reserved_18',0
	.word	1538
	.byte	4,2,35,24,13
	.byte	'KSCSR',0
	.word	18690
	.byte	4,2,35,28,13
	.byte	'T12',0
	.word	19458
	.byte	4,2,35,32,13
	.byte	'T12PR',0
	.word	19651
	.byte	4,2,35,36,13
	.byte	'T12DTC',0
	.word	19520
	.byte	4,2,35,40,13
	.byte	'reserved_2C',0
	.word	1538
	.byte	4,2,35,44,13
	.byte	'CC60R',0
	.word	17352
	.byte	4,2,35,48,13
	.byte	'CC61R',0
	.word	17481
	.byte	4,2,35,52,13
	.byte	'CC62R',0
	.word	17610
	.byte	4,2,35,56,13
	.byte	'reserved_3C',0
	.word	1538
	.byte	4,2,35,60,13
	.byte	'CC60SR',0
	.word	17416
	.byte	4,2,35,64,13
	.byte	'CC61SR',0
	.word	17545
	.byte	4,2,35,68,13
	.byte	'CC62SR',0
	.word	17674
	.byte	4,2,35,72,13
	.byte	'reserved_4C',0
	.word	1538
	.byte	4,2,35,76,13
	.byte	'T13',0
	.word	19715
	.byte	4,2,35,80,13
	.byte	'T13PR',0
	.word	19777
	.byte	4,2,35,84,13
	.byte	'CC63R',0
	.word	17739
	.byte	4,2,35,88,13
	.byte	'CC63SR',0
	.word	17803
	.byte	4,2,35,92,13
	.byte	'CMPSTAT',0
	.word	17997
	.byte	4,2,35,96,13
	.byte	'CMPMODIF',0
	.word	17930
	.byte	4,2,35,100,13
	.byte	'T12MSEL',0
	.word	19585
	.byte	4,2,35,104,13
	.byte	'reserved_6C',0
	.word	1538
	.byte	4,2,35,108,13
	.byte	'TCTR0',0
	.word	19841
	.byte	4,2,35,112,13
	.byte	'TCTR2',0
	.word	19905
	.byte	4,2,35,116,13
	.byte	'TCTR4',0
	.word	19969
	.byte	4,2,35,120,13
	.byte	'reserved_7C',0
	.word	1538
	.byte	4,2,35,124,13
	.byte	'MODCTR',0
	.word	19074
	.byte	4,3,35,128,1,13
	.byte	'TRPCTR',0
	.word	20033
	.byte	4,3,35,132,1,13
	.byte	'PSLR',0
	.word	19395
	.byte	4,3,35,136,1,13
	.byte	'MCMOUTS',0
	.word	19008
	.byte	4,3,35,140,1,13
	.byte	'MCMOUT',0
	.word	18943
	.byte	4,3,35,144,1,13
	.byte	'MCMCTR',0
	.word	18878
	.byte	4,3,35,148,1,13
	.byte	'IMON',0
	.word	18186
	.byte	4,3,35,152,1,13
	.byte	'LI',0
	.word	18754
	.byte	4,3,35,156,1,13
	.byte	'IS',0
	.word	18311
	.byte	4,3,35,160,1,13
	.byte	'ISS',0
	.word	18434
	.byte	4,3,35,164,1,13
	.byte	'ISR',0
	.word	18372
	.byte	4,3,35,168,1,13
	.byte	'INP',0
	.word	18249
	.byte	4,3,35,172,1,13
	.byte	'IEN',0
	.word	18124
	.byte	4,3,35,176,1,13
	.byte	'reserved_B4',0
	.word	20098
	.byte	52,3,35,180,1,13
	.byte	'OCS',0
	.word	19203
	.byte	4,3,35,232,1,13
	.byte	'KRSTCLR',0
	.word	18624
	.byte	4,3,35,236,1,13
	.byte	'KRST1',0
	.word	18560
	.byte	4,3,35,240,1,13
	.byte	'KRST0',0
	.word	18496
	.byte	4,3,35,244,1,13
	.byte	'ACCEN1',0
	.word	17287
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	17222
	.byte	4,3,35,252,1,0,16
	.word	20107
	.byte	21
	.byte	'Ifx_CCU6',0,6,130,8,3
	.word	20963
	.byte	21
	.byte	'boolean',0,7,101,29
	.word	493
	.byte	21
	.byte	'uint8',0,7,105,29
	.word	493
	.byte	21
	.byte	'uint16',0,7,109,29
	.word	510
	.byte	7
	.byte	'unsigned long int',0,4,7,21
	.byte	'uint32',0,7,113,29
	.word	21031
	.byte	21
	.byte	'uint64',0,7,118,29
	.word	352
	.byte	21
	.byte	'sint16',0,7,126,29
	.word	8546
	.byte	7
	.byte	'long int',0,4,5,21
	.byte	'sint32',0,7,131,1,29
	.word	21097
	.byte	7
	.byte	'long long int',0,8,5,21
	.byte	'sint64',0,7,138,1,29
	.word	21125
	.byte	21
	.byte	'float32',0,7,167,1,29
	.word	298
	.byte	21
	.byte	'pvoid',0,8,57,28
	.word	384
	.byte	21
	.byte	'Ifx_TickTime',0,8,79,28
	.word	21125
	.byte	17,8,130,1,9,1,18
	.byte	'Ifx_RxSel_a',0,0,18
	.byte	'Ifx_RxSel_b',0,1,18
	.byte	'Ifx_RxSel_c',0,2,18
	.byte	'Ifx_RxSel_d',0,3,18
	.byte	'Ifx_RxSel_e',0,4,18
	.byte	'Ifx_RxSel_f',0,5,18
	.byte	'Ifx_RxSel_g',0,6,18
	.byte	'Ifx_RxSel_h',0,7,0,21
	.byte	'Ifx_RxSel',0,8,140,1,3
	.word	21210
	.byte	21
	.byte	'Ifx_P_ACCEN0_Bits',0,4,79,3
	.word	7110
	.byte	21
	.byte	'Ifx_P_ACCEN1_Bits',0,4,85,3
	.word	7023
	.byte	21
	.byte	'Ifx_P_ESR_Bits',0,4,107,3
	.word	3366
	.byte	21
	.byte	'Ifx_P_ID_Bits',0,4,115,3
	.word	1419
	.byte	21
	.byte	'Ifx_P_IN_Bits',0,4,137,1,3
	.word	2414
	.byte	21
	.byte	'Ifx_P_IOCR0_Bits',0,4,150,1,3
	.word	1547
	.byte	21
	.byte	'Ifx_P_IOCR12_Bits',0,4,163,1,3
	.word	2194
	.byte	21
	.byte	'Ifx_P_IOCR4_Bits',0,4,176,1,3
	.word	1762
	.byte	21
	.byte	'Ifx_P_IOCR8_Bits',0,4,189,1,3
	.word	1977
	.byte	21
	.byte	'Ifx_P_LPCR0_Bits',0,4,197,1,3
	.word	6382
	.byte	21
	.byte	'Ifx_P_LPCR1_Bits',0,4,205,1,3
	.word	6506
	.byte	21
	.byte	'Ifx_P_LPCR1_P21_Bits',0,4,215,1,3
	.word	6590
	.byte	21
	.byte	'Ifx_P_LPCR2_Bits',0,4,229,1,3
	.word	6770
	.byte	21
	.byte	'Ifx_P_OMCR0_Bits',0,4,240,1,3
	.word	5021
	.byte	21
	.byte	'Ifx_P_OMCR12_Bits',0,4,250,1,3
	.word	5545
	.byte	21
	.byte	'Ifx_P_OMCR4_Bits',0,4,133,2,3
	.word	5195
	.byte	21
	.byte	'Ifx_P_OMCR8_Bits',0,4,144,2,3
	.word	5369
	.byte	21
	.byte	'Ifx_P_OMCR_Bits',0,4,166,2,3
	.word	6034
	.byte	21
	.byte	'Ifx_P_OMR_Bits',0,4,203,2,3
	.word	848
	.byte	21
	.byte	'Ifx_P_OMSR0_Bits',0,4,213,2,3
	.word	4358
	.byte	21
	.byte	'Ifx_P_OMSR12_Bits',0,4,224,2,3
	.word	4846
	.byte	21
	.byte	'Ifx_P_OMSR4_Bits',0,4,235,2,3
	.word	4505
	.byte	21
	.byte	'Ifx_P_OMSR8_Bits',0,4,246,2,3
	.word	4674
	.byte	21
	.byte	'Ifx_P_OMSR_Bits',0,4,140,3,3
	.word	5701
	.byte	21
	.byte	'Ifx_P_OUT_Bits',0,4,162,3,3
	.word	532
	.byte	21
	.byte	'Ifx_P_PCSR_Bits',0,4,180,3,3
	.word	4072
	.byte	21
	.byte	'Ifx_P_PDISC_Bits',0,4,202,3,3
	.word	3706
	.byte	21
	.byte	'Ifx_P_PDR0_Bits',0,4,223,3,3
	.word	2737
	.byte	21
	.byte	'Ifx_P_PDR1_Bits',0,4,244,3,3
	.word	3041
	.byte	21
	.byte	'Ifx_P_ACCEN0',0,4,129,4,3
	.word	7637
	.byte	21
	.byte	'Ifx_P_ACCEN1',0,4,137,4,3
	.word	7070
	.byte	21
	.byte	'Ifx_P_ESR',0,4,145,4,3
	.word	3657
	.byte	21
	.byte	'Ifx_P_ID',0,4,153,4,3
	.word	1498
	.byte	21
	.byte	'Ifx_P_IN',0,4,161,4,3
	.word	2688
	.byte	21
	.byte	'Ifx_P_IOCR0',0,4,169,4,3
	.word	1722
	.byte	21
	.byte	'Ifx_P_IOCR12',0,4,177,4,3
	.word	2374
	.byte	21
	.byte	'Ifx_P_IOCR4',0,4,185,4,3
	.word	1937
	.byte	21
	.byte	'Ifx_P_IOCR8',0,4,193,4,3
	.word	2154
	.byte	21
	.byte	'Ifx_P_LPCR0',0,4,201,4,3
	.word	6466
	.byte	21
	.byte	'Ifx_P_LPCR1',0,4,210,4,3
	.word	6715
	.byte	21
	.byte	'Ifx_P_LPCR2',0,4,218,4,3
	.word	6974
	.byte	21
	.byte	'Ifx_P_OMCR',0,4,226,4,3
	.word	6342
	.byte	21
	.byte	'Ifx_P_OMCR0',0,4,234,4,3
	.word	5155
	.byte	21
	.byte	'Ifx_P_OMCR12',0,4,242,4,3
	.word	5661
	.byte	21
	.byte	'Ifx_P_OMCR4',0,4,250,4,3
	.word	5329
	.byte	21
	.byte	'Ifx_P_OMCR8',0,4,130,5,3
	.word	5505
	.byte	21
	.byte	'Ifx_P_OMR',0,4,138,5,3
	.word	1379
	.byte	21
	.byte	'Ifx_P_OMSR',0,4,146,5,3
	.word	5994
	.byte	21
	.byte	'Ifx_P_OMSR0',0,4,154,5,3
	.word	4465
	.byte	21
	.byte	'Ifx_P_OMSR12',0,4,162,5,3
	.word	4981
	.byte	21
	.byte	'Ifx_P_OMSR4',0,4,170,5,3
	.word	4634
	.byte	21
	.byte	'Ifx_P_OMSR8',0,4,178,5,3
	.word	4806
	.byte	21
	.byte	'Ifx_P_OUT',0,4,186,5,3
	.word	808
	.byte	21
	.byte	'Ifx_P_PCSR',0,4,194,5,3
	.word	4318
	.byte	21
	.byte	'Ifx_P_PDISC',0,4,202,5,3
	.word	4032
	.byte	21
	.byte	'Ifx_P_PDR0',0,4,210,5,3
	.word	3001
	.byte	21
	.byte	'Ifx_P_PDR1',0,4,218,5,3
	.word	3317
	.byte	16
	.word	7677
	.byte	21
	.byte	'Ifx_P',0,4,139,6,3
	.word	22666
	.byte	17,9,250,10,9,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy16384',0,0,18
	.byte	'IfxScu_WDTCON1_IR_divBy256',0,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy64',0,2,0,21
	.byte	'IfxScu_WDTCON1_IR',0,9,255,10,3
	.word	22686
	.byte	10
	.byte	'_Ifx_SCU_ACCEN0_Bits',0,10,45,16,4,11
	.byte	'EN0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_ACCEN0_Bits',0,10,79,3
	.word	22808
	.byte	10
	.byte	'_Ifx_SCU_ACCEN1_Bits',0,10,82,16,4,11
	.byte	'reserved_0',0,4
	.word	470
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_SCU_ACCEN1_Bits',0,10,85,3
	.word	23365
	.byte	10
	.byte	'_Ifx_SCU_ARSTDIS_Bits',0,10,88,16,4,11
	.byte	'STM0DIS',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'STM1DIS',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'STM2DIS',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	470
	.byte	29,0,2,35,0,0,21
	.byte	'Ifx_SCU_ARSTDIS_Bits',0,10,94,3
	.word	23442
	.byte	10
	.byte	'_Ifx_SCU_CCUCON0_Bits',0,10,97,16,4,11
	.byte	'BAUD1DIV',0,1
	.word	493
	.byte	4,4,2,35,0,11
	.byte	'BAUD2DIV',0,1
	.word	493
	.byte	4,0,2,35,0,11
	.byte	'SRIDIV',0,1
	.word	493
	.byte	4,4,2,35,1,11
	.byte	'LPDIV',0,1
	.word	493
	.byte	4,0,2,35,1,11
	.byte	'SPBDIV',0,1
	.word	493
	.byte	4,4,2,35,2,11
	.byte	'FSI2DIV',0,1
	.word	493
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	493
	.byte	2,0,2,35,2,11
	.byte	'FSIDIV',0,1
	.word	493
	.byte	2,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	493
	.byte	2,4,2,35,3,11
	.byte	'CLKSEL',0,1
	.word	493
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON0_Bits',0,10,111,3
	.word	23578
	.byte	10
	.byte	'_Ifx_SCU_CCUCON1_Bits',0,10,114,16,4,11
	.byte	'CANDIV',0,1
	.word	493
	.byte	4,4,2,35,0,11
	.byte	'ERAYDIV',0,1
	.word	493
	.byte	4,0,2,35,0,11
	.byte	'STMDIV',0,1
	.word	493
	.byte	4,4,2,35,1,11
	.byte	'GTMDIV',0,1
	.word	493
	.byte	4,0,2,35,1,11
	.byte	'ETHDIV',0,1
	.word	493
	.byte	4,4,2,35,2,11
	.byte	'ASCLINFDIV',0,1
	.word	493
	.byte	4,0,2,35,2,11
	.byte	'ASCLINSDIV',0,1
	.word	493
	.byte	4,4,2,35,3,11
	.byte	'INSEL',0,1
	.word	493
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON1_Bits',0,10,126,3
	.word	23858
	.byte	10
	.byte	'_Ifx_SCU_CCUCON2_Bits',0,10,129,1,16,4,11
	.byte	'BBBDIV',0,1
	.word	493
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	470
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON2_Bits',0,10,135,1,3
	.word	24096
	.byte	10
	.byte	'_Ifx_SCU_CCUCON3_Bits',0,10,138,1,16,4,11
	.byte	'PLLDIV',0,1
	.word	493
	.byte	6,2,2,35,0,11
	.byte	'PLLSEL',0,1
	.word	493
	.byte	2,0,2,35,0,11
	.byte	'PLLERAYDIV',0,1
	.word	493
	.byte	6,2,2,35,1,11
	.byte	'PLLERAYSEL',0,1
	.word	493
	.byte	2,0,2,35,1,11
	.byte	'SRIDIV',0,1
	.word	493
	.byte	6,2,2,35,2,11
	.byte	'SRISEL',0,1
	.word	493
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON3_Bits',0,10,150,1,3
	.word	24224
	.byte	10
	.byte	'_Ifx_SCU_CCUCON4_Bits',0,10,153,1,16,4,11
	.byte	'SPBDIV',0,1
	.word	493
	.byte	6,2,2,35,0,11
	.byte	'SPBSEL',0,1
	.word	493
	.byte	2,0,2,35,0,11
	.byte	'GTMDIV',0,1
	.word	493
	.byte	6,2,2,35,1,11
	.byte	'GTMSEL',0,1
	.word	493
	.byte	2,0,2,35,1,11
	.byte	'STMDIV',0,1
	.word	493
	.byte	6,2,2,35,2,11
	.byte	'STMSEL',0,1
	.word	493
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON4_Bits',0,10,165,1,3
	.word	24467
	.byte	10
	.byte	'_Ifx_SCU_CCUCON5_Bits',0,10,168,1,16,4,11
	.byte	'MAXDIV',0,1
	.word	493
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	470
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON5_Bits',0,10,174,1,3
	.word	24702
	.byte	10
	.byte	'_Ifx_SCU_CCUCON6_Bits',0,10,177,1,16,4,11
	.byte	'CPU0DIV',0,1
	.word	493
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	470
	.byte	26,0,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON6_Bits',0,10,181,1,3
	.word	24830
	.byte	10
	.byte	'_Ifx_SCU_CCUCON7_Bits',0,10,184,1,16,4,11
	.byte	'CPU1DIV',0,1
	.word	493
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	470
	.byte	26,0,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON7_Bits',0,10,188,1,3
	.word	24930
	.byte	10
	.byte	'_Ifx_SCU_CHIPID_Bits',0,10,191,1,16,4,11
	.byte	'CHREV',0,1
	.word	493
	.byte	6,2,2,35,0,11
	.byte	'CHTEC',0,1
	.word	493
	.byte	2,0,2,35,0,11
	.byte	'CHID',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'EEA',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'UCODE',0,1
	.word	493
	.byte	7,0,2,35,2,11
	.byte	'FSIZE',0,1
	.word	493
	.byte	4,4,2,35,3,11
	.byte	'SP',0,1
	.word	493
	.byte	2,2,2,35,3,11
	.byte	'SEC',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CHIPID_Bits',0,10,202,1,3
	.word	25030
	.byte	10
	.byte	'_Ifx_SCU_DTSCON_Bits',0,10,205,1,16,4,11
	.byte	'PWD',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'START',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	493
	.byte	2,4,2,35,0,11
	.byte	'CAL',0,4
	.word	470
	.byte	20,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	7,1,2,35,3,11
	.byte	'SLCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_DTSCON_Bits',0,10,213,1,3
	.word	25238
	.byte	10
	.byte	'_Ifx_SCU_DTSLIM_Bits',0,10,216,1,16,4,11
	.byte	'LOWER',0,2
	.word	510
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	493
	.byte	5,1,2,35,1,11
	.byte	'LLU',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'UPPER',0,2
	.word	510
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	493
	.byte	4,2,2,35,3,11
	.byte	'SLCK',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'UOF',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_DTSLIM_Bits',0,10,225,1,3
	.word	25403
	.byte	10
	.byte	'_Ifx_SCU_DTSSTAT_Bits',0,10,228,1,16,4,11
	.byte	'RESULT',0,2
	.word	510
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	493
	.byte	4,2,2,35,1,11
	.byte	'RDY',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'BUSY',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_DTSSTAT_Bits',0,10,235,1,3
	.word	25586
	.byte	10
	.byte	'_Ifx_SCU_EICR_Bits',0,10,238,1,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	4,4,2,35,0,11
	.byte	'EXIS0',0,1
	.word	493
	.byte	3,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'FEN0',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'REN0',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'LDEN0',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'EIEN0',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'INP0',0,1
	.word	493
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	470
	.byte	5,12,2,35,0,11
	.byte	'EXIS1',0,1
	.word	493
	.byte	3,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'FEN1',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'REN1',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'LDEN1',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'EIEN1',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'INP1',0,1
	.word	493
	.byte	3,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EICR_Bits',0,10,129,2,3
	.word	25740
	.byte	10
	.byte	'_Ifx_SCU_EIFR_Bits',0,10,132,2,16,4,11
	.byte	'INTF0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'INTF1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'INTF2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'INTF3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'INTF4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'INTF5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'INTF6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'INTF7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	470
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_SCU_EIFR_Bits',0,10,143,2,3
	.word	26104
	.byte	10
	.byte	'_Ifx_SCU_EMSR_Bits',0,10,146,2,16,4,11
	.byte	'POL',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'MODE',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'ENON',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'PSEL',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	510
	.byte	12,0,2,35,0,11
	.byte	'EMSF',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'SEMSF',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	493
	.byte	6,0,2,35,2,11
	.byte	'EMSFM',0,1
	.word	493
	.byte	2,6,2,35,3,11
	.byte	'SEMSFM',0,1
	.word	493
	.byte	2,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	493
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_EMSR_Bits',0,10,159,2,3
	.word	26315
	.byte	10
	.byte	'_Ifx_SCU_ESRCFG_Bits',0,10,162,2,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	7,1,2,35,0,11
	.byte	'EDCON',0,2
	.word	510
	.byte	2,7,2,35,0,11
	.byte	'reserved_9',0,4
	.word	470
	.byte	23,0,2,35,0,0,21
	.byte	'Ifx_SCU_ESRCFG_Bits',0,10,167,2,3
	.word	26567
	.byte	10
	.byte	'_Ifx_SCU_ESROCFG_Bits',0,10,170,2,16,4,11
	.byte	'ARI',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'ARC',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	470
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_ESROCFG_Bits',0,10,175,2,3
	.word	26685
	.byte	10
	.byte	'_Ifx_SCU_EVR13CON_Bits',0,10,178,2,16,4,11
	.byte	'reserved_0',0,4
	.word	470
	.byte	28,4,2,35,0,11
	.byte	'EVR13OFF',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'BPEVR13OFF',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVR13CON_Bits',0,10,185,2,3
	.word	26796
	.byte	10
	.byte	'_Ifx_SCU_EVR33CON_Bits',0,10,188,2,16,4,11
	.byte	'reserved_0',0,4
	.word	470
	.byte	28,4,2,35,0,11
	.byte	'EVR33OFF',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'BPEVR33OFF',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVR33CON_Bits',0,10,195,2,3
	.word	26959
	.byte	10
	.byte	'_Ifx_SCU_EVRADCSTAT_Bits',0,10,198,2,16,4,11
	.byte	'ADC13V',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'ADC33V',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'ADCSWDV',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRADCSTAT_Bits',0,10,205,2,3
	.word	27122
	.byte	10
	.byte	'_Ifx_SCU_EVRDVSTAT_Bits',0,10,208,2,16,4,11
	.byte	'DVS13TRIM',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'DVS33TRIM',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRDVSTAT_Bits',0,10,215,2,3
	.word	27280
	.byte	10
	.byte	'_Ifx_SCU_EVRMONCTRL_Bits',0,10,218,2,16,4,11
	.byte	'EVR13OVMOD',0,1
	.word	493
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	493
	.byte	2,4,2,35,0,11
	.byte	'EVR13UVMOD',0,1
	.word	493
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	493
	.byte	2,0,2,35,0,11
	.byte	'EVR33OVMOD',0,1
	.word	493
	.byte	2,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	493
	.byte	2,4,2,35,1,11
	.byte	'EVR33UVMOD',0,1
	.word	493
	.byte	2,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	493
	.byte	2,0,2,35,1,11
	.byte	'SWDOVMOD',0,1
	.word	493
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	493
	.byte	2,4,2,35,2,11
	.byte	'SWDUVMOD',0,1
	.word	493
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,2
	.word	510
	.byte	10,0,2,35,2,0,21
	.byte	'Ifx_SCU_EVRMONCTRL_Bits',0,10,232,2,3
	.word	27445
	.byte	10
	.byte	'_Ifx_SCU_EVROSCCTRL_Bits',0,10,235,2,16,4,11
	.byte	'OSCTRIM',0,2
	.word	510
	.byte	10,6,2,35,0,11
	.byte	'OSCPTAT',0,1
	.word	493
	.byte	6,0,2,35,1,11
	.byte	'OSCANASEL',0,1
	.word	493
	.byte	4,4,2,35,2,11
	.byte	'HPBGTRIM',0,2
	.word	510
	.byte	7,5,2,35,2,11
	.byte	'HPBGCLKEN',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'OSC3V3',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	493
	.byte	2,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVROSCCTRL_Bits',0,10,245,2,3
	.word	27774
	.byte	10
	.byte	'_Ifx_SCU_EVROVMON_Bits',0,10,248,2,16,4,11
	.byte	'EVR13OVVAL',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'EVR33OVVAL',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'SWDOVVAL',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVROVMON_Bits',0,10,255,2,3
	.word	27995
	.byte	10
	.byte	'_Ifx_SCU_EVRRSTCON_Bits',0,10,130,3,16,4,11
	.byte	'RST13TRIM',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	470
	.byte	16,8,2,35,0,11
	.byte	'RST13OFF',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'BPRST13OFF',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'RST33OFF',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'BPRST33OFF',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'RSTSWDOFF',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'BPRSTSWDOFF',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRRSTCON_Bits',0,10,142,3,3
	.word	28158
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF1_Bits',0,10,145,3,16,4,11
	.byte	'SD5P',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'SD5I',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'SD5D',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF1_Bits',0,10,152,3,3
	.word	28430
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF2_Bits',0,10,155,3,16,4,11
	.byte	'SD33P',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'SD33I',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'SD33D',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF2_Bits',0,10,162,3,3
	.word	28583
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF3_Bits',0,10,165,3,16,4,11
	.byte	'CT5REG0',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'CT5REG1',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'CT5REG2',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF3_Bits',0,10,172,3,3
	.word	28739
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF4_Bits',0,10,175,3,16,4,11
	.byte	'CT5REG3',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'CT5REG4',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF4_Bits',0,10,181,3,3
	.word	28901
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF5_Bits',0,10,184,3,16,4,11
	.byte	'CT33REG0',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'CT33REG1',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'CT33REG2',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF5_Bits',0,10,191,3,3
	.word	29044
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF6_Bits',0,10,194,3,16,4,11
	.byte	'CT33REG3',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'CT33REG4',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF6_Bits',0,10,200,3,3
	.word	29209
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL1_Bits',0,10,203,3,16,4,11
	.byte	'SDFREQSPRD',0,2
	.word	510
	.byte	16,0,2,35,0,11
	.byte	'SDFREQ',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'SDSTEP',0,1
	.word	493
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	493
	.byte	2,2,2,35,3,11
	.byte	'SDSAMPLE',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCTRL1_Bits',0,10,211,3,3
	.word	29354
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL2_Bits',0,10,214,3,16,4,11
	.byte	'DRVP',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'SDMINMAXDC',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'DRVN',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'SDLUT',0,1
	.word	493
	.byte	6,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCTRL2_Bits',0,10,222,3,3
	.word	29535
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL3_Bits',0,10,225,3,16,4,11
	.byte	'SDPWMPRE',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'SDPID',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'SDVOKLVL',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCTRL3_Bits',0,10,232,3,3
	.word	29709
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL4_Bits',0,10,235,3,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'SYNCDIV',0,1
	.word	493
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	470
	.byte	20,1,2,35,0,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCTRL4_Bits',0,10,241,3,3
	.word	29869
	.byte	10
	.byte	'_Ifx_SCU_EVRSTAT_Bits',0,10,244,3,16,4,11
	.byte	'EVR13',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'OV13',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'EVR33',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'OV33',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'OVSWD',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'UV13',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'UV33',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'UVSWD',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'EXTPASS13',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'EXTPASS33',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'BGPROK',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	470
	.byte	21,0,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSTAT_Bits',0,10,130,4,3
	.word	30013
	.byte	10
	.byte	'_Ifx_SCU_EVRTRIM_Bits',0,10,133,4,16,4,11
	.byte	'EVR13TRIM',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'SDVOUTSEL',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRTRIM_Bits',0,10,139,4,3
	.word	30287
	.byte	10
	.byte	'_Ifx_SCU_EVRUVMON_Bits',0,10,142,4,16,4,11
	.byte	'EVR13UVVAL',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'EVR33UVVAL',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'SWDUVVAL',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRUVMON_Bits',0,10,149,4,3
	.word	30426
	.byte	10
	.byte	'_Ifx_SCU_EXTCON_Bits',0,10,152,4,16,4,11
	.byte	'EN0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'SEL0',0,1
	.word	493
	.byte	4,2,2,35,0,11
	.byte	'reserved_6',0,2
	.word	510
	.byte	10,0,2,35,0,11
	.byte	'EN1',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'NSEL',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'SEL1',0,1
	.word	493
	.byte	4,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	493
	.byte	2,0,2,35,2,11
	.byte	'DIV1',0,1
	.word	493
	.byte	8,0,2,35,3,0,21
	.byte	'Ifx_SCU_EXTCON_Bits',0,10,163,4,3
	.word	30589
	.byte	10
	.byte	'_Ifx_SCU_FDR_Bits',0,10,166,4,16,4,11
	.byte	'STEP',0,2
	.word	510
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	493
	.byte	4,2,2,35,1,11
	.byte	'DM',0,1
	.word	493
	.byte	2,0,2,35,1,11
	.byte	'RESULT',0,2
	.word	510
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	493
	.byte	5,1,2,35,3,11
	.byte	'DISCLK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_FDR_Bits',0,10,174,4,3
	.word	30807
	.byte	10
	.byte	'_Ifx_SCU_FMR_Bits',0,10,177,4,16,4,11
	.byte	'FS0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'FS1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'FS2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'FS3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'FS4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'FS5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'FS6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'FS7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'FC0',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'FC1',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'FC2',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'FC3',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'FC4',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'FC5',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'FC6',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'FC7',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	8,0,2,35,3,0,21
	.byte	'Ifx_SCU_FMR_Bits',0,10,197,4,3
	.word	30970
	.byte	10
	.byte	'_Ifx_SCU_ID_Bits',0,10,200,4,16,4,11
	.byte	'MODREV',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_ID_Bits',0,10,205,4,3
	.word	31306
	.byte	10
	.byte	'_Ifx_SCU_IGCR_Bits',0,10,208,4,16,4,11
	.byte	'IPEN00',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'IPEN01',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'IPEN02',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'IPEN03',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'IPEN04',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'IPEN05',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'IPEN06',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'IPEN07',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	493
	.byte	5,3,2,35,1,11
	.byte	'GEEN0',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'IGP0',0,1
	.word	493
	.byte	2,0,2,35,1,11
	.byte	'IPEN10',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'IPEN11',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'IPEN12',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'IPEN13',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'IPEN14',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'IPEN15',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'IPEN16',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'IPEN17',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	5,3,2,35,3,11
	.byte	'GEEN1',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'IGP1',0,1
	.word	493
	.byte	2,0,2,35,3,0,21
	.byte	'Ifx_SCU_IGCR_Bits',0,10,232,4,3
	.word	31413
	.byte	10
	.byte	'_Ifx_SCU_IN_Bits',0,10,235,4,16,4,11
	.byte	'P0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	470
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_IN_Bits',0,10,240,4,3
	.word	31865
	.byte	10
	.byte	'_Ifx_SCU_IOCR_Bits',0,10,243,4,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	4,4,2,35,0,11
	.byte	'PC0',0,1
	.word	493
	.byte	4,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	493
	.byte	4,4,2,35,1,11
	.byte	'PC1',0,1
	.word	493
	.byte	4,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_IOCR_Bits',0,10,250,4,3
	.word	31964
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL0_Bits',0,10,253,4,16,4,11
	.byte	'LBISTREQ',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'LBISTREQP',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'PATTERNS',0,2
	.word	510
	.byte	14,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_LBISTCTRL0_Bits',0,10,131,5,3
	.word	32114
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL1_Bits',0,10,134,5,16,4,11
	.byte	'SEED',0,4
	.word	470
	.byte	23,9,2,35,0,11
	.byte	'reserved_23',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'SPLITSH',0,1
	.word	493
	.byte	3,5,2,35,3,11
	.byte	'BODY',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'LBISTFREQU',0,1
	.word	493
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_LBISTCTRL1_Bits',0,10,141,5,3
	.word	32263
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL2_Bits',0,10,144,5,16,4,11
	.byte	'SIGNATURE',0,4
	.word	470
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	7,1,2,35,3,11
	.byte	'LBISTDONE',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_LBISTCTRL2_Bits',0,10,149,5,3
	.word	32424
	.byte	10
	.byte	'_Ifx_SCU_LCLCON_Bits',0,10,152,5,16,4,11
	.byte	'reserved_0',0,2
	.word	510
	.byte	16,0,2,35,0,11
	.byte	'LS',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,2
	.word	510
	.byte	14,1,2,35,2,11
	.byte	'LSEN',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_LCLCON_Bits',0,10,158,5,3
	.word	32554
	.byte	10
	.byte	'_Ifx_SCU_LCLTEST_Bits',0,10,161,5,16,4,11
	.byte	'LCLT0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'LCLT1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	470
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_LCLTEST_Bits',0,10,166,5,3
	.word	32686
	.byte	10
	.byte	'_Ifx_SCU_MANID_Bits',0,10,169,5,16,4,11
	.byte	'DEPT',0,1
	.word	493
	.byte	5,3,2,35,0,11
	.byte	'MANUF',0,2
	.word	510
	.byte	11,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_MANID_Bits',0,10,174,5,3
	.word	32801
	.byte	10
	.byte	'_Ifx_SCU_OMR_Bits',0,10,177,5,16,4,11
	.byte	'PS0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	510
	.byte	14,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	510
	.byte	14,0,2,35,2,0,21
	.byte	'Ifx_SCU_OMR_Bits',0,10,185,5,3
	.word	32912
	.byte	10
	.byte	'_Ifx_SCU_OSCCON_Bits',0,10,188,5,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'PLLLV',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'OSCRES',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'GAINSEL',0,1
	.word	493
	.byte	2,3,2,35,0,11
	.byte	'MODE',0,1
	.word	493
	.byte	2,1,2,35,0,11
	.byte	'SHBY',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'PLLHV',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'X1D',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'X1DEN',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	493
	.byte	4,0,2,35,1,11
	.byte	'OSCVAL',0,1
	.word	493
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	493
	.byte	2,1,2,35,2,11
	.byte	'APREN',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'CAP0EN',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'CAP1EN',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'CAP2EN',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'CAP3EN',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	493
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_OSCCON_Bits',0,10,209,5,3
	.word	33070
	.byte	10
	.byte	'_Ifx_SCU_OUT_Bits',0,10,212,5,16,4,11
	.byte	'P0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	470
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_OUT_Bits',0,10,217,5,3
	.word	33482
	.byte	10
	.byte	'_Ifx_SCU_OVCCON_Bits',0,10,220,5,16,4,11
	.byte	'CSEL0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'CSEL1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'CSEL2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,2
	.word	510
	.byte	13,0,2,35,0,11
	.byte	'OVSTRT',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'OVSTP',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'DCINVAL',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	493
	.byte	5,0,2,35,2,11
	.byte	'OVCONF',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'POVCONF',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	493
	.byte	6,0,2,35,3,0,21
	.byte	'Ifx_SCU_OVCCON_Bits',0,10,233,5,3
	.word	33583
	.byte	10
	.byte	'_Ifx_SCU_OVCENABLE_Bits',0,10,236,5,16,4,11
	.byte	'OVEN0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'OVEN1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'OVEN2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	470
	.byte	29,0,2,35,0,0,21
	.byte	'Ifx_SCU_OVCENABLE_Bits',0,10,242,5,3
	.word	33850
	.byte	10
	.byte	'_Ifx_SCU_PDISC_Bits',0,10,245,5,16,4,11
	.byte	'PDIS0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	470
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_PDISC_Bits',0,10,250,5,3
	.word	33986
	.byte	10
	.byte	'_Ifx_SCU_PDR_Bits',0,10,253,5,16,4,11
	.byte	'PD0',0,1
	.word	493
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	493
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	470
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_SCU_PDR_Bits',0,10,132,6,3
	.word	34097
	.byte	10
	.byte	'_Ifx_SCU_PDRR_Bits',0,10,135,6,16,4,11
	.byte	'PDR0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'PDR1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'PDR2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'PDR3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'PDR4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'PDR5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'PDR6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'PDR7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	470
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_SCU_PDRR_Bits',0,10,146,6,3
	.word	34230
	.byte	10
	.byte	'_Ifx_SCU_PLLCON0_Bits',0,10,149,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'MODEN',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	510
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	493
	.byte	7,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	493
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	493
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	493
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_PLLCON0_Bits',0,10,166,6,3
	.word	34433
	.byte	10
	.byte	'_Ifx_SCU_PLLCON1_Bits',0,10,169,6,16,4,11
	.byte	'K2DIV',0,1
	.word	493
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	493
	.byte	7,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	493
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	510
	.byte	9,0,2,35,2,0,21
	.byte	'Ifx_SCU_PLLCON1_Bits',0,10,177,6,3
	.word	34789
	.byte	10
	.byte	'_Ifx_SCU_PLLCON2_Bits',0,10,180,6,16,4,11
	.byte	'MODCFG',0,2
	.word	510
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_PLLCON2_Bits',0,10,184,6,3
	.word	34967
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON0_Bits',0,10,187,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	493
	.byte	2,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	510
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	493
	.byte	5,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	493
	.byte	2,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	493
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	493
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	493
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_PLLERAYCON0_Bits',0,10,204,6,3
	.word	35067
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON1_Bits',0,10,207,6,16,4,11
	.byte	'K2DIV',0,1
	.word	493
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	493
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	493
	.byte	4,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	493
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	510
	.byte	9,0,2,35,2,0,21
	.byte	'Ifx_SCU_PLLERAYCON1_Bits',0,10,215,6,3
	.word	35437
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYSTAT_Bits',0,10,218,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'PWDSTAT',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	470
	.byte	26,0,2,35,0,0,21
	.byte	'Ifx_SCU_PLLERAYSTAT_Bits',0,10,227,6,3
	.word	35623
	.byte	10
	.byte	'_Ifx_SCU_PLLSTAT_Bits',0,10,230,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'MODRUN',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	470
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_SCU_PLLSTAT_Bits',0,10,241,6,3
	.word	35821
	.byte	10
	.byte	'_Ifx_SCU_PMCSR_Bits',0,10,244,6,16,4,11
	.byte	'REQSLP',0,1
	.word	493
	.byte	2,6,2,35,0,11
	.byte	'SMUSLP',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	493
	.byte	5,0,2,35,0,11
	.byte	'PMST',0,1
	.word	493
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	470
	.byte	21,0,2,35,0,0,21
	.byte	'Ifx_SCU_PMCSR_Bits',0,10,251,6,3
	.word	36054
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR0_Bits',0,10,254,6,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'ESR1WKEN',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'PINAWKEN',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'PINBWKEN',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'ESR0DFEN',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'ESR0EDCON',0,1
	.word	493
	.byte	2,1,2,35,0,11
	.byte	'ESR1DFEN',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'ESR1EDCON',0,1
	.word	493
	.byte	2,6,2,35,1,11
	.byte	'PINADFEN',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'PINAEDCON',0,1
	.word	493
	.byte	2,3,2,35,1,11
	.byte	'PINBDFEN',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'PINBEDCON',0,1
	.word	493
	.byte	2,0,2,35,1,11
	.byte	'SCREN',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'STBYRAMSEL',0,1
	.word	493
	.byte	2,5,2,35,2,11
	.byte	'SCRCLKSEL',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'TRISTEN',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'TRISTREQ',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'PORSTDF',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'DCDCSYNC',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	493
	.byte	3,3,2,35,3,11
	.byte	'ESR0TRIST',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_PMSWCR0_Bits',0,10,153,7,3
	.word	36206
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR1_Bits',0,10,156,7,16,4,11
	.byte	'SCRSTEN',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'SCRSTREQ',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	493
	.byte	6,0,2,35,0,11
	.byte	'CPUIDLSEL',0,1
	.word	493
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'IRADIS',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	493
	.byte	3,0,2,35,1,11
	.byte	'SCRCFG',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'CPUSEL',0,1
	.word	493
	.byte	3,5,2,35,3,11
	.byte	'STBYEVEN',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'STBYEV',0,1
	.word	493
	.byte	3,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_PMSWCR1_Bits',0,10,170,7,3
	.word	36773
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR2_Bits',0,10,173,7,16,4,11
	.byte	'SCRINT',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'BUSY',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'SCRECC',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'SCRWDT',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'SCRRST',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	493
	.byte	4,0,2,35,1,11
	.byte	'TCINT',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'TCINTREQ',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'SMURST',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'RST',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	493
	.byte	4,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_PMSWCR2_Bits',0,10,187,7,3
	.word	37067
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTAT_Bits',0,10,190,7,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKP',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUN',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'PINAWKP',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUN',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'PINBWKP',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUN',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'PORSTDF',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'HWCFGEVR',0,1
	.word	493
	.byte	3,3,2,35,1,11
	.byte	'STBYRAM',0,1
	.word	493
	.byte	2,1,2,35,1,11
	.byte	'TRIST',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'SCRST',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'SCRWKP',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'SCR',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'ESR1WKEN',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'PINAWKEN',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'PINBWKEN',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	510
	.byte	4,5,2,35,2,11
	.byte	'ESR0TRIST',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	493
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_PMSWSTAT_Bits',0,10,214,7,3
	.word	37345
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTATCLR_Bits',0,10,217,7,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKPCLR',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUNCLR',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'PINAWKPCLR',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUNCLR',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'PINBWKPCLR',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUNCLR',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'SCRSTCLR',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'SCRWKPCLR',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	510
	.byte	14,0,2,35,2,0,21
	.byte	'Ifx_SCU_PMSWSTATCLR_Bits',0,10,230,7,3
	.word	37841
	.byte	10
	.byte	'_Ifx_SCU_RSTCON2_Bits',0,10,233,7,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'CLRC',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	510
	.byte	10,4,2,35,0,11
	.byte	'CSS0',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'CSS1',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'CSS2',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'USRINFO',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_RSTCON2_Bits',0,10,243,7,3
	.word	38154
	.byte	10
	.byte	'_Ifx_SCU_RSTCON_Bits',0,10,246,7,16,4,11
	.byte	'ESR0',0,1
	.word	493
	.byte	2,6,2,35,0,11
	.byte	'ESR1',0,1
	.word	493
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	493
	.byte	2,2,2,35,0,11
	.byte	'SMU',0,1
	.word	493
	.byte	2,0,2,35,0,11
	.byte	'SW',0,1
	.word	493
	.byte	2,6,2,35,1,11
	.byte	'STM0',0,1
	.word	493
	.byte	2,4,2,35,1,11
	.byte	'STM1',0,1
	.word	493
	.byte	2,2,2,35,1,11
	.byte	'STM2',0,1
	.word	493
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_RSTCON_Bits',0,10,129,8,3
	.word	38363
	.byte	10
	.byte	'_Ifx_SCU_RSTSTAT_Bits',0,10,132,8,16,4,11
	.byte	'ESR0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'ESR1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'SMU',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'SW',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'STM0',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'STM1',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'STM2',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'PORST',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'CB0',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'CB1',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'CB3',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	493
	.byte	2,1,2,35,2,11
	.byte	'EVR13',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'EVR33',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'SWD',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	493
	.byte	2,4,2,35,3,11
	.byte	'STBYR',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	493
	.byte	3,0,2,35,3,0,21
	.byte	'Ifx_SCU_RSTSTAT_Bits',0,10,155,8,3
	.word	38574
	.byte	10
	.byte	'_Ifx_SCU_SAFECON_Bits',0,10,158,8,16,4,11
	.byte	'HBT',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	470
	.byte	31,0,2,35,0,0,21
	.byte	'Ifx_SCU_SAFECON_Bits',0,10,162,8,3
	.word	39006
	.byte	10
	.byte	'_Ifx_SCU_STSTAT_Bits',0,10,165,8,16,4,11
	.byte	'HWCFG',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'FTM',0,1
	.word	493
	.byte	7,1,2,35,1,11
	.byte	'MODE',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'FCBAE',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'LUDIS',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'TRSTL',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'SPDEN',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	493
	.byte	3,0,2,35,2,11
	.byte	'RAMINT',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	493
	.byte	7,0,2,35,3,0,21
	.byte	'Ifx_SCU_STSTAT_Bits',0,10,178,8,3
	.word	39102
	.byte	10
	.byte	'_Ifx_SCU_SWRSTCON_Bits',0,10,181,8,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'SWRSTREQ',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	470
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_SWRSTCON_Bits',0,10,186,8,3
	.word	39362
	.byte	10
	.byte	'_Ifx_SCU_SYSCON_Bits',0,10,189,8,16,4,11
	.byte	'CCTRIG0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'RAMINTM',0,1
	.word	493
	.byte	2,4,2,35,0,11
	.byte	'SETLUDIS',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	493
	.byte	3,0,2,35,0,11
	.byte	'DATM',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,4
	.word	470
	.byte	23,0,2,35,0,0,21
	.byte	'Ifx_SCU_SYSCON_Bits',0,10,198,8,3
	.word	39487
	.byte	10
	.byte	'_Ifx_SCU_TRAPCLR_Bits',0,10,201,8,16,4,11
	.byte	'ESR0T',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	470
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPCLR_Bits',0,10,208,8,3
	.word	39684
	.byte	10
	.byte	'_Ifx_SCU_TRAPDIS_Bits',0,10,211,8,16,4,11
	.byte	'ESR0T',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	470
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPDIS_Bits',0,10,218,8,3
	.word	39837
	.byte	10
	.byte	'_Ifx_SCU_TRAPSET_Bits',0,10,221,8,16,4,11
	.byte	'ESR0T',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	470
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPSET_Bits',0,10,228,8,3
	.word	39990
	.byte	10
	.byte	'_Ifx_SCU_TRAPSTAT_Bits',0,10,231,8,16,4,11
	.byte	'ESR0T',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	470
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPSTAT_Bits',0,10,238,8,3
	.word	40143
	.byte	7
	.byte	'unsigned int',0,4,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON0_Bits',0,10,241,8,16,4,11
	.byte	'ENDINIT',0,4
	.word	40298
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	40298
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	40298
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	40298
	.byte	16,0,2,35,0,0,21
	.byte	'Ifx_SCU_WDTCPU_CON0_Bits',0,10,247,8,3
	.word	40314
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU_CON1_Bits',0,10,250,8,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	2,6,2,35,0,11
	.byte	'IR0',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	493
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_WDTCPU_CON1_Bits',0,10,134,9,3
	.word	40444
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU_SR_Bits',0,10,137,9,16,4,11
	.byte	'AE',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	493
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_WDTCPU_SR_Bits',0,10,150,9,3
	.word	40682
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON0_Bits',0,10,153,9,16,4,11
	.byte	'ENDINIT',0,4
	.word	40298
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	40298
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	40298
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	40298
	.byte	16,0,2,35,0,0,21
	.byte	'Ifx_SCU_WDTS_CON0_Bits',0,10,159,9,3
	.word	40905
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON1_Bits',0,10,162,9,16,4,11
	.byte	'CLRIRF',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'IR0',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	493
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_WDTS_CON1_Bits',0,10,175,9,3
	.word	41031
	.byte	10
	.byte	'_Ifx_SCU_WDTS_SR_Bits',0,10,178,9,16,4,11
	.byte	'AE',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	493
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_WDTS_SR_Bits',0,10,191,9,3
	.word	41283
	.byte	12,10,199,9,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22808
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ACCEN0',0,10,204,9,3
	.word	41502
	.byte	12,10,207,9,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23365
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ACCEN1',0,10,212,9,3
	.word	41566
	.byte	12,10,215,9,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23442
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ARSTDIS',0,10,220,9,3
	.word	41630
	.byte	12,10,223,9,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23578
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON0',0,10,228,9,3
	.word	41695
	.byte	12,10,231,9,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23858
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON1',0,10,236,9,3
	.word	41760
	.byte	12,10,239,9,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24096
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON2',0,10,244,9,3
	.word	41825
	.byte	12,10,247,9,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24224
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON3',0,10,252,9,3
	.word	41890
	.byte	12,10,255,9,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24467
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON4',0,10,132,10,3
	.word	41955
	.byte	12,10,135,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24702
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON5',0,10,140,10,3
	.word	42020
	.byte	12,10,143,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24830
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON6',0,10,148,10,3
	.word	42085
	.byte	12,10,151,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24930
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON7',0,10,156,10,3
	.word	42150
	.byte	12,10,159,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25030
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CHIPID',0,10,164,10,3
	.word	42215
	.byte	12,10,167,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25238
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_DTSCON',0,10,172,10,3
	.word	42279
	.byte	12,10,175,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25403
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_DTSLIM',0,10,180,10,3
	.word	42343
	.byte	12,10,183,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25586
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_DTSSTAT',0,10,188,10,3
	.word	42407
	.byte	12,10,191,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25740
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EICR',0,10,196,10,3
	.word	42472
	.byte	12,10,199,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26104
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EIFR',0,10,204,10,3
	.word	42534
	.byte	12,10,207,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26315
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EMSR',0,10,212,10,3
	.word	42596
	.byte	12,10,215,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26567
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ESRCFG',0,10,220,10,3
	.word	42658
	.byte	12,10,223,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26685
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ESROCFG',0,10,228,10,3
	.word	42722
	.byte	12,10,231,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26796
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVR13CON',0,10,236,10,3
	.word	42787
	.byte	12,10,239,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26959
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVR33CON',0,10,244,10,3
	.word	42853
	.byte	12,10,247,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27122
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRADCSTAT',0,10,252,10,3
	.word	42919
	.byte	12,10,255,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27280
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRDVSTAT',0,10,132,11,3
	.word	42987
	.byte	12,10,135,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27445
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRMONCTRL',0,10,140,11,3
	.word	43054
	.byte	12,10,143,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27774
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVROSCCTRL',0,10,148,11,3
	.word	43122
	.byte	12,10,151,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27995
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVROVMON',0,10,156,11,3
	.word	43190
	.byte	12,10,159,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28158
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRRSTCON',0,10,164,11,3
	.word	43256
	.byte	12,10,167,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28430
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF1',0,10,172,11,3
	.word	43323
	.byte	12,10,175,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28583
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF2',0,10,180,11,3
	.word	43392
	.byte	12,10,183,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28739
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF3',0,10,188,11,3
	.word	43461
	.byte	12,10,191,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28901
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF4',0,10,196,11,3
	.word	43530
	.byte	12,10,199,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29044
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF5',0,10,204,11,3
	.word	43599
	.byte	12,10,207,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29209
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF6',0,10,212,11,3
	.word	43668
	.byte	12,10,215,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29354
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCTRL1',0,10,220,11,3
	.word	43737
	.byte	12,10,223,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29535
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCTRL2',0,10,228,11,3
	.word	43805
	.byte	12,10,231,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29709
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCTRL3',0,10,236,11,3
	.word	43873
	.byte	12,10,239,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29869
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCTRL4',0,10,244,11,3
	.word	43941
	.byte	12,10,247,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30013
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSTAT',0,10,252,11,3
	.word	44009
	.byte	12,10,255,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30287
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRTRIM',0,10,132,12,3
	.word	44074
	.byte	12,10,135,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30426
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRUVMON',0,10,140,12,3
	.word	44139
	.byte	12,10,143,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30589
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EXTCON',0,10,148,12,3
	.word	44205
	.byte	12,10,151,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30807
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_FDR',0,10,156,12,3
	.word	44269
	.byte	12,10,159,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30970
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_FMR',0,10,164,12,3
	.word	44330
	.byte	12,10,167,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31306
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ID',0,10,172,12,3
	.word	44391
	.byte	12,10,175,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31413
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_IGCR',0,10,180,12,3
	.word	44451
	.byte	12,10,183,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31865
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_IN',0,10,188,12,3
	.word	44513
	.byte	12,10,191,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31964
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_IOCR',0,10,196,12,3
	.word	44573
	.byte	12,10,199,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32114
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_LBISTCTRL0',0,10,204,12,3
	.word	44635
	.byte	12,10,207,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32263
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_LBISTCTRL1',0,10,212,12,3
	.word	44703
	.byte	12,10,215,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32424
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_LBISTCTRL2',0,10,220,12,3
	.word	44771
	.byte	12,10,223,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32554
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_LCLCON',0,10,228,12,3
	.word	44839
	.byte	12,10,231,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32686
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_LCLTEST',0,10,236,12,3
	.word	44903
	.byte	12,10,239,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32801
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_MANID',0,10,244,12,3
	.word	44968
	.byte	12,10,247,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32912
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_OMR',0,10,252,12,3
	.word	45031
	.byte	12,10,255,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33070
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_OSCCON',0,10,132,13,3
	.word	45092
	.byte	12,10,135,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33482
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_OUT',0,10,140,13,3
	.word	45156
	.byte	12,10,143,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33583
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_OVCCON',0,10,148,13,3
	.word	45217
	.byte	12,10,151,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33850
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_OVCENABLE',0,10,156,13,3
	.word	45281
	.byte	12,10,159,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33986
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PDISC',0,10,164,13,3
	.word	45348
	.byte	12,10,167,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34097
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PDR',0,10,172,13,3
	.word	45411
	.byte	12,10,175,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34230
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PDRR',0,10,180,13,3
	.word	45472
	.byte	12,10,183,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34433
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLCON0',0,10,188,13,3
	.word	45534
	.byte	12,10,191,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34789
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLCON1',0,10,196,13,3
	.word	45599
	.byte	12,10,199,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34967
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLCON2',0,10,204,13,3
	.word	45664
	.byte	12,10,207,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35067
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLERAYCON0',0,10,212,13,3
	.word	45729
	.byte	12,10,215,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35437
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLERAYCON1',0,10,220,13,3
	.word	45798
	.byte	12,10,223,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35623
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLERAYSTAT',0,10,228,13,3
	.word	45867
	.byte	12,10,231,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35821
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLSTAT',0,10,236,13,3
	.word	45936
	.byte	12,10,239,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36054
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMCSR',0,10,244,13,3
	.word	46001
	.byte	12,10,247,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36206
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMSWCR0',0,10,252,13,3
	.word	46064
	.byte	12,10,255,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36773
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMSWCR1',0,10,132,14,3
	.word	46129
	.byte	12,10,135,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37067
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMSWCR2',0,10,140,14,3
	.word	46194
	.byte	12,10,143,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37345
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMSWSTAT',0,10,148,14,3
	.word	46259
	.byte	12,10,151,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37841
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMSWSTATCLR',0,10,156,14,3
	.word	46325
	.byte	12,10,159,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38363
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_RSTCON',0,10,164,14,3
	.word	46394
	.byte	12,10,167,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38154
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_RSTCON2',0,10,172,14,3
	.word	46458
	.byte	12,10,175,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38574
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_RSTSTAT',0,10,180,14,3
	.word	46523
	.byte	12,10,183,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39006
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_SAFECON',0,10,188,14,3
	.word	46588
	.byte	12,10,191,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39102
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_STSTAT',0,10,196,14,3
	.word	46653
	.byte	12,10,199,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39362
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_SWRSTCON',0,10,204,14,3
	.word	46717
	.byte	12,10,207,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39487
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_SYSCON',0,10,212,14,3
	.word	46783
	.byte	12,10,215,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39684
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPCLR',0,10,220,14,3
	.word	46847
	.byte	12,10,223,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39837
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPDIS',0,10,228,14,3
	.word	46912
	.byte	12,10,231,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39990
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPSET',0,10,236,14,3
	.word	46977
	.byte	12,10,239,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40143
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPSTAT',0,10,244,14,3
	.word	47042
	.byte	12,10,247,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40314
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTCPU_CON0',0,10,252,14,3
	.word	47108
	.byte	12,10,255,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40444
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTCPU_CON1',0,10,132,15,3
	.word	47177
	.byte	12,10,135,15,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40682
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTCPU_SR',0,10,140,15,3
	.word	47246
	.byte	12,10,143,15,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40905
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTS_CON0',0,10,148,15,3
	.word	47313
	.byte	12,10,151,15,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41031
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTS_CON1',0,10,156,15,3
	.word	47380
	.byte	12,10,159,15,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41283
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTS_SR',0,10,164,15,3
	.word	47447
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU',0,10,175,15,25,12,13
	.byte	'CON0',0
	.word	47108
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	47177
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	47246
	.byte	4,2,35,8,0,16
	.word	47512
	.byte	21
	.byte	'Ifx_SCU_WDTCPU',0,10,180,15,3
	.word	47575
	.byte	10
	.byte	'_Ifx_SCU_WDTS',0,10,183,15,25,12,13
	.byte	'CON0',0
	.word	47313
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	47380
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	47447
	.byte	4,2,35,8,0,16
	.word	47604
	.byte	21
	.byte	'Ifx_SCU_WDTS',0,10,188,15,3
	.word	47665
	.byte	17,3,83,9,1,18
	.byte	'IfxPort_InputMode_undefined',0,127,18
	.byte	'IfxPort_InputMode_noPullDevice',0,0,18
	.byte	'IfxPort_InputMode_pullDown',0,8,18
	.byte	'IfxPort_InputMode_pullUp',0,16,0,21
	.byte	'IfxPort_InputMode',0,3,89,3
	.word	47692
	.byte	17,3,120,9,1,18
	.byte	'IfxPort_OutputIdx_general',0,128,1,18
	.byte	'IfxPort_OutputIdx_alt1',0,136,1,18
	.byte	'IfxPort_OutputIdx_alt2',0,144,1,18
	.byte	'IfxPort_OutputIdx_alt3',0,152,1,18
	.byte	'IfxPort_OutputIdx_alt4',0,160,1,18
	.byte	'IfxPort_OutputIdx_alt5',0,168,1,18
	.byte	'IfxPort_OutputIdx_alt6',0,176,1,18
	.byte	'IfxPort_OutputIdx_alt7',0,184,1,0,21
	.byte	'IfxPort_OutputIdx',0,3,130,1,3
	.word	47843
	.byte	17,3,134,1,9,1,18
	.byte	'IfxPort_OutputMode_pushPull',0,128,1,18
	.byte	'IfxPort_OutputMode_openDrain',0,192,1,0,21
	.byte	'IfxPort_OutputMode',0,3,138,1,3
	.word	48087
	.byte	17,3,144,1,9,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed1',0,0,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed2',0,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed3',0,2,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed4',0,3,18
	.byte	'IfxPort_PadDriver_lvdsSpeed1',0,4,18
	.byte	'IfxPort_PadDriver_lvdsSpeed2',0,5,18
	.byte	'IfxPort_PadDriver_lvdsSpeed3',0,6,18
	.byte	'IfxPort_PadDriver_lvdsSpeed4',0,7,18
	.byte	'IfxPort_PadDriver_ttlSpeed1',0,8,18
	.byte	'IfxPort_PadDriver_ttlSpeed2',0,9,18
	.byte	'IfxPort_PadDriver_ttlSpeed3',0,10,18
	.byte	'IfxPort_PadDriver_ttlSpeed4',0,11,0,21
	.byte	'IfxPort_PadDriver',0,3,158,1,3
	.word	48185
	.byte	21
	.byte	'IfxPort_State',0,3,178,1,3
	.word	8290
	.byte	23,3,190,1,9,8,13
	.byte	'port',0
	.word	8285
	.byte	4,2,35,0,13
	.byte	'pinIndex',0
	.word	493
	.byte	1,2,35,4,0,21
	.byte	'IfxPort_Pin',0,3,194,1,3
	.word	48650
	.byte	16
	.word	20107
	.byte	3
	.word	48710
	.byte	23,11,59,15,16,13
	.byte	'module',0
	.word	48715
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	48650
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	21210
	.byte	1,2,35,12,0,24
	.word	48720
	.byte	21
	.byte	'IfxCcu6_Cc60in_In',0,11,64,3
	.word	48771
	.byte	23,11,67,15,16,13
	.byte	'module',0
	.word	48715
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	48650
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	21210
	.byte	1,2,35,12,0,24
	.word	48802
	.byte	21
	.byte	'IfxCcu6_Cc61in_In',0,11,72,3
	.word	48853
	.byte	23,11,75,15,16,13
	.byte	'module',0
	.word	48715
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	48650
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	21210
	.byte	1,2,35,12,0,24
	.word	48884
	.byte	21
	.byte	'IfxCcu6_Cc62in_In',0,11,80,3
	.word	48935
	.byte	23,11,83,15,16,13
	.byte	'module',0
	.word	48715
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	48650
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	21210
	.byte	1,2,35,12,0,24
	.word	48966
	.byte	21
	.byte	'IfxCcu6_Ccpos0_In',0,11,88,3
	.word	49017
	.byte	23,11,91,15,16,13
	.byte	'module',0
	.word	48715
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	48650
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	21210
	.byte	1,2,35,12,0,24
	.word	49048
	.byte	21
	.byte	'IfxCcu6_Ccpos1_In',0,11,96,3
	.word	49099
	.byte	23,11,99,15,16,13
	.byte	'module',0
	.word	48715
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	48650
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	21210
	.byte	1,2,35,12,0,24
	.word	49130
	.byte	21
	.byte	'IfxCcu6_Ccpos2_In',0,11,104,3
	.word	49181
	.byte	23,11,107,15,16,13
	.byte	'module',0
	.word	48715
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	48650
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	21210
	.byte	1,2,35,12,0,24
	.word	49212
	.byte	21
	.byte	'IfxCcu6_Ctrap_In',0,11,112,3
	.word	49263
	.byte	23,11,115,15,16,13
	.byte	'module',0
	.word	48715
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	48650
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	21210
	.byte	1,2,35,12,0,24
	.word	49293
	.byte	21
	.byte	'IfxCcu6_T12hr_In',0,11,120,3
	.word	49344
	.byte	23,11,123,15,16,13
	.byte	'module',0
	.word	48715
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	48650
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	21210
	.byte	1,2,35,12,0,24
	.word	49374
	.byte	21
	.byte	'IfxCcu6_T13hr_In',0,11,128,1,3
	.word	49425
	.byte	23,11,131,1,15,16,13
	.byte	'module',0
	.word	48715
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	48650
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	47843
	.byte	1,2,35,12,0,24
	.word	49456
	.byte	21
	.byte	'IfxCcu6_Cc60_Out',0,11,136,1,3
	.word	49508
	.byte	23,11,139,1,15,16,13
	.byte	'module',0
	.word	48715
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	48650
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	47843
	.byte	1,2,35,12,0,24
	.word	49539
	.byte	21
	.byte	'IfxCcu6_Cc61_Out',0,11,144,1,3
	.word	49591
	.byte	23,11,147,1,15,16,13
	.byte	'module',0
	.word	48715
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	48650
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	47843
	.byte	1,2,35,12,0,24
	.word	49622
	.byte	21
	.byte	'IfxCcu6_Cc62_Out',0,11,152,1,3
	.word	49674
	.byte	23,11,155,1,15,16,13
	.byte	'module',0
	.word	48715
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	48650
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	47843
	.byte	1,2,35,12,0,24
	.word	49705
	.byte	21
	.byte	'IfxCcu6_Cout60_Out',0,11,160,1,3
	.word	49757
	.byte	23,11,163,1,15,16,13
	.byte	'module',0
	.word	48715
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	48650
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	47843
	.byte	1,2,35,12,0,24
	.word	49790
	.byte	21
	.byte	'IfxCcu6_Cout61_Out',0,11,168,1,3
	.word	49842
	.byte	23,11,171,1,15,16,13
	.byte	'module',0
	.word	48715
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	48650
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	47843
	.byte	1,2,35,12,0,24
	.word	49875
	.byte	21
	.byte	'IfxCcu6_Cout62_Out',0,11,176,1,3
	.word	49927
	.byte	23,11,179,1,15,16,13
	.byte	'module',0
	.word	48715
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	48650
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	47843
	.byte	1,2,35,12,0,24
	.word	49960
	.byte	21
	.byte	'IfxCcu6_Cout63_Out',0,11,184,1,3
	.word	50012
.L238:
	.byte	24
	.word	49456
.L239:
	.byte	24
	.word	49456
.L240:
	.byte	24
	.word	49456
.L241:
	.byte	24
	.word	49456
.L242:
	.byte	24
	.word	49456
.L243:
	.byte	24
	.word	49456
.L244:
	.byte	24
	.word	49456
.L245:
	.byte	24
	.word	49456
.L246:
	.byte	24
	.word	48720
.L247:
	.byte	24
	.word	48720
.L248:
	.byte	24
	.word	48720
.L249:
	.byte	24
	.word	48720
.L250:
	.byte	24
	.word	48720
.L251:
	.byte	24
	.word	48720
.L252:
	.byte	24
	.word	49539
.L253:
	.byte	24
	.word	49539
.L254:
	.byte	24
	.word	49539
.L255:
	.byte	24
	.word	49539
.L256:
	.byte	24
	.word	49539
.L257:
	.byte	24
	.word	49539
.L258:
	.byte	24
	.word	49539
.L259:
	.byte	24
	.word	49539
.L260:
	.byte	24
	.word	48802
.L261:
	.byte	24
	.word	48802
.L262:
	.byte	24
	.word	48802
.L263:
	.byte	24
	.word	48802
.L264:
	.byte	24
	.word	48802
.L265:
	.byte	24
	.word	48802
.L266:
	.byte	24
	.word	49622
.L267:
	.byte	24
	.word	49622
.L268:
	.byte	24
	.word	49622
.L269:
	.byte	24
	.word	49622
.L270:
	.byte	24
	.word	49622
.L271:
	.byte	24
	.word	49622
.L272:
	.byte	24
	.word	49622
.L273:
	.byte	24
	.word	49622
.L274:
	.byte	24
	.word	48884
.L275:
	.byte	24
	.word	48884
.L276:
	.byte	24
	.word	48884
.L277:
	.byte	24
	.word	48884
.L278:
	.byte	24
	.word	48884
.L279:
	.byte	24
	.word	48884
.L280:
	.byte	24
	.word	48966
.L281:
	.byte	24
	.word	48966
.L282:
	.byte	24
	.word	48966
.L283:
	.byte	24
	.word	48966
.L284:
	.byte	24
	.word	48966
.L285:
	.byte	24
	.word	49048
.L286:
	.byte	24
	.word	49048
.L287:
	.byte	24
	.word	49048
.L288:
	.byte	24
	.word	49048
.L289:
	.byte	24
	.word	49048
.L290:
	.byte	24
	.word	49048
.L291:
	.byte	24
	.word	49048
.L292:
	.byte	24
	.word	49048
.L293:
	.byte	24
	.word	49130
.L294:
	.byte	24
	.word	49130
.L295:
	.byte	24
	.word	49130
.L296:
	.byte	24
	.word	49130
.L297:
	.byte	24
	.word	49130
.L298:
	.byte	24
	.word	49130
.L299:
	.byte	24
	.word	49130
.L300:
	.byte	24
	.word	49705
.L301:
	.byte	24
	.word	49705
.L302:
	.byte	24
	.word	49705
.L303:
	.byte	24
	.word	49705
.L304:
	.byte	24
	.word	49705
.L305:
	.byte	24
	.word	49705
.L306:
	.byte	24
	.word	49790
.L307:
	.byte	24
	.word	49790
.L308:
	.byte	24
	.word	49790
.L309:
	.byte	24
	.word	49790
.L310:
	.byte	24
	.word	49790
.L311:
	.byte	24
	.word	49790
.L312:
	.byte	24
	.word	49875
.L313:
	.byte	24
	.word	49875
.L314:
	.byte	24
	.word	49875
.L315:
	.byte	24
	.word	49875
.L316:
	.byte	24
	.word	49875
.L317:
	.byte	24
	.word	49875
.L318:
	.byte	24
	.word	49960
.L319:
	.byte	24
	.word	49960
.L320:
	.byte	24
	.word	49960
.L321:
	.byte	24
	.word	49960
.L322:
	.byte	24
	.word	49960
.L323:
	.byte	24
	.word	49960
.L324:
	.byte	24
	.word	49960
.L325:
	.byte	24
	.word	49212
.L326:
	.byte	24
	.word	49212
.L327:
	.byte	24
	.word	49212
.L328:
	.byte	24
	.word	49293
.L329:
	.byte	24
	.word	49293
.L330:
	.byte	24
	.word	49293
.L331:
	.byte	24
	.word	49293
.L332:
	.byte	24
	.word	49293
.L333:
	.byte	24
	.word	49293
.L334:
	.byte	24
	.word	49374
.L335:
	.byte	24
	.word	49374
.L336:
	.byte	24
	.word	49374
.L337:
	.byte	24
	.word	49374
	.byte	24
	.word	49456
	.byte	3
	.word	50545
	.byte	14,16
	.word	50550
	.byte	15,3,0
.L338:
	.byte	14,32
	.word	50555
	.byte	15,1,0,24
	.word	48720
	.byte	3
	.word	50573
	.byte	14,12
	.word	50578
	.byte	15,2,0
.L339:
	.byte	14,24
	.word	50583
	.byte	15,1,0,24
	.word	49539
	.byte	3
	.word	50601
	.byte	14,16
	.word	50606
	.byte	15,3,0
.L340:
	.byte	14,32
	.word	50611
	.byte	15,1,0,24
	.word	48802
	.byte	3
	.word	50629
	.byte	14,12
	.word	50634
	.byte	15,2,0
.L341:
	.byte	14,24
	.word	50639
	.byte	15,1,0,24
	.word	49622
	.byte	3
	.word	50657
	.byte	14,16
	.word	50662
	.byte	15,3,0
.L342:
	.byte	14,32
	.word	50667
	.byte	15,1,0,24
	.word	48884
	.byte	3
	.word	50685
	.byte	14,12
	.word	50690
	.byte	15,2,0
.L343:
	.byte	14,24
	.word	50695
	.byte	15,1,0,24
	.word	48966
	.byte	3
	.word	50713
	.byte	14,16
	.word	50718
	.byte	15,3,0
.L344:
	.byte	14,32
	.word	50723
	.byte	15,1,0,24
	.word	49048
	.byte	3
	.word	50741
	.byte	14,16
	.word	50746
	.byte	15,3,0
.L345:
	.byte	14,32
	.word	50751
	.byte	15,1,0,24
	.word	49130
	.byte	3
	.word	50769
	.byte	14,16
	.word	50774
	.byte	15,3,0
.L346:
	.byte	14,32
	.word	50779
	.byte	15,1,0,24
	.word	49705
	.byte	3
	.word	50797
	.byte	14,12
	.word	50802
	.byte	15,2,0
.L347:
	.byte	14,24
	.word	50807
	.byte	15,1,0,24
	.word	49790
	.byte	3
	.word	50825
	.byte	14,12
	.word	50830
	.byte	15,2,0
.L348:
	.byte	14,24
	.word	50835
	.byte	15,1,0,24
	.word	49875
	.byte	3
	.word	50853
	.byte	14,12
	.word	50858
	.byte	15,2,0
.L349:
	.byte	14,24
	.word	50863
	.byte	15,1,0,24
	.word	49960
	.byte	3
	.word	50881
	.byte	14,16
	.word	50886
	.byte	15,3,0
.L350:
	.byte	14,32
	.word	50891
	.byte	15,1,0,24
	.word	49212
	.byte	3
	.word	50909
	.byte	14,12
	.word	50914
	.byte	15,2,0
.L351:
	.byte	14,24
	.word	50919
	.byte	15,1,0,24
	.word	49293
	.byte	3
	.word	50937
	.byte	14,20
	.word	50942
	.byte	15,4,0
.L352:
	.byte	14,40
	.word	50947
	.byte	15,1,0,24
	.word	49374
	.byte	3
	.word	50965
	.byte	14,12
	.word	50970
	.byte	15,2,0
.L353:
	.byte	14,24
	.word	50975
	.byte	15,1,0,0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L4:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,21,0,54,15,39,12,0,0,3,15,0,73,19,0,0,4,46,1,3,8,32,13
	.byte	58,15,59,15,57,15,54,15,39,12,0,0,5,5,0,3,8,58,15,59,15,57,15,73,19,0,0,6,11,0,0,0,7,36,0,3,8,11,15,62
	.byte	15,0,0,8,46,1,3,8,32,13,58,15,59,15,57,15,73,19,54,15,39,12,0,0,9,59,0,3,8,0,0,10,19,1,3,8,58,15,59,15
	.byte	57,15,11,15,0,0,11,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,12,23,1,58,15,59,15,57,15,11,15,0,0,13,13
	.byte	0,3,8,73,19,11,15,56,9,0,0,14,1,1,11,15,73,19,0,0,15,33,0,47,15,0,0,16,53,0,73,19,0,0,17,4,1,58,15,59
	.byte	15,57,15,11,15,0,0,18,40,0,3,8,28,13,0,0,19,46,1,49,19,0,0,20,5,0,49,19,0,0,21,22,0,3,8,58,15,59,15,57
	.byte	15,73,19,0,0,22,21,0,54,15,0,0,23,19,1,58,15,59,15,57,15,11,15,0,0,24,38,0,73,19,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L5:
	.word	.L355-.L354
.L354:
	.half	3
	.word	.L357-.L356
.L356:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg',0
	.byte	0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_Intrinsics.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Port\\Std\\IfxPort.h',0
	.byte	0,0,0
	.byte	'IfxPort_regdef.h',0,1,0,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0,0,0,0
	.byte	'IfxCcu6_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Platform_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Ifx_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxScu_cfg.h',0
	.byte	0,0,0
	.byte	'IfxScu_regdef.h',0,1,0,0
	.byte	'..\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\_PinMap\\IfxCcu6_PinMap.h',0,0,0,0,0
.L357:
.L355:
	.sdecl	'.debug_info',debug,cluster('IfxCcu60_CC60_P02_0_OUT')
	.sect	'.debug_info'
.L6:
	.word	275
	.half	3
	.word	.L7
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu60_CC60_P02_0_OUT',0,5,48,18
	.word	.L238
	.byte	1,5,3
	.word	IfxCcu60_CC60_P02_0_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu60_CC60_P02_0_OUT')
	.sect	'.debug_abbrev'
.L7:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu60_CC60_P02_6_OUT')
	.sect	'.debug_info'
.L8:
	.word	275
	.half	3
	.word	.L9
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu60_CC60_P02_6_OUT',0,5,49,18
	.word	.L239
	.byte	1,5,3
	.word	IfxCcu60_CC60_P02_6_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu60_CC60_P02_6_OUT')
	.sect	'.debug_abbrev'
.L9:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu60_CC60_P11_12_OUT')
	.sect	'.debug_info'
.L10:
	.word	276
	.half	3
	.word	.L11
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu60_CC60_P11_12_OUT',0,5,50,18
	.word	.L240
	.byte	1,5,3
	.word	IfxCcu60_CC60_P11_12_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu60_CC60_P11_12_OUT')
	.sect	'.debug_abbrev'
.L11:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu60_CC60_P15_6_OUT')
	.sect	'.debug_info'
.L12:
	.word	275
	.half	3
	.word	.L13
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu60_CC60_P15_6_OUT',0,5,51,18
	.word	.L241
	.byte	1,5,3
	.word	IfxCcu60_CC60_P15_6_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu60_CC60_P15_6_OUT')
	.sect	'.debug_abbrev'
.L13:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu61_CC60_P00_1_OUT')
	.sect	'.debug_info'
.L14:
	.word	275
	.half	3
	.word	.L15
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu61_CC60_P00_1_OUT',0,5,52,18
	.word	.L242
	.byte	1,5,3
	.word	IfxCcu61_CC60_P00_1_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu61_CC60_P00_1_OUT')
	.sect	'.debug_abbrev'
.L15:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu61_CC60_P00_7_OUT')
	.sect	'.debug_info'
.L16:
	.word	275
	.half	3
	.word	.L17
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu61_CC60_P00_7_OUT',0,5,53,18
	.word	.L243
	.byte	1,5,3
	.word	IfxCcu61_CC60_P00_7_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu61_CC60_P00_7_OUT')
	.sect	'.debug_abbrev'
.L17:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu61_CC60_P20_8_OUT')
	.sect	'.debug_info'
.L18:
	.word	275
	.half	3
	.word	.L19
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu61_CC60_P20_8_OUT',0,5,54,18
	.word	.L244
	.byte	1,5,3
	.word	IfxCcu61_CC60_P20_8_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu61_CC60_P20_8_OUT')
	.sect	'.debug_abbrev'
.L19:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu61_CC60_P33_13_OUT')
	.sect	'.debug_info'
.L20:
	.word	276
	.half	3
	.word	.L21
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu61_CC60_P33_13_OUT',0,5,55,18
	.word	.L245
	.byte	1,5,3
	.word	IfxCcu61_CC60_P33_13_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu61_CC60_P33_13_OUT')
	.sect	'.debug_abbrev'
.L21:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu60_CC60INA_P02_0_IN')
	.sect	'.debug_info'
.L22:
	.word	277
	.half	3
	.word	.L23
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu60_CC60INA_P02_0_IN',0,5,56,19
	.word	.L246
	.byte	1,5,3
	.word	IfxCcu60_CC60INA_P02_0_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu60_CC60INA_P02_0_IN')
	.sect	'.debug_abbrev'
.L23:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu60_CC60INB_P00_1_IN')
	.sect	'.debug_info'
.L24:
	.word	277
	.half	3
	.word	.L25
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu60_CC60INB_P00_1_IN',0,5,57,19
	.word	.L247
	.byte	1,5,3
	.word	IfxCcu60_CC60INB_P00_1_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu60_CC60INB_P00_1_IN')
	.sect	'.debug_abbrev'
.L25:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu60_CC60INC_P02_6_IN')
	.sect	'.debug_info'
.L26:
	.word	277
	.half	3
	.word	.L27
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu60_CC60INC_P02_6_IN',0,5,58,19
	.word	.L248
	.byte	1,5,3
	.word	IfxCcu60_CC60INC_P02_6_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu60_CC60INC_P02_6_IN')
	.sect	'.debug_abbrev'
.L27:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu61_CC60INA_P00_1_IN')
	.sect	'.debug_info'
.L28:
	.word	277
	.half	3
	.word	.L29
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu61_CC60INA_P00_1_IN',0,5,59,19
	.word	.L249
	.byte	1,5,3
	.word	IfxCcu61_CC60INA_P00_1_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu61_CC60INA_P00_1_IN')
	.sect	'.debug_abbrev'
.L29:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu61_CC60INB_P02_0_IN')
	.sect	'.debug_info'
.L30:
	.word	277
	.half	3
	.word	.L31
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu61_CC60INB_P02_0_IN',0,5,60,19
	.word	.L250
	.byte	1,5,3
	.word	IfxCcu61_CC60INB_P02_0_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu61_CC60INB_P02_0_IN')
	.sect	'.debug_abbrev'
.L31:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu61_CC60INC_P00_7_IN')
	.sect	'.debug_info'
.L32:
	.word	277
	.half	3
	.word	.L33
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu61_CC60INC_P00_7_IN',0,5,61,19
	.word	.L251
	.byte	1,5,3
	.word	IfxCcu61_CC60INC_P00_7_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu61_CC60INC_P00_7_IN')
	.sect	'.debug_abbrev'
.L33:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu60_CC61_P02_2_OUT')
	.sect	'.debug_info'
.L34:
	.word	275
	.half	3
	.word	.L35
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu60_CC61_P02_2_OUT',0,5,62,18
	.word	.L252
	.byte	1,5,3
	.word	IfxCcu60_CC61_P02_2_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu60_CC61_P02_2_OUT')
	.sect	'.debug_abbrev'
.L35:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu60_CC61_P02_7_OUT')
	.sect	'.debug_info'
.L36:
	.word	275
	.half	3
	.word	.L37
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu60_CC61_P02_7_OUT',0,5,63,18
	.word	.L253
	.byte	1,5,3
	.word	IfxCcu60_CC61_P02_7_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu60_CC61_P02_7_OUT')
	.sect	'.debug_abbrev'
.L37:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu60_CC61_P11_11_OUT')
	.sect	'.debug_info'
.L38:
	.word	276
	.half	3
	.word	.L39
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu60_CC61_P11_11_OUT',0,5,64,18
	.word	.L254
	.byte	1,5,3
	.word	IfxCcu60_CC61_P11_11_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu60_CC61_P11_11_OUT')
	.sect	'.debug_abbrev'
.L39:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu60_CC61_P15_5_OUT')
	.sect	'.debug_info'
.L40:
	.word	275
	.half	3
	.word	.L41
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu60_CC61_P15_5_OUT',0,5,65,18
	.word	.L255
	.byte	1,5,3
	.word	IfxCcu60_CC61_P15_5_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu60_CC61_P15_5_OUT')
	.sect	'.debug_abbrev'
.L41:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu61_CC61_P00_3_OUT')
	.sect	'.debug_info'
.L42:
	.word	275
	.half	3
	.word	.L43
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu61_CC61_P00_3_OUT',0,5,66,18
	.word	.L256
	.byte	1,5,3
	.word	IfxCcu61_CC61_P00_3_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu61_CC61_P00_3_OUT')
	.sect	'.debug_abbrev'
.L43:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu61_CC61_P00_8_OUT')
	.sect	'.debug_info'
.L44:
	.word	275
	.half	3
	.word	.L45
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu61_CC61_P00_8_OUT',0,5,67,18
	.word	.L257
	.byte	1,5,3
	.word	IfxCcu61_CC61_P00_8_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu61_CC61_P00_8_OUT')
	.sect	'.debug_abbrev'
.L45:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu61_CC61_P20_9_OUT')
	.sect	'.debug_info'
.L46:
	.word	275
	.half	3
	.word	.L47
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu61_CC61_P20_9_OUT',0,5,68,18
	.word	.L258
	.byte	1,5,3
	.word	IfxCcu61_CC61_P20_9_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu61_CC61_P20_9_OUT')
	.sect	'.debug_abbrev'
.L47:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu61_CC61_P33_11_OUT')
	.sect	'.debug_info'
.L48:
	.word	276
	.half	3
	.word	.L49
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu61_CC61_P33_11_OUT',0,5,69,18
	.word	.L259
	.byte	1,5,3
	.word	IfxCcu61_CC61_P33_11_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu61_CC61_P33_11_OUT')
	.sect	'.debug_abbrev'
.L49:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu60_CC61INA_P02_2_IN')
	.sect	'.debug_info'
.L50:
	.word	277
	.half	3
	.word	.L51
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu60_CC61INA_P02_2_IN',0,5,70,19
	.word	.L260
	.byte	1,5,3
	.word	IfxCcu60_CC61INA_P02_2_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu60_CC61INA_P02_2_IN')
	.sect	'.debug_abbrev'
.L51:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu60_CC61INB_P00_3_IN')
	.sect	'.debug_info'
.L52:
	.word	277
	.half	3
	.word	.L53
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu60_CC61INB_P00_3_IN',0,5,71,19
	.word	.L261
	.byte	1,5,3
	.word	IfxCcu60_CC61INB_P00_3_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu60_CC61INB_P00_3_IN')
	.sect	'.debug_abbrev'
.L53:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu60_CC61INC_P02_7_IN')
	.sect	'.debug_info'
.L54:
	.word	277
	.half	3
	.word	.L55
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu60_CC61INC_P02_7_IN',0,5,72,19
	.word	.L262
	.byte	1,5,3
	.word	IfxCcu60_CC61INC_P02_7_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu60_CC61INC_P02_7_IN')
	.sect	'.debug_abbrev'
.L55:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu61_CC61INA_P00_3_IN')
	.sect	'.debug_info'
.L56:
	.word	277
	.half	3
	.word	.L57
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu61_CC61INA_P00_3_IN',0,5,73,19
	.word	.L263
	.byte	1,5,3
	.word	IfxCcu61_CC61INA_P00_3_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu61_CC61INA_P00_3_IN')
	.sect	'.debug_abbrev'
.L57:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu61_CC61INB_P02_2_IN')
	.sect	'.debug_info'
.L58:
	.word	277
	.half	3
	.word	.L59
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu61_CC61INB_P02_2_IN',0,5,74,19
	.word	.L264
	.byte	1,5,3
	.word	IfxCcu61_CC61INB_P02_2_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu61_CC61INB_P02_2_IN')
	.sect	'.debug_abbrev'
.L59:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu61_CC61INC_P00_8_IN')
	.sect	'.debug_info'
.L60:
	.word	277
	.half	3
	.word	.L61
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu61_CC61INC_P00_8_IN',0,5,75,19
	.word	.L265
	.byte	1,5,3
	.word	IfxCcu61_CC61INC_P00_8_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu61_CC61INC_P00_8_IN')
	.sect	'.debug_abbrev'
.L61:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu60_CC62_P02_4_OUT')
	.sect	'.debug_info'
.L62:
	.word	275
	.half	3
	.word	.L63
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu60_CC62_P02_4_OUT',0,5,76,18
	.word	.L266
	.byte	1,5,3
	.word	IfxCcu60_CC62_P02_4_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu60_CC62_P02_4_OUT')
	.sect	'.debug_abbrev'
.L63:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu60_CC62_P02_8_OUT')
	.sect	'.debug_info'
.L64:
	.word	275
	.half	3
	.word	.L65
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu60_CC62_P02_8_OUT',0,5,77,18
	.word	.L267
	.byte	1,5,3
	.word	IfxCcu60_CC62_P02_8_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu60_CC62_P02_8_OUT')
	.sect	'.debug_abbrev'
.L65:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu60_CC62_P11_10_OUT')
	.sect	'.debug_info'
.L66:
	.word	276
	.half	3
	.word	.L67
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu60_CC62_P11_10_OUT',0,5,78,18
	.word	.L268
	.byte	1,5,3
	.word	IfxCcu60_CC62_P11_10_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu60_CC62_P11_10_OUT')
	.sect	'.debug_abbrev'
.L67:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu60_CC62_P15_4_OUT')
	.sect	'.debug_info'
.L68:
	.word	275
	.half	3
	.word	.L69
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu60_CC62_P15_4_OUT',0,5,79,18
	.word	.L269
	.byte	1,5,3
	.word	IfxCcu60_CC62_P15_4_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu60_CC62_P15_4_OUT')
	.sect	'.debug_abbrev'
.L69:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu61_CC62_P00_5_OUT')
	.sect	'.debug_info'
.L70:
	.word	275
	.half	3
	.word	.L71
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu61_CC62_P00_5_OUT',0,5,80,18
	.word	.L270
	.byte	1,5,3
	.word	IfxCcu61_CC62_P00_5_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu61_CC62_P00_5_OUT')
	.sect	'.debug_abbrev'
.L71:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu61_CC62_P00_9_OUT')
	.sect	'.debug_info'
.L72:
	.word	275
	.half	3
	.word	.L73
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu61_CC62_P00_9_OUT',0,5,81,18
	.word	.L271
	.byte	1,5,3
	.word	IfxCcu61_CC62_P00_9_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu61_CC62_P00_9_OUT')
	.sect	'.debug_abbrev'
.L73:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu61_CC62_P20_10_OUT')
	.sect	'.debug_info'
.L74:
	.word	276
	.half	3
	.word	.L75
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu61_CC62_P20_10_OUT',0,5,82,18
	.word	.L272
	.byte	1,5,3
	.word	IfxCcu61_CC62_P20_10_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu61_CC62_P20_10_OUT')
	.sect	'.debug_abbrev'
.L75:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu61_CC62_P33_9_OUT')
	.sect	'.debug_info'
.L76:
	.word	275
	.half	3
	.word	.L77
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu61_CC62_P33_9_OUT',0,5,83,18
	.word	.L273
	.byte	1,5,3
	.word	IfxCcu61_CC62_P33_9_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu61_CC62_P33_9_OUT')
	.sect	'.debug_abbrev'
.L77:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu60_CC62INA_P02_4_IN')
	.sect	'.debug_info'
.L78:
	.word	277
	.half	3
	.word	.L79
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu60_CC62INA_P02_4_IN',0,5,84,19
	.word	.L274
	.byte	1,5,3
	.word	IfxCcu60_CC62INA_P02_4_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu60_CC62INA_P02_4_IN')
	.sect	'.debug_abbrev'
.L79:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu60_CC62INB_P00_5_IN')
	.sect	'.debug_info'
.L80:
	.word	277
	.half	3
	.word	.L81
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu60_CC62INB_P00_5_IN',0,5,85,19
	.word	.L275
	.byte	1,5,3
	.word	IfxCcu60_CC62INB_P00_5_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu60_CC62INB_P00_5_IN')
	.sect	'.debug_abbrev'
.L81:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu60_CC62INC_P02_8_IN')
	.sect	'.debug_info'
.L82:
	.word	277
	.half	3
	.word	.L83
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu60_CC62INC_P02_8_IN',0,5,86,19
	.word	.L276
	.byte	1,5,3
	.word	IfxCcu60_CC62INC_P02_8_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu60_CC62INC_P02_8_IN')
	.sect	'.debug_abbrev'
.L83:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu61_CC62INA_P00_5_IN')
	.sect	'.debug_info'
.L84:
	.word	277
	.half	3
	.word	.L85
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu61_CC62INA_P00_5_IN',0,5,87,19
	.word	.L277
	.byte	1,5,3
	.word	IfxCcu61_CC62INA_P00_5_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu61_CC62INA_P00_5_IN')
	.sect	'.debug_abbrev'
.L85:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu61_CC62INB_P02_4_IN')
	.sect	'.debug_info'
.L86:
	.word	277
	.half	3
	.word	.L87
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu61_CC62INB_P02_4_IN',0,5,88,19
	.word	.L278
	.byte	1,5,3
	.word	IfxCcu61_CC62INB_P02_4_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu61_CC62INB_P02_4_IN')
	.sect	'.debug_abbrev'
.L87:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu61_CC62INC_P00_9_IN')
	.sect	'.debug_info'
.L88:
	.word	277
	.half	3
	.word	.L89
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu61_CC62INC_P00_9_IN',0,5,89,19
	.word	.L279
	.byte	1,5,3
	.word	IfxCcu61_CC62INC_P00_9_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu61_CC62INC_P00_9_IN')
	.sect	'.debug_abbrev'
.L89:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu60_CCPOS0A_P02_6_IN')
	.sect	'.debug_info'
.L90:
	.word	277
	.half	3
	.word	.L91
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu60_CCPOS0A_P02_6_IN',0,5,90,19
	.word	.L280
	.byte	1,5,3
	.word	IfxCcu60_CCPOS0A_P02_6_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu60_CCPOS0A_P02_6_IN')
	.sect	'.debug_abbrev'
.L91:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu60_CCPOS0C_P10_4_IN')
	.sect	'.debug_info'
.L92:
	.word	277
	.half	3
	.word	.L93
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu60_CCPOS0C_P10_4_IN',0,5,91,19
	.word	.L281
	.byte	1,5,3
	.word	IfxCcu60_CCPOS0C_P10_4_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu60_CCPOS0C_P10_4_IN')
	.sect	'.debug_abbrev'
.L93:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu60_CCPOS0D_P40_0_IN')
	.sect	'.debug_info'
.L94:
	.word	277
	.half	3
	.word	.L95
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu60_CCPOS0D_P40_0_IN',0,5,92,19
	.word	.L282
	.byte	1,5,3
	.word	IfxCcu60_CCPOS0D_P40_0_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu60_CCPOS0D_P40_0_IN')
	.sect	'.debug_abbrev'
.L95:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu61_CCPOS0A_P00_7_IN')
	.sect	'.debug_info'
.L96:
	.word	277
	.half	3
	.word	.L97
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu61_CCPOS0A_P00_7_IN',0,5,93,19
	.word	.L283
	.byte	1,5,3
	.word	IfxCcu61_CCPOS0A_P00_7_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu61_CCPOS0A_P00_7_IN')
	.sect	'.debug_abbrev'
.L97:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu61_CCPOS0C_P33_7_IN')
	.sect	'.debug_info'
.L98:
	.word	277
	.half	3
	.word	.L99
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu61_CCPOS0C_P33_7_IN',0,5,94,19
	.word	.L284
	.byte	1,5,3
	.word	IfxCcu61_CCPOS0C_P33_7_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu61_CCPOS0C_P33_7_IN')
	.sect	'.debug_abbrev'
.L99:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu60_CCPOS1A_P02_7_IN')
	.sect	'.debug_info'
.L100:
	.word	277
	.half	3
	.word	.L101
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu60_CCPOS1A_P02_7_IN',0,5,95,19
	.word	.L285
	.byte	1,5,3
	.word	IfxCcu60_CCPOS1A_P02_7_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu60_CCPOS1A_P02_7_IN')
	.sect	'.debug_abbrev'
.L101:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu60_CCPOS1B_P40_1_IN')
	.sect	'.debug_info'
.L102:
	.word	277
	.half	3
	.word	.L103
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu60_CCPOS1B_P40_1_IN',0,5,96,19
	.word	.L286
	.byte	1,5,3
	.word	IfxCcu60_CCPOS1B_P40_1_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu60_CCPOS1B_P40_1_IN')
	.sect	'.debug_abbrev'
.L103:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu60_CCPOS1C_P10_7_IN')
	.sect	'.debug_info'
.L104:
	.word	277
	.half	3
	.word	.L105
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu60_CCPOS1C_P10_7_IN',0,5,97,19
	.word	.L287
	.byte	1,5,3
	.word	IfxCcu60_CCPOS1C_P10_7_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu60_CCPOS1C_P10_7_IN')
	.sect	'.debug_abbrev'
.L105:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu60_CCPOS1D_P40_2_IN')
	.sect	'.debug_info'
.L106:
	.word	277
	.half	3
	.word	.L107
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu60_CCPOS1D_P40_2_IN',0,5,98,19
	.word	.L288
	.byte	1,5,3
	.word	IfxCcu60_CCPOS1D_P40_2_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu60_CCPOS1D_P40_2_IN')
	.sect	'.debug_abbrev'
.L107:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu61_CCPOS1A_P00_8_IN')
	.sect	'.debug_info'
.L108:
	.word	277
	.half	3
	.word	.L109
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu61_CCPOS1A_P00_8_IN',0,5,99,19
	.word	.L289
	.byte	1,5,3
	.word	IfxCcu61_CCPOS1A_P00_8_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu61_CCPOS1A_P00_8_IN')
	.sect	'.debug_abbrev'
.L109:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu61_CCPOS1B_P40_6_IN')
	.sect	'.debug_info'
.L110:
	.word	277
	.half	3
	.word	.L111
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu61_CCPOS1B_P40_6_IN',0,5,100,19
	.word	.L290
	.byte	1,5,3
	.word	IfxCcu61_CCPOS1B_P40_6_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu61_CCPOS1B_P40_6_IN')
	.sect	'.debug_abbrev'
.L111:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu61_CCPOS1C_P33_6_IN')
	.sect	'.debug_info'
.L112:
	.word	277
	.half	3
	.word	.L113
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu61_CCPOS1C_P33_6_IN',0,5,101,19
	.word	.L291
	.byte	1,5,3
	.word	IfxCcu61_CCPOS1C_P33_6_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu61_CCPOS1C_P33_6_IN')
	.sect	'.debug_abbrev'
.L113:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu61_CCPOS1D_P40_7_IN')
	.sect	'.debug_info'
.L114:
	.word	277
	.half	3
	.word	.L115
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu61_CCPOS1D_P40_7_IN',0,5,102,19
	.word	.L292
	.byte	1,5,3
	.word	IfxCcu61_CCPOS1D_P40_7_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu61_CCPOS1D_P40_7_IN')
	.sect	'.debug_abbrev'
.L115:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu60_CCPOS2A_P02_8_IN')
	.sect	'.debug_info'
.L116:
	.word	277
	.half	3
	.word	.L117
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu60_CCPOS2A_P02_8_IN',0,5,103,19
	.word	.L293
	.byte	1,5,3
	.word	IfxCcu60_CCPOS2A_P02_8_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu60_CCPOS2A_P02_8_IN')
	.sect	'.debug_abbrev'
.L117:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu60_CCPOS2B_P40_3_IN')
	.sect	'.debug_info'
.L118:
	.word	277
	.half	3
	.word	.L119
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu60_CCPOS2B_P40_3_IN',0,5,104,19
	.word	.L294
	.byte	1,5,3
	.word	IfxCcu60_CCPOS2B_P40_3_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu60_CCPOS2B_P40_3_IN')
	.sect	'.debug_abbrev'
.L119:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu60_CCPOS2C_P10_8_IN')
	.sect	'.debug_info'
.L120:
	.word	277
	.half	3
	.word	.L121
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu60_CCPOS2C_P10_8_IN',0,5,105,19
	.word	.L295
	.byte	1,5,3
	.word	IfxCcu60_CCPOS2C_P10_8_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu60_CCPOS2C_P10_8_IN')
	.sect	'.debug_abbrev'
.L121:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu61_CCPOS2A_P00_9_IN')
	.sect	'.debug_info'
.L122:
	.word	277
	.half	3
	.word	.L123
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu61_CCPOS2A_P00_9_IN',0,5,106,19
	.word	.L296
	.byte	1,5,3
	.word	IfxCcu61_CCPOS2A_P00_9_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu61_CCPOS2A_P00_9_IN')
	.sect	'.debug_abbrev'
.L123:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu61_CCPOS2B_P40_8_IN')
	.sect	'.debug_info'
.L124:
	.word	277
	.half	3
	.word	.L125
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu61_CCPOS2B_P40_8_IN',0,5,107,19
	.word	.L297
	.byte	1,5,3
	.word	IfxCcu61_CCPOS2B_P40_8_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu61_CCPOS2B_P40_8_IN')
	.sect	'.debug_abbrev'
.L125:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu61_CCPOS2C_P33_5_IN')
	.sect	'.debug_info'
.L126:
	.word	277
	.half	3
	.word	.L127
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu61_CCPOS2C_P33_5_IN',0,5,108,19
	.word	.L298
	.byte	1,5,3
	.word	IfxCcu61_CCPOS2C_P33_5_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu61_CCPOS2C_P33_5_IN')
	.sect	'.debug_abbrev'
.L127:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu61_CCPOS2D_P40_9_IN')
	.sect	'.debug_info'
.L128:
	.word	277
	.half	3
	.word	.L129
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu61_CCPOS2D_P40_9_IN',0,5,109,19
	.word	.L299
	.byte	1,5,3
	.word	IfxCcu61_CCPOS2D_P40_9_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu61_CCPOS2D_P40_9_IN')
	.sect	'.debug_abbrev'
.L129:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu60_COUT60_P02_1_OUT')
	.sect	'.debug_info'
.L130:
	.word	277
	.half	3
	.word	.L131
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu60_COUT60_P02_1_OUT',0,5,110,20
	.word	.L300
	.byte	1,5,3
	.word	IfxCcu60_COUT60_P02_1_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu60_COUT60_P02_1_OUT')
	.sect	'.debug_abbrev'
.L131:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu60_COUT60_P11_9_OUT')
	.sect	'.debug_info'
.L132:
	.word	277
	.half	3
	.word	.L133
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu60_COUT60_P11_9_OUT',0,5,111,20
	.word	.L301
	.byte	1,5,3
	.word	IfxCcu60_COUT60_P11_9_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu60_COUT60_P11_9_OUT')
	.sect	'.debug_abbrev'
.L133:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu60_COUT60_P15_7_OUT')
	.sect	'.debug_info'
.L134:
	.word	277
	.half	3
	.word	.L135
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu60_COUT60_P15_7_OUT',0,5,112,20
	.word	.L302
	.byte	1,5,3
	.word	IfxCcu60_COUT60_P15_7_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu60_COUT60_P15_7_OUT')
	.sect	'.debug_abbrev'
.L135:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu61_COUT60_P00_2_OUT')
	.sect	'.debug_info'
.L136:
	.word	277
	.half	3
	.word	.L137
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu61_COUT60_P00_2_OUT',0,5,113,20
	.word	.L303
	.byte	1,5,3
	.word	IfxCcu61_COUT60_P00_2_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu61_COUT60_P00_2_OUT')
	.sect	'.debug_abbrev'
.L137:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu61_COUT60_P20_11_OUT')
	.sect	'.debug_info'
.L138:
	.word	278
	.half	3
	.word	.L139
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu61_COUT60_P20_11_OUT',0,5,114,20
	.word	.L304
	.byte	1,5,3
	.word	IfxCcu61_COUT60_P20_11_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu61_COUT60_P20_11_OUT')
	.sect	'.debug_abbrev'
.L139:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu61_COUT60_P33_12_OUT')
	.sect	'.debug_info'
.L140:
	.word	278
	.half	3
	.word	.L141
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu61_COUT60_P33_12_OUT',0,5,115,20
	.word	.L305
	.byte	1,5,3
	.word	IfxCcu61_COUT60_P33_12_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu61_COUT60_P33_12_OUT')
	.sect	'.debug_abbrev'
.L141:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu60_COUT61_P02_3_OUT')
	.sect	'.debug_info'
.L142:
	.word	277
	.half	3
	.word	.L143
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu60_COUT61_P02_3_OUT',0,5,116,20
	.word	.L306
	.byte	1,5,3
	.word	IfxCcu60_COUT61_P02_3_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu60_COUT61_P02_3_OUT')
	.sect	'.debug_abbrev'
.L143:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu60_COUT61_P11_6_OUT')
	.sect	'.debug_info'
.L144:
	.word	277
	.half	3
	.word	.L145
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu60_COUT61_P11_6_OUT',0,5,117,20
	.word	.L307
	.byte	1,5,3
	.word	IfxCcu60_COUT61_P11_6_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu60_COUT61_P11_6_OUT')
	.sect	'.debug_abbrev'
.L145:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu60_COUT61_P15_8_OUT')
	.sect	'.debug_info'
.L146:
	.word	277
	.half	3
	.word	.L147
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu60_COUT61_P15_8_OUT',0,5,118,20
	.word	.L308
	.byte	1,5,3
	.word	IfxCcu60_COUT61_P15_8_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu60_COUT61_P15_8_OUT')
	.sect	'.debug_abbrev'
.L147:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu61_COUT61_P00_4_OUT')
	.sect	'.debug_info'
.L148:
	.word	277
	.half	3
	.word	.L149
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu61_COUT61_P00_4_OUT',0,5,119,20
	.word	.L309
	.byte	1,5,3
	.word	IfxCcu61_COUT61_P00_4_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu61_COUT61_P00_4_OUT')
	.sect	'.debug_abbrev'
.L149:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu61_COUT61_P20_12_OUT')
	.sect	'.debug_info'
.L150:
	.word	278
	.half	3
	.word	.L151
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu61_COUT61_P20_12_OUT',0,5,120,20
	.word	.L310
	.byte	1,5,3
	.word	IfxCcu61_COUT61_P20_12_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu61_COUT61_P20_12_OUT')
	.sect	'.debug_abbrev'
.L151:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu61_COUT61_P33_10_OUT')
	.sect	'.debug_info'
.L152:
	.word	278
	.half	3
	.word	.L153
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu61_COUT61_P33_10_OUT',0,5,121,20
	.word	.L311
	.byte	1,5,3
	.word	IfxCcu61_COUT61_P33_10_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu61_COUT61_P33_10_OUT')
	.sect	'.debug_abbrev'
.L153:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu60_COUT62_P02_5_OUT')
	.sect	'.debug_info'
.L154:
	.word	277
	.half	3
	.word	.L155
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu60_COUT62_P02_5_OUT',0,5,122,20
	.word	.L312
	.byte	1,5,3
	.word	IfxCcu60_COUT62_P02_5_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu60_COUT62_P02_5_OUT')
	.sect	'.debug_abbrev'
.L155:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu60_COUT62_P11_3_OUT')
	.sect	'.debug_info'
.L156:
	.word	277
	.half	3
	.word	.L157
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu60_COUT62_P11_3_OUT',0,5,123,20
	.word	.L313
	.byte	1,5,3
	.word	IfxCcu60_COUT62_P11_3_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu60_COUT62_P11_3_OUT')
	.sect	'.debug_abbrev'
.L157:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu60_COUT62_P14_0_OUT')
	.sect	'.debug_info'
.L158:
	.word	277
	.half	3
	.word	.L159
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu60_COUT62_P14_0_OUT',0,5,124,20
	.word	.L314
	.byte	1,5,3
	.word	IfxCcu60_COUT62_P14_0_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu60_COUT62_P14_0_OUT')
	.sect	'.debug_abbrev'
.L159:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu61_COUT62_P00_6_OUT')
	.sect	'.debug_info'
.L160:
	.word	277
	.half	3
	.word	.L161
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu61_COUT62_P00_6_OUT',0,5,125,20
	.word	.L315
	.byte	1,5,3
	.word	IfxCcu61_COUT62_P00_6_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu61_COUT62_P00_6_OUT')
	.sect	'.debug_abbrev'
.L161:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu61_COUT62_P20_13_OUT')
	.sect	'.debug_info'
.L162:
	.word	278
	.half	3
	.word	.L163
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu61_COUT62_P20_13_OUT',0,5,126,20
	.word	.L316
	.byte	1,5,3
	.word	IfxCcu61_COUT62_P20_13_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu61_COUT62_P20_13_OUT')
	.sect	'.debug_abbrev'
.L163:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu61_COUT62_P33_8_OUT')
	.sect	'.debug_info'
.L164:
	.word	277
	.half	3
	.word	.L165
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu61_COUT62_P33_8_OUT',0,5,127,20
	.word	.L317
	.byte	1,5,3
	.word	IfxCcu61_COUT62_P33_8_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu61_COUT62_P33_8_OUT')
	.sect	'.debug_abbrev'
.L165:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu60_COUT63_P00_0_OUT')
	.sect	'.debug_info'
.L166:
	.word	278
	.half	3
	.word	.L167
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu60_COUT63_P00_0_OUT',0,5,128,1,20
	.word	.L318
	.byte	1,5,3
	.word	IfxCcu60_COUT63_P00_0_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu60_COUT63_P00_0_OUT')
	.sect	'.debug_abbrev'
.L167:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu60_COUT63_P11_2_OUT')
	.sect	'.debug_info'
.L168:
	.word	278
	.half	3
	.word	.L169
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu60_COUT63_P11_2_OUT',0,5,129,1,20
	.word	.L319
	.byte	1,5,3
	.word	IfxCcu60_COUT63_P11_2_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu60_COUT63_P11_2_OUT')
	.sect	'.debug_abbrev'
.L169:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu60_COUT63_P14_1_OUT')
	.sect	'.debug_info'
.L170:
	.word	278
	.half	3
	.word	.L171
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu60_COUT63_P14_1_OUT',0,5,130,1,20
	.word	.L320
	.byte	1,5,3
	.word	IfxCcu60_COUT63_P14_1_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu60_COUT63_P14_1_OUT')
	.sect	'.debug_abbrev'
.L171:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu60_COUT63_P32_4_OUT')
	.sect	'.debug_info'
.L172:
	.word	278
	.half	3
	.word	.L173
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu60_COUT63_P32_4_OUT',0,5,131,1,20
	.word	.L321
	.byte	1,5,3
	.word	IfxCcu60_COUT63_P32_4_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu60_COUT63_P32_4_OUT')
	.sect	'.debug_abbrev'
.L173:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu61_COUT63_P00_10_OUT')
	.sect	'.debug_info'
.L174:
	.word	279
	.half	3
	.word	.L175
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu61_COUT63_P00_10_OUT',0,5,132,1,20
	.word	.L322
	.byte	1,5,3
	.word	IfxCcu61_COUT63_P00_10_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu61_COUT63_P00_10_OUT')
	.sect	'.debug_abbrev'
.L175:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu61_COUT63_P00_12_OUT')
	.sect	'.debug_info'
.L176:
	.word	279
	.half	3
	.word	.L177
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu61_COUT63_P00_12_OUT',0,5,133,1,20
	.word	.L323
	.byte	1,5,3
	.word	IfxCcu61_COUT63_P00_12_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu61_COUT63_P00_12_OUT')
	.sect	'.debug_abbrev'
.L177:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu61_COUT63_P20_7_OUT')
	.sect	'.debug_info'
.L178:
	.word	278
	.half	3
	.word	.L179
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu61_COUT63_P20_7_OUT',0,5,134,1,20
	.word	.L324
	.byte	1,5,3
	.word	IfxCcu61_COUT63_P20_7_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu61_COUT63_P20_7_OUT')
	.sect	'.debug_abbrev'
.L179:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu60_CTRAPA_P00_11_IN')
	.sect	'.debug_info'
.L180:
	.word	278
	.half	3
	.word	.L181
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu60_CTRAPA_P00_11_IN',0,5,135,1,18
	.word	.L325
	.byte	1,5,3
	.word	IfxCcu60_CTRAPA_P00_11_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu60_CTRAPA_P00_11_IN')
	.sect	'.debug_abbrev'
.L181:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu61_CTRAPA_P00_0_IN')
	.sect	'.debug_info'
.L182:
	.word	277
	.half	3
	.word	.L183
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu61_CTRAPA_P00_0_IN',0,5,136,1,18
	.word	.L326
	.byte	1,5,3
	.word	IfxCcu61_CTRAPA_P00_0_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu61_CTRAPA_P00_0_IN')
	.sect	'.debug_abbrev'
.L183:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu61_CTRAPC_P33_4_IN')
	.sect	'.debug_info'
.L184:
	.word	277
	.half	3
	.word	.L185
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu61_CTRAPC_P33_4_IN',0,5,137,1,18
	.word	.L327
	.byte	1,5,3
	.word	IfxCcu61_CTRAPC_P33_4_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu61_CTRAPC_P33_4_IN')
	.sect	'.debug_abbrev'
.L185:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu60_T12HRB_P00_7_IN')
	.sect	'.debug_info'
.L186:
	.word	277
	.half	3
	.word	.L187
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu60_T12HRB_P00_7_IN',0,5,138,1,18
	.word	.L328
	.byte	1,5,3
	.word	IfxCcu60_T12HRB_P00_7_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu60_T12HRB_P00_7_IN')
	.sect	'.debug_abbrev'
.L187:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu60_T12HRC_P00_9_IN')
	.sect	'.debug_info'
.L188:
	.word	277
	.half	3
	.word	.L189
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu60_T12HRC_P00_9_IN',0,5,139,1,18
	.word	.L329
	.byte	1,5,3
	.word	IfxCcu60_T12HRC_P00_9_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu60_T12HRC_P00_9_IN')
	.sect	'.debug_abbrev'
.L189:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu60_T12HRE_P00_0_IN')
	.sect	'.debug_info'
.L190:
	.word	277
	.half	3
	.word	.L191
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu60_T12HRE_P00_0_IN',0,5,140,1,18
	.word	.L330
	.byte	1,5,3
	.word	IfxCcu60_T12HRE_P00_0_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu60_T12HRE_P00_0_IN')
	.sect	'.debug_abbrev'
.L191:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu61_T12HRB_P02_6_IN')
	.sect	'.debug_info'
.L192:
	.word	277
	.half	3
	.word	.L193
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu61_T12HRB_P02_6_IN',0,5,141,1,18
	.word	.L331
	.byte	1,5,3
	.word	IfxCcu61_T12HRB_P02_6_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu61_T12HRB_P02_6_IN')
	.sect	'.debug_abbrev'
.L193:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu61_T12HRC_P02_8_IN')
	.sect	'.debug_info'
.L194:
	.word	277
	.half	3
	.word	.L195
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu61_T12HRC_P02_8_IN',0,5,142,1,18
	.word	.L332
	.byte	1,5,3
	.word	IfxCcu61_T12HRC_P02_8_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu61_T12HRC_P02_8_IN')
	.sect	'.debug_abbrev'
.L195:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu61_T12HRE_P00_11_IN')
	.sect	'.debug_info'
.L196:
	.word	278
	.half	3
	.word	.L197
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu61_T12HRE_P00_11_IN',0,5,143,1,18
	.word	.L333
	.byte	1,5,3
	.word	IfxCcu61_T12HRE_P00_11_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu61_T12HRE_P00_11_IN')
	.sect	'.debug_abbrev'
.L197:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu60_T13HRB_P00_8_IN')
	.sect	'.debug_info'
.L198:
	.word	277
	.half	3
	.word	.L199
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu60_T13HRB_P00_8_IN',0,5,144,1,18
	.word	.L334
	.byte	1,5,3
	.word	IfxCcu60_T13HRB_P00_8_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu60_T13HRB_P00_8_IN')
	.sect	'.debug_abbrev'
.L199:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu60_T13HRC_P00_9_IN')
	.sect	'.debug_info'
.L200:
	.word	277
	.half	3
	.word	.L201
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu60_T13HRC_P00_9_IN',0,5,145,1,18
	.word	.L335
	.byte	1,5,3
	.word	IfxCcu60_T13HRC_P00_9_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu60_T13HRC_P00_9_IN')
	.sect	'.debug_abbrev'
.L201:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu61_T13HRB_P02_7_IN')
	.sect	'.debug_info'
.L202:
	.word	277
	.half	3
	.word	.L203
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu61_T13HRB_P02_7_IN',0,5,146,1,18
	.word	.L336
	.byte	1,5,3
	.word	IfxCcu61_T13HRB_P02_7_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu61_T13HRB_P02_7_IN')
	.sect	'.debug_abbrev'
.L203:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu61_T13HRC_P02_8_IN')
	.sect	'.debug_info'
.L204:
	.word	277
	.half	3
	.word	.L205
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu61_T13HRC_P02_8_IN',0,5,147,1,18
	.word	.L337
	.byte	1,5,3
	.word	IfxCcu61_T13HRC_P02_8_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu61_T13HRC_P02_8_IN')
	.sect	'.debug_abbrev'
.L205:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu6_Cc60_Out_pinTable')
	.sect	'.debug_info'
.L206:
	.word	278
	.half	3
	.word	.L207
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu6_Cc60_Out_pinTable',0,5,150,1,25
	.word	.L338
	.byte	1,5,3
	.word	IfxCcu6_Cc60_Out_pinTable
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu6_Cc60_Out_pinTable')
	.sect	'.debug_abbrev'
.L207:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu6_Cc60in_In_pinTable')
	.sect	'.debug_info'
.L208:
	.word	279
	.half	3
	.word	.L209
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu6_Cc60in_In_pinTable',0,5,165,1,26
	.word	.L339
	.byte	1,5,3
	.word	IfxCcu6_Cc60in_In_pinTable
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu6_Cc60in_In_pinTable')
	.sect	'.debug_abbrev'
.L209:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu6_Cc61_Out_pinTable')
	.sect	'.debug_info'
.L210:
	.word	278
	.half	3
	.word	.L211
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu6_Cc61_Out_pinTable',0,5,178,1,25
	.word	.L340
	.byte	1,5,3
	.word	IfxCcu6_Cc61_Out_pinTable
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu6_Cc61_Out_pinTable')
	.sect	'.debug_abbrev'
.L211:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu6_Cc61in_In_pinTable')
	.sect	'.debug_info'
.L212:
	.word	279
	.half	3
	.word	.L213
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu6_Cc61in_In_pinTable',0,5,193,1,26
	.word	.L341
	.byte	1,5,3
	.word	IfxCcu6_Cc61in_In_pinTable
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu6_Cc61in_In_pinTable')
	.sect	'.debug_abbrev'
.L213:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu6_Cc62_Out_pinTable')
	.sect	'.debug_info'
.L214:
	.word	278
	.half	3
	.word	.L215
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu6_Cc62_Out_pinTable',0,5,206,1,25
	.word	.L342
	.byte	1,5,3
	.word	IfxCcu6_Cc62_Out_pinTable
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu6_Cc62_Out_pinTable')
	.sect	'.debug_abbrev'
.L215:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu6_Cc62in_In_pinTable')
	.sect	'.debug_info'
.L216:
	.word	279
	.half	3
	.word	.L217
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu6_Cc62in_In_pinTable',0,5,221,1,26
	.word	.L343
	.byte	1,5,3
	.word	IfxCcu6_Cc62in_In_pinTable
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu6_Cc62in_In_pinTable')
	.sect	'.debug_abbrev'
.L217:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu6_Ccpos0_In_pinTable')
	.sect	'.debug_info'
.L218:
	.word	279
	.half	3
	.word	.L219
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu6_Ccpos0_In_pinTable',0,5,234,1,26
	.word	.L344
	.byte	1,5,3
	.word	IfxCcu6_Ccpos0_In_pinTable
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu6_Ccpos0_In_pinTable')
	.sect	'.debug_abbrev'
.L219:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu6_Ccpos1_In_pinTable')
	.sect	'.debug_info'
.L220:
	.word	279
	.half	3
	.word	.L221
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu6_Ccpos1_In_pinTable',0,5,249,1,26
	.word	.L345
	.byte	1,5,3
	.word	IfxCcu6_Ccpos1_In_pinTable
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu6_Ccpos1_In_pinTable')
	.sect	'.debug_abbrev'
.L221:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu6_Ccpos2_In_pinTable')
	.sect	'.debug_info'
.L222:
	.word	279
	.half	3
	.word	.L223
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu6_Ccpos2_In_pinTable',0,5,136,2,26
	.word	.L346
	.byte	1,5,3
	.word	IfxCcu6_Ccpos2_In_pinTable
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu6_Ccpos2_In_pinTable')
	.sect	'.debug_abbrev'
.L223:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu6_Cout60_Out_pinTable')
	.sect	'.debug_info'
.L224:
	.word	280
	.half	3
	.word	.L225
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu6_Cout60_Out_pinTable',0,5,151,2,27
	.word	.L347
	.byte	1,5,3
	.word	IfxCcu6_Cout60_Out_pinTable
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu6_Cout60_Out_pinTable')
	.sect	'.debug_abbrev'
.L225:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu6_Cout61_Out_pinTable')
	.sect	'.debug_info'
.L226:
	.word	280
	.half	3
	.word	.L227
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu6_Cout61_Out_pinTable',0,5,164,2,27
	.word	.L348
	.byte	1,5,3
	.word	IfxCcu6_Cout61_Out_pinTable
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu6_Cout61_Out_pinTable')
	.sect	'.debug_abbrev'
.L227:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu6_Cout62_Out_pinTable')
	.sect	'.debug_info'
.L228:
	.word	280
	.half	3
	.word	.L229
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu6_Cout62_Out_pinTable',0,5,177,2,27
	.word	.L349
	.byte	1,5,3
	.word	IfxCcu6_Cout62_Out_pinTable
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu6_Cout62_Out_pinTable')
	.sect	'.debug_abbrev'
.L229:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu6_Cout63_Out_pinTable')
	.sect	'.debug_info'
.L230:
	.word	280
	.half	3
	.word	.L231
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu6_Cout63_Out_pinTable',0,5,190,2,27
	.word	.L350
	.byte	1,5,3
	.word	IfxCcu6_Cout63_Out_pinTable
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu6_Cout63_Out_pinTable')
	.sect	'.debug_abbrev'
.L231:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu6_Ctrap_In_pinTable')
	.sect	'.debug_info'
.L232:
	.word	278
	.half	3
	.word	.L233
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu6_Ctrap_In_pinTable',0,5,205,2,25
	.word	.L351
	.byte	1,5,3
	.word	IfxCcu6_Ctrap_In_pinTable
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu6_Ctrap_In_pinTable')
	.sect	'.debug_abbrev'
.L233:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu6_T12hr_In_pinTable')
	.sect	'.debug_info'
.L234:
	.word	278
	.half	3
	.word	.L235
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu6_T12hr_In_pinTable',0,5,218,2,25
	.word	.L352
	.byte	1,5,3
	.word	IfxCcu6_T12hr_In_pinTable
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu6_T12hr_In_pinTable')
	.sect	'.debug_abbrev'
.L235:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu6_T13hr_In_pinTable')
	.sect	'.debug_info'
.L236:
	.word	278
	.half	3
	.word	.L237
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxCcu6_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu6_T13hr_In_pinTable',0,5,235,2,25
	.word	.L353
	.byte	1,5,3
	.word	IfxCcu6_T13hr_In_pinTable
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu6_T13hr_In_pinTable')
	.sect	'.debug_abbrev'
.L237:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	; Module end
