	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc35972a --dep-file=zf_driver_pit.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2\\\\xf_asr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/zf_driver/zf_driver_pit.src ../libraries/zf_driver/zf_driver_pit.c"
	.compiler_name		"ctc"
	;source	'../libraries/zf_driver/zf_driver_pit.c'

	
$TC16X
	
	.sdecl	'.text.zf_driver_pit.pit_close',code,cluster('pit_close')
	.sect	'.text.zf_driver_pit.pit_close'
	.align	2
	
	.global	pit_close
; Function pit_close
.L24:
pit_close:	.type	func
	sub.a	a10,#40
.L152:
	mov	d15,d4
.L154:
	mov	d0,#2
.L208:
	div	e4,d15,d0
.L153:
	call	IfxCcu6_getAddress
.L155:
	st.a	[a10]16,a2
.L209:
	mov	d0,#2
.L210:
	div	e0,d15,d0
.L211:
	st.b	[a10]20,d1
.L212:
	lea	a4,[a10]0
	call	IfxCcu6_Timer_stop
.L156:
	ret
.L69:
	
__pit_close_function_end:
	.size	pit_close,__pit_close_function_end-pit_close
.L43:
	; End of function
	
	.sdecl	'.text.zf_driver_pit.pit_start',code,cluster('pit_start')
	.sect	'.text.zf_driver_pit.pit_start'
	.align	2
	
	.global	pit_start
; Function pit_start
.L26:
pit_start:	.type	func
	sub.a	a10,#40
.L157:
	mov	d15,d4
.L159:
	mov	d0,#2
.L217:
	div	e4,d15,d0
.L158:
	call	IfxCcu6_getAddress
.L160:
	st.a	[a10]16,a2
.L218:
	mov	d0,#2
.L219:
	div	e0,d15,d0
.L220:
	st.b	[a10]20,d1
.L221:
	lea	a4,[a10]0
	call	IfxCcu6_Timer_start
.L161:
	ret
.L76:
	
__pit_start_function_end:
	.size	pit_start,__pit_start_function_end-pit_start
.L48:
	; End of function
	
	.sdecl	'.text.zf_driver_pit.pit_all_close',code,cluster('pit_all_close')
	.sect	'.text.zf_driver_pit.pit_all_close'
	.align	2
	
	.global	pit_all_close
; Function pit_all_close
.L28:
pit_all_close:	.type	func
	movh.a	a15,#@his(IfxCcu6_cfg_indexMap)
	lea	a15,[a15]@los(IfxCcu6_cfg_indexMap)
.L226:
	ld.a	a4,[a15]
.L227:
	call	IfxCcu6_disableModule
.L228:
	movh.a	a15,#@his(IfxCcu6_cfg_indexMap)
	lea	a15,[a15]@los(IfxCcu6_cfg_indexMap)
.L229:
	ld.a	a4,[a15]8
.L230:
	call	IfxCcu6_disableModule
.L231:
	ret
.L80:
	
__pit_all_close_function_end:
	.size	pit_all_close,__pit_all_close_function_end-pit_all_close
.L53:
	; End of function
	
	.sdecl	'.text.zf_driver_pit.pit_disable',code,cluster('pit_disable')
	.sect	'.text.zf_driver_pit.pit_disable'
	.align	2
	
	.global	pit_disable
; Function pit_disable
.L30:
pit_disable:	.type	func
	mov	d15,d4
.L163:
	mov	d0,#2
.L236:
	div	e4,d15,d0
.L162:
	call	IfxCcu6_getAddress
.L165:
	mov	d0,#2
.L237:
	div	e0,d15,d0
.L238:
	mul	d15,d1,#2
.L164:
	add	d15,#7
.L85:
	mov	d0,#1
.L239:
	sh	d0,d0,d15
.L166:
	ld.w	d15,[a2]176
.L240:
	mov	d1,#-1
	xor	d0,d1
.L167:
	and	d15,d0
.L241:
	st.w	[a2]176,d15
.L86:
	ret
.L81:
	
__pit_disable_function_end:
	.size	pit_disable,__pit_disable_function_end-pit_disable
.L58:
	; End of function
	
	.sdecl	'.text.zf_driver_pit.pit_enable',code,cluster('pit_enable')
	.sect	'.text.zf_driver_pit.pit_enable'
	.align	2
	
	.global	pit_enable
; Function pit_enable
.L32:
pit_enable:	.type	func
	mov	d15,d4
.L169:
	mov	d0,#2
.L246:
	div	e4,d15,d0
.L168:
	call	IfxCcu6_getAddress
.L171:
	mov	d0,#2
.L247:
	div	e0,d15,d0
.L248:
	mul	d15,d1,#2
.L170:
	add	d15,#7
.L98:
	mov	d0,#1
.L249:
	sh	d0,d0,d15
.L172:
	ld.w	d15,[a2]176
.L250:
	or	d15,d0
.L251:
	st.w	[a2]176,d15
.L99:
	ret
.L94:
	
__pit_enable_function_end:
	.size	pit_enable,__pit_enable_function_end-pit_enable
.L63:
	; End of function
	
	.sdecl	'.text.zf_driver_pit.pit_init',code,cluster('pit_init')
	.sect	'.text.zf_driver_pit.pit_init'
	.align	2
	
	.global	pit_init
; Function pit_init
.L34:
pit_init:	.type	func
	sub.a	a10,#136
.L173:
	mov	d9,d4
.L175:
	mov	d10,d5
.L120:
	mfcr	d15,#65068
.L176:
	extr.u	d15,d15,#15,#1
.L177:
	ne	d11,d15,#0
.L178:
	j	.L2
.L2:
	disable
.L256:
	nop
.L257:
	j	.L3
.L3:
	j	.L4
.L4:
	mov	d15,#2
.L258:
	div	e4,d9,d15
.L174:
	call	IfxCcu6_getAddress
.L179:
	mov.aa	a15,a2
.L181:
	lea	a4,[a10]36
.L259:
	mov.aa	a5,a15
.L180:
	call	IfxCcu6_Timer_initModuleConfig
.L182:
	call	IfxScuCcu_getSpbFrequency
	mov	d4,d2
	call	__f_ftoull
	mov	e12,d3,d2
.L260:
	mov	d8,#0
.L183:
	j	.L5
.L6:
	mov	d7,#0
	mov	d6,d10
.L184:
	mov	e4,d13,d12
.L186:
	call	__ll_mul64
.L185:
	mov	e4,d3,d2
.L261:
	mov.u	d6,#16960
	addih	d6,d6,#15
	mov	d7,#0
.L262:
	call	__ll_udiv64
.L263:
	mov.u	d15,#65535
.L187:
	jge.u	d2,d15,.L7
.L188:
	j	.L8
.L7:
	sh	d1,d13,#-1
.L189:
	dextr	d0,d13,d12,#31
.L190:
	mov	e12,d1,d0
.L264:
	add	d8,#1
.L5:
	mov	d15,#16
.L265:
	jlt.u	d8,d15,.L6
.L8:
	mov	d15,#16
.L266:
	jlt.u	d8,d15,.L9
.L9:
	mov	d15,#0
	jeq	d15,d9,.L10
.L267:
	mov	d15,#1
	jeq	d15,d9,.L11
.L268:
	mov	d15,#2
	jeq	d15,d9,.L12
.L269:
	mov	d15,#3
	jeq	d15,d9,.L13
	j	.L14
.L10:
	mov	d15,#0
.L270:
	st.b	[a10]100,d15
.L271:
	mov	d15,#30
.L272:
	st.h	[a10]98,d15
.L273:
	j	.L15
.L11:
	mov	d15,#0
.L274:
	st.b	[a10]106,d15
.L275:
	mov	d15,#31
.L276:
	st.h	[a10]104,d15
.L277:
	j	.L16
.L12:
	mov	d15,#0
.L278:
	st.b	[a10]100,d15
.L279:
	mov	d15,#32
.L280:
	st.h	[a10]98,d15
.L281:
	j	.L17
.L13:
	mov	d15,#0
.L282:
	st.b	[a10]106,d15
.L283:
	mov	d15,#33
.L284:
	st.h	[a10]104,d15
.L285:
	j	.L18
.L14:
.L18:
.L17:
.L16:
.L15:
	mov	d15,#2
.L286:
	div	e0,d9,d15
.L287:
	jne	d1,#0,.L19
.L288:
	mov	d15,#0
.L289:
	st.b	[a10]64,d15
.L290:
	mov	d15,#7
.L291:
	st.b	[a10]96,d15
.L292:
	mov	d15,#1
.L293:
	st.b	[a10]97,d15
.L191:
	st.w	[a10]40,d2
.L192:
	mov	e4,d13,d12
.L193:
	call	__f_ulltof
.L294:
	st.w	[a10]36,d2
.L295:
	mov	d15,#0
.L296:
	st.b	[a10]76,d15
.L297:
	j	.L20
.L19:
	mov	d15,#1
.L298:
	st.b	[a10]64,d15
.L299:
	mov	d15,#9
.L300:
	st.b	[a10]102,d15
.L301:
	mov	d15,#2
.L302:
	st.b	[a10]103,d15
.L194:
	st.w	[a10]48,d2
.L195:
	mov	e4,d13,d12
.L196:
	call	__f_ulltof
.L303:
	st.w	[a10]44,d2
.L304:
	mov	d15,#0
.L305:
	st.b	[a10]84,d15
.L20:
	mov	d15,#0
.L306:
	st.h	[a10]90,d15
.L307:
	mov	d15,#0
.L308:
	st.h	[a10]92,d15
.L309:
	mov	d15,#0
.L310:
	st.b	[a10]129,d15
.L311:
	lea	a4,[a10]0
.L312:
	lea	a5,[a10]36
	call	IfxCcu6_Timer_initModule
.L123:
	mov	d15,d11
.L124:
	jeq	d15,#0,.L21
.L313:
	enable
.L21:
	mfcr	d15,#64768
.L197:
	and	d15,#1
.L198:
	jne	d15,#1,.L22
.L314:
	mov	d15,#1
.L144:
	mov	d0,#1
.L199:
	insert	d0,d0,d0,#28,#1
.L315:
	insert	d0,d0,d15,#24,#4
.L316:
	st.w	[a15]232,d0
.L22:
	lea	a4,[a10]0
	call	IfxCcu6_Timer_start
.L317:
	ret
.L106:
	
__pit_init_function_end:
	.size	pit_init,__pit_init_function_end-pit_init
.L68:
	; End of function
	
	.calls	'pit_init','__f_ftoull'
	.calls	'pit_init','__ll_mul64'
	.calls	'pit_init','__ll_udiv64'
	.calls	'pit_init','__f_ulltof'
	.calls	'pit_close','IfxCcu6_getAddress'
	.calls	'pit_close','IfxCcu6_Timer_stop'
	.calls	'pit_start','IfxCcu6_getAddress'
	.calls	'pit_start','IfxCcu6_Timer_start'
	.calls	'pit_all_close','IfxCcu6_disableModule'
	.calls	'pit_disable','IfxCcu6_getAddress'
	.calls	'pit_enable','IfxCcu6_getAddress'
	.calls	'pit_init','IfxCcu6_getAddress'
	.calls	'pit_init','IfxCcu6_Timer_initModuleConfig'
	.calls	'pit_init','IfxScuCcu_getSpbFrequency'
	.calls	'pit_init','IfxCcu6_Timer_initModule'
	.calls	'pit_init','IfxCcu6_Timer_start'
	.calls	'pit_close','',40
	.calls	'pit_start','',40
	.calls	'pit_all_close','',0
	.calls	'pit_disable','',0
	.calls	'pit_enable','',0
	.extern	IfxCcu6_cfg_indexMap
	.extern	IfxScuCcu_getSpbFrequency
	.extern	IfxCcu6_getAddress
	.extern	IfxCcu6_disableModule
	.extern	IfxCcu6_Timer_initModule
	.extern	IfxCcu6_Timer_initModuleConfig
	.extern	IfxCcu6_Timer_start
	.extern	IfxCcu6_Timer_stop
	.extern	__f_ftoull
	.extern	__ll_mul64
	.extern	__ll_udiv64
	.extern	__f_ulltof
	.calls	'pit_init','',136
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L36:
	.word	103456
	.half	3
	.word	.L37
	.byte	4
.L35:
	.byte	1
	.byte	'../libraries/zf_driver/zf_driver_pit.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L38
	.byte	2,1,1,3
	.word	201
	.byte	4
	.byte	'Ifx__jump_and_link',0,3,1,61,17,1,1,5
	.byte	'fun',0,1,61,43
	.word	204
	.byte	6,0,7
	.byte	'__fract',0,4,128,1,7
	.byte	'float',0,4,4,8
	.byte	'Ifx__float_to_fract',0,3,1,152,2,18
	.word	249
	.byte	1,1,5
	.byte	'a',0,1,152,2,44
	.word	261
	.byte	6,0,4
	.byte	'Ifx__stopPerfCounters',0,3,1,172,2,17,1,1,6,0
.L112:
	.byte	7
	.byte	'unsigned long long int',0,8,7,9
	.byte	'void',0,3
	.word	373
	.byte	8
	.byte	'__ld64',0,3,2,135,1,19
	.word	347
	.byte	1,1,5
	.byte	'addr',0,2,135,1,32
	.word	379
	.byte	6,0,4
	.byte	'__st64',0,3,2,143,1,17,1,1,5
	.byte	'addr',0,2,143,1,30
	.word	379
	.byte	5
	.byte	'value',0,2,143,1,43
	.word	347
	.byte	6,0,7
	.byte	'unsigned int',0,4,7,7
	.byte	'int',0,4,5
.L109:
	.byte	7
	.byte	'unsigned char',0,1,8,10
	.byte	'_Ifx_SRC_SRCR_Bits',0,4,45,16,4,11
	.byte	'SRPN',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	488
	.byte	2,6,2,35,1,11
	.byte	'SRE',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'TOS',0,1
	.word	488
	.byte	2,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	488
	.byte	3,0,2,35,1,11
	.byte	'ECC',0,1
	.word	488
	.byte	6,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	488
	.byte	2,0,2,35,2,11
	.byte	'SRR',0,1
	.word	488
	.byte	1,7,2,35,3,11
	.byte	'CLRR',0,1
	.word	488
	.byte	1,6,2,35,3,11
	.byte	'SETR',0,1
	.word	488
	.byte	1,5,2,35,3,11
	.byte	'IOV',0,1
	.word	488
	.byte	1,4,2,35,3,11
	.byte	'IOVCLR',0,1
	.word	488
	.byte	1,3,2,35,3,11
	.byte	'SWS',0,1
	.word	488
	.byte	1,2,2,35,3,11
	.byte	'SWSCLR',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	488
	.byte	1,0,2,35,3,0,12,4,70,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	505
	.byte	4,2,35,0,0,14
	.word	795
	.byte	3
	.word	834
	.byte	4
	.byte	'IfxSrc_clearRequest',0,3,3,250,1,17,1,1,5
	.byte	'src',0,3,250,1,60
	.word	839
	.byte	6,0,7
	.byte	'unsigned int',0,4,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON0_Bits',0,6,241,8,16,4,11
	.byte	'ENDINIT',0,4
	.word	887
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	887
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	887
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	887
	.byte	16,0,2,35,0,0,12,6,247,14,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	903
	.byte	4,2,35,0,0,7
	.byte	'unsigned short int',0,2,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON1_Bits',0,6,250,8,16,4,11
	.byte	'reserved_0',0,1
	.word	488
	.byte	2,6,2,35,0,11
	.byte	'IR0',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	488
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,12,6,255,14,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1061
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_WDTCPU_SR_Bits',0,6,137,9,16,4,11
	.byte	'AE',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	488
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	1039
	.byte	16,0,2,35,2,0,12,6,135,15,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1305
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_WDTCPU',0,6,175,15,25,12,13
	.byte	'CON0',0
	.word	999
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	1265
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	1496
	.byte	4,2,35,8,0,14
	.word	1536
	.byte	3
	.word	1599
	.byte	4
	.byte	'IfxScuWdt_clearCpuEndinitInline',0,3,5,181,3,17,1,1,5
	.byte	'watchdog',0,5,181,3,65
	.word	1604
	.byte	5
	.byte	'password',0,5,181,3,82
	.word	1039
	.byte	6,0,4
	.byte	'IfxScuWdt_setCpuEndinitInline',0,3,5,140,4,17,1,1,5
	.byte	'watchdog',0,5,140,4,63
	.word	1604
	.byte	5
	.byte	'password',0,5,140,4,80
	.word	1039
	.byte	6,0,8
	.byte	'IfxScuWdt_getCpuWatchdogPasswordInline',0,3,5,227,3,19
	.word	1039
	.byte	1,1,5
	.byte	'watchdog',0,5,227,3,74
	.word	1604
	.byte	6,0,15,8,156,1,9,1,16
	.byte	'IfxCpu_ResourceCpu_0',0,0,16
	.byte	'IfxCpu_ResourceCpu_1',0,1,16
	.byte	'IfxCpu_ResourceCpu_none',0,2,0,8
	.byte	'IfxCpu_getCoreIndex',0,3,7,141,6,31
	.word	1834
	.byte	1,1,6,0
.L129:
	.byte	8
	.byte	'IfxCpu_areInterruptsEnabled',0,3,7,139,5,20
	.word	488
	.byte	1,1
.L130:
	.byte	6,0
.L125:
	.byte	8
	.byte	'IfxCpu_disableInterrupts',0,3,7,147,5,20
	.word	488
	.byte	1,1
.L126:
	.byte	17,6,0,0,4
	.byte	'IfxCpu_enableInterrupts',0,3,7,157,5,17,1,1,6,0,4
	.byte	'IfxCpu_forceDisableInterrupts',0,3,7,225,5,17,1,1,6,0
.L137:
	.byte	4
	.byte	'IfxCpu_restoreInterrupts',0,3,7,168,7,17,1,1
.L138:
	.byte	5
	.byte	'enabled',0,7,168,7,50
	.word	488
.L140:
	.byte	6,0
.L92:
	.byte	7
	.byte	'unsigned long int',0,4,7,8
	.byte	'IfxCpu_getPerformanceCounter',0,3,7,161,6,19
	.word	2156
	.byte	1,1,5
	.byte	'address',0,7,161,6,55
	.word	1039
	.byte	6,0,8
	.byte	'IfxCpu_getPerformanceCounterStickyOverflow',0,3,7,190,6,20
	.word	488
	.byte	1,1,5
	.byte	'address',0,7,190,6,70
	.word	1039
	.byte	6,0,4
	.byte	'IfxCpu_updatePerformanceCounter',0,3,7,172,8,17,1,1,5
	.byte	'address',0,7,172,8,56
	.word	2156
	.byte	5
	.byte	'count',0,7,172,8,72
	.word	2156
	.byte	17,6,0,0,10
	.byte	'_Ifx_P_OUT_Bits',0,10,143,3,16,4,11
	.byte	'P0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	488
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,12,10,181,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2387
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMR_Bits',0,10,169,2,16,4,11
	.byte	'PS0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	488
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'PCL0',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	488
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	488
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	488
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	488
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	488
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	488
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	488
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	488
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	488
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	488
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	488
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	488
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	488
	.byte	1,0,2,35,3,0,12,10,133,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2703
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ID_Bits',0,10,110,16,4,11
	.byte	'MODREV',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	488
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	1039
	.byte	16,0,2,35,2,0,12,10,148,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3274
	.byte	4,2,35,0,0,18,4
	.word	488
	.byte	19,3,0,10
	.byte	'_Ifx_P_IOCR0_Bits',0,10,140,1,16,4,11
	.byte	'reserved_0',0,1
	.word	488
	.byte	3,5,2,35,0,11
	.byte	'PC0',0,1
	.word	488
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	488
	.byte	3,5,2,35,1,11
	.byte	'PC1',0,1
	.word	488
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	488
	.byte	3,5,2,35,2,11
	.byte	'PC2',0,1
	.word	488
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	488
	.byte	3,5,2,35,3,11
	.byte	'PC3',0,1
	.word	488
	.byte	5,0,2,35,3,0,12,10,164,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3402
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR4_Bits',0,10,166,1,16,4,11
	.byte	'reserved_0',0,1
	.word	488
	.byte	3,5,2,35,0,11
	.byte	'PC4',0,1
	.word	488
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	488
	.byte	3,5,2,35,1,11
	.byte	'PC5',0,1
	.word	488
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	488
	.byte	3,5,2,35,2,11
	.byte	'PC6',0,1
	.word	488
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	488
	.byte	3,5,2,35,3,11
	.byte	'PC7',0,1
	.word	488
	.byte	5,0,2,35,3,0,12,10,180,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3617
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR8_Bits',0,10,179,1,16,4,11
	.byte	'reserved_0',0,1
	.word	488
	.byte	3,5,2,35,0,11
	.byte	'PC8',0,1
	.word	488
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	488
	.byte	3,5,2,35,1,11
	.byte	'PC9',0,1
	.word	488
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	488
	.byte	3,5,2,35,2,11
	.byte	'PC10',0,1
	.word	488
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	488
	.byte	3,5,2,35,3,11
	.byte	'PC11',0,1
	.word	488
	.byte	5,0,2,35,3,0,12,10,188,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3832
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR12_Bits',0,10,153,1,16,4,11
	.byte	'reserved_0',0,1
	.word	488
	.byte	3,5,2,35,0,11
	.byte	'PC12',0,1
	.word	488
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	488
	.byte	3,5,2,35,1,11
	.byte	'PC13',0,1
	.word	488
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	488
	.byte	3,5,2,35,2,11
	.byte	'PC14',0,1
	.word	488
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	488
	.byte	3,5,2,35,3,11
	.byte	'PC15',0,1
	.word	488
	.byte	5,0,2,35,3,0,12,10,172,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4049
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IN_Bits',0,10,118,16,4,11
	.byte	'P0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	488
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,12,10,156,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4269
	.byte	4,2,35,0,0,18,24
	.word	488
	.byte	19,23,0,10
	.byte	'_Ifx_P_PDR0_Bits',0,10,205,3,16,4,11
	.byte	'PD0',0,1
	.word	488
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	488
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'PD2',0,1
	.word	488
	.byte	3,5,2,35,1,11
	.byte	'PL2',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'PD3',0,1
	.word	488
	.byte	3,1,2,35,1,11
	.byte	'PL3',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'PD4',0,1
	.word	488
	.byte	3,5,2,35,2,11
	.byte	'PL4',0,1
	.word	488
	.byte	1,4,2,35,2,11
	.byte	'PD5',0,1
	.word	488
	.byte	3,1,2,35,2,11
	.byte	'PL5',0,1
	.word	488
	.byte	1,0,2,35,2,11
	.byte	'PD6',0,1
	.word	488
	.byte	3,5,2,35,3,11
	.byte	'PL6',0,1
	.word	488
	.byte	1,4,2,35,3,11
	.byte	'PD7',0,1
	.word	488
	.byte	3,1,2,35,3,11
	.byte	'PL7',0,1
	.word	488
	.byte	1,0,2,35,3,0,12,10,205,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4592
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PDR1_Bits',0,10,226,3,16,4,11
	.byte	'PD8',0,1
	.word	488
	.byte	3,5,2,35,0,11
	.byte	'PL8',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'PD9',0,1
	.word	488
	.byte	3,1,2,35,0,11
	.byte	'PL9',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'PD10',0,1
	.word	488
	.byte	3,5,2,35,1,11
	.byte	'PL10',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'PD11',0,1
	.word	488
	.byte	3,1,2,35,1,11
	.byte	'PL11',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'PD12',0,1
	.word	488
	.byte	3,5,2,35,2,11
	.byte	'PL12',0,1
	.word	488
	.byte	1,4,2,35,2,11
	.byte	'PD13',0,1
	.word	488
	.byte	3,1,2,35,2,11
	.byte	'PL13',0,1
	.word	488
	.byte	1,0,2,35,2,11
	.byte	'PD14',0,1
	.word	488
	.byte	3,5,2,35,3,11
	.byte	'PL14',0,1
	.word	488
	.byte	1,4,2,35,3,11
	.byte	'PD15',0,1
	.word	488
	.byte	3,1,2,35,3,11
	.byte	'PL15',0,1
	.word	488
	.byte	1,0,2,35,3,0,12,10,213,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4896
	.byte	4,2,35,0,0,18,8
	.word	488
	.byte	19,7,0,10
	.byte	'_Ifx_P_ESR_Bits',0,10,88,16,4,11
	.byte	'EN0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	488
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,12,10,140,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5221
	.byte	4,2,35,0,0,18,12
	.word	488
	.byte	19,11,0,10
	.byte	'_Ifx_P_PDISC_Bits',0,10,183,3,16,4,11
	.byte	'PDIS0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'PDIS2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'PDIS3',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'PDIS4',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'PDIS5',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'PDIS6',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'PDIS7',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'PDIS8',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'PDIS9',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'PDIS10',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'PDIS11',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'PDIS12',0,1
	.word	488
	.byte	1,3,2,35,1,11
	.byte	'PDIS13',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'PDIS14',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'PDIS15',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,12,10,197,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5561
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PCSR_Bits',0,10,165,3,16,4,11
	.byte	'SEL0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'SEL1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'SEL2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'SEL3',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'SEL4',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'SEL5',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'SEL6',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'SEL7',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	488
	.byte	2,6,2,35,1,11
	.byte	'SEL10',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'SEL11',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	465
	.byte	19,1,2,35,0,11
	.byte	'LCK',0,1
	.word	488
	.byte	1,0,2,35,3,0,12,10,189,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5927
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR0_Bits',0,10,206,2,16,4,11
	.byte	'PS0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	465
	.byte	28,0,2,35,0,0,12,10,149,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6213
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR4_Bits',0,10,227,2,16,4,11
	.byte	'reserved_0',0,1
	.word	488
	.byte	4,4,2,35,0,11
	.byte	'PS4',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	465
	.byte	24,0,2,35,0,0,12,10,165,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6360
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR8_Bits',0,10,238,2,16,4,11
	.byte	'reserved_0',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'PS8',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	465
	.byte	20,0,2,35,0,0,12,10,173,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6529
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR12_Bits',0,10,216,2,16,4,11
	.byte	'reserved_0',0,2
	.word	1039
	.byte	12,4,2,35,0,11
	.byte	'PS12',0,1
	.word	488
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,12,10,157,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6701
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR0_Bits',0,10,232,1,16,4,11
	.byte	'reserved_0',0,2
	.word	1039
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	488
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	488
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	1039
	.byte	12,0,2,35,2,0,12,10,229,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6876
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR4_Bits',0,10,253,1,16,4,11
	.byte	'reserved_0',0,4
	.word	465
	.byte	20,12,2,35,0,11
	.byte	'PCL4',0,1
	.word	488
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	488
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	488
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	488
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	488
	.byte	8,0,2,35,3,0,12,10,245,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7050
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR8_Bits',0,10,136,2,16,4,11
	.byte	'reserved_0',0,4
	.word	465
	.byte	24,8,2,35,0,11
	.byte	'PCL8',0,1
	.word	488
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	488
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	488
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	488
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	488
	.byte	4,0,2,35,3,0,12,10,253,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7224
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR12_Bits',0,10,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	465
	.byte	28,4,2,35,0,11
	.byte	'PCL12',0,1
	.word	488
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	488
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	488
	.byte	1,0,2,35,3,0,12,10,237,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7400
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR_Bits',0,10,249,2,16,4,11
	.byte	'PS0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	488
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,12,10,141,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7556
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR_Bits',0,10,147,2,16,4,11
	.byte	'reserved_0',0,2
	.word	1039
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	488
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	488
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	488
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	488
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	488
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	488
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	488
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	488
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	488
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	488
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	488
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	488
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	488
	.byte	1,0,2,35,3,0,12,10,221,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7889
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR0_Bits',0,10,192,1,16,4,11
	.byte	'reserved_0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	465
	.byte	30,0,2,35,0,0,12,10,196,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8237
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_Bits',0,10,200,1,16,4,11
	.byte	'reserved_0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	465
	.byte	30,0,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_P21_Bits',0,10,208,1,16,4,11
	.byte	'RDIS_CTRL',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'RX_DIS',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'TERM',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'LRXTERM',0,1
	.word	488
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	465
	.byte	24,0,2,35,0,0,12,10,204,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8361
	.byte	4,2,35,0,13
	.byte	'B_P21',0
	.word	8445
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR2_Bits',0,10,218,1,16,4,11
	.byte	'reserved_0',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'LVDSR',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'LVDSRL',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	488
	.byte	2,4,2,35,1,11
	.byte	'TDIS_CTRL',0,1
	.word	488
	.byte	1,3,2,35,1,11
	.byte	'TX_DIS',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'TX_PD',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'TX_PWDPD',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,12,10,213,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8625
	.byte	4,2,35,0,0,18,76
	.word	488
	.byte	19,75,0,10
	.byte	'_Ifx_P_ACCEN1_Bits',0,10,82,16,4,11
	.byte	'reserved_0',0,4
	.word	465
	.byte	32,0,2,35,0,0,12,10,132,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8878
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ACCEN0_Bits',0,10,45,16,4,11
	.byte	'EN0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	488
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	488
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	488
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	488
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	488
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	488
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	488
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	488
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	488
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	488
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	488
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	488
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	488
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	488
	.byte	1,0,2,35,3,0,12,10,252,3,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8965
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P',0,10,229,5,25,128,2,13
	.byte	'OUT',0
	.word	2663
	.byte	4,2,35,0,13
	.byte	'OMR',0
	.word	3234
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	3353
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	3393
	.byte	4,2,35,12,13
	.byte	'IOCR0',0
	.word	3577
	.byte	4,2,35,16,13
	.byte	'IOCR4',0
	.word	3792
	.byte	4,2,35,20,13
	.byte	'IOCR8',0
	.word	4009
	.byte	4,2,35,24,13
	.byte	'IOCR12',0
	.word	4229
	.byte	4,2,35,28,13
	.byte	'reserved_20',0
	.word	3393
	.byte	4,2,35,32,13
	.byte	'IN',0
	.word	4543
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	4583
	.byte	24,2,35,40,13
	.byte	'PDR0',0
	.word	4856
	.byte	4,2,35,64,13
	.byte	'PDR1',0
	.word	5172
	.byte	4,2,35,68,13
	.byte	'reserved_48',0
	.word	5212
	.byte	8,2,35,72,13
	.byte	'ESR',0
	.word	5512
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	5552
	.byte	12,2,35,84,13
	.byte	'PDISC',0
	.word	5887
	.byte	4,2,35,96,13
	.byte	'PCSR',0
	.word	6173
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	5212
	.byte	8,2,35,104,13
	.byte	'OMSR0',0
	.word	6320
	.byte	4,2,35,112,13
	.byte	'OMSR4',0
	.word	6489
	.byte	4,2,35,116,13
	.byte	'OMSR8',0
	.word	6661
	.byte	4,2,35,120,13
	.byte	'OMSR12',0
	.word	6836
	.byte	4,2,35,124,13
	.byte	'OMCR0',0
	.word	7010
	.byte	4,3,35,128,1,13
	.byte	'OMCR4',0
	.word	7184
	.byte	4,3,35,132,1,13
	.byte	'OMCR8',0
	.word	7360
	.byte	4,3,35,136,1,13
	.byte	'OMCR12',0
	.word	7516
	.byte	4,3,35,140,1,13
	.byte	'OMSR',0
	.word	7849
	.byte	4,3,35,144,1,13
	.byte	'OMCR',0
	.word	8197
	.byte	4,3,35,148,1,13
	.byte	'reserved_98',0
	.word	5212
	.byte	8,3,35,152,1,13
	.byte	'LPCR0',0
	.word	8321
	.byte	4,3,35,160,1,13
	.byte	'LPCR1',0
	.word	8570
	.byte	4,3,35,164,1,13
	.byte	'LPCR2',0
	.word	8829
	.byte	4,3,35,168,1,13
	.byte	'reserved_A4',0
	.word	8869
	.byte	76,3,35,172,1,13
	.byte	'ACCEN1',0
	.word	8925
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	9492
	.byte	4,3,35,252,1,0,14
	.word	9532
	.byte	3
	.word	10135
	.byte	15,9,83,9,1,16
	.byte	'IfxPort_InputMode_undefined',0,127,16
	.byte	'IfxPort_InputMode_noPullDevice',0,0,16
	.byte	'IfxPort_InputMode_pullDown',0,8,16
	.byte	'IfxPort_InputMode_pullUp',0,16,0,4
	.byte	'IfxPort_setPinModeInput',0,3,9,196,4,17,1,1,5
	.byte	'port',0,9,196,4,48
	.word	10140
	.byte	5
	.byte	'pinIndex',0,9,196,4,60
	.word	488
	.byte	5
	.byte	'mode',0,9,196,4,88
	.word	10145
	.byte	6,0,15,9,134,1,9,1,16
	.byte	'IfxPort_OutputMode_pushPull',0,128,1,16
	.byte	'IfxPort_OutputMode_openDrain',0,192,1,0,15,9,120,9,1,16
	.byte	'IfxPort_OutputIdx_general',0,128,1,16
	.byte	'IfxPort_OutputIdx_alt1',0,136,1,16
	.byte	'IfxPort_OutputIdx_alt2',0,144,1,16
	.byte	'IfxPort_OutputIdx_alt3',0,152,1,16
	.byte	'IfxPort_OutputIdx_alt4',0,160,1,16
	.byte	'IfxPort_OutputIdx_alt5',0,168,1,16
	.byte	'IfxPort_OutputIdx_alt6',0,176,1,16
	.byte	'IfxPort_OutputIdx_alt7',0,184,1,0,4
	.byte	'IfxPort_setPinModeOutput',0,3,9,202,4,17,1,1,5
	.byte	'port',0,9,202,4,49
	.word	10140
	.byte	5
	.byte	'pinIndex',0,9,202,4,61
	.word	488
	.byte	5
	.byte	'mode',0,9,202,4,90
	.word	10350
	.byte	5
	.byte	'index',0,9,202,4,114
	.word	10420
	.byte	6,0,15,9,172,1,9,4,16
	.byte	'IfxPort_State_notChanged',0,0,16
	.byte	'IfxPort_State_high',0,1,16
	.byte	'IfxPort_State_low',0,128,128,4,16
	.byte	'IfxPort_State_toggled',0,129,128,4,0,4
	.byte	'IfxPort_setPinState',0,3,9,208,4,17,1,1,5
	.byte	'port',0,9,208,4,44
	.word	10140
	.byte	5
	.byte	'pinIndex',0,9,208,4,56
	.word	488
	.byte	5
	.byte	'action',0,9,208,4,80
	.word	10733
	.byte	6,0,8
	.byte	'IfxScuCcu_getStmFrequency',0,3,11,226,8,20
	.word	261
	.byte	1,1,6,0,10
	.byte	'_Ifx_CCU6_CLC_Bits',0,13,144,1,16,4,11
	.byte	'DISR',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'DISS',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'EDIS',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	465
	.byte	28,0,2,35,0,0,12,13,172,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10954
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_MCFG_Bits',0,13,241,2,16,4,11
	.byte	'T12',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'T13',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'MCM',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	465
	.byte	29,0,2,35,0,0,12,13,164,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11112
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_ID_Bits',0,13,193,1,16,4,11
	.byte	'MODREV',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'MODNUMBER',0,1
	.word	488
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,12,13,196,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11246
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_MOSEL_Bits',0,13,171,3,16,4,11
	.byte	'TRIG0SEL',0,1
	.word	488
	.byte	3,5,2,35,0,11
	.byte	'TRIG1SEL',0,1
	.word	488
	.byte	3,2,2,35,0,11
	.byte	'TRIG2SEL',0,2
	.word	1039
	.byte	3,7,2,35,0,11
	.byte	'reserved_9',0,4
	.word	465
	.byte	23,0,2,35,0,0,12,13,204,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11373
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_PISEL0_Bits',0,13,193,3,16,4,11
	.byte	'ISCC60',0,1
	.word	488
	.byte	2,6,2,35,0,11
	.byte	'ISCC61',0,1
	.word	488
	.byte	2,4,2,35,0,11
	.byte	'ISCC62',0,1
	.word	488
	.byte	2,2,2,35,0,11
	.byte	'ISTRP',0,1
	.word	488
	.byte	2,0,2,35,0,11
	.byte	'ISPOS0',0,1
	.word	488
	.byte	2,6,2,35,1,11
	.byte	'ISPOS1',0,1
	.word	488
	.byte	2,4,2,35,1,11
	.byte	'ISPOS2',0,1
	.word	488
	.byte	2,2,2,35,1,11
	.byte	'IST12HR',0,1
	.word	488
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,12,13,220,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11523
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_PISEL2_Bits',0,13,207,3,16,4,11
	.byte	'IST13HR',0,1
	.word	488
	.byte	2,6,2,35,0,11
	.byte	'ISCNT12',0,1
	.word	488
	.byte	2,4,2,35,0,11
	.byte	'ISCNT13',0,1
	.word	488
	.byte	2,2,2,35,0,11
	.byte	'T12EXT',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'T13EXT',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	465
	.byte	24,0,2,35,0,0,12,13,228,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11759
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_KSCSR_Bits',0,13,212,2,16,4,11
	.byte	'SB0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'SB1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'SB2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'SB3',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	465
	.byte	28,0,2,35,0,0,12,13,148,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11943
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_T12_Bits',0,13,227,3,16,4,11
	.byte	'T12CV',0,2
	.word	1039
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,12,13,244,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12093
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_T12PR_Bits',0,13,131,4,16,4,11
	.byte	'T12PV',0,2
	.word	1039
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,12,13,140,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12199
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_T12DTC_Bits',0,13,234,3,16,4,11
	.byte	'DTM',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'DTE0',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'DTE1',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'DTE2',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'DTR0',0,1
	.word	488
	.byte	1,3,2,35,1,11
	.byte	'DTR1',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'DTR2',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	465
	.byte	17,0,2,35,0,0,12,13,252,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12307
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_CC60R_Bits',0,13,88,16,4,11
	.byte	'CCV',0,2
	.word	1039
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,12,13,236,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12533
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_CC61R_Bits',0,13,102,16,4,11
	.byte	'CCV',0,2
	.word	1039
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,12,13,252,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12638
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_CC62R_Bits',0,13,116,16,4,11
	.byte	'CCV',0,2
	.word	1039
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,12,13,140,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12743
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_CC60SR_Bits',0,13,95,16,4,11
	.byte	'CCS',0,2
	.word	1039
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,12,13,244,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12848
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_CC61SR_Bits',0,13,109,16,4,11
	.byte	'CCS',0,2
	.word	1039
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,12,13,132,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12954
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_CC62SR_Bits',0,13,123,16,4,11
	.byte	'CCS',0,2
	.word	1039
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,12,13,148,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13060
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_T13_Bits',0,13,138,4,16,4,11
	.byte	'T13CV',0,2
	.word	1039
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,12,13,148,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13166
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_T13PR_Bits',0,13,145,4,16,4,11
	.byte	'T13PV',0,2
	.word	1039
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,12,13,156,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13272
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_CC63R_Bits',0,13,130,1,16,4,11
	.byte	'CCV',0,2
	.word	1039
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,12,13,156,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13380
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_CC63SR_Bits',0,13,137,1,16,4,11
	.byte	'CCS',0,2
	.word	1039
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,12,13,164,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13486
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_CMPSTAT_Bits',0,13,171,1,16,4,11
	.byte	'CC60ST',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'CC61ST',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'CC62ST',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'CCPOS60',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'CCPOS61',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'CCPOS62',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'CC63ST',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'CC60PS',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'COUT60PS',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'CC61PS',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'COUT61PS',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'CC62PS',0,1
	.word	488
	.byte	1,3,2,35,1,11
	.byte	'COUT62PS',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'COUT63PS',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'T13IM',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,12,13,188,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13593
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_CMPMODIF_Bits',0,13,154,1,16,4,11
	.byte	'MCC60S',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'MCC61S',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'MCC62S',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	488
	.byte	3,2,2,35,0,11
	.byte	'MCC63S',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'MCC60R',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'MCC61R',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'MCC62R',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	488
	.byte	3,2,2,35,1,11
	.byte	'MCC63R',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	465
	.byte	17,0,2,35,0,0,12,13,180,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13988
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_T12MSEL_Bits',0,13,248,3,16,4,11
	.byte	'MSEL60',0,1
	.word	488
	.byte	4,4,2,35,0,11
	.byte	'MSEL61',0,1
	.word	488
	.byte	4,0,2,35,0,11
	.byte	'MSEL62',0,1
	.word	488
	.byte	4,4,2,35,1,11
	.byte	'HSYNC',0,1
	.word	488
	.byte	3,1,2,35,1,11
	.byte	'DBYP',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,12,13,132,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14293
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_TCTR0_Bits',0,13,152,4,16,4,11
	.byte	'T12CLK',0,1
	.word	488
	.byte	3,5,2,35,0,11
	.byte	'T12PRE',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'T12R',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'STE12',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'CDIR',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'CTM',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'T13CLK',0,1
	.word	488
	.byte	3,5,2,35,1,11
	.byte	'T13PRE',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'T13R',0,1
	.word	488
	.byte	1,3,2,35,1,11
	.byte	'STE13',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'reserved_14',0,4
	.word	465
	.byte	18,0,2,35,0,0,12,13,164,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14473
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_TCTR2_Bits',0,13,168,4,16,4,11
	.byte	'T12SSC',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'T13SSC',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'T13TEC',0,1
	.word	488
	.byte	3,3,2,35,0,11
	.byte	'T13TED',0,1
	.word	488
	.byte	2,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'T12RSEL',0,1
	.word	488
	.byte	2,6,2,35,1,11
	.byte	'T13RSEL',0,1
	.word	488
	.byte	2,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	465
	.byte	20,0,2,35,0,0,12,13,172,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14733
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_TCTR4_Bits',0,13,181,4,16,4,11
	.byte	'T12RR',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'T12RS',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'T12RES',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'DTRES',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'T12CNT',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'T12STR',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'T12STD',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'T13RR',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'T13RS',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'T13RES',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	488
	.byte	2,3,2,35,1,11
	.byte	'T13CNT',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'T13STR',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'T13STD',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,12,13,180,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14956
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_MODCTR_Bits',0,13,159,3,16,4,11
	.byte	'T12MODEN',0,1
	.word	488
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'MCMEN',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'T13MODEN',0,1
	.word	488
	.byte	6,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'ECT13O',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,12,13,196,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15321
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_TRPCTR_Bits',0,13,202,4,16,4,11
	.byte	'TRPM0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'TRPM1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'TRPM2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	488
	.byte	5,0,2,35,0,11
	.byte	'TRPEN',0,1
	.word	488
	.byte	6,2,2,35,1,11
	.byte	'TRPEN13',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'TRPPEN',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,12,13,188,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15533
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_PSLR_Bits',0,13,218,3,16,4,11
	.byte	'PSL',0,1
	.word	488
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'PSL63',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	465
	.byte	24,0,2,35,0,0,12,13,236,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15752
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_MCMOUTS_Bits',0,13,146,3,16,4,11
	.byte	'MCMPS',0,1
	.word	488
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'STRMCM',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'EXPHS',0,1
	.word	488
	.byte	3,5,2,35,1,11
	.byte	'CURHS',0,1
	.word	488
	.byte	3,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'STRHP',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,12,13,188,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15895
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_MCMOUT_Bits',0,13,135,3,16,4,11
	.byte	'MCMP',0,1
	.word	488
	.byte	6,2,2,35,0,11
	.byte	'R',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'EXPH',0,1
	.word	488
	.byte	3,5,2,35,1,11
	.byte	'CURH',0,1
	.word	488
	.byte	3,2,2,35,1,11
	.byte	'reserved_14',0,4
	.word	465
	.byte	18,0,2,35,0,0,12,13,180,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16119
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_MCMCTR_Bits',0,13,250,2,16,4,11
	.byte	'SWSEL',0,1
	.word	488
	.byte	3,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'SWSYN',0,1
	.word	488
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	488
	.byte	2,0,2,35,0,11
	.byte	'STE12U',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'STE12D',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'STE13U',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	465
	.byte	21,0,2,35,0,0,12,13,172,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16294
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_IMON_Bits',0,13,223,1,16,4,11
	.byte	'LBE',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'CCPOS0I',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'CCPOS1I',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'CCPOS2I',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'CC60INI',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'CC61INI',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'CC62INI',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'CTRAPI',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'T12HRI',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'T13HRI',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	465
	.byte	22,0,2,35,0,0,12,13,212,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16518
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_LI_Bits',0,13,222,2,16,4,11
	.byte	'reserved_0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'CCPOS0EN',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'CCPOS1EN',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'CCPOS2EN',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'CC60INEN',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'CC61INEN',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'CC62INEN',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'CTRAPEN',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'T12HREN',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'T13HREN',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	488
	.byte	3,3,2,35,1,11
	.byte	'LBEEN',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'INPLBE',0,1
	.word	488
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,12,13,156,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16791
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_IS_Bits',0,13,252,1,16,4,11
	.byte	'ICC60R',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'ICC60F',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'ICC61R',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'ICC61F',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'ICC62R',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'ICC62F',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'T12OM',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'T12PM',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'T13CM',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'T13PM',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'TRPF',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'TRPS',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'CHE',0,1
	.word	488
	.byte	1,3,2,35,1,11
	.byte	'WHE',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'IDLE',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'STR',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,12,13,228,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17136
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_ISS_Bits',0,13,168,2,16,4,11
	.byte	'SCC60R',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'SCC60F',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'SCC61R',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'SCC61F',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'SCC62R',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'SCC62F',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'ST12OM',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'ST12PM',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'ST13CM',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'ST13PM',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'STRPF',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'SWHC',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'SCHE',0,1
	.word	488
	.byte	1,3,2,35,1,11
	.byte	'SWHE',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'SIDLE',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'SSTR',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,12,13,244,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17493
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_ISR_Bits',0,13,146,2,16,4,11
	.byte	'RCC60R',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'RCC60F',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'RCC61R',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'RCC61F',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'RCC62R',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'RCC62F',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'RT12OM',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'RT12PM',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'RT13CM',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'RT13PM',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'RTRPF',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'RCHE',0,1
	.word	488
	.byte	1,3,2,35,1,11
	.byte	'RWHE',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'RIDLE',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'RSTR',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,12,13,236,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17860
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_INP_Bits',0,13,239,1,16,4,11
	.byte	'INPCC60',0,1
	.word	488
	.byte	2,6,2,35,0,11
	.byte	'INPCC61',0,1
	.word	488
	.byte	2,4,2,35,0,11
	.byte	'INPCC62',0,1
	.word	488
	.byte	2,2,2,35,0,11
	.byte	'INPCHE',0,1
	.word	488
	.byte	2,0,2,35,0,11
	.byte	'INPERR',0,1
	.word	488
	.byte	2,6,2,35,1,11
	.byte	'INPT12',0,1
	.word	488
	.byte	2,4,2,35,1,11
	.byte	'INPT13',0,1
	.word	488
	.byte	2,2,2,35,1,11
	.byte	'reserved_14',0,4
	.word	465
	.byte	18,0,2,35,0,0,12,13,220,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18234
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_IEN_Bits',0,13,201,1,16,4,11
	.byte	'ENCC60R',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'ENCC60F',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'ENCC61R',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'ENCC61F',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'ENCC62R',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'ENCC62F',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'ENT12OM',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'ENT12PM',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'ENT13CM',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'ENT13PM',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'ENTRPF',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'ENCHE',0,1
	.word	488
	.byte	1,3,2,35,1,11
	.byte	'ENWHE',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'ENIDLE',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'ENSTR',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,12,13,204,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18452
	.byte	4,2,35,0,0,18,52
	.word	488
	.byte	19,51,0,10
	.byte	'_Ifx_CCU6_OCS_Bits',0,13,180,3,16,4,11
	.byte	'TGS',0,1
	.word	488
	.byte	2,6,2,35,0,11
	.byte	'TGB',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'TG_P',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	465
	.byte	20,8,2,35,0,11
	.byte	'SUS',0,1
	.word	488
	.byte	4,4,2,35,3,11
	.byte	'SUS_P',0,1
	.word	488
	.byte	1,3,2,35,3,11
	.byte	'SUSSTA',0,1
	.word	488
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	488
	.byte	2,0,2,35,3,0
.L150:
	.byte	12,13,212,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18850
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_KRSTCLR_Bits',0,13,205,2,16,4,11
	.byte	'CLR',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	465
	.byte	31,0,2,35,0,0,12,13,140,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19057
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_KRST1_Bits',0,13,198,2,16,4,11
	.byte	'RST',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	465
	.byte	31,0,2,35,0,0,12,13,132,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19164
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_KRST0_Bits',0,13,190,2,16,4,11
	.byte	'RST',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'RSTSTAT',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	465
	.byte	30,0,2,35,0,0,12,13,252,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19269
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_ACCEN1_Bits',0,13,82,16,4,11
	.byte	'reserved_0',0,4
	.word	465
	.byte	32,0,2,35,0,0,12,13,228,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19393
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_ACCEN0_Bits',0,13,45,16,4,11
	.byte	'EN0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	488
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	488
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	488
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	488
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	488
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	488
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	488
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	488
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	488
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	488
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	488
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	488
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	488
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	488
	.byte	1,0,2,35,3,0,12,13,220,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19483
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6',0,13,204,7,25,128,2,13
	.byte	'CLC',0
	.word	11072
	.byte	4,2,35,0,13
	.byte	'MCFG',0
	.word	11206
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	11333
	.byte	4,2,35,8,13
	.byte	'MOSEL',0
	.word	11483
	.byte	4,2,35,12,13
	.byte	'PISEL0',0
	.word	11719
	.byte	4,2,35,16,13
	.byte	'PISEL2',0
	.word	11903
	.byte	4,2,35,20,13
	.byte	'reserved_18',0
	.word	3393
	.byte	4,2,35,24,13
	.byte	'KSCSR',0
	.word	12053
	.byte	4,2,35,28,13
	.byte	'T12',0
	.word	12159
	.byte	4,2,35,32,13
	.byte	'T12PR',0
	.word	12267
	.byte	4,2,35,36,13
	.byte	'T12DTC',0
	.word	12493
	.byte	4,2,35,40,13
	.byte	'reserved_2C',0
	.word	3393
	.byte	4,2,35,44,13
	.byte	'CC60R',0
	.word	12598
	.byte	4,2,35,48,13
	.byte	'CC61R',0
	.word	12703
	.byte	4,2,35,52,13
	.byte	'CC62R',0
	.word	12808
	.byte	4,2,35,56,13
	.byte	'reserved_3C',0
	.word	3393
	.byte	4,2,35,60,13
	.byte	'CC60SR',0
	.word	12914
	.byte	4,2,35,64,13
	.byte	'CC61SR',0
	.word	13020
	.byte	4,2,35,68,13
	.byte	'CC62SR',0
	.word	13126
	.byte	4,2,35,72,13
	.byte	'reserved_4C',0
	.word	3393
	.byte	4,2,35,76,13
	.byte	'T13',0
	.word	13232
	.byte	4,2,35,80,13
	.byte	'T13PR',0
	.word	13340
	.byte	4,2,35,84,13
	.byte	'CC63R',0
	.word	13446
	.byte	4,2,35,88,13
	.byte	'CC63SR',0
	.word	13553
	.byte	4,2,35,92,13
	.byte	'CMPSTAT',0
	.word	13948
	.byte	4,2,35,96,13
	.byte	'CMPMODIF',0
	.word	14253
	.byte	4,2,35,100,13
	.byte	'T12MSEL',0
	.word	14433
	.byte	4,2,35,104,13
	.byte	'reserved_6C',0
	.word	3393
	.byte	4,2,35,108,13
	.byte	'TCTR0',0
	.word	14693
	.byte	4,2,35,112,13
	.byte	'TCTR2',0
	.word	14916
	.byte	4,2,35,116,13
	.byte	'TCTR4',0
	.word	15281
	.byte	4,2,35,120,13
	.byte	'reserved_7C',0
	.word	3393
	.byte	4,2,35,124,13
	.byte	'MODCTR',0
	.word	15493
	.byte	4,3,35,128,1,13
	.byte	'TRPCTR',0
	.word	15712
	.byte	4,3,35,132,1,13
	.byte	'PSLR',0
	.word	15855
	.byte	4,3,35,136,1,13
	.byte	'MCMOUTS',0
	.word	16079
	.byte	4,3,35,140,1,13
	.byte	'MCMOUT',0
	.word	16254
	.byte	4,3,35,144,1,13
	.byte	'MCMCTR',0
	.word	16478
	.byte	4,3,35,148,1,13
	.byte	'IMON',0
	.word	16751
	.byte	4,3,35,152,1,13
	.byte	'LI',0
	.word	17096
	.byte	4,3,35,156,1,13
	.byte	'IS',0
	.word	17453
	.byte	4,3,35,160,1,13
	.byte	'ISS',0
	.word	17820
	.byte	4,3,35,164,1,13
	.byte	'ISR',0
	.word	18194
	.byte	4,3,35,168,1,13
	.byte	'INP',0
	.word	18412
	.byte	4,3,35,172,1,13
	.byte	'IEN',0
	.word	18801
	.byte	4,3,35,176,1,13
	.byte	'reserved_B4',0
	.word	18841
	.byte	52,3,35,180,1,13
	.byte	'OCS',0
	.word	19017
	.byte	4,3,35,232,1,13
	.byte	'KRSTCLR',0
	.word	19124
	.byte	4,3,35,236,1,13
	.byte	'KRST1',0
	.word	19229
	.byte	4,3,35,240,1,13
	.byte	'KRST0',0
	.word	19353
	.byte	4,3,35,244,1,13
	.byte	'ACCEN1',0
	.word	19443
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	20013
	.byte	4,3,35,252,1,0,14
	.word	20053
.L72:
	.byte	3
	.word	20909
	.byte	15,12,252,1,9,1,16
	.byte	'IfxCcu6_SuspendMode_none',0,0,16
	.byte	'IfxCcu6_SuspendMode_hard',0,1,16
	.byte	'IfxCcu6_SuspendMode_soft',0,2,0
.L143:
	.byte	4
	.byte	'IfxCcu6_setSuspendMode',0,3,12,143,16,17,1,1
.L145:
	.byte	5
	.byte	'ccu6',0,12,143,16,50
	.word	20914
.L147:
	.byte	5
	.byte	'mode',0,12,143,16,76
	.word	20919
.L149:
	.byte	6,0,15,12,182,1,9,1,16
	.byte	'IfxCcu6_InterruptSource_cc60RisingEdge',0,0,16
	.byte	'IfxCcu6_InterruptSource_cc60FallingEdge',0,1,16
	.byte	'IfxCcu6_InterruptSource_cc61RisingEdge',0,2,16
	.byte	'IfxCcu6_InterruptSource_cc61FallingEdge',0,3,16
	.byte	'IfxCcu6_InterruptSource_cc62RisingEdge',0,4,16
	.byte	'IfxCcu6_InterruptSource_cc62FallingEdge',0,5,16
	.byte	'IfxCcu6_InterruptSource_t12OneMatch',0,6,16
	.byte	'IfxCcu6_InterruptSource_t12PeriodMatch',0,7,16
	.byte	'IfxCcu6_InterruptSource_t13CompareMatch',0,8,16
	.byte	'IfxCcu6_InterruptSource_t13PeriodMatch',0,9,16
	.byte	'IfxCcu6_InterruptSource_trap',0,10,16
	.byte	'IfxCcu6_InterruptSource_correctHallEvent',0,12,16
	.byte	'IfxCcu6_InterruptSource_wrongHallEvent',0,13,0
.L84:
	.byte	4
	.byte	'IfxCcu6_disableInterrupt',0,3,12,188,11,17,1,1
.L87:
	.byte	5
	.byte	'ccu6',0,12,188,11,52
	.word	20914
.L89:
	.byte	5
	.byte	'source',0,12,188,11,82
	.word	21068
.L91:
	.byte	6,0
.L97:
	.byte	4
	.byte	'IfxCcu6_enableInterrupt',0,3,12,155,12,17,1,1
.L100:
	.byte	5
	.byte	'ccu6',0,12,155,12,51
	.word	20914
.L102:
	.byte	5
	.byte	'source',0,12,155,12,81
	.word	21068
.L104:
	.byte	6,0,15,12,81,9,1,16
	.byte	'IfxCcu6_CaptureCompareInput_cC60',0,0,16
	.byte	'IfxCcu6_CaptureCompareInput_cC61',0,2,16
	.byte	'IfxCcu6_CaptureCompareInput_cC62',0,4,16
	.byte	'IfxCcu6_CaptureCompareInput_cTRAP',0,6,16
	.byte	'IfxCcu6_CaptureCompareInput_cCPOS0',0,8,16
	.byte	'IfxCcu6_CaptureCompareInput_cCPOS1',0,10,16
	.byte	'IfxCcu6_CaptureCompareInput_cCPOS2',0,12,0,15,12,94,9,1,16
	.byte	'IfxCcu6_CaptureCompareInputSignal_a',0,0,16
	.byte	'IfxCcu6_CaptureCompareInputSignal_b',0,1,16
	.byte	'IfxCcu6_CaptureCompareInputSignal_c',0,2,16
	.byte	'IfxCcu6_CaptureCompareInputSignal_d',0,3,0,4
	.byte	'IfxCcu6_setCaptureCompareInputSignal',0,3,12,161,15,17,1,1,5
	.byte	'ccu6',0,12,161,15,64
	.word	20914
	.byte	5
	.byte	'input',0,12,161,15,98
	.word	21730
	.byte	5
	.byte	'signal',0,12,161,15,139,1
	.word	21988
	.byte	6,0,10
	.byte	'_Ifx_STM_CLC_Bits',0,15,100,16,4,11
	.byte	'DISR',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'DISS',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'EDIS',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	465
	.byte	28,0,2,35,0,0,12,15,149,2,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22239
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_ID_Bits',0,15,142,1,16,4,11
	.byte	'MODREV',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	488
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	1039
	.byte	16,0,2,35,2,0,12,15,181,2,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22395
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM0_Bits',0,15,192,1,16,4,11
	.byte	'STM31_0',0,4
	.word	465
	.byte	32,0,2,35,0,0,12,15,229,2,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22517
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM1_Bits',0,15,204,1,16,4,11
	.byte	'STM35_4',0,4
	.word	465
	.byte	32,0,2,35,0,0,12,15,245,2,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22602
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM2_Bits',0,15,210,1,16,4,11
	.byte	'STM39_8',0,4
	.word	465
	.byte	32,0,2,35,0,0,12,15,253,2,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22687
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM3_Bits',0,15,216,1,16,4,11
	.byte	'STM43_12',0,4
	.word	465
	.byte	32,0,2,35,0,0,12,15,133,3,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22772
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM4_Bits',0,15,222,1,16,4,11
	.byte	'STM47_16',0,4
	.word	465
	.byte	32,0,2,35,0,0,12,15,141,3,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22858
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM5_Bits',0,15,228,1,16,4,11
	.byte	'STM51_20',0,4
	.word	465
	.byte	32,0,2,35,0,0,12,15,149,3,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22944
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM6_Bits',0,15,234,1,16,4,11
	.byte	'STM63_32',0,4
	.word	465
	.byte	32,0,2,35,0,0,12,15,157,3,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23030
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_CAP_Bits',0,15,88,16,4,11
	.byte	'STMCAP63_32',0,4
	.word	465
	.byte	32,0,2,35,0,0,12,15,133,2,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23116
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_CMP_Bits',0,15,123,16,4,11
	.byte	'CMPVAL',0,4
	.word	465
	.byte	32,0,2,35,0,0,12,15,165,2,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23203
	.byte	4,2,35,0,0,18,8
	.word	23245
	.byte	19,1,0,10
	.byte	'_Ifx_STM_CMCON_Bits',0,15,110,16,4,11
	.byte	'MSIZE0',0,1
	.word	488
	.byte	5,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	488
	.byte	3,0,2,35,0,11
	.byte	'MSTART0',0,1
	.word	488
	.byte	5,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	488
	.byte	3,0,2,35,1,11
	.byte	'MSIZE1',0,1
	.word	488
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	488
	.byte	3,0,2,35,2,11
	.byte	'MSTART1',0,1
	.word	488
	.byte	5,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	488
	.byte	3,0,2,35,3,0,12,15,157,2,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23294
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_ICR_Bits',0,15,129,1,16,4,11
	.byte	'CMP0EN',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'CMP0IR',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'CMP0OS',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'CMP1EN',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'CMP1IR',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'CMP1OS',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,4
	.word	465
	.byte	25,0,2,35,0,0,12,15,173,2,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23525
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_ISCR_Bits',0,15,150,1,16,4,11
	.byte	'CMP0IRR',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'CMP0IRS',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'CMP1IRR',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'CMP1IRS',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	465
	.byte	28,0,2,35,0,0,12,15,189,2,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23742
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM0SV_Bits',0,15,198,1,16,4,11
	.byte	'STM31_0',0,4
	.word	465
	.byte	32,0,2,35,0,0,12,15,237,2,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23906
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_CAPSV_Bits',0,15,94,16,4,11
	.byte	'STMCAP63_32',0,4
	.word	465
	.byte	32,0,2,35,0,0,12,15,141,2,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23993
	.byte	4,2,35,0,0,18,144,1
	.word	488
	.byte	19,143,1,0,10
	.byte	'_Ifx_STM_OCS_Bits',0,15,182,1,16,4,11
	.byte	'reserved_0',0,4
	.word	465
	.byte	24,8,2,35,0,11
	.byte	'SUS',0,1
	.word	488
	.byte	4,4,2,35,3,11
	.byte	'SUS_P',0,1
	.word	488
	.byte	1,3,2,35,3,11
	.byte	'SUSSTA',0,1
	.word	488
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	488
	.byte	2,0,2,35,3,0,12,15,221,2,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24093
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_KRSTCLR_Bits',0,15,175,1,16,4,11
	.byte	'CLR',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	465
	.byte	31,0,2,35,0,0,12,15,213,2,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24253
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_KRST1_Bits',0,15,168,1,16,4,11
	.byte	'RST',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	465
	.byte	31,0,2,35,0,0,12,15,205,2,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24359
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_KRST0_Bits',0,15,160,1,16,4,11
	.byte	'RST',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'RSTSTAT',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	465
	.byte	30,0,2,35,0,0,12,15,197,2,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24463
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_ACCEN1_Bits',0,15,82,16,4,11
	.byte	'reserved_0',0,4
	.word	465
	.byte	32,0,2,35,0,0,12,15,253,1,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24586
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_ACCEN0_Bits',0,15,45,16,4,11
	.byte	'EN0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	488
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	488
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	488
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	488
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	488
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	488
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	488
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	488
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	488
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	488
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	488
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	488
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	488
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	488
	.byte	1,0,2,35,3,0,12,15,245,1,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24675
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM',0,15,173,3,25,128,2,13
	.byte	'CLC',0
	.word	22355
	.byte	4,2,35,0,13
	.byte	'reserved_4',0
	.word	3393
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	22477
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	3393
	.byte	4,2,35,12,13
	.byte	'TIM0',0
	.word	22562
	.byte	4,2,35,16,13
	.byte	'TIM1',0
	.word	22647
	.byte	4,2,35,20,13
	.byte	'TIM2',0
	.word	22732
	.byte	4,2,35,24,13
	.byte	'TIM3',0
	.word	22818
	.byte	4,2,35,28,13
	.byte	'TIM4',0
	.word	22904
	.byte	4,2,35,32,13
	.byte	'TIM5',0
	.word	22990
	.byte	4,2,35,36,13
	.byte	'TIM6',0
	.word	23076
	.byte	4,2,35,40,13
	.byte	'CAP',0
	.word	23163
	.byte	4,2,35,44,13
	.byte	'CMP',0
	.word	23285
	.byte	8,2,35,48,13
	.byte	'CMCON',0
	.word	23485
	.byte	4,2,35,56,13
	.byte	'ICR',0
	.word	23702
	.byte	4,2,35,60,13
	.byte	'ISCR',0
	.word	23866
	.byte	4,2,35,64,13
	.byte	'reserved_44',0
	.word	5552
	.byte	12,2,35,68,13
	.byte	'TIM0SV',0
	.word	23953
	.byte	4,2,35,80,13
	.byte	'CAPSV',0
	.word	24042
	.byte	4,2,35,84,13
	.byte	'reserved_58',0
	.word	24082
	.byte	144,1,2,35,88,13
	.byte	'OCS',0
	.word	24213
	.byte	4,3,35,232,1,13
	.byte	'KRSTCLR',0
	.word	24319
	.byte	4,3,35,236,1,13
	.byte	'KRST1',0
	.word	24423
	.byte	4,3,35,240,1,13
	.byte	'KRST0',0
	.word	24546
	.byte	4,3,35,244,1,13
	.byte	'ACCEN1',0
	.word	24635
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	25204
	.byte	4,3,35,252,1,0,14
	.word	25244
	.byte	3
	.word	25664
	.byte	8
	.byte	'IfxStm_get',0,3,14,162,4,19
	.word	347
	.byte	1,1,5
	.byte	'stm',0,14,162,4,39
	.word	25669
	.byte	6,0,8
	.byte	'IfxStm_getFrequency',0,3,14,179,4,20
	.word	261
	.byte	1,1,5
	.byte	'stm',0,14,179,4,49
	.word	25669
	.byte	17,6,6,0,0,8
	.byte	'IfxStm_getLower',0,3,14,190,4,19
	.word	2156
	.byte	1,1,5
	.byte	'stm',0,14,190,4,44
	.word	25669
	.byte	6,0
.L119:
	.byte	8
	.byte	'disableInterrupts',0,3,16,108,20
	.word	488
	.byte	1,1
.L121:
	.byte	17,6,0,0
.L133:
	.byte	4
	.byte	'restoreInterrupts',0,3,16,142,1,17,1,1
.L134:
	.byte	5
	.byte	'enabled',0,16,142,1,43
	.word	488
.L136:
	.byte	17,6,0,0,7
	.byte	'long long int',0,8,5,8
	.byte	'getDeadLine',0,3,16,164,2,25
	.word	25885
	.byte	1,1,5
	.byte	'timeout',0,16,164,2,50
	.word	25885
	.byte	17,6,0,0,8
	.byte	'isDeadLine',0,3,16,211,2,20
	.word	488
	.byte	1,1,5
	.byte	'deadLine',0,16,211,2,44
	.word	25885
	.byte	17,6,0,0,8
	.byte	'now',0,3,16,221,1,25
	.word	25885
	.byte	1,1,17,6,6,6,0,0,8
	.byte	'nowWithoutCriticalSection',0,3,16,240,1,25
	.word	25885
	.byte	1,1,17,6,0,0,14
	.word	481
	.byte	20
	.byte	'__mfcr',0
	.word	26056
	.byte	1,1,1,1,21
	.word	481
	.byte	0,22
	.byte	'__nop',0,1,1,1,1,22
	.byte	'__disable',0,1,1,1,1,22
	.byte	'__enable',0,1,1,1,1,23
	.word	209
	.byte	24
	.word	235
	.byte	6,0,23
	.word	270
	.byte	24
	.word	302
	.byte	6,0,23
	.word	315
	.byte	6,0,23
	.word	384
	.byte	24
	.word	403
	.byte	6,0,23
	.word	419
	.byte	24
	.word	434
	.byte	24
	.word	448
	.byte	6,0,23
	.word	844
	.byte	24
	.word	872
	.byte	6,0,23
	.word	1609
	.byte	24
	.word	1649
	.byte	24
	.word	1667
	.byte	6,0,23
	.word	1687
	.byte	24
	.word	1725
	.byte	24
	.word	1743
	.byte	6,0,23
	.word	1763
	.byte	24
	.word	1814
	.byte	6,0,23
	.word	1913
	.byte	6,0,23
	.word	1947
	.byte	6,0,23
	.word	1989
	.byte	17,25
	.word	1947
	.byte	26
	.word	1987
	.byte	0,6,0,0,23
	.word	2030
	.byte	6,0,23
	.word	2064
	.byte	6,0,23
	.word	2104
	.byte	24
	.word	2137
	.byte	6,0,23
	.word	2177
	.byte	24
	.word	2218
	.byte	6,0,23
	.word	2237
	.byte	24
	.word	2292
	.byte	6,0,23
	.word	2311
	.byte	24
	.word	2351
	.byte	24
	.word	2368
	.byte	17,6,0,0,23
	.word	10270
	.byte	24
	.word	10302
	.byte	24
	.word	10316
	.byte	24
	.word	10334
	.byte	6,0,23
	.word	10637
	.byte	24
	.word	10670
	.byte	24
	.word	10684
	.byte	24
	.word	10702
	.byte	24
	.word	10716
	.byte	6,0,23
	.word	10836
	.byte	24
	.word	10864
	.byte	24
	.word	10878
	.byte	24
	.word	10896
	.byte	6,0,23
	.word	10914
	.byte	6,0,27
	.byte	'IfxScuCcu_getSpbFrequency',0,11,179,7,20
	.word	261
	.byte	1,1,1,1,23
	.word	21007
	.byte	24
	.word	21038
	.byte	24
	.word	21052
	.byte	6,0,15,17,83,9,1,16
	.byte	'IfxCcu6_Index_none',0,127,16
	.byte	'IfxCcu6_Index_0',0,0,16
	.byte	'IfxCcu6_Index_1',0,1,0,28
	.byte	'IfxCcu6_getAddress',0,12,244,6,22
	.word	20914
	.byte	1,1,1,1,5
	.byte	'ccu6',0,12,244,6,55
	.word	26478
	.byte	0,23
	.word	21601
	.byte	24
	.word	21634
	.byte	24
	.word	21648
	.byte	6,0,23
	.word	21666
	.byte	24
	.word	21698
	.byte	24
	.word	21712
	.byte	6,0,23
	.word	22146
	.byte	24
	.word	22191
	.byte	24
	.word	22205
	.byte	24
	.word	22220
	.byte	6,0,29
	.byte	'IfxCcu6_disableModule',0,12,132,9,17,1,1,1,1,5
	.byte	'ccu6',0,12,132,9,49
	.word	20914
	.byte	0,10
	.byte	'Timer_s',0,19,72,8,16,13
	.byte	't12Frequency',0
	.word	261
	.byte	4,2,35,0,13
	.byte	't12Period',0
	.word	2156
	.byte	4,2,35,4,13
	.byte	't13Frequency',0
	.word	261
	.byte	4,2,35,8,13
	.byte	't13Period',0
	.word	2156
	.byte	4,2,35,12,0,15,12,209,2,9,1,16
	.byte	'IfxCcu6_TimerId_t12',0,0,16
	.byte	'IfxCcu6_TimerId_t13',0,1,0,30,9,190,1,9,8,13
	.byte	'port',0
	.word	10140
	.byte	4,2,35,0,13
	.byte	'pinIndex',0
	.word	488
	.byte	1,2,35,4,0,15,21,130,1,9,1,16
	.byte	'Ifx_RxSel_a',0,0,16
	.byte	'Ifx_RxSel_b',0,1,16
	.byte	'Ifx_RxSel_c',0,2,16
	.byte	'Ifx_RxSel_d',0,3,16
	.byte	'Ifx_RxSel_e',0,4,16
	.byte	'Ifx_RxSel_f',0,5,16
	.byte	'Ifx_RxSel_g',0,6,16
	.byte	'Ifx_RxSel_h',0,7,0,30,20,115,15,16,13
	.byte	'module',0
	.word	20914
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	26837
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	26876
	.byte	1,2,35,12,0,31
	.word	26995
	.byte	3
	.word	27046
	.byte	30,20,123,15,16,13
	.byte	'module',0
	.word	20914
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	26837
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	26876
	.byte	1,2,35,12,0,31
	.word	27056
	.byte	3
	.word	27107
	.byte	15,12,152,1,9,1,16
	.byte	'IfxCcu6_ExternalTriggerMode_disable',0,0,16
	.byte	'IfxCcu6_ExternalTriggerMode_risingEdge',0,1,16
	.byte	'IfxCcu6_ExternalTriggerMode_fallingEdge',0,2,16
	.byte	'IfxCcu6_ExternalTriggerMode_anyEdge',0,3,0,30,18,145,2,9,12,13
	.byte	't12ExtInputTrigger',0
	.word	27051
	.byte	4,2,35,0,13
	.byte	't13ExtInputTrigger',0
	.word	27112
	.byte	4,2,35,4,13
	.byte	'extInputTriggerMode',0
	.word	27117
	.byte	1,2,35,8,13
	.byte	't13InSyncWithT12',0
	.word	488
	.byte	1,2,35,9,0
.L74:
	.byte	30,18,168,2,9,36,13
	.byte	'base',0
	.word	26690
	.byte	16,2,35,0,13
	.byte	'ccu6',0
	.word	20914
	.byte	4,2,35,16,13
	.byte	'timer',0
	.word	26786
	.byte	1,2,35,20,13
	.byte	'trigger',0
	.word	27283
	.byte	12,2,35,24,0,3
	.word	27401
	.byte	30,19,84,9,24,13
	.byte	't12Frequency',0
	.word	261
	.byte	4,2,35,0,13
	.byte	't12Period',0
	.word	2156
	.byte	4,2,35,4,13
	.byte	't13Frequency',0
	.word	261
	.byte	4,2,35,8,13
	.byte	't13Period',0
	.word	2156
	.byte	4,2,35,12,13
	.byte	'waitingTime',0
	.word	2156
	.byte	4,2,35,16,13
	.byte	'activeCount',0
	.word	2156
	.byte	4,2,35,20,0,15,12,127,9,1,16
	.byte	'IfxCcu6_CountingInputMode_internal',0,0,16
	.byte	'IfxCcu6_CountingInputMode_manual',0,1,16
	.byte	'IfxCcu6_CountingInputMode_externalRising',0,2,16
	.byte	'IfxCcu6_CountingInputMode_externalFalling',0,3,0,30,18,234,1,9,20,13
	.byte	't12ExtClockEnabled',0
	.word	488
	.byte	1,2,35,0,13
	.byte	't12ExtClockInput',0
	.word	27051
	.byte	4,2,35,4,13
	.byte	't12countingInputMode',0
	.word	27603
	.byte	1,2,35,8,13
	.byte	't13ExtClockEnabled',0
	.word	488
	.byte	1,2,35,9,13
	.byte	't13ExtClockInput',0
	.word	27112
	.byte	4,2,35,12,13
	.byte	't13countingInputMode',0
	.word	27603
	.byte	1,2,35,16,0,15,12,172,2,9,1,16
	.byte	'IfxCcu6_T12CountMode_edgeAligned',0,0,16
	.byte	'IfxCcu6_T12CountMode_centerAligned',0,1,0,30,18,128,2,9,4,13
	.byte	'countMode',0
	.word	27943
	.byte	1,2,35,0,13
	.byte	'counterValue',0
	.word	1039
	.byte	2,2,35,2,0,15,12,194,2,9,1,16
	.byte	'IfxCcu6_T13TriggerEvent_noAction',0,0,16
	.byte	'IfxCcu6_T13TriggerEvent_onCC60RCompare',0,1,16
	.byte	'IfxCcu6_T13TriggerEvent_onCC61RCompare',0,2,16
	.byte	'IfxCcu6_T13TriggerEvent_onCC62RCompare',0,3,16
	.byte	'IfxCcu6_T13TriggerEvent_onAnyT12Compare',0,4,16
	.byte	'IfxCcu6_T13TriggerEvent_onT12Period',0,5,16
	.byte	'IfxCcu6_T13TriggerEvent_onT12Zero',0,6,16
	.byte	'IfxCcu6_T13TriggerEvent_onCCPOSxEdge',0,7,0,15,12,183,2,9,1,16
	.byte	'IfxCcu6_T13TriggerDirection_noAction',0,0,16
	.byte	'IfxCcu6_T13TriggerDirection_onT12CountingUp',0,1,16
	.byte	'IfxCcu6_T13TriggerDirection_onT12CountingDown',0,2,16
	.byte	'IfxCcu6_T13TriggerDirection_anyT12',0,3,0,30,18,136,2,9,4,13
	.byte	'counterValue',0
	.word	1039
	.byte	2,2,35,0,13
	.byte	't12SyncEvent',0
	.word	28070
	.byte	1,2,35,2,13
	.byte	't12SyncDirection',0
	.word	28390
	.byte	1,2,35,3,0,15,12,233,1,9,1,16
	.byte	'IfxCcu6_ServiceRequest_0',0,0,16
	.byte	'IfxCcu6_ServiceRequest_1',0,1,16
	.byte	'IfxCcu6_ServiceRequest_2',0,2,16
	.byte	'IfxCcu6_ServiceRequest_3',0,3,0,15,22,69,9,1,16
	.byte	'IfxSrc_Tos_cpu0',0,0,16
	.byte	'IfxSrc_Tos_cpu1',0,1,16
	.byte	'IfxSrc_Tos_dma',0,3,0,30,18,246,1,9,6,13
	.byte	'source',0
	.word	21068
	.byte	1,2,35,0,13
	.byte	'serviceRequest',0
	.word	28644
	.byte	1,2,35,1,13
	.byte	'priority',0
	.word	1039
	.byte	2,2,35,2,13
	.byte	'typeOfService',0
	.word	28759
	.byte	1,2,35,4,0,30,18,157,2,9,12,13
	.byte	't12hr',0
	.word	27051
	.byte	4,2,35,0,13
	.byte	't13hr',0
	.word	27112
	.byte	4,2,35,4,13
	.byte	't1xhrInputMode',0
	.word	10145
	.byte	1,2,35,8,0,3
	.word	28906
.L115:
	.byte	30,18,178,2,9,100,13
	.byte	'base',0
	.word	27473
	.byte	24,2,35,0,13
	.byte	'ccu6',0
	.word	20914
	.byte	4,2,35,24,13
	.byte	'timer',0
	.word	26786
	.byte	1,2,35,28,13
	.byte	'synchronousOperation',0
	.word	488
	.byte	1,2,35,29,13
	.byte	'clock',0
	.word	27768
	.byte	20,2,35,32,13
	.byte	'timer12',0
	.word	28022
	.byte	4,2,35,52,13
	.byte	'timer13',0
	.word	28567
	.byte	4,2,35,56,13
	.byte	'interrupt1',0
	.word	28818
	.byte	6,2,35,60,13
	.byte	'interrupt2',0
	.word	28818
	.byte	6,2,35,66,13
	.byte	'interrupt3',0
	.word	28818
	.byte	6,2,35,72,13
	.byte	'interrupt4',0
	.word	28818
	.byte	6,2,35,78,13
	.byte	'trigger',0
	.word	27283
	.byte	12,2,35,84,13
	.byte	'pins',0
	.word	28967
	.byte	4,2,35,96,0,31
	.word	28972
	.byte	3
	.word	29212
	.byte	29
	.byte	'IfxCcu6_Timer_initModule',0,18,212,2,17,1,1,1,1,5
	.byte	'timer',0,18,212,2,57
	.word	27468
	.byte	5
	.byte	'config',0,18,212,2,92
	.word	29217
	.byte	0,3
	.word	28972
	.byte	29
	.byte	'IfxCcu6_Timer_initModuleConfig',0,18,222,2,17,1,1,1,1,5
	.byte	'config',0,18,222,2,70
	.word	29288
	.byte	5
	.byte	'ccu6',0,18,222,2,88
	.word	20914
	.byte	0,29
	.byte	'IfxCcu6_Timer_start',0,18,249,2,17,1,1,1,1,5
	.byte	'timer',0,18,249,2,52
	.word	27468
	.byte	0,29
	.byte	'IfxCcu6_Timer_stop',0,18,139,3,17,1,1,1,1,5
	.byte	'timer',0,18,139,3,51
	.word	27468
	.byte	0,23
	.word	25674
	.byte	24
	.word	25697
	.byte	6,0,23
	.word	25712
	.byte	24
	.word	25744
	.byte	17,17,25
	.word	10914
	.byte	26
	.word	10952
	.byte	0,0,6,0,0,23
	.word	25762
	.byte	24
	.word	25790
	.byte	6,0,23
	.word	25805
	.byte	17,25
	.word	1989
	.byte	32
	.word	2026
	.byte	25
	.word	1947
	.byte	26
	.word	1987
	.byte	0,26
	.word	2027
	.byte	0,0,6,0,0,23
	.word	25838
	.byte	24
	.word	25864
	.byte	17,25
	.word	2104
	.byte	24
	.word	2137
	.byte	26
	.word	2154
	.byte	0,6,0,0,23
	.word	25902
	.byte	24
	.word	25926
	.byte	17,25
	.word	25992
	.byte	32
	.word	26008
	.byte	25
	.word	25805
	.byte	32
	.word	25834
	.byte	25
	.word	1989
	.byte	32
	.word	2026
	.byte	25
	.word	1947
	.byte	26
	.word	1987
	.byte	0,26
	.word	2027
	.byte	0,0,26
	.word	25835
	.byte	0,0,26
	.word	26009
	.byte	25
	.word	25838
	.byte	24
	.word	25864
	.byte	32
	.word	25881
	.byte	25
	.word	2104
	.byte	24
	.word	2137
	.byte	26
	.word	2154
	.byte	0,26
	.word	25882
	.byte	0,0,26
	.word	26010
	.byte	25
	.word	25674
	.byte	24
	.word	25697
	.byte	26
	.word	25710
	.byte	0,26
	.word	26011
	.byte	0,0,6,0,0,23
	.word	25947
	.byte	24
	.word	25970
	.byte	17,25
	.word	25992
	.byte	32
	.word	26008
	.byte	25
	.word	25805
	.byte	32
	.word	25834
	.byte	25
	.word	1989
	.byte	32
	.word	2026
	.byte	25
	.word	1947
	.byte	26
	.word	1987
	.byte	0,26
	.word	2027
	.byte	0,0,26
	.word	25835
	.byte	0,0,26
	.word	26009
	.byte	25
	.word	25838
	.byte	24
	.word	25864
	.byte	32
	.word	25881
	.byte	25
	.word	2104
	.byte	24
	.word	2137
	.byte	26
	.word	2154
	.byte	0,26
	.word	25882
	.byte	0,0,26
	.word	26010
	.byte	25
	.word	25674
	.byte	24
	.word	25697
	.byte	26
	.word	25710
	.byte	0,26
	.word	26011
	.byte	0,0,6,0,0,23
	.word	25992
	.byte	17,25
	.word	25805
	.byte	32
	.word	25834
	.byte	25
	.word	1989
	.byte	32
	.word	2026
	.byte	25
	.word	1947
	.byte	26
	.word	1987
	.byte	0,26
	.word	2027
	.byte	0,0,26
	.word	25835
	.byte	0,0,6,25
	.word	25838
	.byte	24
	.word	25864
	.byte	32
	.word	25881
	.byte	25
	.word	2104
	.byte	24
	.word	2137
	.byte	26
	.word	2154
	.byte	0,26
	.word	25882
	.byte	0,0,6,25
	.word	25674
	.byte	24
	.word	25697
	.byte	26
	.word	25710
	.byte	0,6,0,0,23
	.word	26014
	.byte	17,25
	.word	25674
	.byte	24
	.word	25697
	.byte	26
	.word	25710
	.byte	0,6,0,0
.L70:
	.byte	15,23,41,9,1,16
	.byte	'CCU60_CH0',0,0,16
	.byte	'CCU60_CH1',0,1,16
	.byte	'CCU61_CH0',0,2,16
	.byte	'CCU61_CH1',0,3,0,10
	.byte	'_Ifx_CPU_ICR_Bits',0,24,246,2,16,4,11
	.byte	'CCPN',0,4
	.word	887
	.byte	10,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	887
	.byte	5,17,2,35,0,11
	.byte	'IE',0,4
	.word	887
	.byte	1,16,2,35,0,11
	.byte	'PIPN',0,4
	.word	887
	.byte	10,6,2,35,0,11
	.byte	'reserved_26',0,4
	.word	887
	.byte	6,0,2,35,0,0
.L131:
	.byte	12,24,223,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30040
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_DBGSR_Bits',0,24,166,1,16,4,11
	.byte	'DE',0,4
	.word	887
	.byte	1,31,2,35,0,11
	.byte	'HALT',0,4
	.word	887
	.byte	2,29,2,35,0,11
	.byte	'SIH',0,4
	.word	887
	.byte	1,28,2,35,0,11
	.byte	'SUSP',0,4
	.word	887
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	887
	.byte	1,26,2,35,0,11
	.byte	'PREVSUSP',0,4
	.word	887
	.byte	1,25,2,35,0,11
	.byte	'PEVT',0,4
	.word	887
	.byte	1,24,2,35,0,11
	.byte	'EVTSRC',0,4
	.word	887
	.byte	5,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	887
	.byte	19,0,2,35,0,0
.L141:
	.byte	12,24,167,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30197
	.byte	4,2,35,0,0,7
	.byte	'short int',0,2,5,33
	.byte	'__wchar_t',0,25,1,1
	.word	30424
	.byte	33
	.byte	'__size_t',0,25,1,1
	.word	465
	.byte	33
	.byte	'__ptrdiff_t',0,25,1,1
	.word	481
	.byte	34,1,3
	.word	30492
	.byte	33
	.byte	'__codeptr',0,25,1,1
	.word	30494
	.byte	33
	.byte	'__intptr_t',0,25,1,1
	.word	481
	.byte	33
	.byte	'__uintptr_t',0,25,1,1
	.word	465
	.byte	33
	.byte	'boolean',0,26,101,29
	.word	488
	.byte	33
	.byte	'uint8',0,26,105,29
	.word	488
	.byte	33
	.byte	'uint16',0,26,109,29
	.word	1039
	.byte	33
	.byte	'uint32',0,26,113,29
	.word	2156
	.byte	33
	.byte	'uint64',0,26,118,29
	.word	347
	.byte	33
	.byte	'sint16',0,26,126,29
	.word	30424
	.byte	7
	.byte	'long int',0,4,5,33
	.byte	'sint32',0,26,131,1,29
	.word	30646
	.byte	33
	.byte	'sint64',0,26,138,1,29
	.word	25885
	.byte	33
	.byte	'float32',0,26,167,1,29
	.word	261
	.byte	33
	.byte	'pvoid',0,21,57,28
	.word	379
	.byte	33
	.byte	'Ifx_TickTime',0,21,79,28
	.word	25885
	.byte	33
	.byte	'Ifx_Priority',0,21,103,16
	.word	1039
	.byte	33
	.byte	'Ifx_TimerValue',0,21,104,16
	.word	2156
	.byte	33
	.byte	'Ifx_RxSel',0,21,140,1,3
	.word	26876
	.byte	14
	.word	373
	.byte	3
	.word	30805
	.byte	30,21,143,1,9,8,13
	.byte	'module',0
	.word	30810
	.byte	4,2,35,0,13
	.byte	'index',0
	.word	30646
	.byte	4,2,35,4,0,33
	.byte	'IfxModule_IndexMap',0,21,147,1,3
	.word	30815
	.byte	33
	.byte	'Ifx_CCU6_ACCEN0_Bits',0,13,79,3
	.word	19483
	.byte	33
	.byte	'Ifx_CCU6_ACCEN1_Bits',0,13,85,3
	.word	19393
	.byte	33
	.byte	'Ifx_CCU6_CC60R_Bits',0,13,92,3
	.word	12533
	.byte	33
	.byte	'Ifx_CCU6_CC60SR_Bits',0,13,99,3
	.word	12848
	.byte	33
	.byte	'Ifx_CCU6_CC61R_Bits',0,13,106,3
	.word	12638
	.byte	33
	.byte	'Ifx_CCU6_CC61SR_Bits',0,13,113,3
	.word	12954
	.byte	33
	.byte	'Ifx_CCU6_CC62R_Bits',0,13,120,3
	.word	12743
	.byte	33
	.byte	'Ifx_CCU6_CC62SR_Bits',0,13,127,3
	.word	13060
	.byte	33
	.byte	'Ifx_CCU6_CC63R_Bits',0,13,134,1,3
	.word	13380
	.byte	33
	.byte	'Ifx_CCU6_CC63SR_Bits',0,13,141,1,3
	.word	13486
	.byte	33
	.byte	'Ifx_CCU6_CLC_Bits',0,13,151,1,3
	.word	10954
	.byte	33
	.byte	'Ifx_CCU6_CMPMODIF_Bits',0,13,168,1,3
	.word	13988
	.byte	33
	.byte	'Ifx_CCU6_CMPSTAT_Bits',0,13,190,1,3
	.word	13593
	.byte	33
	.byte	'Ifx_CCU6_ID_Bits',0,13,198,1,3
	.word	11246
	.byte	33
	.byte	'Ifx_CCU6_IEN_Bits',0,13,220,1,3
	.word	18452
	.byte	33
	.byte	'Ifx_CCU6_IMON_Bits',0,13,236,1,3
	.word	16518
	.byte	33
	.byte	'Ifx_CCU6_INP_Bits',0,13,249,1,3
	.word	18234
	.byte	33
	.byte	'Ifx_CCU6_IS_Bits',0,13,143,2,3
	.word	17136
	.byte	33
	.byte	'Ifx_CCU6_ISR_Bits',0,13,165,2,3
	.word	17860
	.byte	33
	.byte	'Ifx_CCU6_ISS_Bits',0,13,187,2,3
	.word	17493
	.byte	33
	.byte	'Ifx_CCU6_KRST0_Bits',0,13,195,2,3
	.word	19269
	.byte	33
	.byte	'Ifx_CCU6_KRST1_Bits',0,13,202,2,3
	.word	19164
	.byte	33
	.byte	'Ifx_CCU6_KRSTCLR_Bits',0,13,209,2,3
	.word	19057
	.byte	33
	.byte	'Ifx_CCU6_KSCSR_Bits',0,13,219,2,3
	.word	11943
	.byte	33
	.byte	'Ifx_CCU6_LI_Bits',0,13,238,2,3
	.word	16791
	.byte	33
	.byte	'Ifx_CCU6_MCFG_Bits',0,13,247,2,3
	.word	11112
	.byte	33
	.byte	'Ifx_CCU6_MCMCTR_Bits',0,13,132,3,3
	.word	16294
	.byte	33
	.byte	'Ifx_CCU6_MCMOUT_Bits',0,13,143,3,3
	.word	16119
	.byte	33
	.byte	'Ifx_CCU6_MCMOUTS_Bits',0,13,156,3,3
	.word	15895
	.byte	33
	.byte	'Ifx_CCU6_MODCTR_Bits',0,13,168,3,3
	.word	15321
	.byte	33
	.byte	'Ifx_CCU6_MOSEL_Bits',0,13,177,3,3
	.word	11373
	.byte	33
	.byte	'Ifx_CCU6_OCS_Bits',0,13,190,3,3
	.word	18850
	.byte	33
	.byte	'Ifx_CCU6_PISEL0_Bits',0,13,204,3,3
	.word	11523
	.byte	33
	.byte	'Ifx_CCU6_PISEL2_Bits',0,13,215,3,3
	.word	11759
	.byte	33
	.byte	'Ifx_CCU6_PSLR_Bits',0,13,224,3,3
	.word	15752
	.byte	33
	.byte	'Ifx_CCU6_T12_Bits',0,13,231,3,3
	.word	12093
	.byte	33
	.byte	'Ifx_CCU6_T12DTC_Bits',0,13,245,3,3
	.word	12307
	.byte	33
	.byte	'Ifx_CCU6_T12MSEL_Bits',0,13,128,4,3
	.word	14293
	.byte	33
	.byte	'Ifx_CCU6_T12PR_Bits',0,13,135,4,3
	.word	12199
	.byte	33
	.byte	'Ifx_CCU6_T13_Bits',0,13,142,4,3
	.word	13166
	.byte	33
	.byte	'Ifx_CCU6_T13PR_Bits',0,13,149,4,3
	.word	13272
	.byte	33
	.byte	'Ifx_CCU6_TCTR0_Bits',0,13,165,4,3
	.word	14473
	.byte	33
	.byte	'Ifx_CCU6_TCTR2_Bits',0,13,178,4,3
	.word	14733
	.byte	33
	.byte	'Ifx_CCU6_TCTR4_Bits',0,13,199,4,3
	.word	14956
	.byte	33
	.byte	'Ifx_CCU6_TRPCTR_Bits',0,13,212,4,3
	.word	15533
	.byte	33
	.byte	'Ifx_CCU6_ACCEN0',0,13,225,4,3
	.word	20013
	.byte	33
	.byte	'Ifx_CCU6_ACCEN1',0,13,233,4,3
	.word	19443
	.byte	33
	.byte	'Ifx_CCU6_CC60R',0,13,241,4,3
	.word	12598
	.byte	33
	.byte	'Ifx_CCU6_CC60SR',0,13,249,4,3
	.word	12914
	.byte	33
	.byte	'Ifx_CCU6_CC61R',0,13,129,5,3
	.word	12703
	.byte	33
	.byte	'Ifx_CCU6_CC61SR',0,13,137,5,3
	.word	13020
	.byte	33
	.byte	'Ifx_CCU6_CC62R',0,13,145,5,3
	.word	12808
	.byte	33
	.byte	'Ifx_CCU6_CC62SR',0,13,153,5,3
	.word	13126
	.byte	33
	.byte	'Ifx_CCU6_CC63R',0,13,161,5,3
	.word	13446
	.byte	33
	.byte	'Ifx_CCU6_CC63SR',0,13,169,5,3
	.word	13553
	.byte	33
	.byte	'Ifx_CCU6_CLC',0,13,177,5,3
	.word	11072
	.byte	33
	.byte	'Ifx_CCU6_CMPMODIF',0,13,185,5,3
	.word	14253
	.byte	33
	.byte	'Ifx_CCU6_CMPSTAT',0,13,193,5,3
	.word	13948
	.byte	33
	.byte	'Ifx_CCU6_ID',0,13,201,5,3
	.word	11333
	.byte	33
	.byte	'Ifx_CCU6_IEN',0,13,209,5,3
	.word	18801
	.byte	33
	.byte	'Ifx_CCU6_IMON',0,13,217,5,3
	.word	16751
	.byte	33
	.byte	'Ifx_CCU6_INP',0,13,225,5,3
	.word	18412
	.byte	33
	.byte	'Ifx_CCU6_IS',0,13,233,5,3
	.word	17453
	.byte	33
	.byte	'Ifx_CCU6_ISR',0,13,241,5,3
	.word	18194
	.byte	33
	.byte	'Ifx_CCU6_ISS',0,13,249,5,3
	.word	17820
	.byte	33
	.byte	'Ifx_CCU6_KRST0',0,13,129,6,3
	.word	19353
	.byte	33
	.byte	'Ifx_CCU6_KRST1',0,13,137,6,3
	.word	19229
	.byte	33
	.byte	'Ifx_CCU6_KRSTCLR',0,13,145,6,3
	.word	19124
	.byte	33
	.byte	'Ifx_CCU6_KSCSR',0,13,153,6,3
	.word	12053
	.byte	33
	.byte	'Ifx_CCU6_LI',0,13,161,6,3
	.word	17096
	.byte	33
	.byte	'Ifx_CCU6_MCFG',0,13,169,6,3
	.word	11206
	.byte	33
	.byte	'Ifx_CCU6_MCMCTR',0,13,177,6,3
	.word	16478
	.byte	33
	.byte	'Ifx_CCU6_MCMOUT',0,13,185,6,3
	.word	16254
	.byte	33
	.byte	'Ifx_CCU6_MCMOUTS',0,13,193,6,3
	.word	16079
	.byte	33
	.byte	'Ifx_CCU6_MODCTR',0,13,201,6,3
	.word	15493
	.byte	33
	.byte	'Ifx_CCU6_MOSEL',0,13,209,6,3
	.word	11483
	.byte	33
	.byte	'Ifx_CCU6_OCS',0,13,217,6,3
	.word	19017
	.byte	33
	.byte	'Ifx_CCU6_PISEL0',0,13,225,6,3
	.word	11719
	.byte	33
	.byte	'Ifx_CCU6_PISEL2',0,13,233,6,3
	.word	11903
	.byte	33
	.byte	'Ifx_CCU6_PSLR',0,13,241,6,3
	.word	15855
	.byte	33
	.byte	'Ifx_CCU6_T12',0,13,249,6,3
	.word	12159
	.byte	33
	.byte	'Ifx_CCU6_T12DTC',0,13,129,7,3
	.word	12493
	.byte	33
	.byte	'Ifx_CCU6_T12MSEL',0,13,137,7,3
	.word	14433
	.byte	33
	.byte	'Ifx_CCU6_T12PR',0,13,145,7,3
	.word	12267
	.byte	33
	.byte	'Ifx_CCU6_T13',0,13,153,7,3
	.word	13232
	.byte	33
	.byte	'Ifx_CCU6_T13PR',0,13,161,7,3
	.word	13340
	.byte	33
	.byte	'Ifx_CCU6_TCTR0',0,13,169,7,3
	.word	14693
	.byte	33
	.byte	'Ifx_CCU6_TCTR2',0,13,177,7,3
	.word	14916
	.byte	33
	.byte	'Ifx_CCU6_TCTR4',0,13,185,7,3
	.word	15281
	.byte	33
	.byte	'Ifx_CCU6_TRPCTR',0,13,193,7,3
	.word	15712
	.byte	14
	.word	20053
	.byte	33
	.byte	'Ifx_CCU6',0,13,130,8,3
	.word	33250
	.byte	33
	.byte	'IfxCcu6_Index',0,17,88,3
	.word	26478
	.byte	18,16
	.word	30815
	.byte	19,1,0,31
	.word	33295
	.byte	35
	.byte	'IfxCcu6_cfg_indexMap',0,17,111,41
	.word	33304
	.byte	1,1,33
	.byte	'IfxSrc_Tos',0,22,74,3
	.word	28759
	.byte	33
	.byte	'Ifx_SRC_SRCR_Bits',0,4,62,3
	.word	505
	.byte	33
	.byte	'Ifx_SRC_SRCR',0,4,75,3
	.word	795
	.byte	10
	.byte	'_Ifx_SRC_AGBT',0,4,86,25,4,13
	.byte	'SR',0
	.word	795
	.byte	4,2,35,0,0,14
	.word	33406
	.byte	33
	.byte	'Ifx_SRC_AGBT',0,4,89,3
	.word	33438
	.byte	10
	.byte	'_Ifx_SRC_ASCLIN',0,4,92,25,12,13
	.byte	'TX',0
	.word	795
	.byte	4,2,35,0,13
	.byte	'RX',0
	.word	795
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	795
	.byte	4,2,35,8,0,14
	.word	33464
	.byte	33
	.byte	'Ifx_SRC_ASCLIN',0,4,97,3
	.word	33523
	.byte	10
	.byte	'_Ifx_SRC_BCUSPB',0,4,100,25,4,13
	.byte	'SBSRC',0
	.word	795
	.byte	4,2,35,0,0,14
	.word	33551
	.byte	33
	.byte	'Ifx_SRC_BCUSPB',0,4,103,3
	.word	33588
	.byte	18,64
	.word	795
	.byte	19,15,0,10
	.byte	'_Ifx_SRC_CAN',0,4,106,25,64,13
	.byte	'INT',0
	.word	33616
	.byte	64,2,35,0,0,14
	.word	33625
	.byte	33
	.byte	'Ifx_SRC_CAN',0,4,109,3
	.word	33657
	.byte	10
	.byte	'_Ifx_SRC_CCU6',0,4,112,25,16,13
	.byte	'SR0',0
	.word	795
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	795
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	795
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	795
	.byte	4,2,35,12,0,14
	.word	33682
	.byte	33
	.byte	'Ifx_SRC_CCU6',0,4,118,3
	.word	33754
	.byte	18,8
	.word	795
	.byte	19,1,0,10
	.byte	'_Ifx_SRC_CERBERUS',0,4,121,25,8,13
	.byte	'SR',0
	.word	33780
	.byte	8,2,35,0,0,14
	.word	33789
	.byte	33
	.byte	'Ifx_SRC_CERBERUS',0,4,124,3
	.word	33825
	.byte	10
	.byte	'_Ifx_SRC_CIF',0,4,127,25,16,13
	.byte	'MI',0
	.word	795
	.byte	4,2,35,0,13
	.byte	'MIEP',0
	.word	795
	.byte	4,2,35,4,13
	.byte	'ISP',0
	.word	795
	.byte	4,2,35,8,13
	.byte	'MJPEG',0
	.word	795
	.byte	4,2,35,12,0,14
	.word	33855
	.byte	33
	.byte	'Ifx_SRC_CIF',0,4,133,1,3
	.word	33928
	.byte	10
	.byte	'_Ifx_SRC_CPU',0,4,136,1,25,4,13
	.byte	'SBSRC',0
	.word	795
	.byte	4,2,35,0,0,14
	.word	33954
	.byte	33
	.byte	'Ifx_SRC_CPU',0,4,139,1,3
	.word	33989
	.byte	18,192,1
	.word	795
	.byte	19,47,0,10
	.byte	'_Ifx_SRC_DMA',0,4,142,1,25,208,1,13
	.byte	'ERR',0
	.word	795
	.byte	4,2,35,0,13
	.byte	'reserved_4',0
	.word	5552
	.byte	12,2,35,4,13
	.byte	'CH',0
	.word	34015
	.byte	192,1,2,35,16,0,14
	.word	34025
	.byte	33
	.byte	'Ifx_SRC_DMA',0,4,147,1,3
	.word	34092
	.byte	10
	.byte	'_Ifx_SRC_DSADC',0,4,150,1,25,8,13
	.byte	'SRM',0
	.word	795
	.byte	4,2,35,0,13
	.byte	'SRA',0
	.word	795
	.byte	4,2,35,4,0,14
	.word	34118
	.byte	33
	.byte	'Ifx_SRC_DSADC',0,4,154,1,3
	.word	34166
	.byte	10
	.byte	'_Ifx_SRC_EMEM',0,4,157,1,25,4,13
	.byte	'SR',0
	.word	795
	.byte	4,2,35,0,0,14
	.word	34194
	.byte	33
	.byte	'Ifx_SRC_EMEM',0,4,160,1,3
	.word	34227
	.byte	18,40
	.word	488
	.byte	19,39,0,10
	.byte	'_Ifx_SRC_ERAY',0,4,163,1,25,80,13
	.byte	'INT',0
	.word	33780
	.byte	8,2,35,0,13
	.byte	'TINT',0
	.word	33780
	.byte	8,2,35,8,13
	.byte	'NDAT',0
	.word	33780
	.byte	8,2,35,16,13
	.byte	'MBSC',0
	.word	33780
	.byte	8,2,35,24,13
	.byte	'OBUSY',0
	.word	795
	.byte	4,2,35,32,13
	.byte	'IBUSY',0
	.word	795
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	34254
	.byte	40,2,35,40,0,14
	.word	34263
	.byte	33
	.byte	'Ifx_SRC_ERAY',0,4,172,1,3
	.word	34390
	.byte	10
	.byte	'_Ifx_SRC_ETH',0,4,175,1,25,4,13
	.byte	'SR',0
	.word	795
	.byte	4,2,35,0,0,14
	.word	34417
	.byte	33
	.byte	'Ifx_SRC_ETH',0,4,178,1,3
	.word	34449
	.byte	10
	.byte	'_Ifx_SRC_FCE',0,4,181,1,25,4,13
	.byte	'SR',0
	.word	795
	.byte	4,2,35,0,0,14
	.word	34475
	.byte	33
	.byte	'Ifx_SRC_FCE',0,4,184,1,3
	.word	34507
	.byte	10
	.byte	'_Ifx_SRC_FFT',0,4,187,1,25,12,13
	.byte	'DONE',0
	.word	795
	.byte	4,2,35,0,13
	.byte	'ERR',0
	.word	795
	.byte	4,2,35,4,13
	.byte	'RFS',0
	.word	795
	.byte	4,2,35,8,0,14
	.word	34533
	.byte	33
	.byte	'Ifx_SRC_FFT',0,4,192,1,3
	.word	34593
	.byte	18,16
	.word	488
	.byte	19,15,0,10
	.byte	'_Ifx_SRC_GPSR',0,4,195,1,25,32,13
	.byte	'SR0',0
	.word	795
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	795
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	795
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	795
	.byte	4,2,35,12,13
	.byte	'reserved_10',0
	.word	34619
	.byte	16,2,35,16,0,14
	.word	34628
	.byte	33
	.byte	'Ifx_SRC_GPSR',0,4,202,1,3
	.word	34722
	.byte	10
	.byte	'_Ifx_SRC_GPT12',0,4,205,1,25,48,13
	.byte	'CIRQ',0
	.word	795
	.byte	4,2,35,0,13
	.byte	'T2',0
	.word	795
	.byte	4,2,35,4,13
	.byte	'T3',0
	.word	795
	.byte	4,2,35,8,13
	.byte	'T4',0
	.word	795
	.byte	4,2,35,12,13
	.byte	'T5',0
	.word	795
	.byte	4,2,35,16,13
	.byte	'T6',0
	.word	795
	.byte	4,2,35,20,13
	.byte	'reserved_18',0
	.word	4583
	.byte	24,2,35,24,0,14
	.word	34749
	.byte	33
	.byte	'Ifx_SRC_GPT12',0,4,214,1,3
	.word	34866
	.byte	18,12
	.word	795
	.byte	19,2,0,18,32
	.word	795
	.byte	19,7,0,18,32
	.word	34903
	.byte	19,0,0,18,88
	.word	488
	.byte	19,87,0,18,108
	.word	795
	.byte	19,26,0,18,96
	.word	488
	.byte	19,95,0,18,96
	.word	34903
	.byte	19,2,0,18,160,3
	.word	488
	.byte	19,159,3,0,18,64
	.word	34903
	.byte	19,1,0,18,192,3
	.word	488
	.byte	19,191,3,0,18,16
	.word	795
	.byte	19,3,0,18,64
	.word	34988
	.byte	19,3,0,18,192,2
	.word	488
	.byte	19,191,2,0,10
	.byte	'_Ifx_SRC_GTM',0,4,217,1,25,204,18,13
	.byte	'AEIIRQ',0
	.word	795
	.byte	4,2,35,0,13
	.byte	'ARUIRQ',0
	.word	34894
	.byte	12,2,35,4,13
	.byte	'reserved_10',0
	.word	3393
	.byte	4,2,35,16,13
	.byte	'BRCIRQ',0
	.word	795
	.byte	4,2,35,20,13
	.byte	'CMPIRQ',0
	.word	795
	.byte	4,2,35,24,13
	.byte	'SPEIRQ',0
	.word	33780
	.byte	8,2,35,28,13
	.byte	'reserved_24',0
	.word	5212
	.byte	8,2,35,36,13
	.byte	'PSM',0
	.word	34912
	.byte	32,2,35,44,13
	.byte	'reserved_4C',0
	.word	34921
	.byte	88,2,35,76,13
	.byte	'DPLL',0
	.word	34930
	.byte	108,3,35,164,1,13
	.byte	'reserved_110',0
	.word	34939
	.byte	96,3,35,144,2,13
	.byte	'ERR',0
	.word	795
	.byte	4,3,35,240,2,13
	.byte	'reserved_174',0
	.word	5552
	.byte	12,3,35,244,2,13
	.byte	'TIM',0
	.word	34948
	.byte	96,3,35,128,3,13
	.byte	'reserved_1E0',0
	.word	34957
	.byte	160,3,3,35,224,3,13
	.byte	'MCS',0
	.word	34948
	.byte	96,3,35,128,7,13
	.byte	'reserved_3E0',0
	.word	34957
	.byte	160,3,3,35,224,7,13
	.byte	'TOM',0
	.word	34968
	.byte	64,3,35,128,11,13
	.byte	'reserved_5C0',0
	.word	34977
	.byte	192,3,3,35,192,11,13
	.byte	'ATOM',0
	.word	34997
	.byte	64,3,35,128,15,13
	.byte	'reserved_7C0',0
	.word	35006
	.byte	192,2,3,35,192,15,13
	.byte	'MCSW0',0
	.word	34894
	.byte	12,3,35,128,18,13
	.byte	'reserved_90C',0
	.word	18841
	.byte	52,3,35,140,18,13
	.byte	'MCSW1',0
	.word	34894
	.byte	12,3,35,192,18,0,14
	.word	35017
	.byte	33
	.byte	'Ifx_SRC_GTM',0,4,243,1,3
	.word	35477
	.byte	10
	.byte	'_Ifx_SRC_HSCT',0,4,246,1,25,4,13
	.byte	'SR',0
	.word	795
	.byte	4,2,35,0,0,14
	.word	35503
	.byte	33
	.byte	'Ifx_SRC_HSCT',0,4,249,1,3
	.word	35536
	.byte	10
	.byte	'_Ifx_SRC_HSSL',0,4,252,1,25,16,13
	.byte	'COK',0
	.word	795
	.byte	4,2,35,0,13
	.byte	'RDI',0
	.word	795
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	795
	.byte	4,2,35,8,13
	.byte	'TRG',0
	.word	795
	.byte	4,2,35,12,0,14
	.word	35563
	.byte	33
	.byte	'Ifx_SRC_HSSL',0,4,130,2,3
	.word	35636
	.byte	18,56
	.word	488
	.byte	19,55,0,10
	.byte	'_Ifx_SRC_I2C',0,4,133,2,25,80,13
	.byte	'BREQ',0
	.word	795
	.byte	4,2,35,0,13
	.byte	'LBREQ',0
	.word	795
	.byte	4,2,35,4,13
	.byte	'SREQ',0
	.word	795
	.byte	4,2,35,8,13
	.byte	'LSREQ',0
	.word	795
	.byte	4,2,35,12,13
	.byte	'ERR',0
	.word	795
	.byte	4,2,35,16,13
	.byte	'P',0
	.word	795
	.byte	4,2,35,20,13
	.byte	'reserved_18',0
	.word	35663
	.byte	56,2,35,24,0,14
	.word	35672
	.byte	33
	.byte	'Ifx_SRC_I2C',0,4,142,2,3
	.word	35795
	.byte	10
	.byte	'_Ifx_SRC_LMU',0,4,145,2,25,4,13
	.byte	'SR',0
	.word	795
	.byte	4,2,35,0,0,14
	.word	35821
	.byte	33
	.byte	'Ifx_SRC_LMU',0,4,148,2,3
	.word	35853
	.byte	10
	.byte	'_Ifx_SRC_MSC',0,4,151,2,25,20,13
	.byte	'SR0',0
	.word	795
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	795
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	795
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	795
	.byte	4,2,35,12,13
	.byte	'SR4',0
	.word	795
	.byte	4,2,35,16,0,14
	.word	35879
	.byte	33
	.byte	'Ifx_SRC_MSC',0,4,158,2,3
	.word	35964
	.byte	10
	.byte	'_Ifx_SRC_PMU',0,4,161,2,25,4,13
	.byte	'SR',0
	.word	795
	.byte	4,2,35,0,0,14
	.word	35990
	.byte	33
	.byte	'Ifx_SRC_PMU',0,4,164,2,3
	.word	36022
	.byte	10
	.byte	'_Ifx_SRC_PSI5',0,4,167,2,25,32,13
	.byte	'SR',0
	.word	34903
	.byte	32,2,35,0,0,14
	.word	36048
	.byte	33
	.byte	'Ifx_SRC_PSI5',0,4,170,2,3
	.word	36081
	.byte	10
	.byte	'_Ifx_SRC_PSI5S',0,4,173,2,25,32,13
	.byte	'SR',0
	.word	34903
	.byte	32,2,35,0,0,14
	.word	36108
	.byte	33
	.byte	'Ifx_SRC_PSI5S',0,4,176,2,3
	.word	36142
	.byte	10
	.byte	'_Ifx_SRC_QSPI',0,4,179,2,25,24,13
	.byte	'TX',0
	.word	795
	.byte	4,2,35,0,13
	.byte	'RX',0
	.word	795
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	795
	.byte	4,2,35,8,13
	.byte	'PT',0
	.word	795
	.byte	4,2,35,12,13
	.byte	'HC',0
	.word	795
	.byte	4,2,35,16,13
	.byte	'U',0
	.word	795
	.byte	4,2,35,20,0,14
	.word	36170
	.byte	33
	.byte	'Ifx_SRC_QSPI',0,4,187,2,3
	.word	36263
	.byte	10
	.byte	'_Ifx_SRC_SCR',0,4,190,2,25,4,13
	.byte	'SR',0
	.word	795
	.byte	4,2,35,0,0,14
	.word	36290
	.byte	33
	.byte	'Ifx_SRC_SCR',0,4,193,2,3
	.word	36322
	.byte	10
	.byte	'_Ifx_SRC_SCU',0,4,196,2,25,20,13
	.byte	'DTS',0
	.word	795
	.byte	4,2,35,0,13
	.byte	'ERU',0
	.word	34988
	.byte	16,2,35,4,0,14
	.word	36348
	.byte	33
	.byte	'Ifx_SRC_SCU',0,4,200,2,3
	.word	36394
	.byte	18,24
	.word	795
	.byte	19,5,0,10
	.byte	'_Ifx_SRC_SENT',0,4,203,2,25,24,13
	.byte	'SR',0
	.word	36420
	.byte	24,2,35,0,0,14
	.word	36429
	.byte	33
	.byte	'Ifx_SRC_SENT',0,4,206,2,3
	.word	36462
	.byte	10
	.byte	'_Ifx_SRC_SMU',0,4,209,2,25,12,13
	.byte	'SR',0
	.word	34894
	.byte	12,2,35,0,0,14
	.word	36489
	.byte	33
	.byte	'Ifx_SRC_SMU',0,4,212,2,3
	.word	36521
	.byte	10
	.byte	'_Ifx_SRC_STM',0,4,215,2,25,8,13
	.byte	'SR0',0
	.word	795
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	795
	.byte	4,2,35,4,0,14
	.word	36547
	.byte	33
	.byte	'Ifx_SRC_STM',0,4,219,2,3
	.word	36593
	.byte	10
	.byte	'_Ifx_SRC_VADCCG',0,4,222,2,25,16,13
	.byte	'SR0',0
	.word	795
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	795
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	795
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	795
	.byte	4,2,35,12,0,14
	.word	36619
	.byte	33
	.byte	'Ifx_SRC_VADCCG',0,4,228,2,3
	.word	36694
	.byte	10
	.byte	'_Ifx_SRC_VADCG',0,4,231,2,25,16,13
	.byte	'SR0',0
	.word	795
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	795
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	795
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	795
	.byte	4,2,35,12,0,14
	.word	36723
	.byte	33
	.byte	'Ifx_SRC_VADCG',0,4,237,2,3
	.word	36797
	.byte	10
	.byte	'_Ifx_SRC_XBAR',0,4,240,2,25,4,13
	.byte	'SRC',0
	.word	795
	.byte	4,2,35,0,0,14
	.word	36825
	.byte	33
	.byte	'Ifx_SRC_XBAR',0,4,243,2,3
	.word	36859
	.byte	18,4
	.word	33406
	.byte	19,0,0,14
	.word	36886
	.byte	10
	.byte	'_Ifx_SRC_GAGBT',0,4,128,3,25,4,13
	.byte	'AGBT',0
	.word	36895
	.byte	4,2,35,0,0,14
	.word	36900
	.byte	33
	.byte	'Ifx_SRC_GAGBT',0,4,131,3,3
	.word	36936
	.byte	18,48
	.word	33464
	.byte	19,3,0,14
	.word	36964
	.byte	10
	.byte	'_Ifx_SRC_GASCLIN',0,4,134,3,25,48,13
	.byte	'ASCLIN',0
	.word	36973
	.byte	48,2,35,0,0,14
	.word	36978
	.byte	33
	.byte	'Ifx_SRC_GASCLIN',0,4,137,3,3
	.word	37018
	.byte	14
	.word	33551
	.byte	10
	.byte	'_Ifx_SRC_GBCU',0,4,140,3,25,4,13
	.byte	'SPB',0
	.word	37048
	.byte	4,2,35,0,0,14
	.word	37053
	.byte	33
	.byte	'Ifx_SRC_GBCU',0,4,143,3,3
	.word	37087
	.byte	18,64
	.word	33625
	.byte	19,0,0,14
	.word	37114
	.byte	10
	.byte	'_Ifx_SRC_GCAN',0,4,146,3,25,64,13
	.byte	'CAN',0
	.word	37123
	.byte	64,2,35,0,0,14
	.word	37128
	.byte	33
	.byte	'Ifx_SRC_GCAN',0,4,149,3,3
	.word	37162
	.byte	18,32
	.word	33682
	.byte	19,1,0,14
	.word	37189
	.byte	10
	.byte	'_Ifx_SRC_GCCU6',0,4,152,3,25,32,13
	.byte	'CCU6',0
	.word	37198
	.byte	32,2,35,0,0,14
	.word	37203
	.byte	33
	.byte	'Ifx_SRC_GCCU6',0,4,155,3,3
	.word	37239
	.byte	14
	.word	33789
	.byte	10
	.byte	'_Ifx_SRC_GCERBERUS',0,4,158,3,25,8,13
	.byte	'CERBERUS',0
	.word	37267
	.byte	8,2,35,0,0,14
	.word	37272
	.byte	33
	.byte	'Ifx_SRC_GCERBERUS',0,4,161,3,3
	.word	37316
	.byte	18,16
	.word	33855
	.byte	19,0,0,14
	.word	37348
	.byte	10
	.byte	'_Ifx_SRC_GCIF',0,4,164,3,25,16,13
	.byte	'CIF',0
	.word	37357
	.byte	16,2,35,0,0,14
	.word	37362
	.byte	33
	.byte	'Ifx_SRC_GCIF',0,4,167,3,3
	.word	37396
	.byte	18,8
	.word	33954
	.byte	19,1,0,14
	.word	37423
	.byte	10
	.byte	'_Ifx_SRC_GCPU',0,4,170,3,25,8,13
	.byte	'CPU',0
	.word	37432
	.byte	8,2,35,0,0,14
	.word	37437
	.byte	33
	.byte	'Ifx_SRC_GCPU',0,4,173,3,3
	.word	37471
	.byte	18,208,1
	.word	34025
	.byte	19,0,0,14
	.word	37498
	.byte	10
	.byte	'_Ifx_SRC_GDMA',0,4,176,3,25,208,1,13
	.byte	'DMA',0
	.word	37508
	.byte	208,1,2,35,0,0,14
	.word	37513
	.byte	33
	.byte	'Ifx_SRC_GDMA',0,4,179,3,3
	.word	37549
	.byte	14
	.word	34118
	.byte	14
	.word	34118
	.byte	14
	.word	34118
	.byte	10
	.byte	'_Ifx_SRC_GDSADC',0,4,182,3,25,32,13
	.byte	'DSADC0',0
	.word	37576
	.byte	8,2,35,0,13
	.byte	'reserved_8',0
	.word	5212
	.byte	8,2,35,8,13
	.byte	'DSADC2',0
	.word	37581
	.byte	8,2,35,16,13
	.byte	'DSADC3',0
	.word	37586
	.byte	8,2,35,24,0,14
	.word	37591
	.byte	33
	.byte	'Ifx_SRC_GDSADC',0,4,188,3,3
	.word	37682
	.byte	18,4
	.word	34194
	.byte	19,0,0,14
	.word	37711
	.byte	10
	.byte	'_Ifx_SRC_GEMEM',0,4,191,3,25,4,13
	.byte	'EMEM',0
	.word	37720
	.byte	4,2,35,0,0,14
	.word	37725
	.byte	33
	.byte	'Ifx_SRC_GEMEM',0,4,194,3,3
	.word	37761
	.byte	18,80
	.word	34263
	.byte	19,0,0,14
	.word	37789
	.byte	10
	.byte	'_Ifx_SRC_GERAY',0,4,197,3,25,80,13
	.byte	'ERAY',0
	.word	37798
	.byte	80,2,35,0,0,14
	.word	37803
	.byte	33
	.byte	'Ifx_SRC_GERAY',0,4,200,3,3
	.word	37839
	.byte	18,4
	.word	34417
	.byte	19,0,0,14
	.word	37867
	.byte	10
	.byte	'_Ifx_SRC_GETH',0,4,203,3,25,4,13
	.byte	'ETH',0
	.word	37876
	.byte	4,2,35,0,0,14
	.word	37881
	.byte	33
	.byte	'Ifx_SRC_GETH',0,4,206,3,3
	.word	37915
	.byte	18,4
	.word	34475
	.byte	19,0,0,14
	.word	37942
	.byte	10
	.byte	'_Ifx_SRC_GFCE',0,4,209,3,25,4,13
	.byte	'FCE',0
	.word	37951
	.byte	4,2,35,0,0,14
	.word	37956
	.byte	33
	.byte	'Ifx_SRC_GFCE',0,4,212,3,3
	.word	37990
	.byte	18,12
	.word	34533
	.byte	19,0,0,14
	.word	38017
	.byte	10
	.byte	'_Ifx_SRC_GFFT',0,4,215,3,25,12,13
	.byte	'FFT',0
	.word	38026
	.byte	12,2,35,0,0,14
	.word	38031
	.byte	33
	.byte	'Ifx_SRC_GFFT',0,4,218,3,3
	.word	38065
	.byte	18,64
	.word	34628
	.byte	19,1,0,14
	.word	38092
	.byte	10
	.byte	'_Ifx_SRC_GGPSR',0,4,221,3,25,64,13
	.byte	'GPSR',0
	.word	38101
	.byte	64,2,35,0,0,14
	.word	38106
	.byte	33
	.byte	'Ifx_SRC_GGPSR',0,4,224,3,3
	.word	38142
	.byte	18,48
	.word	34749
	.byte	19,0,0,14
	.word	38170
	.byte	10
	.byte	'_Ifx_SRC_GGPT12',0,4,227,3,25,48,13
	.byte	'GPT12',0
	.word	38179
	.byte	48,2,35,0,0,14
	.word	38184
	.byte	33
	.byte	'Ifx_SRC_GGPT12',0,4,230,3,3
	.word	38222
	.byte	18,204,18
	.word	35017
	.byte	19,0,0,14
	.word	38251
	.byte	10
	.byte	'_Ifx_SRC_GGTM',0,4,233,3,25,204,18,13
	.byte	'GTM',0
	.word	38261
	.byte	204,18,2,35,0,0,14
	.word	38266
	.byte	33
	.byte	'Ifx_SRC_GGTM',0,4,236,3,3
	.word	38302
	.byte	18,4
	.word	35503
	.byte	19,0,0,14
	.word	38329
	.byte	10
	.byte	'_Ifx_SRC_GHSCT',0,4,239,3,25,4,13
	.byte	'HSCT',0
	.word	38338
	.byte	4,2,35,0,0,14
	.word	38343
	.byte	33
	.byte	'Ifx_SRC_GHSCT',0,4,242,3,3
	.word	38379
	.byte	18,64
	.word	35563
	.byte	19,3,0,14
	.word	38407
	.byte	10
	.byte	'_Ifx_SRC_GHSSL',0,4,245,3,25,68,13
	.byte	'HSSL',0
	.word	38416
	.byte	64,2,35,0,13
	.byte	'EXI',0
	.word	795
	.byte	4,2,35,64,0,14
	.word	38421
	.byte	33
	.byte	'Ifx_SRC_GHSSL',0,4,249,3,3
	.word	38470
	.byte	18,80
	.word	35672
	.byte	19,0,0,14
	.word	38498
	.byte	10
	.byte	'_Ifx_SRC_GI2C',0,4,252,3,25,80,13
	.byte	'I2C',0
	.word	38507
	.byte	80,2,35,0,0,14
	.word	38512
	.byte	33
	.byte	'Ifx_SRC_GI2C',0,4,255,3,3
	.word	38546
	.byte	18,4
	.word	35821
	.byte	19,0,0,14
	.word	38573
	.byte	10
	.byte	'_Ifx_SRC_GLMU',0,4,130,4,25,4,13
	.byte	'LMU',0
	.word	38582
	.byte	4,2,35,0,0,14
	.word	38587
	.byte	33
	.byte	'Ifx_SRC_GLMU',0,4,133,4,3
	.word	38621
	.byte	18,40
	.word	35879
	.byte	19,1,0,14
	.word	38648
	.byte	10
	.byte	'_Ifx_SRC_GMSC',0,4,136,4,25,40,13
	.byte	'MSC',0
	.word	38657
	.byte	40,2,35,0,0,14
	.word	38662
	.byte	33
	.byte	'Ifx_SRC_GMSC',0,4,139,4,3
	.word	38696
	.byte	18,8
	.word	35990
	.byte	19,1,0,14
	.word	38723
	.byte	10
	.byte	'_Ifx_SRC_GPMU',0,4,142,4,25,8,13
	.byte	'PMU',0
	.word	38732
	.byte	8,2,35,0,0,14
	.word	38737
	.byte	33
	.byte	'Ifx_SRC_GPMU',0,4,145,4,3
	.word	38771
	.byte	18,32
	.word	36048
	.byte	19,0,0,14
	.word	38798
	.byte	10
	.byte	'_Ifx_SRC_GPSI5',0,4,148,4,25,32,13
	.byte	'PSI5',0
	.word	38807
	.byte	32,2,35,0,0,14
	.word	38812
	.byte	33
	.byte	'Ifx_SRC_GPSI5',0,4,151,4,3
	.word	38848
	.byte	18,32
	.word	36108
	.byte	19,0,0,14
	.word	38876
	.byte	10
	.byte	'_Ifx_SRC_GPSI5S',0,4,154,4,25,32,13
	.byte	'PSI5S',0
	.word	38885
	.byte	32,2,35,0,0,14
	.word	38890
	.byte	33
	.byte	'Ifx_SRC_GPSI5S',0,4,157,4,3
	.word	38928
	.byte	18,96
	.word	36170
	.byte	19,3,0,14
	.word	38957
	.byte	10
	.byte	'_Ifx_SRC_GQSPI',0,4,160,4,25,96,13
	.byte	'QSPI',0
	.word	38966
	.byte	96,2,35,0,0,14
	.word	38971
	.byte	33
	.byte	'Ifx_SRC_GQSPI',0,4,163,4,3
	.word	39007
	.byte	18,4
	.word	36290
	.byte	19,0,0,14
	.word	39035
	.byte	10
	.byte	'_Ifx_SRC_GSCR',0,4,166,4,25,4,13
	.byte	'SCR',0
	.word	39044
	.byte	4,2,35,0,0,14
	.word	39049
	.byte	33
	.byte	'Ifx_SRC_GSCR',0,4,169,4,3
	.word	39083
	.byte	14
	.word	36348
	.byte	10
	.byte	'_Ifx_SRC_GSCU',0,4,172,4,25,20,13
	.byte	'SCU',0
	.word	39110
	.byte	20,2,35,0,0,14
	.word	39115
	.byte	33
	.byte	'Ifx_SRC_GSCU',0,4,175,4,3
	.word	39149
	.byte	18,24
	.word	36429
	.byte	19,0,0,14
	.word	39176
	.byte	10
	.byte	'_Ifx_SRC_GSENT',0,4,178,4,25,24,13
	.byte	'SENT',0
	.word	39185
	.byte	24,2,35,0,0,14
	.word	39190
	.byte	33
	.byte	'Ifx_SRC_GSENT',0,4,181,4,3
	.word	39226
	.byte	18,12
	.word	36489
	.byte	19,0,0,14
	.word	39254
	.byte	10
	.byte	'_Ifx_SRC_GSMU',0,4,184,4,25,12,13
	.byte	'SMU',0
	.word	39263
	.byte	12,2,35,0,0,14
	.word	39268
	.byte	33
	.byte	'Ifx_SRC_GSMU',0,4,187,4,3
	.word	39302
	.byte	18,16
	.word	36547
	.byte	19,1,0,14
	.word	39329
	.byte	10
	.byte	'_Ifx_SRC_GSTM',0,4,190,4,25,16,13
	.byte	'STM',0
	.word	39338
	.byte	16,2,35,0,0,14
	.word	39343
	.byte	33
	.byte	'Ifx_SRC_GSTM',0,4,193,4,3
	.word	39377
	.byte	18,64
	.word	36723
	.byte	19,3,0,14
	.word	39404
	.byte	18,224,1
	.word	488
	.byte	19,223,1,0,18,32
	.word	36619
	.byte	19,1,0,14
	.word	39429
	.byte	10
	.byte	'_Ifx_SRC_GVADC',0,4,196,4,25,192,2,13
	.byte	'G',0
	.word	39413
	.byte	64,2,35,0,13
	.byte	'reserved_40',0
	.word	39418
	.byte	224,1,2,35,64,13
	.byte	'CG',0
	.word	39438
	.byte	32,3,35,160,2,0,14
	.word	39443
	.byte	33
	.byte	'Ifx_SRC_GVADC',0,4,201,4,3
	.word	39512
	.byte	14
	.word	36825
	.byte	10
	.byte	'_Ifx_SRC_GXBAR',0,4,204,4,25,4,13
	.byte	'XBAR',0
	.word	39540
	.byte	4,2,35,0,0,14
	.word	39545
	.byte	33
	.byte	'Ifx_SRC_GXBAR',0,4,207,4,3
	.word	39581
	.byte	15,27,236,10,9,1,16
	.byte	'IfxScu_CCUCON0_CLKSEL_fBack',0,0,16
	.byte	'IfxScu_CCUCON0_CLKSEL_fPll',0,1,0,33
	.byte	'IfxScu_CCUCON0_CLKSEL',0,27,240,10,3
	.word	39609
	.byte	15,27,250,10,9,1,16
	.byte	'IfxScu_WDTCON1_IR_divBy16384',0,0,16
	.byte	'IfxScu_WDTCON1_IR_divBy256',0,1,16
	.byte	'IfxScu_WDTCON1_IR_divBy64',0,2,0,33
	.byte	'IfxScu_WDTCON1_IR',0,27,255,10,3
	.word	39706
	.byte	10
	.byte	'_Ifx_SCU_ACCEN0_Bits',0,6,45,16,4,11
	.byte	'EN0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	488
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	488
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	488
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	488
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	488
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	488
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	488
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	488
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	488
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	488
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	488
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	488
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	488
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	488
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_ACCEN0_Bits',0,6,79,3
	.word	39828
	.byte	10
	.byte	'_Ifx_SCU_ACCEN1_Bits',0,6,82,16,4,11
	.byte	'reserved_0',0,4
	.word	465
	.byte	32,0,2,35,0,0,33
	.byte	'Ifx_SCU_ACCEN1_Bits',0,6,85,3
	.word	40385
	.byte	10
	.byte	'_Ifx_SCU_ARSTDIS_Bits',0,6,88,16,4,11
	.byte	'STM0DIS',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'STM1DIS',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'STM2DIS',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	465
	.byte	29,0,2,35,0,0,33
	.byte	'Ifx_SCU_ARSTDIS_Bits',0,6,94,3
	.word	40462
	.byte	10
	.byte	'_Ifx_SCU_CCUCON0_Bits',0,6,97,16,4,11
	.byte	'BAUD1DIV',0,1
	.word	488
	.byte	4,4,2,35,0,11
	.byte	'BAUD2DIV',0,1
	.word	488
	.byte	4,0,2,35,0,11
	.byte	'SRIDIV',0,1
	.word	488
	.byte	4,4,2,35,1,11
	.byte	'LPDIV',0,1
	.word	488
	.byte	4,0,2,35,1,11
	.byte	'SPBDIV',0,1
	.word	488
	.byte	4,4,2,35,2,11
	.byte	'FSI2DIV',0,1
	.word	488
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	488
	.byte	2,0,2,35,2,11
	.byte	'FSIDIV',0,1
	.word	488
	.byte	2,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	488
	.byte	2,4,2,35,3,11
	.byte	'CLKSEL',0,1
	.word	488
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	488
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_CCUCON0_Bits',0,6,111,3
	.word	40598
	.byte	10
	.byte	'_Ifx_SCU_CCUCON1_Bits',0,6,114,16,4,11
	.byte	'CANDIV',0,1
	.word	488
	.byte	4,4,2,35,0,11
	.byte	'ERAYDIV',0,1
	.word	488
	.byte	4,0,2,35,0,11
	.byte	'STMDIV',0,1
	.word	488
	.byte	4,4,2,35,1,11
	.byte	'GTMDIV',0,1
	.word	488
	.byte	4,0,2,35,1,11
	.byte	'ETHDIV',0,1
	.word	488
	.byte	4,4,2,35,2,11
	.byte	'ASCLINFDIV',0,1
	.word	488
	.byte	4,0,2,35,2,11
	.byte	'ASCLINSDIV',0,1
	.word	488
	.byte	4,4,2,35,3,11
	.byte	'INSEL',0,1
	.word	488
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	488
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_CCUCON1_Bits',0,6,126,3
	.word	40878
	.byte	10
	.byte	'_Ifx_SCU_CCUCON2_Bits',0,6,129,1,16,4,11
	.byte	'BBBDIV',0,1
	.word	488
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	465
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	488
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_CCUCON2_Bits',0,6,135,1,3
	.word	41116
	.byte	10
	.byte	'_Ifx_SCU_CCUCON3_Bits',0,6,138,1,16,4,11
	.byte	'PLLDIV',0,1
	.word	488
	.byte	6,2,2,35,0,11
	.byte	'PLLSEL',0,1
	.word	488
	.byte	2,0,2,35,0,11
	.byte	'PLLERAYDIV',0,1
	.word	488
	.byte	6,2,2,35,1,11
	.byte	'PLLERAYSEL',0,1
	.word	488
	.byte	2,0,2,35,1,11
	.byte	'SRIDIV',0,1
	.word	488
	.byte	6,2,2,35,2,11
	.byte	'SRISEL',0,1
	.word	488
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	488
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	488
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	488
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_CCUCON3_Bits',0,6,150,1,3
	.word	41244
	.byte	10
	.byte	'_Ifx_SCU_CCUCON4_Bits',0,6,153,1,16,4,11
	.byte	'SPBDIV',0,1
	.word	488
	.byte	6,2,2,35,0,11
	.byte	'SPBSEL',0,1
	.word	488
	.byte	2,0,2,35,0,11
	.byte	'GTMDIV',0,1
	.word	488
	.byte	6,2,2,35,1,11
	.byte	'GTMSEL',0,1
	.word	488
	.byte	2,0,2,35,1,11
	.byte	'STMDIV',0,1
	.word	488
	.byte	6,2,2,35,2,11
	.byte	'STMSEL',0,1
	.word	488
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	488
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	488
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	488
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_CCUCON4_Bits',0,6,165,1,3
	.word	41487
	.byte	10
	.byte	'_Ifx_SCU_CCUCON5_Bits',0,6,168,1,16,4,11
	.byte	'MAXDIV',0,1
	.word	488
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	465
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	488
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_CCUCON5_Bits',0,6,174,1,3
	.word	41722
	.byte	10
	.byte	'_Ifx_SCU_CCUCON6_Bits',0,6,177,1,16,4,11
	.byte	'CPU0DIV',0,1
	.word	488
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	465
	.byte	26,0,2,35,0,0,33
	.byte	'Ifx_SCU_CCUCON6_Bits',0,6,181,1,3
	.word	41850
	.byte	10
	.byte	'_Ifx_SCU_CCUCON7_Bits',0,6,184,1,16,4,11
	.byte	'CPU1DIV',0,1
	.word	488
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	465
	.byte	26,0,2,35,0,0,33
	.byte	'Ifx_SCU_CCUCON7_Bits',0,6,188,1,3
	.word	41950
	.byte	10
	.byte	'_Ifx_SCU_CHIPID_Bits',0,6,191,1,16,4,11
	.byte	'CHREV',0,1
	.word	488
	.byte	6,2,2,35,0,11
	.byte	'CHTEC',0,1
	.word	488
	.byte	2,0,2,35,0,11
	.byte	'CHID',0,1
	.word	488
	.byte	8,0,2,35,1,11
	.byte	'EEA',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'UCODE',0,1
	.word	488
	.byte	7,0,2,35,2,11
	.byte	'FSIZE',0,1
	.word	488
	.byte	4,4,2,35,3,11
	.byte	'SP',0,1
	.word	488
	.byte	2,2,2,35,3,11
	.byte	'SEC',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	488
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_CHIPID_Bits',0,6,202,1,3
	.word	42050
	.byte	10
	.byte	'_Ifx_SCU_DTSCON_Bits',0,6,205,1,16,4,11
	.byte	'PWD',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'START',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	488
	.byte	2,4,2,35,0,11
	.byte	'CAL',0,4
	.word	465
	.byte	20,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	488
	.byte	7,1,2,35,3,11
	.byte	'SLCK',0,1
	.word	488
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_DTSCON_Bits',0,6,213,1,3
	.word	42258
	.byte	10
	.byte	'_Ifx_SCU_DTSLIM_Bits',0,6,216,1,16,4,11
	.byte	'LOWER',0,2
	.word	1039
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	488
	.byte	5,1,2,35,1,11
	.byte	'LLU',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'UPPER',0,2
	.word	1039
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	488
	.byte	4,2,2,35,3,11
	.byte	'SLCK',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'UOF',0,1
	.word	488
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_DTSLIM_Bits',0,6,225,1,3
	.word	42423
	.byte	10
	.byte	'_Ifx_SCU_DTSSTAT_Bits',0,6,228,1,16,4,11
	.byte	'RESULT',0,2
	.word	1039
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	488
	.byte	4,2,2,35,1,11
	.byte	'RDY',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'BUSY',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,33
	.byte	'Ifx_SCU_DTSSTAT_Bits',0,6,235,1,3
	.word	42606
	.byte	10
	.byte	'_Ifx_SCU_EICR_Bits',0,6,238,1,16,4,11
	.byte	'reserved_0',0,1
	.word	488
	.byte	4,4,2,35,0,11
	.byte	'EXIS0',0,1
	.word	488
	.byte	3,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'FEN0',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'REN0',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'LDEN0',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'EIEN0',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'INP0',0,1
	.word	488
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	465
	.byte	5,12,2,35,0,11
	.byte	'EXIS1',0,1
	.word	488
	.byte	3,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	488
	.byte	1,0,2,35,2,11
	.byte	'FEN1',0,1
	.word	488
	.byte	1,7,2,35,3,11
	.byte	'REN1',0,1
	.word	488
	.byte	1,6,2,35,3,11
	.byte	'LDEN1',0,1
	.word	488
	.byte	1,5,2,35,3,11
	.byte	'EIEN1',0,1
	.word	488
	.byte	1,4,2,35,3,11
	.byte	'INP1',0,1
	.word	488
	.byte	3,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	488
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_EICR_Bits',0,6,129,2,3
	.word	42760
	.byte	10
	.byte	'_Ifx_SCU_EIFR_Bits',0,6,132,2,16,4,11
	.byte	'INTF0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'INTF1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'INTF2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'INTF3',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'INTF4',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'INTF5',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'INTF6',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'INTF7',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	465
	.byte	24,0,2,35,0,0,33
	.byte	'Ifx_SCU_EIFR_Bits',0,6,143,2,3
	.word	43124
	.byte	10
	.byte	'_Ifx_SCU_EMSR_Bits',0,6,146,2,16,4,11
	.byte	'POL',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'MODE',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'ENON',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'PSEL',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	1039
	.byte	12,0,2,35,0,11
	.byte	'EMSF',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'SEMSF',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	488
	.byte	6,0,2,35,2,11
	.byte	'EMSFM',0,1
	.word	488
	.byte	2,6,2,35,3,11
	.byte	'SEMSFM',0,1
	.word	488
	.byte	2,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	488
	.byte	4,0,2,35,3,0,33
	.byte	'Ifx_SCU_EMSR_Bits',0,6,159,2,3
	.word	43335
	.byte	10
	.byte	'_Ifx_SCU_ESRCFG_Bits',0,6,162,2,16,4,11
	.byte	'reserved_0',0,1
	.word	488
	.byte	7,1,2,35,0,11
	.byte	'EDCON',0,2
	.word	1039
	.byte	2,7,2,35,0,11
	.byte	'reserved_9',0,4
	.word	465
	.byte	23,0,2,35,0,0,33
	.byte	'Ifx_SCU_ESRCFG_Bits',0,6,167,2,3
	.word	43587
	.byte	10
	.byte	'_Ifx_SCU_ESROCFG_Bits',0,6,170,2,16,4,11
	.byte	'ARI',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'ARC',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	465
	.byte	30,0,2,35,0,0,33
	.byte	'Ifx_SCU_ESROCFG_Bits',0,6,175,2,3
	.word	43705
	.byte	10
	.byte	'_Ifx_SCU_EVR13CON_Bits',0,6,178,2,16,4,11
	.byte	'reserved_0',0,4
	.word	465
	.byte	28,4,2,35,0,11
	.byte	'EVR13OFF',0,1
	.word	488
	.byte	1,3,2,35,3,11
	.byte	'BPEVR13OFF',0,1
	.word	488
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	488
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_EVR13CON_Bits',0,6,185,2,3
	.word	43816
	.byte	10
	.byte	'_Ifx_SCU_EVR33CON_Bits',0,6,188,2,16,4,11
	.byte	'reserved_0',0,4
	.word	465
	.byte	28,4,2,35,0,11
	.byte	'EVR33OFF',0,1
	.word	488
	.byte	1,3,2,35,3,11
	.byte	'BPEVR33OFF',0,1
	.word	488
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	488
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_EVR33CON_Bits',0,6,195,2,3
	.word	43979
	.byte	10
	.byte	'_Ifx_SCU_EVRADCSTAT_Bits',0,6,198,2,16,4,11
	.byte	'ADC13V',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'ADC33V',0,1
	.word	488
	.byte	8,0,2,35,1,11
	.byte	'ADCSWDV',0,1
	.word	488
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	488
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	488
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_EVRADCSTAT_Bits',0,6,205,2,3
	.word	44142
	.byte	10
	.byte	'_Ifx_SCU_EVRDVSTAT_Bits',0,6,208,2,16,4,11
	.byte	'DVS13TRIM',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	488
	.byte	8,0,2,35,1,11
	.byte	'DVS33TRIM',0,1
	.word	488
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	488
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	488
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_EVRDVSTAT_Bits',0,6,215,2,3
	.word	44300
	.byte	10
	.byte	'_Ifx_SCU_EVRMONCTRL_Bits',0,6,218,2,16,4,11
	.byte	'EVR13OVMOD',0,1
	.word	488
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	488
	.byte	2,4,2,35,0,11
	.byte	'EVR13UVMOD',0,1
	.word	488
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	488
	.byte	2,0,2,35,0,11
	.byte	'EVR33OVMOD',0,1
	.word	488
	.byte	2,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	488
	.byte	2,4,2,35,1,11
	.byte	'EVR33UVMOD',0,1
	.word	488
	.byte	2,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	488
	.byte	2,0,2,35,1,11
	.byte	'SWDOVMOD',0,1
	.word	488
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	488
	.byte	2,4,2,35,2,11
	.byte	'SWDUVMOD',0,1
	.word	488
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,2
	.word	1039
	.byte	10,0,2,35,2,0,33
	.byte	'Ifx_SCU_EVRMONCTRL_Bits',0,6,232,2,3
	.word	44465
	.byte	10
	.byte	'_Ifx_SCU_EVROSCCTRL_Bits',0,6,235,2,16,4,11
	.byte	'OSCTRIM',0,2
	.word	1039
	.byte	10,6,2,35,0,11
	.byte	'OSCPTAT',0,1
	.word	488
	.byte	6,0,2,35,1,11
	.byte	'OSCANASEL',0,1
	.word	488
	.byte	4,4,2,35,2,11
	.byte	'HPBGTRIM',0,2
	.word	1039
	.byte	7,5,2,35,2,11
	.byte	'HPBGCLKEN',0,1
	.word	488
	.byte	1,4,2,35,3,11
	.byte	'OSC3V3',0,1
	.word	488
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	488
	.byte	2,1,2,35,3,11
	.byte	'LCK',0,1
	.word	488
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_EVROSCCTRL_Bits',0,6,245,2,3
	.word	44794
	.byte	10
	.byte	'_Ifx_SCU_EVROVMON_Bits',0,6,248,2,16,4,11
	.byte	'EVR13OVVAL',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'EVR33OVVAL',0,1
	.word	488
	.byte	8,0,2,35,1,11
	.byte	'SWDOVVAL',0,1
	.word	488
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	488
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	488
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_EVROVMON_Bits',0,6,255,2,3
	.word	45015
	.byte	10
	.byte	'_Ifx_SCU_EVRRSTCON_Bits',0,6,130,3,16,4,11
	.byte	'RST13TRIM',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	465
	.byte	16,8,2,35,0,11
	.byte	'RST13OFF',0,1
	.word	488
	.byte	1,7,2,35,3,11
	.byte	'BPRST13OFF',0,1
	.word	488
	.byte	1,6,2,35,3,11
	.byte	'RST33OFF',0,1
	.word	488
	.byte	1,5,2,35,3,11
	.byte	'BPRST33OFF',0,1
	.word	488
	.byte	1,4,2,35,3,11
	.byte	'RSTSWDOFF',0,1
	.word	488
	.byte	1,3,2,35,3,11
	.byte	'BPRSTSWDOFF',0,1
	.word	488
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	488
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_EVRRSTCON_Bits',0,6,142,3,3
	.word	45178
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF1_Bits',0,6,145,3,16,4,11
	.byte	'SD5P',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'SD5I',0,1
	.word	488
	.byte	8,0,2,35,1,11
	.byte	'SD5D',0,1
	.word	488
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	488
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	488
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_EVRSDCOEFF1_Bits',0,6,152,3,3
	.word	45450
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF2_Bits',0,6,155,3,16,4,11
	.byte	'SD33P',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'SD33I',0,1
	.word	488
	.byte	8,0,2,35,1,11
	.byte	'SD33D',0,1
	.word	488
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	488
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	488
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_EVRSDCOEFF2_Bits',0,6,162,3,3
	.word	45603
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF3_Bits',0,6,165,3,16,4,11
	.byte	'CT5REG0',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'CT5REG1',0,1
	.word	488
	.byte	8,0,2,35,1,11
	.byte	'CT5REG2',0,1
	.word	488
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	488
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	488
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_EVRSDCOEFF3_Bits',0,6,172,3,3
	.word	45759
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF4_Bits',0,6,175,3,16,4,11
	.byte	'CT5REG3',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'CT5REG4',0,1
	.word	488
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	488
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_EVRSDCOEFF4_Bits',0,6,181,3,3
	.word	45921
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF5_Bits',0,6,184,3,16,4,11
	.byte	'CT33REG0',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'CT33REG1',0,1
	.word	488
	.byte	8,0,2,35,1,11
	.byte	'CT33REG2',0,1
	.word	488
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	488
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	488
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_EVRSDCOEFF5_Bits',0,6,191,3,3
	.word	46064
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF6_Bits',0,6,194,3,16,4,11
	.byte	'CT33REG3',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'CT33REG4',0,1
	.word	488
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	488
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_EVRSDCOEFF6_Bits',0,6,200,3,3
	.word	46229
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL1_Bits',0,6,203,3,16,4,11
	.byte	'SDFREQSPRD',0,2
	.word	1039
	.byte	16,0,2,35,0,11
	.byte	'SDFREQ',0,1
	.word	488
	.byte	8,0,2,35,2,11
	.byte	'SDSTEP',0,1
	.word	488
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	488
	.byte	2,2,2,35,3,11
	.byte	'SDSAMPLE',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	488
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_EVRSDCTRL1_Bits',0,6,211,3,3
	.word	46374
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL2_Bits',0,6,214,3,16,4,11
	.byte	'DRVP',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'SDMINMAXDC',0,1
	.word	488
	.byte	8,0,2,35,1,11
	.byte	'DRVN',0,1
	.word	488
	.byte	8,0,2,35,2,11
	.byte	'SDLUT',0,1
	.word	488
	.byte	6,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	488
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_EVRSDCTRL2_Bits',0,6,222,3,3
	.word	46555
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL3_Bits',0,6,225,3,16,4,11
	.byte	'SDPWMPRE',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'SDPID',0,1
	.word	488
	.byte	8,0,2,35,1,11
	.byte	'SDVOKLVL',0,1
	.word	488
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	488
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	488
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_EVRSDCTRL3_Bits',0,6,232,3,3
	.word	46729
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL4_Bits',0,6,235,3,16,4,11
	.byte	'reserved_0',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'SYNCDIV',0,1
	.word	488
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	465
	.byte	20,1,2,35,0,11
	.byte	'LCK',0,1
	.word	488
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_EVRSDCTRL4_Bits',0,6,241,3,3
	.word	46889
	.byte	10
	.byte	'_Ifx_SCU_EVRSTAT_Bits',0,6,244,3,16,4,11
	.byte	'EVR13',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'OV13',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'EVR33',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'OV33',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'OVSWD',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'UV13',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'UV33',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'UVSWD',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'EXTPASS13',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'EXTPASS33',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'BGPROK',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	465
	.byte	21,0,2,35,0,0,33
	.byte	'Ifx_SCU_EVRSTAT_Bits',0,6,130,4,3
	.word	47033
	.byte	10
	.byte	'_Ifx_SCU_EVRTRIM_Bits',0,6,133,4,16,4,11
	.byte	'EVR13TRIM',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'SDVOUTSEL',0,1
	.word	488
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	488
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_EVRTRIM_Bits',0,6,139,4,3
	.word	47307
	.byte	10
	.byte	'_Ifx_SCU_EVRUVMON_Bits',0,6,142,4,16,4,11
	.byte	'EVR13UVVAL',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'EVR33UVVAL',0,1
	.word	488
	.byte	8,0,2,35,1,11
	.byte	'SWDUVVAL',0,1
	.word	488
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	488
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	488
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_EVRUVMON_Bits',0,6,149,4,3
	.word	47446
	.byte	10
	.byte	'_Ifx_SCU_EXTCON_Bits',0,6,152,4,16,4,11
	.byte	'EN0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'SEL0',0,1
	.word	488
	.byte	4,2,2,35,0,11
	.byte	'reserved_6',0,2
	.word	1039
	.byte	10,0,2,35,0,11
	.byte	'EN1',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'NSEL',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'SEL1',0,1
	.word	488
	.byte	4,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	488
	.byte	2,0,2,35,2,11
	.byte	'DIV1',0,1
	.word	488
	.byte	8,0,2,35,3,0,33
	.byte	'Ifx_SCU_EXTCON_Bits',0,6,163,4,3
	.word	47609
	.byte	10
	.byte	'_Ifx_SCU_FDR_Bits',0,6,166,4,16,4,11
	.byte	'STEP',0,2
	.word	1039
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	488
	.byte	4,2,2,35,1,11
	.byte	'DM',0,1
	.word	488
	.byte	2,0,2,35,1,11
	.byte	'RESULT',0,2
	.word	1039
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	488
	.byte	5,1,2,35,3,11
	.byte	'DISCLK',0,1
	.word	488
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_FDR_Bits',0,6,174,4,3
	.word	47827
	.byte	10
	.byte	'_Ifx_SCU_FMR_Bits',0,6,177,4,16,4,11
	.byte	'FS0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'FS1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'FS2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'FS3',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'FS4',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'FS5',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'FS6',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'FS7',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	488
	.byte	8,0,2,35,1,11
	.byte	'FC0',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'FC1',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'FC2',0,1
	.word	488
	.byte	1,5,2,35,2,11
	.byte	'FC3',0,1
	.word	488
	.byte	1,4,2,35,2,11
	.byte	'FC4',0,1
	.word	488
	.byte	1,3,2,35,2,11
	.byte	'FC5',0,1
	.word	488
	.byte	1,2,2,35,2,11
	.byte	'FC6',0,1
	.word	488
	.byte	1,1,2,35,2,11
	.byte	'FC7',0,1
	.word	488
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	488
	.byte	8,0,2,35,3,0,33
	.byte	'Ifx_SCU_FMR_Bits',0,6,197,4,3
	.word	47990
	.byte	10
	.byte	'_Ifx_SCU_ID_Bits',0,6,200,4,16,4,11
	.byte	'MODREV',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	488
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	1039
	.byte	16,0,2,35,2,0,33
	.byte	'Ifx_SCU_ID_Bits',0,6,205,4,3
	.word	48326
	.byte	10
	.byte	'_Ifx_SCU_IGCR_Bits',0,6,208,4,16,4,11
	.byte	'IPEN00',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'IPEN01',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'IPEN02',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'IPEN03',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'IPEN04',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'IPEN05',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'IPEN06',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'IPEN07',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	488
	.byte	5,3,2,35,1,11
	.byte	'GEEN0',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'IGP0',0,1
	.word	488
	.byte	2,0,2,35,1,11
	.byte	'IPEN10',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'IPEN11',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'IPEN12',0,1
	.word	488
	.byte	1,5,2,35,2,11
	.byte	'IPEN13',0,1
	.word	488
	.byte	1,4,2,35,2,11
	.byte	'IPEN14',0,1
	.word	488
	.byte	1,3,2,35,2,11
	.byte	'IPEN15',0,1
	.word	488
	.byte	1,2,2,35,2,11
	.byte	'IPEN16',0,1
	.word	488
	.byte	1,1,2,35,2,11
	.byte	'IPEN17',0,1
	.word	488
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	488
	.byte	5,3,2,35,3,11
	.byte	'GEEN1',0,1
	.word	488
	.byte	1,2,2,35,3,11
	.byte	'IGP1',0,1
	.word	488
	.byte	2,0,2,35,3,0,33
	.byte	'Ifx_SCU_IGCR_Bits',0,6,232,4,3
	.word	48433
	.byte	10
	.byte	'_Ifx_SCU_IN_Bits',0,6,235,4,16,4,11
	.byte	'P0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	465
	.byte	30,0,2,35,0,0,33
	.byte	'Ifx_SCU_IN_Bits',0,6,240,4,3
	.word	48885
	.byte	10
	.byte	'_Ifx_SCU_IOCR_Bits',0,6,243,4,16,4,11
	.byte	'reserved_0',0,1
	.word	488
	.byte	4,4,2,35,0,11
	.byte	'PC0',0,1
	.word	488
	.byte	4,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	488
	.byte	4,4,2,35,1,11
	.byte	'PC1',0,1
	.word	488
	.byte	4,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,33
	.byte	'Ifx_SCU_IOCR_Bits',0,6,250,4,3
	.word	48984
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL0_Bits',0,6,253,4,16,4,11
	.byte	'LBISTREQ',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'LBISTREQP',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'PATTERNS',0,2
	.word	1039
	.byte	14,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,33
	.byte	'Ifx_SCU_LBISTCTRL0_Bits',0,6,131,5,3
	.word	49134
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL1_Bits',0,6,134,5,16,4,11
	.byte	'SEED',0,4
	.word	465
	.byte	23,9,2,35,0,11
	.byte	'reserved_23',0,1
	.word	488
	.byte	1,0,2,35,2,11
	.byte	'SPLITSH',0,1
	.word	488
	.byte	3,5,2,35,3,11
	.byte	'BODY',0,1
	.word	488
	.byte	1,4,2,35,3,11
	.byte	'LBISTFREQU',0,1
	.word	488
	.byte	4,0,2,35,3,0,33
	.byte	'Ifx_SCU_LBISTCTRL1_Bits',0,6,141,5,3
	.word	49283
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL2_Bits',0,6,144,5,16,4,11
	.byte	'SIGNATURE',0,4
	.word	465
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	488
	.byte	7,1,2,35,3,11
	.byte	'LBISTDONE',0,1
	.word	488
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_LBISTCTRL2_Bits',0,6,149,5,3
	.word	49444
	.byte	10
	.byte	'_Ifx_SCU_LCLCON_Bits',0,6,152,5,16,4,11
	.byte	'reserved_0',0,2
	.word	1039
	.byte	16,0,2,35,0,11
	.byte	'LS',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,2
	.word	1039
	.byte	14,1,2,35,2,11
	.byte	'LSEN',0,1
	.word	488
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_LCLCON_Bits',0,6,158,5,3
	.word	49574
	.byte	10
	.byte	'_Ifx_SCU_LCLTEST_Bits',0,6,161,5,16,4,11
	.byte	'LCLT0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'LCLT1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	465
	.byte	30,0,2,35,0,0,33
	.byte	'Ifx_SCU_LCLTEST_Bits',0,6,166,5,3
	.word	49706
	.byte	10
	.byte	'_Ifx_SCU_MANID_Bits',0,6,169,5,16,4,11
	.byte	'DEPT',0,1
	.word	488
	.byte	5,3,2,35,0,11
	.byte	'MANUF',0,2
	.word	1039
	.byte	11,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,33
	.byte	'Ifx_SCU_MANID_Bits',0,6,174,5,3
	.word	49821
	.byte	10
	.byte	'_Ifx_SCU_OMR_Bits',0,6,177,5,16,4,11
	.byte	'PS0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	1039
	.byte	14,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	1039
	.byte	14,0,2,35,2,0,33
	.byte	'Ifx_SCU_OMR_Bits',0,6,185,5,3
	.word	49932
	.byte	10
	.byte	'_Ifx_SCU_OSCCON_Bits',0,6,188,5,16,4,11
	.byte	'reserved_0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'PLLLV',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'OSCRES',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'GAINSEL',0,1
	.word	488
	.byte	2,3,2,35,0,11
	.byte	'MODE',0,1
	.word	488
	.byte	2,1,2,35,0,11
	.byte	'SHBY',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'PLLHV',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'X1D',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'X1DEN',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	488
	.byte	4,0,2,35,1,11
	.byte	'OSCVAL',0,1
	.word	488
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	488
	.byte	2,1,2,35,2,11
	.byte	'APREN',0,1
	.word	488
	.byte	1,0,2,35,2,11
	.byte	'CAP0EN',0,1
	.word	488
	.byte	1,7,2,35,3,11
	.byte	'CAP1EN',0,1
	.word	488
	.byte	1,6,2,35,3,11
	.byte	'CAP2EN',0,1
	.word	488
	.byte	1,5,2,35,3,11
	.byte	'CAP3EN',0,1
	.word	488
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	488
	.byte	4,0,2,35,3,0,33
	.byte	'Ifx_SCU_OSCCON_Bits',0,6,209,5,3
	.word	50090
	.byte	10
	.byte	'_Ifx_SCU_OUT_Bits',0,6,212,5,16,4,11
	.byte	'P0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	465
	.byte	30,0,2,35,0,0,33
	.byte	'Ifx_SCU_OUT_Bits',0,6,217,5,3
	.word	50502
	.byte	10
	.byte	'_Ifx_SCU_OVCCON_Bits',0,6,220,5,16,4,11
	.byte	'CSEL0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'CSEL1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'CSEL2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,2
	.word	1039
	.byte	13,0,2,35,0,11
	.byte	'OVSTRT',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'OVSTP',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'DCINVAL',0,1
	.word	488
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	488
	.byte	5,0,2,35,2,11
	.byte	'OVCONF',0,1
	.word	488
	.byte	1,7,2,35,3,11
	.byte	'POVCONF',0,1
	.word	488
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	488
	.byte	6,0,2,35,3,0,33
	.byte	'Ifx_SCU_OVCCON_Bits',0,6,233,5,3
	.word	50603
	.byte	10
	.byte	'_Ifx_SCU_OVCENABLE_Bits',0,6,236,5,16,4,11
	.byte	'OVEN0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'OVEN1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'OVEN2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	465
	.byte	29,0,2,35,0,0,33
	.byte	'Ifx_SCU_OVCENABLE_Bits',0,6,242,5,3
	.word	50870
	.byte	10
	.byte	'_Ifx_SCU_PDISC_Bits',0,6,245,5,16,4,11
	.byte	'PDIS0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	465
	.byte	30,0,2,35,0,0,33
	.byte	'Ifx_SCU_PDISC_Bits',0,6,250,5,3
	.word	51006
	.byte	10
	.byte	'_Ifx_SCU_PDR_Bits',0,6,253,5,16,4,11
	.byte	'PD0',0,1
	.word	488
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	488
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	465
	.byte	24,0,2,35,0,0,33
	.byte	'Ifx_SCU_PDR_Bits',0,6,132,6,3
	.word	51117
	.byte	10
	.byte	'_Ifx_SCU_PDRR_Bits',0,6,135,6,16,4,11
	.byte	'PDR0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'PDR1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'PDR2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'PDR3',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'PDR4',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'PDR5',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'PDR6',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'PDR7',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	465
	.byte	24,0,2,35,0,0,33
	.byte	'Ifx_SCU_PDRR_Bits',0,6,146,6,3
	.word	51250
	.byte	10
	.byte	'_Ifx_SCU_PLLCON0_Bits',0,6,149,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'MODEN',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	1039
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	488
	.byte	7,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	488
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	488
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	488
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	488
	.byte	4,0,2,35,3,0,33
	.byte	'Ifx_SCU_PLLCON0_Bits',0,6,166,6,3
	.word	51453
	.byte	10
	.byte	'_Ifx_SCU_PLLCON1_Bits',0,6,169,6,16,4,11
	.byte	'K2DIV',0,1
	.word	488
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	488
	.byte	7,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	488
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	1039
	.byte	9,0,2,35,2,0,33
	.byte	'Ifx_SCU_PLLCON1_Bits',0,6,177,6,3
	.word	51809
	.byte	10
	.byte	'_Ifx_SCU_PLLCON2_Bits',0,6,180,6,16,4,11
	.byte	'MODCFG',0,2
	.word	1039
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,33
	.byte	'Ifx_SCU_PLLCON2_Bits',0,6,184,6,3
	.word	51987
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON0_Bits',0,6,187,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	488
	.byte	2,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	1039
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	488
	.byte	5,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	488
	.byte	2,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	488
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	488
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	488
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	488
	.byte	4,0,2,35,3,0,33
	.byte	'Ifx_SCU_PLLERAYCON0_Bits',0,6,204,6,3
	.word	52087
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON1_Bits',0,6,207,6,16,4,11
	.byte	'K2DIV',0,1
	.word	488
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	488
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	488
	.byte	4,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	488
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	1039
	.byte	9,0,2,35,2,0,33
	.byte	'Ifx_SCU_PLLERAYCON1_Bits',0,6,215,6,3
	.word	52457
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYSTAT_Bits',0,6,218,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'PWDSTAT',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	465
	.byte	26,0,2,35,0,0,33
	.byte	'Ifx_SCU_PLLERAYSTAT_Bits',0,6,227,6,3
	.word	52643
	.byte	10
	.byte	'_Ifx_SCU_PLLSTAT_Bits',0,6,230,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'MODRUN',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	465
	.byte	24,0,2,35,0,0,33
	.byte	'Ifx_SCU_PLLSTAT_Bits',0,6,241,6,3
	.word	52841
	.byte	10
	.byte	'_Ifx_SCU_PMCSR_Bits',0,6,244,6,16,4,11
	.byte	'REQSLP',0,1
	.word	488
	.byte	2,6,2,35,0,11
	.byte	'SMUSLP',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	488
	.byte	5,0,2,35,0,11
	.byte	'PMST',0,1
	.word	488
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	465
	.byte	21,0,2,35,0,0,33
	.byte	'Ifx_SCU_PMCSR_Bits',0,6,251,6,3
	.word	53074
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR0_Bits',0,6,254,6,16,4,11
	.byte	'reserved_0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'ESR1WKEN',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'PINAWKEN',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'PINBWKEN',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'ESR0DFEN',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'ESR0EDCON',0,1
	.word	488
	.byte	2,1,2,35,0,11
	.byte	'ESR1DFEN',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'ESR1EDCON',0,1
	.word	488
	.byte	2,6,2,35,1,11
	.byte	'PINADFEN',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'PINAEDCON',0,1
	.word	488
	.byte	2,3,2,35,1,11
	.byte	'PINBDFEN',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'PINBEDCON',0,1
	.word	488
	.byte	2,0,2,35,1,11
	.byte	'SCREN',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'STBYRAMSEL',0,1
	.word	488
	.byte	2,5,2,35,2,11
	.byte	'SCRCLKSEL',0,1
	.word	488
	.byte	1,4,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	488
	.byte	1,3,2,35,2,11
	.byte	'TRISTEN',0,1
	.word	488
	.byte	1,2,2,35,2,11
	.byte	'TRISTREQ',0,1
	.word	488
	.byte	1,1,2,35,2,11
	.byte	'PORSTDF',0,1
	.word	488
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	488
	.byte	1,7,2,35,3,11
	.byte	'DCDCSYNC',0,1
	.word	488
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	488
	.byte	3,3,2,35,3,11
	.byte	'ESR0TRIST',0,1
	.word	488
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	488
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_PMSWCR0_Bits',0,6,153,7,3
	.word	53226
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR1_Bits',0,6,156,7,16,4,11
	.byte	'SCRSTEN',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'SCRSTREQ',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	488
	.byte	6,0,2,35,0,11
	.byte	'CPUIDLSEL',0,1
	.word	488
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'IRADIS',0,1
	.word	488
	.byte	1,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	488
	.byte	3,0,2,35,1,11
	.byte	'SCRCFG',0,1
	.word	488
	.byte	8,0,2,35,2,11
	.byte	'CPUSEL',0,1
	.word	488
	.byte	3,5,2,35,3,11
	.byte	'STBYEVEN',0,1
	.word	488
	.byte	1,4,2,35,3,11
	.byte	'STBYEV',0,1
	.word	488
	.byte	3,1,2,35,3,11
	.byte	'LCK',0,1
	.word	488
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_PMSWCR1_Bits',0,6,170,7,3
	.word	53793
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR2_Bits',0,6,173,7,16,4,11
	.byte	'SCRINT',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'BUSY',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'SCRECC',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'SCRWDT',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'SCRRST',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	488
	.byte	4,0,2,35,1,11
	.byte	'TCINT',0,1
	.word	488
	.byte	8,0,2,35,2,11
	.byte	'TCINTREQ',0,1
	.word	488
	.byte	1,7,2,35,3,11
	.byte	'SMURST',0,1
	.word	488
	.byte	1,6,2,35,3,11
	.byte	'RST',0,1
	.word	488
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	488
	.byte	4,1,2,35,3,11
	.byte	'LCK',0,1
	.word	488
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_PMSWCR2_Bits',0,6,187,7,3
	.word	54087
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTAT_Bits',0,6,190,7,16,4,11
	.byte	'reserved_0',0,1
	.word	488
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKP',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUN',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'PINAWKP',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUN',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'PINBWKP',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUN',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'PORSTDF',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'HWCFGEVR',0,1
	.word	488
	.byte	3,3,2,35,1,11
	.byte	'STBYRAM',0,1
	.word	488
	.byte	2,1,2,35,1,11
	.byte	'TRIST',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'SCRST',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'SCRWKP',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'SCR',0,1
	.word	488
	.byte	1,5,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	488
	.byte	1,4,2,35,2,11
	.byte	'ESR1WKEN',0,1
	.word	488
	.byte	1,3,2,35,2,11
	.byte	'PINAWKEN',0,1
	.word	488
	.byte	1,2,2,35,2,11
	.byte	'PINBWKEN',0,1
	.word	488
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	1039
	.byte	4,5,2,35,2,11
	.byte	'ESR0TRIST',0,1
	.word	488
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	488
	.byte	4,0,2,35,3,0,33
	.byte	'Ifx_SCU_PMSWSTAT_Bits',0,6,214,7,3
	.word	54365
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTATCLR_Bits',0,6,217,7,16,4,11
	.byte	'reserved_0',0,1
	.word	488
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKPCLR',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUNCLR',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'PINAWKPCLR',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUNCLR',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'PINBWKPCLR',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUNCLR',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	488
	.byte	8,0,2,35,1,11
	.byte	'SCRSTCLR',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'SCRWKPCLR',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	1039
	.byte	14,0,2,35,2,0,33
	.byte	'Ifx_SCU_PMSWSTATCLR_Bits',0,6,230,7,3
	.word	54861
	.byte	10
	.byte	'_Ifx_SCU_RSTCON2_Bits',0,6,233,7,16,4,11
	.byte	'reserved_0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'CLRC',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	1039
	.byte	10,4,2,35,0,11
	.byte	'CSS0',0,1
	.word	488
	.byte	1,3,2,35,1,11
	.byte	'CSS1',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'CSS2',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'USRINFO',0,2
	.word	1039
	.byte	16,0,2,35,2,0,33
	.byte	'Ifx_SCU_RSTCON2_Bits',0,6,243,7,3
	.word	55174
	.byte	10
	.byte	'_Ifx_SCU_RSTCON_Bits',0,6,246,7,16,4,11
	.byte	'ESR0',0,1
	.word	488
	.byte	2,6,2,35,0,11
	.byte	'ESR1',0,1
	.word	488
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	488
	.byte	2,2,2,35,0,11
	.byte	'SMU',0,1
	.word	488
	.byte	2,0,2,35,0,11
	.byte	'SW',0,1
	.word	488
	.byte	2,6,2,35,1,11
	.byte	'STM0',0,1
	.word	488
	.byte	2,4,2,35,1,11
	.byte	'STM1',0,1
	.word	488
	.byte	2,2,2,35,1,11
	.byte	'STM2',0,1
	.word	488
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,33
	.byte	'Ifx_SCU_RSTCON_Bits',0,6,129,8,3
	.word	55383
	.byte	10
	.byte	'_Ifx_SCU_RSTSTAT_Bits',0,6,132,8,16,4,11
	.byte	'ESR0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'ESR1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'SMU',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'SW',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'STM0',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'STM1',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'STM2',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	488
	.byte	8,0,2,35,1,11
	.byte	'PORST',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'CB0',0,1
	.word	488
	.byte	1,5,2,35,2,11
	.byte	'CB1',0,1
	.word	488
	.byte	1,4,2,35,2,11
	.byte	'CB3',0,1
	.word	488
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	488
	.byte	2,1,2,35,2,11
	.byte	'EVR13',0,1
	.word	488
	.byte	1,0,2,35,2,11
	.byte	'EVR33',0,1
	.word	488
	.byte	1,7,2,35,3,11
	.byte	'SWD',0,1
	.word	488
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	488
	.byte	2,4,2,35,3,11
	.byte	'STBYR',0,1
	.word	488
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	488
	.byte	3,0,2,35,3,0,33
	.byte	'Ifx_SCU_RSTSTAT_Bits',0,6,155,8,3
	.word	55594
	.byte	10
	.byte	'_Ifx_SCU_SAFECON_Bits',0,6,158,8,16,4,11
	.byte	'HBT',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	465
	.byte	31,0,2,35,0,0,33
	.byte	'Ifx_SCU_SAFECON_Bits',0,6,162,8,3
	.word	56026
	.byte	10
	.byte	'_Ifx_SCU_STSTAT_Bits',0,6,165,8,16,4,11
	.byte	'HWCFG',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'FTM',0,1
	.word	488
	.byte	7,1,2,35,1,11
	.byte	'MODE',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'FCBAE',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'LUDIS',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	488
	.byte	1,5,2,35,2,11
	.byte	'TRSTL',0,1
	.word	488
	.byte	1,4,2,35,2,11
	.byte	'SPDEN',0,1
	.word	488
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	488
	.byte	3,0,2,35,2,11
	.byte	'RAMINT',0,1
	.word	488
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	488
	.byte	7,0,2,35,3,0,33
	.byte	'Ifx_SCU_STSTAT_Bits',0,6,178,8,3
	.word	56122
	.byte	10
	.byte	'_Ifx_SCU_SWRSTCON_Bits',0,6,181,8,16,4,11
	.byte	'reserved_0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'SWRSTREQ',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	465
	.byte	30,0,2,35,0,0,33
	.byte	'Ifx_SCU_SWRSTCON_Bits',0,6,186,8,3
	.word	56382
	.byte	10
	.byte	'_Ifx_SCU_SYSCON_Bits',0,6,189,8,16,4,11
	.byte	'CCTRIG0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'RAMINTM',0,1
	.word	488
	.byte	2,4,2,35,0,11
	.byte	'SETLUDIS',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	488
	.byte	3,0,2,35,0,11
	.byte	'DATM',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,4
	.word	465
	.byte	23,0,2,35,0,0,33
	.byte	'Ifx_SCU_SYSCON_Bits',0,6,198,8,3
	.word	56507
	.byte	10
	.byte	'_Ifx_SCU_TRAPCLR_Bits',0,6,201,8,16,4,11
	.byte	'ESR0T',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	465
	.byte	28,0,2,35,0,0,33
	.byte	'Ifx_SCU_TRAPCLR_Bits',0,6,208,8,3
	.word	56704
	.byte	10
	.byte	'_Ifx_SCU_TRAPDIS_Bits',0,6,211,8,16,4,11
	.byte	'ESR0T',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	465
	.byte	28,0,2,35,0,0,33
	.byte	'Ifx_SCU_TRAPDIS_Bits',0,6,218,8,3
	.word	56857
	.byte	10
	.byte	'_Ifx_SCU_TRAPSET_Bits',0,6,221,8,16,4,11
	.byte	'ESR0T',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	465
	.byte	28,0,2,35,0,0,33
	.byte	'Ifx_SCU_TRAPSET_Bits',0,6,228,8,3
	.word	57010
	.byte	10
	.byte	'_Ifx_SCU_TRAPSTAT_Bits',0,6,231,8,16,4,11
	.byte	'ESR0T',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	465
	.byte	28,0,2,35,0,0,33
	.byte	'Ifx_SCU_TRAPSTAT_Bits',0,6,238,8,3
	.word	57163
	.byte	33
	.byte	'Ifx_SCU_WDTCPU_CON0_Bits',0,6,247,8,3
	.word	903
	.byte	33
	.byte	'Ifx_SCU_WDTCPU_CON1_Bits',0,6,134,9,3
	.word	1061
	.byte	33
	.byte	'Ifx_SCU_WDTCPU_SR_Bits',0,6,150,9,3
	.word	1305
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON0_Bits',0,6,153,9,16,4,11
	.byte	'ENDINIT',0,4
	.word	887
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	887
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	887
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	887
	.byte	16,0,2,35,0,0,33
	.byte	'Ifx_SCU_WDTS_CON0_Bits',0,6,159,9,3
	.word	57418
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON1_Bits',0,6,162,9,16,4,11
	.byte	'CLRIRF',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'IR0',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	488
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,33
	.byte	'Ifx_SCU_WDTS_CON1_Bits',0,6,175,9,3
	.word	57544
	.byte	10
	.byte	'_Ifx_SCU_WDTS_SR_Bits',0,6,178,9,16,4,11
	.byte	'AE',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	488
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	1039
	.byte	16,0,2,35,2,0,33
	.byte	'Ifx_SCU_WDTS_SR_Bits',0,6,191,9,3
	.word	57796
	.byte	12,6,199,9,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39828
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_ACCEN0',0,6,204,9,3
	.word	58015
	.byte	12,6,207,9,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40385
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_ACCEN1',0,6,212,9,3
	.word	58079
	.byte	12,6,215,9,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40462
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_ARSTDIS',0,6,220,9,3
	.word	58143
	.byte	12,6,223,9,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40598
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_CCUCON0',0,6,228,9,3
	.word	58208
	.byte	12,6,231,9,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40878
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_CCUCON1',0,6,236,9,3
	.word	58273
	.byte	12,6,239,9,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41116
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_CCUCON2',0,6,244,9,3
	.word	58338
	.byte	12,6,247,9,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41244
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_CCUCON3',0,6,252,9,3
	.word	58403
	.byte	12,6,255,9,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41487
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_CCUCON4',0,6,132,10,3
	.word	58468
	.byte	12,6,135,10,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41722
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_CCUCON5',0,6,140,10,3
	.word	58533
	.byte	12,6,143,10,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41850
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_CCUCON6',0,6,148,10,3
	.word	58598
	.byte	12,6,151,10,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41950
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_CCUCON7',0,6,156,10,3
	.word	58663
	.byte	12,6,159,10,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42050
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_CHIPID',0,6,164,10,3
	.word	58728
	.byte	12,6,167,10,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42258
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_DTSCON',0,6,172,10,3
	.word	58792
	.byte	12,6,175,10,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42423
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_DTSLIM',0,6,180,10,3
	.word	58856
	.byte	12,6,183,10,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42606
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_DTSSTAT',0,6,188,10,3
	.word	58920
	.byte	12,6,191,10,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42760
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_EICR',0,6,196,10,3
	.word	58985
	.byte	12,6,199,10,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43124
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_EIFR',0,6,204,10,3
	.word	59047
	.byte	12,6,207,10,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43335
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_EMSR',0,6,212,10,3
	.word	59109
	.byte	12,6,215,10,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43587
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_ESRCFG',0,6,220,10,3
	.word	59171
	.byte	12,6,223,10,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43705
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_ESROCFG',0,6,228,10,3
	.word	59235
	.byte	12,6,231,10,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43816
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_EVR13CON',0,6,236,10,3
	.word	59300
	.byte	12,6,239,10,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43979
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_EVR33CON',0,6,244,10,3
	.word	59366
	.byte	12,6,247,10,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44142
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_EVRADCSTAT',0,6,252,10,3
	.word	59432
	.byte	12,6,255,10,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44300
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_EVRDVSTAT',0,6,132,11,3
	.word	59500
	.byte	12,6,135,11,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44465
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_EVRMONCTRL',0,6,140,11,3
	.word	59567
	.byte	12,6,143,11,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44794
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_EVROSCCTRL',0,6,148,11,3
	.word	59635
	.byte	12,6,151,11,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45015
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_EVROVMON',0,6,156,11,3
	.word	59703
	.byte	12,6,159,11,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45178
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_EVRRSTCON',0,6,164,11,3
	.word	59769
	.byte	12,6,167,11,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45450
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_EVRSDCOEFF1',0,6,172,11,3
	.word	59836
	.byte	12,6,175,11,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45603
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_EVRSDCOEFF2',0,6,180,11,3
	.word	59905
	.byte	12,6,183,11,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45759
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_EVRSDCOEFF3',0,6,188,11,3
	.word	59974
	.byte	12,6,191,11,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45921
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_EVRSDCOEFF4',0,6,196,11,3
	.word	60043
	.byte	12,6,199,11,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46064
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_EVRSDCOEFF5',0,6,204,11,3
	.word	60112
	.byte	12,6,207,11,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46229
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_EVRSDCOEFF6',0,6,212,11,3
	.word	60181
	.byte	12,6,215,11,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46374
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_EVRSDCTRL1',0,6,220,11,3
	.word	60250
	.byte	12,6,223,11,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46555
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_EVRSDCTRL2',0,6,228,11,3
	.word	60318
	.byte	12,6,231,11,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46729
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_EVRSDCTRL3',0,6,236,11,3
	.word	60386
	.byte	12,6,239,11,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46889
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_EVRSDCTRL4',0,6,244,11,3
	.word	60454
	.byte	12,6,247,11,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47033
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_EVRSTAT',0,6,252,11,3
	.word	60522
	.byte	12,6,255,11,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47307
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_EVRTRIM',0,6,132,12,3
	.word	60587
	.byte	12,6,135,12,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47446
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_EVRUVMON',0,6,140,12,3
	.word	60652
	.byte	12,6,143,12,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47609
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_EXTCON',0,6,148,12,3
	.word	60718
	.byte	12,6,151,12,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47827
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_FDR',0,6,156,12,3
	.word	60782
	.byte	12,6,159,12,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47990
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_FMR',0,6,164,12,3
	.word	60843
	.byte	12,6,167,12,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48326
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_ID',0,6,172,12,3
	.word	60904
	.byte	12,6,175,12,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48433
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_IGCR',0,6,180,12,3
	.word	60964
	.byte	12,6,183,12,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48885
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_IN',0,6,188,12,3
	.word	61026
	.byte	12,6,191,12,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48984
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_IOCR',0,6,196,12,3
	.word	61086
	.byte	12,6,199,12,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49134
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_LBISTCTRL0',0,6,204,12,3
	.word	61148
	.byte	12,6,207,12,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49283
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_LBISTCTRL1',0,6,212,12,3
	.word	61216
	.byte	12,6,215,12,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49444
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_LBISTCTRL2',0,6,220,12,3
	.word	61284
	.byte	12,6,223,12,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49574
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_LCLCON',0,6,228,12,3
	.word	61352
	.byte	12,6,231,12,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49706
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_LCLTEST',0,6,236,12,3
	.word	61416
	.byte	12,6,239,12,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49821
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_MANID',0,6,244,12,3
	.word	61481
	.byte	12,6,247,12,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49932
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_OMR',0,6,252,12,3
	.word	61544
	.byte	12,6,255,12,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50090
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_OSCCON',0,6,132,13,3
	.word	61605
	.byte	12,6,135,13,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50502
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_OUT',0,6,140,13,3
	.word	61669
	.byte	12,6,143,13,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50603
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_OVCCON',0,6,148,13,3
	.word	61730
	.byte	12,6,151,13,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50870
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_OVCENABLE',0,6,156,13,3
	.word	61794
	.byte	12,6,159,13,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51006
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_PDISC',0,6,164,13,3
	.word	61861
	.byte	12,6,167,13,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51117
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_PDR',0,6,172,13,3
	.word	61924
	.byte	12,6,175,13,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51250
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_PDRR',0,6,180,13,3
	.word	61985
	.byte	12,6,183,13,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51453
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_PLLCON0',0,6,188,13,3
	.word	62047
	.byte	12,6,191,13,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51809
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_PLLCON1',0,6,196,13,3
	.word	62112
	.byte	12,6,199,13,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51987
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_PLLCON2',0,6,204,13,3
	.word	62177
	.byte	12,6,207,13,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52087
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_PLLERAYCON0',0,6,212,13,3
	.word	62242
	.byte	12,6,215,13,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52457
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_PLLERAYCON1',0,6,220,13,3
	.word	62311
	.byte	12,6,223,13,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52643
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_PLLERAYSTAT',0,6,228,13,3
	.word	62380
	.byte	12,6,231,13,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52841
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_PLLSTAT',0,6,236,13,3
	.word	62449
	.byte	12,6,239,13,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53074
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_PMCSR',0,6,244,13,3
	.word	62514
	.byte	12,6,247,13,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53226
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_PMSWCR0',0,6,252,13,3
	.word	62577
	.byte	12,6,255,13,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53793
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_PMSWCR1',0,6,132,14,3
	.word	62642
	.byte	12,6,135,14,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54087
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_PMSWCR2',0,6,140,14,3
	.word	62707
	.byte	12,6,143,14,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54365
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_PMSWSTAT',0,6,148,14,3
	.word	62772
	.byte	12,6,151,14,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54861
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_PMSWSTATCLR',0,6,156,14,3
	.word	62838
	.byte	12,6,159,14,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55383
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_RSTCON',0,6,164,14,3
	.word	62907
	.byte	12,6,167,14,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55174
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_RSTCON2',0,6,172,14,3
	.word	62971
	.byte	12,6,175,14,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55594
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_RSTSTAT',0,6,180,14,3
	.word	63036
	.byte	12,6,183,14,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56026
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_SAFECON',0,6,188,14,3
	.word	63101
	.byte	12,6,191,14,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56122
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_STSTAT',0,6,196,14,3
	.word	63166
	.byte	12,6,199,14,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56382
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_SWRSTCON',0,6,204,14,3
	.word	63230
	.byte	12,6,207,14,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56507
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_SYSCON',0,6,212,14,3
	.word	63296
	.byte	12,6,215,14,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56704
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_TRAPCLR',0,6,220,14,3
	.word	63360
	.byte	12,6,223,14,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56857
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_TRAPDIS',0,6,228,14,3
	.word	63425
	.byte	12,6,231,14,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57010
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_TRAPSET',0,6,236,14,3
	.word	63490
	.byte	12,6,239,14,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57163
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_TRAPSTAT',0,6,244,14,3
	.word	63555
	.byte	33
	.byte	'Ifx_SCU_WDTCPU_CON0',0,6,252,14,3
	.word	999
	.byte	33
	.byte	'Ifx_SCU_WDTCPU_CON1',0,6,132,15,3
	.word	1265
	.byte	33
	.byte	'Ifx_SCU_WDTCPU_SR',0,6,140,15,3
	.word	1496
	.byte	12,6,143,15,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57418
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_WDTS_CON0',0,6,148,15,3
	.word	63706
	.byte	12,6,151,15,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57544
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_WDTS_CON1',0,6,156,15,3
	.word	63773
	.byte	12,6,159,15,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57796
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_WDTS_SR',0,6,164,15,3
	.word	63840
	.byte	14
	.word	1536
	.byte	33
	.byte	'Ifx_SCU_WDTCPU',0,6,180,15,3
	.word	63905
	.byte	10
	.byte	'_Ifx_SCU_WDTS',0,6,183,15,25,12,13
	.byte	'CON0',0
	.word	63706
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	63773
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	63840
	.byte	4,2,35,8,0,14
	.word	63934
	.byte	33
	.byte	'Ifx_SCU_WDTS',0,6,188,15,3
	.word	63995
	.byte	18,8
	.word	59171
	.byte	19,1,0,18,20
	.word	488
	.byte	19,19,0,18,8
	.word	62514
	.byte	19,1,0,14
	.word	63934
	.byte	18,24
	.word	1536
	.byte	19,1,0,14
	.word	64054
	.byte	18,28
	.word	488
	.byte	19,27,0,18,16
	.word	58985
	.byte	19,3,0,18,16
	.word	60964
	.byte	19,3,0,18,180,3
	.word	488
	.byte	19,179,3,0,10
	.byte	'_Ifx_SCU',0,6,201,15,25,128,8,13
	.byte	'reserved_0',0
	.word	5212
	.byte	8,2,35,0,13
	.byte	'ID',0
	.word	60904
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	3393
	.byte	4,2,35,12,13
	.byte	'OSCCON',0
	.word	61605
	.byte	4,2,35,16,13
	.byte	'PLLSTAT',0
	.word	62449
	.byte	4,2,35,20,13
	.byte	'PLLCON0',0
	.word	62047
	.byte	4,2,35,24,13
	.byte	'PLLCON1',0
	.word	62112
	.byte	4,2,35,28,13
	.byte	'PLLCON2',0
	.word	62177
	.byte	4,2,35,32,13
	.byte	'PLLERAYSTAT',0
	.word	62380
	.byte	4,2,35,36,13
	.byte	'PLLERAYCON0',0
	.word	62242
	.byte	4,2,35,40,13
	.byte	'PLLERAYCON1',0
	.word	62311
	.byte	4,2,35,44,13
	.byte	'CCUCON0',0
	.word	58208
	.byte	4,2,35,48,13
	.byte	'CCUCON1',0
	.word	58273
	.byte	4,2,35,52,13
	.byte	'FDR',0
	.word	60782
	.byte	4,2,35,56,13
	.byte	'EXTCON',0
	.word	60718
	.byte	4,2,35,60,13
	.byte	'CCUCON2',0
	.word	58338
	.byte	4,2,35,64,13
	.byte	'CCUCON3',0
	.word	58403
	.byte	4,2,35,68,13
	.byte	'CCUCON4',0
	.word	58468
	.byte	4,2,35,72,13
	.byte	'CCUCON5',0
	.word	58533
	.byte	4,2,35,76,13
	.byte	'RSTSTAT',0
	.word	63036
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	3393
	.byte	4,2,35,84,13
	.byte	'RSTCON',0
	.word	62907
	.byte	4,2,35,88,13
	.byte	'ARSTDIS',0
	.word	58143
	.byte	4,2,35,92,13
	.byte	'SWRSTCON',0
	.word	63230
	.byte	4,2,35,96,13
	.byte	'RSTCON2',0
	.word	62971
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	3393
	.byte	4,2,35,104,13
	.byte	'EVRRSTCON',0
	.word	59769
	.byte	4,2,35,108,13
	.byte	'ESRCFG',0
	.word	64022
	.byte	8,2,35,112,13
	.byte	'ESROCFG',0
	.word	59235
	.byte	4,2,35,120,13
	.byte	'SYSCON',0
	.word	63296
	.byte	4,2,35,124,13
	.byte	'CCUCON6',0
	.word	58598
	.byte	4,3,35,128,1,13
	.byte	'CCUCON7',0
	.word	58663
	.byte	4,3,35,132,1,13
	.byte	'reserved_88',0
	.word	64031
	.byte	20,3,35,136,1,13
	.byte	'PDR',0
	.word	61924
	.byte	4,3,35,156,1,13
	.byte	'IOCR',0
	.word	61086
	.byte	4,3,35,160,1,13
	.byte	'OUT',0
	.word	61669
	.byte	4,3,35,164,1,13
	.byte	'OMR',0
	.word	61544
	.byte	4,3,35,168,1,13
	.byte	'IN',0
	.word	61026
	.byte	4,3,35,172,1,13
	.byte	'EVRSTAT',0
	.word	60522
	.byte	4,3,35,176,1,13
	.byte	'EVRDVSTAT',0
	.word	59500
	.byte	4,3,35,180,1,13
	.byte	'EVR13CON',0
	.word	59300
	.byte	4,3,35,184,1,13
	.byte	'EVR33CON',0
	.word	59366
	.byte	4,3,35,188,1,13
	.byte	'STSTAT',0
	.word	63166
	.byte	4,3,35,192,1,13
	.byte	'reserved_C4',0
	.word	3393
	.byte	4,3,35,196,1,13
	.byte	'PMSWCR0',0
	.word	62577
	.byte	4,3,35,200,1,13
	.byte	'PMSWSTAT',0
	.word	62772
	.byte	4,3,35,204,1,13
	.byte	'PMSWSTATCLR',0
	.word	62838
	.byte	4,3,35,208,1,13
	.byte	'PMCSR',0
	.word	64040
	.byte	8,3,35,212,1,13
	.byte	'reserved_DC',0
	.word	3393
	.byte	4,3,35,220,1,13
	.byte	'DTSSTAT',0
	.word	58920
	.byte	4,3,35,224,1,13
	.byte	'DTSCON',0
	.word	58792
	.byte	4,3,35,228,1,13
	.byte	'PMSWCR1',0
	.word	62642
	.byte	4,3,35,232,1,13
	.byte	'PMSWCR2',0
	.word	62707
	.byte	4,3,35,236,1,13
	.byte	'WDTS',0
	.word	64049
	.byte	12,3,35,240,1,13
	.byte	'EMSR',0
	.word	59109
	.byte	4,3,35,252,1,13
	.byte	'WDTCPU',0
	.word	64063
	.byte	24,3,35,128,2,13
	.byte	'reserved_118',0
	.word	5552
	.byte	12,3,35,152,2,13
	.byte	'TRAPSTAT',0
	.word	63555
	.byte	4,3,35,164,2,13
	.byte	'TRAPSET',0
	.word	63490
	.byte	4,3,35,168,2,13
	.byte	'TRAPCLR',0
	.word	63360
	.byte	4,3,35,172,2,13
	.byte	'TRAPDIS',0
	.word	63425
	.byte	4,3,35,176,2,13
	.byte	'reserved_134',0
	.word	3393
	.byte	4,3,35,180,2,13
	.byte	'LCLCON1',0
	.word	61352
	.byte	4,3,35,184,2,13
	.byte	'LCLTEST',0
	.word	61416
	.byte	4,3,35,188,2,13
	.byte	'CHIPID',0
	.word	58728
	.byte	4,3,35,192,2,13
	.byte	'MANID',0
	.word	61481
	.byte	4,3,35,196,2,13
	.byte	'reserved_148',0
	.word	5212
	.byte	8,3,35,200,2,13
	.byte	'SAFECON',0
	.word	63101
	.byte	4,3,35,208,2,13
	.byte	'reserved_154',0
	.word	34619
	.byte	16,3,35,212,2,13
	.byte	'LBISTCTRL0',0
	.word	61148
	.byte	4,3,35,228,2,13
	.byte	'LBISTCTRL1',0
	.word	61216
	.byte	4,3,35,232,2,13
	.byte	'LBISTCTRL2',0
	.word	61284
	.byte	4,3,35,236,2,13
	.byte	'reserved_170',0
	.word	64068
	.byte	28,3,35,240,2,13
	.byte	'PDISC',0
	.word	61861
	.byte	4,3,35,140,3,13
	.byte	'reserved_190',0
	.word	5212
	.byte	8,3,35,144,3,13
	.byte	'EVRTRIM',0
	.word	60587
	.byte	4,3,35,152,3,13
	.byte	'EVRADCSTAT',0
	.word	59432
	.byte	4,3,35,156,3,13
	.byte	'EVRUVMON',0
	.word	60652
	.byte	4,3,35,160,3,13
	.byte	'EVROVMON',0
	.word	59703
	.byte	4,3,35,164,3,13
	.byte	'EVRMONCTRL',0
	.word	59567
	.byte	4,3,35,168,3,13
	.byte	'reserved_1AC',0
	.word	3393
	.byte	4,3,35,172,3,13
	.byte	'EVRSDCTRL1',0
	.word	60250
	.byte	4,3,35,176,3,13
	.byte	'EVRSDCTRL2',0
	.word	60318
	.byte	4,3,35,180,3,13
	.byte	'EVRSDCTRL3',0
	.word	60386
	.byte	4,3,35,184,3,13
	.byte	'EVRSDCTRL4',0
	.word	60454
	.byte	4,3,35,188,3,13
	.byte	'EVRSDCOEFF1',0
	.word	59836
	.byte	4,3,35,192,3,13
	.byte	'EVRSDCOEFF2',0
	.word	59905
	.byte	4,3,35,196,3,13
	.byte	'EVRSDCOEFF3',0
	.word	59974
	.byte	4,3,35,200,3,13
	.byte	'EVRSDCOEFF4',0
	.word	60043
	.byte	4,3,35,204,3,13
	.byte	'EVRSDCOEFF5',0
	.word	60112
	.byte	4,3,35,208,3,13
	.byte	'EVRSDCOEFF6',0
	.word	60181
	.byte	4,3,35,212,3,13
	.byte	'EVROSCCTRL',0
	.word	59635
	.byte	4,3,35,216,3,13
	.byte	'reserved_1DC',0
	.word	3393
	.byte	4,3,35,220,3,13
	.byte	'OVCENABLE',0
	.word	61794
	.byte	4,3,35,224,3,13
	.byte	'OVCCON',0
	.word	61730
	.byte	4,3,35,228,3,13
	.byte	'reserved_1E8',0
	.word	34254
	.byte	40,3,35,232,3,13
	.byte	'EICR',0
	.word	64077
	.byte	16,3,35,144,4,13
	.byte	'EIFR',0
	.word	59047
	.byte	4,3,35,160,4,13
	.byte	'FMR',0
	.word	60843
	.byte	4,3,35,164,4,13
	.byte	'PDRR',0
	.word	61985
	.byte	4,3,35,168,4,13
	.byte	'IGCR',0
	.word	64086
	.byte	16,3,35,172,4,13
	.byte	'reserved_23C',0
	.word	3393
	.byte	4,3,35,188,4,13
	.byte	'DTSLIM',0
	.word	58856
	.byte	4,3,35,192,4,13
	.byte	'reserved_244',0
	.word	64095
	.byte	180,3,3,35,196,4,13
	.byte	'ACCEN1',0
	.word	58079
	.byte	4,3,35,248,7,13
	.byte	'ACCEN0',0
	.word	58015
	.byte	4,3,35,252,7,0,14
	.word	64106
	.byte	33
	.byte	'Ifx_SCU',0,6,181,16,3
	.word	66096
	.byte	10
	.byte	'_Ifx_CPU_A_Bits',0,24,45,16,4,11
	.byte	'ADDR',0,4
	.word	887
	.byte	32,0,2,35,0,0,33
	.byte	'Ifx_CPU_A_Bits',0,24,48,3
	.word	66118
	.byte	10
	.byte	'_Ifx_CPU_BIV_Bits',0,24,51,16,4,11
	.byte	'VSS',0,4
	.word	887
	.byte	1,31,2,35,0,11
	.byte	'BIV',0,4
	.word	887
	.byte	31,0,2,35,0,0,33
	.byte	'Ifx_CPU_BIV_Bits',0,24,55,3
	.word	66179
	.byte	10
	.byte	'_Ifx_CPU_BTV_Bits',0,24,58,16,4,11
	.byte	'reserved_0',0,4
	.word	887
	.byte	1,31,2,35,0,11
	.byte	'BTV',0,4
	.word	887
	.byte	31,0,2,35,0,0,33
	.byte	'Ifx_CPU_BTV_Bits',0,24,62,3
	.word	66258
	.byte	10
	.byte	'_Ifx_CPU_CCNT_Bits',0,24,65,16,4,11
	.byte	'CountValue',0,4
	.word	887
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	887
	.byte	1,0,2,35,0,0,33
	.byte	'Ifx_CPU_CCNT_Bits',0,24,69,3
	.word	66344
	.byte	10
	.byte	'_Ifx_CPU_CCTRL_Bits',0,24,72,16,4,11
	.byte	'CM',0,4
	.word	887
	.byte	1,31,2,35,0,11
	.byte	'CE',0,4
	.word	887
	.byte	1,30,2,35,0,11
	.byte	'M1',0,4
	.word	887
	.byte	3,27,2,35,0,11
	.byte	'M2',0,4
	.word	887
	.byte	3,24,2,35,0,11
	.byte	'M3',0,4
	.word	887
	.byte	3,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	887
	.byte	21,0,2,35,0,0,33
	.byte	'Ifx_CPU_CCTRL_Bits',0,24,80,3
	.word	66433
	.byte	10
	.byte	'_Ifx_CPU_COMPAT_Bits',0,24,83,16,4,11
	.byte	'reserved_0',0,4
	.word	887
	.byte	3,29,2,35,0,11
	.byte	'RM',0,4
	.word	887
	.byte	1,28,2,35,0,11
	.byte	'SP',0,4
	.word	887
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	887
	.byte	27,0,2,35,0,0,33
	.byte	'Ifx_CPU_COMPAT_Bits',0,24,89,3
	.word	66579
	.byte	10
	.byte	'_Ifx_CPU_CORE_ID_Bits',0,24,92,16,4,11
	.byte	'CORE_ID',0,4
	.word	887
	.byte	3,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	887
	.byte	29,0,2,35,0,0,33
	.byte	'Ifx_CPU_CORE_ID_Bits',0,24,96,3
	.word	66706
	.byte	10
	.byte	'_Ifx_CPU_CPR_L_Bits',0,24,99,16,4,11
	.byte	'reserved_0',0,4
	.word	887
	.byte	3,29,2,35,0,11
	.byte	'LOWBND',0,4
	.word	887
	.byte	29,0,2,35,0,0,33
	.byte	'Ifx_CPU_CPR_L_Bits',0,24,103,3
	.word	66804
	.byte	10
	.byte	'_Ifx_CPU_CPR_U_Bits',0,24,106,16,4,11
	.byte	'reserved_0',0,4
	.word	887
	.byte	3,29,2,35,0,11
	.byte	'UPPBND',0,4
	.word	887
	.byte	29,0,2,35,0,0,33
	.byte	'Ifx_CPU_CPR_U_Bits',0,24,110,3
	.word	66897
	.byte	10
	.byte	'_Ifx_CPU_CPU_ID_Bits',0,24,113,16,4,11
	.byte	'MODREV',0,4
	.word	887
	.byte	8,24,2,35,0,11
	.byte	'MOD_32B',0,4
	.word	887
	.byte	8,16,2,35,0,11
	.byte	'MOD',0,4
	.word	887
	.byte	16,0,2,35,0,0,33
	.byte	'Ifx_CPU_CPU_ID_Bits',0,24,118,3
	.word	66990
	.byte	10
	.byte	'_Ifx_CPU_CPXE_Bits',0,24,121,16,4,11
	.byte	'XE',0,4
	.word	887
	.byte	8,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	887
	.byte	24,0,2,35,0,0,33
	.byte	'Ifx_CPU_CPXE_Bits',0,24,125,3
	.word	67097
	.byte	10
	.byte	'_Ifx_CPU_CREVT_Bits',0,24,128,1,16,4,11
	.byte	'EVTA',0,4
	.word	887
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	887
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	887
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	887
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	887
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	887
	.byte	24,0,2,35,0,0,33
	.byte	'Ifx_CPU_CREVT_Bits',0,24,136,1,3
	.word	67184
	.byte	10
	.byte	'_Ifx_CPU_CUS_ID_Bits',0,24,139,1,16,4,11
	.byte	'CID',0,4
	.word	887
	.byte	3,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	887
	.byte	29,0,2,35,0,0,33
	.byte	'Ifx_CPU_CUS_ID_Bits',0,24,143,1,3
	.word	67338
	.byte	10
	.byte	'_Ifx_CPU_D_Bits',0,24,146,1,16,4,11
	.byte	'DATA',0,4
	.word	887
	.byte	32,0,2,35,0,0,33
	.byte	'Ifx_CPU_D_Bits',0,24,149,1,3
	.word	67432
	.byte	10
	.byte	'_Ifx_CPU_DATR_Bits',0,24,152,1,16,4,11
	.byte	'reserved_0',0,4
	.word	887
	.byte	3,29,2,35,0,11
	.byte	'SBE',0,4
	.word	887
	.byte	1,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	887
	.byte	5,23,2,35,0,11
	.byte	'CWE',0,4
	.word	887
	.byte	1,22,2,35,0,11
	.byte	'CFE',0,4
	.word	887
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	887
	.byte	3,18,2,35,0,11
	.byte	'SOE',0,4
	.word	887
	.byte	1,17,2,35,0,11
	.byte	'SME',0,4
	.word	887
	.byte	1,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	887
	.byte	16,0,2,35,0,0,33
	.byte	'Ifx_CPU_DATR_Bits',0,24,163,1,3
	.word	67495
	.byte	33
	.byte	'Ifx_CPU_DBGSR_Bits',0,24,177,1,3
	.word	30197
	.byte	10
	.byte	'_Ifx_CPU_DBGTCR_Bits',0,24,180,1,16,4,11
	.byte	'DTA',0,4
	.word	887
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	887
	.byte	31,0,2,35,0,0,33
	.byte	'Ifx_CPU_DBGTCR_Bits',0,24,184,1,3
	.word	67741
	.byte	10
	.byte	'_Ifx_CPU_DCON0_Bits',0,24,187,1,16,4,11
	.byte	'reserved_0',0,4
	.word	887
	.byte	1,31,2,35,0,11
	.byte	'DCBYP',0,4
	.word	887
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	887
	.byte	30,0,2,35,0,0,33
	.byte	'Ifx_CPU_DCON0_Bits',0,24,192,1,3
	.word	67835
	.byte	10
	.byte	'_Ifx_CPU_DCON2_Bits',0,24,195,1,16,4,11
	.byte	'DCACHE_SZE',0,4
	.word	887
	.byte	16,16,2,35,0,11
	.byte	'DSCRATCH_SZE',0,4
	.word	887
	.byte	16,0,2,35,0,0,33
	.byte	'Ifx_CPU_DCON2_Bits',0,24,199,1,3
	.word	67951
	.byte	10
	.byte	'_Ifx_CPU_DCX_Bits',0,24,202,1,16,4,11
	.byte	'reserved_0',0,4
	.word	887
	.byte	6,26,2,35,0,11
	.byte	'DCXValue',0,4
	.word	887
	.byte	26,0,2,35,0,0,33
	.byte	'Ifx_CPU_DCX_Bits',0,24,206,1,3
	.word	68052
	.byte	10
	.byte	'_Ifx_CPU_DEADD_Bits',0,24,209,1,16,4,11
	.byte	'ERROR_ADDRESS',0,4
	.word	887
	.byte	32,0,2,35,0,0,33
	.byte	'Ifx_CPU_DEADD_Bits',0,24,212,1,3
	.word	68145
	.byte	10
	.byte	'_Ifx_CPU_DIEAR_Bits',0,24,215,1,16,4,11
	.byte	'TA',0,4
	.word	887
	.byte	32,0,2,35,0,0,33
	.byte	'Ifx_CPU_DIEAR_Bits',0,24,218,1,3
	.word	68225
	.byte	10
	.byte	'_Ifx_CPU_DIETR_Bits',0,24,221,1,16,4,11
	.byte	'IED',0,4
	.word	887
	.byte	1,31,2,35,0,11
	.byte	'IE_T',0,4
	.word	887
	.byte	1,30,2,35,0,11
	.byte	'IE_C',0,4
	.word	887
	.byte	1,29,2,35,0,11
	.byte	'IE_S',0,4
	.word	887
	.byte	1,28,2,35,0,11
	.byte	'IE_BI',0,4
	.word	887
	.byte	1,27,2,35,0,11
	.byte	'E_INFO',0,4
	.word	887
	.byte	6,21,2,35,0,11
	.byte	'IE_DUAL',0,4
	.word	887
	.byte	1,20,2,35,0,11
	.byte	'IE_SP',0,4
	.word	887
	.byte	1,19,2,35,0,11
	.byte	'IE_BS',0,4
	.word	887
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	887
	.byte	18,0,2,35,0,0,33
	.byte	'Ifx_CPU_DIETR_Bits',0,24,233,1,3
	.word	68294
	.byte	10
	.byte	'_Ifx_CPU_DMS_Bits',0,24,236,1,16,4,11
	.byte	'reserved_0',0,4
	.word	887
	.byte	1,31,2,35,0,11
	.byte	'DMSValue',0,4
	.word	887
	.byte	31,0,2,35,0,0,33
	.byte	'Ifx_CPU_DMS_Bits',0,24,240,1,3
	.word	68523
	.byte	10
	.byte	'_Ifx_CPU_DPR_L_Bits',0,24,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	887
	.byte	3,29,2,35,0,11
	.byte	'LOWBND',0,4
	.word	887
	.byte	29,0,2,35,0,0,33
	.byte	'Ifx_CPU_DPR_L_Bits',0,24,247,1,3
	.word	68616
	.byte	10
	.byte	'_Ifx_CPU_DPR_U_Bits',0,24,250,1,16,4,11
	.byte	'reserved_0',0,4
	.word	887
	.byte	3,29,2,35,0,11
	.byte	'UPPBND',0,4
	.word	887
	.byte	29,0,2,35,0,0,33
	.byte	'Ifx_CPU_DPR_U_Bits',0,24,254,1,3
	.word	68711
	.byte	10
	.byte	'_Ifx_CPU_DPRE_Bits',0,24,129,2,16,4,11
	.byte	'RE',0,4
	.word	887
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	887
	.byte	16,0,2,35,0,0,33
	.byte	'Ifx_CPU_DPRE_Bits',0,24,133,2,3
	.word	68806
	.byte	10
	.byte	'_Ifx_CPU_DPWE_Bits',0,24,136,2,16,4,11
	.byte	'WE',0,4
	.word	887
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	887
	.byte	16,0,2,35,0,0,33
	.byte	'Ifx_CPU_DPWE_Bits',0,24,140,2,3
	.word	68896
	.byte	10
	.byte	'_Ifx_CPU_DSTR_Bits',0,24,143,2,16,4,11
	.byte	'SRE',0,4
	.word	887
	.byte	1,31,2,35,0,11
	.byte	'GAE',0,4
	.word	887
	.byte	1,30,2,35,0,11
	.byte	'LBE',0,4
	.word	887
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	887
	.byte	3,26,2,35,0,11
	.byte	'CRE',0,4
	.word	887
	.byte	1,25,2,35,0,11
	.byte	'reserved_7',0,4
	.word	887
	.byte	7,18,2,35,0,11
	.byte	'DTME',0,4
	.word	887
	.byte	1,17,2,35,0,11
	.byte	'LOE',0,4
	.word	887
	.byte	1,16,2,35,0,11
	.byte	'SDE',0,4
	.word	887
	.byte	1,15,2,35,0,11
	.byte	'SCE',0,4
	.word	887
	.byte	1,14,2,35,0,11
	.byte	'CAC',0,4
	.word	887
	.byte	1,13,2,35,0,11
	.byte	'MPE',0,4
	.word	887
	.byte	1,12,2,35,0,11
	.byte	'CLE',0,4
	.word	887
	.byte	1,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	887
	.byte	3,8,2,35,0,11
	.byte	'ALN',0,4
	.word	887
	.byte	1,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	887
	.byte	7,0,2,35,0,0,33
	.byte	'Ifx_CPU_DSTR_Bits',0,24,161,2,3
	.word	68986
	.byte	10
	.byte	'_Ifx_CPU_EXEVT_Bits',0,24,164,2,16,4,11
	.byte	'EVTA',0,4
	.word	887
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	887
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	887
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	887
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	887
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	887
	.byte	24,0,2,35,0,0,33
	.byte	'Ifx_CPU_EXEVT_Bits',0,24,172,2,3
	.word	69310
	.byte	10
	.byte	'_Ifx_CPU_FCX_Bits',0,24,175,2,16,4,11
	.byte	'FCXO',0,4
	.word	887
	.byte	16,16,2,35,0,11
	.byte	'FCXS',0,4
	.word	887
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	887
	.byte	12,0,2,35,0,0,33
	.byte	'Ifx_CPU_FCX_Bits',0,24,180,2,3
	.word	69464
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_CON_Bits',0,24,183,2,16,4,11
	.byte	'TST',0,4
	.word	887
	.byte	1,31,2,35,0,11
	.byte	'TCL',0,4
	.word	887
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	887
	.byte	6,24,2,35,0,11
	.byte	'RM',0,4
	.word	887
	.byte	2,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	887
	.byte	8,14,2,35,0,11
	.byte	'FXE',0,4
	.word	887
	.byte	1,13,2,35,0,11
	.byte	'FUE',0,4
	.word	887
	.byte	1,12,2,35,0,11
	.byte	'FZE',0,4
	.word	887
	.byte	1,11,2,35,0,11
	.byte	'FVE',0,4
	.word	887
	.byte	1,10,2,35,0,11
	.byte	'FIE',0,4
	.word	887
	.byte	1,9,2,35,0,11
	.byte	'reserved_23',0,4
	.word	887
	.byte	3,6,2,35,0,11
	.byte	'FX',0,4
	.word	887
	.byte	1,5,2,35,0,11
	.byte	'FU',0,4
	.word	887
	.byte	1,4,2,35,0,11
	.byte	'FZ',0,4
	.word	887
	.byte	1,3,2,35,0,11
	.byte	'FV',0,4
	.word	887
	.byte	1,2,2,35,0,11
	.byte	'FI',0,4
	.word	887
	.byte	1,1,2,35,0,11
	.byte	'reserved_31',0,4
	.word	887
	.byte	1,0,2,35,0,0,33
	.byte	'Ifx_CPU_FPU_TRAP_CON_Bits',0,24,202,2,3
	.word	69570
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_OPC_Bits',0,24,205,2,16,4,11
	.byte	'OPC',0,4
	.word	887
	.byte	8,24,2,35,0,11
	.byte	'FMT',0,4
	.word	887
	.byte	1,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	887
	.byte	7,16,2,35,0,11
	.byte	'DREG',0,4
	.word	887
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	887
	.byte	12,0,2,35,0,0,33
	.byte	'Ifx_CPU_FPU_TRAP_OPC_Bits',0,24,212,2,3
	.word	69919
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_PC_Bits',0,24,215,2,16,4,11
	.byte	'PC',0,4
	.word	887
	.byte	32,0,2,35,0,0,33
	.byte	'Ifx_CPU_FPU_TRAP_PC_Bits',0,24,218,2,3
	.word	70079
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC1_Bits',0,24,221,2,16,4,11
	.byte	'SRC1',0,4
	.word	887
	.byte	32,0,2,35,0,0,33
	.byte	'Ifx_CPU_FPU_TRAP_SRC1_Bits',0,24,224,2,3
	.word	70160
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC2_Bits',0,24,227,2,16,4,11
	.byte	'SRC2',0,4
	.word	887
	.byte	32,0,2,35,0,0,33
	.byte	'Ifx_CPU_FPU_TRAP_SRC2_Bits',0,24,230,2,3
	.word	70247
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC3_Bits',0,24,233,2,16,4,11
	.byte	'SRC3',0,4
	.word	887
	.byte	32,0,2,35,0,0,33
	.byte	'Ifx_CPU_FPU_TRAP_SRC3_Bits',0,24,236,2,3
	.word	70334
	.byte	10
	.byte	'_Ifx_CPU_ICNT_Bits',0,24,239,2,16,4,11
	.byte	'CountValue',0,4
	.word	887
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	887
	.byte	1,0,2,35,0,0,33
	.byte	'Ifx_CPU_ICNT_Bits',0,24,243,2,3
	.word	70421
	.byte	33
	.byte	'Ifx_CPU_ICR_Bits',0,24,253,2,3
	.word	30040
	.byte	10
	.byte	'_Ifx_CPU_ISP_Bits',0,24,128,3,16,4,11
	.byte	'ISP',0,4
	.word	887
	.byte	32,0,2,35,0,0,33
	.byte	'Ifx_CPU_ISP_Bits',0,24,131,3,3
	.word	70538
	.byte	10
	.byte	'_Ifx_CPU_LCX_Bits',0,24,134,3,16,4,11
	.byte	'LCXO',0,4
	.word	887
	.byte	16,16,2,35,0,11
	.byte	'LCXS',0,4
	.word	887
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	887
	.byte	12,0,2,35,0,0,33
	.byte	'Ifx_CPU_LCX_Bits',0,24,139,3,3
	.word	70604
	.byte	10
	.byte	'_Ifx_CPU_M1CNT_Bits',0,24,142,3,16,4,11
	.byte	'CountValue',0,4
	.word	887
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	887
	.byte	1,0,2,35,0,0,33
	.byte	'Ifx_CPU_M1CNT_Bits',0,24,146,3,3
	.word	70710
	.byte	10
	.byte	'_Ifx_CPU_M2CNT_Bits',0,24,149,3,16,4,11
	.byte	'CountValue',0,4
	.word	887
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	887
	.byte	1,0,2,35,0,0,33
	.byte	'Ifx_CPU_M2CNT_Bits',0,24,153,3,3
	.word	70803
	.byte	10
	.byte	'_Ifx_CPU_M3CNT_Bits',0,24,156,3,16,4,11
	.byte	'CountValue',0,4
	.word	887
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	887
	.byte	1,0,2,35,0,0,33
	.byte	'Ifx_CPU_M3CNT_Bits',0,24,160,3,3
	.word	70896
	.byte	10
	.byte	'_Ifx_CPU_PC_Bits',0,24,163,3,16,4,11
	.byte	'reserved_0',0,4
	.word	887
	.byte	1,31,2,35,0,11
	.byte	'PC',0,4
	.word	887
	.byte	31,0,2,35,0,0,33
	.byte	'Ifx_CPU_PC_Bits',0,24,167,3,3
	.word	70989
	.byte	10
	.byte	'_Ifx_CPU_PCON0_Bits',0,24,170,3,16,4,11
	.byte	'reserved_0',0,4
	.word	887
	.byte	1,31,2,35,0,11
	.byte	'PCBYP',0,4
	.word	887
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	887
	.byte	30,0,2,35,0,0,33
	.byte	'Ifx_CPU_PCON0_Bits',0,24,175,3,3
	.word	71074
	.byte	10
	.byte	'_Ifx_CPU_PCON1_Bits',0,24,178,3,16,4,11
	.byte	'PCINV',0,4
	.word	887
	.byte	1,31,2,35,0,11
	.byte	'PBINV',0,4
	.word	887
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	887
	.byte	30,0,2,35,0,0,33
	.byte	'Ifx_CPU_PCON1_Bits',0,24,183,3,3
	.word	71190
	.byte	10
	.byte	'_Ifx_CPU_PCON2_Bits',0,24,186,3,16,4,11
	.byte	'PCACHE_SZE',0,4
	.word	887
	.byte	16,16,2,35,0,11
	.byte	'PSCRATCH_SZE',0,4
	.word	887
	.byte	16,0,2,35,0,0,33
	.byte	'Ifx_CPU_PCON2_Bits',0,24,190,3,3
	.word	71301
	.byte	10
	.byte	'_Ifx_CPU_PCXI_Bits',0,24,193,3,16,4,11
	.byte	'PCXO',0,4
	.word	887
	.byte	16,16,2,35,0,11
	.byte	'PCXS',0,4
	.word	887
	.byte	4,12,2,35,0,11
	.byte	'UL',0,4
	.word	887
	.byte	1,11,2,35,0,11
	.byte	'PIE',0,4
	.word	887
	.byte	1,10,2,35,0,11
	.byte	'PCPN',0,4
	.word	887
	.byte	10,0,2,35,0,0,33
	.byte	'Ifx_CPU_PCXI_Bits',0,24,200,3,3
	.word	71402
	.byte	10
	.byte	'_Ifx_CPU_PIEAR_Bits',0,24,203,3,16,4,11
	.byte	'TA',0,4
	.word	887
	.byte	32,0,2,35,0,0,33
	.byte	'Ifx_CPU_PIEAR_Bits',0,24,206,3,3
	.word	71532
	.byte	10
	.byte	'_Ifx_CPU_PIETR_Bits',0,24,209,3,16,4,11
	.byte	'IED',0,4
	.word	887
	.byte	1,31,2,35,0,11
	.byte	'IE_T',0,4
	.word	887
	.byte	1,30,2,35,0,11
	.byte	'IE_C',0,4
	.word	887
	.byte	1,29,2,35,0,11
	.byte	'IE_S',0,4
	.word	887
	.byte	1,28,2,35,0,11
	.byte	'IE_BI',0,4
	.word	887
	.byte	1,27,2,35,0,11
	.byte	'E_INFO',0,4
	.word	887
	.byte	6,21,2,35,0,11
	.byte	'IE_DUAL',0,4
	.word	887
	.byte	1,20,2,35,0,11
	.byte	'IE_SP',0,4
	.word	887
	.byte	1,19,2,35,0,11
	.byte	'IE_BS',0,4
	.word	887
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	887
	.byte	18,0,2,35,0,0,33
	.byte	'Ifx_CPU_PIETR_Bits',0,24,221,3,3
	.word	71601
	.byte	10
	.byte	'_Ifx_CPU_PMA0_Bits',0,24,224,3,16,4,11
	.byte	'reserved_0',0,4
	.word	887
	.byte	13,19,2,35,0,11
	.byte	'DAC',0,4
	.word	887
	.byte	3,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	887
	.byte	16,0,2,35,0,0,33
	.byte	'Ifx_CPU_PMA0_Bits',0,24,229,3,3
	.word	71830
	.byte	10
	.byte	'_Ifx_CPU_PMA1_Bits',0,24,232,3,16,4,11
	.byte	'reserved_0',0,4
	.word	887
	.byte	14,18,2,35,0,11
	.byte	'CAC',0,4
	.word	887
	.byte	2,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	887
	.byte	16,0,2,35,0,0,33
	.byte	'Ifx_CPU_PMA1_Bits',0,24,237,3,3
	.word	71943
	.byte	10
	.byte	'_Ifx_CPU_PMA2_Bits',0,24,240,3,16,4,11
	.byte	'PSI',0,4
	.word	887
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	887
	.byte	16,0,2,35,0,0,33
	.byte	'Ifx_CPU_PMA2_Bits',0,24,244,3,3
	.word	72056
	.byte	10
	.byte	'_Ifx_CPU_PSTR_Bits',0,24,247,3,16,4,11
	.byte	'FRE',0,4
	.word	887
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	887
	.byte	1,30,2,35,0,11
	.byte	'FBE',0,4
	.word	887
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	887
	.byte	9,20,2,35,0,11
	.byte	'FPE',0,4
	.word	887
	.byte	1,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	887
	.byte	1,18,2,35,0,11
	.byte	'FME',0,4
	.word	887
	.byte	1,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	887
	.byte	17,0,2,35,0,0,33
	.byte	'Ifx_CPU_PSTR_Bits',0,24,129,4,3
	.word	72147
	.byte	10
	.byte	'_Ifx_CPU_PSW_Bits',0,24,132,4,16,4,11
	.byte	'CDC',0,4
	.word	887
	.byte	7,25,2,35,0,11
	.byte	'CDE',0,4
	.word	887
	.byte	1,24,2,35,0,11
	.byte	'GW',0,4
	.word	887
	.byte	1,23,2,35,0,11
	.byte	'IS',0,4
	.word	887
	.byte	1,22,2,35,0,11
	.byte	'IO',0,4
	.word	887
	.byte	2,20,2,35,0,11
	.byte	'PRS',0,4
	.word	887
	.byte	2,18,2,35,0,11
	.byte	'S',0,4
	.word	887
	.byte	1,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	887
	.byte	12,5,2,35,0,11
	.byte	'SAV',0,4
	.word	887
	.byte	1,4,2,35,0,11
	.byte	'AV',0,4
	.word	887
	.byte	1,3,2,35,0,11
	.byte	'SV',0,4
	.word	887
	.byte	1,2,2,35,0,11
	.byte	'V',0,4
	.word	887
	.byte	1,1,2,35,0,11
	.byte	'C',0,4
	.word	887
	.byte	1,0,2,35,0,0,33
	.byte	'Ifx_CPU_PSW_Bits',0,24,147,4,3
	.word	72350
	.byte	10
	.byte	'_Ifx_CPU_SEGEN_Bits',0,24,150,4,16,4,11
	.byte	'ADFLIP',0,4
	.word	887
	.byte	8,24,2,35,0,11
	.byte	'ADTYPE',0,4
	.word	887
	.byte	2,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	887
	.byte	21,1,2,35,0,11
	.byte	'AE',0,4
	.word	887
	.byte	1,0,2,35,0,0,33
	.byte	'Ifx_CPU_SEGEN_Bits',0,24,156,4,3
	.word	72593
	.byte	10
	.byte	'_Ifx_CPU_SMACON_Bits',0,24,159,4,16,4,11
	.byte	'PC',0,4
	.word	887
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	887
	.byte	1,30,2,35,0,11
	.byte	'PT',0,4
	.word	887
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	887
	.byte	5,24,2,35,0,11
	.byte	'DC',0,4
	.word	887
	.byte	1,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	887
	.byte	1,22,2,35,0,11
	.byte	'DT',0,4
	.word	887
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	887
	.byte	13,8,2,35,0,11
	.byte	'IODT',0,4
	.word	887
	.byte	1,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	887
	.byte	7,0,2,35,0,0,33
	.byte	'Ifx_CPU_SMACON_Bits',0,24,171,4,3
	.word	72721
	.byte	10
	.byte	'_Ifx_CPU_SPROT_ACCENA_Bits',0,24,174,4,16,4,11
	.byte	'EN',0,4
	.word	465
	.byte	32,0,2,35,0,0,33
	.byte	'Ifx_CPU_SPROT_ACCENA_Bits',0,24,177,4,3
	.word	72962
	.byte	10
	.byte	'_Ifx_CPU_SPROT_ACCENB_Bits',0,24,180,4,16,4,11
	.byte	'reserved_0',0,4
	.word	465
	.byte	32,0,2,35,0,0,33
	.byte	'Ifx_CPU_SPROT_ACCENB_Bits',0,24,183,4,3
	.word	73045
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,24,186,4,16,4,11
	.byte	'EN',0,4
	.word	465
	.byte	32,0,2,35,0,0,33
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,24,189,4,3
	.word	73136
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,24,192,4,16,4,11
	.byte	'reserved_0',0,4
	.word	465
	.byte	32,0,2,35,0,0,33
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,24,195,4,3
	.word	73227
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_LA_Bits',0,24,198,4,16,4,11
	.byte	'reserved_0',0,1
	.word	488
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	465
	.byte	27,0,2,35,0,0,33
	.byte	'Ifx_CPU_SPROT_RGN_LA_Bits',0,24,202,4,3
	.word	73326
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_UA_Bits',0,24,205,4,16,4,11
	.byte	'reserved_0',0,1
	.word	488
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	465
	.byte	27,0,2,35,0,0,33
	.byte	'Ifx_CPU_SPROT_RGN_UA_Bits',0,24,209,4,3
	.word	73433
	.byte	10
	.byte	'_Ifx_CPU_SWEVT_Bits',0,24,212,4,16,4,11
	.byte	'EVTA',0,4
	.word	887
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	887
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	887
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	887
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	887
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	887
	.byte	24,0,2,35,0,0,33
	.byte	'Ifx_CPU_SWEVT_Bits',0,24,220,4,3
	.word	73540
	.byte	10
	.byte	'_Ifx_CPU_SYSCON_Bits',0,24,223,4,16,4,11
	.byte	'FCDSF',0,4
	.word	887
	.byte	1,31,2,35,0,11
	.byte	'PROTEN',0,4
	.word	887
	.byte	1,30,2,35,0,11
	.byte	'TPROTEN',0,4
	.word	887
	.byte	1,29,2,35,0,11
	.byte	'IS',0,4
	.word	887
	.byte	1,28,2,35,0,11
	.byte	'IT',0,4
	.word	887
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	887
	.byte	27,0,2,35,0,0,33
	.byte	'Ifx_CPU_SYSCON_Bits',0,24,231,4,3
	.word	73694
	.byte	10
	.byte	'_Ifx_CPU_TASK_ASI_Bits',0,24,234,4,16,4,11
	.byte	'ASI',0,4
	.word	887
	.byte	5,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	887
	.byte	27,0,2,35,0,0,33
	.byte	'Ifx_CPU_TASK_ASI_Bits',0,24,238,4,3
	.word	73855
	.byte	10
	.byte	'_Ifx_CPU_TPS_CON_Bits',0,24,241,4,16,4,11
	.byte	'TEXP0',0,4
	.word	887
	.byte	1,31,2,35,0,11
	.byte	'TEXP1',0,4
	.word	887
	.byte	1,30,2,35,0,11
	.byte	'TEXP2',0,4
	.word	887
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	887
	.byte	13,16,2,35,0,11
	.byte	'TTRAP',0,4
	.word	887
	.byte	1,15,2,35,0,11
	.byte	'reserved_17',0,4
	.word	887
	.byte	15,0,2,35,0,0,33
	.byte	'Ifx_CPU_TPS_CON_Bits',0,24,249,4,3
	.word	73953
	.byte	10
	.byte	'_Ifx_CPU_TPS_TIMER_Bits',0,24,252,4,16,4,11
	.byte	'Timer',0,4
	.word	887
	.byte	32,0,2,35,0,0,33
	.byte	'Ifx_CPU_TPS_TIMER_Bits',0,24,255,4,3
	.word	74125
	.byte	10
	.byte	'_Ifx_CPU_TR_ADR_Bits',0,24,130,5,16,4,11
	.byte	'ADDR',0,4
	.word	887
	.byte	32,0,2,35,0,0,33
	.byte	'Ifx_CPU_TR_ADR_Bits',0,24,133,5,3
	.word	74205
	.byte	10
	.byte	'_Ifx_CPU_TR_EVT_Bits',0,24,136,5,16,4,11
	.byte	'EVTA',0,4
	.word	887
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	887
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	887
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	887
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	887
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	887
	.byte	4,20,2,35,0,11
	.byte	'TYP',0,4
	.word	887
	.byte	1,19,2,35,0,11
	.byte	'RNG',0,4
	.word	887
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	887
	.byte	1,17,2,35,0,11
	.byte	'ASI_EN',0,4
	.word	887
	.byte	1,16,2,35,0,11
	.byte	'ASI',0,4
	.word	887
	.byte	5,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	887
	.byte	6,5,2,35,0,11
	.byte	'AST',0,4
	.word	887
	.byte	1,4,2,35,0,11
	.byte	'ALD',0,4
	.word	887
	.byte	1,3,2,35,0,11
	.byte	'reserved_29',0,4
	.word	887
	.byte	3,0,2,35,0,0,33
	.byte	'Ifx_CPU_TR_EVT_Bits',0,24,153,5,3
	.word	74278
	.byte	10
	.byte	'_Ifx_CPU_TRIG_ACC_Bits',0,24,156,5,16,4,11
	.byte	'T0',0,4
	.word	887
	.byte	1,31,2,35,0,11
	.byte	'T1',0,4
	.word	887
	.byte	1,30,2,35,0,11
	.byte	'T2',0,4
	.word	887
	.byte	1,29,2,35,0,11
	.byte	'T3',0,4
	.word	887
	.byte	1,28,2,35,0,11
	.byte	'T4',0,4
	.word	887
	.byte	1,27,2,35,0,11
	.byte	'T5',0,4
	.word	887
	.byte	1,26,2,35,0,11
	.byte	'T6',0,4
	.word	887
	.byte	1,25,2,35,0,11
	.byte	'T7',0,4
	.word	887
	.byte	1,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	887
	.byte	24,0,2,35,0,0,33
	.byte	'Ifx_CPU_TRIG_ACC_Bits',0,24,167,5,3
	.word	74596
	.byte	12,24,175,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66118
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_A',0,24,180,5,3
	.word	74791
	.byte	12,24,183,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66179
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_BIV',0,24,188,5,3
	.word	74850
	.byte	12,24,191,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66258
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_BTV',0,24,196,5,3
	.word	74911
	.byte	12,24,199,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66344
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_CCNT',0,24,204,5,3
	.word	74972
	.byte	12,24,207,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66433
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_CCTRL',0,24,212,5,3
	.word	75034
	.byte	12,24,215,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66579
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_COMPAT',0,24,220,5,3
	.word	75097
	.byte	12,24,223,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66706
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_CORE_ID',0,24,228,5,3
	.word	75161
	.byte	12,24,231,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66804
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_CPR_L',0,24,236,5,3
	.word	75226
	.byte	12,24,239,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66897
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_CPR_U',0,24,244,5,3
	.word	75289
	.byte	12,24,247,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66990
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_CPU_ID',0,24,252,5,3
	.word	75352
	.byte	12,24,255,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67097
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_CPXE',0,24,132,6,3
	.word	75416
	.byte	12,24,135,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67184
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_CREVT',0,24,140,6,3
	.word	75478
	.byte	12,24,143,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67338
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_CUS_ID',0,24,148,6,3
	.word	75541
	.byte	12,24,151,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67432
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_D',0,24,156,6,3
	.word	75605
	.byte	12,24,159,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67495
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_DATR',0,24,164,6,3
	.word	75664
	.byte	33
	.byte	'Ifx_CPU_DBGSR',0,24,172,6,3
	.word	30384
	.byte	12,24,175,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67741
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_DBGTCR',0,24,180,6,3
	.word	75749
	.byte	12,24,183,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67835
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_DCON0',0,24,188,6,3
	.word	75813
	.byte	12,24,191,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67951
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_DCON2',0,24,196,6,3
	.word	75876
	.byte	12,24,199,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68052
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_DCX',0,24,204,6,3
	.word	75939
	.byte	12,24,207,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68145
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_DEADD',0,24,212,6,3
	.word	76000
	.byte	12,24,215,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68225
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_DIEAR',0,24,220,6,3
	.word	76063
	.byte	12,24,223,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68294
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_DIETR',0,24,228,6,3
	.word	76126
	.byte	12,24,231,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68523
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_DMS',0,24,236,6,3
	.word	76189
	.byte	12,24,239,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68616
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_DPR_L',0,24,244,6,3
	.word	76250
	.byte	12,24,247,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68711
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_DPR_U',0,24,252,6,3
	.word	76313
	.byte	12,24,255,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68806
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_DPRE',0,24,132,7,3
	.word	76376
	.byte	12,24,135,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68896
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_DPWE',0,24,140,7,3
	.word	76438
	.byte	12,24,143,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68986
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_DSTR',0,24,148,7,3
	.word	76500
	.byte	12,24,151,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69310
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_EXEVT',0,24,156,7,3
	.word	76562
	.byte	12,24,159,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69464
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_FCX',0,24,164,7,3
	.word	76625
	.byte	12,24,167,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69570
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_FPU_TRAP_CON',0,24,172,7,3
	.word	76686
	.byte	12,24,175,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69919
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_FPU_TRAP_OPC',0,24,180,7,3
	.word	76756
	.byte	12,24,183,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70079
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_FPU_TRAP_PC',0,24,188,7,3
	.word	76826
	.byte	12,24,191,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70160
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_FPU_TRAP_SRC1',0,24,196,7,3
	.word	76895
	.byte	12,24,199,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70247
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_FPU_TRAP_SRC2',0,24,204,7,3
	.word	76966
	.byte	12,24,207,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70334
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_FPU_TRAP_SRC3',0,24,212,7,3
	.word	77037
	.byte	12,24,215,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70421
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_ICNT',0,24,220,7,3
	.word	77108
	.byte	33
	.byte	'Ifx_CPU_ICR',0,24,228,7,3
	.word	30157
	.byte	12,24,231,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70538
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_ISP',0,24,236,7,3
	.word	77191
	.byte	12,24,239,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70604
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_LCX',0,24,244,7,3
	.word	77252
	.byte	12,24,247,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70710
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_M1CNT',0,24,252,7,3
	.word	77313
	.byte	12,24,255,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70803
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_M2CNT',0,24,132,8,3
	.word	77376
	.byte	12,24,135,8,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70896
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_M3CNT',0,24,140,8,3
	.word	77439
	.byte	12,24,143,8,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70989
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_PC',0,24,148,8,3
	.word	77502
	.byte	12,24,151,8,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71074
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_PCON0',0,24,156,8,3
	.word	77562
	.byte	12,24,159,8,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71190
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_PCON1',0,24,164,8,3
	.word	77625
	.byte	12,24,167,8,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71301
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_PCON2',0,24,172,8,3
	.word	77688
	.byte	12,24,175,8,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71402
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_PCXI',0,24,180,8,3
	.word	77751
	.byte	12,24,183,8,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71532
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_PIEAR',0,24,188,8,3
	.word	77813
	.byte	12,24,191,8,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71601
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_PIETR',0,24,196,8,3
	.word	77876
	.byte	12,24,199,8,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71830
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_PMA0',0,24,204,8,3
	.word	77939
	.byte	12,24,207,8,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71943
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_PMA1',0,24,212,8,3
	.word	78001
	.byte	12,24,215,8,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72056
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_PMA2',0,24,220,8,3
	.word	78063
	.byte	12,24,223,8,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72147
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_PSTR',0,24,228,8,3
	.word	78125
	.byte	12,24,231,8,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72350
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_PSW',0,24,236,8,3
	.word	78187
	.byte	12,24,239,8,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72593
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_SEGEN',0,24,244,8,3
	.word	78248
	.byte	12,24,247,8,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72721
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_SMACON',0,24,252,8,3
	.word	78311
	.byte	12,24,255,8,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72962
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_SPROT_ACCENA',0,24,132,9,3
	.word	78375
	.byte	12,24,135,9,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	73045
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_SPROT_ACCENB',0,24,140,9,3
	.word	78445
	.byte	12,24,143,9,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	73136
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA',0,24,148,9,3
	.word	78515
	.byte	12,24,151,9,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	73227
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB',0,24,156,9,3
	.word	78589
	.byte	12,24,159,9,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	73326
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_SPROT_RGN_LA',0,24,164,9,3
	.word	78663
	.byte	12,24,167,9,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	73433
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_SPROT_RGN_UA',0,24,172,9,3
	.word	78733
	.byte	12,24,175,9,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	73540
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_SWEVT',0,24,180,9,3
	.word	78803
	.byte	12,24,183,9,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	73694
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_SYSCON',0,24,188,9,3
	.word	78866
	.byte	12,24,191,9,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	73855
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_TASK_ASI',0,24,196,9,3
	.word	78930
	.byte	12,24,199,9,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	73953
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_TPS_CON',0,24,204,9,3
	.word	78996
	.byte	12,24,207,9,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	74125
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_TPS_TIMER',0,24,212,9,3
	.word	79061
	.byte	12,24,215,9,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	74205
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_TR_ADR',0,24,220,9,3
	.word	79128
	.byte	12,24,223,9,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	74278
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_TR_EVT',0,24,228,9,3
	.word	79192
	.byte	12,24,231,9,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	74596
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_TRIG_ACC',0,24,236,9,3
	.word	79256
	.byte	10
	.byte	'_Ifx_CPU_CPR',0,24,247,9,25,8,13
	.byte	'L',0
	.word	75226
	.byte	4,2,35,0,13
	.byte	'U',0
	.word	75289
	.byte	4,2,35,4,0,14
	.word	79322
	.byte	33
	.byte	'Ifx_CPU_CPR',0,24,251,9,3
	.word	79364
	.byte	10
	.byte	'_Ifx_CPU_DPR',0,24,254,9,25,8,13
	.byte	'L',0
	.word	76250
	.byte	4,2,35,0,13
	.byte	'U',0
	.word	76313
	.byte	4,2,35,4,0,14
	.word	79390
	.byte	33
	.byte	'Ifx_CPU_DPR',0,24,130,10,3
	.word	79432
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN',0,24,133,10,25,16,13
	.byte	'LA',0
	.word	78663
	.byte	4,2,35,0,13
	.byte	'UA',0
	.word	78733
	.byte	4,2,35,4,13
	.byte	'ACCENA',0
	.word	78515
	.byte	4,2,35,8,13
	.byte	'ACCENB',0
	.word	78589
	.byte	4,2,35,12,0,14
	.word	79458
	.byte	33
	.byte	'Ifx_CPU_SPROT_RGN',0,24,139,10,3
	.word	79540
	.byte	18,12
	.word	79061
	.byte	19,2,0,10
	.byte	'_Ifx_CPU_TPS',0,24,142,10,25,16,13
	.byte	'CON',0
	.word	78996
	.byte	4,2,35,0,13
	.byte	'TIMER',0
	.word	79572
	.byte	12,2,35,4,0,14
	.word	79581
	.byte	33
	.byte	'Ifx_CPU_TPS',0,24,146,10,3
	.word	79629
	.byte	10
	.byte	'_Ifx_CPU_TR',0,24,149,10,25,8,13
	.byte	'EVT',0
	.word	79192
	.byte	4,2,35,0,13
	.byte	'ADR',0
	.word	79128
	.byte	4,2,35,4,0,14
	.word	79655
	.byte	33
	.byte	'Ifx_CPU_TR',0,24,153,10,3
	.word	79700
	.byte	18,176,32
	.word	488
	.byte	19,175,32,0,18,208,223,1
	.word	488
	.byte	19,207,223,1,0,18,248,1
	.word	488
	.byte	19,247,1,0,18,244,29
	.word	488
	.byte	19,243,29,0,18,188,3
	.word	488
	.byte	19,187,3,0,18,232,3
	.word	488
	.byte	19,231,3,0,18,252,23
	.word	488
	.byte	19,251,23,0,18,228,63
	.word	488
	.byte	19,227,63,0,18,128,1
	.word	79390
	.byte	19,15,0,14
	.word	79815
	.byte	18,128,31
	.word	488
	.byte	19,255,30,0,18,64
	.word	79322
	.byte	19,7,0,14
	.word	79841
	.byte	18,192,31
	.word	488
	.byte	19,191,31,0,18,16
	.word	75416
	.byte	19,3,0,18,16
	.word	76376
	.byte	19,3,0,18,16
	.word	76438
	.byte	19,3,0,18,208,7
	.word	488
	.byte	19,207,7,0,14
	.word	79581
	.byte	18,240,23
	.word	488
	.byte	19,239,23,0,18,64
	.word	79655
	.byte	19,7,0,14
	.word	79920
	.byte	18,192,23
	.word	488
	.byte	19,191,23,0,18,232,1
	.word	488
	.byte	19,231,1,0,18,180,1
	.word	488
	.byte	19,179,1,0,18,172,1
	.word	488
	.byte	19,171,1,0,18,64
	.word	75605
	.byte	19,15,0,18,64
	.word	488
	.byte	19,63,0,18,64
	.word	74791
	.byte	19,15,0,10
	.byte	'_Ifx_CPU',0,24,166,10,25,128,128,4,13
	.byte	'reserved_0',0
	.word	79725
	.byte	176,32,2,35,0,13
	.byte	'SEGEN',0
	.word	78248
	.byte	4,3,35,176,32,13
	.byte	'reserved_1034',0
	.word	79736
	.byte	208,223,1,3,35,180,32,13
	.byte	'TASK_ASI',0
	.word	78930
	.byte	4,4,35,132,128,2,13
	.byte	'reserved_8008',0
	.word	79749
	.byte	248,1,4,35,136,128,2,13
	.byte	'PMA0',0
	.word	77939
	.byte	4,4,35,128,130,2,13
	.byte	'PMA1',0
	.word	78001
	.byte	4,4,35,132,130,2,13
	.byte	'PMA2',0
	.word	78063
	.byte	4,4,35,136,130,2,13
	.byte	'reserved_810C',0
	.word	79760
	.byte	244,29,4,35,140,130,2,13
	.byte	'DCON2',0
	.word	75876
	.byte	4,4,35,128,160,2,13
	.byte	'reserved_9004',0
	.word	5212
	.byte	8,4,35,132,160,2,13
	.byte	'SMACON',0
	.word	78311
	.byte	4,4,35,140,160,2,13
	.byte	'DSTR',0
	.word	76500
	.byte	4,4,35,144,160,2,13
	.byte	'reserved_9014',0
	.word	3393
	.byte	4,4,35,148,160,2,13
	.byte	'DATR',0
	.word	75664
	.byte	4,4,35,152,160,2,13
	.byte	'DEADD',0
	.word	76000
	.byte	4,4,35,156,160,2,13
	.byte	'DIEAR',0
	.word	76063
	.byte	4,4,35,160,160,2,13
	.byte	'DIETR',0
	.word	76126
	.byte	4,4,35,164,160,2,13
	.byte	'reserved_9028',0
	.word	4583
	.byte	24,4,35,168,160,2,13
	.byte	'DCON0',0
	.word	75813
	.byte	4,4,35,192,160,2,13
	.byte	'reserved_9044',0
	.word	79771
	.byte	188,3,4,35,196,160,2,13
	.byte	'PSTR',0
	.word	78125
	.byte	4,4,35,128,164,2,13
	.byte	'PCON1',0
	.word	77625
	.byte	4,4,35,132,164,2,13
	.byte	'PCON2',0
	.word	77688
	.byte	4,4,35,136,164,2,13
	.byte	'PCON0',0
	.word	77562
	.byte	4,4,35,140,164,2,13
	.byte	'PIEAR',0
	.word	77813
	.byte	4,4,35,144,164,2,13
	.byte	'PIETR',0
	.word	77876
	.byte	4,4,35,148,164,2,13
	.byte	'reserved_9218',0
	.word	79782
	.byte	232,3,4,35,152,164,2,13
	.byte	'COMPAT',0
	.word	75097
	.byte	4,4,35,128,168,2,13
	.byte	'reserved_9404',0
	.word	79793
	.byte	252,23,4,35,132,168,2,13
	.byte	'FPU_TRAP_CON',0
	.word	76686
	.byte	4,4,35,128,192,2,13
	.byte	'FPU_TRAP_PC',0
	.word	76826
	.byte	4,4,35,132,192,2,13
	.byte	'FPU_TRAP_OPC',0
	.word	76756
	.byte	4,4,35,136,192,2,13
	.byte	'reserved_A00C',0
	.word	3393
	.byte	4,4,35,140,192,2,13
	.byte	'FPU_TRAP_SRC1',0
	.word	76895
	.byte	4,4,35,144,192,2,13
	.byte	'FPU_TRAP_SRC2',0
	.word	76966
	.byte	4,4,35,148,192,2,13
	.byte	'FPU_TRAP_SRC3',0
	.word	77037
	.byte	4,4,35,152,192,2,13
	.byte	'reserved_A01C',0
	.word	79804
	.byte	228,63,4,35,156,192,2,13
	.byte	'DPR',0
	.word	79825
	.byte	128,1,4,35,128,128,3,13
	.byte	'reserved_C080',0
	.word	79830
	.byte	128,31,4,35,128,129,3,13
	.byte	'CPR',0
	.word	79850
	.byte	64,4,35,128,160,3,13
	.byte	'reserved_D040',0
	.word	79855
	.byte	192,31,4,35,192,160,3,13
	.byte	'CPXE',0
	.word	79866
	.byte	16,4,35,128,192,3,13
	.byte	'DPRE',0
	.word	79875
	.byte	16,4,35,144,192,3,13
	.byte	'DPWE',0
	.word	79884
	.byte	16,4,35,160,192,3,13
	.byte	'reserved_E030',0
	.word	79893
	.byte	208,7,4,35,176,192,3,13
	.byte	'TPS',0
	.word	79904
	.byte	16,4,35,128,200,3,13
	.byte	'reserved_E410',0
	.word	79909
	.byte	240,23,4,35,144,200,3,13
	.byte	'TR',0
	.word	79929
	.byte	64,4,35,128,224,3,13
	.byte	'reserved_F040',0
	.word	79934
	.byte	192,23,4,35,192,224,3,13
	.byte	'CCTRL',0
	.word	75034
	.byte	4,4,35,128,248,3,13
	.byte	'CCNT',0
	.word	74972
	.byte	4,4,35,132,248,3,13
	.byte	'ICNT',0
	.word	77108
	.byte	4,4,35,136,248,3,13
	.byte	'M1CNT',0
	.word	77313
	.byte	4,4,35,140,248,3,13
	.byte	'M2CNT',0
	.word	77376
	.byte	4,4,35,144,248,3,13
	.byte	'M3CNT',0
	.word	77439
	.byte	4,4,35,148,248,3,13
	.byte	'reserved_FC18',0
	.word	79945
	.byte	232,1,4,35,152,248,3,13
	.byte	'DBGSR',0
	.word	30384
	.byte	4,4,35,128,250,3,13
	.byte	'reserved_FD04',0
	.word	3393
	.byte	4,4,35,132,250,3,13
	.byte	'EXEVT',0
	.word	76562
	.byte	4,4,35,136,250,3,13
	.byte	'CREVT',0
	.word	75478
	.byte	4,4,35,140,250,3,13
	.byte	'SWEVT',0
	.word	78803
	.byte	4,4,35,144,250,3,13
	.byte	'reserved_FD14',0
	.word	64068
	.byte	28,4,35,148,250,3,13
	.byte	'TRIG_ACC',0
	.word	79256
	.byte	4,4,35,176,250,3,13
	.byte	'reserved_FD34',0
	.word	5552
	.byte	12,4,35,180,250,3,13
	.byte	'DMS',0
	.word	76189
	.byte	4,4,35,192,250,3,13
	.byte	'DCX',0
	.word	75939
	.byte	4,4,35,196,250,3,13
	.byte	'DBGTCR',0
	.word	75749
	.byte	4,4,35,200,250,3,13
	.byte	'reserved_FD4C',0
	.word	79956
	.byte	180,1,4,35,204,250,3,13
	.byte	'PCXI',0
	.word	77751
	.byte	4,4,35,128,252,3,13
	.byte	'PSW',0
	.word	78187
	.byte	4,4,35,132,252,3,13
	.byte	'PC',0
	.word	77502
	.byte	4,4,35,136,252,3,13
	.byte	'reserved_FE0C',0
	.word	5212
	.byte	8,4,35,140,252,3,13
	.byte	'SYSCON',0
	.word	78866
	.byte	4,4,35,148,252,3,13
	.byte	'CPU_ID',0
	.word	75352
	.byte	4,4,35,152,252,3,13
	.byte	'CORE_ID',0
	.word	75161
	.byte	4,4,35,156,252,3,13
	.byte	'BIV',0
	.word	74850
	.byte	4,4,35,160,252,3,13
	.byte	'BTV',0
	.word	74911
	.byte	4,4,35,164,252,3,13
	.byte	'ISP',0
	.word	77191
	.byte	4,4,35,168,252,3,13
	.byte	'ICR',0
	.word	30157
	.byte	4,4,35,172,252,3,13
	.byte	'reserved_FE30',0
	.word	5212
	.byte	8,4,35,176,252,3,13
	.byte	'FCX',0
	.word	76625
	.byte	4,4,35,184,252,3,13
	.byte	'LCX',0
	.word	77252
	.byte	4,4,35,188,252,3,13
	.byte	'reserved_FE40',0
	.word	34619
	.byte	16,4,35,192,252,3,13
	.byte	'CUS_ID',0
	.word	75541
	.byte	4,4,35,208,252,3,13
	.byte	'reserved_FE54',0
	.word	79967
	.byte	172,1,4,35,212,252,3,13
	.byte	'D',0
	.word	79978
	.byte	64,4,35,128,254,3,13
	.byte	'reserved_FF40',0
	.word	79987
	.byte	64,4,35,192,254,3,13
	.byte	'A',0
	.word	79996
	.byte	64,4,35,128,255,3,13
	.byte	'reserved_FFC0',0
	.word	79987
	.byte	64,4,35,192,255,3,0,14
	.word	80005
	.byte	33
	.byte	'Ifx_CPU',0,24,130,11,3
	.word	81796
	.byte	15,8,127,9,1,16
	.byte	'IfxCpu_Id_0',0,0,16
	.byte	'IfxCpu_Id_1',0,1,16
	.byte	'IfxCpu_Id_none',0,2,0,33
	.byte	'IfxCpu_Id',0,8,132,1,3
	.word	81818
	.byte	33
	.byte	'IfxCpu_ResourceCpu',0,8,161,1,3
	.word	1834
	.byte	33
	.byte	'Ifx_STM_ACCEN0_Bits',0,15,79,3
	.word	24675
	.byte	33
	.byte	'Ifx_STM_ACCEN1_Bits',0,15,85,3
	.word	24586
	.byte	33
	.byte	'Ifx_STM_CAP_Bits',0,15,91,3
	.word	23116
	.byte	33
	.byte	'Ifx_STM_CAPSV_Bits',0,15,97,3
	.word	23993
	.byte	33
	.byte	'Ifx_STM_CLC_Bits',0,15,107,3
	.word	22239
	.byte	33
	.byte	'Ifx_STM_CMCON_Bits',0,15,120,3
	.word	23294
	.byte	33
	.byte	'Ifx_STM_CMP_Bits',0,15,126,3
	.word	23203
	.byte	33
	.byte	'Ifx_STM_ICR_Bits',0,15,139,1,3
	.word	23525
	.byte	33
	.byte	'Ifx_STM_ID_Bits',0,15,147,1,3
	.word	22395
	.byte	33
	.byte	'Ifx_STM_ISCR_Bits',0,15,157,1,3
	.word	23742
	.byte	33
	.byte	'Ifx_STM_KRST0_Bits',0,15,165,1,3
	.word	24463
	.byte	33
	.byte	'Ifx_STM_KRST1_Bits',0,15,172,1,3
	.word	24359
	.byte	33
	.byte	'Ifx_STM_KRSTCLR_Bits',0,15,179,1,3
	.word	24253
	.byte	33
	.byte	'Ifx_STM_OCS_Bits',0,15,189,1,3
	.word	24093
	.byte	33
	.byte	'Ifx_STM_TIM0_Bits',0,15,195,1,3
	.word	22517
	.byte	33
	.byte	'Ifx_STM_TIM0SV_Bits',0,15,201,1,3
	.word	23906
	.byte	33
	.byte	'Ifx_STM_TIM1_Bits',0,15,207,1,3
	.word	22602
	.byte	33
	.byte	'Ifx_STM_TIM2_Bits',0,15,213,1,3
	.word	22687
	.byte	33
	.byte	'Ifx_STM_TIM3_Bits',0,15,219,1,3
	.word	22772
	.byte	33
	.byte	'Ifx_STM_TIM4_Bits',0,15,225,1,3
	.word	22858
	.byte	33
	.byte	'Ifx_STM_TIM5_Bits',0,15,231,1,3
	.word	22944
	.byte	33
	.byte	'Ifx_STM_TIM6_Bits',0,15,237,1,3
	.word	23030
	.byte	33
	.byte	'Ifx_STM_ACCEN0',0,15,250,1,3
	.word	25204
	.byte	33
	.byte	'Ifx_STM_ACCEN1',0,15,130,2,3
	.word	24635
	.byte	33
	.byte	'Ifx_STM_CAP',0,15,138,2,3
	.word	23163
	.byte	33
	.byte	'Ifx_STM_CAPSV',0,15,146,2,3
	.word	24042
	.byte	33
	.byte	'Ifx_STM_CLC',0,15,154,2,3
	.word	22355
	.byte	33
	.byte	'Ifx_STM_CMCON',0,15,162,2,3
	.word	23485
	.byte	33
	.byte	'Ifx_STM_CMP',0,15,170,2,3
	.word	23245
	.byte	33
	.byte	'Ifx_STM_ICR',0,15,178,2,3
	.word	23702
	.byte	33
	.byte	'Ifx_STM_ID',0,15,186,2,3
	.word	22477
	.byte	33
	.byte	'Ifx_STM_ISCR',0,15,194,2,3
	.word	23866
	.byte	33
	.byte	'Ifx_STM_KRST0',0,15,202,2,3
	.word	24546
	.byte	33
	.byte	'Ifx_STM_KRST1',0,15,210,2,3
	.word	24423
	.byte	33
	.byte	'Ifx_STM_KRSTCLR',0,15,218,2,3
	.word	24319
	.byte	33
	.byte	'Ifx_STM_OCS',0,15,226,2,3
	.word	24213
	.byte	33
	.byte	'Ifx_STM_TIM0',0,15,234,2,3
	.word	22562
	.byte	33
	.byte	'Ifx_STM_TIM0SV',0,15,242,2,3
	.word	23953
	.byte	33
	.byte	'Ifx_STM_TIM1',0,15,250,2,3
	.word	22647
	.byte	33
	.byte	'Ifx_STM_TIM2',0,15,130,3,3
	.word	22732
	.byte	33
	.byte	'Ifx_STM_TIM3',0,15,138,3,3
	.word	22818
	.byte	33
	.byte	'Ifx_STM_TIM4',0,15,146,3,3
	.word	22904
	.byte	33
	.byte	'Ifx_STM_TIM5',0,15,154,3,3
	.word	22990
	.byte	33
	.byte	'Ifx_STM_TIM6',0,15,162,3,3
	.word	23076
	.byte	14
	.word	25244
	.byte	33
	.byte	'Ifx_STM',0,15,201,3,3
	.word	82999
	.byte	15,7,144,1,9,1,16
	.byte	'IfxCpu_CounterMode_normal',0,0,16
	.byte	'IfxCpu_CounterMode_task',0,1,0,33
	.byte	'IfxCpu_CounterMode',0,7,148,1,3
	.word	83021
	.byte	30,7,160,1,9,6,13
	.byte	'counter',0
	.word	2156
	.byte	4,2,35,0,13
	.byte	'overlfow',0
	.word	488
	.byte	1,2,35,4,0,33
	.byte	'IfxCpu_Counter',0,7,164,1,3
	.word	83110
	.byte	30,7,172,1,9,32,13
	.byte	'instruction',0
	.word	83110
	.byte	6,2,35,0,13
	.byte	'clock',0
	.word	83110
	.byte	6,2,35,6,13
	.byte	'counter1',0
	.word	83110
	.byte	6,2,35,12,13
	.byte	'counter2',0
	.word	83110
	.byte	6,2,35,18,13
	.byte	'counter3',0
	.word	83110
	.byte	6,2,35,24,0,33
	.byte	'IfxCpu_Perf',0,7,179,1,3
	.word	83176
	.byte	10
	.byte	'_Ifx_FLASH_ACCEN0_Bits',0,28,45,16,4,11
	.byte	'EN0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	488
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	488
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	488
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	488
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	488
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	488
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	488
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	488
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	488
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	488
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	488
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	488
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	488
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	488
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_FLASH_ACCEN0_Bits',0,28,79,3
	.word	83294
	.byte	10
	.byte	'_Ifx_FLASH_ACCEN1_Bits',0,28,82,16,4,11
	.byte	'reserved_0',0,4
	.word	465
	.byte	32,0,2,35,0,0,33
	.byte	'Ifx_FLASH_ACCEN1_Bits',0,28,85,3
	.word	83855
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_CFG_Bits',0,28,88,16,4,11
	.byte	'SEL',0,1
	.word	488
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	488
	.byte	2,0,2,35,0,11
	.byte	'CLR',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'DIS',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	465
	.byte	22,0,2,35,0,0,33
	.byte	'Ifx_FLASH_CBAB_CFG_Bits',0,28,95,3
	.word	83936
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_STAT_Bits',0,28,98,16,4,11
	.byte	'VLD0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'VLD1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'VLD2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'VLD3',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'VLD4',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'VLD5',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'VLD6',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'VLD7',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'VLD8',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'VLD9',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	465
	.byte	22,0,2,35,0,0,33
	.byte	'Ifx_FLASH_CBAB_STAT_Bits',0,28,111,3
	.word	84089
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_TOP_Bits',0,28,114,16,4,11
	.byte	'reserved_0',0,1
	.word	488
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	465
	.byte	19,8,2,35,0,11
	.byte	'ERR',0,1
	.word	488
	.byte	6,2,2,35,3,11
	.byte	'VLD',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'CLR',0,1
	.word	488
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_FLASH_CBAB_TOP_Bits',0,28,121,3
	.word	84337
	.byte	10
	.byte	'_Ifx_FLASH_COMM0_Bits',0,28,124,16,4,11
	.byte	'STATUS',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	465
	.byte	24,0,2,35,0,0,33
	.byte	'Ifx_FLASH_COMM0_Bits',0,28,128,1,3
	.word	84483
	.byte	10
	.byte	'_Ifx_FLASH_COMM1_Bits',0,28,131,1,16,4,11
	.byte	'STATUS',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'DATA',0,1
	.word	488
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,33
	.byte	'Ifx_FLASH_COMM1_Bits',0,28,136,1,3
	.word	84581
	.byte	10
	.byte	'_Ifx_FLASH_COMM2_Bits',0,28,139,1,16,4,11
	.byte	'STATUS',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'DATA',0,1
	.word	488
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,33
	.byte	'Ifx_FLASH_COMM2_Bits',0,28,144,1,3
	.word	84697
	.byte	10
	.byte	'_Ifx_FLASH_ECCRD_Bits',0,28,147,1,16,4,11
	.byte	'RCODE',0,4
	.word	465
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	1039
	.byte	8,2,2,35,2,11
	.byte	'EDCERRINJ',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'ECCORDIS',0,1
	.word	488
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_FLASH_ECCRD_Bits',0,28,153,1,3
	.word	84813
	.byte	10
	.byte	'_Ifx_FLASH_ECCRP_Bits',0,28,156,1,16,4,11
	.byte	'RCODE',0,4
	.word	465
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	1039
	.byte	8,2,2,35,2,11
	.byte	'EDCERRINJ',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'ECCORDIS',0,1
	.word	488
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_FLASH_ECCRP_Bits',0,28,162,1,3
	.word	84953
	.byte	10
	.byte	'_Ifx_FLASH_ECCW_Bits',0,28,165,1,16,4,11
	.byte	'WCODE',0,4
	.word	465
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	1039
	.byte	8,2,2,35,2,11
	.byte	'DECENCDIS',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'PECENCDIS',0,1
	.word	488
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_FLASH_ECCW_Bits',0,28,171,1,3
	.word	85093
	.byte	10
	.byte	'_Ifx_FLASH_FCON_Bits',0,28,174,1,16,4,11
	.byte	'WSPFLASH',0,1
	.word	488
	.byte	4,4,2,35,0,11
	.byte	'WSECPF',0,1
	.word	488
	.byte	2,2,2,35,0,11
	.byte	'WSDFLASH',0,2
	.word	1039
	.byte	6,4,2,35,0,11
	.byte	'WSECDF',0,1
	.word	488
	.byte	3,1,2,35,1,11
	.byte	'IDLE',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'ESLDIS',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'SLEEP',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'NSAFECC',0,1
	.word	488
	.byte	1,5,2,35,2,11
	.byte	'STALL',0,1
	.word	488
	.byte	1,4,2,35,2,11
	.byte	'RES21',0,1
	.word	488
	.byte	2,2,2,35,2,11
	.byte	'RES23',0,1
	.word	488
	.byte	2,0,2,35,2,11
	.byte	'VOPERM',0,1
	.word	488
	.byte	1,7,2,35,3,11
	.byte	'SQERM',0,1
	.word	488
	.byte	1,6,2,35,3,11
	.byte	'PROERM',0,1
	.word	488
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	488
	.byte	3,2,2,35,3,11
	.byte	'PR5V',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'EOBM',0,1
	.word	488
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_FLASH_FCON_Bits',0,28,193,1,3
	.word	85232
	.byte	10
	.byte	'_Ifx_FLASH_FPRO_Bits',0,28,196,1,16,4,11
	.byte	'PROINP',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'PRODISP',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'PROIND',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'PRODISD',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'PROINHSMCOTP',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'RES5',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'PROINOTP',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'RES7',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'PROINDBG',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'PRODISDBG',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'PROINHSM',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	488
	.byte	5,0,2,35,1,11
	.byte	'DCFP',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'DDFP',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'DDFPX',0,1
	.word	488
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	488
	.byte	1,4,2,35,2,11
	.byte	'DDFD',0,1
	.word	488
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	488
	.byte	1,2,2,35,2,11
	.byte	'ENPE',0,1
	.word	488
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	488
	.byte	8,0,2,35,3,0,33
	.byte	'Ifx_FLASH_FPRO_Bits',0,28,218,1,3
	.word	85594
	.byte	10
	.byte	'_Ifx_FLASH_FSR_Bits',0,28,221,1,16,4,11
	.byte	'FABUSY',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'D0BUSY',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'RES1',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'P0BUSY',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'P1BUSY',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'RES5',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'RES6',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'PROG',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'ERASE',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'PFPAGE',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'DFPAGE',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'OPER',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'SQER',0,1
	.word	488
	.byte	1,3,2,35,1,11
	.byte	'PROER',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'PFSBER',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'PFDBER',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'PFMBER',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'RES17',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'DFSBER',0,1
	.word	488
	.byte	1,5,2,35,2,11
	.byte	'DFDBER',0,1
	.word	488
	.byte	1,4,2,35,2,11
	.byte	'DFTBER',0,1
	.word	488
	.byte	1,3,2,35,2,11
	.byte	'DFMBER',0,1
	.word	488
	.byte	1,2,2,35,2,11
	.byte	'SRIADDERR',0,1
	.word	488
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	1039
	.byte	2,7,2,35,2,11
	.byte	'PVER',0,1
	.word	488
	.byte	1,6,2,35,3,11
	.byte	'EVER',0,1
	.word	488
	.byte	1,5,2,35,3,11
	.byte	'SPND',0,1
	.word	488
	.byte	1,4,2,35,3,11
	.byte	'SLM',0,1
	.word	488
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	488
	.byte	1,2,2,35,3,11
	.byte	'ORIER',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	488
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_FLASH_FSR_Bits',0,28,254,1,3
	.word	86035
	.byte	10
	.byte	'_Ifx_FLASH_ID_Bits',0,28,129,2,16,4,11
	.byte	'MODREV',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	488
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	1039
	.byte	16,0,2,35,2,0,33
	.byte	'Ifx_FLASH_ID_Bits',0,28,134,2,3
	.word	86641
	.byte	10
	.byte	'_Ifx_FLASH_MARD_Bits',0,28,137,2,16,4,11
	.byte	'HMARGIN',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'SELD0',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'SPND',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'SPNDERR',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,2
	.word	1039
	.byte	10,1,2,35,0,11
	.byte	'TRAPDIS',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,33
	.byte	'Ifx_FLASH_MARD_Bits',0,28,147,2,3
	.word	86752
	.byte	10
	.byte	'_Ifx_FLASH_MARP_Bits',0,28,150,2,16,4,11
	.byte	'SELP0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'SELP1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'RES2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'RES3',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	1039
	.byte	11,1,2,35,0,11
	.byte	'TRAPDIS',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,33
	.byte	'Ifx_FLASH_MARP_Bits',0,28,159,2,3
	.word	86966
	.byte	10
	.byte	'_Ifx_FLASH_PROCOND_Bits',0,28,162,2,16,4,11
	.byte	'L',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'NSAFECC',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'RAMIN',0,1
	.word	488
	.byte	2,4,2,35,0,11
	.byte	'RAMINSEL',0,1
	.word	488
	.byte	4,0,2,35,0,11
	.byte	'OSCCFG',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'MODE',0,1
	.word	488
	.byte	2,5,2,35,1,11
	.byte	'APREN',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'CAP0EN',0,1
	.word	488
	.byte	1,3,2,35,1,11
	.byte	'CAP1EN',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'CAP2EN',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'CAP3EN',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'ESR0CNT',0,2
	.word	1039
	.byte	12,4,2,35,2,11
	.byte	'RES29',0,1
	.word	488
	.byte	2,2,2,35,3,11
	.byte	'RES30',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'RPRO',0,1
	.word	488
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_FLASH_PROCOND_Bits',0,28,179,2,3
	.word	87153
	.byte	10
	.byte	'_Ifx_FLASH_PROCONDBG_Bits',0,28,182,2,16,4,11
	.byte	'OCDSDIS',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'DBGIFLCK',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'EDM',0,1
	.word	488
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	465
	.byte	28,0,2,35,0,0,33
	.byte	'Ifx_FLASH_PROCONDBG_Bits',0,28,188,2,3
	.word	87477
	.byte	10
	.byte	'_Ifx_FLASH_PROCONHSM_Bits',0,28,191,2,16,4,11
	.byte	'HSMDBGDIS',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'DBGIFLCK',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'TSTIFLCK',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'HSMTSTDIS',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'RES15',0,2
	.word	1039
	.byte	12,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,33
	.byte	'Ifx_FLASH_PROCONHSM_Bits',0,28,199,2,3
	.word	87620
	.byte	10
	.byte	'_Ifx_FLASH_PROCONHSMCOTP_Bits',0,28,202,2,16,4,11
	.byte	'HSMBOOTEN',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'SSWWAIT',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'HSMDX',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'HSM6X',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'HSM16X',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'HSM17X',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'S6ROM',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'HSMENPINS',0,2
	.word	1039
	.byte	2,7,2,35,0,11
	.byte	'HSMENRES',0,1
	.word	488
	.byte	2,5,2,35,1,11
	.byte	'DESTDBG',0,1
	.word	488
	.byte	2,3,2,35,1,11
	.byte	'BLKFLAN',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	488
	.byte	2,0,2,35,1,11
	.byte	'S16ROM',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'S17ROM',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	1039
	.byte	14,0,2,35,2,0,33
	.byte	'Ifx_FLASH_PROCONHSMCOTP_Bits',0,28,219,2,3
	.word	87809
	.byte	10
	.byte	'_Ifx_FLASH_PROCONOTP_Bits',0,28,222,2,16,4,11
	.byte	'S0ROM',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'S1ROM',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'S2ROM',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'S3ROM',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'S4ROM',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'S5ROM',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'S6ROM',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'S7ROM',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'S8ROM',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'S9ROM',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'S10ROM',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'S11ROM',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'S12ROM',0,1
	.word	488
	.byte	1,3,2,35,1,11
	.byte	'S13ROM',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'S14ROM',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'S15ROM',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'S16ROM',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'S17ROM',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'S18ROM',0,1
	.word	488
	.byte	1,5,2,35,2,11
	.byte	'S19ROM',0,1
	.word	488
	.byte	1,4,2,35,2,11
	.byte	'S20ROM',0,1
	.word	488
	.byte	1,3,2,35,2,11
	.byte	'S21ROM',0,1
	.word	488
	.byte	1,2,2,35,2,11
	.byte	'S22ROM',0,1
	.word	488
	.byte	1,1,2,35,2,11
	.byte	'S23ROM',0,1
	.word	488
	.byte	1,0,2,35,2,11
	.byte	'S24ROM',0,1
	.word	488
	.byte	1,7,2,35,3,11
	.byte	'S25ROM',0,1
	.word	488
	.byte	1,6,2,35,3,11
	.byte	'S26ROM',0,1
	.word	488
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	488
	.byte	2,3,2,35,3,11
	.byte	'BML',0,1
	.word	488
	.byte	2,1,2,35,3,11
	.byte	'TP',0,1
	.word	488
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_FLASH_PROCONOTP_Bits',0,28,254,2,3
	.word	88172
	.byte	10
	.byte	'_Ifx_FLASH_PROCONP_Bits',0,28,129,3,16,4,11
	.byte	'S0L',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'S1L',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'S2L',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'S3L',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'S4L',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'S5L',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'S6L',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'S7L',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'S8L',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'S9L',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'S10L',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'S11L',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'S12L',0,1
	.word	488
	.byte	1,3,2,35,1,11
	.byte	'S13L',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'S14L',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'S15L',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'S16L',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'S17L',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'S18L',0,1
	.word	488
	.byte	1,5,2,35,2,11
	.byte	'S19L',0,1
	.word	488
	.byte	1,4,2,35,2,11
	.byte	'S20L',0,1
	.word	488
	.byte	1,3,2,35,2,11
	.byte	'S21L',0,1
	.word	488
	.byte	1,2,2,35,2,11
	.byte	'S22L',0,1
	.word	488
	.byte	1,1,2,35,2,11
	.byte	'S23L',0,1
	.word	488
	.byte	1,0,2,35,2,11
	.byte	'S24L',0,1
	.word	488
	.byte	1,7,2,35,3,11
	.byte	'S25L',0,1
	.word	488
	.byte	1,6,2,35,3,11
	.byte	'S26L',0,1
	.word	488
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	488
	.byte	4,1,2,35,3,11
	.byte	'RPRO',0,1
	.word	488
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_FLASH_PROCONP_Bits',0,28,160,3,3
	.word	88767
	.byte	10
	.byte	'_Ifx_FLASH_PROCONWOP_Bits',0,28,163,3,16,4,11
	.byte	'S0WOP',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'S1WOP',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'S2WOP',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'S3WOP',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'S4WOP',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'S5WOP',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'S6WOP',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'S7WOP',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'S8WOP',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'S9WOP',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'S10WOP',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'S11WOP',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'S12WOP',0,1
	.word	488
	.byte	1,3,2,35,1,11
	.byte	'S13WOP',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'S14WOP',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'S15WOP',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'S16WOP',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'S17WOP',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'S18WOP',0,1
	.word	488
	.byte	1,5,2,35,2,11
	.byte	'S19WOP',0,1
	.word	488
	.byte	1,4,2,35,2,11
	.byte	'S20WOP',0,1
	.word	488
	.byte	1,3,2,35,2,11
	.byte	'S21WOP',0,1
	.word	488
	.byte	1,2,2,35,2,11
	.byte	'S22WOP',0,1
	.word	488
	.byte	1,1,2,35,2,11
	.byte	'S23WOP',0,1
	.word	488
	.byte	1,0,2,35,2,11
	.byte	'S24WOP',0,1
	.word	488
	.byte	1,7,2,35,3,11
	.byte	'S25WOP',0,1
	.word	488
	.byte	1,6,2,35,3,11
	.byte	'S26WOP',0,1
	.word	488
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	488
	.byte	4,1,2,35,3,11
	.byte	'DATM',0,1
	.word	488
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_FLASH_PROCONWOP_Bits',0,28,194,3,3
	.word	89291
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG0_Bits',0,28,197,3,16,4,11
	.byte	'TAG',0,1
	.word	488
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	465
	.byte	26,0,2,35,0,0,33
	.byte	'Ifx_FLASH_RDB_CFG0_Bits',0,28,201,3,3
	.word	89873
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG1_Bits',0,28,204,3,16,4,11
	.byte	'TAG',0,1
	.word	488
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	465
	.byte	26,0,2,35,0,0,33
	.byte	'Ifx_FLASH_RDB_CFG1_Bits',0,28,208,3,3
	.word	89975
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG2_Bits',0,28,211,3,16,4,11
	.byte	'TAG',0,1
	.word	488
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	465
	.byte	26,0,2,35,0,0,33
	.byte	'Ifx_FLASH_RDB_CFG2_Bits',0,28,215,3,3
	.word	90077
	.byte	10
	.byte	'_Ifx_FLASH_RRAD_Bits',0,28,218,3,16,4,11
	.byte	'reserved_0',0,1
	.word	488
	.byte	3,5,2,35,0,11
	.byte	'ADD',0,4
	.word	465
	.byte	29,0,2,35,0,0,33
	.byte	'Ifx_FLASH_RRAD_Bits',0,28,222,3,3
	.word	90179
	.byte	10
	.byte	'_Ifx_FLASH_RRCT_Bits',0,28,225,3,16,4,11
	.byte	'STRT',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'STP',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'BUSY',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'DONE',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'ERR',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	488
	.byte	3,0,2,35,0,11
	.byte	'EOBM',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	488
	.byte	7,0,2,35,1,11
	.byte	'CNT',0,2
	.word	1039
	.byte	16,0,2,35,2,0,33
	.byte	'Ifx_FLASH_RRCT_Bits',0,28,236,3,3
	.word	90273
	.byte	10
	.byte	'_Ifx_FLASH_RRD0_Bits',0,28,239,3,16,4,11
	.byte	'DATA',0,4
	.word	465
	.byte	32,0,2,35,0,0,33
	.byte	'Ifx_FLASH_RRD0_Bits',0,28,242,3,3
	.word	90483
	.byte	10
	.byte	'_Ifx_FLASH_RRD1_Bits',0,28,245,3,16,4,11
	.byte	'DATA',0,4
	.word	465
	.byte	32,0,2,35,0,0,33
	.byte	'Ifx_FLASH_RRD1_Bits',0,28,248,3,3
	.word	90556
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_CFG_Bits',0,28,251,3,16,4,11
	.byte	'SEL',0,1
	.word	488
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	488
	.byte	2,0,2,35,0,11
	.byte	'CLR',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'DIS',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	465
	.byte	22,0,2,35,0,0,33
	.byte	'Ifx_FLASH_UBAB_CFG_Bits',0,28,130,4,3
	.word	90629
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_STAT_Bits',0,28,133,4,16,4,11
	.byte	'VLD0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	465
	.byte	31,0,2,35,0,0,33
	.byte	'Ifx_FLASH_UBAB_STAT_Bits',0,28,137,4,3
	.word	90784
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_TOP_Bits',0,28,140,4,16,4,11
	.byte	'reserved_0',0,1
	.word	488
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	465
	.byte	19,8,2,35,0,11
	.byte	'ERR',0,1
	.word	488
	.byte	6,2,2,35,3,11
	.byte	'VLD',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'CLR',0,1
	.word	488
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_FLASH_UBAB_TOP_Bits',0,28,147,4,3
	.word	90889
	.byte	12,28,155,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	83294
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_ACCEN0',0,28,160,4,3
	.word	91037
	.byte	12,28,163,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	83855
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_ACCEN1',0,28,168,4,3
	.word	91103
	.byte	12,28,171,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	83936
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_CBAB_CFG',0,28,176,4,3
	.word	91169
	.byte	12,28,179,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	84089
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_CBAB_STAT',0,28,184,4,3
	.word	91237
	.byte	12,28,187,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	84337
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_CBAB_TOP',0,28,192,4,3
	.word	91306
	.byte	12,28,195,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	84483
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_COMM0',0,28,200,4,3
	.word	91374
	.byte	12,28,203,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	84581
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_COMM1',0,28,208,4,3
	.word	91439
	.byte	12,28,211,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	84697
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_COMM2',0,28,216,4,3
	.word	91504
	.byte	12,28,219,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	84813
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_ECCRD',0,28,224,4,3
	.word	91569
	.byte	12,28,227,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	84953
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_ECCRP',0,28,232,4,3
	.word	91634
	.byte	12,28,235,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	85093
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_ECCW',0,28,240,4,3
	.word	91699
	.byte	12,28,243,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	85232
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_FCON',0,28,248,4,3
	.word	91763
	.byte	12,28,251,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	85594
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_FPRO',0,28,128,5,3
	.word	91827
	.byte	12,28,131,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	86035
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_FSR',0,28,136,5,3
	.word	91891
	.byte	12,28,139,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	86641
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_ID',0,28,144,5,3
	.word	91954
	.byte	12,28,147,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	86752
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_MARD',0,28,152,5,3
	.word	92016
	.byte	12,28,155,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	86966
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_MARP',0,28,160,5,3
	.word	92080
	.byte	12,28,163,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	87153
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_PROCOND',0,28,168,5,3
	.word	92144
	.byte	12,28,171,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	87477
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_PROCONDBG',0,28,176,5,3
	.word	92211
	.byte	12,28,179,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	87620
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_PROCONHSM',0,28,184,5,3
	.word	92280
	.byte	12,28,187,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	87809
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_PROCONHSMCOTP',0,28,192,5,3
	.word	92349
	.byte	12,28,195,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	88172
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_PROCONOTP',0,28,200,5,3
	.word	92422
	.byte	12,28,203,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	88767
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_PROCONP',0,28,208,5,3
	.word	92491
	.byte	12,28,211,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	89291
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_PROCONWOP',0,28,216,5,3
	.word	92558
	.byte	12,28,219,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	89873
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_RDB_CFG0',0,28,224,5,3
	.word	92627
	.byte	12,28,227,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	89975
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_RDB_CFG1',0,28,232,5,3
	.word	92695
	.byte	12,28,235,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	90077
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_RDB_CFG2',0,28,240,5,3
	.word	92763
	.byte	12,28,243,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	90179
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_RRAD',0,28,248,5,3
	.word	92831
	.byte	12,28,251,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	90273
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_RRCT',0,28,128,6,3
	.word	92895
	.byte	12,28,131,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	90483
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_RRD0',0,28,136,6,3
	.word	92959
	.byte	12,28,139,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	90556
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_RRD1',0,28,144,6,3
	.word	93023
	.byte	12,28,147,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	90629
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_UBAB_CFG',0,28,152,6,3
	.word	93087
	.byte	12,28,155,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	90784
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_UBAB_STAT',0,28,160,6,3
	.word	93155
	.byte	12,28,163,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	90889
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_UBAB_TOP',0,28,168,6,3
	.word	93224
	.byte	10
	.byte	'_Ifx_FLASH_CBAB',0,28,179,6,25,12,13
	.byte	'CFG',0
	.word	91169
	.byte	4,2,35,0,13
	.byte	'STAT',0
	.word	91237
	.byte	4,2,35,4,13
	.byte	'TOP',0
	.word	91306
	.byte	4,2,35,8,0,14
	.word	93292
	.byte	33
	.byte	'Ifx_FLASH_CBAB',0,28,184,6,3
	.word	93355
	.byte	10
	.byte	'_Ifx_FLASH_RDB',0,28,187,6,25,12,13
	.byte	'CFG0',0
	.word	92627
	.byte	4,2,35,0,13
	.byte	'CFG1',0
	.word	92695
	.byte	4,2,35,4,13
	.byte	'CFG2',0
	.word	92763
	.byte	4,2,35,8,0,14
	.word	93384
	.byte	33
	.byte	'Ifx_FLASH_RDB',0,28,192,6,3
	.word	93448
	.byte	10
	.byte	'_Ifx_FLASH_UBAB',0,28,195,6,25,12,13
	.byte	'CFG',0
	.word	93087
	.byte	4,2,35,0,13
	.byte	'STAT',0
	.word	93155
	.byte	4,2,35,4,13
	.byte	'TOP',0
	.word	93224
	.byte	4,2,35,8,0,14
	.word	93476
	.byte	33
	.byte	'Ifx_FLASH_UBAB',0,28,200,6,3
	.word	93539
	.byte	33
	.byte	'Ifx_P_ACCEN0_Bits',0,10,79,3
	.word	8965
	.byte	33
	.byte	'Ifx_P_ACCEN1_Bits',0,10,85,3
	.word	8878
	.byte	33
	.byte	'Ifx_P_ESR_Bits',0,10,107,3
	.word	5221
	.byte	33
	.byte	'Ifx_P_ID_Bits',0,10,115,3
	.word	3274
	.byte	33
	.byte	'Ifx_P_IN_Bits',0,10,137,1,3
	.word	4269
	.byte	33
	.byte	'Ifx_P_IOCR0_Bits',0,10,150,1,3
	.word	3402
	.byte	33
	.byte	'Ifx_P_IOCR12_Bits',0,10,163,1,3
	.word	4049
	.byte	33
	.byte	'Ifx_P_IOCR4_Bits',0,10,176,1,3
	.word	3617
	.byte	33
	.byte	'Ifx_P_IOCR8_Bits',0,10,189,1,3
	.word	3832
	.byte	33
	.byte	'Ifx_P_LPCR0_Bits',0,10,197,1,3
	.word	8237
	.byte	33
	.byte	'Ifx_P_LPCR1_Bits',0,10,205,1,3
	.word	8361
	.byte	33
	.byte	'Ifx_P_LPCR1_P21_Bits',0,10,215,1,3
	.word	8445
	.byte	33
	.byte	'Ifx_P_LPCR2_Bits',0,10,229,1,3
	.word	8625
	.byte	33
	.byte	'Ifx_P_OMCR0_Bits',0,10,240,1,3
	.word	6876
	.byte	33
	.byte	'Ifx_P_OMCR12_Bits',0,10,250,1,3
	.word	7400
	.byte	33
	.byte	'Ifx_P_OMCR4_Bits',0,10,133,2,3
	.word	7050
	.byte	33
	.byte	'Ifx_P_OMCR8_Bits',0,10,144,2,3
	.word	7224
	.byte	33
	.byte	'Ifx_P_OMCR_Bits',0,10,166,2,3
	.word	7889
	.byte	33
	.byte	'Ifx_P_OMR_Bits',0,10,203,2,3
	.word	2703
	.byte	33
	.byte	'Ifx_P_OMSR0_Bits',0,10,213,2,3
	.word	6213
	.byte	33
	.byte	'Ifx_P_OMSR12_Bits',0,10,224,2,3
	.word	6701
	.byte	33
	.byte	'Ifx_P_OMSR4_Bits',0,10,235,2,3
	.word	6360
	.byte	33
	.byte	'Ifx_P_OMSR8_Bits',0,10,246,2,3
	.word	6529
	.byte	33
	.byte	'Ifx_P_OMSR_Bits',0,10,140,3,3
	.word	7556
	.byte	33
	.byte	'Ifx_P_OUT_Bits',0,10,162,3,3
	.word	2387
	.byte	33
	.byte	'Ifx_P_PCSR_Bits',0,10,180,3,3
	.word	5927
	.byte	33
	.byte	'Ifx_P_PDISC_Bits',0,10,202,3,3
	.word	5561
	.byte	33
	.byte	'Ifx_P_PDR0_Bits',0,10,223,3,3
	.word	4592
	.byte	33
	.byte	'Ifx_P_PDR1_Bits',0,10,244,3,3
	.word	4896
	.byte	33
	.byte	'Ifx_P_ACCEN0',0,10,129,4,3
	.word	9492
	.byte	33
	.byte	'Ifx_P_ACCEN1',0,10,137,4,3
	.word	8925
	.byte	33
	.byte	'Ifx_P_ESR',0,10,145,4,3
	.word	5512
	.byte	33
	.byte	'Ifx_P_ID',0,10,153,4,3
	.word	3353
	.byte	33
	.byte	'Ifx_P_IN',0,10,161,4,3
	.word	4543
	.byte	33
	.byte	'Ifx_P_IOCR0',0,10,169,4,3
	.word	3577
	.byte	33
	.byte	'Ifx_P_IOCR12',0,10,177,4,3
	.word	4229
	.byte	33
	.byte	'Ifx_P_IOCR4',0,10,185,4,3
	.word	3792
	.byte	33
	.byte	'Ifx_P_IOCR8',0,10,193,4,3
	.word	4009
	.byte	33
	.byte	'Ifx_P_LPCR0',0,10,201,4,3
	.word	8321
	.byte	33
	.byte	'Ifx_P_LPCR1',0,10,210,4,3
	.word	8570
	.byte	33
	.byte	'Ifx_P_LPCR2',0,10,218,4,3
	.word	8829
	.byte	33
	.byte	'Ifx_P_OMCR',0,10,226,4,3
	.word	8197
	.byte	33
	.byte	'Ifx_P_OMCR0',0,10,234,4,3
	.word	7010
	.byte	33
	.byte	'Ifx_P_OMCR12',0,10,242,4,3
	.word	7516
	.byte	33
	.byte	'Ifx_P_OMCR4',0,10,250,4,3
	.word	7184
	.byte	33
	.byte	'Ifx_P_OMCR8',0,10,130,5,3
	.word	7360
	.byte	33
	.byte	'Ifx_P_OMR',0,10,138,5,3
	.word	3234
	.byte	33
	.byte	'Ifx_P_OMSR',0,10,146,5,3
	.word	7849
	.byte	33
	.byte	'Ifx_P_OMSR0',0,10,154,5,3
	.word	6320
	.byte	33
	.byte	'Ifx_P_OMSR12',0,10,162,5,3
	.word	6836
	.byte	33
	.byte	'Ifx_P_OMSR4',0,10,170,5,3
	.word	6489
	.byte	33
	.byte	'Ifx_P_OMSR8',0,10,178,5,3
	.word	6661
	.byte	33
	.byte	'Ifx_P_OUT',0,10,186,5,3
	.word	2663
	.byte	33
	.byte	'Ifx_P_PCSR',0,10,194,5,3
	.word	6173
	.byte	33
	.byte	'Ifx_P_PDISC',0,10,202,5,3
	.word	5887
	.byte	33
	.byte	'Ifx_P_PDR0',0,10,210,5,3
	.word	4856
	.byte	33
	.byte	'Ifx_P_PDR1',0,10,218,5,3
	.word	5172
	.byte	14
	.word	9532
	.byte	33
	.byte	'Ifx_P',0,10,139,6,3
	.word	94886
	.byte	33
	.byte	'IfxPort_InputMode',0,9,89,3
	.word	10145
	.byte	33
	.byte	'IfxPort_OutputIdx',0,9,130,1,3
	.word	10420
	.byte	33
	.byte	'IfxPort_OutputMode',0,9,138,1,3
	.word	10350
	.byte	15,9,144,1,9,1,16
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed1',0,0,16
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed2',0,1,16
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed3',0,2,16
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed4',0,3,16
	.byte	'IfxPort_PadDriver_lvdsSpeed1',0,4,16
	.byte	'IfxPort_PadDriver_lvdsSpeed2',0,5,16
	.byte	'IfxPort_PadDriver_lvdsSpeed3',0,6,16
	.byte	'IfxPort_PadDriver_lvdsSpeed4',0,7,16
	.byte	'IfxPort_PadDriver_ttlSpeed1',0,8,16
	.byte	'IfxPort_PadDriver_ttlSpeed2',0,9,16
	.byte	'IfxPort_PadDriver_ttlSpeed3',0,10,16
	.byte	'IfxPort_PadDriver_ttlSpeed4',0,11,0,33
	.byte	'IfxPort_PadDriver',0,9,158,1,3
	.word	94987
	.byte	33
	.byte	'IfxPort_State',0,9,178,1,3
	.word	10733
	.byte	33
	.byte	'IfxPort_Pin',0,9,194,1,3
	.word	26837
	.byte	33
	.byte	'IfxScuCcu_PllStepsFunctionHook',0,11,148,1,16
	.word	204
	.byte	30,11,212,5,9,8,13
	.byte	'value',0
	.word	2156
	.byte	4,2,35,0,13
	.byte	'mask',0
	.word	2156
	.byte	4,2,35,4,0,33
	.byte	'IfxScuCcu_CcuconRegConfig',0,11,216,5,3
	.word	95513
	.byte	30,11,221,5,9,8,13
	.byte	'pDivider',0
	.word	488
	.byte	1,2,35,0,13
	.byte	'nDivider',0
	.word	488
	.byte	1,2,35,1,13
	.byte	'k2Initial',0
	.word	488
	.byte	1,2,35,2,13
	.byte	'waitTime',0
	.word	261
	.byte	4,2,35,4,0,33
	.byte	'IfxScuCcu_InitialStepConfig',0,11,227,5,3
	.word	95584
	.byte	30,11,231,5,9,12,13
	.byte	'k2Step',0
	.word	488
	.byte	1,2,35,0,13
	.byte	'waitTime',0
	.word	261
	.byte	4,2,35,2,13
	.byte	'hookFunction',0
	.word	95473
	.byte	4,2,35,8,0,33
	.byte	'IfxScuCcu_PllStepsConfig',0,11,236,5,3
	.word	95701
	.byte	3
	.word	201
	.byte	30,11,244,5,9,48,13
	.byte	'ccucon0',0
	.word	95513
	.byte	8,2,35,0,13
	.byte	'ccucon1',0
	.word	95513
	.byte	8,2,35,8,13
	.byte	'ccucon2',0
	.word	95513
	.byte	8,2,35,16,13
	.byte	'ccucon5',0
	.word	95513
	.byte	8,2,35,24,13
	.byte	'ccucon6',0
	.word	95513
	.byte	8,2,35,32,13
	.byte	'ccucon7',0
	.word	95513
	.byte	8,2,35,40,0,33
	.byte	'IfxScuCcu_ClockDistributionConfig',0,11,252,5,3
	.word	95803
	.byte	30,11,128,6,9,8,13
	.byte	'value',0
	.word	2156
	.byte	4,2,35,0,13
	.byte	'mask',0
	.word	2156
	.byte	4,2,35,4,0,33
	.byte	'IfxScuCcu_FlashWaitstateConfig',0,11,132,6,3
	.word	95955
	.byte	3
	.word	95701
	.byte	30,11,137,6,9,16,13
	.byte	'numOfPllDividerSteps',0
	.word	488
	.byte	1,2,35,0,13
	.byte	'pllDividerStep',0
	.word	96031
	.byte	4,2,35,4,13
	.byte	'pllInitialStep',0
	.word	95584
	.byte	8,2,35,8,0,33
	.byte	'IfxScuCcu_SysPllConfig',0,11,142,6,3
	.word	96036
	.byte	30,20,59,15,16,13
	.byte	'module',0
	.word	20914
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	26837
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	26876
	.byte	1,2,35,12,0,31
	.word	96153
	.byte	33
	.byte	'IfxCcu6_Cc60in_In',0,20,64,3
	.word	96204
	.byte	30,20,67,15,16,13
	.byte	'module',0
	.word	20914
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	26837
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	26876
	.byte	1,2,35,12,0,31
	.word	96235
	.byte	33
	.byte	'IfxCcu6_Cc61in_In',0,20,72,3
	.word	96286
	.byte	30,20,75,15,16,13
	.byte	'module',0
	.word	20914
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	26837
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	26876
	.byte	1,2,35,12,0,31
	.word	96317
	.byte	33
	.byte	'IfxCcu6_Cc62in_In',0,20,80,3
	.word	96368
	.byte	30,20,83,15,16,13
	.byte	'module',0
	.word	20914
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	26837
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	26876
	.byte	1,2,35,12,0,31
	.word	96399
	.byte	33
	.byte	'IfxCcu6_Ccpos0_In',0,20,88,3
	.word	96450
	.byte	30,20,91,15,16,13
	.byte	'module',0
	.word	20914
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	26837
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	26876
	.byte	1,2,35,12,0,31
	.word	96481
	.byte	33
	.byte	'IfxCcu6_Ccpos1_In',0,20,96,3
	.word	96532
	.byte	30,20,99,15,16,13
	.byte	'module',0
	.word	20914
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	26837
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	26876
	.byte	1,2,35,12,0,31
	.word	96563
	.byte	33
	.byte	'IfxCcu6_Ccpos2_In',0,20,104,3
	.word	96614
	.byte	30,20,107,15,16,13
	.byte	'module',0
	.word	20914
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	26837
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	26876
	.byte	1,2,35,12,0,31
	.word	96645
	.byte	33
	.byte	'IfxCcu6_Ctrap_In',0,20,112,3
	.word	96696
	.byte	31
	.word	26995
	.byte	33
	.byte	'IfxCcu6_T12hr_In',0,20,120,3
	.word	96726
	.byte	31
	.word	27056
	.byte	33
	.byte	'IfxCcu6_T13hr_In',0,20,128,1,3
	.word	96756
	.byte	30,20,131,1,15,16,13
	.byte	'module',0
	.word	20914
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	26837
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	10420
	.byte	1,2,35,12,0,31
	.word	96787
	.byte	33
	.byte	'IfxCcu6_Cc60_Out',0,20,136,1,3
	.word	96839
	.byte	30,20,139,1,15,16,13
	.byte	'module',0
	.word	20914
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	26837
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	10420
	.byte	1,2,35,12,0,31
	.word	96870
	.byte	33
	.byte	'IfxCcu6_Cc61_Out',0,20,144,1,3
	.word	96922
	.byte	30,20,147,1,15,16,13
	.byte	'module',0
	.word	20914
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	26837
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	10420
	.byte	1,2,35,12,0,31
	.word	96953
	.byte	33
	.byte	'IfxCcu6_Cc62_Out',0,20,152,1,3
	.word	97005
	.byte	30,20,155,1,15,16,13
	.byte	'module',0
	.word	20914
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	26837
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	10420
	.byte	1,2,35,12,0,31
	.word	97036
	.byte	33
	.byte	'IfxCcu6_Cout60_Out',0,20,160,1,3
	.word	97088
	.byte	30,20,163,1,15,16,13
	.byte	'module',0
	.word	20914
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	26837
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	10420
	.byte	1,2,35,12,0,31
	.word	97121
	.byte	33
	.byte	'IfxCcu6_Cout61_Out',0,20,168,1,3
	.word	97173
	.byte	30,20,171,1,15,16,13
	.byte	'module',0
	.word	20914
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	26837
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	10420
	.byte	1,2,35,12,0,31
	.word	97206
	.byte	33
	.byte	'IfxCcu6_Cout62_Out',0,20,176,1,3
	.word	97258
	.byte	30,20,179,1,15,16,13
	.byte	'module',0
	.word	20914
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	26837
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	10420
	.byte	1,2,35,12,0,31
	.word	97291
	.byte	33
	.byte	'IfxCcu6_Cout63_Out',0,20,184,1,3
	.word	97343
	.byte	33
	.byte	'IfxCcu6_CaptureCompareInput',0,12,90,3
	.word	21730
	.byte	33
	.byte	'IfxCcu6_CaptureCompareInputSignal',0,12,100,3
	.word	21988
	.byte	15,12,113,9,1,16
	.byte	'IfxCcu6_ChannelOut_cc0',0,0,16
	.byte	'IfxCcu6_ChannelOut_cout0',0,1,16
	.byte	'IfxCcu6_ChannelOut_cc1',0,2,16
	.byte	'IfxCcu6_ChannelOut_cout1',0,3,16
	.byte	'IfxCcu6_ChannelOut_cc2',0,4,16
	.byte	'IfxCcu6_ChannelOut_cout2',0,5,16
	.byte	'IfxCcu6_ChannelOut_cout3',0,6,0,33
	.byte	'IfxCcu6_ChannelOut',0,12,122,3
	.word	97454
	.byte	33
	.byte	'IfxCcu6_CountingInputMode',0,12,137,1,3
	.word	27603
	.byte	33
	.byte	'IfxCcu6_ExternalTriggerMode',0,12,161,1,3
	.word	27117
	.byte	15,12,166,1,9,1,16
	.byte	'IfxCcu6_HallSensorTriggerMode_permanentCheck',0,0,16
	.byte	'IfxCcu6_HallSensorTriggerMode_cM63',0,1,16
	.byte	'IfxCcu6_HallSensorTriggerMode_t13PM',0,2,16
	.byte	'IfxCcu6_HallSensorTriggerMode_off',0,3,16
	.byte	'IfxCcu6_HallSensorTriggerMode_t12PMCountingUp',0,4,16
	.byte	'IfxCcu6_HallSensorTriggerMode_t12OMCountingDown',0,5,16
	.byte	'IfxCcu6_HallSensorTriggerMode_cM61CountingUp',0,6,16
	.byte	'IfxCcu6_HallSensorTriggerMode_cM61CountingDown',0,7,0,33
	.byte	'IfxCcu6_HallSensorTriggerMode',0,12,177,1,3
	.word	97742
	.byte	33
	.byte	'IfxCcu6_InterruptSource',0,12,203,1,3
	.word	21068
	.byte	15,12,208,1,9,1,16
	.byte	'IfxCcu6_MultiChannelSwitchingSelect_noEvent',0,0,16
	.byte	'IfxCcu6_MultiChannelSwitchingSelect_correctHallEvent',0,1,16
	.byte	'IfxCcu6_MultiChannelSwitchingSelect_t13PeriodMatch',0,2,16
	.byte	'IfxCcu6_MultiChannelSwitchingSelect_t12OneMatch',0,3,16
	.byte	'IfxCcu6_MultiChannelSwitchingSelect_t12Channel1CompareMatch',0,4,16
	.byte	'IfxCcu6_MultiChannelSwitchingSelect_t12PeriodMatch',0,5,0,33
	.byte	'IfxCcu6_MultiChannelSwitchingSelect',0,12,217,1,3
	.word	98173
	.byte	15,12,222,1,9,1,16
	.byte	'IfxCcu6_MultiChannelSwitchingSync_direct',0,0,16
	.byte	'IfxCcu6_MultiChannelSwitchingSync_t13ZeroMatch',0,1,16
	.byte	'IfxCcu6_MultiChannelSwitchingSync_t12ZeroMatch',0,2,0,33
	.byte	'IfxCcu6_MultiChannelSwitchingSync',0,12,229,1,3
	.word	98544
	.byte	33
	.byte	'IfxCcu6_ServiceRequest',0,12,239,1,3
	.word	28644
	.byte	15,12,244,1,9,1,16
	.byte	'IfxCcu6_SleepMode_enable',0,0,16
	.byte	'IfxCcu6_SleepMode_disable',0,1,0,33
	.byte	'IfxCcu6_SleepMode',0,12,248,1,3
	.word	98767
	.byte	33
	.byte	'IfxCcu6_SuspendMode',0,12,129,2,3
	.word	20919
	.byte	15,12,133,2,9,1,16
	.byte	'IfxCcu6_T12Channel_0',0,0,16
	.byte	'IfxCcu6_T12Channel_1',0,1,16
	.byte	'IfxCcu6_T12Channel_2',0,2,0,33
	.byte	'IfxCcu6_T12Channel',0,12,138,2,3
	.word	98885
	.byte	15,12,142,2,9,1,16
	.byte	'IfxCcu6_T12ChannelMode_off',0,0,16
	.byte	'IfxCcu6_T12ChannelMode_compareMode',0,1,16
	.byte	'IfxCcu6_T12ChannelMode_doubleRegisterCaptureRisingAndFalling',0,4,16
	.byte	'IfxCcu6_T12ChannelMode_doubleRegisterCaptureRising',0,5,16
	.byte	'IfxCcu6_T12ChannelMode_doubleRegisterCaptureFalling',0,6,16
	.byte	'IfxCcu6_T12ChannelMode_doubleRegisterCaptureAny',0,7,16
	.byte	'IfxCcu6_T12ChannelMode_hallSensor',0,8,16
	.byte	'IfxCcu6_T12ChannelMode_hysteresisLikecompare',0,9,16
	.byte	'IfxCcu6_T12ChannelMode_multiInputCaptureRisingAndFalling',0,10,16
	.byte	'IfxCcu6_T12ChannelMode_multiInputCaptureFallingAndRising',0,11,16
	.byte	'IfxCcu6_T12ChannelMode_multiInputCaptureBothRising',0,12,16
	.byte	'IfxCcu6_T12ChannelMode_multiInputCaptureBothFalling',0,13,16
	.byte	'IfxCcu6_T12ChannelMode_multiInputCaptureAny',0,14,0,33
	.byte	'IfxCcu6_T12ChannelMode',0,12,158,2,3
	.word	98989
	.byte	15,12,163,2,9,1,16
	.byte	'IfxCcu6_T12CountDirection_up',0,0,16
	.byte	'IfxCcu6_T12CountDirection_down',0,1,0,33
	.byte	'IfxCcu6_T12CountDirection',0,12,167,2,3
	.word	99668
	.byte	33
	.byte	'IfxCcu6_T12CountMode',0,12,178,2,3
	.word	27943
	.byte	33
	.byte	'IfxCcu6_T13TriggerDirection',0,12,189,2,3
	.word	28390
	.byte	33
	.byte	'IfxCcu6_T13TriggerEvent',0,12,205,2,3
	.word	28070
	.byte	33
	.byte	'IfxCcu6_TimerId',0,12,213,2,3
	.word	26786
	.byte	15,12,218,2,9,1,16
	.byte	'IfxCcu6_TimerInputClock_fcc6',0,0,16
	.byte	'IfxCcu6_TimerInputClock_fcc6By2',0,1,16
	.byte	'IfxCcu6_TimerInputClock_fcc6By4',0,2,16
	.byte	'IfxCcu6_TimerInputClock_fcc6By8',0,3,16
	.byte	'IfxCcu6_TimerInputClock_fcc6By16',0,4,16
	.byte	'IfxCcu6_TimerInputClock_fcc6By32',0,5,16
	.byte	'IfxCcu6_TimerInputClock_fcc6By64',0,6,16
	.byte	'IfxCcu6_TimerInputClock_fcc6By128',0,7,0,33
	.byte	'IfxCcu6_TimerInputClock',0,12,228,2,3
	.word	99899
	.byte	15,12,247,2,9,1,16
	.byte	'IfxCcu6_TimerRunStatus_stopped',0,0,16
	.byte	'IfxCcu6_TimerRunStatus_running',0,1,0,33
	.byte	'IfxCcu6_TimerRunStatus',0,12,251,2,3
	.word	100213
	.byte	15,12,128,3,9,1,16
	.byte	'IfxCcu6_TrapMode_automatic',0,0,16
	.byte	'IfxCcu6_TrapMode_manual',0,1,0,33
	.byte	'IfxCcu6_TrapMode',0,12,133,3,3
	.word	100318
	.byte	15,12,138,3,9,1,16
	.byte	'IfxCcu6_TrapState_t12Sync',0,0,16
	.byte	'IfxCcu6_TrapState_t13Sync',0,1,16
	.byte	'IfxCcu6_TrapState_immediate',0,3,0,33
	.byte	'IfxCcu6_TrapState',0,12,145,3,3
	.word	100406
	.byte	33
	.byte	'Timer',0,19,53,24
	.word	26690
	.byte	3
	.word	26690
	.byte	36,1,1,21
	.word	100540
	.byte	0,3
	.word	100545
	.byte	33
	.byte	'Timer_Start',0,19,54,24
	.word	100554
	.byte	33
	.byte	'Timer_Stop',0,19,55,24
	.word	100554
	.byte	33
	.byte	'Timer_SynchronousStart',0,19,56,24
	.word	100554
	.byte	33
	.byte	'Timer_SynchronousStop',0,19,57,24
	.word	100554
	.byte	33
	.byte	'Timer_CountOneStep',0,19,58,24
	.word	100554
	.byte	33
	.byte	'Timer_StartSingleShotMode',0,19,59,24
	.word	100554
	.byte	33
	.byte	'Timer_Config',0,19,92,3
	.word	27473
	.byte	33
	.byte	'IfxCcu6_Timer_Clock',0,18,242,1,3
	.word	27768
	.byte	33
	.byte	'IfxCcu6_Timer_InterruptConfig',0,18,252,1,3
	.word	28818
	.byte	33
	.byte	'IfxCcu6_Timer_Timer12',0,18,132,2,3
	.word	28022
	.byte	33
	.byte	'IfxCcu6_Timer_Timer13',0,18,141,2,3
	.word	28567
	.byte	33
	.byte	'IfxCcu6_Timer_TriggerConfig',0,18,151,2,3
	.word	27283
	.byte	33
	.byte	'IfxCcu6_Timer_Pins',0,18,162,2,3
	.word	28906
	.byte	33
	.byte	'IfxCcu6_Timer',0,18,174,2,3
	.word	27401
	.byte	33
	.byte	'IfxCcu6_Timer_Config',0,18,193,2,3
	.word	28972
	.byte	15,14,151,1,9,1,16
	.byte	'IfxStm_Comparator_0',0,0,16
	.byte	'IfxStm_Comparator_1',0,1,0,33
	.byte	'IfxStm_Comparator',0,14,155,1,3
	.word	100989
	.byte	15,14,159,1,9,1,16
	.byte	'IfxStm_ComparatorInterrupt_ir0',0,0,16
	.byte	'IfxStm_ComparatorInterrupt_ir1',0,1,0,33
	.byte	'IfxStm_ComparatorInterrupt',0,14,163,1,3
	.word	101067
	.byte	15,14,167,1,9,1,16
	.byte	'IfxStm_ComparatorOffset_0',0,0,16
	.byte	'IfxStm_ComparatorOffset_1',0,1,16
	.byte	'IfxStm_ComparatorOffset_2',0,2,16
	.byte	'IfxStm_ComparatorOffset_3',0,3,16
	.byte	'IfxStm_ComparatorOffset_4',0,4,16
	.byte	'IfxStm_ComparatorOffset_5',0,5,16
	.byte	'IfxStm_ComparatorOffset_6',0,6,16
	.byte	'IfxStm_ComparatorOffset_7',0,7,16
	.byte	'IfxStm_ComparatorOffset_8',0,8,16
	.byte	'IfxStm_ComparatorOffset_9',0,9,16
	.byte	'IfxStm_ComparatorOffset_10',0,10,16
	.byte	'IfxStm_ComparatorOffset_11',0,11,16
	.byte	'IfxStm_ComparatorOffset_12',0,12,16
	.byte	'IfxStm_ComparatorOffset_13',0,13,16
	.byte	'IfxStm_ComparatorOffset_14',0,14,16
	.byte	'IfxStm_ComparatorOffset_15',0,15,16
	.byte	'IfxStm_ComparatorOffset_16',0,16,16
	.byte	'IfxStm_ComparatorOffset_17',0,17,16
	.byte	'IfxStm_ComparatorOffset_18',0,18,16
	.byte	'IfxStm_ComparatorOffset_19',0,19,16
	.byte	'IfxStm_ComparatorOffset_20',0,20,16
	.byte	'IfxStm_ComparatorOffset_21',0,21,16
	.byte	'IfxStm_ComparatorOffset_22',0,22,16
	.byte	'IfxStm_ComparatorOffset_23',0,23,16
	.byte	'IfxStm_ComparatorOffset_24',0,24,16
	.byte	'IfxStm_ComparatorOffset_25',0,25,16
	.byte	'IfxStm_ComparatorOffset_26',0,26,16
	.byte	'IfxStm_ComparatorOffset_27',0,27,16
	.byte	'IfxStm_ComparatorOffset_28',0,28,16
	.byte	'IfxStm_ComparatorOffset_29',0,29,16
	.byte	'IfxStm_ComparatorOffset_30',0,30,16
	.byte	'IfxStm_ComparatorOffset_31',0,31,0,33
	.byte	'IfxStm_ComparatorOffset',0,14,201,1,3
	.word	101176
	.byte	15,14,205,1,9,1,16
	.byte	'IfxStm_ComparatorSize_1Bit',0,0,16
	.byte	'IfxStm_ComparatorSize_2Bits',0,1,16
	.byte	'IfxStm_ComparatorSize_3Bits',0,2,16
	.byte	'IfxStm_ComparatorSize_4Bits',0,3,16
	.byte	'IfxStm_ComparatorSize_5Bits',0,4,16
	.byte	'IfxStm_ComparatorSize_6Bits',0,5,16
	.byte	'IfxStm_ComparatorSize_7Bits',0,6,16
	.byte	'IfxStm_ComparatorSize_8Bits',0,7,16
	.byte	'IfxStm_ComparatorSize_9Bits',0,8,16
	.byte	'IfxStm_ComparatorSize_10Bits',0,9,16
	.byte	'IfxStm_ComparatorSize_11Bits',0,10,16
	.byte	'IfxStm_ComparatorSize_12Bits',0,11,16
	.byte	'IfxStm_ComparatorSize_13Bits',0,12,16
	.byte	'IfxStm_ComparatorSize_14Bits',0,13,16
	.byte	'IfxStm_ComparatorSize_15Bits',0,14,16
	.byte	'IfxStm_ComparatorSize_16Bits',0,15,16
	.byte	'IfxStm_ComparatorSize_17Bits',0,16,16
	.byte	'IfxStm_ComparatorSize_18Bits',0,17,16
	.byte	'IfxStm_ComparatorSize_19Bits',0,18,16
	.byte	'IfxStm_ComparatorSize_20Bits',0,19,16
	.byte	'IfxStm_ComparatorSize_21Bits',0,20,16
	.byte	'IfxStm_ComparatorSize_22Bits',0,21,16
	.byte	'IfxStm_ComparatorSize_23Bits',0,22,16
	.byte	'IfxStm_ComparatorSize_24Bits',0,23,16
	.byte	'IfxStm_ComparatorSize_25Bits',0,24,16
	.byte	'IfxStm_ComparatorSize_26Bits',0,25,16
	.byte	'IfxStm_ComparatorSize_27Bits',0,26,16
	.byte	'IfxStm_ComparatorSize_28Bits',0,27,16
	.byte	'IfxStm_ComparatorSize_29Bits',0,28,16
	.byte	'IfxStm_ComparatorSize_30Bits',0,29,16
	.byte	'IfxStm_ComparatorSize_31Bits',0,30,16
	.byte	'IfxStm_ComparatorSize_32Bits',0,31,0,33
	.byte	'IfxStm_ComparatorSize',0,14,239,1,3
	.word	102134
	.byte	15,14,244,1,9,1,16
	.byte	'IfxStm_SleepMode_enable',0,0,16
	.byte	'IfxStm_SleepMode_disable',0,1,0,33
	.byte	'IfxStm_SleepMode',0,14,248,1,3
	.word	103154
	.byte	15,14,252,1,9,1,16
	.byte	'IfxStm_SuspendMode_none',0,0,16
	.byte	'IfxStm_SuspendMode_hard',0,1,16
	.byte	'IfxStm_SuspendMode_soft',0,2,0,33
	.byte	'IfxStm_SuspendMode',0,14,129,2,3
	.word	103240
	.byte	33
	.byte	'_iob_flag_t',0,29,82,25
	.word	1039
	.byte	7
	.byte	'char',0,1,6,33
	.byte	'int8',0,30,54,29
	.word	103373
	.byte	33
	.byte	'int16',0,30,55,29
	.word	30424
	.byte	33
	.byte	'int32',0,30,56,29
	.word	481
	.byte	33
	.byte	'int64',0,30,57,29
	.word	25885
	.byte	33
	.byte	'pit_index_enum',0,23,47,2
	.word	29986
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L37:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,21,0,54,15,39,12,0,0,3,15,0,73,19,0,0,4,46,1,3,8,32,13
	.byte	58,15,59,15,57,15,54,15,39,12,0,0,5,5,0,3,8,58,15,59,15,57,15,73,19,0,0,6,11,0,0,0,7,36,0,3,8,11,15,62
	.byte	15,0,0,8,46,1,3,8,32,13,58,15,59,15,57,15,73,19,54,15,39,12,0,0,9,59,0,3,8,0,0,10,19,1,3,8,58,15,59,15
	.byte	57,15,11,15,0,0,11,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,12,23,1,58,15,59,15,57,15,11,15,0,0,13,13
	.byte	0,3,8,73,19,11,15,56,9,0,0,14,53,0,73,19,0,0,15,4,1,58,15,59,15,57,15,11,15,0,0,16,40,0,3,8,28,13,0,0
	.byte	17,11,1,0,0,18,1,1,11,15,73,19,0,0,19,33,0,47,15,0,0,20,46,1,3,8,73,19,54,15,39,12,63,12,60,12,0,0,21
	.byte	5,0,73,19,0,0,22,46,0,3,8,54,15,39,12,63,12,60,12,0,0,23,46,1,49,19,0,0,24,5,0,49,19,0,0,25,29,1,49,19
	.byte	0,0,26,11,0,49,19,0,0,27,46,0,3,8,58,15,59,15,57,15,73,19,54,15,39,12,63,12,60,12,0,0,28,46,1,3,8,58,15
	.byte	59,15,57,15,73,19,54,15,39,12,63,12,60,12,0,0,29,46,1,3,8,58,15,59,15,57,15,54,15,39,12,63,12,60,12,0
	.byte	0,30,19,1,58,15,59,15,57,15,11,15,0,0,31,38,0,73,19,0,0,32,11,1,49,19,0,0,33,22,0,3,8,58,15,59,15,57,15
	.byte	73,19,0,0,34,21,0,54,15,0,0,35,52,0,3,8,58,15,59,15,57,15,73,19,63,12,60,12,0,0,36,21,1,54,15,39,12,0
	.byte	0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L38:
	.word	.L201-.L200
.L200:
	.half	3
	.word	.L203-.L202
.L202:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer',0
	.byte	'F:\\ADS\\AURIX-Studio-1.10.2\\tools\\Compilers\\Tasking_1.1r8\\ctc\\include\\',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common',0
	.byte	0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_Intrinsics.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Src\\Std\\IfxSrc.h',0
	.byte	0,0,0
	.byte	'IfxSrc_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Scu\\Std\\IfxScuWdt.h',0
	.byte	0,0,0
	.byte	'IfxScu_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxCpu_cfg.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Port\\Std\\IfxPort.h',0
	.byte	0,0,0
	.byte	'IfxPort_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Scu\\Std\\IfxScuCcu.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Ccu6\\Std\\IfxCcu6.h',0
	.byte	0,0,0
	.byte	'IfxCcu6_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Stm\\Std\\IfxStm.h',0
	.byte	0,0,0
	.byte	'IfxStm_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\SysSe\\Bsp\\Bsp.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxCcu6_cfg.h',0
	.byte	0,0,0
	.byte	'IfxCcu6_Timer.h',0,2,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\If\\Ccu6If\\Timer.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_PinMap\\IfxCcu6_PinMap.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Ifx_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxSrc_cfg.h',0
	.byte	0,0,0
	.byte	'..\\libraries\\zf_driver\\zf_driver_pit.h',0,0,0,0
	.byte	'IfxCpu_regdef.h',0,1,0,0
	.byte	'../libraries/zf_driver/zf_driver_pit.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Platform_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxScu_cfg.h',0
	.byte	0,0,0
	.byte	'IfxFlash_regdef.h',0,1,0,0
	.byte	'stdio.h',0,3,0,0
	.byte	'zf_common_typedef.h',0,4,0,0,0
.L203:
.L201:
	.sdecl	'.debug_info',debug,cluster('pit_close')
	.sect	'.debug_info'
.L39:
	.word	312
	.half	3
	.word	.L40
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_pit.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L42,.L41
	.byte	2
	.word	.L35
	.byte	3
	.byte	'pit_close',0,1,48,6,1,1,1
	.word	.L24,.L69,.L23
	.byte	4
	.byte	'pit_index',0,1,48,32
	.word	.L70,.L71
	.byte	5
	.word	.L24,.L69
	.byte	6
	.byte	'module',0,1,50,24
	.word	.L72,.L73
	.byte	6
	.byte	'g_Ccu6Timer',0,1,51,19
	.word	.L74,.L75
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('pit_close')
	.sect	'.debug_abbrev'
.L40:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('pit_close')
	.sect	'.debug_line'
.L41:
	.word	.L205-.L204
.L204:
	.half	3
	.word	.L207-.L206
.L206:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_pit.c',0,0,0,0,0
.L207:
	.byte	5,6,7,0,5,2
	.word	.L24
	.byte	3,47,1,5,61,9
	.half	.L154-.L24
	.byte	3,5,1,5,59,9
	.half	.L208-.L154
	.byte	1,5,33,9
	.half	.L153-.L208
	.byte	1,5,22,9
	.half	.L155-.L153
	.byte	3,2,1,5,56,9
	.half	.L209-.L155
	.byte	3,1,1,5,54,9
	.half	.L210-.L209
	.byte	1,5,23,9
	.half	.L211-.L210
	.byte	1,5,25,9
	.half	.L212-.L211
	.byte	3,2,1,5,1,9
	.half	.L156-.L212
	.byte	3,1,1,7,9
	.half	.L43-.L156
	.byte	0,1,1
.L205:
	.sdecl	'.debug_ranges',debug,cluster('pit_close')
	.sect	'.debug_ranges'
.L42:
	.word	-1,.L24,0,.L43-.L24,0,0
	.sdecl	'.debug_info',debug,cluster('pit_start')
	.sect	'.debug_info'
.L44:
	.word	312
	.half	3
	.word	.L45
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_pit.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L47,.L46
	.byte	2
	.word	.L35
	.byte	3
	.byte	'pit_start',0,1,68,6,1,1,1
	.word	.L26,.L76,.L25
	.byte	4
	.byte	'pit_index',0,1,68,32
	.word	.L70,.L77
	.byte	5
	.word	.L26,.L76
	.byte	6
	.byte	'module',0,1,70,24
	.word	.L72,.L78
	.byte	6
	.byte	'g_Ccu6Timer',0,1,71,19
	.word	.L74,.L79
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('pit_start')
	.sect	'.debug_abbrev'
.L45:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('pit_start')
	.sect	'.debug_line'
.L46:
	.word	.L214-.L213
.L213:
	.half	3
	.word	.L216-.L215
.L215:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_pit.c',0,0,0,0,0
.L216:
	.byte	5,6,7,0,5,2
	.word	.L26
	.byte	3,195,0,1,5,61,9
	.half	.L159-.L26
	.byte	3,5,1,5,59,9
	.half	.L217-.L159
	.byte	1,5,33,9
	.half	.L158-.L217
	.byte	1,5,22,9
	.half	.L160-.L158
	.byte	3,2,1,5,56,9
	.half	.L218-.L160
	.byte	3,1,1,5,54,9
	.half	.L219-.L218
	.byte	1,5,23,9
	.half	.L220-.L219
	.byte	1,5,26,9
	.half	.L221-.L220
	.byte	3,2,1,5,1,9
	.half	.L161-.L221
	.byte	3,1,1,7,9
	.half	.L48-.L161
	.byte	0,1,1
.L214:
	.sdecl	'.debug_ranges',debug,cluster('pit_start')
	.sect	'.debug_ranges'
.L47:
	.word	-1,.L26,0,.L48-.L26,0,0
	.sdecl	'.debug_info',debug,cluster('pit_all_close')
	.sect	'.debug_info'
.L49:
	.word	250
	.half	3
	.word	.L50
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_pit.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L52,.L51
	.byte	2
	.word	.L35
	.byte	3
	.byte	'pit_all_close',0,1,87,6,1,1,1
	.word	.L28,.L80,.L27
	.byte	4
	.word	.L28,.L80
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('pit_all_close')
	.sect	'.debug_abbrev'
.L50:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('pit_all_close')
	.sect	'.debug_line'
.L51:
	.word	.L223-.L222
.L222:
	.half	3
	.word	.L225-.L224
.L224:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_pit.c',0,0,0,0,0
.L225:
	.byte	5,39,7,0,5,2
	.word	.L28
	.byte	3,216,0,1,5,62,9
	.half	.L226-.L28
	.byte	1,5,27,9
	.half	.L227-.L226
	.byte	1,5,39,9
	.half	.L228-.L227
	.byte	3,1,1,5,62,9
	.half	.L229-.L228
	.byte	1,5,27,9
	.half	.L230-.L229
	.byte	1,5,1,9
	.half	.L231-.L230
	.byte	3,1,1,7,9
	.half	.L53-.L231
	.byte	0,1,1
.L223:
	.sdecl	'.debug_ranges',debug,cluster('pit_all_close')
	.sect	'.debug_ranges'
.L52:
	.word	-1,.L28,0,.L53-.L28,0,0
	.sdecl	'.debug_info',debug,cluster('pit_disable')
	.sect	'.debug_info'
.L54:
	.word	354
	.half	3
	.word	.L55
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_pit.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L57,.L56
	.byte	2
	.word	.L35
	.byte	3
	.byte	'pit_disable',0,1,101,6,1,1,1
	.word	.L30,.L81,.L29
	.byte	4
	.byte	'pit_index',0,1,101,34
	.word	.L70,.L82
	.byte	5
	.word	.L30,.L81
	.byte	6
	.byte	'module',0,1,103,24
	.word	.L72,.L83
	.byte	7
	.word	.L84,.L85,.L86
	.byte	8
	.word	.L87,.L88
	.byte	8
	.word	.L89,.L90
	.byte	9
	.word	.L91,.L85,.L86
	.byte	6
	.byte	'mask',0,2,190,11,12
	.word	.L92,.L93
	.byte	0,0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('pit_disable')
	.sect	'.debug_abbrev'
.L55:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,5,0,49,16,2,6,0,0,9,11,1,49,16,17,1,18
	.byte	1,0,0,0
	.sdecl	'.debug_line',debug,cluster('pit_disable')
	.sect	'.debug_line'
.L56:
	.word	.L233-.L232
.L232:
	.half	3
	.word	.L235-.L234
.L234:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_pit.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Ccu6\\Std\\IfxCcu6.h',0
	.byte	0,0,0,0
.L235:
	.byte	5,6,7,0,5,2
	.word	.L30
	.byte	3,228,0,1,5,61,9
	.half	.L163-.L30
	.byte	3,3,1,5,59,9
	.half	.L236-.L163
	.byte	1,5,33,9
	.half	.L162-.L236
	.byte	1,5,51,9
	.half	.L165-.L162
	.byte	3,1,1,5,49,9
	.half	.L237-.L165
	.byte	1,5,54,9
	.half	.L238-.L237
	.byte	1,5,58,9
	.half	.L164-.L238
	.byte	1,4,2,5,20,9
	.half	.L85-.L164
	.byte	3,213,10,1,5,23,9
	.half	.L239-.L85
	.byte	1,5,28,9
	.half	.L166-.L239
	.byte	3,1,1,5,33,9
	.half	.L240-.L166
	.byte	1,5,31,9
	.half	.L167-.L240
	.byte	1,5,17,9
	.half	.L241-.L167
	.byte	1,4,1,5,1,9
	.half	.L86-.L241
	.byte	3,171,117,1,7,9
	.half	.L58-.L86
	.byte	0,1,1
.L233:
	.sdecl	'.debug_ranges',debug,cluster('pit_disable')
	.sect	'.debug_ranges'
.L57:
	.word	-1,.L30,0,.L58-.L30,0,0
	.sdecl	'.debug_info',debug,cluster('pit_enable')
	.sect	'.debug_info'
.L59:
	.word	353
	.half	3
	.word	.L60
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_pit.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L62,.L61
	.byte	2
	.word	.L35
	.byte	3
	.byte	'pit_enable',0,1,115,6,1,1,1
	.word	.L32,.L94,.L31
	.byte	4
	.byte	'pit_index',0,1,115,33
	.word	.L70,.L95
	.byte	5
	.word	.L32,.L94
	.byte	6
	.byte	'module',0,1,117,24
	.word	.L72,.L96
	.byte	7
	.word	.L97,.L98,.L99
	.byte	8
	.word	.L100,.L101
	.byte	8
	.word	.L102,.L103
	.byte	9
	.word	.L104,.L98,.L99
	.byte	6
	.byte	'mask',0,2,157,12,12
	.word	.L92,.L105
	.byte	0,0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('pit_enable')
	.sect	'.debug_abbrev'
.L60:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,5,0,49,16,2,6,0,0,9,11,1,49,16,17,1,18
	.byte	1,0,0,0
	.sdecl	'.debug_line',debug,cluster('pit_enable')
	.sect	'.debug_line'
.L61:
	.word	.L243-.L242
.L242:
	.half	3
	.word	.L245-.L244
.L244:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_pit.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Ccu6\\Std\\IfxCcu6.h',0
	.byte	0,0,0,0
.L245:
	.byte	5,6,7,0,5,2
	.word	.L32
	.byte	3,242,0,1,5,61,9
	.half	.L169-.L32
	.byte	3,3,1,5,59,9
	.half	.L246-.L169
	.byte	1,5,33,9
	.half	.L168-.L246
	.byte	1,5,50,9
	.half	.L171-.L168
	.byte	3,1,1,5,48,9
	.half	.L247-.L171
	.byte	1,5,53,9
	.half	.L248-.L247
	.byte	1,5,57,9
	.half	.L170-.L248
	.byte	1,4,2,5,20,9
	.half	.L98-.L170
	.byte	3,166,11,1,5,23,9
	.half	.L249-.L98
	.byte	1,5,28,9
	.half	.L172-.L249
	.byte	3,1,1,5,31,9
	.half	.L250-.L172
	.byte	1,5,17,9
	.half	.L251-.L250
	.byte	1,4,1,5,1,9
	.half	.L99-.L251
	.byte	3,218,116,1,7,9
	.half	.L63-.L99
	.byte	0,1,1
.L243:
	.sdecl	'.debug_ranges',debug,cluster('pit_enable')
	.sect	'.debug_ranges'
.L62:
	.word	-1,.L32,0,.L63-.L32,0,0
	.sdecl	'.debug_info',debug,cluster('pit_init')
	.sect	'.debug_info'
.L64:
	.word	768
	.half	3
	.word	.L65
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_pit.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L67,.L66
	.byte	2
	.word	.L35
	.byte	3
	.byte	'pit_init',0,1,130,1,6,1,1,1
	.word	.L34,.L106,.L33
	.byte	4
	.byte	'pit_index',0,1,130,1,31
	.word	.L70,.L107
	.byte	4
	.byte	'time',0,1,130,1,49
	.word	.L92,.L108
	.byte	5
	.word	.L34,.L106
	.byte	6
	.byte	'i',0,1,132,1,11
	.word	.L109,.L110
	.byte	6
	.byte	'module',0,1,133,1,24
	.word	.L72,.L111
	.byte	6
	.byte	'timer_input_clk',0,1,134,1,12
	.word	.L112,.L113
	.byte	6
	.byte	'g_Ccu6Timer',0,1,135,1,19
	.word	.L74,.L114
	.byte	6
	.byte	'timerConfig',0,1,136,1,26
	.word	.L115,.L116
	.byte	6
	.byte	'timer_period',0,1,137,1,12
	.word	.L92,.L117
	.byte	6
	.byte	'interrupt_state',0,1,139,1,13
	.word	.L109,.L118
	.byte	7
	.word	.L119,.L120,.L4
	.byte	8
	.word	.L121,.L122
	.byte	7
	.word	.L125,.L120,.L3
	.byte	8
	.word	.L126,.L127
	.byte	6
	.byte	'enabled',0,2,149,5,13
	.word	.L109,.L128
	.byte	7
	.word	.L129,.L120,.L2
	.byte	9
	.word	.L130,.L120,.L2
	.byte	6
	.byte	'reg',0,2,141,5,17
	.word	.L131,.L132
	.byte	0,0,0,0,10
	.word	.L125,.L123,.L124
	.byte	0,0,10
	.word	.L119,.L123,.L124
	.byte	7
	.word	.L133,.L124,.L21
	.byte	11
	.word	.L134,.L135
	.byte	9
	.word	.L136,.L124,.L21
	.byte	7
	.word	.L137,.L124,.L21
	.byte	11
	.word	.L138,.L139
	.byte	12
	.word	.L140,.L124,.L21
	.byte	0,0,0,5
	.word	.L21,.L106
	.byte	6
	.byte	'debug_index',0,1,213,1,19
	.word	.L141,.L142
	.byte	7
	.word	.L143,.L144,.L22
	.byte	11
	.word	.L145,.L146
	.byte	11
	.word	.L147,.L148
	.byte	9
	.word	.L149,.L144,.L22
	.byte	6
	.byte	'ocs',0,3,145,16,18
	.word	.L150,.L151
	.byte	0,0,0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('pit_init')
	.sect	'.debug_abbrev'
.L65:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,11,1,49,16,85,6,0,0,9,11,1,49,16,17,1,18
	.byte	1,0,0,10,29,0,49,16,17,1,18,1,0,0,11,5,0,49,16,2,6,0,0,12,11,0,49,16,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('pit_init')
	.sect	'.debug_line'
.L66:
	.word	.L253-.L252
.L252:
	.half	3
	.word	.L255-.L254
.L254:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_pit.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Ccu6\\Std\\IfxCcu6.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\SysSe\\Bsp\\Bsp.h',0
	.byte	0,0,0,0
.L255:
	.byte	5,6,7,0,5,2
	.word	.L34
	.byte	3,129,1,1,4,2,5,19,9
	.half	.L120-.L34
	.byte	3,140,4,1,5,17,9
	.half	.L176-.L120
	.byte	3,1,1,5,21,9
	.half	.L177-.L176
	.byte	1,5,5,9
	.half	.L178-.L177
	.byte	1,5,14,9
	.half	.L2-.L178
	.byte	3,8,1,5,10,9
	.half	.L256-.L2
	.byte	3,1,1,5,5,9
	.half	.L257-.L256
	.byte	3,1,1,4,4,9
	.half	.L3-.L257
	.byte	3,213,123,1,4,1,5,59,9
	.half	.L4-.L3
	.byte	3,31,1,5,58,9
	.half	.L258-.L4
	.byte	1,5,33,9
	.half	.L174-.L258
	.byte	1,5,12,9
	.half	.L179-.L174
	.byte	1,5,37,9
	.half	.L181-.L179
	.byte	3,2,1,5,50,9
	.half	.L259-.L181
	.byte	1,5,48,9
	.half	.L182-.L259
	.byte	3,4,1,5,7,9
	.half	.L260-.L182
	.byte	3,1,1,5,17,9
	.half	.L183-.L260
	.byte	3,1,1,5,51,9
	.half	.L6-.L183
	.byte	3,2,1,5,49,9
	.half	.L184-.L6
	.byte	1,5,58,9
	.half	.L261-.L184
	.byte	1,5,56,9
	.half	.L262-.L261
	.byte	1,5,27,9
	.half	.L263-.L262
	.byte	3,1,1,5,9,9
	.half	.L187-.L263
	.byte	1,5,37,7,9
	.half	.L188-.L187
	.byte	1,5,25,9
	.half	.L7-.L188
	.byte	3,1,1,5,10,9
	.half	.L264-.L7
	.byte	3,1,1,5,15,9
	.half	.L5-.L264
	.byte	3,123,1,5,17,9
	.half	.L265-.L5
	.byte	1,5,8,7,9
	.half	.L8-.L265
	.byte	3,7,1,5,5,9
	.half	.L266-.L8
	.byte	1,5,14,7,9
	.half	.L9-.L266
	.byte	3,5,1,9
	.half	.L267-.L9
	.byte	3,6,1,9
	.half	.L268-.L267
	.byte	3,6,1,9
	.half	.L269-.L268
	.byte	3,6,1,5,53,9
	.half	.L10-.L269
	.byte	3,112,1,5,51,9
	.half	.L270-.L10
	.byte	1,5,53,9
	.half	.L271-.L270
	.byte	3,1,1,5,51,9
	.half	.L272-.L271
	.byte	1,5,13,9
	.half	.L273-.L272
	.byte	3,1,1,5,53,9
	.half	.L11-.L273
	.byte	3,4,1,5,51,9
	.half	.L274-.L11
	.byte	1,5,53,9
	.half	.L275-.L274
	.byte	3,1,1,5,51,9
	.half	.L276-.L275
	.byte	1,5,13,9
	.half	.L277-.L276
	.byte	3,1,1,5,53,9
	.half	.L12-.L277
	.byte	3,4,1,5,51,9
	.half	.L278-.L12
	.byte	1,5,53,9
	.half	.L279-.L278
	.byte	3,1,1,5,51,9
	.half	.L280-.L279
	.byte	1,5,13,9
	.half	.L281-.L280
	.byte	3,1,1,5,53,9
	.half	.L13-.L281
	.byte	3,4,1,5,51,9
	.half	.L282-.L13
	.byte	1,5,53,9
	.half	.L283-.L282
	.byte	3,1,1,5,51,9
	.half	.L284-.L283
	.byte	1,5,13,9
	.half	.L285-.L284
	.byte	3,1,1,5,21,9
	.half	.L15-.L285
	.byte	3,4,1,5,19,9
	.half	.L286-.L15
	.byte	1,5,5,9
	.half	.L287-.L286
	.byte	1,5,29,7,9
	.half	.L288-.L287
	.byte	3,2,1,5,27,9
	.half	.L289-.L288
	.byte	1,5,50,9
	.half	.L290-.L289
	.byte	3,1,1,5,48,9
	.half	.L291-.L290
	.byte	1,5,50,9
	.half	.L292-.L291
	.byte	3,1,1,5,48,9
	.half	.L293-.L292
	.byte	1,9
	.half	.L191-.L293
	.byte	3,1,1,5,50,9
	.half	.L192-.L191
	.byte	3,1,1,5,48,9
	.half	.L294-.L192
	.byte	1,5,50,9
	.half	.L295-.L294
	.byte	3,1,1,5,48,9
	.half	.L296-.L295
	.byte	1,9
	.half	.L297-.L296
	.byte	3,123,1,5,29,9
	.half	.L19-.L297
	.byte	3,9,1,5,27,9
	.half	.L298-.L19
	.byte	1,5,50,9
	.half	.L299-.L298
	.byte	3,1,1,5,48,9
	.half	.L300-.L299
	.byte	1,5,50,9
	.half	.L301-.L300
	.byte	3,1,1,5,48,9
	.half	.L302-.L301
	.byte	1,9
	.half	.L194-.L302
	.byte	3,1,1,5,50,9
	.half	.L195-.L194
	.byte	3,1,1,5,48,9
	.half	.L303-.L195
	.byte	1,5,50,9
	.half	.L304-.L303
	.byte	3,1,1,5,48,9
	.half	.L305-.L304
	.byte	1,5,44,9
	.half	.L20-.L305
	.byte	3,2,1,5,42,9
	.half	.L306-.L20
	.byte	1,5,44,9
	.half	.L307-.L306
	.byte	3,1,1,5,42,9
	.half	.L308-.L307
	.byte	1,5,44,9
	.half	.L309-.L308
	.byte	3,1,1,5,42,9
	.half	.L310-.L309
	.byte	1,5,31,9
	.half	.L311-.L310
	.byte	3,2,1,5,45,9
	.half	.L312-.L311
	.byte	1,5,23,9
	.half	.L123-.L312
	.byte	3,2,1,4,2,5,5,9
	.half	.L124-.L123
	.byte	3,215,5,1,5,17,7,9
	.half	.L313-.L124
	.byte	3,2,1,4,1,5,27,9
	.half	.L21-.L313
	.byte	3,170,122,1,5,26,9
	.half	.L197-.L21
	.byte	3,1,1,5,5,9
	.half	.L198-.L197
	.byte	1,5,40,7,9
	.half	.L314-.L198
	.byte	3,2,1,4,3,5,19,9
	.half	.L144-.L314
	.byte	3,187,14,1,5,17,9
	.half	.L199-.L144
	.byte	1,9
	.half	.L315-.L199
	.byte	3,1,1,9
	.half	.L316-.L315
	.byte	3,1,1,4,1,5,26,9
	.half	.L22-.L316
	.byte	3,197,113,1,5,1,9
	.half	.L317-.L22
	.byte	3,1,1,7,9
	.half	.L68-.L317
	.byte	0,1,1
.L253:
	.sdecl	'.debug_ranges',debug,cluster('pit_init')
	.sect	'.debug_ranges'
.L67:
	.word	-1,.L34,0,.L68-.L34,0,0
.L122:
	.word	-1,.L34,.L120-.L34,.L4-.L34,.L123-.L34,.L124-.L34,0,0
.L127:
	.word	-1,.L34,.L120-.L34,.L3-.L34,.L123-.L34,.L124-.L34,0,0
	.sdecl	'.debug_loc',debug,cluster('pit_all_close')
	.sect	'.debug_loc'
.L27:
	.word	-1,.L28,0,.L80-.L28
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('pit_close')
	.sect	'.debug_loc'
.L75:
	.word	-1,.L24,0,.L69-.L24
	.half	2
	.byte	145,88
	.word	0,0
.L73:
	.word	-1,.L24,.L155-.L24,.L156-.L24
	.half	1
	.byte	98
	.word	0,0
.L23:
	.word	-1,.L24,0,.L152-.L24
	.half	2
	.byte	138,0
	.word	.L152-.L24,.L69-.L24
	.half	2
	.byte	138,40
	.word	.L69-.L24,.L69-.L24
	.half	2
	.byte	138,0
	.word	0,0
.L71:
	.word	-1,.L24,0,.L153-.L24
	.half	1
	.byte	84
	.word	.L154-.L24,.L69-.L24
	.half	1
	.byte	95
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('pit_disable')
	.sect	'.debug_loc'
.L88:
	.word	0,0
.L93:
	.word	-1,.L30,.L166-.L30,.L167-.L30
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L83:
	.word	-1,.L30,.L165-.L30,.L81-.L30
	.half	1
	.byte	98
	.word	0,0
.L29:
	.word	-1,.L30,0,.L81-.L30
	.half	2
	.byte	138,0
	.word	0,0
.L82:
	.word	-1,.L30,0,.L162-.L30
	.half	1
	.byte	84
	.word	.L163-.L30,.L164-.L30
	.half	1
	.byte	95
	.word	0,0
.L90:
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('pit_enable')
	.sect	'.debug_loc'
.L101:
	.word	0,0
.L105:
	.word	-1,.L32,.L172-.L32,.L94-.L32
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L96:
	.word	-1,.L32,.L171-.L32,.L94-.L32
	.half	1
	.byte	98
	.word	0,0
.L31:
	.word	-1,.L32,0,.L94-.L32
	.half	2
	.byte	138,0
	.word	0,0
.L95:
	.word	-1,.L32,0,.L168-.L32
	.half	1
	.byte	84
	.word	.L169-.L32,.L170-.L32
	.half	1
	.byte	95
	.word	0,0
.L103:
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('pit_init')
	.sect	'.debug_loc'
.L146:
	.word	0,0
.L142:
	.word	-1,.L34,.L197-.L34,.L198-.L34
	.half	1
	.byte	95
	.word	0,0
.L135:
	.word	0,0
.L128:
	.word	0,0
.L139:
	.word	0,0
.L114:
	.word	-1,.L34,0,.L106-.L34
	.half	3
	.byte	145,248,126
	.word	0,0
.L110:
	.word	-1,.L34,.L183-.L34,.L106-.L34
	.half	1
	.byte	88
	.word	0,0
.L118:
	.word	-1,.L34,.L178-.L34,.L106-.L34
	.half	1
	.byte	91
	.word	0,0
.L148:
	.word	0,0
.L111:
	.word	-1,.L34,.L179-.L34,.L180-.L34
	.half	1
	.byte	98
	.word	.L181-.L34,.L106-.L34
	.half	1
	.byte	111
	.word	.L180-.L34,.L182-.L34
	.half	1
	.byte	101
	.word	0,0
.L151:
	.word	-1,.L34,.L199-.L34,.L22-.L34
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L107:
	.word	-1,.L34,0,.L174-.L34
	.half	1
	.byte	84
	.word	.L175-.L34,.L106-.L34
	.half	1
	.byte	89
	.word	0,0
.L33:
	.word	-1,.L34,0,.L173-.L34
	.half	2
	.byte	138,0
	.word	.L173-.L34,.L106-.L34
	.half	3
	.byte	138,136,1
	.word	.L106-.L34,.L106-.L34
	.half	2
	.byte	138,0
	.word	0,0
.L132:
	.word	-1,.L34,.L176-.L34,.L177-.L34
	.half	1
	.byte	95
	.word	0,0
.L108:
	.word	-1,.L34,0,.L174-.L34
	.half	1
	.byte	85
	.word	.L120-.L34,.L106-.L34
	.half	1
	.byte	90
	.word	.L184-.L34,.L185-.L34
	.half	1
	.byte	86
	.word	0,0
.L116:
	.word	-1,.L34,0,.L106-.L34
	.half	3
	.byte	145,156,127
	.word	0,0
.L113:
	.word	-1,.L34,.L184-.L34,.L186-.L34
	.half	2
	.byte	144,38
	.word	.L189-.L34,.L190-.L34
	.half	2
	.byte	144,38
	.word	.L192-.L34,.L193-.L34
	.half	2
	.byte	144,38
	.word	.L195-.L34,.L196-.L34
	.half	2
	.byte	144,38
	.word	0,0
.L117:
	.word	-1,.L34,.L187-.L34,.L188-.L34
	.half	1
	.byte	82
	.word	.L191-.L34,.L192-.L34
	.half	1
	.byte	82
	.word	.L194-.L34,.L195-.L34
	.half	1
	.byte	82
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('pit_start')
	.sect	'.debug_loc'
.L79:
	.word	-1,.L26,0,.L76-.L26
	.half	2
	.byte	145,88
	.word	0,0
.L78:
	.word	-1,.L26,.L160-.L26,.L161-.L26
	.half	1
	.byte	98
	.word	0,0
.L77:
	.word	-1,.L26,0,.L158-.L26
	.half	1
	.byte	84
	.word	.L159-.L26,.L76-.L26
	.half	1
	.byte	95
	.word	0,0
.L25:
	.word	-1,.L26,0,.L157-.L26
	.half	2
	.byte	138,0
	.word	.L157-.L26,.L76-.L26
	.half	2
	.byte	138,40
	.word	.L76-.L26,.L76-.L26
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L318:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('pit_close')
	.sect	'.debug_frame'
	.word	36
	.word	.L318,.L24,.L69-.L24
	.byte	4
	.word	(.L152-.L24)/2
	.byte	19,40,22,26,3,19,138,40,4
	.word	(.L69-.L152)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('pit_start')
	.sect	'.debug_frame'
	.word	36
	.word	.L318,.L26,.L76-.L26
	.byte	4
	.word	(.L157-.L26)/2
	.byte	19,40,22,26,3,19,138,40,4
	.word	(.L76-.L157)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('pit_all_close')
	.sect	'.debug_frame'
	.word	12
	.word	.L318,.L28,.L80-.L28
	.sdecl	'.debug_frame',debug,cluster('pit_disable')
	.sect	'.debug_frame'
	.word	12
	.word	.L318,.L30,.L81-.L30
	.sdecl	'.debug_frame',debug,cluster('pit_enable')
	.sect	'.debug_frame'
	.word	12
	.word	.L318,.L32,.L94-.L32
	.sdecl	'.debug_frame',debug,cluster('pit_init')
	.sect	'.debug_frame'
	.word	36
	.word	.L318,.L34,.L106-.L34
	.byte	4
	.word	(.L173-.L34)/2
	.byte	19,136,1,22,26,4,19,138,136,1,4
	.word	(.L106-.L173)/2
	.byte	19,0,8,26
	; Module end
