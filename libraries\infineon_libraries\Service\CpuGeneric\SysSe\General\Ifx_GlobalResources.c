/**
 * \file Ifx_GlobalResources.h
 * \brief Handling of global resources
 *
 * \copyright Copyright (c) 2013 Infineon Technologies AG. All rights reserved.
 *
 * $Date: 2014-02-27 20:08:36 GMT$
 *
 *                                 IMPORTANT NOTICE
 *
 *
 * Use of this file is subject to the terms of use agreed between (i) you or 
 * the company in which ordinary course of business you are acting and (ii) 
 * Infineon Technologies AG or its licensees. If and as long as no such 
 * terms of use are agreed, use of this file is subject to following:


 * Boost Software License - Version 1.0 - August 17th, 2003

 * Permission is hereby granted, free of charge, to any person or 
 * organization obtaining a copy of the software and accompanying 
 * documentation covered by this license (the "Software") to use, reproduce,
 * display, distribute, execute, and transmit the Software, and to prepare
 * derivative works of the Software, and to permit third-parties to whom the 
 * Software is furnished to do so, all subject to the following:

 * The copyright notices in the Software and this entire statement, including
 * the above license grant, this restriction and the following disclaimer, must
 * be included in all copies of the Software, in whole or in part, and all
 * derivative works of the Software, unless such copies or derivative works are
 * solely in the form of machine-executable object code generated by a source
 * language processor.

 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR 
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE, TITLE AND NON-INFRINGEMENT. IN NO EVENT
 * SHALL THE COPYRIGHT HOLDERS OR ANYONE DISTRIBUTING THE SOFTWARE BE LIABLE 
 * FOR ANY DAMAGES OR OTHER LIABILITY, WHETHER IN CONTRACT, TORT OR OTHERWISE,
 * ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
 * DEALINGS IN THE SOFTWARE.

 *
 */

#include "Ifx_GlobalResources.h"
/** \brief Global resource object */
typedef struct
{
    const Ifx_GlobalResources_Item *table;  /**< \brief Pointer to the global resource table */
    sint32                          size;   /**< \brief Size of the global resource table */
} Ifx_GlobalResources;

#if IFX_CFG_GLOBAL_RESOURCES_ENABLED

Ifx_GlobalResources ifx_GlobalResource;
#endif

void *Ifx_GlobalResources_get(sint32 id)
{
    void *result;

#if IFX_CFG_GLOBAL_RESOURCES_ENABLED

    if (id < ifx_GlobalResource.size)
    {
        result = ifx_GlobalResource.table[id].resource;
    }
    else
    {
        result = NULL_PTR;
    }

#else
    result = NULL_PTR;

#endif

    return result;
}


sint32 Ifx_GlobalResources_getIndex(void *resource)
{
    sint32 id = -1;

#if IFX_CFG_GLOBAL_RESOURCES_ENABLED
    int    i;

    for (i = 0; i < ifx_GlobalResource.size; i++)
    {
        if (resource == ifx_GlobalResource.table[i].resource)
        {
            id = i;
        }
        else
        {}
    }

#else
#endif

    return id;
}


const Ifx_GlobalResources_Item *Ifx_GlobalResources_getItem(sint32 id)
{
    const Ifx_GlobalResources_Item *result;

#if IFX_CFG_GLOBAL_RESOURCES_ENABLED

    if (id < ifx_GlobalResource.size)
    {
        result = &ifx_GlobalResource.table[id];
    }
    else
    {
        result = NULL_PTR;
    }

#else
    result = NULL_PTR;

#endif

    return result;
}


pchar Ifx_GlobalResources_getName(sint32 id)
{
    pchar name;

#if IFX_CFG_GLOBAL_RESOURCES_ENABLED

    if (id < ifx_GlobalResource.size)
    {
        name = ifx_GlobalResource.table[id].name;
    }
    else
    {
        name = "unknown";
    }

#else
    name = "unknown";

#endif

    return name;
}


boolean Ifx_GlobalResources_init(const Ifx_GlobalResources_Item *table, uint32 size)
{
    boolean result;

#if IFX_CFG_GLOBAL_RESOURCES_ENABLED
    ifx_GlobalResource.table = table;
    ifx_GlobalResource.size  = size;
    result                   = TRUE;
#else
    result                   = FALSE;

#endif

    return result;
}
