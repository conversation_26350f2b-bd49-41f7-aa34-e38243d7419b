	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc33064a --dep-file=IfxCpu_Irq.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2\\\\xf_asr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/infineon_libraries/iLLD/TC26B/Tricore/Cpu/Irq/IfxCpu_Irq.src ../libraries/infineon_libraries/iLLD/TC26B/Tricore/Cpu/Irq/IfxCpu_Irq.c"
	.compiler_name		"ctc"
	;source	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Cpu/Irq/IfxCpu_Irq.c'

	
$TC16X
	
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L3:
	.word	77520
	.half	3
	.word	.L4
	.byte	4
.L2:
	.byte	1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Cpu/Irq/IfxCpu_Irq.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L5
	.byte	2,1,1,3
	.word	234
	.byte	4
	.byte	'Ifx__jump_and_link',0,3,1,61,17,1,1,5
	.byte	'fun',0,1,61,43
	.word	237
	.byte	6,0,7
	.byte	'__fract',0,4,128,1,7
	.byte	'float',0,4,4,8
	.byte	'Ifx__float_to_fract',0,3,1,152,2,18
	.word	282
	.byte	1,1,5
	.byte	'a',0,1,152,2,44
	.word	294
	.byte	6,0,4
	.byte	'Ifx__stopPerfCounters',0,3,1,172,2,17,1,1,6,0,7
	.byte	'unsigned long long int',0,8,7,9
	.byte	'void',0,3
	.word	406
	.byte	8
	.byte	'__ld64',0,3,2,135,1,19
	.word	380
	.byte	1,1,5
	.byte	'addr',0,2,135,1,32
	.word	412
	.byte	6,0,4
	.byte	'__st64',0,3,2,143,1,17,1,1,5
	.byte	'addr',0,2,143,1,30
	.word	412
	.byte	5
	.byte	'value',0,2,143,1,43
	.word	380
	.byte	6,0,7
	.byte	'unsigned int',0,4,7,7
	.byte	'int',0,4,5,7
	.byte	'unsigned int',0,4,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON0_Bits',0,4,241,8,16,4,11
	.byte	'ENDINIT',0,4
	.word	521
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	521
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	521
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	521
	.byte	16,0,2,35,0,0,12,4,247,14,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	537
	.byte	4,2,35,0,0,7
	.byte	'unsigned char',0,1,8,7
	.byte	'unsigned short int',0,2,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON1_Bits',0,4,250,8,16,4,11
	.byte	'reserved_0',0,1
	.word	673
	.byte	2,6,2,35,0,11
	.byte	'IR0',0,1
	.word	673
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	673
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	673
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	673
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	673
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	673
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	673
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	673
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	690
	.byte	16,0,2,35,2,0,12,4,255,14,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	712
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_WDTCPU_SR_Bits',0,4,137,9,16,4,11
	.byte	'AE',0,1
	.word	673
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	673
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	673
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	673
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	673
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	673
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	673
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	673
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	673
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	673
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	690
	.byte	16,0,2,35,2,0,12,4,135,15,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	956
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_WDTCPU',0,4,175,15,25,12,13
	.byte	'CON0',0
	.word	633
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	916
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	1147
	.byte	4,2,35,8,0,14
	.word	1187
	.byte	3
	.word	1250
	.byte	4
	.byte	'IfxScuWdt_clearCpuEndinitInline',0,3,3,181,3,17,1,1,5
	.byte	'watchdog',0,3,181,3,65
	.word	1255
	.byte	5
	.byte	'password',0,3,181,3,82
	.word	690
	.byte	6,0,4
	.byte	'IfxScuWdt_setCpuEndinitInline',0,3,3,140,4,17,1,1,5
	.byte	'watchdog',0,3,140,4,63
	.word	1255
	.byte	5
	.byte	'password',0,3,140,4,80
	.word	690
	.byte	6,0,8
	.byte	'IfxScuWdt_getCpuWatchdogPasswordInline',0,3,3,227,3,19
	.word	690
	.byte	1,1,5
	.byte	'watchdog',0,3,227,3,74
	.word	1255
	.byte	6,0,10
	.byte	'_Ifx_P_OUT_Bits',0,6,143,3,16,4,11
	.byte	'P0',0,1
	.word	673
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	673
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	673
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	673
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	673
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	673
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	673
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	673
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	673
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	673
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	673
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	673
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	673
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	673
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	673
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	673
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	690
	.byte	16,0,2,35,2,0,12,6,181,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1485
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMR_Bits',0,6,169,2,16,4,11
	.byte	'PS0',0,1
	.word	673
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	673
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	673
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	673
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	673
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	673
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	673
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	673
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	673
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	673
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	673
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	673
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	673
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	673
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	673
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	673
	.byte	1,0,2,35,1,11
	.byte	'PCL0',0,1
	.word	673
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	673
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	673
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	673
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	673
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	673
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	673
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	673
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	673
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	673
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	673
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	673
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	673
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	673
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	673
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	673
	.byte	1,0,2,35,3,0,12,6,133,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1801
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ID_Bits',0,6,110,16,4,11
	.byte	'MODREV',0,1
	.word	673
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	673
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	690
	.byte	16,0,2,35,2,0,12,6,148,4,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2372
	.byte	4,2,35,0,0,15,4
	.word	673
	.byte	16,3,0,10
	.byte	'_Ifx_P_IOCR0_Bits',0,6,140,1,16,4,11
	.byte	'reserved_0',0,1
	.word	673
	.byte	3,5,2,35,0,11
	.byte	'PC0',0,1
	.word	673
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	673
	.byte	3,5,2,35,1,11
	.byte	'PC1',0,1
	.word	673
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	673
	.byte	3,5,2,35,2,11
	.byte	'PC2',0,1
	.word	673
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	673
	.byte	3,5,2,35,3,11
	.byte	'PC3',0,1
	.word	673
	.byte	5,0,2,35,3,0,12,6,164,4,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2500
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR4_Bits',0,6,166,1,16,4,11
	.byte	'reserved_0',0,1
	.word	673
	.byte	3,5,2,35,0,11
	.byte	'PC4',0,1
	.word	673
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	673
	.byte	3,5,2,35,1,11
	.byte	'PC5',0,1
	.word	673
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	673
	.byte	3,5,2,35,2,11
	.byte	'PC6',0,1
	.word	673
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	673
	.byte	3,5,2,35,3,11
	.byte	'PC7',0,1
	.word	673
	.byte	5,0,2,35,3,0,12,6,180,4,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2715
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR8_Bits',0,6,179,1,16,4,11
	.byte	'reserved_0',0,1
	.word	673
	.byte	3,5,2,35,0,11
	.byte	'PC8',0,1
	.word	673
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	673
	.byte	3,5,2,35,1,11
	.byte	'PC9',0,1
	.word	673
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	673
	.byte	3,5,2,35,2,11
	.byte	'PC10',0,1
	.word	673
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	673
	.byte	3,5,2,35,3,11
	.byte	'PC11',0,1
	.word	673
	.byte	5,0,2,35,3,0,12,6,188,4,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2930
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR12_Bits',0,6,153,1,16,4,11
	.byte	'reserved_0',0,1
	.word	673
	.byte	3,5,2,35,0,11
	.byte	'PC12',0,1
	.word	673
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	673
	.byte	3,5,2,35,1,11
	.byte	'PC13',0,1
	.word	673
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	673
	.byte	3,5,2,35,2,11
	.byte	'PC14',0,1
	.word	673
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	673
	.byte	3,5,2,35,3,11
	.byte	'PC15',0,1
	.word	673
	.byte	5,0,2,35,3,0,12,6,172,4,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3147
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IN_Bits',0,6,118,16,4,11
	.byte	'P0',0,1
	.word	673
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	673
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	673
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	673
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	673
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	673
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	673
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	673
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	673
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	673
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	673
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	673
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	673
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	673
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	673
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	673
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	690
	.byte	16,0,2,35,2,0,12,6,156,4,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3367
	.byte	4,2,35,0,0,15,24
	.word	673
	.byte	16,23,0,10
	.byte	'_Ifx_P_PDR0_Bits',0,6,205,3,16,4,11
	.byte	'PD0',0,1
	.word	673
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	673
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	673
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	673
	.byte	1,0,2,35,0,11
	.byte	'PD2',0,1
	.word	673
	.byte	3,5,2,35,1,11
	.byte	'PL2',0,1
	.word	673
	.byte	1,4,2,35,1,11
	.byte	'PD3',0,1
	.word	673
	.byte	3,1,2,35,1,11
	.byte	'PL3',0,1
	.word	673
	.byte	1,0,2,35,1,11
	.byte	'PD4',0,1
	.word	673
	.byte	3,5,2,35,2,11
	.byte	'PL4',0,1
	.word	673
	.byte	1,4,2,35,2,11
	.byte	'PD5',0,1
	.word	673
	.byte	3,1,2,35,2,11
	.byte	'PL5',0,1
	.word	673
	.byte	1,0,2,35,2,11
	.byte	'PD6',0,1
	.word	673
	.byte	3,5,2,35,3,11
	.byte	'PL6',0,1
	.word	673
	.byte	1,4,2,35,3,11
	.byte	'PD7',0,1
	.word	673
	.byte	3,1,2,35,3,11
	.byte	'PL7',0,1
	.word	673
	.byte	1,0,2,35,3,0,12,6,205,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3690
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PDR1_Bits',0,6,226,3,16,4,11
	.byte	'PD8',0,1
	.word	673
	.byte	3,5,2,35,0,11
	.byte	'PL8',0,1
	.word	673
	.byte	1,4,2,35,0,11
	.byte	'PD9',0,1
	.word	673
	.byte	3,1,2,35,0,11
	.byte	'PL9',0,1
	.word	673
	.byte	1,0,2,35,0,11
	.byte	'PD10',0,1
	.word	673
	.byte	3,5,2,35,1,11
	.byte	'PL10',0,1
	.word	673
	.byte	1,4,2,35,1,11
	.byte	'PD11',0,1
	.word	673
	.byte	3,1,2,35,1,11
	.byte	'PL11',0,1
	.word	673
	.byte	1,0,2,35,1,11
	.byte	'PD12',0,1
	.word	673
	.byte	3,5,2,35,2,11
	.byte	'PL12',0,1
	.word	673
	.byte	1,4,2,35,2,11
	.byte	'PD13',0,1
	.word	673
	.byte	3,1,2,35,2,11
	.byte	'PL13',0,1
	.word	673
	.byte	1,0,2,35,2,11
	.byte	'PD14',0,1
	.word	673
	.byte	3,5,2,35,3,11
	.byte	'PL14',0,1
	.word	673
	.byte	1,4,2,35,3,11
	.byte	'PD15',0,1
	.word	673
	.byte	3,1,2,35,3,11
	.byte	'PL15',0,1
	.word	673
	.byte	1,0,2,35,3,0,12,6,213,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3994
	.byte	4,2,35,0,0,15,8
	.word	673
	.byte	16,7,0,10
	.byte	'_Ifx_P_ESR_Bits',0,6,88,16,4,11
	.byte	'EN0',0,1
	.word	673
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	673
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	673
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	673
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	673
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	673
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	673
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	673
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	673
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	673
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	673
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	673
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	673
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	673
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	673
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	673
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	690
	.byte	16,0,2,35,2,0,12,6,140,4,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4319
	.byte	4,2,35,0,0,15,12
	.word	673
	.byte	16,11,0,10
	.byte	'_Ifx_P_PDISC_Bits',0,6,183,3,16,4,11
	.byte	'PDIS0',0,1
	.word	673
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	673
	.byte	1,6,2,35,0,11
	.byte	'PDIS2',0,1
	.word	673
	.byte	1,5,2,35,0,11
	.byte	'PDIS3',0,1
	.word	673
	.byte	1,4,2,35,0,11
	.byte	'PDIS4',0,1
	.word	673
	.byte	1,3,2,35,0,11
	.byte	'PDIS5',0,1
	.word	673
	.byte	1,2,2,35,0,11
	.byte	'PDIS6',0,1
	.word	673
	.byte	1,1,2,35,0,11
	.byte	'PDIS7',0,1
	.word	673
	.byte	1,0,2,35,0,11
	.byte	'PDIS8',0,1
	.word	673
	.byte	1,7,2,35,1,11
	.byte	'PDIS9',0,1
	.word	673
	.byte	1,6,2,35,1,11
	.byte	'PDIS10',0,1
	.word	673
	.byte	1,5,2,35,1,11
	.byte	'PDIS11',0,1
	.word	673
	.byte	1,4,2,35,1,11
	.byte	'PDIS12',0,1
	.word	673
	.byte	1,3,2,35,1,11
	.byte	'PDIS13',0,1
	.word	673
	.byte	1,2,2,35,1,11
	.byte	'PDIS14',0,1
	.word	673
	.byte	1,1,2,35,1,11
	.byte	'PDIS15',0,1
	.word	673
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	690
	.byte	16,0,2,35,2,0,12,6,197,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4659
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PCSR_Bits',0,6,165,3,16,4,11
	.byte	'SEL0',0,1
	.word	673
	.byte	1,7,2,35,0,11
	.byte	'SEL1',0,1
	.word	673
	.byte	1,6,2,35,0,11
	.byte	'SEL2',0,1
	.word	673
	.byte	1,5,2,35,0,11
	.byte	'SEL3',0,1
	.word	673
	.byte	1,4,2,35,0,11
	.byte	'SEL4',0,1
	.word	673
	.byte	1,3,2,35,0,11
	.byte	'SEL5',0,1
	.word	673
	.byte	1,2,2,35,0,11
	.byte	'SEL6',0,1
	.word	673
	.byte	1,1,2,35,0,11
	.byte	'SEL7',0,1
	.word	673
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	673
	.byte	2,6,2,35,1,11
	.byte	'SEL10',0,1
	.word	673
	.byte	1,5,2,35,1,11
	.byte	'SEL11',0,1
	.word	673
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	498
	.byte	19,1,2,35,0,11
	.byte	'LCK',0,1
	.word	673
	.byte	1,0,2,35,3,0,12,6,189,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5025
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR0_Bits',0,6,206,2,16,4,11
	.byte	'PS0',0,1
	.word	673
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	673
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	673
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	673
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	498
	.byte	28,0,2,35,0,0,12,6,149,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5311
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR4_Bits',0,6,227,2,16,4,11
	.byte	'reserved_0',0,1
	.word	673
	.byte	4,4,2,35,0,11
	.byte	'PS4',0,1
	.word	673
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	673
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	673
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	673
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	498
	.byte	24,0,2,35,0,0,12,6,165,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5458
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR8_Bits',0,6,238,2,16,4,11
	.byte	'reserved_0',0,1
	.word	673
	.byte	8,0,2,35,0,11
	.byte	'PS8',0,1
	.word	673
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	673
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	673
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	673
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	498
	.byte	20,0,2,35,0,0,12,6,173,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5627
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR12_Bits',0,6,216,2,16,4,11
	.byte	'reserved_0',0,2
	.word	690
	.byte	12,4,2,35,0,11
	.byte	'PS12',0,1
	.word	673
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	673
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	673
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	673
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	690
	.byte	16,0,2,35,2,0,12,6,157,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5799
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR0_Bits',0,6,232,1,16,4,11
	.byte	'reserved_0',0,2
	.word	690
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	673
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	673
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	673
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	673
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	690
	.byte	12,0,2,35,2,0,12,6,229,4,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5974
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR4_Bits',0,6,253,1,16,4,11
	.byte	'reserved_0',0,4
	.word	498
	.byte	20,12,2,35,0,11
	.byte	'PCL4',0,1
	.word	673
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	673
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	673
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	673
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	673
	.byte	8,0,2,35,3,0,12,6,245,4,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6148
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR8_Bits',0,6,136,2,16,4,11
	.byte	'reserved_0',0,4
	.word	498
	.byte	24,8,2,35,0,11
	.byte	'PCL8',0,1
	.word	673
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	673
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	673
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	673
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	673
	.byte	4,0,2,35,3,0,12,6,253,4,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6322
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR12_Bits',0,6,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	498
	.byte	28,4,2,35,0,11
	.byte	'PCL12',0,1
	.word	673
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	673
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	673
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	673
	.byte	1,0,2,35,3,0,12,6,237,4,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6498
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR_Bits',0,6,249,2,16,4,11
	.byte	'PS0',0,1
	.word	673
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	673
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	673
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	673
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	673
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	673
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	673
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	673
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	673
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	673
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	673
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	673
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	673
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	673
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	673
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	673
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	690
	.byte	16,0,2,35,2,0,12,6,141,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6654
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR_Bits',0,6,147,2,16,4,11
	.byte	'reserved_0',0,2
	.word	690
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	673
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	673
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	673
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	673
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	673
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	673
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	673
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	673
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	673
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	673
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	673
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	673
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	673
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	673
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	673
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	673
	.byte	1,0,2,35,3,0,12,6,221,4,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6987
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR0_Bits',0,6,192,1,16,4,11
	.byte	'reserved_0',0,1
	.word	673
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	673
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	498
	.byte	30,0,2,35,0,0,12,6,196,4,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7335
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_Bits',0,6,200,1,16,4,11
	.byte	'reserved_0',0,1
	.word	673
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	673
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	498
	.byte	30,0,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_P21_Bits',0,6,208,1,16,4,11
	.byte	'RDIS_CTRL',0,1
	.word	673
	.byte	1,7,2,35,0,11
	.byte	'RX_DIS',0,1
	.word	673
	.byte	1,6,2,35,0,11
	.byte	'TERM',0,1
	.word	673
	.byte	1,5,2,35,0,11
	.byte	'LRXTERM',0,1
	.word	673
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	498
	.byte	24,0,2,35,0,0,12,6,204,4,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7459
	.byte	4,2,35,0,13
	.byte	'B_P21',0
	.word	7543
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR2_Bits',0,6,218,1,16,4,11
	.byte	'reserved_0',0,1
	.word	673
	.byte	8,0,2,35,0,11
	.byte	'LVDSR',0,1
	.word	673
	.byte	1,7,2,35,1,11
	.byte	'LVDSRL',0,1
	.word	673
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	673
	.byte	2,4,2,35,1,11
	.byte	'TDIS_CTRL',0,1
	.word	673
	.byte	1,3,2,35,1,11
	.byte	'TX_DIS',0,1
	.word	673
	.byte	1,2,2,35,1,11
	.byte	'TX_PD',0,1
	.word	673
	.byte	1,1,2,35,1,11
	.byte	'TX_PWDPD',0,1
	.word	673
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	690
	.byte	16,0,2,35,2,0,12,6,213,4,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7723
	.byte	4,2,35,0,0,15,76
	.word	673
	.byte	16,75,0,10
	.byte	'_Ifx_P_ACCEN1_Bits',0,6,82,16,4,11
	.byte	'reserved_0',0,4
	.word	498
	.byte	32,0,2,35,0,0,12,6,132,4,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7976
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ACCEN0_Bits',0,6,45,16,4,11
	.byte	'EN0',0,1
	.word	673
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	673
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	673
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	673
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	673
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	673
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	673
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	673
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	673
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	673
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	673
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	673
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	673
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	673
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	673
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	673
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	673
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	673
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	673
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	673
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	673
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	673
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	673
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	673
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	673
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	673
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	673
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	673
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	673
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	673
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	673
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	673
	.byte	1,0,2,35,3,0,12,6,252,3,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8063
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P',0,6,229,5,25,128,2,13
	.byte	'OUT',0
	.word	1761
	.byte	4,2,35,0,13
	.byte	'OMR',0
	.word	2332
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	2451
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	2491
	.byte	4,2,35,12,13
	.byte	'IOCR0',0
	.word	2675
	.byte	4,2,35,16,13
	.byte	'IOCR4',0
	.word	2890
	.byte	4,2,35,20,13
	.byte	'IOCR8',0
	.word	3107
	.byte	4,2,35,24,13
	.byte	'IOCR12',0
	.word	3327
	.byte	4,2,35,28,13
	.byte	'reserved_20',0
	.word	2491
	.byte	4,2,35,32,13
	.byte	'IN',0
	.word	3641
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	3681
	.byte	24,2,35,40,13
	.byte	'PDR0',0
	.word	3954
	.byte	4,2,35,64,13
	.byte	'PDR1',0
	.word	4270
	.byte	4,2,35,68,13
	.byte	'reserved_48',0
	.word	4310
	.byte	8,2,35,72,13
	.byte	'ESR',0
	.word	4610
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	4650
	.byte	12,2,35,84,13
	.byte	'PDISC',0
	.word	4985
	.byte	4,2,35,96,13
	.byte	'PCSR',0
	.word	5271
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	4310
	.byte	8,2,35,104,13
	.byte	'OMSR0',0
	.word	5418
	.byte	4,2,35,112,13
	.byte	'OMSR4',0
	.word	5587
	.byte	4,2,35,116,13
	.byte	'OMSR8',0
	.word	5759
	.byte	4,2,35,120,13
	.byte	'OMSR12',0
	.word	5934
	.byte	4,2,35,124,13
	.byte	'OMCR0',0
	.word	6108
	.byte	4,3,35,128,1,13
	.byte	'OMCR4',0
	.word	6282
	.byte	4,3,35,132,1,13
	.byte	'OMCR8',0
	.word	6458
	.byte	4,3,35,136,1,13
	.byte	'OMCR12',0
	.word	6614
	.byte	4,3,35,140,1,13
	.byte	'OMSR',0
	.word	6947
	.byte	4,3,35,144,1,13
	.byte	'OMCR',0
	.word	7295
	.byte	4,3,35,148,1,13
	.byte	'reserved_98',0
	.word	4310
	.byte	8,3,35,152,1,13
	.byte	'LPCR0',0
	.word	7419
	.byte	4,3,35,160,1,13
	.byte	'LPCR1',0
	.word	7668
	.byte	4,3,35,164,1,13
	.byte	'LPCR2',0
	.word	7927
	.byte	4,3,35,168,1,13
	.byte	'reserved_A4',0
	.word	7967
	.byte	76,3,35,172,1,13
	.byte	'ACCEN1',0
	.word	8023
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	8590
	.byte	4,3,35,252,1,0,14
	.word	8630
	.byte	3
	.word	9233
	.byte	17,5,172,1,9,4,18
	.byte	'IfxPort_State_notChanged',0,0,18
	.byte	'IfxPort_State_high',0,1,18
	.byte	'IfxPort_State_low',0,128,128,4,18
	.byte	'IfxPort_State_toggled',0,129,128,4,0,4
	.byte	'IfxPort_setPinState',0,3,5,208,4,17,1,1,5
	.byte	'port',0,5,208,4,44
	.word	9238
	.byte	5
	.byte	'pinIndex',0,5,208,4,56
	.word	673
	.byte	5
	.byte	'action',0,5,208,4,80
	.word	9243
	.byte	6,0,17,8,156,1,9,1,18
	.byte	'IfxCpu_ResourceCpu_0',0,0,18
	.byte	'IfxCpu_ResourceCpu_1',0,1,18
	.byte	'IfxCpu_ResourceCpu_none',0,2,0,8
	.byte	'IfxCpu_getCoreIndex',0,3,7,141,6,31
	.word	9424
	.byte	1,1,6,0,8
	.byte	'IfxCpu_areInterruptsEnabled',0,3,7,139,5,20
	.word	673
	.byte	1,1,6,0,7
	.byte	'unsigned long int',0,4,7,8
	.byte	'IfxCpu_getPerformanceCounter',0,3,7,161,6,19
	.word	9579
	.byte	1,1,5
	.byte	'address',0,7,161,6,55
	.word	690
	.byte	6,0,8
	.byte	'IfxCpu_getPerformanceCounterStickyOverflow',0,3,7,190,6,20
	.word	673
	.byte	1,1,5
	.byte	'address',0,7,190,6,70
	.word	690
	.byte	6,0,4
	.byte	'IfxCpu_updatePerformanceCounter',0,3,7,172,8,17,1,1,5
	.byte	'address',0,7,172,8,56
	.word	9579
	.byte	5
	.byte	'count',0,7,172,8,72
	.word	9579
	.byte	19,6,0,0,10
	.byte	'_Ifx_SRC_SRCR_Bits',0,10,45,16,4,11
	.byte	'SRPN',0,1
	.word	673
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	673
	.byte	2,6,2,35,1,11
	.byte	'SRE',0,1
	.word	673
	.byte	1,5,2,35,1,11
	.byte	'TOS',0,1
	.word	673
	.byte	2,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	673
	.byte	3,0,2,35,1,11
	.byte	'ECC',0,1
	.word	673
	.byte	6,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	673
	.byte	2,0,2,35,2,11
	.byte	'SRR',0,1
	.word	673
	.byte	1,7,2,35,3,11
	.byte	'CLRR',0,1
	.word	673
	.byte	1,6,2,35,3,11
	.byte	'SETR',0,1
	.word	673
	.byte	1,5,2,35,3,11
	.byte	'IOV',0,1
	.word	673
	.byte	1,4,2,35,3,11
	.byte	'IOVCLR',0,1
	.word	673
	.byte	1,3,2,35,3,11
	.byte	'SWS',0,1
	.word	673
	.byte	1,2,2,35,3,11
	.byte	'SWSCLR',0,1
	.word	673
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	673
	.byte	1,0,2,35,3,0,12,10,70,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9810
	.byte	4,2,35,0,0,14
	.word	10100
	.byte	3
	.word	10139
	.byte	4
	.byte	'IfxSrc_clearRequest',0,3,9,250,1,17,1,1,5
	.byte	'src',0,9,250,1,60
	.word	10144
	.byte	6,0,20
	.word	242
	.byte	21
	.word	268
	.byte	6,0,20
	.word	303
	.byte	21
	.word	335
	.byte	6,0,20
	.word	348
	.byte	6,0,20
	.word	417
	.byte	21
	.word	436
	.byte	6,0,20
	.word	452
	.byte	21
	.word	467
	.byte	21
	.word	481
	.byte	6,0,20
	.word	1260
	.byte	21
	.word	1300
	.byte	21
	.word	1318
	.byte	6,0,20
	.word	1338
	.byte	21
	.word	1376
	.byte	21
	.word	1394
	.byte	6,0,20
	.word	1414
	.byte	21
	.word	1465
	.byte	6,0,20
	.word	9346
	.byte	21
	.word	9374
	.byte	21
	.word	9388
	.byte	21
	.word	9406
	.byte	6,0,20
	.word	9503
	.byte	6,0,20
	.word	9537
	.byte	6,0,20
	.word	9600
	.byte	21
	.word	9641
	.byte	6,0,20
	.word	9660
	.byte	21
	.word	9715
	.byte	6,0,20
	.word	9734
	.byte	21
	.word	9774
	.byte	21
	.word	9791
	.byte	19,6,0,0,20
	.word	10149
	.byte	21
	.word	10177
	.byte	6,0,7
	.byte	'short int',0,2,5,22
	.byte	'__wchar_t',0,11,1,1
	.word	10389
	.byte	22
	.byte	'__size_t',0,11,1,1
	.word	498
	.byte	22
	.byte	'__ptrdiff_t',0,11,1,1
	.word	514
	.byte	23,1,3
	.word	10457
	.byte	22
	.byte	'__codeptr',0,11,1,1
	.word	10459
	.byte	22
	.byte	'boolean',0,12,101,29
	.word	673
	.byte	22
	.byte	'uint8',0,12,105,29
	.word	673
	.byte	22
	.byte	'uint16',0,12,109,29
	.word	690
	.byte	22
	.byte	'uint32',0,12,113,29
	.word	9579
	.byte	22
	.byte	'uint64',0,12,118,29
	.word	380
	.byte	22
	.byte	'sint16',0,12,126,29
	.word	10389
	.byte	7
	.byte	'long int',0,4,5,22
	.byte	'sint32',0,12,131,1,29
	.word	10572
	.byte	7
	.byte	'long long int',0,8,5,22
	.byte	'sint64',0,12,138,1,29
	.word	10600
	.byte	22
	.byte	'float32',0,12,167,1,29
	.word	294
	.byte	22
	.byte	'pvoid',0,13,57,28
	.word	412
	.byte	22
	.byte	'Ifx_TickTime',0,13,79,28
	.word	10600
	.byte	22
	.byte	'Ifx_Priority',0,13,103,16
	.word	690
	.byte	17,13,130,1,9,1,18
	.byte	'Ifx_RxSel_a',0,0,18
	.byte	'Ifx_RxSel_b',0,1,18
	.byte	'Ifx_RxSel_c',0,2,18
	.byte	'Ifx_RxSel_d',0,3,18
	.byte	'Ifx_RxSel_e',0,4,18
	.byte	'Ifx_RxSel_f',0,5,18
	.byte	'Ifx_RxSel_g',0,6,18
	.byte	'Ifx_RxSel_h',0,7,0,22
	.byte	'Ifx_RxSel',0,13,140,1,3
	.word	10706
	.byte	10
	.byte	'_Ifx_CPU_A_Bits',0,14,45,16,4,11
	.byte	'ADDR',0,4
	.word	521
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_CPU_A_Bits',0,14,48,3
	.word	10844
	.byte	10
	.byte	'_Ifx_CPU_BIV_Bits',0,14,51,16,4,11
	.byte	'VSS',0,4
	.word	521
	.byte	1,31,2,35,0,11
	.byte	'BIV',0,4
	.word	521
	.byte	31,0,2,35,0,0,22
	.byte	'Ifx_CPU_BIV_Bits',0,14,55,3
	.word	10905
	.byte	10
	.byte	'_Ifx_CPU_BTV_Bits',0,14,58,16,4,11
	.byte	'reserved_0',0,4
	.word	521
	.byte	1,31,2,35,0,11
	.byte	'BTV',0,4
	.word	521
	.byte	31,0,2,35,0,0,22
	.byte	'Ifx_CPU_BTV_Bits',0,14,62,3
	.word	10984
	.byte	10
	.byte	'_Ifx_CPU_CCNT_Bits',0,14,65,16,4,11
	.byte	'CountValue',0,4
	.word	521
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	521
	.byte	1,0,2,35,0,0,22
	.byte	'Ifx_CPU_CCNT_Bits',0,14,69,3
	.word	11070
	.byte	10
	.byte	'_Ifx_CPU_CCTRL_Bits',0,14,72,16,4,11
	.byte	'CM',0,4
	.word	521
	.byte	1,31,2,35,0,11
	.byte	'CE',0,4
	.word	521
	.byte	1,30,2,35,0,11
	.byte	'M1',0,4
	.word	521
	.byte	3,27,2,35,0,11
	.byte	'M2',0,4
	.word	521
	.byte	3,24,2,35,0,11
	.byte	'M3',0,4
	.word	521
	.byte	3,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	521
	.byte	21,0,2,35,0,0,22
	.byte	'Ifx_CPU_CCTRL_Bits',0,14,80,3
	.word	11159
	.byte	10
	.byte	'_Ifx_CPU_COMPAT_Bits',0,14,83,16,4,11
	.byte	'reserved_0',0,4
	.word	521
	.byte	3,29,2,35,0,11
	.byte	'RM',0,4
	.word	521
	.byte	1,28,2,35,0,11
	.byte	'SP',0,4
	.word	521
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	521
	.byte	27,0,2,35,0,0,22
	.byte	'Ifx_CPU_COMPAT_Bits',0,14,89,3
	.word	11305
	.byte	10
	.byte	'_Ifx_CPU_CORE_ID_Bits',0,14,92,16,4,11
	.byte	'CORE_ID',0,4
	.word	521
	.byte	3,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	521
	.byte	29,0,2,35,0,0,22
	.byte	'Ifx_CPU_CORE_ID_Bits',0,14,96,3
	.word	11432
	.byte	10
	.byte	'_Ifx_CPU_CPR_L_Bits',0,14,99,16,4,11
	.byte	'reserved_0',0,4
	.word	521
	.byte	3,29,2,35,0,11
	.byte	'LOWBND',0,4
	.word	521
	.byte	29,0,2,35,0,0,22
	.byte	'Ifx_CPU_CPR_L_Bits',0,14,103,3
	.word	11530
	.byte	10
	.byte	'_Ifx_CPU_CPR_U_Bits',0,14,106,16,4,11
	.byte	'reserved_0',0,4
	.word	521
	.byte	3,29,2,35,0,11
	.byte	'UPPBND',0,4
	.word	521
	.byte	29,0,2,35,0,0,22
	.byte	'Ifx_CPU_CPR_U_Bits',0,14,110,3
	.word	11623
	.byte	10
	.byte	'_Ifx_CPU_CPU_ID_Bits',0,14,113,16,4,11
	.byte	'MODREV',0,4
	.word	521
	.byte	8,24,2,35,0,11
	.byte	'MOD_32B',0,4
	.word	521
	.byte	8,16,2,35,0,11
	.byte	'MOD',0,4
	.word	521
	.byte	16,0,2,35,0,0,22
	.byte	'Ifx_CPU_CPU_ID_Bits',0,14,118,3
	.word	11716
	.byte	10
	.byte	'_Ifx_CPU_CPXE_Bits',0,14,121,16,4,11
	.byte	'XE',0,4
	.word	521
	.byte	8,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	521
	.byte	24,0,2,35,0,0,22
	.byte	'Ifx_CPU_CPXE_Bits',0,14,125,3
	.word	11823
	.byte	10
	.byte	'_Ifx_CPU_CREVT_Bits',0,14,128,1,16,4,11
	.byte	'EVTA',0,4
	.word	521
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	521
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	521
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	521
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	521
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	521
	.byte	24,0,2,35,0,0,22
	.byte	'Ifx_CPU_CREVT_Bits',0,14,136,1,3
	.word	11910
	.byte	10
	.byte	'_Ifx_CPU_CUS_ID_Bits',0,14,139,1,16,4,11
	.byte	'CID',0,4
	.word	521
	.byte	3,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	521
	.byte	29,0,2,35,0,0,22
	.byte	'Ifx_CPU_CUS_ID_Bits',0,14,143,1,3
	.word	12064
	.byte	10
	.byte	'_Ifx_CPU_D_Bits',0,14,146,1,16,4,11
	.byte	'DATA',0,4
	.word	521
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_CPU_D_Bits',0,14,149,1,3
	.word	12158
	.byte	10
	.byte	'_Ifx_CPU_DATR_Bits',0,14,152,1,16,4,11
	.byte	'reserved_0',0,4
	.word	521
	.byte	3,29,2,35,0,11
	.byte	'SBE',0,4
	.word	521
	.byte	1,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	521
	.byte	5,23,2,35,0,11
	.byte	'CWE',0,4
	.word	521
	.byte	1,22,2,35,0,11
	.byte	'CFE',0,4
	.word	521
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	521
	.byte	3,18,2,35,0,11
	.byte	'SOE',0,4
	.word	521
	.byte	1,17,2,35,0,11
	.byte	'SME',0,4
	.word	521
	.byte	1,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	521
	.byte	16,0,2,35,0,0,22
	.byte	'Ifx_CPU_DATR_Bits',0,14,163,1,3
	.word	12221
	.byte	10
	.byte	'_Ifx_CPU_DBGSR_Bits',0,14,166,1,16,4,11
	.byte	'DE',0,4
	.word	521
	.byte	1,31,2,35,0,11
	.byte	'HALT',0,4
	.word	521
	.byte	2,29,2,35,0,11
	.byte	'SIH',0,4
	.word	521
	.byte	1,28,2,35,0,11
	.byte	'SUSP',0,4
	.word	521
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	521
	.byte	1,26,2,35,0,11
	.byte	'PREVSUSP',0,4
	.word	521
	.byte	1,25,2,35,0,11
	.byte	'PEVT',0,4
	.word	521
	.byte	1,24,2,35,0,11
	.byte	'EVTSRC',0,4
	.word	521
	.byte	5,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	521
	.byte	19,0,2,35,0,0,22
	.byte	'Ifx_CPU_DBGSR_Bits',0,14,177,1,3
	.word	12439
	.byte	10
	.byte	'_Ifx_CPU_DBGTCR_Bits',0,14,180,1,16,4,11
	.byte	'DTA',0,4
	.word	521
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	521
	.byte	31,0,2,35,0,0,22
	.byte	'Ifx_CPU_DBGTCR_Bits',0,14,184,1,3
	.word	12654
	.byte	10
	.byte	'_Ifx_CPU_DCON0_Bits',0,14,187,1,16,4,11
	.byte	'reserved_0',0,4
	.word	521
	.byte	1,31,2,35,0,11
	.byte	'DCBYP',0,4
	.word	521
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	521
	.byte	30,0,2,35,0,0,22
	.byte	'Ifx_CPU_DCON0_Bits',0,14,192,1,3
	.word	12748
	.byte	10
	.byte	'_Ifx_CPU_DCON2_Bits',0,14,195,1,16,4,11
	.byte	'DCACHE_SZE',0,4
	.word	521
	.byte	16,16,2,35,0,11
	.byte	'DSCRATCH_SZE',0,4
	.word	521
	.byte	16,0,2,35,0,0,22
	.byte	'Ifx_CPU_DCON2_Bits',0,14,199,1,3
	.word	12864
	.byte	10
	.byte	'_Ifx_CPU_DCX_Bits',0,14,202,1,16,4,11
	.byte	'reserved_0',0,4
	.word	521
	.byte	6,26,2,35,0,11
	.byte	'DCXValue',0,4
	.word	521
	.byte	26,0,2,35,0,0,22
	.byte	'Ifx_CPU_DCX_Bits',0,14,206,1,3
	.word	12965
	.byte	10
	.byte	'_Ifx_CPU_DEADD_Bits',0,14,209,1,16,4,11
	.byte	'ERROR_ADDRESS',0,4
	.word	521
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_CPU_DEADD_Bits',0,14,212,1,3
	.word	13058
	.byte	10
	.byte	'_Ifx_CPU_DIEAR_Bits',0,14,215,1,16,4,11
	.byte	'TA',0,4
	.word	521
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_CPU_DIEAR_Bits',0,14,218,1,3
	.word	13138
	.byte	10
	.byte	'_Ifx_CPU_DIETR_Bits',0,14,221,1,16,4,11
	.byte	'IED',0,4
	.word	521
	.byte	1,31,2,35,0,11
	.byte	'IE_T',0,4
	.word	521
	.byte	1,30,2,35,0,11
	.byte	'IE_C',0,4
	.word	521
	.byte	1,29,2,35,0,11
	.byte	'IE_S',0,4
	.word	521
	.byte	1,28,2,35,0,11
	.byte	'IE_BI',0,4
	.word	521
	.byte	1,27,2,35,0,11
	.byte	'E_INFO',0,4
	.word	521
	.byte	6,21,2,35,0,11
	.byte	'IE_DUAL',0,4
	.word	521
	.byte	1,20,2,35,0,11
	.byte	'IE_SP',0,4
	.word	521
	.byte	1,19,2,35,0,11
	.byte	'IE_BS',0,4
	.word	521
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	521
	.byte	18,0,2,35,0,0,22
	.byte	'Ifx_CPU_DIETR_Bits',0,14,233,1,3
	.word	13207
	.byte	10
	.byte	'_Ifx_CPU_DMS_Bits',0,14,236,1,16,4,11
	.byte	'reserved_0',0,4
	.word	521
	.byte	1,31,2,35,0,11
	.byte	'DMSValue',0,4
	.word	521
	.byte	31,0,2,35,0,0,22
	.byte	'Ifx_CPU_DMS_Bits',0,14,240,1,3
	.word	13436
	.byte	10
	.byte	'_Ifx_CPU_DPR_L_Bits',0,14,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	521
	.byte	3,29,2,35,0,11
	.byte	'LOWBND',0,4
	.word	521
	.byte	29,0,2,35,0,0,22
	.byte	'Ifx_CPU_DPR_L_Bits',0,14,247,1,3
	.word	13529
	.byte	10
	.byte	'_Ifx_CPU_DPR_U_Bits',0,14,250,1,16,4,11
	.byte	'reserved_0',0,4
	.word	521
	.byte	3,29,2,35,0,11
	.byte	'UPPBND',0,4
	.word	521
	.byte	29,0,2,35,0,0,22
	.byte	'Ifx_CPU_DPR_U_Bits',0,14,254,1,3
	.word	13624
	.byte	10
	.byte	'_Ifx_CPU_DPRE_Bits',0,14,129,2,16,4,11
	.byte	'RE',0,4
	.word	521
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	521
	.byte	16,0,2,35,0,0,22
	.byte	'Ifx_CPU_DPRE_Bits',0,14,133,2,3
	.word	13719
	.byte	10
	.byte	'_Ifx_CPU_DPWE_Bits',0,14,136,2,16,4,11
	.byte	'WE',0,4
	.word	521
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	521
	.byte	16,0,2,35,0,0,22
	.byte	'Ifx_CPU_DPWE_Bits',0,14,140,2,3
	.word	13809
	.byte	10
	.byte	'_Ifx_CPU_DSTR_Bits',0,14,143,2,16,4,11
	.byte	'SRE',0,4
	.word	521
	.byte	1,31,2,35,0,11
	.byte	'GAE',0,4
	.word	521
	.byte	1,30,2,35,0,11
	.byte	'LBE',0,4
	.word	521
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	521
	.byte	3,26,2,35,0,11
	.byte	'CRE',0,4
	.word	521
	.byte	1,25,2,35,0,11
	.byte	'reserved_7',0,4
	.word	521
	.byte	7,18,2,35,0,11
	.byte	'DTME',0,4
	.word	521
	.byte	1,17,2,35,0,11
	.byte	'LOE',0,4
	.word	521
	.byte	1,16,2,35,0,11
	.byte	'SDE',0,4
	.word	521
	.byte	1,15,2,35,0,11
	.byte	'SCE',0,4
	.word	521
	.byte	1,14,2,35,0,11
	.byte	'CAC',0,4
	.word	521
	.byte	1,13,2,35,0,11
	.byte	'MPE',0,4
	.word	521
	.byte	1,12,2,35,0,11
	.byte	'CLE',0,4
	.word	521
	.byte	1,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	521
	.byte	3,8,2,35,0,11
	.byte	'ALN',0,4
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	521
	.byte	7,0,2,35,0,0,22
	.byte	'Ifx_CPU_DSTR_Bits',0,14,161,2,3
	.word	13899
	.byte	10
	.byte	'_Ifx_CPU_EXEVT_Bits',0,14,164,2,16,4,11
	.byte	'EVTA',0,4
	.word	521
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	521
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	521
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	521
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	521
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	521
	.byte	24,0,2,35,0,0,22
	.byte	'Ifx_CPU_EXEVT_Bits',0,14,172,2,3
	.word	14223
	.byte	10
	.byte	'_Ifx_CPU_FCX_Bits',0,14,175,2,16,4,11
	.byte	'FCXO',0,4
	.word	521
	.byte	16,16,2,35,0,11
	.byte	'FCXS',0,4
	.word	521
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	521
	.byte	12,0,2,35,0,0,22
	.byte	'Ifx_CPU_FCX_Bits',0,14,180,2,3
	.word	14377
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_CON_Bits',0,14,183,2,16,4,11
	.byte	'TST',0,4
	.word	521
	.byte	1,31,2,35,0,11
	.byte	'TCL',0,4
	.word	521
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	521
	.byte	6,24,2,35,0,11
	.byte	'RM',0,4
	.word	521
	.byte	2,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	521
	.byte	8,14,2,35,0,11
	.byte	'FXE',0,4
	.word	521
	.byte	1,13,2,35,0,11
	.byte	'FUE',0,4
	.word	521
	.byte	1,12,2,35,0,11
	.byte	'FZE',0,4
	.word	521
	.byte	1,11,2,35,0,11
	.byte	'FVE',0,4
	.word	521
	.byte	1,10,2,35,0,11
	.byte	'FIE',0,4
	.word	521
	.byte	1,9,2,35,0,11
	.byte	'reserved_23',0,4
	.word	521
	.byte	3,6,2,35,0,11
	.byte	'FX',0,4
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'FU',0,4
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'FZ',0,4
	.word	521
	.byte	1,3,2,35,0,11
	.byte	'FV',0,4
	.word	521
	.byte	1,2,2,35,0,11
	.byte	'FI',0,4
	.word	521
	.byte	1,1,2,35,0,11
	.byte	'reserved_31',0,4
	.word	521
	.byte	1,0,2,35,0,0,22
	.byte	'Ifx_CPU_FPU_TRAP_CON_Bits',0,14,202,2,3
	.word	14483
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_OPC_Bits',0,14,205,2,16,4,11
	.byte	'OPC',0,4
	.word	521
	.byte	8,24,2,35,0,11
	.byte	'FMT',0,4
	.word	521
	.byte	1,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	521
	.byte	7,16,2,35,0,11
	.byte	'DREG',0,4
	.word	521
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	521
	.byte	12,0,2,35,0,0,22
	.byte	'Ifx_CPU_FPU_TRAP_OPC_Bits',0,14,212,2,3
	.word	14832
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_PC_Bits',0,14,215,2,16,4,11
	.byte	'PC',0,4
	.word	521
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_CPU_FPU_TRAP_PC_Bits',0,14,218,2,3
	.word	14992
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC1_Bits',0,14,221,2,16,4,11
	.byte	'SRC1',0,4
	.word	521
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_CPU_FPU_TRAP_SRC1_Bits',0,14,224,2,3
	.word	15073
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC2_Bits',0,14,227,2,16,4,11
	.byte	'SRC2',0,4
	.word	521
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_CPU_FPU_TRAP_SRC2_Bits',0,14,230,2,3
	.word	15160
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC3_Bits',0,14,233,2,16,4,11
	.byte	'SRC3',0,4
	.word	521
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_CPU_FPU_TRAP_SRC3_Bits',0,14,236,2,3
	.word	15247
	.byte	10
	.byte	'_Ifx_CPU_ICNT_Bits',0,14,239,2,16,4,11
	.byte	'CountValue',0,4
	.word	521
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	521
	.byte	1,0,2,35,0,0,22
	.byte	'Ifx_CPU_ICNT_Bits',0,14,243,2,3
	.word	15334
	.byte	10
	.byte	'_Ifx_CPU_ICR_Bits',0,14,246,2,16,4,11
	.byte	'CCPN',0,4
	.word	521
	.byte	10,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	521
	.byte	5,17,2,35,0,11
	.byte	'IE',0,4
	.word	521
	.byte	1,16,2,35,0,11
	.byte	'PIPN',0,4
	.word	521
	.byte	10,6,2,35,0,11
	.byte	'reserved_26',0,4
	.word	521
	.byte	6,0,2,35,0,0,22
	.byte	'Ifx_CPU_ICR_Bits',0,14,253,2,3
	.word	15425
	.byte	10
	.byte	'_Ifx_CPU_ISP_Bits',0,14,128,3,16,4,11
	.byte	'ISP',0,4
	.word	521
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_CPU_ISP_Bits',0,14,131,3,3
	.word	15568
	.byte	10
	.byte	'_Ifx_CPU_LCX_Bits',0,14,134,3,16,4,11
	.byte	'LCXO',0,4
	.word	521
	.byte	16,16,2,35,0,11
	.byte	'LCXS',0,4
	.word	521
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	521
	.byte	12,0,2,35,0,0,22
	.byte	'Ifx_CPU_LCX_Bits',0,14,139,3,3
	.word	15634
	.byte	10
	.byte	'_Ifx_CPU_M1CNT_Bits',0,14,142,3,16,4,11
	.byte	'CountValue',0,4
	.word	521
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	521
	.byte	1,0,2,35,0,0,22
	.byte	'Ifx_CPU_M1CNT_Bits',0,14,146,3,3
	.word	15740
	.byte	10
	.byte	'_Ifx_CPU_M2CNT_Bits',0,14,149,3,16,4,11
	.byte	'CountValue',0,4
	.word	521
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	521
	.byte	1,0,2,35,0,0,22
	.byte	'Ifx_CPU_M2CNT_Bits',0,14,153,3,3
	.word	15833
	.byte	10
	.byte	'_Ifx_CPU_M3CNT_Bits',0,14,156,3,16,4,11
	.byte	'CountValue',0,4
	.word	521
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	521
	.byte	1,0,2,35,0,0,22
	.byte	'Ifx_CPU_M3CNT_Bits',0,14,160,3,3
	.word	15926
	.byte	10
	.byte	'_Ifx_CPU_PC_Bits',0,14,163,3,16,4,11
	.byte	'reserved_0',0,4
	.word	521
	.byte	1,31,2,35,0,11
	.byte	'PC',0,4
	.word	521
	.byte	31,0,2,35,0,0,22
	.byte	'Ifx_CPU_PC_Bits',0,14,167,3,3
	.word	16019
	.byte	10
	.byte	'_Ifx_CPU_PCON0_Bits',0,14,170,3,16,4,11
	.byte	'reserved_0',0,4
	.word	521
	.byte	1,31,2,35,0,11
	.byte	'PCBYP',0,4
	.word	521
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	521
	.byte	30,0,2,35,0,0,22
	.byte	'Ifx_CPU_PCON0_Bits',0,14,175,3,3
	.word	16104
	.byte	10
	.byte	'_Ifx_CPU_PCON1_Bits',0,14,178,3,16,4,11
	.byte	'PCINV',0,4
	.word	521
	.byte	1,31,2,35,0,11
	.byte	'PBINV',0,4
	.word	521
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	521
	.byte	30,0,2,35,0,0,22
	.byte	'Ifx_CPU_PCON1_Bits',0,14,183,3,3
	.word	16220
	.byte	10
	.byte	'_Ifx_CPU_PCON2_Bits',0,14,186,3,16,4,11
	.byte	'PCACHE_SZE',0,4
	.word	521
	.byte	16,16,2,35,0,11
	.byte	'PSCRATCH_SZE',0,4
	.word	521
	.byte	16,0,2,35,0,0,22
	.byte	'Ifx_CPU_PCON2_Bits',0,14,190,3,3
	.word	16331
	.byte	10
	.byte	'_Ifx_CPU_PCXI_Bits',0,14,193,3,16,4,11
	.byte	'PCXO',0,4
	.word	521
	.byte	16,16,2,35,0,11
	.byte	'PCXS',0,4
	.word	521
	.byte	4,12,2,35,0,11
	.byte	'UL',0,4
	.word	521
	.byte	1,11,2,35,0,11
	.byte	'PIE',0,4
	.word	521
	.byte	1,10,2,35,0,11
	.byte	'PCPN',0,4
	.word	521
	.byte	10,0,2,35,0,0,22
	.byte	'Ifx_CPU_PCXI_Bits',0,14,200,3,3
	.word	16432
	.byte	10
	.byte	'_Ifx_CPU_PIEAR_Bits',0,14,203,3,16,4,11
	.byte	'TA',0,4
	.word	521
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_CPU_PIEAR_Bits',0,14,206,3,3
	.word	16562
	.byte	10
	.byte	'_Ifx_CPU_PIETR_Bits',0,14,209,3,16,4,11
	.byte	'IED',0,4
	.word	521
	.byte	1,31,2,35,0,11
	.byte	'IE_T',0,4
	.word	521
	.byte	1,30,2,35,0,11
	.byte	'IE_C',0,4
	.word	521
	.byte	1,29,2,35,0,11
	.byte	'IE_S',0,4
	.word	521
	.byte	1,28,2,35,0,11
	.byte	'IE_BI',0,4
	.word	521
	.byte	1,27,2,35,0,11
	.byte	'E_INFO',0,4
	.word	521
	.byte	6,21,2,35,0,11
	.byte	'IE_DUAL',0,4
	.word	521
	.byte	1,20,2,35,0,11
	.byte	'IE_SP',0,4
	.word	521
	.byte	1,19,2,35,0,11
	.byte	'IE_BS',0,4
	.word	521
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	521
	.byte	18,0,2,35,0,0,22
	.byte	'Ifx_CPU_PIETR_Bits',0,14,221,3,3
	.word	16631
	.byte	10
	.byte	'_Ifx_CPU_PMA0_Bits',0,14,224,3,16,4,11
	.byte	'reserved_0',0,4
	.word	521
	.byte	13,19,2,35,0,11
	.byte	'DAC',0,4
	.word	521
	.byte	3,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	521
	.byte	16,0,2,35,0,0,22
	.byte	'Ifx_CPU_PMA0_Bits',0,14,229,3,3
	.word	16860
	.byte	10
	.byte	'_Ifx_CPU_PMA1_Bits',0,14,232,3,16,4,11
	.byte	'reserved_0',0,4
	.word	521
	.byte	14,18,2,35,0,11
	.byte	'CAC',0,4
	.word	521
	.byte	2,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	521
	.byte	16,0,2,35,0,0,22
	.byte	'Ifx_CPU_PMA1_Bits',0,14,237,3,3
	.word	16973
	.byte	10
	.byte	'_Ifx_CPU_PMA2_Bits',0,14,240,3,16,4,11
	.byte	'PSI',0,4
	.word	521
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	521
	.byte	16,0,2,35,0,0,22
	.byte	'Ifx_CPU_PMA2_Bits',0,14,244,3,3
	.word	17086
	.byte	10
	.byte	'_Ifx_CPU_PSTR_Bits',0,14,247,3,16,4,11
	.byte	'FRE',0,4
	.word	521
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	521
	.byte	1,30,2,35,0,11
	.byte	'FBE',0,4
	.word	521
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	521
	.byte	9,20,2,35,0,11
	.byte	'FPE',0,4
	.word	521
	.byte	1,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	521
	.byte	1,18,2,35,0,11
	.byte	'FME',0,4
	.word	521
	.byte	1,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	521
	.byte	17,0,2,35,0,0,22
	.byte	'Ifx_CPU_PSTR_Bits',0,14,129,4,3
	.word	17177
	.byte	10
	.byte	'_Ifx_CPU_PSW_Bits',0,14,132,4,16,4,11
	.byte	'CDC',0,4
	.word	521
	.byte	7,25,2,35,0,11
	.byte	'CDE',0,4
	.word	521
	.byte	1,24,2,35,0,11
	.byte	'GW',0,4
	.word	521
	.byte	1,23,2,35,0,11
	.byte	'IS',0,4
	.word	521
	.byte	1,22,2,35,0,11
	.byte	'IO',0,4
	.word	521
	.byte	2,20,2,35,0,11
	.byte	'PRS',0,4
	.word	521
	.byte	2,18,2,35,0,11
	.byte	'S',0,4
	.word	521
	.byte	1,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	521
	.byte	12,5,2,35,0,11
	.byte	'SAV',0,4
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'AV',0,4
	.word	521
	.byte	1,3,2,35,0,11
	.byte	'SV',0,4
	.word	521
	.byte	1,2,2,35,0,11
	.byte	'V',0,4
	.word	521
	.byte	1,1,2,35,0,11
	.byte	'C',0,4
	.word	521
	.byte	1,0,2,35,0,0,22
	.byte	'Ifx_CPU_PSW_Bits',0,14,147,4,3
	.word	17380
	.byte	10
	.byte	'_Ifx_CPU_SEGEN_Bits',0,14,150,4,16,4,11
	.byte	'ADFLIP',0,4
	.word	521
	.byte	8,24,2,35,0,11
	.byte	'ADTYPE',0,4
	.word	521
	.byte	2,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	521
	.byte	21,1,2,35,0,11
	.byte	'AE',0,4
	.word	521
	.byte	1,0,2,35,0,0,22
	.byte	'Ifx_CPU_SEGEN_Bits',0,14,156,4,3
	.word	17623
	.byte	10
	.byte	'_Ifx_CPU_SMACON_Bits',0,14,159,4,16,4,11
	.byte	'PC',0,4
	.word	521
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	521
	.byte	1,30,2,35,0,11
	.byte	'PT',0,4
	.word	521
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	521
	.byte	5,24,2,35,0,11
	.byte	'DC',0,4
	.word	521
	.byte	1,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	521
	.byte	1,22,2,35,0,11
	.byte	'DT',0,4
	.word	521
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	521
	.byte	13,8,2,35,0,11
	.byte	'IODT',0,4
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	521
	.byte	7,0,2,35,0,0,22
	.byte	'Ifx_CPU_SMACON_Bits',0,14,171,4,3
	.word	17751
	.byte	10
	.byte	'_Ifx_CPU_SPROT_ACCENA_Bits',0,14,174,4,16,4,11
	.byte	'EN',0,4
	.word	498
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_CPU_SPROT_ACCENA_Bits',0,14,177,4,3
	.word	17992
	.byte	10
	.byte	'_Ifx_CPU_SPROT_ACCENB_Bits',0,14,180,4,16,4,11
	.byte	'reserved_0',0,4
	.word	498
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_CPU_SPROT_ACCENB_Bits',0,14,183,4,3
	.word	18075
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,14,186,4,16,4,11
	.byte	'EN',0,4
	.word	498
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,14,189,4,3
	.word	18166
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,14,192,4,16,4,11
	.byte	'reserved_0',0,4
	.word	498
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,14,195,4,3
	.word	18257
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_LA_Bits',0,14,198,4,16,4,11
	.byte	'reserved_0',0,1
	.word	673
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	498
	.byte	27,0,2,35,0,0,22
	.byte	'Ifx_CPU_SPROT_RGN_LA_Bits',0,14,202,4,3
	.word	18356
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_UA_Bits',0,14,205,4,16,4,11
	.byte	'reserved_0',0,1
	.word	673
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	498
	.byte	27,0,2,35,0,0,22
	.byte	'Ifx_CPU_SPROT_RGN_UA_Bits',0,14,209,4,3
	.word	18463
	.byte	10
	.byte	'_Ifx_CPU_SWEVT_Bits',0,14,212,4,16,4,11
	.byte	'EVTA',0,4
	.word	521
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	521
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	521
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	521
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	521
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	521
	.byte	24,0,2,35,0,0,22
	.byte	'Ifx_CPU_SWEVT_Bits',0,14,220,4,3
	.word	18570
	.byte	10
	.byte	'_Ifx_CPU_SYSCON_Bits',0,14,223,4,16,4,11
	.byte	'FCDSF',0,4
	.word	521
	.byte	1,31,2,35,0,11
	.byte	'PROTEN',0,4
	.word	521
	.byte	1,30,2,35,0,11
	.byte	'TPROTEN',0,4
	.word	521
	.byte	1,29,2,35,0,11
	.byte	'IS',0,4
	.word	521
	.byte	1,28,2,35,0,11
	.byte	'IT',0,4
	.word	521
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	521
	.byte	27,0,2,35,0,0,22
	.byte	'Ifx_CPU_SYSCON_Bits',0,14,231,4,3
	.word	18724
	.byte	10
	.byte	'_Ifx_CPU_TASK_ASI_Bits',0,14,234,4,16,4,11
	.byte	'ASI',0,4
	.word	521
	.byte	5,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	521
	.byte	27,0,2,35,0,0,22
	.byte	'Ifx_CPU_TASK_ASI_Bits',0,14,238,4,3
	.word	18885
	.byte	10
	.byte	'_Ifx_CPU_TPS_CON_Bits',0,14,241,4,16,4,11
	.byte	'TEXP0',0,4
	.word	521
	.byte	1,31,2,35,0,11
	.byte	'TEXP1',0,4
	.word	521
	.byte	1,30,2,35,0,11
	.byte	'TEXP2',0,4
	.word	521
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	521
	.byte	13,16,2,35,0,11
	.byte	'TTRAP',0,4
	.word	521
	.byte	1,15,2,35,0,11
	.byte	'reserved_17',0,4
	.word	521
	.byte	15,0,2,35,0,0,22
	.byte	'Ifx_CPU_TPS_CON_Bits',0,14,249,4,3
	.word	18983
	.byte	10
	.byte	'_Ifx_CPU_TPS_TIMER_Bits',0,14,252,4,16,4,11
	.byte	'Timer',0,4
	.word	521
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_CPU_TPS_TIMER_Bits',0,14,255,4,3
	.word	19155
	.byte	10
	.byte	'_Ifx_CPU_TR_ADR_Bits',0,14,130,5,16,4,11
	.byte	'ADDR',0,4
	.word	521
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_CPU_TR_ADR_Bits',0,14,133,5,3
	.word	19235
	.byte	10
	.byte	'_Ifx_CPU_TR_EVT_Bits',0,14,136,5,16,4,11
	.byte	'EVTA',0,4
	.word	521
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	521
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	521
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	521
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	521
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	521
	.byte	4,20,2,35,0,11
	.byte	'TYP',0,4
	.word	521
	.byte	1,19,2,35,0,11
	.byte	'RNG',0,4
	.word	521
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	521
	.byte	1,17,2,35,0,11
	.byte	'ASI_EN',0,4
	.word	521
	.byte	1,16,2,35,0,11
	.byte	'ASI',0,4
	.word	521
	.byte	5,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	521
	.byte	6,5,2,35,0,11
	.byte	'AST',0,4
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'ALD',0,4
	.word	521
	.byte	1,3,2,35,0,11
	.byte	'reserved_29',0,4
	.word	521
	.byte	3,0,2,35,0,0,22
	.byte	'Ifx_CPU_TR_EVT_Bits',0,14,153,5,3
	.word	19308
	.byte	10
	.byte	'_Ifx_CPU_TRIG_ACC_Bits',0,14,156,5,16,4,11
	.byte	'T0',0,4
	.word	521
	.byte	1,31,2,35,0,11
	.byte	'T1',0,4
	.word	521
	.byte	1,30,2,35,0,11
	.byte	'T2',0,4
	.word	521
	.byte	1,29,2,35,0,11
	.byte	'T3',0,4
	.word	521
	.byte	1,28,2,35,0,11
	.byte	'T4',0,4
	.word	521
	.byte	1,27,2,35,0,11
	.byte	'T5',0,4
	.word	521
	.byte	1,26,2,35,0,11
	.byte	'T6',0,4
	.word	521
	.byte	1,25,2,35,0,11
	.byte	'T7',0,4
	.word	521
	.byte	1,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	521
	.byte	24,0,2,35,0,0,22
	.byte	'Ifx_CPU_TRIG_ACC_Bits',0,14,167,5,3
	.word	19626
	.byte	12,14,175,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10844
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_A',0,14,180,5,3
	.word	19821
	.byte	12,14,183,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10905
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_BIV',0,14,188,5,3
	.word	19880
	.byte	12,14,191,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10984
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_BTV',0,14,196,5,3
	.word	19941
	.byte	12,14,199,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11070
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_CCNT',0,14,204,5,3
	.word	20002
	.byte	12,14,207,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11159
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_CCTRL',0,14,212,5,3
	.word	20064
	.byte	12,14,215,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11305
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_COMPAT',0,14,220,5,3
	.word	20127
	.byte	12,14,223,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11432
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_CORE_ID',0,14,228,5,3
	.word	20191
	.byte	12,14,231,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11530
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_CPR_L',0,14,236,5,3
	.word	20256
	.byte	12,14,239,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11623
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_CPR_U',0,14,244,5,3
	.word	20319
	.byte	12,14,247,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11716
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_CPU_ID',0,14,252,5,3
	.word	20382
	.byte	12,14,255,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11823
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_CPXE',0,14,132,6,3
	.word	20446
	.byte	12,14,135,6,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11910
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_CREVT',0,14,140,6,3
	.word	20508
	.byte	12,14,143,6,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12064
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_CUS_ID',0,14,148,6,3
	.word	20571
	.byte	12,14,151,6,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12158
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_D',0,14,156,6,3
	.word	20635
	.byte	12,14,159,6,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12221
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_DATR',0,14,164,6,3
	.word	20694
	.byte	12,14,167,6,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12439
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_DBGSR',0,14,172,6,3
	.word	20756
	.byte	12,14,175,6,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12654
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_DBGTCR',0,14,180,6,3
	.word	20819
	.byte	12,14,183,6,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12748
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_DCON0',0,14,188,6,3
	.word	20883
	.byte	12,14,191,6,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12864
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_DCON2',0,14,196,6,3
	.word	20946
	.byte	12,14,199,6,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12965
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_DCX',0,14,204,6,3
	.word	21009
	.byte	12,14,207,6,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13058
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_DEADD',0,14,212,6,3
	.word	21070
	.byte	12,14,215,6,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13138
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_DIEAR',0,14,220,6,3
	.word	21133
	.byte	12,14,223,6,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13207
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_DIETR',0,14,228,6,3
	.word	21196
	.byte	12,14,231,6,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13436
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_DMS',0,14,236,6,3
	.word	21259
	.byte	12,14,239,6,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13529
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_DPR_L',0,14,244,6,3
	.word	21320
	.byte	12,14,247,6,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13624
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_DPR_U',0,14,252,6,3
	.word	21383
	.byte	12,14,255,6,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13719
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_DPRE',0,14,132,7,3
	.word	21446
	.byte	12,14,135,7,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13809
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_DPWE',0,14,140,7,3
	.word	21508
	.byte	12,14,143,7,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13899
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_DSTR',0,14,148,7,3
	.word	21570
	.byte	12,14,151,7,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14223
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_EXEVT',0,14,156,7,3
	.word	21632
	.byte	12,14,159,7,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14377
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_FCX',0,14,164,7,3
	.word	21695
	.byte	12,14,167,7,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14483
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_FPU_TRAP_CON',0,14,172,7,3
	.word	21756
	.byte	12,14,175,7,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14832
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_FPU_TRAP_OPC',0,14,180,7,3
	.word	21826
	.byte	12,14,183,7,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14992
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_FPU_TRAP_PC',0,14,188,7,3
	.word	21896
	.byte	12,14,191,7,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15073
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_FPU_TRAP_SRC1',0,14,196,7,3
	.word	21965
	.byte	12,14,199,7,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15160
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_FPU_TRAP_SRC2',0,14,204,7,3
	.word	22036
	.byte	12,14,207,7,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15247
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_FPU_TRAP_SRC3',0,14,212,7,3
	.word	22107
	.byte	12,14,215,7,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15334
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_ICNT',0,14,220,7,3
	.word	22178
	.byte	12,14,223,7,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15425
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_ICR',0,14,228,7,3
	.word	22240
	.byte	12,14,231,7,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15568
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_ISP',0,14,236,7,3
	.word	22301
	.byte	12,14,239,7,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15634
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_LCX',0,14,244,7,3
	.word	22362
	.byte	12,14,247,7,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15740
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_M1CNT',0,14,252,7,3
	.word	22423
	.byte	12,14,255,7,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15833
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_M2CNT',0,14,132,8,3
	.word	22486
	.byte	12,14,135,8,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15926
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_M3CNT',0,14,140,8,3
	.word	22549
	.byte	12,14,143,8,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16019
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_PC',0,14,148,8,3
	.word	22612
	.byte	12,14,151,8,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16104
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_PCON0',0,14,156,8,3
	.word	22672
	.byte	12,14,159,8,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16220
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_PCON1',0,14,164,8,3
	.word	22735
	.byte	12,14,167,8,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16331
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_PCON2',0,14,172,8,3
	.word	22798
	.byte	12,14,175,8,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16432
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_PCXI',0,14,180,8,3
	.word	22861
	.byte	12,14,183,8,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16562
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_PIEAR',0,14,188,8,3
	.word	22923
	.byte	12,14,191,8,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16631
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_PIETR',0,14,196,8,3
	.word	22986
	.byte	12,14,199,8,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16860
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_PMA0',0,14,204,8,3
	.word	23049
	.byte	12,14,207,8,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16973
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_PMA1',0,14,212,8,3
	.word	23111
	.byte	12,14,215,8,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17086
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_PMA2',0,14,220,8,3
	.word	23173
	.byte	12,14,223,8,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17177
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_PSTR',0,14,228,8,3
	.word	23235
	.byte	12,14,231,8,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17380
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_PSW',0,14,236,8,3
	.word	23297
	.byte	12,14,239,8,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17623
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_SEGEN',0,14,244,8,3
	.word	23358
	.byte	12,14,247,8,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17751
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_SMACON',0,14,252,8,3
	.word	23421
	.byte	12,14,255,8,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17992
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_SPROT_ACCENA',0,14,132,9,3
	.word	23485
	.byte	12,14,135,9,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18075
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_SPROT_ACCENB',0,14,140,9,3
	.word	23555
	.byte	12,14,143,9,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18166
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA',0,14,148,9,3
	.word	23625
	.byte	12,14,151,9,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18257
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB',0,14,156,9,3
	.word	23699
	.byte	12,14,159,9,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18356
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_SPROT_RGN_LA',0,14,164,9,3
	.word	23773
	.byte	12,14,167,9,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18463
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_SPROT_RGN_UA',0,14,172,9,3
	.word	23843
	.byte	12,14,175,9,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18570
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_SWEVT',0,14,180,9,3
	.word	23913
	.byte	12,14,183,9,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18724
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_SYSCON',0,14,188,9,3
	.word	23976
	.byte	12,14,191,9,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18885
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_TASK_ASI',0,14,196,9,3
	.word	24040
	.byte	12,14,199,9,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18983
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_TPS_CON',0,14,204,9,3
	.word	24106
	.byte	12,14,207,9,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19155
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_TPS_TIMER',0,14,212,9,3
	.word	24171
	.byte	12,14,215,9,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19235
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_TR_ADR',0,14,220,9,3
	.word	24238
	.byte	12,14,223,9,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19308
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_TR_EVT',0,14,228,9,3
	.word	24302
	.byte	12,14,231,9,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19626
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_TRIG_ACC',0,14,236,9,3
	.word	24366
	.byte	10
	.byte	'_Ifx_CPU_CPR',0,14,247,9,25,8,13
	.byte	'L',0
	.word	20256
	.byte	4,2,35,0,13
	.byte	'U',0
	.word	20319
	.byte	4,2,35,4,0,14
	.word	24432
	.byte	22
	.byte	'Ifx_CPU_CPR',0,14,251,9,3
	.word	24474
	.byte	10
	.byte	'_Ifx_CPU_DPR',0,14,254,9,25,8,13
	.byte	'L',0
	.word	21320
	.byte	4,2,35,0,13
	.byte	'U',0
	.word	21383
	.byte	4,2,35,4,0,14
	.word	24500
	.byte	22
	.byte	'Ifx_CPU_DPR',0,14,130,10,3
	.word	24542
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN',0,14,133,10,25,16,13
	.byte	'LA',0
	.word	23773
	.byte	4,2,35,0,13
	.byte	'UA',0
	.word	23843
	.byte	4,2,35,4,13
	.byte	'ACCENA',0
	.word	23625
	.byte	4,2,35,8,13
	.byte	'ACCENB',0
	.word	23699
	.byte	4,2,35,12,0,14
	.word	24568
	.byte	22
	.byte	'Ifx_CPU_SPROT_RGN',0,14,139,10,3
	.word	24650
	.byte	15,12
	.word	24171
	.byte	16,2,0,10
	.byte	'_Ifx_CPU_TPS',0,14,142,10,25,16,13
	.byte	'CON',0
	.word	24106
	.byte	4,2,35,0,13
	.byte	'TIMER',0
	.word	24682
	.byte	12,2,35,4,0,14
	.word	24691
	.byte	22
	.byte	'Ifx_CPU_TPS',0,14,146,10,3
	.word	24739
	.byte	10
	.byte	'_Ifx_CPU_TR',0,14,149,10,25,8,13
	.byte	'EVT',0
	.word	24302
	.byte	4,2,35,0,13
	.byte	'ADR',0
	.word	24238
	.byte	4,2,35,4,0,14
	.word	24765
	.byte	22
	.byte	'Ifx_CPU_TR',0,14,153,10,3
	.word	24810
	.byte	15,176,32
	.word	673
	.byte	16,175,32,0,15,208,223,1
	.word	673
	.byte	16,207,223,1,0,15,248,1
	.word	673
	.byte	16,247,1,0,15,244,29
	.word	673
	.byte	16,243,29,0,15,188,3
	.word	673
	.byte	16,187,3,0,15,232,3
	.word	673
	.byte	16,231,3,0,15,252,23
	.word	673
	.byte	16,251,23,0,15,228,63
	.word	673
	.byte	16,227,63,0,15,128,1
	.word	24500
	.byte	16,15,0,14
	.word	24925
	.byte	15,128,31
	.word	673
	.byte	16,255,30,0,15,64
	.word	24432
	.byte	16,7,0,14
	.word	24951
	.byte	15,192,31
	.word	673
	.byte	16,191,31,0,15,16
	.word	20446
	.byte	16,3,0,15,16
	.word	21446
	.byte	16,3,0,15,16
	.word	21508
	.byte	16,3,0,15,208,7
	.word	673
	.byte	16,207,7,0,14
	.word	24691
	.byte	15,240,23
	.word	673
	.byte	16,239,23,0,15,64
	.word	24765
	.byte	16,7,0,14
	.word	25030
	.byte	15,192,23
	.word	673
	.byte	16,191,23,0,15,232,1
	.word	673
	.byte	16,231,1,0,15,28
	.word	673
	.byte	16,27,0,15,180,1
	.word	673
	.byte	16,179,1,0,15,16
	.word	673
	.byte	16,15,0,15,172,1
	.word	673
	.byte	16,171,1,0,15,64
	.word	20635
	.byte	16,15,0,15,64
	.word	673
	.byte	16,63,0,15,64
	.word	19821
	.byte	16,15,0,10
	.byte	'_Ifx_CPU',0,14,166,10,25,128,128,4,13
	.byte	'reserved_0',0
	.word	24835
	.byte	176,32,2,35,0,13
	.byte	'SEGEN',0
	.word	23358
	.byte	4,3,35,176,32,13
	.byte	'reserved_1034',0
	.word	24846
	.byte	208,223,1,3,35,180,32,13
	.byte	'TASK_ASI',0
	.word	24040
	.byte	4,4,35,132,128,2,13
	.byte	'reserved_8008',0
	.word	24859
	.byte	248,1,4,35,136,128,2,13
	.byte	'PMA0',0
	.word	23049
	.byte	4,4,35,128,130,2,13
	.byte	'PMA1',0
	.word	23111
	.byte	4,4,35,132,130,2,13
	.byte	'PMA2',0
	.word	23173
	.byte	4,4,35,136,130,2,13
	.byte	'reserved_810C',0
	.word	24870
	.byte	244,29,4,35,140,130,2,13
	.byte	'DCON2',0
	.word	20946
	.byte	4,4,35,128,160,2,13
	.byte	'reserved_9004',0
	.word	4310
	.byte	8,4,35,132,160,2,13
	.byte	'SMACON',0
	.word	23421
	.byte	4,4,35,140,160,2,13
	.byte	'DSTR',0
	.word	21570
	.byte	4,4,35,144,160,2,13
	.byte	'reserved_9014',0
	.word	2491
	.byte	4,4,35,148,160,2,13
	.byte	'DATR',0
	.word	20694
	.byte	4,4,35,152,160,2,13
	.byte	'DEADD',0
	.word	21070
	.byte	4,4,35,156,160,2,13
	.byte	'DIEAR',0
	.word	21133
	.byte	4,4,35,160,160,2,13
	.byte	'DIETR',0
	.word	21196
	.byte	4,4,35,164,160,2,13
	.byte	'reserved_9028',0
	.word	3681
	.byte	24,4,35,168,160,2,13
	.byte	'DCON0',0
	.word	20883
	.byte	4,4,35,192,160,2,13
	.byte	'reserved_9044',0
	.word	24881
	.byte	188,3,4,35,196,160,2,13
	.byte	'PSTR',0
	.word	23235
	.byte	4,4,35,128,164,2,13
	.byte	'PCON1',0
	.word	22735
	.byte	4,4,35,132,164,2,13
	.byte	'PCON2',0
	.word	22798
	.byte	4,4,35,136,164,2,13
	.byte	'PCON0',0
	.word	22672
	.byte	4,4,35,140,164,2,13
	.byte	'PIEAR',0
	.word	22923
	.byte	4,4,35,144,164,2,13
	.byte	'PIETR',0
	.word	22986
	.byte	4,4,35,148,164,2,13
	.byte	'reserved_9218',0
	.word	24892
	.byte	232,3,4,35,152,164,2,13
	.byte	'COMPAT',0
	.word	20127
	.byte	4,4,35,128,168,2,13
	.byte	'reserved_9404',0
	.word	24903
	.byte	252,23,4,35,132,168,2,13
	.byte	'FPU_TRAP_CON',0
	.word	21756
	.byte	4,4,35,128,192,2,13
	.byte	'FPU_TRAP_PC',0
	.word	21896
	.byte	4,4,35,132,192,2,13
	.byte	'FPU_TRAP_OPC',0
	.word	21826
	.byte	4,4,35,136,192,2,13
	.byte	'reserved_A00C',0
	.word	2491
	.byte	4,4,35,140,192,2,13
	.byte	'FPU_TRAP_SRC1',0
	.word	21965
	.byte	4,4,35,144,192,2,13
	.byte	'FPU_TRAP_SRC2',0
	.word	22036
	.byte	4,4,35,148,192,2,13
	.byte	'FPU_TRAP_SRC3',0
	.word	22107
	.byte	4,4,35,152,192,2,13
	.byte	'reserved_A01C',0
	.word	24914
	.byte	228,63,4,35,156,192,2,13
	.byte	'DPR',0
	.word	24935
	.byte	128,1,4,35,128,128,3,13
	.byte	'reserved_C080',0
	.word	24940
	.byte	128,31,4,35,128,129,3,13
	.byte	'CPR',0
	.word	24960
	.byte	64,4,35,128,160,3,13
	.byte	'reserved_D040',0
	.word	24965
	.byte	192,31,4,35,192,160,3,13
	.byte	'CPXE',0
	.word	24976
	.byte	16,4,35,128,192,3,13
	.byte	'DPRE',0
	.word	24985
	.byte	16,4,35,144,192,3,13
	.byte	'DPWE',0
	.word	24994
	.byte	16,4,35,160,192,3,13
	.byte	'reserved_E030',0
	.word	25003
	.byte	208,7,4,35,176,192,3,13
	.byte	'TPS',0
	.word	25014
	.byte	16,4,35,128,200,3,13
	.byte	'reserved_E410',0
	.word	25019
	.byte	240,23,4,35,144,200,3,13
	.byte	'TR',0
	.word	25039
	.byte	64,4,35,128,224,3,13
	.byte	'reserved_F040',0
	.word	25044
	.byte	192,23,4,35,192,224,3,13
	.byte	'CCTRL',0
	.word	20064
	.byte	4,4,35,128,248,3,13
	.byte	'CCNT',0
	.word	20002
	.byte	4,4,35,132,248,3,13
	.byte	'ICNT',0
	.word	22178
	.byte	4,4,35,136,248,3,13
	.byte	'M1CNT',0
	.word	22423
	.byte	4,4,35,140,248,3,13
	.byte	'M2CNT',0
	.word	22486
	.byte	4,4,35,144,248,3,13
	.byte	'M3CNT',0
	.word	22549
	.byte	4,4,35,148,248,3,13
	.byte	'reserved_FC18',0
	.word	25055
	.byte	232,1,4,35,152,248,3,13
	.byte	'DBGSR',0
	.word	20756
	.byte	4,4,35,128,250,3,13
	.byte	'reserved_FD04',0
	.word	2491
	.byte	4,4,35,132,250,3,13
	.byte	'EXEVT',0
	.word	21632
	.byte	4,4,35,136,250,3,13
	.byte	'CREVT',0
	.word	20508
	.byte	4,4,35,140,250,3,13
	.byte	'SWEVT',0
	.word	23913
	.byte	4,4,35,144,250,3,13
	.byte	'reserved_FD14',0
	.word	25066
	.byte	28,4,35,148,250,3,13
	.byte	'TRIG_ACC',0
	.word	24366
	.byte	4,4,35,176,250,3,13
	.byte	'reserved_FD34',0
	.word	4650
	.byte	12,4,35,180,250,3,13
	.byte	'DMS',0
	.word	21259
	.byte	4,4,35,192,250,3,13
	.byte	'DCX',0
	.word	21009
	.byte	4,4,35,196,250,3,13
	.byte	'DBGTCR',0
	.word	20819
	.byte	4,4,35,200,250,3,13
	.byte	'reserved_FD4C',0
	.word	25075
	.byte	180,1,4,35,204,250,3,13
	.byte	'PCXI',0
	.word	22861
	.byte	4,4,35,128,252,3,13
	.byte	'PSW',0
	.word	23297
	.byte	4,4,35,132,252,3,13
	.byte	'PC',0
	.word	22612
	.byte	4,4,35,136,252,3,13
	.byte	'reserved_FE0C',0
	.word	4310
	.byte	8,4,35,140,252,3,13
	.byte	'SYSCON',0
	.word	23976
	.byte	4,4,35,148,252,3,13
	.byte	'CPU_ID',0
	.word	20382
	.byte	4,4,35,152,252,3,13
	.byte	'CORE_ID',0
	.word	20191
	.byte	4,4,35,156,252,3,13
	.byte	'BIV',0
	.word	19880
	.byte	4,4,35,160,252,3,13
	.byte	'BTV',0
	.word	19941
	.byte	4,4,35,164,252,3,13
	.byte	'ISP',0
	.word	22301
	.byte	4,4,35,168,252,3,13
	.byte	'ICR',0
	.word	22240
	.byte	4,4,35,172,252,3,13
	.byte	'reserved_FE30',0
	.word	4310
	.byte	8,4,35,176,252,3,13
	.byte	'FCX',0
	.word	21695
	.byte	4,4,35,184,252,3,13
	.byte	'LCX',0
	.word	22362
	.byte	4,4,35,188,252,3,13
	.byte	'reserved_FE40',0
	.word	25086
	.byte	16,4,35,192,252,3,13
	.byte	'CUS_ID',0
	.word	20571
	.byte	4,4,35,208,252,3,13
	.byte	'reserved_FE54',0
	.word	25095
	.byte	172,1,4,35,212,252,3,13
	.byte	'D',0
	.word	25106
	.byte	64,4,35,128,254,3,13
	.byte	'reserved_FF40',0
	.word	25115
	.byte	64,4,35,192,254,3,13
	.byte	'A',0
	.word	25124
	.byte	64,4,35,128,255,3,13
	.byte	'reserved_FFC0',0
	.word	25115
	.byte	64,4,35,192,255,3,0,14
	.word	25133
	.byte	22
	.byte	'Ifx_CPU',0,14,130,11,3
	.word	26924
	.byte	17,8,127,9,1,18
	.byte	'IfxCpu_Id_0',0,0,18
	.byte	'IfxCpu_Id_1',0,1,18
	.byte	'IfxCpu_Id_none',0,2,0,22
	.byte	'IfxCpu_Id',0,8,132,1,3
	.word	26946
	.byte	22
	.byte	'IfxCpu_ResourceCpu',0,8,161,1,3
	.word	9424
	.byte	22
	.byte	'Ifx_SRC_SRCR_Bits',0,10,62,3
	.word	9810
	.byte	22
	.byte	'Ifx_SRC_SRCR',0,10,75,3
	.word	10100
	.byte	10
	.byte	'_Ifx_SRC_AGBT',0,10,86,25,4,13
	.byte	'SR',0
	.word	10100
	.byte	4,2,35,0,0,14
	.word	27091
	.byte	22
	.byte	'Ifx_SRC_AGBT',0,10,89,3
	.word	27123
	.byte	10
	.byte	'_Ifx_SRC_ASCLIN',0,10,92,25,12,13
	.byte	'TX',0
	.word	10100
	.byte	4,2,35,0,13
	.byte	'RX',0
	.word	10100
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	10100
	.byte	4,2,35,8,0,14
	.word	27149
	.byte	22
	.byte	'Ifx_SRC_ASCLIN',0,10,97,3
	.word	27208
	.byte	10
	.byte	'_Ifx_SRC_BCUSPB',0,10,100,25,4,13
	.byte	'SBSRC',0
	.word	10100
	.byte	4,2,35,0,0,14
	.word	27236
	.byte	22
	.byte	'Ifx_SRC_BCUSPB',0,10,103,3
	.word	27273
	.byte	15,64
	.word	10100
	.byte	16,15,0,10
	.byte	'_Ifx_SRC_CAN',0,10,106,25,64,13
	.byte	'INT',0
	.word	27301
	.byte	64,2,35,0,0,14
	.word	27310
	.byte	22
	.byte	'Ifx_SRC_CAN',0,10,109,3
	.word	27342
	.byte	10
	.byte	'_Ifx_SRC_CCU6',0,10,112,25,16,13
	.byte	'SR0',0
	.word	10100
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10100
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	10100
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	10100
	.byte	4,2,35,12,0,14
	.word	27367
	.byte	22
	.byte	'Ifx_SRC_CCU6',0,10,118,3
	.word	27439
	.byte	15,8
	.word	10100
	.byte	16,1,0,10
	.byte	'_Ifx_SRC_CERBERUS',0,10,121,25,8,13
	.byte	'SR',0
	.word	27465
	.byte	8,2,35,0,0,14
	.word	27474
	.byte	22
	.byte	'Ifx_SRC_CERBERUS',0,10,124,3
	.word	27510
	.byte	10
	.byte	'_Ifx_SRC_CIF',0,10,127,25,16,13
	.byte	'MI',0
	.word	10100
	.byte	4,2,35,0,13
	.byte	'MIEP',0
	.word	10100
	.byte	4,2,35,4,13
	.byte	'ISP',0
	.word	10100
	.byte	4,2,35,8,13
	.byte	'MJPEG',0
	.word	10100
	.byte	4,2,35,12,0,14
	.word	27540
	.byte	22
	.byte	'Ifx_SRC_CIF',0,10,133,1,3
	.word	27613
	.byte	10
	.byte	'_Ifx_SRC_CPU',0,10,136,1,25,4,13
	.byte	'SBSRC',0
	.word	10100
	.byte	4,2,35,0,0,14
	.word	27639
	.byte	22
	.byte	'Ifx_SRC_CPU',0,10,139,1,3
	.word	27674
	.byte	15,192,1
	.word	10100
	.byte	16,47,0,10
	.byte	'_Ifx_SRC_DMA',0,10,142,1,25,208,1,13
	.byte	'ERR',0
	.word	10100
	.byte	4,2,35,0,13
	.byte	'reserved_4',0
	.word	4650
	.byte	12,2,35,4,13
	.byte	'CH',0
	.word	27700
	.byte	192,1,2,35,16,0,14
	.word	27710
	.byte	22
	.byte	'Ifx_SRC_DMA',0,10,147,1,3
	.word	27777
	.byte	10
	.byte	'_Ifx_SRC_DSADC',0,10,150,1,25,8,13
	.byte	'SRM',0
	.word	10100
	.byte	4,2,35,0,13
	.byte	'SRA',0
	.word	10100
	.byte	4,2,35,4,0,14
	.word	27803
	.byte	22
	.byte	'Ifx_SRC_DSADC',0,10,154,1,3
	.word	27851
	.byte	10
	.byte	'_Ifx_SRC_EMEM',0,10,157,1,25,4,13
	.byte	'SR',0
	.word	10100
	.byte	4,2,35,0,0,14
	.word	27879
	.byte	22
	.byte	'Ifx_SRC_EMEM',0,10,160,1,3
	.word	27912
	.byte	15,40
	.word	673
	.byte	16,39,0,10
	.byte	'_Ifx_SRC_ERAY',0,10,163,1,25,80,13
	.byte	'INT',0
	.word	27465
	.byte	8,2,35,0,13
	.byte	'TINT',0
	.word	27465
	.byte	8,2,35,8,13
	.byte	'NDAT',0
	.word	27465
	.byte	8,2,35,16,13
	.byte	'MBSC',0
	.word	27465
	.byte	8,2,35,24,13
	.byte	'OBUSY',0
	.word	10100
	.byte	4,2,35,32,13
	.byte	'IBUSY',0
	.word	10100
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	27939
	.byte	40,2,35,40,0,14
	.word	27948
	.byte	22
	.byte	'Ifx_SRC_ERAY',0,10,172,1,3
	.word	28075
	.byte	10
	.byte	'_Ifx_SRC_ETH',0,10,175,1,25,4,13
	.byte	'SR',0
	.word	10100
	.byte	4,2,35,0,0,14
	.word	28102
	.byte	22
	.byte	'Ifx_SRC_ETH',0,10,178,1,3
	.word	28134
	.byte	10
	.byte	'_Ifx_SRC_FCE',0,10,181,1,25,4,13
	.byte	'SR',0
	.word	10100
	.byte	4,2,35,0,0,14
	.word	28160
	.byte	22
	.byte	'Ifx_SRC_FCE',0,10,184,1,3
	.word	28192
	.byte	10
	.byte	'_Ifx_SRC_FFT',0,10,187,1,25,12,13
	.byte	'DONE',0
	.word	10100
	.byte	4,2,35,0,13
	.byte	'ERR',0
	.word	10100
	.byte	4,2,35,4,13
	.byte	'RFS',0
	.word	10100
	.byte	4,2,35,8,0,14
	.word	28218
	.byte	22
	.byte	'Ifx_SRC_FFT',0,10,192,1,3
	.word	28278
	.byte	10
	.byte	'_Ifx_SRC_GPSR',0,10,195,1,25,32,13
	.byte	'SR0',0
	.word	10100
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10100
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	10100
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	10100
	.byte	4,2,35,12,13
	.byte	'reserved_10',0
	.word	25086
	.byte	16,2,35,16,0,14
	.word	28304
	.byte	22
	.byte	'Ifx_SRC_GPSR',0,10,202,1,3
	.word	28398
	.byte	10
	.byte	'_Ifx_SRC_GPT12',0,10,205,1,25,48,13
	.byte	'CIRQ',0
	.word	10100
	.byte	4,2,35,0,13
	.byte	'T2',0
	.word	10100
	.byte	4,2,35,4,13
	.byte	'T3',0
	.word	10100
	.byte	4,2,35,8,13
	.byte	'T4',0
	.word	10100
	.byte	4,2,35,12,13
	.byte	'T5',0
	.word	10100
	.byte	4,2,35,16,13
	.byte	'T6',0
	.word	10100
	.byte	4,2,35,20,13
	.byte	'reserved_18',0
	.word	3681
	.byte	24,2,35,24,0,14
	.word	28425
	.byte	22
	.byte	'Ifx_SRC_GPT12',0,10,214,1,3
	.word	28542
	.byte	15,12
	.word	10100
	.byte	16,2,0,15,32
	.word	10100
	.byte	16,7,0,15,32
	.word	28579
	.byte	16,0,0,15,88
	.word	673
	.byte	16,87,0,15,108
	.word	10100
	.byte	16,26,0,15,96
	.word	673
	.byte	16,95,0,15,96
	.word	28579
	.byte	16,2,0,15,160,3
	.word	673
	.byte	16,159,3,0,15,64
	.word	28579
	.byte	16,1,0,15,192,3
	.word	673
	.byte	16,191,3,0,15,16
	.word	10100
	.byte	16,3,0,15,64
	.word	28664
	.byte	16,3,0,15,192,2
	.word	673
	.byte	16,191,2,0,15,52
	.word	673
	.byte	16,51,0,10
	.byte	'_Ifx_SRC_GTM',0,10,217,1,25,204,18,13
	.byte	'AEIIRQ',0
	.word	10100
	.byte	4,2,35,0,13
	.byte	'ARUIRQ',0
	.word	28570
	.byte	12,2,35,4,13
	.byte	'reserved_10',0
	.word	2491
	.byte	4,2,35,16,13
	.byte	'BRCIRQ',0
	.word	10100
	.byte	4,2,35,20,13
	.byte	'CMPIRQ',0
	.word	10100
	.byte	4,2,35,24,13
	.byte	'SPEIRQ',0
	.word	27465
	.byte	8,2,35,28,13
	.byte	'reserved_24',0
	.word	4310
	.byte	8,2,35,36,13
	.byte	'PSM',0
	.word	28588
	.byte	32,2,35,44,13
	.byte	'reserved_4C',0
	.word	28597
	.byte	88,2,35,76,13
	.byte	'DPLL',0
	.word	28606
	.byte	108,3,35,164,1,13
	.byte	'reserved_110',0
	.word	28615
	.byte	96,3,35,144,2,13
	.byte	'ERR',0
	.word	10100
	.byte	4,3,35,240,2,13
	.byte	'reserved_174',0
	.word	4650
	.byte	12,3,35,244,2,13
	.byte	'TIM',0
	.word	28624
	.byte	96,3,35,128,3,13
	.byte	'reserved_1E0',0
	.word	28633
	.byte	160,3,3,35,224,3,13
	.byte	'MCS',0
	.word	28624
	.byte	96,3,35,128,7,13
	.byte	'reserved_3E0',0
	.word	28633
	.byte	160,3,3,35,224,7,13
	.byte	'TOM',0
	.word	28644
	.byte	64,3,35,128,11,13
	.byte	'reserved_5C0',0
	.word	28653
	.byte	192,3,3,35,192,11,13
	.byte	'ATOM',0
	.word	28673
	.byte	64,3,35,128,15,13
	.byte	'reserved_7C0',0
	.word	28682
	.byte	192,2,3,35,192,15,13
	.byte	'MCSW0',0
	.word	28570
	.byte	12,3,35,128,18,13
	.byte	'reserved_90C',0
	.word	28693
	.byte	52,3,35,140,18,13
	.byte	'MCSW1',0
	.word	28570
	.byte	12,3,35,192,18,0,14
	.word	28702
	.byte	22
	.byte	'Ifx_SRC_GTM',0,10,243,1,3
	.word	29162
	.byte	10
	.byte	'_Ifx_SRC_HSCT',0,10,246,1,25,4,13
	.byte	'SR',0
	.word	10100
	.byte	4,2,35,0,0,14
	.word	29188
	.byte	22
	.byte	'Ifx_SRC_HSCT',0,10,249,1,3
	.word	29221
	.byte	10
	.byte	'_Ifx_SRC_HSSL',0,10,252,1,25,16,13
	.byte	'COK',0
	.word	10100
	.byte	4,2,35,0,13
	.byte	'RDI',0
	.word	10100
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	10100
	.byte	4,2,35,8,13
	.byte	'TRG',0
	.word	10100
	.byte	4,2,35,12,0,14
	.word	29248
	.byte	22
	.byte	'Ifx_SRC_HSSL',0,10,130,2,3
	.word	29321
	.byte	15,56
	.word	673
	.byte	16,55,0,10
	.byte	'_Ifx_SRC_I2C',0,10,133,2,25,80,13
	.byte	'BREQ',0
	.word	10100
	.byte	4,2,35,0,13
	.byte	'LBREQ',0
	.word	10100
	.byte	4,2,35,4,13
	.byte	'SREQ',0
	.word	10100
	.byte	4,2,35,8,13
	.byte	'LSREQ',0
	.word	10100
	.byte	4,2,35,12,13
	.byte	'ERR',0
	.word	10100
	.byte	4,2,35,16,13
	.byte	'P',0
	.word	10100
	.byte	4,2,35,20,13
	.byte	'reserved_18',0
	.word	29348
	.byte	56,2,35,24,0,14
	.word	29357
	.byte	22
	.byte	'Ifx_SRC_I2C',0,10,142,2,3
	.word	29480
	.byte	10
	.byte	'_Ifx_SRC_LMU',0,10,145,2,25,4,13
	.byte	'SR',0
	.word	10100
	.byte	4,2,35,0,0,14
	.word	29506
	.byte	22
	.byte	'Ifx_SRC_LMU',0,10,148,2,3
	.word	29538
	.byte	10
	.byte	'_Ifx_SRC_MSC',0,10,151,2,25,20,13
	.byte	'SR0',0
	.word	10100
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10100
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	10100
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	10100
	.byte	4,2,35,12,13
	.byte	'SR4',0
	.word	10100
	.byte	4,2,35,16,0,14
	.word	29564
	.byte	22
	.byte	'Ifx_SRC_MSC',0,10,158,2,3
	.word	29649
	.byte	10
	.byte	'_Ifx_SRC_PMU',0,10,161,2,25,4,13
	.byte	'SR',0
	.word	10100
	.byte	4,2,35,0,0,14
	.word	29675
	.byte	22
	.byte	'Ifx_SRC_PMU',0,10,164,2,3
	.word	29707
	.byte	10
	.byte	'_Ifx_SRC_PSI5',0,10,167,2,25,32,13
	.byte	'SR',0
	.word	28579
	.byte	32,2,35,0,0,14
	.word	29733
	.byte	22
	.byte	'Ifx_SRC_PSI5',0,10,170,2,3
	.word	29766
	.byte	10
	.byte	'_Ifx_SRC_PSI5S',0,10,173,2,25,32,13
	.byte	'SR',0
	.word	28579
	.byte	32,2,35,0,0,14
	.word	29793
	.byte	22
	.byte	'Ifx_SRC_PSI5S',0,10,176,2,3
	.word	29827
	.byte	10
	.byte	'_Ifx_SRC_QSPI',0,10,179,2,25,24,13
	.byte	'TX',0
	.word	10100
	.byte	4,2,35,0,13
	.byte	'RX',0
	.word	10100
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	10100
	.byte	4,2,35,8,13
	.byte	'PT',0
	.word	10100
	.byte	4,2,35,12,13
	.byte	'HC',0
	.word	10100
	.byte	4,2,35,16,13
	.byte	'U',0
	.word	10100
	.byte	4,2,35,20,0,14
	.word	29855
	.byte	22
	.byte	'Ifx_SRC_QSPI',0,10,187,2,3
	.word	29948
	.byte	10
	.byte	'_Ifx_SRC_SCR',0,10,190,2,25,4,13
	.byte	'SR',0
	.word	10100
	.byte	4,2,35,0,0,14
	.word	29975
	.byte	22
	.byte	'Ifx_SRC_SCR',0,10,193,2,3
	.word	30007
	.byte	10
	.byte	'_Ifx_SRC_SCU',0,10,196,2,25,20,13
	.byte	'DTS',0
	.word	10100
	.byte	4,2,35,0,13
	.byte	'ERU',0
	.word	28664
	.byte	16,2,35,4,0,14
	.word	30033
	.byte	22
	.byte	'Ifx_SRC_SCU',0,10,200,2,3
	.word	30079
	.byte	15,24
	.word	10100
	.byte	16,5,0,10
	.byte	'_Ifx_SRC_SENT',0,10,203,2,25,24,13
	.byte	'SR',0
	.word	30105
	.byte	24,2,35,0,0,14
	.word	30114
	.byte	22
	.byte	'Ifx_SRC_SENT',0,10,206,2,3
	.word	30147
	.byte	10
	.byte	'_Ifx_SRC_SMU',0,10,209,2,25,12,13
	.byte	'SR',0
	.word	28570
	.byte	12,2,35,0,0,14
	.word	30174
	.byte	22
	.byte	'Ifx_SRC_SMU',0,10,212,2,3
	.word	30206
	.byte	10
	.byte	'_Ifx_SRC_STM',0,10,215,2,25,8,13
	.byte	'SR0',0
	.word	10100
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10100
	.byte	4,2,35,4,0,14
	.word	30232
	.byte	22
	.byte	'Ifx_SRC_STM',0,10,219,2,3
	.word	30278
	.byte	10
	.byte	'_Ifx_SRC_VADCCG',0,10,222,2,25,16,13
	.byte	'SR0',0
	.word	10100
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10100
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	10100
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	10100
	.byte	4,2,35,12,0,14
	.word	30304
	.byte	22
	.byte	'Ifx_SRC_VADCCG',0,10,228,2,3
	.word	30379
	.byte	10
	.byte	'_Ifx_SRC_VADCG',0,10,231,2,25,16,13
	.byte	'SR0',0
	.word	10100
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10100
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	10100
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	10100
	.byte	4,2,35,12,0,14
	.word	30408
	.byte	22
	.byte	'Ifx_SRC_VADCG',0,10,237,2,3
	.word	30482
	.byte	10
	.byte	'_Ifx_SRC_XBAR',0,10,240,2,25,4,13
	.byte	'SRC',0
	.word	10100
	.byte	4,2,35,0,0,14
	.word	30510
	.byte	22
	.byte	'Ifx_SRC_XBAR',0,10,243,2,3
	.word	30544
	.byte	15,4
	.word	27091
	.byte	16,0,0,14
	.word	30571
	.byte	10
	.byte	'_Ifx_SRC_GAGBT',0,10,128,3,25,4,13
	.byte	'AGBT',0
	.word	30580
	.byte	4,2,35,0,0,14
	.word	30585
	.byte	22
	.byte	'Ifx_SRC_GAGBT',0,10,131,3,3
	.word	30621
	.byte	15,48
	.word	27149
	.byte	16,3,0,14
	.word	30649
	.byte	10
	.byte	'_Ifx_SRC_GASCLIN',0,10,134,3,25,48,13
	.byte	'ASCLIN',0
	.word	30658
	.byte	48,2,35,0,0,14
	.word	30663
	.byte	22
	.byte	'Ifx_SRC_GASCLIN',0,10,137,3,3
	.word	30703
	.byte	14
	.word	27236
	.byte	10
	.byte	'_Ifx_SRC_GBCU',0,10,140,3,25,4,13
	.byte	'SPB',0
	.word	30733
	.byte	4,2,35,0,0,14
	.word	30738
	.byte	22
	.byte	'Ifx_SRC_GBCU',0,10,143,3,3
	.word	30772
	.byte	15,64
	.word	27310
	.byte	16,0,0,14
	.word	30799
	.byte	10
	.byte	'_Ifx_SRC_GCAN',0,10,146,3,25,64,13
	.byte	'CAN',0
	.word	30808
	.byte	64,2,35,0,0,14
	.word	30813
	.byte	22
	.byte	'Ifx_SRC_GCAN',0,10,149,3,3
	.word	30847
	.byte	15,32
	.word	27367
	.byte	16,1,0,14
	.word	30874
	.byte	10
	.byte	'_Ifx_SRC_GCCU6',0,10,152,3,25,32,13
	.byte	'CCU6',0
	.word	30883
	.byte	32,2,35,0,0,14
	.word	30888
	.byte	22
	.byte	'Ifx_SRC_GCCU6',0,10,155,3,3
	.word	30924
	.byte	14
	.word	27474
	.byte	10
	.byte	'_Ifx_SRC_GCERBERUS',0,10,158,3,25,8,13
	.byte	'CERBERUS',0
	.word	30952
	.byte	8,2,35,0,0,14
	.word	30957
	.byte	22
	.byte	'Ifx_SRC_GCERBERUS',0,10,161,3,3
	.word	31001
	.byte	15,16
	.word	27540
	.byte	16,0,0,14
	.word	31033
	.byte	10
	.byte	'_Ifx_SRC_GCIF',0,10,164,3,25,16,13
	.byte	'CIF',0
	.word	31042
	.byte	16,2,35,0,0,14
	.word	31047
	.byte	22
	.byte	'Ifx_SRC_GCIF',0,10,167,3,3
	.word	31081
	.byte	15,8
	.word	27639
	.byte	16,1,0,14
	.word	31108
	.byte	10
	.byte	'_Ifx_SRC_GCPU',0,10,170,3,25,8,13
	.byte	'CPU',0
	.word	31117
	.byte	8,2,35,0,0,14
	.word	31122
	.byte	22
	.byte	'Ifx_SRC_GCPU',0,10,173,3,3
	.word	31156
	.byte	15,208,1
	.word	27710
	.byte	16,0,0,14
	.word	31183
	.byte	10
	.byte	'_Ifx_SRC_GDMA',0,10,176,3,25,208,1,13
	.byte	'DMA',0
	.word	31193
	.byte	208,1,2,35,0,0,14
	.word	31198
	.byte	22
	.byte	'Ifx_SRC_GDMA',0,10,179,3,3
	.word	31234
	.byte	14
	.word	27803
	.byte	14
	.word	27803
	.byte	14
	.word	27803
	.byte	10
	.byte	'_Ifx_SRC_GDSADC',0,10,182,3,25,32,13
	.byte	'DSADC0',0
	.word	31261
	.byte	8,2,35,0,13
	.byte	'reserved_8',0
	.word	4310
	.byte	8,2,35,8,13
	.byte	'DSADC2',0
	.word	31266
	.byte	8,2,35,16,13
	.byte	'DSADC3',0
	.word	31271
	.byte	8,2,35,24,0,14
	.word	31276
	.byte	22
	.byte	'Ifx_SRC_GDSADC',0,10,188,3,3
	.word	31367
	.byte	15,4
	.word	27879
	.byte	16,0,0,14
	.word	31396
	.byte	10
	.byte	'_Ifx_SRC_GEMEM',0,10,191,3,25,4,13
	.byte	'EMEM',0
	.word	31405
	.byte	4,2,35,0,0,14
	.word	31410
	.byte	22
	.byte	'Ifx_SRC_GEMEM',0,10,194,3,3
	.word	31446
	.byte	15,80
	.word	27948
	.byte	16,0,0,14
	.word	31474
	.byte	10
	.byte	'_Ifx_SRC_GERAY',0,10,197,3,25,80,13
	.byte	'ERAY',0
	.word	31483
	.byte	80,2,35,0,0,14
	.word	31488
	.byte	22
	.byte	'Ifx_SRC_GERAY',0,10,200,3,3
	.word	31524
	.byte	15,4
	.word	28102
	.byte	16,0,0,14
	.word	31552
	.byte	10
	.byte	'_Ifx_SRC_GETH',0,10,203,3,25,4,13
	.byte	'ETH',0
	.word	31561
	.byte	4,2,35,0,0,14
	.word	31566
	.byte	22
	.byte	'Ifx_SRC_GETH',0,10,206,3,3
	.word	31600
	.byte	15,4
	.word	28160
	.byte	16,0,0,14
	.word	31627
	.byte	10
	.byte	'_Ifx_SRC_GFCE',0,10,209,3,25,4,13
	.byte	'FCE',0
	.word	31636
	.byte	4,2,35,0,0,14
	.word	31641
	.byte	22
	.byte	'Ifx_SRC_GFCE',0,10,212,3,3
	.word	31675
	.byte	15,12
	.word	28218
	.byte	16,0,0,14
	.word	31702
	.byte	10
	.byte	'_Ifx_SRC_GFFT',0,10,215,3,25,12,13
	.byte	'FFT',0
	.word	31711
	.byte	12,2,35,0,0,14
	.word	31716
	.byte	22
	.byte	'Ifx_SRC_GFFT',0,10,218,3,3
	.word	31750
	.byte	15,64
	.word	28304
	.byte	16,1,0,14
	.word	31777
	.byte	10
	.byte	'_Ifx_SRC_GGPSR',0,10,221,3,25,64,13
	.byte	'GPSR',0
	.word	31786
	.byte	64,2,35,0,0,14
	.word	31791
	.byte	22
	.byte	'Ifx_SRC_GGPSR',0,10,224,3,3
	.word	31827
	.byte	15,48
	.word	28425
	.byte	16,0,0,14
	.word	31855
	.byte	10
	.byte	'_Ifx_SRC_GGPT12',0,10,227,3,25,48,13
	.byte	'GPT12',0
	.word	31864
	.byte	48,2,35,0,0,14
	.word	31869
	.byte	22
	.byte	'Ifx_SRC_GGPT12',0,10,230,3,3
	.word	31907
	.byte	15,204,18
	.word	28702
	.byte	16,0,0,14
	.word	31936
	.byte	10
	.byte	'_Ifx_SRC_GGTM',0,10,233,3,25,204,18,13
	.byte	'GTM',0
	.word	31946
	.byte	204,18,2,35,0,0,14
	.word	31951
	.byte	22
	.byte	'Ifx_SRC_GGTM',0,10,236,3,3
	.word	31987
	.byte	15,4
	.word	29188
	.byte	16,0,0,14
	.word	32014
	.byte	10
	.byte	'_Ifx_SRC_GHSCT',0,10,239,3,25,4,13
	.byte	'HSCT',0
	.word	32023
	.byte	4,2,35,0,0,14
	.word	32028
	.byte	22
	.byte	'Ifx_SRC_GHSCT',0,10,242,3,3
	.word	32064
	.byte	15,64
	.word	29248
	.byte	16,3,0,14
	.word	32092
	.byte	10
	.byte	'_Ifx_SRC_GHSSL',0,10,245,3,25,68,13
	.byte	'HSSL',0
	.word	32101
	.byte	64,2,35,0,13
	.byte	'EXI',0
	.word	10100
	.byte	4,2,35,64,0,14
	.word	32106
	.byte	22
	.byte	'Ifx_SRC_GHSSL',0,10,249,3,3
	.word	32155
	.byte	15,80
	.word	29357
	.byte	16,0,0,14
	.word	32183
	.byte	10
	.byte	'_Ifx_SRC_GI2C',0,10,252,3,25,80,13
	.byte	'I2C',0
	.word	32192
	.byte	80,2,35,0,0,14
	.word	32197
	.byte	22
	.byte	'Ifx_SRC_GI2C',0,10,255,3,3
	.word	32231
	.byte	15,4
	.word	29506
	.byte	16,0,0,14
	.word	32258
	.byte	10
	.byte	'_Ifx_SRC_GLMU',0,10,130,4,25,4,13
	.byte	'LMU',0
	.word	32267
	.byte	4,2,35,0,0,14
	.word	32272
	.byte	22
	.byte	'Ifx_SRC_GLMU',0,10,133,4,3
	.word	32306
	.byte	15,40
	.word	29564
	.byte	16,1,0,14
	.word	32333
	.byte	10
	.byte	'_Ifx_SRC_GMSC',0,10,136,4,25,40,13
	.byte	'MSC',0
	.word	32342
	.byte	40,2,35,0,0,14
	.word	32347
	.byte	22
	.byte	'Ifx_SRC_GMSC',0,10,139,4,3
	.word	32381
	.byte	15,8
	.word	29675
	.byte	16,1,0,14
	.word	32408
	.byte	10
	.byte	'_Ifx_SRC_GPMU',0,10,142,4,25,8,13
	.byte	'PMU',0
	.word	32417
	.byte	8,2,35,0,0,14
	.word	32422
	.byte	22
	.byte	'Ifx_SRC_GPMU',0,10,145,4,3
	.word	32456
	.byte	15,32
	.word	29733
	.byte	16,0,0,14
	.word	32483
	.byte	10
	.byte	'_Ifx_SRC_GPSI5',0,10,148,4,25,32,13
	.byte	'PSI5',0
	.word	32492
	.byte	32,2,35,0,0,14
	.word	32497
	.byte	22
	.byte	'Ifx_SRC_GPSI5',0,10,151,4,3
	.word	32533
	.byte	15,32
	.word	29793
	.byte	16,0,0,14
	.word	32561
	.byte	10
	.byte	'_Ifx_SRC_GPSI5S',0,10,154,4,25,32,13
	.byte	'PSI5S',0
	.word	32570
	.byte	32,2,35,0,0,14
	.word	32575
	.byte	22
	.byte	'Ifx_SRC_GPSI5S',0,10,157,4,3
	.word	32613
	.byte	15,96
	.word	29855
	.byte	16,3,0,14
	.word	32642
	.byte	10
	.byte	'_Ifx_SRC_GQSPI',0,10,160,4,25,96,13
	.byte	'QSPI',0
	.word	32651
	.byte	96,2,35,0,0,14
	.word	32656
	.byte	22
	.byte	'Ifx_SRC_GQSPI',0,10,163,4,3
	.word	32692
	.byte	15,4
	.word	29975
	.byte	16,0,0,14
	.word	32720
	.byte	10
	.byte	'_Ifx_SRC_GSCR',0,10,166,4,25,4,13
	.byte	'SCR',0
	.word	32729
	.byte	4,2,35,0,0,14
	.word	32734
	.byte	22
	.byte	'Ifx_SRC_GSCR',0,10,169,4,3
	.word	32768
	.byte	14
	.word	30033
	.byte	10
	.byte	'_Ifx_SRC_GSCU',0,10,172,4,25,20,13
	.byte	'SCU',0
	.word	32795
	.byte	20,2,35,0,0,14
	.word	32800
	.byte	22
	.byte	'Ifx_SRC_GSCU',0,10,175,4,3
	.word	32834
	.byte	15,24
	.word	30114
	.byte	16,0,0,14
	.word	32861
	.byte	10
	.byte	'_Ifx_SRC_GSENT',0,10,178,4,25,24,13
	.byte	'SENT',0
	.word	32870
	.byte	24,2,35,0,0,14
	.word	32875
	.byte	22
	.byte	'Ifx_SRC_GSENT',0,10,181,4,3
	.word	32911
	.byte	15,12
	.word	30174
	.byte	16,0,0,14
	.word	32939
	.byte	10
	.byte	'_Ifx_SRC_GSMU',0,10,184,4,25,12,13
	.byte	'SMU',0
	.word	32948
	.byte	12,2,35,0,0,14
	.word	32953
	.byte	22
	.byte	'Ifx_SRC_GSMU',0,10,187,4,3
	.word	32987
	.byte	15,16
	.word	30232
	.byte	16,1,0,14
	.word	33014
	.byte	10
	.byte	'_Ifx_SRC_GSTM',0,10,190,4,25,16,13
	.byte	'STM',0
	.word	33023
	.byte	16,2,35,0,0,14
	.word	33028
	.byte	22
	.byte	'Ifx_SRC_GSTM',0,10,193,4,3
	.word	33062
	.byte	15,64
	.word	30408
	.byte	16,3,0,14
	.word	33089
	.byte	15,224,1
	.word	673
	.byte	16,223,1,0,15,32
	.word	30304
	.byte	16,1,0,14
	.word	33114
	.byte	10
	.byte	'_Ifx_SRC_GVADC',0,10,196,4,25,192,2,13
	.byte	'G',0
	.word	33098
	.byte	64,2,35,0,13
	.byte	'reserved_40',0
	.word	33103
	.byte	224,1,2,35,64,13
	.byte	'CG',0
	.word	33123
	.byte	32,3,35,160,2,0,14
	.word	33128
	.byte	22
	.byte	'Ifx_SRC_GVADC',0,10,201,4,3
	.word	33197
	.byte	14
	.word	30510
	.byte	10
	.byte	'_Ifx_SRC_GXBAR',0,10,204,4,25,4,13
	.byte	'XBAR',0
	.word	33225
	.byte	4,2,35,0,0,14
	.word	33230
	.byte	22
	.byte	'Ifx_SRC_GXBAR',0,10,207,4,3
	.word	33266
	.byte	10
	.byte	'_Ifx_SCU_ACCEN0_Bits',0,4,45,16,4,11
	.byte	'EN0',0,1
	.word	673
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	673
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	673
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	673
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	673
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	673
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	673
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	673
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	673
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	673
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	673
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	673
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	673
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	673
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	673
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	673
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	673
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	673
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	673
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	673
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	673
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	673
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	673
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	673
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	673
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	673
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	673
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	673
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	673
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	673
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	673
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	673
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_ACCEN0_Bits',0,4,79,3
	.word	33294
	.byte	10
	.byte	'_Ifx_SCU_ACCEN1_Bits',0,4,82,16,4,11
	.byte	'reserved_0',0,4
	.word	498
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_SCU_ACCEN1_Bits',0,4,85,3
	.word	33851
	.byte	10
	.byte	'_Ifx_SCU_ARSTDIS_Bits',0,4,88,16,4,11
	.byte	'STM0DIS',0,1
	.word	673
	.byte	1,7,2,35,0,11
	.byte	'STM1DIS',0,1
	.word	673
	.byte	1,6,2,35,0,11
	.byte	'STM2DIS',0,1
	.word	673
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	498
	.byte	29,0,2,35,0,0,22
	.byte	'Ifx_SCU_ARSTDIS_Bits',0,4,94,3
	.word	33928
	.byte	10
	.byte	'_Ifx_SCU_CCUCON0_Bits',0,4,97,16,4,11
	.byte	'BAUD1DIV',0,1
	.word	673
	.byte	4,4,2,35,0,11
	.byte	'BAUD2DIV',0,1
	.word	673
	.byte	4,0,2,35,0,11
	.byte	'SRIDIV',0,1
	.word	673
	.byte	4,4,2,35,1,11
	.byte	'LPDIV',0,1
	.word	673
	.byte	4,0,2,35,1,11
	.byte	'SPBDIV',0,1
	.word	673
	.byte	4,4,2,35,2,11
	.byte	'FSI2DIV',0,1
	.word	673
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	673
	.byte	2,0,2,35,2,11
	.byte	'FSIDIV',0,1
	.word	673
	.byte	2,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	673
	.byte	2,4,2,35,3,11
	.byte	'CLKSEL',0,1
	.word	673
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	673
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	673
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_CCUCON0_Bits',0,4,111,3
	.word	34064
	.byte	10
	.byte	'_Ifx_SCU_CCUCON1_Bits',0,4,114,16,4,11
	.byte	'CANDIV',0,1
	.word	673
	.byte	4,4,2,35,0,11
	.byte	'ERAYDIV',0,1
	.word	673
	.byte	4,0,2,35,0,11
	.byte	'STMDIV',0,1
	.word	673
	.byte	4,4,2,35,1,11
	.byte	'GTMDIV',0,1
	.word	673
	.byte	4,0,2,35,1,11
	.byte	'ETHDIV',0,1
	.word	673
	.byte	4,4,2,35,2,11
	.byte	'ASCLINFDIV',0,1
	.word	673
	.byte	4,0,2,35,2,11
	.byte	'ASCLINSDIV',0,1
	.word	673
	.byte	4,4,2,35,3,11
	.byte	'INSEL',0,1
	.word	673
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	673
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	673
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_CCUCON1_Bits',0,4,126,3
	.word	34344
	.byte	10
	.byte	'_Ifx_SCU_CCUCON2_Bits',0,4,129,1,16,4,11
	.byte	'BBBDIV',0,1
	.word	673
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	498
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	673
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	673
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_CCUCON2_Bits',0,4,135,1,3
	.word	34582
	.byte	10
	.byte	'_Ifx_SCU_CCUCON3_Bits',0,4,138,1,16,4,11
	.byte	'PLLDIV',0,1
	.word	673
	.byte	6,2,2,35,0,11
	.byte	'PLLSEL',0,1
	.word	673
	.byte	2,0,2,35,0,11
	.byte	'PLLERAYDIV',0,1
	.word	673
	.byte	6,2,2,35,1,11
	.byte	'PLLERAYSEL',0,1
	.word	673
	.byte	2,0,2,35,1,11
	.byte	'SRIDIV',0,1
	.word	673
	.byte	6,2,2,35,2,11
	.byte	'SRISEL',0,1
	.word	673
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	673
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	673
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	673
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	673
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_CCUCON3_Bits',0,4,150,1,3
	.word	34710
	.byte	10
	.byte	'_Ifx_SCU_CCUCON4_Bits',0,4,153,1,16,4,11
	.byte	'SPBDIV',0,1
	.word	673
	.byte	6,2,2,35,0,11
	.byte	'SPBSEL',0,1
	.word	673
	.byte	2,0,2,35,0,11
	.byte	'GTMDIV',0,1
	.word	673
	.byte	6,2,2,35,1,11
	.byte	'GTMSEL',0,1
	.word	673
	.byte	2,0,2,35,1,11
	.byte	'STMDIV',0,1
	.word	673
	.byte	6,2,2,35,2,11
	.byte	'STMSEL',0,1
	.word	673
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	673
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	673
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	673
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	673
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_CCUCON4_Bits',0,4,165,1,3
	.word	34953
	.byte	10
	.byte	'_Ifx_SCU_CCUCON5_Bits',0,4,168,1,16,4,11
	.byte	'MAXDIV',0,1
	.word	673
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	498
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	673
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	673
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_CCUCON5_Bits',0,4,174,1,3
	.word	35188
	.byte	10
	.byte	'_Ifx_SCU_CCUCON6_Bits',0,4,177,1,16,4,11
	.byte	'CPU0DIV',0,1
	.word	673
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	498
	.byte	26,0,2,35,0,0,22
	.byte	'Ifx_SCU_CCUCON6_Bits',0,4,181,1,3
	.word	35316
	.byte	10
	.byte	'_Ifx_SCU_CCUCON7_Bits',0,4,184,1,16,4,11
	.byte	'CPU1DIV',0,1
	.word	673
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	498
	.byte	26,0,2,35,0,0,22
	.byte	'Ifx_SCU_CCUCON7_Bits',0,4,188,1,3
	.word	35416
	.byte	10
	.byte	'_Ifx_SCU_CHIPID_Bits',0,4,191,1,16,4,11
	.byte	'CHREV',0,1
	.word	673
	.byte	6,2,2,35,0,11
	.byte	'CHTEC',0,1
	.word	673
	.byte	2,0,2,35,0,11
	.byte	'CHID',0,1
	.word	673
	.byte	8,0,2,35,1,11
	.byte	'EEA',0,1
	.word	673
	.byte	1,7,2,35,2,11
	.byte	'UCODE',0,1
	.word	673
	.byte	7,0,2,35,2,11
	.byte	'FSIZE',0,1
	.word	673
	.byte	4,4,2,35,3,11
	.byte	'SP',0,1
	.word	673
	.byte	2,2,2,35,3,11
	.byte	'SEC',0,1
	.word	673
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	673
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_CHIPID_Bits',0,4,202,1,3
	.word	35516
	.byte	10
	.byte	'_Ifx_SCU_DTSCON_Bits',0,4,205,1,16,4,11
	.byte	'PWD',0,1
	.word	673
	.byte	1,7,2,35,0,11
	.byte	'START',0,1
	.word	673
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	673
	.byte	2,4,2,35,0,11
	.byte	'CAL',0,4
	.word	498
	.byte	20,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	673
	.byte	7,1,2,35,3,11
	.byte	'SLCK',0,1
	.word	673
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_DTSCON_Bits',0,4,213,1,3
	.word	35724
	.byte	10
	.byte	'_Ifx_SCU_DTSLIM_Bits',0,4,216,1,16,4,11
	.byte	'LOWER',0,2
	.word	690
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	673
	.byte	5,1,2,35,1,11
	.byte	'LLU',0,1
	.word	673
	.byte	1,0,2,35,1,11
	.byte	'UPPER',0,2
	.word	690
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	673
	.byte	4,2,2,35,3,11
	.byte	'SLCK',0,1
	.word	673
	.byte	1,1,2,35,3,11
	.byte	'UOF',0,1
	.word	673
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_DTSLIM_Bits',0,4,225,1,3
	.word	35889
	.byte	10
	.byte	'_Ifx_SCU_DTSSTAT_Bits',0,4,228,1,16,4,11
	.byte	'RESULT',0,2
	.word	690
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	673
	.byte	4,2,2,35,1,11
	.byte	'RDY',0,1
	.word	673
	.byte	1,1,2,35,1,11
	.byte	'BUSY',0,1
	.word	673
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	690
	.byte	16,0,2,35,2,0,22
	.byte	'Ifx_SCU_DTSSTAT_Bits',0,4,235,1,3
	.word	36072
	.byte	10
	.byte	'_Ifx_SCU_EICR_Bits',0,4,238,1,16,4,11
	.byte	'reserved_0',0,1
	.word	673
	.byte	4,4,2,35,0,11
	.byte	'EXIS0',0,1
	.word	673
	.byte	3,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	673
	.byte	1,0,2,35,0,11
	.byte	'FEN0',0,1
	.word	673
	.byte	1,7,2,35,1,11
	.byte	'REN0',0,1
	.word	673
	.byte	1,6,2,35,1,11
	.byte	'LDEN0',0,1
	.word	673
	.byte	1,5,2,35,1,11
	.byte	'EIEN0',0,1
	.word	673
	.byte	1,4,2,35,1,11
	.byte	'INP0',0,1
	.word	673
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	498
	.byte	5,12,2,35,0,11
	.byte	'EXIS1',0,1
	.word	673
	.byte	3,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	673
	.byte	1,0,2,35,2,11
	.byte	'FEN1',0,1
	.word	673
	.byte	1,7,2,35,3,11
	.byte	'REN1',0,1
	.word	673
	.byte	1,6,2,35,3,11
	.byte	'LDEN1',0,1
	.word	673
	.byte	1,5,2,35,3,11
	.byte	'EIEN1',0,1
	.word	673
	.byte	1,4,2,35,3,11
	.byte	'INP1',0,1
	.word	673
	.byte	3,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	673
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EICR_Bits',0,4,129,2,3
	.word	36226
	.byte	10
	.byte	'_Ifx_SCU_EIFR_Bits',0,4,132,2,16,4,11
	.byte	'INTF0',0,1
	.word	673
	.byte	1,7,2,35,0,11
	.byte	'INTF1',0,1
	.word	673
	.byte	1,6,2,35,0,11
	.byte	'INTF2',0,1
	.word	673
	.byte	1,5,2,35,0,11
	.byte	'INTF3',0,1
	.word	673
	.byte	1,4,2,35,0,11
	.byte	'INTF4',0,1
	.word	673
	.byte	1,3,2,35,0,11
	.byte	'INTF5',0,1
	.word	673
	.byte	1,2,2,35,0,11
	.byte	'INTF6',0,1
	.word	673
	.byte	1,1,2,35,0,11
	.byte	'INTF7',0,1
	.word	673
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	498
	.byte	24,0,2,35,0,0,22
	.byte	'Ifx_SCU_EIFR_Bits',0,4,143,2,3
	.word	36590
	.byte	10
	.byte	'_Ifx_SCU_EMSR_Bits',0,4,146,2,16,4,11
	.byte	'POL',0,1
	.word	673
	.byte	1,7,2,35,0,11
	.byte	'MODE',0,1
	.word	673
	.byte	1,6,2,35,0,11
	.byte	'ENON',0,1
	.word	673
	.byte	1,5,2,35,0,11
	.byte	'PSEL',0,1
	.word	673
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	690
	.byte	12,0,2,35,0,11
	.byte	'EMSF',0,1
	.word	673
	.byte	1,7,2,35,2,11
	.byte	'SEMSF',0,1
	.word	673
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	673
	.byte	6,0,2,35,2,11
	.byte	'EMSFM',0,1
	.word	673
	.byte	2,6,2,35,3,11
	.byte	'SEMSFM',0,1
	.word	673
	.byte	2,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	673
	.byte	4,0,2,35,3,0,22
	.byte	'Ifx_SCU_EMSR_Bits',0,4,159,2,3
	.word	36801
	.byte	10
	.byte	'_Ifx_SCU_ESRCFG_Bits',0,4,162,2,16,4,11
	.byte	'reserved_0',0,1
	.word	673
	.byte	7,1,2,35,0,11
	.byte	'EDCON',0,2
	.word	690
	.byte	2,7,2,35,0,11
	.byte	'reserved_9',0,4
	.word	498
	.byte	23,0,2,35,0,0,22
	.byte	'Ifx_SCU_ESRCFG_Bits',0,4,167,2,3
	.word	37053
	.byte	10
	.byte	'_Ifx_SCU_ESROCFG_Bits',0,4,170,2,16,4,11
	.byte	'ARI',0,1
	.word	673
	.byte	1,7,2,35,0,11
	.byte	'ARC',0,1
	.word	673
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	498
	.byte	30,0,2,35,0,0,22
	.byte	'Ifx_SCU_ESROCFG_Bits',0,4,175,2,3
	.word	37171
	.byte	10
	.byte	'_Ifx_SCU_EVR13CON_Bits',0,4,178,2,16,4,11
	.byte	'reserved_0',0,4
	.word	498
	.byte	28,4,2,35,0,11
	.byte	'EVR13OFF',0,1
	.word	673
	.byte	1,3,2,35,3,11
	.byte	'BPEVR13OFF',0,1
	.word	673
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	673
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	673
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVR13CON_Bits',0,4,185,2,3
	.word	37282
	.byte	10
	.byte	'_Ifx_SCU_EVR33CON_Bits',0,4,188,2,16,4,11
	.byte	'reserved_0',0,4
	.word	498
	.byte	28,4,2,35,0,11
	.byte	'EVR33OFF',0,1
	.word	673
	.byte	1,3,2,35,3,11
	.byte	'BPEVR33OFF',0,1
	.word	673
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	673
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	673
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVR33CON_Bits',0,4,195,2,3
	.word	37445
	.byte	10
	.byte	'_Ifx_SCU_EVRADCSTAT_Bits',0,4,198,2,16,4,11
	.byte	'ADC13V',0,1
	.word	673
	.byte	8,0,2,35,0,11
	.byte	'ADC33V',0,1
	.word	673
	.byte	8,0,2,35,1,11
	.byte	'ADCSWDV',0,1
	.word	673
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	673
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	673
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVRADCSTAT_Bits',0,4,205,2,3
	.word	37608
	.byte	10
	.byte	'_Ifx_SCU_EVRDVSTAT_Bits',0,4,208,2,16,4,11
	.byte	'DVS13TRIM',0,1
	.word	673
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	673
	.byte	8,0,2,35,1,11
	.byte	'DVS33TRIM',0,1
	.word	673
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	673
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	673
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVRDVSTAT_Bits',0,4,215,2,3
	.word	37766
	.byte	10
	.byte	'_Ifx_SCU_EVRMONCTRL_Bits',0,4,218,2,16,4,11
	.byte	'EVR13OVMOD',0,1
	.word	673
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	673
	.byte	2,4,2,35,0,11
	.byte	'EVR13UVMOD',0,1
	.word	673
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	673
	.byte	2,0,2,35,0,11
	.byte	'EVR33OVMOD',0,1
	.word	673
	.byte	2,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	673
	.byte	2,4,2,35,1,11
	.byte	'EVR33UVMOD',0,1
	.word	673
	.byte	2,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	673
	.byte	2,0,2,35,1,11
	.byte	'SWDOVMOD',0,1
	.word	673
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	673
	.byte	2,4,2,35,2,11
	.byte	'SWDUVMOD',0,1
	.word	673
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,2
	.word	690
	.byte	10,0,2,35,2,0,22
	.byte	'Ifx_SCU_EVRMONCTRL_Bits',0,4,232,2,3
	.word	37931
	.byte	10
	.byte	'_Ifx_SCU_EVROSCCTRL_Bits',0,4,235,2,16,4,11
	.byte	'OSCTRIM',0,2
	.word	690
	.byte	10,6,2,35,0,11
	.byte	'OSCPTAT',0,1
	.word	673
	.byte	6,0,2,35,1,11
	.byte	'OSCANASEL',0,1
	.word	673
	.byte	4,4,2,35,2,11
	.byte	'HPBGTRIM',0,2
	.word	690
	.byte	7,5,2,35,2,11
	.byte	'HPBGCLKEN',0,1
	.word	673
	.byte	1,4,2,35,3,11
	.byte	'OSC3V3',0,1
	.word	673
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	673
	.byte	2,1,2,35,3,11
	.byte	'LCK',0,1
	.word	673
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVROSCCTRL_Bits',0,4,245,2,3
	.word	38260
	.byte	10
	.byte	'_Ifx_SCU_EVROVMON_Bits',0,4,248,2,16,4,11
	.byte	'EVR13OVVAL',0,1
	.word	673
	.byte	8,0,2,35,0,11
	.byte	'EVR33OVVAL',0,1
	.word	673
	.byte	8,0,2,35,1,11
	.byte	'SWDOVVAL',0,1
	.word	673
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	673
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	673
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVROVMON_Bits',0,4,255,2,3
	.word	38481
	.byte	10
	.byte	'_Ifx_SCU_EVRRSTCON_Bits',0,4,130,3,16,4,11
	.byte	'RST13TRIM',0,1
	.word	673
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	498
	.byte	16,8,2,35,0,11
	.byte	'RST13OFF',0,1
	.word	673
	.byte	1,7,2,35,3,11
	.byte	'BPRST13OFF',0,1
	.word	673
	.byte	1,6,2,35,3,11
	.byte	'RST33OFF',0,1
	.word	673
	.byte	1,5,2,35,3,11
	.byte	'BPRST33OFF',0,1
	.word	673
	.byte	1,4,2,35,3,11
	.byte	'RSTSWDOFF',0,1
	.word	673
	.byte	1,3,2,35,3,11
	.byte	'BPRSTSWDOFF',0,1
	.word	673
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	673
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	673
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVRRSTCON_Bits',0,4,142,3,3
	.word	38644
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF1_Bits',0,4,145,3,16,4,11
	.byte	'SD5P',0,1
	.word	673
	.byte	8,0,2,35,0,11
	.byte	'SD5I',0,1
	.word	673
	.byte	8,0,2,35,1,11
	.byte	'SD5D',0,1
	.word	673
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	673
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	673
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVRSDCOEFF1_Bits',0,4,152,3,3
	.word	38916
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF2_Bits',0,4,155,3,16,4,11
	.byte	'SD33P',0,1
	.word	673
	.byte	8,0,2,35,0,11
	.byte	'SD33I',0,1
	.word	673
	.byte	8,0,2,35,1,11
	.byte	'SD33D',0,1
	.word	673
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	673
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	673
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVRSDCOEFF2_Bits',0,4,162,3,3
	.word	39069
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF3_Bits',0,4,165,3,16,4,11
	.byte	'CT5REG0',0,1
	.word	673
	.byte	8,0,2,35,0,11
	.byte	'CT5REG1',0,1
	.word	673
	.byte	8,0,2,35,1,11
	.byte	'CT5REG2',0,1
	.word	673
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	673
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	673
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVRSDCOEFF3_Bits',0,4,172,3,3
	.word	39225
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF4_Bits',0,4,175,3,16,4,11
	.byte	'CT5REG3',0,1
	.word	673
	.byte	8,0,2,35,0,11
	.byte	'CT5REG4',0,1
	.word	673
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	690
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	673
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVRSDCOEFF4_Bits',0,4,181,3,3
	.word	39387
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF5_Bits',0,4,184,3,16,4,11
	.byte	'CT33REG0',0,1
	.word	673
	.byte	8,0,2,35,0,11
	.byte	'CT33REG1',0,1
	.word	673
	.byte	8,0,2,35,1,11
	.byte	'CT33REG2',0,1
	.word	673
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	673
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	673
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVRSDCOEFF5_Bits',0,4,191,3,3
	.word	39530
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF6_Bits',0,4,194,3,16,4,11
	.byte	'CT33REG3',0,1
	.word	673
	.byte	8,0,2,35,0,11
	.byte	'CT33REG4',0,1
	.word	673
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	690
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	673
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVRSDCOEFF6_Bits',0,4,200,3,3
	.word	39695
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL1_Bits',0,4,203,3,16,4,11
	.byte	'SDFREQSPRD',0,2
	.word	690
	.byte	16,0,2,35,0,11
	.byte	'SDFREQ',0,1
	.word	673
	.byte	8,0,2,35,2,11
	.byte	'SDSTEP',0,1
	.word	673
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	673
	.byte	2,2,2,35,3,11
	.byte	'SDSAMPLE',0,1
	.word	673
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	673
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVRSDCTRL1_Bits',0,4,211,3,3
	.word	39840
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL2_Bits',0,4,214,3,16,4,11
	.byte	'DRVP',0,1
	.word	673
	.byte	8,0,2,35,0,11
	.byte	'SDMINMAXDC',0,1
	.word	673
	.byte	8,0,2,35,1,11
	.byte	'DRVN',0,1
	.word	673
	.byte	8,0,2,35,2,11
	.byte	'SDLUT',0,1
	.word	673
	.byte	6,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	673
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	673
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVRSDCTRL2_Bits',0,4,222,3,3
	.word	40021
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL3_Bits',0,4,225,3,16,4,11
	.byte	'SDPWMPRE',0,1
	.word	673
	.byte	8,0,2,35,0,11
	.byte	'SDPID',0,1
	.word	673
	.byte	8,0,2,35,1,11
	.byte	'SDVOKLVL',0,1
	.word	673
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	673
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	673
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVRSDCTRL3_Bits',0,4,232,3,3
	.word	40195
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL4_Bits',0,4,235,3,16,4,11
	.byte	'reserved_0',0,1
	.word	673
	.byte	8,0,2,35,0,11
	.byte	'SYNCDIV',0,1
	.word	673
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	498
	.byte	20,1,2,35,0,11
	.byte	'LCK',0,1
	.word	673
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVRSDCTRL4_Bits',0,4,241,3,3
	.word	40355
	.byte	10
	.byte	'_Ifx_SCU_EVRSTAT_Bits',0,4,244,3,16,4,11
	.byte	'EVR13',0,1
	.word	673
	.byte	1,7,2,35,0,11
	.byte	'OV13',0,1
	.word	673
	.byte	1,6,2,35,0,11
	.byte	'EVR33',0,1
	.word	673
	.byte	1,5,2,35,0,11
	.byte	'OV33',0,1
	.word	673
	.byte	1,4,2,35,0,11
	.byte	'OVSWD',0,1
	.word	673
	.byte	1,3,2,35,0,11
	.byte	'UV13',0,1
	.word	673
	.byte	1,2,2,35,0,11
	.byte	'UV33',0,1
	.word	673
	.byte	1,1,2,35,0,11
	.byte	'UVSWD',0,1
	.word	673
	.byte	1,0,2,35,0,11
	.byte	'EXTPASS13',0,1
	.word	673
	.byte	1,7,2,35,1,11
	.byte	'EXTPASS33',0,1
	.word	673
	.byte	1,6,2,35,1,11
	.byte	'BGPROK',0,1
	.word	673
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	498
	.byte	21,0,2,35,0,0,22
	.byte	'Ifx_SCU_EVRSTAT_Bits',0,4,130,4,3
	.word	40499
	.byte	10
	.byte	'_Ifx_SCU_EVRTRIM_Bits',0,4,133,4,16,4,11
	.byte	'EVR13TRIM',0,1
	.word	673
	.byte	8,0,2,35,0,11
	.byte	'SDVOUTSEL',0,1
	.word	673
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	690
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	673
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVRTRIM_Bits',0,4,139,4,3
	.word	40773
	.byte	10
	.byte	'_Ifx_SCU_EVRUVMON_Bits',0,4,142,4,16,4,11
	.byte	'EVR13UVVAL',0,1
	.word	673
	.byte	8,0,2,35,0,11
	.byte	'EVR33UVVAL',0,1
	.word	673
	.byte	8,0,2,35,1,11
	.byte	'SWDUVVAL',0,1
	.word	673
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	673
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	673
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVRUVMON_Bits',0,4,149,4,3
	.word	40912
	.byte	10
	.byte	'_Ifx_SCU_EXTCON_Bits',0,4,152,4,16,4,11
	.byte	'EN0',0,1
	.word	673
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	673
	.byte	1,6,2,35,0,11
	.byte	'SEL0',0,1
	.word	673
	.byte	4,2,2,35,0,11
	.byte	'reserved_6',0,2
	.word	690
	.byte	10,0,2,35,0,11
	.byte	'EN1',0,1
	.word	673
	.byte	1,7,2,35,2,11
	.byte	'NSEL',0,1
	.word	673
	.byte	1,6,2,35,2,11
	.byte	'SEL1',0,1
	.word	673
	.byte	4,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	673
	.byte	2,0,2,35,2,11
	.byte	'DIV1',0,1
	.word	673
	.byte	8,0,2,35,3,0,22
	.byte	'Ifx_SCU_EXTCON_Bits',0,4,163,4,3
	.word	41075
	.byte	10
	.byte	'_Ifx_SCU_FDR_Bits',0,4,166,4,16,4,11
	.byte	'STEP',0,2
	.word	690
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	673
	.byte	4,2,2,35,1,11
	.byte	'DM',0,1
	.word	673
	.byte	2,0,2,35,1,11
	.byte	'RESULT',0,2
	.word	690
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	673
	.byte	5,1,2,35,3,11
	.byte	'DISCLK',0,1
	.word	673
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_FDR_Bits',0,4,174,4,3
	.word	41293
	.byte	10
	.byte	'_Ifx_SCU_FMR_Bits',0,4,177,4,16,4,11
	.byte	'FS0',0,1
	.word	673
	.byte	1,7,2,35,0,11
	.byte	'FS1',0,1
	.word	673
	.byte	1,6,2,35,0,11
	.byte	'FS2',0,1
	.word	673
	.byte	1,5,2,35,0,11
	.byte	'FS3',0,1
	.word	673
	.byte	1,4,2,35,0,11
	.byte	'FS4',0,1
	.word	673
	.byte	1,3,2,35,0,11
	.byte	'FS5',0,1
	.word	673
	.byte	1,2,2,35,0,11
	.byte	'FS6',0,1
	.word	673
	.byte	1,1,2,35,0,11
	.byte	'FS7',0,1
	.word	673
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	673
	.byte	8,0,2,35,1,11
	.byte	'FC0',0,1
	.word	673
	.byte	1,7,2,35,2,11
	.byte	'FC1',0,1
	.word	673
	.byte	1,6,2,35,2,11
	.byte	'FC2',0,1
	.word	673
	.byte	1,5,2,35,2,11
	.byte	'FC3',0,1
	.word	673
	.byte	1,4,2,35,2,11
	.byte	'FC4',0,1
	.word	673
	.byte	1,3,2,35,2,11
	.byte	'FC5',0,1
	.word	673
	.byte	1,2,2,35,2,11
	.byte	'FC6',0,1
	.word	673
	.byte	1,1,2,35,2,11
	.byte	'FC7',0,1
	.word	673
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	673
	.byte	8,0,2,35,3,0,22
	.byte	'Ifx_SCU_FMR_Bits',0,4,197,4,3
	.word	41456
	.byte	10
	.byte	'_Ifx_SCU_ID_Bits',0,4,200,4,16,4,11
	.byte	'MODREV',0,1
	.word	673
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	673
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	690
	.byte	16,0,2,35,2,0,22
	.byte	'Ifx_SCU_ID_Bits',0,4,205,4,3
	.word	41792
	.byte	10
	.byte	'_Ifx_SCU_IGCR_Bits',0,4,208,4,16,4,11
	.byte	'IPEN00',0,1
	.word	673
	.byte	1,7,2,35,0,11
	.byte	'IPEN01',0,1
	.word	673
	.byte	1,6,2,35,0,11
	.byte	'IPEN02',0,1
	.word	673
	.byte	1,5,2,35,0,11
	.byte	'IPEN03',0,1
	.word	673
	.byte	1,4,2,35,0,11
	.byte	'IPEN04',0,1
	.word	673
	.byte	1,3,2,35,0,11
	.byte	'IPEN05',0,1
	.word	673
	.byte	1,2,2,35,0,11
	.byte	'IPEN06',0,1
	.word	673
	.byte	1,1,2,35,0,11
	.byte	'IPEN07',0,1
	.word	673
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	673
	.byte	5,3,2,35,1,11
	.byte	'GEEN0',0,1
	.word	673
	.byte	1,2,2,35,1,11
	.byte	'IGP0',0,1
	.word	673
	.byte	2,0,2,35,1,11
	.byte	'IPEN10',0,1
	.word	673
	.byte	1,7,2,35,2,11
	.byte	'IPEN11',0,1
	.word	673
	.byte	1,6,2,35,2,11
	.byte	'IPEN12',0,1
	.word	673
	.byte	1,5,2,35,2,11
	.byte	'IPEN13',0,1
	.word	673
	.byte	1,4,2,35,2,11
	.byte	'IPEN14',0,1
	.word	673
	.byte	1,3,2,35,2,11
	.byte	'IPEN15',0,1
	.word	673
	.byte	1,2,2,35,2,11
	.byte	'IPEN16',0,1
	.word	673
	.byte	1,1,2,35,2,11
	.byte	'IPEN17',0,1
	.word	673
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	673
	.byte	5,3,2,35,3,11
	.byte	'GEEN1',0,1
	.word	673
	.byte	1,2,2,35,3,11
	.byte	'IGP1',0,1
	.word	673
	.byte	2,0,2,35,3,0,22
	.byte	'Ifx_SCU_IGCR_Bits',0,4,232,4,3
	.word	41899
	.byte	10
	.byte	'_Ifx_SCU_IN_Bits',0,4,235,4,16,4,11
	.byte	'P0',0,1
	.word	673
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	673
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	498
	.byte	30,0,2,35,0,0,22
	.byte	'Ifx_SCU_IN_Bits',0,4,240,4,3
	.word	42351
	.byte	10
	.byte	'_Ifx_SCU_IOCR_Bits',0,4,243,4,16,4,11
	.byte	'reserved_0',0,1
	.word	673
	.byte	4,4,2,35,0,11
	.byte	'PC0',0,1
	.word	673
	.byte	4,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	673
	.byte	4,4,2,35,1,11
	.byte	'PC1',0,1
	.word	673
	.byte	4,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	690
	.byte	16,0,2,35,2,0,22
	.byte	'Ifx_SCU_IOCR_Bits',0,4,250,4,3
	.word	42450
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL0_Bits',0,4,253,4,16,4,11
	.byte	'LBISTREQ',0,1
	.word	673
	.byte	1,7,2,35,0,11
	.byte	'LBISTREQP',0,1
	.word	673
	.byte	1,6,2,35,0,11
	.byte	'PATTERNS',0,2
	.word	690
	.byte	14,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	690
	.byte	16,0,2,35,2,0,22
	.byte	'Ifx_SCU_LBISTCTRL0_Bits',0,4,131,5,3
	.word	42600
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL1_Bits',0,4,134,5,16,4,11
	.byte	'SEED',0,4
	.word	498
	.byte	23,9,2,35,0,11
	.byte	'reserved_23',0,1
	.word	673
	.byte	1,0,2,35,2,11
	.byte	'SPLITSH',0,1
	.word	673
	.byte	3,5,2,35,3,11
	.byte	'BODY',0,1
	.word	673
	.byte	1,4,2,35,3,11
	.byte	'LBISTFREQU',0,1
	.word	673
	.byte	4,0,2,35,3,0,22
	.byte	'Ifx_SCU_LBISTCTRL1_Bits',0,4,141,5,3
	.word	42749
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL2_Bits',0,4,144,5,16,4,11
	.byte	'SIGNATURE',0,4
	.word	498
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	673
	.byte	7,1,2,35,3,11
	.byte	'LBISTDONE',0,1
	.word	673
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_LBISTCTRL2_Bits',0,4,149,5,3
	.word	42910
	.byte	10
	.byte	'_Ifx_SCU_LCLCON_Bits',0,4,152,5,16,4,11
	.byte	'reserved_0',0,2
	.word	690
	.byte	16,0,2,35,0,11
	.byte	'LS',0,1
	.word	673
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,2
	.word	690
	.byte	14,1,2,35,2,11
	.byte	'LSEN',0,1
	.word	673
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_LCLCON_Bits',0,4,158,5,3
	.word	43040
	.byte	10
	.byte	'_Ifx_SCU_LCLTEST_Bits',0,4,161,5,16,4,11
	.byte	'LCLT0',0,1
	.word	673
	.byte	1,7,2,35,0,11
	.byte	'LCLT1',0,1
	.word	673
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	498
	.byte	30,0,2,35,0,0,22
	.byte	'Ifx_SCU_LCLTEST_Bits',0,4,166,5,3
	.word	43172
	.byte	10
	.byte	'_Ifx_SCU_MANID_Bits',0,4,169,5,16,4,11
	.byte	'DEPT',0,1
	.word	673
	.byte	5,3,2,35,0,11
	.byte	'MANUF',0,2
	.word	690
	.byte	11,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	690
	.byte	16,0,2,35,2,0,22
	.byte	'Ifx_SCU_MANID_Bits',0,4,174,5,3
	.word	43287
	.byte	10
	.byte	'_Ifx_SCU_OMR_Bits',0,4,177,5,16,4,11
	.byte	'PS0',0,1
	.word	673
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	673
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	690
	.byte	14,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	673
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	673
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	690
	.byte	14,0,2,35,2,0,22
	.byte	'Ifx_SCU_OMR_Bits',0,4,185,5,3
	.word	43398
	.byte	10
	.byte	'_Ifx_SCU_OSCCON_Bits',0,4,188,5,16,4,11
	.byte	'reserved_0',0,1
	.word	673
	.byte	1,7,2,35,0,11
	.byte	'PLLLV',0,1
	.word	673
	.byte	1,6,2,35,0,11
	.byte	'OSCRES',0,1
	.word	673
	.byte	1,5,2,35,0,11
	.byte	'GAINSEL',0,1
	.word	673
	.byte	2,3,2,35,0,11
	.byte	'MODE',0,1
	.word	673
	.byte	2,1,2,35,0,11
	.byte	'SHBY',0,1
	.word	673
	.byte	1,0,2,35,0,11
	.byte	'PLLHV',0,1
	.word	673
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	673
	.byte	1,6,2,35,1,11
	.byte	'X1D',0,1
	.word	673
	.byte	1,5,2,35,1,11
	.byte	'X1DEN',0,1
	.word	673
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	673
	.byte	4,0,2,35,1,11
	.byte	'OSCVAL',0,1
	.word	673
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	673
	.byte	2,1,2,35,2,11
	.byte	'APREN',0,1
	.word	673
	.byte	1,0,2,35,2,11
	.byte	'CAP0EN',0,1
	.word	673
	.byte	1,7,2,35,3,11
	.byte	'CAP1EN',0,1
	.word	673
	.byte	1,6,2,35,3,11
	.byte	'CAP2EN',0,1
	.word	673
	.byte	1,5,2,35,3,11
	.byte	'CAP3EN',0,1
	.word	673
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	673
	.byte	4,0,2,35,3,0,22
	.byte	'Ifx_SCU_OSCCON_Bits',0,4,209,5,3
	.word	43556
	.byte	10
	.byte	'_Ifx_SCU_OUT_Bits',0,4,212,5,16,4,11
	.byte	'P0',0,1
	.word	673
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	673
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	498
	.byte	30,0,2,35,0,0,22
	.byte	'Ifx_SCU_OUT_Bits',0,4,217,5,3
	.word	43968
	.byte	10
	.byte	'_Ifx_SCU_OVCCON_Bits',0,4,220,5,16,4,11
	.byte	'CSEL0',0,1
	.word	673
	.byte	1,7,2,35,0,11
	.byte	'CSEL1',0,1
	.word	673
	.byte	1,6,2,35,0,11
	.byte	'CSEL2',0,1
	.word	673
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,2
	.word	690
	.byte	13,0,2,35,0,11
	.byte	'OVSTRT',0,1
	.word	673
	.byte	1,7,2,35,2,11
	.byte	'OVSTP',0,1
	.word	673
	.byte	1,6,2,35,2,11
	.byte	'DCINVAL',0,1
	.word	673
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	673
	.byte	5,0,2,35,2,11
	.byte	'OVCONF',0,1
	.word	673
	.byte	1,7,2,35,3,11
	.byte	'POVCONF',0,1
	.word	673
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	673
	.byte	6,0,2,35,3,0,22
	.byte	'Ifx_SCU_OVCCON_Bits',0,4,233,5,3
	.word	44069
	.byte	10
	.byte	'_Ifx_SCU_OVCENABLE_Bits',0,4,236,5,16,4,11
	.byte	'OVEN0',0,1
	.word	673
	.byte	1,7,2,35,0,11
	.byte	'OVEN1',0,1
	.word	673
	.byte	1,6,2,35,0,11
	.byte	'OVEN2',0,1
	.word	673
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	498
	.byte	29,0,2,35,0,0,22
	.byte	'Ifx_SCU_OVCENABLE_Bits',0,4,242,5,3
	.word	44336
	.byte	10
	.byte	'_Ifx_SCU_PDISC_Bits',0,4,245,5,16,4,11
	.byte	'PDIS0',0,1
	.word	673
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	673
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	498
	.byte	30,0,2,35,0,0,22
	.byte	'Ifx_SCU_PDISC_Bits',0,4,250,5,3
	.word	44472
	.byte	10
	.byte	'_Ifx_SCU_PDR_Bits',0,4,253,5,16,4,11
	.byte	'PD0',0,1
	.word	673
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	673
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	673
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	673
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	498
	.byte	24,0,2,35,0,0,22
	.byte	'Ifx_SCU_PDR_Bits',0,4,132,6,3
	.word	44583
	.byte	10
	.byte	'_Ifx_SCU_PDRR_Bits',0,4,135,6,16,4,11
	.byte	'PDR0',0,1
	.word	673
	.byte	1,7,2,35,0,11
	.byte	'PDR1',0,1
	.word	673
	.byte	1,6,2,35,0,11
	.byte	'PDR2',0,1
	.word	673
	.byte	1,5,2,35,0,11
	.byte	'PDR3',0,1
	.word	673
	.byte	1,4,2,35,0,11
	.byte	'PDR4',0,1
	.word	673
	.byte	1,3,2,35,0,11
	.byte	'PDR5',0,1
	.word	673
	.byte	1,2,2,35,0,11
	.byte	'PDR6',0,1
	.word	673
	.byte	1,1,2,35,0,11
	.byte	'PDR7',0,1
	.word	673
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	498
	.byte	24,0,2,35,0,0,22
	.byte	'Ifx_SCU_PDRR_Bits',0,4,146,6,3
	.word	44716
	.byte	10
	.byte	'_Ifx_SCU_PLLCON0_Bits',0,4,149,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	673
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	673
	.byte	1,6,2,35,0,11
	.byte	'MODEN',0,1
	.word	673
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	673
	.byte	1,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	673
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	673
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	673
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	690
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	673
	.byte	7,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	673
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	673
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	673
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	673
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	673
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	673
	.byte	4,0,2,35,3,0,22
	.byte	'Ifx_SCU_PLLCON0_Bits',0,4,166,6,3
	.word	44919
	.byte	10
	.byte	'_Ifx_SCU_PLLCON1_Bits',0,4,169,6,16,4,11
	.byte	'K2DIV',0,1
	.word	673
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	673
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	673
	.byte	7,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	673
	.byte	1,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	673
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	690
	.byte	9,0,2,35,2,0,22
	.byte	'Ifx_SCU_PLLCON1_Bits',0,4,177,6,3
	.word	45275
	.byte	10
	.byte	'_Ifx_SCU_PLLCON2_Bits',0,4,180,6,16,4,11
	.byte	'MODCFG',0,2
	.word	690
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	690
	.byte	16,0,2,35,2,0,22
	.byte	'Ifx_SCU_PLLCON2_Bits',0,4,184,6,3
	.word	45453
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON0_Bits',0,4,187,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	673
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	673
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	673
	.byte	2,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	673
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	673
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	673
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	690
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	673
	.byte	5,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	673
	.byte	2,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	673
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	673
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	673
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	673
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	673
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	673
	.byte	4,0,2,35,3,0,22
	.byte	'Ifx_SCU_PLLERAYCON0_Bits',0,4,204,6,3
	.word	45553
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON1_Bits',0,4,207,6,16,4,11
	.byte	'K2DIV',0,1
	.word	673
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	673
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	673
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	673
	.byte	4,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	673
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	690
	.byte	9,0,2,35,2,0,22
	.byte	'Ifx_SCU_PLLERAYCON1_Bits',0,4,215,6,3
	.word	45923
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYSTAT_Bits',0,4,218,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	673
	.byte	1,7,2,35,0,11
	.byte	'PWDSTAT',0,1
	.word	673
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	673
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	673
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	673
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	673
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	498
	.byte	26,0,2,35,0,0,22
	.byte	'Ifx_SCU_PLLERAYSTAT_Bits',0,4,227,6,3
	.word	46109
	.byte	10
	.byte	'_Ifx_SCU_PLLSTAT_Bits',0,4,230,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	673
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	673
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	673
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	673
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	673
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	673
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	673
	.byte	1,1,2,35,0,11
	.byte	'MODRUN',0,1
	.word	673
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	498
	.byte	24,0,2,35,0,0,22
	.byte	'Ifx_SCU_PLLSTAT_Bits',0,4,241,6,3
	.word	46307
	.byte	10
	.byte	'_Ifx_SCU_PMCSR_Bits',0,4,244,6,16,4,11
	.byte	'REQSLP',0,1
	.word	673
	.byte	2,6,2,35,0,11
	.byte	'SMUSLP',0,1
	.word	673
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	673
	.byte	5,0,2,35,0,11
	.byte	'PMST',0,1
	.word	673
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	498
	.byte	21,0,2,35,0,0,22
	.byte	'Ifx_SCU_PMCSR_Bits',0,4,251,6,3
	.word	46540
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR0_Bits',0,4,254,6,16,4,11
	.byte	'reserved_0',0,1
	.word	673
	.byte	1,7,2,35,0,11
	.byte	'ESR1WKEN',0,1
	.word	673
	.byte	1,6,2,35,0,11
	.byte	'PINAWKEN',0,1
	.word	673
	.byte	1,5,2,35,0,11
	.byte	'PINBWKEN',0,1
	.word	673
	.byte	1,4,2,35,0,11
	.byte	'ESR0DFEN',0,1
	.word	673
	.byte	1,3,2,35,0,11
	.byte	'ESR0EDCON',0,1
	.word	673
	.byte	2,1,2,35,0,11
	.byte	'ESR1DFEN',0,1
	.word	673
	.byte	1,0,2,35,0,11
	.byte	'ESR1EDCON',0,1
	.word	673
	.byte	2,6,2,35,1,11
	.byte	'PINADFEN',0,1
	.word	673
	.byte	1,5,2,35,1,11
	.byte	'PINAEDCON',0,1
	.word	673
	.byte	2,3,2,35,1,11
	.byte	'PINBDFEN',0,1
	.word	673
	.byte	1,2,2,35,1,11
	.byte	'PINBEDCON',0,1
	.word	673
	.byte	2,0,2,35,1,11
	.byte	'SCREN',0,1
	.word	673
	.byte	1,7,2,35,2,11
	.byte	'STBYRAMSEL',0,1
	.word	673
	.byte	2,5,2,35,2,11
	.byte	'SCRCLKSEL',0,1
	.word	673
	.byte	1,4,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	673
	.byte	1,3,2,35,2,11
	.byte	'TRISTEN',0,1
	.word	673
	.byte	1,2,2,35,2,11
	.byte	'TRISTREQ',0,1
	.word	673
	.byte	1,1,2,35,2,11
	.byte	'PORSTDF',0,1
	.word	673
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	673
	.byte	1,7,2,35,3,11
	.byte	'DCDCSYNC',0,1
	.word	673
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	673
	.byte	3,3,2,35,3,11
	.byte	'ESR0TRIST',0,1
	.word	673
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	673
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	673
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_PMSWCR0_Bits',0,4,153,7,3
	.word	46692
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR1_Bits',0,4,156,7,16,4,11
	.byte	'SCRSTEN',0,1
	.word	673
	.byte	1,7,2,35,0,11
	.byte	'SCRSTREQ',0,1
	.word	673
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	673
	.byte	6,0,2,35,0,11
	.byte	'CPUIDLSEL',0,1
	.word	673
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	673
	.byte	1,4,2,35,1,11
	.byte	'IRADIS',0,1
	.word	673
	.byte	1,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	673
	.byte	3,0,2,35,1,11
	.byte	'SCRCFG',0,1
	.word	673
	.byte	8,0,2,35,2,11
	.byte	'CPUSEL',0,1
	.word	673
	.byte	3,5,2,35,3,11
	.byte	'STBYEVEN',0,1
	.word	673
	.byte	1,4,2,35,3,11
	.byte	'STBYEV',0,1
	.word	673
	.byte	3,1,2,35,3,11
	.byte	'LCK',0,1
	.word	673
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_PMSWCR1_Bits',0,4,170,7,3
	.word	47259
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR2_Bits',0,4,173,7,16,4,11
	.byte	'SCRINT',0,1
	.word	673
	.byte	8,0,2,35,0,11
	.byte	'BUSY',0,1
	.word	673
	.byte	1,7,2,35,1,11
	.byte	'SCRECC',0,1
	.word	673
	.byte	1,6,2,35,1,11
	.byte	'SCRWDT',0,1
	.word	673
	.byte	1,5,2,35,1,11
	.byte	'SCRRST',0,1
	.word	673
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	673
	.byte	4,0,2,35,1,11
	.byte	'TCINT',0,1
	.word	673
	.byte	8,0,2,35,2,11
	.byte	'TCINTREQ',0,1
	.word	673
	.byte	1,7,2,35,3,11
	.byte	'SMURST',0,1
	.word	673
	.byte	1,6,2,35,3,11
	.byte	'RST',0,1
	.word	673
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	673
	.byte	4,1,2,35,3,11
	.byte	'LCK',0,1
	.word	673
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_PMSWCR2_Bits',0,4,187,7,3
	.word	47553
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTAT_Bits',0,4,190,7,16,4,11
	.byte	'reserved_0',0,1
	.word	673
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKP',0,1
	.word	673
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUN',0,1
	.word	673
	.byte	1,4,2,35,0,11
	.byte	'PINAWKP',0,1
	.word	673
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUN',0,1
	.word	673
	.byte	1,2,2,35,0,11
	.byte	'PINBWKP',0,1
	.word	673
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUN',0,1
	.word	673
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	673
	.byte	1,7,2,35,1,11
	.byte	'PORSTDF',0,1
	.word	673
	.byte	1,6,2,35,1,11
	.byte	'HWCFGEVR',0,1
	.word	673
	.byte	3,3,2,35,1,11
	.byte	'STBYRAM',0,1
	.word	673
	.byte	2,1,2,35,1,11
	.byte	'TRIST',0,1
	.word	673
	.byte	1,0,2,35,1,11
	.byte	'SCRST',0,1
	.word	673
	.byte	1,7,2,35,2,11
	.byte	'SCRWKP',0,1
	.word	673
	.byte	1,6,2,35,2,11
	.byte	'SCR',0,1
	.word	673
	.byte	1,5,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	673
	.byte	1,4,2,35,2,11
	.byte	'ESR1WKEN',0,1
	.word	673
	.byte	1,3,2,35,2,11
	.byte	'PINAWKEN',0,1
	.word	673
	.byte	1,2,2,35,2,11
	.byte	'PINBWKEN',0,1
	.word	673
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	690
	.byte	4,5,2,35,2,11
	.byte	'ESR0TRIST',0,1
	.word	673
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	673
	.byte	4,0,2,35,3,0,22
	.byte	'Ifx_SCU_PMSWSTAT_Bits',0,4,214,7,3
	.word	47831
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTATCLR_Bits',0,4,217,7,16,4,11
	.byte	'reserved_0',0,1
	.word	673
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKPCLR',0,1
	.word	673
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUNCLR',0,1
	.word	673
	.byte	1,4,2,35,0,11
	.byte	'PINAWKPCLR',0,1
	.word	673
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUNCLR',0,1
	.word	673
	.byte	1,2,2,35,0,11
	.byte	'PINBWKPCLR',0,1
	.word	673
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUNCLR',0,1
	.word	673
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	673
	.byte	8,0,2,35,1,11
	.byte	'SCRSTCLR',0,1
	.word	673
	.byte	1,7,2,35,2,11
	.byte	'SCRWKPCLR',0,1
	.word	673
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	690
	.byte	14,0,2,35,2,0,22
	.byte	'Ifx_SCU_PMSWSTATCLR_Bits',0,4,230,7,3
	.word	48327
	.byte	10
	.byte	'_Ifx_SCU_RSTCON2_Bits',0,4,233,7,16,4,11
	.byte	'reserved_0',0,1
	.word	673
	.byte	1,7,2,35,0,11
	.byte	'CLRC',0,1
	.word	673
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	690
	.byte	10,4,2,35,0,11
	.byte	'CSS0',0,1
	.word	673
	.byte	1,3,2,35,1,11
	.byte	'CSS1',0,1
	.word	673
	.byte	1,2,2,35,1,11
	.byte	'CSS2',0,1
	.word	673
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	673
	.byte	1,0,2,35,1,11
	.byte	'USRINFO',0,2
	.word	690
	.byte	16,0,2,35,2,0,22
	.byte	'Ifx_SCU_RSTCON2_Bits',0,4,243,7,3
	.word	48640
	.byte	10
	.byte	'_Ifx_SCU_RSTCON_Bits',0,4,246,7,16,4,11
	.byte	'ESR0',0,1
	.word	673
	.byte	2,6,2,35,0,11
	.byte	'ESR1',0,1
	.word	673
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	673
	.byte	2,2,2,35,0,11
	.byte	'SMU',0,1
	.word	673
	.byte	2,0,2,35,0,11
	.byte	'SW',0,1
	.word	673
	.byte	2,6,2,35,1,11
	.byte	'STM0',0,1
	.word	673
	.byte	2,4,2,35,1,11
	.byte	'STM1',0,1
	.word	673
	.byte	2,2,2,35,1,11
	.byte	'STM2',0,1
	.word	673
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	690
	.byte	16,0,2,35,2,0,22
	.byte	'Ifx_SCU_RSTCON_Bits',0,4,129,8,3
	.word	48849
	.byte	10
	.byte	'_Ifx_SCU_RSTSTAT_Bits',0,4,132,8,16,4,11
	.byte	'ESR0',0,1
	.word	673
	.byte	1,7,2,35,0,11
	.byte	'ESR1',0,1
	.word	673
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	673
	.byte	1,5,2,35,0,11
	.byte	'SMU',0,1
	.word	673
	.byte	1,4,2,35,0,11
	.byte	'SW',0,1
	.word	673
	.byte	1,3,2,35,0,11
	.byte	'STM0',0,1
	.word	673
	.byte	1,2,2,35,0,11
	.byte	'STM1',0,1
	.word	673
	.byte	1,1,2,35,0,11
	.byte	'STM2',0,1
	.word	673
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	673
	.byte	8,0,2,35,1,11
	.byte	'PORST',0,1
	.word	673
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	673
	.byte	1,6,2,35,2,11
	.byte	'CB0',0,1
	.word	673
	.byte	1,5,2,35,2,11
	.byte	'CB1',0,1
	.word	673
	.byte	1,4,2,35,2,11
	.byte	'CB3',0,1
	.word	673
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	673
	.byte	2,1,2,35,2,11
	.byte	'EVR13',0,1
	.word	673
	.byte	1,0,2,35,2,11
	.byte	'EVR33',0,1
	.word	673
	.byte	1,7,2,35,3,11
	.byte	'SWD',0,1
	.word	673
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	673
	.byte	2,4,2,35,3,11
	.byte	'STBYR',0,1
	.word	673
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	673
	.byte	3,0,2,35,3,0,22
	.byte	'Ifx_SCU_RSTSTAT_Bits',0,4,155,8,3
	.word	49060
	.byte	10
	.byte	'_Ifx_SCU_SAFECON_Bits',0,4,158,8,16,4,11
	.byte	'HBT',0,1
	.word	673
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	498
	.byte	31,0,2,35,0,0,22
	.byte	'Ifx_SCU_SAFECON_Bits',0,4,162,8,3
	.word	49492
	.byte	10
	.byte	'_Ifx_SCU_STSTAT_Bits',0,4,165,8,16,4,11
	.byte	'HWCFG',0,1
	.word	673
	.byte	8,0,2,35,0,11
	.byte	'FTM',0,1
	.word	673
	.byte	7,1,2,35,1,11
	.byte	'MODE',0,1
	.word	673
	.byte	1,0,2,35,1,11
	.byte	'FCBAE',0,1
	.word	673
	.byte	1,7,2,35,2,11
	.byte	'LUDIS',0,1
	.word	673
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	673
	.byte	1,5,2,35,2,11
	.byte	'TRSTL',0,1
	.word	673
	.byte	1,4,2,35,2,11
	.byte	'SPDEN',0,1
	.word	673
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	673
	.byte	3,0,2,35,2,11
	.byte	'RAMINT',0,1
	.word	673
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	673
	.byte	7,0,2,35,3,0,22
	.byte	'Ifx_SCU_STSTAT_Bits',0,4,178,8,3
	.word	49588
	.byte	10
	.byte	'_Ifx_SCU_SWRSTCON_Bits',0,4,181,8,16,4,11
	.byte	'reserved_0',0,1
	.word	673
	.byte	1,7,2,35,0,11
	.byte	'SWRSTREQ',0,1
	.word	673
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	498
	.byte	30,0,2,35,0,0,22
	.byte	'Ifx_SCU_SWRSTCON_Bits',0,4,186,8,3
	.word	49848
	.byte	10
	.byte	'_Ifx_SCU_SYSCON_Bits',0,4,189,8,16,4,11
	.byte	'CCTRIG0',0,1
	.word	673
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	673
	.byte	1,6,2,35,0,11
	.byte	'RAMINTM',0,1
	.word	673
	.byte	2,4,2,35,0,11
	.byte	'SETLUDIS',0,1
	.word	673
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	673
	.byte	3,0,2,35,0,11
	.byte	'DATM',0,1
	.word	673
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,4
	.word	498
	.byte	23,0,2,35,0,0,22
	.byte	'Ifx_SCU_SYSCON_Bits',0,4,198,8,3
	.word	49973
	.byte	10
	.byte	'_Ifx_SCU_TRAPCLR_Bits',0,4,201,8,16,4,11
	.byte	'ESR0T',0,1
	.word	673
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	673
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	673
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	673
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	498
	.byte	28,0,2,35,0,0,22
	.byte	'Ifx_SCU_TRAPCLR_Bits',0,4,208,8,3
	.word	50170
	.byte	10
	.byte	'_Ifx_SCU_TRAPDIS_Bits',0,4,211,8,16,4,11
	.byte	'ESR0T',0,1
	.word	673
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	673
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	673
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	673
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	498
	.byte	28,0,2,35,0,0,22
	.byte	'Ifx_SCU_TRAPDIS_Bits',0,4,218,8,3
	.word	50323
	.byte	10
	.byte	'_Ifx_SCU_TRAPSET_Bits',0,4,221,8,16,4,11
	.byte	'ESR0T',0,1
	.word	673
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	673
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	673
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	673
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	498
	.byte	28,0,2,35,0,0,22
	.byte	'Ifx_SCU_TRAPSET_Bits',0,4,228,8,3
	.word	50476
	.byte	10
	.byte	'_Ifx_SCU_TRAPSTAT_Bits',0,4,231,8,16,4,11
	.byte	'ESR0T',0,1
	.word	673
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	673
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	673
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	673
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	498
	.byte	28,0,2,35,0,0,22
	.byte	'Ifx_SCU_TRAPSTAT_Bits',0,4,238,8,3
	.word	50629
	.byte	22
	.byte	'Ifx_SCU_WDTCPU_CON0_Bits',0,4,247,8,3
	.word	537
	.byte	22
	.byte	'Ifx_SCU_WDTCPU_CON1_Bits',0,4,134,9,3
	.word	712
	.byte	22
	.byte	'Ifx_SCU_WDTCPU_SR_Bits',0,4,150,9,3
	.word	956
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON0_Bits',0,4,153,9,16,4,11
	.byte	'ENDINIT',0,4
	.word	521
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	521
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	521
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	521
	.byte	16,0,2,35,0,0,22
	.byte	'Ifx_SCU_WDTS_CON0_Bits',0,4,159,9,3
	.word	50884
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON1_Bits',0,4,162,9,16,4,11
	.byte	'CLRIRF',0,1
	.word	673
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	673
	.byte	1,6,2,35,0,11
	.byte	'IR0',0,1
	.word	673
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	673
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	673
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	673
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	673
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	673
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	673
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	673
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	690
	.byte	16,0,2,35,2,0,22
	.byte	'Ifx_SCU_WDTS_CON1_Bits',0,4,175,9,3
	.word	51010
	.byte	10
	.byte	'_Ifx_SCU_WDTS_SR_Bits',0,4,178,9,16,4,11
	.byte	'AE',0,1
	.word	673
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	673
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	673
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	673
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	673
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	673
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	673
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	673
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	673
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	673
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	690
	.byte	16,0,2,35,2,0,22
	.byte	'Ifx_SCU_WDTS_SR_Bits',0,4,191,9,3
	.word	51262
	.byte	12,4,199,9,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33294
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_ACCEN0',0,4,204,9,3
	.word	51481
	.byte	12,4,207,9,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33851
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_ACCEN1',0,4,212,9,3
	.word	51545
	.byte	12,4,215,9,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33928
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_ARSTDIS',0,4,220,9,3
	.word	51609
	.byte	12,4,223,9,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34064
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_CCUCON0',0,4,228,9,3
	.word	51674
	.byte	12,4,231,9,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34344
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_CCUCON1',0,4,236,9,3
	.word	51739
	.byte	12,4,239,9,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34582
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_CCUCON2',0,4,244,9,3
	.word	51804
	.byte	12,4,247,9,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34710
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_CCUCON3',0,4,252,9,3
	.word	51869
	.byte	12,4,255,9,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34953
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_CCUCON4',0,4,132,10,3
	.word	51934
	.byte	12,4,135,10,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35188
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_CCUCON5',0,4,140,10,3
	.word	51999
	.byte	12,4,143,10,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35316
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_CCUCON6',0,4,148,10,3
	.word	52064
	.byte	12,4,151,10,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35416
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_CCUCON7',0,4,156,10,3
	.word	52129
	.byte	12,4,159,10,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35516
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_CHIPID',0,4,164,10,3
	.word	52194
	.byte	12,4,167,10,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35724
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_DTSCON',0,4,172,10,3
	.word	52258
	.byte	12,4,175,10,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35889
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_DTSLIM',0,4,180,10,3
	.word	52322
	.byte	12,4,183,10,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36072
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_DTSSTAT',0,4,188,10,3
	.word	52386
	.byte	12,4,191,10,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36226
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EICR',0,4,196,10,3
	.word	52451
	.byte	12,4,199,10,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36590
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EIFR',0,4,204,10,3
	.word	52513
	.byte	12,4,207,10,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36801
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EMSR',0,4,212,10,3
	.word	52575
	.byte	12,4,215,10,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37053
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_ESRCFG',0,4,220,10,3
	.word	52637
	.byte	12,4,223,10,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37171
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_ESROCFG',0,4,228,10,3
	.word	52701
	.byte	12,4,231,10,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37282
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVR13CON',0,4,236,10,3
	.word	52766
	.byte	12,4,239,10,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37445
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVR33CON',0,4,244,10,3
	.word	52832
	.byte	12,4,247,10,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37608
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVRADCSTAT',0,4,252,10,3
	.word	52898
	.byte	12,4,255,10,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37766
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVRDVSTAT',0,4,132,11,3
	.word	52966
	.byte	12,4,135,11,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37931
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVRMONCTRL',0,4,140,11,3
	.word	53033
	.byte	12,4,143,11,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38260
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVROSCCTRL',0,4,148,11,3
	.word	53101
	.byte	12,4,151,11,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38481
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVROVMON',0,4,156,11,3
	.word	53169
	.byte	12,4,159,11,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38644
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVRRSTCON',0,4,164,11,3
	.word	53235
	.byte	12,4,167,11,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38916
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVRSDCOEFF1',0,4,172,11,3
	.word	53302
	.byte	12,4,175,11,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39069
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVRSDCOEFF2',0,4,180,11,3
	.word	53371
	.byte	12,4,183,11,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39225
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVRSDCOEFF3',0,4,188,11,3
	.word	53440
	.byte	12,4,191,11,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39387
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVRSDCOEFF4',0,4,196,11,3
	.word	53509
	.byte	12,4,199,11,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39530
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVRSDCOEFF5',0,4,204,11,3
	.word	53578
	.byte	12,4,207,11,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39695
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVRSDCOEFF6',0,4,212,11,3
	.word	53647
	.byte	12,4,215,11,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39840
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVRSDCTRL1',0,4,220,11,3
	.word	53716
	.byte	12,4,223,11,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40021
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVRSDCTRL2',0,4,228,11,3
	.word	53784
	.byte	12,4,231,11,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40195
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVRSDCTRL3',0,4,236,11,3
	.word	53852
	.byte	12,4,239,11,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40355
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVRSDCTRL4',0,4,244,11,3
	.word	53920
	.byte	12,4,247,11,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40499
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVRSTAT',0,4,252,11,3
	.word	53988
	.byte	12,4,255,11,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40773
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVRTRIM',0,4,132,12,3
	.word	54053
	.byte	12,4,135,12,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40912
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVRUVMON',0,4,140,12,3
	.word	54118
	.byte	12,4,143,12,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41075
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EXTCON',0,4,148,12,3
	.word	54184
	.byte	12,4,151,12,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41293
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_FDR',0,4,156,12,3
	.word	54248
	.byte	12,4,159,12,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41456
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_FMR',0,4,164,12,3
	.word	54309
	.byte	12,4,167,12,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41792
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_ID',0,4,172,12,3
	.word	54370
	.byte	12,4,175,12,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41899
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_IGCR',0,4,180,12,3
	.word	54430
	.byte	12,4,183,12,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42351
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_IN',0,4,188,12,3
	.word	54492
	.byte	12,4,191,12,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42450
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_IOCR',0,4,196,12,3
	.word	54552
	.byte	12,4,199,12,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42600
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_LBISTCTRL0',0,4,204,12,3
	.word	54614
	.byte	12,4,207,12,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42749
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_LBISTCTRL1',0,4,212,12,3
	.word	54682
	.byte	12,4,215,12,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42910
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_LBISTCTRL2',0,4,220,12,3
	.word	54750
	.byte	12,4,223,12,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43040
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_LCLCON',0,4,228,12,3
	.word	54818
	.byte	12,4,231,12,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43172
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_LCLTEST',0,4,236,12,3
	.word	54882
	.byte	12,4,239,12,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43287
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_MANID',0,4,244,12,3
	.word	54947
	.byte	12,4,247,12,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43398
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_OMR',0,4,252,12,3
	.word	55010
	.byte	12,4,255,12,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43556
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_OSCCON',0,4,132,13,3
	.word	55071
	.byte	12,4,135,13,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43968
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_OUT',0,4,140,13,3
	.word	55135
	.byte	12,4,143,13,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44069
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_OVCCON',0,4,148,13,3
	.word	55196
	.byte	12,4,151,13,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44336
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_OVCENABLE',0,4,156,13,3
	.word	55260
	.byte	12,4,159,13,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44472
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_PDISC',0,4,164,13,3
	.word	55327
	.byte	12,4,167,13,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44583
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_PDR',0,4,172,13,3
	.word	55390
	.byte	12,4,175,13,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44716
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_PDRR',0,4,180,13,3
	.word	55451
	.byte	12,4,183,13,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44919
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_PLLCON0',0,4,188,13,3
	.word	55513
	.byte	12,4,191,13,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45275
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_PLLCON1',0,4,196,13,3
	.word	55578
	.byte	12,4,199,13,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45453
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_PLLCON2',0,4,204,13,3
	.word	55643
	.byte	12,4,207,13,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45553
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_PLLERAYCON0',0,4,212,13,3
	.word	55708
	.byte	12,4,215,13,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45923
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_PLLERAYCON1',0,4,220,13,3
	.word	55777
	.byte	12,4,223,13,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46109
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_PLLERAYSTAT',0,4,228,13,3
	.word	55846
	.byte	12,4,231,13,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46307
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_PLLSTAT',0,4,236,13,3
	.word	55915
	.byte	12,4,239,13,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46540
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_PMCSR',0,4,244,13,3
	.word	55980
	.byte	12,4,247,13,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46692
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_PMSWCR0',0,4,252,13,3
	.word	56043
	.byte	12,4,255,13,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47259
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_PMSWCR1',0,4,132,14,3
	.word	56108
	.byte	12,4,135,14,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47553
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_PMSWCR2',0,4,140,14,3
	.word	56173
	.byte	12,4,143,14,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47831
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_PMSWSTAT',0,4,148,14,3
	.word	56238
	.byte	12,4,151,14,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48327
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_PMSWSTATCLR',0,4,156,14,3
	.word	56304
	.byte	12,4,159,14,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48849
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_RSTCON',0,4,164,14,3
	.word	56373
	.byte	12,4,167,14,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48640
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_RSTCON2',0,4,172,14,3
	.word	56437
	.byte	12,4,175,14,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49060
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_RSTSTAT',0,4,180,14,3
	.word	56502
	.byte	12,4,183,14,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49492
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_SAFECON',0,4,188,14,3
	.word	56567
	.byte	12,4,191,14,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49588
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_STSTAT',0,4,196,14,3
	.word	56632
	.byte	12,4,199,14,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49848
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_SWRSTCON',0,4,204,14,3
	.word	56696
	.byte	12,4,207,14,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49973
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_SYSCON',0,4,212,14,3
	.word	56762
	.byte	12,4,215,14,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50170
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_TRAPCLR',0,4,220,14,3
	.word	56826
	.byte	12,4,223,14,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50323
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_TRAPDIS',0,4,228,14,3
	.word	56891
	.byte	12,4,231,14,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50476
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_TRAPSET',0,4,236,14,3
	.word	56956
	.byte	12,4,239,14,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50629
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_TRAPSTAT',0,4,244,14,3
	.word	57021
	.byte	22
	.byte	'Ifx_SCU_WDTCPU_CON0',0,4,252,14,3
	.word	633
	.byte	22
	.byte	'Ifx_SCU_WDTCPU_CON1',0,4,132,15,3
	.word	916
	.byte	22
	.byte	'Ifx_SCU_WDTCPU_SR',0,4,140,15,3
	.word	1147
	.byte	12,4,143,15,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50884
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_WDTS_CON0',0,4,148,15,3
	.word	57172
	.byte	12,4,151,15,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51010
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_WDTS_CON1',0,4,156,15,3
	.word	57239
	.byte	12,4,159,15,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51262
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_WDTS_SR',0,4,164,15,3
	.word	57306
	.byte	14
	.word	1187
	.byte	22
	.byte	'Ifx_SCU_WDTCPU',0,4,180,15,3
	.word	57371
	.byte	10
	.byte	'_Ifx_SCU_WDTS',0,4,183,15,25,12,13
	.byte	'CON0',0
	.word	57172
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	57239
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	57306
	.byte	4,2,35,8,0,14
	.word	57400
	.byte	22
	.byte	'Ifx_SCU_WDTS',0,4,188,15,3
	.word	57461
	.byte	15,8
	.word	52637
	.byte	16,1,0,15,20
	.word	673
	.byte	16,19,0,15,8
	.word	55980
	.byte	16,1,0,14
	.word	57400
	.byte	15,24
	.word	1187
	.byte	16,1,0,14
	.word	57520
	.byte	15,16
	.word	52451
	.byte	16,3,0,15,16
	.word	54430
	.byte	16,3,0,15,180,3
	.word	673
	.byte	16,179,3,0,10
	.byte	'_Ifx_SCU',0,4,201,15,25,128,8,13
	.byte	'reserved_0',0
	.word	4310
	.byte	8,2,35,0,13
	.byte	'ID',0
	.word	54370
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	2491
	.byte	4,2,35,12,13
	.byte	'OSCCON',0
	.word	55071
	.byte	4,2,35,16,13
	.byte	'PLLSTAT',0
	.word	55915
	.byte	4,2,35,20,13
	.byte	'PLLCON0',0
	.word	55513
	.byte	4,2,35,24,13
	.byte	'PLLCON1',0
	.word	55578
	.byte	4,2,35,28,13
	.byte	'PLLCON2',0
	.word	55643
	.byte	4,2,35,32,13
	.byte	'PLLERAYSTAT',0
	.word	55846
	.byte	4,2,35,36,13
	.byte	'PLLERAYCON0',0
	.word	55708
	.byte	4,2,35,40,13
	.byte	'PLLERAYCON1',0
	.word	55777
	.byte	4,2,35,44,13
	.byte	'CCUCON0',0
	.word	51674
	.byte	4,2,35,48,13
	.byte	'CCUCON1',0
	.word	51739
	.byte	4,2,35,52,13
	.byte	'FDR',0
	.word	54248
	.byte	4,2,35,56,13
	.byte	'EXTCON',0
	.word	54184
	.byte	4,2,35,60,13
	.byte	'CCUCON2',0
	.word	51804
	.byte	4,2,35,64,13
	.byte	'CCUCON3',0
	.word	51869
	.byte	4,2,35,68,13
	.byte	'CCUCON4',0
	.word	51934
	.byte	4,2,35,72,13
	.byte	'CCUCON5',0
	.word	51999
	.byte	4,2,35,76,13
	.byte	'RSTSTAT',0
	.word	56502
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	2491
	.byte	4,2,35,84,13
	.byte	'RSTCON',0
	.word	56373
	.byte	4,2,35,88,13
	.byte	'ARSTDIS',0
	.word	51609
	.byte	4,2,35,92,13
	.byte	'SWRSTCON',0
	.word	56696
	.byte	4,2,35,96,13
	.byte	'RSTCON2',0
	.word	56437
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	2491
	.byte	4,2,35,104,13
	.byte	'EVRRSTCON',0
	.word	53235
	.byte	4,2,35,108,13
	.byte	'ESRCFG',0
	.word	57488
	.byte	8,2,35,112,13
	.byte	'ESROCFG',0
	.word	52701
	.byte	4,2,35,120,13
	.byte	'SYSCON',0
	.word	56762
	.byte	4,2,35,124,13
	.byte	'CCUCON6',0
	.word	52064
	.byte	4,3,35,128,1,13
	.byte	'CCUCON7',0
	.word	52129
	.byte	4,3,35,132,1,13
	.byte	'reserved_88',0
	.word	57497
	.byte	20,3,35,136,1,13
	.byte	'PDR',0
	.word	55390
	.byte	4,3,35,156,1,13
	.byte	'IOCR',0
	.word	54552
	.byte	4,3,35,160,1,13
	.byte	'OUT',0
	.word	55135
	.byte	4,3,35,164,1,13
	.byte	'OMR',0
	.word	55010
	.byte	4,3,35,168,1,13
	.byte	'IN',0
	.word	54492
	.byte	4,3,35,172,1,13
	.byte	'EVRSTAT',0
	.word	53988
	.byte	4,3,35,176,1,13
	.byte	'EVRDVSTAT',0
	.word	52966
	.byte	4,3,35,180,1,13
	.byte	'EVR13CON',0
	.word	52766
	.byte	4,3,35,184,1,13
	.byte	'EVR33CON',0
	.word	52832
	.byte	4,3,35,188,1,13
	.byte	'STSTAT',0
	.word	56632
	.byte	4,3,35,192,1,13
	.byte	'reserved_C4',0
	.word	2491
	.byte	4,3,35,196,1,13
	.byte	'PMSWCR0',0
	.word	56043
	.byte	4,3,35,200,1,13
	.byte	'PMSWSTAT',0
	.word	56238
	.byte	4,3,35,204,1,13
	.byte	'PMSWSTATCLR',0
	.word	56304
	.byte	4,3,35,208,1,13
	.byte	'PMCSR',0
	.word	57506
	.byte	8,3,35,212,1,13
	.byte	'reserved_DC',0
	.word	2491
	.byte	4,3,35,220,1,13
	.byte	'DTSSTAT',0
	.word	52386
	.byte	4,3,35,224,1,13
	.byte	'DTSCON',0
	.word	52258
	.byte	4,3,35,228,1,13
	.byte	'PMSWCR1',0
	.word	56108
	.byte	4,3,35,232,1,13
	.byte	'PMSWCR2',0
	.word	56173
	.byte	4,3,35,236,1,13
	.byte	'WDTS',0
	.word	57515
	.byte	12,3,35,240,1,13
	.byte	'EMSR',0
	.word	52575
	.byte	4,3,35,252,1,13
	.byte	'WDTCPU',0
	.word	57529
	.byte	24,3,35,128,2,13
	.byte	'reserved_118',0
	.word	4650
	.byte	12,3,35,152,2,13
	.byte	'TRAPSTAT',0
	.word	57021
	.byte	4,3,35,164,2,13
	.byte	'TRAPSET',0
	.word	56956
	.byte	4,3,35,168,2,13
	.byte	'TRAPCLR',0
	.word	56826
	.byte	4,3,35,172,2,13
	.byte	'TRAPDIS',0
	.word	56891
	.byte	4,3,35,176,2,13
	.byte	'reserved_134',0
	.word	2491
	.byte	4,3,35,180,2,13
	.byte	'LCLCON1',0
	.word	54818
	.byte	4,3,35,184,2,13
	.byte	'LCLTEST',0
	.word	54882
	.byte	4,3,35,188,2,13
	.byte	'CHIPID',0
	.word	52194
	.byte	4,3,35,192,2,13
	.byte	'MANID',0
	.word	54947
	.byte	4,3,35,196,2,13
	.byte	'reserved_148',0
	.word	4310
	.byte	8,3,35,200,2,13
	.byte	'SAFECON',0
	.word	56567
	.byte	4,3,35,208,2,13
	.byte	'reserved_154',0
	.word	25086
	.byte	16,3,35,212,2,13
	.byte	'LBISTCTRL0',0
	.word	54614
	.byte	4,3,35,228,2,13
	.byte	'LBISTCTRL1',0
	.word	54682
	.byte	4,3,35,232,2,13
	.byte	'LBISTCTRL2',0
	.word	54750
	.byte	4,3,35,236,2,13
	.byte	'reserved_170',0
	.word	25066
	.byte	28,3,35,240,2,13
	.byte	'PDISC',0
	.word	55327
	.byte	4,3,35,140,3,13
	.byte	'reserved_190',0
	.word	4310
	.byte	8,3,35,144,3,13
	.byte	'EVRTRIM',0
	.word	54053
	.byte	4,3,35,152,3,13
	.byte	'EVRADCSTAT',0
	.word	52898
	.byte	4,3,35,156,3,13
	.byte	'EVRUVMON',0
	.word	54118
	.byte	4,3,35,160,3,13
	.byte	'EVROVMON',0
	.word	53169
	.byte	4,3,35,164,3,13
	.byte	'EVRMONCTRL',0
	.word	53033
	.byte	4,3,35,168,3,13
	.byte	'reserved_1AC',0
	.word	2491
	.byte	4,3,35,172,3,13
	.byte	'EVRSDCTRL1',0
	.word	53716
	.byte	4,3,35,176,3,13
	.byte	'EVRSDCTRL2',0
	.word	53784
	.byte	4,3,35,180,3,13
	.byte	'EVRSDCTRL3',0
	.word	53852
	.byte	4,3,35,184,3,13
	.byte	'EVRSDCTRL4',0
	.word	53920
	.byte	4,3,35,188,3,13
	.byte	'EVRSDCOEFF1',0
	.word	53302
	.byte	4,3,35,192,3,13
	.byte	'EVRSDCOEFF2',0
	.word	53371
	.byte	4,3,35,196,3,13
	.byte	'EVRSDCOEFF3',0
	.word	53440
	.byte	4,3,35,200,3,13
	.byte	'EVRSDCOEFF4',0
	.word	53509
	.byte	4,3,35,204,3,13
	.byte	'EVRSDCOEFF5',0
	.word	53578
	.byte	4,3,35,208,3,13
	.byte	'EVRSDCOEFF6',0
	.word	53647
	.byte	4,3,35,212,3,13
	.byte	'EVROSCCTRL',0
	.word	53101
	.byte	4,3,35,216,3,13
	.byte	'reserved_1DC',0
	.word	2491
	.byte	4,3,35,220,3,13
	.byte	'OVCENABLE',0
	.word	55260
	.byte	4,3,35,224,3,13
	.byte	'OVCCON',0
	.word	55196
	.byte	4,3,35,228,3,13
	.byte	'reserved_1E8',0
	.word	27939
	.byte	40,3,35,232,3,13
	.byte	'EICR',0
	.word	57534
	.byte	16,3,35,144,4,13
	.byte	'EIFR',0
	.word	52513
	.byte	4,3,35,160,4,13
	.byte	'FMR',0
	.word	54309
	.byte	4,3,35,164,4,13
	.byte	'PDRR',0
	.word	55451
	.byte	4,3,35,168,4,13
	.byte	'IGCR',0
	.word	57543
	.byte	16,3,35,172,4,13
	.byte	'reserved_23C',0
	.word	2491
	.byte	4,3,35,188,4,13
	.byte	'DTSLIM',0
	.word	52322
	.byte	4,3,35,192,4,13
	.byte	'reserved_244',0
	.word	57552
	.byte	180,3,3,35,196,4,13
	.byte	'ACCEN1',0
	.word	51545
	.byte	4,3,35,248,7,13
	.byte	'ACCEN0',0
	.word	51481
	.byte	4,3,35,252,7,0,14
	.word	57563
	.byte	22
	.byte	'Ifx_SCU',0,4,181,16,3
	.word	59553
	.byte	10
	.byte	'_Ifx_STM_ACCEN0_Bits',0,15,45,16,4,11
	.byte	'EN0',0,1
	.word	673
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	673
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	673
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	673
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	673
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	673
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	673
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	673
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	673
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	673
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	673
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	673
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	673
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	673
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	673
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	673
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	673
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	673
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	673
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	673
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	673
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	673
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	673
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	673
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	673
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	673
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	673
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	673
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	673
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	673
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	673
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	673
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_STM_ACCEN0_Bits',0,15,79,3
	.word	59575
	.byte	10
	.byte	'_Ifx_STM_ACCEN1_Bits',0,15,82,16,4,11
	.byte	'reserved_0',0,4
	.word	498
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_STM_ACCEN1_Bits',0,15,85,3
	.word	60132
	.byte	10
	.byte	'_Ifx_STM_CAP_Bits',0,15,88,16,4,11
	.byte	'STMCAP63_32',0,4
	.word	498
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_STM_CAP_Bits',0,15,91,3
	.word	60209
	.byte	10
	.byte	'_Ifx_STM_CAPSV_Bits',0,15,94,16,4,11
	.byte	'STMCAP63_32',0,4
	.word	498
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_STM_CAPSV_Bits',0,15,97,3
	.word	60281
	.byte	10
	.byte	'_Ifx_STM_CLC_Bits',0,15,100,16,4,11
	.byte	'DISR',0,1
	.word	673
	.byte	1,7,2,35,0,11
	.byte	'DISS',0,1
	.word	673
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	673
	.byte	1,5,2,35,0,11
	.byte	'EDIS',0,1
	.word	673
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	498
	.byte	28,0,2,35,0,0,22
	.byte	'Ifx_STM_CLC_Bits',0,15,107,3
	.word	60357
	.byte	10
	.byte	'_Ifx_STM_CMCON_Bits',0,15,110,16,4,11
	.byte	'MSIZE0',0,1
	.word	673
	.byte	5,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	673
	.byte	3,0,2,35,0,11
	.byte	'MSTART0',0,1
	.word	673
	.byte	5,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	673
	.byte	3,0,2,35,1,11
	.byte	'MSIZE1',0,1
	.word	673
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	673
	.byte	3,0,2,35,2,11
	.byte	'MSTART1',0,1
	.word	673
	.byte	5,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	673
	.byte	3,0,2,35,3,0,22
	.byte	'Ifx_STM_CMCON_Bits',0,15,120,3
	.word	60498
	.byte	10
	.byte	'_Ifx_STM_CMP_Bits',0,15,123,16,4,11
	.byte	'CMPVAL',0,4
	.word	498
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_STM_CMP_Bits',0,15,126,3
	.word	60716
	.byte	10
	.byte	'_Ifx_STM_ICR_Bits',0,15,129,1,16,4,11
	.byte	'CMP0EN',0,1
	.word	673
	.byte	1,7,2,35,0,11
	.byte	'CMP0IR',0,1
	.word	673
	.byte	1,6,2,35,0,11
	.byte	'CMP0OS',0,1
	.word	673
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	673
	.byte	1,4,2,35,0,11
	.byte	'CMP1EN',0,1
	.word	673
	.byte	1,3,2,35,0,11
	.byte	'CMP1IR',0,1
	.word	673
	.byte	1,2,2,35,0,11
	.byte	'CMP1OS',0,1
	.word	673
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,4
	.word	498
	.byte	25,0,2,35,0,0,22
	.byte	'Ifx_STM_ICR_Bits',0,15,139,1,3
	.word	60783
	.byte	10
	.byte	'_Ifx_STM_ID_Bits',0,15,142,1,16,4,11
	.byte	'MODREV',0,1
	.word	673
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	673
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	690
	.byte	16,0,2,35,2,0,22
	.byte	'Ifx_STM_ID_Bits',0,15,147,1,3
	.word	60986
	.byte	10
	.byte	'_Ifx_STM_ISCR_Bits',0,15,150,1,16,4,11
	.byte	'CMP0IRR',0,1
	.word	673
	.byte	1,7,2,35,0,11
	.byte	'CMP0IRS',0,1
	.word	673
	.byte	1,6,2,35,0,11
	.byte	'CMP1IRR',0,1
	.word	673
	.byte	1,5,2,35,0,11
	.byte	'CMP1IRS',0,1
	.word	673
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	498
	.byte	28,0,2,35,0,0,22
	.byte	'Ifx_STM_ISCR_Bits',0,15,157,1,3
	.word	61093
	.byte	10
	.byte	'_Ifx_STM_KRST0_Bits',0,15,160,1,16,4,11
	.byte	'RST',0,1
	.word	673
	.byte	1,7,2,35,0,11
	.byte	'RSTSTAT',0,1
	.word	673
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	498
	.byte	30,0,2,35,0,0,22
	.byte	'Ifx_STM_KRST0_Bits',0,15,165,1,3
	.word	61244
	.byte	10
	.byte	'_Ifx_STM_KRST1_Bits',0,15,168,1,16,4,11
	.byte	'RST',0,1
	.word	673
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	498
	.byte	31,0,2,35,0,0,22
	.byte	'Ifx_STM_KRST1_Bits',0,15,172,1,3
	.word	61355
	.byte	10
	.byte	'_Ifx_STM_KRSTCLR_Bits',0,15,175,1,16,4,11
	.byte	'CLR',0,1
	.word	673
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	498
	.byte	31,0,2,35,0,0,22
	.byte	'Ifx_STM_KRSTCLR_Bits',0,15,179,1,3
	.word	61447
	.byte	10
	.byte	'_Ifx_STM_OCS_Bits',0,15,182,1,16,4,11
	.byte	'reserved_0',0,4
	.word	498
	.byte	24,8,2,35,0,11
	.byte	'SUS',0,1
	.word	673
	.byte	4,4,2,35,3,11
	.byte	'SUS_P',0,1
	.word	673
	.byte	1,3,2,35,3,11
	.byte	'SUSSTA',0,1
	.word	673
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	673
	.byte	2,0,2,35,3,0,22
	.byte	'Ifx_STM_OCS_Bits',0,15,189,1,3
	.word	61543
	.byte	10
	.byte	'_Ifx_STM_TIM0_Bits',0,15,192,1,16,4,11
	.byte	'STM31_0',0,4
	.word	498
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_STM_TIM0_Bits',0,15,195,1,3
	.word	61689
	.byte	10
	.byte	'_Ifx_STM_TIM0SV_Bits',0,15,198,1,16,4,11
	.byte	'STM31_0',0,4
	.word	498
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_STM_TIM0SV_Bits',0,15,201,1,3
	.word	61761
	.byte	10
	.byte	'_Ifx_STM_TIM1_Bits',0,15,204,1,16,4,11
	.byte	'STM35_4',0,4
	.word	498
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_STM_TIM1_Bits',0,15,207,1,3
	.word	61837
	.byte	10
	.byte	'_Ifx_STM_TIM2_Bits',0,15,210,1,16,4,11
	.byte	'STM39_8',0,4
	.word	498
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_STM_TIM2_Bits',0,15,213,1,3
	.word	61909
	.byte	10
	.byte	'_Ifx_STM_TIM3_Bits',0,15,216,1,16,4,11
	.byte	'STM43_12',0,4
	.word	498
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_STM_TIM3_Bits',0,15,219,1,3
	.word	61981
	.byte	10
	.byte	'_Ifx_STM_TIM4_Bits',0,15,222,1,16,4,11
	.byte	'STM47_16',0,4
	.word	498
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_STM_TIM4_Bits',0,15,225,1,3
	.word	62054
	.byte	10
	.byte	'_Ifx_STM_TIM5_Bits',0,15,228,1,16,4,11
	.byte	'STM51_20',0,4
	.word	498
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_STM_TIM5_Bits',0,15,231,1,3
	.word	62127
	.byte	10
	.byte	'_Ifx_STM_TIM6_Bits',0,15,234,1,16,4,11
	.byte	'STM63_32',0,4
	.word	498
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_STM_TIM6_Bits',0,15,237,1,3
	.word	62200
	.byte	12,15,245,1,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59575
	.byte	4,2,35,0,0,22
	.byte	'Ifx_STM_ACCEN0',0,15,250,1,3
	.word	62273
	.byte	12,15,253,1,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60132
	.byte	4,2,35,0,0,22
	.byte	'Ifx_STM_ACCEN1',0,15,130,2,3
	.word	62337
	.byte	12,15,133,2,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60209
	.byte	4,2,35,0,0,22
	.byte	'Ifx_STM_CAP',0,15,138,2,3
	.word	62401
	.byte	12,15,141,2,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60281
	.byte	4,2,35,0,0,22
	.byte	'Ifx_STM_CAPSV',0,15,146,2,3
	.word	62462
	.byte	12,15,149,2,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60357
	.byte	4,2,35,0,0,22
	.byte	'Ifx_STM_CLC',0,15,154,2,3
	.word	62525
	.byte	12,15,157,2,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60498
	.byte	4,2,35,0,0,22
	.byte	'Ifx_STM_CMCON',0,15,162,2,3
	.word	62586
	.byte	12,15,165,2,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60716
	.byte	4,2,35,0,0,22
	.byte	'Ifx_STM_CMP',0,15,170,2,3
	.word	62649
	.byte	12,15,173,2,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60783
	.byte	4,2,35,0,0,22
	.byte	'Ifx_STM_ICR',0,15,178,2,3
	.word	62710
	.byte	12,15,181,2,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60986
	.byte	4,2,35,0,0,22
	.byte	'Ifx_STM_ID',0,15,186,2,3
	.word	62771
	.byte	12,15,189,2,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61093
	.byte	4,2,35,0,0,22
	.byte	'Ifx_STM_ISCR',0,15,194,2,3
	.word	62831
	.byte	12,15,197,2,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61244
	.byte	4,2,35,0,0,22
	.byte	'Ifx_STM_KRST0',0,15,202,2,3
	.word	62893
	.byte	12,15,205,2,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61355
	.byte	4,2,35,0,0,22
	.byte	'Ifx_STM_KRST1',0,15,210,2,3
	.word	62956
	.byte	12,15,213,2,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61447
	.byte	4,2,35,0,0,22
	.byte	'Ifx_STM_KRSTCLR',0,15,218,2,3
	.word	63019
	.byte	12,15,221,2,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61543
	.byte	4,2,35,0,0,22
	.byte	'Ifx_STM_OCS',0,15,226,2,3
	.word	63084
	.byte	12,15,229,2,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61689
	.byte	4,2,35,0,0,22
	.byte	'Ifx_STM_TIM0',0,15,234,2,3
	.word	63145
	.byte	12,15,237,2,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61761
	.byte	4,2,35,0,0,22
	.byte	'Ifx_STM_TIM0SV',0,15,242,2,3
	.word	63207
	.byte	12,15,245,2,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61837
	.byte	4,2,35,0,0,22
	.byte	'Ifx_STM_TIM1',0,15,250,2,3
	.word	63271
	.byte	12,15,253,2,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61909
	.byte	4,2,35,0,0,22
	.byte	'Ifx_STM_TIM2',0,15,130,3,3
	.word	63333
	.byte	12,15,133,3,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61981
	.byte	4,2,35,0,0,22
	.byte	'Ifx_STM_TIM3',0,15,138,3,3
	.word	63395
	.byte	12,15,141,3,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62054
	.byte	4,2,35,0,0,22
	.byte	'Ifx_STM_TIM4',0,15,146,3,3
	.word	63457
	.byte	12,15,149,3,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62127
	.byte	4,2,35,0,0,22
	.byte	'Ifx_STM_TIM5',0,15,154,3,3
	.word	63519
	.byte	12,15,157,3,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62200
	.byte	4,2,35,0,0,22
	.byte	'Ifx_STM_TIM6',0,15,162,3,3
	.word	63581
	.byte	17,16,236,10,9,1,18
	.byte	'IfxScu_CCUCON0_CLKSEL_fBack',0,0,18
	.byte	'IfxScu_CCUCON0_CLKSEL_fPll',0,1,0,22
	.byte	'IfxScu_CCUCON0_CLKSEL',0,16,240,10,3
	.word	63643
	.byte	17,16,250,10,9,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy16384',0,0,18
	.byte	'IfxScu_WDTCON1_IR_divBy256',0,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy64',0,2,0,22
	.byte	'IfxScu_WDTCON1_IR',0,16,255,10,3
	.word	63740
	.byte	10
	.byte	'_Ifx_FLASH_ACCEN0_Bits',0,17,45,16,4,11
	.byte	'EN0',0,1
	.word	673
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	673
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	673
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	673
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	673
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	673
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	673
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	673
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	673
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	673
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	673
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	673
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	673
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	673
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	673
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	673
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	673
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	673
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	673
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	673
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	673
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	673
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	673
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	673
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	673
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	673
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	673
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	673
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	673
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	673
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	673
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	673
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_FLASH_ACCEN0_Bits',0,17,79,3
	.word	63862
	.byte	10
	.byte	'_Ifx_FLASH_ACCEN1_Bits',0,17,82,16,4,11
	.byte	'reserved_0',0,4
	.word	498
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_FLASH_ACCEN1_Bits',0,17,85,3
	.word	64423
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_CFG_Bits',0,17,88,16,4,11
	.byte	'SEL',0,1
	.word	673
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	673
	.byte	2,0,2,35,0,11
	.byte	'CLR',0,1
	.word	673
	.byte	1,7,2,35,1,11
	.byte	'DIS',0,1
	.word	673
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	498
	.byte	22,0,2,35,0,0,22
	.byte	'Ifx_FLASH_CBAB_CFG_Bits',0,17,95,3
	.word	64504
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_STAT_Bits',0,17,98,16,4,11
	.byte	'VLD0',0,1
	.word	673
	.byte	1,7,2,35,0,11
	.byte	'VLD1',0,1
	.word	673
	.byte	1,6,2,35,0,11
	.byte	'VLD2',0,1
	.word	673
	.byte	1,5,2,35,0,11
	.byte	'VLD3',0,1
	.word	673
	.byte	1,4,2,35,0,11
	.byte	'VLD4',0,1
	.word	673
	.byte	1,3,2,35,0,11
	.byte	'VLD5',0,1
	.word	673
	.byte	1,2,2,35,0,11
	.byte	'VLD6',0,1
	.word	673
	.byte	1,1,2,35,0,11
	.byte	'VLD7',0,1
	.word	673
	.byte	1,0,2,35,0,11
	.byte	'VLD8',0,1
	.word	673
	.byte	1,7,2,35,1,11
	.byte	'VLD9',0,1
	.word	673
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	498
	.byte	22,0,2,35,0,0,22
	.byte	'Ifx_FLASH_CBAB_STAT_Bits',0,17,111,3
	.word	64657
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_TOP_Bits',0,17,114,16,4,11
	.byte	'reserved_0',0,1
	.word	673
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	498
	.byte	19,8,2,35,0,11
	.byte	'ERR',0,1
	.word	673
	.byte	6,2,2,35,3,11
	.byte	'VLD',0,1
	.word	673
	.byte	1,1,2,35,3,11
	.byte	'CLR',0,1
	.word	673
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_FLASH_CBAB_TOP_Bits',0,17,121,3
	.word	64905
	.byte	10
	.byte	'_Ifx_FLASH_COMM0_Bits',0,17,124,16,4,11
	.byte	'STATUS',0,1
	.word	673
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	498
	.byte	24,0,2,35,0,0,22
	.byte	'Ifx_FLASH_COMM0_Bits',0,17,128,1,3
	.word	65051
	.byte	10
	.byte	'_Ifx_FLASH_COMM1_Bits',0,17,131,1,16,4,11
	.byte	'STATUS',0,1
	.word	673
	.byte	8,0,2,35,0,11
	.byte	'DATA',0,1
	.word	673
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	690
	.byte	16,0,2,35,2,0,22
	.byte	'Ifx_FLASH_COMM1_Bits',0,17,136,1,3
	.word	65149
	.byte	10
	.byte	'_Ifx_FLASH_COMM2_Bits',0,17,139,1,16,4,11
	.byte	'STATUS',0,1
	.word	673
	.byte	8,0,2,35,0,11
	.byte	'DATA',0,1
	.word	673
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	690
	.byte	16,0,2,35,2,0,22
	.byte	'Ifx_FLASH_COMM2_Bits',0,17,144,1,3
	.word	65265
	.byte	10
	.byte	'_Ifx_FLASH_ECCRD_Bits',0,17,147,1,16,4,11
	.byte	'RCODE',0,4
	.word	498
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	690
	.byte	8,2,2,35,2,11
	.byte	'EDCERRINJ',0,1
	.word	673
	.byte	1,1,2,35,3,11
	.byte	'ECCORDIS',0,1
	.word	673
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_FLASH_ECCRD_Bits',0,17,153,1,3
	.word	65381
	.byte	10
	.byte	'_Ifx_FLASH_ECCRP_Bits',0,17,156,1,16,4,11
	.byte	'RCODE',0,4
	.word	498
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	690
	.byte	8,2,2,35,2,11
	.byte	'EDCERRINJ',0,1
	.word	673
	.byte	1,1,2,35,3,11
	.byte	'ECCORDIS',0,1
	.word	673
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_FLASH_ECCRP_Bits',0,17,162,1,3
	.word	65521
	.byte	10
	.byte	'_Ifx_FLASH_ECCW_Bits',0,17,165,1,16,4,11
	.byte	'WCODE',0,4
	.word	498
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	690
	.byte	8,2,2,35,2,11
	.byte	'DECENCDIS',0,1
	.word	673
	.byte	1,1,2,35,3,11
	.byte	'PECENCDIS',0,1
	.word	673
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_FLASH_ECCW_Bits',0,17,171,1,3
	.word	65661
	.byte	10
	.byte	'_Ifx_FLASH_FCON_Bits',0,17,174,1,16,4,11
	.byte	'WSPFLASH',0,1
	.word	673
	.byte	4,4,2,35,0,11
	.byte	'WSECPF',0,1
	.word	673
	.byte	2,2,2,35,0,11
	.byte	'WSDFLASH',0,2
	.word	690
	.byte	6,4,2,35,0,11
	.byte	'WSECDF',0,1
	.word	673
	.byte	3,1,2,35,1,11
	.byte	'IDLE',0,1
	.word	673
	.byte	1,0,2,35,1,11
	.byte	'ESLDIS',0,1
	.word	673
	.byte	1,7,2,35,2,11
	.byte	'SLEEP',0,1
	.word	673
	.byte	1,6,2,35,2,11
	.byte	'NSAFECC',0,1
	.word	673
	.byte	1,5,2,35,2,11
	.byte	'STALL',0,1
	.word	673
	.byte	1,4,2,35,2,11
	.byte	'RES21',0,1
	.word	673
	.byte	2,2,2,35,2,11
	.byte	'RES23',0,1
	.word	673
	.byte	2,0,2,35,2,11
	.byte	'VOPERM',0,1
	.word	673
	.byte	1,7,2,35,3,11
	.byte	'SQERM',0,1
	.word	673
	.byte	1,6,2,35,3,11
	.byte	'PROERM',0,1
	.word	673
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	673
	.byte	3,2,2,35,3,11
	.byte	'PR5V',0,1
	.word	673
	.byte	1,1,2,35,3,11
	.byte	'EOBM',0,1
	.word	673
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_FLASH_FCON_Bits',0,17,193,1,3
	.word	65800
	.byte	10
	.byte	'_Ifx_FLASH_FPRO_Bits',0,17,196,1,16,4,11
	.byte	'PROINP',0,1
	.word	673
	.byte	1,7,2,35,0,11
	.byte	'PRODISP',0,1
	.word	673
	.byte	1,6,2,35,0,11
	.byte	'PROIND',0,1
	.word	673
	.byte	1,5,2,35,0,11
	.byte	'PRODISD',0,1
	.word	673
	.byte	1,4,2,35,0,11
	.byte	'PROINHSMCOTP',0,1
	.word	673
	.byte	1,3,2,35,0,11
	.byte	'RES5',0,1
	.word	673
	.byte	1,2,2,35,0,11
	.byte	'PROINOTP',0,1
	.word	673
	.byte	1,1,2,35,0,11
	.byte	'RES7',0,1
	.word	673
	.byte	1,0,2,35,0,11
	.byte	'PROINDBG',0,1
	.word	673
	.byte	1,7,2,35,1,11
	.byte	'PRODISDBG',0,1
	.word	673
	.byte	1,6,2,35,1,11
	.byte	'PROINHSM',0,1
	.word	673
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	673
	.byte	5,0,2,35,1,11
	.byte	'DCFP',0,1
	.word	673
	.byte	1,7,2,35,2,11
	.byte	'DDFP',0,1
	.word	673
	.byte	1,6,2,35,2,11
	.byte	'DDFPX',0,1
	.word	673
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	673
	.byte	1,4,2,35,2,11
	.byte	'DDFD',0,1
	.word	673
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	673
	.byte	1,2,2,35,2,11
	.byte	'ENPE',0,1
	.word	673
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	673
	.byte	8,0,2,35,3,0,22
	.byte	'Ifx_FLASH_FPRO_Bits',0,17,218,1,3
	.word	66162
	.byte	10
	.byte	'_Ifx_FLASH_FSR_Bits',0,17,221,1,16,4,11
	.byte	'FABUSY',0,1
	.word	673
	.byte	1,7,2,35,0,11
	.byte	'D0BUSY',0,1
	.word	673
	.byte	1,6,2,35,0,11
	.byte	'RES1',0,1
	.word	673
	.byte	1,5,2,35,0,11
	.byte	'P0BUSY',0,1
	.word	673
	.byte	1,4,2,35,0,11
	.byte	'P1BUSY',0,1
	.word	673
	.byte	1,3,2,35,0,11
	.byte	'RES5',0,1
	.word	673
	.byte	1,2,2,35,0,11
	.byte	'RES6',0,1
	.word	673
	.byte	1,1,2,35,0,11
	.byte	'PROG',0,1
	.word	673
	.byte	1,0,2,35,0,11
	.byte	'ERASE',0,1
	.word	673
	.byte	1,7,2,35,1,11
	.byte	'PFPAGE',0,1
	.word	673
	.byte	1,6,2,35,1,11
	.byte	'DFPAGE',0,1
	.word	673
	.byte	1,5,2,35,1,11
	.byte	'OPER',0,1
	.word	673
	.byte	1,4,2,35,1,11
	.byte	'SQER',0,1
	.word	673
	.byte	1,3,2,35,1,11
	.byte	'PROER',0,1
	.word	673
	.byte	1,2,2,35,1,11
	.byte	'PFSBER',0,1
	.word	673
	.byte	1,1,2,35,1,11
	.byte	'PFDBER',0,1
	.word	673
	.byte	1,0,2,35,1,11
	.byte	'PFMBER',0,1
	.word	673
	.byte	1,7,2,35,2,11
	.byte	'RES17',0,1
	.word	673
	.byte	1,6,2,35,2,11
	.byte	'DFSBER',0,1
	.word	673
	.byte	1,5,2,35,2,11
	.byte	'DFDBER',0,1
	.word	673
	.byte	1,4,2,35,2,11
	.byte	'DFTBER',0,1
	.word	673
	.byte	1,3,2,35,2,11
	.byte	'DFMBER',0,1
	.word	673
	.byte	1,2,2,35,2,11
	.byte	'SRIADDERR',0,1
	.word	673
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	690
	.byte	2,7,2,35,2,11
	.byte	'PVER',0,1
	.word	673
	.byte	1,6,2,35,3,11
	.byte	'EVER',0,1
	.word	673
	.byte	1,5,2,35,3,11
	.byte	'SPND',0,1
	.word	673
	.byte	1,4,2,35,3,11
	.byte	'SLM',0,1
	.word	673
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	673
	.byte	1,2,2,35,3,11
	.byte	'ORIER',0,1
	.word	673
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	673
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_FLASH_FSR_Bits',0,17,254,1,3
	.word	66603
	.byte	10
	.byte	'_Ifx_FLASH_ID_Bits',0,17,129,2,16,4,11
	.byte	'MODREV',0,1
	.word	673
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	673
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	690
	.byte	16,0,2,35,2,0,22
	.byte	'Ifx_FLASH_ID_Bits',0,17,134,2,3
	.word	67209
	.byte	10
	.byte	'_Ifx_FLASH_MARD_Bits',0,17,137,2,16,4,11
	.byte	'HMARGIN',0,1
	.word	673
	.byte	1,7,2,35,0,11
	.byte	'SELD0',0,1
	.word	673
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	673
	.byte	1,5,2,35,0,11
	.byte	'SPND',0,1
	.word	673
	.byte	1,4,2,35,0,11
	.byte	'SPNDERR',0,1
	.word	673
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,2
	.word	690
	.byte	10,1,2,35,0,11
	.byte	'TRAPDIS',0,1
	.word	673
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	690
	.byte	16,0,2,35,2,0,22
	.byte	'Ifx_FLASH_MARD_Bits',0,17,147,2,3
	.word	67320
	.byte	10
	.byte	'_Ifx_FLASH_MARP_Bits',0,17,150,2,16,4,11
	.byte	'SELP0',0,1
	.word	673
	.byte	1,7,2,35,0,11
	.byte	'SELP1',0,1
	.word	673
	.byte	1,6,2,35,0,11
	.byte	'RES2',0,1
	.word	673
	.byte	1,5,2,35,0,11
	.byte	'RES3',0,1
	.word	673
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	690
	.byte	11,1,2,35,0,11
	.byte	'TRAPDIS',0,1
	.word	673
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	690
	.byte	16,0,2,35,2,0,22
	.byte	'Ifx_FLASH_MARP_Bits',0,17,159,2,3
	.word	67534
	.byte	10
	.byte	'_Ifx_FLASH_PROCOND_Bits',0,17,162,2,16,4,11
	.byte	'L',0,1
	.word	673
	.byte	1,7,2,35,0,11
	.byte	'NSAFECC',0,1
	.word	673
	.byte	1,6,2,35,0,11
	.byte	'RAMIN',0,1
	.word	673
	.byte	2,4,2,35,0,11
	.byte	'RAMINSEL',0,1
	.word	673
	.byte	4,0,2,35,0,11
	.byte	'OSCCFG',0,1
	.word	673
	.byte	1,7,2,35,1,11
	.byte	'MODE',0,1
	.word	673
	.byte	2,5,2,35,1,11
	.byte	'APREN',0,1
	.word	673
	.byte	1,4,2,35,1,11
	.byte	'CAP0EN',0,1
	.word	673
	.byte	1,3,2,35,1,11
	.byte	'CAP1EN',0,1
	.word	673
	.byte	1,2,2,35,1,11
	.byte	'CAP2EN',0,1
	.word	673
	.byte	1,1,2,35,1,11
	.byte	'CAP3EN',0,1
	.word	673
	.byte	1,0,2,35,1,11
	.byte	'ESR0CNT',0,2
	.word	690
	.byte	12,4,2,35,2,11
	.byte	'RES29',0,1
	.word	673
	.byte	2,2,2,35,3,11
	.byte	'RES30',0,1
	.word	673
	.byte	1,1,2,35,3,11
	.byte	'RPRO',0,1
	.word	673
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_FLASH_PROCOND_Bits',0,17,179,2,3
	.word	67721
	.byte	10
	.byte	'_Ifx_FLASH_PROCONDBG_Bits',0,17,182,2,16,4,11
	.byte	'OCDSDIS',0,1
	.word	673
	.byte	1,7,2,35,0,11
	.byte	'DBGIFLCK',0,1
	.word	673
	.byte	1,6,2,35,0,11
	.byte	'EDM',0,1
	.word	673
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	498
	.byte	28,0,2,35,0,0,22
	.byte	'Ifx_FLASH_PROCONDBG_Bits',0,17,188,2,3
	.word	68045
	.byte	10
	.byte	'_Ifx_FLASH_PROCONHSM_Bits',0,17,191,2,16,4,11
	.byte	'HSMDBGDIS',0,1
	.word	673
	.byte	1,7,2,35,0,11
	.byte	'DBGIFLCK',0,1
	.word	673
	.byte	1,6,2,35,0,11
	.byte	'TSTIFLCK',0,1
	.word	673
	.byte	1,5,2,35,0,11
	.byte	'HSMTSTDIS',0,1
	.word	673
	.byte	1,4,2,35,0,11
	.byte	'RES15',0,2
	.word	690
	.byte	12,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	690
	.byte	16,0,2,35,2,0,22
	.byte	'Ifx_FLASH_PROCONHSM_Bits',0,17,199,2,3
	.word	68188
	.byte	10
	.byte	'_Ifx_FLASH_PROCONHSMCOTP_Bits',0,17,202,2,16,4,11
	.byte	'HSMBOOTEN',0,1
	.word	673
	.byte	1,7,2,35,0,11
	.byte	'SSWWAIT',0,1
	.word	673
	.byte	1,6,2,35,0,11
	.byte	'HSMDX',0,1
	.word	673
	.byte	1,5,2,35,0,11
	.byte	'HSM6X',0,1
	.word	673
	.byte	1,4,2,35,0,11
	.byte	'HSM16X',0,1
	.word	673
	.byte	1,3,2,35,0,11
	.byte	'HSM17X',0,1
	.word	673
	.byte	1,2,2,35,0,11
	.byte	'S6ROM',0,1
	.word	673
	.byte	1,1,2,35,0,11
	.byte	'HSMENPINS',0,2
	.word	690
	.byte	2,7,2,35,0,11
	.byte	'HSMENRES',0,1
	.word	673
	.byte	2,5,2,35,1,11
	.byte	'DESTDBG',0,1
	.word	673
	.byte	2,3,2,35,1,11
	.byte	'BLKFLAN',0,1
	.word	673
	.byte	1,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	673
	.byte	2,0,2,35,1,11
	.byte	'S16ROM',0,1
	.word	673
	.byte	1,7,2,35,2,11
	.byte	'S17ROM',0,1
	.word	673
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	690
	.byte	14,0,2,35,2,0,22
	.byte	'Ifx_FLASH_PROCONHSMCOTP_Bits',0,17,219,2,3
	.word	68377
	.byte	10
	.byte	'_Ifx_FLASH_PROCONOTP_Bits',0,17,222,2,16,4,11
	.byte	'S0ROM',0,1
	.word	673
	.byte	1,7,2,35,0,11
	.byte	'S1ROM',0,1
	.word	673
	.byte	1,6,2,35,0,11
	.byte	'S2ROM',0,1
	.word	673
	.byte	1,5,2,35,0,11
	.byte	'S3ROM',0,1
	.word	673
	.byte	1,4,2,35,0,11
	.byte	'S4ROM',0,1
	.word	673
	.byte	1,3,2,35,0,11
	.byte	'S5ROM',0,1
	.word	673
	.byte	1,2,2,35,0,11
	.byte	'S6ROM',0,1
	.word	673
	.byte	1,1,2,35,0,11
	.byte	'S7ROM',0,1
	.word	673
	.byte	1,0,2,35,0,11
	.byte	'S8ROM',0,1
	.word	673
	.byte	1,7,2,35,1,11
	.byte	'S9ROM',0,1
	.word	673
	.byte	1,6,2,35,1,11
	.byte	'S10ROM',0,1
	.word	673
	.byte	1,5,2,35,1,11
	.byte	'S11ROM',0,1
	.word	673
	.byte	1,4,2,35,1,11
	.byte	'S12ROM',0,1
	.word	673
	.byte	1,3,2,35,1,11
	.byte	'S13ROM',0,1
	.word	673
	.byte	1,2,2,35,1,11
	.byte	'S14ROM',0,1
	.word	673
	.byte	1,1,2,35,1,11
	.byte	'S15ROM',0,1
	.word	673
	.byte	1,0,2,35,1,11
	.byte	'S16ROM',0,1
	.word	673
	.byte	1,7,2,35,2,11
	.byte	'S17ROM',0,1
	.word	673
	.byte	1,6,2,35,2,11
	.byte	'S18ROM',0,1
	.word	673
	.byte	1,5,2,35,2,11
	.byte	'S19ROM',0,1
	.word	673
	.byte	1,4,2,35,2,11
	.byte	'S20ROM',0,1
	.word	673
	.byte	1,3,2,35,2,11
	.byte	'S21ROM',0,1
	.word	673
	.byte	1,2,2,35,2,11
	.byte	'S22ROM',0,1
	.word	673
	.byte	1,1,2,35,2,11
	.byte	'S23ROM',0,1
	.word	673
	.byte	1,0,2,35,2,11
	.byte	'S24ROM',0,1
	.word	673
	.byte	1,7,2,35,3,11
	.byte	'S25ROM',0,1
	.word	673
	.byte	1,6,2,35,3,11
	.byte	'S26ROM',0,1
	.word	673
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	673
	.byte	2,3,2,35,3,11
	.byte	'BML',0,1
	.word	673
	.byte	2,1,2,35,3,11
	.byte	'TP',0,1
	.word	673
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_FLASH_PROCONOTP_Bits',0,17,254,2,3
	.word	68740
	.byte	10
	.byte	'_Ifx_FLASH_PROCONP_Bits',0,17,129,3,16,4,11
	.byte	'S0L',0,1
	.word	673
	.byte	1,7,2,35,0,11
	.byte	'S1L',0,1
	.word	673
	.byte	1,6,2,35,0,11
	.byte	'S2L',0,1
	.word	673
	.byte	1,5,2,35,0,11
	.byte	'S3L',0,1
	.word	673
	.byte	1,4,2,35,0,11
	.byte	'S4L',0,1
	.word	673
	.byte	1,3,2,35,0,11
	.byte	'S5L',0,1
	.word	673
	.byte	1,2,2,35,0,11
	.byte	'S6L',0,1
	.word	673
	.byte	1,1,2,35,0,11
	.byte	'S7L',0,1
	.word	673
	.byte	1,0,2,35,0,11
	.byte	'S8L',0,1
	.word	673
	.byte	1,7,2,35,1,11
	.byte	'S9L',0,1
	.word	673
	.byte	1,6,2,35,1,11
	.byte	'S10L',0,1
	.word	673
	.byte	1,5,2,35,1,11
	.byte	'S11L',0,1
	.word	673
	.byte	1,4,2,35,1,11
	.byte	'S12L',0,1
	.word	673
	.byte	1,3,2,35,1,11
	.byte	'S13L',0,1
	.word	673
	.byte	1,2,2,35,1,11
	.byte	'S14L',0,1
	.word	673
	.byte	1,1,2,35,1,11
	.byte	'S15L',0,1
	.word	673
	.byte	1,0,2,35,1,11
	.byte	'S16L',0,1
	.word	673
	.byte	1,7,2,35,2,11
	.byte	'S17L',0,1
	.word	673
	.byte	1,6,2,35,2,11
	.byte	'S18L',0,1
	.word	673
	.byte	1,5,2,35,2,11
	.byte	'S19L',0,1
	.word	673
	.byte	1,4,2,35,2,11
	.byte	'S20L',0,1
	.word	673
	.byte	1,3,2,35,2,11
	.byte	'S21L',0,1
	.word	673
	.byte	1,2,2,35,2,11
	.byte	'S22L',0,1
	.word	673
	.byte	1,1,2,35,2,11
	.byte	'S23L',0,1
	.word	673
	.byte	1,0,2,35,2,11
	.byte	'S24L',0,1
	.word	673
	.byte	1,7,2,35,3,11
	.byte	'S25L',0,1
	.word	673
	.byte	1,6,2,35,3,11
	.byte	'S26L',0,1
	.word	673
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	673
	.byte	4,1,2,35,3,11
	.byte	'RPRO',0,1
	.word	673
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_FLASH_PROCONP_Bits',0,17,160,3,3
	.word	69335
	.byte	10
	.byte	'_Ifx_FLASH_PROCONWOP_Bits',0,17,163,3,16,4,11
	.byte	'S0WOP',0,1
	.word	673
	.byte	1,7,2,35,0,11
	.byte	'S1WOP',0,1
	.word	673
	.byte	1,6,2,35,0,11
	.byte	'S2WOP',0,1
	.word	673
	.byte	1,5,2,35,0,11
	.byte	'S3WOP',0,1
	.word	673
	.byte	1,4,2,35,0,11
	.byte	'S4WOP',0,1
	.word	673
	.byte	1,3,2,35,0,11
	.byte	'S5WOP',0,1
	.word	673
	.byte	1,2,2,35,0,11
	.byte	'S6WOP',0,1
	.word	673
	.byte	1,1,2,35,0,11
	.byte	'S7WOP',0,1
	.word	673
	.byte	1,0,2,35,0,11
	.byte	'S8WOP',0,1
	.word	673
	.byte	1,7,2,35,1,11
	.byte	'S9WOP',0,1
	.word	673
	.byte	1,6,2,35,1,11
	.byte	'S10WOP',0,1
	.word	673
	.byte	1,5,2,35,1,11
	.byte	'S11WOP',0,1
	.word	673
	.byte	1,4,2,35,1,11
	.byte	'S12WOP',0,1
	.word	673
	.byte	1,3,2,35,1,11
	.byte	'S13WOP',0,1
	.word	673
	.byte	1,2,2,35,1,11
	.byte	'S14WOP',0,1
	.word	673
	.byte	1,1,2,35,1,11
	.byte	'S15WOP',0,1
	.word	673
	.byte	1,0,2,35,1,11
	.byte	'S16WOP',0,1
	.word	673
	.byte	1,7,2,35,2,11
	.byte	'S17WOP',0,1
	.word	673
	.byte	1,6,2,35,2,11
	.byte	'S18WOP',0,1
	.word	673
	.byte	1,5,2,35,2,11
	.byte	'S19WOP',0,1
	.word	673
	.byte	1,4,2,35,2,11
	.byte	'S20WOP',0,1
	.word	673
	.byte	1,3,2,35,2,11
	.byte	'S21WOP',0,1
	.word	673
	.byte	1,2,2,35,2,11
	.byte	'S22WOP',0,1
	.word	673
	.byte	1,1,2,35,2,11
	.byte	'S23WOP',0,1
	.word	673
	.byte	1,0,2,35,2,11
	.byte	'S24WOP',0,1
	.word	673
	.byte	1,7,2,35,3,11
	.byte	'S25WOP',0,1
	.word	673
	.byte	1,6,2,35,3,11
	.byte	'S26WOP',0,1
	.word	673
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	673
	.byte	4,1,2,35,3,11
	.byte	'DATM',0,1
	.word	673
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_FLASH_PROCONWOP_Bits',0,17,194,3,3
	.word	69859
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG0_Bits',0,17,197,3,16,4,11
	.byte	'TAG',0,1
	.word	673
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	498
	.byte	26,0,2,35,0,0,22
	.byte	'Ifx_FLASH_RDB_CFG0_Bits',0,17,201,3,3
	.word	70441
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG1_Bits',0,17,204,3,16,4,11
	.byte	'TAG',0,1
	.word	673
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	498
	.byte	26,0,2,35,0,0,22
	.byte	'Ifx_FLASH_RDB_CFG1_Bits',0,17,208,3,3
	.word	70543
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG2_Bits',0,17,211,3,16,4,11
	.byte	'TAG',0,1
	.word	673
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	498
	.byte	26,0,2,35,0,0,22
	.byte	'Ifx_FLASH_RDB_CFG2_Bits',0,17,215,3,3
	.word	70645
	.byte	10
	.byte	'_Ifx_FLASH_RRAD_Bits',0,17,218,3,16,4,11
	.byte	'reserved_0',0,1
	.word	673
	.byte	3,5,2,35,0,11
	.byte	'ADD',0,4
	.word	498
	.byte	29,0,2,35,0,0,22
	.byte	'Ifx_FLASH_RRAD_Bits',0,17,222,3,3
	.word	70747
	.byte	10
	.byte	'_Ifx_FLASH_RRCT_Bits',0,17,225,3,16,4,11
	.byte	'STRT',0,1
	.word	673
	.byte	1,7,2,35,0,11
	.byte	'STP',0,1
	.word	673
	.byte	1,6,2,35,0,11
	.byte	'BUSY',0,1
	.word	673
	.byte	1,5,2,35,0,11
	.byte	'DONE',0,1
	.word	673
	.byte	1,4,2,35,0,11
	.byte	'ERR',0,1
	.word	673
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	673
	.byte	3,0,2,35,0,11
	.byte	'EOBM',0,1
	.word	673
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	673
	.byte	7,0,2,35,1,11
	.byte	'CNT',0,2
	.word	690
	.byte	16,0,2,35,2,0,22
	.byte	'Ifx_FLASH_RRCT_Bits',0,17,236,3,3
	.word	70841
	.byte	10
	.byte	'_Ifx_FLASH_RRD0_Bits',0,17,239,3,16,4,11
	.byte	'DATA',0,4
	.word	498
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_FLASH_RRD0_Bits',0,17,242,3,3
	.word	71051
	.byte	10
	.byte	'_Ifx_FLASH_RRD1_Bits',0,17,245,3,16,4,11
	.byte	'DATA',0,4
	.word	498
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_FLASH_RRD1_Bits',0,17,248,3,3
	.word	71124
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_CFG_Bits',0,17,251,3,16,4,11
	.byte	'SEL',0,1
	.word	673
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	673
	.byte	2,0,2,35,0,11
	.byte	'CLR',0,1
	.word	673
	.byte	1,7,2,35,1,11
	.byte	'DIS',0,1
	.word	673
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	498
	.byte	22,0,2,35,0,0,22
	.byte	'Ifx_FLASH_UBAB_CFG_Bits',0,17,130,4,3
	.word	71197
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_STAT_Bits',0,17,133,4,16,4,11
	.byte	'VLD0',0,1
	.word	673
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	498
	.byte	31,0,2,35,0,0,22
	.byte	'Ifx_FLASH_UBAB_STAT_Bits',0,17,137,4,3
	.word	71352
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_TOP_Bits',0,17,140,4,16,4,11
	.byte	'reserved_0',0,1
	.word	673
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	498
	.byte	19,8,2,35,0,11
	.byte	'ERR',0,1
	.word	673
	.byte	6,2,2,35,3,11
	.byte	'VLD',0,1
	.word	673
	.byte	1,1,2,35,3,11
	.byte	'CLR',0,1
	.word	673
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_FLASH_UBAB_TOP_Bits',0,17,147,4,3
	.word	71457
	.byte	12,17,155,4,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63862
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_ACCEN0',0,17,160,4,3
	.word	71605
	.byte	12,17,163,4,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64423
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_ACCEN1',0,17,168,4,3
	.word	71671
	.byte	12,17,171,4,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64504
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_CBAB_CFG',0,17,176,4,3
	.word	71737
	.byte	12,17,179,4,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64657
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_CBAB_STAT',0,17,184,4,3
	.word	71805
	.byte	12,17,187,4,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64905
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_CBAB_TOP',0,17,192,4,3
	.word	71874
	.byte	12,17,195,4,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65051
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_COMM0',0,17,200,4,3
	.word	71942
	.byte	12,17,203,4,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65149
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_COMM1',0,17,208,4,3
	.word	72007
	.byte	12,17,211,4,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65265
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_COMM2',0,17,216,4,3
	.word	72072
	.byte	12,17,219,4,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65381
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_ECCRD',0,17,224,4,3
	.word	72137
	.byte	12,17,227,4,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65521
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_ECCRP',0,17,232,4,3
	.word	72202
	.byte	12,17,235,4,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65661
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_ECCW',0,17,240,4,3
	.word	72267
	.byte	12,17,243,4,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65800
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_FCON',0,17,248,4,3
	.word	72331
	.byte	12,17,251,4,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66162
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_FPRO',0,17,128,5,3
	.word	72395
	.byte	12,17,131,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66603
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_FSR',0,17,136,5,3
	.word	72459
	.byte	12,17,139,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67209
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_ID',0,17,144,5,3
	.word	72522
	.byte	12,17,147,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67320
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_MARD',0,17,152,5,3
	.word	72584
	.byte	12,17,155,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67534
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_MARP',0,17,160,5,3
	.word	72648
	.byte	12,17,163,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67721
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_PROCOND',0,17,168,5,3
	.word	72712
	.byte	12,17,171,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68045
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_PROCONDBG',0,17,176,5,3
	.word	72779
	.byte	12,17,179,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68188
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_PROCONHSM',0,17,184,5,3
	.word	72848
	.byte	12,17,187,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68377
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_PROCONHSMCOTP',0,17,192,5,3
	.word	72917
	.byte	12,17,195,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68740
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_PROCONOTP',0,17,200,5,3
	.word	72990
	.byte	12,17,203,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69335
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_PROCONP',0,17,208,5,3
	.word	73059
	.byte	12,17,211,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69859
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_PROCONWOP',0,17,216,5,3
	.word	73126
	.byte	12,17,219,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70441
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_RDB_CFG0',0,17,224,5,3
	.word	73195
	.byte	12,17,227,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70543
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_RDB_CFG1',0,17,232,5,3
	.word	73263
	.byte	12,17,235,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70645
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_RDB_CFG2',0,17,240,5,3
	.word	73331
	.byte	12,17,243,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70747
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_RRAD',0,17,248,5,3
	.word	73399
	.byte	12,17,251,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70841
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_RRCT',0,17,128,6,3
	.word	73463
	.byte	12,17,131,6,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71051
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_RRD0',0,17,136,6,3
	.word	73527
	.byte	12,17,139,6,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71124
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_RRD1',0,17,144,6,3
	.word	73591
	.byte	12,17,147,6,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71197
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_UBAB_CFG',0,17,152,6,3
	.word	73655
	.byte	12,17,155,6,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71352
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_UBAB_STAT',0,17,160,6,3
	.word	73723
	.byte	12,17,163,6,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71457
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_UBAB_TOP',0,17,168,6,3
	.word	73792
	.byte	10
	.byte	'_Ifx_FLASH_CBAB',0,17,179,6,25,12,13
	.byte	'CFG',0
	.word	71737
	.byte	4,2,35,0,13
	.byte	'STAT',0
	.word	71805
	.byte	4,2,35,4,13
	.byte	'TOP',0
	.word	71874
	.byte	4,2,35,8,0,14
	.word	73860
	.byte	22
	.byte	'Ifx_FLASH_CBAB',0,17,184,6,3
	.word	73923
	.byte	10
	.byte	'_Ifx_FLASH_RDB',0,17,187,6,25,12,13
	.byte	'CFG0',0
	.word	73195
	.byte	4,2,35,0,13
	.byte	'CFG1',0
	.word	73263
	.byte	4,2,35,4,13
	.byte	'CFG2',0
	.word	73331
	.byte	4,2,35,8,0,14
	.word	73952
	.byte	22
	.byte	'Ifx_FLASH_RDB',0,17,192,6,3
	.word	74016
	.byte	10
	.byte	'_Ifx_FLASH_UBAB',0,17,195,6,25,12,13
	.byte	'CFG',0
	.word	73655
	.byte	4,2,35,0,13
	.byte	'STAT',0
	.word	73723
	.byte	4,2,35,4,13
	.byte	'TOP',0
	.word	73792
	.byte	4,2,35,8,0,14
	.word	74044
	.byte	22
	.byte	'Ifx_FLASH_UBAB',0,17,200,6,3
	.word	74107
	.byte	22
	.byte	'Ifx_P_ACCEN0_Bits',0,6,79,3
	.word	8063
	.byte	22
	.byte	'Ifx_P_ACCEN1_Bits',0,6,85,3
	.word	7976
	.byte	22
	.byte	'Ifx_P_ESR_Bits',0,6,107,3
	.word	4319
	.byte	22
	.byte	'Ifx_P_ID_Bits',0,6,115,3
	.word	2372
	.byte	22
	.byte	'Ifx_P_IN_Bits',0,6,137,1,3
	.word	3367
	.byte	22
	.byte	'Ifx_P_IOCR0_Bits',0,6,150,1,3
	.word	2500
	.byte	22
	.byte	'Ifx_P_IOCR12_Bits',0,6,163,1,3
	.word	3147
	.byte	22
	.byte	'Ifx_P_IOCR4_Bits',0,6,176,1,3
	.word	2715
	.byte	22
	.byte	'Ifx_P_IOCR8_Bits',0,6,189,1,3
	.word	2930
	.byte	22
	.byte	'Ifx_P_LPCR0_Bits',0,6,197,1,3
	.word	7335
	.byte	22
	.byte	'Ifx_P_LPCR1_Bits',0,6,205,1,3
	.word	7459
	.byte	22
	.byte	'Ifx_P_LPCR1_P21_Bits',0,6,215,1,3
	.word	7543
	.byte	22
	.byte	'Ifx_P_LPCR2_Bits',0,6,229,1,3
	.word	7723
	.byte	22
	.byte	'Ifx_P_OMCR0_Bits',0,6,240,1,3
	.word	5974
	.byte	22
	.byte	'Ifx_P_OMCR12_Bits',0,6,250,1,3
	.word	6498
	.byte	22
	.byte	'Ifx_P_OMCR4_Bits',0,6,133,2,3
	.word	6148
	.byte	22
	.byte	'Ifx_P_OMCR8_Bits',0,6,144,2,3
	.word	6322
	.byte	22
	.byte	'Ifx_P_OMCR_Bits',0,6,166,2,3
	.word	6987
	.byte	22
	.byte	'Ifx_P_OMR_Bits',0,6,203,2,3
	.word	1801
	.byte	22
	.byte	'Ifx_P_OMSR0_Bits',0,6,213,2,3
	.word	5311
	.byte	22
	.byte	'Ifx_P_OMSR12_Bits',0,6,224,2,3
	.word	5799
	.byte	22
	.byte	'Ifx_P_OMSR4_Bits',0,6,235,2,3
	.word	5458
	.byte	22
	.byte	'Ifx_P_OMSR8_Bits',0,6,246,2,3
	.word	5627
	.byte	22
	.byte	'Ifx_P_OMSR_Bits',0,6,140,3,3
	.word	6654
	.byte	22
	.byte	'Ifx_P_OUT_Bits',0,6,162,3,3
	.word	1485
	.byte	22
	.byte	'Ifx_P_PCSR_Bits',0,6,180,3,3
	.word	5025
	.byte	22
	.byte	'Ifx_P_PDISC_Bits',0,6,202,3,3
	.word	4659
	.byte	22
	.byte	'Ifx_P_PDR0_Bits',0,6,223,3,3
	.word	3690
	.byte	22
	.byte	'Ifx_P_PDR1_Bits',0,6,244,3,3
	.word	3994
	.byte	22
	.byte	'Ifx_P_ACCEN0',0,6,129,4,3
	.word	8590
	.byte	22
	.byte	'Ifx_P_ACCEN1',0,6,137,4,3
	.word	8023
	.byte	22
	.byte	'Ifx_P_ESR',0,6,145,4,3
	.word	4610
	.byte	22
	.byte	'Ifx_P_ID',0,6,153,4,3
	.word	2451
	.byte	22
	.byte	'Ifx_P_IN',0,6,161,4,3
	.word	3641
	.byte	22
	.byte	'Ifx_P_IOCR0',0,6,169,4,3
	.word	2675
	.byte	22
	.byte	'Ifx_P_IOCR12',0,6,177,4,3
	.word	3327
	.byte	22
	.byte	'Ifx_P_IOCR4',0,6,185,4,3
	.word	2890
	.byte	22
	.byte	'Ifx_P_IOCR8',0,6,193,4,3
	.word	3107
	.byte	22
	.byte	'Ifx_P_LPCR0',0,6,201,4,3
	.word	7419
	.byte	22
	.byte	'Ifx_P_LPCR1',0,6,210,4,3
	.word	7668
	.byte	22
	.byte	'Ifx_P_LPCR2',0,6,218,4,3
	.word	7927
	.byte	22
	.byte	'Ifx_P_OMCR',0,6,226,4,3
	.word	7295
	.byte	22
	.byte	'Ifx_P_OMCR0',0,6,234,4,3
	.word	6108
	.byte	22
	.byte	'Ifx_P_OMCR12',0,6,242,4,3
	.word	6614
	.byte	22
	.byte	'Ifx_P_OMCR4',0,6,250,4,3
	.word	6282
	.byte	22
	.byte	'Ifx_P_OMCR8',0,6,130,5,3
	.word	6458
	.byte	22
	.byte	'Ifx_P_OMR',0,6,138,5,3
	.word	2332
	.byte	22
	.byte	'Ifx_P_OMSR',0,6,146,5,3
	.word	6947
	.byte	22
	.byte	'Ifx_P_OMSR0',0,6,154,5,3
	.word	5418
	.byte	22
	.byte	'Ifx_P_OMSR12',0,6,162,5,3
	.word	5934
	.byte	22
	.byte	'Ifx_P_OMSR4',0,6,170,5,3
	.word	5587
	.byte	22
	.byte	'Ifx_P_OMSR8',0,6,178,5,3
	.word	5759
	.byte	22
	.byte	'Ifx_P_OUT',0,6,186,5,3
	.word	1761
	.byte	22
	.byte	'Ifx_P_PCSR',0,6,194,5,3
	.word	5271
	.byte	22
	.byte	'Ifx_P_PDISC',0,6,202,5,3
	.word	4985
	.byte	22
	.byte	'Ifx_P_PDR0',0,6,210,5,3
	.word	3954
	.byte	22
	.byte	'Ifx_P_PDR1',0,6,218,5,3
	.word	4270
	.byte	14
	.word	8630
	.byte	22
	.byte	'Ifx_P',0,6,139,6,3
	.word	75454
	.byte	17,5,83,9,1,18
	.byte	'IfxPort_InputMode_undefined',0,127,18
	.byte	'IfxPort_InputMode_noPullDevice',0,0,18
	.byte	'IfxPort_InputMode_pullDown',0,8,18
	.byte	'IfxPort_InputMode_pullUp',0,16,0,22
	.byte	'IfxPort_InputMode',0,5,89,3
	.word	75474
	.byte	17,5,120,9,1,18
	.byte	'IfxPort_OutputIdx_general',0,128,1,18
	.byte	'IfxPort_OutputIdx_alt1',0,136,1,18
	.byte	'IfxPort_OutputIdx_alt2',0,144,1,18
	.byte	'IfxPort_OutputIdx_alt3',0,152,1,18
	.byte	'IfxPort_OutputIdx_alt4',0,160,1,18
	.byte	'IfxPort_OutputIdx_alt5',0,168,1,18
	.byte	'IfxPort_OutputIdx_alt6',0,176,1,18
	.byte	'IfxPort_OutputIdx_alt7',0,184,1,0,22
	.byte	'IfxPort_OutputIdx',0,5,130,1,3
	.word	75625
	.byte	17,5,134,1,9,1,18
	.byte	'IfxPort_OutputMode_pushPull',0,128,1,18
	.byte	'IfxPort_OutputMode_openDrain',0,192,1,0,22
	.byte	'IfxPort_OutputMode',0,5,138,1,3
	.word	75869
	.byte	17,5,144,1,9,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed1',0,0,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed2',0,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed3',0,2,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed4',0,3,18
	.byte	'IfxPort_PadDriver_lvdsSpeed1',0,4,18
	.byte	'IfxPort_PadDriver_lvdsSpeed2',0,5,18
	.byte	'IfxPort_PadDriver_lvdsSpeed3',0,6,18
	.byte	'IfxPort_PadDriver_lvdsSpeed4',0,7,18
	.byte	'IfxPort_PadDriver_ttlSpeed1',0,8,18
	.byte	'IfxPort_PadDriver_ttlSpeed2',0,9,18
	.byte	'IfxPort_PadDriver_ttlSpeed3',0,10,18
	.byte	'IfxPort_PadDriver_ttlSpeed4',0,11,0,22
	.byte	'IfxPort_PadDriver',0,5,158,1,3
	.word	75967
	.byte	22
	.byte	'IfxPort_State',0,5,178,1,3
	.word	9243
	.byte	24,5,190,1,9,8,13
	.byte	'port',0
	.word	9238
	.byte	4,2,35,0,13
	.byte	'pinIndex',0
	.word	673
	.byte	1,2,35,4,0,22
	.byte	'IfxPort_Pin',0,5,194,1,3
	.word	76432
	.byte	22
	.byte	'IfxScuCcu_PllStepsFunctionHook',0,18,148,1,16
	.word	237
	.byte	24,18,212,5,9,8,13
	.byte	'value',0
	.word	9579
	.byte	4,2,35,0,13
	.byte	'mask',0
	.word	9579
	.byte	4,2,35,4,0,22
	.byte	'IfxScuCcu_CcuconRegConfig',0,18,216,5,3
	.word	76532
	.byte	24,18,221,5,9,8,13
	.byte	'pDivider',0
	.word	673
	.byte	1,2,35,0,13
	.byte	'nDivider',0
	.word	673
	.byte	1,2,35,1,13
	.byte	'k2Initial',0
	.word	673
	.byte	1,2,35,2,13
	.byte	'waitTime',0
	.word	294
	.byte	4,2,35,4,0,22
	.byte	'IfxScuCcu_InitialStepConfig',0,18,227,5,3
	.word	76603
	.byte	24,18,231,5,9,12,13
	.byte	'k2Step',0
	.word	673
	.byte	1,2,35,0,13
	.byte	'waitTime',0
	.word	294
	.byte	4,2,35,2,13
	.byte	'hookFunction',0
	.word	76492
	.byte	4,2,35,8,0,22
	.byte	'IfxScuCcu_PllStepsConfig',0,18,236,5,3
	.word	76720
	.byte	3
	.word	234
	.byte	24,18,244,5,9,48,13
	.byte	'ccucon0',0
	.word	76532
	.byte	8,2,35,0,13
	.byte	'ccucon1',0
	.word	76532
	.byte	8,2,35,8,13
	.byte	'ccucon2',0
	.word	76532
	.byte	8,2,35,16,13
	.byte	'ccucon5',0
	.word	76532
	.byte	8,2,35,24,13
	.byte	'ccucon6',0
	.word	76532
	.byte	8,2,35,32,13
	.byte	'ccucon7',0
	.word	76532
	.byte	8,2,35,40,0,22
	.byte	'IfxScuCcu_ClockDistributionConfig',0,18,252,5,3
	.word	76822
	.byte	24,18,128,6,9,8,13
	.byte	'value',0
	.word	9579
	.byte	4,2,35,0,13
	.byte	'mask',0
	.word	9579
	.byte	4,2,35,4,0,22
	.byte	'IfxScuCcu_FlashWaitstateConfig',0,18,132,6,3
	.word	76974
	.byte	3
	.word	76720
	.byte	24,18,137,6,9,16,13
	.byte	'numOfPllDividerSteps',0
	.word	673
	.byte	1,2,35,0,13
	.byte	'pllDividerStep',0
	.word	77050
	.byte	4,2,35,4,13
	.byte	'pllInitialStep',0
	.word	76603
	.byte	8,2,35,8,0,22
	.byte	'IfxScuCcu_SysPllConfig',0,18,142,6,3
	.word	77055
	.byte	17,7,144,1,9,1,18
	.byte	'IfxCpu_CounterMode_normal',0,0,18
	.byte	'IfxCpu_CounterMode_task',0,1,0,22
	.byte	'IfxCpu_CounterMode',0,7,148,1,3
	.word	77172
	.byte	24,7,160,1,9,6,13
	.byte	'counter',0
	.word	9579
	.byte	4,2,35,0,13
	.byte	'overlfow',0
	.word	673
	.byte	1,2,35,4,0,22
	.byte	'IfxCpu_Counter',0,7,164,1,3
	.word	77261
	.byte	24,7,172,1,9,32,13
	.byte	'instruction',0
	.word	77261
	.byte	6,2,35,0,13
	.byte	'clock',0
	.word	77261
	.byte	6,2,35,6,13
	.byte	'counter1',0
	.word	77261
	.byte	6,2,35,12,13
	.byte	'counter2',0
	.word	77261
	.byte	6,2,35,18,13
	.byte	'counter3',0
	.word	77261
	.byte	6,2,35,24,0,22
	.byte	'IfxCpu_Perf',0,7,179,1,3
	.word	77327
	.byte	17,19,69,9,1,18
	.byte	'IfxSrc_Tos_cpu0',0,0,18
	.byte	'IfxSrc_Tos_cpu1',0,1,18
	.byte	'IfxSrc_Tos_dma',0,3,0,22
	.byte	'IfxSrc_Tos',0,19,74,3
	.word	77445
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L4:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,21,0,54,15,39,12,0,0,3,15,0,73,19,0,0,4,46,1,3,8,32,13
	.byte	58,15,59,15,57,15,54,15,39,12,0,0,5,5,0,3,8,58,15,59,15,57,15,73,19,0,0,6,11,0,0,0,7,36,0,3,8,11,15,62
	.byte	15,0,0,8,46,1,3,8,32,13,58,15,59,15,57,15,73,19,54,15,39,12,0,0,9,59,0,3,8,0,0,10,19,1,3,8,58,15,59,15
	.byte	57,15,11,15,0,0,11,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,12,23,1,58,15,59,15,57,15,11,15,0,0,13,13
	.byte	0,3,8,73,19,11,15,56,9,0,0,14,53,0,73,19,0,0,15,1,1,11,15,73,19,0,0,16,33,0,47,15,0,0,17,4,1,58,15,59
	.byte	15,57,15,11,15,0,0,18,40,0,3,8,28,13,0,0,19,11,1,0,0,20,46,1,49,19,0,0,21,5,0,49,19,0,0,22,22,0,3,8,58
	.byte	15,59,15,57,15,73,19,0,0,23,21,0,54,15,0,0,24,19,1,58,15,59,15,57,15,11,15,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L5:
	.word	.L7-.L6
.L6:
	.half	3
	.word	.L9-.L8
.L8:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg',0
	.byte	0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_Intrinsics.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Scu\\Std\\IfxScuWdt.h',0
	.byte	0,0,0
	.byte	'IfxScu_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Port\\Std\\IfxPort.h',0
	.byte	0,0,0
	.byte	'IfxPort_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxCpu_cfg.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Src\\Std\\IfxSrc.h',0
	.byte	0,0,0
	.byte	'IfxSrc_regdef.h',0,1,0,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Cpu/Irq/IfxCpu_Irq.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Platform_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Ifx_Types.h',0
	.byte	0,0,0
	.byte	'IfxCpu_regdef.h',0,1,0,0
	.byte	'IfxStm_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxScu_cfg.h',0
	.byte	0,0,0
	.byte	'IfxFlash_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Scu\\Std\\IfxScuCcu.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxSrc_cfg.h',0
	.byte	0,0,0,0
.L9:
.L7:
	; Module end
