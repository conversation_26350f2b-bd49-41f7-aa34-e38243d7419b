	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc33624a --dep-file=IfxScuCcu.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2\\\\xf_asr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuCcu.src ../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuCcu.c"
	.compiler_name		"ctc"
	;source	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuCcu.c'

	
$TC16X
	
	.sdecl	'.text.IfxScuCcu.IfxScuCcu_calculateSysPllDividers',code,cluster('IfxScuCcu_calculateSysPllDividers')
	.sect	'.text.IfxScuCcu.IfxScuCcu_calculateSysPllDividers'
	.align	2
	
	.global	IfxScuCcu_calculateSysPllDividers
; Function IfxScuCcu_calculateSysPllDividers
.L246:
IfxScuCcu_calculateSysPllDividers:	.type	func
	sub.a	a10,#40
.L691:
	mov.aa	a15,a4
.L692:
	mov	d8,d4
.L693:
	mov	d15,#0
.L694:
	st.w	[a10]32,d15
.L696:
	ld.w	d9,[a15]72
.L577:
	mov.u	d0,#49664
.L697:
	addih	d0,d0,#3051
.L1306:
	mov	d15,#13824
.L695:
	addih	d15,d15,#366
	st.w	[a10]28,d15
.L699:
	mov	d15,#4608
.L698:
	addih	d15,d15,#122
	st.w	[a10]24,d15
.L701:
	mov.u	d15,#33792
.L700:
	addih	d15,d15,#6103
	st.w	[a10]16,d15
.L703:
	mov	d15,#2048
.L702:
	addih	d15,d15,#12207
	st.w	[a10]12,d15
.L705:
	mov	d10,#16
.L706:
	mov	d11,#128
.L707:
	mov	d15,#0
.L704:
	st.w	[a10]8,d15
.L709:
	mov	d15,#0
.L708:
	st.w	[a10],d15
.L711:
	mov	d15,#0
.L710:
	st.w	[a10]4,d15
.L713:
	mov	d13,#0
	mov	d12,d0
.L714:
	mov	d15,#2
.L712:
	st.w	[a10]20,d15
.L716:
	mov	d15,#7168
.L715:
	addih	d15,d15,#3662
.L1307:
	jge.u	d15,d8,.L2
.L1308:
	mov	d15,#1
.L717:
	st.w	[a10]20,d15
.L2:
	j	.L3
.L4:
	div.u	e0,d9,d10
	mov	d1,#0
.L1309:
	mov	d3,#0
	ld.w	d15,[a10]24
.L718:
	mov	d2,d15
.L720:
	lt.u	d15,d0,d2
.L719:
	and.eq	d15,d1,d3
	or.lt.u	d15,d1,d3
.L1310:
	jne	d15,#0,.L5
.L1311:
	mov	d3,#0
	ld.w	d15,[a10]28
.L722:
	mov	d2,d15
.L721:
	lt.u	d15,d2,d0
.L723:
	and.eq	d15,d1,d3
	or.lt.u	d15,d3,d1
	jne	d15,#0,.L6
.L1312:
	mov	d14,#1
.L724:
	j	.L7
.L8:
	mov	d5,#0
	mov	d4,d8
.L726:
	mov	d7,#0
	mov	d6,d14
.L728:
	call	__ll_mul64
.L727:
	mov	d1,#0
	ld.w	d15,[a10]16
.L725:
	mov	d0,d15
.L730:
	lt.u	d15,d2,d0
.L729:
	and.eq	d15,d3,d1
	or.lt.u	d15,d3,d1
.L1313:
	jne	d15,#0,.L9
.L1314:
	mov	d1,#0
	ld.w	d15,[a10]12
.L732:
	mov	d0,d15
.L731:
	lt.u	d15,d0,d2
.L733:
	and.eq	d15,d3,d1
	or.lt.u	d15,d1,d3
	jne	d15,#0,.L10
.L1315:
	mov	d2,#1
.L734:
	j	.L11
.L12:
	mul	d15,d10,d14
.L1316:
	div.u	e0,d2,d15
.L1317:
	mul	d15,d0,d9
.L1318:
	sub	d15,d8
	mov	d1,#0
	mov	d0,d15
.L735:
	ne	d15,d0,#0
	or.ne	d15,d1,#0
.L1319:
	jne	d15,#0,.L13
.L1320:
	mov	e12,d1,d0
.L1321:
	st.w	[a10]8,d14
.L1322:
	st.w	[a10],d2
.L1323:
	st.w	[a10]4,d10
.L1324:
	j	.L14
.L13:
	ge.u	d15,d0,d12
	and.eq	d15,d13,d1
	or.lt.u	d15,d13,d1
.L1325:
	jne	d15,#0,.L15
.L1326:
	mov	e12,d1,d0
.L1327:
	st.w	[a10]8,d14
.L1328:
	st.w	[a10],d2
.L1329:
	st.w	[a10]4,d10
.L15:
	add	d2,#1
.L11:
	jge.u	d11,d2,.L12
.L10:
.L9:
	ld.w	d15,[a10]20
.L736:
	add	d14,d15
.L7:
	mov	d15,#128
.L737:
	jge.u	d15,d14,.L8
.L6:
.L5:
	add	d10,#-1
.L3:
	mov	d15,#1
.L738:
	jge.u	d10,d15,.L4
.L14:
	mul	d0,d8,#2
.L1330:
	mov	d15,#100
.L1331:
	div.u	e0,d0,d15
	mov	d1,#0
.L1332:
	ge.u	d15,d12,d0
	and.eq	d15,d13,d1
	or.lt.u	d15,d1,d13
.L1333:
	jne	d15,#0,.L16
.L1334:
	ld.w	d15,[a10]
.L739:
	add	d15,#-1
.L740:
	st.b	[a15]9,d15
.L1335:
	ld.w	d15,[a10]4
.L741:
	add	d15,#-1
.L742:
	st.b	[a15]8,d15
.L1336:
	ld.w	d15,[a10]8
.L743:
	add	d15,#-1
.L744:
	st.b	[a15]10,d15
.L1337:
	mov	d15,#0
.L1338:
	st.w	[a15]12,d15
.L1339:
	mov	d15,#0
.L1340:
	st.b	[a15],d15
.L1341:
	j	.L17
.L16:
	mov	d15,#1
.L745:
	st.w	[a10]32,d15
.L17:
	ld.w	d2,[a10]32
.L746:
	j	.L18
.L18:
	ret
.L570:
	
__IfxScuCcu_calculateSysPllDividers_function_end:
	.size	IfxScuCcu_calculateSysPllDividers,__IfxScuCcu_calculateSysPllDividers_function_end-IfxScuCcu_calculateSysPllDividers
.L425:
	; End of function
	
	.sdecl	'.text.IfxScuCcu.IfxScuCcu_getBaud1Frequency',code,cluster('IfxScuCcu_getBaud1Frequency')
	.sect	'.text.IfxScuCcu.IfxScuCcu_getBaud1Frequency'
	.align	2
	
	.global	IfxScuCcu_getBaud1Frequency
; Function IfxScuCcu_getBaud1Frequency
.L248:
IfxScuCcu_getBaud1Frequency:	.type	func
	movh.a	a15,#61443
	lea	a15,[a15]@los(0xf0036030)
	ld.w	d8,[a15]
.L747:
	extr.u	d15,d8,#0,#4
.L1007:
	jne	d15,#0,.L19
.L1008:
	mov	d2,#0
.L748:
	j	.L20
.L19:
	call	IfxScuCcu_getMaxFrequency
.L1009:
	extr.u	d15,d8,#0,#4
	itof	d15,d15
.L1010:
	div.f	d2,d2,d15
.L20:
	j	.L21
.L21:
	ret
.L475:
	
__IfxScuCcu_getBaud1Frequency_function_end:
	.size	IfxScuCcu_getBaud1Frequency,__IfxScuCcu_getBaud1Frequency_function_end-IfxScuCcu_getBaud1Frequency
.L315:
	; End of function
	
	.sdecl	'.text.IfxScuCcu.IfxScuCcu_getBaud2Frequency',code,cluster('IfxScuCcu_getBaud2Frequency')
	.sect	'.text.IfxScuCcu.IfxScuCcu_getBaud2Frequency'
	.align	2
	
	.global	IfxScuCcu_getBaud2Frequency
; Function IfxScuCcu_getBaud2Frequency
.L250:
IfxScuCcu_getBaud2Frequency:	.type	func
	movh.a	a15,#61443
	lea	a15,[a15]@los(0xf0036030)
	ld.w	d8,[a15]
.L749:
	extr.u	d15,d8,#4,#4
.L1015:
	jne	d15,#0,.L22
.L1016:
	mov	d2,#0
.L750:
	j	.L23
.L22:
	call	IfxScuCcu_getMaxFrequency
.L1017:
	extr.u	d15,d8,#4,#4
	itof	d15,d15
.L1018:
	div.f	d2,d2,d15
.L23:
	j	.L24
.L24:
	ret
.L479:
	
__IfxScuCcu_getBaud2Frequency_function_end:
	.size	IfxScuCcu_getBaud2Frequency,__IfxScuCcu_getBaud2Frequency_function_end-IfxScuCcu_getBaud2Frequency
.L320:
	; End of function
	
	.sdecl	'.text.IfxScuCcu.IfxScuCcu_getBbbFrequency',code,cluster('IfxScuCcu_getBbbFrequency')
	.sect	'.text.IfxScuCcu.IfxScuCcu_getBbbFrequency'
	.align	2
	
	.global	IfxScuCcu_getBbbFrequency
; Function IfxScuCcu_getBbbFrequency
.L252:
IfxScuCcu_getBbbFrequency:	.type	func
	call	IfxScuCcu_getSourceFrequency
.L751:
	movh.a	a15,#61443
	ld.bu	d15,[a15]@los(0xf0036031)
	extr.u	d0,d15,#4,#4
.L1023:
	mov	d15,#0
	jeq	d15,d0,.L25
.L1024:
	mov	d15,#1
	jeq	d15,d0,.L26
.L1025:
	mov	d15,#2
	jeq	d15,d0,.L27
.L1026:
	mov	d15,#3
	jeq	d15,d0,.L28
.L1027:
	mov	d15,#4
	jeq	d15,d0,.L29
	j	.L30
.L25:
	movh.a	a15,#61443
	ld.bu	d15,[a15]@los(0xf0036040)
	and	d15,#15
.L1028:
	jne	d15,#0,.L31
.L1029:
	mov	d2,#0
.L752:
	j	.L32
.L31:
	movh.a	a15,#61443
	ld.bu	d15,[a15]@los(0xf0036040)
	and	d15,#15
	itof	d15,d15
.L1030:
	div.f	d2,d2,d15
.L32:
	j	.L33
.L26:
	movh	d15,#16880
.L1031:
	div.f	d2,d2,d15
.L753:
	j	.L34
.L27:
	movh	d15,#17008
.L1032:
	div.f	d2,d2,d15
.L754:
	j	.L35
.L28:
	movh	d15,#17136
.L1033:
	div.f	d2,d2,d15
.L755:
	j	.L36
.L29:
	movh	d15,#17264
.L1034:
	div.f	d2,d2,d15
.L756:
	j	.L37
.L30:
	mov	d2,#0
.L757:
	j	.L38
.L38:
.L37:
.L36:
.L35:
.L34:
.L33:
	j	.L39
.L39:
	ret
.L482:
	
__IfxScuCcu_getBbbFrequency_function_end:
	.size	IfxScuCcu_getBbbFrequency,__IfxScuCcu_getBbbFrequency_function_end-IfxScuCcu_getBbbFrequency
.L325:
	; End of function
	
	.sdecl	'.text.IfxScuCcu.IfxScuCcu_getCpuFrequency',code,cluster('IfxScuCcu_getCpuFrequency')
	.sect	'.text.IfxScuCcu.IfxScuCcu_getCpuFrequency'
	.align	2
	
	.global	IfxScuCcu_getCpuFrequency
; Function IfxScuCcu_getCpuFrequency
.L254:
IfxScuCcu_getCpuFrequency:	.type	func
	mov	d8,d4
.L759:
	call	IfxScuCcu_getSriFrequency
.L758:
	mov	d0,#0
.L760:
	mov	d15,#0
	jeq	d15,d8,.L40
.L1039:
	mov	d15,#1
	jeq	d15,d8,.L41
	j	.L42
.L40:
	movh.a	a15,#61443
	ld.w	d0,[a15]@los(0xf0036080)
.L1040:
	j	.L43
.L41:
	movh.a	a15,#61443
	ld.w	d0,[a15]@los(0xf0036084)
.L1041:
	j	.L44
.L42:
	mov	d2,#0
.L1042:
	j	.L45
.L45:
.L44:
.L43:
	jeq	d0,#0,.L46
.L1043:
	utof	d15,d0
.L1044:
	movh	d0,#17024
.L761:
	div.f	d15,d15,d0
.L1045:
	mul.f	d2,d2,d15
.L46:
	j	.L47
.L47:
	ret
.L485:
	
__IfxScuCcu_getCpuFrequency_function_end:
	.size	IfxScuCcu_getCpuFrequency,__IfxScuCcu_getCpuFrequency_function_end-IfxScuCcu_getCpuFrequency
.L330:
	; End of function
	
	.sdecl	'.text.IfxScuCcu.IfxScuCcu_getFsi2Frequency',code,cluster('IfxScuCcu_getFsi2Frequency')
	.sect	'.text.IfxScuCcu.IfxScuCcu_getFsi2Frequency'
	.align	2
	
	.global	IfxScuCcu_getFsi2Frequency
; Function IfxScuCcu_getFsi2Frequency
.L256:
IfxScuCcu_getFsi2Frequency:	.type	func
	movh.a	a15,#61443
	lea	a15,[a15]@los(0xf0036030)
	ld.w	d8,[a15]
.L762:
	extr.u	d15,d8,#20,#2
.L1050:
	jne	d15,#0,.L48
.L1051:
	mov	d2,#0
.L763:
	j	.L49
.L48:
	call	IfxScuCcu_getSriFrequency
.L764:
	extr.u	d15,d8,#8,#4
.L1052:
	jeq	d15,#1,.L50
.L1053:
	extr.u	d15,d8,#8,#4
.L1054:
	jne	d15,#2,.L51
.L50:
	extr.u	d15,d8,#20,#2
	itof	d15,d15
.L1055:
	div.f	d2,d2,d15
.L51:
.L49:
	j	.L52
.L52:
	ret
.L491:
	
__IfxScuCcu_getFsi2Frequency_function_end:
	.size	IfxScuCcu_getFsi2Frequency,__IfxScuCcu_getFsi2Frequency_function_end-IfxScuCcu_getFsi2Frequency
.L335:
	; End of function
	
	.sdecl	'.text.IfxScuCcu.IfxScuCcu_getFsiFrequency',code,cluster('IfxScuCcu_getFsiFrequency')
	.sect	'.text.IfxScuCcu.IfxScuCcu_getFsiFrequency'
	.align	2
	
	.global	IfxScuCcu_getFsiFrequency
; Function IfxScuCcu_getFsiFrequency
.L258:
IfxScuCcu_getFsiFrequency:	.type	func
	movh.a	a15,#61443
	lea	a15,[a15]@los(0xf0036030)
	ld.w	d8,[a15]
.L765:
	extr.u	d15,d8,#24,#2
.L1060:
	jne	d15,#0,.L53
.L1061:
	mov	d2,#0
.L766:
	j	.L54
.L53:
	call	IfxScuCcu_getSriFrequency
.L767:
	extr.u	d15,d8,#8,#4
.L1062:
	jeq	d15,#1,.L55
.L1063:
	extr.u	d15,d8,#8,#4
.L1064:
	jne	d15,#2,.L56
.L55:
	extr.u	d15,d8,#24,#2
	itof	d15,d15
.L1065:
	div.f	d2,d2,d15
.L56:
.L54:
	j	.L57
.L57:
	ret
.L494:
	
__IfxScuCcu_getFsiFrequency_function_end:
	.size	IfxScuCcu_getFsiFrequency,__IfxScuCcu_getFsiFrequency_function_end-IfxScuCcu_getFsiFrequency
.L340:
	; End of function
	
	.sdecl	'.text.IfxScuCcu.IfxScuCcu_getMaxFrequency',code,cluster('IfxScuCcu_getMaxFrequency')
	.sect	'.text.IfxScuCcu.IfxScuCcu_getMaxFrequency'
	.align	2
	
	.global	IfxScuCcu_getMaxFrequency
; Function IfxScuCcu_getMaxFrequency
.L260:
IfxScuCcu_getMaxFrequency:	.type	func
	call	IfxScuCcu_getSourceFrequency
.L768:
	movh.a	a15,#61443
	ld.bu	d15,[a15]@los(0xf0036031)
	extr.u	d0,d15,#4,#4
.L1070:
	mov	d15,#0
	jeq	d15,d0,.L58
.L1071:
	mov	d15,#1
	jeq	d15,d0,.L59
.L1072:
	mov	d15,#2
	jeq	d15,d0,.L60
.L1073:
	mov	d15,#3
	jeq	d15,d0,.L61
.L1074:
	mov	d15,#4
	jeq	d15,d0,.L62
	j	.L63
.L58:
	movh.a	a15,#61443
	ld.bu	d15,[a15]@los(0xf003604c)
	and	d15,#15
.L1075:
	jne	d15,#0,.L64
.L1076:
	j	.L65
.L64:
	movh.a	a15,#61443
	ld.bu	d15,[a15]@los(0xf003604c)
	and	d15,#15
	itof	d15,d15
.L1077:
	div.f	d2,d2,d15
.L65:
	j	.L66
.L59:
	movh	d15,#16752
.L1078:
	div.f	d2,d2,d15
.L1079:
	j	.L67
.L60:
	movh	d15,#16880
.L1080:
	div.f	d2,d2,d15
.L1081:
	j	.L68
.L61:
	movh	d15,#17008
.L1082:
	div.f	d2,d2,d15
.L1083:
	j	.L69
.L62:
	movh	d15,#17136
.L1084:
	div.f	d2,d2,d15
.L1085:
	j	.L70
.L63:
	mov	d2,#0
.L1086:
	j	.L71
.L71:
.L70:
.L69:
.L68:
.L67:
.L66:
	j	.L72
.L72:
	ret
.L497:
	
__IfxScuCcu_getMaxFrequency_function_end:
	.size	IfxScuCcu_getMaxFrequency,__IfxScuCcu_getMaxFrequency_function_end-IfxScuCcu_getMaxFrequency
.L345:
	; End of function
	
	.sdecl	'.text.IfxScuCcu.IfxScuCcu_getModuleFrequency',code,cluster('IfxScuCcu_getModuleFrequency')
	.sect	'.text.IfxScuCcu.IfxScuCcu_getModuleFrequency'
	.align	2
	
	.global	IfxScuCcu_getModuleFrequency
; Function IfxScuCcu_getModuleFrequency
.L262:
IfxScuCcu_getModuleFrequency:	.type	func
	movh.a	a15,#61443
	lea	a15,[a15]@los(0xf0036038)
	ld.w	d8,[a15]
.L769:
	call	IfxScuCcu_getSpbFrequency
.L770:
	extr.u	d15,d8,#14,#2
.L1091:
	jne	d15,#1,.L73
.L1092:
	mov	d15,#1024
.L1093:
	extr.u	d0,d8,#0,#10
.L1094:
	sub	d15,d0
	itof	d15,d15
.L1095:
	div.f	d2,d2,d15
.L771:
	j	.L74
.L73:
	extr.u	d15,d8,#14,#2
.L1096:
	jne	d15,#2,.L75
.L1097:
	extr.u	d15,d8,#0,#10
	itof	d15,d15
.L1098:
	mul.f	d15,d2,d15
.L1099:
	movh	d0,#17536
.L1100:
	div.f	d2,d15,d0
.L772:
	j	.L76
.L75:
	mov	d2,#0
.L76:
.L74:
	j	.L77
.L77:
	ret
.L500:
	
__IfxScuCcu_getModuleFrequency_function_end:
	.size	IfxScuCcu_getModuleFrequency,__IfxScuCcu_getModuleFrequency_function_end-IfxScuCcu_getModuleFrequency
.L350:
	; End of function
	
	.sdecl	'.text.IfxScuCcu.IfxScuCcu_getOsc0Frequency',code,cluster('IfxScuCcu_getOsc0Frequency')
	.sect	'.text.IfxScuCcu.IfxScuCcu_getOsc0Frequency'
	.align	2
	
	.global	IfxScuCcu_getOsc0Frequency
; Function IfxScuCcu_getOsc0Frequency
.L264:
IfxScuCcu_getOsc0Frequency:	.type	func
	movh.a	a15,#@his(IfxScuCcu_xtalFrequency)
	lea	a15,[a15]@los(IfxScuCcu_xtalFrequency)
	ld.w	d15,[a15]
.L1105:
	utof	d2,d15
.L1106:
	j	.L78
.L78:
	ret
.L505:
	
__IfxScuCcu_getOsc0Frequency_function_end:
	.size	IfxScuCcu_getOsc0Frequency,__IfxScuCcu_getOsc0Frequency_function_end-IfxScuCcu_getOsc0Frequency
.L355:
	; End of function
	
	.sdecl	'.text.IfxScuCcu.IfxScuCcu_getOscFrequency',code,cluster('IfxScuCcu_getOscFrequency')
	.sect	'.text.IfxScuCcu.IfxScuCcu_getOscFrequency'
	.align	2
	
	.global	IfxScuCcu_getOscFrequency
; Function IfxScuCcu_getOscFrequency
.L266:
IfxScuCcu_getOscFrequency:	.type	func
	movh.a	a15,#61443
	ld.bu	d15,[a15]@los(0xf0036037)
	extr.u	d15,d15,#4,#2
.L1111:
	jne	d15,#0,.L79
.L1112:
	mov.u	d2,#48160
.L773:
	addih	d2,d2,#19646
.L1113:
	j	.L80
.L79:
	movh.a	a15,#61443
	ld.bu	d15,[a15]@los(0xf0036037)
	extr.u	d15,d15,#4,#2
.L1114:
	jne	d15,#1,.L81
.L1115:
	movh.a	a15,#@his(IfxScuCcu_xtalFrequency)
	lea	a15,[a15]@los(IfxScuCcu_xtalFrequency)
	ld.w	d15,[a15]
.L1116:
	utof	d2,d15
.L774:
	j	.L82
.L81:
	mov	d2,#0
.L82:
.L80:
	j	.L83
.L83:
	ret
.L506:
	
__IfxScuCcu_getOscFrequency_function_end:
	.size	IfxScuCcu_getOscFrequency,__IfxScuCcu_getOscFrequency_function_end-IfxScuCcu_getOscFrequency
.L360:
	; End of function
	
	.sdecl	'.text.IfxScuCcu.IfxScuCcu_getPllErayFrequency',code,cluster('IfxScuCcu_getPllErayFrequency')
	.sect	'.text.IfxScuCcu.IfxScuCcu_getPllErayFrequency'
	.align	2
	
	.global	IfxScuCcu_getPllErayFrequency
; Function IfxScuCcu_getPllErayFrequency
.L268:
IfxScuCcu_getPllErayFrequency:	.type	func
	movh.a	a15,#61443
.L775:
	lea	a15,[a15]@los(0xf0036000)
.L1121:
	call	IfxScuCcu_getOscFrequency
.L776:
	ld.bu	d15,[a15]36
	and	d15,#1
.L1122:
	jne	d15,#1,.L84
.L1123:
	ld.bu	d15,[a15]46
	and	d15,#127
.L1124:
	add	d15,#1
	itof	d15,d15
.L1125:
	div.f	d2,d2,d15
.L777:
	j	.L85
.L84:
	ld.bu	d15,[a15]36
	extr.u	d15,d15,#3,#1
.L1126:
	jne	d15,#1,.L86
.L1127:
	mov	d8,#0
	mov.u	d9,#55172
	addih	d9,d9,#16791
.L1128:
	ld.bu	d15,[a15]44
	and	d15,#127
.L1129:
	add	d4,d15,#1
	call	__d_itod
.L778:
	mov	e4,d9,d8
	mov	e6,d3,d2
	call	__d_div
	mov	e4,d3,d2
	call	__d_dtof
.L1130:
	j	.L87
.L86:
	ld.bu	d15,[a15]41
	extr.u	d15,d15,#1,#5
.L1131:
	add	d15,#1
	itof	d15,d15
.L1132:
	mul.f	d0,d2,d15
.L1133:
	ld.bu	d15,[a15]44
	and	d15,#127
.L1134:
	add	d15,#1
	itof	d15,d15
.L1135:
	div.f	d2,d0,d15
.L87:
.L85:
	j	.L88
.L88:
	ret
.L508:
	
__IfxScuCcu_getPllErayFrequency_function_end:
	.size	IfxScuCcu_getPllErayFrequency,__IfxScuCcu_getPllErayFrequency_function_end-IfxScuCcu_getPllErayFrequency
.L365:
	; End of function
	
	.sdecl	'.text.IfxScuCcu.IfxScuCcu_getPllErayVcoFrequency',code,cluster('IfxScuCcu_getPllErayVcoFrequency')
	.sect	'.text.IfxScuCcu.IfxScuCcu_getPllErayVcoFrequency'
	.align	2
	
	.global	IfxScuCcu_getPllErayVcoFrequency
; Function IfxScuCcu_getPllErayVcoFrequency
.L270:
IfxScuCcu_getPllErayVcoFrequency:	.type	func
	movh.a	a15,#61443
	ld.bu	d15,[a15]@los(0xf0036024)
	extr.u	d15,d15,#3,#1
.L1140:
	jne	d15,#1,.L89
.L1141:
	mov.u	d2,#48160
.L779:
	addih	d2,d2,#19646
.L1142:
	j	.L90
.L89:
	call	IfxScuCcu_getOscFrequency
.L1143:
	movh.a	a15,#61443
	ld.bu	d15,[a15]@los(0xf0036029)
	extr.u	d15,d15,#1,#5
.L1144:
	add	d15,#1
	itof	d15,d15
.L1145:
	mul.f	d0,d2,d15
.L1146:
	movh.a	a15,#61443
	ld.bu	d15,[a15]@los(0xf003602b)
	and	d15,#15
.L1147:
	add	d15,#1
	itof	d15,d15
.L1148:
	div.f	d2,d0,d15
.L90:
	j	.L91
.L91:
	ret
.L513:
	
__IfxScuCcu_getPllErayVcoFrequency_function_end:
	.size	IfxScuCcu_getPllErayVcoFrequency,__IfxScuCcu_getPllErayVcoFrequency_function_end-IfxScuCcu_getPllErayVcoFrequency
.L370:
	; End of function
	
	.sdecl	'.text.IfxScuCcu.IfxScuCcu_getPllFrequency',code,cluster('IfxScuCcu_getPllFrequency')
	.sect	'.text.IfxScuCcu.IfxScuCcu_getPllFrequency'
	.align	2
	
	.global	IfxScuCcu_getPllFrequency
; Function IfxScuCcu_getPllFrequency
.L272:
IfxScuCcu_getPllFrequency:	.type	func
	movh.a	a15,#61443
.L780:
	lea	a15,[a15]@los(0xf0036000)
.L1153:
	call	IfxScuCcu_getOscFrequency
.L781:
	ld.bu	d15,[a15]20
	and	d15,#1
.L1154:
	jne	d15,#1,.L92
.L1155:
	ld.bu	d15,[a15]30
	and	d15,#127
.L1156:
	add	d15,#1
	itof	d15,d15
.L1157:
	div.f	d2,d2,d15
.L782:
	j	.L93
.L92:
	ld.bu	d15,[a15]20
	extr.u	d15,d15,#3,#1
.L1158:
	jne	d15,#1,.L94
.L1159:
	mov	d8,#0
	mov.u	d9,#55172
	addih	d9,d9,#16791
.L1160:
	ld.bu	d15,[a15]28
	and	d15,#127
.L1161:
	add	d4,d15,#1
	call	__d_itod
.L783:
	mov	e4,d9,d8
	mov	e6,d3,d2
	call	__d_div
	mov	e4,d3,d2
	call	__d_dtof
.L1162:
	j	.L95
.L94:
	ld.bu	d15,[a15]25
	extr.u	d15,d15,#1,#7
.L1163:
	add	d15,#1
	itof	d15,d15
.L1164:
	mul.f	d1,d2,d15
.L1165:
	ld.bu	d15,[a15]28
	and	d15,#127
.L1166:
	add	d0,d15,#1
.L1167:
	ld.bu	d15,[a15]27
	and	d15,#15
.L1168:
	add	d15,#1
.L1169:
	mul	d0,d15
	itof	d15,d0
.L1170:
	div.f	d2,d1,d15
.L95:
.L93:
	j	.L96
.L96:
	ret
.L515:
	
__IfxScuCcu_getPllFrequency_function_end:
	.size	IfxScuCcu_getPllFrequency,__IfxScuCcu_getPllFrequency_function_end-IfxScuCcu_getPllFrequency
.L375:
	; End of function
	
	.sdecl	'.text.IfxScuCcu.IfxScuCcu_getPllVcoFrequency',code,cluster('IfxScuCcu_getPllVcoFrequency')
	.sect	'.text.IfxScuCcu.IfxScuCcu_getPllVcoFrequency'
	.align	2
	
	.global	IfxScuCcu_getPllVcoFrequency
; Function IfxScuCcu_getPllVcoFrequency
.L274:
IfxScuCcu_getPllVcoFrequency:	.type	func
	movh.a	a15,#61443
	ld.bu	d15,[a15]@los(0xf0036014)
	extr.u	d15,d15,#3,#1
.L1175:
	jne	d15,#1,.L97
.L1176:
	mov.u	d2,#48160
.L784:
	addih	d2,d2,#19646
.L1177:
	j	.L98
.L97:
	call	IfxScuCcu_getOscFrequency
.L1178:
	movh.a	a15,#61443
	ld.bu	d15,[a15]@los(0xf0036019)
	extr.u	d15,d15,#1,#7
.L1179:
	add	d15,#1
	itof	d15,d15
.L1180:
	mul.f	d0,d2,d15
.L1181:
	movh.a	a15,#61443
	ld.bu	d15,[a15]@los(0xf003601b)
	and	d15,#15
.L1182:
	add	d15,#1
	itof	d15,d15
.L1183:
	div.f	d2,d0,d15
.L98:
	j	.L99
.L99:
	ret
.L519:
	
__IfxScuCcu_getPllVcoFrequency_function_end:
	.size	IfxScuCcu_getPllVcoFrequency,__IfxScuCcu_getPllVcoFrequency_function_end-IfxScuCcu_getPllVcoFrequency
.L380:
	; End of function
	
	.sdecl	'.text.IfxScuCcu.IfxScuCcu_getSourceFrequency',code,cluster('IfxScuCcu_getSourceFrequency')
	.sect	'.text.IfxScuCcu.IfxScuCcu_getSourceFrequency'
	.align	2
	
	.global	IfxScuCcu_getSourceFrequency
; Function IfxScuCcu_getSourceFrequency
.L276:
IfxScuCcu_getSourceFrequency:	.type	func
	movh.a	a15,#61443
	ld.bu	d15,[a15]@los(0xf0036033)
	extr.u	d15,d15,#4,#2
.L1188:
	mov	d0,#0
	jeq	d15,d0,.L100
.L1189:
	mov	d0,#1
	jeq	d15,d0,.L101
	j	.L102
.L100:
	mov.u	d2,#48160
.L785:
	addih	d2,d2,#19646
.L1190:
	j	.L103
.L103:
	j	.L104
.L101:
	call	IfxScuCcu_getPllFrequency
.L786:
	j	.L105
.L102:
	mov	d2,#0
.L787:
	j	.L106
.L106:
.L105:
.L104:
	j	.L107
.L107:
	ret
.L521:
	
__IfxScuCcu_getSourceFrequency_function_end:
	.size	IfxScuCcu_getSourceFrequency,__IfxScuCcu_getSourceFrequency_function_end-IfxScuCcu_getSourceFrequency
.L385:
	; End of function
	
	.sdecl	'.text.IfxScuCcu.IfxScuCcu_getSpbFrequency',code,cluster('IfxScuCcu_getSpbFrequency')
	.sect	'.text.IfxScuCcu.IfxScuCcu_getSpbFrequency'
	.align	2
	
	.global	IfxScuCcu_getSpbFrequency
; Function IfxScuCcu_getSpbFrequency
.L278:
IfxScuCcu_getSpbFrequency:	.type	func
	call	IfxScuCcu_getSourceFrequency
.L788:
	movh.a	a15,#61443
	ld.bu	d15,[a15]@los(0xf0036031)
	extr.u	d0,d15,#4,#4
.L1195:
	mov	d15,#0
	jeq	d15,d0,.L108
.L1196:
	mov	d15,#1
	jeq	d15,d0,.L109
.L1197:
	mov	d15,#2
	jeq	d15,d0,.L110
.L1198:
	mov	d15,#3
	jeq	d15,d0,.L111
.L1199:
	mov	d15,#4
	jeq	d15,d0,.L112
	j	.L113
.L108:
	movh.a	a15,#61443
	ld.bu	d15,[a15]@los(0xf0036032)
	and	d15,#15
.L1200:
	jne	d15,#0,.L114
.L1201:
	mov	d2,#0
.L789:
	j	.L115
.L114:
	movh.a	a15,#61443
	ld.bu	d15,[a15]@los(0xf0036032)
	and	d15,#15
	itof	d15,d15
.L1202:
	div.f	d2,d2,d15
.L115:
	j	.L116
.L109:
	movh	d15,#16880
.L1203:
	div.f	d2,d2,d15
.L790:
	j	.L117
.L110:
	movh	d15,#17008
.L1204:
	div.f	d2,d2,d15
.L791:
	j	.L118
.L111:
	movh	d15,#17136
.L1205:
	div.f	d2,d2,d15
.L792:
	j	.L119
.L112:
	movh	d15,#17264
.L1206:
	div.f	d2,d2,d15
.L793:
	j	.L120
.L113:
	mov	d2,#0
.L794:
	j	.L121
.L121:
.L120:
.L119:
.L118:
.L117:
.L116:
	j	.L122
.L122:
	ret
.L525:
	
__IfxScuCcu_getSpbFrequency_function_end:
	.size	IfxScuCcu_getSpbFrequency,__IfxScuCcu_getSpbFrequency_function_end-IfxScuCcu_getSpbFrequency
.L390:
	; End of function
	
	.sdecl	'.text.IfxScuCcu.IfxScuCcu_getSriFrequency',code,cluster('IfxScuCcu_getSriFrequency')
	.sect	'.text.IfxScuCcu.IfxScuCcu_getSriFrequency'
	.align	2
	
	.global	IfxScuCcu_getSriFrequency
; Function IfxScuCcu_getSriFrequency
.L280:
IfxScuCcu_getSriFrequency:	.type	func
	call	IfxScuCcu_getSourceFrequency
.L795:
	movh.a	a15,#61443
	ld.bu	d15,[a15]@los(0xf0036031)
	extr.u	d0,d15,#4,#4
.L1211:
	mov	d15,#0
	jeq	d15,d0,.L123
.L1212:
	mov	d15,#1
	jeq	d15,d0,.L124
.L1213:
	mov	d15,#2
	jeq	d15,d0,.L125
.L1214:
	mov	d15,#3
	jeq	d15,d0,.L126
.L1215:
	mov	d15,#4
	jeq	d15,d0,.L127
	j	.L128
.L123:
	movh.a	a15,#61443
	ld.bu	d15,[a15]@los(0xf0036031)
	and	d15,#15
.L1216:
	jne	d15,#0,.L129
.L1217:
	mov	d2,#0
.L796:
	j	.L130
.L129:
	movh.a	a15,#61443
	ld.bu	d15,[a15]@los(0xf0036031)
	and	d15,#15
	itof	d15,d15
.L1218:
	div.f	d2,d2,d15
.L130:
	j	.L131
.L124:
	movh	d15,#16880
.L1219:
	div.f	d2,d2,d15
.L797:
	j	.L132
.L125:
	movh	d15,#17008
.L1220:
	div.f	d2,d2,d15
.L798:
	j	.L133
.L126:
	movh	d15,#17136
.L1221:
	div.f	d2,d2,d15
.L799:
	j	.L134
.L127:
	movh	d15,#17264
.L1222:
	div.f	d2,d2,d15
.L800:
	j	.L135
.L128:
	mov	d2,#0
.L801:
	j	.L136
.L136:
.L135:
.L134:
.L133:
.L132:
.L131:
	j	.L137
.L137:
	ret
.L528:
	
__IfxScuCcu_getSriFrequency_function_end:
	.size	IfxScuCcu_getSriFrequency,__IfxScuCcu_getSriFrequency_function_end-IfxScuCcu_getSriFrequency
.L395:
	; End of function
	
	.sdecl	'.text.IfxScuCcu.IfxScuCcu_init',code,cluster('IfxScuCcu_init')
	.sect	'.text.IfxScuCcu.IfxScuCcu_init'
	.align	2
	
	.global	IfxScuCcu_init
; Function IfxScuCcu_init
.L282:
IfxScuCcu_init:	.type	func
	mov.aa	a15,a4
.L803:
	mov	d8,#0
.L805:
	movh.a	a2,#@his(IfxScuCcu_xtalFrequency)
	lea	a2,[a2]@los(IfxScuCcu_xtalFrequency)
.L1346:
	ld.w	d15,[a15]72
.L1347:
	st.w	[a2],d15
.L1348:
	call	IfxScuWdt_getCpuWatchdogPassword
.L802:
	mov	d9,d2
.L807:
	call	IfxScuWdt_getSafetyWatchdogPassword
.L806:
	mov	d10,d2
.L809:
	mov	d4,d9
.L810:
	call	IfxScuWdt_clearCpuEndinit
.L808:
	movh.a	a2,#61443
	ld.bu	d15,[a2]@los(0xf0036130)
	extr.u	d11,d15,#3,#1
.L811:
	movh.a	a2,#61443
	ld.bu	d15,[a2]@los(0xf0036130)
.L1349:
	or	d15,#8
	movh.a	a2,#61443
	st.b	[a2]@los(0xf0036130),d15
.L1350:
	mov	d4,d9
.L812:
	call	IfxScuWdt_setCpuEndinit
.L813:
	mov	d4,d10
.L814:
	call	IfxScuWdt_clearSafetyEndinit
.L815:
	j	.L138
.L139:
.L138:
	movh.a	a2,#61443
	ld.bu	d15,[a2]@los(0xf0036033)
	extr.u	d15,d15,#7,#1
.L1351:
	jne	d15,#0,.L139
.L1352:
	movh.a	a2,#61443
	ld.bu	d15,[a2]@los(0xf0036033)
.L1353:
	insert	d15,d15,#0,#4,#2
	movh.a	a2,#61443
	st.b	[a2]@los(0xf0036033),d15
.L1354:
	movh.a	a2,#61443
	ld.bu	d15,[a2]@los(0xf0036033)
.L1355:
	or	d15,#64
	movh.a	a2,#61443
	st.b	[a2]@los(0xf0036033),d15
.L1356:
	movh.a	a2,#61443
	ld.bu	d15,[a2]@los(0xf0036018)
.L1357:
	or	d15,#16
	movh.a	a2,#61443
	st.b	[a2]@los(0xf0036018),d15
.L1358:
	j	.L140
.L141:
.L140:
	movh.a	a2,#61443
	ld.bu	d15,[a2]@los(0xf0036033)
	extr.u	d15,d15,#7,#1
.L1359:
	jne	d15,#0,.L141
.L1360:
	movh.a	a2,#61443
	ld.bu	d15,[a2]@los(0xf0036037)
.L1361:
	insert	d15,d15,#1,#4,#2
	movh.a	a2,#61443
	st.b	[a2]@los(0xf0036037),d15
.L1362:
	movh.a	a2,#61443
	ld.bu	d15,[a2]@los(0xf0036037)
.L1363:
	or	d15,#64
	movh.a	a2,#61443
	st.b	[a2]@los(0xf0036037),d15
.L1364:
	call	IfxScuCcu_isOscillatorStable
.L1365:
	or	d8,d2
.L1366:
	mov	d4,d10
.L816:
	call	IfxScuWdt_setSafetyEndinit
.L817:
	jne	d8,#0,.L142
.L619:
	mov	d4,d10
.L818:
	call	IfxScuWdt_clearSafetyEndinit
.L819:
	j	.L143
.L144:
.L143:
	movh.a	a2,#61443
	ld.bu	d15,[a2]@los(0xf0036014)
	extr.u	d15,d15,#5,#1
.L1367:
	jeq	d15,#0,.L144
.L1368:
	movh.a	a2,#61443
	ld.bu	d0,[a2]@los(0xf003601c)
.L1369:
	ld.bu	d15,[a15]10
.L1370:
	insert	d15,d0,d15,#0,#7
	movh.a	a2,#61443
	st.b	[a2]@los(0xf003601c),d15
.L1371:
	movh.a	a2,#61443
	ld.bu	d0,[a2]@los(0xf003601b)
.L1372:
	ld.bu	d15,[a15]8
.L1373:
	insert	d15,d0,d15,#0,#4
	movh.a	a2,#61443
	st.b	[a2]@los(0xf003601b),d15
.L1374:
	movh.a	a2,#61443
	ld.bu	d0,[a2]@los(0xf0036019)
.L1375:
	ld.bu	d15,[a15]9
.L1376:
	insert	d15,d0,d15,#1,#7
	movh.a	a2,#61443
	st.b	[a2]@los(0xf0036019),d15
.L1377:
	movh.a	a2,#61443
	ld.bu	d15,[a2]@los(0xf0036018)
.L1378:
	or	d15,#64
	movh.a	a2,#61443
	st.b	[a2]@los(0xf0036018),d15
.L1379:
	movh.a	a2,#61443
	ld.bu	d15,[a2]@los(0xf003601a)
.L1380:
	insert	d15,d15,#0,#0,#1
	movh.a	a2,#61443
	st.b	[a2]@los(0xf003601a),d15
.L1381:
	movh.a	a2,#61443
	ld.bu	d15,[a2]@los(0xf0036018)
.L1382:
	or	d15,#32
	movh.a	a2,#61443
	st.b	[a2]@los(0xf0036018),d15
.L1383:
	movh.a	a2,#61443
	ld.bu	d15,[a2]@los(0xf003601a)
.L1384:
	or	d15,#1
	movh.a	a2,#61443
	st.b	[a2]@los(0xf003601a),d15
.L1385:
	movh.a	a2,#61443
	ld.bu	d15,[a2]@los(0xf003601a)
.L1386:
	or	d15,#4
	movh.a	a2,#61443
	st.b	[a2]@los(0xf003601a),d15
.L1387:
	mov.u	d4,#46871
	addih	d4,d4,#14417
	call	IfxScuCcu_wait
.L1388:
	j	.L145
.L146:
.L145:
	movh.a	a2,#61443
	ld.bu	d15,[a2]@los(0xf0036014)
	extr.u	d15,d15,#2,#1
.L1389:
	jeq	d15,#0,.L146
.L1390:
	movh.a	a2,#61443
	ld.bu	d15,[a2]@los(0xf0036018)
.L1391:
	insert	d15,d15,#0,#0,#1
	movh.a	a2,#61443
	st.b	[a2]@los(0xf0036018),d15
.L1392:
	j	.L147
.L148:
.L147:
	movh.a	a2,#61443
	ld.bu	d15,[a2]@los(0xf0036033)
	extr.u	d15,d15,#7,#1
.L1393:
	jne	d15,#0,.L148
.L1394:
	movh.a	a2,#61443
	ld.bu	d15,[a2]@los(0xf0036033)
.L1395:
	insert	d15,d15,#1,#4,#2
	movh.a	a2,#61443
	st.b	[a2]@los(0xf0036033),d15
.L1396:
	j	.L149
.L150:
.L149:
	movh.a	a2,#61443
	ld.bu	d15,[a2]@los(0xf0036033)
	extr.u	d15,d15,#7,#1
.L1397:
	jne	d15,#0,.L150
.L1398:
	ld.w	d4,[a15]12
	call	IfxScuCcu_wait
.L621:
	movh.a	a2,#61443
	ld.w	d0,[a2]@los(0xf0036030)
.L1399:
	ld.w	d15,[a15]20
.L1400:
	mov	d1,#-1
	xor	d15,d1
.L820:
	and	d0,d15
.L1401:
	ld.w	d15,[a15]20
.L1402:
	ld.w	d1,[a15]16
.L1403:
	and	d15,d1
.L1404:
	or	d0,d15
.L1405:
	mov	d15,#1
.L1406:
	insert	d0,d0,d15,#28,#2
.L1407:
	mov	d15,#1
.L1408:
	insert	d0,d0,d15,#30,#1
.L1409:
	movh.a	a2,#61443
	lea	a2,[a2]@los(0xf0036030)
.L1410:
	st.w	[a2],d0
.L622:
	j	.L151
.L152:
.L151:
	movh.a	a2,#61443
	ld.bu	d15,[a2]@los(0xf0036037)
	extr.u	d15,d15,#7,#1
.L1411:
	jne	d15,#0,.L152
.L624:
	movh.a	a2,#61443
	ld.w	d0,[a2]@los(0xf0036034)
.L821:
	ld.w	d15,[a15]28
.L1412:
	mov	d1,#-1
	xor	d15,d1
.L822:
	and	d0,d15
.L1413:
	ld.w	d15,[a15]28
.L1414:
	ld.w	d1,[a15]24
.L1415:
	and	d15,d1
.L1416:
	or	d0,d15
.L1417:
	mov	d15,#1
.L1418:
	insert	d0,d0,d15,#28,#2
.L1419:
	mov	d15,#1
.L1420:
	insert	d0,d0,d15,#30,#1
.L1421:
	movh.a	a2,#61443
	lea	a2,[a2]@los(0xf0036034)
.L1422:
	st.w	[a2],d0
.L625:
	j	.L153
.L154:
.L153:
	movh.a	a2,#61443
	ld.bu	d15,[a2]@los(0xf0036043)
	extr.u	d15,d15,#7,#1
.L1423:
	jne	d15,#0,.L154
.L628:
	movh.a	a2,#61443
	ld.w	d0,[a2]@los(0xf0036040)
.L823:
	ld.w	d15,[a15]36
.L1424:
	mov	d1,#-1
	xor	d15,d1
.L824:
	and	d0,d15
.L1425:
	ld.w	d15,[a15]36
.L1426:
	ld.w	d1,[a15]32
.L1427:
	and	d15,d1
.L1428:
	or	d0,d15
.L1429:
	mov	d15,#1
.L1430:
	insert	d0,d0,d15,#30,#1
.L1431:
	movh.a	a2,#61443
	lea	a2,[a2]@los(0xf0036040)
.L1432:
	st.w	[a2],d0
.L629:
	j	.L155
.L156:
.L155:
	movh.a	a2,#61443
	ld.bu	d15,[a2]@los(0xf003604f)
	extr.u	d15,d15,#7,#1
.L1433:
	jne	d15,#0,.L156
.L632:
	movh.a	a2,#61443
	ld.w	d0,[a2]@los(0xf003604c)
.L825:
	ld.w	d15,[a15]44
.L1434:
	mov	d1,#-1
	xor	d15,d1
.L826:
	and	d0,d15
.L1435:
	ld.w	d15,[a15]44
.L1436:
	ld.w	d1,[a15]40
.L1437:
	and	d15,d1
.L1438:
	or	d0,d15
.L1439:
	mov	d15,#1
.L1440:
	insert	d0,d0,d15,#30,#1
.L1441:
	movh.a	a2,#61443
	lea	a2,[a2]@los(0xf003604c)
.L1442:
	st.w	[a2],d0
.L633:
	movh.a	a2,#61443
	ld.w	d0,[a2]@los(0xf0036080)
.L827:
	ld.w	d15,[a15]52
.L1443:
	mov	d1,#-1
	xor	d15,d1
.L828:
	and	d0,d15
.L1444:
	ld.w	d15,[a15]52
.L1445:
	ld.w	d1,[a15]48
.L1446:
	and	d15,d1
.L1447:
	or	d0,d15
.L1448:
	movh.a	a2,#61443
	lea	a2,[a2]@los(0xf0036080)
.L1449:
	st.w	[a2],d0
.L636:
	movh.a	a2,#61443
	ld.w	d0,[a2]@los(0xf0036084)
.L829:
	ld.w	d15,[a15]60
.L1450:
	mov	d1,#-1
	xor	d15,d1
.L830:
	and	d0,d15
.L1451:
	ld.w	d15,[a15]60
.L1452:
	ld.w	d1,[a15]56
.L1453:
	and	d15,d1
.L1454:
	or	d0,d15
.L1455:
	movh.a	a2,#61443
	lea	a2,[a2]@los(0xf0036084)
.L1456:
	st.w	[a2],d0
.L639:
	mov	d4,d10
.L831:
	call	IfxScuWdt_setSafetyEndinit
.L642:
	movh.a	a2,#63488
	ld.w	d15,[a2]@los(0xf8002014)
.L1457:
	ld.w	d0,[a15]68
.L1458:
	mov	d1,#-1
	xor	d0,d1
.L832:
	and	d15,d0
.L1459:
	ld.w	d0,[a15]68
.L1460:
	mov	d1,#-1
	xor	d0,d1
.L1461:
	and	d15,d0
.L1462:
	ld.w	d0,[a15]68
.L1463:
	ld.w	d1,[a15]64
.L1464:
	and	d0,d1
.L1465:
	or	d15,d0
.L1466:
	mov	d4,d9
.L833:
	call	IfxScuWdt_clearCpuEndinit
.L834:
	movh.a	a2,#63488
	lea	a2,[a2]@los(0xf8002014)
.L1467:
	st.w	[a2],d15
.L1468:
	mov	d4,d9
.L835:
	call	IfxScuWdt_setCpuEndinit
.L643:
	mov	d12,#0
.L836:
	j	.L157
.L158:
	mov	d4,d10
.L838:
	call	IfxScuWdt_clearSafetyEndinit
.L839:
	j	.L159
.L160:
.L159:
	movh.a	a2,#61443
	ld.bu	d15,[a2]@los(0xf0036014)
	extr.u	d15,d15,#5,#1
.L1469:
	jeq	d15,#0,.L160
.L1470:
	movh.a	a2,#61443
	ld.bu	d0,[a2]@los(0xf003601c)
.L1471:
	mul	d15,d12,#12
.L1472:
	ld.a	a2,[a15]4
.L1473:
	addsc.a	a2,a2,d15,#0
.L1474:
	ld.bu	d15,[a2]
.L1475:
	insert	d15,d0,d15,#0,#7
	movh.a	a2,#61443
	st.b	[a2]@los(0xf003601c),d15
.L1476:
	mov	d4,d10
.L840:
	call	IfxScuWdt_setSafetyEndinit
.L841:
	mul	d15,d12,#12
.L1477:
	ld.a	a2,[a15]4
.L1478:
	addsc.a	a2,a2,d15,#0
.L1479:
	ld.w	d15,[a2]8
.L1480:
	jeq	d15,#0,.L161
.L1481:
	mul	d15,d12,#12
.L1482:
	ld.a	a2,[a15]4
.L1483:
	addsc.a	a2,a2,d15,#0
.L1484:
	ld.a	a2,[a2]8
.L1485:
	calli	a2
.L161:
	mul	d15,d12,#12
.L1486:
	ld.a	a2,[a15]4
.L1487:
	addsc.a	a2,a2,d15,#0
.L1488:
	ld.w	d4,[a2]2
	call	IfxScuCcu_wait
.L1489:
	add	d12,#1
.L837:
	extr.u	d12,d12,#0,#8
.L157:
	ld.bu	d15,[a15]
.L1490:
	jlt.u	d12,d15,.L158
.L142:
	mov	d4,d10
.L842:
	call	IfxScuWdt_clearSafetyEndinit
.L843:
	movh.a	a15,#61443
.L804:
	ld.bu	d15,[a15]@los(0xf0036018)
.L1491:
	insert	d15,d15,#0,#6,#1
	movh.a	a15,#61443
	st.b	[a15]@los(0xf0036018),d15
.L1492:
	mov	d4,d10
.L844:
	call	IfxScuWdt_setSafetyEndinit
.L845:
	mov	d4,d9
.L846:
	call	IfxScuWdt_clearCpuEndinit
.L847:
	movh.a	a15,#61443
	ld.bu	d15,[a15]@los(0xf003612c)
.L1493:
	or	d15,#8
	movh.a	a15,#61443
	st.b	[a15]@los(0xf003612c),d15
.L1494:
	movh.a	a15,#61443
	ld.bu	d15,[a15]@los(0xf0036130)
.L1495:
	insert	d15,d15,d11,#3,#1
	movh.a	a15,#61443
	st.b	[a15]@los(0xf0036130),d15
.L1496:
	mov	d4,d9
.L848:
	call	IfxScuWdt_setCpuEndinit
.L849:
	mov	d2,d8
.L850:
	j	.L162
.L162:
	ret
.L612:
	
__IfxScuCcu_init_function_end:
	.size	IfxScuCcu_init,__IfxScuCcu_init_function_end-IfxScuCcu_init
.L430:
	; End of function
	
	.sdecl	'.text.IfxScuCcu.IfxScuCcu_initConfig',code,cluster('IfxScuCcu_initConfig')
	.sect	'.text.IfxScuCcu.IfxScuCcu_initConfig'
	.align	2
	
	.global	IfxScuCcu_initConfig
; Function IfxScuCcu_initConfig
.L284:
IfxScuCcu_initConfig:	.type	func
	movh.a	a15,#@his(IfxScuCcu_defaultClockConfig)
	lea	a15,[a15]@los(IfxScuCcu_defaultClockConfig)
	lea	a15,[a15]0
.L1501:
	lea	a2,[a4]0
	lea	a4,18
.L163:
	ld.w	d15,[a15+]
	st.w	[a2+],d15
	loop	a4,.L163
.L1502:
	ret
.L646:
	
__IfxScuCcu_initConfig_function_end:
	.size	IfxScuCcu_initConfig,__IfxScuCcu_initConfig_function_end-IfxScuCcu_initConfig
.L435:
	; End of function
	
	.sdecl	'.text.IfxScuCcu.IfxScuCcu_initErayPll',code,cluster('IfxScuCcu_initErayPll')
	.sect	'.text.IfxScuCcu.IfxScuCcu_initErayPll'
	.align	2
	
	.global	IfxScuCcu_initErayPll
; Function IfxScuCcu_initErayPll
.L286:
IfxScuCcu_initErayPll:	.type	func
	mov.aa	a15,a4
.L852:
	mov	d8,#0
.L854:
	call	IfxScuWdt_getCpuWatchdogPassword
.L851:
	mov	d9,d2
.L856:
	call	IfxScuWdt_getSafetyWatchdogPassword
.L855:
	mov	d10,d2
.L858:
	mov	d4,d9
.L859:
	call	IfxScuWdt_clearCpuEndinit
.L857:
	movh.a	a2,#61443
	ld.bu	d15,[a2]@los(0xf0036130)
	extr.u	d11,d15,#3,#1
.L860:
	movh.a	a2,#61443
	ld.bu	d15,[a2]@los(0xf0036130)
.L1507:
	or	d15,#8
	movh.a	a2,#61443
	st.b	[a2]@los(0xf0036130),d15
.L1508:
	mov	d4,d9
.L861:
	call	IfxScuWdt_setCpuEndinit
.L862:
	mov	d4,d10
.L863:
	call	IfxScuWdt_clearSafetyEndinit
.L864:
	movh.a	a2,#61443
	ld.bu	d15,[a2]@los(0xf003602a)
.L1509:
	jz.t	d15:0,.L164
.L1510:
	movh.a	a2,#61443
	ld.bu	d15,[a2]@los(0xf0036028)
	jnz.t	d15:1,.L165
.L1511:
	movh.a	a2,#61443
	ld.bu	d15,[a2]@los(0xf0036024)
	jz.t	d15:1,.L166
.L165:
.L164:
	movh.a	a2,#61443
	ld.bu	d15,[a2]@los(0xf003602a)
.L1512:
	or	d15,#1
	movh.a	a2,#61443
	st.b	[a2]@los(0xf003602a),d15
.L1513:
	movh.a	a2,#61443
	ld.bu	d15,[a2]@los(0xf0036028)
.L1514:
	insert	d15,d15,#0,#1,#1
	movh.a	a2,#61443
	st.b	[a2]@los(0xf0036028),d15
.L1515:
	j	.L167
.L168:
.L167:
	movh.a	a2,#61443
	ld.bu	d15,[a2]@los(0xf0036024)
.L1516:
	jnz.t	d15:1,.L168
.L1517:
	ld.w	d4,[a15]4
	call	IfxScuCcu_wait
.L166:
	movh.a	a2,#61443
	ld.bu	d15,[a2]@los(0xf0036024)
.L1518:
	jnz.t	d15:0,.L169
.L1519:
	j	.L170
.L171:
.L170:
	movh.a	a2,#61443
	ld.bu	d15,[a2]@los(0xf0036024)
.L1520:
	jz.t	d15:4,.L171
.L1521:
	movh.a	a2,#61443
	ld.bu	d15,[a2]@los(0xf003602e)
.L1522:
	insert	d15,d15,#3,#0,#7
	movh.a	a2,#61443
	st.b	[a2]@los(0xf003602e),d15
.L1523:
	movh.a	a2,#61443
	ld.bu	d15,[a2]@los(0xf0036028)
.L1524:
	or	d15,#1
	movh.a	a2,#61443
	st.b	[a2]@los(0xf0036028),d15
.L169:
	j	.L172
.L173:
.L172:
	movh.a	a2,#61443
	ld.bu	d15,[a2]@los(0xf0036024)
.L1525:
	jz.t	d15:5,.L173
.L1526:
	movh.a	a2,#61443
	ld.bu	d0,[a2]@los(0xf003602c)
.L1527:
	ld.bu	d15,[a15]2
.L1528:
	insert	d15,d0,d15,#0,#7
	movh.a	a2,#61443
	st.b	[a2]@los(0xf003602c),d15
.L1529:
	movh.a	a2,#61443
	ld.bu	d15,[a2]@los(0xf003602b)
.L1530:
	ld.bu	d0,[a15]
.L1531:
	insert	d15,d15,d0,#0,#4
	movh.a	a2,#61443
	st.b	[a2]@los(0xf003602b),d15
.L1532:
	movh.a	a2,#61443
	ld.bu	d0,[a2]@los(0xf0036029)
.L1533:
	ld.bu	d15,[a15]1
.L1534:
	insert	d15,d0,d15,#1,#5
	movh.a	a15,#61443
.L853:
	st.b	[a15]@los(0xf0036029),d15
.L1535:
	movh.a	a15,#61443
	ld.bu	d15,[a15]@los(0xf003602a)
.L1536:
	or	d15,#4
	movh.a	a15,#61443
	st.b	[a15]@los(0xf003602a),d15
.L1537:
	movh.a	a15,#61443
	ld.bu	d15,[a15]@los(0xf0036028)
.L1538:
	or	d15,#32
	movh.a	a15,#61443
	st.b	[a15]@los(0xf0036028),d15
.L1539:
	mov	d4,d10
.L865:
	call	IfxScuWdt_setSafetyEndinit
.L655:
	mov.u	d15,#50000
.L866:
	j	.L174
.L175:
.L174:
	add	d15,#-1
	jeq	d15,#0,.L176
.L1540:
	movh.a	a15,#61443
	ld.bu	d0,[a15]@los(0xf0036024)
.L1541:
	jz.t	d0:2,.L175
.L176:
	jne	d15,#0,.L177
.L1542:
	mov	d8,#1
.L177:
	mov	d4,d10
.L868:
	call	IfxScuWdt_clearSafetyEndinit
.L869:
	movh.a	a15,#61443
	ld.bu	d15,[a15]@los(0xf0036028)
.L867:
	insert	d15,d15,#0,#0,#1
	movh.a	a15,#61443
	st.b	[a15]@los(0xf0036028),d15
.L1543:
	j	.L178
.L179:
.L178:
	movh.a	a15,#61443
	ld.bu	d15,[a15]@los(0xf0036024)
.L1544:
	jnz.t	d15:0,.L179
.L1545:
	movh.a	a15,#61443
	ld.bu	d15,[a15]@los(0xf0036024)
.L1546:
	jnz.t	d15:2,.L180
.L1547:
	mov	d8,#1
.L180:
	mov	d4,d10
.L870:
	call	IfxScuWdt_setSafetyEndinit
.L871:
	mov	d4,d9
.L872:
	call	IfxScuWdt_clearCpuEndinit
.L873:
	movh.a	a15,#61443
	ld.bu	d15,[a15]@los(0xf003612c)
.L1548:
	or	d15,#8
	movh.a	a15,#61443
	st.b	[a15]@los(0xf003612c),d15
.L1549:
	movh.a	a15,#61443
	ld.bu	d15,[a15]@los(0xf0036130)
.L1550:
	insert	d15,d15,d11,#3,#1
	movh.a	a15,#61443
	st.b	[a15]@los(0xf0036130),d15
.L1551:
	mov	d4,d9
.L874:
	call	IfxScuWdt_setCpuEndinit
.L875:
	mov	d2,d8
.L876:
	j	.L181
.L181:
	ret
.L648:
	
__IfxScuCcu_initErayPll_function_end:
	.size	IfxScuCcu_initErayPll,__IfxScuCcu_initErayPll_function_end-IfxScuCcu_initErayPll
.L440:
	; End of function
	
	.sdecl	'.text.IfxScuCcu.IfxScuCcu_initErayPllConfig',code,cluster('IfxScuCcu_initErayPllConfig')
	.sect	'.text.IfxScuCcu.IfxScuCcu_initErayPllConfig'
	.align	2
	
	.global	IfxScuCcu_initErayPllConfig
; Function IfxScuCcu_initErayPllConfig
.L288:
IfxScuCcu_initErayPllConfig:	.type	func
	movh.a	a15,#@his(IfxScuCcu_defaultErayPllConfig)
	lea	a15,[a15]@los(IfxScuCcu_defaultErayPllConfig)
	ld.da	a2/a3,[a15]0
.L1556:
	st.da	[a4]0,a2/a3
.L1557:
	ret
.L657:
	
__IfxScuCcu_initErayPllConfig_function_end:
	.size	IfxScuCcu_initErayPllConfig,__IfxScuCcu_initErayPllConfig_function_end-IfxScuCcu_initErayPllConfig
.L445:
	; End of function
	
	.sdecl	'.text.IfxScuCcu.IfxScuCcu_isOscillatorStable',code,cluster('IfxScuCcu_isOscillatorStable')
	.sect	'.text.IfxScuCcu.IfxScuCcu_isOscillatorStable'
	.align	2
	
; Function IfxScuCcu_isOscillatorStable
.L290:
IfxScuCcu_isOscillatorStable:	.type	func
	mov	d8,#640
.L877:
	mov	d9,#0
.L878:
	call	IfxScuWdt_getCpuWatchdogPassword
.L879:
	mov	d10,d2
.L881:
	movh.a	a15,#61443
	ld.bu	d0,[a15]@los(0xf0036010)
.L1620:
	insert	d0,d0,#0,#5,#2
	movh.a	a15,#61443
	st.b	[a15]@los(0xf0036010),d0
.L1621:
	movh.a	a15,#61443
	ld.bu	d0,[a15]@los(0xf0036012)
.L1622:
	movh.a	a15,#@his(IfxScuCcu_xtalFrequency)
	lea	a15,[a15]@los(IfxScuCcu_xtalFrequency)
	ld.w	d1,[a15]
.L1623:
	mov	d15,#9632
	addih	d15,d15,#38
.L1624:
	div.u	e2,d1,d15
.L880:
	add	d15,d2,#-1
	extr.u	d15,d15,#0,#8
.L1625:
	insert	d15,d0,d15,#0,#5
	movh.a	a15,#61443
	st.b	[a15]@los(0xf0036012),d15
.L1626:
	movh.a	a15,#61443
	ld.bu	d15,[a15]@los(0xf0036010)
.L1627:
	or	d15,#4
	movh.a	a15,#61443
	st.b	[a15]@los(0xf0036010),d15
.L1628:
	j	.L182
.L183:
.L184:
	add	d8,#-1
.L1629:
	jne	d8,#0,.L185
.L1630:
	mov	d9,#1
.L1631:
	j	.L186
.L185:
.L182:
	movh.a	a15,#61443
	ld.bu	d15,[a15]@los(0xf0036010)
.L1632:
	jz.t	d15:1,.L184
.L1633:
	movh.a	a15,#61443
	ld.bu	d15,[a15]@los(0xf0036011)
.L1634:
	jz.t	d15:0,.L183
.L186:
	mov	d4,d10
.L882:
	call	IfxScuWdt_clearCpuEndinit
.L883:
	movh.a	a15,#61443
	ld.bu	d15,[a15]@los(0xf003612c)
.L1635:
	or	d15,#8
	movh.a	a15,#61443
	st.b	[a15]@los(0xf003612c),d15
.L1636:
	movh.a	a15,#61443
	ld.bu	d15,[a15]@los(0xf0036130)
.L1637:
	or	d15,#8
	movh.a	a15,#61443
	st.b	[a15]@los(0xf0036130),d15
.L1638:
	mov	d4,d10
.L884:
	call	IfxScuWdt_setCpuEndinit
.L885:
	mov	d2,d9
.L886:
	j	.L187
.L187:
	ret
.L676:
	
__IfxScuCcu_isOscillatorStable_function_end:
	.size	IfxScuCcu_isOscillatorStable,__IfxScuCcu_isOscillatorStable_function_end-IfxScuCcu_isOscillatorStable
.L460:
	; End of function
	
	.sdecl	'.text.IfxScuCcu.IfxScuCcu_setCpuFrequency',code,cluster('IfxScuCcu_setCpuFrequency')
	.sect	'.text.IfxScuCcu.IfxScuCcu_setCpuFrequency'
	.align	2
	
	.global	IfxScuCcu_setCpuFrequency
; Function IfxScuCcu_setCpuFrequency
.L292:
IfxScuCcu_setCpuFrequency:	.type	func
	mov	e8,d4,d5
.L1227:
	call	IfxScuCcu_getSriFrequency
.L887:
	mov	d10,d2
.L889:
	cmp.f	d15,d8,d10
.L890:
	and	d15,#6
	ne	d15,d15,#0
.L1228:
	jne	d15,#0,.L188
.L1229:
	movh	d0,#17024
.L891:
	mul.f	d0,d8,d0
.L892:
	div.f	d15,d0,d10
.L1230:
	ftouz	d8,d15
.L893:
	j	.L189
.L188:
	mov	d8,#0
.L189:
	call	IfxScuWdt_getSafetyWatchdogPassword
.L888:
	mov	d11,d2
.L895:
	mov	d4,d11
.L894:
	call	IfxScuWdt_clearSafetyEndinit
.L896:
	mov	d15,#0
.L897:
	jeq	d15,d9,.L190
.L898:
	mov	d15,#1
.L899:
	jeq	d15,d9,.L191
.L900:
	j	.L192
.L190:
	movh.a	a15,#61443
	st.w	[a15]@los(0xf0036080),d8
.L1231:
	j	.L193
.L191:
	movh.a	a15,#61443
	st.w	[a15]@los(0xf0036084),d8
.L1232:
	j	.L194
.L192:
	j	.L195
.L195:
.L194:
.L193:
	mov	d4,d11
.L901:
	call	IfxScuWdt_setSafetyEndinit
.L902:
	jeq	d8,#0,.L196
.L1233:
	utof	d15,d8
.L1234:
	movh	d0,#17024
.L1235:
	div.f	d15,d15,d0
.L1236:
	mul.f	d10,d10,d15
.L196:
	mov	d2,d10
.L903:
	j	.L197
.L197:
	ret
.L531:
	
__IfxScuCcu_setCpuFrequency_function_end:
	.size	IfxScuCcu_setCpuFrequency,__IfxScuCcu_setCpuFrequency_function_end-IfxScuCcu_setCpuFrequency
.L400:
	; End of function
	
	.sdecl	'.text.IfxScuCcu.IfxScuCcu_setGtmFrequency',code,cluster('IfxScuCcu_setGtmFrequency')
	.sect	'.text.IfxScuCcu.IfxScuCcu_setGtmFrequency'
	.align	2
	
	.global	IfxScuCcu_setGtmFrequency
; Function IfxScuCcu_setGtmFrequency
.L294:
IfxScuCcu_setGtmFrequency:	.type	func
	mov	d8,d4
.L905:
	movh.a	a15,#61443
	lea	a15,[a15]@los(0xf0036034)
	ld.w	d9,[a15]
.L907:
	call	IfxScuCcu_getSourceFrequency
.L904:
	mov	d10,d2
.L909:
	div.f	d15,d10,d8
	div.f	d0,d10,d8
	ftoiz	d0,d0
	itof	d0,d0
	sub.f	d4,d15,d0
	call	__f_ftod
.L908:
	mov	e4,d3,d2
	mov	d6,#0
	mov	d7,#0
	addih	d7,d7,#16352
	call	__d_fgt
	jne	d2,#0,.L198
	div.f	d15,d10,d8
	ftoiz	d15,d15
	j	.L199
.L198:
	div.f	d15,d10,d8
	ftoiz	d15,d15
	add	d15,#1
.L199:
	max.u	d8,d15,#1
.L906:
	jlt.u	d8,#7,.L200
.L1601:
	jge.u	d8,#14,.L201
.L1602:
	jz.t	d8:0,.L202
.L1603:
	add	d8,#-1
.L202:
.L201:
.L200:
	mov	d15,#14
.L1604:
	jne	d15,d8,.L203
.L1605:
	mov	d8,#12
.L203:
	call	IfxScuWdt_getSafetyWatchdogPassword
.L911:
	mov	d10,d2
.L910:
	mov	d4,d10
.L912:
	call	IfxScuWdt_clearSafetyEndinit
.L913:
	j	.L204
.L205:
.L204:
	movh.a	a15,#61443
	ld.bu	d15,[a15]@los(0xf0036037)
	extr.u	d15,d15,#7,#1
.L1606:
	jne	d15,#0,.L205
.L1607:
	extr.u	d15,d8,#0,#8
.L1608:
	insert	d9,d9,d15,#12,#4
.L1609:
	mov	d15,#1
.L1610:
	insert	d9,d9,d15,#30,#1
.L1611:
	movh.a	a15,#61443
	st.w	[a15]@los(0xf0036034),d9
.L1612:
	mov	d4,d10
.L914:
	call	IfxScuWdt_setSafetyEndinit
.L674:
	call	IfxScuCcu_getSourceFrequency
.L1613:
	movh.a	a15,#61443
	ld.bu	d15,[a15]@los(0xf0036035)
	extr.u	d15,d15,#4,#4
	itof	d15,d15
.L1614:
	div.f	d2,d2,d15
.L1615:
	j	.L206
.L206:
	j	.L207
.L207:
	ret
.L667:
	
__IfxScuCcu_setGtmFrequency_function_end:
	.size	IfxScuCcu_setGtmFrequency,__IfxScuCcu_setGtmFrequency_function_end-IfxScuCcu_setGtmFrequency
.L455:
	; End of function
	
	.sdecl	'.text.IfxScuCcu.IfxScuCcu_setPll2ErayFrequency',code,cluster('IfxScuCcu_setPll2ErayFrequency')
	.sect	'.text.IfxScuCcu.IfxScuCcu_setPll2ErayFrequency'
	.align	2
	
	.global	IfxScuCcu_setPll2ErayFrequency
; Function IfxScuCcu_setPll2ErayFrequency
.L296:
IfxScuCcu_setPll2ErayFrequency:	.type	func
	mov	d15,d4
.L916:
	call	IfxScuWdt_getSafetyWatchdogPassword
.L915:
	mov	d8,d2
.L919:
	call	IfxScuCcu_getPllErayVcoFrequency
.L918:
	div.f	d15,d2,d15
.L917:
	movh	d0,#16256
.L1241:
	sub.f	d15,d15,d0
.L1242:
	ftouz	d15,d15
.L920:
	mov	d4,d8
.L922:
	call	IfxScuWdt_clearSafetyEndinit
.L923:
	movh.a	a15,#61443
	ld.bu	d0,[a15]@los(0xf003602d)
.L1243:
	extr.u	d15,d15,#0,#8
.L921:
	insert	d15,d0,d15,#0,#4
	movh.a	a15,#61443
	st.b	[a15]@los(0xf003602d),d15
.L1244:
	mov	d4,d8
.L924:
	call	IfxScuWdt_setSafetyEndinit
.L544:
	call	IfxScuCcu_getPllErayVcoFrequency
.L1245:
	movh.a	a15,#61443
	ld.bu	d15,[a15]@los(0xf003602d)
	and	d15,#15
.L1246:
	add	d15,#1
	itof	d15,d15
.L1247:
	div.f	d2,d2,d15
.L925:
	j	.L208
.L208:
	j	.L209
.L209:
	ret
.L539:
	
__IfxScuCcu_setPll2ErayFrequency_function_end:
	.size	IfxScuCcu_setPll2ErayFrequency,__IfxScuCcu_setPll2ErayFrequency_function_end-IfxScuCcu_setPll2ErayFrequency
.L405:
	; End of function
	
	.sdecl	'.text.IfxScuCcu.IfxScuCcu_setPll2Frequency',code,cluster('IfxScuCcu_setPll2Frequency')
	.sect	'.text.IfxScuCcu.IfxScuCcu_setPll2Frequency'
	.align	2
	
	.global	IfxScuCcu_setPll2Frequency
; Function IfxScuCcu_setPll2Frequency
.L298:
IfxScuCcu_setPll2Frequency:	.type	func
	mov	d15,d4
.L927:
	call	IfxScuWdt_getSafetyWatchdogPassword
.L926:
	mov	d8,d2
.L930:
	call	IfxScuCcu_getPllVcoFrequency
.L929:
	div.f	d15,d2,d15
.L928:
	movh	d0,#16256
.L1252:
	sub.f	d15,d15,d0
.L1253:
	ftouz	d15,d15
.L931:
	mov	d4,d8
.L933:
	call	IfxScuWdt_clearSafetyEndinit
.L934:
	movh.a	a15,#61443
	ld.bu	d0,[a15]@los(0xf003601d)
.L1254:
	extr.u	d15,d15,#0,#8
.L932:
	insert	d15,d0,d15,#0,#7
	movh.a	a15,#61443
	st.b	[a15]@los(0xf003601d),d15
.L1255:
	mov	d4,d8
.L935:
	call	IfxScuWdt_setSafetyEndinit
.L552:
	call	IfxScuCcu_getPllVcoFrequency
.L1256:
	movh.a	a15,#61443
	ld.bu	d15,[a15]@los(0xf003601d)
	and	d15,#127
.L1257:
	add	d15,#1
	itof	d15,d15
.L1258:
	div.f	d2,d2,d15
.L936:
	j	.L210
.L210:
	j	.L211
.L211:
	ret
.L547:
	
__IfxScuCcu_setPll2Frequency_function_end:
	.size	IfxScuCcu_setPll2Frequency,__IfxScuCcu_setPll2Frequency_function_end-IfxScuCcu_setPll2Frequency
.L410:
	; End of function
	
	.sdecl	'.text.IfxScuCcu.IfxScuCcu_setSpbFrequency',code,cluster('IfxScuCcu_setSpbFrequency')
	.sect	'.text.IfxScuCcu.IfxScuCcu_setSpbFrequency'
	.align	2
	
	.global	IfxScuCcu_setSpbFrequency
; Function IfxScuCcu_setSpbFrequency
.L300:
IfxScuCcu_setSpbFrequency:	.type	func
	mov	d15,d4
.L938:
	call	IfxScuCcu_getSourceFrequency
.L937:
	div.f	d15,d2,d15
.L939:
	ftouz	d15,d15
.L941:
	max.u	d9,d15,#2
.L942:
	jlt.u	d9,#7,.L212
.L1263:
	jge.u	d9,#14,.L213
.L1264:
	jz.t	d9:0,.L214
.L1265:
	add	d9,#-1
.L214:
.L213:
.L212:
	mov	d15,#14
.L1266:
	jne	d15,d9,.L215
.L1267:
	mov	d9,#12
.L215:
	call	IfxScuWdt_getCpuWatchdogPassword
.L940:
	mov	d8,d2
.L944:
	call	IfxScuWdt_getSafetyWatchdogPassword
.L943:
	mov	d10,d2
.L946:
	mov	d4,d8
.L947:
	call	IfxScuWdt_clearCpuEndinit
.L945:
	movh.a	a15,#61443
	ld.w	d0,[a15]@los(0xf0036130)
.L1268:
	mov	d1,#992
.L1269:
	or	d0,d1
.L1270:
	movh.a	a15,#61443
	st.w	[a15]@los(0xf0036130),d0
.L1271:
	mov	d4,d8
.L948:
	call	IfxScuWdt_setCpuEndinit
.L949:
	mov	d4,d10
.L950:
	call	IfxScuWdt_clearSafetyEndinit
.L951:
	j	.L216
.L217:
.L216:
	movh.a	a15,#61443
	ld.bu	d0,[a15]@los(0xf0036033)
	extr.u	d15,d0,#7,#1
.L1272:
	jne	d15,#0,.L217
.L1273:
	movh.a	a15,#61443
	ld.w	d15,[a15]@los(0xf0036030)
.L952:
	extr.u	d0,d9,#0,#8
.L1274:
	insert	d15,d15,d0,#16,#4
.L1275:
	mov	d0,#1
.L1276:
	insert	d15,d15,d0,#30,#1
.L1277:
	movh.a	a15,#61443
	st.w	[a15]@los(0xf0036030),d15
.L1278:
	mov	d4,d10
.L954:
	call	IfxScuWdt_setSafetyEndinit
.L955:
	mov	d4,d8
.L956:
	call	IfxScuWdt_clearCpuEndinit
.L957:
	movh.a	a15,#61443
	ld.w	d15,[a15]@los(0xf0036130)
.L953:
	insert	d15,d15,#0,#5,#5
.L1279:
	movh.a	a15,#61443
	st.w	[a15]@los(0xf0036130),d15
.L1280:
	mov	d4,d8
.L958:
	call	IfxScuWdt_setCpuEndinit
.L959:
	j	.L218
.L219:
.L218:
	movh.a	a15,#61443
	ld.bu	d15,[a15]@los(0xf0036033)
	extr.u	d15,d15,#7,#1
.L1281:
	jne	d15,#0,.L219
.L1282:
	call	IfxScuCcu_getSpbFrequency
.L1283:
	j	.L220
.L220:
	ret
.L555:
	
__IfxScuCcu_setSpbFrequency_function_end:
	.size	IfxScuCcu_setSpbFrequency,__IfxScuCcu_setSpbFrequency_function_end-IfxScuCcu_setSpbFrequency
.L415:
	; End of function
	
	.sdecl	'.text.IfxScuCcu.IfxScuCcu_setSriFrequency',code,cluster('IfxScuCcu_setSriFrequency')
	.sect	'.text.IfxScuCcu.IfxScuCcu_setSriFrequency'
	.align	2
	
	.global	IfxScuCcu_setSriFrequency
; Function IfxScuCcu_setSriFrequency
.L302:
IfxScuCcu_setSriFrequency:	.type	func
	mov	d8,d4
.L961:
	call	IfxScuCcu_getSourceFrequency
.L960:
	mov	d9,d2
.L964:
	div.f	d15,d9,d8
	div.f	d0,d9,d8
	ftoiz	d0,d0
	itof	d0,d0
	sub.f	d4,d15,d0
	call	__f_ftod
.L963:
	mov	e4,d3,d2
	mov	d6,#0
	mov	d7,#0
	addih	d7,d7,#16352
	call	__d_fgt
	jne	d2,#0,.L221
	div.f	d15,d9,d8
	ftoiz	d15,d15
	j	.L222
.L221:
	div.f	d15,d9,d8
	ftoiz	d15,d15
	add	d15,#1
.L222:
	max.u	d8,d15,#1
.L962:
	jlt.u	d8,#7,.L223
.L1288:
	jge.u	d8,#14,.L224
.L1289:
	jz.t	d8:0,.L225
.L1290:
	add	d8,#-1
.L225:
.L224:
.L223:
	mov	d15,#14
.L1291:
	jne	d15,d8,.L226
.L1292:
	mov	d8,#12
.L226:
	call	IfxScuWdt_getSafetyWatchdogPassword
.L966:
	mov	d9,d2
.L965:
	mov	d4,d9
.L967:
	call	IfxScuWdt_clearSafetyEndinit
.L968:
	j	.L227
.L228:
.L227:
	movh.a	a15,#61443
	ld.bu	d15,[a15]@los(0xf0036033)
	extr.u	d15,d15,#7,#1
.L1293:
	jne	d15,#0,.L228
.L1294:
	movh.a	a15,#61443
	ld.w	d15,[a15]@los(0xf0036030)
.L969:
	extr.u	d0,d8,#0,#8
.L1295:
	insert	d15,d15,d0,#8,#4
.L1296:
	mov	d0,#1
.L1297:
	insert	d15,d15,d0,#30,#1
.L1298:
	movh.a	a15,#61443
	st.w	[a15]@los(0xf0036030),d15
.L1299:
	mov	d4,d9
.L970:
	call	IfxScuWdt_setSafetyEndinit
.L971:
	j	.L229
.L230:
.L229:
	movh.a	a15,#61443
	ld.bu	d15,[a15]@los(0xf0036033)
	extr.u	d15,d15,#7,#1
.L1300:
	jne	d15,#0,.L230
.L1301:
	call	IfxScuCcu_getSriFrequency
.L972:
	j	.L231
.L231:
	ret
.L562:
	
__IfxScuCcu_setSriFrequency_function_end:
	.size	IfxScuCcu_setSriFrequency,__IfxScuCcu_setSriFrequency_function_end-IfxScuCcu_setSriFrequency
.L420:
	; End of function
	
	.sdecl	'.text.IfxScuCcu.IfxScuCcu_switchToBackupClock',code,cluster('IfxScuCcu_switchToBackupClock')
	.sect	'.text.IfxScuCcu.IfxScuCcu_switchToBackupClock'
	.align	2
	
	.global	IfxScuCcu_switchToBackupClock
; Function IfxScuCcu_switchToBackupClock
.L304:
IfxScuCcu_switchToBackupClock:	.type	func
	mov.aa	a15,a4
.L974:
	movh.a	a2,#61443
	ld.bu	d15,[a2]@los(0xf0036033)
	extr.u	d15,d15,#4,#2
.L1562:
	jne	d15,#0,.L232
.L1563:
	j	.L233
.L232:
	call	IfxScuWdt_getSafetyWatchdogPassword
.L973:
	mov	d8,d2
.L977:
	call	IfxScuWdt_getCpuWatchdogPassword
.L976:
	mov	d9,d2
.L978:
	ld.bu	d10,[a15]
.L979:
	j	.L234
.L235:
	mov	d4,d8
.L981:
	call	IfxScuWdt_clearSafetyEndinit
.L982:
	j	.L236
.L237:
.L236:
	movh.a	a2,#61443
	ld.bu	d0,[a2]@los(0xf0036014)
	extr.u	d15,d0,#5,#1
.L1564:
	jeq	d15,#0,.L237
.L1565:
	movh.a	a2,#61443
	ld.bu	d0,[a2]@los(0xf003601c)
.L1566:
	add	d15,d10,#-1
.L1567:
	mul	d15,d15,#12
.L1568:
	ld.a	a2,[a15]4
.L1569:
	addsc.a	a2,a2,d15,#0
.L1570:
	ld.bu	d15,[a2]
.L1571:
	insert	d15,d0,d15,#0,#7
	movh.a	a2,#61443
	st.b	[a2]@los(0xf003601c),d15
.L1572:
	mov	d4,d8
.L983:
	call	IfxScuWdt_setSafetyEndinit
.L984:
	add	d15,d10,#-1
.L1573:
	mul	d15,d15,#12
.L1574:
	ld.a	a2,[a15]4
.L1575:
	addsc.a	a2,a2,d15,#0
.L1576:
	ld.w	d4,[a2]2
	call	IfxScuCcu_wait
.L1577:
	add	d10,#-1
.L234:
	jge	d10,#1,.L235
.L1578:
	mov	d4,d9
.L985:
	call	IfxScuWdt_clearCpuEndinit
.L986:
	movh.a	a15,#61443
.L975:
	ld.bu	d15,[a15]@los(0xf0036130)
	extr.u	d10,d15,#3,#1
.L980:
	movh.a	a15,#61443
	ld.bu	d15,[a15]@los(0xf0036130)
.L1579:
	or	d15,#8
	movh.a	a15,#61443
	st.b	[a15]@los(0xf0036130),d15
.L1580:
	mov	d4,d9
.L987:
	call	IfxScuWdt_setCpuEndinit
.L988:
	mov	d4,d8
.L989:
	call	IfxScuWdt_clearSafetyEndinit
.L990:
	j	.L238
.L239:
.L238:
	movh.a	a15,#61443
	ld.bu	d15,[a15]@los(0xf0036033)
	extr.u	d15,d15,#7,#1
.L1581:
	jne	d15,#0,.L239
.L1582:
	movh.a	a15,#61443
	ld.bu	d15,[a15]@los(0xf0036033)
.L1583:
	insert	d15,d15,#0,#4,#2
	movh.a	a15,#61443
	st.b	[a15]@los(0xf0036033),d15
.L1584:
	movh.a	a15,#61443
	ld.bu	d15,[a15]@los(0xf0036033)
.L1585:
	or	d15,#64
	movh.a	a15,#61443
	st.b	[a15]@los(0xf0036033),d15
.L1586:
	j	.L240
.L241:
.L240:
	movh.a	a15,#61443
	ld.bu	d15,[a15]@los(0xf0036033)
	extr.u	d15,d15,#7,#1
.L1587:
	jne	d15,#0,.L241
.L1588:
	movh.a	a15,#61443
	ld.bu	d15,[a15]@los(0xf0036018)
.L1589:
	or	d15,#16
	movh.a	a15,#61443
	st.b	[a15]@los(0xf0036018),d15
.L1590:
	movh.a	a15,#61443
	ld.bu	d15,[a15]@los(0xf0036018)
.L1591:
	insert	d15,d15,#0,#6,#1
	movh.a	a15,#61443
	st.b	[a15]@los(0xf0036018),d15
.L1592:
	mov	d4,d8
.L991:
	call	IfxScuWdt_setSafetyEndinit
.L992:
	mov	d4,d9
.L993:
	call	IfxScuWdt_clearCpuEndinit
.L994:
	movh.a	a15,#61443
	ld.bu	d15,[a15]@los(0xf003612c)
.L1593:
	or	d15,#8
	movh.a	a15,#61443
	st.b	[a15]@los(0xf003612c),d15
.L1594:
	movh.a	a15,#61443
	ld.bu	d15,[a15]@los(0xf0036130)
.L1595:
	insert	d15,d15,d10,#3,#1
	movh.a	a15,#61443
	st.b	[a15]@los(0xf0036130),d15
.L1596:
	mov	d4,d9
.L995:
	call	IfxScuWdt_setCpuEndinit
.L233:
	ret
.L660:
	
__IfxScuCcu_switchToBackupClock_function_end:
	.size	IfxScuCcu_switchToBackupClock,__IfxScuCcu_switchToBackupClock_function_end-IfxScuCcu_switchToBackupClock
.L450:
	; End of function
	
	.sdecl	'.text.IfxScuCcu.IfxScuCcu_wait',code,cluster('IfxScuCcu_wait')
	.sect	'.text.IfxScuCcu.IfxScuCcu_wait'
	.align	2
	
; Function IfxScuCcu_wait
.L306:
IfxScuCcu_wait:	.type	func
	mov	d8,d4
.L686:
	call	IfxScuCcu_getSourceFrequency
.L996:
	movh.a	a15,#61443
	ld.bu	d15,[a15]@los(0xf0036035)
	and	d15,#15
	itof	d15,d15
.L1643:
	div.f	d15,d2,d15
.L1644:
	j	.L242
.L242:
	mul.f	d15,d15,d8
.L1645:
	ftouz	d15,d15
.L997:
	ld.w	d1,0xf0000010
.L998:
	j	.L243
.L244:
.L243:
	ld.w	d0,0xf0000010
.L1646:
	sub	d0,d1
.L1647:
	jlt.u	d0,d15,.L244
.L1648:
	ret
.L681:
	
__IfxScuCcu_wait_function_end:
	.size	IfxScuCcu_wait,__IfxScuCcu_wait_function_end-IfxScuCcu_wait
.L465:
	; End of function
	
	.sdecl	'.rodata.IfxScuCcu.IfxScuCcu_aDefaultPllConfigSteps',data,rom,cluster('IfxScuCcu_aDefaultPllConfigSteps')
	.sect	'.rodata.IfxScuCcu.IfxScuCcu_aDefaultPllConfigSteps'
	.align	4
IfxScuCcu_aDefaultPllConfigSteps:	.type	object
	.size	IfxScuCcu_aDefaultPllConfigSteps,36
	.byte	4
	.space	1
	.word	953267991
	.space	6
	.byte	3
	.space	1
	.word	953267991
	.space	6
	.byte	2
	.space	1
	.word	953267991
	.space	6
	.sdecl	'.data.IfxScuCcu.IfxScuCcu_xtalFrequency',data,cluster('IfxScuCcu_xtalFrequency')
	.sect	'.data.IfxScuCcu.IfxScuCcu_xtalFrequency'
	.align	2
IfxScuCcu_xtalFrequency:	.type	object
	.size	IfxScuCcu_xtalFrequency,4
	.word	20000000
	.sdecl	'.rodata.IfxScuCcu.IfxScuCcu_defaultClockConfig',data,rom,cluster('IfxScuCcu_defaultClockConfig')
	.sect	'.rodata.IfxScuCcu.IfxScuCcu_defaultClockConfig'
	.global	IfxScuCcu_defaultClockConfig
	.align	4
IfxScuCcu_defaultClockConfig:	.type	object
	.size	IfxScuCcu_defaultClockConfig,76
	.byte	3
	.space	3
	.word	IfxScuCcu_aDefaultPllConfigSteps
	.byte	1,59,5
	.space	1
	.word	961656599,34734354,54464511,34873874,268435455,2,15,1
	.word	15
	.space	4
	.word	63
	.space	4
	.word	63,5333,32767,20000000
	.sdecl	'.rodata.IfxScuCcu.IfxScuCcu_defaultErayPllConfig',data,rom,cluster('IfxScuCcu_defaultErayPllConfig')
	.sect	'.rodata.IfxScuCcu.IfxScuCcu_defaultErayPllConfig'
	.global	IfxScuCcu_defaultErayPllConfig
	.align	4
IfxScuCcu_defaultErayPllConfig:	.type	object
	.size	IfxScuCcu_defaultErayPllConfig,8
	.space	1
	.byte	23,5
	.space	5
	.calls	'IfxScuCcu_calculateSysPllDividers','__ll_mul64'
	.calls	'IfxScuCcu_getPllErayFrequency','__d_itod'
	.calls	'IfxScuCcu_getPllErayFrequency','__d_div'
	.calls	'IfxScuCcu_getPllErayFrequency','__d_dtof'
	.calls	'IfxScuCcu_getPllFrequency','__d_itod'
	.calls	'IfxScuCcu_getPllFrequency','__d_div'
	.calls	'IfxScuCcu_getPllFrequency','__d_dtof'
	.calls	'IfxScuCcu_setGtmFrequency','__f_ftod'
	.calls	'IfxScuCcu_setGtmFrequency','__d_fgt'
	.calls	'IfxScuCcu_setSriFrequency','__f_ftod'
	.calls	'IfxScuCcu_setSriFrequency','__d_fgt'
	.calls	'IfxScuCcu_getBaud1Frequency','IfxScuCcu_getMaxFrequency'
	.calls	'IfxScuCcu_getBaud2Frequency','IfxScuCcu_getMaxFrequency'
	.calls	'IfxScuCcu_getBbbFrequency','IfxScuCcu_getSourceFrequency'
	.calls	'IfxScuCcu_getCpuFrequency','IfxScuCcu_getSriFrequency'
	.calls	'IfxScuCcu_getFsi2Frequency','IfxScuCcu_getSriFrequency'
	.calls	'IfxScuCcu_getFsiFrequency','IfxScuCcu_getSriFrequency'
	.calls	'IfxScuCcu_getMaxFrequency','IfxScuCcu_getSourceFrequency'
	.calls	'IfxScuCcu_getModuleFrequency','IfxScuCcu_getSpbFrequency'
	.calls	'IfxScuCcu_getPllErayFrequency','IfxScuCcu_getOscFrequency'
	.calls	'IfxScuCcu_getPllErayVcoFrequency','IfxScuCcu_getOscFrequency'
	.calls	'IfxScuCcu_getPllFrequency','IfxScuCcu_getOscFrequency'
	.calls	'IfxScuCcu_getPllVcoFrequency','IfxScuCcu_getOscFrequency'
	.calls	'IfxScuCcu_getSourceFrequency','IfxScuCcu_getPllFrequency'
	.calls	'IfxScuCcu_getSpbFrequency','IfxScuCcu_getSourceFrequency'
	.calls	'IfxScuCcu_getSriFrequency','IfxScuCcu_getSourceFrequency'
	.calls	'IfxScuCcu_init','IfxScuWdt_getCpuWatchdogPassword'
	.calls	'IfxScuCcu_init','IfxScuWdt_getSafetyWatchdogPassword'
	.calls	'IfxScuCcu_init','IfxScuWdt_clearCpuEndinit'
	.calls	'IfxScuCcu_init','IfxScuWdt_setCpuEndinit'
	.calls	'IfxScuCcu_init','IfxScuWdt_clearSafetyEndinit'
	.calls	'IfxScuCcu_init','IfxScuCcu_isOscillatorStable'
	.calls	'IfxScuCcu_init','IfxScuWdt_setSafetyEndinit'
	.calls	'IfxScuCcu_init','IfxScuCcu_wait'
	.calls	'IfxScuCcu_init','__INDIRECT__'
	.calls	'IfxScuCcu_initErayPll','IfxScuWdt_getCpuWatchdogPassword'
	.calls	'IfxScuCcu_initErayPll','IfxScuWdt_getSafetyWatchdogPassword'
	.calls	'IfxScuCcu_initErayPll','IfxScuWdt_clearCpuEndinit'
	.calls	'IfxScuCcu_initErayPll','IfxScuWdt_setCpuEndinit'
	.calls	'IfxScuCcu_initErayPll','IfxScuWdt_clearSafetyEndinit'
	.calls	'IfxScuCcu_initErayPll','IfxScuCcu_wait'
	.calls	'IfxScuCcu_initErayPll','IfxScuWdt_setSafetyEndinit'
	.calls	'IfxScuCcu_isOscillatorStable','IfxScuWdt_getCpuWatchdogPassword'
	.calls	'IfxScuCcu_isOscillatorStable','IfxScuWdt_clearCpuEndinit'
	.calls	'IfxScuCcu_isOscillatorStable','IfxScuWdt_setCpuEndinit'
	.calls	'IfxScuCcu_setCpuFrequency','IfxScuCcu_getSriFrequency'
	.calls	'IfxScuCcu_setCpuFrequency','IfxScuWdt_getSafetyWatchdogPassword'
	.calls	'IfxScuCcu_setCpuFrequency','IfxScuWdt_clearSafetyEndinit'
	.calls	'IfxScuCcu_setCpuFrequency','IfxScuWdt_setSafetyEndinit'
	.calls	'IfxScuCcu_setGtmFrequency','IfxScuCcu_getSourceFrequency'
	.calls	'IfxScuCcu_setGtmFrequency','IfxScuWdt_getSafetyWatchdogPassword'
	.calls	'IfxScuCcu_setGtmFrequency','IfxScuWdt_clearSafetyEndinit'
	.calls	'IfxScuCcu_setGtmFrequency','IfxScuWdt_setSafetyEndinit'
	.calls	'IfxScuCcu_setPll2ErayFrequency','IfxScuWdt_getSafetyWatchdogPassword'
	.calls	'IfxScuCcu_setPll2ErayFrequency','IfxScuCcu_getPllErayVcoFrequency'
	.calls	'IfxScuCcu_setPll2ErayFrequency','IfxScuWdt_clearSafetyEndinit'
	.calls	'IfxScuCcu_setPll2ErayFrequency','IfxScuWdt_setSafetyEndinit'
	.calls	'IfxScuCcu_setPll2Frequency','IfxScuWdt_getSafetyWatchdogPassword'
	.calls	'IfxScuCcu_setPll2Frequency','IfxScuCcu_getPllVcoFrequency'
	.calls	'IfxScuCcu_setPll2Frequency','IfxScuWdt_clearSafetyEndinit'
	.calls	'IfxScuCcu_setPll2Frequency','IfxScuWdt_setSafetyEndinit'
	.calls	'IfxScuCcu_setSpbFrequency','IfxScuCcu_getSourceFrequency'
	.calls	'IfxScuCcu_setSpbFrequency','IfxScuWdt_getCpuWatchdogPassword'
	.calls	'IfxScuCcu_setSpbFrequency','IfxScuWdt_getSafetyWatchdogPassword'
	.calls	'IfxScuCcu_setSpbFrequency','IfxScuWdt_clearCpuEndinit'
	.calls	'IfxScuCcu_setSpbFrequency','IfxScuWdt_setCpuEndinit'
	.calls	'IfxScuCcu_setSpbFrequency','IfxScuWdt_clearSafetyEndinit'
	.calls	'IfxScuCcu_setSpbFrequency','IfxScuWdt_setSafetyEndinit'
	.calls	'IfxScuCcu_setSpbFrequency','IfxScuCcu_getSpbFrequency'
	.calls	'IfxScuCcu_setSriFrequency','IfxScuCcu_getSourceFrequency'
	.calls	'IfxScuCcu_setSriFrequency','IfxScuWdt_getSafetyWatchdogPassword'
	.calls	'IfxScuCcu_setSriFrequency','IfxScuWdt_clearSafetyEndinit'
	.calls	'IfxScuCcu_setSriFrequency','IfxScuWdt_setSafetyEndinit'
	.calls	'IfxScuCcu_setSriFrequency','IfxScuCcu_getSriFrequency'
	.calls	'IfxScuCcu_switchToBackupClock','IfxScuWdt_getSafetyWatchdogPassword'
	.calls	'IfxScuCcu_switchToBackupClock','IfxScuWdt_getCpuWatchdogPassword'
	.calls	'IfxScuCcu_switchToBackupClock','IfxScuWdt_clearSafetyEndinit'
	.calls	'IfxScuCcu_switchToBackupClock','IfxScuWdt_setSafetyEndinit'
	.calls	'IfxScuCcu_switchToBackupClock','IfxScuCcu_wait'
	.calls	'IfxScuCcu_switchToBackupClock','IfxScuWdt_clearCpuEndinit'
	.calls	'IfxScuCcu_switchToBackupClock','IfxScuWdt_setCpuEndinit'
	.calls	'IfxScuCcu_wait','IfxScuCcu_getSourceFrequency'
	.calls	'IfxScuCcu_calculateSysPllDividers','',40
	.calls	'IfxScuCcu_getBaud1Frequency','',0
	.calls	'IfxScuCcu_getBaud2Frequency','',0
	.calls	'IfxScuCcu_getBbbFrequency','',0
	.calls	'IfxScuCcu_getCpuFrequency','',0
	.calls	'IfxScuCcu_getFsi2Frequency','',0
	.calls	'IfxScuCcu_getFsiFrequency','',0
	.calls	'IfxScuCcu_getMaxFrequency','',0
	.calls	'IfxScuCcu_getModuleFrequency','',0
	.calls	'IfxScuCcu_getOsc0Frequency','',0
	.calls	'IfxScuCcu_getOscFrequency','',0
	.calls	'IfxScuCcu_getPllErayFrequency','',0
	.calls	'IfxScuCcu_getPllErayVcoFrequency','',0
	.calls	'IfxScuCcu_getPllFrequency','',0
	.calls	'IfxScuCcu_getPllVcoFrequency','',0
	.calls	'IfxScuCcu_getSourceFrequency','',0
	.calls	'IfxScuCcu_getSpbFrequency','',0
	.calls	'IfxScuCcu_getSriFrequency','',0
	.calls	'IfxScuCcu_init','',0
	.calls	'IfxScuCcu_initConfig','',0
	.calls	'IfxScuCcu_initErayPll','',0
	.calls	'IfxScuCcu_initErayPllConfig','',0
	.calls	'IfxScuCcu_isOscillatorStable','',0
	.calls	'IfxScuCcu_setCpuFrequency','',0
	.calls	'IfxScuCcu_setGtmFrequency','',0
	.calls	'IfxScuCcu_setPll2ErayFrequency','',0
	.calls	'IfxScuCcu_setPll2Frequency','',0
	.calls	'IfxScuCcu_setSpbFrequency','',0
	.calls	'IfxScuCcu_setSriFrequency','',0
	.calls	'IfxScuCcu_switchToBackupClock','',0
	.extern	IfxScuWdt_clearCpuEndinit
	.extern	IfxScuWdt_clearSafetyEndinit
	.extern	IfxScuWdt_setCpuEndinit
	.extern	IfxScuWdt_setSafetyEndinit
	.extern	IfxScuWdt_getCpuWatchdogPassword
	.extern	IfxScuWdt_getSafetyWatchdogPassword
	.extern	__ll_mul64
	.extern	__d_itod
	.extern	__d_div
	.extern	__d_dtof
	.extern	__f_ftod
	.extern	__d_fgt
	.extern	__INDIRECT__
	.calls	'IfxScuCcu_wait','',0
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L308:
	.word	78277
	.half	3
	.word	.L309
	.byte	4
.L307:
	.byte	1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuCcu.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L310
	.byte	2,1,1,3
	.word	233
	.byte	4
	.byte	'Ifx__jump_and_link',0,3,1,61,17,1,1,5
	.byte	'fun',0,1,61,43
	.word	236
	.byte	6,0,7
	.byte	'__fract',0,4,128,1
.L474:
	.byte	7
	.byte	'float',0,4,4,8
	.byte	'Ifx__float_to_fract',0,3,1,152,2,18
	.word	281
	.byte	1,1,5
	.byte	'a',0,1,152,2,44
	.word	293
	.byte	6,0,4
	.byte	'Ifx__stopPerfCounters',0,3,1,172,2,17,1,1,6,0
.L607:
	.byte	7
	.byte	'unsigned long long int',0,8,7,9
	.byte	'void',0,3
	.word	405
	.byte	8
	.byte	'__ld64',0,3,2,135,1,19
	.word	379
	.byte	1,1,5
	.byte	'addr',0,2,135,1,32
	.word	411
	.byte	6,0,4
	.byte	'__st64',0,3,2,143,1,17,1,1,5
	.byte	'addr',0,2,143,1,30
	.word	411
	.byte	5
	.byte	'value',0,2,143,1,43
	.word	379
	.byte	6,0,7
	.byte	'unsigned int',0,4,7
.L664:
	.byte	7
	.byte	'int',0,4,5,7
	.byte	'unsigned int',0,4,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON0_Bits',0,4,241,8,16,4,11
	.byte	'ENDINIT',0,4
	.word	520
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	520
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	520
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	520
	.byte	16,0,2,35,0,0,12,4,247,14,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	536
	.byte	4,2,35,0,0
.L569:
	.byte	7
	.byte	'unsigned char',0,1,8
.L535:
	.byte	7
	.byte	'unsigned short int',0,2,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON1_Bits',0,4,250,8,16,4,11
	.byte	'reserved_0',0,1
	.word	672
	.byte	2,6,2,35,0,11
	.byte	'IR0',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	672
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	689
	.byte	16,0,2,35,2,0,12,4,255,14,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	711
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_WDTCPU_SR_Bits',0,4,137,9,16,4,11
	.byte	'AE',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	672
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	689
	.byte	16,0,2,35,2,0,12,4,135,15,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	955
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_WDTCPU',0,4,175,15,25,12,13
	.byte	'CON0',0
	.word	632
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	915
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	1146
	.byte	4,2,35,8,0,14
	.word	1186
	.byte	3
	.word	1249
	.byte	4
	.byte	'IfxScuWdt_clearCpuEndinitInline',0,3,3,181,3,17,1,1,5
	.byte	'watchdog',0,3,181,3,65
	.word	1254
	.byte	5
	.byte	'password',0,3,181,3,82
	.word	689
	.byte	6,0,4
	.byte	'IfxScuWdt_setCpuEndinitInline',0,3,3,140,4,17,1,1,5
	.byte	'watchdog',0,3,140,4,63
	.word	1254
	.byte	5
	.byte	'password',0,3,140,4,80
	.word	689
	.byte	6,0,8
	.byte	'IfxScuWdt_getCpuWatchdogPasswordInline',0,3,3,227,3,19
	.word	689
	.byte	1,1,5
	.byte	'watchdog',0,3,227,3,74
	.word	1254
	.byte	6,0
.L532:
	.byte	15,6,156,1,9,1,16
	.byte	'IfxCpu_ResourceCpu_0',0,0,16
	.byte	'IfxCpu_ResourceCpu_1',0,1,16
	.byte	'IfxCpu_ResourceCpu_none',0,2,0,8
	.byte	'IfxCpu_getCoreIndex',0,3,5,141,6,31
	.word	1484
	.byte	1,1,6,0,8
	.byte	'IfxCpu_areInterruptsEnabled',0,3,5,139,5,20
	.word	672
	.byte	1,1,6,0
.L489:
	.byte	7
	.byte	'unsigned long int',0,4,7,8
	.byte	'IfxCpu_getPerformanceCounter',0,3,5,161,6,19
	.word	1639
	.byte	1,1,5
	.byte	'address',0,5,161,6,55
	.word	689
	.byte	6,0,8
	.byte	'IfxCpu_getPerformanceCounterStickyOverflow',0,3,5,190,6,20
	.word	672
	.byte	1,1,5
	.byte	'address',0,5,190,6,70
	.word	689
	.byte	6,0,4
	.byte	'IfxCpu_updatePerformanceCounter',0,3,5,172,8,17,1,1,5
	.byte	'address',0,5,172,8,56
	.word	1639
	.byte	5
	.byte	'count',0,5,172,8,72
	.word	1639
	.byte	17,6,0,0,10
	.byte	'_Ifx_P_OUT_Bits',0,8,143,3,16,4,11
	.byte	'P0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	672
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	672
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	672
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	672
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	672
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	672
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	672
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	689
	.byte	16,0,2,35,2,0,12,8,181,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1870
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMR_Bits',0,8,169,2,16,4,11
	.byte	'PS0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	672
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	672
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	672
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	672
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	672
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	672
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	672
	.byte	1,0,2,35,1,11
	.byte	'PCL0',0,1
	.word	672
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	672
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	672
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	672
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	672
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	672
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	672
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	672
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	672
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	672
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	672
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	672
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	672
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	672
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	672
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	672
	.byte	1,0,2,35,3,0,12,8,133,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2186
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ID_Bits',0,8,110,16,4,11
	.byte	'MODREV',0,1
	.word	672
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	672
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	689
	.byte	16,0,2,35,2,0,12,8,148,4,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2757
	.byte	4,2,35,0,0,18,4
	.word	672
	.byte	19,3,0,10
	.byte	'_Ifx_P_IOCR0_Bits',0,8,140,1,16,4,11
	.byte	'reserved_0',0,1
	.word	672
	.byte	3,5,2,35,0,11
	.byte	'PC0',0,1
	.word	672
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	672
	.byte	3,5,2,35,1,11
	.byte	'PC1',0,1
	.word	672
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	672
	.byte	3,5,2,35,2,11
	.byte	'PC2',0,1
	.word	672
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	672
	.byte	3,5,2,35,3,11
	.byte	'PC3',0,1
	.word	672
	.byte	5,0,2,35,3,0,12,8,164,4,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2885
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR4_Bits',0,8,166,1,16,4,11
	.byte	'reserved_0',0,1
	.word	672
	.byte	3,5,2,35,0,11
	.byte	'PC4',0,1
	.word	672
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	672
	.byte	3,5,2,35,1,11
	.byte	'PC5',0,1
	.word	672
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	672
	.byte	3,5,2,35,2,11
	.byte	'PC6',0,1
	.word	672
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	672
	.byte	3,5,2,35,3,11
	.byte	'PC7',0,1
	.word	672
	.byte	5,0,2,35,3,0,12,8,180,4,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3100
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR8_Bits',0,8,179,1,16,4,11
	.byte	'reserved_0',0,1
	.word	672
	.byte	3,5,2,35,0,11
	.byte	'PC8',0,1
	.word	672
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	672
	.byte	3,5,2,35,1,11
	.byte	'PC9',0,1
	.word	672
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	672
	.byte	3,5,2,35,2,11
	.byte	'PC10',0,1
	.word	672
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	672
	.byte	3,5,2,35,3,11
	.byte	'PC11',0,1
	.word	672
	.byte	5,0,2,35,3,0,12,8,188,4,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3315
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR12_Bits',0,8,153,1,16,4,11
	.byte	'reserved_0',0,1
	.word	672
	.byte	3,5,2,35,0,11
	.byte	'PC12',0,1
	.word	672
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	672
	.byte	3,5,2,35,1,11
	.byte	'PC13',0,1
	.word	672
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	672
	.byte	3,5,2,35,2,11
	.byte	'PC14',0,1
	.word	672
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	672
	.byte	3,5,2,35,3,11
	.byte	'PC15',0,1
	.word	672
	.byte	5,0,2,35,3,0,12,8,172,4,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3532
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IN_Bits',0,8,118,16,4,11
	.byte	'P0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	672
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	672
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	672
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	672
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	672
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	672
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	672
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	689
	.byte	16,0,2,35,2,0,12,8,156,4,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3752
	.byte	4,2,35,0,0,18,24
	.word	672
	.byte	19,23,0,10
	.byte	'_Ifx_P_PDR0_Bits',0,8,205,3,16,4,11
	.byte	'PD0',0,1
	.word	672
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	672
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'PD2',0,1
	.word	672
	.byte	3,5,2,35,1,11
	.byte	'PL2',0,1
	.word	672
	.byte	1,4,2,35,1,11
	.byte	'PD3',0,1
	.word	672
	.byte	3,1,2,35,1,11
	.byte	'PL3',0,1
	.word	672
	.byte	1,0,2,35,1,11
	.byte	'PD4',0,1
	.word	672
	.byte	3,5,2,35,2,11
	.byte	'PL4',0,1
	.word	672
	.byte	1,4,2,35,2,11
	.byte	'PD5',0,1
	.word	672
	.byte	3,1,2,35,2,11
	.byte	'PL5',0,1
	.word	672
	.byte	1,0,2,35,2,11
	.byte	'PD6',0,1
	.word	672
	.byte	3,5,2,35,3,11
	.byte	'PL6',0,1
	.word	672
	.byte	1,4,2,35,3,11
	.byte	'PD7',0,1
	.word	672
	.byte	3,1,2,35,3,11
	.byte	'PL7',0,1
	.word	672
	.byte	1,0,2,35,3,0,12,8,205,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4075
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PDR1_Bits',0,8,226,3,16,4,11
	.byte	'PD8',0,1
	.word	672
	.byte	3,5,2,35,0,11
	.byte	'PL8',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'PD9',0,1
	.word	672
	.byte	3,1,2,35,0,11
	.byte	'PL9',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'PD10',0,1
	.word	672
	.byte	3,5,2,35,1,11
	.byte	'PL10',0,1
	.word	672
	.byte	1,4,2,35,1,11
	.byte	'PD11',0,1
	.word	672
	.byte	3,1,2,35,1,11
	.byte	'PL11',0,1
	.word	672
	.byte	1,0,2,35,1,11
	.byte	'PD12',0,1
	.word	672
	.byte	3,5,2,35,2,11
	.byte	'PL12',0,1
	.word	672
	.byte	1,4,2,35,2,11
	.byte	'PD13',0,1
	.word	672
	.byte	3,1,2,35,2,11
	.byte	'PL13',0,1
	.word	672
	.byte	1,0,2,35,2,11
	.byte	'PD14',0,1
	.word	672
	.byte	3,5,2,35,3,11
	.byte	'PL14',0,1
	.word	672
	.byte	1,4,2,35,3,11
	.byte	'PD15',0,1
	.word	672
	.byte	3,1,2,35,3,11
	.byte	'PL15',0,1
	.word	672
	.byte	1,0,2,35,3,0,12,8,213,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4379
	.byte	4,2,35,0,0,18,8
	.word	672
	.byte	19,7,0,10
	.byte	'_Ifx_P_ESR_Bits',0,8,88,16,4,11
	.byte	'EN0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	672
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	672
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	672
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	672
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	672
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	672
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	672
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	689
	.byte	16,0,2,35,2,0,12,8,140,4,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4704
	.byte	4,2,35,0,0,18,12
	.word	672
	.byte	19,11,0,10
	.byte	'_Ifx_P_PDISC_Bits',0,8,183,3,16,4,11
	.byte	'PDIS0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'PDIS2',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'PDIS3',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'PDIS4',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'PDIS5',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'PDIS6',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'PDIS7',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'PDIS8',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'PDIS9',0,1
	.word	672
	.byte	1,6,2,35,1,11
	.byte	'PDIS10',0,1
	.word	672
	.byte	1,5,2,35,1,11
	.byte	'PDIS11',0,1
	.word	672
	.byte	1,4,2,35,1,11
	.byte	'PDIS12',0,1
	.word	672
	.byte	1,3,2,35,1,11
	.byte	'PDIS13',0,1
	.word	672
	.byte	1,2,2,35,1,11
	.byte	'PDIS14',0,1
	.word	672
	.byte	1,1,2,35,1,11
	.byte	'PDIS15',0,1
	.word	672
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	689
	.byte	16,0,2,35,2,0,12,8,197,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5044
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PCSR_Bits',0,8,165,3,16,4,11
	.byte	'SEL0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'SEL1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'SEL2',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'SEL3',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'SEL4',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'SEL5',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'SEL6',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'SEL7',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	672
	.byte	2,6,2,35,1,11
	.byte	'SEL10',0,1
	.word	672
	.byte	1,5,2,35,1,11
	.byte	'SEL11',0,1
	.word	672
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	497
	.byte	19,1,2,35,0,11
	.byte	'LCK',0,1
	.word	672
	.byte	1,0,2,35,3,0,12,8,189,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5410
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR0_Bits',0,8,206,2,16,4,11
	.byte	'PS0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	497
	.byte	28,0,2,35,0,0,12,8,149,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5696
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR4_Bits',0,8,227,2,16,4,11
	.byte	'reserved_0',0,1
	.word	672
	.byte	4,4,2,35,0,11
	.byte	'PS4',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	497
	.byte	24,0,2,35,0,0,12,8,165,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5843
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR8_Bits',0,8,238,2,16,4,11
	.byte	'reserved_0',0,1
	.word	672
	.byte	8,0,2,35,0,11
	.byte	'PS8',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	672
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	672
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	672
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	497
	.byte	20,0,2,35,0,0,12,8,173,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6012
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR12_Bits',0,8,216,2,16,4,11
	.byte	'reserved_0',0,2
	.word	689
	.byte	12,4,2,35,0,11
	.byte	'PS12',0,1
	.word	672
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	672
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	672
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	672
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	689
	.byte	16,0,2,35,2,0,12,8,157,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6184
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR0_Bits',0,8,232,1,16,4,11
	.byte	'reserved_0',0,2
	.word	689
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	672
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	672
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	672
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	672
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	689
	.byte	12,0,2,35,2,0,12,8,229,4,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6359
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR4_Bits',0,8,253,1,16,4,11
	.byte	'reserved_0',0,4
	.word	497
	.byte	20,12,2,35,0,11
	.byte	'PCL4',0,1
	.word	672
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	672
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	672
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	672
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	672
	.byte	8,0,2,35,3,0,12,8,245,4,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6533
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR8_Bits',0,8,136,2,16,4,11
	.byte	'reserved_0',0,4
	.word	497
	.byte	24,8,2,35,0,11
	.byte	'PCL8',0,1
	.word	672
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	672
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	672
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	672
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	672
	.byte	4,0,2,35,3,0,12,8,253,4,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6707
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR12_Bits',0,8,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	497
	.byte	28,4,2,35,0,11
	.byte	'PCL12',0,1
	.word	672
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	672
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	672
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	672
	.byte	1,0,2,35,3,0,12,8,237,4,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6883
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR_Bits',0,8,249,2,16,4,11
	.byte	'PS0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	672
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	672
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	672
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	672
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	672
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	672
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	672
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	689
	.byte	16,0,2,35,2,0,12,8,141,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7039
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR_Bits',0,8,147,2,16,4,11
	.byte	'reserved_0',0,2
	.word	689
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	672
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	672
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	672
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	672
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	672
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	672
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	672
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	672
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	672
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	672
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	672
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	672
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	672
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	672
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	672
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	672
	.byte	1,0,2,35,3,0,12,8,221,4,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7372
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR0_Bits',0,8,192,1,16,4,11
	.byte	'reserved_0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	497
	.byte	30,0,2,35,0,0,12,8,196,4,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7720
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_Bits',0,8,200,1,16,4,11
	.byte	'reserved_0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	497
	.byte	30,0,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_P21_Bits',0,8,208,1,16,4,11
	.byte	'RDIS_CTRL',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'RX_DIS',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'TERM',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'LRXTERM',0,1
	.word	672
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	497
	.byte	24,0,2,35,0,0,12,8,204,4,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7844
	.byte	4,2,35,0,13
	.byte	'B_P21',0
	.word	7928
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR2_Bits',0,8,218,1,16,4,11
	.byte	'reserved_0',0,1
	.word	672
	.byte	8,0,2,35,0,11
	.byte	'LVDSR',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'LVDSRL',0,1
	.word	672
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	672
	.byte	2,4,2,35,1,11
	.byte	'TDIS_CTRL',0,1
	.word	672
	.byte	1,3,2,35,1,11
	.byte	'TX_DIS',0,1
	.word	672
	.byte	1,2,2,35,1,11
	.byte	'TX_PD',0,1
	.word	672
	.byte	1,1,2,35,1,11
	.byte	'TX_PWDPD',0,1
	.word	672
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	689
	.byte	16,0,2,35,2,0,12,8,213,4,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8108
	.byte	4,2,35,0,0,18,76
	.word	672
	.byte	19,75,0,10
	.byte	'_Ifx_P_ACCEN1_Bits',0,8,82,16,4,11
	.byte	'reserved_0',0,4
	.word	497
	.byte	32,0,2,35,0,0,12,8,132,4,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8361
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ACCEN0_Bits',0,8,45,16,4,11
	.byte	'EN0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	672
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	672
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	672
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	672
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	672
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	672
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	672
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	672
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	672
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	672
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	672
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	672
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	672
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	672
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	672
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	672
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	672
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	672
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	672
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	672
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	672
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	672
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	672
	.byte	1,0,2,35,3,0,12,8,252,3,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8448
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P',0,8,229,5,25,128,2,13
	.byte	'OUT',0
	.word	2146
	.byte	4,2,35,0,13
	.byte	'OMR',0
	.word	2717
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	2836
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	2876
	.byte	4,2,35,12,13
	.byte	'IOCR0',0
	.word	3060
	.byte	4,2,35,16,13
	.byte	'IOCR4',0
	.word	3275
	.byte	4,2,35,20,13
	.byte	'IOCR8',0
	.word	3492
	.byte	4,2,35,24,13
	.byte	'IOCR12',0
	.word	3712
	.byte	4,2,35,28,13
	.byte	'reserved_20',0
	.word	2876
	.byte	4,2,35,32,13
	.byte	'IN',0
	.word	4026
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	4066
	.byte	24,2,35,40,13
	.byte	'PDR0',0
	.word	4339
	.byte	4,2,35,64,13
	.byte	'PDR1',0
	.word	4655
	.byte	4,2,35,68,13
	.byte	'reserved_48',0
	.word	4695
	.byte	8,2,35,72,13
	.byte	'ESR',0
	.word	4995
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	5035
	.byte	12,2,35,84,13
	.byte	'PDISC',0
	.word	5370
	.byte	4,2,35,96,13
	.byte	'PCSR',0
	.word	5656
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	4695
	.byte	8,2,35,104,13
	.byte	'OMSR0',0
	.word	5803
	.byte	4,2,35,112,13
	.byte	'OMSR4',0
	.word	5972
	.byte	4,2,35,116,13
	.byte	'OMSR8',0
	.word	6144
	.byte	4,2,35,120,13
	.byte	'OMSR12',0
	.word	6319
	.byte	4,2,35,124,13
	.byte	'OMCR0',0
	.word	6493
	.byte	4,3,35,128,1,13
	.byte	'OMCR4',0
	.word	6667
	.byte	4,3,35,132,1,13
	.byte	'OMCR8',0
	.word	6843
	.byte	4,3,35,136,1,13
	.byte	'OMCR12',0
	.word	6999
	.byte	4,3,35,140,1,13
	.byte	'OMSR',0
	.word	7332
	.byte	4,3,35,144,1,13
	.byte	'OMCR',0
	.word	7680
	.byte	4,3,35,148,1,13
	.byte	'reserved_98',0
	.word	4695
	.byte	8,3,35,152,1,13
	.byte	'LPCR0',0
	.word	7804
	.byte	4,3,35,160,1,13
	.byte	'LPCR1',0
	.word	8053
	.byte	4,3,35,164,1,13
	.byte	'LPCR2',0
	.word	8312
	.byte	4,3,35,168,1,13
	.byte	'reserved_A4',0
	.word	8352
	.byte	76,3,35,172,1,13
	.byte	'ACCEN1',0
	.word	8408
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	8975
	.byte	4,3,35,252,1,0,14
	.word	9015
	.byte	3
	.word	9618
	.byte	15,7,172,1,9,4,16
	.byte	'IfxPort_State_notChanged',0,0,16
	.byte	'IfxPort_State_high',0,1,16
	.byte	'IfxPort_State_low',0,128,128,4,16
	.byte	'IfxPort_State_toggled',0,129,128,4,0,4
	.byte	'IfxPort_setPinState',0,3,7,208,4,17,1,1,5
	.byte	'port',0,7,208,4,44
	.word	9623
	.byte	5
	.byte	'pinIndex',0,7,208,4,56
	.word	672
	.byte	5
	.byte	'action',0,7,208,4,80
	.word	9628
	.byte	6,0
.L523:
	.byte	8
	.byte	'IfxScuCcu_getEvrFrequency',0,3,9,189,8,20
	.word	293
	.byte	1,1
.L524:
	.byte	6,0
.L543:
	.byte	8
	.byte	'IfxScuCcu_getPll2ErayFrequency',0,3,9,201,8,20
	.word	293
	.byte	1,1
.L545:
	.byte	6,0
.L551:
	.byte	8
	.byte	'IfxScuCcu_getPll2Frequency',0,3,9,211,8,20
	.word	293
	.byte	1,1
.L553:
	.byte	6,0
.L685:
	.byte	8
	.byte	'IfxScuCcu_getStmFrequency',0,3,9,226,8,20
	.word	293
	.byte	1,1
.L687:
	.byte	6,0
.L673:
	.byte	8
	.byte	'IfxScuCcu_getGtmFrequency',0,3,9,195,8,20
	.word	293
	.byte	1,1
.L675:
	.byte	6,0,20
	.byte	'__maxu',0
	.word	497
	.byte	1,1,1,1,21
	.word	497
	.byte	21
	.word	497
	.byte	0,22
	.word	241
	.byte	23
	.word	267
	.byte	6,0,22
	.word	302
	.byte	23
	.word	334
	.byte	6,0,22
	.word	347
	.byte	6,0,22
	.word	416
	.byte	23
	.word	435
	.byte	6,0,22
	.word	451
	.byte	23
	.word	466
	.byte	23
	.word	480
	.byte	6,0,22
	.word	1259
	.byte	23
	.word	1299
	.byte	23
	.word	1317
	.byte	6,0,22
	.word	1337
	.byte	23
	.word	1375
	.byte	23
	.word	1393
	.byte	6,0,24
	.byte	'IfxScuWdt_clearCpuEndinit',0,3,217,1,17,1,1,1,1,5
	.byte	'password',0,3,217,1,50
	.word	689
	.byte	0,24
	.byte	'IfxScuWdt_clearSafetyEndinit',0,3,229,1,17,1,1,1,1,5
	.byte	'password',0,3,229,1,53
	.word	689
	.byte	0,24
	.byte	'IfxScuWdt_setCpuEndinit',0,3,239,1,17,1,1,1,1,5
	.byte	'password',0,3,239,1,48
	.word	689
	.byte	0,24
	.byte	'IfxScuWdt_setSafetyEndinit',0,3,249,1,17,1,1,1,1,5
	.byte	'password',0,3,249,1,51
	.word	689
	.byte	0,22
	.word	1413
	.byte	23
	.word	1464
	.byte	6,0,25
	.byte	'IfxScuWdt_getCpuWatchdogPassword',0,3,129,3,19
	.word	689
	.byte	1,1,1,1,25
	.byte	'IfxScuWdt_getSafetyWatchdogPassword',0,3,143,3,19
	.word	689
	.byte	1,1,1,1,22
	.word	1563
	.byte	6,0,22
	.word	1597
	.byte	6,0,22
	.word	1660
	.byte	23
	.word	1701
	.byte	6,0,22
	.word	1720
	.byte	23
	.word	1775
	.byte	6,0,22
	.word	1794
	.byte	23
	.word	1834
	.byte	23
	.word	1851
	.byte	17,6,0,0,22
	.word	9731
	.byte	23
	.word	9759
	.byte	23
	.word	9773
	.byte	23
	.word	9791
	.byte	6,0,22
	.word	9809
	.byte	6,0,22
	.word	9849
	.byte	6,0,22
	.word	9894
	.byte	6,0,22
	.word	9935
	.byte	6,0,10
	.byte	'_Ifx_SCU_CCUCON0_Bits',0,4,97,16,4,11
	.byte	'BAUD1DIV',0,1
	.word	672
	.byte	4,4,2,35,0,11
	.byte	'BAUD2DIV',0,1
	.word	672
	.byte	4,0,2,35,0,11
	.byte	'SRIDIV',0,1
	.word	672
	.byte	4,4,2,35,1,11
	.byte	'LPDIV',0,1
	.word	672
	.byte	4,0,2,35,1,11
	.byte	'SPBDIV',0,1
	.word	672
	.byte	4,4,2,35,2,11
	.byte	'FSI2DIV',0,1
	.word	672
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	672
	.byte	2,0,2,35,2,11
	.byte	'FSIDIV',0,1
	.word	672
	.byte	2,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	672
	.byte	2,4,2,35,3,11
	.byte	'CLKSEL',0,1
	.word	672
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	672
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	672
	.byte	1,0,2,35,3,0
.L477:
	.byte	12,4,223,9,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10568
	.byte	4,2,35,0,0
.L486:
	.byte	26
	.word	1484
	.byte	10
	.byte	'_Ifx_SCU_FDR_Bits',0,4,166,4,16,4,11
	.byte	'STEP',0,2
	.word	689
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	672
	.byte	4,2,2,35,1,11
	.byte	'DM',0,1
	.word	672
	.byte	2,0,2,35,1,11
	.byte	'RESULT',0,2
	.word	689
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	672
	.byte	5,1,2,35,3,11
	.byte	'DISCLK',0,1
	.word	672
	.byte	1,0,2,35,3,0
.L503:
	.byte	12,4,151,12,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10864
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_ID_Bits',0,4,200,4,16,4,11
	.byte	'MODREV',0,1
	.word	672
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	672
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	689
	.byte	16,0,2,35,2,0,12,4,167,12,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11041
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_OSCCON_Bits',0,4,188,5,16,4,11
	.byte	'reserved_0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'PLLLV',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'OSCRES',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'GAINSEL',0,1
	.word	672
	.byte	2,3,2,35,0,11
	.byte	'MODE',0,1
	.word	672
	.byte	2,1,2,35,0,11
	.byte	'SHBY',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'PLLHV',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	672
	.byte	1,6,2,35,1,11
	.byte	'X1D',0,1
	.word	672
	.byte	1,5,2,35,1,11
	.byte	'X1DEN',0,1
	.word	672
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	672
	.byte	4,0,2,35,1,11
	.byte	'OSCVAL',0,1
	.word	672
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	672
	.byte	2,1,2,35,2,11
	.byte	'APREN',0,1
	.word	672
	.byte	1,0,2,35,2,11
	.byte	'CAP0EN',0,1
	.word	672
	.byte	1,7,2,35,3,11
	.byte	'CAP1EN',0,1
	.word	672
	.byte	1,6,2,35,3,11
	.byte	'CAP2EN',0,1
	.word	672
	.byte	1,5,2,35,3,11
	.byte	'CAP3EN',0,1
	.word	672
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	672
	.byte	4,0,2,35,3,0,12,4,255,12,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11163
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_PLLSTAT_Bits',0,4,230,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'MODRUN',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	497
	.byte	24,0,2,35,0,0,12,4,231,13,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11586
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_PLLCON0_Bits',0,4,149,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'MODEN',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	689
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	672
	.byte	7,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	672
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	672
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	672
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	672
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	672
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	672
	.byte	4,0,2,35,3,0,12,4,183,13,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11829
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_PLLCON1_Bits',0,4,169,6,16,4,11
	.byte	'K2DIV',0,1
	.word	672
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	672
	.byte	7,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	672
	.byte	1,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	672
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	689
	.byte	9,0,2,35,2,0,12,4,191,13,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12195
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_PLLCON2_Bits',0,4,180,6,16,4,11
	.byte	'MODCFG',0,2
	.word	689
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	689
	.byte	16,0,2,35,2,0,12,4,199,13,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12383
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_PLLERAYSTAT_Bits',0,4,218,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'PWDSTAT',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	497
	.byte	26,0,2,35,0,0,12,4,223,13,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12493
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_PLLERAYCON0_Bits',0,4,187,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	672
	.byte	2,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	689
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	672
	.byte	5,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	672
	.byte	2,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	672
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	672
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	672
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	672
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	672
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	672
	.byte	4,0,2,35,3,0,12,4,207,13,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12697
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_PLLERAYCON1_Bits',0,4,207,6,16,4,11
	.byte	'K2DIV',0,1
	.word	672
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	672
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	672
	.byte	4,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	672
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	689
	.byte	9,0,2,35,2,0,12,4,215,13,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13073
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_CCUCON1_Bits',0,4,114,16,4,11
	.byte	'CANDIV',0,1
	.word	672
	.byte	4,4,2,35,0,11
	.byte	'ERAYDIV',0,1
	.word	672
	.byte	4,0,2,35,0,11
	.byte	'STMDIV',0,1
	.word	672
	.byte	4,4,2,35,1,11
	.byte	'GTMDIV',0,1
	.word	672
	.byte	4,0,2,35,1,11
	.byte	'ETHDIV',0,1
	.word	672
	.byte	4,4,2,35,2,11
	.byte	'ASCLINFDIV',0,1
	.word	672
	.byte	4,0,2,35,2,11
	.byte	'ASCLINSDIV',0,1
	.word	672
	.byte	4,4,2,35,3,11
	.byte	'INSEL',0,1
	.word	672
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	672
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	672
	.byte	1,0,2,35,3,0
.L626:
	.byte	12,4,231,9,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13265
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_EXTCON_Bits',0,4,152,4,16,4,11
	.byte	'EN0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'SEL0',0,1
	.word	672
	.byte	4,2,2,35,0,11
	.byte	'reserved_6',0,2
	.word	689
	.byte	10,0,2,35,0,11
	.byte	'EN1',0,1
	.word	672
	.byte	1,7,2,35,2,11
	.byte	'NSEL',0,1
	.word	672
	.byte	1,6,2,35,2,11
	.byte	'SEL1',0,1
	.word	672
	.byte	4,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	672
	.byte	2,0,2,35,2,11
	.byte	'DIV1',0,1
	.word	672
	.byte	8,0,2,35,3,0,12,4,143,12,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13514
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_CCUCON2_Bits',0,4,129,1,16,4,11
	.byte	'BBBDIV',0,1
	.word	672
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	497
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	672
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	672
	.byte	1,0,2,35,3,0
.L630:
	.byte	12,4,239,9,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13743
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_CCUCON3_Bits',0,4,138,1,16,4,11
	.byte	'PLLDIV',0,1
	.word	672
	.byte	6,2,2,35,0,11
	.byte	'PLLSEL',0,1
	.word	672
	.byte	2,0,2,35,0,11
	.byte	'PLLERAYDIV',0,1
	.word	672
	.byte	6,2,2,35,1,11
	.byte	'PLLERAYSEL',0,1
	.word	672
	.byte	2,0,2,35,1,11
	.byte	'SRIDIV',0,1
	.word	672
	.byte	6,2,2,35,2,11
	.byte	'SRISEL',0,1
	.word	672
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	672
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	672
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	672
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	672
	.byte	1,0,2,35,3,0,12,4,247,9,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13881
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_CCUCON4_Bits',0,4,153,1,16,4,11
	.byte	'SPBDIV',0,1
	.word	672
	.byte	6,2,2,35,0,11
	.byte	'SPBSEL',0,1
	.word	672
	.byte	2,0,2,35,0,11
	.byte	'GTMDIV',0,1
	.word	672
	.byte	6,2,2,35,1,11
	.byte	'GTMSEL',0,1
	.word	672
	.byte	2,0,2,35,1,11
	.byte	'STMDIV',0,1
	.word	672
	.byte	6,2,2,35,2,11
	.byte	'STMSEL',0,1
	.word	672
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	672
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	672
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	672
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	672
	.byte	1,0,2,35,3,0,12,4,255,9,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14134
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_CCUCON5_Bits',0,4,168,1,16,4,11
	.byte	'MAXDIV',0,1
	.word	672
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	497
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	672
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	672
	.byte	1,0,2,35,3,0
.L634:
	.byte	12,4,135,10,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14379
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_RSTSTAT_Bits',0,4,132,8,16,4,11
	.byte	'ESR0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'ESR1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'SMU',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'SW',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'STM0',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'STM1',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'STM2',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	672
	.byte	8,0,2,35,1,11
	.byte	'PORST',0,1
	.word	672
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	672
	.byte	1,6,2,35,2,11
	.byte	'CB0',0,1
	.word	672
	.byte	1,5,2,35,2,11
	.byte	'CB1',0,1
	.word	672
	.byte	1,4,2,35,2,11
	.byte	'CB3',0,1
	.word	672
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	672
	.byte	2,1,2,35,2,11
	.byte	'EVR13',0,1
	.word	672
	.byte	1,0,2,35,2,11
	.byte	'EVR33',0,1
	.word	672
	.byte	1,7,2,35,3,11
	.byte	'SWD',0,1
	.word	672
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	672
	.byte	2,4,2,35,3,11
	.byte	'STBYR',0,1
	.word	672
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	672
	.byte	3,0,2,35,3,0,12,4,175,14,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14517
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_RSTCON_Bits',0,4,246,7,16,4,11
	.byte	'ESR0',0,1
	.word	672
	.byte	2,6,2,35,0,11
	.byte	'ESR1',0,1
	.word	672
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	672
	.byte	2,2,2,35,0,11
	.byte	'SMU',0,1
	.word	672
	.byte	2,0,2,35,0,11
	.byte	'SW',0,1
	.word	672
	.byte	2,6,2,35,1,11
	.byte	'STM0',0,1
	.word	672
	.byte	2,4,2,35,1,11
	.byte	'STM1',0,1
	.word	672
	.byte	2,2,2,35,1,11
	.byte	'STM2',0,1
	.word	672
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	689
	.byte	16,0,2,35,2,0,12,4,159,14,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14959
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_ARSTDIS_Bits',0,4,88,16,4,11
	.byte	'STM0DIS',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'STM1DIS',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'STM2DIS',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	497
	.byte	29,0,2,35,0,0,12,4,215,9,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15181
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_SWRSTCON_Bits',0,4,181,8,16,4,11
	.byte	'reserved_0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'SWRSTREQ',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	497
	.byte	30,0,2,35,0,0,12,4,199,14,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15328
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_RSTCON2_Bits',0,4,233,7,16,4,11
	.byte	'reserved_0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'CLRC',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	689
	.byte	10,4,2,35,0,11
	.byte	'CSS0',0,1
	.word	672
	.byte	1,3,2,35,1,11
	.byte	'CSS1',0,1
	.word	672
	.byte	1,2,2,35,1,11
	.byte	'CSS2',0,1
	.word	672
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	672
	.byte	1,0,2,35,1,11
	.byte	'USRINFO',0,2
	.word	689
	.byte	16,0,2,35,2,0,12,4,167,14,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15462
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_EVRRSTCON_Bits',0,4,130,3,16,4,11
	.byte	'RST13TRIM',0,1
	.word	672
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	497
	.byte	16,8,2,35,0,11
	.byte	'RST13OFF',0,1
	.word	672
	.byte	1,7,2,35,3,11
	.byte	'BPRST13OFF',0,1
	.word	672
	.byte	1,6,2,35,3,11
	.byte	'RST33OFF',0,1
	.word	672
	.byte	1,5,2,35,3,11
	.byte	'BPRST33OFF',0,1
	.word	672
	.byte	1,4,2,35,3,11
	.byte	'RSTSWDOFF',0,1
	.word	672
	.byte	1,3,2,35,3,11
	.byte	'BPRSTSWDOFF',0,1
	.word	672
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	672
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	672
	.byte	1,0,2,35,3,0,12,4,159,11,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15681
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_ESRCFG_Bits',0,4,162,2,16,4,11
	.byte	'reserved_0',0,1
	.word	672
	.byte	7,1,2,35,0,11
	.byte	'EDCON',0,2
	.word	689
	.byte	2,7,2,35,0,11
	.byte	'reserved_9',0,4
	.word	497
	.byte	23,0,2,35,0,0,12,4,215,10,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15961
	.byte	4,2,35,0,0,18,8
	.word	16050
	.byte	19,1,0,10
	.byte	'_Ifx_SCU_ESROCFG_Bits',0,4,170,2,16,4,11
	.byte	'ARI',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'ARC',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	497
	.byte	30,0,2,35,0,0,12,4,223,10,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16099
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_SYSCON_Bits',0,4,189,8,16,4,11
	.byte	'CCTRIG0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'RAMINTM',0,1
	.word	672
	.byte	2,4,2,35,0,11
	.byte	'SETLUDIS',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	672
	.byte	3,0,2,35,0,11
	.byte	'DATM',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,4
	.word	497
	.byte	23,0,2,35,0,0,12,4,207,14,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16220
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_CCUCON6_Bits',0,4,177,1,16,4,11
	.byte	'CPU0DIV',0,1
	.word	672
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	497
	.byte	26,0,2,35,0,0
.L637:
	.byte	12,4,143,10,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16428
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_CCUCON7_Bits',0,4,184,1,16,4,11
	.byte	'CPU1DIV',0,1
	.word	672
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	497
	.byte	26,0,2,35,0,0
.L640:
	.byte	12,4,151,10,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16538
	.byte	4,2,35,0,0,18,20
	.word	672
	.byte	19,19,0,10
	.byte	'_Ifx_SCU_PDR_Bits',0,4,253,5,16,4,11
	.byte	'PD0',0,1
	.word	672
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	672
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	497
	.byte	24,0,2,35,0,0,12,4,167,13,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16657
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_IOCR_Bits',0,4,243,4,16,4,11
	.byte	'reserved_0',0,1
	.word	672
	.byte	4,4,2,35,0,11
	.byte	'PC0',0,1
	.word	672
	.byte	4,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	672
	.byte	4,4,2,35,1,11
	.byte	'PC1',0,1
	.word	672
	.byte	4,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	689
	.byte	16,0,2,35,2,0,12,4,191,12,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16804
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_OUT_Bits',0,4,212,5,16,4,11
	.byte	'P0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	497
	.byte	30,0,2,35,0,0,12,4,135,13,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16967
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_OMR_Bits',0,4,177,5,16,4,11
	.byte	'PS0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	689
	.byte	14,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	672
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	672
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	689
	.byte	14,0,2,35,2,0,12,4,247,12,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17082
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_IN_Bits',0,4,235,4,16,4,11
	.byte	'P0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	497
	.byte	30,0,2,35,0,0,12,4,183,12,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17254
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_EVRSTAT_Bits',0,4,244,3,16,4,11
	.byte	'EVR13',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'OV13',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'EVR33',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'OV33',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'OVSWD',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'UV13',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'UV33',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'UVSWD',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'EXTPASS13',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'EXTPASS33',0,1
	.word	672
	.byte	1,6,2,35,1,11
	.byte	'BGPROK',0,1
	.word	672
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	497
	.byte	21,0,2,35,0,0,12,4,247,11,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17368
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_EVRDVSTAT_Bits',0,4,208,2,16,4,11
	.byte	'DVS13TRIM',0,1
	.word	672
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	672
	.byte	8,0,2,35,1,11
	.byte	'DVS33TRIM',0,1
	.word	672
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	672
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	672
	.byte	1,0,2,35,3,0,12,4,255,10,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17652
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_EVR13CON_Bits',0,4,178,2,16,4,11
	.byte	'reserved_0',0,4
	.word	497
	.byte	28,4,2,35,0,11
	.byte	'EVR13OFF',0,1
	.word	672
	.byte	1,3,2,35,3,11
	.byte	'BPEVR13OFF',0,1
	.word	672
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	672
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	672
	.byte	1,0,2,35,3,0,12,4,231,10,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17825
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_EVR33CON_Bits',0,4,188,2,16,4,11
	.byte	'reserved_0',0,4
	.word	497
	.byte	28,4,2,35,0,11
	.byte	'EVR33OFF',0,1
	.word	672
	.byte	1,3,2,35,3,11
	.byte	'BPEVR33OFF',0,1
	.word	672
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	672
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	672
	.byte	1,0,2,35,3,0,12,4,239,10,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17997
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_STSTAT_Bits',0,4,165,8,16,4,11
	.byte	'HWCFG',0,1
	.word	672
	.byte	8,0,2,35,0,11
	.byte	'FTM',0,1
	.word	672
	.byte	7,1,2,35,1,11
	.byte	'MODE',0,1
	.word	672
	.byte	1,0,2,35,1,11
	.byte	'FCBAE',0,1
	.word	672
	.byte	1,7,2,35,2,11
	.byte	'LUDIS',0,1
	.word	672
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	672
	.byte	1,5,2,35,2,11
	.byte	'TRSTL',0,1
	.word	672
	.byte	1,4,2,35,2,11
	.byte	'SPDEN',0,1
	.word	672
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	672
	.byte	3,0,2,35,2,11
	.byte	'RAMINT',0,1
	.word	672
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	672
	.byte	7,0,2,35,3,0,12,4,191,14,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18169
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_PMSWCR0_Bits',0,4,254,6,16,4,11
	.byte	'reserved_0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'ESR1WKEN',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'PINAWKEN',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'PINBWKEN',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'ESR0DFEN',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'ESR0EDCON',0,1
	.word	672
	.byte	2,1,2,35,0,11
	.byte	'ESR1DFEN',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'ESR1EDCON',0,1
	.word	672
	.byte	2,6,2,35,1,11
	.byte	'PINADFEN',0,1
	.word	672
	.byte	1,5,2,35,1,11
	.byte	'PINAEDCON',0,1
	.word	672
	.byte	2,3,2,35,1,11
	.byte	'PINBDFEN',0,1
	.word	672
	.byte	1,2,2,35,1,11
	.byte	'PINBEDCON',0,1
	.word	672
	.byte	2,0,2,35,1,11
	.byte	'SCREN',0,1
	.word	672
	.byte	1,7,2,35,2,11
	.byte	'STBYRAMSEL',0,1
	.word	672
	.byte	2,5,2,35,2,11
	.byte	'SCRCLKSEL',0,1
	.word	672
	.byte	1,4,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	672
	.byte	1,3,2,35,2,11
	.byte	'TRISTEN',0,1
	.word	672
	.byte	1,2,2,35,2,11
	.byte	'TRISTREQ',0,1
	.word	672
	.byte	1,1,2,35,2,11
	.byte	'PORSTDF',0,1
	.word	672
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	672
	.byte	1,7,2,35,3,11
	.byte	'DCDCSYNC',0,1
	.word	672
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	672
	.byte	3,3,2,35,3,11
	.byte	'ESR0TRIST',0,1
	.word	672
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	672
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	672
	.byte	1,0,2,35,3,0,12,4,247,13,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18440
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_PMSWSTAT_Bits',0,4,190,7,16,4,11
	.byte	'reserved_0',0,1
	.word	672
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKP',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUN',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'PINAWKP',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUN',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'PINBWKP',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUN',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'PORSTDF',0,1
	.word	672
	.byte	1,6,2,35,1,11
	.byte	'HWCFGEVR',0,1
	.word	672
	.byte	3,3,2,35,1,11
	.byte	'STBYRAM',0,1
	.word	672
	.byte	2,1,2,35,1,11
	.byte	'TRIST',0,1
	.word	672
	.byte	1,0,2,35,1,11
	.byte	'SCRST',0,1
	.word	672
	.byte	1,7,2,35,2,11
	.byte	'SCRWKP',0,1
	.word	672
	.byte	1,6,2,35,2,11
	.byte	'SCR',0,1
	.word	672
	.byte	1,5,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	672
	.byte	1,4,2,35,2,11
	.byte	'ESR1WKEN',0,1
	.word	672
	.byte	1,3,2,35,2,11
	.byte	'PINAWKEN',0,1
	.word	672
	.byte	1,2,2,35,2,11
	.byte	'PINBWKEN',0,1
	.word	672
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	689
	.byte	4,5,2,35,2,11
	.byte	'ESR0TRIST',0,1
	.word	672
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	672
	.byte	4,0,2,35,3,0,12,4,143,14,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19017
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_PMSWSTATCLR_Bits',0,4,217,7,16,4,11
	.byte	'reserved_0',0,1
	.word	672
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKPCLR',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUNCLR',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'PINAWKPCLR',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUNCLR',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'PINBWKPCLR',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUNCLR',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	672
	.byte	8,0,2,35,1,11
	.byte	'SCRSTCLR',0,1
	.word	672
	.byte	1,7,2,35,2,11
	.byte	'SCRWKPCLR',0,1
	.word	672
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	689
	.byte	14,0,2,35,2,0,12,4,151,14,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19522
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_PMCSR_Bits',0,4,244,6,16,4,11
	.byte	'REQSLP',0,1
	.word	672
	.byte	2,6,2,35,0,11
	.byte	'SMUSLP',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	672
	.byte	5,0,2,35,0,11
	.byte	'PMST',0,1
	.word	672
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	497
	.byte	21,0,2,35,0,0,12,4,239,13,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19841
	.byte	4,2,35,0,0,18,8
	.word	19965
	.byte	19,1,0,10
	.byte	'_Ifx_SCU_DTSSTAT_Bits',0,4,228,1,16,4,11
	.byte	'RESULT',0,2
	.word	689
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	672
	.byte	4,2,2,35,1,11
	.byte	'RDY',0,1
	.word	672
	.byte	1,1,2,35,1,11
	.byte	'BUSY',0,1
	.word	672
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	689
	.byte	16,0,2,35,2,0,12,4,183,10,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20014
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_DTSCON_Bits',0,4,205,1,16,4,11
	.byte	'PWD',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'START',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	672
	.byte	2,4,2,35,0,11
	.byte	'CAL',0,4
	.word	497
	.byte	20,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	672
	.byte	7,1,2,35,3,11
	.byte	'SLCK',0,1
	.word	672
	.byte	1,0,2,35,3,0,12,4,167,10,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20178
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_PMSWCR1_Bits',0,4,156,7,16,4,11
	.byte	'SCRSTEN',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'SCRSTREQ',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	672
	.byte	6,0,2,35,0,11
	.byte	'CPUIDLSEL',0,1
	.word	672
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	672
	.byte	1,4,2,35,1,11
	.byte	'IRADIS',0,1
	.word	672
	.byte	1,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	672
	.byte	3,0,2,35,1,11
	.byte	'SCRCFG',0,1
	.word	672
	.byte	8,0,2,35,2,11
	.byte	'CPUSEL',0,1
	.word	672
	.byte	3,5,2,35,3,11
	.byte	'STBYEVEN',0,1
	.word	672
	.byte	1,4,2,35,3,11
	.byte	'STBYEV',0,1
	.word	672
	.byte	3,1,2,35,3,11
	.byte	'LCK',0,1
	.word	672
	.byte	1,0,2,35,3,0,12,4,255,13,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20354
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_PMSWCR2_Bits',0,4,173,7,16,4,11
	.byte	'SCRINT',0,1
	.word	672
	.byte	8,0,2,35,0,11
	.byte	'BUSY',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'SCRECC',0,1
	.word	672
	.byte	1,6,2,35,1,11
	.byte	'SCRWDT',0,1
	.word	672
	.byte	1,5,2,35,1,11
	.byte	'SCRRST',0,1
	.word	672
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	672
	.byte	4,0,2,35,1,11
	.byte	'TCINT',0,1
	.word	672
	.byte	8,0,2,35,2,11
	.byte	'TCINTREQ',0,1
	.word	672
	.byte	1,7,2,35,3,11
	.byte	'SMURST',0,1
	.word	672
	.byte	1,6,2,35,3,11
	.byte	'RST',0,1
	.word	672
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	672
	.byte	4,1,2,35,3,11
	.byte	'LCK',0,1
	.word	672
	.byte	1,0,2,35,3,0,12,4,135,14,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20658
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_WDTS_CON0_Bits',0,4,153,9,16,4,11
	.byte	'ENDINIT',0,4
	.word	520
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	520
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	520
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	520
	.byte	16,0,2,35,0,0,12,4,143,15,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20946
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_WDTS_CON1_Bits',0,4,162,9,16,4,11
	.byte	'CLRIRF',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'IR0',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	672
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	689
	.byte	16,0,2,35,2,0,12,4,151,15,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21080
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_WDTS_SR_Bits',0,4,178,9,16,4,11
	.byte	'AE',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	672
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	689
	.byte	16,0,2,35,2,0,12,4,159,15,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21340
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_WDTS',0,4,183,15,25,12,13
	.byte	'CON0',0
	.word	21040
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	21300
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	21529
	.byte	4,2,35,8,0,14
	.word	21569
	.byte	10
	.byte	'_Ifx_SCU_EMSR_Bits',0,4,146,2,16,4,11
	.byte	'POL',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'MODE',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'ENON',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'PSEL',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	689
	.byte	12,0,2,35,0,11
	.byte	'EMSF',0,1
	.word	672
	.byte	1,7,2,35,2,11
	.byte	'SEMSF',0,1
	.word	672
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	672
	.byte	6,0,2,35,2,11
	.byte	'EMSFM',0,1
	.word	672
	.byte	2,6,2,35,3,11
	.byte	'SEMSFM',0,1
	.word	672
	.byte	2,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	672
	.byte	4,0,2,35,3,0,12,4,207,10,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21635
	.byte	4,2,35,0,0,18,24
	.word	1186
	.byte	19,1,0,14
	.word	21900
	.byte	10
	.byte	'_Ifx_SCU_TRAPSTAT_Bits',0,4,231,8,16,4,11
	.byte	'ESR0T',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	497
	.byte	28,0,2,35,0,0,12,4,239,14,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21914
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_TRAPSET_Bits',0,4,221,8,16,4,11
	.byte	'ESR0T',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	497
	.byte	28,0,2,35,0,0,12,4,231,14,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22078
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_TRAPCLR_Bits',0,4,201,8,16,4,11
	.byte	'ESR0T',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	497
	.byte	28,0,2,35,0,0,12,4,215,14,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22241
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_TRAPDIS_Bits',0,4,211,8,16,4,11
	.byte	'ESR0T',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	497
	.byte	28,0,2,35,0,0,12,4,223,14,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22404
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_LCLCON_Bits',0,4,152,5,16,4,11
	.byte	'reserved_0',0,2
	.word	689
	.byte	16,0,2,35,0,11
	.byte	'LS',0,1
	.word	672
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,2
	.word	689
	.byte	14,1,2,35,2,11
	.byte	'LSEN',0,1
	.word	672
	.byte	1,0,2,35,3,0,12,4,223,12,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22567
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_LCLTEST_Bits',0,4,161,5,16,4,11
	.byte	'LCLT0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'LCLT1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	497
	.byte	30,0,2,35,0,0,12,4,231,12,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22710
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_CHIPID_Bits',0,4,191,1,16,4,11
	.byte	'CHREV',0,1
	.word	672
	.byte	6,2,2,35,0,11
	.byte	'CHTEC',0,1
	.word	672
	.byte	2,0,2,35,0,11
	.byte	'CHID',0,1
	.word	672
	.byte	8,0,2,35,1,11
	.byte	'EEA',0,1
	.word	672
	.byte	1,7,2,35,2,11
	.byte	'UCODE',0,1
	.word	672
	.byte	7,0,2,35,2,11
	.byte	'FSIZE',0,1
	.word	672
	.byte	4,4,2,35,3,11
	.byte	'SP',0,1
	.word	672
	.byte	2,2,2,35,3,11
	.byte	'SEC',0,1
	.word	672
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	672
	.byte	1,0,2,35,3,0,12,4,159,10,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22835
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_MANID_Bits',0,4,169,5,16,4,11
	.byte	'DEPT',0,1
	.word	672
	.byte	5,3,2,35,0,11
	.byte	'MANUF',0,2
	.word	689
	.byte	11,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	689
	.byte	16,0,2,35,2,0,12,4,239,12,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23054
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_SAFECON_Bits',0,4,158,8,16,4,11
	.byte	'HBT',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	497
	.byte	31,0,2,35,0,0,12,4,183,14,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23177
	.byte	4,2,35,0,0,18,16
	.word	672
	.byte	19,15,0,10
	.byte	'_Ifx_SCU_LBISTCTRL0_Bits',0,4,253,4,16,4,11
	.byte	'LBISTREQ',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'LBISTREQP',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'PATTERNS',0,2
	.word	689
	.byte	14,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	689
	.byte	16,0,2,35,2,0,12,4,199,12,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23292
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_LBISTCTRL1_Bits',0,4,134,5,16,4,11
	.byte	'SEED',0,4
	.word	497
	.byte	23,9,2,35,0,11
	.byte	'reserved_23',0,1
	.word	672
	.byte	1,0,2,35,2,11
	.byte	'SPLITSH',0,1
	.word	672
	.byte	3,5,2,35,3,11
	.byte	'BODY',0,1
	.word	672
	.byte	1,4,2,35,3,11
	.byte	'LBISTFREQU',0,1
	.word	672
	.byte	4,0,2,35,3,0,12,4,207,12,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23448
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_LBISTCTRL2_Bits',0,4,144,5,16,4,11
	.byte	'SIGNATURE',0,4
	.word	497
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	672
	.byte	7,1,2,35,3,11
	.byte	'LBISTDONE',0,1
	.word	672
	.byte	1,0,2,35,3,0,12,4,215,12,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23616
	.byte	4,2,35,0,0,18,28
	.word	672
	.byte	19,27,0,10
	.byte	'_Ifx_SCU_PDISC_Bits',0,4,245,5,16,4,11
	.byte	'PDIS0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	497
	.byte	30,0,2,35,0,0,12,4,159,13,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23762
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_EVRTRIM_Bits',0,4,133,4,16,4,11
	.byte	'EVR13TRIM',0,1
	.word	672
	.byte	8,0,2,35,0,11
	.byte	'SDVOUTSEL',0,1
	.word	672
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	689
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	672
	.byte	1,0,2,35,3,0,12,4,255,11,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23885
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_EVRADCSTAT_Bits',0,4,198,2,16,4,11
	.byte	'ADC13V',0,1
	.word	672
	.byte	8,0,2,35,0,11
	.byte	'ADC33V',0,1
	.word	672
	.byte	8,0,2,35,1,11
	.byte	'ADCSWDV',0,1
	.word	672
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	672
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	672
	.byte	1,0,2,35,3,0,12,4,247,10,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24034
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_EVRUVMON_Bits',0,4,142,4,16,4,11
	.byte	'EVR13UVVAL',0,1
	.word	672
	.byte	8,0,2,35,0,11
	.byte	'EVR33UVVAL',0,1
	.word	672
	.byte	8,0,2,35,1,11
	.byte	'SWDUVVAL',0,1
	.word	672
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	672
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	672
	.byte	1,0,2,35,3,0,12,4,135,12,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24199
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_EVROVMON_Bits',0,4,248,2,16,4,11
	.byte	'EVR13OVVAL',0,1
	.word	672
	.byte	8,0,2,35,0,11
	.byte	'EVR33OVVAL',0,1
	.word	672
	.byte	8,0,2,35,1,11
	.byte	'SWDOVVAL',0,1
	.word	672
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	672
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	672
	.byte	1,0,2,35,3,0,12,4,151,11,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24371
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_EVRMONCTRL_Bits',0,4,218,2,16,4,11
	.byte	'EVR13OVMOD',0,1
	.word	672
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	672
	.byte	2,4,2,35,0,11
	.byte	'EVR13UVMOD',0,1
	.word	672
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	672
	.byte	2,0,2,35,0,11
	.byte	'EVR33OVMOD',0,1
	.word	672
	.byte	2,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	672
	.byte	2,4,2,35,1,11
	.byte	'EVR33UVMOD',0,1
	.word	672
	.byte	2,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	672
	.byte	2,0,2,35,1,11
	.byte	'SWDOVMOD',0,1
	.word	672
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	672
	.byte	2,4,2,35,2,11
	.byte	'SWDUVMOD',0,1
	.word	672
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,2
	.word	689
	.byte	10,0,2,35,2,0,12,4,135,11,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24543
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_EVRSDCTRL1_Bits',0,4,203,3,16,4,11
	.byte	'SDFREQSPRD',0,2
	.word	689
	.byte	16,0,2,35,0,11
	.byte	'SDFREQ',0,1
	.word	672
	.byte	8,0,2,35,2,11
	.byte	'SDSTEP',0,1
	.word	672
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	672
	.byte	2,2,2,35,3,11
	.byte	'SDSAMPLE',0,1
	.word	672
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	672
	.byte	1,0,2,35,3,0,12,4,215,11,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24879
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_EVRSDCTRL2_Bits',0,4,214,3,16,4,11
	.byte	'DRVP',0,1
	.word	672
	.byte	8,0,2,35,0,11
	.byte	'SDMINMAXDC',0,1
	.word	672
	.byte	8,0,2,35,1,11
	.byte	'DRVN',0,1
	.word	672
	.byte	8,0,2,35,2,11
	.byte	'SDLUT',0,1
	.word	672
	.byte	6,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	672
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	672
	.byte	1,0,2,35,3,0,12,4,223,11,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25067
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_EVRSDCTRL3_Bits',0,4,225,3,16,4,11
	.byte	'SDPWMPRE',0,1
	.word	672
	.byte	8,0,2,35,0,11
	.byte	'SDPID',0,1
	.word	672
	.byte	8,0,2,35,1,11
	.byte	'SDVOKLVL',0,1
	.word	672
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	672
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	672
	.byte	1,0,2,35,3,0,12,4,231,11,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25248
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_EVRSDCTRL4_Bits',0,4,235,3,16,4,11
	.byte	'reserved_0',0,1
	.word	672
	.byte	8,0,2,35,0,11
	.byte	'SYNCDIV',0,1
	.word	672
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	497
	.byte	20,1,2,35,0,11
	.byte	'LCK',0,1
	.word	672
	.byte	1,0,2,35,3,0,12,4,239,11,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25415
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_EVRSDCOEFF1_Bits',0,4,145,3,16,4,11
	.byte	'SD5P',0,1
	.word	672
	.byte	8,0,2,35,0,11
	.byte	'SD5I',0,1
	.word	672
	.byte	8,0,2,35,1,11
	.byte	'SD5D',0,1
	.word	672
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	672
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	672
	.byte	1,0,2,35,3,0,12,4,167,11,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25566
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_EVRSDCOEFF2_Bits',0,4,155,3,16,4,11
	.byte	'SD33P',0,1
	.word	672
	.byte	8,0,2,35,0,11
	.byte	'SD33I',0,1
	.word	672
	.byte	8,0,2,35,1,11
	.byte	'SD33D',0,1
	.word	672
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	672
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	672
	.byte	1,0,2,35,3,0,12,4,175,11,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25725
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_EVRSDCOEFF3_Bits',0,4,165,3,16,4,11
	.byte	'CT5REG0',0,1
	.word	672
	.byte	8,0,2,35,0,11
	.byte	'CT5REG1',0,1
	.word	672
	.byte	8,0,2,35,1,11
	.byte	'CT5REG2',0,1
	.word	672
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	672
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	672
	.byte	1,0,2,35,3,0,12,4,183,11,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25887
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_EVRSDCOEFF4_Bits',0,4,175,3,16,4,11
	.byte	'CT5REG3',0,1
	.word	672
	.byte	8,0,2,35,0,11
	.byte	'CT5REG4',0,1
	.word	672
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	689
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	672
	.byte	1,0,2,35,3,0,12,4,191,11,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26055
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_EVRSDCOEFF5_Bits',0,4,184,3,16,4,11
	.byte	'CT33REG0',0,1
	.word	672
	.byte	8,0,2,35,0,11
	.byte	'CT33REG1',0,1
	.word	672
	.byte	8,0,2,35,1,11
	.byte	'CT33REG2',0,1
	.word	672
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	672
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	672
	.byte	1,0,2,35,3,0,12,4,199,11,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26204
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_EVRSDCOEFF6_Bits',0,4,194,3,16,4,11
	.byte	'CT33REG3',0,1
	.word	672
	.byte	8,0,2,35,0,11
	.byte	'CT33REG4',0,1
	.word	672
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	689
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	672
	.byte	1,0,2,35,3,0,12,4,207,11,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26375
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_EVROSCCTRL_Bits',0,4,235,2,16,4,11
	.byte	'OSCTRIM',0,2
	.word	689
	.byte	10,6,2,35,0,11
	.byte	'OSCPTAT',0,1
	.word	672
	.byte	6,0,2,35,1,11
	.byte	'OSCANASEL',0,1
	.word	672
	.byte	4,4,2,35,2,11
	.byte	'HPBGTRIM',0,2
	.word	689
	.byte	7,5,2,35,2,11
	.byte	'HPBGCLKEN',0,1
	.word	672
	.byte	1,4,2,35,3,11
	.byte	'OSC3V3',0,1
	.word	672
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	672
	.byte	2,1,2,35,3,11
	.byte	'LCK',0,1
	.word	672
	.byte	1,0,2,35,3,0,12,4,143,11,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26526
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_OVCENABLE_Bits',0,4,236,5,16,4,11
	.byte	'OVEN0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'OVEN1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'OVEN2',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	497
	.byte	29,0,2,35,0,0,12,4,151,13,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26754
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_OVCCON_Bits',0,4,220,5,16,4,11
	.byte	'CSEL0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'CSEL1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'CSEL2',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,2
	.word	689
	.byte	13,0,2,35,0,11
	.byte	'OVSTRT',0,1
	.word	672
	.byte	1,7,2,35,2,11
	.byte	'OVSTP',0,1
	.word	672
	.byte	1,6,2,35,2,11
	.byte	'DCINVAL',0,1
	.word	672
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	672
	.byte	5,0,2,35,2,11
	.byte	'OVCONF',0,1
	.word	672
	.byte	1,7,2,35,3,11
	.byte	'POVCONF',0,1
	.word	672
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	672
	.byte	6,0,2,35,3,0,12,4,143,13,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26898
	.byte	4,2,35,0,0,18,40
	.word	672
	.byte	19,39,0,10
	.byte	'_Ifx_SCU_EICR_Bits',0,4,238,1,16,4,11
	.byte	'reserved_0',0,1
	.word	672
	.byte	4,4,2,35,0,11
	.byte	'EXIS0',0,1
	.word	672
	.byte	3,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'FEN0',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'REN0',0,1
	.word	672
	.byte	1,6,2,35,1,11
	.byte	'LDEN0',0,1
	.word	672
	.byte	1,5,2,35,1,11
	.byte	'EIEN0',0,1
	.word	672
	.byte	1,4,2,35,1,11
	.byte	'INP0',0,1
	.word	672
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	497
	.byte	5,12,2,35,0,11
	.byte	'EXIS1',0,1
	.word	672
	.byte	3,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	672
	.byte	1,0,2,35,2,11
	.byte	'FEN1',0,1
	.word	672
	.byte	1,7,2,35,3,11
	.byte	'REN1',0,1
	.word	672
	.byte	1,6,2,35,3,11
	.byte	'LDEN1',0,1
	.word	672
	.byte	1,5,2,35,3,11
	.byte	'EIEN1',0,1
	.word	672
	.byte	1,4,2,35,3,11
	.byte	'INP1',0,1
	.word	672
	.byte	3,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	672
	.byte	1,0,2,35,3,0,12,4,191,10,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27185
	.byte	4,2,35,0,0,18,16
	.word	27522
	.byte	19,3,0,10
	.byte	'_Ifx_SCU_EIFR_Bits',0,4,132,2,16,4,11
	.byte	'INTF0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'INTF1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'INTF2',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'INTF3',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'INTF4',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'INTF5',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'INTF6',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'INTF7',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	497
	.byte	24,0,2,35,0,0,12,4,199,10,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27571
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_FMR_Bits',0,4,177,4,16,4,11
	.byte	'FS0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'FS1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'FS2',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'FS3',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'FS4',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'FS5',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'FS6',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'FS7',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	672
	.byte	8,0,2,35,1,11
	.byte	'FC0',0,1
	.word	672
	.byte	1,7,2,35,2,11
	.byte	'FC1',0,1
	.word	672
	.byte	1,6,2,35,2,11
	.byte	'FC2',0,1
	.word	672
	.byte	1,5,2,35,2,11
	.byte	'FC3',0,1
	.word	672
	.byte	1,4,2,35,2,11
	.byte	'FC4',0,1
	.word	672
	.byte	1,3,2,35,2,11
	.byte	'FC5',0,1
	.word	672
	.byte	1,2,2,35,2,11
	.byte	'FC6',0,1
	.word	672
	.byte	1,1,2,35,2,11
	.byte	'FC7',0,1
	.word	672
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	672
	.byte	8,0,2,35,3,0,12,4,159,12,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27795
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_PDRR_Bits',0,4,135,6,16,4,11
	.byte	'PDR0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'PDR1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'PDR2',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'PDR3',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'PDR4',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'PDR5',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'PDR6',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'PDR7',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	497
	.byte	24,0,2,35,0,0,12,4,175,13,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28145
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_IGCR_Bits',0,4,208,4,16,4,11
	.byte	'IPEN00',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'IPEN01',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'IPEN02',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'IPEN03',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'IPEN04',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'IPEN05',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'IPEN06',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'IPEN07',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	672
	.byte	5,3,2,35,1,11
	.byte	'GEEN0',0,1
	.word	672
	.byte	1,2,2,35,1,11
	.byte	'IGP0',0,1
	.word	672
	.byte	2,0,2,35,1,11
	.byte	'IPEN10',0,1
	.word	672
	.byte	1,7,2,35,2,11
	.byte	'IPEN11',0,1
	.word	672
	.byte	1,6,2,35,2,11
	.byte	'IPEN12',0,1
	.word	672
	.byte	1,5,2,35,2,11
	.byte	'IPEN13',0,1
	.word	672
	.byte	1,4,2,35,2,11
	.byte	'IPEN14',0,1
	.word	672
	.byte	1,3,2,35,2,11
	.byte	'IPEN15',0,1
	.word	672
	.byte	1,2,2,35,2,11
	.byte	'IPEN16',0,1
	.word	672
	.byte	1,1,2,35,2,11
	.byte	'IPEN17',0,1
	.word	672
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	672
	.byte	5,3,2,35,3,11
	.byte	'GEEN1',0,1
	.word	672
	.byte	1,2,2,35,3,11
	.byte	'IGP1',0,1
	.word	672
	.byte	2,0,2,35,3,0,12,4,175,12,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28361
	.byte	4,2,35,0,0,18,16
	.word	28786
	.byte	19,3,0,10
	.byte	'_Ifx_SCU_DTSLIM_Bits',0,4,216,1,16,4,11
	.byte	'LOWER',0,2
	.word	689
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	672
	.byte	5,1,2,35,1,11
	.byte	'LLU',0,1
	.word	672
	.byte	1,0,2,35,1,11
	.byte	'UPPER',0,2
	.word	689
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	672
	.byte	4,2,2,35,3,11
	.byte	'SLCK',0,1
	.word	672
	.byte	1,1,2,35,3,11
	.byte	'UOF',0,1
	.word	672
	.byte	1,0,2,35,3,0,12,4,175,10,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28835
	.byte	4,2,35,0,0,18,180,3
	.word	672
	.byte	19,179,3,0,10
	.byte	'_Ifx_SCU_ACCEN1_Bits',0,4,82,16,4,11
	.byte	'reserved_0',0,4
	.word	497
	.byte	32,0,2,35,0,0,12,4,207,9,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29040
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_ACCEN0_Bits',0,4,45,16,4,11
	.byte	'EN0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	672
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	672
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	672
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	672
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	672
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	672
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	672
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	672
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	672
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	672
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	672
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	672
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	672
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	672
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	672
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	672
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	672
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	672
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	672
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	672
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	672
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	672
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	672
	.byte	1,0,2,35,3,0,12,4,199,9,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29129
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU',0,4,201,15,25,128,8,13
	.byte	'reserved_0',0
	.word	4695
	.byte	8,2,35,0,13
	.byte	'ID',0
	.word	11123
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	2876
	.byte	4,2,35,12,13
	.byte	'OSCCON',0
	.word	11546
	.byte	4,2,35,16,13
	.byte	'PLLSTAT',0
	.word	11789
	.byte	4,2,35,20,13
	.byte	'PLLCON0',0
	.word	12155
	.byte	4,2,35,24,13
	.byte	'PLLCON1',0
	.word	12343
	.byte	4,2,35,28,13
	.byte	'PLLCON2',0
	.word	12453
	.byte	4,2,35,32,13
	.byte	'PLLERAYSTAT',0
	.word	12657
	.byte	4,2,35,36,13
	.byte	'PLLERAYCON0',0
	.word	13033
	.byte	4,2,35,40,13
	.byte	'PLLERAYCON1',0
	.word	13225
	.byte	4,2,35,44,13
	.byte	'CCUCON0',0
	.word	10819
	.byte	4,2,35,48,13
	.byte	'CCUCON1',0
	.word	13474
	.byte	4,2,35,52,13
	.byte	'FDR',0
	.word	11001
	.byte	4,2,35,56,13
	.byte	'EXTCON',0
	.word	13703
	.byte	4,2,35,60,13
	.byte	'CCUCON2',0
	.word	13841
	.byte	4,2,35,64,13
	.byte	'CCUCON3',0
	.word	14094
	.byte	4,2,35,68,13
	.byte	'CCUCON4',0
	.word	14339
	.byte	4,2,35,72,13
	.byte	'CCUCON5',0
	.word	14477
	.byte	4,2,35,76,13
	.byte	'RSTSTAT',0
	.word	14919
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	2876
	.byte	4,2,35,84,13
	.byte	'RSTCON',0
	.word	15141
	.byte	4,2,35,88,13
	.byte	'ARSTDIS',0
	.word	15288
	.byte	4,2,35,92,13
	.byte	'SWRSTCON',0
	.word	15422
	.byte	4,2,35,96,13
	.byte	'RSTCON2',0
	.word	15641
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	2876
	.byte	4,2,35,104,13
	.byte	'EVRRSTCON',0
	.word	15921
	.byte	4,2,35,108,13
	.byte	'ESRCFG',0
	.word	16090
	.byte	8,2,35,112,13
	.byte	'ESROCFG',0
	.word	16180
	.byte	4,2,35,120,13
	.byte	'SYSCON',0
	.word	16388
	.byte	4,2,35,124,13
	.byte	'CCUCON6',0
	.word	16498
	.byte	4,3,35,128,1,13
	.byte	'CCUCON7',0
	.word	16608
	.byte	4,3,35,132,1,13
	.byte	'reserved_88',0
	.word	16648
	.byte	20,3,35,136,1,13
	.byte	'PDR',0
	.word	16764
	.byte	4,3,35,156,1,13
	.byte	'IOCR',0
	.word	16927
	.byte	4,3,35,160,1,13
	.byte	'OUT',0
	.word	17042
	.byte	4,3,35,164,1,13
	.byte	'OMR',0
	.word	17214
	.byte	4,3,35,168,1,13
	.byte	'IN',0
	.word	17328
	.byte	4,3,35,172,1,13
	.byte	'EVRSTAT',0
	.word	17612
	.byte	4,3,35,176,1,13
	.byte	'EVRDVSTAT',0
	.word	17785
	.byte	4,3,35,180,1,13
	.byte	'EVR13CON',0
	.word	17957
	.byte	4,3,35,184,1,13
	.byte	'EVR33CON',0
	.word	18129
	.byte	4,3,35,188,1,13
	.byte	'STSTAT',0
	.word	18400
	.byte	4,3,35,192,1,13
	.byte	'reserved_C4',0
	.word	2876
	.byte	4,3,35,196,1,13
	.byte	'PMSWCR0',0
	.word	18977
	.byte	4,3,35,200,1,13
	.byte	'PMSWSTAT',0
	.word	19482
	.byte	4,3,35,204,1,13
	.byte	'PMSWSTATCLR',0
	.word	19801
	.byte	4,3,35,208,1,13
	.byte	'PMCSR',0
	.word	20005
	.byte	8,3,35,212,1,13
	.byte	'reserved_DC',0
	.word	2876
	.byte	4,3,35,220,1,13
	.byte	'DTSSTAT',0
	.word	20138
	.byte	4,3,35,224,1,13
	.byte	'DTSCON',0
	.word	20314
	.byte	4,3,35,228,1,13
	.byte	'PMSWCR1',0
	.word	20618
	.byte	4,3,35,232,1,13
	.byte	'PMSWCR2',0
	.word	20906
	.byte	4,3,35,236,1,13
	.byte	'WDTS',0
	.word	21630
	.byte	12,3,35,240,1,13
	.byte	'EMSR',0
	.word	21860
	.byte	4,3,35,252,1,13
	.byte	'WDTCPU',0
	.word	21909
	.byte	24,3,35,128,2,13
	.byte	'reserved_118',0
	.word	5035
	.byte	12,3,35,152,2,13
	.byte	'TRAPSTAT',0
	.word	22038
	.byte	4,3,35,164,2,13
	.byte	'TRAPSET',0
	.word	22201
	.byte	4,3,35,168,2,13
	.byte	'TRAPCLR',0
	.word	22364
	.byte	4,3,35,172,2,13
	.byte	'TRAPDIS',0
	.word	22527
	.byte	4,3,35,176,2,13
	.byte	'reserved_134',0
	.word	2876
	.byte	4,3,35,180,2,13
	.byte	'LCLCON1',0
	.word	22670
	.byte	4,3,35,184,2,13
	.byte	'LCLTEST',0
	.word	22795
	.byte	4,3,35,188,2,13
	.byte	'CHIPID',0
	.word	23014
	.byte	4,3,35,192,2,13
	.byte	'MANID',0
	.word	23137
	.byte	4,3,35,196,2,13
	.byte	'reserved_148',0
	.word	4695
	.byte	8,3,35,200,2,13
	.byte	'SAFECON',0
	.word	23243
	.byte	4,3,35,208,2,13
	.byte	'reserved_154',0
	.word	23283
	.byte	16,3,35,212,2,13
	.byte	'LBISTCTRL0',0
	.word	23408
	.byte	4,3,35,228,2,13
	.byte	'LBISTCTRL1',0
	.word	23576
	.byte	4,3,35,232,2,13
	.byte	'LBISTCTRL2',0
	.word	23713
	.byte	4,3,35,236,2,13
	.byte	'reserved_170',0
	.word	23753
	.byte	28,3,35,240,2,13
	.byte	'PDISC',0
	.word	23845
	.byte	4,3,35,140,3,13
	.byte	'reserved_190',0
	.word	4695
	.byte	8,3,35,144,3,13
	.byte	'EVRTRIM',0
	.word	23994
	.byte	4,3,35,152,3,13
	.byte	'EVRADCSTAT',0
	.word	24159
	.byte	4,3,35,156,3,13
	.byte	'EVRUVMON',0
	.word	24331
	.byte	4,3,35,160,3,13
	.byte	'EVROVMON',0
	.word	24503
	.byte	4,3,35,164,3,13
	.byte	'EVRMONCTRL',0
	.word	24839
	.byte	4,3,35,168,3,13
	.byte	'reserved_1AC',0
	.word	2876
	.byte	4,3,35,172,3,13
	.byte	'EVRSDCTRL1',0
	.word	25027
	.byte	4,3,35,176,3,13
	.byte	'EVRSDCTRL2',0
	.word	25208
	.byte	4,3,35,180,3,13
	.byte	'EVRSDCTRL3',0
	.word	25375
	.byte	4,3,35,184,3,13
	.byte	'EVRSDCTRL4',0
	.word	25526
	.byte	4,3,35,188,3,13
	.byte	'EVRSDCOEFF1',0
	.word	25685
	.byte	4,3,35,192,3,13
	.byte	'EVRSDCOEFF2',0
	.word	25847
	.byte	4,3,35,196,3,13
	.byte	'EVRSDCOEFF3',0
	.word	26015
	.byte	4,3,35,200,3,13
	.byte	'EVRSDCOEFF4',0
	.word	26164
	.byte	4,3,35,204,3,13
	.byte	'EVRSDCOEFF5',0
	.word	26335
	.byte	4,3,35,208,3,13
	.byte	'EVRSDCOEFF6',0
	.word	26486
	.byte	4,3,35,212,3,13
	.byte	'EVROSCCTRL',0
	.word	26714
	.byte	4,3,35,216,3,13
	.byte	'reserved_1DC',0
	.word	2876
	.byte	4,3,35,220,3,13
	.byte	'OVCENABLE',0
	.word	26858
	.byte	4,3,35,224,3,13
	.byte	'OVCCON',0
	.word	27136
	.byte	4,3,35,228,3,13
	.byte	'reserved_1E8',0
	.word	27176
	.byte	40,3,35,232,3,13
	.byte	'EICR',0
	.word	27562
	.byte	16,3,35,144,4,13
	.byte	'EIFR',0
	.word	27755
	.byte	4,3,35,160,4,13
	.byte	'FMR',0
	.word	28105
	.byte	4,3,35,164,4,13
	.byte	'PDRR',0
	.word	28321
	.byte	4,3,35,168,4,13
	.byte	'IGCR',0
	.word	28826
	.byte	16,3,35,172,4,13
	.byte	'reserved_23C',0
	.word	2876
	.byte	4,3,35,188,4,13
	.byte	'DTSLIM',0
	.word	28989
	.byte	4,3,35,192,4,13
	.byte	'reserved_244',0
	.word	29029
	.byte	180,3,3,35,196,4,13
	.byte	'ACCEN1',0
	.word	29089
	.byte	4,3,35,248,7,13
	.byte	'ACCEN0',0
	.word	29658
	.byte	4,3,35,252,7,0,14
	.word	29698
.L509:
	.byte	3
	.word	31688
	.byte	27
	.byte	'IfxScuCcu_PllStepsFunctionHook',0,9,148,1,16
	.word	236
	.byte	28,9,231,5,9,12,13
	.byte	'k2Step',0
	.word	672
	.byte	1,2,35,0,13
	.byte	'waitTime',0
	.word	293
	.byte	4,2,35,2,13
	.byte	'hookFunction',0
	.word	31698
	.byte	4,2,35,8,0,3
	.word	31738
	.byte	28,9,221,5,9,8,13
	.byte	'pDivider',0
	.word	672
	.byte	1,2,35,0,13
	.byte	'nDivider',0
	.word	672
	.byte	1,2,35,1,13
	.byte	'k2Initial',0
	.word	672
	.byte	1,2,35,2,13
	.byte	'waitTime',0
	.word	293
	.byte	4,2,35,4,0,28,9,137,6,9,16,13
	.byte	'numOfPllDividerSteps',0
	.word	672
	.byte	1,2,35,0,13
	.byte	'pllDividerStep',0
	.word	31801
	.byte	4,2,35,4,13
	.byte	'pllInitialStep',0
	.word	31806
	.byte	8,2,35,8,0,28,9,212,5,9,8,13
	.byte	'value',0
	.word	1639
	.byte	4,2,35,0,13
	.byte	'mask',0
	.word	1639
	.byte	4,2,35,4,0,28,9,244,5,9,48,13
	.byte	'ccucon0',0
	.word	31971
	.byte	8,2,35,0,13
	.byte	'ccucon1',0
	.word	31971
	.byte	8,2,35,8,13
	.byte	'ccucon2',0
	.word	31971
	.byte	8,2,35,16,13
	.byte	'ccucon5',0
	.word	31971
	.byte	8,2,35,24,13
	.byte	'ccucon6',0
	.word	31971
	.byte	8,2,35,32,13
	.byte	'ccucon7',0
	.word	31971
	.byte	8,2,35,40,0,28,9,128,6,9,8,13
	.byte	'value',0
	.word	1639
	.byte	4,2,35,0,13
	.byte	'mask',0
	.word	1639
	.byte	4,2,35,4,0,28,9,150,6,9,76,13
	.byte	'sysPll',0
	.word	31886
	.byte	16,2,35,0,13
	.byte	'clockDistribution',0
	.word	32007
	.byte	48,2,35,16,13
	.byte	'flashFconWaitStateConfig',0
	.word	32116
	.byte	8,2,35,64,13
	.byte	'xtalFrequency',0
	.word	1639
	.byte	4,2,35,72,0
.L571:
	.byte	3
	.word	32152
	.byte	3
	.word	233
.L578:
	.byte	26
	.word	1639
.L580:
	.byte	26
	.word	1639
.L582:
	.byte	26
	.word	1639
.L584:
	.byte	26
	.word	1639
.L586:
	.byte	26
	.word	1639
.L588:
	.byte	26
	.word	672
.L590:
	.byte	26
	.word	672
.L592:
	.byte	26
	.word	672
.L594:
	.byte	26
	.word	672
.L596:
	.byte	26
	.word	672
.L598:
	.byte	26
	.word	672
	.byte	26
	.word	32152
.L613:
	.byte	3
	.word	32324
	.byte	10
	.byte	'_Ifx_FLASH_FCON_Bits',0,10,174,1,16,4,11
	.byte	'WSPFLASH',0,1
	.word	672
	.byte	4,4,2,35,0,11
	.byte	'WSECPF',0,1
	.word	672
	.byte	2,2,2,35,0,11
	.byte	'WSDFLASH',0,2
	.word	689
	.byte	6,4,2,35,0,11
	.byte	'WSECDF',0,1
	.word	672
	.byte	3,1,2,35,1,11
	.byte	'IDLE',0,1
	.word	672
	.byte	1,0,2,35,1,11
	.byte	'ESLDIS',0,1
	.word	672
	.byte	1,7,2,35,2,11
	.byte	'SLEEP',0,1
	.word	672
	.byte	1,6,2,35,2,11
	.byte	'NSAFECC',0,1
	.word	672
	.byte	1,5,2,35,2,11
	.byte	'STALL',0,1
	.word	672
	.byte	1,4,2,35,2,11
	.byte	'RES21',0,1
	.word	672
	.byte	2,2,2,35,2,11
	.byte	'RES23',0,1
	.word	672
	.byte	2,0,2,35,2,11
	.byte	'VOPERM',0,1
	.word	672
	.byte	1,7,2,35,3,11
	.byte	'SQERM',0,1
	.word	672
	.byte	1,6,2,35,3,11
	.byte	'PROERM',0,1
	.word	672
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	672
	.byte	3,2,2,35,3,11
	.byte	'PR5V',0,1
	.word	672
	.byte	1,1,2,35,3,11
	.byte	'EOBM',0,1
	.word	672
	.byte	1,0,2,35,3,0
.L644:
	.byte	12,10,243,4,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32334
	.byte	4,2,35,0,0,28,9,160,6,9,8,13
	.byte	'pllInitialStep',0
	.word	31806
	.byte	8,2,35,0,0,26
	.word	32707
.L649:
	.byte	3
	.word	32738
.L658:
	.byte	3
	.word	32707
	.byte	22
	.word	9975
	.byte	6,0
.L677:
	.byte	7
	.byte	'long int',0,4,5,29
	.byte	'__INDIRECT__',0,11,1,1,1,1,1,7
	.byte	'short int',0,2,5,27
	.byte	'__wchar_t',0,11,1,1
	.word	32792
	.byte	27
	.byte	'__size_t',0,11,1,1
	.word	497
	.byte	27
	.byte	'__ptrdiff_t',0,11,1,1
	.word	513
	.byte	30,1,3
	.word	32860
	.byte	27
	.byte	'__codeptr',0,11,1,1
	.word	32862
	.byte	15,12,236,10,9,1,16
	.byte	'IfxScu_CCUCON0_CLKSEL_fBack',0,0,16
	.byte	'IfxScu_CCUCON0_CLKSEL_fPll',0,1,0,27
	.byte	'IfxScu_CCUCON0_CLKSEL',0,12,240,10,3
	.word	32885
	.byte	15,12,250,10,9,1,16
	.byte	'IfxScu_WDTCON1_IR_divBy16384',0,0,16
	.byte	'IfxScu_WDTCON1_IR_divBy256',0,1,16
	.byte	'IfxScu_WDTCON1_IR_divBy64',0,2,0,27
	.byte	'IfxScu_WDTCON1_IR',0,12,255,10,3
	.word	32982
	.byte	27
	.byte	'boolean',0,13,101,29
	.word	672
	.byte	27
	.byte	'uint8',0,13,105,29
	.word	672
	.byte	27
	.byte	'uint16',0,13,109,29
	.word	689
	.byte	27
	.byte	'uint32',0,13,113,29
	.word	1639
	.byte	27
	.byte	'uint64',0,13,118,29
	.word	379
	.byte	27
	.byte	'sint16',0,13,126,29
	.word	32792
	.byte	27
	.byte	'sint32',0,13,131,1,29
	.word	32760
	.byte	7
	.byte	'long long int',0,8,5,27
	.byte	'sint64',0,13,138,1,29
	.word	33210
	.byte	27
	.byte	'float32',0,13,167,1,29
	.word	293
	.byte	27
	.byte	'pvoid',0,14,57,28
	.word	411
	.byte	27
	.byte	'Ifx_TickTime',0,14,79,28
	.word	33210
	.byte	15,14,130,1,9,1,16
	.byte	'Ifx_RxSel_a',0,0,16
	.byte	'Ifx_RxSel_b',0,1,16
	.byte	'Ifx_RxSel_c',0,2,16
	.byte	'Ifx_RxSel_d',0,3,16
	.byte	'Ifx_RxSel_e',0,4,16
	.byte	'Ifx_RxSel_f',0,5,16
	.byte	'Ifx_RxSel_g',0,6,16
	.byte	'Ifx_RxSel_h',0,7,0,27
	.byte	'Ifx_RxSel',0,14,140,1,3
	.word	33295
	.byte	27
	.byte	'Ifx_SCU_ACCEN0_Bits',0,4,79,3
	.word	29129
	.byte	27
	.byte	'Ifx_SCU_ACCEN1_Bits',0,4,85,3
	.word	29040
	.byte	27
	.byte	'Ifx_SCU_ARSTDIS_Bits',0,4,94,3
	.word	15181
	.byte	27
	.byte	'Ifx_SCU_CCUCON0_Bits',0,4,111,3
	.word	10568
	.byte	27
	.byte	'Ifx_SCU_CCUCON1_Bits',0,4,126,3
	.word	13265
	.byte	27
	.byte	'Ifx_SCU_CCUCON2_Bits',0,4,135,1,3
	.word	13743
	.byte	27
	.byte	'Ifx_SCU_CCUCON3_Bits',0,4,150,1,3
	.word	13881
	.byte	27
	.byte	'Ifx_SCU_CCUCON4_Bits',0,4,165,1,3
	.word	14134
	.byte	27
	.byte	'Ifx_SCU_CCUCON5_Bits',0,4,174,1,3
	.word	14379
	.byte	27
	.byte	'Ifx_SCU_CCUCON6_Bits',0,4,181,1,3
	.word	16428
	.byte	27
	.byte	'Ifx_SCU_CCUCON7_Bits',0,4,188,1,3
	.word	16538
	.byte	27
	.byte	'Ifx_SCU_CHIPID_Bits',0,4,202,1,3
	.word	22835
	.byte	27
	.byte	'Ifx_SCU_DTSCON_Bits',0,4,213,1,3
	.word	20178
	.byte	27
	.byte	'Ifx_SCU_DTSLIM_Bits',0,4,225,1,3
	.word	28835
	.byte	27
	.byte	'Ifx_SCU_DTSSTAT_Bits',0,4,235,1,3
	.word	20014
	.byte	27
	.byte	'Ifx_SCU_EICR_Bits',0,4,129,2,3
	.word	27185
	.byte	27
	.byte	'Ifx_SCU_EIFR_Bits',0,4,143,2,3
	.word	27571
	.byte	27
	.byte	'Ifx_SCU_EMSR_Bits',0,4,159,2,3
	.word	21635
	.byte	27
	.byte	'Ifx_SCU_ESRCFG_Bits',0,4,167,2,3
	.word	15961
	.byte	27
	.byte	'Ifx_SCU_ESROCFG_Bits',0,4,175,2,3
	.word	16099
	.byte	27
	.byte	'Ifx_SCU_EVR13CON_Bits',0,4,185,2,3
	.word	17825
	.byte	27
	.byte	'Ifx_SCU_EVR33CON_Bits',0,4,195,2,3
	.word	17997
	.byte	27
	.byte	'Ifx_SCU_EVRADCSTAT_Bits',0,4,205,2,3
	.word	24034
	.byte	27
	.byte	'Ifx_SCU_EVRDVSTAT_Bits',0,4,215,2,3
	.word	17652
	.byte	27
	.byte	'Ifx_SCU_EVRMONCTRL_Bits',0,4,232,2,3
	.word	24543
	.byte	27
	.byte	'Ifx_SCU_EVROSCCTRL_Bits',0,4,245,2,3
	.word	26526
	.byte	27
	.byte	'Ifx_SCU_EVROVMON_Bits',0,4,255,2,3
	.word	24371
	.byte	27
	.byte	'Ifx_SCU_EVRRSTCON_Bits',0,4,142,3,3
	.word	15681
	.byte	27
	.byte	'Ifx_SCU_EVRSDCOEFF1_Bits',0,4,152,3,3
	.word	25566
	.byte	27
	.byte	'Ifx_SCU_EVRSDCOEFF2_Bits',0,4,162,3,3
	.word	25725
	.byte	27
	.byte	'Ifx_SCU_EVRSDCOEFF3_Bits',0,4,172,3,3
	.word	25887
	.byte	27
	.byte	'Ifx_SCU_EVRSDCOEFF4_Bits',0,4,181,3,3
	.word	26055
	.byte	27
	.byte	'Ifx_SCU_EVRSDCOEFF5_Bits',0,4,191,3,3
	.word	26204
	.byte	27
	.byte	'Ifx_SCU_EVRSDCOEFF6_Bits',0,4,200,3,3
	.word	26375
	.byte	27
	.byte	'Ifx_SCU_EVRSDCTRL1_Bits',0,4,211,3,3
	.word	24879
	.byte	27
	.byte	'Ifx_SCU_EVRSDCTRL2_Bits',0,4,222,3,3
	.word	25067
	.byte	27
	.byte	'Ifx_SCU_EVRSDCTRL3_Bits',0,4,232,3,3
	.word	25248
	.byte	27
	.byte	'Ifx_SCU_EVRSDCTRL4_Bits',0,4,241,3,3
	.word	25415
	.byte	27
	.byte	'Ifx_SCU_EVRSTAT_Bits',0,4,130,4,3
	.word	17368
	.byte	27
	.byte	'Ifx_SCU_EVRTRIM_Bits',0,4,139,4,3
	.word	23885
	.byte	27
	.byte	'Ifx_SCU_EVRUVMON_Bits',0,4,149,4,3
	.word	24199
	.byte	27
	.byte	'Ifx_SCU_EXTCON_Bits',0,4,163,4,3
	.word	13514
	.byte	27
	.byte	'Ifx_SCU_FDR_Bits',0,4,174,4,3
	.word	10864
	.byte	27
	.byte	'Ifx_SCU_FMR_Bits',0,4,197,4,3
	.word	27795
	.byte	27
	.byte	'Ifx_SCU_ID_Bits',0,4,205,4,3
	.word	11041
	.byte	27
	.byte	'Ifx_SCU_IGCR_Bits',0,4,232,4,3
	.word	28361
	.byte	27
	.byte	'Ifx_SCU_IN_Bits',0,4,240,4,3
	.word	17254
	.byte	27
	.byte	'Ifx_SCU_IOCR_Bits',0,4,250,4,3
	.word	16804
	.byte	27
	.byte	'Ifx_SCU_LBISTCTRL0_Bits',0,4,131,5,3
	.word	23292
	.byte	27
	.byte	'Ifx_SCU_LBISTCTRL1_Bits',0,4,141,5,3
	.word	23448
	.byte	27
	.byte	'Ifx_SCU_LBISTCTRL2_Bits',0,4,149,5,3
	.word	23616
	.byte	27
	.byte	'Ifx_SCU_LCLCON_Bits',0,4,158,5,3
	.word	22567
	.byte	27
	.byte	'Ifx_SCU_LCLTEST_Bits',0,4,166,5,3
	.word	22710
	.byte	27
	.byte	'Ifx_SCU_MANID_Bits',0,4,174,5,3
	.word	23054
	.byte	27
	.byte	'Ifx_SCU_OMR_Bits',0,4,185,5,3
	.word	17082
	.byte	27
	.byte	'Ifx_SCU_OSCCON_Bits',0,4,209,5,3
	.word	11163
	.byte	27
	.byte	'Ifx_SCU_OUT_Bits',0,4,217,5,3
	.word	16967
	.byte	27
	.byte	'Ifx_SCU_OVCCON_Bits',0,4,233,5,3
	.word	26898
	.byte	27
	.byte	'Ifx_SCU_OVCENABLE_Bits',0,4,242,5,3
	.word	26754
	.byte	27
	.byte	'Ifx_SCU_PDISC_Bits',0,4,250,5,3
	.word	23762
	.byte	27
	.byte	'Ifx_SCU_PDR_Bits',0,4,132,6,3
	.word	16657
	.byte	27
	.byte	'Ifx_SCU_PDRR_Bits',0,4,146,6,3
	.word	28145
	.byte	27
	.byte	'Ifx_SCU_PLLCON0_Bits',0,4,166,6,3
	.word	11829
	.byte	27
	.byte	'Ifx_SCU_PLLCON1_Bits',0,4,177,6,3
	.word	12195
	.byte	27
	.byte	'Ifx_SCU_PLLCON2_Bits',0,4,184,6,3
	.word	12383
	.byte	27
	.byte	'Ifx_SCU_PLLERAYCON0_Bits',0,4,204,6,3
	.word	12697
	.byte	27
	.byte	'Ifx_SCU_PLLERAYCON1_Bits',0,4,215,6,3
	.word	13073
	.byte	27
	.byte	'Ifx_SCU_PLLERAYSTAT_Bits',0,4,227,6,3
	.word	12493
	.byte	27
	.byte	'Ifx_SCU_PLLSTAT_Bits',0,4,241,6,3
	.word	11586
	.byte	27
	.byte	'Ifx_SCU_PMCSR_Bits',0,4,251,6,3
	.word	19841
	.byte	27
	.byte	'Ifx_SCU_PMSWCR0_Bits',0,4,153,7,3
	.word	18440
	.byte	27
	.byte	'Ifx_SCU_PMSWCR1_Bits',0,4,170,7,3
	.word	20354
	.byte	27
	.byte	'Ifx_SCU_PMSWCR2_Bits',0,4,187,7,3
	.word	20658
	.byte	27
	.byte	'Ifx_SCU_PMSWSTAT_Bits',0,4,214,7,3
	.word	19017
	.byte	27
	.byte	'Ifx_SCU_PMSWSTATCLR_Bits',0,4,230,7,3
	.word	19522
	.byte	27
	.byte	'Ifx_SCU_RSTCON2_Bits',0,4,243,7,3
	.word	15462
	.byte	27
	.byte	'Ifx_SCU_RSTCON_Bits',0,4,129,8,3
	.word	14959
	.byte	27
	.byte	'Ifx_SCU_RSTSTAT_Bits',0,4,155,8,3
	.word	14517
	.byte	27
	.byte	'Ifx_SCU_SAFECON_Bits',0,4,162,8,3
	.word	23177
	.byte	27
	.byte	'Ifx_SCU_STSTAT_Bits',0,4,178,8,3
	.word	18169
	.byte	27
	.byte	'Ifx_SCU_SWRSTCON_Bits',0,4,186,8,3
	.word	15328
	.byte	27
	.byte	'Ifx_SCU_SYSCON_Bits',0,4,198,8,3
	.word	16220
	.byte	27
	.byte	'Ifx_SCU_TRAPCLR_Bits',0,4,208,8,3
	.word	22241
	.byte	27
	.byte	'Ifx_SCU_TRAPDIS_Bits',0,4,218,8,3
	.word	22404
	.byte	27
	.byte	'Ifx_SCU_TRAPSET_Bits',0,4,228,8,3
	.word	22078
	.byte	27
	.byte	'Ifx_SCU_TRAPSTAT_Bits',0,4,238,8,3
	.word	21914
	.byte	27
	.byte	'Ifx_SCU_WDTCPU_CON0_Bits',0,4,247,8,3
	.word	536
	.byte	27
	.byte	'Ifx_SCU_WDTCPU_CON1_Bits',0,4,134,9,3
	.word	711
	.byte	27
	.byte	'Ifx_SCU_WDTCPU_SR_Bits',0,4,150,9,3
	.word	955
	.byte	27
	.byte	'Ifx_SCU_WDTS_CON0_Bits',0,4,159,9,3
	.word	20946
	.byte	27
	.byte	'Ifx_SCU_WDTS_CON1_Bits',0,4,175,9,3
	.word	21080
	.byte	27
	.byte	'Ifx_SCU_WDTS_SR_Bits',0,4,191,9,3
	.word	21340
	.byte	27
	.byte	'Ifx_SCU_ACCEN0',0,4,204,9,3
	.word	29658
	.byte	27
	.byte	'Ifx_SCU_ACCEN1',0,4,212,9,3
	.word	29089
	.byte	27
	.byte	'Ifx_SCU_ARSTDIS',0,4,220,9,3
	.word	15288
	.byte	27
	.byte	'Ifx_SCU_CCUCON0',0,4,228,9,3
	.word	10819
	.byte	27
	.byte	'Ifx_SCU_CCUCON1',0,4,236,9,3
	.word	13474
	.byte	27
	.byte	'Ifx_SCU_CCUCON2',0,4,244,9,3
	.word	13841
	.byte	27
	.byte	'Ifx_SCU_CCUCON3',0,4,252,9,3
	.word	14094
	.byte	27
	.byte	'Ifx_SCU_CCUCON4',0,4,132,10,3
	.word	14339
	.byte	27
	.byte	'Ifx_SCU_CCUCON5',0,4,140,10,3
	.word	14477
	.byte	27
	.byte	'Ifx_SCU_CCUCON6',0,4,148,10,3
	.word	16498
	.byte	27
	.byte	'Ifx_SCU_CCUCON7',0,4,156,10,3
	.word	16608
	.byte	27
	.byte	'Ifx_SCU_CHIPID',0,4,164,10,3
	.word	23014
	.byte	27
	.byte	'Ifx_SCU_DTSCON',0,4,172,10,3
	.word	20314
	.byte	27
	.byte	'Ifx_SCU_DTSLIM',0,4,180,10,3
	.word	28989
	.byte	27
	.byte	'Ifx_SCU_DTSSTAT',0,4,188,10,3
	.word	20138
	.byte	27
	.byte	'Ifx_SCU_EICR',0,4,196,10,3
	.word	27522
	.byte	27
	.byte	'Ifx_SCU_EIFR',0,4,204,10,3
	.word	27755
	.byte	27
	.byte	'Ifx_SCU_EMSR',0,4,212,10,3
	.word	21860
	.byte	27
	.byte	'Ifx_SCU_ESRCFG',0,4,220,10,3
	.word	16050
	.byte	27
	.byte	'Ifx_SCU_ESROCFG',0,4,228,10,3
	.word	16180
	.byte	27
	.byte	'Ifx_SCU_EVR13CON',0,4,236,10,3
	.word	17957
	.byte	27
	.byte	'Ifx_SCU_EVR33CON',0,4,244,10,3
	.word	18129
	.byte	27
	.byte	'Ifx_SCU_EVRADCSTAT',0,4,252,10,3
	.word	24159
	.byte	27
	.byte	'Ifx_SCU_EVRDVSTAT',0,4,132,11,3
	.word	17785
	.byte	27
	.byte	'Ifx_SCU_EVRMONCTRL',0,4,140,11,3
	.word	24839
	.byte	27
	.byte	'Ifx_SCU_EVROSCCTRL',0,4,148,11,3
	.word	26714
	.byte	27
	.byte	'Ifx_SCU_EVROVMON',0,4,156,11,3
	.word	24503
	.byte	27
	.byte	'Ifx_SCU_EVRRSTCON',0,4,164,11,3
	.word	15921
	.byte	27
	.byte	'Ifx_SCU_EVRSDCOEFF1',0,4,172,11,3
	.word	25685
	.byte	27
	.byte	'Ifx_SCU_EVRSDCOEFF2',0,4,180,11,3
	.word	25847
	.byte	27
	.byte	'Ifx_SCU_EVRSDCOEFF3',0,4,188,11,3
	.word	26015
	.byte	27
	.byte	'Ifx_SCU_EVRSDCOEFF4',0,4,196,11,3
	.word	26164
	.byte	27
	.byte	'Ifx_SCU_EVRSDCOEFF5',0,4,204,11,3
	.word	26335
	.byte	27
	.byte	'Ifx_SCU_EVRSDCOEFF6',0,4,212,11,3
	.word	26486
	.byte	27
	.byte	'Ifx_SCU_EVRSDCTRL1',0,4,220,11,3
	.word	25027
	.byte	27
	.byte	'Ifx_SCU_EVRSDCTRL2',0,4,228,11,3
	.word	25208
	.byte	27
	.byte	'Ifx_SCU_EVRSDCTRL3',0,4,236,11,3
	.word	25375
	.byte	27
	.byte	'Ifx_SCU_EVRSDCTRL4',0,4,244,11,3
	.word	25526
	.byte	27
	.byte	'Ifx_SCU_EVRSTAT',0,4,252,11,3
	.word	17612
	.byte	27
	.byte	'Ifx_SCU_EVRTRIM',0,4,132,12,3
	.word	23994
	.byte	27
	.byte	'Ifx_SCU_EVRUVMON',0,4,140,12,3
	.word	24331
	.byte	27
	.byte	'Ifx_SCU_EXTCON',0,4,148,12,3
	.word	13703
	.byte	27
	.byte	'Ifx_SCU_FDR',0,4,156,12,3
	.word	11001
	.byte	27
	.byte	'Ifx_SCU_FMR',0,4,164,12,3
	.word	28105
	.byte	27
	.byte	'Ifx_SCU_ID',0,4,172,12,3
	.word	11123
	.byte	27
	.byte	'Ifx_SCU_IGCR',0,4,180,12,3
	.word	28786
	.byte	27
	.byte	'Ifx_SCU_IN',0,4,188,12,3
	.word	17328
	.byte	27
	.byte	'Ifx_SCU_IOCR',0,4,196,12,3
	.word	16927
	.byte	27
	.byte	'Ifx_SCU_LBISTCTRL0',0,4,204,12,3
	.word	23408
	.byte	27
	.byte	'Ifx_SCU_LBISTCTRL1',0,4,212,12,3
	.word	23576
	.byte	27
	.byte	'Ifx_SCU_LBISTCTRL2',0,4,220,12,3
	.word	23713
	.byte	27
	.byte	'Ifx_SCU_LCLCON',0,4,228,12,3
	.word	22670
	.byte	27
	.byte	'Ifx_SCU_LCLTEST',0,4,236,12,3
	.word	22795
	.byte	27
	.byte	'Ifx_SCU_MANID',0,4,244,12,3
	.word	23137
	.byte	27
	.byte	'Ifx_SCU_OMR',0,4,252,12,3
	.word	17214
	.byte	27
	.byte	'Ifx_SCU_OSCCON',0,4,132,13,3
	.word	11546
	.byte	27
	.byte	'Ifx_SCU_OUT',0,4,140,13,3
	.word	17042
	.byte	27
	.byte	'Ifx_SCU_OVCCON',0,4,148,13,3
	.word	27136
	.byte	27
	.byte	'Ifx_SCU_OVCENABLE',0,4,156,13,3
	.word	26858
	.byte	27
	.byte	'Ifx_SCU_PDISC',0,4,164,13,3
	.word	23845
	.byte	27
	.byte	'Ifx_SCU_PDR',0,4,172,13,3
	.word	16764
	.byte	27
	.byte	'Ifx_SCU_PDRR',0,4,180,13,3
	.word	28321
	.byte	27
	.byte	'Ifx_SCU_PLLCON0',0,4,188,13,3
	.word	12155
	.byte	27
	.byte	'Ifx_SCU_PLLCON1',0,4,196,13,3
	.word	12343
	.byte	27
	.byte	'Ifx_SCU_PLLCON2',0,4,204,13,3
	.word	12453
	.byte	27
	.byte	'Ifx_SCU_PLLERAYCON0',0,4,212,13,3
	.word	13033
	.byte	27
	.byte	'Ifx_SCU_PLLERAYCON1',0,4,220,13,3
	.word	13225
	.byte	27
	.byte	'Ifx_SCU_PLLERAYSTAT',0,4,228,13,3
	.word	12657
	.byte	27
	.byte	'Ifx_SCU_PLLSTAT',0,4,236,13,3
	.word	11789
	.byte	27
	.byte	'Ifx_SCU_PMCSR',0,4,244,13,3
	.word	19965
	.byte	27
	.byte	'Ifx_SCU_PMSWCR0',0,4,252,13,3
	.word	18977
	.byte	27
	.byte	'Ifx_SCU_PMSWCR1',0,4,132,14,3
	.word	20618
	.byte	27
	.byte	'Ifx_SCU_PMSWCR2',0,4,140,14,3
	.word	20906
	.byte	27
	.byte	'Ifx_SCU_PMSWSTAT',0,4,148,14,3
	.word	19482
	.byte	27
	.byte	'Ifx_SCU_PMSWSTATCLR',0,4,156,14,3
	.word	19801
	.byte	27
	.byte	'Ifx_SCU_RSTCON',0,4,164,14,3
	.word	15141
	.byte	27
	.byte	'Ifx_SCU_RSTCON2',0,4,172,14,3
	.word	15641
	.byte	27
	.byte	'Ifx_SCU_RSTSTAT',0,4,180,14,3
	.word	14919
	.byte	27
	.byte	'Ifx_SCU_SAFECON',0,4,188,14,3
	.word	23243
	.byte	27
	.byte	'Ifx_SCU_STSTAT',0,4,196,14,3
	.word	18400
	.byte	27
	.byte	'Ifx_SCU_SWRSTCON',0,4,204,14,3
	.word	15422
	.byte	27
	.byte	'Ifx_SCU_SYSCON',0,4,212,14,3
	.word	16388
	.byte	27
	.byte	'Ifx_SCU_TRAPCLR',0,4,220,14,3
	.word	22364
	.byte	27
	.byte	'Ifx_SCU_TRAPDIS',0,4,228,14,3
	.word	22527
	.byte	27
	.byte	'Ifx_SCU_TRAPSET',0,4,236,14,3
	.word	22201
	.byte	27
	.byte	'Ifx_SCU_TRAPSTAT',0,4,244,14,3
	.word	22038
	.byte	27
	.byte	'Ifx_SCU_WDTCPU_CON0',0,4,252,14,3
	.word	632
	.byte	27
	.byte	'Ifx_SCU_WDTCPU_CON1',0,4,132,15,3
	.word	915
	.byte	27
	.byte	'Ifx_SCU_WDTCPU_SR',0,4,140,15,3
	.word	1146
	.byte	27
	.byte	'Ifx_SCU_WDTS_CON0',0,4,148,15,3
	.word	21040
	.byte	27
	.byte	'Ifx_SCU_WDTS_CON1',0,4,156,15,3
	.word	21300
	.byte	27
	.byte	'Ifx_SCU_WDTS_SR',0,4,164,15,3
	.word	21529
	.byte	14
	.word	1186
	.byte	27
	.byte	'Ifx_SCU_WDTCPU',0,4,180,15,3
	.word	38548
	.byte	14
	.word	21569
	.byte	27
	.byte	'Ifx_SCU_WDTS',0,4,188,15,3
	.word	38577
	.byte	14
	.word	29698
	.byte	27
	.byte	'Ifx_SCU',0,4,181,16,3
	.word	38604
	.byte	10
	.byte	'_Ifx_CPU_A_Bits',0,15,45,16,4,11
	.byte	'ADDR',0,4
	.word	520
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_CPU_A_Bits',0,15,48,3
	.word	38626
	.byte	10
	.byte	'_Ifx_CPU_BIV_Bits',0,15,51,16,4,11
	.byte	'VSS',0,4
	.word	520
	.byte	1,31,2,35,0,11
	.byte	'BIV',0,4
	.word	520
	.byte	31,0,2,35,0,0,27
	.byte	'Ifx_CPU_BIV_Bits',0,15,55,3
	.word	38687
	.byte	10
	.byte	'_Ifx_CPU_BTV_Bits',0,15,58,16,4,11
	.byte	'reserved_0',0,4
	.word	520
	.byte	1,31,2,35,0,11
	.byte	'BTV',0,4
	.word	520
	.byte	31,0,2,35,0,0,27
	.byte	'Ifx_CPU_BTV_Bits',0,15,62,3
	.word	38766
	.byte	10
	.byte	'_Ifx_CPU_CCNT_Bits',0,15,65,16,4,11
	.byte	'CountValue',0,4
	.word	520
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	520
	.byte	1,0,2,35,0,0,27
	.byte	'Ifx_CPU_CCNT_Bits',0,15,69,3
	.word	38852
	.byte	10
	.byte	'_Ifx_CPU_CCTRL_Bits',0,15,72,16,4,11
	.byte	'CM',0,4
	.word	520
	.byte	1,31,2,35,0,11
	.byte	'CE',0,4
	.word	520
	.byte	1,30,2,35,0,11
	.byte	'M1',0,4
	.word	520
	.byte	3,27,2,35,0,11
	.byte	'M2',0,4
	.word	520
	.byte	3,24,2,35,0,11
	.byte	'M3',0,4
	.word	520
	.byte	3,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	520
	.byte	21,0,2,35,0,0,27
	.byte	'Ifx_CPU_CCTRL_Bits',0,15,80,3
	.word	38941
	.byte	10
	.byte	'_Ifx_CPU_COMPAT_Bits',0,15,83,16,4,11
	.byte	'reserved_0',0,4
	.word	520
	.byte	3,29,2,35,0,11
	.byte	'RM',0,4
	.word	520
	.byte	1,28,2,35,0,11
	.byte	'SP',0,4
	.word	520
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	520
	.byte	27,0,2,35,0,0,27
	.byte	'Ifx_CPU_COMPAT_Bits',0,15,89,3
	.word	39087
	.byte	10
	.byte	'_Ifx_CPU_CORE_ID_Bits',0,15,92,16,4,11
	.byte	'CORE_ID',0,4
	.word	520
	.byte	3,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	520
	.byte	29,0,2,35,0,0,27
	.byte	'Ifx_CPU_CORE_ID_Bits',0,15,96,3
	.word	39214
	.byte	10
	.byte	'_Ifx_CPU_CPR_L_Bits',0,15,99,16,4,11
	.byte	'reserved_0',0,4
	.word	520
	.byte	3,29,2,35,0,11
	.byte	'LOWBND',0,4
	.word	520
	.byte	29,0,2,35,0,0,27
	.byte	'Ifx_CPU_CPR_L_Bits',0,15,103,3
	.word	39312
	.byte	10
	.byte	'_Ifx_CPU_CPR_U_Bits',0,15,106,16,4,11
	.byte	'reserved_0',0,4
	.word	520
	.byte	3,29,2,35,0,11
	.byte	'UPPBND',0,4
	.word	520
	.byte	29,0,2,35,0,0,27
	.byte	'Ifx_CPU_CPR_U_Bits',0,15,110,3
	.word	39405
	.byte	10
	.byte	'_Ifx_CPU_CPU_ID_Bits',0,15,113,16,4,11
	.byte	'MODREV',0,4
	.word	520
	.byte	8,24,2,35,0,11
	.byte	'MOD_32B',0,4
	.word	520
	.byte	8,16,2,35,0,11
	.byte	'MOD',0,4
	.word	520
	.byte	16,0,2,35,0,0,27
	.byte	'Ifx_CPU_CPU_ID_Bits',0,15,118,3
	.word	39498
	.byte	10
	.byte	'_Ifx_CPU_CPXE_Bits',0,15,121,16,4,11
	.byte	'XE',0,4
	.word	520
	.byte	8,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	520
	.byte	24,0,2,35,0,0,27
	.byte	'Ifx_CPU_CPXE_Bits',0,15,125,3
	.word	39605
	.byte	10
	.byte	'_Ifx_CPU_CREVT_Bits',0,15,128,1,16,4,11
	.byte	'EVTA',0,4
	.word	520
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	520
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	520
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	520
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	520
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	520
	.byte	24,0,2,35,0,0,27
	.byte	'Ifx_CPU_CREVT_Bits',0,15,136,1,3
	.word	39692
	.byte	10
	.byte	'_Ifx_CPU_CUS_ID_Bits',0,15,139,1,16,4,11
	.byte	'CID',0,4
	.word	520
	.byte	3,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	520
	.byte	29,0,2,35,0,0,27
	.byte	'Ifx_CPU_CUS_ID_Bits',0,15,143,1,3
	.word	39846
	.byte	10
	.byte	'_Ifx_CPU_D_Bits',0,15,146,1,16,4,11
	.byte	'DATA',0,4
	.word	520
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_CPU_D_Bits',0,15,149,1,3
	.word	39940
	.byte	10
	.byte	'_Ifx_CPU_DATR_Bits',0,15,152,1,16,4,11
	.byte	'reserved_0',0,4
	.word	520
	.byte	3,29,2,35,0,11
	.byte	'SBE',0,4
	.word	520
	.byte	1,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	520
	.byte	5,23,2,35,0,11
	.byte	'CWE',0,4
	.word	520
	.byte	1,22,2,35,0,11
	.byte	'CFE',0,4
	.word	520
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	520
	.byte	3,18,2,35,0,11
	.byte	'SOE',0,4
	.word	520
	.byte	1,17,2,35,0,11
	.byte	'SME',0,4
	.word	520
	.byte	1,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	520
	.byte	16,0,2,35,0,0,27
	.byte	'Ifx_CPU_DATR_Bits',0,15,163,1,3
	.word	40003
	.byte	10
	.byte	'_Ifx_CPU_DBGSR_Bits',0,15,166,1,16,4,11
	.byte	'DE',0,4
	.word	520
	.byte	1,31,2,35,0,11
	.byte	'HALT',0,4
	.word	520
	.byte	2,29,2,35,0,11
	.byte	'SIH',0,4
	.word	520
	.byte	1,28,2,35,0,11
	.byte	'SUSP',0,4
	.word	520
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	520
	.byte	1,26,2,35,0,11
	.byte	'PREVSUSP',0,4
	.word	520
	.byte	1,25,2,35,0,11
	.byte	'PEVT',0,4
	.word	520
	.byte	1,24,2,35,0,11
	.byte	'EVTSRC',0,4
	.word	520
	.byte	5,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	520
	.byte	19,0,2,35,0,0,27
	.byte	'Ifx_CPU_DBGSR_Bits',0,15,177,1,3
	.word	40221
	.byte	10
	.byte	'_Ifx_CPU_DBGTCR_Bits',0,15,180,1,16,4,11
	.byte	'DTA',0,4
	.word	520
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	520
	.byte	31,0,2,35,0,0,27
	.byte	'Ifx_CPU_DBGTCR_Bits',0,15,184,1,3
	.word	40436
	.byte	10
	.byte	'_Ifx_CPU_DCON0_Bits',0,15,187,1,16,4,11
	.byte	'reserved_0',0,4
	.word	520
	.byte	1,31,2,35,0,11
	.byte	'DCBYP',0,4
	.word	520
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	520
	.byte	30,0,2,35,0,0,27
	.byte	'Ifx_CPU_DCON0_Bits',0,15,192,1,3
	.word	40530
	.byte	10
	.byte	'_Ifx_CPU_DCON2_Bits',0,15,195,1,16,4,11
	.byte	'DCACHE_SZE',0,4
	.word	520
	.byte	16,16,2,35,0,11
	.byte	'DSCRATCH_SZE',0,4
	.word	520
	.byte	16,0,2,35,0,0,27
	.byte	'Ifx_CPU_DCON2_Bits',0,15,199,1,3
	.word	40646
	.byte	10
	.byte	'_Ifx_CPU_DCX_Bits',0,15,202,1,16,4,11
	.byte	'reserved_0',0,4
	.word	520
	.byte	6,26,2,35,0,11
	.byte	'DCXValue',0,4
	.word	520
	.byte	26,0,2,35,0,0,27
	.byte	'Ifx_CPU_DCX_Bits',0,15,206,1,3
	.word	40747
	.byte	10
	.byte	'_Ifx_CPU_DEADD_Bits',0,15,209,1,16,4,11
	.byte	'ERROR_ADDRESS',0,4
	.word	520
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_CPU_DEADD_Bits',0,15,212,1,3
	.word	40840
	.byte	10
	.byte	'_Ifx_CPU_DIEAR_Bits',0,15,215,1,16,4,11
	.byte	'TA',0,4
	.word	520
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_CPU_DIEAR_Bits',0,15,218,1,3
	.word	40920
	.byte	10
	.byte	'_Ifx_CPU_DIETR_Bits',0,15,221,1,16,4,11
	.byte	'IED',0,4
	.word	520
	.byte	1,31,2,35,0,11
	.byte	'IE_T',0,4
	.word	520
	.byte	1,30,2,35,0,11
	.byte	'IE_C',0,4
	.word	520
	.byte	1,29,2,35,0,11
	.byte	'IE_S',0,4
	.word	520
	.byte	1,28,2,35,0,11
	.byte	'IE_BI',0,4
	.word	520
	.byte	1,27,2,35,0,11
	.byte	'E_INFO',0,4
	.word	520
	.byte	6,21,2,35,0,11
	.byte	'IE_DUAL',0,4
	.word	520
	.byte	1,20,2,35,0,11
	.byte	'IE_SP',0,4
	.word	520
	.byte	1,19,2,35,0,11
	.byte	'IE_BS',0,4
	.word	520
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	520
	.byte	18,0,2,35,0,0,27
	.byte	'Ifx_CPU_DIETR_Bits',0,15,233,1,3
	.word	40989
	.byte	10
	.byte	'_Ifx_CPU_DMS_Bits',0,15,236,1,16,4,11
	.byte	'reserved_0',0,4
	.word	520
	.byte	1,31,2,35,0,11
	.byte	'DMSValue',0,4
	.word	520
	.byte	31,0,2,35,0,0,27
	.byte	'Ifx_CPU_DMS_Bits',0,15,240,1,3
	.word	41218
	.byte	10
	.byte	'_Ifx_CPU_DPR_L_Bits',0,15,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	520
	.byte	3,29,2,35,0,11
	.byte	'LOWBND',0,4
	.word	520
	.byte	29,0,2,35,0,0,27
	.byte	'Ifx_CPU_DPR_L_Bits',0,15,247,1,3
	.word	41311
	.byte	10
	.byte	'_Ifx_CPU_DPR_U_Bits',0,15,250,1,16,4,11
	.byte	'reserved_0',0,4
	.word	520
	.byte	3,29,2,35,0,11
	.byte	'UPPBND',0,4
	.word	520
	.byte	29,0,2,35,0,0,27
	.byte	'Ifx_CPU_DPR_U_Bits',0,15,254,1,3
	.word	41406
	.byte	10
	.byte	'_Ifx_CPU_DPRE_Bits',0,15,129,2,16,4,11
	.byte	'RE',0,4
	.word	520
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	520
	.byte	16,0,2,35,0,0,27
	.byte	'Ifx_CPU_DPRE_Bits',0,15,133,2,3
	.word	41501
	.byte	10
	.byte	'_Ifx_CPU_DPWE_Bits',0,15,136,2,16,4,11
	.byte	'WE',0,4
	.word	520
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	520
	.byte	16,0,2,35,0,0,27
	.byte	'Ifx_CPU_DPWE_Bits',0,15,140,2,3
	.word	41591
	.byte	10
	.byte	'_Ifx_CPU_DSTR_Bits',0,15,143,2,16,4,11
	.byte	'SRE',0,4
	.word	520
	.byte	1,31,2,35,0,11
	.byte	'GAE',0,4
	.word	520
	.byte	1,30,2,35,0,11
	.byte	'LBE',0,4
	.word	520
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	520
	.byte	3,26,2,35,0,11
	.byte	'CRE',0,4
	.word	520
	.byte	1,25,2,35,0,11
	.byte	'reserved_7',0,4
	.word	520
	.byte	7,18,2,35,0,11
	.byte	'DTME',0,4
	.word	520
	.byte	1,17,2,35,0,11
	.byte	'LOE',0,4
	.word	520
	.byte	1,16,2,35,0,11
	.byte	'SDE',0,4
	.word	520
	.byte	1,15,2,35,0,11
	.byte	'SCE',0,4
	.word	520
	.byte	1,14,2,35,0,11
	.byte	'CAC',0,4
	.word	520
	.byte	1,13,2,35,0,11
	.byte	'MPE',0,4
	.word	520
	.byte	1,12,2,35,0,11
	.byte	'CLE',0,4
	.word	520
	.byte	1,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	520
	.byte	3,8,2,35,0,11
	.byte	'ALN',0,4
	.word	520
	.byte	1,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	520
	.byte	7,0,2,35,0,0,27
	.byte	'Ifx_CPU_DSTR_Bits',0,15,161,2,3
	.word	41681
	.byte	10
	.byte	'_Ifx_CPU_EXEVT_Bits',0,15,164,2,16,4,11
	.byte	'EVTA',0,4
	.word	520
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	520
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	520
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	520
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	520
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	520
	.byte	24,0,2,35,0,0,27
	.byte	'Ifx_CPU_EXEVT_Bits',0,15,172,2,3
	.word	42005
	.byte	10
	.byte	'_Ifx_CPU_FCX_Bits',0,15,175,2,16,4,11
	.byte	'FCXO',0,4
	.word	520
	.byte	16,16,2,35,0,11
	.byte	'FCXS',0,4
	.word	520
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	520
	.byte	12,0,2,35,0,0,27
	.byte	'Ifx_CPU_FCX_Bits',0,15,180,2,3
	.word	42159
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_CON_Bits',0,15,183,2,16,4,11
	.byte	'TST',0,4
	.word	520
	.byte	1,31,2,35,0,11
	.byte	'TCL',0,4
	.word	520
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	520
	.byte	6,24,2,35,0,11
	.byte	'RM',0,4
	.word	520
	.byte	2,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	520
	.byte	8,14,2,35,0,11
	.byte	'FXE',0,4
	.word	520
	.byte	1,13,2,35,0,11
	.byte	'FUE',0,4
	.word	520
	.byte	1,12,2,35,0,11
	.byte	'FZE',0,4
	.word	520
	.byte	1,11,2,35,0,11
	.byte	'FVE',0,4
	.word	520
	.byte	1,10,2,35,0,11
	.byte	'FIE',0,4
	.word	520
	.byte	1,9,2,35,0,11
	.byte	'reserved_23',0,4
	.word	520
	.byte	3,6,2,35,0,11
	.byte	'FX',0,4
	.word	520
	.byte	1,5,2,35,0,11
	.byte	'FU',0,4
	.word	520
	.byte	1,4,2,35,0,11
	.byte	'FZ',0,4
	.word	520
	.byte	1,3,2,35,0,11
	.byte	'FV',0,4
	.word	520
	.byte	1,2,2,35,0,11
	.byte	'FI',0,4
	.word	520
	.byte	1,1,2,35,0,11
	.byte	'reserved_31',0,4
	.word	520
	.byte	1,0,2,35,0,0,27
	.byte	'Ifx_CPU_FPU_TRAP_CON_Bits',0,15,202,2,3
	.word	42265
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_OPC_Bits',0,15,205,2,16,4,11
	.byte	'OPC',0,4
	.word	520
	.byte	8,24,2,35,0,11
	.byte	'FMT',0,4
	.word	520
	.byte	1,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	520
	.byte	7,16,2,35,0,11
	.byte	'DREG',0,4
	.word	520
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	520
	.byte	12,0,2,35,0,0,27
	.byte	'Ifx_CPU_FPU_TRAP_OPC_Bits',0,15,212,2,3
	.word	42614
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_PC_Bits',0,15,215,2,16,4,11
	.byte	'PC',0,4
	.word	520
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_CPU_FPU_TRAP_PC_Bits',0,15,218,2,3
	.word	42774
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC1_Bits',0,15,221,2,16,4,11
	.byte	'SRC1',0,4
	.word	520
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_CPU_FPU_TRAP_SRC1_Bits',0,15,224,2,3
	.word	42855
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC2_Bits',0,15,227,2,16,4,11
	.byte	'SRC2',0,4
	.word	520
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_CPU_FPU_TRAP_SRC2_Bits',0,15,230,2,3
	.word	42942
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC3_Bits',0,15,233,2,16,4,11
	.byte	'SRC3',0,4
	.word	520
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_CPU_FPU_TRAP_SRC3_Bits',0,15,236,2,3
	.word	43029
	.byte	10
	.byte	'_Ifx_CPU_ICNT_Bits',0,15,239,2,16,4,11
	.byte	'CountValue',0,4
	.word	520
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	520
	.byte	1,0,2,35,0,0,27
	.byte	'Ifx_CPU_ICNT_Bits',0,15,243,2,3
	.word	43116
	.byte	10
	.byte	'_Ifx_CPU_ICR_Bits',0,15,246,2,16,4,11
	.byte	'CCPN',0,4
	.word	520
	.byte	10,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	520
	.byte	5,17,2,35,0,11
	.byte	'IE',0,4
	.word	520
	.byte	1,16,2,35,0,11
	.byte	'PIPN',0,4
	.word	520
	.byte	10,6,2,35,0,11
	.byte	'reserved_26',0,4
	.word	520
	.byte	6,0,2,35,0,0,27
	.byte	'Ifx_CPU_ICR_Bits',0,15,253,2,3
	.word	43207
	.byte	10
	.byte	'_Ifx_CPU_ISP_Bits',0,15,128,3,16,4,11
	.byte	'ISP',0,4
	.word	520
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_CPU_ISP_Bits',0,15,131,3,3
	.word	43350
	.byte	10
	.byte	'_Ifx_CPU_LCX_Bits',0,15,134,3,16,4,11
	.byte	'LCXO',0,4
	.word	520
	.byte	16,16,2,35,0,11
	.byte	'LCXS',0,4
	.word	520
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	520
	.byte	12,0,2,35,0,0,27
	.byte	'Ifx_CPU_LCX_Bits',0,15,139,3,3
	.word	43416
	.byte	10
	.byte	'_Ifx_CPU_M1CNT_Bits',0,15,142,3,16,4,11
	.byte	'CountValue',0,4
	.word	520
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	520
	.byte	1,0,2,35,0,0,27
	.byte	'Ifx_CPU_M1CNT_Bits',0,15,146,3,3
	.word	43522
	.byte	10
	.byte	'_Ifx_CPU_M2CNT_Bits',0,15,149,3,16,4,11
	.byte	'CountValue',0,4
	.word	520
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	520
	.byte	1,0,2,35,0,0,27
	.byte	'Ifx_CPU_M2CNT_Bits',0,15,153,3,3
	.word	43615
	.byte	10
	.byte	'_Ifx_CPU_M3CNT_Bits',0,15,156,3,16,4,11
	.byte	'CountValue',0,4
	.word	520
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	520
	.byte	1,0,2,35,0,0,27
	.byte	'Ifx_CPU_M3CNT_Bits',0,15,160,3,3
	.word	43708
	.byte	10
	.byte	'_Ifx_CPU_PC_Bits',0,15,163,3,16,4,11
	.byte	'reserved_0',0,4
	.word	520
	.byte	1,31,2,35,0,11
	.byte	'PC',0,4
	.word	520
	.byte	31,0,2,35,0,0,27
	.byte	'Ifx_CPU_PC_Bits',0,15,167,3,3
	.word	43801
	.byte	10
	.byte	'_Ifx_CPU_PCON0_Bits',0,15,170,3,16,4,11
	.byte	'reserved_0',0,4
	.word	520
	.byte	1,31,2,35,0,11
	.byte	'PCBYP',0,4
	.word	520
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	520
	.byte	30,0,2,35,0,0,27
	.byte	'Ifx_CPU_PCON0_Bits',0,15,175,3,3
	.word	43886
	.byte	10
	.byte	'_Ifx_CPU_PCON1_Bits',0,15,178,3,16,4,11
	.byte	'PCINV',0,4
	.word	520
	.byte	1,31,2,35,0,11
	.byte	'PBINV',0,4
	.word	520
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	520
	.byte	30,0,2,35,0,0,27
	.byte	'Ifx_CPU_PCON1_Bits',0,15,183,3,3
	.word	44002
	.byte	10
	.byte	'_Ifx_CPU_PCON2_Bits',0,15,186,3,16,4,11
	.byte	'PCACHE_SZE',0,4
	.word	520
	.byte	16,16,2,35,0,11
	.byte	'PSCRATCH_SZE',0,4
	.word	520
	.byte	16,0,2,35,0,0,27
	.byte	'Ifx_CPU_PCON2_Bits',0,15,190,3,3
	.word	44113
	.byte	10
	.byte	'_Ifx_CPU_PCXI_Bits',0,15,193,3,16,4,11
	.byte	'PCXO',0,4
	.word	520
	.byte	16,16,2,35,0,11
	.byte	'PCXS',0,4
	.word	520
	.byte	4,12,2,35,0,11
	.byte	'UL',0,4
	.word	520
	.byte	1,11,2,35,0,11
	.byte	'PIE',0,4
	.word	520
	.byte	1,10,2,35,0,11
	.byte	'PCPN',0,4
	.word	520
	.byte	10,0,2,35,0,0,27
	.byte	'Ifx_CPU_PCXI_Bits',0,15,200,3,3
	.word	44214
	.byte	10
	.byte	'_Ifx_CPU_PIEAR_Bits',0,15,203,3,16,4,11
	.byte	'TA',0,4
	.word	520
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_CPU_PIEAR_Bits',0,15,206,3,3
	.word	44344
	.byte	10
	.byte	'_Ifx_CPU_PIETR_Bits',0,15,209,3,16,4,11
	.byte	'IED',0,4
	.word	520
	.byte	1,31,2,35,0,11
	.byte	'IE_T',0,4
	.word	520
	.byte	1,30,2,35,0,11
	.byte	'IE_C',0,4
	.word	520
	.byte	1,29,2,35,0,11
	.byte	'IE_S',0,4
	.word	520
	.byte	1,28,2,35,0,11
	.byte	'IE_BI',0,4
	.word	520
	.byte	1,27,2,35,0,11
	.byte	'E_INFO',0,4
	.word	520
	.byte	6,21,2,35,0,11
	.byte	'IE_DUAL',0,4
	.word	520
	.byte	1,20,2,35,0,11
	.byte	'IE_SP',0,4
	.word	520
	.byte	1,19,2,35,0,11
	.byte	'IE_BS',0,4
	.word	520
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	520
	.byte	18,0,2,35,0,0,27
	.byte	'Ifx_CPU_PIETR_Bits',0,15,221,3,3
	.word	44413
	.byte	10
	.byte	'_Ifx_CPU_PMA0_Bits',0,15,224,3,16,4,11
	.byte	'reserved_0',0,4
	.word	520
	.byte	13,19,2,35,0,11
	.byte	'DAC',0,4
	.word	520
	.byte	3,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	520
	.byte	16,0,2,35,0,0,27
	.byte	'Ifx_CPU_PMA0_Bits',0,15,229,3,3
	.word	44642
	.byte	10
	.byte	'_Ifx_CPU_PMA1_Bits',0,15,232,3,16,4,11
	.byte	'reserved_0',0,4
	.word	520
	.byte	14,18,2,35,0,11
	.byte	'CAC',0,4
	.word	520
	.byte	2,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	520
	.byte	16,0,2,35,0,0,27
	.byte	'Ifx_CPU_PMA1_Bits',0,15,237,3,3
	.word	44755
	.byte	10
	.byte	'_Ifx_CPU_PMA2_Bits',0,15,240,3,16,4,11
	.byte	'PSI',0,4
	.word	520
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	520
	.byte	16,0,2,35,0,0,27
	.byte	'Ifx_CPU_PMA2_Bits',0,15,244,3,3
	.word	44868
	.byte	10
	.byte	'_Ifx_CPU_PSTR_Bits',0,15,247,3,16,4,11
	.byte	'FRE',0,4
	.word	520
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	520
	.byte	1,30,2,35,0,11
	.byte	'FBE',0,4
	.word	520
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	520
	.byte	9,20,2,35,0,11
	.byte	'FPE',0,4
	.word	520
	.byte	1,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	520
	.byte	1,18,2,35,0,11
	.byte	'FME',0,4
	.word	520
	.byte	1,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	520
	.byte	17,0,2,35,0,0,27
	.byte	'Ifx_CPU_PSTR_Bits',0,15,129,4,3
	.word	44959
	.byte	10
	.byte	'_Ifx_CPU_PSW_Bits',0,15,132,4,16,4,11
	.byte	'CDC',0,4
	.word	520
	.byte	7,25,2,35,0,11
	.byte	'CDE',0,4
	.word	520
	.byte	1,24,2,35,0,11
	.byte	'GW',0,4
	.word	520
	.byte	1,23,2,35,0,11
	.byte	'IS',0,4
	.word	520
	.byte	1,22,2,35,0,11
	.byte	'IO',0,4
	.word	520
	.byte	2,20,2,35,0,11
	.byte	'PRS',0,4
	.word	520
	.byte	2,18,2,35,0,11
	.byte	'S',0,4
	.word	520
	.byte	1,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	520
	.byte	12,5,2,35,0,11
	.byte	'SAV',0,4
	.word	520
	.byte	1,4,2,35,0,11
	.byte	'AV',0,4
	.word	520
	.byte	1,3,2,35,0,11
	.byte	'SV',0,4
	.word	520
	.byte	1,2,2,35,0,11
	.byte	'V',0,4
	.word	520
	.byte	1,1,2,35,0,11
	.byte	'C',0,4
	.word	520
	.byte	1,0,2,35,0,0,27
	.byte	'Ifx_CPU_PSW_Bits',0,15,147,4,3
	.word	45162
	.byte	10
	.byte	'_Ifx_CPU_SEGEN_Bits',0,15,150,4,16,4,11
	.byte	'ADFLIP',0,4
	.word	520
	.byte	8,24,2,35,0,11
	.byte	'ADTYPE',0,4
	.word	520
	.byte	2,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	520
	.byte	21,1,2,35,0,11
	.byte	'AE',0,4
	.word	520
	.byte	1,0,2,35,0,0,27
	.byte	'Ifx_CPU_SEGEN_Bits',0,15,156,4,3
	.word	45405
	.byte	10
	.byte	'_Ifx_CPU_SMACON_Bits',0,15,159,4,16,4,11
	.byte	'PC',0,4
	.word	520
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	520
	.byte	1,30,2,35,0,11
	.byte	'PT',0,4
	.word	520
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	520
	.byte	5,24,2,35,0,11
	.byte	'DC',0,4
	.word	520
	.byte	1,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	520
	.byte	1,22,2,35,0,11
	.byte	'DT',0,4
	.word	520
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	520
	.byte	13,8,2,35,0,11
	.byte	'IODT',0,4
	.word	520
	.byte	1,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	520
	.byte	7,0,2,35,0,0,27
	.byte	'Ifx_CPU_SMACON_Bits',0,15,171,4,3
	.word	45533
	.byte	10
	.byte	'_Ifx_CPU_SPROT_ACCENA_Bits',0,15,174,4,16,4,11
	.byte	'EN',0,4
	.word	497
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_CPU_SPROT_ACCENA_Bits',0,15,177,4,3
	.word	45774
	.byte	10
	.byte	'_Ifx_CPU_SPROT_ACCENB_Bits',0,15,180,4,16,4,11
	.byte	'reserved_0',0,4
	.word	497
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_CPU_SPROT_ACCENB_Bits',0,15,183,4,3
	.word	45857
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,15,186,4,16,4,11
	.byte	'EN',0,4
	.word	497
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,15,189,4,3
	.word	45948
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,15,192,4,16,4,11
	.byte	'reserved_0',0,4
	.word	497
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,15,195,4,3
	.word	46039
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_LA_Bits',0,15,198,4,16,4,11
	.byte	'reserved_0',0,1
	.word	672
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	497
	.byte	27,0,2,35,0,0,27
	.byte	'Ifx_CPU_SPROT_RGN_LA_Bits',0,15,202,4,3
	.word	46138
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_UA_Bits',0,15,205,4,16,4,11
	.byte	'reserved_0',0,1
	.word	672
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	497
	.byte	27,0,2,35,0,0,27
	.byte	'Ifx_CPU_SPROT_RGN_UA_Bits',0,15,209,4,3
	.word	46245
	.byte	10
	.byte	'_Ifx_CPU_SWEVT_Bits',0,15,212,4,16,4,11
	.byte	'EVTA',0,4
	.word	520
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	520
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	520
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	520
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	520
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	520
	.byte	24,0,2,35,0,0,27
	.byte	'Ifx_CPU_SWEVT_Bits',0,15,220,4,3
	.word	46352
	.byte	10
	.byte	'_Ifx_CPU_SYSCON_Bits',0,15,223,4,16,4,11
	.byte	'FCDSF',0,4
	.word	520
	.byte	1,31,2,35,0,11
	.byte	'PROTEN',0,4
	.word	520
	.byte	1,30,2,35,0,11
	.byte	'TPROTEN',0,4
	.word	520
	.byte	1,29,2,35,0,11
	.byte	'IS',0,4
	.word	520
	.byte	1,28,2,35,0,11
	.byte	'IT',0,4
	.word	520
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	520
	.byte	27,0,2,35,0,0,27
	.byte	'Ifx_CPU_SYSCON_Bits',0,15,231,4,3
	.word	46506
	.byte	10
	.byte	'_Ifx_CPU_TASK_ASI_Bits',0,15,234,4,16,4,11
	.byte	'ASI',0,4
	.word	520
	.byte	5,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	520
	.byte	27,0,2,35,0,0,27
	.byte	'Ifx_CPU_TASK_ASI_Bits',0,15,238,4,3
	.word	46667
	.byte	10
	.byte	'_Ifx_CPU_TPS_CON_Bits',0,15,241,4,16,4,11
	.byte	'TEXP0',0,4
	.word	520
	.byte	1,31,2,35,0,11
	.byte	'TEXP1',0,4
	.word	520
	.byte	1,30,2,35,0,11
	.byte	'TEXP2',0,4
	.word	520
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	520
	.byte	13,16,2,35,0,11
	.byte	'TTRAP',0,4
	.word	520
	.byte	1,15,2,35,0,11
	.byte	'reserved_17',0,4
	.word	520
	.byte	15,0,2,35,0,0,27
	.byte	'Ifx_CPU_TPS_CON_Bits',0,15,249,4,3
	.word	46765
	.byte	10
	.byte	'_Ifx_CPU_TPS_TIMER_Bits',0,15,252,4,16,4,11
	.byte	'Timer',0,4
	.word	520
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_CPU_TPS_TIMER_Bits',0,15,255,4,3
	.word	46937
	.byte	10
	.byte	'_Ifx_CPU_TR_ADR_Bits',0,15,130,5,16,4,11
	.byte	'ADDR',0,4
	.word	520
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_CPU_TR_ADR_Bits',0,15,133,5,3
	.word	47017
	.byte	10
	.byte	'_Ifx_CPU_TR_EVT_Bits',0,15,136,5,16,4,11
	.byte	'EVTA',0,4
	.word	520
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	520
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	520
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	520
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	520
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	520
	.byte	4,20,2,35,0,11
	.byte	'TYP',0,4
	.word	520
	.byte	1,19,2,35,0,11
	.byte	'RNG',0,4
	.word	520
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	520
	.byte	1,17,2,35,0,11
	.byte	'ASI_EN',0,4
	.word	520
	.byte	1,16,2,35,0,11
	.byte	'ASI',0,4
	.word	520
	.byte	5,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	520
	.byte	6,5,2,35,0,11
	.byte	'AST',0,4
	.word	520
	.byte	1,4,2,35,0,11
	.byte	'ALD',0,4
	.word	520
	.byte	1,3,2,35,0,11
	.byte	'reserved_29',0,4
	.word	520
	.byte	3,0,2,35,0,0,27
	.byte	'Ifx_CPU_TR_EVT_Bits',0,15,153,5,3
	.word	47090
	.byte	10
	.byte	'_Ifx_CPU_TRIG_ACC_Bits',0,15,156,5,16,4,11
	.byte	'T0',0,4
	.word	520
	.byte	1,31,2,35,0,11
	.byte	'T1',0,4
	.word	520
	.byte	1,30,2,35,0,11
	.byte	'T2',0,4
	.word	520
	.byte	1,29,2,35,0,11
	.byte	'T3',0,4
	.word	520
	.byte	1,28,2,35,0,11
	.byte	'T4',0,4
	.word	520
	.byte	1,27,2,35,0,11
	.byte	'T5',0,4
	.word	520
	.byte	1,26,2,35,0,11
	.byte	'T6',0,4
	.word	520
	.byte	1,25,2,35,0,11
	.byte	'T7',0,4
	.word	520
	.byte	1,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	520
	.byte	24,0,2,35,0,0,27
	.byte	'Ifx_CPU_TRIG_ACC_Bits',0,15,167,5,3
	.word	47408
	.byte	12,15,175,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38626
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_A',0,15,180,5,3
	.word	47603
	.byte	12,15,183,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38687
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_BIV',0,15,188,5,3
	.word	47662
	.byte	12,15,191,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38766
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_BTV',0,15,196,5,3
	.word	47723
	.byte	12,15,199,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38852
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_CCNT',0,15,204,5,3
	.word	47784
	.byte	12,15,207,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38941
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_CCTRL',0,15,212,5,3
	.word	47846
	.byte	12,15,215,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39087
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_COMPAT',0,15,220,5,3
	.word	47909
	.byte	12,15,223,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39214
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_CORE_ID',0,15,228,5,3
	.word	47973
	.byte	12,15,231,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39312
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_CPR_L',0,15,236,5,3
	.word	48038
	.byte	12,15,239,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39405
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_CPR_U',0,15,244,5,3
	.word	48101
	.byte	12,15,247,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39498
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_CPU_ID',0,15,252,5,3
	.word	48164
	.byte	12,15,255,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39605
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_CPXE',0,15,132,6,3
	.word	48228
	.byte	12,15,135,6,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39692
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_CREVT',0,15,140,6,3
	.word	48290
	.byte	12,15,143,6,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39846
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_CUS_ID',0,15,148,6,3
	.word	48353
	.byte	12,15,151,6,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39940
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_D',0,15,156,6,3
	.word	48417
	.byte	12,15,159,6,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40003
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_DATR',0,15,164,6,3
	.word	48476
	.byte	12,15,167,6,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40221
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_DBGSR',0,15,172,6,3
	.word	48538
	.byte	12,15,175,6,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40436
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_DBGTCR',0,15,180,6,3
	.word	48601
	.byte	12,15,183,6,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40530
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_DCON0',0,15,188,6,3
	.word	48665
	.byte	12,15,191,6,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40646
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_DCON2',0,15,196,6,3
	.word	48728
	.byte	12,15,199,6,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40747
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_DCX',0,15,204,6,3
	.word	48791
	.byte	12,15,207,6,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40840
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_DEADD',0,15,212,6,3
	.word	48852
	.byte	12,15,215,6,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40920
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_DIEAR',0,15,220,6,3
	.word	48915
	.byte	12,15,223,6,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40989
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_DIETR',0,15,228,6,3
	.word	48978
	.byte	12,15,231,6,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41218
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_DMS',0,15,236,6,3
	.word	49041
	.byte	12,15,239,6,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41311
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_DPR_L',0,15,244,6,3
	.word	49102
	.byte	12,15,247,6,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41406
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_DPR_U',0,15,252,6,3
	.word	49165
	.byte	12,15,255,6,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41501
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_DPRE',0,15,132,7,3
	.word	49228
	.byte	12,15,135,7,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41591
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_DPWE',0,15,140,7,3
	.word	49290
	.byte	12,15,143,7,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41681
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_DSTR',0,15,148,7,3
	.word	49352
	.byte	12,15,151,7,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42005
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_EXEVT',0,15,156,7,3
	.word	49414
	.byte	12,15,159,7,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42159
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_FCX',0,15,164,7,3
	.word	49477
	.byte	12,15,167,7,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42265
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_FPU_TRAP_CON',0,15,172,7,3
	.word	49538
	.byte	12,15,175,7,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42614
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_FPU_TRAP_OPC',0,15,180,7,3
	.word	49608
	.byte	12,15,183,7,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42774
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_FPU_TRAP_PC',0,15,188,7,3
	.word	49678
	.byte	12,15,191,7,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42855
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_FPU_TRAP_SRC1',0,15,196,7,3
	.word	49747
	.byte	12,15,199,7,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42942
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_FPU_TRAP_SRC2',0,15,204,7,3
	.word	49818
	.byte	12,15,207,7,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43029
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_FPU_TRAP_SRC3',0,15,212,7,3
	.word	49889
	.byte	12,15,215,7,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43116
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_ICNT',0,15,220,7,3
	.word	49960
	.byte	12,15,223,7,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43207
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_ICR',0,15,228,7,3
	.word	50022
	.byte	12,15,231,7,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43350
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_ISP',0,15,236,7,3
	.word	50083
	.byte	12,15,239,7,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43416
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_LCX',0,15,244,7,3
	.word	50144
	.byte	12,15,247,7,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43522
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_M1CNT',0,15,252,7,3
	.word	50205
	.byte	12,15,255,7,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43615
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_M2CNT',0,15,132,8,3
	.word	50268
	.byte	12,15,135,8,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43708
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_M3CNT',0,15,140,8,3
	.word	50331
	.byte	12,15,143,8,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43801
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_PC',0,15,148,8,3
	.word	50394
	.byte	12,15,151,8,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43886
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_PCON0',0,15,156,8,3
	.word	50454
	.byte	12,15,159,8,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44002
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_PCON1',0,15,164,8,3
	.word	50517
	.byte	12,15,167,8,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44113
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_PCON2',0,15,172,8,3
	.word	50580
	.byte	12,15,175,8,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44214
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_PCXI',0,15,180,8,3
	.word	50643
	.byte	12,15,183,8,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44344
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_PIEAR',0,15,188,8,3
	.word	50705
	.byte	12,15,191,8,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44413
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_PIETR',0,15,196,8,3
	.word	50768
	.byte	12,15,199,8,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44642
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_PMA0',0,15,204,8,3
	.word	50831
	.byte	12,15,207,8,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44755
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_PMA1',0,15,212,8,3
	.word	50893
	.byte	12,15,215,8,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44868
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_PMA2',0,15,220,8,3
	.word	50955
	.byte	12,15,223,8,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44959
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_PSTR',0,15,228,8,3
	.word	51017
	.byte	12,15,231,8,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45162
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_PSW',0,15,236,8,3
	.word	51079
	.byte	12,15,239,8,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45405
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_SEGEN',0,15,244,8,3
	.word	51140
	.byte	12,15,247,8,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45533
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_SMACON',0,15,252,8,3
	.word	51203
	.byte	12,15,255,8,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45774
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_SPROT_ACCENA',0,15,132,9,3
	.word	51267
	.byte	12,15,135,9,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45857
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_SPROT_ACCENB',0,15,140,9,3
	.word	51337
	.byte	12,15,143,9,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45948
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA',0,15,148,9,3
	.word	51407
	.byte	12,15,151,9,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46039
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB',0,15,156,9,3
	.word	51481
	.byte	12,15,159,9,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46138
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_SPROT_RGN_LA',0,15,164,9,3
	.word	51555
	.byte	12,15,167,9,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46245
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_SPROT_RGN_UA',0,15,172,9,3
	.word	51625
	.byte	12,15,175,9,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46352
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_SWEVT',0,15,180,9,3
	.word	51695
	.byte	12,15,183,9,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46506
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_SYSCON',0,15,188,9,3
	.word	51758
	.byte	12,15,191,9,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46667
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_TASK_ASI',0,15,196,9,3
	.word	51822
	.byte	12,15,199,9,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46765
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_TPS_CON',0,15,204,9,3
	.word	51888
	.byte	12,15,207,9,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46937
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_TPS_TIMER',0,15,212,9,3
	.word	51953
	.byte	12,15,215,9,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47017
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_TR_ADR',0,15,220,9,3
	.word	52020
	.byte	12,15,223,9,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47090
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_TR_EVT',0,15,228,9,3
	.word	52084
	.byte	12,15,231,9,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47408
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_TRIG_ACC',0,15,236,9,3
	.word	52148
	.byte	10
	.byte	'_Ifx_CPU_CPR',0,15,247,9,25,8,13
	.byte	'L',0
	.word	48038
	.byte	4,2,35,0,13
	.byte	'U',0
	.word	48101
	.byte	4,2,35,4,0,14
	.word	52214
	.byte	27
	.byte	'Ifx_CPU_CPR',0,15,251,9,3
	.word	52256
	.byte	10
	.byte	'_Ifx_CPU_DPR',0,15,254,9,25,8,13
	.byte	'L',0
	.word	49102
	.byte	4,2,35,0,13
	.byte	'U',0
	.word	49165
	.byte	4,2,35,4,0,14
	.word	52282
	.byte	27
	.byte	'Ifx_CPU_DPR',0,15,130,10,3
	.word	52324
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN',0,15,133,10,25,16,13
	.byte	'LA',0
	.word	51555
	.byte	4,2,35,0,13
	.byte	'UA',0
	.word	51625
	.byte	4,2,35,4,13
	.byte	'ACCENA',0
	.word	51407
	.byte	4,2,35,8,13
	.byte	'ACCENB',0
	.word	51481
	.byte	4,2,35,12,0,14
	.word	52350
	.byte	27
	.byte	'Ifx_CPU_SPROT_RGN',0,15,139,10,3
	.word	52432
	.byte	18,12
	.word	51953
	.byte	19,2,0,10
	.byte	'_Ifx_CPU_TPS',0,15,142,10,25,16,13
	.byte	'CON',0
	.word	51888
	.byte	4,2,35,0,13
	.byte	'TIMER',0
	.word	52464
	.byte	12,2,35,4,0,14
	.word	52473
	.byte	27
	.byte	'Ifx_CPU_TPS',0,15,146,10,3
	.word	52521
	.byte	10
	.byte	'_Ifx_CPU_TR',0,15,149,10,25,8,13
	.byte	'EVT',0
	.word	52084
	.byte	4,2,35,0,13
	.byte	'ADR',0
	.word	52020
	.byte	4,2,35,4,0,14
	.word	52547
	.byte	27
	.byte	'Ifx_CPU_TR',0,15,153,10,3
	.word	52592
	.byte	18,176,32
	.word	672
	.byte	19,175,32,0,18,208,223,1
	.word	672
	.byte	19,207,223,1,0,18,248,1
	.word	672
	.byte	19,247,1,0,18,244,29
	.word	672
	.byte	19,243,29,0,18,188,3
	.word	672
	.byte	19,187,3,0,18,232,3
	.word	672
	.byte	19,231,3,0,18,252,23
	.word	672
	.byte	19,251,23,0,18,228,63
	.word	672
	.byte	19,227,63,0,18,128,1
	.word	52282
	.byte	19,15,0,14
	.word	52707
	.byte	18,128,31
	.word	672
	.byte	19,255,30,0,18,64
	.word	52214
	.byte	19,7,0,14
	.word	52733
	.byte	18,192,31
	.word	672
	.byte	19,191,31,0,18,16
	.word	48228
	.byte	19,3,0,18,16
	.word	49228
	.byte	19,3,0,18,16
	.word	49290
	.byte	19,3,0,18,208,7
	.word	672
	.byte	19,207,7,0,14
	.word	52473
	.byte	18,240,23
	.word	672
	.byte	19,239,23,0,18,64
	.word	52547
	.byte	19,7,0,14
	.word	52812
	.byte	18,192,23
	.word	672
	.byte	19,191,23,0,18,232,1
	.word	672
	.byte	19,231,1,0,18,180,1
	.word	672
	.byte	19,179,1,0,18,172,1
	.word	672
	.byte	19,171,1,0,18,64
	.word	48417
	.byte	19,15,0,18,64
	.word	672
	.byte	19,63,0,18,64
	.word	47603
	.byte	19,15,0,10
	.byte	'_Ifx_CPU',0,15,166,10,25,128,128,4,13
	.byte	'reserved_0',0
	.word	52617
	.byte	176,32,2,35,0,13
	.byte	'SEGEN',0
	.word	51140
	.byte	4,3,35,176,32,13
	.byte	'reserved_1034',0
	.word	52628
	.byte	208,223,1,3,35,180,32,13
	.byte	'TASK_ASI',0
	.word	51822
	.byte	4,4,35,132,128,2,13
	.byte	'reserved_8008',0
	.word	52641
	.byte	248,1,4,35,136,128,2,13
	.byte	'PMA0',0
	.word	50831
	.byte	4,4,35,128,130,2,13
	.byte	'PMA1',0
	.word	50893
	.byte	4,4,35,132,130,2,13
	.byte	'PMA2',0
	.word	50955
	.byte	4,4,35,136,130,2,13
	.byte	'reserved_810C',0
	.word	52652
	.byte	244,29,4,35,140,130,2,13
	.byte	'DCON2',0
	.word	48728
	.byte	4,4,35,128,160,2,13
	.byte	'reserved_9004',0
	.word	4695
	.byte	8,4,35,132,160,2,13
	.byte	'SMACON',0
	.word	51203
	.byte	4,4,35,140,160,2,13
	.byte	'DSTR',0
	.word	49352
	.byte	4,4,35,144,160,2,13
	.byte	'reserved_9014',0
	.word	2876
	.byte	4,4,35,148,160,2,13
	.byte	'DATR',0
	.word	48476
	.byte	4,4,35,152,160,2,13
	.byte	'DEADD',0
	.word	48852
	.byte	4,4,35,156,160,2,13
	.byte	'DIEAR',0
	.word	48915
	.byte	4,4,35,160,160,2,13
	.byte	'DIETR',0
	.word	48978
	.byte	4,4,35,164,160,2,13
	.byte	'reserved_9028',0
	.word	4066
	.byte	24,4,35,168,160,2,13
	.byte	'DCON0',0
	.word	48665
	.byte	4,4,35,192,160,2,13
	.byte	'reserved_9044',0
	.word	52663
	.byte	188,3,4,35,196,160,2,13
	.byte	'PSTR',0
	.word	51017
	.byte	4,4,35,128,164,2,13
	.byte	'PCON1',0
	.word	50517
	.byte	4,4,35,132,164,2,13
	.byte	'PCON2',0
	.word	50580
	.byte	4,4,35,136,164,2,13
	.byte	'PCON0',0
	.word	50454
	.byte	4,4,35,140,164,2,13
	.byte	'PIEAR',0
	.word	50705
	.byte	4,4,35,144,164,2,13
	.byte	'PIETR',0
	.word	50768
	.byte	4,4,35,148,164,2,13
	.byte	'reserved_9218',0
	.word	52674
	.byte	232,3,4,35,152,164,2,13
	.byte	'COMPAT',0
	.word	47909
	.byte	4,4,35,128,168,2,13
	.byte	'reserved_9404',0
	.word	52685
	.byte	252,23,4,35,132,168,2,13
	.byte	'FPU_TRAP_CON',0
	.word	49538
	.byte	4,4,35,128,192,2,13
	.byte	'FPU_TRAP_PC',0
	.word	49678
	.byte	4,4,35,132,192,2,13
	.byte	'FPU_TRAP_OPC',0
	.word	49608
	.byte	4,4,35,136,192,2,13
	.byte	'reserved_A00C',0
	.word	2876
	.byte	4,4,35,140,192,2,13
	.byte	'FPU_TRAP_SRC1',0
	.word	49747
	.byte	4,4,35,144,192,2,13
	.byte	'FPU_TRAP_SRC2',0
	.word	49818
	.byte	4,4,35,148,192,2,13
	.byte	'FPU_TRAP_SRC3',0
	.word	49889
	.byte	4,4,35,152,192,2,13
	.byte	'reserved_A01C',0
	.word	52696
	.byte	228,63,4,35,156,192,2,13
	.byte	'DPR',0
	.word	52717
	.byte	128,1,4,35,128,128,3,13
	.byte	'reserved_C080',0
	.word	52722
	.byte	128,31,4,35,128,129,3,13
	.byte	'CPR',0
	.word	52742
	.byte	64,4,35,128,160,3,13
	.byte	'reserved_D040',0
	.word	52747
	.byte	192,31,4,35,192,160,3,13
	.byte	'CPXE',0
	.word	52758
	.byte	16,4,35,128,192,3,13
	.byte	'DPRE',0
	.word	52767
	.byte	16,4,35,144,192,3,13
	.byte	'DPWE',0
	.word	52776
	.byte	16,4,35,160,192,3,13
	.byte	'reserved_E030',0
	.word	52785
	.byte	208,7,4,35,176,192,3,13
	.byte	'TPS',0
	.word	52796
	.byte	16,4,35,128,200,3,13
	.byte	'reserved_E410',0
	.word	52801
	.byte	240,23,4,35,144,200,3,13
	.byte	'TR',0
	.word	52821
	.byte	64,4,35,128,224,3,13
	.byte	'reserved_F040',0
	.word	52826
	.byte	192,23,4,35,192,224,3,13
	.byte	'CCTRL',0
	.word	47846
	.byte	4,4,35,128,248,3,13
	.byte	'CCNT',0
	.word	47784
	.byte	4,4,35,132,248,3,13
	.byte	'ICNT',0
	.word	49960
	.byte	4,4,35,136,248,3,13
	.byte	'M1CNT',0
	.word	50205
	.byte	4,4,35,140,248,3,13
	.byte	'M2CNT',0
	.word	50268
	.byte	4,4,35,144,248,3,13
	.byte	'M3CNT',0
	.word	50331
	.byte	4,4,35,148,248,3,13
	.byte	'reserved_FC18',0
	.word	52837
	.byte	232,1,4,35,152,248,3,13
	.byte	'DBGSR',0
	.word	48538
	.byte	4,4,35,128,250,3,13
	.byte	'reserved_FD04',0
	.word	2876
	.byte	4,4,35,132,250,3,13
	.byte	'EXEVT',0
	.word	49414
	.byte	4,4,35,136,250,3,13
	.byte	'CREVT',0
	.word	48290
	.byte	4,4,35,140,250,3,13
	.byte	'SWEVT',0
	.word	51695
	.byte	4,4,35,144,250,3,13
	.byte	'reserved_FD14',0
	.word	23753
	.byte	28,4,35,148,250,3,13
	.byte	'TRIG_ACC',0
	.word	52148
	.byte	4,4,35,176,250,3,13
	.byte	'reserved_FD34',0
	.word	5035
	.byte	12,4,35,180,250,3,13
	.byte	'DMS',0
	.word	49041
	.byte	4,4,35,192,250,3,13
	.byte	'DCX',0
	.word	48791
	.byte	4,4,35,196,250,3,13
	.byte	'DBGTCR',0
	.word	48601
	.byte	4,4,35,200,250,3,13
	.byte	'reserved_FD4C',0
	.word	52848
	.byte	180,1,4,35,204,250,3,13
	.byte	'PCXI',0
	.word	50643
	.byte	4,4,35,128,252,3,13
	.byte	'PSW',0
	.word	51079
	.byte	4,4,35,132,252,3,13
	.byte	'PC',0
	.word	50394
	.byte	4,4,35,136,252,3,13
	.byte	'reserved_FE0C',0
	.word	4695
	.byte	8,4,35,140,252,3,13
	.byte	'SYSCON',0
	.word	51758
	.byte	4,4,35,148,252,3,13
	.byte	'CPU_ID',0
	.word	48164
	.byte	4,4,35,152,252,3,13
	.byte	'CORE_ID',0
	.word	47973
	.byte	4,4,35,156,252,3,13
	.byte	'BIV',0
	.word	47662
	.byte	4,4,35,160,252,3,13
	.byte	'BTV',0
	.word	47723
	.byte	4,4,35,164,252,3,13
	.byte	'ISP',0
	.word	50083
	.byte	4,4,35,168,252,3,13
	.byte	'ICR',0
	.word	50022
	.byte	4,4,35,172,252,3,13
	.byte	'reserved_FE30',0
	.word	4695
	.byte	8,4,35,176,252,3,13
	.byte	'FCX',0
	.word	49477
	.byte	4,4,35,184,252,3,13
	.byte	'LCX',0
	.word	50144
	.byte	4,4,35,188,252,3,13
	.byte	'reserved_FE40',0
	.word	23283
	.byte	16,4,35,192,252,3,13
	.byte	'CUS_ID',0
	.word	48353
	.byte	4,4,35,208,252,3,13
	.byte	'reserved_FE54',0
	.word	52859
	.byte	172,1,4,35,212,252,3,13
	.byte	'D',0
	.word	52870
	.byte	64,4,35,128,254,3,13
	.byte	'reserved_FF40',0
	.word	52879
	.byte	64,4,35,192,254,3,13
	.byte	'A',0
	.word	52888
	.byte	64,4,35,128,255,3,13
	.byte	'reserved_FFC0',0
	.word	52879
	.byte	64,4,35,192,255,3,0,14
	.word	52897
	.byte	27
	.byte	'Ifx_CPU',0,15,130,11,3
	.word	54688
	.byte	15,6,127,9,1,16
	.byte	'IfxCpu_Id_0',0,0,16
	.byte	'IfxCpu_Id_1',0,1,16
	.byte	'IfxCpu_Id_none',0,2,0,27
	.byte	'IfxCpu_Id',0,6,132,1,3
	.word	54710
	.byte	27
	.byte	'IfxCpu_ResourceCpu',0,6,161,1,3
	.word	1484
	.byte	10
	.byte	'_Ifx_SRC_SRCR_Bits',0,16,45,16,4,11
	.byte	'SRPN',0,1
	.word	672
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	672
	.byte	2,6,2,35,1,11
	.byte	'SRE',0,1
	.word	672
	.byte	1,5,2,35,1,11
	.byte	'TOS',0,1
	.word	672
	.byte	2,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	672
	.byte	3,0,2,35,1,11
	.byte	'ECC',0,1
	.word	672
	.byte	6,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	672
	.byte	2,0,2,35,2,11
	.byte	'SRR',0,1
	.word	672
	.byte	1,7,2,35,3,11
	.byte	'CLRR',0,1
	.word	672
	.byte	1,6,2,35,3,11
	.byte	'SETR',0,1
	.word	672
	.byte	1,5,2,35,3,11
	.byte	'IOV',0,1
	.word	672
	.byte	1,4,2,35,3,11
	.byte	'IOVCLR',0,1
	.word	672
	.byte	1,3,2,35,3,11
	.byte	'SWS',0,1
	.word	672
	.byte	1,2,2,35,3,11
	.byte	'SWSCLR',0,1
	.word	672
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	672
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SRC_SRCR_Bits',0,16,62,3
	.word	54808
	.byte	12,16,70,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54808
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SRC_SRCR',0,16,75,3
	.word	55124
	.byte	10
	.byte	'_Ifx_SRC_AGBT',0,16,86,25,4,13
	.byte	'SR',0
	.word	55124
	.byte	4,2,35,0,0,14
	.word	55184
	.byte	27
	.byte	'Ifx_SRC_AGBT',0,16,89,3
	.word	55216
	.byte	10
	.byte	'_Ifx_SRC_ASCLIN',0,16,92,25,12,13
	.byte	'TX',0
	.word	55124
	.byte	4,2,35,0,13
	.byte	'RX',0
	.word	55124
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	55124
	.byte	4,2,35,8,0,14
	.word	55242
	.byte	27
	.byte	'Ifx_SRC_ASCLIN',0,16,97,3
	.word	55301
	.byte	10
	.byte	'_Ifx_SRC_BCUSPB',0,16,100,25,4,13
	.byte	'SBSRC',0
	.word	55124
	.byte	4,2,35,0,0,14
	.word	55329
	.byte	27
	.byte	'Ifx_SRC_BCUSPB',0,16,103,3
	.word	55366
	.byte	18,64
	.word	55124
	.byte	19,15,0,10
	.byte	'_Ifx_SRC_CAN',0,16,106,25,64,13
	.byte	'INT',0
	.word	55394
	.byte	64,2,35,0,0,14
	.word	55403
	.byte	27
	.byte	'Ifx_SRC_CAN',0,16,109,3
	.word	55435
	.byte	10
	.byte	'_Ifx_SRC_CCU6',0,16,112,25,16,13
	.byte	'SR0',0
	.word	55124
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	55124
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	55124
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	55124
	.byte	4,2,35,12,0,14
	.word	55460
	.byte	27
	.byte	'Ifx_SRC_CCU6',0,16,118,3
	.word	55532
	.byte	18,8
	.word	55124
	.byte	19,1,0,10
	.byte	'_Ifx_SRC_CERBERUS',0,16,121,25,8,13
	.byte	'SR',0
	.word	55558
	.byte	8,2,35,0,0,14
	.word	55567
	.byte	27
	.byte	'Ifx_SRC_CERBERUS',0,16,124,3
	.word	55603
	.byte	10
	.byte	'_Ifx_SRC_CIF',0,16,127,25,16,13
	.byte	'MI',0
	.word	55124
	.byte	4,2,35,0,13
	.byte	'MIEP',0
	.word	55124
	.byte	4,2,35,4,13
	.byte	'ISP',0
	.word	55124
	.byte	4,2,35,8,13
	.byte	'MJPEG',0
	.word	55124
	.byte	4,2,35,12,0,14
	.word	55633
	.byte	27
	.byte	'Ifx_SRC_CIF',0,16,133,1,3
	.word	55706
	.byte	10
	.byte	'_Ifx_SRC_CPU',0,16,136,1,25,4,13
	.byte	'SBSRC',0
	.word	55124
	.byte	4,2,35,0,0,14
	.word	55732
	.byte	27
	.byte	'Ifx_SRC_CPU',0,16,139,1,3
	.word	55767
	.byte	18,192,1
	.word	55124
	.byte	19,47,0,10
	.byte	'_Ifx_SRC_DMA',0,16,142,1,25,208,1,13
	.byte	'ERR',0
	.word	55124
	.byte	4,2,35,0,13
	.byte	'reserved_4',0
	.word	5035
	.byte	12,2,35,4,13
	.byte	'CH',0
	.word	55793
	.byte	192,1,2,35,16,0,14
	.word	55803
	.byte	27
	.byte	'Ifx_SRC_DMA',0,16,147,1,3
	.word	55870
	.byte	10
	.byte	'_Ifx_SRC_DSADC',0,16,150,1,25,8,13
	.byte	'SRM',0
	.word	55124
	.byte	4,2,35,0,13
	.byte	'SRA',0
	.word	55124
	.byte	4,2,35,4,0,14
	.word	55896
	.byte	27
	.byte	'Ifx_SRC_DSADC',0,16,154,1,3
	.word	55944
	.byte	10
	.byte	'_Ifx_SRC_EMEM',0,16,157,1,25,4,13
	.byte	'SR',0
	.word	55124
	.byte	4,2,35,0,0,14
	.word	55972
	.byte	27
	.byte	'Ifx_SRC_EMEM',0,16,160,1,3
	.word	56005
	.byte	10
	.byte	'_Ifx_SRC_ERAY',0,16,163,1,25,80,13
	.byte	'INT',0
	.word	55558
	.byte	8,2,35,0,13
	.byte	'TINT',0
	.word	55558
	.byte	8,2,35,8,13
	.byte	'NDAT',0
	.word	55558
	.byte	8,2,35,16,13
	.byte	'MBSC',0
	.word	55558
	.byte	8,2,35,24,13
	.byte	'OBUSY',0
	.word	55124
	.byte	4,2,35,32,13
	.byte	'IBUSY',0
	.word	55124
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	27176
	.byte	40,2,35,40,0,14
	.word	56032
	.byte	27
	.byte	'Ifx_SRC_ERAY',0,16,172,1,3
	.word	56159
	.byte	10
	.byte	'_Ifx_SRC_ETH',0,16,175,1,25,4,13
	.byte	'SR',0
	.word	55124
	.byte	4,2,35,0,0,14
	.word	56186
	.byte	27
	.byte	'Ifx_SRC_ETH',0,16,178,1,3
	.word	56218
	.byte	10
	.byte	'_Ifx_SRC_FCE',0,16,181,1,25,4,13
	.byte	'SR',0
	.word	55124
	.byte	4,2,35,0,0,14
	.word	56244
	.byte	27
	.byte	'Ifx_SRC_FCE',0,16,184,1,3
	.word	56276
	.byte	10
	.byte	'_Ifx_SRC_FFT',0,16,187,1,25,12,13
	.byte	'DONE',0
	.word	55124
	.byte	4,2,35,0,13
	.byte	'ERR',0
	.word	55124
	.byte	4,2,35,4,13
	.byte	'RFS',0
	.word	55124
	.byte	4,2,35,8,0,14
	.word	56302
	.byte	27
	.byte	'Ifx_SRC_FFT',0,16,192,1,3
	.word	56362
	.byte	10
	.byte	'_Ifx_SRC_GPSR',0,16,195,1,25,32,13
	.byte	'SR0',0
	.word	55124
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	55124
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	55124
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	55124
	.byte	4,2,35,12,13
	.byte	'reserved_10',0
	.word	23283
	.byte	16,2,35,16,0,14
	.word	56388
	.byte	27
	.byte	'Ifx_SRC_GPSR',0,16,202,1,3
	.word	56482
	.byte	10
	.byte	'_Ifx_SRC_GPT12',0,16,205,1,25,48,13
	.byte	'CIRQ',0
	.word	55124
	.byte	4,2,35,0,13
	.byte	'T2',0
	.word	55124
	.byte	4,2,35,4,13
	.byte	'T3',0
	.word	55124
	.byte	4,2,35,8,13
	.byte	'T4',0
	.word	55124
	.byte	4,2,35,12,13
	.byte	'T5',0
	.word	55124
	.byte	4,2,35,16,13
	.byte	'T6',0
	.word	55124
	.byte	4,2,35,20,13
	.byte	'reserved_18',0
	.word	4066
	.byte	24,2,35,24,0,14
	.word	56509
	.byte	27
	.byte	'Ifx_SRC_GPT12',0,16,214,1,3
	.word	56626
	.byte	18,12
	.word	55124
	.byte	19,2,0,18,32
	.word	55124
	.byte	19,7,0,18,32
	.word	56663
	.byte	19,0,0,18,88
	.word	672
	.byte	19,87,0,18,108
	.word	55124
	.byte	19,26,0,18,96
	.word	672
	.byte	19,95,0,18,96
	.word	56663
	.byte	19,2,0,18,160,3
	.word	672
	.byte	19,159,3,0,18,64
	.word	56663
	.byte	19,1,0,18,192,3
	.word	672
	.byte	19,191,3,0,18,16
	.word	55124
	.byte	19,3,0,18,64
	.word	56748
	.byte	19,3,0,18,192,2
	.word	672
	.byte	19,191,2,0,18,52
	.word	672
	.byte	19,51,0,10
	.byte	'_Ifx_SRC_GTM',0,16,217,1,25,204,18,13
	.byte	'AEIIRQ',0
	.word	55124
	.byte	4,2,35,0,13
	.byte	'ARUIRQ',0
	.word	56654
	.byte	12,2,35,4,13
	.byte	'reserved_10',0
	.word	2876
	.byte	4,2,35,16,13
	.byte	'BRCIRQ',0
	.word	55124
	.byte	4,2,35,20,13
	.byte	'CMPIRQ',0
	.word	55124
	.byte	4,2,35,24,13
	.byte	'SPEIRQ',0
	.word	55558
	.byte	8,2,35,28,13
	.byte	'reserved_24',0
	.word	4695
	.byte	8,2,35,36,13
	.byte	'PSM',0
	.word	56672
	.byte	32,2,35,44,13
	.byte	'reserved_4C',0
	.word	56681
	.byte	88,2,35,76,13
	.byte	'DPLL',0
	.word	56690
	.byte	108,3,35,164,1,13
	.byte	'reserved_110',0
	.word	56699
	.byte	96,3,35,144,2,13
	.byte	'ERR',0
	.word	55124
	.byte	4,3,35,240,2,13
	.byte	'reserved_174',0
	.word	5035
	.byte	12,3,35,244,2,13
	.byte	'TIM',0
	.word	56708
	.byte	96,3,35,128,3,13
	.byte	'reserved_1E0',0
	.word	56717
	.byte	160,3,3,35,224,3,13
	.byte	'MCS',0
	.word	56708
	.byte	96,3,35,128,7,13
	.byte	'reserved_3E0',0
	.word	56717
	.byte	160,3,3,35,224,7,13
	.byte	'TOM',0
	.word	56728
	.byte	64,3,35,128,11,13
	.byte	'reserved_5C0',0
	.word	56737
	.byte	192,3,3,35,192,11,13
	.byte	'ATOM',0
	.word	56757
	.byte	64,3,35,128,15,13
	.byte	'reserved_7C0',0
	.word	56766
	.byte	192,2,3,35,192,15,13
	.byte	'MCSW0',0
	.word	56654
	.byte	12,3,35,128,18,13
	.byte	'reserved_90C',0
	.word	56777
	.byte	52,3,35,140,18,13
	.byte	'MCSW1',0
	.word	56654
	.byte	12,3,35,192,18,0,14
	.word	56786
	.byte	27
	.byte	'Ifx_SRC_GTM',0,16,243,1,3
	.word	57246
	.byte	10
	.byte	'_Ifx_SRC_HSCT',0,16,246,1,25,4,13
	.byte	'SR',0
	.word	55124
	.byte	4,2,35,0,0,14
	.word	57272
	.byte	27
	.byte	'Ifx_SRC_HSCT',0,16,249,1,3
	.word	57305
	.byte	10
	.byte	'_Ifx_SRC_HSSL',0,16,252,1,25,16,13
	.byte	'COK',0
	.word	55124
	.byte	4,2,35,0,13
	.byte	'RDI',0
	.word	55124
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	55124
	.byte	4,2,35,8,13
	.byte	'TRG',0
	.word	55124
	.byte	4,2,35,12,0,14
	.word	57332
	.byte	27
	.byte	'Ifx_SRC_HSSL',0,16,130,2,3
	.word	57405
	.byte	18,56
	.word	672
	.byte	19,55,0,10
	.byte	'_Ifx_SRC_I2C',0,16,133,2,25,80,13
	.byte	'BREQ',0
	.word	55124
	.byte	4,2,35,0,13
	.byte	'LBREQ',0
	.word	55124
	.byte	4,2,35,4,13
	.byte	'SREQ',0
	.word	55124
	.byte	4,2,35,8,13
	.byte	'LSREQ',0
	.word	55124
	.byte	4,2,35,12,13
	.byte	'ERR',0
	.word	55124
	.byte	4,2,35,16,13
	.byte	'P',0
	.word	55124
	.byte	4,2,35,20,13
	.byte	'reserved_18',0
	.word	57432
	.byte	56,2,35,24,0,14
	.word	57441
	.byte	27
	.byte	'Ifx_SRC_I2C',0,16,142,2,3
	.word	57564
	.byte	10
	.byte	'_Ifx_SRC_LMU',0,16,145,2,25,4,13
	.byte	'SR',0
	.word	55124
	.byte	4,2,35,0,0,14
	.word	57590
	.byte	27
	.byte	'Ifx_SRC_LMU',0,16,148,2,3
	.word	57622
	.byte	10
	.byte	'_Ifx_SRC_MSC',0,16,151,2,25,20,13
	.byte	'SR0',0
	.word	55124
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	55124
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	55124
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	55124
	.byte	4,2,35,12,13
	.byte	'SR4',0
	.word	55124
	.byte	4,2,35,16,0,14
	.word	57648
	.byte	27
	.byte	'Ifx_SRC_MSC',0,16,158,2,3
	.word	57733
	.byte	10
	.byte	'_Ifx_SRC_PMU',0,16,161,2,25,4,13
	.byte	'SR',0
	.word	55124
	.byte	4,2,35,0,0,14
	.word	57759
	.byte	27
	.byte	'Ifx_SRC_PMU',0,16,164,2,3
	.word	57791
	.byte	10
	.byte	'_Ifx_SRC_PSI5',0,16,167,2,25,32,13
	.byte	'SR',0
	.word	56663
	.byte	32,2,35,0,0,14
	.word	57817
	.byte	27
	.byte	'Ifx_SRC_PSI5',0,16,170,2,3
	.word	57850
	.byte	10
	.byte	'_Ifx_SRC_PSI5S',0,16,173,2,25,32,13
	.byte	'SR',0
	.word	56663
	.byte	32,2,35,0,0,14
	.word	57877
	.byte	27
	.byte	'Ifx_SRC_PSI5S',0,16,176,2,3
	.word	57911
	.byte	10
	.byte	'_Ifx_SRC_QSPI',0,16,179,2,25,24,13
	.byte	'TX',0
	.word	55124
	.byte	4,2,35,0,13
	.byte	'RX',0
	.word	55124
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	55124
	.byte	4,2,35,8,13
	.byte	'PT',0
	.word	55124
	.byte	4,2,35,12,13
	.byte	'HC',0
	.word	55124
	.byte	4,2,35,16,13
	.byte	'U',0
	.word	55124
	.byte	4,2,35,20,0,14
	.word	57939
	.byte	27
	.byte	'Ifx_SRC_QSPI',0,16,187,2,3
	.word	58032
	.byte	10
	.byte	'_Ifx_SRC_SCR',0,16,190,2,25,4,13
	.byte	'SR',0
	.word	55124
	.byte	4,2,35,0,0,14
	.word	58059
	.byte	27
	.byte	'Ifx_SRC_SCR',0,16,193,2,3
	.word	58091
	.byte	10
	.byte	'_Ifx_SRC_SCU',0,16,196,2,25,20,13
	.byte	'DTS',0
	.word	55124
	.byte	4,2,35,0,13
	.byte	'ERU',0
	.word	56748
	.byte	16,2,35,4,0,14
	.word	58117
	.byte	27
	.byte	'Ifx_SRC_SCU',0,16,200,2,3
	.word	58163
	.byte	18,24
	.word	55124
	.byte	19,5,0,10
	.byte	'_Ifx_SRC_SENT',0,16,203,2,25,24,13
	.byte	'SR',0
	.word	58189
	.byte	24,2,35,0,0,14
	.word	58198
	.byte	27
	.byte	'Ifx_SRC_SENT',0,16,206,2,3
	.word	58231
	.byte	10
	.byte	'_Ifx_SRC_SMU',0,16,209,2,25,12,13
	.byte	'SR',0
	.word	56654
	.byte	12,2,35,0,0,14
	.word	58258
	.byte	27
	.byte	'Ifx_SRC_SMU',0,16,212,2,3
	.word	58290
	.byte	10
	.byte	'_Ifx_SRC_STM',0,16,215,2,25,8,13
	.byte	'SR0',0
	.word	55124
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	55124
	.byte	4,2,35,4,0,14
	.word	58316
	.byte	27
	.byte	'Ifx_SRC_STM',0,16,219,2,3
	.word	58362
	.byte	10
	.byte	'_Ifx_SRC_VADCCG',0,16,222,2,25,16,13
	.byte	'SR0',0
	.word	55124
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	55124
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	55124
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	55124
	.byte	4,2,35,12,0,14
	.word	58388
	.byte	27
	.byte	'Ifx_SRC_VADCCG',0,16,228,2,3
	.word	58463
	.byte	10
	.byte	'_Ifx_SRC_VADCG',0,16,231,2,25,16,13
	.byte	'SR0',0
	.word	55124
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	55124
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	55124
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	55124
	.byte	4,2,35,12,0,14
	.word	58492
	.byte	27
	.byte	'Ifx_SRC_VADCG',0,16,237,2,3
	.word	58566
	.byte	10
	.byte	'_Ifx_SRC_XBAR',0,16,240,2,25,4,13
	.byte	'SRC',0
	.word	55124
	.byte	4,2,35,0,0,14
	.word	58594
	.byte	27
	.byte	'Ifx_SRC_XBAR',0,16,243,2,3
	.word	58628
	.byte	18,4
	.word	55184
	.byte	19,0,0,14
	.word	58655
	.byte	10
	.byte	'_Ifx_SRC_GAGBT',0,16,128,3,25,4,13
	.byte	'AGBT',0
	.word	58664
	.byte	4,2,35,0,0,14
	.word	58669
	.byte	27
	.byte	'Ifx_SRC_GAGBT',0,16,131,3,3
	.word	58705
	.byte	18,48
	.word	55242
	.byte	19,3,0,14
	.word	58733
	.byte	10
	.byte	'_Ifx_SRC_GASCLIN',0,16,134,3,25,48,13
	.byte	'ASCLIN',0
	.word	58742
	.byte	48,2,35,0,0,14
	.word	58747
	.byte	27
	.byte	'Ifx_SRC_GASCLIN',0,16,137,3,3
	.word	58787
	.byte	14
	.word	55329
	.byte	10
	.byte	'_Ifx_SRC_GBCU',0,16,140,3,25,4,13
	.byte	'SPB',0
	.word	58817
	.byte	4,2,35,0,0,14
	.word	58822
	.byte	27
	.byte	'Ifx_SRC_GBCU',0,16,143,3,3
	.word	58856
	.byte	18,64
	.word	55403
	.byte	19,0,0,14
	.word	58883
	.byte	10
	.byte	'_Ifx_SRC_GCAN',0,16,146,3,25,64,13
	.byte	'CAN',0
	.word	58892
	.byte	64,2,35,0,0,14
	.word	58897
	.byte	27
	.byte	'Ifx_SRC_GCAN',0,16,149,3,3
	.word	58931
	.byte	18,32
	.word	55460
	.byte	19,1,0,14
	.word	58958
	.byte	10
	.byte	'_Ifx_SRC_GCCU6',0,16,152,3,25,32,13
	.byte	'CCU6',0
	.word	58967
	.byte	32,2,35,0,0,14
	.word	58972
	.byte	27
	.byte	'Ifx_SRC_GCCU6',0,16,155,3,3
	.word	59008
	.byte	14
	.word	55567
	.byte	10
	.byte	'_Ifx_SRC_GCERBERUS',0,16,158,3,25,8,13
	.byte	'CERBERUS',0
	.word	59036
	.byte	8,2,35,0,0,14
	.word	59041
	.byte	27
	.byte	'Ifx_SRC_GCERBERUS',0,16,161,3,3
	.word	59085
	.byte	18,16
	.word	55633
	.byte	19,0,0,14
	.word	59117
	.byte	10
	.byte	'_Ifx_SRC_GCIF',0,16,164,3,25,16,13
	.byte	'CIF',0
	.word	59126
	.byte	16,2,35,0,0,14
	.word	59131
	.byte	27
	.byte	'Ifx_SRC_GCIF',0,16,167,3,3
	.word	59165
	.byte	18,8
	.word	55732
	.byte	19,1,0,14
	.word	59192
	.byte	10
	.byte	'_Ifx_SRC_GCPU',0,16,170,3,25,8,13
	.byte	'CPU',0
	.word	59201
	.byte	8,2,35,0,0,14
	.word	59206
	.byte	27
	.byte	'Ifx_SRC_GCPU',0,16,173,3,3
	.word	59240
	.byte	18,208,1
	.word	55803
	.byte	19,0,0,14
	.word	59267
	.byte	10
	.byte	'_Ifx_SRC_GDMA',0,16,176,3,25,208,1,13
	.byte	'DMA',0
	.word	59277
	.byte	208,1,2,35,0,0,14
	.word	59282
	.byte	27
	.byte	'Ifx_SRC_GDMA',0,16,179,3,3
	.word	59318
	.byte	14
	.word	55896
	.byte	14
	.word	55896
	.byte	14
	.word	55896
	.byte	10
	.byte	'_Ifx_SRC_GDSADC',0,16,182,3,25,32,13
	.byte	'DSADC0',0
	.word	59345
	.byte	8,2,35,0,13
	.byte	'reserved_8',0
	.word	4695
	.byte	8,2,35,8,13
	.byte	'DSADC2',0
	.word	59350
	.byte	8,2,35,16,13
	.byte	'DSADC3',0
	.word	59355
	.byte	8,2,35,24,0,14
	.word	59360
	.byte	27
	.byte	'Ifx_SRC_GDSADC',0,16,188,3,3
	.word	59451
	.byte	18,4
	.word	55972
	.byte	19,0,0,14
	.word	59480
	.byte	10
	.byte	'_Ifx_SRC_GEMEM',0,16,191,3,25,4,13
	.byte	'EMEM',0
	.word	59489
	.byte	4,2,35,0,0,14
	.word	59494
	.byte	27
	.byte	'Ifx_SRC_GEMEM',0,16,194,3,3
	.word	59530
	.byte	18,80
	.word	56032
	.byte	19,0,0,14
	.word	59558
	.byte	10
	.byte	'_Ifx_SRC_GERAY',0,16,197,3,25,80,13
	.byte	'ERAY',0
	.word	59567
	.byte	80,2,35,0,0,14
	.word	59572
	.byte	27
	.byte	'Ifx_SRC_GERAY',0,16,200,3,3
	.word	59608
	.byte	18,4
	.word	56186
	.byte	19,0,0,14
	.word	59636
	.byte	10
	.byte	'_Ifx_SRC_GETH',0,16,203,3,25,4,13
	.byte	'ETH',0
	.word	59645
	.byte	4,2,35,0,0,14
	.word	59650
	.byte	27
	.byte	'Ifx_SRC_GETH',0,16,206,3,3
	.word	59684
	.byte	18,4
	.word	56244
	.byte	19,0,0,14
	.word	59711
	.byte	10
	.byte	'_Ifx_SRC_GFCE',0,16,209,3,25,4,13
	.byte	'FCE',0
	.word	59720
	.byte	4,2,35,0,0,14
	.word	59725
	.byte	27
	.byte	'Ifx_SRC_GFCE',0,16,212,3,3
	.word	59759
	.byte	18,12
	.word	56302
	.byte	19,0,0,14
	.word	59786
	.byte	10
	.byte	'_Ifx_SRC_GFFT',0,16,215,3,25,12,13
	.byte	'FFT',0
	.word	59795
	.byte	12,2,35,0,0,14
	.word	59800
	.byte	27
	.byte	'Ifx_SRC_GFFT',0,16,218,3,3
	.word	59834
	.byte	18,64
	.word	56388
	.byte	19,1,0,14
	.word	59861
	.byte	10
	.byte	'_Ifx_SRC_GGPSR',0,16,221,3,25,64,13
	.byte	'GPSR',0
	.word	59870
	.byte	64,2,35,0,0,14
	.word	59875
	.byte	27
	.byte	'Ifx_SRC_GGPSR',0,16,224,3,3
	.word	59911
	.byte	18,48
	.word	56509
	.byte	19,0,0,14
	.word	59939
	.byte	10
	.byte	'_Ifx_SRC_GGPT12',0,16,227,3,25,48,13
	.byte	'GPT12',0
	.word	59948
	.byte	48,2,35,0,0,14
	.word	59953
	.byte	27
	.byte	'Ifx_SRC_GGPT12',0,16,230,3,3
	.word	59991
	.byte	18,204,18
	.word	56786
	.byte	19,0,0,14
	.word	60020
	.byte	10
	.byte	'_Ifx_SRC_GGTM',0,16,233,3,25,204,18,13
	.byte	'GTM',0
	.word	60030
	.byte	204,18,2,35,0,0,14
	.word	60035
	.byte	27
	.byte	'Ifx_SRC_GGTM',0,16,236,3,3
	.word	60071
	.byte	18,4
	.word	57272
	.byte	19,0,0,14
	.word	60098
	.byte	10
	.byte	'_Ifx_SRC_GHSCT',0,16,239,3,25,4,13
	.byte	'HSCT',0
	.word	60107
	.byte	4,2,35,0,0,14
	.word	60112
	.byte	27
	.byte	'Ifx_SRC_GHSCT',0,16,242,3,3
	.word	60148
	.byte	18,64
	.word	57332
	.byte	19,3,0,14
	.word	60176
	.byte	10
	.byte	'_Ifx_SRC_GHSSL',0,16,245,3,25,68,13
	.byte	'HSSL',0
	.word	60185
	.byte	64,2,35,0,13
	.byte	'EXI',0
	.word	55124
	.byte	4,2,35,64,0,14
	.word	60190
	.byte	27
	.byte	'Ifx_SRC_GHSSL',0,16,249,3,3
	.word	60239
	.byte	18,80
	.word	57441
	.byte	19,0,0,14
	.word	60267
	.byte	10
	.byte	'_Ifx_SRC_GI2C',0,16,252,3,25,80,13
	.byte	'I2C',0
	.word	60276
	.byte	80,2,35,0,0,14
	.word	60281
	.byte	27
	.byte	'Ifx_SRC_GI2C',0,16,255,3,3
	.word	60315
	.byte	18,4
	.word	57590
	.byte	19,0,0,14
	.word	60342
	.byte	10
	.byte	'_Ifx_SRC_GLMU',0,16,130,4,25,4,13
	.byte	'LMU',0
	.word	60351
	.byte	4,2,35,0,0,14
	.word	60356
	.byte	27
	.byte	'Ifx_SRC_GLMU',0,16,133,4,3
	.word	60390
	.byte	18,40
	.word	57648
	.byte	19,1,0,14
	.word	60417
	.byte	10
	.byte	'_Ifx_SRC_GMSC',0,16,136,4,25,40,13
	.byte	'MSC',0
	.word	60426
	.byte	40,2,35,0,0,14
	.word	60431
	.byte	27
	.byte	'Ifx_SRC_GMSC',0,16,139,4,3
	.word	60465
	.byte	18,8
	.word	57759
	.byte	19,1,0,14
	.word	60492
	.byte	10
	.byte	'_Ifx_SRC_GPMU',0,16,142,4,25,8,13
	.byte	'PMU',0
	.word	60501
	.byte	8,2,35,0,0,14
	.word	60506
	.byte	27
	.byte	'Ifx_SRC_GPMU',0,16,145,4,3
	.word	60540
	.byte	18,32
	.word	57817
	.byte	19,0,0,14
	.word	60567
	.byte	10
	.byte	'_Ifx_SRC_GPSI5',0,16,148,4,25,32,13
	.byte	'PSI5',0
	.word	60576
	.byte	32,2,35,0,0,14
	.word	60581
	.byte	27
	.byte	'Ifx_SRC_GPSI5',0,16,151,4,3
	.word	60617
	.byte	18,32
	.word	57877
	.byte	19,0,0,14
	.word	60645
	.byte	10
	.byte	'_Ifx_SRC_GPSI5S',0,16,154,4,25,32,13
	.byte	'PSI5S',0
	.word	60654
	.byte	32,2,35,0,0,14
	.word	60659
	.byte	27
	.byte	'Ifx_SRC_GPSI5S',0,16,157,4,3
	.word	60697
	.byte	18,96
	.word	57939
	.byte	19,3,0,14
	.word	60726
	.byte	10
	.byte	'_Ifx_SRC_GQSPI',0,16,160,4,25,96,13
	.byte	'QSPI',0
	.word	60735
	.byte	96,2,35,0,0,14
	.word	60740
	.byte	27
	.byte	'Ifx_SRC_GQSPI',0,16,163,4,3
	.word	60776
	.byte	18,4
	.word	58059
	.byte	19,0,0,14
	.word	60804
	.byte	10
	.byte	'_Ifx_SRC_GSCR',0,16,166,4,25,4,13
	.byte	'SCR',0
	.word	60813
	.byte	4,2,35,0,0,14
	.word	60818
	.byte	27
	.byte	'Ifx_SRC_GSCR',0,16,169,4,3
	.word	60852
	.byte	14
	.word	58117
	.byte	10
	.byte	'_Ifx_SRC_GSCU',0,16,172,4,25,20,13
	.byte	'SCU',0
	.word	60879
	.byte	20,2,35,0,0,14
	.word	60884
	.byte	27
	.byte	'Ifx_SRC_GSCU',0,16,175,4,3
	.word	60918
	.byte	18,24
	.word	58198
	.byte	19,0,0,14
	.word	60945
	.byte	10
	.byte	'_Ifx_SRC_GSENT',0,16,178,4,25,24,13
	.byte	'SENT',0
	.word	60954
	.byte	24,2,35,0,0,14
	.word	60959
	.byte	27
	.byte	'Ifx_SRC_GSENT',0,16,181,4,3
	.word	60995
	.byte	18,12
	.word	58258
	.byte	19,0,0,14
	.word	61023
	.byte	10
	.byte	'_Ifx_SRC_GSMU',0,16,184,4,25,12,13
	.byte	'SMU',0
	.word	61032
	.byte	12,2,35,0,0,14
	.word	61037
	.byte	27
	.byte	'Ifx_SRC_GSMU',0,16,187,4,3
	.word	61071
	.byte	18,16
	.word	58316
	.byte	19,1,0,14
	.word	61098
	.byte	10
	.byte	'_Ifx_SRC_GSTM',0,16,190,4,25,16,13
	.byte	'STM',0
	.word	61107
	.byte	16,2,35,0,0,14
	.word	61112
	.byte	27
	.byte	'Ifx_SRC_GSTM',0,16,193,4,3
	.word	61146
	.byte	18,64
	.word	58492
	.byte	19,3,0,14
	.word	61173
	.byte	18,224,1
	.word	672
	.byte	19,223,1,0,18,32
	.word	58388
	.byte	19,1,0,14
	.word	61198
	.byte	10
	.byte	'_Ifx_SRC_GVADC',0,16,196,4,25,192,2,13
	.byte	'G',0
	.word	61182
	.byte	64,2,35,0,13
	.byte	'reserved_40',0
	.word	61187
	.byte	224,1,2,35,64,13
	.byte	'CG',0
	.word	61207
	.byte	32,3,35,160,2,0,14
	.word	61212
	.byte	27
	.byte	'Ifx_SRC_GVADC',0,16,201,4,3
	.word	61281
	.byte	14
	.word	58594
	.byte	10
	.byte	'_Ifx_SRC_GXBAR',0,16,204,4,25,4,13
	.byte	'XBAR',0
	.word	61309
	.byte	4,2,35,0,0,14
	.word	61314
	.byte	27
	.byte	'Ifx_SRC_GXBAR',0,16,207,4,3
	.word	61350
	.byte	10
	.byte	'_Ifx_STM_ACCEN0_Bits',0,17,45,16,4,11
	.byte	'EN0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	672
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	672
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	672
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	672
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	672
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	672
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	672
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	672
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	672
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	672
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	672
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	672
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	672
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	672
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	672
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	672
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	672
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	672
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	672
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	672
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	672
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	672
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	672
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_STM_ACCEN0_Bits',0,17,79,3
	.word	61378
	.byte	10
	.byte	'_Ifx_STM_ACCEN1_Bits',0,17,82,16,4,11
	.byte	'reserved_0',0,4
	.word	497
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_STM_ACCEN1_Bits',0,17,85,3
	.word	61935
	.byte	10
	.byte	'_Ifx_STM_CAP_Bits',0,17,88,16,4,11
	.byte	'STMCAP63_32',0,4
	.word	497
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_STM_CAP_Bits',0,17,91,3
	.word	62012
	.byte	10
	.byte	'_Ifx_STM_CAPSV_Bits',0,17,94,16,4,11
	.byte	'STMCAP63_32',0,4
	.word	497
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_STM_CAPSV_Bits',0,17,97,3
	.word	62084
	.byte	10
	.byte	'_Ifx_STM_CLC_Bits',0,17,100,16,4,11
	.byte	'DISR',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'DISS',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'EDIS',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	497
	.byte	28,0,2,35,0,0,27
	.byte	'Ifx_STM_CLC_Bits',0,17,107,3
	.word	62160
	.byte	10
	.byte	'_Ifx_STM_CMCON_Bits',0,17,110,16,4,11
	.byte	'MSIZE0',0,1
	.word	672
	.byte	5,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	672
	.byte	3,0,2,35,0,11
	.byte	'MSTART0',0,1
	.word	672
	.byte	5,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	672
	.byte	3,0,2,35,1,11
	.byte	'MSIZE1',0,1
	.word	672
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	672
	.byte	3,0,2,35,2,11
	.byte	'MSTART1',0,1
	.word	672
	.byte	5,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	672
	.byte	3,0,2,35,3,0,27
	.byte	'Ifx_STM_CMCON_Bits',0,17,120,3
	.word	62301
	.byte	10
	.byte	'_Ifx_STM_CMP_Bits',0,17,123,16,4,11
	.byte	'CMPVAL',0,4
	.word	497
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_STM_CMP_Bits',0,17,126,3
	.word	62519
	.byte	10
	.byte	'_Ifx_STM_ICR_Bits',0,17,129,1,16,4,11
	.byte	'CMP0EN',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'CMP0IR',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'CMP0OS',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'CMP1EN',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'CMP1IR',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'CMP1OS',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,4
	.word	497
	.byte	25,0,2,35,0,0,27
	.byte	'Ifx_STM_ICR_Bits',0,17,139,1,3
	.word	62586
	.byte	10
	.byte	'_Ifx_STM_ID_Bits',0,17,142,1,16,4,11
	.byte	'MODREV',0,1
	.word	672
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	672
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	689
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_STM_ID_Bits',0,17,147,1,3
	.word	62789
	.byte	10
	.byte	'_Ifx_STM_ISCR_Bits',0,17,150,1,16,4,11
	.byte	'CMP0IRR',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'CMP0IRS',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'CMP1IRR',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'CMP1IRS',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	497
	.byte	28,0,2,35,0,0,27
	.byte	'Ifx_STM_ISCR_Bits',0,17,157,1,3
	.word	62896
	.byte	10
	.byte	'_Ifx_STM_KRST0_Bits',0,17,160,1,16,4,11
	.byte	'RST',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'RSTSTAT',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	497
	.byte	30,0,2,35,0,0,27
	.byte	'Ifx_STM_KRST0_Bits',0,17,165,1,3
	.word	63047
	.byte	10
	.byte	'_Ifx_STM_KRST1_Bits',0,17,168,1,16,4,11
	.byte	'RST',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	497
	.byte	31,0,2,35,0,0,27
	.byte	'Ifx_STM_KRST1_Bits',0,17,172,1,3
	.word	63158
	.byte	10
	.byte	'_Ifx_STM_KRSTCLR_Bits',0,17,175,1,16,4,11
	.byte	'CLR',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	497
	.byte	31,0,2,35,0,0,27
	.byte	'Ifx_STM_KRSTCLR_Bits',0,17,179,1,3
	.word	63250
	.byte	10
	.byte	'_Ifx_STM_OCS_Bits',0,17,182,1,16,4,11
	.byte	'reserved_0',0,4
	.word	497
	.byte	24,8,2,35,0,11
	.byte	'SUS',0,1
	.word	672
	.byte	4,4,2,35,3,11
	.byte	'SUS_P',0,1
	.word	672
	.byte	1,3,2,35,3,11
	.byte	'SUSSTA',0,1
	.word	672
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	672
	.byte	2,0,2,35,3,0,27
	.byte	'Ifx_STM_OCS_Bits',0,17,189,1,3
	.word	63346
	.byte	10
	.byte	'_Ifx_STM_TIM0_Bits',0,17,192,1,16,4,11
	.byte	'STM31_0',0,4
	.word	497
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_STM_TIM0_Bits',0,17,195,1,3
	.word	63492
	.byte	10
	.byte	'_Ifx_STM_TIM0SV_Bits',0,17,198,1,16,4,11
	.byte	'STM31_0',0,4
	.word	497
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_STM_TIM0SV_Bits',0,17,201,1,3
	.word	63564
	.byte	10
	.byte	'_Ifx_STM_TIM1_Bits',0,17,204,1,16,4,11
	.byte	'STM35_4',0,4
	.word	497
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_STM_TIM1_Bits',0,17,207,1,3
	.word	63640
	.byte	10
	.byte	'_Ifx_STM_TIM2_Bits',0,17,210,1,16,4,11
	.byte	'STM39_8',0,4
	.word	497
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_STM_TIM2_Bits',0,17,213,1,3
	.word	63712
	.byte	10
	.byte	'_Ifx_STM_TIM3_Bits',0,17,216,1,16,4,11
	.byte	'STM43_12',0,4
	.word	497
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_STM_TIM3_Bits',0,17,219,1,3
	.word	63784
	.byte	10
	.byte	'_Ifx_STM_TIM4_Bits',0,17,222,1,16,4,11
	.byte	'STM47_16',0,4
	.word	497
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_STM_TIM4_Bits',0,17,225,1,3
	.word	63857
	.byte	10
	.byte	'_Ifx_STM_TIM5_Bits',0,17,228,1,16,4,11
	.byte	'STM51_20',0,4
	.word	497
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_STM_TIM5_Bits',0,17,231,1,3
	.word	63930
	.byte	10
	.byte	'_Ifx_STM_TIM6_Bits',0,17,234,1,16,4,11
	.byte	'STM63_32',0,4
	.word	497
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_STM_TIM6_Bits',0,17,237,1,3
	.word	64003
	.byte	12,17,245,1,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61378
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_ACCEN0',0,17,250,1,3
	.word	64076
	.byte	12,17,253,1,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61935
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_ACCEN1',0,17,130,2,3
	.word	64140
	.byte	12,17,133,2,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62012
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_CAP',0,17,138,2,3
	.word	64204
	.byte	12,17,141,2,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62084
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_CAPSV',0,17,146,2,3
	.word	64265
	.byte	12,17,149,2,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62160
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_CLC',0,17,154,2,3
	.word	64328
	.byte	12,17,157,2,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62301
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_CMCON',0,17,162,2,3
	.word	64389
	.byte	12,17,165,2,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62519
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_CMP',0,17,170,2,3
	.word	64452
	.byte	12,17,173,2,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62586
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_ICR',0,17,178,2,3
	.word	64513
	.byte	12,17,181,2,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62789
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_ID',0,17,186,2,3
	.word	64574
	.byte	12,17,189,2,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62896
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_ISCR',0,17,194,2,3
	.word	64634
	.byte	12,17,197,2,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63047
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_KRST0',0,17,202,2,3
	.word	64696
	.byte	12,17,205,2,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63158
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_KRST1',0,17,210,2,3
	.word	64759
	.byte	12,17,213,2,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63250
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_KRSTCLR',0,17,218,2,3
	.word	64822
	.byte	12,17,221,2,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63346
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_OCS',0,17,226,2,3
	.word	64887
	.byte	12,17,229,2,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63492
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_TIM0',0,17,234,2,3
	.word	64948
	.byte	12,17,237,2,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63564
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_TIM0SV',0,17,242,2,3
	.word	65010
	.byte	12,17,245,2,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63640
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_TIM1',0,17,250,2,3
	.word	65074
	.byte	12,17,253,2,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63712
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_TIM2',0,17,130,3,3
	.word	65136
	.byte	12,17,133,3,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63784
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_TIM3',0,17,138,3,3
	.word	65198
	.byte	12,17,141,3,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63857
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_TIM4',0,17,146,3,3
	.word	65260
	.byte	12,17,149,3,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63930
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_TIM5',0,17,154,3,3
	.word	65322
	.byte	12,17,157,3,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64003
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_TIM6',0,17,162,3,3
	.word	65384
	.byte	15,5,144,1,9,1,16
	.byte	'IfxCpu_CounterMode_normal',0,0,16
	.byte	'IfxCpu_CounterMode_task',0,1,0,27
	.byte	'IfxCpu_CounterMode',0,5,148,1,3
	.word	65446
	.byte	28,5,160,1,9,6,13
	.byte	'counter',0
	.word	1639
	.byte	4,2,35,0,13
	.byte	'overlfow',0
	.word	672
	.byte	1,2,35,4,0,27
	.byte	'IfxCpu_Counter',0,5,164,1,3
	.word	65535
	.byte	28,5,172,1,9,32,13
	.byte	'instruction',0
	.word	65535
	.byte	6,2,35,0,13
	.byte	'clock',0
	.word	65535
	.byte	6,2,35,6,13
	.byte	'counter1',0
	.word	65535
	.byte	6,2,35,12,13
	.byte	'counter2',0
	.word	65535
	.byte	6,2,35,18,13
	.byte	'counter3',0
	.word	65535
	.byte	6,2,35,24,0,27
	.byte	'IfxCpu_Perf',0,5,179,1,3
	.word	65601
	.byte	10
	.byte	'_Ifx_FLASH_ACCEN0_Bits',0,10,45,16,4,11
	.byte	'EN0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	672
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	672
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	672
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	672
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	672
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	672
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	672
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	672
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	672
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	672
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	672
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	672
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	672
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	672
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	672
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	672
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	672
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	672
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	672
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	672
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	672
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	672
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	672
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_FLASH_ACCEN0_Bits',0,10,79,3
	.word	65719
	.byte	10
	.byte	'_Ifx_FLASH_ACCEN1_Bits',0,10,82,16,4,11
	.byte	'reserved_0',0,4
	.word	497
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_FLASH_ACCEN1_Bits',0,10,85,3
	.word	66280
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_CFG_Bits',0,10,88,16,4,11
	.byte	'SEL',0,1
	.word	672
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	672
	.byte	2,0,2,35,0,11
	.byte	'CLR',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'DIS',0,1
	.word	672
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	497
	.byte	22,0,2,35,0,0,27
	.byte	'Ifx_FLASH_CBAB_CFG_Bits',0,10,95,3
	.word	66361
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_STAT_Bits',0,10,98,16,4,11
	.byte	'VLD0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'VLD1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'VLD2',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'VLD3',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'VLD4',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'VLD5',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'VLD6',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'VLD7',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'VLD8',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'VLD9',0,1
	.word	672
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	497
	.byte	22,0,2,35,0,0,27
	.byte	'Ifx_FLASH_CBAB_STAT_Bits',0,10,111,3
	.word	66514
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_TOP_Bits',0,10,114,16,4,11
	.byte	'reserved_0',0,1
	.word	672
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	497
	.byte	19,8,2,35,0,11
	.byte	'ERR',0,1
	.word	672
	.byte	6,2,2,35,3,11
	.byte	'VLD',0,1
	.word	672
	.byte	1,1,2,35,3,11
	.byte	'CLR',0,1
	.word	672
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_FLASH_CBAB_TOP_Bits',0,10,121,3
	.word	66762
	.byte	10
	.byte	'_Ifx_FLASH_COMM0_Bits',0,10,124,16,4,11
	.byte	'STATUS',0,1
	.word	672
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	497
	.byte	24,0,2,35,0,0,27
	.byte	'Ifx_FLASH_COMM0_Bits',0,10,128,1,3
	.word	66908
	.byte	10
	.byte	'_Ifx_FLASH_COMM1_Bits',0,10,131,1,16,4,11
	.byte	'STATUS',0,1
	.word	672
	.byte	8,0,2,35,0,11
	.byte	'DATA',0,1
	.word	672
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	689
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_FLASH_COMM1_Bits',0,10,136,1,3
	.word	67006
	.byte	10
	.byte	'_Ifx_FLASH_COMM2_Bits',0,10,139,1,16,4,11
	.byte	'STATUS',0,1
	.word	672
	.byte	8,0,2,35,0,11
	.byte	'DATA',0,1
	.word	672
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	689
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_FLASH_COMM2_Bits',0,10,144,1,3
	.word	67122
	.byte	10
	.byte	'_Ifx_FLASH_ECCRD_Bits',0,10,147,1,16,4,11
	.byte	'RCODE',0,4
	.word	497
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	689
	.byte	8,2,2,35,2,11
	.byte	'EDCERRINJ',0,1
	.word	672
	.byte	1,1,2,35,3,11
	.byte	'ECCORDIS',0,1
	.word	672
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_FLASH_ECCRD_Bits',0,10,153,1,3
	.word	67238
	.byte	10
	.byte	'_Ifx_FLASH_ECCRP_Bits',0,10,156,1,16,4,11
	.byte	'RCODE',0,4
	.word	497
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	689
	.byte	8,2,2,35,2,11
	.byte	'EDCERRINJ',0,1
	.word	672
	.byte	1,1,2,35,3,11
	.byte	'ECCORDIS',0,1
	.word	672
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_FLASH_ECCRP_Bits',0,10,162,1,3
	.word	67378
	.byte	10
	.byte	'_Ifx_FLASH_ECCW_Bits',0,10,165,1,16,4,11
	.byte	'WCODE',0,4
	.word	497
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	689
	.byte	8,2,2,35,2,11
	.byte	'DECENCDIS',0,1
	.word	672
	.byte	1,1,2,35,3,11
	.byte	'PECENCDIS',0,1
	.word	672
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_FLASH_ECCW_Bits',0,10,171,1,3
	.word	67518
	.byte	27
	.byte	'Ifx_FLASH_FCON_Bits',0,10,193,1,3
	.word	32334
	.byte	10
	.byte	'_Ifx_FLASH_FPRO_Bits',0,10,196,1,16,4,11
	.byte	'PROINP',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'PRODISP',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'PROIND',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'PRODISD',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'PROINHSMCOTP',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'RES5',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'PROINOTP',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'RES7',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'PROINDBG',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'PRODISDBG',0,1
	.word	672
	.byte	1,6,2,35,1,11
	.byte	'PROINHSM',0,1
	.word	672
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	672
	.byte	5,0,2,35,1,11
	.byte	'DCFP',0,1
	.word	672
	.byte	1,7,2,35,2,11
	.byte	'DDFP',0,1
	.word	672
	.byte	1,6,2,35,2,11
	.byte	'DDFPX',0,1
	.word	672
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	672
	.byte	1,4,2,35,2,11
	.byte	'DDFD',0,1
	.word	672
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	672
	.byte	1,2,2,35,2,11
	.byte	'ENPE',0,1
	.word	672
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	672
	.byte	8,0,2,35,3,0,27
	.byte	'Ifx_FLASH_FPRO_Bits',0,10,218,1,3
	.word	67686
	.byte	10
	.byte	'_Ifx_FLASH_FSR_Bits',0,10,221,1,16,4,11
	.byte	'FABUSY',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'D0BUSY',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'RES1',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'P0BUSY',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'P1BUSY',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'RES5',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'RES6',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'PROG',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'ERASE',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'PFPAGE',0,1
	.word	672
	.byte	1,6,2,35,1,11
	.byte	'DFPAGE',0,1
	.word	672
	.byte	1,5,2,35,1,11
	.byte	'OPER',0,1
	.word	672
	.byte	1,4,2,35,1,11
	.byte	'SQER',0,1
	.word	672
	.byte	1,3,2,35,1,11
	.byte	'PROER',0,1
	.word	672
	.byte	1,2,2,35,1,11
	.byte	'PFSBER',0,1
	.word	672
	.byte	1,1,2,35,1,11
	.byte	'PFDBER',0,1
	.word	672
	.byte	1,0,2,35,1,11
	.byte	'PFMBER',0,1
	.word	672
	.byte	1,7,2,35,2,11
	.byte	'RES17',0,1
	.word	672
	.byte	1,6,2,35,2,11
	.byte	'DFSBER',0,1
	.word	672
	.byte	1,5,2,35,2,11
	.byte	'DFDBER',0,1
	.word	672
	.byte	1,4,2,35,2,11
	.byte	'DFTBER',0,1
	.word	672
	.byte	1,3,2,35,2,11
	.byte	'DFMBER',0,1
	.word	672
	.byte	1,2,2,35,2,11
	.byte	'SRIADDERR',0,1
	.word	672
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	689
	.byte	2,7,2,35,2,11
	.byte	'PVER',0,1
	.word	672
	.byte	1,6,2,35,3,11
	.byte	'EVER',0,1
	.word	672
	.byte	1,5,2,35,3,11
	.byte	'SPND',0,1
	.word	672
	.byte	1,4,2,35,3,11
	.byte	'SLM',0,1
	.word	672
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	672
	.byte	1,2,2,35,3,11
	.byte	'ORIER',0,1
	.word	672
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	672
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_FLASH_FSR_Bits',0,10,254,1,3
	.word	68127
	.byte	10
	.byte	'_Ifx_FLASH_ID_Bits',0,10,129,2,16,4,11
	.byte	'MODREV',0,1
	.word	672
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	672
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	689
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_FLASH_ID_Bits',0,10,134,2,3
	.word	68733
	.byte	10
	.byte	'_Ifx_FLASH_MARD_Bits',0,10,137,2,16,4,11
	.byte	'HMARGIN',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'SELD0',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'SPND',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'SPNDERR',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,2
	.word	689
	.byte	10,1,2,35,0,11
	.byte	'TRAPDIS',0,1
	.word	672
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	689
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_FLASH_MARD_Bits',0,10,147,2,3
	.word	68844
	.byte	10
	.byte	'_Ifx_FLASH_MARP_Bits',0,10,150,2,16,4,11
	.byte	'SELP0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'SELP1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'RES2',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'RES3',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	689
	.byte	11,1,2,35,0,11
	.byte	'TRAPDIS',0,1
	.word	672
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	689
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_FLASH_MARP_Bits',0,10,159,2,3
	.word	69058
	.byte	10
	.byte	'_Ifx_FLASH_PROCOND_Bits',0,10,162,2,16,4,11
	.byte	'L',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'NSAFECC',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'RAMIN',0,1
	.word	672
	.byte	2,4,2,35,0,11
	.byte	'RAMINSEL',0,1
	.word	672
	.byte	4,0,2,35,0,11
	.byte	'OSCCFG',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'MODE',0,1
	.word	672
	.byte	2,5,2,35,1,11
	.byte	'APREN',0,1
	.word	672
	.byte	1,4,2,35,1,11
	.byte	'CAP0EN',0,1
	.word	672
	.byte	1,3,2,35,1,11
	.byte	'CAP1EN',0,1
	.word	672
	.byte	1,2,2,35,1,11
	.byte	'CAP2EN',0,1
	.word	672
	.byte	1,1,2,35,1,11
	.byte	'CAP3EN',0,1
	.word	672
	.byte	1,0,2,35,1,11
	.byte	'ESR0CNT',0,2
	.word	689
	.byte	12,4,2,35,2,11
	.byte	'RES29',0,1
	.word	672
	.byte	2,2,2,35,3,11
	.byte	'RES30',0,1
	.word	672
	.byte	1,1,2,35,3,11
	.byte	'RPRO',0,1
	.word	672
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_FLASH_PROCOND_Bits',0,10,179,2,3
	.word	69245
	.byte	10
	.byte	'_Ifx_FLASH_PROCONDBG_Bits',0,10,182,2,16,4,11
	.byte	'OCDSDIS',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'DBGIFLCK',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'EDM',0,1
	.word	672
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	497
	.byte	28,0,2,35,0,0,27
	.byte	'Ifx_FLASH_PROCONDBG_Bits',0,10,188,2,3
	.word	69569
	.byte	10
	.byte	'_Ifx_FLASH_PROCONHSM_Bits',0,10,191,2,16,4,11
	.byte	'HSMDBGDIS',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'DBGIFLCK',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'TSTIFLCK',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'HSMTSTDIS',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'RES15',0,2
	.word	689
	.byte	12,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	689
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_FLASH_PROCONHSM_Bits',0,10,199,2,3
	.word	69712
	.byte	10
	.byte	'_Ifx_FLASH_PROCONHSMCOTP_Bits',0,10,202,2,16,4,11
	.byte	'HSMBOOTEN',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'SSWWAIT',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'HSMDX',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'HSM6X',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'HSM16X',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'HSM17X',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'S6ROM',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'HSMENPINS',0,2
	.word	689
	.byte	2,7,2,35,0,11
	.byte	'HSMENRES',0,1
	.word	672
	.byte	2,5,2,35,1,11
	.byte	'DESTDBG',0,1
	.word	672
	.byte	2,3,2,35,1,11
	.byte	'BLKFLAN',0,1
	.word	672
	.byte	1,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	672
	.byte	2,0,2,35,1,11
	.byte	'S16ROM',0,1
	.word	672
	.byte	1,7,2,35,2,11
	.byte	'S17ROM',0,1
	.word	672
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	689
	.byte	14,0,2,35,2,0,27
	.byte	'Ifx_FLASH_PROCONHSMCOTP_Bits',0,10,219,2,3
	.word	69901
	.byte	10
	.byte	'_Ifx_FLASH_PROCONOTP_Bits',0,10,222,2,16,4,11
	.byte	'S0ROM',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'S1ROM',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'S2ROM',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'S3ROM',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'S4ROM',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'S5ROM',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'S6ROM',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'S7ROM',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'S8ROM',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'S9ROM',0,1
	.word	672
	.byte	1,6,2,35,1,11
	.byte	'S10ROM',0,1
	.word	672
	.byte	1,5,2,35,1,11
	.byte	'S11ROM',0,1
	.word	672
	.byte	1,4,2,35,1,11
	.byte	'S12ROM',0,1
	.word	672
	.byte	1,3,2,35,1,11
	.byte	'S13ROM',0,1
	.word	672
	.byte	1,2,2,35,1,11
	.byte	'S14ROM',0,1
	.word	672
	.byte	1,1,2,35,1,11
	.byte	'S15ROM',0,1
	.word	672
	.byte	1,0,2,35,1,11
	.byte	'S16ROM',0,1
	.word	672
	.byte	1,7,2,35,2,11
	.byte	'S17ROM',0,1
	.word	672
	.byte	1,6,2,35,2,11
	.byte	'S18ROM',0,1
	.word	672
	.byte	1,5,2,35,2,11
	.byte	'S19ROM',0,1
	.word	672
	.byte	1,4,2,35,2,11
	.byte	'S20ROM',0,1
	.word	672
	.byte	1,3,2,35,2,11
	.byte	'S21ROM',0,1
	.word	672
	.byte	1,2,2,35,2,11
	.byte	'S22ROM',0,1
	.word	672
	.byte	1,1,2,35,2,11
	.byte	'S23ROM',0,1
	.word	672
	.byte	1,0,2,35,2,11
	.byte	'S24ROM',0,1
	.word	672
	.byte	1,7,2,35,3,11
	.byte	'S25ROM',0,1
	.word	672
	.byte	1,6,2,35,3,11
	.byte	'S26ROM',0,1
	.word	672
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	672
	.byte	2,3,2,35,3,11
	.byte	'BML',0,1
	.word	672
	.byte	2,1,2,35,3,11
	.byte	'TP',0,1
	.word	672
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_FLASH_PROCONOTP_Bits',0,10,254,2,3
	.word	70264
	.byte	10
	.byte	'_Ifx_FLASH_PROCONP_Bits',0,10,129,3,16,4,11
	.byte	'S0L',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'S1L',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'S2L',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'S3L',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'S4L',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'S5L',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'S6L',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'S7L',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'S8L',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'S9L',0,1
	.word	672
	.byte	1,6,2,35,1,11
	.byte	'S10L',0,1
	.word	672
	.byte	1,5,2,35,1,11
	.byte	'S11L',0,1
	.word	672
	.byte	1,4,2,35,1,11
	.byte	'S12L',0,1
	.word	672
	.byte	1,3,2,35,1,11
	.byte	'S13L',0,1
	.word	672
	.byte	1,2,2,35,1,11
	.byte	'S14L',0,1
	.word	672
	.byte	1,1,2,35,1,11
	.byte	'S15L',0,1
	.word	672
	.byte	1,0,2,35,1,11
	.byte	'S16L',0,1
	.word	672
	.byte	1,7,2,35,2,11
	.byte	'S17L',0,1
	.word	672
	.byte	1,6,2,35,2,11
	.byte	'S18L',0,1
	.word	672
	.byte	1,5,2,35,2,11
	.byte	'S19L',0,1
	.word	672
	.byte	1,4,2,35,2,11
	.byte	'S20L',0,1
	.word	672
	.byte	1,3,2,35,2,11
	.byte	'S21L',0,1
	.word	672
	.byte	1,2,2,35,2,11
	.byte	'S22L',0,1
	.word	672
	.byte	1,1,2,35,2,11
	.byte	'S23L',0,1
	.word	672
	.byte	1,0,2,35,2,11
	.byte	'S24L',0,1
	.word	672
	.byte	1,7,2,35,3,11
	.byte	'S25L',0,1
	.word	672
	.byte	1,6,2,35,3,11
	.byte	'S26L',0,1
	.word	672
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	672
	.byte	4,1,2,35,3,11
	.byte	'RPRO',0,1
	.word	672
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_FLASH_PROCONP_Bits',0,10,160,3,3
	.word	70859
	.byte	10
	.byte	'_Ifx_FLASH_PROCONWOP_Bits',0,10,163,3,16,4,11
	.byte	'S0WOP',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'S1WOP',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'S2WOP',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'S3WOP',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'S4WOP',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'S5WOP',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'S6WOP',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'S7WOP',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'S8WOP',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'S9WOP',0,1
	.word	672
	.byte	1,6,2,35,1,11
	.byte	'S10WOP',0,1
	.word	672
	.byte	1,5,2,35,1,11
	.byte	'S11WOP',0,1
	.word	672
	.byte	1,4,2,35,1,11
	.byte	'S12WOP',0,1
	.word	672
	.byte	1,3,2,35,1,11
	.byte	'S13WOP',0,1
	.word	672
	.byte	1,2,2,35,1,11
	.byte	'S14WOP',0,1
	.word	672
	.byte	1,1,2,35,1,11
	.byte	'S15WOP',0,1
	.word	672
	.byte	1,0,2,35,1,11
	.byte	'S16WOP',0,1
	.word	672
	.byte	1,7,2,35,2,11
	.byte	'S17WOP',0,1
	.word	672
	.byte	1,6,2,35,2,11
	.byte	'S18WOP',0,1
	.word	672
	.byte	1,5,2,35,2,11
	.byte	'S19WOP',0,1
	.word	672
	.byte	1,4,2,35,2,11
	.byte	'S20WOP',0,1
	.word	672
	.byte	1,3,2,35,2,11
	.byte	'S21WOP',0,1
	.word	672
	.byte	1,2,2,35,2,11
	.byte	'S22WOP',0,1
	.word	672
	.byte	1,1,2,35,2,11
	.byte	'S23WOP',0,1
	.word	672
	.byte	1,0,2,35,2,11
	.byte	'S24WOP',0,1
	.word	672
	.byte	1,7,2,35,3,11
	.byte	'S25WOP',0,1
	.word	672
	.byte	1,6,2,35,3,11
	.byte	'S26WOP',0,1
	.word	672
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	672
	.byte	4,1,2,35,3,11
	.byte	'DATM',0,1
	.word	672
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_FLASH_PROCONWOP_Bits',0,10,194,3,3
	.word	71383
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG0_Bits',0,10,197,3,16,4,11
	.byte	'TAG',0,1
	.word	672
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	497
	.byte	26,0,2,35,0,0,27
	.byte	'Ifx_FLASH_RDB_CFG0_Bits',0,10,201,3,3
	.word	71965
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG1_Bits',0,10,204,3,16,4,11
	.byte	'TAG',0,1
	.word	672
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	497
	.byte	26,0,2,35,0,0,27
	.byte	'Ifx_FLASH_RDB_CFG1_Bits',0,10,208,3,3
	.word	72067
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG2_Bits',0,10,211,3,16,4,11
	.byte	'TAG',0,1
	.word	672
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	497
	.byte	26,0,2,35,0,0,27
	.byte	'Ifx_FLASH_RDB_CFG2_Bits',0,10,215,3,3
	.word	72169
	.byte	10
	.byte	'_Ifx_FLASH_RRAD_Bits',0,10,218,3,16,4,11
	.byte	'reserved_0',0,1
	.word	672
	.byte	3,5,2,35,0,11
	.byte	'ADD',0,4
	.word	497
	.byte	29,0,2,35,0,0,27
	.byte	'Ifx_FLASH_RRAD_Bits',0,10,222,3,3
	.word	72271
	.byte	10
	.byte	'_Ifx_FLASH_RRCT_Bits',0,10,225,3,16,4,11
	.byte	'STRT',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'STP',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'BUSY',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'DONE',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'ERR',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	672
	.byte	3,0,2,35,0,11
	.byte	'EOBM',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	672
	.byte	7,0,2,35,1,11
	.byte	'CNT',0,2
	.word	689
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_FLASH_RRCT_Bits',0,10,236,3,3
	.word	72365
	.byte	10
	.byte	'_Ifx_FLASH_RRD0_Bits',0,10,239,3,16,4,11
	.byte	'DATA',0,4
	.word	497
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_FLASH_RRD0_Bits',0,10,242,3,3
	.word	72575
	.byte	10
	.byte	'_Ifx_FLASH_RRD1_Bits',0,10,245,3,16,4,11
	.byte	'DATA',0,4
	.word	497
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_FLASH_RRD1_Bits',0,10,248,3,3
	.word	72648
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_CFG_Bits',0,10,251,3,16,4,11
	.byte	'SEL',0,1
	.word	672
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	672
	.byte	2,0,2,35,0,11
	.byte	'CLR',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'DIS',0,1
	.word	672
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	497
	.byte	22,0,2,35,0,0,27
	.byte	'Ifx_FLASH_UBAB_CFG_Bits',0,10,130,4,3
	.word	72721
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_STAT_Bits',0,10,133,4,16,4,11
	.byte	'VLD0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	497
	.byte	31,0,2,35,0,0,27
	.byte	'Ifx_FLASH_UBAB_STAT_Bits',0,10,137,4,3
	.word	72876
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_TOP_Bits',0,10,140,4,16,4,11
	.byte	'reserved_0',0,1
	.word	672
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	497
	.byte	19,8,2,35,0,11
	.byte	'ERR',0,1
	.word	672
	.byte	6,2,2,35,3,11
	.byte	'VLD',0,1
	.word	672
	.byte	1,1,2,35,3,11
	.byte	'CLR',0,1
	.word	672
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_FLASH_UBAB_TOP_Bits',0,10,147,4,3
	.word	72981
	.byte	12,10,155,4,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65719
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_ACCEN0',0,10,160,4,3
	.word	73129
	.byte	12,10,163,4,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66280
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_ACCEN1',0,10,168,4,3
	.word	73195
	.byte	12,10,171,4,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66361
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_CBAB_CFG',0,10,176,4,3
	.word	73261
	.byte	12,10,179,4,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66514
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_CBAB_STAT',0,10,184,4,3
	.word	73329
	.byte	12,10,187,4,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66762
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_CBAB_TOP',0,10,192,4,3
	.word	73398
	.byte	12,10,195,4,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66908
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_COMM0',0,10,200,4,3
	.word	73466
	.byte	12,10,203,4,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67006
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_COMM1',0,10,208,4,3
	.word	73531
	.byte	12,10,211,4,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67122
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_COMM2',0,10,216,4,3
	.word	73596
	.byte	12,10,219,4,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67238
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_ECCRD',0,10,224,4,3
	.word	73661
	.byte	12,10,227,4,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67378
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_ECCRP',0,10,232,4,3
	.word	73726
	.byte	12,10,235,4,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67518
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_ECCW',0,10,240,4,3
	.word	73791
	.byte	27
	.byte	'Ifx_FLASH_FCON',0,10,248,4,3
	.word	32667
	.byte	12,10,251,4,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67686
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_FPRO',0,10,128,5,3
	.word	73879
	.byte	12,10,131,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68127
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_FSR',0,10,136,5,3
	.word	73943
	.byte	12,10,139,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68733
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_ID',0,10,144,5,3
	.word	74006
	.byte	12,10,147,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68844
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_MARD',0,10,152,5,3
	.word	74068
	.byte	12,10,155,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69058
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_MARP',0,10,160,5,3
	.word	74132
	.byte	12,10,163,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69245
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_PROCOND',0,10,168,5,3
	.word	74196
	.byte	12,10,171,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69569
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_PROCONDBG',0,10,176,5,3
	.word	74263
	.byte	12,10,179,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69712
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_PROCONHSM',0,10,184,5,3
	.word	74332
	.byte	12,10,187,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69901
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_PROCONHSMCOTP',0,10,192,5,3
	.word	74401
	.byte	12,10,195,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70264
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_PROCONOTP',0,10,200,5,3
	.word	74474
	.byte	12,10,203,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70859
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_PROCONP',0,10,208,5,3
	.word	74543
	.byte	12,10,211,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71383
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_PROCONWOP',0,10,216,5,3
	.word	74610
	.byte	12,10,219,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71965
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_RDB_CFG0',0,10,224,5,3
	.word	74679
	.byte	12,10,227,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72067
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_RDB_CFG1',0,10,232,5,3
	.word	74747
	.byte	12,10,235,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72169
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_RDB_CFG2',0,10,240,5,3
	.word	74815
	.byte	12,10,243,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72271
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_RRAD',0,10,248,5,3
	.word	74883
	.byte	12,10,251,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72365
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_RRCT',0,10,128,6,3
	.word	74947
	.byte	12,10,131,6,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72575
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_RRD0',0,10,136,6,3
	.word	75011
	.byte	12,10,139,6,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72648
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_RRD1',0,10,144,6,3
	.word	75075
	.byte	12,10,147,6,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72721
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_UBAB_CFG',0,10,152,6,3
	.word	75139
	.byte	12,10,155,6,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72876
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_UBAB_STAT',0,10,160,6,3
	.word	75207
	.byte	12,10,163,6,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72981
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_UBAB_TOP',0,10,168,6,3
	.word	75276
	.byte	10
	.byte	'_Ifx_FLASH_CBAB',0,10,179,6,25,12,13
	.byte	'CFG',0
	.word	73261
	.byte	4,2,35,0,13
	.byte	'STAT',0
	.word	73329
	.byte	4,2,35,4,13
	.byte	'TOP',0
	.word	73398
	.byte	4,2,35,8,0,14
	.word	75344
	.byte	27
	.byte	'Ifx_FLASH_CBAB',0,10,184,6,3
	.word	75407
	.byte	10
	.byte	'_Ifx_FLASH_RDB',0,10,187,6,25,12,13
	.byte	'CFG0',0
	.word	74679
	.byte	4,2,35,0,13
	.byte	'CFG1',0
	.word	74747
	.byte	4,2,35,4,13
	.byte	'CFG2',0
	.word	74815
	.byte	4,2,35,8,0,14
	.word	75436
	.byte	27
	.byte	'Ifx_FLASH_RDB',0,10,192,6,3
	.word	75500
	.byte	10
	.byte	'_Ifx_FLASH_UBAB',0,10,195,6,25,12,13
	.byte	'CFG',0
	.word	75139
	.byte	4,2,35,0,13
	.byte	'STAT',0
	.word	75207
	.byte	4,2,35,4,13
	.byte	'TOP',0
	.word	75276
	.byte	4,2,35,8,0,14
	.word	75528
	.byte	27
	.byte	'Ifx_FLASH_UBAB',0,10,200,6,3
	.word	75591
	.byte	27
	.byte	'Ifx_P_ACCEN0_Bits',0,8,79,3
	.word	8448
	.byte	27
	.byte	'Ifx_P_ACCEN1_Bits',0,8,85,3
	.word	8361
	.byte	27
	.byte	'Ifx_P_ESR_Bits',0,8,107,3
	.word	4704
	.byte	27
	.byte	'Ifx_P_ID_Bits',0,8,115,3
	.word	2757
	.byte	27
	.byte	'Ifx_P_IN_Bits',0,8,137,1,3
	.word	3752
	.byte	27
	.byte	'Ifx_P_IOCR0_Bits',0,8,150,1,3
	.word	2885
	.byte	27
	.byte	'Ifx_P_IOCR12_Bits',0,8,163,1,3
	.word	3532
	.byte	27
	.byte	'Ifx_P_IOCR4_Bits',0,8,176,1,3
	.word	3100
	.byte	27
	.byte	'Ifx_P_IOCR8_Bits',0,8,189,1,3
	.word	3315
	.byte	27
	.byte	'Ifx_P_LPCR0_Bits',0,8,197,1,3
	.word	7720
	.byte	27
	.byte	'Ifx_P_LPCR1_Bits',0,8,205,1,3
	.word	7844
	.byte	27
	.byte	'Ifx_P_LPCR1_P21_Bits',0,8,215,1,3
	.word	7928
	.byte	27
	.byte	'Ifx_P_LPCR2_Bits',0,8,229,1,3
	.word	8108
	.byte	27
	.byte	'Ifx_P_OMCR0_Bits',0,8,240,1,3
	.word	6359
	.byte	27
	.byte	'Ifx_P_OMCR12_Bits',0,8,250,1,3
	.word	6883
	.byte	27
	.byte	'Ifx_P_OMCR4_Bits',0,8,133,2,3
	.word	6533
	.byte	27
	.byte	'Ifx_P_OMCR8_Bits',0,8,144,2,3
	.word	6707
	.byte	27
	.byte	'Ifx_P_OMCR_Bits',0,8,166,2,3
	.word	7372
	.byte	27
	.byte	'Ifx_P_OMR_Bits',0,8,203,2,3
	.word	2186
	.byte	27
	.byte	'Ifx_P_OMSR0_Bits',0,8,213,2,3
	.word	5696
	.byte	27
	.byte	'Ifx_P_OMSR12_Bits',0,8,224,2,3
	.word	6184
	.byte	27
	.byte	'Ifx_P_OMSR4_Bits',0,8,235,2,3
	.word	5843
	.byte	27
	.byte	'Ifx_P_OMSR8_Bits',0,8,246,2,3
	.word	6012
	.byte	27
	.byte	'Ifx_P_OMSR_Bits',0,8,140,3,3
	.word	7039
	.byte	27
	.byte	'Ifx_P_OUT_Bits',0,8,162,3,3
	.word	1870
	.byte	27
	.byte	'Ifx_P_PCSR_Bits',0,8,180,3,3
	.word	5410
	.byte	27
	.byte	'Ifx_P_PDISC_Bits',0,8,202,3,3
	.word	5044
	.byte	27
	.byte	'Ifx_P_PDR0_Bits',0,8,223,3,3
	.word	4075
	.byte	27
	.byte	'Ifx_P_PDR1_Bits',0,8,244,3,3
	.word	4379
	.byte	27
	.byte	'Ifx_P_ACCEN0',0,8,129,4,3
	.word	8975
	.byte	27
	.byte	'Ifx_P_ACCEN1',0,8,137,4,3
	.word	8408
	.byte	27
	.byte	'Ifx_P_ESR',0,8,145,4,3
	.word	4995
	.byte	27
	.byte	'Ifx_P_ID',0,8,153,4,3
	.word	2836
	.byte	27
	.byte	'Ifx_P_IN',0,8,161,4,3
	.word	4026
	.byte	27
	.byte	'Ifx_P_IOCR0',0,8,169,4,3
	.word	3060
	.byte	27
	.byte	'Ifx_P_IOCR12',0,8,177,4,3
	.word	3712
	.byte	27
	.byte	'Ifx_P_IOCR4',0,8,185,4,3
	.word	3275
	.byte	27
	.byte	'Ifx_P_IOCR8',0,8,193,4,3
	.word	3492
	.byte	27
	.byte	'Ifx_P_LPCR0',0,8,201,4,3
	.word	7804
	.byte	27
	.byte	'Ifx_P_LPCR1',0,8,210,4,3
	.word	8053
	.byte	27
	.byte	'Ifx_P_LPCR2',0,8,218,4,3
	.word	8312
	.byte	27
	.byte	'Ifx_P_OMCR',0,8,226,4,3
	.word	7680
	.byte	27
	.byte	'Ifx_P_OMCR0',0,8,234,4,3
	.word	6493
	.byte	27
	.byte	'Ifx_P_OMCR12',0,8,242,4,3
	.word	6999
	.byte	27
	.byte	'Ifx_P_OMCR4',0,8,250,4,3
	.word	6667
	.byte	27
	.byte	'Ifx_P_OMCR8',0,8,130,5,3
	.word	6843
	.byte	27
	.byte	'Ifx_P_OMR',0,8,138,5,3
	.word	2717
	.byte	27
	.byte	'Ifx_P_OMSR',0,8,146,5,3
	.word	7332
	.byte	27
	.byte	'Ifx_P_OMSR0',0,8,154,5,3
	.word	5803
	.byte	27
	.byte	'Ifx_P_OMSR12',0,8,162,5,3
	.word	6319
	.byte	27
	.byte	'Ifx_P_OMSR4',0,8,170,5,3
	.word	5972
	.byte	27
	.byte	'Ifx_P_OMSR8',0,8,178,5,3
	.word	6144
	.byte	27
	.byte	'Ifx_P_OUT',0,8,186,5,3
	.word	2146
	.byte	27
	.byte	'Ifx_P_PCSR',0,8,194,5,3
	.word	5656
	.byte	27
	.byte	'Ifx_P_PDISC',0,8,202,5,3
	.word	5370
	.byte	27
	.byte	'Ifx_P_PDR0',0,8,210,5,3
	.word	4339
	.byte	27
	.byte	'Ifx_P_PDR1',0,8,218,5,3
	.word	4655
	.byte	14
	.word	9015
	.byte	27
	.byte	'Ifx_P',0,8,139,6,3
	.word	76938
	.byte	15,7,83,9,1,16
	.byte	'IfxPort_InputMode_undefined',0,127,16
	.byte	'IfxPort_InputMode_noPullDevice',0,0,16
	.byte	'IfxPort_InputMode_pullDown',0,8,16
	.byte	'IfxPort_InputMode_pullUp',0,16,0,27
	.byte	'IfxPort_InputMode',0,7,89,3
	.word	76958
	.byte	15,7,120,9,1,16
	.byte	'IfxPort_OutputIdx_general',0,128,1,16
	.byte	'IfxPort_OutputIdx_alt1',0,136,1,16
	.byte	'IfxPort_OutputIdx_alt2',0,144,1,16
	.byte	'IfxPort_OutputIdx_alt3',0,152,1,16
	.byte	'IfxPort_OutputIdx_alt4',0,160,1,16
	.byte	'IfxPort_OutputIdx_alt5',0,168,1,16
	.byte	'IfxPort_OutputIdx_alt6',0,176,1,16
	.byte	'IfxPort_OutputIdx_alt7',0,184,1,0,27
	.byte	'IfxPort_OutputIdx',0,7,130,1,3
	.word	77109
	.byte	15,7,134,1,9,1,16
	.byte	'IfxPort_OutputMode_pushPull',0,128,1,16
	.byte	'IfxPort_OutputMode_openDrain',0,192,1,0,27
	.byte	'IfxPort_OutputMode',0,7,138,1,3
	.word	77353
	.byte	15,7,144,1,9,1,16
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed1',0,0,16
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed2',0,1,16
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed3',0,2,16
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed4',0,3,16
	.byte	'IfxPort_PadDriver_lvdsSpeed1',0,4,16
	.byte	'IfxPort_PadDriver_lvdsSpeed2',0,5,16
	.byte	'IfxPort_PadDriver_lvdsSpeed3',0,6,16
	.byte	'IfxPort_PadDriver_lvdsSpeed4',0,7,16
	.byte	'IfxPort_PadDriver_ttlSpeed1',0,8,16
	.byte	'IfxPort_PadDriver_ttlSpeed2',0,9,16
	.byte	'IfxPort_PadDriver_ttlSpeed3',0,10,16
	.byte	'IfxPort_PadDriver_ttlSpeed4',0,11,0,27
	.byte	'IfxPort_PadDriver',0,7,158,1,3
	.word	77451
	.byte	27
	.byte	'IfxPort_State',0,7,178,1,3
	.word	9628
	.byte	28,7,190,1,9,8,13
	.byte	'port',0
	.word	9623
	.byte	4,2,35,0,13
	.byte	'pinIndex',0
	.word	672
	.byte	1,2,35,4,0,27
	.byte	'IfxPort_Pin',0,7,194,1,3
	.word	77916
	.byte	27
	.byte	'IfxScuCcu_CcuconRegConfig',0,9,216,5,3
	.word	31971
	.byte	27
	.byte	'IfxScuCcu_InitialStepConfig',0,9,227,5,3
	.word	31806
	.byte	27
	.byte	'IfxScuCcu_PllStepsConfig',0,9,236,5,3
	.word	31738
	.byte	27
	.byte	'IfxScuCcu_ClockDistributionConfig',0,9,252,5,3
	.word	32007
	.byte	27
	.byte	'IfxScuCcu_FlashWaitstateConfig',0,9,132,6,3
	.word	32116
	.byte	27
	.byte	'IfxScuCcu_SysPllConfig',0,9,142,6,3
	.word	31886
	.byte	27
	.byte	'IfxScuCcu_Config',0,9,156,6,3
	.word	32152
	.byte	27
	.byte	'IfxScuCcu_ErayPllConfig',0,9,163,6,3
	.word	32707
.L688:
	.byte	26
	.word	32152
.L689:
	.byte	26
	.word	32707
	.byte	18,36
	.word	31738
	.byte	19,2,0
.L690:
	.byte	26
	.word	78266
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L309:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,21,0,54,15,39,12,0,0,3,15,0,73,19,0,0,4,46,1,3,8,32,13
	.byte	58,15,59,15,57,15,54,15,39,12,0,0,5,5,0,3,8,58,15,59,15,57,15,73,19,0,0,6,11,0,0,0,7,36,0,3,8,11,15,62
	.byte	15,0,0,8,46,1,3,8,32,13,58,15,59,15,57,15,73,19,54,15,39,12,0,0,9,59,0,3,8,0,0,10,19,1,3,8,58,15,59,15
	.byte	57,15,11,15,0,0,11,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,12,23,1,58,15,59,15,57,15,11,15,0,0,13,13
	.byte	0,3,8,73,19,11,15,56,9,0,0,14,53,0,73,19,0,0,15,4,1,58,15,59,15,57,15,11,15,0,0,16,40,0,3,8,28,13,0,0
	.byte	17,11,1,0,0,18,1,1,11,15,73,19,0,0,19,33,0,47,15,0,0,20,46,1,3,8,73,19,54,15,39,12,63,12,60,12,0,0,21
	.byte	5,0,73,19,0,0,22,46,1,49,19,0,0,23,5,0,49,19,0,0,24,46,1,3,8,58,15,59,15,57,15,54,15,39,12,63,12,60,12
	.byte	0,0,25,46,0,3,8,58,15,59,15,57,15,73,19,54,15,39,12,63,12,60,12,0,0,26,38,0,73,19,0,0,27,22,0,3,8,58,15
	.byte	59,15,57,15,73,19,0,0,28,19,1,58,15,59,15,57,15,11,15,0,0,29,46,0,3,8,58,15,59,15,57,15,54,15,63,12,60
	.byte	12,0,0,30,21,0,54,15,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L310:
	.word	.L1000-.L999
.L999:
	.half	3
	.word	.L1002-.L1001
.L1001:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg',0
	.byte	0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_Intrinsics.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Scu\\Std\\IfxScuWdt.h',0
	.byte	0,0,0
	.byte	'IfxScu_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxCpu_cfg.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Port\\Std\\IfxPort.h',0
	.byte	0,0,0
	.byte	'IfxPort_regdef.h',0,1,0,0
	.byte	'..\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Scu\\Std\\IfxScuCcu.h',0,0,0,0
	.byte	'IfxFlash_regdef.h',0,1,0,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuCcu.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxScu_cfg.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Platform_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Ifx_Types.h',0
	.byte	0,0,0
	.byte	'IfxCpu_regdef.h',0,1,0,0
	.byte	'IfxSrc_regdef.h',0,1,0,0
	.byte	'IfxStm_regdef.h',0,1,0,0,0
.L1002:
.L1000:
	.sdecl	'.debug_info',debug,cluster('IfxScuCcu_getBaud1Frequency')
	.sect	'.debug_info'
.L311:
	.word	346
	.half	3
	.word	.L312
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuCcu.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L314,.L313
	.byte	2
	.word	.L307
	.byte	3
	.byte	'IfxScuCcu_getBaud1Frequency',0,1,215,1,9
	.word	.L474
	.byte	1,1,1
	.word	.L248,.L475,.L247
	.byte	4
	.word	.L248,.L475
	.byte	5
	.byte	'frequency',0,1,217,1,21
	.word	.L474,.L476
	.byte	5
	.byte	'ccucon0',0,1,218,1,21
	.word	.L477,.L478
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScuCcu_getBaud1Frequency')
	.sect	'.debug_abbrev'
.L312:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxScuCcu_getBaud1Frequency')
	.sect	'.debug_line'
.L313:
	.word	.L1004-.L1003
.L1003:
	.half	3
	.word	.L1006-.L1005
.L1005:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuCcu.c',0,0,0,0,0
.L1006:
	.byte	5,31,7,0,5,2
	.word	.L248
	.byte	3,217,1,1,5,18,9
	.half	.L747-.L248
	.byte	3,2,1,5,5,9
	.half	.L1007-.L747
	.byte	1,5,19,7,9
	.half	.L1008-.L1007
	.byte	3,2,1,5,22,9
	.half	.L748-.L1008
	.byte	1,5,46,9
	.half	.L19-.L748
	.byte	3,4,1,5,60,9
	.half	.L1009-.L19
	.byte	1,5,49,9
	.half	.L1010-.L1009
	.byte	1,5,5,9
	.half	.L20-.L1010
	.byte	3,3,1,5,1,9
	.half	.L21-.L20
	.byte	3,1,1,7,9
	.half	.L315-.L21
	.byte	0,1,1
.L1004:
	.sdecl	'.debug_ranges',debug,cluster('IfxScuCcu_getBaud1Frequency')
	.sect	'.debug_ranges'
.L314:
	.word	-1,.L248,0,.L315-.L248,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScuCcu_getBaud2Frequency')
	.sect	'.debug_info'
.L316:
	.word	346
	.half	3
	.word	.L317
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuCcu.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L319,.L318
	.byte	2
	.word	.L307
	.byte	3
	.byte	'IfxScuCcu_getBaud2Frequency',0,1,233,1,9
	.word	.L474
	.byte	1,1,1
	.word	.L250,.L479,.L249
	.byte	4
	.word	.L250,.L479
	.byte	5
	.byte	'frequency',0,1,235,1,21
	.word	.L474,.L480
	.byte	5
	.byte	'ccucon0',0,1,236,1,21
	.word	.L477,.L481
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScuCcu_getBaud2Frequency')
	.sect	'.debug_abbrev'
.L317:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxScuCcu_getBaud2Frequency')
	.sect	'.debug_line'
.L318:
	.word	.L1012-.L1011
.L1011:
	.half	3
	.word	.L1014-.L1013
.L1013:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuCcu.c',0,0,0,0,0
.L1014:
	.byte	5,31,7,0,5,2
	.word	.L250
	.byte	3,235,1,1,5,18,9
	.half	.L749-.L250
	.byte	3,2,1,5,5,9
	.half	.L1015-.L749
	.byte	1,5,19,7,9
	.half	.L1016-.L1015
	.byte	3,2,1,5,22,9
	.half	.L750-.L1016
	.byte	1,5,46,9
	.half	.L22-.L750
	.byte	3,4,1,5,60,9
	.half	.L1017-.L22
	.byte	1,5,49,9
	.half	.L1018-.L1017
	.byte	1,5,5,9
	.half	.L23-.L1018
	.byte	3,3,1,5,1,9
	.half	.L24-.L23
	.byte	3,1,1,7,9
	.half	.L320-.L24
	.byte	0,1,1
.L1012:
	.sdecl	'.debug_ranges',debug,cluster('IfxScuCcu_getBaud2Frequency')
	.sect	'.debug_ranges'
.L319:
	.word	-1,.L250,0,.L320-.L250,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScuCcu_getBbbFrequency')
	.sect	'.debug_info'
.L321:
	.word	355
	.half	3
	.word	.L322
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuCcu.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L324,.L323
	.byte	2
	.word	.L307
	.byte	3
	.byte	'IfxScuCcu_getBbbFrequency',0,1,251,1,9
	.word	.L474
	.byte	1,1,1
	.word	.L252,.L482,.L251
	.byte	4
	.word	.L252,.L482
	.byte	5
	.byte	'bbbFrequency',0,1,253,1,13
	.word	.L474,.L483
	.byte	5
	.byte	'sourceFrequency',0,1,254,1,13
	.word	.L474,.L484
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScuCcu_getBbbFrequency')
	.sect	'.debug_abbrev'
.L322:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxScuCcu_getBbbFrequency')
	.sect	'.debug_line'
.L323:
	.word	.L1020-.L1019
.L1019:
	.half	3
	.word	.L1022-.L1021
.L1021:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuCcu.c',0,0,0,0,0
.L1022:
	.byte	5,51,7,0,5,2
	.word	.L252
	.byte	3,255,1,1,5,26,9
	.half	.L751-.L252
	.byte	3,2,1,5,10,9
	.half	.L1023-.L751
	.byte	3,2,1,9
	.half	.L1024-.L1023
	.byte	3,12,1,9
	.half	.L1025-.L1024
	.byte	3,3,1,9
	.half	.L1026-.L1025
	.byte	3,3,1,9
	.half	.L1027-.L1026
	.byte	3,3,1,5,26,9
	.half	.L25-.L1027
	.byte	3,109,1,5,9,9
	.half	.L1028-.L25
	.byte	1,5,26,7,9
	.half	.L1029-.L1028
	.byte	3,2,1,5,31,9
	.half	.L752-.L1029
	.byte	1,5,59,9
	.half	.L31-.L752
	.byte	3,4,1,5,44,9
	.half	.L1030-.L31
	.byte	1,5,9,9
	.half	.L32-.L1030
	.byte	3,3,1,5,42,9
	.half	.L26-.L32
	.byte	3,2,1,5,40,9
	.half	.L1031-.L26
	.byte	1,5,9,9
	.half	.L753-.L1031
	.byte	3,1,1,5,42,9
	.half	.L27-.L753
	.byte	3,2,1,5,40,9
	.half	.L1032-.L27
	.byte	1,5,9,9
	.half	.L754-.L1032
	.byte	3,1,1,5,42,9
	.half	.L28-.L754
	.byte	3,2,1,5,40,9
	.half	.L1033-.L28
	.byte	1,5,9,9
	.half	.L755-.L1033
	.byte	3,1,1,5,42,9
	.half	.L29-.L755
	.byte	3,2,1,5,40,9
	.half	.L1034-.L29
	.byte	1,5,9,9
	.half	.L756-.L1034
	.byte	3,1,1,5,22,9
	.half	.L30-.L756
	.byte	3,2,1,5,9,9
	.half	.L757-.L30
	.byte	3,1,1,5,5,9
	.half	.L33-.L757
	.byte	3,3,1,5,1,9
	.half	.L39-.L33
	.byte	3,1,1,7,9
	.half	.L325-.L39
	.byte	0,1,1
.L1020:
	.sdecl	'.debug_ranges',debug,cluster('IfxScuCcu_getBbbFrequency')
	.sect	'.debug_ranges'
.L324:
	.word	-1,.L252,0,.L325-.L252,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScuCcu_getCpuFrequency')
	.sect	'.debug_info'
.L326:
	.word	360
	.half	3
	.word	.L327
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuCcu.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L329,.L328
	.byte	2
	.word	.L307
	.byte	3
	.byte	'IfxScuCcu_getCpuFrequency',0,1,165,2,9
	.word	.L474
	.byte	1,1,1
	.word	.L254,.L485,.L253
	.byte	4
	.byte	'cpu',0,1,165,2,60
	.word	.L486,.L487
	.byte	5
	.word	.L254,.L485
	.byte	6
	.byte	'frequency',0,1,167,2,13
	.word	.L474,.L488
	.byte	6
	.byte	'cpuDiv',0,1,168,2,13
	.word	.L489,.L490
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScuCcu_getCpuFrequency')
	.sect	'.debug_abbrev'
.L327:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxScuCcu_getCpuFrequency')
	.sect	'.debug_line'
.L328:
	.word	.L1036-.L1035
.L1035:
	.half	3
	.word	.L1038-.L1037
.L1037:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuCcu.c',0,0,0,0,0
.L1038:
	.byte	5,9,7,0,5,2
	.word	.L254
	.byte	3,164,2,1,5,50,9
	.half	.L759-.L254
	.byte	3,2,1,5,23,9
	.half	.L758-.L759
	.byte	3,1,1,5,10,9
	.half	.L760-.L758
	.byte	3,4,1,9
	.half	.L1039-.L760
	.byte	3,3,1,5,29,9
	.half	.L40-.L1039
	.byte	3,126,1,5,9,9
	.half	.L1040-.L40
	.byte	3,1,1,5,29,9
	.half	.L41-.L1040
	.byte	3,2,1,5,9,9
	.half	.L1041-.L41
	.byte	3,1,1,5,19,9
	.half	.L42-.L1041
	.byte	3,2,1,5,9,9
	.half	.L1042-.L42
	.byte	3,1,1,5,5,9
	.half	.L43-.L1042
	.byte	3,3,1,5,34,7,9
	.half	.L1043-.L43
	.byte	3,2,1,5,43,9
	.half	.L1044-.L1043
	.byte	1,5,41,9
	.half	.L761-.L1044
	.byte	1,5,31,9
	.half	.L1045-.L761
	.byte	1,5,5,9
	.half	.L46-.L1045
	.byte	3,3,1,5,1,9
	.half	.L47-.L46
	.byte	3,1,1,7,9
	.half	.L330-.L47
	.byte	0,1,1
.L1036:
	.sdecl	'.debug_ranges',debug,cluster('IfxScuCcu_getCpuFrequency')
	.sect	'.debug_ranges'
.L329:
	.word	-1,.L254,0,.L330-.L254,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScuCcu_getFsi2Frequency')
	.sect	'.debug_info'
.L331:
	.word	345
	.half	3
	.word	.L332
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuCcu.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L334,.L333
	.byte	2
	.word	.L307
	.byte	3
	.byte	'IfxScuCcu_getFsi2Frequency',0,1,192,2,9
	.word	.L474
	.byte	1,1,1
	.word	.L256,.L491,.L255
	.byte	4
	.word	.L256,.L491
	.byte	5
	.byte	'frequency',0,1,194,2,21
	.word	.L474,.L492
	.byte	5
	.byte	'ccucon0',0,1,195,2,21
	.word	.L477,.L493
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScuCcu_getFsi2Frequency')
	.sect	'.debug_abbrev'
.L332:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxScuCcu_getFsi2Frequency')
	.sect	'.debug_line'
.L333:
	.word	.L1047-.L1046
.L1046:
	.half	3
	.word	.L1049-.L1048
.L1048:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuCcu.c',0,0,0,0,0
.L1049:
	.byte	5,31,7,0,5,2
	.word	.L256
	.byte	3,194,2,1,5,18,9
	.half	.L762-.L256
	.byte	3,2,1,5,5,9
	.half	.L1050-.L762
	.byte	1,5,19,7,9
	.half	.L1051-.L1050
	.byte	3,2,1,5,22,9
	.half	.L763-.L1051
	.byte	1,5,46,9
	.half	.L48-.L763
	.byte	3,4,1,5,23,9
	.half	.L764-.L48
	.byte	3,2,1,5,13,9
	.half	.L1052-.L764
	.byte	1,5,50,7,9
	.half	.L1053-.L1052
	.byte	1,5,58,9
	.half	.L1054-.L1053
	.byte	1,5,46,7,9
	.half	.L50-.L1054
	.byte	3,2,1,5,35,9
	.half	.L1055-.L50
	.byte	1,5,5,9
	.half	.L49-.L1055
	.byte	3,4,1,5,1,9
	.half	.L52-.L49
	.byte	3,1,1,7,9
	.half	.L335-.L52
	.byte	0,1,1
.L1047:
	.sdecl	'.debug_ranges',debug,cluster('IfxScuCcu_getFsi2Frequency')
	.sect	'.debug_ranges'
.L334:
	.word	-1,.L256,0,.L335-.L256,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScuCcu_getFsiFrequency')
	.sect	'.debug_info'
.L336:
	.word	344
	.half	3
	.word	.L337
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuCcu.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L339,.L338
	.byte	2
	.word	.L307
	.byte	3
	.byte	'IfxScuCcu_getFsiFrequency',0,1,215,2,9
	.word	.L474
	.byte	1,1,1
	.word	.L258,.L494,.L257
	.byte	4
	.word	.L258,.L494
	.byte	5
	.byte	'frequency',0,1,217,2,21
	.word	.L474,.L495
	.byte	5
	.byte	'ccucon0',0,1,218,2,21
	.word	.L477,.L496
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScuCcu_getFsiFrequency')
	.sect	'.debug_abbrev'
.L337:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxScuCcu_getFsiFrequency')
	.sect	'.debug_line'
.L338:
	.word	.L1057-.L1056
.L1056:
	.half	3
	.word	.L1059-.L1058
.L1058:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuCcu.c',0,0,0,0,0
.L1059:
	.byte	5,31,7,0,5,2
	.word	.L258
	.byte	3,217,2,1,5,18,9
	.half	.L765-.L258
	.byte	3,2,1,5,5,9
	.half	.L1060-.L765
	.byte	1,5,19,7,9
	.half	.L1061-.L1060
	.byte	3,2,1,5,22,9
	.half	.L766-.L1061
	.byte	1,5,46,9
	.half	.L53-.L766
	.byte	3,4,1,5,23,9
	.half	.L767-.L53
	.byte	3,2,1,5,13,9
	.half	.L1062-.L767
	.byte	1,5,50,7,9
	.half	.L1063-.L1062
	.byte	1,5,58,9
	.half	.L1064-.L1063
	.byte	1,5,46,7,9
	.half	.L55-.L1064
	.byte	3,2,1,5,35,9
	.half	.L1065-.L55
	.byte	1,5,5,9
	.half	.L54-.L1065
	.byte	3,4,1,5,1,9
	.half	.L57-.L54
	.byte	3,1,1,7,9
	.half	.L340-.L57
	.byte	0,1,1
.L1057:
	.sdecl	'.debug_ranges',debug,cluster('IfxScuCcu_getFsiFrequency')
	.sect	'.debug_ranges'
.L339:
	.word	-1,.L258,0,.L340-.L258,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScuCcu_getMaxFrequency')
	.sect	'.debug_info'
.L341:
	.word	355
	.half	3
	.word	.L342
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuCcu.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L344,.L343
	.byte	2
	.word	.L307
	.byte	3
	.byte	'IfxScuCcu_getMaxFrequency',0,1,238,2,9
	.word	.L474
	.byte	1,1,1
	.word	.L260,.L497,.L259
	.byte	4
	.word	.L260,.L497
	.byte	5
	.byte	'maxFrequency',0,1,240,2,13
	.word	.L474,.L498
	.byte	5
	.byte	'sourceFrequency',0,1,241,2,13
	.word	.L474,.L499
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScuCcu_getMaxFrequency')
	.sect	'.debug_abbrev'
.L342:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxScuCcu_getMaxFrequency')
	.sect	'.debug_line'
.L343:
	.word	.L1067-.L1066
.L1066:
	.half	3
	.word	.L1069-.L1068
.L1068:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuCcu.c',0,0,0,0,0
.L1069:
	.byte	5,51,7,0,5,2
	.word	.L260
	.byte	3,241,2,1,5,26,9
	.half	.L768-.L260
	.byte	3,2,1,5,10,9
	.half	.L1070-.L768
	.byte	3,2,1,9
	.half	.L1071-.L1070
	.byte	3,12,1,9
	.half	.L1072-.L1071
	.byte	3,3,1,9
	.half	.L1073-.L1072
	.byte	3,3,1,9
	.half	.L1074-.L1073
	.byte	3,3,1,5,26,9
	.half	.L58-.L1074
	.byte	3,109,1,5,9,9
	.half	.L1075-.L58
	.byte	1,5,43,7,9
	.half	.L1076-.L1075
	.byte	3,2,1,5,59,9
	.half	.L64-.L1076
	.byte	3,4,1,5,44,9
	.half	.L1077-.L64
	.byte	1,5,9,9
	.half	.L65-.L1077
	.byte	3,3,1,5,42,9
	.half	.L59-.L65
	.byte	3,2,1,5,40,9
	.half	.L1078-.L59
	.byte	1,5,9,9
	.half	.L1079-.L1078
	.byte	3,1,1,5,42,9
	.half	.L60-.L1079
	.byte	3,2,1,5,40,9
	.half	.L1080-.L60
	.byte	1,5,9,9
	.half	.L1081-.L1080
	.byte	3,1,1,5,42,9
	.half	.L61-.L1081
	.byte	3,2,1,5,40,9
	.half	.L1082-.L61
	.byte	1,5,9,9
	.half	.L1083-.L1082
	.byte	3,1,1,5,42,9
	.half	.L62-.L1083
	.byte	3,2,1,5,40,9
	.half	.L1084-.L62
	.byte	1,5,9,9
	.half	.L1085-.L1084
	.byte	3,1,1,5,22,9
	.half	.L63-.L1085
	.byte	3,2,1,5,9,9
	.half	.L1086-.L63
	.byte	3,1,1,5,5,9
	.half	.L66-.L1086
	.byte	3,3,1,5,1,9
	.half	.L72-.L66
	.byte	3,1,1,7,9
	.half	.L345-.L72
	.byte	0,1,1
.L1067:
	.sdecl	'.debug_ranges',debug,cluster('IfxScuCcu_getMaxFrequency')
	.sect	'.debug_ranges'
.L344:
	.word	-1,.L260,0,.L345-.L260,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScuCcu_getModuleFrequency')
	.sect	'.debug_info'
.L346:
	.word	368
	.half	3
	.word	.L347
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuCcu.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L349,.L348
	.byte	2
	.word	.L307
	.byte	3
	.byte	'IfxScuCcu_getModuleFrequency',0,1,151,3,9
	.word	.L474
	.byte	1,1,1
	.word	.L262,.L500,.L261
	.byte	4
	.word	.L262,.L500
	.byte	5
	.byte	'spbFreq',0,1,153,3,17
	.word	.L474,.L501
	.byte	5
	.byte	'moduleFreq',0,1,154,3,17
	.word	.L474,.L502
	.byte	5
	.byte	'scuFdr',0,1,155,3,17
	.word	.L503,.L504
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScuCcu_getModuleFrequency')
	.sect	'.debug_abbrev'
.L347:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxScuCcu_getModuleFrequency')
	.sect	'.debug_line'
.L348:
	.word	.L1088-.L1087
.L1087:
	.half	3
	.word	.L1090-.L1089
.L1089:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuCcu.c',0,0,0,0,0
.L1090:
	.byte	5,15,7,0,5,2
	.word	.L262
	.byte	3,155,3,1,5,40,9
	.half	.L769-.L262
	.byte	3,1,1,5,17,9
	.half	.L770-.L769
	.byte	3,2,1,5,5,9
	.half	.L1091-.L770
	.byte	1,5,33,7,9
	.half	.L1092-.L1091
	.byte	3,2,1,5,48,9
	.half	.L1093-.L1092
	.byte	1,5,38,9
	.half	.L1094-.L1093
	.byte	1,5,30,9
	.half	.L1095-.L1094
	.byte	1,5,54,9
	.half	.L771-.L1095
	.byte	1,5,22,9
	.half	.L73-.L771
	.byte	3,2,1,5,10,9
	.half	.L1096-.L73
	.byte	1,5,41,7,9
	.half	.L1097-.L1096
	.byte	3,2,1,5,31,9
	.half	.L1098-.L1097
	.byte	1,5,50,9
	.half	.L1099-.L1098
	.byte	1,5,48,9
	.half	.L1100-.L1099
	.byte	1,5,54,9
	.half	.L772-.L1100
	.byte	1,5,20,9
	.half	.L75-.L772
	.byte	3,4,1,5,5,9
	.half	.L74-.L75
	.byte	3,3,1,5,1,9
	.half	.L77-.L74
	.byte	3,1,1,7,9
	.half	.L350-.L77
	.byte	0,1,1
.L1088:
	.sdecl	'.debug_ranges',debug,cluster('IfxScuCcu_getModuleFrequency')
	.sect	'.debug_ranges'
.L349:
	.word	-1,.L262,0,.L350-.L262,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScuCcu_getOsc0Frequency')
	.sect	'.debug_info'
.L351:
	.word	300
	.half	3
	.word	.L352
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuCcu.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L354,.L353
	.byte	2
	.word	.L307
	.byte	3
	.byte	'IfxScuCcu_getOsc0Frequency',0,1,176,3,9
	.word	.L474
	.byte	1,1,1
	.word	.L264,.L505,.L263
	.byte	4
	.word	.L264,.L505
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScuCcu_getOsc0Frequency')
	.sect	'.debug_abbrev'
.L352:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxScuCcu_getOsc0Frequency')
	.sect	'.debug_line'
.L353:
	.word	.L1102-.L1101
.L1101:
	.half	3
	.word	.L1104-.L1103
.L1103:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuCcu.c',0,0,0,0,0
.L1104:
	.byte	5,21,7,0,5,2
	.word	.L264
	.byte	3,177,3,1,5,12,9
	.half	.L1105-.L264
	.byte	1,5,5,9
	.half	.L1106-.L1105
	.byte	1,5,1,9
	.half	.L78-.L1106
	.byte	3,1,1,7,9
	.half	.L355-.L78
	.byte	0,1,1
.L1102:
	.sdecl	'.debug_ranges',debug,cluster('IfxScuCcu_getOsc0Frequency')
	.sect	'.debug_ranges'
.L354:
	.word	-1,.L264,0,.L355-.L264,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScuCcu_getOscFrequency')
	.sect	'.debug_info'
.L356:
	.word	318
	.half	3
	.word	.L357
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuCcu.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L359,.L358
	.byte	2
	.word	.L307
	.byte	3
	.byte	'IfxScuCcu_getOscFrequency',0,1,182,3,9
	.word	.L474
	.byte	1,1,1
	.word	.L266,.L506,.L265
	.byte	4
	.word	.L266,.L506
	.byte	5
	.byte	'freq',0,1,184,3,13
	.word	.L474,.L507
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScuCcu_getOscFrequency')
	.sect	'.debug_abbrev'
.L357:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxScuCcu_getOscFrequency')
	.sect	'.debug_line'
.L358:
	.word	.L1108-.L1107
.L1107:
	.half	3
	.word	.L1110-.L1109
.L1109:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuCcu.c',0,0,0,0,0
.L1110:
	.byte	5,22,7,0,5,2
	.word	.L266
	.byte	3,185,3,1,5,5,9
	.half	.L1111-.L266
	.byte	1,5,16,7,9
	.half	.L1112-.L1111
	.byte	3,2,1,5,40,9
	.half	.L1113-.L1112
	.byte	1,5,27,9
	.half	.L79-.L1113
	.byte	3,2,1,5,10,9
	.half	.L1114-.L79
	.byte	1,5,25,7,9
	.half	.L1115-.L1114
	.byte	3,2,1,5,16,9
	.half	.L1116-.L1115
	.byte	1,5,48,9
	.half	.L774-.L1116
	.byte	1,5,14,9
	.half	.L81-.L774
	.byte	3,5,1,5,5,9
	.half	.L80-.L81
	.byte	3,3,1,5,1,9
	.half	.L83-.L80
	.byte	3,1,1,7,9
	.half	.L360-.L83
	.byte	0,1,1
.L1108:
	.sdecl	'.debug_ranges',debug,cluster('IfxScuCcu_getOscFrequency')
	.sect	'.debug_ranges'
.L359:
	.word	-1,.L266,0,.L360-.L266,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScuCcu_getPllErayFrequency')
	.sect	'.debug_info'
.L361:
	.word	360
	.half	3
	.word	.L362
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuCcu.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L364,.L363
	.byte	2
	.word	.L307
	.byte	3
	.byte	'IfxScuCcu_getPllErayFrequency',0,1,204,3,9
	.word	.L474
	.byte	1,1,1
	.word	.L268,.L508,.L267
	.byte	4
	.word	.L268,.L508
	.byte	5
	.byte	'scu',0,1,206,3,14
	.word	.L509,.L510
	.byte	5
	.byte	'oscFreq',0,1,207,3,14
	.word	.L474,.L511
	.byte	5
	.byte	'freq',0,1,208,3,14
	.word	.L474,.L512
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScuCcu_getPllErayFrequency')
	.sect	'.debug_abbrev'
.L362:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxScuCcu_getPllErayFrequency')
	.sect	'.debug_line'
.L363:
	.word	.L1118-.L1117
.L1117:
	.half	3
	.word	.L1120-.L1119
.L1119:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuCcu.c',0,0,0,0,0
.L1120:
	.byte	5,21,7,0,5,2
	.word	.L268
	.byte	3,205,3,1,5,40,9
	.half	.L1121-.L268
	.byte	3,4,1,5,27,9
	.half	.L776-.L1121
	.byte	3,2,1,5,5,9
	.half	.L1122-.L776
	.byte	1,5,45,7,9
	.half	.L1123-.L1122
	.byte	3,3,1,5,52,9
	.half	.L1124-.L1123
	.byte	1,5,24,9
	.half	.L1125-.L1124
	.byte	1,5,56,9
	.half	.L777-.L1125
	.byte	1,5,32,9
	.half	.L84-.L777
	.byte	3,2,1,5,10,9
	.half	.L1126-.L84
	.byte	1,5,16,7,9
	.half	.L1127-.L1126
	.byte	3,3,1,5,63,9
	.half	.L1128-.L1127
	.byte	1,5,70,9
	.half	.L1129-.L1128
	.byte	1,5,42,9
	.half	.L778-.L1129
	.byte	1,5,74,9
	.half	.L1130-.L778
	.byte	1,5,46,9
	.half	.L86-.L1130
	.byte	3,5,1,5,52,9
	.half	.L1131-.L86
	.byte	1,5,25,9
	.half	.L1132-.L1131
	.byte	1,5,79,9
	.half	.L1133-.L1132
	.byte	1,5,86,9
	.half	.L1134-.L1133
	.byte	1,5,58,9
	.half	.L1135-.L1134
	.byte	1,5,5,9
	.half	.L85-.L1135
	.byte	3,3,1,5,1,9
	.half	.L88-.L85
	.byte	3,1,1,7,9
	.half	.L365-.L88
	.byte	0,1,1
.L1118:
	.sdecl	'.debug_ranges',debug,cluster('IfxScuCcu_getPllErayFrequency')
	.sect	'.debug_ranges'
.L364:
	.word	-1,.L268,0,.L365-.L268,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScuCcu_getPllErayVcoFrequency')
	.sect	'.debug_info'
.L366:
	.word	328
	.half	3
	.word	.L367
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuCcu.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L369,.L368
	.byte	2
	.word	.L307
	.byte	3
	.byte	'IfxScuCcu_getPllErayVcoFrequency',0,1,232,3,9
	.word	.L474
	.byte	1,1,1
	.word	.L270,.L513,.L269
	.byte	4
	.word	.L270,.L513
	.byte	5
	.byte	'vcoFreq',0,1,234,3,13
	.word	.L474,.L514
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScuCcu_getPllErayVcoFrequency')
	.sect	'.debug_abbrev'
.L367:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxScuCcu_getPllErayVcoFrequency')
	.sect	'.debug_line'
.L368:
	.word	.L1137-.L1136
.L1136:
	.half	3
	.word	.L1139-.L1138
.L1138:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuCcu.c',0,0,0,0,0
.L1139:
	.byte	5,26,7,0,5,2
	.word	.L270
	.byte	3,235,3,1,5,5,9
	.half	.L1140-.L270
	.byte	1,5,19,7,9
	.half	.L1141-.L1140
	.byte	3,3,1,5,44,9
	.half	.L1142-.L1141
	.byte	1,5,45,9
	.half	.L89-.L1142
	.byte	3,5,1,5,68,9
	.half	.L1143-.L89
	.byte	1,5,74,9
	.half	.L1144-.L1143
	.byte	1,5,48,9
	.half	.L1145-.L1144
	.byte	1,5,100,9
	.half	.L1146-.L1145
	.byte	1,5,106,9
	.half	.L1147-.L1146
	.byte	1,5,80,9
	.half	.L1148-.L1147
	.byte	1,5,5,9
	.half	.L90-.L1148
	.byte	3,3,1,5,1,9
	.half	.L91-.L90
	.byte	3,1,1,7,9
	.half	.L370-.L91
	.byte	0,1,1
.L1137:
	.sdecl	'.debug_ranges',debug,cluster('IfxScuCcu_getPllErayVcoFrequency')
	.sect	'.debug_ranges'
.L369:
	.word	-1,.L270,0,.L370-.L270,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScuCcu_getPllFrequency')
	.sect	'.debug_info'
.L371:
	.word	356
	.half	3
	.word	.L372
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuCcu.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L374,.L373
	.byte	2
	.word	.L307
	.byte	3
	.byte	'IfxScuCcu_getPllFrequency',0,1,251,3,9
	.word	.L474
	.byte	1,1,1
	.word	.L272,.L515,.L271
	.byte	4
	.word	.L272,.L515
	.byte	5
	.byte	'scu',0,1,253,3,14
	.word	.L509,.L516
	.byte	5
	.byte	'oscFreq',0,1,254,3,14
	.word	.L474,.L517
	.byte	5
	.byte	'freq',0,1,255,3,14
	.word	.L474,.L518
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScuCcu_getPllFrequency')
	.sect	'.debug_abbrev'
.L372:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxScuCcu_getPllFrequency')
	.sect	'.debug_line'
.L373:
	.word	.L1150-.L1149
.L1149:
	.half	3
	.word	.L1152-.L1151
.L1151:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuCcu.c',0,0,0,0,0
.L1152:
	.byte	5,21,7,0,5,2
	.word	.L272
	.byte	3,252,3,1,5,40,9
	.half	.L1153-.L272
	.byte	3,4,1,5,23,9
	.half	.L781-.L1153
	.byte	3,2,1,5,5,9
	.half	.L1154-.L781
	.byte	1,5,41,7,9
	.half	.L1155-.L1154
	.byte	3,3,1,5,48,9
	.half	.L1156-.L1155
	.byte	1,5,24,9
	.half	.L1157-.L1156
	.byte	1,5,52,9
	.half	.L782-.L1157
	.byte	1,5,28,9
	.half	.L92-.L782
	.byte	3,2,1,5,10,9
	.half	.L1158-.L92
	.byte	1,5,16,7,9
	.half	.L1159-.L1158
	.byte	3,3,1,5,59,9
	.half	.L1160-.L1159
	.byte	1,5,66,9
	.half	.L1161-.L1160
	.byte	1,5,42,9
	.half	.L783-.L1161
	.byte	1,5,70,9
	.half	.L1162-.L783
	.byte	1,5,42,9
	.half	.L94-.L1162
	.byte	3,5,1,5,48,9
	.half	.L1163-.L94
	.byte	1,5,25,9
	.half	.L1164-.L1163
	.byte	1,5,72,9
	.half	.L1165-.L1164
	.byte	1,5,79,9
	.half	.L1166-.L1165
	.byte	1,5,101,9
	.half	.L1167-.L1166
	.byte	1,5,107,9
	.half	.L1168-.L1167
	.byte	1,5,84,9
	.half	.L1169-.L1168
	.byte	1,5,54,9
	.half	.L1170-.L1169
	.byte	1,5,5,9
	.half	.L93-.L1170
	.byte	3,3,1,5,1,9
	.half	.L96-.L93
	.byte	3,1,1,7,9
	.half	.L375-.L96
	.byte	0,1,1
.L1150:
	.sdecl	'.debug_ranges',debug,cluster('IfxScuCcu_getPllFrequency')
	.sect	'.debug_ranges'
.L374:
	.word	-1,.L272,0,.L375-.L272,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScuCcu_getPllVcoFrequency')
	.sect	'.debug_info'
.L376:
	.word	324
	.half	3
	.word	.L377
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuCcu.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L379,.L378
	.byte	2
	.word	.L307
	.byte	3
	.byte	'IfxScuCcu_getPllVcoFrequency',0,1,151,4,9
	.word	.L474
	.byte	1,1,1
	.word	.L274,.L519,.L273
	.byte	4
	.word	.L274,.L519
	.byte	5
	.byte	'vcoFreq',0,1,153,4,13
	.word	.L474,.L520
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScuCcu_getPllVcoFrequency')
	.sect	'.debug_abbrev'
.L377:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxScuCcu_getPllVcoFrequency')
	.sect	'.debug_line'
.L378:
	.word	.L1172-.L1171
.L1171:
	.half	3
	.word	.L1174-.L1173
.L1173:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuCcu.c',0,0,0,0,0
.L1174:
	.byte	5,22,7,0,5,2
	.word	.L274
	.byte	3,154,4,1,5,5,9
	.half	.L1175-.L274
	.byte	1,5,19,7,9
	.half	.L1176-.L1175
	.byte	3,3,1,5,44,9
	.half	.L1177-.L1176
	.byte	1,5,45,9
	.half	.L97-.L1177
	.byte	3,5,1,5,64,9
	.half	.L1178-.L97
	.byte	1,5,70,9
	.half	.L1179-.L1178
	.byte	1,5,48,9
	.half	.L1180-.L1179
	.byte	1,5,92,9
	.half	.L1181-.L1180
	.byte	1,5,98,9
	.half	.L1182-.L1181
	.byte	1,5,76,9
	.half	.L1183-.L1182
	.byte	1,5,5,9
	.half	.L98-.L1183
	.byte	3,3,1,5,1,9
	.half	.L99-.L98
	.byte	3,1,1,7,9
	.half	.L380-.L99
	.byte	0,1,1
.L1172:
	.sdecl	'.debug_ranges',debug,cluster('IfxScuCcu_getPllVcoFrequency')
	.sect	'.debug_ranges'
.L379:
	.word	-1,.L274,0,.L380-.L274,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScuCcu_getSourceFrequency')
	.sect	'.debug_info'
.L381:
	.word	354
	.half	3
	.word	.L382
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuCcu.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L384,.L383
	.byte	2
	.word	.L307
	.byte	3
	.byte	'IfxScuCcu_getSourceFrequency',0,1,170,4,9
	.word	.L474
	.byte	1,1,1
	.word	.L276,.L521,.L275
	.byte	4
	.word	.L276,.L521
	.byte	5
	.byte	'sourcefreq',0,1,172,4,13
	.word	.L474,.L522
	.byte	6
	.word	.L523,.L100,.L103
	.byte	7
	.word	.L524,.L100,.L103
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScuCcu_getSourceFrequency')
	.sect	'.debug_abbrev'
.L382:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,6
	.byte	29,1,49,16,17,1,18,1,0,0,7,11,0,49,16,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxScuCcu_getSourceFrequency')
	.sect	'.debug_line'
.L383:
	.word	.L1185-.L1184
.L1184:
	.half	3
	.word	.L1187-.L1186
.L1186:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuCcu.c',0,0,0,0
	.byte	'..\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Scu\\Std\\IfxScuCcu.h',0,0,0,0,0
.L1187:
	.byte	5,26,7,0,5,2
	.word	.L276
	.byte	3,173,4,1,5,10,9
	.half	.L1188-.L276
	.byte	3,2,1,9
	.half	.L1189-.L1188
	.byte	3,3,1,4,2,5,12,9
	.half	.L100-.L1189
	.byte	3,140,4,1,5,5,9
	.half	.L1190-.L100
	.byte	1,4,1,5,9,9
	.half	.L103-.L1190
	.byte	3,243,123,1,5,47,9
	.half	.L101-.L103
	.byte	3,2,1,5,9,9
	.half	.L786-.L101
	.byte	3,1,1,5,20,9
	.half	.L102-.L786
	.byte	3,2,1,5,9,9
	.half	.L787-.L102
	.byte	3,1,1,5,5,9
	.half	.L104-.L787
	.byte	3,3,1,5,1,9
	.half	.L107-.L104
	.byte	3,1,1,7,9
	.half	.L385-.L107
	.byte	0,1,1
.L1185:
	.sdecl	'.debug_ranges',debug,cluster('IfxScuCcu_getSourceFrequency')
	.sect	'.debug_ranges'
.L384:
	.word	-1,.L276,0,.L385-.L276,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScuCcu_getSpbFrequency')
	.sect	'.debug_info'
.L386:
	.word	355
	.half	3
	.word	.L387
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuCcu.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L389,.L388
	.byte	2
	.word	.L307
	.byte	3
	.byte	'IfxScuCcu_getSpbFrequency',0,1,191,4,9
	.word	.L474
	.byte	1,1,1
	.word	.L278,.L525,.L277
	.byte	4
	.word	.L278,.L525
	.byte	5
	.byte	'spbFrequency',0,1,193,4,13
	.word	.L474,.L526
	.byte	5
	.byte	'sourceFrequency',0,1,194,4,13
	.word	.L474,.L527
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScuCcu_getSpbFrequency')
	.sect	'.debug_abbrev'
.L387:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxScuCcu_getSpbFrequency')
	.sect	'.debug_line'
.L388:
	.word	.L1192-.L1191
.L1191:
	.half	3
	.word	.L1194-.L1193
.L1193:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuCcu.c',0,0,0,0,0
.L1194:
	.byte	5,51,7,0,5,2
	.word	.L278
	.byte	3,195,4,1,5,26,9
	.half	.L788-.L278
	.byte	3,2,1,5,10,9
	.half	.L1195-.L788
	.byte	3,2,1,9
	.half	.L1196-.L1195
	.byte	3,12,1,9
	.half	.L1197-.L1196
	.byte	3,3,1,9
	.half	.L1198-.L1197
	.byte	3,3,1,9
	.half	.L1199-.L1198
	.byte	3,3,1,5,26,9
	.half	.L108-.L1199
	.byte	3,109,1,5,9,9
	.half	.L1200-.L108
	.byte	1,5,26,7,9
	.half	.L1201-.L1200
	.byte	3,2,1,5,31,9
	.half	.L789-.L1201
	.byte	1,5,59,9
	.half	.L114-.L789
	.byte	3,4,1,5,44,9
	.half	.L1202-.L114
	.byte	1,5,9,9
	.half	.L115-.L1202
	.byte	3,3,1,5,42,9
	.half	.L109-.L115
	.byte	3,2,1,5,40,9
	.half	.L1203-.L109
	.byte	1,5,9,9
	.half	.L790-.L1203
	.byte	3,1,1,5,42,9
	.half	.L110-.L790
	.byte	3,2,1,5,40,9
	.half	.L1204-.L110
	.byte	1,5,9,9
	.half	.L791-.L1204
	.byte	3,1,1,5,42,9
	.half	.L111-.L791
	.byte	3,2,1,5,40,9
	.half	.L1205-.L111
	.byte	1,5,9,9
	.half	.L792-.L1205
	.byte	3,1,1,5,42,9
	.half	.L112-.L792
	.byte	3,2,1,5,40,9
	.half	.L1206-.L112
	.byte	1,5,9,9
	.half	.L793-.L1206
	.byte	3,1,1,5,22,9
	.half	.L113-.L793
	.byte	3,2,1,5,9,9
	.half	.L794-.L113
	.byte	3,1,1,5,5,9
	.half	.L116-.L794
	.byte	3,3,1,5,1,9
	.half	.L122-.L116
	.byte	3,1,1,7,9
	.half	.L390-.L122
	.byte	0,1,1
.L1192:
	.sdecl	'.debug_ranges',debug,cluster('IfxScuCcu_getSpbFrequency')
	.sect	'.debug_ranges'
.L389:
	.word	-1,.L278,0,.L390-.L278,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScuCcu_getSriFrequency')
	.sect	'.debug_info'
.L391:
	.word	355
	.half	3
	.word	.L392
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuCcu.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L394,.L393
	.byte	2
	.word	.L307
	.byte	3
	.byte	'IfxScuCcu_getSriFrequency',0,1,233,4,9
	.word	.L474
	.byte	1,1,1
	.word	.L280,.L528,.L279
	.byte	4
	.word	.L280,.L528
	.byte	5
	.byte	'sriFrequency',0,1,235,4,13
	.word	.L474,.L529
	.byte	5
	.byte	'sourceFrequency',0,1,236,4,13
	.word	.L474,.L530
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScuCcu_getSriFrequency')
	.sect	'.debug_abbrev'
.L392:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxScuCcu_getSriFrequency')
	.sect	'.debug_line'
.L393:
	.word	.L1208-.L1207
.L1207:
	.half	3
	.word	.L1210-.L1209
.L1209:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuCcu.c',0,0,0,0,0
.L1210:
	.byte	5,51,7,0,5,2
	.word	.L280
	.byte	3,237,4,1,5,26,9
	.half	.L795-.L280
	.byte	3,2,1,5,10,9
	.half	.L1211-.L795
	.byte	3,2,1,9
	.half	.L1212-.L1211
	.byte	3,12,1,9
	.half	.L1213-.L1212
	.byte	3,3,1,9
	.half	.L1214-.L1213
	.byte	3,3,1,9
	.half	.L1215-.L1214
	.byte	3,3,1,5,26,9
	.half	.L123-.L1215
	.byte	3,109,1,5,9,9
	.half	.L1216-.L123
	.byte	1,5,26,7,9
	.half	.L1217-.L1216
	.byte	3,2,1,5,31,9
	.half	.L796-.L1217
	.byte	1,5,59,9
	.half	.L129-.L796
	.byte	3,4,1,5,44,9
	.half	.L1218-.L129
	.byte	1,5,9,9
	.half	.L130-.L1218
	.byte	3,3,1,5,42,9
	.half	.L124-.L130
	.byte	3,2,1,5,40,9
	.half	.L1219-.L124
	.byte	1,5,9,9
	.half	.L797-.L1219
	.byte	3,1,1,5,42,9
	.half	.L125-.L797
	.byte	3,2,1,5,40,9
	.half	.L1220-.L125
	.byte	1,5,9,9
	.half	.L798-.L1220
	.byte	3,1,1,5,42,9
	.half	.L126-.L798
	.byte	3,2,1,5,40,9
	.half	.L1221-.L126
	.byte	1,5,9,9
	.half	.L799-.L1221
	.byte	3,1,1,5,42,9
	.half	.L127-.L799
	.byte	3,2,1,5,40,9
	.half	.L1222-.L127
	.byte	1,5,9,9
	.half	.L800-.L1222
	.byte	3,1,1,5,22,9
	.half	.L128-.L800
	.byte	3,2,1,5,9,9
	.half	.L801-.L128
	.byte	3,1,1,5,5,9
	.half	.L131-.L801
	.byte	3,3,1,5,1,9
	.half	.L137-.L131
	.byte	3,1,1,7,9
	.half	.L395-.L137
	.byte	0,1,1
.L1208:
	.sdecl	'.debug_ranges',debug,cluster('IfxScuCcu_getSriFrequency')
	.sect	'.debug_ranges'
.L394:
	.word	-1,.L280,0,.L395-.L280,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScuCcu_setCpuFrequency')
	.sect	'.debug_info'
.L396:
	.word	407
	.half	3
	.word	.L397
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuCcu.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L399,.L398
	.byte	2
	.word	.L307
	.byte	3
	.byte	'IfxScuCcu_setCpuFrequency',0,1,172,8,9
	.word	.L474
	.byte	1,1,1
	.word	.L292,.L531,.L291
	.byte	4
	.byte	'cpu',0,1,172,8,54
	.word	.L532,.L533
	.byte	4
	.byte	'cpuFreq',0,1,172,8,67
	.word	.L474,.L534
	.byte	5
	.word	.L292,.L531
	.byte	6
	.byte	'endinitSfty_pw',0,1,174,8,13
	.word	.L535,.L536
	.byte	6
	.byte	'sriFreq',0,1,175,8,13
	.word	.L474,.L537
	.byte	6
	.byte	'cpuDiv',0,1,176,8,13
	.word	.L489,.L538
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScuCcu_setCpuFrequency')
	.sect	'.debug_abbrev'
.L397:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxScuCcu_setCpuFrequency')
	.sect	'.debug_line'
.L398:
	.word	.L1224-.L1223
.L1223:
	.half	3
	.word	.L1226-.L1225
.L1225:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuCcu.c',0,0,0,0,0
.L1226:
	.byte	5,9,7,0,5,2
	.word	.L292
	.byte	3,171,8,1,5,40,9
	.half	.L1227-.L292
	.byte	3,6,1,5,13,9
	.half	.L887-.L1227
	.byte	1,5,17,9
	.half	.L889-.L887
	.byte	3,2,1,5,5,9
	.half	.L1228-.L889
	.byte	1,5,38,7,9
	.half	.L1229-.L1228
	.byte	3,6,1,5,36,9
	.half	.L891-.L1229
	.byte	1,5,42,9
	.half	.L892-.L891
	.byte	1,5,18,9
	.half	.L1230-.L892
	.byte	1,5,19,9
	.half	.L893-.L1230
	.byte	3,124,1,5,16,9
	.half	.L188-.L893
	.byte	1,5,57,9
	.half	.L189-.L188
	.byte	3,7,1,5,20,9
	.half	.L888-.L189
	.byte	1,5,38,9
	.half	.L895-.L888
	.byte	3,3,1,5,14,9
	.half	.L896-.L895
	.byte	3,4,1,9
	.half	.L898-.L896
	.byte	3,3,1,5,27,9
	.half	.L190-.L898
	.byte	3,126,1,5,13,9
	.half	.L1231-.L190
	.byte	3,1,1,5,27,9
	.half	.L191-.L1231
	.byte	3,2,1,5,13,9
	.half	.L1232-.L191
	.byte	3,1,1,9
	.half	.L192-.L1232
	.byte	3,3,1,5,36,9
	.half	.L193-.L192
	.byte	3,3,1,5,5,9
	.half	.L902-.L193
	.byte	3,3,1,5,30,7,9
	.half	.L1233-.L902
	.byte	3,2,1,5,39,9
	.half	.L1234-.L1233
	.byte	1,5,37,9
	.half	.L1235-.L1234
	.byte	1,5,27,9
	.half	.L1236-.L1235
	.byte	1,5,5,9
	.half	.L196-.L1236
	.byte	3,3,1,5,1,9
	.half	.L197-.L196
	.byte	3,1,1,7,9
	.half	.L400-.L197
	.byte	0,1,1
.L1224:
	.sdecl	'.debug_ranges',debug,cluster('IfxScuCcu_setCpuFrequency')
	.sect	'.debug_ranges'
.L399:
	.word	-1,.L292,0,.L400-.L292,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScuCcu_setPll2ErayFrequency')
	.sect	'.debug_info'
.L401:
	.word	433
	.half	3
	.word	.L402
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuCcu.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L404,.L403
	.byte	2
	.word	.L307
	.byte	3
	.byte	'IfxScuCcu_setPll2ErayFrequency',0,1,255,8,9
	.word	.L474
	.byte	1,1,1
	.word	.L296,.L539,.L295
	.byte	4
	.byte	'pll2ErayFreq',0,1,255,8,48
	.word	.L474,.L540
	.byte	5
	.word	.L296,.L539
	.byte	6
	.byte	'password',0,1,129,9,12
	.word	.L535,.L541
	.byte	6
	.byte	'pll2Div',0,1,130,9,12
	.word	.L489,.L542
	.byte	7
	.word	.L543,.L544,.L208
	.byte	8
	.word	.L545,.L544,.L208
	.byte	6
	.byte	'pll2ErayFrequency',0,2,203,8,13
	.word	.L474,.L546
	.byte	0,0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScuCcu_setPll2ErayFrequency')
	.sect	'.debug_abbrev'
.L402:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,11,1,49,16,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxScuCcu_setPll2ErayFrequency')
	.sect	'.debug_line'
.L403:
	.word	.L1238-.L1237
.L1237:
	.half	3
	.word	.L1240-.L1239
.L1239:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuCcu.c',0,0,0,0
	.byte	'..\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Scu\\Std\\IfxScuCcu.h',0,0,0,0,0
.L1240:
	.byte	5,9,7,0,5,2
	.word	.L296
	.byte	3,254,8,1,5,58,9
	.half	.L916-.L296
	.byte	3,2,1,5,21,9
	.half	.L915-.L916
	.byte	1,5,65,9
	.half	.L919-.L915
	.byte	3,1,1,5,68,9
	.half	.L918-.L919
	.byte	1,5,86,9
	.half	.L917-.L918
	.byte	1,5,84,9
	.half	.L1241-.L917
	.byte	1,5,23,9
	.half	.L1242-.L1241
	.byte	1,5,38,9
	.half	.L920-.L1242
	.byte	3,2,1,5,26,9
	.half	.L923-.L920
	.byte	3,1,1,5,35,9
	.half	.L1243-.L923
	.byte	1,5,33,9
	.half	.L921-.L1243
	.byte	1,5,36,9
	.half	.L1244-.L921
	.byte	3,1,1,4,2,5,57,9
	.half	.L544-.L1244
	.byte	3,71,1,5,80,9
	.half	.L1245-.L544
	.byte	1,5,87,9
	.half	.L1246-.L1245
	.byte	1,5,60,9
	.half	.L1247-.L1246
	.byte	1,5,5,9
	.half	.L925-.L1247
	.byte	3,2,1,4,1,9
	.half	.L208-.L925
	.byte	3,57,1,5,1,9
	.half	.L209-.L208
	.byte	3,1,1,7,9
	.half	.L405-.L209
	.byte	0,1,1
.L1238:
	.sdecl	'.debug_ranges',debug,cluster('IfxScuCcu_setPll2ErayFrequency')
	.sect	'.debug_ranges'
.L404:
	.word	-1,.L296,0,.L405-.L296,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScuCcu_setPll2Frequency')
	.sect	'.debug_info'
.L406:
	.word	427
	.half	3
	.word	.L407
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuCcu.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L409,.L408
	.byte	2
	.word	.L307
	.byte	3
	.byte	'IfxScuCcu_setPll2Frequency',0,1,140,9,9
	.word	.L474
	.byte	1,1,1
	.word	.L298,.L547,.L297
	.byte	4
	.byte	'pll2Freq',0,1,140,9,44
	.word	.L474,.L548
	.byte	5
	.word	.L298,.L547
	.byte	6
	.byte	'endinitSfty_pw',0,1,142,9,12
	.word	.L535,.L549
	.byte	6
	.byte	'pll2Div',0,1,143,9,12
	.word	.L489,.L550
	.byte	7
	.word	.L551,.L552,.L210
	.byte	8
	.word	.L553,.L552,.L210
	.byte	6
	.byte	'pll2Frequency',0,2,213,8,13
	.word	.L474,.L554
	.byte	0,0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScuCcu_setPll2Frequency')
	.sect	'.debug_abbrev'
.L407:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,11,1,49,16,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxScuCcu_setPll2Frequency')
	.sect	'.debug_line'
.L408:
	.word	.L1249-.L1248
.L1248:
	.half	3
	.word	.L1251-.L1250
.L1250:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuCcu.c',0,0,0,0
	.byte	'..\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Scu\\Std\\IfxScuCcu.h',0,0,0,0,0
.L1251:
	.byte	5,9,7,0,5,2
	.word	.L298
	.byte	3,139,9,1,5,64,9
	.half	.L927-.L298
	.byte	3,2,1,5,27,9
	.half	.L926-.L927
	.byte	1,5,67,9
	.half	.L930-.L926
	.byte	3,1,1,5,70,9
	.half	.L929-.L930
	.byte	1,5,84,9
	.half	.L928-.L929
	.byte	1,5,82,9
	.half	.L1252-.L928
	.byte	1,5,29,9
	.half	.L1253-.L1252
	.byte	1,5,38,9
	.half	.L931-.L1253
	.byte	3,2,1,5,22,9
	.half	.L934-.L931
	.byte	3,1,1,5,31,9
	.half	.L1254-.L934
	.byte	1,5,29,9
	.half	.L932-.L1254
	.byte	1,5,36,9
	.half	.L1255-.L932
	.byte	3,1,1,4,2,5,49,9
	.half	.L552-.L1255
	.byte	3,67,1,5,68,9
	.half	.L1256-.L552
	.byte	1,5,75,9
	.half	.L1257-.L1256
	.byte	1,5,52,9
	.half	.L1258-.L1257
	.byte	1,5,5,9
	.half	.L936-.L1258
	.byte	3,2,1,4,1,9
	.half	.L210-.L936
	.byte	3,61,1,5,1,9
	.half	.L211-.L210
	.byte	3,1,1,7,9
	.half	.L410-.L211
	.byte	0,1,1
.L1249:
	.sdecl	'.debug_ranges',debug,cluster('IfxScuCcu_setPll2Frequency')
	.sect	'.debug_ranges'
.L409:
	.word	-1,.L298,0,.L410-.L298,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScuCcu_setSpbFrequency')
	.sect	'.debug_info'
.L411:
	.word	436
	.half	3
	.word	.L412
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuCcu.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L414,.L413
	.byte	2
	.word	.L307
	.byte	3
	.byte	'IfxScuCcu_setSpbFrequency',0,1,153,9,9
	.word	.L474
	.byte	1,1,1
	.word	.L300,.L555,.L299
	.byte	4
	.byte	'spbFreq',0,1,153,9,43
	.word	.L474,.L556
	.byte	5
	.word	.L300,.L555
	.byte	6
	.byte	'l_EndInitPW',0,1,156,9,21
	.word	.L535,.L557
	.byte	6
	.byte	'l_SEndInitPW',0,1,157,9,21
	.word	.L535,.L558
	.byte	6
	.byte	'ccucon0',0,1,158,9,21
	.word	.L477,.L559
	.byte	6
	.byte	'inputFreq',0,1,159,9,21
	.word	.L474,.L560
	.byte	6
	.byte	'spbDiv',0,1,160,9,21
	.word	.L489,.L561
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScuCcu_setSpbFrequency')
	.sect	'.debug_abbrev'
.L412:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxScuCcu_setSpbFrequency')
	.sect	'.debug_line'
.L413:
	.word	.L1260-.L1259
.L1259:
	.half	3
	.word	.L1262-.L1261
.L1261:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuCcu.c',0,0,0,0,0
.L1262:
	.byte	5,9,7,0,5,2
	.word	.L300
	.byte	3,152,9,1,5,61,9
	.half	.L938-.L300
	.byte	3,6,1,5,52,9
	.half	.L937-.L938
	.byte	3,1,1,5,33,9
	.half	.L939-.L937
	.byte	1,5,20,9
	.half	.L941-.L939
	.byte	3,1,1,5,9,9
	.half	.L942-.L941
	.byte	3,2,1,5,34,7,9
	.half	.L1263-.L942
	.byte	1,5,57,7,9
	.half	.L1264-.L1263
	.byte	1,5,25,7,9
	.half	.L1265-.L1264
	.byte	3,2,1,5,19,9
	.half	.L212-.L1265
	.byte	3,3,1,5,5,9
	.half	.L1266-.L212
	.byte	1,5,16,7,9
	.half	.L1267-.L1266
	.byte	3,2,1,5,52,9
	.half	.L215-.L1267
	.byte	3,3,1,5,18,9
	.half	.L940-.L215
	.byte	1,5,55,9
	.half	.L944-.L940
	.byte	3,1,1,5,18,9
	.half	.L943-.L944
	.byte	1,5,31,9
	.half	.L946-.L943
	.byte	3,2,1,5,32,9
	.half	.L945-.L946
	.byte	3,1,1,5,37,9
	.half	.L1268-.L945
	.byte	1,5,35,9
	.half	.L1269-.L1268
	.byte	1,5,19,9
	.half	.L1270-.L1269
	.byte	1,5,29,9
	.half	.L1271-.L1270
	.byte	3,1,1,5,34,9
	.half	.L949-.L1271
	.byte	3,2,1,5,35,9
	.half	.L951-.L949
	.byte	3,2,1,5,25,9
	.half	.L216-.L951
	.byte	1,5,35,9
	.half	.L1272-.L216
	.byte	1,7,9
	.half	.L1273-.L1272
	.byte	3,3,1,5,24,9
	.half	.L952-.L1273
	.byte	3,1,1,5,22,9
	.half	.L1274-.L952
	.byte	1,5,24,9
	.half	.L1275-.L1274
	.byte	3,1,1,5,22,9
	.half	.L1276-.L1275
	.byte	1,9
	.half	.L1277-.L1276
	.byte	3,1,1,5,32,9
	.half	.L1278-.L1277
	.byte	3,1,1,5,31,9
	.half	.L955-.L1278
	.byte	3,2,1,5,32,9
	.half	.L957-.L955
	.byte	3,1,1,5,35,9
	.half	.L953-.L957
	.byte	1,5,19,9
	.half	.L1279-.L953
	.byte	1,5,29,9
	.half	.L1280-.L1279
	.byte	3,1,1,5,35,9
	.half	.L959-.L1280
	.byte	3,2,1,5,25,9
	.half	.L218-.L959
	.byte	1,5,35,9
	.half	.L1281-.L218
	.byte	1,5,37,7,9
	.half	.L1282-.L1281
	.byte	3,3,1,5,5,9
	.half	.L1283-.L1282
	.byte	1,5,1,9
	.half	.L220-.L1283
	.byte	3,1,1,7,9
	.half	.L415-.L220
	.byte	0,1,1
.L1260:
	.sdecl	'.debug_ranges',debug,cluster('IfxScuCcu_setSpbFrequency')
	.sect	'.debug_ranges'
.L414:
	.word	-1,.L300,0,.L415-.L300,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScuCcu_setSriFrequency')
	.sect	'.debug_info'
.L416:
	.word	426
	.half	3
	.word	.L417
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuCcu.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L419,.L418
	.byte	2
	.word	.L307
	.byte	3
	.byte	'IfxScuCcu_setSriFrequency',0,1,202,9,9
	.word	.L474
	.byte	1,1,1
	.word	.L302,.L562,.L301
	.byte	4
	.byte	'sriFreq',0,1,202,9,43
	.word	.L474,.L563
	.byte	5
	.word	.L302,.L562
	.byte	6
	.byte	'freq',0,1,204,9,21
	.word	.L474,.L564
	.byte	6
	.byte	'source',0,1,205,9,21
	.word	.L474,.L565
	.byte	6
	.byte	'ccucon0',0,1,206,9,21
	.word	.L477,.L566
	.byte	6
	.byte	'l_SEndInitPW',0,1,207,9,21
	.word	.L535,.L567
	.byte	6
	.byte	'sriDiv',0,1,208,9,21
	.word	.L489,.L568
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScuCcu_setSriFrequency')
	.sect	'.debug_abbrev'
.L417:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxScuCcu_setSriFrequency')
	.sect	'.debug_line'
.L418:
	.word	.L1285-.L1284
.L1284:
	.half	3
	.word	.L1287-.L1286
.L1286:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuCcu.c',0,0,0,0,0
.L1287:
	.byte	5,9,7,0,5,2
	.word	.L302
	.byte	3,201,9,1,5,58,9
	.half	.L961-.L302
	.byte	3,3,1,5,28,9
	.half	.L960-.L961
	.byte	1,5,38,9
	.half	.L964-.L960
	.byte	3,3,1,5,20,9
	.half	.L222-.L964
	.byte	3,1,1,5,9,9
	.half	.L962-.L222
	.byte	3,2,1,5,34,7,9
	.half	.L1288-.L962
	.byte	1,5,57,7,9
	.half	.L1289-.L1288
	.byte	1,5,25,7,9
	.half	.L1290-.L1289
	.byte	3,2,1,5,19,9
	.half	.L223-.L1290
	.byte	3,3,1,5,5,9
	.half	.L1291-.L223
	.byte	1,5,16,7,9
	.half	.L1292-.L1291
	.byte	3,2,1,5,55,9
	.half	.L226-.L1292
	.byte	3,3,1,5,18,9
	.half	.L966-.L226
	.byte	1,5,34,9
	.half	.L965-.L966
	.byte	3,1,1,5,35,9
	.half	.L968-.L965
	.byte	3,2,1,5,25,9
	.half	.L227-.L968
	.byte	1,5,35,9
	.half	.L1293-.L227
	.byte	1,7,9
	.half	.L1294-.L1293
	.byte	3,3,1,5,24,9
	.half	.L969-.L1294
	.byte	3,1,1,5,22,9
	.half	.L1295-.L969
	.byte	1,5,24,9
	.half	.L1296-.L1295
	.byte	3,1,1,5,22,9
	.half	.L1297-.L1296
	.byte	1,9
	.half	.L1298-.L1297
	.byte	3,1,1,5,32,9
	.half	.L1299-.L1298
	.byte	3,2,1,5,35,9
	.half	.L971-.L1299
	.byte	3,2,1,5,25,9
	.half	.L229-.L971
	.byte	1,5,35,9
	.half	.L1300-.L229
	.byte	1,5,37,7,9
	.half	.L1301-.L1300
	.byte	3,3,1,5,5,9
	.half	.L972-.L1301
	.byte	3,1,1,5,1,9
	.half	.L231-.L972
	.byte	3,1,1,7,9
	.half	.L420-.L231
	.byte	0,1,1
.L1285:
	.sdecl	'.debug_ranges',debug,cluster('IfxScuCcu_setSriFrequency')
	.sect	'.debug_ranges'
.L419:
	.word	-1,.L302,0,.L420-.L302,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScuCcu_calculateSysPllDividers')
	.sect	'.debug_info'
.L421:
	.word	849
	.half	3
	.word	.L422
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuCcu.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L424,.L423
	.byte	2
	.word	.L307
	.byte	3
	.byte	'IfxScuCcu_calculateSysPllDividers',0,1,113,9
	.word	.L569
	.byte	1,1,1
	.word	.L246,.L570,.L245
	.byte	4
	.byte	'cfg',0,1,113,61
	.word	.L571,.L572
	.byte	4
	.byte	'fPll',0,1,113,73
	.word	.L489,.L573
	.byte	5
	.word	.L246,.L570
	.byte	6
	.byte	'retVal',0,1,115,13
	.word	.L569,.L574
	.byte	6
	.byte	'deviationAllowed',0,1,116,13
	.word	.L569,.L575
	.byte	6
	.byte	'fOsc',0,1,117,13
	.word	.L489,.L576
	.byte	5
	.word	.L577,.L17
	.byte	6
	.byte	'fPllMax',0,1,125,22
	.word	.L578,.L579
	.byte	6
	.byte	'fRefMax',0,1,126,22
	.word	.L580,.L581
	.byte	6
	.byte	'fRefMin',0,1,127,22
	.word	.L582,.L583
	.byte	6
	.byte	'fVcoMin',0,1,128,1,22
	.word	.L584,.L585
	.byte	6
	.byte	'fVcoMax',0,1,129,1,22
	.word	.L586,.L587
	.byte	6
	.byte	'pMin',0,1,130,1,22
	.word	.L588,.L589
	.byte	6
	.byte	'pMax',0,1,131,1,22
	.word	.L590,.L591
	.byte	6
	.byte	'k2Min',0,1,132,1,22
	.word	.L592,.L593
	.byte	6
	.byte	'k2Max',0,1,133,1,22
	.word	.L594,.L595
	.byte	6
	.byte	'nMin',0,1,134,1,22
	.word	.L596,.L597
	.byte	6
	.byte	'nMax',0,1,135,1,22
	.word	.L598,.L599
	.byte	6
	.byte	'p',0,1,137,1,22
	.word	.L489,.L600
	.byte	6
	.byte	'n',0,1,138,1,22
	.word	.L489,.L601
	.byte	6
	.byte	'k2',0,1,139,1,22
	.word	.L489,.L602
	.byte	6
	.byte	'k2Steps',0,1,140,1,22
	.word	.L489,.L603
	.byte	6
	.byte	'bestK2',0,1,141,1,22
	.word	.L489,.L604
	.byte	6
	.byte	'bestN',0,1,141,1,34
	.word	.L489,.L605
	.byte	6
	.byte	'bestP',0,1,141,1,45
	.word	.L489,.L606
	.byte	6
	.byte	'fRef',0,1,143,1,22
	.word	.L607,.L608
	.byte	6
	.byte	'fVco',0,1,143,1,28
	.word	.L607,.L609
	.byte	6
	.byte	'fPllLeastError',0,1,144,1,22
	.word	.L607,.L610
	.byte	5
	.word	.L12,.L15
	.byte	6
	.byte	'fPllError',0,1,170,1,36
	.word	.L607,.L611
	.byte	0,0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScuCcu_calculateSysPllDividers')
	.sect	'.debug_abbrev'
.L422:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxScuCcu_calculateSysPllDividers')
	.sect	'.debug_line'
.L423:
	.word	.L1303-.L1302
.L1302:
	.half	3
	.word	.L1305-.L1304
.L1304:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuCcu.c',0,0,0,0,0
.L1305:
	.byte	5,9,7,0,5,2
	.word	.L246
	.byte	3,240,0,1,5,30,9
	.half	.L693-.L246
	.byte	3,2,1,5,35,9
	.half	.L696-.L693
	.byte	3,2,1,5,32,9
	.half	.L577-.L696
	.byte	3,8,1,9
	.half	.L1306-.L577
	.byte	3,1,1,9
	.half	.L699-.L1306
	.byte	3,1,1,9
	.half	.L701-.L699
	.byte	3,1,1,9
	.half	.L703-.L701
	.byte	3,1,1,5,30,9
	.half	.L705-.L703
	.byte	3,2,1,9
	.half	.L706-.L705
	.byte	3,4,1,5,29,9
	.half	.L707-.L706
	.byte	3,6,1,5,40,9
	.half	.L709-.L707
	.byte	1,5,51,9
	.half	.L711-.L709
	.byte	1,5,26,9
	.half	.L713-.L711
	.byte	3,5,1,5,17,9
	.half	.L714-.L713
	.byte	3,3,1,5,20,9
	.half	.L716-.L714
	.byte	3,2,1,5,9,9
	.half	.L1307-.L716
	.byte	1,5,21,7,9
	.half	.L1308-.L1307
	.byte	3,2,1,5,33,9
	.half	.L2-.L1308
	.byte	3,3,1,5,26,9
	.half	.L4-.L2
	.byte	3,2,1,9
	.half	.L1309-.L4
	.byte	3,2,1,5,23,9
	.half	.L720-.L1309
	.byte	1,5,17,9
	.half	.L1310-.L720
	.byte	1,5,47,7,9
	.half	.L1311-.L1310
	.byte	1,5,44,9
	.half	.L721-.L1311
	.byte	1,5,25,9
	.half	.L1312-.L721
	.byte	3,2,1,5,45,9
	.half	.L724-.L1312
	.byte	1,5,29,9
	.half	.L8-.L724
	.byte	3,2,1,5,45,9
	.half	.L726-.L8
	.byte	1,5,43,9
	.half	.L728-.L726
	.byte	1,5,34,9
	.half	.L727-.L728
	.byte	3,2,1,5,31,9
	.half	.L730-.L727
	.byte	1,5,25,9
	.half	.L1313-.L730
	.byte	1,5,55,7,9
	.half	.L1314-.L1313
	.byte	1,5,52,9
	.half	.L731-.L1314
	.byte	1,5,32,9
	.half	.L1315-.L731
	.byte	3,2,1,5,49,9
	.half	.L734-.L1315
	.byte	1,5,53,9
	.half	.L12-.L734
	.byte	3,3,1,5,48,9
	.half	.L1316-.L12
	.byte	1,5,60,9
	.half	.L1317-.L1316
	.byte	1,5,68,9
	.half	.L1318-.L1317
	.byte	1,5,43,9
	.half	.L735-.L1318
	.byte	3,2,1,5,29,9
	.half	.L1319-.L735
	.byte	1,5,48,7,9
	.half	.L1320-.L1319
	.byte	3,2,1,9
	.half	.L1321-.L1320
	.byte	3,1,1,9
	.half	.L1322-.L1321
	.byte	3,1,1,9
	.half	.L1323-.L1322
	.byte	3,1,1,5,33,9
	.half	.L1324-.L1323
	.byte	3,2,1,5,48,9
	.half	.L13-.L1324
	.byte	3,3,1,5,29,9
	.half	.L1325-.L13
	.byte	1,5,48,7,9
	.half	.L1326-.L1325
	.byte	3,2,1,9
	.half	.L1327-.L1326
	.byte	3,1,1,9
	.half	.L1328-.L1327
	.byte	3,1,1,9
	.half	.L1329-.L1328
	.byte	3,1,1,5,52,9
	.half	.L15-.L1329
	.byte	3,108,1,5,49,9
	.half	.L11-.L15
	.byte	1,5,50,7,9
	.half	.L9-.L11
	.byte	3,122,1,5,30,9
	.half	.L7-.L9
	.byte	3,99,1,5,45,9
	.half	.L737-.L7
	.byte	3,29,1,5,36,7,9
	.half	.L5-.L737
	.byte	3,122,1,5,30,9
	.half	.L3-.L5
	.byte	3,102,1,5,33,9
	.half	.L738-.L3
	.byte	3,26,1,5,39,7,9
	.half	.L14-.L738
	.byte	3,42,1,5,61,9
	.half	.L1330-.L14
	.byte	1,5,59,9
	.half	.L1331-.L1330
	.byte	1,5,30,9
	.half	.L1332-.L1331
	.byte	1,5,9,9
	.half	.L1333-.L1332
	.byte	1,5,66,7,9
	.half	.L1334-.L1333
	.byte	3,2,1,5,50,9
	.half	.L740-.L1334
	.byte	1,5,66,9
	.half	.L1335-.L740
	.byte	3,1,1,5,50,9
	.half	.L742-.L1335
	.byte	1,5,67,9
	.half	.L1336-.L742
	.byte	3,1,1,5,50,9
	.half	.L744-.L1336
	.byte	1,5,52,9
	.half	.L1337-.L744
	.byte	3,1,1,5,50,9
	.half	.L1338-.L1337
	.byte	1,5,52,9
	.half	.L1339-.L1338
	.byte	3,1,1,5,50,9
	.half	.L1340-.L1339
	.byte	1,5,70,9
	.half	.L1341-.L1340
	.byte	3,124,1,5,20,9
	.half	.L16-.L1341
	.byte	3,8,1,5,5,9
	.half	.L17-.L16
	.byte	3,3,1,5,1,9
	.half	.L18-.L17
	.byte	3,1,1,7,9
	.half	.L425-.L18
	.byte	0,1,1
.L1303:
	.sdecl	'.debug_ranges',debug,cluster('IfxScuCcu_calculateSysPllDividers')
	.sect	'.debug_ranges'
.L424:
	.word	-1,.L246,0,.L425-.L246,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScuCcu_init')
	.sect	'.debug_info'
.L426:
	.word	656
	.half	3
	.word	.L427
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuCcu.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L429,.L428
	.byte	2
	.word	.L307
	.byte	3
	.byte	'IfxScuCcu_init',0,1,147,5,9
	.word	.L569
	.byte	1,1,1
	.word	.L282,.L612,.L281
	.byte	4
	.byte	'cfg',0,1,147,5,48
	.word	.L613,.L614
	.byte	5
	.word	.L282,.L612
	.byte	6
	.byte	'smuTrapEnable',0,1,149,5,13
	.word	.L569,.L615
	.byte	6
	.byte	'endinit_pw',0,1,150,5,13
	.word	.L535,.L616
	.byte	6
	.byte	'endinitSfty_pw',0,1,150,5,25
	.word	.L535,.L617
	.byte	6
	.byte	'status',0,1,151,5,13
	.word	.L569,.L618
	.byte	5
	.word	.L619,.L142
	.byte	6
	.byte	'pllStepsCount',0,1,201,5,15
	.word	.L569,.L620
	.byte	5
	.word	.L621,.L622
	.byte	6
	.byte	'ccucon0',0,1,135,6,41
	.word	.L477,.L623
	.byte	0,5
	.word	.L624,.L625
	.byte	6
	.byte	'ccucon1',0,1,152,6,41
	.word	.L626,.L627
	.byte	0,5
	.word	.L628,.L629
	.byte	6
	.byte	'ccucon2',0,1,169,6,41
	.word	.L630,.L631
	.byte	0,5
	.word	.L632,.L633
	.byte	6
	.byte	'ccucon5',0,1,183,6,41
	.word	.L634,.L635
	.byte	0,5
	.word	.L633,.L636
	.byte	6
	.byte	'ccucon6',0,1,192,6,41
	.word	.L637,.L638
	.byte	0,5
	.word	.L636,.L639
	.byte	6
	.byte	'ccucon7',0,1,201,6,41
	.word	.L640,.L641
	.byte	0,5
	.word	.L642,.L643
	.byte	6
	.byte	'fcon',0,1,214,6,28
	.word	.L644,.L645
	.byte	0,0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScuCcu_init')
	.sect	'.debug_abbrev'
.L427:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxScuCcu_init')
	.sect	'.debug_line'
.L428:
	.word	.L1343-.L1342
.L1342:
	.half	3
	.word	.L1345-.L1344
.L1344:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuCcu.c',0,0,0,0,0
.L1345:
	.byte	5,9,7,0,5,2
	.word	.L282
	.byte	3,146,5,1,5,20,9
	.half	.L803-.L282
	.byte	3,4,1,5,5,9
	.half	.L805-.L803
	.byte	3,2,1,5,34,9
	.half	.L1346-.L805
	.byte	1,5,29,9
	.half	.L1347-.L1346
	.byte	1,5,63,9
	.half	.L1348-.L1347
	.byte	3,2,1,5,29,9
	.half	.L802-.L1348
	.byte	1,5,66,9
	.half	.L807-.L802
	.byte	3,1,1,5,29,9
	.half	.L806-.L807
	.byte	1,5,35,9
	.half	.L809-.L806
	.byte	3,4,1,5,43,9
	.half	.L808-.L809
	.byte	3,1,1,5,22,9
	.half	.L811-.L808
	.byte	3,1,1,5,28,9
	.half	.L1349-.L811
	.byte	1,5,33,9
	.half	.L1350-.L1349
	.byte	3,1,1,5,38,9
	.half	.L813-.L1350
	.byte	3,5,1,5,39,9
	.half	.L815-.L813
	.byte	3,2,1,5,29,9
	.half	.L138-.L815
	.byte	1,5,39,9
	.half	.L1351-.L138
	.byte	1,5,22,7,9
	.half	.L1352-.L1351
	.byte	3,6,1,5,30,9
	.half	.L1353-.L1352
	.byte	1,5,22,9
	.half	.L1354-.L1353
	.byte	3,1,1,5,30,9
	.half	.L1355-.L1354
	.byte	1,5,22,9
	.half	.L1356-.L1355
	.byte	3,3,1,5,33,9
	.half	.L1357-.L1356
	.byte	1,5,39,9
	.half	.L1358-.L1357
	.byte	3,4,1,5,29,9
	.half	.L140-.L1358
	.byte	1,5,39,9
	.half	.L1359-.L140
	.byte	1,5,22,7,9
	.half	.L1360-.L1359
	.byte	3,6,1,5,29,9
	.half	.L1361-.L1360
	.byte	1,5,22,9
	.half	.L1362-.L1361
	.byte	3,1,1,5,29,9
	.half	.L1363-.L1362
	.byte	1,5,59,9
	.half	.L1364-.L1363
	.byte	3,2,1,5,28,9
	.half	.L1365-.L1364
	.byte	1,5,36,9
	.half	.L1366-.L1365
	.byte	3,2,1,5,5,9
	.half	.L817-.L1366
	.byte	3,3,1,5,46,7,9
	.half	.L619-.L817
	.byte	3,9,1,5,49,9
	.half	.L819-.L619
	.byte	3,2,1,5,37,9
	.half	.L143-.L819
	.byte	1,5,49,9
	.half	.L1367-.L143
	.byte	1,5,30,7,9
	.half	.L1368-.L1367
	.byte	3,6,1,5,65,9
	.half	.L1369-.L1368
	.byte	1,5,37,9
	.half	.L1370-.L1369
	.byte	1,5,34,9
	.half	.L1371-.L1370
	.byte	3,4,1,5,68,9
	.half	.L1372-.L1371
	.byte	1,5,40,9
	.half	.L1373-.L1372
	.byte	1,5,34,9
	.half	.L1374-.L1373
	.byte	3,1,1,5,68,9
	.half	.L1375-.L1374
	.byte	1,5,40,9
	.half	.L1376-.L1375
	.byte	1,5,34,9
	.half	.L1377-.L1376
	.byte	3,4,1,5,46,9
	.half	.L1378-.L1377
	.byte	1,5,34,9
	.half	.L1379-.L1378
	.byte	3,2,1,5,46,9
	.half	.L1380-.L1379
	.byte	1,5,34,9
	.half	.L1381-.L1380
	.byte	3,2,1,5,46,9
	.half	.L1382-.L1381
	.byte	1,5,34,9
	.half	.L1383-.L1382
	.byte	3,1,1,5,46,9
	.half	.L1384-.L1383
	.byte	1,5,34,9
	.half	.L1385-.L1384
	.byte	3,3,1,5,41,9
	.half	.L1386-.L1385
	.byte	1,5,36,9
	.half	.L1387-.L1386
	.byte	3,2,1,5,55,9
	.half	.L1388-.L1387
	.byte	3,2,1,5,41,9
	.half	.L145-.L1388
	.byte	1,5,55,9
	.half	.L1389-.L145
	.byte	1,5,34,7,9
	.half	.L1390-.L1389
	.byte	3,6,1,5,42,9
	.half	.L1391-.L1390
	.byte	1,5,51,9
	.half	.L1392-.L1391
	.byte	3,2,1,5,41,9
	.half	.L147-.L1392
	.byte	1,5,51,9
	.half	.L1393-.L147
	.byte	1,5,34,7,9
	.half	.L1394-.L1393
	.byte	3,6,1,5,42,9
	.half	.L1395-.L1394
	.byte	1,5,51,9
	.half	.L1396-.L1395
	.byte	3,3,1,5,41,9
	.half	.L149-.L1396
	.byte	1,5,51,9
	.half	.L1397-.L149
	.byte	1,5,62,7,9
	.half	.L1398-.L1397
	.byte	3,7,1,5,55,9
	.half	.L621-.L1398
	.byte	3,4,1,5,91,9
	.half	.L1399-.L621
	.byte	1,5,60,9
	.half	.L1400-.L1399
	.byte	1,5,58,9
	.half	.L820-.L1400
	.byte	1,5,75,9
	.half	.L1401-.L820
	.byte	3,2,1,5,113,9
	.half	.L1402-.L1401
	.byte	1,5,81,9
	.half	.L1403-.L1402
	.byte	1,5,41,9
	.half	.L1404-.L1403
	.byte	1,5,44,9
	.half	.L1405-.L1404
	.byte	3,1,1,5,42,9
	.half	.L1406-.L1405
	.byte	1,5,44,9
	.half	.L1407-.L1406
	.byte	3,1,1,5,42,9
	.half	.L1408-.L1407
	.byte	1,5,25,9
	.half	.L1409-.L1408
	.byte	3,1,1,5,42,9
	.half	.L1410-.L1409
	.byte	1,5,51,9
	.half	.L622-.L1410
	.byte	3,3,1,5,41,9
	.half	.L151-.L622
	.byte	1,5,51,9
	.half	.L1411-.L151
	.byte	1,5,54,7,9
	.half	.L624-.L1411
	.byte	3,9,1,5,90,9
	.half	.L821-.L624
	.byte	1,5,59,9
	.half	.L1412-.L821
	.byte	1,5,57,9
	.half	.L822-.L1412
	.byte	1,5,74,9
	.half	.L1413-.L822
	.byte	3,2,1,5,112,9
	.half	.L1414-.L1413
	.byte	1,5,80,9
	.half	.L1415-.L1414
	.byte	1,5,40,9
	.half	.L1416-.L1415
	.byte	1,5,43,9
	.half	.L1417-.L1416
	.byte	3,1,1,5,41,9
	.half	.L1418-.L1417
	.byte	1,5,43,9
	.half	.L1419-.L1418
	.byte	3,1,1,5,41,9
	.half	.L1420-.L1419
	.byte	1,5,25,9
	.half	.L1421-.L1420
	.byte	3,1,1,5,41,9
	.half	.L1422-.L1421
	.byte	1,5,51,9
	.half	.L625-.L1422
	.byte	3,3,1,5,41,9
	.half	.L153-.L625
	.byte	1,5,51,9
	.half	.L1423-.L153
	.byte	1,7,9
	.half	.L628-.L1423
	.byte	3,9,1,5,87,9
	.half	.L823-.L628
	.byte	1,5,56,9
	.half	.L1424-.L823
	.byte	1,5,54,9
	.half	.L824-.L1424
	.byte	1,5,71,9
	.half	.L1425-.L824
	.byte	3,2,1,5,109,9
	.half	.L1426-.L1425
	.byte	1,5,77,9
	.half	.L1427-.L1426
	.byte	1,5,37,9
	.half	.L1428-.L1427
	.byte	1,5,40,9
	.half	.L1429-.L1428
	.byte	3,1,1,5,38,9
	.half	.L1430-.L1429
	.byte	1,5,25,9
	.half	.L1431-.L1430
	.byte	3,1,1,5,38,9
	.half	.L1432-.L1431
	.byte	1,5,51,9
	.half	.L629-.L1432
	.byte	3,3,1,5,41,9
	.half	.L155-.L629
	.byte	1,5,51,9
	.half	.L1433-.L155
	.byte	1,7,9
	.half	.L632-.L1433
	.byte	3,7,1,5,87,9
	.half	.L825-.L632
	.byte	1,5,56,9
	.half	.L1434-.L825
	.byte	1,5,54,9
	.half	.L826-.L1434
	.byte	1,5,71,9
	.half	.L1435-.L826
	.byte	3,2,1,5,109,9
	.half	.L1436-.L1435
	.byte	1,5,77,9
	.half	.L1437-.L1436
	.byte	1,5,37,9
	.half	.L1438-.L1437
	.byte	1,5,40,9
	.half	.L1439-.L1438
	.byte	3,1,1,5,38,9
	.half	.L1440-.L1439
	.byte	1,5,25,9
	.half	.L1441-.L1440
	.byte	3,1,1,5,38,9
	.half	.L1442-.L1441
	.byte	1,5,50,9
	.half	.L633-.L1442
	.byte	3,5,1,5,86,9
	.half	.L827-.L633
	.byte	1,5,55,9
	.half	.L1443-.L827
	.byte	1,5,53,9
	.half	.L828-.L1443
	.byte	1,5,70,9
	.half	.L1444-.L828
	.byte	3,2,1,5,108,9
	.half	.L1445-.L1444
	.byte	1,5,76,9
	.half	.L1446-.L1445
	.byte	1,5,36,9
	.half	.L1447-.L1446
	.byte	1,5,25,9
	.half	.L1448-.L1447
	.byte	3,1,1,5,37,9
	.half	.L1449-.L1448
	.byte	1,5,50,9
	.half	.L636-.L1449
	.byte	3,6,1,5,86,9
	.half	.L829-.L636
	.byte	1,5,55,9
	.half	.L1450-.L829
	.byte	1,5,53,9
	.half	.L830-.L1450
	.byte	1,5,70,9
	.half	.L1451-.L830
	.byte	3,2,1,5,108,9
	.half	.L1452-.L1451
	.byte	1,5,76,9
	.half	.L1453-.L1452
	.byte	1,5,36,9
	.half	.L1454-.L1453
	.byte	1,5,25,9
	.half	.L1455-.L1454
	.byte	3,1,1,5,37,9
	.half	.L1456-.L1455
	.byte	1,5,44,9
	.half	.L639-.L1456
	.byte	3,4,1,5,33,9
	.half	.L642-.L639
	.byte	3,6,1,5,68,9
	.half	.L1457-.L642
	.byte	1,5,38,9
	.half	.L1458-.L1457
	.byte	1,5,36,9
	.half	.L832-.L1458
	.byte	1,5,53,9
	.half	.L1459-.L832
	.byte	3,3,1,5,23,9
	.half	.L1460-.L1459
	.byte	1,5,20,9
	.half	.L1461-.L1460
	.byte	1,5,53,9
	.half	.L1462-.L1461
	.byte	3,1,1,5,90,9
	.half	.L1463-.L1462
	.byte	1,5,59,9
	.half	.L1464-.L1463
	.byte	1,5,20,9
	.half	.L1465-.L1464
	.byte	1,5,43,9
	.half	.L1466-.L1465
	.byte	3,2,1,5,17,9
	.half	.L834-.L1466
	.byte	3,1,1,5,29,9
	.half	.L1467-.L834
	.byte	1,5,41,9
	.half	.L1468-.L1467
	.byte	3,1,1,5,28,9
	.half	.L643-.L1468
	.byte	3,5,1,5,81,9
	.half	.L836-.L643
	.byte	1,5,46,9
	.half	.L158-.L836
	.byte	3,3,1,5,49,9
	.half	.L839-.L158
	.byte	3,3,1,5,37,9
	.half	.L159-.L839
	.byte	1,5,49,9
	.half	.L1469-.L159
	.byte	1,5,30,7,9
	.half	.L1470-.L1469
	.byte	3,7,1,5,65,9
	.half	.L1471-.L1470
	.byte	1,5,50,9
	.half	.L1472-.L1471
	.byte	1,5,65,9
	.half	.L1473-.L1472
	.byte	1,5,80,9
	.half	.L1474-.L1473
	.byte	1,5,37,9
	.half	.L1475-.L1474
	.byte	1,5,44,9
	.half	.L1476-.L1475
	.byte	3,1,1,5,43,9
	.half	.L841-.L1476
	.byte	3,4,1,5,28,9
	.half	.L1477-.L841
	.byte	1,5,43,9
	.half	.L1478-.L1477
	.byte	1,5,58,9
	.half	.L1479-.L1478
	.byte	1,5,13,9
	.half	.L1480-.L1479
	.byte	1,5,43,7,9
	.half	.L1481-.L1480
	.byte	3,2,1,5,28,9
	.half	.L1482-.L1481
	.byte	1,5,43,9
	.half	.L1483-.L1482
	.byte	1,5,58,9
	.half	.L1484-.L1483
	.byte	1,5,71,9
	.half	.L1485-.L1484
	.byte	1,5,54,9
	.half	.L161-.L1485
	.byte	3,4,1,5,39,9
	.half	.L1486-.L161
	.byte	1,5,54,9
	.half	.L1487-.L1486
	.byte	1,5,69,9
	.half	.L1488-.L1487
	.byte	1,5,96,9
	.half	.L1489-.L1488
	.byte	3,104,1,5,60,9
	.half	.L157-.L1489
	.byte	1,5,81,9
	.half	.L1490-.L157
	.byte	1,5,38,7,9
	.half	.L142-.L1490
	.byte	3,29,1,5,22,9
	.half	.L843-.L142
	.byte	3,1,1,5,34,9
	.half	.L1491-.L843
	.byte	1,5,36,9
	.half	.L1492-.L1491
	.byte	3,1,1,5,35,9
	.half	.L845-.L1492
	.byte	3,4,1,5,22,9
	.half	.L847-.L845
	.byte	3,1,1,5,28,9
	.half	.L1493-.L847
	.byte	1,5,22,9
	.half	.L1494-.L1493
	.byte	3,1,1,5,28,9
	.half	.L1495-.L1494
	.byte	1,5,33,9
	.half	.L1496-.L1495
	.byte	3,1,1,5,5,9
	.half	.L849-.L1496
	.byte	3,2,1,5,1,9
	.half	.L162-.L849
	.byte	3,1,1,7,9
	.half	.L430-.L162
	.byte	0,1,1
.L1343:
	.sdecl	'.debug_ranges',debug,cluster('IfxScuCcu_init')
	.sect	'.debug_ranges'
.L429:
	.word	-1,.L282,0,.L430-.L282,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScuCcu_initConfig')
	.sect	'.debug_info'
.L431:
	.word	307
	.half	3
	.word	.L432
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuCcu.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L434,.L433
	.byte	2
	.word	.L307
	.byte	3
	.byte	'IfxScuCcu_initConfig',0,1,144,7,6,1,1,1
	.word	.L284,.L646,.L283
	.byte	4
	.byte	'cfg',0,1,144,7,45
	.word	.L571,.L647
	.byte	5
	.word	.L284,.L646
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScuCcu_initConfig')
	.sect	'.debug_abbrev'
.L432:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxScuCcu_initConfig')
	.sect	'.debug_line'
.L433:
	.word	.L1498-.L1497
.L1497:
	.half	3
	.word	.L1500-.L1499
.L1499:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuCcu.c',0,0,0,0,0
.L1500:
	.byte	5,12,7,0,5,2
	.word	.L284
	.byte	3,145,7,1,5,10,9
	.half	.L1501-.L284
	.byte	1,5,1,9
	.half	.L1502-.L1501
	.byte	3,1,1,7,9
	.half	.L435-.L1502
	.byte	0,1,1
.L1498:
	.sdecl	'.debug_ranges',debug,cluster('IfxScuCcu_initConfig')
	.sect	'.debug_ranges'
.L434:
	.word	-1,.L284,0,.L435-.L284,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScuCcu_initErayPll')
	.sect	'.debug_info'
.L436:
	.word	448
	.half	3
	.word	.L437
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuCcu.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L439,.L438
	.byte	2
	.word	.L307
	.byte	3
	.byte	'IfxScuCcu_initErayPll',0,1,150,7,9
	.word	.L569
	.byte	1,1,1
	.word	.L286,.L648,.L285
	.byte	4
	.byte	'cfg',0,1,150,7,62
	.word	.L649,.L650
	.byte	5
	.word	.L286,.L648
	.byte	6
	.byte	'smuTrapEnable',0,1,152,7,13
	.word	.L569,.L651
	.byte	6
	.byte	'endinit_pw',0,1,153,7,13
	.word	.L535,.L652
	.byte	6
	.byte	'endinitSfty_pw',0,1,153,7,25
	.word	.L535,.L653
	.byte	6
	.byte	'status',0,1,154,7,13
	.word	.L569,.L654
	.byte	5
	.word	.L655,.L648
	.byte	6
	.byte	'time_out_ctr',0,1,213,7,12
	.word	.L489,.L656
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScuCcu_initErayPll')
	.sect	'.debug_abbrev'
.L437:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxScuCcu_initErayPll')
	.sect	'.debug_line'
.L438:
	.word	.L1504-.L1503
.L1503:
	.half	3
	.word	.L1506-.L1505
.L1505:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuCcu.c',0,0,0,0,0
.L1506:
	.byte	5,9,7,0,5,2
	.word	.L286
	.byte	3,149,7,1,5,20,9
	.half	.L852-.L286
	.byte	3,4,1,5,54,9
	.half	.L854-.L852
	.byte	3,2,1,5,20,9
	.half	.L851-.L854
	.byte	1,5,57,9
	.half	.L856-.L851
	.byte	3,1,1,5,20,9
	.half	.L855-.L856
	.byte	1,5,35,9
	.half	.L858-.L855
	.byte	3,3,1,5,43,9
	.half	.L857-.L858
	.byte	3,1,1,5,22,9
	.half	.L860-.L857
	.byte	3,1,1,5,28,9
	.half	.L1507-.L860
	.byte	1,5,33,9
	.half	.L1508-.L1507
	.byte	3,1,1,5,34,9
	.half	.L862-.L1508
	.byte	3,2,1,5,27,9
	.half	.L864-.L862
	.byte	3,3,1,5,10,9
	.half	.L1509-.L864
	.byte	1,5,55,7,9
	.half	.L1510-.L1509
	.byte	1,5,83,9
	.half	.L1511-.L1510
	.byte	1,5,26,9
	.half	.L164-.L1511
	.byte	3,3,1,5,34,9
	.half	.L1512-.L164
	.byte	1,5,26,9
	.half	.L1513-.L1512
	.byte	3,1,1,5,34,9
	.half	.L1514-.L1513
	.byte	1,5,41,9
	.half	.L1515-.L1514
	.byte	3,2,1,5,33,9
	.half	.L167-.L1515
	.byte	1,5,41,9
	.half	.L1516-.L167
	.byte	1,5,43,7,9
	.half	.L1517-.L1516
	.byte	3,4,1,5,27,9
	.half	.L166-.L1517
	.byte	3,5,1,5,10,9
	.half	.L1518-.L166
	.byte	1,5,40,7,9
	.half	.L1519-.L1518
	.byte	3,2,1,5,34,9
	.half	.L170-.L1519
	.byte	1,5,17,9
	.half	.L1520-.L170
	.byte	1,5,26,7,9
	.half	.L1521-.L1520
	.byte	3,3,1,5,33,9
	.half	.L1522-.L1521
	.byte	1,5,26,9
	.half	.L1523-.L1522
	.byte	3,3,1,5,34,9
	.half	.L1524-.L1523
	.byte	1,5,36,9
	.half	.L169-.L1524
	.byte	3,3,1,5,30,9
	.half	.L172-.L169
	.byte	1,5,13,9
	.half	.L1525-.L172
	.byte	1,5,22,7,9
	.half	.L1526-.L1525
	.byte	3,3,1,5,50,9
	.half	.L1527-.L1526
	.byte	1,5,29,9
	.half	.L1528-.L1527
	.byte	1,5,22,9
	.half	.L1529-.L1528
	.byte	3,1,1,5,50,9
	.half	.L1530-.L1529
	.byte	1,5,29,9
	.half	.L1531-.L1530
	.byte	1,5,22,9
	.half	.L1532-.L1531
	.byte	3,1,1,5,50,9
	.half	.L1533-.L1532
	.byte	1,5,29,9
	.half	.L1534-.L1533
	.byte	1,5,22,9
	.half	.L1535-.L1534
	.byte	3,8,1,5,33,9
	.half	.L1536-.L1535
	.byte	1,5,22,9
	.half	.L1537-.L1536
	.byte	3,1,1,5,33,9
	.half	.L1538-.L1537
	.byte	1,5,32,9
	.half	.L1539-.L1538
	.byte	3,2,1,5,25,9
	.half	.L655-.L1539
	.byte	3,3,1,5,56,9
	.half	.L866-.L655
	.byte	3,2,1,5,12,9
	.half	.L174-.L866
	.byte	1,5,48,9
	.half	.L1540-.L174
	.byte	1,5,31,9
	.half	.L1541-.L1540
	.byte	1,5,10,7,9
	.half	.L176-.L1541
	.byte	3,4,1,5,16,7,9
	.half	.L1542-.L176
	.byte	3,2,1,5,34,9
	.half	.L177-.L1542
	.byte	3,3,1,5,22,9
	.half	.L869-.L177
	.byte	3,2,1,5,30,9
	.half	.L867-.L869
	.byte	1,5,37,9
	.half	.L1543-.L867
	.byte	3,3,1,5,29,9
	.half	.L178-.L1543
	.byte	1,5,37,9
	.half	.L1544-.L178
	.byte	1,5,27,7,9
	.half	.L1545-.L1544
	.byte	3,3,1,5,10,9
	.half	.L1546-.L1545
	.byte	1,5,16,7,9
	.half	.L1547-.L1546
	.byte	3,2,1,5,32,9
	.half	.L180-.L1547
	.byte	3,3,1,5,35,9
	.half	.L871-.L180
	.byte	3,3,1,5,22,9
	.half	.L873-.L871
	.byte	3,1,1,5,28,9
	.half	.L1548-.L873
	.byte	1,5,22,9
	.half	.L1549-.L1548
	.byte	3,1,1,5,28,9
	.half	.L1550-.L1549
	.byte	1,5,33,9
	.half	.L1551-.L1550
	.byte	3,1,1,5,5,9
	.half	.L875-.L1551
	.byte	3,2,1,5,1,9
	.half	.L181-.L875
	.byte	3,1,1,7,9
	.half	.L440-.L181
	.byte	0,1,1
.L1504:
	.sdecl	'.debug_ranges',debug,cluster('IfxScuCcu_initErayPll')
	.sect	'.debug_ranges'
.L439:
	.word	-1,.L286,0,.L440-.L286,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScuCcu_initErayPllConfig')
	.sect	'.debug_info'
.L441:
	.word	314
	.half	3
	.word	.L442
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuCcu.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L444,.L443
	.byte	2
	.word	.L307
	.byte	3
	.byte	'IfxScuCcu_initErayPllConfig',0,1,249,7,6,1,1,1
	.word	.L288,.L657,.L287
	.byte	4
	.byte	'cfg',0,1,249,7,59
	.word	.L658,.L659
	.byte	5
	.word	.L288,.L657
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScuCcu_initErayPllConfig')
	.sect	'.debug_abbrev'
.L442:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxScuCcu_initErayPllConfig')
	.sect	'.debug_line'
.L443:
	.word	.L1553-.L1552
.L1552:
	.half	3
	.word	.L1555-.L1554
.L1554:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuCcu.c',0,0,0,0,0
.L1555:
	.byte	5,12,7,0,5,2
	.word	.L288
	.byte	3,250,7,1,5,10,9
	.half	.L1556-.L288
	.byte	1,5,1,9
	.half	.L1557-.L1556
	.byte	3,1,1,7,9
	.half	.L445-.L1557
	.byte	0,1,1
.L1553:
	.sdecl	'.debug_ranges',debug,cluster('IfxScuCcu_initErayPllConfig')
	.sect	'.debug_ranges'
.L444:
	.word	-1,.L288,0,.L445-.L288,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScuCcu_switchToBackupClock')
	.sect	'.debug_info'
.L446:
	.word	423
	.half	3
	.word	.L447
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuCcu.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L449,.L448
	.byte	2
	.word	.L307
	.byte	3
	.byte	'IfxScuCcu_switchToBackupClock',0,1,242,9,6,1,1,1
	.word	.L304,.L660,.L303
	.byte	4
	.byte	'cfg',0,1,242,9,60
	.word	.L613,.L661
	.byte	5
	.word	.L304,.L660
	.byte	6
	.byte	'endinit_pw',0,1,244,9,12
	.word	.L535,.L662
	.byte	6
	.byte	'endinitSfty_pw',0,1,244,9,24
	.word	.L535,.L663
	.byte	6
	.byte	'pllStepsCount',0,1,245,9,12
	.word	.L664,.L665
	.byte	6
	.byte	'smuTrapEnable',0,1,246,9,12
	.word	.L569,.L666
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScuCcu_switchToBackupClock')
	.sect	'.debug_abbrev'
.L447:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxScuCcu_switchToBackupClock')
	.sect	'.debug_line'
.L448:
	.word	.L1559-.L1558
.L1558:
	.half	3
	.word	.L1561-.L1560
.L1560:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuCcu.c',0,0,0,0,0
.L1561:
	.byte	5,6,7,0,5,2
	.word	.L304
	.byte	3,241,9,1,5,22,9
	.half	.L974-.L304
	.byte	3,6,1,5,5,9
	.half	.L1562-.L974
	.byte	1,5,9,7,9
	.half	.L1563-.L1562
	.byte	3,2,1,5,57,9
	.half	.L232-.L1563
	.byte	3,3,1,5,20,9
	.half	.L973-.L232
	.byte	1,5,54,9
	.half	.L977-.L973
	.byte	3,1,1,5,20,9
	.half	.L976-.L977
	.byte	1,5,37,9
	.half	.L978-.L976
	.byte	3,3,1,5,77,9
	.half	.L979-.L978
	.byte	1,5,42,9
	.half	.L235-.L979
	.byte	3,3,1,5,45,9
	.half	.L982-.L235
	.byte	3,3,1,5,33,9
	.half	.L236-.L982
	.byte	1,5,45,9
	.half	.L1564-.L236
	.byte	1,5,26,7,9
	.half	.L1565-.L1564
	.byte	3,7,1,5,76,9
	.half	.L1566-.L1565
	.byte	1,5,61,9
	.half	.L1567-.L1566
	.byte	1,5,46,9
	.half	.L1568-.L1567
	.byte	1,5,61,9
	.half	.L1569-.L1568
	.byte	1,5,80,9
	.half	.L1570-.L1569
	.byte	1,5,33,9
	.half	.L1571-.L1570
	.byte	1,5,40,9
	.half	.L1572-.L1571
	.byte	3,2,1,5,69,9
	.half	.L984-.L1572
	.byte	3,3,1,5,54,9
	.half	.L1573-.L984
	.byte	1,5,39,9
	.half	.L1574-.L1573
	.byte	1,5,54,9
	.half	.L1575-.L1574
	.byte	1,5,73,9
	.half	.L1576-.L1575
	.byte	1,5,92,9
	.half	.L1577-.L1576
	.byte	3,110,1,5,77,9
	.half	.L234-.L1577
	.byte	1,5,35,7,9
	.half	.L1578-.L234
	.byte	3,24,1,5,43,9
	.half	.L986-.L1578
	.byte	3,2,1,5,22,9
	.half	.L980-.L986
	.byte	3,1,1,5,28,9
	.half	.L1579-.L980
	.byte	1,5,33,9
	.half	.L1580-.L1579
	.byte	3,2,1,5,38,9
	.half	.L988-.L1580
	.byte	3,5,1,5,39,9
	.half	.L990-.L988
	.byte	3,2,1,5,29,9
	.half	.L238-.L990
	.byte	1,5,39,9
	.half	.L1581-.L238
	.byte	1,5,22,7,9
	.half	.L1582-.L1581
	.byte	3,6,1,5,30,9
	.half	.L1583-.L1582
	.byte	1,5,22,9
	.half	.L1584-.L1583
	.byte	3,1,1,5,30,9
	.half	.L1585-.L1584
	.byte	1,5,39,9
	.half	.L1586-.L1585
	.byte	3,2,1,5,29,9
	.half	.L240-.L1586
	.byte	1,5,39,9
	.half	.L1587-.L240
	.byte	1,5,22,7,9
	.half	.L1588-.L1587
	.byte	3,7,1,5,33,9
	.half	.L1589-.L1588
	.byte	1,5,22,9
	.half	.L1590-.L1589
	.byte	3,3,1,5,34,9
	.half	.L1591-.L1590
	.byte	1,5,36,9
	.half	.L1592-.L1591
	.byte	3,1,1,5,35,9
	.half	.L992-.L1592
	.byte	3,4,1,5,22,9
	.half	.L994-.L992
	.byte	3,1,1,5,28,9
	.half	.L1593-.L994
	.byte	1,5,22,9
	.half	.L1594-.L1593
	.byte	3,2,1,5,28,9
	.half	.L1595-.L1594
	.byte	1,5,33,9
	.half	.L1596-.L1595
	.byte	3,1,1,5,1,9
	.half	.L233-.L1596
	.byte	3,2,1,7,9
	.half	.L450-.L233
	.byte	0,1,1
.L1559:
	.sdecl	'.debug_ranges',debug,cluster('IfxScuCcu_switchToBackupClock')
	.sect	'.debug_ranges'
.L449:
	.word	-1,.L304,0,.L450-.L304,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScuCcu_setGtmFrequency')
	.sect	'.debug_info'
.L451:
	.word	438
	.half	3
	.word	.L452
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuCcu.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L454,.L453
	.byte	2
	.word	.L307
	.byte	3
	.byte	'IfxScuCcu_setGtmFrequency',0,1,219,8,9
	.word	.L474
	.byte	1,1,1
	.word	.L294,.L667,.L293
	.byte	4
	.byte	'gtmFreq',0,1,219,8,43
	.word	.L474,.L668
	.byte	5
	.word	.L294,.L667
	.byte	6
	.byte	'l_SEndInitPW',0,1,221,8,21
	.word	.L535,.L669
	.byte	6
	.byte	'ccucon1',0,1,222,8,21
	.word	.L626,.L670
	.byte	6
	.byte	'inputFreq',0,1,224,8,21
	.word	.L474,.L671
	.byte	6
	.byte	'gtmDiv',0,1,225,8,21
	.word	.L489,.L672
	.byte	7
	.word	.L673,.L674,.L206
	.byte	8
	.word	.L675,.L674,.L206
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScuCcu_setGtmFrequency')
	.sect	'.debug_abbrev'
.L452:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,11,0,49,16,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxScuCcu_setGtmFrequency')
	.sect	'.debug_line'
.L453:
	.word	.L1598-.L1597
.L1597:
	.half	3
	.word	.L1600-.L1599
.L1599:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuCcu.c',0,0,0,0
	.byte	'..\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Scu\\Std\\IfxScuCcu.h',0,0,0,0,0
.L1600:
	.byte	5,9,7,0,5,2
	.word	.L294
	.byte	3,218,8,1,5,33,9
	.half	.L905-.L294
	.byte	3,3,1,5,61,9
	.half	.L907-.L905
	.byte	3,2,1,5,31,9
	.half	.L904-.L907
	.byte	1,5,41,9
	.half	.L909-.L904
	.byte	3,1,1,5,20,9
	.half	.L199-.L909
	.byte	3,1,1,5,9,9
	.half	.L906-.L199
	.byte	3,3,1,5,34,7,9
	.half	.L1601-.L906
	.byte	1,5,57,7,9
	.half	.L1602-.L1601
	.byte	1,5,25,7,9
	.half	.L1603-.L1602
	.byte	3,2,1,5,19,9
	.half	.L200-.L1603
	.byte	3,3,1,5,5,9
	.half	.L1604-.L200
	.byte	1,5,16,7,9
	.half	.L1605-.L1604
	.byte	3,2,1,5,55,9
	.half	.L203-.L1605
	.byte	3,3,1,5,18,9
	.half	.L911-.L203
	.byte	1,5,34,9
	.half	.L910-.L911
	.byte	3,1,1,5,35,9
	.half	.L913-.L910
	.byte	3,2,1,5,25,9
	.half	.L204-.L913
	.byte	1,5,35,9
	.half	.L1606-.L204
	.byte	1,5,24,7,9
	.half	.L1607-.L1606
	.byte	3,3,1,5,22,9
	.half	.L1608-.L1607
	.byte	1,5,24,9
	.half	.L1609-.L1608
	.byte	3,1,1,5,22,9
	.half	.L1610-.L1609
	.byte	1,9
	.half	.L1611-.L1610
	.byte	3,1,1,5,32,9
	.half	.L1612-.L1611
	.byte	3,2,1,4,2,5,40,9
	.half	.L674-.L1612
	.byte	3,76,1,5,58,9
	.half	.L1613-.L674
	.byte	1,5,43,9
	.half	.L1614-.L1613
	.byte	1,5,5,9
	.half	.L1615-.L1614
	.byte	1,4,1,9
	.half	.L206-.L1615
	.byte	3,54,1,5,1,9
	.half	.L207-.L206
	.byte	3,1,1,7,9
	.half	.L455-.L207
	.byte	0,1,1
.L1598:
	.sdecl	'.debug_ranges',debug,cluster('IfxScuCcu_setGtmFrequency')
	.sect	'.debug_ranges'
.L454:
	.word	-1,.L294,0,.L455-.L294,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScuCcu_isOscillatorStable')
	.sect	'.debug_info'
.L456:
	.word	369
	.half	3
	.word	.L457
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuCcu.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L459,.L458
	.byte	2
	.word	.L307
	.byte	3
	.byte	'IfxScuCcu_isOscillatorStable',0,1,255,7,20
	.word	.L569
	.byte	1,1
	.word	.L290,.L676,.L289
	.byte	4
	.word	.L290,.L676
	.byte	5
	.byte	'TimeoutCtr',0,1,129,8,13
	.word	.L677,.L678
	.byte	5
	.byte	'status',0,1,130,8,13
	.word	.L569,.L679
	.byte	5
	.byte	'endinitPw',0,1,132,8,13
	.word	.L535,.L680
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScuCcu_isOscillatorStable')
	.sect	'.debug_abbrev'
.L457:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxScuCcu_isOscillatorStable')
	.sect	'.debug_line'
.L458:
	.word	.L1617-.L1616
.L1616:
	.half	3
	.word	.L1619-.L1618
.L1618:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuCcu.c',0,0,0,0,0
.L1619:
	.byte	5,24,7,0,5,2
	.word	.L290
	.byte	3,128,8,1,9
	.half	.L877-.L290
	.byte	3,1,1,5,58,9
	.half	.L878-.L877
	.byte	3,2,1,5,24,9
	.half	.L879-.L878
	.byte	1,5,17,9
	.half	.L881-.L879
	.byte	3,5,1,5,23,9
	.half	.L1620-.L881
	.byte	1,5,17,9
	.half	.L1621-.L1620
	.byte	3,6,1,5,36,9
	.half	.L1622-.L1621
	.byte	1,5,62,9
	.half	.L1623-.L1622
	.byte	1,5,60,9
	.half	.L1624-.L1623
	.byte	1,5,71,9
	.half	.L880-.L1624
	.byte	1,5,25,9
	.half	.L1625-.L880
	.byte	1,5,17,9
	.half	.L1626-.L1625
	.byte	3,3,1,5,25,9
	.half	.L1627-.L1626
	.byte	1,5,66,9
	.half	.L1628-.L1627
	.byte	3,3,1,5,19,9
	.half	.L184-.L1628
	.byte	3,2,1,5,9,9
	.half	.L1629-.L184
	.byte	3,2,1,5,20,7,9
	.half	.L1630-.L1629
	.byte	3,2,1,5,13,9
	.half	.L1631-.L1630
	.byte	3,1,1,5,25,9
	.half	.L182-.L1631
	.byte	3,121,1,5,12,9
	.half	.L1632-.L182
	.byte	1,5,54,7,9
	.half	.L1633-.L1632
	.byte	1,5,61,9
	.half	.L1634-.L1633
	.byte	1,5,35,7,9
	.half	.L186-.L1634
	.byte	3,13,1,5,22,9
	.half	.L883-.L186
	.byte	3,1,1,5,28,9
	.half	.L1635-.L883
	.byte	1,5,22,9
	.half	.L1636-.L1635
	.byte	3,1,1,5,28,9
	.half	.L1637-.L1636
	.byte	1,5,33,9
	.half	.L1638-.L1637
	.byte	3,1,1,5,5,9
	.half	.L885-.L1638
	.byte	3,3,1,5,1,9
	.half	.L187-.L885
	.byte	3,1,1,7,9
	.half	.L460-.L187
	.byte	0,1,1
.L1617:
	.sdecl	'.debug_ranges',debug,cluster('IfxScuCcu_isOscillatorStable')
	.sect	'.debug_ranges'
.L459:
	.word	-1,.L290,0,.L460-.L290,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScuCcu_wait')
	.sect	'.debug_info'
.L461:
	.word	381
	.half	3
	.word	.L462
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuCcu.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L464,.L463
	.byte	2
	.word	.L307
	.byte	3
	.byte	'IfxScuCcu_wait',0,1,198,10,17,1,1
	.word	.L306,.L681,.L305
	.byte	4
	.byte	'timeSec',0,1,198,10,40
	.word	.L474,.L682
	.byte	5
	.word	.L306,.L681
	.byte	6
	.byte	'stmCount',0,1,200,10,12
	.word	.L489,.L683
	.byte	6
	.byte	'stmCountBegin',0,1,201,10,12
	.word	.L489,.L684
	.byte	7
	.word	.L685,.L686,.L242
	.byte	8
	.word	.L687,.L686,.L242
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScuCcu_wait')
	.sect	'.debug_abbrev'
.L462:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3,8,58
	.byte	15,59,15,57,15,73,16,2,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,11,0,49,16,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxScuCcu_wait')
	.sect	'.debug_line'
.L463:
	.word	.L1640-.L1639
.L1639:
	.half	3
	.word	.L1642-.L1641
.L1641:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuCcu.c',0,0,0,0
	.byte	'..\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Scu\\Std\\IfxScuCcu.h',0,0,0,0,0
.L1642:
	.byte	5,17,7,0,5,2
	.word	.L306
	.byte	3,197,10,1,4,2,5,40,9
	.half	.L686-.L306
	.byte	3,158,126,1,5,58,9
	.half	.L996-.L686
	.byte	1,5,43,9
	.half	.L1643-.L996
	.byte	1,5,5,9
	.half	.L1644-.L1643
	.byte	1,4,1,5,65,9
	.half	.L242-.L1644
	.byte	3,228,1,1,5,28,9
	.half	.L1645-.L242
	.byte	1,5,37,9
	.half	.L997-.L1645
	.byte	3,1,1,5,60,9
	.half	.L998-.L997
	.byte	3,2,1,5,30,9
	.half	.L243-.L998
	.byte	1,5,33,9
	.half	.L1646-.L243
	.byte	1,5,60,9
	.half	.L1647-.L1646
	.byte	1,5,1,7,9
	.half	.L1648-.L1647
	.byte	3,9,1,7,9
	.half	.L465-.L1648
	.byte	0,1,1
.L1640:
	.sdecl	'.debug_ranges',debug,cluster('IfxScuCcu_wait')
	.sect	'.debug_ranges'
.L464:
	.word	-1,.L306,0,.L465-.L306,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScuCcu_defaultClockConfig')
	.sect	'.debug_info'
.L466:
	.word	275
	.half	3
	.word	.L467
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuCcu.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L307
	.byte	3
	.byte	'IfxScuCcu_defaultClockConfig',0,11,93,35
	.word	.L688
	.byte	1,5,3
	.word	IfxScuCcu_defaultClockConfig
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScuCcu_defaultClockConfig')
	.sect	'.debug_abbrev'
.L467:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScuCcu_defaultErayPllConfig')
	.sect	'.debug_info'
.L468:
	.word	277
	.half	3
	.word	.L469
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuCcu.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L307
	.byte	3
	.byte	'IfxScuCcu_defaultErayPllConfig',0,11,104,35
	.word	.L689
	.byte	1,5,3
	.word	IfxScuCcu_defaultErayPllConfig
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScuCcu_defaultErayPllConfig')
	.sect	'.debug_abbrev'
.L469:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScuCcu_aDefaultPllConfigSteps')
	.sect	'.debug_info'
.L470:
	.word	278
	.half	3
	.word	.L471
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuCcu.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L307
	.byte	3
	.byte	'IfxScuCcu_aDefaultPllConfigSteps',0,11,81,47
	.word	.L690
	.byte	5,3
	.word	IfxScuCcu_aDefaultPllConfigSteps
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScuCcu_aDefaultPllConfigSteps')
	.sect	'.debug_abbrev'
.L471:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScuCcu_xtalFrequency')
	.sect	'.debug_info'
.L472:
	.word	269
	.half	3
	.word	.L473
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuCcu.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L307
	.byte	3
	.byte	'IfxScuCcu_xtalFrequency',0,11,87,47
	.word	.L489
	.byte	5,3
	.word	IfxScuCcu_xtalFrequency
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScuCcu_xtalFrequency')
	.sect	'.debug_abbrev'
.L473:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_loc',debug,cluster('IfxScuCcu_calculateSysPllDividers')
	.sect	'.debug_loc'
.L245:
	.word	-1,.L246,0,.L691-.L246
	.half	2
	.byte	138,0
	.word	.L691-.L246,.L570-.L246
	.half	2
	.byte	138,40
	.word	.L570-.L246,.L570-.L246
	.half	2
	.byte	138,0
	.word	0,0
.L604:
	.word	-1,.L246,.L704-.L246,.L708-.L246
	.half	1
	.byte	95
	.word	.L709-.L246,.L570-.L246
	.half	2
	.byte	145,96
	.word	.L743-.L246,.L744-.L246
	.half	1
	.byte	95
	.word	0,0
.L605:
	.word	-1,.L246,.L708-.L246,.L710-.L246
	.half	1
	.byte	95
	.word	.L711-.L246,.L570-.L246
	.half	2
	.byte	145,88
	.word	.L734-.L246,.L9-.L246
	.half	1
	.byte	82
	.word	.L739-.L246,.L740-.L246
	.half	1
	.byte	95
	.word	0,0
.L606:
	.word	-1,.L246,.L710-.L246,.L712-.L246
	.half	1
	.byte	95
	.word	.L713-.L246,.L570-.L246
	.half	2
	.byte	145,92
	.word	.L741-.L246,.L742-.L246
	.half	1
	.byte	95
	.word	0,0
.L572:
	.word	-1,.L246,0,.L4-.L246
	.half	1
	.byte	100
	.word	.L692-.L246,.L570-.L246
	.half	1
	.byte	111
	.word	0,0
.L575:
	.word	0,0
.L576:
	.word	-1,.L246,.L577-.L246,.L570-.L246
	.half	1
	.byte	89
	.word	0,0
.L573:
	.word	-1,.L246,0,.L4-.L246
	.half	1
	.byte	84
	.word	.L693-.L246,.L570-.L246
	.half	1
	.byte	88
	.word	.L726-.L246,.L727-.L246
	.half	1
	.byte	84
	.word	0,0
.L611:
	.word	-1,.L246,.L735-.L246,.L11-.L246
	.half	2
	.byte	144,32
	.word	0,0
.L610:
	.word	-1,.L246,.L714-.L246,.L4-.L246
	.half	2
	.byte	144,38
	.word	0,0
.L579:
	.word	-1,.L246,.L697-.L246,.L4-.L246
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L608:
	.word	-1,.L246,.L720-.L246,.L719-.L246
	.half	2
	.byte	144,32
	.word	.L721-.L246,.L723-.L246
	.half	2
	.byte	144,32
	.word	0,0
.L581:
	.word	-1,.L246,.L695-.L246,.L698-.L246
	.half	1
	.byte	95
	.word	.L699-.L246,.L570-.L246
	.half	2
	.byte	145,116
	.word	.L722-.L246,.L723-.L246
	.half	1
	.byte	95
	.word	.L721-.L246,.L8-.L246
	.half	1
	.byte	82
	.word	0,0
.L583:
	.word	-1,.L246,.L698-.L246,.L700-.L246
	.half	1
	.byte	95
	.word	.L701-.L246,.L570-.L246
	.half	2
	.byte	145,112
	.word	.L718-.L246,.L719-.L246
	.half	1
	.byte	95
	.word	.L720-.L246,.L721-.L246
	.half	1
	.byte	82
	.word	0,0
.L609:
	.word	0,0
.L587:
	.word	-1,.L246,.L702-.L246,.L704-.L246
	.half	1
	.byte	95
	.word	.L705-.L246,.L570-.L246
	.half	2
	.byte	145,100
	.word	.L732-.L246,.L733-.L246
	.half	1
	.byte	95
	.word	.L731-.L246,.L12-.L246
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L585:
	.word	-1,.L246,.L700-.L246,.L702-.L246
	.half	1
	.byte	95
	.word	.L703-.L246,.L570-.L246
	.half	2
	.byte	145,104
	.word	.L725-.L246,.L729-.L246
	.half	1
	.byte	95
	.word	.L730-.L246,.L731-.L246
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L602:
	.word	-1,.L246,.L724-.L246,.L5-.L246
	.half	1
	.byte	94
	.word	.L728-.L246,.L727-.L246
	.half	1
	.byte	86
	.word	0,0
.L595:
	.word	-1,.L246,.L8-.L246,.L725-.L246
	.half	1
	.byte	95
	.word	.L737-.L246,.L5-.L246
	.half	1
	.byte	95
	.word	0,0
.L593:
	.word	0,0
.L603:
	.word	-1,.L246,.L712-.L246,.L715-.L246
	.half	1
	.byte	95
	.word	.L716-.L246,.L570-.L246
	.half	2
	.byte	145,108
	.word	.L717-.L246,.L2-.L246
	.half	1
	.byte	95
	.word	.L736-.L246,.L7-.L246
	.half	1
	.byte	95
	.word	0,0
.L601:
	.word	-1,.L246,.L734-.L246,.L9-.L246
	.half	1
	.byte	82
	.word	0,0
.L599:
	.word	-1,.L246,.L707-.L246,.L570-.L246
	.half	1
	.byte	91
	.word	0,0
.L597:
	.word	0,0
.L600:
	.word	0,0
.L591:
	.word	-1,.L246,.L706-.L246,.L570-.L246
	.half	1
	.byte	90
	.word	0,0
.L589:
	.word	-1,.L246,.L4-.L246,.L718-.L246
	.half	1
	.byte	95
	.word	.L738-.L246,.L14-.L246
	.half	1
	.byte	95
	.word	0,0
.L574:
	.word	-1,.L246,.L694-.L246,.L695-.L246
	.half	1
	.byte	95
	.word	.L696-.L246,.L570-.L246
	.half	2
	.byte	145,120
	.word	.L745-.L246,.L17-.L246
	.half	1
	.byte	95
	.word	.L746-.L246,.L570-.L246
	.half	1
	.byte	82
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxScuCcu_getBaud1Frequency')
	.sect	'.debug_loc'
.L247:
	.word	-1,.L248,0,.L475-.L248
	.half	2
	.byte	138,0
	.word	0,0
.L478:
	.word	-1,.L248,.L747-.L248,.L475-.L248
	.half	1
	.byte	88
	.word	0,0
.L476:
	.word	-1,.L248,.L748-.L248,.L19-.L248
	.half	1
	.byte	82
	.word	.L20-.L248,.L475-.L248
	.half	1
	.byte	82
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxScuCcu_getBaud2Frequency')
	.sect	'.debug_loc'
.L249:
	.word	-1,.L250,0,.L479-.L250
	.half	2
	.byte	138,0
	.word	0,0
.L481:
	.word	-1,.L250,.L749-.L250,.L479-.L250
	.half	1
	.byte	88
	.word	0,0
.L480:
	.word	-1,.L250,.L750-.L250,.L22-.L250
	.half	1
	.byte	82
	.word	.L23-.L250,.L479-.L250
	.half	1
	.byte	82
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxScuCcu_getBbbFrequency')
	.sect	'.debug_loc'
.L251:
	.word	-1,.L252,0,.L482-.L252
	.half	2
	.byte	138,0
	.word	0,0
.L483:
	.word	-1,.L252,.L752-.L252,.L31-.L252
	.half	1
	.byte	82
	.word	.L32-.L252,.L26-.L252
	.half	1
	.byte	82
	.word	.L753-.L252,.L27-.L252
	.half	1
	.byte	82
	.word	.L754-.L252,.L28-.L252
	.half	1
	.byte	82
	.word	.L755-.L252,.L29-.L252
	.half	1
	.byte	82
	.word	.L756-.L252,.L30-.L252
	.half	1
	.byte	82
	.word	.L757-.L252,.L482-.L252
	.half	1
	.byte	82
	.word	0,0
.L484:
	.word	-1,.L252,.L751-.L252,.L752-.L252
	.half	1
	.byte	82
	.word	.L31-.L252,.L32-.L252
	.half	1
	.byte	82
	.word	.L26-.L252,.L753-.L252
	.half	1
	.byte	82
	.word	.L27-.L252,.L754-.L252
	.half	1
	.byte	82
	.word	.L28-.L252,.L755-.L252
	.half	1
	.byte	82
	.word	.L29-.L252,.L756-.L252
	.half	1
	.byte	82
	.word	.L30-.L252,.L757-.L252
	.half	1
	.byte	82
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxScuCcu_getCpuFrequency')
	.sect	'.debug_loc'
.L253:
	.word	-1,.L254,0,.L485-.L254
	.half	2
	.byte	138,0
	.word	0,0
.L487:
	.word	-1,.L254,0,.L758-.L254
	.half	1
	.byte	84
	.word	.L759-.L254,.L485-.L254
	.half	1
	.byte	88
	.word	0,0
.L490:
	.word	-1,.L254,.L760-.L254,.L761-.L254
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L488:
	.word	-1,.L254,.L758-.L254,.L485-.L254
	.half	1
	.byte	82
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxScuCcu_getFsi2Frequency')
	.sect	'.debug_loc'
.L255:
	.word	-1,.L256,0,.L491-.L256
	.half	2
	.byte	138,0
	.word	0,0
.L493:
	.word	-1,.L256,.L762-.L256,.L491-.L256
	.half	1
	.byte	88
	.word	0,0
.L492:
	.word	-1,.L256,.L763-.L256,.L48-.L256
	.half	1
	.byte	82
	.word	.L764-.L256,.L491-.L256
	.half	1
	.byte	82
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxScuCcu_getFsiFrequency')
	.sect	'.debug_loc'
.L257:
	.word	-1,.L258,0,.L494-.L258
	.half	2
	.byte	138,0
	.word	0,0
.L496:
	.word	-1,.L258,.L765-.L258,.L494-.L258
	.half	1
	.byte	88
	.word	0,0
.L495:
	.word	-1,.L258,.L766-.L258,.L53-.L258
	.half	1
	.byte	82
	.word	.L767-.L258,.L494-.L258
	.half	1
	.byte	82
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxScuCcu_getMaxFrequency')
	.sect	'.debug_loc'
.L259:
	.word	-1,.L260,0,.L497-.L260
	.half	2
	.byte	138,0
	.word	0,0
.L498:
	.word	-1,.L260,.L768-.L260,.L497-.L260
	.half	1
	.byte	82
	.word	0,0
.L499:
	.word	-1,.L260,.L768-.L260,.L497-.L260
	.half	1
	.byte	82
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxScuCcu_getModuleFrequency')
	.sect	'.debug_loc'
.L261:
	.word	-1,.L262,0,.L500-.L262
	.half	2
	.byte	138,0
	.word	0,0
.L502:
	.word	-1,.L262,.L771-.L262,.L73-.L262
	.half	1
	.byte	82
	.word	.L772-.L262,.L75-.L262
	.half	1
	.byte	82
	.word	.L74-.L262,.L500-.L262
	.half	1
	.byte	82
	.word	0,0
.L504:
	.word	-1,.L262,.L769-.L262,.L500-.L262
	.half	1
	.byte	88
	.word	0,0
.L501:
	.word	-1,.L262,.L770-.L262,.L771-.L262
	.half	1
	.byte	82
	.word	.L73-.L262,.L772-.L262
	.half	1
	.byte	82
	.word	.L75-.L262,.L74-.L262
	.half	1
	.byte	82
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxScuCcu_getOsc0Frequency')
	.sect	'.debug_loc'
.L263:
	.word	-1,.L264,0,.L505-.L264
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxScuCcu_getOscFrequency')
	.sect	'.debug_loc'
.L265:
	.word	-1,.L266,0,.L506-.L266
	.half	2
	.byte	138,0
	.word	0,0
.L507:
	.word	-1,.L266,.L773-.L266,.L79-.L266
	.half	1
	.byte	82
	.word	.L774-.L266,.L81-.L266
	.half	1
	.byte	82
	.word	.L80-.L266,.L506-.L266
	.half	1
	.byte	82
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxScuCcu_getPllErayFrequency')
	.sect	'.debug_loc'
.L267:
	.word	-1,.L268,0,.L508-.L268
	.half	2
	.byte	138,0
	.word	0,0
.L512:
	.word	-1,.L268,.L777-.L268,.L84-.L268
	.half	1
	.byte	82
	.word	0,0
.L511:
	.word	-1,.L268,.L776-.L268,.L777-.L268
	.half	1
	.byte	82
	.word	.L84-.L268,.L778-.L268
	.half	1
	.byte	82
	.word	.L86-.L268,.L85-.L268
	.half	1
	.byte	82
	.word	0,0
.L510:
	.word	-1,.L268,.L775-.L268,.L508-.L268
	.half	1
	.byte	111
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxScuCcu_getPllErayVcoFrequency')
	.sect	'.debug_loc'
.L269:
	.word	-1,.L270,0,.L513-.L270
	.half	2
	.byte	138,0
	.word	0,0
.L514:
	.word	-1,.L270,.L779-.L270,.L89-.L270
	.half	1
	.byte	82
	.word	.L90-.L270,.L513-.L270
	.half	1
	.byte	82
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxScuCcu_getPllFrequency')
	.sect	'.debug_loc'
.L271:
	.word	-1,.L272,0,.L515-.L272
	.half	2
	.byte	138,0
	.word	0,0
.L518:
	.word	-1,.L272,.L782-.L272,.L92-.L272
	.half	1
	.byte	82
	.word	0,0
.L517:
	.word	-1,.L272,.L781-.L272,.L782-.L272
	.half	1
	.byte	82
	.word	.L92-.L272,.L783-.L272
	.half	1
	.byte	82
	.word	.L94-.L272,.L93-.L272
	.half	1
	.byte	82
	.word	0,0
.L516:
	.word	-1,.L272,.L780-.L272,.L515-.L272
	.half	1
	.byte	111
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxScuCcu_getPllVcoFrequency')
	.sect	'.debug_loc'
.L273:
	.word	-1,.L274,0,.L519-.L274
	.half	2
	.byte	138,0
	.word	0,0
.L520:
	.word	-1,.L274,.L784-.L274,.L97-.L274
	.half	1
	.byte	82
	.word	.L98-.L274,.L519-.L274
	.half	1
	.byte	82
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxScuCcu_getSourceFrequency')
	.sect	'.debug_loc'
.L275:
	.word	-1,.L276,0,.L521-.L276
	.half	2
	.byte	138,0
	.word	0,0
.L522:
	.word	-1,.L276,.L785-.L276,.L101-.L276
	.half	1
	.byte	82
	.word	.L786-.L276,.L102-.L276
	.half	1
	.byte	82
	.word	.L787-.L276,.L521-.L276
	.half	1
	.byte	82
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxScuCcu_getSpbFrequency')
	.sect	'.debug_loc'
.L277:
	.word	-1,.L278,0,.L525-.L278
	.half	2
	.byte	138,0
	.word	0,0
.L527:
	.word	-1,.L278,.L788-.L278,.L789-.L278
	.half	1
	.byte	82
	.word	.L114-.L278,.L115-.L278
	.half	1
	.byte	82
	.word	.L109-.L278,.L790-.L278
	.half	1
	.byte	82
	.word	.L110-.L278,.L791-.L278
	.half	1
	.byte	82
	.word	.L111-.L278,.L792-.L278
	.half	1
	.byte	82
	.word	.L112-.L278,.L793-.L278
	.half	1
	.byte	82
	.word	.L113-.L278,.L794-.L278
	.half	1
	.byte	82
	.word	0,0
.L526:
	.word	-1,.L278,.L789-.L278,.L114-.L278
	.half	1
	.byte	82
	.word	.L115-.L278,.L109-.L278
	.half	1
	.byte	82
	.word	.L790-.L278,.L110-.L278
	.half	1
	.byte	82
	.word	.L791-.L278,.L111-.L278
	.half	1
	.byte	82
	.word	.L792-.L278,.L112-.L278
	.half	1
	.byte	82
	.word	.L793-.L278,.L113-.L278
	.half	1
	.byte	82
	.word	.L794-.L278,.L525-.L278
	.half	1
	.byte	82
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxScuCcu_getSriFrequency')
	.sect	'.debug_loc'
.L279:
	.word	-1,.L280,0,.L528-.L280
	.half	2
	.byte	138,0
	.word	0,0
.L530:
	.word	-1,.L280,.L795-.L280,.L796-.L280
	.half	1
	.byte	82
	.word	.L129-.L280,.L130-.L280
	.half	1
	.byte	82
	.word	.L124-.L280,.L797-.L280
	.half	1
	.byte	82
	.word	.L125-.L280,.L798-.L280
	.half	1
	.byte	82
	.word	.L126-.L280,.L799-.L280
	.half	1
	.byte	82
	.word	.L127-.L280,.L800-.L280
	.half	1
	.byte	82
	.word	.L128-.L280,.L801-.L280
	.half	1
	.byte	82
	.word	0,0
.L529:
	.word	-1,.L280,.L796-.L280,.L129-.L280
	.half	1
	.byte	82
	.word	.L130-.L280,.L124-.L280
	.half	1
	.byte	82
	.word	.L797-.L280,.L125-.L280
	.half	1
	.byte	82
	.word	.L798-.L280,.L126-.L280
	.half	1
	.byte	82
	.word	.L799-.L280,.L127-.L280
	.half	1
	.byte	82
	.word	.L800-.L280,.L128-.L280
	.half	1
	.byte	82
	.word	.L801-.L280,.L528-.L280
	.half	1
	.byte	82
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxScuCcu_init')
	.sect	'.debug_loc'
.L281:
	.word	-1,.L282,0,.L612-.L282
	.half	2
	.byte	138,0
	.word	0,0
.L623:
	.word	-1,.L282,.L820-.L282,.L821-.L282
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L627:
	.word	-1,.L282,.L822-.L282,.L823-.L282
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L631:
	.word	-1,.L282,.L824-.L282,.L825-.L282
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L635:
	.word	-1,.L282,.L826-.L282,.L827-.L282
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L638:
	.word	-1,.L282,.L828-.L282,.L829-.L282
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L641:
	.word	-1,.L282,.L830-.L282,.L642-.L282
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L614:
	.word	-1,.L282,0,.L802-.L282
	.half	1
	.byte	100
	.word	.L803-.L282,.L804-.L282
	.half	1
	.byte	111
	.word	0,0
.L617:
	.word	-1,.L282,.L806-.L282,.L808-.L282
	.half	1
	.byte	82
	.word	.L809-.L282,.L612-.L282
	.half	1
	.byte	90
	.word	.L814-.L282,.L815-.L282
	.half	1
	.byte	84
	.word	.L816-.L282,.L817-.L282
	.half	1
	.byte	84
	.word	.L818-.L282,.L819-.L282
	.half	1
	.byte	84
	.word	.L831-.L282,.L642-.L282
	.half	1
	.byte	84
	.word	.L838-.L282,.L839-.L282
	.half	1
	.byte	84
	.word	.L840-.L282,.L841-.L282
	.half	1
	.byte	84
	.word	.L842-.L282,.L843-.L282
	.half	1
	.byte	84
	.word	.L844-.L282,.L845-.L282
	.half	1
	.byte	84
	.word	0,0
.L616:
	.word	-1,.L282,.L802-.L282,.L806-.L282
	.half	1
	.byte	82
	.word	.L807-.L282,.L612-.L282
	.half	1
	.byte	89
	.word	.L810-.L282,.L808-.L282
	.half	1
	.byte	84
	.word	.L812-.L282,.L813-.L282
	.half	1
	.byte	84
	.word	.L833-.L282,.L834-.L282
	.half	1
	.byte	84
	.word	.L835-.L282,.L643-.L282
	.half	1
	.byte	84
	.word	.L846-.L282,.L847-.L282
	.half	1
	.byte	84
	.word	.L848-.L282,.L849-.L282
	.half	1
	.byte	84
	.word	0,0
.L645:
	.word	-1,.L282,.L832-.L282,.L158-.L282
	.half	1
	.byte	95
	.word	0,0
.L620:
	.word	-1,.L282,.L836-.L282,.L837-.L282
	.half	1
	.byte	92
	.word	.L157-.L282,.L142-.L282
	.half	1
	.byte	92
	.word	0,0
.L615:
	.word	-1,.L282,.L811-.L282,.L612-.L282
	.half	1
	.byte	91
	.word	0,0
.L618:
	.word	-1,.L282,.L805-.L282,.L612-.L282
	.half	1
	.byte	88
	.word	.L850-.L282,.L612-.L282
	.half	1
	.byte	82
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxScuCcu_initConfig')
	.sect	'.debug_loc'
.L283:
	.word	-1,.L284,0,.L646-.L284
	.half	2
	.byte	138,0
	.word	0,0
.L647:
	.word	-1,.L284,0,.L163-.L284
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxScuCcu_initErayPll')
	.sect	'.debug_loc'
.L285:
	.word	-1,.L286,0,.L648-.L286
	.half	2
	.byte	138,0
	.word	0,0
.L650:
	.word	-1,.L286,0,.L851-.L286
	.half	1
	.byte	100
	.word	.L852-.L286,.L853-.L286
	.half	1
	.byte	111
	.word	0,0
.L653:
	.word	-1,.L286,.L855-.L286,.L857-.L286
	.half	1
	.byte	82
	.word	.L858-.L286,.L648-.L286
	.half	1
	.byte	90
	.word	.L863-.L286,.L864-.L286
	.half	1
	.byte	84
	.word	.L865-.L286,.L655-.L286
	.half	1
	.byte	84
	.word	.L868-.L286,.L869-.L286
	.half	1
	.byte	84
	.word	.L870-.L286,.L871-.L286
	.half	1
	.byte	84
	.word	0,0
.L652:
	.word	-1,.L286,.L851-.L286,.L855-.L286
	.half	1
	.byte	82
	.word	.L856-.L286,.L648-.L286
	.half	1
	.byte	89
	.word	.L859-.L286,.L857-.L286
	.half	1
	.byte	84
	.word	.L861-.L286,.L862-.L286
	.half	1
	.byte	84
	.word	.L872-.L286,.L873-.L286
	.half	1
	.byte	84
	.word	.L874-.L286,.L875-.L286
	.half	1
	.byte	84
	.word	0,0
.L651:
	.word	-1,.L286,.L860-.L286,.L648-.L286
	.half	1
	.byte	91
	.word	0,0
.L654:
	.word	-1,.L286,.L854-.L286,.L648-.L286
	.half	1
	.byte	88
	.word	.L876-.L286,.L648-.L286
	.half	1
	.byte	82
	.word	0,0
.L656:
	.word	-1,.L286,.L866-.L286,.L867-.L286
	.half	1
	.byte	95
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxScuCcu_initErayPllConfig')
	.sect	'.debug_loc'
.L287:
	.word	-1,.L288,0,.L657-.L288
	.half	2
	.byte	138,0
	.word	0,0
.L659:
	.word	-1,.L288,0,.L657-.L288
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxScuCcu_isOscillatorStable')
	.sect	'.debug_loc'
.L289:
	.word	-1,.L290,0,.L676-.L290
	.half	2
	.byte	138,0
	.word	0,0
.L678:
	.word	-1,.L290,.L877-.L290,.L676-.L290
	.half	1
	.byte	88
	.word	0,0
.L680:
	.word	-1,.L290,.L879-.L290,.L880-.L290
	.half	1
	.byte	82
	.word	.L881-.L290,.L676-.L290
	.half	1
	.byte	90
	.word	.L882-.L290,.L883-.L290
	.half	1
	.byte	84
	.word	.L884-.L290,.L885-.L290
	.half	1
	.byte	84
	.word	0,0
.L679:
	.word	-1,.L290,.L878-.L290,.L676-.L290
	.half	1
	.byte	89
	.word	.L886-.L290,.L676-.L290
	.half	1
	.byte	82
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxScuCcu_setCpuFrequency')
	.sect	'.debug_loc'
.L291:
	.word	-1,.L292,0,.L531-.L292
	.half	2
	.byte	138,0
	.word	0,0
.L533:
	.word	-1,.L292,0,.L887-.L292
	.half	1
	.byte	84
	.word	.L897-.L292,.L898-.L292
	.half	1
	.byte	89
	.word	.L899-.L292,.L900-.L292
	.half	1
	.byte	89
	.word	0,0
.L538:
	.word	-1,.L292,.L893-.L292,.L188-.L292
	.half	1
	.byte	88
	.word	.L189-.L292,.L531-.L292
	.half	1
	.byte	88
	.word	0,0
.L534:
	.word	-1,.L292,0,.L887-.L292
	.half	1
	.byte	85
	.word	.L889-.L292,.L890-.L292
	.half	1
	.byte	88
	.word	.L891-.L292,.L892-.L292
	.half	1
	.byte	88
	.word	0,0
.L536:
	.word	-1,.L292,.L888-.L292,.L894-.L292
	.half	1
	.byte	82
	.word	.L895-.L292,.L531-.L292
	.half	1
	.byte	91
	.word	.L894-.L292,.L896-.L292
	.half	1
	.byte	84
	.word	.L901-.L292,.L902-.L292
	.half	1
	.byte	84
	.word	0,0
.L537:
	.word	-1,.L292,.L887-.L292,.L888-.L292
	.half	1
	.byte	82
	.word	.L889-.L292,.L531-.L292
	.half	1
	.byte	90
	.word	.L903-.L292,.L531-.L292
	.half	1
	.byte	82
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxScuCcu_setGtmFrequency')
	.sect	'.debug_loc'
.L293:
	.word	-1,.L294,0,.L667-.L294
	.half	2
	.byte	138,0
	.word	0,0
.L670:
	.word	-1,.L294,.L907-.L294,.L667-.L294
	.half	1
	.byte	89
	.word	0,0
.L672:
	.word	-1,.L294,.L906-.L294,.L667-.L294
	.half	1
	.byte	88
	.word	0,0
.L668:
	.word	-1,.L294,0,.L904-.L294
	.half	1
	.byte	84
	.word	.L905-.L294,.L906-.L294
	.half	1
	.byte	88
	.word	0,0
.L671:
	.word	-1,.L294,.L904-.L294,.L908-.L294
	.half	1
	.byte	82
	.word	.L909-.L294,.L910-.L294
	.half	1
	.byte	90
	.word	0,0
.L669:
	.word	-1,.L294,.L911-.L294,.L912-.L294
	.half	1
	.byte	82
	.word	.L910-.L294,.L667-.L294
	.half	1
	.byte	90
	.word	.L912-.L294,.L913-.L294
	.half	1
	.byte	84
	.word	.L914-.L294,.L674-.L294
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxScuCcu_setPll2ErayFrequency')
	.sect	'.debug_loc'
.L295:
	.word	-1,.L296,0,.L539-.L296
	.half	2
	.byte	138,0
	.word	0,0
.L541:
	.word	-1,.L296,.L915-.L296,.L918-.L296
	.half	1
	.byte	82
	.word	.L919-.L296,.L539-.L296
	.half	1
	.byte	88
	.word	.L922-.L296,.L923-.L296
	.half	1
	.byte	84
	.word	.L924-.L296,.L544-.L296
	.half	1
	.byte	84
	.word	0,0
.L542:
	.word	-1,.L296,.L920-.L296,.L921-.L296
	.half	1
	.byte	95
	.word	0,0
.L540:
	.word	-1,.L296,0,.L915-.L296
	.half	1
	.byte	84
	.word	.L916-.L296,.L917-.L296
	.half	1
	.byte	95
	.word	0,0
.L546:
	.word	-1,.L296,.L925-.L296,.L539-.L296
	.half	1
	.byte	82
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxScuCcu_setPll2Frequency')
	.sect	'.debug_loc'
.L297:
	.word	-1,.L298,0,.L547-.L298
	.half	2
	.byte	138,0
	.word	0,0
.L549:
	.word	-1,.L298,.L926-.L298,.L929-.L298
	.half	1
	.byte	82
	.word	.L930-.L298,.L547-.L298
	.half	1
	.byte	88
	.word	.L933-.L298,.L934-.L298
	.half	1
	.byte	84
	.word	.L935-.L298,.L552-.L298
	.half	1
	.byte	84
	.word	0,0
.L550:
	.word	-1,.L298,.L931-.L298,.L932-.L298
	.half	1
	.byte	95
	.word	0,0
.L548:
	.word	-1,.L298,0,.L926-.L298
	.half	1
	.byte	84
	.word	.L927-.L298,.L928-.L298
	.half	1
	.byte	95
	.word	0,0
.L554:
	.word	-1,.L298,.L936-.L298,.L547-.L298
	.half	1
	.byte	82
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxScuCcu_setSpbFrequency')
	.sect	'.debug_loc'
.L299:
	.word	-1,.L300,0,.L555-.L300
	.half	2
	.byte	138,0
	.word	0,0
.L559:
	.word	-1,.L300,.L952-.L300,.L953-.L300
	.half	1
	.byte	95
	.word	0,0
.L560:
	.word	-1,.L300,.L937-.L300,.L940-.L300
	.half	1
	.byte	82
	.word	0,0
.L557:
	.word	-1,.L300,.L940-.L300,.L943-.L300
	.half	1
	.byte	82
	.word	.L944-.L300,.L555-.L300
	.half	1
	.byte	88
	.word	.L947-.L300,.L945-.L300
	.half	1
	.byte	84
	.word	.L948-.L300,.L949-.L300
	.half	1
	.byte	84
	.word	.L956-.L300,.L957-.L300
	.half	1
	.byte	84
	.word	.L958-.L300,.L959-.L300
	.half	1
	.byte	84
	.word	0,0
.L558:
	.word	-1,.L300,.L943-.L300,.L945-.L300
	.half	1
	.byte	82
	.word	.L946-.L300,.L555-.L300
	.half	1
	.byte	90
	.word	.L950-.L300,.L951-.L300
	.half	1
	.byte	84
	.word	.L954-.L300,.L955-.L300
	.half	1
	.byte	84
	.word	0,0
.L561:
	.word	-1,.L300,.L941-.L300,.L942-.L300
	.half	1
	.byte	95
	.word	.L942-.L300,.L555-.L300
	.half	1
	.byte	89
	.word	0,0
.L556:
	.word	-1,.L300,0,.L937-.L300
	.half	1
	.byte	84
	.word	.L938-.L300,.L939-.L300
	.half	1
	.byte	95
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxScuCcu_setSriFrequency')
	.sect	'.debug_loc'
.L301:
	.word	-1,.L302,0,.L562-.L302
	.half	2
	.byte	138,0
	.word	0,0
.L566:
	.word	-1,.L302,.L969-.L302,.L229-.L302
	.half	1
	.byte	95
	.word	0,0
.L564:
	.word	-1,.L302,.L972-.L302,.L562-.L302
	.half	1
	.byte	82
	.word	0,0
.L567:
	.word	-1,.L302,.L966-.L302,.L967-.L302
	.half	1
	.byte	82
	.word	.L965-.L302,.L562-.L302
	.half	1
	.byte	89
	.word	.L967-.L302,.L968-.L302
	.half	1
	.byte	84
	.word	.L970-.L302,.L971-.L302
	.half	1
	.byte	84
	.word	0,0
.L565:
	.word	-1,.L302,.L960-.L302,.L963-.L302
	.half	1
	.byte	82
	.word	.L964-.L302,.L965-.L302
	.half	1
	.byte	89
	.word	0,0
.L568:
	.word	-1,.L302,.L962-.L302,.L562-.L302
	.half	1
	.byte	88
	.word	0,0
.L563:
	.word	-1,.L302,0,.L960-.L302
	.half	1
	.byte	84
	.word	.L961-.L302,.L962-.L302
	.half	1
	.byte	88
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxScuCcu_switchToBackupClock')
	.sect	'.debug_loc'
.L303:
	.word	-1,.L304,0,.L660-.L304
	.half	2
	.byte	138,0
	.word	0,0
.L661:
	.word	-1,.L304,0,.L973-.L304
	.half	1
	.byte	100
	.word	.L974-.L304,.L975-.L304
	.half	1
	.byte	111
	.word	0,0
.L663:
	.word	-1,.L304,.L973-.L304,.L976-.L304
	.half	1
	.byte	82
	.word	.L977-.L304,.L233-.L304
	.half	1
	.byte	88
	.word	.L981-.L304,.L982-.L304
	.half	1
	.byte	84
	.word	.L983-.L304,.L984-.L304
	.half	1
	.byte	84
	.word	.L989-.L304,.L990-.L304
	.half	1
	.byte	84
	.word	.L991-.L304,.L992-.L304
	.half	1
	.byte	84
	.word	0,0
.L662:
	.word	-1,.L304,.L976-.L304,.L235-.L304
	.half	1
	.byte	82
	.word	.L978-.L304,.L233-.L304
	.half	1
	.byte	89
	.word	.L985-.L304,.L986-.L304
	.half	1
	.byte	84
	.word	.L987-.L304,.L988-.L304
	.half	1
	.byte	84
	.word	.L993-.L304,.L994-.L304
	.half	1
	.byte	84
	.word	.L995-.L304,.L233-.L304
	.half	1
	.byte	84
	.word	0,0
.L665:
	.word	-1,.L304,.L979-.L304,.L980-.L304
	.half	1
	.byte	90
	.word	0,0
.L666:
	.word	-1,.L304,.L980-.L304,.L233-.L304
	.half	1
	.byte	90
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxScuCcu_wait')
	.sect	'.debug_loc'
.L305:
	.word	-1,.L306,0,.L681-.L306
	.half	2
	.byte	138,0
	.word	0,0
.L683:
	.word	-1,.L306,.L997-.L306,.L681-.L306
	.half	1
	.byte	95
	.word	0,0
.L684:
	.word	-1,.L306,.L998-.L306,.L681-.L306
	.half	1
	.byte	81
	.word	0,0
.L682:
	.word	-1,.L306,0,.L996-.L306
	.half	1
	.byte	84
	.word	.L686-.L306,.L681-.L306
	.half	1
	.byte	88
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L1649:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('IfxScuCcu_calculateSysPllDividers')
	.sect	'.debug_frame'
	.word	36
	.word	.L1649,.L246,.L570-.L246
	.byte	4
	.word	(.L691-.L246)/2
	.byte	19,40,22,26,3,19,138,40,4
	.word	(.L570-.L691)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxScuCcu_getBaud1Frequency')
	.sect	'.debug_frame'
	.word	12
	.word	.L1649,.L248,.L475-.L248
	.sdecl	'.debug_frame',debug,cluster('IfxScuCcu_getBaud2Frequency')
	.sect	'.debug_frame'
	.word	12
	.word	.L1649,.L250,.L479-.L250
	.sdecl	'.debug_frame',debug,cluster('IfxScuCcu_getBbbFrequency')
	.sect	'.debug_frame'
	.word	12
	.word	.L1649,.L252,.L482-.L252
	.sdecl	'.debug_frame',debug,cluster('IfxScuCcu_getCpuFrequency')
	.sect	'.debug_frame'
	.word	12
	.word	.L1649,.L254,.L485-.L254
	.sdecl	'.debug_frame',debug,cluster('IfxScuCcu_getFsi2Frequency')
	.sect	'.debug_frame'
	.word	12
	.word	.L1649,.L256,.L491-.L256
	.sdecl	'.debug_frame',debug,cluster('IfxScuCcu_getFsiFrequency')
	.sect	'.debug_frame'
	.word	12
	.word	.L1649,.L258,.L494-.L258
	.sdecl	'.debug_frame',debug,cluster('IfxScuCcu_getMaxFrequency')
	.sect	'.debug_frame'
	.word	12
	.word	.L1649,.L260,.L497-.L260
	.sdecl	'.debug_frame',debug,cluster('IfxScuCcu_getModuleFrequency')
	.sect	'.debug_frame'
	.word	12
	.word	.L1649,.L262,.L500-.L262
	.sdecl	'.debug_frame',debug,cluster('IfxScuCcu_getOsc0Frequency')
	.sect	'.debug_frame'
	.word	24
	.word	.L1649,.L264,.L505-.L264
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('IfxScuCcu_getOscFrequency')
	.sect	'.debug_frame'
	.word	24
	.word	.L1649,.L266,.L506-.L266
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('IfxScuCcu_getPllErayFrequency')
	.sect	'.debug_frame'
	.word	12
	.word	.L1649,.L268,.L508-.L268
	.sdecl	'.debug_frame',debug,cluster('IfxScuCcu_getPllErayVcoFrequency')
	.sect	'.debug_frame'
	.word	12
	.word	.L1649,.L270,.L513-.L270
	.sdecl	'.debug_frame',debug,cluster('IfxScuCcu_getPllFrequency')
	.sect	'.debug_frame'
	.word	12
	.word	.L1649,.L272,.L515-.L272
	.sdecl	'.debug_frame',debug,cluster('IfxScuCcu_getPllVcoFrequency')
	.sect	'.debug_frame'
	.word	12
	.word	.L1649,.L274,.L519-.L274
	.sdecl	'.debug_frame',debug,cluster('IfxScuCcu_getSourceFrequency')
	.sect	'.debug_frame'
	.word	12
	.word	.L1649,.L276,.L521-.L276
	.sdecl	'.debug_frame',debug,cluster('IfxScuCcu_getSpbFrequency')
	.sect	'.debug_frame'
	.word	12
	.word	.L1649,.L278,.L525-.L278
	.sdecl	'.debug_frame',debug,cluster('IfxScuCcu_getSriFrequency')
	.sect	'.debug_frame'
	.word	12
	.word	.L1649,.L280,.L528-.L280
	.sdecl	'.debug_frame',debug,cluster('IfxScuCcu_init')
	.sect	'.debug_frame'
	.word	12
	.word	.L1649,.L282,.L612-.L282
	.sdecl	'.debug_frame',debug,cluster('IfxScuCcu_initConfig')
	.sect	'.debug_frame'
	.word	20
	.word	.L1649,.L284,.L646-.L284
	.byte	8,19,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('IfxScuCcu_initErayPll')
	.sect	'.debug_frame'
	.word	12
	.word	.L1649,.L286,.L648-.L286
	.sdecl	'.debug_frame',debug,cluster('IfxScuCcu_initErayPllConfig')
	.sect	'.debug_frame'
	.word	20
	.word	.L1649,.L288,.L657-.L288
	.byte	8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxScuCcu_isOscillatorStable')
	.sect	'.debug_frame'
	.word	12
	.word	.L1649,.L290,.L676-.L290
	.sdecl	'.debug_frame',debug,cluster('IfxScuCcu_setCpuFrequency')
	.sect	'.debug_frame'
	.word	12
	.word	.L1649,.L292,.L531-.L292
	.sdecl	'.debug_frame',debug,cluster('IfxScuCcu_setGtmFrequency')
	.sect	'.debug_frame'
	.word	12
	.word	.L1649,.L294,.L667-.L294
	.sdecl	'.debug_frame',debug,cluster('IfxScuCcu_setPll2ErayFrequency')
	.sect	'.debug_frame'
	.word	12
	.word	.L1649,.L296,.L539-.L296
	.sdecl	'.debug_frame',debug,cluster('IfxScuCcu_setPll2Frequency')
	.sect	'.debug_frame'
	.word	12
	.word	.L1649,.L298,.L547-.L298
	.sdecl	'.debug_frame',debug,cluster('IfxScuCcu_setSpbFrequency')
	.sect	'.debug_frame'
	.word	12
	.word	.L1649,.L300,.L555-.L300
	.sdecl	'.debug_frame',debug,cluster('IfxScuCcu_setSriFrequency')
	.sect	'.debug_frame'
	.word	12
	.word	.L1649,.L302,.L562-.L302
	.sdecl	'.debug_frame',debug,cluster('IfxScuCcu_switchToBackupClock')
	.sect	'.debug_frame'
	.word	12
	.word	.L1649,.L304,.L660-.L304
	.sdecl	'.debug_frame',debug,cluster('IfxScuCcu_wait')
	.sect	'.debug_frame'
	.word	12
	.word	.L1649,.L306,.L681-.L306
	; Module end
