/**
 * \file Ifx_LutLinearF32.h
 * \brief Look-up functionality
 *
 *
 *
 * \version disabled
 * \copyright Copyright (c) 2013 Infineon Technologies AG. All rights reserved.
 *
 *
 *                                 IMPORTANT NOTICE
 *
 *
 * Use of this file is subject to the terms of use agreed between (i) you or 
 * the company in which ordinary course of business you are acting and (ii) 
 * Infineon Technologies AG or its licensees. If and as long as no such 
 * terms of use are agreed, use of this file is subject to following:


 * Boost Software License - Version 1.0 - August 17th, 2003

 * Permission is hereby granted, free of charge, to any person or 
 * organization obtaining a copy of the software and accompanying 
 * documentation covered by this license (the "Software") to use, reproduce,
 * display, distribute, execute, and transmit the Software, and to prepare
 * derivative works of the Software, and to permit third-parties to whom the 
 * Software is furnished to do so, all subject to the following:

 * The copyright notices in the Software and this entire statement, including
 * the above license grant, this restriction and the following disclaimer, must
 * be included in all copies of the Software, in whole or in part, and all
 * derivative works of the Software, unless such copies or derivative works are
 * solely in the form of machine-executable object code generated by a source
 * language processor.

 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR 
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE, TITLE AND NON-INFRINGEMENT. IN NO EVENT
 * SHALL THE COPYRIGHT HOLDERS OR ANYONE DISTRIBUTING THE SOFTWARE BE LIABLE 
 * FOR ANY DAMAGES OR OTHER LIABILITY, WHETHER IN CONTRACT, TORT OR OTHERWISE,
 * ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
 * DEALINGS IN THE SOFTWARE.

 *
 * \defgroup library_srvsw_sysse_math_f32_lut_linear Look-up table (with linear interpolation)
 * \ingroup library_srvsw_sysse_math_f32_lut
 *
 */

#ifndef IFX_LUTLINEARF32_H
#define IFX_LUTLINEARF32_H

//________________________________________________________________________________________
// INCLUDES
#include "Cpu/Std/Ifx_Types.h"

typedef struct
{
    float32 gain;        /**< \brief channel gain */
    float32 offset;      /**< \brief channel offset */
    float32 boundary;    /**< \brief segment input upper limit */
} Ifx_LutLinearF32_Item;

typedef struct
{
    sint8                          segmentCount;
    const Ifx_LutLinearF32_Item *segments;
} Ifx_LutLinearF32;

//________________________________________________________________________________________
// FUNCTION PROTOTYPES

/** \addtogroup library_srvsw_sysse_math_f32_lut_linear
 * \{ */
IFX_EXTERN float32 Ifx_LutLinearF32_searchBin(const Ifx_LutLinearF32 *ml, float32 index);
IFX_INLINE float32 Ifx_LutLinearF32_searchNegSeq(const Ifx_LutLinearF32 *ml, float32 index);
IFX_INLINE float32 Ifx_LutLinearF32_searchPosSeq(const Ifx_LutLinearF32 *ml, float32 index);
/** \} */

//________________________________________________________________________________________
// INLINE FUNCTION IMPLEMENTATION

/** \brief Look-up table with positive sequential search implementation
 *
 * Value inside table will be linearly interpolated
 * Value outside table will be linearly extrapolated
 *
 * \param ml pointer to the multi-segment object
 * \param index
 * \return interpolated value */
IFX_INLINE float32 Ifx_LutLinearF32_searchPosSeq(const Ifx_LutLinearF32 *ml, float32 index)
{
    sint8 i = 0;

    while ((index > ml->segments[i].boundary) && (i < ml->segmentCount - 1))
    {
        i++;
    }

    return (ml->segments[i].gain * index) + ml->segments[i].offset;
}


/** \brief Look-up table with negative sequential search implementation
 *
 * Value inside table will be linearly interpolated
 * Value outside table will be linearly extrapolated
 *
 * \param ml pointer to the multi-segment object
 * \param index
 * \return interpolated value */
IFX_INLINE float32 Ifx_LutLinearF32_searchNegSeq(const Ifx_LutLinearF32 *ml, float32 index)
{
    sint8 i = ml->segmentCount - 1;

    while ((i - 1 >= 0) && (index > ml->segments[i - 1].boundary))
    {
        i--;
    }

    return (ml->segments[i].gain * index) + ml->segments[i].offset;
}


#endif /* IFX_LUTLINEARF32_H */
