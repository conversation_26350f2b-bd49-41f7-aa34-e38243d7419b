	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc30740a --dep-file=zf_device_imu963ra.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2\\\\xf_asr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/zf_device/zf_device_imu963ra.src ../libraries/zf_device/zf_device_imu963ra.c"
	.compiler_name		"ctc"
	;source	'../libraries/zf_device/zf_device_imu963ra.c'

	
$TC16X
	
	.sdecl	'.text.zf_device_imu963ra.imu963ra_write_acc_gyro_register',code,cluster('imu963ra_write_acc_gyro_register')
	.sect	'.text.zf_device_imu963ra.imu963ra_write_acc_gyro_register'
	.align	2
	
; Function imu963ra_write_acc_gyro_register
.L79:
imu963ra_write_acc_gyro_register:	.type	func
	mov	e8,d5,d4
.L548:
	mov	d15,#0
	jeq	d15,#0,.L2
	mov	d4,#653
.L234:
	call	get_port
.L233:
	add.a	a2,#4
	mov	d15,#8192
	st.w	[a2],d15
	j	.L3
.L2:
	mov	d4,#653
.L236:
	call	get_port
.L235:
	add.a	a2,#4
	movh	d15,#8192
	st.w	[a2],d15
.L3:
	mov	d4,#0
.L237:
	mov	d5,d8
.L238:
	mov	d6,d9
.L239:
	call	spi_write_8bit_register
.L240:
	mov	d15,#1
	jeq	d15,#0,.L4
	mov	d4,#653
	call	get_port
	add.a	a2,#4
	mov	d15,#8192
	st.w	[a2],d15
	j	.L5
.L4:
	mov	d4,#653
	call	get_port
	add.a	a2,#4
	movh	d15,#8192
	st.w	[a2],d15
.L5:
	ret
.L197:
	
__imu963ra_write_acc_gyro_register_function_end:
	.size	imu963ra_write_acc_gyro_register,__imu963ra_write_acc_gyro_register_function_end-imu963ra_write_acc_gyro_register
.L130:
	; End of function
	
	.sdecl	'.text.zf_device_imu963ra.imu963ra_read_acc_gyro_register',code,cluster('imu963ra_read_acc_gyro_register')
	.sect	'.text.zf_device_imu963ra.imu963ra_read_acc_gyro_register'
	.align	2
	
; Function imu963ra_read_acc_gyro_register
.L81:
imu963ra_read_acc_gyro_register:	.type	func
	mov	d8,d4
.L242:
	mov	d15,#0
	jeq	d15,#0,.L6
	mov	d4,#653
.L241:
	call	get_port
	add.a	a2,#4
	mov	d15,#8192
	st.w	[a2],d15
	j	.L7
.L6:
	mov	d4,#653
.L244:
	call	get_port
	add.a	a2,#4
	movh	d15,#8192
	st.w	[a2],d15
.L7:
	mov	d4,#0
.L553:
	or	d5,d8,#128
	call	spi_read_8bit_register
.L245:
	mov	d8,d2
.L243:
	mov	d15,#1
	jeq	d15,#0,.L8
	mov	d4,#653
	call	get_port
.L246:
	add.a	a2,#4
	mov	d15,#8192
	st.w	[a2],d15
	j	.L9
.L8:
	mov	d4,#653
	call	get_port
.L247:
	add.a	a2,#4
	movh	d15,#8192
	st.w	[a2],d15
.L9:
	mov	d2,d8
.L248:
	j	.L10
.L10:
	ret
.L200:
	
__imu963ra_read_acc_gyro_register_function_end:
	.size	imu963ra_read_acc_gyro_register,__imu963ra_read_acc_gyro_register_function_end-imu963ra_read_acc_gyro_register
.L135:
	; End of function
	
	.sdecl	'.text.zf_device_imu963ra.imu963ra_read_acc_gyro_registers',code,cluster('imu963ra_read_acc_gyro_registers')
	.sect	'.text.zf_device_imu963ra.imu963ra_read_acc_gyro_registers'
	.align	2
	
; Function imu963ra_read_acc_gyro_registers
.L83:
imu963ra_read_acc_gyro_registers:	.type	func
	mov	d8,d4
.L251:
	mov.aa	a15,a4
.L252:
	mov	d9,d5
.L253:
	mov	d15,#0
	jeq	d15,#0,.L11
	mov	d4,#653
.L250:
	call	get_port
.L249:
	add.a	a2,#4
	mov	d15,#8192
	st.w	[a2],d15
	j	.L12
.L11:
	mov	d4,#653
.L255:
	call	get_port
.L254:
	add.a	a2,#4
	movh	d15,#8192
	st.w	[a2],d15
.L12:
	mov	d4,#0
.L558:
	or	d5,d8,#128
.L559:
	mov.aa	a4,a15
.L256:
	mov	d6,d9
.L258:
	call	spi_read_8bit_registers
.L257:
	mov	d15,#1
	jeq	d15,#0,.L13
	mov	d4,#653
	call	get_port
	add.a	a2,#4
	mov	d15,#8192
	st.w	[a2],d15
	j	.L14
.L13:
	mov	d4,#653
	call	get_port
	add.a	a2,#4
	movh	d15,#8192
	st.w	[a2],d15
.L14:
	ret
.L203:
	
__imu963ra_read_acc_gyro_registers_function_end:
	.size	imu963ra_read_acc_gyro_registers,__imu963ra_read_acc_gyro_registers_function_end-imu963ra_read_acc_gyro_registers
.L140:
	; End of function
	
	.sdecl	'.text.zf_device_imu963ra.imu963ra_write_mag_register',code,cluster('imu963ra_write_mag_register')
	.sect	'.text.zf_device_imu963ra.imu963ra_write_mag_register'
	.align	2
	
; Function imu963ra_write_mag_register
.L85:
imu963ra_write_mag_register:	.type	func
	mov	d15,d5
.L262:
	mov	d8,d6
.L263:
	mov	d10,#0
.L264:
	mov	d9,#0
.L265:
	sha	d11,d4,#1
.L259:
	mov	d4,#23
.L564:
	mov	d5,#0
.L261:
	call	imu963ra_write_acc_gyro_register
.L260:
	mov	d4,#21
.L565:
	mov	d5,d11
.L267:
	call	imu963ra_write_acc_gyro_register
.L268:
	mov	d4,#22
.L566:
	mov	d5,d15
.L269:
	call	imu963ra_write_acc_gyro_register
.L270:
	mov	d4,#33
.L567:
	mov	d5,d8
.L271:
	call	imu963ra_write_acc_gyro_register
.L272:
	mov	d4,#20
.L568:
	mov	d5,#76
	call	imu963ra_write_acc_gyro_register
.L569:
	j	.L15
.L16:
	mov	d0,d9
	add	d9,#1
.L266:
	extr.u	d9,d9,#0,#16
.L273:
	mov	d15,#255
.L570:
	jge.u	d15,d0,.L17
.L571:
	mov	d10,#1
.L572:
	j	.L18
.L17:
	mov	d4,#2
	call	system_delay_ms
.L15:
	mov	d4,#34
	call	imu963ra_read_acc_gyro_register
.L573:
	jz.t	d2:7,.L16
.L18:
	mov	d2,d10
.L274:
	j	.L19
.L19:
	ret
.L209:
	
__imu963ra_write_mag_register_function_end:
	.size	imu963ra_write_mag_register,__imu963ra_write_mag_register_function_end-imu963ra_write_mag_register
.L145:
	; End of function
	
	.sdecl	'.text.zf_device_imu963ra.imu963ra_read_mag_register',code,cluster('imu963ra_read_mag_register')
	.sect	'.text.zf_device_imu963ra.imu963ra_read_mag_register'
	.align	2
	
; Function imu963ra_read_mag_register
.L87:
imu963ra_read_mag_register:	.type	func
	mov	d9,d5
.L277:
	mov	d8,#0
.L278:
	sha	d15,d4,#1
.L275:
	mov	d4,#21
.L578:
	or	d5,d15,#1
.L276:
	call	imu963ra_write_acc_gyro_register
.L579:
	mov	d4,#22
.L580:
	mov	d5,d9
.L280:
	call	imu963ra_write_acc_gyro_register
.L281:
	mov	d4,#23
.L581:
	mov	d5,#1
	call	imu963ra_write_acc_gyro_register
.L582:
	mov	d4,#20
.L583:
	mov	d5,#76
	call	imu963ra_write_acc_gyro_register
.L584:
	j	.L20
.L21:
	mov	d0,d8
	add	d8,#1
.L279:
	extr.u	d8,d8,#0,#16
.L282:
	mov	d15,#255
.L585:
	jge.u	d15,d0,.L22
.L586:
	j	.L23
.L22:
	mov	d4,#2
	call	system_delay_ms
.L20:
	mov	d4,#34
	call	imu963ra_read_acc_gyro_register
.L587:
	jz.t	d2:0,.L21
.L23:
	mov	d4,#2
	call	imu963ra_read_acc_gyro_register
.L588:
	j	.L24
.L24:
	ret
.L216:
	
__imu963ra_read_mag_register_function_end:
	.size	imu963ra_read_mag_register,__imu963ra_read_mag_register_function_end-imu963ra_read_mag_register
.L150:
	; End of function
	
	.sdecl	'.text.zf_device_imu963ra.imu963ra_connect_mag',code,cluster('imu963ra_connect_mag')
	.sect	'.text.zf_device_imu963ra.imu963ra_connect_mag'
	.align	2
	
; Function imu963ra_connect_mag
.L89:
imu963ra_connect_mag:	.type	func
	mov	d8,d5
.L285:
	sha	d15,d4,#1
.L283:
	mov	d4,#21
.L593:
	or	d5,d15,#1
.L284:
	call	imu963ra_write_acc_gyro_register
.L594:
	mov	d4,#22
.L595:
	mov	d5,d8
.L286:
	call	imu963ra_write_acc_gyro_register
.L287:
	mov	d4,#23
.L596:
	mov	d5,#6
	call	imu963ra_write_acc_gyro_register
.L597:
	mov	d4,#20
.L598:
	mov	d5,#108
	call	imu963ra_write_acc_gyro_register
.L599:
	ret
.L220:
	
__imu963ra_connect_mag_function_end:
	.size	imu963ra_connect_mag,__imu963ra_connect_mag_function_end-imu963ra_connect_mag
.L155:
	; End of function
	
	.sdecl	'.text.zf_device_imu963ra.imu963ra_acc_gyro_self_check',code,cluster('imu963ra_acc_gyro_self_check')
	.sect	'.text.zf_device_imu963ra.imu963ra_acc_gyro_self_check'
	.align	2
	
; Function imu963ra_acc_gyro_self_check
.L91:
imu963ra_acc_gyro_self_check:	.type	func
	mov	d8,#0
.L288:
	mov	d10,#0
.L289:
	mov	d9,#0
.L291:
	j	.L25
.L26:
	mov	d0,d9
	add	d9,#1
.L292:
	extr.u	d9,d9,#0,#16
.L293:
	mov	d15,#255
.L604:
	jge.u	d15,d0,.L27
.L605:
	mov	d8,#1
.L606:
	j	.L28
.L27:
	mov	d4,#15
	call	imu963ra_read_acc_gyro_register
.L290:
	mov	d10,d2
.L294:
	mov	d4,#10
	call	system_delay_ms
.L25:
	mov	d15,#107
.L607:
	jne	d15,d10,.L26
.L28:
	mov	d2,d8
.L295:
	j	.L29
.L29:
	ret
.L223:
	
__imu963ra_acc_gyro_self_check_function_end:
	.size	imu963ra_acc_gyro_self_check,__imu963ra_acc_gyro_self_check_function_end-imu963ra_acc_gyro_self_check
.L160:
	; End of function
	
	.sdecl	'.text.zf_device_imu963ra.imu963ra_mag_self_check',code,cluster('imu963ra_mag_self_check')
	.sect	'.text.zf_device_imu963ra.imu963ra_mag_self_check'
	.align	2
	
; Function imu963ra_mag_self_check
.L93:
imu963ra_mag_self_check:	.type	func
	mov	d8,#0
.L296:
	mov	d10,#0
.L297:
	mov	d9,#0
.L299:
	j	.L30
.L31:
	mov	d0,d9
	add	d9,#1
.L300:
	extr.u	d9,d9,#0,#16
.L301:
	mov	d15,#255
.L612:
	jge.u	d15,d0,.L32
.L613:
	mov	d8,#1
.L614:
	j	.L33
.L32:
	mov	d4,#13
.L615:
	mov	d5,#13
	call	imu963ra_read_mag_register
.L298:
	mov	d10,d2
.L302:
	mov	d4,#10
	call	system_delay_ms
.L30:
	mov	d15,#255
.L616:
	jne	d15,d10,.L31
.L33:
	mov	d2,d8
.L303:
	j	.L34
.L34:
	ret
.L227:
	
__imu963ra_mag_self_check_function_end:
	.size	imu963ra_mag_self_check,__imu963ra_mag_self_check_function_end-imu963ra_mag_self_check
.L165:
	; End of function
	
	.sdecl	'.text.zf_device_imu963ra.imu963ra_get_acc',code,cluster('imu963ra_get_acc')
	.sect	'.text.zf_device_imu963ra.imu963ra_get_acc'
	.align	2
	
	.global	imu963ra_get_acc
; Function imu963ra_get_acc
.L95:
imu963ra_get_acc:	.type	func
	sub.a	a10,#8
.L304:
	mov	d4,#40
.L320:
	lea	a4,[a10]0
.L321:
	mov	d5,#6
	call	imu963ra_read_acc_gyro_registers
.L322:
	movh.a	a15,#@his(imu963ra_acc_x)
	lea	a15,[a15]@los(imu963ra_acc_x)
.L323:
	ld.bu	d15,[a10]1
.L324:
	sha	d15,d15,#8
.L325:
	ld.bu	d0,[a10]
.L326:
	or	d15,d0
.L327:
	st.h	[a15],d15
.L328:
	movh.a	a15,#@his(imu963ra_acc_y)
	lea	a15,[a15]@los(imu963ra_acc_y)
.L329:
	ld.bu	d15,[a10]3
.L330:
	sha	d0,d15,#8
.L331:
	ld.bu	d15,[a10]2
.L332:
	or	d0,d15
.L333:
	st.h	[a15],d0
.L334:
	movh.a	a15,#@his(imu963ra_acc_z)
	lea	a15,[a15]@los(imu963ra_acc_z)
.L335:
	ld.bu	d15,[a10]5
.L336:
	sha	d0,d15,#8
.L337:
	ld.bu	d15,[a10]4
.L338:
	or	d0,d15
.L339:
	st.h	[a15],d0
.L340:
	ret
.L186:
	
__imu963ra_get_acc_function_end:
	.size	imu963ra_get_acc,__imu963ra_get_acc_function_end-imu963ra_get_acc
.L110:
	; End of function
	
	.sdecl	'.text.zf_device_imu963ra.imu963ra_get_gyro',code,cluster('imu963ra_get_gyro')
	.sect	'.text.zf_device_imu963ra.imu963ra_get_gyro'
	.align	2
	
	.global	imu963ra_get_gyro
; Function imu963ra_get_gyro
.L97:
imu963ra_get_gyro:	.type	func
	sub.a	a10,#8
.L305:
	mov	d4,#34
.L345:
	lea	a4,[a10]0
.L346:
	mov	d5,#6
	call	imu963ra_read_acc_gyro_registers
.L347:
	movh.a	a15,#@his(imu963ra_gyro_x)
	lea	a15,[a15]@los(imu963ra_gyro_x)
.L348:
	ld.bu	d15,[a10]1
.L349:
	sha	d15,d15,#8
.L350:
	ld.bu	d0,[a10]
.L351:
	or	d15,d0
.L352:
	st.h	[a15],d15
.L353:
	movh.a	a15,#@his(imu963ra_gyro_y)
	lea	a15,[a15]@los(imu963ra_gyro_y)
.L354:
	ld.bu	d15,[a10]3
.L355:
	sha	d0,d15,#8
.L356:
	ld.bu	d15,[a10]2
.L357:
	or	d0,d15
.L358:
	st.h	[a15],d0
.L359:
	movh.a	a15,#@his(imu963ra_gyro_z)
	lea	a15,[a15]@los(imu963ra_gyro_z)
.L360:
	ld.bu	d15,[a10]5
.L361:
	sha	d0,d15,#8
.L362:
	ld.bu	d15,[a10]4
.L363:
	or	d0,d15
.L364:
	st.h	[a15],d0
.L365:
	ret
.L189:
	
__imu963ra_get_gyro_function_end:
	.size	imu963ra_get_gyro,__imu963ra_get_gyro_function_end-imu963ra_get_gyro
.L115:
	; End of function
	
	.sdecl	'.text.zf_device_imu963ra.imu963ra_get_mag',code,cluster('imu963ra_get_mag')
	.sect	'.text.zf_device_imu963ra.imu963ra_get_mag'
	.align	2
	
	.global	imu963ra_get_mag
; Function imu963ra_get_mag
.L99:
imu963ra_get_mag:	.type	func
	sub.a	a10,#8
.L306:
	mov	d4,#1
.L370:
	mov	d5,#64
	call	imu963ra_write_acc_gyro_register
.L371:
	mov	d4,#34
	call	imu963ra_read_acc_gyro_register
.L307:
	jz.t	d2:0,.L35
.L372:
	mov	d4,#2
.L373:
	lea	a4,[a10]0
.L374:
	mov	d5,#6
	call	imu963ra_read_acc_gyro_registers
.L308:
	movh.a	a15,#@his(imu963ra_mag_x)
	lea	a15,[a15]@los(imu963ra_mag_x)
.L375:
	ld.bu	d15,[a10]1
.L376:
	sha	d15,d15,#8
.L377:
	ld.bu	d0,[a10]
.L378:
	or	d15,d0
.L379:
	st.h	[a15],d15
.L380:
	movh.a	a15,#@his(imu963ra_mag_y)
	lea	a15,[a15]@los(imu963ra_mag_y)
.L381:
	ld.bu	d15,[a10]3
.L382:
	sha	d0,d15,#8
.L383:
	ld.bu	d15,[a10]2
.L384:
	or	d0,d15
.L385:
	st.h	[a15],d0
.L386:
	movh.a	a15,#@his(imu963ra_mag_z)
	lea	a15,[a15]@los(imu963ra_mag_z)
.L387:
	ld.bu	d15,[a10]5
.L388:
	sha	d0,d15,#8
.L389:
	ld.bu	d15,[a10]4
.L390:
	or	d0,d15
.L391:
	st.h	[a15],d0
.L35:
	mov	d4,#1
.L392:
	mov	d5,#0
	call	imu963ra_write_acc_gyro_register
.L393:
	ret
.L191:
	
__imu963ra_get_mag_function_end:
	.size	imu963ra_get_mag,__imu963ra_get_mag_function_end-imu963ra_get_mag
.L120:
	; End of function
	
	.sdecl	'.text.zf_device_imu963ra.imu963ra_init',code,cluster('imu963ra_init')
	.sect	'.text.zf_device_imu963ra.imu963ra_init'
	.align	2
	
	.global	imu963ra_init
; Function imu963ra_init
.L101:
imu963ra_init:	.type	func
	sub.a	a10,#16
.L309:
	mov	d8,#0
.L310:
	mov	d4,#10
	call	system_delay_ms
.L398:
	mov	d0,#7
	st.h	[a10],d0
.L399:
	mov	d15,#12
	st.h	[a10]4,d15
.L400:
	mov	d15,#403
	st.h	[a10]8,d15
.L401:
	mov	d4,#0
.L402:
	mov	d5,#0
.L403:
	mov.u	d6,#38528
	addih	d6,d6,#152
.L404:
	mov	d7,#0
	call	spi_init
.L405:
	mov	d4,#653
.L406:
	mov	d5,#1
.L407:
	mov	d6,#0
.L408:
	mov	d7,#3
	call	gpio_init
.L36:
	mov	d4,#1
.L409:
	mov	d5,#0
	call	imu963ra_write_acc_gyro_register
.L410:
	mov	d4,#18
.L411:
	mov	d5,#1
	call	imu963ra_write_acc_gyro_register
.L412:
	mov	d4,#2
	call	system_delay_ms
.L413:
	mov	d4,#1
.L414:
	mov	d5,#0
	call	imu963ra_write_acc_gyro_register
.L415:
	call	imu963ra_acc_gyro_self_check
.L416:
	jeq	d2,#0,.L37
.L417:
	mov	d4,#0
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	movh.a	a5,#@his(.2.str)
	lea	a5,[a5]@los(.2.str)
	mov	d5,#376
	call	debug_log_handler
.L418:
	mov	d8,#1
.L419:
	j	.L38
.L37:
	mov	d4,#13
.L420:
	mov	d5,#3
	call	imu963ra_write_acc_gyro_register
.L421:
	mov	d0,#2
.L422:
	mov	d15,#0
	jeq	d15,d0,.L39
.L423:
	mov	d1,#1
	jeq	d1,d0,.L40
.L424:
	mov	d1,#2
	jeq	d1,d0,.L41
.L425:
	mov	d15,#3
	jeq	d15,d0,.L42
	j	.L43
.L43:
	mov	d4,#0
	movh.a	a4,#@his(.3.str)
	lea	a4,[a4]@los(.3.str)
	movh.a	a5,#@his(.2.str)
	lea	a5,[a5]@los(.2.str)
	mov	d5,#392
	call	debug_log_handler
.L426:
	mov	d8,#1
.L427:
	j	.L44
.L39:
	mov	d4,#16
.L428:
	mov	d5,#48
	call	imu963ra_write_acc_gyro_register
.L429:
	movh.a	a15,#@his(imu963ra_transition_factor)
	lea	a15,[a15]@los(imu963ra_transition_factor)
.L430:
	mov	d15,#4608
	addih	d15,d15,#18048
.L431:
	st.w	[a15],d15
.L432:
	j	.L45
.L40:
	mov	d4,#16
.L433:
	mov	d5,#56
	call	imu963ra_write_acc_gyro_register
.L434:
	movh.a	a15,#@his(imu963ra_transition_factor)
	lea	a15,[a15]@los(imu963ra_transition_factor)
.L435:
	mov	d15,#5120
	addih	d15,d15,#17920
.L436:
	st.w	[a15],d15
.L437:
	j	.L46
.L41:
	mov	d4,#16
.L438:
	mov	d5,#60
	call	imu963ra_write_acc_gyro_register
.L439:
	movh.a	a15,#@his(imu963ra_transition_factor)
	lea	a15,[a15]@los(imu963ra_transition_factor)
.L440:
	mov	d15,#4096
	addih	d15,d15,#17792
.L441:
	st.w	[a15],d15
.L442:
	j	.L47
.L42:
	mov	d4,#16
.L443:
	mov	d5,#52
	call	imu963ra_write_acc_gyro_register
.L444:
	movh.a	a15,#@his(imu963ra_transition_factor)
	lea	a15,[a15]@los(imu963ra_transition_factor)
.L445:
	mov	d15,#4096
	addih	d15,d15,#17664
.L446:
	st.w	[a15],d15
.L447:
	j	.L48
.L48:
.L47:
.L46:
.L45:
.L44:
	jne	d8,#1,.L49
.L448:
	j	.L50
.L49:
	mov	d15,#4
.L449:
	mov	d0,#0
	jeq	d15,d0,.L51
.L450:
	mov	d0,#1
	jeq	d15,d0,.L52
.L451:
	mov	d0,#2
	jeq	d15,d0,.L53
.L452:
	mov	d0,#3
	jeq	d15,d0,.L54
.L453:
	mov	d0,#4
	jeq	d15,d0,.L55
.L454:
	mov	d0,#5
	jeq	d15,d0,.L56
	j	.L57
.L57:
	mov	d4,#0
	movh.a	a4,#@his(.4.str)
	lea	a4,[a4]@los(.4.str)
	movh.a	a5,#@his(.2.str)
	lea	a5,[a5]@los(.2.str)
	mov	d5,#432
	call	debug_log_handler
.L455:
	mov	d8,#1
.L456:
	j	.L58
.L51:
	mov	d4,#17
.L457:
	mov	d5,#82
	call	imu963ra_write_acc_gyro_register
.L458:
	movh.a	a15,#@his(imu963ra_transition_factor)
	lea	a15,[a15]@los(imu963ra_transition_factor)
.L459:
	mov.u	d15,#39322
	addih	d15,d15,#17252
.L460:
	st.w	[a15]4,d15
.L461:
	j	.L59
.L52:
	mov	d4,#17
.L462:
	mov	d5,#80
	call	imu963ra_write_acc_gyro_register
.L463:
	movh.a	a15,#@his(imu963ra_transition_factor)
	lea	a15,[a15]@los(imu963ra_transition_factor)
.L464:
	mov.u	d15,#39322
	addih	d15,d15,#17124
.L465:
	st.w	[a15]4,d15
.L466:
	j	.L60
.L53:
	mov	d4,#17
.L467:
	mov	d5,#84
	call	imu963ra_write_acc_gyro_register
.L468:
	movh.a	a15,#@his(imu963ra_transition_factor)
	lea	a15,[a15]@los(imu963ra_transition_factor)
.L469:
	mov	d15,#26214
	addih	d15,d15,#16996
.L470:
	st.w	[a15]4,d15
.L471:
	j	.L61
.L54:
	mov	d4,#17
.L472:
	mov	d5,#88
	call	imu963ra_write_acc_gyro_register
.L473:
	movh.a	a15,#@his(imu963ra_transition_factor)
	lea	a15,[a15]@los(imu963ra_transition_factor)
.L474:
	mov.u	d15,#52429
	addih	d15,d15,#16868
.L475:
	st.w	[a15]4,d15
.L476:
	j	.L62
.L55:
	mov	d4,#17
.L477:
	mov	d5,#92
	call	imu963ra_write_acc_gyro_register
.L478:
	movh.a	a15,#@his(imu963ra_transition_factor)
	lea	a15,[a15]@los(imu963ra_transition_factor)
.L479:
	mov.u	d15,#52429
	addih	d15,d15,#16740
.L480:
	st.w	[a15]4,d15
.L481:
	j	.L63
.L56:
	mov	d4,#17
.L482:
	mov	d5,#81
	call	imu963ra_write_acc_gyro_register
.L483:
	movh.a	a15,#@his(imu963ra_transition_factor)
	lea	a15,[a15]@los(imu963ra_transition_factor)
.L484:
	mov	d15,#13107
	addih	d15,d15,#16611
.L485:
	st.w	[a15]4,d15
.L486:
	j	.L64
.L64:
.L63:
.L62:
.L61:
.L60:
.L59:
.L58:
	jne	d8,#1,.L65
.L487:
	j	.L66
.L65:
	mov	d4,#18
.L488:
	mov	d5,#68
	call	imu963ra_write_acc_gyro_register
.L489:
	mov	d4,#19
.L490:
	mov	d5,#2
	call	imu963ra_write_acc_gyro_register
.L491:
	mov	d4,#20
.L492:
	mov	d5,#0
	call	imu963ra_write_acc_gyro_register
.L493:
	mov	d4,#21
.L494:
	mov	d5,#0
	call	imu963ra_write_acc_gyro_register
.L495:
	mov	d4,#22
.L496:
	mov	d5,#0
	call	imu963ra_write_acc_gyro_register
.L497:
	mov	d4,#24
.L498:
	mov	d5,#1
	call	imu963ra_write_acc_gyro_register
.L499:
	mov	d4,#1
.L500:
	mov	d5,#64
	call	imu963ra_write_acc_gyro_register
.L501:
	mov	d4,#20
.L502:
	mov	d5,#128
	call	imu963ra_write_acc_gyro_register
.L503:
	mov	d4,#2
	call	system_delay_ms
.L504:
	mov	d4,#20
.L505:
	mov	d5,#0
	call	imu963ra_write_acc_gyro_register
.L506:
	mov	d4,#2
	call	system_delay_ms
.L507:
	mov	d4,#13
.L508:
	mov	d5,#10
.L509:
	mov	d6,#128
	call	imu963ra_write_mag_register
.L510:
	mov	d4,#2
	call	system_delay_ms
.L511:
	mov	d4,#13
.L512:
	mov	d5,#10
.L513:
	mov	d6,#0
	call	imu963ra_write_mag_register
.L514:
	mov	d4,#2
	call	system_delay_ms
.L515:
	call	imu963ra_mag_self_check
.L516:
	jeq	d2,#0,.L67
.L517:
	mov	d4,#0
	movh.a	a4,#@his(.5.str)
	lea	a4,[a4]@los(.5.str)
	movh.a	a5,#@his(.2.str)
	lea	a5,[a5]@los(.2.str)
	mov	d5,#491
	call	debug_log_handler
.L518:
	mov	d8,#1
.L519:
	j	.L68
.L67:
	mov	d0,#1
.L520:
	mov	d15,#0
	jeq	d15,d0,.L69
.L521:
	mov	d15,#1
	jeq	d15,d0,.L70
	j	.L71
.L71:
	mov	d4,#0
	movh.a	a4,#@his(.6.str)
	lea	a4,[a4]@los(.6.str)
	movh.a	a5,#@his(.2.str)
	lea	a5,[a5]@los(.2.str)
	mov	d5,#503
	call	debug_log_handler
.L522:
	mov	d8,#1
.L523:
	j	.L72
.L69:
	mov	d4,#13
.L524:
	mov	d5,#9
.L525:
	mov	d6,#9
	call	imu963ra_write_mag_register
.L526:
	movh.a	a15,#@his(imu963ra_transition_factor)
	lea	a15,[a15]@los(imu963ra_transition_factor)
.L527:
	mov.u	d15,#32768
	addih	d15,d15,#17979
.L528:
	st.w	[a15]8,d15
.L529:
	j	.L73
.L70:
	mov	d4,#13
.L530:
	mov	d5,#9
.L531:
	mov	d6,#25
	call	imu963ra_write_mag_register
.L532:
	movh.a	a15,#@his(imu963ra_transition_factor)
	lea	a15,[a15]@los(imu963ra_transition_factor)
.L533:
	mov.u	d15,#32768
	addih	d15,d15,#17723
.L534:
	st.w	[a15]8,d15
.L535:
	j	.L74
.L74:
.L73:
.L72:
	jne	d8,#1,.L75
.L536:
	j	.L76
.L75:
	mov	d4,#13
.L537:
	mov	d5,#11
.L538:
	mov	d6,#1
	call	imu963ra_write_mag_register
.L539:
	mov	d4,#13
.L540:
	mov	d5,#0
	call	imu963ra_connect_mag
.L541:
	mov	d4,#1
.L542:
	mov	d5,#0
	call	imu963ra_write_acc_gyro_register
.L543:
	mov	d4,#20
	call	system_delay_ms
.L76:
.L68:
.L66:
.L50:
.L38:
	mov	d2,d8
.L311:
	j	.L77
.L77:
	ret
.L195:
	
__imu963ra_init_function_end:
	.size	imu963ra_init,__imu963ra_init_function_end-imu963ra_init
.L125:
	; End of function
	
	.sdecl	'.data.zf_device_imu963ra.imu963ra_gyro_x',data,cluster('imu963ra_gyro_x')
	.sect	'.data.zf_device_imu963ra.imu963ra_gyro_x'
	.global	imu963ra_gyro_x
	.align	2
imu963ra_gyro_x:	.type	object
	.size	imu963ra_gyro_x,2
	.space	2
	.sdecl	'.data.zf_device_imu963ra.imu963ra_gyro_y',data,cluster('imu963ra_gyro_y')
	.sect	'.data.zf_device_imu963ra.imu963ra_gyro_y'
	.global	imu963ra_gyro_y
	.align	2
imu963ra_gyro_y:	.type	object
	.size	imu963ra_gyro_y,2
	.space	2
	.sdecl	'.data.zf_device_imu963ra.imu963ra_gyro_z',data,cluster('imu963ra_gyro_z')
	.sect	'.data.zf_device_imu963ra.imu963ra_gyro_z'
	.global	imu963ra_gyro_z
	.align	2
imu963ra_gyro_z:	.type	object
	.size	imu963ra_gyro_z,2
	.space	2
	.sdecl	'.data.zf_device_imu963ra.imu963ra_acc_x',data,cluster('imu963ra_acc_x')
	.sect	'.data.zf_device_imu963ra.imu963ra_acc_x'
	.global	imu963ra_acc_x
	.align	2
imu963ra_acc_x:	.type	object
	.size	imu963ra_acc_x,2
	.space	2
	.sdecl	'.data.zf_device_imu963ra.imu963ra_acc_y',data,cluster('imu963ra_acc_y')
	.sect	'.data.zf_device_imu963ra.imu963ra_acc_y'
	.global	imu963ra_acc_y
	.align	2
imu963ra_acc_y:	.type	object
	.size	imu963ra_acc_y,2
	.space	2
	.sdecl	'.data.zf_device_imu963ra.imu963ra_acc_z',data,cluster('imu963ra_acc_z')
	.sect	'.data.zf_device_imu963ra.imu963ra_acc_z'
	.global	imu963ra_acc_z
	.align	2
imu963ra_acc_z:	.type	object
	.size	imu963ra_acc_z,2
	.space	2
	.sdecl	'.data.zf_device_imu963ra.imu963ra_mag_x',data,cluster('imu963ra_mag_x')
	.sect	'.data.zf_device_imu963ra.imu963ra_mag_x'
	.global	imu963ra_mag_x
	.align	2
imu963ra_mag_x:	.type	object
	.size	imu963ra_mag_x,2
	.space	2
	.sdecl	'.data.zf_device_imu963ra.imu963ra_mag_y',data,cluster('imu963ra_mag_y')
	.sect	'.data.zf_device_imu963ra.imu963ra_mag_y'
	.global	imu963ra_mag_y
	.align	2
imu963ra_mag_y:	.type	object
	.size	imu963ra_mag_y,2
	.space	2
	.sdecl	'.data.zf_device_imu963ra.imu963ra_mag_z',data,cluster('imu963ra_mag_z')
	.sect	'.data.zf_device_imu963ra.imu963ra_mag_z'
	.global	imu963ra_mag_z
	.align	2
imu963ra_mag_z:	.type	object
	.size	imu963ra_mag_z,2
	.space	2
	.sdecl	'.data.zf_device_imu963ra.imu963ra_transition_factor',data,cluster('imu963ra_transition_factor')
	.sect	'.data.zf_device_imu963ra.imu963ra_transition_factor'
	.global	imu963ra_transition_factor
	.align	2
imu963ra_transition_factor:	.type	object
	.size	imu963ra_transition_factor,12
	.word	1166020608,1097125069,1161527296
	.sdecl	'.rodata.zf_device_imu963ra..1.str',data,rom
	.sect	'.rodata.zf_device_imu963ra..1.str'
.1.str:	.type	object
	.size	.1.str,40
	.byte	73,77,85,57,54,51,82,65
	.byte	32,97,99,99,32,97,110,100
	.byte	32,103,121,114,111,32,115,101
	.byte	108,102,32,99,104,101,99,107
	.byte	32,101,114,114
	.byte	111,114,46
	.space	1
	.sdecl	'.rodata.zf_device_imu963ra..2.str',data,rom
	.sect	'.rodata.zf_device_imu963ra..2.str'
.2.str:	.type	object
	.size	.2.str,44
	.byte	46,46,47,108,105,98,114,97
	.byte	114,105,101,115,47,122,102,95
	.byte	100,101,118,105,99,101,47,122
	.byte	102,95,100,101,118,105,99,101
	.byte	95,105,109,117,57,54,51,114
	.byte	97,46,99
	.space	1
	.sdecl	'.rodata.zf_device_imu963ra..3.str',data,rom
	.sect	'.rodata.zf_device_imu963ra..3.str'
.3.str:	.type	object
	.size	.3.str,39
	.byte	73,77,85,57,54,51,82,65
	.byte	95,65,67,67,95,83,65,77
	.byte	80,76,69,95,68,69,70,65
	.byte	85,76,84,32,115,101,116,32
	.byte	101,114,114,111
	.byte	114,46
	.space	1
	.sdecl	'.rodata.zf_device_imu963ra..4.str',data,rom
	.sect	'.rodata.zf_device_imu963ra..4.str'
.4.str:	.type	object
	.size	.4.str,40
	.byte	73,77,85,57,54,51,82,65
	.byte	95,71,89,82,79,95,83,65
	.byte	77,80,76,69,95,68,69,70
	.byte	65,85,76,84,32,115,101,116
	.byte	32,101,114,114
	.byte	111,114,46
	.space	1
	.sdecl	'.rodata.zf_device_imu963ra..5.str',data,rom
	.sect	'.rodata.zf_device_imu963ra..5.str'
.5.str:	.type	object
	.size	.5.str,31
	.byte	73,77,85,57,54,51,82,65
	.byte	32,109,97,103,32,115,101,108
	.byte	102,32,99,104,101,99,107,32
	.byte	101,114,114,111
	.byte	114,46
	.space	1
	.sdecl	'.rodata.zf_device_imu963ra..6.str',data,rom
	.sect	'.rodata.zf_device_imu963ra..6.str'
.6.str:	.type	object
	.size	.6.str,39
	.byte	73,77,85,57,54,51,82,65
	.byte	95,77,65,71,95,83,65,77
	.byte	80,76,69,95,68,69,70,65
	.byte	85,76,84,32,115,101,116,32
	.byte	101,114,114,111
	.byte	114,46
	.space	1
	.calls	'imu963ra_write_acc_gyro_register','get_port'
	.calls	'imu963ra_write_acc_gyro_register','spi_write_8bit_register'
	.calls	'imu963ra_read_acc_gyro_register','get_port'
	.calls	'imu963ra_read_acc_gyro_register','spi_read_8bit_register'
	.calls	'imu963ra_read_acc_gyro_registers','get_port'
	.calls	'imu963ra_read_acc_gyro_registers','spi_read_8bit_registers'
	.calls	'imu963ra_write_mag_register','imu963ra_write_acc_gyro_register'
	.calls	'imu963ra_write_mag_register','system_delay_ms'
	.calls	'imu963ra_write_mag_register','imu963ra_read_acc_gyro_register'
	.calls	'imu963ra_read_mag_register','imu963ra_write_acc_gyro_register'
	.calls	'imu963ra_read_mag_register','system_delay_ms'
	.calls	'imu963ra_read_mag_register','imu963ra_read_acc_gyro_register'
	.calls	'imu963ra_connect_mag','imu963ra_write_acc_gyro_register'
	.calls	'imu963ra_acc_gyro_self_check','imu963ra_read_acc_gyro_register'
	.calls	'imu963ra_acc_gyro_self_check','system_delay_ms'
	.calls	'imu963ra_mag_self_check','imu963ra_read_mag_register'
	.calls	'imu963ra_mag_self_check','system_delay_ms'
	.calls	'imu963ra_get_acc','imu963ra_read_acc_gyro_registers'
	.calls	'imu963ra_get_gyro','imu963ra_read_acc_gyro_registers'
	.calls	'imu963ra_get_mag','imu963ra_write_acc_gyro_register'
	.calls	'imu963ra_get_mag','imu963ra_read_acc_gyro_register'
	.calls	'imu963ra_get_mag','imu963ra_read_acc_gyro_registers'
	.calls	'imu963ra_init','system_delay_ms'
	.calls	'imu963ra_init','spi_init'
	.calls	'imu963ra_init','gpio_init'
	.calls	'imu963ra_init','imu963ra_write_acc_gyro_register'
	.calls	'imu963ra_init','imu963ra_acc_gyro_self_check'
	.calls	'imu963ra_init','debug_log_handler'
	.calls	'imu963ra_init','imu963ra_write_mag_register'
	.calls	'imu963ra_init','imu963ra_mag_self_check'
	.calls	'imu963ra_init','imu963ra_connect_mag'
	.calls	'imu963ra_write_acc_gyro_register','',0
	.calls	'imu963ra_read_acc_gyro_register','',0
	.calls	'imu963ra_read_acc_gyro_registers','',0
	.calls	'imu963ra_write_mag_register','',0
	.calls	'imu963ra_read_mag_register','',0
	.calls	'imu963ra_connect_mag','',0
	.calls	'imu963ra_acc_gyro_self_check','',0
	.calls	'imu963ra_mag_self_check','',0
	.calls	'imu963ra_get_acc','',8
	.calls	'imu963ra_get_gyro','',8
	.calls	'imu963ra_get_mag','',8
	.extern	debug_log_handler
	.extern	system_delay_ms
	.extern	spi_write_8bit_register
	.extern	spi_read_8bit_register
	.extern	spi_read_8bit_registers
	.extern	spi_init
	.extern	get_port
	.extern	gpio_init
	.calls	'imu963ra_init','',16
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L103:
	.word	40898
	.half	3
	.word	.L104
	.byte	4
.L102:
	.byte	1
	.byte	'../libraries/zf_device/zf_device_imu963ra.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L105
	.byte	2,1,1,3
	.word	206
	.byte	4
	.byte	'Ifx__jump_and_link',0,3,1,61,17,1,1,5
	.byte	'fun',0,1,61,43
	.word	209
	.byte	6,0,7
	.byte	'__fract',0,4,128,1,7
	.byte	'float',0,4,4,8
	.byte	'Ifx__float_to_fract',0,3,1,152,2,18
	.word	254
	.byte	1,1,5
	.byte	'a',0,1,152,2,44
	.word	266
	.byte	6,0,7
	.byte	'unsigned long long int',0,8,7,9
	.byte	'void',0,3
	.word	346
	.byte	8
	.byte	'__ld64',0,3,2,135,1,19
	.word	320
	.byte	1,1,5
	.byte	'addr',0,2,135,1,32
	.word	352
	.byte	6,0,4
	.byte	'__st64',0,3,2,143,1,17,1,1,5
	.byte	'addr',0,2,143,1,30
	.word	352
	.byte	5
	.byte	'value',0,2,143,1,43
	.word	320
	.byte	6,0,7
	.byte	'unsigned int',0,4,7,7
	.byte	'int',0,4,5
.L192:
	.byte	7
	.byte	'unsigned char',0,1,8
.L214:
	.byte	7
	.byte	'unsigned short int',0,2,7,10
	.byte	'_Ifx_P_OUT_Bits',0,4,143,3,16,4,11
	.byte	'P0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	461
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	461
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	461
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	461
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	461
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	461
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	461
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	478
	.byte	16,0,2,35,2,0,12,4,181,5,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	500
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMR_Bits',0,4,169,2,16,4,11
	.byte	'PS0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	461
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	461
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	461
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	461
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	461
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	461
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	461
	.byte	1,0,2,35,1,11
	.byte	'PCL0',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	461
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	461
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	461
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	461
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	461
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	461
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	461
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	461
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	461
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	461
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	461
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	461
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	461
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	461
	.byte	1,0,2,35,3,0,12,4,133,5,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	816
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ID_Bits',0,4,110,16,4,11
	.byte	'MODREV',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	478
	.byte	16,0,2,35,2,0,12,4,148,4,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1387
	.byte	4,2,35,0,0,14,4
	.word	461
	.byte	15,3,0,10
	.byte	'_Ifx_P_IOCR0_Bits',0,4,140,1,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	3,5,2,35,0,11
	.byte	'PC0',0,1
	.word	461
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	461
	.byte	3,5,2,35,1,11
	.byte	'PC1',0,1
	.word	461
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	461
	.byte	3,5,2,35,2,11
	.byte	'PC2',0,1
	.word	461
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	3,5,2,35,3,11
	.byte	'PC3',0,1
	.word	461
	.byte	5,0,2,35,3,0,12,4,164,4,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1515
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR4_Bits',0,4,166,1,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	3,5,2,35,0,11
	.byte	'PC4',0,1
	.word	461
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	461
	.byte	3,5,2,35,1,11
	.byte	'PC5',0,1
	.word	461
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	461
	.byte	3,5,2,35,2,11
	.byte	'PC6',0,1
	.word	461
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	3,5,2,35,3,11
	.byte	'PC7',0,1
	.word	461
	.byte	5,0,2,35,3,0,12,4,180,4,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1730
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR8_Bits',0,4,179,1,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	3,5,2,35,0,11
	.byte	'PC8',0,1
	.word	461
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	461
	.byte	3,5,2,35,1,11
	.byte	'PC9',0,1
	.word	461
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	461
	.byte	3,5,2,35,2,11
	.byte	'PC10',0,1
	.word	461
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	3,5,2,35,3,11
	.byte	'PC11',0,1
	.word	461
	.byte	5,0,2,35,3,0,12,4,188,4,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1945
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR12_Bits',0,4,153,1,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	3,5,2,35,0,11
	.byte	'PC12',0,1
	.word	461
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	461
	.byte	3,5,2,35,1,11
	.byte	'PC13',0,1
	.word	461
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	461
	.byte	3,5,2,35,2,11
	.byte	'PC14',0,1
	.word	461
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	3,5,2,35,3,11
	.byte	'PC15',0,1
	.word	461
	.byte	5,0,2,35,3,0,12,4,172,4,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2162
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IN_Bits',0,4,118,16,4,11
	.byte	'P0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	461
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	461
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	461
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	461
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	461
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	461
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	461
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	478
	.byte	16,0,2,35,2,0,12,4,156,4,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2382
	.byte	4,2,35,0,0,14,24
	.word	461
	.byte	15,23,0,10
	.byte	'_Ifx_P_PDR0_Bits',0,4,205,3,16,4,11
	.byte	'PD0',0,1
	.word	461
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	461
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'PD2',0,1
	.word	461
	.byte	3,5,2,35,1,11
	.byte	'PL2',0,1
	.word	461
	.byte	1,4,2,35,1,11
	.byte	'PD3',0,1
	.word	461
	.byte	3,1,2,35,1,11
	.byte	'PL3',0,1
	.word	461
	.byte	1,0,2,35,1,11
	.byte	'PD4',0,1
	.word	461
	.byte	3,5,2,35,2,11
	.byte	'PL4',0,1
	.word	461
	.byte	1,4,2,35,2,11
	.byte	'PD5',0,1
	.word	461
	.byte	3,1,2,35,2,11
	.byte	'PL5',0,1
	.word	461
	.byte	1,0,2,35,2,11
	.byte	'PD6',0,1
	.word	461
	.byte	3,5,2,35,3,11
	.byte	'PL6',0,1
	.word	461
	.byte	1,4,2,35,3,11
	.byte	'PD7',0,1
	.word	461
	.byte	3,1,2,35,3,11
	.byte	'PL7',0,1
	.word	461
	.byte	1,0,2,35,3,0,12,4,205,5,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2705
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PDR1_Bits',0,4,226,3,16,4,11
	.byte	'PD8',0,1
	.word	461
	.byte	3,5,2,35,0,11
	.byte	'PL8',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'PD9',0,1
	.word	461
	.byte	3,1,2,35,0,11
	.byte	'PL9',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'PD10',0,1
	.word	461
	.byte	3,5,2,35,1,11
	.byte	'PL10',0,1
	.word	461
	.byte	1,4,2,35,1,11
	.byte	'PD11',0,1
	.word	461
	.byte	3,1,2,35,1,11
	.byte	'PL11',0,1
	.word	461
	.byte	1,0,2,35,1,11
	.byte	'PD12',0,1
	.word	461
	.byte	3,5,2,35,2,11
	.byte	'PL12',0,1
	.word	461
	.byte	1,4,2,35,2,11
	.byte	'PD13',0,1
	.word	461
	.byte	3,1,2,35,2,11
	.byte	'PL13',0,1
	.word	461
	.byte	1,0,2,35,2,11
	.byte	'PD14',0,1
	.word	461
	.byte	3,5,2,35,3,11
	.byte	'PL14',0,1
	.word	461
	.byte	1,4,2,35,3,11
	.byte	'PD15',0,1
	.word	461
	.byte	3,1,2,35,3,11
	.byte	'PL15',0,1
	.word	461
	.byte	1,0,2,35,3,0,12,4,213,5,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3009
	.byte	4,2,35,0,0,14,8
	.word	461
	.byte	15,7,0,10
	.byte	'_Ifx_P_ESR_Bits',0,4,88,16,4,11
	.byte	'EN0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	461
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	461
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	461
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	461
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	461
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	461
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	461
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	478
	.byte	16,0,2,35,2,0,12,4,140,4,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3334
	.byte	4,2,35,0,0,14,12
	.word	461
	.byte	15,11,0,10
	.byte	'_Ifx_P_PDISC_Bits',0,4,183,3,16,4,11
	.byte	'PDIS0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'PDIS2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'PDIS3',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'PDIS4',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'PDIS5',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'PDIS6',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'PDIS7',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'PDIS8',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'PDIS9',0,1
	.word	461
	.byte	1,6,2,35,1,11
	.byte	'PDIS10',0,1
	.word	461
	.byte	1,5,2,35,1,11
	.byte	'PDIS11',0,1
	.word	461
	.byte	1,4,2,35,1,11
	.byte	'PDIS12',0,1
	.word	461
	.byte	1,3,2,35,1,11
	.byte	'PDIS13',0,1
	.word	461
	.byte	1,2,2,35,1,11
	.byte	'PDIS14',0,1
	.word	461
	.byte	1,1,2,35,1,11
	.byte	'PDIS15',0,1
	.word	461
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	478
	.byte	16,0,2,35,2,0,12,4,197,5,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3674
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PCSR_Bits',0,4,165,3,16,4,11
	.byte	'SEL0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'SEL1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'SEL2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'SEL3',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'SEL4',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'SEL5',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'SEL6',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'SEL7',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	461
	.byte	2,6,2,35,1,11
	.byte	'SEL10',0,1
	.word	461
	.byte	1,5,2,35,1,11
	.byte	'SEL11',0,1
	.word	461
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	438
	.byte	19,1,2,35,0,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,12,4,189,5,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4040
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR0_Bits',0,4,206,2,16,4,11
	.byte	'PS0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	438
	.byte	28,0,2,35,0,0,12,4,149,5,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4326
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR4_Bits',0,4,227,2,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	4,4,2,35,0,11
	.byte	'PS4',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	438
	.byte	24,0,2,35,0,0,12,4,165,5,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4473
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR8_Bits',0,4,238,2,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'PS8',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	461
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	461
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	461
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	438
	.byte	20,0,2,35,0,0,12,4,173,5,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4642
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR12_Bits',0,4,216,2,16,4,11
	.byte	'reserved_0',0,2
	.word	478
	.byte	12,4,2,35,0,11
	.byte	'PS12',0,1
	.word	461
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	461
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	461
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	461
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	478
	.byte	16,0,2,35,2,0,12,4,157,5,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4814
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR0_Bits',0,4,232,1,16,4,11
	.byte	'reserved_0',0,2
	.word	478
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	461
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	461
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	461
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	478
	.byte	12,0,2,35,2,0,12,4,229,4,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4989
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR4_Bits',0,4,253,1,16,4,11
	.byte	'reserved_0',0,4
	.word	438
	.byte	20,12,2,35,0,11
	.byte	'PCL4',0,1
	.word	461
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	461
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	461
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	461
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	8,0,2,35,3,0,12,4,245,4,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5163
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR8_Bits',0,4,136,2,16,4,11
	.byte	'reserved_0',0,4
	.word	438
	.byte	24,8,2,35,0,11
	.byte	'PCL8',0,1
	.word	461
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	461
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	461
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	461
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	461
	.byte	4,0,2,35,3,0,12,4,253,4,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5337
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR12_Bits',0,4,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	438
	.byte	28,4,2,35,0,11
	.byte	'PCL12',0,1
	.word	461
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	461
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	461
	.byte	1,0,2,35,3,0,12,4,237,4,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5513
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR_Bits',0,4,249,2,16,4,11
	.byte	'PS0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	461
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	461
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	461
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	461
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	461
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	461
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	461
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	478
	.byte	16,0,2,35,2,0,12,4,141,5,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5669
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR_Bits',0,4,147,2,16,4,11
	.byte	'reserved_0',0,2
	.word	478
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	461
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	461
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	461
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	461
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	461
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	461
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	461
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	461
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	461
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	461
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	461
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	461
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	461
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	461
	.byte	1,0,2,35,3,0,12,4,221,4,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6002
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR0_Bits',0,4,192,1,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	438
	.byte	30,0,2,35,0,0,12,4,196,4,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6350
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_Bits',0,4,200,1,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	438
	.byte	30,0,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_P21_Bits',0,4,208,1,16,4,11
	.byte	'RDIS_CTRL',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'RX_DIS',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'TERM',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'LRXTERM',0,1
	.word	461
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	438
	.byte	24,0,2,35,0,0,12,4,204,4,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6474
	.byte	4,2,35,0,13
	.byte	'B_P21',0
	.word	6558
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR2_Bits',0,4,218,1,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'LVDSR',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'LVDSRL',0,1
	.word	461
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	461
	.byte	2,4,2,35,1,11
	.byte	'TDIS_CTRL',0,1
	.word	461
	.byte	1,3,2,35,1,11
	.byte	'TX_DIS',0,1
	.word	461
	.byte	1,2,2,35,1,11
	.byte	'TX_PD',0,1
	.word	461
	.byte	1,1,2,35,1,11
	.byte	'TX_PWDPD',0,1
	.word	461
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	478
	.byte	16,0,2,35,2,0,12,4,213,4,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6738
	.byte	4,2,35,0,0,14,76
	.word	461
	.byte	15,75,0,10
	.byte	'_Ifx_P_ACCEN1_Bits',0,4,82,16,4,11
	.byte	'reserved_0',0,4
	.word	438
	.byte	32,0,2,35,0,0,12,4,132,4,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6991
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ACCEN0_Bits',0,4,45,16,4,11
	.byte	'EN0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	461
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	461
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	461
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	461
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	461
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	461
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	461
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	461
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	461
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	461
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	461
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	461
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	461
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	461
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	461
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	461
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	461
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	461
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	461
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	461
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	461
	.byte	1,0,2,35,3,0,12,4,252,3,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7078
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P',0,4,229,5,25,128,2,13
	.byte	'OUT',0
	.word	776
	.byte	4,2,35,0,13
	.byte	'OMR',0
	.word	1347
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	1466
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	1506
	.byte	4,2,35,12,13
	.byte	'IOCR0',0
	.word	1690
	.byte	4,2,35,16,13
	.byte	'IOCR4',0
	.word	1905
	.byte	4,2,35,20,13
	.byte	'IOCR8',0
	.word	2122
	.byte	4,2,35,24,13
	.byte	'IOCR12',0
	.word	2342
	.byte	4,2,35,28,13
	.byte	'reserved_20',0
	.word	1506
	.byte	4,2,35,32,13
	.byte	'IN',0
	.word	2656
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	2696
	.byte	24,2,35,40,13
	.byte	'PDR0',0
	.word	2969
	.byte	4,2,35,64,13
	.byte	'PDR1',0
	.word	3285
	.byte	4,2,35,68,13
	.byte	'reserved_48',0
	.word	3325
	.byte	8,2,35,72,13
	.byte	'ESR',0
	.word	3625
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	3665
	.byte	12,2,35,84,13
	.byte	'PDISC',0
	.word	4000
	.byte	4,2,35,96,13
	.byte	'PCSR',0
	.word	4286
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	3325
	.byte	8,2,35,104,13
	.byte	'OMSR0',0
	.word	4433
	.byte	4,2,35,112,13
	.byte	'OMSR4',0
	.word	4602
	.byte	4,2,35,116,13
	.byte	'OMSR8',0
	.word	4774
	.byte	4,2,35,120,13
	.byte	'OMSR12',0
	.word	4949
	.byte	4,2,35,124,13
	.byte	'OMCR0',0
	.word	5123
	.byte	4,3,35,128,1,13
	.byte	'OMCR4',0
	.word	5297
	.byte	4,3,35,132,1,13
	.byte	'OMCR8',0
	.word	5473
	.byte	4,3,35,136,1,13
	.byte	'OMCR12',0
	.word	5629
	.byte	4,3,35,140,1,13
	.byte	'OMSR',0
	.word	5962
	.byte	4,3,35,144,1,13
	.byte	'OMCR',0
	.word	6310
	.byte	4,3,35,148,1,13
	.byte	'reserved_98',0
	.word	3325
	.byte	8,3,35,152,1,13
	.byte	'LPCR0',0
	.word	6434
	.byte	4,3,35,160,1,13
	.byte	'LPCR1',0
	.word	6683
	.byte	4,3,35,164,1,13
	.byte	'LPCR2',0
	.word	6942
	.byte	4,3,35,168,1,13
	.byte	'reserved_A4',0
	.word	6982
	.byte	76,3,35,172,1,13
	.byte	'ACCEN1',0
	.word	7038
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	7605
	.byte	4,3,35,252,1,0,16
	.word	7645
	.byte	3
	.word	8248
	.byte	17,3,172,1,9,4,18
	.byte	'IfxPort_State_notChanged',0,0,18
	.byte	'IfxPort_State_high',0,1,18
	.byte	'IfxPort_State_low',0,128,128,4,18
	.byte	'IfxPort_State_toggled',0,129,128,4,0,4
	.byte	'IfxPort_setPinState',0,3,3,208,4,17,1,1,5
	.byte	'port',0,3,208,4,44
	.word	8253
	.byte	5
	.byte	'pinIndex',0,3,208,4,56
	.word	461
	.byte	5
	.byte	'action',0,3,208,4,80
	.word	8258
	.byte	6,0,7
	.byte	'char',0,1,6,3
	.word	8439
	.byte	19
	.byte	'debug_log_handler',0,5,113,9,1,1,1,1,5
	.byte	'pass',0,5,113,47
	.word	461
	.byte	5
	.byte	'str',0,5,113,59
	.word	8447
	.byte	5
	.byte	'file',0,5,113,70
	.word	8447
	.byte	5
	.byte	'line',0,5,113,80
	.word	454
	.byte	0
.L207:
	.byte	7
	.byte	'unsigned long int',0,4,7,19
	.byte	'system_delay_ms',0,6,46,9,1,1,1,1,5
	.byte	'time',0,6,46,45
	.word	8530
	.byte	0,17,7,42,9,1,18
	.byte	'SPI_0',0,0,18
	.byte	'SPI_1',0,1,18
	.byte	'SPI_2',0,2,18
	.byte	'SPI_3',0,3,0,20
	.word	461
	.byte	20
	.word	461
	.byte	19
	.byte	'spi_write_8bit_register',0,7,149,1,13,1,1,1,1,5
	.byte	'spi_n',0,7,149,1,61
	.word	8589
	.byte	5
	.byte	'register_name',0,7,149,1,80
	.word	8627
	.byte	5
	.byte	'data',0,7,149,1,107
	.word	8632
	.byte	0,20
	.word	461
	.byte	21
	.byte	'spi_read_8bit_register',0,7,161,1,13
	.word	461
	.byte	1,1,1,1,5
	.byte	'spi_n',0,7,161,1,61
	.word	8589
	.byte	5
	.byte	'register_name',0,7,161,1,80
	.word	8723
	.byte	0,20
	.word	461
.L205:
	.byte	3
	.word	461
	.byte	19
	.byte	'spi_read_8bit_registers',0,7,162,1,13,1,1,1,1,5
	.byte	'spi_n',0,7,162,1,61
	.word	8589
	.byte	5
	.byte	'register_name',0,7,162,1,80
	.word	8803
	.byte	5
	.byte	'data',0,7,162,1,102
	.word	8808
	.byte	5
	.byte	'len',0,7,162,1,115
	.word	8530
	.byte	0,17,7,50,9,1,18
	.byte	'SPI_MODE0',0,0,18
	.byte	'SPI_MODE1',0,1,18
	.byte	'SPI_MODE2',0,2,18
	.byte	'SPI_MODE3',0,3,0,17,7,58,9,2,18
	.byte	'SPI0_SCLK_P20_11',0,0,18
	.byte	'SPI0_SCLK_P20_13',0,1,18
	.byte	'SPI1_SCLK_P10_2',0,230,0,18
	.byte	'SPI1_SCLK_P11_6',0,231,0,18
	.byte	'SPI2_SCLK_P13_0',0,204,1,18
	.byte	'SPI2_SCLK_P13_1',0,205,1,18
	.byte	'SPI2_SCLK_P15_3',0,206,1,18
	.byte	'SPI2_SCLK_P15_6',0,207,1,18
	.byte	'SPI2_SCLK_P15_8',0,208,1,18
	.byte	'SPI3_SCLK_P02_7',0,178,2,18
	.byte	'SPI3_SCLK_P22_0',0,179,2,18
	.byte	'SPI3_SCLK_P22_1',0,180,2,18
	.byte	'SPI3_SCLK_P22_3',0,181,2,18
	.byte	'SPI3_SCLK_P33_11',0,182,2,0,17,7,69,9,2,18
	.byte	'SPI0_MOSI_P20_12',0,6,18
	.byte	'SPI0_MOSI_P20_14',0,7,18
	.byte	'SPI1_MOSI_P10_1',0,236,0,18
	.byte	'SPI1_MOSI_P10_3',0,237,0,18
	.byte	'SPI1_MOSI_P11_9',0,238,0,18
	.byte	'SPI2_MOSI_P13_3',0,210,1,18
	.byte	'SPI2_MOSI_P15_5',0,211,1,18
	.byte	'SPI2_MOSI_P15_6',0,212,1,18
	.byte	'SPI3_MOSI_P02_6',0,184,2,18
	.byte	'SPI3_MOSI_P10_6',0,185,2,18
	.byte	'SPI3_MOSI_P22_0',0,186,2,18
	.byte	'SPI3_MOSI_P22_3',0,187,2,18
	.byte	'SPI3_MOSI_P33_12',0,188,2,0,17,7,80,9,2,18
	.byte	'SPI0_MISO_P20_12',0,12,18
	.byte	'SPI1_MISO_P10_1',0,242,0,18
	.byte	'SPI1_MISO_P11_3',0,243,0,18
	.byte	'SPI2_MISO_P15_2',0,216,1,18
	.byte	'SPI2_MISO_P15_4',0,217,1,18
	.byte	'SPI2_MISO_P15_7',0,218,1,18
	.byte	'SPI2_MISO_P21_2',0,219,1,18
	.byte	'SPI2_MISO_P21_3',0,220,1,18
	.byte	'SPI3_MISO_P02_5',0,190,2,18
	.byte	'SPI3_MISO_P22_1',0,191,2,18
	.byte	'SPI3_MISO_P21_2',0,192,2,18
	.byte	'SPI3_MISO_P21_3',0,193,2,18
	.byte	'SPI3_MISO_P33_13',0,194,2,0,17,7,91,9,2,18
	.byte	'SPI0_CS0_P20_8',0,18,18
	.byte	'SPI0_CS1_P20_9',0,24,18
	.byte	'SPI0_CS2_P20_13',0,30,18
	.byte	'SPI0_CS3_P11_10',0,36,18
	.byte	'SPI0_CS4_P11_11',0,42,18
	.byte	'SPI0_CS5_P11_2',0,48,18
	.byte	'SPI0_CS6_P20_10',0,54,18
	.byte	'SPI0_CS7_P33_5',0,60,18
	.byte	'SPI0_CS8_P20_6',0,194,0,18
	.byte	'SPI0_CS9_P20_3',0,200,0,18
	.byte	'SPI0_CS13_P15_0',0,224,0,18
	.byte	'SPI1_CS0_P20_8',0,248,0,18
	.byte	'SPI1_CS1_P20_9',0,254,0,18
	.byte	'SPI1_CS2_P20_13',0,132,1,18
	.byte	'SPI1_CS3_P11_10',0,138,1,18
	.byte	'SPI1_CS4_P11_11',0,144,1,18
	.byte	'SPI1_CS5_P11_2',0,150,1,18
	.byte	'SPI1_CS6_P33_10',0,156,1,18
	.byte	'SPI1_CS7_P33_5',0,162,1,18
	.byte	'SPI1_CS8_P10_4',0,168,1,18
	.byte	'SPI1_CS9_P10_5',0,174,1,18
	.byte	'SPI2_CS0_P15_2',0,222,1,18
	.byte	'SPI2_CS1_P14_2',0,228,1,18
	.byte	'SPI2_CS2_P14_6',0,234,1,18
	.byte	'SPI2_CS3_P14_3',0,240,1,18
	.byte	'SPI2_CS5_P15_1',0,252,1,18
	.byte	'SPI2_CS6_P33_13',0,130,2,18
	.byte	'SPI2_CS7_P20_10',0,136,2,18
	.byte	'SPI2_CS8_P20_6',0,142,2,18
	.byte	'SPI2_CS9_P20_3',0,148,2,18
	.byte	'SPI3_CS0_P02_4',0,196,2,18
	.byte	'SPI3_CS1_P02_0',0,202,2,18
	.byte	'SPI3_CS1_P33_9',0,203,2,18
	.byte	'SPI3_CS2_P02_1',0,208,2,18
	.byte	'SPI3_CS2_P33_8',0,209,2,18
	.byte	'SPI3_CS3_P02_2',0,214,2,18
	.byte	'SPI3_CS4_P02_3',0,220,2,18
	.byte	'SPI3_CS5_P02_8',0,226,2,18
	.byte	'SPI3_CS6_P00_8',0,232,2,18
	.byte	'SPI3_CS7_P00_9',0,238,2,18
	.byte	'SPI3_CS7_P33_7',0,239,2,18
	.byte	'SPI3_CS8_P10_5',0,244,2,18
	.byte	'SPI3_CS11_P33_10',0,134,3,18
	.byte	'SPI3_CS12_P22_2',0,140,3,18
	.byte	'SPI3_CS13_P23_1',0,146,3,18
	.byte	'SPI_CS_NULL',0,147,3,0,19
	.byte	'spi_init',0,7,170,1,13,1,1,1,1,5
	.byte	'spi_n',0,7,170,1,61
	.word	8589
	.byte	5
	.byte	'mode',0,7,170,1,82
	.word	8912
	.byte	5
	.byte	'baud',0,7,170,1,95
	.word	8530
	.byte	5
	.byte	'sck_pin',0,7,170,1,118
	.word	8966
	.byte	5
	.byte	'mosi_pin',0,7,170,1,145,1
	.word	9239
	.byte	5
	.byte	'miso_pin',0,7,170,1,173,1
	.word	9493
	.byte	5
	.byte	'cs_pin',0,7,170,1,199,1
	.word	9747
	.byte	0,22
	.word	214
	.byte	23
	.word	240
	.byte	6,0,22
	.word	275
	.byte	23
	.word	307
	.byte	6,0,22
	.word	357
	.byte	23
	.word	376
	.byte	6,0,22
	.word	392
	.byte	23
	.word	407
	.byte	23
	.word	421
	.byte	6,0,22
	.word	8361
	.byte	23
	.word	8389
	.byte	23
	.word	8403
	.byte	23
	.word	8421
	.byte	6,0,17,8,42,9,2,18
	.byte	'P00_0',0,0,18
	.byte	'P00_1',0,1,18
	.byte	'P00_2',0,2,18
	.byte	'P00_3',0,3,18
	.byte	'P00_4',0,4,18
	.byte	'P00_5',0,5,18
	.byte	'P00_6',0,6,18
	.byte	'P00_7',0,7,18
	.byte	'P00_8',0,8,18
	.byte	'P00_9',0,9,18
	.byte	'P00_10',0,10,18
	.byte	'P00_11',0,11,18
	.byte	'P00_12',0,12,18
	.byte	'P00_13',0,13,18
	.byte	'P00_14',0,14,18
	.byte	'P00_15',0,15,18
	.byte	'P02_0',0,192,0,18
	.byte	'P02_1',0,193,0,18
	.byte	'P02_2',0,194,0,18
	.byte	'P02_3',0,195,0,18
	.byte	'P02_4',0,196,0,18
	.byte	'P02_5',0,197,0,18
	.byte	'P02_6',0,198,0,18
	.byte	'P02_7',0,199,0,18
	.byte	'P02_8',0,200,0,18
	.byte	'P02_9',0,201,0,18
	.byte	'P02_10',0,202,0,18
	.byte	'P02_11',0,203,0,18
	.byte	'P02_12',0,204,0,18
	.byte	'P02_13',0,205,0,18
	.byte	'P02_14',0,206,0,18
	.byte	'P02_15',0,207,0,18
	.byte	'P10_0',0,192,2,18
	.byte	'P10_1',0,193,2,18
	.byte	'P10_2',0,194,2,18
	.byte	'P10_3',0,195,2,18
	.byte	'P10_4',0,196,2,18
	.byte	'P10_5',0,197,2,18
	.byte	'P10_6',0,198,2,18
	.byte	'P10_7',0,199,2,18
	.byte	'P10_8',0,200,2,18
	.byte	'P10_9',0,201,2,18
	.byte	'P10_10',0,202,2,18
	.byte	'P10_11',0,203,2,18
	.byte	'P10_12',0,204,2,18
	.byte	'P10_13',0,205,2,18
	.byte	'P10_14',0,206,2,18
	.byte	'P10_15',0,207,2,18
	.byte	'P11_0',0,224,2,18
	.byte	'P11_1',0,225,2,18
	.byte	'P11_2',0,226,2,18
	.byte	'P11_3',0,227,2,18
	.byte	'P11_4',0,228,2,18
	.byte	'P11_5',0,229,2,18
	.byte	'P11_6',0,230,2,18
	.byte	'P11_7',0,231,2,18
	.byte	'P11_8',0,232,2,18
	.byte	'P11_9',0,233,2,18
	.byte	'P11_10',0,234,2,18
	.byte	'P11_11',0,235,2,18
	.byte	'P11_12',0,236,2,18
	.byte	'P11_13',0,237,2,18
	.byte	'P11_14',0,238,2,18
	.byte	'P11_15',0,239,2,18
	.byte	'P13_0',0,160,3,18
	.byte	'P13_1',0,161,3,18
	.byte	'P13_2',0,162,3,18
	.byte	'P13_3',0,163,3,18
	.byte	'P13_4',0,164,3,18
	.byte	'P13_5',0,165,3,18
	.byte	'P13_6',0,166,3,18
	.byte	'P13_7',0,167,3,18
	.byte	'P13_8',0,168,3,18
	.byte	'P13_9',0,169,3,18
	.byte	'P13_10',0,170,3,18
	.byte	'P13_11',0,171,3,18
	.byte	'P13_12',0,172,3,18
	.byte	'P13_13',0,173,3,18
	.byte	'P13_14',0,174,3,18
	.byte	'P13_15',0,175,3,18
	.byte	'P14_0',0,192,3,18
	.byte	'P14_1',0,193,3,18
	.byte	'P14_2',0,194,3,18
	.byte	'P14_3',0,195,3,18
	.byte	'P14_4',0,196,3,18
	.byte	'P14_5',0,197,3,18
	.byte	'P14_6',0,198,3,18
	.byte	'P14_7',0,199,3,18
	.byte	'P14_8',0,200,3,18
	.byte	'P14_9',0,201,3,18
	.byte	'P14_10',0,202,3,18
	.byte	'P14_11',0,203,3,18
	.byte	'P14_12',0,204,3,18
	.byte	'P14_13',0,205,3,18
	.byte	'P14_14',0,206,3,18
	.byte	'P14_15',0,207,3,18
	.byte	'P15_0',0,224,3,18
	.byte	'P15_1',0,225,3,18
	.byte	'P15_2',0,226,3,18
	.byte	'P15_3',0,227,3,18
	.byte	'P15_4',0,228,3,18
	.byte	'P15_5',0,229,3,18
	.byte	'P15_6',0,230,3,18
	.byte	'P15_7',0,231,3,18
	.byte	'P15_8',0,232,3,18
	.byte	'P15_9',0,233,3,18
	.byte	'P15_10',0,234,3,18
	.byte	'P15_11',0,235,3,18
	.byte	'P15_12',0,236,3,18
	.byte	'P15_13',0,237,3,18
	.byte	'P15_14',0,238,3,18
	.byte	'P15_15',0,239,3,18
	.byte	'P20_0',0,128,5,18
	.byte	'P20_1',0,129,5,18
	.byte	'P20_2',0,130,5,18
	.byte	'P20_3',0,131,5,18
	.byte	'P20_4',0,132,5,18
	.byte	'P20_5',0,133,5,18
	.byte	'P20_6',0,134,5,18
	.byte	'P20_7',0,135,5,18
	.byte	'P20_8',0,136,5,18
	.byte	'P20_9',0,137,5,18
	.byte	'P20_10',0,138,5,18
	.byte	'P20_11',0,139,5,18
	.byte	'P20_12',0,140,5,18
	.byte	'P20_13',0,141,5,18
	.byte	'P20_14',0,142,5,18
	.byte	'P20_15',0,143,5,18
	.byte	'P21_0',0,160,5,18
	.byte	'P21_1',0,161,5,18
	.byte	'P21_2',0,162,5,18
	.byte	'P21_3',0,163,5,18
	.byte	'P21_4',0,164,5,18
	.byte	'P21_5',0,165,5,18
	.byte	'P21_6',0,166,5,18
	.byte	'P21_7',0,167,5,18
	.byte	'P21_8',0,168,5,18
	.byte	'P21_9',0,169,5,18
	.byte	'P21_10',0,170,5,18
	.byte	'P21_11',0,171,5,18
	.byte	'P21_12',0,172,5,18
	.byte	'P21_13',0,173,5,18
	.byte	'P21_14',0,174,5,18
	.byte	'P21_15',0,175,5,18
	.byte	'P22_0',0,192,5,18
	.byte	'P22_1',0,193,5,18
	.byte	'P22_2',0,194,5,18
	.byte	'P22_3',0,195,5,18
	.byte	'P22_4',0,196,5,18
	.byte	'P22_5',0,197,5,18
	.byte	'P22_6',0,198,5,18
	.byte	'P22_7',0,199,5,18
	.byte	'P22_8',0,200,5,18
	.byte	'P22_9',0,201,5,18
	.byte	'P22_10',0,202,5,18
	.byte	'P22_11',0,203,5,18
	.byte	'P22_12',0,204,5,18
	.byte	'P22_13',0,205,5,18
	.byte	'P22_14',0,206,5,18
	.byte	'P22_15',0,207,5,18
	.byte	'P23_0',0,224,5,18
	.byte	'P23_1',0,225,5,18
	.byte	'P23_2',0,226,5,18
	.byte	'P23_3',0,227,5,18
	.byte	'P23_4',0,228,5,18
	.byte	'P23_5',0,229,5,18
	.byte	'P23_6',0,230,5,18
	.byte	'P23_7',0,231,5,18
	.byte	'P23_8',0,232,5,18
	.byte	'P23_9',0,233,5,18
	.byte	'P23_10',0,234,5,18
	.byte	'P23_11',0,235,5,18
	.byte	'P23_12',0,236,5,18
	.byte	'P23_13',0,237,5,18
	.byte	'P23_14',0,238,5,18
	.byte	'P23_15',0,239,5,18
	.byte	'P32_0',0,128,8,18
	.byte	'P32_1',0,129,8,18
	.byte	'P32_2',0,130,8,18
	.byte	'P32_3',0,131,8,18
	.byte	'P32_4',0,132,8,18
	.byte	'P32_5',0,133,8,18
	.byte	'P32_6',0,134,8,18
	.byte	'P32_7',0,135,8,18
	.byte	'P32_8',0,136,8,18
	.byte	'P32_9',0,137,8,18
	.byte	'P32_10',0,138,8,18
	.byte	'P32_11',0,139,8,18
	.byte	'P32_12',0,140,8,18
	.byte	'P32_13',0,141,8,18
	.byte	'P32_14',0,142,8,18
	.byte	'P32_15',0,143,8,18
	.byte	'P33_0',0,160,8,18
	.byte	'P33_1',0,161,8,18
	.byte	'P33_2',0,162,8,18
	.byte	'P33_3',0,163,8,18
	.byte	'P33_4',0,164,8,18
	.byte	'P33_5',0,165,8,18
	.byte	'P33_6',0,166,8,18
	.byte	'P33_7',0,167,8,18
	.byte	'P33_8',0,168,8,18
	.byte	'P33_9',0,169,8,18
	.byte	'P33_10',0,170,8,18
	.byte	'P33_11',0,171,8,18
	.byte	'P33_12',0,172,8,18
	.byte	'P33_13',0,173,8,18
	.byte	'P33_14',0,174,8,18
	.byte	'P33_15',0,175,8,0,21
	.byte	'get_port',0,8,114,13
	.word	8253
	.byte	1,1,1,1,5
	.byte	'pin',0,8,114,56
	.word	10794
	.byte	0,17,8,91,9,1,18
	.byte	'GPI',0,0,18
	.byte	'GPO',0,1,0,17,8,103,9,1,18
	.byte	'GPI_FLOATING_IN',0,0,18
	.byte	'GPI_PULL_UP',0,1,18
	.byte	'GPI_PULL_DOWN',0,2,18
	.byte	'GPO_PUSH_PULL',0,3,18
	.byte	'GPO_OPEN_DTAIN',0,4,0,19
	.byte	'gpio_init',0,8,143,1,7,1,1,1,1,5
	.byte	'pin',0,8,143,1,40
	.word	10794
	.byte	5
	.byte	'dir',0,8,143,1,59
	.word	12768
	.byte	5
	.byte	'dat',0,8,143,1,70
	.word	461
	.byte	5
	.byte	'pinconf',0,8,143,1,90
	.word	12786
	.byte	0
.L187:
	.byte	14,6
	.word	461
	.byte	15,5,0
.L231:
	.byte	7
	.byte	'short int',0,2,5,24
	.byte	'__wchar_t',0,9,1,1
	.word	12958
	.byte	24
	.byte	'__size_t',0,9,1,1
	.word	438
	.byte	24
	.byte	'__ptrdiff_t',0,9,1,1
	.word	454
	.byte	25,1,3
	.word	13026
	.byte	24
	.byte	'__codeptr',0,9,1,1
	.word	13028
	.byte	24
	.byte	'__intptr_t',0,9,1,1
	.word	454
	.byte	24
	.byte	'__uintptr_t',0,9,1,1
	.word	438
	.byte	24
	.byte	'_iob_flag_t',0,10,82,25
	.word	478
	.byte	24
	.byte	'boolean',0,11,101,29
	.word	461
	.byte	24
	.byte	'uint8',0,11,105,29
	.word	461
	.byte	24
	.byte	'uint16',0,11,109,29
	.word	478
	.byte	24
	.byte	'uint32',0,11,113,29
	.word	8530
	.byte	24
	.byte	'uint64',0,11,118,29
	.word	320
	.byte	24
	.byte	'sint16',0,11,126,29
	.word	12958
	.byte	7
	.byte	'long int',0,4,5,24
	.byte	'sint32',0,11,131,1,29
	.word	13200
	.byte	7
	.byte	'long long int',0,8,5,24
	.byte	'sint64',0,11,138,1,29
	.word	13228
	.byte	24
	.byte	'float32',0,11,167,1,29
	.word	266
	.byte	24
	.byte	'pvoid',0,12,57,28
	.word	352
	.byte	24
	.byte	'Ifx_TickTime',0,12,79,28
	.word	13228
	.byte	7
	.byte	'char',0,1,6,24
	.byte	'int8',0,13,54,29
	.word	13313
	.byte	24
	.byte	'int16',0,13,55,29
	.word	12958
	.byte	24
	.byte	'int32',0,13,56,29
	.word	454
	.byte	24
	.byte	'int64',0,13,57,29
	.word	13228
	.byte	24
	.byte	'spi_index_enum',0,7,48,2
	.word	8589
	.byte	24
	.byte	'spi_mode_enum',0,7,56,2
	.word	8912
	.byte	24
	.byte	'spi_sck_pin_enum',0,7,67,2
	.word	8966
	.byte	24
	.byte	'spi_mosi_pin_enum',0,7,78,2
	.word	9239
	.byte	24
	.byte	'spi_miso_pin_enum',0,7,89,2
	.word	9493
	.byte	24
	.byte	'spi_cs_pin_enum',0,7,140,1,2
	.word	9747
	.byte	24
	.byte	'Ifx_P_ACCEN0_Bits',0,4,79,3
	.word	7078
	.byte	24
	.byte	'Ifx_P_ACCEN1_Bits',0,4,85,3
	.word	6991
	.byte	24
	.byte	'Ifx_P_ESR_Bits',0,4,107,3
	.word	3334
	.byte	24
	.byte	'Ifx_P_ID_Bits',0,4,115,3
	.word	1387
	.byte	24
	.byte	'Ifx_P_IN_Bits',0,4,137,1,3
	.word	2382
	.byte	24
	.byte	'Ifx_P_IOCR0_Bits',0,4,150,1,3
	.word	1515
	.byte	24
	.byte	'Ifx_P_IOCR12_Bits',0,4,163,1,3
	.word	2162
	.byte	24
	.byte	'Ifx_P_IOCR4_Bits',0,4,176,1,3
	.word	1730
	.byte	24
	.byte	'Ifx_P_IOCR8_Bits',0,4,189,1,3
	.word	1945
	.byte	24
	.byte	'Ifx_P_LPCR0_Bits',0,4,197,1,3
	.word	6350
	.byte	24
	.byte	'Ifx_P_LPCR1_Bits',0,4,205,1,3
	.word	6474
	.byte	24
	.byte	'Ifx_P_LPCR1_P21_Bits',0,4,215,1,3
	.word	6558
	.byte	24
	.byte	'Ifx_P_LPCR2_Bits',0,4,229,1,3
	.word	6738
	.byte	24
	.byte	'Ifx_P_OMCR0_Bits',0,4,240,1,3
	.word	4989
	.byte	24
	.byte	'Ifx_P_OMCR12_Bits',0,4,250,1,3
	.word	5513
	.byte	24
	.byte	'Ifx_P_OMCR4_Bits',0,4,133,2,3
	.word	5163
	.byte	24
	.byte	'Ifx_P_OMCR8_Bits',0,4,144,2,3
	.word	5337
	.byte	24
	.byte	'Ifx_P_OMCR_Bits',0,4,166,2,3
	.word	6002
	.byte	24
	.byte	'Ifx_P_OMR_Bits',0,4,203,2,3
	.word	816
	.byte	24
	.byte	'Ifx_P_OMSR0_Bits',0,4,213,2,3
	.word	4326
	.byte	24
	.byte	'Ifx_P_OMSR12_Bits',0,4,224,2,3
	.word	4814
	.byte	24
	.byte	'Ifx_P_OMSR4_Bits',0,4,235,2,3
	.word	4473
	.byte	24
	.byte	'Ifx_P_OMSR8_Bits',0,4,246,2,3
	.word	4642
	.byte	24
	.byte	'Ifx_P_OMSR_Bits',0,4,140,3,3
	.word	5669
	.byte	24
	.byte	'Ifx_P_OUT_Bits',0,4,162,3,3
	.word	500
	.byte	24
	.byte	'Ifx_P_PCSR_Bits',0,4,180,3,3
	.word	4040
	.byte	24
	.byte	'Ifx_P_PDISC_Bits',0,4,202,3,3
	.word	3674
	.byte	24
	.byte	'Ifx_P_PDR0_Bits',0,4,223,3,3
	.word	2705
	.byte	24
	.byte	'Ifx_P_PDR1_Bits',0,4,244,3,3
	.word	3009
	.byte	24
	.byte	'Ifx_P_ACCEN0',0,4,129,4,3
	.word	7605
	.byte	24
	.byte	'Ifx_P_ACCEN1',0,4,137,4,3
	.word	7038
	.byte	24
	.byte	'Ifx_P_ESR',0,4,145,4,3
	.word	3625
	.byte	24
	.byte	'Ifx_P_ID',0,4,153,4,3
	.word	1466
	.byte	24
	.byte	'Ifx_P_IN',0,4,161,4,3
	.word	2656
	.byte	24
	.byte	'Ifx_P_IOCR0',0,4,169,4,3
	.word	1690
	.byte	24
	.byte	'Ifx_P_IOCR12',0,4,177,4,3
	.word	2342
	.byte	24
	.byte	'Ifx_P_IOCR4',0,4,185,4,3
	.word	1905
	.byte	24
	.byte	'Ifx_P_IOCR8',0,4,193,4,3
	.word	2122
	.byte	24
	.byte	'Ifx_P_LPCR0',0,4,201,4,3
	.word	6434
	.byte	24
	.byte	'Ifx_P_LPCR1',0,4,210,4,3
	.word	6683
	.byte	24
	.byte	'Ifx_P_LPCR2',0,4,218,4,3
	.word	6942
	.byte	24
	.byte	'Ifx_P_OMCR',0,4,226,4,3
	.word	6310
	.byte	24
	.byte	'Ifx_P_OMCR0',0,4,234,4,3
	.word	5123
	.byte	24
	.byte	'Ifx_P_OMCR12',0,4,242,4,3
	.word	5629
	.byte	24
	.byte	'Ifx_P_OMCR4',0,4,250,4,3
	.word	5297
	.byte	24
	.byte	'Ifx_P_OMCR8',0,4,130,5,3
	.word	5473
	.byte	24
	.byte	'Ifx_P_OMR',0,4,138,5,3
	.word	1347
	.byte	24
	.byte	'Ifx_P_OMSR',0,4,146,5,3
	.word	5962
	.byte	24
	.byte	'Ifx_P_OMSR0',0,4,154,5,3
	.word	4433
	.byte	24
	.byte	'Ifx_P_OMSR12',0,4,162,5,3
	.word	4949
	.byte	24
	.byte	'Ifx_P_OMSR4',0,4,170,5,3
	.word	4602
	.byte	24
	.byte	'Ifx_P_OMSR8',0,4,178,5,3
	.word	4774
	.byte	24
	.byte	'Ifx_P_OUT',0,4,186,5,3
	.word	776
	.byte	24
	.byte	'Ifx_P_PCSR',0,4,194,5,3
	.word	4286
	.byte	24
	.byte	'Ifx_P_PDISC',0,4,202,5,3
	.word	4000
	.byte	24
	.byte	'Ifx_P_PDR0',0,4,210,5,3
	.word	2969
	.byte	24
	.byte	'Ifx_P_PDR1',0,4,218,5,3
	.word	3285
	.byte	16
	.word	7645
	.byte	24
	.byte	'Ifx_P',0,4,139,6,3
	.word	14841
	.byte	17,14,250,10,9,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy16384',0,0,18
	.byte	'IfxScu_WDTCON1_IR_divBy256',0,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy64',0,2,0,24
	.byte	'IfxScu_WDTCON1_IR',0,14,255,10,3
	.word	14861
	.byte	10
	.byte	'_Ifx_SCU_ACCEN0_Bits',0,15,45,16,4,11
	.byte	'EN0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	461
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	461
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	461
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	461
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	461
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	461
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	461
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	461
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	461
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	461
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	461
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	461
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	461
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	461
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	461
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	461
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	461
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	461
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	461
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	461
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	461
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_ACCEN0_Bits',0,15,79,3
	.word	14983
	.byte	10
	.byte	'_Ifx_SCU_ACCEN1_Bits',0,15,82,16,4,11
	.byte	'reserved_0',0,4
	.word	438
	.byte	32,0,2,35,0,0,24
	.byte	'Ifx_SCU_ACCEN1_Bits',0,15,85,3
	.word	15540
	.byte	10
	.byte	'_Ifx_SCU_ARSTDIS_Bits',0,15,88,16,4,11
	.byte	'STM0DIS',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'STM1DIS',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'STM2DIS',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	438
	.byte	29,0,2,35,0,0,24
	.byte	'Ifx_SCU_ARSTDIS_Bits',0,15,94,3
	.word	15617
	.byte	10
	.byte	'_Ifx_SCU_CCUCON0_Bits',0,15,97,16,4,11
	.byte	'BAUD1DIV',0,1
	.word	461
	.byte	4,4,2,35,0,11
	.byte	'BAUD2DIV',0,1
	.word	461
	.byte	4,0,2,35,0,11
	.byte	'SRIDIV',0,1
	.word	461
	.byte	4,4,2,35,1,11
	.byte	'LPDIV',0,1
	.word	461
	.byte	4,0,2,35,1,11
	.byte	'SPBDIV',0,1
	.word	461
	.byte	4,4,2,35,2,11
	.byte	'FSI2DIV',0,1
	.word	461
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	461
	.byte	2,0,2,35,2,11
	.byte	'FSIDIV',0,1
	.word	461
	.byte	2,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	461
	.byte	2,4,2,35,3,11
	.byte	'CLKSEL',0,1
	.word	461
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_CCUCON0_Bits',0,15,111,3
	.word	15753
	.byte	10
	.byte	'_Ifx_SCU_CCUCON1_Bits',0,15,114,16,4,11
	.byte	'CANDIV',0,1
	.word	461
	.byte	4,4,2,35,0,11
	.byte	'ERAYDIV',0,1
	.word	461
	.byte	4,0,2,35,0,11
	.byte	'STMDIV',0,1
	.word	461
	.byte	4,4,2,35,1,11
	.byte	'GTMDIV',0,1
	.word	461
	.byte	4,0,2,35,1,11
	.byte	'ETHDIV',0,1
	.word	461
	.byte	4,4,2,35,2,11
	.byte	'ASCLINFDIV',0,1
	.word	461
	.byte	4,0,2,35,2,11
	.byte	'ASCLINSDIV',0,1
	.word	461
	.byte	4,4,2,35,3,11
	.byte	'INSEL',0,1
	.word	461
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_CCUCON1_Bits',0,15,126,3
	.word	16033
	.byte	10
	.byte	'_Ifx_SCU_CCUCON2_Bits',0,15,129,1,16,4,11
	.byte	'BBBDIV',0,1
	.word	461
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	438
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_CCUCON2_Bits',0,15,135,1,3
	.word	16271
	.byte	10
	.byte	'_Ifx_SCU_CCUCON3_Bits',0,15,138,1,16,4,11
	.byte	'PLLDIV',0,1
	.word	461
	.byte	6,2,2,35,0,11
	.byte	'PLLSEL',0,1
	.word	461
	.byte	2,0,2,35,0,11
	.byte	'PLLERAYDIV',0,1
	.word	461
	.byte	6,2,2,35,1,11
	.byte	'PLLERAYSEL',0,1
	.word	461
	.byte	2,0,2,35,1,11
	.byte	'SRIDIV',0,1
	.word	461
	.byte	6,2,2,35,2,11
	.byte	'SRISEL',0,1
	.word	461
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	461
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_CCUCON3_Bits',0,15,150,1,3
	.word	16399
	.byte	10
	.byte	'_Ifx_SCU_CCUCON4_Bits',0,15,153,1,16,4,11
	.byte	'SPBDIV',0,1
	.word	461
	.byte	6,2,2,35,0,11
	.byte	'SPBSEL',0,1
	.word	461
	.byte	2,0,2,35,0,11
	.byte	'GTMDIV',0,1
	.word	461
	.byte	6,2,2,35,1,11
	.byte	'GTMSEL',0,1
	.word	461
	.byte	2,0,2,35,1,11
	.byte	'STMDIV',0,1
	.word	461
	.byte	6,2,2,35,2,11
	.byte	'STMSEL',0,1
	.word	461
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	461
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_CCUCON4_Bits',0,15,165,1,3
	.word	16642
	.byte	10
	.byte	'_Ifx_SCU_CCUCON5_Bits',0,15,168,1,16,4,11
	.byte	'MAXDIV',0,1
	.word	461
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	438
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_CCUCON5_Bits',0,15,174,1,3
	.word	16877
	.byte	10
	.byte	'_Ifx_SCU_CCUCON6_Bits',0,15,177,1,16,4,11
	.byte	'CPU0DIV',0,1
	.word	461
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	438
	.byte	26,0,2,35,0,0,24
	.byte	'Ifx_SCU_CCUCON6_Bits',0,15,181,1,3
	.word	17005
	.byte	10
	.byte	'_Ifx_SCU_CCUCON7_Bits',0,15,184,1,16,4,11
	.byte	'CPU1DIV',0,1
	.word	461
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	438
	.byte	26,0,2,35,0,0,24
	.byte	'Ifx_SCU_CCUCON7_Bits',0,15,188,1,3
	.word	17105
	.byte	10
	.byte	'_Ifx_SCU_CHIPID_Bits',0,15,191,1,16,4,11
	.byte	'CHREV',0,1
	.word	461
	.byte	6,2,2,35,0,11
	.byte	'CHTEC',0,1
	.word	461
	.byte	2,0,2,35,0,11
	.byte	'CHID',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'EEA',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'UCODE',0,1
	.word	461
	.byte	7,0,2,35,2,11
	.byte	'FSIZE',0,1
	.word	461
	.byte	4,4,2,35,3,11
	.byte	'SP',0,1
	.word	461
	.byte	2,2,2,35,3,11
	.byte	'SEC',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	461
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_CHIPID_Bits',0,15,202,1,3
	.word	17205
	.byte	10
	.byte	'_Ifx_SCU_DTSCON_Bits',0,15,205,1,16,4,11
	.byte	'PWD',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'START',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	461
	.byte	2,4,2,35,0,11
	.byte	'CAL',0,4
	.word	438
	.byte	20,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	7,1,2,35,3,11
	.byte	'SLCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_DTSCON_Bits',0,15,213,1,3
	.word	17413
	.byte	10
	.byte	'_Ifx_SCU_DTSLIM_Bits',0,15,216,1,16,4,11
	.byte	'LOWER',0,2
	.word	478
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	461
	.byte	5,1,2,35,1,11
	.byte	'LLU',0,1
	.word	461
	.byte	1,0,2,35,1,11
	.byte	'UPPER',0,2
	.word	478
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	461
	.byte	4,2,2,35,3,11
	.byte	'SLCK',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'UOF',0,1
	.word	461
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_DTSLIM_Bits',0,15,225,1,3
	.word	17578
	.byte	10
	.byte	'_Ifx_SCU_DTSSTAT_Bits',0,15,228,1,16,4,11
	.byte	'RESULT',0,2
	.word	478
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	461
	.byte	4,2,2,35,1,11
	.byte	'RDY',0,1
	.word	461
	.byte	1,1,2,35,1,11
	.byte	'BUSY',0,1
	.word	461
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	478
	.byte	16,0,2,35,2,0,24
	.byte	'Ifx_SCU_DTSSTAT_Bits',0,15,235,1,3
	.word	17761
	.byte	10
	.byte	'_Ifx_SCU_EICR_Bits',0,15,238,1,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	4,4,2,35,0,11
	.byte	'EXIS0',0,1
	.word	461
	.byte	3,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'FEN0',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'REN0',0,1
	.word	461
	.byte	1,6,2,35,1,11
	.byte	'LDEN0',0,1
	.word	461
	.byte	1,5,2,35,1,11
	.byte	'EIEN0',0,1
	.word	461
	.byte	1,4,2,35,1,11
	.byte	'INP0',0,1
	.word	461
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	438
	.byte	5,12,2,35,0,11
	.byte	'EXIS1',0,1
	.word	461
	.byte	3,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	461
	.byte	1,0,2,35,2,11
	.byte	'FEN1',0,1
	.word	461
	.byte	1,7,2,35,3,11
	.byte	'REN1',0,1
	.word	461
	.byte	1,6,2,35,3,11
	.byte	'LDEN1',0,1
	.word	461
	.byte	1,5,2,35,3,11
	.byte	'EIEN1',0,1
	.word	461
	.byte	1,4,2,35,3,11
	.byte	'INP1',0,1
	.word	461
	.byte	3,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	461
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_EICR_Bits',0,15,129,2,3
	.word	17915
	.byte	10
	.byte	'_Ifx_SCU_EIFR_Bits',0,15,132,2,16,4,11
	.byte	'INTF0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'INTF1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'INTF2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'INTF3',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'INTF4',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'INTF5',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'INTF6',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'INTF7',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	438
	.byte	24,0,2,35,0,0,24
	.byte	'Ifx_SCU_EIFR_Bits',0,15,143,2,3
	.word	18279
	.byte	10
	.byte	'_Ifx_SCU_EMSR_Bits',0,15,146,2,16,4,11
	.byte	'POL',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'MODE',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'ENON',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'PSEL',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	478
	.byte	12,0,2,35,0,11
	.byte	'EMSF',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'SEMSF',0,1
	.word	461
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	461
	.byte	6,0,2,35,2,11
	.byte	'EMSFM',0,1
	.word	461
	.byte	2,6,2,35,3,11
	.byte	'SEMSFM',0,1
	.word	461
	.byte	2,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	461
	.byte	4,0,2,35,3,0,24
	.byte	'Ifx_SCU_EMSR_Bits',0,15,159,2,3
	.word	18490
	.byte	10
	.byte	'_Ifx_SCU_ESRCFG_Bits',0,15,162,2,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	7,1,2,35,0,11
	.byte	'EDCON',0,2
	.word	478
	.byte	2,7,2,35,0,11
	.byte	'reserved_9',0,4
	.word	438
	.byte	23,0,2,35,0,0,24
	.byte	'Ifx_SCU_ESRCFG_Bits',0,15,167,2,3
	.word	18742
	.byte	10
	.byte	'_Ifx_SCU_ESROCFG_Bits',0,15,170,2,16,4,11
	.byte	'ARI',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'ARC',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	438
	.byte	30,0,2,35,0,0,24
	.byte	'Ifx_SCU_ESROCFG_Bits',0,15,175,2,3
	.word	18860
	.byte	10
	.byte	'_Ifx_SCU_EVR13CON_Bits',0,15,178,2,16,4,11
	.byte	'reserved_0',0,4
	.word	438
	.byte	28,4,2,35,0,11
	.byte	'EVR13OFF',0,1
	.word	461
	.byte	1,3,2,35,3,11
	.byte	'BPEVR13OFF',0,1
	.word	461
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_EVR13CON_Bits',0,15,185,2,3
	.word	18971
	.byte	10
	.byte	'_Ifx_SCU_EVR33CON_Bits',0,15,188,2,16,4,11
	.byte	'reserved_0',0,4
	.word	438
	.byte	28,4,2,35,0,11
	.byte	'EVR33OFF',0,1
	.word	461
	.byte	1,3,2,35,3,11
	.byte	'BPEVR33OFF',0,1
	.word	461
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_EVR33CON_Bits',0,15,195,2,3
	.word	19134
	.byte	10
	.byte	'_Ifx_SCU_EVRADCSTAT_Bits',0,15,198,2,16,4,11
	.byte	'ADC13V',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'ADC33V',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'ADCSWDV',0,1
	.word	461
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	461
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_EVRADCSTAT_Bits',0,15,205,2,3
	.word	19297
	.byte	10
	.byte	'_Ifx_SCU_EVRDVSTAT_Bits',0,15,208,2,16,4,11
	.byte	'DVS13TRIM',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'DVS33TRIM',0,1
	.word	461
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	461
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_EVRDVSTAT_Bits',0,15,215,2,3
	.word	19455
	.byte	10
	.byte	'_Ifx_SCU_EVRMONCTRL_Bits',0,15,218,2,16,4,11
	.byte	'EVR13OVMOD',0,1
	.word	461
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	461
	.byte	2,4,2,35,0,11
	.byte	'EVR13UVMOD',0,1
	.word	461
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	461
	.byte	2,0,2,35,0,11
	.byte	'EVR33OVMOD',0,1
	.word	461
	.byte	2,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	461
	.byte	2,4,2,35,1,11
	.byte	'EVR33UVMOD',0,1
	.word	461
	.byte	2,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	461
	.byte	2,0,2,35,1,11
	.byte	'SWDOVMOD',0,1
	.word	461
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	461
	.byte	2,4,2,35,2,11
	.byte	'SWDUVMOD',0,1
	.word	461
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,2
	.word	478
	.byte	10,0,2,35,2,0,24
	.byte	'Ifx_SCU_EVRMONCTRL_Bits',0,15,232,2,3
	.word	19620
	.byte	10
	.byte	'_Ifx_SCU_EVROSCCTRL_Bits',0,15,235,2,16,4,11
	.byte	'OSCTRIM',0,2
	.word	478
	.byte	10,6,2,35,0,11
	.byte	'OSCPTAT',0,1
	.word	461
	.byte	6,0,2,35,1,11
	.byte	'OSCANASEL',0,1
	.word	461
	.byte	4,4,2,35,2,11
	.byte	'HPBGTRIM',0,2
	.word	478
	.byte	7,5,2,35,2,11
	.byte	'HPBGCLKEN',0,1
	.word	461
	.byte	1,4,2,35,3,11
	.byte	'OSC3V3',0,1
	.word	461
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	461
	.byte	2,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_EVROSCCTRL_Bits',0,15,245,2,3
	.word	19949
	.byte	10
	.byte	'_Ifx_SCU_EVROVMON_Bits',0,15,248,2,16,4,11
	.byte	'EVR13OVVAL',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'EVR33OVVAL',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'SWDOVVAL',0,1
	.word	461
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_EVROVMON_Bits',0,15,255,2,3
	.word	20170
	.byte	10
	.byte	'_Ifx_SCU_EVRRSTCON_Bits',0,15,130,3,16,4,11
	.byte	'RST13TRIM',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	438
	.byte	16,8,2,35,0,11
	.byte	'RST13OFF',0,1
	.word	461
	.byte	1,7,2,35,3,11
	.byte	'BPRST13OFF',0,1
	.word	461
	.byte	1,6,2,35,3,11
	.byte	'RST33OFF',0,1
	.word	461
	.byte	1,5,2,35,3,11
	.byte	'BPRST33OFF',0,1
	.word	461
	.byte	1,4,2,35,3,11
	.byte	'RSTSWDOFF',0,1
	.word	461
	.byte	1,3,2,35,3,11
	.byte	'BPRSTSWDOFF',0,1
	.word	461
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_EVRRSTCON_Bits',0,15,142,3,3
	.word	20333
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF1_Bits',0,15,145,3,16,4,11
	.byte	'SD5P',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'SD5I',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'SD5D',0,1
	.word	461
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_EVRSDCOEFF1_Bits',0,15,152,3,3
	.word	20605
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF2_Bits',0,15,155,3,16,4,11
	.byte	'SD33P',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'SD33I',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'SD33D',0,1
	.word	461
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_EVRSDCOEFF2_Bits',0,15,162,3,3
	.word	20758
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF3_Bits',0,15,165,3,16,4,11
	.byte	'CT5REG0',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'CT5REG1',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'CT5REG2',0,1
	.word	461
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_EVRSDCOEFF3_Bits',0,15,172,3,3
	.word	20914
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF4_Bits',0,15,175,3,16,4,11
	.byte	'CT5REG3',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'CT5REG4',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	478
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_EVRSDCOEFF4_Bits',0,15,181,3,3
	.word	21076
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF5_Bits',0,15,184,3,16,4,11
	.byte	'CT33REG0',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'CT33REG1',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'CT33REG2',0,1
	.word	461
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_EVRSDCOEFF5_Bits',0,15,191,3,3
	.word	21219
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF6_Bits',0,15,194,3,16,4,11
	.byte	'CT33REG3',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'CT33REG4',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	478
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_EVRSDCOEFF6_Bits',0,15,200,3,3
	.word	21384
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL1_Bits',0,15,203,3,16,4,11
	.byte	'SDFREQSPRD',0,2
	.word	478
	.byte	16,0,2,35,0,11
	.byte	'SDFREQ',0,1
	.word	461
	.byte	8,0,2,35,2,11
	.byte	'SDSTEP',0,1
	.word	461
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	461
	.byte	2,2,2,35,3,11
	.byte	'SDSAMPLE',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_EVRSDCTRL1_Bits',0,15,211,3,3
	.word	21529
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL2_Bits',0,15,214,3,16,4,11
	.byte	'DRVP',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'SDMINMAXDC',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'DRVN',0,1
	.word	461
	.byte	8,0,2,35,2,11
	.byte	'SDLUT',0,1
	.word	461
	.byte	6,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_EVRSDCTRL2_Bits',0,15,222,3,3
	.word	21710
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL3_Bits',0,15,225,3,16,4,11
	.byte	'SDPWMPRE',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'SDPID',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'SDVOKLVL',0,1
	.word	461
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_EVRSDCTRL3_Bits',0,15,232,3,3
	.word	21884
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL4_Bits',0,15,235,3,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'SYNCDIV',0,1
	.word	461
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	438
	.byte	20,1,2,35,0,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_EVRSDCTRL4_Bits',0,15,241,3,3
	.word	22044
	.byte	10
	.byte	'_Ifx_SCU_EVRSTAT_Bits',0,15,244,3,16,4,11
	.byte	'EVR13',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'OV13',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'EVR33',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'OV33',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'OVSWD',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'UV13',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'UV33',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'UVSWD',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'EXTPASS13',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'EXTPASS33',0,1
	.word	461
	.byte	1,6,2,35,1,11
	.byte	'BGPROK',0,1
	.word	461
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	438
	.byte	21,0,2,35,0,0,24
	.byte	'Ifx_SCU_EVRSTAT_Bits',0,15,130,4,3
	.word	22188
	.byte	10
	.byte	'_Ifx_SCU_EVRTRIM_Bits',0,15,133,4,16,4,11
	.byte	'EVR13TRIM',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'SDVOUTSEL',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	478
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_EVRTRIM_Bits',0,15,139,4,3
	.word	22462
	.byte	10
	.byte	'_Ifx_SCU_EVRUVMON_Bits',0,15,142,4,16,4,11
	.byte	'EVR13UVVAL',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'EVR33UVVAL',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'SWDUVVAL',0,1
	.word	461
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_EVRUVMON_Bits',0,15,149,4,3
	.word	22601
	.byte	10
	.byte	'_Ifx_SCU_EXTCON_Bits',0,15,152,4,16,4,11
	.byte	'EN0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'SEL0',0,1
	.word	461
	.byte	4,2,2,35,0,11
	.byte	'reserved_6',0,2
	.word	478
	.byte	10,0,2,35,0,11
	.byte	'EN1',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'NSEL',0,1
	.word	461
	.byte	1,6,2,35,2,11
	.byte	'SEL1',0,1
	.word	461
	.byte	4,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	461
	.byte	2,0,2,35,2,11
	.byte	'DIV1',0,1
	.word	461
	.byte	8,0,2,35,3,0,24
	.byte	'Ifx_SCU_EXTCON_Bits',0,15,163,4,3
	.word	22764
	.byte	10
	.byte	'_Ifx_SCU_FDR_Bits',0,15,166,4,16,4,11
	.byte	'STEP',0,2
	.word	478
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	461
	.byte	4,2,2,35,1,11
	.byte	'DM',0,1
	.word	461
	.byte	2,0,2,35,1,11
	.byte	'RESULT',0,2
	.word	478
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	461
	.byte	5,1,2,35,3,11
	.byte	'DISCLK',0,1
	.word	461
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_FDR_Bits',0,15,174,4,3
	.word	22982
	.byte	10
	.byte	'_Ifx_SCU_FMR_Bits',0,15,177,4,16,4,11
	.byte	'FS0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'FS1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'FS2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'FS3',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'FS4',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'FS5',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'FS6',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'FS7',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'FC0',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'FC1',0,1
	.word	461
	.byte	1,6,2,35,2,11
	.byte	'FC2',0,1
	.word	461
	.byte	1,5,2,35,2,11
	.byte	'FC3',0,1
	.word	461
	.byte	1,4,2,35,2,11
	.byte	'FC4',0,1
	.word	461
	.byte	1,3,2,35,2,11
	.byte	'FC5',0,1
	.word	461
	.byte	1,2,2,35,2,11
	.byte	'FC6',0,1
	.word	461
	.byte	1,1,2,35,2,11
	.byte	'FC7',0,1
	.word	461
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	8,0,2,35,3,0,24
	.byte	'Ifx_SCU_FMR_Bits',0,15,197,4,3
	.word	23145
	.byte	10
	.byte	'_Ifx_SCU_ID_Bits',0,15,200,4,16,4,11
	.byte	'MODREV',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	478
	.byte	16,0,2,35,2,0,24
	.byte	'Ifx_SCU_ID_Bits',0,15,205,4,3
	.word	23481
	.byte	10
	.byte	'_Ifx_SCU_IGCR_Bits',0,15,208,4,16,4,11
	.byte	'IPEN00',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'IPEN01',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'IPEN02',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'IPEN03',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'IPEN04',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'IPEN05',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'IPEN06',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'IPEN07',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	461
	.byte	5,3,2,35,1,11
	.byte	'GEEN0',0,1
	.word	461
	.byte	1,2,2,35,1,11
	.byte	'IGP0',0,1
	.word	461
	.byte	2,0,2,35,1,11
	.byte	'IPEN10',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'IPEN11',0,1
	.word	461
	.byte	1,6,2,35,2,11
	.byte	'IPEN12',0,1
	.word	461
	.byte	1,5,2,35,2,11
	.byte	'IPEN13',0,1
	.word	461
	.byte	1,4,2,35,2,11
	.byte	'IPEN14',0,1
	.word	461
	.byte	1,3,2,35,2,11
	.byte	'IPEN15',0,1
	.word	461
	.byte	1,2,2,35,2,11
	.byte	'IPEN16',0,1
	.word	461
	.byte	1,1,2,35,2,11
	.byte	'IPEN17',0,1
	.word	461
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	5,3,2,35,3,11
	.byte	'GEEN1',0,1
	.word	461
	.byte	1,2,2,35,3,11
	.byte	'IGP1',0,1
	.word	461
	.byte	2,0,2,35,3,0,24
	.byte	'Ifx_SCU_IGCR_Bits',0,15,232,4,3
	.word	23588
	.byte	10
	.byte	'_Ifx_SCU_IN_Bits',0,15,235,4,16,4,11
	.byte	'P0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	438
	.byte	30,0,2,35,0,0,24
	.byte	'Ifx_SCU_IN_Bits',0,15,240,4,3
	.word	24040
	.byte	10
	.byte	'_Ifx_SCU_IOCR_Bits',0,15,243,4,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	4,4,2,35,0,11
	.byte	'PC0',0,1
	.word	461
	.byte	4,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	461
	.byte	4,4,2,35,1,11
	.byte	'PC1',0,1
	.word	461
	.byte	4,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	478
	.byte	16,0,2,35,2,0,24
	.byte	'Ifx_SCU_IOCR_Bits',0,15,250,4,3
	.word	24139
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL0_Bits',0,15,253,4,16,4,11
	.byte	'LBISTREQ',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'LBISTREQP',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'PATTERNS',0,2
	.word	478
	.byte	14,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	478
	.byte	16,0,2,35,2,0,24
	.byte	'Ifx_SCU_LBISTCTRL0_Bits',0,15,131,5,3
	.word	24289
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL1_Bits',0,15,134,5,16,4,11
	.byte	'SEED',0,4
	.word	438
	.byte	23,9,2,35,0,11
	.byte	'reserved_23',0,1
	.word	461
	.byte	1,0,2,35,2,11
	.byte	'SPLITSH',0,1
	.word	461
	.byte	3,5,2,35,3,11
	.byte	'BODY',0,1
	.word	461
	.byte	1,4,2,35,3,11
	.byte	'LBISTFREQU',0,1
	.word	461
	.byte	4,0,2,35,3,0,24
	.byte	'Ifx_SCU_LBISTCTRL1_Bits',0,15,141,5,3
	.word	24438
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL2_Bits',0,15,144,5,16,4,11
	.byte	'SIGNATURE',0,4
	.word	438
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	7,1,2,35,3,11
	.byte	'LBISTDONE',0,1
	.word	461
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_LBISTCTRL2_Bits',0,15,149,5,3
	.word	24599
	.byte	10
	.byte	'_Ifx_SCU_LCLCON_Bits',0,15,152,5,16,4,11
	.byte	'reserved_0',0,2
	.word	478
	.byte	16,0,2,35,0,11
	.byte	'LS',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,2
	.word	478
	.byte	14,1,2,35,2,11
	.byte	'LSEN',0,1
	.word	461
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_LCLCON_Bits',0,15,158,5,3
	.word	24729
	.byte	10
	.byte	'_Ifx_SCU_LCLTEST_Bits',0,15,161,5,16,4,11
	.byte	'LCLT0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'LCLT1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	438
	.byte	30,0,2,35,0,0,24
	.byte	'Ifx_SCU_LCLTEST_Bits',0,15,166,5,3
	.word	24861
	.byte	10
	.byte	'_Ifx_SCU_MANID_Bits',0,15,169,5,16,4,11
	.byte	'DEPT',0,1
	.word	461
	.byte	5,3,2,35,0,11
	.byte	'MANUF',0,2
	.word	478
	.byte	11,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	478
	.byte	16,0,2,35,2,0,24
	.byte	'Ifx_SCU_MANID_Bits',0,15,174,5,3
	.word	24976
	.byte	10
	.byte	'_Ifx_SCU_OMR_Bits',0,15,177,5,16,4,11
	.byte	'PS0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	478
	.byte	14,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	461
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	478
	.byte	14,0,2,35,2,0,24
	.byte	'Ifx_SCU_OMR_Bits',0,15,185,5,3
	.word	25087
	.byte	10
	.byte	'_Ifx_SCU_OSCCON_Bits',0,15,188,5,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'PLLLV',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'OSCRES',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'GAINSEL',0,1
	.word	461
	.byte	2,3,2,35,0,11
	.byte	'MODE',0,1
	.word	461
	.byte	2,1,2,35,0,11
	.byte	'SHBY',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'PLLHV',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	461
	.byte	1,6,2,35,1,11
	.byte	'X1D',0,1
	.word	461
	.byte	1,5,2,35,1,11
	.byte	'X1DEN',0,1
	.word	461
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	461
	.byte	4,0,2,35,1,11
	.byte	'OSCVAL',0,1
	.word	461
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	461
	.byte	2,1,2,35,2,11
	.byte	'APREN',0,1
	.word	461
	.byte	1,0,2,35,2,11
	.byte	'CAP0EN',0,1
	.word	461
	.byte	1,7,2,35,3,11
	.byte	'CAP1EN',0,1
	.word	461
	.byte	1,6,2,35,3,11
	.byte	'CAP2EN',0,1
	.word	461
	.byte	1,5,2,35,3,11
	.byte	'CAP3EN',0,1
	.word	461
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	461
	.byte	4,0,2,35,3,0,24
	.byte	'Ifx_SCU_OSCCON_Bits',0,15,209,5,3
	.word	25245
	.byte	10
	.byte	'_Ifx_SCU_OUT_Bits',0,15,212,5,16,4,11
	.byte	'P0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	438
	.byte	30,0,2,35,0,0,24
	.byte	'Ifx_SCU_OUT_Bits',0,15,217,5,3
	.word	25657
	.byte	10
	.byte	'_Ifx_SCU_OVCCON_Bits',0,15,220,5,16,4,11
	.byte	'CSEL0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'CSEL1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'CSEL2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,2
	.word	478
	.byte	13,0,2,35,0,11
	.byte	'OVSTRT',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'OVSTP',0,1
	.word	461
	.byte	1,6,2,35,2,11
	.byte	'DCINVAL',0,1
	.word	461
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	461
	.byte	5,0,2,35,2,11
	.byte	'OVCONF',0,1
	.word	461
	.byte	1,7,2,35,3,11
	.byte	'POVCONF',0,1
	.word	461
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	461
	.byte	6,0,2,35,3,0,24
	.byte	'Ifx_SCU_OVCCON_Bits',0,15,233,5,3
	.word	25758
	.byte	10
	.byte	'_Ifx_SCU_OVCENABLE_Bits',0,15,236,5,16,4,11
	.byte	'OVEN0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'OVEN1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'OVEN2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	438
	.byte	29,0,2,35,0,0,24
	.byte	'Ifx_SCU_OVCENABLE_Bits',0,15,242,5,3
	.word	26025
	.byte	10
	.byte	'_Ifx_SCU_PDISC_Bits',0,15,245,5,16,4,11
	.byte	'PDIS0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	438
	.byte	30,0,2,35,0,0,24
	.byte	'Ifx_SCU_PDISC_Bits',0,15,250,5,3
	.word	26161
	.byte	10
	.byte	'_Ifx_SCU_PDR_Bits',0,15,253,5,16,4,11
	.byte	'PD0',0,1
	.word	461
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	461
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	438
	.byte	24,0,2,35,0,0,24
	.byte	'Ifx_SCU_PDR_Bits',0,15,132,6,3
	.word	26272
	.byte	10
	.byte	'_Ifx_SCU_PDRR_Bits',0,15,135,6,16,4,11
	.byte	'PDR0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'PDR1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'PDR2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'PDR3',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'PDR4',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'PDR5',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'PDR6',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'PDR7',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	438
	.byte	24,0,2,35,0,0,24
	.byte	'Ifx_SCU_PDRR_Bits',0,15,146,6,3
	.word	26405
	.byte	10
	.byte	'_Ifx_SCU_PLLCON0_Bits',0,15,149,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'MODEN',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	478
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	461
	.byte	7,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	461
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	461
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	461
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	461
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	461
	.byte	4,0,2,35,3,0,24
	.byte	'Ifx_SCU_PLLCON0_Bits',0,15,166,6,3
	.word	26608
	.byte	10
	.byte	'_Ifx_SCU_PLLCON1_Bits',0,15,169,6,16,4,11
	.byte	'K2DIV',0,1
	.word	461
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	461
	.byte	7,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	461
	.byte	1,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	461
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	478
	.byte	9,0,2,35,2,0,24
	.byte	'Ifx_SCU_PLLCON1_Bits',0,15,177,6,3
	.word	26964
	.byte	10
	.byte	'_Ifx_SCU_PLLCON2_Bits',0,15,180,6,16,4,11
	.byte	'MODCFG',0,2
	.word	478
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	478
	.byte	16,0,2,35,2,0,24
	.byte	'Ifx_SCU_PLLCON2_Bits',0,15,184,6,3
	.word	27142
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON0_Bits',0,15,187,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	461
	.byte	2,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	478
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	461
	.byte	5,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	461
	.byte	2,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	461
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	461
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	461
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	461
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	461
	.byte	4,0,2,35,3,0,24
	.byte	'Ifx_SCU_PLLERAYCON0_Bits',0,15,204,6,3
	.word	27242
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON1_Bits',0,15,207,6,16,4,11
	.byte	'K2DIV',0,1
	.word	461
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	461
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	461
	.byte	4,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	461
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	478
	.byte	9,0,2,35,2,0,24
	.byte	'Ifx_SCU_PLLERAYCON1_Bits',0,15,215,6,3
	.word	27612
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYSTAT_Bits',0,15,218,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'PWDSTAT',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	438
	.byte	26,0,2,35,0,0,24
	.byte	'Ifx_SCU_PLLERAYSTAT_Bits',0,15,227,6,3
	.word	27798
	.byte	10
	.byte	'_Ifx_SCU_PLLSTAT_Bits',0,15,230,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'MODRUN',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	438
	.byte	24,0,2,35,0,0,24
	.byte	'Ifx_SCU_PLLSTAT_Bits',0,15,241,6,3
	.word	27996
	.byte	10
	.byte	'_Ifx_SCU_PMCSR_Bits',0,15,244,6,16,4,11
	.byte	'REQSLP',0,1
	.word	461
	.byte	2,6,2,35,0,11
	.byte	'SMUSLP',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	461
	.byte	5,0,2,35,0,11
	.byte	'PMST',0,1
	.word	461
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	438
	.byte	21,0,2,35,0,0,24
	.byte	'Ifx_SCU_PMCSR_Bits',0,15,251,6,3
	.word	28229
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR0_Bits',0,15,254,6,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'ESR1WKEN',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'PINAWKEN',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'PINBWKEN',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'ESR0DFEN',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'ESR0EDCON',0,1
	.word	461
	.byte	2,1,2,35,0,11
	.byte	'ESR1DFEN',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'ESR1EDCON',0,1
	.word	461
	.byte	2,6,2,35,1,11
	.byte	'PINADFEN',0,1
	.word	461
	.byte	1,5,2,35,1,11
	.byte	'PINAEDCON',0,1
	.word	461
	.byte	2,3,2,35,1,11
	.byte	'PINBDFEN',0,1
	.word	461
	.byte	1,2,2,35,1,11
	.byte	'PINBEDCON',0,1
	.word	461
	.byte	2,0,2,35,1,11
	.byte	'SCREN',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'STBYRAMSEL',0,1
	.word	461
	.byte	2,5,2,35,2,11
	.byte	'SCRCLKSEL',0,1
	.word	461
	.byte	1,4,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	461
	.byte	1,3,2,35,2,11
	.byte	'TRISTEN',0,1
	.word	461
	.byte	1,2,2,35,2,11
	.byte	'TRISTREQ',0,1
	.word	461
	.byte	1,1,2,35,2,11
	.byte	'PORSTDF',0,1
	.word	461
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	1,7,2,35,3,11
	.byte	'DCDCSYNC',0,1
	.word	461
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	461
	.byte	3,3,2,35,3,11
	.byte	'ESR0TRIST',0,1
	.word	461
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_PMSWCR0_Bits',0,15,153,7,3
	.word	28381
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR1_Bits',0,15,156,7,16,4,11
	.byte	'SCRSTEN',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'SCRSTREQ',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	461
	.byte	6,0,2,35,0,11
	.byte	'CPUIDLSEL',0,1
	.word	461
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	461
	.byte	1,4,2,35,1,11
	.byte	'IRADIS',0,1
	.word	461
	.byte	1,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	461
	.byte	3,0,2,35,1,11
	.byte	'SCRCFG',0,1
	.word	461
	.byte	8,0,2,35,2,11
	.byte	'CPUSEL',0,1
	.word	461
	.byte	3,5,2,35,3,11
	.byte	'STBYEVEN',0,1
	.word	461
	.byte	1,4,2,35,3,11
	.byte	'STBYEV',0,1
	.word	461
	.byte	3,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_PMSWCR1_Bits',0,15,170,7,3
	.word	28948
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR2_Bits',0,15,173,7,16,4,11
	.byte	'SCRINT',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'BUSY',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'SCRECC',0,1
	.word	461
	.byte	1,6,2,35,1,11
	.byte	'SCRWDT',0,1
	.word	461
	.byte	1,5,2,35,1,11
	.byte	'SCRRST',0,1
	.word	461
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	461
	.byte	4,0,2,35,1,11
	.byte	'TCINT',0,1
	.word	461
	.byte	8,0,2,35,2,11
	.byte	'TCINTREQ',0,1
	.word	461
	.byte	1,7,2,35,3,11
	.byte	'SMURST',0,1
	.word	461
	.byte	1,6,2,35,3,11
	.byte	'RST',0,1
	.word	461
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	461
	.byte	4,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_PMSWCR2_Bits',0,15,187,7,3
	.word	29242
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTAT_Bits',0,15,190,7,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKP',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUN',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'PINAWKP',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUN',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'PINBWKP',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUN',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'PORSTDF',0,1
	.word	461
	.byte	1,6,2,35,1,11
	.byte	'HWCFGEVR',0,1
	.word	461
	.byte	3,3,2,35,1,11
	.byte	'STBYRAM',0,1
	.word	461
	.byte	2,1,2,35,1,11
	.byte	'TRIST',0,1
	.word	461
	.byte	1,0,2,35,1,11
	.byte	'SCRST',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'SCRWKP',0,1
	.word	461
	.byte	1,6,2,35,2,11
	.byte	'SCR',0,1
	.word	461
	.byte	1,5,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	461
	.byte	1,4,2,35,2,11
	.byte	'ESR1WKEN',0,1
	.word	461
	.byte	1,3,2,35,2,11
	.byte	'PINAWKEN',0,1
	.word	461
	.byte	1,2,2,35,2,11
	.byte	'PINBWKEN',0,1
	.word	461
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	478
	.byte	4,5,2,35,2,11
	.byte	'ESR0TRIST',0,1
	.word	461
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	461
	.byte	4,0,2,35,3,0,24
	.byte	'Ifx_SCU_PMSWSTAT_Bits',0,15,214,7,3
	.word	29520
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTATCLR_Bits',0,15,217,7,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKPCLR',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUNCLR',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'PINAWKPCLR',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUNCLR',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'PINBWKPCLR',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUNCLR',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'SCRSTCLR',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'SCRWKPCLR',0,1
	.word	461
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	478
	.byte	14,0,2,35,2,0,24
	.byte	'Ifx_SCU_PMSWSTATCLR_Bits',0,15,230,7,3
	.word	30016
	.byte	10
	.byte	'_Ifx_SCU_RSTCON2_Bits',0,15,233,7,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'CLRC',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	478
	.byte	10,4,2,35,0,11
	.byte	'CSS0',0,1
	.word	461
	.byte	1,3,2,35,1,11
	.byte	'CSS1',0,1
	.word	461
	.byte	1,2,2,35,1,11
	.byte	'CSS2',0,1
	.word	461
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	461
	.byte	1,0,2,35,1,11
	.byte	'USRINFO',0,2
	.word	478
	.byte	16,0,2,35,2,0,24
	.byte	'Ifx_SCU_RSTCON2_Bits',0,15,243,7,3
	.word	30329
	.byte	10
	.byte	'_Ifx_SCU_RSTCON_Bits',0,15,246,7,16,4,11
	.byte	'ESR0',0,1
	.word	461
	.byte	2,6,2,35,0,11
	.byte	'ESR1',0,1
	.word	461
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	461
	.byte	2,2,2,35,0,11
	.byte	'SMU',0,1
	.word	461
	.byte	2,0,2,35,0,11
	.byte	'SW',0,1
	.word	461
	.byte	2,6,2,35,1,11
	.byte	'STM0',0,1
	.word	461
	.byte	2,4,2,35,1,11
	.byte	'STM1',0,1
	.word	461
	.byte	2,2,2,35,1,11
	.byte	'STM2',0,1
	.word	461
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	478
	.byte	16,0,2,35,2,0,24
	.byte	'Ifx_SCU_RSTCON_Bits',0,15,129,8,3
	.word	30538
	.byte	10
	.byte	'_Ifx_SCU_RSTSTAT_Bits',0,15,132,8,16,4,11
	.byte	'ESR0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'ESR1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'SMU',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'SW',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'STM0',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'STM1',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'STM2',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'PORST',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	461
	.byte	1,6,2,35,2,11
	.byte	'CB0',0,1
	.word	461
	.byte	1,5,2,35,2,11
	.byte	'CB1',0,1
	.word	461
	.byte	1,4,2,35,2,11
	.byte	'CB3',0,1
	.word	461
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	461
	.byte	2,1,2,35,2,11
	.byte	'EVR13',0,1
	.word	461
	.byte	1,0,2,35,2,11
	.byte	'EVR33',0,1
	.word	461
	.byte	1,7,2,35,3,11
	.byte	'SWD',0,1
	.word	461
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	461
	.byte	2,4,2,35,3,11
	.byte	'STBYR',0,1
	.word	461
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	461
	.byte	3,0,2,35,3,0,24
	.byte	'Ifx_SCU_RSTSTAT_Bits',0,15,155,8,3
	.word	30749
	.byte	10
	.byte	'_Ifx_SCU_SAFECON_Bits',0,15,158,8,16,4,11
	.byte	'HBT',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	438
	.byte	31,0,2,35,0,0,24
	.byte	'Ifx_SCU_SAFECON_Bits',0,15,162,8,3
	.word	31181
	.byte	10
	.byte	'_Ifx_SCU_STSTAT_Bits',0,15,165,8,16,4,11
	.byte	'HWCFG',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'FTM',0,1
	.word	461
	.byte	7,1,2,35,1,11
	.byte	'MODE',0,1
	.word	461
	.byte	1,0,2,35,1,11
	.byte	'FCBAE',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'LUDIS',0,1
	.word	461
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	461
	.byte	1,5,2,35,2,11
	.byte	'TRSTL',0,1
	.word	461
	.byte	1,4,2,35,2,11
	.byte	'SPDEN',0,1
	.word	461
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	461
	.byte	3,0,2,35,2,11
	.byte	'RAMINT',0,1
	.word	461
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	461
	.byte	7,0,2,35,3,0,24
	.byte	'Ifx_SCU_STSTAT_Bits',0,15,178,8,3
	.word	31277
	.byte	10
	.byte	'_Ifx_SCU_SWRSTCON_Bits',0,15,181,8,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'SWRSTREQ',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	438
	.byte	30,0,2,35,0,0,24
	.byte	'Ifx_SCU_SWRSTCON_Bits',0,15,186,8,3
	.word	31537
	.byte	10
	.byte	'_Ifx_SCU_SYSCON_Bits',0,15,189,8,16,4,11
	.byte	'CCTRIG0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'RAMINTM',0,1
	.word	461
	.byte	2,4,2,35,0,11
	.byte	'SETLUDIS',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	461
	.byte	3,0,2,35,0,11
	.byte	'DATM',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,4
	.word	438
	.byte	23,0,2,35,0,0,24
	.byte	'Ifx_SCU_SYSCON_Bits',0,15,198,8,3
	.word	31662
	.byte	10
	.byte	'_Ifx_SCU_TRAPCLR_Bits',0,15,201,8,16,4,11
	.byte	'ESR0T',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	438
	.byte	28,0,2,35,0,0,24
	.byte	'Ifx_SCU_TRAPCLR_Bits',0,15,208,8,3
	.word	31859
	.byte	10
	.byte	'_Ifx_SCU_TRAPDIS_Bits',0,15,211,8,16,4,11
	.byte	'ESR0T',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	438
	.byte	28,0,2,35,0,0,24
	.byte	'Ifx_SCU_TRAPDIS_Bits',0,15,218,8,3
	.word	32012
	.byte	10
	.byte	'_Ifx_SCU_TRAPSET_Bits',0,15,221,8,16,4,11
	.byte	'ESR0T',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	438
	.byte	28,0,2,35,0,0,24
	.byte	'Ifx_SCU_TRAPSET_Bits',0,15,228,8,3
	.word	32165
	.byte	10
	.byte	'_Ifx_SCU_TRAPSTAT_Bits',0,15,231,8,16,4,11
	.byte	'ESR0T',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	438
	.byte	28,0,2,35,0,0,24
	.byte	'Ifx_SCU_TRAPSTAT_Bits',0,15,238,8,3
	.word	32318
	.byte	7
	.byte	'unsigned int',0,4,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON0_Bits',0,15,241,8,16,4,11
	.byte	'ENDINIT',0,4
	.word	32473
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	32473
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	32473
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	32473
	.byte	16,0,2,35,0,0,24
	.byte	'Ifx_SCU_WDTCPU_CON0_Bits',0,15,247,8,3
	.word	32489
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU_CON1_Bits',0,15,250,8,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	2,6,2,35,0,11
	.byte	'IR0',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	461
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	478
	.byte	16,0,2,35,2,0,24
	.byte	'Ifx_SCU_WDTCPU_CON1_Bits',0,15,134,9,3
	.word	32619
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU_SR_Bits',0,15,137,9,16,4,11
	.byte	'AE',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	461
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	478
	.byte	16,0,2,35,2,0,24
	.byte	'Ifx_SCU_WDTCPU_SR_Bits',0,15,150,9,3
	.word	32857
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON0_Bits',0,15,153,9,16,4,11
	.byte	'ENDINIT',0,4
	.word	32473
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	32473
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	32473
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	32473
	.byte	16,0,2,35,0,0,24
	.byte	'Ifx_SCU_WDTS_CON0_Bits',0,15,159,9,3
	.word	33080
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON1_Bits',0,15,162,9,16,4,11
	.byte	'CLRIRF',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'IR0',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	461
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	478
	.byte	16,0,2,35,2,0,24
	.byte	'Ifx_SCU_WDTS_CON1_Bits',0,15,175,9,3
	.word	33206
	.byte	10
	.byte	'_Ifx_SCU_WDTS_SR_Bits',0,15,178,9,16,4,11
	.byte	'AE',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	461
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	478
	.byte	16,0,2,35,2,0,24
	.byte	'Ifx_SCU_WDTS_SR_Bits',0,15,191,9,3
	.word	33458
	.byte	12,15,199,9,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14983
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_ACCEN0',0,15,204,9,3
	.word	33677
	.byte	12,15,207,9,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15540
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_ACCEN1',0,15,212,9,3
	.word	33741
	.byte	12,15,215,9,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15617
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_ARSTDIS',0,15,220,9,3
	.word	33805
	.byte	12,15,223,9,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15753
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_CCUCON0',0,15,228,9,3
	.word	33870
	.byte	12,15,231,9,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16033
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_CCUCON1',0,15,236,9,3
	.word	33935
	.byte	12,15,239,9,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16271
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_CCUCON2',0,15,244,9,3
	.word	34000
	.byte	12,15,247,9,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16399
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_CCUCON3',0,15,252,9,3
	.word	34065
	.byte	12,15,255,9,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16642
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_CCUCON4',0,15,132,10,3
	.word	34130
	.byte	12,15,135,10,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16877
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_CCUCON5',0,15,140,10,3
	.word	34195
	.byte	12,15,143,10,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17005
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_CCUCON6',0,15,148,10,3
	.word	34260
	.byte	12,15,151,10,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17105
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_CCUCON7',0,15,156,10,3
	.word	34325
	.byte	12,15,159,10,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17205
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_CHIPID',0,15,164,10,3
	.word	34390
	.byte	12,15,167,10,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17413
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_DTSCON',0,15,172,10,3
	.word	34454
	.byte	12,15,175,10,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17578
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_DTSLIM',0,15,180,10,3
	.word	34518
	.byte	12,15,183,10,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17761
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_DTSSTAT',0,15,188,10,3
	.word	34582
	.byte	12,15,191,10,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17915
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_EICR',0,15,196,10,3
	.word	34647
	.byte	12,15,199,10,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18279
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_EIFR',0,15,204,10,3
	.word	34709
	.byte	12,15,207,10,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18490
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_EMSR',0,15,212,10,3
	.word	34771
	.byte	12,15,215,10,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18742
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_ESRCFG',0,15,220,10,3
	.word	34833
	.byte	12,15,223,10,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18860
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_ESROCFG',0,15,228,10,3
	.word	34897
	.byte	12,15,231,10,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18971
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_EVR13CON',0,15,236,10,3
	.word	34962
	.byte	12,15,239,10,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19134
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_EVR33CON',0,15,244,10,3
	.word	35028
	.byte	12,15,247,10,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19297
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_EVRADCSTAT',0,15,252,10,3
	.word	35094
	.byte	12,15,255,10,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19455
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_EVRDVSTAT',0,15,132,11,3
	.word	35162
	.byte	12,15,135,11,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19620
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_EVRMONCTRL',0,15,140,11,3
	.word	35229
	.byte	12,15,143,11,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19949
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_EVROSCCTRL',0,15,148,11,3
	.word	35297
	.byte	12,15,151,11,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20170
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_EVROVMON',0,15,156,11,3
	.word	35365
	.byte	12,15,159,11,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20333
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_EVRRSTCON',0,15,164,11,3
	.word	35431
	.byte	12,15,167,11,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20605
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_EVRSDCOEFF1',0,15,172,11,3
	.word	35498
	.byte	12,15,175,11,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20758
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_EVRSDCOEFF2',0,15,180,11,3
	.word	35567
	.byte	12,15,183,11,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20914
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_EVRSDCOEFF3',0,15,188,11,3
	.word	35636
	.byte	12,15,191,11,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21076
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_EVRSDCOEFF4',0,15,196,11,3
	.word	35705
	.byte	12,15,199,11,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21219
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_EVRSDCOEFF5',0,15,204,11,3
	.word	35774
	.byte	12,15,207,11,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21384
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_EVRSDCOEFF6',0,15,212,11,3
	.word	35843
	.byte	12,15,215,11,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21529
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_EVRSDCTRL1',0,15,220,11,3
	.word	35912
	.byte	12,15,223,11,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21710
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_EVRSDCTRL2',0,15,228,11,3
	.word	35980
	.byte	12,15,231,11,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21884
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_EVRSDCTRL3',0,15,236,11,3
	.word	36048
	.byte	12,15,239,11,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22044
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_EVRSDCTRL4',0,15,244,11,3
	.word	36116
	.byte	12,15,247,11,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22188
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_EVRSTAT',0,15,252,11,3
	.word	36184
	.byte	12,15,255,11,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22462
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_EVRTRIM',0,15,132,12,3
	.word	36249
	.byte	12,15,135,12,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22601
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_EVRUVMON',0,15,140,12,3
	.word	36314
	.byte	12,15,143,12,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22764
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_EXTCON',0,15,148,12,3
	.word	36380
	.byte	12,15,151,12,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22982
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_FDR',0,15,156,12,3
	.word	36444
	.byte	12,15,159,12,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23145
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_FMR',0,15,164,12,3
	.word	36505
	.byte	12,15,167,12,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23481
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_ID',0,15,172,12,3
	.word	36566
	.byte	12,15,175,12,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23588
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_IGCR',0,15,180,12,3
	.word	36626
	.byte	12,15,183,12,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24040
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_IN',0,15,188,12,3
	.word	36688
	.byte	12,15,191,12,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24139
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_IOCR',0,15,196,12,3
	.word	36748
	.byte	12,15,199,12,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24289
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_LBISTCTRL0',0,15,204,12,3
	.word	36810
	.byte	12,15,207,12,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24438
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_LBISTCTRL1',0,15,212,12,3
	.word	36878
	.byte	12,15,215,12,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24599
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_LBISTCTRL2',0,15,220,12,3
	.word	36946
	.byte	12,15,223,12,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24729
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_LCLCON',0,15,228,12,3
	.word	37014
	.byte	12,15,231,12,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24861
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_LCLTEST',0,15,236,12,3
	.word	37078
	.byte	12,15,239,12,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24976
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_MANID',0,15,244,12,3
	.word	37143
	.byte	12,15,247,12,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25087
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_OMR',0,15,252,12,3
	.word	37206
	.byte	12,15,255,12,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25245
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_OSCCON',0,15,132,13,3
	.word	37267
	.byte	12,15,135,13,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25657
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_OUT',0,15,140,13,3
	.word	37331
	.byte	12,15,143,13,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25758
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_OVCCON',0,15,148,13,3
	.word	37392
	.byte	12,15,151,13,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26025
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_OVCENABLE',0,15,156,13,3
	.word	37456
	.byte	12,15,159,13,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26161
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_PDISC',0,15,164,13,3
	.word	37523
	.byte	12,15,167,13,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26272
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_PDR',0,15,172,13,3
	.word	37586
	.byte	12,15,175,13,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26405
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_PDRR',0,15,180,13,3
	.word	37647
	.byte	12,15,183,13,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26608
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_PLLCON0',0,15,188,13,3
	.word	37709
	.byte	12,15,191,13,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26964
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_PLLCON1',0,15,196,13,3
	.word	37774
	.byte	12,15,199,13,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27142
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_PLLCON2',0,15,204,13,3
	.word	37839
	.byte	12,15,207,13,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27242
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_PLLERAYCON0',0,15,212,13,3
	.word	37904
	.byte	12,15,215,13,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27612
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_PLLERAYCON1',0,15,220,13,3
	.word	37973
	.byte	12,15,223,13,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27798
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_PLLERAYSTAT',0,15,228,13,3
	.word	38042
	.byte	12,15,231,13,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27996
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_PLLSTAT',0,15,236,13,3
	.word	38111
	.byte	12,15,239,13,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28229
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_PMCSR',0,15,244,13,3
	.word	38176
	.byte	12,15,247,13,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28381
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_PMSWCR0',0,15,252,13,3
	.word	38239
	.byte	12,15,255,13,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28948
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_PMSWCR1',0,15,132,14,3
	.word	38304
	.byte	12,15,135,14,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29242
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_PMSWCR2',0,15,140,14,3
	.word	38369
	.byte	12,15,143,14,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29520
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_PMSWSTAT',0,15,148,14,3
	.word	38434
	.byte	12,15,151,14,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30016
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_PMSWSTATCLR',0,15,156,14,3
	.word	38500
	.byte	12,15,159,14,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30538
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_RSTCON',0,15,164,14,3
	.word	38569
	.byte	12,15,167,14,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30329
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_RSTCON2',0,15,172,14,3
	.word	38633
	.byte	12,15,175,14,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30749
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_RSTSTAT',0,15,180,14,3
	.word	38698
	.byte	12,15,183,14,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31181
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_SAFECON',0,15,188,14,3
	.word	38763
	.byte	12,15,191,14,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31277
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_STSTAT',0,15,196,14,3
	.word	38828
	.byte	12,15,199,14,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31537
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_SWRSTCON',0,15,204,14,3
	.word	38892
	.byte	12,15,207,14,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31662
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_SYSCON',0,15,212,14,3
	.word	38958
	.byte	12,15,215,14,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31859
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_TRAPCLR',0,15,220,14,3
	.word	39022
	.byte	12,15,223,14,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32012
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_TRAPDIS',0,15,228,14,3
	.word	39087
	.byte	12,15,231,14,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32165
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_TRAPSET',0,15,236,14,3
	.word	39152
	.byte	12,15,239,14,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32318
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_TRAPSTAT',0,15,244,14,3
	.word	39217
	.byte	12,15,247,14,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32489
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_WDTCPU_CON0',0,15,252,14,3
	.word	39283
	.byte	12,15,255,14,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32619
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_WDTCPU_CON1',0,15,132,15,3
	.word	39352
	.byte	12,15,135,15,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32857
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_WDTCPU_SR',0,15,140,15,3
	.word	39421
	.byte	12,15,143,15,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33080
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_WDTS_CON0',0,15,148,15,3
	.word	39488
	.byte	12,15,151,15,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33206
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_WDTS_CON1',0,15,156,15,3
	.word	39555
	.byte	12,15,159,15,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33458
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_WDTS_SR',0,15,164,15,3
	.word	39622
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU',0,15,175,15,25,12,13
	.byte	'CON0',0
	.word	39283
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	39352
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	39421
	.byte	4,2,35,8,0,16
	.word	39687
	.byte	24
	.byte	'Ifx_SCU_WDTCPU',0,15,180,15,3
	.word	39750
	.byte	10
	.byte	'_Ifx_SCU_WDTS',0,15,183,15,25,12,13
	.byte	'CON0',0
	.word	39488
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	39555
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	39622
	.byte	4,2,35,8,0,16
	.word	39779
	.byte	24
	.byte	'Ifx_SCU_WDTS',0,15,188,15,3
	.word	39840
	.byte	17,3,83,9,1,18
	.byte	'IfxPort_InputMode_undefined',0,127,18
	.byte	'IfxPort_InputMode_noPullDevice',0,0,18
	.byte	'IfxPort_InputMode_pullDown',0,8,18
	.byte	'IfxPort_InputMode_pullUp',0,16,0,24
	.byte	'IfxPort_InputMode',0,3,89,3
	.word	39867
	.byte	17,3,120,9,1,18
	.byte	'IfxPort_OutputIdx_general',0,128,1,18
	.byte	'IfxPort_OutputIdx_alt1',0,136,1,18
	.byte	'IfxPort_OutputIdx_alt2',0,144,1,18
	.byte	'IfxPort_OutputIdx_alt3',0,152,1,18
	.byte	'IfxPort_OutputIdx_alt4',0,160,1,18
	.byte	'IfxPort_OutputIdx_alt5',0,168,1,18
	.byte	'IfxPort_OutputIdx_alt6',0,176,1,18
	.byte	'IfxPort_OutputIdx_alt7',0,184,1,0,24
	.byte	'IfxPort_OutputIdx',0,3,130,1,3
	.word	40018
	.byte	17,3,134,1,9,1,18
	.byte	'IfxPort_OutputMode_pushPull',0,128,1,18
	.byte	'IfxPort_OutputMode_openDrain',0,192,1,0,24
	.byte	'IfxPort_OutputMode',0,3,138,1,3
	.word	40262
	.byte	17,3,144,1,9,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed1',0,0,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed2',0,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed3',0,2,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed4',0,3,18
	.byte	'IfxPort_PadDriver_lvdsSpeed1',0,4,18
	.byte	'IfxPort_PadDriver_lvdsSpeed2',0,5,18
	.byte	'IfxPort_PadDriver_lvdsSpeed3',0,6,18
	.byte	'IfxPort_PadDriver_lvdsSpeed4',0,7,18
	.byte	'IfxPort_PadDriver_ttlSpeed1',0,8,18
	.byte	'IfxPort_PadDriver_ttlSpeed2',0,9,18
	.byte	'IfxPort_PadDriver_ttlSpeed3',0,10,18
	.byte	'IfxPort_PadDriver_ttlSpeed4',0,11,0,24
	.byte	'IfxPort_PadDriver',0,3,158,1,3
	.word	40360
	.byte	24
	.byte	'IfxPort_State',0,3,178,1,3
	.word	8258
	.byte	24
	.byte	'gpio_pin_enum',0,8,89,2
	.word	10794
	.byte	24
	.byte	'gpio_dir_enum',0,8,95,2
	.word	12768
	.byte	24
	.byte	'gpio_mode_enum',0,8,111,2
	.word	12786
.L232:
	.byte	14,12
	.word	266
	.byte	15,2,0,0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L104:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,21,0,54,15,39,12,0,0,3,15,0,73,19,0,0,4,46,1,3,8,32,13
	.byte	58,15,59,15,57,15,54,15,39,12,0,0,5,5,0,3,8,58,15,59,15,57,15,73,19,0,0,6,11,0,0,0,7,36,0,3,8,11,15,62
	.byte	15,0,0,8,46,1,3,8,32,13,58,15,59,15,57,15,73,19,54,15,39,12,0,0,9,59,0,3,8,0,0,10,19,1,3,8,58,15,59,15
	.byte	57,15,11,15,0,0,11,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,12,23,1,58,15,59,15,57,15,11,15,0,0,13,13
	.byte	0,3,8,73,19,11,15,56,9,0,0,14,1,1,11,15,73,19,0,0,15,33,0,47,15,0,0,16,53,0,73,19,0,0,17,4,1,58,15,59
	.byte	15,57,15,11,15,0,0,18,40,0,3,8,28,13,0,0,19,46,1,3,8,58,15,59,15,57,15,54,15,39,12,63,12,60,12,0,0,20
	.byte	38,0,73,19,0,0,21,46,1,3,8,58,15,59,15,57,15,73,19,54,15,39,12,63,12,60,12,0,0,22,46,1,49,19,0,0,23,5
	.byte	0,49,19,0,0,24,22,0,3,8,58,15,59,15,57,15,73,19,0,0,25,21,0,54,15,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L105:
	.word	.L313-.L312
.L312:
	.half	3
	.word	.L315-.L314
.L314:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver',0
	.byte	'F:\\ADS\\AURIX-Studio-1.10.2\\tools\\Compilers\\Tasking_1.1r8\\ctc\\include\\',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std',0
	.byte	0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_Intrinsics.h',0
	.byte	0,0,0
	.byte	'IFXPORT.h',0,1,0,0
	.byte	'IfxPort_regdef.h',0,2,0,0
	.byte	'zf_common_debug.h',0,3,0,0
	.byte	'zf_driver_delay.h',0,4,0,0
	.byte	'zf_driver_spi.h',0,4,0,0
	.byte	'zf_driver_gpio.h',0,4,0,0
	.byte	'../libraries/zf_device/zf_device_imu963ra.c',0,0,0,0
	.byte	'stdio.h',0,5,0,0
	.byte	'Platform_Types.h',0,6,0,0
	.byte	'ifx_types.h',0,6,0,0
	.byte	'zf_common_typedef.h',0,3,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxScu_cfg.h',0
	.byte	0,0,0
	.byte	'IfxScu_regdef.h',0,2,0,0,0
.L315:
.L313:
	.sdecl	'.debug_info',debug,cluster('imu963ra_get_acc')
	.sect	'.debug_info'
.L106:
	.word	277
	.half	3
	.word	.L107
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_imu963ra.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L109,.L108
	.byte	2
	.word	.L102
	.byte	3
	.byte	'imu963ra_get_acc',0,1,168,2,6,1,1,1
	.word	.L95,.L186,.L94
	.byte	4
	.word	.L95,.L186
	.byte	5
	.byte	'dat',0,1,170,2,11
	.word	.L187,.L188
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('imu963ra_get_acc')
	.sect	'.debug_abbrev'
.L107:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('imu963ra_get_acc')
	.sect	'.debug_line'
.L108:
	.word	.L317-.L316
.L316:
	.half	3
	.word	.L319-.L318
.L318:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_imu963ra.c',0,0,0,0,0
.L319:
	.byte	5,6,7,0,5,2
	.word	.L95
	.byte	3,167,2,1,5,38,9
	.half	.L304-.L95
	.byte	3,4,1,5,57,9
	.half	.L320-.L304
	.byte	1,5,62,9
	.half	.L321-.L320
	.byte	1,5,5,9
	.half	.L322-.L321
	.byte	3,1,1,5,42,9
	.half	.L323-.L322
	.byte	1,5,45,9
	.half	.L324-.L323
	.byte	1,5,54,9
	.half	.L325-.L324
	.byte	1,5,49,9
	.half	.L326-.L325
	.byte	1,5,20,9
	.half	.L327-.L326
	.byte	1,5,5,9
	.half	.L328-.L327
	.byte	3,1,1,5,42,9
	.half	.L329-.L328
	.byte	1,5,45,9
	.half	.L330-.L329
	.byte	1,5,54,9
	.half	.L331-.L330
	.byte	1,5,49,9
	.half	.L332-.L331
	.byte	1,5,20,9
	.half	.L333-.L332
	.byte	1,5,5,9
	.half	.L334-.L333
	.byte	3,1,1,5,42,9
	.half	.L335-.L334
	.byte	1,5,45,9
	.half	.L336-.L335
	.byte	1,5,54,9
	.half	.L337-.L336
	.byte	1,5,49,9
	.half	.L338-.L337
	.byte	1,5,20,9
	.half	.L339-.L338
	.byte	1,5,1,9
	.half	.L340-.L339
	.byte	3,1,1,7,9
	.half	.L110-.L340
	.byte	0,1,1
.L317:
	.sdecl	'.debug_ranges',debug,cluster('imu963ra_get_acc')
	.sect	'.debug_ranges'
.L109:
	.word	-1,.L95,0,.L110-.L95,0,0
	.sdecl	'.debug_info',debug,cluster('imu963ra_get_gyro')
	.sect	'.debug_info'
.L111:
	.word	278
	.half	3
	.word	.L112
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_imu963ra.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L114,.L113
	.byte	2
	.word	.L102
	.byte	3
	.byte	'imu963ra_get_gyro',0,1,186,2,6,1,1,1
	.word	.L97,.L189,.L96
	.byte	4
	.word	.L97,.L189
	.byte	5
	.byte	'dat',0,1,188,2,11
	.word	.L187,.L190
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('imu963ra_get_gyro')
	.sect	'.debug_abbrev'
.L112:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('imu963ra_get_gyro')
	.sect	'.debug_line'
.L113:
	.word	.L342-.L341
.L341:
	.half	3
	.word	.L344-.L343
.L343:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_imu963ra.c',0,0,0,0,0
.L344:
	.byte	5,6,7,0,5,2
	.word	.L97
	.byte	3,185,2,1,5,38,9
	.half	.L305-.L97
	.byte	3,4,1,5,57,9
	.half	.L345-.L305
	.byte	1,5,62,9
	.half	.L346-.L345
	.byte	1,5,5,9
	.half	.L347-.L346
	.byte	3,1,1,5,43,9
	.half	.L348-.L347
	.byte	1,5,46,9
	.half	.L349-.L348
	.byte	1,5,55,9
	.half	.L350-.L349
	.byte	1,5,50,9
	.half	.L351-.L350
	.byte	1,5,21,9
	.half	.L352-.L351
	.byte	1,5,5,9
	.half	.L353-.L352
	.byte	3,1,1,5,43,9
	.half	.L354-.L353
	.byte	1,5,46,9
	.half	.L355-.L354
	.byte	1,5,55,9
	.half	.L356-.L355
	.byte	1,5,50,9
	.half	.L357-.L356
	.byte	1,5,21,9
	.half	.L358-.L357
	.byte	1,5,5,9
	.half	.L359-.L358
	.byte	3,1,1,5,43,9
	.half	.L360-.L359
	.byte	1,5,46,9
	.half	.L361-.L360
	.byte	1,5,55,9
	.half	.L362-.L361
	.byte	1,5,50,9
	.half	.L363-.L362
	.byte	1,5,21,9
	.half	.L364-.L363
	.byte	1,5,1,9
	.half	.L365-.L364
	.byte	3,1,1,7,9
	.half	.L115-.L365
	.byte	0,1,1
.L342:
	.sdecl	'.debug_ranges',debug,cluster('imu963ra_get_gyro')
	.sect	'.debug_ranges'
.L114:
	.word	-1,.L97,0,.L115-.L97,0,0
	.sdecl	'.debug_info',debug,cluster('imu963ra_get_mag')
	.sect	'.debug_info'
.L116:
	.word	302
	.half	3
	.word	.L117
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_imu963ra.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L119,.L118
	.byte	2
	.word	.L102
	.byte	3
	.byte	'imu963ra_get_mag',0,1,204,2,6,1,1,1
	.word	.L99,.L191,.L98
	.byte	4
	.word	.L99,.L191
	.byte	5
	.byte	'temp_status',0,1,206,2,11
	.word	.L192,.L193
	.byte	5
	.byte	'dat',0,1,207,2,11
	.word	.L187,.L194
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('imu963ra_get_mag')
	.sect	'.debug_abbrev'
.L117:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('imu963ra_get_mag')
	.sect	'.debug_line'
.L118:
	.word	.L367-.L366
.L366:
	.half	3
	.word	.L369-.L368
.L368:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_imu963ra.c',0,0,0,0,0
.L369:
	.byte	5,6,7,0,5,2
	.word	.L99
	.byte	3,203,2,1,5,38,9
	.half	.L306-.L99
	.byte	3,5,1,5,64,9
	.half	.L370-.L306
	.byte	1,5,51,9
	.half	.L371-.L370
	.byte	3,1,1,5,5,9
	.half	.L307-.L371
	.byte	3,1,1,5,42,7,9
	.half	.L372-.L307
	.byte	3,2,1,5,65,9
	.half	.L373-.L372
	.byte	1,5,70,9
	.half	.L374-.L373
	.byte	1,5,9,9
	.half	.L308-.L374
	.byte	3,1,1,5,46,9
	.half	.L375-.L308
	.byte	1,5,49,9
	.half	.L376-.L375
	.byte	1,5,58,9
	.half	.L377-.L376
	.byte	1,5,53,9
	.half	.L378-.L377
	.byte	1,5,24,9
	.half	.L379-.L378
	.byte	1,5,9,9
	.half	.L380-.L379
	.byte	3,1,1,5,46,9
	.half	.L381-.L380
	.byte	1,5,49,9
	.half	.L382-.L381
	.byte	1,5,58,9
	.half	.L383-.L382
	.byte	1,5,53,9
	.half	.L384-.L383
	.byte	1,5,24,9
	.half	.L385-.L384
	.byte	1,5,9,9
	.half	.L386-.L385
	.byte	3,1,1,5,46,9
	.half	.L387-.L386
	.byte	1,5,49,9
	.half	.L388-.L387
	.byte	1,5,58,9
	.half	.L389-.L388
	.byte	1,5,53,9
	.half	.L390-.L389
	.byte	1,5,24,9
	.half	.L391-.L390
	.byte	1,5,38,9
	.half	.L35-.L391
	.byte	3,2,1,5,64,9
	.half	.L392-.L35
	.byte	1,5,1,9
	.half	.L393-.L392
	.byte	3,1,1,7,9
	.half	.L120-.L393
	.byte	0,1,1
.L367:
	.sdecl	'.debug_ranges',debug,cluster('imu963ra_get_mag')
	.sect	'.debug_ranges'
.L119:
	.word	-1,.L99,0,.L120-.L99,0,0
	.sdecl	'.debug_info',debug,cluster('imu963ra_init')
	.sect	'.debug_info'
.L121:
	.word	287
	.half	3
	.word	.L122
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_imu963ra.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L124,.L123
	.byte	2
	.word	.L102
	.byte	3
	.byte	'imu963ra_init',0,1,228,2,7
	.word	.L192
	.byte	1,1,1
	.word	.L101,.L195,.L100
	.byte	4
	.word	.L101,.L195
	.byte	5
	.byte	'return_state',0,1,230,2,11
	.word	.L192,.L196
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('imu963ra_init')
	.sect	'.debug_abbrev'
.L122:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('imu963ra_init')
	.sect	'.debug_line'
.L123:
	.word	.L395-.L394
.L394:
	.half	3
	.word	.L397-.L396
.L396:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_imu963ra.c',0,0,0,0,0
.L397:
	.byte	5,7,7,0,5,2
	.word	.L101
	.byte	3,227,2,1,5,24,9
	.half	.L309-.L101
	.byte	3,2,1,5,21,9
	.half	.L310-.L309
	.byte	3,1,1,5,77,9
	.half	.L398-.L310
	.byte	3,5,1,5,95,9
	.half	.L399-.L398
	.byte	1,5,113,9
	.half	.L400-.L399
	.byte	1,5,14,9
	.half	.L401-.L400
	.byte	1,5,28,9
	.half	.L402-.L401
	.byte	1,5,39,9
	.half	.L403-.L402
	.byte	1,5,59,9
	.half	.L404-.L403
	.byte	1,5,15,9
	.half	.L405-.L404
	.byte	3,1,1,5,32,9
	.half	.L406-.L405
	.byte	1,5,37,9
	.half	.L407-.L406
	.byte	1,5,47,9
	.half	.L408-.L407
	.byte	1,5,42,9
	.half	.L36-.L408
	.byte	3,5,1,5,68,9
	.half	.L409-.L36
	.byte	1,5,42,9
	.half	.L410-.L409
	.byte	3,1,1,5,60,9
	.half	.L411-.L410
	.byte	1,5,25,9
	.half	.L412-.L411
	.byte	3,1,1,5,42,9
	.half	.L413-.L412
	.byte	3,1,1,5,68,9
	.half	.L414-.L413
	.byte	1,5,40,9
	.half	.L415-.L414
	.byte	3,1,1,5,9,9
	.half	.L416-.L415
	.byte	1,5,13,7,9
	.half	.L417-.L416
	.byte	3,2,1,5,26,9
	.half	.L418-.L417
	.byte	3,1,1,5,13,9
	.half	.L419-.L418
	.byte	3,1,1,5,42,9
	.half	.L37-.L419
	.byte	3,3,1,5,62,9
	.half	.L420-.L37
	.byte	1,5,16,9
	.half	.L421-.L420
	.byte	3,7,1,5,18,9
	.half	.L422-.L421
	.byte	3,7,1,9
	.half	.L423-.L422
	.byte	3,5,1,9
	.half	.L424-.L423
	.byte	3,5,1,9
	.half	.L425-.L424
	.byte	3,5,1,5,17,9
	.half	.L43-.L425
	.byte	3,110,1,5,30,9
	.half	.L426-.L43
	.byte	3,1,1,5,14,9
	.half	.L427-.L426
	.byte	3,1,1,5,50,9
	.half	.L39-.L427
	.byte	3,3,1,5,69,9
	.half	.L428-.L39
	.byte	1,5,17,9
	.half	.L429-.L428
	.byte	3,1,1,5,49,9
	.half	.L430-.L429
	.byte	1,5,47,9
	.half	.L431-.L430
	.byte	1,5,14,9
	.half	.L432-.L431
	.byte	3,1,1,5,50,9
	.half	.L40-.L432
	.byte	3,3,1,5,69,9
	.half	.L433-.L40
	.byte	1,5,17,9
	.half	.L434-.L433
	.byte	3,1,1,5,49,9
	.half	.L435-.L434
	.byte	1,5,47,9
	.half	.L436-.L435
	.byte	1,5,14,9
	.half	.L437-.L436
	.byte	3,1,1,5,50,9
	.half	.L41-.L437
	.byte	3,3,1,5,69,9
	.half	.L438-.L41
	.byte	1,5,17,9
	.half	.L439-.L438
	.byte	3,1,1,5,49,9
	.half	.L440-.L439
	.byte	1,5,47,9
	.half	.L441-.L440
	.byte	1,5,14,9
	.half	.L442-.L441
	.byte	3,1,1,5,50,9
	.half	.L42-.L442
	.byte	3,3,1,5,69,9
	.half	.L443-.L42
	.byte	1,5,17,9
	.half	.L444-.L443
	.byte	3,1,1,5,49,9
	.half	.L445-.L444
	.byte	1,5,47,9
	.half	.L446-.L445
	.byte	1,5,14,9
	.half	.L447-.L446
	.byte	3,1,1,5,9,9
	.half	.L44-.L447
	.byte	3,2,1,5,13,7,9
	.half	.L448-.L44
	.byte	3,2,1,5,16,9
	.half	.L49-.L448
	.byte	3,10,1,5,18,9
	.half	.L449-.L49
	.byte	3,7,1,9
	.half	.L450-.L449
	.byte	3,5,1,9
	.half	.L451-.L450
	.byte	3,5,1,9
	.half	.L452-.L451
	.byte	3,5,1,9
	.half	.L453-.L452
	.byte	3,5,1,9
	.half	.L454-.L453
	.byte	3,5,1,5,17,9
	.half	.L57-.L454
	.byte	3,100,1,5,30,9
	.half	.L455-.L57
	.byte	3,1,1,5,14,9
	.half	.L456-.L455
	.byte	3,1,1,5,50,9
	.half	.L51-.L456
	.byte	3,3,1,5,68,9
	.half	.L457-.L51
	.byte	1,5,17,9
	.half	.L458-.L457
	.byte	3,1,1,5,49,9
	.half	.L459-.L458
	.byte	1,5,47,9
	.half	.L460-.L459
	.byte	1,5,14,9
	.half	.L461-.L460
	.byte	3,1,1,5,50,9
	.half	.L52-.L461
	.byte	3,3,1,5,68,9
	.half	.L462-.L52
	.byte	1,5,17,9
	.half	.L463-.L462
	.byte	3,1,1,5,49,9
	.half	.L464-.L463
	.byte	1,5,47,9
	.half	.L465-.L464
	.byte	1,5,14,9
	.half	.L466-.L465
	.byte	3,1,1,5,50,9
	.half	.L53-.L466
	.byte	3,3,1,5,68,9
	.half	.L467-.L53
	.byte	1,5,17,9
	.half	.L468-.L467
	.byte	3,1,1,5,49,9
	.half	.L469-.L468
	.byte	1,5,47,9
	.half	.L470-.L469
	.byte	1,5,14,9
	.half	.L471-.L470
	.byte	3,1,1,5,50,9
	.half	.L54-.L471
	.byte	3,3,1,5,68,9
	.half	.L472-.L54
	.byte	1,5,17,9
	.half	.L473-.L472
	.byte	3,1,1,5,49,9
	.half	.L474-.L473
	.byte	1,5,47,9
	.half	.L475-.L474
	.byte	1,5,14,9
	.half	.L476-.L475
	.byte	3,1,1,5,50,9
	.half	.L55-.L476
	.byte	3,3,1,5,68,9
	.half	.L477-.L55
	.byte	1,5,17,9
	.half	.L478-.L477
	.byte	3,1,1,5,49,9
	.half	.L479-.L478
	.byte	1,5,47,9
	.half	.L480-.L479
	.byte	1,5,14,9
	.half	.L481-.L480
	.byte	3,1,1,5,50,9
	.half	.L56-.L481
	.byte	3,3,1,5,68,9
	.half	.L482-.L56
	.byte	1,5,17,9
	.half	.L483-.L482
	.byte	3,1,1,5,49,9
	.half	.L484-.L483
	.byte	1,5,47,9
	.half	.L485-.L484
	.byte	1,5,14,9
	.half	.L486-.L485
	.byte	3,1,1,5,9,9
	.half	.L58-.L486
	.byte	3,2,1,5,13,7,9
	.half	.L487-.L58
	.byte	3,2,1,5,42,9
	.half	.L65-.L487
	.byte	3,3,1,5,60,9
	.half	.L488-.L65
	.byte	1,5,42,9
	.half	.L489-.L488
	.byte	3,1,1,5,60,9
	.half	.L490-.L489
	.byte	1,5,42,9
	.half	.L491-.L490
	.byte	3,1,1,5,60,9
	.half	.L492-.L491
	.byte	1,5,42,9
	.half	.L493-.L492
	.byte	3,1,1,5,60,9
	.half	.L494-.L493
	.byte	1,5,42,9
	.half	.L495-.L494
	.byte	3,1,1,5,60,9
	.half	.L496-.L495
	.byte	1,5,42,9
	.half	.L497-.L496
	.byte	3,1,1,5,61,9
	.half	.L498-.L497
	.byte	1,5,42,9
	.half	.L499-.L498
	.byte	3,2,1,5,68,9
	.half	.L500-.L499
	.byte	1,5,42,9
	.half	.L501-.L500
	.byte	3,1,1,5,66,9
	.half	.L502-.L501
	.byte	1,5,25,9
	.half	.L503-.L502
	.byte	3,1,1,5,42,9
	.half	.L504-.L503
	.byte	3,1,1,5,66,9
	.half	.L505-.L504
	.byte	1,5,25,9
	.half	.L506-.L505
	.byte	3,1,1,5,37,9
	.half	.L507-.L506
	.byte	3,2,1,5,56,9
	.half	.L508-.L507
	.byte	1,5,79,9
	.half	.L509-.L508
	.byte	1,5,25,9
	.half	.L510-.L509
	.byte	3,1,1,5,37,9
	.half	.L511-.L510
	.byte	3,1,1,5,56,9
	.half	.L512-.L511
	.byte	1,5,79,9
	.half	.L513-.L512
	.byte	1,5,25,9
	.half	.L514-.L513
	.byte	3,1,1,5,35,9
	.half	.L515-.L514
	.byte	3,2,1,5,9,9
	.half	.L516-.L515
	.byte	1,5,13,7,9
	.half	.L517-.L516
	.byte	3,2,1,5,26,9
	.half	.L518-.L517
	.byte	3,1,1,5,13,9
	.half	.L519-.L518
	.byte	3,1,1,5,16,9
	.half	.L67-.L519
	.byte	3,6,1,5,18,9
	.half	.L520-.L67
	.byte	3,7,1,9
	.half	.L521-.L520
	.byte	3,5,1,5,17,9
	.half	.L71-.L521
	.byte	3,120,1,5,30,9
	.half	.L522-.L71
	.byte	3,1,1,5,14,9
	.half	.L523-.L522
	.byte	3,1,1,5,45,9
	.half	.L69-.L523
	.byte	3,3,1,5,64,9
	.half	.L524-.L69
	.byte	1,5,87,9
	.half	.L525-.L524
	.byte	1,5,17,9
	.half	.L526-.L525
	.byte	3,1,1,5,49,9
	.half	.L527-.L526
	.byte	1,5,47,9
	.half	.L528-.L527
	.byte	1,5,14,9
	.half	.L529-.L528
	.byte	3,1,1,5,45,9
	.half	.L70-.L529
	.byte	3,3,1,5,64,9
	.half	.L530-.L70
	.byte	1,5,87,9
	.half	.L531-.L530
	.byte	1,5,17,9
	.half	.L532-.L531
	.byte	3,1,1,5,49,9
	.half	.L533-.L532
	.byte	1,5,47,9
	.half	.L534-.L533
	.byte	1,5,14,9
	.half	.L535-.L534
	.byte	3,1,1,5,9,9
	.half	.L72-.L535
	.byte	3,2,1,5,13,7,9
	.half	.L536-.L72
	.byte	3,2,1,5,37,9
	.half	.L75-.L536
	.byte	3,3,1,5,56,9
	.half	.L537-.L75
	.byte	1,5,74,9
	.half	.L538-.L537
	.byte	1,5,30,9
	.half	.L539-.L538
	.byte	3,1,1,5,49,9
	.half	.L540-.L539
	.byte	1,5,42,9
	.half	.L541-.L540
	.byte	3,2,1,5,68,9
	.half	.L542-.L541
	.byte	1,5,25,9
	.half	.L543-.L542
	.byte	3,2,1,5,5,9
	.half	.L38-.L543
	.byte	3,2,1,5,1,9
	.half	.L77-.L38
	.byte	3,1,1,7,9
	.half	.L125-.L77
	.byte	0,1,1
.L395:
	.sdecl	'.debug_ranges',debug,cluster('imu963ra_init')
	.sect	'.debug_ranges'
.L124:
	.word	-1,.L101,0,.L125-.L101,0,0
	.sdecl	'.debug_info',debug,cluster('imu963ra_write_acc_gyro_register')
	.sect	'.debug_info'
.L126:
	.word	306
	.half	3
	.word	.L127
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_imu963ra.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L129,.L128
	.byte	2
	.word	.L102
	.byte	3
	.byte	'imu963ra_write_acc_gyro_register',0,1,110,13,1,1
	.word	.L79,.L197,.L78
	.byte	4
	.byte	'reg',0,1,110,53
	.word	.L192,.L198
	.byte	4
	.byte	'data',0,1,110,64
	.word	.L192,.L199
	.byte	5
	.word	.L79,.L197
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('imu963ra_write_acc_gyro_register')
	.sect	'.debug_abbrev'
.L127:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('imu963ra_write_acc_gyro_register')
	.sect	'.debug_line'
.L128:
	.word	.L545-.L544
.L544:
	.half	3
	.word	.L547-.L546
.L546:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_imu963ra.c',0,0,0,0,0
.L547:
	.byte	5,13,7,0,5,2
	.word	.L79
	.byte	3,237,0,1,5,5,9
	.half	.L548-.L79
	.byte	3,2,1,5,29,9
	.half	.L3-.L548
	.byte	3,1,1,5,65,9
	.half	.L237-.L3
	.byte	1,5,5,9
	.half	.L240-.L237
	.byte	3,2,1,5,1,9
	.half	.L5-.L240
	.byte	3,1,1,7,9
	.half	.L130-.L5
	.byte	0,1,1
.L545:
	.sdecl	'.debug_ranges',debug,cluster('imu963ra_write_acc_gyro_register')
	.sect	'.debug_ranges'
.L129:
	.word	-1,.L79,0,.L130-.L79,0,0
	.sdecl	'.debug_info',debug,cluster('imu963ra_read_acc_gyro_register')
	.sect	'.debug_info'
.L131:
	.word	310
	.half	3
	.word	.L132
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_imu963ra.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L134,.L133
	.byte	2
	.word	.L102
	.byte	3
	.byte	'imu963ra_read_acc_gyro_register',0,1,125,14
	.word	.L192
	.byte	1,1
	.word	.L81,.L200,.L80
	.byte	4
	.byte	'reg',0,1,125,53
	.word	.L192,.L201
	.byte	5
	.word	.L81,.L200
	.byte	6
	.byte	'data',0,1,127,11
	.word	.L192,.L202
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('imu963ra_read_acc_gyro_register')
	.sect	'.debug_abbrev'
.L132:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('imu963ra_read_acc_gyro_register')
	.sect	'.debug_line'
.L133:
	.word	.L550-.L549
.L549:
	.half	3
	.word	.L552-.L551
.L551:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_imu963ra.c',0,0,0,0,0
.L552:
	.byte	5,14,7,0,5,2
	.word	.L81
	.byte	3,252,0,1,5,5,9
	.half	.L242-.L81
	.byte	3,3,1,5,35,9
	.half	.L7-.L242
	.byte	3,1,1,5,53,9
	.half	.L553-.L7
	.byte	1,5,10,9
	.half	.L245-.L553
	.byte	1,5,5,9
	.half	.L243-.L245
	.byte	3,2,1,9
	.half	.L9-.L243
	.byte	3,1,1,5,1,9
	.half	.L10-.L9
	.byte	3,1,1,7,9
	.half	.L135-.L10
	.byte	0,1,1
.L550:
	.sdecl	'.debug_ranges',debug,cluster('imu963ra_read_acc_gyro_register')
	.sect	'.debug_ranges'
.L134:
	.word	-1,.L81,0,.L135-.L81,0,0
	.sdecl	'.debug_info',debug,cluster('imu963ra_read_acc_gyro_registers')
	.sect	'.debug_info'
.L136:
	.word	326
	.half	3
	.word	.L137
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_imu963ra.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L139,.L138
	.byte	2
	.word	.L102
	.byte	3
	.byte	'imu963ra_read_acc_gyro_registers',0,1,144,1,13,1,1
	.word	.L83,.L203,.L82
	.byte	4
	.byte	'reg',0,1,144,1,53
	.word	.L192,.L204
	.byte	4
	.byte	'data',0,1,144,1,65
	.word	.L205,.L206
	.byte	4
	.byte	'len',0,1,144,1,78
	.word	.L207,.L208
	.byte	5
	.word	.L83,.L203
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('imu963ra_read_acc_gyro_registers')
	.sect	'.debug_abbrev'
.L137:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('imu963ra_read_acc_gyro_registers')
	.sect	'.debug_line'
.L138:
	.word	.L555-.L554
.L554:
	.half	3
	.word	.L557-.L556
.L556:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_imu963ra.c',0,0,0,0,0
.L557:
	.byte	5,13,7,0,5,2
	.word	.L83
	.byte	3,143,1,1,5,5,9
	.half	.L253-.L83
	.byte	3,2,1,5,29,9
	.half	.L12-.L253
	.byte	3,1,1,5,47,9
	.half	.L558-.L12
	.byte	1,5,71,9
	.half	.L559-.L558
	.byte	1,5,5,9
	.half	.L257-.L559
	.byte	3,2,1,5,1,9
	.half	.L14-.L257
	.byte	3,1,1,7,9
	.half	.L140-.L14
	.byte	0,1,1
.L555:
	.sdecl	'.debug_ranges',debug,cluster('imu963ra_read_acc_gyro_registers')
	.sect	'.debug_ranges'
.L139:
	.word	-1,.L83,0,.L140-.L83,0,0
	.sdecl	'.debug_info',debug,cluster('imu963ra_write_mag_register')
	.sect	'.debug_info'
.L141:
	.word	380
	.half	3
	.word	.L142
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_imu963ra.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L144,.L143
	.byte	2
	.word	.L102
	.byte	3
	.byte	'imu963ra_write_mag_register',0,1,162,1,14
	.word	.L192
	.byte	1,1
	.word	.L85,.L209,.L84
	.byte	4
	.byte	'addr',0,1,162,1,49
	.word	.L192,.L210
	.byte	4
	.byte	'reg',0,1,162,1,61
	.word	.L192,.L211
	.byte	4
	.byte	'data',0,1,162,1,72
	.word	.L192,.L212
	.byte	5
	.word	.L85,.L209
	.byte	6
	.byte	'return_state',0,1,164,1,11
	.word	.L192,.L213
	.byte	6
	.byte	'timeout_count',0,1,165,1,12
	.word	.L214,.L215
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('imu963ra_write_mag_register')
	.sect	'.debug_abbrev'
.L142:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('imu963ra_write_mag_register')
	.sect	'.debug_line'
.L143:
	.word	.L561-.L560
.L560:
	.half	3
	.word	.L563-.L562
.L562:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_imu963ra.c',0,0,0,0,0
.L563:
	.byte	5,14,7,0,5,2
	.word	.L85
	.byte	3,161,1,1,5,24,9
	.half	.L263-.L85
	.byte	3,2,1,5,26,9
	.half	.L264-.L263
	.byte	3,1,1,5,17,9
	.half	.L265-.L264
	.byte	3,2,1,5,38,9
	.half	.L259-.L265
	.byte	3,1,1,5,60,9
	.half	.L564-.L259
	.byte	1,5,38,9
	.half	.L260-.L564
	.byte	3,1,1,5,57,9
	.half	.L565-.L260
	.byte	1,5,38,9
	.half	.L268-.L565
	.byte	3,1,1,5,60,9
	.half	.L566-.L268
	.byte	1,5,38,9
	.half	.L270-.L566
	.byte	3,1,1,5,63,9
	.half	.L567-.L270
	.byte	1,5,38,9
	.half	.L272-.L567
	.byte	3,1,1,5,62,9
	.half	.L568-.L272
	.byte	1,5,80,9
	.half	.L569-.L568
	.byte	3,3,1,5,51,9
	.half	.L16-.L569
	.byte	3,2,1,5,12,9
	.half	.L273-.L16
	.byte	1,5,9,9
	.half	.L570-.L273
	.byte	1,5,26,7,9
	.half	.L571-.L570
	.byte	3,2,1,5,13,9
	.half	.L572-.L571
	.byte	3,1,1,5,25,9
	.half	.L17-.L572
	.byte	3,2,1,5,56,9
	.half	.L15-.L17
	.byte	3,121,1,5,80,9
	.half	.L573-.L15
	.byte	1,5,5,7,9
	.half	.L18-.L573
	.byte	3,9,1,5,1,9
	.half	.L19-.L18
	.byte	3,1,1,7,9
	.half	.L145-.L19
	.byte	0,1,1
.L561:
	.sdecl	'.debug_ranges',debug,cluster('imu963ra_write_mag_register')
	.sect	'.debug_ranges'
.L144:
	.word	-1,.L85,0,.L145-.L85,0,0
	.sdecl	'.debug_info',debug,cluster('imu963ra_read_mag_register')
	.sect	'.debug_info'
.L146:
	.word	335
	.half	3
	.word	.L147
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_imu963ra.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L149,.L148
	.byte	2
	.word	.L102
	.byte	3
	.byte	'imu963ra_read_mag_register',0,1,195,1,14
	.word	.L192
	.byte	1,1
	.word	.L87,.L216,.L86
	.byte	4
	.byte	'addr',0,1,195,1,48
	.word	.L192,.L217
	.byte	4
	.byte	'reg',0,1,195,1,60
	.word	.L192,.L218
	.byte	5
	.word	.L87,.L216
	.byte	6
	.byte	'timeout_count',0,1,197,1,12
	.word	.L214,.L219
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('imu963ra_read_mag_register')
	.sect	'.debug_abbrev'
.L147:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('imu963ra_read_mag_register')
	.sect	'.debug_line'
.L148:
	.word	.L575-.L574
.L574:
	.half	3
	.word	.L577-.L576
.L576:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_imu963ra.c',0,0,0,0,0
.L577:
	.byte	5,14,7,0,5,2
	.word	.L87
	.byte	3,194,1,1,5,26,9
	.half	.L277-.L87
	.byte	3,2,1,5,17,9
	.half	.L278-.L277
	.byte	3,2,1,5,38,9
	.half	.L275-.L278
	.byte	3,1,1,5,62,9
	.half	.L578-.L275
	.byte	1,5,38,9
	.half	.L579-.L578
	.byte	3,1,1,5,60,9
	.half	.L580-.L579
	.byte	1,5,38,9
	.half	.L281-.L580
	.byte	3,1,1,5,60,9
	.half	.L581-.L281
	.byte	1,5,38,9
	.half	.L582-.L581
	.byte	3,1,1,5,62,9
	.half	.L583-.L582
	.byte	1,5,80,9
	.half	.L584-.L583
	.byte	3,3,1,5,51,9
	.half	.L21-.L584
	.byte	3,2,1,5,12,9
	.half	.L282-.L21
	.byte	1,5,9,9
	.half	.L585-.L282
	.byte	1,5,13,7,9
	.half	.L586-.L585
	.byte	3,2,1,5,25,9
	.half	.L22-.L586
	.byte	3,2,1,5,56,9
	.half	.L20-.L22
	.byte	3,122,1,5,80,9
	.half	.L587-.L20
	.byte	1,5,45,7,9
	.half	.L23-.L587
	.byte	3,9,1,5,5,9
	.half	.L588-.L23
	.byte	1,5,1,9
	.half	.L24-.L588
	.byte	3,1,1,7,9
	.half	.L150-.L24
	.byte	0,1,1
.L575:
	.sdecl	'.debug_ranges',debug,cluster('imu963ra_read_mag_register')
	.sect	'.debug_ranges'
.L149:
	.word	-1,.L87,0,.L150-.L87,0,0
	.sdecl	'.debug_info',debug,cluster('imu963ra_connect_mag')
	.sect	'.debug_info'
.L151:
	.word	297
	.half	3
	.word	.L152
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_imu963ra.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L154,.L153
	.byte	2
	.word	.L102
	.byte	3
	.byte	'imu963ra_connect_mag',0,1,226,1,13,1,1
	.word	.L89,.L220,.L88
	.byte	4
	.byte	'addr',0,1,226,1,41
	.word	.L192,.L221
	.byte	4
	.byte	'reg',0,1,226,1,53
	.word	.L192,.L222
	.byte	5
	.word	.L89,.L220
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('imu963ra_connect_mag')
	.sect	'.debug_abbrev'
.L152:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('imu963ra_connect_mag')
	.sect	'.debug_line'
.L153:
	.word	.L590-.L589
.L589:
	.half	3
	.word	.L592-.L591
.L591:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_imu963ra.c',0,0,0,0,0
.L592:
	.byte	5,13,7,0,5,2
	.word	.L89
	.byte	3,225,1,1,5,17,9
	.half	.L285-.L89
	.byte	3,2,1,5,38,9
	.half	.L283-.L285
	.byte	3,2,1,5,62,9
	.half	.L593-.L283
	.byte	1,5,38,9
	.half	.L594-.L593
	.byte	3,1,1,5,60,9
	.half	.L595-.L594
	.byte	1,5,38,9
	.half	.L287-.L595
	.byte	3,1,1,5,60,9
	.half	.L596-.L287
	.byte	1,5,38,9
	.half	.L597-.L596
	.byte	3,1,1,5,62,9
	.half	.L598-.L597
	.byte	1,5,1,9
	.half	.L599-.L598
	.byte	3,1,1,7,9
	.half	.L155-.L599
	.byte	0,1,1
.L590:
	.sdecl	'.debug_ranges',debug,cluster('imu963ra_connect_mag')
	.sect	'.debug_ranges'
.L154:
	.word	-1,.L89,0,.L155-.L89,0,0
	.sdecl	'.debug_info',debug,cluster('imu963ra_acc_gyro_self_check')
	.sect	'.debug_info'
.L156:
	.word	345
	.half	3
	.word	.L157
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_imu963ra.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L159,.L158
	.byte	2
	.word	.L102
	.byte	3
	.byte	'imu963ra_acc_gyro_self_check',0,1,244,1,14
	.word	.L192
	.byte	1,1
	.word	.L91,.L223,.L90
	.byte	4
	.word	.L91,.L223
	.byte	5
	.byte	'return_state',0,1,246,1,11
	.word	.L192,.L224
	.byte	5
	.byte	'dat',0,1,247,1,11
	.word	.L192,.L225
	.byte	5
	.byte	'timeout_count',0,1,248,1,12
	.word	.L214,.L226
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('imu963ra_acc_gyro_self_check')
	.sect	'.debug_abbrev'
.L157:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('imu963ra_acc_gyro_self_check')
	.sect	'.debug_line'
.L158:
	.word	.L601-.L600
.L600:
	.half	3
	.word	.L603-.L602
.L602:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_imu963ra.c',0,0,0,0,0
.L603:
	.byte	5,24,7,0,5,2
	.word	.L91
	.byte	3,245,1,1,5,15,9
	.half	.L288-.L91
	.byte	3,1,1,5,26,9
	.half	.L289-.L288
	.byte	3,1,1,5,22,9
	.half	.L291-.L289
	.byte	3,2,1,5,51,9
	.half	.L26-.L291
	.byte	3,2,1,5,12,9
	.half	.L293-.L26
	.byte	1,5,9,9
	.half	.L604-.L293
	.byte	1,5,26,7,9
	.half	.L605-.L604
	.byte	3,2,1,5,13,9
	.half	.L606-.L605
	.byte	3,1,1,5,47,9
	.half	.L27-.L606
	.byte	3,2,1,5,13,9
	.half	.L290-.L27
	.byte	1,5,25,9
	.half	.L294-.L290
	.byte	3,1,1,5,11,9
	.half	.L25-.L294
	.byte	3,120,1,5,22,9
	.half	.L607-.L25
	.byte	1,5,5,7,9
	.half	.L28-.L607
	.byte	3,10,1,5,1,9
	.half	.L29-.L28
	.byte	3,1,1,7,9
	.half	.L160-.L29
	.byte	0,1,1
.L601:
	.sdecl	'.debug_ranges',debug,cluster('imu963ra_acc_gyro_self_check')
	.sect	'.debug_ranges'
.L159:
	.word	-1,.L91,0,.L160-.L91,0,0
	.sdecl	'.debug_info',debug,cluster('imu963ra_mag_self_check')
	.sect	'.debug_info'
.L161:
	.word	340
	.half	3
	.word	.L162
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_imu963ra.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L164,.L163
	.byte	2
	.word	.L102
	.byte	3
	.byte	'imu963ra_mag_self_check',0,1,142,2,14
	.word	.L192
	.byte	1,1
	.word	.L93,.L227,.L92
	.byte	4
	.word	.L93,.L227
	.byte	5
	.byte	'return_state',0,1,144,2,11
	.word	.L192,.L228
	.byte	5
	.byte	'dat',0,1,145,2,11
	.word	.L192,.L229
	.byte	5
	.byte	'timeout_count',0,1,146,2,12
	.word	.L214,.L230
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('imu963ra_mag_self_check')
	.sect	'.debug_abbrev'
.L162:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('imu963ra_mag_self_check')
	.sect	'.debug_line'
.L163:
	.word	.L609-.L608
.L608:
	.half	3
	.word	.L611-.L610
.L610:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_imu963ra.c',0,0,0,0,0
.L611:
	.byte	5,24,7,0,5,2
	.word	.L93
	.byte	3,143,2,1,5,15,9
	.half	.L296-.L93
	.byte	3,1,1,5,26,9
	.half	.L297-.L296
	.byte	3,1,1,5,22,9
	.half	.L299-.L297
	.byte	3,2,1,5,51,9
	.half	.L31-.L299
	.byte	3,2,1,5,12,9
	.half	.L301-.L31
	.byte	1,5,9,9
	.half	.L612-.L301
	.byte	1,5,26,7,9
	.half	.L613-.L612
	.byte	3,2,1,5,13,9
	.half	.L614-.L613
	.byte	3,1,1,5,42,9
	.half	.L32-.L614
	.byte	3,2,1,5,61,9
	.half	.L615-.L32
	.byte	1,5,13,9
	.half	.L298-.L615
	.byte	1,5,25,9
	.half	.L302-.L298
	.byte	3,1,1,5,11,9
	.half	.L30-.L302
	.byte	3,120,1,5,22,9
	.half	.L616-.L30
	.byte	1,5,5,7,9
	.half	.L33-.L616
	.byte	3,10,1,5,1,9
	.half	.L34-.L33
	.byte	3,1,1,7,9
	.half	.L165-.L34
	.byte	0,1,1
.L609:
	.sdecl	'.debug_ranges',debug,cluster('imu963ra_mag_self_check')
	.sect	'.debug_ranges'
.L164:
	.word	-1,.L93,0,.L165-.L93,0,0
	.sdecl	'.debug_info',debug,cluster('imu963ra_acc_x')
	.sect	'.debug_info'
.L166:
	.word	234
	.half	3
	.word	.L167
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_imu963ra.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L102
	.byte	3
	.byte	'imu963ra_acc_x',0,9,65,7
	.word	.L231
	.byte	1,5,3
	.word	imu963ra_acc_x
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('imu963ra_acc_x')
	.sect	'.debug_abbrev'
.L167:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('imu963ra_acc_y')
	.sect	'.debug_info'
.L168:
	.word	234
	.half	3
	.word	.L169
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_imu963ra.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L102
	.byte	3
	.byte	'imu963ra_acc_y',0,9,65,28
	.word	.L231
	.byte	1,5,3
	.word	imu963ra_acc_y
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('imu963ra_acc_y')
	.sect	'.debug_abbrev'
.L169:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('imu963ra_acc_z')
	.sect	'.debug_info'
.L170:
	.word	234
	.half	3
	.word	.L171
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_imu963ra.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L102
	.byte	3
	.byte	'imu963ra_acc_z',0,9,65,49
	.word	.L231
	.byte	1,5,3
	.word	imu963ra_acc_z
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('imu963ra_acc_z')
	.sect	'.debug_abbrev'
.L171:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('imu963ra_gyro_x')
	.sect	'.debug_info'
.L172:
	.word	235
	.half	3
	.word	.L173
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_imu963ra.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L102
	.byte	3
	.byte	'imu963ra_gyro_x',0,9,64,7
	.word	.L231
	.byte	1,5,3
	.word	imu963ra_gyro_x
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('imu963ra_gyro_x')
	.sect	'.debug_abbrev'
.L173:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('imu963ra_gyro_y')
	.sect	'.debug_info'
.L174:
	.word	235
	.half	3
	.word	.L175
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_imu963ra.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L102
	.byte	3
	.byte	'imu963ra_gyro_y',0,9,64,28
	.word	.L231
	.byte	1,5,3
	.word	imu963ra_gyro_y
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('imu963ra_gyro_y')
	.sect	'.debug_abbrev'
.L175:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('imu963ra_gyro_z')
	.sect	'.debug_info'
.L176:
	.word	235
	.half	3
	.word	.L177
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_imu963ra.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L102
	.byte	3
	.byte	'imu963ra_gyro_z',0,9,64,49
	.word	.L231
	.byte	1,5,3
	.word	imu963ra_gyro_z
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('imu963ra_gyro_z')
	.sect	'.debug_abbrev'
.L177:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('imu963ra_mag_x')
	.sect	'.debug_info'
.L178:
	.word	234
	.half	3
	.word	.L179
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_imu963ra.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L102
	.byte	3
	.byte	'imu963ra_mag_x',0,9,66,7
	.word	.L231
	.byte	1,5,3
	.word	imu963ra_mag_x
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('imu963ra_mag_x')
	.sect	'.debug_abbrev'
.L179:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('imu963ra_mag_y')
	.sect	'.debug_info'
.L180:
	.word	234
	.half	3
	.word	.L181
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_imu963ra.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L102
	.byte	3
	.byte	'imu963ra_mag_y',0,9,66,28
	.word	.L231
	.byte	1,5,3
	.word	imu963ra_mag_y
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('imu963ra_mag_y')
	.sect	'.debug_abbrev'
.L181:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('imu963ra_mag_z')
	.sect	'.debug_info'
.L182:
	.word	234
	.half	3
	.word	.L183
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_imu963ra.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L102
	.byte	3
	.byte	'imu963ra_mag_z',0,9,66,49
	.word	.L231
	.byte	1,5,3
	.word	imu963ra_mag_z
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('imu963ra_mag_z')
	.sect	'.debug_abbrev'
.L183:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('imu963ra_transition_factor')
	.sect	'.debug_info'
.L184:
	.word	246
	.half	3
	.word	.L185
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_imu963ra.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L102
	.byte	3
	.byte	'imu963ra_transition_factor',0,9,67,7
	.word	.L232
	.byte	1,5,3
	.word	imu963ra_transition_factor
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('imu963ra_transition_factor')
	.sect	'.debug_abbrev'
.L185:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_loc',debug,cluster('imu963ra_acc_gyro_self_check')
	.sect	'.debug_loc'
.L225:
	.word	-1,.L91,.L289-.L91,.L290-.L91
	.half	1
	.byte	90
	.word	.L290-.L91,.L25-.L91
	.half	1
	.byte	82
	.word	.L294-.L91,.L223-.L91
	.half	1
	.byte	90
	.word	0,0
.L90:
	.word	-1,.L91,0,.L223-.L91
	.half	2
	.byte	138,0
	.word	0,0
.L224:
	.word	-1,.L91,.L288-.L91,.L223-.L91
	.half	1
	.byte	88
	.word	.L295-.L91,.L223-.L91
	.half	1
	.byte	82
	.word	0,0
.L226:
	.word	-1,.L91,.L291-.L91,.L292-.L91
	.half	1
	.byte	89
	.word	.L293-.L91,.L223-.L91
	.half	1
	.byte	89
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('imu963ra_connect_mag')
	.sect	'.debug_loc'
.L221:
	.word	-1,.L89,0,.L283-.L89
	.half	1
	.byte	84
	.word	.L283-.L89,.L220-.L89
	.half	1
	.byte	95
	.word	0,0
.L88:
	.word	-1,.L89,0,.L220-.L89
	.half	2
	.byte	138,0
	.word	0,0
.L222:
	.word	-1,.L89,0,.L284-.L89
	.half	1
	.byte	85
	.word	.L285-.L89,.L220-.L89
	.half	1
	.byte	88
	.word	.L286-.L89,.L287-.L89
	.half	1
	.byte	85
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('imu963ra_get_acc')
	.sect	'.debug_loc'
.L188:
	.word	-1,.L95,0,.L186-.L95
	.half	2
	.byte	145,120
	.word	0,0
.L94:
	.word	-1,.L95,0,.L304-.L95
	.half	2
	.byte	138,0
	.word	.L304-.L95,.L186-.L95
	.half	2
	.byte	138,8
	.word	.L186-.L95,.L186-.L95
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('imu963ra_get_gyro')
	.sect	'.debug_loc'
.L190:
	.word	-1,.L97,0,.L189-.L97
	.half	2
	.byte	145,120
	.word	0,0
.L96:
	.word	-1,.L97,0,.L305-.L97
	.half	2
	.byte	138,0
	.word	.L305-.L97,.L189-.L97
	.half	2
	.byte	138,8
	.word	.L189-.L97,.L189-.L97
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('imu963ra_get_mag')
	.sect	'.debug_loc'
.L194:
	.word	-1,.L99,0,.L191-.L99
	.half	2
	.byte	145,120
	.word	0,0
.L98:
	.word	-1,.L99,0,.L306-.L99
	.half	2
	.byte	138,0
	.word	.L306-.L99,.L191-.L99
	.half	2
	.byte	138,8
	.word	.L191-.L99,.L191-.L99
	.half	2
	.byte	138,0
	.word	0,0
.L193:
	.word	-1,.L99,.L307-.L99,.L308-.L99
	.half	1
	.byte	82
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('imu963ra_init')
	.sect	'.debug_loc'
.L100:
	.word	-1,.L101,0,.L309-.L101
	.half	2
	.byte	138,0
	.word	.L309-.L101,.L195-.L101
	.half	2
	.byte	138,16
	.word	.L195-.L101,.L195-.L101
	.half	2
	.byte	138,0
	.word	0,0
.L196:
	.word	-1,.L101,.L310-.L101,.L195-.L101
	.half	1
	.byte	88
	.word	.L311-.L101,.L195-.L101
	.half	1
	.byte	82
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('imu963ra_mag_self_check')
	.sect	'.debug_loc'
.L229:
	.word	-1,.L93,.L297-.L93,.L298-.L93
	.half	1
	.byte	90
	.word	.L298-.L93,.L30-.L93
	.half	1
	.byte	82
	.word	.L302-.L93,.L227-.L93
	.half	1
	.byte	90
	.word	0,0
.L92:
	.word	-1,.L93,0,.L227-.L93
	.half	2
	.byte	138,0
	.word	0,0
.L228:
	.word	-1,.L93,.L296-.L93,.L227-.L93
	.half	1
	.byte	88
	.word	.L303-.L93,.L227-.L93
	.half	1
	.byte	82
	.word	0,0
.L230:
	.word	-1,.L93,.L299-.L93,.L300-.L93
	.half	1
	.byte	89
	.word	.L301-.L93,.L227-.L93
	.half	1
	.byte	89
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('imu963ra_read_acc_gyro_register')
	.sect	'.debug_loc'
.L202:
	.word	-1,.L81,.L245-.L81,.L246-.L81
	.half	1
	.byte	82
	.word	.L243-.L81,.L200-.L81
	.half	1
	.byte	88
	.word	.L8-.L81,.L247-.L81
	.half	1
	.byte	82
	.word	.L248-.L81,.L200-.L81
	.half	1
	.byte	82
	.word	0,0
.L80:
	.word	-1,.L81,0,.L200-.L81
	.half	2
	.byte	138,0
	.word	0,0
.L201:
	.word	-1,.L81,0,.L241-.L81
	.half	1
	.byte	84
	.word	.L242-.L81,.L243-.L81
	.half	1
	.byte	88
	.word	.L6-.L81,.L244-.L81
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('imu963ra_read_acc_gyro_registers')
	.sect	'.debug_loc'
.L206:
	.word	-1,.L83,0,.L249-.L83
	.half	1
	.byte	100
	.word	.L252-.L83,.L203-.L83
	.half	1
	.byte	111
	.word	.L11-.L83,.L254-.L83
	.half	1
	.byte	100
	.word	.L256-.L83,.L257-.L83
	.half	1
	.byte	100
	.word	0,0
.L82:
	.word	-1,.L83,0,.L203-.L83
	.half	2
	.byte	138,0
	.word	0,0
.L208:
	.word	-1,.L83,0,.L249-.L83
	.half	1
	.byte	85
	.word	.L253-.L83,.L203-.L83
	.half	1
	.byte	89
	.word	.L11-.L83,.L254-.L83
	.half	1
	.byte	85
	.word	.L258-.L83,.L257-.L83
	.half	1
	.byte	86
	.word	0,0
.L204:
	.word	-1,.L83,0,.L250-.L83
	.half	1
	.byte	84
	.word	.L251-.L83,.L203-.L83
	.half	1
	.byte	88
	.word	.L11-.L83,.L255-.L83
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('imu963ra_read_mag_register')
	.sect	'.debug_loc'
.L217:
	.word	-1,.L87,0,.L275-.L87
	.half	1
	.byte	84
	.word	.L275-.L87,.L21-.L87
	.half	1
	.byte	95
	.word	0,0
.L86:
	.word	-1,.L87,0,.L216-.L87
	.half	2
	.byte	138,0
	.word	0,0
.L218:
	.word	-1,.L87,0,.L276-.L87
	.half	1
	.byte	85
	.word	.L277-.L87,.L216-.L87
	.half	1
	.byte	89
	.word	.L280-.L87,.L281-.L87
	.half	1
	.byte	85
	.word	0,0
.L219:
	.word	-1,.L87,.L278-.L87,.L279-.L87
	.half	1
	.byte	88
	.word	.L282-.L87,.L216-.L87
	.half	1
	.byte	88
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('imu963ra_write_acc_gyro_register')
	.sect	'.debug_loc'
.L199:
	.word	-1,.L79,0,.L233-.L79
	.half	1
	.byte	85
	.word	.L2-.L79,.L235-.L79
	.half	1
	.byte	85
	.word	.L238-.L79,.L239-.L79
	.half	1
	.byte	89
	.word	.L239-.L79,.L240-.L79
	.half	1
	.byte	86
	.word	0,0
.L78:
	.word	-1,.L79,0,.L197-.L79
	.half	2
	.byte	138,0
	.word	0,0
.L198:
	.word	-1,.L79,0,.L234-.L79
	.half	1
	.byte	84
	.word	.L2-.L79,.L236-.L79
	.half	1
	.byte	84
	.word	.L237-.L79,.L238-.L79
	.half	1
	.byte	88
	.word	.L238-.L79,.L240-.L79
	.half	1
	.byte	85
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('imu963ra_write_mag_register')
	.sect	'.debug_loc'
.L210:
	.word	-1,.L85,0,.L259-.L85
	.half	1
	.byte	84
	.word	.L259-.L85,.L209-.L85
	.half	1
	.byte	91
	.word	.L267-.L85,.L268-.L85
	.half	1
	.byte	85
	.word	0,0
.L212:
	.word	-1,.L85,0,.L260-.L85
	.half	1
	.byte	86
	.word	.L263-.L85,.L209-.L85
	.half	1
	.byte	88
	.word	.L271-.L85,.L272-.L85
	.half	1
	.byte	85
	.word	0,0
.L84:
	.word	-1,.L85,0,.L209-.L85
	.half	2
	.byte	138,0
	.word	0,0
.L211:
	.word	-1,.L85,0,.L261-.L85
	.half	1
	.byte	85
	.word	.L262-.L85,.L16-.L85
	.half	1
	.byte	95
	.word	.L269-.L85,.L270-.L85
	.half	1
	.byte	85
	.word	0,0
.L213:
	.word	-1,.L85,.L264-.L85,.L209-.L85
	.half	1
	.byte	90
	.word	.L274-.L85,.L209-.L85
	.half	1
	.byte	82
	.word	0,0
.L215:
	.word	-1,.L85,.L265-.L85,.L266-.L85
	.half	1
	.byte	89
	.word	.L273-.L85,.L209-.L85
	.half	1
	.byte	89
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L617:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('imu963ra_write_acc_gyro_register')
	.sect	'.debug_frame'
	.word	12
	.word	.L617,.L79,.L197-.L79
	.sdecl	'.debug_frame',debug,cluster('imu963ra_read_acc_gyro_register')
	.sect	'.debug_frame'
	.word	12
	.word	.L617,.L81,.L200-.L81
	.sdecl	'.debug_frame',debug,cluster('imu963ra_read_acc_gyro_registers')
	.sect	'.debug_frame'
	.word	12
	.word	.L617,.L83,.L203-.L83
	.sdecl	'.debug_frame',debug,cluster('imu963ra_write_mag_register')
	.sect	'.debug_frame'
	.word	12
	.word	.L617,.L85,.L209-.L85
	.sdecl	'.debug_frame',debug,cluster('imu963ra_read_mag_register')
	.sect	'.debug_frame'
	.word	12
	.word	.L617,.L87,.L216-.L87
	.sdecl	'.debug_frame',debug,cluster('imu963ra_connect_mag')
	.sect	'.debug_frame'
	.word	12
	.word	.L617,.L89,.L220-.L89
	.sdecl	'.debug_frame',debug,cluster('imu963ra_acc_gyro_self_check')
	.sect	'.debug_frame'
	.word	12
	.word	.L617,.L91,.L223-.L91
	.sdecl	'.debug_frame',debug,cluster('imu963ra_mag_self_check')
	.sect	'.debug_frame'
	.word	12
	.word	.L617,.L93,.L227-.L93
	.sdecl	'.debug_frame',debug,cluster('imu963ra_get_acc')
	.sect	'.debug_frame'
	.word	36
	.word	.L617,.L95,.L186-.L95
	.byte	4
	.word	(.L304-.L95)/2
	.byte	19,8,22,26,3,19,138,8,4
	.word	(.L186-.L304)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('imu963ra_get_gyro')
	.sect	'.debug_frame'
	.word	36
	.word	.L617,.L97,.L189-.L97
	.byte	4
	.word	(.L305-.L97)/2
	.byte	19,8,22,26,3,19,138,8,4
	.word	(.L189-.L305)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('imu963ra_get_mag')
	.sect	'.debug_frame'
	.word	36
	.word	.L617,.L99,.L191-.L99
	.byte	4
	.word	(.L306-.L99)/2
	.byte	19,8,22,26,3,19,138,8,4
	.word	(.L191-.L306)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('imu963ra_init')
	.sect	'.debug_frame'
	.word	36
	.word	.L617,.L101,.L195-.L101
	.byte	4
	.word	(.L309-.L101)/2
	.byte	19,16,22,26,3,19,138,16,4
	.word	(.L195-.L309)/2
	.byte	19,0,8,26,0,0
	; Module end
