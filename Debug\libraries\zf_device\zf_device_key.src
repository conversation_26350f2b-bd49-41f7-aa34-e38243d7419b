	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc44144a --dep-file=zf_device_key.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/zf_device/zf_device_key.src ../libraries/zf_device/zf_device_key.c"
	.compiler_name		"ctc"
	;source	'../libraries/zf_device/zf_device_key.c'

	
$TC16X
	
	.sdecl	'.text.zf_device_key.key_scanner',code,cluster('key_scanner')
	.sect	'.text.zf_device_key.key_scanner'
	.align	2
	
	.global	key_scanner
; Function key_scanner
.L14:
key_scanner:	.type	func
	mov	d8,#0
.L78:
	j	.L2
.L3:
	mul	d15,d8,#2
.L90:
	movh.a	a15,#@his(key_index)
	lea	a15,[a15]@los(key_index)
.L91:
	addsc.a	a15,a15,d15,#0
	ld.h	d4,[a15]
	call	gpio_get_level
.L92:
	jeq	d2,#1,.L4
.L93:
	mul	d15,d8,#4
.L94:
	movh.a	a15,#@his(key_press_time)
	lea	a15,[a15]@los(key_press_time)
.L95:
	addsc.a	a15,a15,d15,#0
	mul	d15,d8,#4
.L96:
	movh.a	a2,#@his(key_press_time)
	lea	a2,[a2]@los(key_press_time)
.L97:
	addsc.a	a2,a2,d15,#0
	ld.w	d0,[a2]
.L98:
	add	d0,#1
	st.w	[a15],d0
.L99:
	mov	d0,#1000
.L100:
	movh.a	a15,#@his(scanner_period)
	lea	a15,[a15]@los(scanner_period)
	ld.w	d15,[a15]
.L101:
	div.u	e0,d0,d15
.L102:
	mul	d15,d8,#4
.L103:
	movh.a	a15,#@his(key_press_time)
	lea	a15,[a15]@los(key_press_time)
.L104:
	addsc.a	a15,a15,d15,#0
	ld.w	d15,[a15]
.L105:
	jlt.u	d15,d0,.L5
.L106:
	movh.a	a15,#@his(key_state)
	lea	a15,[a15]@los(key_state)
.L107:
	addsc.a	a15,a15,d8,#0
.L108:
	mov	d15,#2
.L109:
	st.b	[a15],d15
.L5:
	j	.L6
.L4:
	movh.a	a15,#@his(key_state)
	lea	a15,[a15]@los(key_state)
.L110:
	addsc.a	a15,a15,d8,#0
	ld.bu	d15,[a15]
.L111:
	jeq	d15,#2,.L7
.L112:
	mov	d15,#10
.L113:
	movh.a	a15,#@his(scanner_period)
	lea	a15,[a15]@los(scanner_period)
	ld.w	d0,[a15]
.L114:
	div.u	e0,d15,d0
.L115:
	mul	d15,d8,#4
.L116:
	movh.a	a15,#@his(key_press_time)
	lea	a15,[a15]@los(key_press_time)
.L117:
	addsc.a	a15,a15,d15,#0
	ld.w	d15,[a15]
.L118:
	jlt.u	d15,d0,.L8
.L119:
	movh.a	a15,#@his(key_state)
	lea	a15,[a15]@los(key_state)
.L120:
	addsc.a	a15,a15,d8,#0
.L121:
	mov	d15,#1
.L122:
	st.b	[a15],d15
.L123:
	j	.L9
.L8:
.L7:
	movh.a	a15,#@his(key_state)
	lea	a15,[a15]@los(key_state)
.L124:
	addsc.a	a15,a15,d8,#0
.L125:
	mov	d15,#0
.L126:
	st.b	[a15],d15
.L9:
	mul	d15,d8,#4
.L127:
	movh.a	a15,#@his(key_press_time)
	lea	a15,[a15]@los(key_press_time)
.L128:
	addsc.a	a15,a15,d15,#0
.L129:
	mov	d15,#0
.L130:
	st.w	[a15],d15
.L6:
	add	d8,#1
.L2:
	jlt.u	d8,#4,.L3
.L131:
	ret
.L60:
	
__key_scanner_function_end:
	.size	key_scanner,__key_scanner_function_end-key_scanner
.L31:
	; End of function
	
	.sdecl	'.text.zf_device_key.key_get_state',code,cluster('key_get_state')
	.sect	'.text.zf_device_key.key_get_state'
	.align	2
	
	.global	key_get_state
; Function key_get_state
.L16:
key_get_state:	.type	func
	movh.a	a15,#@his(key_state)
	lea	a15,[a15]@los(key_state)
.L136:
	addsc.a	a15,a15,d4,#0
	ld.bu	d2,[a15]
.L137:
	j	.L10
.L10:
	ret
.L64:
	
__key_get_state_function_end:
	.size	key_get_state,__key_get_state_function_end-key_get_state
.L36:
	; End of function
	
	.sdecl	'.text.zf_device_key.key_clear_state',code,cluster('key_clear_state')
	.sect	'.text.zf_device_key.key_clear_state'
	.align	2
	
	.global	key_clear_state
; Function key_clear_state
.L18:
key_clear_state:	.type	func
	movh.a	a15,#@his(key_state)
	lea	a15,[a15]@los(key_state)
.L142:
	addsc.a	a15,a15,d4,#0
.L143:
	mov	d15,#0
.L144:
	st.b	[a15],d15
.L145:
	ret
.L67:
	
__key_clear_state_function_end:
	.size	key_clear_state,__key_clear_state_function_end-key_clear_state
.L41:
	; End of function
	
	.sdecl	'.text.zf_device_key.key_clear_all_state',code,cluster('key_clear_all_state')
	.sect	'.text.zf_device_key.key_clear_all_state'
	.align	2
	
	.global	key_clear_all_state
; Function key_clear_all_state
.L20:
key_clear_all_state:	.type	func
	movh.a	a15,#@his(key_state)
	lea	a15,[a15]@los(key_state)
.L150:
	mov	d15,#0
.L151:
	st.b	[a15],d15
.L152:
	movh.a	a15,#@his(key_state)
	lea	a15,[a15]@los(key_state)
.L153:
	mov	d15,#0
.L154:
	st.b	[a15]1,d15
.L155:
	movh.a	a15,#@his(key_state)
	lea	a15,[a15]@los(key_state)
.L156:
	mov	d15,#0
.L157:
	st.b	[a15]2,d15
.L158:
	movh.a	a15,#@his(key_state)
	lea	a15,[a15]@los(key_state)
.L159:
	mov	d15,#0
.L160:
	st.b	[a15]3,d15
.L161:
	ret
.L69:
	
__key_clear_all_state_function_end:
	.size	key_clear_all_state,__key_clear_all_state_function_end-key_clear_all_state
.L46:
	; End of function
	
	.sdecl	'.text.zf_device_key.key_init',code,cluster('key_init')
	.sect	'.text.zf_device_key.key_init'
	.align	2
	
	.global	key_init
; Function key_init
.L22:
key_init:	.type	func
	mov	d8,d4
.L80:
	mov	d15,#0
	lt.u	d4,d15,d8
.L79:
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#141
	call	debug_assert_handler
.L73:
	mov	d9,#0
.L81:
	j	.L11
.L12:
	mul	d15,d9,#2
.L166:
	movh.a	a15,#@his(key_index)
	lea	a15,[a15]@los(key_index)
.L167:
	addsc.a	a15,a15,d15,#0
	ld.h	d4,[a15]
.L168:
	mov	d5,#0
.L169:
	mov	d6,#1
.L170:
	mov	d7,#1
	call	gpio_init
.L171:
	movh.a	a15,#@his(key_state)
	lea	a15,[a15]@los(key_state)
.L172:
	addsc.a	a15,a15,d9,#0
.L173:
	mov	d15,#0
.L174:
	st.b	[a15],d15
.L175:
	add	d9,#1
.L11:
	jlt.u	d9,#4,.L12
.L176:
	movh.a	a15,#@his(scanner_period)
	lea	a15,[a15]@los(scanner_period)
.L177:
	st.w	[a15],d8
.L178:
	ret
.L70:
	
__key_init_function_end:
	.size	key_init,__key_init_function_end-key_init
.L51:
	; End of function
	
	.sdecl	'.data.zf_device_key.scanner_period',data,cluster('scanner_period')
	.sect	'.data.zf_device_key.scanner_period'
	.align	2
scanner_period:	.type	object
	.size	scanner_period,4
	.space	4
	.sdecl	'.bss.zf_device_key.key_press_time',data,cluster('key_press_time')
	.sect	'.bss.zf_device_key.key_press_time'
	.align	2
key_press_time:	.type	object
	.size	key_press_time,16
	.space	16
	.sdecl	'.bss.zf_device_key.key_state',data,cluster('key_state')
	.sect	'.bss.zf_device_key.key_state'
key_state:	.type	object
	.size	key_state,4
	.space	4
	.sdecl	'.rodata.zf_device_key.key_index',data,rom,cluster('key_index')
	.sect	'.rodata.zf_device_key.key_index'
	.align	2
key_index:	.type	object
	.size	key_index,8
	.half	646,647,354,355
	.sdecl	'.rodata.zf_device_key..1.str',data,rom
	.sect	'.rodata.zf_device_key..1.str'
.1.str:	.type	object
	.size	.1.str,39
	.byte	46,46,47,108,105,98,114,97
	.byte	114,105,101,115,47,122,102,95
	.byte	100,101,118,105,99,101,47,122
	.byte	102,95,100,101,118,105,99,101
	.byte	95,107,101,121
	.byte	46,99
	.space	1
	.calls	'key_scanner','gpio_get_level'
	.calls	'key_init','debug_assert_handler'
	.calls	'key_init','gpio_init'
	.calls	'key_scanner','',0
	.calls	'key_get_state','',0
	.calls	'key_clear_state','',0
	.calls	'key_clear_all_state','',0
	.extern	debug_assert_handler
	.extern	gpio_get_level
	.extern	gpio_init
	.calls	'key_init','',0
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L24:
	.word	38743
	.half	3
	.word	.L25
	.byte	4
.L23:
	.byte	1
	.byte	'../libraries/zf_device/zf_device_key.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L26
	.byte	2,1,1,3
	.word	201
	.byte	4
	.byte	'Ifx__jump_and_link',0,3,1,61,17,1,1,5
	.byte	'fun',0,1,61,43
	.word	204
	.byte	6,0,7
	.byte	'__fract',0,4,128,1,7
	.byte	'float',0,4,4,8
	.byte	'Ifx__float_to_fract',0,3,1,152,2,18
	.word	249
	.byte	1,1,5
	.byte	'a',0,1,152,2,44
	.word	261
	.byte	6,0,7
	.byte	'unsigned long long int',0,8,7,9
	.byte	'void',0,3
	.word	341
	.byte	8
	.byte	'__ld64',0,3,2,135,1,19
	.word	315
	.byte	1,1,5
	.byte	'addr',0,2,135,1,32
	.word	347
	.byte	6,0,4
	.byte	'__st64',0,3,2,143,1,17,1,1,5
	.byte	'addr',0,2,143,1,30
	.word	347
	.byte	5
	.byte	'value',0,2,143,1,43
	.word	315
	.byte	6,0,7
	.byte	'unsigned int',0,4,7,7
	.byte	'int',0,4,5
.L61:
	.byte	7
	.byte	'unsigned char',0,1,8,7
	.byte	'unsigned short int',0,2,7,10
	.byte	'_Ifx_P_OUT_Bits',0,4,143,3,16,4,11
	.byte	'P0',0,1
	.word	456
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	456
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	456
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	456
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	456
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	456
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	456
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	456
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	456
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	456
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	456
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	456
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	456
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	456
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	456
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	456
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	473
	.byte	16,0,2,35,2,0,12,4,181,5,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	495
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMR_Bits',0,4,169,2,16,4,11
	.byte	'PS0',0,1
	.word	456
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	456
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	456
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	456
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	456
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	456
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	456
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	456
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	456
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	456
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	456
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	456
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	456
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	456
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	456
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	456
	.byte	1,0,2,35,1,11
	.byte	'PCL0',0,1
	.word	456
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	456
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	456
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	456
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	456
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	456
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	456
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	456
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	456
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	456
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	456
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	456
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	456
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	456
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	456
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	456
	.byte	1,0,2,35,3,0,12,4,133,5,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	811
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ID_Bits',0,4,110,16,4,11
	.byte	'MODREV',0,1
	.word	456
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	456
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	473
	.byte	16,0,2,35,2,0,12,4,148,4,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1382
	.byte	4,2,35,0,0,14,4
	.word	456
	.byte	15,3,0,10
	.byte	'_Ifx_P_IOCR0_Bits',0,4,140,1,16,4,11
	.byte	'reserved_0',0,1
	.word	456
	.byte	3,5,2,35,0,11
	.byte	'PC0',0,1
	.word	456
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	456
	.byte	3,5,2,35,1,11
	.byte	'PC1',0,1
	.word	456
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	456
	.byte	3,5,2,35,2,11
	.byte	'PC2',0,1
	.word	456
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	456
	.byte	3,5,2,35,3,11
	.byte	'PC3',0,1
	.word	456
	.byte	5,0,2,35,3,0,12,4,164,4,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1510
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR4_Bits',0,4,166,1,16,4,11
	.byte	'reserved_0',0,1
	.word	456
	.byte	3,5,2,35,0,11
	.byte	'PC4',0,1
	.word	456
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	456
	.byte	3,5,2,35,1,11
	.byte	'PC5',0,1
	.word	456
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	456
	.byte	3,5,2,35,2,11
	.byte	'PC6',0,1
	.word	456
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	456
	.byte	3,5,2,35,3,11
	.byte	'PC7',0,1
	.word	456
	.byte	5,0,2,35,3,0,12,4,180,4,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1725
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR8_Bits',0,4,179,1,16,4,11
	.byte	'reserved_0',0,1
	.word	456
	.byte	3,5,2,35,0,11
	.byte	'PC8',0,1
	.word	456
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	456
	.byte	3,5,2,35,1,11
	.byte	'PC9',0,1
	.word	456
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	456
	.byte	3,5,2,35,2,11
	.byte	'PC10',0,1
	.word	456
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	456
	.byte	3,5,2,35,3,11
	.byte	'PC11',0,1
	.word	456
	.byte	5,0,2,35,3,0,12,4,188,4,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1940
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR12_Bits',0,4,153,1,16,4,11
	.byte	'reserved_0',0,1
	.word	456
	.byte	3,5,2,35,0,11
	.byte	'PC12',0,1
	.word	456
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	456
	.byte	3,5,2,35,1,11
	.byte	'PC13',0,1
	.word	456
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	456
	.byte	3,5,2,35,2,11
	.byte	'PC14',0,1
	.word	456
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	456
	.byte	3,5,2,35,3,11
	.byte	'PC15',0,1
	.word	456
	.byte	5,0,2,35,3,0,12,4,172,4,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2157
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IN_Bits',0,4,118,16,4,11
	.byte	'P0',0,1
	.word	456
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	456
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	456
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	456
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	456
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	456
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	456
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	456
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	456
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	456
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	456
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	456
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	456
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	456
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	456
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	456
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	473
	.byte	16,0,2,35,2,0,12,4,156,4,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2377
	.byte	4,2,35,0,0,14,24
	.word	456
	.byte	15,23,0,10
	.byte	'_Ifx_P_PDR0_Bits',0,4,205,3,16,4,11
	.byte	'PD0',0,1
	.word	456
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	456
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	456
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	456
	.byte	1,0,2,35,0,11
	.byte	'PD2',0,1
	.word	456
	.byte	3,5,2,35,1,11
	.byte	'PL2',0,1
	.word	456
	.byte	1,4,2,35,1,11
	.byte	'PD3',0,1
	.word	456
	.byte	3,1,2,35,1,11
	.byte	'PL3',0,1
	.word	456
	.byte	1,0,2,35,1,11
	.byte	'PD4',0,1
	.word	456
	.byte	3,5,2,35,2,11
	.byte	'PL4',0,1
	.word	456
	.byte	1,4,2,35,2,11
	.byte	'PD5',0,1
	.word	456
	.byte	3,1,2,35,2,11
	.byte	'PL5',0,1
	.word	456
	.byte	1,0,2,35,2,11
	.byte	'PD6',0,1
	.word	456
	.byte	3,5,2,35,3,11
	.byte	'PL6',0,1
	.word	456
	.byte	1,4,2,35,3,11
	.byte	'PD7',0,1
	.word	456
	.byte	3,1,2,35,3,11
	.byte	'PL7',0,1
	.word	456
	.byte	1,0,2,35,3,0,12,4,205,5,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2700
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PDR1_Bits',0,4,226,3,16,4,11
	.byte	'PD8',0,1
	.word	456
	.byte	3,5,2,35,0,11
	.byte	'PL8',0,1
	.word	456
	.byte	1,4,2,35,0,11
	.byte	'PD9',0,1
	.word	456
	.byte	3,1,2,35,0,11
	.byte	'PL9',0,1
	.word	456
	.byte	1,0,2,35,0,11
	.byte	'PD10',0,1
	.word	456
	.byte	3,5,2,35,1,11
	.byte	'PL10',0,1
	.word	456
	.byte	1,4,2,35,1,11
	.byte	'PD11',0,1
	.word	456
	.byte	3,1,2,35,1,11
	.byte	'PL11',0,1
	.word	456
	.byte	1,0,2,35,1,11
	.byte	'PD12',0,1
	.word	456
	.byte	3,5,2,35,2,11
	.byte	'PL12',0,1
	.word	456
	.byte	1,4,2,35,2,11
	.byte	'PD13',0,1
	.word	456
	.byte	3,1,2,35,2,11
	.byte	'PL13',0,1
	.word	456
	.byte	1,0,2,35,2,11
	.byte	'PD14',0,1
	.word	456
	.byte	3,5,2,35,3,11
	.byte	'PL14',0,1
	.word	456
	.byte	1,4,2,35,3,11
	.byte	'PD15',0,1
	.word	456
	.byte	3,1,2,35,3,11
	.byte	'PL15',0,1
	.word	456
	.byte	1,0,2,35,3,0,12,4,213,5,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3004
	.byte	4,2,35,0,0,14,8
	.word	456
	.byte	15,7,0,10
	.byte	'_Ifx_P_ESR_Bits',0,4,88,16,4,11
	.byte	'EN0',0,1
	.word	456
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	456
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	456
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	456
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	456
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	456
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	456
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	456
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	456
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	456
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	456
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	456
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	456
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	456
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	456
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	456
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	473
	.byte	16,0,2,35,2,0,12,4,140,4,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3329
	.byte	4,2,35,0,0,14,12
	.word	456
	.byte	15,11,0,10
	.byte	'_Ifx_P_PDISC_Bits',0,4,183,3,16,4,11
	.byte	'PDIS0',0,1
	.word	456
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	456
	.byte	1,6,2,35,0,11
	.byte	'PDIS2',0,1
	.word	456
	.byte	1,5,2,35,0,11
	.byte	'PDIS3',0,1
	.word	456
	.byte	1,4,2,35,0,11
	.byte	'PDIS4',0,1
	.word	456
	.byte	1,3,2,35,0,11
	.byte	'PDIS5',0,1
	.word	456
	.byte	1,2,2,35,0,11
	.byte	'PDIS6',0,1
	.word	456
	.byte	1,1,2,35,0,11
	.byte	'PDIS7',0,1
	.word	456
	.byte	1,0,2,35,0,11
	.byte	'PDIS8',0,1
	.word	456
	.byte	1,7,2,35,1,11
	.byte	'PDIS9',0,1
	.word	456
	.byte	1,6,2,35,1,11
	.byte	'PDIS10',0,1
	.word	456
	.byte	1,5,2,35,1,11
	.byte	'PDIS11',0,1
	.word	456
	.byte	1,4,2,35,1,11
	.byte	'PDIS12',0,1
	.word	456
	.byte	1,3,2,35,1,11
	.byte	'PDIS13',0,1
	.word	456
	.byte	1,2,2,35,1,11
	.byte	'PDIS14',0,1
	.word	456
	.byte	1,1,2,35,1,11
	.byte	'PDIS15',0,1
	.word	456
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	473
	.byte	16,0,2,35,2,0,12,4,197,5,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3669
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PCSR_Bits',0,4,165,3,16,4,11
	.byte	'SEL0',0,1
	.word	456
	.byte	1,7,2,35,0,11
	.byte	'SEL1',0,1
	.word	456
	.byte	1,6,2,35,0,11
	.byte	'SEL2',0,1
	.word	456
	.byte	1,5,2,35,0,11
	.byte	'SEL3',0,1
	.word	456
	.byte	1,4,2,35,0,11
	.byte	'SEL4',0,1
	.word	456
	.byte	1,3,2,35,0,11
	.byte	'SEL5',0,1
	.word	456
	.byte	1,2,2,35,0,11
	.byte	'SEL6',0,1
	.word	456
	.byte	1,1,2,35,0,11
	.byte	'SEL7',0,1
	.word	456
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	456
	.byte	2,6,2,35,1,11
	.byte	'SEL10',0,1
	.word	456
	.byte	1,5,2,35,1,11
	.byte	'SEL11',0,1
	.word	456
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	433
	.byte	19,1,2,35,0,11
	.byte	'LCK',0,1
	.word	456
	.byte	1,0,2,35,3,0,12,4,189,5,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4035
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR0_Bits',0,4,206,2,16,4,11
	.byte	'PS0',0,1
	.word	456
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	456
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	456
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	456
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	433
	.byte	28,0,2,35,0,0,12,4,149,5,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4321
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR4_Bits',0,4,227,2,16,4,11
	.byte	'reserved_0',0,1
	.word	456
	.byte	4,4,2,35,0,11
	.byte	'PS4',0,1
	.word	456
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	456
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	456
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	456
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	433
	.byte	24,0,2,35,0,0,12,4,165,5,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4468
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR8_Bits',0,4,238,2,16,4,11
	.byte	'reserved_0',0,1
	.word	456
	.byte	8,0,2,35,0,11
	.byte	'PS8',0,1
	.word	456
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	456
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	456
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	456
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	433
	.byte	20,0,2,35,0,0,12,4,173,5,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4637
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR12_Bits',0,4,216,2,16,4,11
	.byte	'reserved_0',0,2
	.word	473
	.byte	12,4,2,35,0,11
	.byte	'PS12',0,1
	.word	456
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	456
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	456
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	456
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	473
	.byte	16,0,2,35,2,0,12,4,157,5,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4809
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR0_Bits',0,4,232,1,16,4,11
	.byte	'reserved_0',0,2
	.word	473
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	456
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	456
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	456
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	456
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	473
	.byte	12,0,2,35,2,0,12,4,229,4,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4984
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR4_Bits',0,4,253,1,16,4,11
	.byte	'reserved_0',0,4
	.word	433
	.byte	20,12,2,35,0,11
	.byte	'PCL4',0,1
	.word	456
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	456
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	456
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	456
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	456
	.byte	8,0,2,35,3,0,12,4,245,4,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5158
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR8_Bits',0,4,136,2,16,4,11
	.byte	'reserved_0',0,4
	.word	433
	.byte	24,8,2,35,0,11
	.byte	'PCL8',0,1
	.word	456
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	456
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	456
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	456
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	456
	.byte	4,0,2,35,3,0,12,4,253,4,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5332
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR12_Bits',0,4,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	433
	.byte	28,4,2,35,0,11
	.byte	'PCL12',0,1
	.word	456
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	456
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	456
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	456
	.byte	1,0,2,35,3,0,12,4,237,4,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5508
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR_Bits',0,4,249,2,16,4,11
	.byte	'PS0',0,1
	.word	456
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	456
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	456
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	456
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	456
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	456
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	456
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	456
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	456
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	456
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	456
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	456
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	456
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	456
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	456
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	456
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	473
	.byte	16,0,2,35,2,0,12,4,141,5,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5664
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR_Bits',0,4,147,2,16,4,11
	.byte	'reserved_0',0,2
	.word	473
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	456
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	456
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	456
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	456
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	456
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	456
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	456
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	456
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	456
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	456
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	456
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	456
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	456
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	456
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	456
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	456
	.byte	1,0,2,35,3,0,12,4,221,4,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5997
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR0_Bits',0,4,192,1,16,4,11
	.byte	'reserved_0',0,1
	.word	456
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	456
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	433
	.byte	30,0,2,35,0,0,12,4,196,4,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6345
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_Bits',0,4,200,1,16,4,11
	.byte	'reserved_0',0,1
	.word	456
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	456
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	433
	.byte	30,0,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_P21_Bits',0,4,208,1,16,4,11
	.byte	'RDIS_CTRL',0,1
	.word	456
	.byte	1,7,2,35,0,11
	.byte	'RX_DIS',0,1
	.word	456
	.byte	1,6,2,35,0,11
	.byte	'TERM',0,1
	.word	456
	.byte	1,5,2,35,0,11
	.byte	'LRXTERM',0,1
	.word	456
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	433
	.byte	24,0,2,35,0,0,12,4,204,4,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6469
	.byte	4,2,35,0,13
	.byte	'B_P21',0
	.word	6553
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR2_Bits',0,4,218,1,16,4,11
	.byte	'reserved_0',0,1
	.word	456
	.byte	8,0,2,35,0,11
	.byte	'LVDSR',0,1
	.word	456
	.byte	1,7,2,35,1,11
	.byte	'LVDSRL',0,1
	.word	456
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	456
	.byte	2,4,2,35,1,11
	.byte	'TDIS_CTRL',0,1
	.word	456
	.byte	1,3,2,35,1,11
	.byte	'TX_DIS',0,1
	.word	456
	.byte	1,2,2,35,1,11
	.byte	'TX_PD',0,1
	.word	456
	.byte	1,1,2,35,1,11
	.byte	'TX_PWDPD',0,1
	.word	456
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	473
	.byte	16,0,2,35,2,0,12,4,213,4,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6733
	.byte	4,2,35,0,0,14,76
	.word	456
	.byte	15,75,0,10
	.byte	'_Ifx_P_ACCEN1_Bits',0,4,82,16,4,11
	.byte	'reserved_0',0,4
	.word	433
	.byte	32,0,2,35,0,0,12,4,132,4,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6986
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ACCEN0_Bits',0,4,45,16,4,11
	.byte	'EN0',0,1
	.word	456
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	456
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	456
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	456
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	456
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	456
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	456
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	456
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	456
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	456
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	456
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	456
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	456
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	456
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	456
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	456
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	456
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	456
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	456
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	456
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	456
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	456
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	456
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	456
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	456
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	456
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	456
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	456
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	456
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	456
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	456
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	456
	.byte	1,0,2,35,3,0,12,4,252,3,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7073
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P',0,4,229,5,25,128,2,13
	.byte	'OUT',0
	.word	771
	.byte	4,2,35,0,13
	.byte	'OMR',0
	.word	1342
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	1461
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	1501
	.byte	4,2,35,12,13
	.byte	'IOCR0',0
	.word	1685
	.byte	4,2,35,16,13
	.byte	'IOCR4',0
	.word	1900
	.byte	4,2,35,20,13
	.byte	'IOCR8',0
	.word	2117
	.byte	4,2,35,24,13
	.byte	'IOCR12',0
	.word	2337
	.byte	4,2,35,28,13
	.byte	'reserved_20',0
	.word	1501
	.byte	4,2,35,32,13
	.byte	'IN',0
	.word	2651
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	2691
	.byte	24,2,35,40,13
	.byte	'PDR0',0
	.word	2964
	.byte	4,2,35,64,13
	.byte	'PDR1',0
	.word	3280
	.byte	4,2,35,68,13
	.byte	'reserved_48',0
	.word	3320
	.byte	8,2,35,72,13
	.byte	'ESR',0
	.word	3620
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	3660
	.byte	12,2,35,84,13
	.byte	'PDISC',0
	.word	3995
	.byte	4,2,35,96,13
	.byte	'PCSR',0
	.word	4281
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	3320
	.byte	8,2,35,104,13
	.byte	'OMSR0',0
	.word	4428
	.byte	4,2,35,112,13
	.byte	'OMSR4',0
	.word	4597
	.byte	4,2,35,116,13
	.byte	'OMSR8',0
	.word	4769
	.byte	4,2,35,120,13
	.byte	'OMSR12',0
	.word	4944
	.byte	4,2,35,124,13
	.byte	'OMCR0',0
	.word	5118
	.byte	4,3,35,128,1,13
	.byte	'OMCR4',0
	.word	5292
	.byte	4,3,35,132,1,13
	.byte	'OMCR8',0
	.word	5468
	.byte	4,3,35,136,1,13
	.byte	'OMCR12',0
	.word	5624
	.byte	4,3,35,140,1,13
	.byte	'OMSR',0
	.word	5957
	.byte	4,3,35,144,1,13
	.byte	'OMCR',0
	.word	6305
	.byte	4,3,35,148,1,13
	.byte	'reserved_98',0
	.word	3320
	.byte	8,3,35,152,1,13
	.byte	'LPCR0',0
	.word	6429
	.byte	4,3,35,160,1,13
	.byte	'LPCR1',0
	.word	6678
	.byte	4,3,35,164,1,13
	.byte	'LPCR2',0
	.word	6937
	.byte	4,3,35,168,1,13
	.byte	'reserved_A4',0
	.word	6977
	.byte	76,3,35,172,1,13
	.byte	'ACCEN1',0
	.word	7033
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	7600
	.byte	4,3,35,252,1,0,16
	.word	7640
	.byte	3
	.word	8243
	.byte	17,3,172,1,9,4,18
	.byte	'IfxPort_State_notChanged',0,0,18
	.byte	'IfxPort_State_high',0,1,18
	.byte	'IfxPort_State_low',0,128,128,4,18
	.byte	'IfxPort_State_toggled',0,129,128,4,0,4
	.byte	'IfxPort_setPinState',0,3,3,208,4,17,1,1,5
	.byte	'port',0,3,208,4,44
	.word	8248
	.byte	5
	.byte	'pinIndex',0,3,208,4,56
	.word	456
	.byte	5
	.byte	'action',0,3,208,4,80
	.word	8253
	.byte	6,0,7
	.byte	'char',0,1,6,3
	.word	8434
	.byte	19
	.byte	'debug_assert_handler',0,5,112,9,1,1,1,1,5
	.byte	'pass',0,5,112,47
	.word	456
	.byte	5
	.byte	'file',0,5,112,59
	.word	8442
	.byte	5
	.byte	'line',0,5,112,69
	.word	449
	.byte	0,20
	.word	209
	.byte	21
	.word	235
	.byte	6,0,20
	.word	270
	.byte	21
	.word	302
	.byte	6,0,20
	.word	352
	.byte	21
	.word	371
	.byte	6,0,20
	.word	387
	.byte	21
	.word	402
	.byte	21
	.word	416
	.byte	6,0,20
	.word	8356
	.byte	21
	.word	8384
	.byte	21
	.word	8398
	.byte	21
	.word	8416
	.byte	6,0,17,6,42,9,2,18
	.byte	'P00_0',0,0,18
	.byte	'P00_1',0,1,18
	.byte	'P00_2',0,2,18
	.byte	'P00_3',0,3,18
	.byte	'P00_4',0,4,18
	.byte	'P00_5',0,5,18
	.byte	'P00_6',0,6,18
	.byte	'P00_7',0,7,18
	.byte	'P00_8',0,8,18
	.byte	'P00_9',0,9,18
	.byte	'P00_10',0,10,18
	.byte	'P00_11',0,11,18
	.byte	'P00_12',0,12,18
	.byte	'P00_13',0,13,18
	.byte	'P00_14',0,14,18
	.byte	'P00_15',0,15,18
	.byte	'P02_0',0,192,0,18
	.byte	'P02_1',0,193,0,18
	.byte	'P02_2',0,194,0,18
	.byte	'P02_3',0,195,0,18
	.byte	'P02_4',0,196,0,18
	.byte	'P02_5',0,197,0,18
	.byte	'P02_6',0,198,0,18
	.byte	'P02_7',0,199,0,18
	.byte	'P02_8',0,200,0,18
	.byte	'P02_9',0,201,0,18
	.byte	'P02_10',0,202,0,18
	.byte	'P02_11',0,203,0,18
	.byte	'P02_12',0,204,0,18
	.byte	'P02_13',0,205,0,18
	.byte	'P02_14',0,206,0,18
	.byte	'P02_15',0,207,0,18
	.byte	'P10_0',0,192,2,18
	.byte	'P10_1',0,193,2,18
	.byte	'P10_2',0,194,2,18
	.byte	'P10_3',0,195,2,18
	.byte	'P10_4',0,196,2,18
	.byte	'P10_5',0,197,2,18
	.byte	'P10_6',0,198,2,18
	.byte	'P10_7',0,199,2,18
	.byte	'P10_8',0,200,2,18
	.byte	'P10_9',0,201,2,18
	.byte	'P10_10',0,202,2,18
	.byte	'P10_11',0,203,2,18
	.byte	'P10_12',0,204,2,18
	.byte	'P10_13',0,205,2,18
	.byte	'P10_14',0,206,2,18
	.byte	'P10_15',0,207,2,18
	.byte	'P11_0',0,224,2,18
	.byte	'P11_1',0,225,2,18
	.byte	'P11_2',0,226,2,18
	.byte	'P11_3',0,227,2,18
	.byte	'P11_4',0,228,2,18
	.byte	'P11_5',0,229,2,18
	.byte	'P11_6',0,230,2,18
	.byte	'P11_7',0,231,2,18
	.byte	'P11_8',0,232,2,18
	.byte	'P11_9',0,233,2,18
	.byte	'P11_10',0,234,2,18
	.byte	'P11_11',0,235,2,18
	.byte	'P11_12',0,236,2,18
	.byte	'P11_13',0,237,2,18
	.byte	'P11_14',0,238,2,18
	.byte	'P11_15',0,239,2,18
	.byte	'P13_0',0,160,3,18
	.byte	'P13_1',0,161,3,18
	.byte	'P13_2',0,162,3,18
	.byte	'P13_3',0,163,3,18
	.byte	'P13_4',0,164,3,18
	.byte	'P13_5',0,165,3,18
	.byte	'P13_6',0,166,3,18
	.byte	'P13_7',0,167,3,18
	.byte	'P13_8',0,168,3,18
	.byte	'P13_9',0,169,3,18
	.byte	'P13_10',0,170,3,18
	.byte	'P13_11',0,171,3,18
	.byte	'P13_12',0,172,3,18
	.byte	'P13_13',0,173,3,18
	.byte	'P13_14',0,174,3,18
	.byte	'P13_15',0,175,3,18
	.byte	'P14_0',0,192,3,18
	.byte	'P14_1',0,193,3,18
	.byte	'P14_2',0,194,3,18
	.byte	'P14_3',0,195,3,18
	.byte	'P14_4',0,196,3,18
	.byte	'P14_5',0,197,3,18
	.byte	'P14_6',0,198,3,18
	.byte	'P14_7',0,199,3,18
	.byte	'P14_8',0,200,3,18
	.byte	'P14_9',0,201,3,18
	.byte	'P14_10',0,202,3,18
	.byte	'P14_11',0,203,3,18
	.byte	'P14_12',0,204,3,18
	.byte	'P14_13',0,205,3,18
	.byte	'P14_14',0,206,3,18
	.byte	'P14_15',0,207,3,18
	.byte	'P15_0',0,224,3,18
	.byte	'P15_1',0,225,3,18
	.byte	'P15_2',0,226,3,18
	.byte	'P15_3',0,227,3,18
	.byte	'P15_4',0,228,3,18
	.byte	'P15_5',0,229,3,18
	.byte	'P15_6',0,230,3,18
	.byte	'P15_7',0,231,3,18
	.byte	'P15_8',0,232,3,18
	.byte	'P15_9',0,233,3,18
	.byte	'P15_10',0,234,3,18
	.byte	'P15_11',0,235,3,18
	.byte	'P15_12',0,236,3,18
	.byte	'P15_13',0,237,3,18
	.byte	'P15_14',0,238,3,18
	.byte	'P15_15',0,239,3,18
	.byte	'P20_0',0,128,5,18
	.byte	'P20_1',0,129,5,18
	.byte	'P20_2',0,130,5,18
	.byte	'P20_3',0,131,5,18
	.byte	'P20_4',0,132,5,18
	.byte	'P20_5',0,133,5,18
	.byte	'P20_6',0,134,5,18
	.byte	'P20_7',0,135,5,18
	.byte	'P20_8',0,136,5,18
	.byte	'P20_9',0,137,5,18
	.byte	'P20_10',0,138,5,18
	.byte	'P20_11',0,139,5,18
	.byte	'P20_12',0,140,5,18
	.byte	'P20_13',0,141,5,18
	.byte	'P20_14',0,142,5,18
	.byte	'P20_15',0,143,5,18
	.byte	'P21_0',0,160,5,18
	.byte	'P21_1',0,161,5,18
	.byte	'P21_2',0,162,5,18
	.byte	'P21_3',0,163,5,18
	.byte	'P21_4',0,164,5,18
	.byte	'P21_5',0,165,5,18
	.byte	'P21_6',0,166,5,18
	.byte	'P21_7',0,167,5,18
	.byte	'P21_8',0,168,5,18
	.byte	'P21_9',0,169,5,18
	.byte	'P21_10',0,170,5,18
	.byte	'P21_11',0,171,5,18
	.byte	'P21_12',0,172,5,18
	.byte	'P21_13',0,173,5,18
	.byte	'P21_14',0,174,5,18
	.byte	'P21_15',0,175,5,18
	.byte	'P22_0',0,192,5,18
	.byte	'P22_1',0,193,5,18
	.byte	'P22_2',0,194,5,18
	.byte	'P22_3',0,195,5,18
	.byte	'P22_4',0,196,5,18
	.byte	'P22_5',0,197,5,18
	.byte	'P22_6',0,198,5,18
	.byte	'P22_7',0,199,5,18
	.byte	'P22_8',0,200,5,18
	.byte	'P22_9',0,201,5,18
	.byte	'P22_10',0,202,5,18
	.byte	'P22_11',0,203,5,18
	.byte	'P22_12',0,204,5,18
	.byte	'P22_13',0,205,5,18
	.byte	'P22_14',0,206,5,18
	.byte	'P22_15',0,207,5,18
	.byte	'P23_0',0,224,5,18
	.byte	'P23_1',0,225,5,18
	.byte	'P23_2',0,226,5,18
	.byte	'P23_3',0,227,5,18
	.byte	'P23_4',0,228,5,18
	.byte	'P23_5',0,229,5,18
	.byte	'P23_6',0,230,5,18
	.byte	'P23_7',0,231,5,18
	.byte	'P23_8',0,232,5,18
	.byte	'P23_9',0,233,5,18
	.byte	'P23_10',0,234,5,18
	.byte	'P23_11',0,235,5,18
	.byte	'P23_12',0,236,5,18
	.byte	'P23_13',0,237,5,18
	.byte	'P23_14',0,238,5,18
	.byte	'P23_15',0,239,5,18
	.byte	'P32_0',0,128,8,18
	.byte	'P32_1',0,129,8,18
	.byte	'P32_2',0,130,8,18
	.byte	'P32_3',0,131,8,18
	.byte	'P32_4',0,132,8,18
	.byte	'P32_5',0,133,8,18
	.byte	'P32_6',0,134,8,18
	.byte	'P32_7',0,135,8,18
	.byte	'P32_8',0,136,8,18
	.byte	'P32_9',0,137,8,18
	.byte	'P32_10',0,138,8,18
	.byte	'P32_11',0,139,8,18
	.byte	'P32_12',0,140,8,18
	.byte	'P32_13',0,141,8,18
	.byte	'P32_14',0,142,8,18
	.byte	'P32_15',0,143,8,18
	.byte	'P33_0',0,160,8,18
	.byte	'P33_1',0,161,8,18
	.byte	'P33_2',0,162,8,18
	.byte	'P33_3',0,163,8,18
	.byte	'P33_4',0,164,8,18
	.byte	'P33_5',0,165,8,18
	.byte	'P33_6',0,166,8,18
	.byte	'P33_7',0,167,8,18
	.byte	'P33_8',0,168,8,18
	.byte	'P33_9',0,169,8,18
	.byte	'P33_10',0,170,8,18
	.byte	'P33_11',0,171,8,18
	.byte	'P33_12',0,172,8,18
	.byte	'P33_13',0,173,8,18
	.byte	'P33_14',0,174,8,18
	.byte	'P33_15',0,175,8,0,22
	.byte	'gpio_get_level',0,6,140,1,7
	.word	456
	.byte	1,1,1,1,5
	.byte	'pin',0,6,140,1,40
	.word	8591
	.byte	0,17,6,91,9,1,18
	.byte	'GPI',0,0,18
	.byte	'GPO',0,1,0,17,6,103,9,1,18
	.byte	'GPI_FLOATING_IN',0,0,18
	.byte	'GPI_PULL_UP',0,1,18
	.byte	'GPI_PULL_DOWN',0,2,18
	.byte	'GPO_PUSH_PULL',0,3,18
	.byte	'GPO_OPEN_DTAIN',0,4,0,19
	.byte	'gpio_init',0,6,143,1,7,1,1,1,1,5
	.byte	'pin',0,6,143,1,40
	.word	8591
	.byte	5
	.byte	'dir',0,6,143,1,59
	.word	10573
	.byte	5
	.byte	'dat',0,6,143,1,70
	.word	456
	.byte	5
	.byte	'pinconf',0,6,143,1,90
	.word	10591
	.byte	0
.L63:
	.byte	17,7,76,9,1,18
	.byte	'KEY_RELEASE',0,0,18
	.byte	'KEY_SHORT_PRESS',0,1,18
	.byte	'KEY_LONG_PRESS',0,2,0
.L65:
	.byte	17,7,67,9,1,18
	.byte	'KEY_1',0,0,18
	.byte	'KEY_2',0,1,18
	.byte	'KEY_3',0,2,18
	.byte	'KEY_4',0,3,18
	.byte	'KEY_NUMBER',0,4,0
.L71:
	.byte	7
	.byte	'unsigned long int',0,4,7,7
	.byte	'short int',0,2,5,23
	.byte	'__wchar_t',0,8,1,1
	.word	10881
	.byte	23
	.byte	'__size_t',0,8,1,1
	.word	433
	.byte	23
	.byte	'__ptrdiff_t',0,8,1,1
	.word	449
	.byte	24,1,3
	.word	10949
	.byte	23
	.byte	'__codeptr',0,8,1,1
	.word	10951
	.byte	23
	.byte	'__intptr_t',0,8,1,1
	.word	449
	.byte	23
	.byte	'__uintptr_t',0,8,1,1
	.word	433
	.byte	23
	.byte	'_iob_flag_t',0,9,82,25
	.word	473
	.byte	23
	.byte	'boolean',0,10,101,29
	.word	456
	.byte	23
	.byte	'uint8',0,10,105,29
	.word	456
	.byte	23
	.byte	'uint16',0,10,109,29
	.word	473
	.byte	23
	.byte	'uint32',0,10,113,29
	.word	10860
	.byte	23
	.byte	'uint64',0,10,118,29
	.word	315
	.byte	23
	.byte	'sint16',0,10,126,29
	.word	10881
	.byte	7
	.byte	'long int',0,4,5,23
	.byte	'sint32',0,10,131,1,29
	.word	11123
	.byte	7
	.byte	'long long int',0,8,5,23
	.byte	'sint64',0,10,138,1,29
	.word	11151
	.byte	23
	.byte	'float32',0,10,167,1,29
	.word	261
	.byte	23
	.byte	'pvoid',0,11,57,28
	.word	347
	.byte	23
	.byte	'Ifx_TickTime',0,11,79,28
	.word	11151
	.byte	7
	.byte	'char',0,1,6,23
	.byte	'int8',0,12,54,29
	.word	11236
	.byte	23
	.byte	'int16',0,12,55,29
	.word	10881
	.byte	23
	.byte	'int32',0,12,56,29
	.word	449
	.byte	23
	.byte	'int64',0,12,57,29
	.word	11151
	.byte	23
	.byte	'Ifx_P_ACCEN0_Bits',0,4,79,3
	.word	7073
	.byte	23
	.byte	'Ifx_P_ACCEN1_Bits',0,4,85,3
	.word	6986
	.byte	23
	.byte	'Ifx_P_ESR_Bits',0,4,107,3
	.word	3329
	.byte	23
	.byte	'Ifx_P_ID_Bits',0,4,115,3
	.word	1382
	.byte	23
	.byte	'Ifx_P_IN_Bits',0,4,137,1,3
	.word	2377
	.byte	23
	.byte	'Ifx_P_IOCR0_Bits',0,4,150,1,3
	.word	1510
	.byte	23
	.byte	'Ifx_P_IOCR12_Bits',0,4,163,1,3
	.word	2157
	.byte	23
	.byte	'Ifx_P_IOCR4_Bits',0,4,176,1,3
	.word	1725
	.byte	23
	.byte	'Ifx_P_IOCR8_Bits',0,4,189,1,3
	.word	1940
	.byte	23
	.byte	'Ifx_P_LPCR0_Bits',0,4,197,1,3
	.word	6345
	.byte	23
	.byte	'Ifx_P_LPCR1_Bits',0,4,205,1,3
	.word	6469
	.byte	23
	.byte	'Ifx_P_LPCR1_P21_Bits',0,4,215,1,3
	.word	6553
	.byte	23
	.byte	'Ifx_P_LPCR2_Bits',0,4,229,1,3
	.word	6733
	.byte	23
	.byte	'Ifx_P_OMCR0_Bits',0,4,240,1,3
	.word	4984
	.byte	23
	.byte	'Ifx_P_OMCR12_Bits',0,4,250,1,3
	.word	5508
	.byte	23
	.byte	'Ifx_P_OMCR4_Bits',0,4,133,2,3
	.word	5158
	.byte	23
	.byte	'Ifx_P_OMCR8_Bits',0,4,144,2,3
	.word	5332
	.byte	23
	.byte	'Ifx_P_OMCR_Bits',0,4,166,2,3
	.word	5997
	.byte	23
	.byte	'Ifx_P_OMR_Bits',0,4,203,2,3
	.word	811
	.byte	23
	.byte	'Ifx_P_OMSR0_Bits',0,4,213,2,3
	.word	4321
	.byte	23
	.byte	'Ifx_P_OMSR12_Bits',0,4,224,2,3
	.word	4809
	.byte	23
	.byte	'Ifx_P_OMSR4_Bits',0,4,235,2,3
	.word	4468
	.byte	23
	.byte	'Ifx_P_OMSR8_Bits',0,4,246,2,3
	.word	4637
	.byte	23
	.byte	'Ifx_P_OMSR_Bits',0,4,140,3,3
	.word	5664
	.byte	23
	.byte	'Ifx_P_OUT_Bits',0,4,162,3,3
	.word	495
	.byte	23
	.byte	'Ifx_P_PCSR_Bits',0,4,180,3,3
	.word	4035
	.byte	23
	.byte	'Ifx_P_PDISC_Bits',0,4,202,3,3
	.word	3669
	.byte	23
	.byte	'Ifx_P_PDR0_Bits',0,4,223,3,3
	.word	2700
	.byte	23
	.byte	'Ifx_P_PDR1_Bits',0,4,244,3,3
	.word	3004
	.byte	23
	.byte	'Ifx_P_ACCEN0',0,4,129,4,3
	.word	7600
	.byte	23
	.byte	'Ifx_P_ACCEN1',0,4,137,4,3
	.word	7033
	.byte	23
	.byte	'Ifx_P_ESR',0,4,145,4,3
	.word	3620
	.byte	23
	.byte	'Ifx_P_ID',0,4,153,4,3
	.word	1461
	.byte	23
	.byte	'Ifx_P_IN',0,4,161,4,3
	.word	2651
	.byte	23
	.byte	'Ifx_P_IOCR0',0,4,169,4,3
	.word	1685
	.byte	23
	.byte	'Ifx_P_IOCR12',0,4,177,4,3
	.word	2337
	.byte	23
	.byte	'Ifx_P_IOCR4',0,4,185,4,3
	.word	1900
	.byte	23
	.byte	'Ifx_P_IOCR8',0,4,193,4,3
	.word	2117
	.byte	23
	.byte	'Ifx_P_LPCR0',0,4,201,4,3
	.word	6429
	.byte	23
	.byte	'Ifx_P_LPCR1',0,4,210,4,3
	.word	6678
	.byte	23
	.byte	'Ifx_P_LPCR2',0,4,218,4,3
	.word	6937
	.byte	23
	.byte	'Ifx_P_OMCR',0,4,226,4,3
	.word	6305
	.byte	23
	.byte	'Ifx_P_OMCR0',0,4,234,4,3
	.word	5118
	.byte	23
	.byte	'Ifx_P_OMCR12',0,4,242,4,3
	.word	5624
	.byte	23
	.byte	'Ifx_P_OMCR4',0,4,250,4,3
	.word	5292
	.byte	23
	.byte	'Ifx_P_OMCR8',0,4,130,5,3
	.word	5468
	.byte	23
	.byte	'Ifx_P_OMR',0,4,138,5,3
	.word	1342
	.byte	23
	.byte	'Ifx_P_OMSR',0,4,146,5,3
	.word	5957
	.byte	23
	.byte	'Ifx_P_OMSR0',0,4,154,5,3
	.word	4428
	.byte	23
	.byte	'Ifx_P_OMSR12',0,4,162,5,3
	.word	4944
	.byte	23
	.byte	'Ifx_P_OMSR4',0,4,170,5,3
	.word	4597
	.byte	23
	.byte	'Ifx_P_OMSR8',0,4,178,5,3
	.word	4769
	.byte	23
	.byte	'Ifx_P_OUT',0,4,186,5,3
	.word	771
	.byte	23
	.byte	'Ifx_P_PCSR',0,4,194,5,3
	.word	4281
	.byte	23
	.byte	'Ifx_P_PDISC',0,4,202,5,3
	.word	3995
	.byte	23
	.byte	'Ifx_P_PDR0',0,4,210,5,3
	.word	2964
	.byte	23
	.byte	'Ifx_P_PDR1',0,4,218,5,3
	.word	3280
	.byte	16
	.word	7640
	.byte	23
	.byte	'Ifx_P',0,4,139,6,3
	.word	12617
	.byte	17,13,250,10,9,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy16384',0,0,18
	.byte	'IfxScu_WDTCON1_IR_divBy256',0,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy64',0,2,0,23
	.byte	'IfxScu_WDTCON1_IR',0,13,255,10,3
	.word	12637
	.byte	10
	.byte	'_Ifx_SCU_ACCEN0_Bits',0,14,45,16,4,11
	.byte	'EN0',0,1
	.word	456
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	456
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	456
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	456
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	456
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	456
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	456
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	456
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	456
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	456
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	456
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	456
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	456
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	456
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	456
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	456
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	456
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	456
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	456
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	456
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	456
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	456
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	456
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	456
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	456
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	456
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	456
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	456
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	456
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	456
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	456
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	456
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_ACCEN0_Bits',0,14,79,3
	.word	12759
	.byte	10
	.byte	'_Ifx_SCU_ACCEN1_Bits',0,14,82,16,4,11
	.byte	'reserved_0',0,4
	.word	433
	.byte	32,0,2,35,0,0,23
	.byte	'Ifx_SCU_ACCEN1_Bits',0,14,85,3
	.word	13316
	.byte	10
	.byte	'_Ifx_SCU_ARSTDIS_Bits',0,14,88,16,4,11
	.byte	'STM0DIS',0,1
	.word	456
	.byte	1,7,2,35,0,11
	.byte	'STM1DIS',0,1
	.word	456
	.byte	1,6,2,35,0,11
	.byte	'STM2DIS',0,1
	.word	456
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	433
	.byte	29,0,2,35,0,0,23
	.byte	'Ifx_SCU_ARSTDIS_Bits',0,14,94,3
	.word	13393
	.byte	10
	.byte	'_Ifx_SCU_CCUCON0_Bits',0,14,97,16,4,11
	.byte	'BAUD1DIV',0,1
	.word	456
	.byte	4,4,2,35,0,11
	.byte	'BAUD2DIV',0,1
	.word	456
	.byte	4,0,2,35,0,11
	.byte	'SRIDIV',0,1
	.word	456
	.byte	4,4,2,35,1,11
	.byte	'LPDIV',0,1
	.word	456
	.byte	4,0,2,35,1,11
	.byte	'SPBDIV',0,1
	.word	456
	.byte	4,4,2,35,2,11
	.byte	'FSI2DIV',0,1
	.word	456
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	456
	.byte	2,0,2,35,2,11
	.byte	'FSIDIV',0,1
	.word	456
	.byte	2,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	456
	.byte	2,4,2,35,3,11
	.byte	'CLKSEL',0,1
	.word	456
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	456
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	456
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_CCUCON0_Bits',0,14,111,3
	.word	13529
	.byte	10
	.byte	'_Ifx_SCU_CCUCON1_Bits',0,14,114,16,4,11
	.byte	'CANDIV',0,1
	.word	456
	.byte	4,4,2,35,0,11
	.byte	'ERAYDIV',0,1
	.word	456
	.byte	4,0,2,35,0,11
	.byte	'STMDIV',0,1
	.word	456
	.byte	4,4,2,35,1,11
	.byte	'GTMDIV',0,1
	.word	456
	.byte	4,0,2,35,1,11
	.byte	'ETHDIV',0,1
	.word	456
	.byte	4,4,2,35,2,11
	.byte	'ASCLINFDIV',0,1
	.word	456
	.byte	4,0,2,35,2,11
	.byte	'ASCLINSDIV',0,1
	.word	456
	.byte	4,4,2,35,3,11
	.byte	'INSEL',0,1
	.word	456
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	456
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	456
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_CCUCON1_Bits',0,14,126,3
	.word	13809
	.byte	10
	.byte	'_Ifx_SCU_CCUCON2_Bits',0,14,129,1,16,4,11
	.byte	'BBBDIV',0,1
	.word	456
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	433
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	456
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	456
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_CCUCON2_Bits',0,14,135,1,3
	.word	14047
	.byte	10
	.byte	'_Ifx_SCU_CCUCON3_Bits',0,14,138,1,16,4,11
	.byte	'PLLDIV',0,1
	.word	456
	.byte	6,2,2,35,0,11
	.byte	'PLLSEL',0,1
	.word	456
	.byte	2,0,2,35,0,11
	.byte	'PLLERAYDIV',0,1
	.word	456
	.byte	6,2,2,35,1,11
	.byte	'PLLERAYSEL',0,1
	.word	456
	.byte	2,0,2,35,1,11
	.byte	'SRIDIV',0,1
	.word	456
	.byte	6,2,2,35,2,11
	.byte	'SRISEL',0,1
	.word	456
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	456
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	456
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	456
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	456
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_CCUCON3_Bits',0,14,150,1,3
	.word	14175
	.byte	10
	.byte	'_Ifx_SCU_CCUCON4_Bits',0,14,153,1,16,4,11
	.byte	'SPBDIV',0,1
	.word	456
	.byte	6,2,2,35,0,11
	.byte	'SPBSEL',0,1
	.word	456
	.byte	2,0,2,35,0,11
	.byte	'GTMDIV',0,1
	.word	456
	.byte	6,2,2,35,1,11
	.byte	'GTMSEL',0,1
	.word	456
	.byte	2,0,2,35,1,11
	.byte	'STMDIV',0,1
	.word	456
	.byte	6,2,2,35,2,11
	.byte	'STMSEL',0,1
	.word	456
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	456
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	456
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	456
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	456
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_CCUCON4_Bits',0,14,165,1,3
	.word	14418
	.byte	10
	.byte	'_Ifx_SCU_CCUCON5_Bits',0,14,168,1,16,4,11
	.byte	'MAXDIV',0,1
	.word	456
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	433
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	456
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	456
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_CCUCON5_Bits',0,14,174,1,3
	.word	14653
	.byte	10
	.byte	'_Ifx_SCU_CCUCON6_Bits',0,14,177,1,16,4,11
	.byte	'CPU0DIV',0,1
	.word	456
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	433
	.byte	26,0,2,35,0,0,23
	.byte	'Ifx_SCU_CCUCON6_Bits',0,14,181,1,3
	.word	14781
	.byte	10
	.byte	'_Ifx_SCU_CCUCON7_Bits',0,14,184,1,16,4,11
	.byte	'CPU1DIV',0,1
	.word	456
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	433
	.byte	26,0,2,35,0,0,23
	.byte	'Ifx_SCU_CCUCON7_Bits',0,14,188,1,3
	.word	14881
	.byte	10
	.byte	'_Ifx_SCU_CHIPID_Bits',0,14,191,1,16,4,11
	.byte	'CHREV',0,1
	.word	456
	.byte	6,2,2,35,0,11
	.byte	'CHTEC',0,1
	.word	456
	.byte	2,0,2,35,0,11
	.byte	'CHID',0,1
	.word	456
	.byte	8,0,2,35,1,11
	.byte	'EEA',0,1
	.word	456
	.byte	1,7,2,35,2,11
	.byte	'UCODE',0,1
	.word	456
	.byte	7,0,2,35,2,11
	.byte	'FSIZE',0,1
	.word	456
	.byte	4,4,2,35,3,11
	.byte	'SP',0,1
	.word	456
	.byte	2,2,2,35,3,11
	.byte	'SEC',0,1
	.word	456
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	456
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_CHIPID_Bits',0,14,202,1,3
	.word	14981
	.byte	10
	.byte	'_Ifx_SCU_DTSCON_Bits',0,14,205,1,16,4,11
	.byte	'PWD',0,1
	.word	456
	.byte	1,7,2,35,0,11
	.byte	'START',0,1
	.word	456
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	456
	.byte	2,4,2,35,0,11
	.byte	'CAL',0,4
	.word	433
	.byte	20,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	456
	.byte	7,1,2,35,3,11
	.byte	'SLCK',0,1
	.word	456
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_DTSCON_Bits',0,14,213,1,3
	.word	15189
	.byte	10
	.byte	'_Ifx_SCU_DTSLIM_Bits',0,14,216,1,16,4,11
	.byte	'LOWER',0,2
	.word	473
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	456
	.byte	5,1,2,35,1,11
	.byte	'LLU',0,1
	.word	456
	.byte	1,0,2,35,1,11
	.byte	'UPPER',0,2
	.word	473
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	456
	.byte	4,2,2,35,3,11
	.byte	'SLCK',0,1
	.word	456
	.byte	1,1,2,35,3,11
	.byte	'UOF',0,1
	.word	456
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_DTSLIM_Bits',0,14,225,1,3
	.word	15354
	.byte	10
	.byte	'_Ifx_SCU_DTSSTAT_Bits',0,14,228,1,16,4,11
	.byte	'RESULT',0,2
	.word	473
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	456
	.byte	4,2,2,35,1,11
	.byte	'RDY',0,1
	.word	456
	.byte	1,1,2,35,1,11
	.byte	'BUSY',0,1
	.word	456
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	473
	.byte	16,0,2,35,2,0,23
	.byte	'Ifx_SCU_DTSSTAT_Bits',0,14,235,1,3
	.word	15537
	.byte	10
	.byte	'_Ifx_SCU_EICR_Bits',0,14,238,1,16,4,11
	.byte	'reserved_0',0,1
	.word	456
	.byte	4,4,2,35,0,11
	.byte	'EXIS0',0,1
	.word	456
	.byte	3,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	456
	.byte	1,0,2,35,0,11
	.byte	'FEN0',0,1
	.word	456
	.byte	1,7,2,35,1,11
	.byte	'REN0',0,1
	.word	456
	.byte	1,6,2,35,1,11
	.byte	'LDEN0',0,1
	.word	456
	.byte	1,5,2,35,1,11
	.byte	'EIEN0',0,1
	.word	456
	.byte	1,4,2,35,1,11
	.byte	'INP0',0,1
	.word	456
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	433
	.byte	5,12,2,35,0,11
	.byte	'EXIS1',0,1
	.word	456
	.byte	3,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	456
	.byte	1,0,2,35,2,11
	.byte	'FEN1',0,1
	.word	456
	.byte	1,7,2,35,3,11
	.byte	'REN1',0,1
	.word	456
	.byte	1,6,2,35,3,11
	.byte	'LDEN1',0,1
	.word	456
	.byte	1,5,2,35,3,11
	.byte	'EIEN1',0,1
	.word	456
	.byte	1,4,2,35,3,11
	.byte	'INP1',0,1
	.word	456
	.byte	3,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	456
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_EICR_Bits',0,14,129,2,3
	.word	15691
	.byte	10
	.byte	'_Ifx_SCU_EIFR_Bits',0,14,132,2,16,4,11
	.byte	'INTF0',0,1
	.word	456
	.byte	1,7,2,35,0,11
	.byte	'INTF1',0,1
	.word	456
	.byte	1,6,2,35,0,11
	.byte	'INTF2',0,1
	.word	456
	.byte	1,5,2,35,0,11
	.byte	'INTF3',0,1
	.word	456
	.byte	1,4,2,35,0,11
	.byte	'INTF4',0,1
	.word	456
	.byte	1,3,2,35,0,11
	.byte	'INTF5',0,1
	.word	456
	.byte	1,2,2,35,0,11
	.byte	'INTF6',0,1
	.word	456
	.byte	1,1,2,35,0,11
	.byte	'INTF7',0,1
	.word	456
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	433
	.byte	24,0,2,35,0,0,23
	.byte	'Ifx_SCU_EIFR_Bits',0,14,143,2,3
	.word	16055
	.byte	10
	.byte	'_Ifx_SCU_EMSR_Bits',0,14,146,2,16,4,11
	.byte	'POL',0,1
	.word	456
	.byte	1,7,2,35,0,11
	.byte	'MODE',0,1
	.word	456
	.byte	1,6,2,35,0,11
	.byte	'ENON',0,1
	.word	456
	.byte	1,5,2,35,0,11
	.byte	'PSEL',0,1
	.word	456
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	473
	.byte	12,0,2,35,0,11
	.byte	'EMSF',0,1
	.word	456
	.byte	1,7,2,35,2,11
	.byte	'SEMSF',0,1
	.word	456
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	456
	.byte	6,0,2,35,2,11
	.byte	'EMSFM',0,1
	.word	456
	.byte	2,6,2,35,3,11
	.byte	'SEMSFM',0,1
	.word	456
	.byte	2,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	456
	.byte	4,0,2,35,3,0,23
	.byte	'Ifx_SCU_EMSR_Bits',0,14,159,2,3
	.word	16266
	.byte	10
	.byte	'_Ifx_SCU_ESRCFG_Bits',0,14,162,2,16,4,11
	.byte	'reserved_0',0,1
	.word	456
	.byte	7,1,2,35,0,11
	.byte	'EDCON',0,2
	.word	473
	.byte	2,7,2,35,0,11
	.byte	'reserved_9',0,4
	.word	433
	.byte	23,0,2,35,0,0,23
	.byte	'Ifx_SCU_ESRCFG_Bits',0,14,167,2,3
	.word	16518
	.byte	10
	.byte	'_Ifx_SCU_ESROCFG_Bits',0,14,170,2,16,4,11
	.byte	'ARI',0,1
	.word	456
	.byte	1,7,2,35,0,11
	.byte	'ARC',0,1
	.word	456
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	433
	.byte	30,0,2,35,0,0,23
	.byte	'Ifx_SCU_ESROCFG_Bits',0,14,175,2,3
	.word	16636
	.byte	10
	.byte	'_Ifx_SCU_EVR13CON_Bits',0,14,178,2,16,4,11
	.byte	'reserved_0',0,4
	.word	433
	.byte	28,4,2,35,0,11
	.byte	'EVR13OFF',0,1
	.word	456
	.byte	1,3,2,35,3,11
	.byte	'BPEVR13OFF',0,1
	.word	456
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	456
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	456
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_EVR13CON_Bits',0,14,185,2,3
	.word	16747
	.byte	10
	.byte	'_Ifx_SCU_EVR33CON_Bits',0,14,188,2,16,4,11
	.byte	'reserved_0',0,4
	.word	433
	.byte	28,4,2,35,0,11
	.byte	'EVR33OFF',0,1
	.word	456
	.byte	1,3,2,35,3,11
	.byte	'BPEVR33OFF',0,1
	.word	456
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	456
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	456
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_EVR33CON_Bits',0,14,195,2,3
	.word	16910
	.byte	10
	.byte	'_Ifx_SCU_EVRADCSTAT_Bits',0,14,198,2,16,4,11
	.byte	'ADC13V',0,1
	.word	456
	.byte	8,0,2,35,0,11
	.byte	'ADC33V',0,1
	.word	456
	.byte	8,0,2,35,1,11
	.byte	'ADCSWDV',0,1
	.word	456
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	456
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	456
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_EVRADCSTAT_Bits',0,14,205,2,3
	.word	17073
	.byte	10
	.byte	'_Ifx_SCU_EVRDVSTAT_Bits',0,14,208,2,16,4,11
	.byte	'DVS13TRIM',0,1
	.word	456
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	456
	.byte	8,0,2,35,1,11
	.byte	'DVS33TRIM',0,1
	.word	456
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	456
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	456
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_EVRDVSTAT_Bits',0,14,215,2,3
	.word	17231
	.byte	10
	.byte	'_Ifx_SCU_EVRMONCTRL_Bits',0,14,218,2,16,4,11
	.byte	'EVR13OVMOD',0,1
	.word	456
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	456
	.byte	2,4,2,35,0,11
	.byte	'EVR13UVMOD',0,1
	.word	456
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	456
	.byte	2,0,2,35,0,11
	.byte	'EVR33OVMOD',0,1
	.word	456
	.byte	2,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	456
	.byte	2,4,2,35,1,11
	.byte	'EVR33UVMOD',0,1
	.word	456
	.byte	2,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	456
	.byte	2,0,2,35,1,11
	.byte	'SWDOVMOD',0,1
	.word	456
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	456
	.byte	2,4,2,35,2,11
	.byte	'SWDUVMOD',0,1
	.word	456
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,2
	.word	473
	.byte	10,0,2,35,2,0,23
	.byte	'Ifx_SCU_EVRMONCTRL_Bits',0,14,232,2,3
	.word	17396
	.byte	10
	.byte	'_Ifx_SCU_EVROSCCTRL_Bits',0,14,235,2,16,4,11
	.byte	'OSCTRIM',0,2
	.word	473
	.byte	10,6,2,35,0,11
	.byte	'OSCPTAT',0,1
	.word	456
	.byte	6,0,2,35,1,11
	.byte	'OSCANASEL',0,1
	.word	456
	.byte	4,4,2,35,2,11
	.byte	'HPBGTRIM',0,2
	.word	473
	.byte	7,5,2,35,2,11
	.byte	'HPBGCLKEN',0,1
	.word	456
	.byte	1,4,2,35,3,11
	.byte	'OSC3V3',0,1
	.word	456
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	456
	.byte	2,1,2,35,3,11
	.byte	'LCK',0,1
	.word	456
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_EVROSCCTRL_Bits',0,14,245,2,3
	.word	17725
	.byte	10
	.byte	'_Ifx_SCU_EVROVMON_Bits',0,14,248,2,16,4,11
	.byte	'EVR13OVVAL',0,1
	.word	456
	.byte	8,0,2,35,0,11
	.byte	'EVR33OVVAL',0,1
	.word	456
	.byte	8,0,2,35,1,11
	.byte	'SWDOVVAL',0,1
	.word	456
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	456
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	456
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_EVROVMON_Bits',0,14,255,2,3
	.word	17946
	.byte	10
	.byte	'_Ifx_SCU_EVRRSTCON_Bits',0,14,130,3,16,4,11
	.byte	'RST13TRIM',0,1
	.word	456
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	433
	.byte	16,8,2,35,0,11
	.byte	'RST13OFF',0,1
	.word	456
	.byte	1,7,2,35,3,11
	.byte	'BPRST13OFF',0,1
	.word	456
	.byte	1,6,2,35,3,11
	.byte	'RST33OFF',0,1
	.word	456
	.byte	1,5,2,35,3,11
	.byte	'BPRST33OFF',0,1
	.word	456
	.byte	1,4,2,35,3,11
	.byte	'RSTSWDOFF',0,1
	.word	456
	.byte	1,3,2,35,3,11
	.byte	'BPRSTSWDOFF',0,1
	.word	456
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	456
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	456
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_EVRRSTCON_Bits',0,14,142,3,3
	.word	18109
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF1_Bits',0,14,145,3,16,4,11
	.byte	'SD5P',0,1
	.word	456
	.byte	8,0,2,35,0,11
	.byte	'SD5I',0,1
	.word	456
	.byte	8,0,2,35,1,11
	.byte	'SD5D',0,1
	.word	456
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	456
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	456
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_EVRSDCOEFF1_Bits',0,14,152,3,3
	.word	18381
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF2_Bits',0,14,155,3,16,4,11
	.byte	'SD33P',0,1
	.word	456
	.byte	8,0,2,35,0,11
	.byte	'SD33I',0,1
	.word	456
	.byte	8,0,2,35,1,11
	.byte	'SD33D',0,1
	.word	456
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	456
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	456
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_EVRSDCOEFF2_Bits',0,14,162,3,3
	.word	18534
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF3_Bits',0,14,165,3,16,4,11
	.byte	'CT5REG0',0,1
	.word	456
	.byte	8,0,2,35,0,11
	.byte	'CT5REG1',0,1
	.word	456
	.byte	8,0,2,35,1,11
	.byte	'CT5REG2',0,1
	.word	456
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	456
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	456
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_EVRSDCOEFF3_Bits',0,14,172,3,3
	.word	18690
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF4_Bits',0,14,175,3,16,4,11
	.byte	'CT5REG3',0,1
	.word	456
	.byte	8,0,2,35,0,11
	.byte	'CT5REG4',0,1
	.word	456
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	473
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	456
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_EVRSDCOEFF4_Bits',0,14,181,3,3
	.word	18852
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF5_Bits',0,14,184,3,16,4,11
	.byte	'CT33REG0',0,1
	.word	456
	.byte	8,0,2,35,0,11
	.byte	'CT33REG1',0,1
	.word	456
	.byte	8,0,2,35,1,11
	.byte	'CT33REG2',0,1
	.word	456
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	456
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	456
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_EVRSDCOEFF5_Bits',0,14,191,3,3
	.word	18995
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF6_Bits',0,14,194,3,16,4,11
	.byte	'CT33REG3',0,1
	.word	456
	.byte	8,0,2,35,0,11
	.byte	'CT33REG4',0,1
	.word	456
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	473
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	456
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_EVRSDCOEFF6_Bits',0,14,200,3,3
	.word	19160
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL1_Bits',0,14,203,3,16,4,11
	.byte	'SDFREQSPRD',0,2
	.word	473
	.byte	16,0,2,35,0,11
	.byte	'SDFREQ',0,1
	.word	456
	.byte	8,0,2,35,2,11
	.byte	'SDSTEP',0,1
	.word	456
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	456
	.byte	2,2,2,35,3,11
	.byte	'SDSAMPLE',0,1
	.word	456
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	456
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_EVRSDCTRL1_Bits',0,14,211,3,3
	.word	19305
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL2_Bits',0,14,214,3,16,4,11
	.byte	'DRVP',0,1
	.word	456
	.byte	8,0,2,35,0,11
	.byte	'SDMINMAXDC',0,1
	.word	456
	.byte	8,0,2,35,1,11
	.byte	'DRVN',0,1
	.word	456
	.byte	8,0,2,35,2,11
	.byte	'SDLUT',0,1
	.word	456
	.byte	6,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	456
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	456
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_EVRSDCTRL2_Bits',0,14,222,3,3
	.word	19486
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL3_Bits',0,14,225,3,16,4,11
	.byte	'SDPWMPRE',0,1
	.word	456
	.byte	8,0,2,35,0,11
	.byte	'SDPID',0,1
	.word	456
	.byte	8,0,2,35,1,11
	.byte	'SDVOKLVL',0,1
	.word	456
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	456
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	456
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_EVRSDCTRL3_Bits',0,14,232,3,3
	.word	19660
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL4_Bits',0,14,235,3,16,4,11
	.byte	'reserved_0',0,1
	.word	456
	.byte	8,0,2,35,0,11
	.byte	'SYNCDIV',0,1
	.word	456
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	433
	.byte	20,1,2,35,0,11
	.byte	'LCK',0,1
	.word	456
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_EVRSDCTRL4_Bits',0,14,241,3,3
	.word	19820
	.byte	10
	.byte	'_Ifx_SCU_EVRSTAT_Bits',0,14,244,3,16,4,11
	.byte	'EVR13',0,1
	.word	456
	.byte	1,7,2,35,0,11
	.byte	'OV13',0,1
	.word	456
	.byte	1,6,2,35,0,11
	.byte	'EVR33',0,1
	.word	456
	.byte	1,5,2,35,0,11
	.byte	'OV33',0,1
	.word	456
	.byte	1,4,2,35,0,11
	.byte	'OVSWD',0,1
	.word	456
	.byte	1,3,2,35,0,11
	.byte	'UV13',0,1
	.word	456
	.byte	1,2,2,35,0,11
	.byte	'UV33',0,1
	.word	456
	.byte	1,1,2,35,0,11
	.byte	'UVSWD',0,1
	.word	456
	.byte	1,0,2,35,0,11
	.byte	'EXTPASS13',0,1
	.word	456
	.byte	1,7,2,35,1,11
	.byte	'EXTPASS33',0,1
	.word	456
	.byte	1,6,2,35,1,11
	.byte	'BGPROK',0,1
	.word	456
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	433
	.byte	21,0,2,35,0,0,23
	.byte	'Ifx_SCU_EVRSTAT_Bits',0,14,130,4,3
	.word	19964
	.byte	10
	.byte	'_Ifx_SCU_EVRTRIM_Bits',0,14,133,4,16,4,11
	.byte	'EVR13TRIM',0,1
	.word	456
	.byte	8,0,2,35,0,11
	.byte	'SDVOUTSEL',0,1
	.word	456
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	473
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	456
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_EVRTRIM_Bits',0,14,139,4,3
	.word	20238
	.byte	10
	.byte	'_Ifx_SCU_EVRUVMON_Bits',0,14,142,4,16,4,11
	.byte	'EVR13UVVAL',0,1
	.word	456
	.byte	8,0,2,35,0,11
	.byte	'EVR33UVVAL',0,1
	.word	456
	.byte	8,0,2,35,1,11
	.byte	'SWDUVVAL',0,1
	.word	456
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	456
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	456
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_EVRUVMON_Bits',0,14,149,4,3
	.word	20377
	.byte	10
	.byte	'_Ifx_SCU_EXTCON_Bits',0,14,152,4,16,4,11
	.byte	'EN0',0,1
	.word	456
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	456
	.byte	1,6,2,35,0,11
	.byte	'SEL0',0,1
	.word	456
	.byte	4,2,2,35,0,11
	.byte	'reserved_6',0,2
	.word	473
	.byte	10,0,2,35,0,11
	.byte	'EN1',0,1
	.word	456
	.byte	1,7,2,35,2,11
	.byte	'NSEL',0,1
	.word	456
	.byte	1,6,2,35,2,11
	.byte	'SEL1',0,1
	.word	456
	.byte	4,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	456
	.byte	2,0,2,35,2,11
	.byte	'DIV1',0,1
	.word	456
	.byte	8,0,2,35,3,0,23
	.byte	'Ifx_SCU_EXTCON_Bits',0,14,163,4,3
	.word	20540
	.byte	10
	.byte	'_Ifx_SCU_FDR_Bits',0,14,166,4,16,4,11
	.byte	'STEP',0,2
	.word	473
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	456
	.byte	4,2,2,35,1,11
	.byte	'DM',0,1
	.word	456
	.byte	2,0,2,35,1,11
	.byte	'RESULT',0,2
	.word	473
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	456
	.byte	5,1,2,35,3,11
	.byte	'DISCLK',0,1
	.word	456
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_FDR_Bits',0,14,174,4,3
	.word	20758
	.byte	10
	.byte	'_Ifx_SCU_FMR_Bits',0,14,177,4,16,4,11
	.byte	'FS0',0,1
	.word	456
	.byte	1,7,2,35,0,11
	.byte	'FS1',0,1
	.word	456
	.byte	1,6,2,35,0,11
	.byte	'FS2',0,1
	.word	456
	.byte	1,5,2,35,0,11
	.byte	'FS3',0,1
	.word	456
	.byte	1,4,2,35,0,11
	.byte	'FS4',0,1
	.word	456
	.byte	1,3,2,35,0,11
	.byte	'FS5',0,1
	.word	456
	.byte	1,2,2,35,0,11
	.byte	'FS6',0,1
	.word	456
	.byte	1,1,2,35,0,11
	.byte	'FS7',0,1
	.word	456
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	456
	.byte	8,0,2,35,1,11
	.byte	'FC0',0,1
	.word	456
	.byte	1,7,2,35,2,11
	.byte	'FC1',0,1
	.word	456
	.byte	1,6,2,35,2,11
	.byte	'FC2',0,1
	.word	456
	.byte	1,5,2,35,2,11
	.byte	'FC3',0,1
	.word	456
	.byte	1,4,2,35,2,11
	.byte	'FC4',0,1
	.word	456
	.byte	1,3,2,35,2,11
	.byte	'FC5',0,1
	.word	456
	.byte	1,2,2,35,2,11
	.byte	'FC6',0,1
	.word	456
	.byte	1,1,2,35,2,11
	.byte	'FC7',0,1
	.word	456
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	456
	.byte	8,0,2,35,3,0,23
	.byte	'Ifx_SCU_FMR_Bits',0,14,197,4,3
	.word	20921
	.byte	10
	.byte	'_Ifx_SCU_ID_Bits',0,14,200,4,16,4,11
	.byte	'MODREV',0,1
	.word	456
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	456
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	473
	.byte	16,0,2,35,2,0,23
	.byte	'Ifx_SCU_ID_Bits',0,14,205,4,3
	.word	21257
	.byte	10
	.byte	'_Ifx_SCU_IGCR_Bits',0,14,208,4,16,4,11
	.byte	'IPEN00',0,1
	.word	456
	.byte	1,7,2,35,0,11
	.byte	'IPEN01',0,1
	.word	456
	.byte	1,6,2,35,0,11
	.byte	'IPEN02',0,1
	.word	456
	.byte	1,5,2,35,0,11
	.byte	'IPEN03',0,1
	.word	456
	.byte	1,4,2,35,0,11
	.byte	'IPEN04',0,1
	.word	456
	.byte	1,3,2,35,0,11
	.byte	'IPEN05',0,1
	.word	456
	.byte	1,2,2,35,0,11
	.byte	'IPEN06',0,1
	.word	456
	.byte	1,1,2,35,0,11
	.byte	'IPEN07',0,1
	.word	456
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	456
	.byte	5,3,2,35,1,11
	.byte	'GEEN0',0,1
	.word	456
	.byte	1,2,2,35,1,11
	.byte	'IGP0',0,1
	.word	456
	.byte	2,0,2,35,1,11
	.byte	'IPEN10',0,1
	.word	456
	.byte	1,7,2,35,2,11
	.byte	'IPEN11',0,1
	.word	456
	.byte	1,6,2,35,2,11
	.byte	'IPEN12',0,1
	.word	456
	.byte	1,5,2,35,2,11
	.byte	'IPEN13',0,1
	.word	456
	.byte	1,4,2,35,2,11
	.byte	'IPEN14',0,1
	.word	456
	.byte	1,3,2,35,2,11
	.byte	'IPEN15',0,1
	.word	456
	.byte	1,2,2,35,2,11
	.byte	'IPEN16',0,1
	.word	456
	.byte	1,1,2,35,2,11
	.byte	'IPEN17',0,1
	.word	456
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	456
	.byte	5,3,2,35,3,11
	.byte	'GEEN1',0,1
	.word	456
	.byte	1,2,2,35,3,11
	.byte	'IGP1',0,1
	.word	456
	.byte	2,0,2,35,3,0,23
	.byte	'Ifx_SCU_IGCR_Bits',0,14,232,4,3
	.word	21364
	.byte	10
	.byte	'_Ifx_SCU_IN_Bits',0,14,235,4,16,4,11
	.byte	'P0',0,1
	.word	456
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	456
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	433
	.byte	30,0,2,35,0,0,23
	.byte	'Ifx_SCU_IN_Bits',0,14,240,4,3
	.word	21816
	.byte	10
	.byte	'_Ifx_SCU_IOCR_Bits',0,14,243,4,16,4,11
	.byte	'reserved_0',0,1
	.word	456
	.byte	4,4,2,35,0,11
	.byte	'PC0',0,1
	.word	456
	.byte	4,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	456
	.byte	4,4,2,35,1,11
	.byte	'PC1',0,1
	.word	456
	.byte	4,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	473
	.byte	16,0,2,35,2,0,23
	.byte	'Ifx_SCU_IOCR_Bits',0,14,250,4,3
	.word	21915
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL0_Bits',0,14,253,4,16,4,11
	.byte	'LBISTREQ',0,1
	.word	456
	.byte	1,7,2,35,0,11
	.byte	'LBISTREQP',0,1
	.word	456
	.byte	1,6,2,35,0,11
	.byte	'PATTERNS',0,2
	.word	473
	.byte	14,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	473
	.byte	16,0,2,35,2,0,23
	.byte	'Ifx_SCU_LBISTCTRL0_Bits',0,14,131,5,3
	.word	22065
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL1_Bits',0,14,134,5,16,4,11
	.byte	'SEED',0,4
	.word	433
	.byte	23,9,2,35,0,11
	.byte	'reserved_23',0,1
	.word	456
	.byte	1,0,2,35,2,11
	.byte	'SPLITSH',0,1
	.word	456
	.byte	3,5,2,35,3,11
	.byte	'BODY',0,1
	.word	456
	.byte	1,4,2,35,3,11
	.byte	'LBISTFREQU',0,1
	.word	456
	.byte	4,0,2,35,3,0,23
	.byte	'Ifx_SCU_LBISTCTRL1_Bits',0,14,141,5,3
	.word	22214
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL2_Bits',0,14,144,5,16,4,11
	.byte	'SIGNATURE',0,4
	.word	433
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	456
	.byte	7,1,2,35,3,11
	.byte	'LBISTDONE',0,1
	.word	456
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_LBISTCTRL2_Bits',0,14,149,5,3
	.word	22375
	.byte	10
	.byte	'_Ifx_SCU_LCLCON_Bits',0,14,152,5,16,4,11
	.byte	'reserved_0',0,2
	.word	473
	.byte	16,0,2,35,0,11
	.byte	'LS',0,1
	.word	456
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,2
	.word	473
	.byte	14,1,2,35,2,11
	.byte	'LSEN',0,1
	.word	456
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_LCLCON_Bits',0,14,158,5,3
	.word	22505
	.byte	10
	.byte	'_Ifx_SCU_LCLTEST_Bits',0,14,161,5,16,4,11
	.byte	'LCLT0',0,1
	.word	456
	.byte	1,7,2,35,0,11
	.byte	'LCLT1',0,1
	.word	456
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	433
	.byte	30,0,2,35,0,0,23
	.byte	'Ifx_SCU_LCLTEST_Bits',0,14,166,5,3
	.word	22637
	.byte	10
	.byte	'_Ifx_SCU_MANID_Bits',0,14,169,5,16,4,11
	.byte	'DEPT',0,1
	.word	456
	.byte	5,3,2,35,0,11
	.byte	'MANUF',0,2
	.word	473
	.byte	11,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	473
	.byte	16,0,2,35,2,0,23
	.byte	'Ifx_SCU_MANID_Bits',0,14,174,5,3
	.word	22752
	.byte	10
	.byte	'_Ifx_SCU_OMR_Bits',0,14,177,5,16,4,11
	.byte	'PS0',0,1
	.word	456
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	456
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	473
	.byte	14,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	456
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	456
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	473
	.byte	14,0,2,35,2,0,23
	.byte	'Ifx_SCU_OMR_Bits',0,14,185,5,3
	.word	22863
	.byte	10
	.byte	'_Ifx_SCU_OSCCON_Bits',0,14,188,5,16,4,11
	.byte	'reserved_0',0,1
	.word	456
	.byte	1,7,2,35,0,11
	.byte	'PLLLV',0,1
	.word	456
	.byte	1,6,2,35,0,11
	.byte	'OSCRES',0,1
	.word	456
	.byte	1,5,2,35,0,11
	.byte	'GAINSEL',0,1
	.word	456
	.byte	2,3,2,35,0,11
	.byte	'MODE',0,1
	.word	456
	.byte	2,1,2,35,0,11
	.byte	'SHBY',0,1
	.word	456
	.byte	1,0,2,35,0,11
	.byte	'PLLHV',0,1
	.word	456
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	456
	.byte	1,6,2,35,1,11
	.byte	'X1D',0,1
	.word	456
	.byte	1,5,2,35,1,11
	.byte	'X1DEN',0,1
	.word	456
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	456
	.byte	4,0,2,35,1,11
	.byte	'OSCVAL',0,1
	.word	456
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	456
	.byte	2,1,2,35,2,11
	.byte	'APREN',0,1
	.word	456
	.byte	1,0,2,35,2,11
	.byte	'CAP0EN',0,1
	.word	456
	.byte	1,7,2,35,3,11
	.byte	'CAP1EN',0,1
	.word	456
	.byte	1,6,2,35,3,11
	.byte	'CAP2EN',0,1
	.word	456
	.byte	1,5,2,35,3,11
	.byte	'CAP3EN',0,1
	.word	456
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	456
	.byte	4,0,2,35,3,0,23
	.byte	'Ifx_SCU_OSCCON_Bits',0,14,209,5,3
	.word	23021
	.byte	10
	.byte	'_Ifx_SCU_OUT_Bits',0,14,212,5,16,4,11
	.byte	'P0',0,1
	.word	456
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	456
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	433
	.byte	30,0,2,35,0,0,23
	.byte	'Ifx_SCU_OUT_Bits',0,14,217,5,3
	.word	23433
	.byte	10
	.byte	'_Ifx_SCU_OVCCON_Bits',0,14,220,5,16,4,11
	.byte	'CSEL0',0,1
	.word	456
	.byte	1,7,2,35,0,11
	.byte	'CSEL1',0,1
	.word	456
	.byte	1,6,2,35,0,11
	.byte	'CSEL2',0,1
	.word	456
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,2
	.word	473
	.byte	13,0,2,35,0,11
	.byte	'OVSTRT',0,1
	.word	456
	.byte	1,7,2,35,2,11
	.byte	'OVSTP',0,1
	.word	456
	.byte	1,6,2,35,2,11
	.byte	'DCINVAL',0,1
	.word	456
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	456
	.byte	5,0,2,35,2,11
	.byte	'OVCONF',0,1
	.word	456
	.byte	1,7,2,35,3,11
	.byte	'POVCONF',0,1
	.word	456
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	456
	.byte	6,0,2,35,3,0,23
	.byte	'Ifx_SCU_OVCCON_Bits',0,14,233,5,3
	.word	23534
	.byte	10
	.byte	'_Ifx_SCU_OVCENABLE_Bits',0,14,236,5,16,4,11
	.byte	'OVEN0',0,1
	.word	456
	.byte	1,7,2,35,0,11
	.byte	'OVEN1',0,1
	.word	456
	.byte	1,6,2,35,0,11
	.byte	'OVEN2',0,1
	.word	456
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	433
	.byte	29,0,2,35,0,0,23
	.byte	'Ifx_SCU_OVCENABLE_Bits',0,14,242,5,3
	.word	23801
	.byte	10
	.byte	'_Ifx_SCU_PDISC_Bits',0,14,245,5,16,4,11
	.byte	'PDIS0',0,1
	.word	456
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	456
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	433
	.byte	30,0,2,35,0,0,23
	.byte	'Ifx_SCU_PDISC_Bits',0,14,250,5,3
	.word	23937
	.byte	10
	.byte	'_Ifx_SCU_PDR_Bits',0,14,253,5,16,4,11
	.byte	'PD0',0,1
	.word	456
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	456
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	456
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	456
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	433
	.byte	24,0,2,35,0,0,23
	.byte	'Ifx_SCU_PDR_Bits',0,14,132,6,3
	.word	24048
	.byte	10
	.byte	'_Ifx_SCU_PDRR_Bits',0,14,135,6,16,4,11
	.byte	'PDR0',0,1
	.word	456
	.byte	1,7,2,35,0,11
	.byte	'PDR1',0,1
	.word	456
	.byte	1,6,2,35,0,11
	.byte	'PDR2',0,1
	.word	456
	.byte	1,5,2,35,0,11
	.byte	'PDR3',0,1
	.word	456
	.byte	1,4,2,35,0,11
	.byte	'PDR4',0,1
	.word	456
	.byte	1,3,2,35,0,11
	.byte	'PDR5',0,1
	.word	456
	.byte	1,2,2,35,0,11
	.byte	'PDR6',0,1
	.word	456
	.byte	1,1,2,35,0,11
	.byte	'PDR7',0,1
	.word	456
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	433
	.byte	24,0,2,35,0,0,23
	.byte	'Ifx_SCU_PDRR_Bits',0,14,146,6,3
	.word	24181
	.byte	10
	.byte	'_Ifx_SCU_PLLCON0_Bits',0,14,149,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	456
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	456
	.byte	1,6,2,35,0,11
	.byte	'MODEN',0,1
	.word	456
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	456
	.byte	1,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	456
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	456
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	456
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	473
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	456
	.byte	7,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	456
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	456
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	456
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	456
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	456
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	456
	.byte	4,0,2,35,3,0,23
	.byte	'Ifx_SCU_PLLCON0_Bits',0,14,166,6,3
	.word	24384
	.byte	10
	.byte	'_Ifx_SCU_PLLCON1_Bits',0,14,169,6,16,4,11
	.byte	'K2DIV',0,1
	.word	456
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	456
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	456
	.byte	7,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	456
	.byte	1,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	456
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	473
	.byte	9,0,2,35,2,0,23
	.byte	'Ifx_SCU_PLLCON1_Bits',0,14,177,6,3
	.word	24740
	.byte	10
	.byte	'_Ifx_SCU_PLLCON2_Bits',0,14,180,6,16,4,11
	.byte	'MODCFG',0,2
	.word	473
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	473
	.byte	16,0,2,35,2,0,23
	.byte	'Ifx_SCU_PLLCON2_Bits',0,14,184,6,3
	.word	24918
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON0_Bits',0,14,187,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	456
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	456
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	456
	.byte	2,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	456
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	456
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	456
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	473
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	456
	.byte	5,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	456
	.byte	2,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	456
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	456
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	456
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	456
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	456
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	456
	.byte	4,0,2,35,3,0,23
	.byte	'Ifx_SCU_PLLERAYCON0_Bits',0,14,204,6,3
	.word	25018
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON1_Bits',0,14,207,6,16,4,11
	.byte	'K2DIV',0,1
	.word	456
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	456
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	456
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	456
	.byte	4,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	456
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	473
	.byte	9,0,2,35,2,0,23
	.byte	'Ifx_SCU_PLLERAYCON1_Bits',0,14,215,6,3
	.word	25388
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYSTAT_Bits',0,14,218,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	456
	.byte	1,7,2,35,0,11
	.byte	'PWDSTAT',0,1
	.word	456
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	456
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	456
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	456
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	456
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	433
	.byte	26,0,2,35,0,0,23
	.byte	'Ifx_SCU_PLLERAYSTAT_Bits',0,14,227,6,3
	.word	25574
	.byte	10
	.byte	'_Ifx_SCU_PLLSTAT_Bits',0,14,230,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	456
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	456
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	456
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	456
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	456
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	456
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	456
	.byte	1,1,2,35,0,11
	.byte	'MODRUN',0,1
	.word	456
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	433
	.byte	24,0,2,35,0,0,23
	.byte	'Ifx_SCU_PLLSTAT_Bits',0,14,241,6,3
	.word	25772
	.byte	10
	.byte	'_Ifx_SCU_PMCSR_Bits',0,14,244,6,16,4,11
	.byte	'REQSLP',0,1
	.word	456
	.byte	2,6,2,35,0,11
	.byte	'SMUSLP',0,1
	.word	456
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	456
	.byte	5,0,2,35,0,11
	.byte	'PMST',0,1
	.word	456
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	433
	.byte	21,0,2,35,0,0,23
	.byte	'Ifx_SCU_PMCSR_Bits',0,14,251,6,3
	.word	26005
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR0_Bits',0,14,254,6,16,4,11
	.byte	'reserved_0',0,1
	.word	456
	.byte	1,7,2,35,0,11
	.byte	'ESR1WKEN',0,1
	.word	456
	.byte	1,6,2,35,0,11
	.byte	'PINAWKEN',0,1
	.word	456
	.byte	1,5,2,35,0,11
	.byte	'PINBWKEN',0,1
	.word	456
	.byte	1,4,2,35,0,11
	.byte	'ESR0DFEN',0,1
	.word	456
	.byte	1,3,2,35,0,11
	.byte	'ESR0EDCON',0,1
	.word	456
	.byte	2,1,2,35,0,11
	.byte	'ESR1DFEN',0,1
	.word	456
	.byte	1,0,2,35,0,11
	.byte	'ESR1EDCON',0,1
	.word	456
	.byte	2,6,2,35,1,11
	.byte	'PINADFEN',0,1
	.word	456
	.byte	1,5,2,35,1,11
	.byte	'PINAEDCON',0,1
	.word	456
	.byte	2,3,2,35,1,11
	.byte	'PINBDFEN',0,1
	.word	456
	.byte	1,2,2,35,1,11
	.byte	'PINBEDCON',0,1
	.word	456
	.byte	2,0,2,35,1,11
	.byte	'SCREN',0,1
	.word	456
	.byte	1,7,2,35,2,11
	.byte	'STBYRAMSEL',0,1
	.word	456
	.byte	2,5,2,35,2,11
	.byte	'SCRCLKSEL',0,1
	.word	456
	.byte	1,4,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	456
	.byte	1,3,2,35,2,11
	.byte	'TRISTEN',0,1
	.word	456
	.byte	1,2,2,35,2,11
	.byte	'TRISTREQ',0,1
	.word	456
	.byte	1,1,2,35,2,11
	.byte	'PORSTDF',0,1
	.word	456
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	456
	.byte	1,7,2,35,3,11
	.byte	'DCDCSYNC',0,1
	.word	456
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	456
	.byte	3,3,2,35,3,11
	.byte	'ESR0TRIST',0,1
	.word	456
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	456
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	456
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_PMSWCR0_Bits',0,14,153,7,3
	.word	26157
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR1_Bits',0,14,156,7,16,4,11
	.byte	'SCRSTEN',0,1
	.word	456
	.byte	1,7,2,35,0,11
	.byte	'SCRSTREQ',0,1
	.word	456
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	456
	.byte	6,0,2,35,0,11
	.byte	'CPUIDLSEL',0,1
	.word	456
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	456
	.byte	1,4,2,35,1,11
	.byte	'IRADIS',0,1
	.word	456
	.byte	1,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	456
	.byte	3,0,2,35,1,11
	.byte	'SCRCFG',0,1
	.word	456
	.byte	8,0,2,35,2,11
	.byte	'CPUSEL',0,1
	.word	456
	.byte	3,5,2,35,3,11
	.byte	'STBYEVEN',0,1
	.word	456
	.byte	1,4,2,35,3,11
	.byte	'STBYEV',0,1
	.word	456
	.byte	3,1,2,35,3,11
	.byte	'LCK',0,1
	.word	456
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_PMSWCR1_Bits',0,14,170,7,3
	.word	26724
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR2_Bits',0,14,173,7,16,4,11
	.byte	'SCRINT',0,1
	.word	456
	.byte	8,0,2,35,0,11
	.byte	'BUSY',0,1
	.word	456
	.byte	1,7,2,35,1,11
	.byte	'SCRECC',0,1
	.word	456
	.byte	1,6,2,35,1,11
	.byte	'SCRWDT',0,1
	.word	456
	.byte	1,5,2,35,1,11
	.byte	'SCRRST',0,1
	.word	456
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	456
	.byte	4,0,2,35,1,11
	.byte	'TCINT',0,1
	.word	456
	.byte	8,0,2,35,2,11
	.byte	'TCINTREQ',0,1
	.word	456
	.byte	1,7,2,35,3,11
	.byte	'SMURST',0,1
	.word	456
	.byte	1,6,2,35,3,11
	.byte	'RST',0,1
	.word	456
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	456
	.byte	4,1,2,35,3,11
	.byte	'LCK',0,1
	.word	456
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_PMSWCR2_Bits',0,14,187,7,3
	.word	27018
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTAT_Bits',0,14,190,7,16,4,11
	.byte	'reserved_0',0,1
	.word	456
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKP',0,1
	.word	456
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUN',0,1
	.word	456
	.byte	1,4,2,35,0,11
	.byte	'PINAWKP',0,1
	.word	456
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUN',0,1
	.word	456
	.byte	1,2,2,35,0,11
	.byte	'PINBWKP',0,1
	.word	456
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUN',0,1
	.word	456
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	456
	.byte	1,7,2,35,1,11
	.byte	'PORSTDF',0,1
	.word	456
	.byte	1,6,2,35,1,11
	.byte	'HWCFGEVR',0,1
	.word	456
	.byte	3,3,2,35,1,11
	.byte	'STBYRAM',0,1
	.word	456
	.byte	2,1,2,35,1,11
	.byte	'TRIST',0,1
	.word	456
	.byte	1,0,2,35,1,11
	.byte	'SCRST',0,1
	.word	456
	.byte	1,7,2,35,2,11
	.byte	'SCRWKP',0,1
	.word	456
	.byte	1,6,2,35,2,11
	.byte	'SCR',0,1
	.word	456
	.byte	1,5,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	456
	.byte	1,4,2,35,2,11
	.byte	'ESR1WKEN',0,1
	.word	456
	.byte	1,3,2,35,2,11
	.byte	'PINAWKEN',0,1
	.word	456
	.byte	1,2,2,35,2,11
	.byte	'PINBWKEN',0,1
	.word	456
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	473
	.byte	4,5,2,35,2,11
	.byte	'ESR0TRIST',0,1
	.word	456
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	456
	.byte	4,0,2,35,3,0,23
	.byte	'Ifx_SCU_PMSWSTAT_Bits',0,14,214,7,3
	.word	27296
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTATCLR_Bits',0,14,217,7,16,4,11
	.byte	'reserved_0',0,1
	.word	456
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKPCLR',0,1
	.word	456
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUNCLR',0,1
	.word	456
	.byte	1,4,2,35,0,11
	.byte	'PINAWKPCLR',0,1
	.word	456
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUNCLR',0,1
	.word	456
	.byte	1,2,2,35,0,11
	.byte	'PINBWKPCLR',0,1
	.word	456
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUNCLR',0,1
	.word	456
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	456
	.byte	8,0,2,35,1,11
	.byte	'SCRSTCLR',0,1
	.word	456
	.byte	1,7,2,35,2,11
	.byte	'SCRWKPCLR',0,1
	.word	456
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	473
	.byte	14,0,2,35,2,0,23
	.byte	'Ifx_SCU_PMSWSTATCLR_Bits',0,14,230,7,3
	.word	27792
	.byte	10
	.byte	'_Ifx_SCU_RSTCON2_Bits',0,14,233,7,16,4,11
	.byte	'reserved_0',0,1
	.word	456
	.byte	1,7,2,35,0,11
	.byte	'CLRC',0,1
	.word	456
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	473
	.byte	10,4,2,35,0,11
	.byte	'CSS0',0,1
	.word	456
	.byte	1,3,2,35,1,11
	.byte	'CSS1',0,1
	.word	456
	.byte	1,2,2,35,1,11
	.byte	'CSS2',0,1
	.word	456
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	456
	.byte	1,0,2,35,1,11
	.byte	'USRINFO',0,2
	.word	473
	.byte	16,0,2,35,2,0,23
	.byte	'Ifx_SCU_RSTCON2_Bits',0,14,243,7,3
	.word	28105
	.byte	10
	.byte	'_Ifx_SCU_RSTCON_Bits',0,14,246,7,16,4,11
	.byte	'ESR0',0,1
	.word	456
	.byte	2,6,2,35,0,11
	.byte	'ESR1',0,1
	.word	456
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	456
	.byte	2,2,2,35,0,11
	.byte	'SMU',0,1
	.word	456
	.byte	2,0,2,35,0,11
	.byte	'SW',0,1
	.word	456
	.byte	2,6,2,35,1,11
	.byte	'STM0',0,1
	.word	456
	.byte	2,4,2,35,1,11
	.byte	'STM1',0,1
	.word	456
	.byte	2,2,2,35,1,11
	.byte	'STM2',0,1
	.word	456
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	473
	.byte	16,0,2,35,2,0,23
	.byte	'Ifx_SCU_RSTCON_Bits',0,14,129,8,3
	.word	28314
	.byte	10
	.byte	'_Ifx_SCU_RSTSTAT_Bits',0,14,132,8,16,4,11
	.byte	'ESR0',0,1
	.word	456
	.byte	1,7,2,35,0,11
	.byte	'ESR1',0,1
	.word	456
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	456
	.byte	1,5,2,35,0,11
	.byte	'SMU',0,1
	.word	456
	.byte	1,4,2,35,0,11
	.byte	'SW',0,1
	.word	456
	.byte	1,3,2,35,0,11
	.byte	'STM0',0,1
	.word	456
	.byte	1,2,2,35,0,11
	.byte	'STM1',0,1
	.word	456
	.byte	1,1,2,35,0,11
	.byte	'STM2',0,1
	.word	456
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	456
	.byte	8,0,2,35,1,11
	.byte	'PORST',0,1
	.word	456
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	456
	.byte	1,6,2,35,2,11
	.byte	'CB0',0,1
	.word	456
	.byte	1,5,2,35,2,11
	.byte	'CB1',0,1
	.word	456
	.byte	1,4,2,35,2,11
	.byte	'CB3',0,1
	.word	456
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	456
	.byte	2,1,2,35,2,11
	.byte	'EVR13',0,1
	.word	456
	.byte	1,0,2,35,2,11
	.byte	'EVR33',0,1
	.word	456
	.byte	1,7,2,35,3,11
	.byte	'SWD',0,1
	.word	456
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	456
	.byte	2,4,2,35,3,11
	.byte	'STBYR',0,1
	.word	456
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	456
	.byte	3,0,2,35,3,0,23
	.byte	'Ifx_SCU_RSTSTAT_Bits',0,14,155,8,3
	.word	28525
	.byte	10
	.byte	'_Ifx_SCU_SAFECON_Bits',0,14,158,8,16,4,11
	.byte	'HBT',0,1
	.word	456
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	433
	.byte	31,0,2,35,0,0,23
	.byte	'Ifx_SCU_SAFECON_Bits',0,14,162,8,3
	.word	28957
	.byte	10
	.byte	'_Ifx_SCU_STSTAT_Bits',0,14,165,8,16,4,11
	.byte	'HWCFG',0,1
	.word	456
	.byte	8,0,2,35,0,11
	.byte	'FTM',0,1
	.word	456
	.byte	7,1,2,35,1,11
	.byte	'MODE',0,1
	.word	456
	.byte	1,0,2,35,1,11
	.byte	'FCBAE',0,1
	.word	456
	.byte	1,7,2,35,2,11
	.byte	'LUDIS',0,1
	.word	456
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	456
	.byte	1,5,2,35,2,11
	.byte	'TRSTL',0,1
	.word	456
	.byte	1,4,2,35,2,11
	.byte	'SPDEN',0,1
	.word	456
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	456
	.byte	3,0,2,35,2,11
	.byte	'RAMINT',0,1
	.word	456
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	456
	.byte	7,0,2,35,3,0,23
	.byte	'Ifx_SCU_STSTAT_Bits',0,14,178,8,3
	.word	29053
	.byte	10
	.byte	'_Ifx_SCU_SWRSTCON_Bits',0,14,181,8,16,4,11
	.byte	'reserved_0',0,1
	.word	456
	.byte	1,7,2,35,0,11
	.byte	'SWRSTREQ',0,1
	.word	456
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	433
	.byte	30,0,2,35,0,0,23
	.byte	'Ifx_SCU_SWRSTCON_Bits',0,14,186,8,3
	.word	29313
	.byte	10
	.byte	'_Ifx_SCU_SYSCON_Bits',0,14,189,8,16,4,11
	.byte	'CCTRIG0',0,1
	.word	456
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	456
	.byte	1,6,2,35,0,11
	.byte	'RAMINTM',0,1
	.word	456
	.byte	2,4,2,35,0,11
	.byte	'SETLUDIS',0,1
	.word	456
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	456
	.byte	3,0,2,35,0,11
	.byte	'DATM',0,1
	.word	456
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,4
	.word	433
	.byte	23,0,2,35,0,0,23
	.byte	'Ifx_SCU_SYSCON_Bits',0,14,198,8,3
	.word	29438
	.byte	10
	.byte	'_Ifx_SCU_TRAPCLR_Bits',0,14,201,8,16,4,11
	.byte	'ESR0T',0,1
	.word	456
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	456
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	456
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	456
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	433
	.byte	28,0,2,35,0,0,23
	.byte	'Ifx_SCU_TRAPCLR_Bits',0,14,208,8,3
	.word	29635
	.byte	10
	.byte	'_Ifx_SCU_TRAPDIS_Bits',0,14,211,8,16,4,11
	.byte	'ESR0T',0,1
	.word	456
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	456
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	456
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	456
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	433
	.byte	28,0,2,35,0,0,23
	.byte	'Ifx_SCU_TRAPDIS_Bits',0,14,218,8,3
	.word	29788
	.byte	10
	.byte	'_Ifx_SCU_TRAPSET_Bits',0,14,221,8,16,4,11
	.byte	'ESR0T',0,1
	.word	456
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	456
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	456
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	456
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	433
	.byte	28,0,2,35,0,0,23
	.byte	'Ifx_SCU_TRAPSET_Bits',0,14,228,8,3
	.word	29941
	.byte	10
	.byte	'_Ifx_SCU_TRAPSTAT_Bits',0,14,231,8,16,4,11
	.byte	'ESR0T',0,1
	.word	456
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	456
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	456
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	456
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	433
	.byte	28,0,2,35,0,0,23
	.byte	'Ifx_SCU_TRAPSTAT_Bits',0,14,238,8,3
	.word	30094
	.byte	7
	.byte	'unsigned int',0,4,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON0_Bits',0,14,241,8,16,4,11
	.byte	'ENDINIT',0,4
	.word	30249
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	30249
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	30249
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	30249
	.byte	16,0,2,35,0,0,23
	.byte	'Ifx_SCU_WDTCPU_CON0_Bits',0,14,247,8,3
	.word	30265
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU_CON1_Bits',0,14,250,8,16,4,11
	.byte	'reserved_0',0,1
	.word	456
	.byte	2,6,2,35,0,11
	.byte	'IR0',0,1
	.word	456
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	456
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	456
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	456
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	456
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	456
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	456
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	456
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	473
	.byte	16,0,2,35,2,0,23
	.byte	'Ifx_SCU_WDTCPU_CON1_Bits',0,14,134,9,3
	.word	30395
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU_SR_Bits',0,14,137,9,16,4,11
	.byte	'AE',0,1
	.word	456
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	456
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	456
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	456
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	456
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	456
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	456
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	456
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	456
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	456
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	473
	.byte	16,0,2,35,2,0,23
	.byte	'Ifx_SCU_WDTCPU_SR_Bits',0,14,150,9,3
	.word	30633
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON0_Bits',0,14,153,9,16,4,11
	.byte	'ENDINIT',0,4
	.word	30249
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	30249
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	30249
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	30249
	.byte	16,0,2,35,0,0,23
	.byte	'Ifx_SCU_WDTS_CON0_Bits',0,14,159,9,3
	.word	30856
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON1_Bits',0,14,162,9,16,4,11
	.byte	'CLRIRF',0,1
	.word	456
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	456
	.byte	1,6,2,35,0,11
	.byte	'IR0',0,1
	.word	456
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	456
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	456
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	456
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	456
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	456
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	456
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	456
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	473
	.byte	16,0,2,35,2,0,23
	.byte	'Ifx_SCU_WDTS_CON1_Bits',0,14,175,9,3
	.word	30982
	.byte	10
	.byte	'_Ifx_SCU_WDTS_SR_Bits',0,14,178,9,16,4,11
	.byte	'AE',0,1
	.word	456
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	456
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	456
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	456
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	456
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	456
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	456
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	456
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	456
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	456
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	473
	.byte	16,0,2,35,2,0,23
	.byte	'Ifx_SCU_WDTS_SR_Bits',0,14,191,9,3
	.word	31234
	.byte	12,14,199,9,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12759
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_ACCEN0',0,14,204,9,3
	.word	31453
	.byte	12,14,207,9,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13316
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_ACCEN1',0,14,212,9,3
	.word	31517
	.byte	12,14,215,9,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13393
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_ARSTDIS',0,14,220,9,3
	.word	31581
	.byte	12,14,223,9,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13529
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_CCUCON0',0,14,228,9,3
	.word	31646
	.byte	12,14,231,9,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13809
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_CCUCON1',0,14,236,9,3
	.word	31711
	.byte	12,14,239,9,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14047
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_CCUCON2',0,14,244,9,3
	.word	31776
	.byte	12,14,247,9,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14175
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_CCUCON3',0,14,252,9,3
	.word	31841
	.byte	12,14,255,9,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14418
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_CCUCON4',0,14,132,10,3
	.word	31906
	.byte	12,14,135,10,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14653
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_CCUCON5',0,14,140,10,3
	.word	31971
	.byte	12,14,143,10,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14781
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_CCUCON6',0,14,148,10,3
	.word	32036
	.byte	12,14,151,10,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14881
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_CCUCON7',0,14,156,10,3
	.word	32101
	.byte	12,14,159,10,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14981
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_CHIPID',0,14,164,10,3
	.word	32166
	.byte	12,14,167,10,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15189
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_DTSCON',0,14,172,10,3
	.word	32230
	.byte	12,14,175,10,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15354
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_DTSLIM',0,14,180,10,3
	.word	32294
	.byte	12,14,183,10,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15537
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_DTSSTAT',0,14,188,10,3
	.word	32358
	.byte	12,14,191,10,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15691
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_EICR',0,14,196,10,3
	.word	32423
	.byte	12,14,199,10,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16055
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_EIFR',0,14,204,10,3
	.word	32485
	.byte	12,14,207,10,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16266
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_EMSR',0,14,212,10,3
	.word	32547
	.byte	12,14,215,10,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16518
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_ESRCFG',0,14,220,10,3
	.word	32609
	.byte	12,14,223,10,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16636
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_ESROCFG',0,14,228,10,3
	.word	32673
	.byte	12,14,231,10,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16747
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_EVR13CON',0,14,236,10,3
	.word	32738
	.byte	12,14,239,10,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16910
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_EVR33CON',0,14,244,10,3
	.word	32804
	.byte	12,14,247,10,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17073
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_EVRADCSTAT',0,14,252,10,3
	.word	32870
	.byte	12,14,255,10,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17231
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_EVRDVSTAT',0,14,132,11,3
	.word	32938
	.byte	12,14,135,11,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17396
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_EVRMONCTRL',0,14,140,11,3
	.word	33005
	.byte	12,14,143,11,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17725
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_EVROSCCTRL',0,14,148,11,3
	.word	33073
	.byte	12,14,151,11,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17946
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_EVROVMON',0,14,156,11,3
	.word	33141
	.byte	12,14,159,11,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18109
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_EVRRSTCON',0,14,164,11,3
	.word	33207
	.byte	12,14,167,11,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18381
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_EVRSDCOEFF1',0,14,172,11,3
	.word	33274
	.byte	12,14,175,11,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18534
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_EVRSDCOEFF2',0,14,180,11,3
	.word	33343
	.byte	12,14,183,11,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18690
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_EVRSDCOEFF3',0,14,188,11,3
	.word	33412
	.byte	12,14,191,11,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18852
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_EVRSDCOEFF4',0,14,196,11,3
	.word	33481
	.byte	12,14,199,11,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18995
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_EVRSDCOEFF5',0,14,204,11,3
	.word	33550
	.byte	12,14,207,11,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19160
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_EVRSDCOEFF6',0,14,212,11,3
	.word	33619
	.byte	12,14,215,11,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19305
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_EVRSDCTRL1',0,14,220,11,3
	.word	33688
	.byte	12,14,223,11,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19486
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_EVRSDCTRL2',0,14,228,11,3
	.word	33756
	.byte	12,14,231,11,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19660
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_EVRSDCTRL3',0,14,236,11,3
	.word	33824
	.byte	12,14,239,11,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19820
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_EVRSDCTRL4',0,14,244,11,3
	.word	33892
	.byte	12,14,247,11,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19964
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_EVRSTAT',0,14,252,11,3
	.word	33960
	.byte	12,14,255,11,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20238
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_EVRTRIM',0,14,132,12,3
	.word	34025
	.byte	12,14,135,12,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20377
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_EVRUVMON',0,14,140,12,3
	.word	34090
	.byte	12,14,143,12,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20540
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_EXTCON',0,14,148,12,3
	.word	34156
	.byte	12,14,151,12,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20758
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_FDR',0,14,156,12,3
	.word	34220
	.byte	12,14,159,12,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20921
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_FMR',0,14,164,12,3
	.word	34281
	.byte	12,14,167,12,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21257
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_ID',0,14,172,12,3
	.word	34342
	.byte	12,14,175,12,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21364
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_IGCR',0,14,180,12,3
	.word	34402
	.byte	12,14,183,12,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21816
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_IN',0,14,188,12,3
	.word	34464
	.byte	12,14,191,12,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21915
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_IOCR',0,14,196,12,3
	.word	34524
	.byte	12,14,199,12,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22065
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_LBISTCTRL0',0,14,204,12,3
	.word	34586
	.byte	12,14,207,12,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22214
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_LBISTCTRL1',0,14,212,12,3
	.word	34654
	.byte	12,14,215,12,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22375
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_LBISTCTRL2',0,14,220,12,3
	.word	34722
	.byte	12,14,223,12,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22505
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_LCLCON',0,14,228,12,3
	.word	34790
	.byte	12,14,231,12,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22637
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_LCLTEST',0,14,236,12,3
	.word	34854
	.byte	12,14,239,12,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22752
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_MANID',0,14,244,12,3
	.word	34919
	.byte	12,14,247,12,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22863
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_OMR',0,14,252,12,3
	.word	34982
	.byte	12,14,255,12,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23021
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_OSCCON',0,14,132,13,3
	.word	35043
	.byte	12,14,135,13,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23433
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_OUT',0,14,140,13,3
	.word	35107
	.byte	12,14,143,13,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23534
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_OVCCON',0,14,148,13,3
	.word	35168
	.byte	12,14,151,13,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23801
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_OVCENABLE',0,14,156,13,3
	.word	35232
	.byte	12,14,159,13,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23937
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_PDISC',0,14,164,13,3
	.word	35299
	.byte	12,14,167,13,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24048
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_PDR',0,14,172,13,3
	.word	35362
	.byte	12,14,175,13,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24181
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_PDRR',0,14,180,13,3
	.word	35423
	.byte	12,14,183,13,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24384
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_PLLCON0',0,14,188,13,3
	.word	35485
	.byte	12,14,191,13,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24740
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_PLLCON1',0,14,196,13,3
	.word	35550
	.byte	12,14,199,13,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24918
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_PLLCON2',0,14,204,13,3
	.word	35615
	.byte	12,14,207,13,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25018
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_PLLERAYCON0',0,14,212,13,3
	.word	35680
	.byte	12,14,215,13,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25388
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_PLLERAYCON1',0,14,220,13,3
	.word	35749
	.byte	12,14,223,13,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25574
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_PLLERAYSTAT',0,14,228,13,3
	.word	35818
	.byte	12,14,231,13,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25772
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_PLLSTAT',0,14,236,13,3
	.word	35887
	.byte	12,14,239,13,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26005
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_PMCSR',0,14,244,13,3
	.word	35952
	.byte	12,14,247,13,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26157
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_PMSWCR0',0,14,252,13,3
	.word	36015
	.byte	12,14,255,13,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26724
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_PMSWCR1',0,14,132,14,3
	.word	36080
	.byte	12,14,135,14,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27018
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_PMSWCR2',0,14,140,14,3
	.word	36145
	.byte	12,14,143,14,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27296
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_PMSWSTAT',0,14,148,14,3
	.word	36210
	.byte	12,14,151,14,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27792
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_PMSWSTATCLR',0,14,156,14,3
	.word	36276
	.byte	12,14,159,14,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28314
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_RSTCON',0,14,164,14,3
	.word	36345
	.byte	12,14,167,14,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28105
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_RSTCON2',0,14,172,14,3
	.word	36409
	.byte	12,14,175,14,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28525
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_RSTSTAT',0,14,180,14,3
	.word	36474
	.byte	12,14,183,14,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28957
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_SAFECON',0,14,188,14,3
	.word	36539
	.byte	12,14,191,14,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29053
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_STSTAT',0,14,196,14,3
	.word	36604
	.byte	12,14,199,14,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29313
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_SWRSTCON',0,14,204,14,3
	.word	36668
	.byte	12,14,207,14,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29438
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_SYSCON',0,14,212,14,3
	.word	36734
	.byte	12,14,215,14,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29635
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_TRAPCLR',0,14,220,14,3
	.word	36798
	.byte	12,14,223,14,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29788
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_TRAPDIS',0,14,228,14,3
	.word	36863
	.byte	12,14,231,14,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29941
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_TRAPSET',0,14,236,14,3
	.word	36928
	.byte	12,14,239,14,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30094
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_TRAPSTAT',0,14,244,14,3
	.word	36993
	.byte	12,14,247,14,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30265
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_WDTCPU_CON0',0,14,252,14,3
	.word	37059
	.byte	12,14,255,14,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30395
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_WDTCPU_CON1',0,14,132,15,3
	.word	37128
	.byte	12,14,135,15,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30633
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_WDTCPU_SR',0,14,140,15,3
	.word	37197
	.byte	12,14,143,15,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30856
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_WDTS_CON0',0,14,148,15,3
	.word	37264
	.byte	12,14,151,15,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30982
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_WDTS_CON1',0,14,156,15,3
	.word	37331
	.byte	12,14,159,15,9,4,13
	.byte	'U',0
	.word	433
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	449
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31234
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_WDTS_SR',0,14,164,15,3
	.word	37398
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU',0,14,175,15,25,12,13
	.byte	'CON0',0
	.word	37059
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	37128
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	37197
	.byte	4,2,35,8,0,16
	.word	37463
	.byte	23
	.byte	'Ifx_SCU_WDTCPU',0,14,180,15,3
	.word	37526
	.byte	10
	.byte	'_Ifx_SCU_WDTS',0,14,183,15,25,12,13
	.byte	'CON0',0
	.word	37264
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	37331
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	37398
	.byte	4,2,35,8,0,16
	.word	37555
	.byte	23
	.byte	'Ifx_SCU_WDTS',0,14,188,15,3
	.word	37616
	.byte	17,3,83,9,1,18
	.byte	'IfxPort_InputMode_undefined',0,127,18
	.byte	'IfxPort_InputMode_noPullDevice',0,0,18
	.byte	'IfxPort_InputMode_pullDown',0,8,18
	.byte	'IfxPort_InputMode_pullUp',0,16,0,23
	.byte	'IfxPort_InputMode',0,3,89,3
	.word	37643
	.byte	17,3,120,9,1,18
	.byte	'IfxPort_OutputIdx_general',0,128,1,18
	.byte	'IfxPort_OutputIdx_alt1',0,136,1,18
	.byte	'IfxPort_OutputIdx_alt2',0,144,1,18
	.byte	'IfxPort_OutputIdx_alt3',0,152,1,18
	.byte	'IfxPort_OutputIdx_alt4',0,160,1,18
	.byte	'IfxPort_OutputIdx_alt5',0,168,1,18
	.byte	'IfxPort_OutputIdx_alt6',0,176,1,18
	.byte	'IfxPort_OutputIdx_alt7',0,184,1,0,23
	.byte	'IfxPort_OutputIdx',0,3,130,1,3
	.word	37794
	.byte	17,3,134,1,9,1,18
	.byte	'IfxPort_OutputMode_pushPull',0,128,1,18
	.byte	'IfxPort_OutputMode_openDrain',0,192,1,0,23
	.byte	'IfxPort_OutputMode',0,3,138,1,3
	.word	38038
	.byte	17,3,144,1,9,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed1',0,0,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed2',0,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed3',0,2,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed4',0,3,18
	.byte	'IfxPort_PadDriver_lvdsSpeed1',0,4,18
	.byte	'IfxPort_PadDriver_lvdsSpeed2',0,5,18
	.byte	'IfxPort_PadDriver_lvdsSpeed3',0,6,18
	.byte	'IfxPort_PadDriver_lvdsSpeed4',0,7,18
	.byte	'IfxPort_PadDriver_ttlSpeed1',0,8,18
	.byte	'IfxPort_PadDriver_ttlSpeed2',0,9,18
	.byte	'IfxPort_PadDriver_ttlSpeed3',0,10,18
	.byte	'IfxPort_PadDriver_ttlSpeed4',0,11,0,23
	.byte	'IfxPort_PadDriver',0,3,158,1,3
	.word	38136
	.byte	23
	.byte	'IfxPort_State',0,3,178,1,3
	.word	8253
	.byte	23
	.byte	'gpio_pin_enum',0,6,89,2
	.word	8591
	.byte	23
	.byte	'gpio_dir_enum',0,6,95,2
	.word	10573
	.byte	23
	.byte	'gpio_mode_enum',0,6,111,2
	.word	10591
	.byte	23
	.byte	'key_index_enum',0,7,74,2
	.word	10809
	.byte	23
	.byte	'key_state_enum',0,7,81,2
	.word	10754
.L75:
	.byte	14,16
	.word	10860
	.byte	15,3,0
.L76:
	.byte	14,4
	.word	10754
	.byte	15,3,0,14,8
	.word	8591
	.byte	15,3,0
.L77:
	.byte	25
	.word	38732
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L25:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,21,0,54,15,39,12,0,0,3,15,0,73,19,0,0,4,46,1,3,8,32,13
	.byte	58,15,59,15,57,15,54,15,39,12,0,0,5,5,0,3,8,58,15,59,15,57,15,73,19,0,0,6,11,0,0,0,7,36,0,3,8,11,15,62
	.byte	15,0,0,8,46,1,3,8,32,13,58,15,59,15,57,15,73,19,54,15,39,12,0,0,9,59,0,3,8,0,0,10,19,1,3,8,58,15,59,15
	.byte	57,15,11,15,0,0,11,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,12,23,1,58,15,59,15,57,15,11,15,0,0,13,13
	.byte	0,3,8,73,19,11,15,56,9,0,0,14,1,1,11,15,73,19,0,0,15,33,0,47,15,0,0,16,53,0,73,19,0,0,17,4,1,58,15,59
	.byte	15,57,15,11,15,0,0,18,40,0,3,8,28,13,0,0,19,46,1,3,8,58,15,59,15,57,15,54,15,39,12,63,12,60,12,0,0,20
	.byte	46,1,49,19,0,0,21,5,0,49,19,0,0,22,46,1,3,8,58,15,59,15,57,15,73,19,54,15,39,12,63,12,60,12,0,0,23,22
	.byte	0,3,8,58,15,59,15,57,15,73,19,0,0,24,21,0,54,15,0,0,25,38,0,73,19,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L26:
	.word	.L83-.L82
.L82:
	.half	3
	.word	.L85-.L84
.L84:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver',0
	.byte	'F:\\ADS\\AURIX-Studio-1.10.2\\tools\\Compilers\\Tasking_1.1r8\\ctc\\include\\',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std',0
	.byte	0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_Intrinsics.h',0
	.byte	0,0,0
	.byte	'IFXPORT.h',0,1,0,0
	.byte	'IfxPort_regdef.h',0,2,0,0
	.byte	'zf_common_debug.h',0,3,0,0
	.byte	'zf_driver_gpio.h',0,4,0,0
	.byte	'..\\libraries\\zf_device\\zf_device_key.h',0,0,0,0
	.byte	'../libraries/zf_device/zf_device_key.c',0,0,0,0
	.byte	'stdio.h',0,5,0,0
	.byte	'Platform_Types.h',0,6,0,0
	.byte	'ifx_types.h',0,6,0,0
	.byte	'zf_common_typedef.h',0,3,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxScu_cfg.h',0
	.byte	0,0,0
	.byte	'IfxScu_regdef.h',0,2,0,0,0
.L85:
.L83:
	.sdecl	'.debug_info',debug,cluster('key_scanner')
	.sect	'.debug_info'
.L27:
	.word	263
	.half	3
	.word	.L28
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_key.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L30,.L29
	.byte	2
	.word	.L23
	.byte	3
	.byte	'key_scanner',0,1,65,6,1,1,1
	.word	.L14,.L60,.L13
	.byte	4
	.word	.L14,.L60
	.byte	5
	.byte	'i',0,1,67,11
	.word	.L61,.L62
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('key_scanner')
	.sect	'.debug_abbrev'
.L28:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('key_scanner')
	.sect	'.debug_line'
.L29:
	.word	.L87-.L86
.L86:
	.half	3
	.word	.L89-.L88
.L88:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_key.c',0,0,0,0,0
.L89:
	.byte	5,11,7,0,5,2
	.word	.L14
	.byte	3,195,0,1,5,30,9
	.half	.L78-.L14
	.byte	1,5,57,9
	.half	.L3-.L78
	.byte	3,2,1,5,48,9
	.half	.L90-.L3
	.byte	1,5,57,9
	.half	.L91-.L90
	.byte	1,5,9,9
	.half	.L92-.L91
	.byte	1,5,27,7,9
	.half	.L93-.L92
	.byte	3,2,1,5,13,9
	.half	.L94-.L93
	.byte	1,5,27,9
	.half	.L95-.L94
	.byte	1,5,13,9
	.half	.L96-.L95
	.byte	1,5,27,9
	.half	.L97-.L96
	.byte	1,5,31,9
	.half	.L98-.L97
	.byte	1,5,16,9
	.half	.L99-.L98
	.byte	3,1,1,5,40,9
	.half	.L100-.L99
	.byte	1,5,38,9
	.half	.L101-.L100
	.byte	1,5,72,9
	.half	.L102-.L101
	.byte	1,5,58,9
	.half	.L103-.L102
	.byte	1,5,72,9
	.half	.L104-.L103
	.byte	1,5,13,9
	.half	.L105-.L104
	.byte	1,5,17,7,9
	.half	.L106-.L105
	.byte	3,2,1,5,26,9
	.half	.L107-.L106
	.byte	1,5,32,9
	.half	.L108-.L107
	.byte	1,5,30,9
	.half	.L109-.L108
	.byte	1,5,13,9
	.half	.L5-.L109
	.byte	3,126,1,5,35,9
	.half	.L4-.L5
	.byte	3,7,1,5,44,9
	.half	.L110-.L4
	.byte	1,5,16,9
	.half	.L111-.L110
	.byte	1,5,53,7,9
	.half	.L112-.L111
	.byte	1,5,76,9
	.half	.L113-.L112
	.byte	1,5,74,9
	.half	.L114-.L113
	.byte	1,5,108,9
	.half	.L115-.L114
	.byte	1,5,94,9
	.half	.L116-.L115
	.byte	1,5,108,9
	.half	.L117-.L116
	.byte	1,5,91,9
	.half	.L118-.L117
	.byte	1,5,17,7,9
	.half	.L119-.L118
	.byte	3,2,1,5,26,9
	.half	.L120-.L119
	.byte	1,5,32,9
	.half	.L121-.L120
	.byte	1,5,30,9
	.half	.L122-.L121
	.byte	1,5,47,9
	.half	.L123-.L122
	.byte	1,5,17,9
	.half	.L7-.L123
	.byte	3,4,1,5,26,9
	.half	.L124-.L7
	.byte	1,5,32,9
	.half	.L125-.L124
	.byte	1,5,30,9
	.half	.L126-.L125
	.byte	1,5,27,9
	.half	.L9-.L126
	.byte	3,2,1,5,13,9
	.half	.L127-.L9
	.byte	1,5,27,9
	.half	.L128-.L127
	.byte	1,5,33,9
	.half	.L129-.L128
	.byte	1,5,31,9
	.half	.L130-.L129
	.byte	1,5,34,9
	.half	.L6-.L130
	.byte	3,108,1,5,30,9
	.half	.L2-.L6
	.byte	1,5,1,7,9
	.half	.L131-.L2
	.byte	3,23,1,7,9
	.half	.L31-.L131
	.byte	0,1,1
.L87:
	.sdecl	'.debug_ranges',debug,cluster('key_scanner')
	.sect	'.debug_ranges'
.L30:
	.word	-1,.L14,0,.L31-.L14,0,0
	.sdecl	'.debug_info',debug,cluster('key_get_state')
	.sect	'.debug_info'
.L32:
	.word	272
	.half	3
	.word	.L33
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_key.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L35,.L34
	.byte	2
	.word	.L23
	.byte	3
	.byte	'key_get_state',0,1,100,16
	.word	.L63
	.byte	1,1,1
	.word	.L16,.L64,.L15
	.byte	4
	.byte	'key_n',0,1,100,46
	.word	.L65,.L66
	.byte	5
	.word	.L16,.L64
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('key_get_state')
	.sect	'.debug_abbrev'
.L33:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('key_get_state')
	.sect	'.debug_line'
.L34:
	.word	.L133-.L132
.L132:
	.half	3
	.word	.L135-.L134
.L134:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_key.c',0,0,0,0,0
.L135:
	.byte	5,12,7,0,5,2
	.word	.L16
	.byte	3,229,0,1,5,21,9
	.half	.L136-.L16
	.byte	1,5,5,9
	.half	.L137-.L136
	.byte	1,5,1,9
	.half	.L10-.L137
	.byte	3,1,1,7,9
	.half	.L36-.L10
	.byte	0,1,1
.L133:
	.sdecl	'.debug_ranges',debug,cluster('key_get_state')
	.sect	'.debug_ranges'
.L35:
	.word	-1,.L16,0,.L36-.L16,0,0
	.sdecl	'.debug_info',debug,cluster('key_clear_state')
	.sect	'.debug_info'
.L37:
	.word	270
	.half	3
	.word	.L38
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_key.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L40,.L39
	.byte	2
	.word	.L23
	.byte	3
	.byte	'key_clear_state',0,1,112,6,1,1,1
	.word	.L18,.L67,.L17
	.byte	4
	.byte	'key_n',0,1,112,38
	.word	.L65,.L68
	.byte	5
	.word	.L18,.L67
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('key_clear_state')
	.sect	'.debug_abbrev'
.L38:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('key_clear_state')
	.sect	'.debug_line'
.L39:
	.word	.L139-.L138
.L138:
	.half	3
	.word	.L141-.L140
.L140:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_key.c',0,0,0,0,0
.L141:
	.byte	5,5,7,0,5,2
	.word	.L18
	.byte	3,241,0,1,5,14,9
	.half	.L142-.L18
	.byte	1,5,24,9
	.half	.L143-.L142
	.byte	1,5,22,9
	.half	.L144-.L143
	.byte	1,5,1,9
	.half	.L145-.L144
	.byte	3,1,1,7,9
	.half	.L41-.L145
	.byte	0,1,1
.L139:
	.sdecl	'.debug_ranges',debug,cluster('key_clear_state')
	.sect	'.debug_ranges'
.L40:
	.word	-1,.L18,0,.L41-.L18,0,0
	.sdecl	'.debug_info',debug,cluster('key_clear_all_state')
	.sect	'.debug_info'
.L42:
	.word	256
	.half	3
	.word	.L43
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_key.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L45,.L44
	.byte	2
	.word	.L23
	.byte	3
	.byte	'key_clear_all_state',0,1,124,6,1,1,1
	.word	.L20,.L69,.L19
	.byte	4
	.word	.L20,.L69
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('key_clear_all_state')
	.sect	'.debug_abbrev'
.L43:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('key_clear_all_state')
	.sect	'.debug_line'
.L44:
	.word	.L147-.L146
.L146:
	.half	3
	.word	.L149-.L148
.L148:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_key.c',0,0,0,0,0
.L149:
	.byte	5,5,7,0,5,2
	.word	.L20
	.byte	3,253,0,1,5,20,9
	.half	.L150-.L20
	.byte	1,5,18,9
	.half	.L151-.L150
	.byte	1,5,5,9
	.half	.L152-.L151
	.byte	3,1,1,5,20,9
	.half	.L153-.L152
	.byte	1,5,18,9
	.half	.L154-.L153
	.byte	1,5,5,9
	.half	.L155-.L154
	.byte	3,1,1,5,20,9
	.half	.L156-.L155
	.byte	1,5,18,9
	.half	.L157-.L156
	.byte	1,5,5,9
	.half	.L158-.L157
	.byte	3,1,1,5,20,9
	.half	.L159-.L158
	.byte	1,5,18,9
	.half	.L160-.L159
	.byte	1,5,1,9
	.half	.L161-.L160
	.byte	3,1,1,7,9
	.half	.L46-.L161
	.byte	0,1,1
.L147:
	.sdecl	'.debug_ranges',debug,cluster('key_clear_all_state')
	.sect	'.debug_ranges'
.L45:
	.word	-1,.L20,0,.L46-.L20,0,0
	.sdecl	'.debug_info',debug,cluster('key_init')
	.sect	'.debug_info'
.L47:
	.word	300
	.half	3
	.word	.L48
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_key.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L50,.L49
	.byte	2
	.word	.L23
	.byte	3
	.byte	'key_init',0,1,139,1,6,1,1,1
	.word	.L22,.L70,.L21
	.byte	4
	.byte	'period',0,1,139,1,23
	.word	.L71,.L72
	.byte	5
	.word	.L22,.L70
	.byte	5
	.word	.L73,.L70
	.byte	6
	.byte	'loop_temp',0,1,142,1,11
	.word	.L61,.L74
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('key_init')
	.sect	'.debug_abbrev'
.L48:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('key_init')
	.sect	'.debug_line'
.L49:
	.word	.L163-.L162
.L162:
	.half	3
	.word	.L165-.L164
.L164:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_key.c',0,0,0,0,0
.L165:
	.byte	5,6,7,0,5,2
	.word	.L22
	.byte	3,138,1,1,5,5,9
	.half	.L80-.L22
	.byte	3,2,1,5,19,9
	.half	.L73-.L80
	.byte	3,2,1,5,46,9
	.half	.L81-.L73
	.byte	1,5,28,9
	.half	.L12-.L81
	.byte	3,2,1,5,19,9
	.half	.L166-.L12
	.byte	1,5,28,9
	.half	.L167-.L166
	.byte	1,5,41,9
	.half	.L168-.L167
	.byte	1,5,46,9
	.half	.L169-.L168
	.byte	1,5,57,9
	.half	.L170-.L169
	.byte	1,5,9,9
	.half	.L171-.L170
	.byte	3,1,1,5,18,9
	.half	.L172-.L171
	.byte	1,5,32,9
	.half	.L173-.L172
	.byte	1,5,30,9
	.half	.L174-.L173
	.byte	1,5,58,9
	.half	.L175-.L174
	.byte	3,125,1,5,46,9
	.half	.L11-.L175
	.byte	1,5,5,7,9
	.half	.L176-.L11
	.byte	3,5,1,5,20,9
	.half	.L177-.L176
	.byte	1,5,1,9
	.half	.L178-.L177
	.byte	3,1,1,7,9
	.half	.L51-.L178
	.byte	0,1,1
.L163:
	.sdecl	'.debug_ranges',debug,cluster('key_init')
	.sect	'.debug_ranges'
.L50:
	.word	-1,.L22,0,.L51-.L22,0,0
	.sdecl	'.debug_info',debug,cluster('scanner_period')
	.sect	'.debug_info'
.L52:
	.word	228
	.half	3
	.word	.L53
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_key.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L23
	.byte	3
	.byte	'scanner_period',0,8,52,29
	.word	.L71
	.byte	5,3
	.word	scanner_period
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('scanner_period')
	.sect	'.debug_abbrev'
.L53:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_info',debug,cluster('key_press_time')
	.sect	'.debug_info'
.L54:
	.word	228
	.half	3
	.word	.L55
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_key.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L23
	.byte	3
	.byte	'key_press_time',0,8,53,29
	.word	.L75
	.byte	5,3
	.word	key_press_time
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('key_press_time')
	.sect	'.debug_abbrev'
.L55:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_info',debug,cluster('key_state')
	.sect	'.debug_info'
.L56:
	.word	223
	.half	3
	.word	.L57
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_key.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L23
	.byte	3
	.byte	'key_state',0,8,54,29
	.word	.L76
	.byte	5,3
	.word	key_state
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('key_state')
	.sect	'.debug_abbrev'
.L57:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_info',debug,cluster('key_index')
	.sect	'.debug_info'
.L58:
	.word	223
	.half	3
	.word	.L59
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_key.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L23
	.byte	3
	.byte	'key_index',0,8,56,29
	.word	.L77
	.byte	5,3
	.word	key_index
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('key_index')
	.sect	'.debug_abbrev'
.L59:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_loc',debug,cluster('key_clear_all_state')
	.sect	'.debug_loc'
.L19:
	.word	-1,.L20,0,.L69-.L20
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('key_clear_state')
	.sect	'.debug_loc'
.L17:
	.word	-1,.L18,0,.L67-.L18
	.half	2
	.byte	138,0
	.word	0,0
.L68:
	.word	-1,.L18,0,.L67-.L18
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('key_get_state')
	.sect	'.debug_loc'
.L15:
	.word	-1,.L16,0,.L64-.L16
	.half	2
	.byte	138,0
	.word	0,0
.L66:
	.word	-1,.L16,0,.L64-.L16
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('key_init')
	.sect	'.debug_loc'
.L21:
	.word	-1,.L22,0,.L70-.L22
	.half	2
	.byte	138,0
	.word	0,0
.L74:
	.word	-1,.L22,.L81-.L22,.L70-.L22
	.half	1
	.byte	89
	.word	0,0
.L72:
	.word	-1,.L22,0,.L79-.L22
	.half	1
	.byte	84
	.word	.L80-.L22,.L70-.L22
	.half	1
	.byte	88
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('key_scanner')
	.sect	'.debug_loc'
.L62:
	.word	-1,.L14,.L78-.L14,.L60-.L14
	.half	1
	.byte	88
	.word	0,0
.L13:
	.word	-1,.L14,0,.L60-.L14
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L179:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('key_scanner')
	.sect	'.debug_frame'
	.word	12
	.word	.L179,.L14,.L60-.L14
	.sdecl	'.debug_frame',debug,cluster('key_get_state')
	.sect	'.debug_frame'
	.word	24
	.word	.L179,.L16,.L64-.L16
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('key_clear_state')
	.sect	'.debug_frame'
	.word	24
	.word	.L179,.L18,.L67-.L18
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('key_clear_all_state')
	.sect	'.debug_frame'
	.word	24
	.word	.L179,.L20,.L69-.L20
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('key_init')
	.sect	'.debug_frame'
	.word	12
	.word	.L179,.L22,.L70-.L22
	; Module end
