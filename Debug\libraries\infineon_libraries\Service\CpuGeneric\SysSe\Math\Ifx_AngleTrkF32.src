	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc29884a --dep-file=Ifx_AngleTrkF32.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_AngleTrkF32.src ../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_AngleTrkF32.c"
	.compiler_name		"ctc"
	;source	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_AngleTrkF32.c'

	
$TC16X
	.sdecl	'.rodata.Ifx_AngleTrkF32..1.cnt',data,rom
	.sect	'.rodata.Ifx_AngleTrkF32..1.cnt'
	.align	4
.1.cnt:	.type	object
	.size	.1.cnt,8
	.word	1610612736,1074340347
	
	.sdecl	'.text.Ifx_AngleTrkF32.round_f',code,cluster('round_f')
	.sect	'.text.Ifx_AngleTrkF32.round_f'
	.align	2
	
	.global	round_f
; Function round_f
.L47:
round_f:	.type	func
	ftoiz	d15,d4
.L349:
	itof	d0,d15
.L774:
	sub.f	d4,d4,d0
.L348:
	call	__f_ftod
	mov	e4,d3,d2
.L775:
	mov	d6,#0
	mov	d7,#0
	addih	d7,d7,#16352
.L776:
	call	__d_fgt
.L777:
	jne	d2,#0,.L2
.L778:
	itof	d2,d15
.L351:
	j	.L3
.L2:
	add	d15,#1
.L350:
	itof	d2,d15
.L3:
	j	.L4
.L4:
	ret
.L339:
	
__round_f_function_end:
	.size	round_f,__round_f_function_end-round_f
.L219:
	; End of function
	
	.sdecl	'.text.Ifx_AngleTrkF32.Ifx_AngleTrkF32_setControlGains',code,cluster('Ifx_AngleTrkF32_setControlGains')
	.sect	'.text.Ifx_AngleTrkF32.Ifx_AngleTrkF32_setControlGains'
	.align	2
	
	.global	Ifx_AngleTrkF32_setControlGains
; Function Ifx_AngleTrkF32_setControlGains
.L49:
Ifx_AngleTrkF32_setControlGains:	.type	func
	movh	d15,#16384
.L783:
	mul.f	d0,d6,d6
.L784:
	madd.f	d15,d0,d4,d15
.L785:
	movh	d0,#16256
.L786:
	add.f	d15,d15,d0
.L787:
	mul.f	d0,d5,d5
.L788:
	div.f	d15,d15,d0
.L789:
	st.w	[a4],d15
.L790:
	movh	d15,#16256
.L791:
	madd.f	d15,d15,d6,d6
.L792:
	mul.f	d15,d4,d15
.L793:
	mul.f	d0,d5,d5
.L794:
	mul.f	d0,d0,d5
.L795:
	div.f	d15,d15,d0
.L796:
	st.w	[a4]4,d15
.L797:
	movh	d15,#16384
.L798:
	add.f	d15,d4,d15
.L799:
	div.f	d15,d15,d5
.L800:
	st.w	[a4]8,d15
.L801:
	ret
.L342:
	
__Ifx_AngleTrkF32_setControlGains_function_end:
	.size	Ifx_AngleTrkF32_setControlGains,__Ifx_AngleTrkF32_setControlGains_function_end-Ifx_AngleTrkF32_setControlGains
.L224:
	; End of function
	
	.sdecl	'.text.Ifx_AngleTrkF32.Ifx_AngleTrkF32_initConfig',code,cluster('Ifx_AngleTrkF32_initConfig')
	.sect	'.text.Ifx_AngleTrkF32.Ifx_AngleTrkF32_initConfig'
	.align	2
	
	.global	Ifx_AngleTrkF32_initConfig
; Function Ifx_AngleTrkF32_initConfig
.L51:
Ifx_AngleTrkF32_initConfig:	.type	func
	sub.a	a10,#24
.L352:
	mov.aa	a15,a4
.L355:
	mov.aa	a12,a5
.L356:
	mov.aa	a13,a6
.L357:
	lea	a4,[a10]0
.L353:
	mov	d4,#13107
	addih	d4,d4,#16924
.L478:
	mov.u	d5,#49807
	addih	d5,d5,#15477
.L479:
	mov.u	d6,#52196
	addih	d6,d6,#16534
	call	Ifx_AngleTrkF32_setControlGains
.L354:
	ld.w	d15,[a10]
.L480:
	st.w	[a15],d15
.L481:
	ld.w	d15,[a10]4
.L482:
	st.w	[a15]4,d15
.L483:
	ld.w	d15,[a10]8
.L484:
	st.w	[a15]8,d15
.L485:
	st.a	[a15]40,a12
.L486:
	st.a	[a15]44,a13
.L487:
	mov.u	d15,#47299
	addih	d15,d15,#15794
.L488:
	st.w	[a15]16,d15
.L489:
	mov	d15,#2089
.L490:
	st.w	[a15]20,d15
.L491:
	mov	d15,#2007
.L492:
	st.w	[a15]24,d15
.L493:
	movh	d15,#17096
.L494:
	st.w	[a15]12,d15
.L495:
	mov	d15,#1
.L496:
	st.h	[a15]28,d15
.L497:
	mov	d15,#0
.L498:
	st.b	[a15]30,d15
.L499:
	mov	d15,#0
.L500:
	st.w	[a15]36,d15
.L501:
	mov	d15,#4096
.L502:
	st.w	[a15]32,d15
.L503:
	ret
.L236:
	
__Ifx_AngleTrkF32_initConfig_function_end:
	.size	Ifx_AngleTrkF32_initConfig,__Ifx_AngleTrkF32_initConfig_function_end-Ifx_AngleTrkF32_initConfig
.L109:
	; End of function
	
	.sdecl	'.text.Ifx_AngleTrkF32.Ifx_AngleTrkF32_setUserSampling',code,cluster('Ifx_AngleTrkF32_setUserSampling')
	.sect	'.text.Ifx_AngleTrkF32.Ifx_AngleTrkF32_setUserSampling'
	.align	2
	
	.global	Ifx_AngleTrkF32_setUserSampling
; Function Ifx_AngleTrkF32_setUserSampling
.L53:
Ifx_AngleTrkF32_setUserSampling:	.type	func
	st.w	[a4]26,d4
.L766:
	movh	d15,#16384
.L767:
	div.f	d15,d4,d15
.L768:
	st.w	[a4]68,d15
.L769:
	ret
.L336:
	
__Ifx_AngleTrkF32_setUserSampling_function_end:
	.size	Ifx_AngleTrkF32_setUserSampling,__Ifx_AngleTrkF32_setUserSampling_function_end-Ifx_AngleTrkF32_setUserSampling
.L214:
	; End of function
	
	.sdecl	'.text.Ifx_AngleTrkF32.Ifx_AngleTrkF32_init',code,cluster('Ifx_AngleTrkF32_init')
	.sect	'.text.Ifx_AngleTrkF32.Ifx_AngleTrkF32_init'
	.align	2
	
	.global	Ifx_AngleTrkF32_init
; Function Ifx_AngleTrkF32_init
.L55:
Ifx_AngleTrkF32_init:	.type	func
	sub.a	a10,#16
.L358:
	mov.aa	a12,a4
.L361:
	mov.aa	a15,a5
.L362:
	mov	d8,d4
.L363:
	ld.w	d15,[a15]36
.L407:
	st.w	[a12]14,d15
.L408:
	ld.w	d15,[a15]32
.L409:
	st.w	[a12]22,d15
.L410:
	ld.bu	d0,[a15]30
.L411:
	st.b	[a12]18,d0
.L412:
	st.w	[a12]26,d8
.L413:
	mov	d15,#0
.L414:
	st.w	[a12],d15
.L415:
	mov	d15,#2
.L416:
	st.b	[a12]8,d15
.L417:
	mov	d15,#0
.L418:
	st.w	[a12]4,d15
.L419:
	mov	d15,#0
.L420:
	st.w	[a12]10,d15
.L421:
	ld.hu	d15,[a15]28
.L422:
	st.h	[a12]20,d15
.L423:
	mov	d10,#0
	mov	d11,#0
	addih	d11,d11,#16368
.L424:
	ld.w	d15,[a12]22
.L425:
	itof	d4,d15
.L359:
	call	__f_ftod
.L360:
	mov	e6,d3,d2
.L426:
	mov	e4,d11,d10
	call	__d_div
	mov	e4,d3,d2
.L427:
	mov	d6,#0
	mov	d7,#0
	addih	d7,d7,#16384
.L428:
	call	__d_mul
	mov	e4,d3,d2
.L429:
	movh.a	a2,#@his(.1.cnt)
	lea	a2,[a2]@los(.1.cnt)
	ld.d	e6,[a2]0
.L430:
	call	__d_mul
	mov	e4,d3,d2
	call	__d_dtof
.L431:
	st.w	[a12]30,d2
.L432:
	ld.w	d15,[a15]8
.L433:
	st.w	[a12]44,d15
.L434:
	ld.w	d15,[a15]4
.L435:
	st.w	[a12]40,d15
.L436:
	ld.w	d15,[a15]
.L437:
	st.w	[a12]36,d15
.L438:
	ld.w	d15,[a15]16
.L439:
	st.w	[a12]48,d15
.L440:
	ld.w	d15,[a15]20
.L441:
	st.w	[a12]52,d15
.L442:
	ld.w	d15,[a15]24
.L443:
	st.w	[a12]56,d15
.L444:
	ld.a	a2,[a15]40
.L445:
	st.a	[a12]60,a2
.L446:
	ld.a	a2,[a15]44
.L447:
	st.a	[a12]64,a2
.L448:
	ld.w	d15,[a15]
	mov	d0,#0
	cmp.f	d15,d15,d0
	jnz.t	d15:2,.L5
	ld.w	d15,[a15]
	mov	d0,#0
	cmp.f	d15,d15,d0
	jnz.t	d15:0,.L6
.L449:
	ld.w	d15,[a15]4
	mov	d0,#0
	cmp.f	d15,d15,d0
	jnz.t	d15:2,.L7
	ld.w	d15,[a15]4
	mov	d0,#0
	cmp.f	d15,d15,d0
	jnz.t	d15:0,.L8
.L450:
	mov	d15,#0
	mov	d0,#0
	cmp.f	d15,d15,d0
	jnz.t	d15:2,.L9
	mov	d15,#0
	mov	d0,#0
	cmp.f	d15,d15,d0
	jnz.t	d15:0,.L10
.L451:
	lea	a4,[a12]36
.L452:
	mov	d4,#13107
	addih	d4,d4,#16924
.L453:
	mov.u	d5,#49807
	addih	d5,d5,#15477
.L454:
	mov.u	d6,#52196
	addih	d6,d6,#16534
	call	Ifx_AngleTrkF32_setControlGains
.L10:
.L8:
.L6:
.L5:
.L7:
.L9:
	mov.aa	a4,a12
.L364:
	mov	d4,d8
.L366:
	call	Ifx_AngleTrkF32_setUserSampling
.L365:
	mov	d15,#0
.L455:
	st.w	[a12]96,d15
.L456:
	mov	d15,#0
.L457:
	st.w	[a12]80,d15
.L458:
	mov	d15,#0
.L459:
	st.w	[a12]92,d15
.L460:
	mov	d15,#0
.L461:
	st.w	[a12]84,d15
.L462:
	mov	d15,#0
.L463:
	st.w	[a12]88,d15
.L464:
	mov	d15,#0
.L465:
	st.w	[a12]76,d15
.L232:
	movh	d15,#16256
.L466:
	st.w	[a10]4,d15
.L467:
	ld.w	d15,[a15]12
.L468:
	mov	d0,#4059
	addih	d0,d0,#16585
.L469:
	mul.f	d15,d15,d0
.L470:
	st.w	[a10],d15
.L471:
	st.w	[a10]8,d8
.L472:
	lea	a4,[a12]100
.L473:
	lea	a5,[a10]0
	call	Ifx_LowPassPt1F32_init
.L233:
	ret
.L225:
	
__Ifx_AngleTrkF32_init_function_end:
	.size	Ifx_AngleTrkF32_init,__Ifx_AngleTrkF32_init_function_end-Ifx_AngleTrkF32_init
.L104:
	; End of function
	
	.sdecl	'.text.Ifx_AngleTrkF32.Ifx_AngleTrkF32_step',code,cluster('Ifx_AngleTrkF32_step')
	.sect	'.text.Ifx_AngleTrkF32.Ifx_AngleTrkF32_step'
	.align	2
	
	.global	Ifx_AngleTrkF32_step
; Function Ifx_AngleTrkF32_step
.L57:
Ifx_AngleTrkF32_step:	.type	func
	mov.aa	a15,a4
.L370:
	mov	d8,d6
.L371:
	ld.bu	d15,[a15]18
.L508:
	jeq	d15,#0,.L11
.L509:
	itof	d15,d5
.L510:
	itof	d5,d4
.L368:
	mov	d4,d15
.L369:
	call	Ifx_LutAtan2F32_float32
.L367:
	j	.L12
.L11:
	itof	d4,d4
.L373:
	itof	d5,d5
.L372:
	call	Ifx_LutAtan2F32_float32
.L12:
	st.w	[a15]72,d2
.L511:
	add.f	d15,d2,d8
.L374:
	st.w	[a15]76,d15
.L512:
	ld.w	d15,[a15]40
.L375:
	ld.w	d0,[a15]96
.L513:
	mul.f	d15,d15,d0
.L376:
	ld.w	d0,[a15]26
.L514:
	ld.w	d1,[a15]92
.L515:
	madd.f	d15,d1,d15,d0
.L377:
	st.w	[a15]92,d15
.L516:
	ld.w	d15,[a15]36
.L517:
	ld.w	d0,[a15]96
.L518:
	ld.w	d1,[a15]92
.L519:
	madd.f	d15,d1,d15,d0
.L378:
	ld.w	d0,[a15]26
.L520:
	ld.w	d1,[a15]84
.L521:
	madd.f	d15,d1,d15,d0
.L379:
	st.w	[a15]84,d15
.L522:
	ld.w	d15,[a15]44
.L523:
	ld.w	d0,[a15]96
.L524:
	ld.w	d1,[a15]84
.L525:
	madd.f	d15,d1,d15,d0
.L380:
	ld.w	d0,[a15]88
.L526:
	add.f	d0,d15,d0
.L527:
	ld.w	d1,[a15]68
.L528:
	ld.w	d2,[a15]80
.L529:
	madd.f	d2,d2,d0,d1
.L381:
	st.w	[a15]88,d15
.L256:
	movh	d15,#16256
.L261:
	extr.u	d15,d15,#23,#8
	ne	d15,d15,#0
	jeq	d15,#0,.L13
.L13:
	mov	d0,#4059
.L383:
	addih	d0,d0,#16585
.L530:
	movh	d15,#16384
.L531:
	div.f	d15,d0,d15
.L532:
	cmp.f	d15,d2,d15
	and	d15,#6
	ne	d15,d15,#0
.L533:
	jeq	d15,#0,.L14
.L534:
	sub.f	d2,d2,d0
.L535:
	j	.L15
.L14:
	insn.t	d15,d0:31,d0:31
.L536:
	movh	d1,#16384
.L537:
	div.f	d15,d15,d1
.L538:
	cmp.f	d15,d2,d15
	extr.u	d15,d15,#0,#1
.L539:
	jeq	d15,#0,.L16
.L540:
	add.f	d2,d2,d0
.L541:
	j	.L17
.L16:
.L17:
.L15:
	j	.L18
.L18:
	j	.L19
.L19:
	st.w	[a15]80,d2
.L542:
	ld.w	d15,[a15]76
.L543:
	sub.f	d0,d15,d2
.L269:
	movh	d15,#16256
.L270:
	extr.u	d15,d15,#23,#8
	ne	d15,d15,#0
	jeq	d15,#0,.L20
.L20:
	mov	d1,#4059
.L384:
	addih	d1,d1,#16585
.L544:
	movh	d15,#16384
.L545:
	div.f	d15,d1,d15
.L546:
	cmp.f	d15,d0,d15
	and	d15,#6
	ne	d15,d15,#0
.L547:
	jeq	d15,#0,.L21
.L548:
	sub.f	d0,d0,d1
.L549:
	j	.L22
.L21:
	insn.t	d15,d1:31,d1:31
.L550:
	movh	d2,#16384
.L382:
	div.f	d15,d15,d2
.L551:
	cmp.f	d15,d0,d15
	extr.u	d15,d15,#0,#1
.L552:
	jeq	d15,#0,.L23
.L553:
	add.f	d0,d0,d1
.L554:
	j	.L24
.L23:
.L24:
.L22:
	j	.L25
.L25:
	j	.L26
.L26:
	st.w	[a15]96,d0
.L555:
	ld.w	d15,[a15]88
.L556:
	mov	d0,#0
.L557:
	cmp.f	d15,d15,d0
	jnz.t	d15:2,.L27
.L558:
	mov	d15,#1
.L559:
	j	.L28
.L27:
	mov	d15,#0
.L28:
	st.b	[a15]8,d15
.L560:
	lea	a4,[a15]100
.L561:
	ld.w	d4,[a15]88
	call	Ifx_LowPassPt1F32_do
.L385:
	ld.w	d2,[a15]80
.L562:
	j	.L29
.L29:
	ret
.L244:
	
__Ifx_AngleTrkF32_step_function_end:
	.size	Ifx_AngleTrkF32_step,__Ifx_AngleTrkF32_step_function_end-Ifx_AngleTrkF32_step
.L114:
	; End of function
	
	.sdecl	'.text.Ifx_AngleTrkF32.Ifx_AngleTrkF32_setOffset',code,cluster('Ifx_AngleTrkF32_setOffset')
	.sect	'.text.Ifx_AngleTrkF32.Ifx_AngleTrkF32_setOffset'
	.align	2
	
	.global	Ifx_AngleTrkF32_setOffset
; Function Ifx_AngleTrkF32_setOffset
.L59:
Ifx_AngleTrkF32_setOffset:	.type	func
	st.w	[a4]14,d4
.L686:
	ret
.L319:
	
__Ifx_AngleTrkF32_setOffset_function_end:
	.size	Ifx_AngleTrkF32_setOffset,__Ifx_AngleTrkF32_setOffset_function_end-Ifx_AngleTrkF32_setOffset
.L194:
	; End of function
	
	.sdecl	'.text.Ifx_AngleTrkF32.Ifx_AngleTrkF32_updateStatus',code,cluster('Ifx_AngleTrkF32_updateStatus')
	.sect	'.text.Ifx_AngleTrkF32.Ifx_AngleTrkF32_updateStatus'
	.align	2
	
	.global	Ifx_AngleTrkF32_updateStatus
; Function Ifx_AngleTrkF32_updateStatus
.L61:
Ifx_AngleTrkF32_updateStatus:	.type	func
	mov.aa	a15,a4
.L388:
	mul	d4,d4
.L387:
	madd	d0,d4,d5,d5
.L389:
	ld.bu	d1,[a15]10
.L567:
	ld.w	d15,[a15]56
.L568:
	lt	d15,d0,d15
.L569:
	insert	d15,d1,d15,#1,#1
	st.b	[a15]10,d15
.L570:
	ld.bu	d1,[a15]10
.L571:
	ld.w	d15,[a15]52
.L572:
	lt	d15,d15,d0
.L573:
	insert	d15,d1,d15,#2,#1
	st.b	[a15]10,d15
.L574:
	ld.w	d4,[a15]96
	call	__f_ftod
.L386:
	mov	d4,#0
	mov	d5,#0
	mov	e6,d3,d2
	call	__d_fgt
	jne	d2,#0,.L30
	ld.w	d0,[a15]96
	j	.L31
.L30:
	ld.w	d15,[a15]96
	insn.t	d0,d15:31,d15:31
.L31:
	ld.bu	d1,[a15]10
.L575:
	ld.w	d15,[a15]48
.L576:
	cmp.f	d15,d0,d15
	extr.u	d15,d15,#2,#1
.L577:
	insert	d15,d1,d15,#3,#1
	st.b	[a15]10,d15
.L578:
	ret
.L272:
	
__Ifx_AngleTrkF32_updateStatus_function_end:
	.size	Ifx_AngleTrkF32_updateStatus,__Ifx_AngleTrkF32_updateStatus_function_end-Ifx_AngleTrkF32_updateStatus
.L119:
	; End of function
	
	.sdecl	'.text.Ifx_AngleTrkF32.Ifx_AngleTrkF32_update',code,cluster('Ifx_AngleTrkF32_update')
	.sect	'.text.Ifx_AngleTrkF32.Ifx_AngleTrkF32_update'
	.align	2
	
	.global	Ifx_AngleTrkF32_update
; Function Ifx_AngleTrkF32_update
.L63:
Ifx_AngleTrkF32_update:	.type	func
	mov.aa	a15,a4
.L391:
	ld.a	a2,[a15]60
.L696:
	ld.h	d4,[a2]
.L697:
	ld.a	a2,[a15]64
.L698:
	ld.h	d5,[a2]
.L699:
	mov	d6,#0
	mov.aa	a4,a15
	call	Ifx_AngleTrkF32_step
.L390:
	ld.w	d15,[a15]22
.L700:
	mov	d0,#2
.L701:
	div	e0,d15,d0
	itof	d15,d0
.L702:
	mul.f	d15,d2,d15
.L703:
	mov	d0,#4059
	addih	d0,d0,#16457
.L704:
	div.f	d15,d15,d0
.L705:
	ftoiz	d15,d15
.L393:
	ld.w	d0,[a15]22
.L706:
	add	d0,#-1
.L707:
	and	d15,d0
.L708:
	st.w	[a15],d15
.L709:
	ld.a	a2,[a15]60
.L710:
	ld.h	d4,[a2]
.L711:
	ld.a	a2,[a15]64
.L712:
	ld.h	d5,[a2]
	mov.aa	a4,a15
.L394:
	call	Ifx_AngleTrkF32_updateStatus
.L392:
	ret
.L326:
	
__Ifx_AngleTrkF32_update_function_end:
	.size	Ifx_AngleTrkF32_update,__Ifx_AngleTrkF32_update_function_end-Ifx_AngleTrkF32_update
.L204:
	; End of function
	
	.sdecl	'.text.Ifx_AngleTrkF32.Ifx_AngleTrkF32_getRawPosition',code,cluster('Ifx_AngleTrkF32_getRawPosition')
	.sect	'.text.Ifx_AngleTrkF32.Ifx_AngleTrkF32_getRawPosition'
	.align	2
	
	.global	Ifx_AngleTrkF32_getRawPosition
; Function Ifx_AngleTrkF32_getRawPosition
.L65:
Ifx_AngleTrkF32_getRawPosition:	.type	func
	ld.w	d2,[a4]
.L626:
	j	.L32
.L32:
	ret
.L295:
	
__Ifx_AngleTrkF32_getRawPosition_function_end:
	.size	Ifx_AngleTrkF32_getRawPosition,__Ifx_AngleTrkF32_getRawPosition_function_end-Ifx_AngleTrkF32_getRawPosition
.L154:
	; End of function
	
	.sdecl	'.text.Ifx_AngleTrkF32.Ifx_AngleTrkF32_getSpeed',code,cluster('Ifx_AngleTrkF32_getSpeed')
	.sect	'.text.Ifx_AngleTrkF32.Ifx_AngleTrkF32_getSpeed'
	.align	2
	
	.global	Ifx_AngleTrkF32_getSpeed
; Function Ifx_AngleTrkF32_getSpeed
.L67:
Ifx_AngleTrkF32_getSpeed:	.type	func
	ld.w	d2,[a4]108
.L651:
	j	.L33
.L33:
	ret
.L307:
	
__Ifx_AngleTrkF32_getSpeed_function_end:
	.size	Ifx_AngleTrkF32_getSpeed,__Ifx_AngleTrkF32_getSpeed_function_end-Ifx_AngleTrkF32_getSpeed
.L179:
	; End of function
	
	.sdecl	'.text.Ifx_AngleTrkF32.Ifx_AngleTrkF32_getAbsolutePosition',code,cluster('Ifx_AngleTrkF32_getAbsolutePosition')
	.sect	'.text.Ifx_AngleTrkF32.Ifx_AngleTrkF32_getAbsolutePosition'
	.align	2
	
	.global	Ifx_AngleTrkF32_getAbsolutePosition
; Function Ifx_AngleTrkF32_getAbsolutePosition
.L69:
Ifx_AngleTrkF32_getAbsolutePosition:	.type	func
	ld.w	d15,[a4]4
.L583:
	itof	d15,d15
.L584:
	ld.w	d0,[a4]
.L585:
	itof	d0,d0
.L586:
	ld.w	d1,[a4]22
.L587:
	itof	d1,d1
.L588:
	div.f	d0,d0,d1
.L589:
	add.f	d4,d15,d0
	call	__f_ftod
.L395:
	mov	d6,#0
	mov	d7,#0
	addih	d7,d7,#16384
.L590:
	mov	e4,d3,d2
	call	__d_mul
.L591:
	movh.a	a15,#@his(.1.cnt)
	lea	a15,[a15]@los(.1.cnt)
	ld.d	e6,[a15]0
.L592:
	mov	e4,d3,d2
	call	__d_mul
	mov	e4,d3,d2
	call	__d_dtof
.L593:
	j	.L34
.L34:
	ret
.L280:
	
__Ifx_AngleTrkF32_getAbsolutePosition_function_end:
	.size	Ifx_AngleTrkF32_getAbsolutePosition,__Ifx_AngleTrkF32_getAbsolutePosition_function_end-Ifx_AngleTrkF32_getAbsolutePosition
.L124:
	; End of function
	
	.sdecl	'.text.Ifx_AngleTrkF32.Ifx_AngleTrkF32_getDirection',code,cluster('Ifx_AngleTrkF32_getDirection')
	.sect	'.text.Ifx_AngleTrkF32.Ifx_AngleTrkF32_getDirection'
	.align	2
	
	.global	Ifx_AngleTrkF32_getDirection
; Function Ifx_AngleTrkF32_getDirection
.L71:
Ifx_AngleTrkF32_getDirection:	.type	func
	ld.bu	d2,[a4]8
.L598:
	j	.L35
.L35:
	ret
.L283:
	
__Ifx_AngleTrkF32_getDirection_function_end:
	.size	Ifx_AngleTrkF32_getDirection,__Ifx_AngleTrkF32_getDirection_function_end-Ifx_AngleTrkF32_getDirection
.L129:
	; End of function
	
	.sdecl	'.text.Ifx_AngleTrkF32.Ifx_AngleTrkF32_getFault',code,cluster('Ifx_AngleTrkF32_getFault')
	.sect	'.text.Ifx_AngleTrkF32.Ifx_AngleTrkF32_getFault'
	.align	2
	
	.global	Ifx_AngleTrkF32_getFault
; Function Ifx_AngleTrkF32_getFault
.L73:
Ifx_AngleTrkF32_getFault:	.type	func
	ld.w	d2,[a4]10
.L603:
	j	.L36
.L36:
	ret
.L286:
	
__Ifx_AngleTrkF32_getFault_function_end:
	.size	Ifx_AngleTrkF32_getFault,__Ifx_AngleTrkF32_getFault_function_end-Ifx_AngleTrkF32_getFault
.L134:
	; End of function
	
	.sdecl	'.text.Ifx_AngleTrkF32.Ifx_AngleTrkF32_getOffset',code,cluster('Ifx_AngleTrkF32_getOffset')
	.sect	'.text.Ifx_AngleTrkF32.Ifx_AngleTrkF32_getOffset'
	.align	2
	
	.global	Ifx_AngleTrkF32_getOffset
; Function Ifx_AngleTrkF32_getOffset
.L75:
Ifx_AngleTrkF32_getOffset:	.type	func
	ld.w	d2,[a4]14
.L608:
	j	.L37
.L37:
	ret
.L288:
	
__Ifx_AngleTrkF32_getOffset_function_end:
	.size	Ifx_AngleTrkF32_getOffset,__Ifx_AngleTrkF32_getOffset_function_end-Ifx_AngleTrkF32_getOffset
.L139:
	; End of function
	
	.sdecl	'.text.Ifx_AngleTrkF32.Ifx_AngleTrkF32_getPeriodPerRotation',code,cluster('Ifx_AngleTrkF32_getPeriodPerRotation')
	.sect	'.text.Ifx_AngleTrkF32.Ifx_AngleTrkF32_getPeriodPerRotation'
	.align	2
	
	.global	Ifx_AngleTrkF32_getPeriodPerRotation
; Function Ifx_AngleTrkF32_getPeriodPerRotation
.L77:
Ifx_AngleTrkF32_getPeriodPerRotation:	.type	func
	ld.hu	d2,[a4]20
.L613:
	j	.L38
.L38:
	ret
.L291:
	
__Ifx_AngleTrkF32_getPeriodPerRotation_function_end:
	.size	Ifx_AngleTrkF32_getPeriodPerRotation,__Ifx_AngleTrkF32_getPeriodPerRotation_function_end-Ifx_AngleTrkF32_getPeriodPerRotation
.L144:
	; End of function
	
	.sdecl	'.text.Ifx_AngleTrkF32.Ifx_AngleTrkF32_getPosition',code,cluster('Ifx_AngleTrkF32_getPosition')
	.sect	'.text.Ifx_AngleTrkF32.Ifx_AngleTrkF32_getPosition'
	.align	2
	
	.global	Ifx_AngleTrkF32_getPosition
; Function Ifx_AngleTrkF32_getPosition
.L79:
Ifx_AngleTrkF32_getPosition:	.type	func
	ld.w	d15,[a4]
.L618:
	itof	d15,d15
.L619:
	ld.w	d0,[a4]30
.L620:
	mul.f	d2,d15,d0
.L621:
	j	.L39
.L39:
	ret
.L293:
	
__Ifx_AngleTrkF32_getPosition_function_end:
	.size	Ifx_AngleTrkF32_getPosition,__Ifx_AngleTrkF32_getPosition_function_end-Ifx_AngleTrkF32_getPosition
.L149:
	; End of function
	
	.sdecl	'.text.Ifx_AngleTrkF32.Ifx_AngleTrkF32_getRefreshPeriod',code,cluster('Ifx_AngleTrkF32_getRefreshPeriod')
	.sect	'.text.Ifx_AngleTrkF32.Ifx_AngleTrkF32_getRefreshPeriod'
	.align	2
	
	.global	Ifx_AngleTrkF32_getRefreshPeriod
; Function Ifx_AngleTrkF32_getRefreshPeriod
.L81:
Ifx_AngleTrkF32_getRefreshPeriod:	.type	func
	ld.w	d2,[a4]26
.L631:
	j	.L40
.L40:
	ret
.L298:
	
__Ifx_AngleTrkF32_getRefreshPeriod_function_end:
	.size	Ifx_AngleTrkF32_getRefreshPeriod,__Ifx_AngleTrkF32_getRefreshPeriod_function_end-Ifx_AngleTrkF32_getRefreshPeriod
.L159:
	; End of function
	
	.sdecl	'.text.Ifx_AngleTrkF32.Ifx_AngleTrkF32_getResolution',code,cluster('Ifx_AngleTrkF32_getResolution')
	.sect	'.text.Ifx_AngleTrkF32.Ifx_AngleTrkF32_getResolution'
	.align	2
	
	.global	Ifx_AngleTrkF32_getResolution
; Function Ifx_AngleTrkF32_getResolution
.L83:
Ifx_AngleTrkF32_getResolution:	.type	func
	ld.w	d2,[a4]22
.L636:
	j	.L41
.L41:
	ret
.L300:
	
__Ifx_AngleTrkF32_getResolution_function_end:
	.size	Ifx_AngleTrkF32_getResolution,__Ifx_AngleTrkF32_getResolution_function_end-Ifx_AngleTrkF32_getResolution
.L164:
	; End of function
	
	.sdecl	'.text.Ifx_AngleTrkF32.Ifx_AngleTrkF32_getSensorType',code,cluster('Ifx_AngleTrkF32_getSensorType')
	.sect	'.text.Ifx_AngleTrkF32.Ifx_AngleTrkF32_getSensorType'
	.align	2
	
	.global	Ifx_AngleTrkF32_getSensorType
; Function Ifx_AngleTrkF32_getSensorType
.L85:
Ifx_AngleTrkF32_getSensorType:	.type	func
	jz.a	a4,.L42
.L42:
	mov	d2,#3
.L646:
	j	.L43
.L43:
	ret
.L305:
	
__Ifx_AngleTrkF32_getSensorType_function_end:
	.size	Ifx_AngleTrkF32_getSensorType,__Ifx_AngleTrkF32_getSensorType_function_end-Ifx_AngleTrkF32_getSensorType
.L174:
	; End of function
	
	.sdecl	'.text.Ifx_AngleTrkF32.Ifx_AngleTrkF32_getTurn',code,cluster('Ifx_AngleTrkF32_getTurn')
	.sect	'.text.Ifx_AngleTrkF32.Ifx_AngleTrkF32_getTurn'
	.align	2
	
	.global	Ifx_AngleTrkF32_getTurn
; Function Ifx_AngleTrkF32_getTurn
.L87:
Ifx_AngleTrkF32_getTurn:	.type	func
	ld.w	d2,[a4]4
.L641:
	j	.L44
.L44:
	ret
.L302:
	
__Ifx_AngleTrkF32_getTurn_function_end:
	.size	Ifx_AngleTrkF32_getTurn,__Ifx_AngleTrkF32_getTurn_function_end-Ifx_AngleTrkF32_getTurn
.L169:
	; End of function
	
	.sdecl	'.text.Ifx_AngleTrkF32.Ifx_AngleTrkF32_resetFaults',code,cluster('Ifx_AngleTrkF32_resetFaults')
	.sect	'.text.Ifx_AngleTrkF32.Ifx_AngleTrkF32_resetFaults'
	.align	2
	
	.global	Ifx_AngleTrkF32_resetFaults
; Function Ifx_AngleTrkF32_resetFaults
.L89:
Ifx_AngleTrkF32_resetFaults:	.type	func
	mov	d15,#0
.L680:
	st.w	[a4]10,d15
.L681:
	ret
.L317:
	
__Ifx_AngleTrkF32_resetFaults_function_end:
	.size	Ifx_AngleTrkF32_resetFaults,__Ifx_AngleTrkF32_resetFaults_function_end-Ifx_AngleTrkF32_resetFaults
.L189:
	; End of function
	
	.sdecl	'.text.Ifx_AngleTrkF32.Ifx_AngleTrkF32_setRefreshPeriod',code,cluster('Ifx_AngleTrkF32_setRefreshPeriod')
	.sect	'.text.Ifx_AngleTrkF32.Ifx_AngleTrkF32_setRefreshPeriod'
	.align	2
	
	.global	Ifx_AngleTrkF32_setRefreshPeriod
; Function Ifx_AngleTrkF32_setRefreshPeriod
.L91:
Ifx_AngleTrkF32_setRefreshPeriod:	.type	func
	st.w	[a4]26,d4
.L691:
	ret
.L323:
	
__Ifx_AngleTrkF32_setRefreshPeriod_function_end:
	.size	Ifx_AngleTrkF32_setRefreshPeriod,__Ifx_AngleTrkF32_setRefreshPeriod_function_end-Ifx_AngleTrkF32_setRefreshPeriod
.L199:
	; End of function
	
	.sdecl	'.text.Ifx_AngleTrkF32.Ifx_AngleTrkF32_reset',code,cluster('Ifx_AngleTrkF32_reset')
	.sect	'.text.Ifx_AngleTrkF32.Ifx_AngleTrkF32_reset'
	.align	2
	
	.global	Ifx_AngleTrkF32_reset
; Function Ifx_AngleTrkF32_reset
.L93:
Ifx_AngleTrkF32_reset:	.type	func
	mov	d15,#0
.L656:
	st.w	[a4],d15
.L657:
	mov	d15,#2
.L658:
	st.b	[a4]8,d15
.L659:
	mov	d15,#0
.L660:
	st.w	[a4]4,d15
.L661:
	mov	d15,#0
.L662:
	st.w	[a4]96,d15
.L663:
	mov	d15,#0
.L664:
	st.w	[a4]80,d15
.L665:
	mov	d15,#0
.L666:
	st.w	[a4]92,d15
.L667:
	mov	d15,#0
.L668:
	st.w	[a4]84,d15
.L669:
	mov	d15,#0
.L670:
	st.w	[a4]88,d15
.L671:
	mov	d15,#0
.L672:
	st.w	[a4]76,d15
.L312:
	mov	d15,#0
.L673:
	st.w	[a4]108,d15
.L313:
	mov	d15,#0
.L674:
	st.w	[a4]10,d15
.L675:
	ret
.L309:
	
__Ifx_AngleTrkF32_reset_function_end:
	.size	Ifx_AngleTrkF32_reset,__Ifx_AngleTrkF32_reset_function_end-Ifx_AngleTrkF32_reset
.L184:
	; End of function
	
	.sdecl	'.text.Ifx_AngleTrkF32.Ifx_AngleTrkF32_stdIfPosInit',code,cluster('Ifx_AngleTrkF32_stdIfPosInit')
	.sect	'.text.Ifx_AngleTrkF32.Ifx_AngleTrkF32_stdIfPosInit'
	.align	2
	
	.global	Ifx_AngleTrkF32_stdIfPosInit
; Function Ifx_AngleTrkF32_stdIfPosInit
.L95:
Ifx_AngleTrkF32_stdIfPosInit:	.type	func
	mov.aa	a15,a4
.L397:
	mov.aa	a12,a5
.L398:
	mov	d4,#0
.L717:
	mov	d5,#92
	mov.aa	a4,a15
	call	memset
.L396:
	st.a	[a15],a12
.L718:
	mov.a	a2,#0
.L719:
	st.a	[a15]4,a2
.L720:
	movh.a	a2,#@his(Ifx_AngleTrkF32_getAbsolutePosition)
	lea	a2,[a2]@los(Ifx_AngleTrkF32_getAbsolutePosition)
.L721:
	st.a	[a15]8,a2
.L722:
	movh.a	a2,#@his(Ifx_AngleTrkF32_getDirection)
	lea	a2,[a2]@los(Ifx_AngleTrkF32_getDirection)
.L723:
	st.a	[a15]20,a2
.L724:
	movh.a	a2,#@his(Ifx_AngleTrkF32_getFault)
	lea	a2,[a2]@los(Ifx_AngleTrkF32_getFault)
.L725:
	st.a	[a15]24,a2
.L726:
	movh.a	a2,#@his(Ifx_AngleTrkF32_getOffset)
	lea	a2,[a2]@los(Ifx_AngleTrkF32_getOffset)
.L727:
	st.a	[a15]12,a2
.L728:
	movh.a	a2,#@his(Ifx_AngleTrkF32_getPeriodPerRotation)
	lea	a2,[a2]@los(Ifx_AngleTrkF32_getPeriodPerRotation)
.L729:
	st.a	[a15]32,a2
.L730:
	movh.a	a2,#@his(Ifx_AngleTrkF32_getPosition)
	lea	a2,[a2]@los(Ifx_AngleTrkF32_getPosition)
.L731:
	st.a	[a15]16,a2
.L732:
	movh.a	a2,#@his(Ifx_AngleTrkF32_getRawPosition)
	lea	a2,[a2]@los(Ifx_AngleTrkF32_getRawPosition)
.L733:
	st.a	[a15]28,a2
.L734:
	movh.a	a2,#@his(Ifx_AngleTrkF32_getRefreshPeriod)
	lea	a2,[a2]@los(Ifx_AngleTrkF32_getRefreshPeriod)
.L735:
	st.a	[a15]36,a2
.L736:
	movh.a	a2,#@his(Ifx_AngleTrkF32_getResolution)
	lea	a2,[a2]@los(Ifx_AngleTrkF32_getResolution)
.L737:
	st.a	[a15]40,a2
.L738:
	movh.a	a2,#@his(Ifx_AngleTrkF32_getSensorType)
	lea	a2,[a2]@los(Ifx_AngleTrkF32_getSensorType)
.L739:
	st.a	[a15]44,a2
.L740:
	movh.a	a2,#@his(Ifx_AngleTrkF32_reset)
	lea	a2,[a2]@los(Ifx_AngleTrkF32_reset)
.L741:
	st.a	[a15]56,a2
.L742:
	movh.a	a2,#@his(Ifx_AngleTrkF32_resetFaults)
	lea	a2,[a2]@los(Ifx_AngleTrkF32_resetFaults)
.L743:
	st.a	[a15]60,a2
.L744:
	movh.a	a2,#@his(Ifx_AngleTrkF32_getSpeed)
	lea	a2,[a2]@los(Ifx_AngleTrkF32_getSpeed)
.L745:
	st.a	[a15]64,a2
.L746:
	movh.a	a2,#@his(Ifx_AngleTrkF32_update)
	lea	a2,[a2]@los(Ifx_AngleTrkF32_update)
.L747:
	st.a	[a15]68,a2
.L748:
	mov.a	a2,#0
.L749:
	st.a	[a15]76,a2
.L750:
	mov.a	a2,#0
.L751:
	st.a	[a15]80,a2
.L752:
	mov.a	a2,#0
.L753:
	st.a	[a15]84,a2
.L754:
	movh.a	a2,#@his(Ifx_AngleTrkF32_setOffset)
	lea	a2,[a2]@los(Ifx_AngleTrkF32_setOffset)
.L755:
	st.a	[a15]72,a2
.L756:
	movh.a	a2,#@his(Ifx_AngleTrkF32_setRefreshPeriod)
	lea	a2,[a2]@los(Ifx_AngleTrkF32_setRefreshPeriod)
.L757:
	st.a	[a15]88,a2
.L758:
	movh.a	a2,#@his(Ifx_AngleTrkF32_getTurn)
	lea	a2,[a2]@los(Ifx_AngleTrkF32_getTurn)
.L759:
	st.a	[a15]48,a2
.L760:
	mov	d2,#1
.L761:
	j	.L45
.L45:
	ret
.L332:
	
__Ifx_AngleTrkF32_stdIfPosInit_function_end:
	.size	Ifx_AngleTrkF32_stdIfPosInit,__Ifx_AngleTrkF32_stdIfPosInit_function_end-Ifx_AngleTrkF32_stdIfPosInit
.L209:
	; End of function
	
	.calls	'round_f','__f_ftod'
	.calls	'round_f','__d_fgt'
	.calls	'Ifx_AngleTrkF32_init','__f_ftod'
	.calls	'Ifx_AngleTrkF32_init','__d_div'
	.calls	'Ifx_AngleTrkF32_init','__d_mul'
	.calls	'Ifx_AngleTrkF32_init','__d_dtof'
	.calls	'Ifx_AngleTrkF32_updateStatus','__f_ftod'
	.calls	'Ifx_AngleTrkF32_updateStatus','__d_fgt'
	.calls	'Ifx_AngleTrkF32_getAbsolutePosition','__f_ftod'
	.calls	'Ifx_AngleTrkF32_getAbsolutePosition','__d_mul'
	.calls	'Ifx_AngleTrkF32_getAbsolutePosition','__d_dtof'
	.calls	'__INDIRECT__','Ifx_AngleTrkF32_getAbsolutePosition'
	.calls	'__INDIRECT__','Ifx_AngleTrkF32_getDirection'
	.calls	'__INDIRECT__','Ifx_AngleTrkF32_getFault'
	.calls	'__INDIRECT__','Ifx_AngleTrkF32_getOffset'
	.calls	'__INDIRECT__','Ifx_AngleTrkF32_getPeriodPerRotation'
	.calls	'__INDIRECT__','Ifx_AngleTrkF32_getPosition'
	.calls	'__INDIRECT__','Ifx_AngleTrkF32_getRawPosition'
	.calls	'__INDIRECT__','Ifx_AngleTrkF32_getRefreshPeriod'
	.calls	'__INDIRECT__','Ifx_AngleTrkF32_getResolution'
	.calls	'__INDIRECT__','Ifx_AngleTrkF32_getTurn'
	.calls	'__INDIRECT__','Ifx_AngleTrkF32_getSensorType'
	.calls	'__INDIRECT__','Ifx_AngleTrkF32_getSpeed'
	.calls	'__INDIRECT__','Ifx_AngleTrkF32_reset'
	.calls	'__INDIRECT__','Ifx_AngleTrkF32_resetFaults'
	.calls	'__INDIRECT__','Ifx_AngleTrkF32_setOffset'
	.calls	'__INDIRECT__','Ifx_AngleTrkF32_setRefreshPeriod'
	.calls	'__INDIRECT__','Ifx_AngleTrkF32_update'
	.calls	'Ifx_AngleTrkF32_initConfig','Ifx_AngleTrkF32_setControlGains'
	.calls	'Ifx_AngleTrkF32_init','Ifx_AngleTrkF32_setControlGains'
	.calls	'Ifx_AngleTrkF32_init','Ifx_AngleTrkF32_setUserSampling'
	.calls	'Ifx_AngleTrkF32_init','Ifx_LowPassPt1F32_init'
	.calls	'Ifx_AngleTrkF32_step','Ifx_LutAtan2F32_float32'
	.calls	'Ifx_AngleTrkF32_step','Ifx_LowPassPt1F32_do'
	.calls	'Ifx_AngleTrkF32_update','Ifx_AngleTrkF32_step'
	.calls	'Ifx_AngleTrkF32_update','Ifx_AngleTrkF32_updateStatus'
	.calls	'Ifx_AngleTrkF32_stdIfPosInit','memset'
	.calls	'round_f','',0
	.calls	'Ifx_AngleTrkF32_setControlGains','',0
	.calls	'Ifx_AngleTrkF32_initConfig','',24
	.calls	'Ifx_AngleTrkF32_setUserSampling','',0
	.calls	'Ifx_AngleTrkF32_init','',16
	.calls	'Ifx_AngleTrkF32_step','',0
	.calls	'Ifx_AngleTrkF32_setOffset','',0
	.calls	'Ifx_AngleTrkF32_updateStatus','',0
	.calls	'Ifx_AngleTrkF32_update','',0
	.calls	'Ifx_AngleTrkF32_getRawPosition','',0
	.calls	'Ifx_AngleTrkF32_getSpeed','',0
	.calls	'Ifx_AngleTrkF32_getAbsolutePosition','',0
	.calls	'Ifx_AngleTrkF32_getDirection','',0
	.calls	'Ifx_AngleTrkF32_getFault','',0
	.calls	'Ifx_AngleTrkF32_getOffset','',0
	.calls	'Ifx_AngleTrkF32_getPeriodPerRotation','',0
	.calls	'Ifx_AngleTrkF32_getPosition','',0
	.calls	'Ifx_AngleTrkF32_getRefreshPeriod','',0
	.calls	'Ifx_AngleTrkF32_getResolution','',0
	.calls	'Ifx_AngleTrkF32_getSensorType','',0
	.calls	'Ifx_AngleTrkF32_getTurn','',0
	.calls	'Ifx_AngleTrkF32_resetFaults','',0
	.calls	'Ifx_AngleTrkF32_setRefreshPeriod','',0
	.calls	'Ifx_AngleTrkF32_reset','',0
	.extern	Ifx_LowPassPt1F32_init
	.extern	Ifx_LowPassPt1F32_do
	.extern	memset
	.extern	Ifx_LutAtan2F32_float32
	.extern	__f_ftod
	.extern	__d_fgt
	.extern	__d_div
	.extern	__d_mul
	.extern	__d_dtof
	.extern	__INDIRECT__
	.calls	'Ifx_AngleTrkF32_stdIfPosInit','',0
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L97:
	.word	6260
	.half	3
	.word	.L98
	.byte	4
.L96:
	.byte	1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_AngleTrkF32.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L99
	.byte	2
	.byte	'unsigned long int',0,4,7
.L331:
	.byte	2
	.byte	'unsigned char',0,1,8,3,1,106,5,1,4
	.byte	'notSynchronised',0,1
	.word	263
	.byte	1,7,2,35,0,4
	.byte	'signalLoss',0,1
	.word	263
	.byte	1,6,2,35,0,4
	.byte	'signalDegradation',0,1
	.word	263
	.byte	1,5,2,35,0,4
	.byte	'trackingLoss',0,1
	.word	263
	.byte	1,4,2,35,0,4
	.byte	'commError',0,1
	.word	263
	.byte	1,3,2,35,0,0
.L285:
	.byte	5,1,103,9,4,6
	.byte	'status',0
	.word	242
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	280
	.byte	1,2,35,0,0,7
	.byte	'void',0,8
	.word	442
	.byte	9
	.byte	'IfxStdIf_InterfaceDriver',0,2,118,15
	.word	448
	.byte	10,1,1,11
	.word	448
	.byte	0,8
	.word	486
	.byte	9
	.byte	'IfxStdIf_Pos_OnZeroIrq',0,1,135,1,16
	.word	495
.L230:
	.byte	2
	.byte	'float',0,4,4,12
	.word	532
	.byte	1,1,11
	.word	448
	.byte	0,8
	.word	541
	.byte	9
	.byte	'IfxStdIf_Pos_GetAbsolutePosition',0,1,129,1,19
	.word	554
.L278:
	.byte	2
	.byte	'long int',0,4,5,12
	.word	601
	.byte	1,1,11
	.word	448
	.byte	0,8
	.word	613
	.byte	9
	.byte	'IfxStdIf_Pos_GetOffset',0,1,142,1,18
	.word	626
	.byte	9
	.byte	'IfxStdIf_Pos_GetPosition',0,1,152,1,19
	.word	554
.L282:
	.byte	13,1,95,9,1,14
	.byte	'IfxStdIf_Pos_Dir_forward',0,0,14
	.byte	'IfxStdIf_Pos_Dir_backward',0,1,14
	.byte	'IfxStdIf_Pos_Dir_unknown',0,2,0,12
	.word	697
	.byte	1,1,11
	.word	448
	.byte	0,8
	.word	785
	.byte	9
	.byte	'IfxStdIf_Pos_GetDirection',0,1,161,1,28
	.word	798
	.byte	12
	.word	409
	.byte	1,1,11
	.word	448
	.byte	0,8
	.word	838
	.byte	9
	.byte	'IfxStdIf_Pos_GetFault',0,1,168,1,31
	.word	851
	.byte	9
	.byte	'IfxStdIf_Pos_GetRawPosition',0,1,184,1,18
	.word	626
.L290:
	.byte	2
	.byte	'unsigned short int',0,2,7,12
	.word	924
	.byte	1,1,11
	.word	448
	.byte	0,8
	.word	946
	.byte	9
	.byte	'IfxStdIf_Pos_GetPeriodPerRotation',0,1,175,1,18
	.word	959
	.byte	9
	.byte	'IfxStdIf_Pos_GetRefreshPeriod',0,1,190,1,19
	.word	554
	.byte	9
	.byte	'IfxStdIf_Pos_GetResolution',0,1,196,1,18
	.word	626
.L304:
	.byte	13,1,84,9,1,14
	.byte	'IfxStdIf_Pos_SensorType_encoder',0,0,14
	.byte	'IfxStdIf_Pos_SensorType_hall',0,1,14
	.byte	'IfxStdIf_Pos_SensorType_resolver',0,2,14
	.byte	'IfxStdIf_Pos_SensorType_angletrk',0,3,14
	.byte	'IfxStdIf_Pos_SensorType_igmr',0,4,14
	.byte	'IfxStdIf_Pos_SensorType_virtual',0,5,0,12
	.word	1082
	.byte	1,1,11
	.word	448
	.byte	0,8
	.word	1288
	.byte	9
	.byte	'IfxStdIf_Pos_GetSensorType',0,1,202,1,35
	.word	1301
	.byte	9
	.byte	'IfxStdIf_Pos_GetTurn',0,1,214,1,18
	.word	626
	.byte	9
	.byte	'IfxStdIf_Pos_OnEventA',0,1,221,1,16
	.word	495
	.byte	9
	.byte	'IfxStdIf_Pos_Reset',0,1,239,1,16
	.word	495
	.byte	9
	.byte	'IfxStdIf_Pos_ResetFaults',0,1,248,1,16
	.word	495
	.byte	9
	.byte	'IfxStdIf_Pos_GetSpeed',0,1,208,1,19
	.word	554
	.byte	9
	.byte	'IfxStdIf_Pos_Update',0,1,230,1,16
	.word	495
	.byte	10,1,1,11
	.word	448
	.byte	11
	.word	601
	.byte	0,8
	.word	1525
	.byte	9
	.byte	'IfxStdIf_Pos_SetOffset',0,1,255,1,16
	.word	1539
	.byte	10,1,1,11
	.word	448
	.byte	11
	.word	532
	.byte	0,8
	.word	1576
	.byte	9
	.byte	'IfxStdIf_Pos_SetPosition',0,1,134,2,16
	.word	1590
	.byte	9
	.byte	'IfxStdIf_Pos_SetRawPosition',0,1,140,2,16
	.word	1539
	.byte	9
	.byte	'IfxStdIf_Pos_SetSpeed',0,1,147,2,16
	.word	1590
	.byte	9
	.byte	'IfxStdIf_Pos_SetRefreshPeriod',0,1,154,2,16
	.word	1590
	.byte	15
	.byte	'IfxStdIf_Pos_',0,1,158,2,8,92,6
	.byte	'driver',0
	.word	453
	.byte	4,2,35,0,6
	.byte	'onZeroIrq',0
	.word	500
	.byte	4,2,35,4,6
	.byte	'getAbsolutePosition',0
	.word	559
	.byte	4,2,35,8,6
	.byte	'getOffset',0
	.word	631
	.byte	4,2,35,12,6
	.byte	'getPosition',0
	.word	663
	.byte	4,2,35,16,6
	.byte	'getDirection',0
	.word	803
	.byte	4,2,35,20,6
	.byte	'getFault',0
	.word	856
	.byte	4,2,35,24,6
	.byte	'getRawPosition',0
	.word	887
	.byte	4,2,35,28,6
	.byte	'getPeriodPerRotation',0
	.word	964
	.byte	4,2,35,32,6
	.byte	'getRefreshPeriod',0
	.word	1007
	.byte	4,2,35,36,6
	.byte	'getResolution',0
	.word	1046
	.byte	4,2,35,40,6
	.byte	'getSensorType',0
	.word	1306
	.byte	4,2,35,44,6
	.byte	'getTurn',0
	.word	1342
	.byte	4,2,35,48,6
	.byte	'onEventA',0
	.word	1372
	.byte	4,2,35,52,6
	.byte	'reset',0
	.word	1403
	.byte	4,2,35,56,6
	.byte	'resetFaults',0
	.word	1431
	.byte	4,2,35,60,6
	.byte	'getSpeed',0
	.word	1465
	.byte	4,2,35,64,6
	.byte	'update',0
	.word	1496
	.byte	4,2,35,68,6
	.byte	'setOffset',0
	.word	1544
	.byte	4,2,35,72,6
	.byte	'setPosition',0
	.word	1595
	.byte	4,2,35,76,6
	.byte	'setRawPosition',0
	.word	1629
	.byte	4,2,35,80,6
	.byte	'setSpeed',0
	.word	1666
	.byte	4,2,35,84,6
	.byte	'setRefreshPeriod',0
	.word	1697
	.byte	4,2,35,88,0
.L333:
	.byte	8
	.word	1736
	.byte	16
	.byte	'IfxStdIf_Pos_getFault',0,3,1,225,2,32
	.word	409
	.byte	1,1,17
	.byte	'stdIf',0,1,225,2,68
	.word	2240
	.byte	18,0,8
	.word	442
	.byte	8
	.word	486
	.byte	8
	.word	541
	.byte	8
	.word	613
	.byte	8
	.word	541
	.byte	8
	.word	785
	.byte	8
	.word	838
	.byte	8
	.word	613
	.byte	8
	.word	946
	.byte	8
	.word	541
	.byte	8
	.word	613
	.byte	8
	.word	1288
	.byte	8
	.word	613
	.byte	8
	.word	486
	.byte	8
	.word	486
	.byte	8
	.word	486
	.byte	8
	.word	541
	.byte	8
	.word	486
	.byte	8
	.word	1525
	.byte	8
	.word	1576
	.byte	8
	.word	1525
	.byte	8
	.word	1576
	.byte	8
	.word	1576
	.byte	3,3,64,9,12,6
	.byte	'a',0
	.word	532
	.byte	4,2,35,0,6
	.byte	'b',0
	.word	532
	.byte	4,2,35,4,6
	.byte	'out',0
	.word	532
	.byte	4,2,35,8,0,8
	.word	2411
.L311:
	.byte	19
	.byte	'Ifx_LowPassPt1F32_reset',0,3,3,93,17,1,1
.L314:
	.byte	17
	.byte	'filter',0,3,93,60
	.word	2452
.L316:
	.byte	18,0,20,1,1,8
	.word	2505
	.byte	19
	.byte	'Ifx__jump_and_link',0,3,4,61,17,1,1,17
	.byte	'fun',0,4,61,43
	.word	2508
	.byte	18,0,2
	.byte	'__fract',0,4,128,1,16
	.byte	'Ifx__float_to_fract',0,3,4,152,2,18
	.word	2553
	.byte	1,1,17
	.byte	'a',0,4,152,2,44
	.word	532
	.byte	18,0,2
	.byte	'unsigned long long int',0,8,7,16
	.byte	'__ld64',0,3,5,135,1,19
	.word	2610
	.byte	1,1,17
	.byte	'addr',0,5,135,1,32
	.word	448
	.byte	18,0,19
	.byte	'__st64',0,3,5,143,1,17,1,1,17
	.byte	'addr',0,5,143,1,30
	.word	448
	.byte	17
	.byte	'value',0,5,143,1,43
	.word	2610
	.byte	18,0
.L260:
	.byte	16
	.byte	'Ifx_AngleTrkF32_bound',0,3,6,73,20
	.word	532
	.byte	1,1
.L262:
	.byte	17
	.byte	'angle',0,6,73,50
	.word	532
.L264:
	.byte	17
	.byte	'periodPerRotation',0,6,73,65
	.word	532
.L266:
	.byte	21
.L267:
	.byte	18,0,0
.L255:
	.byte	16
	.byte	'Ifx_AngleTrkF32_boundInput',0,3,6,93,20
	.word	532
	.byte	1,1
.L257:
	.byte	17
	.byte	'input',0,6,93,55
	.word	532
.L259:
	.byte	21,18,0,0,22
	.word	2245
	.byte	23
	.word	2279
	.byte	18,0
.L234:
	.byte	3,3,72,9,12,6
	.byte	'cutOffFrequency',0
	.word	532
	.byte	4,2,35,0,6
	.byte	'gain',0
	.word	532
	.byte	4,2,35,4,6
	.byte	'samplingTime',0
	.word	532
	.byte	4,2,35,8,0,24
	.word	2862
	.byte	8
	.word	2929
	.byte	25
	.byte	'Ifx_LowPassPt1F32_init',0,3,83,20,1,1,1,1,17
	.byte	'filter',0,3,83,62
	.word	2452
	.byte	17
	.byte	'config',0,3,83,102
	.word	2934
	.byte	0,22
	.word	2457
	.byte	23
	.word	2488
	.byte	18,0,26
	.byte	'Ifx_LowPassPt1F32_do',0,3,85,20
	.word	532
	.byte	1,1,1,1,17
	.byte	'filter',0,3,85,60
	.word	2452
	.byte	17
	.byte	'input',0,3,85,76
	.word	532
	.byte	0,3,7,103,9,36,6
	.byte	'rawPosition',0
	.word	601
	.byte	4,2,35,0,6
	.byte	'turn',0
	.word	601
	.byte	4,2,35,4,6
	.byte	'direction',0
	.word	697
	.byte	1,2,35,8,6
	.byte	'status',0
	.word	409
	.byte	4,2,35,10,6
	.byte	'offset',0
	.word	601
	.byte	4,2,35,14,6
	.byte	'reversed',0
	.word	263
	.byte	1,2,35,18,6
	.byte	'periodPerRotation',0
	.word	924
	.byte	2,2,35,20,6
	.byte	'resolution',0
	.word	601
	.byte	4,2,35,22,6
	.byte	'Ts',0
	.word	532
	.byte	4,2,35,26,6
	.byte	'positionConst',0
	.word	532
	.byte	4,2,35,30,0
.L242:
	.byte	3,7,93,9,24,6
	.byte	'kp',0
	.word	532
	.byte	4,2,35,0,6
	.byte	'ki',0
	.word	532
	.byte	4,2,35,4,6
	.byte	'kd',0
	.word	532
	.byte	4,2,35,8,6
	.byte	'errorThreshold',0
	.word	532
	.byte	4,2,35,12,6
	.byte	'sqrAmplMax',0
	.word	601
	.byte	4,2,35,16,6
	.byte	'sqrAmplMin',0
	.word	601
	.byte	4,2,35,20,0
.L246:
	.byte	2
	.byte	'short int',0,2,5
.L239:
	.byte	8
	.word	3374
	.byte	3,7,119,9,112,6
	.byte	'base',0
	.word	3076
	.byte	36,2,35,0,6
	.byte	'cfgData',0
	.word	3268
	.byte	24,2,35,36,6
	.byte	'sinIn',0
	.word	3387
	.byte	4,2,35,60,6
	.byte	'cosIn',0
	.word	3387
	.byte	4,2,35,64,6
	.byte	'halfTs',0
	.word	532
	.byte	4,2,35,68,6
	.byte	'angleAtan',0
	.word	532
	.byte	4,2,35,72,6
	.byte	'angleRef',0
	.word	532
	.byte	4,2,35,76,6
	.byte	'angleEst',0
	.word	532
	.byte	4,2,35,80,6
	.byte	'speedEstA',0
	.word	532
	.byte	4,2,35,84,6
	.byte	'speedEstB',0
	.word	532
	.byte	4,2,35,88,6
	.byte	'accelEst',0
	.word	532
	.byte	4,2,35,92,6
	.byte	'angleErr',0
	.word	532
	.byte	4,2,35,96,6
	.byte	'speedLpf',0
	.word	2411
	.byte	12,2,35,100,0
.L226:
	.byte	8
	.word	3392
	.byte	3,7,76,9,48,6
	.byte	'kp',0
	.word	532
	.byte	4,2,35,0,6
	.byte	'ki',0
	.word	532
	.byte	4,2,35,4,6
	.byte	'kd',0
	.word	532
	.byte	4,2,35,8,6
	.byte	'speedLpfFc',0
	.word	532
	.byte	4,2,35,12,6
	.byte	'errorThreshold',0
	.word	532
	.byte	4,2,35,16,6
	.byte	'sqrAmplMax',0
	.word	601
	.byte	4,2,35,20,6
	.byte	'sqrAmplMin',0
	.word	601
	.byte	4,2,35,24,6
	.byte	'periodPerRotation',0
	.word	924
	.byte	2,2,35,28,6
	.byte	'reversed',0
	.word	263
	.byte	1,2,35,30,6
	.byte	'resolution',0
	.word	601
	.byte	4,2,35,32,6
	.byte	'offset',0
	.word	601
	.byte	4,2,35,36,6
	.byte	'sinIn',0
	.word	3387
	.byte	4,2,35,40,6
	.byte	'cosIn',0
	.word	3387
	.byte	4,2,35,44,0,24
	.word	3627
.L228:
	.byte	8
	.word	3864
.L237:
	.byte	8
	.word	3627
.L276:
	.byte	8
	.word	3076
	.byte	2
	.byte	'int',0,4,5,2
	.byte	'unsigned int',0,4,7,26
	.byte	'memset',0,8,56,17
	.word	448
	.byte	1,1,1,1,27,8,56,33
	.word	448
	.byte	27,8,56,36
	.word	3884
	.byte	27,8,56,41
	.word	3891
	.byte	0,22
	.word	2513
	.byte	23
	.word	2539
	.byte	18,0,22
	.word	2565
	.byte	23
	.word	2597
	.byte	18,0,22
	.word	2636
	.byte	23
	.word	2655
	.byte	18,0,22
	.word	2671
	.byte	23
	.word	2686
	.byte	23
	.word	2700
	.byte	18,0,26
	.byte	'Ifx_LutAtan2F32_float32',0,9,74,29
	.word	532
	.byte	1,1,1,1,17
	.byte	'y',0,9,74,61
	.word	532
	.byte	17
	.byte	'x',0,9,74,72
	.word	532
	.byte	0,22
	.word	2717
	.byte	23
	.word	2750
	.byte	23
	.word	2764
	.byte	21,18,0,0,22
	.word	2794
	.byte	23
	.word	2832
	.byte	21,28
	.word	2717
	.byte	23
	.word	2750
	.byte	23
	.word	2764
	.byte	29
	.word	2790
	.byte	30
	.word	2791
	.byte	0,0,18,0,0
.L343:
	.byte	8
	.word	3268
	.byte	31
	.byte	'__INDIRECT__',0,6,1,1,1,1,1,9
	.byte	'__wchar_t',0,6,1,1
	.word	3374
	.byte	9
	.byte	'__size_t',0,6,1,1
	.word	3891
	.byte	9
	.byte	'__ptrdiff_t',0,6,1,1
	.word	3884
	.byte	32,1,8
	.word	4201
	.byte	9
	.byte	'__codeptr',0,6,1,1
	.word	4203
	.byte	9
	.byte	'size_t',0,10,24,25
	.word	3891
	.byte	9
	.byte	'boolean',0,11,101,29
	.word	263
	.byte	9
	.byte	'uint8',0,11,105,29
	.word	263
	.byte	9
	.byte	'uint16',0,11,109,29
	.word	924
	.byte	9
	.byte	'uint32',0,11,113,29
	.word	242
	.byte	9
	.byte	'uint64',0,11,118,29
	.word	2610
	.byte	9
	.byte	'sint16',0,11,126,29
	.word	3374
	.byte	9
	.byte	'sint32',0,11,131,1,29
	.word	601
	.byte	2
	.byte	'long long int',0,8,5,9
	.byte	'sint64',0,11,138,1,29
	.word	4347
	.byte	9
	.byte	'float32',0,11,167,1,29
	.word	532
	.byte	9
	.byte	'pvoid',0,12,57,28
	.word	448
	.byte	9
	.byte	'Ifx_TickTime',0,12,79,28
	.word	4347
	.byte	9
	.byte	'Ifx_SizeT',0,12,92,16
	.word	3374
	.byte	8
	.word	3374
	.byte	12
	.word	263
	.byte	1,1,11
	.word	448
	.byte	11
	.word	448
	.byte	11
	.word	4450
	.byte	11
	.word	4347
	.byte	0,8
	.word	4455
	.byte	9
	.byte	'IfxStdIf_DPipe_Write',0,13,92,19
	.word	4483
	.byte	9
	.byte	'IfxStdIf_DPipe_Read',0,13,107,19
	.word	4483
	.byte	9
	.byte	'IfxStdIf_DPipe_GetReadCount',0,13,115,18
	.word	626
	.byte	33
	.word	263
	.byte	8
	.word	4581
	.byte	12
	.word	4586
	.byte	1,1,11
	.word	448
	.byte	0,8
	.word	4591
	.byte	9
	.byte	'IfxStdIf_DPipe_GetReadEvent',0,13,123,36
	.word	4604
	.byte	9
	.byte	'IfxStdIf_DPipe_GetWriteCount',0,13,147,1,18
	.word	626
	.byte	8
	.word	4591
	.byte	9
	.byte	'IfxStdIf_DPipe_GetWriteEvent',0,13,155,1,37
	.word	4683
	.byte	12
	.word	263
	.byte	1,1,11
	.word	448
	.byte	11
	.word	3374
	.byte	11
	.word	4347
	.byte	0,8
	.word	4726
	.byte	9
	.byte	'IfxStdIf_DPipe_CanReadCount',0,13,166,1,19
	.word	4749
	.byte	9
	.byte	'IfxStdIf_DPipe_CanWriteCount',0,13,177,1,19
	.word	4749
	.byte	12
	.word	263
	.byte	1,1,11
	.word	448
	.byte	11
	.word	4347
	.byte	0,8
	.word	4829
	.byte	9
	.byte	'IfxStdIf_DPipe_FlushTx',0,13,186,1,19
	.word	4847
	.byte	9
	.byte	'IfxStdIf_DPipe_ClearTx',0,13,200,1,16
	.word	495
	.byte	9
	.byte	'IfxStdIf_DPipe_ClearRx',0,13,193,1,16
	.word	495
	.byte	9
	.byte	'IfxStdIf_DPipe_OnReceive',0,13,208,1,16
	.word	495
	.byte	9
	.byte	'IfxStdIf_DPipe_OnTransmit',0,13,215,1,16
	.word	495
	.byte	9
	.byte	'IfxStdIf_DPipe_OnError',0,13,222,1,16
	.word	495
	.byte	12
	.word	242
	.byte	1,1,11
	.word	448
	.byte	0,8
	.word	5049
	.byte	9
	.byte	'IfxStdIf_DPipe_GetSendCount',0,13,131,1,18
	.word	5062
	.byte	12
	.word	4347
	.byte	1,1,11
	.word	448
	.byte	0,8
	.word	5104
	.byte	9
	.byte	'IfxStdIf_DPipe_GetTxTimeStamp',0,13,139,1,24
	.word	5117
	.byte	9
	.byte	'IfxStdIf_DPipe_ResetSendCount',0,13,229,1,16
	.word	495
	.byte	15
	.byte	'IfxStdIf_DPipe_',0,13,233,1,8,76,6
	.byte	'driver',0
	.word	453
	.byte	4,2,35,0,6
	.byte	'txDisabled',0
	.word	263
	.byte	1,2,35,4,6
	.byte	'write',0
	.word	4488
	.byte	4,2,35,8,6
	.byte	'read',0
	.word	4517
	.byte	4,2,35,12,6
	.byte	'getReadCount',0
	.word	4545
	.byte	4,2,35,16,6
	.byte	'getReadEvent',0
	.word	4609
	.byte	4,2,35,20,6
	.byte	'getWriteCount',0
	.word	4645
	.byte	4,2,35,24,6
	.byte	'getWriteEvent',0
	.word	4688
	.byte	4,2,35,28,6
	.byte	'canReadCount',0
	.word	4754
	.byte	4,2,35,32,6
	.byte	'canWriteCount',0
	.word	4791
	.byte	4,2,35,36,6
	.byte	'flushTx',0
	.word	4852
	.byte	4,2,35,40,6
	.byte	'clearTx',0
	.word	4884
	.byte	4,2,35,44,6
	.byte	'clearRx',0
	.word	4916
	.byte	4,2,35,48,6
	.byte	'onReceive',0
	.word	4948
	.byte	4,2,35,52,6
	.byte	'onTransmit',0
	.word	4982
	.byte	4,2,35,56,6
	.byte	'onError',0
	.word	5017
	.byte	4,2,35,60,6
	.byte	'getSendCount',0
	.word	5067
	.byte	4,2,35,64,6
	.byte	'getTxTimeStamp',0
	.word	5122
	.byte	4,2,35,68,6
	.byte	'resetSendCount',0
	.word	5161
	.byte	4,2,35,72,0,9
	.byte	'IfxStdIf_DPipe',0,13,71,32
	.word	5200
	.byte	8
	.word	4455
	.byte	8
	.word	4455
	.byte	8
	.word	613
	.byte	8
	.word	4591
	.byte	8
	.word	613
	.byte	8
	.word	4591
	.byte	8
	.word	4726
	.byte	8
	.word	4726
	.byte	8
	.word	4829
	.byte	8
	.word	486
	.byte	8
	.word	486
	.byte	8
	.word	486
	.byte	8
	.word	486
	.byte	8
	.word	486
	.byte	8
	.word	5049
	.byte	8
	.word	5104
	.byte	8
	.word	486
	.byte	33
	.word	263
	.byte	8
	.word	5708
	.byte	9
	.byte	'IfxStdIf_DPipe_WriteEvent',0,13,73,32
	.word	5713
	.byte	9
	.byte	'IfxStdIf_DPipe_ReadEvent',0,13,74,32
	.word	5713
	.byte	13,1,76,9,1,14
	.byte	'IfxStdIf_Pos_ResolutionFactor_oneFold',0,1,14
	.byte	'IfxStdIf_Pos_ResolutionFactor_twoFold',0,2,14
	.byte	'IfxStdIf_Pos_ResolutionFactor_fourFold',0,4,0,9
	.byte	'IfxStdIf_Pos_ResolutionFactor',0,1,81,3
	.word	5785
	.byte	9
	.byte	'IfxStdIf_Pos_SensorType',0,1,92,3
	.word	1082
	.byte	9
	.byte	'IfxStdIf_Pos_Dir',0,1,100,3
	.word	697
	.byte	9
	.byte	'IfxStdIf_Pos_Status',0,1,114,3
	.word	409
	.byte	9
	.byte	'IfxStdIf_Pos_RawAngle',0,1,116,16
	.word	601
	.byte	9
	.byte	'IfxStdIf_Pos',0,1,119,30
	.word	1736
	.byte	9
	.byte	'Ifx_LowPassPt1F32',0,3,69,3
	.word	2411
	.byte	9
	.byte	'Ifx_LowPassPt1F32_Config',0,3,77,3
	.word	2862
	.byte	9
	.byte	'Ifx_AngleTrkF32_Config',0,7,91,3
	.word	3627
	.byte	9
	.byte	'Ifx_AngleTrkF32_CfgData',0,7,101,3
	.word	3268
	.byte	9
	.byte	'Ifx_AngleTrkF32_PosIf',0,7,116,3
	.word	3076
	.byte	9
	.byte	'Ifx_AngleTrkF32',0,7,134,1,3
	.word	3392
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L98:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,36,0,3,8,11,15,62,15,0,0,3,19,1,58,15,59,15,57,15,11,15
	.byte	0,0,4,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,5,23,1,58,15,59,15,57,15,11,15,0,0,6,13,0,3,8,73,19,11
	.byte	15,56,9,0,0,7,59,0,3,8,0,0,8,15,0,73,19,0,0,9,22,0,3,8,58,15,59,15,57,15,73,19,0,0,10,21,1,54,15,39,12
	.byte	0,0,11,5,0,73,19,0,0,12,21,1,73,19,54,15,39,12,0,0,13,4,1,58,15,59,15,57,15,11,15,0,0,14,40,0,3,8,28,13
	.byte	0,0,15,19,1,3,8,58,15,59,15,57,15,11,15,0,0,16,46,1,3,8,32,13,58,15,59,15,57,15,73,19,54,15,39,12,0,0
	.byte	17,5,0,3,8,58,15,59,15,57,15,73,19,0,0,18,11,0,0,0,19,46,1,3,8,32,13,58,15,59,15,57,15,54,15,39,12,0,0
	.byte	20,21,0,54,15,39,12,0,0,21,11,1,0,0,22,46,1,49,19,0,0,23,5,0,49,19,0,0,24,38,0,73,19,0,0,25,46,1,3,8,58
	.byte	15,59,15,57,15,54,15,39,12,63,12,60,12,0,0,26,46,1,3,8,58,15,59,15,57,15,73,19,54,15,39,12,63,12,60,12
	.byte	0,0,27,5,0,58,15,59,15,57,15,73,19,0,0,28,29,1,49,19,0,0,29,11,1,49,19,0,0,30,11,0,49,19,0,0,31,46,0,3
	.byte	8,58,15,59,15,57,15,54,15,63,12,60,12,0,0,32,21,0,54,15,0,0,33,53,0,73,19,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L99:
	.word	.L400-.L399
.L399:
	.half	3
	.word	.L402-.L401
.L401:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'F:\\ADS\\AURIX-Studio-1.10.2\\tools\\Compilers\\Tasking_1.1r8\\ctc\\include\\',0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\StdIf\\IfxStdIf_Pos.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\StdIf\\IfxStdIf.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\SysSe\\Math\\Ifx_LowPassPt1F32.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_Intrinsics.h',0
	.byte	0,0,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_AngleTrkF32.c',0,0,0,0
	.byte	'..\\libraries\\infineon_libraries\\Service\\CpuGeneric\\SysSe\\Math\\Ifx_AngleTrkF32.h',0,0,0,0
	.byte	'string.h',0,1,0,0
	.byte	'..\\libraries\\infineon_libraries\\Service\\CpuGeneric\\SysSe\\Math\\Ifx_LutAtan2F32.h',0,0,0,0
	.byte	'stddef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Platform_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Ifx_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\StdIf\\IfxStdIf_DPipe.h',0
	.byte	0,0,0,0
.L402:
.L400:
	.sdecl	'.debug_info',debug,cluster('Ifx_AngleTrkF32_init')
	.sect	'.debug_info'
.L100:
	.word	388
	.half	3
	.word	.L101
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_AngleTrkF32.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L103,.L102
	.byte	2
	.word	.L96
	.byte	3
	.byte	'Ifx_AngleTrkF32_init',0,1,165,1,6,1,1,1
	.word	.L55,.L225,.L54
	.byte	4
	.byte	'aObsv',0,1,165,1,44
	.word	.L226,.L227
	.byte	4
	.byte	'config',0,1,165,1,81
	.word	.L228,.L229
	.byte	4
	.byte	'Ts',0,1,165,1,97
	.word	.L230,.L231
	.byte	5
	.word	.L55,.L225
	.byte	5
	.word	.L232,.L233
	.byte	6
	.byte	'lpfConfig',0,1,202,1,34
	.word	.L234,.L235
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('Ifx_AngleTrkF32_init')
	.sect	'.debug_abbrev'
.L101:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('Ifx_AngleTrkF32_init')
	.sect	'.debug_line'
.L102:
	.word	.L404-.L403
.L403:
	.half	3
	.word	.L406-.L405
.L405:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_AngleTrkF32.c',0,0,0,0,0
.L406:
	.byte	5,6,7,0,5,2
	.word	.L55
	.byte	3,164,1,1,5,43,9
	.half	.L363-.L55
	.byte	3,2,1,5,35,9
	.half	.L407-.L363
	.byte	1,5,43,9
	.half	.L408-.L407
	.byte	3,1,1,5,35,9
	.half	.L409-.L408
	.byte	1,5,43,9
	.half	.L410-.L409
	.byte	3,1,1,5,35,9
	.half	.L411-.L410
	.byte	1,9
	.half	.L412-.L411
	.byte	3,1,1,5,37,9
	.half	.L413-.L412
	.byte	3,1,1,5,35,9
	.half	.L414-.L413
	.byte	1,5,37,9
	.half	.L415-.L414
	.byte	3,1,1,5,35,9
	.half	.L416-.L415
	.byte	1,5,37,9
	.half	.L417-.L416
	.byte	3,1,1,5,35,9
	.half	.L418-.L417
	.byte	1,5,37,9
	.half	.L419-.L418
	.byte	3,1,1,5,35,9
	.half	.L420-.L419
	.byte	1,5,43,9
	.half	.L421-.L420
	.byte	3,1,1,5,35,9
	.half	.L422-.L421
	.byte	1,5,37,9
	.half	.L423-.L422
	.byte	3,1,1,5,63,9
	.half	.L424-.L423
	.byte	1,5,43,9
	.half	.L425-.L424
	.byte	1,5,41,9
	.half	.L426-.L425
	.byte	1,5,77,9
	.half	.L427-.L426
	.byte	1,5,75,9
	.half	.L428-.L427
	.byte	1,5,83,9
	.half	.L429-.L428
	.byte	1,5,81,9
	.half	.L430-.L429
	.byte	1,5,35,9
	.half	.L431-.L430
	.byte	1,5,43,9
	.half	.L432-.L431
	.byte	3,2,1,5,35,9
	.half	.L433-.L432
	.byte	1,5,43,9
	.half	.L434-.L433
	.byte	3,1,1,5,35,9
	.half	.L435-.L434
	.byte	1,5,43,9
	.half	.L436-.L435
	.byte	3,1,1,5,35,9
	.half	.L437-.L436
	.byte	1,5,43,9
	.half	.L438-.L437
	.byte	3,1,1,5,35,9
	.half	.L439-.L438
	.byte	1,5,43,9
	.half	.L440-.L439
	.byte	3,1,1,5,35,9
	.half	.L441-.L440
	.byte	1,5,43,9
	.half	.L442-.L441
	.byte	3,1,1,5,35,9
	.half	.L443-.L442
	.byte	1,5,43,9
	.half	.L444-.L443
	.byte	3,1,1,5,35,9
	.half	.L445-.L444
	.byte	1,5,43,9
	.half	.L446-.L445
	.byte	3,1,1,5,35,9
	.half	.L447-.L446
	.byte	1,5,10,9
	.half	.L448-.L447
	.byte	3,2,1,5,36,9
	.half	.L449-.L448
	.byte	1,5,62,9
	.half	.L450-.L449
	.byte	1,5,47,9
	.half	.L451-.L450
	.byte	3,3,1,5,58,9
	.half	.L452-.L451
	.byte	1,5,65,9
	.half	.L453-.L452
	.byte	1,5,72,9
	.half	.L454-.L453
	.byte	1,5,44,9
	.half	.L9-.L454
	.byte	3,3,1,5,24,9
	.half	.L365-.L9
	.byte	3,1,1,5,22,9
	.half	.L455-.L365
	.byte	1,5,24,9
	.half	.L456-.L455
	.byte	3,1,1,5,22,9
	.half	.L457-.L456
	.byte	1,5,24,9
	.half	.L458-.L457
	.byte	3,1,1,5,22,9
	.half	.L459-.L458
	.byte	1,5,24,9
	.half	.L460-.L459
	.byte	3,1,1,5,22,9
	.half	.L461-.L460
	.byte	1,5,24,9
	.half	.L462-.L461
	.byte	3,1,1,5,22,9
	.half	.L463-.L462
	.byte	1,5,24,9
	.half	.L464-.L463
	.byte	3,1,1,5,22,9
	.half	.L465-.L464
	.byte	1,5,37,9
	.half	.L232-.L465
	.byte	3,4,1,5,35,9
	.half	.L466-.L232
	.byte	1,5,57,9
	.half	.L467-.L466
	.byte	3,1,1,5,40,9
	.half	.L468-.L467
	.byte	1,5,57,9
	.half	.L469-.L468
	.byte	1,5,35,9
	.half	.L470-.L469
	.byte	1,9
	.half	.L471-.L470
	.byte	3,1,1,5,38,9
	.half	.L472-.L471
	.byte	3,1,1,5,51,9
	.half	.L473-.L472
	.byte	1,5,1,9
	.half	.L233-.L473
	.byte	3,3,1,7,9
	.half	.L104-.L233
	.byte	0,1,1
.L404:
	.sdecl	'.debug_ranges',debug,cluster('Ifx_AngleTrkF32_init')
	.sect	'.debug_ranges'
.L103:
	.word	-1,.L55,0,.L104-.L55,0,0
	.sdecl	'.debug_info',debug,cluster('Ifx_AngleTrkF32_initConfig')
	.sect	'.debug_info'
.L105:
	.word	385
	.half	3
	.word	.L106
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_AngleTrkF32.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L108,.L107
	.byte	2
	.word	.L96
	.byte	3
	.byte	'Ifx_AngleTrkF32_initConfig',0,1,133,1,6,1,1,1
	.word	.L51,.L236,.L50
	.byte	4
	.byte	'config',0,1,133,1,57
	.word	.L237,.L238
	.byte	4
	.byte	'sinIn',0,1,133,1,73
	.word	.L239,.L240
	.byte	4
	.byte	'cosIn',0,1,133,1,88
	.word	.L239,.L241
	.byte	5
	.word	.L51,.L236
	.byte	6
	.byte	'cfgData',0,1,135,1,29
	.word	.L242,.L243
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('Ifx_AngleTrkF32_initConfig')
	.sect	'.debug_abbrev'
.L106:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('Ifx_AngleTrkF32_initConfig')
	.sect	'.debug_line'
.L107:
	.word	.L475-.L474
.L474:
	.half	3
	.word	.L477-.L476
.L476:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_AngleTrkF32.c',0,0,0,0,0
.L477:
	.byte	5,6,7,0,5,2
	.word	.L51
	.byte	3,132,1,1,5,38,9
	.half	.L357-.L51
	.byte	3,3,1,5,47,9
	.half	.L353-.L357
	.byte	1,5,54,9
	.half	.L478-.L353
	.byte	1,5,61,9
	.half	.L479-.L478
	.byte	1,5,40,9
	.half	.L354-.L479
	.byte	3,1,1,5,31,9
	.half	.L480-.L354
	.byte	1,5,40,9
	.half	.L481-.L480
	.byte	3,1,1,5,31,9
	.half	.L482-.L481
	.byte	1,5,40,9
	.half	.L483-.L482
	.byte	3,1,1,5,31,9
	.half	.L484-.L483
	.byte	1,9
	.half	.L485-.L484
	.byte	3,1,1,9
	.half	.L486-.L485
	.byte	3,1,1,5,44,9
	.half	.L487-.L486
	.byte	3,1,1,5,31,9
	.half	.L488-.L487
	.byte	1,5,33,9
	.half	.L489-.L488
	.byte	3,1,1,5,31,9
	.half	.L490-.L489
	.byte	1,5,33,9
	.half	.L491-.L490
	.byte	3,1,1,5,31,9
	.half	.L492-.L491
	.byte	1,5,33,9
	.half	.L493-.L492
	.byte	3,1,1,5,31,9
	.half	.L494-.L493
	.byte	1,5,33,9
	.half	.L495-.L494
	.byte	3,1,1,5,31,9
	.half	.L496-.L495
	.byte	1,5,33,9
	.half	.L497-.L496
	.byte	3,1,1,5,31,9
	.half	.L498-.L497
	.byte	1,5,33,9
	.half	.L499-.L498
	.byte	3,1,1,5,31,9
	.half	.L500-.L499
	.byte	1,5,38,9
	.half	.L501-.L500
	.byte	3,1,1,5,31,9
	.half	.L502-.L501
	.byte	1,5,1,9
	.half	.L503-.L502
	.byte	3,1,1,7,9
	.half	.L109-.L503
	.byte	0,1,1
.L475:
	.sdecl	'.debug_ranges',debug,cluster('Ifx_AngleTrkF32_initConfig')
	.sect	'.debug_ranges'
.L108:
	.word	-1,.L51,0,.L109-.L51,0,0
	.sdecl	'.debug_info',debug,cluster('Ifx_AngleTrkF32_step')
	.sect	'.debug_info'
.L110:
	.word	724
	.half	3
	.word	.L111
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_AngleTrkF32.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L113,.L112
	.byte	2
	.word	.L96
	.byte	3
	.byte	'Ifx_AngleTrkF32_step',0,1,223,1,9
	.word	.L230
	.byte	1,1,1
	.word	.L57,.L244,.L56
	.byte	4
	.byte	'aObsv',0,1,223,1,47
	.word	.L226,.L245
	.byte	4
	.byte	'sinIn',0,1,223,1,61
	.word	.L246,.L247
	.byte	4
	.byte	'cosIn',0,1,223,1,75
	.word	.L246,.L248
	.byte	4
	.byte	'phase',0,1,223,1,90
	.word	.L230,.L249
	.byte	5
	.word	.L57,.L244
	.byte	6
	.byte	'angleRef',0,1,225,1,13
	.word	.L230,.L250
	.byte	6
	.byte	'angleEst',0,1,225,1,23
	.word	.L230,.L251
	.byte	6
	.byte	'dAccel',0,1,225,1,33
	.word	.L230,.L252
	.byte	6
	.byte	'dSpeed',0,1,225,1,41
	.word	.L230,.L253
	.byte	6
	.byte	'dAngle',0,1,225,1,49
	.word	.L230,.L254
	.byte	7
	.word	.L255,.L256,.L19
	.byte	8
	.word	.L257,.L258
	.byte	9
	.word	.L259,.L256,.L19
	.byte	7
	.word	.L260,.L261,.L18
	.byte	8
	.word	.L262,.L263
	.byte	8
	.word	.L264,.L265
	.byte	9
	.word	.L266,.L261,.L18
	.byte	9
	.word	.L267,.L13,.L18
	.byte	6
	.byte	'fullPeriod',0,1,76,13
	.word	.L230,.L268
	.byte	0,0,0,0,0,7
	.word	.L255,.L269,.L26
	.byte	8
	.word	.L257,.L258
	.byte	9
	.word	.L259,.L269,.L26
	.byte	7
	.word	.L260,.L270,.L25
	.byte	8
	.word	.L262,.L263
	.byte	8
	.word	.L264,.L265
	.byte	9
	.word	.L266,.L270,.L25
	.byte	9
	.word	.L267,.L20,.L25
	.byte	6
	.byte	'fullPeriod',0,1,76,13
	.word	.L230,.L271
	.byte	0,0,0,0,0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('Ifx_AngleTrkF32_step')
	.sect	'.debug_abbrev'
.L111:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,5,0,49,16,2,6,0,0,9,11,1,49,16,17
	.byte	1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('Ifx_AngleTrkF32_step')
	.sect	'.debug_line'
.L112:
	.word	.L505-.L504
.L504:
	.half	3
	.word	.L507-.L506
.L506:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_AngleTrkF32.c',0,0,0,0,0
.L507:
	.byte	5,9,7,0,5,2
	.word	.L57
	.byte	3,222,1,1,5,20,9
	.half	.L371-.L57
	.byte	3,4,1,5,5,9
	.half	.L508-.L371
	.byte	1,5,43,7,9
	.half	.L509-.L508
	.byte	3,2,1,5,59,9
	.half	.L510-.L509
	.byte	1,5,74,9
	.half	.L367-.L510
	.byte	1,5,43,9
	.half	.L11-.L367
	.byte	3,4,1,5,59,9
	.half	.L373-.L11
	.byte	1,5,22,9
	.half	.L12-.L373
	.byte	3,3,1,5,33,9
	.half	.L511-.L12
	.byte	3,1,1,5,22,9
	.half	.L374-.L511
	.byte	3,2,1,5,37,9
	.half	.L512-.L374
	.byte	3,3,1,5,48,9
	.half	.L375-.L512
	.byte	1,5,41,9
	.half	.L513-.L375
	.byte	1,5,62,9
	.half	.L376-.L513
	.byte	3,1,1,5,28,9
	.half	.L514-.L376
	.byte	1,5,39,9
	.half	.L515-.L514
	.byte	1,5,21,9
	.half	.L377-.L515
	.byte	1,5,40,9
	.half	.L516-.L377
	.byte	3,3,1,5,51,9
	.half	.L517-.L516
	.byte	1,5,70,9
	.half	.L518-.L517
	.byte	1,5,63,9
	.half	.L519-.L518
	.byte	1,5,64,9
	.half	.L378-.L519
	.byte	3,1,1,5,29,9
	.half	.L520-.L378
	.byte	1,5,41,9
	.half	.L521-.L520
	.byte	1,5,22,9
	.half	.L379-.L521
	.byte	1,5,40,9
	.half	.L522-.L379
	.byte	3,3,1,5,51,9
	.half	.L523-.L522
	.byte	1,5,70,9
	.half	.L524-.L523
	.byte	1,5,63,9
	.half	.L525-.L524
	.byte	1,5,58,9
	.half	.L380-.L525
	.byte	3,1,1,5,51,9
	.half	.L526-.L380
	.byte	1,5,78,9
	.half	.L527-.L526
	.byte	1,5,29,9
	.half	.L528-.L527
	.byte	1,5,40,9
	.half	.L529-.L528
	.byte	1,5,22,9
	.half	.L381-.L529
	.byte	3,1,1,5,41,9
	.half	.L256-.L381
	.byte	3,227,126,1,5,2,9
	.half	.L261-.L256
	.byte	3,108,1,5,28,9
	.half	.L13-.L261
	.byte	3,1,1,5,32,9
	.half	.L530-.L13
	.byte	3,2,1,5,30,9
	.half	.L531-.L530
	.byte	1,5,15,9
	.half	.L532-.L531
	.byte	1,5,5,9
	.half	.L533-.L532
	.byte	1,5,23,7,9
	.half	.L534-.L533
	.byte	3,2,1,5,35,9
	.half	.L535-.L534
	.byte	1,5,23,9
	.half	.L14-.L535
	.byte	3,2,1,5,37,9
	.half	.L536-.L14
	.byte	1,5,35,9
	.half	.L537-.L536
	.byte	1,5,20,9
	.half	.L538-.L537
	.byte	1,5,10,9
	.half	.L539-.L538
	.byte	1,5,23,7,9
	.half	.L540-.L539
	.byte	3,2,1,5,35,9
	.half	.L541-.L540
	.byte	1,5,5,9
	.half	.L15-.L541
	.byte	3,5,1,9
	.half	.L18-.L15
	.byte	3,6,1,5,22,9
	.half	.L19-.L18
	.byte	3,159,1,1,5,61,9
	.half	.L542-.L19
	.byte	3,3,1,5,72,9
	.half	.L543-.L542
	.byte	1,5,41,9
	.half	.L269-.L543
	.byte	3,222,126,1,5,2,9
	.half	.L270-.L269
	.byte	3,108,1,5,28,9
	.half	.L20-.L270
	.byte	3,1,1,5,32,9
	.half	.L544-.L20
	.byte	3,2,1,5,30,9
	.half	.L545-.L544
	.byte	1,5,15,9
	.half	.L546-.L545
	.byte	1,5,5,9
	.half	.L547-.L546
	.byte	1,5,23,7,9
	.half	.L548-.L547
	.byte	3,2,1,5,35,9
	.half	.L549-.L548
	.byte	1,5,23,9
	.half	.L21-.L549
	.byte	3,2,1,5,37,9
	.half	.L550-.L21
	.byte	1,5,35,9
	.half	.L382-.L550
	.byte	1,5,20,9
	.half	.L551-.L382
	.byte	1,5,10,9
	.half	.L552-.L551
	.byte	1,5,23,7,9
	.half	.L553-.L552
	.byte	3,2,1,5,35,9
	.half	.L554-.L553
	.byte	1,5,5,9
	.half	.L22-.L554
	.byte	3,5,1,9
	.half	.L25-.L22
	.byte	3,6,1,5,27,9
	.half	.L26-.L25
	.byte	3,162,1,1,5,34,9
	.half	.L555-.L26
	.byte	3,2,1,5,48,9
	.half	.L556-.L555
	.byte	1,5,29,9
	.half	.L557-.L556
	.byte	1,5,50,9
	.half	.L558-.L557
	.byte	1,5,77,9
	.half	.L559-.L558
	.byte	1,5,50,9
	.half	.L27-.L559
	.byte	1,5,27,9
	.half	.L28-.L27
	.byte	1,5,32,9
	.half	.L560-.L28
	.byte	3,4,1,5,49,9
	.half	.L561-.L560
	.byte	1,5,17,9
	.half	.L385-.L561
	.byte	3,3,1,5,5,9
	.half	.L562-.L385
	.byte	1,5,1,9
	.half	.L29-.L562
	.byte	3,1,1,7,9
	.half	.L114-.L29
	.byte	0,1,1
.L505:
	.sdecl	'.debug_ranges',debug,cluster('Ifx_AngleTrkF32_step')
	.sect	'.debug_ranges'
.L113:
	.word	-1,.L57,0,.L114-.L57,0,0
	.sdecl	'.debug_info',debug,cluster('Ifx_AngleTrkF32_updateStatus')
	.sect	'.debug_info'
.L115:
	.word	404
	.half	3
	.word	.L116
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_AngleTrkF32.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L118,.L117
	.byte	2
	.word	.L96
	.byte	3
	.byte	'Ifx_AngleTrkF32_updateStatus',0,1,159,2,6,1,1,1
	.word	.L61,.L272,.L60
	.byte	4
	.byte	'aObsv',0,1,159,2,52
	.word	.L226,.L273
	.byte	4
	.byte	'sinIn',0,1,159,2,66
	.word	.L246,.L274
	.byte	4
	.byte	'cosIn',0,1,159,2,80
	.word	.L246,.L275
	.byte	5
	.word	.L61,.L272
	.byte	6
	.byte	'base',0,1,161,2,28
	.word	.L276,.L277
	.byte	6
	.byte	'sqrAmpl',0,1,162,2,29
	.word	.L278,.L279
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('Ifx_AngleTrkF32_updateStatus')
	.sect	'.debug_abbrev'
.L116:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('Ifx_AngleTrkF32_updateStatus')
	.sect	'.debug_line'
.L117:
	.word	.L564-.L563
.L563:
	.half	3
	.word	.L566-.L565
.L565:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_AngleTrkF32.c',0,0,0,0,0
.L566:
	.byte	5,6,7,0,5,2
	.word	.L61
	.byte	3,158,2,1,5,54,9
	.half	.L388-.L61
	.byte	3,3,1,5,71,9
	.half	.L387-.L388
	.byte	1,5,19,9
	.half	.L389-.L387
	.byte	3,1,1,5,65,9
	.half	.L567-.L389
	.byte	1,5,49,9
	.half	.L568-.L567
	.byte	1,5,38,9
	.half	.L569-.L568
	.byte	1,5,19,9
	.half	.L570-.L569
	.byte	3,1,1,5,65,9
	.half	.L571-.L570
	.byte	1,5,49,9
	.half	.L572-.L571
	.byte	1,5,38,9
	.half	.L573-.L572
	.byte	1,5,40,9
	.half	.L574-.L573
	.byte	3,1,1,5,19,9
	.half	.L31-.L574
	.byte	1,5,80,9
	.half	.L575-.L31
	.byte	1,5,64,9
	.half	.L576-.L575
	.byte	1,5,38,9
	.half	.L577-.L576
	.byte	1,5,1,9
	.half	.L578-.L577
	.byte	3,1,1,7,9
	.half	.L119-.L578
	.byte	0,1,1
.L564:
	.sdecl	'.debug_ranges',debug,cluster('Ifx_AngleTrkF32_updateStatus')
	.sect	'.debug_ranges'
.L118:
	.word	-1,.L61,0,.L119-.L61,0,0
	.sdecl	'.debug_info',debug,cluster('Ifx_AngleTrkF32_getAbsolutePosition')
	.sect	'.debug_info'
.L120:
	.word	338
	.half	3
	.word	.L121
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_AngleTrkF32.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L123,.L122
	.byte	2
	.word	.L96
	.byte	3
	.byte	'Ifx_AngleTrkF32_getAbsolutePosition',0,1,210,2,9
	.word	.L230
	.byte	1,1,1
	.word	.L69,.L280,.L68
	.byte	4
	.byte	'driver',0,1,210,2,62
	.word	.L226,.L281
	.byte	5
	.word	.L69,.L280
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('Ifx_AngleTrkF32_getAbsolutePosition')
	.sect	'.debug_abbrev'
.L121:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('Ifx_AngleTrkF32_getAbsolutePosition')
	.sect	'.debug_line'
.L122:
	.word	.L580-.L579
.L579:
	.half	3
	.word	.L582-.L581
.L581:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_AngleTrkF32.c',0,0,0,0,0
.L582:
	.byte	5,34,7,0,5,2
	.word	.L69
	.byte	3,211,2,1,5,13,9
	.half	.L583-.L69
	.byte	1,5,63,9
	.half	.L584-.L583
	.byte	1,5,42,9
	.half	.L585-.L584
	.byte	1,5,99,9
	.half	.L586-.L585
	.byte	1,5,78,9
	.half	.L587-.L586
	.byte	1,5,76,9
	.half	.L588-.L587
	.byte	1,5,40,9
	.half	.L589-.L588
	.byte	1,5,114,9
	.half	.L395-.L589
	.byte	1,5,112,9
	.half	.L590-.L395
	.byte	1,5,120,9
	.half	.L591-.L590
	.byte	1,5,118,9
	.half	.L592-.L591
	.byte	1,5,5,9
	.half	.L593-.L592
	.byte	1,5,1,9
	.half	.L34-.L593
	.byte	3,1,1,7,9
	.half	.L124-.L34
	.byte	0,1,1
.L580:
	.sdecl	'.debug_ranges',debug,cluster('Ifx_AngleTrkF32_getAbsolutePosition')
	.sect	'.debug_ranges'
.L123:
	.word	-1,.L69,0,.L124-.L69,0,0
	.sdecl	'.debug_info',debug,cluster('Ifx_AngleTrkF32_getDirection')
	.sect	'.debug_info'
.L125:
	.word	331
	.half	3
	.word	.L126
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_AngleTrkF32.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L128,.L127
	.byte	2
	.word	.L96
	.byte	3
	.byte	'Ifx_AngleTrkF32_getDirection',0,1,216,2,18
	.word	.L282
	.byte	1,1,1
	.word	.L71,.L283,.L70
	.byte	4
	.byte	'driver',0,1,216,2,64
	.word	.L226,.L284
	.byte	5
	.word	.L71,.L283
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('Ifx_AngleTrkF32_getDirection')
	.sect	'.debug_abbrev'
.L126:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('Ifx_AngleTrkF32_getDirection')
	.sect	'.debug_line'
.L127:
	.word	.L595-.L594
.L594:
	.half	3
	.word	.L597-.L596
.L596:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_AngleTrkF32.c',0,0,0,0,0
.L597:
	.byte	5,24,7,0,5,2
	.word	.L71
	.byte	3,217,2,1,5,5,9
	.half	.L598-.L71
	.byte	1,5,1,9
	.half	.L35-.L598
	.byte	3,1,1,7,9
	.half	.L129-.L35
	.byte	0,1,1
.L595:
	.sdecl	'.debug_ranges',debug,cluster('Ifx_AngleTrkF32_getDirection')
	.sect	'.debug_ranges'
.L128:
	.word	-1,.L71,0,.L129-.L71,0,0
	.sdecl	'.debug_info',debug,cluster('Ifx_AngleTrkF32_getFault')
	.sect	'.debug_info'
.L130:
	.word	327
	.half	3
	.word	.L131
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_AngleTrkF32.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L133,.L132
	.byte	2
	.word	.L96
	.byte	3
	.byte	'Ifx_AngleTrkF32_getFault',0,1,222,2,21
	.word	.L285
	.byte	1,1,1
	.word	.L73,.L286,.L72
	.byte	4
	.byte	'driver',0,1,222,2,63
	.word	.L226,.L287
	.byte	5
	.word	.L73,.L286
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('Ifx_AngleTrkF32_getFault')
	.sect	'.debug_abbrev'
.L131:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('Ifx_AngleTrkF32_getFault')
	.sect	'.debug_line'
.L132:
	.word	.L600-.L599
.L599:
	.half	3
	.word	.L602-.L601
.L601:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_AngleTrkF32.c',0,0,0,0,0
.L602:
	.byte	5,24,7,0,5,2
	.word	.L73
	.byte	3,223,2,1,5,5,9
	.half	.L603-.L73
	.byte	1,5,1,9
	.half	.L36-.L603
	.byte	3,1,1,7,9
	.half	.L134-.L36
	.byte	0,1,1
.L600:
	.sdecl	'.debug_ranges',debug,cluster('Ifx_AngleTrkF32_getFault')
	.sect	'.debug_ranges'
.L133:
	.word	-1,.L73,0,.L134-.L73,0,0
	.sdecl	'.debug_info',debug,cluster('Ifx_AngleTrkF32_getOffset')
	.sect	'.debug_info'
.L135:
	.word	328
	.half	3
	.word	.L136
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_AngleTrkF32.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L138,.L137
	.byte	2
	.word	.L96
	.byte	3
	.byte	'Ifx_AngleTrkF32_getOffset',0,1,228,2,8
	.word	.L278
	.byte	1,1,1
	.word	.L75,.L288,.L74
	.byte	4
	.byte	'driver',0,1,228,2,51
	.word	.L226,.L289
	.byte	5
	.word	.L75,.L288
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('Ifx_AngleTrkF32_getOffset')
	.sect	'.debug_abbrev'
.L136:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('Ifx_AngleTrkF32_getOffset')
	.sect	'.debug_line'
.L137:
	.word	.L605-.L604
.L604:
	.half	3
	.word	.L607-.L606
.L606:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_AngleTrkF32.c',0,0,0,0,0
.L607:
	.byte	5,24,7,0,5,2
	.word	.L75
	.byte	3,229,2,1,5,5,9
	.half	.L608-.L75
	.byte	1,5,1,9
	.half	.L37-.L608
	.byte	3,1,1,7,9
	.half	.L139-.L37
	.byte	0,1,1
.L605:
	.sdecl	'.debug_ranges',debug,cluster('Ifx_AngleTrkF32_getOffset')
	.sect	'.debug_ranges'
.L138:
	.word	-1,.L75,0,.L139-.L75,0,0
	.sdecl	'.debug_info',debug,cluster('Ifx_AngleTrkF32_getPeriodPerRotation')
	.sect	'.debug_info'
.L140:
	.word	339
	.half	3
	.word	.L141
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_AngleTrkF32.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L143,.L142
	.byte	2
	.word	.L96
	.byte	3
	.byte	'Ifx_AngleTrkF32_getPeriodPerRotation',0,1,234,2,8
	.word	.L290
	.byte	1,1,1
	.word	.L77,.L291,.L76
	.byte	4
	.byte	'driver',0,1,234,2,62
	.word	.L226,.L292
	.byte	5
	.word	.L77,.L291
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('Ifx_AngleTrkF32_getPeriodPerRotation')
	.sect	'.debug_abbrev'
.L141:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('Ifx_AngleTrkF32_getPeriodPerRotation')
	.sect	'.debug_line'
.L142:
	.word	.L610-.L609
.L609:
	.half	3
	.word	.L612-.L611
.L611:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_AngleTrkF32.c',0,0,0,0,0
.L612:
	.byte	5,24,7,0,5,2
	.word	.L77
	.byte	3,235,2,1,5,5,9
	.half	.L613-.L77
	.byte	1,5,1,9
	.half	.L38-.L613
	.byte	3,1,1,7,9
	.half	.L144-.L38
	.byte	0,1,1
.L610:
	.sdecl	'.debug_ranges',debug,cluster('Ifx_AngleTrkF32_getPeriodPerRotation')
	.sect	'.debug_ranges'
.L143:
	.word	-1,.L77,0,.L144-.L77,0,0
	.sdecl	'.debug_info',debug,cluster('Ifx_AngleTrkF32_getPosition')
	.sect	'.debug_info'
.L145:
	.word	330
	.half	3
	.word	.L146
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_AngleTrkF32.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L148,.L147
	.byte	2
	.word	.L96
	.byte	3
	.byte	'Ifx_AngleTrkF32_getPosition',0,1,240,2,9
	.word	.L230
	.byte	1,1,1
	.word	.L79,.L293,.L78
	.byte	4
	.byte	'driver',0,1,240,2,54
	.word	.L226,.L294
	.byte	5
	.word	.L79,.L293
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('Ifx_AngleTrkF32_getPosition')
	.sect	'.debug_abbrev'
.L146:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('Ifx_AngleTrkF32_getPosition')
	.sect	'.debug_line'
.L147:
	.word	.L615-.L614
.L614:
	.half	3
	.word	.L617-.L616
.L616:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_AngleTrkF32.c',0,0,0,0,0
.L617:
	.byte	5,33,7,0,5,2
	.word	.L79
	.byte	3,241,2,1,5,12,9
	.half	.L618-.L79
	.byte	1,5,60,9
	.half	.L619-.L618
	.byte	1,5,46,9
	.half	.L620-.L619
	.byte	1,5,5,9
	.half	.L621-.L620
	.byte	1,5,1,9
	.half	.L39-.L621
	.byte	3,1,1,7,9
	.half	.L149-.L39
	.byte	0,1,1
.L615:
	.sdecl	'.debug_ranges',debug,cluster('Ifx_AngleTrkF32_getPosition')
	.sect	'.debug_ranges'
.L148:
	.word	-1,.L79,0,.L149-.L79,0,0
	.sdecl	'.debug_info',debug,cluster('Ifx_AngleTrkF32_getRawPosition')
	.sect	'.debug_info'
.L150:
	.word	351
	.half	3
	.word	.L151
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_AngleTrkF32.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L153,.L152
	.byte	2
	.word	.L96
	.byte	3
	.byte	'Ifx_AngleTrkF32_getRawPosition',0,1,189,2,23
	.word	.L278
	.byte	1,1,1
	.word	.L65,.L295,.L64
	.byte	4
	.byte	'aObsv',0,1,189,2,71
	.word	.L226,.L296
	.byte	5
	.word	.L65,.L295
	.byte	6
	.byte	'base',0,1,191,2,28
	.word	.L276,.L297
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('Ifx_AngleTrkF32_getRawPosition')
	.sect	'.debug_abbrev'
.L151:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('Ifx_AngleTrkF32_getRawPosition')
	.sect	'.debug_line'
.L152:
	.word	.L623-.L622
.L622:
	.half	3
	.word	.L625-.L624
.L624:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_AngleTrkF32.c',0,0,0,0,0
.L625:
	.byte	5,16,7,0,5,2
	.word	.L65
	.byte	3,191,2,1,5,5,9
	.half	.L626-.L65
	.byte	1,5,1,9
	.half	.L32-.L626
	.byte	3,1,1,7,9
	.half	.L154-.L32
	.byte	0,1,1
.L623:
	.sdecl	'.debug_ranges',debug,cluster('Ifx_AngleTrkF32_getRawPosition')
	.sect	'.debug_ranges'
.L153:
	.word	-1,.L65,0,.L154-.L65,0,0
	.sdecl	'.debug_info',debug,cluster('Ifx_AngleTrkF32_getRefreshPeriod')
	.sect	'.debug_info'
.L155:
	.word	335
	.half	3
	.word	.L156
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_AngleTrkF32.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L158,.L157
	.byte	2
	.word	.L96
	.byte	3
	.byte	'Ifx_AngleTrkF32_getRefreshPeriod',0,1,246,2,9
	.word	.L230
	.byte	1,1,1
	.word	.L81,.L298,.L80
	.byte	4
	.byte	'driver',0,1,246,2,59
	.word	.L226,.L299
	.byte	5
	.word	.L81,.L298
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('Ifx_AngleTrkF32_getRefreshPeriod')
	.sect	'.debug_abbrev'
.L156:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('Ifx_AngleTrkF32_getRefreshPeriod')
	.sect	'.debug_line'
.L157:
	.word	.L628-.L627
.L627:
	.half	3
	.word	.L630-.L629
.L629:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_AngleTrkF32.c',0,0,0,0,0
.L630:
	.byte	5,24,7,0,5,2
	.word	.L81
	.byte	3,247,2,1,5,5,9
	.half	.L631-.L81
	.byte	1,5,1,9
	.half	.L40-.L631
	.byte	3,1,1,7,9
	.half	.L159-.L40
	.byte	0,1,1
.L628:
	.sdecl	'.debug_ranges',debug,cluster('Ifx_AngleTrkF32_getRefreshPeriod')
	.sect	'.debug_ranges'
.L158:
	.word	-1,.L81,0,.L159-.L81,0,0
	.sdecl	'.debug_info',debug,cluster('Ifx_AngleTrkF32_getResolution')
	.sect	'.debug_info'
.L160:
	.word	332
	.half	3
	.word	.L161
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_AngleTrkF32.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L163,.L162
	.byte	2
	.word	.L96
	.byte	3
	.byte	'Ifx_AngleTrkF32_getResolution',0,1,252,2,8
	.word	.L278
	.byte	1,1,1
	.word	.L83,.L300,.L82
	.byte	4
	.byte	'driver',0,1,252,2,55
	.word	.L226,.L301
	.byte	5
	.word	.L83,.L300
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('Ifx_AngleTrkF32_getResolution')
	.sect	'.debug_abbrev'
.L161:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('Ifx_AngleTrkF32_getResolution')
	.sect	'.debug_line'
.L162:
	.word	.L633-.L632
.L632:
	.half	3
	.word	.L635-.L634
.L634:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_AngleTrkF32.c',0,0,0,0,0
.L635:
	.byte	5,24,7,0,5,2
	.word	.L83
	.byte	3,253,2,1,5,5,9
	.half	.L636-.L83
	.byte	1,5,1,9
	.half	.L41-.L636
	.byte	3,1,1,7,9
	.half	.L164-.L41
	.byte	0,1,1
.L633:
	.sdecl	'.debug_ranges',debug,cluster('Ifx_AngleTrkF32_getResolution')
	.sect	'.debug_ranges'
.L163:
	.word	-1,.L83,0,.L164-.L83,0,0
	.sdecl	'.debug_info',debug,cluster('Ifx_AngleTrkF32_getTurn')
	.sect	'.debug_info'
.L165:
	.word	326
	.half	3
	.word	.L166
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_AngleTrkF32.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L168,.L167
	.byte	2
	.word	.L96
	.byte	3
	.byte	'Ifx_AngleTrkF32_getTurn',0,1,137,3,8
	.word	.L278
	.byte	1,1,1
	.word	.L87,.L302,.L86
	.byte	4
	.byte	'driver',0,1,137,3,49
	.word	.L226,.L303
	.byte	5
	.word	.L87,.L302
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('Ifx_AngleTrkF32_getTurn')
	.sect	'.debug_abbrev'
.L166:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('Ifx_AngleTrkF32_getTurn')
	.sect	'.debug_line'
.L167:
	.word	.L638-.L637
.L637:
	.half	3
	.word	.L640-.L639
.L639:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_AngleTrkF32.c',0,0,0,0,0
.L640:
	.byte	5,24,7,0,5,2
	.word	.L87
	.byte	3,138,3,1,5,5,9
	.half	.L641-.L87
	.byte	1,5,1,9
	.half	.L44-.L641
	.byte	3,1,1,7,9
	.half	.L169-.L44
	.byte	0,1,1
.L638:
	.sdecl	'.debug_ranges',debug,cluster('Ifx_AngleTrkF32_getTurn')
	.sect	'.debug_ranges'
.L168:
	.word	-1,.L87,0,.L169-.L87,0,0
	.sdecl	'.debug_info',debug,cluster('Ifx_AngleTrkF32_getSensorType')
	.sect	'.debug_info'
.L170:
	.word	332
	.half	3
	.word	.L171
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_AngleTrkF32.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L173,.L172
	.byte	2
	.word	.L96
	.byte	3
	.byte	'Ifx_AngleTrkF32_getSensorType',0,1,130,3,25
	.word	.L304
	.byte	1,1,1
	.word	.L85,.L305,.L84
	.byte	4
	.byte	'driver',0,1,130,3,72
	.word	.L226,.L306
	.byte	5
	.word	.L85,.L305
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('Ifx_AngleTrkF32_getSensorType')
	.sect	'.debug_abbrev'
.L171:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('Ifx_AngleTrkF32_getSensorType')
	.sect	'.debug_line'
.L172:
	.word	.L643-.L642
.L642:
	.half	3
	.word	.L645-.L644
.L644:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_AngleTrkF32.c',0,0,0,0,0
.L645:
	.byte	5,2,7,0,5,2
	.word	.L85
	.byte	3,131,3,1,5,12,7,9
	.half	.L42-.L85
	.byte	3,1,1,5,5,9
	.half	.L646-.L42
	.byte	1,5,1,9
	.half	.L43-.L646
	.byte	3,1,1,7,9
	.half	.L174-.L43
	.byte	0,1,1
.L643:
	.sdecl	'.debug_ranges',debug,cluster('Ifx_AngleTrkF32_getSensorType')
	.sect	'.debug_ranges'
.L173:
	.word	-1,.L85,0,.L174-.L85,0,0
	.sdecl	'.debug_info',debug,cluster('Ifx_AngleTrkF32_getSpeed')
	.sect	'.debug_info'
.L175:
	.word	326
	.half	3
	.word	.L176
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_AngleTrkF32.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L178,.L177
	.byte	2
	.word	.L96
	.byte	3
	.byte	'Ifx_AngleTrkF32_getSpeed',0,1,199,2,9
	.word	.L230
	.byte	1,1,1
	.word	.L67,.L307,.L66
	.byte	4
	.byte	'aObsv',0,1,199,2,51
	.word	.L226,.L308
	.byte	5
	.word	.L67,.L307
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('Ifx_AngleTrkF32_getSpeed')
	.sect	'.debug_abbrev'
.L176:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('Ifx_AngleTrkF32_getSpeed')
	.sect	'.debug_line'
.L177:
	.word	.L648-.L647
.L647:
	.half	3
	.word	.L650-.L649
.L649:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_AngleTrkF32.c',0,0,0,0,0
.L650:
	.byte	5,27,7,0,5,2
	.word	.L67
	.byte	3,203,2,1,5,5,9
	.half	.L651-.L67
	.byte	1,5,1,9
	.half	.L33-.L651
	.byte	3,2,1,7,9
	.half	.L179-.L33
	.byte	0,1,1
.L648:
	.sdecl	'.debug_ranges',debug,cluster('Ifx_AngleTrkF32_getSpeed')
	.sect	'.debug_ranges'
.L178:
	.word	-1,.L67,0,.L179-.L67,0,0
	.sdecl	'.debug_info',debug,cluster('Ifx_AngleTrkF32_reset')
	.sect	'.debug_info'
.L180:
	.word	357
	.half	3
	.word	.L181
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_AngleTrkF32.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L183,.L182
	.byte	2
	.word	.L96
	.byte	3
	.byte	'Ifx_AngleTrkF32_reset',0,1,155,3,6,1,1,1
	.word	.L93,.L309,.L92
	.byte	4
	.byte	'driver',0,1,155,3,45
	.word	.L226,.L310
	.byte	5
	.word	.L93,.L309
	.byte	6
	.word	.L311,.L312,.L313
	.byte	7
	.word	.L314,.L315
	.byte	8
	.word	.L316,.L312,.L313
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('Ifx_AngleTrkF32_reset')
	.sect	'.debug_abbrev'
.L181:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,29,1,49
	.byte	16,17,1,18,1,0,0,7,5,0,49,16,2,6,0,0,8,11,0,49,16,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('Ifx_AngleTrkF32_reset')
	.sect	'.debug_line'
.L182:
	.word	.L653-.L652
.L652:
	.half	3
	.word	.L655-.L654
.L654:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_AngleTrkF32.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\SysSe\\Math\\Ifx_LowPassPt1F32.h',0
	.byte	0,0,0,0
.L655:
	.byte	5,32,7,0,5,2
	.word	.L93
	.byte	3,156,3,1,5,30,9
	.half	.L656-.L93
	.byte	1,5,32,9
	.half	.L657-.L656
	.byte	3,1,1,5,30,9
	.half	.L658-.L657
	.byte	1,5,32,9
	.half	.L659-.L658
	.byte	3,1,1,5,30,9
	.half	.L660-.L659
	.byte	1,5,32,9
	.half	.L661-.L660
	.byte	3,1,1,5,30,9
	.half	.L662-.L661
	.byte	1,5,32,9
	.half	.L663-.L662
	.byte	3,1,1,5,30,9
	.half	.L664-.L663
	.byte	1,5,32,9
	.half	.L665-.L664
	.byte	3,1,1,5,30,9
	.half	.L666-.L665
	.byte	1,5,32,9
	.half	.L667-.L666
	.byte	3,1,1,5,30,9
	.half	.L668-.L667
	.byte	1,5,32,9
	.half	.L669-.L668
	.byte	3,1,1,5,30,9
	.half	.L670-.L669
	.byte	1,5,32,9
	.half	.L671-.L670
	.byte	3,1,1,5,30,9
	.half	.L672-.L671
	.byte	1,4,2,5,19,9
	.half	.L312-.L672
	.byte	3,186,125,1,5,17,9
	.half	.L673-.L312
	.byte	1,4,1,5,34,9
	.half	.L313-.L673
	.byte	3,203,2,1,5,32,9
	.half	.L674-.L313
	.byte	1,5,1,9
	.half	.L675-.L674
	.byte	3,1,1,7,9
	.half	.L184-.L675
	.byte	0,1,1
.L653:
	.sdecl	'.debug_ranges',debug,cluster('Ifx_AngleTrkF32_reset')
	.sect	'.debug_ranges'
.L183:
	.word	-1,.L93,0,.L184-.L93,0,0
	.sdecl	'.debug_info',debug,cluster('Ifx_AngleTrkF32_resetFaults')
	.sect	'.debug_info'
.L185:
	.word	326
	.half	3
	.word	.L186
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_AngleTrkF32.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L188,.L187
	.byte	2
	.word	.L96
	.byte	3
	.byte	'Ifx_AngleTrkF32_resetFaults',0,1,143,3,6,1,1,1
	.word	.L89,.L317,.L88
	.byte	4
	.byte	'driver',0,1,143,3,51
	.word	.L226,.L318
	.byte	5
	.word	.L89,.L317
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('Ifx_AngleTrkF32_resetFaults')
	.sect	'.debug_abbrev'
.L186:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('Ifx_AngleTrkF32_resetFaults')
	.sect	'.debug_line'
.L187:
	.word	.L677-.L676
.L676:
	.half	3
	.word	.L679-.L678
.L678:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_AngleTrkF32.c',0,0,0,0,0
.L679:
	.byte	5,34,7,0,5,2
	.word	.L89
	.byte	3,144,3,1,5,32,9
	.half	.L680-.L89
	.byte	1,5,1,9
	.half	.L681-.L680
	.byte	3,1,1,7,9
	.half	.L189-.L681
	.byte	0,1,1
.L677:
	.sdecl	'.debug_ranges',debug,cluster('Ifx_AngleTrkF32_resetFaults')
	.sect	'.debug_ranges'
.L188:
	.word	-1,.L89,0,.L189-.L89,0,0
	.sdecl	'.debug_info',debug,cluster('Ifx_AngleTrkF32_setOffset')
	.sect	'.debug_info'
.L190:
	.word	362
	.half	3
	.word	.L191
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_AngleTrkF32.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L193,.L192
	.byte	2
	.word	.L96
	.byte	3
	.byte	'Ifx_AngleTrkF32_setOffset',0,1,146,2,6,1,1,1
	.word	.L59,.L319,.L58
	.byte	4
	.byte	'aObsv',0,1,146,2,49
	.word	.L226,.L320
	.byte	4
	.byte	'offset',0,1,146,2,78
	.word	.L278,.L321
	.byte	5
	.word	.L59,.L319
	.byte	6
	.byte	'base',0,1,148,2,28
	.word	.L276,.L322
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('Ifx_AngleTrkF32_setOffset')
	.sect	'.debug_abbrev'
.L191:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('Ifx_AngleTrkF32_setOffset')
	.sect	'.debug_line'
.L192:
	.word	.L683-.L682
.L682:
	.half	3
	.word	.L685-.L684
.L684:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_AngleTrkF32.c',0,0,0,0,0
.L685:
	.byte	5,18,7,0,5,2
	.word	.L59
	.byte	3,148,2,1,5,1,9
	.half	.L686-.L59
	.byte	3,1,1,7,9
	.half	.L194-.L686
	.byte	0,1,1
.L683:
	.sdecl	'.debug_ranges',debug,cluster('Ifx_AngleTrkF32_setOffset')
	.sect	'.debug_ranges'
.L193:
	.word	-1,.L59,0,.L194-.L59,0,0
	.sdecl	'.debug_info',debug,cluster('Ifx_AngleTrkF32_setRefreshPeriod')
	.sect	'.debug_info'
.L195:
	.word	357
	.half	3
	.word	.L196
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_AngleTrkF32.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L198,.L197
	.byte	2
	.word	.L96
	.byte	3
	.byte	'Ifx_AngleTrkF32_setRefreshPeriod',0,1,149,3,6,1,1,1
	.word	.L91,.L323,.L90
	.byte	4
	.byte	'driver',0,1,149,3,56
	.word	.L226,.L324
	.byte	4
	.byte	'updatePeriod',0,1,149,3,72
	.word	.L230,.L325
	.byte	5
	.word	.L91,.L323
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('Ifx_AngleTrkF32_setRefreshPeriod')
	.sect	'.debug_abbrev'
.L196:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('Ifx_AngleTrkF32_setRefreshPeriod')
	.sect	'.debug_line'
.L197:
	.word	.L688-.L687
.L687:
	.half	3
	.word	.L690-.L689
.L689:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_AngleTrkF32.c',0,0,0,0,0
.L690:
	.byte	5,21,7,0,5,2
	.word	.L91
	.byte	3,150,3,1,5,1,9
	.half	.L691-.L91
	.byte	3,1,1,7,9
	.half	.L199-.L691
	.byte	0,1,1
.L688:
	.sdecl	'.debug_ranges',debug,cluster('Ifx_AngleTrkF32_setRefreshPeriod')
	.sect	'.debug_ranges'
.L198:
	.word	-1,.L91,0,.L199-.L91,0,0
	.sdecl	'.debug_info',debug,cluster('Ifx_AngleTrkF32_update')
	.sect	'.debug_info'
.L200:
	.word	386
	.half	3
	.word	.L201
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_AngleTrkF32.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L203,.L202
	.byte	2
	.word	.L96
	.byte	3
	.byte	'Ifx_AngleTrkF32_update',0,1,172,2,6,1,1,1
	.word	.L63,.L326,.L62
	.byte	4
	.byte	'aObsv',0,1,172,2,46
	.word	.L226,.L327
	.byte	5
	.word	.L63,.L326
	.byte	6
	.byte	'base',0,1,174,2,28
	.word	.L276,.L328
	.byte	6
	.byte	'angleEst',0,1,175,2,29
	.word	.L230,.L329
	.byte	6
	.byte	'newPosition',0,1,177,2,29
	.word	.L278,.L330
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('Ifx_AngleTrkF32_update')
	.sect	'.debug_abbrev'
.L201:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('Ifx_AngleTrkF32_update')
	.sect	'.debug_line'
.L202:
	.word	.L693-.L692
.L692:
	.half	3
	.word	.L695-.L694
.L694:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_AngleTrkF32.c',0,0,0,0,0
.L695:
	.byte	5,6,7,0,5,2
	.word	.L63
	.byte	3,171,2,1,5,77,9
	.half	.L391-.L63
	.byte	3,3,1,5,71,9
	.half	.L696-.L391
	.byte	1,5,92,9
	.half	.L697-.L696
	.byte	1,5,86,9
	.half	.L698-.L697
	.byte	1,5,101,9
	.half	.L699-.L698
	.byte	1,5,90,9
	.half	.L390-.L699
	.byte	3,2,1,5,104,9
	.half	.L700-.L390
	.byte	1,5,102,9
	.half	.L701-.L700
	.byte	1,5,76,9
	.half	.L702-.L701
	.byte	1,5,109,9
	.half	.L703-.L702
	.byte	1,5,107,9
	.half	.L704-.L703
	.byte	1,5,43,9
	.half	.L705-.L704
	.byte	1,5,51,9
	.half	.L393-.L705
	.byte	3,1,1,5,63,9
	.half	.L706-.L393
	.byte	1,5,37,9
	.half	.L707-.L706
	.byte	1,5,23,9
	.half	.L708-.L707
	.byte	3,1,1,5,47,9
	.half	.L709-.L708
	.byte	3,2,1,5,41,9
	.half	.L710-.L709
	.byte	1,5,62,9
	.half	.L711-.L710
	.byte	1,5,56,9
	.half	.L712-.L711
	.byte	1,5,1,9
	.half	.L392-.L712
	.byte	3,1,1,7,9
	.half	.L204-.L392
	.byte	0,1,1
.L693:
	.sdecl	'.debug_ranges',debug,cluster('Ifx_AngleTrkF32_update')
	.sect	'.debug_ranges'
.L203:
	.word	-1,.L63,0,.L204-.L63,0,0
	.sdecl	'.debug_info',debug,cluster('Ifx_AngleTrkF32_stdIfPosInit')
	.sect	'.debug_info'
.L205:
	.word	350
	.half	3
	.word	.L206
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_AngleTrkF32.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L208,.L207
	.byte	2
	.word	.L96
	.byte	3
	.byte	'Ifx_AngleTrkF32_stdIfPosInit',0,1,174,3,9
	.word	.L331
	.byte	1,1,1
	.word	.L95,.L332,.L94
	.byte	4
	.byte	'stdif',0,1,174,3,52
	.word	.L333,.L334
	.byte	4
	.byte	'driver',0,1,174,3,76
	.word	.L226,.L335
	.byte	5
	.word	.L95,.L332
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('Ifx_AngleTrkF32_stdIfPosInit')
	.sect	'.debug_abbrev'
.L206:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('Ifx_AngleTrkF32_stdIfPosInit')
	.sect	'.debug_line'
.L207:
	.word	.L714-.L713
.L713:
	.half	3
	.word	.L716-.L715
.L715:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_AngleTrkF32.c',0,0,0,0,0
.L716:
	.byte	5,9,7,0,5,2
	.word	.L95
	.byte	3,173,3,1,5,19,9
	.half	.L398-.L95
	.byte	3,3,1,5,22,9
	.half	.L717-.L398
	.byte	1,5,19,9
	.half	.L396-.L717
	.byte	3,3,1,5,29,9
	.half	.L718-.L396
	.byte	3,4,1,5,28,9
	.half	.L719-.L718
	.byte	1,5,29,9
	.half	.L720-.L719
	.byte	3,1,1,5,28,9
	.half	.L721-.L720
	.byte	1,5,26,9
	.half	.L722-.L721
	.byte	3,1,1,5,25,9
	.half	.L723-.L722
	.byte	1,5,29,9
	.half	.L724-.L723
	.byte	3,1,1,5,28,9
	.half	.L725-.L724
	.byte	1,5,23,9
	.half	.L726-.L725
	.byte	3,1,1,5,22,9
	.half	.L727-.L726
	.byte	1,5,32,9
	.half	.L728-.L727
	.byte	3,1,1,5,31,9
	.half	.L729-.L728
	.byte	1,5,25,9
	.half	.L730-.L729
	.byte	3,1,1,5,24,9
	.half	.L731-.L730
	.byte	1,5,27,9
	.half	.L732-.L731
	.byte	3,1,1,5,26,9
	.half	.L733-.L732
	.byte	1,5,29,9
	.half	.L734-.L733
	.byte	3,1,1,5,28,9
	.half	.L735-.L734
	.byte	1,5,29,9
	.half	.L736-.L735
	.byte	3,1,1,5,28,9
	.half	.L737-.L736
	.byte	1,5,32,9
	.half	.L738-.L737
	.byte	3,1,1,5,31,9
	.half	.L739-.L738
	.byte	1,5,20,9
	.half	.L740-.L739
	.byte	3,1,1,5,19,9
	.half	.L741-.L740
	.byte	1,5,25,9
	.half	.L742-.L741
	.byte	3,1,1,5,24,9
	.half	.L743-.L742
	.byte	1,5,29,9
	.half	.L744-.L743
	.byte	3,1,1,5,28,9
	.half	.L745-.L744
	.byte	1,5,21,9
	.half	.L746-.L745
	.byte	3,1,1,5,20,9
	.half	.L747-.L746
	.byte	1,5,25,9
	.half	.L748-.L747
	.byte	3,1,1,5,24,9
	.half	.L749-.L748
	.byte	1,5,27,9
	.half	.L750-.L749
	.byte	3,1,1,5,26,9
	.half	.L751-.L750
	.byte	1,9
	.half	.L752-.L751
	.byte	3,1,1,5,25,9
	.half	.L753-.L752
	.byte	1,5,23,9
	.half	.L754-.L753
	.byte	3,1,1,5,22,9
	.half	.L755-.L754
	.byte	1,5,29,9
	.half	.L756-.L755
	.byte	3,1,1,5,28,9
	.half	.L757-.L756
	.byte	1,5,29,9
	.half	.L758-.L757
	.byte	3,1,1,5,28,9
	.half	.L759-.L758
	.byte	1,5,12,9
	.half	.L760-.L759
	.byte	3,3,1,5,5,9
	.half	.L761-.L760
	.byte	1,5,1,9
	.half	.L45-.L761
	.byte	3,1,1,7,9
	.half	.L209-.L45
	.byte	0,1,1
.L714:
	.sdecl	'.debug_ranges',debug,cluster('Ifx_AngleTrkF32_stdIfPosInit')
	.sect	'.debug_ranges'
.L208:
	.word	-1,.L95,0,.L209-.L95,0,0
	.sdecl	'.debug_info',debug,cluster('Ifx_AngleTrkF32_setUserSampling')
	.sect	'.debug_info'
.L210:
	.word	345
	.half	3
	.word	.L211
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_AngleTrkF32.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L213,.L212
	.byte	2
	.word	.L96
	.byte	3
	.byte	'Ifx_AngleTrkF32_setUserSampling',0,1,153,1,6,1,1,1
	.word	.L53,.L336,.L52
	.byte	4
	.byte	'aObsv',0,1,153,1,55
	.word	.L226,.L337
	.byte	4
	.byte	'Ts',0,1,153,1,70
	.word	.L230,.L338
	.byte	5
	.word	.L53,.L336
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('Ifx_AngleTrkF32_setUserSampling')
	.sect	'.debug_abbrev'
.L211:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('Ifx_AngleTrkF32_setUserSampling')
	.sect	'.debug_line'
.L212:
	.word	.L763-.L762
.L762:
	.half	3
	.word	.L765-.L764
.L764:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_AngleTrkF32.c',0,0,0,0,0
.L765:
	.byte	5,20,7,0,5,2
	.word	.L53
	.byte	3,154,1,1,5,39,9
	.half	.L766-.L53
	.byte	3,1,1,5,37,9
	.half	.L767-.L766
	.byte	1,5,20,9
	.half	.L768-.L767
	.byte	1,5,1,9
	.half	.L769-.L768
	.byte	3,1,1,7,9
	.half	.L214-.L769
	.byte	0,1,1
.L763:
	.sdecl	'.debug_ranges',debug,cluster('Ifx_AngleTrkF32_setUserSampling')
	.sect	'.debug_ranges'
.L213:
	.word	-1,.L53,0,.L214-.L53,0,0
	.sdecl	'.debug_info',debug,cluster('round_f')
	.sect	'.debug_info'
.L215:
	.word	324
	.half	3
	.word	.L216
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_AngleTrkF32.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L218,.L217
	.byte	2
	.word	.L96
	.byte	3
	.byte	'round_f',0,1,99,9
	.word	.L230
	.byte	1,1,1
	.word	.L47,.L339,.L46
	.byte	4
	.byte	'val',0,1,99,25
	.word	.L230,.L340
	.byte	5
	.word	.L47,.L339
	.byte	6
	.byte	'val_i',0,1,102,12
	.word	.L278,.L341
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('round_f')
	.sect	'.debug_abbrev'
.L216:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('round_f')
	.sect	'.debug_line'
.L217:
	.word	.L771-.L770
.L770:
	.half	3
	.word	.L773-.L772
.L772:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_AngleTrkF32.c',0,0,0,0,0
.L773:
	.byte	5,20,7,0,5,2
	.word	.L47
	.byte	3,229,0,1,5,16,9
	.half	.L349-.L47
	.byte	3,3,1,5,14,9
	.half	.L774-.L349
	.byte	1,5,34,9
	.half	.L775-.L774
	.byte	1,5,32,9
	.half	.L776-.L775
	.byte	1,5,5,9
	.half	.L777-.L776
	.byte	1,5,15,7,9
	.half	.L778-.L777
	.byte	3,6,1,5,35,9
	.half	.L351-.L778
	.byte	3,124,1,5,31,9
	.half	.L2-.L351
	.byte	1,5,15,9
	.half	.L350-.L2
	.byte	1,5,5,9
	.half	.L3-.L350
	.byte	3,7,1,5,1,9
	.half	.L4-.L3
	.byte	3,1,1,7,9
	.half	.L219-.L4
	.byte	0,1,1
.L771:
	.sdecl	'.debug_ranges',debug,cluster('round_f')
	.sect	'.debug_ranges'
.L218:
	.word	-1,.L47,0,.L219-.L47,0,0
	.sdecl	'.debug_info',debug,cluster('Ifx_AngleTrkF32_setControlGains')
	.sect	'.debug_info'
.L220:
	.word	373
	.half	3
	.word	.L221
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_AngleTrkF32.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L223,.L222
	.byte	2
	.word	.L96
	.byte	3
	.byte	'Ifx_AngleTrkF32_setControlGains',0,1,124,6,1,1,1
	.word	.L49,.L342,.L48
	.byte	4
	.byte	'cfgData',0,1,124,63
	.word	.L343,.L344
	.byte	4
	.byte	'K',0,1,124,80
	.word	.L230,.L345
	.byte	4
	.byte	'T',0,1,124,91
	.word	.L230,.L346
	.byte	4
	.byte	'psi',0,1,124,102
	.word	.L230,.L347
	.byte	5
	.word	.L49,.L342
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('Ifx_AngleTrkF32_setControlGains')
	.sect	'.debug_abbrev'
.L221:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('Ifx_AngleTrkF32_setControlGains')
	.sect	'.debug_line'
.L222:
	.word	.L780-.L779
.L779:
	.half	3
	.word	.L782-.L781
.L781:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_AngleTrkF32.c',0,0,0,0,0
.L782:
	.byte	5,21,7,0,5,2
	.word	.L49
	.byte	3,253,0,1,5,35,9
	.half	.L783-.L49
	.byte	1,5,28,9
	.half	.L784-.L783
	.byte	1,5,44,9
	.half	.L785-.L784
	.byte	1,5,42,9
	.half	.L786-.L785
	.byte	1,5,52,9
	.half	.L787-.L786
	.byte	1,5,47,9
	.half	.L788-.L787
	.byte	1,5,17,9
	.half	.L789-.L788
	.byte	1,5,38,9
	.half	.L790-.L789
	.byte	3,1,1,5,36,9
	.half	.L791-.L790
	.byte	1,5,21,9
	.half	.L792-.L791
	.byte	1,5,46,9
	.half	.L793-.L792
	.byte	1,5,50,9
	.half	.L794-.L793
	.byte	1,5,41,9
	.half	.L795-.L794
	.byte	1,5,17,9
	.half	.L796-.L795
	.byte	1,5,24,9
	.half	.L797-.L796
	.byte	3,1,1,5,22,9
	.half	.L798-.L797
	.byte	1,5,27,9
	.half	.L799-.L798
	.byte	1,5,17,9
	.half	.L800-.L799
	.byte	1,5,1,9
	.half	.L801-.L800
	.byte	3,1,1,7,9
	.half	.L224-.L801
	.byte	0,1,1
.L780:
	.sdecl	'.debug_ranges',debug,cluster('Ifx_AngleTrkF32_setControlGains')
	.sect	'.debug_ranges'
.L223:
	.word	-1,.L49,0,.L224-.L49,0,0
	.sdecl	'.debug_loc',debug,cluster('Ifx_AngleTrkF32_getAbsolutePosition')
	.sect	'.debug_loc'
.L68:
	.word	-1,.L69,0,.L280-.L69
	.half	2
	.byte	138,0
	.word	0,0
.L281:
	.word	-1,.L69,0,.L395-.L69
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Ifx_AngleTrkF32_getDirection')
	.sect	'.debug_loc'
.L70:
	.word	-1,.L71,0,.L283-.L71
	.half	2
	.byte	138,0
	.word	0,0
.L284:
	.word	-1,.L71,0,.L283-.L71
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Ifx_AngleTrkF32_getFault')
	.sect	'.debug_loc'
.L72:
	.word	-1,.L73,0,.L286-.L73
	.half	2
	.byte	138,0
	.word	0,0
.L287:
	.word	-1,.L73,0,.L286-.L73
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Ifx_AngleTrkF32_getOffset')
	.sect	'.debug_loc'
.L74:
	.word	-1,.L75,0,.L288-.L75
	.half	2
	.byte	138,0
	.word	0,0
.L289:
	.word	-1,.L75,0,.L288-.L75
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Ifx_AngleTrkF32_getPeriodPerRotation')
	.sect	'.debug_loc'
.L76:
	.word	-1,.L77,0,.L291-.L77
	.half	2
	.byte	138,0
	.word	0,0
.L292:
	.word	-1,.L77,0,.L291-.L77
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Ifx_AngleTrkF32_getPosition')
	.sect	'.debug_loc'
.L78:
	.word	-1,.L79,0,.L293-.L79
	.half	2
	.byte	138,0
	.word	0,0
.L294:
	.word	-1,.L79,0,.L293-.L79
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Ifx_AngleTrkF32_getRawPosition')
	.sect	'.debug_loc'
.L64:
	.word	-1,.L65,0,.L295-.L65
	.half	2
	.byte	138,0
	.word	0,0
.L296:
	.word	-1,.L65,0,.L295-.L65
	.half	1
	.byte	100
	.word	0,0
.L297:
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Ifx_AngleTrkF32_getRefreshPeriod')
	.sect	'.debug_loc'
.L80:
	.word	-1,.L81,0,.L298-.L81
	.half	2
	.byte	138,0
	.word	0,0
.L299:
	.word	-1,.L81,0,.L298-.L81
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Ifx_AngleTrkF32_getResolution')
	.sect	'.debug_loc'
.L82:
	.word	-1,.L83,0,.L300-.L83
	.half	2
	.byte	138,0
	.word	0,0
.L301:
	.word	-1,.L83,0,.L300-.L83
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Ifx_AngleTrkF32_getSensorType')
	.sect	'.debug_loc'
.L84:
	.word	-1,.L85,0,.L305-.L85
	.half	2
	.byte	138,0
	.word	0,0
.L306:
	.word	-1,.L85,0,.L305-.L85
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Ifx_AngleTrkF32_getSpeed')
	.sect	'.debug_loc'
.L66:
	.word	-1,.L67,0,.L307-.L67
	.half	2
	.byte	138,0
	.word	0,0
.L308:
	.word	-1,.L67,0,.L307-.L67
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Ifx_AngleTrkF32_getTurn')
	.sect	'.debug_loc'
.L86:
	.word	-1,.L87,0,.L302-.L87
	.half	2
	.byte	138,0
	.word	0,0
.L303:
	.word	-1,.L87,0,.L302-.L87
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Ifx_AngleTrkF32_init')
	.sect	'.debug_loc'
.L54:
	.word	-1,.L55,0,.L358-.L55
	.half	2
	.byte	138,0
	.word	.L358-.L55,.L225-.L55
	.half	2
	.byte	138,16
	.word	.L225-.L55,.L225-.L55
	.half	2
	.byte	138,0
	.word	0,0
.L231:
	.word	-1,.L55,0,.L359-.L55
	.half	1
	.byte	84
	.word	.L363-.L55,.L225-.L55
	.half	1
	.byte	88
	.word	.L366-.L55,.L365-.L55
	.half	1
	.byte	84
	.word	0,0
.L227:
	.word	-1,.L55,0,.L360-.L55
	.half	1
	.byte	100
	.word	.L361-.L55,.L225-.L55
	.half	1
	.byte	108
	.word	.L364-.L55,.L365-.L55
	.half	1
	.byte	100
	.word	0,0
.L229:
	.word	-1,.L55,0,.L360-.L55
	.half	1
	.byte	101
	.word	.L362-.L55,.L225-.L55
	.half	1
	.byte	111
	.word	0,0
.L235:
	.word	-1,.L55,0,.L225-.L55
	.half	2
	.byte	145,112
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Ifx_AngleTrkF32_initConfig')
	.sect	'.debug_loc'
.L50:
	.word	-1,.L51,0,.L352-.L51
	.half	2
	.byte	138,0
	.word	.L352-.L51,.L236-.L51
	.half	2
	.byte	138,24
	.word	.L236-.L51,.L236-.L51
	.half	2
	.byte	138,0
	.word	0,0
.L243:
	.word	-1,.L51,0,.L236-.L51
	.half	2
	.byte	145,104
	.word	0,0
.L238:
	.word	-1,.L51,0,.L353-.L51
	.half	1
	.byte	100
	.word	.L355-.L51,.L236-.L51
	.half	1
	.byte	111
	.word	0,0
.L241:
	.word	-1,.L51,0,.L354-.L51
	.half	1
	.byte	102
	.word	.L357-.L51,.L236-.L51
	.half	1
	.byte	109
	.word	0,0
.L240:
	.word	-1,.L51,0,.L354-.L51
	.half	1
	.byte	101
	.word	.L356-.L51,.L236-.L51
	.half	1
	.byte	108
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Ifx_AngleTrkF32_reset')
	.sect	'.debug_loc'
.L92:
	.word	-1,.L93,0,.L309-.L93
	.half	2
	.byte	138,0
	.word	0,0
.L310:
	.word	-1,.L93,0,.L309-.L93
	.half	1
	.byte	100
	.word	0,0
.L315:
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Ifx_AngleTrkF32_resetFaults')
	.sect	'.debug_loc'
.L88:
	.word	-1,.L89,0,.L317-.L89
	.half	2
	.byte	138,0
	.word	0,0
.L318:
	.word	-1,.L89,0,.L317-.L89
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Ifx_AngleTrkF32_setControlGains')
	.sect	'.debug_loc'
.L48:
	.word	-1,.L49,0,.L342-.L49
	.half	2
	.byte	138,0
	.word	0,0
.L345:
	.word	-1,.L49,0,.L342-.L49
	.half	1
	.byte	84
	.word	0,0
.L346:
	.word	-1,.L49,0,.L342-.L49
	.half	1
	.byte	85
	.word	0,0
.L344:
	.word	-1,.L49,0,.L342-.L49
	.half	1
	.byte	100
	.word	0,0
.L347:
	.word	-1,.L49,0,.L342-.L49
	.half	1
	.byte	86
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Ifx_AngleTrkF32_setOffset')
	.sect	'.debug_loc'
.L58:
	.word	-1,.L59,0,.L319-.L59
	.half	2
	.byte	138,0
	.word	0,0
.L320:
	.word	-1,.L59,0,.L319-.L59
	.half	1
	.byte	100
	.word	0,0
.L322:
	.word	0,0
.L321:
	.word	-1,.L59,0,.L319-.L59
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Ifx_AngleTrkF32_setRefreshPeriod')
	.sect	'.debug_loc'
.L90:
	.word	-1,.L91,0,.L323-.L91
	.half	2
	.byte	138,0
	.word	0,0
.L324:
	.word	-1,.L91,0,.L323-.L91
	.half	1
	.byte	100
	.word	0,0
.L325:
	.word	-1,.L91,0,.L323-.L91
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Ifx_AngleTrkF32_setUserSampling')
	.sect	'.debug_loc'
.L52:
	.word	-1,.L53,0,.L336-.L53
	.half	2
	.byte	138,0
	.word	0,0
.L338:
	.word	-1,.L53,0,.L336-.L53
	.half	1
	.byte	84
	.word	0,0
.L337:
	.word	-1,.L53,0,.L336-.L53
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Ifx_AngleTrkF32_stdIfPosInit')
	.sect	'.debug_loc'
.L94:
	.word	-1,.L95,0,.L332-.L95
	.half	2
	.byte	138,0
	.word	0,0
.L335:
	.word	-1,.L95,0,.L396-.L95
	.half	1
	.byte	101
	.word	.L398-.L95,.L332-.L95
	.half	1
	.byte	108
	.word	0,0
.L334:
	.word	-1,.L95,0,.L396-.L95
	.half	1
	.byte	100
	.word	.L397-.L95,.L332-.L95
	.half	1
	.byte	111
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Ifx_AngleTrkF32_step')
	.sect	'.debug_loc'
.L56:
	.word	-1,.L57,0,.L244-.L57
	.half	2
	.byte	138,0
	.word	0,0
.L245:
	.word	-1,.L57,0,.L367-.L57
	.half	1
	.byte	100
	.word	.L370-.L57,.L244-.L57
	.half	1
	.byte	111
	.word	.L11-.L57,.L12-.L57
	.half	1
	.byte	100
	.word	0,0
.L263:
	.word	0,0
.L251:
	.word	-1,.L57,.L381-.L57,.L382-.L57
	.half	1
	.byte	82
	.word	0,0
.L250:
	.word	-1,.L57,.L367-.L57,.L11-.L57
	.half	1
	.byte	82
	.word	.L12-.L57,.L374-.L57
	.half	1
	.byte	82
	.word	.L374-.L57,.L375-.L57
	.half	1
	.byte	95
	.word	0,0
.L248:
	.word	-1,.L57,0,.L368-.L57
	.half	1
	.byte	85
	.word	.L11-.L57,.L372-.L57
	.half	1
	.byte	85
	.word	0,0
.L252:
	.word	-1,.L57,.L376-.L57,.L377-.L57
	.half	1
	.byte	95
	.word	0,0
.L254:
	.word	-1,.L57,.L380-.L57,.L261-.L57
	.half	1
	.byte	95
	.word	0,0
.L253:
	.word	-1,.L57,.L378-.L57,.L379-.L57
	.half	1
	.byte	95
	.word	0,0
.L271:
	.word	-1,.L57,.L384-.L57,.L385-.L57
	.half	1
	.byte	81
	.word	0,0
.L268:
	.word	-1,.L57,.L383-.L57,.L269-.L57
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L258:
	.word	0,0
.L265:
	.word	0,0
.L249:
	.word	-1,.L57,0,.L367-.L57
	.half	1
	.byte	86
	.word	.L371-.L57,.L244-.L57
	.half	1
	.byte	88
	.word	.L11-.L57,.L12-.L57
	.half	1
	.byte	86
	.word	0,0
.L247:
	.word	-1,.L57,0,.L369-.L57
	.half	1
	.byte	84
	.word	.L11-.L57,.L373-.L57
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Ifx_AngleTrkF32_update')
	.sect	'.debug_loc'
.L62:
	.word	-1,.L63,0,.L326-.L63
	.half	2
	.byte	138,0
	.word	0,0
.L327:
	.word	-1,.L63,0,.L390-.L63
	.half	1
	.byte	100
	.word	.L391-.L63,.L326-.L63
	.half	1
	.byte	111
	.word	.L394-.L63,.L392-.L63
	.half	1
	.byte	100
	.word	0,0
.L329:
	.word	-1,.L63,.L390-.L63,.L392-.L63
	.half	1
	.byte	82
	.word	0,0
.L328:
	.word	0,0
.L330:
	.word	-1,.L63,.L393-.L63,.L326-.L63
	.half	1
	.byte	95
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Ifx_AngleTrkF32_updateStatus')
	.sect	'.debug_loc'
.L60:
	.word	-1,.L61,0,.L272-.L61
	.half	2
	.byte	138,0
	.word	0,0
.L273:
	.word	-1,.L61,0,.L386-.L61
	.half	1
	.byte	100
	.word	.L388-.L61,.L272-.L61
	.half	1
	.byte	111
	.word	0,0
.L277:
	.word	0,0
.L275:
	.word	-1,.L61,0,.L386-.L61
	.half	1
	.byte	85
	.word	0,0
.L274:
	.word	-1,.L61,0,.L387-.L61
	.half	1
	.byte	84
	.word	0,0
.L279:
	.word	-1,.L61,.L389-.L61,.L386-.L61
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('round_f')
	.sect	'.debug_loc'
.L46:
	.word	-1,.L47,0,.L339-.L47
	.half	2
	.byte	138,0
	.word	0,0
.L340:
	.word	-1,.L47,0,.L348-.L47
	.half	1
	.byte	84
	.word	.L351-.L47,.L2-.L47
	.half	1
	.byte	82
	.word	.L3-.L47,.L339-.L47
	.half	1
	.byte	82
	.word	0,0
.L341:
	.word	-1,.L47,.L349-.L47,.L350-.L47
	.half	1
	.byte	95
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L802:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('round_f')
	.sect	'.debug_frame'
	.word	12
	.word	.L802,.L47,.L339-.L47
	.sdecl	'.debug_frame',debug,cluster('Ifx_AngleTrkF32_setControlGains')
	.sect	'.debug_frame'
	.word	24
	.word	.L802,.L49,.L342-.L49
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('Ifx_AngleTrkF32_initConfig')
	.sect	'.debug_frame'
	.word	36
	.word	.L802,.L51,.L236-.L51
	.byte	4
	.word	(.L352-.L51)/2
	.byte	19,24,22,26,3,19,138,24,4
	.word	(.L236-.L352)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('Ifx_AngleTrkF32_setUserSampling')
	.sect	'.debug_frame'
	.word	24
	.word	.L802,.L53,.L336-.L53
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('Ifx_AngleTrkF32_init')
	.sect	'.debug_frame'
	.word	36
	.word	.L802,.L55,.L225-.L55
	.byte	4
	.word	(.L358-.L55)/2
	.byte	19,16,22,26,3,19,138,16,4
	.word	(.L225-.L358)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('Ifx_AngleTrkF32_step')
	.sect	'.debug_frame'
	.word	12
	.word	.L802,.L57,.L244-.L57
	.sdecl	'.debug_frame',debug,cluster('Ifx_AngleTrkF32_setOffset')
	.sect	'.debug_frame'
	.word	24
	.word	.L802,.L59,.L319-.L59
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('Ifx_AngleTrkF32_updateStatus')
	.sect	'.debug_frame'
	.word	12
	.word	.L802,.L61,.L272-.L61
	.sdecl	'.debug_frame',debug,cluster('Ifx_AngleTrkF32_update')
	.sect	'.debug_frame'
	.word	12
	.word	.L802,.L63,.L326-.L63
	.sdecl	'.debug_frame',debug,cluster('Ifx_AngleTrkF32_getRawPosition')
	.sect	'.debug_frame'
	.word	24
	.word	.L802,.L65,.L295-.L65
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('Ifx_AngleTrkF32_getSpeed')
	.sect	'.debug_frame'
	.word	24
	.word	.L802,.L67,.L307-.L67
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('Ifx_AngleTrkF32_getAbsolutePosition')
	.sect	'.debug_frame'
	.word	12
	.word	.L802,.L69,.L280-.L69
	.sdecl	'.debug_frame',debug,cluster('Ifx_AngleTrkF32_getDirection')
	.sect	'.debug_frame'
	.word	24
	.word	.L802,.L71,.L283-.L71
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('Ifx_AngleTrkF32_getFault')
	.sect	'.debug_frame'
	.word	24
	.word	.L802,.L73,.L286-.L73
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('Ifx_AngleTrkF32_getOffset')
	.sect	'.debug_frame'
	.word	24
	.word	.L802,.L75,.L288-.L75
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('Ifx_AngleTrkF32_getPeriodPerRotation')
	.sect	'.debug_frame'
	.word	24
	.word	.L802,.L77,.L291-.L77
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('Ifx_AngleTrkF32_getPosition')
	.sect	'.debug_frame'
	.word	24
	.word	.L802,.L79,.L293-.L79
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('Ifx_AngleTrkF32_getRefreshPeriod')
	.sect	'.debug_frame'
	.word	24
	.word	.L802,.L81,.L298-.L81
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('Ifx_AngleTrkF32_getResolution')
	.sect	'.debug_frame'
	.word	24
	.word	.L802,.L83,.L300-.L83
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('Ifx_AngleTrkF32_getSensorType')
	.sect	'.debug_frame'
	.word	24
	.word	.L802,.L85,.L305-.L85
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('Ifx_AngleTrkF32_getTurn')
	.sect	'.debug_frame'
	.word	24
	.word	.L802,.L87,.L302-.L87
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('Ifx_AngleTrkF32_resetFaults')
	.sect	'.debug_frame'
	.word	24
	.word	.L802,.L89,.L317-.L89
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('Ifx_AngleTrkF32_setRefreshPeriod')
	.sect	'.debug_frame'
	.word	24
	.word	.L802,.L91,.L323-.L91
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('Ifx_AngleTrkF32_reset')
	.sect	'.debug_frame'
	.word	24
	.word	.L802,.L93,.L309-.L93
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('Ifx_AngleTrkF32_stdIfPosInit')
	.sect	'.debug_frame'
	.word	12
	.word	.L802,.L95,.L332-.L95
	; Module end
