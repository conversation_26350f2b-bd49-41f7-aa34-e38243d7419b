	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc44092a --dep-file=IfxStm_cfg.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxStm_cfg.src ../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxStm_cfg.c"
	.compiler_name		"ctc"
	;source	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxStm_cfg.c'

	
$TC16X
	
	.sdecl	'.rodata.IfxStm_cfg.IfxStm_cfg_indexMap',data,rom,cluster('IfxStm_cfg_indexMap')
	.sect	'.rodata.IfxStm_cfg.IfxStm_cfg_indexMap'
	.global	IfxStm_cfg_indexMap
	.align	4
IfxStm_cfg_indexMap:	.type	object
	.size	IfxStm_cfg_indexMap,16
	.word	-268435456
	.space	4
	.word	-268435200,1
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L3:
	.word	5035
	.half	3
	.word	.L4
	.byte	4
.L2:
	.byte	1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxStm_cfg.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L5
	.byte	2,1,1,3
	.word	232
	.byte	4
	.byte	'Ifx__jump_and_link',0,3,1,61,17,1,1,5
	.byte	'fun',0,1,61,43
	.word	235
	.byte	6,0,7
	.byte	'__fract',0,4,128,1,7
	.byte	'float',0,4,4,8
	.byte	'Ifx__float_to_fract',0,3,1,152,2,18
	.word	280
	.byte	1,1,5
	.byte	'a',0,1,152,2,44
	.word	292
	.byte	6,0,7
	.byte	'unsigned long long int',0,8,7,9
	.byte	'void',0,3
	.word	372
	.byte	8
	.byte	'__ld64',0,3,2,135,1,19
	.word	346
	.byte	1,1,5
	.byte	'addr',0,2,135,1,32
	.word	378
	.byte	6,0,4
	.byte	'__st64',0,3,2,143,1,17,1,1,5
	.byte	'addr',0,2,143,1,30
	.word	378
	.byte	5
	.byte	'value',0,2,143,1,43
	.word	346
	.byte	6,0,10
	.word	240
	.byte	11
	.word	266
	.byte	6,0,10
	.word	301
	.byte	11
	.word	333
	.byte	6,0,10
	.word	383
	.byte	11
	.word	402
	.byte	6,0,10
	.word	418
	.byte	11
	.word	433
	.byte	11
	.word	447
	.byte	6,0,7
	.byte	'short int',0,2,5,12
	.byte	'__wchar_t',0,3,1,1
	.word	517
	.byte	7
	.byte	'unsigned int',0,4,7,12
	.byte	'__size_t',0,3,1,1
	.word	548
	.byte	7
	.byte	'int',0,4,5,12
	.byte	'__ptrdiff_t',0,3,1,1
	.word	581
	.byte	13,1,3
	.word	608
	.byte	12
	.byte	'__codeptr',0,3,1,1
	.word	610
	.byte	7
	.byte	'unsigned char',0,1,8,12
	.byte	'uint8',0,4,105,29
	.word	633
	.byte	7
	.byte	'unsigned short int',0,2,7,12
	.byte	'uint16',0,4,109,29
	.word	664
	.byte	7
	.byte	'unsigned long int',0,4,7,12
	.byte	'uint32',0,4,113,29
	.word	701
	.byte	12
	.byte	'uint64',0,4,118,29
	.word	346
	.byte	12
	.byte	'sint16',0,4,126,29
	.word	517
	.byte	7
	.byte	'long int',0,4,5,12
	.byte	'sint32',0,4,131,1,29
	.word	767
	.byte	7
	.byte	'long long int',0,8,5,12
	.byte	'sint64',0,4,138,1,29
	.word	795
	.byte	12
	.byte	'float32',0,4,167,1,29
	.word	292
	.byte	12
	.byte	'pvoid',0,5,57,28
	.word	378
	.byte	12
	.byte	'Ifx_TickTime',0,5,79,28
	.word	795
	.byte	14
	.word	372
	.byte	3
	.word	880
	.byte	15,5,143,1,9,8,16
	.byte	'module',0
	.word	885
	.byte	4,2,35,0,16
	.byte	'index',0
	.word	767
	.byte	4,2,35,4,0,12
	.byte	'IfxModule_IndexMap',0,5,147,1,3
	.word	890
	.byte	17
	.byte	'_Ifx_STM_ACCEN0_Bits',0,6,45,16,4,18
	.byte	'EN0',0,1
	.word	633
	.byte	1,7,2,35,0,18
	.byte	'EN1',0,1
	.word	633
	.byte	1,6,2,35,0,18
	.byte	'EN2',0,1
	.word	633
	.byte	1,5,2,35,0,18
	.byte	'EN3',0,1
	.word	633
	.byte	1,4,2,35,0,18
	.byte	'EN4',0,1
	.word	633
	.byte	1,3,2,35,0,18
	.byte	'EN5',0,1
	.word	633
	.byte	1,2,2,35,0,18
	.byte	'EN6',0,1
	.word	633
	.byte	1,1,2,35,0,18
	.byte	'EN7',0,1
	.word	633
	.byte	1,0,2,35,0,18
	.byte	'EN8',0,1
	.word	633
	.byte	1,7,2,35,1,18
	.byte	'EN9',0,1
	.word	633
	.byte	1,6,2,35,1,18
	.byte	'EN10',0,1
	.word	633
	.byte	1,5,2,35,1,18
	.byte	'EN11',0,1
	.word	633
	.byte	1,4,2,35,1,18
	.byte	'EN12',0,1
	.word	633
	.byte	1,3,2,35,1,18
	.byte	'EN13',0,1
	.word	633
	.byte	1,2,2,35,1,18
	.byte	'EN14',0,1
	.word	633
	.byte	1,1,2,35,1,18
	.byte	'EN15',0,1
	.word	633
	.byte	1,0,2,35,1,18
	.byte	'EN16',0,1
	.word	633
	.byte	1,7,2,35,2,18
	.byte	'EN17',0,1
	.word	633
	.byte	1,6,2,35,2,18
	.byte	'EN18',0,1
	.word	633
	.byte	1,5,2,35,2,18
	.byte	'EN19',0,1
	.word	633
	.byte	1,4,2,35,2,18
	.byte	'EN20',0,1
	.word	633
	.byte	1,3,2,35,2,18
	.byte	'EN21',0,1
	.word	633
	.byte	1,2,2,35,2,18
	.byte	'EN22',0,1
	.word	633
	.byte	1,1,2,35,2,18
	.byte	'EN23',0,1
	.word	633
	.byte	1,0,2,35,2,18
	.byte	'EN24',0,1
	.word	633
	.byte	1,7,2,35,3,18
	.byte	'EN25',0,1
	.word	633
	.byte	1,6,2,35,3,18
	.byte	'EN26',0,1
	.word	633
	.byte	1,5,2,35,3,18
	.byte	'EN27',0,1
	.word	633
	.byte	1,4,2,35,3,18
	.byte	'EN28',0,1
	.word	633
	.byte	1,3,2,35,3,18
	.byte	'EN29',0,1
	.word	633
	.byte	1,2,2,35,3,18
	.byte	'EN30',0,1
	.word	633
	.byte	1,1,2,35,3,18
	.byte	'EN31',0,1
	.word	633
	.byte	1,0,2,35,3,0,12
	.byte	'Ifx_STM_ACCEN0_Bits',0,6,79,3
	.word	956
	.byte	17
	.byte	'_Ifx_STM_ACCEN1_Bits',0,6,82,16,4,18
	.byte	'reserved_0',0,4
	.word	548
	.byte	32,0,2,35,0,0,12
	.byte	'Ifx_STM_ACCEN1_Bits',0,6,85,3
	.word	1513
	.byte	17
	.byte	'_Ifx_STM_CAP_Bits',0,6,88,16,4,18
	.byte	'STMCAP63_32',0,4
	.word	548
	.byte	32,0,2,35,0,0,12
	.byte	'Ifx_STM_CAP_Bits',0,6,91,3
	.word	1590
	.byte	17
	.byte	'_Ifx_STM_CAPSV_Bits',0,6,94,16,4,18
	.byte	'STMCAP63_32',0,4
	.word	548
	.byte	32,0,2,35,0,0,12
	.byte	'Ifx_STM_CAPSV_Bits',0,6,97,3
	.word	1662
	.byte	17
	.byte	'_Ifx_STM_CLC_Bits',0,6,100,16,4,18
	.byte	'DISR',0,1
	.word	633
	.byte	1,7,2,35,0,18
	.byte	'DISS',0,1
	.word	633
	.byte	1,6,2,35,0,18
	.byte	'reserved_2',0,1
	.word	633
	.byte	1,5,2,35,0,18
	.byte	'EDIS',0,1
	.word	633
	.byte	1,4,2,35,0,18
	.byte	'reserved_4',0,4
	.word	548
	.byte	28,0,2,35,0,0,12
	.byte	'Ifx_STM_CLC_Bits',0,6,107,3
	.word	1738
	.byte	17
	.byte	'_Ifx_STM_CMCON_Bits',0,6,110,16,4,18
	.byte	'MSIZE0',0,1
	.word	633
	.byte	5,3,2,35,0,18
	.byte	'reserved_5',0,1
	.word	633
	.byte	3,0,2,35,0,18
	.byte	'MSTART0',0,1
	.word	633
	.byte	5,3,2,35,1,18
	.byte	'reserved_13',0,1
	.word	633
	.byte	3,0,2,35,1,18
	.byte	'MSIZE1',0,1
	.word	633
	.byte	5,3,2,35,2,18
	.byte	'reserved_21',0,1
	.word	633
	.byte	3,0,2,35,2,18
	.byte	'MSTART1',0,1
	.word	633
	.byte	5,3,2,35,3,18
	.byte	'reserved_29',0,1
	.word	633
	.byte	3,0,2,35,3,0,12
	.byte	'Ifx_STM_CMCON_Bits',0,6,120,3
	.word	1879
	.byte	17
	.byte	'_Ifx_STM_CMP_Bits',0,6,123,16,4,18
	.byte	'CMPVAL',0,4
	.word	548
	.byte	32,0,2,35,0,0,12
	.byte	'Ifx_STM_CMP_Bits',0,6,126,3
	.word	2097
	.byte	17
	.byte	'_Ifx_STM_ICR_Bits',0,6,129,1,16,4,18
	.byte	'CMP0EN',0,1
	.word	633
	.byte	1,7,2,35,0,18
	.byte	'CMP0IR',0,1
	.word	633
	.byte	1,6,2,35,0,18
	.byte	'CMP0OS',0,1
	.word	633
	.byte	1,5,2,35,0,18
	.byte	'reserved_3',0,1
	.word	633
	.byte	1,4,2,35,0,18
	.byte	'CMP1EN',0,1
	.word	633
	.byte	1,3,2,35,0,18
	.byte	'CMP1IR',0,1
	.word	633
	.byte	1,2,2,35,0,18
	.byte	'CMP1OS',0,1
	.word	633
	.byte	1,1,2,35,0,18
	.byte	'reserved_7',0,4
	.word	548
	.byte	25,0,2,35,0,0,12
	.byte	'Ifx_STM_ICR_Bits',0,6,139,1,3
	.word	2164
	.byte	17
	.byte	'_Ifx_STM_ID_Bits',0,6,142,1,16,4,18
	.byte	'MODREV',0,1
	.word	633
	.byte	8,0,2,35,0,18
	.byte	'MODTYPE',0,1
	.word	633
	.byte	8,0,2,35,1,18
	.byte	'MODNUMBER',0,2
	.word	664
	.byte	16,0,2,35,2,0,12
	.byte	'Ifx_STM_ID_Bits',0,6,147,1,3
	.word	2367
	.byte	17
	.byte	'_Ifx_STM_ISCR_Bits',0,6,150,1,16,4,18
	.byte	'CMP0IRR',0,1
	.word	633
	.byte	1,7,2,35,0,18
	.byte	'CMP0IRS',0,1
	.word	633
	.byte	1,6,2,35,0,18
	.byte	'CMP1IRR',0,1
	.word	633
	.byte	1,5,2,35,0,18
	.byte	'CMP1IRS',0,1
	.word	633
	.byte	1,4,2,35,0,18
	.byte	'reserved_4',0,4
	.word	548
	.byte	28,0,2,35,0,0,12
	.byte	'Ifx_STM_ISCR_Bits',0,6,157,1,3
	.word	2474
	.byte	17
	.byte	'_Ifx_STM_KRST0_Bits',0,6,160,1,16,4,18
	.byte	'RST',0,1
	.word	633
	.byte	1,7,2,35,0,18
	.byte	'RSTSTAT',0,1
	.word	633
	.byte	1,6,2,35,0,18
	.byte	'reserved_2',0,4
	.word	548
	.byte	30,0,2,35,0,0,12
	.byte	'Ifx_STM_KRST0_Bits',0,6,165,1,3
	.word	2625
	.byte	17
	.byte	'_Ifx_STM_KRST1_Bits',0,6,168,1,16,4,18
	.byte	'RST',0,1
	.word	633
	.byte	1,7,2,35,0,18
	.byte	'reserved_1',0,4
	.word	548
	.byte	31,0,2,35,0,0,12
	.byte	'Ifx_STM_KRST1_Bits',0,6,172,1,3
	.word	2736
	.byte	17
	.byte	'_Ifx_STM_KRSTCLR_Bits',0,6,175,1,16,4,18
	.byte	'CLR',0,1
	.word	633
	.byte	1,7,2,35,0,18
	.byte	'reserved_1',0,4
	.word	548
	.byte	31,0,2,35,0,0,12
	.byte	'Ifx_STM_KRSTCLR_Bits',0,6,179,1,3
	.word	2828
	.byte	17
	.byte	'_Ifx_STM_OCS_Bits',0,6,182,1,16,4,18
	.byte	'reserved_0',0,4
	.word	548
	.byte	24,8,2,35,0,18
	.byte	'SUS',0,1
	.word	633
	.byte	4,4,2,35,3,18
	.byte	'SUS_P',0,1
	.word	633
	.byte	1,3,2,35,3,18
	.byte	'SUSSTA',0,1
	.word	633
	.byte	1,2,2,35,3,18
	.byte	'reserved_30',0,1
	.word	633
	.byte	2,0,2,35,3,0,12
	.byte	'Ifx_STM_OCS_Bits',0,6,189,1,3
	.word	2924
	.byte	17
	.byte	'_Ifx_STM_TIM0_Bits',0,6,192,1,16,4,18
	.byte	'STM31_0',0,4
	.word	548
	.byte	32,0,2,35,0,0,12
	.byte	'Ifx_STM_TIM0_Bits',0,6,195,1,3
	.word	3070
	.byte	17
	.byte	'_Ifx_STM_TIM0SV_Bits',0,6,198,1,16,4,18
	.byte	'STM31_0',0,4
	.word	548
	.byte	32,0,2,35,0,0,12
	.byte	'Ifx_STM_TIM0SV_Bits',0,6,201,1,3
	.word	3142
	.byte	17
	.byte	'_Ifx_STM_TIM1_Bits',0,6,204,1,16,4,18
	.byte	'STM35_4',0,4
	.word	548
	.byte	32,0,2,35,0,0,12
	.byte	'Ifx_STM_TIM1_Bits',0,6,207,1,3
	.word	3218
	.byte	17
	.byte	'_Ifx_STM_TIM2_Bits',0,6,210,1,16,4,18
	.byte	'STM39_8',0,4
	.word	548
	.byte	32,0,2,35,0,0,12
	.byte	'Ifx_STM_TIM2_Bits',0,6,213,1,3
	.word	3290
	.byte	17
	.byte	'_Ifx_STM_TIM3_Bits',0,6,216,1,16,4,18
	.byte	'STM43_12',0,4
	.word	548
	.byte	32,0,2,35,0,0,12
	.byte	'Ifx_STM_TIM3_Bits',0,6,219,1,3
	.word	3362
	.byte	17
	.byte	'_Ifx_STM_TIM4_Bits',0,6,222,1,16,4,18
	.byte	'STM47_16',0,4
	.word	548
	.byte	32,0,2,35,0,0,12
	.byte	'Ifx_STM_TIM4_Bits',0,6,225,1,3
	.word	3435
	.byte	17
	.byte	'_Ifx_STM_TIM5_Bits',0,6,228,1,16,4,18
	.byte	'STM51_20',0,4
	.word	548
	.byte	32,0,2,35,0,0,12
	.byte	'Ifx_STM_TIM5_Bits',0,6,231,1,3
	.word	3508
	.byte	17
	.byte	'_Ifx_STM_TIM6_Bits',0,6,234,1,16,4,18
	.byte	'STM63_32',0,4
	.word	548
	.byte	32,0,2,35,0,0,12
	.byte	'Ifx_STM_TIM6_Bits',0,6,237,1,3
	.word	3581
	.byte	19,6,245,1,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	956
	.byte	4,2,35,0,0,12
	.byte	'Ifx_STM_ACCEN0',0,6,250,1,3
	.word	3654
	.byte	19,6,253,1,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	1513
	.byte	4,2,35,0,0,12
	.byte	'Ifx_STM_ACCEN1',0,6,130,2,3
	.word	3718
	.byte	19,6,133,2,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	1590
	.byte	4,2,35,0,0,12
	.byte	'Ifx_STM_CAP',0,6,138,2,3
	.word	3782
	.byte	19,6,141,2,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	1662
	.byte	4,2,35,0,0,12
	.byte	'Ifx_STM_CAPSV',0,6,146,2,3
	.word	3843
	.byte	19,6,149,2,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	1738
	.byte	4,2,35,0,0,12
	.byte	'Ifx_STM_CLC',0,6,154,2,3
	.word	3906
	.byte	19,6,157,2,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	1879
	.byte	4,2,35,0,0,12
	.byte	'Ifx_STM_CMCON',0,6,162,2,3
	.word	3967
	.byte	19,6,165,2,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	2097
	.byte	4,2,35,0,0,12
	.byte	'Ifx_STM_CMP',0,6,170,2,3
	.word	4030
	.byte	19,6,173,2,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	2164
	.byte	4,2,35,0,0,12
	.byte	'Ifx_STM_ICR',0,6,178,2,3
	.word	4091
	.byte	19,6,181,2,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	2367
	.byte	4,2,35,0,0,12
	.byte	'Ifx_STM_ID',0,6,186,2,3
	.word	4152
	.byte	19,6,189,2,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	2474
	.byte	4,2,35,0,0,12
	.byte	'Ifx_STM_ISCR',0,6,194,2,3
	.word	4212
	.byte	19,6,197,2,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	2625
	.byte	4,2,35,0,0,12
	.byte	'Ifx_STM_KRST0',0,6,202,2,3
	.word	4274
	.byte	19,6,205,2,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	2736
	.byte	4,2,35,0,0,12
	.byte	'Ifx_STM_KRST1',0,6,210,2,3
	.word	4337
	.byte	19,6,213,2,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	2828
	.byte	4,2,35,0,0,12
	.byte	'Ifx_STM_KRSTCLR',0,6,218,2,3
	.word	4400
	.byte	19,6,221,2,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	2924
	.byte	4,2,35,0,0,12
	.byte	'Ifx_STM_OCS',0,6,226,2,3
	.word	4465
	.byte	19,6,229,2,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	3070
	.byte	4,2,35,0,0,12
	.byte	'Ifx_STM_TIM0',0,6,234,2,3
	.word	4526
	.byte	19,6,237,2,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	3142
	.byte	4,2,35,0,0,12
	.byte	'Ifx_STM_TIM0SV',0,6,242,2,3
	.word	4588
	.byte	19,6,245,2,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	3218
	.byte	4,2,35,0,0,12
	.byte	'Ifx_STM_TIM1',0,6,250,2,3
	.word	4652
	.byte	19,6,253,2,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	3290
	.byte	4,2,35,0,0,12
	.byte	'Ifx_STM_TIM2',0,6,130,3,3
	.word	4714
	.byte	19,6,133,3,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	3362
	.byte	4,2,35,0,0,12
	.byte	'Ifx_STM_TIM3',0,6,138,3,3
	.word	4776
	.byte	19,6,141,3,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	3435
	.byte	4,2,35,0,0,12
	.byte	'Ifx_STM_TIM4',0,6,146,3,3
	.word	4838
	.byte	19,6,149,3,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	3508
	.byte	4,2,35,0,0,12
	.byte	'Ifx_STM_TIM5',0,6,154,3,3
	.word	4900
	.byte	19,6,157,3,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	3581
	.byte	4,2,35,0,0,12
	.byte	'Ifx_STM_TIM6',0,6,162,3,3
	.word	4962
	.byte	20,16
	.word	890
	.byte	21,1,0
.L8:
	.byte	22
	.word	5024
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L4:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,21,0,54,15,39,12,0,0,3,15,0,73,19,0,0,4,46,1,3,8,32,13
	.byte	58,15,59,15,57,15,54,15,39,12,0,0,5,5,0,3,8,58,15,59,15,57,15,73,19,0,0,6,11,0,0,0,7,36,0,3,8,11,15,62
	.byte	15,0,0,8,46,1,3,8,32,13,58,15,59,15,57,15,73,19,54,15,39,12,0,0,9,59,0,3,8,0,0,10,46,1,49,19,0,0,11,5
	.byte	0,49,19,0,0,12,22,0,3,8,58,15,59,15,57,15,73,19,0,0,13,21,0,54,15,0,0,14,53,0,73,19,0,0,15,19,1,58,15
	.byte	59,15,57,15,11,15,0,0,16,13,0,3,8,73,19,11,15,56,9,0,0,17,19,1,3,8,58,15,59,15,57,15,11,15,0,0,18,13,0
	.byte	3,8,11,15,73,19,13,15,12,15,56,9,0,0,19,23,1,58,15,59,15,57,15,11,15,0,0,20,1,1,11,15,73,19,0,0,21,33
	.byte	0,47,15,0,0,22,38,0,73,19,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L5:
	.word	.L10-.L9
.L9:
	.half	3
	.word	.L12-.L11
.L11:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg',0
	.byte	0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_Intrinsics.h',0
	.byte	0,0,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxStm_cfg.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Platform_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Ifx_Types.h',0
	.byte	0,0,0
	.byte	'IfxStm_regdef.h',0,1,0,0,0
.L12:
.L10:
	.sdecl	'.debug_info',debug,cluster('IfxStm_cfg_indexMap')
	.sect	'.debug_info'
.L6:
	.word	265
	.half	3
	.word	.L7
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxStm_cfg.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxStm_cfg_indexMap',0,3,55,30
	.word	.L8
	.byte	1,5,3
	.word	IfxStm_cfg_indexMap
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxStm_cfg_indexMap')
	.sect	'.debug_abbrev'
.L7:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	; Module end
