################################################################################
# Automatically-generated file. Do not edit!
################################################################################

# Add inputs and outputs from these tool invocations to the build variables 
C_SRCS += \
../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxAsclin_cfg.c \
../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxCcu6_cfg.c \
../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxCif_cfg.c \
../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxCpu_cfg.c \
../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxDma_cfg.c \
../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxEmem_cfg.c \
../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxEray_cfg.c \
../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxFlash_cfg.c \
../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxGtm_cfg.c \
../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxHssl_cfg.c \
../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxI2c_cfg.c \
../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxMsc_cfg.c \
../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxMtu_cfg.c \
../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxMultican_cfg.c \
../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxPort_cfg.c \
../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxPsi5_cfg.c \
../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxQspi_cfg.c \
../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxScu_cfg.c \
../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxSent_cfg.c \
../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxSmu_cfg.c \
../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxSrc_cfg.c \
../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxStm_cfg.c \
../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxVadc_cfg.c 

COMPILED_SRCS += \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxAsclin_cfg.src \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxCcu6_cfg.src \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxCif_cfg.src \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxCpu_cfg.src \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxDma_cfg.src \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxEmem_cfg.src \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxEray_cfg.src \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxFlash_cfg.src \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxGtm_cfg.src \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxHssl_cfg.src \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxI2c_cfg.src \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxMsc_cfg.src \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxMtu_cfg.src \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxMultican_cfg.src \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxPort_cfg.src \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxPsi5_cfg.src \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxQspi_cfg.src \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxScu_cfg.src \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxSent_cfg.src \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxSmu_cfg.src \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxSrc_cfg.src \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxStm_cfg.src \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxVadc_cfg.src 

C_DEPS += \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxAsclin_cfg.d \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxCcu6_cfg.d \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxCif_cfg.d \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxCpu_cfg.d \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxDma_cfg.d \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxEmem_cfg.d \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxEray_cfg.d \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxFlash_cfg.d \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxGtm_cfg.d \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxHssl_cfg.d \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxI2c_cfg.d \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxMsc_cfg.d \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxMtu_cfg.d \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxMultican_cfg.d \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxPort_cfg.d \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxPsi5_cfg.d \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxQspi_cfg.d \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxScu_cfg.d \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxSent_cfg.d \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxSmu_cfg.d \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxSrc_cfg.d \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxStm_cfg.d \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxVadc_cfg.d 

OBJS += \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxAsclin_cfg.o \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxCcu6_cfg.o \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxCif_cfg.o \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxCpu_cfg.o \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxDma_cfg.o \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxEmem_cfg.o \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxEray_cfg.o \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxFlash_cfg.o \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxGtm_cfg.o \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxHssl_cfg.o \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxI2c_cfg.o \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxMsc_cfg.o \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxMtu_cfg.o \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxMultican_cfg.o \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxPort_cfg.o \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxPsi5_cfg.o \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxQspi_cfg.o \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxScu_cfg.o \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxSent_cfg.o \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxSmu_cfg.o \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxSrc_cfg.o \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxStm_cfg.o \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxVadc_cfg.o 


# Each subdirectory must supply rules for building sources it contributes
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxAsclin_cfg.src: ../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxAsclin_cfg.c libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/subdir.mk
	cctc -cs --dep-file="$(*F).d" --misrac-version=2004 -D__CPU__=tc26xb "-fC:/Users/<USER>/Desktop/ORRN_Code_4G - 鍓湰/ORRN_Code/Mark_1/Debug/TASKING_C_C___Compiler-Include_paths__-I_.opt" --iso=99 --c++14 --language=+volatile --exceptions --anachronisms --fp-model=2 -O0 --tradeoff=4 --compact-max-size=200 -g -Wc-w544 -Wc-w557 -Ctc26xb -Y0 -N0 -Z0 -o "$@" "$<"
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxAsclin_cfg.o: libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxAsclin_cfg.src libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/subdir.mk
	astc -Og -Os --no-warnings= --error-limit=42 -o  "$@" "$<"
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxCcu6_cfg.src: ../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxCcu6_cfg.c libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/subdir.mk
	cctc -cs --dep-file="$(*F).d" --misrac-version=2004 -D__CPU__=tc26xb "-fC:/Users/<USER>/Desktop/ORRN_Code_4G - 鍓湰/ORRN_Code/Mark_1/Debug/TASKING_C_C___Compiler-Include_paths__-I_.opt" --iso=99 --c++14 --language=+volatile --exceptions --anachronisms --fp-model=2 -O0 --tradeoff=4 --compact-max-size=200 -g -Wc-w544 -Wc-w557 -Ctc26xb -Y0 -N0 -Z0 -o "$@" "$<"
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxCcu6_cfg.o: libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxCcu6_cfg.src libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/subdir.mk
	astc -Og -Os --no-warnings= --error-limit=42 -o  "$@" "$<"
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxCif_cfg.src: ../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxCif_cfg.c libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/subdir.mk
	cctc -cs --dep-file="$(*F).d" --misrac-version=2004 -D__CPU__=tc26xb "-fC:/Users/<USER>/Desktop/ORRN_Code_4G - 鍓湰/ORRN_Code/Mark_1/Debug/TASKING_C_C___Compiler-Include_paths__-I_.opt" --iso=99 --c++14 --language=+volatile --exceptions --anachronisms --fp-model=2 -O0 --tradeoff=4 --compact-max-size=200 -g -Wc-w544 -Wc-w557 -Ctc26xb -Y0 -N0 -Z0 -o "$@" "$<"
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxCif_cfg.o: libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxCif_cfg.src libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/subdir.mk
	astc -Og -Os --no-warnings= --error-limit=42 -o  "$@" "$<"
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxCpu_cfg.src: ../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxCpu_cfg.c libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/subdir.mk
	cctc -cs --dep-file="$(*F).d" --misrac-version=2004 -D__CPU__=tc26xb "-fC:/Users/<USER>/Desktop/ORRN_Code_4G - 鍓湰/ORRN_Code/Mark_1/Debug/TASKING_C_C___Compiler-Include_paths__-I_.opt" --iso=99 --c++14 --language=+volatile --exceptions --anachronisms --fp-model=2 -O0 --tradeoff=4 --compact-max-size=200 -g -Wc-w544 -Wc-w557 -Ctc26xb -Y0 -N0 -Z0 -o "$@" "$<"
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxCpu_cfg.o: libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxCpu_cfg.src libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/subdir.mk
	astc -Og -Os --no-warnings= --error-limit=42 -o  "$@" "$<"
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxDma_cfg.src: ../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxDma_cfg.c libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/subdir.mk
	cctc -cs --dep-file="$(*F).d" --misrac-version=2004 -D__CPU__=tc26xb "-fC:/Users/<USER>/Desktop/ORRN_Code_4G - 鍓湰/ORRN_Code/Mark_1/Debug/TASKING_C_C___Compiler-Include_paths__-I_.opt" --iso=99 --c++14 --language=+volatile --exceptions --anachronisms --fp-model=2 -O0 --tradeoff=4 --compact-max-size=200 -g -Wc-w544 -Wc-w557 -Ctc26xb -Y0 -N0 -Z0 -o "$@" "$<"
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxDma_cfg.o: libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxDma_cfg.src libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/subdir.mk
	astc -Og -Os --no-warnings= --error-limit=42 -o  "$@" "$<"
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxEmem_cfg.src: ../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxEmem_cfg.c libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/subdir.mk
	cctc -cs --dep-file="$(*F).d" --misrac-version=2004 -D__CPU__=tc26xb "-fC:/Users/<USER>/Desktop/ORRN_Code_4G - 鍓湰/ORRN_Code/Mark_1/Debug/TASKING_C_C___Compiler-Include_paths__-I_.opt" --iso=99 --c++14 --language=+volatile --exceptions --anachronisms --fp-model=2 -O0 --tradeoff=4 --compact-max-size=200 -g -Wc-w544 -Wc-w557 -Ctc26xb -Y0 -N0 -Z0 -o "$@" "$<"
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxEmem_cfg.o: libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxEmem_cfg.src libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/subdir.mk
	astc -Og -Os --no-warnings= --error-limit=42 -o  "$@" "$<"
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxEray_cfg.src: ../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxEray_cfg.c libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/subdir.mk
	cctc -cs --dep-file="$(*F).d" --misrac-version=2004 -D__CPU__=tc26xb "-fC:/Users/<USER>/Desktop/ORRN_Code_4G - 鍓湰/ORRN_Code/Mark_1/Debug/TASKING_C_C___Compiler-Include_paths__-I_.opt" --iso=99 --c++14 --language=+volatile --exceptions --anachronisms --fp-model=2 -O0 --tradeoff=4 --compact-max-size=200 -g -Wc-w544 -Wc-w557 -Ctc26xb -Y0 -N0 -Z0 -o "$@" "$<"
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxEray_cfg.o: libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxEray_cfg.src libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/subdir.mk
	astc -Og -Os --no-warnings= --error-limit=42 -o  "$@" "$<"
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxFlash_cfg.src: ../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxFlash_cfg.c libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/subdir.mk
	cctc -cs --dep-file="$(*F).d" --misrac-version=2004 -D__CPU__=tc26xb "-fC:/Users/<USER>/Desktop/ORRN_Code_4G - 鍓湰/ORRN_Code/Mark_1/Debug/TASKING_C_C___Compiler-Include_paths__-I_.opt" --iso=99 --c++14 --language=+volatile --exceptions --anachronisms --fp-model=2 -O0 --tradeoff=4 --compact-max-size=200 -g -Wc-w544 -Wc-w557 -Ctc26xb -Y0 -N0 -Z0 -o "$@" "$<"
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxFlash_cfg.o: libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxFlash_cfg.src libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/subdir.mk
	astc -Og -Os --no-warnings= --error-limit=42 -o  "$@" "$<"
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxGtm_cfg.src: ../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxGtm_cfg.c libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/subdir.mk
	cctc -cs --dep-file="$(*F).d" --misrac-version=2004 -D__CPU__=tc26xb "-fC:/Users/<USER>/Desktop/ORRN_Code_4G - 鍓湰/ORRN_Code/Mark_1/Debug/TASKING_C_C___Compiler-Include_paths__-I_.opt" --iso=99 --c++14 --language=+volatile --exceptions --anachronisms --fp-model=2 -O0 --tradeoff=4 --compact-max-size=200 -g -Wc-w544 -Wc-w557 -Ctc26xb -Y0 -N0 -Z0 -o "$@" "$<"
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxGtm_cfg.o: libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxGtm_cfg.src libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/subdir.mk
	astc -Og -Os --no-warnings= --error-limit=42 -o  "$@" "$<"
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxHssl_cfg.src: ../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxHssl_cfg.c libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/subdir.mk
	cctc -cs --dep-file="$(*F).d" --misrac-version=2004 -D__CPU__=tc26xb "-fC:/Users/<USER>/Desktop/ORRN_Code_4G - 鍓湰/ORRN_Code/Mark_1/Debug/TASKING_C_C___Compiler-Include_paths__-I_.opt" --iso=99 --c++14 --language=+volatile --exceptions --anachronisms --fp-model=2 -O0 --tradeoff=4 --compact-max-size=200 -g -Wc-w544 -Wc-w557 -Ctc26xb -Y0 -N0 -Z0 -o "$@" "$<"
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxHssl_cfg.o: libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxHssl_cfg.src libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/subdir.mk
	astc -Og -Os --no-warnings= --error-limit=42 -o  "$@" "$<"
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxI2c_cfg.src: ../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxI2c_cfg.c libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/subdir.mk
	cctc -cs --dep-file="$(*F).d" --misrac-version=2004 -D__CPU__=tc26xb "-fC:/Users/<USER>/Desktop/ORRN_Code_4G - 鍓湰/ORRN_Code/Mark_1/Debug/TASKING_C_C___Compiler-Include_paths__-I_.opt" --iso=99 --c++14 --language=+volatile --exceptions --anachronisms --fp-model=2 -O0 --tradeoff=4 --compact-max-size=200 -g -Wc-w544 -Wc-w557 -Ctc26xb -Y0 -N0 -Z0 -o "$@" "$<"
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxI2c_cfg.o: libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxI2c_cfg.src libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/subdir.mk
	astc -Og -Os --no-warnings= --error-limit=42 -o  "$@" "$<"
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxMsc_cfg.src: ../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxMsc_cfg.c libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/subdir.mk
	cctc -cs --dep-file="$(*F).d" --misrac-version=2004 -D__CPU__=tc26xb "-fC:/Users/<USER>/Desktop/ORRN_Code_4G - 鍓湰/ORRN_Code/Mark_1/Debug/TASKING_C_C___Compiler-Include_paths__-I_.opt" --iso=99 --c++14 --language=+volatile --exceptions --anachronisms --fp-model=2 -O0 --tradeoff=4 --compact-max-size=200 -g -Wc-w544 -Wc-w557 -Ctc26xb -Y0 -N0 -Z0 -o "$@" "$<"
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxMsc_cfg.o: libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxMsc_cfg.src libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/subdir.mk
	astc -Og -Os --no-warnings= --error-limit=42 -o  "$@" "$<"
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxMtu_cfg.src: ../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxMtu_cfg.c libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/subdir.mk
	cctc -cs --dep-file="$(*F).d" --misrac-version=2004 -D__CPU__=tc26xb "-fC:/Users/<USER>/Desktop/ORRN_Code_4G - 鍓湰/ORRN_Code/Mark_1/Debug/TASKING_C_C___Compiler-Include_paths__-I_.opt" --iso=99 --c++14 --language=+volatile --exceptions --anachronisms --fp-model=2 -O0 --tradeoff=4 --compact-max-size=200 -g -Wc-w544 -Wc-w557 -Ctc26xb -Y0 -N0 -Z0 -o "$@" "$<"
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxMtu_cfg.o: libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxMtu_cfg.src libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/subdir.mk
	astc -Og -Os --no-warnings= --error-limit=42 -o  "$@" "$<"
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxMultican_cfg.src: ../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxMultican_cfg.c libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/subdir.mk
	cctc -cs --dep-file="$(*F).d" --misrac-version=2004 -D__CPU__=tc26xb "-fC:/Users/<USER>/Desktop/ORRN_Code_4G - 鍓湰/ORRN_Code/Mark_1/Debug/TASKING_C_C___Compiler-Include_paths__-I_.opt" --iso=99 --c++14 --language=+volatile --exceptions --anachronisms --fp-model=2 -O0 --tradeoff=4 --compact-max-size=200 -g -Wc-w544 -Wc-w557 -Ctc26xb -Y0 -N0 -Z0 -o "$@" "$<"
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxMultican_cfg.o: libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxMultican_cfg.src libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/subdir.mk
	astc -Og -Os --no-warnings= --error-limit=42 -o  "$@" "$<"
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxPort_cfg.src: ../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxPort_cfg.c libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/subdir.mk
	cctc -cs --dep-file="$(*F).d" --misrac-version=2004 -D__CPU__=tc26xb "-fC:/Users/<USER>/Desktop/ORRN_Code_4G - 鍓湰/ORRN_Code/Mark_1/Debug/TASKING_C_C___Compiler-Include_paths__-I_.opt" --iso=99 --c++14 --language=+volatile --exceptions --anachronisms --fp-model=2 -O0 --tradeoff=4 --compact-max-size=200 -g -Wc-w544 -Wc-w557 -Ctc26xb -Y0 -N0 -Z0 -o "$@" "$<"
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxPort_cfg.o: libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxPort_cfg.src libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/subdir.mk
	astc -Og -Os --no-warnings= --error-limit=42 -o  "$@" "$<"
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxPsi5_cfg.src: ../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxPsi5_cfg.c libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/subdir.mk
	cctc -cs --dep-file="$(*F).d" --misrac-version=2004 -D__CPU__=tc26xb "-fC:/Users/<USER>/Desktop/ORRN_Code_4G - 鍓湰/ORRN_Code/Mark_1/Debug/TASKING_C_C___Compiler-Include_paths__-I_.opt" --iso=99 --c++14 --language=+volatile --exceptions --anachronisms --fp-model=2 -O0 --tradeoff=4 --compact-max-size=200 -g -Wc-w544 -Wc-w557 -Ctc26xb -Y0 -N0 -Z0 -o "$@" "$<"
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxPsi5_cfg.o: libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxPsi5_cfg.src libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/subdir.mk
	astc -Og -Os --no-warnings= --error-limit=42 -o  "$@" "$<"
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxQspi_cfg.src: ../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxQspi_cfg.c libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/subdir.mk
	cctc -cs --dep-file="$(*F).d" --misrac-version=2004 -D__CPU__=tc26xb "-fC:/Users/<USER>/Desktop/ORRN_Code_4G - 鍓湰/ORRN_Code/Mark_1/Debug/TASKING_C_C___Compiler-Include_paths__-I_.opt" --iso=99 --c++14 --language=+volatile --exceptions --anachronisms --fp-model=2 -O0 --tradeoff=4 --compact-max-size=200 -g -Wc-w544 -Wc-w557 -Ctc26xb -Y0 -N0 -Z0 -o "$@" "$<"
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxQspi_cfg.o: libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxQspi_cfg.src libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/subdir.mk
	astc -Og -Os --no-warnings= --error-limit=42 -o  "$@" "$<"
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxScu_cfg.src: ../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxScu_cfg.c libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/subdir.mk
	cctc -cs --dep-file="$(*F).d" --misrac-version=2004 -D__CPU__=tc26xb "-fC:/Users/<USER>/Desktop/ORRN_Code_4G - 鍓湰/ORRN_Code/Mark_1/Debug/TASKING_C_C___Compiler-Include_paths__-I_.opt" --iso=99 --c++14 --language=+volatile --exceptions --anachronisms --fp-model=2 -O0 --tradeoff=4 --compact-max-size=200 -g -Wc-w544 -Wc-w557 -Ctc26xb -Y0 -N0 -Z0 -o "$@" "$<"
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxScu_cfg.o: libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxScu_cfg.src libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/subdir.mk
	astc -Og -Os --no-warnings= --error-limit=42 -o  "$@" "$<"
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxSent_cfg.src: ../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxSent_cfg.c libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/subdir.mk
	cctc -cs --dep-file="$(*F).d" --misrac-version=2004 -D__CPU__=tc26xb "-fC:/Users/<USER>/Desktop/ORRN_Code_4G - 鍓湰/ORRN_Code/Mark_1/Debug/TASKING_C_C___Compiler-Include_paths__-I_.opt" --iso=99 --c++14 --language=+volatile --exceptions --anachronisms --fp-model=2 -O0 --tradeoff=4 --compact-max-size=200 -g -Wc-w544 -Wc-w557 -Ctc26xb -Y0 -N0 -Z0 -o "$@" "$<"
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxSent_cfg.o: libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxSent_cfg.src libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/subdir.mk
	astc -Og -Os --no-warnings= --error-limit=42 -o  "$@" "$<"
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxSmu_cfg.src: ../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxSmu_cfg.c libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/subdir.mk
	cctc -cs --dep-file="$(*F).d" --misrac-version=2004 -D__CPU__=tc26xb "-fC:/Users/<USER>/Desktop/ORRN_Code_4G - 鍓湰/ORRN_Code/Mark_1/Debug/TASKING_C_C___Compiler-Include_paths__-I_.opt" --iso=99 --c++14 --language=+volatile --exceptions --anachronisms --fp-model=2 -O0 --tradeoff=4 --compact-max-size=200 -g -Wc-w544 -Wc-w557 -Ctc26xb -Y0 -N0 -Z0 -o "$@" "$<"
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxSmu_cfg.o: libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxSmu_cfg.src libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/subdir.mk
	astc -Og -Os --no-warnings= --error-limit=42 -o  "$@" "$<"
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxSrc_cfg.src: ../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxSrc_cfg.c libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/subdir.mk
	cctc -cs --dep-file="$(*F).d" --misrac-version=2004 -D__CPU__=tc26xb "-fC:/Users/<USER>/Desktop/ORRN_Code_4G - 鍓湰/ORRN_Code/Mark_1/Debug/TASKING_C_C___Compiler-Include_paths__-I_.opt" --iso=99 --c++14 --language=+volatile --exceptions --anachronisms --fp-model=2 -O0 --tradeoff=4 --compact-max-size=200 -g -Wc-w544 -Wc-w557 -Ctc26xb -Y0 -N0 -Z0 -o "$@" "$<"
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxSrc_cfg.o: libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxSrc_cfg.src libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/subdir.mk
	astc -Og -Os --no-warnings= --error-limit=42 -o  "$@" "$<"
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxStm_cfg.src: ../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxStm_cfg.c libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/subdir.mk
	cctc -cs --dep-file="$(*F).d" --misrac-version=2004 -D__CPU__=tc26xb "-fC:/Users/<USER>/Desktop/ORRN_Code_4G - 鍓湰/ORRN_Code/Mark_1/Debug/TASKING_C_C___Compiler-Include_paths__-I_.opt" --iso=99 --c++14 --language=+volatile --exceptions --anachronisms --fp-model=2 -O0 --tradeoff=4 --compact-max-size=200 -g -Wc-w544 -Wc-w557 -Ctc26xb -Y0 -N0 -Z0 -o "$@" "$<"
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxStm_cfg.o: libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxStm_cfg.src libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/subdir.mk
	astc -Og -Os --no-warnings= --error-limit=42 -o  "$@" "$<"
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxVadc_cfg.src: ../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxVadc_cfg.c libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/subdir.mk
	cctc -cs --dep-file="$(*F).d" --misrac-version=2004 -D__CPU__=tc26xb "-fC:/Users/<USER>/Desktop/ORRN_Code_4G - 鍓湰/ORRN_Code/Mark_1/Debug/TASKING_C_C___Compiler-Include_paths__-I_.opt" --iso=99 --c++14 --language=+volatile --exceptions --anachronisms --fp-model=2 -O0 --tradeoff=4 --compact-max-size=200 -g -Wc-w544 -Wc-w557 -Ctc26xb -Y0 -N0 -Z0 -o "$@" "$<"
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxVadc_cfg.o: libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxVadc_cfg.src libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/subdir.mk
	astc -Og -Os --no-warnings= --error-limit=42 -o  "$@" "$<"

clean: clean-libraries-2f-infineon_libraries-2f-iLLD-2f-TC26B-2f-Tricore-2f-_Impl

clean-libraries-2f-infineon_libraries-2f-iLLD-2f-TC26B-2f-Tricore-2f-_Impl:
	-$(RM) libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxAsclin_cfg.d libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxAsclin_cfg.o libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxAsclin_cfg.src libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxCcu6_cfg.d libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxCcu6_cfg.o libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxCcu6_cfg.src libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxCif_cfg.d libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxCif_cfg.o libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxCif_cfg.src libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxCpu_cfg.d libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxCpu_cfg.o libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxCpu_cfg.src libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxDma_cfg.d libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxDma_cfg.o libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxDma_cfg.src libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxEmem_cfg.d libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxEmem_cfg.o libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxEmem_cfg.src libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxEray_cfg.d libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxEray_cfg.o libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxEray_cfg.src libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxFlash_cfg.d libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxFlash_cfg.o libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxFlash_cfg.src libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxGtm_cfg.d libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxGtm_cfg.o libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxGtm_cfg.src libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxHssl_cfg.d libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxHssl_cfg.o libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxHssl_cfg.src libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxI2c_cfg.d libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxI2c_cfg.o libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxI2c_cfg.src libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxMsc_cfg.d libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxMsc_cfg.o libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxMsc_cfg.src libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxMtu_cfg.d libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxMtu_cfg.o libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxMtu_cfg.src libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxMultican_cfg.d libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxMultican_cfg.o libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxMultican_cfg.src libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxPort_cfg.d libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxPort_cfg.o libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxPort_cfg.src libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxPsi5_cfg.d libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxPsi5_cfg.o libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxPsi5_cfg.src libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxQspi_cfg.d libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxQspi_cfg.o libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxQspi_cfg.src libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxScu_cfg.d libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxScu_cfg.o libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxScu_cfg.src libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxSent_cfg.d libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxSent_cfg.o libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxSent_cfg.src libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxSmu_cfg.d libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxSmu_cfg.o libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxSmu_cfg.src libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxSrc_cfg.d libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxSrc_cfg.o libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxSrc_cfg.src libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxStm_cfg.d libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxStm_cfg.o libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxStm_cfg.src libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxVadc_cfg.d libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxVadc_cfg.o libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxVadc_cfg.src

.PHONY: clean-libraries-2f-infineon_libraries-2f-iLLD-2f-TC26B-2f-Tricore-2f-_Impl

