/**
 * \file IfxCcu6_cfg.h
 * \brief CCU6 on-chip implementation data
 * \ingroup IfxLld_Ccu6
 *
 * \version iLLD_1_0_1_11_0
 * \copyright Copyright (c) 2018 Infineon Technologies AG. All rights reserved.
 *
 *
 *                                 IMPORTANT NOTICE
 *
 *
 * Use of this file is subject to the terms of use agreed between (i) you or 
 * the company in which ordinary course of business you are acting and (ii) 
 * Infineon Technologies AG or its licensees. If and as long as no such 
 * terms of use are agreed, use of this file is subject to following:


 * Boost Software License - Version 1.0 - August 17th, 2003

 * Permission is hereby granted, free of charge, to any person or 
 * organization obtaining a copy of the software and accompanying 
 * documentation covered by this license (the "Software") to use, reproduce,
 * display, distribute, execute, and transmit the Software, and to prepare
 * derivative works of the Software, and to permit third-parties to whom the 
 * Software is furnished to do so, all subject to the following:

 * The copyright notices in the Software and this entire statement, including
 * the above license grant, this restriction and the following disclaimer, must
 * be included in all copies of the Software, in whole or in part, and all
 * derivative works of the Software, unless such copies or derivative works are
 * solely in the form of machine-executable object code generated by a source
 * language processor.

 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR 
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE, TITLE AND NON-INFRINGEMENT. IN NO EVENT
 * SHALL THE COPYRIGHT HOLDERS OR ANYONE DISTRIBUTING THE SOFTWARE BE LIABLE 
 * FOR ANY DAMAGES OR OTHER LIABILITY, WHETHER IN CONTRACT, TORT OR OTHERWISE,
 * ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
 * DEALINGS IN THE SOFTWARE.

 *
 * \defgroup IfxLld_Ccu6 CCU6
 * \ingroup IfxLld
 * \defgroup IfxLld_Ccu6_Impl Implementation
 * \ingroup IfxLld_Ccu6
 * \defgroup IfxLld_Ccu6_Std Standard Driver
 * \ingroup IfxLld_Ccu6
 * \defgroup IfxLld_Ccu6_Impl_Enumerations Enumerations
 * \ingroup IfxLld_Ccu6_Impl
 */

#ifndef IFXCCU6_CFG_H
#define IFXCCU6_CFG_H 1

/******************************************************************************/
/*----------------------------------Includes----------------------------------*/
/******************************************************************************/

#include "Cpu/Std/Ifx_Types.h"
#include "IfxCcu6_reg.h"
#include "IfxCcu6_bf.h"

/******************************************************************************/
/*-----------------------------------Macros-----------------------------------*/
/******************************************************************************/

#define IFXCCU6_NUM_MODULES          (2)

#define IFXCCU6_NUM_SERVICE_REQUESTS (4)

#define IFXCCU6_NUM_T12_CHANNELS     (3)

#define IFXCCU6_NUM_T13_CHANNELS     (1)

/******************************************************************************/
/*-------------------------------Enumerations---------------------------------*/
/******************************************************************************/

/** \brief List of the available CCU6 resources
 */
typedef enum
{
    IfxCcu6_Index_none = -1,  /**< \brief Not Selected */
    IfxCcu6_Index_0    = 0,   /**< \brief CCU6 index 0  */
    IfxCcu6_Index_1           /**< \brief CCU6 index 1  */
} IfxCcu6_Index;

typedef enum
{
    IfxCcu6_TrigOut_0 = IFX_CCU6_MOSEL_TRIG0SEL_OFF,  /**< \brief Output Trigger Select for CCU6061 TRIG0 */
    IfxCcu6_TrigOut_1 = IFX_CCU6_MOSEL_TRIG1SEL_OFF,  /**< \brief Output Trigger Select for CCU6061 TRIG1 */
    IfxCcu6_TrigOut_2 = IFX_CCU6_MOSEL_TRIG2SEL_OFF   /**< \brief Output Trigger Select for CCU6061 TRIG2 */
} IfxCcu6_TrigOut;

typedef enum
{
    IfxCcu6_TrigSel_cout63 = 0,
    IfxCcu6_TrigSel_cc60   = 1,
    IfxCcu6_TrigSel_cc61   = 1,
    IfxCcu6_TrigSel_cc62   = 1,
    IfxCcu6_TrigSel_sr1    = 2,
    IfxCcu6_TrigSel_sr3    = 3
} IfxCcu6_TrigSel;

/******************************************************************************/
/*-------------------Global Exported Variables/Constants----------------------*/
/******************************************************************************/

IFX_EXTERN IFX_CONST IfxModule_IndexMap IfxCcu6_cfg_indexMap[IFXCCU6_NUM_MODULES];

#endif /* IFXCCU6_CFG_H */
