/**
 * \file IfxEbcu_bf.h
 * \brief
 * \copyright Copyright (c) 2014 Infineon Technologies AG. All rights reserved.
 *
 * Version: TC2XXED_TS_V1.0.R2
 * Specification: AurixED_TS_V1.0_CPU_VIEW_SFR.xml (Revision: V1.0)
 * MAY BE CHANGED BY USER [yes/no]: No
 *
 *                                 IMPORTANT NOTICE
 *
 * Infineon Technologies AG (Infineon) is supplying this file for use
 * exclusively with Infineon's microcontroller products. This file can be freely
 * distributed within development tools that are supporting such microcontroller
 * products.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS".  NO WARRANTIES, WHETHER EXPRESS, IMPLIED
 * OR STATUTORY, INCLUDING, BUT NOT LIMITED TO, IMPLIED WARRANTIES OF
 * MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE APPLY TO THIS SOFTWARE.
 * INFINEON SHALL NOT, IN ANY CIRCUMSTANCES, BE LIABLE FOR SPECIAL, INCIDENTAL,
 * OR CONSEQUENTIAL DAMAGES, FOR ANY REASON WHATSOEVER.
 *
 * \defgroup IfxLld_Ebcu_BitfieldsMask Bitfields mask and offset
 * \ingroup IfxLld_Ebcu
 * 
 */
#ifndef IFXEBCU_BF_H
#define IFXEBCU_BF_H 1
/******************************************************************************/
/******************************************************************************/
/** \addtogroup IfxLld_Ebcu_BitfieldsMask
 * \{  */

/** \brief  Length for Ifx_EBCU_ACCEN0_Bits.EN0 */
#define IFX_EBCU_ACCEN0_EN0_LEN (1u)

/** \brief  Mask for Ifx_EBCU_ACCEN0_Bits.EN0 */
#define IFX_EBCU_ACCEN0_EN0_MSK (0x1u)

/** \brief  Offset for Ifx_EBCU_ACCEN0_Bits.EN0 */
#define IFX_EBCU_ACCEN0_EN0_OFF (0u)

/** \brief  Length for Ifx_EBCU_ACCEN0_Bits.EN10 */
#define IFX_EBCU_ACCEN0_EN10_LEN (1u)

/** \brief  Mask for Ifx_EBCU_ACCEN0_Bits.EN10 */
#define IFX_EBCU_ACCEN0_EN10_MSK (0x1u)

/** \brief  Offset for Ifx_EBCU_ACCEN0_Bits.EN10 */
#define IFX_EBCU_ACCEN0_EN10_OFF (10u)

/** \brief  Length for Ifx_EBCU_ACCEN0_Bits.EN11 */
#define IFX_EBCU_ACCEN0_EN11_LEN (1u)

/** \brief  Mask for Ifx_EBCU_ACCEN0_Bits.EN11 */
#define IFX_EBCU_ACCEN0_EN11_MSK (0x1u)

/** \brief  Offset for Ifx_EBCU_ACCEN0_Bits.EN11 */
#define IFX_EBCU_ACCEN0_EN11_OFF (11u)

/** \brief  Length for Ifx_EBCU_ACCEN0_Bits.EN12 */
#define IFX_EBCU_ACCEN0_EN12_LEN (1u)

/** \brief  Mask for Ifx_EBCU_ACCEN0_Bits.EN12 */
#define IFX_EBCU_ACCEN0_EN12_MSK (0x1u)

/** \brief  Offset for Ifx_EBCU_ACCEN0_Bits.EN12 */
#define IFX_EBCU_ACCEN0_EN12_OFF (12u)

/** \brief  Length for Ifx_EBCU_ACCEN0_Bits.EN13 */
#define IFX_EBCU_ACCEN0_EN13_LEN (1u)

/** \brief  Mask for Ifx_EBCU_ACCEN0_Bits.EN13 */
#define IFX_EBCU_ACCEN0_EN13_MSK (0x1u)

/** \brief  Offset for Ifx_EBCU_ACCEN0_Bits.EN13 */
#define IFX_EBCU_ACCEN0_EN13_OFF (13u)

/** \brief  Length for Ifx_EBCU_ACCEN0_Bits.EN14 */
#define IFX_EBCU_ACCEN0_EN14_LEN (1u)

/** \brief  Mask for Ifx_EBCU_ACCEN0_Bits.EN14 */
#define IFX_EBCU_ACCEN0_EN14_MSK (0x1u)

/** \brief  Offset for Ifx_EBCU_ACCEN0_Bits.EN14 */
#define IFX_EBCU_ACCEN0_EN14_OFF (14u)

/** \brief  Length for Ifx_EBCU_ACCEN0_Bits.EN15 */
#define IFX_EBCU_ACCEN0_EN15_LEN (1u)

/** \brief  Mask for Ifx_EBCU_ACCEN0_Bits.EN15 */
#define IFX_EBCU_ACCEN0_EN15_MSK (0x1u)

/** \brief  Offset for Ifx_EBCU_ACCEN0_Bits.EN15 */
#define IFX_EBCU_ACCEN0_EN15_OFF (15u)

/** \brief  Length for Ifx_EBCU_ACCEN0_Bits.EN16 */
#define IFX_EBCU_ACCEN0_EN16_LEN (1u)

/** \brief  Mask for Ifx_EBCU_ACCEN0_Bits.EN16 */
#define IFX_EBCU_ACCEN0_EN16_MSK (0x1u)

/** \brief  Offset for Ifx_EBCU_ACCEN0_Bits.EN16 */
#define IFX_EBCU_ACCEN0_EN16_OFF (16u)

/** \brief  Length for Ifx_EBCU_ACCEN0_Bits.EN17 */
#define IFX_EBCU_ACCEN0_EN17_LEN (1u)

/** \brief  Mask for Ifx_EBCU_ACCEN0_Bits.EN17 */
#define IFX_EBCU_ACCEN0_EN17_MSK (0x1u)

/** \brief  Offset for Ifx_EBCU_ACCEN0_Bits.EN17 */
#define IFX_EBCU_ACCEN0_EN17_OFF (17u)

/** \brief  Length for Ifx_EBCU_ACCEN0_Bits.EN18 */
#define IFX_EBCU_ACCEN0_EN18_LEN (1u)

/** \brief  Mask for Ifx_EBCU_ACCEN0_Bits.EN18 */
#define IFX_EBCU_ACCEN0_EN18_MSK (0x1u)

/** \brief  Offset for Ifx_EBCU_ACCEN0_Bits.EN18 */
#define IFX_EBCU_ACCEN0_EN18_OFF (18u)

/** \brief  Length for Ifx_EBCU_ACCEN0_Bits.EN19 */
#define IFX_EBCU_ACCEN0_EN19_LEN (1u)

/** \brief  Mask for Ifx_EBCU_ACCEN0_Bits.EN19 */
#define IFX_EBCU_ACCEN0_EN19_MSK (0x1u)

/** \brief  Offset for Ifx_EBCU_ACCEN0_Bits.EN19 */
#define IFX_EBCU_ACCEN0_EN19_OFF (19u)

/** \brief  Length for Ifx_EBCU_ACCEN0_Bits.EN1 */
#define IFX_EBCU_ACCEN0_EN1_LEN (1u)

/** \brief  Mask for Ifx_EBCU_ACCEN0_Bits.EN1 */
#define IFX_EBCU_ACCEN0_EN1_MSK (0x1u)

/** \brief  Offset for Ifx_EBCU_ACCEN0_Bits.EN1 */
#define IFX_EBCU_ACCEN0_EN1_OFF (1u)

/** \brief  Length for Ifx_EBCU_ACCEN0_Bits.EN20 */
#define IFX_EBCU_ACCEN0_EN20_LEN (1u)

/** \brief  Mask for Ifx_EBCU_ACCEN0_Bits.EN20 */
#define IFX_EBCU_ACCEN0_EN20_MSK (0x1u)

/** \brief  Offset for Ifx_EBCU_ACCEN0_Bits.EN20 */
#define IFX_EBCU_ACCEN0_EN20_OFF (20u)

/** \brief  Length for Ifx_EBCU_ACCEN0_Bits.EN21 */
#define IFX_EBCU_ACCEN0_EN21_LEN (1u)

/** \brief  Mask for Ifx_EBCU_ACCEN0_Bits.EN21 */
#define IFX_EBCU_ACCEN0_EN21_MSK (0x1u)

/** \brief  Offset for Ifx_EBCU_ACCEN0_Bits.EN21 */
#define IFX_EBCU_ACCEN0_EN21_OFF (21u)

/** \brief  Length for Ifx_EBCU_ACCEN0_Bits.EN22 */
#define IFX_EBCU_ACCEN0_EN22_LEN (1u)

/** \brief  Mask for Ifx_EBCU_ACCEN0_Bits.EN22 */
#define IFX_EBCU_ACCEN0_EN22_MSK (0x1u)

/** \brief  Offset for Ifx_EBCU_ACCEN0_Bits.EN22 */
#define IFX_EBCU_ACCEN0_EN22_OFF (22u)

/** \brief  Length for Ifx_EBCU_ACCEN0_Bits.EN23 */
#define IFX_EBCU_ACCEN0_EN23_LEN (1u)

/** \brief  Mask for Ifx_EBCU_ACCEN0_Bits.EN23 */
#define IFX_EBCU_ACCEN0_EN23_MSK (0x1u)

/** \brief  Offset for Ifx_EBCU_ACCEN0_Bits.EN23 */
#define IFX_EBCU_ACCEN0_EN23_OFF (23u)

/** \brief  Length for Ifx_EBCU_ACCEN0_Bits.EN24 */
#define IFX_EBCU_ACCEN0_EN24_LEN (1u)

/** \brief  Mask for Ifx_EBCU_ACCEN0_Bits.EN24 */
#define IFX_EBCU_ACCEN0_EN24_MSK (0x1u)

/** \brief  Offset for Ifx_EBCU_ACCEN0_Bits.EN24 */
#define IFX_EBCU_ACCEN0_EN24_OFF (24u)

/** \brief  Length for Ifx_EBCU_ACCEN0_Bits.EN25 */
#define IFX_EBCU_ACCEN0_EN25_LEN (1u)

/** \brief  Mask for Ifx_EBCU_ACCEN0_Bits.EN25 */
#define IFX_EBCU_ACCEN0_EN25_MSK (0x1u)

/** \brief  Offset for Ifx_EBCU_ACCEN0_Bits.EN25 */
#define IFX_EBCU_ACCEN0_EN25_OFF (25u)

/** \brief  Length for Ifx_EBCU_ACCEN0_Bits.EN26 */
#define IFX_EBCU_ACCEN0_EN26_LEN (1u)

/** \brief  Mask for Ifx_EBCU_ACCEN0_Bits.EN26 */
#define IFX_EBCU_ACCEN0_EN26_MSK (0x1u)

/** \brief  Offset for Ifx_EBCU_ACCEN0_Bits.EN26 */
#define IFX_EBCU_ACCEN0_EN26_OFF (26u)

/** \brief  Length for Ifx_EBCU_ACCEN0_Bits.EN27 */
#define IFX_EBCU_ACCEN0_EN27_LEN (1u)

/** \brief  Mask for Ifx_EBCU_ACCEN0_Bits.EN27 */
#define IFX_EBCU_ACCEN0_EN27_MSK (0x1u)

/** \brief  Offset for Ifx_EBCU_ACCEN0_Bits.EN27 */
#define IFX_EBCU_ACCEN0_EN27_OFF (27u)

/** \brief  Length for Ifx_EBCU_ACCEN0_Bits.EN28 */
#define IFX_EBCU_ACCEN0_EN28_LEN (1u)

/** \brief  Mask for Ifx_EBCU_ACCEN0_Bits.EN28 */
#define IFX_EBCU_ACCEN0_EN28_MSK (0x1u)

/** \brief  Offset for Ifx_EBCU_ACCEN0_Bits.EN28 */
#define IFX_EBCU_ACCEN0_EN28_OFF (28u)

/** \brief  Length for Ifx_EBCU_ACCEN0_Bits.EN29 */
#define IFX_EBCU_ACCEN0_EN29_LEN (1u)

/** \brief  Mask for Ifx_EBCU_ACCEN0_Bits.EN29 */
#define IFX_EBCU_ACCEN0_EN29_MSK (0x1u)

/** \brief  Offset for Ifx_EBCU_ACCEN0_Bits.EN29 */
#define IFX_EBCU_ACCEN0_EN29_OFF (29u)

/** \brief  Length for Ifx_EBCU_ACCEN0_Bits.EN2 */
#define IFX_EBCU_ACCEN0_EN2_LEN (1u)

/** \brief  Mask for Ifx_EBCU_ACCEN0_Bits.EN2 */
#define IFX_EBCU_ACCEN0_EN2_MSK (0x1u)

/** \brief  Offset for Ifx_EBCU_ACCEN0_Bits.EN2 */
#define IFX_EBCU_ACCEN0_EN2_OFF (2u)

/** \brief  Length for Ifx_EBCU_ACCEN0_Bits.EN30 */
#define IFX_EBCU_ACCEN0_EN30_LEN (1u)

/** \brief  Mask for Ifx_EBCU_ACCEN0_Bits.EN30 */
#define IFX_EBCU_ACCEN0_EN30_MSK (0x1u)

/** \brief  Offset for Ifx_EBCU_ACCEN0_Bits.EN30 */
#define IFX_EBCU_ACCEN0_EN30_OFF (30u)

/** \brief  Length for Ifx_EBCU_ACCEN0_Bits.EN31 */
#define IFX_EBCU_ACCEN0_EN31_LEN (1u)

/** \brief  Mask for Ifx_EBCU_ACCEN0_Bits.EN31 */
#define IFX_EBCU_ACCEN0_EN31_MSK (0x1u)

/** \brief  Offset for Ifx_EBCU_ACCEN0_Bits.EN31 */
#define IFX_EBCU_ACCEN0_EN31_OFF (31u)

/** \brief  Length for Ifx_EBCU_ACCEN0_Bits.EN3 */
#define IFX_EBCU_ACCEN0_EN3_LEN (1u)

/** \brief  Mask for Ifx_EBCU_ACCEN0_Bits.EN3 */
#define IFX_EBCU_ACCEN0_EN3_MSK (0x1u)

/** \brief  Offset for Ifx_EBCU_ACCEN0_Bits.EN3 */
#define IFX_EBCU_ACCEN0_EN3_OFF (3u)

/** \brief  Length for Ifx_EBCU_ACCEN0_Bits.EN4 */
#define IFX_EBCU_ACCEN0_EN4_LEN (1u)

/** \brief  Mask for Ifx_EBCU_ACCEN0_Bits.EN4 */
#define IFX_EBCU_ACCEN0_EN4_MSK (0x1u)

/** \brief  Offset for Ifx_EBCU_ACCEN0_Bits.EN4 */
#define IFX_EBCU_ACCEN0_EN4_OFF (4u)

/** \brief  Length for Ifx_EBCU_ACCEN0_Bits.EN5 */
#define IFX_EBCU_ACCEN0_EN5_LEN (1u)

/** \brief  Mask for Ifx_EBCU_ACCEN0_Bits.EN5 */
#define IFX_EBCU_ACCEN0_EN5_MSK (0x1u)

/** \brief  Offset for Ifx_EBCU_ACCEN0_Bits.EN5 */
#define IFX_EBCU_ACCEN0_EN5_OFF (5u)

/** \brief  Length for Ifx_EBCU_ACCEN0_Bits.EN6 */
#define IFX_EBCU_ACCEN0_EN6_LEN (1u)

/** \brief  Mask for Ifx_EBCU_ACCEN0_Bits.EN6 */
#define IFX_EBCU_ACCEN0_EN6_MSK (0x1u)

/** \brief  Offset for Ifx_EBCU_ACCEN0_Bits.EN6 */
#define IFX_EBCU_ACCEN0_EN6_OFF (6u)

/** \brief  Length for Ifx_EBCU_ACCEN0_Bits.EN7 */
#define IFX_EBCU_ACCEN0_EN7_LEN (1u)

/** \brief  Mask for Ifx_EBCU_ACCEN0_Bits.EN7 */
#define IFX_EBCU_ACCEN0_EN7_MSK (0x1u)

/** \brief  Offset for Ifx_EBCU_ACCEN0_Bits.EN7 */
#define IFX_EBCU_ACCEN0_EN7_OFF (7u)

/** \brief  Length for Ifx_EBCU_ACCEN0_Bits.EN8 */
#define IFX_EBCU_ACCEN0_EN8_LEN (1u)

/** \brief  Mask for Ifx_EBCU_ACCEN0_Bits.EN8 */
#define IFX_EBCU_ACCEN0_EN8_MSK (0x1u)

/** \brief  Offset for Ifx_EBCU_ACCEN0_Bits.EN8 */
#define IFX_EBCU_ACCEN0_EN8_OFF (8u)

/** \brief  Length for Ifx_EBCU_ACCEN0_Bits.EN9 */
#define IFX_EBCU_ACCEN0_EN9_LEN (1u)

/** \brief  Mask for Ifx_EBCU_ACCEN0_Bits.EN9 */
#define IFX_EBCU_ACCEN0_EN9_MSK (0x1u)

/** \brief  Offset for Ifx_EBCU_ACCEN0_Bits.EN9 */
#define IFX_EBCU_ACCEN0_EN9_OFF (9u)

/** \brief  Length for Ifx_EBCU_CON_Bits.DBG */
#define IFX_EBCU_CON_DBG_LEN (1u)

/** \brief  Mask for Ifx_EBCU_CON_Bits.DBG */
#define IFX_EBCU_CON_DBG_MSK (0x1u)

/** \brief  Offset for Ifx_EBCU_CON_Bits.DBG */
#define IFX_EBCU_CON_DBG_OFF (16u)

/** \brief  Length for Ifx_EBCU_CON_Bits.SPC */
#define IFX_EBCU_CON_SPC_LEN (8u)

/** \brief  Mask for Ifx_EBCU_CON_Bits.SPC */
#define IFX_EBCU_CON_SPC_MSK (0xffu)

/** \brief  Offset for Ifx_EBCU_CON_Bits.SPC */
#define IFX_EBCU_CON_SPC_OFF (24u)

/** \brief  Length for Ifx_EBCU_CON_Bits.TOUT */
#define IFX_EBCU_CON_TOUT_LEN (16u)

/** \brief  Mask for Ifx_EBCU_CON_Bits.TOUT */
#define IFX_EBCU_CON_TOUT_MSK (0xffffu)

/** \brief  Offset for Ifx_EBCU_CON_Bits.TOUT */
#define IFX_EBCU_CON_TOUT_OFF (0u)

/** \brief  Length for Ifx_EBCU_EADD_Bits.FPIADR */
#define IFX_EBCU_EADD_FPIADR_LEN (32u)

/** \brief  Mask for Ifx_EBCU_EADD_Bits.FPIADR */
#define IFX_EBCU_EADD_FPIADR_MSK (0xffffffffu)

/** \brief  Offset for Ifx_EBCU_EADD_Bits.FPIADR */
#define IFX_EBCU_EADD_FPIADR_OFF (0u)

/** \brief  Length for Ifx_EBCU_ECON_Bits.ABT */
#define IFX_EBCU_ECON_ABT_LEN (1u)

/** \brief  Mask for Ifx_EBCU_ECON_Bits.ABT */
#define IFX_EBCU_ECON_ABT_MSK (0x1u)

/** \brief  Offset for Ifx_EBCU_ECON_Bits.ABT */
#define IFX_EBCU_ECON_ABT_OFF (16u)

/** \brief  Length for Ifx_EBCU_ECON_Bits.ACK */
#define IFX_EBCU_ECON_ACK_LEN (2u)

/** \brief  Mask for Ifx_EBCU_ECON_Bits.ACK */
#define IFX_EBCU_ECON_ACK_MSK (0x3u)

/** \brief  Offset for Ifx_EBCU_ECON_Bits.ACK */
#define IFX_EBCU_ECON_ACK_OFF (17u)

/** \brief  Length for Ifx_EBCU_ECON_Bits.ERRCNT */
#define IFX_EBCU_ECON_ERRCNT_LEN (14u)

/** \brief  Mask for Ifx_EBCU_ECON_Bits.ERRCNT */
#define IFX_EBCU_ECON_ERRCNT_MSK (0x3fffu)

/** \brief  Offset for Ifx_EBCU_ECON_Bits.ERRCNT */
#define IFX_EBCU_ECON_ERRCNT_OFF (0u)

/** \brief  Length for Ifx_EBCU_ECON_Bits.OPC */
#define IFX_EBCU_ECON_OPC_LEN (4u)

/** \brief  Mask for Ifx_EBCU_ECON_Bits.OPC */
#define IFX_EBCU_ECON_OPC_MSK (0xfu)

/** \brief  Offset for Ifx_EBCU_ECON_Bits.OPC */
#define IFX_EBCU_ECON_OPC_OFF (28u)

/** \brief  Length for Ifx_EBCU_ECON_Bits.RDN */
#define IFX_EBCU_ECON_RDN_LEN (1u)

/** \brief  Mask for Ifx_EBCU_ECON_Bits.RDN */
#define IFX_EBCU_ECON_RDN_MSK (0x1u)

/** \brief  Offset for Ifx_EBCU_ECON_Bits.RDN */
#define IFX_EBCU_ECON_RDN_OFF (21u)

/** \brief  Length for Ifx_EBCU_ECON_Bits.RDY */
#define IFX_EBCU_ECON_RDY_LEN (1u)

/** \brief  Mask for Ifx_EBCU_ECON_Bits.RDY */
#define IFX_EBCU_ECON_RDY_MSK (0x1u)

/** \brief  Offset for Ifx_EBCU_ECON_Bits.RDY */
#define IFX_EBCU_ECON_RDY_OFF (15u)

/** \brief  Length for Ifx_EBCU_ECON_Bits.SVM */
#define IFX_EBCU_ECON_SVM_LEN (1u)

/** \brief  Mask for Ifx_EBCU_ECON_Bits.SVM */
#define IFX_EBCU_ECON_SVM_MSK (0x1u)

/** \brief  Offset for Ifx_EBCU_ECON_Bits.SVM */
#define IFX_EBCU_ECON_SVM_OFF (19u)

/** \brief  Length for Ifx_EBCU_ECON_Bits.TAG */
#define IFX_EBCU_ECON_TAG_LEN (6u)

/** \brief  Mask for Ifx_EBCU_ECON_Bits.TAG */
#define IFX_EBCU_ECON_TAG_MSK (0x3fu)

/** \brief  Offset for Ifx_EBCU_ECON_Bits.TAG */
#define IFX_EBCU_ECON_TAG_OFF (22u)

/** \brief  Length for Ifx_EBCU_ECON_Bits.TOUT */
#define IFX_EBCU_ECON_TOUT_LEN (1u)

/** \brief  Mask for Ifx_EBCU_ECON_Bits.TOUT */
#define IFX_EBCU_ECON_TOUT_MSK (0x1u)

/** \brief  Offset for Ifx_EBCU_ECON_Bits.TOUT */
#define IFX_EBCU_ECON_TOUT_OFF (14u)

/** \brief  Length for Ifx_EBCU_ECON_Bits.WRN */
#define IFX_EBCU_ECON_WRN_LEN (1u)

/** \brief  Mask for Ifx_EBCU_ECON_Bits.WRN */
#define IFX_EBCU_ECON_WRN_MSK (0x1u)

/** \brief  Offset for Ifx_EBCU_ECON_Bits.WRN */
#define IFX_EBCU_ECON_WRN_OFF (20u)

/** \brief  Length for Ifx_EBCU_EDAT_Bits.FPIDAT */
#define IFX_EBCU_EDAT_FPIDAT_LEN (32u)

/** \brief  Mask for Ifx_EBCU_EDAT_Bits.FPIDAT */
#define IFX_EBCU_EDAT_FPIDAT_MSK (0xffffffffu)

/** \brief  Offset for Ifx_EBCU_EDAT_Bits.FPIDAT */
#define IFX_EBCU_EDAT_FPIDAT_OFF (0u)

/** \brief  Length for Ifx_EBCU_ID_Bits.MOD_REV */
#define IFX_EBCU_ID_MOD_REV_LEN (8u)

/** \brief  Mask for Ifx_EBCU_ID_Bits.MOD_REV */
#define IFX_EBCU_ID_MOD_REV_MSK (0xffu)

/** \brief  Offset for Ifx_EBCU_ID_Bits.MOD_REV */
#define IFX_EBCU_ID_MOD_REV_OFF (0u)

/** \brief  Length for Ifx_EBCU_ID_Bits.MODNUMBER */
#define IFX_EBCU_ID_MODNUMBER_LEN (8u)

/** \brief  Mask for Ifx_EBCU_ID_Bits.MODNUMBER */
#define IFX_EBCU_ID_MODNUMBER_MSK (0xffu)

/** \brief  Offset for Ifx_EBCU_ID_Bits.MODNUMBER */
#define IFX_EBCU_ID_MODNUMBER_OFF (8u)

/** \brief  Length for Ifx_EBCU_PRIOH_Bits.MASTER10 */
#define IFX_EBCU_PRIOH_MASTER10_LEN (4u)

/** \brief  Mask for Ifx_EBCU_PRIOH_Bits.MASTER10 */
#define IFX_EBCU_PRIOH_MASTER10_MSK (0xfu)

/** \brief  Offset for Ifx_EBCU_PRIOH_Bits.MASTER10 */
#define IFX_EBCU_PRIOH_MASTER10_OFF (8u)

/** \brief  Length for Ifx_EBCU_PRIOH_Bits.MASTER11 */
#define IFX_EBCU_PRIOH_MASTER11_LEN (4u)

/** \brief  Mask for Ifx_EBCU_PRIOH_Bits.MASTER11 */
#define IFX_EBCU_PRIOH_MASTER11_MSK (0xfu)

/** \brief  Offset for Ifx_EBCU_PRIOH_Bits.MASTER11 */
#define IFX_EBCU_PRIOH_MASTER11_OFF (12u)

/** \brief  Length for Ifx_EBCU_PRIOH_Bits.MASTER12 */
#define IFX_EBCU_PRIOH_MASTER12_LEN (4u)

/** \brief  Mask for Ifx_EBCU_PRIOH_Bits.MASTER12 */
#define IFX_EBCU_PRIOH_MASTER12_MSK (0xfu)

/** \brief  Offset for Ifx_EBCU_PRIOH_Bits.MASTER12 */
#define IFX_EBCU_PRIOH_MASTER12_OFF (16u)

/** \brief  Length for Ifx_EBCU_PRIOH_Bits.MASTER13 */
#define IFX_EBCU_PRIOH_MASTER13_LEN (4u)

/** \brief  Mask for Ifx_EBCU_PRIOH_Bits.MASTER13 */
#define IFX_EBCU_PRIOH_MASTER13_MSK (0xfu)

/** \brief  Offset for Ifx_EBCU_PRIOH_Bits.MASTER13 */
#define IFX_EBCU_PRIOH_MASTER13_OFF (20u)

/** \brief  Length for Ifx_EBCU_PRIOH_Bits.MASTER14 */
#define IFX_EBCU_PRIOH_MASTER14_LEN (4u)

/** \brief  Mask for Ifx_EBCU_PRIOH_Bits.MASTER14 */
#define IFX_EBCU_PRIOH_MASTER14_MSK (0xfu)

/** \brief  Offset for Ifx_EBCU_PRIOH_Bits.MASTER14 */
#define IFX_EBCU_PRIOH_MASTER14_OFF (24u)

/** \brief  Length for Ifx_EBCU_PRIOH_Bits.MASTER15 */
#define IFX_EBCU_PRIOH_MASTER15_LEN (4u)

/** \brief  Mask for Ifx_EBCU_PRIOH_Bits.MASTER15 */
#define IFX_EBCU_PRIOH_MASTER15_MSK (0xfu)

/** \brief  Offset for Ifx_EBCU_PRIOH_Bits.MASTER15 */
#define IFX_EBCU_PRIOH_MASTER15_OFF (28u)

/** \brief  Length for Ifx_EBCU_PRIOH_Bits.MASTER8 */
#define IFX_EBCU_PRIOH_MASTER8_LEN (4u)

/** \brief  Mask for Ifx_EBCU_PRIOH_Bits.MASTER8 */
#define IFX_EBCU_PRIOH_MASTER8_MSK (0xfu)

/** \brief  Offset for Ifx_EBCU_PRIOH_Bits.MASTER8 */
#define IFX_EBCU_PRIOH_MASTER8_OFF (0u)

/** \brief  Length for Ifx_EBCU_PRIOH_Bits.MASTER9 */
#define IFX_EBCU_PRIOH_MASTER9_LEN (4u)

/** \brief  Mask for Ifx_EBCU_PRIOH_Bits.MASTER9 */
#define IFX_EBCU_PRIOH_MASTER9_MSK (0xfu)

/** \brief  Offset for Ifx_EBCU_PRIOH_Bits.MASTER9 */
#define IFX_EBCU_PRIOH_MASTER9_OFF (4u)

/** \brief  Length for Ifx_EBCU_PRIOL_Bits.MASTER0 */
#define IFX_EBCU_PRIOL_MASTER0_LEN (4u)

/** \brief  Mask for Ifx_EBCU_PRIOL_Bits.MASTER0 */
#define IFX_EBCU_PRIOL_MASTER0_MSK (0xfu)

/** \brief  Offset for Ifx_EBCU_PRIOL_Bits.MASTER0 */
#define IFX_EBCU_PRIOL_MASTER0_OFF (0u)

/** \brief  Length for Ifx_EBCU_PRIOL_Bits.MASTER1 */
#define IFX_EBCU_PRIOL_MASTER1_LEN (4u)

/** \brief  Mask for Ifx_EBCU_PRIOL_Bits.MASTER1 */
#define IFX_EBCU_PRIOL_MASTER1_MSK (0xfu)

/** \brief  Offset for Ifx_EBCU_PRIOL_Bits.MASTER1 */
#define IFX_EBCU_PRIOL_MASTER1_OFF (4u)

/** \brief  Length for Ifx_EBCU_PRIOL_Bits.MASTER2 */
#define IFX_EBCU_PRIOL_MASTER2_LEN (4u)

/** \brief  Mask for Ifx_EBCU_PRIOL_Bits.MASTER2 */
#define IFX_EBCU_PRIOL_MASTER2_MSK (0xfu)

/** \brief  Offset for Ifx_EBCU_PRIOL_Bits.MASTER2 */
#define IFX_EBCU_PRIOL_MASTER2_OFF (8u)

/** \brief  Length for Ifx_EBCU_PRIOL_Bits.MASTER3 */
#define IFX_EBCU_PRIOL_MASTER3_LEN (4u)

/** \brief  Mask for Ifx_EBCU_PRIOL_Bits.MASTER3 */
#define IFX_EBCU_PRIOL_MASTER3_MSK (0xfu)

/** \brief  Offset for Ifx_EBCU_PRIOL_Bits.MASTER3 */
#define IFX_EBCU_PRIOL_MASTER3_OFF (12u)

/** \brief  Length for Ifx_EBCU_PRIOL_Bits.MASTER4 */
#define IFX_EBCU_PRIOL_MASTER4_LEN (4u)

/** \brief  Mask for Ifx_EBCU_PRIOL_Bits.MASTER4 */
#define IFX_EBCU_PRIOL_MASTER4_MSK (0xfu)

/** \brief  Offset for Ifx_EBCU_PRIOL_Bits.MASTER4 */
#define IFX_EBCU_PRIOL_MASTER4_OFF (16u)

/** \brief  Length for Ifx_EBCU_PRIOL_Bits.MASTER5 */
#define IFX_EBCU_PRIOL_MASTER5_LEN (4u)

/** \brief  Mask for Ifx_EBCU_PRIOL_Bits.MASTER5 */
#define IFX_EBCU_PRIOL_MASTER5_MSK (0xfu)

/** \brief  Offset for Ifx_EBCU_PRIOL_Bits.MASTER5 */
#define IFX_EBCU_PRIOL_MASTER5_OFF (20u)

/** \brief  Length for Ifx_EBCU_PRIOL_Bits.MASTER6 */
#define IFX_EBCU_PRIOL_MASTER6_LEN (4u)

/** \brief  Mask for Ifx_EBCU_PRIOL_Bits.MASTER6 */
#define IFX_EBCU_PRIOL_MASTER6_MSK (0xfu)

/** \brief  Offset for Ifx_EBCU_PRIOL_Bits.MASTER6 */
#define IFX_EBCU_PRIOL_MASTER6_OFF (24u)

/** \brief  Length for Ifx_EBCU_PRIOL_Bits.MASTER7 */
#define IFX_EBCU_PRIOL_MASTER7_LEN (4u)

/** \brief  Mask for Ifx_EBCU_PRIOL_Bits.MASTER7 */
#define IFX_EBCU_PRIOL_MASTER7_MSK (0xfu)

/** \brief  Offset for Ifx_EBCU_PRIOL_Bits.MASTER7 */
#define IFX_EBCU_PRIOL_MASTER7_OFF (28u)
/** \}  */
/******************************************************************************/
/******************************************************************************/
#endif /* IFXEBCU_BF_H */
