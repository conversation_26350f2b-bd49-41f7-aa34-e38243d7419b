/**
 * \file IfxFft_cfg.h
 * \brief FFT on-chip implementation data
 * \ingroup IfxLld_Fft
 *
 * \version iLLD_1_0_1_11_0
 * \copyright Copyright (c) 2017 Infineon Technologies AG. All rights reserved.
 *
 *
 *                                 IMPORTANT NOTICE
 *
 *
 * Use of this file is subject to the terms of use agreed between (i) you or 
 * the company in which ordinary course of business you are acting and (ii) 
 * Infineon Technologies AG or its licensees. If and as long as no such 
 * terms of use are agreed, use of this file is subject to following:


 * Boost Software License - Version 1.0 - August 17th, 2003

 * Permission is hereby granted, free of charge, to any person or 
 * organization obtaining a copy of the software and accompanying 
 * documentation covered by this license (the "Software") to use, reproduce,
 * display, distribute, execute, and transmit the Software, and to prepare
 * derivative works of the Software, and to permit third-parties to whom the 
 * Software is furnished to do so, all subject to the following:

 * The copyright notices in the Software and this entire statement, including
 * the above license grant, this restriction and the following disclaimer, must
 * be included in all copies of the Software, in whole or in part, and all
 * derivative works of the Software, unless such copies or derivative works are
 * solely in the form of machine-executable object code generated by a source
 * language processor.

 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR 
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE, TITLE AND NON-INFRINGEMENT. IN NO EVENT
 * SHALL THE COPYRIGHT HOLDERS OR ANYONE DISTRIBUTING THE SOFTWARE BE LIABLE 
 * FOR ANY DAMAGES OR OTHER LIABILITY, WHETHER IN CONTRACT, TORT OR OTHERWISE,
 * ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
 * DEALINGS IN THE SOFTWARE.

 *
 * \defgroup IfxLld_Fft FFT
 * \ingroup IfxLld
 * \defgroup IfxLld_Fft_Impl Implementation
 * \ingroup IfxLld_Fft
 * \defgroup IfxLld_Fft_Std Standard Driver
 * \ingroup IfxLld_Fft
 */

#ifndef IFXFFT_CFG_H
#define IFXFFT_CFG_H 1

/******************************************************************************/
/*----------------------------------Includes----------------------------------*/
/******************************************************************************/

/******************************************************************************/
/*-----------------------------------Macros-----------------------------------*/
/******************************************************************************/

/** \brief Data space starting address
 */
#define IFXFFT_DATA_SPACE           0xBE000000U

/** \brief Window coefficient space starting address
 */
#define IFXFFT_WINDOW_SPACE         0xBE100000U

/** \brief Maximim possible length of the transform
 */
#define IFXFFT_MAX_LENGTH           2048

#define IFXFFT_MAX_WINDOW_LENGTH    (IFXFFT_MAX_LENGTH / 2)

#define IFXFFT_FFT_DMA_SUPPORT      (1)

#define IFXFFT_FFT_OPTIMIZED        (0)

#define IFXFFT_FFT_DMA_CHANNEL_BASE 4

#define IFXFFT_FFT_PIPELINED        (1)

#define IFXFFT_FFT_NUM_JOBS         (8)

#define IFXFFT_NUM_MODULES          (1)

#endif /* IFXFFT_CFG_H */
