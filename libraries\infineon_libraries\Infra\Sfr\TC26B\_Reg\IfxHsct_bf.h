/**
 * \file IfxHsct_bf.h
 * \brief
 * \copyright Copyright (c) 2015 Infineon Technologies AG. All rights reserved.
 *
 * Version: TC26XB_UM_V1.2.R0
 * Specification: tc26xB_um_v1.2_MCSFR.xml (Revision: UM_V1.2)
 * MAY BE CHANGED BY USER [yes/no]: No
 *
 *                                 IMPORTANT NOTICE
 *
 * Infineon Technologies AG (Infineon) is supplying this file for use
 * exclusively with Infineon's microcontroller products. This file can be freely
 * distributed within development tools that are supporting such microcontroller
 * products.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS".  NO WARRANTIES, WHETHER EXPRESS, IMPLIED
 * OR STATUTORY, INCLUDING, BUT NOT LIMITED TO, IMPLIED WARRANTIES OF
 * MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE APPLY TO THIS SOFTWARE.
 * INFINEON SHALL NOT, IN ANY CIRCUMSTANCES, BE LIABLE FOR SPECIAL, INCIDENTAL,
 * OR CONSEQUENTIAL DAMAGES, FOR ANY REASON WHATSOEVER.
 *
 * \defgroup IfxLld_Hsct_BitfieldsMask Bitfields mask and offset
 * \ingroup IfxLld_Hsct
 * 
 */
#ifndef IFXHSCT_BF_H
#define IFXHSCT_BF_H 1
/******************************************************************************/
/******************************************************************************/
/** \addtogroup IfxLld_Hsct_BitfieldsMask
 * \{  */

/** \brief  Length for Ifx_HSCT_ACCEN0_Bits.EN0 */
#define IFX_HSCT_ACCEN0_EN0_LEN (1u)

/** \brief  Mask for Ifx_HSCT_ACCEN0_Bits.EN0 */
#define IFX_HSCT_ACCEN0_EN0_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_ACCEN0_Bits.EN0 */
#define IFX_HSCT_ACCEN0_EN0_OFF (0u)

/** \brief  Length for Ifx_HSCT_ACCEN0_Bits.EN10 */
#define IFX_HSCT_ACCEN0_EN10_LEN (1u)

/** \brief  Mask for Ifx_HSCT_ACCEN0_Bits.EN10 */
#define IFX_HSCT_ACCEN0_EN10_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_ACCEN0_Bits.EN10 */
#define IFX_HSCT_ACCEN0_EN10_OFF (10u)

/** \brief  Length for Ifx_HSCT_ACCEN0_Bits.EN11 */
#define IFX_HSCT_ACCEN0_EN11_LEN (1u)

/** \brief  Mask for Ifx_HSCT_ACCEN0_Bits.EN11 */
#define IFX_HSCT_ACCEN0_EN11_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_ACCEN0_Bits.EN11 */
#define IFX_HSCT_ACCEN0_EN11_OFF (11u)

/** \brief  Length for Ifx_HSCT_ACCEN0_Bits.EN12 */
#define IFX_HSCT_ACCEN0_EN12_LEN (1u)

/** \brief  Mask for Ifx_HSCT_ACCEN0_Bits.EN12 */
#define IFX_HSCT_ACCEN0_EN12_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_ACCEN0_Bits.EN12 */
#define IFX_HSCT_ACCEN0_EN12_OFF (12u)

/** \brief  Length for Ifx_HSCT_ACCEN0_Bits.EN13 */
#define IFX_HSCT_ACCEN0_EN13_LEN (1u)

/** \brief  Mask for Ifx_HSCT_ACCEN0_Bits.EN13 */
#define IFX_HSCT_ACCEN0_EN13_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_ACCEN0_Bits.EN13 */
#define IFX_HSCT_ACCEN0_EN13_OFF (13u)

/** \brief  Length for Ifx_HSCT_ACCEN0_Bits.EN14 */
#define IFX_HSCT_ACCEN0_EN14_LEN (1u)

/** \brief  Mask for Ifx_HSCT_ACCEN0_Bits.EN14 */
#define IFX_HSCT_ACCEN0_EN14_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_ACCEN0_Bits.EN14 */
#define IFX_HSCT_ACCEN0_EN14_OFF (14u)

/** \brief  Length for Ifx_HSCT_ACCEN0_Bits.EN15 */
#define IFX_HSCT_ACCEN0_EN15_LEN (1u)

/** \brief  Mask for Ifx_HSCT_ACCEN0_Bits.EN15 */
#define IFX_HSCT_ACCEN0_EN15_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_ACCEN0_Bits.EN15 */
#define IFX_HSCT_ACCEN0_EN15_OFF (15u)

/** \brief  Length for Ifx_HSCT_ACCEN0_Bits.EN16 */
#define IFX_HSCT_ACCEN0_EN16_LEN (1u)

/** \brief  Mask for Ifx_HSCT_ACCEN0_Bits.EN16 */
#define IFX_HSCT_ACCEN0_EN16_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_ACCEN0_Bits.EN16 */
#define IFX_HSCT_ACCEN0_EN16_OFF (16u)

/** \brief  Length for Ifx_HSCT_ACCEN0_Bits.EN17 */
#define IFX_HSCT_ACCEN0_EN17_LEN (1u)

/** \brief  Mask for Ifx_HSCT_ACCEN0_Bits.EN17 */
#define IFX_HSCT_ACCEN0_EN17_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_ACCEN0_Bits.EN17 */
#define IFX_HSCT_ACCEN0_EN17_OFF (17u)

/** \brief  Length for Ifx_HSCT_ACCEN0_Bits.EN18 */
#define IFX_HSCT_ACCEN0_EN18_LEN (1u)

/** \brief  Mask for Ifx_HSCT_ACCEN0_Bits.EN18 */
#define IFX_HSCT_ACCEN0_EN18_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_ACCEN0_Bits.EN18 */
#define IFX_HSCT_ACCEN0_EN18_OFF (18u)

/** \brief  Length for Ifx_HSCT_ACCEN0_Bits.EN19 */
#define IFX_HSCT_ACCEN0_EN19_LEN (1u)

/** \brief  Mask for Ifx_HSCT_ACCEN0_Bits.EN19 */
#define IFX_HSCT_ACCEN0_EN19_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_ACCEN0_Bits.EN19 */
#define IFX_HSCT_ACCEN0_EN19_OFF (19u)

/** \brief  Length for Ifx_HSCT_ACCEN0_Bits.EN1 */
#define IFX_HSCT_ACCEN0_EN1_LEN (1u)

/** \brief  Mask for Ifx_HSCT_ACCEN0_Bits.EN1 */
#define IFX_HSCT_ACCEN0_EN1_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_ACCEN0_Bits.EN1 */
#define IFX_HSCT_ACCEN0_EN1_OFF (1u)

/** \brief  Length for Ifx_HSCT_ACCEN0_Bits.EN20 */
#define IFX_HSCT_ACCEN0_EN20_LEN (1u)

/** \brief  Mask for Ifx_HSCT_ACCEN0_Bits.EN20 */
#define IFX_HSCT_ACCEN0_EN20_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_ACCEN0_Bits.EN20 */
#define IFX_HSCT_ACCEN0_EN20_OFF (20u)

/** \brief  Length for Ifx_HSCT_ACCEN0_Bits.EN21 */
#define IFX_HSCT_ACCEN0_EN21_LEN (1u)

/** \brief  Mask for Ifx_HSCT_ACCEN0_Bits.EN21 */
#define IFX_HSCT_ACCEN0_EN21_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_ACCEN0_Bits.EN21 */
#define IFX_HSCT_ACCEN0_EN21_OFF (21u)

/** \brief  Length for Ifx_HSCT_ACCEN0_Bits.EN22 */
#define IFX_HSCT_ACCEN0_EN22_LEN (1u)

/** \brief  Mask for Ifx_HSCT_ACCEN0_Bits.EN22 */
#define IFX_HSCT_ACCEN0_EN22_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_ACCEN0_Bits.EN22 */
#define IFX_HSCT_ACCEN0_EN22_OFF (22u)

/** \brief  Length for Ifx_HSCT_ACCEN0_Bits.EN23 */
#define IFX_HSCT_ACCEN0_EN23_LEN (1u)

/** \brief  Mask for Ifx_HSCT_ACCEN0_Bits.EN23 */
#define IFX_HSCT_ACCEN0_EN23_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_ACCEN0_Bits.EN23 */
#define IFX_HSCT_ACCEN0_EN23_OFF (23u)

/** \brief  Length for Ifx_HSCT_ACCEN0_Bits.EN24 */
#define IFX_HSCT_ACCEN0_EN24_LEN (1u)

/** \brief  Mask for Ifx_HSCT_ACCEN0_Bits.EN24 */
#define IFX_HSCT_ACCEN0_EN24_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_ACCEN0_Bits.EN24 */
#define IFX_HSCT_ACCEN0_EN24_OFF (24u)

/** \brief  Length for Ifx_HSCT_ACCEN0_Bits.EN25 */
#define IFX_HSCT_ACCEN0_EN25_LEN (1u)

/** \brief  Mask for Ifx_HSCT_ACCEN0_Bits.EN25 */
#define IFX_HSCT_ACCEN0_EN25_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_ACCEN0_Bits.EN25 */
#define IFX_HSCT_ACCEN0_EN25_OFF (25u)

/** \brief  Length for Ifx_HSCT_ACCEN0_Bits.EN26 */
#define IFX_HSCT_ACCEN0_EN26_LEN (1u)

/** \brief  Mask for Ifx_HSCT_ACCEN0_Bits.EN26 */
#define IFX_HSCT_ACCEN0_EN26_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_ACCEN0_Bits.EN26 */
#define IFX_HSCT_ACCEN0_EN26_OFF (26u)

/** \brief  Length for Ifx_HSCT_ACCEN0_Bits.EN27 */
#define IFX_HSCT_ACCEN0_EN27_LEN (1u)

/** \brief  Mask for Ifx_HSCT_ACCEN0_Bits.EN27 */
#define IFX_HSCT_ACCEN0_EN27_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_ACCEN0_Bits.EN27 */
#define IFX_HSCT_ACCEN0_EN27_OFF (27u)

/** \brief  Length for Ifx_HSCT_ACCEN0_Bits.EN28 */
#define IFX_HSCT_ACCEN0_EN28_LEN (1u)

/** \brief  Mask for Ifx_HSCT_ACCEN0_Bits.EN28 */
#define IFX_HSCT_ACCEN0_EN28_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_ACCEN0_Bits.EN28 */
#define IFX_HSCT_ACCEN0_EN28_OFF (28u)

/** \brief  Length for Ifx_HSCT_ACCEN0_Bits.EN29 */
#define IFX_HSCT_ACCEN0_EN29_LEN (1u)

/** \brief  Mask for Ifx_HSCT_ACCEN0_Bits.EN29 */
#define IFX_HSCT_ACCEN0_EN29_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_ACCEN0_Bits.EN29 */
#define IFX_HSCT_ACCEN0_EN29_OFF (29u)

/** \brief  Length for Ifx_HSCT_ACCEN0_Bits.EN2 */
#define IFX_HSCT_ACCEN0_EN2_LEN (1u)

/** \brief  Mask for Ifx_HSCT_ACCEN0_Bits.EN2 */
#define IFX_HSCT_ACCEN0_EN2_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_ACCEN0_Bits.EN2 */
#define IFX_HSCT_ACCEN0_EN2_OFF (2u)

/** \brief  Length for Ifx_HSCT_ACCEN0_Bits.EN30 */
#define IFX_HSCT_ACCEN0_EN30_LEN (1u)

/** \brief  Mask for Ifx_HSCT_ACCEN0_Bits.EN30 */
#define IFX_HSCT_ACCEN0_EN30_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_ACCEN0_Bits.EN30 */
#define IFX_HSCT_ACCEN0_EN30_OFF (30u)

/** \brief  Length for Ifx_HSCT_ACCEN0_Bits.EN31 */
#define IFX_HSCT_ACCEN0_EN31_LEN (1u)

/** \brief  Mask for Ifx_HSCT_ACCEN0_Bits.EN31 */
#define IFX_HSCT_ACCEN0_EN31_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_ACCEN0_Bits.EN31 */
#define IFX_HSCT_ACCEN0_EN31_OFF (31u)

/** \brief  Length for Ifx_HSCT_ACCEN0_Bits.EN3 */
#define IFX_HSCT_ACCEN0_EN3_LEN (1u)

/** \brief  Mask for Ifx_HSCT_ACCEN0_Bits.EN3 */
#define IFX_HSCT_ACCEN0_EN3_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_ACCEN0_Bits.EN3 */
#define IFX_HSCT_ACCEN0_EN3_OFF (3u)

/** \brief  Length for Ifx_HSCT_ACCEN0_Bits.EN4 */
#define IFX_HSCT_ACCEN0_EN4_LEN (1u)

/** \brief  Mask for Ifx_HSCT_ACCEN0_Bits.EN4 */
#define IFX_HSCT_ACCEN0_EN4_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_ACCEN0_Bits.EN4 */
#define IFX_HSCT_ACCEN0_EN4_OFF (4u)

/** \brief  Length for Ifx_HSCT_ACCEN0_Bits.EN5 */
#define IFX_HSCT_ACCEN0_EN5_LEN (1u)

/** \brief  Mask for Ifx_HSCT_ACCEN0_Bits.EN5 */
#define IFX_HSCT_ACCEN0_EN5_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_ACCEN0_Bits.EN5 */
#define IFX_HSCT_ACCEN0_EN5_OFF (5u)

/** \brief  Length for Ifx_HSCT_ACCEN0_Bits.EN6 */
#define IFX_HSCT_ACCEN0_EN6_LEN (1u)

/** \brief  Mask for Ifx_HSCT_ACCEN0_Bits.EN6 */
#define IFX_HSCT_ACCEN0_EN6_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_ACCEN0_Bits.EN6 */
#define IFX_HSCT_ACCEN0_EN6_OFF (6u)

/** \brief  Length for Ifx_HSCT_ACCEN0_Bits.EN7 */
#define IFX_HSCT_ACCEN0_EN7_LEN (1u)

/** \brief  Mask for Ifx_HSCT_ACCEN0_Bits.EN7 */
#define IFX_HSCT_ACCEN0_EN7_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_ACCEN0_Bits.EN7 */
#define IFX_HSCT_ACCEN0_EN7_OFF (7u)

/** \brief  Length for Ifx_HSCT_ACCEN0_Bits.EN8 */
#define IFX_HSCT_ACCEN0_EN8_LEN (1u)

/** \brief  Mask for Ifx_HSCT_ACCEN0_Bits.EN8 */
#define IFX_HSCT_ACCEN0_EN8_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_ACCEN0_Bits.EN8 */
#define IFX_HSCT_ACCEN0_EN8_OFF (8u)

/** \brief  Length for Ifx_HSCT_ACCEN0_Bits.EN9 */
#define IFX_HSCT_ACCEN0_EN9_LEN (1u)

/** \brief  Mask for Ifx_HSCT_ACCEN0_Bits.EN9 */
#define IFX_HSCT_ACCEN0_EN9_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_ACCEN0_Bits.EN9 */
#define IFX_HSCT_ACCEN0_EN9_OFF (9u)

/** \brief  Length for Ifx_HSCT_CLC_Bits.DISR */
#define IFX_HSCT_CLC_DISR_LEN (1u)

/** \brief  Mask for Ifx_HSCT_CLC_Bits.DISR */
#define IFX_HSCT_CLC_DISR_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_CLC_Bits.DISR */
#define IFX_HSCT_CLC_DISR_OFF (0u)

/** \brief  Length for Ifx_HSCT_CLC_Bits.DISS */
#define IFX_HSCT_CLC_DISS_LEN (1u)

/** \brief  Mask for Ifx_HSCT_CLC_Bits.DISS */
#define IFX_HSCT_CLC_DISS_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_CLC_Bits.DISS */
#define IFX_HSCT_CLC_DISS_OFF (1u)

/** \brief  Length for Ifx_HSCT_CLC_Bits.EDIS */
#define IFX_HSCT_CLC_EDIS_LEN (1u)

/** \brief  Mask for Ifx_HSCT_CLC_Bits.EDIS */
#define IFX_HSCT_CLC_EDIS_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_CLC_Bits.EDIS */
#define IFX_HSCT_CLC_EDIS_OFF (3u)

/** \brief  Length for Ifx_HSCT_CONFIGPHY_Bits.OSCCLKEN */
#define IFX_HSCT_CONFIGPHY_OSCCLKEN_LEN (1u)

/** \brief  Mask for Ifx_HSCT_CONFIGPHY_Bits.OSCCLKEN */
#define IFX_HSCT_CONFIGPHY_OSCCLKEN_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_CONFIGPHY_Bits.OSCCLKEN */
#define IFX_HSCT_CONFIGPHY_OSCCLKEN_OFF (28u)

/** \brief  Length for Ifx_HSCT_CONFIGPHY_Bits.PHYRST */
#define IFX_HSCT_CONFIGPHY_PHYRST_LEN (1u)

/** \brief  Mask for Ifx_HSCT_CONFIGPHY_Bits.PHYRST */
#define IFX_HSCT_CONFIGPHY_PHYRST_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_CONFIGPHY_Bits.PHYRST */
#define IFX_HSCT_CONFIGPHY_PHYRST_OFF (15u)

/** \brief  Length for Ifx_HSCT_CONFIGPHY_Bits.PLLIVR */
#define IFX_HSCT_CONFIGPHY_PLLIVR_LEN (4u)

/** \brief  Mask for Ifx_HSCT_CONFIGPHY_Bits.PLLIVR */
#define IFX_HSCT_CONFIGPHY_PLLIVR_MSK (0xfu)

/** \brief  Offset for Ifx_HSCT_CONFIGPHY_Bits.PLLIVR */
#define IFX_HSCT_CONFIGPHY_PLLIVR_OFF (22u)

/** \brief  Length for Ifx_HSCT_CONFIGPHY_Bits.PLLKI */
#define IFX_HSCT_CONFIGPHY_PLLKI_LEN (3u)

/** \brief  Mask for Ifx_HSCT_CONFIGPHY_Bits.PLLKI */
#define IFX_HSCT_CONFIGPHY_PLLKI_MSK (0x7u)

/** \brief  Offset for Ifx_HSCT_CONFIGPHY_Bits.PLLKI */
#define IFX_HSCT_CONFIGPHY_PLLKI_OFF (19u)

/** \brief  Length for Ifx_HSCT_CONFIGPHY_Bits.PLLKP */
#define IFX_HSCT_CONFIGPHY_PLLKP_LEN (3u)

/** \brief  Mask for Ifx_HSCT_CONFIGPHY_Bits.PLLKP */
#define IFX_HSCT_CONFIGPHY_PLLKP_MSK (0x7u)

/** \brief  Offset for Ifx_HSCT_CONFIGPHY_Bits.PLLKP */
#define IFX_HSCT_CONFIGPHY_PLLKP_OFF (16u)

/** \brief  Length for Ifx_HSCT_CONFIGPHY_Bits.PLLKPKI */
#define IFX_HSCT_CONFIGPHY_PLLKPKI_LEN (1u)

/** \brief  Mask for Ifx_HSCT_CONFIGPHY_Bits.PLLKPKI */
#define IFX_HSCT_CONFIGPHY_PLLKPKI_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_CONFIGPHY_Bits.PLLKPKI */
#define IFX_HSCT_CONFIGPHY_PLLKPKI_OFF (14u)

/** \brief  Length for Ifx_HSCT_CONFIGPHY_Bits.PLLPE */
#define IFX_HSCT_CONFIGPHY_PLLPE_LEN (6u)

/** \brief  Mask for Ifx_HSCT_CONFIGPHY_Bits.PLLPE */
#define IFX_HSCT_CONFIGPHY_PLLPE_MSK (0x3fu)

/** \brief  Offset for Ifx_HSCT_CONFIGPHY_Bits.PLLPE */
#define IFX_HSCT_CONFIGPHY_PLLPE_OFF (2u)

/** \brief  Length for Ifx_HSCT_CONFIGPHY_Bits.PLLPON */
#define IFX_HSCT_CONFIGPHY_PLLPON_LEN (1u)

/** \brief  Mask for Ifx_HSCT_CONFIGPHY_Bits.PLLPON */
#define IFX_HSCT_CONFIGPHY_PLLPON_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_CONFIGPHY_Bits.PLLPON */
#define IFX_HSCT_CONFIGPHY_PLLPON_OFF (1u)

/** \brief  Length for Ifx_HSCT_CONFIGPHY_Bits.PLLWMF */
#define IFX_HSCT_CONFIGPHY_PLLWMF_LEN (6u)

/** \brief  Mask for Ifx_HSCT_CONFIGPHY_Bits.PLLWMF */
#define IFX_HSCT_CONFIGPHY_PLLWMF_MSK (0x3fu)

/** \brief  Offset for Ifx_HSCT_CONFIGPHY_Bits.PLLWMF */
#define IFX_HSCT_CONFIGPHY_PLLWMF_OFF (8u)

/** \brief  Length for Ifx_HSCT_CTSCTRL_Bits.CTS_FRAME */
#define IFX_HSCT_CTSCTRL_CTS_FRAME_LEN (1u)

/** \brief  Mask for Ifx_HSCT_CTSCTRL_Bits.CTS_FRAME */
#define IFX_HSCT_CTSCTRL_CTS_FRAME_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_CTSCTRL_Bits.CTS_FRAME */
#define IFX_HSCT_CTSCTRL_CTS_FRAME_OFF (0u)

/** \brief  Length for Ifx_HSCT_CTSCTRL_Bits.CTS_RXD */
#define IFX_HSCT_CTSCTRL_CTS_RXD_LEN (1u)

/** \brief  Mask for Ifx_HSCT_CTSCTRL_Bits.CTS_RXD */
#define IFX_HSCT_CTSCTRL_CTS_RXD_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_CTSCTRL_Bits.CTS_RXD */
#define IFX_HSCT_CTSCTRL_CTS_RXD_OFF (2u)

/** \brief  Length for Ifx_HSCT_CTSCTRL_Bits.CTS_TXD */
#define IFX_HSCT_CTSCTRL_CTS_TXD_LEN (1u)

/** \brief  Mask for Ifx_HSCT_CTSCTRL_Bits.CTS_TXD */
#define IFX_HSCT_CTSCTRL_CTS_TXD_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_CTSCTRL_Bits.CTS_TXD */
#define IFX_HSCT_CTSCTRL_CTS_TXD_OFF (1u)

/** \brief  Length for Ifx_HSCT_CTSCTRL_Bits.HSSL_CTS_FBD */
#define IFX_HSCT_CTSCTRL_HSSL_CTS_FBD_LEN (1u)

/** \brief  Mask for Ifx_HSCT_CTSCTRL_Bits.HSSL_CTS_FBD */
#define IFX_HSCT_CTSCTRL_HSSL_CTS_FBD_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_CTSCTRL_Bits.HSSL_CTS_FBD */
#define IFX_HSCT_CTSCTRL_HSSL_CTS_FBD_OFF (3u)

/** \brief  Length for Ifx_HSCT_DISABLE_Bits.RX_DIS */
#define IFX_HSCT_DISABLE_RX_DIS_LEN (1u)

/** \brief  Mask for Ifx_HSCT_DISABLE_Bits.RX_DIS */
#define IFX_HSCT_DISABLE_RX_DIS_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_DISABLE_Bits.RX_DIS */
#define IFX_HSCT_DISABLE_RX_DIS_OFF (1u)

/** \brief  Length for Ifx_HSCT_DISABLE_Bits.RX_HEPD */
#define IFX_HSCT_DISABLE_RX_HEPD_LEN (1u)

/** \brief  Mask for Ifx_HSCT_DISABLE_Bits.RX_HEPD */
#define IFX_HSCT_DISABLE_RX_HEPD_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_DISABLE_Bits.RX_HEPD */
#define IFX_HSCT_DISABLE_RX_HEPD_OFF (2u)

/** \brief  Length for Ifx_HSCT_DISABLE_Bits.TX_DIS */
#define IFX_HSCT_DISABLE_TX_DIS_LEN (1u)

/** \brief  Mask for Ifx_HSCT_DISABLE_Bits.TX_DIS */
#define IFX_HSCT_DISABLE_TX_DIS_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_DISABLE_Bits.TX_DIS */
#define IFX_HSCT_DISABLE_TX_DIS_OFF (0u)

/** \brief  Length for Ifx_HSCT_ID_Bits.MODNUMBER */
#define IFX_HSCT_ID_MODNUMBER_LEN (16u)

/** \brief  Mask for Ifx_HSCT_ID_Bits.MODNUMBER */
#define IFX_HSCT_ID_MODNUMBER_MSK (0xffffu)

/** \brief  Offset for Ifx_HSCT_ID_Bits.MODNUMBER */
#define IFX_HSCT_ID_MODNUMBER_OFF (16u)

/** \brief  Length for Ifx_HSCT_ID_Bits.MODREV */
#define IFX_HSCT_ID_MODREV_LEN (8u)

/** \brief  Mask for Ifx_HSCT_ID_Bits.MODREV */
#define IFX_HSCT_ID_MODREV_MSK (0xffu)

/** \brief  Offset for Ifx_HSCT_ID_Bits.MODREV */
#define IFX_HSCT_ID_MODREV_OFF (0u)

/** \brief  Length for Ifx_HSCT_ID_Bits.MODTYPE */
#define IFX_HSCT_ID_MODTYPE_LEN (8u)

/** \brief  Mask for Ifx_HSCT_ID_Bits.MODTYPE */
#define IFX_HSCT_ID_MODTYPE_MSK (0xffu)

/** \brief  Offset for Ifx_HSCT_ID_Bits.MODTYPE */
#define IFX_HSCT_ID_MODTYPE_OFF (8u)

/** \brief  Length for Ifx_HSCT_IFCTRL_Bits.IFCVS */
#define IFX_HSCT_IFCTRL_IFCVS_LEN (8u)

/** \brief  Mask for Ifx_HSCT_IFCTRL_Bits.IFCVS */
#define IFX_HSCT_IFCTRL_IFCVS_MSK (0xffu)

/** \brief  Offset for Ifx_HSCT_IFCTRL_Bits.IFCVS */
#define IFX_HSCT_IFCTRL_IFCVS_OFF (0u)

/** \brief  Length for Ifx_HSCT_IFCTRL_Bits.IFTESTMD */
#define IFX_HSCT_IFCTRL_IFTESTMD_LEN (1u)

/** \brief  Mask for Ifx_HSCT_IFCTRL_Bits.IFTESTMD */
#define IFX_HSCT_IFCTRL_IFTESTMD_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_IFCTRL_Bits.IFTESTMD */
#define IFX_HSCT_IFCTRL_IFTESTMD_OFF (20u)

/** \brief  Length for Ifx_HSCT_IFCTRL_Bits.MRXSPEED */
#define IFX_HSCT_IFCTRL_MRXSPEED_LEN (2u)

/** \brief  Mask for Ifx_HSCT_IFCTRL_Bits.MRXSPEED */
#define IFX_HSCT_IFCTRL_MRXSPEED_MSK (0x3u)

/** \brief  Offset for Ifx_HSCT_IFCTRL_Bits.MRXSPEED */
#define IFX_HSCT_IFCTRL_MRXSPEED_OFF (16u)

/** \brief  Length for Ifx_HSCT_IFCTRL_Bits.MTXSPEED */
#define IFX_HSCT_IFCTRL_MTXSPEED_LEN (2u)

/** \brief  Mask for Ifx_HSCT_IFCTRL_Bits.MTXSPEED */
#define IFX_HSCT_IFCTRL_MTXSPEED_MSK (0x3u)

/** \brief  Offset for Ifx_HSCT_IFCTRL_Bits.MTXSPEED */
#define IFX_HSCT_IFCTRL_MTXSPEED_OFF (18u)

/** \brief  Length for Ifx_HSCT_IFCTRL_Bits.SIFCV */
#define IFX_HSCT_IFCTRL_SIFCV_LEN (1u)

/** \brief  Mask for Ifx_HSCT_IFCTRL_Bits.SIFCV */
#define IFX_HSCT_IFCTRL_SIFCV_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_IFCTRL_Bits.SIFCV */
#define IFX_HSCT_IFCTRL_SIFCV_OFF (8u)

/** \brief  Length for Ifx_HSCT_IFSTAT_Bits.RX_STAT */
#define IFX_HSCT_IFSTAT_RX_STAT_LEN (3u)

/** \brief  Mask for Ifx_HSCT_IFSTAT_Bits.RX_STAT */
#define IFX_HSCT_IFSTAT_RX_STAT_MSK (0x7u)

/** \brief  Offset for Ifx_HSCT_IFSTAT_Bits.RX_STAT */
#define IFX_HSCT_IFSTAT_RX_STAT_OFF (0u)

/** \brief  Length for Ifx_HSCT_IFSTAT_Bits.TX_STAT */
#define IFX_HSCT_IFSTAT_TX_STAT_LEN (2u)

/** \brief  Mask for Ifx_HSCT_IFSTAT_Bits.TX_STAT */
#define IFX_HSCT_IFSTAT_TX_STAT_MSK (0x3u)

/** \brief  Offset for Ifx_HSCT_IFSTAT_Bits.TX_STAT */
#define IFX_HSCT_IFSTAT_TX_STAT_OFF (3u)

/** \brief  Length for Ifx_HSCT_INIT_Bits.IFM */
#define IFX_HSCT_INIT_IFM_LEN (1u)

/** \brief  Mask for Ifx_HSCT_INIT_Bits.IFM */
#define IFX_HSCT_INIT_IFM_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_INIT_Bits.IFM */
#define IFX_HSCT_INIT_IFM_OFF (3u)

/** \brief  Length for Ifx_HSCT_INIT_Bits.LHLR */
#define IFX_HSCT_INIT_LHLR_LEN (1u)

/** \brief  Mask for Ifx_HSCT_INIT_Bits.LHLR */
#define IFX_HSCT_INIT_LHLR_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_INIT_Bits.LHLR */
#define IFX_HSCT_INIT_LHLR_OFF (10u)

/** \brief  Length for Ifx_HSCT_INIT_Bits.RXHD */
#define IFX_HSCT_INIT_RXHD_LEN (3u)

/** \brief  Mask for Ifx_HSCT_INIT_Bits.RXHD */
#define IFX_HSCT_INIT_RXHD_MSK (0x7u)

/** \brief  Offset for Ifx_HSCT_INIT_Bits.RXHD */
#define IFX_HSCT_INIT_RXHD_OFF (19u)

/** \brief  Length for Ifx_HSCT_INIT_Bits.SRCF */
#define IFX_HSCT_INIT_SRCF_LEN (1u)

/** \brief  Mask for Ifx_HSCT_INIT_Bits.SRCF */
#define IFX_HSCT_INIT_SRCF_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_INIT_Bits.SRCF */
#define IFX_HSCT_INIT_SRCF_OFF (2u)

/** \brief  Length for Ifx_HSCT_INIT_Bits.SYS_CLK_EN */
#define IFX_HSCT_INIT_SYS_CLK_EN_LEN (1u)

/** \brief  Mask for Ifx_HSCT_INIT_Bits.SYS_CLK_EN */
#define IFX_HSCT_INIT_SYS_CLK_EN_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_INIT_Bits.SYS_CLK_EN */
#define IFX_HSCT_INIT_SYS_CLK_EN_OFF (1u)

/** \brief  Length for Ifx_HSCT_INIT_Bits.TXHD */
#define IFX_HSCT_INIT_TXHD_LEN (3u)

/** \brief  Mask for Ifx_HSCT_INIT_Bits.TXHD */
#define IFX_HSCT_INIT_TXHD_MSK (0x7u)

/** \brief  Offset for Ifx_HSCT_INIT_Bits.TXHD */
#define IFX_HSCT_INIT_TXHD_OFF (16u)

/** \brief  Length for Ifx_HSCT_IRQ_Bits.CER */
#define IFX_HSCT_IRQ_CER_LEN (1u)

/** \brief  Mask for Ifx_HSCT_IRQ_Bits.CER */
#define IFX_HSCT_IRQ_CER_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_IRQ_Bits.CER */
#define IFX_HSCT_IRQ_CER_OFF (3u)

/** \brief  Length for Ifx_HSCT_IRQ_Bits.HER */
#define IFX_HSCT_IRQ_HER_LEN (1u)

/** \brief  Mask for Ifx_HSCT_IRQ_Bits.HER */
#define IFX_HSCT_IRQ_HER_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_IRQ_Bits.HER */
#define IFX_HSCT_IRQ_HER_OFF (1u)

/** \brief  Length for Ifx_HSCT_IRQ_Bits.IFCFS */
#define IFX_HSCT_IRQ_IFCFS_LEN (1u)

/** \brief  Mask for Ifx_HSCT_IRQ_Bits.IFCFS */
#define IFX_HSCT_IRQ_IFCFS_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_IRQ_Bits.IFCFS */
#define IFX_HSCT_IRQ_IFCFS_OFF (4u)

/** \brief  Length for Ifx_HSCT_IRQ_Bits.PAR */
#define IFX_HSCT_IRQ_PAR_LEN (1u)

/** \brief  Mask for Ifx_HSCT_IRQ_Bits.PAR */
#define IFX_HSCT_IRQ_PAR_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_IRQ_Bits.PAR */
#define IFX_HSCT_IRQ_PAR_OFF (9u)

/** \brief  Length for Ifx_HSCT_IRQ_Bits.PLER */
#define IFX_HSCT_IRQ_PLER_LEN (1u)

/** \brief  Mask for Ifx_HSCT_IRQ_Bits.PLER */
#define IFX_HSCT_IRQ_PLER_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_IRQ_Bits.PLER */
#define IFX_HSCT_IRQ_PLER_OFF (7u)

/** \brief  Length for Ifx_HSCT_IRQ_Bits.PYER */
#define IFX_HSCT_IRQ_PYER_LEN (1u)

/** \brief  Mask for Ifx_HSCT_IRQ_Bits.PYER */
#define IFX_HSCT_IRQ_PYER_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_IRQ_Bits.PYER */
#define IFX_HSCT_IRQ_PYER_OFF (2u)

/** \brief  Length for Ifx_HSCT_IRQ_Bits.SFO */
#define IFX_HSCT_IRQ_SFO_LEN (1u)

/** \brief  Mask for Ifx_HSCT_IRQ_Bits.SFO */
#define IFX_HSCT_IRQ_SFO_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_IRQ_Bits.SFO */
#define IFX_HSCT_IRQ_SFO_OFF (11u)

/** \brief  Length for Ifx_HSCT_IRQ_Bits.SFU */
#define IFX_HSCT_IRQ_SFU_LEN (1u)

/** \brief  Mask for Ifx_HSCT_IRQ_Bits.SFU */
#define IFX_HSCT_IRQ_SFU_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_IRQ_Bits.SFU */
#define IFX_HSCT_IRQ_SFU_OFF (12u)

/** \brief  Length for Ifx_HSCT_IRQ_Bits.SMER */
#define IFX_HSCT_IRQ_SMER_LEN (1u)

/** \brief  Mask for Ifx_HSCT_IRQ_Bits.SMER */
#define IFX_HSCT_IRQ_SMER_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_IRQ_Bits.SMER */
#define IFX_HSCT_IRQ_SMER_OFF (5u)

/** \brief  Length for Ifx_HSCT_IRQ_Bits.TXTE */
#define IFX_HSCT_IRQ_TXTE_LEN (1u)

/** \brief  Mask for Ifx_HSCT_IRQ_Bits.TXTE */
#define IFX_HSCT_IRQ_TXTE_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_IRQ_Bits.TXTE */
#define IFX_HSCT_IRQ_TXTE_OFF (10u)

/** \brief  Length for Ifx_HSCT_IRQ_Bits.USM */
#define IFX_HSCT_IRQ_USM_LEN (1u)

/** \brief  Mask for Ifx_HSCT_IRQ_Bits.USM */
#define IFX_HSCT_IRQ_USM_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_IRQ_Bits.USM */
#define IFX_HSCT_IRQ_USM_OFF (8u)

/** \brief  Length for Ifx_HSCT_IRQ_Bits.USMSF */
#define IFX_HSCT_IRQ_USMSF_LEN (1u)

/** \brief  Mask for Ifx_HSCT_IRQ_Bits.USMSF */
#define IFX_HSCT_IRQ_USMSF_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_IRQ_Bits.USMSF */
#define IFX_HSCT_IRQ_USMSF_OFF (6u)

/** \brief  Length for Ifx_HSCT_IRQCLR_Bits.CERCLR */
#define IFX_HSCT_IRQCLR_CERCLR_LEN (1u)

/** \brief  Mask for Ifx_HSCT_IRQCLR_Bits.CERCLR */
#define IFX_HSCT_IRQCLR_CERCLR_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_IRQCLR_Bits.CERCLR */
#define IFX_HSCT_IRQCLR_CERCLR_OFF (3u)

/** \brief  Length for Ifx_HSCT_IRQCLR_Bits.HERCLR */
#define IFX_HSCT_IRQCLR_HERCLR_LEN (1u)

/** \brief  Mask for Ifx_HSCT_IRQCLR_Bits.HERCLR */
#define IFX_HSCT_IRQCLR_HERCLR_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_IRQCLR_Bits.HERCLR */
#define IFX_HSCT_IRQCLR_HERCLR_OFF (1u)

/** \brief  Length for Ifx_HSCT_IRQCLR_Bits.IFCFSCLR */
#define IFX_HSCT_IRQCLR_IFCFSCLR_LEN (1u)

/** \brief  Mask for Ifx_HSCT_IRQCLR_Bits.IFCFSCLR */
#define IFX_HSCT_IRQCLR_IFCFSCLR_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_IRQCLR_Bits.IFCFSCLR */
#define IFX_HSCT_IRQCLR_IFCFSCLR_OFF (4u)

/** \brief  Length for Ifx_HSCT_IRQCLR_Bits.PARCLR */
#define IFX_HSCT_IRQCLR_PARCLR_LEN (1u)

/** \brief  Mask for Ifx_HSCT_IRQCLR_Bits.PARCLR */
#define IFX_HSCT_IRQCLR_PARCLR_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_IRQCLR_Bits.PARCLR */
#define IFX_HSCT_IRQCLR_PARCLR_OFF (9u)

/** \brief  Length for Ifx_HSCT_IRQCLR_Bits.PLERCLR */
#define IFX_HSCT_IRQCLR_PLERCLR_LEN (1u)

/** \brief  Mask for Ifx_HSCT_IRQCLR_Bits.PLERCLR */
#define IFX_HSCT_IRQCLR_PLERCLR_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_IRQCLR_Bits.PLERCLR */
#define IFX_HSCT_IRQCLR_PLERCLR_OFF (7u)

/** \brief  Length for Ifx_HSCT_IRQCLR_Bits.PYERCLR */
#define IFX_HSCT_IRQCLR_PYERCLR_LEN (1u)

/** \brief  Mask for Ifx_HSCT_IRQCLR_Bits.PYERCLR */
#define IFX_HSCT_IRQCLR_PYERCLR_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_IRQCLR_Bits.PYERCLR */
#define IFX_HSCT_IRQCLR_PYERCLR_OFF (2u)

/** \brief  Length for Ifx_HSCT_IRQCLR_Bits.SFOCLR */
#define IFX_HSCT_IRQCLR_SFOCLR_LEN (1u)

/** \brief  Mask for Ifx_HSCT_IRQCLR_Bits.SFOCLR */
#define IFX_HSCT_IRQCLR_SFOCLR_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_IRQCLR_Bits.SFOCLR */
#define IFX_HSCT_IRQCLR_SFOCLR_OFF (11u)

/** \brief  Length for Ifx_HSCT_IRQCLR_Bits.SFUCLR */
#define IFX_HSCT_IRQCLR_SFUCLR_LEN (1u)

/** \brief  Mask for Ifx_HSCT_IRQCLR_Bits.SFUCLR */
#define IFX_HSCT_IRQCLR_SFUCLR_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_IRQCLR_Bits.SFUCLR */
#define IFX_HSCT_IRQCLR_SFUCLR_OFF (12u)

/** \brief  Length for Ifx_HSCT_IRQCLR_Bits.SMERCLR */
#define IFX_HSCT_IRQCLR_SMERCLR_LEN (1u)

/** \brief  Mask for Ifx_HSCT_IRQCLR_Bits.SMERCLR */
#define IFX_HSCT_IRQCLR_SMERCLR_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_IRQCLR_Bits.SMERCLR */
#define IFX_HSCT_IRQCLR_SMERCLR_OFF (5u)

/** \brief  Length for Ifx_HSCT_IRQCLR_Bits.TXTECLR */
#define IFX_HSCT_IRQCLR_TXTECLR_LEN (1u)

/** \brief  Mask for Ifx_HSCT_IRQCLR_Bits.TXTECLR */
#define IFX_HSCT_IRQCLR_TXTECLR_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_IRQCLR_Bits.TXTECLR */
#define IFX_HSCT_IRQCLR_TXTECLR_OFF (10u)

/** \brief  Length for Ifx_HSCT_IRQCLR_Bits.USMCLR */
#define IFX_HSCT_IRQCLR_USMCLR_LEN (1u)

/** \brief  Mask for Ifx_HSCT_IRQCLR_Bits.USMCLR */
#define IFX_HSCT_IRQCLR_USMCLR_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_IRQCLR_Bits.USMCLR */
#define IFX_HSCT_IRQCLR_USMCLR_OFF (8u)

/** \brief  Length for Ifx_HSCT_IRQCLR_Bits.USMSFCLR */
#define IFX_HSCT_IRQCLR_USMSFCLR_LEN (1u)

/** \brief  Mask for Ifx_HSCT_IRQCLR_Bits.USMSFCLR */
#define IFX_HSCT_IRQCLR_USMSFCLR_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_IRQCLR_Bits.USMSFCLR */
#define IFX_HSCT_IRQCLR_USMSFCLR_OFF (6u)

/** \brief  Length for Ifx_HSCT_IRQEN_Bits.CEREN */
#define IFX_HSCT_IRQEN_CEREN_LEN (1u)

/** \brief  Mask for Ifx_HSCT_IRQEN_Bits.CEREN */
#define IFX_HSCT_IRQEN_CEREN_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_IRQEN_Bits.CEREN */
#define IFX_HSCT_IRQEN_CEREN_OFF (3u)

/** \brief  Length for Ifx_HSCT_IRQEN_Bits.HEREN */
#define IFX_HSCT_IRQEN_HEREN_LEN (1u)

/** \brief  Mask for Ifx_HSCT_IRQEN_Bits.HEREN */
#define IFX_HSCT_IRQEN_HEREN_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_IRQEN_Bits.HEREN */
#define IFX_HSCT_IRQEN_HEREN_OFF (1u)

/** \brief  Length for Ifx_HSCT_IRQEN_Bits.IFCFSEN */
#define IFX_HSCT_IRQEN_IFCFSEN_LEN (1u)

/** \brief  Mask for Ifx_HSCT_IRQEN_Bits.IFCFSEN */
#define IFX_HSCT_IRQEN_IFCFSEN_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_IRQEN_Bits.IFCFSEN */
#define IFX_HSCT_IRQEN_IFCFSEN_OFF (4u)

/** \brief  Length for Ifx_HSCT_IRQEN_Bits.PAREN */
#define IFX_HSCT_IRQEN_PAREN_LEN (1u)

/** \brief  Mask for Ifx_HSCT_IRQEN_Bits.PAREN */
#define IFX_HSCT_IRQEN_PAREN_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_IRQEN_Bits.PAREN */
#define IFX_HSCT_IRQEN_PAREN_OFF (9u)

/** \brief  Length for Ifx_HSCT_IRQEN_Bits.PLEREN */
#define IFX_HSCT_IRQEN_PLEREN_LEN (1u)

/** \brief  Mask for Ifx_HSCT_IRQEN_Bits.PLEREN */
#define IFX_HSCT_IRQEN_PLEREN_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_IRQEN_Bits.PLEREN */
#define IFX_HSCT_IRQEN_PLEREN_OFF (7u)

/** \brief  Length for Ifx_HSCT_IRQEN_Bits.PYEREN */
#define IFX_HSCT_IRQEN_PYEREN_LEN (1u)

/** \brief  Mask for Ifx_HSCT_IRQEN_Bits.PYEREN */
#define IFX_HSCT_IRQEN_PYEREN_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_IRQEN_Bits.PYEREN */
#define IFX_HSCT_IRQEN_PYEREN_OFF (2u)

/** \brief  Length for Ifx_HSCT_IRQEN_Bits.SFOEN */
#define IFX_HSCT_IRQEN_SFOEN_LEN (1u)

/** \brief  Mask for Ifx_HSCT_IRQEN_Bits.SFOEN */
#define IFX_HSCT_IRQEN_SFOEN_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_IRQEN_Bits.SFOEN */
#define IFX_HSCT_IRQEN_SFOEN_OFF (11u)

/** \brief  Length for Ifx_HSCT_IRQEN_Bits.SFUEN */
#define IFX_HSCT_IRQEN_SFUEN_LEN (1u)

/** \brief  Mask for Ifx_HSCT_IRQEN_Bits.SFUEN */
#define IFX_HSCT_IRQEN_SFUEN_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_IRQEN_Bits.SFUEN */
#define IFX_HSCT_IRQEN_SFUEN_OFF (12u)

/** \brief  Length for Ifx_HSCT_IRQEN_Bits.SMEREN */
#define IFX_HSCT_IRQEN_SMEREN_LEN (1u)

/** \brief  Mask for Ifx_HSCT_IRQEN_Bits.SMEREN */
#define IFX_HSCT_IRQEN_SMEREN_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_IRQEN_Bits.SMEREN */
#define IFX_HSCT_IRQEN_SMEREN_OFF (5u)

/** \brief  Length for Ifx_HSCT_IRQEN_Bits.TXTEEN */
#define IFX_HSCT_IRQEN_TXTEEN_LEN (1u)

/** \brief  Mask for Ifx_HSCT_IRQEN_Bits.TXTEEN */
#define IFX_HSCT_IRQEN_TXTEEN_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_IRQEN_Bits.TXTEEN */
#define IFX_HSCT_IRQEN_TXTEEN_OFF (10u)

/** \brief  Length for Ifx_HSCT_IRQEN_Bits.USMEN */
#define IFX_HSCT_IRQEN_USMEN_LEN (1u)

/** \brief  Mask for Ifx_HSCT_IRQEN_Bits.USMEN */
#define IFX_HSCT_IRQEN_USMEN_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_IRQEN_Bits.USMEN */
#define IFX_HSCT_IRQEN_USMEN_OFF (8u)

/** \brief  Length for Ifx_HSCT_IRQEN_Bits.USMSFEN */
#define IFX_HSCT_IRQEN_USMSFEN_LEN (1u)

/** \brief  Mask for Ifx_HSCT_IRQEN_Bits.USMSFEN */
#define IFX_HSCT_IRQEN_USMSFEN_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_IRQEN_Bits.USMSFEN */
#define IFX_HSCT_IRQEN_USMSFEN_OFF (6u)

/** \brief  Length for Ifx_HSCT_KRST0_Bits.RST */
#define IFX_HSCT_KRST0_RST_LEN (1u)

/** \brief  Mask for Ifx_HSCT_KRST0_Bits.RST */
#define IFX_HSCT_KRST0_RST_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_KRST0_Bits.RST */
#define IFX_HSCT_KRST0_RST_OFF (0u)

/** \brief  Length for Ifx_HSCT_KRST0_Bits.RSTSTAT */
#define IFX_HSCT_KRST0_RSTSTAT_LEN (1u)

/** \brief  Mask for Ifx_HSCT_KRST0_Bits.RSTSTAT */
#define IFX_HSCT_KRST0_RSTSTAT_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_KRST0_Bits.RSTSTAT */
#define IFX_HSCT_KRST0_RSTSTAT_OFF (1u)

/** \brief  Length for Ifx_HSCT_KRST1_Bits.RST */
#define IFX_HSCT_KRST1_RST_LEN (1u)

/** \brief  Mask for Ifx_HSCT_KRST1_Bits.RST */
#define IFX_HSCT_KRST1_RST_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_KRST1_Bits.RST */
#define IFX_HSCT_KRST1_RST_OFF (0u)

/** \brief  Length for Ifx_HSCT_KRSTCLR_Bits.CLR */
#define IFX_HSCT_KRSTCLR_CLR_LEN (1u)

/** \brief  Mask for Ifx_HSCT_KRSTCLR_Bits.CLR */
#define IFX_HSCT_KRSTCLR_CLR_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_KRSTCLR_Bits.CLR */
#define IFX_HSCT_KRSTCLR_CLR_OFF (0u)

/** \brief  Length for Ifx_HSCT_OCS_Bits.SUS */
#define IFX_HSCT_OCS_SUS_LEN (4u)

/** \brief  Mask for Ifx_HSCT_OCS_Bits.SUS */
#define IFX_HSCT_OCS_SUS_MSK (0xfu)

/** \brief  Offset for Ifx_HSCT_OCS_Bits.SUS */
#define IFX_HSCT_OCS_SUS_OFF (24u)

/** \brief  Length for Ifx_HSCT_OCS_Bits.SUS_P */
#define IFX_HSCT_OCS_SUS_P_LEN (1u)

/** \brief  Mask for Ifx_HSCT_OCS_Bits.SUS_P */
#define IFX_HSCT_OCS_SUS_P_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_OCS_Bits.SUS_P */
#define IFX_HSCT_OCS_SUS_P_OFF (28u)

/** \brief  Length for Ifx_HSCT_OCS_Bits.SUSSTA */
#define IFX_HSCT_OCS_SUSSTA_LEN (1u)

/** \brief  Mask for Ifx_HSCT_OCS_Bits.SUSSTA */
#define IFX_HSCT_OCS_SUSSTA_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_OCS_Bits.SUSSTA */
#define IFX_HSCT_OCS_SUSSTA_OFF (29u)

/** \brief  Length for Ifx_HSCT_OCS_Bits.TG_P */
#define IFX_HSCT_OCS_TG_P_LEN (1u)

/** \brief  Mask for Ifx_HSCT_OCS_Bits.TG_P */
#define IFX_HSCT_OCS_TG_P_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_OCS_Bits.TG_P */
#define IFX_HSCT_OCS_TG_P_OFF (3u)

/** \brief  Length for Ifx_HSCT_OCS_Bits.TGB */
#define IFX_HSCT_OCS_TGB_LEN (1u)

/** \brief  Mask for Ifx_HSCT_OCS_Bits.TGB */
#define IFX_HSCT_OCS_TGB_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_OCS_Bits.TGB */
#define IFX_HSCT_OCS_TGB_OFF (2u)

/** \brief  Length for Ifx_HSCT_OCS_Bits.TGS */
#define IFX_HSCT_OCS_TGS_LEN (2u)

/** \brief  Mask for Ifx_HSCT_OCS_Bits.TGS */
#define IFX_HSCT_OCS_TGS_MSK (0x3u)

/** \brief  Offset for Ifx_HSCT_OCS_Bits.TGS */
#define IFX_HSCT_OCS_TGS_OFF (0u)

/** \brief  Length for Ifx_HSCT_SLEEPCTRL_Bits.SLPCLKG */
#define IFX_HSCT_SLEEPCTRL_SLPCLKG_LEN (1u)

/** \brief  Mask for Ifx_HSCT_SLEEPCTRL_Bits.SLPCLKG */
#define IFX_HSCT_SLEEPCTRL_SLPCLKG_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_SLEEPCTRL_Bits.SLPCLKG */
#define IFX_HSCT_SLEEPCTRL_SLPCLKG_OFF (1u)

/** \brief  Length for Ifx_HSCT_SLEEPCTRL_Bits.SLPEN */
#define IFX_HSCT_SLEEPCTRL_SLPEN_LEN (1u)

/** \brief  Mask for Ifx_HSCT_SLEEPCTRL_Bits.SLPEN */
#define IFX_HSCT_SLEEPCTRL_SLPEN_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_SLEEPCTRL_Bits.SLPEN */
#define IFX_HSCT_SLEEPCTRL_SLPEN_OFF (0u)

/** \brief  Length for Ifx_HSCT_STAT_Bits.LIFCCMDR */
#define IFX_HSCT_STAT_LIFCCMDR_LEN (8u)

/** \brief  Mask for Ifx_HSCT_STAT_Bits.LIFCCMDR */
#define IFX_HSCT_STAT_LIFCCMDR_MSK (0xffu)

/** \brief  Offset for Ifx_HSCT_STAT_Bits.LIFCCMDR */
#define IFX_HSCT_STAT_LIFCCMDR_OFF (24u)

/** \brief  Length for Ifx_HSCT_STAT_Bits.RX_CHANNEL */
#define IFX_HSCT_STAT_RX_CHANNEL_LEN (4u)

/** \brief  Mask for Ifx_HSCT_STAT_Bits.RX_CHANNEL */
#define IFX_HSCT_STAT_RX_CHANNEL_MSK (0xfu)

/** \brief  Offset for Ifx_HSCT_STAT_Bits.RX_CHANNEL */
#define IFX_HSCT_STAT_RX_CHANNEL_OFF (3u)

/** \brief  Length for Ifx_HSCT_STAT_Bits.RX_PSIZE */
#define IFX_HSCT_STAT_RX_PSIZE_LEN (3u)

/** \brief  Mask for Ifx_HSCT_STAT_Bits.RX_PSIZE */
#define IFX_HSCT_STAT_RX_PSIZE_MSK (0x7u)

/** \brief  Offset for Ifx_HSCT_STAT_Bits.RX_PSIZE */
#define IFX_HSCT_STAT_RX_PSIZE_OFF (0u)

/** \brief  Length for Ifx_HSCT_STAT_Bits.RX_SLEEP */
#define IFX_HSCT_STAT_RX_SLEEP_LEN (1u)

/** \brief  Mask for Ifx_HSCT_STAT_Bits.RX_SLEEP */
#define IFX_HSCT_STAT_RX_SLEEP_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_STAT_Bits.RX_SLEEP */
#define IFX_HSCT_STAT_RX_SLEEP_OFF (7u)

/** \brief  Length for Ifx_HSCT_STAT_Bits.TX_CHANNEL_TYPE */
#define IFX_HSCT_STAT_TX_CHANNEL_TYPE_LEN (4u)

/** \brief  Mask for Ifx_HSCT_STAT_Bits.TX_CHANNEL_TYPE */
#define IFX_HSCT_STAT_TX_CHANNEL_TYPE_MSK (0xfu)

/** \brief  Offset for Ifx_HSCT_STAT_Bits.TX_CHANNEL_TYPE */
#define IFX_HSCT_STAT_TX_CHANNEL_TYPE_OFF (16u)

/** \brief  Length for Ifx_HSCT_STAT_Bits.TX_PSIZE */
#define IFX_HSCT_STAT_TX_PSIZE_LEN (3u)

/** \brief  Mask for Ifx_HSCT_STAT_Bits.TX_PSIZE */
#define IFX_HSCT_STAT_TX_PSIZE_MSK (0x7u)

/** \brief  Offset for Ifx_HSCT_STAT_Bits.TX_PSIZE */
#define IFX_HSCT_STAT_TX_PSIZE_OFF (12u)

/** \brief  Length for Ifx_HSCT_STAT_Bits.TX_SLEEP */
#define IFX_HSCT_STAT_TX_SLEEP_LEN (1u)

/** \brief  Mask for Ifx_HSCT_STAT_Bits.TX_SLEEP */
#define IFX_HSCT_STAT_TX_SLEEP_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_STAT_Bits.TX_SLEEP */
#define IFX_HSCT_STAT_TX_SLEEP_OFF (8u)

/** \brief  Length for Ifx_HSCT_STATPHY_Bits.PLOCK */
#define IFX_HSCT_STATPHY_PLOCK_LEN (1u)

/** \brief  Mask for Ifx_HSCT_STATPHY_Bits.PLOCK */
#define IFX_HSCT_STATPHY_PLOCK_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_STATPHY_Bits.PLOCK */
#define IFX_HSCT_STATPHY_PLOCK_OFF (0u)

/** \brief  Length for Ifx_HSCT_STATPHY_Bits.RXLSA */
#define IFX_HSCT_STATPHY_RXLSA_LEN (1u)

/** \brief  Mask for Ifx_HSCT_STATPHY_Bits.RXLSA */
#define IFX_HSCT_STATPHY_RXLSA_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_STATPHY_Bits.RXLSA */
#define IFX_HSCT_STATPHY_RXLSA_OFF (1u)

/** \brief  Length for Ifx_HSCT_STATPHY_Bits.TXLSA */
#define IFX_HSCT_STATPHY_TXLSA_LEN (1u)

/** \brief  Mask for Ifx_HSCT_STATPHY_Bits.TXLSA */
#define IFX_HSCT_STATPHY_TXLSA_MSK (0x1u)

/** \brief  Offset for Ifx_HSCT_STATPHY_Bits.TXLSA */
#define IFX_HSCT_STATPHY_TXLSA_OFF (2u)

/** \brief  Length for Ifx_HSCT_USMR_Bits.USMR */
#define IFX_HSCT_USMR_USMR_LEN (32u)

/** \brief  Mask for Ifx_HSCT_USMR_Bits.USMR */
#define IFX_HSCT_USMR_USMR_MSK (0xffffffffu)

/** \brief  Offset for Ifx_HSCT_USMR_Bits.USMR */
#define IFX_HSCT_USMR_USMR_OFF (0u)

/** \brief  Length for Ifx_HSCT_USMS_Bits.USMS */
#define IFX_HSCT_USMS_USMS_LEN (32u)

/** \brief  Mask for Ifx_HSCT_USMS_Bits.USMS */
#define IFX_HSCT_USMS_USMS_MSK (0xffffffffu)

/** \brief  Offset for Ifx_HSCT_USMS_Bits.USMS */
#define IFX_HSCT_USMS_USMS_OFF (0u)
/** \}  */
/******************************************************************************/
/******************************************************************************/
#endif /* IFXHSCT_BF_H */
