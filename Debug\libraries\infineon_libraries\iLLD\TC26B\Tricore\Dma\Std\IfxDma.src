	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc18300a --dep-file=IfxDma.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/infineon_libraries/iLLD/TC26B/Tricore/Dma/Std/IfxDma.src ../libraries/infineon_libraries/iLLD/TC26B/Tricore/Dma/Std/IfxDma.c"
	.compiler_name		"ctc"
	;source	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Dma/Std/IfxDma.c'

	
$TC16X
	
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L3:
	.word	94800
	.half	3
	.word	.L4
	.byte	4
.L2:
	.byte	1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Dma/Std/IfxDma.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L5
	.byte	2,1,1,3
	.word	230
	.byte	4
	.byte	'Ifx__jump_and_link',0,3,1,61,17,1,1,5
	.byte	'fun',0,1,61,43
	.word	233
	.byte	6,0,7
	.byte	'__fract',0,4,128,1,7
	.byte	'float',0,4,4,8
	.byte	'Ifx__float_to_fract',0,3,1,152,2,18
	.word	278
	.byte	1,1,5
	.byte	'a',0,1,152,2,44
	.word	290
	.byte	6,0,4
	.byte	'Ifx__stopPerfCounters',0,3,1,172,2,17,1,1,6,0,7
	.byte	'unsigned long long int',0,8,7,9
	.byte	'void',0,3
	.word	402
	.byte	8
	.byte	'__ld64',0,3,2,135,1,19
	.word	376
	.byte	1,1,5
	.byte	'addr',0,2,135,1,32
	.word	408
	.byte	6,0,4
	.byte	'__st64',0,3,2,143,1,17,1,1,5
	.byte	'addr',0,2,143,1,30
	.word	408
	.byte	5
	.byte	'value',0,2,143,1,43
	.word	376
	.byte	6,0,7
	.byte	'unsigned int',0,4,7,7
	.byte	'int',0,4,5,7
	.byte	'unsigned char',0,1,8,10
	.byte	'_Ifx_SRC_SRCR_Bits',0,4,45,16,4,11
	.byte	'SRPN',0,1
	.word	517
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	517
	.byte	2,6,2,35,1,11
	.byte	'SRE',0,1
	.word	517
	.byte	1,5,2,35,1,11
	.byte	'TOS',0,1
	.word	517
	.byte	2,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	517
	.byte	3,0,2,35,1,11
	.byte	'ECC',0,1
	.word	517
	.byte	6,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	517
	.byte	2,0,2,35,2,11
	.byte	'SRR',0,1
	.word	517
	.byte	1,7,2,35,3,11
	.byte	'CLRR',0,1
	.word	517
	.byte	1,6,2,35,3,11
	.byte	'SETR',0,1
	.word	517
	.byte	1,5,2,35,3,11
	.byte	'IOV',0,1
	.word	517
	.byte	1,4,2,35,3,11
	.byte	'IOVCLR',0,1
	.word	517
	.byte	1,3,2,35,3,11
	.byte	'SWS',0,1
	.word	517
	.byte	1,2,2,35,3,11
	.byte	'SWSCLR',0,1
	.word	517
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	517
	.byte	1,0,2,35,3,0,12,4,70,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	534
	.byte	4,2,35,0,0,14
	.word	824
	.byte	3
	.word	863
	.byte	4
	.byte	'IfxSrc_clearRequest',0,3,3,250,1,17,1,1,5
	.byte	'src',0,3,250,1,60
	.word	868
	.byte	6,0,7
	.byte	'unsigned int',0,4,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON0_Bits',0,6,241,8,16,4,11
	.byte	'ENDINIT',0,4
	.word	916
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	916
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	916
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	916
	.byte	16,0,2,35,0,0,12,6,247,14,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	932
	.byte	4,2,35,0,0,7
	.byte	'unsigned short int',0,2,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON1_Bits',0,6,250,8,16,4,11
	.byte	'reserved_0',0,1
	.word	517
	.byte	2,6,2,35,0,11
	.byte	'IR0',0,1
	.word	517
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	517
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	517
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	517
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	517
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	517
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	517
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	517
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1068
	.byte	16,0,2,35,2,0,12,6,255,14,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1090
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_WDTCPU_SR_Bits',0,6,137,9,16,4,11
	.byte	'AE',0,1
	.word	517
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	517
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	517
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	517
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	517
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	517
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	517
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	517
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	517
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	517
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	1068
	.byte	16,0,2,35,2,0,12,6,135,15,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1334
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_WDTCPU',0,6,175,15,25,12,13
	.byte	'CON0',0
	.word	1028
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	1294
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	1525
	.byte	4,2,35,8,0,14
	.word	1565
	.byte	3
	.word	1628
	.byte	4
	.byte	'IfxScuWdt_clearCpuEndinitInline',0,3,5,181,3,17,1,1,5
	.byte	'watchdog',0,5,181,3,65
	.word	1633
	.byte	5
	.byte	'password',0,5,181,3,82
	.word	1068
	.byte	6,0,4
	.byte	'IfxScuWdt_clearSafetyEndinitInline',0,3,5,204,3,17,1,1,5
	.byte	'password',0,5,204,3,59
	.word	1068
	.byte	6,0,4
	.byte	'IfxScuWdt_setCpuEndinitInline',0,3,5,140,4,17,1,1,5
	.byte	'watchdog',0,5,140,4,63
	.word	1633
	.byte	5
	.byte	'password',0,5,140,4,80
	.word	1068
	.byte	6,0,4
	.byte	'IfxScuWdt_setSafetyEndinitInline',0,3,5,163,4,17,1,1,5
	.byte	'password',0,5,163,4,57
	.word	1068
	.byte	6,0,8
	.byte	'IfxScuWdt_getCpuWatchdogPasswordInline',0,3,5,227,3,19
	.word	1068
	.byte	1,1,5
	.byte	'watchdog',0,5,227,3,74
	.word	1633
	.byte	6,0,8
	.byte	'IfxScuWdt_getSafetyWatchdogPasswordInline',0,3,5,253,3,19
	.word	1068
	.byte	1,1,6,0,15,8,156,1,9,1,16
	.byte	'IfxCpu_ResourceCpu_0',0,0,16
	.byte	'IfxCpu_ResourceCpu_1',0,1,16
	.byte	'IfxCpu_ResourceCpu_none',0,2,0,8
	.byte	'IfxCpu_getCoreIndex',0,3,7,141,6,31
	.word	2043
	.byte	1,1,6,0,8
	.byte	'IfxCpu_areInterruptsEnabled',0,3,7,139,5,20
	.word	517
	.byte	1,1,6,0,7
	.byte	'unsigned long int',0,4,7,8
	.byte	'IfxCpu_getPerformanceCounter',0,3,7,161,6,19
	.word	2198
	.byte	1,1,5
	.byte	'address',0,7,161,6,55
	.word	1068
	.byte	6,0,8
	.byte	'IfxCpu_getPerformanceCounterStickyOverflow',0,3,7,190,6,20
	.word	517
	.byte	1,1,5
	.byte	'address',0,7,190,6,70
	.word	1068
	.byte	6,0,4
	.byte	'IfxCpu_updatePerformanceCounter',0,3,7,172,8,17,1,1,5
	.byte	'address',0,7,172,8,56
	.word	2198
	.byte	5
	.byte	'count',0,7,172,8,72
	.word	2198
	.byte	17,6,0,0,10
	.byte	'_Ifx_P_OUT_Bits',0,10,143,3,16,4,11
	.byte	'P0',0,1
	.word	517
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	517
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	517
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	517
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	517
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	517
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	517
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	517
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	517
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	517
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	517
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	517
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	517
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	517
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	517
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	517
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1068
	.byte	16,0,2,35,2,0,12,10,181,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2429
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMR_Bits',0,10,169,2,16,4,11
	.byte	'PS0',0,1
	.word	517
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	517
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	517
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	517
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	517
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	517
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	517
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	517
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	517
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	517
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	517
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	517
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	517
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	517
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	517
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	517
	.byte	1,0,2,35,1,11
	.byte	'PCL0',0,1
	.word	517
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	517
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	517
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	517
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	517
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	517
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	517
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	517
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	517
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	517
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	517
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	517
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	517
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	517
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	517
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	517
	.byte	1,0,2,35,3,0,12,10,133,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2745
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ID_Bits',0,10,110,16,4,11
	.byte	'MODREV',0,1
	.word	517
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	517
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	1068
	.byte	16,0,2,35,2,0,12,10,148,4,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3316
	.byte	4,2,35,0,0,18,4
	.word	517
	.byte	19,3,0,10
	.byte	'_Ifx_P_IOCR0_Bits',0,10,140,1,16,4,11
	.byte	'reserved_0',0,1
	.word	517
	.byte	3,5,2,35,0,11
	.byte	'PC0',0,1
	.word	517
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	517
	.byte	3,5,2,35,1,11
	.byte	'PC1',0,1
	.word	517
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	517
	.byte	3,5,2,35,2,11
	.byte	'PC2',0,1
	.word	517
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	517
	.byte	3,5,2,35,3,11
	.byte	'PC3',0,1
	.word	517
	.byte	5,0,2,35,3,0,12,10,164,4,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3444
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR4_Bits',0,10,166,1,16,4,11
	.byte	'reserved_0',0,1
	.word	517
	.byte	3,5,2,35,0,11
	.byte	'PC4',0,1
	.word	517
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	517
	.byte	3,5,2,35,1,11
	.byte	'PC5',0,1
	.word	517
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	517
	.byte	3,5,2,35,2,11
	.byte	'PC6',0,1
	.word	517
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	517
	.byte	3,5,2,35,3,11
	.byte	'PC7',0,1
	.word	517
	.byte	5,0,2,35,3,0,12,10,180,4,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3659
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR8_Bits',0,10,179,1,16,4,11
	.byte	'reserved_0',0,1
	.word	517
	.byte	3,5,2,35,0,11
	.byte	'PC8',0,1
	.word	517
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	517
	.byte	3,5,2,35,1,11
	.byte	'PC9',0,1
	.word	517
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	517
	.byte	3,5,2,35,2,11
	.byte	'PC10',0,1
	.word	517
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	517
	.byte	3,5,2,35,3,11
	.byte	'PC11',0,1
	.word	517
	.byte	5,0,2,35,3,0,12,10,188,4,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3874
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR12_Bits',0,10,153,1,16,4,11
	.byte	'reserved_0',0,1
	.word	517
	.byte	3,5,2,35,0,11
	.byte	'PC12',0,1
	.word	517
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	517
	.byte	3,5,2,35,1,11
	.byte	'PC13',0,1
	.word	517
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	517
	.byte	3,5,2,35,2,11
	.byte	'PC14',0,1
	.word	517
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	517
	.byte	3,5,2,35,3,11
	.byte	'PC15',0,1
	.word	517
	.byte	5,0,2,35,3,0,12,10,172,4,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4091
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IN_Bits',0,10,118,16,4,11
	.byte	'P0',0,1
	.word	517
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	517
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	517
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	517
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	517
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	517
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	517
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	517
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	517
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	517
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	517
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	517
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	517
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	517
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	517
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	517
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1068
	.byte	16,0,2,35,2,0,12,10,156,4,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4311
	.byte	4,2,35,0,0,18,24
	.word	517
	.byte	19,23,0,10
	.byte	'_Ifx_P_PDR0_Bits',0,10,205,3,16,4,11
	.byte	'PD0',0,1
	.word	517
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	517
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	517
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	517
	.byte	1,0,2,35,0,11
	.byte	'PD2',0,1
	.word	517
	.byte	3,5,2,35,1,11
	.byte	'PL2',0,1
	.word	517
	.byte	1,4,2,35,1,11
	.byte	'PD3',0,1
	.word	517
	.byte	3,1,2,35,1,11
	.byte	'PL3',0,1
	.word	517
	.byte	1,0,2,35,1,11
	.byte	'PD4',0,1
	.word	517
	.byte	3,5,2,35,2,11
	.byte	'PL4',0,1
	.word	517
	.byte	1,4,2,35,2,11
	.byte	'PD5',0,1
	.word	517
	.byte	3,1,2,35,2,11
	.byte	'PL5',0,1
	.word	517
	.byte	1,0,2,35,2,11
	.byte	'PD6',0,1
	.word	517
	.byte	3,5,2,35,3,11
	.byte	'PL6',0,1
	.word	517
	.byte	1,4,2,35,3,11
	.byte	'PD7',0,1
	.word	517
	.byte	3,1,2,35,3,11
	.byte	'PL7',0,1
	.word	517
	.byte	1,0,2,35,3,0,12,10,205,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4634
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PDR1_Bits',0,10,226,3,16,4,11
	.byte	'PD8',0,1
	.word	517
	.byte	3,5,2,35,0,11
	.byte	'PL8',0,1
	.word	517
	.byte	1,4,2,35,0,11
	.byte	'PD9',0,1
	.word	517
	.byte	3,1,2,35,0,11
	.byte	'PL9',0,1
	.word	517
	.byte	1,0,2,35,0,11
	.byte	'PD10',0,1
	.word	517
	.byte	3,5,2,35,1,11
	.byte	'PL10',0,1
	.word	517
	.byte	1,4,2,35,1,11
	.byte	'PD11',0,1
	.word	517
	.byte	3,1,2,35,1,11
	.byte	'PL11',0,1
	.word	517
	.byte	1,0,2,35,1,11
	.byte	'PD12',0,1
	.word	517
	.byte	3,5,2,35,2,11
	.byte	'PL12',0,1
	.word	517
	.byte	1,4,2,35,2,11
	.byte	'PD13',0,1
	.word	517
	.byte	3,1,2,35,2,11
	.byte	'PL13',0,1
	.word	517
	.byte	1,0,2,35,2,11
	.byte	'PD14',0,1
	.word	517
	.byte	3,5,2,35,3,11
	.byte	'PL14',0,1
	.word	517
	.byte	1,4,2,35,3,11
	.byte	'PD15',0,1
	.word	517
	.byte	3,1,2,35,3,11
	.byte	'PL15',0,1
	.word	517
	.byte	1,0,2,35,3,0,12,10,213,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4938
	.byte	4,2,35,0,0,18,8
	.word	517
	.byte	19,7,0,10
	.byte	'_Ifx_P_ESR_Bits',0,10,88,16,4,11
	.byte	'EN0',0,1
	.word	517
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	517
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	517
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	517
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	517
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	517
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	517
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	517
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	517
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	517
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	517
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	517
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	517
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	517
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	517
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	517
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1068
	.byte	16,0,2,35,2,0,12,10,140,4,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5263
	.byte	4,2,35,0,0,18,12
	.word	517
	.byte	19,11,0,10
	.byte	'_Ifx_P_PDISC_Bits',0,10,183,3,16,4,11
	.byte	'PDIS0',0,1
	.word	517
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	517
	.byte	1,6,2,35,0,11
	.byte	'PDIS2',0,1
	.word	517
	.byte	1,5,2,35,0,11
	.byte	'PDIS3',0,1
	.word	517
	.byte	1,4,2,35,0,11
	.byte	'PDIS4',0,1
	.word	517
	.byte	1,3,2,35,0,11
	.byte	'PDIS5',0,1
	.word	517
	.byte	1,2,2,35,0,11
	.byte	'PDIS6',0,1
	.word	517
	.byte	1,1,2,35,0,11
	.byte	'PDIS7',0,1
	.word	517
	.byte	1,0,2,35,0,11
	.byte	'PDIS8',0,1
	.word	517
	.byte	1,7,2,35,1,11
	.byte	'PDIS9',0,1
	.word	517
	.byte	1,6,2,35,1,11
	.byte	'PDIS10',0,1
	.word	517
	.byte	1,5,2,35,1,11
	.byte	'PDIS11',0,1
	.word	517
	.byte	1,4,2,35,1,11
	.byte	'PDIS12',0,1
	.word	517
	.byte	1,3,2,35,1,11
	.byte	'PDIS13',0,1
	.word	517
	.byte	1,2,2,35,1,11
	.byte	'PDIS14',0,1
	.word	517
	.byte	1,1,2,35,1,11
	.byte	'PDIS15',0,1
	.word	517
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1068
	.byte	16,0,2,35,2,0,12,10,197,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5603
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PCSR_Bits',0,10,165,3,16,4,11
	.byte	'SEL0',0,1
	.word	517
	.byte	1,7,2,35,0,11
	.byte	'SEL1',0,1
	.word	517
	.byte	1,6,2,35,0,11
	.byte	'SEL2',0,1
	.word	517
	.byte	1,5,2,35,0,11
	.byte	'SEL3',0,1
	.word	517
	.byte	1,4,2,35,0,11
	.byte	'SEL4',0,1
	.word	517
	.byte	1,3,2,35,0,11
	.byte	'SEL5',0,1
	.word	517
	.byte	1,2,2,35,0,11
	.byte	'SEL6',0,1
	.word	517
	.byte	1,1,2,35,0,11
	.byte	'SEL7',0,1
	.word	517
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	517
	.byte	2,6,2,35,1,11
	.byte	'SEL10',0,1
	.word	517
	.byte	1,5,2,35,1,11
	.byte	'SEL11',0,1
	.word	517
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	494
	.byte	19,1,2,35,0,11
	.byte	'LCK',0,1
	.word	517
	.byte	1,0,2,35,3,0,12,10,189,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5969
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR0_Bits',0,10,206,2,16,4,11
	.byte	'PS0',0,1
	.word	517
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	517
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	517
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	517
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	494
	.byte	28,0,2,35,0,0,12,10,149,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6255
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR4_Bits',0,10,227,2,16,4,11
	.byte	'reserved_0',0,1
	.word	517
	.byte	4,4,2,35,0,11
	.byte	'PS4',0,1
	.word	517
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	517
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	517
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	517
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	494
	.byte	24,0,2,35,0,0,12,10,165,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6402
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR8_Bits',0,10,238,2,16,4,11
	.byte	'reserved_0',0,1
	.word	517
	.byte	8,0,2,35,0,11
	.byte	'PS8',0,1
	.word	517
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	517
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	517
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	517
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	494
	.byte	20,0,2,35,0,0,12,10,173,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6571
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR12_Bits',0,10,216,2,16,4,11
	.byte	'reserved_0',0,2
	.word	1068
	.byte	12,4,2,35,0,11
	.byte	'PS12',0,1
	.word	517
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	517
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	517
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	517
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1068
	.byte	16,0,2,35,2,0,12,10,157,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6743
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR0_Bits',0,10,232,1,16,4,11
	.byte	'reserved_0',0,2
	.word	1068
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	517
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	517
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	517
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	517
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	1068
	.byte	12,0,2,35,2,0,12,10,229,4,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6918
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR4_Bits',0,10,253,1,16,4,11
	.byte	'reserved_0',0,4
	.word	494
	.byte	20,12,2,35,0,11
	.byte	'PCL4',0,1
	.word	517
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	517
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	517
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	517
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	517
	.byte	8,0,2,35,3,0,12,10,245,4,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7092
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR8_Bits',0,10,136,2,16,4,11
	.byte	'reserved_0',0,4
	.word	494
	.byte	24,8,2,35,0,11
	.byte	'PCL8',0,1
	.word	517
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	517
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	517
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	517
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	517
	.byte	4,0,2,35,3,0,12,10,253,4,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7266
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR12_Bits',0,10,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	494
	.byte	28,4,2,35,0,11
	.byte	'PCL12',0,1
	.word	517
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	517
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	517
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	517
	.byte	1,0,2,35,3,0,12,10,237,4,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7442
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR_Bits',0,10,249,2,16,4,11
	.byte	'PS0',0,1
	.word	517
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	517
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	517
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	517
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	517
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	517
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	517
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	517
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	517
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	517
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	517
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	517
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	517
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	517
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	517
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	517
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1068
	.byte	16,0,2,35,2,0,12,10,141,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7598
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR_Bits',0,10,147,2,16,4,11
	.byte	'reserved_0',0,2
	.word	1068
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	517
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	517
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	517
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	517
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	517
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	517
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	517
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	517
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	517
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	517
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	517
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	517
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	517
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	517
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	517
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	517
	.byte	1,0,2,35,3,0,12,10,221,4,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7931
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR0_Bits',0,10,192,1,16,4,11
	.byte	'reserved_0',0,1
	.word	517
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	517
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	494
	.byte	30,0,2,35,0,0,12,10,196,4,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8279
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_Bits',0,10,200,1,16,4,11
	.byte	'reserved_0',0,1
	.word	517
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	517
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	494
	.byte	30,0,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_P21_Bits',0,10,208,1,16,4,11
	.byte	'RDIS_CTRL',0,1
	.word	517
	.byte	1,7,2,35,0,11
	.byte	'RX_DIS',0,1
	.word	517
	.byte	1,6,2,35,0,11
	.byte	'TERM',0,1
	.word	517
	.byte	1,5,2,35,0,11
	.byte	'LRXTERM',0,1
	.word	517
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	494
	.byte	24,0,2,35,0,0,12,10,204,4,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8403
	.byte	4,2,35,0,13
	.byte	'B_P21',0
	.word	8487
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR2_Bits',0,10,218,1,16,4,11
	.byte	'reserved_0',0,1
	.word	517
	.byte	8,0,2,35,0,11
	.byte	'LVDSR',0,1
	.word	517
	.byte	1,7,2,35,1,11
	.byte	'LVDSRL',0,1
	.word	517
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	517
	.byte	2,4,2,35,1,11
	.byte	'TDIS_CTRL',0,1
	.word	517
	.byte	1,3,2,35,1,11
	.byte	'TX_DIS',0,1
	.word	517
	.byte	1,2,2,35,1,11
	.byte	'TX_PD',0,1
	.word	517
	.byte	1,1,2,35,1,11
	.byte	'TX_PWDPD',0,1
	.word	517
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1068
	.byte	16,0,2,35,2,0,12,10,213,4,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8667
	.byte	4,2,35,0,0,18,76
	.word	517
	.byte	19,75,0,10
	.byte	'_Ifx_P_ACCEN1_Bits',0,10,82,16,4,11
	.byte	'reserved_0',0,4
	.word	494
	.byte	32,0,2,35,0,0,12,10,132,4,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8920
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ACCEN0_Bits',0,10,45,16,4,11
	.byte	'EN0',0,1
	.word	517
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	517
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	517
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	517
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	517
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	517
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	517
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	517
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	517
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	517
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	517
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	517
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	517
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	517
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	517
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	517
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	517
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	517
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	517
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	517
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	517
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	517
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	517
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	517
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	517
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	517
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	517
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	517
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	517
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	517
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	517
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	517
	.byte	1,0,2,35,3,0,12,10,252,3,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9007
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P',0,10,229,5,25,128,2,13
	.byte	'OUT',0
	.word	2705
	.byte	4,2,35,0,13
	.byte	'OMR',0
	.word	3276
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	3395
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	3435
	.byte	4,2,35,12,13
	.byte	'IOCR0',0
	.word	3619
	.byte	4,2,35,16,13
	.byte	'IOCR4',0
	.word	3834
	.byte	4,2,35,20,13
	.byte	'IOCR8',0
	.word	4051
	.byte	4,2,35,24,13
	.byte	'IOCR12',0
	.word	4271
	.byte	4,2,35,28,13
	.byte	'reserved_20',0
	.word	3435
	.byte	4,2,35,32,13
	.byte	'IN',0
	.word	4585
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	4625
	.byte	24,2,35,40,13
	.byte	'PDR0',0
	.word	4898
	.byte	4,2,35,64,13
	.byte	'PDR1',0
	.word	5214
	.byte	4,2,35,68,13
	.byte	'reserved_48',0
	.word	5254
	.byte	8,2,35,72,13
	.byte	'ESR',0
	.word	5554
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	5594
	.byte	12,2,35,84,13
	.byte	'PDISC',0
	.word	5929
	.byte	4,2,35,96,13
	.byte	'PCSR',0
	.word	6215
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	5254
	.byte	8,2,35,104,13
	.byte	'OMSR0',0
	.word	6362
	.byte	4,2,35,112,13
	.byte	'OMSR4',0
	.word	6531
	.byte	4,2,35,116,13
	.byte	'OMSR8',0
	.word	6703
	.byte	4,2,35,120,13
	.byte	'OMSR12',0
	.word	6878
	.byte	4,2,35,124,13
	.byte	'OMCR0',0
	.word	7052
	.byte	4,3,35,128,1,13
	.byte	'OMCR4',0
	.word	7226
	.byte	4,3,35,132,1,13
	.byte	'OMCR8',0
	.word	7402
	.byte	4,3,35,136,1,13
	.byte	'OMCR12',0
	.word	7558
	.byte	4,3,35,140,1,13
	.byte	'OMSR',0
	.word	7891
	.byte	4,3,35,144,1,13
	.byte	'OMCR',0
	.word	8239
	.byte	4,3,35,148,1,13
	.byte	'reserved_98',0
	.word	5254
	.byte	8,3,35,152,1,13
	.byte	'LPCR0',0
	.word	8363
	.byte	4,3,35,160,1,13
	.byte	'LPCR1',0
	.word	8612
	.byte	4,3,35,164,1,13
	.byte	'LPCR2',0
	.word	8871
	.byte	4,3,35,168,1,13
	.byte	'reserved_A4',0
	.word	8911
	.byte	76,3,35,172,1,13
	.byte	'ACCEN1',0
	.word	8967
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	9534
	.byte	4,3,35,252,1,0,14
	.word	9574
	.byte	3
	.word	10177
	.byte	15,9,172,1,9,4,16
	.byte	'IfxPort_State_notChanged',0,0,16
	.byte	'IfxPort_State_high',0,1,16
	.byte	'IfxPort_State_low',0,128,128,4,16
	.byte	'IfxPort_State_toggled',0,129,128,4,0,4
	.byte	'IfxPort_setPinState',0,3,9,208,4,17,1,1,5
	.byte	'port',0,9,208,4,44
	.word	10182
	.byte	5
	.byte	'pinIndex',0,9,208,4,56
	.word	517
	.byte	5
	.byte	'action',0,9,208,4,80
	.word	10187
	.byte	6,0,20
	.word	238
	.byte	21
	.word	264
	.byte	6,0,20
	.word	299
	.byte	21
	.word	331
	.byte	6,0,20
	.word	344
	.byte	6,0,20
	.word	413
	.byte	21
	.word	432
	.byte	6,0,20
	.word	448
	.byte	21
	.word	463
	.byte	21
	.word	477
	.byte	6,0,20
	.word	873
	.byte	21
	.word	901
	.byte	6,0,20
	.word	1638
	.byte	21
	.word	1678
	.byte	21
	.word	1696
	.byte	6,0,20
	.word	1716
	.byte	21
	.word	1759
	.byte	6,0,20
	.word	1779
	.byte	21
	.word	1817
	.byte	21
	.word	1835
	.byte	6,0,20
	.word	1855
	.byte	21
	.word	1896
	.byte	6,0,20
	.word	1916
	.byte	21
	.word	1967
	.byte	6,0,20
	.word	1987
	.byte	6,0,20
	.word	2122
	.byte	6,0,20
	.word	2156
	.byte	6,0,20
	.word	2219
	.byte	21
	.word	2260
	.byte	6,0,20
	.word	2279
	.byte	21
	.word	2334
	.byte	6,0,20
	.word	2353
	.byte	21
	.word	2393
	.byte	21
	.word	2410
	.byte	17,6,0,0,20
	.word	10290
	.byte	21
	.word	10318
	.byte	21
	.word	10332
	.byte	21
	.word	10350
	.byte	6,0,7
	.byte	'short int',0,2,5,22
	.byte	'__wchar_t',0,11,1,1
	.word	10596
	.byte	22
	.byte	'__size_t',0,11,1,1
	.word	494
	.byte	22
	.byte	'__ptrdiff_t',0,11,1,1
	.word	510
	.byte	23,1,3
	.word	10664
	.byte	22
	.byte	'__codeptr',0,11,1,1
	.word	10666
	.byte	22
	.byte	'boolean',0,12,101,29
	.word	517
	.byte	22
	.byte	'uint8',0,12,105,29
	.word	517
	.byte	22
	.byte	'uint16',0,12,109,29
	.word	1068
	.byte	22
	.byte	'uint32',0,12,113,29
	.word	2198
	.byte	22
	.byte	'uint64',0,12,118,29
	.word	376
	.byte	22
	.byte	'sint16',0,12,126,29
	.word	10596
	.byte	7
	.byte	'long int',0,4,5,22
	.byte	'sint32',0,12,131,1,29
	.word	10779
	.byte	7
	.byte	'long long int',0,8,5,22
	.byte	'sint64',0,12,138,1,29
	.word	10807
	.byte	22
	.byte	'float32',0,12,167,1,29
	.word	290
	.byte	22
	.byte	'pvoid',0,13,57,28
	.word	408
	.byte	22
	.byte	'Ifx_TickTime',0,13,79,28
	.word	10807
	.byte	22
	.byte	'Ifx_Priority',0,13,103,16
	.word	1068
	.byte	15,13,130,1,9,1,16
	.byte	'Ifx_RxSel_a',0,0,16
	.byte	'Ifx_RxSel_b',0,1,16
	.byte	'Ifx_RxSel_c',0,2,16
	.byte	'Ifx_RxSel_d',0,3,16
	.byte	'Ifx_RxSel_e',0,4,16
	.byte	'Ifx_RxSel_f',0,5,16
	.byte	'Ifx_RxSel_g',0,6,16
	.byte	'Ifx_RxSel_h',0,7,0,22
	.byte	'Ifx_RxSel',0,13,140,1,3
	.word	10913
	.byte	15,14,105,9,1,16
	.byte	'IfxDma_ChannelId_none',0,127,16
	.byte	'IfxDma_ChannelId_0',0,0,16
	.byte	'IfxDma_ChannelId_1',0,1,16
	.byte	'IfxDma_ChannelId_2',0,2,16
	.byte	'IfxDma_ChannelId_3',0,3,16
	.byte	'IfxDma_ChannelId_4',0,4,16
	.byte	'IfxDma_ChannelId_5',0,5,16
	.byte	'IfxDma_ChannelId_6',0,6,16
	.byte	'IfxDma_ChannelId_7',0,7,16
	.byte	'IfxDma_ChannelId_8',0,8,16
	.byte	'IfxDma_ChannelId_9',0,9,16
	.byte	'IfxDma_ChannelId_10',0,10,16
	.byte	'IfxDma_ChannelId_11',0,11,16
	.byte	'IfxDma_ChannelId_12',0,12,16
	.byte	'IfxDma_ChannelId_13',0,13,16
	.byte	'IfxDma_ChannelId_14',0,14,16
	.byte	'IfxDma_ChannelId_15',0,15,16
	.byte	'IfxDma_ChannelId_16',0,16,16
	.byte	'IfxDma_ChannelId_17',0,17,16
	.byte	'IfxDma_ChannelId_18',0,18,16
	.byte	'IfxDma_ChannelId_19',0,19,16
	.byte	'IfxDma_ChannelId_20',0,20,16
	.byte	'IfxDma_ChannelId_21',0,21,16
	.byte	'IfxDma_ChannelId_22',0,22,16
	.byte	'IfxDma_ChannelId_23',0,23,16
	.byte	'IfxDma_ChannelId_24',0,24,16
	.byte	'IfxDma_ChannelId_25',0,25,16
	.byte	'IfxDma_ChannelId_26',0,26,16
	.byte	'IfxDma_ChannelId_27',0,27,16
	.byte	'IfxDma_ChannelId_28',0,28,16
	.byte	'IfxDma_ChannelId_29',0,29,16
	.byte	'IfxDma_ChannelId_30',0,30,16
	.byte	'IfxDma_ChannelId_31',0,31,16
	.byte	'IfxDma_ChannelId_32',0,32,16
	.byte	'IfxDma_ChannelId_33',0,33,16
	.byte	'IfxDma_ChannelId_34',0,34,16
	.byte	'IfxDma_ChannelId_35',0,35,16
	.byte	'IfxDma_ChannelId_36',0,36,16
	.byte	'IfxDma_ChannelId_37',0,37,16
	.byte	'IfxDma_ChannelId_38',0,38,16
	.byte	'IfxDma_ChannelId_39',0,39,16
	.byte	'IfxDma_ChannelId_40',0,40,16
	.byte	'IfxDma_ChannelId_41',0,41,16
	.byte	'IfxDma_ChannelId_42',0,42,16
	.byte	'IfxDma_ChannelId_43',0,43,16
	.byte	'IfxDma_ChannelId_44',0,44,16
	.byte	'IfxDma_ChannelId_45',0,45,16
	.byte	'IfxDma_ChannelId_46',0,46,16
	.byte	'IfxDma_ChannelId_47',0,47,0,22
	.byte	'IfxDma_ChannelId',0,14,156,1,3
	.word	11051
	.byte	10
	.byte	'_Ifx_DMA_ACCEN00_Bits',0,15,45,16,4,11
	.byte	'EN0',0,1
	.word	517
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	517
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	517
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	517
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	517
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	517
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	517
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	517
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	517
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	517
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	517
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	517
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	517
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	517
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	517
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	517
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	517
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	517
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	517
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	517
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	517
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	517
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	517
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	517
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	517
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	517
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	517
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	517
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	517
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	517
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	517
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	517
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_DMA_ACCEN00_Bits',0,15,79,3
	.word	12153
	.byte	10
	.byte	'_Ifx_DMA_ACCEN01_Bits',0,15,82,16,4,11
	.byte	'reserved_0',0,4
	.word	494
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_DMA_ACCEN01_Bits',0,15,85,3
	.word	12712
	.byte	10
	.byte	'_Ifx_DMA_ACCEN10_Bits',0,15,88,16,4,11
	.byte	'EN0',0,1
	.word	517
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	517
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	517
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	517
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	517
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	517
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	517
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	517
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	517
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	517
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	517
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	517
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	517
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	517
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	517
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	517
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	517
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	517
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	517
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	517
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	517
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	517
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	517
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	517
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	517
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	517
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	517
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	517
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	517
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	517
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	517
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	517
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_DMA_ACCEN10_Bits',0,15,122,3
	.word	12791
	.byte	10
	.byte	'_Ifx_DMA_ACCEN11_Bits',0,15,125,16,4,11
	.byte	'reserved_0',0,4
	.word	494
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_DMA_ACCEN11_Bits',0,15,128,1,3
	.word	13350
	.byte	10
	.byte	'_Ifx_DMA_ACCEN20_Bits',0,15,131,1,16,4,11
	.byte	'EN0',0,1
	.word	517
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	517
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	517
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	517
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	517
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	517
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	517
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	517
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	517
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	517
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	517
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	517
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	517
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	517
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	517
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	517
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	517
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	517
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	517
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	517
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	517
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	517
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	517
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	517
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	517
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	517
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	517
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	517
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	517
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	517
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	517
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	517
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_DMA_ACCEN20_Bits',0,15,165,1,3
	.word	13430
	.byte	10
	.byte	'_Ifx_DMA_ACCEN21_Bits',0,15,168,1,16,4,11
	.byte	'reserved_0',0,4
	.word	494
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_DMA_ACCEN21_Bits',0,15,171,1,3
	.word	13991
	.byte	10
	.byte	'_Ifx_DMA_ACCEN30_Bits',0,15,174,1,16,4,11
	.byte	'EN0',0,1
	.word	517
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	517
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	517
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	517
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	517
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	517
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	517
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	517
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	517
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	517
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	517
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	517
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	517
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	517
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	517
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	517
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	517
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	517
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	517
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	517
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	517
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	517
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	517
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	517
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	517
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	517
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	517
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	517
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	517
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	517
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	517
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	517
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_DMA_ACCEN30_Bits',0,15,208,1,3
	.word	14072
	.byte	10
	.byte	'_Ifx_DMA_ACCEN31_Bits',0,15,211,1,16,4,11
	.byte	'reserved_0',0,4
	.word	494
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_DMA_ACCEN31_Bits',0,15,214,1,3
	.word	14633
	.byte	10
	.byte	'_Ifx_DMA_BLK_CLRE_Bits',0,15,217,1,16,4,11
	.byte	'reserved_0',0,2
	.word	1068
	.byte	16,0,2,35,0,11
	.byte	'CSER',0,1
	.word	517
	.byte	1,7,2,35,2,11
	.byte	'CDER',0,1
	.word	517
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	517
	.byte	2,4,2,35,2,11
	.byte	'CSPBER',0,1
	.word	517
	.byte	1,3,2,35,2,11
	.byte	'CSRIER',0,1
	.word	517
	.byte	1,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	517
	.byte	2,0,2,35,2,11
	.byte	'CRAMER',0,1
	.word	517
	.byte	1,7,2,35,3,11
	.byte	'CSLLER',0,1
	.word	517
	.byte	1,6,2,35,3,11
	.byte	'CDLLER',0,1
	.word	517
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	517
	.byte	5,0,2,35,3,0,22
	.byte	'Ifx_DMA_BLK_CLRE_Bits',0,15,230,1,3
	.word	14714
	.byte	10
	.byte	'_Ifx_DMA_BLK_EER_Bits',0,15,233,1,16,4,11
	.byte	'reserved_0',0,2
	.word	1068
	.byte	16,0,2,35,0,11
	.byte	'ESER',0,1
	.word	517
	.byte	1,7,2,35,2,11
	.byte	'EDER',0,1
	.word	517
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	517
	.byte	6,0,2,35,2,11
	.byte	'ERER',0,1
	.word	517
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	517
	.byte	1,6,2,35,3,11
	.byte	'ELER',0,1
	.word	517
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	517
	.byte	5,0,2,35,3,0,22
	.byte	'Ifx_DMA_BLK_EER_Bits',0,15,243,1,3
	.word	14988
	.byte	10
	.byte	'_Ifx_DMA_BLK_ERRSR_Bits',0,15,246,1,16,4,11
	.byte	'LEC',0,1
	.word	517
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	1068
	.byte	9,0,2,35,0,11
	.byte	'SER',0,1
	.word	517
	.byte	1,7,2,35,2,11
	.byte	'DER',0,1
	.word	517
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	517
	.byte	2,4,2,35,2,11
	.byte	'SPBER',0,1
	.word	517
	.byte	1,3,2,35,2,11
	.byte	'SRIER',0,1
	.word	517
	.byte	1,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	517
	.byte	2,0,2,35,2,11
	.byte	'RAMER',0,1
	.word	517
	.byte	1,7,2,35,3,11
	.byte	'SLLER',0,1
	.word	517
	.byte	1,6,2,35,3,11
	.byte	'DLLER',0,1
	.word	517
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	517
	.byte	5,0,2,35,3,0,22
	.byte	'Ifx_DMA_BLK_ERRSR_Bits',0,15,132,2,3
	.word	15202
	.byte	10
	.byte	'_Ifx_DMA_BLK_ME_ADICR_Bits',0,15,135,2,16,4,11
	.byte	'SMF',0,1
	.word	517
	.byte	3,5,2,35,0,11
	.byte	'INCS',0,1
	.word	517
	.byte	1,4,2,35,0,11
	.byte	'DMF',0,1
	.word	517
	.byte	3,1,2,35,0,11
	.byte	'INCD',0,1
	.word	517
	.byte	1,0,2,35,0,11
	.byte	'CBLS',0,1
	.word	517
	.byte	4,4,2,35,1,11
	.byte	'CBLD',0,1
	.word	517
	.byte	4,0,2,35,1,11
	.byte	'SHCT',0,1
	.word	517
	.byte	4,4,2,35,2,11
	.byte	'SCBE',0,1
	.word	517
	.byte	1,3,2,35,2,11
	.byte	'DCBE',0,1
	.word	517
	.byte	1,2,2,35,2,11
	.byte	'STAMP',0,1
	.word	517
	.byte	1,1,2,35,2,11
	.byte	'ETRL',0,1
	.word	517
	.byte	1,0,2,35,2,11
	.byte	'WRPSE',0,1
	.word	517
	.byte	1,7,2,35,3,11
	.byte	'WRPDE',0,1
	.word	517
	.byte	1,6,2,35,3,11
	.byte	'INTCT',0,1
	.word	517
	.byte	2,4,2,35,3,11
	.byte	'IRDV',0,1
	.word	517
	.byte	4,0,2,35,3,0,22
	.byte	'Ifx_DMA_BLK_ME_ADICR_Bits',0,15,152,2,3
	.word	15486
	.byte	10
	.byte	'_Ifx_DMA_BLK_ME_CHCR_Bits',0,15,155,2,16,4,11
	.byte	'TREL',0,2
	.word	1068
	.byte	14,2,2,35,0,11
	.byte	'reserved_14',0,1
	.word	517
	.byte	2,0,2,35,1,11
	.byte	'BLKM',0,1
	.word	517
	.byte	3,5,2,35,2,11
	.byte	'RROAT',0,1
	.word	517
	.byte	1,4,2,35,2,11
	.byte	'CHMODE',0,1
	.word	517
	.byte	1,3,2,35,2,11
	.byte	'CHDW',0,1
	.word	517
	.byte	3,0,2,35,2,11
	.byte	'PATSEL',0,1
	.word	517
	.byte	3,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	517
	.byte	1,4,2,35,3,11
	.byte	'PRSEL',0,1
	.word	517
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	517
	.byte	1,2,2,35,3,11
	.byte	'DMAPRIO',0,1
	.word	517
	.byte	2,0,2,35,3,0,22
	.byte	'Ifx_DMA_BLK_ME_CHCR_Bits',0,15,168,2,3
	.word	15797
	.byte	10
	.byte	'_Ifx_DMA_BLK_ME_CHSR_Bits',0,15,171,2,16,4,11
	.byte	'TCOUNT',0,2
	.word	1068
	.byte	14,2,2,35,0,11
	.byte	'reserved_14',0,1
	.word	517
	.byte	1,1,2,35,1,11
	.byte	'LXO',0,1
	.word	517
	.byte	1,0,2,35,1,11
	.byte	'WRPS',0,1
	.word	517
	.byte	1,7,2,35,2,11
	.byte	'WRPD',0,1
	.word	517
	.byte	1,6,2,35,2,11
	.byte	'ICH',0,1
	.word	517
	.byte	1,5,2,35,2,11
	.byte	'IPM',0,1
	.word	517
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,1
	.word	517
	.byte	2,2,2,35,2,11
	.byte	'BUFFER',0,1
	.word	517
	.byte	1,1,2,35,2,11
	.byte	'FROZEN',0,1
	.word	517
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	517
	.byte	8,0,2,35,3,0,22
	.byte	'Ifx_DMA_BLK_ME_CHSR_Bits',0,15,184,2,3
	.word	16070
	.byte	10
	.byte	'_Ifx_DMA_BLK_ME_DADR_Bits',0,15,187,2,16,4,11
	.byte	'DADR',0,4
	.word	494
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_DMA_BLK_ME_DADR_Bits',0,15,190,2,3
	.word	16337
	.byte	10
	.byte	'_Ifx_DMA_BLK_ME_R0_Bits',0,15,193,2,16,4,11
	.byte	'RD00',0,1
	.word	517
	.byte	8,0,2,35,0,11
	.byte	'RD01',0,1
	.word	517
	.byte	8,0,2,35,1,11
	.byte	'RD02',0,1
	.word	517
	.byte	8,0,2,35,2,11
	.byte	'RD03',0,1
	.word	517
	.byte	8,0,2,35,3,0,22
	.byte	'Ifx_DMA_BLK_ME_R0_Bits',0,15,199,2,3
	.word	16420
	.byte	10
	.byte	'_Ifx_DMA_BLK_ME_R1_Bits',0,15,202,2,16,4,11
	.byte	'RD10',0,1
	.word	517
	.byte	8,0,2,35,0,11
	.byte	'RD11',0,1
	.word	517
	.byte	8,0,2,35,1,11
	.byte	'RD12',0,1
	.word	517
	.byte	8,0,2,35,2,11
	.byte	'RD13',0,1
	.word	517
	.byte	8,0,2,35,3,0,22
	.byte	'Ifx_DMA_BLK_ME_R1_Bits',0,15,208,2,3
	.word	16547
	.byte	10
	.byte	'_Ifx_DMA_BLK_ME_R2_Bits',0,15,211,2,16,4,11
	.byte	'RD20',0,1
	.word	517
	.byte	8,0,2,35,0,11
	.byte	'RD21',0,1
	.word	517
	.byte	8,0,2,35,1,11
	.byte	'RD22',0,1
	.word	517
	.byte	8,0,2,35,2,11
	.byte	'RD23',0,1
	.word	517
	.byte	8,0,2,35,3,0,22
	.byte	'Ifx_DMA_BLK_ME_R2_Bits',0,15,217,2,3
	.word	16674
	.byte	10
	.byte	'_Ifx_DMA_BLK_ME_R3_Bits',0,15,220,2,16,4,11
	.byte	'RD30',0,1
	.word	517
	.byte	8,0,2,35,0,11
	.byte	'RD31',0,1
	.word	517
	.byte	8,0,2,35,1,11
	.byte	'RD32',0,1
	.word	517
	.byte	8,0,2,35,2,11
	.byte	'RD33',0,1
	.word	517
	.byte	8,0,2,35,3,0,22
	.byte	'Ifx_DMA_BLK_ME_R3_Bits',0,15,226,2,3
	.word	16801
	.byte	10
	.byte	'_Ifx_DMA_BLK_ME_R4_Bits',0,15,229,2,16,4,11
	.byte	'RD40',0,1
	.word	517
	.byte	8,0,2,35,0,11
	.byte	'RD41',0,1
	.word	517
	.byte	8,0,2,35,1,11
	.byte	'RD42',0,1
	.word	517
	.byte	8,0,2,35,2,11
	.byte	'RD43',0,1
	.word	517
	.byte	8,0,2,35,3,0,22
	.byte	'Ifx_DMA_BLK_ME_R4_Bits',0,15,235,2,3
	.word	16928
	.byte	10
	.byte	'_Ifx_DMA_BLK_ME_R5_Bits',0,15,238,2,16,4,11
	.byte	'RD50',0,1
	.word	517
	.byte	8,0,2,35,0,11
	.byte	'RD51',0,1
	.word	517
	.byte	8,0,2,35,1,11
	.byte	'RD52',0,1
	.word	517
	.byte	8,0,2,35,2,11
	.byte	'RD53',0,1
	.word	517
	.byte	8,0,2,35,3,0,22
	.byte	'Ifx_DMA_BLK_ME_R5_Bits',0,15,244,2,3
	.word	17055
	.byte	10
	.byte	'_Ifx_DMA_BLK_ME_R6_Bits',0,15,247,2,16,4,11
	.byte	'RD60',0,1
	.word	517
	.byte	8,0,2,35,0,11
	.byte	'RD61',0,1
	.word	517
	.byte	8,0,2,35,1,11
	.byte	'RD62',0,1
	.word	517
	.byte	8,0,2,35,2,11
	.byte	'RD63',0,1
	.word	517
	.byte	8,0,2,35,3,0,22
	.byte	'Ifx_DMA_BLK_ME_R6_Bits',0,15,253,2,3
	.word	17182
	.byte	10
	.byte	'_Ifx_DMA_BLK_ME_R7_Bits',0,15,128,3,16,4,11
	.byte	'RD70',0,1
	.word	517
	.byte	8,0,2,35,0,11
	.byte	'RD71',0,1
	.word	517
	.byte	8,0,2,35,1,11
	.byte	'RD72',0,1
	.word	517
	.byte	8,0,2,35,2,11
	.byte	'RD73',0,1
	.word	517
	.byte	8,0,2,35,3,0,22
	.byte	'Ifx_DMA_BLK_ME_R7_Bits',0,15,134,3,3
	.word	17309
	.byte	10
	.byte	'_Ifx_DMA_BLK_ME_RDCRC_Bits',0,15,137,3,16,4,11
	.byte	'RDCRC',0,4
	.word	494
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_DMA_BLK_ME_RDCRC_Bits',0,15,140,3,3
	.word	17436
	.byte	10
	.byte	'_Ifx_DMA_BLK_ME_SADR_Bits',0,15,143,3,16,4,11
	.byte	'SADR',0,4
	.word	494
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_DMA_BLK_ME_SADR_Bits',0,15,146,3,3
	.word	17522
	.byte	10
	.byte	'_Ifx_DMA_BLK_ME_SDCRC_Bits',0,15,149,3,16,4,11
	.byte	'SDCRC',0,4
	.word	494
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_DMA_BLK_ME_SDCRC_Bits',0,15,152,3,3
	.word	17605
	.byte	10
	.byte	'_Ifx_DMA_BLK_ME_SHADR_Bits',0,15,155,3,16,4,11
	.byte	'SHADR',0,4
	.word	494
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_DMA_BLK_ME_SHADR_Bits',0,15,158,3,3
	.word	17691
	.byte	10
	.byte	'_Ifx_DMA_BLK_ME_SR_Bits',0,15,161,3,16,4,11
	.byte	'RS',0,1
	.word	517
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	517
	.byte	3,4,2,35,0,11
	.byte	'WS',0,1
	.word	517
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,2
	.word	1068
	.byte	11,0,2,35,0,11
	.byte	'CH',0,1
	.word	517
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	1068
	.byte	9,0,2,35,2,0,22
	.byte	'Ifx_DMA_BLK_ME_SR_Bits',0,15,169,3,3
	.word	17777
	.byte	10
	.byte	'_Ifx_DMA_CH_ADICR_Bits',0,15,172,3,16,4,11
	.byte	'SMF',0,1
	.word	517
	.byte	3,5,2,35,0,11
	.byte	'INCS',0,1
	.word	517
	.byte	1,4,2,35,0,11
	.byte	'DMF',0,1
	.word	517
	.byte	3,1,2,35,0,11
	.byte	'INCD',0,1
	.word	517
	.byte	1,0,2,35,0,11
	.byte	'CBLS',0,1
	.word	517
	.byte	4,4,2,35,1,11
	.byte	'CBLD',0,1
	.word	517
	.byte	4,0,2,35,1,11
	.byte	'SHCT',0,1
	.word	517
	.byte	4,4,2,35,2,11
	.byte	'SCBE',0,1
	.word	517
	.byte	1,3,2,35,2,11
	.byte	'DCBE',0,1
	.word	517
	.byte	1,2,2,35,2,11
	.byte	'STAMP',0,1
	.word	517
	.byte	1,1,2,35,2,11
	.byte	'ETRL',0,1
	.word	517
	.byte	1,0,2,35,2,11
	.byte	'WRPSE',0,1
	.word	517
	.byte	1,7,2,35,3,11
	.byte	'WRPDE',0,1
	.word	517
	.byte	1,6,2,35,3,11
	.byte	'INTCT',0,1
	.word	517
	.byte	2,4,2,35,3,11
	.byte	'IRDV',0,1
	.word	517
	.byte	4,0,2,35,3,0,22
	.byte	'Ifx_DMA_CH_ADICR_Bits',0,15,189,3,3
	.word	17949
	.byte	10
	.byte	'_Ifx_DMA_CH_CHCFGR_Bits',0,15,192,3,16,4,11
	.byte	'TREL',0,2
	.word	1068
	.byte	14,2,2,35,0,11
	.byte	'reserved_14',0,1
	.word	517
	.byte	2,0,2,35,1,11
	.byte	'BLKM',0,1
	.word	517
	.byte	3,5,2,35,2,11
	.byte	'RROAT',0,1
	.word	517
	.byte	1,4,2,35,2,11
	.byte	'CHMODE',0,1
	.word	517
	.byte	1,3,2,35,2,11
	.byte	'CHDW',0,1
	.word	517
	.byte	3,0,2,35,2,11
	.byte	'PATSEL',0,1
	.word	517
	.byte	3,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	517
	.byte	1,4,2,35,3,11
	.byte	'PRSEL',0,1
	.word	517
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	517
	.byte	1,2,2,35,3,11
	.byte	'DMAPRIO',0,1
	.word	517
	.byte	2,0,2,35,3,0,22
	.byte	'Ifx_DMA_CH_CHCFGR_Bits',0,15,205,3,3
	.word	18252
	.byte	10
	.byte	'_Ifx_DMA_CH_CHCSR_Bits',0,15,208,3,16,4,11
	.byte	'TCOUNT',0,2
	.word	1068
	.byte	14,2,2,35,0,11
	.byte	'reserved_14',0,1
	.word	517
	.byte	1,1,2,35,1,11
	.byte	'LXO',0,1
	.word	517
	.byte	1,0,2,35,1,11
	.byte	'WRPS',0,1
	.word	517
	.byte	1,7,2,35,2,11
	.byte	'WRPD',0,1
	.word	517
	.byte	1,6,2,35,2,11
	.byte	'ICH',0,1
	.word	517
	.byte	1,5,2,35,2,11
	.byte	'IPM',0,1
	.word	517
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,1
	.word	517
	.byte	2,2,2,35,2,11
	.byte	'BUFFER',0,1
	.word	517
	.byte	1,1,2,35,2,11
	.byte	'FROZEN',0,1
	.word	517
	.byte	1,0,2,35,2,11
	.byte	'SWB',0,1
	.word	517
	.byte	1,7,2,35,3,11
	.byte	'CWRP',0,1
	.word	517
	.byte	1,6,2,35,3,11
	.byte	'CICH',0,1
	.word	517
	.byte	1,5,2,35,3,11
	.byte	'SIT',0,1
	.word	517
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	517
	.byte	3,1,2,35,3,11
	.byte	'SCH',0,1
	.word	517
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_DMA_CH_CHCSR_Bits',0,15,226,3,3
	.word	18521
	.byte	10
	.byte	'_Ifx_DMA_CH_DADR_Bits',0,15,229,3,16,4,11
	.byte	'DADR',0,4
	.word	494
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_DMA_CH_DADR_Bits',0,15,232,3,3
	.word	18859
	.byte	10
	.byte	'_Ifx_DMA_CH_RDCRCR_Bits',0,15,235,3,16,4,11
	.byte	'RDCRC',0,4
	.word	494
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_DMA_CH_RDCRCR_Bits',0,15,238,3,3
	.word	18934
	.byte	10
	.byte	'_Ifx_DMA_CH_SADR_Bits',0,15,241,3,16,4,11
	.byte	'SADR',0,4
	.word	494
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_DMA_CH_SADR_Bits',0,15,244,3,3
	.word	19014
	.byte	10
	.byte	'_Ifx_DMA_CH_SDCRCR_Bits',0,15,247,3,16,4,11
	.byte	'SDCRC',0,4
	.word	494
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_DMA_CH_SDCRCR_Bits',0,15,250,3,3
	.word	19089
	.byte	10
	.byte	'_Ifx_DMA_CH_SHADR_Bits',0,15,253,3,16,4,11
	.byte	'SHADR',0,4
	.word	494
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_DMA_CH_SHADR_Bits',0,15,128,4,3
	.word	19169
	.byte	10
	.byte	'_Ifx_DMA_CLC_Bits',0,15,131,4,16,4,11
	.byte	'DISR',0,1
	.word	517
	.byte	1,7,2,35,0,11
	.byte	'DISS',0,1
	.word	517
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	517
	.byte	1,5,2,35,0,11
	.byte	'EDIS',0,1
	.word	517
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	494
	.byte	28,0,2,35,0,0,22
	.byte	'Ifx_DMA_CLC_Bits',0,15,138,4,3
	.word	19247
	.byte	10
	.byte	'_Ifx_DMA_ERRINTR_Bits',0,15,141,4,16,4,11
	.byte	'SIT',0,1
	.word	517
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	494
	.byte	31,0,2,35,0,0,22
	.byte	'Ifx_DMA_ERRINTR_Bits',0,15,145,4,3
	.word	19390
	.byte	10
	.byte	'_Ifx_DMA_HRR_Bits',0,15,148,4,16,4,11
	.byte	'HRP',0,1
	.word	517
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	494
	.byte	30,0,2,35,0,0,22
	.byte	'Ifx_DMA_HRR_Bits',0,15,152,4,3
	.word	19486
	.byte	10
	.byte	'_Ifx_DMA_ID_Bits',0,15,155,4,16,4,11
	.byte	'MODREV',0,1
	.word	517
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	517
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	1068
	.byte	16,0,2,35,2,0,22
	.byte	'Ifx_DMA_ID_Bits',0,15,160,4,3
	.word	19574
	.byte	10
	.byte	'_Ifx_DMA_MEMCON_Bits',0,15,163,4,16,4,11
	.byte	'reserved_0',0,4
	.word	916
	.byte	2,30,2,35,0,11
	.byte	'INTERR',0,4
	.word	916
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	916
	.byte	1,28,2,35,0,11
	.byte	'RMWERR',0,4
	.word	916
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	916
	.byte	1,26,2,35,0,11
	.byte	'DATAERR',0,4
	.word	916
	.byte	1,25,2,35,0,11
	.byte	'reserved_7',0,4
	.word	916
	.byte	1,24,2,35,0,11
	.byte	'PMIC',0,4
	.word	916
	.byte	1,23,2,35,0,11
	.byte	'ERRDIS',0,4
	.word	916
	.byte	1,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	916
	.byte	22,0,2,35,0,0,22
	.byte	'Ifx_DMA_MEMCON_Bits',0,15,175,4,3
	.word	19681
	.byte	10
	.byte	'_Ifx_DMA_MODE_Bits',0,15,178,4,16,4,11
	.byte	'MODE',0,1
	.word	517
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	494
	.byte	31,0,2,35,0,0,22
	.byte	'Ifx_DMA_MODE_Bits',0,15,182,4,3
	.word	19938
	.byte	10
	.byte	'_Ifx_DMA_OTSS_Bits',0,15,185,4,16,4,11
	.byte	'TGS',0,1
	.word	517
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	517
	.byte	3,1,2,35,0,11
	.byte	'BS',0,1
	.word	517
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	494
	.byte	24,0,2,35,0,0,22
	.byte	'Ifx_DMA_OTSS_Bits',0,15,191,4,3
	.word	20029
	.byte	10
	.byte	'_Ifx_DMA_PRR0_Bits',0,15,194,4,16,4,11
	.byte	'PAT00',0,1
	.word	517
	.byte	8,0,2,35,0,11
	.byte	'PAT01',0,1
	.word	517
	.byte	8,0,2,35,1,11
	.byte	'PAT02',0,1
	.word	517
	.byte	8,0,2,35,2,11
	.byte	'PAT03',0,1
	.word	517
	.byte	8,0,2,35,3,0,22
	.byte	'Ifx_DMA_PRR0_Bits',0,15,200,4,3
	.word	20155
	.byte	10
	.byte	'_Ifx_DMA_PRR1_Bits',0,15,203,4,16,4,11
	.byte	'PAT10',0,1
	.word	517
	.byte	8,0,2,35,0,11
	.byte	'PAT11',0,1
	.word	517
	.byte	8,0,2,35,1,11
	.byte	'PAT12',0,1
	.word	517
	.byte	8,0,2,35,2,11
	.byte	'PAT13',0,1
	.word	517
	.byte	8,0,2,35,3,0,22
	.byte	'Ifx_DMA_PRR1_Bits',0,15,209,4,3
	.word	20276
	.byte	10
	.byte	'_Ifx_DMA_SUSACR_Bits',0,15,212,4,16,4,11
	.byte	'SUSAC',0,1
	.word	517
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	494
	.byte	31,0,2,35,0,0,22
	.byte	'Ifx_DMA_SUSACR_Bits',0,15,216,4,3
	.word	20397
	.byte	10
	.byte	'_Ifx_DMA_SUSENR_Bits',0,15,219,4,16,4,11
	.byte	'SUSEN',0,1
	.word	517
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	494
	.byte	31,0,2,35,0,0,22
	.byte	'Ifx_DMA_SUSENR_Bits',0,15,223,4,3
	.word	20493
	.byte	10
	.byte	'_Ifx_DMA_TIME_Bits',0,15,226,4,16,4,11
	.byte	'COUNT',0,4
	.word	494
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_DMA_TIME_Bits',0,15,229,4,3
	.word	20589
	.byte	10
	.byte	'_Ifx_DMA_TSR_Bits',0,15,232,4,16,4,11
	.byte	'RST',0,1
	.word	517
	.byte	1,7,2,35,0,11
	.byte	'HTRE',0,1
	.word	517
	.byte	1,6,2,35,0,11
	.byte	'TRL',0,1
	.word	517
	.byte	1,5,2,35,0,11
	.byte	'CH',0,1
	.word	517
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	517
	.byte	4,0,2,35,0,11
	.byte	'HLTREQ',0,1
	.word	517
	.byte	1,7,2,35,1,11
	.byte	'HLTACK',0,1
	.word	517
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	517
	.byte	6,0,2,35,1,11
	.byte	'ECH',0,1
	.word	517
	.byte	1,7,2,35,2,11
	.byte	'DCH',0,1
	.word	517
	.byte	1,6,2,35,2,11
	.byte	'CTL',0,1
	.word	517
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	517
	.byte	5,0,2,35,2,11
	.byte	'HLTCLR',0,1
	.word	517
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	517
	.byte	7,0,2,35,3,0,22
	.byte	'Ifx_DMA_TSR_Bits',0,15,248,4,3
	.word	20659
	.byte	12,15,128,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12153
	.byte	4,2,35,0,0,22
	.byte	'Ifx_DMA_ACCEN00',0,15,133,5,3
	.word	20960
	.byte	12,15,136,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12712
	.byte	4,2,35,0,0,22
	.byte	'Ifx_DMA_ACCEN01',0,15,141,5,3
	.word	21025
	.byte	12,15,144,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12791
	.byte	4,2,35,0,0,22
	.byte	'Ifx_DMA_ACCEN10',0,15,149,5,3
	.word	21090
	.byte	12,15,152,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13350
	.byte	4,2,35,0,0,22
	.byte	'Ifx_DMA_ACCEN11',0,15,157,5,3
	.word	21155
	.byte	12,15,160,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13430
	.byte	4,2,35,0,0,22
	.byte	'Ifx_DMA_ACCEN20',0,15,165,5,3
	.word	21220
	.byte	12,15,168,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13991
	.byte	4,2,35,0,0,22
	.byte	'Ifx_DMA_ACCEN21',0,15,173,5,3
	.word	21285
	.byte	12,15,176,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14072
	.byte	4,2,35,0,0,22
	.byte	'Ifx_DMA_ACCEN30',0,15,181,5,3
	.word	21350
	.byte	12,15,184,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14633
	.byte	4,2,35,0,0,22
	.byte	'Ifx_DMA_ACCEN31',0,15,189,5,3
	.word	21415
	.byte	12,15,192,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14714
	.byte	4,2,35,0,0,22
	.byte	'Ifx_DMA_BLK_CLRE',0,15,197,5,3
	.word	21480
	.byte	12,15,200,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14988
	.byte	4,2,35,0,0,22
	.byte	'Ifx_DMA_BLK_EER',0,15,205,5,3
	.word	21546
	.byte	12,15,208,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15202
	.byte	4,2,35,0,0,22
	.byte	'Ifx_DMA_BLK_ERRSR',0,15,213,5,3
	.word	21611
	.byte	12,15,216,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15486
	.byte	4,2,35,0,0,22
	.byte	'Ifx_DMA_BLK_ME_ADICR',0,15,221,5,3
	.word	21678
	.byte	12,15,224,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15797
	.byte	4,2,35,0,0,22
	.byte	'Ifx_DMA_BLK_ME_CHCR',0,15,229,5,3
	.word	21748
	.byte	12,15,232,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16070
	.byte	4,2,35,0,0,22
	.byte	'Ifx_DMA_BLK_ME_CHSR',0,15,237,5,3
	.word	21817
	.byte	12,15,240,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16337
	.byte	4,2,35,0,0,22
	.byte	'Ifx_DMA_BLK_ME_DADR',0,15,245,5,3
	.word	21886
	.byte	12,15,248,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16420
	.byte	4,2,35,0,0,22
	.byte	'Ifx_DMA_BLK_ME_R0',0,15,253,5,3
	.word	21955
	.byte	12,15,128,6,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16547
	.byte	4,2,35,0,0,22
	.byte	'Ifx_DMA_BLK_ME_R1',0,15,133,6,3
	.word	22022
	.byte	12,15,136,6,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16674
	.byte	4,2,35,0,0,22
	.byte	'Ifx_DMA_BLK_ME_R2',0,15,141,6,3
	.word	22089
	.byte	12,15,144,6,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16801
	.byte	4,2,35,0,0,22
	.byte	'Ifx_DMA_BLK_ME_R3',0,15,149,6,3
	.word	22156
	.byte	12,15,152,6,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16928
	.byte	4,2,35,0,0,22
	.byte	'Ifx_DMA_BLK_ME_R4',0,15,157,6,3
	.word	22223
	.byte	12,15,160,6,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17055
	.byte	4,2,35,0,0,22
	.byte	'Ifx_DMA_BLK_ME_R5',0,15,165,6,3
	.word	22290
	.byte	12,15,168,6,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17182
	.byte	4,2,35,0,0,22
	.byte	'Ifx_DMA_BLK_ME_R6',0,15,173,6,3
	.word	22357
	.byte	12,15,176,6,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17309
	.byte	4,2,35,0,0,22
	.byte	'Ifx_DMA_BLK_ME_R7',0,15,181,6,3
	.word	22424
	.byte	12,15,184,6,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17436
	.byte	4,2,35,0,0,22
	.byte	'Ifx_DMA_BLK_ME_RDCRC',0,15,189,6,3
	.word	22491
	.byte	12,15,192,6,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17522
	.byte	4,2,35,0,0,22
	.byte	'Ifx_DMA_BLK_ME_SADR',0,15,197,6,3
	.word	22561
	.byte	12,15,200,6,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17605
	.byte	4,2,35,0,0,22
	.byte	'Ifx_DMA_BLK_ME_SDCRC',0,15,205,6,3
	.word	22630
	.byte	12,15,208,6,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17691
	.byte	4,2,35,0,0,22
	.byte	'Ifx_DMA_BLK_ME_SHADR',0,15,213,6,3
	.word	22700
	.byte	12,15,216,6,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17777
	.byte	4,2,35,0,0,22
	.byte	'Ifx_DMA_BLK_ME_SR',0,15,221,6,3
	.word	22770
	.byte	12,15,224,6,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17949
	.byte	4,2,35,0,0,22
	.byte	'Ifx_DMA_CH_ADICR',0,15,229,6,3
	.word	22837
	.byte	12,15,232,6,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18252
	.byte	4,2,35,0,0,22
	.byte	'Ifx_DMA_CH_CHCFGR',0,15,237,6,3
	.word	22903
	.byte	12,15,240,6,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18521
	.byte	4,2,35,0,0,22
	.byte	'Ifx_DMA_CH_CHCSR',0,15,245,6,3
	.word	22970
	.byte	12,15,248,6,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18859
	.byte	4,2,35,0,0,22
	.byte	'Ifx_DMA_CH_DADR',0,15,253,6,3
	.word	23036
	.byte	12,15,128,7,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18934
	.byte	4,2,35,0,0,22
	.byte	'Ifx_DMA_CH_RDCRCR',0,15,133,7,3
	.word	23101
	.byte	12,15,136,7,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19014
	.byte	4,2,35,0,0,22
	.byte	'Ifx_DMA_CH_SADR',0,15,141,7,3
	.word	23168
	.byte	12,15,144,7,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19089
	.byte	4,2,35,0,0,22
	.byte	'Ifx_DMA_CH_SDCRCR',0,15,149,7,3
	.word	23233
	.byte	12,15,152,7,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19169
	.byte	4,2,35,0,0,22
	.byte	'Ifx_DMA_CH_SHADR',0,15,157,7,3
	.word	23300
	.byte	12,15,160,7,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19247
	.byte	4,2,35,0,0,22
	.byte	'Ifx_DMA_CLC',0,15,165,7,3
	.word	23366
	.byte	12,15,168,7,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19390
	.byte	4,2,35,0,0,22
	.byte	'Ifx_DMA_ERRINTR',0,15,173,7,3
	.word	23427
	.byte	12,15,176,7,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19486
	.byte	4,2,35,0,0,22
	.byte	'Ifx_DMA_HRR',0,15,181,7,3
	.word	23492
	.byte	12,15,184,7,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19574
	.byte	4,2,35,0,0,22
	.byte	'Ifx_DMA_ID',0,15,189,7,3
	.word	23553
	.byte	12,15,192,7,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19681
	.byte	4,2,35,0,0,22
	.byte	'Ifx_DMA_MEMCON',0,15,197,7,3
	.word	23613
	.byte	12,15,200,7,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19938
	.byte	4,2,35,0,0,22
	.byte	'Ifx_DMA_MODE',0,15,205,7,3
	.word	23677
	.byte	12,15,208,7,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20029
	.byte	4,2,35,0,0,22
	.byte	'Ifx_DMA_OTSS',0,15,213,7,3
	.word	23739
	.byte	12,15,216,7,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20155
	.byte	4,2,35,0,0,22
	.byte	'Ifx_DMA_PRR0',0,15,221,7,3
	.word	23801
	.byte	12,15,224,7,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20276
	.byte	4,2,35,0,0,22
	.byte	'Ifx_DMA_PRR1',0,15,229,7,3
	.word	23863
	.byte	12,15,232,7,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20397
	.byte	4,2,35,0,0,22
	.byte	'Ifx_DMA_SUSACR',0,15,237,7,3
	.word	23925
	.byte	12,15,240,7,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20493
	.byte	4,2,35,0,0,22
	.byte	'Ifx_DMA_SUSENR',0,15,245,7,3
	.word	23989
	.byte	12,15,248,7,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20589
	.byte	4,2,35,0,0,22
	.byte	'Ifx_DMA_TIME',0,15,253,7,3
	.word	24053
	.byte	12,15,128,8,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20659
	.byte	4,2,35,0,0,22
	.byte	'Ifx_DMA_TSR',0,15,133,8,3
	.word	24115
	.byte	18,32
	.word	517
	.byte	19,31,0,10
	.byte	'_Ifx_DMA_BLK_ME',0,15,144,8,25,112,13
	.byte	'SR',0
	.word	22770
	.byte	4,2,35,0,13
	.byte	'reserved_4',0
	.word	5594
	.byte	12,2,35,4,13
	.byte	'R0',0
	.word	21955
	.byte	4,2,35,16,13
	.byte	'R1',0
	.word	22022
	.byte	4,2,35,20,13
	.byte	'R2',0
	.word	22089
	.byte	4,2,35,24,13
	.byte	'R3',0
	.word	22156
	.byte	4,2,35,28,13
	.byte	'R4',0
	.word	22223
	.byte	4,2,35,32,13
	.byte	'R5',0
	.word	22290
	.byte	4,2,35,36,13
	.byte	'R6',0
	.word	22357
	.byte	4,2,35,40,13
	.byte	'R7',0
	.word	22424
	.byte	4,2,35,44,13
	.byte	'reserved_30',0
	.word	24176
	.byte	32,2,35,48,13
	.byte	'RDCRC',0
	.word	22491
	.byte	4,2,35,80,13
	.byte	'SDCRC',0
	.word	22630
	.byte	4,2,35,84,13
	.byte	'SADR',0
	.word	22561
	.byte	4,2,35,88,13
	.byte	'DADR',0
	.word	21886
	.byte	4,2,35,92,13
	.byte	'ADICR',0
	.word	21678
	.byte	4,2,35,96,13
	.byte	'CHCR',0
	.word	21748
	.byte	4,2,35,100,13
	.byte	'SHADR',0
	.word	22700
	.byte	4,2,35,104,13
	.byte	'CHSR',0
	.word	21817
	.byte	4,2,35,108,0,14
	.word	24185
	.byte	22
	.byte	'Ifx_DMA_BLK_ME',0,15,165,8,3
	.word	24473
	.byte	14
	.word	24185
	.byte	10
	.byte	'_Ifx_DMA_BLK',0,15,178,8,25,128,1,13
	.byte	'EER',0
	.word	21546
	.byte	4,2,35,0,13
	.byte	'ERRSR',0
	.word	21611
	.byte	4,2,35,4,13
	.byte	'CLRE',0
	.word	21480
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	3435
	.byte	4,2,35,12,13
	.byte	'ME',0
	.word	24502
	.byte	112,2,35,16,0,14
	.word	24507
	.byte	22
	.byte	'Ifx_DMA_BLK',0,15,185,8,3
	.word	24602
	.byte	10
	.byte	'_Ifx_DMA_CH',0,15,188,8,25,32,13
	.byte	'RDCRCR',0
	.word	23101
	.byte	4,2,35,0,13
	.byte	'SDCRCR',0
	.word	23233
	.byte	4,2,35,4,13
	.byte	'SADR',0
	.word	23168
	.byte	4,2,35,8,13
	.byte	'DADR',0
	.word	23036
	.byte	4,2,35,12,13
	.byte	'ADICR',0
	.word	22837
	.byte	4,2,35,16,13
	.byte	'CHCFGR',0
	.word	22903
	.byte	4,2,35,20,13
	.byte	'SHADR',0
	.word	23300
	.byte	4,2,35,24,13
	.byte	'CHCSR',0
	.word	22970
	.byte	4,2,35,28,0,14
	.word	24628
	.byte	22
	.byte	'Ifx_DMA_CH',0,15,198,8,3
	.word	24768
	.byte	18,20
	.word	517
	.byte	19,19,0,18,28
	.word	517
	.byte	19,27,0,18,192,1
	.word	517
	.byte	19,191,1,0,14
	.word	24507
	.byte	18,128,31
	.word	517
	.byte	19,255,30,0,14
	.word	24507
	.byte	18,96
	.word	517
	.byte	19,95,0,18,236,1
	.word	517
	.byte	19,235,1,0,18,16
	.word	23677
	.byte	19,3,0,18,240,9
	.word	517
	.byte	19,239,9,0,18,192,1
	.word	23492
	.byte	19,47,0,18,192,2
	.word	517
	.byte	19,191,2,0,18,192,1
	.word	23989
	.byte	19,47,0,18,192,1
	.word	23925
	.byte	19,47,0,18,192,1
	.word	24115
	.byte	19,47,0,18,128,12
	.word	24628
	.byte	19,47,0,14
	.word	24934
	.byte	18,128,52
	.word	517
	.byte	19,255,51,0,10
	.byte	'_Ifx_DMA',0,15,211,8,25,128,128,1,13
	.byte	'CLC',0
	.word	23366
	.byte	4,2,35,0,13
	.byte	'reserved_4',0
	.word	3435
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	23553
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	24793
	.byte	20,2,35,12,13
	.byte	'MEMCON',0
	.word	23613
	.byte	4,2,35,32,13
	.byte	'reserved_24',0
	.word	24802
	.byte	28,2,35,36,13
	.byte	'ACCEN00',0
	.word	20960
	.byte	4,2,35,64,13
	.byte	'ACCEN01',0
	.word	21025
	.byte	4,2,35,68,13
	.byte	'ACCEN10',0
	.word	21090
	.byte	4,2,35,72,13
	.byte	'ACCEN11',0
	.word	21155
	.byte	4,2,35,76,13
	.byte	'ACCEN20',0
	.word	21220
	.byte	4,2,35,80,13
	.byte	'ACCEN21',0
	.word	21285
	.byte	4,2,35,84,13
	.byte	'ACCEN30',0
	.word	21350
	.byte	4,2,35,88,13
	.byte	'ACCEN31',0
	.word	21415
	.byte	4,2,35,92,13
	.byte	'reserved_60',0
	.word	24811
	.byte	192,1,2,35,96,13
	.byte	'BLK0',0
	.word	24822
	.byte	128,1,3,35,160,2,13
	.byte	'reserved_1A0',0
	.word	24827
	.byte	128,31,3,35,160,3,13
	.byte	'BLK1',0
	.word	24838
	.byte	128,1,3,35,160,34,13
	.byte	'reserved_11A0',0
	.word	24843
	.byte	96,3,35,160,35,13
	.byte	'OTSS',0
	.word	23739
	.byte	4,3,35,128,36,13
	.byte	'ERRINTR',0
	.word	23427
	.byte	4,3,35,132,36,13
	.byte	'PRR0',0
	.word	23801
	.byte	4,3,35,136,36,13
	.byte	'PRR1',0
	.word	23863
	.byte	4,3,35,140,36,13
	.byte	'TIME',0
	.word	24053
	.byte	4,3,35,144,36,13
	.byte	'reserved_1214',0
	.word	24852
	.byte	236,1,3,35,148,36,13
	.byte	'MODE',0
	.word	24863
	.byte	16,3,35,128,38,13
	.byte	'reserved_1310',0
	.word	24872
	.byte	240,9,3,35,144,38,13
	.byte	'HRR',0
	.word	24883
	.byte	192,1,3,35,128,48,13
	.byte	'reserved_18C0',0
	.word	24893
	.byte	192,2,3,35,192,49,13
	.byte	'SUSENR',0
	.word	24904
	.byte	192,1,3,35,128,52,13
	.byte	'reserved_1AC0',0
	.word	24893
	.byte	192,2,3,35,192,53,13
	.byte	'SUSACR',0
	.word	24914
	.byte	192,1,3,35,128,56,13
	.byte	'reserved_1CC0',0
	.word	24893
	.byte	192,2,3,35,192,57,13
	.byte	'TSR',0
	.word	24924
	.byte	192,1,3,35,128,60,13
	.byte	'reserved_1EC0',0
	.word	24893
	.byte	192,2,3,35,192,61,13
	.byte	'CH',0
	.word	24944
	.byte	128,12,3,35,128,64,13
	.byte	'reserved_2600',0
	.word	24949
	.byte	128,52,3,35,128,76,0,14
	.word	24960
	.byte	22
	.byte	'Ifx_DMA',0,15,250,8,3
	.word	25666
	.byte	15,16,69,9,1,16
	.byte	'IfxSrc_Tos_cpu0',0,0,16
	.byte	'IfxSrc_Tos_cpu1',0,1,16
	.byte	'IfxSrc_Tos_dma',0,3,0,22
	.byte	'IfxSrc_Tos',0,16,74,3
	.word	25688
	.byte	22
	.byte	'Ifx_SRC_SRCR_Bits',0,4,62,3
	.word	534
	.byte	22
	.byte	'Ifx_SRC_SRCR',0,4,75,3
	.word	824
	.byte	10
	.byte	'_Ifx_SRC_AGBT',0,4,86,25,4,13
	.byte	'SR',0
	.word	824
	.byte	4,2,35,0,0,14
	.word	25813
	.byte	22
	.byte	'Ifx_SRC_AGBT',0,4,89,3
	.word	25845
	.byte	10
	.byte	'_Ifx_SRC_ASCLIN',0,4,92,25,12,13
	.byte	'TX',0
	.word	824
	.byte	4,2,35,0,13
	.byte	'RX',0
	.word	824
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	824
	.byte	4,2,35,8,0,14
	.word	25871
	.byte	22
	.byte	'Ifx_SRC_ASCLIN',0,4,97,3
	.word	25930
	.byte	10
	.byte	'_Ifx_SRC_BCUSPB',0,4,100,25,4,13
	.byte	'SBSRC',0
	.word	824
	.byte	4,2,35,0,0,14
	.word	25958
	.byte	22
	.byte	'Ifx_SRC_BCUSPB',0,4,103,3
	.word	25995
	.byte	18,64
	.word	824
	.byte	19,15,0,10
	.byte	'_Ifx_SRC_CAN',0,4,106,25,64,13
	.byte	'INT',0
	.word	26023
	.byte	64,2,35,0,0,14
	.word	26032
	.byte	22
	.byte	'Ifx_SRC_CAN',0,4,109,3
	.word	26064
	.byte	10
	.byte	'_Ifx_SRC_CCU6',0,4,112,25,16,13
	.byte	'SR0',0
	.word	824
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	824
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	824
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	824
	.byte	4,2,35,12,0,14
	.word	26089
	.byte	22
	.byte	'Ifx_SRC_CCU6',0,4,118,3
	.word	26161
	.byte	18,8
	.word	824
	.byte	19,1,0,10
	.byte	'_Ifx_SRC_CERBERUS',0,4,121,25,8,13
	.byte	'SR',0
	.word	26187
	.byte	8,2,35,0,0,14
	.word	26196
	.byte	22
	.byte	'Ifx_SRC_CERBERUS',0,4,124,3
	.word	26232
	.byte	10
	.byte	'_Ifx_SRC_CIF',0,4,127,25,16,13
	.byte	'MI',0
	.word	824
	.byte	4,2,35,0,13
	.byte	'MIEP',0
	.word	824
	.byte	4,2,35,4,13
	.byte	'ISP',0
	.word	824
	.byte	4,2,35,8,13
	.byte	'MJPEG',0
	.word	824
	.byte	4,2,35,12,0,14
	.word	26262
	.byte	22
	.byte	'Ifx_SRC_CIF',0,4,133,1,3
	.word	26335
	.byte	10
	.byte	'_Ifx_SRC_CPU',0,4,136,1,25,4,13
	.byte	'SBSRC',0
	.word	824
	.byte	4,2,35,0,0,14
	.word	26361
	.byte	22
	.byte	'Ifx_SRC_CPU',0,4,139,1,3
	.word	26396
	.byte	18,192,1
	.word	824
	.byte	19,47,0,10
	.byte	'_Ifx_SRC_DMA',0,4,142,1,25,208,1,13
	.byte	'ERR',0
	.word	824
	.byte	4,2,35,0,13
	.byte	'reserved_4',0
	.word	5594
	.byte	12,2,35,4,13
	.byte	'CH',0
	.word	26422
	.byte	192,1,2,35,16,0,14
	.word	26432
	.byte	22
	.byte	'Ifx_SRC_DMA',0,4,147,1,3
	.word	26499
	.byte	10
	.byte	'_Ifx_SRC_DSADC',0,4,150,1,25,8,13
	.byte	'SRM',0
	.word	824
	.byte	4,2,35,0,13
	.byte	'SRA',0
	.word	824
	.byte	4,2,35,4,0,14
	.word	26525
	.byte	22
	.byte	'Ifx_SRC_DSADC',0,4,154,1,3
	.word	26573
	.byte	10
	.byte	'_Ifx_SRC_EMEM',0,4,157,1,25,4,13
	.byte	'SR',0
	.word	824
	.byte	4,2,35,0,0,14
	.word	26601
	.byte	22
	.byte	'Ifx_SRC_EMEM',0,4,160,1,3
	.word	26634
	.byte	18,40
	.word	517
	.byte	19,39,0,10
	.byte	'_Ifx_SRC_ERAY',0,4,163,1,25,80,13
	.byte	'INT',0
	.word	26187
	.byte	8,2,35,0,13
	.byte	'TINT',0
	.word	26187
	.byte	8,2,35,8,13
	.byte	'NDAT',0
	.word	26187
	.byte	8,2,35,16,13
	.byte	'MBSC',0
	.word	26187
	.byte	8,2,35,24,13
	.byte	'OBUSY',0
	.word	824
	.byte	4,2,35,32,13
	.byte	'IBUSY',0
	.word	824
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	26661
	.byte	40,2,35,40,0,14
	.word	26670
	.byte	22
	.byte	'Ifx_SRC_ERAY',0,4,172,1,3
	.word	26797
	.byte	10
	.byte	'_Ifx_SRC_ETH',0,4,175,1,25,4,13
	.byte	'SR',0
	.word	824
	.byte	4,2,35,0,0,14
	.word	26824
	.byte	22
	.byte	'Ifx_SRC_ETH',0,4,178,1,3
	.word	26856
	.byte	10
	.byte	'_Ifx_SRC_FCE',0,4,181,1,25,4,13
	.byte	'SR',0
	.word	824
	.byte	4,2,35,0,0,14
	.word	26882
	.byte	22
	.byte	'Ifx_SRC_FCE',0,4,184,1,3
	.word	26914
	.byte	10
	.byte	'_Ifx_SRC_FFT',0,4,187,1,25,12,13
	.byte	'DONE',0
	.word	824
	.byte	4,2,35,0,13
	.byte	'ERR',0
	.word	824
	.byte	4,2,35,4,13
	.byte	'RFS',0
	.word	824
	.byte	4,2,35,8,0,14
	.word	26940
	.byte	22
	.byte	'Ifx_SRC_FFT',0,4,192,1,3
	.word	27000
	.byte	18,16
	.word	517
	.byte	19,15,0,10
	.byte	'_Ifx_SRC_GPSR',0,4,195,1,25,32,13
	.byte	'SR0',0
	.word	824
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	824
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	824
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	824
	.byte	4,2,35,12,13
	.byte	'reserved_10',0
	.word	27026
	.byte	16,2,35,16,0,14
	.word	27035
	.byte	22
	.byte	'Ifx_SRC_GPSR',0,4,202,1,3
	.word	27129
	.byte	10
	.byte	'_Ifx_SRC_GPT12',0,4,205,1,25,48,13
	.byte	'CIRQ',0
	.word	824
	.byte	4,2,35,0,13
	.byte	'T2',0
	.word	824
	.byte	4,2,35,4,13
	.byte	'T3',0
	.word	824
	.byte	4,2,35,8,13
	.byte	'T4',0
	.word	824
	.byte	4,2,35,12,13
	.byte	'T5',0
	.word	824
	.byte	4,2,35,16,13
	.byte	'T6',0
	.word	824
	.byte	4,2,35,20,13
	.byte	'reserved_18',0
	.word	4625
	.byte	24,2,35,24,0,14
	.word	27156
	.byte	22
	.byte	'Ifx_SRC_GPT12',0,4,214,1,3
	.word	27273
	.byte	18,12
	.word	824
	.byte	19,2,0,18,32
	.word	824
	.byte	19,7,0,18,32
	.word	27310
	.byte	19,0,0,18,88
	.word	517
	.byte	19,87,0,18,108
	.word	824
	.byte	19,26,0,18,96
	.word	27310
	.byte	19,2,0,18,160,3
	.word	517
	.byte	19,159,3,0,18,64
	.word	27310
	.byte	19,1,0,18,192,3
	.word	517
	.byte	19,191,3,0,18,16
	.word	824
	.byte	19,3,0,18,64
	.word	27386
	.byte	19,3,0,18,52
	.word	517
	.byte	19,51,0,10
	.byte	'_Ifx_SRC_GTM',0,4,217,1,25,204,18,13
	.byte	'AEIIRQ',0
	.word	824
	.byte	4,2,35,0,13
	.byte	'ARUIRQ',0
	.word	27301
	.byte	12,2,35,4,13
	.byte	'reserved_10',0
	.word	3435
	.byte	4,2,35,16,13
	.byte	'BRCIRQ',0
	.word	824
	.byte	4,2,35,20,13
	.byte	'CMPIRQ',0
	.word	824
	.byte	4,2,35,24,13
	.byte	'SPEIRQ',0
	.word	26187
	.byte	8,2,35,28,13
	.byte	'reserved_24',0
	.word	5254
	.byte	8,2,35,36,13
	.byte	'PSM',0
	.word	27319
	.byte	32,2,35,44,13
	.byte	'reserved_4C',0
	.word	27328
	.byte	88,2,35,76,13
	.byte	'DPLL',0
	.word	27337
	.byte	108,3,35,164,1,13
	.byte	'reserved_110',0
	.word	24843
	.byte	96,3,35,144,2,13
	.byte	'ERR',0
	.word	824
	.byte	4,3,35,240,2,13
	.byte	'reserved_174',0
	.word	5594
	.byte	12,3,35,244,2,13
	.byte	'TIM',0
	.word	27346
	.byte	96,3,35,128,3,13
	.byte	'reserved_1E0',0
	.word	27355
	.byte	160,3,3,35,224,3,13
	.byte	'MCS',0
	.word	27346
	.byte	96,3,35,128,7,13
	.byte	'reserved_3E0',0
	.word	27355
	.byte	160,3,3,35,224,7,13
	.byte	'TOM',0
	.word	27366
	.byte	64,3,35,128,11,13
	.byte	'reserved_5C0',0
	.word	27375
	.byte	192,3,3,35,192,11,13
	.byte	'ATOM',0
	.word	27395
	.byte	64,3,35,128,15,13
	.byte	'reserved_7C0',0
	.word	24893
	.byte	192,2,3,35,192,15,13
	.byte	'MCSW0',0
	.word	27301
	.byte	12,3,35,128,18,13
	.byte	'reserved_90C',0
	.word	27404
	.byte	52,3,35,140,18,13
	.byte	'MCSW1',0
	.word	27301
	.byte	12,3,35,192,18,0,14
	.word	27413
	.byte	22
	.byte	'Ifx_SRC_GTM',0,4,243,1,3
	.word	27873
	.byte	10
	.byte	'_Ifx_SRC_HSCT',0,4,246,1,25,4,13
	.byte	'SR',0
	.word	824
	.byte	4,2,35,0,0,14
	.word	27899
	.byte	22
	.byte	'Ifx_SRC_HSCT',0,4,249,1,3
	.word	27932
	.byte	10
	.byte	'_Ifx_SRC_HSSL',0,4,252,1,25,16,13
	.byte	'COK',0
	.word	824
	.byte	4,2,35,0,13
	.byte	'RDI',0
	.word	824
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	824
	.byte	4,2,35,8,13
	.byte	'TRG',0
	.word	824
	.byte	4,2,35,12,0,14
	.word	27959
	.byte	22
	.byte	'Ifx_SRC_HSSL',0,4,130,2,3
	.word	28032
	.byte	18,56
	.word	517
	.byte	19,55,0,10
	.byte	'_Ifx_SRC_I2C',0,4,133,2,25,80,13
	.byte	'BREQ',0
	.word	824
	.byte	4,2,35,0,13
	.byte	'LBREQ',0
	.word	824
	.byte	4,2,35,4,13
	.byte	'SREQ',0
	.word	824
	.byte	4,2,35,8,13
	.byte	'LSREQ',0
	.word	824
	.byte	4,2,35,12,13
	.byte	'ERR',0
	.word	824
	.byte	4,2,35,16,13
	.byte	'P',0
	.word	824
	.byte	4,2,35,20,13
	.byte	'reserved_18',0
	.word	28059
	.byte	56,2,35,24,0,14
	.word	28068
	.byte	22
	.byte	'Ifx_SRC_I2C',0,4,142,2,3
	.word	28191
	.byte	10
	.byte	'_Ifx_SRC_LMU',0,4,145,2,25,4,13
	.byte	'SR',0
	.word	824
	.byte	4,2,35,0,0,14
	.word	28217
	.byte	22
	.byte	'Ifx_SRC_LMU',0,4,148,2,3
	.word	28249
	.byte	10
	.byte	'_Ifx_SRC_MSC',0,4,151,2,25,20,13
	.byte	'SR0',0
	.word	824
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	824
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	824
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	824
	.byte	4,2,35,12,13
	.byte	'SR4',0
	.word	824
	.byte	4,2,35,16,0,14
	.word	28275
	.byte	22
	.byte	'Ifx_SRC_MSC',0,4,158,2,3
	.word	28360
	.byte	10
	.byte	'_Ifx_SRC_PMU',0,4,161,2,25,4,13
	.byte	'SR',0
	.word	824
	.byte	4,2,35,0,0,14
	.word	28386
	.byte	22
	.byte	'Ifx_SRC_PMU',0,4,164,2,3
	.word	28418
	.byte	10
	.byte	'_Ifx_SRC_PSI5',0,4,167,2,25,32,13
	.byte	'SR',0
	.word	27310
	.byte	32,2,35,0,0,14
	.word	28444
	.byte	22
	.byte	'Ifx_SRC_PSI5',0,4,170,2,3
	.word	28477
	.byte	10
	.byte	'_Ifx_SRC_PSI5S',0,4,173,2,25,32,13
	.byte	'SR',0
	.word	27310
	.byte	32,2,35,0,0,14
	.word	28504
	.byte	22
	.byte	'Ifx_SRC_PSI5S',0,4,176,2,3
	.word	28538
	.byte	10
	.byte	'_Ifx_SRC_QSPI',0,4,179,2,25,24,13
	.byte	'TX',0
	.word	824
	.byte	4,2,35,0,13
	.byte	'RX',0
	.word	824
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	824
	.byte	4,2,35,8,13
	.byte	'PT',0
	.word	824
	.byte	4,2,35,12,13
	.byte	'HC',0
	.word	824
	.byte	4,2,35,16,13
	.byte	'U',0
	.word	824
	.byte	4,2,35,20,0,14
	.word	28566
	.byte	22
	.byte	'Ifx_SRC_QSPI',0,4,187,2,3
	.word	28659
	.byte	10
	.byte	'_Ifx_SRC_SCR',0,4,190,2,25,4,13
	.byte	'SR',0
	.word	824
	.byte	4,2,35,0,0,14
	.word	28686
	.byte	22
	.byte	'Ifx_SRC_SCR',0,4,193,2,3
	.word	28718
	.byte	10
	.byte	'_Ifx_SRC_SCU',0,4,196,2,25,20,13
	.byte	'DTS',0
	.word	824
	.byte	4,2,35,0,13
	.byte	'ERU',0
	.word	27386
	.byte	16,2,35,4,0,14
	.word	28744
	.byte	22
	.byte	'Ifx_SRC_SCU',0,4,200,2,3
	.word	28790
	.byte	18,24
	.word	824
	.byte	19,5,0,10
	.byte	'_Ifx_SRC_SENT',0,4,203,2,25,24,13
	.byte	'SR',0
	.word	28816
	.byte	24,2,35,0,0,14
	.word	28825
	.byte	22
	.byte	'Ifx_SRC_SENT',0,4,206,2,3
	.word	28858
	.byte	10
	.byte	'_Ifx_SRC_SMU',0,4,209,2,25,12,13
	.byte	'SR',0
	.word	27301
	.byte	12,2,35,0,0,14
	.word	28885
	.byte	22
	.byte	'Ifx_SRC_SMU',0,4,212,2,3
	.word	28917
	.byte	10
	.byte	'_Ifx_SRC_STM',0,4,215,2,25,8,13
	.byte	'SR0',0
	.word	824
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	824
	.byte	4,2,35,4,0,14
	.word	28943
	.byte	22
	.byte	'Ifx_SRC_STM',0,4,219,2,3
	.word	28989
	.byte	10
	.byte	'_Ifx_SRC_VADCCG',0,4,222,2,25,16,13
	.byte	'SR0',0
	.word	824
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	824
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	824
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	824
	.byte	4,2,35,12,0,14
	.word	29015
	.byte	22
	.byte	'Ifx_SRC_VADCCG',0,4,228,2,3
	.word	29090
	.byte	10
	.byte	'_Ifx_SRC_VADCG',0,4,231,2,25,16,13
	.byte	'SR0',0
	.word	824
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	824
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	824
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	824
	.byte	4,2,35,12,0,14
	.word	29119
	.byte	22
	.byte	'Ifx_SRC_VADCG',0,4,237,2,3
	.word	29193
	.byte	10
	.byte	'_Ifx_SRC_XBAR',0,4,240,2,25,4,13
	.byte	'SRC',0
	.word	824
	.byte	4,2,35,0,0,14
	.word	29221
	.byte	22
	.byte	'Ifx_SRC_XBAR',0,4,243,2,3
	.word	29255
	.byte	18,4
	.word	25813
	.byte	19,0,0,14
	.word	29282
	.byte	10
	.byte	'_Ifx_SRC_GAGBT',0,4,128,3,25,4,13
	.byte	'AGBT',0
	.word	29291
	.byte	4,2,35,0,0,14
	.word	29296
	.byte	22
	.byte	'Ifx_SRC_GAGBT',0,4,131,3,3
	.word	29332
	.byte	18,48
	.word	25871
	.byte	19,3,0,14
	.word	29360
	.byte	10
	.byte	'_Ifx_SRC_GASCLIN',0,4,134,3,25,48,13
	.byte	'ASCLIN',0
	.word	29369
	.byte	48,2,35,0,0,14
	.word	29374
	.byte	22
	.byte	'Ifx_SRC_GASCLIN',0,4,137,3,3
	.word	29414
	.byte	14
	.word	25958
	.byte	10
	.byte	'_Ifx_SRC_GBCU',0,4,140,3,25,4,13
	.byte	'SPB',0
	.word	29444
	.byte	4,2,35,0,0,14
	.word	29449
	.byte	22
	.byte	'Ifx_SRC_GBCU',0,4,143,3,3
	.word	29483
	.byte	18,64
	.word	26032
	.byte	19,0,0,14
	.word	29510
	.byte	10
	.byte	'_Ifx_SRC_GCAN',0,4,146,3,25,64,13
	.byte	'CAN',0
	.word	29519
	.byte	64,2,35,0,0,14
	.word	29524
	.byte	22
	.byte	'Ifx_SRC_GCAN',0,4,149,3,3
	.word	29558
	.byte	18,32
	.word	26089
	.byte	19,1,0,14
	.word	29585
	.byte	10
	.byte	'_Ifx_SRC_GCCU6',0,4,152,3,25,32,13
	.byte	'CCU6',0
	.word	29594
	.byte	32,2,35,0,0,14
	.word	29599
	.byte	22
	.byte	'Ifx_SRC_GCCU6',0,4,155,3,3
	.word	29635
	.byte	14
	.word	26196
	.byte	10
	.byte	'_Ifx_SRC_GCERBERUS',0,4,158,3,25,8,13
	.byte	'CERBERUS',0
	.word	29663
	.byte	8,2,35,0,0,14
	.word	29668
	.byte	22
	.byte	'Ifx_SRC_GCERBERUS',0,4,161,3,3
	.word	29712
	.byte	18,16
	.word	26262
	.byte	19,0,0,14
	.word	29744
	.byte	10
	.byte	'_Ifx_SRC_GCIF',0,4,164,3,25,16,13
	.byte	'CIF',0
	.word	29753
	.byte	16,2,35,0,0,14
	.word	29758
	.byte	22
	.byte	'Ifx_SRC_GCIF',0,4,167,3,3
	.word	29792
	.byte	18,8
	.word	26361
	.byte	19,1,0,14
	.word	29819
	.byte	10
	.byte	'_Ifx_SRC_GCPU',0,4,170,3,25,8,13
	.byte	'CPU',0
	.word	29828
	.byte	8,2,35,0,0,14
	.word	29833
	.byte	22
	.byte	'Ifx_SRC_GCPU',0,4,173,3,3
	.word	29867
	.byte	18,208,1
	.word	26432
	.byte	19,0,0,14
	.word	29894
	.byte	10
	.byte	'_Ifx_SRC_GDMA',0,4,176,3,25,208,1,13
	.byte	'DMA',0
	.word	29904
	.byte	208,1,2,35,0,0,14
	.word	29909
	.byte	22
	.byte	'Ifx_SRC_GDMA',0,4,179,3,3
	.word	29945
	.byte	14
	.word	26525
	.byte	14
	.word	26525
	.byte	14
	.word	26525
	.byte	10
	.byte	'_Ifx_SRC_GDSADC',0,4,182,3,25,32,13
	.byte	'DSADC0',0
	.word	29972
	.byte	8,2,35,0,13
	.byte	'reserved_8',0
	.word	5254
	.byte	8,2,35,8,13
	.byte	'DSADC2',0
	.word	29977
	.byte	8,2,35,16,13
	.byte	'DSADC3',0
	.word	29982
	.byte	8,2,35,24,0,14
	.word	29987
	.byte	22
	.byte	'Ifx_SRC_GDSADC',0,4,188,3,3
	.word	30078
	.byte	18,4
	.word	26601
	.byte	19,0,0,14
	.word	30107
	.byte	10
	.byte	'_Ifx_SRC_GEMEM',0,4,191,3,25,4,13
	.byte	'EMEM',0
	.word	30116
	.byte	4,2,35,0,0,14
	.word	30121
	.byte	22
	.byte	'Ifx_SRC_GEMEM',0,4,194,3,3
	.word	30157
	.byte	18,80
	.word	26670
	.byte	19,0,0,14
	.word	30185
	.byte	10
	.byte	'_Ifx_SRC_GERAY',0,4,197,3,25,80,13
	.byte	'ERAY',0
	.word	30194
	.byte	80,2,35,0,0,14
	.word	30199
	.byte	22
	.byte	'Ifx_SRC_GERAY',0,4,200,3,3
	.word	30235
	.byte	18,4
	.word	26824
	.byte	19,0,0,14
	.word	30263
	.byte	10
	.byte	'_Ifx_SRC_GETH',0,4,203,3,25,4,13
	.byte	'ETH',0
	.word	30272
	.byte	4,2,35,0,0,14
	.word	30277
	.byte	22
	.byte	'Ifx_SRC_GETH',0,4,206,3,3
	.word	30311
	.byte	18,4
	.word	26882
	.byte	19,0,0,14
	.word	30338
	.byte	10
	.byte	'_Ifx_SRC_GFCE',0,4,209,3,25,4,13
	.byte	'FCE',0
	.word	30347
	.byte	4,2,35,0,0,14
	.word	30352
	.byte	22
	.byte	'Ifx_SRC_GFCE',0,4,212,3,3
	.word	30386
	.byte	18,12
	.word	26940
	.byte	19,0,0,14
	.word	30413
	.byte	10
	.byte	'_Ifx_SRC_GFFT',0,4,215,3,25,12,13
	.byte	'FFT',0
	.word	30422
	.byte	12,2,35,0,0,14
	.word	30427
	.byte	22
	.byte	'Ifx_SRC_GFFT',0,4,218,3,3
	.word	30461
	.byte	18,64
	.word	27035
	.byte	19,1,0,14
	.word	30488
	.byte	10
	.byte	'_Ifx_SRC_GGPSR',0,4,221,3,25,64,13
	.byte	'GPSR',0
	.word	30497
	.byte	64,2,35,0,0,14
	.word	30502
	.byte	22
	.byte	'Ifx_SRC_GGPSR',0,4,224,3,3
	.word	30538
	.byte	18,48
	.word	27156
	.byte	19,0,0,14
	.word	30566
	.byte	10
	.byte	'_Ifx_SRC_GGPT12',0,4,227,3,25,48,13
	.byte	'GPT12',0
	.word	30575
	.byte	48,2,35,0,0,14
	.word	30580
	.byte	22
	.byte	'Ifx_SRC_GGPT12',0,4,230,3,3
	.word	30618
	.byte	18,204,18
	.word	27413
	.byte	19,0,0,14
	.word	30647
	.byte	10
	.byte	'_Ifx_SRC_GGTM',0,4,233,3,25,204,18,13
	.byte	'GTM',0
	.word	30657
	.byte	204,18,2,35,0,0,14
	.word	30662
	.byte	22
	.byte	'Ifx_SRC_GGTM',0,4,236,3,3
	.word	30698
	.byte	18,4
	.word	27899
	.byte	19,0,0,14
	.word	30725
	.byte	10
	.byte	'_Ifx_SRC_GHSCT',0,4,239,3,25,4,13
	.byte	'HSCT',0
	.word	30734
	.byte	4,2,35,0,0,14
	.word	30739
	.byte	22
	.byte	'Ifx_SRC_GHSCT',0,4,242,3,3
	.word	30775
	.byte	18,64
	.word	27959
	.byte	19,3,0,14
	.word	30803
	.byte	10
	.byte	'_Ifx_SRC_GHSSL',0,4,245,3,25,68,13
	.byte	'HSSL',0
	.word	30812
	.byte	64,2,35,0,13
	.byte	'EXI',0
	.word	824
	.byte	4,2,35,64,0,14
	.word	30817
	.byte	22
	.byte	'Ifx_SRC_GHSSL',0,4,249,3,3
	.word	30866
	.byte	18,80
	.word	28068
	.byte	19,0,0,14
	.word	30894
	.byte	10
	.byte	'_Ifx_SRC_GI2C',0,4,252,3,25,80,13
	.byte	'I2C',0
	.word	30903
	.byte	80,2,35,0,0,14
	.word	30908
	.byte	22
	.byte	'Ifx_SRC_GI2C',0,4,255,3,3
	.word	30942
	.byte	18,4
	.word	28217
	.byte	19,0,0,14
	.word	30969
	.byte	10
	.byte	'_Ifx_SRC_GLMU',0,4,130,4,25,4,13
	.byte	'LMU',0
	.word	30978
	.byte	4,2,35,0,0,14
	.word	30983
	.byte	22
	.byte	'Ifx_SRC_GLMU',0,4,133,4,3
	.word	31017
	.byte	18,40
	.word	28275
	.byte	19,1,0,14
	.word	31044
	.byte	10
	.byte	'_Ifx_SRC_GMSC',0,4,136,4,25,40,13
	.byte	'MSC',0
	.word	31053
	.byte	40,2,35,0,0,14
	.word	31058
	.byte	22
	.byte	'Ifx_SRC_GMSC',0,4,139,4,3
	.word	31092
	.byte	18,8
	.word	28386
	.byte	19,1,0,14
	.word	31119
	.byte	10
	.byte	'_Ifx_SRC_GPMU',0,4,142,4,25,8,13
	.byte	'PMU',0
	.word	31128
	.byte	8,2,35,0,0,14
	.word	31133
	.byte	22
	.byte	'Ifx_SRC_GPMU',0,4,145,4,3
	.word	31167
	.byte	18,32
	.word	28444
	.byte	19,0,0,14
	.word	31194
	.byte	10
	.byte	'_Ifx_SRC_GPSI5',0,4,148,4,25,32,13
	.byte	'PSI5',0
	.word	31203
	.byte	32,2,35,0,0,14
	.word	31208
	.byte	22
	.byte	'Ifx_SRC_GPSI5',0,4,151,4,3
	.word	31244
	.byte	18,32
	.word	28504
	.byte	19,0,0,14
	.word	31272
	.byte	10
	.byte	'_Ifx_SRC_GPSI5S',0,4,154,4,25,32,13
	.byte	'PSI5S',0
	.word	31281
	.byte	32,2,35,0,0,14
	.word	31286
	.byte	22
	.byte	'Ifx_SRC_GPSI5S',0,4,157,4,3
	.word	31324
	.byte	18,96
	.word	28566
	.byte	19,3,0,14
	.word	31353
	.byte	10
	.byte	'_Ifx_SRC_GQSPI',0,4,160,4,25,96,13
	.byte	'QSPI',0
	.word	31362
	.byte	96,2,35,0,0,14
	.word	31367
	.byte	22
	.byte	'Ifx_SRC_GQSPI',0,4,163,4,3
	.word	31403
	.byte	18,4
	.word	28686
	.byte	19,0,0,14
	.word	31431
	.byte	10
	.byte	'_Ifx_SRC_GSCR',0,4,166,4,25,4,13
	.byte	'SCR',0
	.word	31440
	.byte	4,2,35,0,0,14
	.word	31445
	.byte	22
	.byte	'Ifx_SRC_GSCR',0,4,169,4,3
	.word	31479
	.byte	14
	.word	28744
	.byte	10
	.byte	'_Ifx_SRC_GSCU',0,4,172,4,25,20,13
	.byte	'SCU',0
	.word	31506
	.byte	20,2,35,0,0,14
	.word	31511
	.byte	22
	.byte	'Ifx_SRC_GSCU',0,4,175,4,3
	.word	31545
	.byte	18,24
	.word	28825
	.byte	19,0,0,14
	.word	31572
	.byte	10
	.byte	'_Ifx_SRC_GSENT',0,4,178,4,25,24,13
	.byte	'SENT',0
	.word	31581
	.byte	24,2,35,0,0,14
	.word	31586
	.byte	22
	.byte	'Ifx_SRC_GSENT',0,4,181,4,3
	.word	31622
	.byte	18,12
	.word	28885
	.byte	19,0,0,14
	.word	31650
	.byte	10
	.byte	'_Ifx_SRC_GSMU',0,4,184,4,25,12,13
	.byte	'SMU',0
	.word	31659
	.byte	12,2,35,0,0,14
	.word	31664
	.byte	22
	.byte	'Ifx_SRC_GSMU',0,4,187,4,3
	.word	31698
	.byte	18,16
	.word	28943
	.byte	19,1,0,14
	.word	31725
	.byte	10
	.byte	'_Ifx_SRC_GSTM',0,4,190,4,25,16,13
	.byte	'STM',0
	.word	31734
	.byte	16,2,35,0,0,14
	.word	31739
	.byte	22
	.byte	'Ifx_SRC_GSTM',0,4,193,4,3
	.word	31773
	.byte	18,64
	.word	29119
	.byte	19,3,0,14
	.word	31800
	.byte	18,224,1
	.word	517
	.byte	19,223,1,0,18,32
	.word	29015
	.byte	19,1,0,14
	.word	31825
	.byte	10
	.byte	'_Ifx_SRC_GVADC',0,4,196,4,25,192,2,13
	.byte	'G',0
	.word	31809
	.byte	64,2,35,0,13
	.byte	'reserved_40',0
	.word	31814
	.byte	224,1,2,35,64,13
	.byte	'CG',0
	.word	31834
	.byte	32,3,35,160,2,0,14
	.word	31839
	.byte	22
	.byte	'Ifx_SRC_GVADC',0,4,201,4,3
	.word	31908
	.byte	14
	.word	29221
	.byte	10
	.byte	'_Ifx_SRC_GXBAR',0,4,204,4,25,4,13
	.byte	'XBAR',0
	.word	31936
	.byte	4,2,35,0,0,14
	.word	31941
	.byte	22
	.byte	'Ifx_SRC_GXBAR',0,4,207,4,3
	.word	31977
	.byte	15,17,236,10,9,1,16
	.byte	'IfxScu_CCUCON0_CLKSEL_fBack',0,0,16
	.byte	'IfxScu_CCUCON0_CLKSEL_fPll',0,1,0,22
	.byte	'IfxScu_CCUCON0_CLKSEL',0,17,240,10,3
	.word	32005
	.byte	15,17,250,10,9,1,16
	.byte	'IfxScu_WDTCON1_IR_divBy16384',0,0,16
	.byte	'IfxScu_WDTCON1_IR_divBy256',0,1,16
	.byte	'IfxScu_WDTCON1_IR_divBy64',0,2,0,22
	.byte	'IfxScu_WDTCON1_IR',0,17,255,10,3
	.word	32102
	.byte	10
	.byte	'_Ifx_SCU_ACCEN0_Bits',0,6,45,16,4,11
	.byte	'EN0',0,1
	.word	517
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	517
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	517
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	517
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	517
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	517
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	517
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	517
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	517
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	517
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	517
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	517
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	517
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	517
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	517
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	517
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	517
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	517
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	517
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	517
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	517
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	517
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	517
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	517
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	517
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	517
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	517
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	517
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	517
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	517
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	517
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	517
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_ACCEN0_Bits',0,6,79,3
	.word	32224
	.byte	10
	.byte	'_Ifx_SCU_ACCEN1_Bits',0,6,82,16,4,11
	.byte	'reserved_0',0,4
	.word	494
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_SCU_ACCEN1_Bits',0,6,85,3
	.word	32781
	.byte	10
	.byte	'_Ifx_SCU_ARSTDIS_Bits',0,6,88,16,4,11
	.byte	'STM0DIS',0,1
	.word	517
	.byte	1,7,2,35,0,11
	.byte	'STM1DIS',0,1
	.word	517
	.byte	1,6,2,35,0,11
	.byte	'STM2DIS',0,1
	.word	517
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	494
	.byte	29,0,2,35,0,0,22
	.byte	'Ifx_SCU_ARSTDIS_Bits',0,6,94,3
	.word	32858
	.byte	10
	.byte	'_Ifx_SCU_CCUCON0_Bits',0,6,97,16,4,11
	.byte	'BAUD1DIV',0,1
	.word	517
	.byte	4,4,2,35,0,11
	.byte	'BAUD2DIV',0,1
	.word	517
	.byte	4,0,2,35,0,11
	.byte	'SRIDIV',0,1
	.word	517
	.byte	4,4,2,35,1,11
	.byte	'LPDIV',0,1
	.word	517
	.byte	4,0,2,35,1,11
	.byte	'SPBDIV',0,1
	.word	517
	.byte	4,4,2,35,2,11
	.byte	'FSI2DIV',0,1
	.word	517
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	517
	.byte	2,0,2,35,2,11
	.byte	'FSIDIV',0,1
	.word	517
	.byte	2,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	517
	.byte	2,4,2,35,3,11
	.byte	'CLKSEL',0,1
	.word	517
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	517
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	517
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_CCUCON0_Bits',0,6,111,3
	.word	32994
	.byte	10
	.byte	'_Ifx_SCU_CCUCON1_Bits',0,6,114,16,4,11
	.byte	'CANDIV',0,1
	.word	517
	.byte	4,4,2,35,0,11
	.byte	'ERAYDIV',0,1
	.word	517
	.byte	4,0,2,35,0,11
	.byte	'STMDIV',0,1
	.word	517
	.byte	4,4,2,35,1,11
	.byte	'GTMDIV',0,1
	.word	517
	.byte	4,0,2,35,1,11
	.byte	'ETHDIV',0,1
	.word	517
	.byte	4,4,2,35,2,11
	.byte	'ASCLINFDIV',0,1
	.word	517
	.byte	4,0,2,35,2,11
	.byte	'ASCLINSDIV',0,1
	.word	517
	.byte	4,4,2,35,3,11
	.byte	'INSEL',0,1
	.word	517
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	517
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	517
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_CCUCON1_Bits',0,6,126,3
	.word	33274
	.byte	10
	.byte	'_Ifx_SCU_CCUCON2_Bits',0,6,129,1,16,4,11
	.byte	'BBBDIV',0,1
	.word	517
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	494
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	517
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	517
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_CCUCON2_Bits',0,6,135,1,3
	.word	33512
	.byte	10
	.byte	'_Ifx_SCU_CCUCON3_Bits',0,6,138,1,16,4,11
	.byte	'PLLDIV',0,1
	.word	517
	.byte	6,2,2,35,0,11
	.byte	'PLLSEL',0,1
	.word	517
	.byte	2,0,2,35,0,11
	.byte	'PLLERAYDIV',0,1
	.word	517
	.byte	6,2,2,35,1,11
	.byte	'PLLERAYSEL',0,1
	.word	517
	.byte	2,0,2,35,1,11
	.byte	'SRIDIV',0,1
	.word	517
	.byte	6,2,2,35,2,11
	.byte	'SRISEL',0,1
	.word	517
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	517
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	517
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	517
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	517
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_CCUCON3_Bits',0,6,150,1,3
	.word	33640
	.byte	10
	.byte	'_Ifx_SCU_CCUCON4_Bits',0,6,153,1,16,4,11
	.byte	'SPBDIV',0,1
	.word	517
	.byte	6,2,2,35,0,11
	.byte	'SPBSEL',0,1
	.word	517
	.byte	2,0,2,35,0,11
	.byte	'GTMDIV',0,1
	.word	517
	.byte	6,2,2,35,1,11
	.byte	'GTMSEL',0,1
	.word	517
	.byte	2,0,2,35,1,11
	.byte	'STMDIV',0,1
	.word	517
	.byte	6,2,2,35,2,11
	.byte	'STMSEL',0,1
	.word	517
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	517
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	517
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	517
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	517
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_CCUCON4_Bits',0,6,165,1,3
	.word	33883
	.byte	10
	.byte	'_Ifx_SCU_CCUCON5_Bits',0,6,168,1,16,4,11
	.byte	'MAXDIV',0,1
	.word	517
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	494
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	517
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	517
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_CCUCON5_Bits',0,6,174,1,3
	.word	34118
	.byte	10
	.byte	'_Ifx_SCU_CCUCON6_Bits',0,6,177,1,16,4,11
	.byte	'CPU0DIV',0,1
	.word	517
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	494
	.byte	26,0,2,35,0,0,22
	.byte	'Ifx_SCU_CCUCON6_Bits',0,6,181,1,3
	.word	34246
	.byte	10
	.byte	'_Ifx_SCU_CCUCON7_Bits',0,6,184,1,16,4,11
	.byte	'CPU1DIV',0,1
	.word	517
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	494
	.byte	26,0,2,35,0,0,22
	.byte	'Ifx_SCU_CCUCON7_Bits',0,6,188,1,3
	.word	34346
	.byte	10
	.byte	'_Ifx_SCU_CHIPID_Bits',0,6,191,1,16,4,11
	.byte	'CHREV',0,1
	.word	517
	.byte	6,2,2,35,0,11
	.byte	'CHTEC',0,1
	.word	517
	.byte	2,0,2,35,0,11
	.byte	'CHID',0,1
	.word	517
	.byte	8,0,2,35,1,11
	.byte	'EEA',0,1
	.word	517
	.byte	1,7,2,35,2,11
	.byte	'UCODE',0,1
	.word	517
	.byte	7,0,2,35,2,11
	.byte	'FSIZE',0,1
	.word	517
	.byte	4,4,2,35,3,11
	.byte	'SP',0,1
	.word	517
	.byte	2,2,2,35,3,11
	.byte	'SEC',0,1
	.word	517
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	517
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_CHIPID_Bits',0,6,202,1,3
	.word	34446
	.byte	10
	.byte	'_Ifx_SCU_DTSCON_Bits',0,6,205,1,16,4,11
	.byte	'PWD',0,1
	.word	517
	.byte	1,7,2,35,0,11
	.byte	'START',0,1
	.word	517
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	517
	.byte	2,4,2,35,0,11
	.byte	'CAL',0,4
	.word	494
	.byte	20,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	517
	.byte	7,1,2,35,3,11
	.byte	'SLCK',0,1
	.word	517
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_DTSCON_Bits',0,6,213,1,3
	.word	34654
	.byte	10
	.byte	'_Ifx_SCU_DTSLIM_Bits',0,6,216,1,16,4,11
	.byte	'LOWER',0,2
	.word	1068
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	517
	.byte	5,1,2,35,1,11
	.byte	'LLU',0,1
	.word	517
	.byte	1,0,2,35,1,11
	.byte	'UPPER',0,2
	.word	1068
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	517
	.byte	4,2,2,35,3,11
	.byte	'SLCK',0,1
	.word	517
	.byte	1,1,2,35,3,11
	.byte	'UOF',0,1
	.word	517
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_DTSLIM_Bits',0,6,225,1,3
	.word	34819
	.byte	10
	.byte	'_Ifx_SCU_DTSSTAT_Bits',0,6,228,1,16,4,11
	.byte	'RESULT',0,2
	.word	1068
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	517
	.byte	4,2,2,35,1,11
	.byte	'RDY',0,1
	.word	517
	.byte	1,1,2,35,1,11
	.byte	'BUSY',0,1
	.word	517
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1068
	.byte	16,0,2,35,2,0,22
	.byte	'Ifx_SCU_DTSSTAT_Bits',0,6,235,1,3
	.word	35002
	.byte	10
	.byte	'_Ifx_SCU_EICR_Bits',0,6,238,1,16,4,11
	.byte	'reserved_0',0,1
	.word	517
	.byte	4,4,2,35,0,11
	.byte	'EXIS0',0,1
	.word	517
	.byte	3,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	517
	.byte	1,0,2,35,0,11
	.byte	'FEN0',0,1
	.word	517
	.byte	1,7,2,35,1,11
	.byte	'REN0',0,1
	.word	517
	.byte	1,6,2,35,1,11
	.byte	'LDEN0',0,1
	.word	517
	.byte	1,5,2,35,1,11
	.byte	'EIEN0',0,1
	.word	517
	.byte	1,4,2,35,1,11
	.byte	'INP0',0,1
	.word	517
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	494
	.byte	5,12,2,35,0,11
	.byte	'EXIS1',0,1
	.word	517
	.byte	3,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	517
	.byte	1,0,2,35,2,11
	.byte	'FEN1',0,1
	.word	517
	.byte	1,7,2,35,3,11
	.byte	'REN1',0,1
	.word	517
	.byte	1,6,2,35,3,11
	.byte	'LDEN1',0,1
	.word	517
	.byte	1,5,2,35,3,11
	.byte	'EIEN1',0,1
	.word	517
	.byte	1,4,2,35,3,11
	.byte	'INP1',0,1
	.word	517
	.byte	3,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	517
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EICR_Bits',0,6,129,2,3
	.word	35156
	.byte	10
	.byte	'_Ifx_SCU_EIFR_Bits',0,6,132,2,16,4,11
	.byte	'INTF0',0,1
	.word	517
	.byte	1,7,2,35,0,11
	.byte	'INTF1',0,1
	.word	517
	.byte	1,6,2,35,0,11
	.byte	'INTF2',0,1
	.word	517
	.byte	1,5,2,35,0,11
	.byte	'INTF3',0,1
	.word	517
	.byte	1,4,2,35,0,11
	.byte	'INTF4',0,1
	.word	517
	.byte	1,3,2,35,0,11
	.byte	'INTF5',0,1
	.word	517
	.byte	1,2,2,35,0,11
	.byte	'INTF6',0,1
	.word	517
	.byte	1,1,2,35,0,11
	.byte	'INTF7',0,1
	.word	517
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	494
	.byte	24,0,2,35,0,0,22
	.byte	'Ifx_SCU_EIFR_Bits',0,6,143,2,3
	.word	35520
	.byte	10
	.byte	'_Ifx_SCU_EMSR_Bits',0,6,146,2,16,4,11
	.byte	'POL',0,1
	.word	517
	.byte	1,7,2,35,0,11
	.byte	'MODE',0,1
	.word	517
	.byte	1,6,2,35,0,11
	.byte	'ENON',0,1
	.word	517
	.byte	1,5,2,35,0,11
	.byte	'PSEL',0,1
	.word	517
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	1068
	.byte	12,0,2,35,0,11
	.byte	'EMSF',0,1
	.word	517
	.byte	1,7,2,35,2,11
	.byte	'SEMSF',0,1
	.word	517
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	517
	.byte	6,0,2,35,2,11
	.byte	'EMSFM',0,1
	.word	517
	.byte	2,6,2,35,3,11
	.byte	'SEMSFM',0,1
	.word	517
	.byte	2,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	517
	.byte	4,0,2,35,3,0,22
	.byte	'Ifx_SCU_EMSR_Bits',0,6,159,2,3
	.word	35731
	.byte	10
	.byte	'_Ifx_SCU_ESRCFG_Bits',0,6,162,2,16,4,11
	.byte	'reserved_0',0,1
	.word	517
	.byte	7,1,2,35,0,11
	.byte	'EDCON',0,2
	.word	1068
	.byte	2,7,2,35,0,11
	.byte	'reserved_9',0,4
	.word	494
	.byte	23,0,2,35,0,0,22
	.byte	'Ifx_SCU_ESRCFG_Bits',0,6,167,2,3
	.word	35983
	.byte	10
	.byte	'_Ifx_SCU_ESROCFG_Bits',0,6,170,2,16,4,11
	.byte	'ARI',0,1
	.word	517
	.byte	1,7,2,35,0,11
	.byte	'ARC',0,1
	.word	517
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	494
	.byte	30,0,2,35,0,0,22
	.byte	'Ifx_SCU_ESROCFG_Bits',0,6,175,2,3
	.word	36101
	.byte	10
	.byte	'_Ifx_SCU_EVR13CON_Bits',0,6,178,2,16,4,11
	.byte	'reserved_0',0,4
	.word	494
	.byte	28,4,2,35,0,11
	.byte	'EVR13OFF',0,1
	.word	517
	.byte	1,3,2,35,3,11
	.byte	'BPEVR13OFF',0,1
	.word	517
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	517
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	517
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVR13CON_Bits',0,6,185,2,3
	.word	36212
	.byte	10
	.byte	'_Ifx_SCU_EVR33CON_Bits',0,6,188,2,16,4,11
	.byte	'reserved_0',0,4
	.word	494
	.byte	28,4,2,35,0,11
	.byte	'EVR33OFF',0,1
	.word	517
	.byte	1,3,2,35,3,11
	.byte	'BPEVR33OFF',0,1
	.word	517
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	517
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	517
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVR33CON_Bits',0,6,195,2,3
	.word	36375
	.byte	10
	.byte	'_Ifx_SCU_EVRADCSTAT_Bits',0,6,198,2,16,4,11
	.byte	'ADC13V',0,1
	.word	517
	.byte	8,0,2,35,0,11
	.byte	'ADC33V',0,1
	.word	517
	.byte	8,0,2,35,1,11
	.byte	'ADCSWDV',0,1
	.word	517
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	517
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	517
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVRADCSTAT_Bits',0,6,205,2,3
	.word	36538
	.byte	10
	.byte	'_Ifx_SCU_EVRDVSTAT_Bits',0,6,208,2,16,4,11
	.byte	'DVS13TRIM',0,1
	.word	517
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	517
	.byte	8,0,2,35,1,11
	.byte	'DVS33TRIM',0,1
	.word	517
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	517
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	517
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVRDVSTAT_Bits',0,6,215,2,3
	.word	36696
	.byte	10
	.byte	'_Ifx_SCU_EVRMONCTRL_Bits',0,6,218,2,16,4,11
	.byte	'EVR13OVMOD',0,1
	.word	517
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	517
	.byte	2,4,2,35,0,11
	.byte	'EVR13UVMOD',0,1
	.word	517
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	517
	.byte	2,0,2,35,0,11
	.byte	'EVR33OVMOD',0,1
	.word	517
	.byte	2,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	517
	.byte	2,4,2,35,1,11
	.byte	'EVR33UVMOD',0,1
	.word	517
	.byte	2,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	517
	.byte	2,0,2,35,1,11
	.byte	'SWDOVMOD',0,1
	.word	517
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	517
	.byte	2,4,2,35,2,11
	.byte	'SWDUVMOD',0,1
	.word	517
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,2
	.word	1068
	.byte	10,0,2,35,2,0,22
	.byte	'Ifx_SCU_EVRMONCTRL_Bits',0,6,232,2,3
	.word	36861
	.byte	10
	.byte	'_Ifx_SCU_EVROSCCTRL_Bits',0,6,235,2,16,4,11
	.byte	'OSCTRIM',0,2
	.word	1068
	.byte	10,6,2,35,0,11
	.byte	'OSCPTAT',0,1
	.word	517
	.byte	6,0,2,35,1,11
	.byte	'OSCANASEL',0,1
	.word	517
	.byte	4,4,2,35,2,11
	.byte	'HPBGTRIM',0,2
	.word	1068
	.byte	7,5,2,35,2,11
	.byte	'HPBGCLKEN',0,1
	.word	517
	.byte	1,4,2,35,3,11
	.byte	'OSC3V3',0,1
	.word	517
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	517
	.byte	2,1,2,35,3,11
	.byte	'LCK',0,1
	.word	517
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVROSCCTRL_Bits',0,6,245,2,3
	.word	37190
	.byte	10
	.byte	'_Ifx_SCU_EVROVMON_Bits',0,6,248,2,16,4,11
	.byte	'EVR13OVVAL',0,1
	.word	517
	.byte	8,0,2,35,0,11
	.byte	'EVR33OVVAL',0,1
	.word	517
	.byte	8,0,2,35,1,11
	.byte	'SWDOVVAL',0,1
	.word	517
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	517
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	517
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVROVMON_Bits',0,6,255,2,3
	.word	37411
	.byte	10
	.byte	'_Ifx_SCU_EVRRSTCON_Bits',0,6,130,3,16,4,11
	.byte	'RST13TRIM',0,1
	.word	517
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	494
	.byte	16,8,2,35,0,11
	.byte	'RST13OFF',0,1
	.word	517
	.byte	1,7,2,35,3,11
	.byte	'BPRST13OFF',0,1
	.word	517
	.byte	1,6,2,35,3,11
	.byte	'RST33OFF',0,1
	.word	517
	.byte	1,5,2,35,3,11
	.byte	'BPRST33OFF',0,1
	.word	517
	.byte	1,4,2,35,3,11
	.byte	'RSTSWDOFF',0,1
	.word	517
	.byte	1,3,2,35,3,11
	.byte	'BPRSTSWDOFF',0,1
	.word	517
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	517
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	517
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVRRSTCON_Bits',0,6,142,3,3
	.word	37574
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF1_Bits',0,6,145,3,16,4,11
	.byte	'SD5P',0,1
	.word	517
	.byte	8,0,2,35,0,11
	.byte	'SD5I',0,1
	.word	517
	.byte	8,0,2,35,1,11
	.byte	'SD5D',0,1
	.word	517
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	517
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	517
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVRSDCOEFF1_Bits',0,6,152,3,3
	.word	37846
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF2_Bits',0,6,155,3,16,4,11
	.byte	'SD33P',0,1
	.word	517
	.byte	8,0,2,35,0,11
	.byte	'SD33I',0,1
	.word	517
	.byte	8,0,2,35,1,11
	.byte	'SD33D',0,1
	.word	517
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	517
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	517
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVRSDCOEFF2_Bits',0,6,162,3,3
	.word	37999
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF3_Bits',0,6,165,3,16,4,11
	.byte	'CT5REG0',0,1
	.word	517
	.byte	8,0,2,35,0,11
	.byte	'CT5REG1',0,1
	.word	517
	.byte	8,0,2,35,1,11
	.byte	'CT5REG2',0,1
	.word	517
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	517
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	517
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVRSDCOEFF3_Bits',0,6,172,3,3
	.word	38155
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF4_Bits',0,6,175,3,16,4,11
	.byte	'CT5REG3',0,1
	.word	517
	.byte	8,0,2,35,0,11
	.byte	'CT5REG4',0,1
	.word	517
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1068
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	517
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVRSDCOEFF4_Bits',0,6,181,3,3
	.word	38317
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF5_Bits',0,6,184,3,16,4,11
	.byte	'CT33REG0',0,1
	.word	517
	.byte	8,0,2,35,0,11
	.byte	'CT33REG1',0,1
	.word	517
	.byte	8,0,2,35,1,11
	.byte	'CT33REG2',0,1
	.word	517
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	517
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	517
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVRSDCOEFF5_Bits',0,6,191,3,3
	.word	38460
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF6_Bits',0,6,194,3,16,4,11
	.byte	'CT33REG3',0,1
	.word	517
	.byte	8,0,2,35,0,11
	.byte	'CT33REG4',0,1
	.word	517
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1068
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	517
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVRSDCOEFF6_Bits',0,6,200,3,3
	.word	38625
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL1_Bits',0,6,203,3,16,4,11
	.byte	'SDFREQSPRD',0,2
	.word	1068
	.byte	16,0,2,35,0,11
	.byte	'SDFREQ',0,1
	.word	517
	.byte	8,0,2,35,2,11
	.byte	'SDSTEP',0,1
	.word	517
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	517
	.byte	2,2,2,35,3,11
	.byte	'SDSAMPLE',0,1
	.word	517
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	517
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVRSDCTRL1_Bits',0,6,211,3,3
	.word	38770
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL2_Bits',0,6,214,3,16,4,11
	.byte	'DRVP',0,1
	.word	517
	.byte	8,0,2,35,0,11
	.byte	'SDMINMAXDC',0,1
	.word	517
	.byte	8,0,2,35,1,11
	.byte	'DRVN',0,1
	.word	517
	.byte	8,0,2,35,2,11
	.byte	'SDLUT',0,1
	.word	517
	.byte	6,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	517
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	517
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVRSDCTRL2_Bits',0,6,222,3,3
	.word	38951
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL3_Bits',0,6,225,3,16,4,11
	.byte	'SDPWMPRE',0,1
	.word	517
	.byte	8,0,2,35,0,11
	.byte	'SDPID',0,1
	.word	517
	.byte	8,0,2,35,1,11
	.byte	'SDVOKLVL',0,1
	.word	517
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	517
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	517
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVRSDCTRL3_Bits',0,6,232,3,3
	.word	39125
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL4_Bits',0,6,235,3,16,4,11
	.byte	'reserved_0',0,1
	.word	517
	.byte	8,0,2,35,0,11
	.byte	'SYNCDIV',0,1
	.word	517
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	494
	.byte	20,1,2,35,0,11
	.byte	'LCK',0,1
	.word	517
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVRSDCTRL4_Bits',0,6,241,3,3
	.word	39285
	.byte	10
	.byte	'_Ifx_SCU_EVRSTAT_Bits',0,6,244,3,16,4,11
	.byte	'EVR13',0,1
	.word	517
	.byte	1,7,2,35,0,11
	.byte	'OV13',0,1
	.word	517
	.byte	1,6,2,35,0,11
	.byte	'EVR33',0,1
	.word	517
	.byte	1,5,2,35,0,11
	.byte	'OV33',0,1
	.word	517
	.byte	1,4,2,35,0,11
	.byte	'OVSWD',0,1
	.word	517
	.byte	1,3,2,35,0,11
	.byte	'UV13',0,1
	.word	517
	.byte	1,2,2,35,0,11
	.byte	'UV33',0,1
	.word	517
	.byte	1,1,2,35,0,11
	.byte	'UVSWD',0,1
	.word	517
	.byte	1,0,2,35,0,11
	.byte	'EXTPASS13',0,1
	.word	517
	.byte	1,7,2,35,1,11
	.byte	'EXTPASS33',0,1
	.word	517
	.byte	1,6,2,35,1,11
	.byte	'BGPROK',0,1
	.word	517
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	494
	.byte	21,0,2,35,0,0,22
	.byte	'Ifx_SCU_EVRSTAT_Bits',0,6,130,4,3
	.word	39429
	.byte	10
	.byte	'_Ifx_SCU_EVRTRIM_Bits',0,6,133,4,16,4,11
	.byte	'EVR13TRIM',0,1
	.word	517
	.byte	8,0,2,35,0,11
	.byte	'SDVOUTSEL',0,1
	.word	517
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1068
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	517
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVRTRIM_Bits',0,6,139,4,3
	.word	39703
	.byte	10
	.byte	'_Ifx_SCU_EVRUVMON_Bits',0,6,142,4,16,4,11
	.byte	'EVR13UVVAL',0,1
	.word	517
	.byte	8,0,2,35,0,11
	.byte	'EVR33UVVAL',0,1
	.word	517
	.byte	8,0,2,35,1,11
	.byte	'SWDUVVAL',0,1
	.word	517
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	517
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	517
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVRUVMON_Bits',0,6,149,4,3
	.word	39842
	.byte	10
	.byte	'_Ifx_SCU_EXTCON_Bits',0,6,152,4,16,4,11
	.byte	'EN0',0,1
	.word	517
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	517
	.byte	1,6,2,35,0,11
	.byte	'SEL0',0,1
	.word	517
	.byte	4,2,2,35,0,11
	.byte	'reserved_6',0,2
	.word	1068
	.byte	10,0,2,35,0,11
	.byte	'EN1',0,1
	.word	517
	.byte	1,7,2,35,2,11
	.byte	'NSEL',0,1
	.word	517
	.byte	1,6,2,35,2,11
	.byte	'SEL1',0,1
	.word	517
	.byte	4,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	517
	.byte	2,0,2,35,2,11
	.byte	'DIV1',0,1
	.word	517
	.byte	8,0,2,35,3,0,22
	.byte	'Ifx_SCU_EXTCON_Bits',0,6,163,4,3
	.word	40005
	.byte	10
	.byte	'_Ifx_SCU_FDR_Bits',0,6,166,4,16,4,11
	.byte	'STEP',0,2
	.word	1068
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	517
	.byte	4,2,2,35,1,11
	.byte	'DM',0,1
	.word	517
	.byte	2,0,2,35,1,11
	.byte	'RESULT',0,2
	.word	1068
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	517
	.byte	5,1,2,35,3,11
	.byte	'DISCLK',0,1
	.word	517
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_FDR_Bits',0,6,174,4,3
	.word	40223
	.byte	10
	.byte	'_Ifx_SCU_FMR_Bits',0,6,177,4,16,4,11
	.byte	'FS0',0,1
	.word	517
	.byte	1,7,2,35,0,11
	.byte	'FS1',0,1
	.word	517
	.byte	1,6,2,35,0,11
	.byte	'FS2',0,1
	.word	517
	.byte	1,5,2,35,0,11
	.byte	'FS3',0,1
	.word	517
	.byte	1,4,2,35,0,11
	.byte	'FS4',0,1
	.word	517
	.byte	1,3,2,35,0,11
	.byte	'FS5',0,1
	.word	517
	.byte	1,2,2,35,0,11
	.byte	'FS6',0,1
	.word	517
	.byte	1,1,2,35,0,11
	.byte	'FS7',0,1
	.word	517
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	517
	.byte	8,0,2,35,1,11
	.byte	'FC0',0,1
	.word	517
	.byte	1,7,2,35,2,11
	.byte	'FC1',0,1
	.word	517
	.byte	1,6,2,35,2,11
	.byte	'FC2',0,1
	.word	517
	.byte	1,5,2,35,2,11
	.byte	'FC3',0,1
	.word	517
	.byte	1,4,2,35,2,11
	.byte	'FC4',0,1
	.word	517
	.byte	1,3,2,35,2,11
	.byte	'FC5',0,1
	.word	517
	.byte	1,2,2,35,2,11
	.byte	'FC6',0,1
	.word	517
	.byte	1,1,2,35,2,11
	.byte	'FC7',0,1
	.word	517
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	517
	.byte	8,0,2,35,3,0,22
	.byte	'Ifx_SCU_FMR_Bits',0,6,197,4,3
	.word	40386
	.byte	10
	.byte	'_Ifx_SCU_ID_Bits',0,6,200,4,16,4,11
	.byte	'MODREV',0,1
	.word	517
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	517
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	1068
	.byte	16,0,2,35,2,0,22
	.byte	'Ifx_SCU_ID_Bits',0,6,205,4,3
	.word	40722
	.byte	10
	.byte	'_Ifx_SCU_IGCR_Bits',0,6,208,4,16,4,11
	.byte	'IPEN00',0,1
	.word	517
	.byte	1,7,2,35,0,11
	.byte	'IPEN01',0,1
	.word	517
	.byte	1,6,2,35,0,11
	.byte	'IPEN02',0,1
	.word	517
	.byte	1,5,2,35,0,11
	.byte	'IPEN03',0,1
	.word	517
	.byte	1,4,2,35,0,11
	.byte	'IPEN04',0,1
	.word	517
	.byte	1,3,2,35,0,11
	.byte	'IPEN05',0,1
	.word	517
	.byte	1,2,2,35,0,11
	.byte	'IPEN06',0,1
	.word	517
	.byte	1,1,2,35,0,11
	.byte	'IPEN07',0,1
	.word	517
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	517
	.byte	5,3,2,35,1,11
	.byte	'GEEN0',0,1
	.word	517
	.byte	1,2,2,35,1,11
	.byte	'IGP0',0,1
	.word	517
	.byte	2,0,2,35,1,11
	.byte	'IPEN10',0,1
	.word	517
	.byte	1,7,2,35,2,11
	.byte	'IPEN11',0,1
	.word	517
	.byte	1,6,2,35,2,11
	.byte	'IPEN12',0,1
	.word	517
	.byte	1,5,2,35,2,11
	.byte	'IPEN13',0,1
	.word	517
	.byte	1,4,2,35,2,11
	.byte	'IPEN14',0,1
	.word	517
	.byte	1,3,2,35,2,11
	.byte	'IPEN15',0,1
	.word	517
	.byte	1,2,2,35,2,11
	.byte	'IPEN16',0,1
	.word	517
	.byte	1,1,2,35,2,11
	.byte	'IPEN17',0,1
	.word	517
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	517
	.byte	5,3,2,35,3,11
	.byte	'GEEN1',0,1
	.word	517
	.byte	1,2,2,35,3,11
	.byte	'IGP1',0,1
	.word	517
	.byte	2,0,2,35,3,0,22
	.byte	'Ifx_SCU_IGCR_Bits',0,6,232,4,3
	.word	40829
	.byte	10
	.byte	'_Ifx_SCU_IN_Bits',0,6,235,4,16,4,11
	.byte	'P0',0,1
	.word	517
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	517
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	494
	.byte	30,0,2,35,0,0,22
	.byte	'Ifx_SCU_IN_Bits',0,6,240,4,3
	.word	41281
	.byte	10
	.byte	'_Ifx_SCU_IOCR_Bits',0,6,243,4,16,4,11
	.byte	'reserved_0',0,1
	.word	517
	.byte	4,4,2,35,0,11
	.byte	'PC0',0,1
	.word	517
	.byte	4,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	517
	.byte	4,4,2,35,1,11
	.byte	'PC1',0,1
	.word	517
	.byte	4,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1068
	.byte	16,0,2,35,2,0,22
	.byte	'Ifx_SCU_IOCR_Bits',0,6,250,4,3
	.word	41380
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL0_Bits',0,6,253,4,16,4,11
	.byte	'LBISTREQ',0,1
	.word	517
	.byte	1,7,2,35,0,11
	.byte	'LBISTREQP',0,1
	.word	517
	.byte	1,6,2,35,0,11
	.byte	'PATTERNS',0,2
	.word	1068
	.byte	14,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	1068
	.byte	16,0,2,35,2,0,22
	.byte	'Ifx_SCU_LBISTCTRL0_Bits',0,6,131,5,3
	.word	41530
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL1_Bits',0,6,134,5,16,4,11
	.byte	'SEED',0,4
	.word	494
	.byte	23,9,2,35,0,11
	.byte	'reserved_23',0,1
	.word	517
	.byte	1,0,2,35,2,11
	.byte	'SPLITSH',0,1
	.word	517
	.byte	3,5,2,35,3,11
	.byte	'BODY',0,1
	.word	517
	.byte	1,4,2,35,3,11
	.byte	'LBISTFREQU',0,1
	.word	517
	.byte	4,0,2,35,3,0,22
	.byte	'Ifx_SCU_LBISTCTRL1_Bits',0,6,141,5,3
	.word	41679
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL2_Bits',0,6,144,5,16,4,11
	.byte	'SIGNATURE',0,4
	.word	494
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	517
	.byte	7,1,2,35,3,11
	.byte	'LBISTDONE',0,1
	.word	517
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_LBISTCTRL2_Bits',0,6,149,5,3
	.word	41840
	.byte	10
	.byte	'_Ifx_SCU_LCLCON_Bits',0,6,152,5,16,4,11
	.byte	'reserved_0',0,2
	.word	1068
	.byte	16,0,2,35,0,11
	.byte	'LS',0,1
	.word	517
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,2
	.word	1068
	.byte	14,1,2,35,2,11
	.byte	'LSEN',0,1
	.word	517
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_LCLCON_Bits',0,6,158,5,3
	.word	41970
	.byte	10
	.byte	'_Ifx_SCU_LCLTEST_Bits',0,6,161,5,16,4,11
	.byte	'LCLT0',0,1
	.word	517
	.byte	1,7,2,35,0,11
	.byte	'LCLT1',0,1
	.word	517
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	494
	.byte	30,0,2,35,0,0,22
	.byte	'Ifx_SCU_LCLTEST_Bits',0,6,166,5,3
	.word	42102
	.byte	10
	.byte	'_Ifx_SCU_MANID_Bits',0,6,169,5,16,4,11
	.byte	'DEPT',0,1
	.word	517
	.byte	5,3,2,35,0,11
	.byte	'MANUF',0,2
	.word	1068
	.byte	11,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	1068
	.byte	16,0,2,35,2,0,22
	.byte	'Ifx_SCU_MANID_Bits',0,6,174,5,3
	.word	42217
	.byte	10
	.byte	'_Ifx_SCU_OMR_Bits',0,6,177,5,16,4,11
	.byte	'PS0',0,1
	.word	517
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	517
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	1068
	.byte	14,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	517
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	517
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	1068
	.byte	14,0,2,35,2,0,22
	.byte	'Ifx_SCU_OMR_Bits',0,6,185,5,3
	.word	42328
	.byte	10
	.byte	'_Ifx_SCU_OSCCON_Bits',0,6,188,5,16,4,11
	.byte	'reserved_0',0,1
	.word	517
	.byte	1,7,2,35,0,11
	.byte	'PLLLV',0,1
	.word	517
	.byte	1,6,2,35,0,11
	.byte	'OSCRES',0,1
	.word	517
	.byte	1,5,2,35,0,11
	.byte	'GAINSEL',0,1
	.word	517
	.byte	2,3,2,35,0,11
	.byte	'MODE',0,1
	.word	517
	.byte	2,1,2,35,0,11
	.byte	'SHBY',0,1
	.word	517
	.byte	1,0,2,35,0,11
	.byte	'PLLHV',0,1
	.word	517
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	517
	.byte	1,6,2,35,1,11
	.byte	'X1D',0,1
	.word	517
	.byte	1,5,2,35,1,11
	.byte	'X1DEN',0,1
	.word	517
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	517
	.byte	4,0,2,35,1,11
	.byte	'OSCVAL',0,1
	.word	517
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	517
	.byte	2,1,2,35,2,11
	.byte	'APREN',0,1
	.word	517
	.byte	1,0,2,35,2,11
	.byte	'CAP0EN',0,1
	.word	517
	.byte	1,7,2,35,3,11
	.byte	'CAP1EN',0,1
	.word	517
	.byte	1,6,2,35,3,11
	.byte	'CAP2EN',0,1
	.word	517
	.byte	1,5,2,35,3,11
	.byte	'CAP3EN',0,1
	.word	517
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	517
	.byte	4,0,2,35,3,0,22
	.byte	'Ifx_SCU_OSCCON_Bits',0,6,209,5,3
	.word	42486
	.byte	10
	.byte	'_Ifx_SCU_OUT_Bits',0,6,212,5,16,4,11
	.byte	'P0',0,1
	.word	517
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	517
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	494
	.byte	30,0,2,35,0,0,22
	.byte	'Ifx_SCU_OUT_Bits',0,6,217,5,3
	.word	42898
	.byte	10
	.byte	'_Ifx_SCU_OVCCON_Bits',0,6,220,5,16,4,11
	.byte	'CSEL0',0,1
	.word	517
	.byte	1,7,2,35,0,11
	.byte	'CSEL1',0,1
	.word	517
	.byte	1,6,2,35,0,11
	.byte	'CSEL2',0,1
	.word	517
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,2
	.word	1068
	.byte	13,0,2,35,0,11
	.byte	'OVSTRT',0,1
	.word	517
	.byte	1,7,2,35,2,11
	.byte	'OVSTP',0,1
	.word	517
	.byte	1,6,2,35,2,11
	.byte	'DCINVAL',0,1
	.word	517
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	517
	.byte	5,0,2,35,2,11
	.byte	'OVCONF',0,1
	.word	517
	.byte	1,7,2,35,3,11
	.byte	'POVCONF',0,1
	.word	517
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	517
	.byte	6,0,2,35,3,0,22
	.byte	'Ifx_SCU_OVCCON_Bits',0,6,233,5,3
	.word	42999
	.byte	10
	.byte	'_Ifx_SCU_OVCENABLE_Bits',0,6,236,5,16,4,11
	.byte	'OVEN0',0,1
	.word	517
	.byte	1,7,2,35,0,11
	.byte	'OVEN1',0,1
	.word	517
	.byte	1,6,2,35,0,11
	.byte	'OVEN2',0,1
	.word	517
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	494
	.byte	29,0,2,35,0,0,22
	.byte	'Ifx_SCU_OVCENABLE_Bits',0,6,242,5,3
	.word	43266
	.byte	10
	.byte	'_Ifx_SCU_PDISC_Bits',0,6,245,5,16,4,11
	.byte	'PDIS0',0,1
	.word	517
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	517
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	494
	.byte	30,0,2,35,0,0,22
	.byte	'Ifx_SCU_PDISC_Bits',0,6,250,5,3
	.word	43402
	.byte	10
	.byte	'_Ifx_SCU_PDR_Bits',0,6,253,5,16,4,11
	.byte	'PD0',0,1
	.word	517
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	517
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	517
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	517
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	494
	.byte	24,0,2,35,0,0,22
	.byte	'Ifx_SCU_PDR_Bits',0,6,132,6,3
	.word	43513
	.byte	10
	.byte	'_Ifx_SCU_PDRR_Bits',0,6,135,6,16,4,11
	.byte	'PDR0',0,1
	.word	517
	.byte	1,7,2,35,0,11
	.byte	'PDR1',0,1
	.word	517
	.byte	1,6,2,35,0,11
	.byte	'PDR2',0,1
	.word	517
	.byte	1,5,2,35,0,11
	.byte	'PDR3',0,1
	.word	517
	.byte	1,4,2,35,0,11
	.byte	'PDR4',0,1
	.word	517
	.byte	1,3,2,35,0,11
	.byte	'PDR5',0,1
	.word	517
	.byte	1,2,2,35,0,11
	.byte	'PDR6',0,1
	.word	517
	.byte	1,1,2,35,0,11
	.byte	'PDR7',0,1
	.word	517
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	494
	.byte	24,0,2,35,0,0,22
	.byte	'Ifx_SCU_PDRR_Bits',0,6,146,6,3
	.word	43646
	.byte	10
	.byte	'_Ifx_SCU_PLLCON0_Bits',0,6,149,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	517
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	517
	.byte	1,6,2,35,0,11
	.byte	'MODEN',0,1
	.word	517
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	517
	.byte	1,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	517
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	517
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	517
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	1068
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	517
	.byte	7,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	517
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	517
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	517
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	517
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	517
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	517
	.byte	4,0,2,35,3,0,22
	.byte	'Ifx_SCU_PLLCON0_Bits',0,6,166,6,3
	.word	43849
	.byte	10
	.byte	'_Ifx_SCU_PLLCON1_Bits',0,6,169,6,16,4,11
	.byte	'K2DIV',0,1
	.word	517
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	517
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	517
	.byte	7,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	517
	.byte	1,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	517
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	1068
	.byte	9,0,2,35,2,0,22
	.byte	'Ifx_SCU_PLLCON1_Bits',0,6,177,6,3
	.word	44205
	.byte	10
	.byte	'_Ifx_SCU_PLLCON2_Bits',0,6,180,6,16,4,11
	.byte	'MODCFG',0,2
	.word	1068
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	1068
	.byte	16,0,2,35,2,0,22
	.byte	'Ifx_SCU_PLLCON2_Bits',0,6,184,6,3
	.word	44383
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON0_Bits',0,6,187,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	517
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	517
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	517
	.byte	2,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	517
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	517
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	517
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	1068
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	517
	.byte	5,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	517
	.byte	2,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	517
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	517
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	517
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	517
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	517
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	517
	.byte	4,0,2,35,3,0,22
	.byte	'Ifx_SCU_PLLERAYCON0_Bits',0,6,204,6,3
	.word	44483
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON1_Bits',0,6,207,6,16,4,11
	.byte	'K2DIV',0,1
	.word	517
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	517
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	517
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	517
	.byte	4,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	517
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	1068
	.byte	9,0,2,35,2,0,22
	.byte	'Ifx_SCU_PLLERAYCON1_Bits',0,6,215,6,3
	.word	44853
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYSTAT_Bits',0,6,218,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	517
	.byte	1,7,2,35,0,11
	.byte	'PWDSTAT',0,1
	.word	517
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	517
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	517
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	517
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	517
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	494
	.byte	26,0,2,35,0,0,22
	.byte	'Ifx_SCU_PLLERAYSTAT_Bits',0,6,227,6,3
	.word	45039
	.byte	10
	.byte	'_Ifx_SCU_PLLSTAT_Bits',0,6,230,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	517
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	517
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	517
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	517
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	517
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	517
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	517
	.byte	1,1,2,35,0,11
	.byte	'MODRUN',0,1
	.word	517
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	494
	.byte	24,0,2,35,0,0,22
	.byte	'Ifx_SCU_PLLSTAT_Bits',0,6,241,6,3
	.word	45237
	.byte	10
	.byte	'_Ifx_SCU_PMCSR_Bits',0,6,244,6,16,4,11
	.byte	'REQSLP',0,1
	.word	517
	.byte	2,6,2,35,0,11
	.byte	'SMUSLP',0,1
	.word	517
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	517
	.byte	5,0,2,35,0,11
	.byte	'PMST',0,1
	.word	517
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	494
	.byte	21,0,2,35,0,0,22
	.byte	'Ifx_SCU_PMCSR_Bits',0,6,251,6,3
	.word	45470
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR0_Bits',0,6,254,6,16,4,11
	.byte	'reserved_0',0,1
	.word	517
	.byte	1,7,2,35,0,11
	.byte	'ESR1WKEN',0,1
	.word	517
	.byte	1,6,2,35,0,11
	.byte	'PINAWKEN',0,1
	.word	517
	.byte	1,5,2,35,0,11
	.byte	'PINBWKEN',0,1
	.word	517
	.byte	1,4,2,35,0,11
	.byte	'ESR0DFEN',0,1
	.word	517
	.byte	1,3,2,35,0,11
	.byte	'ESR0EDCON',0,1
	.word	517
	.byte	2,1,2,35,0,11
	.byte	'ESR1DFEN',0,1
	.word	517
	.byte	1,0,2,35,0,11
	.byte	'ESR1EDCON',0,1
	.word	517
	.byte	2,6,2,35,1,11
	.byte	'PINADFEN',0,1
	.word	517
	.byte	1,5,2,35,1,11
	.byte	'PINAEDCON',0,1
	.word	517
	.byte	2,3,2,35,1,11
	.byte	'PINBDFEN',0,1
	.word	517
	.byte	1,2,2,35,1,11
	.byte	'PINBEDCON',0,1
	.word	517
	.byte	2,0,2,35,1,11
	.byte	'SCREN',0,1
	.word	517
	.byte	1,7,2,35,2,11
	.byte	'STBYRAMSEL',0,1
	.word	517
	.byte	2,5,2,35,2,11
	.byte	'SCRCLKSEL',0,1
	.word	517
	.byte	1,4,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	517
	.byte	1,3,2,35,2,11
	.byte	'TRISTEN',0,1
	.word	517
	.byte	1,2,2,35,2,11
	.byte	'TRISTREQ',0,1
	.word	517
	.byte	1,1,2,35,2,11
	.byte	'PORSTDF',0,1
	.word	517
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	517
	.byte	1,7,2,35,3,11
	.byte	'DCDCSYNC',0,1
	.word	517
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	517
	.byte	3,3,2,35,3,11
	.byte	'ESR0TRIST',0,1
	.word	517
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	517
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	517
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_PMSWCR0_Bits',0,6,153,7,3
	.word	45622
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR1_Bits',0,6,156,7,16,4,11
	.byte	'SCRSTEN',0,1
	.word	517
	.byte	1,7,2,35,0,11
	.byte	'SCRSTREQ',0,1
	.word	517
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	517
	.byte	6,0,2,35,0,11
	.byte	'CPUIDLSEL',0,1
	.word	517
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	517
	.byte	1,4,2,35,1,11
	.byte	'IRADIS',0,1
	.word	517
	.byte	1,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	517
	.byte	3,0,2,35,1,11
	.byte	'SCRCFG',0,1
	.word	517
	.byte	8,0,2,35,2,11
	.byte	'CPUSEL',0,1
	.word	517
	.byte	3,5,2,35,3,11
	.byte	'STBYEVEN',0,1
	.word	517
	.byte	1,4,2,35,3,11
	.byte	'STBYEV',0,1
	.word	517
	.byte	3,1,2,35,3,11
	.byte	'LCK',0,1
	.word	517
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_PMSWCR1_Bits',0,6,170,7,3
	.word	46189
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR2_Bits',0,6,173,7,16,4,11
	.byte	'SCRINT',0,1
	.word	517
	.byte	8,0,2,35,0,11
	.byte	'BUSY',0,1
	.word	517
	.byte	1,7,2,35,1,11
	.byte	'SCRECC',0,1
	.word	517
	.byte	1,6,2,35,1,11
	.byte	'SCRWDT',0,1
	.word	517
	.byte	1,5,2,35,1,11
	.byte	'SCRRST',0,1
	.word	517
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	517
	.byte	4,0,2,35,1,11
	.byte	'TCINT',0,1
	.word	517
	.byte	8,0,2,35,2,11
	.byte	'TCINTREQ',0,1
	.word	517
	.byte	1,7,2,35,3,11
	.byte	'SMURST',0,1
	.word	517
	.byte	1,6,2,35,3,11
	.byte	'RST',0,1
	.word	517
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	517
	.byte	4,1,2,35,3,11
	.byte	'LCK',0,1
	.word	517
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_PMSWCR2_Bits',0,6,187,7,3
	.word	46483
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTAT_Bits',0,6,190,7,16,4,11
	.byte	'reserved_0',0,1
	.word	517
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKP',0,1
	.word	517
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUN',0,1
	.word	517
	.byte	1,4,2,35,0,11
	.byte	'PINAWKP',0,1
	.word	517
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUN',0,1
	.word	517
	.byte	1,2,2,35,0,11
	.byte	'PINBWKP',0,1
	.word	517
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUN',0,1
	.word	517
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	517
	.byte	1,7,2,35,1,11
	.byte	'PORSTDF',0,1
	.word	517
	.byte	1,6,2,35,1,11
	.byte	'HWCFGEVR',0,1
	.word	517
	.byte	3,3,2,35,1,11
	.byte	'STBYRAM',0,1
	.word	517
	.byte	2,1,2,35,1,11
	.byte	'TRIST',0,1
	.word	517
	.byte	1,0,2,35,1,11
	.byte	'SCRST',0,1
	.word	517
	.byte	1,7,2,35,2,11
	.byte	'SCRWKP',0,1
	.word	517
	.byte	1,6,2,35,2,11
	.byte	'SCR',0,1
	.word	517
	.byte	1,5,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	517
	.byte	1,4,2,35,2,11
	.byte	'ESR1WKEN',0,1
	.word	517
	.byte	1,3,2,35,2,11
	.byte	'PINAWKEN',0,1
	.word	517
	.byte	1,2,2,35,2,11
	.byte	'PINBWKEN',0,1
	.word	517
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	1068
	.byte	4,5,2,35,2,11
	.byte	'ESR0TRIST',0,1
	.word	517
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	517
	.byte	4,0,2,35,3,0,22
	.byte	'Ifx_SCU_PMSWSTAT_Bits',0,6,214,7,3
	.word	46761
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTATCLR_Bits',0,6,217,7,16,4,11
	.byte	'reserved_0',0,1
	.word	517
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKPCLR',0,1
	.word	517
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUNCLR',0,1
	.word	517
	.byte	1,4,2,35,0,11
	.byte	'PINAWKPCLR',0,1
	.word	517
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUNCLR',0,1
	.word	517
	.byte	1,2,2,35,0,11
	.byte	'PINBWKPCLR',0,1
	.word	517
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUNCLR',0,1
	.word	517
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	517
	.byte	8,0,2,35,1,11
	.byte	'SCRSTCLR',0,1
	.word	517
	.byte	1,7,2,35,2,11
	.byte	'SCRWKPCLR',0,1
	.word	517
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	1068
	.byte	14,0,2,35,2,0,22
	.byte	'Ifx_SCU_PMSWSTATCLR_Bits',0,6,230,7,3
	.word	47257
	.byte	10
	.byte	'_Ifx_SCU_RSTCON2_Bits',0,6,233,7,16,4,11
	.byte	'reserved_0',0,1
	.word	517
	.byte	1,7,2,35,0,11
	.byte	'CLRC',0,1
	.word	517
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	1068
	.byte	10,4,2,35,0,11
	.byte	'CSS0',0,1
	.word	517
	.byte	1,3,2,35,1,11
	.byte	'CSS1',0,1
	.word	517
	.byte	1,2,2,35,1,11
	.byte	'CSS2',0,1
	.word	517
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	517
	.byte	1,0,2,35,1,11
	.byte	'USRINFO',0,2
	.word	1068
	.byte	16,0,2,35,2,0,22
	.byte	'Ifx_SCU_RSTCON2_Bits',0,6,243,7,3
	.word	47570
	.byte	10
	.byte	'_Ifx_SCU_RSTCON_Bits',0,6,246,7,16,4,11
	.byte	'ESR0',0,1
	.word	517
	.byte	2,6,2,35,0,11
	.byte	'ESR1',0,1
	.word	517
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	517
	.byte	2,2,2,35,0,11
	.byte	'SMU',0,1
	.word	517
	.byte	2,0,2,35,0,11
	.byte	'SW',0,1
	.word	517
	.byte	2,6,2,35,1,11
	.byte	'STM0',0,1
	.word	517
	.byte	2,4,2,35,1,11
	.byte	'STM1',0,1
	.word	517
	.byte	2,2,2,35,1,11
	.byte	'STM2',0,1
	.word	517
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1068
	.byte	16,0,2,35,2,0,22
	.byte	'Ifx_SCU_RSTCON_Bits',0,6,129,8,3
	.word	47779
	.byte	10
	.byte	'_Ifx_SCU_RSTSTAT_Bits',0,6,132,8,16,4,11
	.byte	'ESR0',0,1
	.word	517
	.byte	1,7,2,35,0,11
	.byte	'ESR1',0,1
	.word	517
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	517
	.byte	1,5,2,35,0,11
	.byte	'SMU',0,1
	.word	517
	.byte	1,4,2,35,0,11
	.byte	'SW',0,1
	.word	517
	.byte	1,3,2,35,0,11
	.byte	'STM0',0,1
	.word	517
	.byte	1,2,2,35,0,11
	.byte	'STM1',0,1
	.word	517
	.byte	1,1,2,35,0,11
	.byte	'STM2',0,1
	.word	517
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	517
	.byte	8,0,2,35,1,11
	.byte	'PORST',0,1
	.word	517
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	517
	.byte	1,6,2,35,2,11
	.byte	'CB0',0,1
	.word	517
	.byte	1,5,2,35,2,11
	.byte	'CB1',0,1
	.word	517
	.byte	1,4,2,35,2,11
	.byte	'CB3',0,1
	.word	517
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	517
	.byte	2,1,2,35,2,11
	.byte	'EVR13',0,1
	.word	517
	.byte	1,0,2,35,2,11
	.byte	'EVR33',0,1
	.word	517
	.byte	1,7,2,35,3,11
	.byte	'SWD',0,1
	.word	517
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	517
	.byte	2,4,2,35,3,11
	.byte	'STBYR',0,1
	.word	517
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	517
	.byte	3,0,2,35,3,0,22
	.byte	'Ifx_SCU_RSTSTAT_Bits',0,6,155,8,3
	.word	47990
	.byte	10
	.byte	'_Ifx_SCU_SAFECON_Bits',0,6,158,8,16,4,11
	.byte	'HBT',0,1
	.word	517
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	494
	.byte	31,0,2,35,0,0,22
	.byte	'Ifx_SCU_SAFECON_Bits',0,6,162,8,3
	.word	48422
	.byte	10
	.byte	'_Ifx_SCU_STSTAT_Bits',0,6,165,8,16,4,11
	.byte	'HWCFG',0,1
	.word	517
	.byte	8,0,2,35,0,11
	.byte	'FTM',0,1
	.word	517
	.byte	7,1,2,35,1,11
	.byte	'MODE',0,1
	.word	517
	.byte	1,0,2,35,1,11
	.byte	'FCBAE',0,1
	.word	517
	.byte	1,7,2,35,2,11
	.byte	'LUDIS',0,1
	.word	517
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	517
	.byte	1,5,2,35,2,11
	.byte	'TRSTL',0,1
	.word	517
	.byte	1,4,2,35,2,11
	.byte	'SPDEN',0,1
	.word	517
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	517
	.byte	3,0,2,35,2,11
	.byte	'RAMINT',0,1
	.word	517
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	517
	.byte	7,0,2,35,3,0,22
	.byte	'Ifx_SCU_STSTAT_Bits',0,6,178,8,3
	.word	48518
	.byte	10
	.byte	'_Ifx_SCU_SWRSTCON_Bits',0,6,181,8,16,4,11
	.byte	'reserved_0',0,1
	.word	517
	.byte	1,7,2,35,0,11
	.byte	'SWRSTREQ',0,1
	.word	517
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	494
	.byte	30,0,2,35,0,0,22
	.byte	'Ifx_SCU_SWRSTCON_Bits',0,6,186,8,3
	.word	48778
	.byte	10
	.byte	'_Ifx_SCU_SYSCON_Bits',0,6,189,8,16,4,11
	.byte	'CCTRIG0',0,1
	.word	517
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	517
	.byte	1,6,2,35,0,11
	.byte	'RAMINTM',0,1
	.word	517
	.byte	2,4,2,35,0,11
	.byte	'SETLUDIS',0,1
	.word	517
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	517
	.byte	3,0,2,35,0,11
	.byte	'DATM',0,1
	.word	517
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,4
	.word	494
	.byte	23,0,2,35,0,0,22
	.byte	'Ifx_SCU_SYSCON_Bits',0,6,198,8,3
	.word	48903
	.byte	10
	.byte	'_Ifx_SCU_TRAPCLR_Bits',0,6,201,8,16,4,11
	.byte	'ESR0T',0,1
	.word	517
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	517
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	517
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	517
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	494
	.byte	28,0,2,35,0,0,22
	.byte	'Ifx_SCU_TRAPCLR_Bits',0,6,208,8,3
	.word	49100
	.byte	10
	.byte	'_Ifx_SCU_TRAPDIS_Bits',0,6,211,8,16,4,11
	.byte	'ESR0T',0,1
	.word	517
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	517
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	517
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	517
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	494
	.byte	28,0,2,35,0,0,22
	.byte	'Ifx_SCU_TRAPDIS_Bits',0,6,218,8,3
	.word	49253
	.byte	10
	.byte	'_Ifx_SCU_TRAPSET_Bits',0,6,221,8,16,4,11
	.byte	'ESR0T',0,1
	.word	517
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	517
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	517
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	517
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	494
	.byte	28,0,2,35,0,0,22
	.byte	'Ifx_SCU_TRAPSET_Bits',0,6,228,8,3
	.word	49406
	.byte	10
	.byte	'_Ifx_SCU_TRAPSTAT_Bits',0,6,231,8,16,4,11
	.byte	'ESR0T',0,1
	.word	517
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	517
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	517
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	517
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	494
	.byte	28,0,2,35,0,0,22
	.byte	'Ifx_SCU_TRAPSTAT_Bits',0,6,238,8,3
	.word	49559
	.byte	22
	.byte	'Ifx_SCU_WDTCPU_CON0_Bits',0,6,247,8,3
	.word	932
	.byte	22
	.byte	'Ifx_SCU_WDTCPU_CON1_Bits',0,6,134,9,3
	.word	1090
	.byte	22
	.byte	'Ifx_SCU_WDTCPU_SR_Bits',0,6,150,9,3
	.word	1334
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON0_Bits',0,6,153,9,16,4,11
	.byte	'ENDINIT',0,4
	.word	916
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	916
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	916
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	916
	.byte	16,0,2,35,0,0,22
	.byte	'Ifx_SCU_WDTS_CON0_Bits',0,6,159,9,3
	.word	49814
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON1_Bits',0,6,162,9,16,4,11
	.byte	'CLRIRF',0,1
	.word	517
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	517
	.byte	1,6,2,35,0,11
	.byte	'IR0',0,1
	.word	517
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	517
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	517
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	517
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	517
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	517
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	517
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	517
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1068
	.byte	16,0,2,35,2,0,22
	.byte	'Ifx_SCU_WDTS_CON1_Bits',0,6,175,9,3
	.word	49940
	.byte	10
	.byte	'_Ifx_SCU_WDTS_SR_Bits',0,6,178,9,16,4,11
	.byte	'AE',0,1
	.word	517
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	517
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	517
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	517
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	517
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	517
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	517
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	517
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	517
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	517
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	1068
	.byte	16,0,2,35,2,0,22
	.byte	'Ifx_SCU_WDTS_SR_Bits',0,6,191,9,3
	.word	50192
	.byte	12,6,199,9,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32224
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_ACCEN0',0,6,204,9,3
	.word	50411
	.byte	12,6,207,9,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32781
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_ACCEN1',0,6,212,9,3
	.word	50475
	.byte	12,6,215,9,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32858
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_ARSTDIS',0,6,220,9,3
	.word	50539
	.byte	12,6,223,9,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32994
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_CCUCON0',0,6,228,9,3
	.word	50604
	.byte	12,6,231,9,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33274
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_CCUCON1',0,6,236,9,3
	.word	50669
	.byte	12,6,239,9,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33512
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_CCUCON2',0,6,244,9,3
	.word	50734
	.byte	12,6,247,9,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33640
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_CCUCON3',0,6,252,9,3
	.word	50799
	.byte	12,6,255,9,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33883
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_CCUCON4',0,6,132,10,3
	.word	50864
	.byte	12,6,135,10,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34118
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_CCUCON5',0,6,140,10,3
	.word	50929
	.byte	12,6,143,10,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34246
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_CCUCON6',0,6,148,10,3
	.word	50994
	.byte	12,6,151,10,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34346
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_CCUCON7',0,6,156,10,3
	.word	51059
	.byte	12,6,159,10,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34446
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_CHIPID',0,6,164,10,3
	.word	51124
	.byte	12,6,167,10,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34654
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_DTSCON',0,6,172,10,3
	.word	51188
	.byte	12,6,175,10,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34819
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_DTSLIM',0,6,180,10,3
	.word	51252
	.byte	12,6,183,10,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35002
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_DTSSTAT',0,6,188,10,3
	.word	51316
	.byte	12,6,191,10,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35156
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EICR',0,6,196,10,3
	.word	51381
	.byte	12,6,199,10,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35520
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EIFR',0,6,204,10,3
	.word	51443
	.byte	12,6,207,10,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35731
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EMSR',0,6,212,10,3
	.word	51505
	.byte	12,6,215,10,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35983
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_ESRCFG',0,6,220,10,3
	.word	51567
	.byte	12,6,223,10,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36101
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_ESROCFG',0,6,228,10,3
	.word	51631
	.byte	12,6,231,10,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36212
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVR13CON',0,6,236,10,3
	.word	51696
	.byte	12,6,239,10,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36375
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVR33CON',0,6,244,10,3
	.word	51762
	.byte	12,6,247,10,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36538
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVRADCSTAT',0,6,252,10,3
	.word	51828
	.byte	12,6,255,10,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36696
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVRDVSTAT',0,6,132,11,3
	.word	51896
	.byte	12,6,135,11,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36861
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVRMONCTRL',0,6,140,11,3
	.word	51963
	.byte	12,6,143,11,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37190
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVROSCCTRL',0,6,148,11,3
	.word	52031
	.byte	12,6,151,11,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37411
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVROVMON',0,6,156,11,3
	.word	52099
	.byte	12,6,159,11,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37574
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVRRSTCON',0,6,164,11,3
	.word	52165
	.byte	12,6,167,11,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37846
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVRSDCOEFF1',0,6,172,11,3
	.word	52232
	.byte	12,6,175,11,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37999
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVRSDCOEFF2',0,6,180,11,3
	.word	52301
	.byte	12,6,183,11,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38155
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVRSDCOEFF3',0,6,188,11,3
	.word	52370
	.byte	12,6,191,11,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38317
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVRSDCOEFF4',0,6,196,11,3
	.word	52439
	.byte	12,6,199,11,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38460
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVRSDCOEFF5',0,6,204,11,3
	.word	52508
	.byte	12,6,207,11,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38625
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVRSDCOEFF6',0,6,212,11,3
	.word	52577
	.byte	12,6,215,11,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38770
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVRSDCTRL1',0,6,220,11,3
	.word	52646
	.byte	12,6,223,11,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38951
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVRSDCTRL2',0,6,228,11,3
	.word	52714
	.byte	12,6,231,11,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39125
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVRSDCTRL3',0,6,236,11,3
	.word	52782
	.byte	12,6,239,11,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39285
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVRSDCTRL4',0,6,244,11,3
	.word	52850
	.byte	12,6,247,11,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39429
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVRSTAT',0,6,252,11,3
	.word	52918
	.byte	12,6,255,11,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39703
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVRTRIM',0,6,132,12,3
	.word	52983
	.byte	12,6,135,12,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39842
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVRUVMON',0,6,140,12,3
	.word	53048
	.byte	12,6,143,12,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40005
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EXTCON',0,6,148,12,3
	.word	53114
	.byte	12,6,151,12,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40223
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_FDR',0,6,156,12,3
	.word	53178
	.byte	12,6,159,12,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40386
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_FMR',0,6,164,12,3
	.word	53239
	.byte	12,6,167,12,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40722
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_ID',0,6,172,12,3
	.word	53300
	.byte	12,6,175,12,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40829
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_IGCR',0,6,180,12,3
	.word	53360
	.byte	12,6,183,12,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41281
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_IN',0,6,188,12,3
	.word	53422
	.byte	12,6,191,12,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41380
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_IOCR',0,6,196,12,3
	.word	53482
	.byte	12,6,199,12,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41530
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_LBISTCTRL0',0,6,204,12,3
	.word	53544
	.byte	12,6,207,12,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41679
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_LBISTCTRL1',0,6,212,12,3
	.word	53612
	.byte	12,6,215,12,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41840
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_LBISTCTRL2',0,6,220,12,3
	.word	53680
	.byte	12,6,223,12,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41970
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_LCLCON',0,6,228,12,3
	.word	53748
	.byte	12,6,231,12,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42102
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_LCLTEST',0,6,236,12,3
	.word	53812
	.byte	12,6,239,12,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42217
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_MANID',0,6,244,12,3
	.word	53877
	.byte	12,6,247,12,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42328
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_OMR',0,6,252,12,3
	.word	53940
	.byte	12,6,255,12,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42486
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_OSCCON',0,6,132,13,3
	.word	54001
	.byte	12,6,135,13,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42898
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_OUT',0,6,140,13,3
	.word	54065
	.byte	12,6,143,13,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42999
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_OVCCON',0,6,148,13,3
	.word	54126
	.byte	12,6,151,13,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43266
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_OVCENABLE',0,6,156,13,3
	.word	54190
	.byte	12,6,159,13,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43402
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_PDISC',0,6,164,13,3
	.word	54257
	.byte	12,6,167,13,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43513
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_PDR',0,6,172,13,3
	.word	54320
	.byte	12,6,175,13,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43646
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_PDRR',0,6,180,13,3
	.word	54381
	.byte	12,6,183,13,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43849
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_PLLCON0',0,6,188,13,3
	.word	54443
	.byte	12,6,191,13,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44205
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_PLLCON1',0,6,196,13,3
	.word	54508
	.byte	12,6,199,13,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44383
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_PLLCON2',0,6,204,13,3
	.word	54573
	.byte	12,6,207,13,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44483
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_PLLERAYCON0',0,6,212,13,3
	.word	54638
	.byte	12,6,215,13,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44853
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_PLLERAYCON1',0,6,220,13,3
	.word	54707
	.byte	12,6,223,13,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45039
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_PLLERAYSTAT',0,6,228,13,3
	.word	54776
	.byte	12,6,231,13,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45237
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_PLLSTAT',0,6,236,13,3
	.word	54845
	.byte	12,6,239,13,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45470
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_PMCSR',0,6,244,13,3
	.word	54910
	.byte	12,6,247,13,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45622
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_PMSWCR0',0,6,252,13,3
	.word	54973
	.byte	12,6,255,13,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46189
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_PMSWCR1',0,6,132,14,3
	.word	55038
	.byte	12,6,135,14,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46483
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_PMSWCR2',0,6,140,14,3
	.word	55103
	.byte	12,6,143,14,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46761
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_PMSWSTAT',0,6,148,14,3
	.word	55168
	.byte	12,6,151,14,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47257
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_PMSWSTATCLR',0,6,156,14,3
	.word	55234
	.byte	12,6,159,14,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47779
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_RSTCON',0,6,164,14,3
	.word	55303
	.byte	12,6,167,14,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47570
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_RSTCON2',0,6,172,14,3
	.word	55367
	.byte	12,6,175,14,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47990
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_RSTSTAT',0,6,180,14,3
	.word	55432
	.byte	12,6,183,14,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48422
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_SAFECON',0,6,188,14,3
	.word	55497
	.byte	12,6,191,14,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48518
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_STSTAT',0,6,196,14,3
	.word	55562
	.byte	12,6,199,14,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48778
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_SWRSTCON',0,6,204,14,3
	.word	55626
	.byte	12,6,207,14,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48903
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_SYSCON',0,6,212,14,3
	.word	55692
	.byte	12,6,215,14,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49100
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_TRAPCLR',0,6,220,14,3
	.word	55756
	.byte	12,6,223,14,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49253
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_TRAPDIS',0,6,228,14,3
	.word	55821
	.byte	12,6,231,14,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49406
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_TRAPSET',0,6,236,14,3
	.word	55886
	.byte	12,6,239,14,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49559
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_TRAPSTAT',0,6,244,14,3
	.word	55951
	.byte	22
	.byte	'Ifx_SCU_WDTCPU_CON0',0,6,252,14,3
	.word	1028
	.byte	22
	.byte	'Ifx_SCU_WDTCPU_CON1',0,6,132,15,3
	.word	1294
	.byte	22
	.byte	'Ifx_SCU_WDTCPU_SR',0,6,140,15,3
	.word	1525
	.byte	12,6,143,15,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49814
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_WDTS_CON0',0,6,148,15,3
	.word	56102
	.byte	12,6,151,15,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49940
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_WDTS_CON1',0,6,156,15,3
	.word	56169
	.byte	12,6,159,15,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50192
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_WDTS_SR',0,6,164,15,3
	.word	56236
	.byte	14
	.word	1565
	.byte	22
	.byte	'Ifx_SCU_WDTCPU',0,6,180,15,3
	.word	56301
	.byte	10
	.byte	'_Ifx_SCU_WDTS',0,6,183,15,25,12,13
	.byte	'CON0',0
	.word	56102
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	56169
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	56236
	.byte	4,2,35,8,0,14
	.word	56330
	.byte	22
	.byte	'Ifx_SCU_WDTS',0,6,188,15,3
	.word	56391
	.byte	18,8
	.word	51567
	.byte	19,1,0,18,8
	.word	54910
	.byte	19,1,0,14
	.word	56330
	.byte	18,24
	.word	1565
	.byte	19,1,0,14
	.word	56441
	.byte	18,16
	.word	51381
	.byte	19,3,0,18,16
	.word	53360
	.byte	19,3,0,18,180,3
	.word	517
	.byte	19,179,3,0,10
	.byte	'_Ifx_SCU',0,6,201,15,25,128,8,13
	.byte	'reserved_0',0
	.word	5254
	.byte	8,2,35,0,13
	.byte	'ID',0
	.word	53300
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	3435
	.byte	4,2,35,12,13
	.byte	'OSCCON',0
	.word	54001
	.byte	4,2,35,16,13
	.byte	'PLLSTAT',0
	.word	54845
	.byte	4,2,35,20,13
	.byte	'PLLCON0',0
	.word	54443
	.byte	4,2,35,24,13
	.byte	'PLLCON1',0
	.word	54508
	.byte	4,2,35,28,13
	.byte	'PLLCON2',0
	.word	54573
	.byte	4,2,35,32,13
	.byte	'PLLERAYSTAT',0
	.word	54776
	.byte	4,2,35,36,13
	.byte	'PLLERAYCON0',0
	.word	54638
	.byte	4,2,35,40,13
	.byte	'PLLERAYCON1',0
	.word	54707
	.byte	4,2,35,44,13
	.byte	'CCUCON0',0
	.word	50604
	.byte	4,2,35,48,13
	.byte	'CCUCON1',0
	.word	50669
	.byte	4,2,35,52,13
	.byte	'FDR',0
	.word	53178
	.byte	4,2,35,56,13
	.byte	'EXTCON',0
	.word	53114
	.byte	4,2,35,60,13
	.byte	'CCUCON2',0
	.word	50734
	.byte	4,2,35,64,13
	.byte	'CCUCON3',0
	.word	50799
	.byte	4,2,35,68,13
	.byte	'CCUCON4',0
	.word	50864
	.byte	4,2,35,72,13
	.byte	'CCUCON5',0
	.word	50929
	.byte	4,2,35,76,13
	.byte	'RSTSTAT',0
	.word	55432
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	3435
	.byte	4,2,35,84,13
	.byte	'RSTCON',0
	.word	55303
	.byte	4,2,35,88,13
	.byte	'ARSTDIS',0
	.word	50539
	.byte	4,2,35,92,13
	.byte	'SWRSTCON',0
	.word	55626
	.byte	4,2,35,96,13
	.byte	'RSTCON2',0
	.word	55367
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	3435
	.byte	4,2,35,104,13
	.byte	'EVRRSTCON',0
	.word	52165
	.byte	4,2,35,108,13
	.byte	'ESRCFG',0
	.word	56418
	.byte	8,2,35,112,13
	.byte	'ESROCFG',0
	.word	51631
	.byte	4,2,35,120,13
	.byte	'SYSCON',0
	.word	55692
	.byte	4,2,35,124,13
	.byte	'CCUCON6',0
	.word	50994
	.byte	4,3,35,128,1,13
	.byte	'CCUCON7',0
	.word	51059
	.byte	4,3,35,132,1,13
	.byte	'reserved_88',0
	.word	24793
	.byte	20,3,35,136,1,13
	.byte	'PDR',0
	.word	54320
	.byte	4,3,35,156,1,13
	.byte	'IOCR',0
	.word	53482
	.byte	4,3,35,160,1,13
	.byte	'OUT',0
	.word	54065
	.byte	4,3,35,164,1,13
	.byte	'OMR',0
	.word	53940
	.byte	4,3,35,168,1,13
	.byte	'IN',0
	.word	53422
	.byte	4,3,35,172,1,13
	.byte	'EVRSTAT',0
	.word	52918
	.byte	4,3,35,176,1,13
	.byte	'EVRDVSTAT',0
	.word	51896
	.byte	4,3,35,180,1,13
	.byte	'EVR13CON',0
	.word	51696
	.byte	4,3,35,184,1,13
	.byte	'EVR33CON',0
	.word	51762
	.byte	4,3,35,188,1,13
	.byte	'STSTAT',0
	.word	55562
	.byte	4,3,35,192,1,13
	.byte	'reserved_C4',0
	.word	3435
	.byte	4,3,35,196,1,13
	.byte	'PMSWCR0',0
	.word	54973
	.byte	4,3,35,200,1,13
	.byte	'PMSWSTAT',0
	.word	55168
	.byte	4,3,35,204,1,13
	.byte	'PMSWSTATCLR',0
	.word	55234
	.byte	4,3,35,208,1,13
	.byte	'PMCSR',0
	.word	56427
	.byte	8,3,35,212,1,13
	.byte	'reserved_DC',0
	.word	3435
	.byte	4,3,35,220,1,13
	.byte	'DTSSTAT',0
	.word	51316
	.byte	4,3,35,224,1,13
	.byte	'DTSCON',0
	.word	51188
	.byte	4,3,35,228,1,13
	.byte	'PMSWCR1',0
	.word	55038
	.byte	4,3,35,232,1,13
	.byte	'PMSWCR2',0
	.word	55103
	.byte	4,3,35,236,1,13
	.byte	'WDTS',0
	.word	56436
	.byte	12,3,35,240,1,13
	.byte	'EMSR',0
	.word	51505
	.byte	4,3,35,252,1,13
	.byte	'WDTCPU',0
	.word	56450
	.byte	24,3,35,128,2,13
	.byte	'reserved_118',0
	.word	5594
	.byte	12,3,35,152,2,13
	.byte	'TRAPSTAT',0
	.word	55951
	.byte	4,3,35,164,2,13
	.byte	'TRAPSET',0
	.word	55886
	.byte	4,3,35,168,2,13
	.byte	'TRAPCLR',0
	.word	55756
	.byte	4,3,35,172,2,13
	.byte	'TRAPDIS',0
	.word	55821
	.byte	4,3,35,176,2,13
	.byte	'reserved_134',0
	.word	3435
	.byte	4,3,35,180,2,13
	.byte	'LCLCON1',0
	.word	53748
	.byte	4,3,35,184,2,13
	.byte	'LCLTEST',0
	.word	53812
	.byte	4,3,35,188,2,13
	.byte	'CHIPID',0
	.word	51124
	.byte	4,3,35,192,2,13
	.byte	'MANID',0
	.word	53877
	.byte	4,3,35,196,2,13
	.byte	'reserved_148',0
	.word	5254
	.byte	8,3,35,200,2,13
	.byte	'SAFECON',0
	.word	55497
	.byte	4,3,35,208,2,13
	.byte	'reserved_154',0
	.word	27026
	.byte	16,3,35,212,2,13
	.byte	'LBISTCTRL0',0
	.word	53544
	.byte	4,3,35,228,2,13
	.byte	'LBISTCTRL1',0
	.word	53612
	.byte	4,3,35,232,2,13
	.byte	'LBISTCTRL2',0
	.word	53680
	.byte	4,3,35,236,2,13
	.byte	'reserved_170',0
	.word	24802
	.byte	28,3,35,240,2,13
	.byte	'PDISC',0
	.word	54257
	.byte	4,3,35,140,3,13
	.byte	'reserved_190',0
	.word	5254
	.byte	8,3,35,144,3,13
	.byte	'EVRTRIM',0
	.word	52983
	.byte	4,3,35,152,3,13
	.byte	'EVRADCSTAT',0
	.word	51828
	.byte	4,3,35,156,3,13
	.byte	'EVRUVMON',0
	.word	53048
	.byte	4,3,35,160,3,13
	.byte	'EVROVMON',0
	.word	52099
	.byte	4,3,35,164,3,13
	.byte	'EVRMONCTRL',0
	.word	51963
	.byte	4,3,35,168,3,13
	.byte	'reserved_1AC',0
	.word	3435
	.byte	4,3,35,172,3,13
	.byte	'EVRSDCTRL1',0
	.word	52646
	.byte	4,3,35,176,3,13
	.byte	'EVRSDCTRL2',0
	.word	52714
	.byte	4,3,35,180,3,13
	.byte	'EVRSDCTRL3',0
	.word	52782
	.byte	4,3,35,184,3,13
	.byte	'EVRSDCTRL4',0
	.word	52850
	.byte	4,3,35,188,3,13
	.byte	'EVRSDCOEFF1',0
	.word	52232
	.byte	4,3,35,192,3,13
	.byte	'EVRSDCOEFF2',0
	.word	52301
	.byte	4,3,35,196,3,13
	.byte	'EVRSDCOEFF3',0
	.word	52370
	.byte	4,3,35,200,3,13
	.byte	'EVRSDCOEFF4',0
	.word	52439
	.byte	4,3,35,204,3,13
	.byte	'EVRSDCOEFF5',0
	.word	52508
	.byte	4,3,35,208,3,13
	.byte	'EVRSDCOEFF6',0
	.word	52577
	.byte	4,3,35,212,3,13
	.byte	'EVROSCCTRL',0
	.word	52031
	.byte	4,3,35,216,3,13
	.byte	'reserved_1DC',0
	.word	3435
	.byte	4,3,35,220,3,13
	.byte	'OVCENABLE',0
	.word	54190
	.byte	4,3,35,224,3,13
	.byte	'OVCCON',0
	.word	54126
	.byte	4,3,35,228,3,13
	.byte	'reserved_1E8',0
	.word	26661
	.byte	40,3,35,232,3,13
	.byte	'EICR',0
	.word	56455
	.byte	16,3,35,144,4,13
	.byte	'EIFR',0
	.word	51443
	.byte	4,3,35,160,4,13
	.byte	'FMR',0
	.word	53239
	.byte	4,3,35,164,4,13
	.byte	'PDRR',0
	.word	54381
	.byte	4,3,35,168,4,13
	.byte	'IGCR',0
	.word	56464
	.byte	16,3,35,172,4,13
	.byte	'reserved_23C',0
	.word	3435
	.byte	4,3,35,188,4,13
	.byte	'DTSLIM',0
	.word	51252
	.byte	4,3,35,192,4,13
	.byte	'reserved_244',0
	.word	56473
	.byte	180,3,3,35,196,4,13
	.byte	'ACCEN1',0
	.word	50475
	.byte	4,3,35,248,7,13
	.byte	'ACCEN0',0
	.word	50411
	.byte	4,3,35,252,7,0,14
	.word	56484
	.byte	22
	.byte	'Ifx_SCU',0,6,181,16,3
	.word	58474
	.byte	10
	.byte	'_Ifx_CPU_A_Bits',0,18,45,16,4,11
	.byte	'ADDR',0,4
	.word	916
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_CPU_A_Bits',0,18,48,3
	.word	58496
	.byte	10
	.byte	'_Ifx_CPU_BIV_Bits',0,18,51,16,4,11
	.byte	'VSS',0,4
	.word	916
	.byte	1,31,2,35,0,11
	.byte	'BIV',0,4
	.word	916
	.byte	31,0,2,35,0,0,22
	.byte	'Ifx_CPU_BIV_Bits',0,18,55,3
	.word	58557
	.byte	10
	.byte	'_Ifx_CPU_BTV_Bits',0,18,58,16,4,11
	.byte	'reserved_0',0,4
	.word	916
	.byte	1,31,2,35,0,11
	.byte	'BTV',0,4
	.word	916
	.byte	31,0,2,35,0,0,22
	.byte	'Ifx_CPU_BTV_Bits',0,18,62,3
	.word	58636
	.byte	10
	.byte	'_Ifx_CPU_CCNT_Bits',0,18,65,16,4,11
	.byte	'CountValue',0,4
	.word	916
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	916
	.byte	1,0,2,35,0,0,22
	.byte	'Ifx_CPU_CCNT_Bits',0,18,69,3
	.word	58722
	.byte	10
	.byte	'_Ifx_CPU_CCTRL_Bits',0,18,72,16,4,11
	.byte	'CM',0,4
	.word	916
	.byte	1,31,2,35,0,11
	.byte	'CE',0,4
	.word	916
	.byte	1,30,2,35,0,11
	.byte	'M1',0,4
	.word	916
	.byte	3,27,2,35,0,11
	.byte	'M2',0,4
	.word	916
	.byte	3,24,2,35,0,11
	.byte	'M3',0,4
	.word	916
	.byte	3,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	916
	.byte	21,0,2,35,0,0,22
	.byte	'Ifx_CPU_CCTRL_Bits',0,18,80,3
	.word	58811
	.byte	10
	.byte	'_Ifx_CPU_COMPAT_Bits',0,18,83,16,4,11
	.byte	'reserved_0',0,4
	.word	916
	.byte	3,29,2,35,0,11
	.byte	'RM',0,4
	.word	916
	.byte	1,28,2,35,0,11
	.byte	'SP',0,4
	.word	916
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	916
	.byte	27,0,2,35,0,0,22
	.byte	'Ifx_CPU_COMPAT_Bits',0,18,89,3
	.word	58957
	.byte	10
	.byte	'_Ifx_CPU_CORE_ID_Bits',0,18,92,16,4,11
	.byte	'CORE_ID',0,4
	.word	916
	.byte	3,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	916
	.byte	29,0,2,35,0,0,22
	.byte	'Ifx_CPU_CORE_ID_Bits',0,18,96,3
	.word	59084
	.byte	10
	.byte	'_Ifx_CPU_CPR_L_Bits',0,18,99,16,4,11
	.byte	'reserved_0',0,4
	.word	916
	.byte	3,29,2,35,0,11
	.byte	'LOWBND',0,4
	.word	916
	.byte	29,0,2,35,0,0,22
	.byte	'Ifx_CPU_CPR_L_Bits',0,18,103,3
	.word	59182
	.byte	10
	.byte	'_Ifx_CPU_CPR_U_Bits',0,18,106,16,4,11
	.byte	'reserved_0',0,4
	.word	916
	.byte	3,29,2,35,0,11
	.byte	'UPPBND',0,4
	.word	916
	.byte	29,0,2,35,0,0,22
	.byte	'Ifx_CPU_CPR_U_Bits',0,18,110,3
	.word	59275
	.byte	10
	.byte	'_Ifx_CPU_CPU_ID_Bits',0,18,113,16,4,11
	.byte	'MODREV',0,4
	.word	916
	.byte	8,24,2,35,0,11
	.byte	'MOD_32B',0,4
	.word	916
	.byte	8,16,2,35,0,11
	.byte	'MOD',0,4
	.word	916
	.byte	16,0,2,35,0,0,22
	.byte	'Ifx_CPU_CPU_ID_Bits',0,18,118,3
	.word	59368
	.byte	10
	.byte	'_Ifx_CPU_CPXE_Bits',0,18,121,16,4,11
	.byte	'XE',0,4
	.word	916
	.byte	8,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	916
	.byte	24,0,2,35,0,0,22
	.byte	'Ifx_CPU_CPXE_Bits',0,18,125,3
	.word	59475
	.byte	10
	.byte	'_Ifx_CPU_CREVT_Bits',0,18,128,1,16,4,11
	.byte	'EVTA',0,4
	.word	916
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	916
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	916
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	916
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	916
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	916
	.byte	24,0,2,35,0,0,22
	.byte	'Ifx_CPU_CREVT_Bits',0,18,136,1,3
	.word	59562
	.byte	10
	.byte	'_Ifx_CPU_CUS_ID_Bits',0,18,139,1,16,4,11
	.byte	'CID',0,4
	.word	916
	.byte	3,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	916
	.byte	29,0,2,35,0,0,22
	.byte	'Ifx_CPU_CUS_ID_Bits',0,18,143,1,3
	.word	59716
	.byte	10
	.byte	'_Ifx_CPU_D_Bits',0,18,146,1,16,4,11
	.byte	'DATA',0,4
	.word	916
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_CPU_D_Bits',0,18,149,1,3
	.word	59810
	.byte	10
	.byte	'_Ifx_CPU_DATR_Bits',0,18,152,1,16,4,11
	.byte	'reserved_0',0,4
	.word	916
	.byte	3,29,2,35,0,11
	.byte	'SBE',0,4
	.word	916
	.byte	1,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	916
	.byte	5,23,2,35,0,11
	.byte	'CWE',0,4
	.word	916
	.byte	1,22,2,35,0,11
	.byte	'CFE',0,4
	.word	916
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	916
	.byte	3,18,2,35,0,11
	.byte	'SOE',0,4
	.word	916
	.byte	1,17,2,35,0,11
	.byte	'SME',0,4
	.word	916
	.byte	1,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	916
	.byte	16,0,2,35,0,0,22
	.byte	'Ifx_CPU_DATR_Bits',0,18,163,1,3
	.word	59873
	.byte	10
	.byte	'_Ifx_CPU_DBGSR_Bits',0,18,166,1,16,4,11
	.byte	'DE',0,4
	.word	916
	.byte	1,31,2,35,0,11
	.byte	'HALT',0,4
	.word	916
	.byte	2,29,2,35,0,11
	.byte	'SIH',0,4
	.word	916
	.byte	1,28,2,35,0,11
	.byte	'SUSP',0,4
	.word	916
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	916
	.byte	1,26,2,35,0,11
	.byte	'PREVSUSP',0,4
	.word	916
	.byte	1,25,2,35,0,11
	.byte	'PEVT',0,4
	.word	916
	.byte	1,24,2,35,0,11
	.byte	'EVTSRC',0,4
	.word	916
	.byte	5,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	916
	.byte	19,0,2,35,0,0,22
	.byte	'Ifx_CPU_DBGSR_Bits',0,18,177,1,3
	.word	60091
	.byte	10
	.byte	'_Ifx_CPU_DBGTCR_Bits',0,18,180,1,16,4,11
	.byte	'DTA',0,4
	.word	916
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	916
	.byte	31,0,2,35,0,0,22
	.byte	'Ifx_CPU_DBGTCR_Bits',0,18,184,1,3
	.word	60306
	.byte	10
	.byte	'_Ifx_CPU_DCON0_Bits',0,18,187,1,16,4,11
	.byte	'reserved_0',0,4
	.word	916
	.byte	1,31,2,35,0,11
	.byte	'DCBYP',0,4
	.word	916
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	916
	.byte	30,0,2,35,0,0,22
	.byte	'Ifx_CPU_DCON0_Bits',0,18,192,1,3
	.word	60400
	.byte	10
	.byte	'_Ifx_CPU_DCON2_Bits',0,18,195,1,16,4,11
	.byte	'DCACHE_SZE',0,4
	.word	916
	.byte	16,16,2,35,0,11
	.byte	'DSCRATCH_SZE',0,4
	.word	916
	.byte	16,0,2,35,0,0,22
	.byte	'Ifx_CPU_DCON2_Bits',0,18,199,1,3
	.word	60516
	.byte	10
	.byte	'_Ifx_CPU_DCX_Bits',0,18,202,1,16,4,11
	.byte	'reserved_0',0,4
	.word	916
	.byte	6,26,2,35,0,11
	.byte	'DCXValue',0,4
	.word	916
	.byte	26,0,2,35,0,0,22
	.byte	'Ifx_CPU_DCX_Bits',0,18,206,1,3
	.word	60617
	.byte	10
	.byte	'_Ifx_CPU_DEADD_Bits',0,18,209,1,16,4,11
	.byte	'ERROR_ADDRESS',0,4
	.word	916
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_CPU_DEADD_Bits',0,18,212,1,3
	.word	60710
	.byte	10
	.byte	'_Ifx_CPU_DIEAR_Bits',0,18,215,1,16,4,11
	.byte	'TA',0,4
	.word	916
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_CPU_DIEAR_Bits',0,18,218,1,3
	.word	60790
	.byte	10
	.byte	'_Ifx_CPU_DIETR_Bits',0,18,221,1,16,4,11
	.byte	'IED',0,4
	.word	916
	.byte	1,31,2,35,0,11
	.byte	'IE_T',0,4
	.word	916
	.byte	1,30,2,35,0,11
	.byte	'IE_C',0,4
	.word	916
	.byte	1,29,2,35,0,11
	.byte	'IE_S',0,4
	.word	916
	.byte	1,28,2,35,0,11
	.byte	'IE_BI',0,4
	.word	916
	.byte	1,27,2,35,0,11
	.byte	'E_INFO',0,4
	.word	916
	.byte	6,21,2,35,0,11
	.byte	'IE_DUAL',0,4
	.word	916
	.byte	1,20,2,35,0,11
	.byte	'IE_SP',0,4
	.word	916
	.byte	1,19,2,35,0,11
	.byte	'IE_BS',0,4
	.word	916
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	916
	.byte	18,0,2,35,0,0,22
	.byte	'Ifx_CPU_DIETR_Bits',0,18,233,1,3
	.word	60859
	.byte	10
	.byte	'_Ifx_CPU_DMS_Bits',0,18,236,1,16,4,11
	.byte	'reserved_0',0,4
	.word	916
	.byte	1,31,2,35,0,11
	.byte	'DMSValue',0,4
	.word	916
	.byte	31,0,2,35,0,0,22
	.byte	'Ifx_CPU_DMS_Bits',0,18,240,1,3
	.word	61088
	.byte	10
	.byte	'_Ifx_CPU_DPR_L_Bits',0,18,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	916
	.byte	3,29,2,35,0,11
	.byte	'LOWBND',0,4
	.word	916
	.byte	29,0,2,35,0,0,22
	.byte	'Ifx_CPU_DPR_L_Bits',0,18,247,1,3
	.word	61181
	.byte	10
	.byte	'_Ifx_CPU_DPR_U_Bits',0,18,250,1,16,4,11
	.byte	'reserved_0',0,4
	.word	916
	.byte	3,29,2,35,0,11
	.byte	'UPPBND',0,4
	.word	916
	.byte	29,0,2,35,0,0,22
	.byte	'Ifx_CPU_DPR_U_Bits',0,18,254,1,3
	.word	61276
	.byte	10
	.byte	'_Ifx_CPU_DPRE_Bits',0,18,129,2,16,4,11
	.byte	'RE',0,4
	.word	916
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	916
	.byte	16,0,2,35,0,0,22
	.byte	'Ifx_CPU_DPRE_Bits',0,18,133,2,3
	.word	61371
	.byte	10
	.byte	'_Ifx_CPU_DPWE_Bits',0,18,136,2,16,4,11
	.byte	'WE',0,4
	.word	916
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	916
	.byte	16,0,2,35,0,0,22
	.byte	'Ifx_CPU_DPWE_Bits',0,18,140,2,3
	.word	61461
	.byte	10
	.byte	'_Ifx_CPU_DSTR_Bits',0,18,143,2,16,4,11
	.byte	'SRE',0,4
	.word	916
	.byte	1,31,2,35,0,11
	.byte	'GAE',0,4
	.word	916
	.byte	1,30,2,35,0,11
	.byte	'LBE',0,4
	.word	916
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	916
	.byte	3,26,2,35,0,11
	.byte	'CRE',0,4
	.word	916
	.byte	1,25,2,35,0,11
	.byte	'reserved_7',0,4
	.word	916
	.byte	7,18,2,35,0,11
	.byte	'DTME',0,4
	.word	916
	.byte	1,17,2,35,0,11
	.byte	'LOE',0,4
	.word	916
	.byte	1,16,2,35,0,11
	.byte	'SDE',0,4
	.word	916
	.byte	1,15,2,35,0,11
	.byte	'SCE',0,4
	.word	916
	.byte	1,14,2,35,0,11
	.byte	'CAC',0,4
	.word	916
	.byte	1,13,2,35,0,11
	.byte	'MPE',0,4
	.word	916
	.byte	1,12,2,35,0,11
	.byte	'CLE',0,4
	.word	916
	.byte	1,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	916
	.byte	3,8,2,35,0,11
	.byte	'ALN',0,4
	.word	916
	.byte	1,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	916
	.byte	7,0,2,35,0,0,22
	.byte	'Ifx_CPU_DSTR_Bits',0,18,161,2,3
	.word	61551
	.byte	10
	.byte	'_Ifx_CPU_EXEVT_Bits',0,18,164,2,16,4,11
	.byte	'EVTA',0,4
	.word	916
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	916
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	916
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	916
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	916
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	916
	.byte	24,0,2,35,0,0,22
	.byte	'Ifx_CPU_EXEVT_Bits',0,18,172,2,3
	.word	61875
	.byte	10
	.byte	'_Ifx_CPU_FCX_Bits',0,18,175,2,16,4,11
	.byte	'FCXO',0,4
	.word	916
	.byte	16,16,2,35,0,11
	.byte	'FCXS',0,4
	.word	916
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	916
	.byte	12,0,2,35,0,0,22
	.byte	'Ifx_CPU_FCX_Bits',0,18,180,2,3
	.word	62029
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_CON_Bits',0,18,183,2,16,4,11
	.byte	'TST',0,4
	.word	916
	.byte	1,31,2,35,0,11
	.byte	'TCL',0,4
	.word	916
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	916
	.byte	6,24,2,35,0,11
	.byte	'RM',0,4
	.word	916
	.byte	2,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	916
	.byte	8,14,2,35,0,11
	.byte	'FXE',0,4
	.word	916
	.byte	1,13,2,35,0,11
	.byte	'FUE',0,4
	.word	916
	.byte	1,12,2,35,0,11
	.byte	'FZE',0,4
	.word	916
	.byte	1,11,2,35,0,11
	.byte	'FVE',0,4
	.word	916
	.byte	1,10,2,35,0,11
	.byte	'FIE',0,4
	.word	916
	.byte	1,9,2,35,0,11
	.byte	'reserved_23',0,4
	.word	916
	.byte	3,6,2,35,0,11
	.byte	'FX',0,4
	.word	916
	.byte	1,5,2,35,0,11
	.byte	'FU',0,4
	.word	916
	.byte	1,4,2,35,0,11
	.byte	'FZ',0,4
	.word	916
	.byte	1,3,2,35,0,11
	.byte	'FV',0,4
	.word	916
	.byte	1,2,2,35,0,11
	.byte	'FI',0,4
	.word	916
	.byte	1,1,2,35,0,11
	.byte	'reserved_31',0,4
	.word	916
	.byte	1,0,2,35,0,0,22
	.byte	'Ifx_CPU_FPU_TRAP_CON_Bits',0,18,202,2,3
	.word	62135
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_OPC_Bits',0,18,205,2,16,4,11
	.byte	'OPC',0,4
	.word	916
	.byte	8,24,2,35,0,11
	.byte	'FMT',0,4
	.word	916
	.byte	1,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	916
	.byte	7,16,2,35,0,11
	.byte	'DREG',0,4
	.word	916
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	916
	.byte	12,0,2,35,0,0,22
	.byte	'Ifx_CPU_FPU_TRAP_OPC_Bits',0,18,212,2,3
	.word	62484
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_PC_Bits',0,18,215,2,16,4,11
	.byte	'PC',0,4
	.word	916
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_CPU_FPU_TRAP_PC_Bits',0,18,218,2,3
	.word	62644
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC1_Bits',0,18,221,2,16,4,11
	.byte	'SRC1',0,4
	.word	916
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_CPU_FPU_TRAP_SRC1_Bits',0,18,224,2,3
	.word	62725
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC2_Bits',0,18,227,2,16,4,11
	.byte	'SRC2',0,4
	.word	916
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_CPU_FPU_TRAP_SRC2_Bits',0,18,230,2,3
	.word	62812
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC3_Bits',0,18,233,2,16,4,11
	.byte	'SRC3',0,4
	.word	916
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_CPU_FPU_TRAP_SRC3_Bits',0,18,236,2,3
	.word	62899
	.byte	10
	.byte	'_Ifx_CPU_ICNT_Bits',0,18,239,2,16,4,11
	.byte	'CountValue',0,4
	.word	916
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	916
	.byte	1,0,2,35,0,0,22
	.byte	'Ifx_CPU_ICNT_Bits',0,18,243,2,3
	.word	62986
	.byte	10
	.byte	'_Ifx_CPU_ICR_Bits',0,18,246,2,16,4,11
	.byte	'CCPN',0,4
	.word	916
	.byte	10,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	916
	.byte	5,17,2,35,0,11
	.byte	'IE',0,4
	.word	916
	.byte	1,16,2,35,0,11
	.byte	'PIPN',0,4
	.word	916
	.byte	10,6,2,35,0,11
	.byte	'reserved_26',0,4
	.word	916
	.byte	6,0,2,35,0,0,22
	.byte	'Ifx_CPU_ICR_Bits',0,18,253,2,3
	.word	63077
	.byte	10
	.byte	'_Ifx_CPU_ISP_Bits',0,18,128,3,16,4,11
	.byte	'ISP',0,4
	.word	916
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_CPU_ISP_Bits',0,18,131,3,3
	.word	63220
	.byte	10
	.byte	'_Ifx_CPU_LCX_Bits',0,18,134,3,16,4,11
	.byte	'LCXO',0,4
	.word	916
	.byte	16,16,2,35,0,11
	.byte	'LCXS',0,4
	.word	916
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	916
	.byte	12,0,2,35,0,0,22
	.byte	'Ifx_CPU_LCX_Bits',0,18,139,3,3
	.word	63286
	.byte	10
	.byte	'_Ifx_CPU_M1CNT_Bits',0,18,142,3,16,4,11
	.byte	'CountValue',0,4
	.word	916
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	916
	.byte	1,0,2,35,0,0,22
	.byte	'Ifx_CPU_M1CNT_Bits',0,18,146,3,3
	.word	63392
	.byte	10
	.byte	'_Ifx_CPU_M2CNT_Bits',0,18,149,3,16,4,11
	.byte	'CountValue',0,4
	.word	916
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	916
	.byte	1,0,2,35,0,0,22
	.byte	'Ifx_CPU_M2CNT_Bits',0,18,153,3,3
	.word	63485
	.byte	10
	.byte	'_Ifx_CPU_M3CNT_Bits',0,18,156,3,16,4,11
	.byte	'CountValue',0,4
	.word	916
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	916
	.byte	1,0,2,35,0,0,22
	.byte	'Ifx_CPU_M3CNT_Bits',0,18,160,3,3
	.word	63578
	.byte	10
	.byte	'_Ifx_CPU_PC_Bits',0,18,163,3,16,4,11
	.byte	'reserved_0',0,4
	.word	916
	.byte	1,31,2,35,0,11
	.byte	'PC',0,4
	.word	916
	.byte	31,0,2,35,0,0,22
	.byte	'Ifx_CPU_PC_Bits',0,18,167,3,3
	.word	63671
	.byte	10
	.byte	'_Ifx_CPU_PCON0_Bits',0,18,170,3,16,4,11
	.byte	'reserved_0',0,4
	.word	916
	.byte	1,31,2,35,0,11
	.byte	'PCBYP',0,4
	.word	916
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	916
	.byte	30,0,2,35,0,0,22
	.byte	'Ifx_CPU_PCON0_Bits',0,18,175,3,3
	.word	63756
	.byte	10
	.byte	'_Ifx_CPU_PCON1_Bits',0,18,178,3,16,4,11
	.byte	'PCINV',0,4
	.word	916
	.byte	1,31,2,35,0,11
	.byte	'PBINV',0,4
	.word	916
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	916
	.byte	30,0,2,35,0,0,22
	.byte	'Ifx_CPU_PCON1_Bits',0,18,183,3,3
	.word	63872
	.byte	10
	.byte	'_Ifx_CPU_PCON2_Bits',0,18,186,3,16,4,11
	.byte	'PCACHE_SZE',0,4
	.word	916
	.byte	16,16,2,35,0,11
	.byte	'PSCRATCH_SZE',0,4
	.word	916
	.byte	16,0,2,35,0,0,22
	.byte	'Ifx_CPU_PCON2_Bits',0,18,190,3,3
	.word	63983
	.byte	10
	.byte	'_Ifx_CPU_PCXI_Bits',0,18,193,3,16,4,11
	.byte	'PCXO',0,4
	.word	916
	.byte	16,16,2,35,0,11
	.byte	'PCXS',0,4
	.word	916
	.byte	4,12,2,35,0,11
	.byte	'UL',0,4
	.word	916
	.byte	1,11,2,35,0,11
	.byte	'PIE',0,4
	.word	916
	.byte	1,10,2,35,0,11
	.byte	'PCPN',0,4
	.word	916
	.byte	10,0,2,35,0,0,22
	.byte	'Ifx_CPU_PCXI_Bits',0,18,200,3,3
	.word	64084
	.byte	10
	.byte	'_Ifx_CPU_PIEAR_Bits',0,18,203,3,16,4,11
	.byte	'TA',0,4
	.word	916
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_CPU_PIEAR_Bits',0,18,206,3,3
	.word	64214
	.byte	10
	.byte	'_Ifx_CPU_PIETR_Bits',0,18,209,3,16,4,11
	.byte	'IED',0,4
	.word	916
	.byte	1,31,2,35,0,11
	.byte	'IE_T',0,4
	.word	916
	.byte	1,30,2,35,0,11
	.byte	'IE_C',0,4
	.word	916
	.byte	1,29,2,35,0,11
	.byte	'IE_S',0,4
	.word	916
	.byte	1,28,2,35,0,11
	.byte	'IE_BI',0,4
	.word	916
	.byte	1,27,2,35,0,11
	.byte	'E_INFO',0,4
	.word	916
	.byte	6,21,2,35,0,11
	.byte	'IE_DUAL',0,4
	.word	916
	.byte	1,20,2,35,0,11
	.byte	'IE_SP',0,4
	.word	916
	.byte	1,19,2,35,0,11
	.byte	'IE_BS',0,4
	.word	916
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	916
	.byte	18,0,2,35,0,0,22
	.byte	'Ifx_CPU_PIETR_Bits',0,18,221,3,3
	.word	64283
	.byte	10
	.byte	'_Ifx_CPU_PMA0_Bits',0,18,224,3,16,4,11
	.byte	'reserved_0',0,4
	.word	916
	.byte	13,19,2,35,0,11
	.byte	'DAC',0,4
	.word	916
	.byte	3,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	916
	.byte	16,0,2,35,0,0,22
	.byte	'Ifx_CPU_PMA0_Bits',0,18,229,3,3
	.word	64512
	.byte	10
	.byte	'_Ifx_CPU_PMA1_Bits',0,18,232,3,16,4,11
	.byte	'reserved_0',0,4
	.word	916
	.byte	14,18,2,35,0,11
	.byte	'CAC',0,4
	.word	916
	.byte	2,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	916
	.byte	16,0,2,35,0,0,22
	.byte	'Ifx_CPU_PMA1_Bits',0,18,237,3,3
	.word	64625
	.byte	10
	.byte	'_Ifx_CPU_PMA2_Bits',0,18,240,3,16,4,11
	.byte	'PSI',0,4
	.word	916
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	916
	.byte	16,0,2,35,0,0,22
	.byte	'Ifx_CPU_PMA2_Bits',0,18,244,3,3
	.word	64738
	.byte	10
	.byte	'_Ifx_CPU_PSTR_Bits',0,18,247,3,16,4,11
	.byte	'FRE',0,4
	.word	916
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	916
	.byte	1,30,2,35,0,11
	.byte	'FBE',0,4
	.word	916
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	916
	.byte	9,20,2,35,0,11
	.byte	'FPE',0,4
	.word	916
	.byte	1,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	916
	.byte	1,18,2,35,0,11
	.byte	'FME',0,4
	.word	916
	.byte	1,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	916
	.byte	17,0,2,35,0,0,22
	.byte	'Ifx_CPU_PSTR_Bits',0,18,129,4,3
	.word	64829
	.byte	10
	.byte	'_Ifx_CPU_PSW_Bits',0,18,132,4,16,4,11
	.byte	'CDC',0,4
	.word	916
	.byte	7,25,2,35,0,11
	.byte	'CDE',0,4
	.word	916
	.byte	1,24,2,35,0,11
	.byte	'GW',0,4
	.word	916
	.byte	1,23,2,35,0,11
	.byte	'IS',0,4
	.word	916
	.byte	1,22,2,35,0,11
	.byte	'IO',0,4
	.word	916
	.byte	2,20,2,35,0,11
	.byte	'PRS',0,4
	.word	916
	.byte	2,18,2,35,0,11
	.byte	'S',0,4
	.word	916
	.byte	1,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	916
	.byte	12,5,2,35,0,11
	.byte	'SAV',0,4
	.word	916
	.byte	1,4,2,35,0,11
	.byte	'AV',0,4
	.word	916
	.byte	1,3,2,35,0,11
	.byte	'SV',0,4
	.word	916
	.byte	1,2,2,35,0,11
	.byte	'V',0,4
	.word	916
	.byte	1,1,2,35,0,11
	.byte	'C',0,4
	.word	916
	.byte	1,0,2,35,0,0,22
	.byte	'Ifx_CPU_PSW_Bits',0,18,147,4,3
	.word	65032
	.byte	10
	.byte	'_Ifx_CPU_SEGEN_Bits',0,18,150,4,16,4,11
	.byte	'ADFLIP',0,4
	.word	916
	.byte	8,24,2,35,0,11
	.byte	'ADTYPE',0,4
	.word	916
	.byte	2,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	916
	.byte	21,1,2,35,0,11
	.byte	'AE',0,4
	.word	916
	.byte	1,0,2,35,0,0,22
	.byte	'Ifx_CPU_SEGEN_Bits',0,18,156,4,3
	.word	65275
	.byte	10
	.byte	'_Ifx_CPU_SMACON_Bits',0,18,159,4,16,4,11
	.byte	'PC',0,4
	.word	916
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	916
	.byte	1,30,2,35,0,11
	.byte	'PT',0,4
	.word	916
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	916
	.byte	5,24,2,35,0,11
	.byte	'DC',0,4
	.word	916
	.byte	1,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	916
	.byte	1,22,2,35,0,11
	.byte	'DT',0,4
	.word	916
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	916
	.byte	13,8,2,35,0,11
	.byte	'IODT',0,4
	.word	916
	.byte	1,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	916
	.byte	7,0,2,35,0,0,22
	.byte	'Ifx_CPU_SMACON_Bits',0,18,171,4,3
	.word	65403
	.byte	10
	.byte	'_Ifx_CPU_SPROT_ACCENA_Bits',0,18,174,4,16,4,11
	.byte	'EN',0,4
	.word	494
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_CPU_SPROT_ACCENA_Bits',0,18,177,4,3
	.word	65644
	.byte	10
	.byte	'_Ifx_CPU_SPROT_ACCENB_Bits',0,18,180,4,16,4,11
	.byte	'reserved_0',0,4
	.word	494
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_CPU_SPROT_ACCENB_Bits',0,18,183,4,3
	.word	65727
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,18,186,4,16,4,11
	.byte	'EN',0,4
	.word	494
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,18,189,4,3
	.word	65818
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,18,192,4,16,4,11
	.byte	'reserved_0',0,4
	.word	494
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,18,195,4,3
	.word	65909
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_LA_Bits',0,18,198,4,16,4,11
	.byte	'reserved_0',0,1
	.word	517
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	494
	.byte	27,0,2,35,0,0,22
	.byte	'Ifx_CPU_SPROT_RGN_LA_Bits',0,18,202,4,3
	.word	66008
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_UA_Bits',0,18,205,4,16,4,11
	.byte	'reserved_0',0,1
	.word	517
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	494
	.byte	27,0,2,35,0,0,22
	.byte	'Ifx_CPU_SPROT_RGN_UA_Bits',0,18,209,4,3
	.word	66115
	.byte	10
	.byte	'_Ifx_CPU_SWEVT_Bits',0,18,212,4,16,4,11
	.byte	'EVTA',0,4
	.word	916
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	916
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	916
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	916
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	916
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	916
	.byte	24,0,2,35,0,0,22
	.byte	'Ifx_CPU_SWEVT_Bits',0,18,220,4,3
	.word	66222
	.byte	10
	.byte	'_Ifx_CPU_SYSCON_Bits',0,18,223,4,16,4,11
	.byte	'FCDSF',0,4
	.word	916
	.byte	1,31,2,35,0,11
	.byte	'PROTEN',0,4
	.word	916
	.byte	1,30,2,35,0,11
	.byte	'TPROTEN',0,4
	.word	916
	.byte	1,29,2,35,0,11
	.byte	'IS',0,4
	.word	916
	.byte	1,28,2,35,0,11
	.byte	'IT',0,4
	.word	916
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	916
	.byte	27,0,2,35,0,0,22
	.byte	'Ifx_CPU_SYSCON_Bits',0,18,231,4,3
	.word	66376
	.byte	10
	.byte	'_Ifx_CPU_TASK_ASI_Bits',0,18,234,4,16,4,11
	.byte	'ASI',0,4
	.word	916
	.byte	5,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	916
	.byte	27,0,2,35,0,0,22
	.byte	'Ifx_CPU_TASK_ASI_Bits',0,18,238,4,3
	.word	66537
	.byte	10
	.byte	'_Ifx_CPU_TPS_CON_Bits',0,18,241,4,16,4,11
	.byte	'TEXP0',0,4
	.word	916
	.byte	1,31,2,35,0,11
	.byte	'TEXP1',0,4
	.word	916
	.byte	1,30,2,35,0,11
	.byte	'TEXP2',0,4
	.word	916
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	916
	.byte	13,16,2,35,0,11
	.byte	'TTRAP',0,4
	.word	916
	.byte	1,15,2,35,0,11
	.byte	'reserved_17',0,4
	.word	916
	.byte	15,0,2,35,0,0,22
	.byte	'Ifx_CPU_TPS_CON_Bits',0,18,249,4,3
	.word	66635
	.byte	10
	.byte	'_Ifx_CPU_TPS_TIMER_Bits',0,18,252,4,16,4,11
	.byte	'Timer',0,4
	.word	916
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_CPU_TPS_TIMER_Bits',0,18,255,4,3
	.word	66807
	.byte	10
	.byte	'_Ifx_CPU_TR_ADR_Bits',0,18,130,5,16,4,11
	.byte	'ADDR',0,4
	.word	916
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_CPU_TR_ADR_Bits',0,18,133,5,3
	.word	66887
	.byte	10
	.byte	'_Ifx_CPU_TR_EVT_Bits',0,18,136,5,16,4,11
	.byte	'EVTA',0,4
	.word	916
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	916
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	916
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	916
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	916
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	916
	.byte	4,20,2,35,0,11
	.byte	'TYP',0,4
	.word	916
	.byte	1,19,2,35,0,11
	.byte	'RNG',0,4
	.word	916
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	916
	.byte	1,17,2,35,0,11
	.byte	'ASI_EN',0,4
	.word	916
	.byte	1,16,2,35,0,11
	.byte	'ASI',0,4
	.word	916
	.byte	5,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	916
	.byte	6,5,2,35,0,11
	.byte	'AST',0,4
	.word	916
	.byte	1,4,2,35,0,11
	.byte	'ALD',0,4
	.word	916
	.byte	1,3,2,35,0,11
	.byte	'reserved_29',0,4
	.word	916
	.byte	3,0,2,35,0,0,22
	.byte	'Ifx_CPU_TR_EVT_Bits',0,18,153,5,3
	.word	66960
	.byte	10
	.byte	'_Ifx_CPU_TRIG_ACC_Bits',0,18,156,5,16,4,11
	.byte	'T0',0,4
	.word	916
	.byte	1,31,2,35,0,11
	.byte	'T1',0,4
	.word	916
	.byte	1,30,2,35,0,11
	.byte	'T2',0,4
	.word	916
	.byte	1,29,2,35,0,11
	.byte	'T3',0,4
	.word	916
	.byte	1,28,2,35,0,11
	.byte	'T4',0,4
	.word	916
	.byte	1,27,2,35,0,11
	.byte	'T5',0,4
	.word	916
	.byte	1,26,2,35,0,11
	.byte	'T6',0,4
	.word	916
	.byte	1,25,2,35,0,11
	.byte	'T7',0,4
	.word	916
	.byte	1,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	916
	.byte	24,0,2,35,0,0,22
	.byte	'Ifx_CPU_TRIG_ACC_Bits',0,18,167,5,3
	.word	67278
	.byte	12,18,175,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58496
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_A',0,18,180,5,3
	.word	67473
	.byte	12,18,183,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58557
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_BIV',0,18,188,5,3
	.word	67532
	.byte	12,18,191,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58636
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_BTV',0,18,196,5,3
	.word	67593
	.byte	12,18,199,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58722
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_CCNT',0,18,204,5,3
	.word	67654
	.byte	12,18,207,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58811
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_CCTRL',0,18,212,5,3
	.word	67716
	.byte	12,18,215,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58957
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_COMPAT',0,18,220,5,3
	.word	67779
	.byte	12,18,223,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59084
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_CORE_ID',0,18,228,5,3
	.word	67843
	.byte	12,18,231,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59182
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_CPR_L',0,18,236,5,3
	.word	67908
	.byte	12,18,239,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59275
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_CPR_U',0,18,244,5,3
	.word	67971
	.byte	12,18,247,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59368
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_CPU_ID',0,18,252,5,3
	.word	68034
	.byte	12,18,255,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59475
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_CPXE',0,18,132,6,3
	.word	68098
	.byte	12,18,135,6,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59562
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_CREVT',0,18,140,6,3
	.word	68160
	.byte	12,18,143,6,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59716
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_CUS_ID',0,18,148,6,3
	.word	68223
	.byte	12,18,151,6,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59810
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_D',0,18,156,6,3
	.word	68287
	.byte	12,18,159,6,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59873
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_DATR',0,18,164,6,3
	.word	68346
	.byte	12,18,167,6,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60091
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_DBGSR',0,18,172,6,3
	.word	68408
	.byte	12,18,175,6,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60306
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_DBGTCR',0,18,180,6,3
	.word	68471
	.byte	12,18,183,6,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60400
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_DCON0',0,18,188,6,3
	.word	68535
	.byte	12,18,191,6,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60516
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_DCON2',0,18,196,6,3
	.word	68598
	.byte	12,18,199,6,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60617
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_DCX',0,18,204,6,3
	.word	68661
	.byte	12,18,207,6,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60710
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_DEADD',0,18,212,6,3
	.word	68722
	.byte	12,18,215,6,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60790
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_DIEAR',0,18,220,6,3
	.word	68785
	.byte	12,18,223,6,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60859
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_DIETR',0,18,228,6,3
	.word	68848
	.byte	12,18,231,6,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61088
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_DMS',0,18,236,6,3
	.word	68911
	.byte	12,18,239,6,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61181
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_DPR_L',0,18,244,6,3
	.word	68972
	.byte	12,18,247,6,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61276
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_DPR_U',0,18,252,6,3
	.word	69035
	.byte	12,18,255,6,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61371
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_DPRE',0,18,132,7,3
	.word	69098
	.byte	12,18,135,7,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61461
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_DPWE',0,18,140,7,3
	.word	69160
	.byte	12,18,143,7,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61551
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_DSTR',0,18,148,7,3
	.word	69222
	.byte	12,18,151,7,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61875
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_EXEVT',0,18,156,7,3
	.word	69284
	.byte	12,18,159,7,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62029
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_FCX',0,18,164,7,3
	.word	69347
	.byte	12,18,167,7,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62135
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_FPU_TRAP_CON',0,18,172,7,3
	.word	69408
	.byte	12,18,175,7,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62484
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_FPU_TRAP_OPC',0,18,180,7,3
	.word	69478
	.byte	12,18,183,7,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62644
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_FPU_TRAP_PC',0,18,188,7,3
	.word	69548
	.byte	12,18,191,7,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62725
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_FPU_TRAP_SRC1',0,18,196,7,3
	.word	69617
	.byte	12,18,199,7,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62812
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_FPU_TRAP_SRC2',0,18,204,7,3
	.word	69688
	.byte	12,18,207,7,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62899
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_FPU_TRAP_SRC3',0,18,212,7,3
	.word	69759
	.byte	12,18,215,7,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62986
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_ICNT',0,18,220,7,3
	.word	69830
	.byte	12,18,223,7,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63077
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_ICR',0,18,228,7,3
	.word	69892
	.byte	12,18,231,7,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63220
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_ISP',0,18,236,7,3
	.word	69953
	.byte	12,18,239,7,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63286
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_LCX',0,18,244,7,3
	.word	70014
	.byte	12,18,247,7,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63392
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_M1CNT',0,18,252,7,3
	.word	70075
	.byte	12,18,255,7,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63485
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_M2CNT',0,18,132,8,3
	.word	70138
	.byte	12,18,135,8,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63578
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_M3CNT',0,18,140,8,3
	.word	70201
	.byte	12,18,143,8,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63671
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_PC',0,18,148,8,3
	.word	70264
	.byte	12,18,151,8,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63756
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_PCON0',0,18,156,8,3
	.word	70324
	.byte	12,18,159,8,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63872
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_PCON1',0,18,164,8,3
	.word	70387
	.byte	12,18,167,8,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63983
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_PCON2',0,18,172,8,3
	.word	70450
	.byte	12,18,175,8,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64084
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_PCXI',0,18,180,8,3
	.word	70513
	.byte	12,18,183,8,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64214
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_PIEAR',0,18,188,8,3
	.word	70575
	.byte	12,18,191,8,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64283
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_PIETR',0,18,196,8,3
	.word	70638
	.byte	12,18,199,8,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64512
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_PMA0',0,18,204,8,3
	.word	70701
	.byte	12,18,207,8,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64625
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_PMA1',0,18,212,8,3
	.word	70763
	.byte	12,18,215,8,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64738
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_PMA2',0,18,220,8,3
	.word	70825
	.byte	12,18,223,8,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64829
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_PSTR',0,18,228,8,3
	.word	70887
	.byte	12,18,231,8,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65032
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_PSW',0,18,236,8,3
	.word	70949
	.byte	12,18,239,8,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65275
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_SEGEN',0,18,244,8,3
	.word	71010
	.byte	12,18,247,8,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65403
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_SMACON',0,18,252,8,3
	.word	71073
	.byte	12,18,255,8,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65644
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_SPROT_ACCENA',0,18,132,9,3
	.word	71137
	.byte	12,18,135,9,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65727
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_SPROT_ACCENB',0,18,140,9,3
	.word	71207
	.byte	12,18,143,9,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65818
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA',0,18,148,9,3
	.word	71277
	.byte	12,18,151,9,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65909
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB',0,18,156,9,3
	.word	71351
	.byte	12,18,159,9,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66008
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_SPROT_RGN_LA',0,18,164,9,3
	.word	71425
	.byte	12,18,167,9,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66115
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_SPROT_RGN_UA',0,18,172,9,3
	.word	71495
	.byte	12,18,175,9,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66222
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_SWEVT',0,18,180,9,3
	.word	71565
	.byte	12,18,183,9,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66376
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_SYSCON',0,18,188,9,3
	.word	71628
	.byte	12,18,191,9,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66537
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_TASK_ASI',0,18,196,9,3
	.word	71692
	.byte	12,18,199,9,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66635
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_TPS_CON',0,18,204,9,3
	.word	71758
	.byte	12,18,207,9,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66807
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_TPS_TIMER',0,18,212,9,3
	.word	71823
	.byte	12,18,215,9,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66887
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_TR_ADR',0,18,220,9,3
	.word	71890
	.byte	12,18,223,9,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66960
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_TR_EVT',0,18,228,9,3
	.word	71954
	.byte	12,18,231,9,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67278
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_TRIG_ACC',0,18,236,9,3
	.word	72018
	.byte	10
	.byte	'_Ifx_CPU_CPR',0,18,247,9,25,8,13
	.byte	'L',0
	.word	67908
	.byte	4,2,35,0,13
	.byte	'U',0
	.word	67971
	.byte	4,2,35,4,0,14
	.word	72084
	.byte	22
	.byte	'Ifx_CPU_CPR',0,18,251,9,3
	.word	72126
	.byte	10
	.byte	'_Ifx_CPU_DPR',0,18,254,9,25,8,13
	.byte	'L',0
	.word	68972
	.byte	4,2,35,0,13
	.byte	'U',0
	.word	69035
	.byte	4,2,35,4,0,14
	.word	72152
	.byte	22
	.byte	'Ifx_CPU_DPR',0,18,130,10,3
	.word	72194
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN',0,18,133,10,25,16,13
	.byte	'LA',0
	.word	71425
	.byte	4,2,35,0,13
	.byte	'UA',0
	.word	71495
	.byte	4,2,35,4,13
	.byte	'ACCENA',0
	.word	71277
	.byte	4,2,35,8,13
	.byte	'ACCENB',0
	.word	71351
	.byte	4,2,35,12,0,14
	.word	72220
	.byte	22
	.byte	'Ifx_CPU_SPROT_RGN',0,18,139,10,3
	.word	72302
	.byte	18,12
	.word	71823
	.byte	19,2,0,10
	.byte	'_Ifx_CPU_TPS',0,18,142,10,25,16,13
	.byte	'CON',0
	.word	71758
	.byte	4,2,35,0,13
	.byte	'TIMER',0
	.word	72334
	.byte	12,2,35,4,0,14
	.word	72343
	.byte	22
	.byte	'Ifx_CPU_TPS',0,18,146,10,3
	.word	72391
	.byte	10
	.byte	'_Ifx_CPU_TR',0,18,149,10,25,8,13
	.byte	'EVT',0
	.word	71954
	.byte	4,2,35,0,13
	.byte	'ADR',0
	.word	71890
	.byte	4,2,35,4,0,14
	.word	72417
	.byte	22
	.byte	'Ifx_CPU_TR',0,18,153,10,3
	.word	72462
	.byte	18,176,32
	.word	517
	.byte	19,175,32,0,18,208,223,1
	.word	517
	.byte	19,207,223,1,0,18,248,1
	.word	517
	.byte	19,247,1,0,18,244,29
	.word	517
	.byte	19,243,29,0,18,188,3
	.word	517
	.byte	19,187,3,0,18,232,3
	.word	517
	.byte	19,231,3,0,18,252,23
	.word	517
	.byte	19,251,23,0,18,228,63
	.word	517
	.byte	19,227,63,0,18,128,1
	.word	72152
	.byte	19,15,0,14
	.word	72577
	.byte	18,64
	.word	72084
	.byte	19,7,0,14
	.word	72592
	.byte	18,192,31
	.word	517
	.byte	19,191,31,0,18,16
	.word	68098
	.byte	19,3,0,18,16
	.word	69098
	.byte	19,3,0,18,16
	.word	69160
	.byte	19,3,0,18,208,7
	.word	517
	.byte	19,207,7,0,14
	.word	72343
	.byte	18,240,23
	.word	517
	.byte	19,239,23,0,18,64
	.word	72417
	.byte	19,7,0,14
	.word	72671
	.byte	18,192,23
	.word	517
	.byte	19,191,23,0,18,232,1
	.word	517
	.byte	19,231,1,0,18,180,1
	.word	517
	.byte	19,179,1,0,18,172,1
	.word	517
	.byte	19,171,1,0,18,64
	.word	68287
	.byte	19,15,0,18,64
	.word	517
	.byte	19,63,0,18,64
	.word	67473
	.byte	19,15,0,10
	.byte	'_Ifx_CPU',0,18,166,10,25,128,128,4,13
	.byte	'reserved_0',0
	.word	72487
	.byte	176,32,2,35,0,13
	.byte	'SEGEN',0
	.word	71010
	.byte	4,3,35,176,32,13
	.byte	'reserved_1034',0
	.word	72498
	.byte	208,223,1,3,35,180,32,13
	.byte	'TASK_ASI',0
	.word	71692
	.byte	4,4,35,132,128,2,13
	.byte	'reserved_8008',0
	.word	72511
	.byte	248,1,4,35,136,128,2,13
	.byte	'PMA0',0
	.word	70701
	.byte	4,4,35,128,130,2,13
	.byte	'PMA1',0
	.word	70763
	.byte	4,4,35,132,130,2,13
	.byte	'PMA2',0
	.word	70825
	.byte	4,4,35,136,130,2,13
	.byte	'reserved_810C',0
	.word	72522
	.byte	244,29,4,35,140,130,2,13
	.byte	'DCON2',0
	.word	68598
	.byte	4,4,35,128,160,2,13
	.byte	'reserved_9004',0
	.word	5254
	.byte	8,4,35,132,160,2,13
	.byte	'SMACON',0
	.word	71073
	.byte	4,4,35,140,160,2,13
	.byte	'DSTR',0
	.word	69222
	.byte	4,4,35,144,160,2,13
	.byte	'reserved_9014',0
	.word	3435
	.byte	4,4,35,148,160,2,13
	.byte	'DATR',0
	.word	68346
	.byte	4,4,35,152,160,2,13
	.byte	'DEADD',0
	.word	68722
	.byte	4,4,35,156,160,2,13
	.byte	'DIEAR',0
	.word	68785
	.byte	4,4,35,160,160,2,13
	.byte	'DIETR',0
	.word	68848
	.byte	4,4,35,164,160,2,13
	.byte	'reserved_9028',0
	.word	4625
	.byte	24,4,35,168,160,2,13
	.byte	'DCON0',0
	.word	68535
	.byte	4,4,35,192,160,2,13
	.byte	'reserved_9044',0
	.word	72533
	.byte	188,3,4,35,196,160,2,13
	.byte	'PSTR',0
	.word	70887
	.byte	4,4,35,128,164,2,13
	.byte	'PCON1',0
	.word	70387
	.byte	4,4,35,132,164,2,13
	.byte	'PCON2',0
	.word	70450
	.byte	4,4,35,136,164,2,13
	.byte	'PCON0',0
	.word	70324
	.byte	4,4,35,140,164,2,13
	.byte	'PIEAR',0
	.word	70575
	.byte	4,4,35,144,164,2,13
	.byte	'PIETR',0
	.word	70638
	.byte	4,4,35,148,164,2,13
	.byte	'reserved_9218',0
	.word	72544
	.byte	232,3,4,35,152,164,2,13
	.byte	'COMPAT',0
	.word	67779
	.byte	4,4,35,128,168,2,13
	.byte	'reserved_9404',0
	.word	72555
	.byte	252,23,4,35,132,168,2,13
	.byte	'FPU_TRAP_CON',0
	.word	69408
	.byte	4,4,35,128,192,2,13
	.byte	'FPU_TRAP_PC',0
	.word	69548
	.byte	4,4,35,132,192,2,13
	.byte	'FPU_TRAP_OPC',0
	.word	69478
	.byte	4,4,35,136,192,2,13
	.byte	'reserved_A00C',0
	.word	3435
	.byte	4,4,35,140,192,2,13
	.byte	'FPU_TRAP_SRC1',0
	.word	69617
	.byte	4,4,35,144,192,2,13
	.byte	'FPU_TRAP_SRC2',0
	.word	69688
	.byte	4,4,35,148,192,2,13
	.byte	'FPU_TRAP_SRC3',0
	.word	69759
	.byte	4,4,35,152,192,2,13
	.byte	'reserved_A01C',0
	.word	72566
	.byte	228,63,4,35,156,192,2,13
	.byte	'DPR',0
	.word	72587
	.byte	128,1,4,35,128,128,3,13
	.byte	'reserved_C080',0
	.word	24827
	.byte	128,31,4,35,128,129,3,13
	.byte	'CPR',0
	.word	72601
	.byte	64,4,35,128,160,3,13
	.byte	'reserved_D040',0
	.word	72606
	.byte	192,31,4,35,192,160,3,13
	.byte	'CPXE',0
	.word	72617
	.byte	16,4,35,128,192,3,13
	.byte	'DPRE',0
	.word	72626
	.byte	16,4,35,144,192,3,13
	.byte	'DPWE',0
	.word	72635
	.byte	16,4,35,160,192,3,13
	.byte	'reserved_E030',0
	.word	72644
	.byte	208,7,4,35,176,192,3,13
	.byte	'TPS',0
	.word	72655
	.byte	16,4,35,128,200,3,13
	.byte	'reserved_E410',0
	.word	72660
	.byte	240,23,4,35,144,200,3,13
	.byte	'TR',0
	.word	72680
	.byte	64,4,35,128,224,3,13
	.byte	'reserved_F040',0
	.word	72685
	.byte	192,23,4,35,192,224,3,13
	.byte	'CCTRL',0
	.word	67716
	.byte	4,4,35,128,248,3,13
	.byte	'CCNT',0
	.word	67654
	.byte	4,4,35,132,248,3,13
	.byte	'ICNT',0
	.word	69830
	.byte	4,4,35,136,248,3,13
	.byte	'M1CNT',0
	.word	70075
	.byte	4,4,35,140,248,3,13
	.byte	'M2CNT',0
	.word	70138
	.byte	4,4,35,144,248,3,13
	.byte	'M3CNT',0
	.word	70201
	.byte	4,4,35,148,248,3,13
	.byte	'reserved_FC18',0
	.word	72696
	.byte	232,1,4,35,152,248,3,13
	.byte	'DBGSR',0
	.word	68408
	.byte	4,4,35,128,250,3,13
	.byte	'reserved_FD04',0
	.word	3435
	.byte	4,4,35,132,250,3,13
	.byte	'EXEVT',0
	.word	69284
	.byte	4,4,35,136,250,3,13
	.byte	'CREVT',0
	.word	68160
	.byte	4,4,35,140,250,3,13
	.byte	'SWEVT',0
	.word	71565
	.byte	4,4,35,144,250,3,13
	.byte	'reserved_FD14',0
	.word	24802
	.byte	28,4,35,148,250,3,13
	.byte	'TRIG_ACC',0
	.word	72018
	.byte	4,4,35,176,250,3,13
	.byte	'reserved_FD34',0
	.word	5594
	.byte	12,4,35,180,250,3,13
	.byte	'DMS',0
	.word	68911
	.byte	4,4,35,192,250,3,13
	.byte	'DCX',0
	.word	68661
	.byte	4,4,35,196,250,3,13
	.byte	'DBGTCR',0
	.word	68471
	.byte	4,4,35,200,250,3,13
	.byte	'reserved_FD4C',0
	.word	72707
	.byte	180,1,4,35,204,250,3,13
	.byte	'PCXI',0
	.word	70513
	.byte	4,4,35,128,252,3,13
	.byte	'PSW',0
	.word	70949
	.byte	4,4,35,132,252,3,13
	.byte	'PC',0
	.word	70264
	.byte	4,4,35,136,252,3,13
	.byte	'reserved_FE0C',0
	.word	5254
	.byte	8,4,35,140,252,3,13
	.byte	'SYSCON',0
	.word	71628
	.byte	4,4,35,148,252,3,13
	.byte	'CPU_ID',0
	.word	68034
	.byte	4,4,35,152,252,3,13
	.byte	'CORE_ID',0
	.word	67843
	.byte	4,4,35,156,252,3,13
	.byte	'BIV',0
	.word	67532
	.byte	4,4,35,160,252,3,13
	.byte	'BTV',0
	.word	67593
	.byte	4,4,35,164,252,3,13
	.byte	'ISP',0
	.word	69953
	.byte	4,4,35,168,252,3,13
	.byte	'ICR',0
	.word	69892
	.byte	4,4,35,172,252,3,13
	.byte	'reserved_FE30',0
	.word	5254
	.byte	8,4,35,176,252,3,13
	.byte	'FCX',0
	.word	69347
	.byte	4,4,35,184,252,3,13
	.byte	'LCX',0
	.word	70014
	.byte	4,4,35,188,252,3,13
	.byte	'reserved_FE40',0
	.word	27026
	.byte	16,4,35,192,252,3,13
	.byte	'CUS_ID',0
	.word	68223
	.byte	4,4,35,208,252,3,13
	.byte	'reserved_FE54',0
	.word	72718
	.byte	172,1,4,35,212,252,3,13
	.byte	'D',0
	.word	72729
	.byte	64,4,35,128,254,3,13
	.byte	'reserved_FF40',0
	.word	72738
	.byte	64,4,35,192,254,3,13
	.byte	'A',0
	.word	72747
	.byte	64,4,35,128,255,3,13
	.byte	'reserved_FFC0',0
	.word	72738
	.byte	64,4,35,192,255,3,0,14
	.word	72756
	.byte	22
	.byte	'Ifx_CPU',0,18,130,11,3
	.word	74547
	.byte	15,8,127,9,1,16
	.byte	'IfxCpu_Id_0',0,0,16
	.byte	'IfxCpu_Id_1',0,1,16
	.byte	'IfxCpu_Id_none',0,2,0,22
	.byte	'IfxCpu_Id',0,8,132,1,3
	.word	74569
	.byte	22
	.byte	'IfxCpu_ResourceCpu',0,8,161,1,3
	.word	2043
	.byte	10
	.byte	'_Ifx_STM_ACCEN0_Bits',0,19,45,16,4,11
	.byte	'EN0',0,1
	.word	517
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	517
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	517
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	517
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	517
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	517
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	517
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	517
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	517
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	517
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	517
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	517
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	517
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	517
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	517
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	517
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	517
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	517
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	517
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	517
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	517
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	517
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	517
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	517
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	517
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	517
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	517
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	517
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	517
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	517
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	517
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	517
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_STM_ACCEN0_Bits',0,19,79,3
	.word	74667
	.byte	10
	.byte	'_Ifx_STM_ACCEN1_Bits',0,19,82,16,4,11
	.byte	'reserved_0',0,4
	.word	494
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_STM_ACCEN1_Bits',0,19,85,3
	.word	75224
	.byte	10
	.byte	'_Ifx_STM_CAP_Bits',0,19,88,16,4,11
	.byte	'STMCAP63_32',0,4
	.word	494
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_STM_CAP_Bits',0,19,91,3
	.word	75301
	.byte	10
	.byte	'_Ifx_STM_CAPSV_Bits',0,19,94,16,4,11
	.byte	'STMCAP63_32',0,4
	.word	494
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_STM_CAPSV_Bits',0,19,97,3
	.word	75373
	.byte	10
	.byte	'_Ifx_STM_CLC_Bits',0,19,100,16,4,11
	.byte	'DISR',0,1
	.word	517
	.byte	1,7,2,35,0,11
	.byte	'DISS',0,1
	.word	517
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	517
	.byte	1,5,2,35,0,11
	.byte	'EDIS',0,1
	.word	517
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	494
	.byte	28,0,2,35,0,0,22
	.byte	'Ifx_STM_CLC_Bits',0,19,107,3
	.word	75449
	.byte	10
	.byte	'_Ifx_STM_CMCON_Bits',0,19,110,16,4,11
	.byte	'MSIZE0',0,1
	.word	517
	.byte	5,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	517
	.byte	3,0,2,35,0,11
	.byte	'MSTART0',0,1
	.word	517
	.byte	5,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	517
	.byte	3,0,2,35,1,11
	.byte	'MSIZE1',0,1
	.word	517
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	517
	.byte	3,0,2,35,2,11
	.byte	'MSTART1',0,1
	.word	517
	.byte	5,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	517
	.byte	3,0,2,35,3,0,22
	.byte	'Ifx_STM_CMCON_Bits',0,19,120,3
	.word	75590
	.byte	10
	.byte	'_Ifx_STM_CMP_Bits',0,19,123,16,4,11
	.byte	'CMPVAL',0,4
	.word	494
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_STM_CMP_Bits',0,19,126,3
	.word	75808
	.byte	10
	.byte	'_Ifx_STM_ICR_Bits',0,19,129,1,16,4,11
	.byte	'CMP0EN',0,1
	.word	517
	.byte	1,7,2,35,0,11
	.byte	'CMP0IR',0,1
	.word	517
	.byte	1,6,2,35,0,11
	.byte	'CMP0OS',0,1
	.word	517
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	517
	.byte	1,4,2,35,0,11
	.byte	'CMP1EN',0,1
	.word	517
	.byte	1,3,2,35,0,11
	.byte	'CMP1IR',0,1
	.word	517
	.byte	1,2,2,35,0,11
	.byte	'CMP1OS',0,1
	.word	517
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,4
	.word	494
	.byte	25,0,2,35,0,0,22
	.byte	'Ifx_STM_ICR_Bits',0,19,139,1,3
	.word	75875
	.byte	10
	.byte	'_Ifx_STM_ID_Bits',0,19,142,1,16,4,11
	.byte	'MODREV',0,1
	.word	517
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	517
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	1068
	.byte	16,0,2,35,2,0,22
	.byte	'Ifx_STM_ID_Bits',0,19,147,1,3
	.word	76078
	.byte	10
	.byte	'_Ifx_STM_ISCR_Bits',0,19,150,1,16,4,11
	.byte	'CMP0IRR',0,1
	.word	517
	.byte	1,7,2,35,0,11
	.byte	'CMP0IRS',0,1
	.word	517
	.byte	1,6,2,35,0,11
	.byte	'CMP1IRR',0,1
	.word	517
	.byte	1,5,2,35,0,11
	.byte	'CMP1IRS',0,1
	.word	517
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	494
	.byte	28,0,2,35,0,0,22
	.byte	'Ifx_STM_ISCR_Bits',0,19,157,1,3
	.word	76185
	.byte	10
	.byte	'_Ifx_STM_KRST0_Bits',0,19,160,1,16,4,11
	.byte	'RST',0,1
	.word	517
	.byte	1,7,2,35,0,11
	.byte	'RSTSTAT',0,1
	.word	517
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	494
	.byte	30,0,2,35,0,0,22
	.byte	'Ifx_STM_KRST0_Bits',0,19,165,1,3
	.word	76336
	.byte	10
	.byte	'_Ifx_STM_KRST1_Bits',0,19,168,1,16,4,11
	.byte	'RST',0,1
	.word	517
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	494
	.byte	31,0,2,35,0,0,22
	.byte	'Ifx_STM_KRST1_Bits',0,19,172,1,3
	.word	76447
	.byte	10
	.byte	'_Ifx_STM_KRSTCLR_Bits',0,19,175,1,16,4,11
	.byte	'CLR',0,1
	.word	517
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	494
	.byte	31,0,2,35,0,0,22
	.byte	'Ifx_STM_KRSTCLR_Bits',0,19,179,1,3
	.word	76539
	.byte	10
	.byte	'_Ifx_STM_OCS_Bits',0,19,182,1,16,4,11
	.byte	'reserved_0',0,4
	.word	494
	.byte	24,8,2,35,0,11
	.byte	'SUS',0,1
	.word	517
	.byte	4,4,2,35,3,11
	.byte	'SUS_P',0,1
	.word	517
	.byte	1,3,2,35,3,11
	.byte	'SUSSTA',0,1
	.word	517
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	517
	.byte	2,0,2,35,3,0,22
	.byte	'Ifx_STM_OCS_Bits',0,19,189,1,3
	.word	76635
	.byte	10
	.byte	'_Ifx_STM_TIM0_Bits',0,19,192,1,16,4,11
	.byte	'STM31_0',0,4
	.word	494
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_STM_TIM0_Bits',0,19,195,1,3
	.word	76781
	.byte	10
	.byte	'_Ifx_STM_TIM0SV_Bits',0,19,198,1,16,4,11
	.byte	'STM31_0',0,4
	.word	494
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_STM_TIM0SV_Bits',0,19,201,1,3
	.word	76853
	.byte	10
	.byte	'_Ifx_STM_TIM1_Bits',0,19,204,1,16,4,11
	.byte	'STM35_4',0,4
	.word	494
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_STM_TIM1_Bits',0,19,207,1,3
	.word	76929
	.byte	10
	.byte	'_Ifx_STM_TIM2_Bits',0,19,210,1,16,4,11
	.byte	'STM39_8',0,4
	.word	494
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_STM_TIM2_Bits',0,19,213,1,3
	.word	77001
	.byte	10
	.byte	'_Ifx_STM_TIM3_Bits',0,19,216,1,16,4,11
	.byte	'STM43_12',0,4
	.word	494
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_STM_TIM3_Bits',0,19,219,1,3
	.word	77073
	.byte	10
	.byte	'_Ifx_STM_TIM4_Bits',0,19,222,1,16,4,11
	.byte	'STM47_16',0,4
	.word	494
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_STM_TIM4_Bits',0,19,225,1,3
	.word	77146
	.byte	10
	.byte	'_Ifx_STM_TIM5_Bits',0,19,228,1,16,4,11
	.byte	'STM51_20',0,4
	.word	494
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_STM_TIM5_Bits',0,19,231,1,3
	.word	77219
	.byte	10
	.byte	'_Ifx_STM_TIM6_Bits',0,19,234,1,16,4,11
	.byte	'STM63_32',0,4
	.word	494
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_STM_TIM6_Bits',0,19,237,1,3
	.word	77292
	.byte	12,19,245,1,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	74667
	.byte	4,2,35,0,0,22
	.byte	'Ifx_STM_ACCEN0',0,19,250,1,3
	.word	77365
	.byte	12,19,253,1,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	75224
	.byte	4,2,35,0,0,22
	.byte	'Ifx_STM_ACCEN1',0,19,130,2,3
	.word	77429
	.byte	12,19,133,2,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	75301
	.byte	4,2,35,0,0,22
	.byte	'Ifx_STM_CAP',0,19,138,2,3
	.word	77493
	.byte	12,19,141,2,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	75373
	.byte	4,2,35,0,0,22
	.byte	'Ifx_STM_CAPSV',0,19,146,2,3
	.word	77554
	.byte	12,19,149,2,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	75449
	.byte	4,2,35,0,0,22
	.byte	'Ifx_STM_CLC',0,19,154,2,3
	.word	77617
	.byte	12,19,157,2,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	75590
	.byte	4,2,35,0,0,22
	.byte	'Ifx_STM_CMCON',0,19,162,2,3
	.word	77678
	.byte	12,19,165,2,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	75808
	.byte	4,2,35,0,0,22
	.byte	'Ifx_STM_CMP',0,19,170,2,3
	.word	77741
	.byte	12,19,173,2,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	75875
	.byte	4,2,35,0,0,22
	.byte	'Ifx_STM_ICR',0,19,178,2,3
	.word	77802
	.byte	12,19,181,2,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	76078
	.byte	4,2,35,0,0,22
	.byte	'Ifx_STM_ID',0,19,186,2,3
	.word	77863
	.byte	12,19,189,2,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	76185
	.byte	4,2,35,0,0,22
	.byte	'Ifx_STM_ISCR',0,19,194,2,3
	.word	77923
	.byte	12,19,197,2,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	76336
	.byte	4,2,35,0,0,22
	.byte	'Ifx_STM_KRST0',0,19,202,2,3
	.word	77985
	.byte	12,19,205,2,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	76447
	.byte	4,2,35,0,0,22
	.byte	'Ifx_STM_KRST1',0,19,210,2,3
	.word	78048
	.byte	12,19,213,2,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	76539
	.byte	4,2,35,0,0,22
	.byte	'Ifx_STM_KRSTCLR',0,19,218,2,3
	.word	78111
	.byte	12,19,221,2,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	76635
	.byte	4,2,35,0,0,22
	.byte	'Ifx_STM_OCS',0,19,226,2,3
	.word	78176
	.byte	12,19,229,2,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	76781
	.byte	4,2,35,0,0,22
	.byte	'Ifx_STM_TIM0',0,19,234,2,3
	.word	78237
	.byte	12,19,237,2,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	76853
	.byte	4,2,35,0,0,22
	.byte	'Ifx_STM_TIM0SV',0,19,242,2,3
	.word	78299
	.byte	12,19,245,2,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	76929
	.byte	4,2,35,0,0,22
	.byte	'Ifx_STM_TIM1',0,19,250,2,3
	.word	78363
	.byte	12,19,253,2,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	77001
	.byte	4,2,35,0,0,22
	.byte	'Ifx_STM_TIM2',0,19,130,3,3
	.word	78425
	.byte	12,19,133,3,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	77073
	.byte	4,2,35,0,0,22
	.byte	'Ifx_STM_TIM3',0,19,138,3,3
	.word	78487
	.byte	12,19,141,3,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	77146
	.byte	4,2,35,0,0,22
	.byte	'Ifx_STM_TIM4',0,19,146,3,3
	.word	78549
	.byte	12,19,149,3,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	77219
	.byte	4,2,35,0,0,22
	.byte	'Ifx_STM_TIM5',0,19,154,3,3
	.word	78611
	.byte	12,19,157,3,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	77292
	.byte	4,2,35,0,0,22
	.byte	'Ifx_STM_TIM6',0,19,162,3,3
	.word	78673
	.byte	15,7,144,1,9,1,16
	.byte	'IfxCpu_CounterMode_normal',0,0,16
	.byte	'IfxCpu_CounterMode_task',0,1,0,22
	.byte	'IfxCpu_CounterMode',0,7,148,1,3
	.word	78735
	.byte	24,7,160,1,9,6,13
	.byte	'counter',0
	.word	2198
	.byte	4,2,35,0,13
	.byte	'overlfow',0
	.word	517
	.byte	1,2,35,4,0,22
	.byte	'IfxCpu_Counter',0,7,164,1,3
	.word	78824
	.byte	24,7,172,1,9,32,13
	.byte	'instruction',0
	.word	78824
	.byte	6,2,35,0,13
	.byte	'clock',0
	.word	78824
	.byte	6,2,35,6,13
	.byte	'counter1',0
	.word	78824
	.byte	6,2,35,12,13
	.byte	'counter2',0
	.word	78824
	.byte	6,2,35,18,13
	.byte	'counter3',0
	.word	78824
	.byte	6,2,35,24,0,22
	.byte	'IfxCpu_Perf',0,7,179,1,3
	.word	78890
	.byte	10
	.byte	'_Ifx_FLASH_ACCEN0_Bits',0,20,45,16,4,11
	.byte	'EN0',0,1
	.word	517
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	517
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	517
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	517
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	517
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	517
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	517
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	517
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	517
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	517
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	517
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	517
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	517
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	517
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	517
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	517
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	517
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	517
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	517
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	517
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	517
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	517
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	517
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	517
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	517
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	517
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	517
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	517
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	517
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	517
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	517
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	517
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_FLASH_ACCEN0_Bits',0,20,79,3
	.word	79008
	.byte	10
	.byte	'_Ifx_FLASH_ACCEN1_Bits',0,20,82,16,4,11
	.byte	'reserved_0',0,4
	.word	494
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_FLASH_ACCEN1_Bits',0,20,85,3
	.word	79569
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_CFG_Bits',0,20,88,16,4,11
	.byte	'SEL',0,1
	.word	517
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	517
	.byte	2,0,2,35,0,11
	.byte	'CLR',0,1
	.word	517
	.byte	1,7,2,35,1,11
	.byte	'DIS',0,1
	.word	517
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	494
	.byte	22,0,2,35,0,0,22
	.byte	'Ifx_FLASH_CBAB_CFG_Bits',0,20,95,3
	.word	79650
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_STAT_Bits',0,20,98,16,4,11
	.byte	'VLD0',0,1
	.word	517
	.byte	1,7,2,35,0,11
	.byte	'VLD1',0,1
	.word	517
	.byte	1,6,2,35,0,11
	.byte	'VLD2',0,1
	.word	517
	.byte	1,5,2,35,0,11
	.byte	'VLD3',0,1
	.word	517
	.byte	1,4,2,35,0,11
	.byte	'VLD4',0,1
	.word	517
	.byte	1,3,2,35,0,11
	.byte	'VLD5',0,1
	.word	517
	.byte	1,2,2,35,0,11
	.byte	'VLD6',0,1
	.word	517
	.byte	1,1,2,35,0,11
	.byte	'VLD7',0,1
	.word	517
	.byte	1,0,2,35,0,11
	.byte	'VLD8',0,1
	.word	517
	.byte	1,7,2,35,1,11
	.byte	'VLD9',0,1
	.word	517
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	494
	.byte	22,0,2,35,0,0,22
	.byte	'Ifx_FLASH_CBAB_STAT_Bits',0,20,111,3
	.word	79803
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_TOP_Bits',0,20,114,16,4,11
	.byte	'reserved_0',0,1
	.word	517
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	494
	.byte	19,8,2,35,0,11
	.byte	'ERR',0,1
	.word	517
	.byte	6,2,2,35,3,11
	.byte	'VLD',0,1
	.word	517
	.byte	1,1,2,35,3,11
	.byte	'CLR',0,1
	.word	517
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_FLASH_CBAB_TOP_Bits',0,20,121,3
	.word	80051
	.byte	10
	.byte	'_Ifx_FLASH_COMM0_Bits',0,20,124,16,4,11
	.byte	'STATUS',0,1
	.word	517
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	494
	.byte	24,0,2,35,0,0,22
	.byte	'Ifx_FLASH_COMM0_Bits',0,20,128,1,3
	.word	80197
	.byte	10
	.byte	'_Ifx_FLASH_COMM1_Bits',0,20,131,1,16,4,11
	.byte	'STATUS',0,1
	.word	517
	.byte	8,0,2,35,0,11
	.byte	'DATA',0,1
	.word	517
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1068
	.byte	16,0,2,35,2,0,22
	.byte	'Ifx_FLASH_COMM1_Bits',0,20,136,1,3
	.word	80295
	.byte	10
	.byte	'_Ifx_FLASH_COMM2_Bits',0,20,139,1,16,4,11
	.byte	'STATUS',0,1
	.word	517
	.byte	8,0,2,35,0,11
	.byte	'DATA',0,1
	.word	517
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1068
	.byte	16,0,2,35,2,0,22
	.byte	'Ifx_FLASH_COMM2_Bits',0,20,144,1,3
	.word	80411
	.byte	10
	.byte	'_Ifx_FLASH_ECCRD_Bits',0,20,147,1,16,4,11
	.byte	'RCODE',0,4
	.word	494
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	1068
	.byte	8,2,2,35,2,11
	.byte	'EDCERRINJ',0,1
	.word	517
	.byte	1,1,2,35,3,11
	.byte	'ECCORDIS',0,1
	.word	517
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_FLASH_ECCRD_Bits',0,20,153,1,3
	.word	80527
	.byte	10
	.byte	'_Ifx_FLASH_ECCRP_Bits',0,20,156,1,16,4,11
	.byte	'RCODE',0,4
	.word	494
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	1068
	.byte	8,2,2,35,2,11
	.byte	'EDCERRINJ',0,1
	.word	517
	.byte	1,1,2,35,3,11
	.byte	'ECCORDIS',0,1
	.word	517
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_FLASH_ECCRP_Bits',0,20,162,1,3
	.word	80667
	.byte	10
	.byte	'_Ifx_FLASH_ECCW_Bits',0,20,165,1,16,4,11
	.byte	'WCODE',0,4
	.word	494
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	1068
	.byte	8,2,2,35,2,11
	.byte	'DECENCDIS',0,1
	.word	517
	.byte	1,1,2,35,3,11
	.byte	'PECENCDIS',0,1
	.word	517
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_FLASH_ECCW_Bits',0,20,171,1,3
	.word	80807
	.byte	10
	.byte	'_Ifx_FLASH_FCON_Bits',0,20,174,1,16,4,11
	.byte	'WSPFLASH',0,1
	.word	517
	.byte	4,4,2,35,0,11
	.byte	'WSECPF',0,1
	.word	517
	.byte	2,2,2,35,0,11
	.byte	'WSDFLASH',0,2
	.word	1068
	.byte	6,4,2,35,0,11
	.byte	'WSECDF',0,1
	.word	517
	.byte	3,1,2,35,1,11
	.byte	'IDLE',0,1
	.word	517
	.byte	1,0,2,35,1,11
	.byte	'ESLDIS',0,1
	.word	517
	.byte	1,7,2,35,2,11
	.byte	'SLEEP',0,1
	.word	517
	.byte	1,6,2,35,2,11
	.byte	'NSAFECC',0,1
	.word	517
	.byte	1,5,2,35,2,11
	.byte	'STALL',0,1
	.word	517
	.byte	1,4,2,35,2,11
	.byte	'RES21',0,1
	.word	517
	.byte	2,2,2,35,2,11
	.byte	'RES23',0,1
	.word	517
	.byte	2,0,2,35,2,11
	.byte	'VOPERM',0,1
	.word	517
	.byte	1,7,2,35,3,11
	.byte	'SQERM',0,1
	.word	517
	.byte	1,6,2,35,3,11
	.byte	'PROERM',0,1
	.word	517
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	517
	.byte	3,2,2,35,3,11
	.byte	'PR5V',0,1
	.word	517
	.byte	1,1,2,35,3,11
	.byte	'EOBM',0,1
	.word	517
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_FLASH_FCON_Bits',0,20,193,1,3
	.word	80946
	.byte	10
	.byte	'_Ifx_FLASH_FPRO_Bits',0,20,196,1,16,4,11
	.byte	'PROINP',0,1
	.word	517
	.byte	1,7,2,35,0,11
	.byte	'PRODISP',0,1
	.word	517
	.byte	1,6,2,35,0,11
	.byte	'PROIND',0,1
	.word	517
	.byte	1,5,2,35,0,11
	.byte	'PRODISD',0,1
	.word	517
	.byte	1,4,2,35,0,11
	.byte	'PROINHSMCOTP',0,1
	.word	517
	.byte	1,3,2,35,0,11
	.byte	'RES5',0,1
	.word	517
	.byte	1,2,2,35,0,11
	.byte	'PROINOTP',0,1
	.word	517
	.byte	1,1,2,35,0,11
	.byte	'RES7',0,1
	.word	517
	.byte	1,0,2,35,0,11
	.byte	'PROINDBG',0,1
	.word	517
	.byte	1,7,2,35,1,11
	.byte	'PRODISDBG',0,1
	.word	517
	.byte	1,6,2,35,1,11
	.byte	'PROINHSM',0,1
	.word	517
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	517
	.byte	5,0,2,35,1,11
	.byte	'DCFP',0,1
	.word	517
	.byte	1,7,2,35,2,11
	.byte	'DDFP',0,1
	.word	517
	.byte	1,6,2,35,2,11
	.byte	'DDFPX',0,1
	.word	517
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	517
	.byte	1,4,2,35,2,11
	.byte	'DDFD',0,1
	.word	517
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	517
	.byte	1,2,2,35,2,11
	.byte	'ENPE',0,1
	.word	517
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	517
	.byte	8,0,2,35,3,0,22
	.byte	'Ifx_FLASH_FPRO_Bits',0,20,218,1,3
	.word	81308
	.byte	10
	.byte	'_Ifx_FLASH_FSR_Bits',0,20,221,1,16,4,11
	.byte	'FABUSY',0,1
	.word	517
	.byte	1,7,2,35,0,11
	.byte	'D0BUSY',0,1
	.word	517
	.byte	1,6,2,35,0,11
	.byte	'RES1',0,1
	.word	517
	.byte	1,5,2,35,0,11
	.byte	'P0BUSY',0,1
	.word	517
	.byte	1,4,2,35,0,11
	.byte	'P1BUSY',0,1
	.word	517
	.byte	1,3,2,35,0,11
	.byte	'RES5',0,1
	.word	517
	.byte	1,2,2,35,0,11
	.byte	'RES6',0,1
	.word	517
	.byte	1,1,2,35,0,11
	.byte	'PROG',0,1
	.word	517
	.byte	1,0,2,35,0,11
	.byte	'ERASE',0,1
	.word	517
	.byte	1,7,2,35,1,11
	.byte	'PFPAGE',0,1
	.word	517
	.byte	1,6,2,35,1,11
	.byte	'DFPAGE',0,1
	.word	517
	.byte	1,5,2,35,1,11
	.byte	'OPER',0,1
	.word	517
	.byte	1,4,2,35,1,11
	.byte	'SQER',0,1
	.word	517
	.byte	1,3,2,35,1,11
	.byte	'PROER',0,1
	.word	517
	.byte	1,2,2,35,1,11
	.byte	'PFSBER',0,1
	.word	517
	.byte	1,1,2,35,1,11
	.byte	'PFDBER',0,1
	.word	517
	.byte	1,0,2,35,1,11
	.byte	'PFMBER',0,1
	.word	517
	.byte	1,7,2,35,2,11
	.byte	'RES17',0,1
	.word	517
	.byte	1,6,2,35,2,11
	.byte	'DFSBER',0,1
	.word	517
	.byte	1,5,2,35,2,11
	.byte	'DFDBER',0,1
	.word	517
	.byte	1,4,2,35,2,11
	.byte	'DFTBER',0,1
	.word	517
	.byte	1,3,2,35,2,11
	.byte	'DFMBER',0,1
	.word	517
	.byte	1,2,2,35,2,11
	.byte	'SRIADDERR',0,1
	.word	517
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	1068
	.byte	2,7,2,35,2,11
	.byte	'PVER',0,1
	.word	517
	.byte	1,6,2,35,3,11
	.byte	'EVER',0,1
	.word	517
	.byte	1,5,2,35,3,11
	.byte	'SPND',0,1
	.word	517
	.byte	1,4,2,35,3,11
	.byte	'SLM',0,1
	.word	517
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	517
	.byte	1,2,2,35,3,11
	.byte	'ORIER',0,1
	.word	517
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	517
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_FLASH_FSR_Bits',0,20,254,1,3
	.word	81749
	.byte	10
	.byte	'_Ifx_FLASH_ID_Bits',0,20,129,2,16,4,11
	.byte	'MODREV',0,1
	.word	517
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	517
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	1068
	.byte	16,0,2,35,2,0,22
	.byte	'Ifx_FLASH_ID_Bits',0,20,134,2,3
	.word	82355
	.byte	10
	.byte	'_Ifx_FLASH_MARD_Bits',0,20,137,2,16,4,11
	.byte	'HMARGIN',0,1
	.word	517
	.byte	1,7,2,35,0,11
	.byte	'SELD0',0,1
	.word	517
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	517
	.byte	1,5,2,35,0,11
	.byte	'SPND',0,1
	.word	517
	.byte	1,4,2,35,0,11
	.byte	'SPNDERR',0,1
	.word	517
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,2
	.word	1068
	.byte	10,1,2,35,0,11
	.byte	'TRAPDIS',0,1
	.word	517
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1068
	.byte	16,0,2,35,2,0,22
	.byte	'Ifx_FLASH_MARD_Bits',0,20,147,2,3
	.word	82466
	.byte	10
	.byte	'_Ifx_FLASH_MARP_Bits',0,20,150,2,16,4,11
	.byte	'SELP0',0,1
	.word	517
	.byte	1,7,2,35,0,11
	.byte	'SELP1',0,1
	.word	517
	.byte	1,6,2,35,0,11
	.byte	'RES2',0,1
	.word	517
	.byte	1,5,2,35,0,11
	.byte	'RES3',0,1
	.word	517
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	1068
	.byte	11,1,2,35,0,11
	.byte	'TRAPDIS',0,1
	.word	517
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1068
	.byte	16,0,2,35,2,0,22
	.byte	'Ifx_FLASH_MARP_Bits',0,20,159,2,3
	.word	82680
	.byte	10
	.byte	'_Ifx_FLASH_PROCOND_Bits',0,20,162,2,16,4,11
	.byte	'L',0,1
	.word	517
	.byte	1,7,2,35,0,11
	.byte	'NSAFECC',0,1
	.word	517
	.byte	1,6,2,35,0,11
	.byte	'RAMIN',0,1
	.word	517
	.byte	2,4,2,35,0,11
	.byte	'RAMINSEL',0,1
	.word	517
	.byte	4,0,2,35,0,11
	.byte	'OSCCFG',0,1
	.word	517
	.byte	1,7,2,35,1,11
	.byte	'MODE',0,1
	.word	517
	.byte	2,5,2,35,1,11
	.byte	'APREN',0,1
	.word	517
	.byte	1,4,2,35,1,11
	.byte	'CAP0EN',0,1
	.word	517
	.byte	1,3,2,35,1,11
	.byte	'CAP1EN',0,1
	.word	517
	.byte	1,2,2,35,1,11
	.byte	'CAP2EN',0,1
	.word	517
	.byte	1,1,2,35,1,11
	.byte	'CAP3EN',0,1
	.word	517
	.byte	1,0,2,35,1,11
	.byte	'ESR0CNT',0,2
	.word	1068
	.byte	12,4,2,35,2,11
	.byte	'RES29',0,1
	.word	517
	.byte	2,2,2,35,3,11
	.byte	'RES30',0,1
	.word	517
	.byte	1,1,2,35,3,11
	.byte	'RPRO',0,1
	.word	517
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_FLASH_PROCOND_Bits',0,20,179,2,3
	.word	82867
	.byte	10
	.byte	'_Ifx_FLASH_PROCONDBG_Bits',0,20,182,2,16,4,11
	.byte	'OCDSDIS',0,1
	.word	517
	.byte	1,7,2,35,0,11
	.byte	'DBGIFLCK',0,1
	.word	517
	.byte	1,6,2,35,0,11
	.byte	'EDM',0,1
	.word	517
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	494
	.byte	28,0,2,35,0,0,22
	.byte	'Ifx_FLASH_PROCONDBG_Bits',0,20,188,2,3
	.word	83191
	.byte	10
	.byte	'_Ifx_FLASH_PROCONHSM_Bits',0,20,191,2,16,4,11
	.byte	'HSMDBGDIS',0,1
	.word	517
	.byte	1,7,2,35,0,11
	.byte	'DBGIFLCK',0,1
	.word	517
	.byte	1,6,2,35,0,11
	.byte	'TSTIFLCK',0,1
	.word	517
	.byte	1,5,2,35,0,11
	.byte	'HSMTSTDIS',0,1
	.word	517
	.byte	1,4,2,35,0,11
	.byte	'RES15',0,2
	.word	1068
	.byte	12,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	1068
	.byte	16,0,2,35,2,0,22
	.byte	'Ifx_FLASH_PROCONHSM_Bits',0,20,199,2,3
	.word	83334
	.byte	10
	.byte	'_Ifx_FLASH_PROCONHSMCOTP_Bits',0,20,202,2,16,4,11
	.byte	'HSMBOOTEN',0,1
	.word	517
	.byte	1,7,2,35,0,11
	.byte	'SSWWAIT',0,1
	.word	517
	.byte	1,6,2,35,0,11
	.byte	'HSMDX',0,1
	.word	517
	.byte	1,5,2,35,0,11
	.byte	'HSM6X',0,1
	.word	517
	.byte	1,4,2,35,0,11
	.byte	'HSM16X',0,1
	.word	517
	.byte	1,3,2,35,0,11
	.byte	'HSM17X',0,1
	.word	517
	.byte	1,2,2,35,0,11
	.byte	'S6ROM',0,1
	.word	517
	.byte	1,1,2,35,0,11
	.byte	'HSMENPINS',0,2
	.word	1068
	.byte	2,7,2,35,0,11
	.byte	'HSMENRES',0,1
	.word	517
	.byte	2,5,2,35,1,11
	.byte	'DESTDBG',0,1
	.word	517
	.byte	2,3,2,35,1,11
	.byte	'BLKFLAN',0,1
	.word	517
	.byte	1,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	517
	.byte	2,0,2,35,1,11
	.byte	'S16ROM',0,1
	.word	517
	.byte	1,7,2,35,2,11
	.byte	'S17ROM',0,1
	.word	517
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	1068
	.byte	14,0,2,35,2,0,22
	.byte	'Ifx_FLASH_PROCONHSMCOTP_Bits',0,20,219,2,3
	.word	83523
	.byte	10
	.byte	'_Ifx_FLASH_PROCONOTP_Bits',0,20,222,2,16,4,11
	.byte	'S0ROM',0,1
	.word	517
	.byte	1,7,2,35,0,11
	.byte	'S1ROM',0,1
	.word	517
	.byte	1,6,2,35,0,11
	.byte	'S2ROM',0,1
	.word	517
	.byte	1,5,2,35,0,11
	.byte	'S3ROM',0,1
	.word	517
	.byte	1,4,2,35,0,11
	.byte	'S4ROM',0,1
	.word	517
	.byte	1,3,2,35,0,11
	.byte	'S5ROM',0,1
	.word	517
	.byte	1,2,2,35,0,11
	.byte	'S6ROM',0,1
	.word	517
	.byte	1,1,2,35,0,11
	.byte	'S7ROM',0,1
	.word	517
	.byte	1,0,2,35,0,11
	.byte	'S8ROM',0,1
	.word	517
	.byte	1,7,2,35,1,11
	.byte	'S9ROM',0,1
	.word	517
	.byte	1,6,2,35,1,11
	.byte	'S10ROM',0,1
	.word	517
	.byte	1,5,2,35,1,11
	.byte	'S11ROM',0,1
	.word	517
	.byte	1,4,2,35,1,11
	.byte	'S12ROM',0,1
	.word	517
	.byte	1,3,2,35,1,11
	.byte	'S13ROM',0,1
	.word	517
	.byte	1,2,2,35,1,11
	.byte	'S14ROM',0,1
	.word	517
	.byte	1,1,2,35,1,11
	.byte	'S15ROM',0,1
	.word	517
	.byte	1,0,2,35,1,11
	.byte	'S16ROM',0,1
	.word	517
	.byte	1,7,2,35,2,11
	.byte	'S17ROM',0,1
	.word	517
	.byte	1,6,2,35,2,11
	.byte	'S18ROM',0,1
	.word	517
	.byte	1,5,2,35,2,11
	.byte	'S19ROM',0,1
	.word	517
	.byte	1,4,2,35,2,11
	.byte	'S20ROM',0,1
	.word	517
	.byte	1,3,2,35,2,11
	.byte	'S21ROM',0,1
	.word	517
	.byte	1,2,2,35,2,11
	.byte	'S22ROM',0,1
	.word	517
	.byte	1,1,2,35,2,11
	.byte	'S23ROM',0,1
	.word	517
	.byte	1,0,2,35,2,11
	.byte	'S24ROM',0,1
	.word	517
	.byte	1,7,2,35,3,11
	.byte	'S25ROM',0,1
	.word	517
	.byte	1,6,2,35,3,11
	.byte	'S26ROM',0,1
	.word	517
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	517
	.byte	2,3,2,35,3,11
	.byte	'BML',0,1
	.word	517
	.byte	2,1,2,35,3,11
	.byte	'TP',0,1
	.word	517
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_FLASH_PROCONOTP_Bits',0,20,254,2,3
	.word	83886
	.byte	10
	.byte	'_Ifx_FLASH_PROCONP_Bits',0,20,129,3,16,4,11
	.byte	'S0L',0,1
	.word	517
	.byte	1,7,2,35,0,11
	.byte	'S1L',0,1
	.word	517
	.byte	1,6,2,35,0,11
	.byte	'S2L',0,1
	.word	517
	.byte	1,5,2,35,0,11
	.byte	'S3L',0,1
	.word	517
	.byte	1,4,2,35,0,11
	.byte	'S4L',0,1
	.word	517
	.byte	1,3,2,35,0,11
	.byte	'S5L',0,1
	.word	517
	.byte	1,2,2,35,0,11
	.byte	'S6L',0,1
	.word	517
	.byte	1,1,2,35,0,11
	.byte	'S7L',0,1
	.word	517
	.byte	1,0,2,35,0,11
	.byte	'S8L',0,1
	.word	517
	.byte	1,7,2,35,1,11
	.byte	'S9L',0,1
	.word	517
	.byte	1,6,2,35,1,11
	.byte	'S10L',0,1
	.word	517
	.byte	1,5,2,35,1,11
	.byte	'S11L',0,1
	.word	517
	.byte	1,4,2,35,1,11
	.byte	'S12L',0,1
	.word	517
	.byte	1,3,2,35,1,11
	.byte	'S13L',0,1
	.word	517
	.byte	1,2,2,35,1,11
	.byte	'S14L',0,1
	.word	517
	.byte	1,1,2,35,1,11
	.byte	'S15L',0,1
	.word	517
	.byte	1,0,2,35,1,11
	.byte	'S16L',0,1
	.word	517
	.byte	1,7,2,35,2,11
	.byte	'S17L',0,1
	.word	517
	.byte	1,6,2,35,2,11
	.byte	'S18L',0,1
	.word	517
	.byte	1,5,2,35,2,11
	.byte	'S19L',0,1
	.word	517
	.byte	1,4,2,35,2,11
	.byte	'S20L',0,1
	.word	517
	.byte	1,3,2,35,2,11
	.byte	'S21L',0,1
	.word	517
	.byte	1,2,2,35,2,11
	.byte	'S22L',0,1
	.word	517
	.byte	1,1,2,35,2,11
	.byte	'S23L',0,1
	.word	517
	.byte	1,0,2,35,2,11
	.byte	'S24L',0,1
	.word	517
	.byte	1,7,2,35,3,11
	.byte	'S25L',0,1
	.word	517
	.byte	1,6,2,35,3,11
	.byte	'S26L',0,1
	.word	517
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	517
	.byte	4,1,2,35,3,11
	.byte	'RPRO',0,1
	.word	517
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_FLASH_PROCONP_Bits',0,20,160,3,3
	.word	84481
	.byte	10
	.byte	'_Ifx_FLASH_PROCONWOP_Bits',0,20,163,3,16,4,11
	.byte	'S0WOP',0,1
	.word	517
	.byte	1,7,2,35,0,11
	.byte	'S1WOP',0,1
	.word	517
	.byte	1,6,2,35,0,11
	.byte	'S2WOP',0,1
	.word	517
	.byte	1,5,2,35,0,11
	.byte	'S3WOP',0,1
	.word	517
	.byte	1,4,2,35,0,11
	.byte	'S4WOP',0,1
	.word	517
	.byte	1,3,2,35,0,11
	.byte	'S5WOP',0,1
	.word	517
	.byte	1,2,2,35,0,11
	.byte	'S6WOP',0,1
	.word	517
	.byte	1,1,2,35,0,11
	.byte	'S7WOP',0,1
	.word	517
	.byte	1,0,2,35,0,11
	.byte	'S8WOP',0,1
	.word	517
	.byte	1,7,2,35,1,11
	.byte	'S9WOP',0,1
	.word	517
	.byte	1,6,2,35,1,11
	.byte	'S10WOP',0,1
	.word	517
	.byte	1,5,2,35,1,11
	.byte	'S11WOP',0,1
	.word	517
	.byte	1,4,2,35,1,11
	.byte	'S12WOP',0,1
	.word	517
	.byte	1,3,2,35,1,11
	.byte	'S13WOP',0,1
	.word	517
	.byte	1,2,2,35,1,11
	.byte	'S14WOP',0,1
	.word	517
	.byte	1,1,2,35,1,11
	.byte	'S15WOP',0,1
	.word	517
	.byte	1,0,2,35,1,11
	.byte	'S16WOP',0,1
	.word	517
	.byte	1,7,2,35,2,11
	.byte	'S17WOP',0,1
	.word	517
	.byte	1,6,2,35,2,11
	.byte	'S18WOP',0,1
	.word	517
	.byte	1,5,2,35,2,11
	.byte	'S19WOP',0,1
	.word	517
	.byte	1,4,2,35,2,11
	.byte	'S20WOP',0,1
	.word	517
	.byte	1,3,2,35,2,11
	.byte	'S21WOP',0,1
	.word	517
	.byte	1,2,2,35,2,11
	.byte	'S22WOP',0,1
	.word	517
	.byte	1,1,2,35,2,11
	.byte	'S23WOP',0,1
	.word	517
	.byte	1,0,2,35,2,11
	.byte	'S24WOP',0,1
	.word	517
	.byte	1,7,2,35,3,11
	.byte	'S25WOP',0,1
	.word	517
	.byte	1,6,2,35,3,11
	.byte	'S26WOP',0,1
	.word	517
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	517
	.byte	4,1,2,35,3,11
	.byte	'DATM',0,1
	.word	517
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_FLASH_PROCONWOP_Bits',0,20,194,3,3
	.word	85005
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG0_Bits',0,20,197,3,16,4,11
	.byte	'TAG',0,1
	.word	517
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	494
	.byte	26,0,2,35,0,0,22
	.byte	'Ifx_FLASH_RDB_CFG0_Bits',0,20,201,3,3
	.word	85587
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG1_Bits',0,20,204,3,16,4,11
	.byte	'TAG',0,1
	.word	517
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	494
	.byte	26,0,2,35,0,0,22
	.byte	'Ifx_FLASH_RDB_CFG1_Bits',0,20,208,3,3
	.word	85689
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG2_Bits',0,20,211,3,16,4,11
	.byte	'TAG',0,1
	.word	517
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	494
	.byte	26,0,2,35,0,0,22
	.byte	'Ifx_FLASH_RDB_CFG2_Bits',0,20,215,3,3
	.word	85791
	.byte	10
	.byte	'_Ifx_FLASH_RRAD_Bits',0,20,218,3,16,4,11
	.byte	'reserved_0',0,1
	.word	517
	.byte	3,5,2,35,0,11
	.byte	'ADD',0,4
	.word	494
	.byte	29,0,2,35,0,0,22
	.byte	'Ifx_FLASH_RRAD_Bits',0,20,222,3,3
	.word	85893
	.byte	10
	.byte	'_Ifx_FLASH_RRCT_Bits',0,20,225,3,16,4,11
	.byte	'STRT',0,1
	.word	517
	.byte	1,7,2,35,0,11
	.byte	'STP',0,1
	.word	517
	.byte	1,6,2,35,0,11
	.byte	'BUSY',0,1
	.word	517
	.byte	1,5,2,35,0,11
	.byte	'DONE',0,1
	.word	517
	.byte	1,4,2,35,0,11
	.byte	'ERR',0,1
	.word	517
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	517
	.byte	3,0,2,35,0,11
	.byte	'EOBM',0,1
	.word	517
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	517
	.byte	7,0,2,35,1,11
	.byte	'CNT',0,2
	.word	1068
	.byte	16,0,2,35,2,0,22
	.byte	'Ifx_FLASH_RRCT_Bits',0,20,236,3,3
	.word	85987
	.byte	10
	.byte	'_Ifx_FLASH_RRD0_Bits',0,20,239,3,16,4,11
	.byte	'DATA',0,4
	.word	494
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_FLASH_RRD0_Bits',0,20,242,3,3
	.word	86197
	.byte	10
	.byte	'_Ifx_FLASH_RRD1_Bits',0,20,245,3,16,4,11
	.byte	'DATA',0,4
	.word	494
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_FLASH_RRD1_Bits',0,20,248,3,3
	.word	86270
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_CFG_Bits',0,20,251,3,16,4,11
	.byte	'SEL',0,1
	.word	517
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	517
	.byte	2,0,2,35,0,11
	.byte	'CLR',0,1
	.word	517
	.byte	1,7,2,35,1,11
	.byte	'DIS',0,1
	.word	517
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	494
	.byte	22,0,2,35,0,0,22
	.byte	'Ifx_FLASH_UBAB_CFG_Bits',0,20,130,4,3
	.word	86343
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_STAT_Bits',0,20,133,4,16,4,11
	.byte	'VLD0',0,1
	.word	517
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	494
	.byte	31,0,2,35,0,0,22
	.byte	'Ifx_FLASH_UBAB_STAT_Bits',0,20,137,4,3
	.word	86498
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_TOP_Bits',0,20,140,4,16,4,11
	.byte	'reserved_0',0,1
	.word	517
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	494
	.byte	19,8,2,35,0,11
	.byte	'ERR',0,1
	.word	517
	.byte	6,2,2,35,3,11
	.byte	'VLD',0,1
	.word	517
	.byte	1,1,2,35,3,11
	.byte	'CLR',0,1
	.word	517
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_FLASH_UBAB_TOP_Bits',0,20,147,4,3
	.word	86603
	.byte	12,20,155,4,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	79008
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_ACCEN0',0,20,160,4,3
	.word	86751
	.byte	12,20,163,4,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	79569
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_ACCEN1',0,20,168,4,3
	.word	86817
	.byte	12,20,171,4,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	79650
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_CBAB_CFG',0,20,176,4,3
	.word	86883
	.byte	12,20,179,4,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	79803
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_CBAB_STAT',0,20,184,4,3
	.word	86951
	.byte	12,20,187,4,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	80051
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_CBAB_TOP',0,20,192,4,3
	.word	87020
	.byte	12,20,195,4,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	80197
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_COMM0',0,20,200,4,3
	.word	87088
	.byte	12,20,203,4,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	80295
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_COMM1',0,20,208,4,3
	.word	87153
	.byte	12,20,211,4,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	80411
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_COMM2',0,20,216,4,3
	.word	87218
	.byte	12,20,219,4,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	80527
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_ECCRD',0,20,224,4,3
	.word	87283
	.byte	12,20,227,4,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	80667
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_ECCRP',0,20,232,4,3
	.word	87348
	.byte	12,20,235,4,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	80807
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_ECCW',0,20,240,4,3
	.word	87413
	.byte	12,20,243,4,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	80946
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_FCON',0,20,248,4,3
	.word	87477
	.byte	12,20,251,4,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81308
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_FPRO',0,20,128,5,3
	.word	87541
	.byte	12,20,131,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81749
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_FSR',0,20,136,5,3
	.word	87605
	.byte	12,20,139,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	82355
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_ID',0,20,144,5,3
	.word	87668
	.byte	12,20,147,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	82466
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_MARD',0,20,152,5,3
	.word	87730
	.byte	12,20,155,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	82680
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_MARP',0,20,160,5,3
	.word	87794
	.byte	12,20,163,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	82867
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_PROCOND',0,20,168,5,3
	.word	87858
	.byte	12,20,171,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	83191
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_PROCONDBG',0,20,176,5,3
	.word	87925
	.byte	12,20,179,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	83334
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_PROCONHSM',0,20,184,5,3
	.word	87994
	.byte	12,20,187,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	83523
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_PROCONHSMCOTP',0,20,192,5,3
	.word	88063
	.byte	12,20,195,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	83886
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_PROCONOTP',0,20,200,5,3
	.word	88136
	.byte	12,20,203,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	84481
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_PROCONP',0,20,208,5,3
	.word	88205
	.byte	12,20,211,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	85005
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_PROCONWOP',0,20,216,5,3
	.word	88272
	.byte	12,20,219,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	85587
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_RDB_CFG0',0,20,224,5,3
	.word	88341
	.byte	12,20,227,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	85689
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_RDB_CFG1',0,20,232,5,3
	.word	88409
	.byte	12,20,235,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	85791
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_RDB_CFG2',0,20,240,5,3
	.word	88477
	.byte	12,20,243,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	85893
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_RRAD',0,20,248,5,3
	.word	88545
	.byte	12,20,251,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	85987
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_RRCT',0,20,128,6,3
	.word	88609
	.byte	12,20,131,6,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	86197
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_RRD0',0,20,136,6,3
	.word	88673
	.byte	12,20,139,6,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	86270
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_RRD1',0,20,144,6,3
	.word	88737
	.byte	12,20,147,6,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	86343
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_UBAB_CFG',0,20,152,6,3
	.word	88801
	.byte	12,20,155,6,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	86498
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_UBAB_STAT',0,20,160,6,3
	.word	88869
	.byte	12,20,163,6,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	86603
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_UBAB_TOP',0,20,168,6,3
	.word	88938
	.byte	10
	.byte	'_Ifx_FLASH_CBAB',0,20,179,6,25,12,13
	.byte	'CFG',0
	.word	86883
	.byte	4,2,35,0,13
	.byte	'STAT',0
	.word	86951
	.byte	4,2,35,4,13
	.byte	'TOP',0
	.word	87020
	.byte	4,2,35,8,0,14
	.word	89006
	.byte	22
	.byte	'Ifx_FLASH_CBAB',0,20,184,6,3
	.word	89069
	.byte	10
	.byte	'_Ifx_FLASH_RDB',0,20,187,6,25,12,13
	.byte	'CFG0',0
	.word	88341
	.byte	4,2,35,0,13
	.byte	'CFG1',0
	.word	88409
	.byte	4,2,35,4,13
	.byte	'CFG2',0
	.word	88477
	.byte	4,2,35,8,0,14
	.word	89098
	.byte	22
	.byte	'Ifx_FLASH_RDB',0,20,192,6,3
	.word	89162
	.byte	10
	.byte	'_Ifx_FLASH_UBAB',0,20,195,6,25,12,13
	.byte	'CFG',0
	.word	88801
	.byte	4,2,35,0,13
	.byte	'STAT',0
	.word	88869
	.byte	4,2,35,4,13
	.byte	'TOP',0
	.word	88938
	.byte	4,2,35,8,0,14
	.word	89190
	.byte	22
	.byte	'Ifx_FLASH_UBAB',0,20,200,6,3
	.word	89253
	.byte	22
	.byte	'Ifx_P_ACCEN0_Bits',0,10,79,3
	.word	9007
	.byte	22
	.byte	'Ifx_P_ACCEN1_Bits',0,10,85,3
	.word	8920
	.byte	22
	.byte	'Ifx_P_ESR_Bits',0,10,107,3
	.word	5263
	.byte	22
	.byte	'Ifx_P_ID_Bits',0,10,115,3
	.word	3316
	.byte	22
	.byte	'Ifx_P_IN_Bits',0,10,137,1,3
	.word	4311
	.byte	22
	.byte	'Ifx_P_IOCR0_Bits',0,10,150,1,3
	.word	3444
	.byte	22
	.byte	'Ifx_P_IOCR12_Bits',0,10,163,1,3
	.word	4091
	.byte	22
	.byte	'Ifx_P_IOCR4_Bits',0,10,176,1,3
	.word	3659
	.byte	22
	.byte	'Ifx_P_IOCR8_Bits',0,10,189,1,3
	.word	3874
	.byte	22
	.byte	'Ifx_P_LPCR0_Bits',0,10,197,1,3
	.word	8279
	.byte	22
	.byte	'Ifx_P_LPCR1_Bits',0,10,205,1,3
	.word	8403
	.byte	22
	.byte	'Ifx_P_LPCR1_P21_Bits',0,10,215,1,3
	.word	8487
	.byte	22
	.byte	'Ifx_P_LPCR2_Bits',0,10,229,1,3
	.word	8667
	.byte	22
	.byte	'Ifx_P_OMCR0_Bits',0,10,240,1,3
	.word	6918
	.byte	22
	.byte	'Ifx_P_OMCR12_Bits',0,10,250,1,3
	.word	7442
	.byte	22
	.byte	'Ifx_P_OMCR4_Bits',0,10,133,2,3
	.word	7092
	.byte	22
	.byte	'Ifx_P_OMCR8_Bits',0,10,144,2,3
	.word	7266
	.byte	22
	.byte	'Ifx_P_OMCR_Bits',0,10,166,2,3
	.word	7931
	.byte	22
	.byte	'Ifx_P_OMR_Bits',0,10,203,2,3
	.word	2745
	.byte	22
	.byte	'Ifx_P_OMSR0_Bits',0,10,213,2,3
	.word	6255
	.byte	22
	.byte	'Ifx_P_OMSR12_Bits',0,10,224,2,3
	.word	6743
	.byte	22
	.byte	'Ifx_P_OMSR4_Bits',0,10,235,2,3
	.word	6402
	.byte	22
	.byte	'Ifx_P_OMSR8_Bits',0,10,246,2,3
	.word	6571
	.byte	22
	.byte	'Ifx_P_OMSR_Bits',0,10,140,3,3
	.word	7598
	.byte	22
	.byte	'Ifx_P_OUT_Bits',0,10,162,3,3
	.word	2429
	.byte	22
	.byte	'Ifx_P_PCSR_Bits',0,10,180,3,3
	.word	5969
	.byte	22
	.byte	'Ifx_P_PDISC_Bits',0,10,202,3,3
	.word	5603
	.byte	22
	.byte	'Ifx_P_PDR0_Bits',0,10,223,3,3
	.word	4634
	.byte	22
	.byte	'Ifx_P_PDR1_Bits',0,10,244,3,3
	.word	4938
	.byte	22
	.byte	'Ifx_P_ACCEN0',0,10,129,4,3
	.word	9534
	.byte	22
	.byte	'Ifx_P_ACCEN1',0,10,137,4,3
	.word	8967
	.byte	22
	.byte	'Ifx_P_ESR',0,10,145,4,3
	.word	5554
	.byte	22
	.byte	'Ifx_P_ID',0,10,153,4,3
	.word	3395
	.byte	22
	.byte	'Ifx_P_IN',0,10,161,4,3
	.word	4585
	.byte	22
	.byte	'Ifx_P_IOCR0',0,10,169,4,3
	.word	3619
	.byte	22
	.byte	'Ifx_P_IOCR12',0,10,177,4,3
	.word	4271
	.byte	22
	.byte	'Ifx_P_IOCR4',0,10,185,4,3
	.word	3834
	.byte	22
	.byte	'Ifx_P_IOCR8',0,10,193,4,3
	.word	4051
	.byte	22
	.byte	'Ifx_P_LPCR0',0,10,201,4,3
	.word	8363
	.byte	22
	.byte	'Ifx_P_LPCR1',0,10,210,4,3
	.word	8612
	.byte	22
	.byte	'Ifx_P_LPCR2',0,10,218,4,3
	.word	8871
	.byte	22
	.byte	'Ifx_P_OMCR',0,10,226,4,3
	.word	8239
	.byte	22
	.byte	'Ifx_P_OMCR0',0,10,234,4,3
	.word	7052
	.byte	22
	.byte	'Ifx_P_OMCR12',0,10,242,4,3
	.word	7558
	.byte	22
	.byte	'Ifx_P_OMCR4',0,10,250,4,3
	.word	7226
	.byte	22
	.byte	'Ifx_P_OMCR8',0,10,130,5,3
	.word	7402
	.byte	22
	.byte	'Ifx_P_OMR',0,10,138,5,3
	.word	3276
	.byte	22
	.byte	'Ifx_P_OMSR',0,10,146,5,3
	.word	7891
	.byte	22
	.byte	'Ifx_P_OMSR0',0,10,154,5,3
	.word	6362
	.byte	22
	.byte	'Ifx_P_OMSR12',0,10,162,5,3
	.word	6878
	.byte	22
	.byte	'Ifx_P_OMSR4',0,10,170,5,3
	.word	6531
	.byte	22
	.byte	'Ifx_P_OMSR8',0,10,178,5,3
	.word	6703
	.byte	22
	.byte	'Ifx_P_OUT',0,10,186,5,3
	.word	2705
	.byte	22
	.byte	'Ifx_P_PCSR',0,10,194,5,3
	.word	6215
	.byte	22
	.byte	'Ifx_P_PDISC',0,10,202,5,3
	.word	5929
	.byte	22
	.byte	'Ifx_P_PDR0',0,10,210,5,3
	.word	4898
	.byte	22
	.byte	'Ifx_P_PDR1',0,10,218,5,3
	.word	5214
	.byte	14
	.word	9574
	.byte	22
	.byte	'Ifx_P',0,10,139,6,3
	.word	90600
	.byte	15,9,83,9,1,16
	.byte	'IfxPort_InputMode_undefined',0,127,16
	.byte	'IfxPort_InputMode_noPullDevice',0,0,16
	.byte	'IfxPort_InputMode_pullDown',0,8,16
	.byte	'IfxPort_InputMode_pullUp',0,16,0,22
	.byte	'IfxPort_InputMode',0,9,89,3
	.word	90620
	.byte	15,9,120,9,1,16
	.byte	'IfxPort_OutputIdx_general',0,128,1,16
	.byte	'IfxPort_OutputIdx_alt1',0,136,1,16
	.byte	'IfxPort_OutputIdx_alt2',0,144,1,16
	.byte	'IfxPort_OutputIdx_alt3',0,152,1,16
	.byte	'IfxPort_OutputIdx_alt4',0,160,1,16
	.byte	'IfxPort_OutputIdx_alt5',0,168,1,16
	.byte	'IfxPort_OutputIdx_alt6',0,176,1,16
	.byte	'IfxPort_OutputIdx_alt7',0,184,1,0,22
	.byte	'IfxPort_OutputIdx',0,9,130,1,3
	.word	90771
	.byte	15,9,134,1,9,1,16
	.byte	'IfxPort_OutputMode_pushPull',0,128,1,16
	.byte	'IfxPort_OutputMode_openDrain',0,192,1,0,22
	.byte	'IfxPort_OutputMode',0,9,138,1,3
	.word	91015
	.byte	15,9,144,1,9,1,16
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed1',0,0,16
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed2',0,1,16
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed3',0,2,16
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed4',0,3,16
	.byte	'IfxPort_PadDriver_lvdsSpeed1',0,4,16
	.byte	'IfxPort_PadDriver_lvdsSpeed2',0,5,16
	.byte	'IfxPort_PadDriver_lvdsSpeed3',0,6,16
	.byte	'IfxPort_PadDriver_lvdsSpeed4',0,7,16
	.byte	'IfxPort_PadDriver_ttlSpeed1',0,8,16
	.byte	'IfxPort_PadDriver_ttlSpeed2',0,9,16
	.byte	'IfxPort_PadDriver_ttlSpeed3',0,10,16
	.byte	'IfxPort_PadDriver_ttlSpeed4',0,11,0,22
	.byte	'IfxPort_PadDriver',0,9,158,1,3
	.word	91113
	.byte	22
	.byte	'IfxPort_State',0,9,178,1,3
	.word	10187
	.byte	24,9,190,1,9,8,13
	.byte	'port',0
	.word	10182
	.byte	4,2,35,0,13
	.byte	'pinIndex',0
	.word	517
	.byte	1,2,35,4,0,22
	.byte	'IfxPort_Pin',0,9,194,1,3
	.word	91578
	.byte	22
	.byte	'IfxScuCcu_PllStepsFunctionHook',0,21,148,1,16
	.word	233
	.byte	24,21,212,5,9,8,13
	.byte	'value',0
	.word	2198
	.byte	4,2,35,0,13
	.byte	'mask',0
	.word	2198
	.byte	4,2,35,4,0,22
	.byte	'IfxScuCcu_CcuconRegConfig',0,21,216,5,3
	.word	91678
	.byte	24,21,221,5,9,8,13
	.byte	'pDivider',0
	.word	517
	.byte	1,2,35,0,13
	.byte	'nDivider',0
	.word	517
	.byte	1,2,35,1,13
	.byte	'k2Initial',0
	.word	517
	.byte	1,2,35,2,13
	.byte	'waitTime',0
	.word	290
	.byte	4,2,35,4,0,22
	.byte	'IfxScuCcu_InitialStepConfig',0,21,227,5,3
	.word	91749
	.byte	24,21,231,5,9,12,13
	.byte	'k2Step',0
	.word	517
	.byte	1,2,35,0,13
	.byte	'waitTime',0
	.word	290
	.byte	4,2,35,2,13
	.byte	'hookFunction',0
	.word	91638
	.byte	4,2,35,8,0,22
	.byte	'IfxScuCcu_PllStepsConfig',0,21,236,5,3
	.word	91866
	.byte	3
	.word	230
	.byte	24,21,244,5,9,48,13
	.byte	'ccucon0',0
	.word	91678
	.byte	8,2,35,0,13
	.byte	'ccucon1',0
	.word	91678
	.byte	8,2,35,8,13
	.byte	'ccucon2',0
	.word	91678
	.byte	8,2,35,16,13
	.byte	'ccucon5',0
	.word	91678
	.byte	8,2,35,24,13
	.byte	'ccucon6',0
	.word	91678
	.byte	8,2,35,32,13
	.byte	'ccucon7',0
	.word	91678
	.byte	8,2,35,40,0,22
	.byte	'IfxScuCcu_ClockDistributionConfig',0,21,252,5,3
	.word	91968
	.byte	24,21,128,6,9,8,13
	.byte	'value',0
	.word	2198
	.byte	4,2,35,0,13
	.byte	'mask',0
	.word	2198
	.byte	4,2,35,4,0,22
	.byte	'IfxScuCcu_FlashWaitstateConfig',0,21,132,6,3
	.word	92120
	.byte	3
	.word	91866
	.byte	24,21,137,6,9,16,13
	.byte	'numOfPllDividerSteps',0
	.word	517
	.byte	1,2,35,0,13
	.byte	'pllDividerStep',0
	.word	92196
	.byte	4,2,35,4,13
	.byte	'pllInitialStep',0
	.word	91749
	.byte	8,2,35,8,0,22
	.byte	'IfxScuCcu_SysPllConfig',0,21,142,6,3
	.word	92201
	.byte	15,22,104,9,1,16
	.byte	'IfxDma_ChannelIncrementCircular_none',0,0,16
	.byte	'IfxDma_ChannelIncrementCircular_2',0,1,16
	.byte	'IfxDma_ChannelIncrementCircular_4',0,2,16
	.byte	'IfxDma_ChannelIncrementCircular_8',0,3,16
	.byte	'IfxDma_ChannelIncrementCircular_16',0,4,16
	.byte	'IfxDma_ChannelIncrementCircular_32',0,5,16
	.byte	'IfxDma_ChannelIncrementCircular_64',0,6,16
	.byte	'IfxDma_ChannelIncrementCircular_128',0,7,16
	.byte	'IfxDma_ChannelIncrementCircular_256',0,8,16
	.byte	'IfxDma_ChannelIncrementCircular_512',0,9,16
	.byte	'IfxDma_ChannelIncrementCircular_1024',0,10,16
	.byte	'IfxDma_ChannelIncrementCircular_2048',0,11,16
	.byte	'IfxDma_ChannelIncrementCircular_4096',0,12,16
	.byte	'IfxDma_ChannelIncrementCircular_8192',0,13,16
	.byte	'IfxDma_ChannelIncrementCircular_16384',0,14,16
	.byte	'IfxDma_ChannelIncrementCircular_32768',0,15,0,22
	.byte	'IfxDma_ChannelIncrementCircular',0,22,122,3
	.word	92318
	.byte	15,22,127,9,1,16
	.byte	'IfxDma_ChannelIncrementDirection_negative',0,0,16
	.byte	'IfxDma_ChannelIncrementDirection_positive',0,1,0,22
	.byte	'IfxDma_ChannelIncrementDirection',0,22,131,1,3
	.word	92972
	.byte	15,22,136,1,9,1,16
	.byte	'IfxDma_ChannelIncrementStep_1',0,0,16
	.byte	'IfxDma_ChannelIncrementStep_2',0,1,16
	.byte	'IfxDma_ChannelIncrementStep_4',0,2,16
	.byte	'IfxDma_ChannelIncrementStep_8',0,3,16
	.byte	'IfxDma_ChannelIncrementStep_16',0,4,16
	.byte	'IfxDma_ChannelIncrementStep_32',0,5,16
	.byte	'IfxDma_ChannelIncrementStep_64',0,6,16
	.byte	'IfxDma_ChannelIncrementStep_128',0,7,0,22
	.byte	'IfxDma_ChannelIncrementStep',0,22,146,1,3
	.word	93108
	.byte	15,22,160,1,9,1,16
	.byte	'IfxDma_ChannelMove_1',0,0,16
	.byte	'IfxDma_ChannelMove_2',0,1,16
	.byte	'IfxDma_ChannelMove_4',0,2,16
	.byte	'IfxDma_ChannelMove_8',0,3,16
	.byte	'IfxDma_ChannelMove_16',0,4,16
	.byte	'IfxDma_ChannelMove_3',0,5,16
	.byte	'IfxDma_ChannelMove_5',0,6,16
	.byte	'IfxDma_ChannelMove_9',0,7,0,22
	.byte	'IfxDma_ChannelMove',0,22,170,1,3
	.word	93413
	.byte	15,22,175,1,9,1,16
	.byte	'IfxDma_ChannelMoveSize_8bit',0,0,16
	.byte	'IfxDma_ChannelMoveSize_16bit',0,1,16
	.byte	'IfxDma_ChannelMoveSize_32bit',0,2,16
	.byte	'IfxDma_ChannelMoveSize_64bit',0,3,16
	.byte	'IfxDma_ChannelMoveSize_128bit',0,4,16
	.byte	'IfxDma_ChannelMoveSize_256bit',0,5,0,22
	.byte	'IfxDma_ChannelMoveSize',0,22,183,1,3
	.word	93633
	.byte	15,22,239,1,9,1,16
	.byte	'IfxDma_ChannelShadow_none',0,0,16
	.byte	'IfxDma_ChannelShadow_src',0,1,16
	.byte	'IfxDma_ChannelShadow_dst',0,2,16
	.byte	'IfxDma_ChannelShadow_srcDirectWrite',0,5,16
	.byte	'IfxDma_ChannelShadow_dstDirectWrite',0,6,16
	.byte	'IfxDma_ChannelShadow_doubleSourceBufferingSwSwitch',0,8,16
	.byte	'IfxDma_ChannelShadow_doubleSourceBufferingHwSwSwitch',0,9,16
	.byte	'IfxDma_ChannelShadow_doubleDestinationBufferingSwSwitch',0,10,16
	.byte	'IfxDma_ChannelShadow_doubleDestinationBufferingHwSwSwitch',0,11,16
	.byte	'IfxDma_ChannelShadow_linkedList',0,12,16
	.byte	'IfxDma_ChannelShadow_accumulatedLinkedList',0,13,16
	.byte	'IfxDma_ChannelShadow_safeLinkedList',0,14,16
	.byte	'IfxDma_ChannelShadow_conditionalLinkedList',0,15,0,22
	.byte	'IfxDma_ChannelShadow',0,22,254,1,3
	.word	93859
	.byte	15,22,128,2,9,1,16
	.byte	'IfxDma_HardwareResourcePartition_0',0,0,16
	.byte	'IfxDma_HardwareResourcePartition_1',0,1,16
	.byte	'IfxDma_HardwareResourcePartition_2',0,2,16
	.byte	'IfxDma_HardwareResourcePartition_3',0,3,0,22
	.byte	'IfxDma_HardwareResourcePartition',0,22,134,2,3
	.word	94442
	.byte	15,22,138,2,9,1,16
	.byte	'IfxDma_MoveEngine_0',0,0,16
	.byte	'IfxDma_MoveEngine_1',0,1,0,22
	.byte	'IfxDma_MoveEngine',0,22,142,2,3
	.word	94639
	.byte	15,22,147,2,9,1,16
	.byte	'IfxDma_SleepMode_enable',0,0,16
	.byte	'IfxDma_SleepMode_disable',0,1,0,22
	.byte	'IfxDma_SleepMode',0,22,151,2,3
	.word	94717
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L4:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,21,0,54,15,39,12,0,0,3,15,0,73,19,0,0,4,46,1,3,8,32,13
	.byte	58,15,59,15,57,15,54,15,39,12,0,0,5,5,0,3,8,58,15,59,15,57,15,73,19,0,0,6,11,0,0,0,7,36,0,3,8,11,15,62
	.byte	15,0,0,8,46,1,3,8,32,13,58,15,59,15,57,15,73,19,54,15,39,12,0,0,9,59,0,3,8,0,0,10,19,1,3,8,58,15,59,15
	.byte	57,15,11,15,0,0,11,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,12,23,1,58,15,59,15,57,15,11,15,0,0,13,13
	.byte	0,3,8,73,19,11,15,56,9,0,0,14,53,0,73,19,0,0,15,4,1,58,15,59,15,57,15,11,15,0,0,16,40,0,3,8,28,13,0,0
	.byte	17,11,1,0,0,18,1,1,11,15,73,19,0,0,19,33,0,47,15,0,0,20,46,1,49,19,0,0,21,5,0,49,19,0,0,22,22,0,3,8,58
	.byte	15,59,15,57,15,73,19,0,0,23,21,0,54,15,0,0,24,19,1,58,15,59,15,57,15,11,15,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L5:
	.word	.L7-.L6
.L6:
	.half	3
	.word	.L9-.L8
.L8:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg',0
	.byte	0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_Intrinsics.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Src\\Std\\IfxSrc.h',0
	.byte	0,0,0
	.byte	'IfxSrc_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Scu\\Std\\IfxScuWdt.h',0
	.byte	0,0,0
	.byte	'IfxScu_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxCpu_cfg.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Port\\Std\\IfxPort.h',0
	.byte	0,0,0
	.byte	'IfxPort_regdef.h',0,1,0,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Dma/Std/IfxDma.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Platform_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Ifx_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxDma_cfg.h',0
	.byte	0,0,0
	.byte	'IfxDma_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxSrc_cfg.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxScu_cfg.h',0
	.byte	0,0,0
	.byte	'IfxCpu_regdef.h',0,1,0,0
	.byte	'IfxStm_regdef.h',0,1,0,0
	.byte	'IfxFlash_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Scu\\Std\\IfxScuCcu.h',0
	.byte	0,0,0
	.byte	'..\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Dma\\Std\\IfxDma.h',0,0,0,0,0
.L9:
.L7:
	; Module end
