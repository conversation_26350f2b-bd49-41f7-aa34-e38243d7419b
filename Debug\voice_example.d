voice_example.o :	../code/user1/voice_example.c
../code/user1/voice_example.c :
voice_example.o :	..\code\user1\gsm_4g_user.h
..\code\user1\gsm_4g_user.h :
voice_example.o :	..\code\user1\gsm_4g_api.h
..\code\user1\gsm_4g_api.h :
voice_example.o :	"F:\ADS\AURIX-Studio-1.10.2\tools\Compilers\Tasking_1.1r8\ctc\include\stdint.h"
"F:\ADS\AURIX-Studio-1.10.2\tools\Compilers\Tasking_1.1r8\ctc\include\stdint.h" :
voice_example.o :	"F:\ADS\AURIX-Studio-1.10.2\tools\Compilers\Tasking_1.1r8\ctc\include\typeinfo.h"
"F:\ADS\AURIX-Studio-1.10.2\tools\Compilers\Tasking_1.1r8\ctc\include\typeinfo.h" :
voice_example.o :	"F:\ADS\AURIX-Studio-1.10.2\tools\Compilers\Tasking_1.1r8\ctc\include\stdbool.h"
"F:\ADS\AURIX-Studio-1.10.2\tools\Compilers\Tasking_1.1r8\ctc\include\stdbool.h" :
voice_example.o :	"F:\ADS\AURIX-Studio-1.10.2\tools\Compilers\Tasking_1.1r8\ctc\include\string.h"
"F:\ADS\AURIX-Studio-1.10.2\tools\Compilers\Tasking_1.1r8\ctc\include\string.h" :
voice_example.o :	"F:\ADS\AURIX-Studio-1.10.2\tools\Compilers\Tasking_1.1r8\ctc\include\stdio.h"
"F:\ADS\AURIX-Studio-1.10.2\tools\Compilers\Tasking_1.1r8\ctc\include\stdio.h" :
voice_example.o :	"F:\ADS\AURIX-Studio-1.10.2\tools\Compilers\Tasking_1.1r8\ctc\include\stdarg.h"
"F:\ADS\AURIX-Studio-1.10.2\tools\Compilers\Tasking_1.1r8\ctc\include\stdarg.h" :
voice_example.o :	"F:\ADS\AURIX-Studio-1.10.2\tools\Compilers\Tasking_1.1r8\ctc\include\stdlib.h"
"F:\ADS\AURIX-Studio-1.10.2\tools\Compilers\Tasking_1.1r8\ctc\include\stdlib.h" :
voice_example.o :	"F:\ADS\AURIX-Studio-1.10.2\tools\Compilers\Tasking_1.1r8\ctc\include\time.h"
"F:\ADS\AURIX-Studio-1.10.2\tools\Compilers\Tasking_1.1r8\ctc\include\time.h" :
voice_example.o :	..\code\user1\gsm_4g_error.h
..\code\user1\gsm_4g_error.h :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_common\zf_common_headfile.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_common\zf_common_headfile.h" :
voice_example.o :	"F:\ADS\AURIX-Studio-1.10.2\tools\Compilers\Tasking_1.1r8\ctc\include\math.h"
"F:\ADS\AURIX-Studio-1.10.2\tools\Compilers\Tasking_1.1r8\ctc\include\math.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\ifxAsclin_reg.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\ifxAsclin_reg.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxAsclin_regdef.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxAsclin_regdef.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\Ifx_TypesReg.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\Ifx_TypesReg.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Service\\CpuGeneric\SysSe\Bsp\Bsp.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Service\\CpuGeneric\SysSe\Bsp\Bsp.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Configurations\Ifx_Cfg.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Configurations\Ifx_Cfg.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Cpu\Std\IfxCpu_Intrinsics.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Cpu\Std\IfxCpu_Intrinsics.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Cpu\Std\Ifx_Types.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Cpu\Std\Ifx_Types.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Platform\Tricore\Compilers\Compilers.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Platform\Tricore\Compilers\Compilers.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Platform\Tricore\Compilers\CompilerTasking.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Platform\Tricore\Compilers\CompilerTasking.h" :
voice_example.o :	"F:\ADS\AURIX-Studio-1.10.2\tools\Compilers\Tasking_1.1r8\ctc\include\stddef.h"
"F:\ADS\AURIX-Studio-1.10.2\tools\Compilers\Tasking_1.1r8\ctc\include\stddef.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Cpu\Std\Platform_Types.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Cpu\Std\Platform_Types.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Cpu\Std\Ifx_TypesTasking.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Cpu\Std\Ifx_TypesTasking.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Cpu\Std\IfxCpu_IntrinsicsTasking.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Cpu\Std\IfxCpu_IntrinsicsTasking.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Service\\CpuGeneric\_Utilities\Ifx_Assert.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Service\\CpuGeneric\_Utilities\Ifx_Assert.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Cpu\Std\Ifx_Types.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Cpu\Std\Ifx_Types.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Cpu\Std\IfxCpu.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Cpu\Std\IfxCpu.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\_Impl\IfxCpu_cfg.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\_Impl\IfxCpu_cfg.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxCpu_reg.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxCpu_reg.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxCpu_regdef.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxCpu_regdef.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxSrc_reg.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxSrc_reg.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxSrc_regdef.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxSrc_regdef.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxScu_reg.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxScu_reg.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxScu_regdef.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxScu_regdef.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxStm_reg.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxStm_reg.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxStm_regdef.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxStm_regdef.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\_Impl\IfxScu_cfg.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\_Impl\IfxScu_cfg.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxScu_bf.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxScu_bf.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxFlash_bf.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxFlash_bf.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Service\\CpuGeneric\_Utilities\Ifx_Assert.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Service\\CpuGeneric\_Utilities\Ifx_Assert.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Scu\Std\IfxScuWdt.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Scu\Std\IfxScuWdt.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Cpu\Std\Ifx_Types.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Cpu\Std\Ifx_Types.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Scu\Std\IfxScuWdt.asm.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Scu\Std\IfxScuWdt.asm.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Cpu\Std\Ifx_Types.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Cpu\Std\Ifx_Types.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Scu\Std\IfxScuCcu.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Scu\Std\IfxScuCcu.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Scu\Std\IfxScuWdt.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Scu\Std\IfxScuWdt.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Cpu\Std\IfxCpu.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Cpu\Std\IfxCpu.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxFlash_reg.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxFlash_reg.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxFlash_regdef.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxFlash_regdef.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\_PinMap\IfxScu_PinMap.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\_PinMap\IfxScu_PinMap.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Port\Std\IfxPort.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Port\Std\IfxPort.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\_Impl\IfxPort_cfg.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\_Impl\IfxPort_cfg.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxPort_reg.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxPort_reg.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxPort_regdef.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxPort_regdef.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Scu\Std\IfxScuWdt.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Scu\Std\IfxScuWdt.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Port\Std\IfxPort.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Port\Std\IfxPort.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Src\Std\IfxSrc.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Src\Std\IfxSrc.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\_Impl\IfxSrc_cfg.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\_Impl\IfxSrc_cfg.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Stm\Std\IfxStm.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Stm\Std\IfxStm.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\_Impl\IfxStm_cfg.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\_Impl\IfxStm_cfg.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Cpu\Std\Ifx_Types.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Cpu\Std\Ifx_Types.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Scu\Std\IfxScuCcu.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Scu\Std\IfxScuCcu.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Scu\Std\IfxScuCcu.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Scu\Std\IfxScuCcu.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Ccu6\\Timer\IfxCcu6_Timer.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Ccu6\\Timer\IfxCcu6_Timer.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Ccu6\Std\IfxCcu6.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Ccu6\Std\IfxCcu6.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\_Impl\IfxCcu6_cfg.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\_Impl\IfxCcu6_cfg.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Cpu\Std\Ifx_Types.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Cpu\Std\Ifx_Types.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxCcu6_reg.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxCcu6_reg.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxCcu6_regdef.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxCcu6_regdef.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxCcu6_bf.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxCcu6_bf.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Scu\Std\IfxScuWdt.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Scu\Std\IfxScuWdt.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Scu\Std\IfxScuCcu.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Scu\Std\IfxScuCcu.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Service\\CpuGeneric\_Utilities\Ifx_Assert.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Service\\CpuGeneric\_Utilities\Ifx_Assert.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\_PinMap\IfxCcu6_PinMap.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\_PinMap\IfxCcu6_PinMap.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\_Impl\IfxCcu6_cfg.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\_Impl\IfxCcu6_cfg.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Port\Std\IfxPort.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Port\Std\IfxPort.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Service\\CpuGeneric\If\Ccu6If\Timer.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Service\\CpuGeneric\If\Ccu6If\Timer.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Cpu\Std\Ifx_Types.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Cpu\Std\Ifx_Types.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Scu\\Std\IfxScuEru.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Scu\\Std\IfxScuEru.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\_PinMap\IfxScu_PinMap.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\_PinMap\IfxScu_PinMap.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_common\zf_common_typedef.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_common\zf_common_typedef.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Cpu\\Std\ifx_types.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Cpu\\Std\ifx_types.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Cpu\\Std\PLATFORM_TYPES.H"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Cpu\\Std\PLATFORM_TYPES.H" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_common\zf_common_clock.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_common\zf_common_clock.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_common\zf_common_debug.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_common\zf_common_debug.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_common\zf_common_interrupt.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_common\zf_common_interrupt.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_common\zf_common_fifo.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_common\zf_common_fifo.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_common\zf_common_font.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_common\zf_common_font.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_common\zf_common_function.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_common\zf_common_function.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\user\isr_config.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\user\isr_config.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_driver\zf_driver_adc.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_driver\zf_driver_adc.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_driver\zf_driver_delay.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_driver\zf_driver_delay.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_driver\zf_driver_dma.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_driver\zf_driver_dma.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Dma\\Std\IfxDma.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Dma\\Std\IfxDma.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\_Impl\IfxDma_cfg.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\_Impl\IfxDma_cfg.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Cpu\Std\Ifx_Types.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Cpu\Std\Ifx_Types.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxDma_bf.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxDma_bf.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxDma_reg.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxDma_reg.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxDma_regdef.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxDma_regdef.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Scu\Std\IfxScuWdt.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Scu\Std\IfxScuWdt.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Scu\Std\IfxScuCcu.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Scu\Std\IfxScuCcu.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_driver\zf_driver_exti.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_driver\zf_driver_exti.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Scu\\Std\IfxScuEru.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Scu\\Std\IfxScuEru.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_driver\zf_driver_encoder.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_driver\zf_driver_encoder.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_driver\zf_driver_exti.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_driver\zf_driver_exti.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_driver\zf_driver_flash.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_driver\zf_driver_flash.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\_Impl\ifxFlash_cfg.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\_Impl\ifxFlash_cfg.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_driver\zf_driver_gpio.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_driver\zf_driver_gpio.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Port\\Std\IFXPORT.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Port\\Std\IFXPORT.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_driver\zf_driver_pit.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_driver\zf_driver_pit.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_driver\zf_driver_pwm.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_driver\zf_driver_pwm.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_driver\zf_driver_soft_iic.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_driver\zf_driver_soft_iic.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_driver\zf_driver_spi.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_driver\zf_driver_spi.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_driver\zf_driver_soft_spi.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_driver\zf_driver_soft_spi.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_driver\zf_driver_uart.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_driver\zf_driver_uart.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Asclin\\Asc\ifxAsclin_Asc.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Asclin\\Asc\ifxAsclin_Asc.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Asclin\Std\IfxAsclin.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Asclin\Std\IfxAsclin.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\_Impl\IfxAsclin_cfg.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\_Impl\IfxAsclin_cfg.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Cpu\Std\Ifx_Types.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Cpu\Std\Ifx_Types.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxAsclin_reg.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxAsclin_reg.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Scu\Std\IfxScuCcu.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Scu\Std\IfxScuCcu.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Scu\Std\IfxScuWdt.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Scu\Std\IfxScuWdt.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\_PinMap\IfxAsclin_PinMap.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\_PinMap\IfxAsclin_PinMap.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\_Impl\IfxAsclin_cfg.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\_Impl\IfxAsclin_cfg.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Port\Std\IfxPort.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Port\Std\IfxPort.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\_Lib\DataHandling\Ifx_Fifo.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\_Lib\DataHandling\Ifx_Fifo.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Service\\CpuGeneric\SysSe\Bsp\Bsp.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Service\\CpuGeneric\SysSe\Bsp\Bsp.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Service\\CpuGeneric\StdIf\IfxStdIf_DPipe.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Service\\CpuGeneric\StdIf\IfxStdIf_DPipe.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Service\\CpuGeneric\StdIf\IfxStdIf.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Service\\CpuGeneric\StdIf\IfxStdIf.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Cpu\Std\Ifx_Types.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Cpu\Std\Ifx_Types.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_driver\zf_driver_timer.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_driver\zf_driver_timer.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_absolute_encoder.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_absolute_encoder.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_ble6a20.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_ble6a20.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_bluetooth_ch9141.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_bluetooth_ch9141.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_gnss.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_gnss.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_camera.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_camera.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_driver\zf_driver_uart.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_driver\zf_driver_uart.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_type.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_type.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_dl1a.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_dl1a.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_dl1b.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_dl1b.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_icm20602.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_icm20602.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_imu660ra.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_imu660ra.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_imu963ra.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_imu963ra.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_ips114.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_ips114.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_ips200.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_ips200.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_key.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_key.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_mpu6050.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_mpu6050.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_mt9v03x.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_mt9v03x.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_oled.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_oled.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_ov7725.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_ov7725.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_scc8660.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_scc8660.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_tft180.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_tft180.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_tsl1401.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_tsl1401.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_uart_receiver.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_uart_receiver.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_virtual_oscilloscope.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_virtual_oscilloscope.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_wifi_uart.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_wifi_uart.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_wifi_spi.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_wifi_spi.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_wireless_uart.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_wireless_uart.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_components\seekfree_assistant.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_components\seekfree_assistant.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_components\seekfree_assistant_interface.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_components\seekfree_assistant_interface.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\code\camera.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\code\camera.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\code\Device.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\code\Device.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_common\zf_common_headfile.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_common\zf_common_headfile.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\code\encoder.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\code\encoder.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\code\Flash.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\code\Flash.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\code\gps.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\code\gps.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\code\imu.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\code\imu.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\code\PID.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\code\PID.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\code\quaternion.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\code\quaternion.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\code\remote.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\code\remote.h" :
voice_example.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\code\user_display_gps.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\code\user_display_gps.h" :
voice_example.o :	..\code\user1\voice_example.h
..\code\user1\voice_example.h :
voice_example.o :	..\code\user1\voice_config.h
..\code\user1\voice_config.h :
