	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc27372a --dep-file=IfxMsc_cfg.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2\\\\xf_asr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxMsc_cfg.src ../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxMsc_cfg.c"
	.compiler_name		"ctc"
	;source	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxMsc_cfg.c'

	
$TC16X
	
	.sdecl	'.rodata.IfxMsc_cfg.IfxMsc_cfg_indexMap',data,rom,cluster('IfxMsc_cfg_indexMap')
	.sect	'.rodata.IfxMsc_cfg.IfxMsc_cfg_indexMap'
	.global	IfxMsc_cfg_indexMap
	.align	4
IfxMsc_cfg_indexMap:	.type	object
	.size	IfxMsc_cfg_indexMap,16
	.word	-268425728
	.space	4
	.word	-268425472,1
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L3:
	.word	9585
	.half	3
	.word	.L4
	.byte	4
.L2:
	.byte	1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxMsc_cfg.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L5
	.byte	2
	.byte	'short int',0,2,5,3
	.byte	'__wchar_t',0,1,1,1
	.word	232
	.byte	2
	.byte	'unsigned int',0,4,7,3
	.byte	'__size_t',0,1,1,1
	.word	263
	.byte	2
	.byte	'int',0,4,5,3
	.byte	'__ptrdiff_t',0,1,1,1
	.word	296
	.byte	4,1,5
	.word	323
	.byte	3
	.byte	'__codeptr',0,1,1,1
	.word	325
	.byte	2
	.byte	'unsigned char',0,1,8,3
	.byte	'uint8',0,2,105,29
	.word	348
	.byte	2
	.byte	'unsigned short int',0,2,7,3
	.byte	'uint16',0,2,109,29
	.word	379
	.byte	2
	.byte	'unsigned long int',0,4,7,3
	.byte	'uint32',0,2,113,29
	.word	416
	.byte	3
	.byte	'sint16',0,2,126,29
	.word	232
	.byte	2
	.byte	'long int',0,4,5,3
	.byte	'sint32',0,2,131,1,29
	.word	467
	.byte	2
	.byte	'long long int',0,8,5,3
	.byte	'sint64',0,2,138,1,29
	.word	495
	.byte	2
	.byte	'float',0,4,4,3
	.byte	'float32',0,2,167,1,29
	.word	528
	.byte	6
	.byte	'void',0,5
	.word	554
	.byte	3
	.byte	'pvoid',0,3,57,28
	.word	560
	.byte	3
	.byte	'Ifx_TickTime',0,3,79,28
	.word	495
	.byte	7
	.word	554
	.byte	5
	.word	600
	.byte	8,3,143,1,9,8,9
	.byte	'module',0
	.word	605
	.byte	4,2,35,0,9
	.byte	'index',0
	.word	467
	.byte	4,2,35,4,0,3
	.byte	'IfxModule_IndexMap',0,3,147,1,3
	.word	610
	.byte	10
	.byte	'_Ifx_MSC_ABC_Bits',0,4,45,16,4,11
	.byte	'LOW',0,1
	.word	348
	.byte	4,4,2,35,0,11
	.byte	'HIGH',0,1
	.word	348
	.byte	4,0,2,35,0,11
	.byte	'OIP',0,1
	.word	348
	.byte	2,6,2,35,1,11
	.byte	'OASR',0,1
	.word	348
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	348
	.byte	1,4,2,35,1,11
	.byte	'OVF',0,1
	.word	348
	.byte	1,3,2,35,1,11
	.byte	'OFM',0,1
	.word	348
	.byte	2,1,2,35,1,11
	.byte	'OIE',0,1
	.word	348
	.byte	1,0,2,35,1,11
	.byte	'NDA',0,1
	.word	348
	.byte	3,5,2,35,2,11
	.byte	'UIP',0,1
	.word	348
	.byte	2,3,2,35,2,11
	.byte	'UASR',0,1
	.word	348
	.byte	1,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	348
	.byte	1,1,2,35,2,11
	.byte	'UNF',0,1
	.word	348
	.byte	1,0,2,35,2,11
	.byte	'UFM',0,1
	.word	348
	.byte	2,6,2,35,3,11
	.byte	'UIE',0,1
	.word	348
	.byte	1,5,2,35,3,11
	.byte	'CLKSEL',0,1
	.word	348
	.byte	3,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	348
	.byte	1,1,2,35,3,11
	.byte	'ABB',0,1
	.word	348
	.byte	1,0,2,35,3,0,3
	.byte	'Ifx_MSC_ABC_Bits',0,4,65,3
	.word	676
	.byte	10
	.byte	'_Ifx_MSC_ACCEN0_Bits',0,4,68,16,4,11
	.byte	'EN0',0,1
	.word	348
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	348
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	348
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	348
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	348
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	348
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	348
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	348
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	348
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	348
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	348
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	348
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	348
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	348
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	348
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	348
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	348
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	348
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	348
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	348
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	348
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	348
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	348
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	348
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	348
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	348
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	348
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	348
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	348
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	348
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	348
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	348
	.byte	1,0,2,35,3,0,3
	.byte	'Ifx_MSC_ACCEN0_Bits',0,4,102,3
	.word	1025
	.byte	10
	.byte	'_Ifx_MSC_ACCEN1_Bits',0,4,105,16,4,11
	.byte	'reserved_0',0,4
	.word	263
	.byte	32,0,2,35,0,0,3
	.byte	'Ifx_MSC_ACCEN1_Bits',0,4,108,3
	.word	1582
	.byte	10
	.byte	'_Ifx_MSC_CLC_Bits',0,4,111,16,4,11
	.byte	'DISR',0,1
	.word	348
	.byte	1,7,2,35,0,11
	.byte	'DISS',0,1
	.word	348
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	348
	.byte	1,5,2,35,0,11
	.byte	'EDIS',0,1
	.word	348
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	263
	.byte	28,0,2,35,0,0,3
	.byte	'Ifx_MSC_CLC_Bits',0,4,118,3
	.word	1659
	.byte	10
	.byte	'_Ifx_MSC_DC_Bits',0,4,121,16,4,11
	.byte	'DCL',0,2
	.word	379
	.byte	16,0,2,35,0,11
	.byte	'DCH',0,2
	.word	379
	.byte	16,0,2,35,2,0,3
	.byte	'Ifx_MSC_DC_Bits',0,4,125,3
	.word	1800
	.byte	10
	.byte	'_Ifx_MSC_DD_Bits',0,4,128,1,16,4,11
	.byte	'DDL',0,2
	.word	379
	.byte	16,0,2,35,0,11
	.byte	'DDH',0,2
	.word	379
	.byte	16,0,2,35,2,0,3
	.byte	'Ifx_MSC_DD_Bits',0,4,132,1,3
	.word	1877
	.byte	10
	.byte	'_Ifx_MSC_DDE_Bits',0,4,135,1,16,4,11
	.byte	'DDLE',0,2
	.word	379
	.byte	16,0,2,35,0,11
	.byte	'DDHE',0,2
	.word	379
	.byte	16,0,2,35,2,0,3
	.byte	'Ifx_MSC_DDE_Bits',0,4,139,1,3
	.word	1956
	.byte	10
	.byte	'_Ifx_MSC_DDM_Bits',0,4,142,1,16,4,11
	.byte	'DDLM',0,2
	.word	379
	.byte	16,0,2,35,0,11
	.byte	'DDHM',0,2
	.word	379
	.byte	16,0,2,35,2,0,3
	.byte	'Ifx_MSC_DDM_Bits',0,4,146,1,3
	.word	2039
	.byte	10
	.byte	'_Ifx_MSC_DSC_Bits',0,4,149,1,16,4,11
	.byte	'TM',0,1
	.word	348
	.byte	1,7,2,35,0,11
	.byte	'CP',0,1
	.word	348
	.byte	1,6,2,35,0,11
	.byte	'DP',0,1
	.word	348
	.byte	1,5,2,35,0,11
	.byte	'NDBL',0,1
	.word	348
	.byte	5,0,2,35,0,11
	.byte	'NDBH',0,1
	.word	348
	.byte	5,3,2,35,1,11
	.byte	'ENSELL',0,1
	.word	348
	.byte	1,2,2,35,1,11
	.byte	'ENSELH',0,1
	.word	348
	.byte	1,1,2,35,1,11
	.byte	'DSDIS',0,1
	.word	348
	.byte	1,0,2,35,1,11
	.byte	'NBC',0,1
	.word	348
	.byte	6,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	348
	.byte	2,0,2,35,2,11
	.byte	'PPD',0,1
	.word	348
	.byte	5,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	348
	.byte	3,0,2,35,3,0,3
	.byte	'Ifx_MSC_DSC_Bits',0,4,163,1,3
	.word	2122
	.byte	10
	.byte	'_Ifx_MSC_DSCE_Bits',0,4,166,1,16,4,11
	.byte	'NDBHE',0,1
	.word	348
	.byte	1,7,2,35,0,11
	.byte	'NDBLE',0,1
	.word	348
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	379
	.byte	12,2,2,35,0,11
	.byte	'EXEN',0,1
	.word	348
	.byte	1,1,2,35,1,11
	.byte	'CCF',0,1
	.word	348
	.byte	1,0,2,35,1,11
	.byte	'INJENP0',0,1
	.word	348
	.byte	1,7,2,35,2,11
	.byte	'INJPOSP0',0,1
	.word	348
	.byte	6,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	348
	.byte	1,0,2,35,2,11
	.byte	'INJENP1',0,1
	.word	348
	.byte	1,7,2,35,3,11
	.byte	'INJPOSP1',0,1
	.word	348
	.byte	6,1,2,35,3,11
	.byte	'CDCM',0,1
	.word	348
	.byte	1,0,2,35,3,0,3
	.byte	'Ifx_MSC_DSCE_Bits',0,4,179,1,3
	.word	2376
	.byte	10
	.byte	'_Ifx_MSC_DSDSH_Bits',0,4,182,1,16,4,11
	.byte	'SH0',0,1
	.word	348
	.byte	2,6,2,35,0,11
	.byte	'SH1',0,1
	.word	348
	.byte	2,4,2,35,0,11
	.byte	'SH2',0,1
	.word	348
	.byte	2,2,2,35,0,11
	.byte	'SH3',0,1
	.word	348
	.byte	2,0,2,35,0,11
	.byte	'SH4',0,1
	.word	348
	.byte	2,6,2,35,1,11
	.byte	'SH5',0,1
	.word	348
	.byte	2,4,2,35,1,11
	.byte	'SH6',0,1
	.word	348
	.byte	2,2,2,35,1,11
	.byte	'SH7',0,1
	.word	348
	.byte	2,0,2,35,1,11
	.byte	'SH8',0,1
	.word	348
	.byte	2,6,2,35,2,11
	.byte	'SH9',0,1
	.word	348
	.byte	2,4,2,35,2,11
	.byte	'SH10',0,1
	.word	348
	.byte	2,2,2,35,2,11
	.byte	'SH11',0,1
	.word	348
	.byte	2,0,2,35,2,11
	.byte	'SH12',0,1
	.word	348
	.byte	2,6,2,35,3,11
	.byte	'SH13',0,1
	.word	348
	.byte	2,4,2,35,3,11
	.byte	'SH14',0,1
	.word	348
	.byte	2,2,2,35,3,11
	.byte	'SH15',0,1
	.word	348
	.byte	2,0,2,35,3,0,3
	.byte	'Ifx_MSC_DSDSH_Bits',0,4,200,1,3
	.word	2633
	.byte	10
	.byte	'_Ifx_MSC_DSDSHE_Bits',0,4,203,1,16,4,11
	.byte	'SH16',0,1
	.word	348
	.byte	2,6,2,35,0,11
	.byte	'SH17',0,1
	.word	348
	.byte	2,4,2,35,0,11
	.byte	'SH18',0,1
	.word	348
	.byte	2,2,2,35,0,11
	.byte	'SH19',0,1
	.word	348
	.byte	2,0,2,35,0,11
	.byte	'SH20',0,1
	.word	348
	.byte	2,6,2,35,1,11
	.byte	'SH21',0,1
	.word	348
	.byte	2,4,2,35,1,11
	.byte	'SH22',0,1
	.word	348
	.byte	2,2,2,35,1,11
	.byte	'SH23',0,1
	.word	348
	.byte	2,0,2,35,1,11
	.byte	'SH24',0,1
	.word	348
	.byte	2,6,2,35,2,11
	.byte	'SH25',0,1
	.word	348
	.byte	2,4,2,35,2,11
	.byte	'SH26',0,1
	.word	348
	.byte	2,2,2,35,2,11
	.byte	'SH27',0,1
	.word	348
	.byte	2,0,2,35,2,11
	.byte	'SH28',0,1
	.word	348
	.byte	2,6,2,35,3,11
	.byte	'SH29',0,1
	.word	348
	.byte	2,4,2,35,3,11
	.byte	'SH30',0,1
	.word	348
	.byte	2,2,2,35,3,11
	.byte	'SH31',0,1
	.word	348
	.byte	2,0,2,35,3,0,3
	.byte	'Ifx_MSC_DSDSHE_Bits',0,4,221,1,3
	.word	2934
	.byte	10
	.byte	'_Ifx_MSC_DSDSL_Bits',0,4,224,1,16,4,11
	.byte	'SL0',0,1
	.word	348
	.byte	2,6,2,35,0,11
	.byte	'SL1',0,1
	.word	348
	.byte	2,4,2,35,0,11
	.byte	'SL2',0,1
	.word	348
	.byte	2,2,2,35,0,11
	.byte	'SL3',0,1
	.word	348
	.byte	2,0,2,35,0,11
	.byte	'SL4',0,1
	.word	348
	.byte	2,6,2,35,1,11
	.byte	'SL5',0,1
	.word	348
	.byte	2,4,2,35,1,11
	.byte	'SL6',0,1
	.word	348
	.byte	2,2,2,35,1,11
	.byte	'SL7',0,1
	.word	348
	.byte	2,0,2,35,1,11
	.byte	'SL8',0,1
	.word	348
	.byte	2,6,2,35,2,11
	.byte	'SL9',0,1
	.word	348
	.byte	2,4,2,35,2,11
	.byte	'SL10',0,1
	.word	348
	.byte	2,2,2,35,2,11
	.byte	'SL11',0,1
	.word	348
	.byte	2,0,2,35,2,11
	.byte	'SL12',0,1
	.word	348
	.byte	2,6,2,35,3,11
	.byte	'SL13',0,1
	.word	348
	.byte	2,4,2,35,3,11
	.byte	'SL14',0,1
	.word	348
	.byte	2,2,2,35,3,11
	.byte	'SL15',0,1
	.word	348
	.byte	2,0,2,35,3,0,3
	.byte	'Ifx_MSC_DSDSL_Bits',0,4,242,1,3
	.word	3247
	.byte	10
	.byte	'_Ifx_MSC_DSDSLE_Bits',0,4,245,1,16,4,11
	.byte	'SL16',0,1
	.word	348
	.byte	2,6,2,35,0,11
	.byte	'SL17',0,1
	.word	348
	.byte	2,4,2,35,0,11
	.byte	'SL18',0,1
	.word	348
	.byte	2,2,2,35,0,11
	.byte	'SL19',0,1
	.word	348
	.byte	2,0,2,35,0,11
	.byte	'SL20',0,1
	.word	348
	.byte	2,6,2,35,1,11
	.byte	'SL21',0,1
	.word	348
	.byte	2,4,2,35,1,11
	.byte	'SL22',0,1
	.word	348
	.byte	2,2,2,35,1,11
	.byte	'SL23',0,1
	.word	348
	.byte	2,0,2,35,1,11
	.byte	'SL24',0,1
	.word	348
	.byte	2,6,2,35,2,11
	.byte	'SL25',0,1
	.word	348
	.byte	2,4,2,35,2,11
	.byte	'SL26',0,1
	.word	348
	.byte	2,2,2,35,2,11
	.byte	'SL27',0,1
	.word	348
	.byte	2,0,2,35,2,11
	.byte	'SL28',0,1
	.word	348
	.byte	2,6,2,35,3,11
	.byte	'SL29',0,1
	.word	348
	.byte	2,4,2,35,3,11
	.byte	'SL30',0,1
	.word	348
	.byte	2,2,2,35,3,11
	.byte	'SL31',0,1
	.word	348
	.byte	2,0,2,35,3,0,3
	.byte	'Ifx_MSC_DSDSLE_Bits',0,4,135,2,3
	.word	3548
	.byte	10
	.byte	'_Ifx_MSC_DSS_Bits',0,4,138,2,16,4,11
	.byte	'PFC',0,1
	.word	348
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	348
	.byte	4,0,2,35,0,11
	.byte	'NPTF',0,1
	.word	348
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	348
	.byte	4,0,2,35,1,11
	.byte	'DC',0,1
	.word	348
	.byte	8,0,2,35,2,11
	.byte	'DFA',0,1
	.word	348
	.byte	1,7,2,35,3,11
	.byte	'CFA',0,1
	.word	348
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	348
	.byte	6,0,2,35,3,0,3
	.byte	'Ifx_MSC_DSS_Bits',0,4,148,2,3
	.word	3861
	.byte	10
	.byte	'_Ifx_MSC_DSTE_Bits',0,4,151,2,16,4,11
	.byte	'PPDE',0,1
	.word	348
	.byte	2,6,2,35,0,11
	.byte	'PPCE',0,1
	.word	348
	.byte	6,0,2,35,0,11
	.byte	'NDD',0,1
	.word	348
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	263
	.byte	20,0,2,35,0,0,3
	.byte	'Ifx_MSC_DSTE_Bits',0,4,157,2,3
	.word	4055
	.byte	10
	.byte	'_Ifx_MSC_ESR_Bits',0,4,160,2,16,4,11
	.byte	'ENL0',0,1
	.word	348
	.byte	1,7,2,35,0,11
	.byte	'ENL1',0,1
	.word	348
	.byte	1,6,2,35,0,11
	.byte	'ENL2',0,1
	.word	348
	.byte	1,5,2,35,0,11
	.byte	'ENL3',0,1
	.word	348
	.byte	1,4,2,35,0,11
	.byte	'ENL4',0,1
	.word	348
	.byte	1,3,2,35,0,11
	.byte	'ENL5',0,1
	.word	348
	.byte	1,2,2,35,0,11
	.byte	'ENL6',0,1
	.word	348
	.byte	1,1,2,35,0,11
	.byte	'ENL7',0,1
	.word	348
	.byte	1,0,2,35,0,11
	.byte	'ENL8',0,1
	.word	348
	.byte	1,7,2,35,1,11
	.byte	'ENL9',0,1
	.word	348
	.byte	1,6,2,35,1,11
	.byte	'ENL10',0,1
	.word	348
	.byte	1,5,2,35,1,11
	.byte	'ENL11',0,1
	.word	348
	.byte	1,4,2,35,1,11
	.byte	'ENL12',0,1
	.word	348
	.byte	1,3,2,35,1,11
	.byte	'ENL13',0,1
	.word	348
	.byte	1,2,2,35,1,11
	.byte	'ENL14',0,1
	.word	348
	.byte	1,1,2,35,1,11
	.byte	'ENL15',0,1
	.word	348
	.byte	1,0,2,35,1,11
	.byte	'ENH0',0,1
	.word	348
	.byte	1,7,2,35,2,11
	.byte	'ENH1',0,1
	.word	348
	.byte	1,6,2,35,2,11
	.byte	'ENH2',0,1
	.word	348
	.byte	1,5,2,35,2,11
	.byte	'ENH3',0,1
	.word	348
	.byte	1,4,2,35,2,11
	.byte	'ENH4',0,1
	.word	348
	.byte	1,3,2,35,2,11
	.byte	'ENH5',0,1
	.word	348
	.byte	1,2,2,35,2,11
	.byte	'ENH6',0,1
	.word	348
	.byte	1,1,2,35,2,11
	.byte	'ENH7',0,1
	.word	348
	.byte	1,0,2,35,2,11
	.byte	'ENH8',0,1
	.word	348
	.byte	1,7,2,35,3,11
	.byte	'ENH9',0,1
	.word	348
	.byte	1,6,2,35,3,11
	.byte	'ENH10',0,1
	.word	348
	.byte	1,5,2,35,3,11
	.byte	'ENH11',0,1
	.word	348
	.byte	1,4,2,35,3,11
	.byte	'ENH12',0,1
	.word	348
	.byte	1,3,2,35,3,11
	.byte	'ENH13',0,1
	.word	348
	.byte	1,2,2,35,3,11
	.byte	'ENH14',0,1
	.word	348
	.byte	1,1,2,35,3,11
	.byte	'ENH15',0,1
	.word	348
	.byte	1,0,2,35,3,0,3
	.byte	'Ifx_MSC_ESR_Bits',0,4,194,2,3
	.word	4178
	.byte	10
	.byte	'_Ifx_MSC_ESRE_Bits',0,4,197,2,16,4,11
	.byte	'ENL16',0,1
	.word	348
	.byte	1,7,2,35,0,11
	.byte	'ENL17',0,1
	.word	348
	.byte	1,6,2,35,0,11
	.byte	'ENL18',0,1
	.word	348
	.byte	1,5,2,35,0,11
	.byte	'ENL19',0,1
	.word	348
	.byte	1,4,2,35,0,11
	.byte	'ENL20',0,1
	.word	348
	.byte	1,3,2,35,0,11
	.byte	'ENL21',0,1
	.word	348
	.byte	1,2,2,35,0,11
	.byte	'ENL22',0,1
	.word	348
	.byte	1,1,2,35,0,11
	.byte	'ENL23',0,1
	.word	348
	.byte	1,0,2,35,0,11
	.byte	'ENL24',0,1
	.word	348
	.byte	1,7,2,35,1,11
	.byte	'ENL25',0,1
	.word	348
	.byte	1,6,2,35,1,11
	.byte	'ENL26',0,1
	.word	348
	.byte	1,5,2,35,1,11
	.byte	'ENL27',0,1
	.word	348
	.byte	1,4,2,35,1,11
	.byte	'ENL28',0,1
	.word	348
	.byte	1,3,2,35,1,11
	.byte	'ENL29',0,1
	.word	348
	.byte	1,2,2,35,1,11
	.byte	'ENL30',0,1
	.word	348
	.byte	1,1,2,35,1,11
	.byte	'ENL31',0,1
	.word	348
	.byte	1,0,2,35,1,11
	.byte	'ENH16',0,1
	.word	348
	.byte	1,7,2,35,2,11
	.byte	'ENH17',0,1
	.word	348
	.byte	1,6,2,35,2,11
	.byte	'ENH18',0,1
	.word	348
	.byte	1,5,2,35,2,11
	.byte	'ENH19',0,1
	.word	348
	.byte	1,4,2,35,2,11
	.byte	'ENH20',0,1
	.word	348
	.byte	1,3,2,35,2,11
	.byte	'ENH21',0,1
	.word	348
	.byte	1,2,2,35,2,11
	.byte	'ENH22',0,1
	.word	348
	.byte	1,1,2,35,2,11
	.byte	'ENH23',0,1
	.word	348
	.byte	1,0,2,35,2,11
	.byte	'ENH24',0,1
	.word	348
	.byte	1,7,2,35,3,11
	.byte	'ENH25',0,1
	.word	348
	.byte	1,6,2,35,3,11
	.byte	'ENH26',0,1
	.word	348
	.byte	1,5,2,35,3,11
	.byte	'ENH27',0,1
	.word	348
	.byte	1,4,2,35,3,11
	.byte	'ENH28',0,1
	.word	348
	.byte	1,3,2,35,3,11
	.byte	'ENH29',0,1
	.word	348
	.byte	1,2,2,35,3,11
	.byte	'ENH30',0,1
	.word	348
	.byte	1,1,2,35,3,11
	.byte	'ENH31',0,1
	.word	348
	.byte	1,0,2,35,3,0,3
	.byte	'Ifx_MSC_ESRE_Bits',0,4,231,2,3
	.word	4753
	.byte	10
	.byte	'_Ifx_MSC_FDR_Bits',0,4,234,2,16,4,11
	.byte	'STEP',0,2
	.word	379
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	348
	.byte	4,2,2,35,1,11
	.byte	'DM',0,1
	.word	348
	.byte	2,0,2,35,1,11
	.byte	'RESULT',0,2
	.word	379
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	348
	.byte	4,2,2,35,3,11
	.byte	'ENHW',0,1
	.word	348
	.byte	1,1,2,35,3,11
	.byte	'DISCLK',0,1
	.word	348
	.byte	1,0,2,35,3,0,3
	.byte	'Ifx_MSC_FDR_Bits',0,4,243,2,3
	.word	5350
	.byte	10
	.byte	'_Ifx_MSC_ICR_Bits',0,4,246,2,16,4,11
	.byte	'EDIP',0,1
	.word	348
	.byte	2,6,2,35,0,11
	.byte	'EDIE',0,1
	.word	348
	.byte	2,4,2,35,0,11
	.byte	'ECIP',0,1
	.word	348
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	348
	.byte	1,1,2,35,0,11
	.byte	'ECIE',0,1
	.word	348
	.byte	1,0,2,35,0,11
	.byte	'TFIP',0,1
	.word	348
	.byte	2,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	348
	.byte	1,5,2,35,1,11
	.byte	'TFIE',0,1
	.word	348
	.byte	1,4,2,35,1,11
	.byte	'RDIP',0,1
	.word	348
	.byte	2,2,2,35,1,11
	.byte	'RDIE',0,1
	.word	348
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	379
	.byte	16,0,2,35,2,0,3
	.byte	'Ifx_MSC_ICR_Bits',0,4,131,3,3
	.word	5529
	.byte	10
	.byte	'_Ifx_MSC_ID_Bits',0,4,134,3,16,4,11
	.byte	'MODREV',0,1
	.word	348
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	348
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	379
	.byte	16,0,2,35,2,0,3
	.byte	'Ifx_MSC_ID_Bits',0,4,139,3,3
	.word	5776
	.byte	10
	.byte	'_Ifx_MSC_ISC_Bits',0,4,142,3,16,4,11
	.byte	'CDEDI',0,1
	.word	348
	.byte	1,7,2,35,0,11
	.byte	'CDECI',0,1
	.word	348
	.byte	1,6,2,35,0,11
	.byte	'CDTFI',0,1
	.word	348
	.byte	1,5,2,35,0,11
	.byte	'CURDI',0,1
	.word	348
	.byte	1,4,2,35,0,11
	.byte	'CDP',0,1
	.word	348
	.byte	1,3,2,35,0,11
	.byte	'CCP',0,1
	.word	348
	.byte	1,2,2,35,0,11
	.byte	'CDDIS',0,1
	.word	348
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	379
	.byte	9,0,2,35,0,11
	.byte	'SDEDI',0,1
	.word	348
	.byte	1,7,2,35,2,11
	.byte	'SDECI',0,1
	.word	348
	.byte	1,6,2,35,2,11
	.byte	'SDTFI',0,1
	.word	348
	.byte	1,5,2,35,2,11
	.byte	'SURDI',0,1
	.word	348
	.byte	1,4,2,35,2,11
	.byte	'SDP',0,1
	.word	348
	.byte	1,3,2,35,2,11
	.byte	'SCP',0,1
	.word	348
	.byte	1,2,2,35,2,11
	.byte	'SDDIS',0,1
	.word	348
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	379
	.byte	9,0,2,35,2,0,3
	.byte	'Ifx_MSC_ISC_Bits',0,4,160,3,3
	.word	5883
	.byte	10
	.byte	'_Ifx_MSC_ISR_Bits',0,4,163,3,16,4,11
	.byte	'DEDI',0,1
	.word	348
	.byte	1,7,2,35,0,11
	.byte	'DECI',0,1
	.word	348
	.byte	1,6,2,35,0,11
	.byte	'DTFI',0,1
	.word	348
	.byte	1,5,2,35,0,11
	.byte	'URDI',0,1
	.word	348
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	263
	.byte	28,0,2,35,0,0,3
	.byte	'Ifx_MSC_ISR_Bits',0,4,170,3,3
	.word	6209
	.byte	10
	.byte	'_Ifx_MSC_KRST0_Bits',0,4,173,3,16,4,11
	.byte	'RST',0,1
	.word	348
	.byte	1,7,2,35,0,11
	.byte	'RSTSTAT',0,1
	.word	348
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	263
	.byte	30,0,2,35,0,0,3
	.byte	'Ifx_MSC_KRST0_Bits',0,4,178,3,3
	.word	6346
	.byte	10
	.byte	'_Ifx_MSC_KRST1_Bits',0,4,181,3,16,4,11
	.byte	'RST',0,1
	.word	348
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	263
	.byte	31,0,2,35,0,0,3
	.byte	'Ifx_MSC_KRST1_Bits',0,4,185,3,3
	.word	6457
	.byte	10
	.byte	'_Ifx_MSC_KRSTCLR_Bits',0,4,188,3,16,4,11
	.byte	'CLR',0,1
	.word	348
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	263
	.byte	31,0,2,35,0,0,3
	.byte	'Ifx_MSC_KRSTCLR_Bits',0,4,192,3,3
	.word	6549
	.byte	10
	.byte	'_Ifx_MSC_OCR_Bits',0,4,195,3,16,4,11
	.byte	'CLP',0,1
	.word	348
	.byte	1,7,2,35,0,11
	.byte	'SLP',0,1
	.word	348
	.byte	1,6,2,35,0,11
	.byte	'CSLP',0,1
	.word	348
	.byte	1,5,2,35,0,11
	.byte	'ILP',0,1
	.word	348
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	348
	.byte	4,0,2,35,0,11
	.byte	'CLKCTRL',0,1
	.word	348
	.byte	1,7,2,35,1,11
	.byte	'CSL',0,1
	.word	348
	.byte	2,5,2,35,1,11
	.byte	'CSH',0,1
	.word	348
	.byte	2,3,2,35,1,11
	.byte	'CSC',0,1
	.word	348
	.byte	2,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	348
	.byte	1,0,2,35,1,11
	.byte	'SDISEL',0,1
	.word	348
	.byte	3,5,2,35,2,11
	.byte	'reserved_19',0,2
	.word	379
	.byte	13,0,2,35,2,0,3
	.byte	'Ifx_MSC_OCR_Bits',0,4,209,3,3
	.word	6645
	.byte	10
	.byte	'_Ifx_MSC_OCS_Bits',0,4,212,3,16,4,11
	.byte	'reserved_0',0,4
	.word	263
	.byte	24,8,2,35,0,11
	.byte	'SUS',0,1
	.word	348
	.byte	4,4,2,35,3,11
	.byte	'SUS_P',0,1
	.word	348
	.byte	1,3,2,35,3,11
	.byte	'SUSSTA',0,1
	.word	348
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	348
	.byte	2,0,2,35,3,0,3
	.byte	'Ifx_MSC_OCS_Bits',0,4,219,3,3
	.word	6907
	.byte	10
	.byte	'_Ifx_MSC_UD_Bits',0,4,222,3,16,4,11
	.byte	'DATA',0,1
	.word	348
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	348
	.byte	8,0,2,35,1,11
	.byte	'V',0,1
	.word	348
	.byte	1,7,2,35,2,11
	.byte	'P',0,1
	.word	348
	.byte	1,6,2,35,2,11
	.byte	'C',0,1
	.word	348
	.byte	1,5,2,35,2,11
	.byte	'LABF',0,1
	.word	348
	.byte	2,3,2,35,2,11
	.byte	'IPF',0,1
	.word	348
	.byte	1,2,2,35,2,11
	.byte	'PERR',0,1
	.word	348
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	379
	.byte	9,0,2,35,2,0,3
	.byte	'Ifx_MSC_UD_Bits',0,4,233,3,3
	.word	7053
	.byte	10
	.byte	'_Ifx_MSC_USCE_Bits',0,4,236,3,16,4,11
	.byte	'USTOPRE',0,1
	.word	348
	.byte	4,4,2,35,0,11
	.byte	'USTOVAL',0,1
	.word	348
	.byte	4,0,2,35,0,11
	.byte	'USTOEN',0,1
	.word	348
	.byte	1,7,2,35,1,11
	.byte	'USTF',0,1
	.word	348
	.byte	1,6,2,35,1,11
	.byte	'USTC',0,1
	.word	348
	.byte	1,5,2,35,1,11
	.byte	'USTS',0,1
	.word	348
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	348
	.byte	1,3,2,35,1,11
	.byte	'UTASR',0,1
	.word	348
	.byte	1,2,2,35,1,11
	.byte	'USTOIP',0,1
	.word	348
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	379
	.byte	16,0,2,35,2,0,3
	.byte	'Ifx_MSC_USCE_Bits',0,4,248,3,3
	.word	7249
	.byte	10
	.byte	'_Ifx_MSC_USR_Bits',0,4,251,3,16,4,11
	.byte	'UFT',0,1
	.word	348
	.byte	1,7,2,35,0,11
	.byte	'URR',0,1
	.word	348
	.byte	3,4,2,35,0,11
	.byte	'PCTR',0,1
	.word	348
	.byte	1,3,2,35,0,11
	.byte	'SRDC',0,1
	.word	348
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,2
	.word	379
	.byte	10,0,2,35,0,11
	.byte	'UC',0,1
	.word	348
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,2
	.word	379
	.byte	11,0,2,35,2,0,3
	.byte	'Ifx_MSC_USR_Bits',0,4,132,4,3
	.word	7487
	.byte	12,4,140,4,9,4,9
	.byte	'U',0
	.word	263
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	296
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	676
	.byte	4,2,35,0,0,3
	.byte	'Ifx_MSC_ABC',0,4,145,4,3
	.word	7659
	.byte	12,4,148,4,9,4,9
	.byte	'U',0
	.word	263
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	296
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	1025
	.byte	4,2,35,0,0,3
	.byte	'Ifx_MSC_ACCEN0',0,4,153,4,3
	.word	7720
	.byte	12,4,156,4,9,4,9
	.byte	'U',0
	.word	263
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	296
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	1582
	.byte	4,2,35,0,0,3
	.byte	'Ifx_MSC_ACCEN1',0,4,161,4,3
	.word	7784
	.byte	12,4,164,4,9,4,9
	.byte	'U',0
	.word	263
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	296
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	1659
	.byte	4,2,35,0,0,3
	.byte	'Ifx_MSC_CLC',0,4,169,4,3
	.word	7848
	.byte	12,4,172,4,9,4,9
	.byte	'U',0
	.word	263
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	296
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	1800
	.byte	4,2,35,0,0,3
	.byte	'Ifx_MSC_DC',0,4,177,4,3
	.word	7909
	.byte	12,4,180,4,9,4,9
	.byte	'U',0
	.word	263
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	296
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	1877
	.byte	4,2,35,0,0,3
	.byte	'Ifx_MSC_DD',0,4,185,4,3
	.word	7969
	.byte	12,4,188,4,9,4,9
	.byte	'U',0
	.word	263
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	296
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	1956
	.byte	4,2,35,0,0,3
	.byte	'Ifx_MSC_DDE',0,4,193,4,3
	.word	8029
	.byte	12,4,196,4,9,4,9
	.byte	'U',0
	.word	263
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	296
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	2039
	.byte	4,2,35,0,0,3
	.byte	'Ifx_MSC_DDM',0,4,201,4,3
	.word	8090
	.byte	12,4,204,4,9,4,9
	.byte	'U',0
	.word	263
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	296
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	2122
	.byte	4,2,35,0,0,3
	.byte	'Ifx_MSC_DSC',0,4,209,4,3
	.word	8151
	.byte	12,4,212,4,9,4,9
	.byte	'U',0
	.word	263
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	296
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	2376
	.byte	4,2,35,0,0,3
	.byte	'Ifx_MSC_DSCE',0,4,217,4,3
	.word	8212
	.byte	12,4,220,4,9,4,9
	.byte	'U',0
	.word	263
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	296
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	2633
	.byte	4,2,35,0,0,3
	.byte	'Ifx_MSC_DSDSH',0,4,225,4,3
	.word	8274
	.byte	12,4,228,4,9,4,9
	.byte	'U',0
	.word	263
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	296
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	2934
	.byte	4,2,35,0,0,3
	.byte	'Ifx_MSC_DSDSHE',0,4,233,4,3
	.word	8337
	.byte	12,4,236,4,9,4,9
	.byte	'U',0
	.word	263
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	296
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	3247
	.byte	4,2,35,0,0,3
	.byte	'Ifx_MSC_DSDSL',0,4,241,4,3
	.word	8401
	.byte	12,4,244,4,9,4,9
	.byte	'U',0
	.word	263
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	296
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	3548
	.byte	4,2,35,0,0,3
	.byte	'Ifx_MSC_DSDSLE',0,4,249,4,3
	.word	8464
	.byte	12,4,252,4,9,4,9
	.byte	'U',0
	.word	263
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	296
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	3861
	.byte	4,2,35,0,0,3
	.byte	'Ifx_MSC_DSS',0,4,129,5,3
	.word	8528
	.byte	12,4,132,5,9,4,9
	.byte	'U',0
	.word	263
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	296
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	4055
	.byte	4,2,35,0,0,3
	.byte	'Ifx_MSC_DSTE',0,4,137,5,3
	.word	8589
	.byte	12,4,140,5,9,4,9
	.byte	'U',0
	.word	263
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	296
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	4178
	.byte	4,2,35,0,0,3
	.byte	'Ifx_MSC_ESR',0,4,145,5,3
	.word	8651
	.byte	12,4,148,5,9,4,9
	.byte	'U',0
	.word	263
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	296
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	4753
	.byte	4,2,35,0,0,3
	.byte	'Ifx_MSC_ESRE',0,4,153,5,3
	.word	8712
	.byte	12,4,156,5,9,4,9
	.byte	'U',0
	.word	263
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	296
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	5350
	.byte	4,2,35,0,0,3
	.byte	'Ifx_MSC_FDR',0,4,161,5,3
	.word	8774
	.byte	12,4,164,5,9,4,9
	.byte	'U',0
	.word	263
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	296
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	5529
	.byte	4,2,35,0,0,3
	.byte	'Ifx_MSC_ICR',0,4,169,5,3
	.word	8835
	.byte	12,4,172,5,9,4,9
	.byte	'U',0
	.word	263
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	296
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	5776
	.byte	4,2,35,0,0,3
	.byte	'Ifx_MSC_ID',0,4,177,5,3
	.word	8896
	.byte	12,4,180,5,9,4,9
	.byte	'U',0
	.word	263
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	296
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	5883
	.byte	4,2,35,0,0,3
	.byte	'Ifx_MSC_ISC',0,4,185,5,3
	.word	8956
	.byte	12,4,188,5,9,4,9
	.byte	'U',0
	.word	263
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	296
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	6209
	.byte	4,2,35,0,0,3
	.byte	'Ifx_MSC_ISR',0,4,193,5,3
	.word	9017
	.byte	12,4,196,5,9,4,9
	.byte	'U',0
	.word	263
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	296
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	6346
	.byte	4,2,35,0,0,3
	.byte	'Ifx_MSC_KRST0',0,4,201,5,3
	.word	9078
	.byte	12,4,204,5,9,4,9
	.byte	'U',0
	.word	263
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	296
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	6457
	.byte	4,2,35,0,0,3
	.byte	'Ifx_MSC_KRST1',0,4,209,5,3
	.word	9141
	.byte	12,4,212,5,9,4,9
	.byte	'U',0
	.word	263
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	296
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	6549
	.byte	4,2,35,0,0,3
	.byte	'Ifx_MSC_KRSTCLR',0,4,217,5,3
	.word	9204
	.byte	12,4,220,5,9,4,9
	.byte	'U',0
	.word	263
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	296
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	6645
	.byte	4,2,35,0,0,3
	.byte	'Ifx_MSC_OCR',0,4,225,5,3
	.word	9269
	.byte	12,4,228,5,9,4,9
	.byte	'U',0
	.word	263
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	296
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	6907
	.byte	4,2,35,0,0,3
	.byte	'Ifx_MSC_OCS',0,4,233,5,3
	.word	9330
	.byte	12,4,236,5,9,4,9
	.byte	'U',0
	.word	263
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	296
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	7053
	.byte	4,2,35,0,0,3
	.byte	'Ifx_MSC_UD',0,4,241,5,3
	.word	9391
	.byte	12,4,244,5,9,4,9
	.byte	'U',0
	.word	263
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	296
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	7249
	.byte	4,2,35,0,0,3
	.byte	'Ifx_MSC_USCE',0,4,249,5,3
	.word	9451
	.byte	12,4,252,5,9,4,9
	.byte	'U',0
	.word	263
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	296
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	7487
	.byte	4,2,35,0,0,3
	.byte	'Ifx_MSC_USR',0,4,129,6,3
	.word	9513
	.byte	13,16
	.word	610
	.byte	14,1,0
.L8:
	.byte	15
	.word	9574
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L4:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,36,0,3,8,11,15,62,15,0,0,3,22,0,3,8,58,15,59,15,57,15
	.byte	73,19,0,0,4,21,0,54,15,0,0,5,15,0,73,19,0,0,6,59,0,3,8,0,0,7,53,0,73,19,0,0,8,19,1,58,15,59,15,57,15,11
	.byte	15,0,0,9,13,0,3,8,73,19,11,15,56,9,0,0,10,19,1,3,8,58,15,59,15,57,15,11,15,0,0,11,13,0,3,8,11,15,73,19
	.byte	13,15,12,15,56,9,0,0,12,23,1,58,15,59,15,57,15,11,15,0,0,13,1,1,11,15,73,19,0,0,14,33,0,47,15,0,0,15,38
	.byte	0,73,19,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L5:
	.word	.L10-.L9
.L9:
	.half	3
	.word	.L12-.L11
.L11:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg',0
	.byte	0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxMsc_cfg.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Platform_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Ifx_Types.h',0
	.byte	0,0,0
	.byte	'IfxMsc_regdef.h',0,1,0,0,0
.L12:
.L10:
	.sdecl	'.debug_info',debug,cluster('IfxMsc_cfg_indexMap')
	.sect	'.debug_info'
.L6:
	.word	265
	.half	3
	.word	.L7
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxMsc_cfg.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMsc_cfg_indexMap',0,1,55,30
	.word	.L8
	.byte	1,5,3
	.word	IfxMsc_cfg_indexMap
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMsc_cfg_indexMap')
	.sect	'.debug_abbrev'
.L7:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	; Module end
