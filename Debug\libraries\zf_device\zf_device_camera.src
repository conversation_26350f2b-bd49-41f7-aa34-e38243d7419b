	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc4184a --dep-file=zf_device_camera.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2\\\\xf_asr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/zf_device/zf_device_camera.src ../libraries/zf_device/zf_device_camera.c"
	.compiler_name		"ctc"
	;source	'../libraries/zf_device/zf_device_camera.c'

	
$TC16X
	
	.sdecl	'.text.zf_device_camera.camera_binary_image_decompression',code,cluster('camera_binary_image_decompression')
	.sect	'.text.zf_device_camera.camera_binary_image_decompression'
	.align	2
	
	.global	camera_binary_image_decompression
; Function camera_binary_image_decompression
.L25:
camera_binary_image_decompression:	.type	func
	mov.aa	a15,a4
.L90:
	mov.aa	a12,a5
.L91:
	mov	d8,d4
.L92:
	mov.a	a2,#0
	ne.a	d4,a2,a15
.L89:
	movh.a	a4,#@his(.1.str)
.L87:
	lea	a4,[a4]@los(.1.str)
	mov	d5,#62
	call	debug_assert_handler
.L88:
	mov.a	a2,#0
	ne.a	d4,a2,a12
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#63
	call	debug_assert_handler
.L69:
	j	.L2
.L3:
	mov	d15,#8
.L93:
	j	.L4
.L5:
	ld.bu	d0,[a15]
.L149:
	mov	d1,d15
.L95:
	rsub	d1,#0
.L94:
	sha	d0,d0,d1
.L150:
	jz.t	d0:0,.L6
.L151:
	mov	d0,#255
.L152:
	j	.L7
.L6:
	mov	d0,#0
.L7:
	st.b	[a12],d0
.L153:
	add.a	a12,#1
.L4:
	mov	d0,d15
.L96:
	add	d15,#-1
	extr.u	d15,d15,#0,#8
.L97:
	jne	d0,#0,.L5
.L154:
	add.a	a15,#1
.L2:
	mov	d15,d8
	add	d8,#-1
.L155:
	jne	d15,#0,.L3
.L156:
	ret
.L62:
	
__camera_binary_image_decompression_function_end:
	.size	camera_binary_image_decompression,__camera_binary_image_decompression_function_end-camera_binary_image_decompression
.L40:
	; End of function
	
	.sdecl	'.text.zf_device_camera.camera_send_image',code,cluster('camera_send_image')
	.sect	'.text.zf_device_camera.camera_send_image'
	.align	2
	
	.global	camera_send_image
; Function camera_send_image
.L27:
camera_send_image:	.type	func
	mov	d15,d4
.L101:
	mov.aa	a15,a4
.L102:
	mov	d8,d5
.L103:
	mov.a	a2,#0
	ne.a	d4,a2,a15
.L100:
	movh.a	a4,#@his(.1.str)
.L98:
	lea	a4,[a4]@los(.1.str)
	mov	d5,#87
.L99:
	call	debug_assert_handler
.L161:
	movh.a	a4,#@his(camera_send_image_frame_header)
	lea	a4,[a4]@los(camera_send_image_frame_header)
.L162:
	mov	d5,#4
	mov	d4,d15
.L104:
	call	uart_write_buffer
.L105:
	mov	d4,d15
.L106:
	mov.aa	a4,a15
.L108:
	mov	d5,d8
.L109:
	call	uart_write_buffer
.L107:
	ret
.L72:
	
__camera_send_image_function_end:
	.size	camera_send_image,__camera_send_image_function_end-camera_send_image
.L45:
	; End of function
	
	.sdecl	'.text.zf_device_camera.camera_fifo_init',code,cluster('camera_fifo_init')
	.sect	'.text.zf_device_camera.camera_fifo_init'
	.align	2
	
	.global	camera_fifo_init
; Function camera_fifo_init
.L29:
camera_fifo_init:	.type	func
	movh.a	a4,#@his(camera_receiver_fifo)
	lea	a4,[a4]@los(camera_receiver_fifo)
.L167:
	mov	d4,#0
.L168:
	movh.a	a5,#@his(camera_receiver_buffer)
	lea	a5,[a5]@los(camera_receiver_buffer)
.L169:
	mov	d5,#8
	call	fifo_init
.L170:
	ret
.L77:
	
__camera_fifo_init_function_end:
	.size	camera_fifo_init,__camera_fifo_init_function_end-camera_fifo_init
.L50:
	; End of function
	
	.sdecl	'.text.zf_device_camera.camera_init',code,cluster('camera_init')
	.sect	'.text.zf_device_camera.camera_init'
	.align	2
	
	.global	camera_init
; Function camera_init
.L31:
camera_init:	.type	func
	mov.aa	a15,a4
.L110:
	mov.aa	a12,a5
.L111:
	mov	d8,d4
.L112:
	movh.a	a2,#@his(camera_type)
	lea	a2,[a2]@los(camera_type)
	ld.bu	d15,[a2]
.L175:
	mov	d0,#1
	jeq	d15,d0,.L8
.L176:
	mov	d0,#2
	jeq	d15,d0,.L9
.L177:
	mov	d0,#3
	jeq	d15,d0,.L10
.L178:
	mov	d0,#4
	jeq	d15,d0,.L11
	j	.L12
.L8:
.L9:
	mov	d15,#0
.L113:
	j	.L13
.L14:
	mov	d5,#0
.L179:
	mov	d6,#0
.L180:
	mov	d7,#0
	mov	d4,d15
.L115:
	call	gpio_init
.L116:
	add	d15,#1
.L13:
	jlt.u	d15,#8,.L14
.L181:
	mov	d4,#5
.L182:
	mov	d5,#7
.L183:
	mov	d6,#1
.L184:
	mov.aa	a4,a15
.L117:
	mov.aa	a5,a12
.L119:
	mov	d7,d8
.L120:
	call	dma_init
.L118:
	mov	d15,d2
.L114:
	mov	d4,#9
.L185:
	mov	d5,#1
	call	exti_init
.L121:
	j	.L15
.L10:
	mov	d15,#0
.L122:
	j	.L16
.L17:
	mov	d5,#0
.L186:
	mov	d6,#0
.L187:
	mov	d7,#0
	mov	d4,d15
.L124:
	call	gpio_init
.L125:
	add	d15,#1
.L16:
	jlt.u	d15,#8,.L17
.L188:
	mov	d4,#5
.L189:
	mov	d5,#7
.L190:
	mov	d6,#0
.L191:
	mov.aa	a4,a15
.L126:
	mov.aa	a5,a12
.L128:
	mov	d7,d8
.L129:
	call	dma_init
.L127:
	mov	d15,d2
.L123:
	mov	d4,#9
.L192:
	mov	d5,#1
	call	exti_init
.L130:
	j	.L18
.L11:
	mov	d15,#0
.L131:
	j	.L19
.L20:
	mov	d5,#0
.L193:
	mov	d6,#0
.L194:
	mov	d7,#0
	mov	d4,d15
.L133:
	call	gpio_init
.L134:
	add	d15,#1
.L19:
	jlt.u	d15,#8,.L20
.L195:
	mov	d4,#5
.L196:
	mov	d5,#7
.L197:
	mov	d6,#0
.L198:
	mov.aa	a4,a15
.L135:
	mov.aa	a5,a12
.L137:
	mov	d7,d8
.L138:
	call	dma_init
.L136:
	mov	d15,d2
.L132:
	mov	d4,#9
.L199:
	mov	d5,#1
	call	exti_init
.L139:
	j	.L21
.L12:
	j	.L22
.L22:
.L21:
.L18:
.L15:
	mov	d2,d15
.L140:
	j	.L23
.L23:
	ret
.L78:
	
__camera_init_function_end:
	.size	camera_init,__camera_init_function_end-camera_init
.L55:
	; End of function
	
	.sdecl	'.bss.zf_device_camera.camera_receiver_fifo',data,cluster('camera_receiver_fifo')
	.sect	'.bss.zf_device_camera.camera_receiver_fifo'
	.global	camera_receiver_fifo
	.align	4
camera_receiver_fifo:	.type	object
	.size	camera_receiver_fifo,24
	.space	24
	.sdecl	'.bss.zf_device_camera.camera_receiver_buffer',data,cluster('camera_receiver_buffer')
	.sect	'.bss.zf_device_camera.camera_receiver_buffer'
	.global	camera_receiver_buffer
camera_receiver_buffer:	.type	object
	.size	camera_receiver_buffer,8
	.space	8
	.sdecl	'.data.zf_device_camera.camera_send_image_frame_header',data,cluster('camera_send_image_frame_header')
	.sect	'.data.zf_device_camera.camera_send_image_frame_header'
	.global	camera_send_image_frame_header
camera_send_image_frame_header:	.type	object
	.size	camera_send_image_frame_header,4
	.space	1
	.byte	255,1,1
	.sdecl	'.rodata.zf_device_camera..1.str',data,rom
	.sect	'.rodata.zf_device_camera..1.str'
.1.str:	.type	object
	.size	.1.str,42
	.byte	46,46,47,108,105,98,114,97
	.byte	114,105,101,115,47,122,102,95
	.byte	100,101,118,105,99,101,47,122
	.byte	102,95,100,101,118,105,99,101
	.byte	95,99,97,109,101,114,97,46
	.byte	99
	.space	1
	.calls	'camera_binary_image_decompression','debug_assert_handler'
	.calls	'camera_send_image','debug_assert_handler'
	.calls	'camera_send_image','uart_write_buffer'
	.calls	'camera_fifo_init','fifo_init'
	.calls	'camera_init','gpio_init'
	.calls	'camera_init','dma_init'
	.calls	'camera_init','exti_init'
	.calls	'camera_binary_image_decompression','',0
	.calls	'camera_send_image','',0
	.calls	'camera_fifo_init','',0
	.extern	debug_assert_handler
	.extern	gpio_init
	.extern	exti_init
	.extern	dma_init
	.extern	camera_type
	.extern	fifo_init
	.extern	uart_write_buffer
	.calls	'camera_init','',0
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L33:
	.word	120915
	.half	3
	.word	.L34
	.byte	4
.L32:
	.byte	1
	.byte	'../libraries/zf_device/zf_device_camera.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L35
	.byte	2,1,1,3
	.word	204
	.byte	4
	.byte	'Ifx__jump_and_link',0,3,1,61,17,1,1,5
	.byte	'fun',0,1,61,43
	.word	207
	.byte	6,0,7
	.byte	'__fract',0,4,128,1,7
	.byte	'float',0,4,4,8
	.byte	'Ifx__float_to_fract',0,3,1,152,2,18
	.word	252
	.byte	1,1,5
	.byte	'a',0,1,152,2,44
	.word	264
	.byte	6,0,4
	.byte	'Ifx__stopPerfCounters',0,3,1,172,2,17,1,1,6,0,7
	.byte	'unsigned long long int',0,8,7,9
	.byte	'void',0,3
	.word	376
	.byte	8
	.byte	'__ld64',0,3,2,135,1,19
	.word	350
	.byte	1,1,5
	.byte	'addr',0,2,135,1,32
	.word	382
	.byte	6,0,4
	.byte	'__st64',0,3,2,143,1,17,1,1,5
	.byte	'addr',0,2,143,1,30
	.word	382
	.byte	5
	.byte	'value',0,2,143,1,43
	.word	350
	.byte	6,0,7
	.byte	'unsigned int',0,4,7,7
	.byte	'int',0,4,5,7
	.byte	'unsigned int',0,4,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON0_Bits',0,4,241,8,16,4,11
	.byte	'ENDINIT',0,4
	.word	491
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	491
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	491
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	491
	.byte	16,0,2,35,0,0,12,4,247,14,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	507
	.byte	4,2,35,0,0
.L70:
	.byte	7
	.byte	'unsigned char',0,1,8,7
	.byte	'unsigned short int',0,2,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON1_Bits',0,4,250,8,16,4,11
	.byte	'reserved_0',0,1
	.word	643
	.byte	2,6,2,35,0,11
	.byte	'IR0',0,1
	.word	643
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	643
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	643
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	643
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	643
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	643
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	643
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	643
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	660
	.byte	16,0,2,35,2,0,12,4,255,14,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	682
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_WDTCPU_SR_Bits',0,4,137,9,16,4,11
	.byte	'AE',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	643
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	643
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	643
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	643
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	643
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	643
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	643
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	643
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	643
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	660
	.byte	16,0,2,35,2,0,12,4,135,15,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	926
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_WDTCPU',0,4,175,15,25,12,13
	.byte	'CON0',0
	.word	603
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	886
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	1117
	.byte	4,2,35,8,0,14
	.word	1157
	.byte	3
	.word	1220
	.byte	4
	.byte	'IfxScuWdt_clearCpuEndinitInline',0,3,3,181,3,17,1,1,5
	.byte	'watchdog',0,3,181,3,65
	.word	1225
	.byte	5
	.byte	'password',0,3,181,3,82
	.word	660
	.byte	6,0,4
	.byte	'IfxScuWdt_clearSafetyEndinitInline',0,3,3,204,3,17,1,1,5
	.byte	'password',0,3,204,3,59
	.word	660
	.byte	6,0,4
	.byte	'IfxScuWdt_setCpuEndinitInline',0,3,3,140,4,17,1,1,5
	.byte	'watchdog',0,3,140,4,63
	.word	1225
	.byte	5
	.byte	'password',0,3,140,4,80
	.word	660
	.byte	6,0,4
	.byte	'IfxScuWdt_setSafetyEndinitInline',0,3,3,163,4,17,1,1,5
	.byte	'password',0,3,163,4,57
	.word	660
	.byte	6,0,8
	.byte	'IfxScuWdt_getCpuWatchdogPasswordInline',0,3,3,227,3,19
	.word	660
	.byte	1,1,5
	.byte	'watchdog',0,3,227,3,74
	.word	1225
	.byte	6,0,8
	.byte	'IfxScuWdt_getSafetyWatchdogPasswordInline',0,3,3,253,3,19
	.word	660
	.byte	1,1,6,0,10
	.byte	'_Ifx_P_OUT_Bits',0,6,143,3,16,4,11
	.byte	'P0',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	643
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	643
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	643
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	643
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	643
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	643
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	643
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	643
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	643
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	643
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	643
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	643
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	643
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	643
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	643
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	660
	.byte	16,0,2,35,2,0,12,6,181,5,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1635
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMR_Bits',0,6,169,2,16,4,11
	.byte	'PS0',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	643
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	643
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	643
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	643
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	643
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	643
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	643
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	643
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	643
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	643
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	643
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	643
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	643
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	643
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	643
	.byte	1,0,2,35,1,11
	.byte	'PCL0',0,1
	.word	643
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	643
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	643
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	643
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	643
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	643
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	643
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	643
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	643
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	643
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	643
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	643
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	643
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	643
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	643
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	643
	.byte	1,0,2,35,3,0,12,6,133,5,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1951
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ID_Bits',0,6,110,16,4,11
	.byte	'MODREV',0,1
	.word	643
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	643
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	660
	.byte	16,0,2,35,2,0,12,6,148,4,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2522
	.byte	4,2,35,0,0,15,4
	.word	643
	.byte	16,3,0,10
	.byte	'_Ifx_P_IOCR0_Bits',0,6,140,1,16,4,11
	.byte	'reserved_0',0,1
	.word	643
	.byte	3,5,2,35,0,11
	.byte	'PC0',0,1
	.word	643
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	643
	.byte	3,5,2,35,1,11
	.byte	'PC1',0,1
	.word	643
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	643
	.byte	3,5,2,35,2,11
	.byte	'PC2',0,1
	.word	643
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	643
	.byte	3,5,2,35,3,11
	.byte	'PC3',0,1
	.word	643
	.byte	5,0,2,35,3,0,12,6,164,4,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2650
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR4_Bits',0,6,166,1,16,4,11
	.byte	'reserved_0',0,1
	.word	643
	.byte	3,5,2,35,0,11
	.byte	'PC4',0,1
	.word	643
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	643
	.byte	3,5,2,35,1,11
	.byte	'PC5',0,1
	.word	643
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	643
	.byte	3,5,2,35,2,11
	.byte	'PC6',0,1
	.word	643
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	643
	.byte	3,5,2,35,3,11
	.byte	'PC7',0,1
	.word	643
	.byte	5,0,2,35,3,0,12,6,180,4,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2865
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR8_Bits',0,6,179,1,16,4,11
	.byte	'reserved_0',0,1
	.word	643
	.byte	3,5,2,35,0,11
	.byte	'PC8',0,1
	.word	643
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	643
	.byte	3,5,2,35,1,11
	.byte	'PC9',0,1
	.word	643
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	643
	.byte	3,5,2,35,2,11
	.byte	'PC10',0,1
	.word	643
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	643
	.byte	3,5,2,35,3,11
	.byte	'PC11',0,1
	.word	643
	.byte	5,0,2,35,3,0,12,6,188,4,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3080
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR12_Bits',0,6,153,1,16,4,11
	.byte	'reserved_0',0,1
	.word	643
	.byte	3,5,2,35,0,11
	.byte	'PC12',0,1
	.word	643
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	643
	.byte	3,5,2,35,1,11
	.byte	'PC13',0,1
	.word	643
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	643
	.byte	3,5,2,35,2,11
	.byte	'PC14',0,1
	.word	643
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	643
	.byte	3,5,2,35,3,11
	.byte	'PC15',0,1
	.word	643
	.byte	5,0,2,35,3,0,12,6,172,4,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3297
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IN_Bits',0,6,118,16,4,11
	.byte	'P0',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	643
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	643
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	643
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	643
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	643
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	643
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	643
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	643
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	643
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	643
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	643
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	643
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	643
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	643
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	643
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	660
	.byte	16,0,2,35,2,0,12,6,156,4,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3517
	.byte	4,2,35,0,0,15,24
	.word	643
	.byte	16,23,0,10
	.byte	'_Ifx_P_PDR0_Bits',0,6,205,3,16,4,11
	.byte	'PD0',0,1
	.word	643
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	643
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	643
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	643
	.byte	1,0,2,35,0,11
	.byte	'PD2',0,1
	.word	643
	.byte	3,5,2,35,1,11
	.byte	'PL2',0,1
	.word	643
	.byte	1,4,2,35,1,11
	.byte	'PD3',0,1
	.word	643
	.byte	3,1,2,35,1,11
	.byte	'PL3',0,1
	.word	643
	.byte	1,0,2,35,1,11
	.byte	'PD4',0,1
	.word	643
	.byte	3,5,2,35,2,11
	.byte	'PL4',0,1
	.word	643
	.byte	1,4,2,35,2,11
	.byte	'PD5',0,1
	.word	643
	.byte	3,1,2,35,2,11
	.byte	'PL5',0,1
	.word	643
	.byte	1,0,2,35,2,11
	.byte	'PD6',0,1
	.word	643
	.byte	3,5,2,35,3,11
	.byte	'PL6',0,1
	.word	643
	.byte	1,4,2,35,3,11
	.byte	'PD7',0,1
	.word	643
	.byte	3,1,2,35,3,11
	.byte	'PL7',0,1
	.word	643
	.byte	1,0,2,35,3,0,12,6,205,5,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3840
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PDR1_Bits',0,6,226,3,16,4,11
	.byte	'PD8',0,1
	.word	643
	.byte	3,5,2,35,0,11
	.byte	'PL8',0,1
	.word	643
	.byte	1,4,2,35,0,11
	.byte	'PD9',0,1
	.word	643
	.byte	3,1,2,35,0,11
	.byte	'PL9',0,1
	.word	643
	.byte	1,0,2,35,0,11
	.byte	'PD10',0,1
	.word	643
	.byte	3,5,2,35,1,11
	.byte	'PL10',0,1
	.word	643
	.byte	1,4,2,35,1,11
	.byte	'PD11',0,1
	.word	643
	.byte	3,1,2,35,1,11
	.byte	'PL11',0,1
	.word	643
	.byte	1,0,2,35,1,11
	.byte	'PD12',0,1
	.word	643
	.byte	3,5,2,35,2,11
	.byte	'PL12',0,1
	.word	643
	.byte	1,4,2,35,2,11
	.byte	'PD13',0,1
	.word	643
	.byte	3,1,2,35,2,11
	.byte	'PL13',0,1
	.word	643
	.byte	1,0,2,35,2,11
	.byte	'PD14',0,1
	.word	643
	.byte	3,5,2,35,3,11
	.byte	'PL14',0,1
	.word	643
	.byte	1,4,2,35,3,11
	.byte	'PD15',0,1
	.word	643
	.byte	3,1,2,35,3,11
	.byte	'PL15',0,1
	.word	643
	.byte	1,0,2,35,3,0,12,6,213,5,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4144
	.byte	4,2,35,0,0,15,8
	.word	643
	.byte	16,7,0,10
	.byte	'_Ifx_P_ESR_Bits',0,6,88,16,4,11
	.byte	'EN0',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	643
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	643
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	643
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	643
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	643
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	643
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	643
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	643
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	643
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	643
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	643
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	643
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	643
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	643
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	643
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	660
	.byte	16,0,2,35,2,0,12,6,140,4,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4469
	.byte	4,2,35,0,0,15,12
	.word	643
	.byte	16,11,0,10
	.byte	'_Ifx_P_PDISC_Bits',0,6,183,3,16,4,11
	.byte	'PDIS0',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	643
	.byte	1,6,2,35,0,11
	.byte	'PDIS2',0,1
	.word	643
	.byte	1,5,2,35,0,11
	.byte	'PDIS3',0,1
	.word	643
	.byte	1,4,2,35,0,11
	.byte	'PDIS4',0,1
	.word	643
	.byte	1,3,2,35,0,11
	.byte	'PDIS5',0,1
	.word	643
	.byte	1,2,2,35,0,11
	.byte	'PDIS6',0,1
	.word	643
	.byte	1,1,2,35,0,11
	.byte	'PDIS7',0,1
	.word	643
	.byte	1,0,2,35,0,11
	.byte	'PDIS8',0,1
	.word	643
	.byte	1,7,2,35,1,11
	.byte	'PDIS9',0,1
	.word	643
	.byte	1,6,2,35,1,11
	.byte	'PDIS10',0,1
	.word	643
	.byte	1,5,2,35,1,11
	.byte	'PDIS11',0,1
	.word	643
	.byte	1,4,2,35,1,11
	.byte	'PDIS12',0,1
	.word	643
	.byte	1,3,2,35,1,11
	.byte	'PDIS13',0,1
	.word	643
	.byte	1,2,2,35,1,11
	.byte	'PDIS14',0,1
	.word	643
	.byte	1,1,2,35,1,11
	.byte	'PDIS15',0,1
	.word	643
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	660
	.byte	16,0,2,35,2,0,12,6,197,5,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4809
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PCSR_Bits',0,6,165,3,16,4,11
	.byte	'SEL0',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'SEL1',0,1
	.word	643
	.byte	1,6,2,35,0,11
	.byte	'SEL2',0,1
	.word	643
	.byte	1,5,2,35,0,11
	.byte	'SEL3',0,1
	.word	643
	.byte	1,4,2,35,0,11
	.byte	'SEL4',0,1
	.word	643
	.byte	1,3,2,35,0,11
	.byte	'SEL5',0,1
	.word	643
	.byte	1,2,2,35,0,11
	.byte	'SEL6',0,1
	.word	643
	.byte	1,1,2,35,0,11
	.byte	'SEL7',0,1
	.word	643
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	643
	.byte	2,6,2,35,1,11
	.byte	'SEL10',0,1
	.word	643
	.byte	1,5,2,35,1,11
	.byte	'SEL11',0,1
	.word	643
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	468
	.byte	19,1,2,35,0,11
	.byte	'LCK',0,1
	.word	643
	.byte	1,0,2,35,3,0,12,6,189,5,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5175
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR0_Bits',0,6,206,2,16,4,11
	.byte	'PS0',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	643
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	643
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	643
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	468
	.byte	28,0,2,35,0,0,12,6,149,5,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5461
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR4_Bits',0,6,227,2,16,4,11
	.byte	'reserved_0',0,1
	.word	643
	.byte	4,4,2,35,0,11
	.byte	'PS4',0,1
	.word	643
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	643
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	643
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	643
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	468
	.byte	24,0,2,35,0,0,12,6,165,5,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5608
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR8_Bits',0,6,238,2,16,4,11
	.byte	'reserved_0',0,1
	.word	643
	.byte	8,0,2,35,0,11
	.byte	'PS8',0,1
	.word	643
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	643
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	643
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	643
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	468
	.byte	20,0,2,35,0,0,12,6,173,5,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5777
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR12_Bits',0,6,216,2,16,4,11
	.byte	'reserved_0',0,2
	.word	660
	.byte	12,4,2,35,0,11
	.byte	'PS12',0,1
	.word	643
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	643
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	643
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	643
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	660
	.byte	16,0,2,35,2,0,12,6,157,5,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5949
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR0_Bits',0,6,232,1,16,4,11
	.byte	'reserved_0',0,2
	.word	660
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	643
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	643
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	643
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	643
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	660
	.byte	12,0,2,35,2,0,12,6,229,4,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6124
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR4_Bits',0,6,253,1,16,4,11
	.byte	'reserved_0',0,4
	.word	468
	.byte	20,12,2,35,0,11
	.byte	'PCL4',0,1
	.word	643
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	643
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	643
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	643
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	643
	.byte	8,0,2,35,3,0,12,6,245,4,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6298
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR8_Bits',0,6,136,2,16,4,11
	.byte	'reserved_0',0,4
	.word	468
	.byte	24,8,2,35,0,11
	.byte	'PCL8',0,1
	.word	643
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	643
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	643
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	643
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	643
	.byte	4,0,2,35,3,0,12,6,253,4,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6472
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR12_Bits',0,6,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	468
	.byte	28,4,2,35,0,11
	.byte	'PCL12',0,1
	.word	643
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	643
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	643
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	643
	.byte	1,0,2,35,3,0,12,6,237,4,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6648
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR_Bits',0,6,249,2,16,4,11
	.byte	'PS0',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	643
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	643
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	643
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	643
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	643
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	643
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	643
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	643
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	643
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	643
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	643
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	643
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	643
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	643
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	643
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	660
	.byte	16,0,2,35,2,0,12,6,141,5,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6804
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR_Bits',0,6,147,2,16,4,11
	.byte	'reserved_0',0,2
	.word	660
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	643
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	643
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	643
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	643
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	643
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	643
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	643
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	643
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	643
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	643
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	643
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	643
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	643
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	643
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	643
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	643
	.byte	1,0,2,35,3,0,12,6,221,4,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7137
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR0_Bits',0,6,192,1,16,4,11
	.byte	'reserved_0',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	643
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	468
	.byte	30,0,2,35,0,0,12,6,196,4,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7485
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_Bits',0,6,200,1,16,4,11
	.byte	'reserved_0',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	643
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	468
	.byte	30,0,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_P21_Bits',0,6,208,1,16,4,11
	.byte	'RDIS_CTRL',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'RX_DIS',0,1
	.word	643
	.byte	1,6,2,35,0,11
	.byte	'TERM',0,1
	.word	643
	.byte	1,5,2,35,0,11
	.byte	'LRXTERM',0,1
	.word	643
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	468
	.byte	24,0,2,35,0,0,12,6,204,4,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7609
	.byte	4,2,35,0,13
	.byte	'B_P21',0
	.word	7693
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR2_Bits',0,6,218,1,16,4,11
	.byte	'reserved_0',0,1
	.word	643
	.byte	8,0,2,35,0,11
	.byte	'LVDSR',0,1
	.word	643
	.byte	1,7,2,35,1,11
	.byte	'LVDSRL',0,1
	.word	643
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	643
	.byte	2,4,2,35,1,11
	.byte	'TDIS_CTRL',0,1
	.word	643
	.byte	1,3,2,35,1,11
	.byte	'TX_DIS',0,1
	.word	643
	.byte	1,2,2,35,1,11
	.byte	'TX_PD',0,1
	.word	643
	.byte	1,1,2,35,1,11
	.byte	'TX_PWDPD',0,1
	.word	643
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	660
	.byte	16,0,2,35,2,0,12,6,213,4,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7873
	.byte	4,2,35,0,0,15,76
	.word	643
	.byte	16,75,0,10
	.byte	'_Ifx_P_ACCEN1_Bits',0,6,82,16,4,11
	.byte	'reserved_0',0,4
	.word	468
	.byte	32,0,2,35,0,0,12,6,132,4,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8126
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ACCEN0_Bits',0,6,45,16,4,11
	.byte	'EN0',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	643
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	643
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	643
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	643
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	643
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	643
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	643
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	643
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	643
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	643
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	643
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	643
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	643
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	643
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	643
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	643
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	643
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	643
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	643
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	643
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	643
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	643
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	643
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	643
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	643
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	643
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	643
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	643
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	643
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	643
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	643
	.byte	1,0,2,35,3,0,12,6,252,3,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8213
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P',0,6,229,5,25,128,2,13
	.byte	'OUT',0
	.word	1911
	.byte	4,2,35,0,13
	.byte	'OMR',0
	.word	2482
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	2601
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	2641
	.byte	4,2,35,12,13
	.byte	'IOCR0',0
	.word	2825
	.byte	4,2,35,16,13
	.byte	'IOCR4',0
	.word	3040
	.byte	4,2,35,20,13
	.byte	'IOCR8',0
	.word	3257
	.byte	4,2,35,24,13
	.byte	'IOCR12',0
	.word	3477
	.byte	4,2,35,28,13
	.byte	'reserved_20',0
	.word	2641
	.byte	4,2,35,32,13
	.byte	'IN',0
	.word	3791
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	3831
	.byte	24,2,35,40,13
	.byte	'PDR0',0
	.word	4104
	.byte	4,2,35,64,13
	.byte	'PDR1',0
	.word	4420
	.byte	4,2,35,68,13
	.byte	'reserved_48',0
	.word	4460
	.byte	8,2,35,72,13
	.byte	'ESR',0
	.word	4760
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	4800
	.byte	12,2,35,84,13
	.byte	'PDISC',0
	.word	5135
	.byte	4,2,35,96,13
	.byte	'PCSR',0
	.word	5421
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	4460
	.byte	8,2,35,104,13
	.byte	'OMSR0',0
	.word	5568
	.byte	4,2,35,112,13
	.byte	'OMSR4',0
	.word	5737
	.byte	4,2,35,116,13
	.byte	'OMSR8',0
	.word	5909
	.byte	4,2,35,120,13
	.byte	'OMSR12',0
	.word	6084
	.byte	4,2,35,124,13
	.byte	'OMCR0',0
	.word	6258
	.byte	4,3,35,128,1,13
	.byte	'OMCR4',0
	.word	6432
	.byte	4,3,35,132,1,13
	.byte	'OMCR8',0
	.word	6608
	.byte	4,3,35,136,1,13
	.byte	'OMCR12',0
	.word	6764
	.byte	4,3,35,140,1,13
	.byte	'OMSR',0
	.word	7097
	.byte	4,3,35,144,1,13
	.byte	'OMCR',0
	.word	7445
	.byte	4,3,35,148,1,13
	.byte	'reserved_98',0
	.word	4460
	.byte	8,3,35,152,1,13
	.byte	'LPCR0',0
	.word	7569
	.byte	4,3,35,160,1,13
	.byte	'LPCR1',0
	.word	7818
	.byte	4,3,35,164,1,13
	.byte	'LPCR2',0
	.word	8077
	.byte	4,3,35,168,1,13
	.byte	'reserved_A4',0
	.word	8117
	.byte	76,3,35,172,1,13
	.byte	'ACCEN1',0
	.word	8173
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	8740
	.byte	4,3,35,252,1,0,14
	.word	8780
	.byte	3
	.word	9383
	.byte	17,5,83,9,1,18
	.byte	'IfxPort_InputMode_undefined',0,127,18
	.byte	'IfxPort_InputMode_noPullDevice',0,0,18
	.byte	'IfxPort_InputMode_pullDown',0,8,18
	.byte	'IfxPort_InputMode_pullUp',0,16,0,4
	.byte	'IfxPort_setPinModeInput',0,3,5,196,4,17,1,1,5
	.byte	'port',0,5,196,4,48
	.word	9388
	.byte	5
	.byte	'pinIndex',0,5,196,4,60
	.word	643
	.byte	5
	.byte	'mode',0,5,196,4,88
	.word	9393
	.byte	6,0,17,5,134,1,9,1,18
	.byte	'IfxPort_OutputMode_pushPull',0,128,1,18
	.byte	'IfxPort_OutputMode_openDrain',0,192,1,0,17,5,120,9,1,18
	.byte	'IfxPort_OutputIdx_general',0,128,1,18
	.byte	'IfxPort_OutputIdx_alt1',0,136,1,18
	.byte	'IfxPort_OutputIdx_alt2',0,144,1,18
	.byte	'IfxPort_OutputIdx_alt3',0,152,1,18
	.byte	'IfxPort_OutputIdx_alt4',0,160,1,18
	.byte	'IfxPort_OutputIdx_alt5',0,168,1,18
	.byte	'IfxPort_OutputIdx_alt6',0,176,1,18
	.byte	'IfxPort_OutputIdx_alt7',0,184,1,0,4
	.byte	'IfxPort_setPinModeOutput',0,3,5,202,4,17,1,1,5
	.byte	'port',0,5,202,4,49
	.word	9388
	.byte	5
	.byte	'pinIndex',0,5,202,4,61
	.word	643
	.byte	5
	.byte	'mode',0,5,202,4,90
	.word	9598
	.byte	5
	.byte	'index',0,5,202,4,114
	.word	9668
	.byte	6,0,17,5,172,1,9,4,18
	.byte	'IfxPort_State_notChanged',0,0,18
	.byte	'IfxPort_State_high',0,1,18
	.byte	'IfxPort_State_low',0,128,128,4,18
	.byte	'IfxPort_State_toggled',0,129,128,4,0,4
	.byte	'IfxPort_setPinState',0,3,5,208,4,17,1,1,5
	.byte	'port',0,5,208,4,44
	.word	9388
	.byte	5
	.byte	'pinIndex',0,5,208,4,56
	.word	643
	.byte	5
	.byte	'action',0,5,208,4,80
	.word	9981
	.byte	6,0,10
	.byte	'_Ifx_SRC_SRCR_Bits',0,8,45,16,4,11
	.byte	'SRPN',0,1
	.word	643
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	643
	.byte	2,6,2,35,1,11
	.byte	'SRE',0,1
	.word	643
	.byte	1,5,2,35,1,11
	.byte	'TOS',0,1
	.word	643
	.byte	2,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	643
	.byte	3,0,2,35,1,11
	.byte	'ECC',0,1
	.word	643
	.byte	6,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	643
	.byte	2,0,2,35,2,11
	.byte	'SRR',0,1
	.word	643
	.byte	1,7,2,35,3,11
	.byte	'CLRR',0,1
	.word	643
	.byte	1,6,2,35,3,11
	.byte	'SETR',0,1
	.word	643
	.byte	1,5,2,35,3,11
	.byte	'IOV',0,1
	.word	643
	.byte	1,4,2,35,3,11
	.byte	'IOVCLR',0,1
	.word	643
	.byte	1,3,2,35,3,11
	.byte	'SWS',0,1
	.word	643
	.byte	1,2,2,35,3,11
	.byte	'SWSCLR',0,1
	.word	643
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	643
	.byte	1,0,2,35,3,0,12,8,70,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10162
	.byte	4,2,35,0,0,14
	.word	10452
	.byte	3
	.word	10491
	.byte	4
	.byte	'IfxSrc_clearRequest',0,3,7,250,1,17,1,1,5
	.byte	'src',0,7,250,1,60
	.word	10496
	.byte	6,0,17,10,156,1,9,1,18
	.byte	'IfxCpu_ResourceCpu_0',0,0,18
	.byte	'IfxCpu_ResourceCpu_1',0,1,18
	.byte	'IfxCpu_ResourceCpu_none',0,2,0,8
	.byte	'IfxCpu_getCoreIndex',0,3,9,141,6,31
	.word	10544
	.byte	1,1,6,0,8
	.byte	'IfxCpu_areInterruptsEnabled',0,3,9,139,5,20
	.word	643
	.byte	1,1,6,0,8
	.byte	'IfxCpu_disableInterrupts',0,3,9,147,5,20
	.word	643
	.byte	1,1,19,6,0,0,4
	.byte	'IfxCpu_enableInterrupts',0,3,9,157,5,17,1,1,6,0,4
	.byte	'IfxCpu_forceDisableInterrupts',0,3,9,225,5,17,1,1,6,0,4
	.byte	'IfxCpu_restoreInterrupts',0,3,9,168,7,17,1,1,5
	.byte	'enabled',0,9,168,7,50
	.word	643
	.byte	6,0
.L67:
	.byte	7
	.byte	'unsigned long int',0,4,7,8
	.byte	'IfxCpu_getPerformanceCounter',0,3,9,161,6,19
	.word	10866
	.byte	1,1,5
	.byte	'address',0,9,161,6,55
	.word	660
	.byte	6,0,8
	.byte	'IfxCpu_getPerformanceCounterStickyOverflow',0,3,9,190,6,20
	.word	643
	.byte	1,1,5
	.byte	'address',0,9,190,6,70
	.word	660
	.byte	6,0,4
	.byte	'IfxCpu_updatePerformanceCounter',0,3,9,172,8,17,1,1,5
	.byte	'address',0,9,172,8,56
	.word	10866
	.byte	5
	.byte	'count',0,9,172,8,72
	.word	10866
	.byte	19,6,0,0,8
	.byte	'IfxScuCcu_getStmFrequency',0,3,11,226,8,20
	.word	264
	.byte	1,1,6,0,10
	.byte	'_Ifx_ASCLIN_CLC_Bits',0,13,118,16,4,11
	.byte	'DISR',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'DISS',0,1
	.word	643
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	643
	.byte	1,5,2,35,0,11
	.byte	'EDIS',0,1
	.word	643
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	468
	.byte	28,0,2,35,0,0,12,13,207,3,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11137
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_IOCR_Bits',0,13,169,2,16,4,11
	.byte	'ALTI',0,1
	.word	643
	.byte	3,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	643
	.byte	1,4,2,35,0,11
	.byte	'DEPTH',0,2
	.word	660
	.byte	6,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	643
	.byte	6,0,2,35,1,11
	.byte	'CTS',0,1
	.word	643
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	660
	.byte	7,7,2,35,2,11
	.byte	'RCPOL',0,1
	.word	643
	.byte	1,6,2,35,3,11
	.byte	'CPOL',0,1
	.word	643
	.byte	1,5,2,35,3,11
	.byte	'SPOL',0,1
	.word	643
	.byte	1,4,2,35,3,11
	.byte	'LB',0,1
	.word	643
	.byte	1,3,2,35,3,11
	.byte	'CTSEN',0,1
	.word	643
	.byte	1,2,2,35,3,11
	.byte	'RXM',0,1
	.word	643
	.byte	1,1,2,35,3,11
	.byte	'TXM',0,1
	.word	643
	.byte	1,0,2,35,3,0,12,13,151,4,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11296
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_ID_Bits',0,13,161,2,16,4,11
	.byte	'MODREV',0,1
	.word	643
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	643
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	660
	.byte	16,0,2,35,2,0,12,13,143,4,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11591
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_TXFIFOCON_Bits',0,13,149,3,16,4,11
	.byte	'FLUSH',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'ENO',0,1
	.word	643
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	643
	.byte	4,2,2,35,0,11
	.byte	'INW',0,1
	.word	643
	.byte	2,0,2,35,0,11
	.byte	'INTLEVEL',0,1
	.word	643
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	643
	.byte	4,0,2,35,1,11
	.byte	'FILL',0,1
	.word	643
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,2
	.word	660
	.byte	11,0,2,35,2,0,12,13,247,4,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11716
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_RXFIFOCON_Bits',0,13,129,3,16,4,11
	.byte	'FLUSH',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'ENI',0,1
	.word	643
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	643
	.byte	4,2,2,35,0,11
	.byte	'OUTW',0,1
	.word	643
	.byte	2,0,2,35,0,11
	.byte	'INTLEVEL',0,1
	.word	643
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	643
	.byte	4,0,2,35,1,11
	.byte	'FILL',0,1
	.word	643
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,2
	.word	660
	.byte	10,1,2,35,2,11
	.byte	'BUF',0,1
	.word	643
	.byte	1,0,2,35,3,0,12,13,231,4,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11941
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_BITCON_Bits',0,13,88,16,4,11
	.byte	'PRESCALER',0,2
	.word	660
	.byte	12,4,2,35,0,11
	.byte	'reserved_12',0,1
	.word	643
	.byte	4,0,2,35,1,11
	.byte	'OVERSAMPLING',0,1
	.word	643
	.byte	4,4,2,35,2,11
	.byte	'reserved_20',0,1
	.word	643
	.byte	4,0,2,35,2,11
	.byte	'SAMPLEPOINT',0,1
	.word	643
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	643
	.byte	3,1,2,35,3,11
	.byte	'SM',0,1
	.word	643
	.byte	1,0,2,35,3,0,12,13,183,3,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12182
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_FRAMECON_Bits',0,13,145,2,16,4,11
	.byte	'reserved_0',0,1
	.word	643
	.byte	6,2,2,35,0,11
	.byte	'IDLE',0,2
	.word	660
	.byte	3,7,2,35,0,11
	.byte	'STOP',0,1
	.word	643
	.byte	3,4,2,35,1,11
	.byte	'LEAD',0,1
	.word	643
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	643
	.byte	1,0,2,35,1,11
	.byte	'MODE',0,1
	.word	643
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	660
	.byte	10,4,2,35,2,11
	.byte	'MSB',0,1
	.word	643
	.byte	1,3,2,35,3,11
	.byte	'CEN',0,1
	.word	643
	.byte	1,2,2,35,3,11
	.byte	'PEN',0,1
	.word	643
	.byte	1,1,2,35,3,11
	.byte	'ODD',0,1
	.word	643
	.byte	1,0,2,35,3,0,12,13,135,4,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12403
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_DATCON_Bits',0,13,136,1,16,4,11
	.byte	'DATLEN',0,1
	.word	643
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	660
	.byte	9,3,2,35,0,11
	.byte	'HO',0,1
	.word	643
	.byte	1,2,2,35,1,11
	.byte	'RM',0,1
	.word	643
	.byte	1,1,2,35,1,11
	.byte	'CSM',0,1
	.word	643
	.byte	1,0,2,35,1,11
	.byte	'RESPONSE',0,1
	.word	643
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	643
	.byte	8,0,2,35,3,0,12,13,223,3,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12668
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_BRG_Bits',0,13,109,16,4,11
	.byte	'DENOMINATOR',0,2
	.word	660
	.byte	12,4,2,35,0,11
	.byte	'reserved_12',0,1
	.word	643
	.byte	4,0,2,35,1,11
	.byte	'NUMERATOR',0,2
	.word	660
	.byte	12,4,2,35,2,11
	.byte	'reserved_28',0,1
	.word	643
	.byte	4,0,2,35,3,0,12,13,199,3,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12865
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_BRD_Bits',0,13,100,16,4,11
	.byte	'LOWERLIMIT',0,1
	.word	643
	.byte	8,0,2,35,0,11
	.byte	'UPPERLIMIT',0,1
	.word	643
	.byte	8,0,2,35,1,11
	.byte	'MEASURED',0,2
	.word	660
	.byte	12,4,2,35,2,11
	.byte	'reserved_28',0,1
	.word	643
	.byte	4,0,2,35,3,0,12,13,191,3,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13022
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_LIN_CON_Bits',0,13,216,2,16,4,11
	.byte	'reserved_0',0,4
	.word	468
	.byte	23,9,2,35,0,11
	.byte	'CSI',0,1
	.word	643
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	643
	.byte	1,7,2,35,3,11
	.byte	'CSEN',0,1
	.word	643
	.byte	1,6,2,35,3,11
	.byte	'MS',0,1
	.word	643
	.byte	1,5,2,35,3,11
	.byte	'ABD',0,1
	.word	643
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	643
	.byte	4,0,2,35,3,0,12,13,191,4,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13176
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_LIN_BTIMER_Bits',0,13,209,2,16,4,11
	.byte	'BREAK',0,1
	.word	643
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	468
	.byte	26,0,2,35,0,0,12,13,183,4,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13376
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_LIN_HTIMER_Bits',0,13,228,2,16,4,11
	.byte	'HEADER',0,1
	.word	643
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	468
	.byte	24,0,2,35,0,0,12,13,199,4,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13490
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_LIN',0,13,135,5,25,12,13
	.byte	'CON',0
	.word	13336
	.byte	4,2,35,0,13
	.byte	'BTIMER',0
	.word	13450
	.byte	4,2,35,4,13
	.byte	'HTIMER',0
	.word	13565
	.byte	4,2,35,8,0,14
	.word	13605
	.byte	10
	.byte	'_Ifx_ASCLIN_FLAGS_Bits',0,13,148,1,16,4,11
	.byte	'TH',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'TR',0,1
	.word	643
	.byte	1,6,2,35,0,11
	.byte	'RH',0,1
	.word	643
	.byte	1,5,2,35,0,11
	.byte	'RR',0,1
	.word	643
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	643
	.byte	1,3,2,35,0,11
	.byte	'FED',0,1
	.word	643
	.byte	1,2,2,35,0,11
	.byte	'RED',0,1
	.word	643
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	660
	.byte	6,3,2,35,0,11
	.byte	'TWRQ',0,1
	.word	643
	.byte	1,2,2,35,1,11
	.byte	'THRQ',0,1
	.word	643
	.byte	1,1,2,35,1,11
	.byte	'TRRQ',0,1
	.word	643
	.byte	1,0,2,35,1,11
	.byte	'PE',0,1
	.word	643
	.byte	1,7,2,35,2,11
	.byte	'TC',0,1
	.word	643
	.byte	1,6,2,35,2,11
	.byte	'FE',0,1
	.word	643
	.byte	1,5,2,35,2,11
	.byte	'HT',0,1
	.word	643
	.byte	1,4,2,35,2,11
	.byte	'RT',0,1
	.word	643
	.byte	1,3,2,35,2,11
	.byte	'BD',0,1
	.word	643
	.byte	1,2,2,35,2,11
	.byte	'LP',0,1
	.word	643
	.byte	1,1,2,35,2,11
	.byte	'LA',0,1
	.word	643
	.byte	1,0,2,35,2,11
	.byte	'LC',0,1
	.word	643
	.byte	1,7,2,35,3,11
	.byte	'CE',0,1
	.word	643
	.byte	1,6,2,35,3,11
	.byte	'RFO',0,1
	.word	643
	.byte	1,5,2,35,3,11
	.byte	'RFU',0,1
	.word	643
	.byte	1,4,2,35,3,11
	.byte	'RFL',0,1
	.word	643
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	643
	.byte	1,2,2,35,3,11
	.byte	'TFO',0,1
	.word	643
	.byte	1,1,2,35,3,11
	.byte	'TFL',0,1
	.word	643
	.byte	1,0,2,35,3,0,12,13,231,3,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13678
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_FLAGSSET_Bits',0,13,241,1,16,4,11
	.byte	'THS',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'TRS',0,1
	.word	643
	.byte	1,6,2,35,0,11
	.byte	'RHS',0,1
	.word	643
	.byte	1,5,2,35,0,11
	.byte	'RRS',0,1
	.word	643
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	643
	.byte	1,3,2,35,0,11
	.byte	'FEDS',0,1
	.word	643
	.byte	1,2,2,35,0,11
	.byte	'REDS',0,1
	.word	643
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	660
	.byte	6,3,2,35,0,11
	.byte	'TWRQS',0,1
	.word	643
	.byte	1,2,2,35,1,11
	.byte	'THRQS',0,1
	.word	643
	.byte	1,1,2,35,1,11
	.byte	'TRRQS',0,1
	.word	643
	.byte	1,0,2,35,1,11
	.byte	'PES',0,1
	.word	643
	.byte	1,7,2,35,2,11
	.byte	'TCS',0,1
	.word	643
	.byte	1,6,2,35,2,11
	.byte	'FES',0,1
	.word	643
	.byte	1,5,2,35,2,11
	.byte	'HTS',0,1
	.word	643
	.byte	1,4,2,35,2,11
	.byte	'RTS',0,1
	.word	643
	.byte	1,3,2,35,2,11
	.byte	'BDS',0,1
	.word	643
	.byte	1,2,2,35,2,11
	.byte	'LPS',0,1
	.word	643
	.byte	1,1,2,35,2,11
	.byte	'LAS',0,1
	.word	643
	.byte	1,0,2,35,2,11
	.byte	'LCS',0,1
	.word	643
	.byte	1,7,2,35,3,11
	.byte	'CES',0,1
	.word	643
	.byte	1,6,2,35,3,11
	.byte	'RFOS',0,1
	.word	643
	.byte	1,5,2,35,3,11
	.byte	'RFUS',0,1
	.word	643
	.byte	1,4,2,35,3,11
	.byte	'RFLS',0,1
	.word	643
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	643
	.byte	1,2,2,35,3,11
	.byte	'TFOS',0,1
	.word	643
	.byte	1,1,2,35,3,11
	.byte	'TFLS',0,1
	.word	643
	.byte	1,0,2,35,3,0,12,13,255,3,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14164
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_FLAGSCLEAR_Bits',0,13,180,1,16,4,11
	.byte	'THC',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'TRC',0,1
	.word	643
	.byte	1,6,2,35,0,11
	.byte	'RHC',0,1
	.word	643
	.byte	1,5,2,35,0,11
	.byte	'RRC',0,1
	.word	643
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	643
	.byte	1,3,2,35,0,11
	.byte	'FEDC',0,1
	.word	643
	.byte	1,2,2,35,0,11
	.byte	'REDC',0,1
	.word	643
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	660
	.byte	6,3,2,35,0,11
	.byte	'TWRQC',0,1
	.word	643
	.byte	1,2,2,35,1,11
	.byte	'THRQC',0,1
	.word	643
	.byte	1,1,2,35,1,11
	.byte	'TRRQC',0,1
	.word	643
	.byte	1,0,2,35,1,11
	.byte	'PEC',0,1
	.word	643
	.byte	1,7,2,35,2,11
	.byte	'TCC',0,1
	.word	643
	.byte	1,6,2,35,2,11
	.byte	'FEC',0,1
	.word	643
	.byte	1,5,2,35,2,11
	.byte	'HTC',0,1
	.word	643
	.byte	1,4,2,35,2,11
	.byte	'RTC',0,1
	.word	643
	.byte	1,3,2,35,2,11
	.byte	'BDC',0,1
	.word	643
	.byte	1,2,2,35,2,11
	.byte	'LPC',0,1
	.word	643
	.byte	1,1,2,35,2,11
	.byte	'LAC',0,1
	.word	643
	.byte	1,0,2,35,2,11
	.byte	'LCC',0,1
	.word	643
	.byte	1,7,2,35,3,11
	.byte	'CEC',0,1
	.word	643
	.byte	1,6,2,35,3,11
	.byte	'RFOC',0,1
	.word	643
	.byte	1,5,2,35,3,11
	.byte	'RFUC',0,1
	.word	643
	.byte	1,4,2,35,3,11
	.byte	'RFLC',0,1
	.word	643
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	643
	.byte	1,2,2,35,3,11
	.byte	'TFOC',0,1
	.word	643
	.byte	1,1,2,35,3,11
	.byte	'TFLC',0,1
	.word	643
	.byte	1,0,2,35,3,0,12,13,239,3,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14677
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_FLAGSENABLE_Bits',0,13,212,1,16,4,11
	.byte	'THE',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'TRE',0,1
	.word	643
	.byte	1,6,2,35,0,11
	.byte	'RHE',0,1
	.word	643
	.byte	1,5,2,35,0,11
	.byte	'RRE',0,1
	.word	643
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	643
	.byte	1,3,2,35,0,11
	.byte	'FEDE',0,1
	.word	643
	.byte	1,2,2,35,0,11
	.byte	'REDE',0,1
	.word	643
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	660
	.byte	9,0,2,35,0,11
	.byte	'PEE',0,1
	.word	643
	.byte	1,7,2,35,2,11
	.byte	'TCE',0,1
	.word	643
	.byte	1,6,2,35,2,11
	.byte	'FEE',0,1
	.word	643
	.byte	1,5,2,35,2,11
	.byte	'HTE',0,1
	.word	643
	.byte	1,4,2,35,2,11
	.byte	'RTE',0,1
	.word	643
	.byte	1,3,2,35,2,11
	.byte	'BDE',0,1
	.word	643
	.byte	1,2,2,35,2,11
	.byte	'LPE',0,1
	.word	643
	.byte	1,1,2,35,2,11
	.byte	'ABE',0,1
	.word	643
	.byte	1,0,2,35,2,11
	.byte	'LCE',0,1
	.word	643
	.byte	1,7,2,35,3,11
	.byte	'CEE',0,1
	.word	643
	.byte	1,6,2,35,3,11
	.byte	'RFOE',0,1
	.word	643
	.byte	1,5,2,35,3,11
	.byte	'RFUE',0,1
	.word	643
	.byte	1,4,2,35,3,11
	.byte	'RFLE',0,1
	.word	643
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	643
	.byte	1,2,2,35,3,11
	.byte	'TFOE',0,1
	.word	643
	.byte	1,1,2,35,3,11
	.byte	'TFLE',0,1
	.word	643
	.byte	1,0,2,35,3,0,12,13,247,3,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15192
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_TXDATA_Bits',0,13,143,3,16,4,11
	.byte	'DATA',0,4
	.word	468
	.byte	32,0,2,35,0,0,12,13,239,4,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15657
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_RXDATA_Bits',0,13,245,2,16,4,11
	.byte	'DATA',0,4
	.word	468
	.byte	32,0,2,35,0,0,12,13,215,4,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15744
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_CSR_Bits',0,13,128,1,16,4,11
	.byte	'CLKSEL',0,1
	.word	643
	.byte	5,3,2,35,0,11
	.byte	'reserved_5',0,4
	.word	468
	.byte	26,1,2,35,0,11
	.byte	'CON',0,1
	.word	643
	.byte	1,0,2,35,3,0,12,13,215,3,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15831
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_RXDATAD_Bits',0,13,251,2,16,4,11
	.byte	'DATA',0,4
	.word	468
	.byte	32,0,2,35,0,0,12,13,223,4,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15954
	.byte	4,2,35,0,0,15,148,1
	.word	643
	.byte	16,147,1,0,10
	.byte	'_Ifx_ASCLIN_OCS_Bits',0,13,235,2,16,4,11
	.byte	'reserved_0',0,4
	.word	468
	.byte	24,8,2,35,0,11
	.byte	'SUS',0,1
	.word	643
	.byte	4,4,2,35,3,11
	.byte	'SUS_P',0,1
	.word	643
	.byte	1,3,2,35,3,11
	.byte	'SUSSTA',0,1
	.word	643
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	643
	.byte	2,0,2,35,3,0,12,13,207,4,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16053
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_KRSTCLR_Bits',0,13,202,2,16,4,11
	.byte	'CLR',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	468
	.byte	31,0,2,35,0,0,12,13,175,4,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16216
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_KRST1_Bits',0,13,195,2,16,4,11
	.byte	'RST',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	468
	.byte	31,0,2,35,0,0,12,13,167,4,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16325
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_KRST0_Bits',0,13,187,2,16,4,11
	.byte	'RST',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'RSTSTAT',0,1
	.word	643
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	468
	.byte	30,0,2,35,0,0,12,13,159,4,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16432
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_ACCEN1_Bits',0,13,82,16,4,11
	.byte	'reserved_0',0,4
	.word	468
	.byte	32,0,2,35,0,0,12,13,175,3,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16558
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_ACCEN0_Bits',0,13,45,16,4,11
	.byte	'EN0',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	643
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	643
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	643
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	643
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	643
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	643
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	643
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	643
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	643
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	643
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	643
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	643
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	643
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	643
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	643
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	643
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	643
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	643
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	643
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	643
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	643
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	643
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	643
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	643
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	643
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	643
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	643
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	643
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	643
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	643
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	643
	.byte	1,0,2,35,3,0,12,13,167,3,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16650
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN',0,13,153,5,25,128,2,13
	.byte	'CLC',0
	.word	11256
	.byte	4,2,35,0,13
	.byte	'IOCR',0
	.word	11551
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	11676
	.byte	4,2,35,8,13
	.byte	'TXFIFOCON',0
	.word	11901
	.byte	4,2,35,12,13
	.byte	'RXFIFOCON',0
	.word	12142
	.byte	4,2,35,16,13
	.byte	'BITCON',0
	.word	12363
	.byte	4,2,35,20,13
	.byte	'FRAMECON',0
	.word	12628
	.byte	4,2,35,24,13
	.byte	'DATCON',0
	.word	12825
	.byte	4,2,35,28,13
	.byte	'BRG',0
	.word	12982
	.byte	4,2,35,32,13
	.byte	'BRD',0
	.word	13136
	.byte	4,2,35,36,13
	.byte	'LIN',0
	.word	13673
	.byte	12,2,35,40,13
	.byte	'FLAGS',0
	.word	14124
	.byte	4,2,35,52,13
	.byte	'FLAGSSET',0
	.word	14637
	.byte	4,2,35,56,13
	.byte	'FLAGSCLEAR',0
	.word	15152
	.byte	4,2,35,60,13
	.byte	'FLAGSENABLE',0
	.word	15617
	.byte	4,2,35,64,13
	.byte	'TXDATA',0
	.word	15704
	.byte	4,2,35,68,13
	.byte	'RXDATA',0
	.word	15791
	.byte	4,2,35,72,13
	.byte	'CSR',0
	.word	15914
	.byte	4,2,35,76,13
	.byte	'RXDATAD',0
	.word	16002
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	16042
	.byte	148,1,2,35,84,13
	.byte	'OCS',0
	.word	16176
	.byte	4,3,35,232,1,13
	.byte	'KRSTCLR',0
	.word	16285
	.byte	4,3,35,236,1,13
	.byte	'KRST1',0
	.word	16392
	.byte	4,3,35,240,1,13
	.byte	'KRST0',0
	.word	16518
	.byte	4,3,35,244,1,13
	.byte	'ACCEN1',0
	.word	16610
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	17182
	.byte	4,3,35,252,1,0,14
	.word	17222
	.byte	3
	.word	17664
	.byte	4
	.byte	'IfxAsclin_enableCts',0,3,12,228,13,17,1,1,5
	.byte	'asclin',0,12,228,13,49
	.word	17669
	.byte	5
	.byte	'enable',0,12,228,13,65
	.word	643
	.byte	6,0,17,12,123,9,1,18
	.byte	'IfxAsclin_CtsInputSelect_0',0,0,18
	.byte	'IfxAsclin_CtsInputSelect_1',0,1,18
	.byte	'IfxAsclin_CtsInputSelect_2',0,2,18
	.byte	'IfxAsclin_CtsInputSelect_3',0,3,0,4
	.byte	'IfxAsclin_setCtsInput',0,3,12,169,17,17,1,1,5
	.byte	'asclin',0,12,169,17,51
	.word	17669
	.byte	5
	.byte	'ctsi',0,12,169,17,84
	.word	17736
	.byte	6,0,17,12,181,2,9,1,18
	.byte	'IfxAsclin_RxInputSelect_0',0,0,18
	.byte	'IfxAsclin_RxInputSelect_1',0,1,18
	.byte	'IfxAsclin_RxInputSelect_2',0,2,18
	.byte	'IfxAsclin_RxInputSelect_3',0,3,18
	.byte	'IfxAsclin_RxInputSelect_4',0,4,18
	.byte	'IfxAsclin_RxInputSelect_5',0,5,18
	.byte	'IfxAsclin_RxInputSelect_6',0,6,18
	.byte	'IfxAsclin_RxInputSelect_7',0,7,0,4
	.byte	'IfxAsclin_setRxInput',0,3,12,191,18,17,1,1,5
	.byte	'asclin',0,12,191,18,50
	.word	17669
	.byte	5
	.byte	'alti',0,12,191,18,82
	.word	17920
	.byte	6,0,7
	.byte	'short int',0,2,5,7
	.byte	'long int',0,4,5,20,14,60,9,12,13
	.byte	'count',0
	.word	18212
	.byte	2,2,35,0,13
	.byte	'readerWaitx',0
	.word	18225
	.byte	4,2,35,2,13
	.byte	'writerWaitx',0
	.word	18225
	.byte	4,2,35,6,13
	.byte	'maxcount',0
	.word	18212
	.byte	2,2,35,10,0,14
	.word	643
	.byte	14
	.word	643
	.byte	10
	.byte	'_Fifo',0,14,73,16,28,13
	.byte	'buffer',0
	.word	382
	.byte	4,2,35,0,13
	.byte	'shared',0
	.word	18237
	.byte	12,2,35,4,13
	.byte	'startIndex',0
	.word	18212
	.byte	2,2,35,16,13
	.byte	'endIndex',0
	.word	18212
	.byte	2,2,35,18,13
	.byte	'size',0
	.word	18212
	.byte	2,2,35,20,13
	.byte	'elementSize',0
	.word	18212
	.byte	2,2,35,22,13
	.byte	'eventReader',0
	.word	18318
	.byte	1,2,35,24,13
	.byte	'eventWriter',0
	.word	18323
	.byte	1,2,35,25,0,3
	.word	18328
	.byte	8
	.byte	'Ifx_Fifo_readCount',0,3,14,206,1,22
	.word	18212
	.byte	1,1,5
	.byte	'fifo',0,14,206,1,51
	.word	18487
	.byte	6,0,10
	.byte	'_Ifx_STM_CLC_Bits',0,16,100,16,4,11
	.byte	'DISR',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'DISS',0,1
	.word	643
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	643
	.byte	1,5,2,35,0,11
	.byte	'EDIS',0,1
	.word	643
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	468
	.byte	28,0,2,35,0,0,12,16,149,2,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18539
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_ID_Bits',0,16,142,1,16,4,11
	.byte	'MODREV',0,1
	.word	643
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	643
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	660
	.byte	16,0,2,35,2,0,12,16,181,2,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18695
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM0_Bits',0,16,192,1,16,4,11
	.byte	'STM31_0',0,4
	.word	468
	.byte	32,0,2,35,0,0,12,16,229,2,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18817
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM1_Bits',0,16,204,1,16,4,11
	.byte	'STM35_4',0,4
	.word	468
	.byte	32,0,2,35,0,0,12,16,245,2,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18902
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM2_Bits',0,16,210,1,16,4,11
	.byte	'STM39_8',0,4
	.word	468
	.byte	32,0,2,35,0,0,12,16,253,2,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18987
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM3_Bits',0,16,216,1,16,4,11
	.byte	'STM43_12',0,4
	.word	468
	.byte	32,0,2,35,0,0,12,16,133,3,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19072
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM4_Bits',0,16,222,1,16,4,11
	.byte	'STM47_16',0,4
	.word	468
	.byte	32,0,2,35,0,0,12,16,141,3,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19158
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM5_Bits',0,16,228,1,16,4,11
	.byte	'STM51_20',0,4
	.word	468
	.byte	32,0,2,35,0,0,12,16,149,3,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19244
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM6_Bits',0,16,234,1,16,4,11
	.byte	'STM63_32',0,4
	.word	468
	.byte	32,0,2,35,0,0,12,16,157,3,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19330
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_CAP_Bits',0,16,88,16,4,11
	.byte	'STMCAP63_32',0,4
	.word	468
	.byte	32,0,2,35,0,0,12,16,133,2,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19416
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_CMP_Bits',0,16,123,16,4,11
	.byte	'CMPVAL',0,4
	.word	468
	.byte	32,0,2,35,0,0,12,16,165,2,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19503
	.byte	4,2,35,0,0,15,8
	.word	19545
	.byte	16,1,0,10
	.byte	'_Ifx_STM_CMCON_Bits',0,16,110,16,4,11
	.byte	'MSIZE0',0,1
	.word	643
	.byte	5,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	643
	.byte	3,0,2,35,0,11
	.byte	'MSTART0',0,1
	.word	643
	.byte	5,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	643
	.byte	3,0,2,35,1,11
	.byte	'MSIZE1',0,1
	.word	643
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	643
	.byte	3,0,2,35,2,11
	.byte	'MSTART1',0,1
	.word	643
	.byte	5,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	643
	.byte	3,0,2,35,3,0,12,16,157,2,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19594
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_ICR_Bits',0,16,129,1,16,4,11
	.byte	'CMP0EN',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'CMP0IR',0,1
	.word	643
	.byte	1,6,2,35,0,11
	.byte	'CMP0OS',0,1
	.word	643
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	643
	.byte	1,4,2,35,0,11
	.byte	'CMP1EN',0,1
	.word	643
	.byte	1,3,2,35,0,11
	.byte	'CMP1IR',0,1
	.word	643
	.byte	1,2,2,35,0,11
	.byte	'CMP1OS',0,1
	.word	643
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,4
	.word	468
	.byte	25,0,2,35,0,0,12,16,173,2,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19825
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_ISCR_Bits',0,16,150,1,16,4,11
	.byte	'CMP0IRR',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'CMP0IRS',0,1
	.word	643
	.byte	1,6,2,35,0,11
	.byte	'CMP1IRR',0,1
	.word	643
	.byte	1,5,2,35,0,11
	.byte	'CMP1IRS',0,1
	.word	643
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	468
	.byte	28,0,2,35,0,0,12,16,189,2,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20042
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM0SV_Bits',0,16,198,1,16,4,11
	.byte	'STM31_0',0,4
	.word	468
	.byte	32,0,2,35,0,0,12,16,237,2,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20206
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_CAPSV_Bits',0,16,94,16,4,11
	.byte	'STMCAP63_32',0,4
	.word	468
	.byte	32,0,2,35,0,0,12,16,141,2,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20293
	.byte	4,2,35,0,0,15,144,1
	.word	643
	.byte	16,143,1,0,10
	.byte	'_Ifx_STM_OCS_Bits',0,16,182,1,16,4,11
	.byte	'reserved_0',0,4
	.word	468
	.byte	24,8,2,35,0,11
	.byte	'SUS',0,1
	.word	643
	.byte	4,4,2,35,3,11
	.byte	'SUS_P',0,1
	.word	643
	.byte	1,3,2,35,3,11
	.byte	'SUSSTA',0,1
	.word	643
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	643
	.byte	2,0,2,35,3,0,12,16,221,2,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20393
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_KRSTCLR_Bits',0,16,175,1,16,4,11
	.byte	'CLR',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	468
	.byte	31,0,2,35,0,0,12,16,213,2,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20553
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_KRST1_Bits',0,16,168,1,16,4,11
	.byte	'RST',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	468
	.byte	31,0,2,35,0,0,12,16,205,2,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20659
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_KRST0_Bits',0,16,160,1,16,4,11
	.byte	'RST',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'RSTSTAT',0,1
	.word	643
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	468
	.byte	30,0,2,35,0,0,12,16,197,2,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20763
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_ACCEN1_Bits',0,16,82,16,4,11
	.byte	'reserved_0',0,4
	.word	468
	.byte	32,0,2,35,0,0,12,16,253,1,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20886
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_ACCEN0_Bits',0,16,45,16,4,11
	.byte	'EN0',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	643
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	643
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	643
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	643
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	643
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	643
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	643
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	643
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	643
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	643
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	643
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	643
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	643
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	643
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	643
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	643
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	643
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	643
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	643
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	643
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	643
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	643
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	643
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	643
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	643
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	643
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	643
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	643
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	643
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	643
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	643
	.byte	1,0,2,35,3,0,12,16,245,1,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20975
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM',0,16,173,3,25,128,2,13
	.byte	'CLC',0
	.word	18655
	.byte	4,2,35,0,13
	.byte	'reserved_4',0
	.word	2641
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	18777
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	2641
	.byte	4,2,35,12,13
	.byte	'TIM0',0
	.word	18862
	.byte	4,2,35,16,13
	.byte	'TIM1',0
	.word	18947
	.byte	4,2,35,20,13
	.byte	'TIM2',0
	.word	19032
	.byte	4,2,35,24,13
	.byte	'TIM3',0
	.word	19118
	.byte	4,2,35,28,13
	.byte	'TIM4',0
	.word	19204
	.byte	4,2,35,32,13
	.byte	'TIM5',0
	.word	19290
	.byte	4,2,35,36,13
	.byte	'TIM6',0
	.word	19376
	.byte	4,2,35,40,13
	.byte	'CAP',0
	.word	19463
	.byte	4,2,35,44,13
	.byte	'CMP',0
	.word	19585
	.byte	8,2,35,48,13
	.byte	'CMCON',0
	.word	19785
	.byte	4,2,35,56,13
	.byte	'ICR',0
	.word	20002
	.byte	4,2,35,60,13
	.byte	'ISCR',0
	.word	20166
	.byte	4,2,35,64,13
	.byte	'reserved_44',0
	.word	4800
	.byte	12,2,35,68,13
	.byte	'TIM0SV',0
	.word	20253
	.byte	4,2,35,80,13
	.byte	'CAPSV',0
	.word	20342
	.byte	4,2,35,84,13
	.byte	'reserved_58',0
	.word	20382
	.byte	144,1,2,35,88,13
	.byte	'OCS',0
	.word	20513
	.byte	4,3,35,232,1,13
	.byte	'KRSTCLR',0
	.word	20619
	.byte	4,3,35,236,1,13
	.byte	'KRST1',0
	.word	20723
	.byte	4,3,35,240,1,13
	.byte	'KRST0',0
	.word	20846
	.byte	4,3,35,244,1,13
	.byte	'ACCEN1',0
	.word	20935
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	21504
	.byte	4,3,35,252,1,0,14
	.word	21544
	.byte	3
	.word	21964
	.byte	8
	.byte	'IfxStm_get',0,3,15,162,4,19
	.word	350
	.byte	1,1,5
	.byte	'stm',0,15,162,4,39
	.word	21969
	.byte	6,0,8
	.byte	'IfxStm_getFrequency',0,3,15,179,4,20
	.word	264
	.byte	1,1,5
	.byte	'stm',0,15,179,4,49
	.word	21969
	.byte	19,6,6,0,0,8
	.byte	'IfxStm_getLower',0,3,15,190,4,19
	.word	10866
	.byte	1,1,5
	.byte	'stm',0,15,190,4,44
	.word	21969
	.byte	6,0,8
	.byte	'disableInterrupts',0,3,17,108,20
	.word	643
	.byte	1,1,19,6,0,0,4
	.byte	'restoreInterrupts',0,3,17,142,1,17,1,1,5
	.byte	'enabled',0,17,142,1,43
	.word	643
	.byte	19,6,0,0,7
	.byte	'long long int',0,8,5,8
	.byte	'getDeadLine',0,3,17,164,2,25
	.word	22185
	.byte	1,1,5
	.byte	'timeout',0,17,164,2,50
	.word	22185
	.byte	19,6,0,0,8
	.byte	'isDeadLine',0,3,17,211,2,20
	.word	643
	.byte	1,1,5
	.byte	'deadLine',0,17,211,2,44
	.word	22185
	.byte	19,6,0,0,8
	.byte	'now',0,3,17,221,1,25
	.word	22185
	.byte	1,1,19,6,6,6,0,0,8
	.byte	'nowWithoutCriticalSection',0,3,17,240,1,25
	.word	22185
	.byte	1,1,19,6,0,0,7
	.byte	'char',0,1,6,3
	.word	22356
	.byte	21
	.byte	'debug_assert_handler',0,18,112,9,1,1,1,1,5
	.byte	'pass',0,18,112,47
	.word	643
	.byte	5
	.byte	'file',0,18,112,59
	.word	22364
	.byte	5
	.byte	'line',0,18,112,69
	.word	484
	.byte	0,22
	.word	212
	.byte	23
	.word	238
	.byte	6,0,22
	.word	273
	.byte	23
	.word	305
	.byte	6,0,22
	.word	318
	.byte	6,0,22
	.word	387
	.byte	23
	.word	406
	.byte	6,0,22
	.word	422
	.byte	23
	.word	437
	.byte	23
	.word	451
	.byte	6,0,22
	.word	1230
	.byte	23
	.word	1270
	.byte	23
	.word	1288
	.byte	6,0,22
	.word	1308
	.byte	23
	.word	1351
	.byte	6,0,22
	.word	1371
	.byte	23
	.word	1409
	.byte	23
	.word	1427
	.byte	6,0,22
	.word	1447
	.byte	23
	.word	1488
	.byte	6,0,22
	.word	1508
	.byte	23
	.word	1559
	.byte	6,0,22
	.word	1579
	.byte	6,0,22
	.word	9518
	.byte	23
	.word	9550
	.byte	23
	.word	9564
	.byte	23
	.word	9582
	.byte	6,0,22
	.word	9885
	.byte	23
	.word	9918
	.byte	23
	.word	9932
	.byte	23
	.word	9950
	.byte	23
	.word	9964
	.byte	6,0,22
	.word	10084
	.byte	23
	.word	10112
	.byte	23
	.word	10126
	.byte	23
	.word	10144
	.byte	6,0,17,19,42,9,2,18
	.byte	'P00_0',0,0,18
	.byte	'P00_1',0,1,18
	.byte	'P00_2',0,2,18
	.byte	'P00_3',0,3,18
	.byte	'P00_4',0,4,18
	.byte	'P00_5',0,5,18
	.byte	'P00_6',0,6,18
	.byte	'P00_7',0,7,18
	.byte	'P00_8',0,8,18
	.byte	'P00_9',0,9,18
	.byte	'P00_10',0,10,18
	.byte	'P00_11',0,11,18
	.byte	'P00_12',0,12,18
	.byte	'P00_13',0,13,18
	.byte	'P00_14',0,14,18
	.byte	'P00_15',0,15,18
	.byte	'P02_0',0,192,0,18
	.byte	'P02_1',0,193,0,18
	.byte	'P02_2',0,194,0,18
	.byte	'P02_3',0,195,0,18
	.byte	'P02_4',0,196,0,18
	.byte	'P02_5',0,197,0,18
	.byte	'P02_6',0,198,0,18
	.byte	'P02_7',0,199,0,18
	.byte	'P02_8',0,200,0,18
	.byte	'P02_9',0,201,0,18
	.byte	'P02_10',0,202,0,18
	.byte	'P02_11',0,203,0,18
	.byte	'P02_12',0,204,0,18
	.byte	'P02_13',0,205,0,18
	.byte	'P02_14',0,206,0,18
	.byte	'P02_15',0,207,0,18
	.byte	'P10_0',0,192,2,18
	.byte	'P10_1',0,193,2,18
	.byte	'P10_2',0,194,2,18
	.byte	'P10_3',0,195,2,18
	.byte	'P10_4',0,196,2,18
	.byte	'P10_5',0,197,2,18
	.byte	'P10_6',0,198,2,18
	.byte	'P10_7',0,199,2,18
	.byte	'P10_8',0,200,2,18
	.byte	'P10_9',0,201,2,18
	.byte	'P10_10',0,202,2,18
	.byte	'P10_11',0,203,2,18
	.byte	'P10_12',0,204,2,18
	.byte	'P10_13',0,205,2,18
	.byte	'P10_14',0,206,2,18
	.byte	'P10_15',0,207,2,18
	.byte	'P11_0',0,224,2,18
	.byte	'P11_1',0,225,2,18
	.byte	'P11_2',0,226,2,18
	.byte	'P11_3',0,227,2,18
	.byte	'P11_4',0,228,2,18
	.byte	'P11_5',0,229,2,18
	.byte	'P11_6',0,230,2,18
	.byte	'P11_7',0,231,2,18
	.byte	'P11_8',0,232,2,18
	.byte	'P11_9',0,233,2,18
	.byte	'P11_10',0,234,2,18
	.byte	'P11_11',0,235,2,18
	.byte	'P11_12',0,236,2,18
	.byte	'P11_13',0,237,2,18
	.byte	'P11_14',0,238,2,18
	.byte	'P11_15',0,239,2,18
	.byte	'P13_0',0,160,3,18
	.byte	'P13_1',0,161,3,18
	.byte	'P13_2',0,162,3,18
	.byte	'P13_3',0,163,3,18
	.byte	'P13_4',0,164,3,18
	.byte	'P13_5',0,165,3,18
	.byte	'P13_6',0,166,3,18
	.byte	'P13_7',0,167,3,18
	.byte	'P13_8',0,168,3,18
	.byte	'P13_9',0,169,3,18
	.byte	'P13_10',0,170,3,18
	.byte	'P13_11',0,171,3,18
	.byte	'P13_12',0,172,3,18
	.byte	'P13_13',0,173,3,18
	.byte	'P13_14',0,174,3,18
	.byte	'P13_15',0,175,3,18
	.byte	'P14_0',0,192,3,18
	.byte	'P14_1',0,193,3,18
	.byte	'P14_2',0,194,3,18
	.byte	'P14_3',0,195,3,18
	.byte	'P14_4',0,196,3,18
	.byte	'P14_5',0,197,3,18
	.byte	'P14_6',0,198,3,18
	.byte	'P14_7',0,199,3,18
	.byte	'P14_8',0,200,3,18
	.byte	'P14_9',0,201,3,18
	.byte	'P14_10',0,202,3,18
	.byte	'P14_11',0,203,3,18
	.byte	'P14_12',0,204,3,18
	.byte	'P14_13',0,205,3,18
	.byte	'P14_14',0,206,3,18
	.byte	'P14_15',0,207,3,18
	.byte	'P15_0',0,224,3,18
	.byte	'P15_1',0,225,3,18
	.byte	'P15_2',0,226,3,18
	.byte	'P15_3',0,227,3,18
	.byte	'P15_4',0,228,3,18
	.byte	'P15_5',0,229,3,18
	.byte	'P15_6',0,230,3,18
	.byte	'P15_7',0,231,3,18
	.byte	'P15_8',0,232,3,18
	.byte	'P15_9',0,233,3,18
	.byte	'P15_10',0,234,3,18
	.byte	'P15_11',0,235,3,18
	.byte	'P15_12',0,236,3,18
	.byte	'P15_13',0,237,3,18
	.byte	'P15_14',0,238,3,18
	.byte	'P15_15',0,239,3,18
	.byte	'P20_0',0,128,5,18
	.byte	'P20_1',0,129,5,18
	.byte	'P20_2',0,130,5,18
	.byte	'P20_3',0,131,5,18
	.byte	'P20_4',0,132,5,18
	.byte	'P20_5',0,133,5,18
	.byte	'P20_6',0,134,5,18
	.byte	'P20_7',0,135,5,18
	.byte	'P20_8',0,136,5,18
	.byte	'P20_9',0,137,5,18
	.byte	'P20_10',0,138,5,18
	.byte	'P20_11',0,139,5,18
	.byte	'P20_12',0,140,5,18
	.byte	'P20_13',0,141,5,18
	.byte	'P20_14',0,142,5,18
	.byte	'P20_15',0,143,5,18
	.byte	'P21_0',0,160,5,18
	.byte	'P21_1',0,161,5,18
	.byte	'P21_2',0,162,5,18
	.byte	'P21_3',0,163,5,18
	.byte	'P21_4',0,164,5,18
	.byte	'P21_5',0,165,5,18
	.byte	'P21_6',0,166,5,18
	.byte	'P21_7',0,167,5,18
	.byte	'P21_8',0,168,5,18
	.byte	'P21_9',0,169,5,18
	.byte	'P21_10',0,170,5,18
	.byte	'P21_11',0,171,5,18
	.byte	'P21_12',0,172,5,18
	.byte	'P21_13',0,173,5,18
	.byte	'P21_14',0,174,5,18
	.byte	'P21_15',0,175,5,18
	.byte	'P22_0',0,192,5,18
	.byte	'P22_1',0,193,5,18
	.byte	'P22_2',0,194,5,18
	.byte	'P22_3',0,195,5,18
	.byte	'P22_4',0,196,5,18
	.byte	'P22_5',0,197,5,18
	.byte	'P22_6',0,198,5,18
	.byte	'P22_7',0,199,5,18
	.byte	'P22_8',0,200,5,18
	.byte	'P22_9',0,201,5,18
	.byte	'P22_10',0,202,5,18
	.byte	'P22_11',0,203,5,18
	.byte	'P22_12',0,204,5,18
	.byte	'P22_13',0,205,5,18
	.byte	'P22_14',0,206,5,18
	.byte	'P22_15',0,207,5,18
	.byte	'P23_0',0,224,5,18
	.byte	'P23_1',0,225,5,18
	.byte	'P23_2',0,226,5,18
	.byte	'P23_3',0,227,5,18
	.byte	'P23_4',0,228,5,18
	.byte	'P23_5',0,229,5,18
	.byte	'P23_6',0,230,5,18
	.byte	'P23_7',0,231,5,18
	.byte	'P23_8',0,232,5,18
	.byte	'P23_9',0,233,5,18
	.byte	'P23_10',0,234,5,18
	.byte	'P23_11',0,235,5,18
	.byte	'P23_12',0,236,5,18
	.byte	'P23_13',0,237,5,18
	.byte	'P23_14',0,238,5,18
	.byte	'P23_15',0,239,5,18
	.byte	'P32_0',0,128,8,18
	.byte	'P32_1',0,129,8,18
	.byte	'P32_2',0,130,8,18
	.byte	'P32_3',0,131,8,18
	.byte	'P32_4',0,132,8,18
	.byte	'P32_5',0,133,8,18
	.byte	'P32_6',0,134,8,18
	.byte	'P32_7',0,135,8,18
	.byte	'P32_8',0,136,8,18
	.byte	'P32_9',0,137,8,18
	.byte	'P32_10',0,138,8,18
	.byte	'P32_11',0,139,8,18
	.byte	'P32_12',0,140,8,18
	.byte	'P32_13',0,141,8,18
	.byte	'P32_14',0,142,8,18
	.byte	'P32_15',0,143,8,18
	.byte	'P33_0',0,160,8,18
	.byte	'P33_1',0,161,8,18
	.byte	'P33_2',0,162,8,18
	.byte	'P33_3',0,163,8,18
	.byte	'P33_4',0,164,8,18
	.byte	'P33_5',0,165,8,18
	.byte	'P33_6',0,166,8,18
	.byte	'P33_7',0,167,8,18
	.byte	'P33_8',0,168,8,18
	.byte	'P33_9',0,169,8,18
	.byte	'P33_10',0,170,8,18
	.byte	'P33_11',0,171,8,18
	.byte	'P33_12',0,172,8,18
	.byte	'P33_13',0,173,8,18
	.byte	'P33_14',0,174,8,18
	.byte	'P33_15',0,175,8,0,17,19,91,9,1,18
	.byte	'GPI',0,0,18
	.byte	'GPO',0,1,0,17,19,103,9,1,18
	.byte	'GPI_FLOATING_IN',0,0,18
	.byte	'GPI_PULL_UP',0,1,18
	.byte	'GPI_PULL_DOWN',0,2,18
	.byte	'GPO_PUSH_PULL',0,3,18
	.byte	'GPO_OPEN_DTAIN',0,4,0,21
	.byte	'gpio_init',0,19,143,1,7,1,1,1,1,5
	.byte	'pin',0,19,143,1,40
	.word	22646
	.byte	5
	.byte	'dir',0,19,143,1,59
	.word	24586
	.byte	5
	.byte	'dat',0,19,143,1,70
	.word	643
	.byte	5
	.byte	'pinconf',0,19,143,1,90
	.word	24604
	.byte	0,22
	.word	10501
	.byte	23
	.word	10529
	.byte	6,0,22
	.word	10623
	.byte	6,0,22
	.word	10657
	.byte	6,0,22
	.word	10699
	.byte	19,24
	.word	10657
	.byte	25
	.word	10697
	.byte	0,6,0,0,22
	.word	10740
	.byte	6,0,22
	.word	10774
	.byte	6,0,22
	.word	10814
	.byte	23
	.word	10847
	.byte	6,0,22
	.word	10887
	.byte	23
	.word	10928
	.byte	6,0,22
	.word	10947
	.byte	23
	.word	11002
	.byte	6,0,22
	.word	11021
	.byte	23
	.word	11061
	.byte	23
	.word	11078
	.byte	19,6,0,0,22
	.word	11097
	.byte	6,0,17,20,42,9,1,18
	.byte	'ERU_CH0_REQ0_P15_4',0,1,18
	.byte	'ERU_CH1_REQ10_P14_3',0,4,18
	.byte	'ERU_CH2_REQ7_P00_4',0,6,18
	.byte	'ERU_CH2_REQ14_P02_1',0,7,18
	.byte	'ERU_CH2_REQ2_P10_2',0,8,18
	.byte	'ERU_CH3_REQ6_P02_0',0,9,18
	.byte	'ERU_CH3_REQ3_P10_3',0,10,18
	.byte	'ERU_CH3_REQ15_P14_1',0,11,18
	.byte	'ERU_CH4_REQ13_P15_5',0,12,18
	.byte	'ERU_CH4_REQ8_P33_7',0,13,18
	.byte	'ERU_CH5_REQ1_P15_8',0,15,18
	.byte	'ERU_CH6_REQ12_P11_10',0,18,18
	.byte	'ERU_CH6_REQ9_P20_0',0,19,18
	.byte	'ERU_CH7_REQ16_P15_1',0,21,18
	.byte	'ERU_CH7_REQ11_P20_9',0,22,0,17,20,65,9,1,18
	.byte	'EXTI_TRIGGER_RISING',0,0,18
	.byte	'EXTI_TRIGGER_FALLING',0,1,18
	.byte	'EXTI_TRIGGER_BOTH',0,2,0,21
	.byte	'exti_init',0,20,83,6,1,1,1,1,5
	.byte	'eru_pin',0,20,83,44
	.word	24889
	.byte	5
	.byte	'trigger',0,20,83,71
	.word	25218
	.byte	0,17,22,105,9,1,18
	.byte	'IfxDma_ChannelId_none',0,127,18
	.byte	'IfxDma_ChannelId_0',0,0,18
	.byte	'IfxDma_ChannelId_1',0,1,18
	.byte	'IfxDma_ChannelId_2',0,2,18
	.byte	'IfxDma_ChannelId_3',0,3,18
	.byte	'IfxDma_ChannelId_4',0,4,18
	.byte	'IfxDma_ChannelId_5',0,5,18
	.byte	'IfxDma_ChannelId_6',0,6,18
	.byte	'IfxDma_ChannelId_7',0,7,18
	.byte	'IfxDma_ChannelId_8',0,8,18
	.byte	'IfxDma_ChannelId_9',0,9,18
	.byte	'IfxDma_ChannelId_10',0,10,18
	.byte	'IfxDma_ChannelId_11',0,11,18
	.byte	'IfxDma_ChannelId_12',0,12,18
	.byte	'IfxDma_ChannelId_13',0,13,18
	.byte	'IfxDma_ChannelId_14',0,14,18
	.byte	'IfxDma_ChannelId_15',0,15,18
	.byte	'IfxDma_ChannelId_16',0,16,18
	.byte	'IfxDma_ChannelId_17',0,17,18
	.byte	'IfxDma_ChannelId_18',0,18,18
	.byte	'IfxDma_ChannelId_19',0,19,18
	.byte	'IfxDma_ChannelId_20',0,20,18
	.byte	'IfxDma_ChannelId_21',0,21,18
	.byte	'IfxDma_ChannelId_22',0,22,18
	.byte	'IfxDma_ChannelId_23',0,23,18
	.byte	'IfxDma_ChannelId_24',0,24,18
	.byte	'IfxDma_ChannelId_25',0,25,18
	.byte	'IfxDma_ChannelId_26',0,26,18
	.byte	'IfxDma_ChannelId_27',0,27,18
	.byte	'IfxDma_ChannelId_28',0,28,18
	.byte	'IfxDma_ChannelId_29',0,29,18
	.byte	'IfxDma_ChannelId_30',0,30,18
	.byte	'IfxDma_ChannelId_31',0,31,18
	.byte	'IfxDma_ChannelId_32',0,32,18
	.byte	'IfxDma_ChannelId_33',0,33,18
	.byte	'IfxDma_ChannelId_34',0,34,18
	.byte	'IfxDma_ChannelId_35',0,35,18
	.byte	'IfxDma_ChannelId_36',0,36,18
	.byte	'IfxDma_ChannelId_37',0,37,18
	.byte	'IfxDma_ChannelId_38',0,38,18
	.byte	'IfxDma_ChannelId_39',0,39,18
	.byte	'IfxDma_ChannelId_40',0,40,18
	.byte	'IfxDma_ChannelId_41',0,41,18
	.byte	'IfxDma_ChannelId_42',0,42,18
	.byte	'IfxDma_ChannelId_43',0,43,18
	.byte	'IfxDma_ChannelId_44',0,44,18
	.byte	'IfxDma_ChannelId_45',0,45,18
	.byte	'IfxDma_ChannelId_46',0,46,18
	.byte	'IfxDma_ChannelId_47',0,47,0
.L65:
	.byte	3
	.word	643
	.byte	26
	.byte	'dma_init',0,21,48,7
	.word	643
	.byte	1,1,1,1,5
	.byte	'dma_ch',0,21,48,39
	.word	25340
	.byte	5
	.byte	'source_addr',0,21,48,54
	.word	26416
	.byte	5
	.byte	'destination_addr',0,21,48,74
	.word	26416
	.byte	5
	.byte	'eru_pin',0,21,48,106
	.word	24889
	.byte	5
	.byte	'trigger',0,21,48,133,1
	.word	25218
	.byte	5
	.byte	'dma_count',0,21,48,149,1
	.word	10866
	.byte	0,17,23,42,9,1,18
	.byte	'FIFO_SUCCESS',0,0,18
	.byte	'FIFO_RESET_UNDO',0,1,18
	.byte	'FIFO_CLEAR_UNDO',0,2,18
	.byte	'FIFO_BUFFER_NULL',0,3,18
	.byte	'FIFO_WRITE_UNDO',0,4,18
	.byte	'FIFO_SPACE_NO_ENOUGH',0,5,18
	.byte	'FIFO_READ_UNDO',0,6,18
	.byte	'FIFO_DATA_NO_ENOUGH',0,7,0,17,23,78,9,1,18
	.byte	'FIFO_DATA_8BIT',0,0,18
	.byte	'FIFO_DATA_16BIT',0,1,18
	.byte	'FIFO_DATA_32BIT',0,2,0
.L84:
	.byte	20,23,85,9,24,13
	.byte	'execution',0
	.word	643
	.byte	1,2,35,0,13
	.byte	'type',0
	.word	26711
	.byte	1,2,35,1,13
	.byte	'buffer',0
	.word	382
	.byte	4,2,35,4,13
	.byte	'head',0
	.word	10866
	.byte	4,2,35,8,13
	.byte	'end',0
	.word	10866
	.byte	4,2,35,12,13
	.byte	'size',0
	.word	10866
	.byte	4,2,35,16,13
	.byte	'max',0
	.word	10866
	.byte	4,2,35,20,0,3
	.word	26770
	.byte	26
	.byte	'fifo_init',0,23,105,17
	.word	26555
	.byte	1,1,1,1,5
	.byte	'fifo',0,23,105,55
	.word	26879
	.byte	5
	.byte	'type',0,23,105,81
	.word	26711
	.byte	5
	.byte	'buffer_addr',0,23,105,93
	.word	382
	.byte	5
	.byte	'size',0,23,105,113
	.word	10866
	.byte	0,22
	.word	17674
	.byte	23
	.word	17702
	.byte	23
	.word	17718
	.byte	6,0,22
	.word	17858
	.byte	23
	.word	17888
	.byte	23
	.word	17904
	.byte	6,0,22
	.word	18151
	.byte	23
	.word	18180
	.byte	23
	.word	18196
	.byte	6,0,22
	.word	18492
	.byte	23
	.word	18523
	.byte	6,0,22
	.word	21974
	.byte	23
	.word	21997
	.byte	6,0,22
	.word	22012
	.byte	23
	.word	22044
	.byte	19,19,24
	.word	11097
	.byte	25
	.word	11135
	.byte	0,0,6,0,0,22
	.word	22062
	.byte	23
	.word	22090
	.byte	6,0,22
	.word	22105
	.byte	19,24
	.word	10699
	.byte	27
	.word	10736
	.byte	24
	.word	10657
	.byte	25
	.word	10697
	.byte	0,25
	.word	10737
	.byte	0,0,6,0,0,22
	.word	22138
	.byte	23
	.word	22164
	.byte	19,24
	.word	10814
	.byte	23
	.word	10847
	.byte	25
	.word	10864
	.byte	0,6,0,0,22
	.word	22202
	.byte	23
	.word	22226
	.byte	19,24
	.word	22292
	.byte	27
	.word	22308
	.byte	24
	.word	22105
	.byte	27
	.word	22134
	.byte	24
	.word	10699
	.byte	27
	.word	10736
	.byte	24
	.word	10657
	.byte	25
	.word	10697
	.byte	0,25
	.word	10737
	.byte	0,0,25
	.word	22135
	.byte	0,0,25
	.word	22309
	.byte	24
	.word	22138
	.byte	23
	.word	22164
	.byte	27
	.word	22181
	.byte	24
	.word	10814
	.byte	23
	.word	10847
	.byte	25
	.word	10864
	.byte	0,25
	.word	22182
	.byte	0,0,25
	.word	22310
	.byte	24
	.word	21974
	.byte	23
	.word	21997
	.byte	25
	.word	22010
	.byte	0,25
	.word	22311
	.byte	0,0,6,0,0,22
	.word	22247
	.byte	23
	.word	22270
	.byte	19,24
	.word	22292
	.byte	27
	.word	22308
	.byte	24
	.word	22105
	.byte	27
	.word	22134
	.byte	24
	.word	10699
	.byte	27
	.word	10736
	.byte	24
	.word	10657
	.byte	25
	.word	10697
	.byte	0,25
	.word	10737
	.byte	0,0,25
	.word	22135
	.byte	0,0,25
	.word	22309
	.byte	24
	.word	22138
	.byte	23
	.word	22164
	.byte	27
	.word	22181
	.byte	24
	.word	10814
	.byte	23
	.word	10847
	.byte	25
	.word	10864
	.byte	0,25
	.word	22182
	.byte	0,0,25
	.word	22310
	.byte	24
	.word	21974
	.byte	23
	.word	21997
	.byte	25
	.word	22010
	.byte	0,25
	.word	22311
	.byte	0,0,6,0,0,22
	.word	22292
	.byte	19,24
	.word	22105
	.byte	27
	.word	22134
	.byte	24
	.word	10699
	.byte	27
	.word	10736
	.byte	24
	.word	10657
	.byte	25
	.word	10697
	.byte	0,25
	.word	10737
	.byte	0,0,25
	.word	22135
	.byte	0,0,6,24
	.word	22138
	.byte	23
	.word	22164
	.byte	27
	.word	22181
	.byte	24
	.word	10814
	.byte	23
	.word	10847
	.byte	25
	.word	10864
	.byte	0,25
	.word	22182
	.byte	0,0,6,24
	.word	21974
	.byte	23
	.word	21997
	.byte	25
	.word	22010
	.byte	0,6,0,0,22
	.word	22314
	.byte	19,24
	.word	21974
	.byte	23
	.word	21997
	.byte	25
	.word	22010
	.byte	0,6,0,0
.L73:
	.byte	17,24,103,9,1,18
	.byte	'UART_0',0,0,18
	.byte	'UART_1',0,1,18
	.byte	'UART_2',0,2,18
	.byte	'UART_3',0,3,0,28
	.word	643
.L63:
	.byte	3
	.word	27604
	.byte	21
	.byte	'uart_write_buffer',0,24,119,9,1,1,1,1,5
	.byte	'uartn',0,24,119,62
	.word	27562
	.byte	5
	.byte	'buff',0,24,119,82
	.word	27609
	.byte	5
	.byte	'len',0,24,119,95
	.word	10866
	.byte	0,29
	.byte	'__wchar_t',0,25,1,1
	.word	18212
	.byte	29
	.byte	'__size_t',0,25,1,1
	.word	468
	.byte	29
	.byte	'__ptrdiff_t',0,25,1,1
	.word	484
	.byte	30,1,3
	.word	27735
	.byte	29
	.byte	'__codeptr',0,25,1,1
	.word	27737
	.byte	29
	.byte	'__intptr_t',0,25,1,1
	.word	484
	.byte	29
	.byte	'__uintptr_t',0,25,1,1
	.word	468
	.byte	29
	.byte	'_iob_flag_t',0,26,82,25
	.word	660
	.byte	29
	.byte	'boolean',0,27,101,29
	.word	643
	.byte	29
	.byte	'uint8',0,27,105,29
	.word	643
	.byte	29
	.byte	'uint16',0,27,109,29
	.word	660
	.byte	29
	.byte	'uint32',0,27,113,29
	.word	10866
	.byte	29
	.byte	'uint64',0,27,118,29
	.word	350
	.byte	29
	.byte	'sint16',0,27,126,29
	.word	18212
	.byte	29
	.byte	'sint32',0,27,131,1,29
	.word	18225
	.byte	29
	.byte	'sint64',0,27,138,1,29
	.word	22185
	.byte	29
	.byte	'float32',0,27,167,1,29
	.word	264
	.byte	29
	.byte	'pvoid',0,28,57,28
	.word	382
	.byte	29
	.byte	'Ifx_TickTime',0,28,79,28
	.word	22185
	.byte	29
	.byte	'Ifx_SizeT',0,28,92,16
	.word	18212
	.byte	29
	.byte	'Ifx_Priority',0,28,103,16
	.word	660
	.byte	17,28,130,1,9,1,18
	.byte	'Ifx_RxSel_a',0,0,18
	.byte	'Ifx_RxSel_b',0,1,18
	.byte	'Ifx_RxSel_c',0,2,18
	.byte	'Ifx_RxSel_d',0,3,18
	.byte	'Ifx_RxSel_e',0,4,18
	.byte	'Ifx_RxSel_f',0,5,18
	.byte	'Ifx_RxSel_g',0,6,18
	.byte	'Ifx_RxSel_h',0,7,0,29
	.byte	'Ifx_RxSel',0,28,140,1,3
	.word	28032
	.byte	17,28,164,1,9,1,18
	.byte	'Ifx_DataBufferMode_normal',0,0,18
	.byte	'Ifx_DataBufferMode_timeStampSingle',0,1,0,29
	.byte	'Ifx_DataBufferMode',0,28,169,1,2
	.word	28170
	.byte	7
	.byte	'char',0,1,6,29
	.byte	'int8',0,29,54,29
	.word	28270
	.byte	29
	.byte	'int16',0,29,55,29
	.word	18212
	.byte	29
	.byte	'int32',0,29,56,29
	.word	484
	.byte	29
	.byte	'int64',0,29,57,29
	.word	22185
	.byte	29
	.byte	'Ifx_P_ACCEN0_Bits',0,6,79,3
	.word	8213
	.byte	29
	.byte	'Ifx_P_ACCEN1_Bits',0,6,85,3
	.word	8126
	.byte	29
	.byte	'Ifx_P_ESR_Bits',0,6,107,3
	.word	4469
	.byte	29
	.byte	'Ifx_P_ID_Bits',0,6,115,3
	.word	2522
	.byte	29
	.byte	'Ifx_P_IN_Bits',0,6,137,1,3
	.word	3517
	.byte	29
	.byte	'Ifx_P_IOCR0_Bits',0,6,150,1,3
	.word	2650
	.byte	29
	.byte	'Ifx_P_IOCR12_Bits',0,6,163,1,3
	.word	3297
	.byte	29
	.byte	'Ifx_P_IOCR4_Bits',0,6,176,1,3
	.word	2865
	.byte	29
	.byte	'Ifx_P_IOCR8_Bits',0,6,189,1,3
	.word	3080
	.byte	29
	.byte	'Ifx_P_LPCR0_Bits',0,6,197,1,3
	.word	7485
	.byte	29
	.byte	'Ifx_P_LPCR1_Bits',0,6,205,1,3
	.word	7609
	.byte	29
	.byte	'Ifx_P_LPCR1_P21_Bits',0,6,215,1,3
	.word	7693
	.byte	29
	.byte	'Ifx_P_LPCR2_Bits',0,6,229,1,3
	.word	7873
	.byte	29
	.byte	'Ifx_P_OMCR0_Bits',0,6,240,1,3
	.word	6124
	.byte	29
	.byte	'Ifx_P_OMCR12_Bits',0,6,250,1,3
	.word	6648
	.byte	29
	.byte	'Ifx_P_OMCR4_Bits',0,6,133,2,3
	.word	6298
	.byte	29
	.byte	'Ifx_P_OMCR8_Bits',0,6,144,2,3
	.word	6472
	.byte	29
	.byte	'Ifx_P_OMCR_Bits',0,6,166,2,3
	.word	7137
	.byte	29
	.byte	'Ifx_P_OMR_Bits',0,6,203,2,3
	.word	1951
	.byte	29
	.byte	'Ifx_P_OMSR0_Bits',0,6,213,2,3
	.word	5461
	.byte	29
	.byte	'Ifx_P_OMSR12_Bits',0,6,224,2,3
	.word	5949
	.byte	29
	.byte	'Ifx_P_OMSR4_Bits',0,6,235,2,3
	.word	5608
	.byte	29
	.byte	'Ifx_P_OMSR8_Bits',0,6,246,2,3
	.word	5777
	.byte	29
	.byte	'Ifx_P_OMSR_Bits',0,6,140,3,3
	.word	6804
	.byte	29
	.byte	'Ifx_P_OUT_Bits',0,6,162,3,3
	.word	1635
	.byte	29
	.byte	'Ifx_P_PCSR_Bits',0,6,180,3,3
	.word	5175
	.byte	29
	.byte	'Ifx_P_PDISC_Bits',0,6,202,3,3
	.word	4809
	.byte	29
	.byte	'Ifx_P_PDR0_Bits',0,6,223,3,3
	.word	3840
	.byte	29
	.byte	'Ifx_P_PDR1_Bits',0,6,244,3,3
	.word	4144
	.byte	29
	.byte	'Ifx_P_ACCEN0',0,6,129,4,3
	.word	8740
	.byte	29
	.byte	'Ifx_P_ACCEN1',0,6,137,4,3
	.word	8173
	.byte	29
	.byte	'Ifx_P_ESR',0,6,145,4,3
	.word	4760
	.byte	29
	.byte	'Ifx_P_ID',0,6,153,4,3
	.word	2601
	.byte	29
	.byte	'Ifx_P_IN',0,6,161,4,3
	.word	3791
	.byte	29
	.byte	'Ifx_P_IOCR0',0,6,169,4,3
	.word	2825
	.byte	29
	.byte	'Ifx_P_IOCR12',0,6,177,4,3
	.word	3477
	.byte	29
	.byte	'Ifx_P_IOCR4',0,6,185,4,3
	.word	3040
	.byte	29
	.byte	'Ifx_P_IOCR8',0,6,193,4,3
	.word	3257
	.byte	29
	.byte	'Ifx_P_LPCR0',0,6,201,4,3
	.word	7569
	.byte	29
	.byte	'Ifx_P_LPCR1',0,6,210,4,3
	.word	7818
	.byte	29
	.byte	'Ifx_P_LPCR2',0,6,218,4,3
	.word	8077
	.byte	29
	.byte	'Ifx_P_OMCR',0,6,226,4,3
	.word	7445
	.byte	29
	.byte	'Ifx_P_OMCR0',0,6,234,4,3
	.word	6258
	.byte	29
	.byte	'Ifx_P_OMCR12',0,6,242,4,3
	.word	6764
	.byte	29
	.byte	'Ifx_P_OMCR4',0,6,250,4,3
	.word	6432
	.byte	29
	.byte	'Ifx_P_OMCR8',0,6,130,5,3
	.word	6608
	.byte	29
	.byte	'Ifx_P_OMR',0,6,138,5,3
	.word	2482
	.byte	29
	.byte	'Ifx_P_OMSR',0,6,146,5,3
	.word	7097
	.byte	29
	.byte	'Ifx_P_OMSR0',0,6,154,5,3
	.word	5568
	.byte	29
	.byte	'Ifx_P_OMSR12',0,6,162,5,3
	.word	6084
	.byte	29
	.byte	'Ifx_P_OMSR4',0,6,170,5,3
	.word	5737
	.byte	29
	.byte	'Ifx_P_OMSR8',0,6,178,5,3
	.word	5909
	.byte	29
	.byte	'Ifx_P_OUT',0,6,186,5,3
	.word	1911
	.byte	29
	.byte	'Ifx_P_PCSR',0,6,194,5,3
	.word	5421
	.byte	29
	.byte	'Ifx_P_PDISC',0,6,202,5,3
	.word	5135
	.byte	29
	.byte	'Ifx_P_PDR0',0,6,210,5,3
	.word	4104
	.byte	29
	.byte	'Ifx_P_PDR1',0,6,218,5,3
	.word	4420
	.byte	14
	.word	8780
	.byte	29
	.byte	'Ifx_P',0,6,139,6,3
	.word	29651
	.byte	17,30,236,10,9,1,18
	.byte	'IfxScu_CCUCON0_CLKSEL_fBack',0,0,18
	.byte	'IfxScu_CCUCON0_CLKSEL_fPll',0,1,0,29
	.byte	'IfxScu_CCUCON0_CLKSEL',0,30,240,10,3
	.word	29671
	.byte	17,30,250,10,9,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy16384',0,0,18
	.byte	'IfxScu_WDTCON1_IR_divBy256',0,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy64',0,2,0,29
	.byte	'IfxScu_WDTCON1_IR',0,30,255,10,3
	.word	29768
	.byte	10
	.byte	'_Ifx_SCU_ACCEN0_Bits',0,4,45,16,4,11
	.byte	'EN0',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	643
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	643
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	643
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	643
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	643
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	643
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	643
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	643
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	643
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	643
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	643
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	643
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	643
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	643
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	643
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	643
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	643
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	643
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	643
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	643
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	643
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	643
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	643
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	643
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	643
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	643
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	643
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	643
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	643
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	643
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	643
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_ACCEN0_Bits',0,4,79,3
	.word	29890
	.byte	10
	.byte	'_Ifx_SCU_ACCEN1_Bits',0,4,82,16,4,11
	.byte	'reserved_0',0,4
	.word	468
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_SCU_ACCEN1_Bits',0,4,85,3
	.word	30447
	.byte	10
	.byte	'_Ifx_SCU_ARSTDIS_Bits',0,4,88,16,4,11
	.byte	'STM0DIS',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'STM1DIS',0,1
	.word	643
	.byte	1,6,2,35,0,11
	.byte	'STM2DIS',0,1
	.word	643
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	468
	.byte	29,0,2,35,0,0,29
	.byte	'Ifx_SCU_ARSTDIS_Bits',0,4,94,3
	.word	30524
	.byte	10
	.byte	'_Ifx_SCU_CCUCON0_Bits',0,4,97,16,4,11
	.byte	'BAUD1DIV',0,1
	.word	643
	.byte	4,4,2,35,0,11
	.byte	'BAUD2DIV',0,1
	.word	643
	.byte	4,0,2,35,0,11
	.byte	'SRIDIV',0,1
	.word	643
	.byte	4,4,2,35,1,11
	.byte	'LPDIV',0,1
	.word	643
	.byte	4,0,2,35,1,11
	.byte	'SPBDIV',0,1
	.word	643
	.byte	4,4,2,35,2,11
	.byte	'FSI2DIV',0,1
	.word	643
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	643
	.byte	2,0,2,35,2,11
	.byte	'FSIDIV',0,1
	.word	643
	.byte	2,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	643
	.byte	2,4,2,35,3,11
	.byte	'CLKSEL',0,1
	.word	643
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	643
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	643
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_CCUCON0_Bits',0,4,111,3
	.word	30660
	.byte	10
	.byte	'_Ifx_SCU_CCUCON1_Bits',0,4,114,16,4,11
	.byte	'CANDIV',0,1
	.word	643
	.byte	4,4,2,35,0,11
	.byte	'ERAYDIV',0,1
	.word	643
	.byte	4,0,2,35,0,11
	.byte	'STMDIV',0,1
	.word	643
	.byte	4,4,2,35,1,11
	.byte	'GTMDIV',0,1
	.word	643
	.byte	4,0,2,35,1,11
	.byte	'ETHDIV',0,1
	.word	643
	.byte	4,4,2,35,2,11
	.byte	'ASCLINFDIV',0,1
	.word	643
	.byte	4,0,2,35,2,11
	.byte	'ASCLINSDIV',0,1
	.word	643
	.byte	4,4,2,35,3,11
	.byte	'INSEL',0,1
	.word	643
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	643
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	643
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_CCUCON1_Bits',0,4,126,3
	.word	30940
	.byte	10
	.byte	'_Ifx_SCU_CCUCON2_Bits',0,4,129,1,16,4,11
	.byte	'BBBDIV',0,1
	.word	643
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	468
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	643
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	643
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_CCUCON2_Bits',0,4,135,1,3
	.word	31178
	.byte	10
	.byte	'_Ifx_SCU_CCUCON3_Bits',0,4,138,1,16,4,11
	.byte	'PLLDIV',0,1
	.word	643
	.byte	6,2,2,35,0,11
	.byte	'PLLSEL',0,1
	.word	643
	.byte	2,0,2,35,0,11
	.byte	'PLLERAYDIV',0,1
	.word	643
	.byte	6,2,2,35,1,11
	.byte	'PLLERAYSEL',0,1
	.word	643
	.byte	2,0,2,35,1,11
	.byte	'SRIDIV',0,1
	.word	643
	.byte	6,2,2,35,2,11
	.byte	'SRISEL',0,1
	.word	643
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	643
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	643
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	643
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	643
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_CCUCON3_Bits',0,4,150,1,3
	.word	31306
	.byte	10
	.byte	'_Ifx_SCU_CCUCON4_Bits',0,4,153,1,16,4,11
	.byte	'SPBDIV',0,1
	.word	643
	.byte	6,2,2,35,0,11
	.byte	'SPBSEL',0,1
	.word	643
	.byte	2,0,2,35,0,11
	.byte	'GTMDIV',0,1
	.word	643
	.byte	6,2,2,35,1,11
	.byte	'GTMSEL',0,1
	.word	643
	.byte	2,0,2,35,1,11
	.byte	'STMDIV',0,1
	.word	643
	.byte	6,2,2,35,2,11
	.byte	'STMSEL',0,1
	.word	643
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	643
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	643
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	643
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	643
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_CCUCON4_Bits',0,4,165,1,3
	.word	31549
	.byte	10
	.byte	'_Ifx_SCU_CCUCON5_Bits',0,4,168,1,16,4,11
	.byte	'MAXDIV',0,1
	.word	643
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	468
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	643
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	643
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_CCUCON5_Bits',0,4,174,1,3
	.word	31784
	.byte	10
	.byte	'_Ifx_SCU_CCUCON6_Bits',0,4,177,1,16,4,11
	.byte	'CPU0DIV',0,1
	.word	643
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	468
	.byte	26,0,2,35,0,0,29
	.byte	'Ifx_SCU_CCUCON6_Bits',0,4,181,1,3
	.word	31912
	.byte	10
	.byte	'_Ifx_SCU_CCUCON7_Bits',0,4,184,1,16,4,11
	.byte	'CPU1DIV',0,1
	.word	643
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	468
	.byte	26,0,2,35,0,0,29
	.byte	'Ifx_SCU_CCUCON7_Bits',0,4,188,1,3
	.word	32012
	.byte	10
	.byte	'_Ifx_SCU_CHIPID_Bits',0,4,191,1,16,4,11
	.byte	'CHREV',0,1
	.word	643
	.byte	6,2,2,35,0,11
	.byte	'CHTEC',0,1
	.word	643
	.byte	2,0,2,35,0,11
	.byte	'CHID',0,1
	.word	643
	.byte	8,0,2,35,1,11
	.byte	'EEA',0,1
	.word	643
	.byte	1,7,2,35,2,11
	.byte	'UCODE',0,1
	.word	643
	.byte	7,0,2,35,2,11
	.byte	'FSIZE',0,1
	.word	643
	.byte	4,4,2,35,3,11
	.byte	'SP',0,1
	.word	643
	.byte	2,2,2,35,3,11
	.byte	'SEC',0,1
	.word	643
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	643
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_CHIPID_Bits',0,4,202,1,3
	.word	32112
	.byte	10
	.byte	'_Ifx_SCU_DTSCON_Bits',0,4,205,1,16,4,11
	.byte	'PWD',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'START',0,1
	.word	643
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	643
	.byte	2,4,2,35,0,11
	.byte	'CAL',0,4
	.word	468
	.byte	20,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	643
	.byte	7,1,2,35,3,11
	.byte	'SLCK',0,1
	.word	643
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_DTSCON_Bits',0,4,213,1,3
	.word	32320
	.byte	10
	.byte	'_Ifx_SCU_DTSLIM_Bits',0,4,216,1,16,4,11
	.byte	'LOWER',0,2
	.word	660
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	643
	.byte	5,1,2,35,1,11
	.byte	'LLU',0,1
	.word	643
	.byte	1,0,2,35,1,11
	.byte	'UPPER',0,2
	.word	660
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	643
	.byte	4,2,2,35,3,11
	.byte	'SLCK',0,1
	.word	643
	.byte	1,1,2,35,3,11
	.byte	'UOF',0,1
	.word	643
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_DTSLIM_Bits',0,4,225,1,3
	.word	32485
	.byte	10
	.byte	'_Ifx_SCU_DTSSTAT_Bits',0,4,228,1,16,4,11
	.byte	'RESULT',0,2
	.word	660
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	643
	.byte	4,2,2,35,1,11
	.byte	'RDY',0,1
	.word	643
	.byte	1,1,2,35,1,11
	.byte	'BUSY',0,1
	.word	643
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	660
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_SCU_DTSSTAT_Bits',0,4,235,1,3
	.word	32668
	.byte	10
	.byte	'_Ifx_SCU_EICR_Bits',0,4,238,1,16,4,11
	.byte	'reserved_0',0,1
	.word	643
	.byte	4,4,2,35,0,11
	.byte	'EXIS0',0,1
	.word	643
	.byte	3,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	643
	.byte	1,0,2,35,0,11
	.byte	'FEN0',0,1
	.word	643
	.byte	1,7,2,35,1,11
	.byte	'REN0',0,1
	.word	643
	.byte	1,6,2,35,1,11
	.byte	'LDEN0',0,1
	.word	643
	.byte	1,5,2,35,1,11
	.byte	'EIEN0',0,1
	.word	643
	.byte	1,4,2,35,1,11
	.byte	'INP0',0,1
	.word	643
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	468
	.byte	5,12,2,35,0,11
	.byte	'EXIS1',0,1
	.word	643
	.byte	3,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	643
	.byte	1,0,2,35,2,11
	.byte	'FEN1',0,1
	.word	643
	.byte	1,7,2,35,3,11
	.byte	'REN1',0,1
	.word	643
	.byte	1,6,2,35,3,11
	.byte	'LDEN1',0,1
	.word	643
	.byte	1,5,2,35,3,11
	.byte	'EIEN1',0,1
	.word	643
	.byte	1,4,2,35,3,11
	.byte	'INP1',0,1
	.word	643
	.byte	3,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	643
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EICR_Bits',0,4,129,2,3
	.word	32822
	.byte	10
	.byte	'_Ifx_SCU_EIFR_Bits',0,4,132,2,16,4,11
	.byte	'INTF0',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'INTF1',0,1
	.word	643
	.byte	1,6,2,35,0,11
	.byte	'INTF2',0,1
	.word	643
	.byte	1,5,2,35,0,11
	.byte	'INTF3',0,1
	.word	643
	.byte	1,4,2,35,0,11
	.byte	'INTF4',0,1
	.word	643
	.byte	1,3,2,35,0,11
	.byte	'INTF5',0,1
	.word	643
	.byte	1,2,2,35,0,11
	.byte	'INTF6',0,1
	.word	643
	.byte	1,1,2,35,0,11
	.byte	'INTF7',0,1
	.word	643
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	468
	.byte	24,0,2,35,0,0,29
	.byte	'Ifx_SCU_EIFR_Bits',0,4,143,2,3
	.word	33186
	.byte	10
	.byte	'_Ifx_SCU_EMSR_Bits',0,4,146,2,16,4,11
	.byte	'POL',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'MODE',0,1
	.word	643
	.byte	1,6,2,35,0,11
	.byte	'ENON',0,1
	.word	643
	.byte	1,5,2,35,0,11
	.byte	'PSEL',0,1
	.word	643
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	660
	.byte	12,0,2,35,0,11
	.byte	'EMSF',0,1
	.word	643
	.byte	1,7,2,35,2,11
	.byte	'SEMSF',0,1
	.word	643
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	643
	.byte	6,0,2,35,2,11
	.byte	'EMSFM',0,1
	.word	643
	.byte	2,6,2,35,3,11
	.byte	'SEMSFM',0,1
	.word	643
	.byte	2,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	643
	.byte	4,0,2,35,3,0,29
	.byte	'Ifx_SCU_EMSR_Bits',0,4,159,2,3
	.word	33397
	.byte	10
	.byte	'_Ifx_SCU_ESRCFG_Bits',0,4,162,2,16,4,11
	.byte	'reserved_0',0,1
	.word	643
	.byte	7,1,2,35,0,11
	.byte	'EDCON',0,2
	.word	660
	.byte	2,7,2,35,0,11
	.byte	'reserved_9',0,4
	.word	468
	.byte	23,0,2,35,0,0,29
	.byte	'Ifx_SCU_ESRCFG_Bits',0,4,167,2,3
	.word	33649
	.byte	10
	.byte	'_Ifx_SCU_ESROCFG_Bits',0,4,170,2,16,4,11
	.byte	'ARI',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'ARC',0,1
	.word	643
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	468
	.byte	30,0,2,35,0,0,29
	.byte	'Ifx_SCU_ESROCFG_Bits',0,4,175,2,3
	.word	33767
	.byte	10
	.byte	'_Ifx_SCU_EVR13CON_Bits',0,4,178,2,16,4,11
	.byte	'reserved_0',0,4
	.word	468
	.byte	28,4,2,35,0,11
	.byte	'EVR13OFF',0,1
	.word	643
	.byte	1,3,2,35,3,11
	.byte	'BPEVR13OFF',0,1
	.word	643
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	643
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	643
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVR13CON_Bits',0,4,185,2,3
	.word	33878
	.byte	10
	.byte	'_Ifx_SCU_EVR33CON_Bits',0,4,188,2,16,4,11
	.byte	'reserved_0',0,4
	.word	468
	.byte	28,4,2,35,0,11
	.byte	'EVR33OFF',0,1
	.word	643
	.byte	1,3,2,35,3,11
	.byte	'BPEVR33OFF',0,1
	.word	643
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	643
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	643
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVR33CON_Bits',0,4,195,2,3
	.word	34041
	.byte	10
	.byte	'_Ifx_SCU_EVRADCSTAT_Bits',0,4,198,2,16,4,11
	.byte	'ADC13V',0,1
	.word	643
	.byte	8,0,2,35,0,11
	.byte	'ADC33V',0,1
	.word	643
	.byte	8,0,2,35,1,11
	.byte	'ADCSWDV',0,1
	.word	643
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	643
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	643
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRADCSTAT_Bits',0,4,205,2,3
	.word	34204
	.byte	10
	.byte	'_Ifx_SCU_EVRDVSTAT_Bits',0,4,208,2,16,4,11
	.byte	'DVS13TRIM',0,1
	.word	643
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	643
	.byte	8,0,2,35,1,11
	.byte	'DVS33TRIM',0,1
	.word	643
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	643
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	643
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRDVSTAT_Bits',0,4,215,2,3
	.word	34362
	.byte	10
	.byte	'_Ifx_SCU_EVRMONCTRL_Bits',0,4,218,2,16,4,11
	.byte	'EVR13OVMOD',0,1
	.word	643
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	643
	.byte	2,4,2,35,0,11
	.byte	'EVR13UVMOD',0,1
	.word	643
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	643
	.byte	2,0,2,35,0,11
	.byte	'EVR33OVMOD',0,1
	.word	643
	.byte	2,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	643
	.byte	2,4,2,35,1,11
	.byte	'EVR33UVMOD',0,1
	.word	643
	.byte	2,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	643
	.byte	2,0,2,35,1,11
	.byte	'SWDOVMOD',0,1
	.word	643
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	643
	.byte	2,4,2,35,2,11
	.byte	'SWDUVMOD',0,1
	.word	643
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,2
	.word	660
	.byte	10,0,2,35,2,0,29
	.byte	'Ifx_SCU_EVRMONCTRL_Bits',0,4,232,2,3
	.word	34527
	.byte	10
	.byte	'_Ifx_SCU_EVROSCCTRL_Bits',0,4,235,2,16,4,11
	.byte	'OSCTRIM',0,2
	.word	660
	.byte	10,6,2,35,0,11
	.byte	'OSCPTAT',0,1
	.word	643
	.byte	6,0,2,35,1,11
	.byte	'OSCANASEL',0,1
	.word	643
	.byte	4,4,2,35,2,11
	.byte	'HPBGTRIM',0,2
	.word	660
	.byte	7,5,2,35,2,11
	.byte	'HPBGCLKEN',0,1
	.word	643
	.byte	1,4,2,35,3,11
	.byte	'OSC3V3',0,1
	.word	643
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	643
	.byte	2,1,2,35,3,11
	.byte	'LCK',0,1
	.word	643
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVROSCCTRL_Bits',0,4,245,2,3
	.word	34856
	.byte	10
	.byte	'_Ifx_SCU_EVROVMON_Bits',0,4,248,2,16,4,11
	.byte	'EVR13OVVAL',0,1
	.word	643
	.byte	8,0,2,35,0,11
	.byte	'EVR33OVVAL',0,1
	.word	643
	.byte	8,0,2,35,1,11
	.byte	'SWDOVVAL',0,1
	.word	643
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	643
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	643
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVROVMON_Bits',0,4,255,2,3
	.word	35077
	.byte	10
	.byte	'_Ifx_SCU_EVRRSTCON_Bits',0,4,130,3,16,4,11
	.byte	'RST13TRIM',0,1
	.word	643
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	468
	.byte	16,8,2,35,0,11
	.byte	'RST13OFF',0,1
	.word	643
	.byte	1,7,2,35,3,11
	.byte	'BPRST13OFF',0,1
	.word	643
	.byte	1,6,2,35,3,11
	.byte	'RST33OFF',0,1
	.word	643
	.byte	1,5,2,35,3,11
	.byte	'BPRST33OFF',0,1
	.word	643
	.byte	1,4,2,35,3,11
	.byte	'RSTSWDOFF',0,1
	.word	643
	.byte	1,3,2,35,3,11
	.byte	'BPRSTSWDOFF',0,1
	.word	643
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	643
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	643
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRRSTCON_Bits',0,4,142,3,3
	.word	35240
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF1_Bits',0,4,145,3,16,4,11
	.byte	'SD5P',0,1
	.word	643
	.byte	8,0,2,35,0,11
	.byte	'SD5I',0,1
	.word	643
	.byte	8,0,2,35,1,11
	.byte	'SD5D',0,1
	.word	643
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	643
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	643
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF1_Bits',0,4,152,3,3
	.word	35512
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF2_Bits',0,4,155,3,16,4,11
	.byte	'SD33P',0,1
	.word	643
	.byte	8,0,2,35,0,11
	.byte	'SD33I',0,1
	.word	643
	.byte	8,0,2,35,1,11
	.byte	'SD33D',0,1
	.word	643
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	643
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	643
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF2_Bits',0,4,162,3,3
	.word	35665
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF3_Bits',0,4,165,3,16,4,11
	.byte	'CT5REG0',0,1
	.word	643
	.byte	8,0,2,35,0,11
	.byte	'CT5REG1',0,1
	.word	643
	.byte	8,0,2,35,1,11
	.byte	'CT5REG2',0,1
	.word	643
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	643
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	643
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF3_Bits',0,4,172,3,3
	.word	35821
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF4_Bits',0,4,175,3,16,4,11
	.byte	'CT5REG3',0,1
	.word	643
	.byte	8,0,2,35,0,11
	.byte	'CT5REG4',0,1
	.word	643
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	660
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	643
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF4_Bits',0,4,181,3,3
	.word	35983
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF5_Bits',0,4,184,3,16,4,11
	.byte	'CT33REG0',0,1
	.word	643
	.byte	8,0,2,35,0,11
	.byte	'CT33REG1',0,1
	.word	643
	.byte	8,0,2,35,1,11
	.byte	'CT33REG2',0,1
	.word	643
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	643
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	643
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF5_Bits',0,4,191,3,3
	.word	36126
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF6_Bits',0,4,194,3,16,4,11
	.byte	'CT33REG3',0,1
	.word	643
	.byte	8,0,2,35,0,11
	.byte	'CT33REG4',0,1
	.word	643
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	660
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	643
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF6_Bits',0,4,200,3,3
	.word	36291
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL1_Bits',0,4,203,3,16,4,11
	.byte	'SDFREQSPRD',0,2
	.word	660
	.byte	16,0,2,35,0,11
	.byte	'SDFREQ',0,1
	.word	643
	.byte	8,0,2,35,2,11
	.byte	'SDSTEP',0,1
	.word	643
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	643
	.byte	2,2,2,35,3,11
	.byte	'SDSAMPLE',0,1
	.word	643
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	643
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRSDCTRL1_Bits',0,4,211,3,3
	.word	36436
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL2_Bits',0,4,214,3,16,4,11
	.byte	'DRVP',0,1
	.word	643
	.byte	8,0,2,35,0,11
	.byte	'SDMINMAXDC',0,1
	.word	643
	.byte	8,0,2,35,1,11
	.byte	'DRVN',0,1
	.word	643
	.byte	8,0,2,35,2,11
	.byte	'SDLUT',0,1
	.word	643
	.byte	6,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	643
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	643
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRSDCTRL2_Bits',0,4,222,3,3
	.word	36617
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL3_Bits',0,4,225,3,16,4,11
	.byte	'SDPWMPRE',0,1
	.word	643
	.byte	8,0,2,35,0,11
	.byte	'SDPID',0,1
	.word	643
	.byte	8,0,2,35,1,11
	.byte	'SDVOKLVL',0,1
	.word	643
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	643
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	643
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRSDCTRL3_Bits',0,4,232,3,3
	.word	36791
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL4_Bits',0,4,235,3,16,4,11
	.byte	'reserved_0',0,1
	.word	643
	.byte	8,0,2,35,0,11
	.byte	'SYNCDIV',0,1
	.word	643
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	468
	.byte	20,1,2,35,0,11
	.byte	'LCK',0,1
	.word	643
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRSDCTRL4_Bits',0,4,241,3,3
	.word	36951
	.byte	10
	.byte	'_Ifx_SCU_EVRSTAT_Bits',0,4,244,3,16,4,11
	.byte	'EVR13',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'OV13',0,1
	.word	643
	.byte	1,6,2,35,0,11
	.byte	'EVR33',0,1
	.word	643
	.byte	1,5,2,35,0,11
	.byte	'OV33',0,1
	.word	643
	.byte	1,4,2,35,0,11
	.byte	'OVSWD',0,1
	.word	643
	.byte	1,3,2,35,0,11
	.byte	'UV13',0,1
	.word	643
	.byte	1,2,2,35,0,11
	.byte	'UV33',0,1
	.word	643
	.byte	1,1,2,35,0,11
	.byte	'UVSWD',0,1
	.word	643
	.byte	1,0,2,35,0,11
	.byte	'EXTPASS13',0,1
	.word	643
	.byte	1,7,2,35,1,11
	.byte	'EXTPASS33',0,1
	.word	643
	.byte	1,6,2,35,1,11
	.byte	'BGPROK',0,1
	.word	643
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	468
	.byte	21,0,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSTAT_Bits',0,4,130,4,3
	.word	37095
	.byte	10
	.byte	'_Ifx_SCU_EVRTRIM_Bits',0,4,133,4,16,4,11
	.byte	'EVR13TRIM',0,1
	.word	643
	.byte	8,0,2,35,0,11
	.byte	'SDVOUTSEL',0,1
	.word	643
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	660
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	643
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRTRIM_Bits',0,4,139,4,3
	.word	37369
	.byte	10
	.byte	'_Ifx_SCU_EVRUVMON_Bits',0,4,142,4,16,4,11
	.byte	'EVR13UVVAL',0,1
	.word	643
	.byte	8,0,2,35,0,11
	.byte	'EVR33UVVAL',0,1
	.word	643
	.byte	8,0,2,35,1,11
	.byte	'SWDUVVAL',0,1
	.word	643
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	643
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	643
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRUVMON_Bits',0,4,149,4,3
	.word	37508
	.byte	10
	.byte	'_Ifx_SCU_EXTCON_Bits',0,4,152,4,16,4,11
	.byte	'EN0',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	643
	.byte	1,6,2,35,0,11
	.byte	'SEL0',0,1
	.word	643
	.byte	4,2,2,35,0,11
	.byte	'reserved_6',0,2
	.word	660
	.byte	10,0,2,35,0,11
	.byte	'EN1',0,1
	.word	643
	.byte	1,7,2,35,2,11
	.byte	'NSEL',0,1
	.word	643
	.byte	1,6,2,35,2,11
	.byte	'SEL1',0,1
	.word	643
	.byte	4,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	643
	.byte	2,0,2,35,2,11
	.byte	'DIV1',0,1
	.word	643
	.byte	8,0,2,35,3,0,29
	.byte	'Ifx_SCU_EXTCON_Bits',0,4,163,4,3
	.word	37671
	.byte	10
	.byte	'_Ifx_SCU_FDR_Bits',0,4,166,4,16,4,11
	.byte	'STEP',0,2
	.word	660
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	643
	.byte	4,2,2,35,1,11
	.byte	'DM',0,1
	.word	643
	.byte	2,0,2,35,1,11
	.byte	'RESULT',0,2
	.word	660
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	643
	.byte	5,1,2,35,3,11
	.byte	'DISCLK',0,1
	.word	643
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_FDR_Bits',0,4,174,4,3
	.word	37889
	.byte	10
	.byte	'_Ifx_SCU_FMR_Bits',0,4,177,4,16,4,11
	.byte	'FS0',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'FS1',0,1
	.word	643
	.byte	1,6,2,35,0,11
	.byte	'FS2',0,1
	.word	643
	.byte	1,5,2,35,0,11
	.byte	'FS3',0,1
	.word	643
	.byte	1,4,2,35,0,11
	.byte	'FS4',0,1
	.word	643
	.byte	1,3,2,35,0,11
	.byte	'FS5',0,1
	.word	643
	.byte	1,2,2,35,0,11
	.byte	'FS6',0,1
	.word	643
	.byte	1,1,2,35,0,11
	.byte	'FS7',0,1
	.word	643
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	643
	.byte	8,0,2,35,1,11
	.byte	'FC0',0,1
	.word	643
	.byte	1,7,2,35,2,11
	.byte	'FC1',0,1
	.word	643
	.byte	1,6,2,35,2,11
	.byte	'FC2',0,1
	.word	643
	.byte	1,5,2,35,2,11
	.byte	'FC3',0,1
	.word	643
	.byte	1,4,2,35,2,11
	.byte	'FC4',0,1
	.word	643
	.byte	1,3,2,35,2,11
	.byte	'FC5',0,1
	.word	643
	.byte	1,2,2,35,2,11
	.byte	'FC6',0,1
	.word	643
	.byte	1,1,2,35,2,11
	.byte	'FC7',0,1
	.word	643
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	643
	.byte	8,0,2,35,3,0,29
	.byte	'Ifx_SCU_FMR_Bits',0,4,197,4,3
	.word	38052
	.byte	10
	.byte	'_Ifx_SCU_ID_Bits',0,4,200,4,16,4,11
	.byte	'MODREV',0,1
	.word	643
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	643
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	660
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_SCU_ID_Bits',0,4,205,4,3
	.word	38388
	.byte	10
	.byte	'_Ifx_SCU_IGCR_Bits',0,4,208,4,16,4,11
	.byte	'IPEN00',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'IPEN01',0,1
	.word	643
	.byte	1,6,2,35,0,11
	.byte	'IPEN02',0,1
	.word	643
	.byte	1,5,2,35,0,11
	.byte	'IPEN03',0,1
	.word	643
	.byte	1,4,2,35,0,11
	.byte	'IPEN04',0,1
	.word	643
	.byte	1,3,2,35,0,11
	.byte	'IPEN05',0,1
	.word	643
	.byte	1,2,2,35,0,11
	.byte	'IPEN06',0,1
	.word	643
	.byte	1,1,2,35,0,11
	.byte	'IPEN07',0,1
	.word	643
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	643
	.byte	5,3,2,35,1,11
	.byte	'GEEN0',0,1
	.word	643
	.byte	1,2,2,35,1,11
	.byte	'IGP0',0,1
	.word	643
	.byte	2,0,2,35,1,11
	.byte	'IPEN10',0,1
	.word	643
	.byte	1,7,2,35,2,11
	.byte	'IPEN11',0,1
	.word	643
	.byte	1,6,2,35,2,11
	.byte	'IPEN12',0,1
	.word	643
	.byte	1,5,2,35,2,11
	.byte	'IPEN13',0,1
	.word	643
	.byte	1,4,2,35,2,11
	.byte	'IPEN14',0,1
	.word	643
	.byte	1,3,2,35,2,11
	.byte	'IPEN15',0,1
	.word	643
	.byte	1,2,2,35,2,11
	.byte	'IPEN16',0,1
	.word	643
	.byte	1,1,2,35,2,11
	.byte	'IPEN17',0,1
	.word	643
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	643
	.byte	5,3,2,35,3,11
	.byte	'GEEN1',0,1
	.word	643
	.byte	1,2,2,35,3,11
	.byte	'IGP1',0,1
	.word	643
	.byte	2,0,2,35,3,0,29
	.byte	'Ifx_SCU_IGCR_Bits',0,4,232,4,3
	.word	38495
	.byte	10
	.byte	'_Ifx_SCU_IN_Bits',0,4,235,4,16,4,11
	.byte	'P0',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	643
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	468
	.byte	30,0,2,35,0,0,29
	.byte	'Ifx_SCU_IN_Bits',0,4,240,4,3
	.word	38947
	.byte	10
	.byte	'_Ifx_SCU_IOCR_Bits',0,4,243,4,16,4,11
	.byte	'reserved_0',0,1
	.word	643
	.byte	4,4,2,35,0,11
	.byte	'PC0',0,1
	.word	643
	.byte	4,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	643
	.byte	4,4,2,35,1,11
	.byte	'PC1',0,1
	.word	643
	.byte	4,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	660
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_SCU_IOCR_Bits',0,4,250,4,3
	.word	39046
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL0_Bits',0,4,253,4,16,4,11
	.byte	'LBISTREQ',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'LBISTREQP',0,1
	.word	643
	.byte	1,6,2,35,0,11
	.byte	'PATTERNS',0,2
	.word	660
	.byte	14,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	660
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_SCU_LBISTCTRL0_Bits',0,4,131,5,3
	.word	39196
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL1_Bits',0,4,134,5,16,4,11
	.byte	'SEED',0,4
	.word	468
	.byte	23,9,2,35,0,11
	.byte	'reserved_23',0,1
	.word	643
	.byte	1,0,2,35,2,11
	.byte	'SPLITSH',0,1
	.word	643
	.byte	3,5,2,35,3,11
	.byte	'BODY',0,1
	.word	643
	.byte	1,4,2,35,3,11
	.byte	'LBISTFREQU',0,1
	.word	643
	.byte	4,0,2,35,3,0,29
	.byte	'Ifx_SCU_LBISTCTRL1_Bits',0,4,141,5,3
	.word	39345
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL2_Bits',0,4,144,5,16,4,11
	.byte	'SIGNATURE',0,4
	.word	468
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	643
	.byte	7,1,2,35,3,11
	.byte	'LBISTDONE',0,1
	.word	643
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_LBISTCTRL2_Bits',0,4,149,5,3
	.word	39506
	.byte	10
	.byte	'_Ifx_SCU_LCLCON_Bits',0,4,152,5,16,4,11
	.byte	'reserved_0',0,2
	.word	660
	.byte	16,0,2,35,0,11
	.byte	'LS',0,1
	.word	643
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,2
	.word	660
	.byte	14,1,2,35,2,11
	.byte	'LSEN',0,1
	.word	643
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_LCLCON_Bits',0,4,158,5,3
	.word	39636
	.byte	10
	.byte	'_Ifx_SCU_LCLTEST_Bits',0,4,161,5,16,4,11
	.byte	'LCLT0',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'LCLT1',0,1
	.word	643
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	468
	.byte	30,0,2,35,0,0,29
	.byte	'Ifx_SCU_LCLTEST_Bits',0,4,166,5,3
	.word	39768
	.byte	10
	.byte	'_Ifx_SCU_MANID_Bits',0,4,169,5,16,4,11
	.byte	'DEPT',0,1
	.word	643
	.byte	5,3,2,35,0,11
	.byte	'MANUF',0,2
	.word	660
	.byte	11,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	660
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_SCU_MANID_Bits',0,4,174,5,3
	.word	39883
	.byte	10
	.byte	'_Ifx_SCU_OMR_Bits',0,4,177,5,16,4,11
	.byte	'PS0',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	643
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	660
	.byte	14,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	643
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	643
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	660
	.byte	14,0,2,35,2,0,29
	.byte	'Ifx_SCU_OMR_Bits',0,4,185,5,3
	.word	39994
	.byte	10
	.byte	'_Ifx_SCU_OSCCON_Bits',0,4,188,5,16,4,11
	.byte	'reserved_0',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'PLLLV',0,1
	.word	643
	.byte	1,6,2,35,0,11
	.byte	'OSCRES',0,1
	.word	643
	.byte	1,5,2,35,0,11
	.byte	'GAINSEL',0,1
	.word	643
	.byte	2,3,2,35,0,11
	.byte	'MODE',0,1
	.word	643
	.byte	2,1,2,35,0,11
	.byte	'SHBY',0,1
	.word	643
	.byte	1,0,2,35,0,11
	.byte	'PLLHV',0,1
	.word	643
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	643
	.byte	1,6,2,35,1,11
	.byte	'X1D',0,1
	.word	643
	.byte	1,5,2,35,1,11
	.byte	'X1DEN',0,1
	.word	643
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	643
	.byte	4,0,2,35,1,11
	.byte	'OSCVAL',0,1
	.word	643
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	643
	.byte	2,1,2,35,2,11
	.byte	'APREN',0,1
	.word	643
	.byte	1,0,2,35,2,11
	.byte	'CAP0EN',0,1
	.word	643
	.byte	1,7,2,35,3,11
	.byte	'CAP1EN',0,1
	.word	643
	.byte	1,6,2,35,3,11
	.byte	'CAP2EN',0,1
	.word	643
	.byte	1,5,2,35,3,11
	.byte	'CAP3EN',0,1
	.word	643
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	643
	.byte	4,0,2,35,3,0,29
	.byte	'Ifx_SCU_OSCCON_Bits',0,4,209,5,3
	.word	40152
	.byte	10
	.byte	'_Ifx_SCU_OUT_Bits',0,4,212,5,16,4,11
	.byte	'P0',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	643
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	468
	.byte	30,0,2,35,0,0,29
	.byte	'Ifx_SCU_OUT_Bits',0,4,217,5,3
	.word	40564
	.byte	10
	.byte	'_Ifx_SCU_OVCCON_Bits',0,4,220,5,16,4,11
	.byte	'CSEL0',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'CSEL1',0,1
	.word	643
	.byte	1,6,2,35,0,11
	.byte	'CSEL2',0,1
	.word	643
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,2
	.word	660
	.byte	13,0,2,35,0,11
	.byte	'OVSTRT',0,1
	.word	643
	.byte	1,7,2,35,2,11
	.byte	'OVSTP',0,1
	.word	643
	.byte	1,6,2,35,2,11
	.byte	'DCINVAL',0,1
	.word	643
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	643
	.byte	5,0,2,35,2,11
	.byte	'OVCONF',0,1
	.word	643
	.byte	1,7,2,35,3,11
	.byte	'POVCONF',0,1
	.word	643
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	643
	.byte	6,0,2,35,3,0,29
	.byte	'Ifx_SCU_OVCCON_Bits',0,4,233,5,3
	.word	40665
	.byte	10
	.byte	'_Ifx_SCU_OVCENABLE_Bits',0,4,236,5,16,4,11
	.byte	'OVEN0',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'OVEN1',0,1
	.word	643
	.byte	1,6,2,35,0,11
	.byte	'OVEN2',0,1
	.word	643
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	468
	.byte	29,0,2,35,0,0,29
	.byte	'Ifx_SCU_OVCENABLE_Bits',0,4,242,5,3
	.word	40932
	.byte	10
	.byte	'_Ifx_SCU_PDISC_Bits',0,4,245,5,16,4,11
	.byte	'PDIS0',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	643
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	468
	.byte	30,0,2,35,0,0,29
	.byte	'Ifx_SCU_PDISC_Bits',0,4,250,5,3
	.word	41068
	.byte	10
	.byte	'_Ifx_SCU_PDR_Bits',0,4,253,5,16,4,11
	.byte	'PD0',0,1
	.word	643
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	643
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	643
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	643
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	468
	.byte	24,0,2,35,0,0,29
	.byte	'Ifx_SCU_PDR_Bits',0,4,132,6,3
	.word	41179
	.byte	10
	.byte	'_Ifx_SCU_PDRR_Bits',0,4,135,6,16,4,11
	.byte	'PDR0',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'PDR1',0,1
	.word	643
	.byte	1,6,2,35,0,11
	.byte	'PDR2',0,1
	.word	643
	.byte	1,5,2,35,0,11
	.byte	'PDR3',0,1
	.word	643
	.byte	1,4,2,35,0,11
	.byte	'PDR4',0,1
	.word	643
	.byte	1,3,2,35,0,11
	.byte	'PDR5',0,1
	.word	643
	.byte	1,2,2,35,0,11
	.byte	'PDR6',0,1
	.word	643
	.byte	1,1,2,35,0,11
	.byte	'PDR7',0,1
	.word	643
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	468
	.byte	24,0,2,35,0,0,29
	.byte	'Ifx_SCU_PDRR_Bits',0,4,146,6,3
	.word	41312
	.byte	10
	.byte	'_Ifx_SCU_PLLCON0_Bits',0,4,149,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	643
	.byte	1,6,2,35,0,11
	.byte	'MODEN',0,1
	.word	643
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	643
	.byte	1,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	643
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	643
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	643
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	660
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	643
	.byte	7,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	643
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	643
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	643
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	643
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	643
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	643
	.byte	4,0,2,35,3,0,29
	.byte	'Ifx_SCU_PLLCON0_Bits',0,4,166,6,3
	.word	41515
	.byte	10
	.byte	'_Ifx_SCU_PLLCON1_Bits',0,4,169,6,16,4,11
	.byte	'K2DIV',0,1
	.word	643
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	643
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	643
	.byte	7,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	643
	.byte	1,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	643
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	660
	.byte	9,0,2,35,2,0,29
	.byte	'Ifx_SCU_PLLCON1_Bits',0,4,177,6,3
	.word	41871
	.byte	10
	.byte	'_Ifx_SCU_PLLCON2_Bits',0,4,180,6,16,4,11
	.byte	'MODCFG',0,2
	.word	660
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	660
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_SCU_PLLCON2_Bits',0,4,184,6,3
	.word	42049
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON0_Bits',0,4,187,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	643
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	643
	.byte	2,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	643
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	643
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	643
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	660
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	643
	.byte	5,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	643
	.byte	2,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	643
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	643
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	643
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	643
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	643
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	643
	.byte	4,0,2,35,3,0,29
	.byte	'Ifx_SCU_PLLERAYCON0_Bits',0,4,204,6,3
	.word	42149
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON1_Bits',0,4,207,6,16,4,11
	.byte	'K2DIV',0,1
	.word	643
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	643
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	643
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	643
	.byte	4,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	643
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	660
	.byte	9,0,2,35,2,0,29
	.byte	'Ifx_SCU_PLLERAYCON1_Bits',0,4,215,6,3
	.word	42519
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYSTAT_Bits',0,4,218,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'PWDSTAT',0,1
	.word	643
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	643
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	643
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	643
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	643
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	468
	.byte	26,0,2,35,0,0,29
	.byte	'Ifx_SCU_PLLERAYSTAT_Bits',0,4,227,6,3
	.word	42705
	.byte	10
	.byte	'_Ifx_SCU_PLLSTAT_Bits',0,4,230,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	643
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	643
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	643
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	643
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	643
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	643
	.byte	1,1,2,35,0,11
	.byte	'MODRUN',0,1
	.word	643
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	468
	.byte	24,0,2,35,0,0,29
	.byte	'Ifx_SCU_PLLSTAT_Bits',0,4,241,6,3
	.word	42903
	.byte	10
	.byte	'_Ifx_SCU_PMCSR_Bits',0,4,244,6,16,4,11
	.byte	'REQSLP',0,1
	.word	643
	.byte	2,6,2,35,0,11
	.byte	'SMUSLP',0,1
	.word	643
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	643
	.byte	5,0,2,35,0,11
	.byte	'PMST',0,1
	.word	643
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	468
	.byte	21,0,2,35,0,0,29
	.byte	'Ifx_SCU_PMCSR_Bits',0,4,251,6,3
	.word	43136
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR0_Bits',0,4,254,6,16,4,11
	.byte	'reserved_0',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'ESR1WKEN',0,1
	.word	643
	.byte	1,6,2,35,0,11
	.byte	'PINAWKEN',0,1
	.word	643
	.byte	1,5,2,35,0,11
	.byte	'PINBWKEN',0,1
	.word	643
	.byte	1,4,2,35,0,11
	.byte	'ESR0DFEN',0,1
	.word	643
	.byte	1,3,2,35,0,11
	.byte	'ESR0EDCON',0,1
	.word	643
	.byte	2,1,2,35,0,11
	.byte	'ESR1DFEN',0,1
	.word	643
	.byte	1,0,2,35,0,11
	.byte	'ESR1EDCON',0,1
	.word	643
	.byte	2,6,2,35,1,11
	.byte	'PINADFEN',0,1
	.word	643
	.byte	1,5,2,35,1,11
	.byte	'PINAEDCON',0,1
	.word	643
	.byte	2,3,2,35,1,11
	.byte	'PINBDFEN',0,1
	.word	643
	.byte	1,2,2,35,1,11
	.byte	'PINBEDCON',0,1
	.word	643
	.byte	2,0,2,35,1,11
	.byte	'SCREN',0,1
	.word	643
	.byte	1,7,2,35,2,11
	.byte	'STBYRAMSEL',0,1
	.word	643
	.byte	2,5,2,35,2,11
	.byte	'SCRCLKSEL',0,1
	.word	643
	.byte	1,4,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	643
	.byte	1,3,2,35,2,11
	.byte	'TRISTEN',0,1
	.word	643
	.byte	1,2,2,35,2,11
	.byte	'TRISTREQ',0,1
	.word	643
	.byte	1,1,2,35,2,11
	.byte	'PORSTDF',0,1
	.word	643
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	643
	.byte	1,7,2,35,3,11
	.byte	'DCDCSYNC',0,1
	.word	643
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	643
	.byte	3,3,2,35,3,11
	.byte	'ESR0TRIST',0,1
	.word	643
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	643
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	643
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_PMSWCR0_Bits',0,4,153,7,3
	.word	43288
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR1_Bits',0,4,156,7,16,4,11
	.byte	'SCRSTEN',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'SCRSTREQ',0,1
	.word	643
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	643
	.byte	6,0,2,35,0,11
	.byte	'CPUIDLSEL',0,1
	.word	643
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	643
	.byte	1,4,2,35,1,11
	.byte	'IRADIS',0,1
	.word	643
	.byte	1,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	643
	.byte	3,0,2,35,1,11
	.byte	'SCRCFG',0,1
	.word	643
	.byte	8,0,2,35,2,11
	.byte	'CPUSEL',0,1
	.word	643
	.byte	3,5,2,35,3,11
	.byte	'STBYEVEN',0,1
	.word	643
	.byte	1,4,2,35,3,11
	.byte	'STBYEV',0,1
	.word	643
	.byte	3,1,2,35,3,11
	.byte	'LCK',0,1
	.word	643
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_PMSWCR1_Bits',0,4,170,7,3
	.word	43855
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR2_Bits',0,4,173,7,16,4,11
	.byte	'SCRINT',0,1
	.word	643
	.byte	8,0,2,35,0,11
	.byte	'BUSY',0,1
	.word	643
	.byte	1,7,2,35,1,11
	.byte	'SCRECC',0,1
	.word	643
	.byte	1,6,2,35,1,11
	.byte	'SCRWDT',0,1
	.word	643
	.byte	1,5,2,35,1,11
	.byte	'SCRRST',0,1
	.word	643
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	643
	.byte	4,0,2,35,1,11
	.byte	'TCINT',0,1
	.word	643
	.byte	8,0,2,35,2,11
	.byte	'TCINTREQ',0,1
	.word	643
	.byte	1,7,2,35,3,11
	.byte	'SMURST',0,1
	.word	643
	.byte	1,6,2,35,3,11
	.byte	'RST',0,1
	.word	643
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	643
	.byte	4,1,2,35,3,11
	.byte	'LCK',0,1
	.word	643
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_PMSWCR2_Bits',0,4,187,7,3
	.word	44149
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTAT_Bits',0,4,190,7,16,4,11
	.byte	'reserved_0',0,1
	.word	643
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKP',0,1
	.word	643
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUN',0,1
	.word	643
	.byte	1,4,2,35,0,11
	.byte	'PINAWKP',0,1
	.word	643
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUN',0,1
	.word	643
	.byte	1,2,2,35,0,11
	.byte	'PINBWKP',0,1
	.word	643
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUN',0,1
	.word	643
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	643
	.byte	1,7,2,35,1,11
	.byte	'PORSTDF',0,1
	.word	643
	.byte	1,6,2,35,1,11
	.byte	'HWCFGEVR',0,1
	.word	643
	.byte	3,3,2,35,1,11
	.byte	'STBYRAM',0,1
	.word	643
	.byte	2,1,2,35,1,11
	.byte	'TRIST',0,1
	.word	643
	.byte	1,0,2,35,1,11
	.byte	'SCRST',0,1
	.word	643
	.byte	1,7,2,35,2,11
	.byte	'SCRWKP',0,1
	.word	643
	.byte	1,6,2,35,2,11
	.byte	'SCR',0,1
	.word	643
	.byte	1,5,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	643
	.byte	1,4,2,35,2,11
	.byte	'ESR1WKEN',0,1
	.word	643
	.byte	1,3,2,35,2,11
	.byte	'PINAWKEN',0,1
	.word	643
	.byte	1,2,2,35,2,11
	.byte	'PINBWKEN',0,1
	.word	643
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	660
	.byte	4,5,2,35,2,11
	.byte	'ESR0TRIST',0,1
	.word	643
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	643
	.byte	4,0,2,35,3,0,29
	.byte	'Ifx_SCU_PMSWSTAT_Bits',0,4,214,7,3
	.word	44427
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTATCLR_Bits',0,4,217,7,16,4,11
	.byte	'reserved_0',0,1
	.word	643
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKPCLR',0,1
	.word	643
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUNCLR',0,1
	.word	643
	.byte	1,4,2,35,0,11
	.byte	'PINAWKPCLR',0,1
	.word	643
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUNCLR',0,1
	.word	643
	.byte	1,2,2,35,0,11
	.byte	'PINBWKPCLR',0,1
	.word	643
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUNCLR',0,1
	.word	643
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	643
	.byte	8,0,2,35,1,11
	.byte	'SCRSTCLR',0,1
	.word	643
	.byte	1,7,2,35,2,11
	.byte	'SCRWKPCLR',0,1
	.word	643
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	660
	.byte	14,0,2,35,2,0,29
	.byte	'Ifx_SCU_PMSWSTATCLR_Bits',0,4,230,7,3
	.word	44923
	.byte	10
	.byte	'_Ifx_SCU_RSTCON2_Bits',0,4,233,7,16,4,11
	.byte	'reserved_0',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'CLRC',0,1
	.word	643
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	660
	.byte	10,4,2,35,0,11
	.byte	'CSS0',0,1
	.word	643
	.byte	1,3,2,35,1,11
	.byte	'CSS1',0,1
	.word	643
	.byte	1,2,2,35,1,11
	.byte	'CSS2',0,1
	.word	643
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	643
	.byte	1,0,2,35,1,11
	.byte	'USRINFO',0,2
	.word	660
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_SCU_RSTCON2_Bits',0,4,243,7,3
	.word	45236
	.byte	10
	.byte	'_Ifx_SCU_RSTCON_Bits',0,4,246,7,16,4,11
	.byte	'ESR0',0,1
	.word	643
	.byte	2,6,2,35,0,11
	.byte	'ESR1',0,1
	.word	643
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	643
	.byte	2,2,2,35,0,11
	.byte	'SMU',0,1
	.word	643
	.byte	2,0,2,35,0,11
	.byte	'SW',0,1
	.word	643
	.byte	2,6,2,35,1,11
	.byte	'STM0',0,1
	.word	643
	.byte	2,4,2,35,1,11
	.byte	'STM1',0,1
	.word	643
	.byte	2,2,2,35,1,11
	.byte	'STM2',0,1
	.word	643
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	660
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_SCU_RSTCON_Bits',0,4,129,8,3
	.word	45445
	.byte	10
	.byte	'_Ifx_SCU_RSTSTAT_Bits',0,4,132,8,16,4,11
	.byte	'ESR0',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'ESR1',0,1
	.word	643
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	643
	.byte	1,5,2,35,0,11
	.byte	'SMU',0,1
	.word	643
	.byte	1,4,2,35,0,11
	.byte	'SW',0,1
	.word	643
	.byte	1,3,2,35,0,11
	.byte	'STM0',0,1
	.word	643
	.byte	1,2,2,35,0,11
	.byte	'STM1',0,1
	.word	643
	.byte	1,1,2,35,0,11
	.byte	'STM2',0,1
	.word	643
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	643
	.byte	8,0,2,35,1,11
	.byte	'PORST',0,1
	.word	643
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	643
	.byte	1,6,2,35,2,11
	.byte	'CB0',0,1
	.word	643
	.byte	1,5,2,35,2,11
	.byte	'CB1',0,1
	.word	643
	.byte	1,4,2,35,2,11
	.byte	'CB3',0,1
	.word	643
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	643
	.byte	2,1,2,35,2,11
	.byte	'EVR13',0,1
	.word	643
	.byte	1,0,2,35,2,11
	.byte	'EVR33',0,1
	.word	643
	.byte	1,7,2,35,3,11
	.byte	'SWD',0,1
	.word	643
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	643
	.byte	2,4,2,35,3,11
	.byte	'STBYR',0,1
	.word	643
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	643
	.byte	3,0,2,35,3,0,29
	.byte	'Ifx_SCU_RSTSTAT_Bits',0,4,155,8,3
	.word	45656
	.byte	10
	.byte	'_Ifx_SCU_SAFECON_Bits',0,4,158,8,16,4,11
	.byte	'HBT',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	468
	.byte	31,0,2,35,0,0,29
	.byte	'Ifx_SCU_SAFECON_Bits',0,4,162,8,3
	.word	46088
	.byte	10
	.byte	'_Ifx_SCU_STSTAT_Bits',0,4,165,8,16,4,11
	.byte	'HWCFG',0,1
	.word	643
	.byte	8,0,2,35,0,11
	.byte	'FTM',0,1
	.word	643
	.byte	7,1,2,35,1,11
	.byte	'MODE',0,1
	.word	643
	.byte	1,0,2,35,1,11
	.byte	'FCBAE',0,1
	.word	643
	.byte	1,7,2,35,2,11
	.byte	'LUDIS',0,1
	.word	643
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	643
	.byte	1,5,2,35,2,11
	.byte	'TRSTL',0,1
	.word	643
	.byte	1,4,2,35,2,11
	.byte	'SPDEN',0,1
	.word	643
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	643
	.byte	3,0,2,35,2,11
	.byte	'RAMINT',0,1
	.word	643
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	643
	.byte	7,0,2,35,3,0,29
	.byte	'Ifx_SCU_STSTAT_Bits',0,4,178,8,3
	.word	46184
	.byte	10
	.byte	'_Ifx_SCU_SWRSTCON_Bits',0,4,181,8,16,4,11
	.byte	'reserved_0',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'SWRSTREQ',0,1
	.word	643
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	468
	.byte	30,0,2,35,0,0,29
	.byte	'Ifx_SCU_SWRSTCON_Bits',0,4,186,8,3
	.word	46444
	.byte	10
	.byte	'_Ifx_SCU_SYSCON_Bits',0,4,189,8,16,4,11
	.byte	'CCTRIG0',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	643
	.byte	1,6,2,35,0,11
	.byte	'RAMINTM',0,1
	.word	643
	.byte	2,4,2,35,0,11
	.byte	'SETLUDIS',0,1
	.word	643
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	643
	.byte	3,0,2,35,0,11
	.byte	'DATM',0,1
	.word	643
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,4
	.word	468
	.byte	23,0,2,35,0,0,29
	.byte	'Ifx_SCU_SYSCON_Bits',0,4,198,8,3
	.word	46569
	.byte	10
	.byte	'_Ifx_SCU_TRAPCLR_Bits',0,4,201,8,16,4,11
	.byte	'ESR0T',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	643
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	643
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	643
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	468
	.byte	28,0,2,35,0,0,29
	.byte	'Ifx_SCU_TRAPCLR_Bits',0,4,208,8,3
	.word	46766
	.byte	10
	.byte	'_Ifx_SCU_TRAPDIS_Bits',0,4,211,8,16,4,11
	.byte	'ESR0T',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	643
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	643
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	643
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	468
	.byte	28,0,2,35,0,0,29
	.byte	'Ifx_SCU_TRAPDIS_Bits',0,4,218,8,3
	.word	46919
	.byte	10
	.byte	'_Ifx_SCU_TRAPSET_Bits',0,4,221,8,16,4,11
	.byte	'ESR0T',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	643
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	643
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	643
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	468
	.byte	28,0,2,35,0,0,29
	.byte	'Ifx_SCU_TRAPSET_Bits',0,4,228,8,3
	.word	47072
	.byte	10
	.byte	'_Ifx_SCU_TRAPSTAT_Bits',0,4,231,8,16,4,11
	.byte	'ESR0T',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	643
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	643
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	643
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	468
	.byte	28,0,2,35,0,0,29
	.byte	'Ifx_SCU_TRAPSTAT_Bits',0,4,238,8,3
	.word	47225
	.byte	29
	.byte	'Ifx_SCU_WDTCPU_CON0_Bits',0,4,247,8,3
	.word	507
	.byte	29
	.byte	'Ifx_SCU_WDTCPU_CON1_Bits',0,4,134,9,3
	.word	682
	.byte	29
	.byte	'Ifx_SCU_WDTCPU_SR_Bits',0,4,150,9,3
	.word	926
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON0_Bits',0,4,153,9,16,4,11
	.byte	'ENDINIT',0,4
	.word	491
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	491
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	491
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	491
	.byte	16,0,2,35,0,0,29
	.byte	'Ifx_SCU_WDTS_CON0_Bits',0,4,159,9,3
	.word	47480
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON1_Bits',0,4,162,9,16,4,11
	.byte	'CLRIRF',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	643
	.byte	1,6,2,35,0,11
	.byte	'IR0',0,1
	.word	643
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	643
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	643
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	643
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	643
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	643
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	643
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	643
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	660
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_SCU_WDTS_CON1_Bits',0,4,175,9,3
	.word	47606
	.byte	10
	.byte	'_Ifx_SCU_WDTS_SR_Bits',0,4,178,9,16,4,11
	.byte	'AE',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	643
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	643
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	643
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	643
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	643
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	643
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	643
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	643
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	643
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	660
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_SCU_WDTS_SR_Bits',0,4,191,9,3
	.word	47858
	.byte	12,4,199,9,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29890
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_ACCEN0',0,4,204,9,3
	.word	48077
	.byte	12,4,207,9,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30447
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_ACCEN1',0,4,212,9,3
	.word	48141
	.byte	12,4,215,9,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30524
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_ARSTDIS',0,4,220,9,3
	.word	48205
	.byte	12,4,223,9,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30660
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_CCUCON0',0,4,228,9,3
	.word	48270
	.byte	12,4,231,9,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30940
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_CCUCON1',0,4,236,9,3
	.word	48335
	.byte	12,4,239,9,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31178
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_CCUCON2',0,4,244,9,3
	.word	48400
	.byte	12,4,247,9,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31306
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_CCUCON3',0,4,252,9,3
	.word	48465
	.byte	12,4,255,9,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31549
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_CCUCON4',0,4,132,10,3
	.word	48530
	.byte	12,4,135,10,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31784
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_CCUCON5',0,4,140,10,3
	.word	48595
	.byte	12,4,143,10,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31912
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_CCUCON6',0,4,148,10,3
	.word	48660
	.byte	12,4,151,10,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32012
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_CCUCON7',0,4,156,10,3
	.word	48725
	.byte	12,4,159,10,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32112
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_CHIPID',0,4,164,10,3
	.word	48790
	.byte	12,4,167,10,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32320
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_DTSCON',0,4,172,10,3
	.word	48854
	.byte	12,4,175,10,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32485
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_DTSLIM',0,4,180,10,3
	.word	48918
	.byte	12,4,183,10,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32668
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_DTSSTAT',0,4,188,10,3
	.word	48982
	.byte	12,4,191,10,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32822
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EICR',0,4,196,10,3
	.word	49047
	.byte	12,4,199,10,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33186
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EIFR',0,4,204,10,3
	.word	49109
	.byte	12,4,207,10,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33397
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EMSR',0,4,212,10,3
	.word	49171
	.byte	12,4,215,10,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33649
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_ESRCFG',0,4,220,10,3
	.word	49233
	.byte	12,4,223,10,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33767
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_ESROCFG',0,4,228,10,3
	.word	49297
	.byte	12,4,231,10,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33878
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVR13CON',0,4,236,10,3
	.word	49362
	.byte	12,4,239,10,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34041
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVR33CON',0,4,244,10,3
	.word	49428
	.byte	12,4,247,10,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34204
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRADCSTAT',0,4,252,10,3
	.word	49494
	.byte	12,4,255,10,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34362
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRDVSTAT',0,4,132,11,3
	.word	49562
	.byte	12,4,135,11,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34527
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRMONCTRL',0,4,140,11,3
	.word	49629
	.byte	12,4,143,11,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34856
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVROSCCTRL',0,4,148,11,3
	.word	49697
	.byte	12,4,151,11,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35077
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVROVMON',0,4,156,11,3
	.word	49765
	.byte	12,4,159,11,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35240
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRRSTCON',0,4,164,11,3
	.word	49831
	.byte	12,4,167,11,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35512
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF1',0,4,172,11,3
	.word	49898
	.byte	12,4,175,11,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35665
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF2',0,4,180,11,3
	.word	49967
	.byte	12,4,183,11,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35821
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF3',0,4,188,11,3
	.word	50036
	.byte	12,4,191,11,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35983
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF4',0,4,196,11,3
	.word	50105
	.byte	12,4,199,11,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36126
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF5',0,4,204,11,3
	.word	50174
	.byte	12,4,207,11,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36291
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF6',0,4,212,11,3
	.word	50243
	.byte	12,4,215,11,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36436
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSDCTRL1',0,4,220,11,3
	.word	50312
	.byte	12,4,223,11,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36617
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSDCTRL2',0,4,228,11,3
	.word	50380
	.byte	12,4,231,11,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36791
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSDCTRL3',0,4,236,11,3
	.word	50448
	.byte	12,4,239,11,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36951
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSDCTRL4',0,4,244,11,3
	.word	50516
	.byte	12,4,247,11,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37095
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSTAT',0,4,252,11,3
	.word	50584
	.byte	12,4,255,11,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37369
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRTRIM',0,4,132,12,3
	.word	50649
	.byte	12,4,135,12,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37508
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRUVMON',0,4,140,12,3
	.word	50714
	.byte	12,4,143,12,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37671
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EXTCON',0,4,148,12,3
	.word	50780
	.byte	12,4,151,12,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37889
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_FDR',0,4,156,12,3
	.word	50844
	.byte	12,4,159,12,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38052
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_FMR',0,4,164,12,3
	.word	50905
	.byte	12,4,167,12,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38388
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_ID',0,4,172,12,3
	.word	50966
	.byte	12,4,175,12,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38495
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_IGCR',0,4,180,12,3
	.word	51026
	.byte	12,4,183,12,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38947
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_IN',0,4,188,12,3
	.word	51088
	.byte	12,4,191,12,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39046
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_IOCR',0,4,196,12,3
	.word	51148
	.byte	12,4,199,12,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39196
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_LBISTCTRL0',0,4,204,12,3
	.word	51210
	.byte	12,4,207,12,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39345
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_LBISTCTRL1',0,4,212,12,3
	.word	51278
	.byte	12,4,215,12,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39506
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_LBISTCTRL2',0,4,220,12,3
	.word	51346
	.byte	12,4,223,12,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39636
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_LCLCON',0,4,228,12,3
	.word	51414
	.byte	12,4,231,12,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39768
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_LCLTEST',0,4,236,12,3
	.word	51478
	.byte	12,4,239,12,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39883
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_MANID',0,4,244,12,3
	.word	51543
	.byte	12,4,247,12,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39994
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_OMR',0,4,252,12,3
	.word	51606
	.byte	12,4,255,12,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40152
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_OSCCON',0,4,132,13,3
	.word	51667
	.byte	12,4,135,13,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40564
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_OUT',0,4,140,13,3
	.word	51731
	.byte	12,4,143,13,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40665
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_OVCCON',0,4,148,13,3
	.word	51792
	.byte	12,4,151,13,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40932
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_OVCENABLE',0,4,156,13,3
	.word	51856
	.byte	12,4,159,13,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41068
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PDISC',0,4,164,13,3
	.word	51923
	.byte	12,4,167,13,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41179
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PDR',0,4,172,13,3
	.word	51986
	.byte	12,4,175,13,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41312
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PDRR',0,4,180,13,3
	.word	52047
	.byte	12,4,183,13,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41515
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PLLCON0',0,4,188,13,3
	.word	52109
	.byte	12,4,191,13,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41871
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PLLCON1',0,4,196,13,3
	.word	52174
	.byte	12,4,199,13,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42049
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PLLCON2',0,4,204,13,3
	.word	52239
	.byte	12,4,207,13,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42149
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PLLERAYCON0',0,4,212,13,3
	.word	52304
	.byte	12,4,215,13,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42519
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PLLERAYCON1',0,4,220,13,3
	.word	52373
	.byte	12,4,223,13,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42705
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PLLERAYSTAT',0,4,228,13,3
	.word	52442
	.byte	12,4,231,13,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42903
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PLLSTAT',0,4,236,13,3
	.word	52511
	.byte	12,4,239,13,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43136
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PMCSR',0,4,244,13,3
	.word	52576
	.byte	12,4,247,13,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43288
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PMSWCR0',0,4,252,13,3
	.word	52639
	.byte	12,4,255,13,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43855
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PMSWCR1',0,4,132,14,3
	.word	52704
	.byte	12,4,135,14,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44149
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PMSWCR2',0,4,140,14,3
	.word	52769
	.byte	12,4,143,14,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44427
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PMSWSTAT',0,4,148,14,3
	.word	52834
	.byte	12,4,151,14,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44923
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PMSWSTATCLR',0,4,156,14,3
	.word	52900
	.byte	12,4,159,14,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45445
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_RSTCON',0,4,164,14,3
	.word	52969
	.byte	12,4,167,14,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45236
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_RSTCON2',0,4,172,14,3
	.word	53033
	.byte	12,4,175,14,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45656
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_RSTSTAT',0,4,180,14,3
	.word	53098
	.byte	12,4,183,14,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46088
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_SAFECON',0,4,188,14,3
	.word	53163
	.byte	12,4,191,14,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46184
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_STSTAT',0,4,196,14,3
	.word	53228
	.byte	12,4,199,14,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46444
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_SWRSTCON',0,4,204,14,3
	.word	53292
	.byte	12,4,207,14,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46569
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_SYSCON',0,4,212,14,3
	.word	53358
	.byte	12,4,215,14,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46766
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_TRAPCLR',0,4,220,14,3
	.word	53422
	.byte	12,4,223,14,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46919
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_TRAPDIS',0,4,228,14,3
	.word	53487
	.byte	12,4,231,14,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47072
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_TRAPSET',0,4,236,14,3
	.word	53552
	.byte	12,4,239,14,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47225
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_TRAPSTAT',0,4,244,14,3
	.word	53617
	.byte	29
	.byte	'Ifx_SCU_WDTCPU_CON0',0,4,252,14,3
	.word	603
	.byte	29
	.byte	'Ifx_SCU_WDTCPU_CON1',0,4,132,15,3
	.word	886
	.byte	29
	.byte	'Ifx_SCU_WDTCPU_SR',0,4,140,15,3
	.word	1117
	.byte	12,4,143,15,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47480
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_WDTS_CON0',0,4,148,15,3
	.word	53768
	.byte	12,4,151,15,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47606
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_WDTS_CON1',0,4,156,15,3
	.word	53835
	.byte	12,4,159,15,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47858
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_WDTS_SR',0,4,164,15,3
	.word	53902
	.byte	14
	.word	1157
	.byte	29
	.byte	'Ifx_SCU_WDTCPU',0,4,180,15,3
	.word	53967
	.byte	10
	.byte	'_Ifx_SCU_WDTS',0,4,183,15,25,12,13
	.byte	'CON0',0
	.word	53768
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	53835
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	53902
	.byte	4,2,35,8,0,14
	.word	53996
	.byte	29
	.byte	'Ifx_SCU_WDTS',0,4,188,15,3
	.word	54057
	.byte	15,8
	.word	49233
	.byte	16,1,0,15,20
	.word	643
	.byte	16,19,0,15,8
	.word	52576
	.byte	16,1,0,14
	.word	53996
	.byte	15,24
	.word	1157
	.byte	16,1,0,14
	.word	54116
	.byte	15,16
	.word	643
	.byte	16,15,0,15,28
	.word	643
	.byte	16,27,0,15,40
	.word	643
	.byte	16,39,0,15,16
	.word	49047
	.byte	16,3,0,15,16
	.word	51026
	.byte	16,3,0,15,180,3
	.word	643
	.byte	16,179,3,0,10
	.byte	'_Ifx_SCU',0,4,201,15,25,128,8,13
	.byte	'reserved_0',0
	.word	4460
	.byte	8,2,35,0,13
	.byte	'ID',0
	.word	50966
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	2641
	.byte	4,2,35,12,13
	.byte	'OSCCON',0
	.word	51667
	.byte	4,2,35,16,13
	.byte	'PLLSTAT',0
	.word	52511
	.byte	4,2,35,20,13
	.byte	'PLLCON0',0
	.word	52109
	.byte	4,2,35,24,13
	.byte	'PLLCON1',0
	.word	52174
	.byte	4,2,35,28,13
	.byte	'PLLCON2',0
	.word	52239
	.byte	4,2,35,32,13
	.byte	'PLLERAYSTAT',0
	.word	52442
	.byte	4,2,35,36,13
	.byte	'PLLERAYCON0',0
	.word	52304
	.byte	4,2,35,40,13
	.byte	'PLLERAYCON1',0
	.word	52373
	.byte	4,2,35,44,13
	.byte	'CCUCON0',0
	.word	48270
	.byte	4,2,35,48,13
	.byte	'CCUCON1',0
	.word	48335
	.byte	4,2,35,52,13
	.byte	'FDR',0
	.word	50844
	.byte	4,2,35,56,13
	.byte	'EXTCON',0
	.word	50780
	.byte	4,2,35,60,13
	.byte	'CCUCON2',0
	.word	48400
	.byte	4,2,35,64,13
	.byte	'CCUCON3',0
	.word	48465
	.byte	4,2,35,68,13
	.byte	'CCUCON4',0
	.word	48530
	.byte	4,2,35,72,13
	.byte	'CCUCON5',0
	.word	48595
	.byte	4,2,35,76,13
	.byte	'RSTSTAT',0
	.word	53098
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	2641
	.byte	4,2,35,84,13
	.byte	'RSTCON',0
	.word	52969
	.byte	4,2,35,88,13
	.byte	'ARSTDIS',0
	.word	48205
	.byte	4,2,35,92,13
	.byte	'SWRSTCON',0
	.word	53292
	.byte	4,2,35,96,13
	.byte	'RSTCON2',0
	.word	53033
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	2641
	.byte	4,2,35,104,13
	.byte	'EVRRSTCON',0
	.word	49831
	.byte	4,2,35,108,13
	.byte	'ESRCFG',0
	.word	54084
	.byte	8,2,35,112,13
	.byte	'ESROCFG',0
	.word	49297
	.byte	4,2,35,120,13
	.byte	'SYSCON',0
	.word	53358
	.byte	4,2,35,124,13
	.byte	'CCUCON6',0
	.word	48660
	.byte	4,3,35,128,1,13
	.byte	'CCUCON7',0
	.word	48725
	.byte	4,3,35,132,1,13
	.byte	'reserved_88',0
	.word	54093
	.byte	20,3,35,136,1,13
	.byte	'PDR',0
	.word	51986
	.byte	4,3,35,156,1,13
	.byte	'IOCR',0
	.word	51148
	.byte	4,3,35,160,1,13
	.byte	'OUT',0
	.word	51731
	.byte	4,3,35,164,1,13
	.byte	'OMR',0
	.word	51606
	.byte	4,3,35,168,1,13
	.byte	'IN',0
	.word	51088
	.byte	4,3,35,172,1,13
	.byte	'EVRSTAT',0
	.word	50584
	.byte	4,3,35,176,1,13
	.byte	'EVRDVSTAT',0
	.word	49562
	.byte	4,3,35,180,1,13
	.byte	'EVR13CON',0
	.word	49362
	.byte	4,3,35,184,1,13
	.byte	'EVR33CON',0
	.word	49428
	.byte	4,3,35,188,1,13
	.byte	'STSTAT',0
	.word	53228
	.byte	4,3,35,192,1,13
	.byte	'reserved_C4',0
	.word	2641
	.byte	4,3,35,196,1,13
	.byte	'PMSWCR0',0
	.word	52639
	.byte	4,3,35,200,1,13
	.byte	'PMSWSTAT',0
	.word	52834
	.byte	4,3,35,204,1,13
	.byte	'PMSWSTATCLR',0
	.word	52900
	.byte	4,3,35,208,1,13
	.byte	'PMCSR',0
	.word	54102
	.byte	8,3,35,212,1,13
	.byte	'reserved_DC',0
	.word	2641
	.byte	4,3,35,220,1,13
	.byte	'DTSSTAT',0
	.word	48982
	.byte	4,3,35,224,1,13
	.byte	'DTSCON',0
	.word	48854
	.byte	4,3,35,228,1,13
	.byte	'PMSWCR1',0
	.word	52704
	.byte	4,3,35,232,1,13
	.byte	'PMSWCR2',0
	.word	52769
	.byte	4,3,35,236,1,13
	.byte	'WDTS',0
	.word	54111
	.byte	12,3,35,240,1,13
	.byte	'EMSR',0
	.word	49171
	.byte	4,3,35,252,1,13
	.byte	'WDTCPU',0
	.word	54125
	.byte	24,3,35,128,2,13
	.byte	'reserved_118',0
	.word	4800
	.byte	12,3,35,152,2,13
	.byte	'TRAPSTAT',0
	.word	53617
	.byte	4,3,35,164,2,13
	.byte	'TRAPSET',0
	.word	53552
	.byte	4,3,35,168,2,13
	.byte	'TRAPCLR',0
	.word	53422
	.byte	4,3,35,172,2,13
	.byte	'TRAPDIS',0
	.word	53487
	.byte	4,3,35,176,2,13
	.byte	'reserved_134',0
	.word	2641
	.byte	4,3,35,180,2,13
	.byte	'LCLCON1',0
	.word	51414
	.byte	4,3,35,184,2,13
	.byte	'LCLTEST',0
	.word	51478
	.byte	4,3,35,188,2,13
	.byte	'CHIPID',0
	.word	48790
	.byte	4,3,35,192,2,13
	.byte	'MANID',0
	.word	51543
	.byte	4,3,35,196,2,13
	.byte	'reserved_148',0
	.word	4460
	.byte	8,3,35,200,2,13
	.byte	'SAFECON',0
	.word	53163
	.byte	4,3,35,208,2,13
	.byte	'reserved_154',0
	.word	54130
	.byte	16,3,35,212,2,13
	.byte	'LBISTCTRL0',0
	.word	51210
	.byte	4,3,35,228,2,13
	.byte	'LBISTCTRL1',0
	.word	51278
	.byte	4,3,35,232,2,13
	.byte	'LBISTCTRL2',0
	.word	51346
	.byte	4,3,35,236,2,13
	.byte	'reserved_170',0
	.word	54139
	.byte	28,3,35,240,2,13
	.byte	'PDISC',0
	.word	51923
	.byte	4,3,35,140,3,13
	.byte	'reserved_190',0
	.word	4460
	.byte	8,3,35,144,3,13
	.byte	'EVRTRIM',0
	.word	50649
	.byte	4,3,35,152,3,13
	.byte	'EVRADCSTAT',0
	.word	49494
	.byte	4,3,35,156,3,13
	.byte	'EVRUVMON',0
	.word	50714
	.byte	4,3,35,160,3,13
	.byte	'EVROVMON',0
	.word	49765
	.byte	4,3,35,164,3,13
	.byte	'EVRMONCTRL',0
	.word	49629
	.byte	4,3,35,168,3,13
	.byte	'reserved_1AC',0
	.word	2641
	.byte	4,3,35,172,3,13
	.byte	'EVRSDCTRL1',0
	.word	50312
	.byte	4,3,35,176,3,13
	.byte	'EVRSDCTRL2',0
	.word	50380
	.byte	4,3,35,180,3,13
	.byte	'EVRSDCTRL3',0
	.word	50448
	.byte	4,3,35,184,3,13
	.byte	'EVRSDCTRL4',0
	.word	50516
	.byte	4,3,35,188,3,13
	.byte	'EVRSDCOEFF1',0
	.word	49898
	.byte	4,3,35,192,3,13
	.byte	'EVRSDCOEFF2',0
	.word	49967
	.byte	4,3,35,196,3,13
	.byte	'EVRSDCOEFF3',0
	.word	50036
	.byte	4,3,35,200,3,13
	.byte	'EVRSDCOEFF4',0
	.word	50105
	.byte	4,3,35,204,3,13
	.byte	'EVRSDCOEFF5',0
	.word	50174
	.byte	4,3,35,208,3,13
	.byte	'EVRSDCOEFF6',0
	.word	50243
	.byte	4,3,35,212,3,13
	.byte	'EVROSCCTRL',0
	.word	49697
	.byte	4,3,35,216,3,13
	.byte	'reserved_1DC',0
	.word	2641
	.byte	4,3,35,220,3,13
	.byte	'OVCENABLE',0
	.word	51856
	.byte	4,3,35,224,3,13
	.byte	'OVCCON',0
	.word	51792
	.byte	4,3,35,228,3,13
	.byte	'reserved_1E8',0
	.word	54148
	.byte	40,3,35,232,3,13
	.byte	'EICR',0
	.word	54157
	.byte	16,3,35,144,4,13
	.byte	'EIFR',0
	.word	49109
	.byte	4,3,35,160,4,13
	.byte	'FMR',0
	.word	50905
	.byte	4,3,35,164,4,13
	.byte	'PDRR',0
	.word	52047
	.byte	4,3,35,168,4,13
	.byte	'IGCR',0
	.word	54166
	.byte	16,3,35,172,4,13
	.byte	'reserved_23C',0
	.word	2641
	.byte	4,3,35,188,4,13
	.byte	'DTSLIM',0
	.word	48918
	.byte	4,3,35,192,4,13
	.byte	'reserved_244',0
	.word	54175
	.byte	180,3,3,35,196,4,13
	.byte	'ACCEN1',0
	.word	48141
	.byte	4,3,35,248,7,13
	.byte	'ACCEN0',0
	.word	48077
	.byte	4,3,35,252,7,0,14
	.word	54186
	.byte	29
	.byte	'Ifx_SCU',0,4,181,16,3
	.word	56176
	.byte	29
	.byte	'IfxPort_InputMode',0,5,89,3
	.word	9393
	.byte	29
	.byte	'IfxPort_OutputIdx',0,5,130,1,3
	.word	9668
	.byte	29
	.byte	'IfxPort_OutputMode',0,5,138,1,3
	.word	9598
	.byte	17,5,144,1,9,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed1',0,0,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed2',0,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed3',0,2,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed4',0,3,18
	.byte	'IfxPort_PadDriver_lvdsSpeed1',0,4,18
	.byte	'IfxPort_PadDriver_lvdsSpeed2',0,5,18
	.byte	'IfxPort_PadDriver_lvdsSpeed3',0,6,18
	.byte	'IfxPort_PadDriver_lvdsSpeed4',0,7,18
	.byte	'IfxPort_PadDriver_ttlSpeed1',0,8,18
	.byte	'IfxPort_PadDriver_ttlSpeed2',0,9,18
	.byte	'IfxPort_PadDriver_ttlSpeed3',0,10,18
	.byte	'IfxPort_PadDriver_ttlSpeed4',0,11,0,29
	.byte	'IfxPort_PadDriver',0,5,158,1,3
	.word	56279
	.byte	29
	.byte	'IfxPort_State',0,5,178,1,3
	.word	9981
	.byte	20,5,190,1,9,8,13
	.byte	'port',0
	.word	9388
	.byte	4,2,35,0,13
	.byte	'pinIndex',0
	.word	643
	.byte	1,2,35,4,0,29
	.byte	'IfxPort_Pin',0,5,194,1,3
	.word	56744
	.byte	29
	.byte	'gpio_pin_enum',0,19,89,2
	.word	22646
	.byte	29
	.byte	'gpio_dir_enum',0,19,95,2
	.word	24586
	.byte	29
	.byte	'gpio_mode_enum',0,19,111,2
	.word	24604
	.byte	29
	.byte	'IfxDma_ChannelId',0,22,156,1,3
	.word	25340
	.byte	10
	.byte	'_Ifx_DMA_ACCEN00_Bits',0,31,45,16,4,11
	.byte	'EN0',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	643
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	643
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	643
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	643
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	643
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	643
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	643
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	643
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	643
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	643
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	643
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	643
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	643
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	643
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	643
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	643
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	643
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	643
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	643
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	643
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	643
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	643
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	643
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	643
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	643
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	643
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	643
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	643
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	643
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	643
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	643
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_DMA_ACCEN00_Bits',0,31,79,3
	.word	56897
	.byte	10
	.byte	'_Ifx_DMA_ACCEN01_Bits',0,31,82,16,4,11
	.byte	'reserved_0',0,4
	.word	468
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_DMA_ACCEN01_Bits',0,31,85,3
	.word	57456
	.byte	10
	.byte	'_Ifx_DMA_ACCEN10_Bits',0,31,88,16,4,11
	.byte	'EN0',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	643
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	643
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	643
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	643
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	643
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	643
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	643
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	643
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	643
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	643
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	643
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	643
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	643
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	643
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	643
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	643
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	643
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	643
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	643
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	643
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	643
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	643
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	643
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	643
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	643
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	643
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	643
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	643
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	643
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	643
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	643
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_DMA_ACCEN10_Bits',0,31,122,3
	.word	57535
	.byte	10
	.byte	'_Ifx_DMA_ACCEN11_Bits',0,31,125,16,4,11
	.byte	'reserved_0',0,4
	.word	468
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_DMA_ACCEN11_Bits',0,31,128,1,3
	.word	58094
	.byte	10
	.byte	'_Ifx_DMA_ACCEN20_Bits',0,31,131,1,16,4,11
	.byte	'EN0',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	643
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	643
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	643
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	643
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	643
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	643
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	643
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	643
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	643
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	643
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	643
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	643
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	643
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	643
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	643
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	643
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	643
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	643
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	643
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	643
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	643
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	643
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	643
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	643
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	643
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	643
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	643
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	643
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	643
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	643
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	643
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_DMA_ACCEN20_Bits',0,31,165,1,3
	.word	58174
	.byte	10
	.byte	'_Ifx_DMA_ACCEN21_Bits',0,31,168,1,16,4,11
	.byte	'reserved_0',0,4
	.word	468
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_DMA_ACCEN21_Bits',0,31,171,1,3
	.word	58735
	.byte	10
	.byte	'_Ifx_DMA_ACCEN30_Bits',0,31,174,1,16,4,11
	.byte	'EN0',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	643
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	643
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	643
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	643
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	643
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	643
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	643
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	643
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	643
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	643
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	643
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	643
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	643
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	643
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	643
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	643
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	643
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	643
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	643
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	643
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	643
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	643
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	643
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	643
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	643
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	643
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	643
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	643
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	643
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	643
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	643
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_DMA_ACCEN30_Bits',0,31,208,1,3
	.word	58816
	.byte	10
	.byte	'_Ifx_DMA_ACCEN31_Bits',0,31,211,1,16,4,11
	.byte	'reserved_0',0,4
	.word	468
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_DMA_ACCEN31_Bits',0,31,214,1,3
	.word	59377
	.byte	10
	.byte	'_Ifx_DMA_BLK_CLRE_Bits',0,31,217,1,16,4,11
	.byte	'reserved_0',0,2
	.word	660
	.byte	16,0,2,35,0,11
	.byte	'CSER',0,1
	.word	643
	.byte	1,7,2,35,2,11
	.byte	'CDER',0,1
	.word	643
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	643
	.byte	2,4,2,35,2,11
	.byte	'CSPBER',0,1
	.word	643
	.byte	1,3,2,35,2,11
	.byte	'CSRIER',0,1
	.word	643
	.byte	1,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	643
	.byte	2,0,2,35,2,11
	.byte	'CRAMER',0,1
	.word	643
	.byte	1,7,2,35,3,11
	.byte	'CSLLER',0,1
	.word	643
	.byte	1,6,2,35,3,11
	.byte	'CDLLER',0,1
	.word	643
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	643
	.byte	5,0,2,35,3,0,29
	.byte	'Ifx_DMA_BLK_CLRE_Bits',0,31,230,1,3
	.word	59458
	.byte	10
	.byte	'_Ifx_DMA_BLK_EER_Bits',0,31,233,1,16,4,11
	.byte	'reserved_0',0,2
	.word	660
	.byte	16,0,2,35,0,11
	.byte	'ESER',0,1
	.word	643
	.byte	1,7,2,35,2,11
	.byte	'EDER',0,1
	.word	643
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	643
	.byte	6,0,2,35,2,11
	.byte	'ERER',0,1
	.word	643
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	643
	.byte	1,6,2,35,3,11
	.byte	'ELER',0,1
	.word	643
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	643
	.byte	5,0,2,35,3,0,29
	.byte	'Ifx_DMA_BLK_EER_Bits',0,31,243,1,3
	.word	59732
	.byte	10
	.byte	'_Ifx_DMA_BLK_ERRSR_Bits',0,31,246,1,16,4,11
	.byte	'LEC',0,1
	.word	643
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	660
	.byte	9,0,2,35,0,11
	.byte	'SER',0,1
	.word	643
	.byte	1,7,2,35,2,11
	.byte	'DER',0,1
	.word	643
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	643
	.byte	2,4,2,35,2,11
	.byte	'SPBER',0,1
	.word	643
	.byte	1,3,2,35,2,11
	.byte	'SRIER',0,1
	.word	643
	.byte	1,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	643
	.byte	2,0,2,35,2,11
	.byte	'RAMER',0,1
	.word	643
	.byte	1,7,2,35,3,11
	.byte	'SLLER',0,1
	.word	643
	.byte	1,6,2,35,3,11
	.byte	'DLLER',0,1
	.word	643
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	643
	.byte	5,0,2,35,3,0,29
	.byte	'Ifx_DMA_BLK_ERRSR_Bits',0,31,132,2,3
	.word	59946
	.byte	10
	.byte	'_Ifx_DMA_BLK_ME_ADICR_Bits',0,31,135,2,16,4,11
	.byte	'SMF',0,1
	.word	643
	.byte	3,5,2,35,0,11
	.byte	'INCS',0,1
	.word	643
	.byte	1,4,2,35,0,11
	.byte	'DMF',0,1
	.word	643
	.byte	3,1,2,35,0,11
	.byte	'INCD',0,1
	.word	643
	.byte	1,0,2,35,0,11
	.byte	'CBLS',0,1
	.word	643
	.byte	4,4,2,35,1,11
	.byte	'CBLD',0,1
	.word	643
	.byte	4,0,2,35,1,11
	.byte	'SHCT',0,1
	.word	643
	.byte	4,4,2,35,2,11
	.byte	'SCBE',0,1
	.word	643
	.byte	1,3,2,35,2,11
	.byte	'DCBE',0,1
	.word	643
	.byte	1,2,2,35,2,11
	.byte	'STAMP',0,1
	.word	643
	.byte	1,1,2,35,2,11
	.byte	'ETRL',0,1
	.word	643
	.byte	1,0,2,35,2,11
	.byte	'WRPSE',0,1
	.word	643
	.byte	1,7,2,35,3,11
	.byte	'WRPDE',0,1
	.word	643
	.byte	1,6,2,35,3,11
	.byte	'INTCT',0,1
	.word	643
	.byte	2,4,2,35,3,11
	.byte	'IRDV',0,1
	.word	643
	.byte	4,0,2,35,3,0,29
	.byte	'Ifx_DMA_BLK_ME_ADICR_Bits',0,31,152,2,3
	.word	60230
	.byte	10
	.byte	'_Ifx_DMA_BLK_ME_CHCR_Bits',0,31,155,2,16,4,11
	.byte	'TREL',0,2
	.word	660
	.byte	14,2,2,35,0,11
	.byte	'reserved_14',0,1
	.word	643
	.byte	2,0,2,35,1,11
	.byte	'BLKM',0,1
	.word	643
	.byte	3,5,2,35,2,11
	.byte	'RROAT',0,1
	.word	643
	.byte	1,4,2,35,2,11
	.byte	'CHMODE',0,1
	.word	643
	.byte	1,3,2,35,2,11
	.byte	'CHDW',0,1
	.word	643
	.byte	3,0,2,35,2,11
	.byte	'PATSEL',0,1
	.word	643
	.byte	3,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	643
	.byte	1,4,2,35,3,11
	.byte	'PRSEL',0,1
	.word	643
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	643
	.byte	1,2,2,35,3,11
	.byte	'DMAPRIO',0,1
	.word	643
	.byte	2,0,2,35,3,0,29
	.byte	'Ifx_DMA_BLK_ME_CHCR_Bits',0,31,168,2,3
	.word	60541
	.byte	10
	.byte	'_Ifx_DMA_BLK_ME_CHSR_Bits',0,31,171,2,16,4,11
	.byte	'TCOUNT',0,2
	.word	660
	.byte	14,2,2,35,0,11
	.byte	'reserved_14',0,1
	.word	643
	.byte	1,1,2,35,1,11
	.byte	'LXO',0,1
	.word	643
	.byte	1,0,2,35,1,11
	.byte	'WRPS',0,1
	.word	643
	.byte	1,7,2,35,2,11
	.byte	'WRPD',0,1
	.word	643
	.byte	1,6,2,35,2,11
	.byte	'ICH',0,1
	.word	643
	.byte	1,5,2,35,2,11
	.byte	'IPM',0,1
	.word	643
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,1
	.word	643
	.byte	2,2,2,35,2,11
	.byte	'BUFFER',0,1
	.word	643
	.byte	1,1,2,35,2,11
	.byte	'FROZEN',0,1
	.word	643
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	643
	.byte	8,0,2,35,3,0,29
	.byte	'Ifx_DMA_BLK_ME_CHSR_Bits',0,31,184,2,3
	.word	60814
	.byte	10
	.byte	'_Ifx_DMA_BLK_ME_DADR_Bits',0,31,187,2,16,4,11
	.byte	'DADR',0,4
	.word	468
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_DMA_BLK_ME_DADR_Bits',0,31,190,2,3
	.word	61081
	.byte	10
	.byte	'_Ifx_DMA_BLK_ME_R0_Bits',0,31,193,2,16,4,11
	.byte	'RD00',0,1
	.word	643
	.byte	8,0,2,35,0,11
	.byte	'RD01',0,1
	.word	643
	.byte	8,0,2,35,1,11
	.byte	'RD02',0,1
	.word	643
	.byte	8,0,2,35,2,11
	.byte	'RD03',0,1
	.word	643
	.byte	8,0,2,35,3,0,29
	.byte	'Ifx_DMA_BLK_ME_R0_Bits',0,31,199,2,3
	.word	61164
	.byte	10
	.byte	'_Ifx_DMA_BLK_ME_R1_Bits',0,31,202,2,16,4,11
	.byte	'RD10',0,1
	.word	643
	.byte	8,0,2,35,0,11
	.byte	'RD11',0,1
	.word	643
	.byte	8,0,2,35,1,11
	.byte	'RD12',0,1
	.word	643
	.byte	8,0,2,35,2,11
	.byte	'RD13',0,1
	.word	643
	.byte	8,0,2,35,3,0,29
	.byte	'Ifx_DMA_BLK_ME_R1_Bits',0,31,208,2,3
	.word	61291
	.byte	10
	.byte	'_Ifx_DMA_BLK_ME_R2_Bits',0,31,211,2,16,4,11
	.byte	'RD20',0,1
	.word	643
	.byte	8,0,2,35,0,11
	.byte	'RD21',0,1
	.word	643
	.byte	8,0,2,35,1,11
	.byte	'RD22',0,1
	.word	643
	.byte	8,0,2,35,2,11
	.byte	'RD23',0,1
	.word	643
	.byte	8,0,2,35,3,0,29
	.byte	'Ifx_DMA_BLK_ME_R2_Bits',0,31,217,2,3
	.word	61418
	.byte	10
	.byte	'_Ifx_DMA_BLK_ME_R3_Bits',0,31,220,2,16,4,11
	.byte	'RD30',0,1
	.word	643
	.byte	8,0,2,35,0,11
	.byte	'RD31',0,1
	.word	643
	.byte	8,0,2,35,1,11
	.byte	'RD32',0,1
	.word	643
	.byte	8,0,2,35,2,11
	.byte	'RD33',0,1
	.word	643
	.byte	8,0,2,35,3,0,29
	.byte	'Ifx_DMA_BLK_ME_R3_Bits',0,31,226,2,3
	.word	61545
	.byte	10
	.byte	'_Ifx_DMA_BLK_ME_R4_Bits',0,31,229,2,16,4,11
	.byte	'RD40',0,1
	.word	643
	.byte	8,0,2,35,0,11
	.byte	'RD41',0,1
	.word	643
	.byte	8,0,2,35,1,11
	.byte	'RD42',0,1
	.word	643
	.byte	8,0,2,35,2,11
	.byte	'RD43',0,1
	.word	643
	.byte	8,0,2,35,3,0,29
	.byte	'Ifx_DMA_BLK_ME_R4_Bits',0,31,235,2,3
	.word	61672
	.byte	10
	.byte	'_Ifx_DMA_BLK_ME_R5_Bits',0,31,238,2,16,4,11
	.byte	'RD50',0,1
	.word	643
	.byte	8,0,2,35,0,11
	.byte	'RD51',0,1
	.word	643
	.byte	8,0,2,35,1,11
	.byte	'RD52',0,1
	.word	643
	.byte	8,0,2,35,2,11
	.byte	'RD53',0,1
	.word	643
	.byte	8,0,2,35,3,0,29
	.byte	'Ifx_DMA_BLK_ME_R5_Bits',0,31,244,2,3
	.word	61799
	.byte	10
	.byte	'_Ifx_DMA_BLK_ME_R6_Bits',0,31,247,2,16,4,11
	.byte	'RD60',0,1
	.word	643
	.byte	8,0,2,35,0,11
	.byte	'RD61',0,1
	.word	643
	.byte	8,0,2,35,1,11
	.byte	'RD62',0,1
	.word	643
	.byte	8,0,2,35,2,11
	.byte	'RD63',0,1
	.word	643
	.byte	8,0,2,35,3,0,29
	.byte	'Ifx_DMA_BLK_ME_R6_Bits',0,31,253,2,3
	.word	61926
	.byte	10
	.byte	'_Ifx_DMA_BLK_ME_R7_Bits',0,31,128,3,16,4,11
	.byte	'RD70',0,1
	.word	643
	.byte	8,0,2,35,0,11
	.byte	'RD71',0,1
	.word	643
	.byte	8,0,2,35,1,11
	.byte	'RD72',0,1
	.word	643
	.byte	8,0,2,35,2,11
	.byte	'RD73',0,1
	.word	643
	.byte	8,0,2,35,3,0,29
	.byte	'Ifx_DMA_BLK_ME_R7_Bits',0,31,134,3,3
	.word	62053
	.byte	10
	.byte	'_Ifx_DMA_BLK_ME_RDCRC_Bits',0,31,137,3,16,4,11
	.byte	'RDCRC',0,4
	.word	468
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_DMA_BLK_ME_RDCRC_Bits',0,31,140,3,3
	.word	62180
	.byte	10
	.byte	'_Ifx_DMA_BLK_ME_SADR_Bits',0,31,143,3,16,4,11
	.byte	'SADR',0,4
	.word	468
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_DMA_BLK_ME_SADR_Bits',0,31,146,3,3
	.word	62266
	.byte	10
	.byte	'_Ifx_DMA_BLK_ME_SDCRC_Bits',0,31,149,3,16,4,11
	.byte	'SDCRC',0,4
	.word	468
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_DMA_BLK_ME_SDCRC_Bits',0,31,152,3,3
	.word	62349
	.byte	10
	.byte	'_Ifx_DMA_BLK_ME_SHADR_Bits',0,31,155,3,16,4,11
	.byte	'SHADR',0,4
	.word	468
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_DMA_BLK_ME_SHADR_Bits',0,31,158,3,3
	.word	62435
	.byte	10
	.byte	'_Ifx_DMA_BLK_ME_SR_Bits',0,31,161,3,16,4,11
	.byte	'RS',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	643
	.byte	3,4,2,35,0,11
	.byte	'WS',0,1
	.word	643
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,2
	.word	660
	.byte	11,0,2,35,0,11
	.byte	'CH',0,1
	.word	643
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	660
	.byte	9,0,2,35,2,0,29
	.byte	'Ifx_DMA_BLK_ME_SR_Bits',0,31,169,3,3
	.word	62521
	.byte	10
	.byte	'_Ifx_DMA_CH_ADICR_Bits',0,31,172,3,16,4,11
	.byte	'SMF',0,1
	.word	643
	.byte	3,5,2,35,0,11
	.byte	'INCS',0,1
	.word	643
	.byte	1,4,2,35,0,11
	.byte	'DMF',0,1
	.word	643
	.byte	3,1,2,35,0,11
	.byte	'INCD',0,1
	.word	643
	.byte	1,0,2,35,0,11
	.byte	'CBLS',0,1
	.word	643
	.byte	4,4,2,35,1,11
	.byte	'CBLD',0,1
	.word	643
	.byte	4,0,2,35,1,11
	.byte	'SHCT',0,1
	.word	643
	.byte	4,4,2,35,2,11
	.byte	'SCBE',0,1
	.word	643
	.byte	1,3,2,35,2,11
	.byte	'DCBE',0,1
	.word	643
	.byte	1,2,2,35,2,11
	.byte	'STAMP',0,1
	.word	643
	.byte	1,1,2,35,2,11
	.byte	'ETRL',0,1
	.word	643
	.byte	1,0,2,35,2,11
	.byte	'WRPSE',0,1
	.word	643
	.byte	1,7,2,35,3,11
	.byte	'WRPDE',0,1
	.word	643
	.byte	1,6,2,35,3,11
	.byte	'INTCT',0,1
	.word	643
	.byte	2,4,2,35,3,11
	.byte	'IRDV',0,1
	.word	643
	.byte	4,0,2,35,3,0,29
	.byte	'Ifx_DMA_CH_ADICR_Bits',0,31,189,3,3
	.word	62693
	.byte	10
	.byte	'_Ifx_DMA_CH_CHCFGR_Bits',0,31,192,3,16,4,11
	.byte	'TREL',0,2
	.word	660
	.byte	14,2,2,35,0,11
	.byte	'reserved_14',0,1
	.word	643
	.byte	2,0,2,35,1,11
	.byte	'BLKM',0,1
	.word	643
	.byte	3,5,2,35,2,11
	.byte	'RROAT',0,1
	.word	643
	.byte	1,4,2,35,2,11
	.byte	'CHMODE',0,1
	.word	643
	.byte	1,3,2,35,2,11
	.byte	'CHDW',0,1
	.word	643
	.byte	3,0,2,35,2,11
	.byte	'PATSEL',0,1
	.word	643
	.byte	3,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	643
	.byte	1,4,2,35,3,11
	.byte	'PRSEL',0,1
	.word	643
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	643
	.byte	1,2,2,35,3,11
	.byte	'DMAPRIO',0,1
	.word	643
	.byte	2,0,2,35,3,0,29
	.byte	'Ifx_DMA_CH_CHCFGR_Bits',0,31,205,3,3
	.word	62996
	.byte	10
	.byte	'_Ifx_DMA_CH_CHCSR_Bits',0,31,208,3,16,4,11
	.byte	'TCOUNT',0,2
	.word	660
	.byte	14,2,2,35,0,11
	.byte	'reserved_14',0,1
	.word	643
	.byte	1,1,2,35,1,11
	.byte	'LXO',0,1
	.word	643
	.byte	1,0,2,35,1,11
	.byte	'WRPS',0,1
	.word	643
	.byte	1,7,2,35,2,11
	.byte	'WRPD',0,1
	.word	643
	.byte	1,6,2,35,2,11
	.byte	'ICH',0,1
	.word	643
	.byte	1,5,2,35,2,11
	.byte	'IPM',0,1
	.word	643
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,1
	.word	643
	.byte	2,2,2,35,2,11
	.byte	'BUFFER',0,1
	.word	643
	.byte	1,1,2,35,2,11
	.byte	'FROZEN',0,1
	.word	643
	.byte	1,0,2,35,2,11
	.byte	'SWB',0,1
	.word	643
	.byte	1,7,2,35,3,11
	.byte	'CWRP',0,1
	.word	643
	.byte	1,6,2,35,3,11
	.byte	'CICH',0,1
	.word	643
	.byte	1,5,2,35,3,11
	.byte	'SIT',0,1
	.word	643
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	643
	.byte	3,1,2,35,3,11
	.byte	'SCH',0,1
	.word	643
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_DMA_CH_CHCSR_Bits',0,31,226,3,3
	.word	63265
	.byte	10
	.byte	'_Ifx_DMA_CH_DADR_Bits',0,31,229,3,16,4,11
	.byte	'DADR',0,4
	.word	468
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_DMA_CH_DADR_Bits',0,31,232,3,3
	.word	63603
	.byte	10
	.byte	'_Ifx_DMA_CH_RDCRCR_Bits',0,31,235,3,16,4,11
	.byte	'RDCRC',0,4
	.word	468
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_DMA_CH_RDCRCR_Bits',0,31,238,3,3
	.word	63678
	.byte	10
	.byte	'_Ifx_DMA_CH_SADR_Bits',0,31,241,3,16,4,11
	.byte	'SADR',0,4
	.word	468
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_DMA_CH_SADR_Bits',0,31,244,3,3
	.word	63758
	.byte	10
	.byte	'_Ifx_DMA_CH_SDCRCR_Bits',0,31,247,3,16,4,11
	.byte	'SDCRC',0,4
	.word	468
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_DMA_CH_SDCRCR_Bits',0,31,250,3,3
	.word	63833
	.byte	10
	.byte	'_Ifx_DMA_CH_SHADR_Bits',0,31,253,3,16,4,11
	.byte	'SHADR',0,4
	.word	468
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_DMA_CH_SHADR_Bits',0,31,128,4,3
	.word	63913
	.byte	10
	.byte	'_Ifx_DMA_CLC_Bits',0,31,131,4,16,4,11
	.byte	'DISR',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'DISS',0,1
	.word	643
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	643
	.byte	1,5,2,35,0,11
	.byte	'EDIS',0,1
	.word	643
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	468
	.byte	28,0,2,35,0,0,29
	.byte	'Ifx_DMA_CLC_Bits',0,31,138,4,3
	.word	63991
	.byte	10
	.byte	'_Ifx_DMA_ERRINTR_Bits',0,31,141,4,16,4,11
	.byte	'SIT',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	468
	.byte	31,0,2,35,0,0,29
	.byte	'Ifx_DMA_ERRINTR_Bits',0,31,145,4,3
	.word	64134
	.byte	10
	.byte	'_Ifx_DMA_HRR_Bits',0,31,148,4,16,4,11
	.byte	'HRP',0,1
	.word	643
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	468
	.byte	30,0,2,35,0,0,29
	.byte	'Ifx_DMA_HRR_Bits',0,31,152,4,3
	.word	64230
	.byte	10
	.byte	'_Ifx_DMA_ID_Bits',0,31,155,4,16,4,11
	.byte	'MODREV',0,1
	.word	643
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	643
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	660
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_DMA_ID_Bits',0,31,160,4,3
	.word	64318
	.byte	10
	.byte	'_Ifx_DMA_MEMCON_Bits',0,31,163,4,16,4,11
	.byte	'reserved_0',0,4
	.word	491
	.byte	2,30,2,35,0,11
	.byte	'INTERR',0,4
	.word	491
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	491
	.byte	1,28,2,35,0,11
	.byte	'RMWERR',0,4
	.word	491
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	491
	.byte	1,26,2,35,0,11
	.byte	'DATAERR',0,4
	.word	491
	.byte	1,25,2,35,0,11
	.byte	'reserved_7',0,4
	.word	491
	.byte	1,24,2,35,0,11
	.byte	'PMIC',0,4
	.word	491
	.byte	1,23,2,35,0,11
	.byte	'ERRDIS',0,4
	.word	491
	.byte	1,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	491
	.byte	22,0,2,35,0,0,29
	.byte	'Ifx_DMA_MEMCON_Bits',0,31,175,4,3
	.word	64425
	.byte	10
	.byte	'_Ifx_DMA_MODE_Bits',0,31,178,4,16,4,11
	.byte	'MODE',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	468
	.byte	31,0,2,35,0,0,29
	.byte	'Ifx_DMA_MODE_Bits',0,31,182,4,3
	.word	64682
	.byte	10
	.byte	'_Ifx_DMA_OTSS_Bits',0,31,185,4,16,4,11
	.byte	'TGS',0,1
	.word	643
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	643
	.byte	3,1,2,35,0,11
	.byte	'BS',0,1
	.word	643
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	468
	.byte	24,0,2,35,0,0,29
	.byte	'Ifx_DMA_OTSS_Bits',0,31,191,4,3
	.word	64773
	.byte	10
	.byte	'_Ifx_DMA_PRR0_Bits',0,31,194,4,16,4,11
	.byte	'PAT00',0,1
	.word	643
	.byte	8,0,2,35,0,11
	.byte	'PAT01',0,1
	.word	643
	.byte	8,0,2,35,1,11
	.byte	'PAT02',0,1
	.word	643
	.byte	8,0,2,35,2,11
	.byte	'PAT03',0,1
	.word	643
	.byte	8,0,2,35,3,0,29
	.byte	'Ifx_DMA_PRR0_Bits',0,31,200,4,3
	.word	64899
	.byte	10
	.byte	'_Ifx_DMA_PRR1_Bits',0,31,203,4,16,4,11
	.byte	'PAT10',0,1
	.word	643
	.byte	8,0,2,35,0,11
	.byte	'PAT11',0,1
	.word	643
	.byte	8,0,2,35,1,11
	.byte	'PAT12',0,1
	.word	643
	.byte	8,0,2,35,2,11
	.byte	'PAT13',0,1
	.word	643
	.byte	8,0,2,35,3,0,29
	.byte	'Ifx_DMA_PRR1_Bits',0,31,209,4,3
	.word	65020
	.byte	10
	.byte	'_Ifx_DMA_SUSACR_Bits',0,31,212,4,16,4,11
	.byte	'SUSAC',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	468
	.byte	31,0,2,35,0,0,29
	.byte	'Ifx_DMA_SUSACR_Bits',0,31,216,4,3
	.word	65141
	.byte	10
	.byte	'_Ifx_DMA_SUSENR_Bits',0,31,219,4,16,4,11
	.byte	'SUSEN',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	468
	.byte	31,0,2,35,0,0,29
	.byte	'Ifx_DMA_SUSENR_Bits',0,31,223,4,3
	.word	65237
	.byte	10
	.byte	'_Ifx_DMA_TIME_Bits',0,31,226,4,16,4,11
	.byte	'COUNT',0,4
	.word	468
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_DMA_TIME_Bits',0,31,229,4,3
	.word	65333
	.byte	10
	.byte	'_Ifx_DMA_TSR_Bits',0,31,232,4,16,4,11
	.byte	'RST',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'HTRE',0,1
	.word	643
	.byte	1,6,2,35,0,11
	.byte	'TRL',0,1
	.word	643
	.byte	1,5,2,35,0,11
	.byte	'CH',0,1
	.word	643
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	643
	.byte	4,0,2,35,0,11
	.byte	'HLTREQ',0,1
	.word	643
	.byte	1,7,2,35,1,11
	.byte	'HLTACK',0,1
	.word	643
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	643
	.byte	6,0,2,35,1,11
	.byte	'ECH',0,1
	.word	643
	.byte	1,7,2,35,2,11
	.byte	'DCH',0,1
	.word	643
	.byte	1,6,2,35,2,11
	.byte	'CTL',0,1
	.word	643
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	643
	.byte	5,0,2,35,2,11
	.byte	'HLTCLR',0,1
	.word	643
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	643
	.byte	7,0,2,35,3,0,29
	.byte	'Ifx_DMA_TSR_Bits',0,31,248,4,3
	.word	65403
	.byte	12,31,128,5,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56897
	.byte	4,2,35,0,0,29
	.byte	'Ifx_DMA_ACCEN00',0,31,133,5,3
	.word	65704
	.byte	12,31,136,5,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57456
	.byte	4,2,35,0,0,29
	.byte	'Ifx_DMA_ACCEN01',0,31,141,5,3
	.word	65769
	.byte	12,31,144,5,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57535
	.byte	4,2,35,0,0,29
	.byte	'Ifx_DMA_ACCEN10',0,31,149,5,3
	.word	65834
	.byte	12,31,152,5,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58094
	.byte	4,2,35,0,0,29
	.byte	'Ifx_DMA_ACCEN11',0,31,157,5,3
	.word	65899
	.byte	12,31,160,5,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58174
	.byte	4,2,35,0,0,29
	.byte	'Ifx_DMA_ACCEN20',0,31,165,5,3
	.word	65964
	.byte	12,31,168,5,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58735
	.byte	4,2,35,0,0,29
	.byte	'Ifx_DMA_ACCEN21',0,31,173,5,3
	.word	66029
	.byte	12,31,176,5,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58816
	.byte	4,2,35,0,0,29
	.byte	'Ifx_DMA_ACCEN30',0,31,181,5,3
	.word	66094
	.byte	12,31,184,5,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59377
	.byte	4,2,35,0,0,29
	.byte	'Ifx_DMA_ACCEN31',0,31,189,5,3
	.word	66159
	.byte	12,31,192,5,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59458
	.byte	4,2,35,0,0,29
	.byte	'Ifx_DMA_BLK_CLRE',0,31,197,5,3
	.word	66224
	.byte	12,31,200,5,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59732
	.byte	4,2,35,0,0,29
	.byte	'Ifx_DMA_BLK_EER',0,31,205,5,3
	.word	66290
	.byte	12,31,208,5,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59946
	.byte	4,2,35,0,0,29
	.byte	'Ifx_DMA_BLK_ERRSR',0,31,213,5,3
	.word	66355
	.byte	12,31,216,5,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60230
	.byte	4,2,35,0,0,29
	.byte	'Ifx_DMA_BLK_ME_ADICR',0,31,221,5,3
	.word	66422
	.byte	12,31,224,5,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60541
	.byte	4,2,35,0,0,29
	.byte	'Ifx_DMA_BLK_ME_CHCR',0,31,229,5,3
	.word	66492
	.byte	12,31,232,5,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60814
	.byte	4,2,35,0,0,29
	.byte	'Ifx_DMA_BLK_ME_CHSR',0,31,237,5,3
	.word	66561
	.byte	12,31,240,5,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61081
	.byte	4,2,35,0,0,29
	.byte	'Ifx_DMA_BLK_ME_DADR',0,31,245,5,3
	.word	66630
	.byte	12,31,248,5,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61164
	.byte	4,2,35,0,0,29
	.byte	'Ifx_DMA_BLK_ME_R0',0,31,253,5,3
	.word	66699
	.byte	12,31,128,6,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61291
	.byte	4,2,35,0,0,29
	.byte	'Ifx_DMA_BLK_ME_R1',0,31,133,6,3
	.word	66766
	.byte	12,31,136,6,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61418
	.byte	4,2,35,0,0,29
	.byte	'Ifx_DMA_BLK_ME_R2',0,31,141,6,3
	.word	66833
	.byte	12,31,144,6,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61545
	.byte	4,2,35,0,0,29
	.byte	'Ifx_DMA_BLK_ME_R3',0,31,149,6,3
	.word	66900
	.byte	12,31,152,6,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61672
	.byte	4,2,35,0,0,29
	.byte	'Ifx_DMA_BLK_ME_R4',0,31,157,6,3
	.word	66967
	.byte	12,31,160,6,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61799
	.byte	4,2,35,0,0,29
	.byte	'Ifx_DMA_BLK_ME_R5',0,31,165,6,3
	.word	67034
	.byte	12,31,168,6,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61926
	.byte	4,2,35,0,0,29
	.byte	'Ifx_DMA_BLK_ME_R6',0,31,173,6,3
	.word	67101
	.byte	12,31,176,6,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62053
	.byte	4,2,35,0,0,29
	.byte	'Ifx_DMA_BLK_ME_R7',0,31,181,6,3
	.word	67168
	.byte	12,31,184,6,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62180
	.byte	4,2,35,0,0,29
	.byte	'Ifx_DMA_BLK_ME_RDCRC',0,31,189,6,3
	.word	67235
	.byte	12,31,192,6,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62266
	.byte	4,2,35,0,0,29
	.byte	'Ifx_DMA_BLK_ME_SADR',0,31,197,6,3
	.word	67305
	.byte	12,31,200,6,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62349
	.byte	4,2,35,0,0,29
	.byte	'Ifx_DMA_BLK_ME_SDCRC',0,31,205,6,3
	.word	67374
	.byte	12,31,208,6,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62435
	.byte	4,2,35,0,0,29
	.byte	'Ifx_DMA_BLK_ME_SHADR',0,31,213,6,3
	.word	67444
	.byte	12,31,216,6,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62521
	.byte	4,2,35,0,0,29
	.byte	'Ifx_DMA_BLK_ME_SR',0,31,221,6,3
	.word	67514
	.byte	12,31,224,6,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62693
	.byte	4,2,35,0,0,29
	.byte	'Ifx_DMA_CH_ADICR',0,31,229,6,3
	.word	67581
	.byte	12,31,232,6,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62996
	.byte	4,2,35,0,0,29
	.byte	'Ifx_DMA_CH_CHCFGR',0,31,237,6,3
	.word	67647
	.byte	12,31,240,6,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63265
	.byte	4,2,35,0,0,29
	.byte	'Ifx_DMA_CH_CHCSR',0,31,245,6,3
	.word	67714
	.byte	12,31,248,6,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63603
	.byte	4,2,35,0,0,29
	.byte	'Ifx_DMA_CH_DADR',0,31,253,6,3
	.word	67780
	.byte	12,31,128,7,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63678
	.byte	4,2,35,0,0,29
	.byte	'Ifx_DMA_CH_RDCRCR',0,31,133,7,3
	.word	67845
	.byte	12,31,136,7,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63758
	.byte	4,2,35,0,0,29
	.byte	'Ifx_DMA_CH_SADR',0,31,141,7,3
	.word	67912
	.byte	12,31,144,7,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63833
	.byte	4,2,35,0,0,29
	.byte	'Ifx_DMA_CH_SDCRCR',0,31,149,7,3
	.word	67977
	.byte	12,31,152,7,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63913
	.byte	4,2,35,0,0,29
	.byte	'Ifx_DMA_CH_SHADR',0,31,157,7,3
	.word	68044
	.byte	12,31,160,7,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63991
	.byte	4,2,35,0,0,29
	.byte	'Ifx_DMA_CLC',0,31,165,7,3
	.word	68110
	.byte	12,31,168,7,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64134
	.byte	4,2,35,0,0,29
	.byte	'Ifx_DMA_ERRINTR',0,31,173,7,3
	.word	68171
	.byte	12,31,176,7,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64230
	.byte	4,2,35,0,0,29
	.byte	'Ifx_DMA_HRR',0,31,181,7,3
	.word	68236
	.byte	12,31,184,7,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64318
	.byte	4,2,35,0,0,29
	.byte	'Ifx_DMA_ID',0,31,189,7,3
	.word	68297
	.byte	12,31,192,7,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64425
	.byte	4,2,35,0,0,29
	.byte	'Ifx_DMA_MEMCON',0,31,197,7,3
	.word	68357
	.byte	12,31,200,7,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64682
	.byte	4,2,35,0,0,29
	.byte	'Ifx_DMA_MODE',0,31,205,7,3
	.word	68421
	.byte	12,31,208,7,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64773
	.byte	4,2,35,0,0,29
	.byte	'Ifx_DMA_OTSS',0,31,213,7,3
	.word	68483
	.byte	12,31,216,7,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64899
	.byte	4,2,35,0,0,29
	.byte	'Ifx_DMA_PRR0',0,31,221,7,3
	.word	68545
	.byte	12,31,224,7,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65020
	.byte	4,2,35,0,0,29
	.byte	'Ifx_DMA_PRR1',0,31,229,7,3
	.word	68607
	.byte	12,31,232,7,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65141
	.byte	4,2,35,0,0,29
	.byte	'Ifx_DMA_SUSACR',0,31,237,7,3
	.word	68669
	.byte	12,31,240,7,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65237
	.byte	4,2,35,0,0,29
	.byte	'Ifx_DMA_SUSENR',0,31,245,7,3
	.word	68733
	.byte	12,31,248,7,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65333
	.byte	4,2,35,0,0,29
	.byte	'Ifx_DMA_TIME',0,31,253,7,3
	.word	68797
	.byte	12,31,128,8,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65403
	.byte	4,2,35,0,0,29
	.byte	'Ifx_DMA_TSR',0,31,133,8,3
	.word	68859
	.byte	15,32
	.word	643
	.byte	16,31,0,10
	.byte	'_Ifx_DMA_BLK_ME',0,31,144,8,25,112,13
	.byte	'SR',0
	.word	67514
	.byte	4,2,35,0,13
	.byte	'reserved_4',0
	.word	4800
	.byte	12,2,35,4,13
	.byte	'R0',0
	.word	66699
	.byte	4,2,35,16,13
	.byte	'R1',0
	.word	66766
	.byte	4,2,35,20,13
	.byte	'R2',0
	.word	66833
	.byte	4,2,35,24,13
	.byte	'R3',0
	.word	66900
	.byte	4,2,35,28,13
	.byte	'R4',0
	.word	66967
	.byte	4,2,35,32,13
	.byte	'R5',0
	.word	67034
	.byte	4,2,35,36,13
	.byte	'R6',0
	.word	67101
	.byte	4,2,35,40,13
	.byte	'R7',0
	.word	67168
	.byte	4,2,35,44,13
	.byte	'reserved_30',0
	.word	68920
	.byte	32,2,35,48,13
	.byte	'RDCRC',0
	.word	67235
	.byte	4,2,35,80,13
	.byte	'SDCRC',0
	.word	67374
	.byte	4,2,35,84,13
	.byte	'SADR',0
	.word	67305
	.byte	4,2,35,88,13
	.byte	'DADR',0
	.word	66630
	.byte	4,2,35,92,13
	.byte	'ADICR',0
	.word	66422
	.byte	4,2,35,96,13
	.byte	'CHCR',0
	.word	66492
	.byte	4,2,35,100,13
	.byte	'SHADR',0
	.word	67444
	.byte	4,2,35,104,13
	.byte	'CHSR',0
	.word	66561
	.byte	4,2,35,108,0,14
	.word	68929
	.byte	29
	.byte	'Ifx_DMA_BLK_ME',0,31,165,8,3
	.word	69217
	.byte	14
	.word	68929
	.byte	10
	.byte	'_Ifx_DMA_BLK',0,31,178,8,25,128,1,13
	.byte	'EER',0
	.word	66290
	.byte	4,2,35,0,13
	.byte	'ERRSR',0
	.word	66355
	.byte	4,2,35,4,13
	.byte	'CLRE',0
	.word	66224
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	2641
	.byte	4,2,35,12,13
	.byte	'ME',0
	.word	69246
	.byte	112,2,35,16,0,14
	.word	69251
	.byte	29
	.byte	'Ifx_DMA_BLK',0,31,185,8,3
	.word	69346
	.byte	10
	.byte	'_Ifx_DMA_CH',0,31,188,8,25,32,13
	.byte	'RDCRCR',0
	.word	67845
	.byte	4,2,35,0,13
	.byte	'SDCRCR',0
	.word	67977
	.byte	4,2,35,4,13
	.byte	'SADR',0
	.word	67912
	.byte	4,2,35,8,13
	.byte	'DADR',0
	.word	67780
	.byte	4,2,35,12,13
	.byte	'ADICR',0
	.word	67581
	.byte	4,2,35,16,13
	.byte	'CHCFGR',0
	.word	67647
	.byte	4,2,35,20,13
	.byte	'SHADR',0
	.word	68044
	.byte	4,2,35,24,13
	.byte	'CHCSR',0
	.word	67714
	.byte	4,2,35,28,0,14
	.word	69372
	.byte	29
	.byte	'Ifx_DMA_CH',0,31,198,8,3
	.word	69512
	.byte	15,192,1
	.word	643
	.byte	16,191,1,0,14
	.word	69251
	.byte	15,128,31
	.word	643
	.byte	16,255,30,0,14
	.word	69251
	.byte	15,96
	.word	643
	.byte	16,95,0,15,236,1
	.word	643
	.byte	16,235,1,0,15,16
	.word	68421
	.byte	16,3,0,15,240,9
	.word	643
	.byte	16,239,9,0,15,192,1
	.word	68236
	.byte	16,47,0,15,192,2
	.word	643
	.byte	16,191,2,0,15,192,1
	.word	68733
	.byte	16,47,0,15,192,1
	.word	68669
	.byte	16,47,0,15,192,1
	.word	68859
	.byte	16,47,0,15,128,12
	.word	69372
	.byte	16,47,0,14
	.word	69660
	.byte	15,128,52
	.word	643
	.byte	16,255,51,0,10
	.byte	'_Ifx_DMA',0,31,211,8,25,128,128,1,13
	.byte	'CLC',0
	.word	68110
	.byte	4,2,35,0,13
	.byte	'reserved_4',0
	.word	2641
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	68297
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	54093
	.byte	20,2,35,12,13
	.byte	'MEMCON',0
	.word	68357
	.byte	4,2,35,32,13
	.byte	'reserved_24',0
	.word	54139
	.byte	28,2,35,36,13
	.byte	'ACCEN00',0
	.word	65704
	.byte	4,2,35,64,13
	.byte	'ACCEN01',0
	.word	65769
	.byte	4,2,35,68,13
	.byte	'ACCEN10',0
	.word	65834
	.byte	4,2,35,72,13
	.byte	'ACCEN11',0
	.word	65899
	.byte	4,2,35,76,13
	.byte	'ACCEN20',0
	.word	65964
	.byte	4,2,35,80,13
	.byte	'ACCEN21',0
	.word	66029
	.byte	4,2,35,84,13
	.byte	'ACCEN30',0
	.word	66094
	.byte	4,2,35,88,13
	.byte	'ACCEN31',0
	.word	66159
	.byte	4,2,35,92,13
	.byte	'reserved_60',0
	.word	69537
	.byte	192,1,2,35,96,13
	.byte	'BLK0',0
	.word	69548
	.byte	128,1,3,35,160,2,13
	.byte	'reserved_1A0',0
	.word	69553
	.byte	128,31,3,35,160,3,13
	.byte	'BLK1',0
	.word	69564
	.byte	128,1,3,35,160,34,13
	.byte	'reserved_11A0',0
	.word	69569
	.byte	96,3,35,160,35,13
	.byte	'OTSS',0
	.word	68483
	.byte	4,3,35,128,36,13
	.byte	'ERRINTR',0
	.word	68171
	.byte	4,3,35,132,36,13
	.byte	'PRR0',0
	.word	68545
	.byte	4,3,35,136,36,13
	.byte	'PRR1',0
	.word	68607
	.byte	4,3,35,140,36,13
	.byte	'TIME',0
	.word	68797
	.byte	4,3,35,144,36,13
	.byte	'reserved_1214',0
	.word	69578
	.byte	236,1,3,35,148,36,13
	.byte	'MODE',0
	.word	69589
	.byte	16,3,35,128,38,13
	.byte	'reserved_1310',0
	.word	69598
	.byte	240,9,3,35,144,38,13
	.byte	'HRR',0
	.word	69609
	.byte	192,1,3,35,128,48,13
	.byte	'reserved_18C0',0
	.word	69619
	.byte	192,2,3,35,192,49,13
	.byte	'SUSENR',0
	.word	69630
	.byte	192,1,3,35,128,52,13
	.byte	'reserved_1AC0',0
	.word	69619
	.byte	192,2,3,35,192,53,13
	.byte	'SUSACR',0
	.word	69640
	.byte	192,1,3,35,128,56,13
	.byte	'reserved_1CC0',0
	.word	69619
	.byte	192,2,3,35,192,57,13
	.byte	'TSR',0
	.word	69650
	.byte	192,1,3,35,128,60,13
	.byte	'reserved_1EC0',0
	.word	69619
	.byte	192,2,3,35,192,61,13
	.byte	'CH',0
	.word	69670
	.byte	128,12,3,35,128,64,13
	.byte	'reserved_2600',0
	.word	69675
	.byte	128,52,3,35,128,76,0,14
	.word	69686
	.byte	29
	.byte	'Ifx_DMA',0,31,250,8,3
	.word	70392
	.byte	17,32,69,9,1,18
	.byte	'IfxSrc_Tos_cpu0',0,0,18
	.byte	'IfxSrc_Tos_cpu1',0,1,18
	.byte	'IfxSrc_Tos_dma',0,3,0,29
	.byte	'IfxSrc_Tos',0,32,74,3
	.word	70414
	.byte	29
	.byte	'Ifx_SRC_SRCR_Bits',0,8,62,3
	.word	10162
	.byte	29
	.byte	'Ifx_SRC_SRCR',0,8,75,3
	.word	10452
	.byte	10
	.byte	'_Ifx_SRC_AGBT',0,8,86,25,4,13
	.byte	'SR',0
	.word	10452
	.byte	4,2,35,0,0,14
	.word	70539
	.byte	29
	.byte	'Ifx_SRC_AGBT',0,8,89,3
	.word	70571
	.byte	10
	.byte	'_Ifx_SRC_ASCLIN',0,8,92,25,12,13
	.byte	'TX',0
	.word	10452
	.byte	4,2,35,0,13
	.byte	'RX',0
	.word	10452
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	10452
	.byte	4,2,35,8,0,14
	.word	70597
	.byte	29
	.byte	'Ifx_SRC_ASCLIN',0,8,97,3
	.word	70656
	.byte	10
	.byte	'_Ifx_SRC_BCUSPB',0,8,100,25,4,13
	.byte	'SBSRC',0
	.word	10452
	.byte	4,2,35,0,0,14
	.word	70684
	.byte	29
	.byte	'Ifx_SRC_BCUSPB',0,8,103,3
	.word	70721
	.byte	15,64
	.word	10452
	.byte	16,15,0,10
	.byte	'_Ifx_SRC_CAN',0,8,106,25,64,13
	.byte	'INT',0
	.word	70749
	.byte	64,2,35,0,0,14
	.word	70758
	.byte	29
	.byte	'Ifx_SRC_CAN',0,8,109,3
	.word	70790
	.byte	10
	.byte	'_Ifx_SRC_CCU6',0,8,112,25,16,13
	.byte	'SR0',0
	.word	10452
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10452
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	10452
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	10452
	.byte	4,2,35,12,0,14
	.word	70815
	.byte	29
	.byte	'Ifx_SRC_CCU6',0,8,118,3
	.word	70887
	.byte	15,8
	.word	10452
	.byte	16,1,0,10
	.byte	'_Ifx_SRC_CERBERUS',0,8,121,25,8,13
	.byte	'SR',0
	.word	70913
	.byte	8,2,35,0,0,14
	.word	70922
	.byte	29
	.byte	'Ifx_SRC_CERBERUS',0,8,124,3
	.word	70958
	.byte	10
	.byte	'_Ifx_SRC_CIF',0,8,127,25,16,13
	.byte	'MI',0
	.word	10452
	.byte	4,2,35,0,13
	.byte	'MIEP',0
	.word	10452
	.byte	4,2,35,4,13
	.byte	'ISP',0
	.word	10452
	.byte	4,2,35,8,13
	.byte	'MJPEG',0
	.word	10452
	.byte	4,2,35,12,0,14
	.word	70988
	.byte	29
	.byte	'Ifx_SRC_CIF',0,8,133,1,3
	.word	71061
	.byte	10
	.byte	'_Ifx_SRC_CPU',0,8,136,1,25,4,13
	.byte	'SBSRC',0
	.word	10452
	.byte	4,2,35,0,0,14
	.word	71087
	.byte	29
	.byte	'Ifx_SRC_CPU',0,8,139,1,3
	.word	71122
	.byte	15,192,1
	.word	10452
	.byte	16,47,0,10
	.byte	'_Ifx_SRC_DMA',0,8,142,1,25,208,1,13
	.byte	'ERR',0
	.word	10452
	.byte	4,2,35,0,13
	.byte	'reserved_4',0
	.word	4800
	.byte	12,2,35,4,13
	.byte	'CH',0
	.word	71148
	.byte	192,1,2,35,16,0,14
	.word	71158
	.byte	29
	.byte	'Ifx_SRC_DMA',0,8,147,1,3
	.word	71225
	.byte	10
	.byte	'_Ifx_SRC_DSADC',0,8,150,1,25,8,13
	.byte	'SRM',0
	.word	10452
	.byte	4,2,35,0,13
	.byte	'SRA',0
	.word	10452
	.byte	4,2,35,4,0,14
	.word	71251
	.byte	29
	.byte	'Ifx_SRC_DSADC',0,8,154,1,3
	.word	71299
	.byte	10
	.byte	'_Ifx_SRC_EMEM',0,8,157,1,25,4,13
	.byte	'SR',0
	.word	10452
	.byte	4,2,35,0,0,14
	.word	71327
	.byte	29
	.byte	'Ifx_SRC_EMEM',0,8,160,1,3
	.word	71360
	.byte	10
	.byte	'_Ifx_SRC_ERAY',0,8,163,1,25,80,13
	.byte	'INT',0
	.word	70913
	.byte	8,2,35,0,13
	.byte	'TINT',0
	.word	70913
	.byte	8,2,35,8,13
	.byte	'NDAT',0
	.word	70913
	.byte	8,2,35,16,13
	.byte	'MBSC',0
	.word	70913
	.byte	8,2,35,24,13
	.byte	'OBUSY',0
	.word	10452
	.byte	4,2,35,32,13
	.byte	'IBUSY',0
	.word	10452
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	54148
	.byte	40,2,35,40,0,14
	.word	71387
	.byte	29
	.byte	'Ifx_SRC_ERAY',0,8,172,1,3
	.word	71514
	.byte	10
	.byte	'_Ifx_SRC_ETH',0,8,175,1,25,4,13
	.byte	'SR',0
	.word	10452
	.byte	4,2,35,0,0,14
	.word	71541
	.byte	29
	.byte	'Ifx_SRC_ETH',0,8,178,1,3
	.word	71573
	.byte	10
	.byte	'_Ifx_SRC_FCE',0,8,181,1,25,4,13
	.byte	'SR',0
	.word	10452
	.byte	4,2,35,0,0,14
	.word	71599
	.byte	29
	.byte	'Ifx_SRC_FCE',0,8,184,1,3
	.word	71631
	.byte	10
	.byte	'_Ifx_SRC_FFT',0,8,187,1,25,12,13
	.byte	'DONE',0
	.word	10452
	.byte	4,2,35,0,13
	.byte	'ERR',0
	.word	10452
	.byte	4,2,35,4,13
	.byte	'RFS',0
	.word	10452
	.byte	4,2,35,8,0,14
	.word	71657
	.byte	29
	.byte	'Ifx_SRC_FFT',0,8,192,1,3
	.word	71717
	.byte	10
	.byte	'_Ifx_SRC_GPSR',0,8,195,1,25,32,13
	.byte	'SR0',0
	.word	10452
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10452
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	10452
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	10452
	.byte	4,2,35,12,13
	.byte	'reserved_10',0
	.word	54130
	.byte	16,2,35,16,0,14
	.word	71743
	.byte	29
	.byte	'Ifx_SRC_GPSR',0,8,202,1,3
	.word	71837
	.byte	10
	.byte	'_Ifx_SRC_GPT12',0,8,205,1,25,48,13
	.byte	'CIRQ',0
	.word	10452
	.byte	4,2,35,0,13
	.byte	'T2',0
	.word	10452
	.byte	4,2,35,4,13
	.byte	'T3',0
	.word	10452
	.byte	4,2,35,8,13
	.byte	'T4',0
	.word	10452
	.byte	4,2,35,12,13
	.byte	'T5',0
	.word	10452
	.byte	4,2,35,16,13
	.byte	'T6',0
	.word	10452
	.byte	4,2,35,20,13
	.byte	'reserved_18',0
	.word	3831
	.byte	24,2,35,24,0,14
	.word	71864
	.byte	29
	.byte	'Ifx_SRC_GPT12',0,8,214,1,3
	.word	71981
	.byte	15,12
	.word	10452
	.byte	16,2,0,15,32
	.word	10452
	.byte	16,7,0,15,32
	.word	72018
	.byte	16,0,0,15,88
	.word	643
	.byte	16,87,0,15,108
	.word	10452
	.byte	16,26,0,15,96
	.word	72018
	.byte	16,2,0,15,160,3
	.word	643
	.byte	16,159,3,0,15,64
	.word	72018
	.byte	16,1,0,15,192,3
	.word	643
	.byte	16,191,3,0,15,16
	.word	10452
	.byte	16,3,0,15,64
	.word	72094
	.byte	16,3,0,15,52
	.word	643
	.byte	16,51,0,10
	.byte	'_Ifx_SRC_GTM',0,8,217,1,25,204,18,13
	.byte	'AEIIRQ',0
	.word	10452
	.byte	4,2,35,0,13
	.byte	'ARUIRQ',0
	.word	72009
	.byte	12,2,35,4,13
	.byte	'reserved_10',0
	.word	2641
	.byte	4,2,35,16,13
	.byte	'BRCIRQ',0
	.word	10452
	.byte	4,2,35,20,13
	.byte	'CMPIRQ',0
	.word	10452
	.byte	4,2,35,24,13
	.byte	'SPEIRQ',0
	.word	70913
	.byte	8,2,35,28,13
	.byte	'reserved_24',0
	.word	4460
	.byte	8,2,35,36,13
	.byte	'PSM',0
	.word	72027
	.byte	32,2,35,44,13
	.byte	'reserved_4C',0
	.word	72036
	.byte	88,2,35,76,13
	.byte	'DPLL',0
	.word	72045
	.byte	108,3,35,164,1,13
	.byte	'reserved_110',0
	.word	69569
	.byte	96,3,35,144,2,13
	.byte	'ERR',0
	.word	10452
	.byte	4,3,35,240,2,13
	.byte	'reserved_174',0
	.word	4800
	.byte	12,3,35,244,2,13
	.byte	'TIM',0
	.word	72054
	.byte	96,3,35,128,3,13
	.byte	'reserved_1E0',0
	.word	72063
	.byte	160,3,3,35,224,3,13
	.byte	'MCS',0
	.word	72054
	.byte	96,3,35,128,7,13
	.byte	'reserved_3E0',0
	.word	72063
	.byte	160,3,3,35,224,7,13
	.byte	'TOM',0
	.word	72074
	.byte	64,3,35,128,11,13
	.byte	'reserved_5C0',0
	.word	72083
	.byte	192,3,3,35,192,11,13
	.byte	'ATOM',0
	.word	72103
	.byte	64,3,35,128,15,13
	.byte	'reserved_7C0',0
	.word	69619
	.byte	192,2,3,35,192,15,13
	.byte	'MCSW0',0
	.word	72009
	.byte	12,3,35,128,18,13
	.byte	'reserved_90C',0
	.word	72112
	.byte	52,3,35,140,18,13
	.byte	'MCSW1',0
	.word	72009
	.byte	12,3,35,192,18,0,14
	.word	72121
	.byte	29
	.byte	'Ifx_SRC_GTM',0,8,243,1,3
	.word	72581
	.byte	10
	.byte	'_Ifx_SRC_HSCT',0,8,246,1,25,4,13
	.byte	'SR',0
	.word	10452
	.byte	4,2,35,0,0,14
	.word	72607
	.byte	29
	.byte	'Ifx_SRC_HSCT',0,8,249,1,3
	.word	72640
	.byte	10
	.byte	'_Ifx_SRC_HSSL',0,8,252,1,25,16,13
	.byte	'COK',0
	.word	10452
	.byte	4,2,35,0,13
	.byte	'RDI',0
	.word	10452
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	10452
	.byte	4,2,35,8,13
	.byte	'TRG',0
	.word	10452
	.byte	4,2,35,12,0,14
	.word	72667
	.byte	29
	.byte	'Ifx_SRC_HSSL',0,8,130,2,3
	.word	72740
	.byte	15,56
	.word	643
	.byte	16,55,0,10
	.byte	'_Ifx_SRC_I2C',0,8,133,2,25,80,13
	.byte	'BREQ',0
	.word	10452
	.byte	4,2,35,0,13
	.byte	'LBREQ',0
	.word	10452
	.byte	4,2,35,4,13
	.byte	'SREQ',0
	.word	10452
	.byte	4,2,35,8,13
	.byte	'LSREQ',0
	.word	10452
	.byte	4,2,35,12,13
	.byte	'ERR',0
	.word	10452
	.byte	4,2,35,16,13
	.byte	'P',0
	.word	10452
	.byte	4,2,35,20,13
	.byte	'reserved_18',0
	.word	72767
	.byte	56,2,35,24,0,14
	.word	72776
	.byte	29
	.byte	'Ifx_SRC_I2C',0,8,142,2,3
	.word	72899
	.byte	10
	.byte	'_Ifx_SRC_LMU',0,8,145,2,25,4,13
	.byte	'SR',0
	.word	10452
	.byte	4,2,35,0,0,14
	.word	72925
	.byte	29
	.byte	'Ifx_SRC_LMU',0,8,148,2,3
	.word	72957
	.byte	10
	.byte	'_Ifx_SRC_MSC',0,8,151,2,25,20,13
	.byte	'SR0',0
	.word	10452
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10452
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	10452
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	10452
	.byte	4,2,35,12,13
	.byte	'SR4',0
	.word	10452
	.byte	4,2,35,16,0,14
	.word	72983
	.byte	29
	.byte	'Ifx_SRC_MSC',0,8,158,2,3
	.word	73068
	.byte	10
	.byte	'_Ifx_SRC_PMU',0,8,161,2,25,4,13
	.byte	'SR',0
	.word	10452
	.byte	4,2,35,0,0,14
	.word	73094
	.byte	29
	.byte	'Ifx_SRC_PMU',0,8,164,2,3
	.word	73126
	.byte	10
	.byte	'_Ifx_SRC_PSI5',0,8,167,2,25,32,13
	.byte	'SR',0
	.word	72018
	.byte	32,2,35,0,0,14
	.word	73152
	.byte	29
	.byte	'Ifx_SRC_PSI5',0,8,170,2,3
	.word	73185
	.byte	10
	.byte	'_Ifx_SRC_PSI5S',0,8,173,2,25,32,13
	.byte	'SR',0
	.word	72018
	.byte	32,2,35,0,0,14
	.word	73212
	.byte	29
	.byte	'Ifx_SRC_PSI5S',0,8,176,2,3
	.word	73246
	.byte	10
	.byte	'_Ifx_SRC_QSPI',0,8,179,2,25,24,13
	.byte	'TX',0
	.word	10452
	.byte	4,2,35,0,13
	.byte	'RX',0
	.word	10452
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	10452
	.byte	4,2,35,8,13
	.byte	'PT',0
	.word	10452
	.byte	4,2,35,12,13
	.byte	'HC',0
	.word	10452
	.byte	4,2,35,16,13
	.byte	'U',0
	.word	10452
	.byte	4,2,35,20,0,14
	.word	73274
	.byte	29
	.byte	'Ifx_SRC_QSPI',0,8,187,2,3
	.word	73367
	.byte	10
	.byte	'_Ifx_SRC_SCR',0,8,190,2,25,4,13
	.byte	'SR',0
	.word	10452
	.byte	4,2,35,0,0,14
	.word	73394
	.byte	29
	.byte	'Ifx_SRC_SCR',0,8,193,2,3
	.word	73426
	.byte	10
	.byte	'_Ifx_SRC_SCU',0,8,196,2,25,20,13
	.byte	'DTS',0
	.word	10452
	.byte	4,2,35,0,13
	.byte	'ERU',0
	.word	72094
	.byte	16,2,35,4,0,14
	.word	73452
	.byte	29
	.byte	'Ifx_SRC_SCU',0,8,200,2,3
	.word	73498
	.byte	15,24
	.word	10452
	.byte	16,5,0,10
	.byte	'_Ifx_SRC_SENT',0,8,203,2,25,24,13
	.byte	'SR',0
	.word	73524
	.byte	24,2,35,0,0,14
	.word	73533
	.byte	29
	.byte	'Ifx_SRC_SENT',0,8,206,2,3
	.word	73566
	.byte	10
	.byte	'_Ifx_SRC_SMU',0,8,209,2,25,12,13
	.byte	'SR',0
	.word	72009
	.byte	12,2,35,0,0,14
	.word	73593
	.byte	29
	.byte	'Ifx_SRC_SMU',0,8,212,2,3
	.word	73625
	.byte	10
	.byte	'_Ifx_SRC_STM',0,8,215,2,25,8,13
	.byte	'SR0',0
	.word	10452
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10452
	.byte	4,2,35,4,0,14
	.word	73651
	.byte	29
	.byte	'Ifx_SRC_STM',0,8,219,2,3
	.word	73697
	.byte	10
	.byte	'_Ifx_SRC_VADCCG',0,8,222,2,25,16,13
	.byte	'SR0',0
	.word	10452
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10452
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	10452
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	10452
	.byte	4,2,35,12,0,14
	.word	73723
	.byte	29
	.byte	'Ifx_SRC_VADCCG',0,8,228,2,3
	.word	73798
	.byte	10
	.byte	'_Ifx_SRC_VADCG',0,8,231,2,25,16,13
	.byte	'SR0',0
	.word	10452
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10452
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	10452
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	10452
	.byte	4,2,35,12,0,14
	.word	73827
	.byte	29
	.byte	'Ifx_SRC_VADCG',0,8,237,2,3
	.word	73901
	.byte	10
	.byte	'_Ifx_SRC_XBAR',0,8,240,2,25,4,13
	.byte	'SRC',0
	.word	10452
	.byte	4,2,35,0,0,14
	.word	73929
	.byte	29
	.byte	'Ifx_SRC_XBAR',0,8,243,2,3
	.word	73963
	.byte	15,4
	.word	70539
	.byte	16,0,0,14
	.word	73990
	.byte	10
	.byte	'_Ifx_SRC_GAGBT',0,8,128,3,25,4,13
	.byte	'AGBT',0
	.word	73999
	.byte	4,2,35,0,0,14
	.word	74004
	.byte	29
	.byte	'Ifx_SRC_GAGBT',0,8,131,3,3
	.word	74040
	.byte	15,48
	.word	70597
	.byte	16,3,0,14
	.word	74068
	.byte	10
	.byte	'_Ifx_SRC_GASCLIN',0,8,134,3,25,48,13
	.byte	'ASCLIN',0
	.word	74077
	.byte	48,2,35,0,0,14
	.word	74082
	.byte	29
	.byte	'Ifx_SRC_GASCLIN',0,8,137,3,3
	.word	74122
	.byte	14
	.word	70684
	.byte	10
	.byte	'_Ifx_SRC_GBCU',0,8,140,3,25,4,13
	.byte	'SPB',0
	.word	74152
	.byte	4,2,35,0,0,14
	.word	74157
	.byte	29
	.byte	'Ifx_SRC_GBCU',0,8,143,3,3
	.word	74191
	.byte	15,64
	.word	70758
	.byte	16,0,0,14
	.word	74218
	.byte	10
	.byte	'_Ifx_SRC_GCAN',0,8,146,3,25,64,13
	.byte	'CAN',0
	.word	74227
	.byte	64,2,35,0,0,14
	.word	74232
	.byte	29
	.byte	'Ifx_SRC_GCAN',0,8,149,3,3
	.word	74266
	.byte	15,32
	.word	70815
	.byte	16,1,0,14
	.word	74293
	.byte	10
	.byte	'_Ifx_SRC_GCCU6',0,8,152,3,25,32,13
	.byte	'CCU6',0
	.word	74302
	.byte	32,2,35,0,0,14
	.word	74307
	.byte	29
	.byte	'Ifx_SRC_GCCU6',0,8,155,3,3
	.word	74343
	.byte	14
	.word	70922
	.byte	10
	.byte	'_Ifx_SRC_GCERBERUS',0,8,158,3,25,8,13
	.byte	'CERBERUS',0
	.word	74371
	.byte	8,2,35,0,0,14
	.word	74376
	.byte	29
	.byte	'Ifx_SRC_GCERBERUS',0,8,161,3,3
	.word	74420
	.byte	15,16
	.word	70988
	.byte	16,0,0,14
	.word	74452
	.byte	10
	.byte	'_Ifx_SRC_GCIF',0,8,164,3,25,16,13
	.byte	'CIF',0
	.word	74461
	.byte	16,2,35,0,0,14
	.word	74466
	.byte	29
	.byte	'Ifx_SRC_GCIF',0,8,167,3,3
	.word	74500
	.byte	15,8
	.word	71087
	.byte	16,1,0,14
	.word	74527
	.byte	10
	.byte	'_Ifx_SRC_GCPU',0,8,170,3,25,8,13
	.byte	'CPU',0
	.word	74536
	.byte	8,2,35,0,0,14
	.word	74541
	.byte	29
	.byte	'Ifx_SRC_GCPU',0,8,173,3,3
	.word	74575
	.byte	15,208,1
	.word	71158
	.byte	16,0,0,14
	.word	74602
	.byte	10
	.byte	'_Ifx_SRC_GDMA',0,8,176,3,25,208,1,13
	.byte	'DMA',0
	.word	74612
	.byte	208,1,2,35,0,0,14
	.word	74617
	.byte	29
	.byte	'Ifx_SRC_GDMA',0,8,179,3,3
	.word	74653
	.byte	14
	.word	71251
	.byte	14
	.word	71251
	.byte	14
	.word	71251
	.byte	10
	.byte	'_Ifx_SRC_GDSADC',0,8,182,3,25,32,13
	.byte	'DSADC0',0
	.word	74680
	.byte	8,2,35,0,13
	.byte	'reserved_8',0
	.word	4460
	.byte	8,2,35,8,13
	.byte	'DSADC2',0
	.word	74685
	.byte	8,2,35,16,13
	.byte	'DSADC3',0
	.word	74690
	.byte	8,2,35,24,0,14
	.word	74695
	.byte	29
	.byte	'Ifx_SRC_GDSADC',0,8,188,3,3
	.word	74786
	.byte	15,4
	.word	71327
	.byte	16,0,0,14
	.word	74815
	.byte	10
	.byte	'_Ifx_SRC_GEMEM',0,8,191,3,25,4,13
	.byte	'EMEM',0
	.word	74824
	.byte	4,2,35,0,0,14
	.word	74829
	.byte	29
	.byte	'Ifx_SRC_GEMEM',0,8,194,3,3
	.word	74865
	.byte	15,80
	.word	71387
	.byte	16,0,0,14
	.word	74893
	.byte	10
	.byte	'_Ifx_SRC_GERAY',0,8,197,3,25,80,13
	.byte	'ERAY',0
	.word	74902
	.byte	80,2,35,0,0,14
	.word	74907
	.byte	29
	.byte	'Ifx_SRC_GERAY',0,8,200,3,3
	.word	74943
	.byte	15,4
	.word	71541
	.byte	16,0,0,14
	.word	74971
	.byte	10
	.byte	'_Ifx_SRC_GETH',0,8,203,3,25,4,13
	.byte	'ETH',0
	.word	74980
	.byte	4,2,35,0,0,14
	.word	74985
	.byte	29
	.byte	'Ifx_SRC_GETH',0,8,206,3,3
	.word	75019
	.byte	15,4
	.word	71599
	.byte	16,0,0,14
	.word	75046
	.byte	10
	.byte	'_Ifx_SRC_GFCE',0,8,209,3,25,4,13
	.byte	'FCE',0
	.word	75055
	.byte	4,2,35,0,0,14
	.word	75060
	.byte	29
	.byte	'Ifx_SRC_GFCE',0,8,212,3,3
	.word	75094
	.byte	15,12
	.word	71657
	.byte	16,0,0,14
	.word	75121
	.byte	10
	.byte	'_Ifx_SRC_GFFT',0,8,215,3,25,12,13
	.byte	'FFT',0
	.word	75130
	.byte	12,2,35,0,0,14
	.word	75135
	.byte	29
	.byte	'Ifx_SRC_GFFT',0,8,218,3,3
	.word	75169
	.byte	15,64
	.word	71743
	.byte	16,1,0,14
	.word	75196
	.byte	10
	.byte	'_Ifx_SRC_GGPSR',0,8,221,3,25,64,13
	.byte	'GPSR',0
	.word	75205
	.byte	64,2,35,0,0,14
	.word	75210
	.byte	29
	.byte	'Ifx_SRC_GGPSR',0,8,224,3,3
	.word	75246
	.byte	15,48
	.word	71864
	.byte	16,0,0,14
	.word	75274
	.byte	10
	.byte	'_Ifx_SRC_GGPT12',0,8,227,3,25,48,13
	.byte	'GPT12',0
	.word	75283
	.byte	48,2,35,0,0,14
	.word	75288
	.byte	29
	.byte	'Ifx_SRC_GGPT12',0,8,230,3,3
	.word	75326
	.byte	15,204,18
	.word	72121
	.byte	16,0,0,14
	.word	75355
	.byte	10
	.byte	'_Ifx_SRC_GGTM',0,8,233,3,25,204,18,13
	.byte	'GTM',0
	.word	75365
	.byte	204,18,2,35,0,0,14
	.word	75370
	.byte	29
	.byte	'Ifx_SRC_GGTM',0,8,236,3,3
	.word	75406
	.byte	15,4
	.word	72607
	.byte	16,0,0,14
	.word	75433
	.byte	10
	.byte	'_Ifx_SRC_GHSCT',0,8,239,3,25,4,13
	.byte	'HSCT',0
	.word	75442
	.byte	4,2,35,0,0,14
	.word	75447
	.byte	29
	.byte	'Ifx_SRC_GHSCT',0,8,242,3,3
	.word	75483
	.byte	15,64
	.word	72667
	.byte	16,3,0,14
	.word	75511
	.byte	10
	.byte	'_Ifx_SRC_GHSSL',0,8,245,3,25,68,13
	.byte	'HSSL',0
	.word	75520
	.byte	64,2,35,0,13
	.byte	'EXI',0
	.word	10452
	.byte	4,2,35,64,0,14
	.word	75525
	.byte	29
	.byte	'Ifx_SRC_GHSSL',0,8,249,3,3
	.word	75574
	.byte	15,80
	.word	72776
	.byte	16,0,0,14
	.word	75602
	.byte	10
	.byte	'_Ifx_SRC_GI2C',0,8,252,3,25,80,13
	.byte	'I2C',0
	.word	75611
	.byte	80,2,35,0,0,14
	.word	75616
	.byte	29
	.byte	'Ifx_SRC_GI2C',0,8,255,3,3
	.word	75650
	.byte	15,4
	.word	72925
	.byte	16,0,0,14
	.word	75677
	.byte	10
	.byte	'_Ifx_SRC_GLMU',0,8,130,4,25,4,13
	.byte	'LMU',0
	.word	75686
	.byte	4,2,35,0,0,14
	.word	75691
	.byte	29
	.byte	'Ifx_SRC_GLMU',0,8,133,4,3
	.word	75725
	.byte	15,40
	.word	72983
	.byte	16,1,0,14
	.word	75752
	.byte	10
	.byte	'_Ifx_SRC_GMSC',0,8,136,4,25,40,13
	.byte	'MSC',0
	.word	75761
	.byte	40,2,35,0,0,14
	.word	75766
	.byte	29
	.byte	'Ifx_SRC_GMSC',0,8,139,4,3
	.word	75800
	.byte	15,8
	.word	73094
	.byte	16,1,0,14
	.word	75827
	.byte	10
	.byte	'_Ifx_SRC_GPMU',0,8,142,4,25,8,13
	.byte	'PMU',0
	.word	75836
	.byte	8,2,35,0,0,14
	.word	75841
	.byte	29
	.byte	'Ifx_SRC_GPMU',0,8,145,4,3
	.word	75875
	.byte	15,32
	.word	73152
	.byte	16,0,0,14
	.word	75902
	.byte	10
	.byte	'_Ifx_SRC_GPSI5',0,8,148,4,25,32,13
	.byte	'PSI5',0
	.word	75911
	.byte	32,2,35,0,0,14
	.word	75916
	.byte	29
	.byte	'Ifx_SRC_GPSI5',0,8,151,4,3
	.word	75952
	.byte	15,32
	.word	73212
	.byte	16,0,0,14
	.word	75980
	.byte	10
	.byte	'_Ifx_SRC_GPSI5S',0,8,154,4,25,32,13
	.byte	'PSI5S',0
	.word	75989
	.byte	32,2,35,0,0,14
	.word	75994
	.byte	29
	.byte	'Ifx_SRC_GPSI5S',0,8,157,4,3
	.word	76032
	.byte	15,96
	.word	73274
	.byte	16,3,0,14
	.word	76061
	.byte	10
	.byte	'_Ifx_SRC_GQSPI',0,8,160,4,25,96,13
	.byte	'QSPI',0
	.word	76070
	.byte	96,2,35,0,0,14
	.word	76075
	.byte	29
	.byte	'Ifx_SRC_GQSPI',0,8,163,4,3
	.word	76111
	.byte	15,4
	.word	73394
	.byte	16,0,0,14
	.word	76139
	.byte	10
	.byte	'_Ifx_SRC_GSCR',0,8,166,4,25,4,13
	.byte	'SCR',0
	.word	76148
	.byte	4,2,35,0,0,14
	.word	76153
	.byte	29
	.byte	'Ifx_SRC_GSCR',0,8,169,4,3
	.word	76187
	.byte	14
	.word	73452
	.byte	10
	.byte	'_Ifx_SRC_GSCU',0,8,172,4,25,20,13
	.byte	'SCU',0
	.word	76214
	.byte	20,2,35,0,0,14
	.word	76219
	.byte	29
	.byte	'Ifx_SRC_GSCU',0,8,175,4,3
	.word	76253
	.byte	15,24
	.word	73533
	.byte	16,0,0,14
	.word	76280
	.byte	10
	.byte	'_Ifx_SRC_GSENT',0,8,178,4,25,24,13
	.byte	'SENT',0
	.word	76289
	.byte	24,2,35,0,0,14
	.word	76294
	.byte	29
	.byte	'Ifx_SRC_GSENT',0,8,181,4,3
	.word	76330
	.byte	15,12
	.word	73593
	.byte	16,0,0,14
	.word	76358
	.byte	10
	.byte	'_Ifx_SRC_GSMU',0,8,184,4,25,12,13
	.byte	'SMU',0
	.word	76367
	.byte	12,2,35,0,0,14
	.word	76372
	.byte	29
	.byte	'Ifx_SRC_GSMU',0,8,187,4,3
	.word	76406
	.byte	15,16
	.word	73651
	.byte	16,1,0,14
	.word	76433
	.byte	10
	.byte	'_Ifx_SRC_GSTM',0,8,190,4,25,16,13
	.byte	'STM',0
	.word	76442
	.byte	16,2,35,0,0,14
	.word	76447
	.byte	29
	.byte	'Ifx_SRC_GSTM',0,8,193,4,3
	.word	76481
	.byte	15,64
	.word	73827
	.byte	16,3,0,14
	.word	76508
	.byte	15,224,1
	.word	643
	.byte	16,223,1,0,15,32
	.word	73723
	.byte	16,1,0,14
	.word	76533
	.byte	10
	.byte	'_Ifx_SRC_GVADC',0,8,196,4,25,192,2,13
	.byte	'G',0
	.word	76517
	.byte	64,2,35,0,13
	.byte	'reserved_40',0
	.word	76522
	.byte	224,1,2,35,64,13
	.byte	'CG',0
	.word	76542
	.byte	32,3,35,160,2,0,14
	.word	76547
	.byte	29
	.byte	'Ifx_SRC_GVADC',0,8,201,4,3
	.word	76616
	.byte	14
	.word	73929
	.byte	10
	.byte	'_Ifx_SRC_GXBAR',0,8,204,4,25,4,13
	.byte	'XBAR',0
	.word	76644
	.byte	4,2,35,0,0,14
	.word	76649
	.byte	29
	.byte	'Ifx_SRC_GXBAR',0,8,207,4,3
	.word	76685
	.byte	10
	.byte	'_Ifx_CPU_A_Bits',0,33,45,16,4,11
	.byte	'ADDR',0,4
	.word	491
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_A_Bits',0,33,48,3
	.word	76713
	.byte	10
	.byte	'_Ifx_CPU_BIV_Bits',0,33,51,16,4,11
	.byte	'VSS',0,4
	.word	491
	.byte	1,31,2,35,0,11
	.byte	'BIV',0,4
	.word	491
	.byte	31,0,2,35,0,0,29
	.byte	'Ifx_CPU_BIV_Bits',0,33,55,3
	.word	76774
	.byte	10
	.byte	'_Ifx_CPU_BTV_Bits',0,33,58,16,4,11
	.byte	'reserved_0',0,4
	.word	491
	.byte	1,31,2,35,0,11
	.byte	'BTV',0,4
	.word	491
	.byte	31,0,2,35,0,0,29
	.byte	'Ifx_CPU_BTV_Bits',0,33,62,3
	.word	76853
	.byte	10
	.byte	'_Ifx_CPU_CCNT_Bits',0,33,65,16,4,11
	.byte	'CountValue',0,4
	.word	491
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	491
	.byte	1,0,2,35,0,0,29
	.byte	'Ifx_CPU_CCNT_Bits',0,33,69,3
	.word	76939
	.byte	10
	.byte	'_Ifx_CPU_CCTRL_Bits',0,33,72,16,4,11
	.byte	'CM',0,4
	.word	491
	.byte	1,31,2,35,0,11
	.byte	'CE',0,4
	.word	491
	.byte	1,30,2,35,0,11
	.byte	'M1',0,4
	.word	491
	.byte	3,27,2,35,0,11
	.byte	'M2',0,4
	.word	491
	.byte	3,24,2,35,0,11
	.byte	'M3',0,4
	.word	491
	.byte	3,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	491
	.byte	21,0,2,35,0,0,29
	.byte	'Ifx_CPU_CCTRL_Bits',0,33,80,3
	.word	77028
	.byte	10
	.byte	'_Ifx_CPU_COMPAT_Bits',0,33,83,16,4,11
	.byte	'reserved_0',0,4
	.word	491
	.byte	3,29,2,35,0,11
	.byte	'RM',0,4
	.word	491
	.byte	1,28,2,35,0,11
	.byte	'SP',0,4
	.word	491
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	491
	.byte	27,0,2,35,0,0,29
	.byte	'Ifx_CPU_COMPAT_Bits',0,33,89,3
	.word	77174
	.byte	10
	.byte	'_Ifx_CPU_CORE_ID_Bits',0,33,92,16,4,11
	.byte	'CORE_ID',0,4
	.word	491
	.byte	3,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	491
	.byte	29,0,2,35,0,0,29
	.byte	'Ifx_CPU_CORE_ID_Bits',0,33,96,3
	.word	77301
	.byte	10
	.byte	'_Ifx_CPU_CPR_L_Bits',0,33,99,16,4,11
	.byte	'reserved_0',0,4
	.word	491
	.byte	3,29,2,35,0,11
	.byte	'LOWBND',0,4
	.word	491
	.byte	29,0,2,35,0,0,29
	.byte	'Ifx_CPU_CPR_L_Bits',0,33,103,3
	.word	77399
	.byte	10
	.byte	'_Ifx_CPU_CPR_U_Bits',0,33,106,16,4,11
	.byte	'reserved_0',0,4
	.word	491
	.byte	3,29,2,35,0,11
	.byte	'UPPBND',0,4
	.word	491
	.byte	29,0,2,35,0,0,29
	.byte	'Ifx_CPU_CPR_U_Bits',0,33,110,3
	.word	77492
	.byte	10
	.byte	'_Ifx_CPU_CPU_ID_Bits',0,33,113,16,4,11
	.byte	'MODREV',0,4
	.word	491
	.byte	8,24,2,35,0,11
	.byte	'MOD_32B',0,4
	.word	491
	.byte	8,16,2,35,0,11
	.byte	'MOD',0,4
	.word	491
	.byte	16,0,2,35,0,0,29
	.byte	'Ifx_CPU_CPU_ID_Bits',0,33,118,3
	.word	77585
	.byte	10
	.byte	'_Ifx_CPU_CPXE_Bits',0,33,121,16,4,11
	.byte	'XE',0,4
	.word	491
	.byte	8,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	491
	.byte	24,0,2,35,0,0,29
	.byte	'Ifx_CPU_CPXE_Bits',0,33,125,3
	.word	77692
	.byte	10
	.byte	'_Ifx_CPU_CREVT_Bits',0,33,128,1,16,4,11
	.byte	'EVTA',0,4
	.word	491
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	491
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	491
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	491
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	491
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	491
	.byte	24,0,2,35,0,0,29
	.byte	'Ifx_CPU_CREVT_Bits',0,33,136,1,3
	.word	77779
	.byte	10
	.byte	'_Ifx_CPU_CUS_ID_Bits',0,33,139,1,16,4,11
	.byte	'CID',0,4
	.word	491
	.byte	3,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	491
	.byte	29,0,2,35,0,0,29
	.byte	'Ifx_CPU_CUS_ID_Bits',0,33,143,1,3
	.word	77933
	.byte	10
	.byte	'_Ifx_CPU_D_Bits',0,33,146,1,16,4,11
	.byte	'DATA',0,4
	.word	491
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_D_Bits',0,33,149,1,3
	.word	78027
	.byte	10
	.byte	'_Ifx_CPU_DATR_Bits',0,33,152,1,16,4,11
	.byte	'reserved_0',0,4
	.word	491
	.byte	3,29,2,35,0,11
	.byte	'SBE',0,4
	.word	491
	.byte	1,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	491
	.byte	5,23,2,35,0,11
	.byte	'CWE',0,4
	.word	491
	.byte	1,22,2,35,0,11
	.byte	'CFE',0,4
	.word	491
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	491
	.byte	3,18,2,35,0,11
	.byte	'SOE',0,4
	.word	491
	.byte	1,17,2,35,0,11
	.byte	'SME',0,4
	.word	491
	.byte	1,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	491
	.byte	16,0,2,35,0,0,29
	.byte	'Ifx_CPU_DATR_Bits',0,33,163,1,3
	.word	78090
	.byte	10
	.byte	'_Ifx_CPU_DBGSR_Bits',0,33,166,1,16,4,11
	.byte	'DE',0,4
	.word	491
	.byte	1,31,2,35,0,11
	.byte	'HALT',0,4
	.word	491
	.byte	2,29,2,35,0,11
	.byte	'SIH',0,4
	.word	491
	.byte	1,28,2,35,0,11
	.byte	'SUSP',0,4
	.word	491
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	491
	.byte	1,26,2,35,0,11
	.byte	'PREVSUSP',0,4
	.word	491
	.byte	1,25,2,35,0,11
	.byte	'PEVT',0,4
	.word	491
	.byte	1,24,2,35,0,11
	.byte	'EVTSRC',0,4
	.word	491
	.byte	5,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	491
	.byte	19,0,2,35,0,0,29
	.byte	'Ifx_CPU_DBGSR_Bits',0,33,177,1,3
	.word	78308
	.byte	10
	.byte	'_Ifx_CPU_DBGTCR_Bits',0,33,180,1,16,4,11
	.byte	'DTA',0,4
	.word	491
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	491
	.byte	31,0,2,35,0,0,29
	.byte	'Ifx_CPU_DBGTCR_Bits',0,33,184,1,3
	.word	78523
	.byte	10
	.byte	'_Ifx_CPU_DCON0_Bits',0,33,187,1,16,4,11
	.byte	'reserved_0',0,4
	.word	491
	.byte	1,31,2,35,0,11
	.byte	'DCBYP',0,4
	.word	491
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	491
	.byte	30,0,2,35,0,0,29
	.byte	'Ifx_CPU_DCON0_Bits',0,33,192,1,3
	.word	78617
	.byte	10
	.byte	'_Ifx_CPU_DCON2_Bits',0,33,195,1,16,4,11
	.byte	'DCACHE_SZE',0,4
	.word	491
	.byte	16,16,2,35,0,11
	.byte	'DSCRATCH_SZE',0,4
	.word	491
	.byte	16,0,2,35,0,0,29
	.byte	'Ifx_CPU_DCON2_Bits',0,33,199,1,3
	.word	78733
	.byte	10
	.byte	'_Ifx_CPU_DCX_Bits',0,33,202,1,16,4,11
	.byte	'reserved_0',0,4
	.word	491
	.byte	6,26,2,35,0,11
	.byte	'DCXValue',0,4
	.word	491
	.byte	26,0,2,35,0,0,29
	.byte	'Ifx_CPU_DCX_Bits',0,33,206,1,3
	.word	78834
	.byte	10
	.byte	'_Ifx_CPU_DEADD_Bits',0,33,209,1,16,4,11
	.byte	'ERROR_ADDRESS',0,4
	.word	491
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_DEADD_Bits',0,33,212,1,3
	.word	78927
	.byte	10
	.byte	'_Ifx_CPU_DIEAR_Bits',0,33,215,1,16,4,11
	.byte	'TA',0,4
	.word	491
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_DIEAR_Bits',0,33,218,1,3
	.word	79007
	.byte	10
	.byte	'_Ifx_CPU_DIETR_Bits',0,33,221,1,16,4,11
	.byte	'IED',0,4
	.word	491
	.byte	1,31,2,35,0,11
	.byte	'IE_T',0,4
	.word	491
	.byte	1,30,2,35,0,11
	.byte	'IE_C',0,4
	.word	491
	.byte	1,29,2,35,0,11
	.byte	'IE_S',0,4
	.word	491
	.byte	1,28,2,35,0,11
	.byte	'IE_BI',0,4
	.word	491
	.byte	1,27,2,35,0,11
	.byte	'E_INFO',0,4
	.word	491
	.byte	6,21,2,35,0,11
	.byte	'IE_DUAL',0,4
	.word	491
	.byte	1,20,2,35,0,11
	.byte	'IE_SP',0,4
	.word	491
	.byte	1,19,2,35,0,11
	.byte	'IE_BS',0,4
	.word	491
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	491
	.byte	18,0,2,35,0,0,29
	.byte	'Ifx_CPU_DIETR_Bits',0,33,233,1,3
	.word	79076
	.byte	10
	.byte	'_Ifx_CPU_DMS_Bits',0,33,236,1,16,4,11
	.byte	'reserved_0',0,4
	.word	491
	.byte	1,31,2,35,0,11
	.byte	'DMSValue',0,4
	.word	491
	.byte	31,0,2,35,0,0,29
	.byte	'Ifx_CPU_DMS_Bits',0,33,240,1,3
	.word	79305
	.byte	10
	.byte	'_Ifx_CPU_DPR_L_Bits',0,33,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	491
	.byte	3,29,2,35,0,11
	.byte	'LOWBND',0,4
	.word	491
	.byte	29,0,2,35,0,0,29
	.byte	'Ifx_CPU_DPR_L_Bits',0,33,247,1,3
	.word	79398
	.byte	10
	.byte	'_Ifx_CPU_DPR_U_Bits',0,33,250,1,16,4,11
	.byte	'reserved_0',0,4
	.word	491
	.byte	3,29,2,35,0,11
	.byte	'UPPBND',0,4
	.word	491
	.byte	29,0,2,35,0,0,29
	.byte	'Ifx_CPU_DPR_U_Bits',0,33,254,1,3
	.word	79493
	.byte	10
	.byte	'_Ifx_CPU_DPRE_Bits',0,33,129,2,16,4,11
	.byte	'RE',0,4
	.word	491
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	491
	.byte	16,0,2,35,0,0,29
	.byte	'Ifx_CPU_DPRE_Bits',0,33,133,2,3
	.word	79588
	.byte	10
	.byte	'_Ifx_CPU_DPWE_Bits',0,33,136,2,16,4,11
	.byte	'WE',0,4
	.word	491
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	491
	.byte	16,0,2,35,0,0,29
	.byte	'Ifx_CPU_DPWE_Bits',0,33,140,2,3
	.word	79678
	.byte	10
	.byte	'_Ifx_CPU_DSTR_Bits',0,33,143,2,16,4,11
	.byte	'SRE',0,4
	.word	491
	.byte	1,31,2,35,0,11
	.byte	'GAE',0,4
	.word	491
	.byte	1,30,2,35,0,11
	.byte	'LBE',0,4
	.word	491
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	491
	.byte	3,26,2,35,0,11
	.byte	'CRE',0,4
	.word	491
	.byte	1,25,2,35,0,11
	.byte	'reserved_7',0,4
	.word	491
	.byte	7,18,2,35,0,11
	.byte	'DTME',0,4
	.word	491
	.byte	1,17,2,35,0,11
	.byte	'LOE',0,4
	.word	491
	.byte	1,16,2,35,0,11
	.byte	'SDE',0,4
	.word	491
	.byte	1,15,2,35,0,11
	.byte	'SCE',0,4
	.word	491
	.byte	1,14,2,35,0,11
	.byte	'CAC',0,4
	.word	491
	.byte	1,13,2,35,0,11
	.byte	'MPE',0,4
	.word	491
	.byte	1,12,2,35,0,11
	.byte	'CLE',0,4
	.word	491
	.byte	1,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	491
	.byte	3,8,2,35,0,11
	.byte	'ALN',0,4
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	491
	.byte	7,0,2,35,0,0,29
	.byte	'Ifx_CPU_DSTR_Bits',0,33,161,2,3
	.word	79768
	.byte	10
	.byte	'_Ifx_CPU_EXEVT_Bits',0,33,164,2,16,4,11
	.byte	'EVTA',0,4
	.word	491
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	491
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	491
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	491
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	491
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	491
	.byte	24,0,2,35,0,0,29
	.byte	'Ifx_CPU_EXEVT_Bits',0,33,172,2,3
	.word	80092
	.byte	10
	.byte	'_Ifx_CPU_FCX_Bits',0,33,175,2,16,4,11
	.byte	'FCXO',0,4
	.word	491
	.byte	16,16,2,35,0,11
	.byte	'FCXS',0,4
	.word	491
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	491
	.byte	12,0,2,35,0,0,29
	.byte	'Ifx_CPU_FCX_Bits',0,33,180,2,3
	.word	80246
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_CON_Bits',0,33,183,2,16,4,11
	.byte	'TST',0,4
	.word	491
	.byte	1,31,2,35,0,11
	.byte	'TCL',0,4
	.word	491
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	491
	.byte	6,24,2,35,0,11
	.byte	'RM',0,4
	.word	491
	.byte	2,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	491
	.byte	8,14,2,35,0,11
	.byte	'FXE',0,4
	.word	491
	.byte	1,13,2,35,0,11
	.byte	'FUE',0,4
	.word	491
	.byte	1,12,2,35,0,11
	.byte	'FZE',0,4
	.word	491
	.byte	1,11,2,35,0,11
	.byte	'FVE',0,4
	.word	491
	.byte	1,10,2,35,0,11
	.byte	'FIE',0,4
	.word	491
	.byte	1,9,2,35,0,11
	.byte	'reserved_23',0,4
	.word	491
	.byte	3,6,2,35,0,11
	.byte	'FX',0,4
	.word	491
	.byte	1,5,2,35,0,11
	.byte	'FU',0,4
	.word	491
	.byte	1,4,2,35,0,11
	.byte	'FZ',0,4
	.word	491
	.byte	1,3,2,35,0,11
	.byte	'FV',0,4
	.word	491
	.byte	1,2,2,35,0,11
	.byte	'FI',0,4
	.word	491
	.byte	1,1,2,35,0,11
	.byte	'reserved_31',0,4
	.word	491
	.byte	1,0,2,35,0,0,29
	.byte	'Ifx_CPU_FPU_TRAP_CON_Bits',0,33,202,2,3
	.word	80352
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_OPC_Bits',0,33,205,2,16,4,11
	.byte	'OPC',0,4
	.word	491
	.byte	8,24,2,35,0,11
	.byte	'FMT',0,4
	.word	491
	.byte	1,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	491
	.byte	7,16,2,35,0,11
	.byte	'DREG',0,4
	.word	491
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	491
	.byte	12,0,2,35,0,0,29
	.byte	'Ifx_CPU_FPU_TRAP_OPC_Bits',0,33,212,2,3
	.word	80701
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_PC_Bits',0,33,215,2,16,4,11
	.byte	'PC',0,4
	.word	491
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_FPU_TRAP_PC_Bits',0,33,218,2,3
	.word	80861
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC1_Bits',0,33,221,2,16,4,11
	.byte	'SRC1',0,4
	.word	491
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_FPU_TRAP_SRC1_Bits',0,33,224,2,3
	.word	80942
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC2_Bits',0,33,227,2,16,4,11
	.byte	'SRC2',0,4
	.word	491
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_FPU_TRAP_SRC2_Bits',0,33,230,2,3
	.word	81029
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC3_Bits',0,33,233,2,16,4,11
	.byte	'SRC3',0,4
	.word	491
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_FPU_TRAP_SRC3_Bits',0,33,236,2,3
	.word	81116
	.byte	10
	.byte	'_Ifx_CPU_ICNT_Bits',0,33,239,2,16,4,11
	.byte	'CountValue',0,4
	.word	491
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	491
	.byte	1,0,2,35,0,0,29
	.byte	'Ifx_CPU_ICNT_Bits',0,33,243,2,3
	.word	81203
	.byte	10
	.byte	'_Ifx_CPU_ICR_Bits',0,33,246,2,16,4,11
	.byte	'CCPN',0,4
	.word	491
	.byte	10,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	491
	.byte	5,17,2,35,0,11
	.byte	'IE',0,4
	.word	491
	.byte	1,16,2,35,0,11
	.byte	'PIPN',0,4
	.word	491
	.byte	10,6,2,35,0,11
	.byte	'reserved_26',0,4
	.word	491
	.byte	6,0,2,35,0,0,29
	.byte	'Ifx_CPU_ICR_Bits',0,33,253,2,3
	.word	81294
	.byte	10
	.byte	'_Ifx_CPU_ISP_Bits',0,33,128,3,16,4,11
	.byte	'ISP',0,4
	.word	491
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_ISP_Bits',0,33,131,3,3
	.word	81437
	.byte	10
	.byte	'_Ifx_CPU_LCX_Bits',0,33,134,3,16,4,11
	.byte	'LCXO',0,4
	.word	491
	.byte	16,16,2,35,0,11
	.byte	'LCXS',0,4
	.word	491
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	491
	.byte	12,0,2,35,0,0,29
	.byte	'Ifx_CPU_LCX_Bits',0,33,139,3,3
	.word	81503
	.byte	10
	.byte	'_Ifx_CPU_M1CNT_Bits',0,33,142,3,16,4,11
	.byte	'CountValue',0,4
	.word	491
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	491
	.byte	1,0,2,35,0,0,29
	.byte	'Ifx_CPU_M1CNT_Bits',0,33,146,3,3
	.word	81609
	.byte	10
	.byte	'_Ifx_CPU_M2CNT_Bits',0,33,149,3,16,4,11
	.byte	'CountValue',0,4
	.word	491
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	491
	.byte	1,0,2,35,0,0,29
	.byte	'Ifx_CPU_M2CNT_Bits',0,33,153,3,3
	.word	81702
	.byte	10
	.byte	'_Ifx_CPU_M3CNT_Bits',0,33,156,3,16,4,11
	.byte	'CountValue',0,4
	.word	491
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	491
	.byte	1,0,2,35,0,0,29
	.byte	'Ifx_CPU_M3CNT_Bits',0,33,160,3,3
	.word	81795
	.byte	10
	.byte	'_Ifx_CPU_PC_Bits',0,33,163,3,16,4,11
	.byte	'reserved_0',0,4
	.word	491
	.byte	1,31,2,35,0,11
	.byte	'PC',0,4
	.word	491
	.byte	31,0,2,35,0,0,29
	.byte	'Ifx_CPU_PC_Bits',0,33,167,3,3
	.word	81888
	.byte	10
	.byte	'_Ifx_CPU_PCON0_Bits',0,33,170,3,16,4,11
	.byte	'reserved_0',0,4
	.word	491
	.byte	1,31,2,35,0,11
	.byte	'PCBYP',0,4
	.word	491
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	491
	.byte	30,0,2,35,0,0,29
	.byte	'Ifx_CPU_PCON0_Bits',0,33,175,3,3
	.word	81973
	.byte	10
	.byte	'_Ifx_CPU_PCON1_Bits',0,33,178,3,16,4,11
	.byte	'PCINV',0,4
	.word	491
	.byte	1,31,2,35,0,11
	.byte	'PBINV',0,4
	.word	491
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	491
	.byte	30,0,2,35,0,0,29
	.byte	'Ifx_CPU_PCON1_Bits',0,33,183,3,3
	.word	82089
	.byte	10
	.byte	'_Ifx_CPU_PCON2_Bits',0,33,186,3,16,4,11
	.byte	'PCACHE_SZE',0,4
	.word	491
	.byte	16,16,2,35,0,11
	.byte	'PSCRATCH_SZE',0,4
	.word	491
	.byte	16,0,2,35,0,0,29
	.byte	'Ifx_CPU_PCON2_Bits',0,33,190,3,3
	.word	82200
	.byte	10
	.byte	'_Ifx_CPU_PCXI_Bits',0,33,193,3,16,4,11
	.byte	'PCXO',0,4
	.word	491
	.byte	16,16,2,35,0,11
	.byte	'PCXS',0,4
	.word	491
	.byte	4,12,2,35,0,11
	.byte	'UL',0,4
	.word	491
	.byte	1,11,2,35,0,11
	.byte	'PIE',0,4
	.word	491
	.byte	1,10,2,35,0,11
	.byte	'PCPN',0,4
	.word	491
	.byte	10,0,2,35,0,0,29
	.byte	'Ifx_CPU_PCXI_Bits',0,33,200,3,3
	.word	82301
	.byte	10
	.byte	'_Ifx_CPU_PIEAR_Bits',0,33,203,3,16,4,11
	.byte	'TA',0,4
	.word	491
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_PIEAR_Bits',0,33,206,3,3
	.word	82431
	.byte	10
	.byte	'_Ifx_CPU_PIETR_Bits',0,33,209,3,16,4,11
	.byte	'IED',0,4
	.word	491
	.byte	1,31,2,35,0,11
	.byte	'IE_T',0,4
	.word	491
	.byte	1,30,2,35,0,11
	.byte	'IE_C',0,4
	.word	491
	.byte	1,29,2,35,0,11
	.byte	'IE_S',0,4
	.word	491
	.byte	1,28,2,35,0,11
	.byte	'IE_BI',0,4
	.word	491
	.byte	1,27,2,35,0,11
	.byte	'E_INFO',0,4
	.word	491
	.byte	6,21,2,35,0,11
	.byte	'IE_DUAL',0,4
	.word	491
	.byte	1,20,2,35,0,11
	.byte	'IE_SP',0,4
	.word	491
	.byte	1,19,2,35,0,11
	.byte	'IE_BS',0,4
	.word	491
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	491
	.byte	18,0,2,35,0,0,29
	.byte	'Ifx_CPU_PIETR_Bits',0,33,221,3,3
	.word	82500
	.byte	10
	.byte	'_Ifx_CPU_PMA0_Bits',0,33,224,3,16,4,11
	.byte	'reserved_0',0,4
	.word	491
	.byte	13,19,2,35,0,11
	.byte	'DAC',0,4
	.word	491
	.byte	3,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	491
	.byte	16,0,2,35,0,0,29
	.byte	'Ifx_CPU_PMA0_Bits',0,33,229,3,3
	.word	82729
	.byte	10
	.byte	'_Ifx_CPU_PMA1_Bits',0,33,232,3,16,4,11
	.byte	'reserved_0',0,4
	.word	491
	.byte	14,18,2,35,0,11
	.byte	'CAC',0,4
	.word	491
	.byte	2,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	491
	.byte	16,0,2,35,0,0,29
	.byte	'Ifx_CPU_PMA1_Bits',0,33,237,3,3
	.word	82842
	.byte	10
	.byte	'_Ifx_CPU_PMA2_Bits',0,33,240,3,16,4,11
	.byte	'PSI',0,4
	.word	491
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	491
	.byte	16,0,2,35,0,0,29
	.byte	'Ifx_CPU_PMA2_Bits',0,33,244,3,3
	.word	82955
	.byte	10
	.byte	'_Ifx_CPU_PSTR_Bits',0,33,247,3,16,4,11
	.byte	'FRE',0,4
	.word	491
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	491
	.byte	1,30,2,35,0,11
	.byte	'FBE',0,4
	.word	491
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	491
	.byte	9,20,2,35,0,11
	.byte	'FPE',0,4
	.word	491
	.byte	1,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	491
	.byte	1,18,2,35,0,11
	.byte	'FME',0,4
	.word	491
	.byte	1,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	491
	.byte	17,0,2,35,0,0,29
	.byte	'Ifx_CPU_PSTR_Bits',0,33,129,4,3
	.word	83046
	.byte	10
	.byte	'_Ifx_CPU_PSW_Bits',0,33,132,4,16,4,11
	.byte	'CDC',0,4
	.word	491
	.byte	7,25,2,35,0,11
	.byte	'CDE',0,4
	.word	491
	.byte	1,24,2,35,0,11
	.byte	'GW',0,4
	.word	491
	.byte	1,23,2,35,0,11
	.byte	'IS',0,4
	.word	491
	.byte	1,22,2,35,0,11
	.byte	'IO',0,4
	.word	491
	.byte	2,20,2,35,0,11
	.byte	'PRS',0,4
	.word	491
	.byte	2,18,2,35,0,11
	.byte	'S',0,4
	.word	491
	.byte	1,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	491
	.byte	12,5,2,35,0,11
	.byte	'SAV',0,4
	.word	491
	.byte	1,4,2,35,0,11
	.byte	'AV',0,4
	.word	491
	.byte	1,3,2,35,0,11
	.byte	'SV',0,4
	.word	491
	.byte	1,2,2,35,0,11
	.byte	'V',0,4
	.word	491
	.byte	1,1,2,35,0,11
	.byte	'C',0,4
	.word	491
	.byte	1,0,2,35,0,0,29
	.byte	'Ifx_CPU_PSW_Bits',0,33,147,4,3
	.word	83249
	.byte	10
	.byte	'_Ifx_CPU_SEGEN_Bits',0,33,150,4,16,4,11
	.byte	'ADFLIP',0,4
	.word	491
	.byte	8,24,2,35,0,11
	.byte	'ADTYPE',0,4
	.word	491
	.byte	2,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	491
	.byte	21,1,2,35,0,11
	.byte	'AE',0,4
	.word	491
	.byte	1,0,2,35,0,0,29
	.byte	'Ifx_CPU_SEGEN_Bits',0,33,156,4,3
	.word	83492
	.byte	10
	.byte	'_Ifx_CPU_SMACON_Bits',0,33,159,4,16,4,11
	.byte	'PC',0,4
	.word	491
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	491
	.byte	1,30,2,35,0,11
	.byte	'PT',0,4
	.word	491
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	491
	.byte	5,24,2,35,0,11
	.byte	'DC',0,4
	.word	491
	.byte	1,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	491
	.byte	1,22,2,35,0,11
	.byte	'DT',0,4
	.word	491
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	491
	.byte	13,8,2,35,0,11
	.byte	'IODT',0,4
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	491
	.byte	7,0,2,35,0,0,29
	.byte	'Ifx_CPU_SMACON_Bits',0,33,171,4,3
	.word	83620
	.byte	10
	.byte	'_Ifx_CPU_SPROT_ACCENA_Bits',0,33,174,4,16,4,11
	.byte	'EN',0,4
	.word	468
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_SPROT_ACCENA_Bits',0,33,177,4,3
	.word	83861
	.byte	10
	.byte	'_Ifx_CPU_SPROT_ACCENB_Bits',0,33,180,4,16,4,11
	.byte	'reserved_0',0,4
	.word	468
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_SPROT_ACCENB_Bits',0,33,183,4,3
	.word	83944
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,33,186,4,16,4,11
	.byte	'EN',0,4
	.word	468
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,33,189,4,3
	.word	84035
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,33,192,4,16,4,11
	.byte	'reserved_0',0,4
	.word	468
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,33,195,4,3
	.word	84126
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_LA_Bits',0,33,198,4,16,4,11
	.byte	'reserved_0',0,1
	.word	643
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	468
	.byte	27,0,2,35,0,0,29
	.byte	'Ifx_CPU_SPROT_RGN_LA_Bits',0,33,202,4,3
	.word	84225
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_UA_Bits',0,33,205,4,16,4,11
	.byte	'reserved_0',0,1
	.word	643
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	468
	.byte	27,0,2,35,0,0,29
	.byte	'Ifx_CPU_SPROT_RGN_UA_Bits',0,33,209,4,3
	.word	84332
	.byte	10
	.byte	'_Ifx_CPU_SWEVT_Bits',0,33,212,4,16,4,11
	.byte	'EVTA',0,4
	.word	491
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	491
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	491
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	491
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	491
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	491
	.byte	24,0,2,35,0,0,29
	.byte	'Ifx_CPU_SWEVT_Bits',0,33,220,4,3
	.word	84439
	.byte	10
	.byte	'_Ifx_CPU_SYSCON_Bits',0,33,223,4,16,4,11
	.byte	'FCDSF',0,4
	.word	491
	.byte	1,31,2,35,0,11
	.byte	'PROTEN',0,4
	.word	491
	.byte	1,30,2,35,0,11
	.byte	'TPROTEN',0,4
	.word	491
	.byte	1,29,2,35,0,11
	.byte	'IS',0,4
	.word	491
	.byte	1,28,2,35,0,11
	.byte	'IT',0,4
	.word	491
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	491
	.byte	27,0,2,35,0,0,29
	.byte	'Ifx_CPU_SYSCON_Bits',0,33,231,4,3
	.word	84593
	.byte	10
	.byte	'_Ifx_CPU_TASK_ASI_Bits',0,33,234,4,16,4,11
	.byte	'ASI',0,4
	.word	491
	.byte	5,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	491
	.byte	27,0,2,35,0,0,29
	.byte	'Ifx_CPU_TASK_ASI_Bits',0,33,238,4,3
	.word	84754
	.byte	10
	.byte	'_Ifx_CPU_TPS_CON_Bits',0,33,241,4,16,4,11
	.byte	'TEXP0',0,4
	.word	491
	.byte	1,31,2,35,0,11
	.byte	'TEXP1',0,4
	.word	491
	.byte	1,30,2,35,0,11
	.byte	'TEXP2',0,4
	.word	491
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	491
	.byte	13,16,2,35,0,11
	.byte	'TTRAP',0,4
	.word	491
	.byte	1,15,2,35,0,11
	.byte	'reserved_17',0,4
	.word	491
	.byte	15,0,2,35,0,0,29
	.byte	'Ifx_CPU_TPS_CON_Bits',0,33,249,4,3
	.word	84852
	.byte	10
	.byte	'_Ifx_CPU_TPS_TIMER_Bits',0,33,252,4,16,4,11
	.byte	'Timer',0,4
	.word	491
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_TPS_TIMER_Bits',0,33,255,4,3
	.word	85024
	.byte	10
	.byte	'_Ifx_CPU_TR_ADR_Bits',0,33,130,5,16,4,11
	.byte	'ADDR',0,4
	.word	491
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_TR_ADR_Bits',0,33,133,5,3
	.word	85104
	.byte	10
	.byte	'_Ifx_CPU_TR_EVT_Bits',0,33,136,5,16,4,11
	.byte	'EVTA',0,4
	.word	491
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	491
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	491
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	491
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	491
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	491
	.byte	4,20,2,35,0,11
	.byte	'TYP',0,4
	.word	491
	.byte	1,19,2,35,0,11
	.byte	'RNG',0,4
	.word	491
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	491
	.byte	1,17,2,35,0,11
	.byte	'ASI_EN',0,4
	.word	491
	.byte	1,16,2,35,0,11
	.byte	'ASI',0,4
	.word	491
	.byte	5,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	491
	.byte	6,5,2,35,0,11
	.byte	'AST',0,4
	.word	491
	.byte	1,4,2,35,0,11
	.byte	'ALD',0,4
	.word	491
	.byte	1,3,2,35,0,11
	.byte	'reserved_29',0,4
	.word	491
	.byte	3,0,2,35,0,0,29
	.byte	'Ifx_CPU_TR_EVT_Bits',0,33,153,5,3
	.word	85177
	.byte	10
	.byte	'_Ifx_CPU_TRIG_ACC_Bits',0,33,156,5,16,4,11
	.byte	'T0',0,4
	.word	491
	.byte	1,31,2,35,0,11
	.byte	'T1',0,4
	.word	491
	.byte	1,30,2,35,0,11
	.byte	'T2',0,4
	.word	491
	.byte	1,29,2,35,0,11
	.byte	'T3',0,4
	.word	491
	.byte	1,28,2,35,0,11
	.byte	'T4',0,4
	.word	491
	.byte	1,27,2,35,0,11
	.byte	'T5',0,4
	.word	491
	.byte	1,26,2,35,0,11
	.byte	'T6',0,4
	.word	491
	.byte	1,25,2,35,0,11
	.byte	'T7',0,4
	.word	491
	.byte	1,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	491
	.byte	24,0,2,35,0,0,29
	.byte	'Ifx_CPU_TRIG_ACC_Bits',0,33,167,5,3
	.word	85495
	.byte	12,33,175,5,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	76713
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_A',0,33,180,5,3
	.word	85690
	.byte	12,33,183,5,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	76774
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_BIV',0,33,188,5,3
	.word	85749
	.byte	12,33,191,5,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	76853
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_BTV',0,33,196,5,3
	.word	85810
	.byte	12,33,199,5,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	76939
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_CCNT',0,33,204,5,3
	.word	85871
	.byte	12,33,207,5,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	77028
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_CCTRL',0,33,212,5,3
	.word	85933
	.byte	12,33,215,5,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	77174
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_COMPAT',0,33,220,5,3
	.word	85996
	.byte	12,33,223,5,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	77301
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_CORE_ID',0,33,228,5,3
	.word	86060
	.byte	12,33,231,5,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	77399
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_CPR_L',0,33,236,5,3
	.word	86125
	.byte	12,33,239,5,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	77492
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_CPR_U',0,33,244,5,3
	.word	86188
	.byte	12,33,247,5,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	77585
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_CPU_ID',0,33,252,5,3
	.word	86251
	.byte	12,33,255,5,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	77692
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_CPXE',0,33,132,6,3
	.word	86315
	.byte	12,33,135,6,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	77779
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_CREVT',0,33,140,6,3
	.word	86377
	.byte	12,33,143,6,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	77933
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_CUS_ID',0,33,148,6,3
	.word	86440
	.byte	12,33,151,6,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	78027
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_D',0,33,156,6,3
	.word	86504
	.byte	12,33,159,6,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	78090
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_DATR',0,33,164,6,3
	.word	86563
	.byte	12,33,167,6,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	78308
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_DBGSR',0,33,172,6,3
	.word	86625
	.byte	12,33,175,6,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	78523
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_DBGTCR',0,33,180,6,3
	.word	86688
	.byte	12,33,183,6,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	78617
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_DCON0',0,33,188,6,3
	.word	86752
	.byte	12,33,191,6,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	78733
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_DCON2',0,33,196,6,3
	.word	86815
	.byte	12,33,199,6,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	78834
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_DCX',0,33,204,6,3
	.word	86878
	.byte	12,33,207,6,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	78927
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_DEADD',0,33,212,6,3
	.word	86939
	.byte	12,33,215,6,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	79007
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_DIEAR',0,33,220,6,3
	.word	87002
	.byte	12,33,223,6,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	79076
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_DIETR',0,33,228,6,3
	.word	87065
	.byte	12,33,231,6,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	79305
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_DMS',0,33,236,6,3
	.word	87128
	.byte	12,33,239,6,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	79398
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_DPR_L',0,33,244,6,3
	.word	87189
	.byte	12,33,247,6,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	79493
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_DPR_U',0,33,252,6,3
	.word	87252
	.byte	12,33,255,6,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	79588
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_DPRE',0,33,132,7,3
	.word	87315
	.byte	12,33,135,7,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	79678
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_DPWE',0,33,140,7,3
	.word	87377
	.byte	12,33,143,7,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	79768
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_DSTR',0,33,148,7,3
	.word	87439
	.byte	12,33,151,7,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	80092
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_EXEVT',0,33,156,7,3
	.word	87501
	.byte	12,33,159,7,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	80246
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_FCX',0,33,164,7,3
	.word	87564
	.byte	12,33,167,7,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	80352
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_FPU_TRAP_CON',0,33,172,7,3
	.word	87625
	.byte	12,33,175,7,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	80701
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_FPU_TRAP_OPC',0,33,180,7,3
	.word	87695
	.byte	12,33,183,7,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	80861
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_FPU_TRAP_PC',0,33,188,7,3
	.word	87765
	.byte	12,33,191,7,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	80942
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_FPU_TRAP_SRC1',0,33,196,7,3
	.word	87834
	.byte	12,33,199,7,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81029
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_FPU_TRAP_SRC2',0,33,204,7,3
	.word	87905
	.byte	12,33,207,7,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81116
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_FPU_TRAP_SRC3',0,33,212,7,3
	.word	87976
	.byte	12,33,215,7,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81203
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_ICNT',0,33,220,7,3
	.word	88047
	.byte	12,33,223,7,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81294
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_ICR',0,33,228,7,3
	.word	88109
	.byte	12,33,231,7,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81437
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_ISP',0,33,236,7,3
	.word	88170
	.byte	12,33,239,7,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81503
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_LCX',0,33,244,7,3
	.word	88231
	.byte	12,33,247,7,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81609
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_M1CNT',0,33,252,7,3
	.word	88292
	.byte	12,33,255,7,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81702
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_M2CNT',0,33,132,8,3
	.word	88355
	.byte	12,33,135,8,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81795
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_M3CNT',0,33,140,8,3
	.word	88418
	.byte	12,33,143,8,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81888
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_PC',0,33,148,8,3
	.word	88481
	.byte	12,33,151,8,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81973
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_PCON0',0,33,156,8,3
	.word	88541
	.byte	12,33,159,8,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	82089
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_PCON1',0,33,164,8,3
	.word	88604
	.byte	12,33,167,8,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	82200
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_PCON2',0,33,172,8,3
	.word	88667
	.byte	12,33,175,8,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	82301
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_PCXI',0,33,180,8,3
	.word	88730
	.byte	12,33,183,8,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	82431
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_PIEAR',0,33,188,8,3
	.word	88792
	.byte	12,33,191,8,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	82500
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_PIETR',0,33,196,8,3
	.word	88855
	.byte	12,33,199,8,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	82729
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_PMA0',0,33,204,8,3
	.word	88918
	.byte	12,33,207,8,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	82842
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_PMA1',0,33,212,8,3
	.word	88980
	.byte	12,33,215,8,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	82955
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_PMA2',0,33,220,8,3
	.word	89042
	.byte	12,33,223,8,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	83046
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_PSTR',0,33,228,8,3
	.word	89104
	.byte	12,33,231,8,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	83249
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_PSW',0,33,236,8,3
	.word	89166
	.byte	12,33,239,8,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	83492
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_SEGEN',0,33,244,8,3
	.word	89227
	.byte	12,33,247,8,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	83620
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_SMACON',0,33,252,8,3
	.word	89290
	.byte	12,33,255,8,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	83861
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_SPROT_ACCENA',0,33,132,9,3
	.word	89354
	.byte	12,33,135,9,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	83944
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_SPROT_ACCENB',0,33,140,9,3
	.word	89424
	.byte	12,33,143,9,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	84035
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA',0,33,148,9,3
	.word	89494
	.byte	12,33,151,9,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	84126
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB',0,33,156,9,3
	.word	89568
	.byte	12,33,159,9,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	84225
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_SPROT_RGN_LA',0,33,164,9,3
	.word	89642
	.byte	12,33,167,9,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	84332
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_SPROT_RGN_UA',0,33,172,9,3
	.word	89712
	.byte	12,33,175,9,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	84439
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_SWEVT',0,33,180,9,3
	.word	89782
	.byte	12,33,183,9,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	84593
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_SYSCON',0,33,188,9,3
	.word	89845
	.byte	12,33,191,9,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	84754
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_TASK_ASI',0,33,196,9,3
	.word	89909
	.byte	12,33,199,9,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	84852
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_TPS_CON',0,33,204,9,3
	.word	89975
	.byte	12,33,207,9,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	85024
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_TPS_TIMER',0,33,212,9,3
	.word	90040
	.byte	12,33,215,9,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	85104
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_TR_ADR',0,33,220,9,3
	.word	90107
	.byte	12,33,223,9,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	85177
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_TR_EVT',0,33,228,9,3
	.word	90171
	.byte	12,33,231,9,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	85495
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_TRIG_ACC',0,33,236,9,3
	.word	90235
	.byte	10
	.byte	'_Ifx_CPU_CPR',0,33,247,9,25,8,13
	.byte	'L',0
	.word	86125
	.byte	4,2,35,0,13
	.byte	'U',0
	.word	86188
	.byte	4,2,35,4,0,14
	.word	90301
	.byte	29
	.byte	'Ifx_CPU_CPR',0,33,251,9,3
	.word	90343
	.byte	10
	.byte	'_Ifx_CPU_DPR',0,33,254,9,25,8,13
	.byte	'L',0
	.word	87189
	.byte	4,2,35,0,13
	.byte	'U',0
	.word	87252
	.byte	4,2,35,4,0,14
	.word	90369
	.byte	29
	.byte	'Ifx_CPU_DPR',0,33,130,10,3
	.word	90411
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN',0,33,133,10,25,16,13
	.byte	'LA',0
	.word	89642
	.byte	4,2,35,0,13
	.byte	'UA',0
	.word	89712
	.byte	4,2,35,4,13
	.byte	'ACCENA',0
	.word	89494
	.byte	4,2,35,8,13
	.byte	'ACCENB',0
	.word	89568
	.byte	4,2,35,12,0,14
	.word	90437
	.byte	29
	.byte	'Ifx_CPU_SPROT_RGN',0,33,139,10,3
	.word	90519
	.byte	15,12
	.word	90040
	.byte	16,2,0,10
	.byte	'_Ifx_CPU_TPS',0,33,142,10,25,16,13
	.byte	'CON',0
	.word	89975
	.byte	4,2,35,0,13
	.byte	'TIMER',0
	.word	90551
	.byte	12,2,35,4,0,14
	.word	90560
	.byte	29
	.byte	'Ifx_CPU_TPS',0,33,146,10,3
	.word	90608
	.byte	10
	.byte	'_Ifx_CPU_TR',0,33,149,10,25,8,13
	.byte	'EVT',0
	.word	90171
	.byte	4,2,35,0,13
	.byte	'ADR',0
	.word	90107
	.byte	4,2,35,4,0,14
	.word	90634
	.byte	29
	.byte	'Ifx_CPU_TR',0,33,153,10,3
	.word	90679
	.byte	15,176,32
	.word	643
	.byte	16,175,32,0,15,208,223,1
	.word	643
	.byte	16,207,223,1,0,15,248,1
	.word	643
	.byte	16,247,1,0,15,244,29
	.word	643
	.byte	16,243,29,0,15,188,3
	.word	643
	.byte	16,187,3,0,15,232,3
	.word	643
	.byte	16,231,3,0,15,252,23
	.word	643
	.byte	16,251,23,0,15,228,63
	.word	643
	.byte	16,227,63,0,15,128,1
	.word	90369
	.byte	16,15,0,14
	.word	90794
	.byte	15,64
	.word	90301
	.byte	16,7,0,14
	.word	90809
	.byte	15,192,31
	.word	643
	.byte	16,191,31,0,15,16
	.word	86315
	.byte	16,3,0,15,16
	.word	87315
	.byte	16,3,0,15,16
	.word	87377
	.byte	16,3,0,15,208,7
	.word	643
	.byte	16,207,7,0,14
	.word	90560
	.byte	15,240,23
	.word	643
	.byte	16,239,23,0,15,64
	.word	90634
	.byte	16,7,0,14
	.word	90888
	.byte	15,192,23
	.word	643
	.byte	16,191,23,0,15,232,1
	.word	643
	.byte	16,231,1,0,15,180,1
	.word	643
	.byte	16,179,1,0,15,172,1
	.word	643
	.byte	16,171,1,0,15,64
	.word	86504
	.byte	16,15,0,15,64
	.word	643
	.byte	16,63,0,15,64
	.word	85690
	.byte	16,15,0,10
	.byte	'_Ifx_CPU',0,33,166,10,25,128,128,4,13
	.byte	'reserved_0',0
	.word	90704
	.byte	176,32,2,35,0,13
	.byte	'SEGEN',0
	.word	89227
	.byte	4,3,35,176,32,13
	.byte	'reserved_1034',0
	.word	90715
	.byte	208,223,1,3,35,180,32,13
	.byte	'TASK_ASI',0
	.word	89909
	.byte	4,4,35,132,128,2,13
	.byte	'reserved_8008',0
	.word	90728
	.byte	248,1,4,35,136,128,2,13
	.byte	'PMA0',0
	.word	88918
	.byte	4,4,35,128,130,2,13
	.byte	'PMA1',0
	.word	88980
	.byte	4,4,35,132,130,2,13
	.byte	'PMA2',0
	.word	89042
	.byte	4,4,35,136,130,2,13
	.byte	'reserved_810C',0
	.word	90739
	.byte	244,29,4,35,140,130,2,13
	.byte	'DCON2',0
	.word	86815
	.byte	4,4,35,128,160,2,13
	.byte	'reserved_9004',0
	.word	4460
	.byte	8,4,35,132,160,2,13
	.byte	'SMACON',0
	.word	89290
	.byte	4,4,35,140,160,2,13
	.byte	'DSTR',0
	.word	87439
	.byte	4,4,35,144,160,2,13
	.byte	'reserved_9014',0
	.word	2641
	.byte	4,4,35,148,160,2,13
	.byte	'DATR',0
	.word	86563
	.byte	4,4,35,152,160,2,13
	.byte	'DEADD',0
	.word	86939
	.byte	4,4,35,156,160,2,13
	.byte	'DIEAR',0
	.word	87002
	.byte	4,4,35,160,160,2,13
	.byte	'DIETR',0
	.word	87065
	.byte	4,4,35,164,160,2,13
	.byte	'reserved_9028',0
	.word	3831
	.byte	24,4,35,168,160,2,13
	.byte	'DCON0',0
	.word	86752
	.byte	4,4,35,192,160,2,13
	.byte	'reserved_9044',0
	.word	90750
	.byte	188,3,4,35,196,160,2,13
	.byte	'PSTR',0
	.word	89104
	.byte	4,4,35,128,164,2,13
	.byte	'PCON1',0
	.word	88604
	.byte	4,4,35,132,164,2,13
	.byte	'PCON2',0
	.word	88667
	.byte	4,4,35,136,164,2,13
	.byte	'PCON0',0
	.word	88541
	.byte	4,4,35,140,164,2,13
	.byte	'PIEAR',0
	.word	88792
	.byte	4,4,35,144,164,2,13
	.byte	'PIETR',0
	.word	88855
	.byte	4,4,35,148,164,2,13
	.byte	'reserved_9218',0
	.word	90761
	.byte	232,3,4,35,152,164,2,13
	.byte	'COMPAT',0
	.word	85996
	.byte	4,4,35,128,168,2,13
	.byte	'reserved_9404',0
	.word	90772
	.byte	252,23,4,35,132,168,2,13
	.byte	'FPU_TRAP_CON',0
	.word	87625
	.byte	4,4,35,128,192,2,13
	.byte	'FPU_TRAP_PC',0
	.word	87765
	.byte	4,4,35,132,192,2,13
	.byte	'FPU_TRAP_OPC',0
	.word	87695
	.byte	4,4,35,136,192,2,13
	.byte	'reserved_A00C',0
	.word	2641
	.byte	4,4,35,140,192,2,13
	.byte	'FPU_TRAP_SRC1',0
	.word	87834
	.byte	4,4,35,144,192,2,13
	.byte	'FPU_TRAP_SRC2',0
	.word	87905
	.byte	4,4,35,148,192,2,13
	.byte	'FPU_TRAP_SRC3',0
	.word	87976
	.byte	4,4,35,152,192,2,13
	.byte	'reserved_A01C',0
	.word	90783
	.byte	228,63,4,35,156,192,2,13
	.byte	'DPR',0
	.word	90804
	.byte	128,1,4,35,128,128,3,13
	.byte	'reserved_C080',0
	.word	69553
	.byte	128,31,4,35,128,129,3,13
	.byte	'CPR',0
	.word	90818
	.byte	64,4,35,128,160,3,13
	.byte	'reserved_D040',0
	.word	90823
	.byte	192,31,4,35,192,160,3,13
	.byte	'CPXE',0
	.word	90834
	.byte	16,4,35,128,192,3,13
	.byte	'DPRE',0
	.word	90843
	.byte	16,4,35,144,192,3,13
	.byte	'DPWE',0
	.word	90852
	.byte	16,4,35,160,192,3,13
	.byte	'reserved_E030',0
	.word	90861
	.byte	208,7,4,35,176,192,3,13
	.byte	'TPS',0
	.word	90872
	.byte	16,4,35,128,200,3,13
	.byte	'reserved_E410',0
	.word	90877
	.byte	240,23,4,35,144,200,3,13
	.byte	'TR',0
	.word	90897
	.byte	64,4,35,128,224,3,13
	.byte	'reserved_F040',0
	.word	90902
	.byte	192,23,4,35,192,224,3,13
	.byte	'CCTRL',0
	.word	85933
	.byte	4,4,35,128,248,3,13
	.byte	'CCNT',0
	.word	85871
	.byte	4,4,35,132,248,3,13
	.byte	'ICNT',0
	.word	88047
	.byte	4,4,35,136,248,3,13
	.byte	'M1CNT',0
	.word	88292
	.byte	4,4,35,140,248,3,13
	.byte	'M2CNT',0
	.word	88355
	.byte	4,4,35,144,248,3,13
	.byte	'M3CNT',0
	.word	88418
	.byte	4,4,35,148,248,3,13
	.byte	'reserved_FC18',0
	.word	90913
	.byte	232,1,4,35,152,248,3,13
	.byte	'DBGSR',0
	.word	86625
	.byte	4,4,35,128,250,3,13
	.byte	'reserved_FD04',0
	.word	2641
	.byte	4,4,35,132,250,3,13
	.byte	'EXEVT',0
	.word	87501
	.byte	4,4,35,136,250,3,13
	.byte	'CREVT',0
	.word	86377
	.byte	4,4,35,140,250,3,13
	.byte	'SWEVT',0
	.word	89782
	.byte	4,4,35,144,250,3,13
	.byte	'reserved_FD14',0
	.word	54139
	.byte	28,4,35,148,250,3,13
	.byte	'TRIG_ACC',0
	.word	90235
	.byte	4,4,35,176,250,3,13
	.byte	'reserved_FD34',0
	.word	4800
	.byte	12,4,35,180,250,3,13
	.byte	'DMS',0
	.word	87128
	.byte	4,4,35,192,250,3,13
	.byte	'DCX',0
	.word	86878
	.byte	4,4,35,196,250,3,13
	.byte	'DBGTCR',0
	.word	86688
	.byte	4,4,35,200,250,3,13
	.byte	'reserved_FD4C',0
	.word	90924
	.byte	180,1,4,35,204,250,3,13
	.byte	'PCXI',0
	.word	88730
	.byte	4,4,35,128,252,3,13
	.byte	'PSW',0
	.word	89166
	.byte	4,4,35,132,252,3,13
	.byte	'PC',0
	.word	88481
	.byte	4,4,35,136,252,3,13
	.byte	'reserved_FE0C',0
	.word	4460
	.byte	8,4,35,140,252,3,13
	.byte	'SYSCON',0
	.word	89845
	.byte	4,4,35,148,252,3,13
	.byte	'CPU_ID',0
	.word	86251
	.byte	4,4,35,152,252,3,13
	.byte	'CORE_ID',0
	.word	86060
	.byte	4,4,35,156,252,3,13
	.byte	'BIV',0
	.word	85749
	.byte	4,4,35,160,252,3,13
	.byte	'BTV',0
	.word	85810
	.byte	4,4,35,164,252,3,13
	.byte	'ISP',0
	.word	88170
	.byte	4,4,35,168,252,3,13
	.byte	'ICR',0
	.word	88109
	.byte	4,4,35,172,252,3,13
	.byte	'reserved_FE30',0
	.word	4460
	.byte	8,4,35,176,252,3,13
	.byte	'FCX',0
	.word	87564
	.byte	4,4,35,184,252,3,13
	.byte	'LCX',0
	.word	88231
	.byte	4,4,35,188,252,3,13
	.byte	'reserved_FE40',0
	.word	54130
	.byte	16,4,35,192,252,3,13
	.byte	'CUS_ID',0
	.word	86440
	.byte	4,4,35,208,252,3,13
	.byte	'reserved_FE54',0
	.word	90935
	.byte	172,1,4,35,212,252,3,13
	.byte	'D',0
	.word	90946
	.byte	64,4,35,128,254,3,13
	.byte	'reserved_FF40',0
	.word	90955
	.byte	64,4,35,192,254,3,13
	.byte	'A',0
	.word	90964
	.byte	64,4,35,128,255,3,13
	.byte	'reserved_FFC0',0
	.word	90955
	.byte	64,4,35,192,255,3,0,14
	.word	90973
	.byte	29
	.byte	'Ifx_CPU',0,33,130,11,3
	.word	92764
	.byte	17,10,127,9,1,18
	.byte	'IfxCpu_Id_0',0,0,18
	.byte	'IfxCpu_Id_1',0,1,18
	.byte	'IfxCpu_Id_none',0,2,0,29
	.byte	'IfxCpu_Id',0,10,132,1,3
	.word	92786
	.byte	29
	.byte	'IfxCpu_ResourceCpu',0,10,161,1,3
	.word	10544
	.byte	29
	.byte	'Ifx_STM_ACCEN0_Bits',0,16,79,3
	.word	20975
	.byte	29
	.byte	'Ifx_STM_ACCEN1_Bits',0,16,85,3
	.word	20886
	.byte	29
	.byte	'Ifx_STM_CAP_Bits',0,16,91,3
	.word	19416
	.byte	29
	.byte	'Ifx_STM_CAPSV_Bits',0,16,97,3
	.word	20293
	.byte	29
	.byte	'Ifx_STM_CLC_Bits',0,16,107,3
	.word	18539
	.byte	29
	.byte	'Ifx_STM_CMCON_Bits',0,16,120,3
	.word	19594
	.byte	29
	.byte	'Ifx_STM_CMP_Bits',0,16,126,3
	.word	19503
	.byte	29
	.byte	'Ifx_STM_ICR_Bits',0,16,139,1,3
	.word	19825
	.byte	29
	.byte	'Ifx_STM_ID_Bits',0,16,147,1,3
	.word	18695
	.byte	29
	.byte	'Ifx_STM_ISCR_Bits',0,16,157,1,3
	.word	20042
	.byte	29
	.byte	'Ifx_STM_KRST0_Bits',0,16,165,1,3
	.word	20763
	.byte	29
	.byte	'Ifx_STM_KRST1_Bits',0,16,172,1,3
	.word	20659
	.byte	29
	.byte	'Ifx_STM_KRSTCLR_Bits',0,16,179,1,3
	.word	20553
	.byte	29
	.byte	'Ifx_STM_OCS_Bits',0,16,189,1,3
	.word	20393
	.byte	29
	.byte	'Ifx_STM_TIM0_Bits',0,16,195,1,3
	.word	18817
	.byte	29
	.byte	'Ifx_STM_TIM0SV_Bits',0,16,201,1,3
	.word	20206
	.byte	29
	.byte	'Ifx_STM_TIM1_Bits',0,16,207,1,3
	.word	18902
	.byte	29
	.byte	'Ifx_STM_TIM2_Bits',0,16,213,1,3
	.word	18987
	.byte	29
	.byte	'Ifx_STM_TIM3_Bits',0,16,219,1,3
	.word	19072
	.byte	29
	.byte	'Ifx_STM_TIM4_Bits',0,16,225,1,3
	.word	19158
	.byte	29
	.byte	'Ifx_STM_TIM5_Bits',0,16,231,1,3
	.word	19244
	.byte	29
	.byte	'Ifx_STM_TIM6_Bits',0,16,237,1,3
	.word	19330
	.byte	29
	.byte	'Ifx_STM_ACCEN0',0,16,250,1,3
	.word	21504
	.byte	29
	.byte	'Ifx_STM_ACCEN1',0,16,130,2,3
	.word	20935
	.byte	29
	.byte	'Ifx_STM_CAP',0,16,138,2,3
	.word	19463
	.byte	29
	.byte	'Ifx_STM_CAPSV',0,16,146,2,3
	.word	20342
	.byte	29
	.byte	'Ifx_STM_CLC',0,16,154,2,3
	.word	18655
	.byte	29
	.byte	'Ifx_STM_CMCON',0,16,162,2,3
	.word	19785
	.byte	29
	.byte	'Ifx_STM_CMP',0,16,170,2,3
	.word	19545
	.byte	29
	.byte	'Ifx_STM_ICR',0,16,178,2,3
	.word	20002
	.byte	29
	.byte	'Ifx_STM_ID',0,16,186,2,3
	.word	18777
	.byte	29
	.byte	'Ifx_STM_ISCR',0,16,194,2,3
	.word	20166
	.byte	29
	.byte	'Ifx_STM_KRST0',0,16,202,2,3
	.word	20846
	.byte	29
	.byte	'Ifx_STM_KRST1',0,16,210,2,3
	.word	20723
	.byte	29
	.byte	'Ifx_STM_KRSTCLR',0,16,218,2,3
	.word	20619
	.byte	29
	.byte	'Ifx_STM_OCS',0,16,226,2,3
	.word	20513
	.byte	29
	.byte	'Ifx_STM_TIM0',0,16,234,2,3
	.word	18862
	.byte	29
	.byte	'Ifx_STM_TIM0SV',0,16,242,2,3
	.word	20253
	.byte	29
	.byte	'Ifx_STM_TIM1',0,16,250,2,3
	.word	18947
	.byte	29
	.byte	'Ifx_STM_TIM2',0,16,130,3,3
	.word	19032
	.byte	29
	.byte	'Ifx_STM_TIM3',0,16,138,3,3
	.word	19118
	.byte	29
	.byte	'Ifx_STM_TIM4',0,16,146,3,3
	.word	19204
	.byte	29
	.byte	'Ifx_STM_TIM5',0,16,154,3,3
	.word	19290
	.byte	29
	.byte	'Ifx_STM_TIM6',0,16,162,3,3
	.word	19376
	.byte	14
	.word	21544
	.byte	29
	.byte	'Ifx_STM',0,16,201,3,3
	.word	93967
	.byte	17,9,144,1,9,1,18
	.byte	'IfxCpu_CounterMode_normal',0,0,18
	.byte	'IfxCpu_CounterMode_task',0,1,0,29
	.byte	'IfxCpu_CounterMode',0,9,148,1,3
	.word	93989
	.byte	20,9,160,1,9,6,13
	.byte	'counter',0
	.word	10866
	.byte	4,2,35,0,13
	.byte	'overlfow',0
	.word	643
	.byte	1,2,35,4,0,29
	.byte	'IfxCpu_Counter',0,9,164,1,3
	.word	94078
	.byte	20,9,172,1,9,32,13
	.byte	'instruction',0
	.word	94078
	.byte	6,2,35,0,13
	.byte	'clock',0
	.word	94078
	.byte	6,2,35,6,13
	.byte	'counter1',0
	.word	94078
	.byte	6,2,35,12,13
	.byte	'counter2',0
	.word	94078
	.byte	6,2,35,18,13
	.byte	'counter3',0
	.word	94078
	.byte	6,2,35,24,0,29
	.byte	'IfxCpu_Perf',0,9,179,1,3
	.word	94144
	.byte	10
	.byte	'_Ifx_FLASH_ACCEN0_Bits',0,34,45,16,4,11
	.byte	'EN0',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	643
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	643
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	643
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	643
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	643
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	643
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	643
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	643
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	643
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	643
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	643
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	643
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	643
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	643
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	643
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	643
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	643
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	643
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	643
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	643
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	643
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	643
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	643
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	643
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	643
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	643
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	643
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	643
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	643
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	643
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	643
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_FLASH_ACCEN0_Bits',0,34,79,3
	.word	94262
	.byte	10
	.byte	'_Ifx_FLASH_ACCEN1_Bits',0,34,82,16,4,11
	.byte	'reserved_0',0,4
	.word	468
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_FLASH_ACCEN1_Bits',0,34,85,3
	.word	94823
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_CFG_Bits',0,34,88,16,4,11
	.byte	'SEL',0,1
	.word	643
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	643
	.byte	2,0,2,35,0,11
	.byte	'CLR',0,1
	.word	643
	.byte	1,7,2,35,1,11
	.byte	'DIS',0,1
	.word	643
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	468
	.byte	22,0,2,35,0,0,29
	.byte	'Ifx_FLASH_CBAB_CFG_Bits',0,34,95,3
	.word	94904
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_STAT_Bits',0,34,98,16,4,11
	.byte	'VLD0',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'VLD1',0,1
	.word	643
	.byte	1,6,2,35,0,11
	.byte	'VLD2',0,1
	.word	643
	.byte	1,5,2,35,0,11
	.byte	'VLD3',0,1
	.word	643
	.byte	1,4,2,35,0,11
	.byte	'VLD4',0,1
	.word	643
	.byte	1,3,2,35,0,11
	.byte	'VLD5',0,1
	.word	643
	.byte	1,2,2,35,0,11
	.byte	'VLD6',0,1
	.word	643
	.byte	1,1,2,35,0,11
	.byte	'VLD7',0,1
	.word	643
	.byte	1,0,2,35,0,11
	.byte	'VLD8',0,1
	.word	643
	.byte	1,7,2,35,1,11
	.byte	'VLD9',0,1
	.word	643
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	468
	.byte	22,0,2,35,0,0,29
	.byte	'Ifx_FLASH_CBAB_STAT_Bits',0,34,111,3
	.word	95057
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_TOP_Bits',0,34,114,16,4,11
	.byte	'reserved_0',0,1
	.word	643
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	468
	.byte	19,8,2,35,0,11
	.byte	'ERR',0,1
	.word	643
	.byte	6,2,2,35,3,11
	.byte	'VLD',0,1
	.word	643
	.byte	1,1,2,35,3,11
	.byte	'CLR',0,1
	.word	643
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_FLASH_CBAB_TOP_Bits',0,34,121,3
	.word	95305
	.byte	10
	.byte	'_Ifx_FLASH_COMM0_Bits',0,34,124,16,4,11
	.byte	'STATUS',0,1
	.word	643
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	468
	.byte	24,0,2,35,0,0,29
	.byte	'Ifx_FLASH_COMM0_Bits',0,34,128,1,3
	.word	95451
	.byte	10
	.byte	'_Ifx_FLASH_COMM1_Bits',0,34,131,1,16,4,11
	.byte	'STATUS',0,1
	.word	643
	.byte	8,0,2,35,0,11
	.byte	'DATA',0,1
	.word	643
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	660
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_FLASH_COMM1_Bits',0,34,136,1,3
	.word	95549
	.byte	10
	.byte	'_Ifx_FLASH_COMM2_Bits',0,34,139,1,16,4,11
	.byte	'STATUS',0,1
	.word	643
	.byte	8,0,2,35,0,11
	.byte	'DATA',0,1
	.word	643
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	660
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_FLASH_COMM2_Bits',0,34,144,1,3
	.word	95665
	.byte	10
	.byte	'_Ifx_FLASH_ECCRD_Bits',0,34,147,1,16,4,11
	.byte	'RCODE',0,4
	.word	468
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	660
	.byte	8,2,2,35,2,11
	.byte	'EDCERRINJ',0,1
	.word	643
	.byte	1,1,2,35,3,11
	.byte	'ECCORDIS',0,1
	.word	643
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_FLASH_ECCRD_Bits',0,34,153,1,3
	.word	95781
	.byte	10
	.byte	'_Ifx_FLASH_ECCRP_Bits',0,34,156,1,16,4,11
	.byte	'RCODE',0,4
	.word	468
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	660
	.byte	8,2,2,35,2,11
	.byte	'EDCERRINJ',0,1
	.word	643
	.byte	1,1,2,35,3,11
	.byte	'ECCORDIS',0,1
	.word	643
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_FLASH_ECCRP_Bits',0,34,162,1,3
	.word	95921
	.byte	10
	.byte	'_Ifx_FLASH_ECCW_Bits',0,34,165,1,16,4,11
	.byte	'WCODE',0,4
	.word	468
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	660
	.byte	8,2,2,35,2,11
	.byte	'DECENCDIS',0,1
	.word	643
	.byte	1,1,2,35,3,11
	.byte	'PECENCDIS',0,1
	.word	643
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_FLASH_ECCW_Bits',0,34,171,1,3
	.word	96061
	.byte	10
	.byte	'_Ifx_FLASH_FCON_Bits',0,34,174,1,16,4,11
	.byte	'WSPFLASH',0,1
	.word	643
	.byte	4,4,2,35,0,11
	.byte	'WSECPF',0,1
	.word	643
	.byte	2,2,2,35,0,11
	.byte	'WSDFLASH',0,2
	.word	660
	.byte	6,4,2,35,0,11
	.byte	'WSECDF',0,1
	.word	643
	.byte	3,1,2,35,1,11
	.byte	'IDLE',0,1
	.word	643
	.byte	1,0,2,35,1,11
	.byte	'ESLDIS',0,1
	.word	643
	.byte	1,7,2,35,2,11
	.byte	'SLEEP',0,1
	.word	643
	.byte	1,6,2,35,2,11
	.byte	'NSAFECC',0,1
	.word	643
	.byte	1,5,2,35,2,11
	.byte	'STALL',0,1
	.word	643
	.byte	1,4,2,35,2,11
	.byte	'RES21',0,1
	.word	643
	.byte	2,2,2,35,2,11
	.byte	'RES23',0,1
	.word	643
	.byte	2,0,2,35,2,11
	.byte	'VOPERM',0,1
	.word	643
	.byte	1,7,2,35,3,11
	.byte	'SQERM',0,1
	.word	643
	.byte	1,6,2,35,3,11
	.byte	'PROERM',0,1
	.word	643
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	643
	.byte	3,2,2,35,3,11
	.byte	'PR5V',0,1
	.word	643
	.byte	1,1,2,35,3,11
	.byte	'EOBM',0,1
	.word	643
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_FLASH_FCON_Bits',0,34,193,1,3
	.word	96200
	.byte	10
	.byte	'_Ifx_FLASH_FPRO_Bits',0,34,196,1,16,4,11
	.byte	'PROINP',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'PRODISP',0,1
	.word	643
	.byte	1,6,2,35,0,11
	.byte	'PROIND',0,1
	.word	643
	.byte	1,5,2,35,0,11
	.byte	'PRODISD',0,1
	.word	643
	.byte	1,4,2,35,0,11
	.byte	'PROINHSMCOTP',0,1
	.word	643
	.byte	1,3,2,35,0,11
	.byte	'RES5',0,1
	.word	643
	.byte	1,2,2,35,0,11
	.byte	'PROINOTP',0,1
	.word	643
	.byte	1,1,2,35,0,11
	.byte	'RES7',0,1
	.word	643
	.byte	1,0,2,35,0,11
	.byte	'PROINDBG',0,1
	.word	643
	.byte	1,7,2,35,1,11
	.byte	'PRODISDBG',0,1
	.word	643
	.byte	1,6,2,35,1,11
	.byte	'PROINHSM',0,1
	.word	643
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	643
	.byte	5,0,2,35,1,11
	.byte	'DCFP',0,1
	.word	643
	.byte	1,7,2,35,2,11
	.byte	'DDFP',0,1
	.word	643
	.byte	1,6,2,35,2,11
	.byte	'DDFPX',0,1
	.word	643
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	643
	.byte	1,4,2,35,2,11
	.byte	'DDFD',0,1
	.word	643
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	643
	.byte	1,2,2,35,2,11
	.byte	'ENPE',0,1
	.word	643
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	643
	.byte	8,0,2,35,3,0,29
	.byte	'Ifx_FLASH_FPRO_Bits',0,34,218,1,3
	.word	96562
	.byte	10
	.byte	'_Ifx_FLASH_FSR_Bits',0,34,221,1,16,4,11
	.byte	'FABUSY',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'D0BUSY',0,1
	.word	643
	.byte	1,6,2,35,0,11
	.byte	'RES1',0,1
	.word	643
	.byte	1,5,2,35,0,11
	.byte	'P0BUSY',0,1
	.word	643
	.byte	1,4,2,35,0,11
	.byte	'P1BUSY',0,1
	.word	643
	.byte	1,3,2,35,0,11
	.byte	'RES5',0,1
	.word	643
	.byte	1,2,2,35,0,11
	.byte	'RES6',0,1
	.word	643
	.byte	1,1,2,35,0,11
	.byte	'PROG',0,1
	.word	643
	.byte	1,0,2,35,0,11
	.byte	'ERASE',0,1
	.word	643
	.byte	1,7,2,35,1,11
	.byte	'PFPAGE',0,1
	.word	643
	.byte	1,6,2,35,1,11
	.byte	'DFPAGE',0,1
	.word	643
	.byte	1,5,2,35,1,11
	.byte	'OPER',0,1
	.word	643
	.byte	1,4,2,35,1,11
	.byte	'SQER',0,1
	.word	643
	.byte	1,3,2,35,1,11
	.byte	'PROER',0,1
	.word	643
	.byte	1,2,2,35,1,11
	.byte	'PFSBER',0,1
	.word	643
	.byte	1,1,2,35,1,11
	.byte	'PFDBER',0,1
	.word	643
	.byte	1,0,2,35,1,11
	.byte	'PFMBER',0,1
	.word	643
	.byte	1,7,2,35,2,11
	.byte	'RES17',0,1
	.word	643
	.byte	1,6,2,35,2,11
	.byte	'DFSBER',0,1
	.word	643
	.byte	1,5,2,35,2,11
	.byte	'DFDBER',0,1
	.word	643
	.byte	1,4,2,35,2,11
	.byte	'DFTBER',0,1
	.word	643
	.byte	1,3,2,35,2,11
	.byte	'DFMBER',0,1
	.word	643
	.byte	1,2,2,35,2,11
	.byte	'SRIADDERR',0,1
	.word	643
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	660
	.byte	2,7,2,35,2,11
	.byte	'PVER',0,1
	.word	643
	.byte	1,6,2,35,3,11
	.byte	'EVER',0,1
	.word	643
	.byte	1,5,2,35,3,11
	.byte	'SPND',0,1
	.word	643
	.byte	1,4,2,35,3,11
	.byte	'SLM',0,1
	.word	643
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	643
	.byte	1,2,2,35,3,11
	.byte	'ORIER',0,1
	.word	643
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	643
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_FLASH_FSR_Bits',0,34,254,1,3
	.word	97003
	.byte	10
	.byte	'_Ifx_FLASH_ID_Bits',0,34,129,2,16,4,11
	.byte	'MODREV',0,1
	.word	643
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	643
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	660
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_FLASH_ID_Bits',0,34,134,2,3
	.word	97609
	.byte	10
	.byte	'_Ifx_FLASH_MARD_Bits',0,34,137,2,16,4,11
	.byte	'HMARGIN',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'SELD0',0,1
	.word	643
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	643
	.byte	1,5,2,35,0,11
	.byte	'SPND',0,1
	.word	643
	.byte	1,4,2,35,0,11
	.byte	'SPNDERR',0,1
	.word	643
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,2
	.word	660
	.byte	10,1,2,35,0,11
	.byte	'TRAPDIS',0,1
	.word	643
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	660
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_FLASH_MARD_Bits',0,34,147,2,3
	.word	97720
	.byte	10
	.byte	'_Ifx_FLASH_MARP_Bits',0,34,150,2,16,4,11
	.byte	'SELP0',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'SELP1',0,1
	.word	643
	.byte	1,6,2,35,0,11
	.byte	'RES2',0,1
	.word	643
	.byte	1,5,2,35,0,11
	.byte	'RES3',0,1
	.word	643
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	660
	.byte	11,1,2,35,0,11
	.byte	'TRAPDIS',0,1
	.word	643
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	660
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_FLASH_MARP_Bits',0,34,159,2,3
	.word	97934
	.byte	10
	.byte	'_Ifx_FLASH_PROCOND_Bits',0,34,162,2,16,4,11
	.byte	'L',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'NSAFECC',0,1
	.word	643
	.byte	1,6,2,35,0,11
	.byte	'RAMIN',0,1
	.word	643
	.byte	2,4,2,35,0,11
	.byte	'RAMINSEL',0,1
	.word	643
	.byte	4,0,2,35,0,11
	.byte	'OSCCFG',0,1
	.word	643
	.byte	1,7,2,35,1,11
	.byte	'MODE',0,1
	.word	643
	.byte	2,5,2,35,1,11
	.byte	'APREN',0,1
	.word	643
	.byte	1,4,2,35,1,11
	.byte	'CAP0EN',0,1
	.word	643
	.byte	1,3,2,35,1,11
	.byte	'CAP1EN',0,1
	.word	643
	.byte	1,2,2,35,1,11
	.byte	'CAP2EN',0,1
	.word	643
	.byte	1,1,2,35,1,11
	.byte	'CAP3EN',0,1
	.word	643
	.byte	1,0,2,35,1,11
	.byte	'ESR0CNT',0,2
	.word	660
	.byte	12,4,2,35,2,11
	.byte	'RES29',0,1
	.word	643
	.byte	2,2,2,35,3,11
	.byte	'RES30',0,1
	.word	643
	.byte	1,1,2,35,3,11
	.byte	'RPRO',0,1
	.word	643
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_FLASH_PROCOND_Bits',0,34,179,2,3
	.word	98121
	.byte	10
	.byte	'_Ifx_FLASH_PROCONDBG_Bits',0,34,182,2,16,4,11
	.byte	'OCDSDIS',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'DBGIFLCK',0,1
	.word	643
	.byte	1,6,2,35,0,11
	.byte	'EDM',0,1
	.word	643
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	468
	.byte	28,0,2,35,0,0,29
	.byte	'Ifx_FLASH_PROCONDBG_Bits',0,34,188,2,3
	.word	98445
	.byte	10
	.byte	'_Ifx_FLASH_PROCONHSM_Bits',0,34,191,2,16,4,11
	.byte	'HSMDBGDIS',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'DBGIFLCK',0,1
	.word	643
	.byte	1,6,2,35,0,11
	.byte	'TSTIFLCK',0,1
	.word	643
	.byte	1,5,2,35,0,11
	.byte	'HSMTSTDIS',0,1
	.word	643
	.byte	1,4,2,35,0,11
	.byte	'RES15',0,2
	.word	660
	.byte	12,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	660
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_FLASH_PROCONHSM_Bits',0,34,199,2,3
	.word	98588
	.byte	10
	.byte	'_Ifx_FLASH_PROCONHSMCOTP_Bits',0,34,202,2,16,4,11
	.byte	'HSMBOOTEN',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'SSWWAIT',0,1
	.word	643
	.byte	1,6,2,35,0,11
	.byte	'HSMDX',0,1
	.word	643
	.byte	1,5,2,35,0,11
	.byte	'HSM6X',0,1
	.word	643
	.byte	1,4,2,35,0,11
	.byte	'HSM16X',0,1
	.word	643
	.byte	1,3,2,35,0,11
	.byte	'HSM17X',0,1
	.word	643
	.byte	1,2,2,35,0,11
	.byte	'S6ROM',0,1
	.word	643
	.byte	1,1,2,35,0,11
	.byte	'HSMENPINS',0,2
	.word	660
	.byte	2,7,2,35,0,11
	.byte	'HSMENRES',0,1
	.word	643
	.byte	2,5,2,35,1,11
	.byte	'DESTDBG',0,1
	.word	643
	.byte	2,3,2,35,1,11
	.byte	'BLKFLAN',0,1
	.word	643
	.byte	1,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	643
	.byte	2,0,2,35,1,11
	.byte	'S16ROM',0,1
	.word	643
	.byte	1,7,2,35,2,11
	.byte	'S17ROM',0,1
	.word	643
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	660
	.byte	14,0,2,35,2,0,29
	.byte	'Ifx_FLASH_PROCONHSMCOTP_Bits',0,34,219,2,3
	.word	98777
	.byte	10
	.byte	'_Ifx_FLASH_PROCONOTP_Bits',0,34,222,2,16,4,11
	.byte	'S0ROM',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'S1ROM',0,1
	.word	643
	.byte	1,6,2,35,0,11
	.byte	'S2ROM',0,1
	.word	643
	.byte	1,5,2,35,0,11
	.byte	'S3ROM',0,1
	.word	643
	.byte	1,4,2,35,0,11
	.byte	'S4ROM',0,1
	.word	643
	.byte	1,3,2,35,0,11
	.byte	'S5ROM',0,1
	.word	643
	.byte	1,2,2,35,0,11
	.byte	'S6ROM',0,1
	.word	643
	.byte	1,1,2,35,0,11
	.byte	'S7ROM',0,1
	.word	643
	.byte	1,0,2,35,0,11
	.byte	'S8ROM',0,1
	.word	643
	.byte	1,7,2,35,1,11
	.byte	'S9ROM',0,1
	.word	643
	.byte	1,6,2,35,1,11
	.byte	'S10ROM',0,1
	.word	643
	.byte	1,5,2,35,1,11
	.byte	'S11ROM',0,1
	.word	643
	.byte	1,4,2,35,1,11
	.byte	'S12ROM',0,1
	.word	643
	.byte	1,3,2,35,1,11
	.byte	'S13ROM',0,1
	.word	643
	.byte	1,2,2,35,1,11
	.byte	'S14ROM',0,1
	.word	643
	.byte	1,1,2,35,1,11
	.byte	'S15ROM',0,1
	.word	643
	.byte	1,0,2,35,1,11
	.byte	'S16ROM',0,1
	.word	643
	.byte	1,7,2,35,2,11
	.byte	'S17ROM',0,1
	.word	643
	.byte	1,6,2,35,2,11
	.byte	'S18ROM',0,1
	.word	643
	.byte	1,5,2,35,2,11
	.byte	'S19ROM',0,1
	.word	643
	.byte	1,4,2,35,2,11
	.byte	'S20ROM',0,1
	.word	643
	.byte	1,3,2,35,2,11
	.byte	'S21ROM',0,1
	.word	643
	.byte	1,2,2,35,2,11
	.byte	'S22ROM',0,1
	.word	643
	.byte	1,1,2,35,2,11
	.byte	'S23ROM',0,1
	.word	643
	.byte	1,0,2,35,2,11
	.byte	'S24ROM',0,1
	.word	643
	.byte	1,7,2,35,3,11
	.byte	'S25ROM',0,1
	.word	643
	.byte	1,6,2,35,3,11
	.byte	'S26ROM',0,1
	.word	643
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	643
	.byte	2,3,2,35,3,11
	.byte	'BML',0,1
	.word	643
	.byte	2,1,2,35,3,11
	.byte	'TP',0,1
	.word	643
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_FLASH_PROCONOTP_Bits',0,34,254,2,3
	.word	99140
	.byte	10
	.byte	'_Ifx_FLASH_PROCONP_Bits',0,34,129,3,16,4,11
	.byte	'S0L',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'S1L',0,1
	.word	643
	.byte	1,6,2,35,0,11
	.byte	'S2L',0,1
	.word	643
	.byte	1,5,2,35,0,11
	.byte	'S3L',0,1
	.word	643
	.byte	1,4,2,35,0,11
	.byte	'S4L',0,1
	.word	643
	.byte	1,3,2,35,0,11
	.byte	'S5L',0,1
	.word	643
	.byte	1,2,2,35,0,11
	.byte	'S6L',0,1
	.word	643
	.byte	1,1,2,35,0,11
	.byte	'S7L',0,1
	.word	643
	.byte	1,0,2,35,0,11
	.byte	'S8L',0,1
	.word	643
	.byte	1,7,2,35,1,11
	.byte	'S9L',0,1
	.word	643
	.byte	1,6,2,35,1,11
	.byte	'S10L',0,1
	.word	643
	.byte	1,5,2,35,1,11
	.byte	'S11L',0,1
	.word	643
	.byte	1,4,2,35,1,11
	.byte	'S12L',0,1
	.word	643
	.byte	1,3,2,35,1,11
	.byte	'S13L',0,1
	.word	643
	.byte	1,2,2,35,1,11
	.byte	'S14L',0,1
	.word	643
	.byte	1,1,2,35,1,11
	.byte	'S15L',0,1
	.word	643
	.byte	1,0,2,35,1,11
	.byte	'S16L',0,1
	.word	643
	.byte	1,7,2,35,2,11
	.byte	'S17L',0,1
	.word	643
	.byte	1,6,2,35,2,11
	.byte	'S18L',0,1
	.word	643
	.byte	1,5,2,35,2,11
	.byte	'S19L',0,1
	.word	643
	.byte	1,4,2,35,2,11
	.byte	'S20L',0,1
	.word	643
	.byte	1,3,2,35,2,11
	.byte	'S21L',0,1
	.word	643
	.byte	1,2,2,35,2,11
	.byte	'S22L',0,1
	.word	643
	.byte	1,1,2,35,2,11
	.byte	'S23L',0,1
	.word	643
	.byte	1,0,2,35,2,11
	.byte	'S24L',0,1
	.word	643
	.byte	1,7,2,35,3,11
	.byte	'S25L',0,1
	.word	643
	.byte	1,6,2,35,3,11
	.byte	'S26L',0,1
	.word	643
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	643
	.byte	4,1,2,35,3,11
	.byte	'RPRO',0,1
	.word	643
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_FLASH_PROCONP_Bits',0,34,160,3,3
	.word	99735
	.byte	10
	.byte	'_Ifx_FLASH_PROCONWOP_Bits',0,34,163,3,16,4,11
	.byte	'S0WOP',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'S1WOP',0,1
	.word	643
	.byte	1,6,2,35,0,11
	.byte	'S2WOP',0,1
	.word	643
	.byte	1,5,2,35,0,11
	.byte	'S3WOP',0,1
	.word	643
	.byte	1,4,2,35,0,11
	.byte	'S4WOP',0,1
	.word	643
	.byte	1,3,2,35,0,11
	.byte	'S5WOP',0,1
	.word	643
	.byte	1,2,2,35,0,11
	.byte	'S6WOP',0,1
	.word	643
	.byte	1,1,2,35,0,11
	.byte	'S7WOP',0,1
	.word	643
	.byte	1,0,2,35,0,11
	.byte	'S8WOP',0,1
	.word	643
	.byte	1,7,2,35,1,11
	.byte	'S9WOP',0,1
	.word	643
	.byte	1,6,2,35,1,11
	.byte	'S10WOP',0,1
	.word	643
	.byte	1,5,2,35,1,11
	.byte	'S11WOP',0,1
	.word	643
	.byte	1,4,2,35,1,11
	.byte	'S12WOP',0,1
	.word	643
	.byte	1,3,2,35,1,11
	.byte	'S13WOP',0,1
	.word	643
	.byte	1,2,2,35,1,11
	.byte	'S14WOP',0,1
	.word	643
	.byte	1,1,2,35,1,11
	.byte	'S15WOP',0,1
	.word	643
	.byte	1,0,2,35,1,11
	.byte	'S16WOP',0,1
	.word	643
	.byte	1,7,2,35,2,11
	.byte	'S17WOP',0,1
	.word	643
	.byte	1,6,2,35,2,11
	.byte	'S18WOP',0,1
	.word	643
	.byte	1,5,2,35,2,11
	.byte	'S19WOP',0,1
	.word	643
	.byte	1,4,2,35,2,11
	.byte	'S20WOP',0,1
	.word	643
	.byte	1,3,2,35,2,11
	.byte	'S21WOP',0,1
	.word	643
	.byte	1,2,2,35,2,11
	.byte	'S22WOP',0,1
	.word	643
	.byte	1,1,2,35,2,11
	.byte	'S23WOP',0,1
	.word	643
	.byte	1,0,2,35,2,11
	.byte	'S24WOP',0,1
	.word	643
	.byte	1,7,2,35,3,11
	.byte	'S25WOP',0,1
	.word	643
	.byte	1,6,2,35,3,11
	.byte	'S26WOP',0,1
	.word	643
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	643
	.byte	4,1,2,35,3,11
	.byte	'DATM',0,1
	.word	643
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_FLASH_PROCONWOP_Bits',0,34,194,3,3
	.word	100259
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG0_Bits',0,34,197,3,16,4,11
	.byte	'TAG',0,1
	.word	643
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	468
	.byte	26,0,2,35,0,0,29
	.byte	'Ifx_FLASH_RDB_CFG0_Bits',0,34,201,3,3
	.word	100841
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG1_Bits',0,34,204,3,16,4,11
	.byte	'TAG',0,1
	.word	643
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	468
	.byte	26,0,2,35,0,0,29
	.byte	'Ifx_FLASH_RDB_CFG1_Bits',0,34,208,3,3
	.word	100943
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG2_Bits',0,34,211,3,16,4,11
	.byte	'TAG',0,1
	.word	643
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	468
	.byte	26,0,2,35,0,0,29
	.byte	'Ifx_FLASH_RDB_CFG2_Bits',0,34,215,3,3
	.word	101045
	.byte	10
	.byte	'_Ifx_FLASH_RRAD_Bits',0,34,218,3,16,4,11
	.byte	'reserved_0',0,1
	.word	643
	.byte	3,5,2,35,0,11
	.byte	'ADD',0,4
	.word	468
	.byte	29,0,2,35,0,0,29
	.byte	'Ifx_FLASH_RRAD_Bits',0,34,222,3,3
	.word	101147
	.byte	10
	.byte	'_Ifx_FLASH_RRCT_Bits',0,34,225,3,16,4,11
	.byte	'STRT',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'STP',0,1
	.word	643
	.byte	1,6,2,35,0,11
	.byte	'BUSY',0,1
	.word	643
	.byte	1,5,2,35,0,11
	.byte	'DONE',0,1
	.word	643
	.byte	1,4,2,35,0,11
	.byte	'ERR',0,1
	.word	643
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	643
	.byte	3,0,2,35,0,11
	.byte	'EOBM',0,1
	.word	643
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	643
	.byte	7,0,2,35,1,11
	.byte	'CNT',0,2
	.word	660
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_FLASH_RRCT_Bits',0,34,236,3,3
	.word	101241
	.byte	10
	.byte	'_Ifx_FLASH_RRD0_Bits',0,34,239,3,16,4,11
	.byte	'DATA',0,4
	.word	468
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_FLASH_RRD0_Bits',0,34,242,3,3
	.word	101451
	.byte	10
	.byte	'_Ifx_FLASH_RRD1_Bits',0,34,245,3,16,4,11
	.byte	'DATA',0,4
	.word	468
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_FLASH_RRD1_Bits',0,34,248,3,3
	.word	101524
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_CFG_Bits',0,34,251,3,16,4,11
	.byte	'SEL',0,1
	.word	643
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	643
	.byte	2,0,2,35,0,11
	.byte	'CLR',0,1
	.word	643
	.byte	1,7,2,35,1,11
	.byte	'DIS',0,1
	.word	643
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	468
	.byte	22,0,2,35,0,0,29
	.byte	'Ifx_FLASH_UBAB_CFG_Bits',0,34,130,4,3
	.word	101597
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_STAT_Bits',0,34,133,4,16,4,11
	.byte	'VLD0',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	468
	.byte	31,0,2,35,0,0,29
	.byte	'Ifx_FLASH_UBAB_STAT_Bits',0,34,137,4,3
	.word	101752
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_TOP_Bits',0,34,140,4,16,4,11
	.byte	'reserved_0',0,1
	.word	643
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	468
	.byte	19,8,2,35,0,11
	.byte	'ERR',0,1
	.word	643
	.byte	6,2,2,35,3,11
	.byte	'VLD',0,1
	.word	643
	.byte	1,1,2,35,3,11
	.byte	'CLR',0,1
	.word	643
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_FLASH_UBAB_TOP_Bits',0,34,147,4,3
	.word	101857
	.byte	12,34,155,4,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	94262
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_ACCEN0',0,34,160,4,3
	.word	102005
	.byte	12,34,163,4,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	94823
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_ACCEN1',0,34,168,4,3
	.word	102071
	.byte	12,34,171,4,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	94904
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_CBAB_CFG',0,34,176,4,3
	.word	102137
	.byte	12,34,179,4,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	95057
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_CBAB_STAT',0,34,184,4,3
	.word	102205
	.byte	12,34,187,4,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	95305
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_CBAB_TOP',0,34,192,4,3
	.word	102274
	.byte	12,34,195,4,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	95451
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_COMM0',0,34,200,4,3
	.word	102342
	.byte	12,34,203,4,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	95549
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_COMM1',0,34,208,4,3
	.word	102407
	.byte	12,34,211,4,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	95665
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_COMM2',0,34,216,4,3
	.word	102472
	.byte	12,34,219,4,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	95781
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_ECCRD',0,34,224,4,3
	.word	102537
	.byte	12,34,227,4,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	95921
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_ECCRP',0,34,232,4,3
	.word	102602
	.byte	12,34,235,4,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	96061
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_ECCW',0,34,240,4,3
	.word	102667
	.byte	12,34,243,4,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	96200
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_FCON',0,34,248,4,3
	.word	102731
	.byte	12,34,251,4,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	96562
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_FPRO',0,34,128,5,3
	.word	102795
	.byte	12,34,131,5,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	97003
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_FSR',0,34,136,5,3
	.word	102859
	.byte	12,34,139,5,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	97609
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_ID',0,34,144,5,3
	.word	102922
	.byte	12,34,147,5,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	97720
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_MARD',0,34,152,5,3
	.word	102984
	.byte	12,34,155,5,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	97934
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_MARP',0,34,160,5,3
	.word	103048
	.byte	12,34,163,5,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	98121
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_PROCOND',0,34,168,5,3
	.word	103112
	.byte	12,34,171,5,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	98445
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_PROCONDBG',0,34,176,5,3
	.word	103179
	.byte	12,34,179,5,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	98588
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_PROCONHSM',0,34,184,5,3
	.word	103248
	.byte	12,34,187,5,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	98777
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_PROCONHSMCOTP',0,34,192,5,3
	.word	103317
	.byte	12,34,195,5,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	99140
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_PROCONOTP',0,34,200,5,3
	.word	103390
	.byte	12,34,203,5,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	99735
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_PROCONP',0,34,208,5,3
	.word	103459
	.byte	12,34,211,5,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	100259
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_PROCONWOP',0,34,216,5,3
	.word	103526
	.byte	12,34,219,5,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	100841
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_RDB_CFG0',0,34,224,5,3
	.word	103595
	.byte	12,34,227,5,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	100943
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_RDB_CFG1',0,34,232,5,3
	.word	103663
	.byte	12,34,235,5,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	101045
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_RDB_CFG2',0,34,240,5,3
	.word	103731
	.byte	12,34,243,5,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	101147
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_RRAD',0,34,248,5,3
	.word	103799
	.byte	12,34,251,5,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	101241
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_RRCT',0,34,128,6,3
	.word	103863
	.byte	12,34,131,6,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	101451
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_RRD0',0,34,136,6,3
	.word	103927
	.byte	12,34,139,6,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	101524
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_RRD1',0,34,144,6,3
	.word	103991
	.byte	12,34,147,6,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	101597
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_UBAB_CFG',0,34,152,6,3
	.word	104055
	.byte	12,34,155,6,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	101752
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_UBAB_STAT',0,34,160,6,3
	.word	104123
	.byte	12,34,163,6,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	101857
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_UBAB_TOP',0,34,168,6,3
	.word	104192
	.byte	10
	.byte	'_Ifx_FLASH_CBAB',0,34,179,6,25,12,13
	.byte	'CFG',0
	.word	102137
	.byte	4,2,35,0,13
	.byte	'STAT',0
	.word	102205
	.byte	4,2,35,4,13
	.byte	'TOP',0
	.word	102274
	.byte	4,2,35,8,0,14
	.word	104260
	.byte	29
	.byte	'Ifx_FLASH_CBAB',0,34,184,6,3
	.word	104323
	.byte	10
	.byte	'_Ifx_FLASH_RDB',0,34,187,6,25,12,13
	.byte	'CFG0',0
	.word	103595
	.byte	4,2,35,0,13
	.byte	'CFG1',0
	.word	103663
	.byte	4,2,35,4,13
	.byte	'CFG2',0
	.word	103731
	.byte	4,2,35,8,0,14
	.word	104352
	.byte	29
	.byte	'Ifx_FLASH_RDB',0,34,192,6,3
	.word	104416
	.byte	10
	.byte	'_Ifx_FLASH_UBAB',0,34,195,6,25,12,13
	.byte	'CFG',0
	.word	104055
	.byte	4,2,35,0,13
	.byte	'STAT',0
	.word	104123
	.byte	4,2,35,4,13
	.byte	'TOP',0
	.word	104192
	.byte	4,2,35,8,0,14
	.word	104444
	.byte	29
	.byte	'Ifx_FLASH_UBAB',0,34,200,6,3
	.word	104507
	.byte	14
	.word	54186
	.byte	3
	.word	104536
	.byte	20,35,74,15,20,13
	.byte	'module',0
	.word	104541
	.byte	4,2,35,0,13
	.byte	'channelId',0
	.word	643
	.byte	1,2,35,4,13
	.byte	'pin',0
	.word	56744
	.byte	8,2,35,8,13
	.byte	'select',0
	.word	28032
	.byte	1,2,35,16,0,28
	.word	104546
	.byte	29
	.byte	'IfxScu_Req_In',0,35,80,3
	.word	104616
	.byte	29
	.byte	'IfxScuCcu_PllStepsFunctionHook',0,11,148,1,16
	.word	207
	.byte	20,11,212,5,9,8,13
	.byte	'value',0
	.word	10866
	.byte	4,2,35,0,13
	.byte	'mask',0
	.word	10866
	.byte	4,2,35,4,0,29
	.byte	'IfxScuCcu_CcuconRegConfig',0,11,216,5,3
	.word	104683
	.byte	20,11,221,5,9,8,13
	.byte	'pDivider',0
	.word	643
	.byte	1,2,35,0,13
	.byte	'nDivider',0
	.word	643
	.byte	1,2,35,1,13
	.byte	'k2Initial',0
	.word	643
	.byte	1,2,35,2,13
	.byte	'waitTime',0
	.word	264
	.byte	4,2,35,4,0,29
	.byte	'IfxScuCcu_InitialStepConfig',0,11,227,5,3
	.word	104754
	.byte	20,11,231,5,9,12,13
	.byte	'k2Step',0
	.word	643
	.byte	1,2,35,0,13
	.byte	'waitTime',0
	.word	264
	.byte	4,2,35,2,13
	.byte	'hookFunction',0
	.word	104643
	.byte	4,2,35,8,0,29
	.byte	'IfxScuCcu_PllStepsConfig',0,11,236,5,3
	.word	104871
	.byte	3
	.word	204
	.byte	20,11,244,5,9,48,13
	.byte	'ccucon0',0
	.word	104683
	.byte	8,2,35,0,13
	.byte	'ccucon1',0
	.word	104683
	.byte	8,2,35,8,13
	.byte	'ccucon2',0
	.word	104683
	.byte	8,2,35,16,13
	.byte	'ccucon5',0
	.word	104683
	.byte	8,2,35,24,13
	.byte	'ccucon6',0
	.word	104683
	.byte	8,2,35,32,13
	.byte	'ccucon7',0
	.word	104683
	.byte	8,2,35,40,0,29
	.byte	'IfxScuCcu_ClockDistributionConfig',0,11,252,5,3
	.word	104973
	.byte	20,11,128,6,9,8,13
	.byte	'value',0
	.word	10866
	.byte	4,2,35,0,13
	.byte	'mask',0
	.word	10866
	.byte	4,2,35,4,0,29
	.byte	'IfxScuCcu_FlashWaitstateConfig',0,11,132,6,3
	.word	105125
	.byte	3
	.word	104871
	.byte	20,11,137,6,9,16,13
	.byte	'numOfPllDividerSteps',0
	.word	643
	.byte	1,2,35,0,13
	.byte	'pllDividerStep',0
	.word	105201
	.byte	4,2,35,4,13
	.byte	'pllInitialStep',0
	.word	104754
	.byte	8,2,35,8,0,29
	.byte	'IfxScuCcu_SysPllConfig',0,11,142,6,3
	.word	105206
	.byte	17,36,104,9,1,18
	.byte	'IfxDma_ChannelIncrementCircular_none',0,0,18
	.byte	'IfxDma_ChannelIncrementCircular_2',0,1,18
	.byte	'IfxDma_ChannelIncrementCircular_4',0,2,18
	.byte	'IfxDma_ChannelIncrementCircular_8',0,3,18
	.byte	'IfxDma_ChannelIncrementCircular_16',0,4,18
	.byte	'IfxDma_ChannelIncrementCircular_32',0,5,18
	.byte	'IfxDma_ChannelIncrementCircular_64',0,6,18
	.byte	'IfxDma_ChannelIncrementCircular_128',0,7,18
	.byte	'IfxDma_ChannelIncrementCircular_256',0,8,18
	.byte	'IfxDma_ChannelIncrementCircular_512',0,9,18
	.byte	'IfxDma_ChannelIncrementCircular_1024',0,10,18
	.byte	'IfxDma_ChannelIncrementCircular_2048',0,11,18
	.byte	'IfxDma_ChannelIncrementCircular_4096',0,12,18
	.byte	'IfxDma_ChannelIncrementCircular_8192',0,13,18
	.byte	'IfxDma_ChannelIncrementCircular_16384',0,14,18
	.byte	'IfxDma_ChannelIncrementCircular_32768',0,15,0,29
	.byte	'IfxDma_ChannelIncrementCircular',0,36,122,3
	.word	105323
	.byte	17,36,127,9,1,18
	.byte	'IfxDma_ChannelIncrementDirection_negative',0,0,18
	.byte	'IfxDma_ChannelIncrementDirection_positive',0,1,0,29
	.byte	'IfxDma_ChannelIncrementDirection',0,36,131,1,3
	.word	105977
	.byte	17,36,136,1,9,1,18
	.byte	'IfxDma_ChannelIncrementStep_1',0,0,18
	.byte	'IfxDma_ChannelIncrementStep_2',0,1,18
	.byte	'IfxDma_ChannelIncrementStep_4',0,2,18
	.byte	'IfxDma_ChannelIncrementStep_8',0,3,18
	.byte	'IfxDma_ChannelIncrementStep_16',0,4,18
	.byte	'IfxDma_ChannelIncrementStep_32',0,5,18
	.byte	'IfxDma_ChannelIncrementStep_64',0,6,18
	.byte	'IfxDma_ChannelIncrementStep_128',0,7,0,29
	.byte	'IfxDma_ChannelIncrementStep',0,36,146,1,3
	.word	106113
	.byte	17,36,160,1,9,1,18
	.byte	'IfxDma_ChannelMove_1',0,0,18
	.byte	'IfxDma_ChannelMove_2',0,1,18
	.byte	'IfxDma_ChannelMove_4',0,2,18
	.byte	'IfxDma_ChannelMove_8',0,3,18
	.byte	'IfxDma_ChannelMove_16',0,4,18
	.byte	'IfxDma_ChannelMove_3',0,5,18
	.byte	'IfxDma_ChannelMove_5',0,6,18
	.byte	'IfxDma_ChannelMove_9',0,7,0,29
	.byte	'IfxDma_ChannelMove',0,36,170,1,3
	.word	106418
	.byte	17,36,175,1,9,1,18
	.byte	'IfxDma_ChannelMoveSize_8bit',0,0,18
	.byte	'IfxDma_ChannelMoveSize_16bit',0,1,18
	.byte	'IfxDma_ChannelMoveSize_32bit',0,2,18
	.byte	'IfxDma_ChannelMoveSize_64bit',0,3,18
	.byte	'IfxDma_ChannelMoveSize_128bit',0,4,18
	.byte	'IfxDma_ChannelMoveSize_256bit',0,5,0,29
	.byte	'IfxDma_ChannelMoveSize',0,36,183,1,3
	.word	106638
	.byte	17,36,239,1,9,1,18
	.byte	'IfxDma_ChannelShadow_none',0,0,18
	.byte	'IfxDma_ChannelShadow_src',0,1,18
	.byte	'IfxDma_ChannelShadow_dst',0,2,18
	.byte	'IfxDma_ChannelShadow_srcDirectWrite',0,5,18
	.byte	'IfxDma_ChannelShadow_dstDirectWrite',0,6,18
	.byte	'IfxDma_ChannelShadow_doubleSourceBufferingSwSwitch',0,8,18
	.byte	'IfxDma_ChannelShadow_doubleSourceBufferingHwSwSwitch',0,9,18
	.byte	'IfxDma_ChannelShadow_doubleDestinationBufferingSwSwitch',0,10,18
	.byte	'IfxDma_ChannelShadow_doubleDestinationBufferingHwSwSwitch',0,11,18
	.byte	'IfxDma_ChannelShadow_linkedList',0,12,18
	.byte	'IfxDma_ChannelShadow_accumulatedLinkedList',0,13,18
	.byte	'IfxDma_ChannelShadow_safeLinkedList',0,14,18
	.byte	'IfxDma_ChannelShadow_conditionalLinkedList',0,15,0,29
	.byte	'IfxDma_ChannelShadow',0,36,254,1,3
	.word	106864
	.byte	17,36,128,2,9,1,18
	.byte	'IfxDma_HardwareResourcePartition_0',0,0,18
	.byte	'IfxDma_HardwareResourcePartition_1',0,1,18
	.byte	'IfxDma_HardwareResourcePartition_2',0,2,18
	.byte	'IfxDma_HardwareResourcePartition_3',0,3,0,29
	.byte	'IfxDma_HardwareResourcePartition',0,36,134,2,3
	.word	107447
	.byte	17,36,138,2,9,1,18
	.byte	'IfxDma_MoveEngine_0',0,0,18
	.byte	'IfxDma_MoveEngine_1',0,1,0,29
	.byte	'IfxDma_MoveEngine',0,36,142,2,3
	.word	107644
	.byte	17,36,147,2,9,1,18
	.byte	'IfxDma_SleepMode_enable',0,0,18
	.byte	'IfxDma_SleepMode_disable',0,1,0,29
	.byte	'IfxDma_SleepMode',0,36,151,2,3
	.word	107722
	.byte	29
	.byte	'exti_pin_enum',0,20,61,2
	.word	24889
	.byte	29
	.byte	'exti_trigger_enum',0,20,70,2
	.word	25218
	.byte	17,37,44,9,1,18
	.byte	'NO_CAMERE',0,0,18
	.byte	'CAMERA_BIN_IIC',0,1,18
	.byte	'CAMERA_BIN_UART',0,2,18
	.byte	'CAMERA_GRAYSCALE',0,3,18
	.byte	'CAMERA_COLOR',0,4,0,29
	.byte	'camera_type_enum',0,37,51,2
	.word	107856
	.byte	31
	.byte	'camera_type',0,37,78,25
	.word	107856
	.byte	1,1,29
	.byte	'fifo_state_enum',0,23,53,2
	.word	26555
	.byte	29
	.byte	'fifo_data_type_enum',0,23,83,2
	.word	26711
	.byte	29
	.byte	'fifo_struct',0,23,94,2
	.word	26770
	.byte	29
	.byte	'Ifx_ASCLIN_ACCEN0_Bits',0,13,79,3
	.word	16650
	.byte	29
	.byte	'Ifx_ASCLIN_ACCEN1_Bits',0,13,85,3
	.word	16558
	.byte	29
	.byte	'Ifx_ASCLIN_BITCON_Bits',0,13,97,3
	.word	12182
	.byte	29
	.byte	'Ifx_ASCLIN_BRD_Bits',0,13,106,3
	.word	13022
	.byte	29
	.byte	'Ifx_ASCLIN_BRG_Bits',0,13,115,3
	.word	12865
	.byte	29
	.byte	'Ifx_ASCLIN_CLC_Bits',0,13,125,3
	.word	11137
	.byte	29
	.byte	'Ifx_ASCLIN_CSR_Bits',0,13,133,1,3
	.word	15831
	.byte	29
	.byte	'Ifx_ASCLIN_DATCON_Bits',0,13,145,1,3
	.word	12668
	.byte	29
	.byte	'Ifx_ASCLIN_FLAGS_Bits',0,13,177,1,3
	.word	13678
	.byte	29
	.byte	'Ifx_ASCLIN_FLAGSCLEAR_Bits',0,13,209,1,3
	.word	14677
	.byte	29
	.byte	'Ifx_ASCLIN_FLAGSENABLE_Bits',0,13,238,1,3
	.word	15192
	.byte	29
	.byte	'Ifx_ASCLIN_FLAGSSET_Bits',0,13,142,2,3
	.word	14164
	.byte	29
	.byte	'Ifx_ASCLIN_FRAMECON_Bits',0,13,158,2,3
	.word	12403
	.byte	29
	.byte	'Ifx_ASCLIN_ID_Bits',0,13,166,2,3
	.word	11591
	.byte	29
	.byte	'Ifx_ASCLIN_IOCR_Bits',0,13,184,2,3
	.word	11296
	.byte	29
	.byte	'Ifx_ASCLIN_KRST0_Bits',0,13,192,2,3
	.word	16432
	.byte	29
	.byte	'Ifx_ASCLIN_KRST1_Bits',0,13,199,2,3
	.word	16325
	.byte	29
	.byte	'Ifx_ASCLIN_KRSTCLR_Bits',0,13,206,2,3
	.word	16216
	.byte	29
	.byte	'Ifx_ASCLIN_LIN_BTIMER_Bits',0,13,213,2,3
	.word	13376
	.byte	29
	.byte	'Ifx_ASCLIN_LIN_CON_Bits',0,13,225,2,3
	.word	13176
	.byte	29
	.byte	'Ifx_ASCLIN_LIN_HTIMER_Bits',0,13,232,2,3
	.word	13490
	.byte	29
	.byte	'Ifx_ASCLIN_OCS_Bits',0,13,242,2,3
	.word	16053
	.byte	29
	.byte	'Ifx_ASCLIN_RXDATA_Bits',0,13,248,2,3
	.word	15744
	.byte	29
	.byte	'Ifx_ASCLIN_RXDATAD_Bits',0,13,254,2,3
	.word	15954
	.byte	29
	.byte	'Ifx_ASCLIN_RXFIFOCON_Bits',0,13,140,3,3
	.word	11941
	.byte	29
	.byte	'Ifx_ASCLIN_TXDATA_Bits',0,13,146,3,3
	.word	15657
	.byte	29
	.byte	'Ifx_ASCLIN_TXFIFOCON_Bits',0,13,159,3,3
	.word	11716
	.byte	29
	.byte	'Ifx_ASCLIN_ACCEN0',0,13,172,3,3
	.word	17182
	.byte	29
	.byte	'Ifx_ASCLIN_ACCEN1',0,13,180,3,3
	.word	16610
	.byte	29
	.byte	'Ifx_ASCLIN_BITCON',0,13,188,3,3
	.word	12363
	.byte	29
	.byte	'Ifx_ASCLIN_BRD',0,13,196,3,3
	.word	13136
	.byte	29
	.byte	'Ifx_ASCLIN_BRG',0,13,204,3,3
	.word	12982
	.byte	29
	.byte	'Ifx_ASCLIN_CLC',0,13,212,3,3
	.word	11256
	.byte	29
	.byte	'Ifx_ASCLIN_CSR',0,13,220,3,3
	.word	15914
	.byte	29
	.byte	'Ifx_ASCLIN_DATCON',0,13,228,3,3
	.word	12825
	.byte	29
	.byte	'Ifx_ASCLIN_FLAGS',0,13,236,3,3
	.word	14124
	.byte	29
	.byte	'Ifx_ASCLIN_FLAGSCLEAR',0,13,244,3,3
	.word	15152
	.byte	29
	.byte	'Ifx_ASCLIN_FLAGSENABLE',0,13,252,3,3
	.word	15617
	.byte	29
	.byte	'Ifx_ASCLIN_FLAGSSET',0,13,132,4,3
	.word	14637
	.byte	29
	.byte	'Ifx_ASCLIN_FRAMECON',0,13,140,4,3
	.word	12628
	.byte	29
	.byte	'Ifx_ASCLIN_ID',0,13,148,4,3
	.word	11676
	.byte	29
	.byte	'Ifx_ASCLIN_IOCR',0,13,156,4,3
	.word	11551
	.byte	29
	.byte	'Ifx_ASCLIN_KRST0',0,13,164,4,3
	.word	16518
	.byte	29
	.byte	'Ifx_ASCLIN_KRST1',0,13,172,4,3
	.word	16392
	.byte	29
	.byte	'Ifx_ASCLIN_KRSTCLR',0,13,180,4,3
	.word	16285
	.byte	29
	.byte	'Ifx_ASCLIN_LIN_BTIMER',0,13,188,4,3
	.word	13450
	.byte	29
	.byte	'Ifx_ASCLIN_LIN_CON',0,13,196,4,3
	.word	13336
	.byte	29
	.byte	'Ifx_ASCLIN_LIN_HTIMER',0,13,204,4,3
	.word	13565
	.byte	29
	.byte	'Ifx_ASCLIN_OCS',0,13,212,4,3
	.word	16176
	.byte	29
	.byte	'Ifx_ASCLIN_RXDATA',0,13,220,4,3
	.word	15791
	.byte	29
	.byte	'Ifx_ASCLIN_RXDATAD',0,13,228,4,3
	.word	16002
	.byte	29
	.byte	'Ifx_ASCLIN_RXFIFOCON',0,13,236,4,3
	.word	12142
	.byte	29
	.byte	'Ifx_ASCLIN_TXDATA',0,13,244,4,3
	.word	15704
	.byte	29
	.byte	'Ifx_ASCLIN_TXFIFOCON',0,13,252,4,3
	.word	11901
	.byte	14
	.word	13605
	.byte	29
	.byte	'Ifx_ASCLIN_LIN',0,13,140,5,3
	.word	109661
	.byte	14
	.word	17222
	.byte	29
	.byte	'Ifx_ASCLIN',0,13,181,5,3
	.word	109690
	.byte	20,38,59,15,16,13
	.byte	'module',0
	.word	17669
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	56744
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	28032
	.byte	1,2,35,12,0,28
	.word	109715
	.byte	29
	.byte	'IfxAsclin_Cts_In',0,38,64,3
	.word	109766
	.byte	20,38,67,15,16,13
	.byte	'module',0
	.word	17669
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	56744
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	28032
	.byte	1,2,35,12,0,28
	.word	109796
	.byte	29
	.byte	'IfxAsclin_Rx_In',0,38,72,3
	.word	109847
	.byte	20,38,75,15,16,13
	.byte	'module',0
	.word	17669
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	56744
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	9668
	.byte	1,2,35,12,0,28
	.word	109876
	.byte	29
	.byte	'IfxAsclin_Rts_Out',0,38,80,3
	.word	109927
	.byte	20,38,83,15,16,13
	.byte	'module',0
	.word	17669
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	56744
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	9668
	.byte	1,2,35,12,0,28
	.word	109958
	.byte	29
	.byte	'IfxAsclin_Sclk_Out',0,38,88,3
	.word	110009
	.byte	20,38,91,15,16,13
	.byte	'module',0
	.word	17669
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	56744
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	9668
	.byte	1,2,35,12,0,28
	.word	110041
	.byte	29
	.byte	'IfxAsclin_Slso_Out',0,38,96,3
	.word	110092
	.byte	20,38,99,15,16,13
	.byte	'module',0
	.word	17669
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	56744
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	9668
	.byte	1,2,35,12,0,28
	.word	110124
	.byte	29
	.byte	'IfxAsclin_Tx_Out',0,38,104,3
	.word	110175
	.byte	17,12,82,9,1,18
	.byte	'IfxAsclin_Checksum_classic',0,0,18
	.byte	'IfxAsclin_Checksum_enhanced',0,1,0,29
	.byte	'IfxAsclin_Checksum',0,12,86,3
	.word	110205
	.byte	17,12,91,9,1,18
	.byte	'IfxAsclin_ChecksumInjection_notWritten',0,0,18
	.byte	'IfxAsclin_ChecksumInjection_written',0,1,0,29
	.byte	'IfxAsclin_ChecksumInjection',0,12,95,3
	.word	110297
	.byte	17,12,101,9,1,18
	.byte	'IfxAsclin_ClockPolarity_idleLow',0,0,18
	.byte	'IfxAsclin_ClockPolarity_idleHigh',0,1,0,29
	.byte	'IfxAsclin_ClockPolarity',0,12,105,3
	.word	110418
	.byte	17,12,110,9,1,18
	.byte	'IfxAsclin_ClockSource_noClock',0,0,18
	.byte	'IfxAsclin_ClockSource_kernelClock',0,1,18
	.byte	'IfxAsclin_ClockSource_oscillatorClock',0,2,18
	.byte	'IfxAsclin_ClockSource_flexRayClock',0,4,18
	.byte	'IfxAsclin_ClockSource_ascFastClock',0,8,18
	.byte	'IfxAsclin_ClockSource_ascSlowClock',0,16,0,29
	.byte	'IfxAsclin_ClockSource',0,12,118,3
	.word	110525
	.byte	29
	.byte	'IfxAsclin_CtsInputSelect',0,12,129,1,3
	.word	17736
	.byte	17,12,134,1,9,1,18
	.byte	'IfxAsclin_DataLength_1',0,0,18
	.byte	'IfxAsclin_DataLength_2',0,1,18
	.byte	'IfxAsclin_DataLength_3',0,2,18
	.byte	'IfxAsclin_DataLength_4',0,3,18
	.byte	'IfxAsclin_DataLength_5',0,4,18
	.byte	'IfxAsclin_DataLength_6',0,5,18
	.byte	'IfxAsclin_DataLength_7',0,6,18
	.byte	'IfxAsclin_DataLength_8',0,7,18
	.byte	'IfxAsclin_DataLength_9',0,8,18
	.byte	'IfxAsclin_DataLength_10',0,9,18
	.byte	'IfxAsclin_DataLength_11',0,10,18
	.byte	'IfxAsclin_DataLength_12',0,11,18
	.byte	'IfxAsclin_DataLength_13',0,12,18
	.byte	'IfxAsclin_DataLength_14',0,13,18
	.byte	'IfxAsclin_DataLength_15',0,14,18
	.byte	'IfxAsclin_DataLength_16',0,15,0,29
	.byte	'IfxAsclin_DataLength',0,12,152,1,3
	.word	110814
	.byte	17,12,157,1,9,1,18
	.byte	'IfxAsclin_FrameMode_initialise',0,0,18
	.byte	'IfxAsclin_FrameMode_asc',0,1,18
	.byte	'IfxAsclin_FrameMode_spi',0,2,18
	.byte	'IfxAsclin_FrameMode_lin',0,3,0,29
	.byte	'IfxAsclin_FrameMode',0,12,163,1,3
	.word	111258
	.byte	17,12,168,1,9,1,18
	.byte	'IfxAsclin_HeaderResponseSelect_headerAndResponse',0,0,18
	.byte	'IfxAsclin_HeaderResponseSelect_headerOnly',0,1,0,29
	.byte	'IfxAsclin_HeaderResponseSelect',0,12,172,1,3
	.word	111405
	.byte	17,12,179,1,9,1,18
	.byte	'IfxAsclin_IdleDelay_0',0,0,18
	.byte	'IfxAsclin_IdleDelay_1',0,1,18
	.byte	'IfxAsclin_IdleDelay_2',0,2,18
	.byte	'IfxAsclin_IdleDelay_3',0,3,18
	.byte	'IfxAsclin_IdleDelay_4',0,4,18
	.byte	'IfxAsclin_IdleDelay_5',0,5,18
	.byte	'IfxAsclin_IdleDelay_6',0,6,18
	.byte	'IfxAsclin_IdleDelay_7',0,7,0,29
	.byte	'IfxAsclin_IdleDelay',0,12,189,1,3
	.word	111547
	.byte	17,12,195,1,9,1,18
	.byte	'IfxAsclin_LeadDelay_0',0,0,18
	.byte	'IfxAsclin_LeadDelay_1',0,1,18
	.byte	'IfxAsclin_LeadDelay_2',0,2,18
	.byte	'IfxAsclin_LeadDelay_3',0,3,18
	.byte	'IfxAsclin_LeadDelay_4',0,4,18
	.byte	'IfxAsclin_LeadDelay_5',0,5,18
	.byte	'IfxAsclin_LeadDelay_6',0,6,18
	.byte	'IfxAsclin_LeadDelay_7',0,7,0,29
	.byte	'IfxAsclin_LeadDelay',0,12,205,1,3
	.word	111775
	.byte	17,12,210,1,9,1,18
	.byte	'IfxAsclin_LinMode_slave',0,0,18
	.byte	'IfxAsclin_LinMode_master',0,1,0,29
	.byte	'IfxAsclin_LinMode',0,12,214,1,3
	.word	112003
	.byte	17,12,219,1,9,1,18
	.byte	'IfxAsclin_LinResponseTimeoutMode_frameTimeout',0,0,18
	.byte	'IfxAsclin_LinResponseTimeoutMode_responseTimeout',0,1,0,29
	.byte	'IfxAsclin_LinResponseTimeoutMode',0,12,223,1,3
	.word	112090
	.byte	17,12,228,1,9,1,18
	.byte	'IfxAsclin_OversamplingFactor_4',0,3,18
	.byte	'IfxAsclin_OversamplingFactor_5',0,4,18
	.byte	'IfxAsclin_OversamplingFactor_6',0,5,18
	.byte	'IfxAsclin_OversamplingFactor_7',0,6,18
	.byte	'IfxAsclin_OversamplingFactor_8',0,7,18
	.byte	'IfxAsclin_OversamplingFactor_9',0,8,18
	.byte	'IfxAsclin_OversamplingFactor_10',0,9,18
	.byte	'IfxAsclin_OversamplingFactor_11',0,10,18
	.byte	'IfxAsclin_OversamplingFactor_12',0,11,18
	.byte	'IfxAsclin_OversamplingFactor_13',0,12,18
	.byte	'IfxAsclin_OversamplingFactor_14',0,13,18
	.byte	'IfxAsclin_OversamplingFactor_15',0,14,18
	.byte	'IfxAsclin_OversamplingFactor_16',0,15,0,29
	.byte	'IfxAsclin_OversamplingFactor',0,12,243,1,3
	.word	112238
	.byte	17,12,248,1,9,1,18
	.byte	'IfxAsclin_ParityType_even',0,0,18
	.byte	'IfxAsclin_ParityType_odd',0,1,0,29
	.byte	'IfxAsclin_ParityType',0,12,252,1,3
	.word	112719
	.byte	17,12,129,2,9,1,18
	.byte	'IfxAsclin_ReceiveBufferMode_rxFifo',0,0,18
	.byte	'IfxAsclin_ReceiveBufferMode_rxBuffer',0,1,0,29
	.byte	'IfxAsclin_ReceiveBufferMode',0,12,133,2,3
	.word	112811
	.byte	17,12,138,2,9,1,18
	.byte	'IfxAsclin_RtsCtsPolarity_activeHigh',0,0,18
	.byte	'IfxAsclin_RtsCtsPolarity_activeLow',0,1,0,29
	.byte	'IfxAsclin_RtsCtsPolarity',0,12,142,2,3
	.word	112931
	.byte	17,12,147,2,9,1,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_1',0,0,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_2',0,1,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_3',0,2,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_4',0,3,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_5',0,4,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_6',0,5,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_7',0,6,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_8',0,7,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_9',0,8,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_10',0,9,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_11',0,10,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_12',0,11,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_13',0,12,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_14',0,13,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_15',0,14,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_16',0,15,0,29
	.byte	'IfxAsclin_RxFifoInterruptLevel',0,12,165,2,3
	.word	113047
	.byte	17,12,170,2,9,1,18
	.byte	'IfxAsclin_RxFifoOutletWidth_0',0,0,18
	.byte	'IfxAsclin_RxFifoOutletWidth_1',0,1,18
	.byte	'IfxAsclin_RxFifoOutletWidth_2',0,2,18
	.byte	'IfxAsclin_RxFifoOutletWidth_3',0,3,0,29
	.byte	'IfxAsclin_RxFifoOutletWidth',0,12,176,2,3
	.word	113661
	.byte	29
	.byte	'IfxAsclin_RxInputSelect',0,12,191,2,3
	.word	17920
	.byte	17,12,196,2,9,1,18
	.byte	'IfxAsclin_SamplePointPosition_1',0,1,18
	.byte	'IfxAsclin_SamplePointPosition_2',0,2,18
	.byte	'IfxAsclin_SamplePointPosition_3',0,3,18
	.byte	'IfxAsclin_SamplePointPosition_4',0,4,18
	.byte	'IfxAsclin_SamplePointPosition_5',0,5,18
	.byte	'IfxAsclin_SamplePointPosition_6',0,6,18
	.byte	'IfxAsclin_SamplePointPosition_7',0,7,18
	.byte	'IfxAsclin_SamplePointPosition_8',0,8,18
	.byte	'IfxAsclin_SamplePointPosition_9',0,9,18
	.byte	'IfxAsclin_SamplePointPosition_10',0,10,18
	.byte	'IfxAsclin_SamplePointPosition_11',0,11,18
	.byte	'IfxAsclin_SamplePointPosition_12',0,12,18
	.byte	'IfxAsclin_SamplePointPosition_13',0,13,18
	.byte	'IfxAsclin_SamplePointPosition_14',0,14,18
	.byte	'IfxAsclin_SamplePointPosition_15',0,15,0,29
	.byte	'IfxAsclin_SamplePointPosition',0,12,213,2,3
	.word	113866
	.byte	17,12,218,2,9,1,18
	.byte	'IfxAsclin_SamplesPerBit_one',0,0,18
	.byte	'IfxAsclin_SamplesPerBit_three',0,1,0,29
	.byte	'IfxAsclin_SamplesPerBit',0,12,222,2,3
	.word	114428
	.byte	17,12,228,2,9,1,18
	.byte	'IfxAsclin_ShiftDirection_lsbFirst',0,0,18
	.byte	'IfxAsclin_ShiftDirection_msbFirst',0,1,0,29
	.byte	'IfxAsclin_ShiftDirection',0,12,232,2,3
	.word	114530
	.byte	17,12,238,2,9,1,18
	.byte	'IfxAsclin_SlavePolarity_idleLow',0,0,18
	.byte	'IfxAsclin_SlavePolarity_idlehigh',0,1,0,29
	.byte	'IfxAsclin_SlavePolarity',0,12,242,2,3
	.word	114643
	.byte	17,12,247,2,9,1,18
	.byte	'IfxAsclin_SleepMode_enable',0,0,18
	.byte	'IfxAsclin_SleepMode_disable',0,1,0,29
	.byte	'IfxAsclin_SleepMode',0,12,251,2,3
	.word	114752
	.byte	17,12,136,3,9,1,18
	.byte	'IfxAsclin_StopBit_0',0,0,18
	.byte	'IfxAsclin_StopBit_1',0,1,18
	.byte	'IfxAsclin_StopBit_2',0,2,18
	.byte	'IfxAsclin_StopBit_3',0,3,18
	.byte	'IfxAsclin_StopBit_4',0,4,18
	.byte	'IfxAsclin_StopBit_5',0,5,18
	.byte	'IfxAsclin_StopBit_6',0,6,18
	.byte	'IfxAsclin_StopBit_7',0,7,0,29
	.byte	'IfxAsclin_StopBit',0,12,146,3,3
	.word	114847
	.byte	17,12,150,3,9,1,18
	.byte	'IfxAsclin_SuspendMode_none',0,0,18
	.byte	'IfxAsclin_SuspendMode_hard',0,1,18
	.byte	'IfxAsclin_SuspendMode_soft',0,2,0,29
	.byte	'IfxAsclin_SuspendMode',0,12,155,3,3
	.word	115057
	.byte	17,12,160,3,9,1,18
	.byte	'IfxAsclin_TxFifoInletWidth_0',0,0,18
	.byte	'IfxAsclin_TxFifoInletWidth_1',0,1,18
	.byte	'IfxAsclin_TxFifoInletWidth_2',0,2,18
	.byte	'IfxAsclin_TxFifoInletWidth_3',0,3,0,29
	.byte	'IfxAsclin_TxFifoInletWidth',0,12,166,3,3
	.word	115182
	.byte	17,12,171,3,9,1,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_0',0,0,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_1',0,1,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_2',0,2,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_3',0,3,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_4',0,4,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_5',0,5,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_6',0,6,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_7',0,7,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_8',0,8,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_9',0,9,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_10',0,10,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_11',0,11,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_12',0,12,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_13',0,13,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_14',0,14,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_15',0,15,0,29
	.byte	'IfxAsclin_TxFifoInterruptLevel',0,12,189,3,3
	.word	115349
	.byte	29
	.byte	'Ifx_Fifo_Shared',0,14,66,3
	.word	18237
	.byte	29
	.byte	'Ifx_Fifo',0,14,83,3
	.word	18328
	.byte	17,15,151,1,9,1,18
	.byte	'IfxStm_Comparator_0',0,0,18
	.byte	'IfxStm_Comparator_1',0,1,0,29
	.byte	'IfxStm_Comparator',0,15,155,1,3
	.word	116003
	.byte	17,15,159,1,9,1,18
	.byte	'IfxStm_ComparatorInterrupt_ir0',0,0,18
	.byte	'IfxStm_ComparatorInterrupt_ir1',0,1,0,29
	.byte	'IfxStm_ComparatorInterrupt',0,15,163,1,3
	.word	116081
	.byte	17,15,167,1,9,1,18
	.byte	'IfxStm_ComparatorOffset_0',0,0,18
	.byte	'IfxStm_ComparatorOffset_1',0,1,18
	.byte	'IfxStm_ComparatorOffset_2',0,2,18
	.byte	'IfxStm_ComparatorOffset_3',0,3,18
	.byte	'IfxStm_ComparatorOffset_4',0,4,18
	.byte	'IfxStm_ComparatorOffset_5',0,5,18
	.byte	'IfxStm_ComparatorOffset_6',0,6,18
	.byte	'IfxStm_ComparatorOffset_7',0,7,18
	.byte	'IfxStm_ComparatorOffset_8',0,8,18
	.byte	'IfxStm_ComparatorOffset_9',0,9,18
	.byte	'IfxStm_ComparatorOffset_10',0,10,18
	.byte	'IfxStm_ComparatorOffset_11',0,11,18
	.byte	'IfxStm_ComparatorOffset_12',0,12,18
	.byte	'IfxStm_ComparatorOffset_13',0,13,18
	.byte	'IfxStm_ComparatorOffset_14',0,14,18
	.byte	'IfxStm_ComparatorOffset_15',0,15,18
	.byte	'IfxStm_ComparatorOffset_16',0,16,18
	.byte	'IfxStm_ComparatorOffset_17',0,17,18
	.byte	'IfxStm_ComparatorOffset_18',0,18,18
	.byte	'IfxStm_ComparatorOffset_19',0,19,18
	.byte	'IfxStm_ComparatorOffset_20',0,20,18
	.byte	'IfxStm_ComparatorOffset_21',0,21,18
	.byte	'IfxStm_ComparatorOffset_22',0,22,18
	.byte	'IfxStm_ComparatorOffset_23',0,23,18
	.byte	'IfxStm_ComparatorOffset_24',0,24,18
	.byte	'IfxStm_ComparatorOffset_25',0,25,18
	.byte	'IfxStm_ComparatorOffset_26',0,26,18
	.byte	'IfxStm_ComparatorOffset_27',0,27,18
	.byte	'IfxStm_ComparatorOffset_28',0,28,18
	.byte	'IfxStm_ComparatorOffset_29',0,29,18
	.byte	'IfxStm_ComparatorOffset_30',0,30,18
	.byte	'IfxStm_ComparatorOffset_31',0,31,0,29
	.byte	'IfxStm_ComparatorOffset',0,15,201,1,3
	.word	116190
	.byte	17,15,205,1,9,1,18
	.byte	'IfxStm_ComparatorSize_1Bit',0,0,18
	.byte	'IfxStm_ComparatorSize_2Bits',0,1,18
	.byte	'IfxStm_ComparatorSize_3Bits',0,2,18
	.byte	'IfxStm_ComparatorSize_4Bits',0,3,18
	.byte	'IfxStm_ComparatorSize_5Bits',0,4,18
	.byte	'IfxStm_ComparatorSize_6Bits',0,5,18
	.byte	'IfxStm_ComparatorSize_7Bits',0,6,18
	.byte	'IfxStm_ComparatorSize_8Bits',0,7,18
	.byte	'IfxStm_ComparatorSize_9Bits',0,8,18
	.byte	'IfxStm_ComparatorSize_10Bits',0,9,18
	.byte	'IfxStm_ComparatorSize_11Bits',0,10,18
	.byte	'IfxStm_ComparatorSize_12Bits',0,11,18
	.byte	'IfxStm_ComparatorSize_13Bits',0,12,18
	.byte	'IfxStm_ComparatorSize_14Bits',0,13,18
	.byte	'IfxStm_ComparatorSize_15Bits',0,14,18
	.byte	'IfxStm_ComparatorSize_16Bits',0,15,18
	.byte	'IfxStm_ComparatorSize_17Bits',0,16,18
	.byte	'IfxStm_ComparatorSize_18Bits',0,17,18
	.byte	'IfxStm_ComparatorSize_19Bits',0,18,18
	.byte	'IfxStm_ComparatorSize_20Bits',0,19,18
	.byte	'IfxStm_ComparatorSize_21Bits',0,20,18
	.byte	'IfxStm_ComparatorSize_22Bits',0,21,18
	.byte	'IfxStm_ComparatorSize_23Bits',0,22,18
	.byte	'IfxStm_ComparatorSize_24Bits',0,23,18
	.byte	'IfxStm_ComparatorSize_25Bits',0,24,18
	.byte	'IfxStm_ComparatorSize_26Bits',0,25,18
	.byte	'IfxStm_ComparatorSize_27Bits',0,26,18
	.byte	'IfxStm_ComparatorSize_28Bits',0,27,18
	.byte	'IfxStm_ComparatorSize_29Bits',0,28,18
	.byte	'IfxStm_ComparatorSize_30Bits',0,29,18
	.byte	'IfxStm_ComparatorSize_31Bits',0,30,18
	.byte	'IfxStm_ComparatorSize_32Bits',0,31,0,29
	.byte	'IfxStm_ComparatorSize',0,15,239,1,3
	.word	117148
	.byte	17,15,244,1,9,1,18
	.byte	'IfxStm_SleepMode_enable',0,0,18
	.byte	'IfxStm_SleepMode_disable',0,1,0,29
	.byte	'IfxStm_SleepMode',0,15,248,1,3
	.word	118168
	.byte	17,15,252,1,9,1,18
	.byte	'IfxStm_SuspendMode_none',0,0,18
	.byte	'IfxStm_SuspendMode_hard',0,1,18
	.byte	'IfxStm_SuspendMode_soft',0,2,0,29
	.byte	'IfxStm_SuspendMode',0,15,129,2,3
	.word	118254
	.byte	29
	.byte	'IfxStdIf_InterfaceDriver',0,39,118,15
	.word	382
	.byte	3
	.word	18212
	.byte	32
	.word	643
	.byte	1,1,33
	.word	382
	.byte	33
	.word	382
	.byte	33
	.word	118400
	.byte	33
	.word	22185
	.byte	0,3
	.word	118405
	.byte	29
	.byte	'IfxStdIf_DPipe_Write',0,40,92,19
	.word	118433
	.byte	29
	.byte	'IfxStdIf_DPipe_Read',0,40,107,19
	.word	118433
	.byte	32
	.word	18225
	.byte	1,1,33
	.word	382
	.byte	0,3
	.word	118495
	.byte	29
	.byte	'IfxStdIf_DPipe_GetReadCount',0,40,115,18
	.word	118508
	.byte	14
	.word	643
	.byte	3
	.word	118549
	.byte	32
	.word	118554
	.byte	1,1,33
	.word	382
	.byte	0,3
	.word	118559
	.byte	29
	.byte	'IfxStdIf_DPipe_GetReadEvent',0,40,123,36
	.word	118572
	.byte	29
	.byte	'IfxStdIf_DPipe_GetWriteCount',0,40,147,1,18
	.word	118508
	.byte	3
	.word	118559
	.byte	29
	.byte	'IfxStdIf_DPipe_GetWriteEvent',0,40,155,1,37
	.word	118651
	.byte	32
	.word	643
	.byte	1,1,33
	.word	382
	.byte	33
	.word	18212
	.byte	33
	.word	22185
	.byte	0,3
	.word	118694
	.byte	29
	.byte	'IfxStdIf_DPipe_CanReadCount',0,40,166,1,19
	.word	118717
	.byte	29
	.byte	'IfxStdIf_DPipe_CanWriteCount',0,40,177,1,19
	.word	118717
	.byte	32
	.word	643
	.byte	1,1,33
	.word	382
	.byte	33
	.word	22185
	.byte	0,3
	.word	118797
	.byte	29
	.byte	'IfxStdIf_DPipe_FlushTx',0,40,186,1,19
	.word	118815
	.byte	34,1,1,33
	.word	382
	.byte	0,3
	.word	118852
	.byte	29
	.byte	'IfxStdIf_DPipe_ClearTx',0,40,200,1,16
	.word	118861
	.byte	29
	.byte	'IfxStdIf_DPipe_ClearRx',0,40,193,1,16
	.word	118861
	.byte	29
	.byte	'IfxStdIf_DPipe_OnReceive',0,40,208,1,16
	.word	118861
	.byte	29
	.byte	'IfxStdIf_DPipe_OnTransmit',0,40,215,1,16
	.word	118861
	.byte	29
	.byte	'IfxStdIf_DPipe_OnError',0,40,222,1,16
	.word	118861
	.byte	32
	.word	10866
	.byte	1,1,33
	.word	382
	.byte	0,3
	.word	119031
	.byte	29
	.byte	'IfxStdIf_DPipe_GetSendCount',0,40,131,1,18
	.word	119044
	.byte	32
	.word	22185
	.byte	1,1,33
	.word	382
	.byte	0,3
	.word	119086
	.byte	29
	.byte	'IfxStdIf_DPipe_GetTxTimeStamp',0,40,139,1,24
	.word	119099
	.byte	29
	.byte	'IfxStdIf_DPipe_ResetSendCount',0,40,229,1,16
	.word	118861
	.byte	10
	.byte	'IfxStdIf_DPipe_',0,40,233,1,8,76,13
	.byte	'driver',0
	.word	118367
	.byte	4,2,35,0,13
	.byte	'txDisabled',0
	.word	643
	.byte	1,2,35,4,13
	.byte	'write',0
	.word	118438
	.byte	4,2,35,8,13
	.byte	'read',0
	.word	118467
	.byte	4,2,35,12,13
	.byte	'getReadCount',0
	.word	118513
	.byte	4,2,35,16,13
	.byte	'getReadEvent',0
	.word	118577
	.byte	4,2,35,20,13
	.byte	'getWriteCount',0
	.word	118613
	.byte	4,2,35,24,13
	.byte	'getWriteEvent',0
	.word	118656
	.byte	4,2,35,28,13
	.byte	'canReadCount',0
	.word	118722
	.byte	4,2,35,32,13
	.byte	'canWriteCount',0
	.word	118759
	.byte	4,2,35,36,13
	.byte	'flushTx',0
	.word	118820
	.byte	4,2,35,40,13
	.byte	'clearTx',0
	.word	118866
	.byte	4,2,35,44,13
	.byte	'clearRx',0
	.word	118898
	.byte	4,2,35,48,13
	.byte	'onReceive',0
	.word	118930
	.byte	4,2,35,52,13
	.byte	'onTransmit',0
	.word	118964
	.byte	4,2,35,56,13
	.byte	'onError',0
	.word	118999
	.byte	4,2,35,60,13
	.byte	'getSendCount',0
	.word	119049
	.byte	4,2,35,64,13
	.byte	'getTxTimeStamp',0
	.word	119104
	.byte	4,2,35,68,13
	.byte	'resetSendCount',0
	.word	119143
	.byte	4,2,35,72,0,29
	.byte	'IfxStdIf_DPipe',0,40,71,32
	.word	119182
	.byte	3
	.word	376
	.byte	3
	.word	118405
	.byte	3
	.word	118405
	.byte	3
	.word	118495
	.byte	3
	.word	118559
	.byte	3
	.word	118495
	.byte	3
	.word	118559
	.byte	3
	.word	118694
	.byte	3
	.word	118694
	.byte	3
	.word	118797
	.byte	3
	.word	118852
	.byte	3
	.word	118852
	.byte	3
	.word	118852
	.byte	3
	.word	118852
	.byte	3
	.word	118852
	.byte	3
	.word	119031
	.byte	3
	.word	119086
	.byte	3
	.word	118852
	.byte	14
	.word	643
	.byte	3
	.word	119695
	.byte	29
	.byte	'IfxStdIf_DPipe_WriteEvent',0,40,73,32
	.word	119700
	.byte	29
	.byte	'IfxStdIf_DPipe_ReadEvent',0,40,74,32
	.word	119700
	.byte	20,41,252,1,9,1,11
	.byte	'parityError',0,1
	.word	643
	.byte	1,7,2,35,0,11
	.byte	'frameError',0,1
	.word	643
	.byte	1,6,2,35,0,11
	.byte	'rxFifoOverflow',0,1
	.word	643
	.byte	1,5,2,35,0,11
	.byte	'rxFifoUnderflow',0,1
	.word	643
	.byte	1,4,2,35,0,11
	.byte	'txFifoOverflow',0,1
	.word	643
	.byte	1,3,2,35,0,0,29
	.byte	'IfxAsclin_Asc_ErrorFlags',0,41,131,2,3
	.word	119772
	.byte	20,41,137,2,9,8,13
	.byte	'baudrate',0
	.word	264
	.byte	4,2,35,0,13
	.byte	'prescaler',0
	.word	660
	.byte	2,2,35,4,13
	.byte	'oversampling',0
	.word	112238
	.byte	1,2,35,6,0,29
	.byte	'IfxAsclin_Asc_BaudRate',0,41,142,2,3
	.word	119937
	.byte	20,41,146,2,9,2,13
	.byte	'medianFilter',0
	.word	114428
	.byte	1,2,35,0,13
	.byte	'samplePointPosition',0
	.word	113866
	.byte	1,2,35,1,0,29
	.byte	'IfxAsclin_Asc_BitTimingControl',0,41,150,2,3
	.word	120035
	.byte	20,41,154,2,9,6,13
	.byte	'inWidth',0
	.word	115182
	.byte	1,2,35,0,13
	.byte	'outWidth',0
	.word	113661
	.byte	1,2,35,1,13
	.byte	'txFifoInterruptLevel',0
	.word	115349
	.byte	1,2,35,2,13
	.byte	'rxFifoInterruptLevel',0
	.word	113047
	.byte	1,2,35,3,13
	.byte	'buffMode',0
	.word	112811
	.byte	1,2,35,4,0,29
	.byte	'IfxAsclin_Asc_FifoControl',0,41,161,2,3
	.word	120133
	.byte	20,41,165,2,9,8,13
	.byte	'idleDelay',0
	.word	111547
	.byte	1,2,35,0,13
	.byte	'stopBit',0
	.word	114847
	.byte	1,2,35,1,13
	.byte	'frameMode',0
	.word	111258
	.byte	1,2,35,2,13
	.byte	'shiftDir',0
	.word	114530
	.byte	1,2,35,3,13
	.byte	'parityType',0
	.word	112719
	.byte	1,2,35,4,13
	.byte	'dataLength',0
	.word	110814
	.byte	1,2,35,5,13
	.byte	'parityBit',0
	.word	643
	.byte	1,2,35,6,0,29
	.byte	'IfxAsclin_Asc_FrameControl',0,41,174,2,3
	.word	120288
	.byte	20,41,178,2,9,8,13
	.byte	'txPriority',0
	.word	660
	.byte	2,2,35,0,13
	.byte	'rxPriority',0
	.word	660
	.byte	2,2,35,2,13
	.byte	'erPriority',0
	.word	660
	.byte	2,2,35,4,13
	.byte	'typeOfService',0
	.word	70414
	.byte	1,2,35,6,0,29
	.byte	'IfxAsclin_Asc_InterruptConfig',0,41,184,2,3
	.word	120463
	.byte	28
	.word	109715
	.byte	3
	.word	120592
	.byte	28
	.word	109796
	.byte	3
	.word	120602
	.byte	28
	.word	109876
	.byte	3
	.word	120612
	.byte	28
	.word	110124
	.byte	3
	.word	120622
	.byte	20,41,188,2,9,32,13
	.byte	'cts',0
	.word	120597
	.byte	4,2,35,0,13
	.byte	'ctsMode',0
	.word	9393
	.byte	1,2,35,4,13
	.byte	'rx',0
	.word	120607
	.byte	4,2,35,8,13
	.byte	'rxMode',0
	.word	9393
	.byte	1,2,35,12,13
	.byte	'rts',0
	.word	120617
	.byte	4,2,35,16,13
	.byte	'rtsMode',0
	.word	9598
	.byte	1,2,35,20,13
	.byte	'tx',0
	.word	120627
	.byte	4,2,35,24,13
	.byte	'txMode',0
	.word	9598
	.byte	1,2,35,28,13
	.byte	'pinDriver',0
	.word	56279
	.byte	1,2,35,29,0,29
	.byte	'IfxAsclin_Asc_Pins',0,41,199,2,3
	.word	120632
	.byte	12,41,205,2,9,1,13
	.byte	'ALL',0
	.word	643
	.byte	1,2,35,0,13
	.byte	'flags',0
	.word	119772
	.byte	1,2,35,0,0,29
	.byte	'IfxAsclin_Asc_ErrorFlagsUnion',0,41,209,2,3
	.word	120802
	.byte	29
	.byte	'uart_index_enum',0,24,109,2
	.word	27562
.L85:
	.byte	15,4
	.word	643
	.byte	16,3,0
.L86:
	.byte	15,8
	.word	643
	.byte	16,7,0,0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L34:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,21,0,54,15,39,12,0,0,3,15,0,73,19,0,0,4,46,1,3,8,32,13
	.byte	58,15,59,15,57,15,54,15,39,12,0,0,5,5,0,3,8,58,15,59,15,57,15,73,19,0,0,6,11,0,0,0,7,36,0,3,8,11,15,62
	.byte	15,0,0,8,46,1,3,8,32,13,58,15,59,15,57,15,73,19,54,15,39,12,0,0,9,59,0,3,8,0,0,10,19,1,3,8,58,15,59,15
	.byte	57,15,11,15,0,0,11,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,12,23,1,58,15,59,15,57,15,11,15,0,0,13,13
	.byte	0,3,8,73,19,11,15,56,9,0,0,14,53,0,73,19,0,0,15,1,1,11,15,73,19,0,0,16,33,0,47,15,0,0,17,4,1,58,15,59
	.byte	15,57,15,11,15,0,0,18,40,0,3,8,28,13,0,0,19,11,1,0,0,20,19,1,58,15,59,15,57,15,11,15,0,0,21,46,1,3,8,58
	.byte	15,59,15,57,15,54,15,39,12,63,12,60,12,0,0,22,46,1,49,19,0,0,23,5,0,49,19,0,0,24,29,1,49,19,0,0,25,11
	.byte	0,49,19,0,0,26,46,1,3,8,58,15,59,15,57,15,73,19,54,15,39,12,63,12,60,12,0,0,27,11,1,49,19,0,0,28,38,0
	.byte	73,19,0,0,29,22,0,3,8,58,15,59,15,57,15,73,19,0,0,30,21,0,54,15,0,0,31,52,0,3,8,58,15,59,15,57,15,73,19
	.byte	63,12,60,12,0,0,32,21,1,73,19,54,15,39,12,0,0,33,5,0,73,19,0,0,34,21,1,54,15,39,12,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L35:
	.word	.L142-.L141
.L141:
	.half	3
	.word	.L144-.L143
.L143:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver',0
	.byte	'F:\\ADS\\AURIX-Studio-1.10.2\\tools\\Compilers\\Tasking_1.1r8\\ctc\\include\\',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc',0
	.byte	0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_Intrinsics.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Scu\\Std\\IfxScuWdt.h',0
	.byte	0,0,0
	.byte	'IfxScu_regdef.h',0,1,0,0
	.byte	'IFXPORT.h',0,2,0,0
	.byte	'IfxPort_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Src\\Std\\IfxSrc.h',0
	.byte	0,0,0
	.byte	'IfxSrc_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxCpu_cfg.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Scu\\Std\\IfxScuCcu.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Asclin\\Std\\IfxAsclin.h',0
	.byte	0,0,0
	.byte	'IfxAsclin_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Lib\\DataHandling\\Ifx_Fifo.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Stm\\Std\\IfxStm.h',0
	.byte	0,0,0
	.byte	'IfxStm_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\SysSe\\Bsp\\Bsp.h',0
	.byte	0,0,0
	.byte	'zf_common_debug.h',0,3,0,0
	.byte	'zf_driver_gpio.h',0,4,0,0
	.byte	'zf_driver_exti.h',0,4,0,0
	.byte	'zf_driver_dma.h',0,4,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxDma_cfg.h',0
	.byte	0,0,0
	.byte	'zf_common_fifo.h',0,3,0,0
	.byte	'zf_driver_uart.h',0,4,0,0
	.byte	'../libraries/zf_device/zf_device_camera.c',0,0,0,0
	.byte	'stdio.h',0,5,0,0
	.byte	'Platform_Types.h',0,6,0,0
	.byte	'ifx_types.h',0,6,0,0
	.byte	'zf_common_typedef.h',0,3,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxScu_cfg.h',0
	.byte	0,0,0
	.byte	'IfxDma_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxSrc_cfg.h',0
	.byte	0,0,0
	.byte	'IfxCpu_regdef.h',0,1,0,0
	.byte	'IfxFlash_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_PinMap\\IfxScu_PinMap.h',0
	.byte	0,0,0
	.byte	'IfxDma.h',0,7,0,0
	.byte	'..\\libraries\\zf_device\\zf_device_type.h',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_PinMap\\IfxAsclin_PinMap.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\StdIf\\IfxStdIf.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\StdIf\\IfxStdIf_DPipe.h',0
	.byte	0,0,0
	.byte	'ifxAsclin_Asc.h',0,8,0,0,0
.L144:
.L142:
	.sdecl	'.debug_info',debug,cluster('camera_binary_image_decompression')
	.sect	'.debug_info'
.L36:
	.word	357
	.half	3
	.word	.L37
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_camera.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L39,.L38
	.byte	2
	.word	.L32
	.byte	3
	.byte	'camera_binary_image_decompression',0,1,60,6,1,1,1
	.word	.L25,.L62,.L24
	.byte	4
	.byte	'data1',0,1,60,54
	.word	.L63,.L64
	.byte	4
	.byte	'data2',0,1,60,68
	.word	.L65,.L66
	.byte	4
	.byte	'image_size',0,1,60,82
	.word	.L67,.L68
	.byte	5
	.word	.L25,.L62
	.byte	5
	.word	.L69,.L62
	.byte	6
	.byte	'i',0,1,64,12
	.word	.L70,.L71
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('camera_binary_image_decompression')
	.sect	'.debug_abbrev'
.L37:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('camera_binary_image_decompression')
	.sect	'.debug_line'
.L38:
	.word	.L146-.L145
.L145:
	.half	3
	.word	.L148-.L147
.L147:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_camera.c',0,0,0,0,0
.L148:
	.byte	5,6,7,0,5,2
	.word	.L25
	.byte	3,59,1,5,5,9
	.half	.L92-.L25
	.byte	3,2,1,9
	.half	.L88-.L92
	.byte	3,1,1,5,24,9
	.half	.L69-.L88
	.byte	3,3,1,5,11,9
	.half	.L3-.L69
	.byte	3,2,1,5,19,9
	.half	.L93-.L3
	.byte	3,1,1,5,28,9
	.half	.L5-.L93
	.byte	3,2,1,5,35,9
	.half	.L149-.L5
	.byte	1,5,26,9
	.half	.L150-.L149
	.byte	1,5,49,7,9
	.half	.L151-.L150
	.byte	1,5,55,9
	.half	.L152-.L151
	.byte	1,5,49,9
	.half	.L6-.L152
	.byte	1,5,23,9
	.half	.L7-.L6
	.byte	1,5,20,9
	.half	.L153-.L7
	.byte	1,5,17,9
	.half	.L4-.L153
	.byte	3,126,1,5,19,9
	.half	.L97-.L4
	.byte	1,5,15,7,9
	.half	.L154-.L97
	.byte	3,4,1,5,22,9
	.half	.L2-.L154
	.byte	3,121,1,5,24,9
	.half	.L155-.L2
	.byte	1,5,1,7,9
	.half	.L156-.L155
	.byte	3,9,1,7,9
	.half	.L40-.L156
	.byte	0,1,1
.L146:
	.sdecl	'.debug_ranges',debug,cluster('camera_binary_image_decompression')
	.sect	'.debug_ranges'
.L39:
	.word	-1,.L25,0,.L40-.L25,0,0
	.sdecl	'.debug_info',debug,cluster('camera_send_image')
	.sect	'.debug_info'
.L41:
	.word	321
	.half	3
	.word	.L42
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_camera.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L44,.L43
	.byte	2
	.word	.L32
	.byte	3
	.byte	'camera_send_image',0,1,85,6,1,1,1
	.word	.L27,.L72,.L26
	.byte	4
	.byte	'uartn',0,1,85,41
	.word	.L73,.L74
	.byte	4
	.byte	'image_addr',0,1,85,61
	.word	.L63,.L75
	.byte	4
	.byte	'image_size',0,1,85,80
	.word	.L67,.L76
	.byte	5
	.word	.L27,.L72
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('camera_send_image')
	.sect	'.debug_abbrev'
.L42:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('camera_send_image')
	.sect	'.debug_line'
.L43:
	.word	.L158-.L157
.L157:
	.half	3
	.word	.L160-.L159
.L159:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_camera.c',0,0,0,0,0
.L160:
	.byte	5,6,7,0,5,2
	.word	.L27
	.byte	3,212,0,1,5,5,9
	.half	.L103-.L27
	.byte	3,2,1,5,30,9
	.half	.L161-.L103
	.byte	3,2,1,5,62,9
	.half	.L162-.L161
	.byte	1,5,51,9
	.half	.L105-.L162
	.byte	3,3,1,5,1,9
	.half	.L107-.L105
	.byte	3,1,1,7,9
	.half	.L45-.L107
	.byte	0,1,1
.L158:
	.sdecl	'.debug_ranges',debug,cluster('camera_send_image')
	.sect	'.debug_ranges'
.L44:
	.word	-1,.L27,0,.L45-.L27,0,0
	.sdecl	'.debug_info',debug,cluster('camera_fifo_init')
	.sect	'.debug_info'
.L46:
	.word	256
	.half	3
	.word	.L47
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_camera.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L49,.L48
	.byte	2
	.word	.L32
	.byte	3
	.byte	'camera_fifo_init',0,1,102,6,1,1,1
	.word	.L29,.L77,.L28
	.byte	4
	.word	.L29,.L77
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('camera_fifo_init')
	.sect	'.debug_abbrev'
.L47:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('camera_fifo_init')
	.sect	'.debug_line'
.L48:
	.word	.L164-.L163
.L163:
	.half	3
	.word	.L166-.L165
.L165:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_camera.c',0,0,0,0,0
.L166:
	.byte	5,16,7,0,5,2
	.word	.L29
	.byte	3,231,0,1,5,38,9
	.half	.L167-.L29
	.byte	1,5,54,9
	.half	.L168-.L167
	.byte	1,5,78,9
	.half	.L169-.L168
	.byte	1,5,1,9
	.half	.L170-.L169
	.byte	3,1,1,7,9
	.half	.L50-.L170
	.byte	0,1,1
.L164:
	.sdecl	'.debug_ranges',debug,cluster('camera_fifo_init')
	.sect	'.debug_ranges'
.L49:
	.word	-1,.L29,0,.L50-.L29,0,0
	.sdecl	'.debug_info',debug,cluster('camera_init')
	.sect	'.debug_info'
.L51:
	.word	374
	.half	3
	.word	.L52
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_camera.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L54,.L53
	.byte	2
	.word	.L32
	.byte	3
	.byte	'camera_init',0,1,118,7
	.word	.L70
	.byte	1,1,1
	.word	.L31,.L78,.L30
	.byte	4
	.byte	'source_addr',0,1,118,27
	.word	.L65,.L79
	.byte	4
	.byte	'destination_addr',0,1,118,47
	.word	.L65,.L80
	.byte	4
	.byte	'image_size',0,1,118,72
	.word	.L67,.L81
	.byte	5
	.word	.L31,.L78
	.byte	6
	.byte	'num',0,1,120,11
	.word	.L70,.L82
	.byte	6
	.byte	'link_list_num',0,1,121,11
	.word	.L70,.L83
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('camera_init')
	.sect	'.debug_abbrev'
.L52:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('camera_init')
	.sect	'.debug_line'
.L53:
	.word	.L172-.L171
.L171:
	.half	3
	.word	.L174-.L173
.L173:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_camera.c',0,0,0,0,0
.L174:
	.byte	5,7,7,0,5,2
	.word	.L31
	.byte	3,245,0,1,5,12,9
	.half	.L112-.L31
	.byte	3,4,1,5,14,9
	.half	.L175-.L112
	.byte	3,2,1,9
	.half	.L176-.L175
	.byte	3,1,1,9
	.half	.L177-.L176
	.byte	3,13,1,9
	.half	.L178-.L177
	.byte	3,14,1,5,21,9
	.half	.L9-.L178
	.byte	3,102,1,5,33,9
	.half	.L113-.L9
	.byte	1,5,67,9
	.half	.L14-.L113
	.byte	3,2,1,5,72,9
	.half	.L179-.L14
	.byte	1,5,82,9
	.half	.L180-.L179
	.byte	1,5,39,9
	.half	.L116-.L180
	.byte	3,126,1,5,33,9
	.half	.L13-.L116
	.byte	1,5,38,7,9
	.half	.L181-.L13
	.byte	3,4,1,9
	.half	.L182-.L181
	.byte	3,3,1,9
	.half	.L183-.L182
	.byte	3,1,1,9
	.half	.L184-.L183
	.byte	3,1,1,5,27,9
	.half	.L118-.L184
	.byte	3,123,1,5,23,9
	.half	.L114-.L118
	.byte	3,6,1,5,41,9
	.half	.L185-.L114
	.byte	1,5,13,9
	.half	.L121-.L185
	.byte	3,1,1,5,21,9
	.half	.L10-.L121
	.byte	3,2,1,5,33,9
	.half	.L122-.L10
	.byte	1,5,68,9
	.half	.L17-.L122
	.byte	3,2,1,5,73,9
	.half	.L186-.L17
	.byte	1,5,83,9
	.half	.L187-.L186
	.byte	1,5,39,9
	.half	.L125-.L187
	.byte	3,126,1,5,33,9
	.half	.L16-.L125
	.byte	1,5,38,7,9
	.half	.L188-.L16
	.byte	3,4,1,9
	.half	.L189-.L188
	.byte	3,3,1,9
	.half	.L190-.L189
	.byte	3,1,1,9
	.half	.L191-.L190
	.byte	3,1,1,5,27,9
	.half	.L127-.L191
	.byte	3,123,1,5,23,9
	.half	.L123-.L127
	.byte	3,7,1,5,42,9
	.half	.L192-.L123
	.byte	1,5,13,9
	.half	.L130-.L192
	.byte	3,1,1,5,20,9
	.half	.L11-.L130
	.byte	3,2,1,5,29,9
	.half	.L131-.L11
	.byte	1,5,68,9
	.half	.L20-.L131
	.byte	3,2,1,5,73,9
	.half	.L193-.L20
	.byte	1,5,83,9
	.half	.L194-.L193
	.byte	1,5,34,9
	.half	.L134-.L194
	.byte	3,126,1,5,29,9
	.half	.L19-.L134
	.byte	1,5,38,7,9
	.half	.L195-.L19
	.byte	3,5,1,9
	.half	.L196-.L195
	.byte	3,3,1,9
	.half	.L197-.L196
	.byte	3,1,1,9
	.half	.L198-.L197
	.byte	3,1,1,5,27,9
	.half	.L136-.L198
	.byte	3,123,1,5,23,9
	.half	.L132-.L136
	.byte	3,7,1,5,42,9
	.half	.L199-.L132
	.byte	1,5,13,9
	.half	.L139-.L199
	.byte	3,1,1,9
	.half	.L12-.L139
	.byte	3,2,1,5,5,9
	.half	.L15-.L12
	.byte	3,2,1,5,1,9
	.half	.L23-.L15
	.byte	3,1,1,7,9
	.half	.L55-.L23
	.byte	0,1,1
.L172:
	.sdecl	'.debug_ranges',debug,cluster('camera_init')
	.sect	'.debug_ranges'
.L54:
	.word	-1,.L31,0,.L55-.L31,0,0
	.sdecl	'.debug_info',debug,cluster('camera_receiver_fifo')
	.sect	'.debug_info'
.L56:
	.word	238
	.half	3
	.word	.L57
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_camera.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L32
	.byte	3
	.byte	'camera_receiver_fifo',0,25,48,13
	.word	.L84
	.byte	1,5,3
	.word	camera_receiver_fifo
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('camera_receiver_fifo')
	.sect	'.debug_abbrev'
.L57:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('camera_send_image_frame_header')
	.sect	'.debug_info'
.L58:
	.word	248
	.half	3
	.word	.L59
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_camera.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L32
	.byte	3
	.byte	'camera_send_image_frame_header',0,25,50,7
	.word	.L85
	.byte	1,5,3
	.word	camera_send_image_frame_header
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('camera_send_image_frame_header')
	.sect	'.debug_abbrev'
.L59:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('camera_receiver_buffer')
	.sect	'.debug_info'
.L60:
	.word	240
	.half	3
	.word	.L61
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_camera.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L32
	.byte	3
	.byte	'camera_receiver_buffer',0,25,49,7
	.word	.L86
	.byte	1,5,3
	.word	camera_receiver_buffer
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('camera_receiver_buffer')
	.sect	'.debug_abbrev'
.L61:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_loc',debug,cluster('camera_binary_image_decompression')
	.sect	'.debug_loc'
.L24:
	.word	-1,.L25,0,.L62-.L25
	.half	2
	.byte	138,0
	.word	0,0
.L64:
	.word	-1,.L25,0,.L87-.L25
	.half	1
	.byte	100
	.word	.L90-.L25,.L62-.L25
	.half	1
	.byte	111
	.word	0,0
.L66:
	.word	-1,.L25,0,.L88-.L25
	.half	1
	.byte	101
	.word	.L91-.L25,.L62-.L25
	.half	1
	.byte	108
	.word	0,0
.L71:
	.word	-1,.L25,.L93-.L25,.L94-.L25
	.half	1
	.byte	95
	.word	.L95-.L25,.L4-.L25
	.half	1
	.byte	81
	.word	.L4-.L25,.L96-.L25
	.half	1
	.byte	95
	.word	.L97-.L25,.L2-.L25
	.half	1
	.byte	95
	.word	0,0
.L68:
	.word	-1,.L25,0,.L89-.L25
	.half	1
	.byte	84
	.word	.L92-.L25,.L62-.L25
	.half	1
	.byte	88
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('camera_fifo_init')
	.sect	'.debug_loc'
.L28:
	.word	-1,.L29,0,.L77-.L29
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('camera_init')
	.sect	'.debug_loc'
.L30:
	.word	-1,.L31,0,.L78-.L31
	.half	2
	.byte	138,0
	.word	0,0
.L80:
	.word	-1,.L31,0,.L14-.L31
	.half	1
	.byte	101
	.word	.L111-.L31,.L78-.L31
	.half	1
	.byte	108
	.word	.L119-.L31,.L118-.L31
	.half	1
	.byte	101
	.word	.L10-.L31,.L17-.L31
	.half	1
	.byte	101
	.word	.L128-.L31,.L127-.L31
	.half	1
	.byte	101
	.word	.L11-.L31,.L20-.L31
	.half	1
	.byte	101
	.word	.L137-.L31,.L136-.L31
	.half	1
	.byte	101
	.word	.L12-.L31,.L15-.L31
	.half	1
	.byte	101
	.word	0,0
.L81:
	.word	-1,.L31,0,.L14-.L31
	.half	1
	.byte	84
	.word	.L112-.L31,.L78-.L31
	.half	1
	.byte	88
	.word	.L120-.L31,.L118-.L31
	.half	1
	.byte	87
	.word	.L10-.L31,.L17-.L31
	.half	1
	.byte	84
	.word	.L129-.L31,.L127-.L31
	.half	1
	.byte	87
	.word	.L11-.L31,.L20-.L31
	.half	1
	.byte	84
	.word	.L138-.L31,.L136-.L31
	.half	1
	.byte	87
	.word	.L12-.L31,.L15-.L31
	.half	1
	.byte	84
	.word	0,0
.L83:
	.word	-1,.L31,.L118-.L31,.L121-.L31
	.half	1
	.byte	82
	.word	.L114-.L31,.L10-.L31
	.half	1
	.byte	95
	.word	.L127-.L31,.L130-.L31
	.half	1
	.byte	82
	.word	.L123-.L31,.L11-.L31
	.half	1
	.byte	95
	.word	.L136-.L31,.L139-.L31
	.half	1
	.byte	82
	.word	.L132-.L31,.L12-.L31
	.half	1
	.byte	95
	.word	.L15-.L31,.L140-.L31
	.half	1
	.byte	95
	.word	.L140-.L31,.L78-.L31
	.half	1
	.byte	82
	.word	0,0
.L82:
	.word	-1,.L31,.L113-.L31,.L114-.L31
	.half	1
	.byte	95
	.word	.L115-.L31,.L116-.L31
	.half	1
	.byte	84
	.word	.L122-.L31,.L123-.L31
	.half	1
	.byte	95
	.word	.L124-.L31,.L125-.L31
	.half	1
	.byte	84
	.word	.L131-.L31,.L132-.L31
	.half	1
	.byte	95
	.word	.L133-.L31,.L134-.L31
	.half	1
	.byte	84
	.word	0,0
.L79:
	.word	-1,.L31,0,.L14-.L31
	.half	1
	.byte	100
	.word	.L110-.L31,.L78-.L31
	.half	1
	.byte	111
	.word	.L117-.L31,.L118-.L31
	.half	1
	.byte	100
	.word	.L10-.L31,.L17-.L31
	.half	1
	.byte	100
	.word	.L126-.L31,.L127-.L31
	.half	1
	.byte	100
	.word	.L11-.L31,.L20-.L31
	.half	1
	.byte	100
	.word	.L135-.L31,.L136-.L31
	.half	1
	.byte	100
	.word	.L12-.L31,.L15-.L31
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('camera_send_image')
	.sect	'.debug_loc'
.L26:
	.word	-1,.L27,0,.L72-.L27
	.half	2
	.byte	138,0
	.word	0,0
.L75:
	.word	-1,.L27,0,.L98-.L27
	.half	1
	.byte	100
	.word	.L102-.L27,.L72-.L27
	.half	1
	.byte	111
	.word	.L108-.L27,.L107-.L27
	.half	1
	.byte	100
	.word	0,0
.L76:
	.word	-1,.L27,0,.L99-.L27
	.half	1
	.byte	85
	.word	.L103-.L27,.L72-.L27
	.half	1
	.byte	88
	.word	.L109-.L27,.L107-.L27
	.half	1
	.byte	85
	.word	0,0
.L74:
	.word	-1,.L27,0,.L100-.L27
	.half	1
	.byte	84
	.word	.L101-.L27,.L72-.L27
	.half	1
	.byte	95
	.word	.L104-.L27,.L105-.L27
	.half	1
	.byte	84
	.word	.L106-.L27,.L107-.L27
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L200:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('camera_binary_image_decompression')
	.sect	'.debug_frame'
	.word	12
	.word	.L200,.L25,.L62-.L25
	.sdecl	'.debug_frame',debug,cluster('camera_send_image')
	.sect	'.debug_frame'
	.word	12
	.word	.L200,.L27,.L72-.L27
	.sdecl	'.debug_frame',debug,cluster('camera_fifo_init')
	.sect	'.debug_frame'
	.word	12
	.word	.L200,.L29,.L77-.L29
	.sdecl	'.debug_frame',debug,cluster('camera_init')
	.sect	'.debug_frame'
	.word	12
	.word	.L200,.L31,.L78-.L31
	; Module end
