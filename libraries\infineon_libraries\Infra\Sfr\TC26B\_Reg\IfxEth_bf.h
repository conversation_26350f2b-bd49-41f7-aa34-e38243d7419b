/**
 * \file IfxEth_bf.h
 * \brief
 * \copyright Copyright (c) 2015 Infineon Technologies AG. All rights reserved.
 *
 * Version: TC26XB_UM_V1.2.R0
 * Specification: tc26xB_um_v1.2_MCSFR.xml (Revision: UM_V1.2)
 * MAY BE CHANGED BY USER [yes/no]: No
 *
 *                                 IMPORTANT NOTICE
 *
 * Infineon Technologies AG (Infineon) is supplying this file for use
 * exclusively with Infineon's microcontroller products. This file can be freely
 * distributed within development tools that are supporting such microcontroller
 * products.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS".  NO WARRANTIES, WHETHER EXPRESS, IMPLIED
 * OR STATUTORY, INCLUDING, BUT NOT LIMITED TO, IMPLIED WARRANTIES OF
 * MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE APPLY TO THIS SOFTWARE.
 * INFINEON SHALL NOT, IN ANY CIRCUMSTANCES, BE LIABLE FOR SPECIAL, INCIDENTAL,
 * OR CONSEQUENTIAL DAMAGES, FOR ANY REASON WHATSOEVER.
 *
 * \defgroup IfxLld_Eth_BitfieldsMask Bitfields mask and offset
 * \ingroup IfxLld_Eth
 * 
 */
#ifndef IFXETH_BF_H
#define IFXETH_BF_H 1
/******************************************************************************/
/******************************************************************************/
/** \addtogroup IfxLld_Eth_BitfieldsMask
 * \{  */

/** \brief  Length for Ifx_ETH_ACCEN0_Bits.EN0 */
#define IFX_ETH_ACCEN0_EN0_LEN (1u)

/** \brief  Mask for Ifx_ETH_ACCEN0_Bits.EN0 */
#define IFX_ETH_ACCEN0_EN0_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_ACCEN0_Bits.EN0 */
#define IFX_ETH_ACCEN0_EN0_OFF (0u)

/** \brief  Length for Ifx_ETH_ACCEN0_Bits.EN10 */
#define IFX_ETH_ACCEN0_EN10_LEN (1u)

/** \brief  Mask for Ifx_ETH_ACCEN0_Bits.EN10 */
#define IFX_ETH_ACCEN0_EN10_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_ACCEN0_Bits.EN10 */
#define IFX_ETH_ACCEN0_EN10_OFF (10u)

/** \brief  Length for Ifx_ETH_ACCEN0_Bits.EN11 */
#define IFX_ETH_ACCEN0_EN11_LEN (1u)

/** \brief  Mask for Ifx_ETH_ACCEN0_Bits.EN11 */
#define IFX_ETH_ACCEN0_EN11_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_ACCEN0_Bits.EN11 */
#define IFX_ETH_ACCEN0_EN11_OFF (11u)

/** \brief  Length for Ifx_ETH_ACCEN0_Bits.EN12 */
#define IFX_ETH_ACCEN0_EN12_LEN (1u)

/** \brief  Mask for Ifx_ETH_ACCEN0_Bits.EN12 */
#define IFX_ETH_ACCEN0_EN12_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_ACCEN0_Bits.EN12 */
#define IFX_ETH_ACCEN0_EN12_OFF (12u)

/** \brief  Length for Ifx_ETH_ACCEN0_Bits.EN13 */
#define IFX_ETH_ACCEN0_EN13_LEN (1u)

/** \brief  Mask for Ifx_ETH_ACCEN0_Bits.EN13 */
#define IFX_ETH_ACCEN0_EN13_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_ACCEN0_Bits.EN13 */
#define IFX_ETH_ACCEN0_EN13_OFF (13u)

/** \brief  Length for Ifx_ETH_ACCEN0_Bits.EN14 */
#define IFX_ETH_ACCEN0_EN14_LEN (1u)

/** \brief  Mask for Ifx_ETH_ACCEN0_Bits.EN14 */
#define IFX_ETH_ACCEN0_EN14_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_ACCEN0_Bits.EN14 */
#define IFX_ETH_ACCEN0_EN14_OFF (14u)

/** \brief  Length for Ifx_ETH_ACCEN0_Bits.EN15 */
#define IFX_ETH_ACCEN0_EN15_LEN (1u)

/** \brief  Mask for Ifx_ETH_ACCEN0_Bits.EN15 */
#define IFX_ETH_ACCEN0_EN15_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_ACCEN0_Bits.EN15 */
#define IFX_ETH_ACCEN0_EN15_OFF (15u)

/** \brief  Length for Ifx_ETH_ACCEN0_Bits.EN16 */
#define IFX_ETH_ACCEN0_EN16_LEN (1u)

/** \brief  Mask for Ifx_ETH_ACCEN0_Bits.EN16 */
#define IFX_ETH_ACCEN0_EN16_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_ACCEN0_Bits.EN16 */
#define IFX_ETH_ACCEN0_EN16_OFF (16u)

/** \brief  Length for Ifx_ETH_ACCEN0_Bits.EN17 */
#define IFX_ETH_ACCEN0_EN17_LEN (1u)

/** \brief  Mask for Ifx_ETH_ACCEN0_Bits.EN17 */
#define IFX_ETH_ACCEN0_EN17_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_ACCEN0_Bits.EN17 */
#define IFX_ETH_ACCEN0_EN17_OFF (17u)

/** \brief  Length for Ifx_ETH_ACCEN0_Bits.EN18 */
#define IFX_ETH_ACCEN0_EN18_LEN (1u)

/** \brief  Mask for Ifx_ETH_ACCEN0_Bits.EN18 */
#define IFX_ETH_ACCEN0_EN18_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_ACCEN0_Bits.EN18 */
#define IFX_ETH_ACCEN0_EN18_OFF (18u)

/** \brief  Length for Ifx_ETH_ACCEN0_Bits.EN19 */
#define IFX_ETH_ACCEN0_EN19_LEN (1u)

/** \brief  Mask for Ifx_ETH_ACCEN0_Bits.EN19 */
#define IFX_ETH_ACCEN0_EN19_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_ACCEN0_Bits.EN19 */
#define IFX_ETH_ACCEN0_EN19_OFF (19u)

/** \brief  Length for Ifx_ETH_ACCEN0_Bits.EN1 */
#define IFX_ETH_ACCEN0_EN1_LEN (1u)

/** \brief  Mask for Ifx_ETH_ACCEN0_Bits.EN1 */
#define IFX_ETH_ACCEN0_EN1_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_ACCEN0_Bits.EN1 */
#define IFX_ETH_ACCEN0_EN1_OFF (1u)

/** \brief  Length for Ifx_ETH_ACCEN0_Bits.EN20 */
#define IFX_ETH_ACCEN0_EN20_LEN (1u)

/** \brief  Mask for Ifx_ETH_ACCEN0_Bits.EN20 */
#define IFX_ETH_ACCEN0_EN20_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_ACCEN0_Bits.EN20 */
#define IFX_ETH_ACCEN0_EN20_OFF (20u)

/** \brief  Length for Ifx_ETH_ACCEN0_Bits.EN21 */
#define IFX_ETH_ACCEN0_EN21_LEN (1u)

/** \brief  Mask for Ifx_ETH_ACCEN0_Bits.EN21 */
#define IFX_ETH_ACCEN0_EN21_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_ACCEN0_Bits.EN21 */
#define IFX_ETH_ACCEN0_EN21_OFF (21u)

/** \brief  Length for Ifx_ETH_ACCEN0_Bits.EN22 */
#define IFX_ETH_ACCEN0_EN22_LEN (1u)

/** \brief  Mask for Ifx_ETH_ACCEN0_Bits.EN22 */
#define IFX_ETH_ACCEN0_EN22_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_ACCEN0_Bits.EN22 */
#define IFX_ETH_ACCEN0_EN22_OFF (22u)

/** \brief  Length for Ifx_ETH_ACCEN0_Bits.EN23 */
#define IFX_ETH_ACCEN0_EN23_LEN (1u)

/** \brief  Mask for Ifx_ETH_ACCEN0_Bits.EN23 */
#define IFX_ETH_ACCEN0_EN23_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_ACCEN0_Bits.EN23 */
#define IFX_ETH_ACCEN0_EN23_OFF (23u)

/** \brief  Length for Ifx_ETH_ACCEN0_Bits.EN24 */
#define IFX_ETH_ACCEN0_EN24_LEN (1u)

/** \brief  Mask for Ifx_ETH_ACCEN0_Bits.EN24 */
#define IFX_ETH_ACCEN0_EN24_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_ACCEN0_Bits.EN24 */
#define IFX_ETH_ACCEN0_EN24_OFF (24u)

/** \brief  Length for Ifx_ETH_ACCEN0_Bits.EN25 */
#define IFX_ETH_ACCEN0_EN25_LEN (1u)

/** \brief  Mask for Ifx_ETH_ACCEN0_Bits.EN25 */
#define IFX_ETH_ACCEN0_EN25_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_ACCEN0_Bits.EN25 */
#define IFX_ETH_ACCEN0_EN25_OFF (25u)

/** \brief  Length for Ifx_ETH_ACCEN0_Bits.EN26 */
#define IFX_ETH_ACCEN0_EN26_LEN (1u)

/** \brief  Mask for Ifx_ETH_ACCEN0_Bits.EN26 */
#define IFX_ETH_ACCEN0_EN26_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_ACCEN0_Bits.EN26 */
#define IFX_ETH_ACCEN0_EN26_OFF (26u)

/** \brief  Length for Ifx_ETH_ACCEN0_Bits.EN27 */
#define IFX_ETH_ACCEN0_EN27_LEN (1u)

/** \brief  Mask for Ifx_ETH_ACCEN0_Bits.EN27 */
#define IFX_ETH_ACCEN0_EN27_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_ACCEN0_Bits.EN27 */
#define IFX_ETH_ACCEN0_EN27_OFF (27u)

/** \brief  Length for Ifx_ETH_ACCEN0_Bits.EN28 */
#define IFX_ETH_ACCEN0_EN28_LEN (1u)

/** \brief  Mask for Ifx_ETH_ACCEN0_Bits.EN28 */
#define IFX_ETH_ACCEN0_EN28_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_ACCEN0_Bits.EN28 */
#define IFX_ETH_ACCEN0_EN28_OFF (28u)

/** \brief  Length for Ifx_ETH_ACCEN0_Bits.EN29 */
#define IFX_ETH_ACCEN0_EN29_LEN (1u)

/** \brief  Mask for Ifx_ETH_ACCEN0_Bits.EN29 */
#define IFX_ETH_ACCEN0_EN29_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_ACCEN0_Bits.EN29 */
#define IFX_ETH_ACCEN0_EN29_OFF (29u)

/** \brief  Length for Ifx_ETH_ACCEN0_Bits.EN2 */
#define IFX_ETH_ACCEN0_EN2_LEN (1u)

/** \brief  Mask for Ifx_ETH_ACCEN0_Bits.EN2 */
#define IFX_ETH_ACCEN0_EN2_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_ACCEN0_Bits.EN2 */
#define IFX_ETH_ACCEN0_EN2_OFF (2u)

/** \brief  Length for Ifx_ETH_ACCEN0_Bits.EN30 */
#define IFX_ETH_ACCEN0_EN30_LEN (1u)

/** \brief  Mask for Ifx_ETH_ACCEN0_Bits.EN30 */
#define IFX_ETH_ACCEN0_EN30_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_ACCEN0_Bits.EN30 */
#define IFX_ETH_ACCEN0_EN30_OFF (30u)

/** \brief  Length for Ifx_ETH_ACCEN0_Bits.EN31 */
#define IFX_ETH_ACCEN0_EN31_LEN (1u)

/** \brief  Mask for Ifx_ETH_ACCEN0_Bits.EN31 */
#define IFX_ETH_ACCEN0_EN31_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_ACCEN0_Bits.EN31 */
#define IFX_ETH_ACCEN0_EN31_OFF (31u)

/** \brief  Length for Ifx_ETH_ACCEN0_Bits.EN3 */
#define IFX_ETH_ACCEN0_EN3_LEN (1u)

/** \brief  Mask for Ifx_ETH_ACCEN0_Bits.EN3 */
#define IFX_ETH_ACCEN0_EN3_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_ACCEN0_Bits.EN3 */
#define IFX_ETH_ACCEN0_EN3_OFF (3u)

/** \brief  Length for Ifx_ETH_ACCEN0_Bits.EN4 */
#define IFX_ETH_ACCEN0_EN4_LEN (1u)

/** \brief  Mask for Ifx_ETH_ACCEN0_Bits.EN4 */
#define IFX_ETH_ACCEN0_EN4_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_ACCEN0_Bits.EN4 */
#define IFX_ETH_ACCEN0_EN4_OFF (4u)

/** \brief  Length for Ifx_ETH_ACCEN0_Bits.EN5 */
#define IFX_ETH_ACCEN0_EN5_LEN (1u)

/** \brief  Mask for Ifx_ETH_ACCEN0_Bits.EN5 */
#define IFX_ETH_ACCEN0_EN5_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_ACCEN0_Bits.EN5 */
#define IFX_ETH_ACCEN0_EN5_OFF (5u)

/** \brief  Length for Ifx_ETH_ACCEN0_Bits.EN6 */
#define IFX_ETH_ACCEN0_EN6_LEN (1u)

/** \brief  Mask for Ifx_ETH_ACCEN0_Bits.EN6 */
#define IFX_ETH_ACCEN0_EN6_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_ACCEN0_Bits.EN6 */
#define IFX_ETH_ACCEN0_EN6_OFF (6u)

/** \brief  Length for Ifx_ETH_ACCEN0_Bits.EN7 */
#define IFX_ETH_ACCEN0_EN7_LEN (1u)

/** \brief  Mask for Ifx_ETH_ACCEN0_Bits.EN7 */
#define IFX_ETH_ACCEN0_EN7_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_ACCEN0_Bits.EN7 */
#define IFX_ETH_ACCEN0_EN7_OFF (7u)

/** \brief  Length for Ifx_ETH_ACCEN0_Bits.EN8 */
#define IFX_ETH_ACCEN0_EN8_LEN (1u)

/** \brief  Mask for Ifx_ETH_ACCEN0_Bits.EN8 */
#define IFX_ETH_ACCEN0_EN8_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_ACCEN0_Bits.EN8 */
#define IFX_ETH_ACCEN0_EN8_OFF (8u)

/** \brief  Length for Ifx_ETH_ACCEN0_Bits.EN9 */
#define IFX_ETH_ACCEN0_EN9_LEN (1u)

/** \brief  Mask for Ifx_ETH_ACCEN0_Bits.EN9 */
#define IFX_ETH_ACCEN0_EN9_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_ACCEN0_Bits.EN9 */
#define IFX_ETH_ACCEN0_EN9_OFF (9u)

/** \brief  Length for Ifx_ETH_AHB_OR_AXI_STATUS_Bits.AXIRDSTS */
#define IFX_ETH_AHB_OR_AXI_STATUS_AXIRDSTS_LEN (1u)

/** \brief  Mask for Ifx_ETH_AHB_OR_AXI_STATUS_Bits.AXIRDSTS */
#define IFX_ETH_AHB_OR_AXI_STATUS_AXIRDSTS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_AHB_OR_AXI_STATUS_Bits.AXIRDSTS */
#define IFX_ETH_AHB_OR_AXI_STATUS_AXIRDSTS_OFF (1u)

/** \brief  Length for Ifx_ETH_AHB_OR_AXI_STATUS_Bits.AXWHSTS */
#define IFX_ETH_AHB_OR_AXI_STATUS_AXWHSTS_LEN (1u)

/** \brief  Mask for Ifx_ETH_AHB_OR_AXI_STATUS_Bits.AXWHSTS */
#define IFX_ETH_AHB_OR_AXI_STATUS_AXWHSTS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_AHB_OR_AXI_STATUS_Bits.AXWHSTS */
#define IFX_ETH_AHB_OR_AXI_STATUS_AXWHSTS_OFF (0u)

/** \brief  Length for Ifx_ETH_BUS_MODE_Bits.AAL */
#define IFX_ETH_BUS_MODE_AAL_LEN (1u)

/** \brief  Mask for Ifx_ETH_BUS_MODE_Bits.AAL */
#define IFX_ETH_BUS_MODE_AAL_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_BUS_MODE_Bits.AAL */
#define IFX_ETH_BUS_MODE_AAL_OFF (25u)

/** \brief  Length for Ifx_ETH_BUS_MODE_Bits.ATDS */
#define IFX_ETH_BUS_MODE_ATDS_LEN (1u)

/** \brief  Mask for Ifx_ETH_BUS_MODE_Bits.ATDS */
#define IFX_ETH_BUS_MODE_ATDS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_BUS_MODE_Bits.ATDS */
#define IFX_ETH_BUS_MODE_ATDS_OFF (7u)

/** \brief  Length for Ifx_ETH_BUS_MODE_Bits.DA */
#define IFX_ETH_BUS_MODE_DA_LEN (1u)

/** \brief  Mask for Ifx_ETH_BUS_MODE_Bits.DA */
#define IFX_ETH_BUS_MODE_DA_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_BUS_MODE_Bits.DA */
#define IFX_ETH_BUS_MODE_DA_OFF (1u)

/** \brief  Length for Ifx_ETH_BUS_MODE_Bits.DSL */
#define IFX_ETH_BUS_MODE_DSL_LEN (5u)

/** \brief  Mask for Ifx_ETH_BUS_MODE_Bits.DSL */
#define IFX_ETH_BUS_MODE_DSL_MSK (0x1fu)

/** \brief  Offset for Ifx_ETH_BUS_MODE_Bits.DSL */
#define IFX_ETH_BUS_MODE_DSL_OFF (2u)

/** \brief  Length for Ifx_ETH_BUS_MODE_Bits.FB */
#define IFX_ETH_BUS_MODE_FB_LEN (1u)

/** \brief  Mask for Ifx_ETH_BUS_MODE_Bits.FB */
#define IFX_ETH_BUS_MODE_FB_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_BUS_MODE_Bits.FB */
#define IFX_ETH_BUS_MODE_FB_OFF (16u)

/** \brief  Length for Ifx_ETH_BUS_MODE_Bits.MB */
#define IFX_ETH_BUS_MODE_MB_LEN (1u)

/** \brief  Mask for Ifx_ETH_BUS_MODE_Bits.MB */
#define IFX_ETH_BUS_MODE_MB_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_BUS_MODE_Bits.MB */
#define IFX_ETH_BUS_MODE_MB_OFF (26u)

/** \brief  Length for Ifx_ETH_BUS_MODE_Bits.PBL */
#define IFX_ETH_BUS_MODE_PBL_LEN (6u)

/** \brief  Mask for Ifx_ETH_BUS_MODE_Bits.PBL */
#define IFX_ETH_BUS_MODE_PBL_MSK (0x3fu)

/** \brief  Offset for Ifx_ETH_BUS_MODE_Bits.PBL */
#define IFX_ETH_BUS_MODE_PBL_OFF (8u)

/** \brief  Length for Ifx_ETH_BUS_MODE_Bits.PBLx8 */
#define IFX_ETH_BUS_MODE_PBLX8_LEN (1u)

/** \brief  Mask for Ifx_ETH_BUS_MODE_Bits.PBLx8 */
#define IFX_ETH_BUS_MODE_PBLX8_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_BUS_MODE_Bits.PBLx8 */
#define IFX_ETH_BUS_MODE_PBLX8_OFF (24u)

/** \brief  Length for Ifx_ETH_BUS_MODE_Bits.PR */
#define IFX_ETH_BUS_MODE_PR_LEN (2u)

/** \brief  Mask for Ifx_ETH_BUS_MODE_Bits.PR */
#define IFX_ETH_BUS_MODE_PR_MSK (0x3u)

/** \brief  Offset for Ifx_ETH_BUS_MODE_Bits.PR */
#define IFX_ETH_BUS_MODE_PR_OFF (14u)

/** \brief  Length for Ifx_ETH_BUS_MODE_Bits.PRWG */
#define IFX_ETH_BUS_MODE_PRWG_LEN (2u)

/** \brief  Mask for Ifx_ETH_BUS_MODE_Bits.PRWG */
#define IFX_ETH_BUS_MODE_PRWG_MSK (0x3u)

/** \brief  Offset for Ifx_ETH_BUS_MODE_Bits.PRWG */
#define IFX_ETH_BUS_MODE_PRWG_OFF (28u)

/** \brief  Length for Ifx_ETH_BUS_MODE_Bits.RPBL */
#define IFX_ETH_BUS_MODE_RPBL_LEN (6u)

/** \brief  Mask for Ifx_ETH_BUS_MODE_Bits.RPBL */
#define IFX_ETH_BUS_MODE_RPBL_MSK (0x3fu)

/** \brief  Offset for Ifx_ETH_BUS_MODE_Bits.RPBL */
#define IFX_ETH_BUS_MODE_RPBL_OFF (17u)

/** \brief  Length for Ifx_ETH_BUS_MODE_Bits.SWR */
#define IFX_ETH_BUS_MODE_SWR_LEN (1u)

/** \brief  Mask for Ifx_ETH_BUS_MODE_Bits.SWR */
#define IFX_ETH_BUS_MODE_SWR_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_BUS_MODE_Bits.SWR */
#define IFX_ETH_BUS_MODE_SWR_OFF (0u)

/** \brief  Length for Ifx_ETH_BUS_MODE_Bits.TXPR */
#define IFX_ETH_BUS_MODE_TXPR_LEN (1u)

/** \brief  Mask for Ifx_ETH_BUS_MODE_Bits.TXPR */
#define IFX_ETH_BUS_MODE_TXPR_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_BUS_MODE_Bits.TXPR */
#define IFX_ETH_BUS_MODE_TXPR_OFF (27u)

/** \brief  Length for Ifx_ETH_BUS_MODE_Bits.USP */
#define IFX_ETH_BUS_MODE_USP_LEN (1u)

/** \brief  Mask for Ifx_ETH_BUS_MODE_Bits.USP */
#define IFX_ETH_BUS_MODE_USP_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_BUS_MODE_Bits.USP */
#define IFX_ETH_BUS_MODE_USP_OFF (23u)

/** \brief  Length for Ifx_ETH_CLC_Bits.DISR */
#define IFX_ETH_CLC_DISR_LEN (1u)

/** \brief  Mask for Ifx_ETH_CLC_Bits.DISR */
#define IFX_ETH_CLC_DISR_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_CLC_Bits.DISR */
#define IFX_ETH_CLC_DISR_OFF (0u)

/** \brief  Length for Ifx_ETH_CLC_Bits.DISS */
#define IFX_ETH_CLC_DISS_LEN (1u)

/** \brief  Mask for Ifx_ETH_CLC_Bits.DISS */
#define IFX_ETH_CLC_DISS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_CLC_Bits.DISS */
#define IFX_ETH_CLC_DISS_OFF (1u)

/** \brief  Length for
 * Ifx_ETH_CURRENT_HOST_RECEIVE_BUFFER_ADDRESS_Bits.CURRBUFAPTR */
#define IFX_ETH_CURRENT_HOST_RECEIVE_BUFFER_ADDRESS_CURRBUFAPTR_LEN (32u)

/** \brief  Mask for
 * Ifx_ETH_CURRENT_HOST_RECEIVE_BUFFER_ADDRESS_Bits.CURRBUFAPTR */
#define IFX_ETH_CURRENT_HOST_RECEIVE_BUFFER_ADDRESS_CURRBUFAPTR_MSK (0xffffffffu)

/** \brief  Offset for
 * Ifx_ETH_CURRENT_HOST_RECEIVE_BUFFER_ADDRESS_Bits.CURRBUFAPTR */
#define IFX_ETH_CURRENT_HOST_RECEIVE_BUFFER_ADDRESS_CURRBUFAPTR_OFF (0u)

/** \brief  Length for Ifx_ETH_CURRENT_HOST_RECEIVE_DESCRIPTOR_Bits.CURRDESAPTR */
#define IFX_ETH_CURRENT_HOST_RECEIVE_DESCRIPTOR_CURRDESAPTR_LEN (32u)

/** \brief  Mask for Ifx_ETH_CURRENT_HOST_RECEIVE_DESCRIPTOR_Bits.CURRDESAPTR */
#define IFX_ETH_CURRENT_HOST_RECEIVE_DESCRIPTOR_CURRDESAPTR_MSK (0xffffffffu)

/** \brief  Offset for Ifx_ETH_CURRENT_HOST_RECEIVE_DESCRIPTOR_Bits.CURRDESAPTR */
#define IFX_ETH_CURRENT_HOST_RECEIVE_DESCRIPTOR_CURRDESAPTR_OFF (0u)

/** \brief  Length for
 * Ifx_ETH_CURRENT_HOST_TRANSMIT_BUFFER_ADDRESS_Bits.CURTBUFAPTR */
#define IFX_ETH_CURRENT_HOST_TRANSMIT_BUFFER_ADDRESS_CURTBUFAPTR_LEN (32u)

/** \brief  Mask for
 * Ifx_ETH_CURRENT_HOST_TRANSMIT_BUFFER_ADDRESS_Bits.CURTBUFAPTR */
#define IFX_ETH_CURRENT_HOST_TRANSMIT_BUFFER_ADDRESS_CURTBUFAPTR_MSK (0xffffffffu)

/** \brief  Offset for
 * Ifx_ETH_CURRENT_HOST_TRANSMIT_BUFFER_ADDRESS_Bits.CURTBUFAPTR */
#define IFX_ETH_CURRENT_HOST_TRANSMIT_BUFFER_ADDRESS_CURTBUFAPTR_OFF (0u)

/** \brief  Length for Ifx_ETH_CURRENT_HOST_TRANSMIT_DESCRIPTOR_Bits.CURTDESAPTR */
#define IFX_ETH_CURRENT_HOST_TRANSMIT_DESCRIPTOR_CURTDESAPTR_LEN (32u)

/** \brief  Mask for Ifx_ETH_CURRENT_HOST_TRANSMIT_DESCRIPTOR_Bits.CURTDESAPTR */
#define IFX_ETH_CURRENT_HOST_TRANSMIT_DESCRIPTOR_CURTDESAPTR_MSK (0xffffffffu)

/** \brief  Offset for Ifx_ETH_CURRENT_HOST_TRANSMIT_DESCRIPTOR_Bits.CURTDESAPTR */
#define IFX_ETH_CURRENT_HOST_TRANSMIT_DESCRIPTOR_CURTDESAPTR_OFF (0u)

/** \brief  Length for Ifx_ETH_DEBUG_Bits.RFCFCSTS */
#define IFX_ETH_DEBUG_RFCFCSTS_LEN (2u)

/** \brief  Mask for Ifx_ETH_DEBUG_Bits.RFCFCSTS */
#define IFX_ETH_DEBUG_RFCFCSTS_MSK (0x3u)

/** \brief  Offset for Ifx_ETH_DEBUG_Bits.RFCFCSTS */
#define IFX_ETH_DEBUG_RFCFCSTS_OFF (1u)

/** \brief  Length for Ifx_ETH_DEBUG_Bits.RPESTS */
#define IFX_ETH_DEBUG_RPESTS_LEN (1u)

/** \brief  Mask for Ifx_ETH_DEBUG_Bits.RPESTS */
#define IFX_ETH_DEBUG_RPESTS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_DEBUG_Bits.RPESTS */
#define IFX_ETH_DEBUG_RPESTS_OFF (0u)

/** \brief  Length for Ifx_ETH_DEBUG_Bits.RRCSTS */
#define IFX_ETH_DEBUG_RRCSTS_LEN (2u)

/** \brief  Mask for Ifx_ETH_DEBUG_Bits.RRCSTS */
#define IFX_ETH_DEBUG_RRCSTS_MSK (0x3u)

/** \brief  Offset for Ifx_ETH_DEBUG_Bits.RRCSTS */
#define IFX_ETH_DEBUG_RRCSTS_OFF (5u)

/** \brief  Length for Ifx_ETH_DEBUG_Bits.RWCSTS */
#define IFX_ETH_DEBUG_RWCSTS_LEN (1u)

/** \brief  Mask for Ifx_ETH_DEBUG_Bits.RWCSTS */
#define IFX_ETH_DEBUG_RWCSTS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_DEBUG_Bits.RWCSTS */
#define IFX_ETH_DEBUG_RWCSTS_OFF (4u)

/** \brief  Length for Ifx_ETH_DEBUG_Bits.RXFSTS */
#define IFX_ETH_DEBUG_RXFSTS_LEN (2u)

/** \brief  Mask for Ifx_ETH_DEBUG_Bits.RXFSTS */
#define IFX_ETH_DEBUG_RXFSTS_MSK (0x3u)

/** \brief  Offset for Ifx_ETH_DEBUG_Bits.RXFSTS */
#define IFX_ETH_DEBUG_RXFSTS_OFF (8u)

/** \brief  Length for Ifx_ETH_DEBUG_Bits.TFCSTS */
#define IFX_ETH_DEBUG_TFCSTS_LEN (2u)

/** \brief  Mask for Ifx_ETH_DEBUG_Bits.TFCSTS */
#define IFX_ETH_DEBUG_TFCSTS_MSK (0x3u)

/** \brief  Offset for Ifx_ETH_DEBUG_Bits.TFCSTS */
#define IFX_ETH_DEBUG_TFCSTS_OFF (17u)

/** \brief  Length for Ifx_ETH_DEBUG_Bits.TPESTS */
#define IFX_ETH_DEBUG_TPESTS_LEN (1u)

/** \brief  Mask for Ifx_ETH_DEBUG_Bits.TPESTS */
#define IFX_ETH_DEBUG_TPESTS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_DEBUG_Bits.TPESTS */
#define IFX_ETH_DEBUG_TPESTS_OFF (16u)

/** \brief  Length for Ifx_ETH_DEBUG_Bits.TRCSTS */
#define IFX_ETH_DEBUG_TRCSTS_LEN (2u)

/** \brief  Mask for Ifx_ETH_DEBUG_Bits.TRCSTS */
#define IFX_ETH_DEBUG_TRCSTS_MSK (0x3u)

/** \brief  Offset for Ifx_ETH_DEBUG_Bits.TRCSTS */
#define IFX_ETH_DEBUG_TRCSTS_OFF (20u)

/** \brief  Length for Ifx_ETH_DEBUG_Bits.TWCSTS */
#define IFX_ETH_DEBUG_TWCSTS_LEN (1u)

/** \brief  Mask for Ifx_ETH_DEBUG_Bits.TWCSTS */
#define IFX_ETH_DEBUG_TWCSTS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_DEBUG_Bits.TWCSTS */
#define IFX_ETH_DEBUG_TWCSTS_OFF (22u)

/** \brief  Length for Ifx_ETH_DEBUG_Bits.TXFSTS */
#define IFX_ETH_DEBUG_TXFSTS_LEN (1u)

/** \brief  Mask for Ifx_ETH_DEBUG_Bits.TXFSTS */
#define IFX_ETH_DEBUG_TXFSTS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_DEBUG_Bits.TXFSTS */
#define IFX_ETH_DEBUG_TXFSTS_OFF (24u)

/** \brief  Length for Ifx_ETH_DEBUG_Bits.TXPAUSED */
#define IFX_ETH_DEBUG_TXPAUSED_LEN (1u)

/** \brief  Mask for Ifx_ETH_DEBUG_Bits.TXPAUSED */
#define IFX_ETH_DEBUG_TXPAUSED_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_DEBUG_Bits.TXPAUSED */
#define IFX_ETH_DEBUG_TXPAUSED_OFF (19u)

/** \brief  Length for Ifx_ETH_DEBUG_Bits.TXSTSFSTS */
#define IFX_ETH_DEBUG_TXSTSFSTS_LEN (1u)

/** \brief  Mask for Ifx_ETH_DEBUG_Bits.TXSTSFSTS */
#define IFX_ETH_DEBUG_TXSTSFSTS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_DEBUG_Bits.TXSTSFSTS */
#define IFX_ETH_DEBUG_TXSTSFSTS_OFF (25u)

/** \brief  Length for Ifx_ETH_FLOW_CONTROL_Bits.DZPQ */
#define IFX_ETH_FLOW_CONTROL_DZPQ_LEN (1u)

/** \brief  Mask for Ifx_ETH_FLOW_CONTROL_Bits.DZPQ */
#define IFX_ETH_FLOW_CONTROL_DZPQ_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_FLOW_CONTROL_Bits.DZPQ */
#define IFX_ETH_FLOW_CONTROL_DZPQ_OFF (7u)

/** \brief  Length for Ifx_ETH_FLOW_CONTROL_Bits.FCA_BPA */
#define IFX_ETH_FLOW_CONTROL_FCA_BPA_LEN (1u)

/** \brief  Mask for Ifx_ETH_FLOW_CONTROL_Bits.FCA_BPA */
#define IFX_ETH_FLOW_CONTROL_FCA_BPA_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_FLOW_CONTROL_Bits.FCA_BPA */
#define IFX_ETH_FLOW_CONTROL_FCA_BPA_OFF (0u)

/** \brief  Length for Ifx_ETH_FLOW_CONTROL_Bits.PLT */
#define IFX_ETH_FLOW_CONTROL_PLT_LEN (2u)

/** \brief  Mask for Ifx_ETH_FLOW_CONTROL_Bits.PLT */
#define IFX_ETH_FLOW_CONTROL_PLT_MSK (0x3u)

/** \brief  Offset for Ifx_ETH_FLOW_CONTROL_Bits.PLT */
#define IFX_ETH_FLOW_CONTROL_PLT_OFF (4u)

/** \brief  Length for Ifx_ETH_FLOW_CONTROL_Bits.PT */
#define IFX_ETH_FLOW_CONTROL_PT_LEN (16u)

/** \brief  Mask for Ifx_ETH_FLOW_CONTROL_Bits.PT */
#define IFX_ETH_FLOW_CONTROL_PT_MSK (0xffffu)

/** \brief  Offset for Ifx_ETH_FLOW_CONTROL_Bits.PT */
#define IFX_ETH_FLOW_CONTROL_PT_OFF (16u)

/** \brief  Length for Ifx_ETH_FLOW_CONTROL_Bits.RFE */
#define IFX_ETH_FLOW_CONTROL_RFE_LEN (1u)

/** \brief  Mask for Ifx_ETH_FLOW_CONTROL_Bits.RFE */
#define IFX_ETH_FLOW_CONTROL_RFE_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_FLOW_CONTROL_Bits.RFE */
#define IFX_ETH_FLOW_CONTROL_RFE_OFF (2u)

/** \brief  Length for Ifx_ETH_FLOW_CONTROL_Bits.TFE */
#define IFX_ETH_FLOW_CONTROL_TFE_LEN (1u)

/** \brief  Mask for Ifx_ETH_FLOW_CONTROL_Bits.TFE */
#define IFX_ETH_FLOW_CONTROL_TFE_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_FLOW_CONTROL_Bits.TFE */
#define IFX_ETH_FLOW_CONTROL_TFE_OFF (1u)

/** \brief  Length for Ifx_ETH_FLOW_CONTROL_Bits.UP */
#define IFX_ETH_FLOW_CONTROL_UP_LEN (1u)

/** \brief  Mask for Ifx_ETH_FLOW_CONTROL_Bits.UP */
#define IFX_ETH_FLOW_CONTROL_UP_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_FLOW_CONTROL_Bits.UP */
#define IFX_ETH_FLOW_CONTROL_UP_OFF (3u)

/** \brief  Length for Ifx_ETH_GMII_ADDRESS_Bits.CR */
#define IFX_ETH_GMII_ADDRESS_CR_LEN (4u)

/** \brief  Mask for Ifx_ETH_GMII_ADDRESS_Bits.CR */
#define IFX_ETH_GMII_ADDRESS_CR_MSK (0xfu)

/** \brief  Offset for Ifx_ETH_GMII_ADDRESS_Bits.CR */
#define IFX_ETH_GMII_ADDRESS_CR_OFF (2u)

/** \brief  Length for Ifx_ETH_GMII_ADDRESS_Bits.GB */
#define IFX_ETH_GMII_ADDRESS_GB_LEN (1u)

/** \brief  Mask for Ifx_ETH_GMII_ADDRESS_Bits.GB */
#define IFX_ETH_GMII_ADDRESS_GB_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_GMII_ADDRESS_Bits.GB */
#define IFX_ETH_GMII_ADDRESS_GB_OFF (0u)

/** \brief  Length for Ifx_ETH_GMII_ADDRESS_Bits.GR */
#define IFX_ETH_GMII_ADDRESS_GR_LEN (5u)

/** \brief  Mask for Ifx_ETH_GMII_ADDRESS_Bits.GR */
#define IFX_ETH_GMII_ADDRESS_GR_MSK (0x1fu)

/** \brief  Offset for Ifx_ETH_GMII_ADDRESS_Bits.GR */
#define IFX_ETH_GMII_ADDRESS_GR_OFF (6u)

/** \brief  Length for Ifx_ETH_GMII_ADDRESS_Bits.GW */
#define IFX_ETH_GMII_ADDRESS_GW_LEN (1u)

/** \brief  Mask for Ifx_ETH_GMII_ADDRESS_Bits.GW */
#define IFX_ETH_GMII_ADDRESS_GW_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_GMII_ADDRESS_Bits.GW */
#define IFX_ETH_GMII_ADDRESS_GW_OFF (1u)

/** \brief  Length for Ifx_ETH_GMII_ADDRESS_Bits.PA */
#define IFX_ETH_GMII_ADDRESS_PA_LEN (5u)

/** \brief  Mask for Ifx_ETH_GMII_ADDRESS_Bits.PA */
#define IFX_ETH_GMII_ADDRESS_PA_MSK (0x1fu)

/** \brief  Offset for Ifx_ETH_GMII_ADDRESS_Bits.PA */
#define IFX_ETH_GMII_ADDRESS_PA_OFF (11u)

/** \brief  Length for Ifx_ETH_GMII_DATA_Bits.GD */
#define IFX_ETH_GMII_DATA_GD_LEN (16u)

/** \brief  Mask for Ifx_ETH_GMII_DATA_Bits.GD */
#define IFX_ETH_GMII_DATA_GD_MSK (0xffffu)

/** \brief  Offset for Ifx_ETH_GMII_DATA_Bits.GD */
#define IFX_ETH_GMII_DATA_GD_OFF (0u)

/** \brief  Length for Ifx_ETH_GPCTL_Bits.ALTI0 */
#define IFX_ETH_GPCTL_ALTI0_LEN (2u)

/** \brief  Mask for Ifx_ETH_GPCTL_Bits.ALTI0 */
#define IFX_ETH_GPCTL_ALTI0_MSK (0x3u)

/** \brief  Offset for Ifx_ETH_GPCTL_Bits.ALTI0 */
#define IFX_ETH_GPCTL_ALTI0_OFF (0u)

/** \brief  Length for Ifx_ETH_GPCTL_Bits.ALTI10 */
#define IFX_ETH_GPCTL_ALTI10_LEN (2u)

/** \brief  Mask for Ifx_ETH_GPCTL_Bits.ALTI10 */
#define IFX_ETH_GPCTL_ALTI10_MSK (0x3u)

/** \brief  Offset for Ifx_ETH_GPCTL_Bits.ALTI10 */
#define IFX_ETH_GPCTL_ALTI10_OFF (20u)

/** \brief  Length for Ifx_ETH_GPCTL_Bits.ALTI1 */
#define IFX_ETH_GPCTL_ALTI1_LEN (2u)

/** \brief  Mask for Ifx_ETH_GPCTL_Bits.ALTI1 */
#define IFX_ETH_GPCTL_ALTI1_MSK (0x3u)

/** \brief  Offset for Ifx_ETH_GPCTL_Bits.ALTI1 */
#define IFX_ETH_GPCTL_ALTI1_OFF (2u)

/** \brief  Length for Ifx_ETH_GPCTL_Bits.ALTI2 */
#define IFX_ETH_GPCTL_ALTI2_LEN (2u)

/** \brief  Mask for Ifx_ETH_GPCTL_Bits.ALTI2 */
#define IFX_ETH_GPCTL_ALTI2_MSK (0x3u)

/** \brief  Offset for Ifx_ETH_GPCTL_Bits.ALTI2 */
#define IFX_ETH_GPCTL_ALTI2_OFF (4u)

/** \brief  Length for Ifx_ETH_GPCTL_Bits.ALTI3 */
#define IFX_ETH_GPCTL_ALTI3_LEN (2u)

/** \brief  Mask for Ifx_ETH_GPCTL_Bits.ALTI3 */
#define IFX_ETH_GPCTL_ALTI3_MSK (0x3u)

/** \brief  Offset for Ifx_ETH_GPCTL_Bits.ALTI3 */
#define IFX_ETH_GPCTL_ALTI3_OFF (6u)

/** \brief  Length for Ifx_ETH_GPCTL_Bits.ALTI4 */
#define IFX_ETH_GPCTL_ALTI4_LEN (2u)

/** \brief  Mask for Ifx_ETH_GPCTL_Bits.ALTI4 */
#define IFX_ETH_GPCTL_ALTI4_MSK (0x3u)

/** \brief  Offset for Ifx_ETH_GPCTL_Bits.ALTI4 */
#define IFX_ETH_GPCTL_ALTI4_OFF (8u)

/** \brief  Length for Ifx_ETH_GPCTL_Bits.ALTI5 */
#define IFX_ETH_GPCTL_ALTI5_LEN (2u)

/** \brief  Mask for Ifx_ETH_GPCTL_Bits.ALTI5 */
#define IFX_ETH_GPCTL_ALTI5_MSK (0x3u)

/** \brief  Offset for Ifx_ETH_GPCTL_Bits.ALTI5 */
#define IFX_ETH_GPCTL_ALTI5_OFF (10u)

/** \brief  Length for Ifx_ETH_GPCTL_Bits.ALTI6 */
#define IFX_ETH_GPCTL_ALTI6_LEN (2u)

/** \brief  Mask for Ifx_ETH_GPCTL_Bits.ALTI6 */
#define IFX_ETH_GPCTL_ALTI6_MSK (0x3u)

/** \brief  Offset for Ifx_ETH_GPCTL_Bits.ALTI6 */
#define IFX_ETH_GPCTL_ALTI6_OFF (12u)

/** \brief  Length for Ifx_ETH_GPCTL_Bits.ALTI7 */
#define IFX_ETH_GPCTL_ALTI7_LEN (2u)

/** \brief  Mask for Ifx_ETH_GPCTL_Bits.ALTI7 */
#define IFX_ETH_GPCTL_ALTI7_MSK (0x3u)

/** \brief  Offset for Ifx_ETH_GPCTL_Bits.ALTI7 */
#define IFX_ETH_GPCTL_ALTI7_OFF (14u)

/** \brief  Length for Ifx_ETH_GPCTL_Bits.ALTI8 */
#define IFX_ETH_GPCTL_ALTI8_LEN (2u)

/** \brief  Mask for Ifx_ETH_GPCTL_Bits.ALTI8 */
#define IFX_ETH_GPCTL_ALTI8_MSK (0x3u)

/** \brief  Offset for Ifx_ETH_GPCTL_Bits.ALTI8 */
#define IFX_ETH_GPCTL_ALTI8_OFF (16u)

/** \brief  Length for Ifx_ETH_GPCTL_Bits.ALTI9 */
#define IFX_ETH_GPCTL_ALTI9_LEN (2u)

/** \brief  Mask for Ifx_ETH_GPCTL_Bits.ALTI9 */
#define IFX_ETH_GPCTL_ALTI9_MSK (0x3u)

/** \brief  Offset for Ifx_ETH_GPCTL_Bits.ALTI9 */
#define IFX_ETH_GPCTL_ALTI9_OFF (18u)

/** \brief  Length for Ifx_ETH_GPCTL_Bits.DIV */
#define IFX_ETH_GPCTL_DIV_LEN (1u)

/** \brief  Mask for Ifx_ETH_GPCTL_Bits.DIV */
#define IFX_ETH_GPCTL_DIV_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_GPCTL_Bits.DIV */
#define IFX_ETH_GPCTL_DIV_OFF (25u)

/** \brief  Length for Ifx_ETH_GPCTL_Bits.EPR */
#define IFX_ETH_GPCTL_EPR_LEN (1u)

/** \brief  Mask for Ifx_ETH_GPCTL_Bits.EPR */
#define IFX_ETH_GPCTL_EPR_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_GPCTL_Bits.EPR */
#define IFX_ETH_GPCTL_EPR_OFF (24u)

/** \brief  Length for Ifx_ETH_HASH_TABLE_HIGH_Bits.HTH */
#define IFX_ETH_HASH_TABLE_HIGH_HTH_LEN (32u)

/** \brief  Mask for Ifx_ETH_HASH_TABLE_HIGH_Bits.HTH */
#define IFX_ETH_HASH_TABLE_HIGH_HTH_MSK (0xffffffffu)

/** \brief  Offset for Ifx_ETH_HASH_TABLE_HIGH_Bits.HTH */
#define IFX_ETH_HASH_TABLE_HIGH_HTH_OFF (0u)

/** \brief  Length for Ifx_ETH_HASH_TABLE_LOW_Bits.HTL */
#define IFX_ETH_HASH_TABLE_LOW_HTL_LEN (32u)

/** \brief  Mask for Ifx_ETH_HASH_TABLE_LOW_Bits.HTL */
#define IFX_ETH_HASH_TABLE_LOW_HTL_MSK (0xffffffffu)

/** \brief  Offset for Ifx_ETH_HASH_TABLE_LOW_Bits.HTL */
#define IFX_ETH_HASH_TABLE_LOW_HTL_OFF (0u)

/** \brief  Length for Ifx_ETH_HW_FEATURE_Bits.ACTPHYIF */
#define IFX_ETH_HW_FEATURE_ACTPHYIF_LEN (3u)

/** \brief  Mask for Ifx_ETH_HW_FEATURE_Bits.ACTPHYIF */
#define IFX_ETH_HW_FEATURE_ACTPHYIF_MSK (0x7u)

/** \brief  Offset for Ifx_ETH_HW_FEATURE_Bits.ACTPHYIF */
#define IFX_ETH_HW_FEATURE_ACTPHYIF_OFF (28u)

/** \brief  Length for Ifx_ETH_HW_FEATURE_Bits.ADDMACADRSEL */
#define IFX_ETH_HW_FEATURE_ADDMACADRSEL_LEN (1u)

/** \brief  Mask for Ifx_ETH_HW_FEATURE_Bits.ADDMACADRSEL */
#define IFX_ETH_HW_FEATURE_ADDMACADRSEL_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_HW_FEATURE_Bits.ADDMACADRSEL */
#define IFX_ETH_HW_FEATURE_ADDMACADRSEL_OFF (5u)

/** \brief  Length for Ifx_ETH_HW_FEATURE_Bits.AVSEL */
#define IFX_ETH_HW_FEATURE_AVSEL_LEN (1u)

/** \brief  Mask for Ifx_ETH_HW_FEATURE_Bits.AVSEL */
#define IFX_ETH_HW_FEATURE_AVSEL_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_HW_FEATURE_Bits.AVSEL */
#define IFX_ETH_HW_FEATURE_AVSEL_OFF (15u)

/** \brief  Length for Ifx_ETH_HW_FEATURE_Bits.EEESEL */
#define IFX_ETH_HW_FEATURE_EEESEL_LEN (1u)

/** \brief  Mask for Ifx_ETH_HW_FEATURE_Bits.EEESEL */
#define IFX_ETH_HW_FEATURE_EEESEL_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_HW_FEATURE_Bits.EEESEL */
#define IFX_ETH_HW_FEATURE_EEESEL_OFF (14u)

/** \brief  Length for Ifx_ETH_HW_FEATURE_Bits.ENHDESSEL */
#define IFX_ETH_HW_FEATURE_ENHDESSEL_LEN (1u)

/** \brief  Mask for Ifx_ETH_HW_FEATURE_Bits.ENHDESSEL */
#define IFX_ETH_HW_FEATURE_ENHDESSEL_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_HW_FEATURE_Bits.ENHDESSEL */
#define IFX_ETH_HW_FEATURE_ENHDESSEL_OFF (24u)

/** \brief  Length for Ifx_ETH_HW_FEATURE_Bits.EXTHASHEN */
#define IFX_ETH_HW_FEATURE_EXTHASHEN_LEN (1u)

/** \brief  Mask for Ifx_ETH_HW_FEATURE_Bits.EXTHASHEN */
#define IFX_ETH_HW_FEATURE_EXTHASHEN_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_HW_FEATURE_Bits.EXTHASHEN */
#define IFX_ETH_HW_FEATURE_EXTHASHEN_OFF (3u)

/** \brief  Length for Ifx_ETH_HW_FEATURE_Bits.FLEXIPPSEN */
#define IFX_ETH_HW_FEATURE_FLEXIPPSEN_LEN (1u)

/** \brief  Mask for Ifx_ETH_HW_FEATURE_Bits.FLEXIPPSEN */
#define IFX_ETH_HW_FEATURE_FLEXIPPSEN_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_HW_FEATURE_Bits.FLEXIPPSEN */
#define IFX_ETH_HW_FEATURE_FLEXIPPSEN_OFF (26u)

/** \brief  Length for Ifx_ETH_HW_FEATURE_Bits.GMIISEL */
#define IFX_ETH_HW_FEATURE_GMIISEL_LEN (1u)

/** \brief  Mask for Ifx_ETH_HW_FEATURE_Bits.GMIISEL */
#define IFX_ETH_HW_FEATURE_GMIISEL_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_HW_FEATURE_Bits.GMIISEL */
#define IFX_ETH_HW_FEATURE_GMIISEL_OFF (1u)

/** \brief  Length for Ifx_ETH_HW_FEATURE_Bits.HASHSEL */
#define IFX_ETH_HW_FEATURE_HASHSEL_LEN (1u)

/** \brief  Mask for Ifx_ETH_HW_FEATURE_Bits.HASHSEL */
#define IFX_ETH_HW_FEATURE_HASHSEL_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_HW_FEATURE_Bits.HASHSEL */
#define IFX_ETH_HW_FEATURE_HASHSEL_OFF (4u)

/** \brief  Length for Ifx_ETH_HW_FEATURE_Bits.HDSEL */
#define IFX_ETH_HW_FEATURE_HDSEL_LEN (1u)

/** \brief  Mask for Ifx_ETH_HW_FEATURE_Bits.HDSEL */
#define IFX_ETH_HW_FEATURE_HDSEL_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_HW_FEATURE_Bits.HDSEL */
#define IFX_ETH_HW_FEATURE_HDSEL_OFF (2u)

/** \brief  Length for Ifx_ETH_HW_FEATURE_Bits.INTTSEN */
#define IFX_ETH_HW_FEATURE_INTTSEN_LEN (1u)

/** \brief  Mask for Ifx_ETH_HW_FEATURE_Bits.INTTSEN */
#define IFX_ETH_HW_FEATURE_INTTSEN_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_HW_FEATURE_Bits.INTTSEN */
#define IFX_ETH_HW_FEATURE_INTTSEN_OFF (25u)

/** \brief  Length for Ifx_ETH_HW_FEATURE_Bits.L3L4FLTREN */
#define IFX_ETH_HW_FEATURE_L3L4FLTREN_LEN (1u)

/** \brief  Mask for Ifx_ETH_HW_FEATURE_Bits.L3L4FLTREN */
#define IFX_ETH_HW_FEATURE_L3L4FLTREN_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_HW_FEATURE_Bits.L3L4FLTREN */
#define IFX_ETH_HW_FEATURE_L3L4FLTREN_OFF (7u)

/** \brief  Length for Ifx_ETH_HW_FEATURE_Bits.MGKSEL */
#define IFX_ETH_HW_FEATURE_MGKSEL_LEN (1u)

/** \brief  Mask for Ifx_ETH_HW_FEATURE_Bits.MGKSEL */
#define IFX_ETH_HW_FEATURE_MGKSEL_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_HW_FEATURE_Bits.MGKSEL */
#define IFX_ETH_HW_FEATURE_MGKSEL_OFF (10u)

/** \brief  Length for Ifx_ETH_HW_FEATURE_Bits.MIISEL */
#define IFX_ETH_HW_FEATURE_MIISEL_LEN (1u)

/** \brief  Mask for Ifx_ETH_HW_FEATURE_Bits.MIISEL */
#define IFX_ETH_HW_FEATURE_MIISEL_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_HW_FEATURE_Bits.MIISEL */
#define IFX_ETH_HW_FEATURE_MIISEL_OFF (0u)

/** \brief  Length for Ifx_ETH_HW_FEATURE_Bits.MMCSEL */
#define IFX_ETH_HW_FEATURE_MMCSEL_LEN (1u)

/** \brief  Mask for Ifx_ETH_HW_FEATURE_Bits.MMCSEL */
#define IFX_ETH_HW_FEATURE_MMCSEL_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_HW_FEATURE_Bits.MMCSEL */
#define IFX_ETH_HW_FEATURE_MMCSEL_OFF (11u)

/** \brief  Length for Ifx_ETH_HW_FEATURE_Bits.PCSSEL */
#define IFX_ETH_HW_FEATURE_PCSSEL_LEN (1u)

/** \brief  Mask for Ifx_ETH_HW_FEATURE_Bits.PCSSEL */
#define IFX_ETH_HW_FEATURE_PCSSEL_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_HW_FEATURE_Bits.PCSSEL */
#define IFX_ETH_HW_FEATURE_PCSSEL_OFF (6u)

/** \brief  Length for Ifx_ETH_HW_FEATURE_Bits.RWKSEL */
#define IFX_ETH_HW_FEATURE_RWKSEL_LEN (1u)

/** \brief  Mask for Ifx_ETH_HW_FEATURE_Bits.RWKSEL */
#define IFX_ETH_HW_FEATURE_RWKSEL_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_HW_FEATURE_Bits.RWKSEL */
#define IFX_ETH_HW_FEATURE_RWKSEL_OFF (9u)

/** \brief  Length for Ifx_ETH_HW_FEATURE_Bits.RXCHCNT */
#define IFX_ETH_HW_FEATURE_RXCHCNT_LEN (2u)

/** \brief  Mask for Ifx_ETH_HW_FEATURE_Bits.RXCHCNT */
#define IFX_ETH_HW_FEATURE_RXCHCNT_MSK (0x3u)

/** \brief  Offset for Ifx_ETH_HW_FEATURE_Bits.RXCHCNT */
#define IFX_ETH_HW_FEATURE_RXCHCNT_OFF (20u)

/** \brief  Length for Ifx_ETH_HW_FEATURE_Bits.RXFIFOSIZE */
#define IFX_ETH_HW_FEATURE_RXFIFOSIZE_LEN (1u)

/** \brief  Mask for Ifx_ETH_HW_FEATURE_Bits.RXFIFOSIZE */
#define IFX_ETH_HW_FEATURE_RXFIFOSIZE_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_HW_FEATURE_Bits.RXFIFOSIZE */
#define IFX_ETH_HW_FEATURE_RXFIFOSIZE_OFF (19u)

/** \brief  Length for Ifx_ETH_HW_FEATURE_Bits.RXTYP1COE */
#define IFX_ETH_HW_FEATURE_RXTYP1COE_LEN (1u)

/** \brief  Mask for Ifx_ETH_HW_FEATURE_Bits.RXTYP1COE */
#define IFX_ETH_HW_FEATURE_RXTYP1COE_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_HW_FEATURE_Bits.RXTYP1COE */
#define IFX_ETH_HW_FEATURE_RXTYP1COE_OFF (17u)

/** \brief  Length for Ifx_ETH_HW_FEATURE_Bits.RXTYP2COE */
#define IFX_ETH_HW_FEATURE_RXTYP2COE_LEN (1u)

/** \brief  Mask for Ifx_ETH_HW_FEATURE_Bits.RXTYP2COE */
#define IFX_ETH_HW_FEATURE_RXTYP2COE_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_HW_FEATURE_Bits.RXTYP2COE */
#define IFX_ETH_HW_FEATURE_RXTYP2COE_OFF (18u)

/** \brief  Length for Ifx_ETH_HW_FEATURE_Bits.SAVLANINS */
#define IFX_ETH_HW_FEATURE_SAVLANINS_LEN (1u)

/** \brief  Mask for Ifx_ETH_HW_FEATURE_Bits.SAVLANINS */
#define IFX_ETH_HW_FEATURE_SAVLANINS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_HW_FEATURE_Bits.SAVLANINS */
#define IFX_ETH_HW_FEATURE_SAVLANINS_OFF (27u)

/** \brief  Length for Ifx_ETH_HW_FEATURE_Bits.SMASEL */
#define IFX_ETH_HW_FEATURE_SMASEL_LEN (1u)

/** \brief  Mask for Ifx_ETH_HW_FEATURE_Bits.SMASEL */
#define IFX_ETH_HW_FEATURE_SMASEL_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_HW_FEATURE_Bits.SMASEL */
#define IFX_ETH_HW_FEATURE_SMASEL_OFF (8u)

/** \brief  Length for Ifx_ETH_HW_FEATURE_Bits.TSVER1SEL */
#define IFX_ETH_HW_FEATURE_TSVER1SEL_LEN (1u)

/** \brief  Mask for Ifx_ETH_HW_FEATURE_Bits.TSVER1SEL */
#define IFX_ETH_HW_FEATURE_TSVER1SEL_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_HW_FEATURE_Bits.TSVER1SEL */
#define IFX_ETH_HW_FEATURE_TSVER1SEL_OFF (12u)

/** \brief  Length for Ifx_ETH_HW_FEATURE_Bits.TSVER2SEL */
#define IFX_ETH_HW_FEATURE_TSVER2SEL_LEN (1u)

/** \brief  Mask for Ifx_ETH_HW_FEATURE_Bits.TSVER2SEL */
#define IFX_ETH_HW_FEATURE_TSVER2SEL_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_HW_FEATURE_Bits.TSVER2SEL */
#define IFX_ETH_HW_FEATURE_TSVER2SEL_OFF (13u)

/** \brief  Length for Ifx_ETH_HW_FEATURE_Bits.TXCHCNT */
#define IFX_ETH_HW_FEATURE_TXCHCNT_LEN (2u)

/** \brief  Mask for Ifx_ETH_HW_FEATURE_Bits.TXCHCNT */
#define IFX_ETH_HW_FEATURE_TXCHCNT_MSK (0x3u)

/** \brief  Offset for Ifx_ETH_HW_FEATURE_Bits.TXCHCNT */
#define IFX_ETH_HW_FEATURE_TXCHCNT_OFF (22u)

/** \brief  Length for Ifx_ETH_HW_FEATURE_Bits.TXCOESEL */
#define IFX_ETH_HW_FEATURE_TXCOESEL_LEN (1u)

/** \brief  Mask for Ifx_ETH_HW_FEATURE_Bits.TXCOESEL */
#define IFX_ETH_HW_FEATURE_TXCOESEL_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_HW_FEATURE_Bits.TXCOESEL */
#define IFX_ETH_HW_FEATURE_TXCOESEL_OFF (16u)

/** \brief  Length for Ifx_ETH_ID_Bits.MODNUMBER */
#define IFX_ETH_ID_MODNUMBER_LEN (16u)

/** \brief  Mask for Ifx_ETH_ID_Bits.MODNUMBER */
#define IFX_ETH_ID_MODNUMBER_MSK (0xffffu)

/** \brief  Offset for Ifx_ETH_ID_Bits.MODNUMBER */
#define IFX_ETH_ID_MODNUMBER_OFF (16u)

/** \brief  Length for Ifx_ETH_ID_Bits.MODREV */
#define IFX_ETH_ID_MODREV_LEN (8u)

/** \brief  Mask for Ifx_ETH_ID_Bits.MODREV */
#define IFX_ETH_ID_MODREV_MSK (0xffu)

/** \brief  Offset for Ifx_ETH_ID_Bits.MODREV */
#define IFX_ETH_ID_MODREV_OFF (0u)

/** \brief  Length for Ifx_ETH_ID_Bits.MODTYPE */
#define IFX_ETH_ID_MODTYPE_LEN (8u)

/** \brief  Mask for Ifx_ETH_ID_Bits.MODTYPE */
#define IFX_ETH_ID_MODTYPE_MSK (0xffu)

/** \brief  Offset for Ifx_ETH_ID_Bits.MODTYPE */
#define IFX_ETH_ID_MODTYPE_OFF (8u)

/** \brief  Length for Ifx_ETH_INTERRUPT_ENABLE_Bits.AIE */
#define IFX_ETH_INTERRUPT_ENABLE_AIE_LEN (1u)

/** \brief  Mask for Ifx_ETH_INTERRUPT_ENABLE_Bits.AIE */
#define IFX_ETH_INTERRUPT_ENABLE_AIE_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_INTERRUPT_ENABLE_Bits.AIE */
#define IFX_ETH_INTERRUPT_ENABLE_AIE_OFF (15u)

/** \brief  Length for Ifx_ETH_INTERRUPT_ENABLE_Bits.ERE */
#define IFX_ETH_INTERRUPT_ENABLE_ERE_LEN (1u)

/** \brief  Mask for Ifx_ETH_INTERRUPT_ENABLE_Bits.ERE */
#define IFX_ETH_INTERRUPT_ENABLE_ERE_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_INTERRUPT_ENABLE_Bits.ERE */
#define IFX_ETH_INTERRUPT_ENABLE_ERE_OFF (14u)

/** \brief  Length for Ifx_ETH_INTERRUPT_ENABLE_Bits.ETE */
#define IFX_ETH_INTERRUPT_ENABLE_ETE_LEN (1u)

/** \brief  Mask for Ifx_ETH_INTERRUPT_ENABLE_Bits.ETE */
#define IFX_ETH_INTERRUPT_ENABLE_ETE_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_INTERRUPT_ENABLE_Bits.ETE */
#define IFX_ETH_INTERRUPT_ENABLE_ETE_OFF (10u)

/** \brief  Length for Ifx_ETH_INTERRUPT_ENABLE_Bits.FBE */
#define IFX_ETH_INTERRUPT_ENABLE_FBE_LEN (1u)

/** \brief  Mask for Ifx_ETH_INTERRUPT_ENABLE_Bits.FBE */
#define IFX_ETH_INTERRUPT_ENABLE_FBE_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_INTERRUPT_ENABLE_Bits.FBE */
#define IFX_ETH_INTERRUPT_ENABLE_FBE_OFF (13u)

/** \brief  Length for Ifx_ETH_INTERRUPT_ENABLE_Bits.NIE */
#define IFX_ETH_INTERRUPT_ENABLE_NIE_LEN (1u)

/** \brief  Mask for Ifx_ETH_INTERRUPT_ENABLE_Bits.NIE */
#define IFX_ETH_INTERRUPT_ENABLE_NIE_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_INTERRUPT_ENABLE_Bits.NIE */
#define IFX_ETH_INTERRUPT_ENABLE_NIE_OFF (16u)

/** \brief  Length for Ifx_ETH_INTERRUPT_ENABLE_Bits.OVE */
#define IFX_ETH_INTERRUPT_ENABLE_OVE_LEN (1u)

/** \brief  Mask for Ifx_ETH_INTERRUPT_ENABLE_Bits.OVE */
#define IFX_ETH_INTERRUPT_ENABLE_OVE_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_INTERRUPT_ENABLE_Bits.OVE */
#define IFX_ETH_INTERRUPT_ENABLE_OVE_OFF (4u)

/** \brief  Length for Ifx_ETH_INTERRUPT_ENABLE_Bits.RIE */
#define IFX_ETH_INTERRUPT_ENABLE_RIE_LEN (1u)

/** \brief  Mask for Ifx_ETH_INTERRUPT_ENABLE_Bits.RIE */
#define IFX_ETH_INTERRUPT_ENABLE_RIE_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_INTERRUPT_ENABLE_Bits.RIE */
#define IFX_ETH_INTERRUPT_ENABLE_RIE_OFF (6u)

/** \brief  Length for Ifx_ETH_INTERRUPT_ENABLE_Bits.RSE */
#define IFX_ETH_INTERRUPT_ENABLE_RSE_LEN (1u)

/** \brief  Mask for Ifx_ETH_INTERRUPT_ENABLE_Bits.RSE */
#define IFX_ETH_INTERRUPT_ENABLE_RSE_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_INTERRUPT_ENABLE_Bits.RSE */
#define IFX_ETH_INTERRUPT_ENABLE_RSE_OFF (8u)

/** \brief  Length for Ifx_ETH_INTERRUPT_ENABLE_Bits.RUE */
#define IFX_ETH_INTERRUPT_ENABLE_RUE_LEN (1u)

/** \brief  Mask for Ifx_ETH_INTERRUPT_ENABLE_Bits.RUE */
#define IFX_ETH_INTERRUPT_ENABLE_RUE_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_INTERRUPT_ENABLE_Bits.RUE */
#define IFX_ETH_INTERRUPT_ENABLE_RUE_OFF (7u)

/** \brief  Length for Ifx_ETH_INTERRUPT_ENABLE_Bits.RWE */
#define IFX_ETH_INTERRUPT_ENABLE_RWE_LEN (1u)

/** \brief  Mask for Ifx_ETH_INTERRUPT_ENABLE_Bits.RWE */
#define IFX_ETH_INTERRUPT_ENABLE_RWE_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_INTERRUPT_ENABLE_Bits.RWE */
#define IFX_ETH_INTERRUPT_ENABLE_RWE_OFF (9u)

/** \brief  Length for Ifx_ETH_INTERRUPT_ENABLE_Bits.TIE */
#define IFX_ETH_INTERRUPT_ENABLE_TIE_LEN (1u)

/** \brief  Mask for Ifx_ETH_INTERRUPT_ENABLE_Bits.TIE */
#define IFX_ETH_INTERRUPT_ENABLE_TIE_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_INTERRUPT_ENABLE_Bits.TIE */
#define IFX_ETH_INTERRUPT_ENABLE_TIE_OFF (0u)

/** \brief  Length for Ifx_ETH_INTERRUPT_ENABLE_Bits.TJE */
#define IFX_ETH_INTERRUPT_ENABLE_TJE_LEN (1u)

/** \brief  Mask for Ifx_ETH_INTERRUPT_ENABLE_Bits.TJE */
#define IFX_ETH_INTERRUPT_ENABLE_TJE_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_INTERRUPT_ENABLE_Bits.TJE */
#define IFX_ETH_INTERRUPT_ENABLE_TJE_OFF (3u)

/** \brief  Length for Ifx_ETH_INTERRUPT_ENABLE_Bits.TSE */
#define IFX_ETH_INTERRUPT_ENABLE_TSE_LEN (1u)

/** \brief  Mask for Ifx_ETH_INTERRUPT_ENABLE_Bits.TSE */
#define IFX_ETH_INTERRUPT_ENABLE_TSE_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_INTERRUPT_ENABLE_Bits.TSE */
#define IFX_ETH_INTERRUPT_ENABLE_TSE_OFF (1u)

/** \brief  Length for Ifx_ETH_INTERRUPT_ENABLE_Bits.TUE */
#define IFX_ETH_INTERRUPT_ENABLE_TUE_LEN (1u)

/** \brief  Mask for Ifx_ETH_INTERRUPT_ENABLE_Bits.TUE */
#define IFX_ETH_INTERRUPT_ENABLE_TUE_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_INTERRUPT_ENABLE_Bits.TUE */
#define IFX_ETH_INTERRUPT_ENABLE_TUE_OFF (2u)

/** \brief  Length for Ifx_ETH_INTERRUPT_ENABLE_Bits.UNE */
#define IFX_ETH_INTERRUPT_ENABLE_UNE_LEN (1u)

/** \brief  Mask for Ifx_ETH_INTERRUPT_ENABLE_Bits.UNE */
#define IFX_ETH_INTERRUPT_ENABLE_UNE_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_INTERRUPT_ENABLE_Bits.UNE */
#define IFX_ETH_INTERRUPT_ENABLE_UNE_OFF (5u)

/** \brief  Length for Ifx_ETH_INTERRUPT_MASK_Bits.LPIIM */
#define IFX_ETH_INTERRUPT_MASK_LPIIM_LEN (1u)

/** \brief  Mask for Ifx_ETH_INTERRUPT_MASK_Bits.LPIIM */
#define IFX_ETH_INTERRUPT_MASK_LPIIM_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_INTERRUPT_MASK_Bits.LPIIM */
#define IFX_ETH_INTERRUPT_MASK_LPIIM_OFF (10u)

/** \brief  Length for Ifx_ETH_INTERRUPT_MASK_Bits.PCSANCIM */
#define IFX_ETH_INTERRUPT_MASK_PCSANCIM_LEN (1u)

/** \brief  Mask for Ifx_ETH_INTERRUPT_MASK_Bits.PCSANCIM */
#define IFX_ETH_INTERRUPT_MASK_PCSANCIM_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_INTERRUPT_MASK_Bits.PCSANCIM */
#define IFX_ETH_INTERRUPT_MASK_PCSANCIM_OFF (2u)

/** \brief  Length for Ifx_ETH_INTERRUPT_MASK_Bits.PCSLCHGIM */
#define IFX_ETH_INTERRUPT_MASK_PCSLCHGIM_LEN (1u)

/** \brief  Mask for Ifx_ETH_INTERRUPT_MASK_Bits.PCSLCHGIM */
#define IFX_ETH_INTERRUPT_MASK_PCSLCHGIM_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_INTERRUPT_MASK_Bits.PCSLCHGIM */
#define IFX_ETH_INTERRUPT_MASK_PCSLCHGIM_OFF (1u)

/** \brief  Length for Ifx_ETH_INTERRUPT_MASK_Bits.PMTIM */
#define IFX_ETH_INTERRUPT_MASK_PMTIM_LEN (1u)

/** \brief  Mask for Ifx_ETH_INTERRUPT_MASK_Bits.PMTIM */
#define IFX_ETH_INTERRUPT_MASK_PMTIM_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_INTERRUPT_MASK_Bits.PMTIM */
#define IFX_ETH_INTERRUPT_MASK_PMTIM_OFF (3u)

/** \brief  Length for Ifx_ETH_INTERRUPT_MASK_Bits.RGSMIIIM */
#define IFX_ETH_INTERRUPT_MASK_RGSMIIIM_LEN (1u)

/** \brief  Mask for Ifx_ETH_INTERRUPT_MASK_Bits.RGSMIIIM */
#define IFX_ETH_INTERRUPT_MASK_RGSMIIIM_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_INTERRUPT_MASK_Bits.RGSMIIIM */
#define IFX_ETH_INTERRUPT_MASK_RGSMIIIM_OFF (0u)

/** \brief  Length for Ifx_ETH_INTERRUPT_MASK_Bits.TSIM */
#define IFX_ETH_INTERRUPT_MASK_TSIM_LEN (1u)

/** \brief  Mask for Ifx_ETH_INTERRUPT_MASK_Bits.TSIM */
#define IFX_ETH_INTERRUPT_MASK_TSIM_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_INTERRUPT_MASK_Bits.TSIM */
#define IFX_ETH_INTERRUPT_MASK_TSIM_OFF (9u)

/** \brief  Length for Ifx_ETH_INTERRUPT_STATUS_Bits.LPIIS */
#define IFX_ETH_INTERRUPT_STATUS_LPIIS_LEN (1u)

/** \brief  Mask for Ifx_ETH_INTERRUPT_STATUS_Bits.LPIIS */
#define IFX_ETH_INTERRUPT_STATUS_LPIIS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_INTERRUPT_STATUS_Bits.LPIIS */
#define IFX_ETH_INTERRUPT_STATUS_LPIIS_OFF (10u)

/** \brief  Length for Ifx_ETH_INTERRUPT_STATUS_Bits.MMCIS */
#define IFX_ETH_INTERRUPT_STATUS_MMCIS_LEN (1u)

/** \brief  Mask for Ifx_ETH_INTERRUPT_STATUS_Bits.MMCIS */
#define IFX_ETH_INTERRUPT_STATUS_MMCIS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_INTERRUPT_STATUS_Bits.MMCIS */
#define IFX_ETH_INTERRUPT_STATUS_MMCIS_OFF (4u)

/** \brief  Length for Ifx_ETH_INTERRUPT_STATUS_Bits.MMCRXIPIS */
#define IFX_ETH_INTERRUPT_STATUS_MMCRXIPIS_LEN (1u)

/** \brief  Mask for Ifx_ETH_INTERRUPT_STATUS_Bits.MMCRXIPIS */
#define IFX_ETH_INTERRUPT_STATUS_MMCRXIPIS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_INTERRUPT_STATUS_Bits.MMCRXIPIS */
#define IFX_ETH_INTERRUPT_STATUS_MMCRXIPIS_OFF (7u)

/** \brief  Length for Ifx_ETH_INTERRUPT_STATUS_Bits.MMCRXIS */
#define IFX_ETH_INTERRUPT_STATUS_MMCRXIS_LEN (1u)

/** \brief  Mask for Ifx_ETH_INTERRUPT_STATUS_Bits.MMCRXIS */
#define IFX_ETH_INTERRUPT_STATUS_MMCRXIS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_INTERRUPT_STATUS_Bits.MMCRXIS */
#define IFX_ETH_INTERRUPT_STATUS_MMCRXIS_OFF (5u)

/** \brief  Length for Ifx_ETH_INTERRUPT_STATUS_Bits.MMCTXIS */
#define IFX_ETH_INTERRUPT_STATUS_MMCTXIS_LEN (1u)

/** \brief  Mask for Ifx_ETH_INTERRUPT_STATUS_Bits.MMCTXIS */
#define IFX_ETH_INTERRUPT_STATUS_MMCTXIS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_INTERRUPT_STATUS_Bits.MMCTXIS */
#define IFX_ETH_INTERRUPT_STATUS_MMCTXIS_OFF (6u)

/** \brief  Length for Ifx_ETH_INTERRUPT_STATUS_Bits.PCSANCIS */
#define IFX_ETH_INTERRUPT_STATUS_PCSANCIS_LEN (1u)

/** \brief  Mask for Ifx_ETH_INTERRUPT_STATUS_Bits.PCSANCIS */
#define IFX_ETH_INTERRUPT_STATUS_PCSANCIS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_INTERRUPT_STATUS_Bits.PCSANCIS */
#define IFX_ETH_INTERRUPT_STATUS_PCSANCIS_OFF (2u)

/** \brief  Length for Ifx_ETH_INTERRUPT_STATUS_Bits.PCSLCHGIS */
#define IFX_ETH_INTERRUPT_STATUS_PCSLCHGIS_LEN (1u)

/** \brief  Mask for Ifx_ETH_INTERRUPT_STATUS_Bits.PCSLCHGIS */
#define IFX_ETH_INTERRUPT_STATUS_PCSLCHGIS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_INTERRUPT_STATUS_Bits.PCSLCHGIS */
#define IFX_ETH_INTERRUPT_STATUS_PCSLCHGIS_OFF (1u)

/** \brief  Length for Ifx_ETH_INTERRUPT_STATUS_Bits.PMTIS */
#define IFX_ETH_INTERRUPT_STATUS_PMTIS_LEN (1u)

/** \brief  Mask for Ifx_ETH_INTERRUPT_STATUS_Bits.PMTIS */
#define IFX_ETH_INTERRUPT_STATUS_PMTIS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_INTERRUPT_STATUS_Bits.PMTIS */
#define IFX_ETH_INTERRUPT_STATUS_PMTIS_OFF (3u)

/** \brief  Length for Ifx_ETH_INTERRUPT_STATUS_Bits.RGSMIIIS */
#define IFX_ETH_INTERRUPT_STATUS_RGSMIIIS_LEN (1u)

/** \brief  Mask for Ifx_ETH_INTERRUPT_STATUS_Bits.RGSMIIIS */
#define IFX_ETH_INTERRUPT_STATUS_RGSMIIIS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_INTERRUPT_STATUS_Bits.RGSMIIIS */
#define IFX_ETH_INTERRUPT_STATUS_RGSMIIIS_OFF (0u)

/** \brief  Length for Ifx_ETH_INTERRUPT_STATUS_Bits.TSIS */
#define IFX_ETH_INTERRUPT_STATUS_TSIS_LEN (1u)

/** \brief  Mask for Ifx_ETH_INTERRUPT_STATUS_Bits.TSIS */
#define IFX_ETH_INTERRUPT_STATUS_TSIS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_INTERRUPT_STATUS_Bits.TSIS */
#define IFX_ETH_INTERRUPT_STATUS_TSIS_OFF (9u)

/** \brief  Length for Ifx_ETH_KRST0_Bits.RST */
#define IFX_ETH_KRST0_RST_LEN (1u)

/** \brief  Mask for Ifx_ETH_KRST0_Bits.RST */
#define IFX_ETH_KRST0_RST_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_KRST0_Bits.RST */
#define IFX_ETH_KRST0_RST_OFF (0u)

/** \brief  Length for Ifx_ETH_KRST0_Bits.RSTSTAT */
#define IFX_ETH_KRST0_RSTSTAT_LEN (1u)

/** \brief  Mask for Ifx_ETH_KRST0_Bits.RSTSTAT */
#define IFX_ETH_KRST0_RSTSTAT_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_KRST0_Bits.RSTSTAT */
#define IFX_ETH_KRST0_RSTSTAT_OFF (1u)

/** \brief  Length for Ifx_ETH_KRST1_Bits.RST */
#define IFX_ETH_KRST1_RST_LEN (1u)

/** \brief  Mask for Ifx_ETH_KRST1_Bits.RST */
#define IFX_ETH_KRST1_RST_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_KRST1_Bits.RST */
#define IFX_ETH_KRST1_RST_OFF (0u)

/** \brief  Length for Ifx_ETH_KRSTCLR_Bits.CLR */
#define IFX_ETH_KRSTCLR_CLR_LEN (1u)

/** \brief  Mask for Ifx_ETH_KRSTCLR_Bits.CLR */
#define IFX_ETH_KRSTCLR_CLR_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_KRSTCLR_Bits.CLR */
#define IFX_ETH_KRSTCLR_CLR_OFF (0u)

/** \brief  Length for Ifx_ETH_MAC_ADDRESS_HIGH_Bits.ADDRHI */
#define IFX_ETH_MAC_ADDRESS_HIGH_ADDRHI_LEN (16u)

/** \brief  Mask for Ifx_ETH_MAC_ADDRESS_HIGH_Bits.ADDRHI */
#define IFX_ETH_MAC_ADDRESS_HIGH_ADDRHI_MSK (0xffffu)

/** \brief  Offset for Ifx_ETH_MAC_ADDRESS_HIGH_Bits.ADDRHI */
#define IFX_ETH_MAC_ADDRESS_HIGH_ADDRHI_OFF (0u)

/** \brief  Length for Ifx_ETH_MAC_ADDRESS_HIGH_Bits.AE */
#define IFX_ETH_MAC_ADDRESS_HIGH_AE_LEN (1u)

/** \brief  Mask for Ifx_ETH_MAC_ADDRESS_HIGH_Bits.AE */
#define IFX_ETH_MAC_ADDRESS_HIGH_AE_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MAC_ADDRESS_HIGH_Bits.AE */
#define IFX_ETH_MAC_ADDRESS_HIGH_AE_OFF (31u)

/** \brief  Length for Ifx_ETH_MAC_ADDRESS_HIGH_Bits.MBC */
#define IFX_ETH_MAC_ADDRESS_HIGH_MBC_LEN (6u)

/** \brief  Mask for Ifx_ETH_MAC_ADDRESS_HIGH_Bits.MBC */
#define IFX_ETH_MAC_ADDRESS_HIGH_MBC_MSK (0x3fu)

/** \brief  Offset for Ifx_ETH_MAC_ADDRESS_HIGH_Bits.MBC */
#define IFX_ETH_MAC_ADDRESS_HIGH_MBC_OFF (24u)

/** \brief  Length for Ifx_ETH_MAC_ADDRESS_HIGH_Bits.SA */
#define IFX_ETH_MAC_ADDRESS_HIGH_SA_LEN (1u)

/** \brief  Mask for Ifx_ETH_MAC_ADDRESS_HIGH_Bits.SA */
#define IFX_ETH_MAC_ADDRESS_HIGH_SA_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MAC_ADDRESS_HIGH_Bits.SA */
#define IFX_ETH_MAC_ADDRESS_HIGH_SA_OFF (30u)

/** \brief  Length for Ifx_ETH_MAC_ADDRESS_LOW_Bits.ADDRLO */
#define IFX_ETH_MAC_ADDRESS_LOW_ADDRLO_LEN (32u)

/** \brief  Mask for Ifx_ETH_MAC_ADDRESS_LOW_Bits.ADDRLO */
#define IFX_ETH_MAC_ADDRESS_LOW_ADDRLO_MSK (0xffffffffu)

/** \brief  Offset for Ifx_ETH_MAC_ADDRESS_LOW_Bits.ADDRLO */
#define IFX_ETH_MAC_ADDRESS_LOW_ADDRLO_OFF (0u)

/** \brief  Length for Ifx_ETH_MAC_CONFIGURATION_Bits.ACS */
#define IFX_ETH_MAC_CONFIGURATION_ACS_LEN (1u)

/** \brief  Mask for Ifx_ETH_MAC_CONFIGURATION_Bits.ACS */
#define IFX_ETH_MAC_CONFIGURATION_ACS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MAC_CONFIGURATION_Bits.ACS */
#define IFX_ETH_MAC_CONFIGURATION_ACS_OFF (7u)

/** \brief  Length for Ifx_ETH_MAC_CONFIGURATION_Bits.BE */
#define IFX_ETH_MAC_CONFIGURATION_BE_LEN (1u)

/** \brief  Mask for Ifx_ETH_MAC_CONFIGURATION_Bits.BE */
#define IFX_ETH_MAC_CONFIGURATION_BE_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MAC_CONFIGURATION_Bits.BE */
#define IFX_ETH_MAC_CONFIGURATION_BE_OFF (21u)

/** \brief  Length for Ifx_ETH_MAC_CONFIGURATION_Bits.BL */
#define IFX_ETH_MAC_CONFIGURATION_BL_LEN (2u)

/** \brief  Mask for Ifx_ETH_MAC_CONFIGURATION_Bits.BL */
#define IFX_ETH_MAC_CONFIGURATION_BL_MSK (0x3u)

/** \brief  Offset for Ifx_ETH_MAC_CONFIGURATION_Bits.BL */
#define IFX_ETH_MAC_CONFIGURATION_BL_OFF (5u)

/** \brief  Length for Ifx_ETH_MAC_CONFIGURATION_Bits.CST */
#define IFX_ETH_MAC_CONFIGURATION_CST_LEN (1u)

/** \brief  Mask for Ifx_ETH_MAC_CONFIGURATION_Bits.CST */
#define IFX_ETH_MAC_CONFIGURATION_CST_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MAC_CONFIGURATION_Bits.CST */
#define IFX_ETH_MAC_CONFIGURATION_CST_OFF (25u)

/** \brief  Length for Ifx_ETH_MAC_CONFIGURATION_Bits.DC */
#define IFX_ETH_MAC_CONFIGURATION_DC_LEN (1u)

/** \brief  Mask for Ifx_ETH_MAC_CONFIGURATION_Bits.DC */
#define IFX_ETH_MAC_CONFIGURATION_DC_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MAC_CONFIGURATION_Bits.DC */
#define IFX_ETH_MAC_CONFIGURATION_DC_OFF (4u)

/** \brief  Length for Ifx_ETH_MAC_CONFIGURATION_Bits.DCRS */
#define IFX_ETH_MAC_CONFIGURATION_DCRS_LEN (1u)

/** \brief  Mask for Ifx_ETH_MAC_CONFIGURATION_Bits.DCRS */
#define IFX_ETH_MAC_CONFIGURATION_DCRS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MAC_CONFIGURATION_Bits.DCRS */
#define IFX_ETH_MAC_CONFIGURATION_DCRS_OFF (16u)

/** \brief  Length for Ifx_ETH_MAC_CONFIGURATION_Bits.DM */
#define IFX_ETH_MAC_CONFIGURATION_DM_LEN (1u)

/** \brief  Mask for Ifx_ETH_MAC_CONFIGURATION_Bits.DM */
#define IFX_ETH_MAC_CONFIGURATION_DM_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MAC_CONFIGURATION_Bits.DM */
#define IFX_ETH_MAC_CONFIGURATION_DM_OFF (11u)

/** \brief  Length for Ifx_ETH_MAC_CONFIGURATION_Bits.DO */
#define IFX_ETH_MAC_CONFIGURATION_DO_LEN (1u)

/** \brief  Mask for Ifx_ETH_MAC_CONFIGURATION_Bits.DO */
#define IFX_ETH_MAC_CONFIGURATION_DO_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MAC_CONFIGURATION_Bits.DO */
#define IFX_ETH_MAC_CONFIGURATION_DO_OFF (13u)

/** \brief  Length for Ifx_ETH_MAC_CONFIGURATION_Bits.DR */
#define IFX_ETH_MAC_CONFIGURATION_DR_LEN (1u)

/** \brief  Mask for Ifx_ETH_MAC_CONFIGURATION_Bits.DR */
#define IFX_ETH_MAC_CONFIGURATION_DR_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MAC_CONFIGURATION_Bits.DR */
#define IFX_ETH_MAC_CONFIGURATION_DR_OFF (9u)

/** \brief  Length for Ifx_ETH_MAC_CONFIGURATION_Bits.FES */
#define IFX_ETH_MAC_CONFIGURATION_FES_LEN (1u)

/** \brief  Mask for Ifx_ETH_MAC_CONFIGURATION_Bits.FES */
#define IFX_ETH_MAC_CONFIGURATION_FES_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MAC_CONFIGURATION_Bits.FES */
#define IFX_ETH_MAC_CONFIGURATION_FES_OFF (14u)

/** \brief  Length for Ifx_ETH_MAC_CONFIGURATION_Bits.IFG */
#define IFX_ETH_MAC_CONFIGURATION_IFG_LEN (3u)

/** \brief  Mask for Ifx_ETH_MAC_CONFIGURATION_Bits.IFG */
#define IFX_ETH_MAC_CONFIGURATION_IFG_MSK (0x7u)

/** \brief  Offset for Ifx_ETH_MAC_CONFIGURATION_Bits.IFG */
#define IFX_ETH_MAC_CONFIGURATION_IFG_OFF (17u)

/** \brief  Length for Ifx_ETH_MAC_CONFIGURATION_Bits.IPC */
#define IFX_ETH_MAC_CONFIGURATION_IPC_LEN (1u)

/** \brief  Mask for Ifx_ETH_MAC_CONFIGURATION_Bits.IPC */
#define IFX_ETH_MAC_CONFIGURATION_IPC_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MAC_CONFIGURATION_Bits.IPC */
#define IFX_ETH_MAC_CONFIGURATION_IPC_OFF (10u)

/** \brief  Length for Ifx_ETH_MAC_CONFIGURATION_Bits.JD */
#define IFX_ETH_MAC_CONFIGURATION_JD_LEN (1u)

/** \brief  Mask for Ifx_ETH_MAC_CONFIGURATION_Bits.JD */
#define IFX_ETH_MAC_CONFIGURATION_JD_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MAC_CONFIGURATION_Bits.JD */
#define IFX_ETH_MAC_CONFIGURATION_JD_OFF (22u)

/** \brief  Length for Ifx_ETH_MAC_CONFIGURATION_Bits.JE */
#define IFX_ETH_MAC_CONFIGURATION_JE_LEN (1u)

/** \brief  Mask for Ifx_ETH_MAC_CONFIGURATION_Bits.JE */
#define IFX_ETH_MAC_CONFIGURATION_JE_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MAC_CONFIGURATION_Bits.JE */
#define IFX_ETH_MAC_CONFIGURATION_JE_OFF (20u)

/** \brief  Length for Ifx_ETH_MAC_CONFIGURATION_Bits.LM */
#define IFX_ETH_MAC_CONFIGURATION_LM_LEN (1u)

/** \brief  Mask for Ifx_ETH_MAC_CONFIGURATION_Bits.LM */
#define IFX_ETH_MAC_CONFIGURATION_LM_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MAC_CONFIGURATION_Bits.LM */
#define IFX_ETH_MAC_CONFIGURATION_LM_OFF (12u)

/** \brief  Length for Ifx_ETH_MAC_CONFIGURATION_Bits.LUD */
#define IFX_ETH_MAC_CONFIGURATION_LUD_LEN (1u)

/** \brief  Mask for Ifx_ETH_MAC_CONFIGURATION_Bits.LUD */
#define IFX_ETH_MAC_CONFIGURATION_LUD_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MAC_CONFIGURATION_Bits.LUD */
#define IFX_ETH_MAC_CONFIGURATION_LUD_OFF (8u)

/** \brief  Length for Ifx_ETH_MAC_CONFIGURATION_Bits.PRELEN */
#define IFX_ETH_MAC_CONFIGURATION_PRELEN_LEN (2u)

/** \brief  Mask for Ifx_ETH_MAC_CONFIGURATION_Bits.PRELEN */
#define IFX_ETH_MAC_CONFIGURATION_PRELEN_MSK (0x3u)

/** \brief  Offset for Ifx_ETH_MAC_CONFIGURATION_Bits.PRELEN */
#define IFX_ETH_MAC_CONFIGURATION_PRELEN_OFF (0u)

/** \brief  Length for Ifx_ETH_MAC_CONFIGURATION_Bits.PS */
#define IFX_ETH_MAC_CONFIGURATION_PS_LEN (1u)

/** \brief  Mask for Ifx_ETH_MAC_CONFIGURATION_Bits.PS */
#define IFX_ETH_MAC_CONFIGURATION_PS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MAC_CONFIGURATION_Bits.PS */
#define IFX_ETH_MAC_CONFIGURATION_PS_OFF (15u)

/** \brief  Length for Ifx_ETH_MAC_CONFIGURATION_Bits.RE */
#define IFX_ETH_MAC_CONFIGURATION_RE_LEN (1u)

/** \brief  Mask for Ifx_ETH_MAC_CONFIGURATION_Bits.RE */
#define IFX_ETH_MAC_CONFIGURATION_RE_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MAC_CONFIGURATION_Bits.RE */
#define IFX_ETH_MAC_CONFIGURATION_RE_OFF (2u)

/** \brief  Length for Ifx_ETH_MAC_CONFIGURATION_Bits.SARC */
#define IFX_ETH_MAC_CONFIGURATION_SARC_LEN (3u)

/** \brief  Mask for Ifx_ETH_MAC_CONFIGURATION_Bits.SARC */
#define IFX_ETH_MAC_CONFIGURATION_SARC_MSK (0x7u)

/** \brief  Offset for Ifx_ETH_MAC_CONFIGURATION_Bits.SARC */
#define IFX_ETH_MAC_CONFIGURATION_SARC_OFF (28u)

/** \brief  Length for Ifx_ETH_MAC_CONFIGURATION_Bits.SFTERR */
#define IFX_ETH_MAC_CONFIGURATION_SFTERR_LEN (1u)

/** \brief  Mask for Ifx_ETH_MAC_CONFIGURATION_Bits.SFTERR */
#define IFX_ETH_MAC_CONFIGURATION_SFTERR_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MAC_CONFIGURATION_Bits.SFTERR */
#define IFX_ETH_MAC_CONFIGURATION_SFTERR_OFF (26u)

/** \brief  Length for Ifx_ETH_MAC_CONFIGURATION_Bits.TC */
#define IFX_ETH_MAC_CONFIGURATION_TC_LEN (1u)

/** \brief  Mask for Ifx_ETH_MAC_CONFIGURATION_Bits.TC */
#define IFX_ETH_MAC_CONFIGURATION_TC_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MAC_CONFIGURATION_Bits.TC */
#define IFX_ETH_MAC_CONFIGURATION_TC_OFF (24u)

/** \brief  Length for Ifx_ETH_MAC_CONFIGURATION_Bits.TE */
#define IFX_ETH_MAC_CONFIGURATION_TE_LEN (1u)

/** \brief  Mask for Ifx_ETH_MAC_CONFIGURATION_Bits.TE */
#define IFX_ETH_MAC_CONFIGURATION_TE_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MAC_CONFIGURATION_Bits.TE */
#define IFX_ETH_MAC_CONFIGURATION_TE_OFF (3u)

/** \brief  Length for Ifx_ETH_MAC_CONFIGURATION_Bits.TWOKPE */
#define IFX_ETH_MAC_CONFIGURATION_TWOKPE_LEN (1u)

/** \brief  Mask for Ifx_ETH_MAC_CONFIGURATION_Bits.TWOKPE */
#define IFX_ETH_MAC_CONFIGURATION_TWOKPE_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MAC_CONFIGURATION_Bits.TWOKPE */
#define IFX_ETH_MAC_CONFIGURATION_TWOKPE_OFF (27u)

/** \brief  Length for Ifx_ETH_MAC_CONFIGURATION_Bits.WD */
#define IFX_ETH_MAC_CONFIGURATION_WD_LEN (1u)

/** \brief  Mask for Ifx_ETH_MAC_CONFIGURATION_Bits.WD */
#define IFX_ETH_MAC_CONFIGURATION_WD_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MAC_CONFIGURATION_Bits.WD */
#define IFX_ETH_MAC_CONFIGURATION_WD_OFF (23u)

/** \brief  Length for Ifx_ETH_MAC_FRAME_FILTER_Bits.DAIF */
#define IFX_ETH_MAC_FRAME_FILTER_DAIF_LEN (1u)

/** \brief  Mask for Ifx_ETH_MAC_FRAME_FILTER_Bits.DAIF */
#define IFX_ETH_MAC_FRAME_FILTER_DAIF_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MAC_FRAME_FILTER_Bits.DAIF */
#define IFX_ETH_MAC_FRAME_FILTER_DAIF_OFF (3u)

/** \brief  Length for Ifx_ETH_MAC_FRAME_FILTER_Bits.DBF */
#define IFX_ETH_MAC_FRAME_FILTER_DBF_LEN (1u)

/** \brief  Mask for Ifx_ETH_MAC_FRAME_FILTER_Bits.DBF */
#define IFX_ETH_MAC_FRAME_FILTER_DBF_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MAC_FRAME_FILTER_Bits.DBF */
#define IFX_ETH_MAC_FRAME_FILTER_DBF_OFF (5u)

/** \brief  Length for Ifx_ETH_MAC_FRAME_FILTER_Bits.DNTU */
#define IFX_ETH_MAC_FRAME_FILTER_DNTU_LEN (1u)

/** \brief  Mask for Ifx_ETH_MAC_FRAME_FILTER_Bits.DNTU */
#define IFX_ETH_MAC_FRAME_FILTER_DNTU_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MAC_FRAME_FILTER_Bits.DNTU */
#define IFX_ETH_MAC_FRAME_FILTER_DNTU_OFF (21u)

/** \brief  Length for Ifx_ETH_MAC_FRAME_FILTER_Bits.HMC */
#define IFX_ETH_MAC_FRAME_FILTER_HMC_LEN (1u)

/** \brief  Mask for Ifx_ETH_MAC_FRAME_FILTER_Bits.HMC */
#define IFX_ETH_MAC_FRAME_FILTER_HMC_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MAC_FRAME_FILTER_Bits.HMC */
#define IFX_ETH_MAC_FRAME_FILTER_HMC_OFF (2u)

/** \brief  Length for Ifx_ETH_MAC_FRAME_FILTER_Bits.HPF */
#define IFX_ETH_MAC_FRAME_FILTER_HPF_LEN (1u)

/** \brief  Mask for Ifx_ETH_MAC_FRAME_FILTER_Bits.HPF */
#define IFX_ETH_MAC_FRAME_FILTER_HPF_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MAC_FRAME_FILTER_Bits.HPF */
#define IFX_ETH_MAC_FRAME_FILTER_HPF_OFF (10u)

/** \brief  Length for Ifx_ETH_MAC_FRAME_FILTER_Bits.HUC */
#define IFX_ETH_MAC_FRAME_FILTER_HUC_LEN (1u)

/** \brief  Mask for Ifx_ETH_MAC_FRAME_FILTER_Bits.HUC */
#define IFX_ETH_MAC_FRAME_FILTER_HUC_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MAC_FRAME_FILTER_Bits.HUC */
#define IFX_ETH_MAC_FRAME_FILTER_HUC_OFF (1u)

/** \brief  Length for Ifx_ETH_MAC_FRAME_FILTER_Bits.IPFE */
#define IFX_ETH_MAC_FRAME_FILTER_IPFE_LEN (1u)

/** \brief  Mask for Ifx_ETH_MAC_FRAME_FILTER_Bits.IPFE */
#define IFX_ETH_MAC_FRAME_FILTER_IPFE_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MAC_FRAME_FILTER_Bits.IPFE */
#define IFX_ETH_MAC_FRAME_FILTER_IPFE_OFF (20u)

/** \brief  Length for Ifx_ETH_MAC_FRAME_FILTER_Bits.PCF */
#define IFX_ETH_MAC_FRAME_FILTER_PCF_LEN (2u)

/** \brief  Mask for Ifx_ETH_MAC_FRAME_FILTER_Bits.PCF */
#define IFX_ETH_MAC_FRAME_FILTER_PCF_MSK (0x3u)

/** \brief  Offset for Ifx_ETH_MAC_FRAME_FILTER_Bits.PCF */
#define IFX_ETH_MAC_FRAME_FILTER_PCF_OFF (6u)

/** \brief  Length for Ifx_ETH_MAC_FRAME_FILTER_Bits.PM */
#define IFX_ETH_MAC_FRAME_FILTER_PM_LEN (1u)

/** \brief  Mask for Ifx_ETH_MAC_FRAME_FILTER_Bits.PM */
#define IFX_ETH_MAC_FRAME_FILTER_PM_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MAC_FRAME_FILTER_Bits.PM */
#define IFX_ETH_MAC_FRAME_FILTER_PM_OFF (4u)

/** \brief  Length for Ifx_ETH_MAC_FRAME_FILTER_Bits.PR */
#define IFX_ETH_MAC_FRAME_FILTER_PR_LEN (1u)

/** \brief  Mask for Ifx_ETH_MAC_FRAME_FILTER_Bits.PR */
#define IFX_ETH_MAC_FRAME_FILTER_PR_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MAC_FRAME_FILTER_Bits.PR */
#define IFX_ETH_MAC_FRAME_FILTER_PR_OFF (0u)

/** \brief  Length for Ifx_ETH_MAC_FRAME_FILTER_Bits.RA */
#define IFX_ETH_MAC_FRAME_FILTER_RA_LEN (1u)

/** \brief  Mask for Ifx_ETH_MAC_FRAME_FILTER_Bits.RA */
#define IFX_ETH_MAC_FRAME_FILTER_RA_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MAC_FRAME_FILTER_Bits.RA */
#define IFX_ETH_MAC_FRAME_FILTER_RA_OFF (31u)

/** \brief  Length for Ifx_ETH_MAC_FRAME_FILTER_Bits.SAF */
#define IFX_ETH_MAC_FRAME_FILTER_SAF_LEN (1u)

/** \brief  Mask for Ifx_ETH_MAC_FRAME_FILTER_Bits.SAF */
#define IFX_ETH_MAC_FRAME_FILTER_SAF_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MAC_FRAME_FILTER_Bits.SAF */
#define IFX_ETH_MAC_FRAME_FILTER_SAF_OFF (9u)

/** \brief  Length for Ifx_ETH_MAC_FRAME_FILTER_Bits.SAIF */
#define IFX_ETH_MAC_FRAME_FILTER_SAIF_LEN (1u)

/** \brief  Mask for Ifx_ETH_MAC_FRAME_FILTER_Bits.SAIF */
#define IFX_ETH_MAC_FRAME_FILTER_SAIF_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MAC_FRAME_FILTER_Bits.SAIF */
#define IFX_ETH_MAC_FRAME_FILTER_SAIF_OFF (8u)

/** \brief  Length for Ifx_ETH_MAC_FRAME_FILTER_Bits.VTFE */
#define IFX_ETH_MAC_FRAME_FILTER_VTFE_LEN (1u)

/** \brief  Mask for Ifx_ETH_MAC_FRAME_FILTER_Bits.VTFE */
#define IFX_ETH_MAC_FRAME_FILTER_VTFE_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MAC_FRAME_FILTER_Bits.VTFE */
#define IFX_ETH_MAC_FRAME_FILTER_VTFE_OFF (16u)

/** \brief  Length for
 * Ifx_ETH_MISSED_FRAME_AND_BUFFER_OVERFLOW_COUNTER_Bits.MISCNTOVF */
#define IFX_ETH_MISSED_FRAME_AND_BUFFER_OVERFLOW_COUNTER_MISCNTOVF_LEN (1u)

/** \brief  Mask for
 * Ifx_ETH_MISSED_FRAME_AND_BUFFER_OVERFLOW_COUNTER_Bits.MISCNTOVF */
#define IFX_ETH_MISSED_FRAME_AND_BUFFER_OVERFLOW_COUNTER_MISCNTOVF_MSK (0x1u)

/** \brief  Offset for
 * Ifx_ETH_MISSED_FRAME_AND_BUFFER_OVERFLOW_COUNTER_Bits.MISCNTOVF */
#define IFX_ETH_MISSED_FRAME_AND_BUFFER_OVERFLOW_COUNTER_MISCNTOVF_OFF (16u)

/** \brief  Length for
 * Ifx_ETH_MISSED_FRAME_AND_BUFFER_OVERFLOW_COUNTER_Bits.MISFRMCNT */
#define IFX_ETH_MISSED_FRAME_AND_BUFFER_OVERFLOW_COUNTER_MISFRMCNT_LEN (16u)

/** \brief  Mask for
 * Ifx_ETH_MISSED_FRAME_AND_BUFFER_OVERFLOW_COUNTER_Bits.MISFRMCNT */
#define IFX_ETH_MISSED_FRAME_AND_BUFFER_OVERFLOW_COUNTER_MISFRMCNT_MSK (0xffffu)

/** \brief  Offset for
 * Ifx_ETH_MISSED_FRAME_AND_BUFFER_OVERFLOW_COUNTER_Bits.MISFRMCNT */
#define IFX_ETH_MISSED_FRAME_AND_BUFFER_OVERFLOW_COUNTER_MISFRMCNT_OFF (0u)

/** \brief  Length for
 * Ifx_ETH_MISSED_FRAME_AND_BUFFER_OVERFLOW_COUNTER_Bits.OVFCNTOVF */
#define IFX_ETH_MISSED_FRAME_AND_BUFFER_OVERFLOW_COUNTER_OVFCNTOVF_LEN (1u)

/** \brief  Mask for
 * Ifx_ETH_MISSED_FRAME_AND_BUFFER_OVERFLOW_COUNTER_Bits.OVFCNTOVF */
#define IFX_ETH_MISSED_FRAME_AND_BUFFER_OVERFLOW_COUNTER_OVFCNTOVF_MSK (0x1u)

/** \brief  Offset for
 * Ifx_ETH_MISSED_FRAME_AND_BUFFER_OVERFLOW_COUNTER_Bits.OVFCNTOVF */
#define IFX_ETH_MISSED_FRAME_AND_BUFFER_OVERFLOW_COUNTER_OVFCNTOVF_OFF (28u)

/** \brief  Length for
 * Ifx_ETH_MISSED_FRAME_AND_BUFFER_OVERFLOW_COUNTER_Bits.OVFFRMCNT */
#define IFX_ETH_MISSED_FRAME_AND_BUFFER_OVERFLOW_COUNTER_OVFFRMCNT_LEN (11u)

/** \brief  Mask for
 * Ifx_ETH_MISSED_FRAME_AND_BUFFER_OVERFLOW_COUNTER_Bits.OVFFRMCNT */
#define IFX_ETH_MISSED_FRAME_AND_BUFFER_OVERFLOW_COUNTER_OVFFRMCNT_MSK (0x7ffu)

/** \brief  Offset for
 * Ifx_ETH_MISSED_FRAME_AND_BUFFER_OVERFLOW_COUNTER_Bits.OVFFRMCNT */
#define IFX_ETH_MISSED_FRAME_AND_BUFFER_OVERFLOW_COUNTER_OVFFRMCNT_OFF (17u)

/** \brief  Length for Ifx_ETH_MMC_CONTROL_Bits.CNTFREEZ */
#define IFX_ETH_MMC_CONTROL_CNTFREEZ_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_CONTROL_Bits.CNTFREEZ */
#define IFX_ETH_MMC_CONTROL_CNTFREEZ_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_CONTROL_Bits.CNTFREEZ */
#define IFX_ETH_MMC_CONTROL_CNTFREEZ_OFF (3u)

/** \brief  Length for Ifx_ETH_MMC_CONTROL_Bits.CNTPRST */
#define IFX_ETH_MMC_CONTROL_CNTPRST_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_CONTROL_Bits.CNTPRST */
#define IFX_ETH_MMC_CONTROL_CNTPRST_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_CONTROL_Bits.CNTPRST */
#define IFX_ETH_MMC_CONTROL_CNTPRST_OFF (4u)

/** \brief  Length for Ifx_ETH_MMC_CONTROL_Bits.CNTPRSTLVL */
#define IFX_ETH_MMC_CONTROL_CNTPRSTLVL_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_CONTROL_Bits.CNTPRSTLVL */
#define IFX_ETH_MMC_CONTROL_CNTPRSTLVL_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_CONTROL_Bits.CNTPRSTLVL */
#define IFX_ETH_MMC_CONTROL_CNTPRSTLVL_OFF (5u)

/** \brief  Length for Ifx_ETH_MMC_CONTROL_Bits.CNTRST */
#define IFX_ETH_MMC_CONTROL_CNTRST_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_CONTROL_Bits.CNTRST */
#define IFX_ETH_MMC_CONTROL_CNTRST_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_CONTROL_Bits.CNTRST */
#define IFX_ETH_MMC_CONTROL_CNTRST_OFF (0u)

/** \brief  Length for Ifx_ETH_MMC_CONTROL_Bits.CNTSTOPRO */
#define IFX_ETH_MMC_CONTROL_CNTSTOPRO_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_CONTROL_Bits.CNTSTOPRO */
#define IFX_ETH_MMC_CONTROL_CNTSTOPRO_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_CONTROL_Bits.CNTSTOPRO */
#define IFX_ETH_MMC_CONTROL_CNTSTOPRO_OFF (1u)

/** \brief  Length for Ifx_ETH_MMC_CONTROL_Bits.RSTONRD */
#define IFX_ETH_MMC_CONTROL_RSTONRD_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_CONTROL_Bits.RSTONRD */
#define IFX_ETH_MMC_CONTROL_RSTONRD_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_CONTROL_Bits.RSTONRD */
#define IFX_ETH_MMC_CONTROL_RSTONRD_OFF (2u)

/** \brief  Length for Ifx_ETH_MMC_CONTROL_Bits.UCDBC */
#define IFX_ETH_MMC_CONTROL_UCDBC_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_CONTROL_Bits.UCDBC */
#define IFX_ETH_MMC_CONTROL_UCDBC_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_CONTROL_Bits.UCDBC */
#define IFX_ETH_MMC_CONTROL_UCDBC_OFF (8u)

/** \brief  Length for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_Bits.RXICMPERFIM */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_RXICMPERFIM_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_Bits.RXICMPERFIM */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_RXICMPERFIM_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_Bits.RXICMPERFIM */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_RXICMPERFIM_OFF (13u)

/** \brief  Length for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_Bits.RXICMPEROIM */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_RXICMPEROIM_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_Bits.RXICMPEROIM */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_RXICMPEROIM_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_Bits.RXICMPEROIM */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_RXICMPEROIM_OFF (29u)

/** \brief  Length for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_Bits.RXICMPGFIM */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_RXICMPGFIM_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_Bits.RXICMPGFIM */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_RXICMPGFIM_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_Bits.RXICMPGFIM */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_RXICMPGFIM_OFF (12u)

/** \brief  Length for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_Bits.RXICMPGOIM */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_RXICMPGOIM_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_Bits.RXICMPGOIM */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_RXICMPGOIM_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_Bits.RXICMPGOIM */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_RXICMPGOIM_OFF (28u)

/** \brief  Length for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_Bits.RXIPV4FRAGFIM */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_RXIPV4FRAGFIM_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_Bits.RXIPV4FRAGFIM */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_RXIPV4FRAGFIM_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_Bits.RXIPV4FRAGFIM */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_RXIPV4FRAGFIM_OFF (3u)

/** \brief  Length for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_Bits.RXIPV4FRAGOIM */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_RXIPV4FRAGOIM_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_Bits.RXIPV4FRAGOIM */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_RXIPV4FRAGOIM_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_Bits.RXIPV4FRAGOIM */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_RXIPV4FRAGOIM_OFF (19u)

/** \brief  Length for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_Bits.RXIPV4GFIM */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_RXIPV4GFIM_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_Bits.RXIPV4GFIM */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_RXIPV4GFIM_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_Bits.RXIPV4GFIM */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_RXIPV4GFIM_OFF (0u)

/** \brief  Length for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_Bits.RXIPV4GOIM */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_RXIPV4GOIM_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_Bits.RXIPV4GOIM */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_RXIPV4GOIM_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_Bits.RXIPV4GOIM */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_RXIPV4GOIM_OFF (16u)

/** \brief  Length for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_Bits.RXIPV4HERFIM */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_RXIPV4HERFIM_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_Bits.RXIPV4HERFIM */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_RXIPV4HERFIM_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_Bits.RXIPV4HERFIM */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_RXIPV4HERFIM_OFF (1u)

/** \brief  Length for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_Bits.RXIPV4HEROIM */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_RXIPV4HEROIM_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_Bits.RXIPV4HEROIM */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_RXIPV4HEROIM_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_Bits.RXIPV4HEROIM */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_RXIPV4HEROIM_OFF (17u)

/** \brief  Length for
 * Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_Bits.RXIPV4NOPAYFIM */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_RXIPV4NOPAYFIM_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_Bits.RXIPV4NOPAYFIM */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_RXIPV4NOPAYFIM_MSK (0x1u)

/** \brief  Offset for
 * Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_Bits.RXIPV4NOPAYFIM */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_RXIPV4NOPAYFIM_OFF (2u)

/** \brief  Length for
 * Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_Bits.RXIPV4NOPAYOIM */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_RXIPV4NOPAYOIM_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_Bits.RXIPV4NOPAYOIM */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_RXIPV4NOPAYOIM_MSK (0x1u)

/** \brief  Offset for
 * Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_Bits.RXIPV4NOPAYOIM */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_RXIPV4NOPAYOIM_OFF (18u)

/** \brief  Length for
 * Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_Bits.RXIPV4UDSBLFIM */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_RXIPV4UDSBLFIM_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_Bits.RXIPV4UDSBLFIM */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_RXIPV4UDSBLFIM_MSK (0x1u)

/** \brief  Offset for
 * Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_Bits.RXIPV4UDSBLFIM */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_RXIPV4UDSBLFIM_OFF (4u)

/** \brief  Length for
 * Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_Bits.RXIPV4UDSBLOIM */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_RXIPV4UDSBLOIM_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_Bits.RXIPV4UDSBLOIM */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_RXIPV4UDSBLOIM_MSK (0x1u)

/** \brief  Offset for
 * Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_Bits.RXIPV4UDSBLOIM */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_RXIPV4UDSBLOIM_OFF (20u)

/** \brief  Length for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_Bits.RXIPV6GFIM */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_RXIPV6GFIM_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_Bits.RXIPV6GFIM */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_RXIPV6GFIM_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_Bits.RXIPV6GFIM */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_RXIPV6GFIM_OFF (5u)

/** \brief  Length for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_Bits.RXIPV6GOIM */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_RXIPV6GOIM_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_Bits.RXIPV6GOIM */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_RXIPV6GOIM_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_Bits.RXIPV6GOIM */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_RXIPV6GOIM_OFF (21u)

/** \brief  Length for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_Bits.RXIPV6HERFIM */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_RXIPV6HERFIM_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_Bits.RXIPV6HERFIM */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_RXIPV6HERFIM_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_Bits.RXIPV6HERFIM */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_RXIPV6HERFIM_OFF (6u)

/** \brief  Length for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_Bits.RXIPV6HEROIM */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_RXIPV6HEROIM_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_Bits.RXIPV6HEROIM */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_RXIPV6HEROIM_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_Bits.RXIPV6HEROIM */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_RXIPV6HEROIM_OFF (22u)

/** \brief  Length for
 * Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_Bits.RXIPV6NOPAYFIM */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_RXIPV6NOPAYFIM_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_Bits.RXIPV6NOPAYFIM */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_RXIPV6NOPAYFIM_MSK (0x1u)

/** \brief  Offset for
 * Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_Bits.RXIPV6NOPAYFIM */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_RXIPV6NOPAYFIM_OFF (7u)

/** \brief  Length for
 * Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_Bits.RXIPV6NOPAYOIM */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_RXIPV6NOPAYOIM_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_Bits.RXIPV6NOPAYOIM */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_RXIPV6NOPAYOIM_MSK (0x1u)

/** \brief  Offset for
 * Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_Bits.RXIPV6NOPAYOIM */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_RXIPV6NOPAYOIM_OFF (23u)

/** \brief  Length for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_Bits.RXTCPERFIM */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_RXTCPERFIM_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_Bits.RXTCPERFIM */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_RXTCPERFIM_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_Bits.RXTCPERFIM */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_RXTCPERFIM_OFF (11u)

/** \brief  Length for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_Bits.RXTCPEROIM */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_RXTCPEROIM_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_Bits.RXTCPEROIM */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_RXTCPEROIM_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_Bits.RXTCPEROIM */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_RXTCPEROIM_OFF (27u)

/** \brief  Length for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_Bits.RXTCPGFIM */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_RXTCPGFIM_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_Bits.RXTCPGFIM */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_RXTCPGFIM_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_Bits.RXTCPGFIM */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_RXTCPGFIM_OFF (10u)

/** \brief  Length for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_Bits.RXTCPGOIM */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_RXTCPGOIM_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_Bits.RXTCPGOIM */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_RXTCPGOIM_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_Bits.RXTCPGOIM */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_RXTCPGOIM_OFF (26u)

/** \brief  Length for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_Bits.RXUDPERFIM */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_RXUDPERFIM_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_Bits.RXUDPERFIM */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_RXUDPERFIM_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_Bits.RXUDPERFIM */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_RXUDPERFIM_OFF (9u)

/** \brief  Length for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_Bits.RXUDPEROIM */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_RXUDPEROIM_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_Bits.RXUDPEROIM */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_RXUDPEROIM_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_Bits.RXUDPEROIM */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_RXUDPEROIM_OFF (25u)

/** \brief  Length for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_Bits.RXUDPGFIM */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_RXUDPGFIM_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_Bits.RXUDPGFIM */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_RXUDPGFIM_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_Bits.RXUDPGFIM */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_RXUDPGFIM_OFF (8u)

/** \brief  Length for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_Bits.RXUDPGOIM */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_RXUDPGOIM_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_Bits.RXUDPGOIM */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_RXUDPGOIM_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_Bits.RXUDPGOIM */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_RXUDPGOIM_OFF (24u)

/** \brief  Length for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_Bits.RXICMPERFIS */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_RXICMPERFIS_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_Bits.RXICMPERFIS */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_RXICMPERFIS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_Bits.RXICMPERFIS */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_RXICMPERFIS_OFF (13u)

/** \brief  Length for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_Bits.RXICMPEROIS */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_RXICMPEROIS_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_Bits.RXICMPEROIS */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_RXICMPEROIS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_Bits.RXICMPEROIS */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_RXICMPEROIS_OFF (29u)

/** \brief  Length for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_Bits.RXICMPGFIS */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_RXICMPGFIS_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_Bits.RXICMPGFIS */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_RXICMPGFIS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_Bits.RXICMPGFIS */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_RXICMPGFIS_OFF (12u)

/** \brief  Length for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_Bits.RXICMPGOIS */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_RXICMPGOIS_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_Bits.RXICMPGOIS */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_RXICMPGOIS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_Bits.RXICMPGOIS */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_RXICMPGOIS_OFF (28u)

/** \brief  Length for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_Bits.RXIPV4FRAGFIS */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_RXIPV4FRAGFIS_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_Bits.RXIPV4FRAGFIS */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_RXIPV4FRAGFIS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_Bits.RXIPV4FRAGFIS */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_RXIPV4FRAGFIS_OFF (3u)

/** \brief  Length for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_Bits.RXIPV4FRAGOIS */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_RXIPV4FRAGOIS_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_Bits.RXIPV4FRAGOIS */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_RXIPV4FRAGOIS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_Bits.RXIPV4FRAGOIS */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_RXIPV4FRAGOIS_OFF (19u)

/** \brief  Length for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_Bits.RXIPV4GFIS */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_RXIPV4GFIS_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_Bits.RXIPV4GFIS */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_RXIPV4GFIS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_Bits.RXIPV4GFIS */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_RXIPV4GFIS_OFF (0u)

/** \brief  Length for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_Bits.RXIPV4GOIS */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_RXIPV4GOIS_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_Bits.RXIPV4GOIS */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_RXIPV4GOIS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_Bits.RXIPV4GOIS */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_RXIPV4GOIS_OFF (16u)

/** \brief  Length for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_Bits.RXIPV4HERFIS */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_RXIPV4HERFIS_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_Bits.RXIPV4HERFIS */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_RXIPV4HERFIS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_Bits.RXIPV4HERFIS */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_RXIPV4HERFIS_OFF (1u)

/** \brief  Length for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_Bits.RXIPV4HEROIS */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_RXIPV4HEROIS_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_Bits.RXIPV4HEROIS */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_RXIPV4HEROIS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_Bits.RXIPV4HEROIS */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_RXIPV4HEROIS_OFF (17u)

/** \brief  Length for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_Bits.RXIPV4NOPAYFIS */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_RXIPV4NOPAYFIS_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_Bits.RXIPV4NOPAYFIS */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_RXIPV4NOPAYFIS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_Bits.RXIPV4NOPAYFIS */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_RXIPV4NOPAYFIS_OFF (2u)

/** \brief  Length for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_Bits.RXIPV4NOPAYOIS */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_RXIPV4NOPAYOIS_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_Bits.RXIPV4NOPAYOIS */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_RXIPV4NOPAYOIS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_Bits.RXIPV4NOPAYOIS */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_RXIPV4NOPAYOIS_OFF (18u)

/** \brief  Length for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_Bits.RXIPV4UDSBLFIS */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_RXIPV4UDSBLFIS_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_Bits.RXIPV4UDSBLFIS */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_RXIPV4UDSBLFIS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_Bits.RXIPV4UDSBLFIS */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_RXIPV4UDSBLFIS_OFF (4u)

/** \brief  Length for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_Bits.RXIPV4UDSBLOIS */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_RXIPV4UDSBLOIS_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_Bits.RXIPV4UDSBLOIS */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_RXIPV4UDSBLOIS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_Bits.RXIPV4UDSBLOIS */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_RXIPV4UDSBLOIS_OFF (20u)

/** \brief  Length for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_Bits.RXIPV6GFIS */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_RXIPV6GFIS_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_Bits.RXIPV6GFIS */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_RXIPV6GFIS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_Bits.RXIPV6GFIS */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_RXIPV6GFIS_OFF (5u)

/** \brief  Length for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_Bits.RXIPV6GOIS */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_RXIPV6GOIS_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_Bits.RXIPV6GOIS */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_RXIPV6GOIS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_Bits.RXIPV6GOIS */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_RXIPV6GOIS_OFF (21u)

/** \brief  Length for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_Bits.RXIPV6HERFIS */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_RXIPV6HERFIS_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_Bits.RXIPV6HERFIS */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_RXIPV6HERFIS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_Bits.RXIPV6HERFIS */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_RXIPV6HERFIS_OFF (6u)

/** \brief  Length for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_Bits.RXIPV6HEROIS */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_RXIPV6HEROIS_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_Bits.RXIPV6HEROIS */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_RXIPV6HEROIS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_Bits.RXIPV6HEROIS */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_RXIPV6HEROIS_OFF (22u)

/** \brief  Length for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_Bits.RXIPV6NOPAYFIS */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_RXIPV6NOPAYFIS_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_Bits.RXIPV6NOPAYFIS */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_RXIPV6NOPAYFIS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_Bits.RXIPV6NOPAYFIS */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_RXIPV6NOPAYFIS_OFF (7u)

/** \brief  Length for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_Bits.RXIPV6NOPAYOIS */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_RXIPV6NOPAYOIS_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_Bits.RXIPV6NOPAYOIS */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_RXIPV6NOPAYOIS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_Bits.RXIPV6NOPAYOIS */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_RXIPV6NOPAYOIS_OFF (23u)

/** \brief  Length for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_Bits.RXTCPERFIS */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_RXTCPERFIS_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_Bits.RXTCPERFIS */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_RXTCPERFIS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_Bits.RXTCPERFIS */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_RXTCPERFIS_OFF (11u)

/** \brief  Length for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_Bits.RXTCPEROIS */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_RXTCPEROIS_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_Bits.RXTCPEROIS */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_RXTCPEROIS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_Bits.RXTCPEROIS */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_RXTCPEROIS_OFF (27u)

/** \brief  Length for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_Bits.RXTCPGFIS */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_RXTCPGFIS_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_Bits.RXTCPGFIS */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_RXTCPGFIS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_Bits.RXTCPGFIS */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_RXTCPGFIS_OFF (10u)

/** \brief  Length for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_Bits.RXTCPGOIS */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_RXTCPGOIS_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_Bits.RXTCPGOIS */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_RXTCPGOIS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_Bits.RXTCPGOIS */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_RXTCPGOIS_OFF (26u)

/** \brief  Length for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_Bits.RXUDPERFIS */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_RXUDPERFIS_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_Bits.RXUDPERFIS */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_RXUDPERFIS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_Bits.RXUDPERFIS */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_RXUDPERFIS_OFF (9u)

/** \brief  Length for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_Bits.RXUDPEROIS */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_RXUDPEROIS_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_Bits.RXUDPEROIS */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_RXUDPEROIS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_Bits.RXUDPEROIS */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_RXUDPEROIS_OFF (25u)

/** \brief  Length for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_Bits.RXUDPGFIS */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_RXUDPGFIS_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_Bits.RXUDPGFIS */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_RXUDPGFIS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_Bits.RXUDPGFIS */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_RXUDPGFIS_OFF (8u)

/** \brief  Length for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_Bits.RXUDPGOIS */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_RXUDPGOIS_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_Bits.RXUDPGOIS */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_RXUDPGOIS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_Bits.RXUDPGOIS */
#define IFX_ETH_MMC_IPC_RECEIVE_INTERRUPT_RXUDPGOIS_OFF (24u)

/** \brief  Length for
 * Ifx_ETH_MMC_RECEIVE_INTERRUPT_MASK_Bits.RX1024TMAXOCTGBFIM */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_MASK_RX1024TMAXOCTGBFIM_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_RECEIVE_INTERRUPT_MASK_Bits.RX1024TMAXOCTGBFIM */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_MASK_RX1024TMAXOCTGBFIM_MSK (0x1u)

/** \brief  Offset for
 * Ifx_ETH_MMC_RECEIVE_INTERRUPT_MASK_Bits.RX1024TMAXOCTGBFIM */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_MASK_RX1024TMAXOCTGBFIM_OFF (16u)

/** \brief  Length for Ifx_ETH_MMC_RECEIVE_INTERRUPT_MASK_Bits.RX128T255OCTGBFIM */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_MASK_RX128T255OCTGBFIM_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_RECEIVE_INTERRUPT_MASK_Bits.RX128T255OCTGBFIM */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_MASK_RX128T255OCTGBFIM_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_RECEIVE_INTERRUPT_MASK_Bits.RX128T255OCTGBFIM */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_MASK_RX128T255OCTGBFIM_OFF (13u)

/** \brief  Length for Ifx_ETH_MMC_RECEIVE_INTERRUPT_MASK_Bits.RX256T511OCTGBFIM */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_MASK_RX256T511OCTGBFIM_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_RECEIVE_INTERRUPT_MASK_Bits.RX256T511OCTGBFIM */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_MASK_RX256T511OCTGBFIM_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_RECEIVE_INTERRUPT_MASK_Bits.RX256T511OCTGBFIM */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_MASK_RX256T511OCTGBFIM_OFF (14u)

/** \brief  Length for
 * Ifx_ETH_MMC_RECEIVE_INTERRUPT_MASK_Bits.RX512T1023OCTGBFIM */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_MASK_RX512T1023OCTGBFIM_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_RECEIVE_INTERRUPT_MASK_Bits.RX512T1023OCTGBFIM */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_MASK_RX512T1023OCTGBFIM_MSK (0x1u)

/** \brief  Offset for
 * Ifx_ETH_MMC_RECEIVE_INTERRUPT_MASK_Bits.RX512T1023OCTGBFIM */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_MASK_RX512T1023OCTGBFIM_OFF (15u)

/** \brief  Length for Ifx_ETH_MMC_RECEIVE_INTERRUPT_MASK_Bits.RX64OCTGBFIM */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_MASK_RX64OCTGBFIM_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_RECEIVE_INTERRUPT_MASK_Bits.RX64OCTGBFIM */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_MASK_RX64OCTGBFIM_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_RECEIVE_INTERRUPT_MASK_Bits.RX64OCTGBFIM */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_MASK_RX64OCTGBFIM_OFF (11u)

/** \brief  Length for Ifx_ETH_MMC_RECEIVE_INTERRUPT_MASK_Bits.RX65T127OCTGBFIM */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_MASK_RX65T127OCTGBFIM_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_RECEIVE_INTERRUPT_MASK_Bits.RX65T127OCTGBFIM */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_MASK_RX65T127OCTGBFIM_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_RECEIVE_INTERRUPT_MASK_Bits.RX65T127OCTGBFIM */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_MASK_RX65T127OCTGBFIM_OFF (12u)

/** \brief  Length for Ifx_ETH_MMC_RECEIVE_INTERRUPT_MASK_Bits.RXALGNERFIM */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_MASK_RXALGNERFIM_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_RECEIVE_INTERRUPT_MASK_Bits.RXALGNERFIM */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_MASK_RXALGNERFIM_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_RECEIVE_INTERRUPT_MASK_Bits.RXALGNERFIM */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_MASK_RXALGNERFIM_OFF (6u)

/** \brief  Length for Ifx_ETH_MMC_RECEIVE_INTERRUPT_MASK_Bits.RXBCGFIM */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_MASK_RXBCGFIM_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_RECEIVE_INTERRUPT_MASK_Bits.RXBCGFIM */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_MASK_RXBCGFIM_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_RECEIVE_INTERRUPT_MASK_Bits.RXBCGFIM */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_MASK_RXBCGFIM_OFF (3u)

/** \brief  Length for Ifx_ETH_MMC_RECEIVE_INTERRUPT_MASK_Bits.RXCRCERFIM */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_MASK_RXCRCERFIM_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_RECEIVE_INTERRUPT_MASK_Bits.RXCRCERFIM */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_MASK_RXCRCERFIM_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_RECEIVE_INTERRUPT_MASK_Bits.RXCRCERFIM */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_MASK_RXCRCERFIM_OFF (5u)

/** \brief  Length for Ifx_ETH_MMC_RECEIVE_INTERRUPT_MASK_Bits.RXCTRLFIM */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_MASK_RXCTRLFIM_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_RECEIVE_INTERRUPT_MASK_Bits.RXCTRLFIM */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_MASK_RXCTRLFIM_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_RECEIVE_INTERRUPT_MASK_Bits.RXCTRLFIM */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_MASK_RXCTRLFIM_OFF (25u)

/** \brief  Length for Ifx_ETH_MMC_RECEIVE_INTERRUPT_MASK_Bits.RXFOVFIM */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_MASK_RXFOVFIM_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_RECEIVE_INTERRUPT_MASK_Bits.RXFOVFIM */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_MASK_RXFOVFIM_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_RECEIVE_INTERRUPT_MASK_Bits.RXFOVFIM */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_MASK_RXFOVFIM_OFF (21u)

/** \brief  Length for Ifx_ETH_MMC_RECEIVE_INTERRUPT_MASK_Bits.RXGBFRMIM */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_MASK_RXGBFRMIM_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_RECEIVE_INTERRUPT_MASK_Bits.RXGBFRMIM */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_MASK_RXGBFRMIM_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_RECEIVE_INTERRUPT_MASK_Bits.RXGBFRMIM */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_MASK_RXGBFRMIM_OFF (0u)

/** \brief  Length for Ifx_ETH_MMC_RECEIVE_INTERRUPT_MASK_Bits.RXGBOCTIM */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_MASK_RXGBOCTIM_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_RECEIVE_INTERRUPT_MASK_Bits.RXGBOCTIM */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_MASK_RXGBOCTIM_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_RECEIVE_INTERRUPT_MASK_Bits.RXGBOCTIM */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_MASK_RXGBOCTIM_OFF (1u)

/** \brief  Length for Ifx_ETH_MMC_RECEIVE_INTERRUPT_MASK_Bits.RXGOCTIM */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_MASK_RXGOCTIM_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_RECEIVE_INTERRUPT_MASK_Bits.RXGOCTIM */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_MASK_RXGOCTIM_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_RECEIVE_INTERRUPT_MASK_Bits.RXGOCTIM */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_MASK_RXGOCTIM_OFF (2u)

/** \brief  Length for Ifx_ETH_MMC_RECEIVE_INTERRUPT_MASK_Bits.RXJABERFIM */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_MASK_RXJABERFIM_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_RECEIVE_INTERRUPT_MASK_Bits.RXJABERFIM */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_MASK_RXJABERFIM_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_RECEIVE_INTERRUPT_MASK_Bits.RXJABERFIM */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_MASK_RXJABERFIM_OFF (8u)

/** \brief  Length for Ifx_ETH_MMC_RECEIVE_INTERRUPT_MASK_Bits.RXLENERFIM */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_MASK_RXLENERFIM_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_RECEIVE_INTERRUPT_MASK_Bits.RXLENERFIM */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_MASK_RXLENERFIM_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_RECEIVE_INTERRUPT_MASK_Bits.RXLENERFIM */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_MASK_RXLENERFIM_OFF (18u)

/** \brief  Length for Ifx_ETH_MMC_RECEIVE_INTERRUPT_MASK_Bits.RXMCGFIM */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_MASK_RXMCGFIM_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_RECEIVE_INTERRUPT_MASK_Bits.RXMCGFIM */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_MASK_RXMCGFIM_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_RECEIVE_INTERRUPT_MASK_Bits.RXMCGFIM */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_MASK_RXMCGFIM_OFF (4u)

/** \brief  Length for Ifx_ETH_MMC_RECEIVE_INTERRUPT_MASK_Bits.RXORANGEFIM */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_MASK_RXORANGEFIM_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_RECEIVE_INTERRUPT_MASK_Bits.RXORANGEFIM */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_MASK_RXORANGEFIM_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_RECEIVE_INTERRUPT_MASK_Bits.RXORANGEFIM */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_MASK_RXORANGEFIM_OFF (19u)

/** \brief  Length for Ifx_ETH_MMC_RECEIVE_INTERRUPT_MASK_Bits.RXOSIZEGFIM */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_MASK_RXOSIZEGFIM_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_RECEIVE_INTERRUPT_MASK_Bits.RXOSIZEGFIM */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_MASK_RXOSIZEGFIM_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_RECEIVE_INTERRUPT_MASK_Bits.RXOSIZEGFIM */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_MASK_RXOSIZEGFIM_OFF (10u)

/** \brief  Length for Ifx_ETH_MMC_RECEIVE_INTERRUPT_MASK_Bits.RXPAUSFIM */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_MASK_RXPAUSFIM_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_RECEIVE_INTERRUPT_MASK_Bits.RXPAUSFIM */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_MASK_RXPAUSFIM_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_RECEIVE_INTERRUPT_MASK_Bits.RXPAUSFIM */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_MASK_RXPAUSFIM_OFF (20u)

/** \brief  Length for Ifx_ETH_MMC_RECEIVE_INTERRUPT_MASK_Bits.RXRCVERRFIM */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_MASK_RXRCVERRFIM_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_RECEIVE_INTERRUPT_MASK_Bits.RXRCVERRFIM */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_MASK_RXRCVERRFIM_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_RECEIVE_INTERRUPT_MASK_Bits.RXRCVERRFIM */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_MASK_RXRCVERRFIM_OFF (24u)

/** \brief  Length for Ifx_ETH_MMC_RECEIVE_INTERRUPT_MASK_Bits.RXRUNTFIM */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_MASK_RXRUNTFIM_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_RECEIVE_INTERRUPT_MASK_Bits.RXRUNTFIM */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_MASK_RXRUNTFIM_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_RECEIVE_INTERRUPT_MASK_Bits.RXRUNTFIM */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_MASK_RXRUNTFIM_OFF (7u)

/** \brief  Length for Ifx_ETH_MMC_RECEIVE_INTERRUPT_MASK_Bits.RXUCGFIM */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_MASK_RXUCGFIM_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_RECEIVE_INTERRUPT_MASK_Bits.RXUCGFIM */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_MASK_RXUCGFIM_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_RECEIVE_INTERRUPT_MASK_Bits.RXUCGFIM */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_MASK_RXUCGFIM_OFF (17u)

/** \brief  Length for Ifx_ETH_MMC_RECEIVE_INTERRUPT_MASK_Bits.RXUSIZEGFIM */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_MASK_RXUSIZEGFIM_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_RECEIVE_INTERRUPT_MASK_Bits.RXUSIZEGFIM */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_MASK_RXUSIZEGFIM_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_RECEIVE_INTERRUPT_MASK_Bits.RXUSIZEGFIM */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_MASK_RXUSIZEGFIM_OFF (9u)

/** \brief  Length for Ifx_ETH_MMC_RECEIVE_INTERRUPT_MASK_Bits.RXVLANGBFIM */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_MASK_RXVLANGBFIM_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_RECEIVE_INTERRUPT_MASK_Bits.RXVLANGBFIM */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_MASK_RXVLANGBFIM_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_RECEIVE_INTERRUPT_MASK_Bits.RXVLANGBFIM */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_MASK_RXVLANGBFIM_OFF (22u)

/** \brief  Length for Ifx_ETH_MMC_RECEIVE_INTERRUPT_MASK_Bits.RXWDOGFIM */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_MASK_RXWDOGFIM_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_RECEIVE_INTERRUPT_MASK_Bits.RXWDOGFIM */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_MASK_RXWDOGFIM_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_RECEIVE_INTERRUPT_MASK_Bits.RXWDOGFIM */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_MASK_RXWDOGFIM_OFF (23u)

/** \brief  Length for Ifx_ETH_MMC_RECEIVE_INTERRUPT_Bits.RX1024TMAXOCTGBFIS */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_RX1024TMAXOCTGBFIS_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_RECEIVE_INTERRUPT_Bits.RX1024TMAXOCTGBFIS */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_RX1024TMAXOCTGBFIS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_RECEIVE_INTERRUPT_Bits.RX1024TMAXOCTGBFIS */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_RX1024TMAXOCTGBFIS_OFF (16u)

/** \brief  Length for Ifx_ETH_MMC_RECEIVE_INTERRUPT_Bits.RX128T255OCTGBFIS */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_RX128T255OCTGBFIS_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_RECEIVE_INTERRUPT_Bits.RX128T255OCTGBFIS */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_RX128T255OCTGBFIS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_RECEIVE_INTERRUPT_Bits.RX128T255OCTGBFIS */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_RX128T255OCTGBFIS_OFF (13u)

/** \brief  Length for Ifx_ETH_MMC_RECEIVE_INTERRUPT_Bits.RX256T511OCTGBFIS */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_RX256T511OCTGBFIS_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_RECEIVE_INTERRUPT_Bits.RX256T511OCTGBFIS */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_RX256T511OCTGBFIS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_RECEIVE_INTERRUPT_Bits.RX256T511OCTGBFIS */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_RX256T511OCTGBFIS_OFF (14u)

/** \brief  Length for Ifx_ETH_MMC_RECEIVE_INTERRUPT_Bits.RX512T1023OCTGBFIS */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_RX512T1023OCTGBFIS_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_RECEIVE_INTERRUPT_Bits.RX512T1023OCTGBFIS */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_RX512T1023OCTGBFIS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_RECEIVE_INTERRUPT_Bits.RX512T1023OCTGBFIS */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_RX512T1023OCTGBFIS_OFF (15u)

/** \brief  Length for Ifx_ETH_MMC_RECEIVE_INTERRUPT_Bits.RX64OCTGBFIS */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_RX64OCTGBFIS_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_RECEIVE_INTERRUPT_Bits.RX64OCTGBFIS */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_RX64OCTGBFIS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_RECEIVE_INTERRUPT_Bits.RX64OCTGBFIS */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_RX64OCTGBFIS_OFF (11u)

/** \brief  Length for Ifx_ETH_MMC_RECEIVE_INTERRUPT_Bits.RX65T127OCTGBFIS */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_RX65T127OCTGBFIS_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_RECEIVE_INTERRUPT_Bits.RX65T127OCTGBFIS */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_RX65T127OCTGBFIS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_RECEIVE_INTERRUPT_Bits.RX65T127OCTGBFIS */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_RX65T127OCTGBFIS_OFF (12u)

/** \brief  Length for Ifx_ETH_MMC_RECEIVE_INTERRUPT_Bits.RXALGNERFIS */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_RXALGNERFIS_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_RECEIVE_INTERRUPT_Bits.RXALGNERFIS */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_RXALGNERFIS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_RECEIVE_INTERRUPT_Bits.RXALGNERFIS */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_RXALGNERFIS_OFF (6u)

/** \brief  Length for Ifx_ETH_MMC_RECEIVE_INTERRUPT_Bits.RXBCGFIS */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_RXBCGFIS_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_RECEIVE_INTERRUPT_Bits.RXBCGFIS */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_RXBCGFIS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_RECEIVE_INTERRUPT_Bits.RXBCGFIS */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_RXBCGFIS_OFF (3u)

/** \brief  Length for Ifx_ETH_MMC_RECEIVE_INTERRUPT_Bits.RXCRCERFIS */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_RXCRCERFIS_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_RECEIVE_INTERRUPT_Bits.RXCRCERFIS */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_RXCRCERFIS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_RECEIVE_INTERRUPT_Bits.RXCRCERFIS */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_RXCRCERFIS_OFF (5u)

/** \brief  Length for Ifx_ETH_MMC_RECEIVE_INTERRUPT_Bits.RXCTRLFIS */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_RXCTRLFIS_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_RECEIVE_INTERRUPT_Bits.RXCTRLFIS */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_RXCTRLFIS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_RECEIVE_INTERRUPT_Bits.RXCTRLFIS */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_RXCTRLFIS_OFF (25u)

/** \brief  Length for Ifx_ETH_MMC_RECEIVE_INTERRUPT_Bits.RXFOVFIS */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_RXFOVFIS_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_RECEIVE_INTERRUPT_Bits.RXFOVFIS */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_RXFOVFIS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_RECEIVE_INTERRUPT_Bits.RXFOVFIS */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_RXFOVFIS_OFF (21u)

/** \brief  Length for Ifx_ETH_MMC_RECEIVE_INTERRUPT_Bits.RXGBFRMIS */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_RXGBFRMIS_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_RECEIVE_INTERRUPT_Bits.RXGBFRMIS */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_RXGBFRMIS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_RECEIVE_INTERRUPT_Bits.RXGBFRMIS */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_RXGBFRMIS_OFF (0u)

/** \brief  Length for Ifx_ETH_MMC_RECEIVE_INTERRUPT_Bits.RXGBOCTIS */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_RXGBOCTIS_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_RECEIVE_INTERRUPT_Bits.RXGBOCTIS */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_RXGBOCTIS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_RECEIVE_INTERRUPT_Bits.RXGBOCTIS */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_RXGBOCTIS_OFF (1u)

/** \brief  Length for Ifx_ETH_MMC_RECEIVE_INTERRUPT_Bits.RXGOCTIS */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_RXGOCTIS_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_RECEIVE_INTERRUPT_Bits.RXGOCTIS */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_RXGOCTIS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_RECEIVE_INTERRUPT_Bits.RXGOCTIS */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_RXGOCTIS_OFF (2u)

/** \brief  Length for Ifx_ETH_MMC_RECEIVE_INTERRUPT_Bits.RXJABERFIS */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_RXJABERFIS_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_RECEIVE_INTERRUPT_Bits.RXJABERFIS */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_RXJABERFIS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_RECEIVE_INTERRUPT_Bits.RXJABERFIS */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_RXJABERFIS_OFF (8u)

/** \brief  Length for Ifx_ETH_MMC_RECEIVE_INTERRUPT_Bits.RXLENERFIS */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_RXLENERFIS_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_RECEIVE_INTERRUPT_Bits.RXLENERFIS */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_RXLENERFIS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_RECEIVE_INTERRUPT_Bits.RXLENERFIS */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_RXLENERFIS_OFF (18u)

/** \brief  Length for Ifx_ETH_MMC_RECEIVE_INTERRUPT_Bits.RXMCGFIS */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_RXMCGFIS_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_RECEIVE_INTERRUPT_Bits.RXMCGFIS */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_RXMCGFIS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_RECEIVE_INTERRUPT_Bits.RXMCGFIS */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_RXMCGFIS_OFF (4u)

/** \brief  Length for Ifx_ETH_MMC_RECEIVE_INTERRUPT_Bits.RXORANGEFIS */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_RXORANGEFIS_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_RECEIVE_INTERRUPT_Bits.RXORANGEFIS */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_RXORANGEFIS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_RECEIVE_INTERRUPT_Bits.RXORANGEFIS */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_RXORANGEFIS_OFF (19u)

/** \brief  Length for Ifx_ETH_MMC_RECEIVE_INTERRUPT_Bits.RXOSIZEGFIS */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_RXOSIZEGFIS_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_RECEIVE_INTERRUPT_Bits.RXOSIZEGFIS */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_RXOSIZEGFIS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_RECEIVE_INTERRUPT_Bits.RXOSIZEGFIS */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_RXOSIZEGFIS_OFF (10u)

/** \brief  Length for Ifx_ETH_MMC_RECEIVE_INTERRUPT_Bits.RXPAUSFIS */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_RXPAUSFIS_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_RECEIVE_INTERRUPT_Bits.RXPAUSFIS */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_RXPAUSFIS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_RECEIVE_INTERRUPT_Bits.RXPAUSFIS */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_RXPAUSFIS_OFF (20u)

/** \brief  Length for Ifx_ETH_MMC_RECEIVE_INTERRUPT_Bits.RXRCVERRFIS */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_RXRCVERRFIS_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_RECEIVE_INTERRUPT_Bits.RXRCVERRFIS */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_RXRCVERRFIS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_RECEIVE_INTERRUPT_Bits.RXRCVERRFIS */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_RXRCVERRFIS_OFF (24u)

/** \brief  Length for Ifx_ETH_MMC_RECEIVE_INTERRUPT_Bits.RXRUNTFIS */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_RXRUNTFIS_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_RECEIVE_INTERRUPT_Bits.RXRUNTFIS */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_RXRUNTFIS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_RECEIVE_INTERRUPT_Bits.RXRUNTFIS */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_RXRUNTFIS_OFF (7u)

/** \brief  Length for Ifx_ETH_MMC_RECEIVE_INTERRUPT_Bits.RXUCGFIS */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_RXUCGFIS_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_RECEIVE_INTERRUPT_Bits.RXUCGFIS */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_RXUCGFIS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_RECEIVE_INTERRUPT_Bits.RXUCGFIS */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_RXUCGFIS_OFF (17u)

/** \brief  Length for Ifx_ETH_MMC_RECEIVE_INTERRUPT_Bits.RXUSIZEGFIS */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_RXUSIZEGFIS_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_RECEIVE_INTERRUPT_Bits.RXUSIZEGFIS */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_RXUSIZEGFIS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_RECEIVE_INTERRUPT_Bits.RXUSIZEGFIS */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_RXUSIZEGFIS_OFF (9u)

/** \brief  Length for Ifx_ETH_MMC_RECEIVE_INTERRUPT_Bits.RXVLANGBFIS */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_RXVLANGBFIS_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_RECEIVE_INTERRUPT_Bits.RXVLANGBFIS */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_RXVLANGBFIS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_RECEIVE_INTERRUPT_Bits.RXVLANGBFIS */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_RXVLANGBFIS_OFF (22u)

/** \brief  Length for Ifx_ETH_MMC_RECEIVE_INTERRUPT_Bits.RXWDOGFIS */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_RXWDOGFIS_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_RECEIVE_INTERRUPT_Bits.RXWDOGFIS */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_RXWDOGFIS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_RECEIVE_INTERRUPT_Bits.RXWDOGFIS */
#define IFX_ETH_MMC_RECEIVE_INTERRUPT_RXWDOGFIS_OFF (23u)

/** \brief  Length for
 * Ifx_ETH_MMC_TRANSMIT_INTERRUPT_MASK_Bits.TX1024TMAXOCTGBFIM */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_MASK_TX1024TMAXOCTGBFIM_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_MASK_Bits.TX1024TMAXOCTGBFIM */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_MASK_TX1024TMAXOCTGBFIM_MSK (0x1u)

/** \brief  Offset for
 * Ifx_ETH_MMC_TRANSMIT_INTERRUPT_MASK_Bits.TX1024TMAXOCTGBFIM */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_MASK_TX1024TMAXOCTGBFIM_OFF (9u)

/** \brief  Length for
 * Ifx_ETH_MMC_TRANSMIT_INTERRUPT_MASK_Bits.TX128T255OCTGBFIM */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_MASK_TX128T255OCTGBFIM_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_MASK_Bits.TX128T255OCTGBFIM */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_MASK_TX128T255OCTGBFIM_MSK (0x1u)

/** \brief  Offset for
 * Ifx_ETH_MMC_TRANSMIT_INTERRUPT_MASK_Bits.TX128T255OCTGBFIM */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_MASK_TX128T255OCTGBFIM_OFF (6u)

/** \brief  Length for
 * Ifx_ETH_MMC_TRANSMIT_INTERRUPT_MASK_Bits.TX256T511OCTGBFIM */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_MASK_TX256T511OCTGBFIM_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_MASK_Bits.TX256T511OCTGBFIM */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_MASK_TX256T511OCTGBFIM_MSK (0x1u)

/** \brief  Offset for
 * Ifx_ETH_MMC_TRANSMIT_INTERRUPT_MASK_Bits.TX256T511OCTGBFIM */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_MASK_TX256T511OCTGBFIM_OFF (7u)

/** \brief  Length for
 * Ifx_ETH_MMC_TRANSMIT_INTERRUPT_MASK_Bits.TX512T1023OCTGBFIM */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_MASK_TX512T1023OCTGBFIM_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_MASK_Bits.TX512T1023OCTGBFIM */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_MASK_TX512T1023OCTGBFIM_MSK (0x1u)

/** \brief  Offset for
 * Ifx_ETH_MMC_TRANSMIT_INTERRUPT_MASK_Bits.TX512T1023OCTGBFIM */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_MASK_TX512T1023OCTGBFIM_OFF (8u)

/** \brief  Length for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_MASK_Bits.TX64OCTGBFIM */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_MASK_TX64OCTGBFIM_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_MASK_Bits.TX64OCTGBFIM */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_MASK_TX64OCTGBFIM_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_MASK_Bits.TX64OCTGBFIM */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_MASK_TX64OCTGBFIM_OFF (4u)

/** \brief  Length for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_MASK_Bits.TX65T127OCTGBFIM */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_MASK_TX65T127OCTGBFIM_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_MASK_Bits.TX65T127OCTGBFIM */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_MASK_TX65T127OCTGBFIM_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_MASK_Bits.TX65T127OCTGBFIM */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_MASK_TX65T127OCTGBFIM_OFF (5u)

/** \brief  Length for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_MASK_Bits.TXBCGBFIM */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_MASK_TXBCGBFIM_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_MASK_Bits.TXBCGBFIM */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_MASK_TXBCGBFIM_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_MASK_Bits.TXBCGBFIM */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_MASK_TXBCGBFIM_OFF (12u)

/** \brief  Length for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_MASK_Bits.TXBCGFIM */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_MASK_TXBCGFIM_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_MASK_Bits.TXBCGFIM */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_MASK_TXBCGFIM_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_MASK_Bits.TXBCGFIM */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_MASK_TXBCGFIM_OFF (2u)

/** \brief  Length for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_MASK_Bits.TXCARERFIM */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_MASK_TXCARERFIM_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_MASK_Bits.TXCARERFIM */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_MASK_TXCARERFIM_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_MASK_Bits.TXCARERFIM */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_MASK_TXCARERFIM_OFF (19u)

/** \brief  Length for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_MASK_Bits.TXDEFFIM */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_MASK_TXDEFFIM_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_MASK_Bits.TXDEFFIM */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_MASK_TXDEFFIM_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_MASK_Bits.TXDEFFIM */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_MASK_TXDEFFIM_OFF (16u)

/** \brief  Length for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_MASK_Bits.TXEXCOLFIM */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_MASK_TXEXCOLFIM_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_MASK_Bits.TXEXCOLFIM */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_MASK_TXEXCOLFIM_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_MASK_Bits.TXEXCOLFIM */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_MASK_TXEXCOLFIM_OFF (18u)

/** \brief  Length for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_MASK_Bits.TXEXDEFFIM */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_MASK_TXEXDEFFIM_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_MASK_Bits.TXEXDEFFIM */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_MASK_TXEXDEFFIM_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_MASK_Bits.TXEXDEFFIM */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_MASK_TXEXDEFFIM_OFF (22u)

/** \brief  Length for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_MASK_Bits.TXGBFRMIM */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_MASK_TXGBFRMIM_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_MASK_Bits.TXGBFRMIM */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_MASK_TXGBFRMIM_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_MASK_Bits.TXGBFRMIM */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_MASK_TXGBFRMIM_OFF (1u)

/** \brief  Length for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_MASK_Bits.TXGBOCTIM */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_MASK_TXGBOCTIM_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_MASK_Bits.TXGBOCTIM */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_MASK_TXGBOCTIM_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_MASK_Bits.TXGBOCTIM */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_MASK_TXGBOCTIM_OFF (0u)

/** \brief  Length for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_MASK_Bits.TXGFRMIM */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_MASK_TXGFRMIM_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_MASK_Bits.TXGFRMIM */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_MASK_TXGFRMIM_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_MASK_Bits.TXGFRMIM */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_MASK_TXGFRMIM_OFF (21u)

/** \brief  Length for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_MASK_Bits.TXGOCTIM */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_MASK_TXGOCTIM_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_MASK_Bits.TXGOCTIM */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_MASK_TXGOCTIM_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_MASK_Bits.TXGOCTIM */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_MASK_TXGOCTIM_OFF (20u)

/** \brief  Length for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_MASK_Bits.TXLATCOLFIM */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_MASK_TXLATCOLFIM_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_MASK_Bits.TXLATCOLFIM */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_MASK_TXLATCOLFIM_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_MASK_Bits.TXLATCOLFIM */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_MASK_TXLATCOLFIM_OFF (17u)

/** \brief  Length for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_MASK_Bits.TXMCGBFIM */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_MASK_TXMCGBFIM_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_MASK_Bits.TXMCGBFIM */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_MASK_TXMCGBFIM_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_MASK_Bits.TXMCGBFIM */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_MASK_TXMCGBFIM_OFF (11u)

/** \brief  Length for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_MASK_Bits.TXMCGFIM */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_MASK_TXMCGFIM_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_MASK_Bits.TXMCGFIM */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_MASK_TXMCGFIM_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_MASK_Bits.TXMCGFIM */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_MASK_TXMCGFIM_OFF (3u)

/** \brief  Length for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_MASK_Bits.TXMCOLGFIM */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_MASK_TXMCOLGFIM_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_MASK_Bits.TXMCOLGFIM */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_MASK_TXMCOLGFIM_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_MASK_Bits.TXMCOLGFIM */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_MASK_TXMCOLGFIM_OFF (15u)

/** \brief  Length for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_MASK_Bits.TXOSIZEGFIM */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_MASK_TXOSIZEGFIM_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_MASK_Bits.TXOSIZEGFIM */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_MASK_TXOSIZEGFIM_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_MASK_Bits.TXOSIZEGFIM */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_MASK_TXOSIZEGFIM_OFF (25u)

/** \brief  Length for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_MASK_Bits.TXPAUSFIM */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_MASK_TXPAUSFIM_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_MASK_Bits.TXPAUSFIM */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_MASK_TXPAUSFIM_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_MASK_Bits.TXPAUSFIM */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_MASK_TXPAUSFIM_OFF (23u)

/** \brief  Length for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_MASK_Bits.TXSCOLGFIM */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_MASK_TXSCOLGFIM_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_MASK_Bits.TXSCOLGFIM */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_MASK_TXSCOLGFIM_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_MASK_Bits.TXSCOLGFIM */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_MASK_TXSCOLGFIM_OFF (14u)

/** \brief  Length for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_MASK_Bits.TXUCGBFIM */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_MASK_TXUCGBFIM_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_MASK_Bits.TXUCGBFIM */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_MASK_TXUCGBFIM_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_MASK_Bits.TXUCGBFIM */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_MASK_TXUCGBFIM_OFF (10u)

/** \brief  Length for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_MASK_Bits.TXUFLOWERFIM */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_MASK_TXUFLOWERFIM_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_MASK_Bits.TXUFLOWERFIM */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_MASK_TXUFLOWERFIM_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_MASK_Bits.TXUFLOWERFIM */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_MASK_TXUFLOWERFIM_OFF (13u)

/** \brief  Length for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_MASK_Bits.TXVLANGFIM */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_MASK_TXVLANGFIM_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_MASK_Bits.TXVLANGFIM */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_MASK_TXVLANGFIM_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_MASK_Bits.TXVLANGFIM */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_MASK_TXVLANGFIM_OFF (24u)

/** \brief  Length for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_Bits.TX1024TMAXOCTGBFIS */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_TX1024TMAXOCTGBFIS_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_Bits.TX1024TMAXOCTGBFIS */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_TX1024TMAXOCTGBFIS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_Bits.TX1024TMAXOCTGBFIS */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_TX1024TMAXOCTGBFIS_OFF (9u)

/** \brief  Length for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_Bits.TX128T255OCTGBFIS */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_TX128T255OCTGBFIS_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_Bits.TX128T255OCTGBFIS */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_TX128T255OCTGBFIS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_Bits.TX128T255OCTGBFIS */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_TX128T255OCTGBFIS_OFF (6u)

/** \brief  Length for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_Bits.TX256T511OCTGBFIS */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_TX256T511OCTGBFIS_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_Bits.TX256T511OCTGBFIS */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_TX256T511OCTGBFIS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_Bits.TX256T511OCTGBFIS */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_TX256T511OCTGBFIS_OFF (7u)

/** \brief  Length for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_Bits.TX512T1023OCTGBFIS */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_TX512T1023OCTGBFIS_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_Bits.TX512T1023OCTGBFIS */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_TX512T1023OCTGBFIS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_Bits.TX512T1023OCTGBFIS */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_TX512T1023OCTGBFIS_OFF (8u)

/** \brief  Length for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_Bits.TX64OCTGBFIS */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_TX64OCTGBFIS_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_Bits.TX64OCTGBFIS */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_TX64OCTGBFIS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_Bits.TX64OCTGBFIS */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_TX64OCTGBFIS_OFF (4u)

/** \brief  Length for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_Bits.TX65T127OCTGBFIS */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_TX65T127OCTGBFIS_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_Bits.TX65T127OCTGBFIS */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_TX65T127OCTGBFIS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_Bits.TX65T127OCTGBFIS */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_TX65T127OCTGBFIS_OFF (5u)

/** \brief  Length for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_Bits.TXBCGBFIS */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_TXBCGBFIS_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_Bits.TXBCGBFIS */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_TXBCGBFIS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_Bits.TXBCGBFIS */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_TXBCGBFIS_OFF (12u)

/** \brief  Length for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_Bits.TXBCGFIS */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_TXBCGFIS_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_Bits.TXBCGFIS */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_TXBCGFIS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_Bits.TXBCGFIS */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_TXBCGFIS_OFF (2u)

/** \brief  Length for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_Bits.TXCARERFIS */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_TXCARERFIS_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_Bits.TXCARERFIS */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_TXCARERFIS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_Bits.TXCARERFIS */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_TXCARERFIS_OFF (19u)

/** \brief  Length for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_Bits.TXDEFFIS */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_TXDEFFIS_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_Bits.TXDEFFIS */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_TXDEFFIS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_Bits.TXDEFFIS */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_TXDEFFIS_OFF (16u)

/** \brief  Length for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_Bits.TXEXCOLFIS */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_TXEXCOLFIS_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_Bits.TXEXCOLFIS */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_TXEXCOLFIS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_Bits.TXEXCOLFIS */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_TXEXCOLFIS_OFF (18u)

/** \brief  Length for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_Bits.TXEXDEFFIS */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_TXEXDEFFIS_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_Bits.TXEXDEFFIS */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_TXEXDEFFIS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_Bits.TXEXDEFFIS */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_TXEXDEFFIS_OFF (22u)

/** \brief  Length for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_Bits.TXGBFRMIS */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_TXGBFRMIS_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_Bits.TXGBFRMIS */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_TXGBFRMIS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_Bits.TXGBFRMIS */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_TXGBFRMIS_OFF (1u)

/** \brief  Length for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_Bits.TXGBOCTIS */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_TXGBOCTIS_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_Bits.TXGBOCTIS */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_TXGBOCTIS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_Bits.TXGBOCTIS */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_TXGBOCTIS_OFF (0u)

/** \brief  Length for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_Bits.TXGFRMIS */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_TXGFRMIS_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_Bits.TXGFRMIS */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_TXGFRMIS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_Bits.TXGFRMIS */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_TXGFRMIS_OFF (21u)

/** \brief  Length for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_Bits.TXGOCTIS */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_TXGOCTIS_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_Bits.TXGOCTIS */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_TXGOCTIS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_Bits.TXGOCTIS */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_TXGOCTIS_OFF (20u)

/** \brief  Length for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_Bits.TXLATCOLFIS */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_TXLATCOLFIS_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_Bits.TXLATCOLFIS */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_TXLATCOLFIS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_Bits.TXLATCOLFIS */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_TXLATCOLFIS_OFF (17u)

/** \brief  Length for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_Bits.TXMCGBFIS */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_TXMCGBFIS_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_Bits.TXMCGBFIS */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_TXMCGBFIS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_Bits.TXMCGBFIS */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_TXMCGBFIS_OFF (11u)

/** \brief  Length for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_Bits.TXMCGFIS */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_TXMCGFIS_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_Bits.TXMCGFIS */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_TXMCGFIS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_Bits.TXMCGFIS */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_TXMCGFIS_OFF (3u)

/** \brief  Length for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_Bits.TXMCOLGFIS */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_TXMCOLGFIS_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_Bits.TXMCOLGFIS */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_TXMCOLGFIS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_Bits.TXMCOLGFIS */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_TXMCOLGFIS_OFF (15u)

/** \brief  Length for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_Bits.TXOSIZEGFIS */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_TXOSIZEGFIS_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_Bits.TXOSIZEGFIS */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_TXOSIZEGFIS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_Bits.TXOSIZEGFIS */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_TXOSIZEGFIS_OFF (25u)

/** \brief  Length for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_Bits.TXPAUSFIS */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_TXPAUSFIS_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_Bits.TXPAUSFIS */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_TXPAUSFIS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_Bits.TXPAUSFIS */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_TXPAUSFIS_OFF (23u)

/** \brief  Length for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_Bits.TXSCOLGFIS */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_TXSCOLGFIS_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_Bits.TXSCOLGFIS */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_TXSCOLGFIS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_Bits.TXSCOLGFIS */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_TXSCOLGFIS_OFF (14u)

/** \brief  Length for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_Bits.TXUCGBFIS */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_TXUCGBFIS_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_Bits.TXUCGBFIS */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_TXUCGBFIS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_Bits.TXUCGBFIS */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_TXUCGBFIS_OFF (10u)

/** \brief  Length for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_Bits.TXUFLOWERFIS */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_TXUFLOWERFIS_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_Bits.TXUFLOWERFIS */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_TXUFLOWERFIS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_Bits.TXUFLOWERFIS */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_TXUFLOWERFIS_OFF (13u)

/** \brief  Length for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_Bits.TXVLANGFIS */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_TXVLANGFIS_LEN (1u)

/** \brief  Mask for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_Bits.TXVLANGFIS */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_TXVLANGFIS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_MMC_TRANSMIT_INTERRUPT_Bits.TXVLANGFIS */
#define IFX_ETH_MMC_TRANSMIT_INTERRUPT_TXVLANGFIS_OFF (24u)

/** \brief  Length for Ifx_ETH_OPERATION_MODE_Bits.DFF */
#define IFX_ETH_OPERATION_MODE_DFF_LEN (1u)

/** \brief  Mask for Ifx_ETH_OPERATION_MODE_Bits.DFF */
#define IFX_ETH_OPERATION_MODE_DFF_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_OPERATION_MODE_Bits.DFF */
#define IFX_ETH_OPERATION_MODE_DFF_OFF (24u)

/** \brief  Length for Ifx_ETH_OPERATION_MODE_Bits.DT */
#define IFX_ETH_OPERATION_MODE_DT_LEN (1u)

/** \brief  Mask for Ifx_ETH_OPERATION_MODE_Bits.DT */
#define IFX_ETH_OPERATION_MODE_DT_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_OPERATION_MODE_Bits.DT */
#define IFX_ETH_OPERATION_MODE_DT_OFF (26u)

/** \brief  Length for Ifx_ETH_OPERATION_MODE_Bits.EFC */
#define IFX_ETH_OPERATION_MODE_EFC_LEN (1u)

/** \brief  Mask for Ifx_ETH_OPERATION_MODE_Bits.EFC */
#define IFX_ETH_OPERATION_MODE_EFC_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_OPERATION_MODE_Bits.EFC */
#define IFX_ETH_OPERATION_MODE_EFC_OFF (8u)

/** \brief  Length for Ifx_ETH_OPERATION_MODE_Bits.FEF */
#define IFX_ETH_OPERATION_MODE_FEF_LEN (1u)

/** \brief  Mask for Ifx_ETH_OPERATION_MODE_Bits.FEF */
#define IFX_ETH_OPERATION_MODE_FEF_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_OPERATION_MODE_Bits.FEF */
#define IFX_ETH_OPERATION_MODE_FEF_OFF (7u)

/** \brief  Length for Ifx_ETH_OPERATION_MODE_Bits.FTF */
#define IFX_ETH_OPERATION_MODE_FTF_LEN (1u)

/** \brief  Mask for Ifx_ETH_OPERATION_MODE_Bits.FTF */
#define IFX_ETH_OPERATION_MODE_FTF_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_OPERATION_MODE_Bits.FTF */
#define IFX_ETH_OPERATION_MODE_FTF_OFF (20u)

/** \brief  Length for Ifx_ETH_OPERATION_MODE_Bits.FUF */
#define IFX_ETH_OPERATION_MODE_FUF_LEN (1u)

/** \brief  Mask for Ifx_ETH_OPERATION_MODE_Bits.FUF */
#define IFX_ETH_OPERATION_MODE_FUF_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_OPERATION_MODE_Bits.FUF */
#define IFX_ETH_OPERATION_MODE_FUF_OFF (6u)

/** \brief  Length for Ifx_ETH_OPERATION_MODE_Bits.OSF */
#define IFX_ETH_OPERATION_MODE_OSF_LEN (1u)

/** \brief  Mask for Ifx_ETH_OPERATION_MODE_Bits.OSF */
#define IFX_ETH_OPERATION_MODE_OSF_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_OPERATION_MODE_Bits.OSF */
#define IFX_ETH_OPERATION_MODE_OSF_OFF (2u)

/** \brief  Length for Ifx_ETH_OPERATION_MODE_Bits.RFA_2 */
#define IFX_ETH_OPERATION_MODE_RFA_2_LEN (1u)

/** \brief  Mask for Ifx_ETH_OPERATION_MODE_Bits.RFA_2 */
#define IFX_ETH_OPERATION_MODE_RFA_2_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_OPERATION_MODE_Bits.RFA_2 */
#define IFX_ETH_OPERATION_MODE_RFA_2_OFF (23u)

/** \brief  Length for Ifx_ETH_OPERATION_MODE_Bits.RFA */
#define IFX_ETH_OPERATION_MODE_RFA_LEN (2u)

/** \brief  Mask for Ifx_ETH_OPERATION_MODE_Bits.RFA */
#define IFX_ETH_OPERATION_MODE_RFA_MSK (0x3u)

/** \brief  Offset for Ifx_ETH_OPERATION_MODE_Bits.RFA */
#define IFX_ETH_OPERATION_MODE_RFA_OFF (9u)

/** \brief  Length for Ifx_ETH_OPERATION_MODE_Bits.RFD_2 */
#define IFX_ETH_OPERATION_MODE_RFD_2_LEN (1u)

/** \brief  Mask for Ifx_ETH_OPERATION_MODE_Bits.RFD_2 */
#define IFX_ETH_OPERATION_MODE_RFD_2_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_OPERATION_MODE_Bits.RFD_2 */
#define IFX_ETH_OPERATION_MODE_RFD_2_OFF (22u)

/** \brief  Length for Ifx_ETH_OPERATION_MODE_Bits.RFD */
#define IFX_ETH_OPERATION_MODE_RFD_LEN (2u)

/** \brief  Mask for Ifx_ETH_OPERATION_MODE_Bits.RFD */
#define IFX_ETH_OPERATION_MODE_RFD_MSK (0x3u)

/** \brief  Offset for Ifx_ETH_OPERATION_MODE_Bits.RFD */
#define IFX_ETH_OPERATION_MODE_RFD_OFF (11u)

/** \brief  Length for Ifx_ETH_OPERATION_MODE_Bits.RSF */
#define IFX_ETH_OPERATION_MODE_RSF_LEN (1u)

/** \brief  Mask for Ifx_ETH_OPERATION_MODE_Bits.RSF */
#define IFX_ETH_OPERATION_MODE_RSF_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_OPERATION_MODE_Bits.RSF */
#define IFX_ETH_OPERATION_MODE_RSF_OFF (25u)

/** \brief  Length for Ifx_ETH_OPERATION_MODE_Bits.RTC */
#define IFX_ETH_OPERATION_MODE_RTC_LEN (2u)

/** \brief  Mask for Ifx_ETH_OPERATION_MODE_Bits.RTC */
#define IFX_ETH_OPERATION_MODE_RTC_MSK (0x3u)

/** \brief  Offset for Ifx_ETH_OPERATION_MODE_Bits.RTC */
#define IFX_ETH_OPERATION_MODE_RTC_OFF (3u)

/** \brief  Length for Ifx_ETH_OPERATION_MODE_Bits.SR */
#define IFX_ETH_OPERATION_MODE_SR_LEN (1u)

/** \brief  Mask for Ifx_ETH_OPERATION_MODE_Bits.SR */
#define IFX_ETH_OPERATION_MODE_SR_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_OPERATION_MODE_Bits.SR */
#define IFX_ETH_OPERATION_MODE_SR_OFF (1u)

/** \brief  Length for Ifx_ETH_OPERATION_MODE_Bits.ST */
#define IFX_ETH_OPERATION_MODE_ST_LEN (1u)

/** \brief  Mask for Ifx_ETH_OPERATION_MODE_Bits.ST */
#define IFX_ETH_OPERATION_MODE_ST_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_OPERATION_MODE_Bits.ST */
#define IFX_ETH_OPERATION_MODE_ST_OFF (13u)

/** \brief  Length for Ifx_ETH_OPERATION_MODE_Bits.TSF */
#define IFX_ETH_OPERATION_MODE_TSF_LEN (1u)

/** \brief  Mask for Ifx_ETH_OPERATION_MODE_Bits.TSF */
#define IFX_ETH_OPERATION_MODE_TSF_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_OPERATION_MODE_Bits.TSF */
#define IFX_ETH_OPERATION_MODE_TSF_OFF (21u)

/** \brief  Length for Ifx_ETH_OPERATION_MODE_Bits.TTC */
#define IFX_ETH_OPERATION_MODE_TTC_LEN (3u)

/** \brief  Mask for Ifx_ETH_OPERATION_MODE_Bits.TTC */
#define IFX_ETH_OPERATION_MODE_TTC_MSK (0x7u)

/** \brief  Offset for Ifx_ETH_OPERATION_MODE_Bits.TTC */
#define IFX_ETH_OPERATION_MODE_TTC_OFF (14u)

/** \brief  Length for Ifx_ETH_PMT_CONTROL_STATUS_Bits.GLBLUCAST */
#define IFX_ETH_PMT_CONTROL_STATUS_GLBLUCAST_LEN (1u)

/** \brief  Mask for Ifx_ETH_PMT_CONTROL_STATUS_Bits.GLBLUCAST */
#define IFX_ETH_PMT_CONTROL_STATUS_GLBLUCAST_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_PMT_CONTROL_STATUS_Bits.GLBLUCAST */
#define IFX_ETH_PMT_CONTROL_STATUS_GLBLUCAST_OFF (9u)

/** \brief  Length for Ifx_ETH_PMT_CONTROL_STATUS_Bits.MGKPKTEN */
#define IFX_ETH_PMT_CONTROL_STATUS_MGKPKTEN_LEN (1u)

/** \brief  Mask for Ifx_ETH_PMT_CONTROL_STATUS_Bits.MGKPKTEN */
#define IFX_ETH_PMT_CONTROL_STATUS_MGKPKTEN_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_PMT_CONTROL_STATUS_Bits.MGKPKTEN */
#define IFX_ETH_PMT_CONTROL_STATUS_MGKPKTEN_OFF (1u)

/** \brief  Length for Ifx_ETH_PMT_CONTROL_STATUS_Bits.MGKPRCVD */
#define IFX_ETH_PMT_CONTROL_STATUS_MGKPRCVD_LEN (1u)

/** \brief  Mask for Ifx_ETH_PMT_CONTROL_STATUS_Bits.MGKPRCVD */
#define IFX_ETH_PMT_CONTROL_STATUS_MGKPRCVD_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_PMT_CONTROL_STATUS_Bits.MGKPRCVD */
#define IFX_ETH_PMT_CONTROL_STATUS_MGKPRCVD_OFF (5u)

/** \brief  Length for Ifx_ETH_PMT_CONTROL_STATUS_Bits.PWRDWN */
#define IFX_ETH_PMT_CONTROL_STATUS_PWRDWN_LEN (1u)

/** \brief  Mask for Ifx_ETH_PMT_CONTROL_STATUS_Bits.PWRDWN */
#define IFX_ETH_PMT_CONTROL_STATUS_PWRDWN_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_PMT_CONTROL_STATUS_Bits.PWRDWN */
#define IFX_ETH_PMT_CONTROL_STATUS_PWRDWN_OFF (0u)

/** \brief  Length for Ifx_ETH_PMT_CONTROL_STATUS_Bits.RWKFILTRST */
#define IFX_ETH_PMT_CONTROL_STATUS_RWKFILTRST_LEN (1u)

/** \brief  Mask for Ifx_ETH_PMT_CONTROL_STATUS_Bits.RWKFILTRST */
#define IFX_ETH_PMT_CONTROL_STATUS_RWKFILTRST_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_PMT_CONTROL_STATUS_Bits.RWKFILTRST */
#define IFX_ETH_PMT_CONTROL_STATUS_RWKFILTRST_OFF (31u)

/** \brief  Length for Ifx_ETH_PMT_CONTROL_STATUS_Bits.RWKPKTEN */
#define IFX_ETH_PMT_CONTROL_STATUS_RWKPKTEN_LEN (1u)

/** \brief  Mask for Ifx_ETH_PMT_CONTROL_STATUS_Bits.RWKPKTEN */
#define IFX_ETH_PMT_CONTROL_STATUS_RWKPKTEN_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_PMT_CONTROL_STATUS_Bits.RWKPKTEN */
#define IFX_ETH_PMT_CONTROL_STATUS_RWKPKTEN_OFF (2u)

/** \brief  Length for Ifx_ETH_PMT_CONTROL_STATUS_Bits.RWKPRCVD */
#define IFX_ETH_PMT_CONTROL_STATUS_RWKPRCVD_LEN (1u)

/** \brief  Mask for Ifx_ETH_PMT_CONTROL_STATUS_Bits.RWKPRCVD */
#define IFX_ETH_PMT_CONTROL_STATUS_RWKPRCVD_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_PMT_CONTROL_STATUS_Bits.RWKPRCVD */
#define IFX_ETH_PMT_CONTROL_STATUS_RWKPRCVD_OFF (6u)

/** \brief  Length for Ifx_ETH_PPS_CONTROL_Bits.PPSCMD1 */
#define IFX_ETH_PPS_CONTROL_PPSCMD1_LEN (3u)

/** \brief  Mask for Ifx_ETH_PPS_CONTROL_Bits.PPSCMD1 */
#define IFX_ETH_PPS_CONTROL_PPSCMD1_MSK (0x7u)

/** \brief  Offset for Ifx_ETH_PPS_CONTROL_Bits.PPSCMD1 */
#define IFX_ETH_PPS_CONTROL_PPSCMD1_OFF (8u)

/** \brief  Length for Ifx_ETH_PPS_CONTROL_Bits.PPSCMD2 */
#define IFX_ETH_PPS_CONTROL_PPSCMD2_LEN (3u)

/** \brief  Mask for Ifx_ETH_PPS_CONTROL_Bits.PPSCMD2 */
#define IFX_ETH_PPS_CONTROL_PPSCMD2_MSK (0x7u)

/** \brief  Offset for Ifx_ETH_PPS_CONTROL_Bits.PPSCMD2 */
#define IFX_ETH_PPS_CONTROL_PPSCMD2_OFF (16u)

/** \brief  Length for Ifx_ETH_PPS_CONTROL_Bits.PPSCMD3 */
#define IFX_ETH_PPS_CONTROL_PPSCMD3_LEN (3u)

/** \brief  Mask for Ifx_ETH_PPS_CONTROL_Bits.PPSCMD3 */
#define IFX_ETH_PPS_CONTROL_PPSCMD3_MSK (0x7u)

/** \brief  Offset for Ifx_ETH_PPS_CONTROL_Bits.PPSCMD3 */
#define IFX_ETH_PPS_CONTROL_PPSCMD3_OFF (24u)

/** \brief  Length for Ifx_ETH_PPS_CONTROL_Bits.PPSCTRL_PPSCMD */
#define IFX_ETH_PPS_CONTROL_PPSCTRL_PPSCMD_LEN (4u)

/** \brief  Mask for Ifx_ETH_PPS_CONTROL_Bits.PPSCTRL_PPSCMD */
#define IFX_ETH_PPS_CONTROL_PPSCTRL_PPSCMD_MSK (0xfu)

/** \brief  Offset for Ifx_ETH_PPS_CONTROL_Bits.PPSCTRL_PPSCMD */
#define IFX_ETH_PPS_CONTROL_PPSCTRL_PPSCMD_OFF (0u)

/** \brief  Length for Ifx_ETH_PPS_CONTROL_Bits.PPSEN0 */
#define IFX_ETH_PPS_CONTROL_PPSEN0_LEN (1u)

/** \brief  Mask for Ifx_ETH_PPS_CONTROL_Bits.PPSEN0 */
#define IFX_ETH_PPS_CONTROL_PPSEN0_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_PPS_CONTROL_Bits.PPSEN0 */
#define IFX_ETH_PPS_CONTROL_PPSEN0_OFF (4u)

/** \brief  Length for Ifx_ETH_PPS_CONTROL_Bits.TRGTMODSEL0 */
#define IFX_ETH_PPS_CONTROL_TRGTMODSEL0_LEN (2u)

/** \brief  Mask for Ifx_ETH_PPS_CONTROL_Bits.TRGTMODSEL0 */
#define IFX_ETH_PPS_CONTROL_TRGTMODSEL0_MSK (0x3u)

/** \brief  Offset for Ifx_ETH_PPS_CONTROL_Bits.TRGTMODSEL0 */
#define IFX_ETH_PPS_CONTROL_TRGTMODSEL0_OFF (5u)

/** \brief  Length for Ifx_ETH_PPS_CONTROL_Bits.TRGTMODSEL1 */
#define IFX_ETH_PPS_CONTROL_TRGTMODSEL1_LEN (2u)

/** \brief  Mask for Ifx_ETH_PPS_CONTROL_Bits.TRGTMODSEL1 */
#define IFX_ETH_PPS_CONTROL_TRGTMODSEL1_MSK (0x3u)

/** \brief  Offset for Ifx_ETH_PPS_CONTROL_Bits.TRGTMODSEL1 */
#define IFX_ETH_PPS_CONTROL_TRGTMODSEL1_OFF (13u)

/** \brief  Length for Ifx_ETH_PPS_CONTROL_Bits.TRGTMODSEL2 */
#define IFX_ETH_PPS_CONTROL_TRGTMODSEL2_LEN (2u)

/** \brief  Mask for Ifx_ETH_PPS_CONTROL_Bits.TRGTMODSEL2 */
#define IFX_ETH_PPS_CONTROL_TRGTMODSEL2_MSK (0x3u)

/** \brief  Offset for Ifx_ETH_PPS_CONTROL_Bits.TRGTMODSEL2 */
#define IFX_ETH_PPS_CONTROL_TRGTMODSEL2_OFF (21u)

/** \brief  Length for Ifx_ETH_PPS_CONTROL_Bits.TRGTMODSEL3 */
#define IFX_ETH_PPS_CONTROL_TRGTMODSEL3_LEN (2u)

/** \brief  Mask for Ifx_ETH_PPS_CONTROL_Bits.TRGTMODSEL3 */
#define IFX_ETH_PPS_CONTROL_TRGTMODSEL3_MSK (0x3u)

/** \brief  Offset for Ifx_ETH_PPS_CONTROL_Bits.TRGTMODSEL3 */
#define IFX_ETH_PPS_CONTROL_TRGTMODSEL3_OFF (29u)

/** \brief  Length for Ifx_ETH_RECEIVE_DESCRIPTOR_LIST_ADDRESS_Bits.RDESLA */
#define IFX_ETH_RECEIVE_DESCRIPTOR_LIST_ADDRESS_RDESLA_LEN (30u)

/** \brief  Mask for Ifx_ETH_RECEIVE_DESCRIPTOR_LIST_ADDRESS_Bits.RDESLA */
#define IFX_ETH_RECEIVE_DESCRIPTOR_LIST_ADDRESS_RDESLA_MSK (0x3fffffffu)

/** \brief  Offset for Ifx_ETH_RECEIVE_DESCRIPTOR_LIST_ADDRESS_Bits.RDESLA */
#define IFX_ETH_RECEIVE_DESCRIPTOR_LIST_ADDRESS_RDESLA_OFF (2u)

/** \brief  Length for Ifx_ETH_RECEIVE_INTERRUPT_WATCHDOG_TIMER_Bits.RIWT */
#define IFX_ETH_RECEIVE_INTERRUPT_WATCHDOG_TIMER_RIWT_LEN (8u)

/** \brief  Mask for Ifx_ETH_RECEIVE_INTERRUPT_WATCHDOG_TIMER_Bits.RIWT */
#define IFX_ETH_RECEIVE_INTERRUPT_WATCHDOG_TIMER_RIWT_MSK (0xffu)

/** \brief  Offset for Ifx_ETH_RECEIVE_INTERRUPT_WATCHDOG_TIMER_Bits.RIWT */
#define IFX_ETH_RECEIVE_INTERRUPT_WATCHDOG_TIMER_RIWT_OFF (0u)

/** \brief  Length for Ifx_ETH_RECEIVE_POLL_DEMAND_Bits.RPD */
#define IFX_ETH_RECEIVE_POLL_DEMAND_RPD_LEN (32u)

/** \brief  Mask for Ifx_ETH_RECEIVE_POLL_DEMAND_Bits.RPD */
#define IFX_ETH_RECEIVE_POLL_DEMAND_RPD_MSK (0xffffffffu)

/** \brief  Offset for Ifx_ETH_RECEIVE_POLL_DEMAND_Bits.RPD */
#define IFX_ETH_RECEIVE_POLL_DEMAND_RPD_OFF (0u)

/** \brief  Length for Ifx_ETH_REMOTE_WAKE_UP_FRAME_FILTER_Bits.WKUPFRMFTR */
#define IFX_ETH_REMOTE_WAKE_UP_FRAME_FILTER_WKUPFRMFTR_LEN (32u)

/** \brief  Mask for Ifx_ETH_REMOTE_WAKE_UP_FRAME_FILTER_Bits.WKUPFRMFTR */
#define IFX_ETH_REMOTE_WAKE_UP_FRAME_FILTER_WKUPFRMFTR_MSK (0xffffffffu)

/** \brief  Offset for Ifx_ETH_REMOTE_WAKE_UP_FRAME_FILTER_Bits.WKUPFRMFTR */
#define IFX_ETH_REMOTE_WAKE_UP_FRAME_FILTER_WKUPFRMFTR_OFF (0u)

/** \brief  Length for
 * Ifx_ETH_RX_1024TOMAXOCTETS_FRAMES_GOOD_BAD_Bits.RX1024_MAXOCTGB */
#define IFX_ETH_RX_1024TOMAXOCTETS_FRAMES_GOOD_BAD_RX1024_MAXOCTGB_LEN (32u)

/** \brief  Mask for
 * Ifx_ETH_RX_1024TOMAXOCTETS_FRAMES_GOOD_BAD_Bits.RX1024_MAXOCTGB */
#define IFX_ETH_RX_1024TOMAXOCTETS_FRAMES_GOOD_BAD_RX1024_MAXOCTGB_MSK (0xffffffffu)

/** \brief  Offset for
 * Ifx_ETH_RX_1024TOMAXOCTETS_FRAMES_GOOD_BAD_Bits.RX1024_MAXOCTGB */
#define IFX_ETH_RX_1024TOMAXOCTETS_FRAMES_GOOD_BAD_RX1024_MAXOCTGB_OFF (0u)

/** \brief  Length for
 * Ifx_ETH_RX_128TO255OCTETS_FRAMES_GOOD_BAD_Bits.RX128_255OCTGB */
#define IFX_ETH_RX_128TO255OCTETS_FRAMES_GOOD_BAD_RX128_255OCTGB_LEN (32u)

/** \brief  Mask for
 * Ifx_ETH_RX_128TO255OCTETS_FRAMES_GOOD_BAD_Bits.RX128_255OCTGB */
#define IFX_ETH_RX_128TO255OCTETS_FRAMES_GOOD_BAD_RX128_255OCTGB_MSK (0xffffffffu)

/** \brief  Offset for
 * Ifx_ETH_RX_128TO255OCTETS_FRAMES_GOOD_BAD_Bits.RX128_255OCTGB */
#define IFX_ETH_RX_128TO255OCTETS_FRAMES_GOOD_BAD_RX128_255OCTGB_OFF (0u)

/** \brief  Length for
 * Ifx_ETH_RX_256TO511OCTETS_FRAMES_GOOD_BAD_Bits.RX256_511OCTGB */
#define IFX_ETH_RX_256TO511OCTETS_FRAMES_GOOD_BAD_RX256_511OCTGB_LEN (32u)

/** \brief  Mask for
 * Ifx_ETH_RX_256TO511OCTETS_FRAMES_GOOD_BAD_Bits.RX256_511OCTGB */
#define IFX_ETH_RX_256TO511OCTETS_FRAMES_GOOD_BAD_RX256_511OCTGB_MSK (0xffffffffu)

/** \brief  Offset for
 * Ifx_ETH_RX_256TO511OCTETS_FRAMES_GOOD_BAD_Bits.RX256_511OCTGB */
#define IFX_ETH_RX_256TO511OCTETS_FRAMES_GOOD_BAD_RX256_511OCTGB_OFF (0u)

/** \brief  Length for
 * Ifx_ETH_RX_512TO1023OCTETS_FRAMES_GOOD_BAD_Bits.RX512_1023OCTGB */
#define IFX_ETH_RX_512TO1023OCTETS_FRAMES_GOOD_BAD_RX512_1023OCTGB_LEN (32u)

/** \brief  Mask for
 * Ifx_ETH_RX_512TO1023OCTETS_FRAMES_GOOD_BAD_Bits.RX512_1023OCTGB */
#define IFX_ETH_RX_512TO1023OCTETS_FRAMES_GOOD_BAD_RX512_1023OCTGB_MSK (0xffffffffu)

/** \brief  Offset for
 * Ifx_ETH_RX_512TO1023OCTETS_FRAMES_GOOD_BAD_Bits.RX512_1023OCTGB */
#define IFX_ETH_RX_512TO1023OCTETS_FRAMES_GOOD_BAD_RX512_1023OCTGB_OFF (0u)

/** \brief  Length for Ifx_ETH_RX_64OCTETS_FRAMES_GOOD_BAD_Bits.RX64OCTGB */
#define IFX_ETH_RX_64OCTETS_FRAMES_GOOD_BAD_RX64OCTGB_LEN (32u)

/** \brief  Mask for Ifx_ETH_RX_64OCTETS_FRAMES_GOOD_BAD_Bits.RX64OCTGB */
#define IFX_ETH_RX_64OCTETS_FRAMES_GOOD_BAD_RX64OCTGB_MSK (0xffffffffu)

/** \brief  Offset for Ifx_ETH_RX_64OCTETS_FRAMES_GOOD_BAD_Bits.RX64OCTGB */
#define IFX_ETH_RX_64OCTETS_FRAMES_GOOD_BAD_RX64OCTGB_OFF (0u)

/** \brief  Length for
 * Ifx_ETH_RX_65TO127OCTETS_FRAMES_GOOD_BAD_Bits.RX65_127OCTGB */
#define IFX_ETH_RX_65TO127OCTETS_FRAMES_GOOD_BAD_RX65_127OCTGB_LEN (32u)

/** \brief  Mask for Ifx_ETH_RX_65TO127OCTETS_FRAMES_GOOD_BAD_Bits.RX65_127OCTGB */
#define IFX_ETH_RX_65TO127OCTETS_FRAMES_GOOD_BAD_RX65_127OCTGB_MSK (0xffffffffu)

/** \brief  Offset for
 * Ifx_ETH_RX_65TO127OCTETS_FRAMES_GOOD_BAD_Bits.RX65_127OCTGB */
#define IFX_ETH_RX_65TO127OCTETS_FRAMES_GOOD_BAD_RX65_127OCTGB_OFF (0u)

/** \brief  Length for Ifx_ETH_RX_ALIGNMENT_ERROR_FRAMES_Bits.RXALGNERR */
#define IFX_ETH_RX_ALIGNMENT_ERROR_FRAMES_RXALGNERR_LEN (32u)

/** \brief  Mask for Ifx_ETH_RX_ALIGNMENT_ERROR_FRAMES_Bits.RXALGNERR */
#define IFX_ETH_RX_ALIGNMENT_ERROR_FRAMES_RXALGNERR_MSK (0xffffffffu)

/** \brief  Offset for Ifx_ETH_RX_ALIGNMENT_ERROR_FRAMES_Bits.RXALGNERR */
#define IFX_ETH_RX_ALIGNMENT_ERROR_FRAMES_RXALGNERR_OFF (0u)

/** \brief  Length for Ifx_ETH_RX_BROADCAST_FRAMES_GOOD_Bits.RXBCASTG */
#define IFX_ETH_RX_BROADCAST_FRAMES_GOOD_RXBCASTG_LEN (32u)

/** \brief  Mask for Ifx_ETH_RX_BROADCAST_FRAMES_GOOD_Bits.RXBCASTG */
#define IFX_ETH_RX_BROADCAST_FRAMES_GOOD_RXBCASTG_MSK (0xffffffffu)

/** \brief  Offset for Ifx_ETH_RX_BROADCAST_FRAMES_GOOD_Bits.RXBCASTG */
#define IFX_ETH_RX_BROADCAST_FRAMES_GOOD_RXBCASTG_OFF (0u)

/** \brief  Length for Ifx_ETH_RX_CONTROL_FRAMES_GOOD_Bits.RXCTRLG */
#define IFX_ETH_RX_CONTROL_FRAMES_GOOD_RXCTRLG_LEN (32u)

/** \brief  Mask for Ifx_ETH_RX_CONTROL_FRAMES_GOOD_Bits.RXCTRLG */
#define IFX_ETH_RX_CONTROL_FRAMES_GOOD_RXCTRLG_MSK (0xffffffffu)

/** \brief  Offset for Ifx_ETH_RX_CONTROL_FRAMES_GOOD_Bits.RXCTRLG */
#define IFX_ETH_RX_CONTROL_FRAMES_GOOD_RXCTRLG_OFF (0u)

/** \brief  Length for Ifx_ETH_RX_CRC_ERROR_FRAMES_Bits.RXCRCERR */
#define IFX_ETH_RX_CRC_ERROR_FRAMES_RXCRCERR_LEN (32u)

/** \brief  Mask for Ifx_ETH_RX_CRC_ERROR_FRAMES_Bits.RXCRCERR */
#define IFX_ETH_RX_CRC_ERROR_FRAMES_RXCRCERR_MSK (0xffffffffu)

/** \brief  Offset for Ifx_ETH_RX_CRC_ERROR_FRAMES_Bits.RXCRCERR */
#define IFX_ETH_RX_CRC_ERROR_FRAMES_RXCRCERR_OFF (0u)

/** \brief  Length for Ifx_ETH_RX_FIFO_OVERFLOW_FRAMES_Bits.RXFIFOOVFL */
#define IFX_ETH_RX_FIFO_OVERFLOW_FRAMES_RXFIFOOVFL_LEN (32u)

/** \brief  Mask for Ifx_ETH_RX_FIFO_OVERFLOW_FRAMES_Bits.RXFIFOOVFL */
#define IFX_ETH_RX_FIFO_OVERFLOW_FRAMES_RXFIFOOVFL_MSK (0xffffffffu)

/** \brief  Offset for Ifx_ETH_RX_FIFO_OVERFLOW_FRAMES_Bits.RXFIFOOVFL */
#define IFX_ETH_RX_FIFO_OVERFLOW_FRAMES_RXFIFOOVFL_OFF (0u)

/** \brief  Length for Ifx_ETH_RX_FRAMES_COUNT_GOOD_BAD_Bits.RXFRMGB */
#define IFX_ETH_RX_FRAMES_COUNT_GOOD_BAD_RXFRMGB_LEN (32u)

/** \brief  Mask for Ifx_ETH_RX_FRAMES_COUNT_GOOD_BAD_Bits.RXFRMGB */
#define IFX_ETH_RX_FRAMES_COUNT_GOOD_BAD_RXFRMGB_MSK (0xffffffffu)

/** \brief  Offset for Ifx_ETH_RX_FRAMES_COUNT_GOOD_BAD_Bits.RXFRMGB */
#define IFX_ETH_RX_FRAMES_COUNT_GOOD_BAD_RXFRMGB_OFF (0u)

/** \brief  Length for Ifx_ETH_RX_JABBER_ERROR_FRAMES_Bits.RXJABERR */
#define IFX_ETH_RX_JABBER_ERROR_FRAMES_RXJABERR_LEN (32u)

/** \brief  Mask for Ifx_ETH_RX_JABBER_ERROR_FRAMES_Bits.RXJABERR */
#define IFX_ETH_RX_JABBER_ERROR_FRAMES_RXJABERR_MSK (0xffffffffu)

/** \brief  Offset for Ifx_ETH_RX_JABBER_ERROR_FRAMES_Bits.RXJABERR */
#define IFX_ETH_RX_JABBER_ERROR_FRAMES_RXJABERR_OFF (0u)

/** \brief  Length for Ifx_ETH_RX_LENGTH_ERROR_FRAMES_Bits.RXLENERR */
#define IFX_ETH_RX_LENGTH_ERROR_FRAMES_RXLENERR_LEN (32u)

/** \brief  Mask for Ifx_ETH_RX_LENGTH_ERROR_FRAMES_Bits.RXLENERR */
#define IFX_ETH_RX_LENGTH_ERROR_FRAMES_RXLENERR_MSK (0xffffffffu)

/** \brief  Offset for Ifx_ETH_RX_LENGTH_ERROR_FRAMES_Bits.RXLENERR */
#define IFX_ETH_RX_LENGTH_ERROR_FRAMES_RXLENERR_OFF (0u)

/** \brief  Length for Ifx_ETH_RX_MULTICAST_FRAMES_GOOD_Bits.RXMCASTG */
#define IFX_ETH_RX_MULTICAST_FRAMES_GOOD_RXMCASTG_LEN (32u)

/** \brief  Mask for Ifx_ETH_RX_MULTICAST_FRAMES_GOOD_Bits.RXMCASTG */
#define IFX_ETH_RX_MULTICAST_FRAMES_GOOD_RXMCASTG_MSK (0xffffffffu)

/** \brief  Offset for Ifx_ETH_RX_MULTICAST_FRAMES_GOOD_Bits.RXMCASTG */
#define IFX_ETH_RX_MULTICAST_FRAMES_GOOD_RXMCASTG_OFF (0u)

/** \brief  Length for Ifx_ETH_RX_OCTET_COUNT_GOOD_BAD_Bits.RXOCTGB */
#define IFX_ETH_RX_OCTET_COUNT_GOOD_BAD_RXOCTGB_LEN (32u)

/** \brief  Mask for Ifx_ETH_RX_OCTET_COUNT_GOOD_BAD_Bits.RXOCTGB */
#define IFX_ETH_RX_OCTET_COUNT_GOOD_BAD_RXOCTGB_MSK (0xffffffffu)

/** \brief  Offset for Ifx_ETH_RX_OCTET_COUNT_GOOD_BAD_Bits.RXOCTGB */
#define IFX_ETH_RX_OCTET_COUNT_GOOD_BAD_RXOCTGB_OFF (0u)

/** \brief  Length for Ifx_ETH_RX_OCTET_COUNT_GOOD_Bits.RXOCTG */
#define IFX_ETH_RX_OCTET_COUNT_GOOD_RXOCTG_LEN (32u)

/** \brief  Mask for Ifx_ETH_RX_OCTET_COUNT_GOOD_Bits.RXOCTG */
#define IFX_ETH_RX_OCTET_COUNT_GOOD_RXOCTG_MSK (0xffffffffu)

/** \brief  Offset for Ifx_ETH_RX_OCTET_COUNT_GOOD_Bits.RXOCTG */
#define IFX_ETH_RX_OCTET_COUNT_GOOD_RXOCTG_OFF (0u)

/** \brief  Length for Ifx_ETH_RX_OUT_OF_RANGE_TYPE_FRAMES_Bits.RXOUTOFRNG */
#define IFX_ETH_RX_OUT_OF_RANGE_TYPE_FRAMES_RXOUTOFRNG_LEN (32u)

/** \brief  Mask for Ifx_ETH_RX_OUT_OF_RANGE_TYPE_FRAMES_Bits.RXOUTOFRNG */
#define IFX_ETH_RX_OUT_OF_RANGE_TYPE_FRAMES_RXOUTOFRNG_MSK (0xffffffffu)

/** \brief  Offset for Ifx_ETH_RX_OUT_OF_RANGE_TYPE_FRAMES_Bits.RXOUTOFRNG */
#define IFX_ETH_RX_OUT_OF_RANGE_TYPE_FRAMES_RXOUTOFRNG_OFF (0u)

/** \brief  Length for Ifx_ETH_RX_OVERSIZE_FRAMES_GOOD_Bits.RXOVERSZG */
#define IFX_ETH_RX_OVERSIZE_FRAMES_GOOD_RXOVERSZG_LEN (32u)

/** \brief  Mask for Ifx_ETH_RX_OVERSIZE_FRAMES_GOOD_Bits.RXOVERSZG */
#define IFX_ETH_RX_OVERSIZE_FRAMES_GOOD_RXOVERSZG_MSK (0xffffffffu)

/** \brief  Offset for Ifx_ETH_RX_OVERSIZE_FRAMES_GOOD_Bits.RXOVERSZG */
#define IFX_ETH_RX_OVERSIZE_FRAMES_GOOD_RXOVERSZG_OFF (0u)

/** \brief  Length for Ifx_ETH_RX_PAUSE_FRAMES_Bits.RXPAUSEFRM */
#define IFX_ETH_RX_PAUSE_FRAMES_RXPAUSEFRM_LEN (32u)

/** \brief  Mask for Ifx_ETH_RX_PAUSE_FRAMES_Bits.RXPAUSEFRM */
#define IFX_ETH_RX_PAUSE_FRAMES_RXPAUSEFRM_MSK (0xffffffffu)

/** \brief  Offset for Ifx_ETH_RX_PAUSE_FRAMES_Bits.RXPAUSEFRM */
#define IFX_ETH_RX_PAUSE_FRAMES_RXPAUSEFRM_OFF (0u)

/** \brief  Length for Ifx_ETH_RX_RECEIVE_ERROR_FRAMES_Bits.RXRCVERR */
#define IFX_ETH_RX_RECEIVE_ERROR_FRAMES_RXRCVERR_LEN (32u)

/** \brief  Mask for Ifx_ETH_RX_RECEIVE_ERROR_FRAMES_Bits.RXRCVERR */
#define IFX_ETH_RX_RECEIVE_ERROR_FRAMES_RXRCVERR_MSK (0xffffffffu)

/** \brief  Offset for Ifx_ETH_RX_RECEIVE_ERROR_FRAMES_Bits.RXRCVERR */
#define IFX_ETH_RX_RECEIVE_ERROR_FRAMES_RXRCVERR_OFF (0u)

/** \brief  Length for Ifx_ETH_RX_RUNT_ERROR_FRAMES_Bits.RXRUNTERR */
#define IFX_ETH_RX_RUNT_ERROR_FRAMES_RXRUNTERR_LEN (32u)

/** \brief  Mask for Ifx_ETH_RX_RUNT_ERROR_FRAMES_Bits.RXRUNTERR */
#define IFX_ETH_RX_RUNT_ERROR_FRAMES_RXRUNTERR_MSK (0xffffffffu)

/** \brief  Offset for Ifx_ETH_RX_RUNT_ERROR_FRAMES_Bits.RXRUNTERR */
#define IFX_ETH_RX_RUNT_ERROR_FRAMES_RXRUNTERR_OFF (0u)

/** \brief  Length for Ifx_ETH_RX_UNDERSIZE_FRAMES_GOOD_Bits.RXUNDERSZG */
#define IFX_ETH_RX_UNDERSIZE_FRAMES_GOOD_RXUNDERSZG_LEN (32u)

/** \brief  Mask for Ifx_ETH_RX_UNDERSIZE_FRAMES_GOOD_Bits.RXUNDERSZG */
#define IFX_ETH_RX_UNDERSIZE_FRAMES_GOOD_RXUNDERSZG_MSK (0xffffffffu)

/** \brief  Offset for Ifx_ETH_RX_UNDERSIZE_FRAMES_GOOD_Bits.RXUNDERSZG */
#define IFX_ETH_RX_UNDERSIZE_FRAMES_GOOD_RXUNDERSZG_OFF (0u)

/** \brief  Length for Ifx_ETH_RX_UNICAST_FRAMES_GOOD_Bits.RXUCASTG */
#define IFX_ETH_RX_UNICAST_FRAMES_GOOD_RXUCASTG_LEN (32u)

/** \brief  Mask for Ifx_ETH_RX_UNICAST_FRAMES_GOOD_Bits.RXUCASTG */
#define IFX_ETH_RX_UNICAST_FRAMES_GOOD_RXUCASTG_MSK (0xffffffffu)

/** \brief  Offset for Ifx_ETH_RX_UNICAST_FRAMES_GOOD_Bits.RXUCASTG */
#define IFX_ETH_RX_UNICAST_FRAMES_GOOD_RXUCASTG_OFF (0u)

/** \brief  Length for Ifx_ETH_RX_VLAN_FRAMES_GOOD_BAD_Bits.RXVLANFRGB */
#define IFX_ETH_RX_VLAN_FRAMES_GOOD_BAD_RXVLANFRGB_LEN (32u)

/** \brief  Mask for Ifx_ETH_RX_VLAN_FRAMES_GOOD_BAD_Bits.RXVLANFRGB */
#define IFX_ETH_RX_VLAN_FRAMES_GOOD_BAD_RXVLANFRGB_MSK (0xffffffffu)

/** \brief  Offset for Ifx_ETH_RX_VLAN_FRAMES_GOOD_BAD_Bits.RXVLANFRGB */
#define IFX_ETH_RX_VLAN_FRAMES_GOOD_BAD_RXVLANFRGB_OFF (0u)

/** \brief  Length for Ifx_ETH_RX_WATCHDOG_ERROR_FRAMES_Bits.RXWDGERR */
#define IFX_ETH_RX_WATCHDOG_ERROR_FRAMES_RXWDGERR_LEN (32u)

/** \brief  Mask for Ifx_ETH_RX_WATCHDOG_ERROR_FRAMES_Bits.RXWDGERR */
#define IFX_ETH_RX_WATCHDOG_ERROR_FRAMES_RXWDGERR_MSK (0xffffffffu)

/** \brief  Offset for Ifx_ETH_RX_WATCHDOG_ERROR_FRAMES_Bits.RXWDGERR */
#define IFX_ETH_RX_WATCHDOG_ERROR_FRAMES_RXWDGERR_OFF (0u)

/** \brief  Length for Ifx_ETH_RXICMP_ERROR_FRAMES_Bits.RXICMPERRFRM */
#define IFX_ETH_RXICMP_ERROR_FRAMES_RXICMPERRFRM_LEN (32u)

/** \brief  Mask for Ifx_ETH_RXICMP_ERROR_FRAMES_Bits.RXICMPERRFRM */
#define IFX_ETH_RXICMP_ERROR_FRAMES_RXICMPERRFRM_MSK (0xffffffffu)

/** \brief  Offset for Ifx_ETH_RXICMP_ERROR_FRAMES_Bits.RXICMPERRFRM */
#define IFX_ETH_RXICMP_ERROR_FRAMES_RXICMPERRFRM_OFF (0u)

/** \brief  Length for Ifx_ETH_RXICMP_ERROR_OCTETS_Bits.RXICMPERROCT */
#define IFX_ETH_RXICMP_ERROR_OCTETS_RXICMPERROCT_LEN (32u)

/** \brief  Mask for Ifx_ETH_RXICMP_ERROR_OCTETS_Bits.RXICMPERROCT */
#define IFX_ETH_RXICMP_ERROR_OCTETS_RXICMPERROCT_MSK (0xffffffffu)

/** \brief  Offset for Ifx_ETH_RXICMP_ERROR_OCTETS_Bits.RXICMPERROCT */
#define IFX_ETH_RXICMP_ERROR_OCTETS_RXICMPERROCT_OFF (0u)

/** \brief  Length for Ifx_ETH_RXICMP_GOOD_FRAMES_Bits.RXICMPGDFRM */
#define IFX_ETH_RXICMP_GOOD_FRAMES_RXICMPGDFRM_LEN (32u)

/** \brief  Mask for Ifx_ETH_RXICMP_GOOD_FRAMES_Bits.RXICMPGDFRM */
#define IFX_ETH_RXICMP_GOOD_FRAMES_RXICMPGDFRM_MSK (0xffffffffu)

/** \brief  Offset for Ifx_ETH_RXICMP_GOOD_FRAMES_Bits.RXICMPGDFRM */
#define IFX_ETH_RXICMP_GOOD_FRAMES_RXICMPGDFRM_OFF (0u)

/** \brief  Length for Ifx_ETH_RXICMP_GOOD_OCTETS_Bits.RXICMPGDOCT */
#define IFX_ETH_RXICMP_GOOD_OCTETS_RXICMPGDOCT_LEN (32u)

/** \brief  Mask for Ifx_ETH_RXICMP_GOOD_OCTETS_Bits.RXICMPGDOCT */
#define IFX_ETH_RXICMP_GOOD_OCTETS_RXICMPGDOCT_MSK (0xffffffffu)

/** \brief  Offset for Ifx_ETH_RXICMP_GOOD_OCTETS_Bits.RXICMPGDOCT */
#define IFX_ETH_RXICMP_GOOD_OCTETS_RXICMPGDOCT_OFF (0u)

/** \brief  Length for Ifx_ETH_RXIPV4_FRAGMENTED_FRAMES_Bits.RXIPV4FRAGFRM */
#define IFX_ETH_RXIPV4_FRAGMENTED_FRAMES_RXIPV4FRAGFRM_LEN (32u)

/** \brief  Mask for Ifx_ETH_RXIPV4_FRAGMENTED_FRAMES_Bits.RXIPV4FRAGFRM */
#define IFX_ETH_RXIPV4_FRAGMENTED_FRAMES_RXIPV4FRAGFRM_MSK (0xffffffffu)

/** \brief  Offset for Ifx_ETH_RXIPV4_FRAGMENTED_FRAMES_Bits.RXIPV4FRAGFRM */
#define IFX_ETH_RXIPV4_FRAGMENTED_FRAMES_RXIPV4FRAGFRM_OFF (0u)

/** \brief  Length for Ifx_ETH_RXIPV4_FRAGMENTED_OCTETS_Bits.RXIPV4FRAGOCT */
#define IFX_ETH_RXIPV4_FRAGMENTED_OCTETS_RXIPV4FRAGOCT_LEN (32u)

/** \brief  Mask for Ifx_ETH_RXIPV4_FRAGMENTED_OCTETS_Bits.RXIPV4FRAGOCT */
#define IFX_ETH_RXIPV4_FRAGMENTED_OCTETS_RXIPV4FRAGOCT_MSK (0xffffffffu)

/** \brief  Offset for Ifx_ETH_RXIPV4_FRAGMENTED_OCTETS_Bits.RXIPV4FRAGOCT */
#define IFX_ETH_RXIPV4_FRAGMENTED_OCTETS_RXIPV4FRAGOCT_OFF (0u)

/** \brief  Length for Ifx_ETH_RXIPV4_GOOD_FRAMES_Bits.RXIPV4GDFRM */
#define IFX_ETH_RXIPV4_GOOD_FRAMES_RXIPV4GDFRM_LEN (32u)

/** \brief  Mask for Ifx_ETH_RXIPV4_GOOD_FRAMES_Bits.RXIPV4GDFRM */
#define IFX_ETH_RXIPV4_GOOD_FRAMES_RXIPV4GDFRM_MSK (0xffffffffu)

/** \brief  Offset for Ifx_ETH_RXIPV4_GOOD_FRAMES_Bits.RXIPV4GDFRM */
#define IFX_ETH_RXIPV4_GOOD_FRAMES_RXIPV4GDFRM_OFF (0u)

/** \brief  Length for Ifx_ETH_RXIPV4_GOOD_OCTETS_Bits.RXIPV4GDOCT */
#define IFX_ETH_RXIPV4_GOOD_OCTETS_RXIPV4GDOCT_LEN (32u)

/** \brief  Mask for Ifx_ETH_RXIPV4_GOOD_OCTETS_Bits.RXIPV4GDOCT */
#define IFX_ETH_RXIPV4_GOOD_OCTETS_RXIPV4GDOCT_MSK (0xffffffffu)

/** \brief  Offset for Ifx_ETH_RXIPV4_GOOD_OCTETS_Bits.RXIPV4GDOCT */
#define IFX_ETH_RXIPV4_GOOD_OCTETS_RXIPV4GDOCT_OFF (0u)

/** \brief  Length for Ifx_ETH_RXIPV4_HEADER_ERROR_FRAMES_Bits.RXIPV4HDRERRFRM */
#define IFX_ETH_RXIPV4_HEADER_ERROR_FRAMES_RXIPV4HDRERRFRM_LEN (32u)

/** \brief  Mask for Ifx_ETH_RXIPV4_HEADER_ERROR_FRAMES_Bits.RXIPV4HDRERRFRM */
#define IFX_ETH_RXIPV4_HEADER_ERROR_FRAMES_RXIPV4HDRERRFRM_MSK (0xffffffffu)

/** \brief  Offset for Ifx_ETH_RXIPV4_HEADER_ERROR_FRAMES_Bits.RXIPV4HDRERRFRM */
#define IFX_ETH_RXIPV4_HEADER_ERROR_FRAMES_RXIPV4HDRERRFRM_OFF (0u)

/** \brief  Length for Ifx_ETH_RXIPV4_HEADER_ERROR_OCTETS_Bits.RXIPV4HDRERROCT */
#define IFX_ETH_RXIPV4_HEADER_ERROR_OCTETS_RXIPV4HDRERROCT_LEN (32u)

/** \brief  Mask for Ifx_ETH_RXIPV4_HEADER_ERROR_OCTETS_Bits.RXIPV4HDRERROCT */
#define IFX_ETH_RXIPV4_HEADER_ERROR_OCTETS_RXIPV4HDRERROCT_MSK (0xffffffffu)

/** \brief  Offset for Ifx_ETH_RXIPV4_HEADER_ERROR_OCTETS_Bits.RXIPV4HDRERROCT */
#define IFX_ETH_RXIPV4_HEADER_ERROR_OCTETS_RXIPV4HDRERROCT_OFF (0u)

/** \brief  Length for Ifx_ETH_RXIPV4_NO_PAYLOAD_FRAMES_Bits.RXIPV4NOPAYFRM */
#define IFX_ETH_RXIPV4_NO_PAYLOAD_FRAMES_RXIPV4NOPAYFRM_LEN (32u)

/** \brief  Mask for Ifx_ETH_RXIPV4_NO_PAYLOAD_FRAMES_Bits.RXIPV4NOPAYFRM */
#define IFX_ETH_RXIPV4_NO_PAYLOAD_FRAMES_RXIPV4NOPAYFRM_MSK (0xffffffffu)

/** \brief  Offset for Ifx_ETH_RXIPV4_NO_PAYLOAD_FRAMES_Bits.RXIPV4NOPAYFRM */
#define IFX_ETH_RXIPV4_NO_PAYLOAD_FRAMES_RXIPV4NOPAYFRM_OFF (0u)

/** \brief  Length for Ifx_ETH_RXIPV4_NO_PAYLOAD_OCTETS_Bits.RXIPV4NOPAYOCT */
#define IFX_ETH_RXIPV4_NO_PAYLOAD_OCTETS_RXIPV4NOPAYOCT_LEN (32u)

/** \brief  Mask for Ifx_ETH_RXIPV4_NO_PAYLOAD_OCTETS_Bits.RXIPV4NOPAYOCT */
#define IFX_ETH_RXIPV4_NO_PAYLOAD_OCTETS_RXIPV4NOPAYOCT_MSK (0xffffffffu)

/** \brief  Offset for Ifx_ETH_RXIPV4_NO_PAYLOAD_OCTETS_Bits.RXIPV4NOPAYOCT */
#define IFX_ETH_RXIPV4_NO_PAYLOAD_OCTETS_RXIPV4NOPAYOCT_OFF (0u)

/** \brief  Length for
 * Ifx_ETH_RXIPV4_UDP_CHECKSUM_DISABLE_OCTETS_Bits.RXIPV4UDSBLOCT */
#define IFX_ETH_RXIPV4_UDP_CHECKSUM_DISABLE_OCTETS_RXIPV4UDSBLOCT_LEN (32u)

/** \brief  Mask for
 * Ifx_ETH_RXIPV4_UDP_CHECKSUM_DISABLE_OCTETS_Bits.RXIPV4UDSBLOCT */
#define IFX_ETH_RXIPV4_UDP_CHECKSUM_DISABLE_OCTETS_RXIPV4UDSBLOCT_MSK (0xffffffffu)

/** \brief  Offset for
 * Ifx_ETH_RXIPV4_UDP_CHECKSUM_DISABLE_OCTETS_Bits.RXIPV4UDSBLOCT */
#define IFX_ETH_RXIPV4_UDP_CHECKSUM_DISABLE_OCTETS_RXIPV4UDSBLOCT_OFF (0u)

/** \brief  Length for
 * Ifx_ETH_RXIPV4_UDP_CHECKSUM_DISABLED_FRAMES_Bits.RXIPV4UDSBLFRM */
#define IFX_ETH_RXIPV4_UDP_CHECKSUM_DISABLED_FRAMES_RXIPV4UDSBLFRM_LEN (32u)

/** \brief  Mask for
 * Ifx_ETH_RXIPV4_UDP_CHECKSUM_DISABLED_FRAMES_Bits.RXIPV4UDSBLFRM */
#define IFX_ETH_RXIPV4_UDP_CHECKSUM_DISABLED_FRAMES_RXIPV4UDSBLFRM_MSK (0xffffffffu)

/** \brief  Offset for
 * Ifx_ETH_RXIPV4_UDP_CHECKSUM_DISABLED_FRAMES_Bits.RXIPV4UDSBLFRM */
#define IFX_ETH_RXIPV4_UDP_CHECKSUM_DISABLED_FRAMES_RXIPV4UDSBLFRM_OFF (0u)

/** \brief  Length for Ifx_ETH_RXIPV6_GOOD_FRAMES_Bits.RXIPV6GDFRM */
#define IFX_ETH_RXIPV6_GOOD_FRAMES_RXIPV6GDFRM_LEN (32u)

/** \brief  Mask for Ifx_ETH_RXIPV6_GOOD_FRAMES_Bits.RXIPV6GDFRM */
#define IFX_ETH_RXIPV6_GOOD_FRAMES_RXIPV6GDFRM_MSK (0xffffffffu)

/** \brief  Offset for Ifx_ETH_RXIPV6_GOOD_FRAMES_Bits.RXIPV6GDFRM */
#define IFX_ETH_RXIPV6_GOOD_FRAMES_RXIPV6GDFRM_OFF (0u)

/** \brief  Length for Ifx_ETH_RXIPV6_GOOD_OCTETS_Bits.RXIPV6GDOCT */
#define IFX_ETH_RXIPV6_GOOD_OCTETS_RXIPV6GDOCT_LEN (32u)

/** \brief  Mask for Ifx_ETH_RXIPV6_GOOD_OCTETS_Bits.RXIPV6GDOCT */
#define IFX_ETH_RXIPV6_GOOD_OCTETS_RXIPV6GDOCT_MSK (0xffffffffu)

/** \brief  Offset for Ifx_ETH_RXIPV6_GOOD_OCTETS_Bits.RXIPV6GDOCT */
#define IFX_ETH_RXIPV6_GOOD_OCTETS_RXIPV6GDOCT_OFF (0u)

/** \brief  Length for Ifx_ETH_RXIPV6_HEADER_ERROR_FRAMES_Bits.RXIPV6HDRERRFRM */
#define IFX_ETH_RXIPV6_HEADER_ERROR_FRAMES_RXIPV6HDRERRFRM_LEN (32u)

/** \brief  Mask for Ifx_ETH_RXIPV6_HEADER_ERROR_FRAMES_Bits.RXIPV6HDRERRFRM */
#define IFX_ETH_RXIPV6_HEADER_ERROR_FRAMES_RXIPV6HDRERRFRM_MSK (0xffffffffu)

/** \brief  Offset for Ifx_ETH_RXIPV6_HEADER_ERROR_FRAMES_Bits.RXIPV6HDRERRFRM */
#define IFX_ETH_RXIPV6_HEADER_ERROR_FRAMES_RXIPV6HDRERRFRM_OFF (0u)

/** \brief  Length for Ifx_ETH_RXIPV6_HEADER_ERROR_OCTETS_Bits.RXIPV6HDRERROCT */
#define IFX_ETH_RXIPV6_HEADER_ERROR_OCTETS_RXIPV6HDRERROCT_LEN (32u)

/** \brief  Mask for Ifx_ETH_RXIPV6_HEADER_ERROR_OCTETS_Bits.RXIPV6HDRERROCT */
#define IFX_ETH_RXIPV6_HEADER_ERROR_OCTETS_RXIPV6HDRERROCT_MSK (0xffffffffu)

/** \brief  Offset for Ifx_ETH_RXIPV6_HEADER_ERROR_OCTETS_Bits.RXIPV6HDRERROCT */
#define IFX_ETH_RXIPV6_HEADER_ERROR_OCTETS_RXIPV6HDRERROCT_OFF (0u)

/** \brief  Length for Ifx_ETH_RXIPV6_NO_PAYLOAD_FRAMES_Bits.RXIPV6NOPAYFRM */
#define IFX_ETH_RXIPV6_NO_PAYLOAD_FRAMES_RXIPV6NOPAYFRM_LEN (32u)

/** \brief  Mask for Ifx_ETH_RXIPV6_NO_PAYLOAD_FRAMES_Bits.RXIPV6NOPAYFRM */
#define IFX_ETH_RXIPV6_NO_PAYLOAD_FRAMES_RXIPV6NOPAYFRM_MSK (0xffffffffu)

/** \brief  Offset for Ifx_ETH_RXIPV6_NO_PAYLOAD_FRAMES_Bits.RXIPV6NOPAYFRM */
#define IFX_ETH_RXIPV6_NO_PAYLOAD_FRAMES_RXIPV6NOPAYFRM_OFF (0u)

/** \brief  Length for Ifx_ETH_RXIPV6_NO_PAYLOAD_OCTETS_Bits.RXIPV6NOPAYOCT */
#define IFX_ETH_RXIPV6_NO_PAYLOAD_OCTETS_RXIPV6NOPAYOCT_LEN (32u)

/** \brief  Mask for Ifx_ETH_RXIPV6_NO_PAYLOAD_OCTETS_Bits.RXIPV6NOPAYOCT */
#define IFX_ETH_RXIPV6_NO_PAYLOAD_OCTETS_RXIPV6NOPAYOCT_MSK (0xffffffffu)

/** \brief  Offset for Ifx_ETH_RXIPV6_NO_PAYLOAD_OCTETS_Bits.RXIPV6NOPAYOCT */
#define IFX_ETH_RXIPV6_NO_PAYLOAD_OCTETS_RXIPV6NOPAYOCT_OFF (0u)

/** \brief  Length for Ifx_ETH_RXTCP_ERROR_FRAMES_Bits.RXTCPERRFRM */
#define IFX_ETH_RXTCP_ERROR_FRAMES_RXTCPERRFRM_LEN (32u)

/** \brief  Mask for Ifx_ETH_RXTCP_ERROR_FRAMES_Bits.RXTCPERRFRM */
#define IFX_ETH_RXTCP_ERROR_FRAMES_RXTCPERRFRM_MSK (0xffffffffu)

/** \brief  Offset for Ifx_ETH_RXTCP_ERROR_FRAMES_Bits.RXTCPERRFRM */
#define IFX_ETH_RXTCP_ERROR_FRAMES_RXTCPERRFRM_OFF (0u)

/** \brief  Length for Ifx_ETH_RXTCP_ERROR_OCTETS_Bits.RXTCPERROCT */
#define IFX_ETH_RXTCP_ERROR_OCTETS_RXTCPERROCT_LEN (32u)

/** \brief  Mask for Ifx_ETH_RXTCP_ERROR_OCTETS_Bits.RXTCPERROCT */
#define IFX_ETH_RXTCP_ERROR_OCTETS_RXTCPERROCT_MSK (0xffffffffu)

/** \brief  Offset for Ifx_ETH_RXTCP_ERROR_OCTETS_Bits.RXTCPERROCT */
#define IFX_ETH_RXTCP_ERROR_OCTETS_RXTCPERROCT_OFF (0u)

/** \brief  Length for Ifx_ETH_RXTCP_GOOD_FRAMES_Bits.RXTCPGDFRM */
#define IFX_ETH_RXTCP_GOOD_FRAMES_RXTCPGDFRM_LEN (32u)

/** \brief  Mask for Ifx_ETH_RXTCP_GOOD_FRAMES_Bits.RXTCPGDFRM */
#define IFX_ETH_RXTCP_GOOD_FRAMES_RXTCPGDFRM_MSK (0xffffffffu)

/** \brief  Offset for Ifx_ETH_RXTCP_GOOD_FRAMES_Bits.RXTCPGDFRM */
#define IFX_ETH_RXTCP_GOOD_FRAMES_RXTCPGDFRM_OFF (0u)

/** \brief  Length for Ifx_ETH_RXTCP_GOOD_OCTETS_Bits.RXTCPGDOCT */
#define IFX_ETH_RXTCP_GOOD_OCTETS_RXTCPGDOCT_LEN (32u)

/** \brief  Mask for Ifx_ETH_RXTCP_GOOD_OCTETS_Bits.RXTCPGDOCT */
#define IFX_ETH_RXTCP_GOOD_OCTETS_RXTCPGDOCT_MSK (0xffffffffu)

/** \brief  Offset for Ifx_ETH_RXTCP_GOOD_OCTETS_Bits.RXTCPGDOCT */
#define IFX_ETH_RXTCP_GOOD_OCTETS_RXTCPGDOCT_OFF (0u)

/** \brief  Length for Ifx_ETH_RXUDP_ERROR_FRAMES_Bits.RXUDPERRFRM */
#define IFX_ETH_RXUDP_ERROR_FRAMES_RXUDPERRFRM_LEN (32u)

/** \brief  Mask for Ifx_ETH_RXUDP_ERROR_FRAMES_Bits.RXUDPERRFRM */
#define IFX_ETH_RXUDP_ERROR_FRAMES_RXUDPERRFRM_MSK (0xffffffffu)

/** \brief  Offset for Ifx_ETH_RXUDP_ERROR_FRAMES_Bits.RXUDPERRFRM */
#define IFX_ETH_RXUDP_ERROR_FRAMES_RXUDPERRFRM_OFF (0u)

/** \brief  Length for Ifx_ETH_RXUDP_ERROR_OCTETS_Bits.RXUDPERROCT */
#define IFX_ETH_RXUDP_ERROR_OCTETS_RXUDPERROCT_LEN (32u)

/** \brief  Mask for Ifx_ETH_RXUDP_ERROR_OCTETS_Bits.RXUDPERROCT */
#define IFX_ETH_RXUDP_ERROR_OCTETS_RXUDPERROCT_MSK (0xffffffffu)

/** \brief  Offset for Ifx_ETH_RXUDP_ERROR_OCTETS_Bits.RXUDPERROCT */
#define IFX_ETH_RXUDP_ERROR_OCTETS_RXUDPERROCT_OFF (0u)

/** \brief  Length for Ifx_ETH_RXUDP_GOOD_FRAMES_Bits.RXUDPGDFRM */
#define IFX_ETH_RXUDP_GOOD_FRAMES_RXUDPGDFRM_LEN (32u)

/** \brief  Mask for Ifx_ETH_RXUDP_GOOD_FRAMES_Bits.RXUDPGDFRM */
#define IFX_ETH_RXUDP_GOOD_FRAMES_RXUDPGDFRM_MSK (0xffffffffu)

/** \brief  Offset for Ifx_ETH_RXUDP_GOOD_FRAMES_Bits.RXUDPGDFRM */
#define IFX_ETH_RXUDP_GOOD_FRAMES_RXUDPGDFRM_OFF (0u)

/** \brief  Length for Ifx_ETH_RXUDP_GOOD_OCTETS_Bits.RXUDPGDOCT */
#define IFX_ETH_RXUDP_GOOD_OCTETS_RXUDPGDOCT_LEN (32u)

/** \brief  Mask for Ifx_ETH_RXUDP_GOOD_OCTETS_Bits.RXUDPGDOCT */
#define IFX_ETH_RXUDP_GOOD_OCTETS_RXUDPGDOCT_MSK (0xffffffffu)

/** \brief  Offset for Ifx_ETH_RXUDP_GOOD_OCTETS_Bits.RXUDPGDOCT */
#define IFX_ETH_RXUDP_GOOD_OCTETS_RXUDPGDOCT_OFF (0u)

/** \brief  Length for Ifx_ETH_STATUS_Bits.AIS */
#define IFX_ETH_STATUS_AIS_LEN (1u)

/** \brief  Mask for Ifx_ETH_STATUS_Bits.AIS */
#define IFX_ETH_STATUS_AIS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_STATUS_Bits.AIS */
#define IFX_ETH_STATUS_AIS_OFF (15u)

/** \brief  Length for Ifx_ETH_STATUS_Bits.EB */
#define IFX_ETH_STATUS_EB_LEN (3u)

/** \brief  Mask for Ifx_ETH_STATUS_Bits.EB */
#define IFX_ETH_STATUS_EB_MSK (0x7u)

/** \brief  Offset for Ifx_ETH_STATUS_Bits.EB */
#define IFX_ETH_STATUS_EB_OFF (23u)

/** \brief  Length for Ifx_ETH_STATUS_Bits.ERI */
#define IFX_ETH_STATUS_ERI_LEN (1u)

/** \brief  Mask for Ifx_ETH_STATUS_Bits.ERI */
#define IFX_ETH_STATUS_ERI_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_STATUS_Bits.ERI */
#define IFX_ETH_STATUS_ERI_OFF (14u)

/** \brief  Length for Ifx_ETH_STATUS_Bits.ETI */
#define IFX_ETH_STATUS_ETI_LEN (1u)

/** \brief  Mask for Ifx_ETH_STATUS_Bits.ETI */
#define IFX_ETH_STATUS_ETI_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_STATUS_Bits.ETI */
#define IFX_ETH_STATUS_ETI_OFF (10u)

/** \brief  Length for Ifx_ETH_STATUS_Bits.FBI */
#define IFX_ETH_STATUS_FBI_LEN (1u)

/** \brief  Mask for Ifx_ETH_STATUS_Bits.FBI */
#define IFX_ETH_STATUS_FBI_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_STATUS_Bits.FBI */
#define IFX_ETH_STATUS_FBI_OFF (13u)

/** \brief  Length for Ifx_ETH_STATUS_Bits.GLI */
#define IFX_ETH_STATUS_GLI_LEN (1u)

/** \brief  Mask for Ifx_ETH_STATUS_Bits.GLI */
#define IFX_ETH_STATUS_GLI_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_STATUS_Bits.GLI */
#define IFX_ETH_STATUS_GLI_OFF (26u)

/** \brief  Length for Ifx_ETH_STATUS_Bits.GLPII */
#define IFX_ETH_STATUS_GLPII_LEN (1u)

/** \brief  Mask for Ifx_ETH_STATUS_Bits.GLPII */
#define IFX_ETH_STATUS_GLPII_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_STATUS_Bits.GLPII */
#define IFX_ETH_STATUS_GLPII_OFF (30u)

/** \brief  Length for Ifx_ETH_STATUS_Bits.GMI */
#define IFX_ETH_STATUS_GMI_LEN (1u)

/** \brief  Mask for Ifx_ETH_STATUS_Bits.GMI */
#define IFX_ETH_STATUS_GMI_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_STATUS_Bits.GMI */
#define IFX_ETH_STATUS_GMI_OFF (27u)

/** \brief  Length for Ifx_ETH_STATUS_Bits.GPI */
#define IFX_ETH_STATUS_GPI_LEN (1u)

/** \brief  Mask for Ifx_ETH_STATUS_Bits.GPI */
#define IFX_ETH_STATUS_GPI_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_STATUS_Bits.GPI */
#define IFX_ETH_STATUS_GPI_OFF (28u)

/** \brief  Length for Ifx_ETH_STATUS_Bits.NIS */
#define IFX_ETH_STATUS_NIS_LEN (1u)

/** \brief  Mask for Ifx_ETH_STATUS_Bits.NIS */
#define IFX_ETH_STATUS_NIS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_STATUS_Bits.NIS */
#define IFX_ETH_STATUS_NIS_OFF (16u)

/** \brief  Length for Ifx_ETH_STATUS_Bits.OVF */
#define IFX_ETH_STATUS_OVF_LEN (1u)

/** \brief  Mask for Ifx_ETH_STATUS_Bits.OVF */
#define IFX_ETH_STATUS_OVF_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_STATUS_Bits.OVF */
#define IFX_ETH_STATUS_OVF_OFF (4u)

/** \brief  Length for Ifx_ETH_STATUS_Bits.RI */
#define IFX_ETH_STATUS_RI_LEN (1u)

/** \brief  Mask for Ifx_ETH_STATUS_Bits.RI */
#define IFX_ETH_STATUS_RI_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_STATUS_Bits.RI */
#define IFX_ETH_STATUS_RI_OFF (6u)

/** \brief  Length for Ifx_ETH_STATUS_Bits.RPS */
#define IFX_ETH_STATUS_RPS_LEN (1u)

/** \brief  Mask for Ifx_ETH_STATUS_Bits.RPS */
#define IFX_ETH_STATUS_RPS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_STATUS_Bits.RPS */
#define IFX_ETH_STATUS_RPS_OFF (8u)

/** \brief  Length for Ifx_ETH_STATUS_Bits.RS */
#define IFX_ETH_STATUS_RS_LEN (3u)

/** \brief  Mask for Ifx_ETH_STATUS_Bits.RS */
#define IFX_ETH_STATUS_RS_MSK (0x7u)

/** \brief  Offset for Ifx_ETH_STATUS_Bits.RS */
#define IFX_ETH_STATUS_RS_OFF (17u)

/** \brief  Length for Ifx_ETH_STATUS_Bits.RU */
#define IFX_ETH_STATUS_RU_LEN (1u)

/** \brief  Mask for Ifx_ETH_STATUS_Bits.RU */
#define IFX_ETH_STATUS_RU_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_STATUS_Bits.RU */
#define IFX_ETH_STATUS_RU_OFF (7u)

/** \brief  Length for Ifx_ETH_STATUS_Bits.RWT */
#define IFX_ETH_STATUS_RWT_LEN (1u)

/** \brief  Mask for Ifx_ETH_STATUS_Bits.RWT */
#define IFX_ETH_STATUS_RWT_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_STATUS_Bits.RWT */
#define IFX_ETH_STATUS_RWT_OFF (9u)

/** \brief  Length for Ifx_ETH_STATUS_Bits.TI */
#define IFX_ETH_STATUS_TI_LEN (1u)

/** \brief  Mask for Ifx_ETH_STATUS_Bits.TI */
#define IFX_ETH_STATUS_TI_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_STATUS_Bits.TI */
#define IFX_ETH_STATUS_TI_OFF (0u)

/** \brief  Length for Ifx_ETH_STATUS_Bits.TJT */
#define IFX_ETH_STATUS_TJT_LEN (1u)

/** \brief  Mask for Ifx_ETH_STATUS_Bits.TJT */
#define IFX_ETH_STATUS_TJT_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_STATUS_Bits.TJT */
#define IFX_ETH_STATUS_TJT_OFF (3u)

/** \brief  Length for Ifx_ETH_STATUS_Bits.TPS */
#define IFX_ETH_STATUS_TPS_LEN (1u)

/** \brief  Mask for Ifx_ETH_STATUS_Bits.TPS */
#define IFX_ETH_STATUS_TPS_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_STATUS_Bits.TPS */
#define IFX_ETH_STATUS_TPS_OFF (1u)

/** \brief  Length for Ifx_ETH_STATUS_Bits.TS */
#define IFX_ETH_STATUS_TS_LEN (3u)

/** \brief  Mask for Ifx_ETH_STATUS_Bits.TS */
#define IFX_ETH_STATUS_TS_MSK (0x7u)

/** \brief  Offset for Ifx_ETH_STATUS_Bits.TS */
#define IFX_ETH_STATUS_TS_OFF (20u)

/** \brief  Length for Ifx_ETH_STATUS_Bits.TTI */
#define IFX_ETH_STATUS_TTI_LEN (1u)

/** \brief  Mask for Ifx_ETH_STATUS_Bits.TTI */
#define IFX_ETH_STATUS_TTI_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_STATUS_Bits.TTI */
#define IFX_ETH_STATUS_TTI_OFF (29u)

/** \brief  Length for Ifx_ETH_STATUS_Bits.TU */
#define IFX_ETH_STATUS_TU_LEN (1u)

/** \brief  Mask for Ifx_ETH_STATUS_Bits.TU */
#define IFX_ETH_STATUS_TU_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_STATUS_Bits.TU */
#define IFX_ETH_STATUS_TU_OFF (2u)

/** \brief  Length for Ifx_ETH_STATUS_Bits.UNF */
#define IFX_ETH_STATUS_UNF_LEN (1u)

/** \brief  Mask for Ifx_ETH_STATUS_Bits.UNF */
#define IFX_ETH_STATUS_UNF_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_STATUS_Bits.UNF */
#define IFX_ETH_STATUS_UNF_OFF (5u)

/** \brief  Length for Ifx_ETH_SUB_SECOND_INCREMENT_Bits.SSINC */
#define IFX_ETH_SUB_SECOND_INCREMENT_SSINC_LEN (8u)

/** \brief  Mask for Ifx_ETH_SUB_SECOND_INCREMENT_Bits.SSINC */
#define IFX_ETH_SUB_SECOND_INCREMENT_SSINC_MSK (0xffu)

/** \brief  Offset for Ifx_ETH_SUB_SECOND_INCREMENT_Bits.SSINC */
#define IFX_ETH_SUB_SECOND_INCREMENT_SSINC_OFF (0u)

/** \brief  Length for Ifx_ETH_SYSTEM_TIME_HIGHER_WORD_SECONDS_Bits.TSHWR */
#define IFX_ETH_SYSTEM_TIME_HIGHER_WORD_SECONDS_TSHWR_LEN (16u)

/** \brief  Mask for Ifx_ETH_SYSTEM_TIME_HIGHER_WORD_SECONDS_Bits.TSHWR */
#define IFX_ETH_SYSTEM_TIME_HIGHER_WORD_SECONDS_TSHWR_MSK (0xffffu)

/** \brief  Offset for Ifx_ETH_SYSTEM_TIME_HIGHER_WORD_SECONDS_Bits.TSHWR */
#define IFX_ETH_SYSTEM_TIME_HIGHER_WORD_SECONDS_TSHWR_OFF (0u)

/** \brief  Length for Ifx_ETH_SYSTEM_TIME_NANOSECONDS_Bits.TSSS */
#define IFX_ETH_SYSTEM_TIME_NANOSECONDS_TSSS_LEN (31u)

/** \brief  Mask for Ifx_ETH_SYSTEM_TIME_NANOSECONDS_Bits.TSSS */
#define IFX_ETH_SYSTEM_TIME_NANOSECONDS_TSSS_MSK (0x7fffffffu)

/** \brief  Offset for Ifx_ETH_SYSTEM_TIME_NANOSECONDS_Bits.TSSS */
#define IFX_ETH_SYSTEM_TIME_NANOSECONDS_TSSS_OFF (0u)

/** \brief  Length for Ifx_ETH_SYSTEM_TIME_NANOSECONDS_UPDATE_Bits.ADDSUB */
#define IFX_ETH_SYSTEM_TIME_NANOSECONDS_UPDATE_ADDSUB_LEN (1u)

/** \brief  Mask for Ifx_ETH_SYSTEM_TIME_NANOSECONDS_UPDATE_Bits.ADDSUB */
#define IFX_ETH_SYSTEM_TIME_NANOSECONDS_UPDATE_ADDSUB_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_SYSTEM_TIME_NANOSECONDS_UPDATE_Bits.ADDSUB */
#define IFX_ETH_SYSTEM_TIME_NANOSECONDS_UPDATE_ADDSUB_OFF (31u)

/** \brief  Length for Ifx_ETH_SYSTEM_TIME_NANOSECONDS_UPDATE_Bits.TSSS */
#define IFX_ETH_SYSTEM_TIME_NANOSECONDS_UPDATE_TSSS_LEN (31u)

/** \brief  Mask for Ifx_ETH_SYSTEM_TIME_NANOSECONDS_UPDATE_Bits.TSSS */
#define IFX_ETH_SYSTEM_TIME_NANOSECONDS_UPDATE_TSSS_MSK (0x7fffffffu)

/** \brief  Offset for Ifx_ETH_SYSTEM_TIME_NANOSECONDS_UPDATE_Bits.TSSS */
#define IFX_ETH_SYSTEM_TIME_NANOSECONDS_UPDATE_TSSS_OFF (0u)

/** \brief  Length for Ifx_ETH_SYSTEM_TIME_SECONDS_Bits.TSS */
#define IFX_ETH_SYSTEM_TIME_SECONDS_TSS_LEN (32u)

/** \brief  Mask for Ifx_ETH_SYSTEM_TIME_SECONDS_Bits.TSS */
#define IFX_ETH_SYSTEM_TIME_SECONDS_TSS_MSK (0xffffffffu)

/** \brief  Offset for Ifx_ETH_SYSTEM_TIME_SECONDS_Bits.TSS */
#define IFX_ETH_SYSTEM_TIME_SECONDS_TSS_OFF (0u)

/** \brief  Length for Ifx_ETH_SYSTEM_TIME_SECONDS_UPDATE_Bits.TSS */
#define IFX_ETH_SYSTEM_TIME_SECONDS_UPDATE_TSS_LEN (32u)

/** \brief  Mask for Ifx_ETH_SYSTEM_TIME_SECONDS_UPDATE_Bits.TSS */
#define IFX_ETH_SYSTEM_TIME_SECONDS_UPDATE_TSS_MSK (0xffffffffu)

/** \brief  Offset for Ifx_ETH_SYSTEM_TIME_SECONDS_UPDATE_Bits.TSS */
#define IFX_ETH_SYSTEM_TIME_SECONDS_UPDATE_TSS_OFF (0u)

/** \brief  Length for Ifx_ETH_TARGET_TIME_NANOSECONDS_Bits.TRGTBUSY */
#define IFX_ETH_TARGET_TIME_NANOSECONDS_TRGTBUSY_LEN (1u)

/** \brief  Mask for Ifx_ETH_TARGET_TIME_NANOSECONDS_Bits.TRGTBUSY */
#define IFX_ETH_TARGET_TIME_NANOSECONDS_TRGTBUSY_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_TARGET_TIME_NANOSECONDS_Bits.TRGTBUSY */
#define IFX_ETH_TARGET_TIME_NANOSECONDS_TRGTBUSY_OFF (31u)

/** \brief  Length for Ifx_ETH_TARGET_TIME_NANOSECONDS_Bits.TTSLO */
#define IFX_ETH_TARGET_TIME_NANOSECONDS_TTSLO_LEN (31u)

/** \brief  Mask for Ifx_ETH_TARGET_TIME_NANOSECONDS_Bits.TTSLO */
#define IFX_ETH_TARGET_TIME_NANOSECONDS_TTSLO_MSK (0x7fffffffu)

/** \brief  Offset for Ifx_ETH_TARGET_TIME_NANOSECONDS_Bits.TTSLO */
#define IFX_ETH_TARGET_TIME_NANOSECONDS_TTSLO_OFF (0u)

/** \brief  Length for Ifx_ETH_TARGET_TIME_SECONDS_Bits.TSTR */
#define IFX_ETH_TARGET_TIME_SECONDS_TSTR_LEN (32u)

/** \brief  Mask for Ifx_ETH_TARGET_TIME_SECONDS_Bits.TSTR */
#define IFX_ETH_TARGET_TIME_SECONDS_TSTR_MSK (0xffffffffu)

/** \brief  Offset for Ifx_ETH_TARGET_TIME_SECONDS_Bits.TSTR */
#define IFX_ETH_TARGET_TIME_SECONDS_TSTR_OFF (0u)

/** \brief  Length for Ifx_ETH_TIMESTAMP_ADDEND_Bits.TSAR */
#define IFX_ETH_TIMESTAMP_ADDEND_TSAR_LEN (32u)

/** \brief  Mask for Ifx_ETH_TIMESTAMP_ADDEND_Bits.TSAR */
#define IFX_ETH_TIMESTAMP_ADDEND_TSAR_MSK (0xffffffffu)

/** \brief  Offset for Ifx_ETH_TIMESTAMP_ADDEND_Bits.TSAR */
#define IFX_ETH_TIMESTAMP_ADDEND_TSAR_OFF (0u)

/** \brief  Length for Ifx_ETH_TIMESTAMP_CONTROL_Bits.ATSEN0 */
#define IFX_ETH_TIMESTAMP_CONTROL_ATSEN0_LEN (1u)

/** \brief  Mask for Ifx_ETH_TIMESTAMP_CONTROL_Bits.ATSEN0 */
#define IFX_ETH_TIMESTAMP_CONTROL_ATSEN0_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_TIMESTAMP_CONTROL_Bits.ATSEN0 */
#define IFX_ETH_TIMESTAMP_CONTROL_ATSEN0_OFF (25u)

/** \brief  Length for Ifx_ETH_TIMESTAMP_CONTROL_Bits.ATSEN1 */
#define IFX_ETH_TIMESTAMP_CONTROL_ATSEN1_LEN (1u)

/** \brief  Mask for Ifx_ETH_TIMESTAMP_CONTROL_Bits.ATSEN1 */
#define IFX_ETH_TIMESTAMP_CONTROL_ATSEN1_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_TIMESTAMP_CONTROL_Bits.ATSEN1 */
#define IFX_ETH_TIMESTAMP_CONTROL_ATSEN1_OFF (26u)

/** \brief  Length for Ifx_ETH_TIMESTAMP_CONTROL_Bits.ATSEN2 */
#define IFX_ETH_TIMESTAMP_CONTROL_ATSEN2_LEN (1u)

/** \brief  Mask for Ifx_ETH_TIMESTAMP_CONTROL_Bits.ATSEN2 */
#define IFX_ETH_TIMESTAMP_CONTROL_ATSEN2_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_TIMESTAMP_CONTROL_Bits.ATSEN2 */
#define IFX_ETH_TIMESTAMP_CONTROL_ATSEN2_OFF (27u)

/** \brief  Length for Ifx_ETH_TIMESTAMP_CONTROL_Bits.ATSEN3 */
#define IFX_ETH_TIMESTAMP_CONTROL_ATSEN3_LEN (1u)

/** \brief  Mask for Ifx_ETH_TIMESTAMP_CONTROL_Bits.ATSEN3 */
#define IFX_ETH_TIMESTAMP_CONTROL_ATSEN3_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_TIMESTAMP_CONTROL_Bits.ATSEN3 */
#define IFX_ETH_TIMESTAMP_CONTROL_ATSEN3_OFF (28u)

/** \brief  Length for Ifx_ETH_TIMESTAMP_CONTROL_Bits.ATSFC */
#define IFX_ETH_TIMESTAMP_CONTROL_ATSFC_LEN (1u)

/** \brief  Mask for Ifx_ETH_TIMESTAMP_CONTROL_Bits.ATSFC */
#define IFX_ETH_TIMESTAMP_CONTROL_ATSFC_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_TIMESTAMP_CONTROL_Bits.ATSFC */
#define IFX_ETH_TIMESTAMP_CONTROL_ATSFC_OFF (24u)

/** \brief  Length for Ifx_ETH_TIMESTAMP_CONTROL_Bits.SNAPTYPSEL */
#define IFX_ETH_TIMESTAMP_CONTROL_SNAPTYPSEL_LEN (2u)

/** \brief  Mask for Ifx_ETH_TIMESTAMP_CONTROL_Bits.SNAPTYPSEL */
#define IFX_ETH_TIMESTAMP_CONTROL_SNAPTYPSEL_MSK (0x3u)

/** \brief  Offset for Ifx_ETH_TIMESTAMP_CONTROL_Bits.SNAPTYPSEL */
#define IFX_ETH_TIMESTAMP_CONTROL_SNAPTYPSEL_OFF (16u)

/** \brief  Length for Ifx_ETH_TIMESTAMP_CONTROL_Bits.TSADDREG */
#define IFX_ETH_TIMESTAMP_CONTROL_TSADDREG_LEN (1u)

/** \brief  Mask for Ifx_ETH_TIMESTAMP_CONTROL_Bits.TSADDREG */
#define IFX_ETH_TIMESTAMP_CONTROL_TSADDREG_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_TIMESTAMP_CONTROL_Bits.TSADDREG */
#define IFX_ETH_TIMESTAMP_CONTROL_TSADDREG_OFF (5u)

/** \brief  Length for Ifx_ETH_TIMESTAMP_CONTROL_Bits.TSCFUPDT */
#define IFX_ETH_TIMESTAMP_CONTROL_TSCFUPDT_LEN (1u)

/** \brief  Mask for Ifx_ETH_TIMESTAMP_CONTROL_Bits.TSCFUPDT */
#define IFX_ETH_TIMESTAMP_CONTROL_TSCFUPDT_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_TIMESTAMP_CONTROL_Bits.TSCFUPDT */
#define IFX_ETH_TIMESTAMP_CONTROL_TSCFUPDT_OFF (1u)

/** \brief  Length for Ifx_ETH_TIMESTAMP_CONTROL_Bits.TSCTRLSSR */
#define IFX_ETH_TIMESTAMP_CONTROL_TSCTRLSSR_LEN (1u)

/** \brief  Mask for Ifx_ETH_TIMESTAMP_CONTROL_Bits.TSCTRLSSR */
#define IFX_ETH_TIMESTAMP_CONTROL_TSCTRLSSR_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_TIMESTAMP_CONTROL_Bits.TSCTRLSSR */
#define IFX_ETH_TIMESTAMP_CONTROL_TSCTRLSSR_OFF (9u)

/** \brief  Length for Ifx_ETH_TIMESTAMP_CONTROL_Bits.TSENA */
#define IFX_ETH_TIMESTAMP_CONTROL_TSENA_LEN (1u)

/** \brief  Mask for Ifx_ETH_TIMESTAMP_CONTROL_Bits.TSENA */
#define IFX_ETH_TIMESTAMP_CONTROL_TSENA_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_TIMESTAMP_CONTROL_Bits.TSENA */
#define IFX_ETH_TIMESTAMP_CONTROL_TSENA_OFF (0u)

/** \brief  Length for Ifx_ETH_TIMESTAMP_CONTROL_Bits.TSENALL */
#define IFX_ETH_TIMESTAMP_CONTROL_TSENALL_LEN (1u)

/** \brief  Mask for Ifx_ETH_TIMESTAMP_CONTROL_Bits.TSENALL */
#define IFX_ETH_TIMESTAMP_CONTROL_TSENALL_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_TIMESTAMP_CONTROL_Bits.TSENALL */
#define IFX_ETH_TIMESTAMP_CONTROL_TSENALL_OFF (8u)

/** \brief  Length for Ifx_ETH_TIMESTAMP_CONTROL_Bits.TSENMACADDR */
#define IFX_ETH_TIMESTAMP_CONTROL_TSENMACADDR_LEN (1u)

/** \brief  Mask for Ifx_ETH_TIMESTAMP_CONTROL_Bits.TSENMACADDR */
#define IFX_ETH_TIMESTAMP_CONTROL_TSENMACADDR_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_TIMESTAMP_CONTROL_Bits.TSENMACADDR */
#define IFX_ETH_TIMESTAMP_CONTROL_TSENMACADDR_OFF (18u)

/** \brief  Length for Ifx_ETH_TIMESTAMP_CONTROL_Bits.TSEVNTENA */
#define IFX_ETH_TIMESTAMP_CONTROL_TSEVNTENA_LEN (1u)

/** \brief  Mask for Ifx_ETH_TIMESTAMP_CONTROL_Bits.TSEVNTENA */
#define IFX_ETH_TIMESTAMP_CONTROL_TSEVNTENA_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_TIMESTAMP_CONTROL_Bits.TSEVNTENA */
#define IFX_ETH_TIMESTAMP_CONTROL_TSEVNTENA_OFF (14u)

/** \brief  Length for Ifx_ETH_TIMESTAMP_CONTROL_Bits.TSINIT */
#define IFX_ETH_TIMESTAMP_CONTROL_TSINIT_LEN (1u)

/** \brief  Mask for Ifx_ETH_TIMESTAMP_CONTROL_Bits.TSINIT */
#define IFX_ETH_TIMESTAMP_CONTROL_TSINIT_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_TIMESTAMP_CONTROL_Bits.TSINIT */
#define IFX_ETH_TIMESTAMP_CONTROL_TSINIT_OFF (2u)

/** \brief  Length for Ifx_ETH_TIMESTAMP_CONTROL_Bits.TSIPENA */
#define IFX_ETH_TIMESTAMP_CONTROL_TSIPENA_LEN (1u)

/** \brief  Mask for Ifx_ETH_TIMESTAMP_CONTROL_Bits.TSIPENA */
#define IFX_ETH_TIMESTAMP_CONTROL_TSIPENA_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_TIMESTAMP_CONTROL_Bits.TSIPENA */
#define IFX_ETH_TIMESTAMP_CONTROL_TSIPENA_OFF (11u)

/** \brief  Length for Ifx_ETH_TIMESTAMP_CONTROL_Bits.TSIPV4ENA */
#define IFX_ETH_TIMESTAMP_CONTROL_TSIPV4ENA_LEN (1u)

/** \brief  Mask for Ifx_ETH_TIMESTAMP_CONTROL_Bits.TSIPV4ENA */
#define IFX_ETH_TIMESTAMP_CONTROL_TSIPV4ENA_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_TIMESTAMP_CONTROL_Bits.TSIPV4ENA */
#define IFX_ETH_TIMESTAMP_CONTROL_TSIPV4ENA_OFF (13u)

/** \brief  Length for Ifx_ETH_TIMESTAMP_CONTROL_Bits.TSIPV6ENA */
#define IFX_ETH_TIMESTAMP_CONTROL_TSIPV6ENA_LEN (1u)

/** \brief  Mask for Ifx_ETH_TIMESTAMP_CONTROL_Bits.TSIPV6ENA */
#define IFX_ETH_TIMESTAMP_CONTROL_TSIPV6ENA_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_TIMESTAMP_CONTROL_Bits.TSIPV6ENA */
#define IFX_ETH_TIMESTAMP_CONTROL_TSIPV6ENA_OFF (12u)

/** \brief  Length for Ifx_ETH_TIMESTAMP_CONTROL_Bits.TSMSTRENA */
#define IFX_ETH_TIMESTAMP_CONTROL_TSMSTRENA_LEN (1u)

/** \brief  Mask for Ifx_ETH_TIMESTAMP_CONTROL_Bits.TSMSTRENA */
#define IFX_ETH_TIMESTAMP_CONTROL_TSMSTRENA_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_TIMESTAMP_CONTROL_Bits.TSMSTRENA */
#define IFX_ETH_TIMESTAMP_CONTROL_TSMSTRENA_OFF (15u)

/** \brief  Length for Ifx_ETH_TIMESTAMP_CONTROL_Bits.TSTRIG */
#define IFX_ETH_TIMESTAMP_CONTROL_TSTRIG_LEN (1u)

/** \brief  Mask for Ifx_ETH_TIMESTAMP_CONTROL_Bits.TSTRIG */
#define IFX_ETH_TIMESTAMP_CONTROL_TSTRIG_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_TIMESTAMP_CONTROL_Bits.TSTRIG */
#define IFX_ETH_TIMESTAMP_CONTROL_TSTRIG_OFF (4u)

/** \brief  Length for Ifx_ETH_TIMESTAMP_CONTROL_Bits.TSUPDT */
#define IFX_ETH_TIMESTAMP_CONTROL_TSUPDT_LEN (1u)

/** \brief  Mask for Ifx_ETH_TIMESTAMP_CONTROL_Bits.TSUPDT */
#define IFX_ETH_TIMESTAMP_CONTROL_TSUPDT_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_TIMESTAMP_CONTROL_Bits.TSUPDT */
#define IFX_ETH_TIMESTAMP_CONTROL_TSUPDT_OFF (3u)

/** \brief  Length for Ifx_ETH_TIMESTAMP_CONTROL_Bits.TSVER2ENA */
#define IFX_ETH_TIMESTAMP_CONTROL_TSVER2ENA_LEN (1u)

/** \brief  Mask for Ifx_ETH_TIMESTAMP_CONTROL_Bits.TSVER2ENA */
#define IFX_ETH_TIMESTAMP_CONTROL_TSVER2ENA_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_TIMESTAMP_CONTROL_Bits.TSVER2ENA */
#define IFX_ETH_TIMESTAMP_CONTROL_TSVER2ENA_OFF (10u)

/** \brief  Length for Ifx_ETH_TIMESTAMP_STATUS_Bits.ATSNS */
#define IFX_ETH_TIMESTAMP_STATUS_ATSNS_LEN (5u)

/** \brief  Mask for Ifx_ETH_TIMESTAMP_STATUS_Bits.ATSNS */
#define IFX_ETH_TIMESTAMP_STATUS_ATSNS_MSK (0x1fu)

/** \brief  Offset for Ifx_ETH_TIMESTAMP_STATUS_Bits.ATSNS */
#define IFX_ETH_TIMESTAMP_STATUS_ATSNS_OFF (25u)

/** \brief  Length for Ifx_ETH_TIMESTAMP_STATUS_Bits.ATSSTM */
#define IFX_ETH_TIMESTAMP_STATUS_ATSSTM_LEN (1u)

/** \brief  Mask for Ifx_ETH_TIMESTAMP_STATUS_Bits.ATSSTM */
#define IFX_ETH_TIMESTAMP_STATUS_ATSSTM_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_TIMESTAMP_STATUS_Bits.ATSSTM */
#define IFX_ETH_TIMESTAMP_STATUS_ATSSTM_OFF (24u)

/** \brief  Length for Ifx_ETH_TIMESTAMP_STATUS_Bits.ATSSTN */
#define IFX_ETH_TIMESTAMP_STATUS_ATSSTN_LEN (4u)

/** \brief  Mask for Ifx_ETH_TIMESTAMP_STATUS_Bits.ATSSTN */
#define IFX_ETH_TIMESTAMP_STATUS_ATSSTN_MSK (0xfu)

/** \brief  Offset for Ifx_ETH_TIMESTAMP_STATUS_Bits.ATSSTN */
#define IFX_ETH_TIMESTAMP_STATUS_ATSSTN_OFF (16u)

/** \brief  Length for Ifx_ETH_TIMESTAMP_STATUS_Bits.AUXTSTRIG */
#define IFX_ETH_TIMESTAMP_STATUS_AUXTSTRIG_LEN (1u)

/** \brief  Mask for Ifx_ETH_TIMESTAMP_STATUS_Bits.AUXTSTRIG */
#define IFX_ETH_TIMESTAMP_STATUS_AUXTSTRIG_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_TIMESTAMP_STATUS_Bits.AUXTSTRIG */
#define IFX_ETH_TIMESTAMP_STATUS_AUXTSTRIG_OFF (2u)

/** \brief  Length for Ifx_ETH_TIMESTAMP_STATUS_Bits.TSSOVF */
#define IFX_ETH_TIMESTAMP_STATUS_TSSOVF_LEN (1u)

/** \brief  Mask for Ifx_ETH_TIMESTAMP_STATUS_Bits.TSSOVF */
#define IFX_ETH_TIMESTAMP_STATUS_TSSOVF_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_TIMESTAMP_STATUS_Bits.TSSOVF */
#define IFX_ETH_TIMESTAMP_STATUS_TSSOVF_OFF (0u)

/** \brief  Length for Ifx_ETH_TIMESTAMP_STATUS_Bits.TSTARGT1 */
#define IFX_ETH_TIMESTAMP_STATUS_TSTARGT1_LEN (1u)

/** \brief  Mask for Ifx_ETH_TIMESTAMP_STATUS_Bits.TSTARGT1 */
#define IFX_ETH_TIMESTAMP_STATUS_TSTARGT1_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_TIMESTAMP_STATUS_Bits.TSTARGT1 */
#define IFX_ETH_TIMESTAMP_STATUS_TSTARGT1_OFF (4u)

/** \brief  Length for Ifx_ETH_TIMESTAMP_STATUS_Bits.TSTARGT2 */
#define IFX_ETH_TIMESTAMP_STATUS_TSTARGT2_LEN (1u)

/** \brief  Mask for Ifx_ETH_TIMESTAMP_STATUS_Bits.TSTARGT2 */
#define IFX_ETH_TIMESTAMP_STATUS_TSTARGT2_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_TIMESTAMP_STATUS_Bits.TSTARGT2 */
#define IFX_ETH_TIMESTAMP_STATUS_TSTARGT2_OFF (6u)

/** \brief  Length for Ifx_ETH_TIMESTAMP_STATUS_Bits.TSTARGT3 */
#define IFX_ETH_TIMESTAMP_STATUS_TSTARGT3_LEN (1u)

/** \brief  Mask for Ifx_ETH_TIMESTAMP_STATUS_Bits.TSTARGT3 */
#define IFX_ETH_TIMESTAMP_STATUS_TSTARGT3_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_TIMESTAMP_STATUS_Bits.TSTARGT3 */
#define IFX_ETH_TIMESTAMP_STATUS_TSTARGT3_OFF (8u)

/** \brief  Length for Ifx_ETH_TIMESTAMP_STATUS_Bits.TSTARGT */
#define IFX_ETH_TIMESTAMP_STATUS_TSTARGT_LEN (1u)

/** \brief  Mask for Ifx_ETH_TIMESTAMP_STATUS_Bits.TSTARGT */
#define IFX_ETH_TIMESTAMP_STATUS_TSTARGT_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_TIMESTAMP_STATUS_Bits.TSTARGT */
#define IFX_ETH_TIMESTAMP_STATUS_TSTARGT_OFF (1u)

/** \brief  Length for Ifx_ETH_TIMESTAMP_STATUS_Bits.TSTRGTERR1 */
#define IFX_ETH_TIMESTAMP_STATUS_TSTRGTERR1_LEN (1u)

/** \brief  Mask for Ifx_ETH_TIMESTAMP_STATUS_Bits.TSTRGTERR1 */
#define IFX_ETH_TIMESTAMP_STATUS_TSTRGTERR1_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_TIMESTAMP_STATUS_Bits.TSTRGTERR1 */
#define IFX_ETH_TIMESTAMP_STATUS_TSTRGTERR1_OFF (5u)

/** \brief  Length for Ifx_ETH_TIMESTAMP_STATUS_Bits.TSTRGTERR2 */
#define IFX_ETH_TIMESTAMP_STATUS_TSTRGTERR2_LEN (1u)

/** \brief  Mask for Ifx_ETH_TIMESTAMP_STATUS_Bits.TSTRGTERR2 */
#define IFX_ETH_TIMESTAMP_STATUS_TSTRGTERR2_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_TIMESTAMP_STATUS_Bits.TSTRGTERR2 */
#define IFX_ETH_TIMESTAMP_STATUS_TSTRGTERR2_OFF (7u)

/** \brief  Length for Ifx_ETH_TIMESTAMP_STATUS_Bits.TSTRGTERR3 */
#define IFX_ETH_TIMESTAMP_STATUS_TSTRGTERR3_LEN (1u)

/** \brief  Mask for Ifx_ETH_TIMESTAMP_STATUS_Bits.TSTRGTERR3 */
#define IFX_ETH_TIMESTAMP_STATUS_TSTRGTERR3_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_TIMESTAMP_STATUS_Bits.TSTRGTERR3 */
#define IFX_ETH_TIMESTAMP_STATUS_TSTRGTERR3_OFF (9u)

/** \brief  Length for Ifx_ETH_TIMESTAMP_STATUS_Bits.TSTRGTERR */
#define IFX_ETH_TIMESTAMP_STATUS_TSTRGTERR_LEN (1u)

/** \brief  Mask for Ifx_ETH_TIMESTAMP_STATUS_Bits.TSTRGTERR */
#define IFX_ETH_TIMESTAMP_STATUS_TSTRGTERR_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_TIMESTAMP_STATUS_Bits.TSTRGTERR */
#define IFX_ETH_TIMESTAMP_STATUS_TSTRGTERR_OFF (3u)

/** \brief  Length for Ifx_ETH_TRANSMIT_DESCRIPTOR_LIST_ADDRESS_Bits.TDESLA */
#define IFX_ETH_TRANSMIT_DESCRIPTOR_LIST_ADDRESS_TDESLA_LEN (30u)

/** \brief  Mask for Ifx_ETH_TRANSMIT_DESCRIPTOR_LIST_ADDRESS_Bits.TDESLA */
#define IFX_ETH_TRANSMIT_DESCRIPTOR_LIST_ADDRESS_TDESLA_MSK (0x3fffffffu)

/** \brief  Offset for Ifx_ETH_TRANSMIT_DESCRIPTOR_LIST_ADDRESS_Bits.TDESLA */
#define IFX_ETH_TRANSMIT_DESCRIPTOR_LIST_ADDRESS_TDESLA_OFF (2u)

/** \brief  Length for Ifx_ETH_TRANSMIT_POLL_DEMAND_Bits.TPD */
#define IFX_ETH_TRANSMIT_POLL_DEMAND_TPD_LEN (32u)

/** \brief  Mask for Ifx_ETH_TRANSMIT_POLL_DEMAND_Bits.TPD */
#define IFX_ETH_TRANSMIT_POLL_DEMAND_TPD_MSK (0xffffffffu)

/** \brief  Offset for Ifx_ETH_TRANSMIT_POLL_DEMAND_Bits.TPD */
#define IFX_ETH_TRANSMIT_POLL_DEMAND_TPD_OFF (0u)

/** \brief  Length for
 * Ifx_ETH_TX_1024TOMAXOCTETS_FRAMES_GOOD_BAD_Bits.TX1024_MAXOCTGB */
#define IFX_ETH_TX_1024TOMAXOCTETS_FRAMES_GOOD_BAD_TX1024_MAXOCTGB_LEN (32u)

/** \brief  Mask for
 * Ifx_ETH_TX_1024TOMAXOCTETS_FRAMES_GOOD_BAD_Bits.TX1024_MAXOCTGB */
#define IFX_ETH_TX_1024TOMAXOCTETS_FRAMES_GOOD_BAD_TX1024_MAXOCTGB_MSK (0xffffffffu)

/** \brief  Offset for
 * Ifx_ETH_TX_1024TOMAXOCTETS_FRAMES_GOOD_BAD_Bits.TX1024_MAXOCTGB */
#define IFX_ETH_TX_1024TOMAXOCTETS_FRAMES_GOOD_BAD_TX1024_MAXOCTGB_OFF (0u)

/** \brief  Length for
 * Ifx_ETH_TX_128TO255OCTETS_FRAMES_GOOD_BAD_Bits.TX128_255OCTGB */
#define IFX_ETH_TX_128TO255OCTETS_FRAMES_GOOD_BAD_TX128_255OCTGB_LEN (32u)

/** \brief  Mask for
 * Ifx_ETH_TX_128TO255OCTETS_FRAMES_GOOD_BAD_Bits.TX128_255OCTGB */
#define IFX_ETH_TX_128TO255OCTETS_FRAMES_GOOD_BAD_TX128_255OCTGB_MSK (0xffffffffu)

/** \brief  Offset for
 * Ifx_ETH_TX_128TO255OCTETS_FRAMES_GOOD_BAD_Bits.TX128_255OCTGB */
#define IFX_ETH_TX_128TO255OCTETS_FRAMES_GOOD_BAD_TX128_255OCTGB_OFF (0u)

/** \brief  Length for
 * Ifx_ETH_TX_256TO511OCTETS_FRAMES_GOOD_BAD_Bits.TX256_511OCTGB */
#define IFX_ETH_TX_256TO511OCTETS_FRAMES_GOOD_BAD_TX256_511OCTGB_LEN (32u)

/** \brief  Mask for
 * Ifx_ETH_TX_256TO511OCTETS_FRAMES_GOOD_BAD_Bits.TX256_511OCTGB */
#define IFX_ETH_TX_256TO511OCTETS_FRAMES_GOOD_BAD_TX256_511OCTGB_MSK (0xffffffffu)

/** \brief  Offset for
 * Ifx_ETH_TX_256TO511OCTETS_FRAMES_GOOD_BAD_Bits.TX256_511OCTGB */
#define IFX_ETH_TX_256TO511OCTETS_FRAMES_GOOD_BAD_TX256_511OCTGB_OFF (0u)

/** \brief  Length for
 * Ifx_ETH_TX_512TO1023OCTETS_FRAMES_GOOD_BAD_Bits.TX512_1023OCTGB */
#define IFX_ETH_TX_512TO1023OCTETS_FRAMES_GOOD_BAD_TX512_1023OCTGB_LEN (32u)

/** \brief  Mask for
 * Ifx_ETH_TX_512TO1023OCTETS_FRAMES_GOOD_BAD_Bits.TX512_1023OCTGB */
#define IFX_ETH_TX_512TO1023OCTETS_FRAMES_GOOD_BAD_TX512_1023OCTGB_MSK (0xffffffffu)

/** \brief  Offset for
 * Ifx_ETH_TX_512TO1023OCTETS_FRAMES_GOOD_BAD_Bits.TX512_1023OCTGB */
#define IFX_ETH_TX_512TO1023OCTETS_FRAMES_GOOD_BAD_TX512_1023OCTGB_OFF (0u)

/** \brief  Length for Ifx_ETH_TX_64OCTETS_FRAMES_GOOD_BAD_Bits.TX64OCTGB */
#define IFX_ETH_TX_64OCTETS_FRAMES_GOOD_BAD_TX64OCTGB_LEN (32u)

/** \brief  Mask for Ifx_ETH_TX_64OCTETS_FRAMES_GOOD_BAD_Bits.TX64OCTGB */
#define IFX_ETH_TX_64OCTETS_FRAMES_GOOD_BAD_TX64OCTGB_MSK (0xffffffffu)

/** \brief  Offset for Ifx_ETH_TX_64OCTETS_FRAMES_GOOD_BAD_Bits.TX64OCTGB */
#define IFX_ETH_TX_64OCTETS_FRAMES_GOOD_BAD_TX64OCTGB_OFF (0u)

/** \brief  Length for
 * Ifx_ETH_TX_65TO127OCTETS_FRAMES_GOOD_BAD_Bits.TX65_127OCTGB */
#define IFX_ETH_TX_65TO127OCTETS_FRAMES_GOOD_BAD_TX65_127OCTGB_LEN (32u)

/** \brief  Mask for Ifx_ETH_TX_65TO127OCTETS_FRAMES_GOOD_BAD_Bits.TX65_127OCTGB */
#define IFX_ETH_TX_65TO127OCTETS_FRAMES_GOOD_BAD_TX65_127OCTGB_MSK (0xffffffffu)

/** \brief  Offset for
 * Ifx_ETH_TX_65TO127OCTETS_FRAMES_GOOD_BAD_Bits.TX65_127OCTGB */
#define IFX_ETH_TX_65TO127OCTETS_FRAMES_GOOD_BAD_TX65_127OCTGB_OFF (0u)

/** \brief  Length for Ifx_ETH_TX_BROADCAST_FRAMES_GOOD_BAD_Bits.TXBCASTGB */
#define IFX_ETH_TX_BROADCAST_FRAMES_GOOD_BAD_TXBCASTGB_LEN (32u)

/** \brief  Mask for Ifx_ETH_TX_BROADCAST_FRAMES_GOOD_BAD_Bits.TXBCASTGB */
#define IFX_ETH_TX_BROADCAST_FRAMES_GOOD_BAD_TXBCASTGB_MSK (0xffffffffu)

/** \brief  Offset for Ifx_ETH_TX_BROADCAST_FRAMES_GOOD_BAD_Bits.TXBCASTGB */
#define IFX_ETH_TX_BROADCAST_FRAMES_GOOD_BAD_TXBCASTGB_OFF (0u)

/** \brief  Length for Ifx_ETH_TX_BROADCAST_FRAMES_GOOD_Bits.TXBCASTG */
#define IFX_ETH_TX_BROADCAST_FRAMES_GOOD_TXBCASTG_LEN (32u)

/** \brief  Mask for Ifx_ETH_TX_BROADCAST_FRAMES_GOOD_Bits.TXBCASTG */
#define IFX_ETH_TX_BROADCAST_FRAMES_GOOD_TXBCASTG_MSK (0xffffffffu)

/** \brief  Offset for Ifx_ETH_TX_BROADCAST_FRAMES_GOOD_Bits.TXBCASTG */
#define IFX_ETH_TX_BROADCAST_FRAMES_GOOD_TXBCASTG_OFF (0u)

/** \brief  Length for Ifx_ETH_TX_CARRIER_ERROR_FRAMES_Bits.TXCARR */
#define IFX_ETH_TX_CARRIER_ERROR_FRAMES_TXCARR_LEN (32u)

/** \brief  Mask for Ifx_ETH_TX_CARRIER_ERROR_FRAMES_Bits.TXCARR */
#define IFX_ETH_TX_CARRIER_ERROR_FRAMES_TXCARR_MSK (0xffffffffu)

/** \brief  Offset for Ifx_ETH_TX_CARRIER_ERROR_FRAMES_Bits.TXCARR */
#define IFX_ETH_TX_CARRIER_ERROR_FRAMES_TXCARR_OFF (0u)

/** \brief  Length for Ifx_ETH_TX_DEFERRED_FRAMES_Bits.TXDEFRD */
#define IFX_ETH_TX_DEFERRED_FRAMES_TXDEFRD_LEN (32u)

/** \brief  Mask for Ifx_ETH_TX_DEFERRED_FRAMES_Bits.TXDEFRD */
#define IFX_ETH_TX_DEFERRED_FRAMES_TXDEFRD_MSK (0xffffffffu)

/** \brief  Offset for Ifx_ETH_TX_DEFERRED_FRAMES_Bits.TXDEFRD */
#define IFX_ETH_TX_DEFERRED_FRAMES_TXDEFRD_OFF (0u)

/** \brief  Length for Ifx_ETH_TX_EXCESSIVE_COLLISION_FRAMES_Bits.TXEXSCOL */
#define IFX_ETH_TX_EXCESSIVE_COLLISION_FRAMES_TXEXSCOL_LEN (32u)

/** \brief  Mask for Ifx_ETH_TX_EXCESSIVE_COLLISION_FRAMES_Bits.TXEXSCOL */
#define IFX_ETH_TX_EXCESSIVE_COLLISION_FRAMES_TXEXSCOL_MSK (0xffffffffu)

/** \brief  Offset for Ifx_ETH_TX_EXCESSIVE_COLLISION_FRAMES_Bits.TXEXSCOL */
#define IFX_ETH_TX_EXCESSIVE_COLLISION_FRAMES_TXEXSCOL_OFF (0u)

/** \brief  Length for Ifx_ETH_TX_EXCESSIVE_DEFERRAL_ERROR_Bits.TXEXSDEF */
#define IFX_ETH_TX_EXCESSIVE_DEFERRAL_ERROR_TXEXSDEF_LEN (32u)

/** \brief  Mask for Ifx_ETH_TX_EXCESSIVE_DEFERRAL_ERROR_Bits.TXEXSDEF */
#define IFX_ETH_TX_EXCESSIVE_DEFERRAL_ERROR_TXEXSDEF_MSK (0xffffffffu)

/** \brief  Offset for Ifx_ETH_TX_EXCESSIVE_DEFERRAL_ERROR_Bits.TXEXSDEF */
#define IFX_ETH_TX_EXCESSIVE_DEFERRAL_ERROR_TXEXSDEF_OFF (0u)

/** \brief  Length for Ifx_ETH_TX_FRAME_COUNT_GOOD_BAD_Bits.TXFRMGB */
#define IFX_ETH_TX_FRAME_COUNT_GOOD_BAD_TXFRMGB_LEN (32u)

/** \brief  Mask for Ifx_ETH_TX_FRAME_COUNT_GOOD_BAD_Bits.TXFRMGB */
#define IFX_ETH_TX_FRAME_COUNT_GOOD_BAD_TXFRMGB_MSK (0xffffffffu)

/** \brief  Offset for Ifx_ETH_TX_FRAME_COUNT_GOOD_BAD_Bits.TXFRMGB */
#define IFX_ETH_TX_FRAME_COUNT_GOOD_BAD_TXFRMGB_OFF (0u)

/** \brief  Length for Ifx_ETH_TX_FRAME_COUNT_GOOD_Bits.TXFRMG */
#define IFX_ETH_TX_FRAME_COUNT_GOOD_TXFRMG_LEN (32u)

/** \brief  Mask for Ifx_ETH_TX_FRAME_COUNT_GOOD_Bits.TXFRMG */
#define IFX_ETH_TX_FRAME_COUNT_GOOD_TXFRMG_MSK (0xffffffffu)

/** \brief  Offset for Ifx_ETH_TX_FRAME_COUNT_GOOD_Bits.TXFRMG */
#define IFX_ETH_TX_FRAME_COUNT_GOOD_TXFRMG_OFF (0u)

/** \brief  Length for Ifx_ETH_TX_LATE_COLLISION_FRAMES_Bits.TXLATECOL */
#define IFX_ETH_TX_LATE_COLLISION_FRAMES_TXLATECOL_LEN (32u)

/** \brief  Mask for Ifx_ETH_TX_LATE_COLLISION_FRAMES_Bits.TXLATECOL */
#define IFX_ETH_TX_LATE_COLLISION_FRAMES_TXLATECOL_MSK (0xffffffffu)

/** \brief  Offset for Ifx_ETH_TX_LATE_COLLISION_FRAMES_Bits.TXLATECOL */
#define IFX_ETH_TX_LATE_COLLISION_FRAMES_TXLATECOL_OFF (0u)

/** \brief  Length for Ifx_ETH_TX_MULTICAST_FRAMES_GOOD_BAD_Bits.TXMCASTGB */
#define IFX_ETH_TX_MULTICAST_FRAMES_GOOD_BAD_TXMCASTGB_LEN (32u)

/** \brief  Mask for Ifx_ETH_TX_MULTICAST_FRAMES_GOOD_BAD_Bits.TXMCASTGB */
#define IFX_ETH_TX_MULTICAST_FRAMES_GOOD_BAD_TXMCASTGB_MSK (0xffffffffu)

/** \brief  Offset for Ifx_ETH_TX_MULTICAST_FRAMES_GOOD_BAD_Bits.TXMCASTGB */
#define IFX_ETH_TX_MULTICAST_FRAMES_GOOD_BAD_TXMCASTGB_OFF (0u)

/** \brief  Length for Ifx_ETH_TX_MULTICAST_FRAMES_GOOD_Bits.TXMCASTG */
#define IFX_ETH_TX_MULTICAST_FRAMES_GOOD_TXMCASTG_LEN (32u)

/** \brief  Mask for Ifx_ETH_TX_MULTICAST_FRAMES_GOOD_Bits.TXMCASTG */
#define IFX_ETH_TX_MULTICAST_FRAMES_GOOD_TXMCASTG_MSK (0xffffffffu)

/** \brief  Offset for Ifx_ETH_TX_MULTICAST_FRAMES_GOOD_Bits.TXMCASTG */
#define IFX_ETH_TX_MULTICAST_FRAMES_GOOD_TXMCASTG_OFF (0u)

/** \brief  Length for Ifx_ETH_TX_MULTIPLE_COLLISION_GOOD_FRAMES_Bits.TXMULTCOLG */
#define IFX_ETH_TX_MULTIPLE_COLLISION_GOOD_FRAMES_TXMULTCOLG_LEN (32u)

/** \brief  Mask for Ifx_ETH_TX_MULTIPLE_COLLISION_GOOD_FRAMES_Bits.TXMULTCOLG */
#define IFX_ETH_TX_MULTIPLE_COLLISION_GOOD_FRAMES_TXMULTCOLG_MSK (0xffffffffu)

/** \brief  Offset for Ifx_ETH_TX_MULTIPLE_COLLISION_GOOD_FRAMES_Bits.TXMULTCOLG */
#define IFX_ETH_TX_MULTIPLE_COLLISION_GOOD_FRAMES_TXMULTCOLG_OFF (0u)

/** \brief  Length for Ifx_ETH_TX_OCTET_COUNT_GOOD_BAD_Bits.TXOCTGB */
#define IFX_ETH_TX_OCTET_COUNT_GOOD_BAD_TXOCTGB_LEN (32u)

/** \brief  Mask for Ifx_ETH_TX_OCTET_COUNT_GOOD_BAD_Bits.TXOCTGB */
#define IFX_ETH_TX_OCTET_COUNT_GOOD_BAD_TXOCTGB_MSK (0xffffffffu)

/** \brief  Offset for Ifx_ETH_TX_OCTET_COUNT_GOOD_BAD_Bits.TXOCTGB */
#define IFX_ETH_TX_OCTET_COUNT_GOOD_BAD_TXOCTGB_OFF (0u)

/** \brief  Length for Ifx_ETH_TX_OCTET_COUNT_GOOD_Bits.TXOCTG */
#define IFX_ETH_TX_OCTET_COUNT_GOOD_TXOCTG_LEN (32u)

/** \brief  Mask for Ifx_ETH_TX_OCTET_COUNT_GOOD_Bits.TXOCTG */
#define IFX_ETH_TX_OCTET_COUNT_GOOD_TXOCTG_MSK (0xffffffffu)

/** \brief  Offset for Ifx_ETH_TX_OCTET_COUNT_GOOD_Bits.TXOCTG */
#define IFX_ETH_TX_OCTET_COUNT_GOOD_TXOCTG_OFF (0u)

/** \brief  Length for Ifx_ETH_TX_OSIZE_FRAMES_GOOD_Bits.TXOSIZG */
#define IFX_ETH_TX_OSIZE_FRAMES_GOOD_TXOSIZG_LEN (32u)

/** \brief  Mask for Ifx_ETH_TX_OSIZE_FRAMES_GOOD_Bits.TXOSIZG */
#define IFX_ETH_TX_OSIZE_FRAMES_GOOD_TXOSIZG_MSK (0xffffffffu)

/** \brief  Offset for Ifx_ETH_TX_OSIZE_FRAMES_GOOD_Bits.TXOSIZG */
#define IFX_ETH_TX_OSIZE_FRAMES_GOOD_TXOSIZG_OFF (0u)

/** \brief  Length for Ifx_ETH_TX_PAUSE_FRAMES_Bits.TXPAUSE */
#define IFX_ETH_TX_PAUSE_FRAMES_TXPAUSE_LEN (32u)

/** \brief  Mask for Ifx_ETH_TX_PAUSE_FRAMES_Bits.TXPAUSE */
#define IFX_ETH_TX_PAUSE_FRAMES_TXPAUSE_MSK (0xffffffffu)

/** \brief  Offset for Ifx_ETH_TX_PAUSE_FRAMES_Bits.TXPAUSE */
#define IFX_ETH_TX_PAUSE_FRAMES_TXPAUSE_OFF (0u)

/** \brief  Length for Ifx_ETH_TX_SINGLE_COLLISION_GOOD_FRAMES_Bits.TXSNGLCOLG */
#define IFX_ETH_TX_SINGLE_COLLISION_GOOD_FRAMES_TXSNGLCOLG_LEN (32u)

/** \brief  Mask for Ifx_ETH_TX_SINGLE_COLLISION_GOOD_FRAMES_Bits.TXSNGLCOLG */
#define IFX_ETH_TX_SINGLE_COLLISION_GOOD_FRAMES_TXSNGLCOLG_MSK (0xffffffffu)

/** \brief  Offset for Ifx_ETH_TX_SINGLE_COLLISION_GOOD_FRAMES_Bits.TXSNGLCOLG */
#define IFX_ETH_TX_SINGLE_COLLISION_GOOD_FRAMES_TXSNGLCOLG_OFF (0u)

/** \brief  Length for Ifx_ETH_TX_UNDERFLOW_ERROR_FRAMES_Bits.TXUNDRFLW */
#define IFX_ETH_TX_UNDERFLOW_ERROR_FRAMES_TXUNDRFLW_LEN (32u)

/** \brief  Mask for Ifx_ETH_TX_UNDERFLOW_ERROR_FRAMES_Bits.TXUNDRFLW */
#define IFX_ETH_TX_UNDERFLOW_ERROR_FRAMES_TXUNDRFLW_MSK (0xffffffffu)

/** \brief  Offset for Ifx_ETH_TX_UNDERFLOW_ERROR_FRAMES_Bits.TXUNDRFLW */
#define IFX_ETH_TX_UNDERFLOW_ERROR_FRAMES_TXUNDRFLW_OFF (0u)

/** \brief  Length for Ifx_ETH_TX_UNICAST_FRAMES_GOOD_BAD_Bits.TXUCASTGB */
#define IFX_ETH_TX_UNICAST_FRAMES_GOOD_BAD_TXUCASTGB_LEN (32u)

/** \brief  Mask for Ifx_ETH_TX_UNICAST_FRAMES_GOOD_BAD_Bits.TXUCASTGB */
#define IFX_ETH_TX_UNICAST_FRAMES_GOOD_BAD_TXUCASTGB_MSK (0xffffffffu)

/** \brief  Offset for Ifx_ETH_TX_UNICAST_FRAMES_GOOD_BAD_Bits.TXUCASTGB */
#define IFX_ETH_TX_UNICAST_FRAMES_GOOD_BAD_TXUCASTGB_OFF (0u)

/** \brief  Length for Ifx_ETH_TX_VLAN_FRAMES_GOOD_Bits.TXVLANG */
#define IFX_ETH_TX_VLAN_FRAMES_GOOD_TXVLANG_LEN (32u)

/** \brief  Mask for Ifx_ETH_TX_VLAN_FRAMES_GOOD_Bits.TXVLANG */
#define IFX_ETH_TX_VLAN_FRAMES_GOOD_TXVLANG_MSK (0xffffffffu)

/** \brief  Offset for Ifx_ETH_TX_VLAN_FRAMES_GOOD_Bits.TXVLANG */
#define IFX_ETH_TX_VLAN_FRAMES_GOOD_TXVLANG_OFF (0u)

/** \brief  Length for Ifx_ETH_VERSION_Bits.SNPSVER */
#define IFX_ETH_VERSION_SNPSVER_LEN (8u)

/** \brief  Mask for Ifx_ETH_VERSION_Bits.SNPSVER */
#define IFX_ETH_VERSION_SNPSVER_MSK (0xffu)

/** \brief  Offset for Ifx_ETH_VERSION_Bits.SNPSVER */
#define IFX_ETH_VERSION_SNPSVER_OFF (0u)

/** \brief  Length for Ifx_ETH_VERSION_Bits.USERVER */
#define IFX_ETH_VERSION_USERVER_LEN (8u)

/** \brief  Mask for Ifx_ETH_VERSION_Bits.USERVER */
#define IFX_ETH_VERSION_USERVER_MSK (0xffu)

/** \brief  Offset for Ifx_ETH_VERSION_Bits.USERVER */
#define IFX_ETH_VERSION_USERVER_OFF (8u)

/** \brief  Length for Ifx_ETH_VLAN_TAG_Bits.ESVL */
#define IFX_ETH_VLAN_TAG_ESVL_LEN (1u)

/** \brief  Mask for Ifx_ETH_VLAN_TAG_Bits.ESVL */
#define IFX_ETH_VLAN_TAG_ESVL_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_VLAN_TAG_Bits.ESVL */
#define IFX_ETH_VLAN_TAG_ESVL_OFF (18u)

/** \brief  Length for Ifx_ETH_VLAN_TAG_Bits.ETV */
#define IFX_ETH_VLAN_TAG_ETV_LEN (1u)

/** \brief  Mask for Ifx_ETH_VLAN_TAG_Bits.ETV */
#define IFX_ETH_VLAN_TAG_ETV_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_VLAN_TAG_Bits.ETV */
#define IFX_ETH_VLAN_TAG_ETV_OFF (16u)

/** \brief  Length for Ifx_ETH_VLAN_TAG_Bits.VL */
#define IFX_ETH_VLAN_TAG_VL_LEN (16u)

/** \brief  Mask for Ifx_ETH_VLAN_TAG_Bits.VL */
#define IFX_ETH_VLAN_TAG_VL_MSK (0xffffu)

/** \brief  Offset for Ifx_ETH_VLAN_TAG_Bits.VL */
#define IFX_ETH_VLAN_TAG_VL_OFF (0u)

/** \brief  Length for Ifx_ETH_VLAN_TAG_Bits.VTHM */
#define IFX_ETH_VLAN_TAG_VTHM_LEN (1u)

/** \brief  Mask for Ifx_ETH_VLAN_TAG_Bits.VTHM */
#define IFX_ETH_VLAN_TAG_VTHM_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_VLAN_TAG_Bits.VTHM */
#define IFX_ETH_VLAN_TAG_VTHM_OFF (19u)

/** \brief  Length for Ifx_ETH_VLAN_TAG_Bits.VTIM */
#define IFX_ETH_VLAN_TAG_VTIM_LEN (1u)

/** \brief  Mask for Ifx_ETH_VLAN_TAG_Bits.VTIM */
#define IFX_ETH_VLAN_TAG_VTIM_MSK (0x1u)

/** \brief  Offset for Ifx_ETH_VLAN_TAG_Bits.VTIM */
#define IFX_ETH_VLAN_TAG_VTIM_OFF (17u)
/** \}  */
/******************************************************************************/
/******************************************************************************/
#endif /* IFXETH_BF_H */
