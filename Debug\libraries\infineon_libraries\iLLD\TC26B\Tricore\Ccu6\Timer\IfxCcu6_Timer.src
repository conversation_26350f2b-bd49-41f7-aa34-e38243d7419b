	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc38200a --dep-file=IfxCcu6_Timer.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/infineon_libraries/iLLD/TC26B/Tricore/Ccu6/Timer/IfxCcu6_Timer.src ../libraries/infineon_libraries/iLLD/TC26B/Tricore/Ccu6/Timer/IfxCcu6_Timer.c"
	.compiler_name		"ctc"
	;source	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Ccu6/Timer/IfxCcu6_Timer.c'

	
$TC16X
	
	.sdecl	'.text.IfxCcu6_Timer.IfxCcu6_Timer_countOneStep',code,cluster('IfxCcu6_Timer_countOneStep')
	.sect	'.text.IfxCcu6_Timer.IfxCcu6_Timer_countOneStep'
	.align	2
	
	.global	IfxCcu6_Timer_countOneStep
; Function IfxCcu6_Timer_countOneStep
.L74:
IfxCcu6_Timer_countOneStep:	.type	func
	ld.bu	d15,[a4]20
.L786:
	jne	d15,#1,.L2
.L787:
	ld.a	a15,[a4]16
.L788:
	mov	d15,#0
.L789:
	mov	d0,#1
.L336:
	mov	d1,#0
.L527:
	insert	d1,d1,d15,#6,#1
.L790:
	insert	d1,d1,d0,#14,#1
.L791:
	st.w	[a15]120,d1
.L337:
	ld.a	a15,[a4]16
.L792:
	mov	d15,#1
.L793:
	mov	d0,#1
.L347:
	mul	d1,d15,#2
.L528:
	add	d1,#2
.L794:
	mov	d2,#3
.L795:
	sh	d2,d2,d1
.L530:
	ld.w	d15,[a15]20
.L796:
	mov	d3,#-1
	xor	d2,d3
.L531:
	and	d15,d2
.L797:
	sh	d0,d0,d1
.L798:
	or	d15,d0
.L799:
	st.w	[a15]20,d15
.L348:
	ld.a	a15,[a4]16
.L800:
	mov	d15,#0
.L801:
	mov	d0,#1
.L355:
	mov	d1,#0
.L529:
	insert	d1,d1,d15,#5,#1
.L802:
	insert	d1,d1,d0,#13,#1
.L803:
	st.w	[a15]120,d1
.L356:
	j	.L3
.L2:
	ld.bu	d15,[a4]20
.L804:
	jne	d15,#0,.L4
.L805:
	ld.a	a15,[a4]16
.L806:
	mov	d15,#1
.L807:
	mov	d0,#0
.L365:
	mov	d1,#0
.L532:
	insert	d1,d1,d15,#6,#1
.L808:
	insert	d1,d1,d0,#14,#1
.L809:
	st.w	[a15]120,d1
.L366:
	ld.a	a15,[a4]16
.L810:
	mov	d15,#0
.L811:
	mov	d0,#1
.L368:
	mul	d1,d15,#2
.L533:
	add	d1,#2
.L812:
	mov	d2,#3
.L813:
	sh	d2,d2,d1
.L535:
	ld.w	d15,[a15]20
.L814:
	mov	d3,#-1
	xor	d2,d3
.L536:
	and	d15,d2
.L815:
	sh	d0,d0,d1
.L816:
	or	d15,d0
.L817:
	st.w	[a15]20,d15
.L369:
	ld.a	a15,[a4]16
.L818:
	mov	d15,#1
.L819:
	mov	d0,#0
.L372:
	mov	d1,#0
.L534:
	insert	d1,d1,d15,#5,#1
.L820:
	insert	d1,d1,d0,#13,#1
.L821:
	st.w	[a15]120,d1
.L4:
.L3:
	ret
.L333:
	
__IfxCcu6_Timer_countOneStep_function_end:
	.size	IfxCcu6_Timer_countOneStep,__IfxCcu6_Timer_countOneStep_function_end-IfxCcu6_Timer_countOneStep
.L107:
	; End of function
	
	.sdecl	'.text.IfxCcu6_Timer.IfxCcu6_Timer_initModule',code,cluster('IfxCcu6_Timer_initModule')
	.sect	'.text.IfxCcu6_Timer.IfxCcu6_Timer_initModule'
	.align	2
	
	.global	IfxCcu6_Timer_initModule
; Function IfxCcu6_Timer_initModule
.L76:
IfxCcu6_Timer_initModule:	.type	func
	mov.aa	a13,a4
.L538:
	mov.aa	a15,a5
.L539:
	ld.a	a12,[a15]24
.L541:
	st.a	[a13]16,a12
.L141:
	ld.bu	d15,[a12]
	extr.u	d15,d15,#1,#1
.L629:
	eq	d15,d15,#0
.L630:
	j	.L5
.L5:
	jne	d15,#0,.L6
.L631:
	mov.aa	a4,a12
.L537:
	call	IfxCcu6_enableModule
.L6:
	ld.bu	d15,[a15]28
.L632:
	jeq	d15,#1,.L7
.L633:
	ld.bu	d15,[a15]29
	jeq	d15,#0,.L8
.L7:
	mov	d15,#1
.L146:
	mov	d0,#1
.L634:
	sh	d0,d0,d15
.L543:
	ld.w	d15,[a12]4
.L635:
	and	d15,d0
.L636:
	jeq	d15,#0,.L9
.L637:
	mov	d15,#1
.L638:
	j	.L10
.L9:
	mov	d15,#0
.L10:
	j	.L11
.L11:
	jne	d15,#0,.L12
.L147:
	mov	d15,#1
.L156:
	mov	d0,#1
.L544:
	sh	d0,d0,d15
.L545:
	ld.w	d15,[a12]4
.L639:
	or	d15,d0
.L640:
	st.w	[a12]4,d15
.L12:
	ld.bu	d15,[a15]41
.L641:
	jeq	d15,#0,.L13
.L642:
	ld.w	d15,[a15]44
.L643:
	jeq	d15,#0,.L14
.L644:
	ld.a	a4,[a15]24
.L645:
	ld.a	a5,[a15]44
	call	IfxCcu6_setT13InputSignal
.L14:
	mov	d15,#1
.L646:
	ld.bu	d0,[a15]48
.L164:
	mul	d1,d15,#2
.L546:
	add	d1,#2
.L647:
	mov	d2,#3
.L648:
	sh	d2,d2,d1
.L547:
	ld.w	d15,[a12]20
.L649:
	mov	d3,#-1
	xor	d2,d3
.L548:
	and	d15,d2
.L650:
	sh	d0,d0,d1
.L651:
	or	d15,d0
.L652:
	st.w	[a12]20,d15
.L165:
	ld.w	d15,[a15]12
.L653:
	extr.u	d15,d15,#0,#16
.L176:
	ld.hu	d0,[a12]84
.L654:
	insert	d15,d0,d15,#0,#16
	st.h	[a12]84,d15
.L177:
	j	.L15
.L13:
	ld.w	d4,[a15]8
.L655:
	ld.w	d5,[a15]12
	mov.aa	a4,a12
.L549:
	call	IfxCcu6_setT13Frequency
.L15:
	ld.hu	d15,[a15]56
.L184:
	ld.hu	d0,[a12]80
.L656:
	insert	d15,d0,d15,#0,#16
	st.h	[a12]80,d15
.L185:
	ld.bu	d15,[a15]93
.L657:
	jeq	d15,#0,.L16
.L658:
	ld.bu	d15,[a15]29
.L659:
	jne	d15,#0,.L17
.L660:
	ld.bu	d15,[a15]58
.L192:
	ld.bu	d0,[a12]116
.L661:
	insert	d15,d0,d15,#2,#3
	st.b	[a12]116,d15
.L193:
	ld.bu	d15,[a15]59
.L200:
	ld.bu	d0,[a12]116
.L662:
	insert	d15,d0,d15,#5,#2
	st.b	[a12]116,d15
.L17:
.L16:
	j	.L18
.L8:
.L18:
	ld.bu	d15,[a15]28
.L663:
	jeq	d15,#0,.L19
.L664:
	ld.bu	d15,[a15]93
	jne	d15,#0,.L20
.L665:
	ld.bu	d15,[a15]29
	jeq	d15,#0,.L21
.L20:
.L19:
	ld.w	d8,[a15]4
.L550:
	mov	d1,#0
.L207:
	mov	d0,#1
.L666:
	sh	d0,d0,d1
.L551:
	ld.w	d15,[a12]4
.L667:
	and	d0,d15
.L552:
	jeq	d0,#0,.L22
.L668:
	mov	d15,#1
.L669:
	j	.L23
.L22:
	mov	d15,#0
.L23:
	j	.L24
.L24:
	jne	d15,#0,.L25
.L208:
	mov	d0,#0
.L210:
	mov	d1,#1
.L670:
	sh	d1,d1,d0
.L553:
	ld.w	d15,[a12]4
.L671:
	or	d15,d1
.L672:
	st.w	[a12]4,d15
.L25:
	ld.bu	d15,[a15]93
.L673:
	jeq	d15,#0,.L26
.L674:
	ld.bu	d15,[a15]29
.L675:
	jne	d15,#0,.L27
.L676:
	ld.w	d15,[a15]16
.L677:
	jeq	d15,#0,.L28
.L678:
	ld.bu	d15,[a15]58
.L679:
	jne	d15,#5,.L29
.L680:
	ld.w	d8,[a15]16
.L29:
	j	.L30
.L28:
.L27:
.L26:
.L30:
	ld.bu	d15,[a15]32
.L681:
	jeq	d15,#0,.L31
.L682:
	ld.w	d15,[a15]36
.L683:
	jeq	d15,#0,.L32
.L684:
	ld.a	a4,[a15]24
.L685:
	ld.a	a5,[a15]36
	call	IfxCcu6_setT12InputSignal
.L32:
	mov	d15,#0
.L686:
	ld.bu	d0,[a15]40
.L212:
	mul	d15,d15,#2
.L687:
	add	d1,d15,#2
.L554:
	mov	d15,#3
.L688:
	sh	d2,d15,d1
.L556:
	ld.w	d15,[a12]20
.L689:
	mov	d3,#-1
	xor	d2,d3
.L557:
	and	d15,d2
.L690:
	sh	d0,d0,d1
.L691:
	or	d15,d0
.L692:
	st.w	[a12]20,d15
.L213:
	ld.bu	d15,[a15]52
.L217:
	ld.bu	d0,[a12]112
.L693:
	insert	d15,d0,d15,#7,#1
	st.b	[a12]112,d15
.L218:
	ld.bu	d15,[a15]52
.L694:
	jne	d15,#1,.L33
.L695:
	mov	d15,#2
.L696:
	div.u	e0,d8,d15
.L555:
	add	d8,d0,#-1
.L33:
	extr.u	d15,d8,#0,#16
.L225:
	ld.hu	d0,[a12]36
.L697:
	insert	d15,d0,d15,#0,#16
	st.h	[a12]36,d15
.L226:
	j	.L34
.L31:
	ld.w	d4,[a15]
.L698:
	ld.bu	d6,[a15]52
	mov.aa	a4,a12
.L558:
	mov	d5,d8
.L559:
	call	IfxCcu6_setT12Frequency
.L34:
	ld.hu	d15,[a15]54
.L233:
	ld.hu	d0,[a12]32
.L699:
	insert	d15,d0,d15,#0,#16
	st.h	[a12]32,d15
.L21:
	ld.a	a14,[a15]96
.L560:
	jz.a	a14,.L35
.L241:
	ld.a	a12,[a14]
.L542:
	jz.a	a12,.L36
.L700:
	ld.b	d15,[a14]8
.L245:
	ld.a	a4,[a12]4
.L701:
	ld.bu	d4,[a12]8
.L252:
	extr.u	d5,d15,#0,#8
	call	IfxPort_setPinMode
.L253:
	ld.a	a4,[a12]
.L702:
	mov.aa	a5,a12
.L562:
	call	IfxCcu6_setT12InputSignal
.L36:
	ld.a	a12,[a14]4
.L561:
	jz.a	a12,.L37
.L703:
	ld.b	d15,[a14]8
.L264:
	ld.a	a4,[a12]4
.L704:
	ld.bu	d4,[a12]8
.L270:
	extr.u	d5,d15,#0,#8
	call	IfxPort_setPinMode
.L271:
	ld.a	a4,[a12]
.L705:
	mov.aa	a5,a12
.L563:
	call	IfxCcu6_setT13InputSignal
.L37:
.L35:
	ld.hu	d15,[a15]62
.L706:
	jlt.u	d15,#1,.L38
.L707:
	ld.a	a2,[a15]24
.L708:
	ld.bu	d0,[a15]60
.L273:
	mov	d15,#1
.L709:
	sh	d15,d15,d0
.L564:
	ld.w	d0,[a2]176
.L710:
	or	d0,d15
.L711:
	st.w	[a2]176,d0
.L274:
	ld.a	a4,[a15]24
.L712:
	ld.bu	d4,[a15]60
.L713:
	ld.bu	d5,[a15]61
	call	IfxCcu6_routeInterruptNode
.L281:
	ld.a	a4,[a15]24
.L714:
	ld.bu	d4,[a15]61
	call	IfxCcu6_getSrcAddress
.L565:
	ld.bu	d0,[a15]64
.L715:
	ld.hu	d15,[a15]62
.L285:
	ld.bu	d1,[a2]
.L716:
	extr.u	d15,d15,#0,#8
.L717:
	insert	d15,d1,d15,#0,#8
	st.b	[a2],d15
.L718:
	ld.bu	d15,[a2]1
.L719:
	insert	d15,d15,d0,#3,#2
	st.b	[a2]1,d15
.L295:
	ld.bu	d15,[a2]3
.L720:
	or	d15,#2
	st.b	[a2]3,d15
.L286:
	ld.bu	d15,[a2]1
.L721:
	or	d15,#4
	st.b	[a2]1,d15
.L38:
	ld.hu	d15,[a15]68
.L722:
	jlt.u	d15,#1,.L39
.L723:
	ld.a	a2,[a15]24
.L724:
	ld.bu	d0,[a15]66
.L303:
	mov	d15,#1
.L725:
	sh	d15,d15,d0
.L566:
	ld.w	d0,[a2]176
.L726:
	or	d0,d15
.L727:
	st.w	[a2]176,d0
.L304:
	ld.a	a4,[a15]24
.L728:
	ld.bu	d4,[a15]66
.L729:
	ld.bu	d5,[a15]67
	call	IfxCcu6_routeInterruptNode
.L306:
	ld.a	a4,[a15]24
.L730:
	ld.bu	d4,[a15]67
	call	IfxCcu6_getSrcAddress
.L567:
	ld.bu	d0,[a15]70
.L731:
	ld.hu	d15,[a15]68
.L308:
	ld.bu	d1,[a2]
.L732:
	extr.u	d15,d15,#0,#8
.L733:
	insert	d15,d1,d15,#0,#8
	st.b	[a2],d15
.L734:
	ld.bu	d15,[a2]1
.L735:
	insert	d15,d15,d0,#3,#2
	st.b	[a2]1,d15
.L310:
	ld.bu	d15,[a2]3
.L736:
	or	d15,#2
	st.b	[a2]3,d15
.L309:
	ld.bu	d15,[a2]1
.L737:
	or	d15,#4
	st.b	[a2]1,d15
.L39:
	ld.hu	d15,[a15]74
.L738:
	jlt.u	d15,#1,.L40
.L739:
	ld.a	a2,[a15]24
.L740:
	ld.bu	d0,[a15]72
.L311:
	mov	d15,#1
.L741:
	sh	d15,d15,d0
.L568:
	ld.w	d0,[a2]176
.L742:
	or	d0,d15
.L743:
	st.w	[a2]176,d0
.L312:
	ld.a	a4,[a15]24
.L744:
	ld.bu	d4,[a15]72
.L745:
	ld.bu	d5,[a15]73
	call	IfxCcu6_routeInterruptNode
.L314:
	ld.a	a4,[a15]24
.L746:
	ld.bu	d4,[a15]73
	call	IfxCcu6_getSrcAddress
.L569:
	ld.bu	d0,[a15]76
.L747:
	ld.hu	d15,[a15]74
.L316:
	ld.bu	d1,[a2]
.L748:
	extr.u	d15,d15,#0,#8
.L749:
	insert	d15,d1,d15,#0,#8
	st.b	[a2],d15
.L750:
	ld.bu	d15,[a2]1
.L751:
	insert	d15,d15,d0,#3,#2
	st.b	[a2]1,d15
.L318:
	ld.bu	d15,[a2]3
.L752:
	or	d15,#2
	st.b	[a2]3,d15
.L317:
	ld.bu	d15,[a2]1
.L753:
	or	d15,#4
	st.b	[a2]1,d15
.L40:
	ld.hu	d15,[a15]80
.L754:
	jlt.u	d15,#1,.L41
.L755:
	ld.a	a2,[a15]24
.L756:
	ld.bu	d0,[a15]78
.L319:
	mov	d15,#1
.L757:
	sh	d15,d15,d0
.L570:
	ld.w	d0,[a2]176
.L758:
	or	d0,d15
.L759:
	st.w	[a2]176,d0
.L320:
	ld.a	a4,[a15]24
.L760:
	ld.bu	d4,[a15]78
.L761:
	ld.bu	d5,[a15]79
	call	IfxCcu6_routeInterruptNode
.L322:
	ld.a	a4,[a15]24
.L762:
	ld.bu	d4,[a15]79
	call	IfxCcu6_getSrcAddress
.L571:
	ld.bu	d0,[a15]82
.L763:
	ld.hu	d15,[a15]80
.L324:
	ld.bu	d1,[a2]
.L764:
	extr.u	d15,d15,#0,#8
.L765:
	insert	d15,d1,d15,#0,#8
	st.b	[a2],d15
.L766:
	ld.bu	d15,[a2]1
.L767:
	insert	d15,d15,d0,#3,#2
	st.b	[a2]1,d15
.L326:
	ld.bu	d15,[a2]3
.L768:
	or	d15,#2
	st.b	[a2]3,d15
.L325:
	ld.bu	d15,[a2]1
.L769:
	or	d15,#4
	st.b	[a2]1,d15
.L41:
	ld.bu	d15,[a15]28
.L770:
	st.b	[a13]20,d15
.L771:
	lea	a15,[a15]84
.L540:
	lea	a2,[a13]24
	mov.a	a4,#2
.L42:
	ld.w	d15,[a15+]
	st.w	[a2+],d15
	loop	a4,.L42
.L772:
	ret
.L133:
	
__IfxCcu6_Timer_initModule_function_end:
	.size	IfxCcu6_Timer_initModule,__IfxCcu6_Timer_initModule_function_end-IfxCcu6_Timer_initModule
.L97:
	; End of function
	
	.sdecl	'.text.IfxCcu6_Timer.IfxCcu6_Timer_initModuleConfig',code,cluster('IfxCcu6_Timer_initModuleConfig')
	.sect	'.text.IfxCcu6_Timer.IfxCcu6_Timer_initModuleConfig'
	.align	2
	
	.global	IfxCcu6_Timer_initModuleConfig
; Function IfxCcu6_Timer_initModuleConfig
.L78:
IfxCcu6_Timer_initModuleConfig:	.type	func
	sub.a	a10,#104
.L572:
	movh.a	a15,#@his(.1.ini)
	lea	a15,[a15]@los(.1.ini)
	lea	a15,[a15]0
.L777:
	lea	a2,[a10]0
	lea	a6,24
.L43:
	ld.w	d15,[a15+]
	st.w	[a2+],d15
	loop	a6,.L43
.L778:
	lea	a15,[a10]0
.L779:
	lea	a2,[a4]0
	lea	a6,24
.L44:
	ld.w	d15,[a15+]
	st.w	[a2+],d15
	loop	a6,.L44
.L780:
	st.a	[a4]24,a5
.L781:
	ret
.L327:
	
__IfxCcu6_Timer_initModuleConfig_function_end:
	.size	IfxCcu6_Timer_initModuleConfig,__IfxCcu6_Timer_initModuleConfig_function_end-IfxCcu6_Timer_initModuleConfig
.L102:
	; End of function
	
	.sdecl	'.text.IfxCcu6_Timer.IfxCcu6_Timer_start',code,cluster('IfxCcu6_Timer_start')
	.sect	'.text.IfxCcu6_Timer.IfxCcu6_Timer_start'
	.align	2
	
	.global	IfxCcu6_Timer_start
; Function IfxCcu6_Timer_start
.L80:
IfxCcu6_Timer_start:	.type	func
	mov.aa	a15,a4
.L574:
	ld.bu	d15,[a15]20
.L826:
	jne	d15,#1,.L45
.L827:
	ld.a	a2,[a15]16
.L828:
	mov	d15,#0
.L829:
	mov	d0,#1
.L376:
	mov	d1,#0
.L576:
	insert	d1,d1,d15,#6,#1
.L830:
	insert	d1,d1,d0,#14,#1
.L831:
	st.w	[a2]120,d1
.L377:
	ld.bu	d15,[a15]33
.L832:
	jne	d15,#0,.L46
.L833:
	ld.w	d15,[a15]28
.L834:
	jeq	d15,#0,.L47
.L835:
	ld.a	a2,[a15]16
.L836:
	mov	d0,#1
.L837:
	ld.bu	d15,[a15]32
.L383:
	mul	d0,d0,#2
.L838:
	add	d1,d0,#8
.L577:
	mov	d0,#3
.L839:
	sh	d0,d0,d1
.L579:
	ld.w	d2,[a2]116
.L840:
	mov	d3,#-1
	xor	d0,d3
.L580:
	and	d2,d0
.L841:
	sh	d15,d15,d1
.L842:
	or	d2,d15
.L843:
	st.w	[a2]116,d2
.L384:
	ld.a	a4,[a15]16
.L573:
	ld.a	a5,[a15]28
	call	IfxCcu6_setT13InputSignal
.L578:
	j	.L48
.L47:
	ld.a	a2,[a15]16
.L844:
	mov	d15,#0
.L845:
	mov	d0,#1
.L395:
	mov	d1,#0
.L581:
	insert	d1,d1,d15,#1,#1
.L846:
	insert	d1,d1,d0,#9,#1
.L847:
	st.w	[a2]120,d1
.L48:
	j	.L49
.L46:
.L49:
	j	.L50
.L45:
.L50:
	ld.bu	d15,[a15]20
.L848:
	jeq	d15,#0,.L51
.L849:
	ld.bu	d15,[a15]33
	jeq	d15,#0,.L52
.L51:
	ld.a	a2,[a15]16
.L850:
	mov	d15,#1
.L851:
	mov	d0,#0
.L404:
	mov	d1,#0
.L582:
	insert	d1,d1,d15,#6,#1
.L852:
	insert	d1,d1,d0,#14,#1
.L853:
	st.w	[a2]120,d1
.L405:
	ld.w	d15,[a15]24
.L854:
	jeq	d15,#0,.L53
.L855:
	ld.a	a2,[a15]16
.L856:
	mov	d0,#0
.L857:
	ld.bu	d15,[a15]32
.L407:
	mul	d0,d0,#2
.L858:
	add	d1,d0,#8
.L583:
	mov	d0,#3
.L859:
	sh	d0,d0,d1
.L585:
	ld.w	d2,[a2]116
.L860:
	mov	d3,#-1
	xor	d0,d3
.L586:
	and	d2,d0
.L861:
	sh	d15,d15,d1
.L862:
	or	d2,d15
.L863:
	st.w	[a2]116,d2
.L408:
	ld.a	a4,[a15]16
.L864:
	ld.a	a5,[a15]24
	call	IfxCcu6_setT12InputSignal
.L584:
	j	.L54
.L53:
	ld.a	a15,[a15]16
.L575:
	mov	d15,#1
.L865:
	mov	d0,#0
.L411:
	mov	d1,#0
.L587:
	insert	d1,d1,d15,#1,#1
.L866:
	insert	d1,d1,d0,#9,#1
.L867:
	st.w	[a15]120,d1
.L54:
	j	.L55
.L52:
.L55:
	ret
.L374:
	
__IfxCcu6_Timer_start_function_end:
	.size	IfxCcu6_Timer_start,__IfxCcu6_Timer_start_function_end-IfxCcu6_Timer_start
.L112:
	; End of function
	
	.sdecl	'.text.IfxCcu6_Timer.IfxCcu6_Timer_startSingleShotMode',code,cluster('IfxCcu6_Timer_startSingleShotMode')
	.sect	'.text.IfxCcu6_Timer.IfxCcu6_Timer_startSingleShotMode'
	.align	2
	
	.global	IfxCcu6_Timer_startSingleShotMode
; Function IfxCcu6_Timer_startSingleShotMode
.L82:
IfxCcu6_Timer_startSingleShotMode:	.type	func
	mov.aa	a15,a4
.L589:
	ld.bu	d15,[a15]20
.L872:
	jne	d15,#1,.L56
.L873:
	ld.a	a2,[a15]16
.L874:
	mov	d15,#0
.L875:
	mov	d0,#1
.L415:
	mov	d1,#0
.L591:
	insert	d1,d1,d15,#6,#1
.L876:
	insert	d1,d1,d0,#14,#1
.L877:
	st.w	[a2]120,d1
.L416:
	ld.a	a2,[a15]16
.L878:
	mov	d0,#1
.L422:
	mov	d15,#1
.L879:
	sh	d15,d15,d0
.L593:
	ld.w	d0,[a2]116
.L880:
	or	d0,d15
.L881:
	st.w	[a2]116,d0
.L423:
	ld.bu	d15,[a15]33
.L594:
	jne	d15,#0,.L57
.L882:
	ld.w	d15,[a15]28
.L883:
	jeq	d15,#0,.L58
.L884:
	ld.a	a2,[a15]16
.L885:
	mov	d0,#1
.L886:
	ld.bu	d15,[a15]32
.L430:
	mul	d0,d0,#2
.L887:
	add	d1,d0,#8
.L592:
	mov	d0,#3
.L888:
	sh	d0,d0,d1
.L596:
	ld.w	d2,[a2]116
.L889:
	mov	d3,#-1
	xor	d0,d3
.L597:
	and	d2,d0
.L890:
	sh	d15,d15,d1
.L891:
	or	d2,d15
.L892:
	st.w	[a2]116,d2
.L431:
	ld.a	a4,[a15]16
.L588:
	ld.a	a5,[a15]28
	call	IfxCcu6_setT13InputSignal
.L595:
	j	.L59
.L58:
	ld.a	a2,[a15]16
.L893:
	mov	d15,#0
.L894:
	mov	d0,#1
.L437:
	mov	d1,#0
.L598:
	insert	d1,d1,d15,#1,#1
.L895:
	insert	d1,d1,d0,#9,#1
.L896:
	st.w	[a2]120,d1
.L59:
	j	.L60
.L57:
.L60:
	j	.L61
.L56:
.L61:
	ld.bu	d15,[a15]20
.L897:
	jeq	d15,#0,.L62
.L898:
	ld.bu	d15,[a15]33
	jeq	d15,#0,.L63
.L62:
	ld.a	a2,[a15]16
.L899:
	mov	d15,#1
.L900:
	mov	d0,#0
.L442:
	mov	d1,#0
.L599:
	insert	d1,d1,d15,#6,#1
.L901:
	insert	d1,d1,d0,#14,#1
.L902:
	st.w	[a2]120,d1
.L443:
	ld.a	a2,[a15]16
.L903:
	mov	d0,#0
.L445:
	mov	d15,#1
.L904:
	sh	d15,d15,d0
.L601:
	ld.w	d0,[a2]116
.L905:
	or	d0,d15
.L906:
	st.w	[a2]116,d0
.L446:
	ld.w	d15,[a15]24
.L602:
	jeq	d15,#0,.L64
.L907:
	ld.a	a2,[a15]16
.L908:
	mov	d0,#0
.L909:
	ld.bu	d15,[a15]32
.L448:
	mul	d0,d0,#2
.L910:
	add	d1,d0,#8
.L600:
	mov	d0,#3
.L911:
	sh	d0,d0,d1
.L604:
	ld.w	d2,[a2]116
.L912:
	mov	d3,#-1
	xor	d0,d3
.L605:
	and	d2,d0
.L913:
	sh	d15,d15,d1
.L914:
	or	d2,d15
.L915:
	st.w	[a2]116,d2
.L449:
	ld.a	a4,[a15]16
.L916:
	ld.a	a5,[a15]24
	call	IfxCcu6_setT12InputSignal
.L603:
	j	.L65
.L64:
	ld.a	a15,[a15]16
.L590:
	mov	d15,#1
.L917:
	mov	d0,#0
.L452:
	mov	d1,#0
.L606:
	insert	d1,d1,d15,#1,#1
.L918:
	insert	d1,d1,d0,#9,#1
.L919:
	st.w	[a15]120,d1
.L65:
	j	.L66
.L63:
.L66:
	ret
.L413:
	
__IfxCcu6_Timer_startSingleShotMode_function_end:
	.size	IfxCcu6_Timer_startSingleShotMode,__IfxCcu6_Timer_startSingleShotMode_function_end-IfxCcu6_Timer_startSingleShotMode
.L117:
	; End of function
	
	.sdecl	'.text.IfxCcu6_Timer.IfxCcu6_Timer_stop',code,cluster('IfxCcu6_Timer_stop')
	.sect	'.text.IfxCcu6_Timer.IfxCcu6_Timer_stop'
	.align	2
	
	.global	IfxCcu6_Timer_stop
; Function IfxCcu6_Timer_stop
.L84:
IfxCcu6_Timer_stop:	.type	func
	ld.bu	d15,[a4]20
.L924:
	jeq	d15,#1,.L67
.L925:
	ld.bu	d15,[a4]33
	jeq	d15,#0,.L68
.L67:
	ld.a	a15,[a4]16
.L926:
	mov	d15,#0
.L927:
	mov	d0,#1
.L457:
	mov	d1,#0
.L607:
	insert	d1,d1,d15,#7,#1
.L928:
	insert	d1,d1,d0,#15,#1
.L929:
	st.w	[a15]120,d1
.L458:
	ld.w	d15,[a4]28
.L930:
	jeq	d15,#0,.L69
.L931:
	ld.a	a15,[a4]16
.L932:
	mov	d0,#1
.L933:
	mov	d15,#0
.L467:
	mul	d0,d0,#2
.L934:
	add	d1,d0,#8
.L608:
	mov	d0,#3
.L935:
	sh	d0,d0,d1
.L609:
	ld.w	d2,[a15]116
.L936:
	mov	d3,#-1
	xor	d0,d3
.L610:
	and	d2,d0
.L937:
	sh	d15,d15,d1
.L938:
	or	d2,d15
.L939:
	st.w	[a15]116,d2
.L69:
	ld.bu	d15,[a4]33
.L940:
	jeq	d15,#0,.L70
.L941:
	ld.a	a15,[a4]16
.L942:
	mov	d15,#0
.L473:
	ld.bu	d0,[a15]116
.L943:
	insert	d15,d0,d15,#2,#3
	st.b	[a15]116,d15
.L474:
	ld.a	a15,[a4]16
.L944:
	mov	d15,#0
.L477:
	ld.bu	d0,[a15]116
.L945:
	insert	d15,d0,d15,#5,#2
	st.b	[a15]116,d15
.L70:
	ld.a	a15,[a4]16
.L946:
	mov	d15,#0
.L947:
	mov	d0,#1
.L481:
	mov	d1,#0
.L611:
	insert	d1,d1,d15,#0,#1
.L948:
	insert	d1,d1,d0,#8,#1
.L949:
	st.w	[a15]120,d1
.L482:
	j	.L71
.L68:
	ld.a	a15,[a4]16
.L950:
	mov	d15,#1
.L951:
	mov	d0,#0
.L491:
	mov	d1,#0
.L612:
	insert	d1,d1,d15,#7,#1
.L952:
	insert	d1,d1,d0,#15,#1
.L953:
	st.w	[a15]120,d1
.L492:
	ld.w	d15,[a4]24
.L954:
	jeq	d15,#0,.L72
.L955:
	ld.a	a15,[a4]16
.L956:
	mov	d0,#0
.L957:
	mov	d15,#0
.L494:
	mul	d0,d0,#2
.L958:
	add	d1,d0,#8
.L613:
	mov	d0,#3
.L959:
	sh	d0,d0,d1
.L614:
	ld.w	d2,[a15]116
.L960:
	mov	d3,#-1
	xor	d0,d3
.L615:
	and	d2,d0
.L961:
	sh	d15,d15,d1
.L962:
	or	d2,d15
.L963:
	st.w	[a15]116,d2
.L72:
	ld.a	a15,[a4]16
.L964:
	mov	d15,#1
.L965:
	mov	d0,#0
.L497:
	mov	d1,#0
.L616:
	insert	d1,d1,d15,#0,#1
.L966:
	insert	d1,d1,d0,#8,#1
.L967:
	st.w	[a15]120,d1
.L71:
	ret
.L454:
	
__IfxCcu6_Timer_stop_function_end:
	.size	IfxCcu6_Timer_stop,__IfxCcu6_Timer_stop_function_end-IfxCcu6_Timer_stop
.L122:
	; End of function
	
	.sdecl	'.text.IfxCcu6_Timer.IfxCcu6_Timer_synchronousStart',code,cluster('IfxCcu6_Timer_synchronousStart')
	.sect	'.text.IfxCcu6_Timer.IfxCcu6_Timer_synchronousStart'
	.align	2
	
	.global	IfxCcu6_Timer_synchronousStart
; Function IfxCcu6_Timer_synchronousStart
.L86:
IfxCcu6_Timer_synchronousStart:	.type	func
	ld.a	a15,[a4]16
.L972:
	mov	d15,#1
.L973:
	mov	d0,#1
.L501:
	mov	d1,#0
.L617:
	insert	d1,d1,d15,#6,#1
.L974:
	insert	d1,d1,d0,#14,#1
.L975:
	st.w	[a15]120,d1
.L502:
	ld.a	a15,[a4]16
.L976:
	mov	d15,#1
.L977:
	mov	d0,#1
.L507:
	mov	d1,#0
.L618:
	insert	d1,d1,d15,#1,#1
.L978:
	insert	d1,d1,d0,#9,#1
.L979:
	st.w	[a15]120,d1
.L508:
	ret
.L499:
	
__IfxCcu6_Timer_synchronousStart_function_end:
	.size	IfxCcu6_Timer_synchronousStart,__IfxCcu6_Timer_synchronousStart_function_end-IfxCcu6_Timer_synchronousStart
.L127:
	; End of function
	
	.sdecl	'.text.IfxCcu6_Timer.IfxCcu6_Timer_synchronousStop',code,cluster('IfxCcu6_Timer_synchronousStop')
	.sect	'.text.IfxCcu6_Timer.IfxCcu6_Timer_synchronousStop'
	.align	2
	
	.global	IfxCcu6_Timer_synchronousStop
; Function IfxCcu6_Timer_synchronousStop
.L88:
IfxCcu6_Timer_synchronousStop:	.type	func
	ld.a	a15,[a4]16
.L984:
	mov	d15,#1
.L985:
	mov	d0,#1
.L515:
	mov	d1,#0
.L619:
	insert	d1,d1,d15,#7,#1
.L986:
	insert	d1,d1,d0,#15,#1
.L987:
	st.w	[a15]120,d1
.L516:
	ld.a	a15,[a4]16
.L988:
	mov	d15,#1
.L989:
	mov	d0,#1
.L521:
	mov	d1,#0
.L620:
	insert	d1,d1,d15,#0,#1
.L990:
	insert	d1,d1,d0,#8,#1
.L991:
	st.w	[a15]120,d1
.L522:
	ret
.L513:
	
__IfxCcu6_Timer_synchronousStop_function_end:
	.size	IfxCcu6_Timer_synchronousStop,__IfxCcu6_Timer_synchronousStop_function_end-IfxCcu6_Timer_synchronousStop
.L132:
	; End of function
	
	.sdecl	'.rodata.IfxCcu6_Timer..1.ini',data,rom
	.sect	'.rodata.IfxCcu6_Timer..1.ini'
	.align	4
.1.ini:	.type	object
	.size	.1.ini,100
	.word	1220759552,100,1220759552,100
	.word	20
	.space	8
	.byte	1
	.space	29
	.byte	1,1,7
	.space	5
	.byte	9,1
	.space	4
	.byte	6,2
	.space	4
	.byte	10,3
	.space	12
	.byte	1,1
	.space	6
	.calls	'IfxCcu6_Timer_initModule','IfxCcu6_enableModule'
	.calls	'IfxCcu6_Timer_initModule','IfxCcu6_setT13InputSignal'
	.calls	'IfxCcu6_Timer_initModule','IfxCcu6_setT13Frequency'
	.calls	'IfxCcu6_Timer_initModule','IfxCcu6_setT12InputSignal'
	.calls	'IfxCcu6_Timer_initModule','IfxCcu6_setT12Frequency'
	.calls	'IfxCcu6_Timer_initModule','IfxPort_setPinMode'
	.calls	'IfxCcu6_Timer_initModule','IfxCcu6_routeInterruptNode'
	.calls	'IfxCcu6_Timer_initModule','IfxCcu6_getSrcAddress'
	.calls	'IfxCcu6_Timer_start','IfxCcu6_setT13InputSignal'
	.calls	'IfxCcu6_Timer_start','IfxCcu6_setT12InputSignal'
	.calls	'IfxCcu6_Timer_startSingleShotMode','IfxCcu6_setT13InputSignal'
	.calls	'IfxCcu6_Timer_startSingleShotMode','IfxCcu6_setT12InputSignal'
	.calls	'IfxCcu6_Timer_countOneStep','',0
	.calls	'IfxCcu6_Timer_initModule','',0
	.calls	'IfxCcu6_Timer_initModuleConfig','',104
	.calls	'IfxCcu6_Timer_start','',0
	.calls	'IfxCcu6_Timer_startSingleShotMode','',0
	.calls	'IfxCcu6_Timer_stop','',0
	.calls	'IfxCcu6_Timer_synchronousStart','',0
	.extern	IfxPort_setPinMode
	.extern	IfxCcu6_routeInterruptNode
	.extern	IfxCcu6_setT12Frequency
	.extern	IfxCcu6_setT12InputSignal
	.extern	IfxCcu6_setT13Frequency
	.extern	IfxCcu6_setT13InputSignal
	.extern	IfxCcu6_getSrcAddress
	.extern	IfxCcu6_enableModule
	.calls	'IfxCcu6_Timer_synchronousStop','',0
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L90:
	.word	101822
	.half	3
	.word	.L91
	.byte	4
.L89:
	.byte	1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Ccu6/Timer/IfxCcu6_Timer.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L92
	.byte	2,1,1,3
	.word	240
	.byte	4
	.byte	'Ifx__jump_and_link',0,3,1,61,17,1,1,5
	.byte	'fun',0,1,61,43
	.word	243
	.byte	6,0,7
	.byte	'__fract',0,4,128,1,7
	.byte	'float',0,4,4,8
	.byte	'Ifx__float_to_fract',0,3,1,152,2,18
	.word	288
	.byte	1,1,5
	.byte	'a',0,1,152,2,44
	.word	300
	.byte	6,0,4
	.byte	'Ifx__stopPerfCounters',0,3,1,172,2,17,1,1,6,0,7
	.byte	'unsigned long long int',0,8,7,9
	.byte	'void',0,3
	.word	412
	.byte	8
	.byte	'__ld64',0,3,2,135,1,19
	.word	386
	.byte	1,1,5
	.byte	'addr',0,2,135,1,32
	.word	418
	.byte	6,0,4
	.byte	'__st64',0,3,2,143,1,17,1,1,5
	.byte	'addr',0,2,143,1,30
	.word	418
	.byte	5
	.byte	'value',0,2,143,1,43
	.word	386
	.byte	6,0,7
	.byte	'unsigned int',0,4,7,7
	.byte	'int',0,4,5,7
	.byte	'unsigned char',0,1,8,10
	.byte	'_Ifx_SRC_SRCR_Bits',0,4,45,16,4,11
	.byte	'SRPN',0,1
	.word	527
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	527
	.byte	2,6,2,35,1,11
	.byte	'SRE',0,1
	.word	527
	.byte	1,5,2,35,1,11
	.byte	'TOS',0,1
	.word	527
	.byte	2,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	527
	.byte	3,0,2,35,1,11
	.byte	'ECC',0,1
	.word	527
	.byte	6,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	527
	.byte	2,0,2,35,2,11
	.byte	'SRR',0,1
	.word	527
	.byte	1,7,2,35,3,11
	.byte	'CLRR',0,1
	.word	527
	.byte	1,6,2,35,3,11
	.byte	'SETR',0,1
	.word	527
	.byte	1,5,2,35,3,11
	.byte	'IOV',0,1
	.word	527
	.byte	1,4,2,35,3,11
	.byte	'IOVCLR',0,1
	.word	527
	.byte	1,3,2,35,3,11
	.byte	'SWS',0,1
	.word	527
	.byte	1,2,2,35,3,11
	.byte	'SWSCLR',0,1
	.word	527
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	527
	.byte	1,0,2,35,3,0,12,4,70,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	544
	.byte	4,2,35,0,0,14
	.word	834
.L282:
	.byte	3
	.word	873
.L294:
	.byte	4
	.byte	'IfxSrc_clearRequest',0,3,3,250,1,17,1,1
.L296:
	.byte	5
	.byte	'src',0,3,250,1,60
	.word	878
.L298:
	.byte	6,0
.L299:
	.byte	4
	.byte	'IfxSrc_enable',0,3,3,140,2,17,1,1
.L300:
	.byte	5
	.byte	'src',0,3,140,2,54
	.word	878
.L302:
	.byte	6,0,15,5,69,9,1,16
	.byte	'IfxSrc_Tos_cpu0',0,0,16
	.byte	'IfxSrc_Tos_cpu1',0,1,16
	.byte	'IfxSrc_Tos_dma',0,3,0,7
	.byte	'unsigned short int',0,2,7
.L284:
	.byte	4
	.byte	'IfxSrc_init',0,3,3,146,2,17,1,1
.L287:
	.byte	5
	.byte	'src',0,3,146,2,52
	.word	878
.L289:
	.byte	5
	.byte	'typOfService',0,3,146,2,68
	.word	963
.L291:
	.byte	5
	.byte	'priority',0,3,146,2,95
	.word	1022
.L293:
	.byte	17,6,0,0,7
	.byte	'unsigned int',0,4,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON0_Bits',0,7,241,8,16,4,11
	.byte	'ENDINIT',0,4
	.word	1121
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	1121
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	1121
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	1121
	.byte	16,0,2,35,0,0,12,7,247,14,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1137
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_WDTCPU_CON1_Bits',0,7,250,8,16,4,11
	.byte	'reserved_0',0,1
	.word	527
	.byte	2,6,2,35,0,11
	.byte	'IR0',0,1
	.word	527
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	527
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	527
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	527
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	527
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	527
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	527
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	527
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1022
	.byte	16,0,2,35,2,0,12,7,255,14,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1273
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_WDTCPU_SR_Bits',0,7,137,9,16,4,11
	.byte	'AE',0,1
	.word	527
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	527
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	527
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	527
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	527
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	527
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	527
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	527
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	527
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	527
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	1022
	.byte	16,0,2,35,2,0,12,7,135,15,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1517
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_WDTCPU',0,7,175,15,25,12,13
	.byte	'CON0',0
	.word	1233
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	1477
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	1708
	.byte	4,2,35,8,0,14
	.word	1748
	.byte	3
	.word	1811
	.byte	4
	.byte	'IfxScuWdt_clearCpuEndinitInline',0,3,6,181,3,17,1,1,5
	.byte	'watchdog',0,6,181,3,65
	.word	1816
	.byte	5
	.byte	'password',0,6,181,3,82
	.word	1022
	.byte	6,0,4
	.byte	'IfxScuWdt_setCpuEndinitInline',0,3,6,140,4,17,1,1,5
	.byte	'watchdog',0,6,140,4,63
	.word	1816
	.byte	5
	.byte	'password',0,6,140,4,80
	.word	1022
	.byte	6,0,8
	.byte	'IfxScuWdt_getCpuWatchdogPasswordInline',0,3,6,227,3,19
	.word	1022
	.byte	1,1,5
	.byte	'watchdog',0,6,227,3,74
	.word	1816
	.byte	6,0,15,9,156,1,9,1,16
	.byte	'IfxCpu_ResourceCpu_0',0,0,16
	.byte	'IfxCpu_ResourceCpu_1',0,1,16
	.byte	'IfxCpu_ResourceCpu_none',0,2,0,8
	.byte	'IfxCpu_getCoreIndex',0,3,8,141,6,31
	.word	2046
	.byte	1,1,6,0,8
	.byte	'IfxCpu_areInterruptsEnabled',0,3,8,139,5,20
	.word	527
	.byte	1,1,6,0
.L153:
	.byte	7
	.byte	'unsigned long int',0,4,7,8
	.byte	'IfxCpu_getPerformanceCounter',0,3,8,161,6,19
	.word	2201
	.byte	1,1,5
	.byte	'address',0,8,161,6,55
	.word	1022
	.byte	6,0,8
	.byte	'IfxCpu_getPerformanceCounterStickyOverflow',0,3,8,190,6,20
	.word	527
	.byte	1,1,5
	.byte	'address',0,8,190,6,70
	.word	1022
	.byte	6,0,4
	.byte	'IfxCpu_updatePerformanceCounter',0,3,8,172,8,17,1,1,5
	.byte	'address',0,8,172,8,56
	.word	2201
	.byte	5
	.byte	'count',0,8,172,8,72
	.word	2201
	.byte	17,6,0,0,10
	.byte	'_Ifx_P_OUT_Bits',0,11,143,3,16,4,11
	.byte	'P0',0,1
	.word	527
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	527
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	527
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	527
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	527
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	527
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	527
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	527
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	527
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	527
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	527
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	527
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	527
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	527
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	527
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	527
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1022
	.byte	16,0,2,35,2,0,12,11,181,5,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2432
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMR_Bits',0,11,169,2,16,4,11
	.byte	'PS0',0,1
	.word	527
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	527
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	527
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	527
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	527
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	527
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	527
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	527
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	527
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	527
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	527
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	527
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	527
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	527
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	527
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	527
	.byte	1,0,2,35,1,11
	.byte	'PCL0',0,1
	.word	527
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	527
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	527
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	527
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	527
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	527
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	527
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	527
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	527
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	527
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	527
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	527
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	527
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	527
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	527
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	527
	.byte	1,0,2,35,3,0,12,11,133,5,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2748
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ID_Bits',0,11,110,16,4,11
	.byte	'MODREV',0,1
	.word	527
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	527
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	1022
	.byte	16,0,2,35,2,0,12,11,148,4,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3319
	.byte	4,2,35,0,0,18,4
	.word	527
	.byte	19,3,0,10
	.byte	'_Ifx_P_IOCR0_Bits',0,11,140,1,16,4,11
	.byte	'reserved_0',0,1
	.word	527
	.byte	3,5,2,35,0,11
	.byte	'PC0',0,1
	.word	527
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	527
	.byte	3,5,2,35,1,11
	.byte	'PC1',0,1
	.word	527
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	527
	.byte	3,5,2,35,2,11
	.byte	'PC2',0,1
	.word	527
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	527
	.byte	3,5,2,35,3,11
	.byte	'PC3',0,1
	.word	527
	.byte	5,0,2,35,3,0,12,11,164,4,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3447
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR4_Bits',0,11,166,1,16,4,11
	.byte	'reserved_0',0,1
	.word	527
	.byte	3,5,2,35,0,11
	.byte	'PC4',0,1
	.word	527
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	527
	.byte	3,5,2,35,1,11
	.byte	'PC5',0,1
	.word	527
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	527
	.byte	3,5,2,35,2,11
	.byte	'PC6',0,1
	.word	527
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	527
	.byte	3,5,2,35,3,11
	.byte	'PC7',0,1
	.word	527
	.byte	5,0,2,35,3,0,12,11,180,4,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3662
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR8_Bits',0,11,179,1,16,4,11
	.byte	'reserved_0',0,1
	.word	527
	.byte	3,5,2,35,0,11
	.byte	'PC8',0,1
	.word	527
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	527
	.byte	3,5,2,35,1,11
	.byte	'PC9',0,1
	.word	527
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	527
	.byte	3,5,2,35,2,11
	.byte	'PC10',0,1
	.word	527
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	527
	.byte	3,5,2,35,3,11
	.byte	'PC11',0,1
	.word	527
	.byte	5,0,2,35,3,0,12,11,188,4,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3877
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR12_Bits',0,11,153,1,16,4,11
	.byte	'reserved_0',0,1
	.word	527
	.byte	3,5,2,35,0,11
	.byte	'PC12',0,1
	.word	527
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	527
	.byte	3,5,2,35,1,11
	.byte	'PC13',0,1
	.word	527
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	527
	.byte	3,5,2,35,2,11
	.byte	'PC14',0,1
	.word	527
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	527
	.byte	3,5,2,35,3,11
	.byte	'PC15',0,1
	.word	527
	.byte	5,0,2,35,3,0,12,11,172,4,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4094
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IN_Bits',0,11,118,16,4,11
	.byte	'P0',0,1
	.word	527
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	527
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	527
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	527
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	527
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	527
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	527
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	527
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	527
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	527
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	527
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	527
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	527
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	527
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	527
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	527
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1022
	.byte	16,0,2,35,2,0,12,11,156,4,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4314
	.byte	4,2,35,0,0,18,24
	.word	527
	.byte	19,23,0,10
	.byte	'_Ifx_P_PDR0_Bits',0,11,205,3,16,4,11
	.byte	'PD0',0,1
	.word	527
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	527
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	527
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	527
	.byte	1,0,2,35,0,11
	.byte	'PD2',0,1
	.word	527
	.byte	3,5,2,35,1,11
	.byte	'PL2',0,1
	.word	527
	.byte	1,4,2,35,1,11
	.byte	'PD3',0,1
	.word	527
	.byte	3,1,2,35,1,11
	.byte	'PL3',0,1
	.word	527
	.byte	1,0,2,35,1,11
	.byte	'PD4',0,1
	.word	527
	.byte	3,5,2,35,2,11
	.byte	'PL4',0,1
	.word	527
	.byte	1,4,2,35,2,11
	.byte	'PD5',0,1
	.word	527
	.byte	3,1,2,35,2,11
	.byte	'PL5',0,1
	.word	527
	.byte	1,0,2,35,2,11
	.byte	'PD6',0,1
	.word	527
	.byte	3,5,2,35,3,11
	.byte	'PL6',0,1
	.word	527
	.byte	1,4,2,35,3,11
	.byte	'PD7',0,1
	.word	527
	.byte	3,1,2,35,3,11
	.byte	'PL7',0,1
	.word	527
	.byte	1,0,2,35,3,0,12,11,205,5,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4637
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PDR1_Bits',0,11,226,3,16,4,11
	.byte	'PD8',0,1
	.word	527
	.byte	3,5,2,35,0,11
	.byte	'PL8',0,1
	.word	527
	.byte	1,4,2,35,0,11
	.byte	'PD9',0,1
	.word	527
	.byte	3,1,2,35,0,11
	.byte	'PL9',0,1
	.word	527
	.byte	1,0,2,35,0,11
	.byte	'PD10',0,1
	.word	527
	.byte	3,5,2,35,1,11
	.byte	'PL10',0,1
	.word	527
	.byte	1,4,2,35,1,11
	.byte	'PD11',0,1
	.word	527
	.byte	3,1,2,35,1,11
	.byte	'PL11',0,1
	.word	527
	.byte	1,0,2,35,1,11
	.byte	'PD12',0,1
	.word	527
	.byte	3,5,2,35,2,11
	.byte	'PL12',0,1
	.word	527
	.byte	1,4,2,35,2,11
	.byte	'PD13',0,1
	.word	527
	.byte	3,1,2,35,2,11
	.byte	'PL13',0,1
	.word	527
	.byte	1,0,2,35,2,11
	.byte	'PD14',0,1
	.word	527
	.byte	3,5,2,35,3,11
	.byte	'PL14',0,1
	.word	527
	.byte	1,4,2,35,3,11
	.byte	'PD15',0,1
	.word	527
	.byte	3,1,2,35,3,11
	.byte	'PL15',0,1
	.word	527
	.byte	1,0,2,35,3,0,12,11,213,5,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4941
	.byte	4,2,35,0,0,18,8
	.word	527
	.byte	19,7,0,10
	.byte	'_Ifx_P_ESR_Bits',0,11,88,16,4,11
	.byte	'EN0',0,1
	.word	527
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	527
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	527
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	527
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	527
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	527
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	527
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	527
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	527
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	527
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	527
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	527
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	527
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	527
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	527
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	527
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1022
	.byte	16,0,2,35,2,0,12,11,140,4,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5266
	.byte	4,2,35,0,0,18,12
	.word	527
	.byte	19,11,0,10
	.byte	'_Ifx_P_PDISC_Bits',0,11,183,3,16,4,11
	.byte	'PDIS0',0,1
	.word	527
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	527
	.byte	1,6,2,35,0,11
	.byte	'PDIS2',0,1
	.word	527
	.byte	1,5,2,35,0,11
	.byte	'PDIS3',0,1
	.word	527
	.byte	1,4,2,35,0,11
	.byte	'PDIS4',0,1
	.word	527
	.byte	1,3,2,35,0,11
	.byte	'PDIS5',0,1
	.word	527
	.byte	1,2,2,35,0,11
	.byte	'PDIS6',0,1
	.word	527
	.byte	1,1,2,35,0,11
	.byte	'PDIS7',0,1
	.word	527
	.byte	1,0,2,35,0,11
	.byte	'PDIS8',0,1
	.word	527
	.byte	1,7,2,35,1,11
	.byte	'PDIS9',0,1
	.word	527
	.byte	1,6,2,35,1,11
	.byte	'PDIS10',0,1
	.word	527
	.byte	1,5,2,35,1,11
	.byte	'PDIS11',0,1
	.word	527
	.byte	1,4,2,35,1,11
	.byte	'PDIS12',0,1
	.word	527
	.byte	1,3,2,35,1,11
	.byte	'PDIS13',0,1
	.word	527
	.byte	1,2,2,35,1,11
	.byte	'PDIS14',0,1
	.word	527
	.byte	1,1,2,35,1,11
	.byte	'PDIS15',0,1
	.word	527
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1022
	.byte	16,0,2,35,2,0,12,11,197,5,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5606
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PCSR_Bits',0,11,165,3,16,4,11
	.byte	'SEL0',0,1
	.word	527
	.byte	1,7,2,35,0,11
	.byte	'SEL1',0,1
	.word	527
	.byte	1,6,2,35,0,11
	.byte	'SEL2',0,1
	.word	527
	.byte	1,5,2,35,0,11
	.byte	'SEL3',0,1
	.word	527
	.byte	1,4,2,35,0,11
	.byte	'SEL4',0,1
	.word	527
	.byte	1,3,2,35,0,11
	.byte	'SEL5',0,1
	.word	527
	.byte	1,2,2,35,0,11
	.byte	'SEL6',0,1
	.word	527
	.byte	1,1,2,35,0,11
	.byte	'SEL7',0,1
	.word	527
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	527
	.byte	2,6,2,35,1,11
	.byte	'SEL10',0,1
	.word	527
	.byte	1,5,2,35,1,11
	.byte	'SEL11',0,1
	.word	527
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	504
	.byte	19,1,2,35,0,11
	.byte	'LCK',0,1
	.word	527
	.byte	1,0,2,35,3,0,12,11,189,5,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5972
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR0_Bits',0,11,206,2,16,4,11
	.byte	'PS0',0,1
	.word	527
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	527
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	527
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	527
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	504
	.byte	28,0,2,35,0,0,12,11,149,5,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6258
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR4_Bits',0,11,227,2,16,4,11
	.byte	'reserved_0',0,1
	.word	527
	.byte	4,4,2,35,0,11
	.byte	'PS4',0,1
	.word	527
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	527
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	527
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	527
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	504
	.byte	24,0,2,35,0,0,12,11,165,5,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6405
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR8_Bits',0,11,238,2,16,4,11
	.byte	'reserved_0',0,1
	.word	527
	.byte	8,0,2,35,0,11
	.byte	'PS8',0,1
	.word	527
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	527
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	527
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	527
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	504
	.byte	20,0,2,35,0,0,12,11,173,5,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6574
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR12_Bits',0,11,216,2,16,4,11
	.byte	'reserved_0',0,2
	.word	1022
	.byte	12,4,2,35,0,11
	.byte	'PS12',0,1
	.word	527
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	527
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	527
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	527
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1022
	.byte	16,0,2,35,2,0,12,11,157,5,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6746
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR0_Bits',0,11,232,1,16,4,11
	.byte	'reserved_0',0,2
	.word	1022
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	527
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	527
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	527
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	527
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	1022
	.byte	12,0,2,35,2,0,12,11,229,4,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6921
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR4_Bits',0,11,253,1,16,4,11
	.byte	'reserved_0',0,4
	.word	504
	.byte	20,12,2,35,0,11
	.byte	'PCL4',0,1
	.word	527
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	527
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	527
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	527
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	527
	.byte	8,0,2,35,3,0,12,11,245,4,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7095
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR8_Bits',0,11,136,2,16,4,11
	.byte	'reserved_0',0,4
	.word	504
	.byte	24,8,2,35,0,11
	.byte	'PCL8',0,1
	.word	527
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	527
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	527
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	527
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	527
	.byte	4,0,2,35,3,0,12,11,253,4,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7269
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR12_Bits',0,11,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	504
	.byte	28,4,2,35,0,11
	.byte	'PCL12',0,1
	.word	527
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	527
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	527
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	527
	.byte	1,0,2,35,3,0,12,11,237,4,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7445
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR_Bits',0,11,249,2,16,4,11
	.byte	'PS0',0,1
	.word	527
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	527
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	527
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	527
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	527
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	527
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	527
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	527
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	527
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	527
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	527
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	527
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	527
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	527
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	527
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	527
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1022
	.byte	16,0,2,35,2,0,12,11,141,5,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7601
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR_Bits',0,11,147,2,16,4,11
	.byte	'reserved_0',0,2
	.word	1022
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	527
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	527
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	527
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	527
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	527
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	527
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	527
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	527
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	527
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	527
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	527
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	527
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	527
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	527
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	527
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	527
	.byte	1,0,2,35,3,0,12,11,221,4,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7934
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR0_Bits',0,11,192,1,16,4,11
	.byte	'reserved_0',0,1
	.word	527
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	527
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	504
	.byte	30,0,2,35,0,0,12,11,196,4,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8282
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_Bits',0,11,200,1,16,4,11
	.byte	'reserved_0',0,1
	.word	527
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	527
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	504
	.byte	30,0,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_P21_Bits',0,11,208,1,16,4,11
	.byte	'RDIS_CTRL',0,1
	.word	527
	.byte	1,7,2,35,0,11
	.byte	'RX_DIS',0,1
	.word	527
	.byte	1,6,2,35,0,11
	.byte	'TERM',0,1
	.word	527
	.byte	1,5,2,35,0,11
	.byte	'LRXTERM',0,1
	.word	527
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	504
	.byte	24,0,2,35,0,0,12,11,204,4,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8406
	.byte	4,2,35,0,13
	.byte	'B_P21',0
	.word	8490
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR2_Bits',0,11,218,1,16,4,11
	.byte	'reserved_0',0,1
	.word	527
	.byte	8,0,2,35,0,11
	.byte	'LVDSR',0,1
	.word	527
	.byte	1,7,2,35,1,11
	.byte	'LVDSRL',0,1
	.word	527
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	527
	.byte	2,4,2,35,1,11
	.byte	'TDIS_CTRL',0,1
	.word	527
	.byte	1,3,2,35,1,11
	.byte	'TX_DIS',0,1
	.word	527
	.byte	1,2,2,35,1,11
	.byte	'TX_PD',0,1
	.word	527
	.byte	1,1,2,35,1,11
	.byte	'TX_PWDPD',0,1
	.word	527
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1022
	.byte	16,0,2,35,2,0,12,11,213,4,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8670
	.byte	4,2,35,0,0,18,76
	.word	527
	.byte	19,75,0,10
	.byte	'_Ifx_P_ACCEN1_Bits',0,11,82,16,4,11
	.byte	'reserved_0',0,4
	.word	504
	.byte	32,0,2,35,0,0,12,11,132,4,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8923
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ACCEN0_Bits',0,11,45,16,4,11
	.byte	'EN0',0,1
	.word	527
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	527
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	527
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	527
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	527
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	527
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	527
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	527
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	527
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	527
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	527
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	527
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	527
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	527
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	527
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	527
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	527
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	527
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	527
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	527
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	527
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	527
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	527
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	527
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	527
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	527
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	527
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	527
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	527
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	527
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	527
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	527
	.byte	1,0,2,35,3,0,12,11,252,3,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9010
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P',0,11,229,5,25,128,2,13
	.byte	'OUT',0
	.word	2708
	.byte	4,2,35,0,13
	.byte	'OMR',0
	.word	3279
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	3398
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	3438
	.byte	4,2,35,12,13
	.byte	'IOCR0',0
	.word	3622
	.byte	4,2,35,16,13
	.byte	'IOCR4',0
	.word	3837
	.byte	4,2,35,20,13
	.byte	'IOCR8',0
	.word	4054
	.byte	4,2,35,24,13
	.byte	'IOCR12',0
	.word	4274
	.byte	4,2,35,28,13
	.byte	'reserved_20',0
	.word	3438
	.byte	4,2,35,32,13
	.byte	'IN',0
	.word	4588
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	4628
	.byte	24,2,35,40,13
	.byte	'PDR0',0
	.word	4901
	.byte	4,2,35,64,13
	.byte	'PDR1',0
	.word	5217
	.byte	4,2,35,68,13
	.byte	'reserved_48',0
	.word	5257
	.byte	8,2,35,72,13
	.byte	'ESR',0
	.word	5557
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	5597
	.byte	12,2,35,84,13
	.byte	'PDISC',0
	.word	5932
	.byte	4,2,35,96,13
	.byte	'PCSR',0
	.word	6218
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	5257
	.byte	8,2,35,104,13
	.byte	'OMSR0',0
	.word	6365
	.byte	4,2,35,112,13
	.byte	'OMSR4',0
	.word	6534
	.byte	4,2,35,116,13
	.byte	'OMSR8',0
	.word	6706
	.byte	4,2,35,120,13
	.byte	'OMSR12',0
	.word	6881
	.byte	4,2,35,124,13
	.byte	'OMCR0',0
	.word	7055
	.byte	4,3,35,128,1,13
	.byte	'OMCR4',0
	.word	7229
	.byte	4,3,35,132,1,13
	.byte	'OMCR8',0
	.word	7405
	.byte	4,3,35,136,1,13
	.byte	'OMCR12',0
	.word	7561
	.byte	4,3,35,140,1,13
	.byte	'OMSR',0
	.word	7894
	.byte	4,3,35,144,1,13
	.byte	'OMCR',0
	.word	8242
	.byte	4,3,35,148,1,13
	.byte	'reserved_98',0
	.word	5257
	.byte	8,3,35,152,1,13
	.byte	'LPCR0',0
	.word	8366
	.byte	4,3,35,160,1,13
	.byte	'LPCR1',0
	.word	8615
	.byte	4,3,35,164,1,13
	.byte	'LPCR2',0
	.word	8874
	.byte	4,3,35,168,1,13
	.byte	'reserved_A4',0
	.word	8914
	.byte	76,3,35,172,1,13
	.byte	'ACCEN1',0
	.word	8970
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	9537
	.byte	4,3,35,252,1,0,14
	.word	9577
	.byte	3
	.word	10180
	.byte	15,10,83,9,1,16
	.byte	'IfxPort_InputMode_undefined',0,127,16
	.byte	'IfxPort_InputMode_noPullDevice',0,0,16
	.byte	'IfxPort_InputMode_pullDown',0,8,16
	.byte	'IfxPort_InputMode_pullUp',0,16,0
.L251:
	.byte	4
	.byte	'IfxPort_setPinModeInput',0,3,10,196,4,17,1,1
.L254:
	.byte	5
	.byte	'port',0,10,196,4,48
	.word	10185
.L256:
	.byte	5
	.byte	'pinIndex',0,10,196,4,60
	.word	527
.L258:
	.byte	5
	.byte	'mode',0,10,196,4,88
	.word	10190
.L260:
	.byte	6,0,15,10,134,1,9,1,16
	.byte	'IfxPort_OutputMode_pushPull',0,128,1,16
	.byte	'IfxPort_OutputMode_openDrain',0,192,1,0,15,10,120,9,1,16
	.byte	'IfxPort_OutputIdx_general',0,128,1,16
	.byte	'IfxPort_OutputIdx_alt1',0,136,1,16
	.byte	'IfxPort_OutputIdx_alt2',0,144,1,16
	.byte	'IfxPort_OutputIdx_alt3',0,152,1,16
	.byte	'IfxPort_OutputIdx_alt4',0,160,1,16
	.byte	'IfxPort_OutputIdx_alt5',0,168,1,16
	.byte	'IfxPort_OutputIdx_alt6',0,176,1,16
	.byte	'IfxPort_OutputIdx_alt7',0,184,1,0,4
	.byte	'IfxPort_setPinModeOutput',0,3,10,202,4,17,1,1,5
	.byte	'port',0,10,202,4,49
	.word	10185
	.byte	5
	.byte	'pinIndex',0,10,202,4,61
	.word	527
	.byte	5
	.byte	'mode',0,10,202,4,90
	.word	10395
	.byte	5
	.byte	'index',0,10,202,4,114
	.word	10465
	.byte	6,0,15,10,172,1,9,4,16
	.byte	'IfxPort_State_notChanged',0,0,16
	.byte	'IfxPort_State_high',0,1,16
	.byte	'IfxPort_State_low',0,128,128,4,16
	.byte	'IfxPort_State_toggled',0,129,128,4,0,4
	.byte	'IfxPort_setPinState',0,3,10,208,4,17,1,1,5
	.byte	'port',0,10,208,4,44
	.word	10185
	.byte	5
	.byte	'pinIndex',0,10,208,4,56
	.word	527
	.byte	5
	.byte	'action',0,10,208,4,80
	.word	10778
	.byte	6,0,10
	.byte	'_Ifx_CCU6_CLC_Bits',0,13,144,1,16,4,11
	.byte	'DISR',0,1
	.word	527
	.byte	1,7,2,35,0,11
	.byte	'DISS',0,1
	.word	527
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	527
	.byte	1,5,2,35,0,11
	.byte	'EDIS',0,1
	.word	527
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	504
	.byte	28,0,2,35,0,0,12,13,172,5,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10959
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_MCFG_Bits',0,13,241,2,16,4,11
	.byte	'T12',0,1
	.word	527
	.byte	1,7,2,35,0,11
	.byte	'T13',0,1
	.word	527
	.byte	1,6,2,35,0,11
	.byte	'MCM',0,1
	.word	527
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	504
	.byte	29,0,2,35,0,0,12,13,164,6,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11117
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_ID_Bits',0,13,193,1,16,4,11
	.byte	'MODREV',0,1
	.word	527
	.byte	8,0,2,35,0,11
	.byte	'MODNUMBER',0,1
	.word	527
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1022
	.byte	16,0,2,35,2,0,12,13,196,5,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11251
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_MOSEL_Bits',0,13,171,3,16,4,11
	.byte	'TRIG0SEL',0,1
	.word	527
	.byte	3,5,2,35,0,11
	.byte	'TRIG1SEL',0,1
	.word	527
	.byte	3,2,2,35,0,11
	.byte	'TRIG2SEL',0,2
	.word	1022
	.byte	3,7,2,35,0,11
	.byte	'reserved_9',0,4
	.word	504
	.byte	23,0,2,35,0,0,12,13,204,6,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11378
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_PISEL0_Bits',0,13,193,3,16,4,11
	.byte	'ISCC60',0,1
	.word	527
	.byte	2,6,2,35,0,11
	.byte	'ISCC61',0,1
	.word	527
	.byte	2,4,2,35,0,11
	.byte	'ISCC62',0,1
	.word	527
	.byte	2,2,2,35,0,11
	.byte	'ISTRP',0,1
	.word	527
	.byte	2,0,2,35,0,11
	.byte	'ISPOS0',0,1
	.word	527
	.byte	2,6,2,35,1,11
	.byte	'ISPOS1',0,1
	.word	527
	.byte	2,4,2,35,1,11
	.byte	'ISPOS2',0,1
	.word	527
	.byte	2,2,2,35,1,11
	.byte	'IST12HR',0,1
	.word	527
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1022
	.byte	16,0,2,35,2,0,12,13,220,6,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11528
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_PISEL2_Bits',0,13,207,3,16,4,11
	.byte	'IST13HR',0,1
	.word	527
	.byte	2,6,2,35,0,11
	.byte	'ISCNT12',0,1
	.word	527
	.byte	2,4,2,35,0,11
	.byte	'ISCNT13',0,1
	.word	527
	.byte	2,2,2,35,0,11
	.byte	'T12EXT',0,1
	.word	527
	.byte	1,1,2,35,0,11
	.byte	'T13EXT',0,1
	.word	527
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	504
	.byte	24,0,2,35,0,0,12,13,228,6,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11764
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_KSCSR_Bits',0,13,212,2,16,4,11
	.byte	'SB0',0,1
	.word	527
	.byte	1,7,2,35,0,11
	.byte	'SB1',0,1
	.word	527
	.byte	1,6,2,35,0,11
	.byte	'SB2',0,1
	.word	527
	.byte	1,5,2,35,0,11
	.byte	'SB3',0,1
	.word	527
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	504
	.byte	28,0,2,35,0,0,12,13,148,6,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11948
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_T12_Bits',0,13,227,3,16,4,11
	.byte	'T12CV',0,2
	.word	1022
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	1022
	.byte	16,0,2,35,2,0,12,13,244,6,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12098
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_T12PR_Bits',0,13,131,4,16,4,11
	.byte	'T12PV',0,2
	.word	1022
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	1022
	.byte	16,0,2,35,2,0,12,13,140,7,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12204
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_T12DTC_Bits',0,13,234,3,16,4,11
	.byte	'DTM',0,1
	.word	527
	.byte	8,0,2,35,0,11
	.byte	'DTE0',0,1
	.word	527
	.byte	1,7,2,35,1,11
	.byte	'DTE1',0,1
	.word	527
	.byte	1,6,2,35,1,11
	.byte	'DTE2',0,1
	.word	527
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	527
	.byte	1,4,2,35,1,11
	.byte	'DTR0',0,1
	.word	527
	.byte	1,3,2,35,1,11
	.byte	'DTR1',0,1
	.word	527
	.byte	1,2,2,35,1,11
	.byte	'DTR2',0,1
	.word	527
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	504
	.byte	17,0,2,35,0,0,12,13,252,6,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12312
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_CC60R_Bits',0,13,88,16,4,11
	.byte	'CCV',0,2
	.word	1022
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	1022
	.byte	16,0,2,35,2,0,12,13,236,4,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12538
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_CC61R_Bits',0,13,102,16,4,11
	.byte	'CCV',0,2
	.word	1022
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	1022
	.byte	16,0,2,35,2,0,12,13,252,4,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12643
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_CC62R_Bits',0,13,116,16,4,11
	.byte	'CCV',0,2
	.word	1022
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	1022
	.byte	16,0,2,35,2,0,12,13,140,5,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12748
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_CC60SR_Bits',0,13,95,16,4,11
	.byte	'CCS',0,2
	.word	1022
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	1022
	.byte	16,0,2,35,2,0,12,13,244,4,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12853
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_CC61SR_Bits',0,13,109,16,4,11
	.byte	'CCS',0,2
	.word	1022
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	1022
	.byte	16,0,2,35,2,0,12,13,132,5,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12959
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_CC62SR_Bits',0,13,123,16,4,11
	.byte	'CCS',0,2
	.word	1022
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	1022
	.byte	16,0,2,35,2,0,12,13,148,5,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13065
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_T13_Bits',0,13,138,4,16,4,11
	.byte	'T13CV',0,2
	.word	1022
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	1022
	.byte	16,0,2,35,2,0,12,13,148,7,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13171
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_T13PR_Bits',0,13,145,4,16,4,11
	.byte	'T13PV',0,2
	.word	1022
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	1022
	.byte	16,0,2,35,2,0,12,13,156,7,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13277
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_CC63R_Bits',0,13,130,1,16,4,11
	.byte	'CCV',0,2
	.word	1022
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	1022
	.byte	16,0,2,35,2,0,12,13,156,5,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13385
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_CC63SR_Bits',0,13,137,1,16,4,11
	.byte	'CCS',0,2
	.word	1022
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	1022
	.byte	16,0,2,35,2,0,12,13,164,5,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13491
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_CMPSTAT_Bits',0,13,171,1,16,4,11
	.byte	'CC60ST',0,1
	.word	527
	.byte	1,7,2,35,0,11
	.byte	'CC61ST',0,1
	.word	527
	.byte	1,6,2,35,0,11
	.byte	'CC62ST',0,1
	.word	527
	.byte	1,5,2,35,0,11
	.byte	'CCPOS60',0,1
	.word	527
	.byte	1,4,2,35,0,11
	.byte	'CCPOS61',0,1
	.word	527
	.byte	1,3,2,35,0,11
	.byte	'CCPOS62',0,1
	.word	527
	.byte	1,2,2,35,0,11
	.byte	'CC63ST',0,1
	.word	527
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	527
	.byte	1,0,2,35,0,11
	.byte	'CC60PS',0,1
	.word	527
	.byte	1,7,2,35,1,11
	.byte	'COUT60PS',0,1
	.word	527
	.byte	1,6,2,35,1,11
	.byte	'CC61PS',0,1
	.word	527
	.byte	1,5,2,35,1,11
	.byte	'COUT61PS',0,1
	.word	527
	.byte	1,4,2,35,1,11
	.byte	'CC62PS',0,1
	.word	527
	.byte	1,3,2,35,1,11
	.byte	'COUT62PS',0,1
	.word	527
	.byte	1,2,2,35,1,11
	.byte	'COUT63PS',0,1
	.word	527
	.byte	1,1,2,35,1,11
	.byte	'T13IM',0,1
	.word	527
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1022
	.byte	16,0,2,35,2,0,12,13,188,5,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13598
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_CMPMODIF_Bits',0,13,154,1,16,4,11
	.byte	'MCC60S',0,1
	.word	527
	.byte	1,7,2,35,0,11
	.byte	'MCC61S',0,1
	.word	527
	.byte	1,6,2,35,0,11
	.byte	'MCC62S',0,1
	.word	527
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	527
	.byte	3,2,2,35,0,11
	.byte	'MCC63S',0,1
	.word	527
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	527
	.byte	1,0,2,35,0,11
	.byte	'MCC60R',0,1
	.word	527
	.byte	1,7,2,35,1,11
	.byte	'MCC61R',0,1
	.word	527
	.byte	1,6,2,35,1,11
	.byte	'MCC62R',0,1
	.word	527
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	527
	.byte	3,2,2,35,1,11
	.byte	'MCC63R',0,1
	.word	527
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	504
	.byte	17,0,2,35,0,0,12,13,180,5,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13993
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_T12MSEL_Bits',0,13,248,3,16,4,11
	.byte	'MSEL60',0,1
	.word	527
	.byte	4,4,2,35,0,11
	.byte	'MSEL61',0,1
	.word	527
	.byte	4,0,2,35,0,11
	.byte	'MSEL62',0,1
	.word	527
	.byte	4,4,2,35,1,11
	.byte	'HSYNC',0,1
	.word	527
	.byte	3,1,2,35,1,11
	.byte	'DBYP',0,1
	.word	527
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1022
	.byte	16,0,2,35,2,0,12,13,132,7,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14298
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_TCTR0_Bits',0,13,152,4,16,4,11
	.byte	'T12CLK',0,1
	.word	527
	.byte	3,5,2,35,0,11
	.byte	'T12PRE',0,1
	.word	527
	.byte	1,4,2,35,0,11
	.byte	'T12R',0,1
	.word	527
	.byte	1,3,2,35,0,11
	.byte	'STE12',0,1
	.word	527
	.byte	1,2,2,35,0,11
	.byte	'CDIR',0,1
	.word	527
	.byte	1,1,2,35,0,11
	.byte	'CTM',0,1
	.word	527
	.byte	1,0,2,35,0,11
	.byte	'T13CLK',0,1
	.word	527
	.byte	3,5,2,35,1,11
	.byte	'T13PRE',0,1
	.word	527
	.byte	1,4,2,35,1,11
	.byte	'T13R',0,1
	.word	527
	.byte	1,3,2,35,1,11
	.byte	'STE13',0,1
	.word	527
	.byte	1,2,2,35,1,11
	.byte	'reserved_14',0,4
	.word	504
	.byte	18,0,2,35,0,0,12,13,164,7,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14478
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_TCTR2_Bits',0,13,168,4,16,4,11
	.byte	'T12SSC',0,1
	.word	527
	.byte	1,7,2,35,0,11
	.byte	'T13SSC',0,1
	.word	527
	.byte	1,6,2,35,0,11
	.byte	'T13TEC',0,1
	.word	527
	.byte	3,3,2,35,0,11
	.byte	'T13TED',0,1
	.word	527
	.byte	2,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	527
	.byte	1,0,2,35,0,11
	.byte	'T12RSEL',0,1
	.word	527
	.byte	2,6,2,35,1,11
	.byte	'T13RSEL',0,1
	.word	527
	.byte	2,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	504
	.byte	20,0,2,35,0,0,12,13,172,7,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14738
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_TCTR4_Bits',0,13,181,4,16,4,11
	.byte	'T12RR',0,1
	.word	527
	.byte	1,7,2,35,0,11
	.byte	'T12RS',0,1
	.word	527
	.byte	1,6,2,35,0,11
	.byte	'T12RES',0,1
	.word	527
	.byte	1,5,2,35,0,11
	.byte	'DTRES',0,1
	.word	527
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	527
	.byte	1,3,2,35,0,11
	.byte	'T12CNT',0,1
	.word	527
	.byte	1,2,2,35,0,11
	.byte	'T12STR',0,1
	.word	527
	.byte	1,1,2,35,0,11
	.byte	'T12STD',0,1
	.word	527
	.byte	1,0,2,35,0,11
	.byte	'T13RR',0,1
	.word	527
	.byte	1,7,2,35,1,11
	.byte	'T13RS',0,1
	.word	527
	.byte	1,6,2,35,1,11
	.byte	'T13RES',0,1
	.word	527
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	527
	.byte	2,3,2,35,1,11
	.byte	'T13CNT',0,1
	.word	527
	.byte	1,2,2,35,1,11
	.byte	'T13STR',0,1
	.word	527
	.byte	1,1,2,35,1,11
	.byte	'T13STD',0,1
	.word	527
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1022
	.byte	16,0,2,35,2,0
.L345:
	.byte	12,13,180,7,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14961
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_MODCTR_Bits',0,13,159,3,16,4,11
	.byte	'T12MODEN',0,1
	.word	527
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	527
	.byte	1,1,2,35,0,11
	.byte	'MCMEN',0,1
	.word	527
	.byte	1,0,2,35,0,11
	.byte	'T13MODEN',0,1
	.word	527
	.byte	6,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	527
	.byte	1,1,2,35,1,11
	.byte	'ECT13O',0,1
	.word	527
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1022
	.byte	16,0,2,35,2,0,12,13,196,6,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15326
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_TRPCTR_Bits',0,13,202,4,16,4,11
	.byte	'TRPM0',0,1
	.word	527
	.byte	1,7,2,35,0,11
	.byte	'TRPM1',0,1
	.word	527
	.byte	1,6,2,35,0,11
	.byte	'TRPM2',0,1
	.word	527
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	527
	.byte	5,0,2,35,0,11
	.byte	'TRPEN',0,1
	.word	527
	.byte	6,2,2,35,1,11
	.byte	'TRPEN13',0,1
	.word	527
	.byte	1,1,2,35,1,11
	.byte	'TRPPEN',0,1
	.word	527
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1022
	.byte	16,0,2,35,2,0,12,13,188,7,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15538
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_PSLR_Bits',0,13,218,3,16,4,11
	.byte	'PSL',0,1
	.word	527
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	527
	.byte	1,1,2,35,0,11
	.byte	'PSL63',0,1
	.word	527
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	504
	.byte	24,0,2,35,0,0,12,13,236,6,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15757
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_MCMOUTS_Bits',0,13,146,3,16,4,11
	.byte	'MCMPS',0,1
	.word	527
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	527
	.byte	1,1,2,35,0,11
	.byte	'STRMCM',0,1
	.word	527
	.byte	1,0,2,35,0,11
	.byte	'EXPHS',0,1
	.word	527
	.byte	3,5,2,35,1,11
	.byte	'CURHS',0,1
	.word	527
	.byte	3,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	527
	.byte	1,1,2,35,1,11
	.byte	'STRHP',0,1
	.word	527
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1022
	.byte	16,0,2,35,2,0,12,13,188,6,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15900
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_MCMOUT_Bits',0,13,135,3,16,4,11
	.byte	'MCMP',0,1
	.word	527
	.byte	6,2,2,35,0,11
	.byte	'R',0,1
	.word	527
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	527
	.byte	1,0,2,35,0,11
	.byte	'EXPH',0,1
	.word	527
	.byte	3,5,2,35,1,11
	.byte	'CURH',0,1
	.word	527
	.byte	3,2,2,35,1,11
	.byte	'reserved_14',0,4
	.word	504
	.byte	18,0,2,35,0,0,12,13,180,6,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16124
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_MCMCTR_Bits',0,13,250,2,16,4,11
	.byte	'SWSEL',0,1
	.word	527
	.byte	3,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	527
	.byte	1,4,2,35,0,11
	.byte	'SWSYN',0,1
	.word	527
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	527
	.byte	2,0,2,35,0,11
	.byte	'STE12U',0,1
	.word	527
	.byte	1,7,2,35,1,11
	.byte	'STE12D',0,1
	.word	527
	.byte	1,6,2,35,1,11
	.byte	'STE13U',0,1
	.word	527
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	504
	.byte	21,0,2,35,0,0,12,13,172,6,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16299
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_IMON_Bits',0,13,223,1,16,4,11
	.byte	'LBE',0,1
	.word	527
	.byte	1,7,2,35,0,11
	.byte	'CCPOS0I',0,1
	.word	527
	.byte	1,6,2,35,0,11
	.byte	'CCPOS1I',0,1
	.word	527
	.byte	1,5,2,35,0,11
	.byte	'CCPOS2I',0,1
	.word	527
	.byte	1,4,2,35,0,11
	.byte	'CC60INI',0,1
	.word	527
	.byte	1,3,2,35,0,11
	.byte	'CC61INI',0,1
	.word	527
	.byte	1,2,2,35,0,11
	.byte	'CC62INI',0,1
	.word	527
	.byte	1,1,2,35,0,11
	.byte	'CTRAPI',0,1
	.word	527
	.byte	1,0,2,35,0,11
	.byte	'T12HRI',0,1
	.word	527
	.byte	1,7,2,35,1,11
	.byte	'T13HRI',0,1
	.word	527
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	504
	.byte	22,0,2,35,0,0,12,13,212,5,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16523
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_LI_Bits',0,13,222,2,16,4,11
	.byte	'reserved_0',0,1
	.word	527
	.byte	1,7,2,35,0,11
	.byte	'CCPOS0EN',0,1
	.word	527
	.byte	1,6,2,35,0,11
	.byte	'CCPOS1EN',0,1
	.word	527
	.byte	1,5,2,35,0,11
	.byte	'CCPOS2EN',0,1
	.word	527
	.byte	1,4,2,35,0,11
	.byte	'CC60INEN',0,1
	.word	527
	.byte	1,3,2,35,0,11
	.byte	'CC61INEN',0,1
	.word	527
	.byte	1,2,2,35,0,11
	.byte	'CC62INEN',0,1
	.word	527
	.byte	1,1,2,35,0,11
	.byte	'CTRAPEN',0,1
	.word	527
	.byte	1,0,2,35,0,11
	.byte	'T12HREN',0,1
	.word	527
	.byte	1,7,2,35,1,11
	.byte	'T13HREN',0,1
	.word	527
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	527
	.byte	3,3,2,35,1,11
	.byte	'LBEEN',0,1
	.word	527
	.byte	1,2,2,35,1,11
	.byte	'INPLBE',0,1
	.word	527
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1022
	.byte	16,0,2,35,2,0,12,13,156,6,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16796
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_IS_Bits',0,13,252,1,16,4,11
	.byte	'ICC60R',0,1
	.word	527
	.byte	1,7,2,35,0,11
	.byte	'ICC60F',0,1
	.word	527
	.byte	1,6,2,35,0,11
	.byte	'ICC61R',0,1
	.word	527
	.byte	1,5,2,35,0,11
	.byte	'ICC61F',0,1
	.word	527
	.byte	1,4,2,35,0,11
	.byte	'ICC62R',0,1
	.word	527
	.byte	1,3,2,35,0,11
	.byte	'ICC62F',0,1
	.word	527
	.byte	1,2,2,35,0,11
	.byte	'T12OM',0,1
	.word	527
	.byte	1,1,2,35,0,11
	.byte	'T12PM',0,1
	.word	527
	.byte	1,0,2,35,0,11
	.byte	'T13CM',0,1
	.word	527
	.byte	1,7,2,35,1,11
	.byte	'T13PM',0,1
	.word	527
	.byte	1,6,2,35,1,11
	.byte	'TRPF',0,1
	.word	527
	.byte	1,5,2,35,1,11
	.byte	'TRPS',0,1
	.word	527
	.byte	1,4,2,35,1,11
	.byte	'CHE',0,1
	.word	527
	.byte	1,3,2,35,1,11
	.byte	'WHE',0,1
	.word	527
	.byte	1,2,2,35,1,11
	.byte	'IDLE',0,1
	.word	527
	.byte	1,1,2,35,1,11
	.byte	'STR',0,1
	.word	527
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1022
	.byte	16,0,2,35,2,0,12,13,228,5,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17141
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_ISS_Bits',0,13,168,2,16,4,11
	.byte	'SCC60R',0,1
	.word	527
	.byte	1,7,2,35,0,11
	.byte	'SCC60F',0,1
	.word	527
	.byte	1,6,2,35,0,11
	.byte	'SCC61R',0,1
	.word	527
	.byte	1,5,2,35,0,11
	.byte	'SCC61F',0,1
	.word	527
	.byte	1,4,2,35,0,11
	.byte	'SCC62R',0,1
	.word	527
	.byte	1,3,2,35,0,11
	.byte	'SCC62F',0,1
	.word	527
	.byte	1,2,2,35,0,11
	.byte	'ST12OM',0,1
	.word	527
	.byte	1,1,2,35,0,11
	.byte	'ST12PM',0,1
	.word	527
	.byte	1,0,2,35,0,11
	.byte	'ST13CM',0,1
	.word	527
	.byte	1,7,2,35,1,11
	.byte	'ST13PM',0,1
	.word	527
	.byte	1,6,2,35,1,11
	.byte	'STRPF',0,1
	.word	527
	.byte	1,5,2,35,1,11
	.byte	'SWHC',0,1
	.word	527
	.byte	1,4,2,35,1,11
	.byte	'SCHE',0,1
	.word	527
	.byte	1,3,2,35,1,11
	.byte	'SWHE',0,1
	.word	527
	.byte	1,2,2,35,1,11
	.byte	'SIDLE',0,1
	.word	527
	.byte	1,1,2,35,1,11
	.byte	'SSTR',0,1
	.word	527
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1022
	.byte	16,0,2,35,2,0,12,13,244,5,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17498
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_ISR_Bits',0,13,146,2,16,4,11
	.byte	'RCC60R',0,1
	.word	527
	.byte	1,7,2,35,0,11
	.byte	'RCC60F',0,1
	.word	527
	.byte	1,6,2,35,0,11
	.byte	'RCC61R',0,1
	.word	527
	.byte	1,5,2,35,0,11
	.byte	'RCC61F',0,1
	.word	527
	.byte	1,4,2,35,0,11
	.byte	'RCC62R',0,1
	.word	527
	.byte	1,3,2,35,0,11
	.byte	'RCC62F',0,1
	.word	527
	.byte	1,2,2,35,0,11
	.byte	'RT12OM',0,1
	.word	527
	.byte	1,1,2,35,0,11
	.byte	'RT12PM',0,1
	.word	527
	.byte	1,0,2,35,0,11
	.byte	'RT13CM',0,1
	.word	527
	.byte	1,7,2,35,1,11
	.byte	'RT13PM',0,1
	.word	527
	.byte	1,6,2,35,1,11
	.byte	'RTRPF',0,1
	.word	527
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	527
	.byte	1,4,2,35,1,11
	.byte	'RCHE',0,1
	.word	527
	.byte	1,3,2,35,1,11
	.byte	'RWHE',0,1
	.word	527
	.byte	1,2,2,35,1,11
	.byte	'RIDLE',0,1
	.word	527
	.byte	1,1,2,35,1,11
	.byte	'RSTR',0,1
	.word	527
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1022
	.byte	16,0,2,35,2,0,12,13,236,5,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17865
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_INP_Bits',0,13,239,1,16,4,11
	.byte	'INPCC60',0,1
	.word	527
	.byte	2,6,2,35,0,11
	.byte	'INPCC61',0,1
	.word	527
	.byte	2,4,2,35,0,11
	.byte	'INPCC62',0,1
	.word	527
	.byte	2,2,2,35,0,11
	.byte	'INPCHE',0,1
	.word	527
	.byte	2,0,2,35,0,11
	.byte	'INPERR',0,1
	.word	527
	.byte	2,6,2,35,1,11
	.byte	'INPT12',0,1
	.word	527
	.byte	2,4,2,35,1,11
	.byte	'INPT13',0,1
	.word	527
	.byte	2,2,2,35,1,11
	.byte	'reserved_14',0,4
	.word	504
	.byte	18,0,2,35,0,0,12,13,220,5,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18239
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_IEN_Bits',0,13,201,1,16,4,11
	.byte	'ENCC60R',0,1
	.word	527
	.byte	1,7,2,35,0,11
	.byte	'ENCC60F',0,1
	.word	527
	.byte	1,6,2,35,0,11
	.byte	'ENCC61R',0,1
	.word	527
	.byte	1,5,2,35,0,11
	.byte	'ENCC61F',0,1
	.word	527
	.byte	1,4,2,35,0,11
	.byte	'ENCC62R',0,1
	.word	527
	.byte	1,3,2,35,0,11
	.byte	'ENCC62F',0,1
	.word	527
	.byte	1,2,2,35,0,11
	.byte	'ENT12OM',0,1
	.word	527
	.byte	1,1,2,35,0,11
	.byte	'ENT12PM',0,1
	.word	527
	.byte	1,0,2,35,0,11
	.byte	'ENT13CM',0,1
	.word	527
	.byte	1,7,2,35,1,11
	.byte	'ENT13PM',0,1
	.word	527
	.byte	1,6,2,35,1,11
	.byte	'ENTRPF',0,1
	.word	527
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	527
	.byte	1,4,2,35,1,11
	.byte	'ENCHE',0,1
	.word	527
	.byte	1,3,2,35,1,11
	.byte	'ENWHE',0,1
	.word	527
	.byte	1,2,2,35,1,11
	.byte	'ENIDLE',0,1
	.word	527
	.byte	1,1,2,35,1,11
	.byte	'ENSTR',0,1
	.word	527
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1022
	.byte	16,0,2,35,2,0,12,13,204,5,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18457
	.byte	4,2,35,0,0,18,52
	.word	527
	.byte	19,51,0,10
	.byte	'_Ifx_CCU6_OCS_Bits',0,13,180,3,16,4,11
	.byte	'TGS',0,1
	.word	527
	.byte	2,6,2,35,0,11
	.byte	'TGB',0,1
	.word	527
	.byte	1,5,2,35,0,11
	.byte	'TG_P',0,1
	.word	527
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	504
	.byte	20,8,2,35,0,11
	.byte	'SUS',0,1
	.word	527
	.byte	4,4,2,35,3,11
	.byte	'SUS_P',0,1
	.word	527
	.byte	1,3,2,35,3,11
	.byte	'SUSSTA',0,1
	.word	527
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	527
	.byte	2,0,2,35,3,0,12,13,212,6,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18855
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_KRSTCLR_Bits',0,13,205,2,16,4,11
	.byte	'CLR',0,1
	.word	527
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	504
	.byte	31,0,2,35,0,0,12,13,140,6,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19062
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_KRST1_Bits',0,13,198,2,16,4,11
	.byte	'RST',0,1
	.word	527
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	504
	.byte	31,0,2,35,0,0,12,13,132,6,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19169
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_KRST0_Bits',0,13,190,2,16,4,11
	.byte	'RST',0,1
	.word	527
	.byte	1,7,2,35,0,11
	.byte	'RSTSTAT',0,1
	.word	527
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	504
	.byte	30,0,2,35,0,0,12,13,252,5,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19274
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_ACCEN1_Bits',0,13,82,16,4,11
	.byte	'reserved_0',0,4
	.word	504
	.byte	32,0,2,35,0,0,12,13,228,4,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19398
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_ACCEN0_Bits',0,13,45,16,4,11
	.byte	'EN0',0,1
	.word	527
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	527
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	527
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	527
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	527
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	527
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	527
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	527
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	527
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	527
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	527
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	527
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	527
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	527
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	527
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	527
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	527
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	527
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	527
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	527
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	527
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	527
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	527
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	527
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	527
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	527
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	527
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	527
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	527
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	527
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	527
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	527
	.byte	1,0,2,35,3,0,12,13,220,4,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19488
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6',0,13,204,7,25,128,2,13
	.byte	'CLC',0
	.word	11077
	.byte	4,2,35,0,13
	.byte	'MCFG',0
	.word	11211
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	11338
	.byte	4,2,35,8,13
	.byte	'MOSEL',0
	.word	11488
	.byte	4,2,35,12,13
	.byte	'PISEL0',0
	.word	11724
	.byte	4,2,35,16,13
	.byte	'PISEL2',0
	.word	11908
	.byte	4,2,35,20,13
	.byte	'reserved_18',0
	.word	3438
	.byte	4,2,35,24,13
	.byte	'KSCSR',0
	.word	12058
	.byte	4,2,35,28,13
	.byte	'T12',0
	.word	12164
	.byte	4,2,35,32,13
	.byte	'T12PR',0
	.word	12272
	.byte	4,2,35,36,13
	.byte	'T12DTC',0
	.word	12498
	.byte	4,2,35,40,13
	.byte	'reserved_2C',0
	.word	3438
	.byte	4,2,35,44,13
	.byte	'CC60R',0
	.word	12603
	.byte	4,2,35,48,13
	.byte	'CC61R',0
	.word	12708
	.byte	4,2,35,52,13
	.byte	'CC62R',0
	.word	12813
	.byte	4,2,35,56,13
	.byte	'reserved_3C',0
	.word	3438
	.byte	4,2,35,60,13
	.byte	'CC60SR',0
	.word	12919
	.byte	4,2,35,64,13
	.byte	'CC61SR',0
	.word	13025
	.byte	4,2,35,68,13
	.byte	'CC62SR',0
	.word	13131
	.byte	4,2,35,72,13
	.byte	'reserved_4C',0
	.word	3438
	.byte	4,2,35,76,13
	.byte	'T13',0
	.word	13237
	.byte	4,2,35,80,13
	.byte	'T13PR',0
	.word	13345
	.byte	4,2,35,84,13
	.byte	'CC63R',0
	.word	13451
	.byte	4,2,35,88,13
	.byte	'CC63SR',0
	.word	13558
	.byte	4,2,35,92,13
	.byte	'CMPSTAT',0
	.word	13953
	.byte	4,2,35,96,13
	.byte	'CMPMODIF',0
	.word	14258
	.byte	4,2,35,100,13
	.byte	'T12MSEL',0
	.word	14438
	.byte	4,2,35,104,13
	.byte	'reserved_6C',0
	.word	3438
	.byte	4,2,35,108,13
	.byte	'TCTR0',0
	.word	14698
	.byte	4,2,35,112,13
	.byte	'TCTR2',0
	.word	14921
	.byte	4,2,35,116,13
	.byte	'TCTR4',0
	.word	15286
	.byte	4,2,35,120,13
	.byte	'reserved_7C',0
	.word	3438
	.byte	4,2,35,124,13
	.byte	'MODCTR',0
	.word	15498
	.byte	4,3,35,128,1,13
	.byte	'TRPCTR',0
	.word	15717
	.byte	4,3,35,132,1,13
	.byte	'PSLR',0
	.word	15860
	.byte	4,3,35,136,1,13
	.byte	'MCMOUTS',0
	.word	16084
	.byte	4,3,35,140,1,13
	.byte	'MCMOUT',0
	.word	16259
	.byte	4,3,35,144,1,13
	.byte	'MCMCTR',0
	.word	16483
	.byte	4,3,35,148,1,13
	.byte	'IMON',0
	.word	16756
	.byte	4,3,35,152,1,13
	.byte	'LI',0
	.word	17101
	.byte	4,3,35,156,1,13
	.byte	'IS',0
	.word	17458
	.byte	4,3,35,160,1,13
	.byte	'ISS',0
	.word	17825
	.byte	4,3,35,164,1,13
	.byte	'ISR',0
	.word	18199
	.byte	4,3,35,168,1,13
	.byte	'INP',0
	.word	18417
	.byte	4,3,35,172,1,13
	.byte	'IEN',0
	.word	18806
	.byte	4,3,35,176,1,13
	.byte	'reserved_B4',0
	.word	18846
	.byte	52,3,35,180,1,13
	.byte	'OCS',0
	.word	19022
	.byte	4,3,35,232,1,13
	.byte	'KRSTCLR',0
	.word	19129
	.byte	4,3,35,236,1,13
	.byte	'KRST1',0
	.word	19234
	.byte	4,3,35,240,1,13
	.byte	'KRST0',0
	.word	19358
	.byte	4,3,35,244,1,13
	.byte	'ACCEN1',0
	.word	19448
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	20018
	.byte	4,3,35,252,1,0,14
	.word	20058
.L138:
	.byte	3
	.word	20914
.L140:
	.byte	8
	.byte	'IfxCcu6_isModuleEnabled',0,3,12,253,14,20
	.word	527
	.byte	1,1
.L142:
	.byte	5
	.byte	'ccu6',0,12,253,14,54
	.word	20919
.L144:
	.byte	6,0,15,12,209,2,9,1,16
	.byte	'IfxCcu6_TimerId_t12',0,0,16
	.byte	'IfxCcu6_TimerId_t13',0,1,0,15,12,127,9,1,16
	.byte	'IfxCcu6_CountingInputMode_internal',0,0,16
	.byte	'IfxCcu6_CountingInputMode_manual',0,1,16
	.byte	'IfxCcu6_CountingInputMode_externalRising',0,2,16
	.byte	'IfxCcu6_CountingInputMode_externalFalling',0,3,0
.L163:
	.byte	4
	.byte	'IfxCcu6_setCountingInputMode',0,3,12,169,15,17,1,1
.L166:
	.byte	5
	.byte	'ccu6',0,12,169,15,56
	.word	20919
.L168:
	.byte	5
	.byte	'timer',0,12,169,15,78
	.word	20976
.L170:
	.byte	5
	.byte	'mode',0,12,169,15,111
	.word	21027
.L172:
	.byte	6,0,15,12,152,1,9,1,16
	.byte	'IfxCcu6_ExternalTriggerMode_disable',0,0,16
	.byte	'IfxCcu6_ExternalTriggerMode_risingEdge',0,1,16
	.byte	'IfxCcu6_ExternalTriggerMode_fallingEdge',0,2,16
	.byte	'IfxCcu6_ExternalTriggerMode_anyEdge',0,3,0
.L382:
	.byte	4
	.byte	'IfxCcu6_setExternalRunMode',0,3,12,195,15,17,1,1
.L385:
	.byte	5
	.byte	'ccu6',0,12,195,15,54
	.word	20919
.L387:
	.byte	5
	.byte	'timer',0,12,195,15,76
	.word	20976
.L389:
	.byte	5
	.byte	'mode',0,12,195,15,111
	.word	21274
.L391:
	.byte	6,0,15,12,172,2,9,1,16
	.byte	'IfxCcu6_T12CountMode_edgeAligned',0,0,16
	.byte	'IfxCcu6_T12CountMode_centerAligned',0,1,0
.L216:
	.byte	4
	.byte	'IfxCcu6_setT12CountMode',0,3,12,162,16,17,1,1
.L219:
	.byte	5
	.byte	'ccu6',0,12,162,16,51
	.word	20919
.L221:
	.byte	5
	.byte	'mode',0,12,162,16,78
	.word	21520
.L223:
	.byte	6,0
.L232:
	.byte	4
	.byte	'IfxCcu6_setT12CounterValue',0,3,12,168,16,17,1,1
.L234:
	.byte	5
	.byte	'ccu6',0,12,168,16,54
	.word	20919
.L236:
	.byte	5
	.byte	'value',0,12,168,16,67
	.word	1022
.L238:
	.byte	6,0
.L224:
	.byte	4
	.byte	'IfxCcu6_setT12PeriodValue',0,3,12,174,16,17,1,1
.L227:
	.byte	5
	.byte	'ccu6',0,12,174,16,53
	.word	20919
.L229:
	.byte	5
	.byte	'value',0,12,174,16,66
	.word	1022
.L231:
	.byte	6,0
.L183:
	.byte	4
	.byte	'IfxCcu6_setT13CounterValue',0,3,12,186,16,17,1,1
.L186:
	.byte	5
	.byte	'ccu6',0,12,186,16,54
	.word	20919
.L188:
	.byte	5
	.byte	'value',0,12,186,16,67
	.word	1022
.L190:
	.byte	6,0
.L175:
	.byte	4
	.byte	'IfxCcu6_setT13PeriodValue',0,3,12,192,16,17,1,1
.L178:
	.byte	5
	.byte	'ccu6',0,12,192,16,53
	.word	20919
.L180:
	.byte	5
	.byte	'value',0,12,192,16,66
	.word	1022
.L182:
	.byte	6,0,15,12,183,2,9,1,16
	.byte	'IfxCcu6_T13TriggerDirection_noAction',0,0,16
	.byte	'IfxCcu6_T13TriggerDirection_onT12CountingUp',0,1,16
	.byte	'IfxCcu6_T13TriggerDirection_onT12CountingDown',0,2,16
	.byte	'IfxCcu6_T13TriggerDirection_anyT12',0,3,0
.L199:
	.byte	4
	.byte	'IfxCcu6_setT13TriggerEventDirection',0,3,12,198,16,17,1,1
.L201:
	.byte	5
	.byte	'ccu6',0,12,198,16,63
	.word	20919
.L203:
	.byte	5
	.byte	'direction',0,12,198,16,97
	.word	21923
.L205:
	.byte	6,0,15,12,194,2,9,1,16
	.byte	'IfxCcu6_T13TriggerEvent_noAction',0,0,16
	.byte	'IfxCcu6_T13TriggerEvent_onCC60RCompare',0,1,16
	.byte	'IfxCcu6_T13TriggerEvent_onCC61RCompare',0,2,16
	.byte	'IfxCcu6_T13TriggerEvent_onCC62RCompare',0,3,16
	.byte	'IfxCcu6_T13TriggerEvent_onAnyT12Compare',0,4,16
	.byte	'IfxCcu6_T13TriggerEvent_onT12Period',0,5,16
	.byte	'IfxCcu6_T13TriggerEvent_onT12Zero',0,6,16
	.byte	'IfxCcu6_T13TriggerEvent_onCCPOSxEdge',0,7,0
.L191:
	.byte	4
	.byte	'IfxCcu6_setT13TriggerEventMode',0,3,12,204,16,17,1,1
.L194:
	.byte	5
	.byte	'ccu6',0,12,204,16,58
	.word	20919
.L196:
	.byte	5
	.byte	'mode',0,12,204,16,88
	.word	22179
.L198:
	.byte	6,0
.L394:
	.byte	4
	.byte	'IfxCcu6_startTimer',0,3,12,223,16,17,1,1
.L396:
	.byte	5
	.byte	'ccu6',0,12,223,16,46
	.word	20919
.L398:
	.byte	5
	.byte	't12',0,12,223,16,60
	.word	527
.L400:
	.byte	5
	.byte	't13',0,12,223,16,73
	.word	527
.L402:
	.byte	6,0
.L480:
	.byte	4
	.byte	'IfxCcu6_stopTimer',0,3,12,233,16,17,1,1
.L483:
	.byte	5
	.byte	'ccu6',0,12,233,16,45
	.word	20919
.L485:
	.byte	5
	.byte	't12',0,12,233,16,59
	.word	527
.L487:
	.byte	5
	.byte	't13',0,12,233,16,72
	.word	527
.L489:
	.byte	6,0
.L145:
	.byte	8
	.byte	'IfxCcu6_getTimerAvailabilityStatus',0,3,12,182,13,20
	.word	527
	.byte	1,1
.L148:
	.byte	5
	.byte	'ccu6',0,12,182,13,65
	.word	20919
.L150:
	.byte	5
	.byte	'timer',0,12,182,13,87
	.word	20976
.L152:
	.byte	6,0
.L456:
	.byte	4
	.byte	'IfxCcu6_disableShadowTransfer',0,3,12,201,11,17,1,1
.L459:
	.byte	5
	.byte	'ccu6',0,12,201,11,57
	.word	20919
.L461:
	.byte	5
	.byte	't12',0,12,201,11,71
	.word	527
.L463:
	.byte	5
	.byte	't13',0,12,201,11,84
	.word	527
.L465:
	.byte	6,0
.L354:
	.byte	4
	.byte	'IfxCcu6_enableCountEvent',0,3,12,253,11,17,1,1
.L357:
	.byte	5
	.byte	'ccu6',0,12,253,11,52
	.word	20919
.L359:
	.byte	5
	.byte	't12',0,12,253,11,66
	.word	527
.L361:
	.byte	5
	.byte	't13',0,12,253,11,79
	.word	527
.L363:
	.byte	6,0,15,12,182,1,9,1,16
	.byte	'IfxCcu6_InterruptSource_cc60RisingEdge',0,0,16
	.byte	'IfxCcu6_InterruptSource_cc60FallingEdge',0,1,16
	.byte	'IfxCcu6_InterruptSource_cc61RisingEdge',0,2,16
	.byte	'IfxCcu6_InterruptSource_cc61FallingEdge',0,3,16
	.byte	'IfxCcu6_InterruptSource_cc62RisingEdge',0,4,16
	.byte	'IfxCcu6_InterruptSource_cc62FallingEdge',0,5,16
	.byte	'IfxCcu6_InterruptSource_t12OneMatch',0,6,16
	.byte	'IfxCcu6_InterruptSource_t12PeriodMatch',0,7,16
	.byte	'IfxCcu6_InterruptSource_t13CompareMatch',0,8,16
	.byte	'IfxCcu6_InterruptSource_t13PeriodMatch',0,9,16
	.byte	'IfxCcu6_InterruptSource_trap',0,10,16
	.byte	'IfxCcu6_InterruptSource_correctHallEvent',0,12,16
	.byte	'IfxCcu6_InterruptSource_wrongHallEvent',0,13,0
.L272:
	.byte	4
	.byte	'IfxCcu6_enableInterrupt',0,3,12,155,12,17,1,1
.L275:
	.byte	5
	.byte	'ccu6',0,12,155,12,51
	.word	20919
.L277:
	.byte	5
	.byte	'source',0,12,155,12,81
	.word	22938
.L279:
	.byte	6,0
.L335:
	.byte	4
	.byte	'IfxCcu6_enableShadowTransfer',0,3,12,198,12,17,1,1
.L338:
	.byte	5
	.byte	'ccu6',0,12,198,12,56
	.word	20919
.L340:
	.byte	5
	.byte	't12',0,12,198,12,70
	.word	527
.L342:
	.byte	5
	.byte	't13',0,12,198,12,83
	.word	527
.L344:
	.byte	6,0
.L421:
	.byte	4
	.byte	'IfxCcu6_enableSingleShotMode',0,3,12,208,12,17,1,1
.L424:
	.byte	5
	.byte	'ccu6',0,12,208,12,56
	.word	20919
.L426:
	.byte	5
	.byte	'timer',0,12,208,12,78
	.word	20976
.L428:
	.byte	6,0
.L155:
	.byte	4
	.byte	'IfxCcu6_enableTimer',0,3,12,221,12,17,1,1
.L157:
	.byte	5
	.byte	'ccu6',0,12,221,12,47
	.word	20919
.L159:
	.byte	5
	.byte	'timer',0,12,221,12,69
	.word	20976
.L161:
	.byte	6,0,15,12,81,9,1,16
	.byte	'IfxCcu6_CaptureCompareInput_cC60',0,0,16
	.byte	'IfxCcu6_CaptureCompareInput_cC61',0,2,16
	.byte	'IfxCcu6_CaptureCompareInput_cC62',0,4,16
	.byte	'IfxCcu6_CaptureCompareInput_cTRAP',0,6,16
	.byte	'IfxCcu6_CaptureCompareInput_cCPOS0',0,8,16
	.byte	'IfxCcu6_CaptureCompareInput_cCPOS1',0,10,16
	.byte	'IfxCcu6_CaptureCompareInput_cCPOS2',0,12,0,15,12,94,9,1,16
	.byte	'IfxCcu6_CaptureCompareInputSignal_a',0,0,16
	.byte	'IfxCcu6_CaptureCompareInputSignal_b',0,1,16
	.byte	'IfxCcu6_CaptureCompareInputSignal_c',0,2,16
	.byte	'IfxCcu6_CaptureCompareInputSignal_d',0,3,0,4
	.byte	'IfxCcu6_setCaptureCompareInputSignal',0,3,12,161,15,17,1,1,5
	.byte	'ccu6',0,12,161,15,64
	.word	20919
	.byte	5
	.byte	'input',0,12,161,15,98
	.word	23741
	.byte	5
	.byte	'signal',0,12,161,15,139,1
	.word	23999
	.byte	6,0,20,10,190,1,9,8,13
	.byte	'port',0
	.word	10185
	.byte	4,2,35,0,13
	.byte	'pinIndex',0
	.word	527
	.byte	1,2,35,4,0,15,15,130,1,9,1,16
	.byte	'Ifx_RxSel_a',0,0,16
	.byte	'Ifx_RxSel_b',0,1,16
	.byte	'Ifx_RxSel_c',0,2,16
	.byte	'Ifx_RxSel_d',0,3,16
	.byte	'Ifx_RxSel_e',0,4,16
	.byte	'Ifx_RxSel_f',0,5,16
	.byte	'Ifx_RxSel_g',0,6,16
	.byte	'Ifx_RxSel_h',0,7,0,20,14,115,15,16,13
	.byte	'module',0
	.word	20919
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	24250
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	24289
	.byte	1,2,35,12,0,21
	.word	24408
.L242:
	.byte	3
	.word	24459
.L244:
	.byte	4
	.byte	'IfxCcu6_initT12hrPin',0,3,12,223,14,17,1,1
.L246:
	.byte	5
	.byte	't12hrIn',0,12,223,14,62
	.word	24464
.L248:
	.byte	5
	.byte	'inputMode',0,12,223,14,89
	.word	10190
.L250:
	.byte	17,6,0,0,20,14,123,15,16,13
	.byte	'module',0
	.word	20919
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	24250
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	24289
	.byte	1,2,35,12,0,21
	.word	24538
.L261:
	.byte	3
	.word	24589
.L263:
	.byte	4
	.byte	'IfxCcu6_initT13hrPin',0,3,12,238,14,17,1,1
.L265:
	.byte	5
	.byte	't13hrIn',0,12,238,14,62
	.word	24594
.L267:
	.byte	5
	.byte	'inputMode',0,12,238,14,89
	.word	10190
.L269:
	.byte	17,6,0,0,22
	.word	248
	.byte	23
	.word	274
	.byte	6,0,22
	.word	309
	.byte	23
	.word	341
	.byte	6,0,22
	.word	354
	.byte	6,0,22
	.word	423
	.byte	23
	.word	442
	.byte	6,0,22
	.word	458
	.byte	23
	.word	473
	.byte	23
	.word	487
	.byte	6,0,22
	.word	883
	.byte	23
	.word	911
	.byte	6,0,22
	.word	926
	.byte	23
	.word	948
	.byte	6,0,22
	.word	1044
	.byte	23
	.word	1064
	.byte	23
	.word	1077
	.byte	23
	.word	1099
	.byte	17,24
	.word	883
	.byte	23
	.word	911
	.byte	25
	.word	924
	.byte	0,6,0,0,22
	.word	1821
	.byte	23
	.word	1861
	.byte	23
	.word	1879
	.byte	6,0,22
	.word	1899
	.byte	23
	.word	1937
	.byte	23
	.word	1955
	.byte	6,0,22
	.word	1975
	.byte	23
	.word	2026
	.byte	6,0,22
	.word	2125
	.byte	6,0,22
	.word	2159
	.byte	6,0,22
	.word	2222
	.byte	23
	.word	2263
	.byte	6,0,22
	.word	2282
	.byte	23
	.word	2337
	.byte	6,0,22
	.word	2356
	.byte	23
	.word	2396
	.byte	23
	.word	2413
	.byte	17,6,0,0,22
	.word	10315
	.byte	23
	.word	10347
	.byte	23
	.word	10361
	.byte	23
	.word	10379
	.byte	6,0,22
	.word	10682
	.byte	23
	.word	10715
	.byte	23
	.word	10729
	.byte	23
	.word	10747
	.byte	23
	.word	10761
	.byte	6,0,22
	.word	10881
	.byte	23
	.word	10909
	.byte	23
	.word	10923
	.byte	23
	.word	10941
	.byte	6,0,15,10,95,9,1,16
	.byte	'IfxPort_Mode_inputNoPullDevice',0,0,16
	.byte	'IfxPort_Mode_inputPullDown',0,8,16
	.byte	'IfxPort_Mode_inputPullUp',0,16,16
	.byte	'IfxPort_Mode_outputPushPullGeneral',0,128,1,16
	.byte	'IfxPort_Mode_outputPushPullAlt1',0,136,1,16
	.byte	'IfxPort_Mode_outputPushPullAlt2',0,144,1,16
	.byte	'IfxPort_Mode_outputPushPullAlt3',0,152,1,16
	.byte	'IfxPort_Mode_outputPushPullAlt4',0,160,1,16
	.byte	'IfxPort_Mode_outputPushPullAlt5',0,168,1,16
	.byte	'IfxPort_Mode_outputPushPullAlt6',0,176,1,16
	.byte	'IfxPort_Mode_outputPushPullAlt7',0,184,1,16
	.byte	'IfxPort_Mode_outputOpenDrainGeneral',0,192,1,16
	.byte	'IfxPort_Mode_outputOpenDrainAlt1',0,200,1,16
	.byte	'IfxPort_Mode_outputOpenDrainAlt2',0,208,1,16
	.byte	'IfxPort_Mode_outputOpenDrainAlt3',0,216,1,16
	.byte	'IfxPort_Mode_outputOpenDrainAlt4',0,224,1,16
	.byte	'IfxPort_Mode_outputOpenDrainAlt5',0,232,1,16
	.byte	'IfxPort_Mode_outputOpenDrainAlt6',0,240,1,16
	.byte	'IfxPort_Mode_outputOpenDrainAlt7',0,248,1,0,26
	.byte	'IfxPort_setPinMode',0,10,247,2,17,1,1,1,1,5
	.byte	'port',0,10,247,2,43
	.word	10185
	.byte	5
	.byte	'pinIndex',0,10,247,2,55
	.word	527
	.byte	5
	.byte	'mode',0,10,247,2,78
	.word	24966
	.byte	0,22
	.word	20924
	.byte	23
	.word	20960
	.byte	6,0,22
	.word	21192
	.byte	23
	.word	21229
	.byte	23
	.word	21243
	.byte	23
	.word	21258
	.byte	6,0,22
	.word	21440
	.byte	23
	.word	21475
	.byte	23
	.word	21489
	.byte	23
	.word	21504
	.byte	6,0,22
	.word	21599
	.byte	23
	.word	21631
	.byte	23
	.word	21645
	.byte	6,0,22
	.word	21661
	.byte	23
	.word	21696
	.byte	23
	.word	21710
	.byte	6,0,22
	.word	21727
	.byte	23
	.word	21761
	.byte	23
	.word	21775
	.byte	6,0,22
	.word	21792
	.byte	23
	.word	21827
	.byte	23
	.word	21841
	.byte	6,0,22
	.word	21858
	.byte	23
	.word	21892
	.byte	23
	.word	21906
	.byte	6,0,22
	.word	22100
	.byte	23
	.word	22144
	.byte	23
	.word	22158
	.byte	6,0,22
	.word	22499
	.byte	23
	.word	22538
	.byte	23
	.word	22552
	.byte	6,0,22
	.word	22568
	.byte	23
	.word	22595
	.byte	23
	.word	22609
	.byte	23
	.word	22622
	.byte	6,0,22
	.word	22637
	.byte	23
	.word	22663
	.byte	23
	.word	22677
	.byte	23
	.word	22690
	.byte	6,0,15,12,233,1,9,1,16
	.byte	'IfxCcu6_ServiceRequest_0',0,0,16
	.byte	'IfxCcu6_ServiceRequest_1',0,1,16
	.byte	'IfxCcu6_ServiceRequest_2',0,2,16
	.byte	'IfxCcu6_ServiceRequest_3',0,3,0,26
	.byte	'IfxCcu6_routeInterruptNode',0,12,188,5,17,1,1,1,1,5
	.byte	'ccu6',0,12,188,5,54
	.word	20919
	.byte	5
	.byte	'source',0,12,188,5,84
	.word	22938
	.byte	5
	.byte	'serviceRequest',0,12,188,5,115
	.word	25929
	.byte	0,27
	.byte	'IfxCcu6_setT12Frequency',0,12,226,5,20
	.word	300
	.byte	1,1,1,1,5
	.byte	'ccu6',0,12,226,5,54
	.word	20919
	.byte	5
	.byte	'frequency',0,12,226,5,68
	.word	300
	.byte	5
	.byte	'period',0,12,226,5,94
	.word	2201
	.byte	5
	.byte	'countMode',0,12,226,5,123
	.word	21520
	.byte	0,26
	.byte	'IfxCcu6_setT12InputSignal',0,12,233,5,17,1,1,1,1,5
	.byte	'ccu6',0,12,233,5,53
	.word	20919
	.byte	5
	.byte	'extInput',0,12,233,5,77
	.word	24464
	.byte	0,27
	.byte	'IfxCcu6_setT13Frequency',0,12,252,5,20
	.word	300
	.byte	1,1,1,1,5
	.byte	'ccu6',0,12,252,5,54
	.word	20919
	.byte	5
	.byte	'frequency',0,12,252,5,68
	.word	300
	.byte	5
	.byte	'period',0,12,252,5,94
	.word	2201
	.byte	0,26
	.byte	'IfxCcu6_setT13InputSignal',0,12,131,6,17,1,1,1,1,5
	.byte	'ccu6',0,12,131,6,53
	.word	20919
	.byte	5
	.byte	'extInput',0,12,131,6,77
	.word	24594
	.byte	0,22
	.word	22705
	.byte	23
	.word	22752
	.byte	23
	.word	22766
	.byte	6,0,27
	.byte	'IfxCcu6_getSrcAddress',0,12,143,7,35
	.word	878
	.byte	1,1,1,1,5
	.byte	'ccu6',0,12,143,7,67
	.word	20919
	.byte	5
	.byte	'serviceRequest',0,12,143,7,96
	.word	25929
	.byte	0,22
	.word	22783
	.byte	23
	.word	22821
	.byte	23
	.word	22835
	.byte	23
	.word	22848
	.byte	6,0,22
	.word	22863
	.byte	23
	.word	22896
	.byte	23
	.word	22910
	.byte	23
	.word	22923
	.byte	6,0,22
	.word	23471
	.byte	23
	.word	23503
	.byte	23
	.word	23517
	.byte	6,0,22
	.word	23535
	.byte	23
	.word	23572
	.byte	23
	.word	23586
	.byte	23
	.word	23599
	.byte	6,0,22
	.word	23614
	.byte	23
	.word	23651
	.byte	23
	.word	23665
	.byte	6,0,22
	.word	23682
	.byte	23
	.word	23710
	.byte	23
	.word	23724
	.byte	6,0,22
	.word	24157
	.byte	23
	.word	24202
	.byte	23
	.word	24216
	.byte	23
	.word	24231
	.byte	6,0,26
	.byte	'IfxCcu6_enableModule',0,12,146,9,17,1,1,1,1,5
	.byte	'ccu6',0,12,146,9,48
	.word	20919
	.byte	0,22
	.word	24469
	.byte	23
	.word	24498
	.byte	23
	.word	24515
	.byte	17,24
	.word	10315
	.byte	23
	.word	10347
	.byte	23
	.word	10361
	.byte	23
	.word	10379
	.byte	25
	.word	10393
	.byte	0,6,0,0,22
	.word	24599
	.byte	23
	.word	24628
	.byte	23
	.word	24645
	.byte	17,24
	.word	10315
	.byte	23
	.word	10347
	.byte	23
	.word	10361
	.byte	23
	.word	10379
	.byte	25
	.word	10393
	.byte	0,6,0,0,10
	.byte	'Timer_s',0,17,72,8,16,13
	.byte	't12Frequency',0
	.word	300
	.byte	4,2,35,0,13
	.byte	't12Period',0
	.word	2201
	.byte	4,2,35,4,13
	.byte	't13Frequency',0
	.word	300
	.byte	4,2,35,8,13
	.byte	't13Period',0
	.word	2201
	.byte	4,2,35,12,0,20,16,145,2,9,12,13
	.byte	't12ExtInputTrigger',0
	.word	24464
	.byte	4,2,35,0,13
	.byte	't13ExtInputTrigger',0
	.word	24594
	.byte	4,2,35,4,13
	.byte	'extInputTriggerMode',0
	.word	21274
	.byte	1,2,35,8,13
	.byte	't13InSyncWithT12',0
	.word	527
	.byte	1,2,35,9,0,20,16,168,2,9,36,13
	.byte	'base',0
	.word	26829
	.byte	16,2,35,0,13
	.byte	'ccu6',0
	.word	20919
	.byte	4,2,35,16,13
	.byte	'timer',0
	.word	20976
	.byte	1,2,35,20,13
	.byte	'trigger',0
	.word	26925
	.byte	12,2,35,24,0
.L134:
	.byte	3
	.word	27043
	.byte	20,17,84,9,24,13
	.byte	't12Frequency',0
	.word	300
	.byte	4,2,35,0,13
	.byte	't12Period',0
	.word	2201
	.byte	4,2,35,4,13
	.byte	't13Frequency',0
	.word	300
	.byte	4,2,35,8,13
	.byte	't13Period',0
	.word	2201
	.byte	4,2,35,12,13
	.byte	'waitingTime',0
	.word	2201
	.byte	4,2,35,16,13
	.byte	'activeCount',0
	.word	2201
	.byte	4,2,35,20,0,20,16,234,1,9,20,13
	.byte	't12ExtClockEnabled',0
	.word	527
	.byte	1,2,35,0,13
	.byte	't12ExtClockInput',0
	.word	24464
	.byte	4,2,35,4,13
	.byte	't12countingInputMode',0
	.word	21027
	.byte	1,2,35,8,13
	.byte	't13ExtClockEnabled',0
	.word	527
	.byte	1,2,35,9,13
	.byte	't13ExtClockInput',0
	.word	24594
	.byte	4,2,35,12,13
	.byte	't13countingInputMode',0
	.word	21027
	.byte	1,2,35,16,0,20,16,128,2,9,4,13
	.byte	'countMode',0
	.word	21520
	.byte	1,2,35,0,13
	.byte	'counterValue',0
	.word	1022
	.byte	2,2,35,2,0,20,16,136,2,9,4,13
	.byte	'counterValue',0
	.word	1022
	.byte	2,2,35,0,13
	.byte	't12SyncEvent',0
	.word	22179
	.byte	1,2,35,2,13
	.byte	't12SyncDirection',0
	.word	21923
	.byte	1,2,35,3,0,20,16,246,1,9,6,13
	.byte	'source',0
	.word	22938
	.byte	1,2,35,0,13
	.byte	'serviceRequest',0
	.word	25929
	.byte	1,2,35,1,13
	.byte	'priority',0
	.word	1022
	.byte	2,2,35,2,13
	.byte	'typeOfService',0
	.word	963
	.byte	1,2,35,4,0,20,16,157,2,9,12,13
	.byte	't12hr',0
	.word	24464
	.byte	4,2,35,0,13
	.byte	't13hr',0
	.word	24594
	.byte	4,2,35,4,13
	.byte	't1xhrInputMode',0
	.word	10190
	.byte	1,2,35,8,0,3
	.word	27633
	.byte	20,16,178,2,9,100,13
	.byte	'base',0
	.word	27115
	.byte	24,2,35,0,13
	.byte	'ccu6',0
	.word	20919
	.byte	4,2,35,24,13
	.byte	'timer',0
	.word	20976
	.byte	1,2,35,28,13
	.byte	'synchronousOperation',0
	.word	527
	.byte	1,2,35,29,13
	.byte	'clock',0
	.word	27245
	.byte	20,2,35,32,13
	.byte	'timer12',0
	.word	27420
	.byte	4,2,35,52,13
	.byte	'timer13',0
	.word	27468
	.byte	4,2,35,56,13
	.byte	'interrupt1',0
	.word	27545
	.byte	6,2,35,60,13
	.byte	'interrupt2',0
	.word	27545
	.byte	6,2,35,66,13
	.byte	'interrupt3',0
	.word	27545
	.byte	6,2,35,72,13
	.byte	'interrupt4',0
	.word	27545
	.byte	6,2,35,78,13
	.byte	'trigger',0
	.word	26925
	.byte	12,2,35,84,13
	.byte	'pins',0
	.word	27694
	.byte	4,2,35,96,0,21
	.word	27699
.L136:
	.byte	3
	.word	27939
	.byte	21
	.word	27633
.L239:
	.byte	3
	.word	27949
.L328:
	.byte	3
	.word	27699
.L331:
	.byte	21
	.word	27699
	.byte	7
	.byte	'short int',0,2,5,28
	.byte	'__wchar_t',0,18,1,1
	.word	27969
	.byte	28
	.byte	'__size_t',0,18,1,1
	.word	504
	.byte	28
	.byte	'__ptrdiff_t',0,18,1,1
	.word	520
	.byte	29,1,3
	.word	28037
	.byte	28
	.byte	'__codeptr',0,18,1,1
	.word	28039
	.byte	28
	.byte	'boolean',0,19,101,29
	.word	527
	.byte	28
	.byte	'uint8',0,19,105,29
	.word	527
	.byte	28
	.byte	'uint16',0,19,109,29
	.word	1022
	.byte	28
	.byte	'uint32',0,19,113,29
	.word	2201
	.byte	28
	.byte	'uint64',0,19,118,29
	.word	386
	.byte	28
	.byte	'sint16',0,19,126,29
	.word	27969
	.byte	7
	.byte	'long int',0,4,5,28
	.byte	'sint32',0,19,131,1,29
	.word	28152
	.byte	7
	.byte	'long long int',0,8,5,28
	.byte	'sint64',0,19,138,1,29
	.word	28180
	.byte	28
	.byte	'float32',0,19,167,1,29
	.word	300
	.byte	28
	.byte	'pvoid',0,15,57,28
	.word	418
	.byte	28
	.byte	'Ifx_TickTime',0,15,79,28
	.word	28180
	.byte	28
	.byte	'Ifx_Priority',0,15,103,16
	.word	1022
	.byte	28
	.byte	'Ifx_TimerValue',0,15,104,16
	.word	2201
	.byte	28
	.byte	'Ifx_RxSel',0,15,140,1,3
	.word	24289
	.byte	28
	.byte	'Ifx_CCU6_ACCEN0_Bits',0,13,79,3
	.word	19488
	.byte	28
	.byte	'Ifx_CCU6_ACCEN1_Bits',0,13,85,3
	.word	19398
	.byte	28
	.byte	'Ifx_CCU6_CC60R_Bits',0,13,92,3
	.word	12538
	.byte	28
	.byte	'Ifx_CCU6_CC60SR_Bits',0,13,99,3
	.word	12853
	.byte	28
	.byte	'Ifx_CCU6_CC61R_Bits',0,13,106,3
	.word	12643
	.byte	28
	.byte	'Ifx_CCU6_CC61SR_Bits',0,13,113,3
	.word	12959
	.byte	28
	.byte	'Ifx_CCU6_CC62R_Bits',0,13,120,3
	.word	12748
	.byte	28
	.byte	'Ifx_CCU6_CC62SR_Bits',0,13,127,3
	.word	13065
	.byte	28
	.byte	'Ifx_CCU6_CC63R_Bits',0,13,134,1,3
	.word	13385
	.byte	28
	.byte	'Ifx_CCU6_CC63SR_Bits',0,13,141,1,3
	.word	13491
	.byte	28
	.byte	'Ifx_CCU6_CLC_Bits',0,13,151,1,3
	.word	10959
	.byte	28
	.byte	'Ifx_CCU6_CMPMODIF_Bits',0,13,168,1,3
	.word	13993
	.byte	28
	.byte	'Ifx_CCU6_CMPSTAT_Bits',0,13,190,1,3
	.word	13598
	.byte	28
	.byte	'Ifx_CCU6_ID_Bits',0,13,198,1,3
	.word	11251
	.byte	28
	.byte	'Ifx_CCU6_IEN_Bits',0,13,220,1,3
	.word	18457
	.byte	28
	.byte	'Ifx_CCU6_IMON_Bits',0,13,236,1,3
	.word	16523
	.byte	28
	.byte	'Ifx_CCU6_INP_Bits',0,13,249,1,3
	.word	18239
	.byte	28
	.byte	'Ifx_CCU6_IS_Bits',0,13,143,2,3
	.word	17141
	.byte	28
	.byte	'Ifx_CCU6_ISR_Bits',0,13,165,2,3
	.word	17865
	.byte	28
	.byte	'Ifx_CCU6_ISS_Bits',0,13,187,2,3
	.word	17498
	.byte	28
	.byte	'Ifx_CCU6_KRST0_Bits',0,13,195,2,3
	.word	19274
	.byte	28
	.byte	'Ifx_CCU6_KRST1_Bits',0,13,202,2,3
	.word	19169
	.byte	28
	.byte	'Ifx_CCU6_KRSTCLR_Bits',0,13,209,2,3
	.word	19062
	.byte	28
	.byte	'Ifx_CCU6_KSCSR_Bits',0,13,219,2,3
	.word	11948
	.byte	28
	.byte	'Ifx_CCU6_LI_Bits',0,13,238,2,3
	.word	16796
	.byte	28
	.byte	'Ifx_CCU6_MCFG_Bits',0,13,247,2,3
	.word	11117
	.byte	28
	.byte	'Ifx_CCU6_MCMCTR_Bits',0,13,132,3,3
	.word	16299
	.byte	28
	.byte	'Ifx_CCU6_MCMOUT_Bits',0,13,143,3,3
	.word	16124
	.byte	28
	.byte	'Ifx_CCU6_MCMOUTS_Bits',0,13,156,3,3
	.word	15900
	.byte	28
	.byte	'Ifx_CCU6_MODCTR_Bits',0,13,168,3,3
	.word	15326
	.byte	28
	.byte	'Ifx_CCU6_MOSEL_Bits',0,13,177,3,3
	.word	11378
	.byte	28
	.byte	'Ifx_CCU6_OCS_Bits',0,13,190,3,3
	.word	18855
	.byte	28
	.byte	'Ifx_CCU6_PISEL0_Bits',0,13,204,3,3
	.word	11528
	.byte	28
	.byte	'Ifx_CCU6_PISEL2_Bits',0,13,215,3,3
	.word	11764
	.byte	28
	.byte	'Ifx_CCU6_PSLR_Bits',0,13,224,3,3
	.word	15757
	.byte	28
	.byte	'Ifx_CCU6_T12_Bits',0,13,231,3,3
	.word	12098
	.byte	28
	.byte	'Ifx_CCU6_T12DTC_Bits',0,13,245,3,3
	.word	12312
	.byte	28
	.byte	'Ifx_CCU6_T12MSEL_Bits',0,13,128,4,3
	.word	14298
	.byte	28
	.byte	'Ifx_CCU6_T12PR_Bits',0,13,135,4,3
	.word	12204
	.byte	28
	.byte	'Ifx_CCU6_T13_Bits',0,13,142,4,3
	.word	13171
	.byte	28
	.byte	'Ifx_CCU6_T13PR_Bits',0,13,149,4,3
	.word	13277
	.byte	28
	.byte	'Ifx_CCU6_TCTR0_Bits',0,13,165,4,3
	.word	14478
	.byte	28
	.byte	'Ifx_CCU6_TCTR2_Bits',0,13,178,4,3
	.word	14738
	.byte	28
	.byte	'Ifx_CCU6_TCTR4_Bits',0,13,199,4,3
	.word	14961
	.byte	28
	.byte	'Ifx_CCU6_TRPCTR_Bits',0,13,212,4,3
	.word	15538
	.byte	28
	.byte	'Ifx_CCU6_ACCEN0',0,13,225,4,3
	.word	20018
	.byte	28
	.byte	'Ifx_CCU6_ACCEN1',0,13,233,4,3
	.word	19448
	.byte	28
	.byte	'Ifx_CCU6_CC60R',0,13,241,4,3
	.word	12603
	.byte	28
	.byte	'Ifx_CCU6_CC60SR',0,13,249,4,3
	.word	12919
	.byte	28
	.byte	'Ifx_CCU6_CC61R',0,13,129,5,3
	.word	12708
	.byte	28
	.byte	'Ifx_CCU6_CC61SR',0,13,137,5,3
	.word	13025
	.byte	28
	.byte	'Ifx_CCU6_CC62R',0,13,145,5,3
	.word	12813
	.byte	28
	.byte	'Ifx_CCU6_CC62SR',0,13,153,5,3
	.word	13131
	.byte	28
	.byte	'Ifx_CCU6_CC63R',0,13,161,5,3
	.word	13451
	.byte	28
	.byte	'Ifx_CCU6_CC63SR',0,13,169,5,3
	.word	13558
	.byte	28
	.byte	'Ifx_CCU6_CLC',0,13,177,5,3
	.word	11077
	.byte	28
	.byte	'Ifx_CCU6_CMPMODIF',0,13,185,5,3
	.word	14258
	.byte	28
	.byte	'Ifx_CCU6_CMPSTAT',0,13,193,5,3
	.word	13953
	.byte	28
	.byte	'Ifx_CCU6_ID',0,13,201,5,3
	.word	11338
	.byte	28
	.byte	'Ifx_CCU6_IEN',0,13,209,5,3
	.word	18806
	.byte	28
	.byte	'Ifx_CCU6_IMON',0,13,217,5,3
	.word	16756
	.byte	28
	.byte	'Ifx_CCU6_INP',0,13,225,5,3
	.word	18417
	.byte	28
	.byte	'Ifx_CCU6_IS',0,13,233,5,3
	.word	17458
	.byte	28
	.byte	'Ifx_CCU6_ISR',0,13,241,5,3
	.word	18199
	.byte	28
	.byte	'Ifx_CCU6_ISS',0,13,249,5,3
	.word	17825
	.byte	28
	.byte	'Ifx_CCU6_KRST0',0,13,129,6,3
	.word	19358
	.byte	28
	.byte	'Ifx_CCU6_KRST1',0,13,137,6,3
	.word	19234
	.byte	28
	.byte	'Ifx_CCU6_KRSTCLR',0,13,145,6,3
	.word	19129
	.byte	28
	.byte	'Ifx_CCU6_KSCSR',0,13,153,6,3
	.word	12058
	.byte	28
	.byte	'Ifx_CCU6_LI',0,13,161,6,3
	.word	17101
	.byte	28
	.byte	'Ifx_CCU6_MCFG',0,13,169,6,3
	.word	11211
	.byte	28
	.byte	'Ifx_CCU6_MCMCTR',0,13,177,6,3
	.word	16483
	.byte	28
	.byte	'Ifx_CCU6_MCMOUT',0,13,185,6,3
	.word	16259
	.byte	28
	.byte	'Ifx_CCU6_MCMOUTS',0,13,193,6,3
	.word	16084
	.byte	28
	.byte	'Ifx_CCU6_MODCTR',0,13,201,6,3
	.word	15498
	.byte	28
	.byte	'Ifx_CCU6_MOSEL',0,13,209,6,3
	.word	11488
	.byte	28
	.byte	'Ifx_CCU6_OCS',0,13,217,6,3
	.word	19022
	.byte	28
	.byte	'Ifx_CCU6_PISEL0',0,13,225,6,3
	.word	11724
	.byte	28
	.byte	'Ifx_CCU6_PISEL2',0,13,233,6,3
	.word	11908
	.byte	28
	.byte	'Ifx_CCU6_PSLR',0,13,241,6,3
	.word	15860
	.byte	28
	.byte	'Ifx_CCU6_T12',0,13,249,6,3
	.word	12164
	.byte	28
	.byte	'Ifx_CCU6_T12DTC',0,13,129,7,3
	.word	12498
	.byte	28
	.byte	'Ifx_CCU6_T12MSEL',0,13,137,7,3
	.word	14438
	.byte	28
	.byte	'Ifx_CCU6_T12PR',0,13,145,7,3
	.word	12272
	.byte	28
	.byte	'Ifx_CCU6_T13',0,13,153,7,3
	.word	13237
	.byte	28
	.byte	'Ifx_CCU6_T13PR',0,13,161,7,3
	.word	13345
	.byte	28
	.byte	'Ifx_CCU6_TCTR0',0,13,169,7,3
	.word	14698
	.byte	28
	.byte	'Ifx_CCU6_TCTR2',0,13,177,7,3
	.word	14921
	.byte	28
	.byte	'Ifx_CCU6_TCTR4',0,13,185,7,3
	.word	15286
	.byte	28
	.byte	'Ifx_CCU6_TRPCTR',0,13,193,7,3
	.word	15717
	.byte	14
	.word	20058
	.byte	28
	.byte	'Ifx_CCU6',0,13,130,8,3
	.word	30697
	.byte	28
	.byte	'IfxSrc_Tos',0,5,74,3
	.word	963
	.byte	28
	.byte	'Ifx_SRC_SRCR_Bits',0,4,62,3
	.word	544
	.byte	28
	.byte	'Ifx_SRC_SRCR',0,4,75,3
	.word	834
	.byte	10
	.byte	'_Ifx_SRC_AGBT',0,4,86,25,4,13
	.byte	'SR',0
	.word	834
	.byte	4,2,35,0,0,14
	.word	30786
	.byte	28
	.byte	'Ifx_SRC_AGBT',0,4,89,3
	.word	30818
	.byte	10
	.byte	'_Ifx_SRC_ASCLIN',0,4,92,25,12,13
	.byte	'TX',0
	.word	834
	.byte	4,2,35,0,13
	.byte	'RX',0
	.word	834
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	834
	.byte	4,2,35,8,0,14
	.word	30844
	.byte	28
	.byte	'Ifx_SRC_ASCLIN',0,4,97,3
	.word	30903
	.byte	10
	.byte	'_Ifx_SRC_BCUSPB',0,4,100,25,4,13
	.byte	'SBSRC',0
	.word	834
	.byte	4,2,35,0,0,14
	.word	30931
	.byte	28
	.byte	'Ifx_SRC_BCUSPB',0,4,103,3
	.word	30968
	.byte	18,64
	.word	834
	.byte	19,15,0,10
	.byte	'_Ifx_SRC_CAN',0,4,106,25,64,13
	.byte	'INT',0
	.word	30996
	.byte	64,2,35,0,0,14
	.word	31005
	.byte	28
	.byte	'Ifx_SRC_CAN',0,4,109,3
	.word	31037
	.byte	10
	.byte	'_Ifx_SRC_CCU6',0,4,112,25,16,13
	.byte	'SR0',0
	.word	834
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	834
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	834
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	834
	.byte	4,2,35,12,0,14
	.word	31062
	.byte	28
	.byte	'Ifx_SRC_CCU6',0,4,118,3
	.word	31134
	.byte	18,8
	.word	834
	.byte	19,1,0,10
	.byte	'_Ifx_SRC_CERBERUS',0,4,121,25,8,13
	.byte	'SR',0
	.word	31160
	.byte	8,2,35,0,0,14
	.word	31169
	.byte	28
	.byte	'Ifx_SRC_CERBERUS',0,4,124,3
	.word	31205
	.byte	10
	.byte	'_Ifx_SRC_CIF',0,4,127,25,16,13
	.byte	'MI',0
	.word	834
	.byte	4,2,35,0,13
	.byte	'MIEP',0
	.word	834
	.byte	4,2,35,4,13
	.byte	'ISP',0
	.word	834
	.byte	4,2,35,8,13
	.byte	'MJPEG',0
	.word	834
	.byte	4,2,35,12,0,14
	.word	31235
	.byte	28
	.byte	'Ifx_SRC_CIF',0,4,133,1,3
	.word	31308
	.byte	10
	.byte	'_Ifx_SRC_CPU',0,4,136,1,25,4,13
	.byte	'SBSRC',0
	.word	834
	.byte	4,2,35,0,0,14
	.word	31334
	.byte	28
	.byte	'Ifx_SRC_CPU',0,4,139,1,3
	.word	31369
	.byte	18,192,1
	.word	834
	.byte	19,47,0,10
	.byte	'_Ifx_SRC_DMA',0,4,142,1,25,208,1,13
	.byte	'ERR',0
	.word	834
	.byte	4,2,35,0,13
	.byte	'reserved_4',0
	.word	5597
	.byte	12,2,35,4,13
	.byte	'CH',0
	.word	31395
	.byte	192,1,2,35,16,0,14
	.word	31405
	.byte	28
	.byte	'Ifx_SRC_DMA',0,4,147,1,3
	.word	31472
	.byte	10
	.byte	'_Ifx_SRC_DSADC',0,4,150,1,25,8,13
	.byte	'SRM',0
	.word	834
	.byte	4,2,35,0,13
	.byte	'SRA',0
	.word	834
	.byte	4,2,35,4,0,14
	.word	31498
	.byte	28
	.byte	'Ifx_SRC_DSADC',0,4,154,1,3
	.word	31546
	.byte	10
	.byte	'_Ifx_SRC_EMEM',0,4,157,1,25,4,13
	.byte	'SR',0
	.word	834
	.byte	4,2,35,0,0,14
	.word	31574
	.byte	28
	.byte	'Ifx_SRC_EMEM',0,4,160,1,3
	.word	31607
	.byte	18,40
	.word	527
	.byte	19,39,0,10
	.byte	'_Ifx_SRC_ERAY',0,4,163,1,25,80,13
	.byte	'INT',0
	.word	31160
	.byte	8,2,35,0,13
	.byte	'TINT',0
	.word	31160
	.byte	8,2,35,8,13
	.byte	'NDAT',0
	.word	31160
	.byte	8,2,35,16,13
	.byte	'MBSC',0
	.word	31160
	.byte	8,2,35,24,13
	.byte	'OBUSY',0
	.word	834
	.byte	4,2,35,32,13
	.byte	'IBUSY',0
	.word	834
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	31634
	.byte	40,2,35,40,0,14
	.word	31643
	.byte	28
	.byte	'Ifx_SRC_ERAY',0,4,172,1,3
	.word	31770
	.byte	10
	.byte	'_Ifx_SRC_ETH',0,4,175,1,25,4,13
	.byte	'SR',0
	.word	834
	.byte	4,2,35,0,0,14
	.word	31797
	.byte	28
	.byte	'Ifx_SRC_ETH',0,4,178,1,3
	.word	31829
	.byte	10
	.byte	'_Ifx_SRC_FCE',0,4,181,1,25,4,13
	.byte	'SR',0
	.word	834
	.byte	4,2,35,0,0,14
	.word	31855
	.byte	28
	.byte	'Ifx_SRC_FCE',0,4,184,1,3
	.word	31887
	.byte	10
	.byte	'_Ifx_SRC_FFT',0,4,187,1,25,12,13
	.byte	'DONE',0
	.word	834
	.byte	4,2,35,0,13
	.byte	'ERR',0
	.word	834
	.byte	4,2,35,4,13
	.byte	'RFS',0
	.word	834
	.byte	4,2,35,8,0,14
	.word	31913
	.byte	28
	.byte	'Ifx_SRC_FFT',0,4,192,1,3
	.word	31973
	.byte	18,16
	.word	527
	.byte	19,15,0,10
	.byte	'_Ifx_SRC_GPSR',0,4,195,1,25,32,13
	.byte	'SR0',0
	.word	834
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	834
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	834
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	834
	.byte	4,2,35,12,13
	.byte	'reserved_10',0
	.word	31999
	.byte	16,2,35,16,0,14
	.word	32008
	.byte	28
	.byte	'Ifx_SRC_GPSR',0,4,202,1,3
	.word	32102
	.byte	10
	.byte	'_Ifx_SRC_GPT12',0,4,205,1,25,48,13
	.byte	'CIRQ',0
	.word	834
	.byte	4,2,35,0,13
	.byte	'T2',0
	.word	834
	.byte	4,2,35,4,13
	.byte	'T3',0
	.word	834
	.byte	4,2,35,8,13
	.byte	'T4',0
	.word	834
	.byte	4,2,35,12,13
	.byte	'T5',0
	.word	834
	.byte	4,2,35,16,13
	.byte	'T6',0
	.word	834
	.byte	4,2,35,20,13
	.byte	'reserved_18',0
	.word	4628
	.byte	24,2,35,24,0,14
	.word	32129
	.byte	28
	.byte	'Ifx_SRC_GPT12',0,4,214,1,3
	.word	32246
	.byte	18,12
	.word	834
	.byte	19,2,0,18,32
	.word	834
	.byte	19,7,0,18,32
	.word	32283
	.byte	19,0,0,18,88
	.word	527
	.byte	19,87,0,18,108
	.word	834
	.byte	19,26,0,18,96
	.word	527
	.byte	19,95,0,18,96
	.word	32283
	.byte	19,2,0,18,160,3
	.word	527
	.byte	19,159,3,0,18,64
	.word	32283
	.byte	19,1,0,18,192,3
	.word	527
	.byte	19,191,3,0,18,16
	.word	834
	.byte	19,3,0,18,64
	.word	32368
	.byte	19,3,0,18,192,2
	.word	527
	.byte	19,191,2,0,10
	.byte	'_Ifx_SRC_GTM',0,4,217,1,25,204,18,13
	.byte	'AEIIRQ',0
	.word	834
	.byte	4,2,35,0,13
	.byte	'ARUIRQ',0
	.word	32274
	.byte	12,2,35,4,13
	.byte	'reserved_10',0
	.word	3438
	.byte	4,2,35,16,13
	.byte	'BRCIRQ',0
	.word	834
	.byte	4,2,35,20,13
	.byte	'CMPIRQ',0
	.word	834
	.byte	4,2,35,24,13
	.byte	'SPEIRQ',0
	.word	31160
	.byte	8,2,35,28,13
	.byte	'reserved_24',0
	.word	5257
	.byte	8,2,35,36,13
	.byte	'PSM',0
	.word	32292
	.byte	32,2,35,44,13
	.byte	'reserved_4C',0
	.word	32301
	.byte	88,2,35,76,13
	.byte	'DPLL',0
	.word	32310
	.byte	108,3,35,164,1,13
	.byte	'reserved_110',0
	.word	32319
	.byte	96,3,35,144,2,13
	.byte	'ERR',0
	.word	834
	.byte	4,3,35,240,2,13
	.byte	'reserved_174',0
	.word	5597
	.byte	12,3,35,244,2,13
	.byte	'TIM',0
	.word	32328
	.byte	96,3,35,128,3,13
	.byte	'reserved_1E0',0
	.word	32337
	.byte	160,3,3,35,224,3,13
	.byte	'MCS',0
	.word	32328
	.byte	96,3,35,128,7,13
	.byte	'reserved_3E0',0
	.word	32337
	.byte	160,3,3,35,224,7,13
	.byte	'TOM',0
	.word	32348
	.byte	64,3,35,128,11,13
	.byte	'reserved_5C0',0
	.word	32357
	.byte	192,3,3,35,192,11,13
	.byte	'ATOM',0
	.word	32377
	.byte	64,3,35,128,15,13
	.byte	'reserved_7C0',0
	.word	32386
	.byte	192,2,3,35,192,15,13
	.byte	'MCSW0',0
	.word	32274
	.byte	12,3,35,128,18,13
	.byte	'reserved_90C',0
	.word	18846
	.byte	52,3,35,140,18,13
	.byte	'MCSW1',0
	.word	32274
	.byte	12,3,35,192,18,0,14
	.word	32397
	.byte	28
	.byte	'Ifx_SRC_GTM',0,4,243,1,3
	.word	32857
	.byte	10
	.byte	'_Ifx_SRC_HSCT',0,4,246,1,25,4,13
	.byte	'SR',0
	.word	834
	.byte	4,2,35,0,0,14
	.word	32883
	.byte	28
	.byte	'Ifx_SRC_HSCT',0,4,249,1,3
	.word	32916
	.byte	10
	.byte	'_Ifx_SRC_HSSL',0,4,252,1,25,16,13
	.byte	'COK',0
	.word	834
	.byte	4,2,35,0,13
	.byte	'RDI',0
	.word	834
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	834
	.byte	4,2,35,8,13
	.byte	'TRG',0
	.word	834
	.byte	4,2,35,12,0,14
	.word	32943
	.byte	28
	.byte	'Ifx_SRC_HSSL',0,4,130,2,3
	.word	33016
	.byte	18,56
	.word	527
	.byte	19,55,0,10
	.byte	'_Ifx_SRC_I2C',0,4,133,2,25,80,13
	.byte	'BREQ',0
	.word	834
	.byte	4,2,35,0,13
	.byte	'LBREQ',0
	.word	834
	.byte	4,2,35,4,13
	.byte	'SREQ',0
	.word	834
	.byte	4,2,35,8,13
	.byte	'LSREQ',0
	.word	834
	.byte	4,2,35,12,13
	.byte	'ERR',0
	.word	834
	.byte	4,2,35,16,13
	.byte	'P',0
	.word	834
	.byte	4,2,35,20,13
	.byte	'reserved_18',0
	.word	33043
	.byte	56,2,35,24,0,14
	.word	33052
	.byte	28
	.byte	'Ifx_SRC_I2C',0,4,142,2,3
	.word	33175
	.byte	10
	.byte	'_Ifx_SRC_LMU',0,4,145,2,25,4,13
	.byte	'SR',0
	.word	834
	.byte	4,2,35,0,0,14
	.word	33201
	.byte	28
	.byte	'Ifx_SRC_LMU',0,4,148,2,3
	.word	33233
	.byte	10
	.byte	'_Ifx_SRC_MSC',0,4,151,2,25,20,13
	.byte	'SR0',0
	.word	834
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	834
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	834
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	834
	.byte	4,2,35,12,13
	.byte	'SR4',0
	.word	834
	.byte	4,2,35,16,0,14
	.word	33259
	.byte	28
	.byte	'Ifx_SRC_MSC',0,4,158,2,3
	.word	33344
	.byte	10
	.byte	'_Ifx_SRC_PMU',0,4,161,2,25,4,13
	.byte	'SR',0
	.word	834
	.byte	4,2,35,0,0,14
	.word	33370
	.byte	28
	.byte	'Ifx_SRC_PMU',0,4,164,2,3
	.word	33402
	.byte	10
	.byte	'_Ifx_SRC_PSI5',0,4,167,2,25,32,13
	.byte	'SR',0
	.word	32283
	.byte	32,2,35,0,0,14
	.word	33428
	.byte	28
	.byte	'Ifx_SRC_PSI5',0,4,170,2,3
	.word	33461
	.byte	10
	.byte	'_Ifx_SRC_PSI5S',0,4,173,2,25,32,13
	.byte	'SR',0
	.word	32283
	.byte	32,2,35,0,0,14
	.word	33488
	.byte	28
	.byte	'Ifx_SRC_PSI5S',0,4,176,2,3
	.word	33522
	.byte	10
	.byte	'_Ifx_SRC_QSPI',0,4,179,2,25,24,13
	.byte	'TX',0
	.word	834
	.byte	4,2,35,0,13
	.byte	'RX',0
	.word	834
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	834
	.byte	4,2,35,8,13
	.byte	'PT',0
	.word	834
	.byte	4,2,35,12,13
	.byte	'HC',0
	.word	834
	.byte	4,2,35,16,13
	.byte	'U',0
	.word	834
	.byte	4,2,35,20,0,14
	.word	33550
	.byte	28
	.byte	'Ifx_SRC_QSPI',0,4,187,2,3
	.word	33643
	.byte	10
	.byte	'_Ifx_SRC_SCR',0,4,190,2,25,4,13
	.byte	'SR',0
	.word	834
	.byte	4,2,35,0,0,14
	.word	33670
	.byte	28
	.byte	'Ifx_SRC_SCR',0,4,193,2,3
	.word	33702
	.byte	10
	.byte	'_Ifx_SRC_SCU',0,4,196,2,25,20,13
	.byte	'DTS',0
	.word	834
	.byte	4,2,35,0,13
	.byte	'ERU',0
	.word	32368
	.byte	16,2,35,4,0,14
	.word	33728
	.byte	28
	.byte	'Ifx_SRC_SCU',0,4,200,2,3
	.word	33774
	.byte	18,24
	.word	834
	.byte	19,5,0,10
	.byte	'_Ifx_SRC_SENT',0,4,203,2,25,24,13
	.byte	'SR',0
	.word	33800
	.byte	24,2,35,0,0,14
	.word	33809
	.byte	28
	.byte	'Ifx_SRC_SENT',0,4,206,2,3
	.word	33842
	.byte	10
	.byte	'_Ifx_SRC_SMU',0,4,209,2,25,12,13
	.byte	'SR',0
	.word	32274
	.byte	12,2,35,0,0,14
	.word	33869
	.byte	28
	.byte	'Ifx_SRC_SMU',0,4,212,2,3
	.word	33901
	.byte	10
	.byte	'_Ifx_SRC_STM',0,4,215,2,25,8,13
	.byte	'SR0',0
	.word	834
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	834
	.byte	4,2,35,4,0,14
	.word	33927
	.byte	28
	.byte	'Ifx_SRC_STM',0,4,219,2,3
	.word	33973
	.byte	10
	.byte	'_Ifx_SRC_VADCCG',0,4,222,2,25,16,13
	.byte	'SR0',0
	.word	834
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	834
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	834
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	834
	.byte	4,2,35,12,0,14
	.word	33999
	.byte	28
	.byte	'Ifx_SRC_VADCCG',0,4,228,2,3
	.word	34074
	.byte	10
	.byte	'_Ifx_SRC_VADCG',0,4,231,2,25,16,13
	.byte	'SR0',0
	.word	834
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	834
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	834
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	834
	.byte	4,2,35,12,0,14
	.word	34103
	.byte	28
	.byte	'Ifx_SRC_VADCG',0,4,237,2,3
	.word	34177
	.byte	10
	.byte	'_Ifx_SRC_XBAR',0,4,240,2,25,4,13
	.byte	'SRC',0
	.word	834
	.byte	4,2,35,0,0,14
	.word	34205
	.byte	28
	.byte	'Ifx_SRC_XBAR',0,4,243,2,3
	.word	34239
	.byte	18,4
	.word	30786
	.byte	19,0,0,14
	.word	34266
	.byte	10
	.byte	'_Ifx_SRC_GAGBT',0,4,128,3,25,4,13
	.byte	'AGBT',0
	.word	34275
	.byte	4,2,35,0,0,14
	.word	34280
	.byte	28
	.byte	'Ifx_SRC_GAGBT',0,4,131,3,3
	.word	34316
	.byte	18,48
	.word	30844
	.byte	19,3,0,14
	.word	34344
	.byte	10
	.byte	'_Ifx_SRC_GASCLIN',0,4,134,3,25,48,13
	.byte	'ASCLIN',0
	.word	34353
	.byte	48,2,35,0,0,14
	.word	34358
	.byte	28
	.byte	'Ifx_SRC_GASCLIN',0,4,137,3,3
	.word	34398
	.byte	14
	.word	30931
	.byte	10
	.byte	'_Ifx_SRC_GBCU',0,4,140,3,25,4,13
	.byte	'SPB',0
	.word	34428
	.byte	4,2,35,0,0,14
	.word	34433
	.byte	28
	.byte	'Ifx_SRC_GBCU',0,4,143,3,3
	.word	34467
	.byte	18,64
	.word	31005
	.byte	19,0,0,14
	.word	34494
	.byte	10
	.byte	'_Ifx_SRC_GCAN',0,4,146,3,25,64,13
	.byte	'CAN',0
	.word	34503
	.byte	64,2,35,0,0,14
	.word	34508
	.byte	28
	.byte	'Ifx_SRC_GCAN',0,4,149,3,3
	.word	34542
	.byte	18,32
	.word	31062
	.byte	19,1,0,14
	.word	34569
	.byte	10
	.byte	'_Ifx_SRC_GCCU6',0,4,152,3,25,32,13
	.byte	'CCU6',0
	.word	34578
	.byte	32,2,35,0,0,14
	.word	34583
	.byte	28
	.byte	'Ifx_SRC_GCCU6',0,4,155,3,3
	.word	34619
	.byte	14
	.word	31169
	.byte	10
	.byte	'_Ifx_SRC_GCERBERUS',0,4,158,3,25,8,13
	.byte	'CERBERUS',0
	.word	34647
	.byte	8,2,35,0,0,14
	.word	34652
	.byte	28
	.byte	'Ifx_SRC_GCERBERUS',0,4,161,3,3
	.word	34696
	.byte	18,16
	.word	31235
	.byte	19,0,0,14
	.word	34728
	.byte	10
	.byte	'_Ifx_SRC_GCIF',0,4,164,3,25,16,13
	.byte	'CIF',0
	.word	34737
	.byte	16,2,35,0,0,14
	.word	34742
	.byte	28
	.byte	'Ifx_SRC_GCIF',0,4,167,3,3
	.word	34776
	.byte	18,8
	.word	31334
	.byte	19,1,0,14
	.word	34803
	.byte	10
	.byte	'_Ifx_SRC_GCPU',0,4,170,3,25,8,13
	.byte	'CPU',0
	.word	34812
	.byte	8,2,35,0,0,14
	.word	34817
	.byte	28
	.byte	'Ifx_SRC_GCPU',0,4,173,3,3
	.word	34851
	.byte	18,208,1
	.word	31405
	.byte	19,0,0,14
	.word	34878
	.byte	10
	.byte	'_Ifx_SRC_GDMA',0,4,176,3,25,208,1,13
	.byte	'DMA',0
	.word	34888
	.byte	208,1,2,35,0,0,14
	.word	34893
	.byte	28
	.byte	'Ifx_SRC_GDMA',0,4,179,3,3
	.word	34929
	.byte	14
	.word	31498
	.byte	14
	.word	31498
	.byte	14
	.word	31498
	.byte	10
	.byte	'_Ifx_SRC_GDSADC',0,4,182,3,25,32,13
	.byte	'DSADC0',0
	.word	34956
	.byte	8,2,35,0,13
	.byte	'reserved_8',0
	.word	5257
	.byte	8,2,35,8,13
	.byte	'DSADC2',0
	.word	34961
	.byte	8,2,35,16,13
	.byte	'DSADC3',0
	.word	34966
	.byte	8,2,35,24,0,14
	.word	34971
	.byte	28
	.byte	'Ifx_SRC_GDSADC',0,4,188,3,3
	.word	35062
	.byte	18,4
	.word	31574
	.byte	19,0,0,14
	.word	35091
	.byte	10
	.byte	'_Ifx_SRC_GEMEM',0,4,191,3,25,4,13
	.byte	'EMEM',0
	.word	35100
	.byte	4,2,35,0,0,14
	.word	35105
	.byte	28
	.byte	'Ifx_SRC_GEMEM',0,4,194,3,3
	.word	35141
	.byte	18,80
	.word	31643
	.byte	19,0,0,14
	.word	35169
	.byte	10
	.byte	'_Ifx_SRC_GERAY',0,4,197,3,25,80,13
	.byte	'ERAY',0
	.word	35178
	.byte	80,2,35,0,0,14
	.word	35183
	.byte	28
	.byte	'Ifx_SRC_GERAY',0,4,200,3,3
	.word	35219
	.byte	18,4
	.word	31797
	.byte	19,0,0,14
	.word	35247
	.byte	10
	.byte	'_Ifx_SRC_GETH',0,4,203,3,25,4,13
	.byte	'ETH',0
	.word	35256
	.byte	4,2,35,0,0,14
	.word	35261
	.byte	28
	.byte	'Ifx_SRC_GETH',0,4,206,3,3
	.word	35295
	.byte	18,4
	.word	31855
	.byte	19,0,0,14
	.word	35322
	.byte	10
	.byte	'_Ifx_SRC_GFCE',0,4,209,3,25,4,13
	.byte	'FCE',0
	.word	35331
	.byte	4,2,35,0,0,14
	.word	35336
	.byte	28
	.byte	'Ifx_SRC_GFCE',0,4,212,3,3
	.word	35370
	.byte	18,12
	.word	31913
	.byte	19,0,0,14
	.word	35397
	.byte	10
	.byte	'_Ifx_SRC_GFFT',0,4,215,3,25,12,13
	.byte	'FFT',0
	.word	35406
	.byte	12,2,35,0,0,14
	.word	35411
	.byte	28
	.byte	'Ifx_SRC_GFFT',0,4,218,3,3
	.word	35445
	.byte	18,64
	.word	32008
	.byte	19,1,0,14
	.word	35472
	.byte	10
	.byte	'_Ifx_SRC_GGPSR',0,4,221,3,25,64,13
	.byte	'GPSR',0
	.word	35481
	.byte	64,2,35,0,0,14
	.word	35486
	.byte	28
	.byte	'Ifx_SRC_GGPSR',0,4,224,3,3
	.word	35522
	.byte	18,48
	.word	32129
	.byte	19,0,0,14
	.word	35550
	.byte	10
	.byte	'_Ifx_SRC_GGPT12',0,4,227,3,25,48,13
	.byte	'GPT12',0
	.word	35559
	.byte	48,2,35,0,0,14
	.word	35564
	.byte	28
	.byte	'Ifx_SRC_GGPT12',0,4,230,3,3
	.word	35602
	.byte	18,204,18
	.word	32397
	.byte	19,0,0,14
	.word	35631
	.byte	10
	.byte	'_Ifx_SRC_GGTM',0,4,233,3,25,204,18,13
	.byte	'GTM',0
	.word	35641
	.byte	204,18,2,35,0,0,14
	.word	35646
	.byte	28
	.byte	'Ifx_SRC_GGTM',0,4,236,3,3
	.word	35682
	.byte	18,4
	.word	32883
	.byte	19,0,0,14
	.word	35709
	.byte	10
	.byte	'_Ifx_SRC_GHSCT',0,4,239,3,25,4,13
	.byte	'HSCT',0
	.word	35718
	.byte	4,2,35,0,0,14
	.word	35723
	.byte	28
	.byte	'Ifx_SRC_GHSCT',0,4,242,3,3
	.word	35759
	.byte	18,64
	.word	32943
	.byte	19,3,0,14
	.word	35787
	.byte	10
	.byte	'_Ifx_SRC_GHSSL',0,4,245,3,25,68,13
	.byte	'HSSL',0
	.word	35796
	.byte	64,2,35,0,13
	.byte	'EXI',0
	.word	834
	.byte	4,2,35,64,0,14
	.word	35801
	.byte	28
	.byte	'Ifx_SRC_GHSSL',0,4,249,3,3
	.word	35850
	.byte	18,80
	.word	33052
	.byte	19,0,0,14
	.word	35878
	.byte	10
	.byte	'_Ifx_SRC_GI2C',0,4,252,3,25,80,13
	.byte	'I2C',0
	.word	35887
	.byte	80,2,35,0,0,14
	.word	35892
	.byte	28
	.byte	'Ifx_SRC_GI2C',0,4,255,3,3
	.word	35926
	.byte	18,4
	.word	33201
	.byte	19,0,0,14
	.word	35953
	.byte	10
	.byte	'_Ifx_SRC_GLMU',0,4,130,4,25,4,13
	.byte	'LMU',0
	.word	35962
	.byte	4,2,35,0,0,14
	.word	35967
	.byte	28
	.byte	'Ifx_SRC_GLMU',0,4,133,4,3
	.word	36001
	.byte	18,40
	.word	33259
	.byte	19,1,0,14
	.word	36028
	.byte	10
	.byte	'_Ifx_SRC_GMSC',0,4,136,4,25,40,13
	.byte	'MSC',0
	.word	36037
	.byte	40,2,35,0,0,14
	.word	36042
	.byte	28
	.byte	'Ifx_SRC_GMSC',0,4,139,4,3
	.word	36076
	.byte	18,8
	.word	33370
	.byte	19,1,0,14
	.word	36103
	.byte	10
	.byte	'_Ifx_SRC_GPMU',0,4,142,4,25,8,13
	.byte	'PMU',0
	.word	36112
	.byte	8,2,35,0,0,14
	.word	36117
	.byte	28
	.byte	'Ifx_SRC_GPMU',0,4,145,4,3
	.word	36151
	.byte	18,32
	.word	33428
	.byte	19,0,0,14
	.word	36178
	.byte	10
	.byte	'_Ifx_SRC_GPSI5',0,4,148,4,25,32,13
	.byte	'PSI5',0
	.word	36187
	.byte	32,2,35,0,0,14
	.word	36192
	.byte	28
	.byte	'Ifx_SRC_GPSI5',0,4,151,4,3
	.word	36228
	.byte	18,32
	.word	33488
	.byte	19,0,0,14
	.word	36256
	.byte	10
	.byte	'_Ifx_SRC_GPSI5S',0,4,154,4,25,32,13
	.byte	'PSI5S',0
	.word	36265
	.byte	32,2,35,0,0,14
	.word	36270
	.byte	28
	.byte	'Ifx_SRC_GPSI5S',0,4,157,4,3
	.word	36308
	.byte	18,96
	.word	33550
	.byte	19,3,0,14
	.word	36337
	.byte	10
	.byte	'_Ifx_SRC_GQSPI',0,4,160,4,25,96,13
	.byte	'QSPI',0
	.word	36346
	.byte	96,2,35,0,0,14
	.word	36351
	.byte	28
	.byte	'Ifx_SRC_GQSPI',0,4,163,4,3
	.word	36387
	.byte	18,4
	.word	33670
	.byte	19,0,0,14
	.word	36415
	.byte	10
	.byte	'_Ifx_SRC_GSCR',0,4,166,4,25,4,13
	.byte	'SCR',0
	.word	36424
	.byte	4,2,35,0,0,14
	.word	36429
	.byte	28
	.byte	'Ifx_SRC_GSCR',0,4,169,4,3
	.word	36463
	.byte	14
	.word	33728
	.byte	10
	.byte	'_Ifx_SRC_GSCU',0,4,172,4,25,20,13
	.byte	'SCU',0
	.word	36490
	.byte	20,2,35,0,0,14
	.word	36495
	.byte	28
	.byte	'Ifx_SRC_GSCU',0,4,175,4,3
	.word	36529
	.byte	18,24
	.word	33809
	.byte	19,0,0,14
	.word	36556
	.byte	10
	.byte	'_Ifx_SRC_GSENT',0,4,178,4,25,24,13
	.byte	'SENT',0
	.word	36565
	.byte	24,2,35,0,0,14
	.word	36570
	.byte	28
	.byte	'Ifx_SRC_GSENT',0,4,181,4,3
	.word	36606
	.byte	18,12
	.word	33869
	.byte	19,0,0,14
	.word	36634
	.byte	10
	.byte	'_Ifx_SRC_GSMU',0,4,184,4,25,12,13
	.byte	'SMU',0
	.word	36643
	.byte	12,2,35,0,0,14
	.word	36648
	.byte	28
	.byte	'Ifx_SRC_GSMU',0,4,187,4,3
	.word	36682
	.byte	18,16
	.word	33927
	.byte	19,1,0,14
	.word	36709
	.byte	10
	.byte	'_Ifx_SRC_GSTM',0,4,190,4,25,16,13
	.byte	'STM',0
	.word	36718
	.byte	16,2,35,0,0,14
	.word	36723
	.byte	28
	.byte	'Ifx_SRC_GSTM',0,4,193,4,3
	.word	36757
	.byte	18,64
	.word	34103
	.byte	19,3,0,14
	.word	36784
	.byte	18,224,1
	.word	527
	.byte	19,223,1,0,18,32
	.word	33999
	.byte	19,1,0,14
	.word	36809
	.byte	10
	.byte	'_Ifx_SRC_GVADC',0,4,196,4,25,192,2,13
	.byte	'G',0
	.word	36793
	.byte	64,2,35,0,13
	.byte	'reserved_40',0
	.word	36798
	.byte	224,1,2,35,64,13
	.byte	'CG',0
	.word	36818
	.byte	32,3,35,160,2,0,14
	.word	36823
	.byte	28
	.byte	'Ifx_SRC_GVADC',0,4,201,4,3
	.word	36892
	.byte	14
	.word	34205
	.byte	10
	.byte	'_Ifx_SRC_GXBAR',0,4,204,4,25,4,13
	.byte	'XBAR',0
	.word	36920
	.byte	4,2,35,0,0,14
	.word	36925
	.byte	28
	.byte	'Ifx_SRC_GXBAR',0,4,207,4,3
	.word	36961
	.byte	15,20,236,10,9,1,16
	.byte	'IfxScu_CCUCON0_CLKSEL_fBack',0,0,16
	.byte	'IfxScu_CCUCON0_CLKSEL_fPll',0,1,0,28
	.byte	'IfxScu_CCUCON0_CLKSEL',0,20,240,10,3
	.word	36989
	.byte	15,20,250,10,9,1,16
	.byte	'IfxScu_WDTCON1_IR_divBy16384',0,0,16
	.byte	'IfxScu_WDTCON1_IR_divBy256',0,1,16
	.byte	'IfxScu_WDTCON1_IR_divBy64',0,2,0,28
	.byte	'IfxScu_WDTCON1_IR',0,20,255,10,3
	.word	37086
	.byte	10
	.byte	'_Ifx_SCU_ACCEN0_Bits',0,7,45,16,4,11
	.byte	'EN0',0,1
	.word	527
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	527
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	527
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	527
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	527
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	527
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	527
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	527
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	527
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	527
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	527
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	527
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	527
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	527
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	527
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	527
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	527
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	527
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	527
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	527
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	527
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	527
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	527
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	527
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	527
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	527
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	527
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	527
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	527
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	527
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	527
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	527
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_ACCEN0_Bits',0,7,79,3
	.word	37208
	.byte	10
	.byte	'_Ifx_SCU_ACCEN1_Bits',0,7,82,16,4,11
	.byte	'reserved_0',0,4
	.word	504
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_SCU_ACCEN1_Bits',0,7,85,3
	.word	37765
	.byte	10
	.byte	'_Ifx_SCU_ARSTDIS_Bits',0,7,88,16,4,11
	.byte	'STM0DIS',0,1
	.word	527
	.byte	1,7,2,35,0,11
	.byte	'STM1DIS',0,1
	.word	527
	.byte	1,6,2,35,0,11
	.byte	'STM2DIS',0,1
	.word	527
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	504
	.byte	29,0,2,35,0,0,28
	.byte	'Ifx_SCU_ARSTDIS_Bits',0,7,94,3
	.word	37842
	.byte	10
	.byte	'_Ifx_SCU_CCUCON0_Bits',0,7,97,16,4,11
	.byte	'BAUD1DIV',0,1
	.word	527
	.byte	4,4,2,35,0,11
	.byte	'BAUD2DIV',0,1
	.word	527
	.byte	4,0,2,35,0,11
	.byte	'SRIDIV',0,1
	.word	527
	.byte	4,4,2,35,1,11
	.byte	'LPDIV',0,1
	.word	527
	.byte	4,0,2,35,1,11
	.byte	'SPBDIV',0,1
	.word	527
	.byte	4,4,2,35,2,11
	.byte	'FSI2DIV',0,1
	.word	527
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	527
	.byte	2,0,2,35,2,11
	.byte	'FSIDIV',0,1
	.word	527
	.byte	2,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	527
	.byte	2,4,2,35,3,11
	.byte	'CLKSEL',0,1
	.word	527
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	527
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	527
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_CCUCON0_Bits',0,7,111,3
	.word	37978
	.byte	10
	.byte	'_Ifx_SCU_CCUCON1_Bits',0,7,114,16,4,11
	.byte	'CANDIV',0,1
	.word	527
	.byte	4,4,2,35,0,11
	.byte	'ERAYDIV',0,1
	.word	527
	.byte	4,0,2,35,0,11
	.byte	'STMDIV',0,1
	.word	527
	.byte	4,4,2,35,1,11
	.byte	'GTMDIV',0,1
	.word	527
	.byte	4,0,2,35,1,11
	.byte	'ETHDIV',0,1
	.word	527
	.byte	4,4,2,35,2,11
	.byte	'ASCLINFDIV',0,1
	.word	527
	.byte	4,0,2,35,2,11
	.byte	'ASCLINSDIV',0,1
	.word	527
	.byte	4,4,2,35,3,11
	.byte	'INSEL',0,1
	.word	527
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	527
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	527
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_CCUCON1_Bits',0,7,126,3
	.word	38258
	.byte	10
	.byte	'_Ifx_SCU_CCUCON2_Bits',0,7,129,1,16,4,11
	.byte	'BBBDIV',0,1
	.word	527
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	504
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	527
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	527
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_CCUCON2_Bits',0,7,135,1,3
	.word	38496
	.byte	10
	.byte	'_Ifx_SCU_CCUCON3_Bits',0,7,138,1,16,4,11
	.byte	'PLLDIV',0,1
	.word	527
	.byte	6,2,2,35,0,11
	.byte	'PLLSEL',0,1
	.word	527
	.byte	2,0,2,35,0,11
	.byte	'PLLERAYDIV',0,1
	.word	527
	.byte	6,2,2,35,1,11
	.byte	'PLLERAYSEL',0,1
	.word	527
	.byte	2,0,2,35,1,11
	.byte	'SRIDIV',0,1
	.word	527
	.byte	6,2,2,35,2,11
	.byte	'SRISEL',0,1
	.word	527
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	527
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	527
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	527
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	527
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_CCUCON3_Bits',0,7,150,1,3
	.word	38624
	.byte	10
	.byte	'_Ifx_SCU_CCUCON4_Bits',0,7,153,1,16,4,11
	.byte	'SPBDIV',0,1
	.word	527
	.byte	6,2,2,35,0,11
	.byte	'SPBSEL',0,1
	.word	527
	.byte	2,0,2,35,0,11
	.byte	'GTMDIV',0,1
	.word	527
	.byte	6,2,2,35,1,11
	.byte	'GTMSEL',0,1
	.word	527
	.byte	2,0,2,35,1,11
	.byte	'STMDIV',0,1
	.word	527
	.byte	6,2,2,35,2,11
	.byte	'STMSEL',0,1
	.word	527
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	527
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	527
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	527
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	527
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_CCUCON4_Bits',0,7,165,1,3
	.word	38867
	.byte	10
	.byte	'_Ifx_SCU_CCUCON5_Bits',0,7,168,1,16,4,11
	.byte	'MAXDIV',0,1
	.word	527
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	504
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	527
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	527
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_CCUCON5_Bits',0,7,174,1,3
	.word	39102
	.byte	10
	.byte	'_Ifx_SCU_CCUCON6_Bits',0,7,177,1,16,4,11
	.byte	'CPU0DIV',0,1
	.word	527
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	504
	.byte	26,0,2,35,0,0,28
	.byte	'Ifx_SCU_CCUCON6_Bits',0,7,181,1,3
	.word	39230
	.byte	10
	.byte	'_Ifx_SCU_CCUCON7_Bits',0,7,184,1,16,4,11
	.byte	'CPU1DIV',0,1
	.word	527
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	504
	.byte	26,0,2,35,0,0,28
	.byte	'Ifx_SCU_CCUCON7_Bits',0,7,188,1,3
	.word	39330
	.byte	10
	.byte	'_Ifx_SCU_CHIPID_Bits',0,7,191,1,16,4,11
	.byte	'CHREV',0,1
	.word	527
	.byte	6,2,2,35,0,11
	.byte	'CHTEC',0,1
	.word	527
	.byte	2,0,2,35,0,11
	.byte	'CHID',0,1
	.word	527
	.byte	8,0,2,35,1,11
	.byte	'EEA',0,1
	.word	527
	.byte	1,7,2,35,2,11
	.byte	'UCODE',0,1
	.word	527
	.byte	7,0,2,35,2,11
	.byte	'FSIZE',0,1
	.word	527
	.byte	4,4,2,35,3,11
	.byte	'SP',0,1
	.word	527
	.byte	2,2,2,35,3,11
	.byte	'SEC',0,1
	.word	527
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	527
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_CHIPID_Bits',0,7,202,1,3
	.word	39430
	.byte	10
	.byte	'_Ifx_SCU_DTSCON_Bits',0,7,205,1,16,4,11
	.byte	'PWD',0,1
	.word	527
	.byte	1,7,2,35,0,11
	.byte	'START',0,1
	.word	527
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	527
	.byte	2,4,2,35,0,11
	.byte	'CAL',0,4
	.word	504
	.byte	20,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	527
	.byte	7,1,2,35,3,11
	.byte	'SLCK',0,1
	.word	527
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_DTSCON_Bits',0,7,213,1,3
	.word	39638
	.byte	10
	.byte	'_Ifx_SCU_DTSLIM_Bits',0,7,216,1,16,4,11
	.byte	'LOWER',0,2
	.word	1022
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	527
	.byte	5,1,2,35,1,11
	.byte	'LLU',0,1
	.word	527
	.byte	1,0,2,35,1,11
	.byte	'UPPER',0,2
	.word	1022
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	527
	.byte	4,2,2,35,3,11
	.byte	'SLCK',0,1
	.word	527
	.byte	1,1,2,35,3,11
	.byte	'UOF',0,1
	.word	527
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_DTSLIM_Bits',0,7,225,1,3
	.word	39803
	.byte	10
	.byte	'_Ifx_SCU_DTSSTAT_Bits',0,7,228,1,16,4,11
	.byte	'RESULT',0,2
	.word	1022
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	527
	.byte	4,2,2,35,1,11
	.byte	'RDY',0,1
	.word	527
	.byte	1,1,2,35,1,11
	.byte	'BUSY',0,1
	.word	527
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1022
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_SCU_DTSSTAT_Bits',0,7,235,1,3
	.word	39986
	.byte	10
	.byte	'_Ifx_SCU_EICR_Bits',0,7,238,1,16,4,11
	.byte	'reserved_0',0,1
	.word	527
	.byte	4,4,2,35,0,11
	.byte	'EXIS0',0,1
	.word	527
	.byte	3,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	527
	.byte	1,0,2,35,0,11
	.byte	'FEN0',0,1
	.word	527
	.byte	1,7,2,35,1,11
	.byte	'REN0',0,1
	.word	527
	.byte	1,6,2,35,1,11
	.byte	'LDEN0',0,1
	.word	527
	.byte	1,5,2,35,1,11
	.byte	'EIEN0',0,1
	.word	527
	.byte	1,4,2,35,1,11
	.byte	'INP0',0,1
	.word	527
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	504
	.byte	5,12,2,35,0,11
	.byte	'EXIS1',0,1
	.word	527
	.byte	3,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	527
	.byte	1,0,2,35,2,11
	.byte	'FEN1',0,1
	.word	527
	.byte	1,7,2,35,3,11
	.byte	'REN1',0,1
	.word	527
	.byte	1,6,2,35,3,11
	.byte	'LDEN1',0,1
	.word	527
	.byte	1,5,2,35,3,11
	.byte	'EIEN1',0,1
	.word	527
	.byte	1,4,2,35,3,11
	.byte	'INP1',0,1
	.word	527
	.byte	3,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	527
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EICR_Bits',0,7,129,2,3
	.word	40140
	.byte	10
	.byte	'_Ifx_SCU_EIFR_Bits',0,7,132,2,16,4,11
	.byte	'INTF0',0,1
	.word	527
	.byte	1,7,2,35,0,11
	.byte	'INTF1',0,1
	.word	527
	.byte	1,6,2,35,0,11
	.byte	'INTF2',0,1
	.word	527
	.byte	1,5,2,35,0,11
	.byte	'INTF3',0,1
	.word	527
	.byte	1,4,2,35,0,11
	.byte	'INTF4',0,1
	.word	527
	.byte	1,3,2,35,0,11
	.byte	'INTF5',0,1
	.word	527
	.byte	1,2,2,35,0,11
	.byte	'INTF6',0,1
	.word	527
	.byte	1,1,2,35,0,11
	.byte	'INTF7',0,1
	.word	527
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	504
	.byte	24,0,2,35,0,0,28
	.byte	'Ifx_SCU_EIFR_Bits',0,7,143,2,3
	.word	40504
	.byte	10
	.byte	'_Ifx_SCU_EMSR_Bits',0,7,146,2,16,4,11
	.byte	'POL',0,1
	.word	527
	.byte	1,7,2,35,0,11
	.byte	'MODE',0,1
	.word	527
	.byte	1,6,2,35,0,11
	.byte	'ENON',0,1
	.word	527
	.byte	1,5,2,35,0,11
	.byte	'PSEL',0,1
	.word	527
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	1022
	.byte	12,0,2,35,0,11
	.byte	'EMSF',0,1
	.word	527
	.byte	1,7,2,35,2,11
	.byte	'SEMSF',0,1
	.word	527
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	527
	.byte	6,0,2,35,2,11
	.byte	'EMSFM',0,1
	.word	527
	.byte	2,6,2,35,3,11
	.byte	'SEMSFM',0,1
	.word	527
	.byte	2,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	527
	.byte	4,0,2,35,3,0,28
	.byte	'Ifx_SCU_EMSR_Bits',0,7,159,2,3
	.word	40715
	.byte	10
	.byte	'_Ifx_SCU_ESRCFG_Bits',0,7,162,2,16,4,11
	.byte	'reserved_0',0,1
	.word	527
	.byte	7,1,2,35,0,11
	.byte	'EDCON',0,2
	.word	1022
	.byte	2,7,2,35,0,11
	.byte	'reserved_9',0,4
	.word	504
	.byte	23,0,2,35,0,0,28
	.byte	'Ifx_SCU_ESRCFG_Bits',0,7,167,2,3
	.word	40967
	.byte	10
	.byte	'_Ifx_SCU_ESROCFG_Bits',0,7,170,2,16,4,11
	.byte	'ARI',0,1
	.word	527
	.byte	1,7,2,35,0,11
	.byte	'ARC',0,1
	.word	527
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	504
	.byte	30,0,2,35,0,0,28
	.byte	'Ifx_SCU_ESROCFG_Bits',0,7,175,2,3
	.word	41085
	.byte	10
	.byte	'_Ifx_SCU_EVR13CON_Bits',0,7,178,2,16,4,11
	.byte	'reserved_0',0,4
	.word	504
	.byte	28,4,2,35,0,11
	.byte	'EVR13OFF',0,1
	.word	527
	.byte	1,3,2,35,3,11
	.byte	'BPEVR13OFF',0,1
	.word	527
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	527
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	527
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVR13CON_Bits',0,7,185,2,3
	.word	41196
	.byte	10
	.byte	'_Ifx_SCU_EVR33CON_Bits',0,7,188,2,16,4,11
	.byte	'reserved_0',0,4
	.word	504
	.byte	28,4,2,35,0,11
	.byte	'EVR33OFF',0,1
	.word	527
	.byte	1,3,2,35,3,11
	.byte	'BPEVR33OFF',0,1
	.word	527
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	527
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	527
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVR33CON_Bits',0,7,195,2,3
	.word	41359
	.byte	10
	.byte	'_Ifx_SCU_EVRADCSTAT_Bits',0,7,198,2,16,4,11
	.byte	'ADC13V',0,1
	.word	527
	.byte	8,0,2,35,0,11
	.byte	'ADC33V',0,1
	.word	527
	.byte	8,0,2,35,1,11
	.byte	'ADCSWDV',0,1
	.word	527
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	527
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	527
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVRADCSTAT_Bits',0,7,205,2,3
	.word	41522
	.byte	10
	.byte	'_Ifx_SCU_EVRDVSTAT_Bits',0,7,208,2,16,4,11
	.byte	'DVS13TRIM',0,1
	.word	527
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	527
	.byte	8,0,2,35,1,11
	.byte	'DVS33TRIM',0,1
	.word	527
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	527
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	527
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVRDVSTAT_Bits',0,7,215,2,3
	.word	41680
	.byte	10
	.byte	'_Ifx_SCU_EVRMONCTRL_Bits',0,7,218,2,16,4,11
	.byte	'EVR13OVMOD',0,1
	.word	527
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	527
	.byte	2,4,2,35,0,11
	.byte	'EVR13UVMOD',0,1
	.word	527
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	527
	.byte	2,0,2,35,0,11
	.byte	'EVR33OVMOD',0,1
	.word	527
	.byte	2,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	527
	.byte	2,4,2,35,1,11
	.byte	'EVR33UVMOD',0,1
	.word	527
	.byte	2,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	527
	.byte	2,0,2,35,1,11
	.byte	'SWDOVMOD',0,1
	.word	527
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	527
	.byte	2,4,2,35,2,11
	.byte	'SWDUVMOD',0,1
	.word	527
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,2
	.word	1022
	.byte	10,0,2,35,2,0,28
	.byte	'Ifx_SCU_EVRMONCTRL_Bits',0,7,232,2,3
	.word	41845
	.byte	10
	.byte	'_Ifx_SCU_EVROSCCTRL_Bits',0,7,235,2,16,4,11
	.byte	'OSCTRIM',0,2
	.word	1022
	.byte	10,6,2,35,0,11
	.byte	'OSCPTAT',0,1
	.word	527
	.byte	6,0,2,35,1,11
	.byte	'OSCANASEL',0,1
	.word	527
	.byte	4,4,2,35,2,11
	.byte	'HPBGTRIM',0,2
	.word	1022
	.byte	7,5,2,35,2,11
	.byte	'HPBGCLKEN',0,1
	.word	527
	.byte	1,4,2,35,3,11
	.byte	'OSC3V3',0,1
	.word	527
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	527
	.byte	2,1,2,35,3,11
	.byte	'LCK',0,1
	.word	527
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVROSCCTRL_Bits',0,7,245,2,3
	.word	42174
	.byte	10
	.byte	'_Ifx_SCU_EVROVMON_Bits',0,7,248,2,16,4,11
	.byte	'EVR13OVVAL',0,1
	.word	527
	.byte	8,0,2,35,0,11
	.byte	'EVR33OVVAL',0,1
	.word	527
	.byte	8,0,2,35,1,11
	.byte	'SWDOVVAL',0,1
	.word	527
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	527
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	527
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVROVMON_Bits',0,7,255,2,3
	.word	42395
	.byte	10
	.byte	'_Ifx_SCU_EVRRSTCON_Bits',0,7,130,3,16,4,11
	.byte	'RST13TRIM',0,1
	.word	527
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	504
	.byte	16,8,2,35,0,11
	.byte	'RST13OFF',0,1
	.word	527
	.byte	1,7,2,35,3,11
	.byte	'BPRST13OFF',0,1
	.word	527
	.byte	1,6,2,35,3,11
	.byte	'RST33OFF',0,1
	.word	527
	.byte	1,5,2,35,3,11
	.byte	'BPRST33OFF',0,1
	.word	527
	.byte	1,4,2,35,3,11
	.byte	'RSTSWDOFF',0,1
	.word	527
	.byte	1,3,2,35,3,11
	.byte	'BPRSTSWDOFF',0,1
	.word	527
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	527
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	527
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVRRSTCON_Bits',0,7,142,3,3
	.word	42558
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF1_Bits',0,7,145,3,16,4,11
	.byte	'SD5P',0,1
	.word	527
	.byte	8,0,2,35,0,11
	.byte	'SD5I',0,1
	.word	527
	.byte	8,0,2,35,1,11
	.byte	'SD5D',0,1
	.word	527
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	527
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	527
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVRSDCOEFF1_Bits',0,7,152,3,3
	.word	42830
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF2_Bits',0,7,155,3,16,4,11
	.byte	'SD33P',0,1
	.word	527
	.byte	8,0,2,35,0,11
	.byte	'SD33I',0,1
	.word	527
	.byte	8,0,2,35,1,11
	.byte	'SD33D',0,1
	.word	527
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	527
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	527
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVRSDCOEFF2_Bits',0,7,162,3,3
	.word	42983
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF3_Bits',0,7,165,3,16,4,11
	.byte	'CT5REG0',0,1
	.word	527
	.byte	8,0,2,35,0,11
	.byte	'CT5REG1',0,1
	.word	527
	.byte	8,0,2,35,1,11
	.byte	'CT5REG2',0,1
	.word	527
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	527
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	527
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVRSDCOEFF3_Bits',0,7,172,3,3
	.word	43139
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF4_Bits',0,7,175,3,16,4,11
	.byte	'CT5REG3',0,1
	.word	527
	.byte	8,0,2,35,0,11
	.byte	'CT5REG4',0,1
	.word	527
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1022
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	527
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVRSDCOEFF4_Bits',0,7,181,3,3
	.word	43301
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF5_Bits',0,7,184,3,16,4,11
	.byte	'CT33REG0',0,1
	.word	527
	.byte	8,0,2,35,0,11
	.byte	'CT33REG1',0,1
	.word	527
	.byte	8,0,2,35,1,11
	.byte	'CT33REG2',0,1
	.word	527
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	527
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	527
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVRSDCOEFF5_Bits',0,7,191,3,3
	.word	43444
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF6_Bits',0,7,194,3,16,4,11
	.byte	'CT33REG3',0,1
	.word	527
	.byte	8,0,2,35,0,11
	.byte	'CT33REG4',0,1
	.word	527
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1022
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	527
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVRSDCOEFF6_Bits',0,7,200,3,3
	.word	43609
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL1_Bits',0,7,203,3,16,4,11
	.byte	'SDFREQSPRD',0,2
	.word	1022
	.byte	16,0,2,35,0,11
	.byte	'SDFREQ',0,1
	.word	527
	.byte	8,0,2,35,2,11
	.byte	'SDSTEP',0,1
	.word	527
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	527
	.byte	2,2,2,35,3,11
	.byte	'SDSAMPLE',0,1
	.word	527
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	527
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVRSDCTRL1_Bits',0,7,211,3,3
	.word	43754
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL2_Bits',0,7,214,3,16,4,11
	.byte	'DRVP',0,1
	.word	527
	.byte	8,0,2,35,0,11
	.byte	'SDMINMAXDC',0,1
	.word	527
	.byte	8,0,2,35,1,11
	.byte	'DRVN',0,1
	.word	527
	.byte	8,0,2,35,2,11
	.byte	'SDLUT',0,1
	.word	527
	.byte	6,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	527
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	527
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVRSDCTRL2_Bits',0,7,222,3,3
	.word	43935
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL3_Bits',0,7,225,3,16,4,11
	.byte	'SDPWMPRE',0,1
	.word	527
	.byte	8,0,2,35,0,11
	.byte	'SDPID',0,1
	.word	527
	.byte	8,0,2,35,1,11
	.byte	'SDVOKLVL',0,1
	.word	527
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	527
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	527
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVRSDCTRL3_Bits',0,7,232,3,3
	.word	44109
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL4_Bits',0,7,235,3,16,4,11
	.byte	'reserved_0',0,1
	.word	527
	.byte	8,0,2,35,0,11
	.byte	'SYNCDIV',0,1
	.word	527
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	504
	.byte	20,1,2,35,0,11
	.byte	'LCK',0,1
	.word	527
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVRSDCTRL4_Bits',0,7,241,3,3
	.word	44269
	.byte	10
	.byte	'_Ifx_SCU_EVRSTAT_Bits',0,7,244,3,16,4,11
	.byte	'EVR13',0,1
	.word	527
	.byte	1,7,2,35,0,11
	.byte	'OV13',0,1
	.word	527
	.byte	1,6,2,35,0,11
	.byte	'EVR33',0,1
	.word	527
	.byte	1,5,2,35,0,11
	.byte	'OV33',0,1
	.word	527
	.byte	1,4,2,35,0,11
	.byte	'OVSWD',0,1
	.word	527
	.byte	1,3,2,35,0,11
	.byte	'UV13',0,1
	.word	527
	.byte	1,2,2,35,0,11
	.byte	'UV33',0,1
	.word	527
	.byte	1,1,2,35,0,11
	.byte	'UVSWD',0,1
	.word	527
	.byte	1,0,2,35,0,11
	.byte	'EXTPASS13',0,1
	.word	527
	.byte	1,7,2,35,1,11
	.byte	'EXTPASS33',0,1
	.word	527
	.byte	1,6,2,35,1,11
	.byte	'BGPROK',0,1
	.word	527
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	504
	.byte	21,0,2,35,0,0,28
	.byte	'Ifx_SCU_EVRSTAT_Bits',0,7,130,4,3
	.word	44413
	.byte	10
	.byte	'_Ifx_SCU_EVRTRIM_Bits',0,7,133,4,16,4,11
	.byte	'EVR13TRIM',0,1
	.word	527
	.byte	8,0,2,35,0,11
	.byte	'SDVOUTSEL',0,1
	.word	527
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1022
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	527
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVRTRIM_Bits',0,7,139,4,3
	.word	44687
	.byte	10
	.byte	'_Ifx_SCU_EVRUVMON_Bits',0,7,142,4,16,4,11
	.byte	'EVR13UVVAL',0,1
	.word	527
	.byte	8,0,2,35,0,11
	.byte	'EVR33UVVAL',0,1
	.word	527
	.byte	8,0,2,35,1,11
	.byte	'SWDUVVAL',0,1
	.word	527
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	527
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	527
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVRUVMON_Bits',0,7,149,4,3
	.word	44826
	.byte	10
	.byte	'_Ifx_SCU_EXTCON_Bits',0,7,152,4,16,4,11
	.byte	'EN0',0,1
	.word	527
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	527
	.byte	1,6,2,35,0,11
	.byte	'SEL0',0,1
	.word	527
	.byte	4,2,2,35,0,11
	.byte	'reserved_6',0,2
	.word	1022
	.byte	10,0,2,35,0,11
	.byte	'EN1',0,1
	.word	527
	.byte	1,7,2,35,2,11
	.byte	'NSEL',0,1
	.word	527
	.byte	1,6,2,35,2,11
	.byte	'SEL1',0,1
	.word	527
	.byte	4,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	527
	.byte	2,0,2,35,2,11
	.byte	'DIV1',0,1
	.word	527
	.byte	8,0,2,35,3,0,28
	.byte	'Ifx_SCU_EXTCON_Bits',0,7,163,4,3
	.word	44989
	.byte	10
	.byte	'_Ifx_SCU_FDR_Bits',0,7,166,4,16,4,11
	.byte	'STEP',0,2
	.word	1022
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	527
	.byte	4,2,2,35,1,11
	.byte	'DM',0,1
	.word	527
	.byte	2,0,2,35,1,11
	.byte	'RESULT',0,2
	.word	1022
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	527
	.byte	5,1,2,35,3,11
	.byte	'DISCLK',0,1
	.word	527
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_FDR_Bits',0,7,174,4,3
	.word	45207
	.byte	10
	.byte	'_Ifx_SCU_FMR_Bits',0,7,177,4,16,4,11
	.byte	'FS0',0,1
	.word	527
	.byte	1,7,2,35,0,11
	.byte	'FS1',0,1
	.word	527
	.byte	1,6,2,35,0,11
	.byte	'FS2',0,1
	.word	527
	.byte	1,5,2,35,0,11
	.byte	'FS3',0,1
	.word	527
	.byte	1,4,2,35,0,11
	.byte	'FS4',0,1
	.word	527
	.byte	1,3,2,35,0,11
	.byte	'FS5',0,1
	.word	527
	.byte	1,2,2,35,0,11
	.byte	'FS6',0,1
	.word	527
	.byte	1,1,2,35,0,11
	.byte	'FS7',0,1
	.word	527
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	527
	.byte	8,0,2,35,1,11
	.byte	'FC0',0,1
	.word	527
	.byte	1,7,2,35,2,11
	.byte	'FC1',0,1
	.word	527
	.byte	1,6,2,35,2,11
	.byte	'FC2',0,1
	.word	527
	.byte	1,5,2,35,2,11
	.byte	'FC3',0,1
	.word	527
	.byte	1,4,2,35,2,11
	.byte	'FC4',0,1
	.word	527
	.byte	1,3,2,35,2,11
	.byte	'FC5',0,1
	.word	527
	.byte	1,2,2,35,2,11
	.byte	'FC6',0,1
	.word	527
	.byte	1,1,2,35,2,11
	.byte	'FC7',0,1
	.word	527
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	527
	.byte	8,0,2,35,3,0,28
	.byte	'Ifx_SCU_FMR_Bits',0,7,197,4,3
	.word	45370
	.byte	10
	.byte	'_Ifx_SCU_ID_Bits',0,7,200,4,16,4,11
	.byte	'MODREV',0,1
	.word	527
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	527
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	1022
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_SCU_ID_Bits',0,7,205,4,3
	.word	45706
	.byte	10
	.byte	'_Ifx_SCU_IGCR_Bits',0,7,208,4,16,4,11
	.byte	'IPEN00',0,1
	.word	527
	.byte	1,7,2,35,0,11
	.byte	'IPEN01',0,1
	.word	527
	.byte	1,6,2,35,0,11
	.byte	'IPEN02',0,1
	.word	527
	.byte	1,5,2,35,0,11
	.byte	'IPEN03',0,1
	.word	527
	.byte	1,4,2,35,0,11
	.byte	'IPEN04',0,1
	.word	527
	.byte	1,3,2,35,0,11
	.byte	'IPEN05',0,1
	.word	527
	.byte	1,2,2,35,0,11
	.byte	'IPEN06',0,1
	.word	527
	.byte	1,1,2,35,0,11
	.byte	'IPEN07',0,1
	.word	527
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	527
	.byte	5,3,2,35,1,11
	.byte	'GEEN0',0,1
	.word	527
	.byte	1,2,2,35,1,11
	.byte	'IGP0',0,1
	.word	527
	.byte	2,0,2,35,1,11
	.byte	'IPEN10',0,1
	.word	527
	.byte	1,7,2,35,2,11
	.byte	'IPEN11',0,1
	.word	527
	.byte	1,6,2,35,2,11
	.byte	'IPEN12',0,1
	.word	527
	.byte	1,5,2,35,2,11
	.byte	'IPEN13',0,1
	.word	527
	.byte	1,4,2,35,2,11
	.byte	'IPEN14',0,1
	.word	527
	.byte	1,3,2,35,2,11
	.byte	'IPEN15',0,1
	.word	527
	.byte	1,2,2,35,2,11
	.byte	'IPEN16',0,1
	.word	527
	.byte	1,1,2,35,2,11
	.byte	'IPEN17',0,1
	.word	527
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	527
	.byte	5,3,2,35,3,11
	.byte	'GEEN1',0,1
	.word	527
	.byte	1,2,2,35,3,11
	.byte	'IGP1',0,1
	.word	527
	.byte	2,0,2,35,3,0,28
	.byte	'Ifx_SCU_IGCR_Bits',0,7,232,4,3
	.word	45813
	.byte	10
	.byte	'_Ifx_SCU_IN_Bits',0,7,235,4,16,4,11
	.byte	'P0',0,1
	.word	527
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	527
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	504
	.byte	30,0,2,35,0,0,28
	.byte	'Ifx_SCU_IN_Bits',0,7,240,4,3
	.word	46265
	.byte	10
	.byte	'_Ifx_SCU_IOCR_Bits',0,7,243,4,16,4,11
	.byte	'reserved_0',0,1
	.word	527
	.byte	4,4,2,35,0,11
	.byte	'PC0',0,1
	.word	527
	.byte	4,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	527
	.byte	4,4,2,35,1,11
	.byte	'PC1',0,1
	.word	527
	.byte	4,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1022
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_SCU_IOCR_Bits',0,7,250,4,3
	.word	46364
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL0_Bits',0,7,253,4,16,4,11
	.byte	'LBISTREQ',0,1
	.word	527
	.byte	1,7,2,35,0,11
	.byte	'LBISTREQP',0,1
	.word	527
	.byte	1,6,2,35,0,11
	.byte	'PATTERNS',0,2
	.word	1022
	.byte	14,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	1022
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_SCU_LBISTCTRL0_Bits',0,7,131,5,3
	.word	46514
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL1_Bits',0,7,134,5,16,4,11
	.byte	'SEED',0,4
	.word	504
	.byte	23,9,2,35,0,11
	.byte	'reserved_23',0,1
	.word	527
	.byte	1,0,2,35,2,11
	.byte	'SPLITSH',0,1
	.word	527
	.byte	3,5,2,35,3,11
	.byte	'BODY',0,1
	.word	527
	.byte	1,4,2,35,3,11
	.byte	'LBISTFREQU',0,1
	.word	527
	.byte	4,0,2,35,3,0,28
	.byte	'Ifx_SCU_LBISTCTRL1_Bits',0,7,141,5,3
	.word	46663
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL2_Bits',0,7,144,5,16,4,11
	.byte	'SIGNATURE',0,4
	.word	504
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	527
	.byte	7,1,2,35,3,11
	.byte	'LBISTDONE',0,1
	.word	527
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_LBISTCTRL2_Bits',0,7,149,5,3
	.word	46824
	.byte	10
	.byte	'_Ifx_SCU_LCLCON_Bits',0,7,152,5,16,4,11
	.byte	'reserved_0',0,2
	.word	1022
	.byte	16,0,2,35,0,11
	.byte	'LS',0,1
	.word	527
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,2
	.word	1022
	.byte	14,1,2,35,2,11
	.byte	'LSEN',0,1
	.word	527
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_LCLCON_Bits',0,7,158,5,3
	.word	46954
	.byte	10
	.byte	'_Ifx_SCU_LCLTEST_Bits',0,7,161,5,16,4,11
	.byte	'LCLT0',0,1
	.word	527
	.byte	1,7,2,35,0,11
	.byte	'LCLT1',0,1
	.word	527
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	504
	.byte	30,0,2,35,0,0,28
	.byte	'Ifx_SCU_LCLTEST_Bits',0,7,166,5,3
	.word	47086
	.byte	10
	.byte	'_Ifx_SCU_MANID_Bits',0,7,169,5,16,4,11
	.byte	'DEPT',0,1
	.word	527
	.byte	5,3,2,35,0,11
	.byte	'MANUF',0,2
	.word	1022
	.byte	11,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	1022
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_SCU_MANID_Bits',0,7,174,5,3
	.word	47201
	.byte	10
	.byte	'_Ifx_SCU_OMR_Bits',0,7,177,5,16,4,11
	.byte	'PS0',0,1
	.word	527
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	527
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	1022
	.byte	14,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	527
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	527
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	1022
	.byte	14,0,2,35,2,0,28
	.byte	'Ifx_SCU_OMR_Bits',0,7,185,5,3
	.word	47312
	.byte	10
	.byte	'_Ifx_SCU_OSCCON_Bits',0,7,188,5,16,4,11
	.byte	'reserved_0',0,1
	.word	527
	.byte	1,7,2,35,0,11
	.byte	'PLLLV',0,1
	.word	527
	.byte	1,6,2,35,0,11
	.byte	'OSCRES',0,1
	.word	527
	.byte	1,5,2,35,0,11
	.byte	'GAINSEL',0,1
	.word	527
	.byte	2,3,2,35,0,11
	.byte	'MODE',0,1
	.word	527
	.byte	2,1,2,35,0,11
	.byte	'SHBY',0,1
	.word	527
	.byte	1,0,2,35,0,11
	.byte	'PLLHV',0,1
	.word	527
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	527
	.byte	1,6,2,35,1,11
	.byte	'X1D',0,1
	.word	527
	.byte	1,5,2,35,1,11
	.byte	'X1DEN',0,1
	.word	527
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	527
	.byte	4,0,2,35,1,11
	.byte	'OSCVAL',0,1
	.word	527
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	527
	.byte	2,1,2,35,2,11
	.byte	'APREN',0,1
	.word	527
	.byte	1,0,2,35,2,11
	.byte	'CAP0EN',0,1
	.word	527
	.byte	1,7,2,35,3,11
	.byte	'CAP1EN',0,1
	.word	527
	.byte	1,6,2,35,3,11
	.byte	'CAP2EN',0,1
	.word	527
	.byte	1,5,2,35,3,11
	.byte	'CAP3EN',0,1
	.word	527
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	527
	.byte	4,0,2,35,3,0,28
	.byte	'Ifx_SCU_OSCCON_Bits',0,7,209,5,3
	.word	47470
	.byte	10
	.byte	'_Ifx_SCU_OUT_Bits',0,7,212,5,16,4,11
	.byte	'P0',0,1
	.word	527
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	527
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	504
	.byte	30,0,2,35,0,0,28
	.byte	'Ifx_SCU_OUT_Bits',0,7,217,5,3
	.word	47882
	.byte	10
	.byte	'_Ifx_SCU_OVCCON_Bits',0,7,220,5,16,4,11
	.byte	'CSEL0',0,1
	.word	527
	.byte	1,7,2,35,0,11
	.byte	'CSEL1',0,1
	.word	527
	.byte	1,6,2,35,0,11
	.byte	'CSEL2',0,1
	.word	527
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,2
	.word	1022
	.byte	13,0,2,35,0,11
	.byte	'OVSTRT',0,1
	.word	527
	.byte	1,7,2,35,2,11
	.byte	'OVSTP',0,1
	.word	527
	.byte	1,6,2,35,2,11
	.byte	'DCINVAL',0,1
	.word	527
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	527
	.byte	5,0,2,35,2,11
	.byte	'OVCONF',0,1
	.word	527
	.byte	1,7,2,35,3,11
	.byte	'POVCONF',0,1
	.word	527
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	527
	.byte	6,0,2,35,3,0,28
	.byte	'Ifx_SCU_OVCCON_Bits',0,7,233,5,3
	.word	47983
	.byte	10
	.byte	'_Ifx_SCU_OVCENABLE_Bits',0,7,236,5,16,4,11
	.byte	'OVEN0',0,1
	.word	527
	.byte	1,7,2,35,0,11
	.byte	'OVEN1',0,1
	.word	527
	.byte	1,6,2,35,0,11
	.byte	'OVEN2',0,1
	.word	527
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	504
	.byte	29,0,2,35,0,0,28
	.byte	'Ifx_SCU_OVCENABLE_Bits',0,7,242,5,3
	.word	48250
	.byte	10
	.byte	'_Ifx_SCU_PDISC_Bits',0,7,245,5,16,4,11
	.byte	'PDIS0',0,1
	.word	527
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	527
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	504
	.byte	30,0,2,35,0,0,28
	.byte	'Ifx_SCU_PDISC_Bits',0,7,250,5,3
	.word	48386
	.byte	10
	.byte	'_Ifx_SCU_PDR_Bits',0,7,253,5,16,4,11
	.byte	'PD0',0,1
	.word	527
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	527
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	527
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	527
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	504
	.byte	24,0,2,35,0,0,28
	.byte	'Ifx_SCU_PDR_Bits',0,7,132,6,3
	.word	48497
	.byte	10
	.byte	'_Ifx_SCU_PDRR_Bits',0,7,135,6,16,4,11
	.byte	'PDR0',0,1
	.word	527
	.byte	1,7,2,35,0,11
	.byte	'PDR1',0,1
	.word	527
	.byte	1,6,2,35,0,11
	.byte	'PDR2',0,1
	.word	527
	.byte	1,5,2,35,0,11
	.byte	'PDR3',0,1
	.word	527
	.byte	1,4,2,35,0,11
	.byte	'PDR4',0,1
	.word	527
	.byte	1,3,2,35,0,11
	.byte	'PDR5',0,1
	.word	527
	.byte	1,2,2,35,0,11
	.byte	'PDR6',0,1
	.word	527
	.byte	1,1,2,35,0,11
	.byte	'PDR7',0,1
	.word	527
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	504
	.byte	24,0,2,35,0,0,28
	.byte	'Ifx_SCU_PDRR_Bits',0,7,146,6,3
	.word	48630
	.byte	10
	.byte	'_Ifx_SCU_PLLCON0_Bits',0,7,149,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	527
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	527
	.byte	1,6,2,35,0,11
	.byte	'MODEN',0,1
	.word	527
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	527
	.byte	1,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	527
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	527
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	527
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	1022
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	527
	.byte	7,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	527
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	527
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	527
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	527
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	527
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	527
	.byte	4,0,2,35,3,0,28
	.byte	'Ifx_SCU_PLLCON0_Bits',0,7,166,6,3
	.word	48833
	.byte	10
	.byte	'_Ifx_SCU_PLLCON1_Bits',0,7,169,6,16,4,11
	.byte	'K2DIV',0,1
	.word	527
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	527
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	527
	.byte	7,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	527
	.byte	1,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	527
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	1022
	.byte	9,0,2,35,2,0,28
	.byte	'Ifx_SCU_PLLCON1_Bits',0,7,177,6,3
	.word	49189
	.byte	10
	.byte	'_Ifx_SCU_PLLCON2_Bits',0,7,180,6,16,4,11
	.byte	'MODCFG',0,2
	.word	1022
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	1022
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_SCU_PLLCON2_Bits',0,7,184,6,3
	.word	49367
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON0_Bits',0,7,187,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	527
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	527
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	527
	.byte	2,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	527
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	527
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	527
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	1022
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	527
	.byte	5,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	527
	.byte	2,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	527
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	527
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	527
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	527
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	527
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	527
	.byte	4,0,2,35,3,0,28
	.byte	'Ifx_SCU_PLLERAYCON0_Bits',0,7,204,6,3
	.word	49467
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON1_Bits',0,7,207,6,16,4,11
	.byte	'K2DIV',0,1
	.word	527
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	527
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	527
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	527
	.byte	4,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	527
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	1022
	.byte	9,0,2,35,2,0,28
	.byte	'Ifx_SCU_PLLERAYCON1_Bits',0,7,215,6,3
	.word	49837
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYSTAT_Bits',0,7,218,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	527
	.byte	1,7,2,35,0,11
	.byte	'PWDSTAT',0,1
	.word	527
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	527
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	527
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	527
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	527
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	504
	.byte	26,0,2,35,0,0,28
	.byte	'Ifx_SCU_PLLERAYSTAT_Bits',0,7,227,6,3
	.word	50023
	.byte	10
	.byte	'_Ifx_SCU_PLLSTAT_Bits',0,7,230,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	527
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	527
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	527
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	527
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	527
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	527
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	527
	.byte	1,1,2,35,0,11
	.byte	'MODRUN',0,1
	.word	527
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	504
	.byte	24,0,2,35,0,0,28
	.byte	'Ifx_SCU_PLLSTAT_Bits',0,7,241,6,3
	.word	50221
	.byte	10
	.byte	'_Ifx_SCU_PMCSR_Bits',0,7,244,6,16,4,11
	.byte	'REQSLP',0,1
	.word	527
	.byte	2,6,2,35,0,11
	.byte	'SMUSLP',0,1
	.word	527
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	527
	.byte	5,0,2,35,0,11
	.byte	'PMST',0,1
	.word	527
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	504
	.byte	21,0,2,35,0,0,28
	.byte	'Ifx_SCU_PMCSR_Bits',0,7,251,6,3
	.word	50454
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR0_Bits',0,7,254,6,16,4,11
	.byte	'reserved_0',0,1
	.word	527
	.byte	1,7,2,35,0,11
	.byte	'ESR1WKEN',0,1
	.word	527
	.byte	1,6,2,35,0,11
	.byte	'PINAWKEN',0,1
	.word	527
	.byte	1,5,2,35,0,11
	.byte	'PINBWKEN',0,1
	.word	527
	.byte	1,4,2,35,0,11
	.byte	'ESR0DFEN',0,1
	.word	527
	.byte	1,3,2,35,0,11
	.byte	'ESR0EDCON',0,1
	.word	527
	.byte	2,1,2,35,0,11
	.byte	'ESR1DFEN',0,1
	.word	527
	.byte	1,0,2,35,0,11
	.byte	'ESR1EDCON',0,1
	.word	527
	.byte	2,6,2,35,1,11
	.byte	'PINADFEN',0,1
	.word	527
	.byte	1,5,2,35,1,11
	.byte	'PINAEDCON',0,1
	.word	527
	.byte	2,3,2,35,1,11
	.byte	'PINBDFEN',0,1
	.word	527
	.byte	1,2,2,35,1,11
	.byte	'PINBEDCON',0,1
	.word	527
	.byte	2,0,2,35,1,11
	.byte	'SCREN',0,1
	.word	527
	.byte	1,7,2,35,2,11
	.byte	'STBYRAMSEL',0,1
	.word	527
	.byte	2,5,2,35,2,11
	.byte	'SCRCLKSEL',0,1
	.word	527
	.byte	1,4,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	527
	.byte	1,3,2,35,2,11
	.byte	'TRISTEN',0,1
	.word	527
	.byte	1,2,2,35,2,11
	.byte	'TRISTREQ',0,1
	.word	527
	.byte	1,1,2,35,2,11
	.byte	'PORSTDF',0,1
	.word	527
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	527
	.byte	1,7,2,35,3,11
	.byte	'DCDCSYNC',0,1
	.word	527
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	527
	.byte	3,3,2,35,3,11
	.byte	'ESR0TRIST',0,1
	.word	527
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	527
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	527
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_PMSWCR0_Bits',0,7,153,7,3
	.word	50606
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR1_Bits',0,7,156,7,16,4,11
	.byte	'SCRSTEN',0,1
	.word	527
	.byte	1,7,2,35,0,11
	.byte	'SCRSTREQ',0,1
	.word	527
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	527
	.byte	6,0,2,35,0,11
	.byte	'CPUIDLSEL',0,1
	.word	527
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	527
	.byte	1,4,2,35,1,11
	.byte	'IRADIS',0,1
	.word	527
	.byte	1,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	527
	.byte	3,0,2,35,1,11
	.byte	'SCRCFG',0,1
	.word	527
	.byte	8,0,2,35,2,11
	.byte	'CPUSEL',0,1
	.word	527
	.byte	3,5,2,35,3,11
	.byte	'STBYEVEN',0,1
	.word	527
	.byte	1,4,2,35,3,11
	.byte	'STBYEV',0,1
	.word	527
	.byte	3,1,2,35,3,11
	.byte	'LCK',0,1
	.word	527
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_PMSWCR1_Bits',0,7,170,7,3
	.word	51173
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR2_Bits',0,7,173,7,16,4,11
	.byte	'SCRINT',0,1
	.word	527
	.byte	8,0,2,35,0,11
	.byte	'BUSY',0,1
	.word	527
	.byte	1,7,2,35,1,11
	.byte	'SCRECC',0,1
	.word	527
	.byte	1,6,2,35,1,11
	.byte	'SCRWDT',0,1
	.word	527
	.byte	1,5,2,35,1,11
	.byte	'SCRRST',0,1
	.word	527
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	527
	.byte	4,0,2,35,1,11
	.byte	'TCINT',0,1
	.word	527
	.byte	8,0,2,35,2,11
	.byte	'TCINTREQ',0,1
	.word	527
	.byte	1,7,2,35,3,11
	.byte	'SMURST',0,1
	.word	527
	.byte	1,6,2,35,3,11
	.byte	'RST',0,1
	.word	527
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	527
	.byte	4,1,2,35,3,11
	.byte	'LCK',0,1
	.word	527
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_PMSWCR2_Bits',0,7,187,7,3
	.word	51467
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTAT_Bits',0,7,190,7,16,4,11
	.byte	'reserved_0',0,1
	.word	527
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKP',0,1
	.word	527
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUN',0,1
	.word	527
	.byte	1,4,2,35,0,11
	.byte	'PINAWKP',0,1
	.word	527
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUN',0,1
	.word	527
	.byte	1,2,2,35,0,11
	.byte	'PINBWKP',0,1
	.word	527
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUN',0,1
	.word	527
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	527
	.byte	1,7,2,35,1,11
	.byte	'PORSTDF',0,1
	.word	527
	.byte	1,6,2,35,1,11
	.byte	'HWCFGEVR',0,1
	.word	527
	.byte	3,3,2,35,1,11
	.byte	'STBYRAM',0,1
	.word	527
	.byte	2,1,2,35,1,11
	.byte	'TRIST',0,1
	.word	527
	.byte	1,0,2,35,1,11
	.byte	'SCRST',0,1
	.word	527
	.byte	1,7,2,35,2,11
	.byte	'SCRWKP',0,1
	.word	527
	.byte	1,6,2,35,2,11
	.byte	'SCR',0,1
	.word	527
	.byte	1,5,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	527
	.byte	1,4,2,35,2,11
	.byte	'ESR1WKEN',0,1
	.word	527
	.byte	1,3,2,35,2,11
	.byte	'PINAWKEN',0,1
	.word	527
	.byte	1,2,2,35,2,11
	.byte	'PINBWKEN',0,1
	.word	527
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	1022
	.byte	4,5,2,35,2,11
	.byte	'ESR0TRIST',0,1
	.word	527
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	527
	.byte	4,0,2,35,3,0,28
	.byte	'Ifx_SCU_PMSWSTAT_Bits',0,7,214,7,3
	.word	51745
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTATCLR_Bits',0,7,217,7,16,4,11
	.byte	'reserved_0',0,1
	.word	527
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKPCLR',0,1
	.word	527
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUNCLR',0,1
	.word	527
	.byte	1,4,2,35,0,11
	.byte	'PINAWKPCLR',0,1
	.word	527
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUNCLR',0,1
	.word	527
	.byte	1,2,2,35,0,11
	.byte	'PINBWKPCLR',0,1
	.word	527
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUNCLR',0,1
	.word	527
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	527
	.byte	8,0,2,35,1,11
	.byte	'SCRSTCLR',0,1
	.word	527
	.byte	1,7,2,35,2,11
	.byte	'SCRWKPCLR',0,1
	.word	527
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	1022
	.byte	14,0,2,35,2,0,28
	.byte	'Ifx_SCU_PMSWSTATCLR_Bits',0,7,230,7,3
	.word	52241
	.byte	10
	.byte	'_Ifx_SCU_RSTCON2_Bits',0,7,233,7,16,4,11
	.byte	'reserved_0',0,1
	.word	527
	.byte	1,7,2,35,0,11
	.byte	'CLRC',0,1
	.word	527
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	1022
	.byte	10,4,2,35,0,11
	.byte	'CSS0',0,1
	.word	527
	.byte	1,3,2,35,1,11
	.byte	'CSS1',0,1
	.word	527
	.byte	1,2,2,35,1,11
	.byte	'CSS2',0,1
	.word	527
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	527
	.byte	1,0,2,35,1,11
	.byte	'USRINFO',0,2
	.word	1022
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_SCU_RSTCON2_Bits',0,7,243,7,3
	.word	52554
	.byte	10
	.byte	'_Ifx_SCU_RSTCON_Bits',0,7,246,7,16,4,11
	.byte	'ESR0',0,1
	.word	527
	.byte	2,6,2,35,0,11
	.byte	'ESR1',0,1
	.word	527
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	527
	.byte	2,2,2,35,0,11
	.byte	'SMU',0,1
	.word	527
	.byte	2,0,2,35,0,11
	.byte	'SW',0,1
	.word	527
	.byte	2,6,2,35,1,11
	.byte	'STM0',0,1
	.word	527
	.byte	2,4,2,35,1,11
	.byte	'STM1',0,1
	.word	527
	.byte	2,2,2,35,1,11
	.byte	'STM2',0,1
	.word	527
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1022
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_SCU_RSTCON_Bits',0,7,129,8,3
	.word	52763
	.byte	10
	.byte	'_Ifx_SCU_RSTSTAT_Bits',0,7,132,8,16,4,11
	.byte	'ESR0',0,1
	.word	527
	.byte	1,7,2,35,0,11
	.byte	'ESR1',0,1
	.word	527
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	527
	.byte	1,5,2,35,0,11
	.byte	'SMU',0,1
	.word	527
	.byte	1,4,2,35,0,11
	.byte	'SW',0,1
	.word	527
	.byte	1,3,2,35,0,11
	.byte	'STM0',0,1
	.word	527
	.byte	1,2,2,35,0,11
	.byte	'STM1',0,1
	.word	527
	.byte	1,1,2,35,0,11
	.byte	'STM2',0,1
	.word	527
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	527
	.byte	8,0,2,35,1,11
	.byte	'PORST',0,1
	.word	527
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	527
	.byte	1,6,2,35,2,11
	.byte	'CB0',0,1
	.word	527
	.byte	1,5,2,35,2,11
	.byte	'CB1',0,1
	.word	527
	.byte	1,4,2,35,2,11
	.byte	'CB3',0,1
	.word	527
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	527
	.byte	2,1,2,35,2,11
	.byte	'EVR13',0,1
	.word	527
	.byte	1,0,2,35,2,11
	.byte	'EVR33',0,1
	.word	527
	.byte	1,7,2,35,3,11
	.byte	'SWD',0,1
	.word	527
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	527
	.byte	2,4,2,35,3,11
	.byte	'STBYR',0,1
	.word	527
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	527
	.byte	3,0,2,35,3,0,28
	.byte	'Ifx_SCU_RSTSTAT_Bits',0,7,155,8,3
	.word	52974
	.byte	10
	.byte	'_Ifx_SCU_SAFECON_Bits',0,7,158,8,16,4,11
	.byte	'HBT',0,1
	.word	527
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	504
	.byte	31,0,2,35,0,0,28
	.byte	'Ifx_SCU_SAFECON_Bits',0,7,162,8,3
	.word	53406
	.byte	10
	.byte	'_Ifx_SCU_STSTAT_Bits',0,7,165,8,16,4,11
	.byte	'HWCFG',0,1
	.word	527
	.byte	8,0,2,35,0,11
	.byte	'FTM',0,1
	.word	527
	.byte	7,1,2,35,1,11
	.byte	'MODE',0,1
	.word	527
	.byte	1,0,2,35,1,11
	.byte	'FCBAE',0,1
	.word	527
	.byte	1,7,2,35,2,11
	.byte	'LUDIS',0,1
	.word	527
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	527
	.byte	1,5,2,35,2,11
	.byte	'TRSTL',0,1
	.word	527
	.byte	1,4,2,35,2,11
	.byte	'SPDEN',0,1
	.word	527
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	527
	.byte	3,0,2,35,2,11
	.byte	'RAMINT',0,1
	.word	527
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	527
	.byte	7,0,2,35,3,0,28
	.byte	'Ifx_SCU_STSTAT_Bits',0,7,178,8,3
	.word	53502
	.byte	10
	.byte	'_Ifx_SCU_SWRSTCON_Bits',0,7,181,8,16,4,11
	.byte	'reserved_0',0,1
	.word	527
	.byte	1,7,2,35,0,11
	.byte	'SWRSTREQ',0,1
	.word	527
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	504
	.byte	30,0,2,35,0,0,28
	.byte	'Ifx_SCU_SWRSTCON_Bits',0,7,186,8,3
	.word	53762
	.byte	10
	.byte	'_Ifx_SCU_SYSCON_Bits',0,7,189,8,16,4,11
	.byte	'CCTRIG0',0,1
	.word	527
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	527
	.byte	1,6,2,35,0,11
	.byte	'RAMINTM',0,1
	.word	527
	.byte	2,4,2,35,0,11
	.byte	'SETLUDIS',0,1
	.word	527
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	527
	.byte	3,0,2,35,0,11
	.byte	'DATM',0,1
	.word	527
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,4
	.word	504
	.byte	23,0,2,35,0,0,28
	.byte	'Ifx_SCU_SYSCON_Bits',0,7,198,8,3
	.word	53887
	.byte	10
	.byte	'_Ifx_SCU_TRAPCLR_Bits',0,7,201,8,16,4,11
	.byte	'ESR0T',0,1
	.word	527
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	527
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	527
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	527
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	504
	.byte	28,0,2,35,0,0,28
	.byte	'Ifx_SCU_TRAPCLR_Bits',0,7,208,8,3
	.word	54084
	.byte	10
	.byte	'_Ifx_SCU_TRAPDIS_Bits',0,7,211,8,16,4,11
	.byte	'ESR0T',0,1
	.word	527
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	527
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	527
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	527
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	504
	.byte	28,0,2,35,0,0,28
	.byte	'Ifx_SCU_TRAPDIS_Bits',0,7,218,8,3
	.word	54237
	.byte	10
	.byte	'_Ifx_SCU_TRAPSET_Bits',0,7,221,8,16,4,11
	.byte	'ESR0T',0,1
	.word	527
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	527
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	527
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	527
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	504
	.byte	28,0,2,35,0,0,28
	.byte	'Ifx_SCU_TRAPSET_Bits',0,7,228,8,3
	.word	54390
	.byte	10
	.byte	'_Ifx_SCU_TRAPSTAT_Bits',0,7,231,8,16,4,11
	.byte	'ESR0T',0,1
	.word	527
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	527
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	527
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	527
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	504
	.byte	28,0,2,35,0,0,28
	.byte	'Ifx_SCU_TRAPSTAT_Bits',0,7,238,8,3
	.word	54543
	.byte	28
	.byte	'Ifx_SCU_WDTCPU_CON0_Bits',0,7,247,8,3
	.word	1137
	.byte	28
	.byte	'Ifx_SCU_WDTCPU_CON1_Bits',0,7,134,9,3
	.word	1273
	.byte	28
	.byte	'Ifx_SCU_WDTCPU_SR_Bits',0,7,150,9,3
	.word	1517
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON0_Bits',0,7,153,9,16,4,11
	.byte	'ENDINIT',0,4
	.word	1121
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	1121
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	1121
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	1121
	.byte	16,0,2,35,0,0,28
	.byte	'Ifx_SCU_WDTS_CON0_Bits',0,7,159,9,3
	.word	54798
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON1_Bits',0,7,162,9,16,4,11
	.byte	'CLRIRF',0,1
	.word	527
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	527
	.byte	1,6,2,35,0,11
	.byte	'IR0',0,1
	.word	527
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	527
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	527
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	527
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	527
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	527
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	527
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	527
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1022
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_SCU_WDTS_CON1_Bits',0,7,175,9,3
	.word	54924
	.byte	10
	.byte	'_Ifx_SCU_WDTS_SR_Bits',0,7,178,9,16,4,11
	.byte	'AE',0,1
	.word	527
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	527
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	527
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	527
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	527
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	527
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	527
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	527
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	527
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	527
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	1022
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_SCU_WDTS_SR_Bits',0,7,191,9,3
	.word	55176
	.byte	12,7,199,9,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37208
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_ACCEN0',0,7,204,9,3
	.word	55395
	.byte	12,7,207,9,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37765
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_ACCEN1',0,7,212,9,3
	.word	55459
	.byte	12,7,215,9,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37842
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_ARSTDIS',0,7,220,9,3
	.word	55523
	.byte	12,7,223,9,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37978
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_CCUCON0',0,7,228,9,3
	.word	55588
	.byte	12,7,231,9,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38258
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_CCUCON1',0,7,236,9,3
	.word	55653
	.byte	12,7,239,9,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38496
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_CCUCON2',0,7,244,9,3
	.word	55718
	.byte	12,7,247,9,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38624
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_CCUCON3',0,7,252,9,3
	.word	55783
	.byte	12,7,255,9,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38867
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_CCUCON4',0,7,132,10,3
	.word	55848
	.byte	12,7,135,10,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39102
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_CCUCON5',0,7,140,10,3
	.word	55913
	.byte	12,7,143,10,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39230
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_CCUCON6',0,7,148,10,3
	.word	55978
	.byte	12,7,151,10,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39330
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_CCUCON7',0,7,156,10,3
	.word	56043
	.byte	12,7,159,10,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39430
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_CHIPID',0,7,164,10,3
	.word	56108
	.byte	12,7,167,10,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39638
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_DTSCON',0,7,172,10,3
	.word	56172
	.byte	12,7,175,10,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39803
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_DTSLIM',0,7,180,10,3
	.word	56236
	.byte	12,7,183,10,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39986
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_DTSSTAT',0,7,188,10,3
	.word	56300
	.byte	12,7,191,10,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40140
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EICR',0,7,196,10,3
	.word	56365
	.byte	12,7,199,10,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40504
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EIFR',0,7,204,10,3
	.word	56427
	.byte	12,7,207,10,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40715
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EMSR',0,7,212,10,3
	.word	56489
	.byte	12,7,215,10,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40967
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_ESRCFG',0,7,220,10,3
	.word	56551
	.byte	12,7,223,10,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41085
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_ESROCFG',0,7,228,10,3
	.word	56615
	.byte	12,7,231,10,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41196
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVR13CON',0,7,236,10,3
	.word	56680
	.byte	12,7,239,10,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41359
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVR33CON',0,7,244,10,3
	.word	56746
	.byte	12,7,247,10,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41522
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRADCSTAT',0,7,252,10,3
	.word	56812
	.byte	12,7,255,10,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41680
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRDVSTAT',0,7,132,11,3
	.word	56880
	.byte	12,7,135,11,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41845
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRMONCTRL',0,7,140,11,3
	.word	56947
	.byte	12,7,143,11,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42174
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVROSCCTRL',0,7,148,11,3
	.word	57015
	.byte	12,7,151,11,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42395
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVROVMON',0,7,156,11,3
	.word	57083
	.byte	12,7,159,11,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42558
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRRSTCON',0,7,164,11,3
	.word	57149
	.byte	12,7,167,11,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42830
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRSDCOEFF1',0,7,172,11,3
	.word	57216
	.byte	12,7,175,11,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42983
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRSDCOEFF2',0,7,180,11,3
	.word	57285
	.byte	12,7,183,11,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43139
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRSDCOEFF3',0,7,188,11,3
	.word	57354
	.byte	12,7,191,11,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43301
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRSDCOEFF4',0,7,196,11,3
	.word	57423
	.byte	12,7,199,11,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43444
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRSDCOEFF5',0,7,204,11,3
	.word	57492
	.byte	12,7,207,11,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43609
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRSDCOEFF6',0,7,212,11,3
	.word	57561
	.byte	12,7,215,11,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43754
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRSDCTRL1',0,7,220,11,3
	.word	57630
	.byte	12,7,223,11,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43935
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRSDCTRL2',0,7,228,11,3
	.word	57698
	.byte	12,7,231,11,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44109
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRSDCTRL3',0,7,236,11,3
	.word	57766
	.byte	12,7,239,11,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44269
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRSDCTRL4',0,7,244,11,3
	.word	57834
	.byte	12,7,247,11,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44413
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRSTAT',0,7,252,11,3
	.word	57902
	.byte	12,7,255,11,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44687
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRTRIM',0,7,132,12,3
	.word	57967
	.byte	12,7,135,12,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44826
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRUVMON',0,7,140,12,3
	.word	58032
	.byte	12,7,143,12,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44989
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EXTCON',0,7,148,12,3
	.word	58098
	.byte	12,7,151,12,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45207
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_FDR',0,7,156,12,3
	.word	58162
	.byte	12,7,159,12,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45370
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_FMR',0,7,164,12,3
	.word	58223
	.byte	12,7,167,12,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45706
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_ID',0,7,172,12,3
	.word	58284
	.byte	12,7,175,12,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45813
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_IGCR',0,7,180,12,3
	.word	58344
	.byte	12,7,183,12,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46265
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_IN',0,7,188,12,3
	.word	58406
	.byte	12,7,191,12,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46364
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_IOCR',0,7,196,12,3
	.word	58466
	.byte	12,7,199,12,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46514
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_LBISTCTRL0',0,7,204,12,3
	.word	58528
	.byte	12,7,207,12,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46663
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_LBISTCTRL1',0,7,212,12,3
	.word	58596
	.byte	12,7,215,12,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46824
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_LBISTCTRL2',0,7,220,12,3
	.word	58664
	.byte	12,7,223,12,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46954
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_LCLCON',0,7,228,12,3
	.word	58732
	.byte	12,7,231,12,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47086
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_LCLTEST',0,7,236,12,3
	.word	58796
	.byte	12,7,239,12,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47201
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_MANID',0,7,244,12,3
	.word	58861
	.byte	12,7,247,12,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47312
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_OMR',0,7,252,12,3
	.word	58924
	.byte	12,7,255,12,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47470
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_OSCCON',0,7,132,13,3
	.word	58985
	.byte	12,7,135,13,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47882
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_OUT',0,7,140,13,3
	.word	59049
	.byte	12,7,143,13,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47983
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_OVCCON',0,7,148,13,3
	.word	59110
	.byte	12,7,151,13,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48250
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_OVCENABLE',0,7,156,13,3
	.word	59174
	.byte	12,7,159,13,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48386
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PDISC',0,7,164,13,3
	.word	59241
	.byte	12,7,167,13,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48497
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PDR',0,7,172,13,3
	.word	59304
	.byte	12,7,175,13,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48630
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PDRR',0,7,180,13,3
	.word	59365
	.byte	12,7,183,13,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48833
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PLLCON0',0,7,188,13,3
	.word	59427
	.byte	12,7,191,13,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49189
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PLLCON1',0,7,196,13,3
	.word	59492
	.byte	12,7,199,13,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49367
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PLLCON2',0,7,204,13,3
	.word	59557
	.byte	12,7,207,13,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49467
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PLLERAYCON0',0,7,212,13,3
	.word	59622
	.byte	12,7,215,13,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49837
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PLLERAYCON1',0,7,220,13,3
	.word	59691
	.byte	12,7,223,13,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50023
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PLLERAYSTAT',0,7,228,13,3
	.word	59760
	.byte	12,7,231,13,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50221
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PLLSTAT',0,7,236,13,3
	.word	59829
	.byte	12,7,239,13,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50454
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PMCSR',0,7,244,13,3
	.word	59894
	.byte	12,7,247,13,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50606
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PMSWCR0',0,7,252,13,3
	.word	59957
	.byte	12,7,255,13,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51173
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PMSWCR1',0,7,132,14,3
	.word	60022
	.byte	12,7,135,14,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51467
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PMSWCR2',0,7,140,14,3
	.word	60087
	.byte	12,7,143,14,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51745
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PMSWSTAT',0,7,148,14,3
	.word	60152
	.byte	12,7,151,14,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52241
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PMSWSTATCLR',0,7,156,14,3
	.word	60218
	.byte	12,7,159,14,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52763
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_RSTCON',0,7,164,14,3
	.word	60287
	.byte	12,7,167,14,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52554
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_RSTCON2',0,7,172,14,3
	.word	60351
	.byte	12,7,175,14,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52974
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_RSTSTAT',0,7,180,14,3
	.word	60416
	.byte	12,7,183,14,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53406
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_SAFECON',0,7,188,14,3
	.word	60481
	.byte	12,7,191,14,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53502
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_STSTAT',0,7,196,14,3
	.word	60546
	.byte	12,7,199,14,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53762
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_SWRSTCON',0,7,204,14,3
	.word	60610
	.byte	12,7,207,14,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53887
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_SYSCON',0,7,212,14,3
	.word	60676
	.byte	12,7,215,14,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54084
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_TRAPCLR',0,7,220,14,3
	.word	60740
	.byte	12,7,223,14,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54237
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_TRAPDIS',0,7,228,14,3
	.word	60805
	.byte	12,7,231,14,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54390
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_TRAPSET',0,7,236,14,3
	.word	60870
	.byte	12,7,239,14,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54543
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_TRAPSTAT',0,7,244,14,3
	.word	60935
	.byte	28
	.byte	'Ifx_SCU_WDTCPU_CON0',0,7,252,14,3
	.word	1233
	.byte	28
	.byte	'Ifx_SCU_WDTCPU_CON1',0,7,132,15,3
	.word	1477
	.byte	28
	.byte	'Ifx_SCU_WDTCPU_SR',0,7,140,15,3
	.word	1708
	.byte	12,7,143,15,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54798
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_WDTS_CON0',0,7,148,15,3
	.word	61086
	.byte	12,7,151,15,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54924
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_WDTS_CON1',0,7,156,15,3
	.word	61153
	.byte	12,7,159,15,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55176
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_WDTS_SR',0,7,164,15,3
	.word	61220
	.byte	14
	.word	1748
	.byte	28
	.byte	'Ifx_SCU_WDTCPU',0,7,180,15,3
	.word	61285
	.byte	10
	.byte	'_Ifx_SCU_WDTS',0,7,183,15,25,12,13
	.byte	'CON0',0
	.word	61086
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	61153
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	61220
	.byte	4,2,35,8,0,14
	.word	61314
	.byte	28
	.byte	'Ifx_SCU_WDTS',0,7,188,15,3
	.word	61375
	.byte	18,8
	.word	56551
	.byte	19,1,0,18,20
	.word	527
	.byte	19,19,0,18,8
	.word	59894
	.byte	19,1,0,14
	.word	61314
	.byte	18,24
	.word	1748
	.byte	19,1,0,14
	.word	61434
	.byte	18,28
	.word	527
	.byte	19,27,0,18,16
	.word	56365
	.byte	19,3,0,18,16
	.word	58344
	.byte	19,3,0,18,180,3
	.word	527
	.byte	19,179,3,0,10
	.byte	'_Ifx_SCU',0,7,201,15,25,128,8,13
	.byte	'reserved_0',0
	.word	5257
	.byte	8,2,35,0,13
	.byte	'ID',0
	.word	58284
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	3438
	.byte	4,2,35,12,13
	.byte	'OSCCON',0
	.word	58985
	.byte	4,2,35,16,13
	.byte	'PLLSTAT',0
	.word	59829
	.byte	4,2,35,20,13
	.byte	'PLLCON0',0
	.word	59427
	.byte	4,2,35,24,13
	.byte	'PLLCON1',0
	.word	59492
	.byte	4,2,35,28,13
	.byte	'PLLCON2',0
	.word	59557
	.byte	4,2,35,32,13
	.byte	'PLLERAYSTAT',0
	.word	59760
	.byte	4,2,35,36,13
	.byte	'PLLERAYCON0',0
	.word	59622
	.byte	4,2,35,40,13
	.byte	'PLLERAYCON1',0
	.word	59691
	.byte	4,2,35,44,13
	.byte	'CCUCON0',0
	.word	55588
	.byte	4,2,35,48,13
	.byte	'CCUCON1',0
	.word	55653
	.byte	4,2,35,52,13
	.byte	'FDR',0
	.word	58162
	.byte	4,2,35,56,13
	.byte	'EXTCON',0
	.word	58098
	.byte	4,2,35,60,13
	.byte	'CCUCON2',0
	.word	55718
	.byte	4,2,35,64,13
	.byte	'CCUCON3',0
	.word	55783
	.byte	4,2,35,68,13
	.byte	'CCUCON4',0
	.word	55848
	.byte	4,2,35,72,13
	.byte	'CCUCON5',0
	.word	55913
	.byte	4,2,35,76,13
	.byte	'RSTSTAT',0
	.word	60416
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	3438
	.byte	4,2,35,84,13
	.byte	'RSTCON',0
	.word	60287
	.byte	4,2,35,88,13
	.byte	'ARSTDIS',0
	.word	55523
	.byte	4,2,35,92,13
	.byte	'SWRSTCON',0
	.word	60610
	.byte	4,2,35,96,13
	.byte	'RSTCON2',0
	.word	60351
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	3438
	.byte	4,2,35,104,13
	.byte	'EVRRSTCON',0
	.word	57149
	.byte	4,2,35,108,13
	.byte	'ESRCFG',0
	.word	61402
	.byte	8,2,35,112,13
	.byte	'ESROCFG',0
	.word	56615
	.byte	4,2,35,120,13
	.byte	'SYSCON',0
	.word	60676
	.byte	4,2,35,124,13
	.byte	'CCUCON6',0
	.word	55978
	.byte	4,3,35,128,1,13
	.byte	'CCUCON7',0
	.word	56043
	.byte	4,3,35,132,1,13
	.byte	'reserved_88',0
	.word	61411
	.byte	20,3,35,136,1,13
	.byte	'PDR',0
	.word	59304
	.byte	4,3,35,156,1,13
	.byte	'IOCR',0
	.word	58466
	.byte	4,3,35,160,1,13
	.byte	'OUT',0
	.word	59049
	.byte	4,3,35,164,1,13
	.byte	'OMR',0
	.word	58924
	.byte	4,3,35,168,1,13
	.byte	'IN',0
	.word	58406
	.byte	4,3,35,172,1,13
	.byte	'EVRSTAT',0
	.word	57902
	.byte	4,3,35,176,1,13
	.byte	'EVRDVSTAT',0
	.word	56880
	.byte	4,3,35,180,1,13
	.byte	'EVR13CON',0
	.word	56680
	.byte	4,3,35,184,1,13
	.byte	'EVR33CON',0
	.word	56746
	.byte	4,3,35,188,1,13
	.byte	'STSTAT',0
	.word	60546
	.byte	4,3,35,192,1,13
	.byte	'reserved_C4',0
	.word	3438
	.byte	4,3,35,196,1,13
	.byte	'PMSWCR0',0
	.word	59957
	.byte	4,3,35,200,1,13
	.byte	'PMSWSTAT',0
	.word	60152
	.byte	4,3,35,204,1,13
	.byte	'PMSWSTATCLR',0
	.word	60218
	.byte	4,3,35,208,1,13
	.byte	'PMCSR',0
	.word	61420
	.byte	8,3,35,212,1,13
	.byte	'reserved_DC',0
	.word	3438
	.byte	4,3,35,220,1,13
	.byte	'DTSSTAT',0
	.word	56300
	.byte	4,3,35,224,1,13
	.byte	'DTSCON',0
	.word	56172
	.byte	4,3,35,228,1,13
	.byte	'PMSWCR1',0
	.word	60022
	.byte	4,3,35,232,1,13
	.byte	'PMSWCR2',0
	.word	60087
	.byte	4,3,35,236,1,13
	.byte	'WDTS',0
	.word	61429
	.byte	12,3,35,240,1,13
	.byte	'EMSR',0
	.word	56489
	.byte	4,3,35,252,1,13
	.byte	'WDTCPU',0
	.word	61443
	.byte	24,3,35,128,2,13
	.byte	'reserved_118',0
	.word	5597
	.byte	12,3,35,152,2,13
	.byte	'TRAPSTAT',0
	.word	60935
	.byte	4,3,35,164,2,13
	.byte	'TRAPSET',0
	.word	60870
	.byte	4,3,35,168,2,13
	.byte	'TRAPCLR',0
	.word	60740
	.byte	4,3,35,172,2,13
	.byte	'TRAPDIS',0
	.word	60805
	.byte	4,3,35,176,2,13
	.byte	'reserved_134',0
	.word	3438
	.byte	4,3,35,180,2,13
	.byte	'LCLCON1',0
	.word	58732
	.byte	4,3,35,184,2,13
	.byte	'LCLTEST',0
	.word	58796
	.byte	4,3,35,188,2,13
	.byte	'CHIPID',0
	.word	56108
	.byte	4,3,35,192,2,13
	.byte	'MANID',0
	.word	58861
	.byte	4,3,35,196,2,13
	.byte	'reserved_148',0
	.word	5257
	.byte	8,3,35,200,2,13
	.byte	'SAFECON',0
	.word	60481
	.byte	4,3,35,208,2,13
	.byte	'reserved_154',0
	.word	31999
	.byte	16,3,35,212,2,13
	.byte	'LBISTCTRL0',0
	.word	58528
	.byte	4,3,35,228,2,13
	.byte	'LBISTCTRL1',0
	.word	58596
	.byte	4,3,35,232,2,13
	.byte	'LBISTCTRL2',0
	.word	58664
	.byte	4,3,35,236,2,13
	.byte	'reserved_170',0
	.word	61448
	.byte	28,3,35,240,2,13
	.byte	'PDISC',0
	.word	59241
	.byte	4,3,35,140,3,13
	.byte	'reserved_190',0
	.word	5257
	.byte	8,3,35,144,3,13
	.byte	'EVRTRIM',0
	.word	57967
	.byte	4,3,35,152,3,13
	.byte	'EVRADCSTAT',0
	.word	56812
	.byte	4,3,35,156,3,13
	.byte	'EVRUVMON',0
	.word	58032
	.byte	4,3,35,160,3,13
	.byte	'EVROVMON',0
	.word	57083
	.byte	4,3,35,164,3,13
	.byte	'EVRMONCTRL',0
	.word	56947
	.byte	4,3,35,168,3,13
	.byte	'reserved_1AC',0
	.word	3438
	.byte	4,3,35,172,3,13
	.byte	'EVRSDCTRL1',0
	.word	57630
	.byte	4,3,35,176,3,13
	.byte	'EVRSDCTRL2',0
	.word	57698
	.byte	4,3,35,180,3,13
	.byte	'EVRSDCTRL3',0
	.word	57766
	.byte	4,3,35,184,3,13
	.byte	'EVRSDCTRL4',0
	.word	57834
	.byte	4,3,35,188,3,13
	.byte	'EVRSDCOEFF1',0
	.word	57216
	.byte	4,3,35,192,3,13
	.byte	'EVRSDCOEFF2',0
	.word	57285
	.byte	4,3,35,196,3,13
	.byte	'EVRSDCOEFF3',0
	.word	57354
	.byte	4,3,35,200,3,13
	.byte	'EVRSDCOEFF4',0
	.word	57423
	.byte	4,3,35,204,3,13
	.byte	'EVRSDCOEFF5',0
	.word	57492
	.byte	4,3,35,208,3,13
	.byte	'EVRSDCOEFF6',0
	.word	57561
	.byte	4,3,35,212,3,13
	.byte	'EVROSCCTRL',0
	.word	57015
	.byte	4,3,35,216,3,13
	.byte	'reserved_1DC',0
	.word	3438
	.byte	4,3,35,220,3,13
	.byte	'OVCENABLE',0
	.word	59174
	.byte	4,3,35,224,3,13
	.byte	'OVCCON',0
	.word	59110
	.byte	4,3,35,228,3,13
	.byte	'reserved_1E8',0
	.word	31634
	.byte	40,3,35,232,3,13
	.byte	'EICR',0
	.word	61457
	.byte	16,3,35,144,4,13
	.byte	'EIFR',0
	.word	56427
	.byte	4,3,35,160,4,13
	.byte	'FMR',0
	.word	58223
	.byte	4,3,35,164,4,13
	.byte	'PDRR',0
	.word	59365
	.byte	4,3,35,168,4,13
	.byte	'IGCR',0
	.word	61466
	.byte	16,3,35,172,4,13
	.byte	'reserved_23C',0
	.word	3438
	.byte	4,3,35,188,4,13
	.byte	'DTSLIM',0
	.word	56236
	.byte	4,3,35,192,4,13
	.byte	'reserved_244',0
	.word	61475
	.byte	180,3,3,35,196,4,13
	.byte	'ACCEN1',0
	.word	55459
	.byte	4,3,35,248,7,13
	.byte	'ACCEN0',0
	.word	55395
	.byte	4,3,35,252,7,0,14
	.word	61486
	.byte	28
	.byte	'Ifx_SCU',0,7,181,16,3
	.word	63476
	.byte	10
	.byte	'_Ifx_CPU_A_Bits',0,21,45,16,4,11
	.byte	'ADDR',0,4
	.word	1121
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_CPU_A_Bits',0,21,48,3
	.word	63498
	.byte	10
	.byte	'_Ifx_CPU_BIV_Bits',0,21,51,16,4,11
	.byte	'VSS',0,4
	.word	1121
	.byte	1,31,2,35,0,11
	.byte	'BIV',0,4
	.word	1121
	.byte	31,0,2,35,0,0,28
	.byte	'Ifx_CPU_BIV_Bits',0,21,55,3
	.word	63559
	.byte	10
	.byte	'_Ifx_CPU_BTV_Bits',0,21,58,16,4,11
	.byte	'reserved_0',0,4
	.word	1121
	.byte	1,31,2,35,0,11
	.byte	'BTV',0,4
	.word	1121
	.byte	31,0,2,35,0,0,28
	.byte	'Ifx_CPU_BTV_Bits',0,21,62,3
	.word	63638
	.byte	10
	.byte	'_Ifx_CPU_CCNT_Bits',0,21,65,16,4,11
	.byte	'CountValue',0,4
	.word	1121
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	1121
	.byte	1,0,2,35,0,0,28
	.byte	'Ifx_CPU_CCNT_Bits',0,21,69,3
	.word	63724
	.byte	10
	.byte	'_Ifx_CPU_CCTRL_Bits',0,21,72,16,4,11
	.byte	'CM',0,4
	.word	1121
	.byte	1,31,2,35,0,11
	.byte	'CE',0,4
	.word	1121
	.byte	1,30,2,35,0,11
	.byte	'M1',0,4
	.word	1121
	.byte	3,27,2,35,0,11
	.byte	'M2',0,4
	.word	1121
	.byte	3,24,2,35,0,11
	.byte	'M3',0,4
	.word	1121
	.byte	3,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	1121
	.byte	21,0,2,35,0,0,28
	.byte	'Ifx_CPU_CCTRL_Bits',0,21,80,3
	.word	63813
	.byte	10
	.byte	'_Ifx_CPU_COMPAT_Bits',0,21,83,16,4,11
	.byte	'reserved_0',0,4
	.word	1121
	.byte	3,29,2,35,0,11
	.byte	'RM',0,4
	.word	1121
	.byte	1,28,2,35,0,11
	.byte	'SP',0,4
	.word	1121
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	1121
	.byte	27,0,2,35,0,0,28
	.byte	'Ifx_CPU_COMPAT_Bits',0,21,89,3
	.word	63959
	.byte	10
	.byte	'_Ifx_CPU_CORE_ID_Bits',0,21,92,16,4,11
	.byte	'CORE_ID',0,4
	.word	1121
	.byte	3,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	1121
	.byte	29,0,2,35,0,0,28
	.byte	'Ifx_CPU_CORE_ID_Bits',0,21,96,3
	.word	64086
	.byte	10
	.byte	'_Ifx_CPU_CPR_L_Bits',0,21,99,16,4,11
	.byte	'reserved_0',0,4
	.word	1121
	.byte	3,29,2,35,0,11
	.byte	'LOWBND',0,4
	.word	1121
	.byte	29,0,2,35,0,0,28
	.byte	'Ifx_CPU_CPR_L_Bits',0,21,103,3
	.word	64184
	.byte	10
	.byte	'_Ifx_CPU_CPR_U_Bits',0,21,106,16,4,11
	.byte	'reserved_0',0,4
	.word	1121
	.byte	3,29,2,35,0,11
	.byte	'UPPBND',0,4
	.word	1121
	.byte	29,0,2,35,0,0,28
	.byte	'Ifx_CPU_CPR_U_Bits',0,21,110,3
	.word	64277
	.byte	10
	.byte	'_Ifx_CPU_CPU_ID_Bits',0,21,113,16,4,11
	.byte	'MODREV',0,4
	.word	1121
	.byte	8,24,2,35,0,11
	.byte	'MOD_32B',0,4
	.word	1121
	.byte	8,16,2,35,0,11
	.byte	'MOD',0,4
	.word	1121
	.byte	16,0,2,35,0,0,28
	.byte	'Ifx_CPU_CPU_ID_Bits',0,21,118,3
	.word	64370
	.byte	10
	.byte	'_Ifx_CPU_CPXE_Bits',0,21,121,16,4,11
	.byte	'XE',0,4
	.word	1121
	.byte	8,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	1121
	.byte	24,0,2,35,0,0,28
	.byte	'Ifx_CPU_CPXE_Bits',0,21,125,3
	.word	64477
	.byte	10
	.byte	'_Ifx_CPU_CREVT_Bits',0,21,128,1,16,4,11
	.byte	'EVTA',0,4
	.word	1121
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	1121
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	1121
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	1121
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	1121
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	1121
	.byte	24,0,2,35,0,0,28
	.byte	'Ifx_CPU_CREVT_Bits',0,21,136,1,3
	.word	64564
	.byte	10
	.byte	'_Ifx_CPU_CUS_ID_Bits',0,21,139,1,16,4,11
	.byte	'CID',0,4
	.word	1121
	.byte	3,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	1121
	.byte	29,0,2,35,0,0,28
	.byte	'Ifx_CPU_CUS_ID_Bits',0,21,143,1,3
	.word	64718
	.byte	10
	.byte	'_Ifx_CPU_D_Bits',0,21,146,1,16,4,11
	.byte	'DATA',0,4
	.word	1121
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_CPU_D_Bits',0,21,149,1,3
	.word	64812
	.byte	10
	.byte	'_Ifx_CPU_DATR_Bits',0,21,152,1,16,4,11
	.byte	'reserved_0',0,4
	.word	1121
	.byte	3,29,2,35,0,11
	.byte	'SBE',0,4
	.word	1121
	.byte	1,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	1121
	.byte	5,23,2,35,0,11
	.byte	'CWE',0,4
	.word	1121
	.byte	1,22,2,35,0,11
	.byte	'CFE',0,4
	.word	1121
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	1121
	.byte	3,18,2,35,0,11
	.byte	'SOE',0,4
	.word	1121
	.byte	1,17,2,35,0,11
	.byte	'SME',0,4
	.word	1121
	.byte	1,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	1121
	.byte	16,0,2,35,0,0,28
	.byte	'Ifx_CPU_DATR_Bits',0,21,163,1,3
	.word	64875
	.byte	10
	.byte	'_Ifx_CPU_DBGSR_Bits',0,21,166,1,16,4,11
	.byte	'DE',0,4
	.word	1121
	.byte	1,31,2,35,0,11
	.byte	'HALT',0,4
	.word	1121
	.byte	2,29,2,35,0,11
	.byte	'SIH',0,4
	.word	1121
	.byte	1,28,2,35,0,11
	.byte	'SUSP',0,4
	.word	1121
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	1121
	.byte	1,26,2,35,0,11
	.byte	'PREVSUSP',0,4
	.word	1121
	.byte	1,25,2,35,0,11
	.byte	'PEVT',0,4
	.word	1121
	.byte	1,24,2,35,0,11
	.byte	'EVTSRC',0,4
	.word	1121
	.byte	5,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	1121
	.byte	19,0,2,35,0,0,28
	.byte	'Ifx_CPU_DBGSR_Bits',0,21,177,1,3
	.word	65093
	.byte	10
	.byte	'_Ifx_CPU_DBGTCR_Bits',0,21,180,1,16,4,11
	.byte	'DTA',0,4
	.word	1121
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	1121
	.byte	31,0,2,35,0,0,28
	.byte	'Ifx_CPU_DBGTCR_Bits',0,21,184,1,3
	.word	65308
	.byte	10
	.byte	'_Ifx_CPU_DCON0_Bits',0,21,187,1,16,4,11
	.byte	'reserved_0',0,4
	.word	1121
	.byte	1,31,2,35,0,11
	.byte	'DCBYP',0,4
	.word	1121
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	1121
	.byte	30,0,2,35,0,0,28
	.byte	'Ifx_CPU_DCON0_Bits',0,21,192,1,3
	.word	65402
	.byte	10
	.byte	'_Ifx_CPU_DCON2_Bits',0,21,195,1,16,4,11
	.byte	'DCACHE_SZE',0,4
	.word	1121
	.byte	16,16,2,35,0,11
	.byte	'DSCRATCH_SZE',0,4
	.word	1121
	.byte	16,0,2,35,0,0,28
	.byte	'Ifx_CPU_DCON2_Bits',0,21,199,1,3
	.word	65518
	.byte	10
	.byte	'_Ifx_CPU_DCX_Bits',0,21,202,1,16,4,11
	.byte	'reserved_0',0,4
	.word	1121
	.byte	6,26,2,35,0,11
	.byte	'DCXValue',0,4
	.word	1121
	.byte	26,0,2,35,0,0,28
	.byte	'Ifx_CPU_DCX_Bits',0,21,206,1,3
	.word	65619
	.byte	10
	.byte	'_Ifx_CPU_DEADD_Bits',0,21,209,1,16,4,11
	.byte	'ERROR_ADDRESS',0,4
	.word	1121
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_CPU_DEADD_Bits',0,21,212,1,3
	.word	65712
	.byte	10
	.byte	'_Ifx_CPU_DIEAR_Bits',0,21,215,1,16,4,11
	.byte	'TA',0,4
	.word	1121
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_CPU_DIEAR_Bits',0,21,218,1,3
	.word	65792
	.byte	10
	.byte	'_Ifx_CPU_DIETR_Bits',0,21,221,1,16,4,11
	.byte	'IED',0,4
	.word	1121
	.byte	1,31,2,35,0,11
	.byte	'IE_T',0,4
	.word	1121
	.byte	1,30,2,35,0,11
	.byte	'IE_C',0,4
	.word	1121
	.byte	1,29,2,35,0,11
	.byte	'IE_S',0,4
	.word	1121
	.byte	1,28,2,35,0,11
	.byte	'IE_BI',0,4
	.word	1121
	.byte	1,27,2,35,0,11
	.byte	'E_INFO',0,4
	.word	1121
	.byte	6,21,2,35,0,11
	.byte	'IE_DUAL',0,4
	.word	1121
	.byte	1,20,2,35,0,11
	.byte	'IE_SP',0,4
	.word	1121
	.byte	1,19,2,35,0,11
	.byte	'IE_BS',0,4
	.word	1121
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	1121
	.byte	18,0,2,35,0,0,28
	.byte	'Ifx_CPU_DIETR_Bits',0,21,233,1,3
	.word	65861
	.byte	10
	.byte	'_Ifx_CPU_DMS_Bits',0,21,236,1,16,4,11
	.byte	'reserved_0',0,4
	.word	1121
	.byte	1,31,2,35,0,11
	.byte	'DMSValue',0,4
	.word	1121
	.byte	31,0,2,35,0,0,28
	.byte	'Ifx_CPU_DMS_Bits',0,21,240,1,3
	.word	66090
	.byte	10
	.byte	'_Ifx_CPU_DPR_L_Bits',0,21,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	1121
	.byte	3,29,2,35,0,11
	.byte	'LOWBND',0,4
	.word	1121
	.byte	29,0,2,35,0,0,28
	.byte	'Ifx_CPU_DPR_L_Bits',0,21,247,1,3
	.word	66183
	.byte	10
	.byte	'_Ifx_CPU_DPR_U_Bits',0,21,250,1,16,4,11
	.byte	'reserved_0',0,4
	.word	1121
	.byte	3,29,2,35,0,11
	.byte	'UPPBND',0,4
	.word	1121
	.byte	29,0,2,35,0,0,28
	.byte	'Ifx_CPU_DPR_U_Bits',0,21,254,1,3
	.word	66278
	.byte	10
	.byte	'_Ifx_CPU_DPRE_Bits',0,21,129,2,16,4,11
	.byte	'RE',0,4
	.word	1121
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	1121
	.byte	16,0,2,35,0,0,28
	.byte	'Ifx_CPU_DPRE_Bits',0,21,133,2,3
	.word	66373
	.byte	10
	.byte	'_Ifx_CPU_DPWE_Bits',0,21,136,2,16,4,11
	.byte	'WE',0,4
	.word	1121
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	1121
	.byte	16,0,2,35,0,0,28
	.byte	'Ifx_CPU_DPWE_Bits',0,21,140,2,3
	.word	66463
	.byte	10
	.byte	'_Ifx_CPU_DSTR_Bits',0,21,143,2,16,4,11
	.byte	'SRE',0,4
	.word	1121
	.byte	1,31,2,35,0,11
	.byte	'GAE',0,4
	.word	1121
	.byte	1,30,2,35,0,11
	.byte	'LBE',0,4
	.word	1121
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	1121
	.byte	3,26,2,35,0,11
	.byte	'CRE',0,4
	.word	1121
	.byte	1,25,2,35,0,11
	.byte	'reserved_7',0,4
	.word	1121
	.byte	7,18,2,35,0,11
	.byte	'DTME',0,4
	.word	1121
	.byte	1,17,2,35,0,11
	.byte	'LOE',0,4
	.word	1121
	.byte	1,16,2,35,0,11
	.byte	'SDE',0,4
	.word	1121
	.byte	1,15,2,35,0,11
	.byte	'SCE',0,4
	.word	1121
	.byte	1,14,2,35,0,11
	.byte	'CAC',0,4
	.word	1121
	.byte	1,13,2,35,0,11
	.byte	'MPE',0,4
	.word	1121
	.byte	1,12,2,35,0,11
	.byte	'CLE',0,4
	.word	1121
	.byte	1,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	1121
	.byte	3,8,2,35,0,11
	.byte	'ALN',0,4
	.word	1121
	.byte	1,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	1121
	.byte	7,0,2,35,0,0,28
	.byte	'Ifx_CPU_DSTR_Bits',0,21,161,2,3
	.word	66553
	.byte	10
	.byte	'_Ifx_CPU_EXEVT_Bits',0,21,164,2,16,4,11
	.byte	'EVTA',0,4
	.word	1121
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	1121
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	1121
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	1121
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	1121
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	1121
	.byte	24,0,2,35,0,0,28
	.byte	'Ifx_CPU_EXEVT_Bits',0,21,172,2,3
	.word	66877
	.byte	10
	.byte	'_Ifx_CPU_FCX_Bits',0,21,175,2,16,4,11
	.byte	'FCXO',0,4
	.word	1121
	.byte	16,16,2,35,0,11
	.byte	'FCXS',0,4
	.word	1121
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	1121
	.byte	12,0,2,35,0,0,28
	.byte	'Ifx_CPU_FCX_Bits',0,21,180,2,3
	.word	67031
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_CON_Bits',0,21,183,2,16,4,11
	.byte	'TST',0,4
	.word	1121
	.byte	1,31,2,35,0,11
	.byte	'TCL',0,4
	.word	1121
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	1121
	.byte	6,24,2,35,0,11
	.byte	'RM',0,4
	.word	1121
	.byte	2,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	1121
	.byte	8,14,2,35,0,11
	.byte	'FXE',0,4
	.word	1121
	.byte	1,13,2,35,0,11
	.byte	'FUE',0,4
	.word	1121
	.byte	1,12,2,35,0,11
	.byte	'FZE',0,4
	.word	1121
	.byte	1,11,2,35,0,11
	.byte	'FVE',0,4
	.word	1121
	.byte	1,10,2,35,0,11
	.byte	'FIE',0,4
	.word	1121
	.byte	1,9,2,35,0,11
	.byte	'reserved_23',0,4
	.word	1121
	.byte	3,6,2,35,0,11
	.byte	'FX',0,4
	.word	1121
	.byte	1,5,2,35,0,11
	.byte	'FU',0,4
	.word	1121
	.byte	1,4,2,35,0,11
	.byte	'FZ',0,4
	.word	1121
	.byte	1,3,2,35,0,11
	.byte	'FV',0,4
	.word	1121
	.byte	1,2,2,35,0,11
	.byte	'FI',0,4
	.word	1121
	.byte	1,1,2,35,0,11
	.byte	'reserved_31',0,4
	.word	1121
	.byte	1,0,2,35,0,0,28
	.byte	'Ifx_CPU_FPU_TRAP_CON_Bits',0,21,202,2,3
	.word	67137
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_OPC_Bits',0,21,205,2,16,4,11
	.byte	'OPC',0,4
	.word	1121
	.byte	8,24,2,35,0,11
	.byte	'FMT',0,4
	.word	1121
	.byte	1,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	1121
	.byte	7,16,2,35,0,11
	.byte	'DREG',0,4
	.word	1121
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	1121
	.byte	12,0,2,35,0,0,28
	.byte	'Ifx_CPU_FPU_TRAP_OPC_Bits',0,21,212,2,3
	.word	67486
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_PC_Bits',0,21,215,2,16,4,11
	.byte	'PC',0,4
	.word	1121
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_CPU_FPU_TRAP_PC_Bits',0,21,218,2,3
	.word	67646
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC1_Bits',0,21,221,2,16,4,11
	.byte	'SRC1',0,4
	.word	1121
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_CPU_FPU_TRAP_SRC1_Bits',0,21,224,2,3
	.word	67727
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC2_Bits',0,21,227,2,16,4,11
	.byte	'SRC2',0,4
	.word	1121
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_CPU_FPU_TRAP_SRC2_Bits',0,21,230,2,3
	.word	67814
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC3_Bits',0,21,233,2,16,4,11
	.byte	'SRC3',0,4
	.word	1121
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_CPU_FPU_TRAP_SRC3_Bits',0,21,236,2,3
	.word	67901
	.byte	10
	.byte	'_Ifx_CPU_ICNT_Bits',0,21,239,2,16,4,11
	.byte	'CountValue',0,4
	.word	1121
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	1121
	.byte	1,0,2,35,0,0,28
	.byte	'Ifx_CPU_ICNT_Bits',0,21,243,2,3
	.word	67988
	.byte	10
	.byte	'_Ifx_CPU_ICR_Bits',0,21,246,2,16,4,11
	.byte	'CCPN',0,4
	.word	1121
	.byte	10,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	1121
	.byte	5,17,2,35,0,11
	.byte	'IE',0,4
	.word	1121
	.byte	1,16,2,35,0,11
	.byte	'PIPN',0,4
	.word	1121
	.byte	10,6,2,35,0,11
	.byte	'reserved_26',0,4
	.word	1121
	.byte	6,0,2,35,0,0,28
	.byte	'Ifx_CPU_ICR_Bits',0,21,253,2,3
	.word	68079
	.byte	10
	.byte	'_Ifx_CPU_ISP_Bits',0,21,128,3,16,4,11
	.byte	'ISP',0,4
	.word	1121
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_CPU_ISP_Bits',0,21,131,3,3
	.word	68222
	.byte	10
	.byte	'_Ifx_CPU_LCX_Bits',0,21,134,3,16,4,11
	.byte	'LCXO',0,4
	.word	1121
	.byte	16,16,2,35,0,11
	.byte	'LCXS',0,4
	.word	1121
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	1121
	.byte	12,0,2,35,0,0,28
	.byte	'Ifx_CPU_LCX_Bits',0,21,139,3,3
	.word	68288
	.byte	10
	.byte	'_Ifx_CPU_M1CNT_Bits',0,21,142,3,16,4,11
	.byte	'CountValue',0,4
	.word	1121
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	1121
	.byte	1,0,2,35,0,0,28
	.byte	'Ifx_CPU_M1CNT_Bits',0,21,146,3,3
	.word	68394
	.byte	10
	.byte	'_Ifx_CPU_M2CNT_Bits',0,21,149,3,16,4,11
	.byte	'CountValue',0,4
	.word	1121
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	1121
	.byte	1,0,2,35,0,0,28
	.byte	'Ifx_CPU_M2CNT_Bits',0,21,153,3,3
	.word	68487
	.byte	10
	.byte	'_Ifx_CPU_M3CNT_Bits',0,21,156,3,16,4,11
	.byte	'CountValue',0,4
	.word	1121
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	1121
	.byte	1,0,2,35,0,0,28
	.byte	'Ifx_CPU_M3CNT_Bits',0,21,160,3,3
	.word	68580
	.byte	10
	.byte	'_Ifx_CPU_PC_Bits',0,21,163,3,16,4,11
	.byte	'reserved_0',0,4
	.word	1121
	.byte	1,31,2,35,0,11
	.byte	'PC',0,4
	.word	1121
	.byte	31,0,2,35,0,0,28
	.byte	'Ifx_CPU_PC_Bits',0,21,167,3,3
	.word	68673
	.byte	10
	.byte	'_Ifx_CPU_PCON0_Bits',0,21,170,3,16,4,11
	.byte	'reserved_0',0,4
	.word	1121
	.byte	1,31,2,35,0,11
	.byte	'PCBYP',0,4
	.word	1121
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	1121
	.byte	30,0,2,35,0,0,28
	.byte	'Ifx_CPU_PCON0_Bits',0,21,175,3,3
	.word	68758
	.byte	10
	.byte	'_Ifx_CPU_PCON1_Bits',0,21,178,3,16,4,11
	.byte	'PCINV',0,4
	.word	1121
	.byte	1,31,2,35,0,11
	.byte	'PBINV',0,4
	.word	1121
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	1121
	.byte	30,0,2,35,0,0,28
	.byte	'Ifx_CPU_PCON1_Bits',0,21,183,3,3
	.word	68874
	.byte	10
	.byte	'_Ifx_CPU_PCON2_Bits',0,21,186,3,16,4,11
	.byte	'PCACHE_SZE',0,4
	.word	1121
	.byte	16,16,2,35,0,11
	.byte	'PSCRATCH_SZE',0,4
	.word	1121
	.byte	16,0,2,35,0,0,28
	.byte	'Ifx_CPU_PCON2_Bits',0,21,190,3,3
	.word	68985
	.byte	10
	.byte	'_Ifx_CPU_PCXI_Bits',0,21,193,3,16,4,11
	.byte	'PCXO',0,4
	.word	1121
	.byte	16,16,2,35,0,11
	.byte	'PCXS',0,4
	.word	1121
	.byte	4,12,2,35,0,11
	.byte	'UL',0,4
	.word	1121
	.byte	1,11,2,35,0,11
	.byte	'PIE',0,4
	.word	1121
	.byte	1,10,2,35,0,11
	.byte	'PCPN',0,4
	.word	1121
	.byte	10,0,2,35,0,0,28
	.byte	'Ifx_CPU_PCXI_Bits',0,21,200,3,3
	.word	69086
	.byte	10
	.byte	'_Ifx_CPU_PIEAR_Bits',0,21,203,3,16,4,11
	.byte	'TA',0,4
	.word	1121
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_CPU_PIEAR_Bits',0,21,206,3,3
	.word	69216
	.byte	10
	.byte	'_Ifx_CPU_PIETR_Bits',0,21,209,3,16,4,11
	.byte	'IED',0,4
	.word	1121
	.byte	1,31,2,35,0,11
	.byte	'IE_T',0,4
	.word	1121
	.byte	1,30,2,35,0,11
	.byte	'IE_C',0,4
	.word	1121
	.byte	1,29,2,35,0,11
	.byte	'IE_S',0,4
	.word	1121
	.byte	1,28,2,35,0,11
	.byte	'IE_BI',0,4
	.word	1121
	.byte	1,27,2,35,0,11
	.byte	'E_INFO',0,4
	.word	1121
	.byte	6,21,2,35,0,11
	.byte	'IE_DUAL',0,4
	.word	1121
	.byte	1,20,2,35,0,11
	.byte	'IE_SP',0,4
	.word	1121
	.byte	1,19,2,35,0,11
	.byte	'IE_BS',0,4
	.word	1121
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	1121
	.byte	18,0,2,35,0,0,28
	.byte	'Ifx_CPU_PIETR_Bits',0,21,221,3,3
	.word	69285
	.byte	10
	.byte	'_Ifx_CPU_PMA0_Bits',0,21,224,3,16,4,11
	.byte	'reserved_0',0,4
	.word	1121
	.byte	13,19,2,35,0,11
	.byte	'DAC',0,4
	.word	1121
	.byte	3,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	1121
	.byte	16,0,2,35,0,0,28
	.byte	'Ifx_CPU_PMA0_Bits',0,21,229,3,3
	.word	69514
	.byte	10
	.byte	'_Ifx_CPU_PMA1_Bits',0,21,232,3,16,4,11
	.byte	'reserved_0',0,4
	.word	1121
	.byte	14,18,2,35,0,11
	.byte	'CAC',0,4
	.word	1121
	.byte	2,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	1121
	.byte	16,0,2,35,0,0,28
	.byte	'Ifx_CPU_PMA1_Bits',0,21,237,3,3
	.word	69627
	.byte	10
	.byte	'_Ifx_CPU_PMA2_Bits',0,21,240,3,16,4,11
	.byte	'PSI',0,4
	.word	1121
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	1121
	.byte	16,0,2,35,0,0,28
	.byte	'Ifx_CPU_PMA2_Bits',0,21,244,3,3
	.word	69740
	.byte	10
	.byte	'_Ifx_CPU_PSTR_Bits',0,21,247,3,16,4,11
	.byte	'FRE',0,4
	.word	1121
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	1121
	.byte	1,30,2,35,0,11
	.byte	'FBE',0,4
	.word	1121
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	1121
	.byte	9,20,2,35,0,11
	.byte	'FPE',0,4
	.word	1121
	.byte	1,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	1121
	.byte	1,18,2,35,0,11
	.byte	'FME',0,4
	.word	1121
	.byte	1,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	1121
	.byte	17,0,2,35,0,0,28
	.byte	'Ifx_CPU_PSTR_Bits',0,21,129,4,3
	.word	69831
	.byte	10
	.byte	'_Ifx_CPU_PSW_Bits',0,21,132,4,16,4,11
	.byte	'CDC',0,4
	.word	1121
	.byte	7,25,2,35,0,11
	.byte	'CDE',0,4
	.word	1121
	.byte	1,24,2,35,0,11
	.byte	'GW',0,4
	.word	1121
	.byte	1,23,2,35,0,11
	.byte	'IS',0,4
	.word	1121
	.byte	1,22,2,35,0,11
	.byte	'IO',0,4
	.word	1121
	.byte	2,20,2,35,0,11
	.byte	'PRS',0,4
	.word	1121
	.byte	2,18,2,35,0,11
	.byte	'S',0,4
	.word	1121
	.byte	1,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	1121
	.byte	12,5,2,35,0,11
	.byte	'SAV',0,4
	.word	1121
	.byte	1,4,2,35,0,11
	.byte	'AV',0,4
	.word	1121
	.byte	1,3,2,35,0,11
	.byte	'SV',0,4
	.word	1121
	.byte	1,2,2,35,0,11
	.byte	'V',0,4
	.word	1121
	.byte	1,1,2,35,0,11
	.byte	'C',0,4
	.word	1121
	.byte	1,0,2,35,0,0,28
	.byte	'Ifx_CPU_PSW_Bits',0,21,147,4,3
	.word	70034
	.byte	10
	.byte	'_Ifx_CPU_SEGEN_Bits',0,21,150,4,16,4,11
	.byte	'ADFLIP',0,4
	.word	1121
	.byte	8,24,2,35,0,11
	.byte	'ADTYPE',0,4
	.word	1121
	.byte	2,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	1121
	.byte	21,1,2,35,0,11
	.byte	'AE',0,4
	.word	1121
	.byte	1,0,2,35,0,0,28
	.byte	'Ifx_CPU_SEGEN_Bits',0,21,156,4,3
	.word	70277
	.byte	10
	.byte	'_Ifx_CPU_SMACON_Bits',0,21,159,4,16,4,11
	.byte	'PC',0,4
	.word	1121
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	1121
	.byte	1,30,2,35,0,11
	.byte	'PT',0,4
	.word	1121
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	1121
	.byte	5,24,2,35,0,11
	.byte	'DC',0,4
	.word	1121
	.byte	1,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	1121
	.byte	1,22,2,35,0,11
	.byte	'DT',0,4
	.word	1121
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	1121
	.byte	13,8,2,35,0,11
	.byte	'IODT',0,4
	.word	1121
	.byte	1,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	1121
	.byte	7,0,2,35,0,0,28
	.byte	'Ifx_CPU_SMACON_Bits',0,21,171,4,3
	.word	70405
	.byte	10
	.byte	'_Ifx_CPU_SPROT_ACCENA_Bits',0,21,174,4,16,4,11
	.byte	'EN',0,4
	.word	504
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_CPU_SPROT_ACCENA_Bits',0,21,177,4,3
	.word	70646
	.byte	10
	.byte	'_Ifx_CPU_SPROT_ACCENB_Bits',0,21,180,4,16,4,11
	.byte	'reserved_0',0,4
	.word	504
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_CPU_SPROT_ACCENB_Bits',0,21,183,4,3
	.word	70729
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,21,186,4,16,4,11
	.byte	'EN',0,4
	.word	504
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,21,189,4,3
	.word	70820
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,21,192,4,16,4,11
	.byte	'reserved_0',0,4
	.word	504
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,21,195,4,3
	.word	70911
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_LA_Bits',0,21,198,4,16,4,11
	.byte	'reserved_0',0,1
	.word	527
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	504
	.byte	27,0,2,35,0,0,28
	.byte	'Ifx_CPU_SPROT_RGN_LA_Bits',0,21,202,4,3
	.word	71010
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_UA_Bits',0,21,205,4,16,4,11
	.byte	'reserved_0',0,1
	.word	527
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	504
	.byte	27,0,2,35,0,0,28
	.byte	'Ifx_CPU_SPROT_RGN_UA_Bits',0,21,209,4,3
	.word	71117
	.byte	10
	.byte	'_Ifx_CPU_SWEVT_Bits',0,21,212,4,16,4,11
	.byte	'EVTA',0,4
	.word	1121
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	1121
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	1121
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	1121
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	1121
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	1121
	.byte	24,0,2,35,0,0,28
	.byte	'Ifx_CPU_SWEVT_Bits',0,21,220,4,3
	.word	71224
	.byte	10
	.byte	'_Ifx_CPU_SYSCON_Bits',0,21,223,4,16,4,11
	.byte	'FCDSF',0,4
	.word	1121
	.byte	1,31,2,35,0,11
	.byte	'PROTEN',0,4
	.word	1121
	.byte	1,30,2,35,0,11
	.byte	'TPROTEN',0,4
	.word	1121
	.byte	1,29,2,35,0,11
	.byte	'IS',0,4
	.word	1121
	.byte	1,28,2,35,0,11
	.byte	'IT',0,4
	.word	1121
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	1121
	.byte	27,0,2,35,0,0,28
	.byte	'Ifx_CPU_SYSCON_Bits',0,21,231,4,3
	.word	71378
	.byte	10
	.byte	'_Ifx_CPU_TASK_ASI_Bits',0,21,234,4,16,4,11
	.byte	'ASI',0,4
	.word	1121
	.byte	5,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	1121
	.byte	27,0,2,35,0,0,28
	.byte	'Ifx_CPU_TASK_ASI_Bits',0,21,238,4,3
	.word	71539
	.byte	10
	.byte	'_Ifx_CPU_TPS_CON_Bits',0,21,241,4,16,4,11
	.byte	'TEXP0',0,4
	.word	1121
	.byte	1,31,2,35,0,11
	.byte	'TEXP1',0,4
	.word	1121
	.byte	1,30,2,35,0,11
	.byte	'TEXP2',0,4
	.word	1121
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	1121
	.byte	13,16,2,35,0,11
	.byte	'TTRAP',0,4
	.word	1121
	.byte	1,15,2,35,0,11
	.byte	'reserved_17',0,4
	.word	1121
	.byte	15,0,2,35,0,0,28
	.byte	'Ifx_CPU_TPS_CON_Bits',0,21,249,4,3
	.word	71637
	.byte	10
	.byte	'_Ifx_CPU_TPS_TIMER_Bits',0,21,252,4,16,4,11
	.byte	'Timer',0,4
	.word	1121
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_CPU_TPS_TIMER_Bits',0,21,255,4,3
	.word	71809
	.byte	10
	.byte	'_Ifx_CPU_TR_ADR_Bits',0,21,130,5,16,4,11
	.byte	'ADDR',0,4
	.word	1121
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_CPU_TR_ADR_Bits',0,21,133,5,3
	.word	71889
	.byte	10
	.byte	'_Ifx_CPU_TR_EVT_Bits',0,21,136,5,16,4,11
	.byte	'EVTA',0,4
	.word	1121
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	1121
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	1121
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	1121
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	1121
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	1121
	.byte	4,20,2,35,0,11
	.byte	'TYP',0,4
	.word	1121
	.byte	1,19,2,35,0,11
	.byte	'RNG',0,4
	.word	1121
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	1121
	.byte	1,17,2,35,0,11
	.byte	'ASI_EN',0,4
	.word	1121
	.byte	1,16,2,35,0,11
	.byte	'ASI',0,4
	.word	1121
	.byte	5,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	1121
	.byte	6,5,2,35,0,11
	.byte	'AST',0,4
	.word	1121
	.byte	1,4,2,35,0,11
	.byte	'ALD',0,4
	.word	1121
	.byte	1,3,2,35,0,11
	.byte	'reserved_29',0,4
	.word	1121
	.byte	3,0,2,35,0,0,28
	.byte	'Ifx_CPU_TR_EVT_Bits',0,21,153,5,3
	.word	71962
	.byte	10
	.byte	'_Ifx_CPU_TRIG_ACC_Bits',0,21,156,5,16,4,11
	.byte	'T0',0,4
	.word	1121
	.byte	1,31,2,35,0,11
	.byte	'T1',0,4
	.word	1121
	.byte	1,30,2,35,0,11
	.byte	'T2',0,4
	.word	1121
	.byte	1,29,2,35,0,11
	.byte	'T3',0,4
	.word	1121
	.byte	1,28,2,35,0,11
	.byte	'T4',0,4
	.word	1121
	.byte	1,27,2,35,0,11
	.byte	'T5',0,4
	.word	1121
	.byte	1,26,2,35,0,11
	.byte	'T6',0,4
	.word	1121
	.byte	1,25,2,35,0,11
	.byte	'T7',0,4
	.word	1121
	.byte	1,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	1121
	.byte	24,0,2,35,0,0,28
	.byte	'Ifx_CPU_TRIG_ACC_Bits',0,21,167,5,3
	.word	72280
	.byte	12,21,175,5,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63498
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_A',0,21,180,5,3
	.word	72475
	.byte	12,21,183,5,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63559
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_BIV',0,21,188,5,3
	.word	72534
	.byte	12,21,191,5,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63638
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_BTV',0,21,196,5,3
	.word	72595
	.byte	12,21,199,5,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63724
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_CCNT',0,21,204,5,3
	.word	72656
	.byte	12,21,207,5,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63813
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_CCTRL',0,21,212,5,3
	.word	72718
	.byte	12,21,215,5,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63959
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_COMPAT',0,21,220,5,3
	.word	72781
	.byte	12,21,223,5,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64086
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_CORE_ID',0,21,228,5,3
	.word	72845
	.byte	12,21,231,5,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64184
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_CPR_L',0,21,236,5,3
	.word	72910
	.byte	12,21,239,5,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64277
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_CPR_U',0,21,244,5,3
	.word	72973
	.byte	12,21,247,5,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64370
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_CPU_ID',0,21,252,5,3
	.word	73036
	.byte	12,21,255,5,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64477
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_CPXE',0,21,132,6,3
	.word	73100
	.byte	12,21,135,6,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64564
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_CREVT',0,21,140,6,3
	.word	73162
	.byte	12,21,143,6,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64718
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_CUS_ID',0,21,148,6,3
	.word	73225
	.byte	12,21,151,6,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64812
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_D',0,21,156,6,3
	.word	73289
	.byte	12,21,159,6,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64875
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_DATR',0,21,164,6,3
	.word	73348
	.byte	12,21,167,6,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65093
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_DBGSR',0,21,172,6,3
	.word	73410
	.byte	12,21,175,6,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65308
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_DBGTCR',0,21,180,6,3
	.word	73473
	.byte	12,21,183,6,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65402
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_DCON0',0,21,188,6,3
	.word	73537
	.byte	12,21,191,6,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65518
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_DCON2',0,21,196,6,3
	.word	73600
	.byte	12,21,199,6,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65619
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_DCX',0,21,204,6,3
	.word	73663
	.byte	12,21,207,6,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65712
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_DEADD',0,21,212,6,3
	.word	73724
	.byte	12,21,215,6,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65792
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_DIEAR',0,21,220,6,3
	.word	73787
	.byte	12,21,223,6,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65861
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_DIETR',0,21,228,6,3
	.word	73850
	.byte	12,21,231,6,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66090
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_DMS',0,21,236,6,3
	.word	73913
	.byte	12,21,239,6,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66183
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_DPR_L',0,21,244,6,3
	.word	73974
	.byte	12,21,247,6,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66278
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_DPR_U',0,21,252,6,3
	.word	74037
	.byte	12,21,255,6,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66373
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_DPRE',0,21,132,7,3
	.word	74100
	.byte	12,21,135,7,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66463
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_DPWE',0,21,140,7,3
	.word	74162
	.byte	12,21,143,7,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66553
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_DSTR',0,21,148,7,3
	.word	74224
	.byte	12,21,151,7,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66877
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_EXEVT',0,21,156,7,3
	.word	74286
	.byte	12,21,159,7,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67031
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_FCX',0,21,164,7,3
	.word	74349
	.byte	12,21,167,7,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67137
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_FPU_TRAP_CON',0,21,172,7,3
	.word	74410
	.byte	12,21,175,7,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67486
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_FPU_TRAP_OPC',0,21,180,7,3
	.word	74480
	.byte	12,21,183,7,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67646
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_FPU_TRAP_PC',0,21,188,7,3
	.word	74550
	.byte	12,21,191,7,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67727
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_FPU_TRAP_SRC1',0,21,196,7,3
	.word	74619
	.byte	12,21,199,7,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67814
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_FPU_TRAP_SRC2',0,21,204,7,3
	.word	74690
	.byte	12,21,207,7,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67901
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_FPU_TRAP_SRC3',0,21,212,7,3
	.word	74761
	.byte	12,21,215,7,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67988
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_ICNT',0,21,220,7,3
	.word	74832
	.byte	12,21,223,7,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68079
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_ICR',0,21,228,7,3
	.word	74894
	.byte	12,21,231,7,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68222
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_ISP',0,21,236,7,3
	.word	74955
	.byte	12,21,239,7,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68288
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_LCX',0,21,244,7,3
	.word	75016
	.byte	12,21,247,7,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68394
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_M1CNT',0,21,252,7,3
	.word	75077
	.byte	12,21,255,7,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68487
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_M2CNT',0,21,132,8,3
	.word	75140
	.byte	12,21,135,8,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68580
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_M3CNT',0,21,140,8,3
	.word	75203
	.byte	12,21,143,8,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68673
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_PC',0,21,148,8,3
	.word	75266
	.byte	12,21,151,8,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68758
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_PCON0',0,21,156,8,3
	.word	75326
	.byte	12,21,159,8,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68874
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_PCON1',0,21,164,8,3
	.word	75389
	.byte	12,21,167,8,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68985
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_PCON2',0,21,172,8,3
	.word	75452
	.byte	12,21,175,8,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69086
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_PCXI',0,21,180,8,3
	.word	75515
	.byte	12,21,183,8,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69216
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_PIEAR',0,21,188,8,3
	.word	75577
	.byte	12,21,191,8,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69285
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_PIETR',0,21,196,8,3
	.word	75640
	.byte	12,21,199,8,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69514
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_PMA0',0,21,204,8,3
	.word	75703
	.byte	12,21,207,8,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69627
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_PMA1',0,21,212,8,3
	.word	75765
	.byte	12,21,215,8,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69740
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_PMA2',0,21,220,8,3
	.word	75827
	.byte	12,21,223,8,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69831
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_PSTR',0,21,228,8,3
	.word	75889
	.byte	12,21,231,8,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70034
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_PSW',0,21,236,8,3
	.word	75951
	.byte	12,21,239,8,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70277
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_SEGEN',0,21,244,8,3
	.word	76012
	.byte	12,21,247,8,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70405
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_SMACON',0,21,252,8,3
	.word	76075
	.byte	12,21,255,8,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70646
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_SPROT_ACCENA',0,21,132,9,3
	.word	76139
	.byte	12,21,135,9,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70729
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_SPROT_ACCENB',0,21,140,9,3
	.word	76209
	.byte	12,21,143,9,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70820
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA',0,21,148,9,3
	.word	76279
	.byte	12,21,151,9,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70911
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB',0,21,156,9,3
	.word	76353
	.byte	12,21,159,9,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71010
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_SPROT_RGN_LA',0,21,164,9,3
	.word	76427
	.byte	12,21,167,9,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71117
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_SPROT_RGN_UA',0,21,172,9,3
	.word	76497
	.byte	12,21,175,9,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71224
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_SWEVT',0,21,180,9,3
	.word	76567
	.byte	12,21,183,9,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71378
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_SYSCON',0,21,188,9,3
	.word	76630
	.byte	12,21,191,9,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71539
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_TASK_ASI',0,21,196,9,3
	.word	76694
	.byte	12,21,199,9,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71637
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_TPS_CON',0,21,204,9,3
	.word	76760
	.byte	12,21,207,9,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71809
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_TPS_TIMER',0,21,212,9,3
	.word	76825
	.byte	12,21,215,9,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71889
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_TR_ADR',0,21,220,9,3
	.word	76892
	.byte	12,21,223,9,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71962
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_TR_EVT',0,21,228,9,3
	.word	76956
	.byte	12,21,231,9,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72280
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_TRIG_ACC',0,21,236,9,3
	.word	77020
	.byte	10
	.byte	'_Ifx_CPU_CPR',0,21,247,9,25,8,13
	.byte	'L',0
	.word	72910
	.byte	4,2,35,0,13
	.byte	'U',0
	.word	72973
	.byte	4,2,35,4,0,14
	.word	77086
	.byte	28
	.byte	'Ifx_CPU_CPR',0,21,251,9,3
	.word	77128
	.byte	10
	.byte	'_Ifx_CPU_DPR',0,21,254,9,25,8,13
	.byte	'L',0
	.word	73974
	.byte	4,2,35,0,13
	.byte	'U',0
	.word	74037
	.byte	4,2,35,4,0,14
	.word	77154
	.byte	28
	.byte	'Ifx_CPU_DPR',0,21,130,10,3
	.word	77196
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN',0,21,133,10,25,16,13
	.byte	'LA',0
	.word	76427
	.byte	4,2,35,0,13
	.byte	'UA',0
	.word	76497
	.byte	4,2,35,4,13
	.byte	'ACCENA',0
	.word	76279
	.byte	4,2,35,8,13
	.byte	'ACCENB',0
	.word	76353
	.byte	4,2,35,12,0,14
	.word	77222
	.byte	28
	.byte	'Ifx_CPU_SPROT_RGN',0,21,139,10,3
	.word	77304
	.byte	18,12
	.word	76825
	.byte	19,2,0,10
	.byte	'_Ifx_CPU_TPS',0,21,142,10,25,16,13
	.byte	'CON',0
	.word	76760
	.byte	4,2,35,0,13
	.byte	'TIMER',0
	.word	77336
	.byte	12,2,35,4,0,14
	.word	77345
	.byte	28
	.byte	'Ifx_CPU_TPS',0,21,146,10,3
	.word	77393
	.byte	10
	.byte	'_Ifx_CPU_TR',0,21,149,10,25,8,13
	.byte	'EVT',0
	.word	76956
	.byte	4,2,35,0,13
	.byte	'ADR',0
	.word	76892
	.byte	4,2,35,4,0,14
	.word	77419
	.byte	28
	.byte	'Ifx_CPU_TR',0,21,153,10,3
	.word	77464
	.byte	18,176,32
	.word	527
	.byte	19,175,32,0,18,208,223,1
	.word	527
	.byte	19,207,223,1,0,18,248,1
	.word	527
	.byte	19,247,1,0,18,244,29
	.word	527
	.byte	19,243,29,0,18,188,3
	.word	527
	.byte	19,187,3,0,18,232,3
	.word	527
	.byte	19,231,3,0,18,252,23
	.word	527
	.byte	19,251,23,0,18,228,63
	.word	527
	.byte	19,227,63,0,18,128,1
	.word	77154
	.byte	19,15,0,14
	.word	77579
	.byte	18,128,31
	.word	527
	.byte	19,255,30,0,18,64
	.word	77086
	.byte	19,7,0,14
	.word	77605
	.byte	18,192,31
	.word	527
	.byte	19,191,31,0,18,16
	.word	73100
	.byte	19,3,0,18,16
	.word	74100
	.byte	19,3,0,18,16
	.word	74162
	.byte	19,3,0,18,208,7
	.word	527
	.byte	19,207,7,0,14
	.word	77345
	.byte	18,240,23
	.word	527
	.byte	19,239,23,0,18,64
	.word	77419
	.byte	19,7,0,14
	.word	77684
	.byte	18,192,23
	.word	527
	.byte	19,191,23,0,18,232,1
	.word	527
	.byte	19,231,1,0,18,180,1
	.word	527
	.byte	19,179,1,0,18,172,1
	.word	527
	.byte	19,171,1,0,18,64
	.word	73289
	.byte	19,15,0,18,64
	.word	527
	.byte	19,63,0,18,64
	.word	72475
	.byte	19,15,0,10
	.byte	'_Ifx_CPU',0,21,166,10,25,128,128,4,13
	.byte	'reserved_0',0
	.word	77489
	.byte	176,32,2,35,0,13
	.byte	'SEGEN',0
	.word	76012
	.byte	4,3,35,176,32,13
	.byte	'reserved_1034',0
	.word	77500
	.byte	208,223,1,3,35,180,32,13
	.byte	'TASK_ASI',0
	.word	76694
	.byte	4,4,35,132,128,2,13
	.byte	'reserved_8008',0
	.word	77513
	.byte	248,1,4,35,136,128,2,13
	.byte	'PMA0',0
	.word	75703
	.byte	4,4,35,128,130,2,13
	.byte	'PMA1',0
	.word	75765
	.byte	4,4,35,132,130,2,13
	.byte	'PMA2',0
	.word	75827
	.byte	4,4,35,136,130,2,13
	.byte	'reserved_810C',0
	.word	77524
	.byte	244,29,4,35,140,130,2,13
	.byte	'DCON2',0
	.word	73600
	.byte	4,4,35,128,160,2,13
	.byte	'reserved_9004',0
	.word	5257
	.byte	8,4,35,132,160,2,13
	.byte	'SMACON',0
	.word	76075
	.byte	4,4,35,140,160,2,13
	.byte	'DSTR',0
	.word	74224
	.byte	4,4,35,144,160,2,13
	.byte	'reserved_9014',0
	.word	3438
	.byte	4,4,35,148,160,2,13
	.byte	'DATR',0
	.word	73348
	.byte	4,4,35,152,160,2,13
	.byte	'DEADD',0
	.word	73724
	.byte	4,4,35,156,160,2,13
	.byte	'DIEAR',0
	.word	73787
	.byte	4,4,35,160,160,2,13
	.byte	'DIETR',0
	.word	73850
	.byte	4,4,35,164,160,2,13
	.byte	'reserved_9028',0
	.word	4628
	.byte	24,4,35,168,160,2,13
	.byte	'DCON0',0
	.word	73537
	.byte	4,4,35,192,160,2,13
	.byte	'reserved_9044',0
	.word	77535
	.byte	188,3,4,35,196,160,2,13
	.byte	'PSTR',0
	.word	75889
	.byte	4,4,35,128,164,2,13
	.byte	'PCON1',0
	.word	75389
	.byte	4,4,35,132,164,2,13
	.byte	'PCON2',0
	.word	75452
	.byte	4,4,35,136,164,2,13
	.byte	'PCON0',0
	.word	75326
	.byte	4,4,35,140,164,2,13
	.byte	'PIEAR',0
	.word	75577
	.byte	4,4,35,144,164,2,13
	.byte	'PIETR',0
	.word	75640
	.byte	4,4,35,148,164,2,13
	.byte	'reserved_9218',0
	.word	77546
	.byte	232,3,4,35,152,164,2,13
	.byte	'COMPAT',0
	.word	72781
	.byte	4,4,35,128,168,2,13
	.byte	'reserved_9404',0
	.word	77557
	.byte	252,23,4,35,132,168,2,13
	.byte	'FPU_TRAP_CON',0
	.word	74410
	.byte	4,4,35,128,192,2,13
	.byte	'FPU_TRAP_PC',0
	.word	74550
	.byte	4,4,35,132,192,2,13
	.byte	'FPU_TRAP_OPC',0
	.word	74480
	.byte	4,4,35,136,192,2,13
	.byte	'reserved_A00C',0
	.word	3438
	.byte	4,4,35,140,192,2,13
	.byte	'FPU_TRAP_SRC1',0
	.word	74619
	.byte	4,4,35,144,192,2,13
	.byte	'FPU_TRAP_SRC2',0
	.word	74690
	.byte	4,4,35,148,192,2,13
	.byte	'FPU_TRAP_SRC3',0
	.word	74761
	.byte	4,4,35,152,192,2,13
	.byte	'reserved_A01C',0
	.word	77568
	.byte	228,63,4,35,156,192,2,13
	.byte	'DPR',0
	.word	77589
	.byte	128,1,4,35,128,128,3,13
	.byte	'reserved_C080',0
	.word	77594
	.byte	128,31,4,35,128,129,3,13
	.byte	'CPR',0
	.word	77614
	.byte	64,4,35,128,160,3,13
	.byte	'reserved_D040',0
	.word	77619
	.byte	192,31,4,35,192,160,3,13
	.byte	'CPXE',0
	.word	77630
	.byte	16,4,35,128,192,3,13
	.byte	'DPRE',0
	.word	77639
	.byte	16,4,35,144,192,3,13
	.byte	'DPWE',0
	.word	77648
	.byte	16,4,35,160,192,3,13
	.byte	'reserved_E030',0
	.word	77657
	.byte	208,7,4,35,176,192,3,13
	.byte	'TPS',0
	.word	77668
	.byte	16,4,35,128,200,3,13
	.byte	'reserved_E410',0
	.word	77673
	.byte	240,23,4,35,144,200,3,13
	.byte	'TR',0
	.word	77693
	.byte	64,4,35,128,224,3,13
	.byte	'reserved_F040',0
	.word	77698
	.byte	192,23,4,35,192,224,3,13
	.byte	'CCTRL',0
	.word	72718
	.byte	4,4,35,128,248,3,13
	.byte	'CCNT',0
	.word	72656
	.byte	4,4,35,132,248,3,13
	.byte	'ICNT',0
	.word	74832
	.byte	4,4,35,136,248,3,13
	.byte	'M1CNT',0
	.word	75077
	.byte	4,4,35,140,248,3,13
	.byte	'M2CNT',0
	.word	75140
	.byte	4,4,35,144,248,3,13
	.byte	'M3CNT',0
	.word	75203
	.byte	4,4,35,148,248,3,13
	.byte	'reserved_FC18',0
	.word	77709
	.byte	232,1,4,35,152,248,3,13
	.byte	'DBGSR',0
	.word	73410
	.byte	4,4,35,128,250,3,13
	.byte	'reserved_FD04',0
	.word	3438
	.byte	4,4,35,132,250,3,13
	.byte	'EXEVT',0
	.word	74286
	.byte	4,4,35,136,250,3,13
	.byte	'CREVT',0
	.word	73162
	.byte	4,4,35,140,250,3,13
	.byte	'SWEVT',0
	.word	76567
	.byte	4,4,35,144,250,3,13
	.byte	'reserved_FD14',0
	.word	61448
	.byte	28,4,35,148,250,3,13
	.byte	'TRIG_ACC',0
	.word	77020
	.byte	4,4,35,176,250,3,13
	.byte	'reserved_FD34',0
	.word	5597
	.byte	12,4,35,180,250,3,13
	.byte	'DMS',0
	.word	73913
	.byte	4,4,35,192,250,3,13
	.byte	'DCX',0
	.word	73663
	.byte	4,4,35,196,250,3,13
	.byte	'DBGTCR',0
	.word	73473
	.byte	4,4,35,200,250,3,13
	.byte	'reserved_FD4C',0
	.word	77720
	.byte	180,1,4,35,204,250,3,13
	.byte	'PCXI',0
	.word	75515
	.byte	4,4,35,128,252,3,13
	.byte	'PSW',0
	.word	75951
	.byte	4,4,35,132,252,3,13
	.byte	'PC',0
	.word	75266
	.byte	4,4,35,136,252,3,13
	.byte	'reserved_FE0C',0
	.word	5257
	.byte	8,4,35,140,252,3,13
	.byte	'SYSCON',0
	.word	76630
	.byte	4,4,35,148,252,3,13
	.byte	'CPU_ID',0
	.word	73036
	.byte	4,4,35,152,252,3,13
	.byte	'CORE_ID',0
	.word	72845
	.byte	4,4,35,156,252,3,13
	.byte	'BIV',0
	.word	72534
	.byte	4,4,35,160,252,3,13
	.byte	'BTV',0
	.word	72595
	.byte	4,4,35,164,252,3,13
	.byte	'ISP',0
	.word	74955
	.byte	4,4,35,168,252,3,13
	.byte	'ICR',0
	.word	74894
	.byte	4,4,35,172,252,3,13
	.byte	'reserved_FE30',0
	.word	5257
	.byte	8,4,35,176,252,3,13
	.byte	'FCX',0
	.word	74349
	.byte	4,4,35,184,252,3,13
	.byte	'LCX',0
	.word	75016
	.byte	4,4,35,188,252,3,13
	.byte	'reserved_FE40',0
	.word	31999
	.byte	16,4,35,192,252,3,13
	.byte	'CUS_ID',0
	.word	73225
	.byte	4,4,35,208,252,3,13
	.byte	'reserved_FE54',0
	.word	77731
	.byte	172,1,4,35,212,252,3,13
	.byte	'D',0
	.word	77742
	.byte	64,4,35,128,254,3,13
	.byte	'reserved_FF40',0
	.word	77751
	.byte	64,4,35,192,254,3,13
	.byte	'A',0
	.word	77760
	.byte	64,4,35,128,255,3,13
	.byte	'reserved_FFC0',0
	.word	77751
	.byte	64,4,35,192,255,3,0,14
	.word	77769
	.byte	28
	.byte	'Ifx_CPU',0,21,130,11,3
	.word	79560
	.byte	15,9,127,9,1,16
	.byte	'IfxCpu_Id_0',0,0,16
	.byte	'IfxCpu_Id_1',0,1,16
	.byte	'IfxCpu_Id_none',0,2,0,28
	.byte	'IfxCpu_Id',0,9,132,1,3
	.word	79582
	.byte	28
	.byte	'IfxCpu_ResourceCpu',0,9,161,1,3
	.word	2046
	.byte	10
	.byte	'_Ifx_STM_ACCEN0_Bits',0,22,45,16,4,11
	.byte	'EN0',0,1
	.word	527
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	527
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	527
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	527
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	527
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	527
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	527
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	527
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	527
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	527
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	527
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	527
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	527
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	527
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	527
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	527
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	527
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	527
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	527
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	527
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	527
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	527
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	527
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	527
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	527
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	527
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	527
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	527
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	527
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	527
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	527
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	527
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_STM_ACCEN0_Bits',0,22,79,3
	.word	79680
	.byte	10
	.byte	'_Ifx_STM_ACCEN1_Bits',0,22,82,16,4,11
	.byte	'reserved_0',0,4
	.word	504
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_STM_ACCEN1_Bits',0,22,85,3
	.word	80237
	.byte	10
	.byte	'_Ifx_STM_CAP_Bits',0,22,88,16,4,11
	.byte	'STMCAP63_32',0,4
	.word	504
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_STM_CAP_Bits',0,22,91,3
	.word	80314
	.byte	10
	.byte	'_Ifx_STM_CAPSV_Bits',0,22,94,16,4,11
	.byte	'STMCAP63_32',0,4
	.word	504
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_STM_CAPSV_Bits',0,22,97,3
	.word	80386
	.byte	10
	.byte	'_Ifx_STM_CLC_Bits',0,22,100,16,4,11
	.byte	'DISR',0,1
	.word	527
	.byte	1,7,2,35,0,11
	.byte	'DISS',0,1
	.word	527
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	527
	.byte	1,5,2,35,0,11
	.byte	'EDIS',0,1
	.word	527
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	504
	.byte	28,0,2,35,0,0,28
	.byte	'Ifx_STM_CLC_Bits',0,22,107,3
	.word	80462
	.byte	10
	.byte	'_Ifx_STM_CMCON_Bits',0,22,110,16,4,11
	.byte	'MSIZE0',0,1
	.word	527
	.byte	5,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	527
	.byte	3,0,2,35,0,11
	.byte	'MSTART0',0,1
	.word	527
	.byte	5,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	527
	.byte	3,0,2,35,1,11
	.byte	'MSIZE1',0,1
	.word	527
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	527
	.byte	3,0,2,35,2,11
	.byte	'MSTART1',0,1
	.word	527
	.byte	5,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	527
	.byte	3,0,2,35,3,0,28
	.byte	'Ifx_STM_CMCON_Bits',0,22,120,3
	.word	80603
	.byte	10
	.byte	'_Ifx_STM_CMP_Bits',0,22,123,16,4,11
	.byte	'CMPVAL',0,4
	.word	504
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_STM_CMP_Bits',0,22,126,3
	.word	80821
	.byte	10
	.byte	'_Ifx_STM_ICR_Bits',0,22,129,1,16,4,11
	.byte	'CMP0EN',0,1
	.word	527
	.byte	1,7,2,35,0,11
	.byte	'CMP0IR',0,1
	.word	527
	.byte	1,6,2,35,0,11
	.byte	'CMP0OS',0,1
	.word	527
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	527
	.byte	1,4,2,35,0,11
	.byte	'CMP1EN',0,1
	.word	527
	.byte	1,3,2,35,0,11
	.byte	'CMP1IR',0,1
	.word	527
	.byte	1,2,2,35,0,11
	.byte	'CMP1OS',0,1
	.word	527
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,4
	.word	504
	.byte	25,0,2,35,0,0,28
	.byte	'Ifx_STM_ICR_Bits',0,22,139,1,3
	.word	80888
	.byte	10
	.byte	'_Ifx_STM_ID_Bits',0,22,142,1,16,4,11
	.byte	'MODREV',0,1
	.word	527
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	527
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	1022
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_STM_ID_Bits',0,22,147,1,3
	.word	81091
	.byte	10
	.byte	'_Ifx_STM_ISCR_Bits',0,22,150,1,16,4,11
	.byte	'CMP0IRR',0,1
	.word	527
	.byte	1,7,2,35,0,11
	.byte	'CMP0IRS',0,1
	.word	527
	.byte	1,6,2,35,0,11
	.byte	'CMP1IRR',0,1
	.word	527
	.byte	1,5,2,35,0,11
	.byte	'CMP1IRS',0,1
	.word	527
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	504
	.byte	28,0,2,35,0,0,28
	.byte	'Ifx_STM_ISCR_Bits',0,22,157,1,3
	.word	81198
	.byte	10
	.byte	'_Ifx_STM_KRST0_Bits',0,22,160,1,16,4,11
	.byte	'RST',0,1
	.word	527
	.byte	1,7,2,35,0,11
	.byte	'RSTSTAT',0,1
	.word	527
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	504
	.byte	30,0,2,35,0,0,28
	.byte	'Ifx_STM_KRST0_Bits',0,22,165,1,3
	.word	81349
	.byte	10
	.byte	'_Ifx_STM_KRST1_Bits',0,22,168,1,16,4,11
	.byte	'RST',0,1
	.word	527
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	504
	.byte	31,0,2,35,0,0,28
	.byte	'Ifx_STM_KRST1_Bits',0,22,172,1,3
	.word	81460
	.byte	10
	.byte	'_Ifx_STM_KRSTCLR_Bits',0,22,175,1,16,4,11
	.byte	'CLR',0,1
	.word	527
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	504
	.byte	31,0,2,35,0,0,28
	.byte	'Ifx_STM_KRSTCLR_Bits',0,22,179,1,3
	.word	81552
	.byte	10
	.byte	'_Ifx_STM_OCS_Bits',0,22,182,1,16,4,11
	.byte	'reserved_0',0,4
	.word	504
	.byte	24,8,2,35,0,11
	.byte	'SUS',0,1
	.word	527
	.byte	4,4,2,35,3,11
	.byte	'SUS_P',0,1
	.word	527
	.byte	1,3,2,35,3,11
	.byte	'SUSSTA',0,1
	.word	527
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	527
	.byte	2,0,2,35,3,0,28
	.byte	'Ifx_STM_OCS_Bits',0,22,189,1,3
	.word	81648
	.byte	10
	.byte	'_Ifx_STM_TIM0_Bits',0,22,192,1,16,4,11
	.byte	'STM31_0',0,4
	.word	504
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_STM_TIM0_Bits',0,22,195,1,3
	.word	81794
	.byte	10
	.byte	'_Ifx_STM_TIM0SV_Bits',0,22,198,1,16,4,11
	.byte	'STM31_0',0,4
	.word	504
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_STM_TIM0SV_Bits',0,22,201,1,3
	.word	81866
	.byte	10
	.byte	'_Ifx_STM_TIM1_Bits',0,22,204,1,16,4,11
	.byte	'STM35_4',0,4
	.word	504
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_STM_TIM1_Bits',0,22,207,1,3
	.word	81942
	.byte	10
	.byte	'_Ifx_STM_TIM2_Bits',0,22,210,1,16,4,11
	.byte	'STM39_8',0,4
	.word	504
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_STM_TIM2_Bits',0,22,213,1,3
	.word	82014
	.byte	10
	.byte	'_Ifx_STM_TIM3_Bits',0,22,216,1,16,4,11
	.byte	'STM43_12',0,4
	.word	504
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_STM_TIM3_Bits',0,22,219,1,3
	.word	82086
	.byte	10
	.byte	'_Ifx_STM_TIM4_Bits',0,22,222,1,16,4,11
	.byte	'STM47_16',0,4
	.word	504
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_STM_TIM4_Bits',0,22,225,1,3
	.word	82159
	.byte	10
	.byte	'_Ifx_STM_TIM5_Bits',0,22,228,1,16,4,11
	.byte	'STM51_20',0,4
	.word	504
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_STM_TIM5_Bits',0,22,231,1,3
	.word	82232
	.byte	10
	.byte	'_Ifx_STM_TIM6_Bits',0,22,234,1,16,4,11
	.byte	'STM63_32',0,4
	.word	504
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_STM_TIM6_Bits',0,22,237,1,3
	.word	82305
	.byte	12,22,245,1,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	79680
	.byte	4,2,35,0,0,28
	.byte	'Ifx_STM_ACCEN0',0,22,250,1,3
	.word	82378
	.byte	12,22,253,1,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	80237
	.byte	4,2,35,0,0,28
	.byte	'Ifx_STM_ACCEN1',0,22,130,2,3
	.word	82442
	.byte	12,22,133,2,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	80314
	.byte	4,2,35,0,0,28
	.byte	'Ifx_STM_CAP',0,22,138,2,3
	.word	82506
	.byte	12,22,141,2,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	80386
	.byte	4,2,35,0,0,28
	.byte	'Ifx_STM_CAPSV',0,22,146,2,3
	.word	82567
	.byte	12,22,149,2,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	80462
	.byte	4,2,35,0,0,28
	.byte	'Ifx_STM_CLC',0,22,154,2,3
	.word	82630
	.byte	12,22,157,2,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	80603
	.byte	4,2,35,0,0,28
	.byte	'Ifx_STM_CMCON',0,22,162,2,3
	.word	82691
	.byte	12,22,165,2,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	80821
	.byte	4,2,35,0,0,28
	.byte	'Ifx_STM_CMP',0,22,170,2,3
	.word	82754
	.byte	12,22,173,2,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	80888
	.byte	4,2,35,0,0,28
	.byte	'Ifx_STM_ICR',0,22,178,2,3
	.word	82815
	.byte	12,22,181,2,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81091
	.byte	4,2,35,0,0,28
	.byte	'Ifx_STM_ID',0,22,186,2,3
	.word	82876
	.byte	12,22,189,2,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81198
	.byte	4,2,35,0,0,28
	.byte	'Ifx_STM_ISCR',0,22,194,2,3
	.word	82936
	.byte	12,22,197,2,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81349
	.byte	4,2,35,0,0,28
	.byte	'Ifx_STM_KRST0',0,22,202,2,3
	.word	82998
	.byte	12,22,205,2,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81460
	.byte	4,2,35,0,0,28
	.byte	'Ifx_STM_KRST1',0,22,210,2,3
	.word	83061
	.byte	12,22,213,2,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81552
	.byte	4,2,35,0,0,28
	.byte	'Ifx_STM_KRSTCLR',0,22,218,2,3
	.word	83124
	.byte	12,22,221,2,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81648
	.byte	4,2,35,0,0,28
	.byte	'Ifx_STM_OCS',0,22,226,2,3
	.word	83189
	.byte	12,22,229,2,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81794
	.byte	4,2,35,0,0,28
	.byte	'Ifx_STM_TIM0',0,22,234,2,3
	.word	83250
	.byte	12,22,237,2,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81866
	.byte	4,2,35,0,0,28
	.byte	'Ifx_STM_TIM0SV',0,22,242,2,3
	.word	83312
	.byte	12,22,245,2,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81942
	.byte	4,2,35,0,0,28
	.byte	'Ifx_STM_TIM1',0,22,250,2,3
	.word	83376
	.byte	12,22,253,2,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	82014
	.byte	4,2,35,0,0,28
	.byte	'Ifx_STM_TIM2',0,22,130,3,3
	.word	83438
	.byte	12,22,133,3,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	82086
	.byte	4,2,35,0,0,28
	.byte	'Ifx_STM_TIM3',0,22,138,3,3
	.word	83500
	.byte	12,22,141,3,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	82159
	.byte	4,2,35,0,0,28
	.byte	'Ifx_STM_TIM4',0,22,146,3,3
	.word	83562
	.byte	12,22,149,3,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	82232
	.byte	4,2,35,0,0,28
	.byte	'Ifx_STM_TIM5',0,22,154,3,3
	.word	83624
	.byte	12,22,157,3,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	82305
	.byte	4,2,35,0,0,28
	.byte	'Ifx_STM_TIM6',0,22,162,3,3
	.word	83686
	.byte	15,8,144,1,9,1,16
	.byte	'IfxCpu_CounterMode_normal',0,0,16
	.byte	'IfxCpu_CounterMode_task',0,1,0,28
	.byte	'IfxCpu_CounterMode',0,8,148,1,3
	.word	83748
	.byte	20,8,160,1,9,6,13
	.byte	'counter',0
	.word	2201
	.byte	4,2,35,0,13
	.byte	'overlfow',0
	.word	527
	.byte	1,2,35,4,0,28
	.byte	'IfxCpu_Counter',0,8,164,1,3
	.word	83837
	.byte	20,8,172,1,9,32,13
	.byte	'instruction',0
	.word	83837
	.byte	6,2,35,0,13
	.byte	'clock',0
	.word	83837
	.byte	6,2,35,6,13
	.byte	'counter1',0
	.word	83837
	.byte	6,2,35,12,13
	.byte	'counter2',0
	.word	83837
	.byte	6,2,35,18,13
	.byte	'counter3',0
	.word	83837
	.byte	6,2,35,24,0,28
	.byte	'IfxCpu_Perf',0,8,179,1,3
	.word	83903
	.byte	10
	.byte	'_Ifx_FLASH_ACCEN0_Bits',0,23,45,16,4,11
	.byte	'EN0',0,1
	.word	527
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	527
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	527
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	527
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	527
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	527
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	527
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	527
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	527
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	527
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	527
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	527
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	527
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	527
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	527
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	527
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	527
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	527
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	527
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	527
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	527
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	527
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	527
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	527
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	527
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	527
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	527
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	527
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	527
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	527
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	527
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	527
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_FLASH_ACCEN0_Bits',0,23,79,3
	.word	84021
	.byte	10
	.byte	'_Ifx_FLASH_ACCEN1_Bits',0,23,82,16,4,11
	.byte	'reserved_0',0,4
	.word	504
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_FLASH_ACCEN1_Bits',0,23,85,3
	.word	84582
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_CFG_Bits',0,23,88,16,4,11
	.byte	'SEL',0,1
	.word	527
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	527
	.byte	2,0,2,35,0,11
	.byte	'CLR',0,1
	.word	527
	.byte	1,7,2,35,1,11
	.byte	'DIS',0,1
	.word	527
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	504
	.byte	22,0,2,35,0,0,28
	.byte	'Ifx_FLASH_CBAB_CFG_Bits',0,23,95,3
	.word	84663
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_STAT_Bits',0,23,98,16,4,11
	.byte	'VLD0',0,1
	.word	527
	.byte	1,7,2,35,0,11
	.byte	'VLD1',0,1
	.word	527
	.byte	1,6,2,35,0,11
	.byte	'VLD2',0,1
	.word	527
	.byte	1,5,2,35,0,11
	.byte	'VLD3',0,1
	.word	527
	.byte	1,4,2,35,0,11
	.byte	'VLD4',0,1
	.word	527
	.byte	1,3,2,35,0,11
	.byte	'VLD5',0,1
	.word	527
	.byte	1,2,2,35,0,11
	.byte	'VLD6',0,1
	.word	527
	.byte	1,1,2,35,0,11
	.byte	'VLD7',0,1
	.word	527
	.byte	1,0,2,35,0,11
	.byte	'VLD8',0,1
	.word	527
	.byte	1,7,2,35,1,11
	.byte	'VLD9',0,1
	.word	527
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	504
	.byte	22,0,2,35,0,0,28
	.byte	'Ifx_FLASH_CBAB_STAT_Bits',0,23,111,3
	.word	84816
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_TOP_Bits',0,23,114,16,4,11
	.byte	'reserved_0',0,1
	.word	527
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	504
	.byte	19,8,2,35,0,11
	.byte	'ERR',0,1
	.word	527
	.byte	6,2,2,35,3,11
	.byte	'VLD',0,1
	.word	527
	.byte	1,1,2,35,3,11
	.byte	'CLR',0,1
	.word	527
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_FLASH_CBAB_TOP_Bits',0,23,121,3
	.word	85064
	.byte	10
	.byte	'_Ifx_FLASH_COMM0_Bits',0,23,124,16,4,11
	.byte	'STATUS',0,1
	.word	527
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	504
	.byte	24,0,2,35,0,0,28
	.byte	'Ifx_FLASH_COMM0_Bits',0,23,128,1,3
	.word	85210
	.byte	10
	.byte	'_Ifx_FLASH_COMM1_Bits',0,23,131,1,16,4,11
	.byte	'STATUS',0,1
	.word	527
	.byte	8,0,2,35,0,11
	.byte	'DATA',0,1
	.word	527
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1022
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_FLASH_COMM1_Bits',0,23,136,1,3
	.word	85308
	.byte	10
	.byte	'_Ifx_FLASH_COMM2_Bits',0,23,139,1,16,4,11
	.byte	'STATUS',0,1
	.word	527
	.byte	8,0,2,35,0,11
	.byte	'DATA',0,1
	.word	527
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1022
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_FLASH_COMM2_Bits',0,23,144,1,3
	.word	85424
	.byte	10
	.byte	'_Ifx_FLASH_ECCRD_Bits',0,23,147,1,16,4,11
	.byte	'RCODE',0,4
	.word	504
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	1022
	.byte	8,2,2,35,2,11
	.byte	'EDCERRINJ',0,1
	.word	527
	.byte	1,1,2,35,3,11
	.byte	'ECCORDIS',0,1
	.word	527
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_FLASH_ECCRD_Bits',0,23,153,1,3
	.word	85540
	.byte	10
	.byte	'_Ifx_FLASH_ECCRP_Bits',0,23,156,1,16,4,11
	.byte	'RCODE',0,4
	.word	504
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	1022
	.byte	8,2,2,35,2,11
	.byte	'EDCERRINJ',0,1
	.word	527
	.byte	1,1,2,35,3,11
	.byte	'ECCORDIS',0,1
	.word	527
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_FLASH_ECCRP_Bits',0,23,162,1,3
	.word	85680
	.byte	10
	.byte	'_Ifx_FLASH_ECCW_Bits',0,23,165,1,16,4,11
	.byte	'WCODE',0,4
	.word	504
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	1022
	.byte	8,2,2,35,2,11
	.byte	'DECENCDIS',0,1
	.word	527
	.byte	1,1,2,35,3,11
	.byte	'PECENCDIS',0,1
	.word	527
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_FLASH_ECCW_Bits',0,23,171,1,3
	.word	85820
	.byte	10
	.byte	'_Ifx_FLASH_FCON_Bits',0,23,174,1,16,4,11
	.byte	'WSPFLASH',0,1
	.word	527
	.byte	4,4,2,35,0,11
	.byte	'WSECPF',0,1
	.word	527
	.byte	2,2,2,35,0,11
	.byte	'WSDFLASH',0,2
	.word	1022
	.byte	6,4,2,35,0,11
	.byte	'WSECDF',0,1
	.word	527
	.byte	3,1,2,35,1,11
	.byte	'IDLE',0,1
	.word	527
	.byte	1,0,2,35,1,11
	.byte	'ESLDIS',0,1
	.word	527
	.byte	1,7,2,35,2,11
	.byte	'SLEEP',0,1
	.word	527
	.byte	1,6,2,35,2,11
	.byte	'NSAFECC',0,1
	.word	527
	.byte	1,5,2,35,2,11
	.byte	'STALL',0,1
	.word	527
	.byte	1,4,2,35,2,11
	.byte	'RES21',0,1
	.word	527
	.byte	2,2,2,35,2,11
	.byte	'RES23',0,1
	.word	527
	.byte	2,0,2,35,2,11
	.byte	'VOPERM',0,1
	.word	527
	.byte	1,7,2,35,3,11
	.byte	'SQERM',0,1
	.word	527
	.byte	1,6,2,35,3,11
	.byte	'PROERM',0,1
	.word	527
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	527
	.byte	3,2,2,35,3,11
	.byte	'PR5V',0,1
	.word	527
	.byte	1,1,2,35,3,11
	.byte	'EOBM',0,1
	.word	527
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_FLASH_FCON_Bits',0,23,193,1,3
	.word	85959
	.byte	10
	.byte	'_Ifx_FLASH_FPRO_Bits',0,23,196,1,16,4,11
	.byte	'PROINP',0,1
	.word	527
	.byte	1,7,2,35,0,11
	.byte	'PRODISP',0,1
	.word	527
	.byte	1,6,2,35,0,11
	.byte	'PROIND',0,1
	.word	527
	.byte	1,5,2,35,0,11
	.byte	'PRODISD',0,1
	.word	527
	.byte	1,4,2,35,0,11
	.byte	'PROINHSMCOTP',0,1
	.word	527
	.byte	1,3,2,35,0,11
	.byte	'RES5',0,1
	.word	527
	.byte	1,2,2,35,0,11
	.byte	'PROINOTP',0,1
	.word	527
	.byte	1,1,2,35,0,11
	.byte	'RES7',0,1
	.word	527
	.byte	1,0,2,35,0,11
	.byte	'PROINDBG',0,1
	.word	527
	.byte	1,7,2,35,1,11
	.byte	'PRODISDBG',0,1
	.word	527
	.byte	1,6,2,35,1,11
	.byte	'PROINHSM',0,1
	.word	527
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	527
	.byte	5,0,2,35,1,11
	.byte	'DCFP',0,1
	.word	527
	.byte	1,7,2,35,2,11
	.byte	'DDFP',0,1
	.word	527
	.byte	1,6,2,35,2,11
	.byte	'DDFPX',0,1
	.word	527
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	527
	.byte	1,4,2,35,2,11
	.byte	'DDFD',0,1
	.word	527
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	527
	.byte	1,2,2,35,2,11
	.byte	'ENPE',0,1
	.word	527
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	527
	.byte	8,0,2,35,3,0,28
	.byte	'Ifx_FLASH_FPRO_Bits',0,23,218,1,3
	.word	86321
	.byte	10
	.byte	'_Ifx_FLASH_FSR_Bits',0,23,221,1,16,4,11
	.byte	'FABUSY',0,1
	.word	527
	.byte	1,7,2,35,0,11
	.byte	'D0BUSY',0,1
	.word	527
	.byte	1,6,2,35,0,11
	.byte	'RES1',0,1
	.word	527
	.byte	1,5,2,35,0,11
	.byte	'P0BUSY',0,1
	.word	527
	.byte	1,4,2,35,0,11
	.byte	'P1BUSY',0,1
	.word	527
	.byte	1,3,2,35,0,11
	.byte	'RES5',0,1
	.word	527
	.byte	1,2,2,35,0,11
	.byte	'RES6',0,1
	.word	527
	.byte	1,1,2,35,0,11
	.byte	'PROG',0,1
	.word	527
	.byte	1,0,2,35,0,11
	.byte	'ERASE',0,1
	.word	527
	.byte	1,7,2,35,1,11
	.byte	'PFPAGE',0,1
	.word	527
	.byte	1,6,2,35,1,11
	.byte	'DFPAGE',0,1
	.word	527
	.byte	1,5,2,35,1,11
	.byte	'OPER',0,1
	.word	527
	.byte	1,4,2,35,1,11
	.byte	'SQER',0,1
	.word	527
	.byte	1,3,2,35,1,11
	.byte	'PROER',0,1
	.word	527
	.byte	1,2,2,35,1,11
	.byte	'PFSBER',0,1
	.word	527
	.byte	1,1,2,35,1,11
	.byte	'PFDBER',0,1
	.word	527
	.byte	1,0,2,35,1,11
	.byte	'PFMBER',0,1
	.word	527
	.byte	1,7,2,35,2,11
	.byte	'RES17',0,1
	.word	527
	.byte	1,6,2,35,2,11
	.byte	'DFSBER',0,1
	.word	527
	.byte	1,5,2,35,2,11
	.byte	'DFDBER',0,1
	.word	527
	.byte	1,4,2,35,2,11
	.byte	'DFTBER',0,1
	.word	527
	.byte	1,3,2,35,2,11
	.byte	'DFMBER',0,1
	.word	527
	.byte	1,2,2,35,2,11
	.byte	'SRIADDERR',0,1
	.word	527
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	1022
	.byte	2,7,2,35,2,11
	.byte	'PVER',0,1
	.word	527
	.byte	1,6,2,35,3,11
	.byte	'EVER',0,1
	.word	527
	.byte	1,5,2,35,3,11
	.byte	'SPND',0,1
	.word	527
	.byte	1,4,2,35,3,11
	.byte	'SLM',0,1
	.word	527
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	527
	.byte	1,2,2,35,3,11
	.byte	'ORIER',0,1
	.word	527
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	527
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_FLASH_FSR_Bits',0,23,254,1,3
	.word	86762
	.byte	10
	.byte	'_Ifx_FLASH_ID_Bits',0,23,129,2,16,4,11
	.byte	'MODREV',0,1
	.word	527
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	527
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	1022
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_FLASH_ID_Bits',0,23,134,2,3
	.word	87368
	.byte	10
	.byte	'_Ifx_FLASH_MARD_Bits',0,23,137,2,16,4,11
	.byte	'HMARGIN',0,1
	.word	527
	.byte	1,7,2,35,0,11
	.byte	'SELD0',0,1
	.word	527
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	527
	.byte	1,5,2,35,0,11
	.byte	'SPND',0,1
	.word	527
	.byte	1,4,2,35,0,11
	.byte	'SPNDERR',0,1
	.word	527
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,2
	.word	1022
	.byte	10,1,2,35,0,11
	.byte	'TRAPDIS',0,1
	.word	527
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1022
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_FLASH_MARD_Bits',0,23,147,2,3
	.word	87479
	.byte	10
	.byte	'_Ifx_FLASH_MARP_Bits',0,23,150,2,16,4,11
	.byte	'SELP0',0,1
	.word	527
	.byte	1,7,2,35,0,11
	.byte	'SELP1',0,1
	.word	527
	.byte	1,6,2,35,0,11
	.byte	'RES2',0,1
	.word	527
	.byte	1,5,2,35,0,11
	.byte	'RES3',0,1
	.word	527
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	1022
	.byte	11,1,2,35,0,11
	.byte	'TRAPDIS',0,1
	.word	527
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1022
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_FLASH_MARP_Bits',0,23,159,2,3
	.word	87693
	.byte	10
	.byte	'_Ifx_FLASH_PROCOND_Bits',0,23,162,2,16,4,11
	.byte	'L',0,1
	.word	527
	.byte	1,7,2,35,0,11
	.byte	'NSAFECC',0,1
	.word	527
	.byte	1,6,2,35,0,11
	.byte	'RAMIN',0,1
	.word	527
	.byte	2,4,2,35,0,11
	.byte	'RAMINSEL',0,1
	.word	527
	.byte	4,0,2,35,0,11
	.byte	'OSCCFG',0,1
	.word	527
	.byte	1,7,2,35,1,11
	.byte	'MODE',0,1
	.word	527
	.byte	2,5,2,35,1,11
	.byte	'APREN',0,1
	.word	527
	.byte	1,4,2,35,1,11
	.byte	'CAP0EN',0,1
	.word	527
	.byte	1,3,2,35,1,11
	.byte	'CAP1EN',0,1
	.word	527
	.byte	1,2,2,35,1,11
	.byte	'CAP2EN',0,1
	.word	527
	.byte	1,1,2,35,1,11
	.byte	'CAP3EN',0,1
	.word	527
	.byte	1,0,2,35,1,11
	.byte	'ESR0CNT',0,2
	.word	1022
	.byte	12,4,2,35,2,11
	.byte	'RES29',0,1
	.word	527
	.byte	2,2,2,35,3,11
	.byte	'RES30',0,1
	.word	527
	.byte	1,1,2,35,3,11
	.byte	'RPRO',0,1
	.word	527
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_FLASH_PROCOND_Bits',0,23,179,2,3
	.word	87880
	.byte	10
	.byte	'_Ifx_FLASH_PROCONDBG_Bits',0,23,182,2,16,4,11
	.byte	'OCDSDIS',0,1
	.word	527
	.byte	1,7,2,35,0,11
	.byte	'DBGIFLCK',0,1
	.word	527
	.byte	1,6,2,35,0,11
	.byte	'EDM',0,1
	.word	527
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	504
	.byte	28,0,2,35,0,0,28
	.byte	'Ifx_FLASH_PROCONDBG_Bits',0,23,188,2,3
	.word	88204
	.byte	10
	.byte	'_Ifx_FLASH_PROCONHSM_Bits',0,23,191,2,16,4,11
	.byte	'HSMDBGDIS',0,1
	.word	527
	.byte	1,7,2,35,0,11
	.byte	'DBGIFLCK',0,1
	.word	527
	.byte	1,6,2,35,0,11
	.byte	'TSTIFLCK',0,1
	.word	527
	.byte	1,5,2,35,0,11
	.byte	'HSMTSTDIS',0,1
	.word	527
	.byte	1,4,2,35,0,11
	.byte	'RES15',0,2
	.word	1022
	.byte	12,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	1022
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_FLASH_PROCONHSM_Bits',0,23,199,2,3
	.word	88347
	.byte	10
	.byte	'_Ifx_FLASH_PROCONHSMCOTP_Bits',0,23,202,2,16,4,11
	.byte	'HSMBOOTEN',0,1
	.word	527
	.byte	1,7,2,35,0,11
	.byte	'SSWWAIT',0,1
	.word	527
	.byte	1,6,2,35,0,11
	.byte	'HSMDX',0,1
	.word	527
	.byte	1,5,2,35,0,11
	.byte	'HSM6X',0,1
	.word	527
	.byte	1,4,2,35,0,11
	.byte	'HSM16X',0,1
	.word	527
	.byte	1,3,2,35,0,11
	.byte	'HSM17X',0,1
	.word	527
	.byte	1,2,2,35,0,11
	.byte	'S6ROM',0,1
	.word	527
	.byte	1,1,2,35,0,11
	.byte	'HSMENPINS',0,2
	.word	1022
	.byte	2,7,2,35,0,11
	.byte	'HSMENRES',0,1
	.word	527
	.byte	2,5,2,35,1,11
	.byte	'DESTDBG',0,1
	.word	527
	.byte	2,3,2,35,1,11
	.byte	'BLKFLAN',0,1
	.word	527
	.byte	1,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	527
	.byte	2,0,2,35,1,11
	.byte	'S16ROM',0,1
	.word	527
	.byte	1,7,2,35,2,11
	.byte	'S17ROM',0,1
	.word	527
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	1022
	.byte	14,0,2,35,2,0,28
	.byte	'Ifx_FLASH_PROCONHSMCOTP_Bits',0,23,219,2,3
	.word	88536
	.byte	10
	.byte	'_Ifx_FLASH_PROCONOTP_Bits',0,23,222,2,16,4,11
	.byte	'S0ROM',0,1
	.word	527
	.byte	1,7,2,35,0,11
	.byte	'S1ROM',0,1
	.word	527
	.byte	1,6,2,35,0,11
	.byte	'S2ROM',0,1
	.word	527
	.byte	1,5,2,35,0,11
	.byte	'S3ROM',0,1
	.word	527
	.byte	1,4,2,35,0,11
	.byte	'S4ROM',0,1
	.word	527
	.byte	1,3,2,35,0,11
	.byte	'S5ROM',0,1
	.word	527
	.byte	1,2,2,35,0,11
	.byte	'S6ROM',0,1
	.word	527
	.byte	1,1,2,35,0,11
	.byte	'S7ROM',0,1
	.word	527
	.byte	1,0,2,35,0,11
	.byte	'S8ROM',0,1
	.word	527
	.byte	1,7,2,35,1,11
	.byte	'S9ROM',0,1
	.word	527
	.byte	1,6,2,35,1,11
	.byte	'S10ROM',0,1
	.word	527
	.byte	1,5,2,35,1,11
	.byte	'S11ROM',0,1
	.word	527
	.byte	1,4,2,35,1,11
	.byte	'S12ROM',0,1
	.word	527
	.byte	1,3,2,35,1,11
	.byte	'S13ROM',0,1
	.word	527
	.byte	1,2,2,35,1,11
	.byte	'S14ROM',0,1
	.word	527
	.byte	1,1,2,35,1,11
	.byte	'S15ROM',0,1
	.word	527
	.byte	1,0,2,35,1,11
	.byte	'S16ROM',0,1
	.word	527
	.byte	1,7,2,35,2,11
	.byte	'S17ROM',0,1
	.word	527
	.byte	1,6,2,35,2,11
	.byte	'S18ROM',0,1
	.word	527
	.byte	1,5,2,35,2,11
	.byte	'S19ROM',0,1
	.word	527
	.byte	1,4,2,35,2,11
	.byte	'S20ROM',0,1
	.word	527
	.byte	1,3,2,35,2,11
	.byte	'S21ROM',0,1
	.word	527
	.byte	1,2,2,35,2,11
	.byte	'S22ROM',0,1
	.word	527
	.byte	1,1,2,35,2,11
	.byte	'S23ROM',0,1
	.word	527
	.byte	1,0,2,35,2,11
	.byte	'S24ROM',0,1
	.word	527
	.byte	1,7,2,35,3,11
	.byte	'S25ROM',0,1
	.word	527
	.byte	1,6,2,35,3,11
	.byte	'S26ROM',0,1
	.word	527
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	527
	.byte	2,3,2,35,3,11
	.byte	'BML',0,1
	.word	527
	.byte	2,1,2,35,3,11
	.byte	'TP',0,1
	.word	527
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_FLASH_PROCONOTP_Bits',0,23,254,2,3
	.word	88899
	.byte	10
	.byte	'_Ifx_FLASH_PROCONP_Bits',0,23,129,3,16,4,11
	.byte	'S0L',0,1
	.word	527
	.byte	1,7,2,35,0,11
	.byte	'S1L',0,1
	.word	527
	.byte	1,6,2,35,0,11
	.byte	'S2L',0,1
	.word	527
	.byte	1,5,2,35,0,11
	.byte	'S3L',0,1
	.word	527
	.byte	1,4,2,35,0,11
	.byte	'S4L',0,1
	.word	527
	.byte	1,3,2,35,0,11
	.byte	'S5L',0,1
	.word	527
	.byte	1,2,2,35,0,11
	.byte	'S6L',0,1
	.word	527
	.byte	1,1,2,35,0,11
	.byte	'S7L',0,1
	.word	527
	.byte	1,0,2,35,0,11
	.byte	'S8L',0,1
	.word	527
	.byte	1,7,2,35,1,11
	.byte	'S9L',0,1
	.word	527
	.byte	1,6,2,35,1,11
	.byte	'S10L',0,1
	.word	527
	.byte	1,5,2,35,1,11
	.byte	'S11L',0,1
	.word	527
	.byte	1,4,2,35,1,11
	.byte	'S12L',0,1
	.word	527
	.byte	1,3,2,35,1,11
	.byte	'S13L',0,1
	.word	527
	.byte	1,2,2,35,1,11
	.byte	'S14L',0,1
	.word	527
	.byte	1,1,2,35,1,11
	.byte	'S15L',0,1
	.word	527
	.byte	1,0,2,35,1,11
	.byte	'S16L',0,1
	.word	527
	.byte	1,7,2,35,2,11
	.byte	'S17L',0,1
	.word	527
	.byte	1,6,2,35,2,11
	.byte	'S18L',0,1
	.word	527
	.byte	1,5,2,35,2,11
	.byte	'S19L',0,1
	.word	527
	.byte	1,4,2,35,2,11
	.byte	'S20L',0,1
	.word	527
	.byte	1,3,2,35,2,11
	.byte	'S21L',0,1
	.word	527
	.byte	1,2,2,35,2,11
	.byte	'S22L',0,1
	.word	527
	.byte	1,1,2,35,2,11
	.byte	'S23L',0,1
	.word	527
	.byte	1,0,2,35,2,11
	.byte	'S24L',0,1
	.word	527
	.byte	1,7,2,35,3,11
	.byte	'S25L',0,1
	.word	527
	.byte	1,6,2,35,3,11
	.byte	'S26L',0,1
	.word	527
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	527
	.byte	4,1,2,35,3,11
	.byte	'RPRO',0,1
	.word	527
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_FLASH_PROCONP_Bits',0,23,160,3,3
	.word	89494
	.byte	10
	.byte	'_Ifx_FLASH_PROCONWOP_Bits',0,23,163,3,16,4,11
	.byte	'S0WOP',0,1
	.word	527
	.byte	1,7,2,35,0,11
	.byte	'S1WOP',0,1
	.word	527
	.byte	1,6,2,35,0,11
	.byte	'S2WOP',0,1
	.word	527
	.byte	1,5,2,35,0,11
	.byte	'S3WOP',0,1
	.word	527
	.byte	1,4,2,35,0,11
	.byte	'S4WOP',0,1
	.word	527
	.byte	1,3,2,35,0,11
	.byte	'S5WOP',0,1
	.word	527
	.byte	1,2,2,35,0,11
	.byte	'S6WOP',0,1
	.word	527
	.byte	1,1,2,35,0,11
	.byte	'S7WOP',0,1
	.word	527
	.byte	1,0,2,35,0,11
	.byte	'S8WOP',0,1
	.word	527
	.byte	1,7,2,35,1,11
	.byte	'S9WOP',0,1
	.word	527
	.byte	1,6,2,35,1,11
	.byte	'S10WOP',0,1
	.word	527
	.byte	1,5,2,35,1,11
	.byte	'S11WOP',0,1
	.word	527
	.byte	1,4,2,35,1,11
	.byte	'S12WOP',0,1
	.word	527
	.byte	1,3,2,35,1,11
	.byte	'S13WOP',0,1
	.word	527
	.byte	1,2,2,35,1,11
	.byte	'S14WOP',0,1
	.word	527
	.byte	1,1,2,35,1,11
	.byte	'S15WOP',0,1
	.word	527
	.byte	1,0,2,35,1,11
	.byte	'S16WOP',0,1
	.word	527
	.byte	1,7,2,35,2,11
	.byte	'S17WOP',0,1
	.word	527
	.byte	1,6,2,35,2,11
	.byte	'S18WOP',0,1
	.word	527
	.byte	1,5,2,35,2,11
	.byte	'S19WOP',0,1
	.word	527
	.byte	1,4,2,35,2,11
	.byte	'S20WOP',0,1
	.word	527
	.byte	1,3,2,35,2,11
	.byte	'S21WOP',0,1
	.word	527
	.byte	1,2,2,35,2,11
	.byte	'S22WOP',0,1
	.word	527
	.byte	1,1,2,35,2,11
	.byte	'S23WOP',0,1
	.word	527
	.byte	1,0,2,35,2,11
	.byte	'S24WOP',0,1
	.word	527
	.byte	1,7,2,35,3,11
	.byte	'S25WOP',0,1
	.word	527
	.byte	1,6,2,35,3,11
	.byte	'S26WOP',0,1
	.word	527
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	527
	.byte	4,1,2,35,3,11
	.byte	'DATM',0,1
	.word	527
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_FLASH_PROCONWOP_Bits',0,23,194,3,3
	.word	90018
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG0_Bits',0,23,197,3,16,4,11
	.byte	'TAG',0,1
	.word	527
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	504
	.byte	26,0,2,35,0,0,28
	.byte	'Ifx_FLASH_RDB_CFG0_Bits',0,23,201,3,3
	.word	90600
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG1_Bits',0,23,204,3,16,4,11
	.byte	'TAG',0,1
	.word	527
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	504
	.byte	26,0,2,35,0,0,28
	.byte	'Ifx_FLASH_RDB_CFG1_Bits',0,23,208,3,3
	.word	90702
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG2_Bits',0,23,211,3,16,4,11
	.byte	'TAG',0,1
	.word	527
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	504
	.byte	26,0,2,35,0,0,28
	.byte	'Ifx_FLASH_RDB_CFG2_Bits',0,23,215,3,3
	.word	90804
	.byte	10
	.byte	'_Ifx_FLASH_RRAD_Bits',0,23,218,3,16,4,11
	.byte	'reserved_0',0,1
	.word	527
	.byte	3,5,2,35,0,11
	.byte	'ADD',0,4
	.word	504
	.byte	29,0,2,35,0,0,28
	.byte	'Ifx_FLASH_RRAD_Bits',0,23,222,3,3
	.word	90906
	.byte	10
	.byte	'_Ifx_FLASH_RRCT_Bits',0,23,225,3,16,4,11
	.byte	'STRT',0,1
	.word	527
	.byte	1,7,2,35,0,11
	.byte	'STP',0,1
	.word	527
	.byte	1,6,2,35,0,11
	.byte	'BUSY',0,1
	.word	527
	.byte	1,5,2,35,0,11
	.byte	'DONE',0,1
	.word	527
	.byte	1,4,2,35,0,11
	.byte	'ERR',0,1
	.word	527
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	527
	.byte	3,0,2,35,0,11
	.byte	'EOBM',0,1
	.word	527
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	527
	.byte	7,0,2,35,1,11
	.byte	'CNT',0,2
	.word	1022
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_FLASH_RRCT_Bits',0,23,236,3,3
	.word	91000
	.byte	10
	.byte	'_Ifx_FLASH_RRD0_Bits',0,23,239,3,16,4,11
	.byte	'DATA',0,4
	.word	504
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_FLASH_RRD0_Bits',0,23,242,3,3
	.word	91210
	.byte	10
	.byte	'_Ifx_FLASH_RRD1_Bits',0,23,245,3,16,4,11
	.byte	'DATA',0,4
	.word	504
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_FLASH_RRD1_Bits',0,23,248,3,3
	.word	91283
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_CFG_Bits',0,23,251,3,16,4,11
	.byte	'SEL',0,1
	.word	527
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	527
	.byte	2,0,2,35,0,11
	.byte	'CLR',0,1
	.word	527
	.byte	1,7,2,35,1,11
	.byte	'DIS',0,1
	.word	527
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	504
	.byte	22,0,2,35,0,0,28
	.byte	'Ifx_FLASH_UBAB_CFG_Bits',0,23,130,4,3
	.word	91356
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_STAT_Bits',0,23,133,4,16,4,11
	.byte	'VLD0',0,1
	.word	527
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	504
	.byte	31,0,2,35,0,0,28
	.byte	'Ifx_FLASH_UBAB_STAT_Bits',0,23,137,4,3
	.word	91511
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_TOP_Bits',0,23,140,4,16,4,11
	.byte	'reserved_0',0,1
	.word	527
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	504
	.byte	19,8,2,35,0,11
	.byte	'ERR',0,1
	.word	527
	.byte	6,2,2,35,3,11
	.byte	'VLD',0,1
	.word	527
	.byte	1,1,2,35,3,11
	.byte	'CLR',0,1
	.word	527
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_FLASH_UBAB_TOP_Bits',0,23,147,4,3
	.word	91616
	.byte	12,23,155,4,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	84021
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_ACCEN0',0,23,160,4,3
	.word	91764
	.byte	12,23,163,4,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	84582
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_ACCEN1',0,23,168,4,3
	.word	91830
	.byte	12,23,171,4,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	84663
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_CBAB_CFG',0,23,176,4,3
	.word	91896
	.byte	12,23,179,4,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	84816
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_CBAB_STAT',0,23,184,4,3
	.word	91964
	.byte	12,23,187,4,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	85064
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_CBAB_TOP',0,23,192,4,3
	.word	92033
	.byte	12,23,195,4,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	85210
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_COMM0',0,23,200,4,3
	.word	92101
	.byte	12,23,203,4,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	85308
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_COMM1',0,23,208,4,3
	.word	92166
	.byte	12,23,211,4,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	85424
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_COMM2',0,23,216,4,3
	.word	92231
	.byte	12,23,219,4,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	85540
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_ECCRD',0,23,224,4,3
	.word	92296
	.byte	12,23,227,4,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	85680
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_ECCRP',0,23,232,4,3
	.word	92361
	.byte	12,23,235,4,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	85820
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_ECCW',0,23,240,4,3
	.word	92426
	.byte	12,23,243,4,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	85959
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_FCON',0,23,248,4,3
	.word	92490
	.byte	12,23,251,4,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	86321
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_FPRO',0,23,128,5,3
	.word	92554
	.byte	12,23,131,5,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	86762
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_FSR',0,23,136,5,3
	.word	92618
	.byte	12,23,139,5,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	87368
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_ID',0,23,144,5,3
	.word	92681
	.byte	12,23,147,5,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	87479
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_MARD',0,23,152,5,3
	.word	92743
	.byte	12,23,155,5,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	87693
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_MARP',0,23,160,5,3
	.word	92807
	.byte	12,23,163,5,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	87880
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_PROCOND',0,23,168,5,3
	.word	92871
	.byte	12,23,171,5,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	88204
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_PROCONDBG',0,23,176,5,3
	.word	92938
	.byte	12,23,179,5,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	88347
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_PROCONHSM',0,23,184,5,3
	.word	93007
	.byte	12,23,187,5,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	88536
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_PROCONHSMCOTP',0,23,192,5,3
	.word	93076
	.byte	12,23,195,5,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	88899
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_PROCONOTP',0,23,200,5,3
	.word	93149
	.byte	12,23,203,5,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	89494
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_PROCONP',0,23,208,5,3
	.word	93218
	.byte	12,23,211,5,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	90018
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_PROCONWOP',0,23,216,5,3
	.word	93285
	.byte	12,23,219,5,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	90600
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_RDB_CFG0',0,23,224,5,3
	.word	93354
	.byte	12,23,227,5,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	90702
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_RDB_CFG1',0,23,232,5,3
	.word	93422
	.byte	12,23,235,5,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	90804
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_RDB_CFG2',0,23,240,5,3
	.word	93490
	.byte	12,23,243,5,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	90906
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_RRAD',0,23,248,5,3
	.word	93558
	.byte	12,23,251,5,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	91000
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_RRCT',0,23,128,6,3
	.word	93622
	.byte	12,23,131,6,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	91210
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_RRD0',0,23,136,6,3
	.word	93686
	.byte	12,23,139,6,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	91283
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_RRD1',0,23,144,6,3
	.word	93750
	.byte	12,23,147,6,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	91356
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_UBAB_CFG',0,23,152,6,3
	.word	93814
	.byte	12,23,155,6,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	91511
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_UBAB_STAT',0,23,160,6,3
	.word	93882
	.byte	12,23,163,6,9,4,13
	.byte	'U',0
	.word	504
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	520
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	91616
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_UBAB_TOP',0,23,168,6,3
	.word	93951
	.byte	10
	.byte	'_Ifx_FLASH_CBAB',0,23,179,6,25,12,13
	.byte	'CFG',0
	.word	91896
	.byte	4,2,35,0,13
	.byte	'STAT',0
	.word	91964
	.byte	4,2,35,4,13
	.byte	'TOP',0
	.word	92033
	.byte	4,2,35,8,0,14
	.word	94019
	.byte	28
	.byte	'Ifx_FLASH_CBAB',0,23,184,6,3
	.word	94082
	.byte	10
	.byte	'_Ifx_FLASH_RDB',0,23,187,6,25,12,13
	.byte	'CFG0',0
	.word	93354
	.byte	4,2,35,0,13
	.byte	'CFG1',0
	.word	93422
	.byte	4,2,35,4,13
	.byte	'CFG2',0
	.word	93490
	.byte	4,2,35,8,0,14
	.word	94111
	.byte	28
	.byte	'Ifx_FLASH_RDB',0,23,192,6,3
	.word	94175
	.byte	10
	.byte	'_Ifx_FLASH_UBAB',0,23,195,6,25,12,13
	.byte	'CFG',0
	.word	93814
	.byte	4,2,35,0,13
	.byte	'STAT',0
	.word	93882
	.byte	4,2,35,4,13
	.byte	'TOP',0
	.word	93951
	.byte	4,2,35,8,0,14
	.word	94203
	.byte	28
	.byte	'Ifx_FLASH_UBAB',0,23,200,6,3
	.word	94266
	.byte	28
	.byte	'Ifx_P_ACCEN0_Bits',0,11,79,3
	.word	9010
	.byte	28
	.byte	'Ifx_P_ACCEN1_Bits',0,11,85,3
	.word	8923
	.byte	28
	.byte	'Ifx_P_ESR_Bits',0,11,107,3
	.word	5266
	.byte	28
	.byte	'Ifx_P_ID_Bits',0,11,115,3
	.word	3319
	.byte	28
	.byte	'Ifx_P_IN_Bits',0,11,137,1,3
	.word	4314
	.byte	28
	.byte	'Ifx_P_IOCR0_Bits',0,11,150,1,3
	.word	3447
	.byte	28
	.byte	'Ifx_P_IOCR12_Bits',0,11,163,1,3
	.word	4094
	.byte	28
	.byte	'Ifx_P_IOCR4_Bits',0,11,176,1,3
	.word	3662
	.byte	28
	.byte	'Ifx_P_IOCR8_Bits',0,11,189,1,3
	.word	3877
	.byte	28
	.byte	'Ifx_P_LPCR0_Bits',0,11,197,1,3
	.word	8282
	.byte	28
	.byte	'Ifx_P_LPCR1_Bits',0,11,205,1,3
	.word	8406
	.byte	28
	.byte	'Ifx_P_LPCR1_P21_Bits',0,11,215,1,3
	.word	8490
	.byte	28
	.byte	'Ifx_P_LPCR2_Bits',0,11,229,1,3
	.word	8670
	.byte	28
	.byte	'Ifx_P_OMCR0_Bits',0,11,240,1,3
	.word	6921
	.byte	28
	.byte	'Ifx_P_OMCR12_Bits',0,11,250,1,3
	.word	7445
	.byte	28
	.byte	'Ifx_P_OMCR4_Bits',0,11,133,2,3
	.word	7095
	.byte	28
	.byte	'Ifx_P_OMCR8_Bits',0,11,144,2,3
	.word	7269
	.byte	28
	.byte	'Ifx_P_OMCR_Bits',0,11,166,2,3
	.word	7934
	.byte	28
	.byte	'Ifx_P_OMR_Bits',0,11,203,2,3
	.word	2748
	.byte	28
	.byte	'Ifx_P_OMSR0_Bits',0,11,213,2,3
	.word	6258
	.byte	28
	.byte	'Ifx_P_OMSR12_Bits',0,11,224,2,3
	.word	6746
	.byte	28
	.byte	'Ifx_P_OMSR4_Bits',0,11,235,2,3
	.word	6405
	.byte	28
	.byte	'Ifx_P_OMSR8_Bits',0,11,246,2,3
	.word	6574
	.byte	28
	.byte	'Ifx_P_OMSR_Bits',0,11,140,3,3
	.word	7601
	.byte	28
	.byte	'Ifx_P_OUT_Bits',0,11,162,3,3
	.word	2432
	.byte	28
	.byte	'Ifx_P_PCSR_Bits',0,11,180,3,3
	.word	5972
	.byte	28
	.byte	'Ifx_P_PDISC_Bits',0,11,202,3,3
	.word	5606
	.byte	28
	.byte	'Ifx_P_PDR0_Bits',0,11,223,3,3
	.word	4637
	.byte	28
	.byte	'Ifx_P_PDR1_Bits',0,11,244,3,3
	.word	4941
	.byte	28
	.byte	'Ifx_P_ACCEN0',0,11,129,4,3
	.word	9537
	.byte	28
	.byte	'Ifx_P_ACCEN1',0,11,137,4,3
	.word	8970
	.byte	28
	.byte	'Ifx_P_ESR',0,11,145,4,3
	.word	5557
	.byte	28
	.byte	'Ifx_P_ID',0,11,153,4,3
	.word	3398
	.byte	28
	.byte	'Ifx_P_IN',0,11,161,4,3
	.word	4588
	.byte	28
	.byte	'Ifx_P_IOCR0',0,11,169,4,3
	.word	3622
	.byte	28
	.byte	'Ifx_P_IOCR12',0,11,177,4,3
	.word	4274
	.byte	28
	.byte	'Ifx_P_IOCR4',0,11,185,4,3
	.word	3837
	.byte	28
	.byte	'Ifx_P_IOCR8',0,11,193,4,3
	.word	4054
	.byte	28
	.byte	'Ifx_P_LPCR0',0,11,201,4,3
	.word	8366
	.byte	28
	.byte	'Ifx_P_LPCR1',0,11,210,4,3
	.word	8615
	.byte	28
	.byte	'Ifx_P_LPCR2',0,11,218,4,3
	.word	8874
	.byte	28
	.byte	'Ifx_P_OMCR',0,11,226,4,3
	.word	8242
	.byte	28
	.byte	'Ifx_P_OMCR0',0,11,234,4,3
	.word	7055
	.byte	28
	.byte	'Ifx_P_OMCR12',0,11,242,4,3
	.word	7561
	.byte	28
	.byte	'Ifx_P_OMCR4',0,11,250,4,3
	.word	7229
	.byte	28
	.byte	'Ifx_P_OMCR8',0,11,130,5,3
	.word	7405
	.byte	28
	.byte	'Ifx_P_OMR',0,11,138,5,3
	.word	3279
	.byte	28
	.byte	'Ifx_P_OMSR',0,11,146,5,3
	.word	7894
	.byte	28
	.byte	'Ifx_P_OMSR0',0,11,154,5,3
	.word	6365
	.byte	28
	.byte	'Ifx_P_OMSR12',0,11,162,5,3
	.word	6881
	.byte	28
	.byte	'Ifx_P_OMSR4',0,11,170,5,3
	.word	6534
	.byte	28
	.byte	'Ifx_P_OMSR8',0,11,178,5,3
	.word	6706
	.byte	28
	.byte	'Ifx_P_OUT',0,11,186,5,3
	.word	2708
	.byte	28
	.byte	'Ifx_P_PCSR',0,11,194,5,3
	.word	6218
	.byte	28
	.byte	'Ifx_P_PDISC',0,11,202,5,3
	.word	5932
	.byte	28
	.byte	'Ifx_P_PDR0',0,11,210,5,3
	.word	4901
	.byte	28
	.byte	'Ifx_P_PDR1',0,11,218,5,3
	.word	5217
	.byte	14
	.word	9577
	.byte	28
	.byte	'Ifx_P',0,11,139,6,3
	.word	95613
	.byte	28
	.byte	'IfxPort_InputMode',0,10,89,3
	.word	10190
	.byte	28
	.byte	'IfxPort_Mode',0,10,116,3
	.word	24966
	.byte	28
	.byte	'IfxPort_OutputIdx',0,10,130,1,3
	.word	10465
	.byte	28
	.byte	'IfxPort_OutputMode',0,10,138,1,3
	.word	10395
	.byte	15,10,144,1,9,1,16
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed1',0,0,16
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed2',0,1,16
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed3',0,2,16
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed4',0,3,16
	.byte	'IfxPort_PadDriver_lvdsSpeed1',0,4,16
	.byte	'IfxPort_PadDriver_lvdsSpeed2',0,5,16
	.byte	'IfxPort_PadDriver_lvdsSpeed3',0,6,16
	.byte	'IfxPort_PadDriver_lvdsSpeed4',0,7,16
	.byte	'IfxPort_PadDriver_ttlSpeed1',0,8,16
	.byte	'IfxPort_PadDriver_ttlSpeed2',0,9,16
	.byte	'IfxPort_PadDriver_ttlSpeed3',0,10,16
	.byte	'IfxPort_PadDriver_ttlSpeed4',0,11,0,28
	.byte	'IfxPort_PadDriver',0,10,158,1,3
	.word	95735
	.byte	28
	.byte	'IfxPort_State',0,10,178,1,3
	.word	10778
	.byte	28
	.byte	'IfxPort_Pin',0,10,194,1,3
	.word	24250
	.byte	28
	.byte	'IfxScuCcu_PllStepsFunctionHook',0,24,148,1,16
	.word	243
	.byte	20,24,212,5,9,8,13
	.byte	'value',0
	.word	2201
	.byte	4,2,35,0,13
	.byte	'mask',0
	.word	2201
	.byte	4,2,35,4,0,28
	.byte	'IfxScuCcu_CcuconRegConfig',0,24,216,5,3
	.word	96261
	.byte	20,24,221,5,9,8,13
	.byte	'pDivider',0
	.word	527
	.byte	1,2,35,0,13
	.byte	'nDivider',0
	.word	527
	.byte	1,2,35,1,13
	.byte	'k2Initial',0
	.word	527
	.byte	1,2,35,2,13
	.byte	'waitTime',0
	.word	300
	.byte	4,2,35,4,0,28
	.byte	'IfxScuCcu_InitialStepConfig',0,24,227,5,3
	.word	96332
	.byte	20,24,231,5,9,12,13
	.byte	'k2Step',0
	.word	527
	.byte	1,2,35,0,13
	.byte	'waitTime',0
	.word	300
	.byte	4,2,35,2,13
	.byte	'hookFunction',0
	.word	96221
	.byte	4,2,35,8,0,28
	.byte	'IfxScuCcu_PllStepsConfig',0,24,236,5,3
	.word	96449
	.byte	3
	.word	240
	.byte	20,24,244,5,9,48,13
	.byte	'ccucon0',0
	.word	96261
	.byte	8,2,35,0,13
	.byte	'ccucon1',0
	.word	96261
	.byte	8,2,35,8,13
	.byte	'ccucon2',0
	.word	96261
	.byte	8,2,35,16,13
	.byte	'ccucon5',0
	.word	96261
	.byte	8,2,35,24,13
	.byte	'ccucon6',0
	.word	96261
	.byte	8,2,35,32,13
	.byte	'ccucon7',0
	.word	96261
	.byte	8,2,35,40,0,28
	.byte	'IfxScuCcu_ClockDistributionConfig',0,24,252,5,3
	.word	96551
	.byte	20,24,128,6,9,8,13
	.byte	'value',0
	.word	2201
	.byte	4,2,35,0,13
	.byte	'mask',0
	.word	2201
	.byte	4,2,35,4,0,28
	.byte	'IfxScuCcu_FlashWaitstateConfig',0,24,132,6,3
	.word	96703
	.byte	3
	.word	96449
	.byte	20,24,137,6,9,16,13
	.byte	'numOfPllDividerSteps',0
	.word	527
	.byte	1,2,35,0,13
	.byte	'pllDividerStep',0
	.word	96779
	.byte	4,2,35,4,13
	.byte	'pllInitialStep',0
	.word	96332
	.byte	8,2,35,8,0,28
	.byte	'IfxScuCcu_SysPllConfig',0,24,142,6,3
	.word	96784
	.byte	20,14,59,15,16,13
	.byte	'module',0
	.word	20919
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	24250
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	24289
	.byte	1,2,35,12,0,21
	.word	96901
	.byte	28
	.byte	'IfxCcu6_Cc60in_In',0,14,64,3
	.word	96952
	.byte	20,14,67,15,16,13
	.byte	'module',0
	.word	20919
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	24250
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	24289
	.byte	1,2,35,12,0,21
	.word	96983
	.byte	28
	.byte	'IfxCcu6_Cc61in_In',0,14,72,3
	.word	97034
	.byte	20,14,75,15,16,13
	.byte	'module',0
	.word	20919
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	24250
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	24289
	.byte	1,2,35,12,0,21
	.word	97065
	.byte	28
	.byte	'IfxCcu6_Cc62in_In',0,14,80,3
	.word	97116
	.byte	20,14,83,15,16,13
	.byte	'module',0
	.word	20919
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	24250
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	24289
	.byte	1,2,35,12,0,21
	.word	97147
	.byte	28
	.byte	'IfxCcu6_Ccpos0_In',0,14,88,3
	.word	97198
	.byte	20,14,91,15,16,13
	.byte	'module',0
	.word	20919
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	24250
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	24289
	.byte	1,2,35,12,0,21
	.word	97229
	.byte	28
	.byte	'IfxCcu6_Ccpos1_In',0,14,96,3
	.word	97280
	.byte	20,14,99,15,16,13
	.byte	'module',0
	.word	20919
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	24250
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	24289
	.byte	1,2,35,12,0,21
	.word	97311
	.byte	28
	.byte	'IfxCcu6_Ccpos2_In',0,14,104,3
	.word	97362
	.byte	20,14,107,15,16,13
	.byte	'module',0
	.word	20919
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	24250
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	24289
	.byte	1,2,35,12,0,21
	.word	97393
	.byte	28
	.byte	'IfxCcu6_Ctrap_In',0,14,112,3
	.word	97444
	.byte	21
	.word	24408
	.byte	28
	.byte	'IfxCcu6_T12hr_In',0,14,120,3
	.word	97474
	.byte	21
	.word	24538
	.byte	28
	.byte	'IfxCcu6_T13hr_In',0,14,128,1,3
	.word	97504
	.byte	20,14,131,1,15,16,13
	.byte	'module',0
	.word	20919
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	24250
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	10465
	.byte	1,2,35,12,0,21
	.word	97535
	.byte	28
	.byte	'IfxCcu6_Cc60_Out',0,14,136,1,3
	.word	97587
	.byte	20,14,139,1,15,16,13
	.byte	'module',0
	.word	20919
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	24250
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	10465
	.byte	1,2,35,12,0,21
	.word	97618
	.byte	28
	.byte	'IfxCcu6_Cc61_Out',0,14,144,1,3
	.word	97670
	.byte	20,14,147,1,15,16,13
	.byte	'module',0
	.word	20919
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	24250
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	10465
	.byte	1,2,35,12,0,21
	.word	97701
	.byte	28
	.byte	'IfxCcu6_Cc62_Out',0,14,152,1,3
	.word	97753
	.byte	20,14,155,1,15,16,13
	.byte	'module',0
	.word	20919
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	24250
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	10465
	.byte	1,2,35,12,0,21
	.word	97784
	.byte	28
	.byte	'IfxCcu6_Cout60_Out',0,14,160,1,3
	.word	97836
	.byte	20,14,163,1,15,16,13
	.byte	'module',0
	.word	20919
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	24250
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	10465
	.byte	1,2,35,12,0,21
	.word	97869
	.byte	28
	.byte	'IfxCcu6_Cout61_Out',0,14,168,1,3
	.word	97921
	.byte	20,14,171,1,15,16,13
	.byte	'module',0
	.word	20919
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	24250
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	10465
	.byte	1,2,35,12,0,21
	.word	97954
	.byte	28
	.byte	'IfxCcu6_Cout62_Out',0,14,176,1,3
	.word	98006
	.byte	20,14,179,1,15,16,13
	.byte	'module',0
	.word	20919
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	24250
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	10465
	.byte	1,2,35,12,0,21
	.word	98039
	.byte	28
	.byte	'IfxCcu6_Cout63_Out',0,14,184,1,3
	.word	98091
	.byte	28
	.byte	'IfxCcu6_CaptureCompareInput',0,12,90,3
	.word	23741
	.byte	28
	.byte	'IfxCcu6_CaptureCompareInputSignal',0,12,100,3
	.word	23999
	.byte	15,12,113,9,1,16
	.byte	'IfxCcu6_ChannelOut_cc0',0,0,16
	.byte	'IfxCcu6_ChannelOut_cout0',0,1,16
	.byte	'IfxCcu6_ChannelOut_cc1',0,2,16
	.byte	'IfxCcu6_ChannelOut_cout1',0,3,16
	.byte	'IfxCcu6_ChannelOut_cc2',0,4,16
	.byte	'IfxCcu6_ChannelOut_cout2',0,5,16
	.byte	'IfxCcu6_ChannelOut_cout3',0,6,0,28
	.byte	'IfxCcu6_ChannelOut',0,12,122,3
	.word	98202
	.byte	28
	.byte	'IfxCcu6_CountingInputMode',0,12,137,1,3
	.word	21027
	.byte	28
	.byte	'IfxCcu6_ExternalTriggerMode',0,12,161,1,3
	.word	21274
	.byte	15,12,166,1,9,1,16
	.byte	'IfxCcu6_HallSensorTriggerMode_permanentCheck',0,0,16
	.byte	'IfxCcu6_HallSensorTriggerMode_cM63',0,1,16
	.byte	'IfxCcu6_HallSensorTriggerMode_t13PM',0,2,16
	.byte	'IfxCcu6_HallSensorTriggerMode_off',0,3,16
	.byte	'IfxCcu6_HallSensorTriggerMode_t12PMCountingUp',0,4,16
	.byte	'IfxCcu6_HallSensorTriggerMode_t12OMCountingDown',0,5,16
	.byte	'IfxCcu6_HallSensorTriggerMode_cM61CountingUp',0,6,16
	.byte	'IfxCcu6_HallSensorTriggerMode_cM61CountingDown',0,7,0,28
	.byte	'IfxCcu6_HallSensorTriggerMode',0,12,177,1,3
	.word	98490
	.byte	28
	.byte	'IfxCcu6_InterruptSource',0,12,203,1,3
	.word	22938
	.byte	15,12,208,1,9,1,16
	.byte	'IfxCcu6_MultiChannelSwitchingSelect_noEvent',0,0,16
	.byte	'IfxCcu6_MultiChannelSwitchingSelect_correctHallEvent',0,1,16
	.byte	'IfxCcu6_MultiChannelSwitchingSelect_t13PeriodMatch',0,2,16
	.byte	'IfxCcu6_MultiChannelSwitchingSelect_t12OneMatch',0,3,16
	.byte	'IfxCcu6_MultiChannelSwitchingSelect_t12Channel1CompareMatch',0,4,16
	.byte	'IfxCcu6_MultiChannelSwitchingSelect_t12PeriodMatch',0,5,0,28
	.byte	'IfxCcu6_MultiChannelSwitchingSelect',0,12,217,1,3
	.word	98921
	.byte	15,12,222,1,9,1,16
	.byte	'IfxCcu6_MultiChannelSwitchingSync_direct',0,0,16
	.byte	'IfxCcu6_MultiChannelSwitchingSync_t13ZeroMatch',0,1,16
	.byte	'IfxCcu6_MultiChannelSwitchingSync_t12ZeroMatch',0,2,0,28
	.byte	'IfxCcu6_MultiChannelSwitchingSync',0,12,229,1,3
	.word	99292
	.byte	28
	.byte	'IfxCcu6_ServiceRequest',0,12,239,1,3
	.word	25929
	.byte	15,12,244,1,9,1,16
	.byte	'IfxCcu6_SleepMode_enable',0,0,16
	.byte	'IfxCcu6_SleepMode_disable',0,1,0,28
	.byte	'IfxCcu6_SleepMode',0,12,248,1,3
	.word	99515
	.byte	15,12,252,1,9,1,16
	.byte	'IfxCcu6_SuspendMode_none',0,0,16
	.byte	'IfxCcu6_SuspendMode_hard',0,1,16
	.byte	'IfxCcu6_SuspendMode_soft',0,2,0,28
	.byte	'IfxCcu6_SuspendMode',0,12,129,2,3
	.word	99604
	.byte	15,12,133,2,9,1,16
	.byte	'IfxCcu6_T12Channel_0',0,0,16
	.byte	'IfxCcu6_T12Channel_1',0,1,16
	.byte	'IfxCcu6_T12Channel_2',0,2,0,28
	.byte	'IfxCcu6_T12Channel',0,12,138,2,3
	.word	99721
	.byte	15,12,142,2,9,1,16
	.byte	'IfxCcu6_T12ChannelMode_off',0,0,16
	.byte	'IfxCcu6_T12ChannelMode_compareMode',0,1,16
	.byte	'IfxCcu6_T12ChannelMode_doubleRegisterCaptureRisingAndFalling',0,4,16
	.byte	'IfxCcu6_T12ChannelMode_doubleRegisterCaptureRising',0,5,16
	.byte	'IfxCcu6_T12ChannelMode_doubleRegisterCaptureFalling',0,6,16
	.byte	'IfxCcu6_T12ChannelMode_doubleRegisterCaptureAny',0,7,16
	.byte	'IfxCcu6_T12ChannelMode_hallSensor',0,8,16
	.byte	'IfxCcu6_T12ChannelMode_hysteresisLikecompare',0,9,16
	.byte	'IfxCcu6_T12ChannelMode_multiInputCaptureRisingAndFalling',0,10,16
	.byte	'IfxCcu6_T12ChannelMode_multiInputCaptureFallingAndRising',0,11,16
	.byte	'IfxCcu6_T12ChannelMode_multiInputCaptureBothRising',0,12,16
	.byte	'IfxCcu6_T12ChannelMode_multiInputCaptureBothFalling',0,13,16
	.byte	'IfxCcu6_T12ChannelMode_multiInputCaptureAny',0,14,0,28
	.byte	'IfxCcu6_T12ChannelMode',0,12,158,2,3
	.word	99825
	.byte	15,12,163,2,9,1,16
	.byte	'IfxCcu6_T12CountDirection_up',0,0,16
	.byte	'IfxCcu6_T12CountDirection_down',0,1,0,28
	.byte	'IfxCcu6_T12CountDirection',0,12,167,2,3
	.word	100504
	.byte	28
	.byte	'IfxCcu6_T12CountMode',0,12,178,2,3
	.word	21520
	.byte	28
	.byte	'IfxCcu6_T13TriggerDirection',0,12,189,2,3
	.word	21923
	.byte	28
	.byte	'IfxCcu6_T13TriggerEvent',0,12,205,2,3
	.word	22179
	.byte	28
	.byte	'IfxCcu6_TimerId',0,12,213,2,3
	.word	20976
	.byte	15,12,218,2,9,1,16
	.byte	'IfxCcu6_TimerInputClock_fcc6',0,0,16
	.byte	'IfxCcu6_TimerInputClock_fcc6By2',0,1,16
	.byte	'IfxCcu6_TimerInputClock_fcc6By4',0,2,16
	.byte	'IfxCcu6_TimerInputClock_fcc6By8',0,3,16
	.byte	'IfxCcu6_TimerInputClock_fcc6By16',0,4,16
	.byte	'IfxCcu6_TimerInputClock_fcc6By32',0,5,16
	.byte	'IfxCcu6_TimerInputClock_fcc6By64',0,6,16
	.byte	'IfxCcu6_TimerInputClock_fcc6By128',0,7,0,28
	.byte	'IfxCcu6_TimerInputClock',0,12,228,2,3
	.word	100735
	.byte	15,12,247,2,9,1,16
	.byte	'IfxCcu6_TimerRunStatus_stopped',0,0,16
	.byte	'IfxCcu6_TimerRunStatus_running',0,1,0,28
	.byte	'IfxCcu6_TimerRunStatus',0,12,251,2,3
	.word	101049
	.byte	15,12,128,3,9,1,16
	.byte	'IfxCcu6_TrapMode_automatic',0,0,16
	.byte	'IfxCcu6_TrapMode_manual',0,1,0,28
	.byte	'IfxCcu6_TrapMode',0,12,133,3,3
	.word	101154
	.byte	15,12,138,3,9,1,16
	.byte	'IfxCcu6_TrapState_t12Sync',0,0,16
	.byte	'IfxCcu6_TrapState_t13Sync',0,1,16
	.byte	'IfxCcu6_TrapState_immediate',0,3,0,28
	.byte	'IfxCcu6_TrapState',0,12,145,3,3
	.word	101242
	.byte	28
	.byte	'Timer',0,17,53,24
	.word	26829
	.byte	3
	.word	26829
	.byte	30,1,1,31
	.word	101376
	.byte	0,3
	.word	101381
	.byte	28
	.byte	'Timer_Start',0,17,54,24
	.word	101390
	.byte	28
	.byte	'Timer_Stop',0,17,55,24
	.word	101390
	.byte	28
	.byte	'Timer_SynchronousStart',0,17,56,24
	.word	101390
	.byte	28
	.byte	'Timer_SynchronousStop',0,17,57,24
	.word	101390
	.byte	28
	.byte	'Timer_CountOneStep',0,17,58,24
	.word	101390
	.byte	28
	.byte	'Timer_StartSingleShotMode',0,17,59,24
	.word	101390
	.byte	28
	.byte	'Timer_Config',0,17,92,3
	.word	27115
	.byte	28
	.byte	'IfxCcu6_Timer_Clock',0,16,242,1,3
	.word	27245
	.byte	28
	.byte	'IfxCcu6_Timer_InterruptConfig',0,16,252,1,3
	.word	27545
	.byte	28
	.byte	'IfxCcu6_Timer_Timer12',0,16,132,2,3
	.word	27420
	.byte	28
	.byte	'IfxCcu6_Timer_Timer13',0,16,141,2,3
	.word	27468
	.byte	28
	.byte	'IfxCcu6_Timer_TriggerConfig',0,16,151,2,3
	.word	26925
	.byte	28
	.byte	'IfxCcu6_Timer_Pins',0,16,162,2,3
	.word	27633
	.byte	28
	.byte	'IfxCcu6_Timer',0,16,174,2,3
	.word	27043
	.byte	28
	.byte	'IfxCcu6_Timer_Config',0,16,193,2,3
	.word	27699
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L91:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,21,0,54,15,39,12,0,0,3,15,0,73,19,0,0,4,46,1,3,8,32,13
	.byte	58,15,59,15,57,15,54,15,39,12,0,0,5,5,0,3,8,58,15,59,15,57,15,73,19,0,0,6,11,0,0,0,7,36,0,3,8,11,15,62
	.byte	15,0,0,8,46,1,3,8,32,13,58,15,59,15,57,15,73,19,54,15,39,12,0,0,9,59,0,3,8,0,0,10,19,1,3,8,58,15,59,15
	.byte	57,15,11,15,0,0,11,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,12,23,1,58,15,59,15,57,15,11,15,0,0,13,13
	.byte	0,3,8,73,19,11,15,56,9,0,0,14,53,0,73,19,0,0,15,4,1,58,15,59,15,57,15,11,15,0,0,16,40,0,3,8,28,13,0,0
	.byte	17,11,1,0,0,18,1,1,11,15,73,19,0,0,19,33,0,47,15,0,0,20,19,1,58,15,59,15,57,15,11,15,0,0,21,38,0,73,19
	.byte	0,0,22,46,1,49,19,0,0,23,5,0,49,19,0,0,24,29,1,49,19,0,0,25,11,0,49,19,0,0,26,46,1,3,8,58,15,59,15,57
	.byte	15,54,15,39,12,63,12,60,12,0,0,27,46,1,3,8,58,15,59,15,57,15,73,19,54,15,39,12,63,12,60,12,0,0,28,22,0
	.byte	3,8,58,15,59,15,57,15,73,19,0,0,29,21,0,54,15,0,0,30,21,1,54,15,39,12,0,0,31,5,0,73,19,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L92:
	.word	.L622-.L621
.L621:
	.half	3
	.word	.L624-.L623
.L623:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg',0
	.byte	0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_Intrinsics.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Src\\Std\\IfxSrc.h',0
	.byte	0,0,0
	.byte	'IfxSrc_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxSrc_cfg.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Scu\\Std\\IfxScuWdt.h',0
	.byte	0,0,0
	.byte	'IfxScu_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxCpu_cfg.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Port\\Std\\IfxPort.h',0
	.byte	0,0,0
	.byte	'IfxPort_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Ccu6\\Std\\IfxCcu6.h',0
	.byte	0,0,0
	.byte	'IfxCcu6_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_PinMap\\IfxCcu6_PinMap.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Ifx_Types.h',0
	.byte	0,0,0
	.byte	'..\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Ccu6\\Timer\\IfxCcu6_Timer.h',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\If\\Ccu6If\\Timer.h',0
	.byte	0,0,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Ccu6/Timer/IfxCcu6_Timer.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Platform_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxScu_cfg.h',0
	.byte	0,0,0
	.byte	'IfxCpu_regdef.h',0,1,0,0
	.byte	'IfxStm_regdef.h',0,1,0,0
	.byte	'IfxFlash_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Scu\\Std\\IfxScuCcu.h',0
	.byte	0,0,0,0
.L624:
.L622:
	.sdecl	'.debug_info',debug,cluster('IfxCcu6_Timer_initModule')
	.sect	'.debug_info'
.L93:
	.word	2337
	.half	3
	.word	.L94
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Ccu6/Timer/IfxCcu6_Timer.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L96,.L95
	.byte	2
	.word	.L89
	.byte	3
	.byte	'IfxCcu6_Timer_initModule',0,1,79,6,1,1,1
	.word	.L76,.L133,.L75
	.byte	4
	.byte	'timer',0,1,79,46
	.word	.L134,.L135
	.byte	4
	.byte	'config',0,1,79,81
	.word	.L136,.L137
	.byte	5
	.word	.L76,.L133
	.byte	6
	.byte	'ccu6SFR',0,1,81,15
	.word	.L138,.L139
	.byte	7
	.word	.L140,.L141,.L5
	.byte	8
	.word	.L142,.L143
	.byte	9
	.word	.L144,.L141,.L5
	.byte	0,7
	.word	.L145,.L146,.L147
	.byte	8
	.word	.L148,.L149
	.byte	8
	.word	.L150,.L151
	.byte	10
	.word	.L152,.L146,.L147
	.byte	6
	.byte	'mask',0,2,184,13,12
	.word	.L153,.L154
	.byte	0,0,7
	.word	.L155,.L156,.L12
	.byte	8
	.word	.L157,.L158
	.byte	8
	.word	.L159,.L160
	.byte	10
	.word	.L161,.L156,.L12
	.byte	6
	.byte	'mask',0,2,223,12,12
	.word	.L153,.L162
	.byte	0,0,7
	.word	.L163,.L164,.L165
	.byte	8
	.word	.L166,.L167
	.byte	8
	.word	.L168,.L169
	.byte	8
	.word	.L170,.L171
	.byte	10
	.word	.L172,.L164,.L165
	.byte	6
	.byte	'shift',0,2,171,15,12
	.word	.L153,.L173
	.byte	6
	.byte	'mask',0,2,172,15,12
	.word	.L153,.L174
	.byte	0,0,7
	.word	.L175,.L176,.L177
	.byte	8
	.word	.L178,.L179
	.byte	8
	.word	.L180,.L181
	.byte	9
	.word	.L182,.L176,.L177
	.byte	0,7
	.word	.L183,.L184,.L185
	.byte	8
	.word	.L186,.L187
	.byte	8
	.word	.L188,.L189
	.byte	9
	.word	.L190,.L184,.L185
	.byte	0,7
	.word	.L191,.L192,.L193
	.byte	8
	.word	.L194,.L195
	.byte	8
	.word	.L196,.L197
	.byte	9
	.word	.L198,.L192,.L193
	.byte	0,7
	.word	.L199,.L200,.L16
	.byte	8
	.word	.L201,.L202
	.byte	8
	.word	.L203,.L204
	.byte	9
	.word	.L205,.L200,.L16
	.byte	0,5
	.word	.L19,.L21
	.byte	6
	.byte	'period',0,1,143,1,24
	.word	.L153,.L206
	.byte	7
	.word	.L145,.L207,.L208
	.byte	8
	.word	.L148,.L149
	.byte	8
	.word	.L150,.L151
	.byte	10
	.word	.L152,.L207,.L208
	.byte	6
	.byte	'mask',0,2,184,13,12
	.word	.L153,.L209
	.byte	0,0,7
	.word	.L155,.L210,.L25
	.byte	8
	.word	.L157,.L158
	.byte	8
	.word	.L159,.L160
	.byte	10
	.word	.L161,.L210,.L25
	.byte	6
	.byte	'mask',0,2,223,12,12
	.word	.L153,.L211
	.byte	0,0,7
	.word	.L163,.L212,.L213
	.byte	8
	.word	.L166,.L167
	.byte	8
	.word	.L168,.L169
	.byte	8
	.word	.L170,.L171
	.byte	10
	.word	.L172,.L212,.L213
	.byte	6
	.byte	'shift',0,2,171,15,12
	.word	.L153,.L214
	.byte	6
	.byte	'mask',0,2,172,15,12
	.word	.L153,.L215
	.byte	0,0,7
	.word	.L216,.L217,.L218
	.byte	8
	.word	.L219,.L220
	.byte	8
	.word	.L221,.L222
	.byte	9
	.word	.L223,.L217,.L218
	.byte	0,7
	.word	.L224,.L225,.L226
	.byte	8
	.word	.L227,.L228
	.byte	8
	.word	.L229,.L230
	.byte	9
	.word	.L231,.L225,.L226
	.byte	0,7
	.word	.L232,.L233,.L21
	.byte	8
	.word	.L234,.L235
	.byte	8
	.word	.L236,.L237
	.byte	9
	.word	.L238,.L233,.L21
	.byte	0,0,5
	.word	.L21,.L133
	.byte	6
	.byte	'pins',0,1,203,1,31
	.word	.L239,.L240
	.byte	5
	.word	.L241,.L35
	.byte	6
	.byte	't12hr',0,1,207,1,27
	.word	.L242,.L243
	.byte	7
	.word	.L244,.L245,.L36
	.byte	8
	.word	.L246,.L247
	.byte	8
	.word	.L248,.L249
	.byte	10
	.word	.L250,.L245,.L36
	.byte	7
	.word	.L251,.L252,.L253
	.byte	8
	.word	.L254,.L255
	.byte	8
	.word	.L256,.L257
	.byte	8
	.word	.L258,.L259
	.byte	9
	.word	.L260,.L252,.L253
	.byte	0,0,0,5
	.word	.L36,.L35
	.byte	6
	.byte	't13hr',0,1,214,1,27
	.word	.L261,.L262
	.byte	7
	.word	.L263,.L264,.L35
	.byte	8
	.word	.L265,.L266
	.byte	8
	.word	.L267,.L268
	.byte	10
	.word	.L269,.L264,.L35
	.byte	7
	.word	.L251,.L270,.L271
	.byte	8
	.word	.L254,.L255
	.byte	8
	.word	.L256,.L257
	.byte	8
	.word	.L258,.L259
	.byte	9
	.word	.L260,.L270,.L271
	.byte	0,0,0,0,0,7
	.word	.L272,.L273,.L274
	.byte	8
	.word	.L275,.L276
	.byte	8
	.word	.L277,.L278
	.byte	10
	.word	.L279,.L273,.L274
	.byte	6
	.byte	'mask',0,2,157,12,12
	.word	.L153,.L280
	.byte	0,0,5
	.word	.L281,.L38
	.byte	6
	.byte	'src',0,1,229,1,32
	.word	.L282,.L283
	.byte	7
	.word	.L284,.L285,.L286
	.byte	8
	.word	.L287,.L288
	.byte	8
	.word	.L289,.L290
	.byte	8
	.word	.L291,.L292
	.byte	10
	.word	.L293,.L285,.L286
	.byte	7
	.word	.L294,.L295,.L286
	.byte	8
	.word	.L296,.L297
	.byte	9
	.word	.L298,.L295,.L286
	.byte	0,0,0,7
	.word	.L299,.L286,.L38
	.byte	8
	.word	.L300,.L301
	.byte	9
	.word	.L302,.L286,.L38
	.byte	0,0,7
	.word	.L272,.L303,.L304
	.byte	8
	.word	.L275,.L276
	.byte	8
	.word	.L277,.L278
	.byte	10
	.word	.L279,.L303,.L304
	.byte	6
	.byte	'mask',0,2,157,12,12
	.word	.L153,.L305
	.byte	0,0,5
	.word	.L306,.L39
	.byte	6
	.byte	'src',0,1,240,1,32
	.word	.L282,.L307
	.byte	7
	.word	.L284,.L308,.L309
	.byte	8
	.word	.L287,.L288
	.byte	8
	.word	.L289,.L290
	.byte	8
	.word	.L291,.L292
	.byte	10
	.word	.L293,.L308,.L309
	.byte	7
	.word	.L294,.L310,.L309
	.byte	8
	.word	.L296,.L297
	.byte	9
	.word	.L298,.L310,.L309
	.byte	0,0,0,7
	.word	.L299,.L309,.L39
	.byte	8
	.word	.L300,.L301
	.byte	9
	.word	.L302,.L309,.L39
	.byte	0,0,7
	.word	.L272,.L311,.L312
	.byte	8
	.word	.L275,.L276
	.byte	8
	.word	.L277,.L278
	.byte	10
	.word	.L279,.L311,.L312
	.byte	6
	.byte	'mask',0,2,157,12,12
	.word	.L153,.L313
	.byte	0,0,5
	.word	.L314,.L40
	.byte	6
	.byte	'src',0,1,251,1,32
	.word	.L282,.L315
	.byte	7
	.word	.L284,.L316,.L317
	.byte	8
	.word	.L287,.L288
	.byte	8
	.word	.L289,.L290
	.byte	8
	.word	.L291,.L292
	.byte	10
	.word	.L293,.L316,.L317
	.byte	7
	.word	.L294,.L318,.L317
	.byte	8
	.word	.L296,.L297
	.byte	9
	.word	.L298,.L318,.L317
	.byte	0,0,0,7
	.word	.L299,.L317,.L40
	.byte	8
	.word	.L300,.L301
	.byte	9
	.word	.L302,.L317,.L40
	.byte	0,0,7
	.word	.L272,.L319,.L320
	.byte	8
	.word	.L275,.L276
	.byte	8
	.word	.L277,.L278
	.byte	10
	.word	.L279,.L319,.L320
	.byte	6
	.byte	'mask',0,2,157,12,12
	.word	.L153,.L321
	.byte	0,0,5
	.word	.L322,.L41
	.byte	6
	.byte	'src',0,1,134,2,32
	.word	.L282,.L323
	.byte	7
	.word	.L284,.L324,.L325
	.byte	8
	.word	.L287,.L288
	.byte	8
	.word	.L289,.L290
	.byte	8
	.word	.L291,.L292
	.byte	10
	.word	.L293,.L324,.L325
	.byte	7
	.word	.L294,.L326,.L325
	.byte	8
	.word	.L296,.L297
	.byte	9
	.word	.L298,.L326,.L325
	.byte	0,0,0,7
	.word	.L299,.L325,.L41
	.byte	8
	.word	.L300,.L301
	.byte	9
	.word	.L302,.L325,.L41
	.byte	0,0,0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu6_Timer_initModule')
	.sect	'.debug_abbrev'
.L94:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,5,0,49,16,2,6,0,0,9,11,0,49,16,17,1,18
	.byte	1,0,0,10,11,1,49,16,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxCcu6_Timer_initModule')
	.sect	'.debug_line'
.L95:
	.word	.L626-.L625
.L625:
	.half	3
	.word	.L628-.L627
.L627:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Ccu6/Timer/IfxCcu6_Timer.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Ccu6\\Std\\IfxCcu6.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Port\\Std\\IfxPort.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Src\\Std\\IfxSrc.h',0
	.byte	0,0,0,0
.L628:
	.byte	5,6,7,0,5,2
	.word	.L76
	.byte	3,206,0,1,5,31,9
	.half	.L539-.L76
	.byte	3,2,1,5,17,9
	.half	.L541-.L539
	.byte	3,1,1,4,2,5,23,9
	.half	.L141-.L541
	.byte	3,173,14,1,5,29,9
	.half	.L629-.L141
	.byte	1,5,5,9
	.half	.L630-.L629
	.byte	1,4,1,9
	.half	.L5-.L630
	.byte	3,216,113,1,5,30,7,9
	.half	.L631-.L5
	.byte	3,2,1,5,16,9
	.half	.L6-.L631
	.byte	3,7,1,5,9,9
	.half	.L632-.L6
	.byte	1,5,58,7,9
	.half	.L633-.L632
	.byte	1,5,57,9
	.half	.L7-.L633
	.byte	3,3,1,4,2,5,20,9
	.half	.L146-.L7
	.byte	3,213,12,1,5,23,9
	.half	.L634-.L146
	.byte	1,9
	.half	.L543-.L634
	.byte	3,1,1,5,26,9
	.half	.L635-.L543
	.byte	1,5,12,9
	.half	.L636-.L635
	.byte	1,5,34,7,9
	.half	.L637-.L636
	.byte	1,5,41,9
	.half	.L638-.L637
	.byte	1,5,34,9
	.half	.L9-.L638
	.byte	1,5,5,9
	.half	.L10-.L9
	.byte	1,4,1,5,9,9
	.half	.L11-.L10
	.byte	3,170,115,1,5,42,7,9
	.half	.L147-.L11
	.byte	3,2,1,4,2,5,20,9
	.half	.L156-.L147
	.byte	3,250,11,1,5,23,9
	.half	.L544-.L156
	.byte	1,5,30,9
	.half	.L545-.L544
	.byte	3,1,1,5,33,9
	.half	.L639-.L545
	.byte	1,5,18,9
	.half	.L640-.L639
	.byte	1,4,1,5,26,9
	.half	.L12-.L640
	.byte	3,138,116,1,5,9,9
	.half	.L641-.L12
	.byte	1,5,30,7,9
	.half	.L642-.L641
	.byte	3,3,1,5,13,9
	.half	.L643-.L642
	.byte	1,5,49,7,9
	.half	.L644-.L643
	.byte	3,2,1,5,70,9
	.half	.L645-.L644
	.byte	1,5,51,9
	.half	.L14-.L645
	.byte	3,4,1,5,85,9
	.half	.L646-.L14
	.byte	1,4,2,5,28,9
	.half	.L164-.L646
	.byte	3,184,14,1,5,33,9
	.half	.L546-.L164
	.byte	1,5,21,9
	.half	.L647-.L546
	.byte	3,1,1,5,26,9
	.half	.L648-.L647
	.byte	1,5,35,9
	.half	.L547-.L648
	.byte	3,1,1,5,40,9
	.half	.L649-.L547
	.byte	1,5,38,9
	.half	.L548-.L649
	.byte	1,5,63,9
	.half	.L650-.L548
	.byte	1,5,47,9
	.half	.L651-.L650
	.byte	1,5,20,9
	.half	.L652-.L651
	.byte	1,4,1,5,68,9
	.half	.L165-.L652
	.byte	3,201,113,1,5,48,9
	.half	.L653-.L165
	.byte	1,4,2,5,18,9
	.half	.L176-.L653
	.byte	3,204,15,1,5,25,9
	.half	.L654-.L176
	.byte	1,4,1,5,107,9
	.half	.L177-.L654
	.byte	3,177,112,1,5,58,9
	.half	.L13-.L177
	.byte	3,7,1,5,85,9
	.half	.L655-.L13
	.byte	1,5,60,9
	.half	.L15-.L655
	.byte	3,5,1,4,2,5,16,9
	.half	.L184-.L15
	.byte	3,189,15,1,5,23,9
	.half	.L656-.L184
	.byte	1,4,1,5,29,9
	.half	.L185-.L656
	.byte	3,198,112,1,5,13,9
	.half	.L657-.L185
	.byte	1,5,60,7,9
	.half	.L658-.L657
	.byte	1,5,53,9
	.half	.L659-.L658
	.byte	1,5,68,7,9
	.half	.L660-.L659
	.byte	3,2,1,4,2,5,18,9
	.half	.L192-.L660
	.byte	3,202,15,1,5,26,9
	.half	.L661-.L192
	.byte	1,4,1,5,73,9
	.half	.L193-.L661
	.byte	3,183,112,1,4,2,5,18,9
	.half	.L200-.L193
	.byte	3,195,15,1,5,26,9
	.half	.L662-.L200
	.byte	1,4,1,5,84,9
	.half	.L16-.L662
	.byte	3,186,112,1,5,16,9
	.half	.L18-.L16
	.byte	3,11,1,5,9,9
	.half	.L663-.L18
	.byte	1,5,67,7,9
	.half	.L664-.L663
	.byte	1,5,96,9
	.half	.L665-.L664
	.byte	1,5,45,9
	.half	.L19-.L665
	.byte	3,2,1,5,57,9
	.half	.L550-.L19
	.byte	3,3,1,4,2,5,20,9
	.half	.L207-.L550
	.byte	3,166,12,1,5,23,9
	.half	.L666-.L207
	.byte	1,9
	.half	.L551-.L666
	.byte	3,1,1,5,26,9
	.half	.L667-.L551
	.byte	1,5,12,9
	.half	.L552-.L667
	.byte	1,5,34,7,9
	.half	.L668-.L552
	.byte	1,5,41,9
	.half	.L669-.L668
	.byte	1,5,34,9
	.half	.L22-.L669
	.byte	1,5,5,9
	.half	.L23-.L22
	.byte	1,4,1,5,9,9
	.half	.L24-.L23
	.byte	3,217,115,1,5,42,7,9
	.half	.L208-.L24
	.byte	3,2,1,4,2,5,20,9
	.half	.L210-.L208
	.byte	3,203,11,1,5,23,9
	.half	.L670-.L210
	.byte	1,5,30,9
	.half	.L553-.L670
	.byte	3,1,1,5,33,9
	.half	.L671-.L553
	.byte	1,5,18,9
	.half	.L672-.L671
	.byte	1,4,1,5,29,9
	.half	.L25-.L672
	.byte	3,184,116,1,5,13,9
	.half	.L673-.L25
	.byte	1,5,60,7,9
	.half	.L674-.L673
	.byte	1,5,53,9
	.half	.L675-.L674
	.byte	1,5,101,7,9
	.half	.L676-.L675
	.byte	1,5,114,9
	.half	.L677-.L676
	.byte	1,5,32,7,9
	.half	.L678-.L677
	.byte	3,3,1,5,13,9
	.half	.L679-.L678
	.byte	1,5,38,7,9
	.half	.L680-.L679
	.byte	3,2,1,5,13,9
	.half	.L29-.L680
	.byte	3,126,1,5,26,9
	.half	.L30-.L29
	.byte	3,12,1,5,9,9
	.half	.L681-.L30
	.byte	1,5,30,7,9
	.half	.L682-.L681
	.byte	3,3,1,5,13,9
	.half	.L683-.L682
	.byte	1,5,49,7,9
	.half	.L684-.L683
	.byte	3,2,1,5,70,9
	.half	.L685-.L684
	.byte	1,5,51,9
	.half	.L32-.L685
	.byte	3,4,1,5,85,9
	.half	.L686-.L32
	.byte	1,4,2,5,28,9
	.half	.L212-.L686
	.byte	3,251,13,1,5,33,9
	.half	.L687-.L212
	.byte	1,5,21,9
	.half	.L554-.L687
	.byte	3,1,1,5,26,9
	.half	.L688-.L554
	.byte	1,5,35,9
	.half	.L556-.L688
	.byte	3,1,1,5,40,9
	.half	.L689-.L556
	.byte	1,5,38,9
	.half	.L557-.L689
	.byte	1,5,63,9
	.half	.L690-.L557
	.byte	1,5,47,9
	.half	.L691-.L690
	.byte	1,5,20,9
	.half	.L692-.L691
	.byte	1,4,1,5,61,9
	.half	.L213-.L692
	.byte	3,134,114,1,4,2,5,18,9
	.half	.L217-.L213
	.byte	3,241,14,1,5,23,9
	.half	.L693-.L217
	.byte	1,4,1,5,32,9
	.half	.L218-.L693
	.byte	3,146,113,1,5,13,9
	.half	.L694-.L218
	.byte	1,5,36,7,9
	.half	.L695-.L694
	.byte	3,2,1,5,34,9
	.half	.L696-.L695
	.byte	1,5,39,9
	.half	.L555-.L696
	.byte	1,5,48,9
	.half	.L33-.L555
	.byte	3,4,1,4,2,5,18,9
	.half	.L225-.L33
	.byte	3,244,14,1,5,25,9
	.half	.L697-.L225
	.byte	1,4,1,5,63,9
	.half	.L226-.L697
	.byte	3,140,113,1,5,58,9
	.half	.L31-.L226
	.byte	3,5,1,5,96,9
	.half	.L698-.L31
	.byte	1,5,60,9
	.half	.L34-.L698
	.byte	3,5,1,4,2,5,16,9
	.half	.L233-.L34
	.byte	3,228,14,1,5,23,9
	.half	.L699-.L233
	.byte	1,4,1,5,44,9
	.half	.L21-.L699
	.byte	3,161,113,1,5,5,9
	.half	.L560-.L21
	.byte	3,2,1,5,39,7,9
	.half	.L241-.L560
	.byte	3,2,1,5,9,9
	.half	.L542-.L241
	.byte	3,2,1,5,45,7,9
	.half	.L700-.L542
	.byte	3,2,1,4,2,5,41,9
	.half	.L245-.L700
	.byte	3,142,13,1,5,60,9
	.half	.L701-.L245
	.byte	1,4,3,5,40,9
	.half	.L252-.L701
	.byte	3,229,117,1,4,2,5,38,9
	.half	.L253-.L252
	.byte	3,156,10,1,5,48,9
	.half	.L702-.L253
	.byte	1,4,1,5,39,9
	.half	.L36-.L702
	.byte	3,244,114,1,5,9,9
	.half	.L561-.L36
	.byte	3,2,1,5,45,7,9
	.half	.L703-.L561
	.byte	3,2,1,4,2,5,41,9
	.half	.L264-.L703
	.byte	3,150,13,1,5,60,9
	.half	.L704-.L264
	.byte	1,4,3,5,40,9
	.half	.L270-.L704
	.byte	3,214,117,1,4,2,5,38,9
	.half	.L271-.L270
	.byte	3,171,10,1,5,48,9
	.half	.L705-.L271
	.byte	1,4,1,5,27,9
	.half	.L35-.L705
	.byte	3,239,114,1,5,5,9
	.half	.L706-.L35
	.byte	1,5,39,7,9
	.half	.L707-.L706
	.byte	3,2,1,5,65,9
	.half	.L708-.L707
	.byte	1,4,2,5,20,9
	.half	.L273-.L708
	.byte	3,187,10,1,5,23,9
	.half	.L709-.L273
	.byte	1,5,28,9
	.half	.L564-.L709
	.byte	3,1,1,5,31,9
	.half	.L710-.L564
	.byte	1,5,17,9
	.half	.L711-.L710
	.byte	1,4,1,5,42,9
	.half	.L274-.L711
	.byte	3,197,117,1,5,68,9
	.half	.L712-.L274
	.byte	1,5,95,9
	.half	.L713-.L712
	.byte	1,5,43,9
	.half	.L281-.L713
	.byte	3,3,1,5,69,9
	.half	.L714-.L281
	.byte	1,5,44,9
	.half	.L565-.L714
	.byte	3,1,1,5,78,9
	.half	.L715-.L565
	.byte	1,4,4,5,11,9
	.half	.L285-.L715
	.byte	3,45,1,5,19,9
	.half	.L716-.L285
	.byte	1,5,17,9
	.half	.L717-.L716
	.byte	1,5,11,9
	.half	.L718-.L717
	.byte	3,1,1,5,17,9
	.half	.L719-.L718
	.byte	1,5,11,9
	.half	.L295-.L719
	.byte	3,103,1,5,17,9
	.half	.L720-.L295
	.byte	1,5,11,9
	.half	.L286-.L720
	.byte	3,18,1,5,16,9
	.half	.L721-.L286
	.byte	1,4,1,5,27,9
	.half	.L38-.L721
	.byte	3,93,1,5,5,9
	.half	.L722-.L38
	.byte	1,5,39,7,9
	.half	.L723-.L722
	.byte	3,2,1,5,65,9
	.half	.L724-.L723
	.byte	1,4,2,5,20,9
	.half	.L303-.L724
	.byte	3,176,10,1,5,23,9
	.half	.L725-.L303
	.byte	1,5,28,9
	.half	.L566-.L725
	.byte	3,1,1,5,31,9
	.half	.L726-.L566
	.byte	1,5,17,9
	.half	.L727-.L726
	.byte	1,4,1,5,42,9
	.half	.L304-.L727
	.byte	3,208,117,1,5,68,9
	.half	.L728-.L304
	.byte	1,5,95,9
	.half	.L729-.L728
	.byte	1,5,43,9
	.half	.L306-.L729
	.byte	3,3,1,5,69,9
	.half	.L730-.L306
	.byte	1,5,44,9
	.half	.L567-.L730
	.byte	3,1,1,5,78,9
	.half	.L731-.L567
	.byte	1,4,4,5,11,9
	.half	.L308-.L731
	.byte	3,34,1,5,19,9
	.half	.L732-.L308
	.byte	1,5,17,9
	.half	.L733-.L732
	.byte	1,5,11,9
	.half	.L734-.L733
	.byte	3,1,1,5,17,9
	.half	.L735-.L734
	.byte	1,5,11,9
	.half	.L310-.L735
	.byte	3,103,1,5,17,9
	.half	.L736-.L310
	.byte	1,5,11,9
	.half	.L309-.L736
	.byte	3,18,1,5,16,9
	.half	.L737-.L309
	.byte	1,4,1,5,27,9
	.half	.L39-.L737
	.byte	3,104,1,5,5,9
	.half	.L738-.L39
	.byte	1,5,39,7,9
	.half	.L739-.L738
	.byte	3,2,1,5,65,9
	.half	.L740-.L739
	.byte	1,4,2,5,20,9
	.half	.L311-.L740
	.byte	3,165,10,1,5,23,9
	.half	.L741-.L311
	.byte	1,5,28,9
	.half	.L568-.L741
	.byte	3,1,1,5,31,9
	.half	.L742-.L568
	.byte	1,5,17,9
	.half	.L743-.L742
	.byte	1,4,1,5,42,9
	.half	.L312-.L743
	.byte	3,219,117,1,5,68,9
	.half	.L744-.L312
	.byte	1,5,95,9
	.half	.L745-.L744
	.byte	1,5,43,9
	.half	.L314-.L745
	.byte	3,3,1,5,69,9
	.half	.L746-.L314
	.byte	1,5,44,9
	.half	.L569-.L746
	.byte	3,1,1,5,78,9
	.half	.L747-.L569
	.byte	1,4,4,5,11,9
	.half	.L316-.L747
	.byte	3,23,1,5,19,9
	.half	.L748-.L316
	.byte	1,5,17,9
	.half	.L749-.L748
	.byte	1,5,11,9
	.half	.L750-.L749
	.byte	3,1,1,5,17,9
	.half	.L751-.L750
	.byte	1,5,11,9
	.half	.L318-.L751
	.byte	3,103,1,5,17,9
	.half	.L752-.L318
	.byte	1,5,11,9
	.half	.L317-.L752
	.byte	3,18,1,5,16,9
	.half	.L753-.L317
	.byte	1,4,1,5,27,9
	.half	.L40-.L753
	.byte	3,115,1,5,5,9
	.half	.L754-.L40
	.byte	1,5,39,7,9
	.half	.L755-.L754
	.byte	3,2,1,5,65,9
	.half	.L756-.L755
	.byte	1,4,2,5,20,9
	.half	.L319-.L756
	.byte	3,154,10,1,5,23,9
	.half	.L757-.L319
	.byte	1,5,28,9
	.half	.L570-.L757
	.byte	3,1,1,5,31,9
	.half	.L758-.L570
	.byte	1,5,17,9
	.half	.L759-.L758
	.byte	1,4,1,5,42,9
	.half	.L320-.L759
	.byte	3,230,117,1,5,68,9
	.half	.L760-.L320
	.byte	1,5,95,9
	.half	.L761-.L760
	.byte	1,5,43,9
	.half	.L322-.L761
	.byte	3,3,1,5,69,9
	.half	.L762-.L322
	.byte	1,5,44,9
	.half	.L571-.L762
	.byte	3,1,1,5,78,9
	.half	.L763-.L571
	.byte	1,4,4,5,11,9
	.half	.L324-.L763
	.byte	3,12,1,5,19,9
	.half	.L764-.L324
	.byte	1,5,17,9
	.half	.L765-.L764
	.byte	1,5,11,9
	.half	.L766-.L765
	.byte	3,1,1,5,17,9
	.half	.L767-.L766
	.byte	1,5,11,9
	.half	.L326-.L767
	.byte	3,103,1,5,17,9
	.half	.L768-.L326
	.byte	1,5,11,9
	.half	.L325-.L768
	.byte	3,18,1,5,16,9
	.half	.L769-.L325
	.byte	1,4,1,5,28,9
	.half	.L41-.L769
	.byte	3,126,1,5,20,9
	.half	.L770-.L41
	.byte	1,5,28,9
	.half	.L771-.L770
	.byte	3,1,1,5,20,9
	.half	.L540-.L771
	.byte	1,5,1,9
	.half	.L772-.L540
	.byte	3,11,1,7,9
	.half	.L97-.L772
	.byte	0,1,1
.L626:
	.sdecl	'.debug_ranges',debug,cluster('IfxCcu6_Timer_initModule')
	.sect	'.debug_ranges'
.L96:
	.word	-1,.L76,0,.L97-.L76,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu6_Timer_initModuleConfig')
	.sect	'.debug_info'
.L98:
	.word	373
	.half	3
	.word	.L99
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Ccu6/Timer/IfxCcu6_Timer.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L101,.L100
	.byte	2
	.word	.L89
	.byte	3
	.byte	'IfxCcu6_Timer_initModuleConfig',0,1,155,2,6,1,1,1
	.word	.L78,.L327,.L77
	.byte	4
	.byte	'config',0,1,155,2,59
	.word	.L328,.L329
	.byte	4
	.byte	'ccu6',0,1,155,2,77
	.word	.L138,.L330
	.byte	5
	.word	.L78,.L327
	.byte	6
	.byte	'defaultConfig',0,1,157,2,32
	.word	.L331,.L332
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu6_Timer_initModuleConfig')
	.sect	'.debug_abbrev'
.L99:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxCcu6_Timer_initModuleConfig')
	.sect	'.debug_line'
.L100:
	.word	.L774-.L773
.L773:
	.half	3
	.word	.L776-.L775
.L775:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Ccu6/Timer/IfxCcu6_Timer.c',0,0,0,0,0
.L776:
	.byte	5,6,7,0,5,2
	.word	.L78
	.byte	3,154,2,1,5,48,9
	.half	.L572-.L78
	.byte	3,2,1,5,46,9
	.half	.L777-.L572
	.byte	1,5,15,9
	.half	.L778-.L777
	.byte	3,203,0,1,5,13,9
	.half	.L779-.L778
	.byte	1,5,18,9
	.half	.L780-.L779
	.byte	3,3,1,5,1,9
	.half	.L781-.L780
	.byte	3,1,1,7,9
	.half	.L102-.L781
	.byte	0,1,1
.L774:
	.sdecl	'.debug_ranges',debug,cluster('IfxCcu6_Timer_initModuleConfig')
	.sect	'.debug_ranges'
.L101:
	.word	-1,.L78,0,.L102-.L78,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu6_Timer_countOneStep')
	.sect	'.debug_info'
.L103:
	.word	801
	.half	3
	.word	.L104
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Ccu6/Timer/IfxCcu6_Timer.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L106,.L105
	.byte	2
	.word	.L89
	.byte	3
	.byte	'IfxCcu6_Timer_countOneStep',0,1,56,6,1,1,1
	.word	.L74,.L333,.L73
	.byte	4
	.byte	'timer',0,1,56,48
	.word	.L134,.L334
	.byte	5
	.word	.L74,.L333
	.byte	6
	.word	.L335,.L336,.L337
	.byte	7
	.word	.L338,.L339
	.byte	7
	.word	.L340,.L341
	.byte	7
	.word	.L342,.L343
	.byte	8
	.word	.L344,.L336,.L337
	.byte	9
	.byte	'tctr4',0,2,200,12,20
	.word	.L345,.L346
	.byte	0,0,6
	.word	.L163,.L347,.L348
	.byte	7
	.word	.L166,.L349
	.byte	7
	.word	.L168,.L350
	.byte	7
	.word	.L170,.L351
	.byte	8
	.word	.L172,.L347,.L348
	.byte	9
	.byte	'shift',0,2,171,15,12
	.word	.L153,.L352
	.byte	9
	.byte	'mask',0,2,172,15,12
	.word	.L153,.L353
	.byte	0,0,6
	.word	.L354,.L355,.L356
	.byte	7
	.word	.L357,.L358
	.byte	7
	.word	.L359,.L360
	.byte	7
	.word	.L361,.L362
	.byte	8
	.word	.L363,.L355,.L356
	.byte	9
	.byte	'tctr4',0,2,255,11,20
	.word	.L345,.L364
	.byte	0,0,6
	.word	.L335,.L365,.L366
	.byte	7
	.word	.L338,.L339
	.byte	7
	.word	.L340,.L341
	.byte	7
	.word	.L342,.L343
	.byte	8
	.word	.L344,.L365,.L366
	.byte	9
	.byte	'tctr4',0,2,200,12,20
	.word	.L345,.L367
	.byte	0,0,6
	.word	.L163,.L368,.L369
	.byte	7
	.word	.L166,.L349
	.byte	7
	.word	.L168,.L350
	.byte	7
	.word	.L170,.L351
	.byte	8
	.word	.L172,.L368,.L369
	.byte	9
	.byte	'shift',0,2,171,15,12
	.word	.L153,.L370
	.byte	9
	.byte	'mask',0,2,172,15,12
	.word	.L153,.L371
	.byte	0,0,6
	.word	.L354,.L372,.L3
	.byte	7
	.word	.L357,.L358
	.byte	7
	.word	.L359,.L360
	.byte	7
	.word	.L361,.L362
	.byte	8
	.word	.L363,.L372,.L3
	.byte	9
	.byte	'tctr4',0,2,255,11,20
	.word	.L345,.L373
	.byte	0,0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu6_Timer_countOneStep')
	.sect	'.debug_abbrev'
.L104:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,29,1,49
	.byte	16,17,1,18,1,0,0,7,5,0,49,16,2,6,0,0,8,11,1,49,16,17,1,18,1,0,0,9,52,0,3,8,58,15,59,15,57,15,73,16,2,6
	.byte	0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxCcu6_Timer_countOneStep')
	.sect	'.debug_line'
.L105:
	.word	.L783-.L782
.L782:
	.half	3
	.word	.L785-.L784
.L784:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Ccu6/Timer/IfxCcu6_Timer.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Ccu6\\Std\\IfxCcu6.h',0
	.byte	0,0,0,0
.L785:
	.byte	5,14,7,0,5,2
	.word	.L74
	.byte	3,57,1,5,5,9
	.half	.L786-.L74
	.byte	1,5,43,7,9
	.half	.L787-.L786
	.byte	3,3,1,5,51,9
	.half	.L788-.L787
	.byte	1,5,58,9
	.half	.L789-.L788
	.byte	1,4,2,5,20,9
	.half	.L336-.L789
	.byte	3,140,12,1,9
	.half	.L527-.L336
	.byte	3,1,1,9
	.half	.L790-.L527
	.byte	3,1,1,9
	.half	.L791-.L790
	.byte	3,1,1,4,1,5,43,9
	.half	.L337-.L791
	.byte	3,244,115,1,5,51,9
	.half	.L792-.L337
	.byte	1,5,72,9
	.half	.L793-.L792
	.byte	1,4,2,5,28,9
	.half	.L347-.L793
	.byte	3,235,14,1,5,33,9
	.half	.L528-.L347
	.byte	1,5,21,9
	.half	.L794-.L528
	.byte	3,1,1,5,26,9
	.half	.L795-.L794
	.byte	1,5,35,9
	.half	.L530-.L795
	.byte	3,1,1,5,40,9
	.half	.L796-.L530
	.byte	1,5,38,9
	.half	.L531-.L796
	.byte	1,5,63,9
	.half	.L797-.L531
	.byte	1,5,47,9
	.half	.L798-.L797
	.byte	1,5,20,9
	.half	.L799-.L798
	.byte	1,4,1,5,39,9
	.half	.L348-.L799
	.byte	3,148,113,1,5,47,9
	.half	.L800-.L348
	.byte	1,5,54,9
	.half	.L801-.L800
	.byte	1,4,2,5,20,9
	.half	.L355-.L801
	.byte	3,191,11,1,9
	.half	.L529-.L355
	.byte	3,1,1,9
	.half	.L802-.L529
	.byte	3,1,1,9
	.half	.L803-.L802
	.byte	3,1,1,4,1,5,63,9
	.half	.L356-.L803
	.byte	3,186,116,1,5,19,9
	.half	.L2-.L356
	.byte	3,6,1,5,10,9
	.half	.L804-.L2
	.byte	1,5,43,7,9
	.half	.L805-.L804
	.byte	3,3,1,5,51,9
	.half	.L806-.L805
	.byte	1,5,57,9
	.half	.L807-.L806
	.byte	1,4,2,5,20,9
	.half	.L365-.L807
	.byte	3,131,12,1,9
	.half	.L532-.L365
	.byte	3,1,1,9
	.half	.L808-.L532
	.byte	3,1,1,9
	.half	.L809-.L808
	.byte	3,1,1,4,1,5,43,9
	.half	.L366-.L809
	.byte	3,253,115,1,5,51,9
	.half	.L810-.L366
	.byte	1,5,72,9
	.half	.L811-.L810
	.byte	1,4,2,5,28,9
	.half	.L368-.L811
	.byte	3,226,14,1,5,33,9
	.half	.L533-.L368
	.byte	1,5,21,9
	.half	.L812-.L533
	.byte	3,1,1,5,26,9
	.half	.L813-.L812
	.byte	1,5,35,9
	.half	.L535-.L813
	.byte	3,1,1,5,40,9
	.half	.L814-.L535
	.byte	1,5,38,9
	.half	.L536-.L814
	.byte	1,5,63,9
	.half	.L815-.L536
	.byte	1,5,47,9
	.half	.L816-.L815
	.byte	1,5,20,9
	.half	.L817-.L816
	.byte	1,4,1,5,39,9
	.half	.L369-.L817
	.byte	3,157,113,1,5,47,9
	.half	.L818-.L369
	.byte	1,5,53,9
	.half	.L819-.L818
	.byte	1,4,2,5,20,9
	.half	.L372-.L819
	.byte	3,182,11,1,9
	.half	.L534-.L372
	.byte	3,1,1,9
	.half	.L820-.L534
	.byte	3,1,1,9
	.half	.L821-.L820
	.byte	3,1,1,4,1,5,1,9
	.half	.L3-.L821
	.byte	3,201,116,1,7,9
	.half	.L107-.L3
	.byte	0,1,1
.L783:
	.sdecl	'.debug_ranges',debug,cluster('IfxCcu6_Timer_countOneStep')
	.sect	'.debug_ranges'
.L106:
	.word	-1,.L74,0,.L107-.L74,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu6_Timer_start')
	.sect	'.debug_info'
.L108:
	.word	796
	.half	3
	.word	.L109
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Ccu6/Timer/IfxCcu6_Timer.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L111,.L110
	.byte	2
	.word	.L89
	.byte	3
	.byte	'IfxCcu6_Timer_start',0,1,239,2,6,1,1,1
	.word	.L80,.L374,.L79
	.byte	4
	.byte	'timer',0,1,239,2,41
	.word	.L134,.L375
	.byte	5
	.word	.L80,.L374
	.byte	6
	.word	.L335,.L376,.L377
	.byte	7
	.word	.L338,.L378
	.byte	7
	.word	.L340,.L379
	.byte	7
	.word	.L342,.L380
	.byte	8
	.word	.L344,.L376,.L377
	.byte	9
	.byte	'tctr4',0,2,200,12,20
	.word	.L345,.L381
	.byte	0,0,6
	.word	.L382,.L383,.L384
	.byte	7
	.word	.L385,.L386
	.byte	7
	.word	.L387,.L388
	.byte	7
	.word	.L389,.L390
	.byte	8
	.word	.L391,.L383,.L384
	.byte	9
	.byte	'shift',0,2,197,15,12
	.word	.L153,.L392
	.byte	9
	.byte	'mask',0,2,198,15,12
	.word	.L153,.L393
	.byte	0,0,6
	.word	.L394,.L395,.L48
	.byte	7
	.word	.L396,.L397
	.byte	7
	.word	.L398,.L399
	.byte	7
	.word	.L400,.L401
	.byte	8
	.word	.L402,.L395,.L48
	.byte	9
	.byte	'tctr4',0,2,225,16,20
	.word	.L345,.L403
	.byte	0,0,6
	.word	.L335,.L404,.L405
	.byte	7
	.word	.L338,.L378
	.byte	7
	.word	.L340,.L379
	.byte	7
	.word	.L342,.L380
	.byte	8
	.word	.L344,.L404,.L405
	.byte	9
	.byte	'tctr4',0,2,200,12,20
	.word	.L345,.L406
	.byte	0,0,6
	.word	.L382,.L407,.L408
	.byte	7
	.word	.L385,.L386
	.byte	7
	.word	.L387,.L388
	.byte	7
	.word	.L389,.L390
	.byte	8
	.word	.L391,.L407,.L408
	.byte	9
	.byte	'shift',0,2,197,15,12
	.word	.L153,.L409
	.byte	9
	.byte	'mask',0,2,198,15,12
	.word	.L153,.L410
	.byte	0,0,6
	.word	.L394,.L411,.L54
	.byte	7
	.word	.L396,.L397
	.byte	7
	.word	.L398,.L399
	.byte	7
	.word	.L400,.L401
	.byte	8
	.word	.L402,.L411,.L54
	.byte	9
	.byte	'tctr4',0,2,225,16,20
	.word	.L345,.L412
	.byte	0,0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu6_Timer_start')
	.sect	'.debug_abbrev'
.L109:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,29,1,49
	.byte	16,17,1,18,1,0,0,7,5,0,49,16,2,6,0,0,8,11,1,49,16,17,1,18,1,0,0,9,52,0,3,8,58,15,59,15,57,15,73,16,2,6
	.byte	0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxCcu6_Timer_start')
	.sect	'.debug_line'
.L110:
	.word	.L823-.L822
.L822:
	.half	3
	.word	.L825-.L824
.L824:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Ccu6/Timer/IfxCcu6_Timer.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Ccu6\\Std\\IfxCcu6.h',0
	.byte	0,0,0,0
.L825:
	.byte	5,6,7,0,5,2
	.word	.L80
	.byte	3,238,2,1,5,14,9
	.half	.L574-.L80
	.byte	3,4,1,5,5,9
	.half	.L826-.L574
	.byte	1,5,43,7,9
	.half	.L827-.L826
	.byte	3,3,1,5,51,9
	.half	.L828-.L827
	.byte	1,5,58,9
	.half	.L829-.L828
	.byte	1,4,2,5,20,9
	.half	.L376-.L829
	.byte	3,211,9,1,9
	.half	.L576-.L376
	.byte	3,1,1,9
	.half	.L830-.L576
	.byte	3,1,1,9
	.half	.L831-.L830
	.byte	3,1,1,4,1,5,29,9
	.half	.L377-.L831
	.byte	3,173,118,1,5,14,9
	.half	.L832-.L377
	.byte	1,5,31,7,9
	.half	.L833-.L832
	.byte	3,2,1,5,13,9
	.half	.L834-.L833
	.byte	1,5,49,7,9
	.half	.L835-.L834
	.byte	3,3,1,5,57,9
	.half	.L836-.L835
	.byte	1,5,92,9
	.half	.L837-.L836
	.byte	1,4,2,5,28,9
	.half	.L383-.L837
	.byte	3,199,12,1,5,33,9
	.half	.L838-.L383
	.byte	1,5,21,9
	.half	.L577-.L838
	.byte	3,1,1,5,26,9
	.half	.L839-.L577
	.byte	1,5,33,9
	.half	.L579-.L839
	.byte	3,1,1,5,38,9
	.half	.L840-.L579
	.byte	1,5,36,9
	.half	.L580-.L840
	.byte	1,5,61,9
	.half	.L841-.L580
	.byte	1,5,45,9
	.half	.L842-.L841
	.byte	1,5,19,9
	.half	.L843-.L842
	.byte	1,4,1,5,48,9
	.half	.L384-.L843
	.byte	3,184,115,1,5,70,9
	.half	.L573-.L384
	.byte	1,5,113,9
	.half	.L578-.L573
	.byte	3,127,1,5,41,9
	.half	.L47-.L578
	.byte	3,6,1,5,49,9
	.half	.L844-.L47
	.byte	1,5,56,9
	.half	.L845-.L844
	.byte	1,4,2,5,19,9
	.half	.L395-.L845
	.byte	3,222,13,1,9
	.half	.L581-.L395
	.byte	3,1,1,9
	.half	.L846-.L581
	.byte	3,1,1,9
	.half	.L847-.L846
	.byte	3,1,1,4,1,5,13,9
	.half	.L48-.L847
	.byte	3,156,114,1,5,9,9
	.half	.L49-.L48
	.byte	3,6,1,5,15,9
	.half	.L50-.L49
	.byte	3,8,1,5,9,9
	.half	.L848-.L50
	.byte	1,5,65,7,9
	.half	.L849-.L848
	.byte	1,5,43,9
	.half	.L51-.L849
	.byte	3,3,1,5,51,9
	.half	.L850-.L51
	.byte	1,5,57,9
	.half	.L851-.L850
	.byte	1,4,2,5,20,9
	.half	.L404-.L851
	.byte	3,183,9,1,9
	.half	.L582-.L404
	.byte	3,1,1,9
	.half	.L852-.L582
	.byte	3,1,1,9
	.half	.L853-.L852
	.byte	3,1,1,4,1,5,27,9
	.half	.L405-.L853
	.byte	3,201,118,1,5,9,9
	.half	.L854-.L405
	.byte	1,5,45,7,9
	.half	.L855-.L854
	.byte	3,3,1,5,53,9
	.half	.L856-.L855
	.byte	1,5,88,9
	.half	.L857-.L856
	.byte	1,4,2,5,28,9
	.half	.L407-.L857
	.byte	3,173,12,1,5,33,9
	.half	.L858-.L407
	.byte	1,5,21,9
	.half	.L583-.L858
	.byte	3,1,1,5,26,9
	.half	.L859-.L583
	.byte	1,5,33,9
	.half	.L585-.L859
	.byte	3,1,1,5,38,9
	.half	.L860-.L585
	.byte	1,5,36,9
	.half	.L586-.L860
	.byte	1,5,61,9
	.half	.L861-.L586
	.byte	1,5,45,9
	.half	.L862-.L861
	.byte	1,5,19,9
	.half	.L863-.L862
	.byte	1,4,1,5,44,9
	.half	.L408-.L863
	.byte	3,210,115,1,5,66,9
	.half	.L864-.L408
	.byte	1,5,109,9
	.half	.L584-.L864
	.byte	3,127,1,5,37,9
	.half	.L53-.L584
	.byte	3,6,1,5,45,9
	.half	.L575-.L53
	.byte	1,5,51,9
	.half	.L865-.L575
	.byte	1,4,2,5,19,9
	.half	.L411-.L865
	.byte	3,196,13,1,9
	.half	.L587-.L411
	.byte	3,1,1,9
	.half	.L866-.L587
	.byte	3,1,1,9
	.half	.L867-.L866
	.byte	3,1,1,4,1,5,9,9
	.half	.L54-.L867
	.byte	3,182,114,1,5,1,9
	.half	.L55-.L54
	.byte	3,8,1,7,9
	.half	.L112-.L55
	.byte	0,1,1
.L823:
	.sdecl	'.debug_ranges',debug,cluster('IfxCcu6_Timer_start')
	.sect	'.debug_ranges'
.L111:
	.word	-1,.L80,0,.L112-.L80,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu6_Timer_startSingleShotMode')
	.sect	'.debug_info'
.L113:
	.word	938
	.half	3
	.word	.L114
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Ccu6/Timer/IfxCcu6_Timer.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L116,.L115
	.byte	2
	.word	.L89
	.byte	3
	.byte	'IfxCcu6_Timer_startSingleShotMode',0,1,166,3,6,1,1,1
	.word	.L82,.L413,.L81
	.byte	4
	.byte	'timer',0,1,166,3,55
	.word	.L134,.L414
	.byte	5
	.word	.L82,.L413
	.byte	6
	.word	.L335,.L415,.L416
	.byte	7
	.word	.L338,.L417
	.byte	7
	.word	.L340,.L418
	.byte	7
	.word	.L342,.L419
	.byte	8
	.word	.L344,.L415,.L416
	.byte	9
	.byte	'tctr4',0,2,200,12,20
	.word	.L345,.L420
	.byte	0,0,6
	.word	.L421,.L422,.L423
	.byte	7
	.word	.L424,.L425
	.byte	7
	.word	.L426,.L427
	.byte	8
	.word	.L428,.L422,.L423
	.byte	9
	.byte	'mask',0,2,210,12,12
	.word	.L153,.L429
	.byte	0,0,6
	.word	.L382,.L430,.L431
	.byte	7
	.word	.L385,.L432
	.byte	7
	.word	.L387,.L433
	.byte	7
	.word	.L389,.L434
	.byte	8
	.word	.L391,.L430,.L431
	.byte	9
	.byte	'shift',0,2,197,15,12
	.word	.L153,.L435
	.byte	9
	.byte	'mask',0,2,198,15,12
	.word	.L153,.L436
	.byte	0,0,6
	.word	.L394,.L437,.L59
	.byte	7
	.word	.L396,.L438
	.byte	7
	.word	.L398,.L439
	.byte	7
	.word	.L400,.L440
	.byte	8
	.word	.L402,.L437,.L59
	.byte	9
	.byte	'tctr4',0,2,225,16,20
	.word	.L345,.L441
	.byte	0,0,6
	.word	.L335,.L442,.L443
	.byte	7
	.word	.L338,.L417
	.byte	7
	.word	.L340,.L418
	.byte	7
	.word	.L342,.L419
	.byte	8
	.word	.L344,.L442,.L443
	.byte	9
	.byte	'tctr4',0,2,200,12,20
	.word	.L345,.L444
	.byte	0,0,6
	.word	.L421,.L445,.L446
	.byte	7
	.word	.L424,.L425
	.byte	7
	.word	.L426,.L427
	.byte	8
	.word	.L428,.L445,.L446
	.byte	9
	.byte	'mask',0,2,210,12,12
	.word	.L153,.L447
	.byte	0,0,6
	.word	.L382,.L448,.L449
	.byte	7
	.word	.L385,.L432
	.byte	7
	.word	.L387,.L433
	.byte	7
	.word	.L389,.L434
	.byte	8
	.word	.L391,.L448,.L449
	.byte	9
	.byte	'shift',0,2,197,15,12
	.word	.L153,.L450
	.byte	9
	.byte	'mask',0,2,198,15,12
	.word	.L153,.L451
	.byte	0,0,6
	.word	.L394,.L452,.L65
	.byte	7
	.word	.L396,.L438
	.byte	7
	.word	.L398,.L439
	.byte	7
	.word	.L400,.L440
	.byte	8
	.word	.L402,.L452,.L65
	.byte	9
	.byte	'tctr4',0,2,225,16,20
	.word	.L345,.L453
	.byte	0,0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu6_Timer_startSingleShotMode')
	.sect	'.debug_abbrev'
.L114:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,29,1,49
	.byte	16,17,1,18,1,0,0,7,5,0,49,16,2,6,0,0,8,11,1,49,16,17,1,18,1,0,0,9,52,0,3,8,58,15,59,15,57,15,73,16,2,6
	.byte	0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxCcu6_Timer_startSingleShotMode')
	.sect	'.debug_line'
.L115:
	.word	.L869-.L868
.L868:
	.half	3
	.word	.L871-.L870
.L870:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Ccu6/Timer/IfxCcu6_Timer.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Ccu6\\Std\\IfxCcu6.h',0
	.byte	0,0,0,0
.L871:
	.byte	5,6,7,0,5,2
	.word	.L82
	.byte	3,165,3,1,5,14,9
	.half	.L589-.L82
	.byte	3,4,1,5,5,9
	.half	.L872-.L589
	.byte	1,5,43,7,9
	.half	.L873-.L872
	.byte	3,3,1,5,51,9
	.half	.L874-.L873
	.byte	1,5,58,9
	.half	.L875-.L874
	.byte	1,4,2,5,20,9
	.half	.L415-.L875
	.byte	3,156,9,1,9
	.half	.L591-.L415
	.byte	3,1,1,9
	.half	.L876-.L591
	.byte	3,1,1,9
	.half	.L877-.L876
	.byte	3,1,1,4,1,5,43,9
	.half	.L416-.L877
	.byte	3,228,118,1,5,51,9
	.half	.L878-.L416
	.byte	1,4,2,5,20,9
	.half	.L422-.L878
	.byte	3,162,9,1,5,23,9
	.half	.L879-.L422
	.byte	1,5,32,9
	.half	.L593-.L879
	.byte	3,1,1,5,35,9
	.half	.L880-.L593
	.byte	1,5,19,9
	.half	.L881-.L880
	.byte	1,4,1,5,29,9
	.half	.L423-.L881
	.byte	3,224,118,1,5,14,9
	.half	.L594-.L423
	.byte	1,5,31,7,9
	.half	.L882-.L594
	.byte	3,2,1,5,13,9
	.half	.L883-.L882
	.byte	1,5,49,7,9
	.half	.L884-.L883
	.byte	3,3,1,5,57,9
	.half	.L885-.L884
	.byte	1,5,92,9
	.half	.L886-.L885
	.byte	1,4,2,5,28,9
	.half	.L430-.L886
	.byte	3,141,12,1,5,33,9
	.half	.L887-.L430
	.byte	1,5,21,9
	.half	.L592-.L887
	.byte	3,1,1,5,26,9
	.half	.L888-.L592
	.byte	1,5,33,9
	.half	.L596-.L888
	.byte	3,1,1,5,38,9
	.half	.L889-.L596
	.byte	1,5,36,9
	.half	.L597-.L889
	.byte	1,5,61,9
	.half	.L890-.L597
	.byte	1,5,45,9
	.half	.L891-.L890
	.byte	1,5,19,9
	.half	.L892-.L891
	.byte	1,4,1,5,48,9
	.half	.L431-.L892
	.byte	3,242,115,1,5,70,9
	.half	.L588-.L431
	.byte	1,5,113,9
	.half	.L595-.L588
	.byte	3,127,1,5,41,9
	.half	.L58-.L595
	.byte	3,6,1,5,49,9
	.half	.L893-.L58
	.byte	1,5,56,9
	.half	.L894-.L893
	.byte	1,4,2,5,19,9
	.half	.L437-.L894
	.byte	3,164,13,1,9
	.half	.L598-.L437
	.byte	3,1,1,9
	.half	.L895-.L598
	.byte	3,1,1,9
	.half	.L896-.L895
	.byte	3,1,1,4,1,5,13,9
	.half	.L59-.L896
	.byte	3,214,114,1,5,9,9
	.half	.L60-.L59
	.byte	3,6,1,5,15,9
	.half	.L61-.L60
	.byte	3,8,1,5,9,9
	.half	.L897-.L61
	.byte	1,5,65,7,9
	.half	.L898-.L897
	.byte	1,5,43,9
	.half	.L62-.L898
	.byte	3,3,1,5,51,9
	.half	.L899-.L62
	.byte	1,5,57,9
	.half	.L900-.L899
	.byte	1,4,2,5,20,9
	.half	.L442-.L900
	.byte	3,253,8,1,9
	.half	.L599-.L442
	.byte	3,1,1,9
	.half	.L901-.L599
	.byte	3,1,1,9
	.half	.L902-.L901
	.byte	3,1,1,4,1,5,43,9
	.half	.L443-.L902
	.byte	3,131,119,1,5,51,9
	.half	.L903-.L443
	.byte	1,4,2,5,20,9
	.half	.L445-.L903
	.byte	3,131,9,1,5,23,9
	.half	.L904-.L445
	.byte	1,5,32,9
	.half	.L601-.L904
	.byte	3,1,1,5,35,9
	.half	.L905-.L601
	.byte	1,5,19,9
	.half	.L906-.L905
	.byte	1,4,1,5,27,9
	.half	.L446-.L906
	.byte	3,254,118,1,5,9,9
	.half	.L602-.L446
	.byte	1,5,45,7,9
	.half	.L907-.L602
	.byte	3,3,1,5,53,9
	.half	.L908-.L907
	.byte	1,5,88,9
	.half	.L909-.L908
	.byte	1,4,2,5,28,9
	.half	.L448-.L909
	.byte	3,241,11,1,5,33,9
	.half	.L910-.L448
	.byte	1,5,21,9
	.half	.L600-.L910
	.byte	3,1,1,5,26,9
	.half	.L911-.L600
	.byte	1,5,33,9
	.half	.L604-.L911
	.byte	3,1,1,5,38,9
	.half	.L912-.L604
	.byte	1,5,36,9
	.half	.L605-.L912
	.byte	1,5,61,9
	.half	.L913-.L605
	.byte	1,5,45,9
	.half	.L914-.L913
	.byte	1,5,19,9
	.half	.L915-.L914
	.byte	1,4,1,5,44,9
	.half	.L449-.L915
	.byte	3,142,116,1,5,66,9
	.half	.L916-.L449
	.byte	1,5,109,9
	.half	.L603-.L916
	.byte	3,127,1,5,37,9
	.half	.L64-.L603
	.byte	3,6,1,5,45,9
	.half	.L590-.L64
	.byte	1,5,51,9
	.half	.L917-.L590
	.byte	1,4,2,5,19,9
	.half	.L452-.L917
	.byte	3,136,13,1,9
	.half	.L606-.L452
	.byte	3,1,1,9
	.half	.L918-.L606
	.byte	3,1,1,9
	.half	.L919-.L918
	.byte	3,1,1,4,1,5,9,9
	.half	.L65-.L919
	.byte	3,242,114,1,5,1,9
	.half	.L66-.L65
	.byte	3,8,1,7,9
	.half	.L117-.L66
	.byte	0,1,1
.L869:
	.sdecl	'.debug_ranges',debug,cluster('IfxCcu6_Timer_startSingleShotMode')
	.sect	'.debug_ranges'
.L116:
	.word	-1,.L82,0,.L117-.L82,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu6_Timer_stop')
	.sect	'.debug_info'
.L118:
	.word	885
	.half	3
	.word	.L119
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Ccu6/Timer/IfxCcu6_Timer.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L121,.L120
	.byte	2
	.word	.L89
	.byte	3
	.byte	'IfxCcu6_Timer_stop',0,1,226,3,6,1,1,1
	.word	.L84,.L454,.L83
	.byte	4
	.byte	'timer',0,1,226,3,40
	.word	.L134,.L455
	.byte	5
	.word	.L84,.L454
	.byte	6
	.word	.L456,.L457,.L458
	.byte	7
	.word	.L459,.L460
	.byte	7
	.word	.L461,.L462
	.byte	7
	.word	.L463,.L464
	.byte	8
	.word	.L465,.L457,.L458
	.byte	9
	.byte	'tctr4',0,2,203,11,20
	.word	.L345,.L466
	.byte	0,0,6
	.word	.L382,.L467,.L69
	.byte	7
	.word	.L385,.L468
	.byte	7
	.word	.L387,.L469
	.byte	7
	.word	.L389,.L470
	.byte	8
	.word	.L391,.L467,.L69
	.byte	9
	.byte	'shift',0,2,197,15,12
	.word	.L153,.L471
	.byte	9
	.byte	'mask',0,2,198,15,12
	.word	.L153,.L472
	.byte	0,0,6
	.word	.L191,.L473,.L474
	.byte	7
	.word	.L194,.L475
	.byte	7
	.word	.L196,.L476
	.byte	10
	.word	.L198,.L473,.L474
	.byte	0,6
	.word	.L199,.L477,.L70
	.byte	7
	.word	.L201,.L478
	.byte	7
	.word	.L203,.L479
	.byte	10
	.word	.L205,.L477,.L70
	.byte	0,6
	.word	.L480,.L481,.L482
	.byte	7
	.word	.L483,.L484
	.byte	7
	.word	.L485,.L486
	.byte	7
	.word	.L487,.L488
	.byte	8
	.word	.L489,.L481,.L482
	.byte	9
	.byte	'tctr4',0,2,235,16,20
	.word	.L345,.L490
	.byte	0,0,6
	.word	.L456,.L491,.L492
	.byte	7
	.word	.L459,.L460
	.byte	7
	.word	.L461,.L462
	.byte	7
	.word	.L463,.L464
	.byte	8
	.word	.L465,.L491,.L492
	.byte	9
	.byte	'tctr4',0,2,203,11,20
	.word	.L345,.L493
	.byte	0,0,6
	.word	.L382,.L494,.L72
	.byte	7
	.word	.L385,.L468
	.byte	7
	.word	.L387,.L469
	.byte	7
	.word	.L389,.L470
	.byte	8
	.word	.L391,.L494,.L72
	.byte	9
	.byte	'shift',0,2,197,15,12
	.word	.L153,.L495
	.byte	9
	.byte	'mask',0,2,198,15,12
	.word	.L153,.L496
	.byte	0,0,6
	.word	.L480,.L497,.L71
	.byte	7
	.word	.L483,.L484
	.byte	7
	.word	.L485,.L486
	.byte	7
	.word	.L487,.L488
	.byte	8
	.word	.L489,.L497,.L71
	.byte	9
	.byte	'tctr4',0,2,235,16,20
	.word	.L345,.L498
	.byte	0,0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu6_Timer_stop')
	.sect	'.debug_abbrev'
.L119:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,29,1,49
	.byte	16,17,1,18,1,0,0,7,5,0,49,16,2,6,0,0,8,11,1,49,16,17,1,18,1,0,0,9,52,0,3,8,58,15,59,15,57,15,73,16,2,6
	.byte	0,0,10,11,0,49,16,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxCcu6_Timer_stop')
	.sect	'.debug_line'
.L120:
	.word	.L921-.L920
.L920:
	.half	3
	.word	.L923-.L922
.L922:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Ccu6/Timer/IfxCcu6_Timer.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Ccu6\\Std\\IfxCcu6.h',0
	.byte	0,0,0,0
.L923:
	.byte	5,15,7,0,5,2
	.word	.L84
	.byte	3,229,3,1,5,9,9
	.half	.L924-.L84
	.byte	1,5,65,7,9
	.half	.L925-.L924
	.byte	1,5,44,9
	.half	.L67-.L925
	.byte	3,3,1,5,52,9
	.half	.L926-.L67
	.byte	1,5,59,9
	.half	.L927-.L926
	.byte	1,4,2,5,20,9
	.half	.L457-.L927
	.byte	3,227,7,1,9
	.half	.L607-.L457
	.byte	3,1,1,9
	.half	.L928-.L607
	.byte	3,1,1,9
	.half	.L929-.L928
	.byte	3,1,1,4,1,5,27,9
	.half	.L458-.L929
	.byte	3,157,120,1,5,9,9
	.half	.L930-.L458
	.byte	1,5,45,7,9
	.half	.L931-.L930
	.byte	3,2,1,5,53,9
	.half	.L932-.L931
	.byte	1,5,74,9
	.half	.L933-.L932
	.byte	1,4,2,5,28,9
	.half	.L467-.L933
	.byte	3,215,11,1,5,33,9
	.half	.L934-.L467
	.byte	1,5,21,9
	.half	.L608-.L934
	.byte	3,1,1,5,26,9
	.half	.L935-.L608
	.byte	1,5,33,9
	.half	.L609-.L935
	.byte	3,1,1,5,38,9
	.half	.L936-.L609
	.byte	1,5,36,9
	.half	.L610-.L936
	.byte	1,5,61,9
	.half	.L937-.L610
	.byte	1,5,45,9
	.half	.L938-.L937
	.byte	1,5,19,9
	.half	.L939-.L938
	.byte	1,4,1,5,27,9
	.half	.L69-.L939
	.byte	3,171,116,1,5,9,9
	.half	.L940-.L69
	.byte	1,5,49,7,9
	.half	.L941-.L940
	.byte	3,2,1,5,57,9
	.half	.L942-.L941
	.byte	1,4,2,5,18,9
	.half	.L473-.L942
	.byte	3,218,12,1,5,26,9
	.half	.L943-.L473
	.byte	1,4,1,5,54,9
	.half	.L474-.L943
	.byte	3,167,115,1,5,62,9
	.half	.L944-.L474
	.byte	1,4,2,5,18,9
	.half	.L477-.L944
	.byte	3,211,12,1,5,26,9
	.half	.L945-.L477
	.byte	1,4,1,5,32,9
	.half	.L70-.L945
	.byte	3,177,115,1,5,40,9
	.half	.L946-.L70
	.byte	1,5,47,9
	.half	.L947-.L946
	.byte	1,4,2,5,19,9
	.half	.L481-.L947
	.byte	3,243,12,1,9
	.half	.L611-.L481
	.byte	3,1,1,9
	.half	.L948-.L611
	.byte	3,1,1,9
	.half	.L949-.L948
	.byte	3,1,1,4,1,5,52,9
	.half	.L482-.L949
	.byte	3,138,115,1,5,44,9
	.half	.L68-.L482
	.byte	3,8,1,5,52,9
	.half	.L950-.L68
	.byte	1,5,58,9
	.half	.L951-.L950
	.byte	1,4,2,5,20,9
	.half	.L491-.L951
	.byte	3,203,7,1,9
	.half	.L612-.L491
	.byte	3,1,1,9
	.half	.L952-.L612
	.byte	3,1,1,9
	.half	.L953-.L952
	.byte	3,1,1,4,1,5,27,9
	.half	.L492-.L953
	.byte	3,181,120,1,5,9,9
	.half	.L954-.L492
	.byte	1,5,45,7,9
	.half	.L955-.L954
	.byte	3,2,1,5,53,9
	.half	.L956-.L955
	.byte	1,5,74,9
	.half	.L957-.L956
	.byte	1,4,2,5,28,9
	.half	.L494-.L957
	.byte	3,191,11,1,5,33,9
	.half	.L958-.L494
	.byte	1,5,21,9
	.half	.L613-.L958
	.byte	3,1,1,5,26,9
	.half	.L959-.L613
	.byte	1,5,33,9
	.half	.L614-.L959
	.byte	3,1,1,5,38,9
	.half	.L960-.L614
	.byte	1,5,36,9
	.half	.L615-.L960
	.byte	1,5,61,9
	.half	.L961-.L615
	.byte	1,5,45,9
	.half	.L962-.L961
	.byte	1,5,19,9
	.half	.L963-.L962
	.byte	1,4,1,5,32,9
	.half	.L72-.L963
	.byte	3,195,116,1,5,40,9
	.half	.L964-.L72
	.byte	1,5,46,9
	.half	.L965-.L964
	.byte	1,4,2,5,19,9
	.half	.L497-.L965
	.byte	3,226,12,1,9
	.half	.L616-.L497
	.byte	3,1,1,9
	.half	.L966-.L616
	.byte	3,1,1,9
	.half	.L967-.L966
	.byte	3,1,1,4,1,5,1,9
	.half	.L71-.L967
	.byte	3,157,115,1,7,9
	.half	.L122-.L71
	.byte	0,1,1
.L921:
	.sdecl	'.debug_ranges',debug,cluster('IfxCcu6_Timer_stop')
	.sect	'.debug_ranges'
.L121:
	.word	-1,.L84,0,.L122-.L84,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu6_Timer_synchronousStart')
	.sect	'.debug_info'
.L123:
	.word	475
	.half	3
	.word	.L124
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Ccu6/Timer/IfxCcu6_Timer.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L126,.L125
	.byte	2
	.word	.L89
	.byte	3
	.byte	'IfxCcu6_Timer_synchronousStart',0,1,143,4,6,1,1,1
	.word	.L86,.L499,.L85
	.byte	4
	.byte	'timer',0,1,143,4,52
	.word	.L134,.L500
	.byte	5
	.word	.L86,.L499
	.byte	6
	.word	.L335,.L501,.L502
	.byte	7
	.word	.L338,.L503
	.byte	7
	.word	.L340,.L504
	.byte	7
	.word	.L342,.L505
	.byte	8
	.word	.L344,.L501,.L502
	.byte	9
	.byte	'tctr4',0,2,200,12,20
	.word	.L345,.L506
	.byte	0,0,6
	.word	.L394,.L507,.L508
	.byte	7
	.word	.L396,.L509
	.byte	7
	.word	.L398,.L510
	.byte	7
	.word	.L400,.L511
	.byte	8
	.word	.L402,.L507,.L508
	.byte	9
	.byte	'tctr4',0,2,225,16,20
	.word	.L345,.L512
	.byte	0,0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu6_Timer_synchronousStart')
	.sect	'.debug_abbrev'
.L124:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,29,1,49
	.byte	16,17,1,18,1,0,0,7,5,0,49,16,2,6,0,0,8,11,1,49,16,17,1,18,1,0,0,9,52,0,3,8,58,15,59,15,57,15,73,16,2,6
	.byte	0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxCcu6_Timer_synchronousStart')
	.sect	'.debug_line'
.L125:
	.word	.L969-.L968
.L968:
	.half	3
	.word	.L971-.L970
.L970:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Ccu6/Timer/IfxCcu6_Timer.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Ccu6\\Std\\IfxCcu6.h',0
	.byte	0,0,0,0
.L971:
	.byte	5,39,7,0,5,2
	.word	.L86
	.byte	3,145,4,1,5,47,9
	.half	.L972-.L86
	.byte	1,5,53,9
	.half	.L973-.L972
	.byte	1,4,2,5,20,9
	.half	.L501-.L973
	.byte	3,183,8,1,9
	.half	.L617-.L501
	.byte	3,1,1,9
	.half	.L974-.L617
	.byte	3,1,1,9
	.half	.L975-.L974
	.byte	3,1,1,4,1,5,29,9
	.half	.L502-.L975
	.byte	3,201,119,1,5,37,9
	.half	.L976-.L502
	.byte	1,5,43,9
	.half	.L977-.L976
	.byte	1,4,2,5,19,9
	.half	.L507-.L977
	.byte	3,205,12,1,9
	.half	.L618-.L507
	.byte	3,1,1,9
	.half	.L978-.L618
	.byte	3,1,1,9
	.half	.L979-.L978
	.byte	3,1,1,4,1,5,1,9
	.half	.L508-.L979
	.byte	3,177,115,1,7,9
	.half	.L127-.L508
	.byte	0,1,1
.L969:
	.sdecl	'.debug_ranges',debug,cluster('IfxCcu6_Timer_synchronousStart')
	.sect	'.debug_ranges'
.L126:
	.word	-1,.L86,0,.L127-.L86,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu6_Timer_synchronousStop')
	.sect	'.debug_info'
.L128:
	.word	474
	.half	3
	.word	.L129
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Ccu6/Timer/IfxCcu6_Timer.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L131,.L130
	.byte	2
	.word	.L89
	.byte	3
	.byte	'IfxCcu6_Timer_synchronousStop',0,1,153,4,6,1,1,1
	.word	.L88,.L513,.L87
	.byte	4
	.byte	'timer',0,1,153,4,51
	.word	.L134,.L514
	.byte	5
	.word	.L88,.L513
	.byte	6
	.word	.L456,.L515,.L516
	.byte	7
	.word	.L459,.L517
	.byte	7
	.word	.L461,.L518
	.byte	7
	.word	.L463,.L519
	.byte	8
	.word	.L465,.L515,.L516
	.byte	9
	.byte	'tctr4',0,2,203,11,20
	.word	.L345,.L520
	.byte	0,0,6
	.word	.L480,.L521,.L522
	.byte	7
	.word	.L483,.L523
	.byte	7
	.word	.L485,.L524
	.byte	7
	.word	.L487,.L525
	.byte	8
	.word	.L489,.L521,.L522
	.byte	9
	.byte	'tctr4',0,2,235,16,20
	.word	.L345,.L526
	.byte	0,0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu6_Timer_synchronousStop')
	.sect	'.debug_abbrev'
.L129:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,29,1,49
	.byte	16,17,1,18,1,0,0,7,5,0,49,16,2,6,0,0,8,11,1,49,16,17,1,18,1,0,0,9,52,0,3,8,58,15,59,15,57,15,73,16,2,6
	.byte	0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxCcu6_Timer_synchronousStop')
	.sect	'.debug_line'
.L130:
	.word	.L981-.L980
.L980:
	.half	3
	.word	.L983-.L982
.L982:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Ccu6/Timer/IfxCcu6_Timer.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Ccu6\\Std\\IfxCcu6.h',0
	.byte	0,0,0,0
.L983:
	.byte	5,40,7,0,5,2
	.word	.L88
	.byte	3,155,4,1,5,48,9
	.half	.L984-.L88
	.byte	1,5,54,9
	.half	.L985-.L984
	.byte	1,4,2,5,20,9
	.half	.L515-.L985
	.byte	3,176,7,1,9
	.half	.L619-.L515
	.byte	3,1,1,9
	.half	.L986-.L619
	.byte	3,1,1,9
	.half	.L987-.L986
	.byte	3,1,1,4,1,5,28,9
	.half	.L516-.L987
	.byte	3,208,120,1,5,36,9
	.half	.L988-.L516
	.byte	1,5,42,9
	.half	.L989-.L988
	.byte	1,4,2,5,19,9
	.half	.L521-.L989
	.byte	3,205,12,1,9
	.half	.L620-.L521
	.byte	3,1,1,9
	.half	.L990-.L620
	.byte	3,1,1,9
	.half	.L991-.L990
	.byte	3,1,1,4,1,5,1,9
	.half	.L522-.L991
	.byte	3,177,115,1,7,9
	.half	.L132-.L522
	.byte	0,1,1
.L981:
	.sdecl	'.debug_ranges',debug,cluster('IfxCcu6_Timer_synchronousStop')
	.sect	'.debug_ranges'
.L131:
	.word	-1,.L88,0,.L132-.L88,0,0
	.sdecl	'.debug_loc',debug,cluster('IfxCcu6_Timer_countOneStep')
	.sect	'.debug_loc'
.L73:
	.word	-1,.L74,0,.L333-.L74
	.half	2
	.byte	138,0
	.word	0,0
.L358:
	.word	0,0
.L339:
	.word	0,0
.L349:
	.word	0,0
.L371:
	.word	-1,.L74,.L535-.L74,.L536-.L74
	.half	1
	.byte	82
	.word	0,0
.L353:
	.word	-1,.L74,.L530-.L74,.L531-.L74
	.half	1
	.byte	82
	.word	0,0
.L351:
	.word	0,0
.L370:
	.word	-1,.L74,.L533-.L74,.L534-.L74
	.half	1
	.byte	81
	.word	0,0
.L352:
	.word	-1,.L74,.L528-.L74,.L529-.L74
	.half	1
	.byte	81
	.word	0,0
.L360:
	.word	0,0
.L341:
	.word	0,0
.L362:
	.word	0,0
.L343:
	.word	0,0
.L373:
	.word	-1,.L74,.L534-.L74,.L3-.L74
	.half	1
	.byte	81
	.word	0,0
.L364:
	.word	-1,.L74,.L529-.L74,.L2-.L74
	.half	1
	.byte	81
	.word	0,0
.L346:
	.word	-1,.L74,.L527-.L74,.L528-.L74
	.half	1
	.byte	81
	.word	0,0
.L367:
	.word	-1,.L74,.L532-.L74,.L533-.L74
	.half	1
	.byte	81
	.word	0,0
.L334:
	.word	-1,.L74,0,.L333-.L74
	.half	1
	.byte	100
	.word	0,0
.L350:
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxCcu6_Timer_initModule')
	.sect	'.debug_loc'
.L75:
	.word	-1,.L76,0,.L133-.L76
	.half	2
	.byte	138,0
	.word	0,0
.L276:
	.word	0,0
.L158:
	.word	0,0
.L149:
	.word	0,0
.L143:
	.word	0,0
.L167:
	.word	0,0
.L220:
	.word	0,0
.L235:
	.word	0,0
.L228:
	.word	0,0
.L187:
	.word	0,0
.L179:
	.word	0,0
.L202:
	.word	0,0
.L195:
	.word	0,0
.L139:
	.word	-1,.L76,.L541-.L76,.L542-.L76
	.half	1
	.byte	108
	.word	.L537-.L76,.L6-.L76
	.half	1
	.byte	100
	.word	.L549-.L76,.L15-.L76
	.half	1
	.byte	100
	.word	.L558-.L76,.L34-.L76
	.half	1
	.byte	100
	.word	0,0
.L137:
	.word	-1,.L76,0,.L6-.L76
	.half	1
	.byte	101
	.word	.L539-.L76,.L540-.L76
	.half	1
	.byte	111
	.word	0,0
.L204:
	.word	0,0
.L249:
	.word	0,0
.L268:
	.word	0,0
.L280:
	.word	-1,.L76,.L564-.L76,.L285-.L76
	.half	1
	.byte	95
	.word	0,0
.L305:
	.word	-1,.L76,.L566-.L76,.L308-.L76
	.half	1
	.byte	95
	.word	0,0
.L313:
	.word	-1,.L76,.L568-.L76,.L316-.L76
	.half	1
	.byte	95
	.word	0,0
.L321:
	.word	-1,.L76,.L570-.L76,.L324-.L76
	.half	1
	.byte	95
	.word	0,0
.L211:
	.word	-1,.L76,.L553-.L76,.L25-.L76
	.half	1
	.byte	81
	.word	0,0
.L162:
	.word	-1,.L76,.L545-.L76,.L12-.L76
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L209:
	.word	-1,.L76,.L551-.L76,.L552-.L76
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L154:
	.word	-1,.L76,.L543-.L76,.L544-.L76
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L215:
	.word	-1,.L76,.L556-.L76,.L557-.L76
	.half	1
	.byte	82
	.word	0,0
.L174:
	.word	-1,.L76,.L547-.L76,.L548-.L76
	.half	1
	.byte	82
	.word	0,0
.L259:
	.word	0,0
.L171:
	.word	0,0
.L222:
	.word	0,0
.L197:
	.word	0,0
.L206:
	.word	-1,.L76,.L550-.L76,.L21-.L76
	.half	1
	.byte	88
	.word	.L559-.L76,.L34-.L76
	.half	1
	.byte	85
	.word	0,0
.L257:
	.word	0,0
.L240:
	.word	-1,.L76,.L560-.L76,.L133-.L76
	.half	1
	.byte	110
	.word	0,0
.L255:
	.word	0,0
.L292:
	.word	0,0
.L173:
	.word	-1,.L76,.L546-.L76,.L13-.L76
	.half	1
	.byte	81
	.word	0,0
.L214:
	.word	-1,.L76,.L554-.L76,.L555-.L76
	.half	1
	.byte	81
	.word	0,0
.L278:
	.word	0,0
.L283:
	.word	-1,.L76,.L565-.L76,.L38-.L76
	.half	1
	.byte	98
	.word	0,0
.L307:
	.word	-1,.L76,.L567-.L76,.L39-.L76
	.half	1
	.byte	98
	.word	0,0
.L297:
	.word	0,0
.L315:
	.word	-1,.L76,.L569-.L76,.L40-.L76
	.half	1
	.byte	98
	.word	0,0
.L323:
	.word	-1,.L76,.L571-.L76,.L41-.L76
	.half	1
	.byte	98
	.word	0,0
.L301:
	.word	0,0
.L288:
	.word	0,0
.L243:
	.word	-1,.L76,.L542-.L76,.L561-.L76
	.half	1
	.byte	108
	.word	.L562-.L76,.L36-.L76
	.half	1
	.byte	101
	.word	0,0
.L247:
	.word	0,0
.L262:
	.word	-1,.L76,.L561-.L76,.L35-.L76
	.half	1
	.byte	108
	.word	.L563-.L76,.L35-.L76
	.half	1
	.byte	101
	.word	0,0
.L266:
	.word	0,0
.L135:
	.word	-1,.L76,0,.L537-.L76
	.half	1
	.byte	100
	.word	.L538-.L76,.L133-.L76
	.half	1
	.byte	109
	.word	0,0
.L160:
	.word	0,0
.L151:
	.word	0,0
.L169:
	.word	0,0
.L290:
	.word	0,0
.L237:
	.word	0,0
.L230:
	.word	0,0
.L189:
	.word	0,0
.L181:
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxCcu6_Timer_initModuleConfig')
	.sect	'.debug_loc'
.L77:
	.word	-1,.L78,0,.L572-.L78
	.half	2
	.byte	138,0
	.word	.L572-.L78,.L327-.L78
	.half	3
	.byte	138,232,0
	.word	.L327-.L78,.L327-.L78
	.half	2
	.byte	138,0
	.word	0,0
.L330:
	.word	-1,.L78,0,.L327-.L78
	.half	1
	.byte	101
	.word	0,0
.L329:
	.word	-1,.L78,0,.L327-.L78
	.half	1
	.byte	100
	.word	0,0
.L332:
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxCcu6_Timer_start')
	.sect	'.debug_loc'
.L79:
	.word	-1,.L80,0,.L374-.L80
	.half	2
	.byte	138,0
	.word	0,0
.L378:
	.word	0,0
.L386:
	.word	0,0
.L397:
	.word	0,0
.L393:
	.word	-1,.L80,.L579-.L80,.L580-.L80
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L410:
	.word	-1,.L80,.L585-.L80,.L586-.L80
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L390:
	.word	0,0
.L409:
	.word	-1,.L80,.L583-.L80,.L584-.L80
	.half	1
	.byte	81
	.word	0,0
.L392:
	.word	-1,.L80,.L577-.L80,.L578-.L80
	.half	1
	.byte	81
	.word	0,0
.L379:
	.word	0,0
.L399:
	.word	0,0
.L380:
	.word	0,0
.L401:
	.word	0,0
.L406:
	.word	-1,.L80,.L582-.L80,.L583-.L80
	.half	1
	.byte	81
	.word	.L53-.L80,.L587-.L80
	.half	1
	.byte	81
	.word	0,0
.L381:
	.word	-1,.L80,.L576-.L80,.L577-.L80
	.half	1
	.byte	81
	.word	.L47-.L80,.L581-.L80
	.half	1
	.byte	81
	.word	0,0
.L403:
	.word	-1,.L80,.L581-.L80,.L48-.L80
	.half	1
	.byte	81
	.word	0,0
.L412:
	.word	-1,.L80,.L587-.L80,.L54-.L80
	.half	1
	.byte	81
	.word	0,0
.L375:
	.word	-1,.L80,0,.L573-.L80
	.half	1
	.byte	100
	.word	.L574-.L80,.L575-.L80
	.half	1
	.byte	111
	.word	.L47-.L80,.L48-.L80
	.half	1
	.byte	100
	.word	0,0
.L388:
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxCcu6_Timer_startSingleShotMode')
	.sect	'.debug_loc'
.L81:
	.word	-1,.L82,0,.L413-.L82
	.half	2
	.byte	138,0
	.word	0,0
.L417:
	.word	0,0
.L425:
	.word	0,0
.L432:
	.word	0,0
.L438:
	.word	0,0
.L447:
	.word	-1,.L82,.L601-.L82,.L602-.L82
	.half	1
	.byte	95
	.word	0,0
.L429:
	.word	-1,.L82,.L593-.L82,.L594-.L82
	.half	1
	.byte	95
	.word	0,0
.L436:
	.word	-1,.L82,.L596-.L82,.L597-.L82
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L451:
	.word	-1,.L82,.L604-.L82,.L605-.L82
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L434:
	.word	0,0
.L435:
	.word	-1,.L82,.L592-.L82,.L595-.L82
	.half	1
	.byte	81
	.word	0,0
.L450:
	.word	-1,.L82,.L600-.L82,.L603-.L82
	.half	1
	.byte	81
	.word	0,0
.L418:
	.word	0,0
.L439:
	.word	0,0
.L419:
	.word	0,0
.L440:
	.word	0,0
.L444:
	.word	-1,.L82,.L599-.L82,.L600-.L82
	.half	1
	.byte	81
	.word	.L64-.L82,.L606-.L82
	.half	1
	.byte	81
	.word	0,0
.L420:
	.word	-1,.L82,.L591-.L82,.L592-.L82
	.half	1
	.byte	81
	.word	.L58-.L82,.L598-.L82
	.half	1
	.byte	81
	.word	0,0
.L441:
	.word	-1,.L82,.L598-.L82,.L59-.L82
	.half	1
	.byte	81
	.word	0,0
.L453:
	.word	-1,.L82,.L606-.L82,.L65-.L82
	.half	1
	.byte	81
	.word	0,0
.L414:
	.word	-1,.L82,0,.L588-.L82
	.half	1
	.byte	100
	.word	.L589-.L82,.L590-.L82
	.half	1
	.byte	111
	.word	.L58-.L82,.L59-.L82
	.half	1
	.byte	100
	.word	0,0
.L427:
	.word	0,0
.L433:
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxCcu6_Timer_stop')
	.sect	'.debug_loc'
.L83:
	.word	-1,.L84,0,.L454-.L84
	.half	2
	.byte	138,0
	.word	0,0
.L460:
	.word	0,0
.L468:
	.word	0,0
.L478:
	.word	0,0
.L475:
	.word	0,0
.L484:
	.word	0,0
.L479:
	.word	0,0
.L496:
	.word	-1,.L84,.L614-.L84,.L615-.L84
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L472:
	.word	-1,.L84,.L609-.L84,.L610-.L84
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L470:
	.word	0,0
.L476:
	.word	0,0
.L471:
	.word	-1,.L84,.L608-.L84,.L69-.L84
	.half	1
	.byte	81
	.word	0,0
.L495:
	.word	-1,.L84,.L613-.L84,.L72-.L84
	.half	1
	.byte	81
	.word	0,0
.L462:
	.word	0,0
.L486:
	.word	0,0
.L464:
	.word	0,0
.L488:
	.word	0,0
.L493:
	.word	-1,.L84,.L612-.L84,.L613-.L84
	.half	1
	.byte	81
	.word	0,0
.L466:
	.word	-1,.L84,.L607-.L84,.L608-.L84
	.half	1
	.byte	81
	.word	0,0
.L498:
	.word	-1,.L84,.L616-.L84,.L71-.L84
	.half	1
	.byte	81
	.word	0,0
.L490:
	.word	-1,.L84,.L611-.L84,.L68-.L84
	.half	1
	.byte	81
	.word	0,0
.L455:
	.word	-1,.L84,0,.L454-.L84
	.half	1
	.byte	100
	.word	0,0
.L469:
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxCcu6_Timer_synchronousStart')
	.sect	'.debug_loc'
.L85:
	.word	-1,.L86,0,.L499-.L86
	.half	2
	.byte	138,0
	.word	0,0
.L503:
	.word	0,0
.L509:
	.word	0,0
.L504:
	.word	0,0
.L510:
	.word	0,0
.L505:
	.word	0,0
.L511:
	.word	0,0
.L506:
	.word	-1,.L86,.L617-.L86,.L618-.L86
	.half	1
	.byte	81
	.word	0,0
.L512:
	.word	-1,.L86,.L618-.L86,.L499-.L86
	.half	1
	.byte	81
	.word	0,0
.L500:
	.word	-1,.L86,0,.L499-.L86
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxCcu6_Timer_synchronousStop')
	.sect	'.debug_loc'
.L87:
	.word	-1,.L88,0,.L513-.L88
	.half	2
	.byte	138,0
	.word	0,0
.L517:
	.word	0,0
.L523:
	.word	0,0
.L518:
	.word	0,0
.L524:
	.word	0,0
.L519:
	.word	0,0
.L525:
	.word	0,0
.L520:
	.word	-1,.L88,.L619-.L88,.L620-.L88
	.half	1
	.byte	81
	.word	0,0
.L526:
	.word	-1,.L88,.L620-.L88,.L513-.L88
	.half	1
	.byte	81
	.word	0,0
.L514:
	.word	-1,.L88,0,.L513-.L88
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L992:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('IfxCcu6_Timer_countOneStep')
	.sect	'.debug_frame'
	.word	24
	.word	.L992,.L74,.L333-.L74
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxCcu6_Timer_initModule')
	.sect	'.debug_frame'
	.word	12
	.word	.L992,.L76,.L133-.L76
	.sdecl	'.debug_frame',debug,cluster('IfxCcu6_Timer_initModuleConfig')
	.sect	'.debug_frame'
	.word	40
	.word	.L992,.L78,.L327-.L78
	.byte	8,19,8,23,4
	.word	(.L572-.L78)/2
	.byte	19,232,0,22,26,4,19,138,232,0,4
	.word	(.L327-.L572)/2
	.byte	19,0,8,26
	.sdecl	'.debug_frame',debug,cluster('IfxCcu6_Timer_start')
	.sect	'.debug_frame'
	.word	12
	.word	.L992,.L80,.L374-.L80
	.sdecl	'.debug_frame',debug,cluster('IfxCcu6_Timer_startSingleShotMode')
	.sect	'.debug_frame'
	.word	12
	.word	.L992,.L82,.L413-.L82
	.sdecl	'.debug_frame',debug,cluster('IfxCcu6_Timer_stop')
	.sect	'.debug_frame'
	.word	24
	.word	.L992,.L84,.L454-.L84
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxCcu6_Timer_synchronousStart')
	.sect	'.debug_frame'
	.word	24
	.word	.L992,.L86,.L499-.L86
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxCcu6_Timer_synchronousStop')
	.sect	'.debug_frame'
	.word	24
	.word	.L992,.L88,.L513-.L88
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	; Module end
